package com.nci.tunan.qry.common;
/**
 * 通知书类型常量类工具包
 * <AUTHOR>
 *
 */
public class DocumentTypeUtil {
	/**补费通知书*/
	public static final String 	ADDPRE_DOCUMENT = "103";
	//默认机构代码
	public static final String 	ORGANCODE = "00000000";
	//生成xml编码格式
	public static final String UTF8="UTF-8";
	public static final String DATASETS="DATASETS";
	public static final String DATASET="DataSet";
	//通知书状态
	public static final String NOTICE_STATUS0="0";//待发放
	public static final String NOTICE_STATUS="1";//待发放
	public static final String NOTICE_STATUS2="2";//待打印
	public static final String NOTICE_STATUS6="6";//待复核
	public static final String NOTICE_STATUS3="3";//已打印
	public static final String NOTICE_STATUS9="9";//已逾期
	public static final String NOTICE_STATS_ENTRY="5";//待录入
	public static final String NOTICE_STATS_BACK="7";//已回销
	public static final String NOTICE_STATUS4="4";//待回扫
	
	//打印系统服务ip配置
	public static final String  DOCUMENTIP= "**********";
	public static final String  DOCUMENTSERVICE= "http://**********:5999/?wsdl";
	/*通知书单证类型*/
	public static final String LTR_SENDTYPE= "Ltr_ORT";
//	public static final String PIP_SENDTYPE= "Pip_ORT";//旧的2016-10-24李奇邮件改为下面的
	public static final String PIP_SENDTYPE_BPO= "PolicyBPO";//bpo制单
	public static final String PIP_SENDTYPE_COM= "Pip_ORT";//公司自主
	public static final String PIP_SENDTYPE_ELE= "ePolicy";//电子
	
	public static final String DOCUMENT_Y="Y";
	public static final String DOCUMENT_N="N";
	public static final String  DOCUMENT_ ="_";
   //	打印类型
	public static final String PRINT_PRINTTYPE= "Print";
	public static final String PREVIEW_PRINTTYPE= "Preview";
	public static final String PREVIEWANDPRINT_PRINTTYPE= "PreviewAndPrint";
	public static final String QUERY= "query";
	//通知书模板主键
	public static final String WITHOUTNOTICENO= "WITHOUTNOTICENO";
	public static final String RECEIPTNOTICENO= "RECEIPTNOTICENO";
	public static final String CONTCHNOTICENO="CONTCHNOTICENO";
	public static final String OVERDUENOTICENO="OVERDUENOTICENO";
	//出单前撤保
	public static final String CHECKNOTICENO ="CHECKNOTICENO";
	//补费通知书模板主键
    public static final String ADDDOCUMENTNO = "PAYNOTICENO";
  //溢交退费通知书模板主键
    public static final String OVERFLOWDOCUMENT ="OVERFLOWDOCUMENT";
  //逾期退费通知书模板主键
    public static final String REFUNDNOTICENO ="REFUNDNOTICENO";
    
    //客户
	public static final String CLIENTNBNOTICENO= "CLIENTNBNOTICENO";
	//业务员
	public static final String AGENTNBNOTICENO= "AGENTNBNOTICENO";
	
	/**通知书配置类型******开始******/
	//补费通知书
	public static final String DOCUEMENT01_TYPE ="NBS_00001";
	//补费通知书-card_code
	public static final String DOCUEMENT01_CODE ="UN016";
	//出单前撤保申请
	public static final String DOCUEMENT02_TYPE ="NBS_00002";
	//出单前撤保通知书--card_code
	public static final String DOCUEMENT02_CODE ="UN008";
	//初审不通过通知书类型
	public static final String DOCUEMENT03_TYPE ="NBS_00003";
	//初审不通过通知书类型--card_code
	public static final String DOCUEMENT03_CODE ="UN031";
	//初审回执通知书类型
	public static final String DOCUEMENT04_TYPE ="NBS_00004";
	//初审回执通知书类型--card_code
	public static final String DOCUEMENT04_CODE ="UN032";
	//勘误通知书_业务员
	public static final String DOCUEMENT05_TYPE ="NBS_00005";
	//勘误通知书_客户
	public static final String DOCUEMENT06_TYPE ="NBS_00006";
	//勘误通知书_客户--card_code
	public static final String DOCUEMENT06_CODE ="EUN116";
	//投保内容变更通知书
	public static final String DOCUEMENT07_TYPE ="NBS_00007";
	//投保内容变更通知书---card_code
	public static final String DOCUEMENT07_CODE ="UN009";
	//新契约变更通知书(业务员)
	public static final String DOCUEMENT08_TYPE ="NBS_00008";
	//溢交退费通知书
	public static final String DOCUEMENT09_TYPE ="NBS_00009";
	//溢交退费通知书---CARD_CODE
	public static final String DOCUEMENT09_CODE ="UN070";
	//逾期通知书
	public static final String DOCUEMENT10_TYPE="NBS_00010";
	//逾期通知书--card_code(逾期退费通知书公用一个)
	public static final String DOCUEMENT10_CODE="UN006";
	//生调通知书
	public static final String DOCUEMENT12_CODE="UN012";
	//逾期退费通知书
	public static final String DOCUEMENT11_TYPE ="NBS_00011";
	//质检通知书_客户
	public static final String DOCUEMENT12_TYPE ="NBS_00012";
	//质检通知书_业务员
	public static final String DOCUEMENT13_TYPE ="NBS_00013";
	//质检通知书_CRAD_CODE
	public static final String DOCUEMENTALL_CODE ="EUN115";
	//催缴通知书
	public static final String DOCUEMENT14_TYPE ="NBS_00008";
	//催缴通知书--CARD_CODE
	public static final String DOCUEMENT14_CODE ="UN001";
	/**通知书配置类型******结束******/
	
	/**保全通知书类型配置*****开始**/
	//批单打印
	//保单信息通知书
	//保全收费银行划款成功通知书
	//补费通知书
	//付费通知书
	//公司解约通知书
	//收费费形式变更通知书

	public static final String CSDOCUEMENT03_TYPE ="CUS_00003";	
	//保单信息通知书
	public static final String CSDOCUEMENT01_TYPE ="CUS_00001";	
	//保全收费银行划款成功通知书
	public static final String CSDOCUEMENT05_TYPE ="CUS_00005";	
	//补费通知书
	public static final String CSDOCUEMENT06_TYPE ="CUS_00006";
	//付费通知书
	public static final String CSDOCUEMENT07_TYPE ="CUS_00007";	
	//公司解约通知书
	public static final String CSDOCUEMENT08_TYPE ="CUS_00008";	
	//收费费形式变更通知书
	public static final String CSDOCUEMENT10_TYPE ="CUS_00010";	
	/**保全通知书类型配置*******结束**/
	
	//新契约内容变更通知书
	public static final String NB_INFO_TEMPLATE_CLIENT_CODE="nb.info.template.clientcode";
	
	public static final String NB_INFO_TEMPLATE_AGENT_CODE="nb.info.template.agentcode";
	
	public static final String ISSUE_STATE01="01";
	public static final String ISSUE_STATE02="02";
	public static final String ISSUE_STATE03="03";
	
	public static final String SENDTYPE01 ="01";//客户
	public static final String SENDTYPE02 ="02";//业务员
	public static final String DOCUMNET_NBS_SYSTEM_ID = "065";  //add by jinghb_wb 2017-03-30
	
	
	public static final String DOCUEMENT_UW_TYPE_03 ="UWS_00003";//体检通知书（核保下发）
	public static final String DOCUEMENT_UW_TYPE_06 ="UWS_00006";//契约内容变更通知书
	public static final String DOCUEMENT_UW_TYPE_07 ="UWS_00007";//保全核保内容变更通知书
	public static final String DOCUEMENT_UW_TYPE_05 ="UWS_00005";//核保下下发的不自动续保通知书
	public static final String BUSS_SOURCE_CODE ="001";//通知书业务来源编码 - 新契约 (t_biz_source)
	public static final String STORAGE_NO ="001";//存储码，可与内容平台沟通
}
