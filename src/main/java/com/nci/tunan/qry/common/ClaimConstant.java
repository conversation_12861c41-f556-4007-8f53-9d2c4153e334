package com.nci.tunan.qry.common;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.nci.tunan.qry.interfaces.model.clm.vo.ExcleSheetVO;
import com.nci.udmp.framework.util.Constants;

public final class ClaimConstant extends Constants {
    /**
     * 私有构造函数，不允许new
     */
    private ClaimConstant() { 
    }

    /**
     * 赔案号
     */
    public static final String CASENO = "";

    /**
     * 个人池标识
     */
    public static final String PERSONALQUERYTASKTYPE = "2";

    /**
     * 共享池标识
     */
    public static final String SHAREQUERYTASKTYPE = "1";

    /**
     * 报案节点编码
     */
    
    public static final String REPORTSHARETASKCODE = "CLM010201";
    
    /**
     * 协谈开始节点编码
     */
    public static final String TREATYSTATATASKCODE = "CLM070000";

    /**
     * 分配协谈任务 - 节点编码
     */
    public static final String TREATYTASKCODE = "CLM070201";
    
    /**
     * 理赔协谈节点编码
     */
    public static final String CLMTREATYTASKCODE = "CLM070202";
    
    /**
     * 出具机构协谈结论 - 节点编码
     */
    public static final String CLMORGTREATYTASKCODE = "CLM070203";
    
    /**
     * 事中质检节点编码
     */
    public static final String MIDDLECHECKTASKCODE = "CLM010215";
    
    /**
     * 报案状态
     */
    public static final String REPORTSTATUSE = "10";

    /**
     * 签收节点编码
     */
    public static final String SIGNSHARETASKCODE = "CLM010202";

    /**
     * 签收状态
     */
    public static final String SIGNSTATUSE = "20";

    /**
     * 立案节点编码
     */
    public static final String REGISTERSHARETASKCODE = "CLM010203";

    /**
     * 立案状态
     */
    public static final String REGISTERSTATUSE = "30";

    /**
     * 审核节点编码
     */
    public static final String AUDITSHARETASKCODE = "CLM010207";

    /**
     * 审核状态
     */
    public static final String AUDITSTATUSE = "60";
    /**
     * 案件状态审核中
     */
    public static final String AUDITSTATUSEZ = "61";
    /**
     * 案件状态审批
     */
    public static final String APPROVESTATUSE = "70";
    /**
     * 案件状态审批中
     */
    public static final String APPROVESTATUSEZ = "71";
    
    /**
     * 预付中
     */
    public static final String ADVANCE_MID_STATUS = "51";
    
    /**
     * 待预付录入
     */
    public static final String ADVANCE_ENTER_STATUS = "52";
    
    /**
     * 待预付复核
     */
    public static final String ADVANCE_CHECK_STATUS = "53";
    
    /**
     * 预付复核中
     */
    public static final String ADVANCE_CHECKING_STATUS = "54";

    /**
     * 模板标识
     */
    public static final String PROCESSFLAG = "CLMMain";
    
    /**
     * 协谈模板标识
     */
    public static final String TREATYFLAG = "CLMC";
    
    /**
     * 合议模板标识
     */
    public static final String DISCUSSFLAG = "CLMPD";
    
    /**
     * 回退模板标识
     */
    public static final String ROLLBACKFLAG = "CLMRM";
    
    /**
     * 付费变更模板标识
     */
    public static final String PAYCHANGEFLAG = "CLMPC";
    
    /** 
    * 补充问题件模板标识
    */ 
    public static final String SUPPLYCHECKLISTFLAG = "CLMRFP";

    /**
     * 审批结案处理节点编码
     */
    public static final String APPROVETASKCODE = "CLM010409";
    
    /**
     * 预付申请节点编码
     */
    public static final String PREPAREPAYAPPLYTASKCODE = "CLM010204";
    
    /**
     * 预付复核节点编码
     */
    public static final String PREPAREPAYCHECKTASKCODE = "CLM010205";
    
    /**
     * 预付审批生效节点编码
     */
    public static final String PREPAREPAYCHECKEFFICETASKCODE = "CLM010406";
    
    /**
     * 回退申请节点编码
     */
    public static final String ROLLBACKAPPLYTASKCODE = "CLM040201";
    
    /**
     * 回退复核节点编码
     */
    public static final String ROLLBACKCHECKTASKCODE = "CLM040202";
    
    /**
     * 付费变更申请节点编码
     */
    public static final String PAYCHANGEAPPLYTASKCODE = "CLM060201";
    
    /**
     * 付费变更复核节点编码
     */
    public static final String PAYCHANGECHECKTASKCODE = "CLM060202";
    
    
    /**
     * 分配合议任务 - 节点编码
     */
    public static final String ALLOTDISCUSSIONREPLYTASKCODE = "CLM030201";
    
    /**
     * 合议回复节点编码
     */
    public static final String DISCUSSIONREPLYTASKCODE = "CLM030202";
    
    /**
     * 出具合议结论 - 节点编码
     */
    public static final String ORGDISCUSSIONREPLYTASKCODE = "CLM030203";
    
    /** 
    * 补充问题件 - 节点编码
    */ 
    public static final String SUPPLYCLAIMCHECKLISTTASKCODE = "CLM100201";
    
    /**
     * 审批处理节点编码
     */
    public static final String APPROVEDISPOSETASKCODE = "CLM010208";
    
    /**
     * 复核案件节点编码
     */
    public static final String AGAINCHECKTASKCODE = "CLM010212";
    
    /**
     * 案件抽检(事中质检)处理节点编码
     */
    public static final String MIDCTASKCODE = "CLM010215";
    
    /**
     * 案件质检(事后质检)处理节点编码
     */
    public static final String AFCTASKCODE= "CLM050201";

    /**
     * 结案状态
     */
    public static final String CLOSECASESTATUSE = "80";
    /**
     * 待审批状态
     */
    public static final String WAITCASESTATUSE = "70";

    /**
     * 业务类型
     */
    public static final String BUSINESSTYPE = "CLM";
    /**
     * 任务创建类型 0: 创建流程不提交,1：创建流程且自动提交到下一节点
     */
    public static final String CREATETASKCREATETYPE = "0";
    /**
     * 任务创建类型 1：创建流程且自动提交到下一节点
     */
    public static final String SUBMITTASKCREATETYPE = "1";
    /**
     * 理赔创建类型
     */
    public static final String CLAIMCREATETYPE = "0";
    /**
     * 立案共享池非空验证提示
     */
    public static final String SHAREPOLLNOTNULL = "请至少输入一项查询条件";

    /**
     * 立案共享池操作失败提示
     */
    public static final String SHAREPOLLFAIL = "查询失败";

    /**
     * 查询出险人操作失败提示
     */
    public static final String QUERYINSUREDFAIL = "查询出险人失败";

    /**
     * 查询出险人操作失败提示
     */
    public static final String QUERYINSUREDNOTEXIST = "您要查询的客户不存在，请重新输入查询条件";

    /**
     * 保单挂起抄单操作成功提示
     */
    public static final String CONTRACTHANGUPSUCCEED = "抄单成功";

    /**
     * 保单挂起抄单操作失败提示
     */
    public static final String CONTRACTHANGUPFAIL = "抄单失败";

    /**
     * 保单挂起抄单操作失败提示
     */
    public static final String CONTRACTHANGUPDATE = "该出险日期没有对应的保单,或抄单保存失败";

    /**
     * 定义复核机制删除复核规则操作成功提示
     */
    public static final String DATACHECKDELETESUCCEED = "复核规则删除成功";

    /**
     * 匹配理算成功提示
     */
    public static final String MATCHCALCSUCCEED = "匹配理算成功";

    /**
     * 匹配理算失败提示
     */
    public static final String MATCHCALCFAIL = "匹配理算失败";

    /**
     * 结论保存成功提示
     */
    public static final String CONCLUSIONSUCCEED = "结论保存成功";

    /**
     * 结论保存失败提示
     */
    public static final String CONCLUSIONFAIL = "结论保存失败";
    // 险种设计类型-投连险
    public static final String PRODUCT_CATEGORY1_TL = "20004";
    // 投连险卖出标识 0-按照申请日的上一个计价日计算
    public static final String SELL_FALG_0 = "0";
    // 投连险卖出标识 1-按照申请日的下一个计价日计算
    public static final String SELL_FALG_1 = "1";
    // 卖出类型 0-试算
    public static final String SELL_TYPE_CALCULATION = "0";
    // 卖出类型 1-卖出
    public static final String SELL_TYPE_SOLD = "1";
    // 匹配理算类型 0-匹配预算
    public static final String MATCH_CALC_PRE = "0";
    // 匹配理算类型 1-匹配理算
    public static final String MATCH_CALC_CALCULATION = "1";
    // 累加器类型 1-扣款
    public static final String ACCUMU_TYPE_DEDUCT = "1";
    // 累加器类型 2-共担
    public static final String ACCUMU_TYPE_COPAY = "2";
    // 累加器类型 3-责任限额
    public static final String ACCUMU_TYPE_DSL = "3";
    // 累加器类型 4-双重限额
    public static final String ACCUMU_TYPE_DOUBLELIMIT = "4";
    // 累加器类型 5-乘法累加器
    public static final String ACCUMU_TYPE_MULTIPLIER = "5";
    // 累加器类型 6-最小值累加器
    public static final String ACCUMU_TYPE_MINVALUE = "6";
    // 累加器类型 7-账单住院天数
    public static final String ACCUMU_TYPE_HOSPITALIZATION = "7";
    // 累加器模式 0-无关
    public static final String CONDITION_TYPE_NA = "0";
    // 累加器模式 1-单个事故
    public static final String CONDITION_TYPE_PER_EVENT = "1";
    // 累加器模式 2-单个住院期间
    public static final String CONDITION_TYPE_PRE_HOSPITALIZATION = "2";
    // 累加器模式 3-单次诊断
    public static final String CONDITION_TYPE_PRE_DIAGNOSIS = "3";
    // 累加器模式 4-同一事故原因的事故
    public static final String CONDITION_TYPE_SAME_EVENT = "4";
    // 累加器模式 5-单个保单年度
    public static final String CONDITION_TYPE_PER_POLICY_YEAR = "5";
    // 抄单日期 当前标示 0 - 出险日期
    public static final BigDecimal FLAG_CLAIM_DATE = new BigDecimal(0);
    // 抄单日期 当前标示 1 - 当前日期
    public static final BigDecimal FLAG_CURRENT_DATE = new BigDecimal(1);
    // 相关性判断结果  0 - 否
    public static final String NO = "0";
    // 相关性判断结果  1 - 是 
    public static final String YES = "1";
    // 治疗类型  0-门诊
    public static final String TREAT_TYPE_CLINIC = "0";
    // 治疗类型  1-住院
    public static final String TREAT_TYPE_HOSPITALIZATION = "1";
    // 相关性类型 1 - T_PRODUCT_RELATIVITY
    public static final BigDecimal RELATIVITY_TYPE_1 = new BigDecimal(1);
    // 相关性类型2 - T_PARAM
    public static final BigDecimal RELATIVITY_TYPE_2 = new BigDecimal(2);
    // 1- 社保
    public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_1 =  new BigDecimal(1);
    // 2- 第三方支付
    public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_2 =  new BigDecimal(2);
    // 3- 公费 
    public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_3 =  new BigDecimal(3);
    
    
    
    // 调用打印时加入
    // 读取配置问题件路径
    public static final String NB_UW_LOGINADMIN_PATH = "META-INF/conf/config.properties";
    // 登陆用户名
    public static final String NB_UW_LOGIN_USERNAME = "login.userName";
    // 登陆密码
    public static final String NB_UW_LOGIN_PASSWORD = "login.passWord";
    // 体检结果录入访问URL
    public static final String NB_UW_LOGIN_PHYSICALURL = "login.physicalUrl";
    // 核保结论通知书录入访问URL----2015-01-27
    public static final String NB_UW_LOGIN_UWDESITIONURL = "login.uwDesitionUrl";
    // 应用上下文名称
    public static final String NB_UW_LOGIN_WEBCONTEXT = "login.webcontext";
    // 体检结果录入访问的IP地址
    public static final String NB_UW_LOGIN_IP = "login.ip";
    // 核保结论通知书录入访问的IP地址
    public static final String NB_UW_LOGIN_UWDESITIONIP = "login.uwDesitionIp";
    // 端口号
    public static final String NB_UW_LOGIN_PORT = "login.port";
    // 用户名
    public static final String NB_BMP_userCode = "syn";
    // ProcessFlag
    public static final String NB_BMP_ProcessFlag = "NBEnter";
    public static final String NB_BMP_Question_ProcessFlag = "NBIM";
    // 投保单录入-->TaskCode
    public static final String NB_BMP_TaskCode = "NB030202";
    // 复核任务-->TaskCode
    public static final String NB_BMP_TaskCode2 = "NB030203";
    public static final String NB_BMP_Question_TaskCode = "NB120201";
    public static final String NB_BMP_Question_Review_TaskCode = "NB120202";
    // QueryTaskType,共享池
    public static final String NB_BMP_QueryTaskType_Pubpool = "1";
    // 个人池
    public static final String NB_BMP_QueryTaskType_Perpool = "2";
    // 通知书下载临时路径
    public static final String NB_NOTICE_URL = "notice.url";

    // 初审不通过通知书类型
    public static final String NB_AUDIT_NOT_TYPE = "audit.notice.type";
    // 初审回执通知书类型
    public static final String NB_AUDIT_RECEIPT_TYPE = "audit.receipt.type";
    // 业务来源编码
    public static final String BUSS_SOURCE_CODE = "buss.source.code";
    // 发送对象类型
    public static final String SEND_OBJECT_TYPE = "send.obj.type";
    // 通知书状态
    public static final String NOTICE_STATUS = "1";
    // 记事本访问路径
    public static final String NB_UW_LOGIN_NOTEBOOK = "login.uwNoteUrl";

    // 记事本访问ip
    public static final String NB_UW_LOGIN_UWNOTE = "login.uwnoteIp";
    // 退回重扫模板标识-->ProcessFlag
    public static final String NB_BMP_ProcessFlag_Reset = "NBIFS";
    // 退回重扫-->TaskCode
    public static final String NB_BMP_TaskCode_Reset = "NB040201";

    // 通知书打印ip地址
    public static final String NB_DOCUMENT_IP = "documentIp";
    // 通知书单证类型
    public static final String LTR_SENDTYPE = "ltr.sendType";
    public static final String PIP_SENDTYPE = "pip.sendType";
    // 打印类型
    public static final String PRINT_PRINTTYPE = "print.printType";
    public static final String PREVIEW_PRINTTYPE = "preview.printType";
    public static final String PREVIEWANDPRINT_PRINTTYPE = "previewAndPrint.printType";
    // 规则引擎ip地址
    public static final String NB_ILOG_IP = "ilogIp";
    // 签单模板标识-->ProcessFlag
    public static final String NB_BMP_SIGN_PROCESSFLAG = "NBSignin";
    // 待签单处理节点编码-->TaskCode
    public static final String NB_BMP_SIGN_WAITTING_TASKCODE = "NB050202";
    // 事后质检模板标识-->ProcessFlag
    public static final String NB_BMP_CHECK_PROCESSFLAG = "NBCheck";
    // 进行质检操作节点编码-->TaskCode
    public static final String NB_BMP_CHECK_ING_TASKCODE = "NB100201";
    // 纠错模板标识-->ProcessFlag
    public static final String NB_BMP_CORRECT_PROCESSFLAG = "NBCorrect";
    // 纠错申请节点编码-->TaskCode
    public static final String NB_BMP_CORRECT_APPLY_TASKCODE = "NB090201";
    // 纠错审核节点编码-->TaskCode
    public static final String NB_BMP_CORRECT_VERIFY_TASKCODE = "NB090202";
    // 保单重打模板标识-->ProcessF审核lag
    public static final String NB_BMP_REPRINT_PROCESSFLAG = "NBPR";
    // 重打审核节点编码-->TaskCode
    public static final String NB_BMP_REPRINT_VERIFY_TASKCODE = "NB070203";
    // 通知书管理模板标识-->ProcessFlag
    public static final String NB_BMP_DOCUMENT_PROCESSFLAG = "NM";
    // 通知书打印节点编码-->TaskCode
    public static final String NB_BMP_DOCUMENT_PRINT_TASKCODE = "NM010201";
    // 通知书录入节点编码-->TaskCode
    public static final String NB_BMP_DOCUMENT_ENTRY_TASKCODE = "NM010203";
    // 保单更新回执信息成功的标识
    public static final String NB_PAS_RECEIPT_SUCCESS = "0";
    // 任务提醒的方式-->清单类
    public static final String NB_TASK_DL = "1";
    // 任务提醒的方式-->任务池类
    public static final String NB_TASK_POOL = "2";
    // 理赔类型---医疗
    public static final String ClAIMTYPE_MEDICAL = "03";
    // 理赔类型---医疗
    public static final String ClAIMTYPE_ILLNESS = "08";
    // 案件状态预付未完成状态
    public static final String ClAIMSTATUS = "50";
    // 调用工作流成功标识
    public static final String BIZRESCD = "0";
    // 调用工作流失败标识
    public static final String BIZRESCDNO = "1";
    // 出险人性别常量
    public static final String SEX = "9L";
    // 案件状态为签收中
    public static final String CASESTATUS = "21";
    // 案件状态为立案中
    public static final String REGISTERCASESTATUS = "31";
    //案件状态为关闭
    public static final String CANCELSTATUS = "90";
    // 理赔关怀中受益人的年龄
    public static final String BENEAGE = "18";
    // 应收应付理赔业务来源
    public static final String DERIVTYPE = "5";

    /**
     *  数字0
     */
    public static final int ZERO = 0;

    /**
     * 数字1
     */
    public static final int ONE = 1;
    
    /**
     * 数字2
     */
    public static final int TWO = 2;
    
    /**
     * 数字3
     */
    public static final int THREE = 3;
    
    /**
     * 数字4
     */
    public static final int FORE = 4;
    
    /**
     * 数字6
     */
    public static final int SIX = 6;

    /**
     * 关系代码，"00"代表本人
     */
    public static final String RELATION = "00";
    
    /**
     * 北京市编码
     */
    public static final String  BEIJING = "110000";

    /**
     * 东城区编码
     */
    public static final String  DONGCHENG = "110100";
    /**
     * 结算类型代码，"8"代表退还保费
     */
    public static final String ADJUST_TYPE = "8";

    /**
     * 两个BigDecimal的数据比较大小，小于的返回值为-1
     */
    public static final int COMPARE_RESULT = -1;

    // 反馈质检结果模板
    public static final String CHECKAFTERFLAG = "CLMCheck";
    // 反馈质检结果节点号
    public static final String CHECKAFTERCODE = "CLM050201";
    /**
     * 出具审核结论 常量
     * add xuyz_wb
     */
     /**
     * @Fields Medical_YES : 是否医疗赔付明细：Y
     */
     public static final String Medical_YES = "Y";
     /**
     * @Fields Medical_NO : 是否医疗赔付明细：N
     */
     public static final String Medical_NO = "N";
     /**
      * @Fields NUM_THREE : 数字：3
      */
     public static final int NUM_THREE = 3;
     /**
      * @Fields NUM_FOUR : 数字：4
      */
     public static final int NUM_FOUR = 4;
     /**
      * @Fields NUM_FIVE : 数字：5
      */
     public static final int NUM_FIVE = 5;
     /**
      * @Fields NUM_SIX : 数字：6
      */
     public static final int NUM_SIX = 6;
     /**
      * @Fields NUM_EIGHT : 数字：8
      */
     public static final int NUM_EIGHT = 8;
     /**
      * @Fields NUM_NINE : 数字：9
      */
     public static final int NUM_NINE = 9;
     /**
      * @Fields NUM_TEN : 数字：10
      */
     public static final int NUM_TEN = 10;
     /**
      * @Fields NUM_SEVENTEEN : 数字：17
      */
     public static final int NUM_SEVENTEEN = 17;
     
     /**
      * @Fields CASE_STATUS_SEVENTYONE : 赔案状态 审核中："71"
      */
     public static final String CASE_STATUS_SEVENTYONE = "71";
     /**
      * @Fields COMMA : 逗号：","
      */
     public static final String COMMA = ",";
     /**
      * 责任层赔付结论和赔案层赔付结论比较
      */
     public static final String LIABCONCLUSION_FLAG_ZERO = "0";
     public static final String LIABCONCLUSION_FLAG_ONE = "1";
     public static final String LIABCONCLUSION_FLAG_TWO = "2";
     public static final String LIABCONCLUSION_FLAG_THREE = "3";
     public static final String CONDITION_TYPE_NAME = "审批权限";
     /**
      * 退保终止03、解约终止05  理赔终止02
      */
     public static final String END_CAUSE_THREE = "03";
     public static final String END_CAUSE_FIVE = "05";
     public static final String END_CAUSE_TWO = "02";
     /**
      * 打印类型
      */
     public static final String PRINT_TYPE_ = "Print";
     public static final String PREVIEW_TYPE = "Preview";
     public static final String PREVIEWANDPRINT_TYPE = "PreviewAndPrint";
     /**
      * @Fields MODUBLE_TYPE : 模块类型："05"
      */
     public static final String MODUBLE_TYPE = "05";
    // 支付状态失败
    public static final String PAYMENT_STATUS_FAIL = "00";
    // 支付状态成功
    public static final String PAYMENT_STATUS_SUCCEED = "01";
    /**
     * 分次给付模板标识
     */
    public static final String PROCESSFLA = "CLMGO";
    //分次给付任务创建类型
    public static final String TASKCREATETYPE = "1";
    
    /**
     * 单证打印查询无记录提示信息
     */
    public static final String DOCUMENT_PRINT_CLAIM = "您要查询的赔案不存在，请重新输入查询条件!";
    
    /**
     * 1秒 = 1000毫秒数
     */
    public static final int MILLISECONDS = 1000;
    
    /**
     * 1分钟 = 60秒
     */
    public static final int MINUTE = 60;
    
    /**
     * 1小时 = 60分钟
     */
    public static final int HOUR = 60;
    
    /**
     * 任务类型，审核类型
     */
    public static final String AUDIT_TASK_TYPE = "3";
    
    /**
     * 任务类型，审批类型
     */
    public static final String APPROVED_TASK_TYPE = "4";
    
    /**
     * 任务类型，事中质检类型
     */
    public static final String MIDC_TASK_TYPE = "8";
    
    /**
     * 任务类型，事后质检类型
     */
    public static final String AFC_TASK_TYPE = "9";
    
    /**
     * 分页标识,不进行分页
     */
    public static final String ISPAGEFLAG_NO = "0";
    
    /**
     * 分页标识，进行分页
     */
    public static final String ISPAGEFLAG_YES = "1";
    
    /**
     * 空字符串用于判断
     */
    public static final String NULL_STR = "";
    
    /**
     * 4：查询全部个人池待办
     */
    public static final String QUERY_ALL_PERSONAL_TASK_TYPE = "4";
    
    /**
     * 任务查询：3-全部查询
     */
    public static final String QUERY_ALL_TASK_TYPE = "3";
    
    /**
     * -1用于进行判断
     */
    public static final int ADJUST = -1;
    
    /**
     * 用于分割字符串
     */
    public static final String SPLIT_STR = ":";
    
    /**
     * 机构级别第二级
     */
    public static final String ORGAN_GRADE_TWO = "02";

    /**
     * 单证打印查询赔案失败
     */
    public static final String DOCUMENT_PRINT_ERROR = "查询赔案失败!";
    
    public static final String POLICY_CLAUSE_BYCASEID = "该赔案无关联保单!";
    
    public static final String CLAIM_PRINT_IP = "**********";
    //案件标识正常
    public static final String CASE_FLAG_YES = "0";
    //案件标识非正常
    public static final String CASE_FLAG_NO = "1";
    /**
     * 事中质检参数类型----理赔类型
     */
    public static final String CHECK_PARA_CLAIM_TYPE = "01";
    /**
     * 事中质检参数类型----险种
     */
    public static final String CHECK_PARA_PRODUCT = "02";
    /**
     * 事中质检参数类型----操作机构
     */
    public static final String CHECK_PARA_ORGANIZATION= "03";
    /**
     * 事中质检参数类型----操作人员
     */
    public static final String CHECK_PARA_OPERATOR = "04";
    /**
     * 事中质检参数类型----案件标识
     */
    public static final String CHECK_PARA_CASE_FLAG = "08";
    /**
     * 签收确认创建工作流标识
     */
    public static final String SIGN_CONFIRM_FLAG = "1";
    /**
     * 工作流成功标识
     */
    public static final String WORKFLOW_SUCCESS = "0";
    /**
     * 工作流失败标识
     */
    public static final String WORKFLOW_FAIL = "1";
    
    /**
     * 领取频率  1:年领
     */
    public static final String PAY_TYPE_ONE = "1";
    /**
     * 领取频率  2:半年领
     */
    public static final String PAY_TYPE_TWO = "2";
    /**
     * 领取频率  3:季领
     */
    public static final String PAY_TYPE_THREE = "3";
    /**
     * 领取频率  4:月领
     */
    public static final String PAY_TYPE_FORE = "4";
    /**
     * 领取频率  5:趸领
     */
    public static final String PAY_TYPE_FIVE = "5";
    /**
     * 权限名称 ：审核权限
     */
    public static final String PERMISSIONNAME_AUDIT = "审核";
    /**
     * 权限名称 ：审批权限
     */
    public static final String PERMISSIONNAME_APPROVED = "审批";
    /**
     * 权限名称 ：事中质检权限
     */
    public static final String PERMISSIONNAME_MIDC = "事中质检";
    /**
     * 权限名称 ：事后质检权限
     */
    public static final String PERMISSIONNAME_AFC = "事后质检";
    /**
     * 业务来源 005：其他
     */
    public static final String BIZ_OURCE = "005";
    
    /**
     * 事中
     */
    public static final String THINGS = "1";
    
    public static final String CARE_SERVER = "您要查询的服务人员不存在，请重新输入查询条件！";
    

    /**
     * 补充单证模板标识
     */
    public static final String START_SUPPLY_CREATE_TEMPLATE = "CLMRFP";

    /**
     * 补充单证节点编码
     */
    public static final String START_SUPPLY_CREATE_NUMB = "CLM100201"; //创建的节点
    
    public static final String START_SUPPLY_CREATE_SUBMIT = "CLM109999"; //
    
    /**
     * 理赔回退管理流程-审核节点编码
     */
    public static final String ROLLBACK_AUDIT_TASK_CODE = "CLM040203";
    
    /**
     * 分配标识     自动分配 （1 个人申请 2人工分配 3 自动分配）
     */
    public static final String ASSIGNFLAG_AUTO = "3";
    
    /**
     * 分配标识     手工分配   （1 个人申请 2人工分配 3 自动分配）
     */
    public static final String ASSIGNFLAG_HAND = "2";
    
    /**
     * 分配标识 个人申请 （1 个人申请 2人工分配 3 自动分配）
     */
    public static final String ASSIGNFLAG_PERSONAL = "1";

    /**
     * 发起二核调工作流模板标志
     */
    public static final String START_UNDWER_TWICE_TEMPLATE = "CLMSU";
    
    /**
     * 结论是否生效工作流节点
     */
    public static final String START_UNDWER_TWICE_CODECON = "CLM110202";
    
    /**
     * 加费工作流节点
     */
    public static final String START_UNDWER_TWICE_CODEFEE = "CLM110203";
    
    /**
     * 核销工作流节点
     */
    public static final String START_UNDWER_TWICE_CODECANCLE = "CLM110204";
    
    /**
     * 分次给付审核节点编码
     */
    public static final String GRADATION_PAY_AUDIT = "CLM090201";
    
    /**
     * 超期补偿标识，1为超期
     */
    public static final BigDecimal EXCEEDDUEY = new BigDecimal(1);
    
    /**
     * 超期补偿标识，0为未超期
     */
    public static final BigDecimal EXCEEDDUEN = new BigDecimal(0);
    
    /**
     * 审批结论，1为通过
     */
    public static final BigDecimal APPROVECONCLUSIONY = new BigDecimal(1);
    
    /**
     * 审批结论，2为未通过
     */
    public static final BigDecimal APPROVECONCLUSIONN = new BigDecimal(2);
    
  //受检机构的CODE值
    public static final String ROLECODEONE = "1";
    public static final String ROLECODETWO = "2";
    public static final String ROLECODETHREE = "3";
    public static final String ROLECODEFOUR = "4";
    public static final String ROLECODEFIVE = "5";
    public static final String ROLECODESIX = "6";
    public static final String ROLECODESEVEN = "7";
    /**
     * 事中质检参数类型----出险原因
     */
    public static final String CHECK_PARA_REASON = "09";
    
    /**
     * 参数定义表名称
     */
    public static final String DEFINDEMNITYDUE = "indemnityDue";
    
    /**
     * 发送方式 - 短信
     */
    public static final BigDecimal SMSSENDWAY = new BigDecimal(1);
    
    /**
     * 发送方式 - 邮件
     */
    public static final BigDecimal MAILSENDWAY = new BigDecimal(2);
    
    /**
     * 一年的天数
     */
    public static final BigDecimal YEARDAYCOUNT = new BigDecimal(365);
    
    /**
     * 审核结论 - 全部给付
     */
    public static final BigDecimal CONCLUSIONALLPAY = new BigDecimal(1);
    
    /**
     * 审核结论 - 部分给付
     */
    public static final BigDecimal CONCLUSIONPARTPAY = new BigDecimal(2);
    
    /**
     * 审核结论  - 3为拒付
     */
    public static final BigDecimal CONCLUSIONREJECT = new BigDecimal(3);
    
    /**
     * 是否为常规给付 - 2为否
     */
    public static final BigDecimal ISCOMMONN = new BigDecimal(2);
    
    /**
     * 结算类型 - 现金价值
     */
    public static final String CASHVALUE = "1";
    
    /**
     * 结算类型 - 账户价值
     */
    public static final String ACCOUNTVALUE = "2";
    
    /**
     * 结算类型 - 自垫本金
     */
    public static final String PADPRINCIPAL = "3";
    
    /**
     * 结算类型 - 自垫利息
     */
    public static final String PADINTEREST = "4";
    
    /**
     * 结算类型 - 贷款本金
     */
    public static final String LOANPRINCIPAL = "5";
    
    /**
     * 结算类型 - 贷款利息
     */
    public static final String LOANINTEREST = "6";
    
    /**
     * 结算类型 - 扣除保费
     */
    public static final String DEDUCTPREMIUM = "7";
    
    /**
     * 结算类型 - 返还保费
     */
    public static final String RETURNPREMIUM = "8";
    
    /**
     * 结算类型 - 终了红利
     */
    public static final String FINALBONUS = "9";
    
    /**
     * 结算类型 - 利差返还
     */
    public static final String PRICERETURN = "10";
    
    /**
     * 结算类型 - 风险保费
     */
    public static final String RISKPREMIUM = "11";
    
    /**
     * 结算类型 - 保单管理费
     */
    public static final String CONTRACTMANAGECOST = "12";
    
    /**
     * 结算类型 - 应领未领的保证领取年金
     */
    public static final String NOTGETANNUITY = "13";
    
    /**
     * 结算类型 - 出险日后发放的年金/生存金
     */
    public static final String ACCIDENTBACKANNUITY = "14";
    
    /**
     * 结算类型 - 未追回满期金
     */
    public static final String NOTRECOVEREXPIRE = "15";
    
    /**
     * 关联到T_YES_NO表,0为否
     */
    public static final BigDecimal BIGDECIMALNO = new BigDecimal(0);
    
    /**
     * 关联到T_YES_NO表,1为是
     */
    public static final BigDecimal BIGDECIMALYES = new BigDecimal(1);
    /**
     * 用于截取字符串的数
     */
    public static final int SIXINT = 6;
    /**
     * 案件状态为待复核
     */
    public static final String CASESTATUSISREVIEW = "40";
    
    /**
     * 是否已回复
     */
    public static final BigDecimal YETREPLY = new BigDecimal(1);
    /**
     * BigDecimal(0)
     */
    public static final BigDecimal BIGDECIMAL_ZERO = new BigDecimal(0);
    /**
     * BigDecimal(1)
     */
    public static final BigDecimal BIGDECIMAL_ONE = new BigDecimal(1);
    /**
     * 生调标识
     */
    public static final String ONESURVEYFLAG = "1";
    /**
     * 调查类型标识
     */
    public static final String ONESURVEYTYPE = "2";
    /**
     * 调查原因
     */
    public static final String ONESURVEYREASON = "99";
    /**
     * 提起阶段标识
     */
    public static final String ONESURVEYSECTION = "4";
    /**
     * 回退状态 - 回退申请中
     */
    public static final BigDecimal BACKSTATUSAPPLY = new BigDecimal(0);
    /**
     * 回退状态 - 回退审核中
     */
    public static final BigDecimal BACKSTATUSAUDIT = new BigDecimal(1);
    /**
     * 回退状态 - 回退完成
     */
    public static final BigDecimal BACKSTATUSFINISH = new BigDecimal(2);
    /**
     * 回退状态 - 回退撤销
     */
    public static final BigDecimal BACKSTATUSREVOKE = new BigDecimal(3);
    /**
     * 业务锁名称 AC
     */
    public static final String LOCKAC = "AC";
    /**
     * 账单类型 - 门诊
     */
    public static final BigDecimal BILL_TYPE_OUTPATIENT = new BigDecimal(1);
    public static final BigDecimal BILL_TYPE_HIGH = new BigDecimal(2);
    public static final BigDecimal BILL_TYPE_CANCER = new BigDecimal(3);
    /**
     * String 14
     */
    public static final String STRING_FOURTEEN = "14";
    /**
     * 公共参数类型 -预付比例
     */
    public static final String ADVANCE_PARA_TYPE = "1";
    
    /**
     * 非取消预付的标识
     */
    public static final String NON_CANCEL_ADVANCE_FLAG = "0";
    
    /**
     * 取消预付的标识
     */
    public static final String CANCEL_ADVANCE_FLAG = "1";
    
    /**
     * 预付审批结论为通过
     */
    public static final String ADVANCE_AUDIT_DECISION = new String("1");
    
    /**
     *传值到工作流，预付审批结论通过为1
     */
    public static final String BPM_ADVANCE_AUDIT_PASS = "1";
    
    /**
     *传值到工作流，预付审批结论不通过为0
     */
    public static final String NON_BPM_ADVANCE_AUDIT_PASS = "0";
    
    /**
     * 支付类型为返盘制
     */
    public static final String PAYMODE_BACK_SYSTEM = "32";
    
    /**
     * 接口调用成功
     */
    public static final String INVOKING_SUCCEED = "01";
    
    /**
     * 理赔编码
     */
    public static final String CLM = "067";
    
    /**
     * 保单锁：保全
     */
    public static final String POLICYLOCK_BQ = "BQ";
    
    /**
     * 保单锁：续期
     */
    public static final String POLICYLOCK_XQ = "XQ";
    
    /**
     * 保单锁：解锁标识
     */
    public static final String POLICYLOCK_CLEAR = "2";
    
    /**
     * 通知书号
     */
    public static final String ADVICE_NOTE_NUMBER = "DOCUMENTNO";
    
    /**
     * 通知书类型 - 分割单
     */
    public static final String CARVE_UP_LIST = "分割单";
    
    /**
     * 分割单 - 编码
     */
    public static final String CARVE_UP_LIST_CODE = "CLM_00001";
    
    /**
     * 通知书类型 - 拒付通知书
     */
    public static final String PROTEST_ADVICE_NOTE = "拒付通知书";
    
    /**
     * 拒付通知书 - 编码
     */
    public static final String PROTEST_ADVICE_NOTE_CODE = "CLM_00002";
    
    /**
     * 通知书类型 - 赔付依据与说明
     */
    public static final String PAY_GOES_AND_EXPLAIN = "赔付依据与说明";
    
    /**
     * 赔付依据与说明 - 编码
     */
    public static final String PAY_GOES_AND_EXPLAIN_CODE = "CLM_00010";
    
    /**
     * 通知书类型 - 索赔申请书
     */
    public static final String INDEMNITY_APPLICATION = "索赔申请书";
    
    /**
     * 索赔申请书 - 编码
     */
    public static final String INDEMNITY_APPLICATION_CODE = "CLM_00008";
    
    /**
     * 理赔单证通知书 - 编码
     */
    public static final String DOCUMENT_NOTE = "理赔单证通知书";
    
    /**
     * 理赔单证通知书 - 编码
     */
    public static final String DOCUMENT_NOTE_CODE = "CLM_00004";
    
    /**
     * 收费付费标识  - 收费
     */
    public static final String COLLECT_FEES = "1";
    
    /**
     * 收费付费标识  - 付费
     */
    public static final String PAY = "2";
    
    /**
     * 应收应付的收付状态  - 已收付
     */
    public static final String FEE_STATUS = "02";
    /**
     * 备注问题件类型
     */
    public static final String REMARK_COUNT_ONE_TYPE = "1";
    /**
     * 备注选项
     */
    public static final String REMARK_COUNT_ONE_OPTION = "101";
    
    /**
     * 任务分配状态 ：全部
     */
    public static final String ASSIGN_TASK_ALL = "1";
    
    /**
     * 任务分配状态 ：已分配
     */
    public static final String ASSIGN_TASK_ALREADY = "3";
    
    /**
     * 任务分配状态 ：未分配
     */
    public static final String ASSIGN_TASK_NON = "2";
    
    /**
     * 理赔类型 ：豁免
     */
    public static final String CLAIMTYPE_ELEVEN = "11";
    
    /**
     * 业务员类型：星级
     */
    public static final BigDecimal STAR_AGENT_TYPE = new BigDecimal("1");
    /**
     * 受托人类型：业务员
     */
    public static final BigDecimal TRUSTEE_TYPE_YWY = new BigDecimal("1");
    /**
     * 受托人类型：其它
     */
    public static final BigDecimal TRUSTEE_TYPE_OTHER = new BigDecimal("2");
    /**
     * 续保决定：不续保
     */
    public static final BigDecimal DECISION_DESC_NO = new BigDecimal("1");
    /**
     * 续保决定：可以续保
     */
    public static final BigDecimal DECISION_DESC_AGREE = new BigDecimal("2");
    /**
     * 续保决定：已经续保
     */
    public static final BigDecimal DECISION_DESC_ALREADY = new BigDecimal("3");
    /**
     * 续保决定：豁免标识-YES
     */
    public static final BigDecimal WAIVE_YES = new BigDecimal("1");
    /**
     * 续保决定：豁免标识-NO
     */
    public static final BigDecimal WAIVE_NO = new BigDecimal("0");
    /**
     * 附加险
     */
    public static final String SHORT_RISK = "10002";
    /**
     * 意外险
     */
    public static final String PRODUCT__CATEGORY2_UNEXPECTED = "30002";
    /**
     * 全部给付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_ALLPAY = new BigDecimal("1");
    /**
     * 部分给付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_PARTPAY = new BigDecimal("2");
    /**
     * 协议给付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_AGREEMENTPAY = new BigDecimal("3");
    /**
     * 通融给付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_PARDONPAY = new BigDecimal("4");
    /**
     * 拒付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_REFUSEPAY = new BigDecimal("5");
    /**
     * 清单导出每页数量
     */
    public static final int EXCLEPAGESIZE = 60000;
    /**
     * @description 处理需要分页数据集合
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param dataList 需要处理的总集合
     * @param sheetName sheet名称
     * @return 处理后的sheet页数据
    */
    public static List<ExcleSheetVO> byPage(List<?> dataList,String sheetName) {
        int pagecount; // 总页数
        int nowDataListPoint = 0; // 读取到接收的哪一条数据

        pagecount = countPages(dataList); // 计算页码
        List<ExcleSheetVO> pageList = new ArrayList<ExcleSheetVO>(); // 页面分页
        for (int i = 0; i < pagecount; i++) {

            List<Object> pagedata = new ArrayList<Object>();
            // 把传来的数据取出
            while (nowDataListPoint < dataList.size()) {
                pagedata.add(dataList.get(nowDataListPoint));
                nowDataListPoint += 1;
                if (nowDataListPoint != 0 && nowDataListPoint % EXCLEPAGESIZE == 0) {
                    break;
                }
            }
            ExcleSheetVO page = new ExcleSheetVO(sheetName + (i + 1), (i + 1) + "", pagecount + "", pagedata);
            pageList.add(page);
        }
        return pageList;
    }
    /**
     * @description 获取sheet分页名称
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param page 集合
     * @return sheet分页名称集合
    */
    public static ArrayList<String> getSheetName(List<ExcleSheetVO> page) {
        ArrayList<String> sheetList = new ArrayList<String>();
        for (int i = 0; i < page.size(); i++) {
        	sheetList.add(page.get(i).getSheetName());
        }
        return sheetList;
    }
    /**
     * @description 计算分页
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param dataList 总数量
     * @return 分几个sheet导出
    */
    public static int countPages(List<?> dataList) {
        int recordcount = dataList.size(); // 总记录数
        //EXCLEPAGESIZE 一个sheet展示多少数据
        return (recordcount + EXCLEPAGESIZE - 1) / EXCLEPAGESIZE;
    }
    /**
     * 获取意健险平台结果查询网址
     */
    public static final String CLAIM_CIITQUERY = "ciitQuery";

    /** 
     * @Fields RECORD_TYPE : 理赔信息查看类型：5-行业共享信息
     */
    public static final String RECORD_TYPE_FIVE = "5";
    
    /**
     * String 0
     */
    public static final String STRING_ZERO = "0";
    /**
     * String 1
     */
    public static final String STRING_ONE = "1";
    /**
     * String 2
     */
    public static final String STRING_TWO = "2";
    /**
     * String 3
     */
    public static final String STRING_THREE = "3";
    /**
     * String 4
     */
    public static final String STRING_FOUR = "4";
    /**
     * String 5
     */
    public static final String STRING_FIVE = "5";
    /**
     * String 6
     */
    public static final String STRING_SIX = "6";
    /**
     * String 7
     */
    public static final String STRING_SEVEN = "7";
    /**
     * String 8
     */
    public static final String STRING_EIGHT = "8";
    /**
     * String 9
     */
    public static final String STRING_NINE = "9";
    /**
     * String 10
     */
    public static final String STRING_TEN = "10";
    /**
     * String 11
     */
    public static final String STRING_EVELEN = "11";
    /**
     * String 12
     */
    public static final String STRING_TWELVE = "12";
    /**
     * String 13
     */
    public static final String STRING_THIRTEEN = "13";
    /**
     * String 15
     */
    public static final String STRING_FIFTEENTH = "15";
    /**
     * String 16
     */
    public static final String STRING_SIXTEEN = "16";
    /**
     * String 17
     */
    public static final String STRING_SEVENTEEN  = "17";
    /**
     * String 18
     */
    public static final String STRING_EIGHTEEN   = "18";
    /**
     * String 19
     */
    public static final String STRING_NINETEEN   = "19";
    /**
     * String 20
     */
    public static final String STRING_TWENTY   = "20";
    /**
     * String 21
     */
    public static final String STRING_TWENTY_ONE   = "21";
    /**
     * String 22
     */
    public static final String STRING_TWENTY_TWO   = "22";
    /**
     * String 23
     */
    public static final String STRING_TWENTY_THREE   = "23";
    /**
     * String 24
     */
    public static final String STRING_TWENTY_FOUR = "24";
    /**
     * String 25
     */
    public static final String STRING_TWENTYFIVE = "25";


    /**
     * 86机构
     */
    public static final String ZONG_ORG_CODE = "86";

    /**
     * 清单导出最大数
     */
    public static final int NINE_TEN_NINE_WAN=999999;
    public static final String PERMISSIONNAME_DIFFICULT_APPROVED = "审批权限(疑难)";
    /**
     * 受理渠道10：直赔
     */
    public static final String CHANNEL_CODE_TEN = "11";
    /**
     * 受理渠道 11：快赔
     */
    public static final String CHANNEL_CODE_ELEVEN = "12";
    /**
     * 回退案件支付时点-回退前
     */
    public static final String DIRECT_PAYMENT_TIME_BEFORE = "回退前";
    /**
     * 回退案件支付时点-回退后
     */
    public static final String DIRECT_PAYMENT_TIME_AFTER = "回退后";
    /** 
     * @Fields DIRECT_CLM_BUSI_PRODCODE : 对公支付姓名
     */ 
     public static final List<String> DIRECT_PAYEE_COMPANY_NAME = new ArrayList<String>();
     static {
    	 DIRECT_PAYEE_COMPANY_NAME.add("乐约电子科技（上海）有限公司");
    	 DIRECT_PAYEE_COMPANY_NAME.add("杭州米宝科技有限公司");
    	 DIRECT_PAYEE_COMPANY_NAME.add("杭州米加健康科技有限公司");
     }
    /**
     * 超期补偿标识-1
     */
    public static final BigDecimal OVERDUE_FLAG =  new BigDecimal(1);
    /**
     * 终止
     */
    public static final BigDecimal LIABILITY_STATUS_STOP = new BigDecimal("3");
    /**
     * 理赔类型：身故
     */
    public static final String CLAIM_TYPE_ONG = "01";
    
    /**
     * 意健险接口调用成功状态码
     */
    public static final String CIITC_RET_CODE_001 = "001";
    
    /**
     * 查询标识
     */
    public static final String CX_FLAG = "1";
    
    /**
     * 理算特殊处理的产品编码
     */
    public static final String CLM_BUSI_PRODCODE_NINEEIGHTFOUR = "00984000";
    /** 
	* @Fields ACQUIST_WAY_ONE : 采集方式-外包
	*/ 
	public static final BigDecimal ACQUIST_WAY_ONE = new BigDecimal("1");
	/** 
	* @Fields ACQUIST_WAY_TWO : 采集方式-核心自采
	*/ 
	public static final BigDecimal ACQUIST_WAY_TWO = new BigDecimal("2");
	/** 
	* @Fields ACQUIST_WAY_THREE : 采集方式-数采自采
	*/ 
	public static final BigDecimal ACQUIST_WAY_THREE = new BigDecimal("3");
	/** 
	* @Fields ACQUIST_WAY_FOUR : 采集方式-直连
	*/ 
	public static final BigDecimal ACQUIST_WAY_FOUR = new BigDecimal("4");
	/** 
	* @Fields OUTSOURCE_WAY_ONE : 外包方式-驻场外包
	*/ 
	public static final BigDecimal OUTSOURCE_WAY_ONE = new BigDecimal("1");
	/** 
	* @Fields OUTSOURCE_WAY_TWO : 外包方式-离场外包
	*/ 
	public static final BigDecimal OUTSOURCE_WAY_TWO = new BigDecimal("2");
	/** 
	* @Fields OUTSOURCE_WAY_THREE : 外包方式-数采自采
	*/ 
	public static final BigDecimal OUTSOURCE_WAY_THREE = new BigDecimal("3");
	/**
     * String 111
     */
    public static final String STRING_ONEHUNDREDELEVEN = "111";
    /**
     * String 112
     */
    public static final String STRING_ONEHUNDREDTWELVE  = "112";
    /**
     * String 113
     */
    public static final String STRING_ONEHUNDREDTHIRTEEN  = "113";
    /**
     *  数字5000
     */
    public static final int FIVE_THOUSAND = 5000;
    /**
     * 时效清单-回复状态：1-未回复
     */
    public static final BigDecimal CODE_ONE = new BigDecimal(1);

    /**
     * 时效清单-回复状态：2-已回复
     */
    public static final BigDecimal CODE_TWO = new BigDecimal(2);

    /**
     * 时效清单-回复状态：3-问题件
     */
    public static final BigDecimal CODE_THREE = new BigDecimal(3);

    /**
     * 时效清单-回复状态：4-待发送
     */
    public static final BigDecimal CODE_FOUR = new BigDecimal(4);
    /**
     * 用户名AUTO
     */
    public static final String AUTO = "AUTO";
    /**
     *  数字9999
     */
    public static final int NINE_THOUSAND_NINE_HUNDRED_NINTY_NINE = 9999;
}
