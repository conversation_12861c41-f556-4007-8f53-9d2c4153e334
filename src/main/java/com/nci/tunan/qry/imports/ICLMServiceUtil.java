package com.nci.tunan.qry.imports;



import java.util.List;

/**
 * @description 服务总接口
 * <AUTHOR>
 * @date 2015-11-04 14:02:12
 * 
 */
public interface ICLMServiceUtil {

    /**
     * @description 理赔-核保信息查询接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.uw.interfaces.uwvo.queryInfo.uwQuery.
     *            UwInputDataInterfaceVo
     * @return 输出的参数VO：com.nci.tunan.uw.interfaces.uwvo.queryInfo.uwQuery.
     *         UwOutPutDataInterfaceVo
     */
    public com.nci.tunan.uw.interfaces.uwvo.queryInfo.uwQuery.UwOutPutDataInterfaceVo uwiuwqueryuccgetUwExtraPrem(
            com.nci.tunan.uw.interfaces.uwvo.queryInfo.uwQuery.UwInputDataInterfaceVo inputData);

    /**
     * @description 计算年金、生存金、满期金
     * @param inputData
     *            输入参数VO:com.nci.tunan.mms.interfaces.calc.exports.calcannuvalue
     *            .vo.MmsCalcAnnuValueReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.mms.interfaces.calc.exports.calcannuvalue.vo
     *         .MmsCalcAnnuValueResVO
     */
    public com.nci.tunan.mms.interfaces.calc.exports.calcannuvalue.vo.MmsCalcAnnuValueResVO prdimmscalcannuvalueucccalcAnnuValue(
            com.nci.tunan.mms.interfaces.calc.exports.calcannuvalue.vo.MmsCalcAnnuValueReqVO inputData);

    /**
     * @description 保单锁
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.LockServiceSetVO
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.LockResultVO
     */
    public com.nci.core.common.interfaces.vo.LockResultVO paipublockuccsetLockOne(
            com.nci.core.common.interfaces.vo.LockServiceSetVO inputData);

    /**
     * @description 保单锁
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.LockServiceQueryVO
     * @return 输出的参数VO：boolean
     */
    public boolean paipublockuccqueryLockOne(com.nci.core.common.interfaces.vo.LockServiceQueryVO inputData);

    /**
     * @description 风险保额查询
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.riskquery.
     *            QueryRiskAmountReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.riskquery.
     *         QueryRiskAmountResVO
     */
    public com.nci.tunan.pa.interfaces.serviceData.riskquery.QueryRiskAmountResVO pairiskamountuccqueryRiskAmount(
            com.nci.tunan.pa.interfaces.serviceData.riskquery.QueryRiskAmountReqVO inputData);

    /**
     * @description 统计个人池及共享池现有的待办任务总数
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.task.count.CountTaskRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.task.count.CountTaskResultVO
     */
    public com.nci.tunan.bpm.vo.task.count.CountTaskResultVO bpmiqueryucccountTaskNum(
            com.nci.tunan.bpm.vo.task.count.CountTaskRequestVO inputData);

    /**
     * @description 将指定流程终止
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.process.cancel.
     *            CancelProcessRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.process.cancel.CancelProcessResultVO
     */
    public com.nci.tunan.bpm.vo.process.cancel.CancelProcessResultVO bpmiprocessucccancelProcess(
            com.nci.tunan.bpm.vo.process.cancel.CancelProcessRequestVO inputData);

    /**
     * @description 按照指定条件查询共享池/个人池任务
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.process.query.
     *            QueryProcessRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.process.query.QueryProcessResultVO
     */
    public com.nci.tunan.bpm.vo.process.query.QueryProcessResultVO bpmiqueryuccqueryTask(
            com.nci.tunan.bpm.vo.process.query.QueryProcessRequestVO inputData);

    /**
     * @description 支持任务进行查询、获取、更新操作
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.task.acquire.AcquireTaskRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.task.acquire.AcquireTaskResultVO
     */
    public com.nci.tunan.bpm.vo.task.acquire.AcquireTaskResultVO bpmitaskuccacquireTask(
            com.nci.tunan.bpm.vo.task.acquire.AcquireTaskRequestVO inputData);

    /**
     * @description 按照输入数据创建工作流实例
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.process.create.
     *            CreateProcessRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.process.create.CreateProcessResultVO
     */
    public com.nci.tunan.bpm.vo.process.create.CreateProcessResultVO bpmiprocessucccreateProcess(
            com.nci.tunan.bpm.vo.process.create.CreateProcessRequestVO inputData);

    /**
     * @description 将指定任务进行提交
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.process.submit.
     *            SubmitProcessRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.process.submit.SubmitProcessResultVO
     */
    public com.nci.tunan.bpm.vo.process.submit.SubmitProcessResultVO bpmitaskuccsubmitTask(
            com.nci.tunan.bpm.vo.process.submit.SubmitProcessRequestVO inputData);

    /**
     * @description 将指定任务从个人池释放至共享池
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.task.release.ReleaseTaskRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.task.release.ReleaseTaskResultVO
     */
    public com.nci.tunan.bpm.vo.task.release.ReleaseTaskResultVO bpmitaskuccreleaseTask(
            com.nci.tunan.bpm.vo.task.release.ReleaseTaskRequestVO inputData);

    /**
     * @description 将指定任务指派给指定人员
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.task.assign.AssignTaskRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.task.assign.AssignTaskResultVO
     */
    public com.nci.tunan.bpm.vo.task.assign.AssignTaskResultVO bpmitaskuccassignTask(
            com.nci.tunan.bpm.vo.task.assign.AssignTaskRequestVO inputData);

    /**
     * @description 统计指定时间段内个人已完成任务数
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.task.countcl.CountCLTaskRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.task.countcl.CountCLTaskResultVO
     */
    public com.nci.tunan.bpm.vo.task.countcl.CountCLTaskResultVO bpmiqueryucccountCLTask(
            com.nci.tunan.bpm.vo.task.countcl.CountCLTaskRequestVO inputData);

    /**
     * @description 查询保单信息
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.query.exports.
     *            querypolicyinfo.vo.QueryPolicyInfoReqData
     * @return 
     *         输出的参数VO：com.nci.tunan.pa.interfaces.query.exports.querypolicyinfo.
     *         vo.QueryPolicyInfoResData
     */
    public com.nci.tunan.pa.interfaces.query.exports.querypolicyinfo.vo.QueryPolicyInfoResData paiquerypolicyinfouccgetPolicyInfo(
            com.nci.tunan.pa.interfaces.query.exports.querypolicyinfo.vo.QueryPolicyInfoReqData inputData);

    /**
     * @description 查询出险人
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.queryInjured.
     *            QueryInjuredReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.queryInjured.
     *         QueryInjuredResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.queryInjured.QueryInjuredResData paiqueryinjureduccqueryInjured(
            com.nci.tunan.pa.interfaces.serviceData.queryInjured.QueryInjuredReqData inputData);

    /**
     * @description 从保单表中获取某一时点保单信息
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.querypolicy.
     *            QueryPolicyReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.querypolicy.
     *         QueryPolicyResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData paiquerypolicyuccqueryPolicy(
            com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyReqData inputData);

    /**
     * @description 查询保单列表
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.querypolicylist
     *            .QueryPolicyListReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.querypolicylist.
     *         QueryPolicyListResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.querypolicylist.QueryPolicyListResData paiquerypolicylistuccgetPolicyList(
            com.nci.tunan.pa.interfaces.serviceData.querypolicylist.QueryPolicyListReqData inputData);

    /**
     * @description 根据保单号查询保单相关角色的信息
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.queryroleinfo.
     *            QueryRoleInfoReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.queryroleinfo.
     *         QueryRoleInfoResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.queryroleinfo.QueryRoleInfoResData paiqueryroleinfouccqueryRoleInfo(
            com.nci.tunan.pa.interfaces.serviceData.queryroleinfo.QueryRoleInfoReqData inputData);

    /**
     * @description 查询续保加费特约历史
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.queryfeehistory
     *            .QueryFeeHistoryReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.queryfeehistory.
     *         QueryFeeHistoryResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.queryfeehistory.QueryFeeHistoryResData paiqueryfeehistoryuccqueryFeeHistory(
            com.nci.tunan.pa.interfaces.serviceData.queryfeehistory.QueryFeeHistoryReqData inputData);

    /**
     * @description 既往保全信息查询接口（理赔）
     * @param inputData
     *            输入参数VO:com.nci.tunan.cs.interfaces.serviceData.querycstoclpast
     *            .CsPastReqDataVO
     * @return 输出的参数VO：com.nci.tunan.cs.interfaces.serviceData.querycstoclpast.
     *         CsPastResDataVO
     */
    public com.nci.tunan.cs.interfaces.serviceData.querycstoclpast.CsPastResDataVO paicstoclpastqueryuccquerypastcl(
            com.nci.tunan.cs.interfaces.serviceData.querycstoclpast.CsPastReqDataVO inputData);

    /**
     * @description 责任匹配与理算准备接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            dutymatetruthfigure.vo.DutyMateTruthFigureReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure
     *         .vo.DutyMateTruthFigureResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.DutyMateTruthFigureResVO prdidutymatetruthfigureuccdutymatetruthfigure(
            com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.DutyMateTruthFigureReqVO inputData);

    /**
     * @description 匹配理算接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            mateadjustment.vo.MateAdjustmentReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo
     *         .MateAdjustmentResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.MateAdjustmentResVO prdimateadjustmentuccmateadjustment(
            com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.MateAdjustmentReqVO inputData);

    /**
     * @description 查询合同处理方式接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            contrproswayquery.vo.ContrProsWayQueryReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.contrproswayquery
     *         .vo.ContrProsWayQueryResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.contrproswayquery.vo.ContrProsWayQueryResVO prdicontrproswayqueryucccontrProsWayQueryUccFun(
            com.nci.tunan.prd.interfaces.calc.exports.contrproswayquery.vo.ContrProsWayQueryReqVO inputData);

    /**
     * @description 理赔金转年金给付相关性接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            queryliabpayparamannu.vo.QueryLiabPayParamAnnuReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.queryliabpayparamannu
     *         .vo.QueryLiabPayParamAnnuResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.queryliabpayparamannu.vo.QueryLiabPayParamAnnuResVO queryLiabPayParamAnnuUccqueryLiabPayRelaUccAddr(
            com.nci.tunan.prd.interfaces.calc.exports.queryliabpayparamannu.vo.QueryLiabPayParamAnnuReqVO inputData);

    /**
     * @description 生存责任配置相关性接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            queryliabilityconfig.vo.QueryLiabilityConfigReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.queryliabilityconfig
     *         .vo.QueryLiabilityConfigResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.queryliabilityconfig.vo.QueryLiabilityConfigResVO queryLiabPayilityConfigUccqueryLiabilityConfigUccAddr(
            com.nci.tunan.prd.interfaces.calc.exports.queryliabilityconfig.vo.QueryLiabilityConfigReqVO inputData);

    /**
     * @description 计算年金领取金额接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            calcreceannuamount.vo.CalcReceAnnuAmountReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.calcreceannuamount
     *         .vo.CalcReceAnnuAmountResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.calcreceannuamount.vo.CalcReceAnnuAmountResVO prdicalcreceannuamountucccalcReceAnnuAmountUcc(
            com.nci.tunan.prd.interfaces.calc.exports.calcreceannuamount.vo.CalcReceAnnuAmountReqVO inputData);

    /**
     * @description 分期给付计算接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            calcinstallment.vo.ProductCalcInstallmentReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.calcinstallment.
     *         vo.ProductCalcInstallmentResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.calcinstallment.vo.ProductCalcInstallmentResVO prdicalcinstallmentucccalcInstallment(
            com.nci.tunan.prd.interfaces.calc.exports.calcinstallment.vo.ProductCalcInstallmentReqVO inputData);

    /**
     * @description 保存应收应付
     * @param inputData
     *            输入参数VO:com.nci.tunan.cap.interfaces.vo.ArapSaveInputData
     * @return 输出的参数VO：com.nci.tunan.cap.interfaces.vo.ArapSaveOutputData
     */
    public com.nci.tunan.cap.interfaces.vo.savearap.ArapSaveOutputData capiarapuccsaveArap(
    		com.nci.tunan.cap.interfaces.vo.savearap.ArapSaveInputData inputData);

    /**
     * @description 查询指定待办任务在指定人工节点的操作轨迹
     * @param inputData
     *            输入参数VO:com.nci.tunan.bpm.vo.task.query.QueryTaskRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.task.query.QueryTaskResultVO
     */
    public com.nci.tunan.bpm.vo.task.query.QueryTaskResultVO bpmiqueryuccqueryTaskInstance(
            com.nci.tunan.bpm.vo.task.query.QueryTaskRequestVO inputData);

    /**
     * @description 收付费信息变更
     * @param inputData
     *            输入参数VO:com.nci.tunan.cap.interfaces.vo.PremArapChangeInputData
     * @return 输出的参数VO：com.nci.tunan.cap.interfaces.vo.PremArapChangeOutputData
     */
    public com.nci.tunan.cap.interfaces.vo.premarapchange.PremArapChangeOutputData capichangecapinfouccchangePremArapByUnitNumber(
    		com.nci.tunan.cap.interfaces.vo.premarapchange.PremArapChangeInputData inputData);

    /**
     * @description 查询投连万能账户价值
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.
     *            EndCaseReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.
     *         InvestValueResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.endcase.InvestValueResData paiinvestvalueuccqueryInvestValue(
            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData);

    /**
     * @description 计算自垫及贷款本息
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.LoanSelfpayResData
    */
  public com.nci.tunan.pa.interfaces.serviceData.endcase.LoanSelfpayResData pailoanselfpayuccqueryLoanSelfpay(com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData);
  

    /**
     * @description 查询出现日之前客户欠缴的保费
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.
     *            EndCaseReqData
     * @return 
     *         输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData paidebtpremuccqueryDebtPrem(
            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData);

    /**
     * @description 查询出险日之后客户已交的保费
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.
     *            EndCaseReqData
     * @return 
     *         输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.OverPremResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.endcase.OverPremResData paioverpremuccqueryOverPrem(
            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData);

    /**
     * @description 计算利差账户本金和利息
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.AccountValueResData
    */
    public com.nci.tunan.pa.interfaces.serviceData.endcase.AccountValueResData paiaccountvalueuccqueryAccountValue(com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData);


    /**
     * @description 计算客户欠缴的保单管理费的风险保费
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.
     *            EndCaseReqData
     * @return 
     *         输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.AboutFeeResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.endcase.AboutFeeResData paiaboutfeeuccqueryAboutFee(
            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData);

    /**
     * @description 查询客户应领未领的保证领取年金
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.
     *            EndCaseReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.
     *         EnsureAnnualResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.endcase.EnsureAnnualResData paiensureannualuccqueryEnsureAnnual(
            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData);

    /**
     * @description 查询出险日之后领取的生存金年金满期金
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.endcase.
     *            EndCaseReqData
     * @return 
     *         输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.endcase.BackPremResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.endcase.BackPremResData paibackpremuccqueryBackPrem(
            com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputData);

    /**
     * @description 新契约复核完毕，将新契约数据推送给核保系统
     * @param inputData
     *            输入参数VO:com.nci.tunan.uw.interfaces.copyPolicy.exports.
     *            copyDetail.vo.CopyPolicyReqCompData
     * @return 
     *         输出的参数VO：com.nci.tunan.uw.interfaces.copyPolicy.exports.copyDetail.
     *         vo.CopyPolicyResCompData
     */
    public com.nci.tunan.uw.interfaces.copyPolicy.exports.copyDetail.vo.CopyPolicyResCompData uwicopypolicyuccsaveCopyPolicy(
            com.nci.tunan.uw.interfaces.copyPolicy.exports.copyDetail.vo.CopyPolicyReqCompData inputData);

    /**
     * @description 查询利率
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            bankinterestrate.vo.ProductBankInterestRateReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.bankinterestrate
     *         .vo.ProductBankInterestRateResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.bankinterestrate.vo.ProductBankInterestRateResVO prdibankinterestrateuccbankInterestRate(
            com.nci.tunan.prd.interfaces.calc.exports.bankinterestrate.vo.ProductBankInterestRateReqVO inputData);

    /**
     * @description 理赔结案接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.claimsettlement
     *            .ClaimSettlemetReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.claimsettlement.
     *         ClaimSettlementResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.claimsettlement.ClaimSettlementResData paiclaimsettlementucccliamSettlement(
            com.nci.tunan.pa.interfaces.serviceData.claimsettlement.ClaimSettlemetReqData inputData);

    /**
     * @description 保单/险种终止
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.busiprodstop.
     *            BusiProdStopReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.busiprodstop.
     *         BusiProdStopResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.busiprodstop.BusiProdStopResData paibusiprodstopuccupdatePolicyOrBusiProd(
            com.nci.tunan.pa.interfaces.serviceData.busiprodstop.BusiProdStopReqData inputData);

    /**
     * @description 查询保单服务信息
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.
     *            policyserviceinfo.QueryPolicyServiceInfoReqData
     * @return 
     *         输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.policyserviceinfo.
     *         QueryPolicyServiceInfoResData
     */
    public com.nci.tunan.pa.interfaces.serviceData.policyserviceinfo.QueryPolicyServiceInfoResData paiquerypolicyserviceinfouccqueryPolicyInfo(
            com.nci.tunan.pa.interfaces.serviceData.policyserviceinfo.QueryPolicyServiceInfoReqData inputData);

    /**
     * @description 保单应收应付查询
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.
     *            querypremaraplist.QueryPremArapReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.
     *         QueryPremArapResVO
     */
    public com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapResVO paiquerypremarapuccqueryPremArapMsg(
            com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapReqVO inputData);

    /**
     * @description 查询生存给付应领记录
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.querypayduelist
     *            .QuerPayDueReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.querypayduelist.
     *         QueryPayDueResVO
     */
    public com.nci.tunan.pa.interfaces.serviceData.querypayduelist.QueryPayDueResVO paiquerypaydueuccqueryPayDue(
            com.nci.tunan.pa.interfaces.serviceData.querypayduelist.QuerPayDueReqVO inputData);

    /**
     * @description 查询所有包含满期金责任的的产品列表接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.mms.interfaces.query.exports.
     *            liabilityinfoquery.vo.MmsLiabilityInfoQueryReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.mms.interfaces.query.exports.liabilityinfoquery
     *         .vo.MmsLiabilityInfoQueryResVO
     */
    public com.nci.tunan.mms.interfaces.query.exports.liabilityinfoquery.vo.MmsLiabilityInfoQueryResVO prdimmsliabilityinfoqueryuccliabilityInfoQuery(
            com.nci.tunan.mms.interfaces.query.exports.liabilityinfoquery.vo.MmsLiabilityInfoQueryReqVO inputData);

    /**
     * @description 计算险种现金价值
     * @param inputData
     *            输入参数VO:com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.
     *            vo.MmsCalcCashValueReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.
     *         MmsCalcCashValueResVO
     */
    public com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueResVO prdimmscalccashvalueucccalcCashValue(
            com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueReqVO inputData);

    /**
     * @description 终了红利计算接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.mms.interfaces.calc.exports.calctbamount.
     *            vo.MmsCalcTBAmountReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.mms.interfaces.calc.exports.calctbamount.vo.
     *         MmsCalcTBAmountResVO
     */
    public com.nci.tunan.mms.interfaces.calc.exports.calctbamount.vo.MmsCalcTBAmountResVO prdimmscalctbamountucccalcTBAmount(
            com.nci.tunan.mms.interfaces.calc.exports.calctbamount.vo.MmsCalcTBAmountReqVO inputData);

    /**
     * @description 理赔分期给付计划准备接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.
     *            dutypayplanment.vo.DutyPayPlanMentReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.dutypayplanment.
     *         vo.DutyPayPlanMentResVO
     */
    public com.nci.tunan.prd.interfaces.calc.exports.dutypayplanment.vo.DutyPayPlanMentResVO prdidutypayplanmentuccdutypayplanment(
            com.nci.tunan.prd.interfaces.calc.exports.dutypayplanment.vo.DutyPayPlanMentReqVO inputData);

    /**
     * @description 校验的保单账户是否有各项余额
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.accountcheck.
     *            AccountCheckReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.accountcheck.
     *         AccountCheckResVO
     */
    public com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckResVO paiaccountcheckuccaccountCheckUCC(
            com.nci.tunan.pa.interfaces.serviceData.accountcheck.AccountCheckReqVO inputData);

    /**
     * @description 计算第t个保单年度，t年f个月,t+1保单年度生存金、年金
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.
     *            calcSurvivalAmount.SurvivalAmountReqVO
     * @return 
     *         输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.calcSurvivalAmount
     *         .SurvivalAmountResVO
     */
    public com.nci.tunan.pa.interfaces.serviceData.calcSurvivalAmount.SurvivalAmountResVO paicalcsurvivalamountucccalculateNexAmount(
            com.nci.tunan.pa.interfaces.serviceData.calcSurvivalAmount.SurvivalAmountReqVO inputData);

    /**
     * @description 出险日后投连、万能险领取查询接口
     * @param inputData
     *            输入参数VO:com.nci.tunan.pa.interfaces.serviceData.operatequery.
     *            OperateQueryReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.operatequery.
     *         OperateQueryResVO
     */  
    public com.nci.tunan.pa.interfaces.serviceData.operatequery.OperateQueryResVO paioperatequeryuccoperateQuery(
            com.nci.tunan.pa.interfaces.serviceData.operatequery.OperateQueryReqVO inputData);
    /**
     * @description 业务相关号码查询转办业务
     * @param inputData 输入参数VO:com.nci.core.common.interfaces.vo.BizTurnVO
     * @return 输出的参数list：List<com.nci.core.common.interfaces.vo.BizTurnVO>
    */
    public List<com.nci.core.common.interfaces.vo.BizTurnVO> paibizturnuccfindAllBizTurn(com.nci.core.common.interfaces.vo.BizTurnVO inputData);
    /**
     * @description 签报业务相关号码查询签报业务
     * @param inputData 输入参数VO:com.nci.core.common.interfaces.vo.BizSignVO
     * @return 输出的参数list：List<com.nci.core.common.interfaces.vo.BizSignVO>
    */
    public List<com.nci.core.common.interfaces.vo.BizSignVO> paibizsignuccfindAllBizSign(com.nci.core.common.interfaces.vo.BizSignVO inputData);
    /**
     * @description 计算红利相关金额-1130

     * @param inputData 输入参数VO:com.nci.tunan.mms.interfaces.calc.exports.calcbonusamount.vo.MmsCalcBonusAmountReqVO
     * @return 输出的参数VO：com.nci.tunan.mms.interfaces.calc.exports.calcbonusamount.vo.MmsCalcBonusAmountResVO
    */
    public com.nci.tunan.mms.interfaces.calc.exports.calcbonusamount.vo.MmsCalcBonusAmountResVO prdimmscalcbonusamountucccalcBonusAmount(com.nci.tunan.mms.interfaces.calc.exports.calcbonusamount.vo.MmsCalcBonusAmountReqVO inputData);
    /**
     * @description 投连试算/卖出
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestReqData
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestResData
    */
    public com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestResData paisellinvestuccsellInvest(com.nci.tunan.pa.interfaces.serviceData.sellinvest.SellInvestReqData inputData);
    /**
     * @description 查询保额红利信息
     * @param inputData 输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.querybonusinfo.vo.PrdQueryBonusInfoReqVO
     * @return 输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.querybonusinfo.vo.PrdQueryBonusInfoResVO
    */
    public com.nci.tunan.prd.interfaces.calc.exports.querybonusinfo.vo.PrdQueryBonusInfoResVO prdiprdquerybonusinfouccfindBonusInfo(com.nci.tunan.prd.interfaces.calc.exports.querybonusinfo.vo.PrdQueryBonusInfoReqVO inputData);
    /**
     * @description 查询宽限期
     * @param inputData 输入参数VO:com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO
     * @return 输出的参数VO：com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryResVO
    */
    public com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryResVO prdimmsgraceperiodqueryuccgracePeriodQuery(com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO inputData);
  
    /**
     * @description 应收应付核销接口
     * @param inputData 输入参数VO:com.nci.tunan.cap.interfaces.vo.ArapVerificationInputData
     * @return 输出的参数VO：com.nci.tunan.cap.interfaces.vo.ArapVerificationOutputData  
    */
    public com.nci.tunan.cap.interfaces.vo.arapverification.ArapVerificationOutputData capiarapinfosaveuccchangeArapVerification(com.nci.tunan.cap.interfaces.vo.arapverification.ArapVerificationInputData inputData);
	/**
     * @description 保费计算接口(mms)
     * @param inputData 输入参数VO:com.nci.tunan.mms.interfaces.calc.exports.calpremandsa.vo.PrdPremCalByMMsReqVO
     * @return 输出的参数VO：com.nci.tunan.mms.interfaces.calc.exports.calpremandsa.vo.PrdPremCalByMMSResVO
    */
    public com.nci.tunan.mms.interfaces.calc.exports.calpremandsa.vo.PrdPremCalByMMSResVO prdicalpremandsaucccalProductPremAndSA(com.nci.tunan.mms.interfaces.calc.exports.calpremandsa.vo.PrdPremCalByMMsReqVO inputData);
     /**
     * @description 风险保额明细查询，测试调试
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailResVO
    */
    public com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailResVO pairiskamountuccqueryProductRiskDetail(com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailReqVO inputData);
    /**
     * @description 根据指定任务信息更新该任务实例内容
     * @param inputData 输入参数VO:com.nci.tunan.bpm.vo.process.update.UpdateProcessRequestVO
     * @return 输出的参数VO：com.nci.tunan.bpm.vo.process.update.UpdateProcessResultVO  
    */
    public com.nci.tunan.bpm.vo.process.update.UpdateProcessResultVO bpmitaskuccupdateTask(com.nci.tunan.bpm.vo.process.update.UpdateProcessRequestVO inputData);
	/**
     * @description 加费
     * @param inputData 输入参数VO:com.nci.tunan.mms.interfaces.calc.exports.addpremcal.vo.ProductExtraPremCalReqVO
     * @return 输出的参数VO：com.nci.tunan.mms.interfaces.calc.exports.addpremcal.vo.ProductExtraPremCalResVO
    */
    public com.nci.tunan.mms.interfaces.calc.exports.addpremcal.vo.ProductExtraPremCalResVO prdicalpremandsaucccalProductExtraPrem(com.nci.tunan.mms.interfaces.calc.exports.addpremcal.vo.ProductExtraPremCalReqVO inputData);
	/**
     * @description 保单变更履历查询
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.transdata.TransDataReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.transdata.TransDataResVO
    */
  //  public com.nci.tunan.pa.interfaces.serviceData.transdata.TransDataResVO paitransdataucctransDataUCC(com.nci.tunan.pa.interfaces.serviceData.transdata.TransDataReqVO inputData);
    /**
     * @description 理赔二核核销确认后调用核保将二核核销结论推给核保
     * @param inputData 输入参数VO:com.nci.tunan.uw.interfaces.vo.clmCancel.InputClmCancelVO
     * @return 输出的参数VO：com.nci.tunan.uw.interfaces.vo.clmCancel.OutputClmCancelVO
    */
   // public com.nci.tunan.uw.interfaces.vo.clmCancel.OutputClmCancelVO uwiclmcanceluccwriteOffConfirm(com.nci.tunan.uw.interfaces.vo.clmCancel.InputClmCancelVO inputData);
    /**
     * @description 理赔回退
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.clmrollback.CLMRollBackReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.clmrollback.CLMRollBackResVO
    */
  //  public com.nci.tunan.pa.interfaces.serviceData.clmrollback.CLMRollBackResVO paiclmrollbackuccrollBackAllData(com.nci.tunan.pa.interfaces.serviceData.clmrollback.CLMRollBackReqVO inputData);
    /**
     * @description 查询保证领取参数
     * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodReqVO
     * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodResVO
    */
   // public com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodResVO paiguaranteeperiodparamuccqueryGuaranteePeriodParam(com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodReqVO inputData);
  
    /**
     * @description 查询产品是否包含豁免责任接口
     * @param inputData 输入参数VO:com.nci.tunan.mms.interfaces.query.exports.waiverLiabQuery.vo.MmsWaiverLiabQueryReqVO
     * @return 输出的参数VO：com.nci.tunan.mms.interfaces.query.exports.waiverLiabQuery.vo.MmsWaiverLiabQueryResVO
    */
   public com.nci.tunan.mms.interfaces.query.exports.waiverLiabQuery.vo.MmsWaiverLiabQueryResVO prdimmswaiverliabqueryuccwaiverLiabQuery(com.nci.tunan.mms.interfaces.query.exports.waiverLiabQuery.vo.MmsWaiverLiabQueryReqVO inputData);
	  /**
	   * @description 查询终了红利标识接口
	   * @param inputData 输入参数VO:com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagReqVO
	   * @return 输出的参数VO：com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagResVO
	  */
  // public com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagResVO prdiprdquerytbflaguccqueryTBFlag(com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagReqVO inputData);
	/**
    * @description 查询责任组最新加费em
    * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.queryextraprem.ExtraPremEmReqVO
    * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.queryextraprem.ExtraPremEmResVO
   */
  // public com.nci.tunan.pa.interfaces.serviceData.queryextraprem.ExtraPremEmResVO paiextraprememuccextraPremEmUcc(com.nci.tunan.pa.interfaces.serviceData.queryextraprem.ExtraPremEmReqVO inputData);
	 /**
    * @description 保证领取金额
    * @param inputData 输入参数VO:com.nci.tunan.prd.interfaces.calc.exports.guaranteedamount.vo.GuaranteedAmountReqVO
    * @return 输出的参数VO：com.nci.tunan.prd.interfaces.calc.exports.guaranteedamount.vo.GuaranteedAmountResVO
   */
//	public com.nci.tunan.prd.interfaces.calc.exports.guaranteedamount.vo.GuaranteedAmountResVO prdiguaranteedamountuccguaranteedamount(com.nci.tunan.prd.interfaces.calc.exports.guaranteedamount.vo.GuaranteedAmountReqVO inputData);
	/**
     * @description 查询保单下某个产品退保的现金价值
     * @param inputData 输入参数VO:com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.CLMQueryInputVO
     * @return 输出的参数VO：com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.CLMQueryOutputVO
    */
	//public com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.CLMQueryOutputVO paiclmquerycashvalueuccgetCTAoumnt(com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.CLMQueryInputVO inputData);
	/**
     * @description 子任务轨迹更新状态--完成
     * @param inputData 输入参数VO:com.nci.tunan.css.interfaces.tasktrail.vo.SubTaskTrailUpdateInputVO
     * @return 输出的参数VO：com.nci.tunan.css.interfaces.tasktrail.vo.SubTaskTrailUpdateOutputVO  
    */
	///public com.nci.tunan.css.interfaces.tasktrail.vo.SubTaskTrailUpdateOutputVO cssicsssubtasktrailinfouccUpdateCssSubTaskTrailInfo(com.nci.tunan.css.interfaces.tasktrail.vo.SubTaskTrailUpdateInputVO inputData);
	/**
     * @description 查询理赔责任接口
     * @param inputData 输入参数VO:com.nci.tunan.prd.interfaces.query.exports.queryclmliab.PrdQueryClmLiabReqVO
     * @return 输出的参数VO：com.nci.tunan.prd.interfaces.query.exports.queryclmliab.PrdQueryClmLiabResVO
    */
    //public com.nci.tunan.prd.interfaces.query.exports.queryclmliab.PrdQueryClmLiabResVO prdiprdqueryclmliabuccqueryClmLiab(com.nci.tunan.prd.interfaces.query.exports.queryclmliab.PrdQueryClmLiabReqVO inputData);

    /**
     * @description 制盘状态查询
     * @param inputData 输入参数VO:com.nci.tunan.cap.interfaces.vo.transferaccount.TransferAccoutInputData
     * @return 输出的参数VO：com.nci.tunan.cap.interfaces.vo.transferaccount.TransferAccountOutputData  
    */
    public com.nci.tunan.cap.interfaces.vo.transferaccount.TransferAccountOutputData capitransferaccountqueryuccqueryTransAcc(com.nci.tunan.cap.interfaces.vo.transferaccount.TransferAccoutInputData inputData);
	/**
     * @description 计算投连万能扣费金额-1130
     * @param inputData 输入参数VO:com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeReqVO
     * @return 输出的参数VO：com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeResVO
    */
    public com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeResVO prdimmscalcaccountfeeucccalcAccountFee(com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeReqVO inputData);

    /**
     * @description 最近实收时间查询
     * @param inputData 输入参数VO:com.nci.tunan.cap.interfaces.vo.recentreceipt.RecentReceiptInputData
     * @return 输出的参数VO：com.nci.tunan.cap.interfaces.vo.recentreceipt.RecentReceiptOutputData  
    */
   public com.nci.tunan.cap.interfaces.vo.recentreceipt.RecentReceiptOutputData capiarapinfosaveuccqueryRecentReceiptTime(com.nci.tunan.cap.interfaces.vo.recentreceipt.RecentReceiptInputData inputData);
   /**
	 * @description 根据理赔出险日查询上次分红日及出险日是否在首次年金领取日之前
	 * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO
	 * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO
	*/
  // public com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO paiclaimquerypolicyinfouccqueryPolicyInfo(com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO inputData);
   
   /**
    * @description 更新豁免止期
    * @param inputData 输入参数VO:com.nci.tunan.pa.interfaces.serviceData.contractproduct.ContractProductReqVO
    * @return 输出的参数VO：com.nci.tunan.pa.interfaces.serviceData.contractproduct.ContractProductResVO
   */
 //public com.nci.tunan.pa.interfaces.serviceData.contractproduct.ContractProductResVO paicontractproductuccmodifyWaiverEnd(com.nci.tunan.pa.interfaces.serviceData.contractproduct.ContractProductReqVO inputData);
 
 /**
  * @description 查询流程实例运行中节点情况
  * @param inputData 输入参数VO:com.nci.tunan.bpm.vo.process.task.QueryRunTaskRequestVO
  * @return 输出的参数VO：com.nci.tunan.bpm.vo.process.task.QueryRunTaskResultVO  
 */
public com.nci.tunan.bpm.vo.process.task.QueryRunTaskResultVO bpmiqueryuccqueryRunTask(com.nci.tunan.bpm.vo.process.task.QueryRunTaskRequestVO inputData);

}
