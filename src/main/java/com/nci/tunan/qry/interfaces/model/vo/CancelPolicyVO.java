package com.nci.tunan.qry.interfaces.model.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class CancelPolicyVO implements Serializable{
	
private static final long serialVersionUID = 1L;
	
	private String agentLevel;
	private String agentLevelDesc;
	private String channelName;
	private String cancelStateDesc;
	private String service_bank_branch;
	private String canclestate;
	private String signSource;
	private String subinputType;
	private String userName;
	/**
	 * 用户机构
	 */
	private String userorganCode;
	/**
	 * 撤保类型 深圳用户使用
	 */
	private String canclestatexy;
	
	/**
	 * 真实姓名
	 */
	private String realName;
	private String queryType;
	private String salesOrganNameZ;//营业组
	private String salesOrganNameQ;//营业区
	private String salesOrganNameB;//营业部
	private String subinputTypeDesc; //银保通保单来源name
	
	/**
	 * 是否自保件
	 */
	private BigDecimal isSelfInsured;
	/**
	 * 是否互保件
	 */
	private BigDecimal isMutualInsured;
	
	/**
     * 保费金额
     */
	private BigDecimal totalPremAf;	
	
	/**
	 *  #146055 共同参保保单
	 */
	private String jointlyInsuredPolicy;
	
	public String getJointlyInsuredPolicy() {
		return jointlyInsuredPolicy;
	}
	public void setJointlyInsuredPolicy(String jointlyInsuredPolicy) {
		this.jointlyInsuredPolicy = jointlyInsuredPolicy;
	}
	
	public BigDecimal getTotalPremAf() {
		return totalPremAf;
	}
	public void setTotalPremAf(BigDecimal totalPremAf) {
		this.totalPremAf = totalPremAf;
	}
	public String getCanclestatexy() {
		return canclestatexy;
	}
	public void setCanclestatexy(String canclestatexy) {
		this.canclestatexy = canclestatexy;
	}
	public String getUserorganCode() {
		return userorganCode;
	}
	public void setUserorganCode(String userorganCode) {
		this.userorganCode = userorganCode;
	}
	public String getSubinputType() {
		return subinputType;
	}
	public void setSubinputType(String subinputType) {
		this.subinputType = subinputType;
	}
	public String getSubinputTypeDesc() {
		return subinputTypeDesc;
	}
	public void setSubinputTypeDesc(String subinputTypeDesc) {
		this.subinputTypeDesc = subinputTypeDesc;
	}
	public String getSignSource() {
		return signSource;
	}
	public void setSignSource(String signSource) {
		this.signSource = signSource;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public String getCanclestate() {
		return canclestate;
	}
	public void setCanclestate(String canclestate) {
		this.canclestate = canclestate;
	}
	public String getService_bank_branch() {
		return service_bank_branch;
	}
	public void setService_bank_branch(String service_bank_branch) {
		this.service_bank_branch = service_bank_branch;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public String getCancelStateDesc() {
		return cancelStateDesc;
	}
	public void setCancelStateDesc(String cancelStateDesc) {
		this.cancelStateDesc = cancelStateDesc;
	}
	public String getChannelName() {
		return channelName;
	}
	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}
	public String getAgentLevelDesc() {
		return agentLevelDesc;
	}
	public void setAgentLevelDesc(String agentLevelDesc) {
		this.agentLevelDesc = agentLevelDesc;
	}
	private String holderResidentName;
	public String getHolderResidentName() {
		return holderResidentName;
	}
	public void setHolderResidentName(String holderResidentName) {
		this.holderResidentName = holderResidentName;
	}
	public String getInsuredResidentName() {
		return insuredResidentName;
	}
	public void setInsuredResidentName(String insuredResidentName) {
		this.insuredResidentName = insuredResidentName;
	}
	private String insuredResidentName;
	private String policyCode;
	//private Date policyDate;
	private String channelType;
	//private String submitChannel;
	private BigDecimal submitChannel;//保单来源
	private String winningStartFlag;
	private String highSaIndi;
	private String applyCode;
	private String customerVip;
	private String blacklistFlag;
	private String customerName;
	private String state;
	private String insuredName;
	private String policyHolder;
	
	public String getPolicyHolder() {
		return policyHolder;
	}
	public void setPolicyHolder(String policyHolder) {
		this.policyHolder = policyHolder;
	}
	public String getInsuredName() {
		return insuredName;
	}
	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}
	private String salesChannelName;
	public String getSalesChannelName() {
		return salesChannelName;
	}
	public void setSalesChannelName(String salesChannelName) {
		this.salesChannelName = salesChannelName;
	}
	private String reason;
	private Date applyTime;
	private Date operateTime;
	public Date getOperateTime() {
		return operateTime;
	}
	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}
	private String agentName;
	private String salesOrganCode;
	private String salesOrganCodeTwo;
	private String salesOrganCodeThree;
	private String agentCode;
	private String feeStatus;
	private String feeType;
	private Date startDate;
	private Date endDate;
	private String organCode;
	private String isScan;
	private String serviceBank;
	private String customerLevelDesc;
	public String getCustomerLevelDesc() {
		return customerLevelDesc;
	}
	public void setCustomerLevelDesc(String customerLevelDesc) {
		this.customerLevelDesc = customerLevelDesc;
	}
	private String bankBranch;
	private String organName;
	private String isQualityAgent;
	private Date applyDate;
	private String bank;
	private String bankName;
	
	public BigDecimal getSubmitChannel() {
		return submitChannel;
	}
	public void setSubmitChannel(BigDecimal submitChannel) {
		this.submitChannel = submitChannel;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public Date getApplyDate() {
		return applyDate;
	}
	public String getServiceBank() {
		return serviceBank;
	}
	public void setServiceBank(String serviceBank) {
		this.serviceBank = serviceBank;
	}
	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}
	public String getIsQualityAgent() {
		return isQualityAgent;
	}
	public void setIsQualityAgent(String isQualityAgent) {
		this.isQualityAgent = isQualityAgent;
	}
	public String getOrganName() {
		return organName;
	}
	public void setOrganName(String organName) {
		this.organName = organName;
	}
	public String getIsScan() {
		return isScan;
	}
	public void setIsScan(String isScan) {
		this.isScan = isScan;
	}
	public String getBank() {
		return bank;
	}
	public void setBank(String bank) {
		this.bank = bank;
	}
	public String getBankBranch() {
		return bankBranch;
	}
	public void setBankBranch(String bankBranch) {
		this.bankBranch = bankBranch;
	}
	public String getOrganCode() {
		return organCode;
	}
	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	public String getAgentCode() {
		return agentCode;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}
	public String getAgentLevel() {
		return agentLevel;
	}
	public void setAgentLevel(String agentLevel) {
		this.agentLevel = agentLevel;
	}
	public String getPolicyCode() {
		return policyCode;
	}
	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getChannelType() {
		return channelType;
	}
	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}


	/*public String getSubmitChannel() {
		return submitChannel;
	}
	public void setSubmitChannel(String submitChannel) {
		this.submitChannel = submitChannel;
	}*/
	public String getApplyCode() {
		return applyCode;
	}
	public String getFeeStatus() {
		return feeStatus;
	}
	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}
	public String getFeeType() {
		return feeType;
	}
	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}
	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public Date getApplyTime() {
		return applyTime;
	}
	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}
	public String getAgentName() {
		return agentName;
	}
	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	public String getSalesOrganCode() {
		return salesOrganCode;
	}
	public void setSalesOrganCode(String salesOrganCode) {
		this.salesOrganCode = salesOrganCode;
	}
	public String getSalesOrganCodeTwo() {
		return salesOrganCodeTwo;
	}
	public void setSalesOrganCodeTwo(String salesOrganCodeTwo) {
		this.salesOrganCodeTwo = salesOrganCodeTwo;
	}
	
	public String getWinningStartFlag() {
		return winningStartFlag;
	}
	public void setWinningStartFlag(String winningStartFlag) {
		this.winningStartFlag = winningStartFlag;
	}
	public String getHighSaIndi() {
		return highSaIndi;
	}
	public void setHighSaIndi(String highSaIndi) {
		this.highSaIndi = highSaIndi;
	}
	public String getCustomerVip() {
		return customerVip;
	}
	public void setCustomerVip(String customerVip) {
		this.customerVip = customerVip;
	}
	public String getBlacklistFlag() {
		return blacklistFlag;
	}
	public void setBlacklistFlag(String blacklistFlag) {
		this.blacklistFlag = blacklistFlag;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getSalesOrganCodeThree() {
		return salesOrganCodeThree;
	}
	public void setSalesOrganCodeThree(String salesOrganCodeThree) {
		this.salesOrganCodeThree = salesOrganCodeThree;
	}
	public String getQueryType() {
		return queryType;
	}
	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}
	public String getSalesOrganNameZ() {
		return salesOrganNameZ;
	}
	public void setSalesOrganNameZ(String salesOrganNameZ) {
		this.salesOrganNameZ = salesOrganNameZ;
	}
	public String getSalesOrganNameQ() {
		return salesOrganNameQ;
	}
	public void setSalesOrganNameQ(String salesOrganNameQ) {
		this.salesOrganNameQ = salesOrganNameQ;
	}
	public String getSalesOrganNameB() {
		return salesOrganNameB;
	}
	public void setSalesOrganNameB(String salesOrganNameB) {
		this.salesOrganNameB = salesOrganNameB;
	}
	public BigDecimal getIsSelfInsured() {
		return isSelfInsured;
	}
	public void setIsSelfInsured(BigDecimal isSelfInsured) {
		this.isSelfInsured = isSelfInsured;
	}
	public BigDecimal getIsMutualInsured() {
		return isMutualInsured;
	}
	public void setIsMutualInsured(BigDecimal isMutualInsured) {
		this.isMutualInsured = isMutualInsured;
	}
    

}
