package com.nci.tunan.qry.interfaces.model.bo;

import com.nci.udmp.framework.model.*;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description UserVO对象
 * <AUTHOR> 
 * @date 2015-08-24 10:34:38  
 */
public class CssUserBO extends BaseBO {	
	 /** 
    * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
    */ 
    
    private static final long serialVersionUID = 1L;
    /** 
	* @Fields deptId :  所属部门id
 	*/ 
	private BigDecimal deptId;
	 /** 
	* @Fields unitName :  统一认证单位名称
 	*/ 
	private String unitName;
	 /** 
	* @Fields realName :  真实姓名
 	*/ 
	private String realName;
	 /** 
	* @Fields idType :  证件类型
 	*/ 
	private String idType;
	 /** 
	* @Fields needChangePass :  是否需要修改口令标志
 	*/ 
	private String needChangePass;
	 /** 
	* @Fields phone :  手机号码
 	*/ 
	private String phone;
	 /** 
	* @Fields organCode :  null
 	*/ 
	private String organCode;
		 /** 
	* @Fields shortNum :  排序序号
 	*/ 
	private BigDecimal shortNum;
	 /** 
	* @Fields idCard :  ID卡号
 	*/ 
	private String idCard;
		 /** 
	* @Fields email :  邮件地址
 	*/ 
	private String email;
	 /** 
	* @Fields password :  口令
 	*/ 
	private String password;
	 /** 
	* @Fields organId :  所属机构 id
 	*/ 
	private BigDecimal organId;
	 /** 
	* @Fields userId :  用户ID
 	*/ 
	private BigDecimal userId;
	 /** 
	* @Fields disableDate :  禁用时间
 	*/ 
	private Date disableDate;
	 /** 
	* @Fields userDisable :  是否禁用标志
 	*/ 
	private String userDisable;
		 /** 
	* @Fields userType :  用户类型
 	*/ 
	private BigDecimal userType;
		 /** 
	* @Fields uniqueid :  统一认证唯一标识
 	*/ 
	private String uniqueid;
	 /** 
	* @Fields passwordChange :  口令更改时间
 	*/ 
	private Date passwordChange;
	 /** 
	* @Fields clientIp :  客户端IP地址
 	*/ 
	private String clientIp;
	 /** 
	* @Fields latestLoginTime :  上次登录时间
 	*/ 
	private Date latestLoginTime;
	 /** 
	* @Fields partyRole :  Party 角色
 	*/ 
	private String partyRole;
	 /** 
	* @Fields userName :  用户名称
 	*/ 
	private String userName;
	 /** 
	* @Fields invalidLogin :  登录失败次数
 	*/ 
	private BigDecimal invalidLogin;
		 /** 
	* @Fields createDate :  创建时间
 	*/ 
	private Date createDate;
		 /** 
	* @Fields changePwdCause :  口令修改原因编码
 	*/ 
	private BigDecimal changePwdCause;
	 /** 
	* @Fields navigationId :  页面快捷ID
 	*/ 
	private String navigationId;
	 /** 
	* @Fields unitCode :  统一认证组织机构代码
 	*/ 
	private String unitCode;
		
	 public void setDeptId(BigDecimal deptId) {
		this.deptId = deptId;
	}
	
	public BigDecimal getDeptId() {
		return deptId;
	}
	 public void setUnitName(String unitName) {
		this.unitName = unitName;
	}
	
	public String getUnitName() {
		return unitName;
	}
	 public void setRealName(String realName) {
		this.realName = realName;
	}
	
	public String getRealName() {
		return realName;
	}
	 public void setIdType(String idType) {
		this.idType = idType;
	}
	
	public String getIdType() {
		return idType;
	}
	 public void setNeedChangePass(String needChangePass) {
		this.needChangePass = needChangePass;
	}
	
	public String getNeedChangePass() {
		return needChangePass;
	}
	 public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public String getPhone() {
		return phone;
	}
	 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
		 public void setShortNum(BigDecimal shortNum) {
		this.shortNum = shortNum;
	}
	
	public BigDecimal getShortNum() {
		return shortNum;
	}
	 public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	
	public String getIdCard() {
		return idCard;
	}
		 public void setEmail(String email) {
		this.email = email;
	}
	
	public String getEmail() {
		return email;
	}
	 public void setPassword(String password) {
		this.password = password;
	}
	
	public String getPassword() {
		return password;
	}
	 public void setOrganId(BigDecimal organId) {
		this.organId = organId;
	}
	
	public BigDecimal getOrganId() {
		return organId;
	}
	 public void setUserId(BigDecimal userId) {
		this.userId = userId;
	}
	
	public BigDecimal getUserId() {
		return userId;
	}
	 public void setDisableDate(Date disableDate) {
		this.disableDate = disableDate;
	}
	
	public Date getDisableDate() {
		return disableDate;
	}
	 public void setUserDisable(String userDisable) {
		this.userDisable = userDisable;
	}
	
	public String getUserDisable() {
		return userDisable;
	}
		 public void setUserType(BigDecimal userType) {
		this.userType = userType;
	}
	
	public BigDecimal getUserType() {
		return userType;
	}
		 public void setUniqueid(String uniqueid) {
		this.uniqueid = uniqueid;
	}
	
	public String getUniqueid() {
		return uniqueid;
	}
	 public void setPasswordChange(Date passwordChange) {
		this.passwordChange = passwordChange;
	}
	
	public Date getPasswordChange() {
		return passwordChange;
	}
	 public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}
	
	public String getClientIp() {
		return clientIp;
	}
	 public void setLatestLoginTime(Date latestLoginTime) {
		this.latestLoginTime = latestLoginTime;
	}
	
	public Date getLatestLoginTime() {
		return latestLoginTime;
	}
	 public void setPartyRole(String partyRole) {
		this.partyRole = partyRole;
	}
	
	public String getPartyRole() {
		return partyRole;
	}
	 public void setUserName(String userName) {
		this.userName = userName;
	}
	
	public String getUserName() {
		return userName;
	}
	 public void setInvalidLogin(BigDecimal invalidLogin) {
		this.invalidLogin = invalidLogin;
	}
	
	public BigDecimal getInvalidLogin() {
		return invalidLogin;
	}
		 public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	public Date getCreateDate() {
		return createDate;
	}
		 public void setChangePwdCause(BigDecimal changePwdCause) {
		this.changePwdCause = changePwdCause;
	}
	
	public BigDecimal getChangePwdCause() {
		return changePwdCause;
	}
	 public void setNavigationId(String navigationId) {
		this.navigationId = navigationId;
	}
	
	public String getNavigationId() {
		return navigationId;
	}
	 public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}
	
	public String getUnitCode() {
		return unitCode;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "UserBO [" +
				"deptId="+deptId+","+
"unitName="+unitName+","+
"realName="+realName+","+
"idType="+idType+","+
"needChangePass="+needChangePass+","+
"phone="+phone+","+
"organCode="+organCode+","+
"shortNum="+shortNum+","+
"idCard="+idCard+","+
"email="+email+","+
"password="+password+","+
"organId="+organId+","+
"userId="+userId+","+
"disableDate="+disableDate+","+
"userDisable="+userDisable+","+
"userType="+userType+","+
"uniqueid="+uniqueid+","+
"passwordChange="+passwordChange+","+
"clientIp="+clientIp+","+
"latestLoginTime="+latestLoginTime+","+
"partyRole="+partyRole+","+
"userName="+userName+","+
"invalidLogin="+invalidLogin+","+
"createDate="+createDate+","+
"changePwdCause="+changePwdCause+","+
"navigationId="+navigationId+","+
"unitCode="+unitCode+"]";
    }
}
