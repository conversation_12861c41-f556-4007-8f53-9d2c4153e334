package com.nci.tunan.qry.interfaces.model.uw.bo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description AskforinfoBO对象
 * <AUTHOR>
 * @date 2014-11-25 19:40:59
 */
public class AskforinfoBO extends BaseBO {
	/**
	 * @Fields uwId : null
	 */
	private BigDecimal uwId;
	/**
	 * @Fields customerName : null
	 */
	private String customerName;
	/**
	 * @Fields docListId : null
	 */
	private BigDecimal docListId;
	/**
	 * @Fields customerId : null
	 */
	private BigDecimal customerId;
	/**
	 * @Fields askforinfoId : null
	 */
	private BigDecimal askforinfoId;
	/**
	 * @Fields roleType : null
	 */
	private BigDecimal roleType;
	/**
	 * @Fields policyId : null
	 */
	private BigDecimal policyId;
	/**
	 * @Fields customerSex : null
	 */
	private BigDecimal customerGender;
	/**
	 * @Fields customerBirth : null
	 */
	private Date customerBirth;
	private String applyCode;
	
	private String policyCode;

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public void setUwId(BigDecimal uwId) {
		this.uwId = uwId;
	}

	public BigDecimal getUwId() {
		return uwId;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setDocListId(BigDecimal docListId) {
		this.docListId = docListId;
	}

	public BigDecimal getDocListId() {
		return docListId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setAskforinfoId(BigDecimal askforinfoId) {
		this.askforinfoId = askforinfoId;
	}

	public BigDecimal getAskforinfoId() {
		return askforinfoId;
	}

	public void setRoleType(BigDecimal roleType) {
		this.roleType = roleType;
	}

	public BigDecimal getRoleType() {
		return roleType;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public BigDecimal getCustomerGender() {
		return customerGender;
	}

	public void setCustomerGender(BigDecimal customerGender) {
		this.customerGender = customerGender;
	}

	public void setCustomerBirth(Date customerBirth) {
		this.customerBirth = customerBirth;
	}

	public Date getCustomerBirth() {
		return customerBirth;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "AskforinfoBO [" + "uwId=" + uwId + "," + "customerName="
				+ customerName + "," + "docListId=" + docListId + ","
				+ "customerId=" + customerId + "," + "askforinfoId="
				+ askforinfoId + "," + "roleType=" + roleType + ","
				+ "policyId=" + policyId + "," + "customerSex="
				+ customerGender + "," + "customerBirth=" + customerBirth + "]";
	}
}
