package com.nci.tunan.qry.interfaces.model.nb.bo;

import java.math.BigDecimal;
import java.util.List;


import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * 
 * 类说明: 录入流转配置操作对象
 * 
 * <AUTHOR>
 * @version 1.0 2014-11-21
 * @see fangxiao_wb 2014-11-21 创建
 * @see fangxiao_wb 2014-11-21 修改
 * @since CustomerSurveyOperBO1.0
 */
@XStreamAlias("CustomerSurveyOper")
public class CustomerSurveyOperBO {
	@XStreamAlias("CustomerSurvey")
	private QuestionaireCustomerBO questionaireCustomerBO;
	private QuestionaireAndAnswerBO questionaireAndAnswerBO;
	
	private List<QuestionaireCustomerParamBO> questionaireCustomerParamBOList;
	private List<QuestionaireParamAndAnswerBO> questionaireParamAndAnswerBOList;
	
	
	@XStreamAlias("CustomerSurvey")
	private CustomerSurveyBO customerSurveyBO;
	private CustomerSurveyAndTemplateBO customerSurveyAndTemplateBO;
	@XStreamAlias("CustomerSurveyDetailList")
	private List<CustomerSurveyDetailBO> customerSurveyDetailBOList;
	private List<CustomerSurveyAndQuestionBO> customerSurveyAndQuestionBOList;
		
	private BigDecimal policyId;
	
	private String agentCode;
	
	private BigDecimal customerId; 
	
	private String applyCode;
	private String policyCode;



	public QuestionaireCustomerBO getQuestionaireCustomerBO() {
		return questionaireCustomerBO;
	}

	public void setQuestionaireCustomerBO(
			QuestionaireCustomerBO questionaireCustomerBO) {
		this.questionaireCustomerBO = questionaireCustomerBO;
	}

	public QuestionaireAndAnswerBO getQuestionaireAndAnswerBO() {
		return questionaireAndAnswerBO;
	}

	public void setQuestionaireAndAnswerBO(
			QuestionaireAndAnswerBO questionaireAndAnswerBO) {
		this.questionaireAndAnswerBO = questionaireAndAnswerBO;
	}

	public List<QuestionaireCustomerParamBO> getQuestionaireCustomerParamBOList() {
		return questionaireCustomerParamBOList;
	}

	public void setQuestionaireCustomerParamBOList(
			List<QuestionaireCustomerParamBO> questionaireCustomerParamBOList) {
		this.questionaireCustomerParamBOList = questionaireCustomerParamBOList;
	}

	public List<QuestionaireParamAndAnswerBO> getQuestionaireParamAndAnswerBOList() {
		return questionaireParamAndAnswerBOList;
	}

	public void setQuestionaireParamAndAnswerBOList(
			List<QuestionaireParamAndAnswerBO> questionaireParamAndAnswerBOList) {
		this.questionaireParamAndAnswerBOList = questionaireParamAndAnswerBOList;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
		if(questionaireCustomerBO != null){
			questionaireCustomerBO.setPolicyId(policyId);
		}
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
		if(questionaireCustomerBO != null){
			questionaireCustomerBO.setAgentCode(agentCode);
		}
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
		if(questionaireCustomerBO != null){
			questionaireCustomerBO.setCustomerId(customerId);
		}
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
		if(questionaireCustomerBO != null){
			questionaireCustomerBO.setApplyCode(applyCode);
		}
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
		if(questionaireCustomerBO != null){
			questionaireCustomerBO.setPolicyCode(policyCode);
		}
	}

	public CustomerSurveyBO getCustomerSurveyBO() {
		return customerSurveyBO;
	}

	public void setCustomerSurveyBO(CustomerSurveyBO customerSurveyBO) {
		this.customerSurveyBO = customerSurveyBO;
	}

	public CustomerSurveyAndTemplateBO getCustomerSurveyAndTemplateBO() {
		return customerSurveyAndTemplateBO;
	}

	public void setCustomerSurveyAndTemplateBO(
			CustomerSurveyAndTemplateBO customerSurveyAndTemplateBO) {
		this.customerSurveyAndTemplateBO = customerSurveyAndTemplateBO;
	}

	public List<CustomerSurveyDetailBO> getCustomerSurveyDetailBOList() {
		return customerSurveyDetailBOList;
	}

	public void setCustomerSurveyDetailBOList(
			List<CustomerSurveyDetailBO> customerSurveyDetailBOList) {
		this.customerSurveyDetailBOList = customerSurveyDetailBOList;
	}

	public List<CustomerSurveyAndQuestionBO> getCustomerSurveyAndQuestionBOList() {
		return customerSurveyAndQuestionBOList;
	}

	public void setCustomerSurveyAndQuestionBOList(
			List<CustomerSurveyAndQuestionBO> customerSurveyAndQuestionBOList) {
		this.customerSurveyAndQuestionBOList = customerSurveyAndQuestionBOList;
	}

	
	
}
