package com.nci.tunan.qry.interfaces.model.vo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;


/** 
 * @description  反洗钱客户身份识别报表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2020-8-25 上午11:18:35  
*/
public class ClaimAntiMoneyLaunderingVO extends BaseVO {	
	/** 
	* @Fields caseNo :  赔案号
 	*/ 
	private String caseNo;
	/** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
	/** 
	 * @Fields organName :  受理机构
	 */ 
	private String organName;
	/** 
	* @Fields policyCode :  保单号
 	*/ 
	private String policyCode;
	/** 
	* @Fields claimTypeStr :  理赔类型拼接后
 	*/ 
	private String claimTypeStr;
	/** 
	* @Fields actualPay :  赔案最终赔付金额
 	*/ 
	private BigDecimal actualPay;
	/** 
	* @Fields payeeAmount : 领款金额
 	*/ 
	private BigDecimal payeeAmount;
	
	
	
	/** 
	* @Fields insuredName :  被保险人姓名
 	*/ 
	private String insuredName;
	/** 
	* @Fields insuredSex :  被保险人性别
 	*/ 
	private String insuredSex;
	/** 
	* @Fields insuredNationStr :  被保险人国籍
 	*/ 
	private String insuredNationStr;
	/** 
	* @Fields insuredJob :  被保险人职业
 	*/ 
	private String insuredJob;
	/** 
	* @Fields insuredAdress :  被保险人地址
 	*/ 
	private String insuredAdress;
	/** 
	* @Fields insuredPhone :  被保险人电话
 	*/ 
	private String insuredPhone;
	/** 
	* @Fields insuredCertType :  被保险人证件类型
 	*/ 
	private String insuredCertType;
	/** 
	* @Fields insuredCertCode :  被保险人证件号码
 	*/ 
	private String insuredCertCode;
	/** 
	* @Fields insuredCertiStart :  被保险人证件有效起期
 	*/ 
	private String insuredCertiStart;
	/** 
	* @Fields insuredCertiEnd :  被保险人证件有效止期
 	*/ 
	private String insuredCertiEnd;
	
	
	/** 
	* @Fields beneName :  受益人姓名
 	*/ 
	private String beneName;
	/** 
	* @Fields beneSex :  受益人性别
 	*/ 
	private String beneSex;
	/** 
	* @Fields beneNationStr :  受益人国籍
 	*/ 
	private String beneNationStr;
	/** 
	* @Fields beneJob :  受益人职业
 	*/ 
	private String beneJob;
	/** 
	* @Fields beneAdress :  受益人地址
 	*/ 
	private String beneAdress;
	/** 
	* @Fields benePhone :  受益人电话
 	*/ 
	private String benePhone;
	/** 
	* @Fields beneCertType :  受益人证件类型
 	*/ 
	private String beneCertType;
	/** 
	* @Fields beneCertCode :  受益人证件号码
 	*/ 
	private String beneCertCode;
	/** 
	* @Fields beneCertiStart :  受益人证件有效起期
 	*/ 
	private String beneCertiStart;
	/** 
	* @Fields beneCertiEnd :  受益人证件有效止期
 	*/ 
	private String beneCertiEnd;
	
	/** 
	* @Fields payeeName :  领款人姓名
 	*/ 
	private String payeeName;
	/** 
	* @Fields payeeSex :  领款人性别
 	*/ 
	private String payeeSex;
	/** 
	* @Fields payeeNationStr :  领款人国籍
 	*/ 
	private String payeeNationStr;
	/** 
	* @Fields payeeJob :  领款人职业
 	*/ 
	private String payeeJob;
	/** 
	* @Fields payeeAdress :  领款人地址
 	*/ 
	private String payeeAdress;
	/** 
	* @Fields payeePhone :  领款人电话
 	*/ 
	private String payeePhone;
	/** 
	* @Fields payeeCertType :  领款人证件类型
 	*/ 
	private String payeeCertType;
	/** 
	* @Fields payeeCertCode :  领款人证件号码
 	*/ 
	private String payeeCertCode;
	/** 
	* @Fields payeeCertiStart :  领款人证件有效起期
 	*/ 
	private String payeeCertiStart;
	/** 
	* @Fields payeeCertiEnd :  领款人证件有效止期
 	*/ 
	private String payeeCertiEnd;
	
	
	/** 
	* @Fields holderInsuredRelation : 投保人与被保险人关系
	*/ 
	private String holderInsuredRelation;
	/** 
	* @Fields InsuredBeneRelation : 被保险人与受益人关系
	*/ 
	private String InsuredBeneRelation;
	/** 
	* @Fields holderBeneRelation : 投保人与受益人关系
	*/ 
	private String holderBeneRelation;
	/** 
	* @Fields benePayeeRelation : 受益人与领款人关系
	*/ 
	private String benePayeeRelation;
	/** 
	 * @Fields adjustType : 结算项目
	 */ 
	private String adjustType;
	
	public String getAdjustType() {
		return adjustType;
	}

	public void setAdjustType(String adjustType) {
		this.adjustType = adjustType;
	}
	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public BigDecimal getCaseId() {
		return caseId;
	}

	public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getClaimTypeStr() {
		return claimTypeStr;
	}

	public void setClaimTypeStr(String claimTypeStr) {
		this.claimTypeStr = claimTypeStr;
	}

	public BigDecimal getActualPay() {
		return actualPay;
	}

	public void setActualPay(BigDecimal actualPay) {
		this.actualPay = actualPay;
	}

	public BigDecimal getPayeeAmount() {
		return payeeAmount;
	}

	public void setPayeeAmount(BigDecimal payeeAmount) {
		this.payeeAmount = payeeAmount;
	}

	public String getInsuredName() {
		return insuredName;
	}

	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}

	public String getInsuredSex() {
		return insuredSex;
	}

	public void setInsuredSex(String insuredSex) {
		this.insuredSex = insuredSex;
	}

	public String getInsuredNationStr() {
		return insuredNationStr;
	}

	public void setInsuredNationStr(String insuredNationStr) {
		this.insuredNationStr = insuredNationStr;
	}

	public String getInsuredJob() {
		return insuredJob;
	}

	public void setInsuredJob(String insuredJob) {
		this.insuredJob = insuredJob;
	}

	public String getInsuredAdress() {
		return insuredAdress;
	}

	public void setInsuredAdress(String insuredAdress) {
		this.insuredAdress = insuredAdress;
	}

	public String getInsuredPhone() {
		return insuredPhone;
	}

	public void setInsuredPhone(String insuredPhone) {
		this.insuredPhone = insuredPhone;
	}

	public String getInsuredCertType() {
		return insuredCertType;
	}

	public void setInsuredCertType(String insuredCertType) {
		this.insuredCertType = insuredCertType;
	}

	public String getInsuredCertCode() {
		return insuredCertCode;
	}

	public void setInsuredCertCode(String insuredCertCode) {
		this.insuredCertCode = insuredCertCode;
	}


	public String getBeneName() {
		return beneName;
	}

	public void setBeneName(String beneName) {
		this.beneName = beneName;
	}

	public String getBeneSex() {
		return beneSex;
	}

	public void setBeneSex(String beneSex) {
		this.beneSex = beneSex;
	}

	public String getBeneNationStr() {
		return beneNationStr;
	}

	public void setBeneNationStr(String beneNationStr) {
		this.beneNationStr = beneNationStr;
	}

	public String getBeneJob() {
		return beneJob;
	}

	public void setBeneJob(String beneJob) {
		this.beneJob = beneJob;
	}

	public String getBeneAdress() {
		return beneAdress;
	}

	public void setBeneAdress(String beneAdress) {
		this.beneAdress = beneAdress;
	}

	public String getBenePhone() {
		return benePhone;
	}

	public void setBenePhone(String benePhone) {
		this.benePhone = benePhone;
	}

	public String getBeneCertType() {
		return beneCertType;
	}

	public void setBeneCertType(String beneCertType) {
		this.beneCertType = beneCertType;
	}

	public String getBeneCertCode() {
		return beneCertCode;
	}

	public void setBeneCertCode(String beneCertCode) {
		this.beneCertCode = beneCertCode;
	}


	public String getPayeeName() {
		return payeeName;
	}

	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}

	public String getPayeeSex() {
		return payeeSex;
	}

	public void setPayeeSex(String payeeSex) {
		this.payeeSex = payeeSex;
	}

	public String getPayeeNationStr() {
		return payeeNationStr;
	}

	public void setPayeeNationStr(String payeeNationStr) {
		this.payeeNationStr = payeeNationStr;
	}

	public String getPayeeJob() {
		return payeeJob;
	}

	public void setPayeeJob(String payeeJob) {
		this.payeeJob = payeeJob;
	}

	public String getPayeeAdress() {
		return payeeAdress;
	}

	public void setPayeeAdress(String payeeAdress) {
		this.payeeAdress = payeeAdress;
	}

	public String getPayeePhone() {
		return payeePhone;
	}

	public void setPayeePhone(String payeePhone) {
		this.payeePhone = payeePhone;
	}

	public String getPayeeCertType() {
		return payeeCertType;
	}

	public void setPayeeCertType(String payeeCertType) {
		this.payeeCertType = payeeCertType;
	}

	public String getPayeeCertCode() {
		return payeeCertCode;
	}

	public void setPayeeCertCode(String payeeCertCode) {
		this.payeeCertCode = payeeCertCode;
	}


	public String getHolderInsuredRelation() {
		return holderInsuredRelation;
	}

	public void setHolderInsuredRelation(String holderInsuredRelation) {
		this.holderInsuredRelation = holderInsuredRelation;
	}

	public String getInsuredBeneRelation() {
		return InsuredBeneRelation;
	}

	public void setInsuredBeneRelation(String insuredBeneRelation) {
		InsuredBeneRelation = insuredBeneRelation;
	}

	public String getHolderBeneRelation() {
		return holderBeneRelation;
	}

	public void setHolderBeneRelation(String holderBeneRelation) {
		this.holderBeneRelation = holderBeneRelation;
	}

	public String getBenePayeeRelation() {
		return benePayeeRelation;
	}

	public void setBenePayeeRelation(String benePayeeRelation) {
		this.benePayeeRelation = benePayeeRelation;
	}
	

	public String getInsuredCertiStart() {
		return insuredCertiStart;
	}

	public void setInsuredCertiStart(String insuredCertiStart) {
		this.insuredCertiStart = insuredCertiStart;
	}

	public String getInsuredCertiEnd() {
		return insuredCertiEnd;
	}

	public void setInsuredCertiEnd(String insuredCertiEnd) {
		this.insuredCertiEnd = insuredCertiEnd;
	}

	public String getBeneCertiStart() {
		return beneCertiStart;
	}

	public void setBeneCertiStart(String beneCertiStart) {
		this.beneCertiStart = beneCertiStart;
	}

	public String getBeneCertiEnd() {
		return beneCertiEnd;
	}

	public void setBeneCertiEnd(String beneCertiEnd) {
		this.beneCertiEnd = beneCertiEnd;
	}

	public String getPayeeCertiStart() {
		return payeeCertiStart;
	}

	public void setPayeeCertiStart(String payeeCertiStart) {
		this.payeeCertiStart = payeeCertiStart;
	}

	public String getPayeeCertiEnd() {
		return payeeCertiEnd;
	}

	public void setPayeeCertiEnd(String payeeCertiEnd) {
		this.payeeCertiEnd = payeeCertiEnd;
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String toString() {
		return "ClaimAntiMoneyLaunderingVO [caseNo=" + caseNo + ", caseId="
				+ caseId + ", organName=" + organName + ", policyCode="
				+ policyCode + ", claimTypeStr=" + claimTypeStr
				+ ", actualPay=" + actualPay + ", payeeAmount=" + payeeAmount
				+ ", insuredName=" + insuredName + ", insuredSex=" + insuredSex
				+ ", insuredNationStr=" + insuredNationStr + ", insuredJob="
				+ insuredJob + ", insuredAdress=" + insuredAdress
				+ ", insuredPhone=" + insuredPhone + ", insuredCertType="
				+ insuredCertType + ", insuredCertCode=" + insuredCertCode
				+ ", insuredCertiStart=" + insuredCertiStart
				+ ", insuredCertiEnd=" + insuredCertiEnd + ", beneName="
				+ beneName + ", beneSex=" + beneSex + ", beneNationStr="
				+ beneNationStr + ", beneJob=" + beneJob + ", beneAdress="
				+ beneAdress + ", benePhone=" + benePhone + ", beneCertType="
				+ beneCertType + ", beneCertCode=" + beneCertCode
				+ ", beneCertiStart=" + beneCertiStart + ", beneCertiEnd="
				+ beneCertiEnd + ", payeeName=" + payeeName + ", payeeSex="
				+ payeeSex + ", payeeNationStr=" + payeeNationStr
				+ ", payeeJob=" + payeeJob + ", payeeAdress=" + payeeAdress
				+ ", payeePhone=" + payeePhone + ", payeeCertType="
				+ payeeCertType + ", payeeCertCode=" + payeeCertCode
				+ ", payeeCertiStart=" + payeeCertiStart + ", payeeCertiEnd="
				+ payeeCertiEnd + ", holderInsuredRelation="
				+ holderInsuredRelation + ", InsuredBeneRelation="
				+ InsuredBeneRelation + ", holderBeneRelation="
				+ holderBeneRelation + ", benePayeeRelation="
				+ benePayeeRelation + "]";
	}
	
  
}
