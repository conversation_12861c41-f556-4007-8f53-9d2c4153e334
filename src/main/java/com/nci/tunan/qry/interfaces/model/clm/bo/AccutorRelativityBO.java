package com.nci.tunan.qry.interfaces.model.clm.bo;

import java.math.BigDecimal;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * 累加器相关性
 * @description 
 * <AUTHOR> <EMAIL>
 * @date 2015-12-28 下午5:29:33
 */
public class AccutorRelativityBO extends BaseBO {
	
	/**
	 * 相关性1
	 */
	private BigDecimal relativity1;
	private String relv1Value;
	
	/**
	 * 相关性2
	 */
    private BigDecimal relativity2;
    private String relv2Value;
    
    /**
	 * 相关性3
	 */
    private BigDecimal relativity3;
    private String relv3Value;
    
    /**
	 * 相关性4
	 */
    private BigDecimal relativity4;
    private String relv4Value;
    
    /**
	 * 相关性5
	 */
    private BigDecimal relativity5;
    private String relv5Value;
    
    /**
	 * 相关性6
	 */
    private BigDecimal relativity6;
    private String relv6Value;
    
	public BigDecimal getRelativity1() {
		return relativity1;
	}

	public void setRelativity1(BigDecimal relativity1) {
		this.relativity1 = relativity1;
	}

	public BigDecimal getRelativity2() {
		return relativity2;
	}

	public void setRelativity2(BigDecimal relativity2) {
		this.relativity2 = relativity2;
	}

	public BigDecimal getRelativity3() {
		return relativity3;
	}

	public void setRelativity3(BigDecimal relativity3) {
		this.relativity3 = relativity3;
	}

	public BigDecimal getRelativity4() {
		return relativity4;
	}

	public void setRelativity4(BigDecimal relativity4) {
		this.relativity4 = relativity4;
	}

	public BigDecimal getRelativity5() {
		return relativity5;
	}

	public void setRelativity5(BigDecimal relativity5) {
		this.relativity5 = relativity5;
	}

	public BigDecimal getRelativity6() {
		return relativity6;
	}

	public void setRelativity6(BigDecimal relativity6) {
		this.relativity6 = relativity6;
	}

	public String getRelv1Value() {
		return relv1Value;
	}

	public void setRelv1Value(String relv1Value) {
		this.relv1Value = relv1Value;
	}

	public String getRelv2Value() {
		return relv2Value;
	}

	public void setRelv2Value(String relv2Value) {
		this.relv2Value = relv2Value;
	}

	public String getRelv3Value() {
		return relv3Value;
	}

	public void setRelv3Value(String relv3Value) {
		this.relv3Value = relv3Value;
	}

	public String getRelv4Value() {
		return relv4Value;
	}

	public void setRelv4Value(String relv4Value) {
		this.relv4Value = relv4Value;
	}

	public String getRelv5Value() {
		return relv5Value;
	}

	public void setRelv5Value(String relv5Value) {
		this.relv5Value = relv5Value;
	}

	public String getRelv6Value() {
		return relv6Value;
	}

	public void setRelv6Value(String relv6Value) {
		this.relv6Value = relv6Value;
	}
	
	@Override
	public String getBizId() {
		return null;
	}

}
