package com.nci.tunan.qry.interfaces.model.vo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;

/** 
 * @description CsApplicationVO对象
 * <AUTHOR> 
 * @date 2016-03-05 11:39:29  
 */
/**20180924提交**/
public class QryCsApplicationVO extends BaseVO {	
	 /** 
	* @Fields applicantType :  申请人类型
 	*/ 
	private String applicantType;
	 /** 
	* @Fields agentCertiType :  代办人证件类型
 	*/ 
	private String agentCertiType;
	 /** 
	* @Fields billSendType :  单证发放方式（纸质单证）
 	*/ 
	private String billSendType;
	 /** 
	* @Fields tryCalcNo :  试算编号
 	*/ 
	private String tryCalcNo;
	 /** 
	* @Fields applyTime :  保全申请时间
 	*/ 
	private Date applyTime;
	 /** 
	* @Fields customerId :  申请人客户号
 	*/ 
	private BigDecimal customerId;
	 /** 
	* @Fields finishTime :  结束时间
 	*/ 
	private Date finishTime;
	 /** 
	* @Fields cancelCause :  保全撤销原因
 	*/ 
	private String cancelCause;
	 /** 
	* @Fields applyCode :  保全申请号
 	*/ 
	private String applyCode;
		 /** 
	* @Fields sourceType :  受理来源：渠道
 	*/ 
	private String sourceType;
	 /** 
	* @Fields organCode :  操作人员机构号
 	*/ 
	private String organCode;
		 /** 
	* @Fields changeId :  保全申请ID
 	*/ 
	private BigDecimal changeId;
	 /** 
	* @Fields agentLevel :  代办人绩优等级
 	*/ 
	private String agentLevel;
	 /** 
	* @Fields serviceType :  保全申请方式:亲办,代办
 	*/ 
	private String serviceType;
	 /** 
	* @Fields applyName :  申请人姓名
 	*/ 
	private String applyName;
	 /** 
	* @Fields toUwDate :  提交核保日
 	*/ 
	private Date toUwDate;
		 /** 
	* @Fields agentName :  代办人姓名
 	*/ 
	private String agentName;
	 /** 
	* @Fields agentCertiCode :  代办人证件号码
 	*/ 
	private String agentCertiCode;
		 /** 
	* @Fields cancelNote :  保全撤销说明
 	*/ 
	private String cancelNote;
	 /** 
	* @Fields eventCode :  汇缴事件号
 	*/ 
	private String eventCode;
	 /** 
	* @Fields appStatus :  保全变更状态
 	*/ 
	private String appStatus;
	 /** 
	* @Fields cancelId :  保全撤销操作人
 	*/ 
	private BigDecimal cancelId;
	 /** 
	* @Fields nofillFlag :  是否免填单
 	*/ 
	private BigDecimal nofillFlag;
	/**
	 * 是否预受理
	 */
	private BigDecimal preFlag; 
		 /** 
	* @Fields agentTel :  代办人联系电话
 	*/ 
	private String agentTel;
		 /** 
	* @Fields agentCode :  业务员代码
 	*/ 
	private String agentCode;
	 /** 
	* @Fields toApproveDate :  提交复核日
 	*/ 
	private Date toApproveDate;
	/** 
	  * @Fields nofillFlag :  是否电子签名
	 */ 
	private BigDecimal isElecSign ;
	/**
	 * 具体紧急原因
	 */
	private String urgentDetail;
	
	/**
	 * 人脸识别标识
	 */
	private String faceFlag;
	/**
	 * 空中签名
	 */
	private String skySign;
	/**
	 * 简项标识
	 */
	private String identityFlag;
	/**
	 * 是否需要验真
	 */
	private String isIdentityCheck;
	
	
	 public String getFaceFlag() {
		return faceFlag;
	}

	public void setFaceFlag(String faceFlag) {
		this.faceFlag = faceFlag;
	}

	public String getSkySign() {
		return skySign;
	}

	public void setSkySign(String skySign) {
		this.skySign = skySign;
	}

	public String getIdentityFlag() {
		return identityFlag;
	}

	public void setIdentityFlag(String identityFlag) {
		this.identityFlag = identityFlag;
	}

	public String getIsIdentityCheck() {
		return isIdentityCheck;
	}

	public void setIsIdentityCheck(String isIdentityCheck) {
		this.isIdentityCheck = isIdentityCheck;
	}

	public String getUrgentDetail() {
		return urgentDetail;
	}

	public void setUrgentDetail(String urgentDetail) {
		this.urgentDetail = urgentDetail;
	}

	public BigDecimal getIsElecSign() {
		return isElecSign;
	}

	public void setIsElecSign(BigDecimal isElecSign) {
		this.isElecSign = isElecSign;
	}

	public BigDecimal getPreFlag() {
		return preFlag;
	}

	public void setPreFlag(BigDecimal preFlag) {
		this.preFlag = preFlag;
	}

	public void setApplicantType(String applicantType) {
		this.applicantType = applicantType;
	}
	
	public String getApplicantType() {
		return applicantType;
	}
	 public void setAgentCertiType(String agentCertiType) {
		this.agentCertiType = agentCertiType;
	}
	
	public String getAgentCertiType() {
		return agentCertiType;
	}
	 public void setBillSendType(String billSendType) {
		this.billSendType = billSendType;
	}
	
	public String getBillSendType() {
		return billSendType;
	}
	 public void setTryCalcNo(String tryCalcNo) {
		this.tryCalcNo = tryCalcNo;
	}
	
	public String getTryCalcNo() {
		return tryCalcNo;
	}
	 public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}
	
	public Date getApplyTime() {
		return applyTime;
	}
	 public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}
	
	public BigDecimal getCustomerId() {
		return customerId;
	}
	 public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}
	
	public Date getFinishTime() {
		return finishTime;
	}
	 public void setCancelCause(String cancelCause) {
		this.cancelCause = cancelCause;
	}
	
	public String getCancelCause() {
		return cancelCause;
	}
	 public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	
	public String getApplyCode() {
		return applyCode;
	}
		 public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}
	
	public String getSourceType() {
		return sourceType;
	}
	 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
		 public void setChangeId(BigDecimal changeId) {
		this.changeId = changeId;
	}
	
	public BigDecimal getChangeId() {
		return changeId;
	}
	 public void setAgentLevel(String agentLevel) {
		this.agentLevel = agentLevel;
	}
	
	public String getAgentLevel() {
		return agentLevel;
	}
	 public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}
	
	public String getServiceType() {
		return serviceType;
	}
	 public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	
	public String getApplyName() {
		return applyName;
	}
	 public void setToUwDate(Date toUwDate) {
		this.toUwDate = toUwDate;
	}
	
	public Date getToUwDate() {
		return toUwDate;
	}
		 public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	
	public String getAgentName() {
		return agentName;
	}
	 public void setAgentCertiCode(String agentCertiCode) {
		this.agentCertiCode = agentCertiCode;
	}
	
	public String getAgentCertiCode() {
		return agentCertiCode;
	}
		 public void setCancelNote(String cancelNote) {
		this.cancelNote = cancelNote;
	}
	
	public String getCancelNote() {
		return cancelNote;
	}
	 public void setEventCode(String eventCode) {
		this.eventCode = eventCode;
	}
	
	public String getEventCode() {
		return eventCode;
	}
	 public void setAppStatus(String appStatus) {
		this.appStatus = appStatus;
	}
	
	public String getAppStatus() {
		return appStatus;
	}
	 public void setCancelId(BigDecimal cancelId) {
		this.cancelId = cancelId;
	}
	
	public BigDecimal getCancelId() {
		return cancelId;
	}
	 public void setNofillFlag(BigDecimal nofillFlag) {
		this.nofillFlag = nofillFlag;
	}
	
	public BigDecimal getNofillFlag() {
		return nofillFlag;
	}
		 public void setAgentTel(String agentTel) {
		this.agentTel = agentTel;
	}
	
	public String getAgentTel() {
		return agentTel;
	}
		 public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}
	
	public String getAgentCode() {
		return agentCode;
	}
	 public void setToApproveDate(Date toApproveDate) {
		this.toApproveDate = toApproveDate;
	}
	
	public Date getToApproveDate() {
		return toApproveDate;
	}
		
	@Override
    public String getBizId() {
        return null;
    }

	@Override
	public String toString() {
		return "QryCsApplicationVO [applicantType=" + applicantType
				+ ", agentCertiType=" + agentCertiType + ", billSendType="
				+ billSendType + ", tryCalcNo=" + tryCalcNo + ", applyTime="
				+ applyTime + ", customerId=" + customerId + ", finishTime="
				+ finishTime + ", cancelCause=" + cancelCause + ", applyCode="
				+ applyCode + ", sourceType=" + sourceType + ", organCode="
				+ organCode + ", changeId=" + changeId + ", agentLevel="
				+ agentLevel + ", serviceType=" + serviceType + ", applyName="
				+ applyName + ", toUwDate=" + toUwDate + ", agentName="
				+ agentName + ", agentCertiCode=" + agentCertiCode
				+ ", cancelNote=" + cancelNote + ", eventCode=" + eventCode
				+ ", appStatus=" + appStatus + ", cancelId=" + cancelId
				+ ", nofillFlag=" + nofillFlag + ", preFlag=" + preFlag
				+ ", agentTel=" + agentTel + ", agentCode=" + agentCode
				+ ", toApproveDate=" + toApproveDate + ", isElecSign="
				+ isElecSign + ", urgentDetail=" + urgentDetail + ", faceFlag="
				+ faceFlag + ", skySign=" + skySign + ", identityFlag="
				+ identityFlag + ", isIdentityCheck=" + isIdentityCheck + "]";
	}

    
}
