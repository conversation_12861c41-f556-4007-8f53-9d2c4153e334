package com.nci.tunan.qry.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class CsCustomerPasswordBO extends BaseBO{
    private BigDecimal listId;
    private BigDecimal customerId;
    private String setCause;
    private Date setTime;
    private String password;
    private String status;
    private Date lapseTime;
    private String lapseCause;
    private BigDecimal insertBy;
    private Date insertTime;
    private Date insertTimestamp;
    private BigDecimal updateBy;
    private Date updateTime;
    private Date updateTimestamp;
    private BigDecimal changeId;
    private String acceptCode;
    private String policyChgId;
    private String customerName;
    
    public BigDecimal getListId() {
        return listId;
    }

    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public String getSetCause() {
        return setCause;
    }

    public void setSetCause(String setCause) {
        this.setCause = setCause;
    }

    public Date getSetTime() {
        return setTime;
    }

    public void setSetTime(Date setTime) {
        this.setTime = setTime;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getLapseTime() {
        return lapseTime;
    }

    public void setLapseTime(Date lapseTime) {
        this.lapseTime = lapseTime;
    }

    public String getLapseCause() {
        return lapseCause;
    }

    public void setLapseCause(String lapseCause) {
        this.lapseCause = lapseCause;
    }

    public BigDecimal getInsertBy() {
        return insertBy;
    }

    public void setInsertBy(BigDecimal insertBy) {
        this.insertBy = insertBy;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Date getInsertTimestamp() {
        return insertTimestamp;
    }

    public void setInsertTimestamp(Date insertTimestamp) {
        this.insertTimestamp = insertTimestamp;
    }

    public BigDecimal getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(BigDecimal updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }

    public BigDecimal getChangeId() {
        return changeId;
    }

    public void setChangeId(BigDecimal changeId) {
        this.changeId = changeId;
    }

    public String getAcceptCode() {
        return acceptCode;
    }

    public void setAcceptCode(String acceptCode) {
        this.acceptCode = acceptCode;
    }

    public String getPolicyChgId() {
        return policyChgId;
    }

    public void setPolicyChgId(String policyChgId) {
        this.policyChgId = policyChgId;
    }
    

    public String getCustomerName() {
        return customerName;
    }




    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }




    @Override
    public String getBizId() {
        // TODO Auto-generated method stub
        return null;
    }

}
