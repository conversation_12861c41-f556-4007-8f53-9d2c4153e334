package com.nci.tunan.qry.interfaces.model.bo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;

/** 
 * @description IhiClmbroCustomerVO对象
 * <AUTHOR> 
 * @date 2022-04-19 16:31:37  
 */
public class IhiClmbroCustomerBO extends BaseBO {	
	 /** 
	* @Fields customerCertType :  证件类型
 	*/ 
	private String customerCertType;
	 /** 
	* @Fields clmbroId :  意健险理赔风险广播主键
 	*/ 
	private BigDecimal clmbroId;
	 /** 
	* @Fields receiveTime :  数据接收时间
 	*/ 
	private Date receiveTime;
	 /** 
	* @Fields customerName :  客户姓名
 	*/ 
	private String customerName;
	 /** 
	* @Fields labelName :  标签名称
 	*/ 
	private String labelName;
		 /** 
	* @Fields sceneName :  场景名称
 	*/ 
	private String sceneName;
	 /** 
	* @Fields appkey :  公司机构编码
 	*/ 
	private String appkey;
	 /** 
	* @Fields dstCertiCode :  脱敏证件号码
 	*/ 
	private String dstCertiCode;
	 /** 
	* @Fields customerId :  客户号
 	*/ 
	private BigDecimal customerId;
		 /** 
	* @Fields customerCertiCode :  证件号码
 	*/ 
	private String customerCertiCode;
		 /** 
	* @Fields infoSource :  信息来源
 	*/ 
	private String infoSource;
	 /** 
	* @Fields policyCode :  保单号码
 	*/ 
	private String policyCode;
		 /** 
	* @Fields dstName :  脱敏姓名
 	*/ 
	private String dstName;
			 /** 
	* @Fields dstCertType :  脱敏证件类型
 	*/ 
	private String dstCertType;
		
	 public void setCustomerCertType(String customerCertType) {
		this.customerCertType = customerCertType;
	}
	
	public String getCustomerCertType() {
		return customerCertType;
	}
	 public void setClmbroId(BigDecimal clmbroId) {
		this.clmbroId = clmbroId;
	}
	
	public BigDecimal getClmbroId() {
		return clmbroId;
	}
	 public void setReceiveTime(Date receiveTime) {
		this.receiveTime = receiveTime;
	}
	
	public Date getReceiveTime() {
		return receiveTime;
	}
	 public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	
	public String getCustomerName() {
		return customerName;
	}
	 public void setLabelName(String labelName) {
		this.labelName = labelName;
	}
	
	public String getLabelName() {
		return labelName;
	}
		 public void setSceneName(String sceneName) {
		this.sceneName = sceneName;
	}
	
	public String getSceneName() {
		return sceneName;
	}
	 public void setAppkey(String appkey) {
		this.appkey = appkey;
	}
	
	public String getAppkey() {
		return appkey;
	}
	 public void setDstCertiCode(String dstCertiCode) {
		this.dstCertiCode = dstCertiCode;
	}
	
	public String getDstCertiCode() {
		return dstCertiCode;
	}
	 public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}
	
	public BigDecimal getCustomerId() {
		return customerId;
	}
		 public void setCustomerCertiCode(String customerCertiCode) {
		this.customerCertiCode = customerCertiCode;
	}
	
	public String getCustomerCertiCode() {
		return customerCertiCode;
	}
		 public void setInfoSource(String infoSource) {
		this.infoSource = infoSource;
	}
	
	public String getInfoSource() {
		return infoSource;
	}
	 public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getPolicyCode() {
		return policyCode;
	}
		 public void setDstName(String dstName) {
		this.dstName = dstName;
	}
	
	public String getDstName() {
		return dstName;
	}
			 public void setDstCertType(String dstCertType) {
		this.dstCertType = dstCertType;
	}
	
	public String getDstCertType() {
		return dstCertType;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "IhiClmbroCustomerVO [" +
				"customerCertType="+customerCertType+","+
"clmbroId="+clmbroId+","+
"receiveTime="+receiveTime+","+
"customerName="+customerName+","+
"labelName="+labelName+","+
"sceneName="+sceneName+","+
"appkey="+appkey+","+
"dstCertiCode="+dstCertiCode+","+
"customerId="+customerId+","+
"customerCertiCode="+customerCertiCode+","+
"infoSource="+infoSource+","+
"policyCode="+policyCode+","+
"dstName="+dstName+","+
"dstCertType="+dstCertType+"]";
    }
}
