package com.nci.tunan.qry.interfaces.model.clm.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;
/**
 * 理赔合议子表
 * @description 
 * <AUTHOR>
 * @date 2017年9月15日 上午10:02:26
 */
public class ClaimSubDiscussPO extends BasePO {

    /**
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
     */

    private static final long serialVersionUID = 1L;

    /** 属性 --- java类型 --- oracle类型_数据长度_小数位长度_注释信息 */
    // subDiscussStatus --- BigDecimal --- NUMBER_1_0_合议状态;
    // subDiscussBy --- BigDecimal --- NUMBER_10_0_合议人;
    // subDiscussConclusion --- BigDecimal --- NUMBER_1_0_合议结论;
    // discussId --- BigDecimal --- NUMBER_16_0_合议主表ID;
    // subDiscussDate --- Date --- DATE_7_0_合议回复日期;
    // caseNo --- String --- VARCHAR2_20_0_赔案号;
    // subDiscussId --- BigDecimal --- NUMBER_16_0_合议分表ID;
    // subDiscussReply --- String --- VARCHAR2_500_0_合议回复;

    public void setSubDiscussStatus(BigDecimal subDiscussStatus) {
        setBigDecimal("sub_discuss_status", subDiscussStatus);
    }

    public BigDecimal getSubDiscussStatus() {
        return getBigDecimal("sub_discuss_status");
    }

    public void setSubDiscussBy(BigDecimal subDiscussBy) {
        setBigDecimal("sub_discuss_by", subDiscussBy);
    }

    public BigDecimal getSubDiscussBy() {
        return getBigDecimal("sub_discuss_by");
    }

    public void setSubDiscussConclusion(BigDecimal subDiscussConclusion) {
        setBigDecimal("sub_discuss_conclusion", subDiscussConclusion);
    }

    public BigDecimal getSubDiscussConclusion() {
        return getBigDecimal("sub_discuss_conclusion");
    }

    public void setDiscussId(BigDecimal discussId) {
        setBigDecimal("discuss_id", discussId);
    }

    public BigDecimal getDiscussId() {
        return getBigDecimal("discuss_id");
    }

    public void setSubDiscussDate(Date subDiscussDate) {
        setUtilDate("sub_discuss_date", subDiscussDate);
    }

    public Date getSubDiscussDate() {
        return getUtilDate("sub_discuss_date");
    }

    public void setCaseNo(String caseNo) {
        setString("case_no", caseNo);
    }

    public String getCaseNo() {
        return getString("case_no");
    }

    public void setSubDiscussId(BigDecimal subDiscussId) {
        setBigDecimal("sub_discuss_id", subDiscussId);
    }

    public BigDecimal getSubDiscussId() {
        return getBigDecimal("sub_discuss_id");
    }

    public void setSubDiscussReply(String subDiscussReply) {
        setString("sub_discuss_reply", subDiscussReply);
    }

    public String getSubDiscussReply() {
        return getString("sub_discuss_reply");
    }
    public void setRealname(String realname) {
        getData().put("real_name", realname);
    }

    public String getRealname() {
        return (String) getData().get("real_name");
    }
    public void setUserName(String userName) {
        getData().put("user_name", userName);
    }

    public String getUserName() {
        return (String) getData().get("user_name");
    }

    @Override
    public String toString() {
        return "ClaimSubDiscussPO [" + "subDiscussStatus=" + getSubDiscussStatus() + "," + "subDiscussBy="
                + getSubDiscussBy() + "," + "subDiscussConclusion=" + getSubDiscussConclusion() + "," + "discussId="
                + getDiscussId() + "," + "subDiscussDate=" + getSubDiscussDate() + "," + "caseNo=" + getCaseNo() + ","
                + "subDiscussId=" + getSubDiscussId() + "," + "subDiscussReply=" + getSubDiscussReply() + "]";
    }

}
