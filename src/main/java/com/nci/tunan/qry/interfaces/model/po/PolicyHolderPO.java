package com.nci.tunan.qry.interfaces.model.po;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description PolicyHolderPO对象
 * <AUTHOR> 
 * @date 2016-03-22 18:18:12  
 */
public class PolicyHolderPO extends BasePO {
 	/** 属性  ---  java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// addressId  ---  BigDecimal  ---  NUMBER_16_0_null;
			// customerHeight  ---  BigDecimal  ---  NUMBER_3_0_null;
		// customerId  ---  BigDecimal  ---  NUMBER_16_0_null;
		// jobCode  ---  String  ---  CHAR_7_0_null;
			// customerWeight  ---  BigDecimal  ---  NUMBER_3_0_null;
		// applyCode  ---  String  ---  VARCHAR2_20_0_null;
			// policyCode  ---  String  ---  VARCHAR2_20_0_null;
			// jobUnderwrite  ---  String  ---  CHAR_1_0_null;
		// listId  ---  BigDecimal  ---  NUMBER_16_0_null;
				// policyId  ---  BigDecimal  ---  NUMBER_16_0_null;
	
		public void setAddressId(BigDecimal addressId){
		setBigDecimal("address_id", addressId);
	}
	
	public BigDecimal getAddressId(){
		return getBigDecimal("address_id");
	}
			public void setCustomerHeight(BigDecimal customerHeight){
		setBigDecimal("customer_height", customerHeight);
	}
	
	public BigDecimal getCustomerHeight(){
		return getBigDecimal("customer_height");
	}
		public void setCustomerId(BigDecimal customerId){
		setBigDecimal("customer_id", customerId);
	}
	
	public BigDecimal getCustomerId(){
		return getBigDecimal("customer_id");
	}
		public void setJobCode(String jobCode){
		setString("job_code", jobCode);
	}
	
	public String getJobCode(){
		return getString("job_code");
	}
			public void setCustomerWeight(BigDecimal customerWeight){
		setBigDecimal("customer_weight", customerWeight);
	}
	
	public BigDecimal getCustomerWeight(){
		return getBigDecimal("customer_weight");
	}
		public void setApplyCode(String applyCode){
		setString("apply_code", applyCode);
	}
	
	public String getApplyCode(){
		return getString("apply_code");
	}
			public void setPolicyCode(String policyCode){
		setString("policy_code", policyCode);
	}
	
	public String getPolicyCode(){
		return getString("policy_code");
	}
			public void setJobUnderwrite(String jobUnderwrite){
		setString("job_underwrite", jobUnderwrite);
	}
	
	public String getJobUnderwrite(){
		return getString("job_underwrite");
	}
		public void setListId(BigDecimal listId){
		setBigDecimal("list_id", listId);
	}
	
	public BigDecimal getListId(){
		return getBigDecimal("list_id");
	}
				public void setPolicyId(BigDecimal policyId){
		setBigDecimal("policy_id", policyId);
	}
	
	public BigDecimal getPolicyId(){
		return getBigDecimal("policy_id");
	}
		
	public void setBillCardNo(String billcardNo){
		setString("billcard_no", billcardNo);
	}
	
	public String getBillCardNo(){
		return getString("billcard_no");
	}
	@Override
    public String toString() {
        return "PolicyHolderPO [" +
				"addressId="+getAddressId()+","+
"customerHeight="+getCustomerHeight()+","+
"customerId="+getCustomerId()+","+
"jobCode="+getJobCode()+","+
"customerWeight="+getCustomerWeight()+","+
"applyCode="+getApplyCode()+","+
"policyCode="+getPolicyCode()+","+
"jobUnderwrite="+getJobUnderwrite()+","+
"listId="+getListId()+","+
"policyId="+getPolicyId()+"]";
    }	
 }
