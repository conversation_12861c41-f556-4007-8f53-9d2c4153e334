package com.nci.tunan.qry.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class QryAccountBO extends BaseBO {

    /**
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
     */

    private static final long serialVersionUID = 1L;
    private BigDecimal accountId;
    private Date balanceDate;
    private String policyCode;
    private BigDecimal busiItemId;
    private BigDecimal itemId;
    // 险种代码
    private String busiProdCode;
    // 险种名称
    private String busiProdName;
    // 责任组代码
    private BigDecimal productCode;
    // 责任组名称
    private String productName;
    // 责任ID
    private BigDecimal liabId;
    //
    private String liabName;
    // 账户成立日
    private Date accountDate;
    // 本金利息和
    private BigDecimal interestCapital;
    // 上一结算日期
    private Date countDate;
    // 账户价值
    private BigDecimal accountValue;
    // 单位数
    private BigDecimal unitNumber;
    // 单位价值
    private BigDecimal unitPrice;
    // 变更类型
    private BigDecimal transType;
    // 变更日期
    private Date dealTime;
    // 交易金额
    private BigDecimal transAmount;
    // 交易利率
    private BigDecimal transProportion;
    // 交易利息
    private BigDecimal transInterest;

    // 账户状态
    private BigDecimal policyAccountStatus;
    // 本金
    private BigDecimal capitalBalance;
    // 账户类型
    private BigDecimal accountType;
    // 结算利息和
    private BigDecimal interestBalance;
    // 结算利率
    private BigDecimal interestRate;
    // 清偿标示
    private BigDecimal regularRepay;
    // 交易编号
    private String transCode;
    // 交易日期
    private Date transTime;
    // 险种下次缴费日期
    private Date payDueDate;
  //自垫保费总额
    private BigDecimal  countBalance;//COUNT_BALANCE, COUNT(*) AS COUNT_PERIODS
    //自垫保费期数
    private BigDecimal countPeriods;
    
    private BigDecimal policyId;
    
    //万能投连账户类型
    private BigDecimal investAccountType;
    /**
     * 注销原因
     */
    private String closeReason;
    /**
     * 死亡日期
     */
    private String deathDate;    
    /**
     * 建立日期
     */
    private Date createDate;
    /**
     * 投资组合名称
     */
    private String fundName;
    /**
     * 保单投资连结主键id
     */
    private BigDecimal listId;
    
    /**
     * 险种代码简称
     */
    private String busiAbbrName;
    
	private Date pricingDate;
	
	
    public Date getPricingDate() {
		return pricingDate;
	}

	public void setPricingDate(Date pricingDate) {
		this.pricingDate = pricingDate;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public BigDecimal getListId() {
		return listId;
	}

	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}

	public String getCloseReason() {
		return closeReason;
	}

	public void setCloseReason(String closeReason) {
		this.closeReason = closeReason;
	}

	public String getDeathDate() {
		return deathDate;
	}

	public void setDeathDate(String deathDate) {
		this.deathDate = deathDate;
	}

	public BigDecimal getPolicyId() {
        return policyId;
    }

    public void setPolicyId(BigDecimal policyId) {
        this.policyId = policyId;
    }
    
    public BigDecimal getCountBalance() {
        return countBalance;
    }

    public void setCountBalance(BigDecimal countBalance) {
        this.countBalance = countBalance;
    }

    public BigDecimal getCountPeriods() {
        return countPeriods;
    }

    public void setCountPeriods(BigDecimal countPeriods) {
        this.countPeriods = countPeriods;
    }

    public Date getPayDueDate() {
        return payDueDate;
    }

    public void setPayDueDate(Date payDueDate) {
        this.payDueDate = payDueDate;
    }

    public BigDecimal getRegularRepay() {
        return regularRepay;
    }

    public void setRegularRepay(BigDecimal regularRepay) {
        this.regularRepay = regularRepay;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public Date getTransTime() {
        return transTime;
    }

    public void setTransTime(Date transTime) {
        this.transTime = transTime;
    }

    public BigDecimal getInterestRate() {
        return interestRate;
    }

    public void setInterestRate(BigDecimal interestRate) {
        this.interestRate = interestRate;
    }

    public BigDecimal getAccountType() {
        return accountType;
    }

    public void setAccountType(BigDecimal accountType) {
        this.accountType = accountType;
    }

    public BigDecimal getInterestBalance() {
        return interestBalance;
    }

    public void setInterestBalance(BigDecimal interestBalance) {
        this.interestBalance = interestBalance;
    }

    public BigDecimal getPolicyAccountStatus() {
        return policyAccountStatus;
    }

    public void setPolicyAccountStatus(BigDecimal policyAccountStatus) {
        this.policyAccountStatus = policyAccountStatus;
    }

    public BigDecimal getCapitalBalance() {
        return capitalBalance;
    }

    public void setCapitalBalance(BigDecimal capitalBalance) {
        this.capitalBalance = capitalBalance;
    }

    public BigDecimal getTransType() {
        return transType;
    }

    public void setTransType(BigDecimal transType) {
        this.transType = transType;
    }

    public Date getDealTime() {
        return dealTime;
    }

    public void setDealTime(Date dealTime) {
        this.dealTime = dealTime;
    }

    public BigDecimal getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(BigDecimal transAmount) {
        this.transAmount = transAmount;
    }

    public BigDecimal getTransProportion() {
        return transProportion;
    }

    public void setTransProportion(BigDecimal transProportion) {
        this.transProportion = transProportion;
    }

    public BigDecimal getTransInterest() {
        return transInterest;
    }

    public void setTransInterest(BigDecimal transInterest) {
        this.transInterest = transInterest;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    

    public String getBusiProdCode() {
        return busiProdCode;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public BigDecimal getProductCode() {
        return productCode;
    }

    public void setProductCode(BigDecimal productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getLiabId() {
        return liabId;
    }

    public void setLiabId(BigDecimal liabId) {
        this.liabId = liabId;
    }

    public String getLiabName() {
        return liabName;
    }

    public void setLiabName(String liabName) {
        this.liabName = liabName;
    }

    public Date getAccountDate() {
        return accountDate;
    }

    public void setAccountDate(Date accountDate) {
        this.accountDate = accountDate;
    }

    public BigDecimal getInterestCapital() {
        return interestCapital;
    }

    public void setInterestCapital(BigDecimal interestCapital) {
        this.interestCapital = interestCapital;
    }

    public Date getCountDate() {
        return countDate;
    }

    public void setCountDate(Date countDate) {
        this.countDate = countDate;
    }

    public BigDecimal getAccountValue() {
        return accountValue;
    }

    public void setAccountValue(BigDecimal accountValue) {
        this.accountValue = accountValue;
    }

    public BigDecimal getUnitNumber() {
        return unitNumber;
    }

    public void setUnitNumber(BigDecimal unitNumber) {
        this.unitNumber = unitNumber;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getAccountId() {
        return accountId;
    }

    public void setAccountId(BigDecimal accountId) {
        this.accountId = accountId;
    }

    public Date getBalanceDate() {
        return balanceDate;
    }

    public void setBalanceDate(Date balanceDate) {
        this.balanceDate = balanceDate;
    }

    public BigDecimal getBusiItemId() {
        return busiItemId;
    }

    public void setBusiItemId(BigDecimal busiItemId) {
        this.busiItemId = busiItemId;
    }

    
    public BigDecimal getInvestAccountType() {
		return investAccountType;
	}

	public void setInvestAccountType(BigDecimal investAccountType) {
		this.investAccountType = investAccountType;
	}

	
	public String getBusiAbbrName() {
		return busiAbbrName;
	}

	public void setBusiAbbrName(String busiAbbrName) {
		this.busiAbbrName = busiAbbrName;
	}
	
	
	@Override
    public String toString() {
        return "QryAccountBO [accountId=" + accountId + ", balanceDate=" + balanceDate + ", policyCode=" + policyCode
                + ", busiItemId=" + busiItemId + ", itemId=" + itemId + ", busiProdCode=" + busiProdCode
                + ", busiProdName=" + busiProdName + ", productCode=" + productCode + ", productName=" + productName
                + ", liabId=" + liabId + ", liabName=" + liabName + ", accountDate=" + accountDate
                + ", interestCapital=" + interestCapital + ", countDate=" + countDate + ", accountValue="
                + accountValue + ", unitNumber=" + unitNumber + ", unitPrice=" + unitPrice + ", transType=" + transType
                + ", dealTime=" + dealTime + ", transAmount=" + transAmount + ", transProportion=" + transProportion
                + ", transInterest=" + transInterest + ", policyAccountStatus=" + policyAccountStatus
                + ", capitalBalance=" + capitalBalance + ", accountType=" + accountType + ", interestBalance="
                + interestBalance + ", interestRate=" + interestRate + ", regularRepay=" + regularRepay
                + ", transCode=" + transCode + ", transTime=" + transTime + ", payDueDate=" + payDueDate + "]";
    }

    public BigDecimal getItemId() {
        return itemId;
    }

    public void setItemId(BigDecimal itemId) {
        this.itemId = itemId;
    }

    @Override
    public String getBizId() {
        return null;
    }

}
