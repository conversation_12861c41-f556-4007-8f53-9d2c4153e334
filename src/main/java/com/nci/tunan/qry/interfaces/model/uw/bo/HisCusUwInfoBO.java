package com.nci.tunan.qry.interfaces.model.uw.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * 
 * @description 既往保全信息BO
 * <AUTHOR> <EMAIL> 
 * @date 2015-5-23 下午9:36:34 
 * @.belongToModule 综合查询-核保
 */
@SuppressWarnings("serial")
public class HisCusUwInfoBO extends BaseBO {
	/**
	 * 核保ID
	 */
    private BigDecimal uwId;
    /**
	 * 用户ID
	 */
    private BigDecimal uwUserId;
    /**
	 * 客户ID
	 */
    private BigDecimal customerId;
    /**
	 * 责任名称
	 */
    private String productName;
    /**
	 * 核保决定
	 */
    private BigDecimal uwDecision;
    /**
	 * 受理状态
	 */
    private String acceptStatus;
    /**
	 * 核保状态
	 */
    private String uwStatus;
    /**
	 * 受理日期
	 */
    private Date acceptTime;
    /**
	 * 保全项编码
	 */
    private String serviceCode;
    /**
	 * 保全项名称
	 */
    private String serviceName;
    /**
	 * 保单号
	 */
    private String policyCode;
    /**
	 * 受理号
	 */
    private String acceptCode;
    /**
	 * 保单层核保决定
	 */
    private String policyDecision;
    /**
	 * 险种编码
	 */
    private String queryBusiCode;
    /**
	 * 是否非标史
	 */
    private  String  isStandardRisk;
    /**
	 * 投保人或被保人
	 */
    private String roleType;
    /**
	 * 投被保人异常告知
	 */
    private BigDecimal isExceptionNotifyFlag;
    /**
	 * 是否经过人工核保
	 */
    private String uwAutoFlag;
    /**
	 * 保全申请id
	 */
    private BigDecimal acceptId;
    /**
     * 机构号
     */
    private String organCode;
    /**
     * 年收入SURVEY_MODULE_RESULT
     */
    private String surveyModuleResult;
    /**
	 * 现在的total1
	 */
	private BigDecimal total1;
	
	/**
	 * 现在的total2
	 */
	private BigDecimal total2;
	/**
	 * 投保人或者被保人异常告知
	 */
	private String appntoldimpartInfo;
	/**
	 * 投被保人异常告知
	 */
	private BigDecimal isExceptionNotifyFlag2;
	
	public BigDecimal getIsExceptionNotifyFlag2() {
		return isExceptionNotifyFlag2;
	}

	public void setIsExceptionNotifyFlag2(BigDecimal isExceptionNotifyFlag2) {
		this.isExceptionNotifyFlag2 = isExceptionNotifyFlag2;
	}

	public String getAppntoldimpartInfo() {
		return appntoldimpartInfo;
	}

	public void setAppntoldimpartInfo(String appntoldimpartInfo) {
		this.appntoldimpartInfo = appntoldimpartInfo;
	}
	
	public BigDecimal getTotal1() {
		return total1;
	}

	public void setTotal1(BigDecimal total1) {
		this.total1 = total1;
	}

	public BigDecimal getTotal2() {
		return total2;
	}

	public void setTotal2(BigDecimal total2) {
		this.total2 = total2;
	}
	
    public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getSurveyModuleResult() {
		return surveyModuleResult;
	}

	public void setSurveyModuleResult(String surveyModuleResult) {
		this.surveyModuleResult = surveyModuleResult;
	}
    public String getUwAutoFlag() {
		return uwAutoFlag;
	}

	public void setUwAutoFlag(String uwAutoFlag) {
		this.uwAutoFlag = uwAutoFlag;
	}

	public BigDecimal getAcceptId() {
		return acceptId;
	}

	public void setAcceptId(BigDecimal acceptId) {
		this.acceptId = acceptId;
	}

	public BigDecimal getIsExceptionNotifyFlag() {
		return isExceptionNotifyFlag;
	}

	public void setIsExceptionNotifyFlag(BigDecimal isExceptionNotifyFlag) {
		this.isExceptionNotifyFlag = isExceptionNotifyFlag;
	}

    public String getRoleType() {
		return roleType;
	}

	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}
    public String getQueryBusiCode() {
		return queryBusiCode;
	}

	public void setQueryBusiCode(String queryBusiCode) {
		this.queryBusiCode = queryBusiCode;
	}
    public String getPolicyDecision() {
		return policyDecision;
	}

	public void setPolicyDecision(String policyDecision) {
		this.policyDecision = policyDecision;
	}

	public BigDecimal getUwId() {
        return uwId;
    }

    public void setUwId(BigDecimal uwId) {
        this.uwId = uwId;
    }

    public BigDecimal getUwUserId() {
        return uwUserId;
    }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public void setUwUserId(BigDecimal uwUserId) {
        this.uwUserId = uwUserId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getUwDecision() {
        return uwDecision;
    }

    public void setUwDecision(BigDecimal uwDecision) {
        this.uwDecision = uwDecision;
    }

    public String getAcceptStatus() {
        return acceptStatus;
    }

    public void setAcceptStatus(String acceptStatus) {
        this.acceptStatus = acceptStatus;
    }

    public String getUwStatus() {
        return uwStatus;
    }

    public void setUwStatus(String uwStatus) {
        this.uwStatus = uwStatus;
    }

    public Date getAcceptTime() {
        return acceptTime;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getAcceptCode() {
        return acceptCode;
    }

    public void setAcceptCode(String acceptCode) {
        this.acceptCode = acceptCode;
    }

    public String getIsStandardRisk() {
		return isStandardRisk;
	}

	public void setIsStandardRisk(String isStandardRisk) {
		this.isStandardRisk = isStandardRisk;
	}


	@Override
	public String toString() {
		return "HisCusUwInfoBO [uwId=" + uwId + ", uwUserId=" + uwUserId
				+ ", customerId=" + customerId + ", productName=" + productName
				+ ", uwDecision=" + uwDecision + ", acceptStatus="
				+ acceptStatus + ", uwStatus=" + uwStatus + ", acceptTime="
				+ acceptTime + ", serviceCode=" + serviceCode
				+ ", serviceName=" + serviceName + ", policyCode=" + policyCode
				+ ", acceptCode=" + acceptCode + ", policyDecision="
				+ policyDecision + ", queryBusiCode=" + queryBusiCode
				+ ", isStandardRisk=" + isStandardRisk + ", roleType="
				+ roleType + ", isExceptionNotifyFlag=" + isExceptionNotifyFlag
				+ ", uwAutoFlag=" + uwAutoFlag + ", acceptId=" + acceptId
				+ ", organCode=" + organCode + ", surveyModuleResult="
				+ surveyModuleResult + ", total1=" + total1 + ", total2="
				+ total2 + ", appntoldimpartInfo=" + appntoldimpartInfo
				+ ", isExceptionNotifyFlag2=" + isExceptionNotifyFlag2 + "]";
	}

	/**
	 * 
	 * @description 父类需重写方法
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL> 
	 * @return
	 */
    @Override
    public String getBizId() {
        return null;
    }

}
