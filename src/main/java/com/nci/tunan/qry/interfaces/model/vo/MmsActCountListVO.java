package com.nci.tunan.qry.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;
/**
 * @description 营销活动统计清单VO 
 * <AUTHOR> 
 * @date 2016年2月5日 上午9:43:38
 */
public class MmsActCountListVO extends BaseVO{
    
    /** 
     * @Fields serialVersionUID : 
     */ 
    private static final long serialVersionUID = 1L;

    /** 管理机构代码 */
    private String organCode;
    
    /** 管理机构名称 */
    private String organName;
    
    /** 营销活动名称 */
    private String marketName;
    
    /** 子活动名称 */
    private String activityName;
    
    /** @Fields channelCode : 销售渠道代码*/
    private String channelCode;
    
    /** @Fields channelName : 销售渠道名称*/
    private String channelName;
    
    /** @Fields bankCode : 银行网点代码*/
    private String bankCode;
    
    /** @Fields bankName : 银行网点名称*/
    private String bankName;
    
    /** @Fields agentCode : 中介代码*/
    private String agentCode;
    
    /** @Fields agentName : 中介名称*/
    private String agentName;
    
    
    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getMarketName() {
        return marketName;
    }

    public void setMarketName(String marketName) {
        this.marketName = marketName;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    @Override
    public String getBizId() {
        // TODO Auto-generated method stub
        return null;
    }
    
}
