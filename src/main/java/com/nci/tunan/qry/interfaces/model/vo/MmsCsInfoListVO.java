package com.nci.tunan.qry.interfaces.model.vo;

import java.util.LinkedHashMap;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 产品组合、营销活动保全业务清单VO
 * <AUTHOR>
 *
 */
public class MmsCsInfoListVO extends BaseVO {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/** 保全申请日期 */
	
    /** 销售渠道代码 */
    private String channelCode;
    
	/** 营销类型 */
	
	/** 银行网点代码 */
    private String bankCode;
    
    /** 中介机构代码 */
    private String agentCode;
    
    /** 中介机构名称 */
    private String agentName;
	
	/** 产品组合、营销活动代码 */
    private String groupCode;
    
    /** 产品组合、营销活动名称 */
    private String groupName;
    
	/** 保全项目 */
    
    /** 管理机构代码 */
    private String organCode;
    
    /** 管理机构名称 */
    private String organName;
    
	/** 保全受理号 */
    
	/** 保单号 */
    
	/** 补退费金额 */
    
    private LinkedHashMap<String, Object> value;
    
    public LinkedHashMap<String, Object> getValue() {
		return value;
	}

	public void setValue(LinkedHashMap<String, Object> value) {
		this.value = value;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}
    
	@Override
	public String getBizId() {
		return null;
	}

}
