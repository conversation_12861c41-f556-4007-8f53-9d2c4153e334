package com.nci.tunan.qry.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;
import com.nci.udmp.util.lang.DateUtilsEx;

@SuppressWarnings("serial")
public class QryContractProductCompVO extends BaseVO {
    /**
     * @Fields itemId : 责任组ID
     */
    private BigDecimal itemId;
    /**
     * @Fields productId : 精算产品ID
     */
    private String productId;
    /**
     * @Fields busiItemId : 险种ID
     */
    private BigDecimal busiItemId;
    /**
     * @Fields busiPrdId : 产品ID
     */
    private BigDecimal busiPrdId;
    /**
     * @Fields applyCode : 投保单号
     */
    private String applyCode;
    /**
     * @Fields policyCode : 保单号
     */
    private String policyCode;
    /**
     * @Fields amount : 保额
     */
    private BigDecimal amount;
    /**
     * @Fields unit : 份数
     */
    private BigDecimal unit;
    /**
     * @Fields applyDate : 投保日期
     */
    private Date applyDate;
    /**
     * @Fields validateDate : 生效日期
     */
    private Date validateDate;
    /**
     * @Fields expiryDate : 终止日期
     */
    private Date expiryDate;
    /**
     * @Fields paidupDate : 缴费终止日期
     */
    private Date paidupDate;
    /**
     * @Fields liabilityState : 责任组状态
     */
    private BigDecimal liabilityState;
    /**
     * @Fields endCause : 责任终止原因
     */
    private String endCause;
    /**
     * @Fields premFreq : 交费方式
     */
    private BigDecimal premFreq;
    /**
     * @Fields chargeYear : 交费年期
     */
    private BigDecimal chargeYear;
    /**
     * @Fields chargePeriod : 交费年期类型
     */
    private String chargePeriod;
    /**
     * @Fields coveragePeriod : 保障年期类型
     */
    private String coveragePeriod;
    /**
     * @Fields coverageYear : 保障年期
     */
    private BigDecimal coverageYear;
    /**
     * @Fields benefitLevel : 投保档次
     */
    private String benefitLevel;
    /**
     * @Fields stdPremAf : 标准期交保费
     */
    private BigDecimal stdPremAf;

    /**
     * @Fields DiscntedPrem : 折扣期交保费
     */
    private BigDecimal discntedPrem;
    /**
     * @Fields renewalDiscntedPremAf : 折扣后保费
     */
    private BigDecimal renewalDiscntedPremAf;
    /**
     * @Fields initialDiscntPremAf : 折扣保费
     */
    private BigDecimal initialDiscntPremAf;
    /**
     * @Fields bonusSa : 累积红利保额
     */
    private BigDecimal bonusSa;
    /**
     * @Fields lapseCause : 失效原因
     */
    private String lapseCause;
    /**
     * @Fields insuredAge : 投保年龄
     */
    private BigDecimal insuredAge;
    /**
     * @Fields waiverEnd : 豁免开始时间
     */
    private Date waiverEnd;
    /**
     * @Fields waiverStart : 豁免结束时间
     */
    private Date waiverStart;

    /**
     * @Fields appendPremAf : 期交保费加费总额
     */
    private BigDecimal appendPremAf;
    /**
     * 产品名称
     */
    private String productNameSys;
    /**
     * flag标识
     */
    private String flag;
    /**
     * 是否预中止
     */
    private String suspend;
    
    /**
     * 加费
     */
    private BigDecimal extraPrem;
    /**
     * 加费开始时间
     */
    private Date startDate;
    /**
     * 加费结束时间
     */
    private Date endDate;
    /**
     * 
     * @description 所属业务产品代码
     * @date 2019-2-22
     * <AUTHOR>
     * @return
     */
     private String busiProdCode;
     
     /**
      * 续保的原保单号
      */
     private String oldPolicyCode;
     
     /**
      * 续保次数
      */
     private int policyCount;
     /**
      * 保全变更ID
      */
     private BigDecimal changeId;
     
     /**
      * 保单变更ID
      */
     private BigDecimal policyChgId;
     
     /**
      * 交費方式
      */
     private String chargeName;
     /**
 	 * 保单续投转投标识
 	 */
 	private String policyReinsureFlag;
	
	/** 98492start */
    /**
     * 养老年间开始领取年龄
     */
    private BigDecimal startPayAge;
    
    /**
     * 养老年金开始领取日
     */
    private Date startPayDate;
    
    /**
     * 养老年金开始领取日string
     */
    private String startPayDateString;
    
    /**
     * 保证领取期间类型
     */
    private String field8;
    
    /**
     * 保证领取期间
     */
    private String field9;

    /**
     * 养老年金领取方式
     */
    private String yljPayFrep;

    /**
	 * 领取频率
	 */
	private String payFreq;
    
	/**
     * 稳健回报型投资组合分配比例
     */
    private String assignRateOne;
    
    /**
     * 积极进取型投资组合分配比例
     */
    private String assignRateTwo;
    
    /**
     * 首期不定期交保费 
     */
    private BigDecimal irregularPrem;
    /** 98492end */
	
	 	
	public String getPolicyReinsureFlag() {
		return policyReinsureFlag;
	}

	public void setPolicyReinsureFlag(String policyReinsureFlag) {
		this.policyReinsureFlag = policyReinsureFlag;
	}

	public BigDecimal getChangeId() {
		return changeId;
	}

	public void setChangeId(BigDecimal changeId) {
		this.changeId = changeId;
	}

	public BigDecimal getPolicyChgId() {
		return policyChgId;
	}

	public void setPolicyChgId(BigDecimal policyChgId) {
		this.policyChgId = policyChgId;
	}

	public String getChargeName() {
		return chargeName;
	}

	public void setChargeName(String chargeName) {
		this.chargeName = chargeName;
	}

	public int getPolicyCount() {
		return policyCount;
	}

	public void setPolicyCount(int policyCount) {
		this.policyCount = policyCount;
	}

	public String getOldPolicyCode() {
		return oldPolicyCode;
	}

	public void setOldPolicyCode(String oldPolicyCode) {
		this.oldPolicyCode = oldPolicyCode;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

    public BigDecimal getItemId() {
        return itemId;
    }

    public void setItemId(BigDecimal itemId) {
        this.itemId = itemId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public BigDecimal getBusiItemId() {
        return busiItemId;
    }

    public void setBusiItemId(BigDecimal busiItemId) {
        this.busiItemId = busiItemId;
    }

    public BigDecimal getBusiPrdId() {
        return busiPrdId;
    }

    public void setBusiPrdId(BigDecimal busiPrdId) {
        this.busiPrdId = busiPrdId;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getUnit() {
        return unit;
    }

    public void setUnit(BigDecimal unit) {
        this.unit = unit;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getValidateDate() {
        return validateDate;
    }

    public void setValidateDate(Date validateDate) {
        this.validateDate = validateDate;
    }

    public Date getExpiryDate() {
    	return this.expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Date getPaidupDate() {
    	return this.paidupDate;
    }

    public void setPaidupDate(Date paidupDate) {
        this.paidupDate = paidupDate;
    }

    public BigDecimal getLiabilityState() {
        return liabilityState;
    }

    public void setLiabilityState(BigDecimal liabilityState) {
        this.liabilityState = liabilityState;
    }

    public String getEndCause() {
        return endCause;
    }

    public void setEndCause(String endCause) {
        this.endCause = endCause;
    }

    public BigDecimal getPremFreq() {
        return premFreq;
    }

    public void setPremFreq(BigDecimal premFreq) {
        this.premFreq = premFreq;
    }

    public BigDecimal getChargeYear() {
        return chargeYear;
    }

    public void setChargeYear(BigDecimal chargeYear) {
        this.chargeYear = chargeYear;
    }

    public String getChargePeriod() {
        return chargePeriod;
    }

    public void setChargePeriod(String chargePeriod) {
        this.chargePeriod = chargePeriod;
    }

    public String getCoveragePeriod() {
        return coveragePeriod;
    }

    public void setCoveragePeriod(String coveragePeriod) {
        this.coveragePeriod = coveragePeriod;
    }

    public BigDecimal getCoverageYear() {
        return coverageYear;
    }

    public void setCoverageYear(BigDecimal coverageYear) {
        this.coverageYear = coverageYear;
    }

    public String getBenefitLevel() {
        return benefitLevel;
    }

    public void setBenefitLevel(String benefitLevel) {
        this.benefitLevel = benefitLevel;
    }

    public BigDecimal getStdPremAf() {
        return stdPremAf;
    }

    public void setStdPremAf(BigDecimal stdPremAf) {
        this.stdPremAf = stdPremAf;
    }

    public BigDecimal getDiscntedPrem() {
        return discntedPrem;
    }

    public void setDiscntedPrem(BigDecimal discntedPrem) {
        this.discntedPrem = discntedPrem;
    }

    public BigDecimal getRenewalDiscntedPremAf() {
        return renewalDiscntedPremAf;
    }

    public void setRenewalDiscntedPremAf(BigDecimal renewalDiscntedPremAf) {
        this.renewalDiscntedPremAf = renewalDiscntedPremAf;
    }

    public BigDecimal getInitialDiscntPremAf() {
        return initialDiscntPremAf;
    }

    public void setInitialDiscntPremAf(BigDecimal initialDiscntPremAf) {
        this.initialDiscntPremAf = initialDiscntPremAf;
    }

    public BigDecimal getBonusSa() {
        return bonusSa;
    }

    public void setBonusSa(BigDecimal bonusSa) {
        this.bonusSa = bonusSa;
    }

    public String getLapseCause() {
        return lapseCause;
    }

    public void setLapseCause(String lapseCause) {
        this.lapseCause = lapseCause;
    }

    public BigDecimal getInsuredAge() {
        return insuredAge;
    }

    public void setInsuredAge(BigDecimal insuredAge) {
        this.insuredAge = insuredAge;
    }

    public Date getWaiverEnd() {
        return waiverEnd;
    }

    public void setWaiverEnd(Date waiverEnd) {
        this.waiverEnd = waiverEnd;
    }

    public Date getWaiverStart() {
        return waiverStart;
    }

    public void setWaiverStart(Date waiverStart) {
        this.waiverStart = waiverStart;
    }

    public BigDecimal getAppendPremAf() {
        return appendPremAf;
    }

    public void setAppendPremAf(BigDecimal appendPremAf) {
        this.appendPremAf = appendPremAf;
    }

    public String getProductNameSys() {
        return productNameSys;
    }

    public void setProductNameSys(String productNameSys) {
        this.productNameSys = productNameSys;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
    

    public String getSuspend() {
        return suspend;
    }

    public void setSuspend(String suspend) {
        this.suspend = suspend;
    }

    
    public BigDecimal getExtraPrem() {
		return extraPrem;
	}

	public void setExtraPrem(BigDecimal extraPrem) {
		this.extraPrem = extraPrem;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	
	private BigDecimal deductibleFranchise;//免赔额
	private BigDecimal payoutRate;//赔付比例m
	private String extraType;//加费类型m
	private Date initialValidateDate;// 续保保单的原始生效日期m
	private BigDecimal renew;//是否自动续保m
	
	public BigDecimal getDeductibleFranchise() {
		return deductibleFranchise;
	}

	public void setDeductibleFranchise(BigDecimal deductibleFranchise) {
		this.deductibleFranchise = deductibleFranchise;
	}

	public BigDecimal getPayoutRate() {
		return payoutRate;
	}

	public void setPayoutRate(BigDecimal payoutRate) {
		this.payoutRate = payoutRate;
	}

	public String getExtraType() {
		return extraType;
	}   

	public void setExtraType(String extraType) {
		this.extraType = extraType;
	}

	public Date getInitialValidateDate() {
		return initialValidateDate;
	}

	public void setInitialValidateDate(Date initialValidateDate) {
		this.initialValidateDate = initialValidateDate;
	}

	public BigDecimal getRenew() {
		return renew;
	}

	public void setRenew(BigDecimal renew) {
		this.renew = renew;
	}
	
	public BigDecimal getStartPayAge() {
		return startPayAge;
	}

	public void setStartPayAge(BigDecimal startPayAge) {
		this.startPayAge = startPayAge;
	}

	public Date getStartPayDate() {
		return startPayDate;
	}

	public void setStartPayDate(Date startPayDate) {
		this.startPayDate = startPayDate;
	}

	public String getField8() {
		return field8;
	}

	public void setField8(String field8) {
		this.field8 = field8;
	}

	public String getField9() {
		return field9;
	}

	public void setField9(String field9) {
		this.field9 = field9;
	}

	public String getPayFreq() {
		return payFreq;
	}

	public void setPayFreq(String payFreq) {
		this.payFreq = payFreq;
	}

	public String getAssignRateOne() {
		return assignRateOne;
	}

	public void setAssignRateOne(String assignRateOne) {
		this.assignRateOne = assignRateOne;
	}

	public String getAssignRateTwo() {
		return assignRateTwo;
	}

	public void setAssignRateTwo(String assignRateTwo) {
		this.assignRateTwo = assignRateTwo;
	}

	public BigDecimal getIrregularPrem() {
		return irregularPrem;
	}

	public void setIrregularPrem(BigDecimal irregularPrem) {
		this.irregularPrem = irregularPrem;
	}

	public String getStartPayDateString() {
		return startPayDateString;
	}

	public void setStartPayDateString(String startPayDateString) {
		this.startPayDateString = startPayDateString;
	}
	
	public String getYljPayFrep() {
		return yljPayFrep;
	}

	public void setYljPayFrep(String yljPayFrep) {
		this.yljPayFrep = yljPayFrep;
	}

	@Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "QryContractProductCompVO [itemId=" + itemId + ", productId=" + productId + ", busiItemId=" + busiItemId
                + ", busiPrdId=" + busiPrdId + ", applyCode=" + applyCode + ", policyCode=" + policyCode + ", amount="
                + amount + ", unit=" + unit + ", applyDate=" + applyDate + ", validateDate=" + validateDate
                + ", expiryDate=" + expiryDate + ", paidupDate=" + paidupDate + ", liabilityState=" + liabilityState
                + ", endCause=" + endCause + ", premFreq=" + premFreq + ", chargeYear=" + chargeYear
                + ", chargePeriod=" + chargePeriod + ", coveragePeriod=" + coveragePeriod + ", coverageYear="
                + coverageYear + ", benefitLevel=" + benefitLevel + ", stdPremAf=" + stdPremAf + ", discntedPrem="
                + discntedPrem + ", renewalDiscntedPremAf=" + renewalDiscntedPremAf + ", initialDiscntPremAf="
                + initialDiscntPremAf + ", bonusSa=" + bonusSa + ", lapseCause=" + lapseCause + ", insuredAge="
                + insuredAge + ", waiverEnd=" + waiverEnd + ", waiverStart=" + waiverStart + ", appendPremAf="
                + appendPremAf + ", productNameSys=" + productNameSys + ", flag=" + flag + "]";
    }

}
