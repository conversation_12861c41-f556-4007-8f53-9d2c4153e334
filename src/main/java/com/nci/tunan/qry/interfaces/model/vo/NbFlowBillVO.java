package com.nci.tunan.qry.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 新契约流程清单
 * 
 * <AUTHOR>
 * 
 */
/**20180924 提交**/
public class NbFlowBillVO extends BaseVO {

	private static final long serialVersionUID = -7618265755302788700L;

	// *************************************************
	private String sendState;
	private String receiveState;
	private Date sendTime;
	private Date receiveDate;
	private String receiveTime;
	
	private String holderResidentName;
	private String insuredResidentName;
	
	private String printOrg;
	private String printOrgName;
	private String agentlevel;
	private String subinputType;
	private String accountquestiontype;
	private String paymode;
	private String banktextstatus;
	private String payfailcause;
	private String scannum;
	private String processstep;
	private String problemdoc;
	private String efficiency;
	private Date scandate;
	
	private String questiontype;
	public String getQuestiontype() {
		return questiontype;
	}

	public void setQuestiontype(String questiontype) {
		this.questiontype = questiontype;
	}
	
	public Date getScandate() {
		return scandate;
	}

	public void setScandate(Date scandate) {
		this.scandate = scandate;
	}

	private String productCode;
	
	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	

	public String getAgentlevel() {
		return agentlevel;
	}

	public void setAgentlevel(String agentlevel) {
		this.agentlevel = agentlevel;
	}

	public String getSubinputType() {
		return subinputType;
	}

	public void setSubinputType(String subinputType) {
		this.subinputType = subinputType;
	}

	public String getAccountquestiontype() {
		return accountquestiontype;
	}

	public void setAccountquestiontype(String accountquestiontype) {
		this.accountquestiontype = accountquestiontype;
	}

	public String getPaymode() {
		return paymode;
	}

	public void setPaymode(String paymode) {
		this.paymode = paymode;
	}

	public String getBanktextstatus() {
		return banktextstatus;
	}

	public void setBanktextstatus(String banktextstatus) {
		this.banktextstatus = banktextstatus;
	}

	public String getPayfailcause() {
		return payfailcause;
	}

	public void setPayfailcause(String payfailcause) {
		this.payfailcause = payfailcause;
	}

	public String getScannum() {
		return scannum;
	}

	public void setScannum(String scannum) {
		this.scannum = scannum;
	}

	public String getProcessstep() {
		return processstep;
	}

	public void setProcessstep(String processstep) {
		this.processstep = processstep;
	}

	public String getProblemdoc() {
		return problemdoc;
	}

	public void setProblemdoc(String problemdoc) {
		this.problemdoc = problemdoc;
	}

	public String getEfficiency() {
		return efficiency;
	}

	public void setEfficiency(String efficiency) {
		this.efficiency = efficiency;
	}
	public String getPrintOrg() {
		return printOrg;
	}

	public void setPrintOrg(String printOrg) {
		this.printOrg = printOrg;
	}

	public String getPrintOrgName() {
		return printOrgName;
	}

	public void setPrintOrgName(String printOrgName) {
		this.printOrgName = printOrgName;
	}

	public String getHolderResidentName() {
		return holderResidentName;
	}

	public void setHolderResidentName(String holderResidentName) {
		this.holderResidentName = holderResidentName;
	}

	public String getInsuredResidentName() {
		return insuredResidentName;
	}

	public void setInsuredResidentName(String insuredResidentName) {
		this.insuredResidentName = insuredResidentName;
	}

	public String getReceiveTime() {
		return receiveTime;
	}

	public void setReceiveTime(String receiveTime) {
		this.receiveTime = receiveTime;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public Date getReceiveDate() {
		return receiveDate;
	}

	public void setReceiveDate(Date receiveDate) {
		this.receiveDate = receiveDate;
	}

	public String getSendState() {
		return sendState;
	}

	public void setSendState(String sendState) {
		this.sendState = sendState;
	}

	public String getReceiveState() {
		return receiveState;
	}

	public void setReceiveState(String receiveState) {
		this.receiveState = receiveState;
	}

	private String conclusionDesc;
	private String scanStatus;
	private String printStatusName;
	private String signResultDesc;
	private String causeTypeDesc;
	private String qtstatusName;
	private String errorSourceDesc;
	private String errorCorrectTypeName;
	private String errstatusName;
	private String siteName;

	public String getConclusionDesc() {
		return conclusionDesc;
	}

	public void setConclusionDesc(String conclusionDesc) {
		this.conclusionDesc = conclusionDesc;
	}

	public String getScanStatus() {
		return scanStatus;
	}

	public void setScanStatus(String scanStatus) {
		this.scanStatus = scanStatus;
	}

	public String getPrintStatusName() {
		return printStatusName;
	}

	public void setPrintStatusName(String printStatusName) {
		this.printStatusName = printStatusName;
	}

	public String getSignResultDesc() {
		return signResultDesc;
	}

	public void setSignResultDesc(String signResultDesc) {
		this.signResultDesc = signResultDesc;
	}

	public String getCauseTypeDesc() {
		return causeTypeDesc;
	}

	public void setCauseTypeDesc(String causeTypeDesc) {
		this.causeTypeDesc = causeTypeDesc;
	}

	public String getQtstatusName() {
		return qtstatusName;
	}

	public void setQtstatusName(String qtstatusName) {
		this.qtstatusName = qtstatusName;
	}

	public String getErrorSourceDesc() {
		return errorSourceDesc;
	}

	public void setErrorSourceDesc(String errorSourceDesc) {
		this.errorSourceDesc = errorSourceDesc;
	}

	public String getErrorCorrectTypeName() {
		return errorCorrectTypeName;
	}

	public void setErrorCorrectTypeName(String errorCorrectTypeName) {
		this.errorCorrectTypeName = errorCorrectTypeName;
	}

	public String getErrstatusName() {
		return errstatusName;
	}

	public void setErrstatusName(String errstatusName) {
		this.errstatusName = errstatusName;
	}

	public String getSiteName() {
		return siteName;
	}

	public void setSiteName(String siteName) {
		this.siteName = siteName;
	}

	// **********************************************

	private String statusName;

	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	private String appBy;
	private String auditBy;

	public String getAppBy() {
		return appBy;
	}

	public void setAppBy(String appBy) {
		this.appBy = appBy;
	}

	public String getAuditBy() {
		return auditBy;
	}

	public void setAuditBy(String auditBy) {
		this.auditBy = auditBy;
	}

	private String organCode;
	private String promoteOrganCode;//展业机构  add  chenhf   bug：24180
	private String applyCode;
	private Date applyDate;
	private Date issueDate;// 承保日期
	private String policyCode;
	private String agentCode;
	private String agentName;
	private String customerName;
	private String applicationName;// 被保人
	private String salesOrganName;
	private String businessPrdId;
	private String productNameSys;// 险种名称
	private String errorDetail;
	private BigDecimal feeAmount;
	private BigDecimal submitChannel;// 递交渠道 = 终端类型
	private String channelType;// 销售渠道 = 所属渠道

	private String submitChannelName;// 递交渠道 = 终端类型
	private String channelTypeName;// 销售渠道 = 所属渠道

	private Date overdueTime;
	private BigDecimal mediaType;


	public BigDecimal getMediaType() {
		return mediaType;
	}

	public void setMediaType(BigDecimal mediaType) {
		this.mediaType = mediaType;
	}

	private BigDecimal conclusionCode;
	private String causeDesc;
	private String feeStatus; //
	private BigDecimal unitNumber;// 交费凭证号
	private BigDecimal imageScanStatus;
	private String proposalStatus;
	private Date insertTime;
	private Date acknowledgementInsertTime;
	private Date uwCompleteTime;
	private String uwUserCode;
	private Date operateTime;
	private String causeIdList;
	private BigDecimal printStatus;
	private BigDecimal printType;
	private BigDecimal siteCode;
	private String postId;
	private BigDecimal printTimes;
	private String signStatus;
	private String causeType;
	private BigDecimal qtStatus;
	private Date qtTime;
	private String qtUser;
	private String errorSource;
	private String correctType;
	private Date appDate;
	private Date auditDate;
	private Date valiDate;
	private Date policyRecycleDate;
	private Date reminderDate;
	private String reminderStatus;
	private Date inputDate;
	private Date startDate;// 统计开始时间
	private Date endDate;// 统计开始时间
	private Date changePolicyTime;//焕发保单时间
	private String content;//出单前撤保原因
	
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getChangePolicyTime() {
		return changePolicyTime;
	}

	public void setChangePolicyTime(Date changePolicyTime) {
		this.changePolicyTime = changePolicyTime;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	private String customerLevel;
	private BigDecimal elePrintTimes;// 电子保单重出次数
	private String exhibitionName;// 展业机构名称
	private BigDecimal paycount;// 划款次数
	private BigDecimal payAmount;// 已预收金额
	private BigDecimal firstPrem;//首期保费
	public BigDecimal getFirstPrem() {
		return firstPrem;
	}

	public void setFirstPrem(BigDecimal firstPrem) {
		this.firstPrem = firstPrem;
	}

	private String payStatus;// 划款状态
	private BigDecimal scanTimes;// 扫描次数
	private String orgName;

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	private Date outDate;// 签单状态时间

	private Date operationTime;// 出单时间

	private Date addDate;// 录入开始时间

	private Date checkTime;// 首次核保时间

	private Date firstPrintTime;// 首次打印时间

	private Date lastPrintTime;// 最后打印时间

	private String payType;// 付款方式

	private String decisionDesc;// 核保结论

	private String statusDesc;// 录入状态

	private BigDecimal issueId;// 问题件ID

	private String issueType;// 问题件类型

	private BigDecimal ifGoodStart;// 是否开门红活动

	private String statusCode;// 纠错状态

	private String comments;// 初审原因说明
	private BigDecimal certiQuestion;// 证件可疑
	private BigDecimal telQuestion;// 电话可疑
	private String accountQuestion;// 账号可疑

	private Date endTime;// 录入完成时间
	
	private Date finishTime;//溢交退费完成时间
	private Date startFinishTime;// 溢交退费统计开始时间
	private Date endFinishTime;// 溢交退费统计结束时间
	private BigDecimal refundAmount;//溢交退费金额
	private Date acknowledgementImageTime;//回执扫描时间
	private BigDecimal signDate;//承保时效
	private BigDecimal platformFlag;//打印错误内容
	private String errorContent;//打印平台标记
	private String confirmWay;//移动平台签名确认方式
	
	
	public String getConfirmWay() {
		return confirmWay;
	}

	public void setConfirmWay(String confirmWay) {
		this.confirmWay = confirmWay;
	}

	public BigDecimal getPlatformFlag() {
		return platformFlag;
	}

	public void setPlatformFlag(BigDecimal platformFlag) {
		this.platformFlag = platformFlag;
	}


	public String getErrorContent() {
		return errorContent;
	}

	public void setErrorContent(String errorContent) {
		this.errorContent = errorContent;
	}

	public BigDecimal getSignDate() {
		return signDate;
	}

	public void setSignDate(BigDecimal signDate) {
		this.signDate = signDate;
	}

	public Date getAcknowledgementImageTime() {
		return acknowledgementImageTime;
	}

	public void setAcknowledgementImageTime(Date acknowledgementImageTime) {
		this.acknowledgementImageTime = acknowledgementImageTime;
	}

	public Date getFinishTime() {
		return finishTime;
	}

	public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}

	public Date getStartFinishTime() {
		return startFinishTime;
	}

	public void setStartFinishTime(Date startFinishTime) {
		this.startFinishTime = startFinishTime;
	}

	public Date getEndFinishTime() {
		return endFinishTime;
	}

	public void setEndFinishTime(Date endFinishTime) {
		this.endFinishTime = endFinishTime;
	}

	public BigDecimal getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public BigDecimal getCertiQuestion() {
		return certiQuestion;
	}

	public void setCertiQuestion(BigDecimal certiQuestion) {
		this.certiQuestion = certiQuestion;
	}

	public BigDecimal getTelQuestion() {
		return telQuestion;
	}

	public void setTelQuestion(BigDecimal telQuestion) {
		this.telQuestion = telQuestion;
	}

	public String getAccountQuestion() {
		return accountQuestion;
	}

	public void setAccountQuestion(String accountQuestion) {
		this.accountQuestion = accountQuestion;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}

	public String getCustomerLevel() {
		return customerLevel;
	}

	public void setCustomerLevel(String customerLevel) {
		this.customerLevel = customerLevel;
	}

	public String getReminderStatus() {
		return reminderStatus;
	}

	public void setReminderStatus(String reminderStatus) {
		this.reminderStatus = reminderStatus;
	}

	public String getIssueType() {
		return issueType;
	}

	public void setIssueType(String issueType) {
		this.issueType = issueType;
	}

	public BigDecimal getIssueId() {
		return issueId;
	}

	public void setIssueId(BigDecimal issueId) {
		this.issueId = issueId;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}

	public String getDecisionDesc() {
		return decisionDesc;
	}

	public void setDecisionDesc(String decisionDesc) {
		this.decisionDesc = decisionDesc;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public Date getFirstPrintTime() {
		return firstPrintTime;
	}

	public void setFirstPrintTime(Date firstPrintTime) {
		this.firstPrintTime = firstPrintTime;
	}

	public Date getLastPrintTime() {
		return lastPrintTime;
	}

	public void setLastPrintTime(Date lastPrintTime) {
		this.lastPrintTime = lastPrintTime;
	}

	public Date getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Date checkTime) {
		this.checkTime = checkTime;
	}

	public Date getAddDate() {
		return addDate;
	}

	public void setAddDate(Date addDate) {
		this.addDate = addDate;
	}

	public Date getOperationTime() {
		return operationTime;
	}

	public void setOperationTime(Date operationTime) {
		this.operationTime = operationTime;
	}

	public Date getOutDate() {
		return outDate;
	}

	public void setOutDate(Date outDate) {
		this.outDate = outDate;
	}

	public BigDecimal getScanTimes() {
		return scanTimes;
	}

	public void setScanTimes(BigDecimal scanTimes) {
		this.scanTimes = scanTimes;
	}

	public BigDecimal getPaycount() {
		return paycount;
	}

	public void setPaycount(BigDecimal paycount) {
		this.paycount = paycount;
	}

	public BigDecimal getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public String getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getSubmitChannelName() {
		return submitChannelName;
	}

	public void setSubmitChannelName(String submitChannelName) {
		this.submitChannelName = submitChannelName;
	}

	public String getChannelTypeName() {
		return channelTypeName;
	}

	public void setChannelTypeName(String channelTypeName) {
		this.channelTypeName = channelTypeName;
	}

	public String getExhibitionName() {
		return exhibitionName;
	}

	public void setExhibitionName(String exhibitionName) {
		this.exhibitionName = exhibitionName;
	}

	public BigDecimal getElePrintTimes() {
		return elePrintTimes;
	}

	public void setElePrintTimes(BigDecimal elePrintTimes) {
		this.elePrintTimes = elePrintTimes;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getSalesOrganName() {
		return salesOrganName;
	}

	public void setSalesOrganName(String salesOrganName) {
		this.salesOrganName = salesOrganName;
	}

	// public BigDecimal getBusinessPrdId() {
	// return businessPrdId;
	// }
	//
	// public void setBusinessPrdId(BigDecimal businessPrdId) {
	// this.businessPrdId = businessPrdId;
	// }

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public String getBusinessPrdId() {
		return businessPrdId;
	}

	public void setBusinessPrdId(String businessPrdId) {
		this.businessPrdId = businessPrdId;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public BigDecimal getSubmitChannel() {
		return submitChannel;
	}

	public void setSubmitChannel(BigDecimal submitChannel) {
		this.submitChannel = submitChannel;
	}

	public Date getOverdueTime() {
		return overdueTime;
	}

	public void setOverdueTime(Date overdueTime) {
		this.overdueTime = overdueTime;
	}

	public BigDecimal getConclusionCode() {
		return conclusionCode;
	}

	public void setConclusionCode(BigDecimal conclusionCode) {
		this.conclusionCode = conclusionCode;
	}

	public String getCauseDesc() {
		return causeDesc;
	}

	public void setCauseDesc(String causeDesc) {
		this.causeDesc = causeDesc;
	}

	public String getFeeStatus() {
		return feeStatus;
	}

	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}

	public BigDecimal getUnitNumber() {
		return unitNumber;
	}

	public void setUnitNumber(BigDecimal unitNumber) {
		this.unitNumber = unitNumber;
	}

	public BigDecimal getImageScanStatus() {
		return imageScanStatus;
	}

	public void setImageScanStatus(BigDecimal imageScanStatus) {
		this.imageScanStatus = imageScanStatus;
	}

	public String getProposalStatus() {
		return proposalStatus;
	}

	public void setProposalStatus(String proposalStatus) {
		this.proposalStatus = proposalStatus;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	public Date getUwCompleteTime() {
		return uwCompleteTime;
	}

	public void setUwCompleteTime(Date uwCompleteTime) {
		this.uwCompleteTime = uwCompleteTime;
	}

	public Date getOperateTime() {
		return operateTime;
	}

	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}

	public String getCauseIdList() {
		return causeIdList;
	}

	public void setCauseIdList(String causeIdList) {
		this.causeIdList = causeIdList;
	}

	public BigDecimal getPrintStatus() {
		return printStatus;
	}

	public void setPrintStatus(BigDecimal printStatus) {
		this.printStatus = printStatus;
	}

	public BigDecimal getPrintType() {
		return printType;
	}

	public void setPrintType(BigDecimal printType) {
		this.printType = printType;
	}

	public BigDecimal getSiteCode() {
		return siteCode;
	}

	public void setSiteCode(BigDecimal siteCode) {
		this.siteCode = siteCode;
	}

	public String getPostId() {
		return postId;
	}

	public void setPostId(String postId) {
		this.postId = postId;
	}

	public BigDecimal getPrintTimes() {
		return printTimes;
	}

	public void setPrintTimes(BigDecimal printTimes) {
		this.printTimes = printTimes;
	}

	public String getSignStatus() {
		return signStatus;
	}

	public void setSignStatus(String signStatus) {
		this.signStatus = signStatus;
	}

	public String getCauseType() {
		return causeType;
	}

	public void setCauseType(String causeType) {
		this.causeType = causeType;
	}

	public BigDecimal getQtStatus() {
		return qtStatus;
	}

	public void setQtStatus(BigDecimal qtStatus) {
		this.qtStatus = qtStatus;
	}

	public Date getQtTime() {
		return qtTime;
	}

	public void setQtTime(Date qtTime) {
		this.qtTime = qtTime;
	}

	public String getQtUser() {
		return qtUser;
	}

	public void setQtUser(String qtUser) {
		this.qtUser = qtUser;
	}

	public String getErrorSource() {
		return errorSource;
	}

	public void setErrorSource(String errorSource) {
		this.errorSource = errorSource;
	}

	public String getCorrectType() {
		return correctType;
	}

	public void setCorrectType(String correctType) {
		this.correctType = correctType;
	}

	public Date getAppDate() {
		return appDate;
	}

	public void setAppDate(Date appDate) {
		this.appDate = appDate;
	}

	public Date getAuditDate() {
		return auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	public Date getValiDate() {
		return valiDate;
	}

	public void setValiDate(Date valiDate) {
		this.valiDate = valiDate;
	}

	public Date getPolicyRecycleDate() {
		return policyRecycleDate;
	}

	public void setPolicyRecycleDate(Date policyRecycleDate) {
		this.policyRecycleDate = policyRecycleDate;
	}

	public Date getReminderDate() {
		return reminderDate;
	}

	public void setReminderDate(Date reminderDate) {
		this.reminderDate = reminderDate;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getUwUserCode() {
		return uwUserCode;
	}

	public void setUwUserCode(String uwUserCode) {
		this.uwUserCode = uwUserCode;
	}

	public String getProductNameSys() {
		return productNameSys;
	}

	public void setProductNameSys(String productNameSys) {
		this.productNameSys = productNameSys;
	}

	public String getErrorDetail() {
		return errorDetail;
	}

	public void setErrorDetail(String errorDetail) {
		this.errorDetail = errorDetail;
	}

	public Date getInputDate() {
		return inputDate;
	}

	public void setInputDate(Date inputDate) {
		this.inputDate = inputDate;
	}

	public String getApplicationName() {
		return applicationName;
	}

	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

	public Date getAcknowledgementInsertTime() {
		return acknowledgementInsertTime;
	}

	public void setAcknowledgementInsertTime(Date acknowledgementInsertTime) {
		this.acknowledgementInsertTime = acknowledgementInsertTime;
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

	public BigDecimal getIfGoodStart() {
		return ifGoodStart;
	}

	public void setIfGoodStart(BigDecimal ifGoodStart) {
		this.ifGoodStart = ifGoodStart;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}
	
	public Date getIssueDate() {
		return issueDate;
	}

	public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}

	public String getPromoteOrganCode() {
		return promoteOrganCode;
	}

	public void setPromoteOrganCode(String promoteOrganCode) {
		this.promoteOrganCode = promoteOrganCode;
	}
	
}
