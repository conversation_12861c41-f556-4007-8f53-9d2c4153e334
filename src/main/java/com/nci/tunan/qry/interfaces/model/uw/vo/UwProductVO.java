package com.nci.tunan.qry.interfaces.model.uw.vo;

import com.nci.udmp.framework.model.*;

import org.slf4j.Logger;

import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;
import java.util.List;

/** 
 * @description UwProductVO对象
 * <AUTHOR> 
 * @date 2015-05-26 10:59:04  
 */
public class UwProductVO extends BaseVO{	
	private BigDecimal chargeInputType;	//缴费期间控件类型
	private BigDecimal coverageInputType;//保险期间控件类型
	/*以上四个属性来自元素单元配置表T_PAGECFG_UNIT*/
	
	private String limitFlag;			//判断当前责任组是否曾经提交过限额数据的标志
	private String specialFlag;			//判断当前责任组是否存在以前的特约信息
	
	private String productNameSys;
	private String ProductName;
	private List<UwDecisionReasonVO> list;
	
	private BigDecimal insuredListId;
	
    private BigDecimal limitAfterPrem;
	
	private BigDecimal limitAfterAmount;
	
	private String countWay;
	
	private BigDecimal insuredId;       //被保人ID
	
	private BigDecimal customerId;	
	
	private BigDecimal id;
	
	private BigDecimal pid;
	
	private BigDecimal isparent;
	
	private BigDecimal productId;
	
	private String customerName;
	
	private BigDecimal initialType;
	
	//private Date ApplyDate;
	
	private Date validateDate;
	 /** 
	* @Fields benefitLevel :  null
 	*/ 
	private String benefitLevel;
	 /** 
	* @Fields uwId :  null
 	*/ 
	private BigDecimal uwId;
	 /** 
	* @Fields discntedPremAf :  标准保费 - 折扣保费
 	*/ 
	private BigDecimal discntedPremAf;
	 /** 
	* @Fields autoRenewalIndi :  null
 	*/ 
	private BigDecimal autoRenewalIndi;
	 /** 
	* @Fields uwBusiId :  null
 	*/ 
	private BigDecimal uwBusiId;
	 /** 
	* @Fields coveragePeriod :  null
 	*/ 
	private String coveragePeriod;
	 /** 
	* @Fields itemId :  null
 	*/ 
	private BigDecimal itemId;
	 /** 
	* @Fields uwPrdId :  null
 	*/ 
	private BigDecimal uwPrdId;
	 /** 
	* @Fields busiProdCode :  null
 	*/ 
	private String busiProdCode;
	 /** 
	* @Fields applyCode :  null
 	*/ 
	private String applyCode;
	 /** 
	* @Fields organCode :  null
 	*/ 
	private String organCode;
		 /** 
	* @Fields postponedMonths :  用于保全
 	*/ 
	private String postponedMonths;
		 /** 
	* @Fields chargeYear :  null
 	*/ 
	private BigDecimal chargeYear;

	 /** 
	* @Fields extraPremAf :  null
 	*/ 
	private BigDecimal extraPremAf;
	 /** 
	* @Fields preDocUwdecision :  null
 	*/ 
	private String preDocUwdecision;
	 /** 
	* @Fields busiItemId :  null
 	*/ 
	private BigDecimal busiItemId;
	 /** 
	* @Fields policyId :  null
 	*/ 
	private BigDecimal policyId;
	 /** 
	* @Fields unit :  null
 	*/ 
	private BigDecimal unit;
	 /** 
	* @Fields coverageYear :  null
 	*/ 
	private BigDecimal coverageYear;
	 /** 
	* @Fields uwDecisionStatus :  null
 	*/ 
	private BigDecimal uwDecisionStatus;
	 /** 
	* @Fields deleteIndi :  null
 	*/ 
	private BigDecimal deleteIndi;
	 /** 
	* @Fields qualityIndi :  null
 	*/ 
	private BigDecimal qualityIndi;
	 /** 
	* @Fields decisionRemark :  null
 	*/ 
	private String decisionRemark;
		 /** 
	* @Fields decisionIndi :    0 -可以下结论
  1-不可以下结论
 从保全或理赔过来的险种责任组是否参与核保决定的标记

是否需要给出核保结论(保全：提出申请的给结论)
 	*/ 
	private BigDecimal decisionIndi;
		 /** 
	* @Fields amount :  null
 	*/ 
	private BigDecimal amount;
	 /** 
	* @Fields totalPremAf :  折扣后保费 + 加费
 	*/ 
	private BigDecimal totalPremAf;
	 /** 
	* @Fields decisionCode :  null
 	*/ 
	private String decisionCode;
	 /** 
	* @Fields chargePeriod :  null
 	*/ 
	private String chargePeriod;
	 /** 
	* @Fields stdPremAf :  null
 	*/ 
	private BigDecimal stdPremAf;
	 /** 
	* @Fields policyCode :  null
 	*/ 
	private String policyCode;
	 /** 
	* @Fields discntPremAf :  null
 	*/ 
	private BigDecimal discntPremAf;
	
	private String productCode;
	
	 private Date applyDate;

	 private String nbProDecisionCode;
	
	private String uwSourceType;

	public String ServiceCode;

	// add byp xuhp 保全核保增补告知时默认显示终止日期 2016年1月5日
	private Date expiryDate;

	private Date paidupDate;  //缴费终止日期
	
	private String inputTypeFlag ;  //是否需要查询缴费期限标识  
	
	private Date customerBirthday;  //客户生日
	
	private String cdecisionCode; //抄单核保结论
	
	private String chargePeriodFlag;  //交费期间标志标识
	
	private BigDecimal chargeYearFlag;  //交费期间标识
	
	private String coveragePeriodFlag; //保险期间标志标识
	
	private BigDecimal coverageYearFlag;  //保险期间标识
	
	private BigDecimal masterProductId;  //主险责任组ID
	
	private String coverageYearName;  //保障年限中文显示
	
	private String chargeYearName;  //缴费年期中文显示
	
	private String coverageYearActionId;  //保险期间动作元素编码
	
	private BigDecimal conAmount;  //原始保额
	
	private BigDecimal conTotalPremAf;//原始总保费
	
	private BigDecimal amountBf;  //限额前保额
	
	private BigDecimal stdPremBf;  //限额前保费
	
	private BigDecimal chargeYearBf;  //限额前缴费期限
	
	private BigDecimal coverageYearBf;  //限额前保障期限
	
	private String chargePeriodBf;  //限额前缴费年期类型
	
	private String coveragePeriodBf;  //限额前保障年期类型
	
	private String conditionDesc;//特约内容
	
    public String getConditionDesc() {
		return conditionDesc;
	}

	public void setConditionDesc(String conditionDesc) {
		this.conditionDesc = conditionDesc;
	}
	
    public BigDecimal getAmountBf() {
		return amountBf;
	}

	public void setAmountBf(BigDecimal amountBf) {
		this.amountBf = amountBf;
	}

	public BigDecimal getStdPremBf() {
		return stdPremBf;
	}

	public void setStdPremBf(BigDecimal stdPremBf) {
		this.stdPremBf = stdPremBf;
	}

	public BigDecimal getChargeYearBf() {
		return chargeYearBf;
	}

	public void setChargeYearBf(BigDecimal chargeYearBf) {
		this.chargeYearBf = chargeYearBf;
	}

	public BigDecimal getCoverageYearBf() {
		return coverageYearBf;
	}

	public void setCoverageYearBf(BigDecimal coverageYearBf) {
		this.coverageYearBf = coverageYearBf;
	}

	public String getChargePeriodBf() {
		return chargePeriodBf;
	}

	public void setChargePeriodBf(String chargePeriodBf) {
		this.chargePeriodBf = chargePeriodBf;
	}

	public String getCoveragePeriodBf() {
		return coveragePeriodBf;
	}

	public void setCoveragePeriodBf(String coveragePeriodBf) {
		this.coveragePeriodBf = coveragePeriodBf;
	}

	public BigDecimal getConTotalPremAf() {
		return conTotalPremAf;
	}

	public void setConTotalPremAf(BigDecimal conTotalPremAf) {
		this.conTotalPremAf = conTotalPremAf;
	}
	
    public BigDecimal getConAmount() {
		return conAmount;
	}

	public void setConAmount(BigDecimal conAmount) {
		this.conAmount = conAmount;
	}
	
	public String getCoverageYearActionId() {
		return coverageYearActionId;
	}
	public void setCoverageYearActionId(String coverageYearActionId) {
		this.coverageYearActionId = coverageYearActionId;
	}
	public String getCoverageYearName() {
		return coverageYearName;
	}
	public void setCoverageYearName(String coverageYearName) {
		this.coverageYearName = coverageYearName;
	}
	public String getChargeYearName() {
		return chargeYearName;
	}
	public void setChargeYearName(String chargeYearName) {
		this.chargeYearName = chargeYearName;
	}
	
	public BigDecimal getMasterProductId() {
		return masterProductId;
	}

	public void setMasterProductId(BigDecimal masterProductId) {
		this.masterProductId = masterProductId;
	}

	public String getChargePeriodFlag() {
		return chargePeriodFlag;
	}

	public void setChargePeriodFlag(String chargePeriodFlag) {
		this.chargePeriodFlag = chargePeriodFlag;
	}

	public BigDecimal getChargeYearFlag() {
		return chargeYearFlag;
	}

	public void setChargeYearFlag(BigDecimal chargeYearFlag) {
		this.chargeYearFlag = chargeYearFlag;
	}

	public String getCoveragePeriodFlag() {
		return coveragePeriodFlag;
	}

	public void setCoveragePeriodFlag(String coveragePeriodFlag) {
		this.coveragePeriodFlag = coveragePeriodFlag;
	}

	public BigDecimal getCoverageYearFlag() {
		return coverageYearFlag;
	}

	public void setCoverageYearFlag(BigDecimal coverageYearFlag) {
		this.coverageYearFlag = coverageYearFlag;
	}

	public String getCdecisionCode() {
		return cdecisionCode;
	}

	public void setCdecisionCode(String cdecisionCode) {
		this.cdecisionCode = cdecisionCode;
	}
	
	public Date getCustomerBirthday() {
		return customerBirthday;
	}

	public void setCustomerBirthday(Date customerBirthday) {
		this.customerBirthday = customerBirthday;
	}

	public String getInputTypeFlag() {
		return inputTypeFlag;
	}

	public void setInputTypeFlag(String inputTypeFlag) {
		this.inputTypeFlag = inputTypeFlag;
	}

	public Date getPaidupDate() {
		return paidupDate;
	}

	public void setPaidupDate(Date paidupDate) {
		this.paidupDate = paidupDate;
	}
	
	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}
	
	 public String getServiceCode() {
		return ServiceCode;
	 }
	
	 public void setServiceCode(String serviceCode) {
	 	ServiceCode = serviceCode;
	 }
		
	public String getUwSourceType() {
		return uwSourceType;
	}

	public void setUwSourceType(String uwSourceType) {
		this.uwSourceType = uwSourceType;
	}

	public String getNbProDecisionCode() {
		return nbProDecisionCode;
	}

	public void setNbProDecisionCode(String nbProDecisionCode) {
		this.nbProDecisionCode = nbProDecisionCode;
	}
				
	 public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public void setBenefitLevel(String benefitLevel) {
		this.benefitLevel = benefitLevel;
	}
	
	public String getBenefitLevel() {
		return benefitLevel;
	}
	 public void setUwId(BigDecimal uwId) {
		this.uwId = uwId;
	}
	
	public BigDecimal getUwId() {
		return uwId;
	}
	 public void setDiscntedPremAf(BigDecimal discntedPremAf) {
		this.discntedPremAf = discntedPremAf;
	}
	
	public BigDecimal getDiscntedPremAf() {
		return discntedPremAf;
	}
	 public void setAutoRenewalIndi(BigDecimal autoRenewalIndi) {
		this.autoRenewalIndi = autoRenewalIndi;
	}
	
	public BigDecimal getAutoRenewalIndi() {
		return autoRenewalIndi;
	}
	 public void setUwBusiId(BigDecimal uwBusiId) {
		this.uwBusiId = uwBusiId;
	}
	
	public BigDecimal getUwBusiId() {
		return uwBusiId;
	}
	 public void setCoveragePeriod(String coveragePeriod) {
		this.coveragePeriod = coveragePeriod;
	}
	
	public String getCoveragePeriod() {
		return coveragePeriod;
	}
	 public void setItemId(BigDecimal itemId) {
		this.itemId = itemId;
	}
	
	public BigDecimal getItemId() {
		return itemId;
	}
	 public void setUwPrdId(BigDecimal uwPrdId) {
		this.uwPrdId = uwPrdId;
	}
	
	public BigDecimal getUwPrdId() {
		return uwPrdId;
	}
	 public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}
	
	public String getBusiProdCode() {
		return busiProdCode;
	}
	 public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	
	public String getApplyCode() {
		return applyCode;
	}
	 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}

		 public String getPostponedMonths() {
		return postponedMonths;
	}

	public void setPostponedMonths(String postponedMonths) {
		this.postponedMonths = postponedMonths;
	}

		public void setChargeYear(BigDecimal chargeYear) {
		this.chargeYear = chargeYear;
	}
	
	public BigDecimal getChargeYear() {
		return chargeYear;
	}

	 public void setExtraPremAf(BigDecimal extraPremAf) {
		this.extraPremAf = extraPremAf;
	}
	
	public BigDecimal getExtraPremAf() {
		return extraPremAf;
	}
	 public void setPreDocUwdecision(String preDocUwdecision) {
		this.preDocUwdecision = preDocUwdecision;
	}
	
	public String getPreDocUwdecision() {
		return preDocUwdecision;
	}

	 public BigDecimal getBusiItemId() {
		return busiItemId;
	}

	public void setBusiItemId(BigDecimal busiItemId) {
		this.busiItemId = busiItemId;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}
	
	public BigDecimal getPolicyId() {
		return policyId;
	}
	 public void setUnit(BigDecimal unit) {
		this.unit = unit;
	}
	
	public BigDecimal getUnit() {
		return unit;
	}
	 public void setCoverageYear(BigDecimal coverageYear) {
		this.coverageYear = coverageYear;
	}
	
	public BigDecimal getCoverageYear() {
		return coverageYear;
	}
	 public void setUwDecisionStatus(BigDecimal uwDecisionStatus) {
		this.uwDecisionStatus = uwDecisionStatus;
	}
	
	public BigDecimal getUwDecisionStatus() {
		return uwDecisionStatus;
	}
	 public void setDeleteIndi(BigDecimal deleteIndi) {
		this.deleteIndi = deleteIndi;
	}
	
	public BigDecimal getDeleteIndi() {
		return deleteIndi;
	}
	 public void setQualityIndi(BigDecimal qualityIndi) {
		this.qualityIndi = qualityIndi;
	}
	
	public BigDecimal getQualityIndi() {
		return qualityIndi;
	}
	 public void setDecisionRemark(String decisionRemark) {
		this.decisionRemark = decisionRemark;
	}
	
	public String getDecisionRemark() {
		return decisionRemark;
	}
		 public void setDecisionIndi(BigDecimal decisionIndi) {
		this.decisionIndi = decisionIndi;
	}
	
	public BigDecimal getDecisionIndi() {
		return decisionIndi;
	}
		 public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	
	public BigDecimal getAmount() {
		return amount;
	}
	 public void setTotalPremAf(BigDecimal totalPremAf) {
		this.totalPremAf = totalPremAf;
	}
	
	public BigDecimal getTotalPremAf() {
		return totalPremAf;
	}
	 public void setDecisionCode(String decisionCode) {
		this.decisionCode = decisionCode;
	}
	
	public String getDecisionCode() {
		return decisionCode;
	}
	 public void setChargePeriod(String chargePeriod) {
		this.chargePeriod = chargePeriod;
	}
	
	public String getChargePeriod() {
		return chargePeriod;
	}
	 public void setStdPremAf(BigDecimal stdPremAf) {
		this.stdPremAf = stdPremAf;
	}
	
	public BigDecimal getStdPremAf() {
		return stdPremAf;
	}
	 public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getPolicyCode() {
		return policyCode;
	}
	 public void setDiscntPremAf(BigDecimal discntPremAf) {
		this.discntPremAf = discntPremAf;
	}
	
	public BigDecimal getDiscntPremAf() {
		return discntPremAf;
	}
	
	
	public BigDecimal getChargeInputType() {
		return chargeInputType;
	}

	public void setChargeInputType(BigDecimal chargeInputType) {
		this.chargeInputType = chargeInputType;
	}

	public BigDecimal getCoverageInputType() {
		return coverageInputType;
	}

	public void setCoverageInputType(BigDecimal coverageInputType) {
		this.coverageInputType = coverageInputType;
	}

	public String getLimitFlag() {
		return limitFlag;
	}

	public void setLimitFlag(String limitFlag) {
		this.limitFlag = limitFlag;
	}

	public String getSpecialFlag() {
		return specialFlag;
	}

	public void setSpecialFlag(String specialFlag) {
		this.specialFlag = specialFlag;
	}



	public List<UwDecisionReasonVO> getList() {
		return list;
	}

	public void setList(List<UwDecisionReasonVO> list) {
		this.list = list;
	}

	public BigDecimal getLimitAfterPrem() {
		return limitAfterPrem;
	}

	public void setLimitAfterPrem(BigDecimal limitAfterPrem) {
		this.limitAfterPrem = limitAfterPrem;
	}

	public BigDecimal getLimitAfterAmount() {
		return limitAfterAmount;
	}

	public void setLimitAfterAmount(BigDecimal limitAfterAmount) {
		this.limitAfterAmount = limitAfterAmount;
	}

	public String getCountWay() {
		return countWay;
	}

	public void setCountWay(String countWay) {
		this.countWay = countWay;
	}

	public BigDecimal getInsuredId() {
		return insuredId;
	}

	public void setInsuredId(BigDecimal insuredId) {
		this.insuredId = insuredId;
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}

	public BigDecimal getId() {
		return id;
	}

	public void setId(BigDecimal id) {
		this.id = id;
	}

	public BigDecimal getPid() {
		return pid;
	}

	public void setPid(BigDecimal pid) {
		this.pid = pid;
	}

	public BigDecimal getIsparent() {
		return isparent;
	}

	public void setIsparent(BigDecimal isparent) {
		this.isparent = isparent;
	}

	public BigDecimal getProductId() {
		return productId;
	}

	public void setProductId(BigDecimal productId) {
		this.productId = productId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public BigDecimal getInitialType() {
		return initialType;
	}

	public void setInitialType(BigDecimal initialType) {
		this.initialType = initialType;
	}

//	public Date getApplyDate() {
//		return ApplyDate;
//	}
//
//	public void setApplyDate(Date applyDate) {
//		ApplyDate = applyDate;
//	}

	
	
	

	public String getProductNameSys() {
		return productNameSys;
	}

	public void setProductNameSys(String productNameSys) {
		this.productNameSys = productNameSys;
	}

	public String getProductName() {
		return ProductName;
	}

	public void setProductName(String productName) {
		ProductName = productName;
	}
	
	

	public BigDecimal getInsuredListId() {
		return insuredListId;
	}

	public void setInsuredListId(BigDecimal insuredListId) {
		this.insuredListId = insuredListId;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}
	
	public Date getValidateDate() {
		return validateDate;
	}

	public void setValidateDate(Date validateDate) {
		this.validateDate = validateDate;
	}

	@Override
    public String getBizId() {
        return null;
    }

	@Override
	public String toString() {
		return "UwProductVO [chargeInputType=" + chargeInputType
				+ ", coverageInputType=" + coverageInputType + ", limitFlag="
				+ limitFlag + ", specialFlag=" + specialFlag
				+ ", productNameSys=" + productNameSys + ", ProductName="
				+ ProductName + ", list=" + list + ", insuredListId="
				+ insuredListId + ", limitAfterPrem=" + limitAfterPrem
				+ ", limitAfterAmount=" + limitAfterAmount + ", countWay="
				+ countWay + ", insuredId=" + insuredId + ", customerId="
				+ customerId + ", id=" + id + ", pid=" + pid + ", isparent="
				+ isparent + ", productId=" + productId + ", customerName="
				+ customerName + ", initialType=" + initialType
				+ ", validateDate=" + validateDate + ", benefitLevel=" + benefitLevel
				+ ", uwId=" + uwId + ", discntedPremAf=" + discntedPremAf
				+ ", autoRenewalIndi=" + autoRenewalIndi + ", uwBusiId="
				+ uwBusiId + ", coveragePeriod=" + coveragePeriod + ", itemId="
				+ itemId + ", uwPrdId=" + uwPrdId + ", busiProdCode="
				+ busiProdCode + ", applyCode=" + applyCode + ", organCode="
				+ organCode + ", postponedMonths=" + postponedMonths
				+ ", chargeYear=" + chargeYear + ", extraPremAf=" + extraPremAf
				+ ", preDocUwdecision=" + preDocUwdecision + ", busiItemId="
				+ busiItemId + ", policyId=" + policyId + ", unit=" + unit
				+ ", coverageYear=" + coverageYear + ", uwDecisionStatus="
				+ uwDecisionStatus + ", deleteIndi=" + deleteIndi
				+ ", qualityIndi=" + qualityIndi + ", decisionRemark="
				+ decisionRemark + ", decisionIndi=" + decisionIndi
				+ ", amount=" + amount + ", totalPremAf=" + totalPremAf
				+ ", decisionCode=" + decisionCode + ", chargePeriod="
				+ chargePeriod + ", stdPremAf=" + stdPremAf + ", policyCode="
				+ policyCode + ", discntPremAf=" + discntPremAf
				+ ", productCode=" + productCode + ", applyDate=" + applyDate
				+ ", nbProDecisionCode=" + nbProDecisionCode
				+ ", uwSourceType=" + uwSourceType + ", ServiceCode="
				+ ServiceCode + ", expiryDate=" + expiryDate + "]";
	}

}
