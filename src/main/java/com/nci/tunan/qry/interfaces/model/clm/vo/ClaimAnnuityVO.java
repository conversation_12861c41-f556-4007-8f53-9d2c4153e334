package com.nci.tunan.qry.interfaces.model.clm.vo;



import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description ClaimAnnuityVO对象
 * <AUTHOR> 
 * @date 2015-09-29 10:57:45  
 */
public class ClaimAnnuityVO extends BaseVO {	
//	年金转换功能添加字段  case_id,policy_id,busi_item_id,beneId;
	
	private BigDecimal caseId;
	private BigDecimal policyId;
	private BigDecimal busiItemId;
	private BigDecimal beneId;
		 public BigDecimal getCaseId() {
		return caseId;
	}

	public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public BigDecimal getBusiItemId() {
		return busiItemId;
	}

	public void setBusiItemId(BigDecimal busiItemId) {
		this.busiItemId = busiItemId;
	}

	public BigDecimal getBeneId() {
		return beneId;
	}

	public void setBeneId(BigDecimal beneId) {
		this.beneId = beneId;
	}

		/** 
	* @Fields interestIndi :  是否计息
 	*/ 
	private BigDecimal interestIndi;
	 /** 
	* @Fields annuityProd :  转换年金产品
 	*/ 
	private BigDecimal annuityProd;
	 /** 
	* @Fields annuityStartDate :  应领开始日期
 	*/ 
	private Date annuityStartDate;
	 /** 
	* @Fields claimLiabId :  理赔责任ID
 	*/ 
	private BigDecimal claimLiabId;
		 /** 
	* @Fields annuityLiab :  转换年金责任
 	*/ 
	private BigDecimal annuityLiab;
	 /** 
	* @Fields annuityPay :  应领金额
 	*/ 
	private BigDecimal annuityPay;
	 /** 
	* @Fields payYear :  给付年期
 	*/ 
	private BigDecimal payYear;
		 /** 
	* @Fields increaseRate :  递增比例
 	*/ 
	private BigDecimal increaseRate;
	 /** 
	* @Fields annuityFreq :  年金领取频率
 	*/ 
	private String annuityFreq;
	 /** 
	* @Fields payPeriod :  给付年期类型
 	*/ 
	private String payPeriod;
		 /** 
	* @Fields claimPayId :  受益分配ID
 	*/ 
	private BigDecimal claimPayId;
	 /** 
	* @Fields increaseYear :  递增年
 	*/ 
	private BigDecimal increaseYear;
	 /** 
	* @Fields totalAmount :  总给付金额
 	*/ 
	private BigDecimal totalAmount;
	 /** 
	* @Fields annuityEndDate :  应领结束日期
 	*/ 
	private Date annuityEndDate;
	 /** 
	* @Fields guaranteePeriod :  保证费率区间
 	*/ 
	private BigDecimal guaranteePeriod;
		 /** 
	* @Fields annuityType :  转换类型
 	*/ 
	private BigDecimal annuityType;
		 /** 
	* @Fields increaseFreq :  递增频率
 	*/ 
	private String increaseFreq;
	 /** 
	* @Fields annuityId :  转年金应领ID
 	*/ 
	private BigDecimal annuityId;
	/**
	 * 受益人年龄
	 */
	private String beneAge;
	
	private String annuityStartDateString;
	
	private String annuityEndDateString;
	
	/**
	 * @Fields computeRemark : 计算公式备注信息
	 */
	private String computeRemark;
		
		public String getAnnuityStartDateString() {
		return annuityStartDateString;
	}

	public void setAnnuityStartDateString(String annuityStartDateString) {
		this.annuityStartDateString = annuityStartDateString;
	}

	public String getAnnuityEndDateString() {
		return annuityEndDateString;
	}

	public void setAnnuityEndDateString(String annuityEndDateString) {
		this.annuityEndDateString = annuityEndDateString;
	}

		public String getBeneAge() {
		return beneAge;
	}

	public void setBeneAge(String beneAge) {
		this.beneAge = beneAge;
	}

		public void setInterestIndi(BigDecimal interestIndi) {
		this.interestIndi = interestIndi;
	}
	
	public BigDecimal getInterestIndi() {
		return interestIndi;
	}
	 public void setAnnuityProd(BigDecimal annuityProd) {
		this.annuityProd = annuityProd;
	}
	
	public BigDecimal getAnnuityProd() {
		return annuityProd;
	}
	 public void setAnnuityStartDate(Date annuityStartDate) {
		this.annuityStartDate = annuityStartDate;
	}
	
	public Date getAnnuityStartDate() {
		return annuityStartDate;
	}
	 public void setClaimLiabId(BigDecimal claimLiabId) {
		this.claimLiabId = claimLiabId;
	}
	
	public BigDecimal getClaimLiabId() {
		return claimLiabId;
	}
		 public void setAnnuityLiab(BigDecimal annuityLiab) {
		this.annuityLiab = annuityLiab;
	}
	
	public BigDecimal getAnnuityLiab() {
		return annuityLiab;
	}
	 public void setAnnuityPay(BigDecimal annuityPay) {
		this.annuityPay = annuityPay;
	}
	
	public BigDecimal getAnnuityPay() {
		return annuityPay;
	}
	 public void setPayYear(BigDecimal payYear) {
		this.payYear = payYear;
	}
	
	public BigDecimal getPayYear() {
		return payYear;
	}
		 public void setIncreaseRate(BigDecimal increaseRate) {
		this.increaseRate = increaseRate;
	}
	
	public BigDecimal getIncreaseRate() {
		return increaseRate;
	}
	 public void setAnnuityFreq(String annuityFreq) {
		this.annuityFreq = annuityFreq;
	}
	
	public String getAnnuityFreq() {
		return annuityFreq;
	}
	 public void setPayPeriod(String payPeriod) {
		this.payPeriod = payPeriod;
	}
	
	public String getPayPeriod() {
		return payPeriod;
	}
		 public void setClaimPayId(BigDecimal claimPayId) {
		this.claimPayId = claimPayId;
	}
	
	public BigDecimal getClaimPayId() {
		return claimPayId;
	}
	 public void setIncreaseYear(BigDecimal increaseYear) {
		this.increaseYear = increaseYear;
	}
	
	public BigDecimal getIncreaseYear() {
		return increaseYear;
	}
	 public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}
	
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}
	 public void setAnnuityEndDate(Date annuityEndDate) {
		this.annuityEndDate = annuityEndDate;
	}
	
	public Date getAnnuityEndDate() {
		return annuityEndDate;
	}
	 public void setGuaranteePeriod(BigDecimal guaranteePeriod) {
		this.guaranteePeriod = guaranteePeriod;
	}
	
	public BigDecimal getGuaranteePeriod() {
		return guaranteePeriod;
	}
		 public void setAnnuityType(BigDecimal annuityType) {
		this.annuityType = annuityType;
	}
	
	public BigDecimal getAnnuityType() {
		return annuityType;
	}
		 public void setIncreaseFreq(String increaseFreq) {
		this.increaseFreq = increaseFreq;
	}
	
	public String getIncreaseFreq() {
		return increaseFreq;
	}
	 public void setAnnuityId(BigDecimal annuityId) {
		this.annuityId = annuityId;
	}
	
	public BigDecimal getAnnuityId() {
		return annuityId;
	}
		
	public String getComputeRemark() {
		return computeRemark;
	}

	public void setComputeRemark(String computeRemark) {
		this.computeRemark = computeRemark;
	}

	@Override
    public String getBizId() {
        return null;
    }

	@Override
	public String toString() {
		return "ClaimAnnuityVO [caseId=" + caseId + ", policyId=" + policyId
				+ ", busiItemId=" + busiItemId + ", beneId=" + beneId
				+ ", interestIndi=" + interestIndi + ", annuityProd="
				+ annuityProd + ", annuityStartDate=" + annuityStartDate
				+ ", claimLiabId=" + claimLiabId + ", annuityLiab="
				+ annuityLiab + ", annuityPay=" + annuityPay + ", payYear="
				+ payYear + ", increaseRate=" + increaseRate + ", annuityFreq="
				+ annuityFreq + ", payPeriod=" + payPeriod + ", claimPayId="
				+ claimPayId + ", increaseYear=" + increaseYear
				+ ", totalAmount=" + totalAmount + ", annuityEndDate="
				+ annuityEndDate + ", guaranteePeriod=" + guaranteePeriod
				+ ", annuityType=" + annuityType + ", increaseFreq="
				+ increaseFreq + ", annuityId=" + annuityId + ", beneAge="
				+ beneAge + ", annuityStartDateString="
				+ annuityStartDateString + ", annuityEndDateString="
				+ annuityEndDateString + ", computeRemark=" + computeRemark
				+ "]";
	}
    
}
