package com.nci.tunan.qry.interfaces.model.nb.vo;

import java.math.BigDecimal;
import java.io.Serializable;
import java.lang.String;
import java.util.Date;

/**
 * @description NbContractMasterVO对象
 * <AUTHOR>
 * @date 2014-11-25 15:04:14
 */
public class NbContractMasterVO implements Serializable { 

    /**
     * @Fields serialVersionUID : TODO（用一句话描述这个变量表示什么）
     */

    private static final long serialVersionUID = 1L;
    /**
     * @Fields operatorUserCode : null
     */
    private String operatorUserCode;
    /**
     * @Fields channelId : null
     */
    private String channelId;
    /**
     * @Fields paUserCode : null
     */
    private String paUserCode;
    /**
     * @Fields nextAyerName : null
     */
    private String nextAyerName;
    /**
     * @Fields proposalStatus : null
     */
    private String proposalStatus;
    /**
     * @Fields applyCode : null
     */
    private String applyCode;
    /**
     * @Fields organCode : null
     */
    private String organCode;
    
    /**
     * 管理机构名称
     */
    private String organCodeName;
    
    
    /**
     * @Fields channelType : null
     */
    private String channelType;
    /**
     * @Fields overdueTime : null
     */
    private Date overdueTime;
    /**
     * @Fields saleAgentName : null
     */
    private String saleAgentName;
    /**
     * @Fields insuredFamily : null
     */
    private BigDecimal insuredFamily;
    /**
     * @Fields serviceHandlerName : null
     */
    private String serviceHandlerName;
    /**
     * @Fields nextPayBankAccount : null
     */
    private String nextPayBankAccount;
    /**
     * @Fields policyId : null
     */
    private BigDecimal policyId;
    /**
     * @Fields scanCompleteTime : null
     */
    private Date scanCompleteTime;
    /**
     * @Fields campaignCode : null
     */
    private String campaignCode;
    /**
     * @Fields advancePremIndi : null
     */
    private BigDecimal advancePremIndi;
    /**
     * @Fields derivation : null
     */
    private String derivation;
    /**
     * @Fields initialPayMode : null
     */
    private String initialPayMode;
    /**
     * @Fields channelOrgId : null
     */
    private String channelOrgCode;
    /**
     * @Fields policyType : null
     */
    private String policyType;
    /**
     * @Fields expiryDate : null
     */
    private Date expiryDate;
    /**
     * @Fields submitChannel : null
     */
    private BigDecimal submitChannel;
    /**
     * @Fields liabilityState : null
     */
    private BigDecimal liabilityState;
    /**
     * @Fields policyCode : null
     */
    private String policyCode;
    /**
     * @Fields saleAgentCode : null
     */
    private String saleAgentCode;
    /**
     * @Fields validateDate : null
     */
    private Date validateDate;
    /**
     * @Fields ePolicyFlag : null
     */
    private BigDecimal ePolicyFlag;
    /**
     * @Fields moneyCode : null
     */
    private String moneyCode;
    /**
     * @Fields serviceHandlerCode : null
     */
    private String serviceHandlerCode;
    /**
     * @Fields applyDate : null
     */
    private Date applyDate;
    /**
     * @Fields branchOrganCode : null
     */
    private String branchOrganCode;
    /**
     * @Fields manualUwIndi : null
     */
    private BigDecimal manualUwIndi;
    /**
     * @Fields nextPayMode : null
     */
    private String nextPayMode;
    /**
     * @Fields submissionDate : null
     */
    private Date submissionDate;
    /**
     * @Fields uwUserCode : null
     */
    private BigDecimal uwUserCode;
    /**
     * @Fields serviceHandler : null
     */
    private String serviceHandler;
    /**
     * @Fields eServiceFlag : null
     */
    private BigDecimal eServiceFlag;
    /**
     * @Fields uwCompleteTime : null
     */
    private Date uwCompleteTime;
    /**
     * @Fields riskIndi : null
     */
    private BigDecimal riskIndi;
    /**
     * @Fields serviceBankBranch : null
     */
    private String serviceBankBranch;
    /**
     * @Fields issueUserCode : null
     */
    private String issueUserCode;
    /**
     * @Fields initialPayBankCode : null
     */
    private String initialPayBankCode;
    /**
     * @Fields issueDate : null
     */
    private Date issueDate;
    /**
     * @Fields initialPayerName : null
     */
    private String initialPayerName;
    /**
     * @Fields highSaIndi : null
     */
    private BigDecimal highSaIndi;
    /**
     * @Fields agentOrgId : null
     */
    private String agentOrgId;
    /**
     * @Fields billChecked : null
     */
    private BigDecimal billChecked;
    /**
     * @Fields serviceBank : null
     */
    private String serviceBank;
    /**
     * @Fields paCompleteTime : null
     */
    private Date paCompleteTime;
    /**
     * @Fields initialPayBankAccount : null
     */
    private String initialPayBankAccount;
    /**
     * @Fields birthdayPolIndi : null
     */
    private BigDecimal birthdayPolIndi;
    /**
     * @Fields langCode : null
     */
    private String langCode;
    /**
     * @Fields nextPayBankCode : null
     */
    private String nextPayBankCode;
    /**
     * @Fields scanUserCode : null
     */
    private String scanUserCode;

    private String statusDesc;

    private String typeName;

    private String bankName;

    private String policyPwd;

    private Date startDate;
    private Date endDate;

    private String decisionCode;

    private BigDecimal mediaType;

    private String agentCode;
    private String agentName;
    private String agentLevel;
    private String agentMobile;
    private String agentOrganCode;
    /**
     * 代理人机构名称
     */
    private String agentOrganCodeName;
    
    private String relationToPh;
    private BigDecimal dialectIndi;// 方言件标识
    private String callTimeList;// 电话回访时间段
    private String callTimeLista;// 电话回访时间段 8:00-12:00
    private String callTimeListb;// 电话回访时间段 12:00-13:00
    private String callTimeListc;// 电话回访时间段 13:00-18:00
    private String callTimeListd;// 电话回访时间段 18:00-20:30
    private String bankCode;//服务银行代码
    private String bankBranchCode;//银行网点代码
    private Date acknowledgeDate;//回执日期
    /**
     * 双录标识
     */
    private BigDecimal drqFlag;
    private Date uwSubmitTime;//核保日期
    private String ruleMag;//核保意见
    
    /**
     * #55326 增加销售方式(团队) 字段
     * @description
     * @return
     */
    private String groupTypeCode;
    /** 52031 中介机构号  中介机构名称 start*/
    private String otherManageCom;
    /** 52031 中介机构号  中介机构名称 end*/
    /**
     * #48816 增加 “中介机构代码”，“中介机构名称”，“协议号” 字段
     */
    private String manageComOuter;
    private String otherManageName;
    private String protocolId;
    /**
   	 * 160232 合作中介机构协议号
   	 * @return
   	 */
   	private String  cooperationProtocolId;
    /**
     * 中介机构编码
     * @description
     * @return
     */
    private String entryNo;
	/**
	 * EAST自保件 YES_OR_NO
	 */
	private BigDecimal eastSelfInsured;
	/**
	 * EAST互保件 YES_OR_NO
	 */
	private BigDecimal eastMutualInsured;


	/**
     * 移动平台确认方式
     */
    private String confirmWay;
    /**
     * 投保人、被保险人或受益人与业务员关系
     */
    private String relationToAll;
    /**
     * 是否互保件
     */
    private String isMutualInsured;
    /**
     * 是否自保件
     */
    private String isSelfInsured;
	 /** 
	* @Fields onlineFlag :  上线标识
	*/ 
	private String onlineFlag;	
	

	/**
	 * 投保时客户选择的个人信息保护政策选项内容，多个选项用英文“|”线分割，例如01|02|03|04|05，表示选择了5个政策选项，
	 * 01-处理政策中以加粗方式标注的敏感个人信息，
	 * 02-将个人信息提供给第三方
	 * 03-为本政策中所述目的的处理个人图像、身份识别信息
	 * 04-向境外提供个人信息
	 * 05-根据本人的明确同意，或根据法律法规、法律程序、强制性的行政或司法要求和/或上级主管部门的要求公开个人信息
	 */
	private String personalInfoProtect;
	
	/**
	 * 投保时客户选择的个人信息保护政策选项内容，多个选项用英文“|”线分割，例如01|02|03|04|05，表示选择了5个政策选项，
	 * 01-处理政策中以加粗方式标注的敏感个人信息，
	 */
	private String personalInfoProtect01;
	/**
	 * 投保时客户选择的个人信息保护政策选项内容，多个选项用英文“|”线分割，例如01|02|03|04|05，表示选择了5个政策选项，
	 * 02-将个人信息提供给第三方
	 */
	private String personalInfoProtect02;
	/**
	 * 投保时客户选择的个人信息保护政策选项内容，多个选项用英文“|”线分割，例如01|02|03|04|05，表示选择了5个政策选项，
	 * 03-为本政策中所述目的的处理个人图像、身份识别信息
	 */
	private String personalInfoProtect03;
	/**
	 * 投保时客户选择的个人信息保护政策选项内容，多个选项用英文“|”线分割，例如01|02|03|04|05，表示选择了5个政策选项，
	 * 04-向境外提供个人信息
	 */
	private String personalInfoProtect04;
	/**
	 * 投保时客户选择的个人信息保护政策选项内容，多个选项用英文“|”线分割，例如01|02|03|04|05，表示选择了5个政策选项，
	 * 05-根据本人的明确同意，或根据法律法规、法律程序、强制性的行政或司法要求和/或上级主管部门的要求公开个人信息
	 */
	private String personalInfoProtect05;
	
	/**
	 * 特殊帐户标识(1-中银保信个人养老金账户、非中银保信个人养老金账户时传空)
	 */
	private String specialAccountFlag;
	  /**
     * 执业证标识
     */
    private String licenseNo;
    /**
     * 执业证号码
     */
    private String licenseNumber;
    /**
	 * 客户经理执业证编码 
	 */
	private String bankManagerLicenseNo;
	
	/** 
	 * @Fields applySource : 纸质保单申请渠道
	 */ 
	private BigDecimal applySource;
	

	/**
	 * 纸质回执回销日期，记录纸质保单回执录入系统日期
	 */
	private Date pAcknowledgeDate; //回执日销日期
	

	/**
	 * 回执申请日期名称
	 */
	private String applySourceName;
	
	/**
	 * 新型产品通知接收方式(1-短信 2-邮寄 3-自助下载)
	 */
	private BigDecimal notificationReceiveMethod;
	/**
	 * 银代非实时标志
	 */
	private BigDecimal banknrtFalg;
	/** 
	* @Fields constantsDesc :  常量描述
	*/ 
	private String constantsDesc;
	 /** 
	* @Fields constantsValue :  常量键值
	*/ 
	private String constantsValue;
	 /** 
	* @Fields constantsKey :  常量键名
	*/ 
	private String constantsKey;
	 /** 
	* @Fields constantsId :  常量ID
	*/ 
	private BigDecimal constantsId;
	 /** 
	* @Fields subId :  常量所属子系统，T
	*/ 
	private String subId;
	 /**
     * @Fields haveInsureds : 是否984多被保人
     */
    private String haveInsureds;
    
    /**
     * 合作中介机构代码
     */
    private String cooperationCode;
    /**
     * 合作中介机构名称
     */
    private String cooperationName;
	
	//--------------------------------  get  set    分割线---------------------------------

  //#173020综合查询增加渠道销售方式、是否为互联网保单标识
  	/**
  	 * 销售方式
  	 */
  	private String saleType;
  	public String getSaleType() {
  		return saleType;
  	}
  	public void setSaleType(String saleType) {
  		this.saleType = saleType;
  	}
  	/**
  	 * 互联网合作业务
  	 */
  	 private String internetCooperationBusiness;
 	public String getInternetCooperationBusiness() {
  		return internetCooperationBusiness;
  	}
  	public void setInternetCooperationBusiness(String internetCooperationBusiness) {
  		this.internetCooperationBusiness = internetCooperationBusiness;
  	}
	public String getCooperationCode() {
		return cooperationCode;
	}

	public void setCooperationCode(String cooperationCode) {
		this.cooperationCode = cooperationCode;
	}

	public String getCooperationName() {
		return cooperationName;
	}

	public void setCooperationName(String cooperationName) {
		this.cooperationName = cooperationName;
	}
	
	public BigDecimal getEastSelfInsured() {
		return eastSelfInsured;
	}
	
	public void setEastSelfInsured(BigDecimal eastSelfInsured) {
		this.eastSelfInsured = eastSelfInsured;
	}

	public BigDecimal getEastMutualInsured() {
		return eastMutualInsured;
	}

	public void setEastMutualInsured(BigDecimal eastMutualInsured) {
		this.eastMutualInsured = eastMutualInsured;
	}

	public String getHaveInsureds() {
		return haveInsureds;
	}
	public void setHaveInsureds(String haveInsureds) {
		this.haveInsureds = haveInsureds;
	}
	public String getConstantsDesc() {
		return constantsDesc;
	}
	public void setConstantsDesc(String constantsDesc) {
		this.constantsDesc = constantsDesc;
	}
	public String getConstantsValue() {
		return constantsValue;
	}
	public void setConstantsValue(String constantsValue) {
		this.constantsValue = constantsValue;
	}
	public String getConstantsKey() {
		return constantsKey;
	}
	public void setConstantsKey(String constantsKey) {
		this.constantsKey = constantsKey;
	}
	public BigDecimal getConstantsId() {
		return constantsId;
	}
	public void setConstantsId(BigDecimal constantsId) {
		this.constantsId = constantsId;
	}
	public String getSubId() {
		return subId;
	}
	public void setSubId(String subId) {
		this.subId = subId;
	}
	public BigDecimal getNotificationReceiveMethod() {
		return notificationReceiveMethod;
	}
	public void setNotificationReceiveMethod(BigDecimal notificationReceiveMethod) {
		this.notificationReceiveMethod = notificationReceiveMethod;
	}
	
	public String getApplySourceName() {
		return applySourceName;
	}
	public void setApplySourceName(String applySourceName) {
		this.applySourceName = applySourceName;
	}
	
	public Date getpAcknowledgeDate() {
		return pAcknowledgeDate;
	}
	public void setpAcknowledgeDate(Date pAcknowledgeDate) {
		this.pAcknowledgeDate = pAcknowledgeDate;
	}
	public void setApplySource(BigDecimal applySource) {
		this.applySource = applySource;
	}

	public BigDecimal getApplySource() {
		return applySource;
	}
	public String getSpecialAccountFlag() {
		return specialAccountFlag;
	}
	public void setSpecialAccountFlag(String specialAccountFlag) {
		this.specialAccountFlag = specialAccountFlag;
	}
	/**
	 * 投保时客户选择的个人信息保护政策选项内容，多个选项用英文“|”线分割，例如01|02|03|04|05，表示选择了5个政策选项，
	 * 01-处理政策中以加粗方式标注的敏感个人信息，
	 * 02-将个人信息提供给第三方
	 * 03-为本政策中所述目的的处理个人图像、身份识别信息
	 * 04-向境外提供个人信息
	 * 05
	 */
	public String getPersonalInfoProtect() {
		return personalInfoProtect;
	}
	/**
	 * 投保时客户选择的个人信息保护政策选项内容，多个选项用英文“|”线分割，例如01|02|03|04|05，表示选择了5个政策选项，
	 * 01-处理政策中以加粗方式标注的敏感个人信息，
	 * 02-将个人信息提供给第三方
	 * 03-为本政策中所述目的的处理个人图像、身份识别信息
	 * 04-向境外提供个人信息
	 * 05
	 */
	public void setPersonalInfoProtect(String personalInfoProtect) {
		this.personalInfoProtect = personalInfoProtect;
	}
	
	
	public String getPersonalInfoProtect01() {
		return personalInfoProtect01;
	}
	public void setPersonalInfoProtect01(String personalInfoProtect01) {
		this.personalInfoProtect01 = personalInfoProtect01;
	}
	public String getPersonalInfoProtect02() {
		return personalInfoProtect02;
	}
	public void setPersonalInfoProtect02(String personalInfoProtect02) {
		this.personalInfoProtect02 = personalInfoProtect02;
	}
	public String getPersonalInfoProtect03() {
		return personalInfoProtect03;
	}
	public void setPersonalInfoProtect03(String personalInfoProtect03) {
		this.personalInfoProtect03 = personalInfoProtect03;
	}
	public String getPersonalInfoProtect04() {
		return personalInfoProtect04;
	}
	public void setPersonalInfoProtect04(String personalInfoProtect04) {
		this.personalInfoProtect04 = personalInfoProtect04;
	}
	public String getPersonalInfoProtect05() {
		return personalInfoProtect05;
	}
	public void setPersonalInfoProtect05(String personalInfoProtect05) {
		this.personalInfoProtect05 = personalInfoProtect05;
	}
	public String getOnlineFlag() {
		return onlineFlag;
	}
	public void setOnlineFlag(String onlineFlag) {
		this.onlineFlag = onlineFlag;
	}
    public String getIsSelfInsured() {
		return isSelfInsured;
	}

	public void setIsSelfInsured(String isSelfInsured) {
		this.isSelfInsured = isSelfInsured;
	}
    public String getIsMutualInsured() {
		return isMutualInsured;
	}

	public void setIsMutualInsured(String isMutualInsured) {
		this.isMutualInsured = isMutualInsured;
	}

	public String getRelationToAll() {
		return relationToAll;
	}

	public void setRelationToAll(String relationToAll) {
		this.relationToAll = relationToAll;
	}

	public String getConfirmWay() {
		return confirmWay;
	}

	public void setConfirmWay(String confirmWay) {
		this.confirmWay = confirmWay;
	}

	public String getManageComOuter() {
		return manageComOuter;
	}

	public void setManageComOuter(String manageComOuter) {
		this.manageComOuter = manageComOuter;
	}

	public String getProtocolId() {
		return protocolId;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	public String getEntryNo() {
		return entryNo;
	}

	public void setEntryNo(String entryNo) {
		this.entryNo = entryNo;
	}

    public Date getUwSubmitTime() {
		return uwSubmitTime;
	}

	public String getOtherManageCom() {
		return otherManageCom;
	}

	public void setOtherManageCom(String otherManageCom) {
		this.otherManageCom = otherManageCom;
	}

	public String getOtherManageName() {
		return otherManageName;
	}

	public void setOtherManageName(String otherManageName) {
		this.otherManageName = otherManageName;
	}

	public void setUwSubmitTime(Date uwSubmitTime) {
		this.uwSubmitTime = uwSubmitTime;
	}

	public String getRuleMag() {
		return ruleMag;
	}

	public void setRuleMag(String ruleMag) {
		this.ruleMag = ruleMag;
	}
    public Date getAcknowledgeDate() {
		return acknowledgeDate;
	}

	public void setAcknowledgeDate(Date acknowledgeDate) {
		this.acknowledgeDate = acknowledgeDate;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankBranchCode() {
		return bankBranchCode;
	}

	public void setBankBranchCode(String bankBranchCode) {
		this.bankBranchCode = bankBranchCode;
	}

	public String getCallTimeListb() { 
		return callTimeListb; 
	}

	public void setCallTimeListb(String callTimeListb) {
		this.callTimeListb = callTimeListb;
	}

	public String getCallTimeListc() {
		return callTimeListc;
	}

	public void setCallTimeListc(String callTimeListc) {
		this.callTimeListc = callTimeListc;
	}

	public String getCallTimeListd() {
		return callTimeListd;
	}

	public void setCallTimeListd(String callTimeListd) {
		this.callTimeListd = callTimeListd;
	}

	public String getCallTimeLista() {
		return callTimeLista;
	}

	public void setCallTimeLista(String callTimeLista) {
		this.callTimeLista = callTimeLista;
	}

	public BigDecimal getMediaType() {
        return mediaType;
    }

    public void setMediaType(BigDecimal mediaType) {
        this.mediaType = mediaType;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public void setOperatorUserCode(String operatorUserCode) {
        this.operatorUserCode = operatorUserCode;
    }

    public String getOperatorUserCode() {
        return operatorUserCode;
    }

    public void setPaUserCode(String paUserCode) {
        this.paUserCode = paUserCode;
    }

    public String getPaUserCode() {
        return paUserCode;
    }

    public void setNextAyerName(String nextAyerName) {
        this.nextAyerName = nextAyerName;
    }

    public String getNextAyerName() {
        return nextAyerName;
    }

    public String getProposalStatus() {
        return proposalStatus;
    }

    public void setProposalStatus(String proposalStatus) {
        this.proposalStatus = proposalStatus;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setOverdueTime(Date overdueTime) {
        this.overdueTime = overdueTime;
    }

    public Date getOverdueTime() {
        return overdueTime;
    }

    public void setSaleAgentName(String saleAgentName) {
        this.saleAgentName = saleAgentName;
    }

    public String getSaleAgentName() {
        return saleAgentName;
    }

    public void setInsuredFamily(BigDecimal insuredFamily) {
        this.insuredFamily = insuredFamily;
    }

    public BigDecimal getInsuredFamily() {
        return insuredFamily;
    }

    public void setServiceHandlerName(String serviceHandlerName) {
        this.serviceHandlerName = serviceHandlerName;
    }

    public String getServiceHandlerName() {
        return serviceHandlerName;
    }

    public void setNextPayBankAccount(String nextPayBankAccount) {
        this.nextPayBankAccount = nextPayBankAccount;
    }

    public String getNextPayBankAccount() {
        return nextPayBankAccount;
    }

    public void setPolicyId(BigDecimal policyId) {
        this.policyId = policyId;
    }

    public BigDecimal getPolicyId() {
        return policyId;
    }

    public void setScanCompleteTime(Date scanCompleteTime) {
        this.scanCompleteTime = scanCompleteTime;
    }

    public Date getScanCompleteTime() {
        return scanCompleteTime;
    }

    public void setCampaignCode(String campaignCode) {
        this.campaignCode = campaignCode;
    }

    public String getCampaignCode() {
        return campaignCode;
    }

    public void setAdvancePremIndi(BigDecimal advancePremIndi) {
        this.advancePremIndi = advancePremIndi;
    }

    public BigDecimal getAdvancePremIndi() {
        return advancePremIndi;
    }

    public void setDerivation(String derivation) {
        this.derivation = derivation;
    }

    public String getDerivation() {
        return derivation;
    }

    public void setInitialPayMode(String initialPayMode) {
        this.initialPayMode = initialPayMode;
    }

    public String getInitialPayMode() {
        return initialPayMode;
    }

    public String getChannelOrgCode() {
        return channelOrgCode;
    }

    public void setChannelOrgCode(String channelOrgCode) {
        this.channelOrgCode = channelOrgCode;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public BigDecimal getSubmitChannel() {
        return submitChannel;
    }

    public void setSubmitChannel(BigDecimal submitChannel) {
        this.submitChannel = submitChannel;
    }

    public void setLiabilityState(BigDecimal liabilityState) {
        this.liabilityState = liabilityState;
    }

    public BigDecimal getLiabilityState() {
        return liabilityState;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setSaleAgentCode(String saleAgentCode) {
        this.saleAgentCode = saleAgentCode;
    }

    public String getSaleAgentCode() {
        return saleAgentCode;
    }

    public void setValidateDate(Date validateDate) {
        this.validateDate = validateDate;
    }

    public Date getValidateDate() {
        return validateDate;
    }

    public void setEPolicyFlag(BigDecimal ePolicyFlag) {
        this.ePolicyFlag = ePolicyFlag;
    }

    public BigDecimal getEPolicyFlag() {
        return ePolicyFlag;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setServiceHandlerCode(String serviceHandlerCode) {
        this.serviceHandlerCode = serviceHandlerCode;
    }

    public String getServiceHandlerCode() {
        return serviceHandlerCode;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setBranchOrganCode(String branchOrganCode) {
        this.branchOrganCode = branchOrganCode;
    }

    public String getBranchOrganCode() {
        return branchOrganCode;
    }

    public void setManualUwIndi(BigDecimal manualUwIndi) {
        this.manualUwIndi = manualUwIndi;
    }

    public BigDecimal getManualUwIndi() {
        return manualUwIndi;
    }

    public void setNextPayMode(String nextPayMode) {
        this.nextPayMode = nextPayMode;
    }

    public String getNextPayMode() {
        return nextPayMode;
    }

    public void setSubmissionDate(Date submissionDate) {
        this.submissionDate = submissionDate;
    }

    public Date getSubmissionDate() {
        return submissionDate;
    }

    public void setUwUserCode(BigDecimal uwUserCode) {
        this.uwUserCode = uwUserCode;
    }

    public BigDecimal getUwUserCode() {
        return uwUserCode;
    }

    public void setServiceHandler(String serviceHandler) {
        this.serviceHandler = serviceHandler;
    }

    public String getServiceHandler() {
        return serviceHandler;
    }

    public void setEServiceFlag(BigDecimal eServiceFlag) {
        this.eServiceFlag = eServiceFlag;
    }

    public BigDecimal getEServiceFlag() {
        return eServiceFlag;
    }

    public void setUwCompleteTime(Date uwCompleteTime) {
        this.uwCompleteTime = uwCompleteTime;
    }

    public Date getUwCompleteTime() {
        return uwCompleteTime;
    }

    public void setRiskIndi(BigDecimal riskIndi) {
        this.riskIndi = riskIndi;
    }

    public BigDecimal getRiskIndi() {
        return riskIndi;
    }

    public void setServiceBankBranch(String serviceBankBranch) {
        this.serviceBankBranch = serviceBankBranch;
    }

    public String getServiceBankBranch() {
        return serviceBankBranch;
    }

    public void setIssueUserCode(String issueUserCode) {
        this.issueUserCode = issueUserCode;
    }

    public String getIssueUserCode() {
        return issueUserCode;
    }

    public void setInitialPayBankCode(String initialPayBankCode) {
        this.initialPayBankCode = initialPayBankCode;
    }

    public String getInitialPayBankCode() {
        return initialPayBankCode;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setInitialPayerName(String initialPayerName) {
        this.initialPayerName = initialPayerName;
    }

    public String getInitialPayerName() {
        return initialPayerName;
    }

    public void setHighSaIndi(BigDecimal highSaIndi) {
        this.highSaIndi = highSaIndi;
    }

    public BigDecimal getHighSaIndi() {
        return highSaIndi;
    }

    public void setAgentOrgId(String agentOrgId) {
        this.agentOrgId = agentOrgId;
    }

    public String getAgentOrgId() {
        return agentOrgId;
    }

    public void setBillChecked(BigDecimal billChecked) {
        this.billChecked = billChecked;
    }

    public BigDecimal getBillChecked() {
        return billChecked;
    }

    public void setServiceBank(String serviceBank) {
        this.serviceBank = serviceBank;
    }

    public String getServiceBank() {
        return serviceBank;
    }

    public void setPaCompleteTime(Date paCompleteTime) {
        this.paCompleteTime = paCompleteTime;
    }

    public Date getPaCompleteTime() {
        return paCompleteTime;
    }

    public void setInitialPayBankAccount(String initialPayBankAccount) {
        this.initialPayBankAccount = initialPayBankAccount;
    }

    public String getInitialPayBankAccount() {
        return initialPayBankAccount;
    }

    public void setBirthdayPolIndi(BigDecimal birthdayPolIndi) {
        this.birthdayPolIndi = birthdayPolIndi;
    }

    public BigDecimal getBirthdayPolIndi() {
        return birthdayPolIndi;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setNextPayBankCode(String nextPayBankCode) {
        this.nextPayBankCode = nextPayBankCode;
    }

    public String getNextPayBankCode() {
        return nextPayBankCode;
    }

    public void setScanUserCode(String scanUserCode) {
        this.scanUserCode = scanUserCode;
    }

    public String getScanUserCode() {
        return scanUserCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getPolicyPwd() {
        return policyPwd;
    }

    public void setPolicyPwd(String policyPwd) {
        this.policyPwd = policyPwd;
    }

    public String getDecisionCode() {
        return decisionCode;
    }

    public void setDecisionCode(String decisionCode) {
        this.decisionCode = decisionCode;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public BigDecimal getePolicyFlag() {
        return ePolicyFlag;
    }

    public void setePolicyFlag(BigDecimal ePolicyFlag) {
        this.ePolicyFlag = ePolicyFlag;
    }

    public BigDecimal geteServiceFlag() {
        return eServiceFlag;
    }

    public void seteServiceFlag(BigDecimal eServiceFlag) {
        this.eServiceFlag = eServiceFlag;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }


    public String getAgentLevel() {
        return agentLevel;
    }

    public void setAgentLevel(String agentLevel) {
        this.agentLevel = agentLevel;
    }

    public String getAgentMobile() {
        return agentMobile;
    }

    public void setAgentMobile(String agentMobile) {
        this.agentMobile = agentMobile;
    }

    public String getAgentOrganCode() {
        return agentOrganCode;
    }

    public void setAgentOrganCode(String agentOrganCode) {
        this.agentOrganCode = agentOrganCode;
    }

    public String getRelationToPh() {
        return relationToPh;
    }

    public void setRelationToPh(String relationToPh) {
        this.relationToPh = relationToPh;
    }

    public BigDecimal getDialectIndi() {
        return dialectIndi;
    }

    public void setDialectIndi(BigDecimal dialectIndi) {
        this.dialectIndi = dialectIndi;
    }

    public String getCallTimeList() {
        return callTimeList;
    }

    public void setCallTimeList(String callTimeList) {
        this.callTimeList = callTimeList;
    }


    public String getOrganCodeName() {
        return organCodeName;
    }

    public void setOrganCodeName(String organCodeName) {
        this.organCodeName = organCodeName;
    }

    public String getAgentOrganCodeName() {
        return agentOrganCodeName;
    }

    public void setAgentOrganCodeName(String agentOrganCodeName) {
        this.agentOrganCodeName = agentOrganCodeName;
    }


    public BigDecimal getDrqFlag() {
        return drqFlag;
    }

    public void setDrqFlag(BigDecimal drqFlag) {
        this.drqFlag = drqFlag;
    }


    public String getGroupTypeCode() {
		return groupTypeCode;
	}

	public void setGroupTypeCode(String groupTypeCode) {
		this.groupTypeCode = groupTypeCode;
	}
	public String getLicenseNo() {
		return licenseNo;
	}
	public void setLicenseNo(String licenseNo) {
		this.licenseNo = licenseNo;
	}
	public String getLicenseNumber() {
		return licenseNumber;
	}
	public void setLicenseNumber(String licenseNumber) {
		this.licenseNumber = licenseNumber;
	}
	
	public String getBankManagerLicenseNo() {
		return bankManagerLicenseNo;
	}
	public void setBankManagerLicenseNo(String bankManagerLicenseNo) {
		this.bankManagerLicenseNo = bankManagerLicenseNo;
	}
	@Override
    public String toString() {
        return "NbContractMasterVO [operatorUserCode=" + operatorUserCode + ", channelId=" + channelId
                + ", paUserCode=" + paUserCode + ", nextAyerName=" + nextAyerName + ", proposalStatus="
                + proposalStatus + ", applyCode=" + applyCode + ", organCode=" + organCode + ", channelType="
                + channelType + ", overdueTime=" + overdueTime + ", saleAgentName=" + saleAgentName
                + ", insuredFamily=" + insuredFamily + ", serviceHandlerName=" + serviceHandlerName
                + ", nextPayBankAccount=" + nextPayBankAccount + ", policyId=" + policyId + ", scanCompleteTime="
                + scanCompleteTime + ", campaignCode=" + campaignCode + ", advancePremIndi=" + advancePremIndi
                + ", derivation=" + derivation + ", initialPayMode=" + initialPayMode + ", channelOrgCode="
                + channelOrgCode + ", policyType=" + policyType + ", expiryDate=" + expiryDate + ", submitChannel="
                + submitChannel + ", liabilityState=" + liabilityState + ", policyCode=" + policyCode
                + ", saleAgentCode=" + saleAgentCode + ", validateDate=" + validateDate + ", ePolicyFlag="
                + ePolicyFlag + ", moneyCode=" + moneyCode + ", serviceHandlerCode=" + serviceHandlerCode
                + ", applyDate=" + applyDate + ", branchOrganCode=" + branchOrganCode + ", manualUwIndi="
                + manualUwIndi + ", nextPayMode=" + nextPayMode + ", submissionDate=" + submissionDate
                + ", uwUserCode=" + uwUserCode + ", serviceHandler=" + serviceHandler + ", eServiceFlag="
                + eServiceFlag + ", uwCompleteTime=" + uwCompleteTime + ", riskIndi=" + riskIndi
                + ", serviceBankBranch=" + serviceBankBranch + ", issueUserCode=" + issueUserCode
                + ", initialPayBankCode=" + initialPayBankCode + ", issueDate=" + issueDate + ", initialPayerName="
                + initialPayerName + ", highSaIndi=" + highSaIndi + ", agentOrgId=" + agentOrgId + ", billChecked="
                + billChecked + ", serviceBank=" + serviceBank + ", paCompleteTime=" + paCompleteTime
                + ", initialPayBankAccount=" + initialPayBankAccount + ", birthdayPolIndi=" + birthdayPolIndi
                + ", langCode=" + langCode + ", nextPayBankCode=" + nextPayBankCode + ", scanUserCode=" + scanUserCode
                + ", statusDesc=" + statusDesc + ", typeName=" + typeName + ", bankName=" + bankName + ", policyPwd="
                + policyPwd + ", startDate=" + startDate + ", endDate=" + endDate + ", decisionCode=" + decisionCode
                + ", mediaType=" + mediaType + ", agentCode=" + agentCode + ", agentName=" + agentName
                + ", agentLevel=" + agentLevel + ", agentMobile=" + agentMobile + ", agentOrganCode=" + agentOrganCode
                + ", relationToPh=" + relationToPh + ", dialectIndi=" + dialectIndi + ", callTimeList=" + callTimeList
                + ", callTimeLista=" + callTimeLista + ", callTimeListb=" + callTimeListb + ", callTimeListc="
                + callTimeListc + ", callTimeListd=" + callTimeListd +
                 ", drqFlag=" + drqFlag + "]";
    }
	public BigDecimal getBanknrtFalg() {
		return banknrtFalg;
	}
	public void setBanknrtFalg(BigDecimal banknrtFalg) {
		this.banknrtFalg = banknrtFalg;
	}
	public String getCooperationProtocolId() {
		return cooperationProtocolId;
	}
	public void setCooperationProtocolId(String cooperationProtocolId) {
		this.cooperationProtocolId = cooperationProtocolId;
	}
	
}
