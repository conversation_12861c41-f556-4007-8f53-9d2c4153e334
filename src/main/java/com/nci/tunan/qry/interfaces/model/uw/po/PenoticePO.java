package com.nci.tunan.qry.interfaces.model.uw.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/**
 * @description PenoticePO对象
 * <AUTHOR>
 * @date 2015-06-17 14:33:01
 */
public class PenoticePO extends BasePO {
	/** 
	* @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
	*/ 
	private static final long serialVersionUID = 1L;

	/** 属性 --- java类型 --- oracle类型_数据长度_小数位长度_注释信息 */
	// customerName --- String --- VARCHAR2_50_0_null;
	// docListId --- BigDecimal --- NUMBER_16_0_null;
	// uwId --- BigDecimal --- NUMBER_16_0_null;
	// qualityEvaluation --- BigDecimal --- NUMBER_1_0_null;
	// penoticeId --- BigDecimal --- NUMBER_16_0_null;
	// peReason --- String --- VARCHAR2_500_0_null;
	// resultMagnumMsg --- String --- VARCHAR2_1000_0_null;
	// customerId --- BigDecimal --- NUMBER_16_0_null;
	// resultAge --- BigDecimal --- NUMBER_3_0_null;
	// applyCode --- String --- VARCHAR2_20_0_null;
	// roleType --- BigDecimal --- NUMBER_1_0_null;
	// resultCusScore --- String --- VARCHAR2_1_0_null;
	// policyId --- BigDecimal --- NUMBER_16_0_null;
	// customerBirth --- Date --- DATE_7_0_null;
	// otherComments --- String --- VARCHAR2_500_0_null;
	// positiveIndi --- BigDecimal --- NUMBER_1_0_体检结果值为【↑↓*】时，记录阳性标记;
	// resultPeDate --- Date --- DATE_7_0_null;
	// uwHospitalId --- BigDecimal --- NUMBER_16_0_null;
	// resultTel --- String --- VARCHAR2_50_0_null;
	// resultMagnumValue --- String --- VARCHAR2_1000_0_null;
	// qualityComment --- String --- VARCHAR2_500_0_null;
	// resultComment --- String --- VARCHAR2_500_0_null;
	// policyCode --- String --- VARCHAR2_20_0_null;
	// genderCode --- BigDecimal --- NUMBER_1_0_null;
	// esIndi --- BigDecimal --- NUMBER_1_0_null;

	public void setCustomerName(String customerName) {
		setString("customer_name", customerName);
	}

	public String getCustomerName() {
		return getString("customer_name");
	}

	public void setDocListId(BigDecimal docListId) {
		setBigDecimal("doc_list_id", docListId);
	}

	public BigDecimal getDocListId() {
		return getBigDecimal("doc_list_id");
	}

	public void setUwId(BigDecimal uwId) {
		setBigDecimal("uw_id", uwId);
	}

	public BigDecimal getUwId() {
		return getBigDecimal("uw_id");
	}

	public void setQualityEvaluation(BigDecimal qualityEvaluation) {
		setBigDecimal("quality_evaluation", qualityEvaluation);
	}

	public BigDecimal getQualityEvaluation() {
		return getBigDecimal("quality_evaluation");
	}

	public void setPenoticeId(BigDecimal penoticeId) {
		setBigDecimal("penotice_id", penoticeId);
	}

	public BigDecimal getPenoticeId() {
		return getBigDecimal("penotice_id");
	}

	public void setPeReason(String peReason) {
		setString("pe_reason", peReason);
	}

	public String getPeReason() {
		return getString("pe_reason");
	}

	public void setResultMagnumMsg(String resultMagnumMsg) {
		setString("result_magnum_msg", resultMagnumMsg);
	}

	public String getResultMagnumMsg() {
		return getString("result_magnum_msg");
	}

	public void setCustomerId(BigDecimal customerId) {
		setBigDecimal("customer_id", customerId);
	}

	public BigDecimal getCustomerId() {
		return getBigDecimal("customer_id");
	}

	public void setResultAge(BigDecimal resultAge) {
		setBigDecimal("result_age", resultAge);
	}

	public BigDecimal getResultAge() {
		return getBigDecimal("result_age");
	}

	public void setApplyCode(String applyCode) {
		setString("apply_code", applyCode);
	}

	public String getApplyCode() {
		return getString("apply_code");
	}

	public void setRoleType(BigDecimal roleType) {
		setBigDecimal("role_type", roleType);
	}

	public BigDecimal getRoleType() {
		return getBigDecimal("role_type");
	}

	public void setResultCusScore(String resultCusScore) {
		setString("result_cus_score", resultCusScore);
	}

	public String getResultCusScore() {
		return getString("result_cus_score");
	}

	public void setPolicyId(BigDecimal policyId) {
		setBigDecimal("policy_id", policyId);
	}

	public BigDecimal getPolicyId() {
		return getBigDecimal("policy_id");
	}

	public void setCustomerBirth(Date customerBirth) {
		setUtilDate("customer_birth", customerBirth);
	}

	public Date getCustomerBirth() {
		return getUtilDate("customer_birth");
	}

	public void setOtherComments(String otherComments) {
		setString("other_comments", otherComments);
	}

	public String getOtherComments() {
		return getString("other_comments");
	}

	public void setPositiveIndi(BigDecimal positiveIndi) {
		setBigDecimal("positive_indi", positiveIndi);
	}

	public BigDecimal getPositiveIndi() {
		return getBigDecimal("positive_indi");
	}

	public void setResultPeDate(Date resultPeDate) {
		setUtilDate("result_pe_date", resultPeDate);
	}

	public Date getResultPeDate() {
		return getUtilDate("result_pe_date");
	}

	public void setUwHospitalId(BigDecimal uwHospitalId) {
		setBigDecimal("uw_hospital_id", uwHospitalId);
	}

	public BigDecimal getUwHospitalId() {
		return getBigDecimal("uw_hospital_id");
	}

	public void setResultTel(String resultTel) {
		setString("result_tel", resultTel);
	}

	public String getResultTel() {
		return getString("result_tel");
	}

	public void setResultMagnumValue(String resultMagnumValue) {
		setString("result_magnum_value", resultMagnumValue);
	}

	public String getResultMagnumValue() {
		return getString("result_magnum_value");
	}

	public void setQualityComment(String qualityComment) {
		setString("quality_comment", qualityComment);
	}

	public String getQualityComment() {
		return getString("quality_comment");
	}

	public void setResultComment(String resultComment) {
		setString("result_comment", resultComment);
	}

	public String getResultComment() {
		return getString("result_comment");
	}

	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}

	public String getPolicyCode() {
		return getString("policy_code");
	}

	public void setGenderCode(BigDecimal genderCode) {
		setBigDecimal("gender_code", genderCode);
	}

	public BigDecimal getGenderCode() {
		return getBigDecimal("gender_code");
	}

	public void setEsIndi(BigDecimal esIndi) {
		setBigDecimal("es_indi", esIndi);
	}

	public BigDecimal getEsIndi() {
		return getBigDecimal("es_indi");
	}

	public void setIsChangeFlag(BigDecimal isChangeFlag) {
		setBigDecimal("is_change_flag", isChangeFlag);
	}

	public BigDecimal getIsChangeFlag() {
		return getBigDecimal("is_change_flag");
	}

	public void setStatus(String status) {
		setString("status", status);
	}

	public String getStatus() {
		return getString("status");
	}

	public void setIsPhysical(BigDecimal isPhysical) {
		setBigDecimal("is_physical", isPhysical);
	}

	public BigDecimal getIsPhysical() {
		return getBigDecimal("is_physical");
	}

	public void setPeFee(BigDecimal peFee) {
		setBigDecimal("pe_fee", peFee);
	}

	public BigDecimal getPeFee() {
		return getBigDecimal("pe_fee");
	}

	public void setUserName(String userName) {
		setString("user_name", userName);
	}

	public String getUserName() {
		return getString("user_name");
	}

	public String getNewDocumentNo() {
		return getString("newDocument_no");
	}

	public void setNewDocumentNo(String newDocumentNo) {
		setString("newDocument_no", newDocumentNo);
	}

	public void setResultPeStartDate(Date resultPeStartDate) {
		setUtilDate("result_pe_start_date", resultPeStartDate);
	}

	public Date getResultPeStartDate() {
		return getUtilDate("result_pe_start_date");
	}

	public void setResultPeEndDate(Date resultPeEndDate) {
		setUtilDate("result_pe_end_date", resultPeEndDate);
	}

	public Date getResultPeEndDate() {
		return getUtilDate("result_pe_end_date");
	}

	public void setOrganCode(String organCode) {
		setString("organ_code", organCode);
	}

	public String getOrganCode() {
		return getString("organ_code");
	}

	public void setOrganName(String organName) {
		setString("organ_name", organName);
	}

	public String getOrganName() {
		return getString("organ_name");
	}

	public void setPhysicalTotal(BigDecimal physicalTotal) {
		setBigDecimal("physical_total", physicalTotal);
	}

	public BigDecimal getPhysicalTotal() {
		return getBigDecimal("physical_total");
	}

	public void setPositiveTotal(BigDecimal positiveTotal) {
		setBigDecimal("positive_total", positiveTotal);
	}

	public BigDecimal getPositiveTotal() {
		return getBigDecimal("positive_total");
	}

	public BigDecimal getCustomerIdStd() {
		return getBigDecimal("customer_id_std");
	}

	public void setCustomerIdStd(BigDecimal customerIdStd) {
		setBigDecimal("customer_id_std", customerIdStd);
	}

	public void setUwSource(String uwSource) {
		setString("uw_source", uwSource);
	}

	public String getUwSource() {
		return getString("uw_source");
	}

	public void setSendTime(Date sendTime) {
		setUtilDate("send_time", sendTime);
	}

	public Date getSendTime() {
		return getUtilDate("send_time");
	}

	public void setPrintTime(Date printTime) {
		setUtilDate("print_time", printTime);
	}

	public Date getPrintTime() {
		return getUtilDate("print_time");
	}

	public void setCloseTime(Date closeTime) {
		setUtilDate("close_time", closeTime);
	}

	public Date getCloseTime() {
		return getUtilDate("close_time");
	}

	public void setHospitalName(String hospitalName) {
		setString("hospital_name", hospitalName);
	}

	public String getHospitalName() {
		return getString("hospital_name");
	}

	public String getDocumentNo() {
		return getString("document_no");
	}

	public void setDocumentNo(String documentNo) {
		setString("document_no", documentNo);
	}
	public BigDecimal getResultComplete() {
		return getBigDecimal("result_complete");
	}

	public void setResultComplete(BigDecimal resultComplete) {
		setBigDecimal("result_complete", resultComplete);
	}
	
	public BigDecimal getAetiologyAnemia() {
		return getBigDecimal("aetiology_anemia");
	}

	public void setAetiologyAnemia(BigDecimal aetiologyanemia) {
		setBigDecimal("aetiology_anemia", aetiologyanemia);
	}
	
	public void setHospitalAccStr(String hospitalAccStr) {
		setString("hospital_acc_str", hospitalAccStr);
	}

	public String getHospitalAccStr() {
		return getString("hospital_acc_str");
	}
	
	public BigDecimal getAccompanyingExaFlag() {
		return getBigDecimal("accompanying_exa_flag");
	}

	public void setAccompanyingExaFlag(BigDecimal accompanyingExaFlag) {
		setBigDecimal("accompanying_exa_flag", accompanyingExaFlag);
	}
	@Override
	public String toString() {
		return "PenoticePO [getCustomerName()=" + getCustomerName()
				+ ", getDocListId()=" + getDocListId() + ", getUwId()="
				+ getUwId() + ", getQualityEvaluation()="
				+ getQualityEvaluation() + ", getPenoticeId()="
				+ getPenoticeId() + ", getPeReason()=" + getPeReason()
				+ ", getResultMagnumMsg()=" + getResultMagnumMsg()
				+ ", getCustomerId()=" + getCustomerId() + ", getResultAge()="
				+ getResultAge() + ", getApplyCode()=" + getApplyCode()
				+ ", getRoleType()=" + getRoleType() + ", getResultCusScore()="
				+ getResultCusScore() + ", getPolicyId()=" + getPolicyId()
				+ ", getCustomerBirth()=" + getCustomerBirth()
				+ ", getOtherComments()=" + getOtherComments()
				+ ", getPositiveIndi()=" + getPositiveIndi()
				+ ", getResultPeDate()=" + getResultPeDate()
				+ ", getUwHospitalId()=" + getUwHospitalId()
				+ ", getResultTel()=" + getResultTel()
				+ ", getResultMagnumValue()=" + getResultMagnumValue()
				+ ", getQualityComment()=" + getQualityComment()
				+ ", getResultComment()=" + getResultComment()
				+ ", getPolicyCode()=" + getPolicyCode() + ", getGenderCode()="
				+ getGenderCode() + ", getEsIndi()=" + getEsIndi()
				+ ", getIsChangeFlag()=" + getIsChangeFlag() + ", getStatus()="
				+ getStatus() + ", getIsPhysical()=" + getIsPhysical()
				+ ", getPeFee()=" + getPeFee() + ", getUserName()="
				+ getUserName() + ", getNewDocumentNo()=" + getNewDocumentNo()
				+ ", getResultPeStartDate()=" + getResultPeStartDate()
				+ ", getResultPeEndDate()=" + getResultPeEndDate()
				+ ", getOrganCode()=" + getOrganCode() + ", getOrganName()="
				+ getOrganName() + ", getPhysicalTotal()=" + getPhysicalTotal()
				+ ", getPositiveTotal()=" + getPositiveTotal()
				+ ", getCustomerIdStd()=" + getCustomerIdStd()
				+ ", getUwSource()=" + getUwSource() + ", getSendTime()="
				+ getSendTime() + ", getPrintTime()=" + getPrintTime()
				+ ", getCloseTime()=" + getCloseTime() + ", getHospitalName()="
				+ getHospitalName() + ", getDocumentNo()=" + getDocumentNo()
				+ "]";
	}

}
