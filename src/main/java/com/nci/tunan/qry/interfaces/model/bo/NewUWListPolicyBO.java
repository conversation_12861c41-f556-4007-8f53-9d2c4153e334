package com.nci.tunan.qry.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseVO;

public class NewUWListPolicyBO extends BaseVO {
	
	private static final long serialVersionUID = 1L;
	private String organCode;// 页面所需
	private String startDate;// 起期
	private String endDate;// 止期
	private String organName;
	// 导出所需字段
	private String fgsOrganCode;// 分公司代码
	private String fgsOrganName;// 分公司名称
	private String zhgsOrganCode;// 支公司代码
	private String zhgsOrganName;// 支公司名称
	private String salesOrganCode;// 销售部公司代码
	private String salesOrganName;// 销售部公司名称
	private String applyCode;// 投保单号码
	private String feeStatus;// 是否到账
	private String applyDate;// 投保日期
	private String busiProdCode;// 险种代码
	private String busiProdName;// 险种名称
	private String amount;// 险种保额
	private String totalPremAf;// 险种保费 总保费
	private String chargeYear;// 缴费期间
	private String decisionCode;// 险种层核保结论
	private String policyDecision;// 保单层核保结论
	private String uwIndi;// 保单流向
	private String uwOverIndi;// 核保流向 -- 超权限上报
	private String uwEscaIndi;// 核保流向 -- 疑难案件上报
	private String decisionDesc;// 拒保、延期原因
	private String refusalReason;// 拒保原因
	private String delayReason;// 延期原因
	private String specialReason;// 特约内容
	private String emValue;// 加费评点
	private String authoritySuper;// 评点超权限上报原因
	private String difficultReporting;// 疑难案例上报原因
	private String uwUserId;// 核保员代码 T_UW_POLICY
	private String uwDecision;// 原因说明 -- 核保意见

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getDecisionDesc() {
		return decisionDesc;
	}

	public void setDecisionDesc(String decisionDesc) {
		this.decisionDesc = decisionDesc;
	}

	public String getRefusalReason() {
		return refusalReason;
	}

	public void setRefusalReason(String refusalReason) {
		this.refusalReason = refusalReason;
	}

	public String getDelayReason() {
		return delayReason;
	}

	public void setDelayReason(String delayReason) {
		this.delayReason = delayReason;
	}

	public String getSpecialReason() {
		return specialReason;
	}

	public void setSpecialReason(String specialReason) {
		this.specialReason = specialReason;
	}

	public String getEmValue() {
		return emValue;
	}

	public void setEmValue(String emValue) {
		this.emValue = emValue;
	}

	public String getAuthoritySuper() {
		return authoritySuper;
	}

	public void setAuthoritySuper(String authoritySuper) {
		this.authoritySuper = authoritySuper;
	}

	public String getDifficultReporting() {
		return difficultReporting;
	}

	public void setDifficultReporting(String difficultReporting) {
		this.difficultReporting = difficultReporting;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getFgsOrganCode() {
		return fgsOrganCode;
	}

	public void setFgsOrganCode(String fgsOrganCode) {
		this.fgsOrganCode = fgsOrganCode;
	}

	public String getFgsOrganName() {
		return fgsOrganName;
	}

	public void setFgsOrganName(String fgsOrganName) {
		this.fgsOrganName = fgsOrganName;
	}

	public String getZhgsOrganCode() {
		return zhgsOrganCode;
	}

	public void setZhgsOrganCode(String zhgsOrganCode) {
		this.zhgsOrganCode = zhgsOrganCode;
	}

	public String getZhgsOrganName() {
		return zhgsOrganName;
	}

	public void setZhgsOrganName(String zhgsOrganName) {
		this.zhgsOrganName = zhgsOrganName;
	}

	public String getSalesOrganCode() {
		return salesOrganCode;
	}

	public void setSalesOrganCode(String salesOrganCode) {
		this.salesOrganCode = salesOrganCode;
	}

	public String getSalesOrganName() {
		return salesOrganName;
	}

	public void setSalesOrganName(String salesOrganName) {
		this.salesOrganName = salesOrganName;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getFeeStatus() {
		return feeStatus;
	}

	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}

	public String getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public String getBusiProdName() {
		return busiProdName;
	}

	public void setBusiProdName(String busiProdName) {
		this.busiProdName = busiProdName;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getTotalPremAf() {
		return totalPremAf;
	}

	public void setTotalPremAf(String totalPremAf) {
		this.totalPremAf = totalPremAf;
	}

	public String getChargeYear() {
		return chargeYear;
	}

	public void setChargeYear(String chargeYear) {
		this.chargeYear = chargeYear;
	}

	public String getDecisionCode() {
		return decisionCode;
	}

	public void setDecisionCode(String decisionCode) {
		this.decisionCode = decisionCode;
	}

	public String getPolicyDecision() {
		return policyDecision;
	}

	public void setPolicyDecision(String policyDecision) {
		this.policyDecision = policyDecision;
	}

	public String getUwIndi() {
		return uwIndi;
	}

	public void setUwIndi(String uwIndi) {
		this.uwIndi = uwIndi;
	}

	public String getUwOverIndi() {
		return uwOverIndi;
	}

	public void setUwOverIndi(String uwOverIndi) {
		this.uwOverIndi = uwOverIndi;
	}

	public String getUwEscaIndi() {
		return uwEscaIndi;
	}

	public void setUwEscaIndi(String uwEscaIndi) {
		this.uwEscaIndi = uwEscaIndi;
	}

	public String getUwUserId() {
		return uwUserId;
	}

	public void setUwUserId(String uwUserId) {
		this.uwUserId = uwUserId;
	}

	public String getUwDecision() {
		return uwDecision;
	}

	public void setUwDecision(String uwDecision) {
		this.uwDecision = uwDecision;
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String toString() {
		return "NewUWListPolicyBO [organCode=" + organCode + ", startDate="
				+ startDate + ", endDate=" + endDate + ", organName="
				+ organName + ", fgsOrganCode=" + fgsOrganCode
				+ ", fgsOrganName=" + fgsOrganName + ", zhgsOrganCode="
				+ zhgsOrganCode + ", zhgsOrganName=" + zhgsOrganName
				+ ", salesOrganCode=" + salesOrganCode + ", salesOrganName="
				+ salesOrganName + ", applyCode=" + applyCode + ", feeStatus="
				+ feeStatus + ", applyDate=" + applyDate + ", busiProdCode="
				+ busiProdCode + ", busiProdName=" + busiProdName + ", amount="
				+ amount + ", totalPremAf=" + totalPremAf + ", chargeYear="
				+ chargeYear + ", decisionCode=" + decisionCode
				+ ", policyDecision=" + policyDecision + ", uwIndi=" + uwIndi
				+ ", uwOverIndi=" + uwOverIndi + ", uwEscaIndi=" + uwEscaIndi
				+ ", decisionDesc=" + decisionDesc + ", refusalReason="
				+ refusalReason + ", delayReason=" + delayReason
				+ ", specialReason=" + specialReason + ", emValue=" + emValue
				+ ", authoritySuper=" + authoritySuper
				+ ", difficultReporting=" + difficultReporting + ", uwUserId="
				+ uwUserId + ", uwDecision=" + uwDecision + "]";
	}
}
