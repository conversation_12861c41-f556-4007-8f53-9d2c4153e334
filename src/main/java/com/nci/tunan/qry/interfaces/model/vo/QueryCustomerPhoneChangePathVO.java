package com.nci.tunan.qry.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;
/**
 * 
 * @description 客户电话信息变更VO对象
 * <AUTHOR> <EMAIL> 
 * @date 2019-11-4 上午9:57:22 
 * @.belongToModule 综合查询-VO对象
 */
public class QueryCustomerPhoneChangePathVO extends BaseVO {
	/**
	 * 序列
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 保单号
	 */
	private String policyCode; 
	/**
	 *  客户号
	 */
	private BigDecimal customerId; 
	/**
	 * 姓名
	 */
	private String customerName; 
	/**
	 * 变更前电话
	 */
	private String telBefor; 
	/**
	 * 变更后电话
	 */
	private String telAfter; 
	/**
	 * 变更时间
	 */
	private Date updateTime; 
	/**
	 * 纸质影像资料存储路径
	 */
	private String imgPath; 
	
	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getTelBefor() {
		return telBefor;
	}

	public void setTelBefor(String telBefor) {
		this.telBefor = telBefor;
	}

	public String getTelAfter() {
		return telAfter;
	}

	public void setTelAfter(String telAfter) {
		this.telAfter = telAfter;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getImgPath() {
		return imgPath;
	}

	public void setImgPath(String imgPath) {
		this.imgPath = imgPath;
	}

	@Override
	public String getBizId() {
		return null;
	}

}
