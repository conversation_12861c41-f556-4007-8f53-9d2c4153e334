package com.nci.tunan.qry.interfaces.model.po;

import com.nci.udmp.framework.model.*;
import java.lang.String;
import java.util.Date;

/**
 * @description AddInvestPO对象
 * <AUTHOR>
 * @date 2015-05-28 16:43:45
 */
public class QryPolicyHolderInfoPO extends BasePO {
	// 保单号
	private String policyCode;
	// 客户id，主键，序列
	private String customerId;
	// 客户统一编号
	private String unCustomerCode;
	// 老核心客户ID
	private String oldCustomerId;
	// 结婚日期
	private String marriageDate;
	// 学历
	private String education;
	// 客户名称
	private String customerName;
	// 客户出生日期
	private Date customerBirthday;
	// 客户性别，关联到性别字典表
	private String customerGender;
	// 客户身高
	private String customerHeight;
	// 客户体重
	private String customerWeight;
	// 客户证件类型 关联到证件类型表
	private String customerCertType;
	// 客户证件号码
	private String customerCertiCode;
	// 客户身份证号码
	private String customerIdCode;
	// 客户证件有效期起期
	private String custCertStarDate;
	// 客户证件有效期止期
	private String custCertEndDate;
	// 职业代码，关联到职业代码表
	private String jobCode;
	// 行业
	private String jobNature;
	// 职业所属行业性质
	private String jobKind;
	// 职务
	private String jobTitle;
	// 婚姻状态,关联到婚姻状态表
	private String marriageStatus;
	// 子女状况(有、无) --是否有无子女
	private String isParent;
	// 年收入(万元)
	private String annualIncome;
	// 国籍 关联到国家代码
	private String countryCode;
	// 宗教
	private String religionCode;
	// 民族 关联到民族代码
	private String nationCode;
	// 驾照类型 关联到驾照类型
	private String driverLicenseType;
	// 工作单位
	private String companyName;
	// 联系电话
	private String offenUseTel;
	// 住宅电话
	private String houseTel;
	// 传真电话
	private String faxTel;
	// 办公电话
	private String officeTel;
	// 移动电话
	private String mobileTel;
	// 电子邮箱
	private String email;
	// QQ号码
	private String qq;
	// 微信号码
	private String wechatNo;
	// 其他联系方式
	private String other;
	// "客户等级
	private String customerLevel;
	// 反洗钱客户风险等级
	private String customerRiskLevel;
	// 是否VIP
	private String customerVip;
	// 是否吸烟
	private String smokingFlag;
	// 是否饮酒
	private String drunkFlag;
	// 黑名单客户标志
	private String blacklistFlag;
	// 是否家庭主妇
	private String housekeeperFlag;
	// 是否已经和MDM同步过数据
	private String synMdmFlag;
	// 生存状态
	private String liveStatus;
	// 是否退休
	private String retiredFlag;
	// 死亡日期
	private String deathDate;
	// 健康状况 关联到健康状况字典表
	private String healthStatus;
	// 客户品质描述信息
	private String remark;
	// 客户层密码
	private String custPwd;
	// 续期缴费银行帐号
	private String nextAccount;
	// 续期缴费账户持有人姓名
	private String nextAccountName;
	// 续期缴费账户银号开户银行
	private String nextAccountBank;
	// 续期缴费银行转帐账户ID
	private String nextAccountId;
	// 续期交费方式
	private String payNext;
	// 投保人性别名称
	private String genderDesc;
	// 客户级别名称
	private String typeName;
	// 婚姻状况名称
	private String marriage;
	// 投保人国籍名称
	private String countryName;
	// 投保人证件类型名称
	private String type;
	// 驾照类型名称
	private String licenseDesc;
	// 职业名称
	private String categoryJobDesc;
	// 职业类别名称
	private String kindJobDesc;
	// 续期交费方式名称
	private String name;
	// 通讯地址
	private String address;
	// 邮编
	private String postCode;
	// 省代码
	private String state;
	// 市代码
	private String city;
	// 区县代码
	private String district;
	// 省名称
	private String statename;
	// 市名称
	private String cityname;
	// 区县名称
	private String districtname;

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public String getUnCustomerCode() {
		return unCustomerCode;
	}

	public void setUnCustomerCode(String unCustomerCode) {
		this.unCustomerCode = unCustomerCode;
	}

	public String getOldCustomerId() {
		return oldCustomerId;
	}

	public void setOldCustomerId(String oldCustomerId) {
		this.oldCustomerId = oldCustomerId;
	}

	public String getMarriageDate() {
		return marriageDate;
	}

	public void setMarriageDate(String marriageDate) {
		this.marriageDate = marriageDate;
	}

	public String getEducation() {
		return education;
	}

	public void setEducation(String education) {
		this.education = education;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public Date getCustomerBirthday() {
		return customerBirthday;
	}

	public void setCustomerBirthday(Date customerBirthday) {
		this.customerBirthday = customerBirthday;
	}

	public String getCustomerGender() {
		return customerGender;
	}

	public void setCustomerGender(String customerGender) {
		this.customerGender = customerGender;
	}

	public String getCustomerHeight() {
		return customerHeight;
	}

	public void setCustomerHeight(String customerHeight) {
		this.customerHeight = customerHeight;
	}

	public String getCustomerWeight() {
		return customerWeight;
	}

	public void setCustomerWeight(String customerWeight) {
		this.customerWeight = customerWeight;
	}

	public String getCustomerCertType() {
		return customerCertType;
	}

	public void setCustomerCertType(String customerCertType) {
		this.customerCertType = customerCertType;
	}

	public String getCustomerCertiCode() {
		return customerCertiCode;
	}

	public void setCustomerCertiCode(String customerCertiCode) {
		this.customerCertiCode = customerCertiCode;
	}

	public String getCustomerIdCode() {
		return customerIdCode;
	}

	public void setCustomerIdCode(String customerIdCode) {
		this.customerIdCode = customerIdCode;
	}

	public String getCustCertStarDate() {
		return custCertStarDate;
	}

	public void setCustCertStarDate(String custCertStarDate) {
		this.custCertStarDate = custCertStarDate;
	}

	public String getCustCertEndDate() {
		return custCertEndDate;
	}

	public void setCustCertEndDate(String custCertEndDate) {
		this.custCertEndDate = custCertEndDate;
	}

	public String getJobCode() {
		return jobCode;
	}

	public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}

	public String getJobNature() {
		return jobNature;
	}

	public void setJobNature(String jobNature) {
		this.jobNature = jobNature;
	}

	public String getJobKind() {
		return jobKind;
	}

	public void setJobKind(String jobKind) {
		this.jobKind = jobKind;
	}

	public String getJobTitle() {
		return jobTitle;
	}

	public void setJobTitle(String jobTitle) {
		this.jobTitle = jobTitle;
	}

	public String getMarriageStatus() {
		return marriageStatus;
	}

	public void setMarriageStatus(String marriageStatus) {
		this.marriageStatus = marriageStatus;
	}

	public String getIsParent() {
		return isParent;
	}

	public void setIsParent(String isParent) {
		this.isParent = isParent;
	}

	public String getAnnualIncome() {
		return annualIncome;
	}

	public void setAnnualIncome(String annualIncome) {
		this.annualIncome = annualIncome;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getReligionCode() {
		return religionCode;
	}

	public void setReligionCode(String religionCode) {
		this.religionCode = religionCode;
	}

	public String getNationCode() {
		return nationCode;
	}

	public void setNationCode(String nationCode) {
		this.nationCode = nationCode;
	}

	public String getDriverLicenseType() {
		return driverLicenseType;
	}

	public void setDriverLicenseType(String driverLicenseType) {
		this.driverLicenseType = driverLicenseType;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getOffenUseTel() {
		return offenUseTel;
	}

	public void setOffenUseTel(String offenUseTel) {
		this.offenUseTel = offenUseTel;
	}

	public String getHouseTel() {
		return houseTel;
	}

	public void setHouseTel(String houseTel) {
		this.houseTel = houseTel;
	}

	public String getFaxTel() {
		return faxTel;
	}

	public void setFaxTel(String faxTel) {
		this.faxTel = faxTel;
	}

	public String getOfficeTel() {
		return officeTel;
	}

	public void setOfficeTel(String officeTel) {
		this.officeTel = officeTel;
	}

	public String getMobileTel() {
		return mobileTel;
	}

	public void setMobileTel(String mobileTel) {
		this.mobileTel = mobileTel;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public String getWechatNo() {
		return wechatNo;
	}

	public void setWechatNo(String wechatNo) {
		this.wechatNo = wechatNo;
	}

	public String getOther() {
		return other;
	}

	public void setOther(String other) {
		this.other = other;
	}

	public String getCustomerLevel() {
		return customerLevel;
	}

	public void setCustomerLevel(String customerLevel) {
		this.customerLevel = customerLevel;
	}

	public String getCustomerRiskLevel() {
		return customerRiskLevel;
	}

	public void setCustomerRiskLevel(String customerRiskLevel) {
		this.customerRiskLevel = customerRiskLevel;
	}

	public String getCustomerVip() {
		return customerVip;
	}

	public void setCustomerVip(String customerVip) {
		this.customerVip = customerVip;
	}

	public String getSmokingFlag() {
		return smokingFlag;
	}

	public void setSmokingFlag(String smokingFlag) {
		this.smokingFlag = smokingFlag;
	}

	public String getDrunkFlag() {
		return drunkFlag;
	}

	public void setDrunkFlag(String drunkFlag) {
		this.drunkFlag = drunkFlag;
	}

	public String getBlacklistFlag() {
		return blacklistFlag;
	}

	public void setBlacklistFlag(String blacklistFlag) {
		this.blacklistFlag = blacklistFlag;
	}

	public String getHousekeeperFlag() {
		return housekeeperFlag;
	}

	public void setHousekeeperFlag(String housekeeperFlag) {
		this.housekeeperFlag = housekeeperFlag;
	}

	public String getSynMdmFlag() {
		return synMdmFlag;
	}

	public void setSynMdmFlag(String synMdmFlag) {
		this.synMdmFlag = synMdmFlag;
	}

	public String getLiveStatus() {
		return liveStatus;
	}

	public void setLiveStatus(String liveStatus) {
		this.liveStatus = liveStatus;
	}

	public String getRetiredFlag() {
		return retiredFlag;
	}

	public void setRetiredFlag(String retiredFlag) {
		this.retiredFlag = retiredFlag;
	}

	public String getDeathDate() {
		return deathDate;
	}

	public void setDeathDate(String deathDate) {
		this.deathDate = deathDate;
	}

	public String getHealthStatus() {
		return healthStatus;
	}

	public void setHealthStatus(String healthStatus) {
		this.healthStatus = healthStatus;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCustPwd() {
		return custPwd;
	}

	public void setCustPwd(String custPwd) {
		this.custPwd = custPwd;
	}

	public String getNextAccount() {
		return nextAccount;
	}

	public void setNextAccount(String nextAccount) {
		this.nextAccount = nextAccount;
	}

	public String getNextAccountName() {
		return nextAccountName;
	}

	public void setNextAccountName(String nextAccountName) {
		this.nextAccountName = nextAccountName;
	}

	public String getNextAccountBank() {
		return nextAccountBank;
	}

	public void setNextAccountBank(String nextAccountBank) {
		this.nextAccountBank = nextAccountBank;
	}

	public String getNextAccountId() {
		return nextAccountId;
	}

	public void setNextAccountId(String nextAccountId) {
		this.nextAccountId = nextAccountId;
	}

	public String getPayNext() {
		return payNext;
	}

	public void setPayNext(String payNext) {
		this.payNext = payNext;
	}

	public String getGenderDesc() {
		return genderDesc;
	}

	public void setGenderDesc(String genderDesc) {
		this.genderDesc = genderDesc;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getMarriage() {
		return marriage;
	}

	public void setMarriage(String marriage) {
		this.marriage = marriage;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getLicenseDesc() {
		return licenseDesc;
	}

	public void setLicenseDesc(String licenseDesc) {
		this.licenseDesc = licenseDesc;
	}

	public String getCategoryJobDesc() {
		return categoryJobDesc;
	}

	public void setCategoryJobDesc(String categoryJobDesc) {
		this.categoryJobDesc = categoryJobDesc;
	}

	public String getKindJobDesc() {
		return kindJobDesc;
	}

	public void setKindJobDesc(String kindJobDesc) {
		this.kindJobDesc = kindJobDesc;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getDistrict() {
		return district;
	}

	public void setDistrict(String district) {
		this.district = district;
	}

	public String getStatename() {
		return statename;
	}

	public void setStatename(String statename) {
		this.statename = statename;
	}

	public String getCityname() {
		return cityname;
	}

	public void setCityname(String cityname) {
		this.cityname = cityname;
	}

	public String getDistrictname() {
		return districtname;
	}

	public void setDistrictname(String districtname) {
		this.districtname = districtname;
	}

	@Override
	public String toString() {
		return "PolicyHolderInfoPO [" 
				+ "policyCode=" + getPolicyCode() + ","
				+ "customerId=" + getCustomerId() + ","
				+ "unCustomerCode=" + getUnCustomerCode() + ","
				+ "oldCustomerId=" + getOldCustomerId() + ","
				+ "marriageDate=" + getMarriageDate() + ","
				+ "education=" + getEducation() + ","
				+ "customerName=" + getCustomerName() + ","
				+ "customerBirthday=" + getCustomerBirthday() + ","
				+ "customerGender=" + getCustomerGender() + ","
				+ "customerHeight=" + getCustomerHeight() + ","
				+ "customerWeight=" + getCustomerWeight() + ","
				+ "customerCertType=" + getCustomerCertType() + ","
				+ "customerCertiCode=" + getCustomerCertiCode() + ","
				+ "customerIdCode=" + getCustomerIdCode() + ","
				+ "custCertStarDate=" + getCustCertStarDate() + ","
				+ "custCertEndDate=" + getCustCertEndDate() + ","
				+ "jobCode=" + getJobCode() + ","
				+ "jobNature=" + getJobNature() + ","
				+ "jobKind=" + getJobKind() + ","
				+ "jobTitle=" + getJobTitle() + ","
				+ "marriageStatus=" + getMarriageStatus() + ","
				+ "isParent=" + getIsParent() + ","
				+ "annualIncome=" + getAnnualIncome() + ","
				+ "countryCode=" + getCountryCode() + ","
				+ "religionCode=" + getReligionCode() + ","
				+ "nationCode=" + getNationCode() + ","
				+ "driverLicenseType=" + getDriverLicenseType() + ","
				+ "companyName=" + getCompanyName() + ","
				+ "offenUseTel=" + getOffenUseTel() + ","
				+ "houseTel=" + getHouseTel() + ","
				+ "faxTel=" + getFaxTel() + ","
				+ "officeTel=" + getOfficeTel() + ","
				+ "mobileTel=" + getMobileTel() + ","
				+ "email=" + getEmail() + ","
				+ "qq=" + getQq() + ","
				+ "wechatNo=" + getWechatNo() + ","
				+ "other=" + getOther() + ","
				+ "customerLevel=" + getCustomerLevel() + ","
				+ "customerRiskLevel=" + getCustomerRiskLevel() + ","
				+ "customerVip=" + getCustomerVip() + ","
				+ "smokingFlag=" + getSmokingFlag() + ","
				+ "drunkFlag=" + getDrunkFlag() + ","
				+ "blacklistFlag=" + getBlacklistFlag() + ","
				+ "housekeeperFlag=" + getHousekeeperFlag() + ","
				+ "synMdmFlag=" + getSynMdmFlag() + ","
				+ "liveStatus=" + getLiveStatus() + ","
				+ "retiredFlag=" + getRetiredFlag() + ","
				+ "deathDate=" + getDeathDate() + ","
				+ "healthStatus=" + getHealthStatus() + ","
				+ "remark=" + getRemark() + ","
				+ "custPwd=" + getCustPwd() + ","
				+ "nextAccount =" + getNextAccount () + ","
				+ "nextAccountName=" + getNextAccountName() + ","
				+ "nextAccountBank=" + getNextAccountBank() + ","
				+ "nextAccountId=" + getNextAccountId() + ","
				+ "payNext=" + getPayNext() + ","
				+ "genderDesc=" + getGenderDesc() + ","
				+ "typeName=" + getTypeName() + ","
				+ "marriage=" + getMarriage() + ","
				+ "countryName=" + getCountryName() + ","
				+ "type=" + getType() + ","
				+ "licenseDesc=" + getLicenseDesc() + ","
				+ "categoryJobDesc=" + getCategoryJobDesc() + ","
				+ "kindJobDesc=" + getKindJobDesc() + ","
				+ "name=" + getName() + ","
				+ "address=" + getAddress() + ","
				+ "postCode=" + getPostCode() + ","
				+ "state=" + getState() + ","
				+ "city=" + getCity() + ","
				+ "district =" + getDistrict () + ","
				+ "statename=" + getStatename() + ","
				+ "cityname=" + getCityname() + ","
				+ "districtname=" + getDistrictname() + "]";
	}
}
