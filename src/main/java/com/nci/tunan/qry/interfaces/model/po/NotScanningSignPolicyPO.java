package com.nci.tunan.qry.interfaces.model.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/**
 * @description AddressPO对象
 * <AUTHOR>
 * @date 2014-12-11 10:11:40
 */
public class NotScanningSignPolicyPO extends BasePO {
	/**
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
	 */
	private static final long serialVersionUID = 1L;

	/** 属性 --- java类型 --- oracle类型_数据长度_小数位长度_注释信息 */

	// private String organCode ;//管理机构
	// private String organName;//管理机构名称
	// private String applyCode;//投保单号
	// private String policyCode;//保单号
	// private String holderName;//投保人
	// private String insuredName;//被保险人
	// private String agentCode;//业务员代码
	// private String agentName;//业务员名称
	// private String salesChannelCode;//销售渠道
	// private String salesChannelName;//销售渠道名字
	// private Date applyDate;//保单录入日期
	// private String liabilityState//保单状态
	/**
	 * rm146055 共同参保保单
	 */
    public String getJointlyInsuredPolicy(){
        return getString("jointly_insured_policy");
    }
    /**
     * rm146055 共同参保保单
     */
    public void setJointlyInsuredPolicy(String jointlyInsuredPolicy){
        setString("jointly_insured_policy",jointlyInsuredPolicy);
    }
	public void setLiabilityState(String liabilityState) {
		setString("liability_state", liabilityState);
	}

	public String getLiabilityState() {
		return getString("liability_state");
	}

	public void setSubmitChannel(BigDecimal submitChannel) {
		setBigDecimal("submit_channel", submitChannel);
	}

	public BigDecimal getSubmitChannel() {
		return getBigDecimal("submit_channel");
	}

	public void setSubmitChannelName(String submitChannelName) {
		setString("submit_channel_name", submitChannelName);
	}

	public String getSubmitChannelName() {
		return getString("submit_channel_name");
	}

	public void setSubinputTypeCode(String subinputTypeCode) {
		setString("subinput_type_code", subinputTypeCode);
	}

	public String getSubinputTypeCode() {
		return getString("subinput_type_code");
	}

	public void setSubinputTypeDesc(String subinputTypeDesc) {
		setString("subinput_type_desc", subinputTypeDesc);
	}

	public String getSubinputTypeDesc() {
		return getString("subinput_type_desc");
	}

	public void setBankCode(String bankCode) {
		setString("bank_code", bankCode);
	}

	public String getBankCode() {
		return getString("bank_code");
	}

	public void setBankName(String bankName) {
		setString("bank_name", bankName);
	}

	public String getBankName() {
		return getString("bank_name");
	}

	public void setBankBranchCode(String bankBranchCode) {
		setString("bank_branch_code", bankBranchCode);
	}

	public String getBankBranchCode() {
		return getString("bank_branch_code");
	}

	public void setBankBranchName(String bankBranchName) {
		setString("bank_branch_name", bankBranchName);
	}

	public String getBankBranchName() {
		return getString("bank_branch_name");
	}

	public void setBranchOrganCode(String branchOrganCode) {
		setString("branch_organ_code", branchOrganCode);
	}

	public String getBranchOrganCode() {
		return getString("branch_organ_code");
	}

	public void setBranchOrganName(String branchOrganName) {
		setString("branch_organ_name", branchOrganName);
	}

	public String getBranchOrganName() {
		return getString("branch_organ_name");
	}

	public void setOrganName(String organName) {
		setString("organ_name", organName);
	}

	public String getOrganName() {
		return getString("organ_name");
	}

	public void setOrganCode(String organCode) {
		setString("organ_code", organCode);
	}

	public String getOrganCode() {
		return getString("organ_code");
	}

	public void setApplyCode(String applyCode) {
		setString("apply_code", applyCode);
	}

	public String getApplyCode() {
		return getString("apply_code");
	}

	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}

	public String getPolicyCode() {
		return getString("policy_code");
	}

	public void setHolderName(String holderName) {
		setString("holder_name", holderName);
	}

	public String getHolderName() {
		return getString("holder_name");
	}

	public void setInsuredName(String insuredName) {
		setString("insured_name", insuredName);
	}

	public String getInsuredName() {
		return getString("insured_name");
	}

	public void setAgentCode(String agentCode) {
		setString("agent_code", agentCode);
	}

	public String getAgentCode() {
		return getString("agent_code");
	}

	public void setAgentName(String agentName) {
		setString("agent_name", agentName);
	}

	public String getAgentName() {
		return getString("agent_name");
	}

	public void setSalesChannelCode(String salesChannelCode) {
		setString("sales_channel_code", salesChannelCode);
	}

	public String getSalesChannelCode() {
		return getString("sales_channel_code");
	}

	public void setSalesChannelName(String salesChannelName) {
		setString("sales_channel_name", salesChannelName);
	}

	public String getSalesChannelName() {
		return getString("sales_channel_name");
	}

	public void setApplyDate(Date applyDate) {
		setUtilDate("apply_date", applyDate);
	}

	public Date getApplyDate() {
		return getUtilDate("apply_date");
	}

	public void setFinishTime(Date finishTime) {
		setUtilDate("finish_time", finishTime);
	}

	public Date getFinishTime() {
		return getUtilDate("finish_time");
	}

	public void setValidateDate(Date validateDate) {
		setUtilDate("validate_date", validateDate);
	}

	public Date getValidateDate() {
		return getUtilDate("validate_date");
	}

	public void setTotalPremAf(BigDecimal totalPremAf) {
		setBigDecimal("total_prem_af", totalPremAf);
	}

	public BigDecimal getTotalPremAf() {
		return getBigDecimal("total_prem_af");
	}

	// private Date finishTime;//保单收费日期
	// private Date validateDate;//签单日期
	// private String totalPremAf;//保费
	// private String adminOrganCode;//营销服务部代码
	// private String adminOrganName;//营销服务部名字
	// private String salesOrganName;//营业部名字
	// private String salesOrganCode;//营业部代码
	// private String startDate;
	// private String endDate;

	public void setAdminOrganCode(String adminOrganCode) {
		setString("admin_organ_code", adminOrganCode);
	}

	public String getAdminOrganCode() {
		return getString("admin_organ_code");
	}

	public void setAdminOrganName(String adminOrganName) {
		setString("admin_organ_name", adminOrganName);
	}

	public String getAdminOrganName() {
		return getString("admin_organ_name");
	}

	public void setSalesOrganName(String salesOrganName) {
		setString("sales_organ_name", salesOrganName);
	}

	public String getSalesOrganName() {
		return getString("sales_organ_name");
	}

	// private Date finishTime;//保单收费日期
	// private Date validateDate;//签单日期
	// private String totalPremAf;//保费
	// private String adminOrganCode;//营销服务部代码
	// private String adminOrganName;//营销服务部名字
	// private String salesOrganName;//营业部名字
	// private String salesOrganCode;//营业部代码
	// private String startDate;
	// private String endDate;
	public void setSalesOrganCode(String salesOrganCode) {
		setString("sales_organ_code", salesOrganCode);
	}

	public String getSalesOrganCode() {
		return getString("sales_organ_code");
	}

	public void setStartDate(String startDate) {
		setString("start_date", startDate);
	}

	public String getStartDate() {
		return getString("start_date");
	}

	public void setEndDate(String endDate) {
		setString("end_date", endDate);
	}

	public String getEndDate() {
		return getString("end_date");
	}

	public void setAgentLevel(String agentLevel) {
		setString("agent_level", agentLevel);
	}

	public String getAgentLevel() {
		return getString("agent_level");
	}

	public void setChannelType(String channelType) {
		setString("channel_type", channelType);
	}

	public String getChannelType() {
		return getString("channel_type");
	}

	public void setAgentLevelDesc(String agentLevelDesc) {
		setString("agent_level_desc", agentLevelDesc);
	}

	public String getAgentLevelDesc() {
		return getString("agent_level_desc");
	}

	public void setServiceBank(String serviceBank) {
		setString("service_bank", serviceBank);
	}

	public String getServiceBank() {
		return getString("service_bank");
	}

	public void setChargeYear(BigDecimal chargeYear) {
		setBigDecimal("charge_year", chargeYear);
	}

	public BigDecimal getChargeYear() {
		return getBigDecimal("charge_year");
	}

	public void setSubinputType(String subinputType) {
		setString("subinput_type", subinputType);
	}

	public String getSubinputType() {
		return getString("subinput_type");
	}

	public String getSalesOrganNameQ() {
		return getString("sales_organ_name_q");
	}

	public void setSalesOrganNameQ(String salesOrganNameQ) {
		setString("sales_organ_name_q", salesOrganNameQ);
	}

	public String getSalesOrganNameB() {
		return getString("sales_organ_name_b");
	}

	public void setSalesOrganNameB(String salesOrganNameB) {
		setString("sales_organ_name_b", salesOrganNameB);
	}

	public String getSalesOrganNameZ() {
		return getString("sales_organ_name_z");
	}

	public void setSalesOrganNameZ(String salesOrganNameZ) {
		setString("sales_organ_name_z", salesOrganNameZ);
	}

	public String getServiceHandler() {
		return getString("service_handler");
	}

	public void setServiceHandler(String serviceHandler) {
		setString("service_handler", serviceHandler);
	}

	public String getServiceHandlerName() {
		return getString("service_handler_name");
	}

	public void setServiceHandlerName(String serviceHandlerName) {
		setString("service_handler_name", serviceHandlerName);
	}

	public void setIsMoreThan20(BigDecimal isMoreThan20) {
		setBigDecimal("isMoreThan20", isMoreThan20);
	}

	public BigDecimal getIsMoreThan20() {
		return getBigDecimal("isMoreThan20");
	}

	public void setSumPremArap(BigDecimal sumPremArap) {
		setBigDecimal("sum_prem_arap", sumPremArap);
	}

	public BigDecimal getSumPremArap() {
		return getBigDecimal("sum_prem_arap");
	}

	/**
	 * 是否达到反洗钱标准
	 */
	public void setIsAML(BigDecimal isAML) {
		setBigDecimal("is_aml", isAML);
	}

	/**
	 * 是否达到反洗钱标准
	 */
	public BigDecimal getIsAML() {
		return getBigDecimal("is_aml");
	}
	
	
	// 110855 反洗钱标识
	public BigDecimal getAmlFlag(){
		return getBigDecimal("aml_flag");
	}
	public void setAmlFlag(BigDecimal amlFlag){
				setBigDecimal("aml_flag",amlFlag);
	}
			

	@Override
	public String toString() {
		return "NotScanningSignPolicyPO [getLiabilityState()="
				+ getLiabilityState() + ", getSubmitChannel()="
				+ getSubmitChannel() + ", getSubmitChannelName()="
				+ getSubmitChannelName() + ", getSubinputTypeCode()="
				+ getSubinputTypeCode() + ", getSubinputTypeDesc()="
				+ ", getAmlFlag()=" + getAmlFlag()
				+ getSubinputTypeDesc() + ", getBankCode()=" + getBankCode()
				+ ", getBankName()=" + getBankName() + ", getBankBranchCode()="
				+ getBankBranchCode() + ", getBankBranchName()="
				+ getBankBranchName() + ", getBranchOrganCode()="
				+ getBranchOrganCode() + ", getBranchOrganName()="
				+ getBranchOrganName() + ", getOrganName()=" + getOrganName()
				+ ", getOrganCode()=" + getOrganCode() + ", getApplyCode()="
				+ getApplyCode() + ", getPolicyCode()=" + getPolicyCode()
				+ ", getHolderName()=" + getHolderName()
				+ ", getInsuredName()=" + getInsuredName()
				+ ", getAgentCode()=" + getAgentCode() + ", getAgentName()="
				+ getAgentName() + ", getSalesChannelCode()="
				+ getSalesChannelCode() + ", getSalesChannelName()="
				+ getSalesChannelName() + ", getApplyDate()=" + getApplyDate()
				+ ", getFinishTime()=" + getFinishTime()
				+ ", getValidateDate()=" + getValidateDate()
				+ ", getTotalPremAf()=" + getTotalPremAf()
				+ ", getAdminOrganCode()=" + getAdminOrganCode()
				+ ", getAdminOrganName()=" + getAdminOrganName()
				+ ", getSalesOrganName()=" + getSalesOrganName()
				+ ", getSalesOrganCode()=" + getSalesOrganCode()
				+ ", getStartDate()=" + getStartDate() + ", getEndDate()="
				+ getEndDate() + ", getAgentLevel()=" + getAgentLevel()
				+ ", getChannelType()=" + getChannelType()
				+ ", getAgentLevelDesc()=" + getAgentLevelDesc()
				+ ", getServiceBank()=" + getServiceBank()
				+ ", getChargeYear()=" + getChargeYear()
				+ ", getSubinputType()=" + getSubinputType()
				+ ", getSalesOrganNameQ()=" + getSalesOrganNameQ()
				+ ", getSalesOrganNameB()=" + getSalesOrganNameB()
				+ ", getSalesOrganNameZ()=" + getSalesOrganNameZ()
				+ ", getServiceHandler()=" + getServiceHandler()
				+ ", getServiceHandlerName()=" + getServiceHandlerName()
				+ ", getIsMoreThan20()=" + getIsMoreThan20()
				+ ", getSumPremArap()=" + getSumPremArap() + "]";
	}

}
