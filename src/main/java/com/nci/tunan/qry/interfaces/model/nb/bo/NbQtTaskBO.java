package com.nci.tunan.qry.interfaces.model.nb.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description NbQtTaskVO对象
 * <AUTHOR>
 * @date 2015-01-14 17:29:21
 */
public class NbQtTaskBO extends BaseBO {
	/**
	 * @Fields bpoComp : null
	 */
	private BigDecimal bpoComp;
	/**
	 * @Fields qtSelectDate : null
	 */
	private Date qtSelectDate;
	/**
	 * @Fields qtUser : null
	 */
	private String qtUser;
	/**
	 * @Fields entryType : null
	 */
	private String entryType;
	/**
	 * @Fields createTime : null
	 */
	private Date createTime;
	/**
	 * @Fields custLevel : null
	 */
	private String custLevel;
	/**
	 * @Fields productCode : null
	 */
	private String productCode;
	/**
	 * @Fields taskId : null
	 */
	private BigDecimal taskId;
	/**
	 * @Fields qtTime : null
	 */
	private Date qtTime;
	/**
	 * @Fields qaType : null
	 */
	private String qaType;
	/**
	 * @Fields applyCode : null
	 */
	private String applyCode;
	/**
	 * @Fields qtResult : null
	 */
	private String qtResult;
	/**
	 * @Fields channelType : null
	 */
	private String channelType;
	/**
	 * @Fields qtComments : null
	 */
	private String qtComments;
	/**
	 * @Fields agentLevel : null
	 */
	private String agentLevel;
	/**
	 * @Fields policyId : null
	 */
	private BigDecimal policyId;
	/**
	 * @Fields qtErrorCount : null
	 */
	private BigDecimal qtErrorCount;
	/**
	 * @Fields qtHolderIndi : null
	 */
	private BigDecimal qtHolderIndi;
	/**
	 * @Fields qtInsuredIndi : null
	 */
	private BigDecimal qtInsuredIndi;
	/**
	 * @Fields qtVerifyIndi : null
	 */
	private BigDecimal qtVerifyIndi;
	/**
	 * @Fields qtErrorRate : null
	 */
	private BigDecimal fieldErrorRate;
	/**
	 * @Fields qtTooltip : null
	 */
	private String qtTooltip;
	/**
	 * @Fields qtElementTotal : null
	 */
	private BigDecimal qtElementTotal;
	/**
	 * @Fields bankBranchCode : null
	 */
	private String bankBranchCode;
	/**
	 * @Fields cardCode : null
	 */
	private String cardCode;
	/**
	 * @Fields qtCriteriaId : null
	 */
	private BigDecimal qtCriteriaId;
	/**
	 * @Fields policyCode : null
	 */
	private String policyCode;
	/**
	 * @Fields standIndi : null
	 */
	private String standIndi;
	/**
	 * @Fields batchId : null
	 */
	private BigDecimal batchId;
	/**
	 * @Fields bankCode : null
	 */
	private String bankCode;	
	private String bankName;
	private String bankBranchName;
	private String isQualityAgent;//是否绩优业务员
	private String qualityAgentLevel;//业务员绩优等级
	private Date insertTime;
	private String proposalStatus; //投保单状态
	private Date audioInfoDate; //双录影像回传日期
	private String audioInfoSize; //双录影像回传文件大小
	private String documentNo;//通知书号
	private BigDecimal docuFlag;
	private String batchNo;//上传批次号,双录影音的上传的批次号
	private String agentNo;//业务员编号
	private String belongsUser;//所属人员
	private String policyType;
	private String signDateString;//String 类型的签单日期
	private List<String> qtApplyCodeList;
	private List<String> uwApplyCodeList;
	private String queryTaskType;
	private BigDecimal versionNo;//版本号
	private BigDecimal isSelfInsured;//自保件
	private BigDecimal totalNum;
	private BigDecimal completeNum;
	private BigDecimal completeRate;
	private String frontAfterType;
	private BigDecimal agentNum;//绩优业务员质检任务数
	private BigDecimal completeAgent;//绩优业务员已质检任务数
	private BigDecimal agentRate;//绩优业务员质检完成率
	private BigDecimal qtTotalCount;//该投保单实际录入的数据的个数 
	private String billErrorPercent;//收单品质差错率
	private String startDate;//起始日期
	private String endDate;//终止日期
	private String organCode;//管理机构
	private String organName;//机构名称
	private String typeName;//质检任务类型
	private BigDecimal task;//总件数
	private BigDecimal qualified;//通过
	private BigDecimal unqualified;//不通过
	private BigDecimal quali;//通过率
	private String statusName;//质检状态
	private String agentCode;//业务员编号
	private String userName;//业务员姓名
	private String agentName;//业务员姓名
	private String customerName;//投保人
	private Date issueDate;//签单日期
	private Date auditDate;//审核日期
	private Date reviewDate;//复核日期
	private BigDecimal timeStatus;//时效状态
	private String populationTimeStatus;//总体处理时效
    private String remark;//质检不通过原因
    private String customerLevel;//客户等级
	private BigDecimal isArt;//是否人工复检0-否  1-是
	private BigDecimal isImportDqr;//是否导入质检结论0-否 1-是
	private BigDecimal operatorCode;//操作员代码
	private Date operatorDate;//操作日期
	private boolean isOmnipotent = false;//是否万能险标记
	private String isDoubleRepet;//是否复检任务
	private String applyResult;//修正申请结论
	private String ctResult;//修正复核结论
	private String inputType;//出单方式编码
	private String queryWay; //查询方式
	private String salesChannelName;
	private String productNameStd;
	private BigDecimal totalPremAf;
	private BigDecimal chargeYear;
	private String statusDesc;
	private String productCategory1;
	private String subinputType;
	private Date updateTime;
	
	private String chargeYear1;//为使用趸交的1000显示设置
	/** 质检不通过原因码 */
	private String resionCode;
	
	public String getResionCode() {
		return resionCode;
	}
	public void setResionCode(String resionCode) {
		this.resionCode = resionCode;
	}
	public String getChargeYear1() {
		return chargeYear1;
	}
	public void setChargeYear1(String chargeYear1) {
		this.chargeYear1 = chargeYear1;
	}
	public String getQueryWay() {
		return queryWay;
	}
	public void setQueryWay(String queryWay) {
		this.queryWay = queryWay;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getSubinputType() {
		return subinputType;
	}
	public void setSubinputType(String subinputType) {
		this.subinputType = subinputType;
	}
	
	
	
	
	
	
	
	public String getSalesChannelName() {
		return salesChannelName;
	}
	public void setSalesChannelName(String salesChannelName) {
		this.salesChannelName = salesChannelName;
	}
	public String getProductNameStd() {
		return productNameStd;
	}
	public void setProductNameStd(String productNameStd) {
		this.productNameStd = productNameStd;
	}
	public BigDecimal getTotalPremAf() {
		return totalPremAf;
	}
	public void setTotalPremAf(BigDecimal totalPremAf) {
		this.totalPremAf = totalPremAf;
	}
	public BigDecimal getChargeYear() {
		return chargeYear;
	}
	public void setChargeYear(BigDecimal chargeYear) {
		this.chargeYear = chargeYear;
	}
	public String getStatusDesc() {
		return statusDesc;
	}
	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}
	public String getProductCategory1() {
		return productCategory1;
	}
	public void setProductCategory1(String productCategory1) {
		this.productCategory1 = productCategory1;
	}
	public String getInputType() {
		return inputType;
	}
	public void setInputType(String inputType) {
		this.inputType = inputType;
	}

	/**
	 * @Fields orgCode : null
	 */
	/**
	 * @Fields qtOrgCode : null
	 */
	private String qtOrgCode;
	/**
	 * @Fields qtStatus : null
	 */
	private BigDecimal qtStatus;

	private String bpmTaskId;

	private Date signDate;
	
	private BigDecimal riskDefectIndi;
	private String acode;
	private String uname;
	private String qtStatusIn;
	private Date startDt;// 开始日期
	private Date endDt;// 终止日期
	private String operator;// 当前数据的受理人
	private String feeStatus; // 收费状态
	private Date applyDate; // 投保日期
	private Date applyDateS;
	private Date applyDateE;
	private Date applyDateDateType; // 投保日期
	private String levelDesc; // 客户等级
	private String holderName;// 投保人姓名
	private String insuredName; // 被保人姓名
	private Date acknowledgeDate; // 回执签收日期
	private String manualUw;//人工核保类型
	private BigDecimal drqId;
	private String noticeStatus;//通知状态
	private String taskIdList;
	private BigDecimal doubleMainriskFlag;
	private String signSource;//标记回执签署来源，使用码表T_SIGN_SOURCE
	
	private String liabilityState;//保单效力状态
	
	
	public String getLiabilityState() {
		return liabilityState;
	}
	public void setLiabilityState(String liabilityState) {
		this.liabilityState = liabilityState;
	}
	
	
	
	public String getApplyResult() {
		return applyResult;
	}
	public void setApplyResult(String applyResult) {
		this.applyResult = applyResult;
	}

	public String getCtResult() {
		return ctResult;
	}
	public void setCtResult(String ctResult) {
		this.ctResult = ctResult;
	}

	public String getSignSource() {
		return signSource;
	}

	public void setSignSource(String signSource) {
		this.signSource = signSource;
	}

	public String getIsDoubleRepet() {
		return isDoubleRepet;
	}

	public void setIsDoubleRepet(String isDoubleRepet) {
		this.isDoubleRepet = isDoubleRepet;
	}

	public BigDecimal getDoubleMainriskFlag() {
		return doubleMainriskFlag;
	}

	public void setDoubleMainriskFlag(BigDecimal doubleMainriskFlag) {
		this.doubleMainriskFlag = doubleMainriskFlag;
	}

	public boolean isOmnipotent() {
		return isOmnipotent;
	}

	public void setOmnipotent(boolean isOmnipotent) {
		this.isOmnipotent = isOmnipotent;
	}

	public String getNoticeStatus() {
		return noticeStatus;
	}

	public void setNoticeStatus(String noticeStatus) {
		this.noticeStatus = noticeStatus;
	}

	public Date getOperatorDate() {
		return operatorDate;
	}

	public void setOperatorDate(Date operatorDate) {
		this.operatorDate = operatorDate;
	}

	public BigDecimal getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(BigDecimal operatorCode) {
		this.operatorCode = operatorCode;
	}

	public BigDecimal getIsImportDqr() {
		return isImportDqr;
	}

	public void setIsImportDqr(BigDecimal isImportDqr) {
		this.isImportDqr = isImportDqr;
	}

	public BigDecimal getDrqId() {
		return drqId;
	}

	public void setDrqId(BigDecimal drqId) {
		this.drqId = drqId;
	}

	public BigDecimal getIsSelfInsured() {
		return isSelfInsured;
	}

	public void setIsSelfInsured(BigDecimal isSelfInsured) {
		this.isSelfInsured = isSelfInsured;
	}

	public BigDecimal getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(BigDecimal versionNo) {
		this.versionNo = versionNo;
	}

	public String getSignDateString() {
		return signDateString;
	}

	public void setSignDateString(String signDateString) {
		this.signDateString = signDateString;
	}

	public String getPolicyType() {
		return policyType;
	}

	public void setPolicyType(String policyType) {
		this.policyType = policyType;
	}

	public String getBelongsUser() {
		return belongsUser;
	}

	public void setBelongsUser(String belongsUser) {
		this.belongsUser = belongsUser;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getDocumentNo() {
		return documentNo;
	}

	public void setDocumentNo(String documentNo) {
		this.documentNo = documentNo;
	}

	public BigDecimal getDocuFlag() {
		return docuFlag;
	}

	public void setDocuFlag(BigDecimal docuFlag) {
		this.docuFlag = docuFlag;
	}

	public String getProposalStatus() {
		return proposalStatus;
	}

	public void setProposalStatus(String proposalStatus) {
		this.proposalStatus = proposalStatus;
	}

	public Date getAudioInfoDate() {
		return audioInfoDate;
	}

	public void setAudioInfoDate(Date audioInfoDate) {
		this.audioInfoDate = audioInfoDate;
	}

	public String getAudioInfoSize() {
		return audioInfoSize;
	}

	public void setAudioInfoSize(String audioInfoSize) {
		this.audioInfoSize = audioInfoSize;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public BigDecimal getTask() {
		return task;
	}

	public void setTask(BigDecimal task) {
		this.task = task;
	}

	public BigDecimal getQualified() {
		return qualified;
	}

	public void setQualified(BigDecimal qualified) {
		this.qualified = qualified;
	}


	public BigDecimal getUnqualified() {
		return unqualified;
	}

	public void setUnqualified(BigDecimal unqualified) {
		this.unqualified = unqualified;
	}
	public BigDecimal getIsArt() {
		return isArt;
	}

	public void setIsArt(BigDecimal isArt) {
		this.isArt = isArt;
	}

	public String getCustomerLevel() {
		return customerLevel;
	}

	public void setCustomerLevel(String customerLevel) {
		this.customerLevel = customerLevel;
	}

	public String getPopulationTimeStatus() {
		return populationTimeStatus;
	}

	public void setPopulationTimeStatus(String populationTimeStatus) {
		this.populationTimeStatus = populationTimeStatus;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
    public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}


	public Date getIssueDate() {
		return issueDate;
	}

	public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}

	public Date getAuditDate() {
		return auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	public Date getReviewDate() {
		return reviewDate;
	}

	public void setReviewDate(Date reviewDate) {
		this.reviewDate = reviewDate;
	}


	

	public BigDecimal getTimeStatus() {
		return timeStatus;
	}

	public void setTimeStatus(BigDecimal timeStatus) {
		this.timeStatus = timeStatus;
	}

	public String getAcode() {
		return acode;
	}

	public void setAcode(String acode) {
		this.acode = acode;
	}

	public String getUname() {
		return uname;
	}

	public void setUname(String uname) {
		this.uname = uname;
	}
	
	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public BigDecimal getQuali() {
		return quali;
	}

	public void setQuali(BigDecimal quali) {
		this.quali = quali;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getIsQualityAgent() {
		return isQualityAgent;
	}

	public void setIsQualityAgent(String isQualityAgent) {
		this.isQualityAgent = isQualityAgent;
	}

	public String getQualityAgentLevel() {
		return qualityAgentLevel;
	}

	public void setQualityAgentLevel(String qualityAgentLevel) {
		this.qualityAgentLevel = qualityAgentLevel;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankBranchName() {
		return bankBranchName;
	}

	public void setBankBranchName(String bankBranchName) {
		this.bankBranchName = bankBranchName;
	}
	
	
	public String getQtStatusIn() {
        return qtStatusIn;
    }

    public void setQtStatusIn(String qtStatusIn) {
        this.qtStatusIn = qtStatusIn;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public BigDecimal getRiskDefectIndi() {
		return riskDefectIndi;
	}

	public void setRiskDefectIndi(BigDecimal riskDefectIndi) {
		this.riskDefectIndi = riskDefectIndi;
	}
	
	public String getBpmTaskId() {
		return bpmTaskId;
	}

	public void setBpmTaskId(String bpmTaskId) {
		this.bpmTaskId = bpmTaskId;
	}
	
	public String getManualUw() {
		return manualUw;
	}

	public void setManualUw(String manualUw) {
		this.manualUw = manualUw;
	}

	public Date getApplyDateDateType() {
        return applyDateDateType;
    }

    public void setApplyDateDateType(Date applyDateDateType) {
        this.applyDateDateType = applyDateDateType;
    }

    public Date getAcknowledgeDate() {
        return acknowledgeDate;
    }

    public void setAcknowledgeDate(Date acknowledgeDate) {
        this.acknowledgeDate = acknowledgeDate;
    }

    public String getHolderName() {
		return holderName;
	}

	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}

	public String getInsuredName() {
		return insuredName;
	}

	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public String getLevelDesc() {
		return levelDesc;
	}

	public void setLevelDesc(String levelDesc) {
		this.levelDesc = levelDesc;
	}

	public String getFeeStatus() {
		return feeStatus;
	}

	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}

	public void setBpoComp(BigDecimal bpoComp) {
		this.bpoComp = bpoComp;
	}

	public BigDecimal getBpoComp() {
		return bpoComp;
	}

	public Date getStartDt() {
		return startDt;
	}

	public void setStartDt(Date startDt) {
		this.startDt = startDt;
	}

	public Date getEndDt() {
		return endDt;
	}

	public void setEndDt(Date endDt) {
		this.endDt = endDt;
	}

	public void setQtSelectDate(Date qtSelectDate) {
		this.qtSelectDate = qtSelectDate;
	}

	public Date getQtSelectDate() {
		return qtSelectDate;
	}

	public void setQtUser(String qtUser) {
		this.qtUser = qtUser;
	}

	public String getQtUser() {
		return qtUser;
	}

	public void setEntryType(String entryType) {
		this.entryType = entryType;
	}

	public String getEntryType() {
		return entryType;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCustLevel(String custLevel) {
		this.custLevel = custLevel;
	}

	public String getCustLevel() {
		return custLevel;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setTaskId(BigDecimal taskId) {
		this.taskId = taskId;
	}

	public BigDecimal getTaskId() {
		return taskId;
	}

	public void setQtTime(Date qtTime) {
		this.qtTime = qtTime;
	}

	public Date getQtTime() {
		return qtTime;
	}

	public void setQaType(String qaType) {
		this.qaType = qaType;
	}

	public String getQaType() {
		return qaType;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setQtResult(String qtResult) {
		this.qtResult = qtResult;
	}

	public String getQtResult() {
		return qtResult;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setQtComments(String qtComments) {
		this.qtComments = qtComments;
	}

	public String getQtComments() {
		return qtComments;
	}

	public String getAgentLevel() {
		return agentLevel;
	}

	public void setAgentLevel(String agentLevel) {
		this.agentLevel = agentLevel;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setQtErrorCount(BigDecimal qtErrorCount) {
		this.qtErrorCount = qtErrorCount;
	}

	public BigDecimal getQtErrorCount() {
		return qtErrorCount;
	}

	public void setQtHolderIndi(BigDecimal qtHolderIndi) {
		this.qtHolderIndi = qtHolderIndi;
	}

	public BigDecimal getQtHolderIndi() {
		return qtHolderIndi;
	}

	public void setQtInsuredIndi(BigDecimal qtInsuredIndi) {
		this.qtInsuredIndi = qtInsuredIndi;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public BigDecimal getQtInsuredIndi() {
		return qtInsuredIndi;
	}

	public void setQtVerifyIndi(BigDecimal qtVerifyIndi) {
		this.qtVerifyIndi = qtVerifyIndi;
	}

	public BigDecimal getQtVerifyIndi() {
		return qtVerifyIndi;
	}

	public BigDecimal getFieldErrorRate() {
		return fieldErrorRate;
	}

	public void setFieldErrorRate(BigDecimal fieldErrorRate) {
		this.fieldErrorRate = fieldErrorRate;
	}

	public void setQtTooltip(String qtTooltip) {
		this.qtTooltip = qtTooltip;
	}

	public String getQtTooltip() {
		return qtTooltip;
	}

	public void setQtElementTotal(BigDecimal qtElementTotal) {
		this.qtElementTotal = qtElementTotal;
	}

	public BigDecimal getQtElementTotal() {
		return qtElementTotal;
	}

	public void setBankBranchCode(String bankBranchCode) {
		this.bankBranchCode = bankBranchCode;
	}

	public String getBankBranchCode() {
		return bankBranchCode;
	}

	public void setCardCode(String cardCode) {
		this.cardCode = cardCode;
	}

	public String getCardCode() {
		return cardCode;
	}

	public void setQtCriteriaId(BigDecimal qtCriteriaId) {
		this.qtCriteriaId = qtCriteriaId;
	}

	public BigDecimal getQtCriteriaId() {
		return qtCriteriaId;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setStandIndi(String standIndi) {
		this.standIndi = standIndi;
	}

	public String getStandIndi() {
		return standIndi;
	}

	public void setBatchId(BigDecimal batchId) {
		this.batchId = batchId;
	}

	public BigDecimal getBatchId() {
		return batchId;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setQtOrgCode(String qtOrgCode) {
		this.qtOrgCode = qtOrgCode;
	}

	public String getQtOrgCode() {
		return qtOrgCode;
	}

	public BigDecimal getQtStatus() {
		return qtStatus;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public void setQtStatus(BigDecimal qtStatus) {
		this.qtStatus = qtStatus;
	}

	
	public String getQueryTaskType() {
		return queryTaskType;
	}

	public void setQueryTaskType(String queryTaskType) {
		this.queryTaskType = queryTaskType;
	}

	@Override
	public String getBizId() {
		return null;
	}


	public List<String> getQtApplyCodeList() {
		return qtApplyCodeList;
	}

	public void setQtApplyCodeList(List<String> qtApplyCodeList) {
		this.qtApplyCodeList = qtApplyCodeList;
	}

	public List<String> getUwApplyCodeList() {
		return uwApplyCodeList;
	}

	public void setUwApplyCodeList(List<String> uwApplyCodeList) {
		this.uwApplyCodeList = uwApplyCodeList;
	}
	
	public BigDecimal getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(BigDecimal totalNum) {
		this.totalNum = totalNum;
	}

	public BigDecimal getCompleteNum() {
		return completeNum;
	}

	public void setCompleteNum(BigDecimal completeNum) {
		this.completeNum = completeNum;
	}

	public BigDecimal getCompleteRate() {
		return completeRate;
	}

	public void setCompleteRate(BigDecimal completeRate) {
		this.completeRate = completeRate;
	}

	public String getFrontAfterType() {
		return frontAfterType;
	}

	public void setFrontAfterType(String frontAfterType) {
		this.frontAfterType = frontAfterType;
	}

	public BigDecimal getAgentNum() {
		return agentNum;
	}

	public void setAgentNum(BigDecimal agentNum) {
		this.agentNum = agentNum;
	}

	public BigDecimal getCompleteAgent() {
		return completeAgent;
	}

	public void setCompleteAgent(BigDecimal completeAgent) {
		this.completeAgent = completeAgent;
	}

	public BigDecimal getAgentRate() {
		return agentRate;
	}

	public void setAgentRate(BigDecimal agentRate) {
		this.agentRate = agentRate;
	}

	public BigDecimal getQtTotalCount() {
		return qtTotalCount;
	}

	public void setQtTotalCount(BigDecimal qtTotalCount) {
		this.qtTotalCount = qtTotalCount;
	}

	public String getBillErrorPercent() {
		return billErrorPercent;
	}

	public void setBillErrorPercent(String billErrorPercent) {
		this.billErrorPercent = billErrorPercent;
	}

	public Date getApplyDateS() {
		return applyDateS;
	}

	public void setApplyDateS(Date applyDateS) {
		this.applyDateS = applyDateS;
	}

	public Date getApplyDateE() {
		return applyDateE;
	}

	public void setApplyDateE(Date applyDateE) {
		this.applyDateE = applyDateE;
	}

	//taskIdlist #需求变更#35812增加，接收申请已撤销的任务ID
	public String getTaskIdList() {
		return taskIdList;
	}
	//taskIdlist #需求变更#35812增加，接收申请已撤销的任务ID
	public void setTaskIdList(String taskIdList) {
		this.taskIdList = taskIdList;
	}

	@Override
	public String toString() {
		return "NbQtTaskBO [bpoComp=" + bpoComp + ", qtSelectDate="
				+ qtSelectDate + ", qtUser=" + qtUser + ", entryType="
				+ entryType + ", createTime=" + createTime + ", custLevel="
				+ custLevel + ", productCode=" + productCode + ", taskId="
				+ taskId + ", qtTime=" + qtTime + ", qaType=" + qaType
				+ ", applyCode=" + applyCode + ", qtResult=" + qtResult
				+ ", channelType=" + channelType + ", qtComments=" + qtComments
				+ ", agentLevel=" + agentLevel + ", policyId=" + policyId
				+ ", qtErrorCount=" + qtErrorCount + ", qtHolderIndi="
				+ qtHolderIndi + ", qtInsuredIndi=" + qtInsuredIndi
				+ ", qtVerifyIndi=" + qtVerifyIndi + ", fieldErrorRate="
				+ fieldErrorRate + ", qtTooltip=" + qtTooltip
				+ ", qtElementTotal=" + qtElementTotal + ", bankBranchCode="
				+ bankBranchCode + ", cardCode=" + cardCode + ", qtCriteriaId="
				+ qtCriteriaId + ", policyCode=" + policyCode + ", standIndi="
				+ standIndi + ", batchId=" + batchId + ", bankCode=" + bankCode
				+ ", bankName=" + bankName + ", bankBranchName="
				+ bankBranchName + ", isQualityAgent=" + isQualityAgent
				+ ", qualityAgentLevel=" + qualityAgentLevel + ", insertTime="
				+ insertTime + ", proposalStatus=" + proposalStatus
				+ ", audioInfoDate=" + audioInfoDate + ", audioInfoSize="
				+ audioInfoSize + ", documentNo=" + documentNo + ", docuFlag="
				+ docuFlag + ", batchNo=" + batchNo + ", agentNo=" + agentNo
				+ ", belongsUser=" + belongsUser + ", policyType=" + policyType
				+ ", signDateString=" + signDateString + ", qtApplyCodeList="
				+ qtApplyCodeList + ", uwApplyCodeList=" + uwApplyCodeList
				+ ", queryTaskType=" + queryTaskType + ", versionNo="
				+ versionNo + ", isSelfInsured=" + isSelfInsured
				+ ", totalNum=" + totalNum + ", completeNum=" + completeNum
				+ ", completeRate=" + completeRate + ", frontAfterType="
				+ frontAfterType + ", agentNum=" + agentNum
				+ ", completeAgent=" + completeAgent + ", agentRate="
				+ agentRate + ", qtTotalCount=" + qtTotalCount
				+ ", billErrorPercent=" + billErrorPercent + ", startDate="
				+ startDate + ", endDate=" + endDate + ", organCode="
				+ organCode + ", organName=" + organName + ", typeName="
				+ typeName + ", task=" + task + ", qualified=" + qualified
				+ ", unqualified=" + unqualified + ", quali=" + quali
				+ ", statusName=" + statusName + ", agentCode=" + agentCode
				+ ", userName=" + userName + ", agentName=" + agentName
				+ ", applyResult=" + applyResult + ", ctResult=" + ctResult
				+ ", customerName=" + customerName + ", issueDate=" + issueDate
				+ ", auditDate=" + auditDate + ", reviewDate=" + reviewDate
				+ ", timeStatus=" + timeStatus + ", populationTimeStatus="
				+ populationTimeStatus + ", remark=" + remark
				+ ", customerLevel=" + customerLevel + ", isArt=" + isArt
				+ ", isImportDqr=" + isImportDqr + ", operatorCode="
				+ operatorCode + ", operatorDate=" + operatorDate
				+ ", isOmnipotent=" + isOmnipotent + ", isDoubleRepet="
				+ isDoubleRepet + ", qtOrgCode=" + qtOrgCode + ", qtStatus="
				+ qtStatus + ", bpmTaskId=" + bpmTaskId + ", signDate="
				+ signDate + ", riskDefectIndi=" + riskDefectIndi + ", acode="
				+ acode + ", uname=" + uname + ", qtStatusIn=" + qtStatusIn
				+ ", startDt=" + startDt + ", endDt=" + endDt + ", operator="
				+ operator + ", feeStatus=" + feeStatus + ", applyDate="
				+ applyDate + ", applyDateS=" + applyDateS + ", applyDateE="
				+ applyDateE + ", applyDateDateType=" + applyDateDateType
				+ ", levelDesc=" + levelDesc + ", holderName=" + holderName
				+ ", insuredName=" + insuredName + ", acknowledgeDate="
				+ acknowledgeDate + ", manualUw=" + manualUw + ", drqId="
				+ drqId + ", noticeStatus=" + noticeStatus + ", taskIdList="
				+ taskIdList + ", doubleMainriskFlag=" + doubleMainriskFlag
				+ ", signSource=" + signSource + "]";
	}


	

	
	
	
}
