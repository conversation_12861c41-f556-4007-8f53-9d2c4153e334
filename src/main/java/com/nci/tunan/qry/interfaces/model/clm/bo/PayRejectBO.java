package com.nci.tunan.qry.interfaces.model.clm.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class PayRejectBO extends BaseBO {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/** 
     * @Fields organCode :  机构代码
     */ 
     private String organCode;
     /**
      * @Fields organName: 机构名称
      */
     private String organName;
     /** 
      * @Fields accidentNo :  事故号 
      */ 
     private String accidentNo;
     
     /** 
      * @Fields caseNo :  赔案号
      */ 
     private String caseNo;
     /** 
      * @Fields policyCode :  保单号 
      */ 
     private String policyCode;
     /**
      * @Fields caseFlagName:案件类型
      */
     private String caseFlagName;
     /** 
      * @Fields busiProdCode :  险种号码
      */ 
     private String busiProdCode;
     /**
      * @Fields busiName: 险种简称
      */
     private String busiName;
     /**
      * @Fields claimName:理赔类型
      */
     private String claimName;
     /** 
      * @Fields liabId :  保项代码
      */ 
     private  BigDecimal liabId;
     /** 
      * @Fields rn:  行号
      */ 
     private  BigDecimal rn;	
     /** 
      * @Fields liabId :  保项代码
      */ 
     private  String liabCode;
     /**
      * @Fields liabName: 保项名称
      */
     private String liabName;
     /** 
      * @Fields customerName :  出险人
      */ 
     private String customerName;
     /**
      * @Fields mobileTel:出险人联系电话
      */
     private String mobileTel;
     /**
      * @Fields age: 年龄
      */
     private BigDecimal age;
     /**
      * @Fields gender:出险人性别
      */
     private String gender;
     /** 
      * @Fields validdateDate : 保单生效日
      */ 
     private Date validdateDate;
     /** 
      * @Fields accDate :  事故日期
      */ 
     private Date accDate;
     /** 
      * @Fields claimDate :  出险日期
      */ 
     private Date claimDate;
     /** 
      * @Fields treatStart :  住院日期
      */ 
     private Date treatStart;
     /** 
      * @Fields accDesc :  事故描述 
      */ 
     private String accDesc;
     /** 
      * @Fields rptrName :  报案人姓名
      */ 
     private String rptrName;
     /** 
      * @Fields rptrMp :  报案人联系方式
      */ 
     private String rptrMp;
     /** 
      * @Fields rptrTime :  报案日期
      */ 
     private Date rptrTime;
     /** 
      * @Fields signTime :  签收时间
      */ 
     private Date signTime;
     /** 
      * @Fields signerId :  签收人id
      */ 
     private BigDecimal signerId;
     /**
      * @Fields signerName:签收人
      */
     private String signerName;
     /**
      * @Fields registeTime:立案日期
      */
     private Date registeTime;
     /**
      * @Fields accReasonDesc:出险原因代码
      */
     private String accReasonDesc;
     /**
      * @Fields registerName:立案人
      */
     private String registerName;
     /**
      * @Fields auditorName:审核人
      */
     private String auditorName;
     /**
      * @Fields auditDecision:审核结论
      */
     private String auditDecision;
     /** 
      * @Fields approverId :  审批人 
      */ 
     private String approverName;
     /** 
      * @Fields approveTime :  审批时间 
      */ 
     private Date approveTime;
     /** 
      * @Fields trusteeName :  受托人
      */ 
     private String trusteeName;
     /** 
      * @Fields trusteeMp :  受托人联系电话
      */ 
     private String trusteeMp;
     /**
      * @Fields trusteeOrganName:受托人区部组
      */
     private String trusteeOrganName;
     /** 
      * @Fields liabConclusion :  保项结论
      */ 
     private String liabConclusion;
     /** 
      * @Fields caseFlag :  简易案件标识
      */ 
     private String caseFlag;
     
     /**
      * @Fields :账单金额
      */
     private BigDecimal sumAmount;
     
     //TODO 账单金额
     /**
      * @Fields deductibleFranchise:责任外金额（免赔额）
      */
     private BigDecimal deductAmount;
     //TODO 服务员代码
     private String agentName;
     //TODO 服务员区部组
     private String agentOrganCode;
     
     private String agentOrganCodeStr;
     
     /**
      * IT问题件单号
      */
     private String itProblemNo; 
     /**
      * 是否IT问题单 
      */
     private String itProblemCase;
     
     public String getAgentOrganCodeStr() {
		return agentOrganCodeStr;
	}

	public void setAgentOrganCodeStr(String agentOrganCodeStr) {
		this.agentOrganCodeStr = agentOrganCodeStr;
	}
	/**
      * @Feilds adjustPay:应付金额
      */
     private BigDecimal adjustPay;
     /** 
      * @Fields rejectPay :  拒付金额
      */ 
     private BigDecimal rejectPay;
     /** 
      * @Fields rejectReason :  拒付原因
      */ 
     private String rejectReason;
     /**
      * 扣费
      */
     private String detainPay;
     /**
      * 退费
      */
     private String returnPay;
     /**
      * @Fields payAmount:理赔金给付金额
      */
     private BigDecimal payAmount;
     /**
      * @Fields liveStatus:案件标识
      */
     private String liveStatus;
     /**
      * @Fields beneName:领款人
      */
     private String beneName;
     private String surveyFlag;
     private BigDecimal surveyNum;
     private String positiveFlag;
     private String surveyById1Name;
     private String surveyById2Name;
     private String surveyById3Name;
     private Date firstApplyDate;
     private Date lastApplyDate;
     private BigDecimal surveyFee;
     private Date auditTime;
     /** 
      * @Fields endCaseTime :  结案时间
      */ 
     private Date endCaseTime;
     /** 
      * @Fields payMode :  给付方式
      */ 
     private String payMode;
     /** 
      * @Fields cureStatus :  治疗类别
      */ 
     private String cureStatus;
     /** 
      * @Fields hospitalName :  治疗医院
      */ 
     private String hospitalName;
     private String isDesignated;
     /** 
      * @Fields policyType :  业务类别
      */ 
     private String policyType;
     /** 
      * @Fields dueTime :  实付日期
      */ 
     private Date dueTime;
     /** 
      * @Fields accidentDetail :  意外细节代码
      */ 
     private String accidentDetail;
     /** 
      * @Fields addidentDesc :  意外细节
      */ 
     private String addidentDesc;
     /** 
      * @Fields accResult1 :  出险结果1代码
      */ 
     private String accResult1;
     /** 
      * @Fields accName1 :  出险结果1
      */ 
     private String accName1;
     /** 
      * @Fields accResult2 :  出险结果2代码
      */ 
     private String accResult2;
     /** 
      * @Fields accName2 :  出险结果2
      */ 
     private String accName2;
     private String secondOrganCode;
     private String thirdOrganCode;
     private String fourthOrganCode;
     /** 
      * @Fields clmtName :  申请人姓名
      */ 
     private String clmtName;
     /** 
      * @Fields clmtInsurRelation :  申请人与保险人关系
      */ 
     private String clmtInsurRelation;
     /** 
      * @Fields clmtMp :  申请人联系电话
      */ 
     private String clmtMp;
     /** 
      * @Fields resistFlag :  不可抗辩标识
      */ 
     private String resistFlag;
     
     /**
      * 区分调查、二核、其他案件类型
      */
     private String clmFlag;
     /**
      * 二核案件数量
      */
     private BigDecimal uwSum;
     
     /**
      * 总给付金额
      */
     private BigDecimal sumPayAmount;
     /**
      * 总拒付金额
      */
     private BigDecimal sumRejectPay;
     /**
      * 总应付金额
      */
	 private BigDecimal sumAdjustPay;
     /**            
      * ************查询条件字段*****************
      */
     /**统计机构*/
     private String searOrganCode;
 	
 	/**统计机构*/
 	private String searOrganName;
 	
 	/**统计日期*/
 	private String searDate;
 	
 	/**清单类型*/
 	private String searListType;
 	
 	/**统计起期*/
 	private Date searStaBeginDate;
 	
 	/**统计止期*/
 	private Date searStaEndDate;
 	
 	/**申请类型*/
 	private BigDecimal searApplyType; 
 	
 	/**案件类型*/
 	private BigDecimal searCaseFlag;
 	
 	/**理赔类型*/
 	private String searClaimType;
 	
 	/**保项结论类型*/
 	private BigDecimal searLiabConclusion;
 	
 	/**险种代码*/
 	private String searBusiItemId;
 	
 	private BigDecimal userId;
 	
 	/**
 	 * 重疾慰问先赔标识
 	 */
 	private BigDecimal priorityClaim;
 	
 	/**
 	 * 重疾慰问先赔标识名称
 	 */
 	private String priorityClaimName;
 	
 	/**
 	 * @Fields liabDecision :  案件结论
 	 */
 	private String liabDecision;
 	/**
 	 * @Fields auditPermission :  赔案审核权限
 	 */
 	private String auditPermission;
 	/**
 	 * @Fields approvePermission :  赔案审批权限
 	 */
 	private String approvePermission;
 	/**
 	 * @Fields surveyStatus :  是否存在审核阶段调查
 	 */
 	private String surveyStatus;
 	/**
 	 * @Fields initialValidate :  险种原始生效日期
 	 */
 	private Date initialValidate;
 	/**
 	 * @Fields adjustPayAmount :  案值
 	 */
 	private BigDecimal adjustPayAmount;
 	/**
 	 * @Fields auditOrganCode :  审核人机构
 	 */
 	private String auditOrganCode;
 	/**
 	 * @Fields approveOrganCode :  审批人机构
 	 */
 	private String approveOrganCode;
 	/**
 	 * @Fields auditRemark :  审核意见简略
 	 */
 	private String auditRemark;
 	/**
 	 * @Fields auditRemarkDesc :  审核意见详细
 	 */
 	private String auditRemarkDesc;
 	
 	/**
 	 * @Fields approveRemark :  审批意见简略
 	 */
 	private String approveRemark;
 	/**
 	 * @Fields approveRemarkDesc :  审批意见详细
 	 */
 	private String approveRemarkDesc;
 	
    /**
     * 申请渠道
     */
    private String channelCode;
	
	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}
	/**
     * 申请渠道名称
     */
    private String channelName;

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public String getApproveRemark() {
		return approveRemark;
	}

	public void setApproveRemark(String approveRemark) {
		this.approveRemark = approveRemark;
	}

	public String getApproveRemarkDesc() {
		return approveRemarkDesc;
	}

	public void setApproveRemarkDesc(String approveRemarkDesc) {
		this.approveRemarkDesc = approveRemarkDesc;
	}
 	
	public String getLiabDecision() {
		return liabDecision;
	}

	public void setLiabDecision(String liabDecision) {
		this.liabDecision = liabDecision;
	}

	public String getAuditPermission() {
		return auditPermission;
	}

	public void setAuditPermission(String auditPermission) {
		this.auditPermission = auditPermission;
	}

	public String getApprovePermission() {
		return approvePermission;
	}

	public void setApprovePermission(String approvePermission) {
		this.approvePermission = approvePermission;
	}

	public String getSurveyStatus() {
		return surveyStatus;
	}

	public void setSurveyStatus(String surveyStatus) {
		this.surveyStatus = surveyStatus;
	}

	public Date getInitialValidate() {
		return initialValidate;
	}

	public void setInitialValidate(Date initialValidate) {
		this.initialValidate = initialValidate;
	}

	public BigDecimal getAdjustPayAmount() {
		return adjustPayAmount;
	}

	public void setAdjustPayAmount(BigDecimal adjustPayAmount) {
		this.adjustPayAmount = adjustPayAmount;
	}

	public String getAuditOrganCode() {
		return auditOrganCode;
	}

	public void setAuditOrganCode(String auditOrganCode) {
		this.auditOrganCode = auditOrganCode;
	}

	public String getApproveOrganCode() {
		return approveOrganCode;
	}

	public void setApproveOrganCode(String approveOrganCode) {
		this.approveOrganCode = approveOrganCode;
	}

	public String getAuditRemark() {
		return auditRemark;
	}

	public void setAuditRemark(String auditRemark) {
		this.auditRemark = auditRemark;
	}

	public String getAuditRemarkDesc() {
		return auditRemarkDesc;
	}

	public void setAuditRemarkDesc(String auditRemarkDesc) {
		this.auditRemarkDesc = auditRemarkDesc;
	}

	public String getPriorityClaimName() {
		return priorityClaimName;
	}

	public void setPriorityClaimName(String priorityClaimName) {
		this.priorityClaimName = priorityClaimName;
	}

	public BigDecimal getPriorityClaim() {
		return priorityClaim;
	}

	public void setPriorityClaim(BigDecimal priorityClaim) {
		this.priorityClaim = priorityClaim;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getAccidentNo() {
		return accidentNo;
	}

	public void setAccidentNo(String accidentNo) {
		this.accidentNo = accidentNo;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getCaseFlagName() {
		return caseFlagName;
	}

	public void setCaseFlagName(String caseFlagName) {
		this.caseFlagName = caseFlagName;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public String getBusiName() {
		return busiName;
	}

	public void setBusiName(String busiName) {
		this.busiName = busiName;
	}

	public String getClaimName() {
		return claimName;
	}

	public void setClaimName(String claimName) {
		this.claimName = claimName;
	}

	public BigDecimal getLiabId() {
		return liabId;
	}

	public void setLiabId(BigDecimal liabId) {
		this.liabId = liabId;
	}

	public String getLiabName() {
		return liabName;
	}

	public void setLiabName(String liabName) {
		this.liabName = liabName;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getMobileTel() {
		return mobileTel;
	}

	public void setMobileTel(String mobileTel) {
		this.mobileTel = mobileTel;
	}

	public BigDecimal getAge() {
		return age;
	}

	public void setAge(BigDecimal age) {
		this.age = age;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public Date getValiddateDate() {
		return validdateDate;
	}

	public void setValiddateDate(Date validdateDate) {
		this.validdateDate = validdateDate;
	}

	public Date getAccDate() {
		return accDate;
	}

	public void setAccDate(Date accDate) {
		this.accDate = accDate;
	}

	public Date getClaimDate() {
		return claimDate;
	}

	public void setClaimDate(Date claimDate) {
		this.claimDate = claimDate;
	}

	public Date getTreatStart() {
		return treatStart;
	}

	public void setTreatStart(Date treatStart) {
		this.treatStart = treatStart;
	}

	public String getAccDesc() {
		return accDesc;
	}

	public void setAccDesc(String accDesc) {
		this.accDesc = accDesc;
	}

	public String getRptrName() {
		return rptrName;
	}

	public void setRptrName(String rptrName) {
		this.rptrName = rptrName;
	}

	public String getRptrMp() {
		return rptrMp;
	}

	public void setRptrMp(String rptrMp) {
		this.rptrMp = rptrMp;
	}

	public Date getRptrTime() {
		return rptrTime;
	}

	public void setRptrTime(Date rptrTime) {
		this.rptrTime = rptrTime;
	}

	public Date getSignTime() {
		return signTime;
	}

	public void setSignTime(Date signTime) {
		this.signTime = signTime;
	}

	public BigDecimal getSignerId() {
		return signerId;
	}

	public void setSignerId(BigDecimal signerId) {
		this.signerId = signerId;
	}

	public String getSignerName() {
		return signerName;
	}

	public void setSignerName(String signerName) {
		this.signerName = signerName;
	}

	public Date getRegisteTime() {
		return registeTime;
	}

	public void setRegisteTime(Date registeTime) {
		this.registeTime = registeTime;
	}

	public String getAccReasonDesc() {
		return accReasonDesc;
	}

	public void setAccReasonDesc(String accReasonDesc) {
		this.accReasonDesc = accReasonDesc;
	}

	public String getRegisterName() {
		return registerName;
	}

	public void setRegisterName(String registerName) {
		this.registerName = registerName;
	}

	public String getAuditorName() {
		return auditorName;
	}

	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}

	public String getAuditDecision() {
		return auditDecision;
	}

	public void setAuditDecision(String auditDecision) {
		this.auditDecision = auditDecision;
	}

	public String getApproverName() {
		return approverName;
	}

	public void setApproverName(String approverName) {
		this.approverName = approverName;
	}

	public Date getApproveTime() {
		return approveTime;
	}

	public void setApproveTime(Date approveTime) {
		this.approveTime = approveTime;
	}

	public String getTrusteeName() {
		return trusteeName;
	}

	public void setTrusteeName(String trusteeName) {
		this.trusteeName = trusteeName;
	}

	public String getTrusteeMp() {
		return trusteeMp;
	}

	public void setTrusteeMp(String trusteeMp) {
		this.trusteeMp = trusteeMp;
	}

	public String getTrusteeOrganName() {
		return trusteeOrganName;
	}

	public void setTrusteeOrganName(String trusteeOrganName) {
		this.trusteeOrganName = trusteeOrganName;
	}

	public String getLiabConclusion() {
		return liabConclusion;
	}

	public void setLiabConclusion(String liabConclusion) {
		this.liabConclusion = liabConclusion;
	}

	public String getCaseFlag() {
		return caseFlag;
	}

	public void setCaseFlag(String caseFlag) {
		this.caseFlag = caseFlag;
	}

	public BigDecimal getSumAmount() {
		return sumAmount;
	}

	public void setSumAmount(BigDecimal sumAmount) {
		this.sumAmount = sumAmount;
	}

	public BigDecimal getDeductAmount() {
		return deductAmount;
	}

	public void setDeductAmount(BigDecimal deductAmount) {
		this.deductAmount = deductAmount;
	}

	public BigDecimal getAdjustPay() {
		return adjustPay;
	}

	public void setAdjustPay(BigDecimal adjustPay) {
		this.adjustPay = adjustPay;
	}

	public BigDecimal getRejectPay() {
		return rejectPay;
	}

	public void setRejectPay(BigDecimal rejectPay) {
		this.rejectPay = rejectPay;
	}

	public String getRejectReason() {
		return rejectReason;
	}

	public void setRejectReason(String rejectReason) {
		this.rejectReason = rejectReason;
	}

	public String getDetainPay() {
		return detainPay;
	}

	public void setDetainPay(String detainPay) {
		this.detainPay = detainPay;
	}

	public String getReturnPay() {
		return returnPay;
	}

	public void setReturnPay(String returnPay) {
		this.returnPay = returnPay;
	}

	public BigDecimal getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public String getLiveStatus() {
		return liveStatus;
	}

	public void setLiveStatus(String liveStatus) {
		this.liveStatus = liveStatus;
	}

	public String getBeneName() {
		return beneName;
	}

	public void setBeneName(String beneName) {
		this.beneName = beneName;
	}

	public String getSurveyFlag() {
		return surveyFlag;
	}

	public void setSurveyFlag(String surveyFlag) {
		this.surveyFlag = surveyFlag;
	}

	public BigDecimal getSurveyNum() {
		return surveyNum;
	}

	public void setSurveyNum(BigDecimal surveyNum) {
		this.surveyNum = surveyNum;
	}

	public String getPositiveFlag() {
		return positiveFlag;
	}

	public void setPositiveFlag(String positiveFlag) {
		this.positiveFlag = positiveFlag;
	}

	public String getSurveyById1Name() {
		return surveyById1Name;
	}

	public void setSurveyById1Name(String surveyById1Name) {
		this.surveyById1Name = surveyById1Name;
	}

	public String getSurveyById2Name() {
		return surveyById2Name;
	}

	public void setSurveyById2Name(String surveyById2Name) {
		this.surveyById2Name = surveyById2Name;
	}

	public String getSurveyById3Name() {
		return surveyById3Name;
	}

	public void setSurveyById3Name(String surveyById3Name) {
		this.surveyById3Name = surveyById3Name;
	}

	public Date getFirstApplyDate() {
		return firstApplyDate;
	}

	public void setFirstApplyDate(Date firstApplyDate) {
		this.firstApplyDate = firstApplyDate;
	}

	public Date getLastApplyDate() {
		return lastApplyDate;
	}

	public void setLastApplyDate(Date lastApplyDate) {
		this.lastApplyDate = lastApplyDate;
	}

	public BigDecimal getSurveyFee() {
		return surveyFee;
	}

	public void setSurveyFee(BigDecimal surveyFee) {
		this.surveyFee = surveyFee;
	}

	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}

	public Date getEndCaseTime() {
		return endCaseTime;
	}

	public void setEndCaseTime(Date endCaseTime) {
		this.endCaseTime = endCaseTime;
	}

	public String getPayMode() {
		return payMode;
	}

	public void setPayMode(String payMode) {
		this.payMode = payMode;
	}

	public String getCureStatus() {
		return cureStatus;
	}

	public void setCureStatus(String cureStatus) {
		this.cureStatus = cureStatus;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public String getIsDesignated() {
		return isDesignated;
	}

	public void setIsDesignated(String isDesignated) {
		this.isDesignated = isDesignated;
	}

	public String getPolicyType() {
		return policyType;
	}

	public void setPolicyType(String policyType) {
		this.policyType = policyType;
	}

	public Date getDueTime() {
		return dueTime;
	}

	public void setDueTime(Date dueTime) {
		this.dueTime = dueTime;
	}

	public String getAccidentDetail() {
		return accidentDetail;
	}

	public void setAccidentDetail(String accidentDetail) {
		this.accidentDetail = accidentDetail;
	}

	public String getAddidentDesc() {
		return addidentDesc;
	}

	public void setAddidentDesc(String addidentDesc) {
		this.addidentDesc = addidentDesc;
	}

	public String getAccResult1() {
		return accResult1;
	}

	public void setAccResult1(String accResult1) {
		this.accResult1 = accResult1;
	}

	public String getAccName1() {
		return accName1;
	}

	public void setAccName1(String accName1) {
		this.accName1 = accName1;
	}

	public String getAccResult2() {
		return accResult2;
	}

	public void setAccResult2(String accResult2) {
		this.accResult2 = accResult2;
	}

	public String getAccName2() {
		return accName2;
	}

	public void setAccName2(String accName2) {
		this.accName2 = accName2;
	}

	public String getSecondOrganCode() {
		return secondOrganCode;
	}

	public void setSecondOrganCode(String secondOrganCode) {
		this.secondOrganCode = secondOrganCode;
	}

	public String getThirdOrganCode() {
		return thirdOrganCode;
	}

	public void setThirdOrganCode(String thirdOrganCode) {
		this.thirdOrganCode = thirdOrganCode;
	}

	public String getFourthOrganCode() {
		return fourthOrganCode;
	}

	public void setFourthOrganCode(String fourthOrganCode) {
		this.fourthOrganCode = fourthOrganCode;
	}

	public String getClmtName() {
		return clmtName;
	}

	public void setClmtName(String clmtName) {
		this.clmtName = clmtName;
	}

	public String getClmtInsurRelation() {
		return clmtInsurRelation;
	}

	public void setClmtInsurRelation(String clmtInsurRelation) {
		this.clmtInsurRelation = clmtInsurRelation;
	}

	public String getClmtMp() {
		return clmtMp;
	}

	public void setClmtMp(String clmtMp) {
		this.clmtMp = clmtMp;
	}

	public String getResistFlag() {
		return resistFlag;
	}

	public void setResistFlag(String resistFlag) {
		this.resistFlag = resistFlag;
	}

	public String getSearOrganCode() {
		return searOrganCode;
	}

	public void setSearOrganCode(String searOrganCode) {
		this.searOrganCode = searOrganCode;
	}

	public String getSearOrganName() {
		return searOrganName;
	}

	public void setSearOrganName(String searOrganName) {
		this.searOrganName = searOrganName;
	}

	public String getSearDate() {
		return searDate;
	}

	public void setSearDate(String searDate) {
		this.searDate = searDate;
	}

	public String getSearListType() {
		return searListType;
	}

	public void setSearListType(String searListType) {
		this.searListType = searListType;
	}

	public Date getSearStaBeginDate() {
		return searStaBeginDate;
	}

	public void setSearStaBeginDate(Date searStaBeginDate) {
		this.searStaBeginDate = searStaBeginDate;
	}

	public Date getSearStaEndDate() {
		return searStaEndDate;
	}

	public void setSearStaEndDate(Date searStaEndDate) {
		this.searStaEndDate = searStaEndDate;
	}

	public BigDecimal getSearApplyType() {
		return searApplyType;
	}

	public void setSearApplyType(BigDecimal searApplyType) {
		this.searApplyType = searApplyType;
	}

	public BigDecimal getSearCaseFlag() {
		return searCaseFlag;
	}

	public void setSearCaseFlag(BigDecimal searCaseFlag) {
		this.searCaseFlag = searCaseFlag;
	}

	public String getSearClaimType() {
		return searClaimType;
	}

	
	public BigDecimal getRn() {
		return rn;
	}

	public void setRn(BigDecimal rn) {
		this.rn = rn;
	}

	public void setSearClaimType(String searClaimType) {
		this.searClaimType = searClaimType;
	}

	public BigDecimal getSearLiabConclusion() {
		return searLiabConclusion;
	}

	public void setSearLiabConclusion(BigDecimal searLiabConclusion) {
		this.searLiabConclusion = searLiabConclusion;
	}

	public String getSearBusiItemId() {
		return searBusiItemId;
	}

	public void setSearBusiItemId(String searBusiItemId) {
		this.searBusiItemId = searBusiItemId;
	}

	public BigDecimal getUserId() {
		return userId;
	}

	public void setUserId(BigDecimal userId) {
		this.userId = userId;
	}

	public String getClmFlag() {
		return clmFlag;
	}

	public void setClmFlag(String clmFlag) {
		this.clmFlag = clmFlag;
	}
	
	public BigDecimal getUwSum() {
		return uwSum;
	}

	public void setUwSum(BigDecimal uwSum) {
		this.uwSum = uwSum;
	}

	public BigDecimal getSumPayAmount() {
		return sumPayAmount;
	}

	public void setSumPayAmount(BigDecimal sumPayAmount) {
		this.sumPayAmount = sumPayAmount;
	}

	public BigDecimal getSumRejectPay() {
		return sumRejectPay;
	}

	public void setSumRejectPay(BigDecimal sumRejectPay) {
		this.sumRejectPay = sumRejectPay;
	}

	public BigDecimal getSumAdjustPay() {
		return sumAdjustPay;
	}

	public void setSumAdjustPay(BigDecimal sumAdjustPay) {
		this.sumAdjustPay = sumAdjustPay;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	
	public String getAgentOrganCode() {
		return agentOrganCode;
	}

	public void setAgentOrganCode(String agentOrganCode) {
		this.agentOrganCode = agentOrganCode;
	}
	/**
	 * 险种标识
	 */
	private String busiFlag;
	/**
	 * 续保保单生效日
	 */
	private Date initialValidateDate;
	public String getBusiFlag() {
		return busiFlag;
	}

	public void setBusiFlag(String busiFlag) {
		this.busiFlag = busiFlag;
	}

	public Date getInitialValidateDate() {
		return initialValidateDate;
	}

	public void setInitialValidateDate(Date initialValidateDate) {
		this.initialValidateDate = initialValidateDate;
	}
	
	
	public String getLiabCode() {
		return liabCode;
	}

	public void setLiabCode(String liabCode) {
		this.liabCode = liabCode;
	}

	public String getItProblemNo() {
		return itProblemNo;
	}

	public void setItProblemNo(String itProblemNo) {
		this.itProblemNo = itProblemNo;
	}

	public String getItProblemCase() {
		return itProblemCase;
	}

	public void setItProblemCase(String itProblemCase) {
		this.itProblemCase = itProblemCase;
	}

	@Override
	public String toString() {
		return "PayRejectBO [organCode=" + organCode + ", organName="
				+ organName + ", accidentNo=" + accidentNo + ", caseNo="
				+ caseNo + ", policyCode=" + policyCode + ", caseFlagName="
				+ caseFlagName + ", busiProdCode=" + busiProdCode
				+ ", busiName=" + busiName + ", claimName=" + claimName
				+ ", liabId=" + liabId + ", liabName=" + liabName
				+ ", customerName=" + customerName + ", mobileTel=" + mobileTel
				+ ", age=" + age + ", gender=" + gender + ", validdateDate="
				+ validdateDate + ", accDate=" + accDate + ", claimDate="
				+ claimDate + ", treatStart=" + treatStart + ", accDesc="
				+ accDesc + ", rptrName=" + rptrName + ", rptrMp=" + rptrMp
				+ ", rptrTime=" + rptrTime + ", signTime=" + signTime
				+ ", signerId=" + signerId + ", signerName=" + signerName
				+ ", registeTime=" + registeTime + ", accReasonDesc="
				+ accReasonDesc + ", registerName=" + registerName
				+ ", auditorName=" + auditorName + ", auditDecision="
				+ auditDecision + ", approverName=" + approverName
				+ ", approveTime=" + approveTime + ", trusteeName="
				+ trusteeName + ", trusteeMp=" + trusteeMp
				+ ", trusteeOrganName=" + trusteeOrganName
				+ ", liabConclusion=" + liabConclusion + ", caseFlag="
				+ caseFlag + ", sumAmount=" + sumAmount
				+ ", deductAmount=" + deductAmount
				+ ", adjustPay=" + adjustPay + ", rejectPay=" + rejectPay
				+ ", rejectReason=" + rejectReason + ", detainPay=" + detainPay
				+ ", returnPay=" + returnPay + ", payAmount=" + payAmount
				+ ", liveStatus=" + liveStatus + ", beneName=" + beneName
				+ ", surveyFlag=" + surveyFlag + ", surveyNum=" + surveyNum
				+ ", positiveFlag=" + positiveFlag + ", surveyById1Name="
				+ surveyById1Name + ", surveyById2Name=" + surveyById2Name
				+ ", surveyById3Name=" + surveyById3Name + ", firstApplyDate="
				+ firstApplyDate + ", lastApplyDate=" + lastApplyDate
				+ ", surveyFee=" + surveyFee + ", auditTime=" + auditTime
				+ ", endCaseTime=" + endCaseTime + ", payMode=" + payMode
				+ ", cureStatus=" + cureStatus + ", hospitalName="
				+ hospitalName + ", isDesignated=" + isDesignated
				+ ", policyType=" + policyType + ", dueTime=" + dueTime
				+ ", accidentDetail=" + accidentDetail + ", addidentDesc="
				+ addidentDesc + ", accResult1=" + accResult1 + ", accName1="
				+ accName1 + ", accResult2=" + accResult2 + ", accName2="
				+ accName2 + ", secondOrganCode=" + secondOrganCode
				+ ", thirdOrganCode=" + thirdOrganCode + ", fourthOrganCode="
				+ fourthOrganCode + ", clmtName=" + clmtName
				+ ", clmtInsurRelation=" + clmtInsurRelation + ", clmtMp="
				+ clmtMp + ", resistFlag=" + resistFlag + ", searOrganCode="
				+ searOrganCode + ", searOrganName=" + searOrganName
				+ ", searDate=" + searDate + ", searListType=" + searListType
				+ ", searStaBeginDate=" + searStaBeginDate
				+ ", searStaEndDate=" + searStaEndDate + ", searApplyType="
				+ searApplyType + ", searCaseFlag=" + searCaseFlag
				+ ", searClaimType=" + searClaimType + ", searLiabConclusion="
				+ searLiabConclusion + ", searBusiItemId=" + searBusiItemId
				+ ", userId=" + userId + ",priorityClaim="+priorityClaim
				+ ", channelCode=" + channelCode
				+ ", channelName=" + channelName
				+ "]";
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

}
