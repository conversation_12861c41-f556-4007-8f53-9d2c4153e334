package com.nci.tunan.qry.interfaces.model.uw.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

@SuppressWarnings("serial")
public class HisCusUwInfoPO extends BasePO {

	public String getIsStandardRisk() {
		return getString("isStandardRisk");
	}

	public void setIsStandardRisk(String isStandardRisk) {
		setString("isStandardRisk", isStandardRisk);

	}

	public void setUwId(BigDecimal uwId) {
		setBigDecimal("uw_id", uwId);
	}

	public BigDecimal getUwId() {
		return getBigDecimal("uw_id");
	}

	public void setUwUserId(BigDecimal uwUserId) {
		setBigDecimal("uw_user_id", uwUserId);
	}

	public BigDecimal getUwUserId() {
		return getBigDecimal("uw_user_id");
	}

	public void setCustomerId(BigDecimal customerId) {
		setBigDecimal("customer_id", customerId);
	}

	public BigDecimal getCustomerId() {
		return getBigDecimal("customer_id");
	}

	public void setUwDecision(BigDecimal uwDecision) {
		setBigDecimal("uw_decision", uwDecision);
	}

	public BigDecimal getUwDecision() {
		return getBigDecimal("uw_decision");
	}

	public void setAcceptTime(Date acceptTime) {
		setUtilDate("accept_time", acceptTime);
	}

	public Date getAcceptTime() {
		return getUtilDate("accept_time");
	}

	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}

	public String getPolicyCode() {
		return getString("policy_code");
	}

	public void setAcceptCode(String acceptCode) {
		setString("accept_code", acceptCode);
	}

	public String getAcceptCode() {
		return getString("accept_code");
	}

	public void setServiceCode(String serviceCode) {
		setString("service_code", serviceCode);
	}

	public String getServiceCode() {
		return getString("service_code");
	}

	public void setAcceptStatus(String acceptStatus) {
		setString("accept_status", acceptStatus);
	}

	public String getAcceptStatus() {
		return getString("accept_status");
	}

	public void setUwStatus(String uwStatus) {
		setString("uw_status", uwStatus);
	}

	public String getUwStatus() {
		return getString("uw_status");
	}

	public void setProductName(String productName) {
		setString("product_name", productName);
	}

	public String getProductName() {
		return getString("product_name");
	}

	public String getPolicyDecision() {
		return getString("policy_decision");
	}

	public void setPolicyDecision(String policyDecision) {
		setString("policy_decision", policyDecision);
	}

	public String getApplyCode() {
		return getString("apply_code");
	}

	public void setApplyCode(String applyCode) {
		setString("apply_code", applyCode);
	}

	public String getQueryBusiCode() {
		return getString("query_busi_code");
	}

	public void setQueryBusiCode(String queryBusiCode) {
		setString("query_busi_code", queryBusiCode);
	}

	public String getBusiProdCode() {
		return getString("busi_prod_code");
	}

	public void setBusiProdCode(String busiProdCode) {
		setString("busi_prod_code", busiProdCode);
	}

	public String getRoleType() {
		return getString("role_type");
	}

	public void setRoleType(String roleType) {
		setString("role_type", roleType);
	}

	public void setIsExceptionNotifyFlag(BigDecimal isExceptionNotifyFlag) {
		setBigDecimal("is_exception_notify_flag", isExceptionNotifyFlag);
	}

	public BigDecimal getIsExceptionNotifyFlag() {
		return getBigDecimal("is_exception_notify_flag");
	}

	public void setAcceptId(BigDecimal acceptId) {
		setBigDecimal("accept_id", acceptId);
	}

	public BigDecimal getAcceptId() {
		return getBigDecimal("accept_id");
	}

	public String getUwAutoFlag() {
		return getString("uwAutoFlag");
	}

	public void setUwAutoFlag(String uwAutoFlag) {
		setString("uwAutoFlag", uwAutoFlag);
	}

	public String getOrganCode() {
		return getString("organ_code");
	}

	public void setOrganCode(String organCode) {
		setString("organ_code", organCode);
	}

	public String getSurveyModuleResult() {
		return getString("survey_module_result");
	}

	public void setSurveyModuleResult(String surveyModuleResult) {
		setString("survey_module_result", surveyModuleResult);
	}

	public void setTotal1(BigDecimal total1) {
		setBigDecimal("total_1", total1);
	}

	public BigDecimal getTotal1() {
		return getBigDecimal("total_1");
	}
	
	public void setTotal2(BigDecimal total2) {
		setBigDecimal("total_2", total2);
	}

	public BigDecimal getTotal2() {
		return getBigDecimal("total_2");
	}
	
	public void setIncome(BigDecimal income) {
		setBigDecimal("income", income);
	}

	public BigDecimal getIncome() {
		return getBigDecimal("income");
	}
	/**
	 * 投保人或者被保人异常告知
	 */
	public void setAppntoldimpartInfo(String appntoldimpartInfo) {
		setString("appntoldimpartInfo", appntoldimpartInfo);
	}
	/**
	 * 投保人或者被保人异常告知
	 */
	public String getAppntoldimpartInfo() {
		return getString("appntoldimpartInfo");
	}
	/**
	 * 投保人或者被保人异常告知
	 */
	public BigDecimal getIsExceptionNotifyFlag2() {
		return getBigDecimal("is_exception_notify_flag2");
	}
	/**
	 * 投保人或者被保人异常告知
	 */
	public void setIsExceptionNotifyFlag2(BigDecimal isExceptionNotifyFlag2) {
		setBigDecimal("is_exception_notify_flag2", isExceptionNotifyFlag2);
	}
	
	/**
	 * 984非标标识
	 */
	public void setNonStandardsValueS(String nonStandardsValueS) {
		setString("non_standards_valueS", nonStandardsValueS);
	}
	public String getNonStandardsValueS() {
		return getString("non_standards_valueS");
	}
	@Override
	public String toString() {
		return "HisCusUwInfoPO [getIsStandardRisk()=" + getIsStandardRisk()
				+ ", getUwId()=" + getUwId() + ", getUwUserId()="
				+ getUwUserId() + ", getCustomerId()=" + getCustomerId()
				+ ", getUwDecision()=" + getUwDecision() + ", getAcceptTime()="
				+ getAcceptTime() + ", getPolicyCode()=" + getPolicyCode()
				+ ", getAcceptCode()=" + getAcceptCode()
				+ ", getServiceCode()=" + getServiceCode()
				+ ", getAcceptStatus()=" + getAcceptStatus()
				+ ", getUwStatus()=" + getUwStatus() + ", getProductName()="
				+ getProductName() + ", getPolicyDecision()="
				+ getPolicyDecision() + ", getApplyCode()=" + getApplyCode()
				+ ", getQueryBusiCode()=" + getQueryBusiCode()
				+ ", getBusiProdCode()=" + getBusiProdCode()
				+ ", getRoleType()=" + getRoleType()
				+ ", getIsExceptionNotifyFlag()=" + getIsExceptionNotifyFlag()
				+ ", getAcceptId()=" + getAcceptId() + ", getUwAutoFlag()="
				+ getUwAutoFlag() + ", getOrganCode()=" + getOrganCode()
				+ ", getSurveyModuleResult()=" + getSurveyModuleResult() + "]";
	}

}
