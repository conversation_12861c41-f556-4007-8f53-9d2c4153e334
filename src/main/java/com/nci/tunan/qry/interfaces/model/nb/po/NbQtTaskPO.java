package com.nci.tunan.qry.interfaces.model.nb.po;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BasePO;

/**
 * @description NbQtTaskPO对象
 * <AUTHOR>
 * @date 2015-01-14 17:30:48
 */
public class NbQtTaskPO extends BasePO {
	/** 属性 --- java类型 --- oracle类型_数据长度_小数位长度_注释信息 */
	// bpoComp --- BigDecimal --- NUMBER_10_0_null;
	// qtSelectDate --- Date --- DATE_7_0_null;
	// qtUser --- String --- VARCHAR2_30_0_null;
	// entryType --- String --- CHAR_1_0_null;
	// createTime --- Date --- DATE_7_0_null;
	// custLevel --- String --- VARCHAR2_2_0_null;
	// productCode --- String --- VARCHAR2_40_0_null;
	// taskId --- BigDecimal --- NUMBER_10_0_null;
	// qtTime --- Date --- DATE_7_0_null;
	// qaType --- String --- CHAR_1_0_null;
	// applyCode --- String --- VARCHAR2_30_0_null;
	// qtResult --- String --- VARCHAR2_20_0_null;
	// channelType --- String --- CHAR_1_0_null;
	// qtComments --- String --- VARCHAR2_255_0_null;
	// agentLevel --- String --- NUMBER_1_0_null;
	// policyId --- BigDecimal --- NUMBER_10_0_null;
	// qtErrorCount --- BigDecimal --- NUMBER_5_0_null;
	// qtHolderIndi --- BigDecimal --- NUMBER_1_0_null;
	// qtInsuredIndi --- BigDecimal --- NUMBER_1_0_null;
	// qtVerifyIndi --- BigDecimal --- NUMBER_1_0_null;
	// qtErrorRate --- BigDecimal --- NUMBER_2_4_null;
	// qtTooltip --- String --- VARCHAR2_500_0_null;
	// qtElementTotal --- BigDecimal --- NUMBER_5_0_null;
	// bankBranchCode --- String --- VARCHAR2_20_0_null;
	// policyType --- String --- CHAR_1_0_null;
	// qtCriteriaId --- BigDecimal --- NUMBER_10_0_null;
	// policyCode --- String --- VARCHAR2_30_0_null;
	// standIndi --- String --- CHAR_1_0_null;
	// batchId --- BigDecimal --- NUMBER_10_0_null;
	// bankCode --- String --- VARCHAR2_30_0_null;
	// orgCode --- String --- VARCHAR2_8_0_null;
	// qtOrgCode --- String --- VARCHAR2_20_0_null;
	// qtStatus --- String --- CHAR_1_0_null;
	// manualuw--- String --- 
	//String batchNO;//双录质检上传批次号
	//signSource String //标记回执签署来源，使用码表T_SIGN_SOURCE
	
	//标记回执签署来源，使用码表T_SIGN_SOURCE
	public void setSignSource(String signSource) {
		setString("sign_source", signSource);
	}

	public String getSignSource() {
		return getString("sign_source");
	}
	
	public void setOmnipotent(boolean isOmnipotent) {
		setBoolean("is_omnipotent", isOmnipotent);
	}
	public Date getOperatorDate(){
		return getUtilDate("operator_date");
	}
	public void setOperatorDate(Date operatorDate){
		setUtilDate("operator_date", operatorDate);
	}
	
	public BigDecimal getOperatorCode(){
		return getBigDecimal("operator_code");
	}
	public void setOperatorCode(BigDecimal operatorCode){
		setBigDecimal("operator_code", operatorCode);
	}
	
	public void setIsArt(BigDecimal isArt){
		setBigDecimal("is_art",isArt);
	}//是否人工复检0-否  1-是  
	
	public BigDecimal getIsArt(){
		return getBigDecimal("is_art");
	}

	public void setBatchNo(String batchNo){
		setString("batch_no",batchNo);
	}//人工核保
	
	public String getBatchNo(){
		return getString("batch_no");
	}
	
	public void setManualUw(String manualUw){
		setString("manual_uw",manualUw);
	}//人工核保
	
	public String getManualUw(){
		return getString("manual_uw");
	}
	public void setDocumentNo(String documentNo) {
		setString("document_no", documentNo);
	}//通知书号

	public String getDocumentNo() {
		return getString("document_no");
	}//通知书号
	
	public void setBpoComp(BigDecimal bpoComp) {
		setBigDecimal("bpo_comp", bpoComp);
	}
	
	public void setStartDate(String startDate) {
		setString("start_date", startDate);
	}//起始日期

	public String getStartDate() {
		return getString("start_date");
	}//起始日期
	public void setEndDate(String endDate) {
		setString("end_date", endDate);
	}//终止日期

	public String getEndDate() {
		return getString("end_date");
	}//终止日期
	public void setOrganCode(String organCode) {
		setString("organ_code", organCode);
	}//管理机构

	public String getOrganCode() {
		return getString("organ_code");
	}//管理机构
	public void setOrganName(String organName) {
		setString("organ_name", organName);
	}//机构名称

	public String getOrganName() {
		return getString("organ_name");
	}//机构名称
	public void setTypeName(String typeName) {
		setString("type_name", typeName);
	}//质检任务类型

	public String getTypeName() {
		return getString("type_name");
	}//质检任务类型
	public void setTask(BigDecimal task) {
		setBigDecimal("task", task);
	}//总件数

	public BigDecimal getTask() {
		return getBigDecimal("task");
	}//总件数
	public void setQualified(BigDecimal qualified) {
		setBigDecimal("qualified", qualified);
	}//通过

	public BigDecimal getQualified() {
		return getBigDecimal("qualified");
	}//通过
	public void setUnqualified(BigDecimal unqualified) {
		setBigDecimal("unqualified", unqualified);
	}//不通过

	public BigDecimal getUnqualified() {
		return getBigDecimal("unqualified");
	}//不通过
	public void setQuali(BigDecimal quali) {
		setBigDecimal("quali", quali);
	}//通过率

	public BigDecimal getQuali() {
		return getBigDecimal("quali");
	}//通过率
	public void setStatusName(String statusName) {
		setString("status_name", statusName);
	}//质检状态

	public String getStatusName() {
		return getString("status_name");
	}//质检状态
	public void setAgentCode(String agentCode) {
		setString("agent_code", agentCode);
	}//业务员编号

	public String getAgentCode() {
		return getString("agent_code");
	}//业务员编号
	public void setUserName(String userName) {
		setString("user_name", userName);
	}//业务员姓名

	public String getUserName() {
		return getString("user_name");
	}//业务员姓名
	
	public void setAcode(String acode) {
		setString("agent_code", acode);
	}//业务员编号

	public String getAcode() {
		return getString("agent_code");
	}//业务员编号
	public void setUname(String uname) {
		setString("uname", uname);
	}//业务员姓名

	public String getUname() {
		return getString("uname");
	}//业务员姓名
	public BigDecimal getBpoComp() {
		return getBigDecimal("bpo_comp");
	}
	public void setCustomerName(String customerName) {
		setString("customer_name", customerName);
	}//投保人
	public String getCustomerName() {
		return getString("customer_name");
	}//投保人
	public void setIssueDate(Date issueDate) {
		setUtilDate("issue_date", issueDate);
	}//签单日期
	public Date getIssueDate() {
		return getUtilDate("issue_date");
	}//签单日期
	
	public void setAuditDate(Date auditDate) {
		setUtilDate("audit_date", auditDate);
	}//审核日期
	public Date getAuditDate() {
		return getUtilDate("audit_date");
	}//审核日期
	public void setReviewDate(Date reviewDate) {
		setUtilDate("review_date", reviewDate);
	}//复核日期
	public Date getReviewDate() {
		return getUtilDate("review_date");
	}//复核日期
	
	public void setTimeStatus(BigDecimal timeStatus) {
		setBigDecimal("time_status", timeStatus);
	}//时效状态
	public BigDecimal getTimeStatus() {
		return getBigDecimal("time_status");
	}//时效状态

	
	public void setPopulationTimeStatus(BigDecimal populationTimeStatus) {
		setBigDecimal("population_time_status", populationTimeStatus);
	}//时效状态
	public BigDecimal getPopulationTimeStatus() {
		return getBigDecimal("population_time_status");
	}//时效状态

	public void setQtUser(String qtUser) {
		setString("qt_user", qtUser);
	}
	public String getQtUser() {
		return getString("qt_user");
	}

	public void setRemark(String remark) {
		setString("remark", remark);
	}
	
	public String getRemark() {
		return getString("remark");
	}
	public void setEntryType(String entryType) {
		setString("entry_type", entryType);
	}

	public String getEntryType() {
		return getString("entry_type");
	}

	public void setCreateTime(Date createTime) {
		setUtilDate("create_time", createTime);
	}

	public Date getCreateTime() {
		return getUtilDate("create_time");
	}

	public void setTaskId(BigDecimal taskId) {
		setBigDecimal("task_id", taskId);
	}

	public BigDecimal getTaskId() {
		return getBigDecimal("task_id");
	}

	public void setQtTime(Date qtTime) {
		setUtilDate("qt_time", qtTime);
	}

	public Date getQtTime() {
		return getUtilDate("qt_time");
	}

	public void setQaType(String qaType) {
		setString("qa_type", qaType);
	}

	public String getQaType() {
		return getString("qa_type");
	}

	public void setIsImportDqr(BigDecimal isImportDqr){
		setBigDecimal("is_import_dqr", isImportDqr);
	}
	public BigDecimal getIsImportDqr(){
		return getBigDecimal("is_import_dqr");
	}
	
	public void setApplyCode(String applyCode) {
		setString("apply_code", applyCode);
	}

	public String getApplyCode() {
		return getString("apply_code");
	}

	public void setQtResult(String qtResult) {
		setString("qt_result", qtResult);
	}

	public String getQtResult() {
		return getString("qt_result");
	}

	public void setChannelType(String channelType) {
		setString("channel_type", channelType);
	}

	public String getChannelType() {
		return getString("channel_type");
	}

	public void setQtComments(String qtComments) {
		setString("qt_comments", qtComments);
	}

	public String getQtComments() {
		return getString("qt_comments");
	}

	public void setAgentLevel(String agentLevel) {
		setString("agent_level", agentLevel);
	}

	public String getAgentLevel() {
		return getString("agent_level");
	}

	public void setPolicyId(BigDecimal policyId) {
		setBigDecimal("policy_id", policyId);
	}

	public BigDecimal getPolicyId() {
		return getBigDecimal("policy_id");
	}

	public void setQtErrorCount(BigDecimal qtErrorCount) {
		setBigDecimal("qt_error_count", qtErrorCount);
	}

	public BigDecimal getQtErrorCount() {
		return getBigDecimal("qt_error_count");
	}

	public void setQtHolderIndi(BigDecimal qtHolderIndi) {
		setBigDecimal("qt_holder_indi", qtHolderIndi);
	}

	public BigDecimal getQtHolderIndi() {
		return getBigDecimal("qt_holder_indi");
	}

	public void setQtInsuredIndi(BigDecimal qtInsuredIndi) {
		setBigDecimal("qt_insured_indi", qtInsuredIndi);
	}

	public BigDecimal getQtInsuredIndi() {
		return getBigDecimal("qt_insured_indi");
	}

	public void setQtVerifyIndi(BigDecimal qtVerifyIndi) {
		setBigDecimal("qt_verify_indi", qtVerifyIndi);
	}

	public BigDecimal getQtVerifyIndi() {
		return getBigDecimal("qt_verify_indi");
	}

	public void setQtTooltip(String qtTooltip) {
		setString("qt_tooltip", qtTooltip);
	}

	public String getQtTooltip() {
		return getString("qt_tooltip");
	}

	public void setQtElementTotal(BigDecimal qtElementTotal) {
		setBigDecimal("qt_element_total", qtElementTotal);
	}

	public BigDecimal getQtElementTotal() {
		return getBigDecimal("qt_element_total");
	}
	public void setBankBranchName(String bankBranchName) {
		setString("bank_branch_name", bankBranchName);
	}

	public String getBankBranchName() {
		return getString("bank_branch_name");
	}
	public String getBankName() {
		return getString("bank_name");
	}	
	public void setBankName(String bankName) {
		setString("bank_name", bankName);
	}

	public void setBankBranchCode(String bankBranchCode) {
		setString("bank_branch_code", bankBranchCode);
	}

	public String getBankBranchCode() {
		return getString("bank_branch_code");
	}

	public void setCardCode(String cardCode) {
		setString("card_code", cardCode);
	}

	public String getCardCode() {
		return getString("card_code");
	}

	public void setQtCriteriaId(BigDecimal qtCriteriaId) {
		setBigDecimal("qt_criteria_id", qtCriteriaId);
	}

	public BigDecimal getQtCriteriaId() {
		return getBigDecimal("qt_criteria_id");
	}

	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}

	public String getPolicyCode() {
		return getString("policy_code");
	}

	public void setBatchId(BigDecimal batchId) {
		setBigDecimal("batch_id", batchId);
	}

	public BigDecimal getBatchId() {
		return getBigDecimal("batch_id");
	}

	public void setBankCode(String bankCode) {
		setString("bank_code", bankCode);
	}

	public String getBankCode() {
		return getString("bank_code");
	}

	public void setQtStatus(BigDecimal qtStatus) {
		setBigDecimal("qt_status", qtStatus);
	}

	public BigDecimal getQtStatus() {
		return getBigDecimal("qt_status");
	}

	public void setRiskDefectIndi(BigDecimal riskDefectIndi) {
		setBigDecimal("risk_defect_indi", riskDefectIndi);
	}

	public BigDecimal getRiskDefectIndi() {
		return getBigDecimal("risk_defect_indi");
	}


	public void setCustomerLevel(String customerLevel) {
		setString("customer_level", customerLevel);
	}

	public String getCustomerLevel() {
		return getString("customer_level");
	}

	public void setFieldErrorRate(BigDecimal fieldErrorRate) {
		setBigDecimal("field_error_rate", fieldErrorRate);
	}

	public BigDecimal getFieldErrorRate() {
		return getBigDecimal("field_error_rate");
	}

	public void setDefectIndi(BigDecimal defectIndi) {
		setBigDecimal("defect_indi", defectIndi);
	}

	public BigDecimal getDefectIndi() {
		return getBigDecimal("defect_indi");
	}

	public void setBillErrorRate(BigDecimal billErrorRate) {
		setBigDecimal("bill_error_rate", billErrorRate);
	}

	public BigDecimal getBillErrorRate() {
		return getBigDecimal("bill_error_rate");
	}
	
	public void setHolderId(BigDecimal holderId) {
		setBigDecimal("holder_id", holderId);
	}

	public BigDecimal getHolderId() {
		return getBigDecimal("holder_id");
	}
	
	public void setInsuredId(BigDecimal insuredId) {
		setBigDecimal("insured_id", insuredId);
	}

	public BigDecimal getInsuredId() {
		return getBigDecimal("insured_id");
	}
	
	
	public void setDocuFlag(BigDecimal docuFlag) {
		setBigDecimal("docu_flag", docuFlag);
	}

	public BigDecimal getDocuFlag() {
		return getBigDecimal("docu_flag");
	}
	
	public void setAcknowledgeDate(Date acknowledgeDate) {
	    setUtilDate("acknowledge_date", acknowledgeDate);
	}

	public Date getAcknowledgeDate() {
		return getUtilDate("acknowledge_date");
	}
	public void setProposalStatus(String proposalStatus) {
		setString("proposal_status", proposalStatus);
	}

	public String getProposalStatus() {
		return getString("proposal_status");
	}
	
	public void setApplyDate(Date applyDate) {
		setUtilDate("apply_date", applyDate);
	}

	public Date getApplyDate() {
		return getUtilDate("apply_date");
	}
	
	public void setApplyDateS(Date applyDateS) {
		setUtilDate("apply_date_s", applyDateS);
	}

	public Date getApplyDateS() {
		return getUtilDate("apply_date_s");
	}
	
	public void setApplyDateE(Date applyDateE) {
		setUtilDate("apply_date_e", applyDateE);
	}

	public Date getApplyDateE() {
		return getUtilDate("apply_date_e");
	}
	
	public void setAudioInfoDate(Date audioInfoDate) {
		setUtilDate("audio_info_date", audioInfoDate);
	}

	public Date getAudioInfoDate() {
		return getUtilDate("audio_info_date");
	}
	
	public void setAudioInfoSize(String audioInfoSize) {
		setString("audio_info_size", audioInfoSize);
	}

	public String getAudioInfoSize() {
		return getString("audio_info_size");
	}
	
	public void setTotalNum(BigDecimal totalNum) {
        setBigDecimal("total_num", totalNum);
    }

    public BigDecimal getTotalNum() {
        return getBigDecimal("total_num");
    }
    
    public void setCompleteNum(BigDecimal completeNum) {
        setBigDecimal("complete_num", completeNum);
    }

    public BigDecimal getCompleteNum() {
        return getBigDecimal("complete_num");
    }
    
    public void setCompleteRate(BigDecimal completeRate) {
        setBigDecimal("complete_rate", completeRate);
    }

    public BigDecimal getCompleteRate() {
        return getBigDecimal("complete_rate");
    }
    
    public BigDecimal getAgentNum() {
		return getBigDecimal("agent_num");
	}

	public void setAgentNum(BigDecimal agentNum) {
		setBigDecimal("agent_num",agentNum);
	}

	public BigDecimal getCompleteAgent() {
		return getBigDecimal("complete_agent");
	}

	public void setCompleteAgent(BigDecimal completeAgent) {
		setBigDecimal("complete_agent",completeAgent);
	}

	public BigDecimal getAgentRate() {
		return getBigDecimal("agent_rate");
	}

	public void setAgentRate(BigDecimal agentRate) {
		setBigDecimal("agent_rate",agentRate);
	}
	
    public void setIsQualityAgent(String isQualityAgent) {
        setString("is_quality_agent", isQualityAgent);
    }

    public String getIsQualityAgent() {
        return getString("is_quality_agent");
    }
    
    public void setQtStatusIn(String qtStatusIn) {
        setString("qt_status_in", qtStatusIn);
    }

    public String getQtStatusIn() {
        return getString("qt_status_in");
    }
    
    public void setQualityAgentLevel(String qualityAgentLevel) {
        setString("quality_agent_level", qualityAgentLevel);
    }

    public String getQualityAgentLevel() {
        return getString("quality_agent_level");
    }
    
    public String getSignDateString() {
		return getString("sign_date_string");
	}

	public void setSignDateString(String signDateString) {
		setString("sign_date_string",signDateString);
	}
	
	public String getHolderName() {
		return getString("holder_name");
	}

	public void setHolderName(String holderName) {
		setString("holder_name",holderName);
	}

	public String getInsuredName() {
		return getString("insured_name");
	}

	public void setInsuredName(String insuredName) {
		setString("insured_name",insuredName);
	}
	
	
	public void setQtApplyCodeList(List<String> qtApplyCodeList) {
		setList("qt_apply_code_list",qtApplyCodeList);
	}

	public void setUwApplyCodeList(List<String> uwApplyCodeList) {
		setList("uw_apply_code_list",uwApplyCodeList);
	}
	public String getFeeStatus(){
		return getString("fee_status");
	}
	
	public void setFeeStatus(String feeStatus){
		setString("fee_status",feeStatus);
	}
	
	public String getQueryTaskType(){
		return getString("query_task_type");
	}
	
	public void setQueryTaskType(String queryTaskType){
		setString("query_task_type",queryTaskType);
	}
	
	public BigDecimal getVersionNo() {
		return getBigDecimal("version_no");
	}

	public void setVersionNo(BigDecimal versionNo) {
		setBigDecimal("version_no", versionNo);
	}
	public BigDecimal getDoubleMainriskFlag() {
		return getBigDecimal("double_mainrisk_flag");
	}
	
	public void setDoubleMainriskFlag(BigDecimal doubleMainriskFlag) {
		setBigDecimal("double_mainrisk_flag", doubleMainriskFlag);
	}
	public BigDecimal getDrqId(){
		return getBigDecimal("drq_id");
	}
	public void getDrqId(BigDecimal drqId){
		setBigDecimal("drq_id",drqId);
	}
	public Date getStartDt() {
		return getUtilDate("start_dt");
	}
	public void setStartDt(Date startDt) {
		setUtilDate("start_dt", startDt);
	}
	public Date getEndDt() {
		return getUtilDate("end_dt");
	}
	public void setEndDt(Date endDt) {
		setUtilDate("end_dt", endDt);
	}
	
	//通知状态
	public String getNoticeStatus() {
		return getString("notice_status");
	}

	public void setNoticeStatus(String noticeStatus) {
		setString("notice_status",noticeStatus);
	}
	
	//修正申请结论
	public String getApplyResult() {
		return getString("apply_result");
	}
	public void setApplyResult(String applyResult) {
		setString("apply_result",applyResult);
	}
	
	//修正复核结论
	public String getCtResult() {
		return getString("ct_result");
	}
	public void setCtResult(String ctResult) {
		setString("ct_result",ctResult);
	}
	
	//taskIdlist #需求变更#35812增加，接收申请已撤销的任务ID
	public String getTaskIdList() {
		return getString("task_id_list");
	}
	//taskIdlist #需求变更#35812增加，接收申请已撤销的任务ID
	public void setTaskIdList(String taskIdList) {
		setString("task_id_list",taskIdList);
	}
	
	public String getQueryWay() {
		return getString("queryWay");
	}

	public void setQueryWay(String queryWay) {
		setString("queryWay",queryWay);
	}
	public String getInputType() {
		return getString("input_type");
	}

	public void setInputType(String inputType) {
		setString("input_type",inputType);
	}

	

	

	
	public String getSalesChannelName() {
		return getString("sales_channel_name");
	}

	public void setSalesChannelName(String salesChannelName) {
		setString("sales_channel_name",salesChannelName);
	}

	public String getProductNameStd() {
		return getString("product_Name_Std");
	}

	public void setProductNameStd(String productNameStd) {
		setString("product_Name_Std",productNameStd);
	}

	public BigDecimal getTotalPremAf() {
		return getBigDecimal("total_Prem_Af");
	}

	public void setTotalPremAf(BigDecimal totalPremAf) {
		setBigDecimal("total_Prem_Af",totalPremAf);
	}

	public BigDecimal getChargeYear() {
		return getBigDecimal("charge_Year");
	}

	public void setChargeYear(BigDecimal chargeYear) {
		setBigDecimal("charge_Year",chargeYear);
	}

	public String getStatusDesc() {
		return getString("status_Desc");
	}

	public void setStatusDesc(String statusDesc) {
		setString("status_Desc",statusDesc);
	}

	public String getProductCategory1() {
		return getString("product_Category1");
	}

	public void setProductCategory1(String productCategory1) {
		setString("product_Category1",productCategory1);
	}
	
	public String getProductCode() {
		return getString("product_Code");
	}

	public void setProductCode(String productCode) {
		setString("product_Code",productCode);
	}
	public String getAgentName() {
		return getString("agent_name");
	}

	public void setAgentName(String agentName) {
		setString("agent_name",agentName);
	}
	
	public String getSubinputType() {
		return getString("subinput_Type");
	}
	public void setSubinputType(String subinputType) {
		setString("subinput_Type",subinputType);
	}

	
	public Date getUpdateTime() {
		return getUtilDate("update_Time");
	}
	public void setUpdateTime(Date updateTime) {
		setUtilDate("update_Time",updateTime);
	}

	
	public String getChargeYear1() {
		return getString("charge_Year1");
	}
	public void setChargeYear1(String chargeYear1) {
		setString("charge_Year1",chargeYear1);
	}
	
	public String getLiabilityState() {
		return getString("liability_State");
	}
	public void setLiabilityState(String liabilityState) {
		setString("liability_State",liabilityState);
	}
	public String getResionCode() {
		return getString("resion_code");
	}
	public void setResionCode(String resionCode) {
		setString("resion_code",resionCode);
	}
	
	@Override
	public String toString() {
		return "NbQtTaskPO [getIsArt()=" + getIsArt() + ", getBatchNo()="
				+ getBatchNo() + ", getManualUw()=" + getManualUw()
				+ ", getDocumentNo()=" + getDocumentNo() + ", getStartDate()="
				+ getStartDate() + ", getEndDate()=" + getEndDate()
				+ ", getOrganCode()=" + getOrganCode() + ", getOrganName()="
				+ getOrganName() + ", getTypeName()=" + getTypeName()
				+ ", getTask()=" + getTask() + ", getQualified()="
				+ getQualified() + ", getUnqualified()=" + getUnqualified()
				+ ", getQuali()=" + getQuali() + ", getStatusName()="
				+ getStatusName() + ", getAgentCode()=" + getAgentCode()
				+ ", getUserName()=" + getUserName() + ", getAcode()="
				+ getAcode() + ", getUname()=" + getUname() + ", getBpoComp()="
				+ getBpoComp() + ", getCustomerName()=" + getCustomerName()
				+ ", getIssueDate()=" + getIssueDate() + ", getAuditDate()="
				+ getAuditDate() + ", getReviewDate()=" + getReviewDate()
				+ ", getTimeStatus()=" + getTimeStatus()
				+ ", getPopulationTimeStatus()=" + getPopulationTimeStatus()
				+ ", getQtUser()=" + getQtUser() + ", getRemark()="
				+ getRemark() + ", getEntryType()=" + getEntryType()
				+ ", getCreateTime()=" + getCreateTime() + ", getTaskId()="
				+ getTaskId() + ", getQtTime()=" + getQtTime()
				+ ", getQaType()=" + getQaType() + ", getIsImportDqr()="
				+ getIsImportDqr() + ", getApplyCode()="
				+ getApplyCode() + ", getQtResult()=" + getQtResult()
				+ ", getChannelType()=" + getChannelType()
				+ ", getNoticeStatus()=" + getNoticeStatus()
				+ ", getQtComments()=" + getQtComments() + ", getAgentLevel()="
				+ getAgentLevel() + ", getPolicyId()=" + getPolicyId()
				+ ", getQtErrorCount()=" + getQtErrorCount()
				+ ", getQtHolderIndi()=" + getQtHolderIndi()
				+ ", getQtInsuredIndi()=" + getQtInsuredIndi()
				+ ", getQtVerifyIndi()=" + getQtVerifyIndi()
				+ ", getQtTooltip()=" + getQtTooltip()
				+ ", getQtElementTotal()=" + getQtElementTotal()
				+ ", getBankBranchName()=" + getBankBranchName()
				+ ", getBankName()=" + getBankName() + ", getBankBranchCode()="
				+ getBankBranchCode() + ", getCardCode()=" + getCardCode()
				+ ", getQtCriteriaId()=" + getQtCriteriaId()
				+ ", getPolicyCode()=" + getPolicyCode() + ", getBatchId()="
				+ getBatchId() + ", getBankCode()=" + getBankCode()
				+ ", getQtStatus()=" + getQtStatus() + ", getRiskDefectIndi()="
				+ getRiskDefectIndi() + ", getCustomerLevel()="
				+ getCustomerLevel() + ", getFieldErrorRate()="
				+ getFieldErrorRate() + ", getDefectIndi()=" + getDefectIndi()
				+ ", getBillErrorRate()=" + getBillErrorRate()
				+ ", getHolderId()=" + getHolderId() + ", getInsuredId()="
				+ getInsuredId() + ", getDocuFlag()=" + getDocuFlag()
				+ ", getAcknowledgeDate()=" + getAcknowledgeDate()
				+ ", getProposalStatus()=" + getProposalStatus()
				+ ", getApplyDate()=" + getApplyDate() + ", getApplyDateS()="
				+ getApplyDateS() + ", getApplyDateE()=" + getApplyDateE()
				+ ", getAudioInfoDate()=" + getAudioInfoDate()
				+ ", getAudioInfoSize()=" + getAudioInfoSize()
				+ ", getTotalNum()=" + getTotalNum() + ", getCompleteNum()="
				+ getCompleteNum() + ", getCompleteRate()=" + getCompleteRate()
				+ ", getAgentNum()=" + getAgentNum() + ", getCompleteAgent()="
				+ getCompleteAgent() + ", getAgentRate()=" + getAgentRate()
				+ ", getIsQualityAgent()=" + getIsQualityAgent()
				+ ", getQtStatusIn()=" + getQtStatusIn()
				+ ", getQualityAgentLevel()=" + getQualityAgentLevel()
				+ ", getSignDateString()=" + getSignDateString()
				+ ", getHolderName()=" + getHolderName()
				+ ", getInsuredName()=" + getInsuredName()
				+ ", getFeeStatus()=" + getFeeStatus()
				+ ", getQueryTaskType()=" + getQueryTaskType()
				+ ", getVersionNo()=" + getVersionNo() + ", getDrqId()="
				+ getDrqId() + ", getTablePrev()=" + getTablePrev()
				+ ", getRowId()=" + getRowId() + ", getIsDeleted()="
				+ getIsDeleted() + ", getCreator()=" + getCreator() 
				+ ", getOperatorCode()=" + getOperatorCode() 
				+ ", getOperatorDate()=" + getOperatorDate()
				+ ", getCreateDate()=" + getCreateDate()
				+ ", getLastModifyBy()=" + getLastModifyBy()
				+ ", getApplyResult()=" + getApplyResult()
				+ ", getCtResult()=" + getCtResult()
				+ ", getStartDt=" + getStartDt()
				+ ", getEndDt()=" + getEndDt()
				+ ", getLastModifyDate()=" + getLastModifyDate()
				+ ", getDoubleMainriskFlag()=" + getDoubleMainriskFlag()
				+ ", getDataBaseCreateTime()=" + getDataBaseCreateTime()
				+ ", getDataBaseUpdateTime()=" + getDataBaseUpdateTime()
				+ ", toString()=" + super.toString() + ", getData()="
				+ getData() + ", getValues()=" + getValues() + ", getClass()="
				+ getClass() + ", hashCode()=" + hashCode() + "]";
	}
	
}
