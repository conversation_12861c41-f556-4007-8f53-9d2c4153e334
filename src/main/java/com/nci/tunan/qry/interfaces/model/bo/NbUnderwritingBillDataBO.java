package com.nci.tunan.qry.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;
public class NbUnderwritingBillDataBO extends BaseBO{
	/** 
	* @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
	*/ 
	private static final long serialVersionUID = -5367359282571855695L;
	/**
	 * 135633 反洗钱标识
	 */
	private BigDecimal amlFlag;
	
	public BigDecimal getAmlFlag() {
		return amlFlag;
	}
	public void setAmlFlag(BigDecimal amlFlag) {
		this.amlFlag = amlFlag;
	}

	private String productCodes;
	private String occupationInsureanceFlags;
	private String chargeYears;
	
	/**
	 * 被保险人是否新市民
	 */
	private String isInsuredNewResident;
	/**
	 * 被保人录入顺序    984产品
	 */
	private BigDecimal inputSequence;
		
	public BigDecimal getInputSequence() {
		return inputSequence;
	}
	public void setInputSequence(BigDecimal inputSequence) {
		this.inputSequence = inputSequence;
	}
	public String getProductCodes() {
		return productCodes;
	}
	public void setProductCodes(String productCodes) {
		this.productCodes = productCodes;
	}
	public String getOccupationInsureanceFlags() {
		return occupationInsureanceFlags;
	}
	public void setOccupationInsureanceFlags(String occupationInsureanceFlags) {
		this.occupationInsureanceFlags = occupationInsureanceFlags;
	}
	public String getChargeYears() {
		return chargeYears;
	}
	public void setChargeYears(String chargeYears) {
		this.chargeYears = chargeYears;
	}

	/** 投保人手机号码核验结果 */
	private String phCheckPhone;
	/** 被保人手机号码核验结果 */
	private String liCheckPhone;
	/** 受益人手机号码核验结果 */
	private String beCheckPhone;
	/** 投保人手机号码验真结果 */
	private String phReturnPhone;
	/** 被保人手机号码验真结果 */
	private String liReturnPhone;
	/** 受益人手机号码验真结果 */
	private String beReturnPhone;
	
	
	private String channelType;//所属渠道
	private String channelName;//渠道名称
	private String applyCode;//投保单号
	private Date applyDate;//投保日期
	private BigDecimal ifGoodStart;//是否开门红活动
	private String hoiderName;//投保人
	private String holderResidentName;//投保人税收身份
	private String applicationName;//被保险人
	private String insuredResidentName;//被保险人税收身份
	private String customerLevel;//是否高端客户
	private BigDecimal submitChannel;//保单来源
	//private String policyCode;//签单状态
	private String causeType;//签单不成功原因
	private String operationTime;//签单状态时间
	private String isAccount;//是否足额到账
	private BigDecimal refundAmount;//退费金额
	private Date finishTime;//退费时间
	private String branchOrganCodeName;//分公司名称
	private String organCode;//机构代码
	private String thredOrganCodeName;//中心支公司
	private String fourOrganCodeName;//营销服务部
	private String serviceBank;//银行
	private String bankName;//银行网点
	private String agentCode;//业务员代码
	private String agentName;//业务员姓名
	//private String agentCode;//绩优等级
	private String agentLevel;//是否绩优
	/**
	 * 特殊人员类型
	 */
	private BigDecimal applicantSpePeople;

	/**
	 * 是否双录标识
	 */
	private String isDrqFlag;
	/**
	 * 	抽捡结果
	 */
	private BigDecimal isDrqDraw;
	/**
	 * 	抽捡结果
	 */
	private String isDrqDraw1;
	/**
	 * 是否双录标识
	 */
	private BigDecimal drqFlag;
	/**
   	 * 投被保险人关系
   	 */
	private String holderInsuredRelation;
	 /**
   	 * 被保险人与受益人关系
   	 */
	private String insuredBeneRelation;
 


	/**
   	 * 是否CRS产品
   	 */
	private String taxRevenueFlag;  
	public String getIsDrqFlag() {
		return isDrqFlag;
	}
	public void setIsDrqFlag(String isDrqFlag) {
		this.isDrqFlag = isDrqFlag;
	}
	public Date getCustomerBirthday() {
		return customerBirthday;
	}
	public void setCustomerBirthday(Date customerBirthday) {
		this.customerBirthday = customerBirthday;
	}
	public String getCustomerAge() {
		return customerAge;
	}
	public void setCustomerAge(String customerAge) {
		this.customerAge = customerAge;
	}

	/**
	 * 投保人出生日期
	 */
	private Date customerBirthday;

	/**
	 * 投保人年龄
	 */
	private String customerAge;
	private String startDate;//统计开始日期
	private String endDate;//统计结束日期
	private String startFinishTime;//溢交退费日期(起)
	private String endFinishTime;//溢交退费日期(止)
	private String policyCode;//保单号
	private BigDecimal organId;	//管理机构
	private String organName;//管理机构名称
	private String policyType;//销售渠道
	private String serviceBankName;//银行名称
	private String submitChannelName;//保单来源名称
	private String subinputTypeDesc;//银保通保单来源名称
	private String adminOrganCode;//营销服务部机构
	private String adminOrganName;//营销服务部名称
	private String updateTimeStamp; //签单状态时间
	private BigDecimal feeAmount; //溢交退费总金额
	private String proposalStatus; //签单状态
	private String insertTimeStamp;
	private String confirmWay;//移动平台确认方式
    private String productCode; //主险产品代码
    private String productName; //主险产品名称
	private BigDecimal totalPremAf;	//保费金额
	private BigDecimal totalPremAfs;	//首期保费合计
	private String chargeYear;//缴费期间
	private String chargePeriod;//缴费方式
	
	private String oldPolicyCode;//原保单号
	private String renewalFlag;//续保标识
	
	private String salesOrganNameQ;//区
	private String salesOrganNameB;//部
	private String salesOrganNameZ;//组
	private Date issueDate;//签单日期
	private Date validateDate;//保单生效日期
	private String liabilityStatusName;//保单效力状态
	private String isWinningFlag;//是否预承保
	private Date scanTime;//扫描日期
	private BigDecimal riskPrem;	//险种保费
	
	private String issueStartDate;//统计签单开始日期
	private String issueEndDate;//统计签单结束日期
	private String validateStartDate;//统计生效开始日期
	private String validateEndDate;//统计生效结束日期
	private String remoteSignature;//远程签名
	private String queryType;//统计方式
	private String hesitateFlag;//统计维度
	private Date feeTime;//退费时间
	private BigDecimal certFlag;//身份识别标记
	private String signStutasNotes;//签单不通过原因
	/**
	 * 是否提供关系证明
	 * @return
	 */
	private String relationshipFlag;
	/**
	 * 客户身份识别标记
	 * @return
	 */
	private String customerCertFlag;
	
	private String isRemoteAutograph;
	/**
	 * 是否职域产品标识
	 */
	private String occupationInsureanceFlag;
	/**
	 * 自保件标记
	 */
	private BigDecimal isSelfInsured;
	/**
	 * 互保件标记
	 * @return
	 */
	private BigDecimal isMutualInsured;
	/**
	 * 投保时间（时分秒）
	 */
	private String applyTime;
	
	/**
	 * 收费方式
	 */
	private String payModeName;

	/**
	 * 合同保费
	 */
	private BigDecimal totalPremAfContract;
	
	/** rm46080承保身份识别应用需求 strat */
	/** 投保人注销验真 */
	private String hcusCheckLogout;
	/** 被保人注销验真*/
	private String icusCheckLogout;
	/** 投保人人像比对 */
	private String hcusCheckPeopleImage;
	/** 被保人人像比对 */
	private String icusCheckPeopleImage;
	/** 投保人人证比对 */
	private String hcusCheckPeopleCode;
	/** 被保人人证比对 */
	private String icusCheckPeopleCode;
	/**
	 * 续保转保标识
	 */
	private String policyReinsureFlag;
	
	
	public String getPolicyReinsureFlag() {
		return policyReinsureFlag;
	}

	public void setPolicyReinsureFlag(String policyReinsureFlag) {
		this.policyReinsureFlag = policyReinsureFlag;
	}
	
	
	
	/**
	 * 是否绩优
	 */
	private String isQualityAgent;
	/**绩优等级码值*/
	private String agentLevelDesc;
	/**
	 *  第一被保人 特殊人员类型 1脱贫户、2边缘户、3-乡村人口、4-残疾人
	 */
	private String insSpePeople;
	
	/**
	 * 第一被保人 乡村人口标识
	 */
	private BigDecimal ruralPopulationFlag;
	/**
	 *  第一被保人 残疾人标识
	 */
	private BigDecimal disabilityFlag;
	/**
	 * 第二被保人 特殊人员类型 1脱贫户、2边缘户、3-乡村人口、4-残疾人
	 */
	private String insSpePeopleSec;
	
	/**
	 * 第二被保人 乡村人口标识
	 */
	private BigDecimal ruralPopulationFlagSec;
	/**
	 * 第二被保人 残疾人标识
	 */
	private BigDecimal disabilityFlagSec;

	/**
	 * 是否预录入
	 */
	private BigDecimal preEntryFlag;

	/**
	 * 是否复核修改
	 */
	private BigDecimal checkFlag;
	/**
	 * 是否复核修改结果
	 */
	private BigDecimal checkFlaged;
	/**
	 * 重投标识，0-首次投保，1-第一次重投，2-第二次重投
	 */
	private BigDecimal reviewFlag;
	/**
	 * @Fields taxDistrict : 纳税地点区/县RM132190
	 */
	private String taxDistrict;
	/**
	 * @Fields retirementAge : 退休年龄
	 */
	private BigDecimal retirementAge;
	/**
	 * @Fields busSreDeptCode : 所在单位的统一社会信用代码
	 */
	private String busSreDeptCode;
	/**
	 * @Fields insuredType : 参保人身份类型
	 */
	private String insuredType;
	/**
	 * @Fields taxNumber : 纳税人识别号RM132190
	 */
	private String taxNumber;
	
	/**
	 *  rm146055 共同参保保单
	 */
	private String jointlyInsuredPolicy;
	
	/**
	 * 投保人是否新市民
	 */
	private BigDecimal holderNewResident;
	/**
	 * 被保险人是否新市民
	 */
	private BigDecimal insuredNewResident;
	/**
	 * 第二被保险人是否新市民
	 */
	private BigDecimal insuredNewResidentSe;
	/**
	 * 投保人是否新市民
	 */
	private BigDecimal holderNewResidentReveal;
	/**
	 * 被保险人是否新市民
	 */
	private BigDecimal insuredNewResidentReveal;
	/**
	 * 第二被保险人是否新市民
	 */
	private BigDecimal insuredNewResidentSeReveal;
	/**
	 * 自保件标记(查询)
	 */
	private BigDecimal isSelfInsuredSe;
	/**
	 * 互保件标记（查询）
	 * @return
	 */
	private BigDecimal isMutualInsuredSe;
	
	
	public BigDecimal getIsSelfInsuredSe() {
		return isSelfInsuredSe;
	}
	public void setIsSelfInsuredSe(BigDecimal isSelfInsuredSe) {
		this.isSelfInsuredSe = isSelfInsuredSe;
	}
	public BigDecimal getIsMutualInsuredSe() {
		return isMutualInsuredSe;
	}
	public void setIsMutualInsuredSe(BigDecimal isMutualInsuredSe) {
		this.isMutualInsuredSe = isMutualInsuredSe;
	}
	public BigDecimal getHolderNewResidentReveal() {
		return holderNewResidentReveal;
	}
	public void setHolderNewResidentReveal(BigDecimal holderNewResidentReveal) {
		this.holderNewResidentReveal = holderNewResidentReveal;
	}
	public BigDecimal getInsuredNewResidentReveal() {
		return insuredNewResidentReveal;
	}
	public void setInsuredNewResidentReveal(BigDecimal insuredNewResidentReveal) {
		this.insuredNewResidentReveal = insuredNewResidentReveal;
	}
	public BigDecimal getInsuredNewResidentSeReveal() {
		return insuredNewResidentSeReveal;
	}
	public void setInsuredNewResidentSeReveal(BigDecimal insuredNewResidentSeReveal) {
		this.insuredNewResidentSeReveal = insuredNewResidentSeReveal;
	}
	public BigDecimal getHolderNewResident() {
		return holderNewResident;
	}
	public void setHolderNewResident(BigDecimal holderNewResident) {
		this.holderNewResident = holderNewResident;
	}
	public BigDecimal getInsuredNewResident() {
		return insuredNewResident;
	}
	public void setInsuredNewResident(BigDecimal insuredNewResident) {
		this.insuredNewResident = insuredNewResident;
	}
	public BigDecimal getInsuredNewResidentSe() {
		return insuredNewResidentSe;
	}
	public void setInsuredNewResidentSe(BigDecimal insuredNewResidentSe) {
		this.insuredNewResidentSe = insuredNewResidentSe;
	}
	public String getJointlyInsuredPolicy() {
		return jointlyInsuredPolicy;
	}
	public void setJointlyInsuredPolicy(String jointlyInsuredPolicy) {
		this.jointlyInsuredPolicy = jointlyInsuredPolicy;
	}
	
	
	
	
	public String getTaxDistrict() {
		return taxDistrict;
	}
	public void setTaxDistrict(String taxDistrict) {
		this.taxDistrict = taxDistrict;
	}
	public BigDecimal getRetirementAge() {
		return retirementAge;
	}
	public void setRetirementAge(BigDecimal retirementAge) {
		this.retirementAge = retirementAge;
	}
	public String getBusSreDeptCode() {
		return busSreDeptCode;
	}
	public void setBusSreDeptCode(String busSreDeptCode) {
		this.busSreDeptCode = busSreDeptCode;
	}
	public String getInsuredType() {
		return insuredType;
	}
	public void setInsuredType(String insuredType) {
		this.insuredType = insuredType;
	}
	public String getTaxNumber() {
		return taxNumber;
	}
	public void setTaxNumber(String taxNumber) {
		this.taxNumber = taxNumber;
	}
	public BigDecimal getCheckFlag() {
		return checkFlag;
	}
	public void setCheckFlag(BigDecimal checkFlag) {
		this.checkFlag = checkFlag;
	}
	public BigDecimal getCheckFlaged() {
		return checkFlaged;
	}
	public void setCheckFlaged(BigDecimal checkFlaged) {
		this.checkFlaged = checkFlaged;
	}
	public BigDecimal getReviewFlag() {
		return reviewFlag;
	}
	public void setReviewFlag(BigDecimal reviewFlag) {
		this.reviewFlag = reviewFlag;
	}
	public BigDecimal getPreEntryFlag() {
		return preEntryFlag;
	}
	public void setPreEntryFlag(BigDecimal preEntryFlag) {
		this.preEntryFlag = preEntryFlag;
	}
	public String getInsSpePeople() {
		return insSpePeople;
	}
	public void setInsSpePeople(String insSpePeople) {
		this.insSpePeople = insSpePeople;
	}
	public BigDecimal getRuralPopulationFlag() {
		return ruralPopulationFlag;
	}
	public void setRuralPopulationFlag(BigDecimal ruralPopulationFlag) {
		this.ruralPopulationFlag = ruralPopulationFlag;
	}
	public BigDecimal getDisabilityFlag() {
		return disabilityFlag;
	}
	public void setDisabilityFlag(BigDecimal disabilityFlag) {
		this.disabilityFlag = disabilityFlag;
	}
	public String getInsSpePeopleSec() {
		return insSpePeopleSec;
	}
	public void setInsSpePeopleSec(String insSpePeopleSec) {
		this.insSpePeopleSec = insSpePeopleSec;
	}
	public BigDecimal getRuralPopulationFlagSec() {
		return ruralPopulationFlagSec;
	}
	public void setRuralPopulationFlagSec(BigDecimal ruralPopulationFlagSec) {
		this.ruralPopulationFlagSec = ruralPopulationFlagSec;
	}
	public BigDecimal getDisabilityFlagSec() {
		return disabilityFlagSec;
	}
	public void setDisabilityFlagSec(BigDecimal disabilityFlagSec) {
		this.disabilityFlagSec = disabilityFlagSec;
	}

	public String getIsQualityAgent() {
		return isQualityAgent;
	}
	public void setIsQualityAgent(String isQualityAgent) {
		this.isQualityAgent = isQualityAgent;
	}

	public String getAgentLevelDesc() {
		return agentLevelDesc;
	}

	public void setAgentLevelDesc(String agentLevelDesc) {
		this.agentLevelDesc = agentLevelDesc;
	}

	
	
	
	
	public String getHcusCheckLogout() {
		return hcusCheckLogout;
	}

	public void setHcusCheckLogout(String hcusCheckLogout) {
		this.hcusCheckLogout = hcusCheckLogout;
	}

	public String getIcusCheckLogout() {
		return icusCheckLogout;
	}

	public void setIcusCheckLogout(String icusCheckLogout) {
		this.icusCheckLogout = icusCheckLogout;
	}

	public String getHcusCheckPeopleImage() {
		return hcusCheckPeopleImage;
	}

	public void setHcusCheckPeopleImage(String hcusCheckPeopleImage) {
		this.hcusCheckPeopleImage = hcusCheckPeopleImage;
	}

	public String getIcusCheckPeopleImage() {
		return icusCheckPeopleImage;
	}

	public void setIcusCheckPeopleImage(String icusCheckPeopleImage) {
		this.icusCheckPeopleImage = icusCheckPeopleImage;
	}

	public String getHcusCheckPeopleCode() {
		return hcusCheckPeopleCode;
	}

	public void setHcusCheckPeopleCode(String hcusCheckPeopleCode) {
		this.hcusCheckPeopleCode = hcusCheckPeopleCode;
	}

	public String getIcusCheckPeopleCode() {
		return icusCheckPeopleCode;
	}

	public void setIcusCheckPeopleCode(String icusCheckPeopleCode) {
		this.icusCheckPeopleCode = icusCheckPeopleCode;
	}
	/** rm46080承保身份识别应用需求 end */
	public BigDecimal getTotalPremAfContract() {
		return totalPremAfContract;
	}

	public void setTotalPremAfContract(BigDecimal totalPremAfContract) {
		this.totalPremAfContract = totalPremAfContract;
	}

	public String getPayModeName() {
		return payModeName;
	}

	public void setPayModeName(String payModeName) {
		this.payModeName = payModeName;
	}
	
	public String getApplyTime() {
		return applyTime;
	}
	
	public void setApplyTime(String applyTime) {
		this.applyTime = applyTime;
	}

	public BigDecimal getIsMutualInsured() {
		return isMutualInsured;
	}

	public void setIsMutualInsured(BigDecimal isMutualInsured) {
		this.isMutualInsured = isMutualInsured;
	}

	public BigDecimal getIsSelfInsured() {
		return isSelfInsured;
	}

	public void setIsSelfInsured(BigDecimal isSelfInsured) {
		this.isSelfInsured = isSelfInsured;
	}
	
	public String getOccupationInsureanceFlag() {
		return occupationInsureanceFlag;
	}

	public void setOccupationInsureanceFlag(String occupationInsureanceFlag) {
		this.occupationInsureanceFlag = occupationInsureanceFlag;
	}
	public String getRelationshipFlag() {
		return relationshipFlag;
	}

	public void setRelationshipFlag(String relationshipFlag) {
		this.relationshipFlag = relationshipFlag;
	}

	public String getCustomerCertFlag() {
		return customerCertFlag;
	}

	public void setCustomerCertFlag(String customerCertFlag) {
		this.customerCertFlag = customerCertFlag;
	}

	public String getSubinputTypeDesc() {
		return subinputTypeDesc;
	}

	public void setSubinputTypeDesc(String subinputTypeDesc) {
		this.subinputTypeDesc = subinputTypeDesc;
	}

	public String getHesitateFlag() {
		return hesitateFlag;
	}

	public void setHesitateFlag(String hesitateFlag) {
		this.hesitateFlag = hesitateFlag;
	}
	public String getRemoteSignature() {
		return remoteSignature;
	}

	public void setRemoteSignature(String remoteSignature) {
		this.remoteSignature = remoteSignature;
	}
	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getChargeYear() {
		return chargeYear;
	}

	public void setChargeYear(String chargeYear) {
		this.chargeYear = chargeYear;
	}

	public String getIssueStartDate() {
		return issueStartDate;
	}

	public void setIssueStartDate(String issueStartDate) {
		this.issueStartDate = issueStartDate;
	}

	public String getIssueEndDate() {
		return issueEndDate;
	}

	public void setIssueEndDate(String issueEndDate) {
		this.issueEndDate = issueEndDate;
	}

	public String getValidateStartDate() {
		return validateStartDate;
	}

	public void setValidateStartDate(String validateStartDate) {
		this.validateStartDate = validateStartDate;
	}

	public String getValidateEndDate() {
		return validateEndDate;
	}

	public void setValidateEndDate(String validateEndDate) {
		this.validateEndDate = validateEndDate;
	}
	
	public String getSalesOrganNameQ() {
		return salesOrganNameQ;
	}

	public void setSalesOrganNameQ(String salesOrganNameQ) {
		this.salesOrganNameQ = salesOrganNameQ;
	}

	public String getSalesOrganNameB() {
		return salesOrganNameB;
	}

	public void setSalesOrganNameB(String salesOrganNameB) {
		this.salesOrganNameB = salesOrganNameB;
	}

	public String getSalesOrganNameZ() {
		return salesOrganNameZ;
	}

	public void setSalesOrganNameZ(String salesOrganNameZ) {
		this.salesOrganNameZ = salesOrganNameZ;
	}

	public Date getIssueDate() {
		return issueDate;
	}

	public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}

	public Date getValidateDate() {
		return validateDate;
	}

	public void setValidateDate(Date validateDate) {
		this.validateDate = validateDate;
	}

	public String getLiabilityStatusName() {
		return liabilityStatusName;
	}

	public void setLiabilityStatusName(String liabilityStatusName) {
		this.liabilityStatusName = liabilityStatusName;
	}

	public String getIsWinningFlag() {
		return isWinningFlag;
	}

	public void setIsWinningFlag(String isWinningFlag) {
		this.isWinningFlag = isWinningFlag;
	}

	public Date getScanTime() {
		return scanTime;
	}

	public void setScanTime(Date scanTime) {
		this.scanTime = scanTime;
	}

	public BigDecimal getRiskPrem() {
		return riskPrem;
	}

	public void setRiskPrem(BigDecimal riskPrem) {
		this.riskPrem = riskPrem;
	}

	public String getOldPolicyCode() {
		return oldPolicyCode;
	}

	public void setOldPolicyCode(String oldPolicyCode) {
		this.oldPolicyCode = oldPolicyCode;
	}

	public String getRenewalFlag() {
		return renewalFlag;
	}

	public void setRenewalFlag(String renewalFlag) {
		this.renewalFlag = renewalFlag;
	}

	public String getInsertTimeStamp() {
		return insertTimeStamp;
	}

	public void setInsertTimeStamp(String insertTimeStamp) {
		this.insertTimeStamp = insertTimeStamp;
	}

	public String getStartFinishTime() {
		return startFinishTime;
	}

	public void setStartFinishTime(String startFinishTime) {
		this.startFinishTime = startFinishTime;
	}

	public String getEndFinishTime() {
		return endFinishTime;
	}

	public void setEndFinishTime(String endFinishTime) {
		this.endFinishTime = endFinishTime;
	}

	public String getProposalStatus() {
		return proposalStatus;
	}

	public void setProposalStatus(String proposalStatus) {
		this.proposalStatus = proposalStatus;
	}

	public String getUpdateTimeStamp() {
		return updateTimeStamp;
	}

	public void setUpdateTimeStamp(String updateTimeStamp) {
		this.updateTimeStamp = updateTimeStamp;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}
	
	public String getAdminOrganCode() {
		return adminOrganCode;
	}

	public void setAdminOrganCode(String adminOrganCode) {
		this.adminOrganCode = adminOrganCode;
	}

	public String getAdminOrganName() {
		return adminOrganName;
	}

	public void setAdminOrganName(String adminOrganName) {
		this.adminOrganName = adminOrganName;
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getSubmitChannelName() {
		return submitChannelName;
	}

	public void setSubmitChannelName(String submitChannelName) {
		this.submitChannelName = submitChannelName;
	}

	public String getServiceBankName() {
		return serviceBankName;
	}

	public void setServiceBankName(String serviceBankName) {
		this.serviceBankName = serviceBankName;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public BigDecimal getIfGoodStart() {
		return ifGoodStart;
	}

	public void setIfGoodStart(BigDecimal ifGoodStart) {
		this.ifGoodStart = ifGoodStart;
	}

	public String getHoiderName() {
		return hoiderName;
	}

	public void setHoiderName(String hoiderName) {
		this.hoiderName = hoiderName;
	}

	public String getHolderResidentName() {
		return holderResidentName;
	}

	public void setHolderResidentName(String holderResidentName) {
		this.holderResidentName = holderResidentName;
	}

	public String getApplicationName() {
		return applicationName;
	}

	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

	public String getInsuredResidentName() {
		return insuredResidentName;
	}

	public void setInsuredResidentName(String insuredResidentName) {
		this.insuredResidentName = insuredResidentName;
	}

	public String getCauseType() {
		return causeType;
	}

	public void setCauseType(String causeType) {
		this.causeType = causeType;
	}

	public BigDecimal getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public Date getFinishTime() {
		return finishTime;
	}

	public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public BigDecimal getOrganId() {
		return organId;
	}

	public void setOrganId(BigDecimal organId) {
		this.organId = organId;
	}

	public String getPolicyType() {
		return policyType;
	}

	public void setPolicyType(String policyType) {
		this.policyType = policyType;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getCustomerLevel() {
		return customerLevel;
	}

	public void setCustomerLevel(String customerLevel) {
		this.customerLevel = customerLevel;
	}

	public BigDecimal getSubmitChannel() {
		return submitChannel;
	}

	public void setSubmitChannel(BigDecimal submitChannel) {
		this.submitChannel = submitChannel;
	}

	public String getOperationTime() {
		return operationTime;
	}

	public void setOperationTime(String operationTime) {
		this.operationTime = operationTime;
	}

	public String getIsAccount() {
		return isAccount;
	}

	public void setIsAccount(String isAccount) {
		this.isAccount = isAccount;
	}

	public String getBranchOrganCodeName() {
		return branchOrganCodeName;
	}

	public void setBranchOrganCodeName(String branchOrganCodeName) {
		this.branchOrganCodeName = branchOrganCodeName;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getThredOrganCodeName() {
		return thredOrganCodeName;
	}

	public void setThredOrganCodeName(String thredOrganCodeName) {
		this.thredOrganCodeName = thredOrganCodeName;
	}

	public String getFourOrganCodeName() {
		return fourOrganCodeName;
	}

	public void setFourOrganCodeName(String fourOrganCodeName) {
		this.fourOrganCodeName = fourOrganCodeName;
	}

	public String getServiceBank() {
		return serviceBank;
	}

	public void setServiceBank(String serviceBank) {
		this.serviceBank = serviceBank;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getAgentLevel() {
		return agentLevel;
	}

	public void setAgentLevel(String agentLevel) {
		this.agentLevel = agentLevel;
	}

    public String getConfirmWay() {
        return confirmWay;
    }

    public void setConfirmWay(String confirmWay) {
        this.confirmWay = confirmWay;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
	
	public BigDecimal getTotalPremAf() {
		return totalPremAf;
	}

	public void setTotalPremAf(BigDecimal totalPremAf) {
		this.totalPremAf = totalPremAf;
	}

	public String getQueryType() {
		return queryType;
	}

	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}

	public Date getFeeTime() {
		return feeTime;
	}

	public void setFeeTime(Date feeTime) {
		this.feeTime = feeTime;
	}

	public BigDecimal getCertFlag() {
		return certFlag;
	}

	public void setCertFlag(BigDecimal certFlag) {
		this.certFlag = certFlag;
	}

	public String getSignStutasNotes() {
		return signStutasNotes;
	}

	public void setSignStutasNotes(String signStutasNotes) {
		this.signStutasNotes = signStutasNotes;
	}

	public String getIsRemoteAutograph() {
		return isRemoteAutograph;
	}

	public void setIsRemoteAutograph(String isRemoteAutograph) {
		this.isRemoteAutograph = isRemoteAutograph;
	}
	
	
	public BigDecimal getTotalPremAfs() {
		return totalPremAfs;
	}

	public void setTotalPremAfs(BigDecimal totalPremAfs) {
		this.totalPremAfs = totalPremAfs;
	}

	public String getChargePeriod() {
		return chargePeriod;
	}

	public void setChargePeriod(String chargePeriod) {
		this.chargePeriod = chargePeriod;
	}

	public String getPhCheckPhone() {
		return phCheckPhone;
	}

	public void setPhCheckPhone(String phCheckPhone) {
		this.phCheckPhone = phCheckPhone;
	}

	public String getLiCheckPhone() {
		return liCheckPhone;
	}

	public void setLiCheckPhone(String liCheckPhone) {
		this.liCheckPhone = liCheckPhone;
	}

	public String getBeCheckPhone() {
		return beCheckPhone;
	}

	public void setBeCheckPhone(String beCheckPhone) {
		this.beCheckPhone = beCheckPhone;
	}

	public BigDecimal getApplicantSpePeople() {
		return applicantSpePeople;
	}

	public void setApplicantSpePeople(BigDecimal applicantSpePeople) {
		this.applicantSpePeople = applicantSpePeople;
	}
	public String getPhReturnPhone() {
		return phReturnPhone;
	}
	public void setPhReturnPhone(String phReturnPhone) {
		this.phReturnPhone = phReturnPhone;
	}
	public String getLiReturnPhone() {
		return liReturnPhone;
	}
	public void setLiReturnPhone(String liReturnPhone) {
		this.liReturnPhone = liReturnPhone;
	}
	public String getBeReturnPhone() {
		return beReturnPhone;
	}
	public void setBeReturnPhone(String beReturnPhone) {
		this.beReturnPhone = beReturnPhone;
	}
	
	public String getIsInsuredNewResident() {
		return isInsuredNewResident;
	}
	public void setIsInsuredNewResident(String isInsuredNewResident) {
		this.isInsuredNewResident = isInsuredNewResident;
	}
	public String getHolderInsuredRelation() {
		return holderInsuredRelation;
	}
	public void setHolderInsuredRelation(String holderInsuredRelation) {
		this.holderInsuredRelation = holderInsuredRelation;
	}
	public String getInsuredBeneRelation() {
		return insuredBeneRelation;
	}
	public void setInsuredBeneRelation(String insuredBeneRelation) {
		this.insuredBeneRelation = insuredBeneRelation;
	}
	public String getTaxRevenueFlag() {
		return taxRevenueFlag;
	}
	public void setTaxRevenueFlag(String taxRevenueFlag) {
		this.taxRevenueFlag = taxRevenueFlag;
	}
	@Override
	public String toString() {
		return "NbUnderwritingBillDataBO [channelType=" + channelType
				+ ", channelName=" + channelName + ", applyCode=" + applyCode
				+ ", applyDate=" + applyDate + ", ifGoodStart=" + ifGoodStart
				+ ", hoiderName=" + hoiderName + ", holderResidentName="
				+ holderResidentName + ", applicationName=" + applicationName
				+ ", insuredResidentName=" + insuredResidentName
				+ ", customerLevel=" + customerLevel + ", submitChannel="
				+ submitChannel + ", causeType=" + causeType
				+ ", operationTime=" + operationTime + ", isAccount="
				+ isAccount + ", refundAmount=" + refundAmount
				+ ", finishTime=" + finishTime + ", branchOrganCodeName="
				+ branchOrganCodeName + ", organCode=" + organCode
				+ ", thredOrganCodeName=" + thredOrganCodeName
				+ ", fourOrganCodeName=" + fourOrganCodeName + ", serviceBank="
				+ serviceBank + ", bankName=" + bankName + ", agentCode="
				+ agentCode + ", agentName=" + agentName + ", agentLevel="
				+ agentLevel + ", startDate=" + startDate + ", endDate="
				+ endDate + ", startFinishTime=" + startFinishTime
				+ ", endFinishTime=" + endFinishTime + ", policyCode="
				+ policyCode + ", organId=" + organId + ", organName="
				+ organName + ", policyType=" + policyType
				+ ", serviceBankName=" + serviceBankName
				+ ", submitChannelName=" + submitChannelName
				+ ", subinputTypeDesc=" + subinputTypeDesc
				+ ", adminOrganCode=" + adminOrganCode + ", adminOrganName="
				+ adminOrganName + ", updateTimeStamp=" + updateTimeStamp
				+ ", feeAmount=" + feeAmount + ", proposalStatus="
				+ proposalStatus + ", insertTimeStamp=" + insertTimeStamp
				+ ", confirmWay=" + confirmWay + ", productCode=" + productCode
				+ ", productName=" + productName + ", totalPremAf="
				+ totalPremAf + ", chargeYear=" + chargeYear
				+ ", oldPolicyCode=" + oldPolicyCode + ", renewalFlag="
				+ renewalFlag + ", salesOrganNameQ=" + salesOrganNameQ
				+ ", salesOrganNameB=" + salesOrganNameB + ", salesOrganNameZ="
				+ salesOrganNameZ + ", issueDate=" + issueDate
				+ ", validateDate=" + validateDate + ", liabilityStatusName="
				+ liabilityStatusName + ", isWinningFlag=" + isWinningFlag
				+ ", scanTime=" + scanTime + ", riskPrem=" + riskPrem
				+ ", issueStartDate=" + issueStartDate + ", issueEndDate="
				+ issueEndDate + ", validateStartDate=" + validateStartDate
				+ ", validateEndDate=" + validateEndDate + ", remoteSignature="
				+ remoteSignature + ", queryType=" + queryType
				+ ", hesitateFlag=" + hesitateFlag + ", feeTime=" + feeTime
				+ ", certFlag=" + certFlag + ", signStutasNotes="
				+ signStutasNotes + ", relationshipFlag=" + relationshipFlag
				+ ", customerCertFlag=" + customerCertFlag
				+ ", isRemoteAutograph=" + isRemoteAutograph 
				+ ", totalPremAfs=" + totalPremAfs 
				+ ", chargePeriod=" + chargePeriod 
				+ "]";
	} 
	public String getIsDrqDraw1() {
		return isDrqDraw1;
	}
	public void setIsDrqDraw1(String isDrqDraw1) {
		this.isDrqDraw1 = isDrqDraw1;
	}
	public BigDecimal getIsDrqDraw() {
		return isDrqDraw;
	}
	public void setIsDrqDraw(BigDecimal isDrqDraw) {
		this.isDrqDraw = isDrqDraw;
	}
	public BigDecimal getDrqFlag() {
		return drqFlag;
	}
	public void setDrqFlag(BigDecimal drqFlag) {
		this.drqFlag = drqFlag;
	}

	
}
