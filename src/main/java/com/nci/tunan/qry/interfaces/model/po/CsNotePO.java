package com.nci.tunan.qry.interfaces.model.po;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description CsNotePO对象
 * <AUTHOR> 
 * @date 2015-06-03 15:58:24  
 */
public class CsNotePO extends BasePO {
 	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
		// noteId  ---  BigDecimal  ---  NUMBER_16_0_��±���;
		// noteContent  ---  String  ---  VARCHAR2_2000_0_��;
				// changeId  ---  BigDecimal  ---  NUMBER_16_0_��ȫ��Id;
		// currProcessPoint  ---  String  ---  VARCHAR2_3_0_��ǰ�ڵ㣨��,¼�,�˱�,���ˣ�;
				// policyChgId  ---  BigDecimal  ---  NUMBER_16_0_������Id;
		
			public void setNoteId(BigDecimal noteId){
		setBigDecimal("note_id", noteId);
	}
	
	public BigDecimal getNoteId(){
		return getBigDecimal("note_id");
	}
		public void setNoteContent(String noteContent){
		setString("note_content", noteContent);
	}
	
	public String getNoteContent(){
		return getString("note_content");
	}
				public void setChangeId(BigDecimal changeId){
		setBigDecimal("change_id", changeId);
	}
	
	public BigDecimal getChangeId(){
		return getBigDecimal("change_id");
	}
		public void setCurrProcessPoint(String currProcessPoint){
		setString("curr_process_point", currProcessPoint);
	}
	
	public String getCurrProcessPoint(){
		return getString("curr_process_point");
	}
				public void setPolicyChgId(BigDecimal policyChgId){
		setBigDecimal("policy_chg_id", policyChgId);
	}
	
	public BigDecimal getPolicyChgId(){
		return getBigDecimal("policy_chg_id");
	}
			
	@Override
    public String toString() {
        return "CsNotePO [" +
				"noteId="+getNoteId()+","+
"noteContent="+getNoteContent()+","+
"changeId="+getChangeId()+","+
"currProcessPoint="+getCurrProcessPoint()+","+
"policyChgId="+getPolicyChgId()+"]";
    }	
 }
