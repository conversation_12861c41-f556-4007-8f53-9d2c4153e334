package com.nci.tunan.qry.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;
import com.nci.udmp.util.lang.DateUtilsEx;

public class ClaimCaseDetailVO extends BaseVO{
	/**
     * @Fields trusteeCertiCode : 省
     */
    private String province;
    /**
     * @Fields trusteeCertiCode : 市
     */
    private String city;
    /**
     * @Fields trusteeCertiCode : 县
     */
    private String area;
    /**
     * @Fields trusteeCertiCode : 受托人证件号码
     */
    private String trusteeCertiCode;
    /**
     * @Fields endCaseTime : 结案时间
     */
    private Date endCaseTime;
    /**
     * @Fields actualPay : 实际给付金额
     */
    private BigDecimal actualPay;
    /**
     * @Fields reportMode : 报案方式
     */
    private BigDecimal reportMode;
    /**
     * @Fields approveRejectReason : 审批不通过原因
     */
    private String approveRejectReason;
    /**
     * @Fields relatedNo : 关联赔案
     */
    private String relatedNo;
    /**
     * @Fields rptrRelation : 报案人与出险人关系
     */
    private String rptrRelation;
    /**
     * @Fields overCompFlag : 超期补偿标识
     */
    private BigDecimal overCompFlag;
    /**
     * @Fields otherReason : 审核其他原因
     */
    private String otherReason;
    /**
     * @Fields repealReason : 案件撤销原因
     */
    private BigDecimal repealReason;
    /**
     * @Fields isCommon : 是否为常规给付
     */
    private BigDecimal isCommon;
    /**
     * @Fields cureHospital : 治疗医院
     */
    private String cureHospital;
    /**
     * @Fields caseNo : 赔案号
     */
    private String caseNo;
    /**
     * @Fields reviewFlag : 复核标识
     */
    private BigDecimal reviewFlag;
    /**
     * @Fields organCode : 报案管理机构
     */
    private String organCode;
    /**
     * @Fields organName : 报案管理机构名称
     */
    private String organName;
    /**
     * @Fields advanceAskFlag : 客户是否要求预付
     */
    private BigDecimal advanceAskFlag;
    /**
     * @Fields caseFlag : 案件标识 -普通案件 -疑难案件 -诉讼案件
     */
    private BigDecimal caseFlag;
    /**
     * @Fields greenFlag : 绿色通道标识
     */
    private BigDecimal greenFlag;
    /**
     * @Fields auditRejectReason : 审核拒付原因
     */
    private String auditRejectReason;
    /**
     * @Fields balancePay : 结算金额
     */
    private BigDecimal balancePay;
    /**
     * @Fields rejectReason : 不予立案原因
     */
    private String rejectReason;
    /**
     * @Fields auditorId : 审核人ID
     */
    private BigDecimal auditorId;
    /**
     * @Fields rptrMp : 报案人手机号
     */
    private String rptrMp;
    /**
     * @Fields seriousDisease : 重大疾病
     */
    private String seriousDisease;
    /**
     * @Fields registeTime : 立案时间
     */
    private Date registeTime;
    /** 
    * @Fields startTime : 起期
    */ 
    private Date startTime;
    /** 
    * @Fields endTime : 止期 
    */ 
    private Date endTime;
    /**
     * @Fields caseApplyType : 赔案申请类型
     */
    private BigDecimal caseApplyType;
    /**
     * @Fields repealDesc : 案件撤销原因描述
     */
    private String repealDesc;
    /**
     * @Fields rptrEmail : 报案人EMAIL
     */
    private String rptrEmail;
    /**
     * @Fields trusteeCertiType : 受托人证件类型
     */
    private String trusteeCertiType;
    /**
     * @Fields insuredId : 出险人号
     */
    private BigDecimal insuredId;
    /**
     * @Fields accidentDetail : 意外细节
     */
    private String accidentDetail;
    /**
     * @Fields caseStatus : 案件状态
     */
    private String caseStatus;
    /**
     * @Fields caseId : 赔案ID
     */
    private BigDecimal caseId;
    /**
     * @Fields rptrTime : 报案确认时间
     */
    private Date rptrTime;
    /**
     * @Fields auditDecision : 审核结论
     */
    private BigDecimal auditDecision;
    /**
     * @Fields registerId : 立案人ID
     */
    private BigDecimal registerId;
    /**
     * @Fields claimSource : 案件来源 TPA
     */
    private BigDecimal claimSource;
    /**
     * @Fields trusteeType : 受托人类型
     */
    private BigDecimal trusteeType;
    /**
     * @Fields approverId : 审批人ID
     */
    private BigDecimal approverId;
    /**
     * @Fields accidentId : 事件ID
     */
    private BigDecimal accidentId;
    /**
     * @Fields rejectPay : 拒付金额
     */
    private BigDecimal rejectPay;
    /**
     * @Fields signTime : 签收确认时间
     */
    private Date signTime;
    /**
     * @Fields signerId : 签收人ID
     */
    private BigDecimal signerId;
    /**
     * @Fields advancePay : 预付金额
     */
    private BigDecimal advancePay;
    /**
     * @Fields rptrName : 报案人的姓名
     */
    private String rptrName;
    /**
     * @Fields rptrAddr : 报案人地址
     */
    private String rptrAddr;
    /**
     * @Fields approveRemark : 审批意见
     */
    private String approveRemark;
    /**
     * @Fields trusteeName : 受托人姓名
     */
    private String trusteeName;
    /**
     * @Fields applyDate : 申请日期
     */
    private Date applyDate;
    /**
     * @Fields advanceFlag : 预付标识
     */
    private BigDecimal advanceFlag;
    /**
     * @Fields registeConfTime : 立案确认时间
     */
    private Date registeConfTime;
    /**
     * @Fields trusteeTel : 受托人固定电话
     */
    private String trusteeTel;
    /**
     * @Fields rptrZip : 报案人邮编
     */
    private BigDecimal rptrZip;
    /**
     * @Fields isBpo : 是否外包
     */
    private BigDecimal isBpo;
    /**
     * @Fields auditTime : 审核时间
     */
    private Date auditTime;
    /**
     * @Fields approveTime : 审批时间
     */
    private Date approveTime;
    /**
     * @Fields auditRemark : 审核意见
     */
    private String auditRemark;
    /**
     * @Fields approveDecision : 审批结论
     */
    private BigDecimal approveDecision;
    /**
     * @Fields trusteeCode : 受托人代码
     */
    private String trusteeCode;
    /**
     * @Fields doorSignTime : 上门签收日期
     */
    private Date doorSignTime;
    /**
     * @Fields comfortFlag : 慰问标识
     */
    private BigDecimal comfortFlag;
    /**
     * @Fields acceptTime : 受理时间
     */
    private Date acceptTime;
    /**
     * @Fields isDeductFlag : 是否扣减欠缴费用
     */
    private BigDecimal isDeductFlag;
    /**
     * @Fields medDept : 医疗科室
     */
    private String medDept;
    /**
     * @Fields doctorName : 医生姓名
     */
    private String doctorName;
    /**
     * @Fields trusteeMp : 受托人手机码
     */
    private String trusteeMp;
    /**
     * @Fields cureStatus : 治疗情况
     */
    private String cureStatus;
    /**
     * @Fields acceptDecision : 立案结论
     */
    private BigDecimal acceptDecision;
    /**
     * @Fields acceptorId : 受理人ID
     */
    private BigDecimal acceptorId;
    /**
     * @Fields caseSubStatus : 案件子状态
     */
    private String caseSubStatus;
    /**
     * @Fields calcPay : 理算金额
     */
    private BigDecimal calcPay;
    /**
     * @Fields userRealName :报案受理人的名字
     */
    private String userRealName;
    /**
     * @Fields systemDateString :获取系统的时间
     */
    private String systemDateString;

    private Integer customerSex;
    /**
     * @Fields policyCode : 保单号
     */
    private String policyCode;
    /**
     * @Fields claimType : 理赔类型
     */
    private String claimType;
    /**
     * @Fields uwStatus : 二核状态
     */
    private String uwStatus;
    /**
     * @Fields uwStatus :  出险人的出生日期
     */
    private Date customerBirthday;

    //匹配理算类型 0：预算    1： 理算
    private String calcFlag;

    /**
     * @Fields policyType :保单类型
     */
    private String policyType;
    /**
     * @Fields productAbbrName :险种名称
     */
    private String productAbbrName;
    /**
     * @Fields fee_status :支付状态
     */
    private String feeStatus;
    /**
     * @Fields fee_status_date :支付日期
     */
    private Date feeStatusDate;

    private String approverName; //审批人
    
    private String auditorName; //审核人
    
    private String auditRealName; //审核人
    
    private String auditorgName; //审核机构
    
    private String userName; //用户代码
    
    private String insuredName; //出险人姓名
    
    private String insuredCode; //出险人证件号码
    /**
     * @Fields surveyStatus : 调查标识
     */
    private String surveyStatus;
    //豁免处理页面相关字段
    /** 
     * @Fields policyId :  保单ID
     */ 
    private BigDecimal policyId;
     /** 
      * @Fields busiItemId :  业务险种ID
      */ 
    private BigDecimal busiItemId;
    /** 
     * @Fields productNameStd :  险种名称
     */ 
    private String productNameStd;
    /** 
     * @Fields productCodeStd :  险种代码
     */ 
    private String productCodeStd;
    /** 
     * @Fields liabilityState :  险种效力状态
     */ 
    private BigDecimal liabilityState;
    /** 
     * @Fields paidupString :缴费终止日期
     */ 
    private String paidupString;
    /** 
     * @Fields paidupDate :  缴费终止日期
     */ 
    private Date paidupDate;
     /** 
      * @Fields totalPremAf :  总保费
      */ 
    private BigDecimal totalPremAf;
      /** 
       * @Fields itemId :  责任组ID
       */ 
    private BigDecimal itemId;
    /** 
     * @Fields initialType :  当期缴费频率
     */ 
     private BigDecimal initialType;
     /** 
      * @Fields initialType :  当期缴费频率
      */ 
     private String chargeName;
     /** 
      * @Fields isWaived :  豁免标识
      */ 
     private BigDecimal isWaived;
     /** 
      * @Fields waiveReason :  豁免原因
      */ 
     private String waiveReason;
     /** 
      * @Fields dueDate :  下期缴费日期
      */ 
      private Date dueDate;
       /** 
      * @Fields waiveStart :  豁免起期
      */ 
      private Date waiveStart;
      /** 
       * @Fields waiveStartString :  豁免起期
       */ 
       private String waiveStartString;
      /** 
       * @Fields waiveAmt :  豁免总金额
       */ 
       private BigDecimal waiveAmt;
        /** 
       * @Fields waiveEnd :  豁免止期
       */ 
     private Date waiveEnd;
    /** 
     * @Fields waiveEndString :  豁免止期
     */ 
     private String waiveEndString;
    /**
     * @Fields waiveDesc : 豁免描述
     */
     private String waiveDesc;
    /** 
     * @Fields expiryDate :  保单终止日期
     */ 
    private String expiryDate;
    
    private Date documentSDate; //单证生成时间起始
    
    private Date documentEDate; //单证生成时间结束
    
    private String documentType; //单证类型
    
    private String documentName; //单证名称
    /**
     * 传递报文数据 
     */
    private String context;

    private Date claimDate; //出险日期
    
    private BigDecimal rePrintTimes; //打印次数
    
    private Date printTime; //打印时间
    
    private Date initialPremDate; //保单缴费对应日
    
    private String caseType;//案件标识
    
    private String documentNo; //通知书号
    
    private String documentTName; //通知书名称
    
    private BigDecimal docListId; //通知书主键
    
    private String claimDateAll; //赔案关联所有出险日期
    
    private String caseResult; //赔案关联所有出险结果
    
    private String visitorName; //回访人
    
    private String visitMarker; //回访标志
    
    private Date nextVisitDate; //下次回访日期
    
    private BigDecimal careTimes; //关怀次数
    
    private String checkAll; //选择的单证集合
    
    private String seleNo; //每页显示条数
    
    private String seleNohid;
    
    private String casePermissionName; //案件审批权限
    
    private String accidentNo; //事件号
    
    private String problemNo; //问题件号
    
    private String cardCode; //单证代码列表
    private String cardCodeName; //单证代码列表
    private BigDecimal checked; //是否被选择
    
    //事故日期
    private Date accDate;
    //立案日期
    private Date registeDateStart;//统计起期
    private Date registeDateEnd;//统计止期
    private int days;//超时天数
    private Date statDate=new java.util.Date();//统计日期
    private Date policyUpdateTime;//保单挂起时间
    private String deformityType;//伤残理赔类型
    private BigDecimal customerVip;//客户vip标识
    private String auditDec;//审核结论描述
    private String caseFlagName;//案件类型
    private String accDesc;//事故描述
    /**
     * 审核不通过原因
     */
    private String auditNoPassReason;
    /**
     * 立案二级机构代码
     */
    private String registerSecondOrganCode;
    /**
     * 立案三级机构代码
     */
    private String registerThirdOrganCode;
    /**
     * 管理机构
     */
    private String manageOrganCode;
    /**
     * 渠道类型
     */
    private String channelType;
    /**
     * 审批起期
     */
    private Date approveStartDate;
    /**
     * 审批止期
     */
    private Date approveEndDate;
    /**
     * 案件关闭起期
     */
    private Date caseCloseStartDate;
    /**
     * 案件关闭止期
     */
    private Date caseCloseEndDate;
    /**
     * 回退起期
     */
    private Date caseBackStartDate;
    /**
     * 回退止期
     */
    private Date caseBackEndDate;
    /**
     * 调查标识
     */
    private String surveyFlag;
    /**
     * 调查阶段
     */
    private String surveySection;
    /**
     * 调查三级机构代码
     */
    private String thirdSurveyOrganCode; 
    /**
     * 调查三级机构名称
     */
    private String thirdSurveyOrganName;
    /**
     * 调查二级机构代码
     */
    private String secondSurveyOrganCode;
    /**
     * 调查二级机构名称
     */
    private String secondSurveyOrganName;
    /**
     * 第一调查人
     */
    private String firstSurveyPer;
    /**
     * 第二调查人
     */
    private String secondSurveyPer;
    /**
     * 调查类型
     */
    private String surveyType;
    /**
     * 拒付标识
     */
    private String isReject;
    /**
     * 回退标识
     */
    private String isBack;
    /**
     * 回退日期
     */
    private Date backTime;
	/**
 	 * 小额理赔标识
 	 */
 	private String caseIdentName;
	/**
	 * 报案方式名称
	 */
	private String reportModeName;
	/**
	 * 申请渠道
	 */
	private String channelCode;	
	/**
	 * 服务商
	 */
	private String serviceProvider;
	
	public String getReportModeName() {
		return reportModeName;
	}

	public void setReportModeName(String reportModeName) {
		this.reportModeName = reportModeName;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getServiceProvider() {
		return serviceProvider;
	}

	public void setServiceProvider(String serviceProvider) {
		this.serviceProvider = serviceProvider;
	}

 	public String getCaseIdentName() {
 		return caseIdentName;
 	}

 	public void setCaseIdentName(String caseIdentName) {
 		this.caseIdentName = caseIdentName;
 	}
    private String signName; //签收人
    private String registerName; //立案人

	/**
	 * 调用渠道
	 */
	private String leftFlag;
    
    
    
    public String getAuditRealName() {
		return auditRealName;
	}

	public void setAuditRealName(String auditRealName) {
		this.auditRealName = auditRealName;
	}

	public String getSignName() {
		return signName;
	}

	public void setSignName(String signName) {
		this.signName = signName;
	}

	public String getRegisterName() {
		return registerName;
	}

	public void setRegisterName(String registerName) {
		this.registerName = registerName;
	}

	public String getIsReject() {
		return isReject;
	}

	public void setIsReject(String isReject) {
		this.isReject = isReject;
	}

	public String getIsBack() {
		return isBack;
	}

	public void setIsBack(String isBack) {
		this.isBack = isBack;
	}

	public Date getBackTime() {
		return backTime;
	}

	public void setBackTime(Date backTime) {
		this.backTime = backTime;
	}

	public String getFirstSurveyPer() {
		return firstSurveyPer;
	}

	public void setFirstSurveyPer(String firstSurveyPer) {
		this.firstSurveyPer = firstSurveyPer;
	}

	public String getSecondSurveyPer() {
		return secondSurveyPer;
	}

	public void setSecondSurveyPer(String secondSurveyPer) {
		this.secondSurveyPer = secondSurveyPer;
	}

	public String getSurveyType() {
		return surveyType;
	}

	public void setSurveyType(String surveyType) {
		this.surveyType = surveyType;
	}

	public String getThirdSurveyOrganCode() {
		return thirdSurveyOrganCode;
	}

	public void setThirdSurveyOrganCode(String thirdSurveyOrganCode) {
		this.thirdSurveyOrganCode = thirdSurveyOrganCode;
	}

	public String getThirdSurveyOrganName() {
		return thirdSurveyOrganName;
	}

	public void setThirdSurveyOrganName(String thirdSurveyOrganName) {
		this.thirdSurveyOrganName = thirdSurveyOrganName;
	}

	public String getSecondSurveyOrganCode() {
		return secondSurveyOrganCode;
	}

	public void setSecondSurveyOrganCode(String secondSurveyOrganCode) {
		this.secondSurveyOrganCode = secondSurveyOrganCode;
	}

	public String getSecondSurveyOrganName() {
		return secondSurveyOrganName;
	}

	public void setSecondSurveyOrganName(String secondSurveyOrganName) {
		this.secondSurveyOrganName = secondSurveyOrganName;
	}

	public String getAuditNoPassReason() {
		return auditNoPassReason;
	}

	public void setAuditNoPassReason(String auditNoPassReason) {
		this.auditNoPassReason = auditNoPassReason;
	}

	public String getRegisterSecondOrganCode() {
		return registerSecondOrganCode;
	}

	public void setRegisterSecondOrganCode(String registerSecondOrganCode) {
		this.registerSecondOrganCode = registerSecondOrganCode;
	}

	public String getRegisterThirdOrganCode() {
		return registerThirdOrganCode;
	}

	public void setRegisterThirdOrganCode(String registerThirdOrganCode) {
		this.registerThirdOrganCode = registerThirdOrganCode;
	}

	public String getManageOrganCode() {
		return manageOrganCode;
	}

	public void setManageOrganCode(String manageOrganCode) {
		this.manageOrganCode = manageOrganCode;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public Date getApproveStartDate() {
		return approveStartDate;
	}

	public void setApproveStartDate(Date approveStartDate) {
		this.approveStartDate = approveStartDate;
	}

	public Date getApproveEndDate() {
		return approveEndDate;
	}

	public void setApproveEndDate(Date approveEndDate) {
		this.approveEndDate = approveEndDate;
	}

	public Date getCaseCloseStartDate() {
		return caseCloseStartDate;
	}

	public void setCaseCloseStartDate(Date caseCloseStartDate) {
		this.caseCloseStartDate = caseCloseStartDate;
	}

	public Date getCaseCloseEndDate() {
		return caseCloseEndDate;
	}

	public void setCaseCloseEndDate(Date caseCloseEndDate) {
		this.caseCloseEndDate = caseCloseEndDate;
	}

	public Date getCaseBackStartDate() {
		return caseBackStartDate;
	}

	public void setCaseBackStartDate(Date caseBackStartDate) {
		this.caseBackStartDate = caseBackStartDate;
	}

	public Date getCaseBackEndDate() {
		return caseBackEndDate;
	}

	public void setCaseBackEndDate(Date caseBackEndDate) {
		this.caseBackEndDate = caseBackEndDate;
	}

	public String getSurveyFlag() {
		return surveyFlag;
	}

	public void setSurveyFlag(String surveyFlag) {
		this.surveyFlag = surveyFlag;
	}

	public String getSurveySection() {
		return surveySection;
	}

	public void setSurveySection(String surveySection) {
		this.surveySection = surveySection;
	}

	public BigDecimal getChecked() {
        return checked;
    }

    public void setChecked(BigDecimal checked) {
        this.checked = checked;
    }

    public String getCardCodeName() {
        return cardCodeName;
    }

    public void setCardCodeName(String cardCodeName) {
        this.cardCodeName = cardCodeName;
    }

    public String getCardCode() {
        return cardCode;
    }

    public void setCardCode(String cardCode) {
        this.cardCode = cardCode;
    }

    public String getProblemNo() {
        return problemNo;
    }

    public void setProblemNo(String problemNo) {
        this.problemNo = problemNo;
    }

    public String getAccidentNo() {
        return accidentNo;
    }

    public void setAccidentNo(String accidentNo) {
        this.accidentNo = accidentNo;
    }

    public String getCasePermissionName() {
        return casePermissionName;
    }

    public void setCasePermissionName(String casePermissionName) {
        this.casePermissionName = casePermissionName;
    }

    public String getCalcFlag() {
        return calcFlag;
    }

    public void setCalcFlag(String calcFlag) {
        this.calcFlag = calcFlag;
    }
    
    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }
    
    public Date getCustomerBirthday() {
        return customerBirthday;
    }

    public void setCustomerBirthday(Date customerBirthday) {
        this.customerBirthday = customerBirthday;
    }
    
    public BigDecimal getDocListId() {
        return docListId;
    }

    public void setDocListId(BigDecimal docListId) {
        this.docListId = docListId;
    }

    public String getDocumentNo() {
        return documentNo;
    }

    public void setDocumentNo(String documentNo) {
        this.documentNo = documentNo;
    }

    public String getDocumentTName() {
        return documentTName;
    }

    public void setDocumentTName(String documentTName) {
        this.documentTName = documentTName;
    }

    public Date getDocumentSDate() {
        return documentSDate;
    }

    public void setDocumentSDate(Date documentSDate) {
        this.documentSDate = documentSDate;
    }

    public Date getDocumentEDate() {
        return documentEDate;
    }

    public void setDocumentEDate(Date documentEDate) {
        this.documentEDate = documentEDate;
    }

    public String getDocumentType() {
        return documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public Date getClaimDate() {
        return claimDate;
    }

    public void setClaimDate(Date claimDate) {
        this.claimDate = claimDate;
    }

    public BigDecimal getRePrintTimes() {
        return rePrintTimes;
    }

    public void setRePrintTimes(BigDecimal rePrintTimes) {
        this.rePrintTimes = rePrintTimes;
    }

    public Date getPrintTime() {
        return printTime;
    }

    public void setPrintTime(Date printTime) {
        this.printTime = printTime;
    }

    public BigDecimal getPrintBy() {
        return printBy;
    }

    public void setPrintBy(BigDecimal printBy) {
        this.printBy = printBy;
    }

    public String getCaseStatusName() {
        return caseStatusName;
    }

    public void setCaseStatusName(String caseStatusName) {
        this.caseStatusName = caseStatusName;
    }

    public String getCheckPrint() {
        return checkPrint;
    }

    public void setCheckPrint(String checkPrint) {
        this.checkPrint = checkPrint;
    }

    public BigDecimal getAccReason() {
        return accReason;
    }

    public void setAccReason(BigDecimal accReason) {
        this.accReason = accReason;
    }

    private BigDecimal printBy; //打印人
    
    private String printByName; //打印人名称
    
    private String caseStatusName; //案件状态名称
    
    private String checkPrint; //单证打印是否选中
    
    private BigDecimal accReason; //出险原因
    
//    private String OrganName; //机构名称
    
    private String memoType; // 备注问题件类型

    private BigDecimal memoId; // 备注ID

    private String memoContent; // 备注内容

    private String memoOption; // 备注选项
    
    private String isCommonStr; // 非常规标识
    private String surveyFlagName; // 查勘标识
    private String claimFlagName; // 二核标识
    private String discussFlagName; // 合议标识
    private String injuryFlagName; // 鉴定标示
    private String talkFlagName; // 协谈标识
    private String starFlagName; // 明星业务员标识
    private String customerVipName; // 客户VIP标识
    private String comfortFlagName; // 慰问标识

    private String claimName; //理赔类型名称
    private String caseName; //案件类型名称
    private String policyPoject; //保单挂起项目
    private String actualPaytotal; // 统计数量
    private String actualPaySum; // 统计金额
    private String checklistproblemFlag;  //是否为问题件

    public String getChecklistproblemFlag() {
		return checklistproblemFlag;
	}

	public void setChecklistproblemFlag(String checklistproblemFlag) {
		this.checklistproblemFlag = checklistproblemFlag;
	}

	public String getMemoType() {
        return memoType;
    }

    public void setMemoType(String memoType) {
        this.memoType = memoType;
    }

    public BigDecimal getMemoId() {
        return memoId;
    }

    public void setMemoId(BigDecimal memoId) {
        this.memoId = memoId;
    }

    public String getMemoContent() {
        return memoContent;
    }

    public void setMemoContent(String memoContent) {
        this.memoContent = memoContent;
    }

    public String getMemoOption() {
        return memoOption;
    }

    public void setMemoOption(String memoOption) {
        this.memoOption = memoOption;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getSurveyStatus() {
        return surveyStatus;
    }

    public void setSurveyStatus(String surveyStatus) {
        this.surveyStatus = surveyStatus;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getInsuredCode() {
        return insuredCode;
    }

    public void setInsuredCode(String insuredCode) {
        this.insuredCode = insuredCode;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getProductAbbrName() {
        return productAbbrName;
    }

    public void setProductAbbrName(String productAbbrName) {
        this.productAbbrName = productAbbrName;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public Date getFeeStatusDate() {
        return feeStatusDate;
    }

    public void setFeeStatusDate(Date feeStatusDate) {
        this.feeStatusDate = feeStatusDate;
    }

    public String getUwStatus() {
        return uwStatus;
    }

    public void setUwStatus(String uwStatus) {
        this.uwStatus = uwStatus;
    }

    public String getClaimType() {
        return claimType;
    }

    public void setClaimType(String claimType) {
        this.claimType = claimType;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public Integer getCustomerSex() {
        return customerSex;
    }

    public void setCustomerSex(Integer customerSex) {
        this.customerSex = customerSex;
    }

    public String getSystemDateString() {
        return systemDateString;
    }

    public void setSystemDateString(String systemDateString) {
        this.systemDateString = systemDateString;
    }

    public String getUserRealName() {
        return userRealName;
    }

    public void setUserRealName(String userRealName) {
        this.userRealName = userRealName;
    }

    public void setTrusteeCertiCode(String trusteeCertiCode) {
        this.trusteeCertiCode = trusteeCertiCode;
    }

    public String getTrusteeCertiCode() {
        return trusteeCertiCode;
    }

    public void setEndCaseTime(Date endCaseTime) {
        this.endCaseTime = endCaseTime;
    }

    public Date getEndCaseTime() {
        return endCaseTime;
    }

    public void setActualPay(BigDecimal actualPay) {
        this.actualPay = actualPay;
    }

    public BigDecimal getActualPay() {
        return actualPay;
    }

    public void setReportMode(BigDecimal reportMode) {
        this.reportMode = reportMode;
    }

    public BigDecimal getReportMode() {
        return reportMode;
    }

    public void setApproveRejectReason(String approveRejectReason) {
        this.approveRejectReason = approveRejectReason;
    }

    public String getApproveRejectReason() {
        return approveRejectReason;
    }

    public void setRelatedNo(String relatedNo) {
        this.relatedNo = relatedNo;
    }

    public String getRelatedNo() {
        return relatedNo;
    }

    public void setRptrRelation(String rptrRelation) {
        this.rptrRelation = rptrRelation;
    }

    public String getRptrRelation() {
        return rptrRelation;
    }

    public void setOverCompFlag(BigDecimal overCompFlag) {
        this.overCompFlag = overCompFlag;
    }

    public BigDecimal getOverCompFlag() {
        return overCompFlag;
    }

    public void setOtherReason(String otherReason) {
        this.otherReason = otherReason;
    }

    public String getOtherReason() {
        return otherReason;
    }

    public void setRepealReason(BigDecimal repealReason) {
        this.repealReason = repealReason;
    }

    public BigDecimal getRepealReason() {
        return repealReason;
    }

    public void setIsCommon(BigDecimal isCommon) {
        this.isCommon = isCommon;
    }

    public BigDecimal getIsCommon() {
        return isCommon;
    }

    public void setCureHospital(String cureHospital) {
        this.cureHospital = cureHospital;
    }

    public String getCureHospital() {
        return cureHospital;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setReviewFlag(BigDecimal reviewFlag) {
        this.reviewFlag = reviewFlag;
    }

    public BigDecimal getReviewFlag() {
        return reviewFlag;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setAdvanceAskFlag(BigDecimal advanceAskFlag) {
        this.advanceAskFlag = advanceAskFlag;
    }

    public BigDecimal getAdvanceAskFlag() {
        return advanceAskFlag;
    }

    public void setCaseFlag(BigDecimal caseFlag) {
        this.caseFlag = caseFlag;
    }

    public BigDecimal getCaseFlag() {
        return caseFlag;
    }

    public void setGreenFlag(BigDecimal greenFlag) {
        this.greenFlag = greenFlag;
    }

    public BigDecimal getGreenFlag() {
        return greenFlag;
    }

    public void setAuditRejectReason(String auditRejectReason) {
        this.auditRejectReason = auditRejectReason;
    }

    public String getAuditRejectReason() {
        return auditRejectReason;
    }

    public void setBalancePay(BigDecimal balancePay) {
        this.balancePay = balancePay;
    }

    public BigDecimal getBalancePay() {
        return balancePay;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setAuditorId(BigDecimal auditorId) {
        this.auditorId = auditorId;
    }

    public BigDecimal getAuditorId() {
        return auditorId;
    }

    public void setRptrMp(String rptrMp) {
        this.rptrMp = rptrMp;
    }

    public String getRptrMp() {
        return rptrMp;
    }

    public void setSeriousDisease(String seriousDisease) {
        this.seriousDisease = seriousDisease;
    }

    public String getSeriousDisease() {
        return seriousDisease;
    }

    public void setRegisteTime(Date registeTime) {
        this.registeTime = registeTime;
    }

    public Date getRegisteTime() {
        return registeTime;
    }

    public void setCaseApplyType(BigDecimal caseApplyType) {
        this.caseApplyType = caseApplyType;
    }

    public BigDecimal getCaseApplyType() {
        return caseApplyType;
    }

    public void setRepealDesc(String repealDesc) {
        this.repealDesc = repealDesc;
    }

    public String getRepealDesc() {
        return repealDesc;
    }

    public void setRptrEmail(String rptrEmail) {
        this.rptrEmail = rptrEmail;
    }

    public String getRptrEmail() {
        return rptrEmail;
    }

    public void setTrusteeCertiType(String trusteeCertiType) {
        this.trusteeCertiType = trusteeCertiType;
    }

    public String getTrusteeCertiType() {
        return trusteeCertiType;
    }

    public void setInsuredId(BigDecimal insuredId) {
        this.insuredId = insuredId;
    }

    public BigDecimal getInsuredId() {
        return insuredId;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setCaseStatus(String caseStatus) {
        this.caseStatus = caseStatus;
    }

    public String getCaseStatus() {
        return caseStatus;
    }

    public void setCaseId(BigDecimal caseId) {
        this.caseId = caseId;
    }

    public BigDecimal getCaseId() {
        return caseId;
    }

    public void setRptrTime(Date rptrTime) {
        this.rptrTime = rptrTime;
    }

    public Date getRptrTime() {
        return rptrTime;
    }

    public void setAuditDecision(BigDecimal auditDecision) {
        this.auditDecision = auditDecision;
    }

    public BigDecimal getAuditDecision() {
        return auditDecision;
    }

    public void setRegisterId(BigDecimal registerId) {
        this.registerId = registerId;
    }

    public BigDecimal getRegisterId() {
        return registerId;
    }

    public void setClaimSource(BigDecimal claimSource) {
        this.claimSource = claimSource;
    }

    public BigDecimal getClaimSource() {
        return claimSource;
    }

    public void setTrusteeType(BigDecimal trusteeType) {
        this.trusteeType = trusteeType;
    }

    public BigDecimal getTrusteeType() {
        return trusteeType;
    }

    public void setApproverId(BigDecimal approverId) {
        this.approverId = approverId;
    }

    public BigDecimal getApproverId() {
        return approverId;
    }

    public void setAccidentId(BigDecimal accidentId) {
        this.accidentId = accidentId;
    }

    public BigDecimal getAccidentId() {
        return accidentId;
    }

    public void setRejectPay(BigDecimal rejectPay) {
        this.rejectPay = rejectPay;
    }

    public BigDecimal getRejectPay() {
        return rejectPay;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignerId(BigDecimal signerId) {
        this.signerId = signerId;
    }

    public BigDecimal getSignerId() {
        return signerId;
    }

    public void setAdvancePay(BigDecimal advancePay) {
        this.advancePay = advancePay;
    }

    public BigDecimal getAdvancePay() {
        return advancePay;
    }

    public void setRptrName(String rptrName) {
        this.rptrName = rptrName;
    }

    public String getRptrName() {
        return rptrName;
    }

    public void setRptrAddr(String rptrAddr) {
        this.rptrAddr = rptrAddr;
    }

    public String getRptrAddr() {
        return rptrAddr;
    }

    public void setApproveRemark(String approveRemark) {
        this.approveRemark = approveRemark;
    }

    public String getApproveRemark() {
        return approveRemark;
    }

    public void setTrusteeName(String trusteeName) {
        this.trusteeName = trusteeName;
    }

    public String getTrusteeName() {
        return trusteeName;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setAdvanceFlag(BigDecimal advanceFlag) {
        this.advanceFlag = advanceFlag;
    }

    public BigDecimal getAdvanceFlag() {
        return advanceFlag;
    }

    public void setRegisteConfTime(Date registeConfTime) {
        this.registeConfTime = registeConfTime;
    }

    public Date getRegisteConfTime() {
        return registeConfTime;
    }

    public void setTrusteeTel(String trusteeTel) {
        this.trusteeTel = trusteeTel;
    }

    public String getTrusteeTel() {
        return trusteeTel;
    }

    public void setRptrZip(BigDecimal rptrZip) {
        this.rptrZip = rptrZip;
    }

    public BigDecimal getRptrZip() {
        return rptrZip;
    }

    public void setIsBpo(BigDecimal isBpo) {
        this.isBpo = isBpo;
    }

    public BigDecimal getIsBpo() {
        return isBpo;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setApproveDecision(BigDecimal approveDecision) {
        this.approveDecision = approveDecision;
    }

    public BigDecimal getApproveDecision() {
        return approveDecision;
    }

    public void setTrusteeCode(String trusteeCode) {
        this.trusteeCode = trusteeCode;
    }

    public String getTrusteeCode() {
        return trusteeCode;
    }

    public void setDoorSignTime(Date doorSignTime) {
        this.doorSignTime = doorSignTime;
    }

    public Date getDoorSignTime() {
        return doorSignTime;
    }

    public void setComfortFlag(BigDecimal comfortFlag) {
        this.comfortFlag = comfortFlag;
    }

    public BigDecimal getComfortFlag() {
        return comfortFlag;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public Date getAcceptTime() {
        return acceptTime;
    }

    public void setIsDeductFlag(BigDecimal isDeductFlag) {
        this.isDeductFlag = isDeductFlag;
    }

    public BigDecimal getIsDeductFlag() {
        return isDeductFlag;
    }

    public void setMedDept(String medDept) {
        this.medDept = medDept;
    }

    public String getMedDept() {
        return medDept;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setTrusteeMp(String trusteeMp) {
        this.trusteeMp = trusteeMp;
    }

    public String getTrusteeMp() {
        return trusteeMp;
    }

    public void setCureStatus(String cureStatus) {
        this.cureStatus = cureStatus;
    }

    public String getCureStatus() {
        return cureStatus;
    }

    public void setAcceptDecision(BigDecimal acceptDecision) {
        this.acceptDecision = acceptDecision;
    }

    public BigDecimal getAcceptDecision() {
        return acceptDecision;
    }

    public void setAcceptorId(BigDecimal acceptorId) {
        this.acceptorId = acceptorId;
    }

    public BigDecimal getAcceptorId() {
        return acceptorId;
    }

    public void setCaseSubStatus(String caseSubStatus) {
        this.caseSubStatus = caseSubStatus;
    }

    public String getCaseSubStatus() {
        return caseSubStatus;
    }

    public void setCalcPay(BigDecimal calcPay) {
        this.calcPay = calcPay;
    }

    public BigDecimal getCalcPay() {
        return calcPay;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ClaimCaseVO [" + "trusteeCertiCode=" + trusteeCertiCode + "," + "endCaseTime=" + endCaseTime + ","
                + "actualPay=" + actualPay + "," + "reportMode=" + reportMode + "," + "approveRejectReason="
                + approveRejectReason + "," + "relatedNo=" + relatedNo + "," + "rptrRelation=" + rptrRelation + ","
                + "overCompFlag=" + overCompFlag + "," + "otherReason=" + otherReason + "," + "repealReason="
                + repealReason + "," + "isCommon=" + isCommon + "," + "cureHospital=" + cureHospital + "," + "caseNo="
                + caseNo + "," + "reviewFlag=" + reviewFlag + "," + "organCode=" + organCode + "," + "advanceAskFlag="
                + advanceAskFlag + "," + "caseFlag=" + caseFlag + "," + "greenFlag=" + greenFlag + ","
                + "auditRejectReason=" + auditRejectReason + "," + "balancePay=" + balancePay + "," + "rejectReason="
                + rejectReason + "," + "auditorId=" + auditorId + "," + "rptrMp=" + rptrMp + "," + "seriousDisease="
                + seriousDisease + "," + "registeTime=" + registeTime + "," + "caseApplyType=" + caseApplyType + ","
                + "repealDesc=" + repealDesc + "," + "rptrEmail=" + rptrEmail + "," + "trusteeCertiType="
                + trusteeCertiType + "," + "insuredId=" + insuredId + "," + "accidentDetail=" + accidentDetail + ","
                + "caseStatus=" + caseStatus + "," + "caseId=" + caseId + "," + "rptrTime=" + rptrTime + ","
                + "auditDecision=" + auditDecision + "," + "registerId=" + registerId + "," + "claimSource="
                + claimSource + "," + "trusteeType=" + trusteeType + "," + "approverId=" + approverId + ","
                + "accidentId=" + accidentId + "," + "rejectPay=" + rejectPay + "," + "signTime=" + signTime + ","
                + "signerId=" + signerId + "," + "advancePay=" + advancePay + "," + "rptrName=" + rptrName + ","
                + "rptrAddr=" + rptrAddr + "," + "approveRemark=" + approveRemark + "," + "trusteeName=" + trusteeName
                + "," + "applyDate=" + applyDate + "," + "advanceFlag=" + advanceFlag + "," + "registeConfTime="
                + registeConfTime + "," + "trusteeTel=" + trusteeTel + "," + "rptrZip=" + rptrZip + "," + "isBpo="
                + isBpo + "," + "auditTime=" + auditTime + "," + "approveTime=" + approveTime + "," + "auditRemark="
                + auditRemark + "," + "approveDecision=" + approveDecision + "," + "trusteeCode=" + trusteeCode + ","
                + "doorSignTime=" + doorSignTime + "," + "comfortFlag=" + comfortFlag + "," + "acceptTime="
                + acceptTime + "," + "isDeductFlag=" + isDeductFlag + "," + "medDept=" + medDept + "," + "doctorName="
                + doctorName + "," + "trusteeMp=" + trusteeMp + "," + "cureStatus=" + cureStatus + ","
                + "acceptDecision=" + acceptDecision + "," + "acceptorId=" + acceptorId + "," + "caseSubStatus="
                + caseSubStatus + "," + "calcPay=" + calcPay + "]";
    }

   
    
    

    

   
    

    public String getPrintByName() {
        return printByName;
    }

    public void setPrintByName(String printByName) {
        this.printByName = printByName;
    }

    public String getClaimDateAll() {
        return claimDateAll;
    }

    public void setClaimDateAll(String claimDateAll) {
        this.claimDateAll = claimDateAll;
    }

    public String getCaseResult() {
        return caseResult;
    }

    public void setCaseResult(String caseResult) {
        this.caseResult = caseResult;
    }

    public String getVisitorName() {
        return visitorName;
    }

    public void setVisitorName(String visitorName) {
        this.visitorName = visitorName;
    }

    public String getVisitMarker() {
        return visitMarker;
    }

    public void setVisitMarker(String visitMarker) {
        this.visitMarker = visitMarker;
    }

    public Date getNextVisitDate() {
        return nextVisitDate;
    }

    public void setNextVisitDate(Date nextVisitDate) {
        this.nextVisitDate = nextVisitDate;
    }

    public BigDecimal getCareTimes() {
        return careTimes;
    }

    public void setCareTimes(BigDecimal careTimes) {
        this.careTimes = careTimes;
    }

    public String getCheckAll() {
        return checkAll;
    }

    public void setCheckAll(String checkAll) {
        this.checkAll = checkAll;
    }

    public String getSeleNo() {
        return seleNo;
    }

    public void setSeleNo(String seleNo) {
        this.seleNo = seleNo;
    }

    public String getSeleNohid() {
        return seleNohid;
    }

    public void setSeleNohid(String seleNohid) {
        this.seleNohid = seleNohid;
    }

    

    public String getProductNameStd() {
        return productNameStd;
    }

    public void setProductNameStd(String productNameStd) {
        this.productNameStd = productNameStd;
    }

    public String getProductCodeStd() {
        return productCodeStd;
    }

    public void setProductCodeStd(String productCodeStd) {
        this.productCodeStd = productCodeStd;
    }

    public BigDecimal getPolicyId() {
        return policyId;
    }

    public void setPolicyId(BigDecimal policyId) {
        this.policyId = policyId;
    }

    public BigDecimal getBusiItemId() {
        return busiItemId;
    }

    public void setBusiItemId(BigDecimal busiItemId) {
        this.busiItemId = busiItemId;
    }

    public BigDecimal getLiabilityState() {
        return liabilityState;
    }

    public void setLiabilityState(BigDecimal liabilityState) {
        this.liabilityState = liabilityState;
    }

    public Date getPaidupDate() {
        return paidupDate;
    }

    public void setPaidupDate(Date paidupDate) {
        this.paidupDate = paidupDate;
    }

    public BigDecimal getTotalPremAf() {
        return totalPremAf;
    }

    public void setTotalPremAf(BigDecimal totalPremAf) {
        this.totalPremAf = totalPremAf;
    }

    public BigDecimal getItemId() {
        return itemId;
    }

    public void setItemId(BigDecimal itemId) {
        this.itemId = itemId;
    }

    public BigDecimal getInitialType() {
        return initialType;
    }

    public void setInitialType(BigDecimal initialType) {
        this.initialType = initialType;
    }

    public String getPaidupString() {
        return paidupString;
    }

    public void setPaidupString(String paidupString) {
        this.paidupString = paidupString;
    }

    public String getChargeName() {
        return chargeName;
    }

    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    public BigDecimal getIsWaived() {
        return isWaived;
    }

    public void setIsWaived(BigDecimal isWaived) {
        this.isWaived = isWaived;
    }

    public String getWaiveReason() {
        return waiveReason;
    }

    public void setWaiveReason(String waiveReason) {
        this.waiveReason = waiveReason;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public Date getWaiveStart() {
        return waiveStart;
    }

    public void setWaiveStart(Date waiveStart) {
        this.waiveStart = waiveStart;
    }

    public BigDecimal getWaiveAmt() {
        return waiveAmt;
    }

    public void setWaiveAmt(BigDecimal waiveAmt) {
        this.waiveAmt = waiveAmt;
    }

    public Date getWaiveEnd() {
        return waiveEnd;
    }

    public void setWaiveEnd(Date waiveEnd) {
        this.waiveEnd = waiveEnd;
    }

    public String getWaiveStartString() {
        return waiveStartString;
    }

    public void setWaiveStartString(String waiveStartString) {
        this.waiveStartString = waiveStartString;
    }

    public String getWaiveEndString() {
        return waiveEndString;
    }

    public void setWaiveEndString(String waiveEndString) {
        this.waiveEndString = waiveEndString;
    }

    public String getWaiveDesc() {
        return waiveDesc;
    }

    public void setWaiveDesc(String waiveDesc) {
        this.waiveDesc = waiveDesc;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public Date getAccDate() {
		return accDate;
	}

	public void setAccDate(Date accDate) {
		this.accDate = accDate;
	}

	public Date getRegisteDateStart() {
		return registeDateStart;
	}

	public void setRegisteDateStart(Date registeDateStart) {
		this.registeDateStart = registeDateStart;
	}

	public Date getRegisteDateEnd() {
		return registeDateEnd;
	}

	public void setRegisteDateEnd(Date registeDateEnd) {
		this.registeDateEnd = registeDateEnd;
	}

	public int getDays() {
		return days;
	}

	public void setDays(int days) {
		this.days = days;
	}

	public Date getPolicyUpdateTime() {
		return policyUpdateTime;
	}

	public void setPolicyUpdateTime(Date policyUpdateTime) {
		this.policyUpdateTime = policyUpdateTime;
	}

	public Date getStatDate() {
		return statDate;
	}

	public void setStatDate(Date statDate) {
		this.statDate = statDate;
	}

	public String getDeformityType() {
		return deformityType;
	}

	public void setDeformityType(String deformityType) {
		this.deformityType = deformityType;
	}

	public BigDecimal getCustomerVip() {
		return customerVip;
	}

	public void setCustomerVip(BigDecimal customerVip) {
		this.customerVip = customerVip;
	}

	public String getAuditDec() {
		return auditDec;
	}

	public void setAuditDec(String auditDec) {
		this.auditDec = auditDec;
	}

	public String getAuditorgName() {
		return auditorgName;
	}

	public void setAuditorgName(String auditorgName) {
		this.auditorgName = auditorgName;
	}


	public String getClaimName() {
		return claimName;
	}

	public void setClaimName(String claimName) {
		this.claimName = claimName;
	}

	public String getCaseName() {
		return caseName;
	}

	public void setCaseName(String caseName) {
		this.caseName = caseName;
	}

	public Date getInitialPremDate() {
		return initialPremDate;
	}

	public void setInitialPremDate(Date initialPremDate) {
		this.initialPremDate = initialPremDate;
	}

	public String getCaseType() {
		return caseType;
	}

	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}

	public String getPolicyPoject() {
		return policyPoject;
	}

	public void setPolicyPoject(String policyPoject) {
		this.policyPoject = policyPoject;
	}

	public String getActualPaytotal() {
		return actualPaytotal;
	}

	public void setActualPaytotal(String actualPaytotal) {
		this.actualPaytotal = actualPaytotal;
	}

	public String getActualPaySum() {
		return actualPaySum;
	}

	public void setActualPaySum(String actualPaySum) {
		this.actualPaySum = actualPaySum;
	}

	public String getCaseFlagName() {
		return caseFlagName;
	}

	public void setCaseFlagName(String caseFlagName) {
		this.caseFlagName = caseFlagName;
	}

	public String getSurveyFlagName() {
		return surveyFlagName;
	}

	public void setSurveyFlagName(String surveyFlagName) {
		this.surveyFlagName = surveyFlagName;
	}

	public String getClaimFlagName() {
		return claimFlagName;
	}

	public void setClaimFlagName(String claimFlagName) {
		this.claimFlagName = claimFlagName;
	}

	public String getDiscussFlagName() {
		return discussFlagName;
	}

	public void setDiscussFlagName(String discussFlagName) {
		this.discussFlagName = discussFlagName;
	}

	public String getInjuryFlagName() {
		return injuryFlagName;
	}

	public void setInjuryFlagName(String injuryFlagName) {
		this.injuryFlagName = injuryFlagName;
	}

	public String getTalkFlagName() {
		return talkFlagName;
	}

	public void setTalkFlagName(String talkFlagName) {
		this.talkFlagName = talkFlagName;
	}

	public String getStarFlagName() {
		return starFlagName;
	}

	public void setStarFlagName(String starFlagName) {
		this.starFlagName = starFlagName;
	}

	public String getCustomerVipName() {
		return customerVipName;
	}

	public void setCustomerVipName(String customerVipName) {
		this.customerVipName = customerVipName;
	}

	public String getComfortFlagName() {
		return comfortFlagName;
	}

	public void setComfortFlagName(String comfortFlagName) {
		this.comfortFlagName = comfortFlagName;
	}

	public String getIsCommonStr() {
		return isCommonStr;
	}

	public void setIsCommonStr(String isCommonStr) {
		this.isCommonStr = isCommonStr;
	}

	public static void main(String[] strs){
    	Date date = new Date();
    	Date d = DateUtilsEx.addDay(date, 2);
    	System.out.println(d);
    	
    }

	public String getAccDesc() {
		return accDesc;
	}

	public void setAccDesc(String accDesc) {
		this.accDesc = accDesc;
	}

	public String getLeftFlag() {
		return leftFlag;
	}
	
	public void setLeftFlag(String leftFlag) {
		this.leftFlag = leftFlag;
	}
	
	
}
