package com.nci.tunan.qry.interfaces.model.po;


import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/**
 * @description BankTextDetailPO对象
 * @<NAME_EMAIL>
 * @date 2015-9-18 下午3:06:56
 */
public class BankTextDetailCompPO extends BasePO {
    /**
     * @Fields serialVersionUID :
     */

    private static final long serialVersionUID = 1L;

    /** 属性 --- java类型 --- oracle类型_数据长度_小数位长度_注释信息 */
    // moneyCode --- String --- CHAR_3_0_币种代码 CNY；人民币元;
    // bankAccount --- String --- VARCHAR2_40_0_帐号;
    // diskMode --- String --- CHAR_1_0_资金使用，默认值：1 1：代收付;
    // payeePhone --- String --- VARCHAR2_30_0_手机号;
    // uuid --- String --- VARCHAR2_32_0_唯一标识码;
    // sendId --- BigDecimal --- NUMBER_16_0_盘文件ID;
    // bankTextStatus --- String --- CHAR_1_0_返盘状态 1,返盘成功 2,返盘失败 ,
    // seqNo --- BigDecimal --- NUMBER_16_0_流水号，无业务含义;
    // certiCode --- String --- VARCHAR2_30_0_证件号码;
    // feeAmount --- BigDecimal --- NUMBER_18_2_制盘金额;
    // cashDesc --- String --- VARCHAR2_30_0_标记现金流量，是会计入账科目的CODE，可不填;
    // dueDate --- Date --- DATE_7_0_应收应付日期;
    // rtnCode --- BigDecimal --- NUMBER_10_0_银行返回代码 E1409；付款行未开通业务
    // E1503；客户信息存在非法字符;
    // zone --- String --- VARCHAR2_6_0_对方账号的所属区域 3201；江苏省_南京市 3202；江苏省_无锡市;
    // derivType --- String --- VARCHAR2_3_0_业务来源;
    // arapFlag --- String --- CHAR_1_0_应收付标识S；收费F；付费;
    // lhh --- String --- VARCHAR2_12_0_对方银行的联行号;
    // bankCode --- String --- VARCHAR2_20_0_分组标记+银行代码;
    // accoName --- String --- VARCHAR2_50_0_账户所有人姓名;
    // certiType --- String --- CHAR_2_0_证件类型;

    /**
     * @description 币种代码
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param moneyCode
     *            币种代码
     */
    public void setMoneyCode(String moneyCode) {
        setString("money_code", moneyCode);
    }

    public String getMoneyCode() {
        return getString("money_code");
    }

    /**
     * @description 帐号
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param bankAccount
     *            帐号
     */
    public void setBankAccount(String bankAccount) {
        setString("bank_account", bankAccount);
    }

    public String getBankAccount() {
        return getString("bank_account");
    }

    /**
     * @description 代收付
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param diskMode
     *            代收付
     */
    public void setDiskMode(String diskMode) {
        setString("disk_mode", diskMode);
    }

    public String getDiskMode() {
        return getString("disk_mode");
    }

    /**
     * @description 手机号
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param payeePhone
     *            手机号
     */
    public void setPayeePhone(String payeePhone) {
        setString("payee_phone", payeePhone);
    }

    public String getPayeePhone() {
        return getString("payee_phone");
    }

    /**
     * @description 唯一标识码
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param uuid
     *            唯一标识码
     */
    public void setUuid(String uuid) {
        setString("uuid", uuid);
    }

    public String getUuid() {
        return getString("uuid");
    }

    /**
     * @description 盘文件ID
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param sendId
     *            盘文件ID
     */
    public void setSendId(BigDecimal sendId) {
        setBigDecimal("send_id", sendId);
    }

    public BigDecimal getSendId() {
        return getBigDecimal("send_id");
    }

    /**
     * @description 0_返盘状态 1,返盘成功 2,返盘失败
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param bankTextStatus
     *            0_返盘状态 1,返盘成功 2,返盘失败
     */
    public void setBankTextStatus(String bankTextStatus) {
        setString("bank_text_status", bankTextStatus);
    }

    public String getBankTextStatus() {
        return getString("bank_text_status");
    }

    /**
     * @description bank_text表 制盘状态 
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param bankTextStatus
     *           
     */
    public void setBankTextMainStatus(String bankTextStatus) {
        setString("bank_text_main_status", bankTextStatus);
    }

    public String getBankTextMainStatus() {
        return getString("bank_text_main_status");
    }
    
    /**
     * @description 流水号，无业务含义
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param seqNo
     *            流水号，无业务含义
     */
    public void setSeqNo(BigDecimal seqNo) {
        setBigDecimal("seq_no", seqNo);
    }

    public BigDecimal getSeqNo() {
        return getBigDecimal("seq_no");
    }

    /**
     * @description 证件号码
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param certiCode
     *            证件号码
     */
    public void setCertiCode(String certiCode) {
        setString("certi_code", certiCode);
    }

    public String getCertiCode() {
        return getString("certi_code");
    }

    /**
     * @description 制盘金额
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param feeAmount
     *            制盘金额
     */
    public void setFeeAmount(BigDecimal feeAmount) {
        setBigDecimal("fee_amount", feeAmount);
    }

    public BigDecimal getFeeAmount() {
        return getBigDecimal("fee_amount");
    }

    /**
     * @description 标记现金流量，是会计入账科目的CODE，可不填
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param cashDesc
     *            标记现金流量，是会计入账科目的CODE，可不填
     */
    public void setCashDesc(String cashDesc) {
        setString("cash_desc", cashDesc);
    }

    public String getCashDesc() {
        return getString("cash_desc");
    }

    /**
     * @description 应收应付日期
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param dueDate
     *            应收应付日期
     */
    public void setDueDate(Date dueDate) {
        setUtilDate("due_date", dueDate);
    }

    public Date getDueDate() {
        return getUtilDate("due_date");
    }

    /**
     * @description 银行返回代码
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param rtnCode
     *            银行返回代码
     */
    public void setRtnCode(String rtnCode) {
        setString("rtn_code", rtnCode);
    }

    public String getRtnCode() {
        return getString("rtn_code");
    }
    
    public void setRtnReason(String rtnReason){
    	setString("rtn_reason",rtnReason);
    }
    public String getRtnReason(){
    	return getString("rtn_reason");
    }
    /**
     * @description 对方账号的所属区域
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param zone
     *            对方账号的所属区域
     */
    public void setZone(String zone) {
        setString("zone", zone);
    }

    public String getZone() {
        return getString("zone");
    }

    /**
     * @description 业务来源
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param derivType
     *            业务来源
     */
    public void setDerivType(String derivType) {
        setString("deriv_type", derivType);
    }

    public String getDerivType() {
        return getString("deriv_type");
    }

    /**
     * @description 应收付标识
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param arapFlag
     *            应收付标识
     */
    public void setArapFlag(String arapFlag) {
        setString("arap_flag", arapFlag);
    }

    public String getArapFlag() {
        return getString("arap_flag");
    }

    /**
     * @description 对方银行的联行号
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param lhh
     *            对方银行的联行号
     */
    public void setLhh(String lhh) {
        setString("lhh", lhh);
    }

    public String getLhh() {
        return getString("lhh");
    }

    /**
     * @description 分组标记+银行代码
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param bankCode
     *            分组标记+银行代码
     */
    public void setBankCode(String bankCode) {
        setString("bank_code", bankCode);
    }

    public String getBankCode() {
        return getString("bank_code");
    }

    /**
     * @description 账户所有人姓名
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param accoName
     *            账户所有人姓名
     */
    public void setAccoName(String accoName) {
        setString("acco_name", accoName);
    }

    public String getAccoName() {
        return getString("acco_name");
    }

    /**
     * @description 证件类型
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param certiType
     *            证件类型
     */
    public void setCertiType(String certiType) {
        setString("certi_type", certiType);
    }

    public String getCertiType() {
        return getString("certi_type");
    }

    // =====================================================================================
    /**
     * @description inserendbegenTime
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param inserendbegenTime
     *            inserendbegenTime
     */
    public void setInserendbegenTime(Date inserendbegenTime) {
        setUtilDate("inserendbegenTime", inserendbegenTime);
    }

    public Date getInserendbegenTime() {
        return getUtilDate("inserendbegenTime");
    }

    /**
     * @description insertbegenTime
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param insertbegenTime
     *            insertbegenTime
     */
    public void setInsertbegenTime(Date insertbegenTime) {
        setUtilDate("insertbegenTime", insertbegenTime);
    }

    public Date getInsertbegenTime() {
        return getUtilDate("insertbegenTime");
    }

    /**
     * @description begenTime
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param begenTime
     *            begenTime
     */
    public void setBegenTime(Date begenTime) {
        setUtilDate("begenTime", begenTime);
    }

    public Date getBegenTime() {
        return getUtilDate("begenTime");
    }

    /**
     * @description endTime
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param endTime
     *            endTime
     */
    public void setEndTime(Date endTime) {
        setUtilDate("endTime", endTime);
    }

    public Date getEndTime() {
        return getUtilDate("endTime");
    }
    
    public Date getBankDealDate(){
        return getUtilDate("bank_deal_date");
    }
    
    public void setBankDealDate(Date bankDealDate){
        setUtilDate("bank_deal_date",bankDealDate);
    }
    public String getIsCorporate(){
    	return getString("is_corporate");
    }
    public void  setIsCorporate(String isCorporate){
    	setString("is_corporate", isCorporate);
    }
	@Override
	public String toString() {
		return "BankTextDetailCompPO [getMoneyCode()=" + getMoneyCode()
				+ ", getBankAccount()=" + getBankAccount() + ", getDiskMode()="
				+ getDiskMode() + ", getPayeePhone()=" + getPayeePhone()
				+ ", getUuid()=" + getUuid() + ", getSendId()=" + getSendId()
				+ ", getBankTextStatus()=" + getBankTextStatus()
				+ ", getBankTextMainStatus()=" + getBankTextMainStatus()
				+ ", getSeqNo()=" + getSeqNo() + ", getCertiCode()="
				+ getCertiCode() + ", getFeeAmount()=" + getFeeAmount()
				+ ", getCashDesc()=" + getCashDesc() + ", getDueDate()="
				+ getDueDate() + ", getRtnCode()=" + getRtnCode()
				+ ", getZone()=" + getZone() + ", getDerivType()="
				+ getDerivType() + ", getArapFlag()=" + getArapFlag()
				+ ", getLhh()=" + getLhh() + ", getBankCode()=" + getBankCode()
				+ ", getAccoName()=" + getAccoName() + ", getCertiType()="
				+ getCertiType() + ", getInserendbegenTime()="
				+ getInserendbegenTime() + ", getInsertbegenTime()="
				+ getInsertbegenTime() + ", getBegenTime()=" + getBegenTime()
				+ ", getEndTime()=" + getEndTime() + ", getBankDealDate()="
				+ getBankDealDate() + ", getTablePrev()=" + getTablePrev()
				+ ", getRowId()=" + getRowId() + ", getIsDeleted()="
				+ getIsDeleted() + ", getCreator()=" + getCreator()
				+ ", getCreateDate()=" + getCreateDate()
				+ ", getLastModifyBy()=" + getLastModifyBy()
				+ ", getLastModifyDate()=" + getLastModifyDate()
				+ ", getDataBaseCreateTime()=" + getDataBaseCreateTime()
				+ ", getDataBaseUpdateTime()=" + getDataBaseUpdateTime()
				+ ", toString()=" + super.toString() + ", getData()="
				+ getData() + ", getValues()=" + getValues() + ", getClass()="
				+ getClass() + ", hashCode()=" + hashCode() + "]";
	}
    
   
}
