package com.nci.tunan.qry.interfaces.model.po;
import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

public class NbUnderwritingBillDataPO extends BasePO{
	/** 
	* @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
	*/ 
	
	private static final long serialVersionUID = -5367359282571855695L;

	 // 135633 反洗钱标识
	public BigDecimal getAmlFlag(){
		return getBigDecimal("aml_flag");
	}
	public void setAmlFlag(BigDecimal amlFlag){
		setBigDecimal("aml_flag",amlFlag);
	}
	/**
	 * rm146055 共同参保保单
	 */
    public String getJointlyInsuredPolicy(){
        return getString("jointly_insured_policy");
    }
    /**
     * rm146055 共同参保保单
     */
    public void setJointlyInsuredPolicy(String jointlyInsuredPolicy){
        setString("jointly_insured_policy",jointlyInsuredPolicy);
    }
	//多主多附----str
    public String getProductCodes(){
        return getString("product_codes");
    }
    public void setProductCodes(String productCodes){
        setString("product_codes",productCodes);
    }
	public String getOccupationInsureanceFlags(){
		return getString("occupation_insureance_flags");
	}
	public void setOccupationInsureanceFlags(String occupationInsureanceFlags){
		setString("occupation_insureance_flags", occupationInsureanceFlags);
	}
    public String getChargeYears(){
    	return getString("charge_years");
    }
    public void setChargeYears(String chargeYears){
    	setString("charge_years",chargeYears);
    }
	//多主多附----end
	
	
	
	
	
	
	//---------------------------------------------------手机号核验--str
	/**
	 * 投保人手机号码核验结果
	 */
	public String getPhCheckPhone() {
		return getString("ph_check_phone");
	}
	/**
	 * 投保人手机号码核验结果
	 */
	public void setPhCheckPhone(String phCheckPhone) {
		setString("ph_check_phone", phCheckPhone);
	}
	/**
	 * 被保人手机号码核验结果
	 */
	public String getLiCheckPhone() {
		return getString("li_check_phone");
	}
	/**
	 * 被保人手机号码核验结果
	 */
	public void setLiCheckPhone(String liCheckPhone) {
		setString("li_check_phone", liCheckPhone);
	}
	/**
	 * 受益人手机号码核验结果
	 */
	public String getBeCheckPhone() {
		return getString("be_check_phone");
	}
	/**
	 * 受益人手机号码核验结果
	 */
	public void setBeCheckPhone(String beCheckPhone) {
		setString("be_check_phone", beCheckPhone);
	}
	//---------------------------------------------------手机号核验--end
	public void setOrganId(BigDecimal organId) {
		setBigDecimal("organ_id", organId);
	}

	public BigDecimal getOrganId() {
		return getBigDecimal("organ_id");
	}
	
	public String getPolicyType() {
		return getString("policy_type");
	}

	public void setPolicyType(String policyType) {
		setString("policy_type", policyType);
	}
	public String getCauseType() {
		return getString("cause_type");
	}
	
	public void setCauseType(String causeType) {
		setString("cause_type", causeType);
	}
	
	public String getIsAccount() {
		return getString("is_account");
	}

	public void setIsAccount(String isAccount) {
		setString("is_account", isAccount);
	}
	
	public void setOldPolicyCode(String oldPolicyCode){
		setString("old_policy_code",oldPolicyCode);
	}
	
	public String getOldPolicyCode(){
		return getString("old_policy_code");
	}
	
	public void setRenewalFlag(String renewalFlag){
		setString("renewal_flag",renewalFlag);
	}
	
	public String getRenewalFlag(){
		return getString("renewal_flag");
	}
	
	public void setChannelType(String channelType) {
        setString("channel_type", channelType);
    }

    public String getChannelType() {
        return getString("channel_type");
    }
    
    public void setChannelName(String channelName) {
    	setString("channel_name", channelName);
    }
    
    public String getChannelName() {
    	return getString("channel_name");
    }
    
    public void setApplyCode(String applyCode) {
    	setString("apply_code", applyCode);
    }
    
    public String getApplyCode() {
    	return getString("apply_code");
    }
    
    public Date getApplyDate() {
		return getUtilDate("apply_date");
	}

	public void setApplyDate(Date applyDate) {
		setUtilDate("apply_date", applyDate);
	}
    
    public void setIfGoodStart(BigDecimal ifGoodStart) {
        setBigDecimal("if_good_start", ifGoodStart);
    }

    public BigDecimal getIfGoodStart() {
        return getBigDecimal("if_good_start");
    }
    
    public void setCustomerName(String customerName) {
    	setString("customer_name", customerName);
    }
    
    public String getCustomerName() {
    	return getString("customer_name");
    }
    
    public void setHolderResidentName(String holderResidentName) {
    	setString("holder_resident_name", holderResidentName);
    }
    
    public String getHolderResidentName() {
    	return getString("holder_resident_name");
    }
    
    public void setApplicationName(String applicationName) {
    	setString("application_name", applicationName);
    }
    
    public String getApplicationName() {
    	return getString("application_name");
    }
    
    public void setInsuredResidentName(String insuredResidentName) {
    	setString("insured_resident_name", insuredResidentName);
    }
    
    public String getInsuredResidentName() {
    	return getString("insured_resident_name");
    }
    
    public void setCustomerLevel(String customerLevel) {
    	setString("customer_level", customerLevel);
    }
    
    public String getCustomerLevel() {
    	return getString("customer_level");
    }
    
    public void setSubmitChannel(BigDecimal submitChannel) {
    	setBigDecimal("submit_channel", submitChannel);
    }
    
    public BigDecimal getSubmitChannel() {
    	return getBigDecimal("submit_channel");
    }
    
    public void setErrorDetail(String errorDetail) {
    	setString("error_detail", errorDetail);
    }
    
    public String getErrorDetail() {
    	return getString("error_detail");
    }
    
    public void setOperationTime(String operationTime) {
    	setString("operation_time", operationTime);
    }
    
    public String getOperationTime() {
    	return getString("operation_time");
    }
    
    public void setFeeStatus(String feeStatus) {
    	setString("fee_status", feeStatus);
    }
    
    public String getFeeStatus() {
    	return getString("fee_status");
    }
    
    public void setRefundAmount(BigDecimal refundAmount) {
        setBigDecimal("refund_amount", refundAmount);
    }

    public BigDecimal getRefundAmount() {
        return getBigDecimal("refund_amount");
    }
    
    public void setOrganCode(String organCode) {
    	setString("organ_code", organCode);
    }
    
    public String getOrganCode() {
    	return getString("organ_code");
    }
    
    public void setFinishTime(Date finishTime) {
        setUtilDate("finish_time", finishTime);
    }
    
    public Date getFinishTime() {
        return getUtilDate("finish_time");
    }
    
    public void setBranchOrganCode(String branchOrganCode) {
    	setString("branch_organ_code", branchOrganCode);
    }
    
    public String getBranchOrganCode() {
    	return getString("branch_organ_code");
    }
    
    public void setServiceBank(String serviceBank) {
    	setString("service_bank", serviceBank);
    }
    
    public String getServiceBank() {
    	return getString("service_bank");
    }
    
    public void setServiceBankBranch(String serviceBankBranch) {
    	setString("service_bank_branch", serviceBankBranch);
    }
    
    public String getServiceBankBranch() {
    	return getString("service_bank_branch");
    }
    
    public void setAgentCode(String agentCode) {
    	setString("agent_code", agentCode);
    }
    
    public String getAgentCode() {
    	return getString("agent_code");
    }
    
    public void setAgentName(String agentName) {
    	setString("agent_name", agentName);
    }
    
    public String getAgentName() {
    	return getString("agent_name");
    }
    
    public void setAgentLevel(String agentLevel) {
    	setString("agent_level", agentLevel);
    }
    
    public String getAgentLevel() {
    	return getString("agent_level");
    }
    
    public void setStartDate(String startDate) {
    	setString("start_date", startDate);
    }
    
    public String getStartDate() {
        return getString("start_date");
    }
    
    public void setEndDate(String endDate) {
    	setString("end_date", endDate);
    }
    
    public String getEndDate() {
        return getString("end_date");
    }
    
    public void setStartFinishTime(String startFinishTime) {
    	setString("start_finish_time", startFinishTime);
    }
    
    public String getStartFinishTime() {
        return getString("start_finish_time");
    }
    
    public void setEndFinishTime(String endFinishTime) {
    	setString("end_finish_time", endFinishTime);
    }
    
    public String getEndFinishTime() {
        return getString("end_finish_time");
    }
    
    public void setPolicyCode(String policyCode) {
    	setString("policy_code", policyCode);
    }
    
    public String getPolicyCode() {
    	return getString("policy_code");
    }
    
/*    public void setOrganCode(String organCode) {
    	setString("organ_code", organCode);
    }
    
    public String getOrganCode() {
    	return getString("organ_code");
    }*/
	//绩优等级码值
	public String getPolicyReinsureFlag() {
		return getString("policy_reinsure_flag");
	}
	
	public void setPolicyReinsureFlag(String policyReinsureFlag) {
		setString("policy_reinsure_flag", policyReinsureFlag);
	}
	
    public void setOrganName(String organName) {
    	setString("organ_name", organName);
    }
    
    public String getOrganName() {
    	return getString("organ_name");
    }
    
    public void setHoiderName(String hoiderName) {
    	setString("hoider_name", hoiderName);
    }
    
    public String getHoiderName() {
    	return getString("hoider_name");
    }
    public void setBankName(String bankName) {
    	setString("bank_name", bankName);
    }
    
    public String getBankName() {
    	return getString("bank_name");
    }
    public void setBranchOrganCodeName(String branchOrganCodeName) {
    	setString("branch_organ_Code_name", branchOrganCodeName);
    }
    
    public String getBranchOrganCodeName() {
    	return getString("branch_organ_Code_name");
    }
    public void setThredOrganCodeName(String thredOrganCodeName) {
    	setString("thred_organ_code_name", thredOrganCodeName);
    }
    
    public String getThredOrganCodeName() {
    	return getString("thred_organ_code_name");
    }
    public void setFourOrganCodeName(String fourOrganCodeName) {
    	setString("four_organ_code_name", fourOrganCodeName);
    }
    
    public String getFourOrganCodeName() {
    	return getString("four_organ_code_name");
    }
    
    public void setServiceBankName(String serviceBankName) {
    	setString("service_bank_name", serviceBankName);
    }
    
    public String getServiceBankName() {
    	return getString("service_bank_name");
    }
    
    
    public void setSubmitChannelName(String submitChannelName) {
    	setString("submit_channel_name", submitChannelName);
    }
    
    public String getSubmitChannelName() {
    	return getString("submit_channel_name");
    }
    
    
    public String getAdminOrganCode() {
		return getString("admin_organ_code");
	}

	public void setAdminOrganCode(String adminOrganCode) {
		setString("admin_organ_code", adminOrganCode);
	}
	
	public String getAdminOrganName() {
		return getString("admin_organ_name");
	}
	
	public void setAdminOrganName(String adminOrganName) {
		setString("admin_organ_name", adminOrganName);
	}
    
	public String getUpdateTimeStamp() {
		return getString("update_timestamp");
	}
	
	public void setUpdateTimeStamp(String updateTimeStamp) {
		setString("update_timestamp", updateTimeStamp);
	}
	
	public BigDecimal getFeeAmount() {
		return getBigDecimal("fee_amount");
	}
	
	public void setFeeAmount(BigDecimal feeAmount) {
		setBigDecimal("fee_amount", feeAmount);
	}
	
	public String getProposalStatus() {
		return getString("proposal_status");
	}
	
	public void setProposalStatus(String proposalStatus) {
		setString("proposal_status", proposalStatus);
	}
	
	public String getInsertTimeStamp() {
		return getString("insert_timestamp");
	}
	
	public void setInsertTimeStamp(String insertTimeStamp) {
		setString("insert_timestamp", insertTimeStamp);
	}

	public void setTotalPremAf(BigDecimal totalPremAf) {
		setBigDecimal("total_prem_af", totalPremAf);
	}

	public BigDecimal getTotalPremAf() {
		return getBigDecimal("total_prem_af");
	}
	public void setTotalPremAfs(BigDecimal totalPremAfs) {
		setBigDecimal("total_prem_afs", totalPremAfs);
	}

	public BigDecimal getTotalPremAfs() {
		return getBigDecimal("total_prem_afs");
	}
    public String getConfirmWay(){
        return getString("confirm_way");
    }
    
    public void setConfirmWay(String confirmWay){
        setString("confirm_way",confirmWay);
    }
    
    public String getProductCode(){
        return getString("product_code");
    }
    
    public void setProductCode(String productCode){
        setString("product_code",productCode);
    }
    
    public String getProductName(){
    	return getString("product_name");
    }
    
    public void setProductName(String productName){
    	setString("product_name",productName);
    }
    public String getChargeYear(){
    	return getString("charge_year");
    }
    
    public void setChargeYear(String chargeYear){
    	setString("charge_year",chargeYear);
    }
    
	public String getSalesOrganNameQ() {
		return getString("sales_organ_name_q");
	}

	public void setSalesOrganNameQ(String salesOrganNameQ) {
		setString("sales_organ_name_q",salesOrganNameQ);
	}

	public String getSalesOrganNameB(){
		return getString("sales_organ_name_b");
	}

	public void setSalesOrganNameB(String salesOrganNameB) {
		setString("sales_organ_name_b",salesOrganNameB);
	}

	public String getSalesOrganNameZ() {
		return getString("sales_organ_name_z");
	}

	public void setSalesOrganNameZ(String salesOrganNameZ) {
		setString("sales_organ_name_z",salesOrganNameZ);
	}

	public Date getIssueDate() {
		return getUtilDate("issue_date");
	}

	public void setIssueDate(Date issueDate) {
		setUtilDate("issue_date", issueDate);
	}

	public Date getValidateDate() {
		return getUtilDate("validate_date");
	}

	public void setValidateDate(Date validateDate) {
		setUtilDate("validate_date",validateDate);
	}

	public String getLiabilityStatusName() {
		return getString("liability_status_name") ;
	}

	public void setLiabilityStatusName(String liabilityStatusName) {
		setString("liability_status_name", liabilityStatusName);
	}

	public String getIsWinningFlag() {
		return getString("is_winning_flag");
	}

	public void setIsWinningFlag(String isWinningFlag) {
		setString("is_winning_flag", isWinningFlag);
	}

	public Date getScanTime() {
		return getUtilDate("scan_time");
	}

	public void setScanTime(Date scanTime) {
		setUtilDate("scan_time", scanTime);
	}

	public BigDecimal getRiskPrem() {
		return getBigDecimal("risk_prem");
	}

	public void setRiskPrem(BigDecimal riskPrem) {
		setBigDecimal("risk_prem", riskPrem);
	}
	
	public String getIssueStartDate() {
		return getString("issue_start_date");
	}

	public void setIssueStartDate(String issueStartDate) {
		setString("issue_start_date",issueStartDate);
	}

	public String getIssueEndDate() {
		return getString("issue_end_date");
	}

	public void setIssueEndDate(String issueEndDate) {
		setString("issue_end_date",issueEndDate);
	}

	public String getValidateStartDate() {
		return getString("validate_start_date");
	}

	public void setValidateStartDate(String validateStartDate) {
		setString("validate_start_date", validateStartDate);
	}

	public String getValidateEndDate() {
		return getString("validate_end_date");
	}

	public void setValidateEndDate(String validateEndDate) {
		setString("validate_end_date", validateEndDate);
	}

	public String getRemoteSignature() {
		return getString("remote_signature");
	}

	public void setRemoteSignature(String remoteSignature) {
		setString("remote_signature", remoteSignature);
	}
	
	public String getQueryType() {
		return getString("queryType");
	}

	public void setQueryType(String queryType) {
		setString("queryType", queryType);
	}
	
	public String getHesitateFlag() {
		return getString("hesitateFlag");
	}

	public void setHesitateFlag(String hesitateFlag) {
		setString("hesitateFlag", hesitateFlag);
	}
	/**银保通保单来源更改*/
	public String getSubinputTypeDesc() {
		return getString("subinput_type_desc");
	}

	public void setSubinputTypeDesc(String subinputTypeDesc) {
		setString("subinput_type_desc", subinputTypeDesc);
	}
	/**
	 * 是否提供关系证明
	 * @return
	 */
	public String getRelationshipFlag() {
		return getString("relationship_flag");
	}
	
	public void setRelationshipFlag(String relationshipFlag) {
		setString("relationship_flag", relationshipFlag);
	}
	/**
	 * 客户身份识别标记
	 * @return
	 */
	public String getCustomerCertFlag() {
		return getString("customer_cert_flag");
	}
	
	public void setCustomerCertFlag(String customerCertFlag) {
		setString("customer_cert_flag", customerCertFlag);
	}
	

	public Date getFeeTime() {
		return getUtilDate("fee_time");
	}

	public void setFeeTime(Date feeTime) {
		setUtilDate("fee_time", feeTime);
	}

	public BigDecimal getCertFlag() {
		return getBigDecimal("cert_flag");
	}

	public void setCertFlag(BigDecimal certFlag) {
		setBigDecimal("cert_flag", certFlag);
	}

	public String getSignStutasNotes() {
		return getString("sign_stutas_notes");
	}

	public void setSignStutasNotes(String signStutasNotes) {
		setString("sign_stutas_notes", signStutasNotes);
	}
	
	public String getIsRemoteAutograph(){
		return getString("is_remote_autograph");
	}
	public void setIsRemoteAutograph(String isRemoteAutograph){
		setString("is_remote_autograph", isRemoteAutograph);
	}
	
	public String getOccupationInsureanceFlag(){
		return getString("occupation_insureance_flag");
	}
	public void setOccupationInsureanceFlag(String occupationInsureanceFlag){
		setString("occupation_insureance_flag", occupationInsureanceFlag);
	}
	
	public String getChargePeriod(){
        return getString("charge_period");
    }
    
    public void setChargePeriod(String chargePeriod){
        setString("charge_period",chargePeriod);
    }
	
    public BigDecimal getIsSelfInsured() {
		return getBigDecimal("is_self_insured");
	}

	public void setIsSelfInsured(BigDecimal isSelfInsured) {
		setBigDecimal("is_self_insured",isSelfInsured);
	}

	public BigDecimal getIsMutualInsured() {
		return getBigDecimal("is_mutual_insured");
	}

	public void setIsMutualInsured(BigDecimal isMutualInsured) {
		setBigDecimal("is_mutual_insured",isMutualInsured);
	}
	
	
	public String getApplyTime() {
		return getString("apply_Time");
	}

	public void setApplyTime(String applyTime) {
		setString("apply_Time",applyTime);
	}

	
	public String getPayModeName() {
		return getString("pay_Mode_Name");
	}

	public void setPayModeName(String payModeName) {
		setString("pay_Mode_Name",payModeName);
	}
	public void setTotalPremAfContract(BigDecimal totalPremAfContract) {
		setBigDecimal("total_prem_af_contract", totalPremAfContract);
	}

	public BigDecimal getTotalPremAfContract() {
		return getBigDecimal("total_prem_af_contract");
	}
	/** rm46080承保身份识别应用需求 start */
	public String getHcusCheckLogout() {
		return getString("hcus_check_logout");
	}

	public void setHcusCheckLogout(String hcusCheckLogout) {
		setString("hcus_check_logout",hcusCheckLogout);
	}

	public String getIcusCheckLogout() {
		return getString("icus_check_logout");
	}

	public void setIcusCheckLogout(String icusCheckLogout) {
		setString("icus_check_logout",icusCheckLogout);
	}

	public String getHcusCheckPeopleImage() {
		return getString("hcus_check_people_image");
	}

	public void setHcusCheckPeopleImage(String hcusCheckPeopleImage) {
		setString("hcus_check_people_image",hcusCheckPeopleImage);
	}

	public String getIcusCheckPeopleImage() {
		return getString("icus_check_people_image");
	}

	public void setIcusCheckPeopleImage(String icusCheckPeopleImage) {
		setString("icus_check_people_image",icusCheckPeopleImage);
	}

	public String getHcusCheckPeopleCode() {
		return getString("hcus_check_people_code");
	}

	public void setHcusCheckPeopleCode(String hcusCheckPeopleCode) {
		setString("hcus_check_people_code",hcusCheckPeopleCode);
	}

	public String getIcusCheckPeopleCode() {
		return getString("icus_check_people_code");
	}

	public void setIcusCheckPeopleCode(String icusCheckPeopleCode) {
		setString("icus_check_people_code",icusCheckPeopleCode);
	}
	/** rm46080承保身份识别应用需求 end */
	
	/** 是否绩优 */
	public String getIsQualityAgent() {
		return getString("is_quality_agent");
	}

	public void setIsQualityAgent(String isQualityAgent) {
		setString("is_quality_agent", isQualityAgent);
	}
	//绩优等级码值
	public String getAgentLevelDesc() {
		return getString("agent_level_desc");
	}
	
	public void setAgentLevelDesc(String agentLevelDesc) {
		setString("agent_level_desc", agentLevelDesc);
	}
	
	public BigDecimal getApplicantSpePeople() {
		return getBigDecimal("applicant_spe_people");
	}

	public void setApplicantSpePeople(BigDecimal applicantSpePeople) {
		setBigDecimal("applicant_spe_people", applicantSpePeople);
	}

	
	/**
	 * 第一被保人 特殊人员类型 1脱贫户、2边缘户、3-乡村人口、4-残疾人
	 */
	public String getInsSpePeople() {
		return getString("ins_spe_people");
	}

	public void setInsSpePeople(String insSpePeople) {
		setString("ins_spe_people", insSpePeople);
	}
	/**
	 * 第一被保人 乡村人口标识
	 */
	public BigDecimal getRuralPopulationFlag() {
		return getBigDecimal("rural_population_flag");
	}

	public void setRuralPopulationFlag(BigDecimal ruralPopulationFlag) {
		setBigDecimal("rural_population_flag", ruralPopulationFlag);
	}
	/**
	 * 第一被保人 残疾人标识
	 */
	public BigDecimal getDisabilityFlag() {
		return getBigDecimal("disability_flag");
	}

	public void setDisabilityFlag(BigDecimal disabilityFlag) {
		setBigDecimal("disability_flag", disabilityFlag);
	}
	/**
	 * 第二被保人 特殊人员类型 1脱贫户、2边缘户、3-乡村人口、4-残疾人
	 */
	public String getInsSpePeopleSec() {
		return getString("ins_spe_people_sec");
	}
	
	public void setInsSpePeopleSec(String insSpePeopleSec) {
		setString("ins_spe_people_sec", insSpePeopleSec);
	}
	/**
	 * 第二被保人 乡村人口标识
	 */
	public BigDecimal getRuralPopulationFlagSec() {
		return getBigDecimal("rural_population_flag_sec");
	}
	
	public void setRuralPopulationFlagSec(BigDecimal ruralPopulationFlagSec) {
		setBigDecimal("rural_population_flag_sec", ruralPopulationFlagSec);
	}
	/**
	 * 第二被保人 残疾人标识
	 */
	public BigDecimal getDisabilityFlagSec() {
		return getBigDecimal("disability_flag_sec");
	}
	
	public void setDisabilityFlagSec(BigDecimal disabilityFlagSec) {
		setBigDecimal("disability_flag_sec", disabilityFlagSec);
	}
	/**
	 * 是否预录入
	 */
	public BigDecimal getPreEntryFlag() {
		return getBigDecimal("pre_entry_flag");
	}
	public void setPreEntryFlag(BigDecimal preEntryFlag) {
		setBigDecimal("pre_entry_flag", preEntryFlag);
	}

	/**
	 * 重投标识，0-首次投保，1-第一次重投，2-第二次重投
	 */
	public BigDecimal getReviewFlag() {
		return getBigDecimal("review_flag");
	}
	/**
	 * 重投标识，0-首次投保，1-第一次重投，2-第二次重投
	 */
	public void setReviewFlag(BigDecimal reviewFlag) {
		setBigDecimal("review_flag",reviewFlag);
	}
	/**
	 * 是否复核修改
	 */
	public BigDecimal getCheckFlag() {
		return getBigDecimal("check_flag");
	}
	/**
	 * 是否复核修改
	 */
	public void setCheckFlag(BigDecimal checkFlag) {
		setBigDecimal("check_flag",checkFlag);
	}
	/**
	 * 是否复核修改结果
	 */
	public BigDecimal getCheckFlaged() {
		return getBigDecimal("check_flaged");
	}
	/**
	 * 是否复核修改结果
	 */
	public void setCheckFlaged(BigDecimal checkFlaged) {
		setBigDecimal("check_flaged",checkFlaged);
	}
	/**
	*	纳税地区
	**/
	public void setTaxDistrict(String taxDistrict) {
		setString("tax_district", taxDistrict);
	}

	public String getTaxDistrict() {
		return getString("tax_district");
	}
	/**
	*	退休年龄
	**/
	public void setRetirementAge(BigDecimal retirementAge) {
		setBigDecimal("retirement_age", retirementAge);
	}

	public BigDecimal getRetirementAge() {
		return getBigDecimal("retirement_age");
	}
	/**
	*	所在单位的统一社会信用代码
	**/
	public void setBusSreDeptCode(String busSreDeptCode) {
		setString("bus_sre_dept_code", busSreDeptCode);
	}

	public String getBusSreDeptCode() {
		return getString("bus_sre_dept_code");
	}

	/**
	*	参保人身份类型
	**/
	public void setInsuredType(String insuredType) {
		setString("insured_type", insuredType);
	}

	public String getInsuredType() {
		return getString("insured_type");
	}

	
	/**
	*	纳税人识别号
	**/
	public void setTaxNumber(String taxNumber) {
		setString("tax_number", taxNumber);
	}

	public String getTaxNumber() {
		return getString("tax_number");
	}
	
	/**
	 * 投保人手机号码验真结果
	 */
	public String getPhReturnPhone() {
		return getString("ph_return_phone");
	}
	/**
	 * 投保人手机号码验真结果
	 */
	public void setPhReturnPhone(String phReturnPhone) {
		setString("ph_return_phone", phReturnPhone);
	}
	
	/**
	 * 被保人手机号码验真结果
	 */
	public String getLiReturnPhone() {
		return getString("li_return_phone");
	}
	/**
	 * 被保人手机号码验真结果
	 */
	public void setLiReturnPhone(String liReturnPhone) {
		setString("li_return_phone", liReturnPhone);
	}
	/**
	 * 受益人手机号码验真结果
	 */
	public String getBeReturnPhone() {
		return getString("be_return_phone");
	}
	/**
	 * 受益人手机号码验真结果
	 */
	public void setBeReturnPhone(String beReturnPhone) {
		setString("be_return_phone", beReturnPhone);
	}
	
	public BigDecimal getIsSelfInsuredSe() {
		return getBigDecimal("is_self_insured_se");
	}
	
	public void setIsSelfInsuredSe(BigDecimal isSelfInsuredSe) {
		setBigDecimal("is_self_insured_se",isSelfInsuredSe);
	}
	
	public BigDecimal getIsMutualInsuredSe() {
		return getBigDecimal("is_mutual_insured_se");
	}
	
	public void setIsMutualInsuredSe(BigDecimal isMutualInsuredSe) {
		setBigDecimal("is_mutual_insured_se",isMutualInsuredSe);
	}
	public BigDecimal getHolderNewResident() {
		return getBigDecimal("holder_new_resident");
	}
	public void setHolderNewResident(BigDecimal holderNewResident) {
		setBigDecimal("holder_new_resident",holderNewResident);
	}
	public BigDecimal getInsuredNewResident() {
		return getBigDecimal("insured_new_resident");
	}
	public void setInsuredNewResident(BigDecimal insuredNewResident) {
		setBigDecimal("insured_new_resident",insuredNewResident);
	}
	public BigDecimal getInsuredNewResidentSe() {
		return getBigDecimal("insured_new_resident_se");
	}
	public void setInsuredNewResidentSe(BigDecimal insuredNewResidentSe) {
		setBigDecimal("insured_new_resident_se",insuredNewResidentSe);
	}
	public BigDecimal getHolderNewResidentReveal() {
		return getBigDecimal("holder_new_resident_reveal");
	}
	public void setHolderNewResidentReveal(BigDecimal holderNewResidentReveal) {
		setBigDecimal("holder_new_resident_reveal",holderNewResidentReveal);
	}
	public BigDecimal getInsuredNewResidentReveal() {
		return getBigDecimal("insured_new_resident_reveal");
	}
	public void setInsuredNewResidentReveal(BigDecimal insuredNewResidentReveal) {
		setBigDecimal("insured_new_resident_reveal",insuredNewResidentReveal);
	}
	public BigDecimal getInsuredNewResidentSeReveal() {
		return getBigDecimal("insured_new_resident_se_reveal");
	}
	public void setInsuredNewResidentSeReveal(BigDecimal insuredNewResidentSeReveal) {
		setBigDecimal("insured_new_resident_se_reveal",insuredNewResidentSeReveal);
	}
	
	//被保人录入顺序
		public BigDecimal getInputSequence() {
			return getBigDecimal("input_sequence");
		}

		public void setInputSequence(BigDecimal inputSequence) {
			setBigDecimal("input_sequence", inputSequence);
		}
		public String getIsInsuredNewResident() {
			return getString("is_insured_new_resident");
			
		}
		
		public void setIsInsuredNewResident(String isInsuredNewResident) {
			setString("is_insured_new_resident",isInsuredNewResident);
		}
		/**
		 * 投保人出生日期
		 */
		public Date getCustomerBirthday() {
			return getUtilDate("customer_birthday");
		}

		public void setCustomerBirthday(Date customerBirthday) {
			setUtilDate("customer_birthday", customerBirthday);
		}
		/**
		 * 投保人年龄
		 */
		public String getCustomerAge() {
			return getString("customer_age");
		}

		public void setCustomerAge(String customerAge) {
			setString("customer_age", customerAge);
		}
		/**
		 * 	抽捡结果
		 */
	    public String getIsDrqDraw1(){
	        return getString("is_drq_draw1");
	    }
	    public void setIsDrqDraw1(String isDrqDraw1){
	        setString("is_drq_draw1",isDrqDraw1);
	    }
	    /**
		 * 	是否双录
		 */
	    public String getIsDrqFlag(){
	        return getString("is_drq_flag");
	    }
	    public void setIsDrqFlag(String isDrqFlag){
	        setString("is_drq_flag",isDrqFlag);
	    }
	    /**
		 * 	抽捡结果
		 */
	    public BigDecimal getIsDrqDraw(){
	        return getBigDecimal("is_drq_draw");
	    }
	    public void setIsDrqDraw(BigDecimal isDrqDraw){
	        setBigDecimal("is_drq_draw",isDrqDraw);
	    }
		/**
		 * 双录标记类型
		 */
	    public BigDecimal getDrqFlag(){
	        return getBigDecimal("drq_flag");
	    }
	    public void setDrqFlag(BigDecimal drqFlag){
	    	setBigDecimal("drq_flag",drqFlag);
	    }
	    /**
	   	 * 投被保险人关系
	   	 */
	    public String getHolderInsuredRelation(){
	        return getString("holder_Insured_Relation");
	    }
	    public void setHolderInsuredRelation(String holderInsuredRelation){
	        setString("holder_Insured_Relation",holderInsuredRelation);
	    }
	    /**
	   	 * 被保险人与受益人关系
	   	 */
	    public String getInsuredBeneRelation(){
	        return getString("insured_Bene_Relation");
	    }
	    public void setInsuredBeneRelation(String insuredBeneRelation){
	        setString("insured_Bene_Relation",insuredBeneRelation);
	    } 
	    /**
	   	 * 是否CRS产品
	   	 */
	    public String getTaxRevenueFlag(){
	        return getString("TAX_REVENUE_FLAG");
	    }
	    public void setTaxRevenueFlag(String taxRevenueFlag){
	        setString("TAX_REVENUE_FLAG",taxRevenueFlag);
	    }
}
