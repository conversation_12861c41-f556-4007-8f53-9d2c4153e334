package com.nci.tunan.qry.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 投被保人shoujihaoVO对象
 * <AUTHOR> <EMAIL>
 * @date 2020-9-20 上午11:00:06
 * @.belongToModule 综合查询-VO对象
 */
public class QryPolicyPhoneVO extends BaseVO {
	/** 
	* 序列id
	*/ 
	private static final long serialVersionUID = 1L;
	/**
	 * 投保单号
	 */
	private String applyCode;
	/**
	 * 投保人手机号
	 */
	private String appntPhone;
	/**
	 * 被保人手机号
	 */
	private String insuredPhone;

	/**
	 * 第二被保人手机号
	 */
	private String secondInsuredPhone;
	
	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getAppntPhone() {
		return appntPhone;
	}

	public void setAppntPhone(String appntPhone) {
		this.appntPhone = appntPhone;
	}

	public String getInsuredPhone() {
		return insuredPhone;
	}

	public void setInsuredPhone(String insuredPhone) {
		this.insuredPhone = insuredPhone;
	}
	
	public String getSecondInsuredPhone() {
		return secondInsuredPhone;
	}

	public void setSecondInsuredPhone(String secondInsuredPhone) {
		this.secondInsuredPhone = secondInsuredPhone;
	}

	@Override
	public String toString() {
		return "QryPhoneVO [applyCode=" + applyCode + ", appntPhone="
				+ appntPhone + ", insuredPhone=" + insuredPhone + "]";
	}

	@Override
	public String getBizId() {
		return null;
	}

}
