package com.nci.tunan.qry.interfaces.model.cus.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

public class MaturityOutstandingBO extends BaseVO  {
	private String sourceType;	//受理渠道
	private String manageCode; //管理机构
	private String organName;	//受理机构名称
	private String nbSalesmaninfo;//新契约服务人员
	private String serviceSalesmaninfo;//服务人员
	/**
     * 原贷款起息日
     */
    private Date interestStartDate;
	public Date getInterestStartDate() {
		return interestStartDate;
	}

	public void setInterestStartDate(Date interestStartDate) {
		this.interestStartDate = interestStartDate;
	}
	private String channelOrgCode; //销售机构
	
	private Date repayDueDate; //贷款到期
	
	private Date beginRepayDueDate;  //贷款到期起期
	
	private Date endRepayDueDate;	//贷款到期止期
	
	private String policyCode;	//保单号
	
	private String applyCode;	//申请号
	
	private String acceptCode;	//受理号
	
	private Date reviewTime;	//复核日期
	
	private String acceptStatus; //受理状态
	
	private Date insertTime;	//申请确认日期
	
	private Date validateTime; //生效日期
	
	private String serviceCode; //保全项目
	
	private Date applyTime;	//申请时间
	
	private String organCode;	//受理机构
	
	private String channelType;	//销售渠道
	
	private String serviceType;	//申请方式
	
	private String busiProdCode;	//险种代码
	
	private String busiProdName;	//险种名称
	
	private String masterBusiItemId;	//主附险标志
	
	private Date planValidate;	//险种生效日期
	
	private String autoLoan;	//自动续贷
	
	private String holderName;	//投保人姓名
	
	private String mobileTel;	//投保人移动电话
	
	private String offenUseTel;	//投保人固定电话
	
	private String insuredName;	//被保人姓名
	
	private String itemName;	//责任
	
	private BigDecimal thisValue;	//现价
	
	private BigDecimal thisTex;	//可贷比
	
	private BigDecimal thisSum;	//贷款限额
	
	private Date loanStartDate;	//贷款起期
	
	private Date finishTime;	//到账日期
	
	private BigDecimal initRate;	//初始利率
	
	private BigDecimal doubleRate;	//逾期/二阶利率
	
	private BigDecimal threeRate;		//三阶利率
	
	private BigDecimal capitalBalance; //贷款本金
	
	private String loanNum;	//续贷原贷款本息和
	
	private BigDecimal hexnum;	//印花税(以前为10，现在变更为35)
	
	private BigDecimal interestnum;	//到期利息
	
	private BigDecimal feeAmount;	//补退费金额 
	
	private String inputerInfo;	//录入人信息
	
	private String accepterInfo;	//受理人信息
	
	private String reviewerInfo;	//复核人信息
	
	private String minputerInfo;	//契约录入人信息
	
	private String manageName;	//保单管理机构名称
	
	private String channelOrgName;	//保单销售机构名称
	
	private String statusName;//保单状态
	private String causeDesc;//终止/失效原因
	
	/**
	 * 多主险标识
	 */
	private String multiMainriskFlag;
	
	/**
	 * 
	 *  投保人年龄
	 */
	private String policyHolderAge;
	
	/**
	 * 
	 *  是否支付至既往交费账户
	 */
	private String iszfjwzh;
	
	public String getPolicyHolderAge() {
		return policyHolderAge;
	}

	public void setPolicyHolderAge(String policyHolderAge) {
		this.policyHolderAge = policyHolderAge;
	}

	public String getIszfjwzh() {
		return iszfjwzh;
	}

	public void setIszfjwzh(String iszfjwzh) {
		this.iszfjwzh = iszfjwzh;
	}
	
	public String getMultiMainriskFlag() {
		return multiMainriskFlag;
	}

	public void setMultiMainriskFlag(String multiMainriskFlag) {
		this.multiMainriskFlag = multiMainriskFlag;
	}
	
	public String getChannelOrgCode() {
		return channelOrgCode;
	}

	public void setChannelOrgCode(String channelOrgCode) {
		this.channelOrgCode = channelOrgCode;
	}

	public Date getRepayDueDate() {
		return repayDueDate;
	}

	public void setRepayDueDate(Date repayDueDate) {
		this.repayDueDate = repayDueDate;
	}

	public Date getBeginRepayDueDate() {
		return beginRepayDueDate;
	}

	public void setBeginRepayDueDate(Date beginRepayDueDate) {
		this.beginRepayDueDate = beginRepayDueDate;
	}

	public Date getEndRepayDueDate() {
		return endRepayDueDate;
	}

	public void setEndRepayDueDate(Date endRepayDueDate) {
		this.endRepayDueDate = endRepayDueDate;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getAcceptCode() {
		return acceptCode;
	}

	public void setAcceptCode(String acceptCode) {
		this.acceptCode = acceptCode;
	}

	public Date getReviewTime() {
		return reviewTime;
	}

	public void setReviewTime(Date reviewTime) {
		this.reviewTime = reviewTime;
	}

	public String getAcceptStatus() {
		return acceptStatus;
	}

	public void setAcceptStatus(String acceptStatus) {
		this.acceptStatus = acceptStatus;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	public Date getValidateTime() {
		return validateTime;
	}

	public void setValidateTime(Date validateTime) {
		this.validateTime = validateTime;
	}

	public String getServiceCode() {
		return serviceCode;
	}

	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getServiceType() {
		return serviceType;
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public String getBusiProdName() {
		return busiProdName;
	}

	public void setBusiProdName(String busiProdName) {
		this.busiProdName = busiProdName;
	}

	public String getMasterBusiItemId() {
		return masterBusiItemId;
	}

	public void setMasterBusiItemId(String masterBusiItemId) {
		this.masterBusiItemId = masterBusiItemId;
	}

	public Date getPlanValidate() {
		return planValidate;
	}

	public void setPlanValidate(Date planValidate) {
		this.planValidate = planValidate;
	}

	public String getAutoLoan() {
		return autoLoan;
	}

	public void setAutoLoan(String autoLoan) {
		this.autoLoan = autoLoan;
	}

	public String getHolderName() {
		return holderName;
	}

	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}

	public String getMobileTel() {
		return mobileTel;
	}

	public void setMobileTel(String mobileTel) {
		this.mobileTel = mobileTel;
	}

	public String getOffenUseTel() {
		return offenUseTel;
	}

	public void setOffenUseTel(String offenUseTel) {
		this.offenUseTel = offenUseTel;
	}

	public String getInsuredName() {
		return insuredName;
	}

	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	
	public BigDecimal getThisValue() {
		return thisValue;
	}

	public void setThisValue(BigDecimal thisValue) {
		this.thisValue = thisValue;
	}

	public BigDecimal getThisTex() {
		return thisTex;
	}

	public void setThisTex(BigDecimal thisTex) {
		this.thisTex = thisTex;
	}

	public BigDecimal getThisSum() {
		return thisSum;
	}

	public void setThisSum(BigDecimal thisSum) {
		this.thisSum = thisSum;
	}

	public Date getLoanStartDate() {
		return loanStartDate;
	}

	public void setLoanStartDate(Date loanStartDate) {
		this.loanStartDate = loanStartDate;
	}

	public Date getFinishTime() {
		return finishTime;
	}

	public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}

	public BigDecimal getInitRate() {
		return initRate;
	}

	public void setInitRate(BigDecimal initRate) {
		this.initRate = initRate;
	}

	public BigDecimal getDoubleRate() {
		return doubleRate;
	}

	public void setDoubleRate(BigDecimal doubleRate) {
		this.doubleRate = doubleRate;
	}

	public BigDecimal getThreeRate() {
		return threeRate;
	}

	public void setThreeRate(BigDecimal threeRate) {
		this.threeRate = threeRate;
	}

	public BigDecimal getCapitalBalance() {
		return capitalBalance;
	}

	public void setCapitalBalance(BigDecimal capitalBalance) {
		this.capitalBalance = capitalBalance;
	}

	public String getLoanNum() {
		return loanNum;
	}

	public void setLoanNum(String loanNum) {
		this.loanNum = loanNum;
	}

	public BigDecimal getHexnum() {
		return hexnum;
	}

	public void setHexnum(BigDecimal hexnum) {
		this.hexnum = hexnum;
	}

	public BigDecimal getInterestnum() {
		return interestnum;
	}

	public void setInterestnum(BigDecimal interestnum) {
		this.interestnum = interestnum;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public String getInputerInfo() {
		return inputerInfo;
	}

	public void setInputerInfo(String inputerInfo) {
		this.inputerInfo = inputerInfo;
	}

	public String getAccepterInfo() {
		return accepterInfo;
	}

	public void setAccepterInfo(String accepterInfo) {
		this.accepterInfo = accepterInfo;
	}

	public String getReviewerInfo() {
		return reviewerInfo;
	}

	public void setReviewerInfo(String reviewerInfo) {
		this.reviewerInfo = reviewerInfo;
	}

	public String getMinputerInfo() {
		return minputerInfo;
	}

	public void setMinputerInfo(String minputerInfo) {
		this.minputerInfo = minputerInfo;
	}

	public String getManageName() {
		return manageName;
	}

	public void setManageName(String manageName) {
		this.manageName = manageName;
	}

	public String getChannelOrgName() {
		return channelOrgName;
	}

	public void setChannelOrgName(String channelOrgName) {
		this.channelOrgName = channelOrgName;
	}

	
	
	public String getManageCode() {
		return manageCode;
	}

	public void setManageCode(String manageCode) {
		this.manageCode = manageCode;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}
	
	
	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}
	
	public String getNbSalesmaninfo() {
		return nbSalesmaninfo;
	}

	public void setNbSalesmaninfo(String nbSalesmaninfo) {
		this.nbSalesmaninfo = nbSalesmaninfo;
	}

	public String getServiceSalesmaninfo() {
		return serviceSalesmaninfo;
	}

	public void setServiceSalesmaninfo(String serviceSalesmaninfo) {
		this.serviceSalesmaninfo = serviceSalesmaninfo;
	}

	@Override
	public String toString() {
		return "MaturityOutstandingBO [channelOrgCode=" + channelOrgCode
				+ ", repayDueDate=" + repayDueDate + ", beginRepayDueDate="
				+ beginRepayDueDate + ", endRepayDueDate=" + endRepayDueDate
				+ ", policyCode=" + policyCode + ", applyCode=" + applyCode
				+ ", acceptCode=" + acceptCode + ", reviewTime=" + reviewTime
				+ ", acceptStatus=" + acceptStatus + ", insertTime="
				+ insertTime + ", validateTime=" + validateTime
				+ ", serviceCode=" + serviceCode + ", applyTime=" + applyTime
				+ ", organCode=" + organCode + ", channelType=" + channelType
				+ ", serviceType=" + serviceType + ", busiProdCode="
				+ busiProdCode + ", busiProdName=" + busiProdName
				+ ", masterBusiItemId=" + masterBusiItemId + ", planValidate="
				+ planValidate + ", autoLoan=" + autoLoan + ", holderName="
				+ holderName + ", mobileTel=" + mobileTel + ", offenUseTel="
				+ offenUseTel + ", insuredName=" + insuredName + ", itemName="
				+ itemName + ", thisValue=" + thisValue + ", thisTex="
				+ thisTex + ", thisSum=" + thisSum + ", loanStartDate="
				+ loanStartDate + ", finishTime=" + finishTime + ", initRate="
				+ initRate + ", doubleRate=" + doubleRate + ", threeRate="
				+ threeRate + ", capitalBalance=" + capitalBalance
				+ ", loanNum=" + loanNum + ", hexnum=" + hexnum
				+ ", interestnum=" + interestnum + ", feeAmount=" + feeAmount
				+ ", inputerInfo=" + inputerInfo + ", accepterInfo="
				+ accepterInfo + ", reviewerInfo=" + reviewerInfo
				+ ", minputerInfo=" + minputerInfo + ", manageName="
				+ manageName + ", channelOrgName=" + channelOrgName + "]";
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public String getCauseDesc() {
		return causeDesc;
	}

	public void setCauseDesc(String causeDesc) {
		this.causeDesc = causeDesc;
	}

}
