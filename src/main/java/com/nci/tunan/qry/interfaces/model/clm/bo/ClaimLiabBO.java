package com.nci.tunan.qry.interfaces.model.clm.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description ClaimLiabVO对象
 * <AUTHOR> 
 * @date 2016-01-25 17:37:19  
 */
public class ClaimLiabBO extends BaseBO {   
	/**
     * @Fields isMustSign 转年金标识
     */
    private String isMustSign;
	/** 
	 * @Fields isSubsidy :  是否分期给付
	 */ 
	private BigDecimal isInstalment;
	/** 
	 * @Fields isSubsidy :  是否为津贴责任
	 */ 
	private BigDecimal isSubsidy;
     /** 
    * @Fields advancePay :  预付金额
    */ 
    private BigDecimal advancePay = new BigDecimal(0);
     /** 
    * @Fields claimType :  理赔类型
    */ 
    private String claimType;
     /** 
    * @Fields actualPay :  实际给付金额
    */ 
    private BigDecimal actualPay = new BigDecimal(0);
     /** 
    * @Fields productId :  责任组 ID
    */ 
    private BigDecimal productId;
     /** 
    * @Fields clmRemark :  备注（理算日志）
    */ 
    private String clmRemark;
     /** 
    * @Fields claimLiabId :  顺序号
    */ 
    private BigDecimal claimLiabId;
     /** 
    * @Fields itemId :  精算产品 ID
    */ 
    private BigDecimal itemId;
     /** 
    * @Fields isCommon :  是否常规给付
    */ 
    private BigDecimal isCommon;
     /** 
    * @Fields liabStartDate :  责任起期
    */ 
    private Date liabStartDate;
     /** 
    * @Fields busiProdCode :  业务产品编码
    */ 
    private String busiProdCode;
     /** 
    * @Fields isWaived :  豁免标识
    */ 
    private BigDecimal isWaived;
             /** 
    * @Fields subCaseId :  子案件ID
    */ 
    private BigDecimal subCaseId;
     /** 
    * @Fields amountBasicPay :  基础保额基础给付金额
    */ 
    private BigDecimal amountBasicPay;
     /** 
    * @Fields waiveItem :  豁免产品项目
    */ 
    private BigDecimal waiveItem;
     /** 
    * @Fields waiveReason :  豁免原因
    */ 
    private String waiveReason;
     /** 
    * @Fields busiItemId :  业务险种ID
    */ 
    private BigDecimal busiItemId;
     /** 
    * @Fields policyId :  保单ID
    */ 
    private BigDecimal policyId;
     /** 
    * @Fields liabAdjustReason :  调整原因
    */ 
    private String liabAdjustReason;
     /** 
    * @Fields makeupBasicPay :  补差金额基础给付金额
    */ 
    private BigDecimal makeupBasicPay;
     /** 
    * @Fields resistFlag :  不可抗辩标识
    */ 
    private BigDecimal resistFlag;
     /** 
    * @Fields liabilityStatus :  责任状态
    */ 
    private BigDecimal liabilityStatus;
     /** 
    * @Fields waiveStart :  豁免起期
    */ 
    private Date waiveStart;
         /** 
    * @Fields adjustPay :  调整金额
    */ 
    private BigDecimal adjustPay = new BigDecimal(0);
     /** 
    * @Fields liabId :  责任编码
    */ 
    private BigDecimal liabId;
         /** 
    * @Fields bonusBasicPay :  红利保额基础给付金额
    */ 
    private BigDecimal bonusBasicPay;
     /** 
    * @Fields liabName :  责任名称
    */ 
    private String liabName;
    /** 
     * @Fields clmAfterState :  理赔后责任状态
     */ 
    private String clmAfterState;
     /** 
    * @Fields caseId :  赔案ID
    */ 
    private BigDecimal caseId;
     /** 
    * @Fields adjustRemark :  调整备注
    */ 
    private String adjustRemark;
     /** 
    * @Fields liabConclusion :  责任赔付结论
    */ 
    private BigDecimal liabConclusion;
     /** 
    * @Fields policyCode :  保单号
    */ 
    private String policyCode;
     /** 
    * @Fields waiveAmt :  豁免总金额
    */ 
    private BigDecimal waiveAmt = new BigDecimal(0);
     /** 
    * @Fields waiveEnd :  豁免止期
    */ 
    private Date waiveEnd;
             /** 
    * @Fields calcPay :  理算金额
    */ 
    private BigDecimal calcPay = new BigDecimal(0);
     /** 
    * @Fields basicPay :  基础给付金额
    */ 
    private BigDecimal basicPay = new BigDecimal(0);
     /** 
    * @Fields liabEndDate :  责任止期
    */ 
    private Date liabEndDate;
     /** 
    * @Fields advanceDate :  预付日期
    */ 
    private Date advanceDate;
    /**
     * @Fields paidAmount : 社保给付
     */
    private BigDecimal paidAmount;
    /**
     * @Fields sumAmount : 第三方给付
     */
    private BigDecimal sumAmount;
    
    private LiabilityBO liabilityBO;

    private ClaimLiabFormulaBO claimLiabFormulaBO;
    
    private BigDecimal sumAmountBill; //账单金额
    
    private Date treatStart; //住院起始时间
    
    private Date treatEnd; //住院结束时间
    
    private BigDecimal feeAmount; //合同结算金额
    
    private BigDecimal proAmount; //保险金额
    
    private BigDecimal proBonusSa;//累计红利保额

    private BigDecimal payAmount;//终了红利
    /** 
     * @Fields beneId :  受益人ID
     */ 
    private BigDecimal beneId;
     
     /** 
      * @Fields beneBirth :  受益人出生日期
      */ 
    private Date beneBirth;
    /**
     * 受益分配ID
     */
    private BigDecimal clmPayId; 
    
    private BigDecimal masterBusiItemId;
    
    private BigDecimal gracePeriod; //宽限期
    /**
     * 给付责任与医疗费用的关系表
     */
    private List<ClaimLiabBillRelationBO> claimLiabBillRelationBOs;
    /**
     * 匹配理算日志
     * @return
     */
    private List<ClaimMatchJournaBO> claimMatchJournaBOs;
    /**
     * 险种名称
     */
    private String productNameStd;
    
     public String getProductNameStd() {
		return productNameStd;
	}

	public void setProductNameStd(String productNameStd) {
		this.productNameStd = productNameStd;
	}

	public List<ClaimMatchJournaBO> getClaimMatchJournaBOs() {
		return claimMatchJournaBOs;
	}

	public void setClaimMatchJournaBOs(List<ClaimMatchJournaBO> claimMatchJournaBOs) {
		this.claimMatchJournaBOs = claimMatchJournaBOs;
	}

	public String getIsMustSign() {
		return isMustSign;
	}

	public void setIsMustSign(String isMustSign) {
		this.isMustSign = isMustSign;
	}

	public BigDecimal getGracePeriod() {
		return gracePeriod;
	}

	public void setGracePeriod(BigDecimal gracePeriod) {
		this.gracePeriod = gracePeriod;
	}

	public BigDecimal getClmPayId() {
		return clmPayId;
	}

	public void setClmPayId(BigDecimal clmPayId) {
		this.clmPayId = clmPayId;
	}

	public LiabilityBO getLiabilityBO() {
        return liabilityBO;
    }

    public void setLiabilityBO(LiabilityBO liabilityBO) {
        this.liabilityBO = liabilityBO;
    }

    public ClaimLiabFormulaBO getClaimLiabFormulaBO() {
        return claimLiabFormulaBO;
    }

    public void setClaimLiabFormulaBO(ClaimLiabFormulaBO claimLiabFormulaBO) {
        this.claimLiabFormulaBO = claimLiabFormulaBO;
    }

    public BigDecimal getBeneId() {
        return beneId;
    }

    public void setBeneId(BigDecimal beneId) {
        this.beneId = beneId;
    }

    public Date getBeneBirth() {
        return beneBirth;
    }

    public void setBeneBirth(Date beneBirth) {
        this.beneBirth = beneBirth;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(BigDecimal sumAmount) {
        this.sumAmount = sumAmount;
    }

    public BigDecimal getSumAmountBill() {
        return sumAmountBill;
    }

    public void setSumAmountBill(BigDecimal sumAmountBill) {
        this.sumAmountBill = sumAmountBill;
    }

    public Date getTreatStart() {
        return treatStart;
    }

    public void setTreatStart(Date treatStart) {
        this.treatStart = treatStart;
    }

    public Date getTreatEnd() {
        return treatEnd;
    }

    public void setTreatEnd(Date treatEnd) {
        this.treatEnd = treatEnd;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getProAmount() {
        return proAmount;
    }

    public void setProAmount(BigDecimal proAmount) {
        this.proAmount = proAmount;
    }
    
    public void setAdvancePay(BigDecimal advancePay) {
        this.advancePay = advancePay;
    }
    
    public BigDecimal getAdvancePay() {
        return advancePay;
    }
     public void setClaimType(String claimType) {
        this.claimType = claimType;
    }
    
    public String getClaimType() {
        return claimType;
    }
     public void setActualPay(BigDecimal actualPay) {
        this.actualPay = actualPay;
    }
    
    public BigDecimal getActualPay() {
        return actualPay;
    }
     public void setProductId(BigDecimal productId) {
        this.productId = productId;
    }
    
    public BigDecimal getProductId() {
        return productId;
    }
     public void setClmRemark(String clmRemark) {
        this.clmRemark = clmRemark;
    }
    
    public String getClmRemark() {
        return clmRemark;
    }
     public void setClaimLiabId(BigDecimal claimLiabId) {
        this.claimLiabId = claimLiabId;
    }
    
    public BigDecimal getClaimLiabId() {
        return claimLiabId;
    }
     public void setItemId(BigDecimal itemId) {
        this.itemId = itemId;
    }
    
    public BigDecimal getItemId() {
        return itemId;
    }
     public void setIsCommon(BigDecimal isCommon) {
        this.isCommon = isCommon;
    }
    
    public BigDecimal getIsCommon() {
        return isCommon;
    }
     public void setLiabStartDate(Date liabStartDate) {
        this.liabStartDate = liabStartDate;
    }
    
    public Date getLiabStartDate() {
        return liabStartDate;
    }
     public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }
    
    public String getBusiProdCode() {
        return busiProdCode;
    }
     public void setIsWaived(BigDecimal isWaived) {
        this.isWaived = isWaived;
    }
    
    public BigDecimal getIsWaived() {
        return isWaived;
    }
             public void setSubCaseId(BigDecimal subCaseId) {
        this.subCaseId = subCaseId;
    }
    
    public BigDecimal getSubCaseId() {
        return subCaseId;
    }
     public void setAmountBasicPay(BigDecimal amountBasicPay) {
        this.amountBasicPay = amountBasicPay;
    }
    
    public BigDecimal getAmountBasicPay() {
        return amountBasicPay;
    }
     public void setWaiveItem(BigDecimal waiveItem) {
        this.waiveItem = waiveItem;
    }
    
    public BigDecimal getWaiveItem() {
        return waiveItem;
    }
     public void setWaiveReason(String waiveReason) {
        this.waiveReason = waiveReason;
    }
    
    public String getWaiveReason() {
        return waiveReason;
    }
     public void setBusiItemId(BigDecimal busiItemId) {
        this.busiItemId = busiItemId;
    }
    
    public BigDecimal getBusiItemId() {
        return busiItemId;
    }
     public void setPolicyId(BigDecimal policyId) {
        this.policyId = policyId;
    }
    
    public BigDecimal getPolicyId() {
        return policyId;
    }
     public void setLiabAdjustReason(String liabAdjustReason) {
        this.liabAdjustReason = liabAdjustReason;
    }
    
    public String getLiabAdjustReason() {
        return liabAdjustReason;
    }
     public void setMakeupBasicPay(BigDecimal makeupBasicPay) {
        this.makeupBasicPay = makeupBasicPay;
    }
    
    public BigDecimal getMakeupBasicPay() {
        return makeupBasicPay;
    }
     public void setResistFlag(BigDecimal resistFlag) {
        this.resistFlag = resistFlag;
    }
    
    public BigDecimal getResistFlag() {
        return resistFlag;
    }
     public void setLiabilityStatus(BigDecimal liabilityStatus) {
        this.liabilityStatus = liabilityStatus;
    }
    
    public BigDecimal getLiabilityStatus() {
        return liabilityStatus;
    }
     public void setWaiveStart(Date waiveStart) {
        this.waiveStart = waiveStart;
    }
    
    public Date getWaiveStart() {
        return waiveStart;
    }
         public void setAdjustPay(BigDecimal adjustPay) {
        this.adjustPay = adjustPay;
    }
    
    public BigDecimal getAdjustPay() {
        return adjustPay;
    }
     public void setLiabId(BigDecimal liabId) {
        this.liabId = liabId;
    }
    
    public BigDecimal getLiabId() {
        return liabId;
    }
         public void setBonusBasicPay(BigDecimal bonusBasicPay) {
        this.bonusBasicPay = bonusBasicPay;
    }
    
    public BigDecimal getBonusBasicPay() {
        return bonusBasicPay;
    }
     public void setLiabName(String liabName) {
        this.liabName = liabName;
    }
    
    public String getLiabName() {
        return liabName;
    }
     public void setCaseId(BigDecimal caseId) {
        this.caseId = caseId;
    }
    
    public BigDecimal getCaseId() {
        return caseId;
    }
     public void setAdjustRemark(String adjustRemark) {
        this.adjustRemark = adjustRemark;
    }
    
    public String getAdjustRemark() {
        return adjustRemark;
    }
     public void setLiabConclusion(BigDecimal liabConclusion) {
        this.liabConclusion = liabConclusion;
    }
    
    public BigDecimal getLiabConclusion() {
        return liabConclusion;
    }
     public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }
    
    public String getPolicyCode() {
        return policyCode;
    }
     public void setWaiveAmt(BigDecimal waiveAmt) {
        this.waiveAmt = waiveAmt;
    }
    
    public BigDecimal getWaiveAmt() {
        return waiveAmt;
    }
     public void setWaiveEnd(Date waiveEnd) {
        this.waiveEnd = waiveEnd;
    }
    
    public Date getWaiveEnd() {
        return waiveEnd;
    }
             public void setCalcPay(BigDecimal calcPay) {
        this.calcPay = calcPay;
    }
    
    public BigDecimal getCalcPay() {
        return calcPay;
    }
     public void setBasicPay(BigDecimal basicPay) {
        this.basicPay = basicPay;
    }
    
    public BigDecimal getBasicPay() {
        return basicPay;
    }
     public void setLiabEndDate(Date liabEndDate) {
        this.liabEndDate = liabEndDate;
    }
    
    public Date getLiabEndDate() {
        return liabEndDate;
    }
     public void setAdvanceDate(Date advanceDate) {
        this.advanceDate = advanceDate;
    }
    
    public Date getAdvanceDate() {
        return advanceDate;
    }
      
    public String getClmAfterState() {
        return clmAfterState;
    }

    public void setClmAfterState(String clmAfterState) {
        this.clmAfterState = clmAfterState;
    }

    public BigDecimal getMasterBusiItemId() {
		return masterBusiItemId;
	}

	public void setMasterBusiItemId(BigDecimal masterBusiItemId) {
		this.masterBusiItemId = masterBusiItemId;
	}

	public List<ClaimLiabBillRelationBO> getClaimLiabBillRelationBOs() {
		return claimLiabBillRelationBOs;
	}

	public void setClaimLiabBillRelationBOs(
			List<ClaimLiabBillRelationBO> claimLiabBillRelationBOs) {
		this.claimLiabBillRelationBOs = claimLiabBillRelationBOs;
	}

	public BigDecimal getIsSubsidy() {
		return isSubsidy;
	}

	public void setIsSubsidy(BigDecimal isSubsidy) {
		this.isSubsidy = isSubsidy;
	}

	@Override
    public String getBizId() {
        return null;
    }
    
    public BigDecimal getProBonusSa() {
		return proBonusSa;
	}

	public void setProBonusSa(BigDecimal proBonusSa) {
		this.proBonusSa = proBonusSa;
	}

	public BigDecimal getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public BigDecimal getIsInstalment() {
		return isInstalment;
	}

	public void setIsInstalment(BigDecimal isInstalment) {
		this.isInstalment = isInstalment;
	}

	@Override
    public String toString() {
        return "ClaimLiabVO [" +
                "advancePay="+advancePay+","+
"claimType="+claimType+","+
"actualPay="+actualPay+","+
"productId="+productId+","+
"clmRemark="+clmRemark+","+
"claimLiabId="+claimLiabId+","+
"itemId="+itemId+","+
"isCommon="+isCommon+","+
"liabStartDate="+liabStartDate+","+
"busiProdCode="+busiProdCode+","+
"isWaived="+isWaived+","+
"subCaseId="+subCaseId+","+
"amountBasicPay="+amountBasicPay+","+
"waiveItem="+waiveItem+","+
"waiveReason="+waiveReason+","+
"busiItemId="+busiItemId+","+
"policyId="+policyId+","+
"liabAdjustReason="+liabAdjustReason+","+
"makeupBasicPay="+makeupBasicPay+","+
"resistFlag="+resistFlag+","+
"liabilityStatus="+liabilityStatus+","+
"waiveStart="+waiveStart+","+
"adjustPay="+adjustPay+","+
"liabId="+liabId+","+
"bonusBasicPay="+bonusBasicPay+","+
"liabName="+liabName+","+
"caseId="+caseId+","+
"adjustRemark="+adjustRemark+","+
"liabConclusion="+liabConclusion+","+
"policyCode="+policyCode+","+
"waiveAmt="+waiveAmt+","+
"waiveEnd="+waiveEnd+","+
"calcPay="+calcPay+","+
"basicPay="+basicPay+","+
"liabEndDate="+liabEndDate+","+
"advanceDate="+advanceDate+"]";
    }
}
