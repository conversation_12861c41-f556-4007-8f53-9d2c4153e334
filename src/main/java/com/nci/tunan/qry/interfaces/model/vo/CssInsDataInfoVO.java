package com.nci.tunan.qry.interfaces.model.vo;

import com.nci.udmp.framework.model.*;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description CssInsDataInfoVO对象
 * <AUTHOR>
 * @date 2015-07-20 11:31:56
 */
public class CssInsDataInfoVO extends BaseVO {
    /**
     * @Fields insDate : null
     */
    private Date insDate;
    /**
     * @Fields planId : null
     */
    private BigDecimal planId;
    /**
     * @Fields remark : null
     */
    private String remark;
    /**
     * @Fields planType : 1:定时临检 2:及时临检
     */
    private String planType;
    /**
     * @Fields jobCode : null
     */
    private String jobCode;
    /**
     * @Fields operator : 记录员工的id
     */
    private String operator;
    /**
     * @Fields dataInfoId : null
     */
    private BigDecimal dataInfoId;
    /**
     * @Fields failReason : null
     */
    private String failReason;
    /**
     * @Fields isPass : 是：Y 否：N
     */
    private BigDecimal isPass;
    /**
     * @Fields panelCode : null
     */
    private String panelCode;
    /**
     * @Fields version : 控制数据新增、修改版本
     */
    private BigDecimal version;
    /**
     * @Fields branchCode : null
     */
    private String branchCode;
    /**
     * @Fields businessType : 业务类型: 1.保全 2.理赔 3.投诉 4.新契约
     */
    private String businessType;
    /**
     * @Fields businessItem : 根据选择的业务类型选择不同的业务项目 1：保全项目 2：理赔项目 3：新契约项目
     */
    private String businessItem;
    /**
     * @Fields processDate : null
     */
    private Date processDate;
    /**
     * @Fields insOperator : 记录临检人员id
     */
    private String insOperator;

    private String checkDateType;
    private String startDate;
    private String endDate;
    private String operatorType;

    private String failNum; // 差错件个数
    private String missPercent; // 差错件百分比
    private String itemPercent; // 抽检件数
    private BigDecimal itemRatio; // 抽检比例
    private BigDecimal itemTotal; // 抽检总数

    public BigDecimal getItemTotal() {
        return itemTotal;
    }

    public void setItemTotal(BigDecimal itemTotal) {
        this.itemTotal = itemTotal;
    }

    public BigDecimal getItemRatio() {
        return itemRatio;
    }

    public void setItemRatio(BigDecimal itemRatio) {
        this.itemRatio = itemRatio;
    }

    public String getFailNum() {
        return failNum;
    }

    public void setFailNum(String failNum) {
        this.failNum = failNum;
    }

    public String getMissPercent() {
        return missPercent;
    }

    public void setMissPercent(String missPercent) {
        this.missPercent = missPercent;
    }

    public String getItemPercent() {
        return itemPercent;
    }

    public void setItemPercent(String itemPercent) {
        this.itemPercent = itemPercent;
    }

    public String getCheckDateType() {
        return checkDateType;
    }

    public void setCheckDateType(String checkDateType) {
        this.checkDateType = checkDateType;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }

    public void setInsDate(Date insDate) {
        this.insDate = insDate;
    }

    public Date getInsDate() {
        return insDate;
    }

    public void setPlanId(BigDecimal planId) {
        this.planId = planId;
    }

    public BigDecimal getPlanId() {
        return planId;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setPlanType(String planType) {
        this.planType = planType;
    }

    public String getPlanType() {
        return planType;
    }

    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    public String getJobCode() {
        return jobCode;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperator() {
        return operator;
    }

    public void setDataInfoId(BigDecimal dataInfoId) {
        this.dataInfoId = dataInfoId;
    }

    public BigDecimal getDataInfoId() {
        return dataInfoId;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setIsPass(BigDecimal isPass) {
        this.isPass = isPass;
    }

    public BigDecimal getIsPass() {
        return isPass;
    }

    public void setPanelCode(String panelCode) {
        this.panelCode = panelCode;
    }

    public String getPanelCode() {
        return panelCode;
    }

    public void setVersion(BigDecimal version) {
        this.version = version;
    }

    public BigDecimal getVersion() {
        return version;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessItem(String businessItem) {
        this.businessItem = businessItem;
    }

    public String getBusinessItem() {
        return businessItem;
    }

    public void setProcessDate(Date processDate) {
        this.processDate = processDate;
    }

    public Date getProcessDate() {
        return processDate;
    }

    public void setInsOperator(String insOperator) {
        this.insOperator = insOperator;
    }

    public String getInsOperator() {
        return insOperator;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "CssInsDataInfoVO [" + "insDate=" + insDate + ",planId=" + planId + ",remark=" + remark + ","
                + "planType=" + planType + ",jobCode=" + jobCode + "," + "operator=" + operator + "," + "dataInfoId="
                + dataInfoId + ",failReason=" + failReason + ",isPass=" + isPass + "," + "panelCode=" + panelCode
                + ",version=" + version + ",branchCode=" + branchCode + "," + "businessType=" + businessType
                + ",businessItem=" + businessItem + ",processDate=" + processDate + ",insOperator=" + insOperator + "]";
    }
}
