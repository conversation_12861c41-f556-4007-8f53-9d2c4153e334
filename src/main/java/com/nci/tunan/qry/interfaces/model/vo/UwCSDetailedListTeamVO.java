package com.nci.tunan.qry.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseBO;

/**
 * 
 * @description 核保清单
 * @<NAME_EMAIL>
 * @date 2015-1-24 上午11:26:32
 * @.belongToModule 综合查询-VO对象
 */
public class UwCSDetailedListTeamVO extends BaseBO {
	/**
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 保全受理号
	 */
	private String acceptCode;
	/**
	 * @Fields csService : null
	 */
	private String csService;
	/**
	 * @Fields uwSubmitTime : null
	 */
	private String bizDate;

	public String getBizDate() {
		return bizDate;
	}

	public void setBizDate(String bizDate) {
		this.bizDate = bizDate;
	}

	/* add by wangwu_wb 2016-4-12 biz_date */
	private String uwSubmitTime;
	
	private String uwSubmitTimeStart;
	
	private String uwSubmitTimeEnd;
	
	private String uwFinishTimeStart;
	
	private String uwFinishTimeEnd;
	
	private String csAcceptTimeStart;
	
	private String csAcceptTimeEnd;
	/**
	 * 保单机构
	 */
	private String organCode;
	/**
	 * 保单机构名称
	 */
	private String organName;

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	/**
	 * getter method
	 * 
	 * @return the acceptCode
	 */

	public String getAcceptCode() {
		return acceptCode;
	}

	public String getUwSubmitTimeStart() {
		return uwSubmitTimeStart;
	}

	public void setUwSubmitTimeStart(String uwSubmitTimeStart) {
		this.uwSubmitTimeStart = uwSubmitTimeStart;
	}

	public String getUwSubmitTimeEnd() {
		return uwSubmitTimeEnd;
	}

	public void setUwSubmitTimeEnd(String uwSubmitTimeEnd) {
		this.uwSubmitTimeEnd = uwSubmitTimeEnd;
	}

	public String getUwFinishTimeStart() {
		return uwFinishTimeStart;
	}

	public void setUwFinishTimeStart(String uwFinishTimeStart) {
		this.uwFinishTimeStart = uwFinishTimeStart;
	}

	public String getUwFinishTimeEnd() {
		return uwFinishTimeEnd;
	}

	public void setUwFinishTimeEnd(String uwFinishTimeEnd) {
		this.uwFinishTimeEnd = uwFinishTimeEnd;
	}

	public String getCsAcceptTimeStart() {
		return csAcceptTimeStart;
	}

	public void setCsAcceptTimeStart(String csAcceptTimeStart) {
		this.csAcceptTimeStart = csAcceptTimeStart;
	}

	public String getCsAcceptTimeEnd() {
		return csAcceptTimeEnd;
	}

	public void setCsAcceptTimeEnd(String csAcceptTimeEnd) {
		this.csAcceptTimeEnd = csAcceptTimeEnd;
	}

	/**
	 * setter method
	 * 
	 * @param acceptCode
	 *            the acceptCode to set
	 */

	public void setAcceptCode(String acceptCode) {
		this.acceptCode = acceptCode;
	}

	/**
	 * getter method
	 * 
	 * @return the csService
	 */

	public String getCsService() {
		return csService;
	}

	/**
	 * setter method
	 * 
	 * @param csService
	 *            the csService to set
	 */

	public void setCsService(String csService) {
		this.csService = csService;
	}

	/**
	 * getter method
	 * 
	 * @return the uwSubmitTime
	 */

	public String getUwSubmitTime() {
		return uwSubmitTime;
	}

	/**
	 * setter method
	 * 
	 * @param uwSubmitTime
	 *            the uwSubmitTime to set
	 */

	public void setUwSubmitTime(String uwSubmitTime) {
		this.uwSubmitTime = uwSubmitTime;
	}

	/**
	 * getter method
	 * 
	 * @return the policyCode
	 */

	public String getPolicyCode() {
		return policyCode;
	}

	/**
	 * setter method
	 * 
	 * @param policyCode
	 *            the policyCode to set
	 */

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	/**
	 * getter method
	 * 
	 * @return the realName
	 */

	public String getRealName() {
		return realName;
	}

	/**
	 * setter method
	 * 
	 * @param realName
	 *            the realName to set
	 */

	public void setRealName(String realName) {
		this.realName = realName;
	}

	/**
	 * getter method
	 * 
	 * @return the userName
	 */

	public String getUserName() {
		return userName;
	}

	/**
	 * setter method
	 * 
	 * @param userName
	 *            the userName to set
	 */

	public void setUserName(String userName) {
		this.userName = userName;
	}

	/**
	 * getter method
	 * 
	 * @return the uwDecision
	 */

	public String getUwDecision() {
		return uwDecision;
	}

	/**
	 * setter method
	 * 
	 * @param uwDecision
	 *            the uwDecision to set
	 */

	public void setUwDecision(String uwDecision) {
		this.uwDecision = uwDecision;
	}

	/**
	 * getter method
	 * 
	 * @return the uwFinishTime
	 */

	public String getUwFinishTime() {
		return uwFinishTime;
	}

	/**
	 * setter method
	 * 
	 * @param uwFinishTime
	 *            the uwFinishTime to set
	 */

	public void setUwFinishTime(String uwFinishTime) {
		this.uwFinishTime = uwFinishTime;
	}

	/**
	 * getter method
	 * 
	 * @return the itemId
	 */

	public String getItemId() {
		return itemId;
	}

	/**
	 * setter method
	 * 
	 * @param itemId
	 *            the itemId to set
	 */

	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	/**
	 * getter method
	 * 
	 * @return the decisionCode
	 */

	public String getDecisionCode() {
		return decisionCode;
	}

	/**
	 * setter method
	 * 
	 * @param decisionCode
	 *            the decisionCode to set
	 */

	public void setDecisionCode(String decisionCode) {
		this.decisionCode = decisionCode;
	}

	/**
	 * getter method
	 * 
	 * @return the busiItemId
	 */

	public String getBusiItemId() {
		return busiItemId;
	}

	/**
	 * setter method
	 * 
	 * @param busiItemId
	 *            the busiItemId to set
	 */

	public void setBusiItemId(String busiItemId) {
		this.busiItemId = busiItemId;
	}

	/**
	 * getter method
	 * 
	 * @return the busiDecisionCode
	 */

	public String getBusiDecisionCode() {
		return busiDecisionCode;
	}

	/**
	 * setter method
	 * 
	 * @param busiDecisionCode
	 *            the busiDecisionCode to set
	 */

	public void setBusiDecisionCode(String busiDecisionCode) {
		this.busiDecisionCode = busiDecisionCode;
	}

	/**
	 * getter method
	 * 
	 * @return the emValue
	 */

	public String getEmValue() {
		return emValue;
	}

	/**
	 * setter method
	 * 
	 * @param emValue
	 *            the emValue to set
	 */

	public void setEmValue(String emValue) {
		this.emValue = emValue;
	}

	/**
	 * @Fields policyCode : null
	 */
	private String policyCode;
	/**
	 * @Fields realName : null
	 */
	private String realName;
	/**
	 * @Fields userName : null
	 */
	private String userName;
	/**
	 * @Fields uwDecision : null
	 */
	private String uwDecision;
	/**
	 * @Fields uwFinishTime : null
	 */
	private String uwFinishTime;
	/**
	 * @Fields itemId : null
	 */
	private String itemId;
	/**
	 * @Fields amount : null
	 */
	private String decisionCode;
	/**
	 * @Fields busiItemId : null
	 */
	private String busiItemId;
	/**
	 * @Fields busiDecisionCode : null
	 */
	private String busiDecisionCode;
	/**
	 * @Fields emValue : null
	 */
	private String emValue;

	/**
	 * 
	 * @description 业务ID
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.udmp.framework.model.BaseBO#getBizId()
	 * @return
	 */
	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String toString() {
		return "UwCSDetailedListTeamVO [acceptCode=" + acceptCode
				+ ", csService=" + csService + ", bizDate=" + bizDate
				+ ", uwSubmitTime=" + uwSubmitTime + ", uwSubmitTimeStart="
				+ uwSubmitTimeStart + ", uwSubmitTimeEnd=" + uwSubmitTimeEnd
				+ ", uwFinishTimeStart=" + uwFinishTimeStart
				+ ", uwFinishTimeEnd=" + uwFinishTimeEnd
				+ ", csAcceptTimeStart=" + csAcceptTimeStart
				+ ", csAcceptTimeEnd=" + csAcceptTimeEnd + ", organCode="
				+ organCode + ", organName=" + organName + ", policyCode="
				+ policyCode + ", realName=" + realName + ", userName="
				+ userName + ", uwDecision=" + uwDecision + ", uwFinishTime="
				+ uwFinishTime + ", itemId=" + itemId + ", decisionCode="
				+ decisionCode + ", busiItemId=" + busiItemId
				+ ", busiDecisionCode=" + busiDecisionCode + ", emValue="
				+ emValue + "]";
	}
}
