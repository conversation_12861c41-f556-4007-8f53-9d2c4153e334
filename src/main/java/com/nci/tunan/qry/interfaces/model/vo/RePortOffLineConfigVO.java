package com.nci.tunan.qry.interfaces.model.vo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 离线任务配置信息
 * <AUTHOR> <EMAIL> 
 * @date 2021-3-14 下午10:16:18 
 * @.belongToModule 综合查询-离线任务-配置信息
 */
public class RePortOffLineConfigVO  extends BaseVO {

	private BigDecimal configId;
	private String reportCname;
	private String reportCode;
	private BigDecimal offlineSubmitInterval;
	private BigDecimal offlineQueryScope;
	private BigDecimal onlineQueryScope;
	private String belongSystem;
	private String batchJobId;
	public BigDecimal getConfigId() {
		return configId;
	}
	public void setConfigId(BigDecimal configId) {
		this.configId = configId;
	}
	public String getReportCname() {
		return reportCname;
	}
	public void setReportCname(String reportCname) {
		this.reportCname = reportCname;
	}
	public String getReportCode() {
		return reportCode;
	}
	public void setReportCode(String reportCode) {
		this.reportCode = reportCode;
	}
	public BigDecimal getOfflineSubmitInterval() {
		return offlineSubmitInterval;
	}
	public void setOfflineSubmitInterval(BigDecimal offlineSubmitInterval) {
		this.offlineSubmitInterval = offlineSubmitInterval;
	}
	public BigDecimal getOfflineQueryScope() {
		return offlineQueryScope;
	}
	public void setOfflineQueryScope(BigDecimal offlineQueryScope) {
		this.offlineQueryScope = offlineQueryScope;
	}
	public BigDecimal getOnlineQueryScope() {
		return onlineQueryScope;
	}
	public void setOnlineQueryScope(BigDecimal onlineQueryScope) {
		this.onlineQueryScope = onlineQueryScope;
	}
	public String getBelongSystem() {
		return belongSystem;
	}
	public void setBelongSystem(String belongSystem) {
		this.belongSystem = belongSystem;
	}
		
	public String getBatchJobId() {
		return batchJobId;
	}
	public void setBatchJobId(String batchJobId) {
		this.batchJobId = batchJobId;
	}
	
	@Override
	public String toString() {
		return "RePortOffLineConfigVO [configId=" + configId + ", reportCname="
				+ reportCname + ", reportCode=" + reportCode
				+ ", offlineSubmitInterval=" + offlineSubmitInterval
				+ ", offlineQueryScope=" + offlineQueryScope
				+ ", onlineQueryScope=" + onlineQueryScope + ", belongSystem="
				+ belongSystem + ", batchJobId=" + batchJobId + "]";
	}
	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}
	
}
