package com.nci.tunan.qry.interfaces.model.bo;

import com.nci.udmp.framework.model.*;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Arrays;
import java.util.Date;
import com.nci.udmp.framework.model.BaseVO;

/**
 * @description ClaimAfcTaskVO对象
 * <AUTHOR>
 * @date 2015-07-31 16:49:29
 */
public class ClaimAfcTaskBO extends BaseBO {
	/**
	 * @Fields planId : 质检ID
	 */
	private BigDecimal planId;
	/**
	 * @Fields remark : 备注
	 */
	private String remark;
	/**
	 * @Fields checkConclusion : 质检结论
	 */
	private BigDecimal checkConclusion;
	/**
	 * @Fields checkDate : 质检日期
	 */
	private Date checkDate;
	/**
	 * @Fields taskId : 任务ID
	 */
	private BigDecimal taskId;
	/**
	 * @Fields caseNo : 赔案号
	 */
	private String caseNo;
	/**
	 * @Fields caseId : 赔案ID
	 */
	private BigDecimal caseId;
	/**
	 * @Fields organCode : 质检机构
	 */
	private String organCode;
	/**
	 * @Fields checkBy : 质检人
	 */
	private BigDecimal checkBy;
	/**
	 * @Fields makeDate : 质检任务生成日期
	 */
	private Date makeDate;
	/**
	 * @Fields qcStatus : 质检状态
	 */
	private BigDecimal qcStatus;
	/**
	 * @Fields checkReason : 质检不通过原因
	 */
	private String checkReason;
	
	/**
     * @Fields checkTotal : 质检总数
     */
    private int checkTotal;
    /**
     * @Fields checkNumber : 已质检总数
     */
    private int checkNumber;
    /**
     * @Fields checkNumberNo : 未质检数
     */
    private int checkNumberNo;
    /**
     * @Fields auditorName : 审核人
     */
    private String auditorName;
    /**
     * @Fields approverName : 审批人
     */
    private String approverName;
    /**
     * @Fields checkBy : 质检人姓名
     */
    private String checkName;
    /**
     * 出险人
     */
    private String customerName;
    /**
     * 管理机构
     */
    private String organCodeName;
    /**
     * 结案日期
     */
    private String endCaseTime;
    /**
     * 抽取日期
     */
    private Date extractDate;
    /**
     * String类型的任务ID
     */
    private String taskIdStr;
    
    public String getTaskIdStr() {
		return taskIdStr;
	}

	public void setTaskIdStr(String taskIdStr) {
		this.taskIdStr = taskIdStr;
	}
    public Date getExtractDate() {
		return extractDate;
	}

	public void setExtractDate(Date extractDate) {
		this.extractDate = extractDate;
	}

	public String getEndCaseTime() {
		return endCaseTime;
	}

	public void setEndCaseTime(String endCaseTime) {
		this.endCaseTime = endCaseTime;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getOrganCodeName() {
		return organCodeName;
	}

	public void setOrganCodeName(String organCodeName) {
		this.organCodeName = organCodeName;
	}

	public String getCheckName() {
        return checkName;
    }

    public void setCheckName(String checkName) {
        this.checkName = checkName;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public int getCheckTotal() {
        return checkTotal;
    }

    public void setCheckTotal(int checkTotal) {
        this.checkTotal = checkTotal;
    }

    public int getCheckNumber() {
        return checkNumber;
    }

    public void setCheckNumber(int checkNumber) {
        this.checkNumber = checkNumber;
    }

    public int getCheckNumberNo() {
        return checkNumberNo;
    }

    public void setCheckNumberNo(int checkNumberNo) {
        this.checkNumberNo = checkNumberNo;
    }

    public void setPlanId(BigDecimal planId) {
		this.planId = planId;
	}

	public BigDecimal getPlanId() {
		return planId;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRemark() {
		return remark;
	}

	public void setCheckConclusion(BigDecimal checkConclusion) {
		this.checkConclusion = checkConclusion;
	}

	public BigDecimal getCheckConclusion() {
		return checkConclusion;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setTaskId(BigDecimal taskId) {
		this.taskId = taskId;
	}

	public BigDecimal getTaskId() {
		return taskId;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}

	public BigDecimal getCaseId() {
		return caseId;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setCheckBy(BigDecimal checkBy) {
		this.checkBy = checkBy;
	}

	public BigDecimal getCheckBy() {
		return checkBy;
	}

	public void setMakeDate(Date makeDate) {
		this.makeDate = makeDate;
	}

	public Date getMakeDate() {
		return makeDate;
	}

	public void setQcStatus(BigDecimal qcStatus) {
		this.qcStatus = qcStatus;
	}

	public BigDecimal getQcStatus() {
		return qcStatus;
	}

	public void setCheckReason(String checkReason) {
		this.checkReason = checkReason;
	}

	public String getCheckReason() {
		return checkReason;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "ClaimAfcTaskVO [" + "planId=" + planId + "," + "remark="
				+ remark + "," + "checkConclusion=" + checkConclusion + ","
				+ "checkDate=" + checkDate + "," + "taskId=" + taskId + ","
				+ "caseNo=" + caseNo + "," + "caseId=" + caseId + ","
				+ "organCode=" + organCode + "," + "checkBy=" + checkBy + ","
				+ "makeDate=" + makeDate + "," + "qcStatus=" + qcStatus + ","
				+ "checkReason=" + checkReason + "]";
	}
}
