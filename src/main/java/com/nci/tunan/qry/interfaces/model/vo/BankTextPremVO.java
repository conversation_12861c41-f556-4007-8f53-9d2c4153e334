package com.nci.tunan.qry.interfaces.model.vo;


import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 制返盘数据统计
 * <AUTHOR> <EMAIL>
 * @date 2022-04-27
 * @.belongToModule 保单-制返盘数据统计
 */
public class BankTextPremVO extends BaseVO {

    /**
     * @Fields serialVersionUID : SUID
     */

    private static final long serialVersionUID = -3280369593993885288L;

    /** 
    * @Fields arapFlag :  "应收应付类型 0;非应收/应付 1;应收 2;应付"
    */ 
    private String arapFlag; 

    /** 
    * @Fields discNo :  流水号
    */ 
    private String discNo; 

    /** 
    * @Fields batchNo : 批次号
    */ 
    private String batchNo;

    /**
     * @Fields taskMark : 制返盘进行时，提高效率 收付费操作，无具体业务意义
     */
    private BigDecimal taskMark;

    /**
     * @Fields policyOrganCode : 保单直属机构
     */
    private String policyOrganCode;

    /**
     * @Fields holderName : 投保人姓名
     */
    private String holderName;

    /**
     * @Fields isItemMain : 主组合产品标识
     */
    private String isItemMain;

    /**
     * @Fields bookkeepingId : 记账ID
     */
    private BigDecimal bookkeepingId;

    /**
     * @Fields batchNo : null
     */
    /**
     * @Fields busiProdName : 险种名称
     */
    private String busiProdName;

    /**
     * @Fields payeePhone : 收付费人手机号
     */
    private String payeePhone;

    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;

    /**
     * @Fields paidCount : 缴费次数
     */
    private BigDecimal paidCount;

    /**
     * @Fields feeType : 费用业务类型 32；实付退费 41；保费收入 参考费用类型列表
     */
    private String feeType;

    /**
     * @Fields seqNo : 制盘明细主键
     */
    private BigDecimal seqNo;

    /**
     * @Fields busiProdCode : 险种代码
     */
    private String busiProdCode;

    /**
     * @Fields applyCode : 投保单号码
     */
    private String applyCode;

    /**
     * @Fields feeStatusDate : 收付状态更新时间
     */
    private Date feeStatusDate;

    /**
     * @Fields organCode : 管理机构
     */
    private String organCode;

    /**
     * @Fields redBookkeepingTime : 红冲记录记账时间
     */
    private Date redBookkeepingTime;

    /**
     * @Fields channelType : 个人、银行、团险等
     */
    private String channelType;

    /**
     * @Fields isBankTextDate : 制盘状态更新时间
     */
    private Date isBankTextDate;

    /**
     * @Fields handlerName : 业务员名称
     */
    private String handlerName;

    /**
     * @Fields chargeYear : 缴费年期
     */
    private BigDecimal chargeYear;

    /**
     * @Fields busiProdPackName : 产品组合名称
     */
    private String busiProdPackName;

    /**
     * @Fields isRiskMain : 主险附加险标识
     */
    private String isRiskMain;

    /**
     * @Fields isBankAccount : 帐号是否有效
     */
    private BigDecimal isBankAccount;

    /**
     * @Fields frozenStatus : 冻结状态 0：正常 1：已冻结 2：已挂起
     */
    private String frozenStatus;

    /**
     * @Fields posted : 记账状态
     */
    private String posted;

    /**
     * @Fields bookkeepingFlag : 是否记账 01；未记账 02；已记账 03；不可记账
     */
    private BigDecimal bookkeepingFlag;

    /**
     * @Fields groupId : 制返盘记录表主键
     */
    private BigDecimal groupId;

    /**
     * @Fields redBookkeepingBy : 红冲记账操作人
     */
    private BigDecimal redBookkeepingBy;

    /**
     * @Fields frozenStatusDate : 收付状态更新时间
     */
    private Date frozenStatusDate;

    /**
     * @Fields insuredName : 被保人姓名
     */
    private String insuredName;

    /**
     * @Fields insuredId : 被保人编码
     */
    private BigDecimal insuredId;

    /**
     * @Fields policyType : 个单、团单
     */
    private String policyType;

    /**
     * @Fields productChannel : 产品渠道
     */
    /**
     * @Fields isBankAccountDate : 帐号有效状态更新时间
     */
    private Date isBankAccountDate;

    /**
     * @Fields policyCode : 保单号码
     */
    /**
     * @Fields payMode : 收付方式 10;现金 11;现金送款薄 20;支票 21;现金支票 22;转账支票 30;银行转账
     *         31;银行转账（非制返盘） 32;银行转账（制返盘） 33;网银转账 40;实时收费 41;POS收款 50;内部转账
     *         51;普通内部转账 52;预存内部转账 60;银保通 70;第三方支付 80;客户账户 90;其它
     */
    private String payMode;

    /**
     * @Fields branchCode : 保单所属分公司
     */
    /**
     * @Fields validateDate : 业务生效日期
     */
    private Date validateDate;

    /**
     * @Fields businessType : 业务类型 新契约类型（新单、退单）、保全（保全项）、理赔（受理）
     *         参照总账汇总——FEEOPERATIONTYPE，如理赔业务、贷款清偿。。。
     */
    private String businessType;

    /**
     * @Fields redBelnr : SAP红冲凭证号
     */
    private String redBelnr;

    /**
     * @Fields certiType : 收付款人证件类型
     */
    private String certiType;

    /**
     * @Fields isBankTextBy : 制盘状态更新人
     */
    private BigDecimal isBankTextBy;

    /**
     * @Fields moneyCode : 币种代码 CNY；人民币元
     */
    private String moneyCode;

    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;

    /**
     * @Fields isBankAccountBy : 帐号有效状态更新人
     */
    private BigDecimal isBankAccountBy;

    /**
     * @Fields payeeName : 收付款人姓名
     */
    private String payeeName;

    /**
     * @Fields bankAccount : 银行账号
     */
    /**
     * @Fields redBookkeepingFlag : 红冲标识
     */
    private BigDecimal redBookkeepingFlag;

    /**
     * @Fields finishTime : 业务核销时间
     */
    private Date finishTime;

    /**
     * @Fields dueTime : 应缴应付日
     */
    private Date dueTime;

    /**
     * @Fields certiCode : 收付款人证件号码
     */
    private String certiCode;

    /**
     * @Fields busiApplyDate : 业务申请日
     */
    private Date busiApplyDate;

    /**
     * @Fields belnr : SAP凭证号
     */
    private String belnr;

    /**
     * @Fields feeAmount : 应收付的金额
     */
    /**
     * @Fields listId : 流水号，无业务含义
     */
    private BigDecimal listId;

    /**
     * @Fields holderId : 投保人编码
     */
    private BigDecimal holderId;

    /**
     * @Fields withdrawType : 记账业务类型
     */
    private String withdrawType;

    /**
     * @Fields rollbackUnitNumber : 回退应收付业务流水标识
     */
    private String rollbackUnitNumber;

    /**
     * @Fields failTimes : 制返盘失败次数
     */
    private BigDecimal failTimes;

    /**
     * @Fields customerId : 收付款人 ID
     */
    private BigDecimal customerId;

    /**
     * @Fields policyYear : 年度当前年度
     */
    private BigDecimal policyYear;

    /**
     * @Fields frozenStatusBy : 收付状态更新人
     */
    private BigDecimal frozenStatusBy;

    /**
     * @Fields bankUserName : 户名
     */
    /**
     * @Fields feeStatus : 收付状态 01；待收付 02；已收付 03；回退 04；中止 05；不可收付
     */
    private String feeStatus;

    /**
     * @Fields feeStatusBy : 收付状态更新人
     */
    private BigDecimal feeStatusBy;

    /**
     * @Fields busiProdPackCode : 产品组合代码
     */
    private String busiProdPackCode;

    /**
     * @Fields payEndDate : 宽限期止期
     */
    private Date payEndDate;

    /**
     * @Fields derivType : 业务来源 1;契约 2;保全 3;续保 4;数据迁移 5;其他
     */
    /**
     * @Fields redBookkeepingId : 红冲记账中间表主键ID
     */
    private BigDecimal redBookkeepingId;

    /**
     * @Fields bookkeepingBy : 记账操作人
     */
    private BigDecimal bookkeepingBy;

    /**
     * @Fields handlerCode : 业务号编码
     */
    private String handlerCode;

    /**
     * @Fields refeflag : 参考项
     */
    private String refeflag;

    /**
     * @Fields bookkeepingTime : 记录记账时间
     */
    private Date bookkeepingTime;

    /**
     * @Fields agentCode : 代理人员CODE
     */
    private String agentCode;

    /**
     * @Fields premFreq : 年缴、月缴、趸交等等 1;趸缴 2;月缴 3;季缴 4;半年缴 5;年缴 6;不定期缴 9;其他
     */
    private BigDecimal premFreq;


    /**
     * @Fields diskMode : 资金使用，默认值：1 1：代收付
     */
    private String diskMode;


    /**
     * @Fields uuid : 唯一标识码
     */
    private String uuid;

    /**
     * @Fields sendId : 盘文件ID
     */
    private BigDecimal sendId;


   
    /**
     * @Fields cashDesc : 标记现金流量，是会计入账科目的CODE，可不填
     */
    private String cashDesc;

    /**
     * @Fields dueDate : 应收应付日期
     */
    private Date dueDate;

    /**
     * @Fields zone : 对方账号的所属区域 3201；江苏省
     */
    private String zone;

    /**
     * @Fields lhh : 对方银行的联行号
     */
    private String lhh;


    /**
     * @Fields accoName : 账户所有人姓名
     */
    private String accoName;

    /**
     * @Fields bankTextStatus : 制盘状态 1;待制盘 2;制盘中 3;返盘成功 4;返盘失败
     */
    private String bankTextStatus;

    /**
     * @Fields BRANCH_CODE : 管理机构
     */
    private String branchCode;

    /**
     * /** 是否制盘
     */
    private BigDecimal isBankText;

    /**
     * @Fields backTextTime : 返盘时间
     */
    private Date backTextTime;

    /**
     * @Fields backTextTime : 返盘时间开始时间
     */
    private Date begenTime;

    /**
     * @Fields 返盘结束时间
     */
    private Date endTime;

    /**
     * @Fields productChannel : 产品渠道
     */
    private String productChannel;

    /**
     * @Fields policyCode : 保单号
     */
    private String policyCode;

    /**
     * @Fields derivType : 业务来源
     */
    private String derivType;

    /**
     * @Fields bankAccount :银行账号
     */
    private String bankAccount;

    /**
     * @Fields backTextTime : 制盘时间
     */
    private Date insertTime;

    /**
     * @Fields backTextTime : 制盘开始时间
     */
    private Date insertbegenTime;

    /**
     * @Fields backTextTime : 制盘结束时间
     */
    private Date inserendbegenTime;

    /**
     * @Fields bankUserName : 户名
     */
    private String bankUserName;

    /**
     * @Fields bankUserName : 收付费机构
     */
    private String actualCapOrgId;

    /**
     * @Fields groupNum : 盘文件组号
     */
    private String groupNum;

    /**
     * @Fields bankCode :银行代码
     */
    private String bankCode;

    /**
     * @Fields status:交易结果
     */
    private String status;

    /**
     * @Fields 失败原因
     */
    private String rtnCode;

    /**
     * 银行返回结果
     */
    private String rtnReason;
    /**
     * @Fields feeAmount :金额
     */
    private BigDecimal feeAmount;

    /**
     * @Fields backTextSuccAcc : 返盘成功金额
     */
    private BigDecimal backTextSuccAcc;

    /**
     * @Fields createTime : 报盘文档生成时间
     */
    private Date createTime;

    /**
     * @Fields batchTaskTime : 批任务日期
     */
    private Date batchTaskTime;

    /**
     * @Fields pcrtEnd : 预制盘结束时间
     */
    private Date pcrtEnd;

    /**
     * @Fields mainId : 序列号
     */
    private BigDecimal mainId;

    /**
     * @Fields fileName : 唯一，作为资金系统的来源批号。
     */
    private String fileName;

    /**
     * @Fields sendTextMd : MD5摘要
     */
    private String sendTextMd;

    /**
     * @Fields backTextPath : 返盘文件路径
     */
    private String backTextPath;

    /**
     * @Fields senderId : 报盘人员ID
     */
    private BigDecimal senderId;

    /** 
    * @Fields chksNames : 检查名称
    */ 
    private String chksNames;

    /** 
    * @Fields successrate : 成功率
    */ 
    private String successrate;

    /** 
    * @Fields fatlsucc : 处理条数FATLSUCC
    */ 
    private BigDecimal fatlsucc;

    /** 
    * @Fields bankName : 银行名称
    */ 
    private String bankName;

    /**
     * @Fields textPath : 制盘文件路径
     */
    private String textPath;

    /**
     * @Fields backTextFailAcc : 返盘失败金额
     */
    private BigDecimal backTextFailAcc;

    /**
     * @Fields sendCount : 报盘总记录数
     */
    private BigDecimal sendCount;

    /**
     * @Fields batchTask : 批处理任务配置
     */
    private BigDecimal batchTask;

    /**
     * @Fields batchTaskNum : 批处理任务号
     */
    private BigDecimal batchTaskNum;

    /**
     * @Fields backText : 返盘电子文档
     */
    private String backText;

    /**
     * @Fields backTextFailNum : 返盘失败条数
     */
    private BigDecimal backTextFailNum;

    /**
     * @Fields downloads : 报盘下载次数
     */
    private BigDecimal downloads;

    /**
     * @Fields backerId : 返盘人员ID
     */
    private BigDecimal backerId;

    /**
     * @Fields groupNum : 盘文件组号
     */
    /**
     * @Fields textStatus : 盘文件状态 2 预制盘成功 3 预制盘回退 5 制盘失败 6 制盘成功 7 返盘成功 8 返盘失败 9
     *         制盘下载成功 10 返盘上传成功
     */
    private BigDecimal textStatus;

    /** 
    * @Fields bankTextMainStatus : bank_text表 制盘状态
    */ 
    private String bankTextMainStatus;

    /**
     * @Fields sendAmount : 报盘总金额
     */
    private BigDecimal sendAmount;

    /**
     * @Fields uploadTime : 返盘上载时间
     */
    private Date uploadTime;

    /**
     * @Fields backTextSuccNum : 返盘成功条数
     */
    private BigDecimal backTextSuccNum;

    /**
     * @Fields backFileName : 返盘文件名
     */
    private String backFileName;


    /** 
    * @Fields crtStart : 制盘开始日期
    */ 
    private Date crtStart;

    /** 
    * @Fields crtEnd : 制盘结束日期
    */ 
    private Date crtEnd;

    /** 
    * @Fields backStart : 返盘开始日期
    */ 
    private Date backStart;

    /** 
    * @Fields backEnd : 返盘结束日期
    */ 
    private Date backEnd;

    /** 
    * @Fields loadMode : 加载类型
    */ 
    private String loadMode;

    /** 
    * @Fields organName : 机构名称
    */ 
    private String organName;

    /** 
    * @Fields actualCapOrgName : 实际收付机构
    */ 
    private String actualCapOrgName;

    /** 
    * @Fields bankTextStatusList : 盘状态集合
    */ 
    private List<String> bankTextStatusList;
    
    /** 
    * @Fields channelGroup : 销售渠道组别
    */ 
    private String  channelGroup;
    /** 
    * @Fields isYbtBankTaxt :  银保通制盘标识
    */ 
    private String  isYbtBankTaxt;
    /**
     * @Fields isCorporate :  对公标识
     */
    private String isCorporate;
    

	public String getRtnReason() {
		return rtnReason;
	}

	public void setRtnReason(String rtnReason) {
		this.rtnReason = rtnReason;
	}

	public String getActualCapOrgName() {
        return actualCapOrgName;
    }

    public void setActualCapOrgName(String actualCapOrgName) {
        this.actualCapOrgName = actualCapOrgName;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public void setBankTextStatus(String bankTextStatus) {
        this.bankTextStatus = bankTextStatus;
    }

    public String getBankTextStatus() {
        return bankTextStatus;
    }

    public String getBankTextMainStatus() {
        return bankTextMainStatus;
    }

    public void setBankTextMainStatus(String bankTextMainStatus) {
        this.bankTextMainStatus = bankTextMainStatus;
    }

    public String getStatus() {
        return status;
    }

    public BigDecimal getIsBankText() {
        return isBankText;
    }

    public void setIsBankText(BigDecimal isBankText) {
        this.isBankText = isBankText;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRtnCode() {
        return rtnCode;
    }

    public void setRtnCode(String rtnCode) {
        this.rtnCode = rtnCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public Date getBackTextTime() {
        return backTextTime;
    }

    public void setBackTextTime(Date backTextTime) {
        this.backTextTime = backTextTime;
    }

    public Date getBegenTime() {
        return begenTime;
    }

    public void setBegenTime(Date begenTime) {
        this.begenTime = begenTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Date getInsertbegenTime() {
        return insertbegenTime;
    }

    public void setInsertbegenTime(Date insertbegenTime) {
        this.insertbegenTime = insertbegenTime;
    }

    public Date getInserendbegenTime() {
        return inserendbegenTime;
    }

    public void setInserendbegenTime(Date inserendbegenTime) {
        this.inserendbegenTime = inserendbegenTime;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getActualCapOrgId() {
        return actualCapOrgId;
    }

    public void setActualCapOrgId(String actualCapOrgId) {
        this.actualCapOrgId = actualCapOrgId;
    }

    public String getGroupNum() {
        return groupNum;
    }

    public void setGroupNum(String groupNum) {
        this.groupNum = groupNum;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getDiscNo() {
        return discNo;
    }

    public void setDiscNo(String discNo) {
        this.discNo = discNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Date getCrtStart() {
        return crtStart;
    }

    public void setCrtStart(Date crtStart) {
        this.crtStart = crtStart;
    }

    public Date getCrtEnd() {
        return crtEnd;
    }

    public void setCrtEnd(Date crtEnd) {
        this.crtEnd = crtEnd;
    }

    public Date getBackStart() {
        return backStart;
    }

    public void setBackStart(Date backStart) {
        this.backStart = backStart;
    }

    public Date getBackEnd() {
        return backEnd;
    }

    public void setBackEnd(Date backEnd) {
        this.backEnd = backEnd;
    }

    public BigDecimal getFatlsucc() {
        return fatlsucc;
    }

    public void setFatlsucc(BigDecimal fatlsucc) {
        this.fatlsucc = fatlsucc;
    }

    public String getSuccessrate() {
        return successrate;
    }

    public void setSuccessrate(String successrate) {
        this.successrate = successrate;
    }

    public void setBackTextSuccAcc(BigDecimal backTextSuccAcc) {
        this.backTextSuccAcc = backTextSuccAcc;
    }

    public BigDecimal getBackTextSuccAcc() {
        return backTextSuccAcc;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setBatchTaskTime(Date batchTaskTime) {
        this.batchTaskTime = batchTaskTime;
    }

    public Date getBatchTaskTime() {
        return batchTaskTime;
    }

    public void setPcrtEnd(Date pcrtEnd) {
        this.pcrtEnd = pcrtEnd;
    }

    public Date getPcrtEnd() {
        return pcrtEnd;
    }

    public void setTextPath(String textPath) {
        this.textPath = textPath;
    }

    public String getTextPath() {
        return textPath;
    }

    public void setBackTextFailAcc(BigDecimal backTextFailAcc) {
        this.backTextFailAcc = backTextFailAcc;
    }

    public BigDecimal getBackTextFailAcc() {
        return backTextFailAcc;
    }

    public void setSendCount(BigDecimal sendCount) {
        this.sendCount = sendCount;
    }

    public BigDecimal getSendCount() {
        return sendCount;
    }

    public void setBatchTask(BigDecimal batchTask) {
        this.batchTask = batchTask;
    }

    public BigDecimal getBatchTask() {
        return batchTask;
    }

    public void setBatchTaskNum(BigDecimal batchTaskNum) {
        this.batchTaskNum = batchTaskNum;
    }

    public BigDecimal getBatchTaskNum() {
        return batchTaskNum;
    }

    public void setBackText(String backText) {
        this.backText = backText;
    }

    public String getBackText() {
        return backText;
    }

    public void setBackTextFailNum(BigDecimal backTextFailNum) {
        this.backTextFailNum = backTextFailNum;
    }

    public BigDecimal getBackTextFailNum() {
        return backTextFailNum;
    }

    public void setDownloads(BigDecimal downloads) {
        this.downloads = downloads;
    }

    public BigDecimal getDownloads() {
        return downloads;
    }

    public void setBackerId(BigDecimal backerId) {
        this.backerId = backerId;
    }

    public BigDecimal getBackerId() {
        return backerId;
    }

    public void setTextStatus(BigDecimal textStatus) {
        this.textStatus = textStatus;
    }

    public BigDecimal getTextStatus() {
        return textStatus;
    }

    public void setSendAmount(BigDecimal sendAmount) {
        this.sendAmount = sendAmount;
    }

    public BigDecimal getSendAmount() {
        return sendAmount;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setBackTextSuccNum(BigDecimal backTextSuccNum) {
        this.backTextSuccNum = backTextSuccNum;
    }

    public BigDecimal getBackTextSuccNum() {
        return backTextSuccNum;
    }

    public void setBackFileName(String backFileName) {
        this.backFileName = backFileName;
    }

    public String getBackFileName() {
        return backFileName;
    }

    public void setMainId(BigDecimal mainId) {
        this.mainId = mainId;
    }

    public BigDecimal getMainId() {
        return mainId;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setSendTextMd(String sendTextMd) {
        this.sendTextMd = sendTextMd;
    }

    public String getSendTextMd() {
        return sendTextMd;
    }

    public void setBackTextPath(String backTextPath) {
        this.backTextPath = backTextPath;
    }

    public String getBackTextPath() {
        return backTextPath;
    }

    public void setSenderId(BigDecimal senderId) {
        this.senderId = senderId;
    }

    public BigDecimal getSenderId() {
        return senderId;
    }

    public String getChksNames() {
        return chksNames;
    }

    public void setChksNames(String chksNames) {
        this.chksNames = chksNames;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public void setDiskMode(String diskMode) {
        this.diskMode = diskMode;
    }

    public String getDiskMode() {
        return diskMode;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setSendId(BigDecimal sendId) {
        this.sendId = sendId;
    }

    public BigDecimal getSendId() {
        return sendId;
    }

    public void setCashDesc(String cashDesc) {
        this.cashDesc = cashDesc;
    }

    public String getCashDesc() {
        return cashDesc;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getZone() {
        return zone;
    }

    public void setLhh(String lhh) {
        this.lhh = lhh;
    }

    public String getLhh() {
        return lhh;
    }

    public void setAccoName(String accoName) {
        this.accoName = accoName;
    }

    public String getAccoName() {
        return accoName;
    }

    public void setTaskMark(BigDecimal taskMark) {
        this.taskMark = taskMark;
    }

    public BigDecimal getTaskMark() {
        return taskMark;
    }

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setIsItemMain(String isItemMain) {
        this.isItemMain = isItemMain;
    }

    public String getIsItemMain() {
        return isItemMain;
    }

    public void setBookkeepingId(BigDecimal bookkeepingId) {
        this.bookkeepingId = bookkeepingId;
    }

    public BigDecimal getBookkeepingId() {
        return bookkeepingId;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setPayeePhone(String payeePhone) {
        this.payeePhone = payeePhone;
    }

    public String getPayeePhone() {
        return payeePhone;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setPaidCount(BigDecimal paidCount) {
        this.paidCount = paidCount;
    }

    public BigDecimal getPaidCount() {
        return paidCount;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setSeqNo(BigDecimal seqNo) {
        this.seqNo = seqNo;
    }

    public BigDecimal getSeqNo() {
        return seqNo;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }

    public String getBusiProdCode() {
        return busiProdCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setFeeStatusDate(Date feeStatusDate) {
        this.feeStatusDate = feeStatusDate;
    }

    public Date getFeeStatusDate() {
        return feeStatusDate;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setRedBookkeepingTime(Date redBookkeepingTime) {
        this.redBookkeepingTime = redBookkeepingTime;
    }

    public Date getRedBookkeepingTime() {
        return redBookkeepingTime;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setIsBankTextDate(Date isBankTextDate) {
        this.isBankTextDate = isBankTextDate;
    }

    public Date getIsBankTextDate() {
        return isBankTextDate;
    }

    public void setHandlerName(String handlerName) {
        this.handlerName = handlerName;
    }

    public String getHandlerName() {
        return handlerName;
    }

    public void setChargeYear(BigDecimal chargeYear) {
        this.chargeYear = chargeYear;
    }

    public BigDecimal getChargeYear() {
        return chargeYear;
    }

    public void setBusiProdPackName(String busiProdPackName) {
        this.busiProdPackName = busiProdPackName;
    }

    public String getBusiProdPackName() {
        return busiProdPackName;
    }

    public void setIsRiskMain(String isRiskMain) {
        this.isRiskMain = isRiskMain;
    }

    public String getIsRiskMain() {
        return isRiskMain;
    }

    public void setIsBankAccount(BigDecimal isBankAccount) {
        this.isBankAccount = isBankAccount;
    }

    public BigDecimal getIsBankAccount() {
        return isBankAccount;
    }

    public void setFrozenStatus(String frozenStatus) {
        this.frozenStatus = frozenStatus;
    }

    public String getFrozenStatus() {
        return frozenStatus;
    }

    public void setPosted(String posted) {
        this.posted = posted;
    }

    public String getPosted() {
        return posted;
    }

    public void setBookkeepingFlag(BigDecimal bookkeepingFlag) {
        this.bookkeepingFlag = bookkeepingFlag;
    }

    public BigDecimal getBookkeepingFlag() {
        return bookkeepingFlag;
    }

    public void setGroupId(BigDecimal groupId) {
        this.groupId = groupId;
    }

    public BigDecimal getGroupId() {
        return groupId;
    }

    public void setRedBookkeepingBy(BigDecimal redBookkeepingBy) {
        this.redBookkeepingBy = redBookkeepingBy;
    }

    public BigDecimal getRedBookkeepingBy() {
        return redBookkeepingBy;
    }

    public void setFrozenStatusDate(Date frozenStatusDate) {
        this.frozenStatusDate = frozenStatusDate;
    }

    public Date getFrozenStatusDate() {
        return frozenStatusDate;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredId(BigDecimal insuredId) {
        this.insuredId = insuredId;
    }

    public BigDecimal getInsuredId() {
        return insuredId;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setIsBankAccountDate(Date isBankAccountDate) {
        this.isBankAccountDate = isBankAccountDate;
    }

    public Date getIsBankAccountDate() {
        return isBankAccountDate;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setValidateDate(Date validateDate) {
        this.validateDate = validateDate;
    }

    public Date getValidateDate() {
        return validateDate;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setRedBelnr(String redBelnr) {
        this.redBelnr = redBelnr;
    }

    public String getRedBelnr() {
        return redBelnr;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiType() {
        return certiType;
    }

    public void setIsBankTextBy(BigDecimal isBankTextBy) {
        this.isBankTextBy = isBankTextBy;
    }

    public BigDecimal getIsBankTextBy() {
        return isBankTextBy;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setIsBankAccountBy(BigDecimal isBankAccountBy) {
        this.isBankAccountBy = isBankAccountBy;
    }

    public BigDecimal getIsBankAccountBy() {
        return isBankAccountBy;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setRedBookkeepingFlag(BigDecimal redBookkeepingFlag) {
        this.redBookkeepingFlag = redBookkeepingFlag;
    }

    public BigDecimal getRedBookkeepingFlag() {
        return redBookkeepingFlag;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public Date getDueTime() {
        return dueTime;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setBusiApplyDate(Date busiApplyDate) {
        this.busiApplyDate = busiApplyDate;
    }

    public Date getBusiApplyDate() {
        return busiApplyDate;
    }

    public void setBelnr(String belnr) {
        this.belnr = belnr;
    }

    public String getBelnr() {
        return belnr;
    }

    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

    public BigDecimal getListId() {
        return listId;
    }

    public void setHolderId(BigDecimal holderId) {
        this.holderId = holderId;
    }

    public BigDecimal getHolderId() {
        return holderId;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setRollbackUnitNumber(String rollbackUnitNumber) {
        this.rollbackUnitNumber = rollbackUnitNumber;
    }

    public String getRollbackUnitNumber() {
        return rollbackUnitNumber;
    }

    public void setFailTimes(BigDecimal failTimes) {
        this.failTimes = failTimes;
    }

    public BigDecimal getFailTimes() {
        return failTimes;
    }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public void setPolicyYear(BigDecimal policyYear) {
        this.policyYear = policyYear;
    }

    public BigDecimal getPolicyYear() {
        return policyYear;
    }

    public void setFrozenStatusBy(BigDecimal frozenStatusBy) {
        this.frozenStatusBy = frozenStatusBy;
    }

    public BigDecimal getFrozenStatusBy() {
        return frozenStatusBy;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatusBy(BigDecimal feeStatusBy) {
        this.feeStatusBy = feeStatusBy;
    }

    public BigDecimal getFeeStatusBy() {
        return feeStatusBy;
    }

    public void setBusiProdPackCode(String busiProdPackCode) {
        this.busiProdPackCode = busiProdPackCode;
    }

    public String getBusiProdPackCode() {
        return busiProdPackCode;
    }

    public void setPayEndDate(Date payEndDate) {
        this.payEndDate = payEndDate;
    }

    public Date getPayEndDate() {
        return payEndDate;
    }

    public void setRedBookkeepingId(BigDecimal redBookkeepingId) {
        this.redBookkeepingId = redBookkeepingId;
    }

    public BigDecimal getRedBookkeepingId() {
        return redBookkeepingId;
    }

    public void setBookkeepingBy(BigDecimal bookkeepingBy) {
        this.bookkeepingBy = bookkeepingBy;
    }

    public BigDecimal getBookkeepingBy() {
        return bookkeepingBy;
    }

    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode;
    }

    public String getHandlerCode() {
        return handlerCode;
    }

    public void setRefeflag(String refeflag) {
        this.refeflag = refeflag;
    }

    public String getRefeflag() {
        return refeflag;
    }

    public void setBookkeepingTime(Date bookkeepingTime) {
        this.bookkeepingTime = bookkeepingTime;
    }

    public Date getBookkeepingTime() {
        return bookkeepingTime;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setPremFreq(BigDecimal premFreq) {
        this.premFreq = premFreq;
    }

    public BigDecimal getPremFreq() {
        return premFreq;
    }

    public String getLoadMode() {
        return loadMode;
    }

    public void setLoadMode(String loadMode) {
        this.loadMode = loadMode;
    }

    public List<String> getBankTextStatusList() {
        return bankTextStatusList;
    }

    public void setBankTextStatusList(List<String> bankTextStatusList) {
        this.bankTextStatusList = bankTextStatusList;
    }

    /* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "BankTextPremVO [arapFlag=" + arapFlag + ", discNo=" + discNo
				+ ", batchNo=" + batchNo + ", taskMark=" + taskMark
				+ ", policyOrganCode=" + policyOrganCode + ", holderName="
				+ holderName + ", isItemMain=" + isItemMain
				+ ", bookkeepingId=" + bookkeepingId + ", busiProdName="
				+ busiProdName + ", payeePhone=" + payeePhone + ", unitNumber="
				+ unitNumber + ", paidCount=" + paidCount + ", feeType="
				+ feeType + ", seqNo=" + seqNo + ", busiProdCode="
				+ busiProdCode + ", applyCode=" + applyCode
				+ ", feeStatusDate=" + feeStatusDate + ", organCode="
				+ organCode + ", redBookkeepingTime=" + redBookkeepingTime
				+ ", channelType=" + channelType + ", isBankTextDate="
				+ isBankTextDate + ", handlerName=" + handlerName
				+ ", chargeYear=" + chargeYear + ", busiProdPackName="
				+ busiProdPackName + ", isRiskMain=" + isRiskMain
				+ ", isBankAccount=" + isBankAccount + ", frozenStatus="
				+ frozenStatus + ", posted=" + posted + ", bookkeepingFlag="
				+ bookkeepingFlag + ", groupId=" + groupId
				+ ", redBookkeepingBy=" + redBookkeepingBy
				+ ", frozenStatusDate=" + frozenStatusDate + ", insuredName="
				+ insuredName + ", insuredId=" + insuredId + ", policyType="
				+ policyType + ", isBankAccountDate=" + isBankAccountDate
				+ ", payMode=" + payMode + ", validateDate=" + validateDate
				+ ", businessType=" + businessType + ", redBelnr=" + redBelnr
				+ ", certiType=" + certiType + ", isBankTextBy=" + isBankTextBy
				+ ", moneyCode=" + moneyCode + ", businessCode=" + businessCode
				+ ", isBankAccountBy=" + isBankAccountBy + ", payeeName="
				+ payeeName + ", redBookkeepingFlag=" + redBookkeepingFlag
				+ ", finishTime=" + finishTime + ", dueTime=" + dueTime
				+ ", certiCode=" + certiCode + ", busiApplyDate="
				+ busiApplyDate + ", belnr=" + belnr + ", listId=" + listId
				+ ", holderId=" + holderId + ", withdrawType=" + withdrawType
				+ ", rollbackUnitNumber=" + rollbackUnitNumber + ", failTimes="
				+ failTimes + ", customerId=" + customerId + ", policyYear="
				+ policyYear + ", frozenStatusBy=" + frozenStatusBy
				+ ", feeStatus=" + feeStatus + ", feeStatusBy=" + feeStatusBy
				+ ", busiProdPackCode=" + busiProdPackCode + ", payEndDate="
				+ payEndDate + ", redBookkeepingId=" + redBookkeepingId
				+ ", bookkeepingBy=" + bookkeepingBy + ", handlerCode="
				+ handlerCode + ", refeflag=" + refeflag + ", bookkeepingTime="
				+ bookkeepingTime + ", agentCode=" + agentCode + ", premFreq="
				+ premFreq + ", diskMode=" + diskMode + ", uuid=" + uuid
				+ ", sendId=" + sendId + ", cashDesc=" + cashDesc
				+ ", dueDate=" + dueDate + ", zone=" + zone + ", lhh=" + lhh
				+ ", accoName=" + accoName + ", bankTextStatus="
				+ bankTextStatus + ", branchCode=" + branchCode
				+ ", isBankText=" + isBankText + ", backTextTime="
				+ backTextTime + ", begenTime=" + begenTime + ", endTime="
				+ endTime + ", productChannel=" + productChannel
				+ ", policyCode=" + policyCode + ", derivType=" + derivType
				+ ", bankAccount=" + bankAccount + ", insertTime=" + insertTime
				+ ", insertbegenTime=" + insertbegenTime
				+ ", inserendbegenTime=" + inserendbegenTime
				+ ", bankUserName=" + bankUserName + ", actualCapOrgId="
				+ actualCapOrgId + ", groupNum=" + groupNum + ", bankCode="
				+ bankCode + ", status=" + status + ", rtnCode=" + rtnCode
				+ ", rtnReason=" + rtnReason + ", feeAmount=" + feeAmount
				+ ", backTextSuccAcc=" + backTextSuccAcc + ", createTime="
				+ createTime + ", batchTaskTime=" + batchTaskTime
				+ ", pcrtEnd=" + pcrtEnd + ", mainId=" + mainId + ", fileName="
				+ fileName + ", sendTextMd=" + sendTextMd + ", backTextPath="
				+ backTextPath + ", senderId=" + senderId + ", chksNames="
				+ chksNames + ", successrate=" + successrate + ", fatlsucc="
				+ fatlsucc + ", bankName=" + bankName + ", textPath="
				+ textPath + ", backTextFailAcc=" + backTextFailAcc
				+ ", sendCount=" + sendCount + ", batchTask=" + batchTask
				+ ", batchTaskNum=" + batchTaskNum + ", backText=" + backText
				+ ", backTextFailNum=" + backTextFailNum + ", downloads="
				+ downloads + ", backerId=" + backerId + ", textStatus="
				+ textStatus + ", bankTextMainStatus=" + bankTextMainStatus
				+ ", sendAmount=" + sendAmount + ", uploadTime=" + uploadTime
				+ ", backTextSuccNum=" + backTextSuccNum + ", backFileName="
				+ backFileName + ", crtStart=" + crtStart + ", crtEnd="
				+ crtEnd + ", backStart=" + backStart + ", backEnd=" + backEnd
				+ ", loadMode=" + loadMode + ", organName=" + organName
				+ ", actualCapOrgName=" + actualCapOrgName
				+ ", bankTextStatusList=" + bankTextStatusList
				+ ", channelGroup=" + channelGroup + ", isYbtBankTaxt="
				+ isYbtBankTaxt + ",isCorporate"+isCorporate+"]";
	}

    public String getBizId() {

        return null;
    }

	

	public String getChannelGroup() {
		return channelGroup;
	}

	public void setChannelGroup(String channelGroup) {
		this.channelGroup = channelGroup;
	}

	public String getIsYbtBankTaxt() {
		return isYbtBankTaxt;
	}

	public void setIsYbtBankTaxt(String isYbtBankTaxt) {
		this.isYbtBankTaxt = isYbtBankTaxt;
	}

	public String getIsCorporate() {
		return isCorporate;
	}

	public void setIsCorporate(String isCorporate) {
		this.isCorporate = isCorporate;
	}

}
