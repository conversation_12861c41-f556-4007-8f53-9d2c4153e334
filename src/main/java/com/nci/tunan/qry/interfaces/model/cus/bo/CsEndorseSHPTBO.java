package com.nci.tunan.qry.interfaces.model.cus.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;
/**
 * 医保退保清单
 * @description
 * <AUTHOR> <EMAIL>
 * @date 2020年12月17日 下午2:52:06
 */
public class CsEndorseSHPTBO extends BaseBO {
	
	/**
	 * 管理机构
	 */
	private String organCode;
	/**
	 * 管理机构名称
	 */
	private String organName;
	/**
	 * 统计起期
	 */
	private Date startFinishTime;
	/**
	 * 统计止期
	 */
	private Date endFinishTime;
	/**
	 * 保单编码
	 */
	private String policySequenceNo;
	/**
	 * 保险单号
	 */
	private String policyCode;
	/**
	 * 险种代码
	 */
	private String busiProdCode; 
	/**
	 * 保费（变更后）
	 */
	private BigDecimal totalPremAfAfter;
	/**
	 * 保额
	 */
	private BigDecimal amount;
	/**
	 * 退保类型
	 */
	private String surrenderFlag;
	/**
	 * 退保金额
	 */
	private BigDecimal feeAmount;
	/**
	 * 实退金额
	 */
	private BigDecimal surrenderAmount;
	/**
	 * 是否全额退保
	 */
	private String isTotalPrem;
	/**
	 * 退款方式
	 */
	private String payMode;
	/**
	 * CHARGE_YEAR, --缴费年期    
	 */
	private String chargeYear; 
	/**
	 * 投保日期buy_insurance_date
	 */
	private Date buyInsuranceDate;
	/**
	 * 退保原因
	 */
	private String causeRemark;
	/**
	 * 生效日期
	 */
	private Date validateDate;
	/**
	 * 申请确认日期
	 */
	private Date acceptTime;
	/**
	 * 经办人
	 */
	private String serviceHandler;
	/**
	 * 缴费次数(已交保费次数)
	 */
	private BigDecimal policyPeriod;
	/**
	 * PAY_DUE_DATE, --缴费对应日
	 */
	private Date payDueDate;
	/**
	 * 投保人与业务员关系
	 */
	private String relationToPh;
	/**
	 * 业务员编码
	 */
	private String agentCode;
	/**
	 * 业务员姓名
	 */
	private String agentName;
	/**
	 * 营销服务部
	 */
	private String agentServiceOrgan;
	/**
	 * 营业部
	 */
	private String agentOrgan;
	/**
	 * 业务员渠
	 */
	private String channelType;
	/**
	 * 中介机构代码
	 */
	private String bankBranchCode;
	/**
	 * 中介机构名称
	 */
	private String bankBranchName;
	/**
	 * 网点名称
	 */
	private String bankBranchNetName;
	/**
	 * 网点代码
	 */
	private String serviceBankBranch;
		
	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
    
	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public Date getStartFinishTime() {
		return startFinishTime;
	}

	public void setStartFinishTime(Date startFinishTime) {
		this.startFinishTime = startFinishTime;
	}

	public Date getEndFinishTime() {
		return endFinishTime;
	}

	public void setEndFinishTime(Date endFinishTime) {
		this.endFinishTime = endFinishTime;
	}

	public String getPolicySequenceNo() {
		return policySequenceNo;
	}

	public void setPolicySequenceNo(String policySequenceNo) {
		this.policySequenceNo = policySequenceNo;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public BigDecimal getTotalPremAfAfter() {
		return totalPremAfAfter;
	}

	public void setTotalPremAfAfter(BigDecimal totalPremAfAfter) {
		this.totalPremAfAfter = totalPremAfAfter;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
    
	public String getSurrenderFlag() {
		return surrenderFlag;
	}

	public void setSurrenderFlag(String surrenderFlag) {
		this.surrenderFlag = surrenderFlag;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public BigDecimal getSurrenderAmount() {
		return surrenderAmount;
	}

	public void setSurrenderAmount(BigDecimal surrenderAmount) {
		this.surrenderAmount = surrenderAmount;
	}

	public String getIsTotalPrem() {
		return isTotalPrem;
	}

	public void setIsTotalPrem(String isTotalPrem) {
		this.isTotalPrem = isTotalPrem;
	}

	public String getPayMode() {
		return payMode;
	}

	public void setPayMode(String payMode) {
		this.payMode = payMode;
	}

	public String getChargeYear() {
		return chargeYear;
	}

	public void setChargeYear(String chargeYear) {
		this.chargeYear = chargeYear;
	}

	public Date getBuyInsuranceDate() {
		return buyInsuranceDate;
	}

	public void setBuyInsuranceDate(Date buyInsuranceDate) {
		this.buyInsuranceDate = buyInsuranceDate;
	}

	public String getCauseRemark() {
		return causeRemark;
	}

	public void setCauseRemark(String causeRemark) {
		this.causeRemark = causeRemark;
	}

	public Date getValidateDate() {
		return validateDate;
	}

	public void setValidateDate(Date validateDate) {
		this.validateDate = validateDate;
	}

	public Date getAcceptTime() {
		return acceptTime;
	}

	public void setAcceptTime(Date acceptTime) {
		this.acceptTime = acceptTime;
	}

	public String getServiceHandler() {
		return serviceHandler;
	}

	public void setServiceHandler(String serviceHandler) {
		this.serviceHandler = serviceHandler;
	}

	public BigDecimal getPolicyPeriod() {
		return policyPeriod;
	}

	public void setPolicyPeriod(BigDecimal policyPeriod) {
		this.policyPeriod = policyPeriod;
	}

	public Date getPayDueDate() {
		return payDueDate;
	}

	public void setPayDueDate(Date payDueDate) {
		this.payDueDate = payDueDate;
	}

	public String getRelationToPh() {
		return relationToPh;
	}

	public void setRelationToPh(String relationToPh) {
		this.relationToPh = relationToPh;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getAgentServiceOrgan() {
		return agentServiceOrgan;
	}

	public void setAgentServiceOrgan(String agentServiceOrgan) {
		this.agentServiceOrgan = agentServiceOrgan;
	}

	public String getAgentOrgan() {
		return agentOrgan;
	}

	public void setAgentOrgan(String agentOrgan) {
		this.agentOrgan = agentOrgan;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getBankBranchCode() {
		return bankBranchCode;
	}

	public void setBankBranchCode(String bankBranchCode) {
		this.bankBranchCode = bankBranchCode;
	}

	public String getBankBranchName() {
		return bankBranchName;
	}

	public void setBankBranchName(String bankBranchName) {
		this.bankBranchName = bankBranchName;
	}

	public String getBankBranchNetName() {
		return bankBranchNetName;
	}

	public void setBankBranchNetName(String bankBranchNetName) {
		this.bankBranchNetName = bankBranchNetName;
	}

	public String getServiceBankBranch() {
		return serviceBankBranch;
	}

	public void setServiceBankBranch(String serviceBankBranch) {
		this.serviceBankBranch = serviceBankBranch;
	}

	@Override
	public String getBizId() {
		return null;
	}

}
