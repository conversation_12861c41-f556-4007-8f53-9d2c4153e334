package com.nci.tunan.qry.interfaces.model.cus.bo;



import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;
/**
 * 
 * @description 业务产品详情BO对象
 * <AUTHOR> <EMAIL> 
 * @date 2020-12-31 下午5:40:21 
 * @.belongToModule 综合查询-BO对象
 */
public class CsBusinessDetaillistBO extends BaseBO{
	/**
	 * 绩优等级
	 */
	private String agentLevel;

	/** 
	* 序列id
	*/ 
	private static final long serialVersionUID = 1L;
	/**
	 * 保单管理机构代码查询限定条件
	 */
	private String organCodeSer;
	/**
	 * 电子签名
	 */
	private BigDecimal isElecSign; 
	
	/**
     * 
     * 页面导航标志
     */
    private String flag;
	
    public String getFlag() {
		return flag;
	}
	public void setFlag(String flag) {
		this.flag = flag;
	}
	

	/**
	 * 受理完成日期起期
	 */
	private Date beginAccComDate;
	/**
	 * 受理完成日期止期
	 */
	private Date endAccComDate;
	
	/**
     * 受理完成日期
     */
    private Date  accComTime;
	
	
    public Date getAccComTime() {
		return accComTime;
	}
	public void setAccComTime(Date accComTime) {
		this.accComTime = accComTime;
	}
	
	
    public Date getBeginAccComDate() {
		return beginAccComDate;
	}
	public void setBeginAccComDate(Date beginAccComDate) {
		this.beginAccComDate = beginAccComDate;
	}
	public Date getEndAccComDate() {
		return endAccComDate;
	}
	public void setEndAccComDate(Date endAccComDate) {
		this.endAccComDate = endAccComDate;
	}
	
	
	/**
	 * 销售渠道
	 */
	private String  channelType;
	/**
	 * 销售渠道名称
	 */
	private String  channelTypeName;
	/**
	 * 复核类型
	 */
	private String  reviewType;
	/**
	 * 受理机构代码（受理机构）
	 */
	private String acceptOrganCode;
	/**
	 * 受理机构名称
	 */
	private String acceptOrganName;
	/**
	 * 复核机构代码（管理机构）
	 */
	private String reviewOrganCode;
	/**
	 * 复核机构名称
	 */
	private String reviewOrgName;
	/**
	 * 是否免填单
	 */
	private String nofillFlag;
	/**
	 * 受理渠道
	 */
	private String sourceType;
	/**
	 * 受理渠道名称
	 */
	private String sourceTypeName;
	/**
	 * 人工核保完成日期
	 */
    private Date uwFinishTime;
    /**
     * 是否集中复核标识
     */
    private String focusReview;
    /**
     * 复核人员id
     */
	private BigDecimal reviewId;
	/**
	 * 申请方式
	 */
	private String   serviceType;
	/**
	 * 申请方式名称
	 */
	private String   serviceTypeName;
	/**
	 * 保全项代码（保全项目）
	 */
	private String   serviceCode;
	/**
	 * 保全项目名称
	 */
	private String  serviceCodeName;
	/**
	 * 申请提交日期起期
	 */
	private Date beginApplyDate;
	/**
	 * 申请提交日期止期
	 */
	private Date endApplyDate;
	/**
	 * 复核日期起期
	 */
	private Date beginReviewDate;
	/**
	 * 复核日期止期
	 */
	private Date endReviewDate;
	/**
	 * 单位
	 */
	private String moneyCode;
	/**
	 * 生成清单日期
	 */
	private Date   generationListDate;
	/**
	 * 保全申请号
	 */
	private String applyCode;
	/**
	 * 保全受理号
	 */
	private String acceptCode;
	/**
	 * 所属的主险险种ID（判断是主附险空为主险，用于查询主险代码及主险名称）
	 */
	private BigDecimal masterBusiItemId;
	/**
	 * 险种代码（主险代码）
	 */
    private String  busiProdCode;
    /**
     * 险种名称（主险名称）
     */
    private String busiProdName;
    /**
     * 保单号
     */
    private String policyCode;
    /**
     * 保单管理机构代码
     */
    private String organCode;
    /**
     * 销售机构代码
     */
    private String channelOrgCode;
    /**
     * 保单管理机构名称
     */
	private String organName;
	/**
	 * 销售机构名称
	 */
    private String channelOrgName;
    /**
     * 收付费金额
     */
    private BigDecimal  feeAmount;
    /**
     * 复核结论
     */
    private String  reviewResult;
    /**
     * 复核意见
     */
    private String  reviewView;
    /**
     * 复核修改原因
     */
    private String  backEntyrCause;
    /**
     * 申请确认日期
     */
    private Date    insertTime;
    /**
     * 录入日期
     */
    private Date    updateTime;
    /**
     * 扫描时间
     */
    private Date    scanTime;
    /**
     * 集中共享工作持时间
     */
    private String    jzgxJobTime;
    /**
     * 进入复核工作池时间
     */
    private String    fhJobTime;
    /**
     * 复核日期
     */
    private Date    reviewTime;
    /**
     * 生效日期
     */
    private Date    validateTime;
    /**
     * 申请提交日期
     */
    private Date    applyTime;
    /**
     * 复核人员姓名
     */
    private String  reviewerName;
    /**
     * 全部复核人员ID
     */
    private String  reviewerAll;
    /**
     * 全部复核人员ID集合
     */
    private List  reviewerList;
    /**
     * 是否预受理
     */
    private String preFlag;
    /**
     * 批改状态
     */
	private String  correctState;
	/**
	 * 受理人员信息
	 */
	private String  accepterInfo;
	/**
	 * 录入人员信息
	 */
	private String  inputerInfo;
	/**
	 * 复核人员信息
	 */
	private String  reviewerInfo;
	/**
	 * 受理人员机构代码
	 */
	private String  accepterOrganCode;
	/**
	 * 录入人员机构代码
	 */
	private String  inputerOrganCode;
	/**
	 * 复核人员机构代码
	 */
	private String  reviewerOrganCode;
	/**
	 * 判断复核时点是否有影像件  是 ，否
	 */
	private String reviewImage;
	/**
	 * 录入人员id
	 */
	private BigDecimal insertBy;
	/**
	 * 是否签报
	 */
	private String isSignFlag;
	/**
	 * 指定生效日期
	 */
	private Date preValidateDate;
	/**
	 * 受理折标系数
	 */
	private BigDecimal reviewAcceptRate;
	/**
	 * 录入折标系数
	 */
	private BigDecimal reviewInputRate;
	/**
	 * 折标系数
	 */
	private BigDecimal reviewRate;
	/**
	 * 是否复核修改
	 */
	private String causeFlag;
	/**
	 * rn属性
	 */
    private BigDecimal rn;
    /**
     * 是否紧急件
     */
    private String urgentFlag;
    /**
     * 差错原因
     */
    private String errorFlag;
    /**
     * 是否异地
     */
    private String eslewhereFlag;
    /**
     * 关联万能险保单号
     */
    private String relationPolicyCode;
    /**
     * 关联关系
     */
    private String relationType;
    /**
     * 上收类型
     */
    private String headCollectType;
    /**
     * 保全受理状态
     */
    private String acceptStatusType;
    /**
     * 保全受理状态名称
     */
    private String acceptStatusName;
    /**
     * 签单类型
     */
    private String signType ;
    /**
     * 系统当前用户
     */
    private String userOrgan;
    /**
     * 服务业务员信息
     */
    private String serviceAgentInfo;
    /**
     * 受理业务员信息
     */
    private String acceptAgentInfo;
    /**
     * 受理机构
     */
    private String accepteOrg;
    /**
	 * 多主险标识
	 */
	private String multimainriskflag;
	/**
	 * 客户号
	 */
	private BigDecimal customerId;
	
	/**
	 * 自互保件
	 * @return
	 */
	private String insured;
	
	/**
	 * 同时结算
	 */
	private BigDecimal balanceFlag;
	

	//134673个险新核心“保全业务明细清单”优化
	//modify by cuiqi_wb
	//2023-06-12==========start========================================
	/**
	 * 
	 * 录入完成开始日期
	 */
	private Date beginEnterDate;
	
	/**
	 * 
	 * 录入完成终止日期
	 */
	private Date endEnterDate;
	
	/**
	 * 
	 * 受理生效开始日期
	 */
	private Date beginEffectDate;
	
	/**
	 * 
	 * 受理生效终止日期
	 */
	private Date endEffectDate;
	
	/**
	 * 保全受理生效日期（实际生效日期）
	 * 
	 */
	
	private Date effectTime;
	/**
	 * 终止类型
	 * 
	 */
	private String appType;
	/**
	 * 复核终止原因
	 */
	private String reviewEndCause;	
	
	/**
	 * 是否超时
	 */
	private BigDecimal isOutTime;
	
	/**
	 * 超时原因
	 */
	private String outTimeCause;  
	
	public String getOutTimeCause() {
		return outTimeCause;
	}
	public void setOutTimeCause(String outTimeCause) {
		this.outTimeCause = outTimeCause;
	}
		   
	public BigDecimal getIsOutTime() {
		return isOutTime;
	}
	public void setIsOutTime(BigDecimal isOutTime) {
		this.isOutTime = isOutTime;
	}
		   
	public String getAppType() {
		return appType;
	}
	public void setAppType(String appType) {
		this.appType = appType;
	}
	public String getReviewEndCause() {
		return reviewEndCause;
	}
	public void setReviewEndCause(String reviewEndCause) {
		this.reviewEndCause = reviewEndCause;
	}
	public Date getEffectTime() {
		return effectTime;
	}
	public void setEffectTime(Date effectTime) {
		this.effectTime = effectTime;
	}
	public Date getBeginEffectDate() {
		return beginEffectDate;
	}
	public void setBeginEffectDate(Date beginEffectDate) {
		this.beginEffectDate = beginEffectDate;
	}
	public Date getEndEffectDate() {
		return endEffectDate;
	}
	public void setEndEffectDate(Date endEffectDate) {
		this.endEffectDate = endEffectDate;
	}
	public Date getBeginEnterDate() {
		return beginEnterDate;
	}
	public void setBeginEnterDate(Date beginEnterDate) {
		this.beginEnterDate = beginEnterDate;
	}
	public Date getEndEnterDate() {
		return endEnterDate;
	}
	public void setEndEnterDate(Date endEnterDate) {
		this.endEnterDate = endEnterDate;
	}
	//===============================end================================
	
	//134088保全影像分类需求1
		//modify by cuiqi_wb
		//2023-08-23
		//$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$start$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
		
		/**
		 * 保全作业申请书类单证
		 * 
		 */
		private String csJobApplication;	
		/**
		 * 保全身份证明类单证
		 * 
		 */
		private String csIdentification;
		/**
		 * 保全客户影像类单证
		 * 
		 */
		private String csCustomerImage;
		/**
		 * 病历资料类单证
		 * 
		 */
		private String csMedicalRecords;
		/**
		 * 保全银行账户类单证
		 * 
		 */
		private String csBankAccount;
		/**
		 * 保全关系证明类单证
		 * 
		 */
		private String csProofRelationship;
		/**
		 * 保单/收据/发票类单证
		 * 
		 */
		private String csDocumentProof;
		/**
		 * 保全其他类单证
		 * 
		 */
		private String csOtherDocuments;
		/**
		 * 保全录音类单证
		 * 
		 */
		private String csSoundRecording;
		/**
		 * 保全视频类单证
		 * 
		 */
		private String csVideo;
		/**
		 * 保全收付费方式变更类单证
		 * 
		 */
		private String csPaymentMethod;	
		
	    public String getCsJobApplication() {
			return csJobApplication;
		}
		public void setCsJobApplication(String csJobApplication) {
			this.csJobApplication = csJobApplication;
		}
		public String getCsIdentification() {
			return csIdentification;
		}
		public void setCsIdentification(String csIdentification) {
			this.csIdentification = csIdentification;
		}
		public String getCsCustomerImage() {
			return csCustomerImage;
		}
		public void setCsCustomerImage(String csCustomerImage) {
			this.csCustomerImage = csCustomerImage;
		}
		public String getCsMedicalRecords() {
			return csMedicalRecords;
		}
		public void setCsMedicalRecords(String csMedicalRecords) {
			this.csMedicalRecords = csMedicalRecords;
		}
		public String getCsBankAccount() {
			return csBankAccount;
		}
		public void setCsBankAccount(String csBankAccount) {
			this.csBankAccount = csBankAccount;
		}
		public String getCsProofRelationship() {
			return csProofRelationship;
		}
		public void setCsProofRelationship(String csProofRelationship) {
			this.csProofRelationship = csProofRelationship;
		}
		public String getCsDocumentProof() {
			return csDocumentProof;
		}
		public void setCsDocumentProof(String csDocumentProof) {
			this.csDocumentProof = csDocumentProof;
		}
		public String getCsOtherDocuments() {
			return csOtherDocuments;
		}
		public void setCsOtherDocuments(String csOtherDocuments) {
			this.csOtherDocuments = csOtherDocuments;
		}
		public String getCsSoundRecording() {
			return csSoundRecording;
		}
		public void setCsSoundRecording(String csSoundRecording) {
			this.csSoundRecording = csSoundRecording;
		}
		public String getCsVideo() {
			return csVideo;
		}
		public void setCsVideo(String csVideo) {
			this.csVideo = csVideo;
		}
		public String getCsPaymentMethod() {
			return csPaymentMethod;
		}
		public void setCsPaymentMethod(String csPaymentMethod) {
			this.csPaymentMethod = csPaymentMethod;
		}
		
		//$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$end$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
	
    public BigDecimal getBalanceFlag() {
		return balanceFlag;
	}
	public void setBalanceFlag(BigDecimal balanceFlag) {
		this.balanceFlag = balanceFlag;
	}
	public String getInsured() {
		return insured;
	}
	public void setInsured(String insured) {
		this.insured = insured;
	}
	public BigDecimal getCustomerId() {
		return customerId;
	}
	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}
    public String getAccepteOrg() {
		return accepteOrg;
	}
	public void setAccepteOrg(String accepteOrg) {
		this.accepteOrg = accepteOrg;
	}
    public String getServiceAgentInfo() {
		return serviceAgentInfo;
	}
	public void setServiceAgentInfo(String serviceAgentInfo) {
		this.serviceAgentInfo = serviceAgentInfo;
	}
	public String getAcceptAgentInfo() {
		return acceptAgentInfo;
	}
	public void setAcceptAgentInfo(String acceptAgentInfo) {
		this.acceptAgentInfo = acceptAgentInfo;
	}
    
    public String getSignType() {
		return signType;
	}

	public void setSignType(String signType) {
		this.signType = signType;
	}
    public String getEslewhereFlag() {
		return eslewhereFlag;
	}

	public void setEslewhereFlag(String eslewhereFlag) {
		this.eslewhereFlag = eslewhereFlag;
	}

	public String getChannelTypeName() {
		return channelTypeName;
	}

	public void setChannelTypeName(String channelTypeName) {
		this.channelTypeName = channelTypeName;
	}

	public String getSourceTypeName() {
		return sourceTypeName;
	}

	public void setSourceTypeName(String sourceTypeName) {
		this.sourceTypeName = sourceTypeName;
	}

	public String getServiceTypeName() {
		return serviceTypeName;
	}

	public void setServiceTypeName(String serviceTypeName) {
		this.serviceTypeName = serviceTypeName;
	}
	
	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getReviewType() {
		return reviewType;
	}

	public void setReviewType(String reviewType) {
		this.reviewType = reviewType;
	}

	public String getAcceptOrganCode() {
		return acceptOrganCode;
	}

	public void setAcceptOrganCode(String acceptOrganCode) {
		this.acceptOrganCode = acceptOrganCode;
	}

	public String getAcceptOrganName() {
		return acceptOrganName;
	}

	public void setAcceptOrganName(String acceptOrganName) {
		this.acceptOrganName = acceptOrganName;
	}

	public String getReviewOrganCode() {
		return reviewOrganCode;
	}

	public void setReviewOrganCode(String reviewOrganCode) {
		this.reviewOrganCode = reviewOrganCode;
	}

	public String getReviewOrgName() {
		return reviewOrgName;
	}

	public void setReviewOrgName(String reviewOrgName) {
		this.reviewOrgName = reviewOrgName;
	}

	public String getNofillFlag() {
		return nofillFlag;
	}

	public void setNofillFlag(String nofillFlag) {
		this.nofillFlag = nofillFlag;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	public Date getUwFinishTime() {
		return uwFinishTime;
	}

	public void setUwFinishTime(Date uwFinishTime) {
		this.uwFinishTime = uwFinishTime;
	}

	public String getFocusReview() {
		return focusReview;
	}

	public void setFocusReview(String focusReview) {
		this.focusReview = focusReview;
	}

	public BigDecimal getReviewId() {
		return reviewId;
	}

	public void setReviewId(BigDecimal reviewId) {
		this.reviewId = reviewId;
	}

	public String getServiceType() {
		return serviceType;
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}

	public String getServiceCode() {
		return serviceCode;
	}

	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	public String getServiceCodeName() {
		return serviceCodeName;
	}

	public void setServiceCodeName(String serviceCodeName) {
		this.serviceCodeName = serviceCodeName;
	}

	public Date getBeginApplyDate() {
		return beginApplyDate;
	}

	public void setBeginApplyDate(Date beginApplyDate) {
		this.beginApplyDate = beginApplyDate;
	}

	public Date getEndApplyDate() {
		return endApplyDate;
	}

	public void setEndApplyDate(Date endApplyDate) {
		this.endApplyDate = endApplyDate;
	}

	public Date getBeginReviewDate() {
		return beginReviewDate;
	}

	public void setBeginReviewDate(Date beginReviewDate) {
		this.beginReviewDate = beginReviewDate;
	}

	public Date getEndReviewDate() {
		return endReviewDate;
	}

	public void setEndReviewDate(Date endReviewDate) {
		this.endReviewDate = endReviewDate;
	}

	public String getMoneyCode() {
		return moneyCode;
	}

	public void setMoneyCode(String moneyCode) {
		this.moneyCode = moneyCode;
	}

	public Date getGenerationListDate() {
		return generationListDate;
	}

	public void setGenerationListDate(Date generationListDate) {
		this.generationListDate = generationListDate;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getAcceptCode() {
		return acceptCode;
	}

	public void setAcceptCode(String acceptCode) {
		this.acceptCode = acceptCode;
	}

	public BigDecimal getMasterBusiItemId() {
		return masterBusiItemId;
	}

	public void setMasterBusiItemId(BigDecimal masterBusiItemId) {
		this.masterBusiItemId = masterBusiItemId;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public String getBusiProdName() {
		return busiProdName;
	}

	public void setBusiProdName(String busiProdName) {
		this.busiProdName = busiProdName;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getChannelOrgCode() {
		return channelOrgCode;
	}

	public void setChannelOrgCode(String channelOrgCode) {
		this.channelOrgCode = channelOrgCode;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getChannelOrgName() {
		return channelOrgName;
	}

	public void setChannelOrgName(String channelOrgName) {
		this.channelOrgName = channelOrgName;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public String getReviewResult() {
		return reviewResult;
	}

	public void setReviewResult(String reviewResult) {
		this.reviewResult = reviewResult;
	}

	public String getReviewView() {
		return reviewView;
	}

	public void setReviewView(String reviewView) {
		this.reviewView = reviewView;
	}

	public String getBackEntyrCause() {
		return backEntyrCause;
	}

	public void setBackEntyrCause(String backEntyrCause) {
		this.backEntyrCause = backEntyrCause;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getReviewTime() {
		return reviewTime;
	}

	public void setReviewTime(Date reviewTime) {
		this.reviewTime = reviewTime;
	}

	public Date getValidateTime() {
		return validateTime;
	}

	public void setValidateTime(Date validateTime) {
		this.validateTime = validateTime;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public String getReviewerName() {
		return reviewerName;
	}

	public void setReviewerName(String reviewerName) {
		this.reviewerName = reviewerName;
	}

	public String getPreFlag() {
		return preFlag;
	}

	public void setPreFlag(String preFlag) {
		this.preFlag = preFlag;
	}

	public String getCorrectState() {
		return correctState;
	}

	public void setCorrectState(String correctState) {
		this.correctState = correctState;
	}

	public String getAccepterInfo() {
		return accepterInfo;
	}

	public void setAccepterInfo(String accepterInfo) {
		this.accepterInfo = accepterInfo;
	}

	public String getInputerInfo() {
		return inputerInfo;
	}

	public void setInputerInfo(String inputerInfo) {
		this.inputerInfo = inputerInfo;
	}

	public String getReviewerInfo() {
		return reviewerInfo;
	}

	public void setReviewerInfo(String reviewerInfo) {
		this.reviewerInfo = reviewerInfo;
	}

	public String getAccepterOrganCode() {
		return accepterOrganCode;
	}

	public void setAccepterOrganCode(String accepterOrganCode) {
		this.accepterOrganCode = accepterOrganCode;
	}

	public String getInputerOrganCode() {
		return inputerOrganCode;
	}

	public void setInputerOrganCode(String inputerOrganCode) {
		this.inputerOrganCode = inputerOrganCode;
	}

	public String getReviewerOrganCode() {
		return reviewerOrganCode;
	}

	public void setReviewerOrganCode(String reviewerOrganCode) {
		this.reviewerOrganCode = reviewerOrganCode;
	}

	public String getReviewImage() {
		return reviewImage;
	}

	public void setReviewImage(String reviewImage) {
		this.reviewImage = reviewImage;
	}

	public BigDecimal getInsertBy() {
		return insertBy;
	}

	public void setInsertBy(BigDecimal insertBy) {
		this.insertBy = insertBy;
	}

	public String getIsSignFlag() {
		return isSignFlag;
	}

	public void setIsSignFlag(String isSignFlag) {
		this.isSignFlag = isSignFlag;
	}

	public Date getPreValidateDate() {
		return preValidateDate;
	}

	public void setPreValidateDate(Date preValidateDate) {
		this.preValidateDate = preValidateDate;
	}

	public BigDecimal getReviewAcceptRate() {
		return reviewAcceptRate;
	}

	public void setReviewAcceptRate(BigDecimal reviewAcceptRate) {
		this.reviewAcceptRate = reviewAcceptRate;
	}

	public BigDecimal getReviewInputRate() {
		return reviewInputRate;
	}

	public void setReviewInputRate(BigDecimal reviewInputRate) {
		this.reviewInputRate = reviewInputRate;
	}

	public BigDecimal getReviewRate() {
		return reviewRate;
	}

	public void setReviewRate(BigDecimal reviewRate) {
		this.reviewRate = reviewRate;
	}

	public String getCauseFlag() {
		return causeFlag;
	}

	public void setCauseFlag(String causeFlag) {
		this.causeFlag = causeFlag;
	}

	public BigDecimal getRn() {
		return rn;
	}

	public void setRn(BigDecimal rn) {
		this.rn = rn;
	}

	public String getUrgentFlag() {
		return urgentFlag;
	}

	public void setUrgentFlag(String urgentFlag) {
		this.urgentFlag = urgentFlag;
	}

	public String getErrorFlag() {
		return errorFlag;
	}

	public void setErrorFlag(String errorFlag) {
		this.errorFlag = errorFlag;
	}
	

	public String getRelationPolicyCode() {
        return relationPolicyCode;
    }

    public void setRelationPolicyCode(String relationPolicyCode) {
        this.relationPolicyCode = relationPolicyCode;
    }

    public String getRelationType() {
        return relationType;
    }

    public void setRelationType(String relationType) {
        this.relationType = relationType;
    }

    public BigDecimal getIsElecSign() {
		return isElecSign;
	}

	public void setIsElecSign(BigDecimal isElecSign) {
		this.isElecSign = isElecSign;
	}

	public String getOrganCodeSer() {
		return organCodeSer;
	}

	public void setOrganCodeSer(String organCodeSer) {
		this.organCodeSer = organCodeSer;
	}

	
	
	public Date getScanTime() {
		return scanTime;
	}

	public void setScanTime(Date scanTime) {
		this.scanTime = scanTime;
	}

	public String getJzgxJobTime() {
		return jzgxJobTime;
	}

	public void setJzgxJobTime(String jzgxJobTime) {
		this.jzgxJobTime = jzgxJobTime;
	}

	public String getFhJobTime() {
		return fhJobTime;
	}

	public void setFhJobTime(String fhJobTime) {
		this.fhJobTime = fhJobTime;
	}

	public String getHeadCollectType() {
		return headCollectType;
	}

	public void setHeadCollectType(String headCollectType) {
		this.headCollectType = headCollectType;
	}

	
	public String getAcceptStatusType() {
		return acceptStatusType;
	}

	public void setAcceptStatusType(String acceptStatusType) {
		this.acceptStatusType = acceptStatusType;
	}
	
	


	public String getAcceptStatusName() {
		return acceptStatusName;
	}

	public void setAcceptStatusName(String acceptStatusName) {
		this.acceptStatusName = acceptStatusName;
	}

	public String getReviewerAll() {
		return reviewerAll;
	}
	public void setReviewerAll(String reviewerAll) {
		this.reviewerAll = reviewerAll;
	}
	public List getReviewerList() {
		return reviewerList;
	}
	public void setReviewerList(List reviewerList) {
		this.reviewerList = reviewerList;
	}
	public String getUserOrgan() {
		return userOrgan;
	}

	public void setUserOrgan(String userOrgan) {
		this.userOrgan = userOrgan;
	}
	
	public String getMultimainriskflag() {
		return multimainriskflag;
	}

	public void setMultimainriskflag(String multimainriskflag) {
		this.multimainriskflag = multimainriskflag;
	}
	
	public String getAgentLevel() {
		return agentLevel;
	}
	public void setAgentLevel(String agentLevel) {
		this.agentLevel = agentLevel;
	}
	@Override
	public String getBizId() {
		
		return null;
	}
	@Override
	public String toString() {
		return "CsBusinessDetaillistBO [agentLevel=" + agentLevel + ", organCodeSer=" + organCodeSer + ", isElecSign="
				+ isElecSign + ", channelType=" + channelType + ", channelTypeName=" + channelTypeName + ", reviewType="
				+ reviewType + ", acceptOrganCode=" + acceptOrganCode + ", acceptOrganName=" + acceptOrganName
				+ ", reviewOrganCode=" + reviewOrganCode + ", reviewOrgName=" + reviewOrgName + ", nofillFlag="
				+ nofillFlag + ", sourceType=" + sourceType + ", sourceTypeName=" + sourceTypeName + ", uwFinishTime="
				+ uwFinishTime + ", focusReview=" + focusReview + ", reviewId=" + reviewId + ", serviceType="
				+ serviceType + ", serviceTypeName=" + serviceTypeName + ", serviceCode=" + serviceCode
				+ ", serviceCodeName=" + serviceCodeName + ", beginApplyDate=" + beginApplyDate + ", endApplyDate="
				+ endApplyDate + ", beginReviewDate=" + beginReviewDate + ", endReviewDate=" + endReviewDate
				+ ", moneyCode=" + moneyCode + ", generationListDate=" + generationListDate + ", applyCode=" + applyCode
				+ ", acceptCode=" + acceptCode + ", masterBusiItemId=" + masterBusiItemId + ", busiProdCode="
				+ busiProdCode + ", busiProdName=" + busiProdName + ", policyCode=" + policyCode + ", organCode="
				+ organCode + ", channelOrgCode=" + channelOrgCode + ", organName=" + organName + ", channelOrgName="
				+ channelOrgName + ", feeAmount=" + feeAmount + ", reviewResult=" + reviewResult + ", reviewView="
				+ reviewView + ", backEntyrCause=" + backEntyrCause + ", insertTime=" + insertTime + ", updateTime="
				+ updateTime + ", scanTime=" + scanTime + ", jzgxJobTime=" + jzgxJobTime + ", fhJobTime=" + fhJobTime
				+ ", reviewTime=" + reviewTime + ", validateTime=" + validateTime + ", applyTime=" + applyTime
				+ ", reviewerName=" + reviewerName + ", reviewerAll=" + reviewerAll + ", reviewerList=" + reviewerList
				+ ", preFlag=" + preFlag + ", correctState=" + correctState + ", accepterInfo=" + accepterInfo
				+ ", inputerInfo=" + inputerInfo + ", reviewerInfo=" + reviewerInfo + ", accepterOrganCode="
				+ accepterOrganCode + ", inputerOrganCode=" + inputerOrganCode + ", reviewerOrganCode="
				+ reviewerOrganCode + ", reviewImage=" + reviewImage + ", insertBy=" + insertBy + ", isSignFlag="
				+ isSignFlag + ", preValidateDate=" + preValidateDate + ", reviewAcceptRate=" + reviewAcceptRate
				+ ", reviewInputRate=" + reviewInputRate + ", reviewRate=" + reviewRate + ", causeFlag=" + causeFlag
				+ ", rn=" + rn + ", urgentFlag=" + urgentFlag + ", errorFlag=" + errorFlag + ", eslewhereFlag="
				+ eslewhereFlag + ", relationPolicyCode=" + relationPolicyCode + ", relationType=" + relationType
				+ ", headCollectType=" + headCollectType + ", acceptStatusType=" + acceptStatusType
				+ ", acceptStatusName=" + acceptStatusName + ", signType=" + signType + ", userOrgan=" + userOrgan
				+ ", serviceAgentInfo=" + serviceAgentInfo + ", acceptAgentInfo=" + acceptAgentInfo + ", accepteOrg="
				+ accepteOrg + ", multimainriskflag=" + multimainriskflag + ", customerId=" + customerId + ", insured="
				+ insured + ", balanceFlag=" + balanceFlag + ", beginEnterDate=" + beginEnterDate + ", endEnterDate="
				+ endEnterDate + ", beginEffectDate=" + beginEffectDate + ", endEffectDate=" + endEffectDate
				+ ", effectTime=" + effectTime + ", appType=" + appType + ", reviewEndCause=" + reviewEndCause
				+ ", isOutTime=" + isOutTime + ", outTimeCause=" + outTimeCause + ", csJobApplication="
				+ csJobApplication + ", csIdentification=" + csIdentification + ", csCustomerImage=" + csCustomerImage
				+ ", csMedicalRecords=" + csMedicalRecords + ", csBankAccount=" + csBankAccount
				+ ", csProofRelationship=" + csProofRelationship + ", csDocumentProof=" + csDocumentProof
				+ ", csOtherDocuments=" + csOtherDocuments + ", csSoundRecording=" + csSoundRecording + ", csVideo="
				+ csVideo + ", csPaymentMethod=" + csPaymentMethod + "]";
	}
}
