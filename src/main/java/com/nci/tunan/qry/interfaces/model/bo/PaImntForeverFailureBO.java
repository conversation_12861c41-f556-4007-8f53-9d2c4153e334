package com.nci.tunan.qry.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class PaImntForeverFailureBO extends BaseBO {
	/**
	 * 保单号码
	 */
	private String policyCode;

	/**
	 * 销售机构
	 */
	private String channelOrgCode;

	/**
	 * 投保人
	 */
	private String customerName;
	/**
	 * 第一被保险人
	 */
	private String insuredName;

	/**
	 * 缴费方式
	 */
	private String payNext;

	/**
	 * 交费对应日
	 */
	private Date dueTime;

	/**
	 * 本期保费
	 */
	private BigDecimal totalPremAf;

	/**
	 * 银行卡号
	 */
	private String bankAccount;

	/**
	 * 银行网点
	 */
	private String serviceBankBranch;

	/**
	 * 通讯地址
	 */
	private String address;

	/**
	 * 电话
	 */
	private String tel;

	/**
	 * 失效日期
	 */
	private Date lapseDate;

	/**
	 * 上期保单服务人员工号
	 */
	private String lastAgentCode;
	/**
	 * 上期保单服务人员姓名
	 */
	private String lastAgentName;

	/**
	 * 本期保单服务人员工号
	 */
	private String nowAgentName;
	/**
	 * 本期保单服务人员姓名
	 */
	private String nowAgentCode;
	/**
	 * 保单状态
	 */
	private String statusName;
	/**
	 * 保单状态
	 */
	private String policyType;
	
	/**
	 * @Fields salesOrganCode : 销售机构
	 */
	private String salesOrganCode;
	
	/**
     * @Fields organCode : 销售机构
     */
    private String organCode;
    
    private String originalAgentCode; //原始销售人员工号
   	private String originalAgentName; //原始销售人员姓名
   	private String yinYzu; //营业组
   	private String yinYbu; //营业部
   	private String yinYqu; //营业区
   	private String handler; // 现服务人员部 
   	private String handerCode; //现服务人员工号
   	private Date   serviceDate; // 现服务人员服务起始日期
   	private String salesChannelName;//现服务渠道
   	private String originalChannelTypeName; //原销售渠道
   	
   	  /**
        * @Fields paidCount : 交费次数
        */
       private BigDecimal paidCount;
       
       /**
   	 * 永久失效日期
   	 */
   	private Date perLapseDate;
   	
   	/**
   	 * 险种code
   	 */
   	private String busiProdCode;
   	
   	 /**
        * @Fields busiProdName : 险种名称
        */
       private String busiProdName;
	
       /**
        * 银行代码
        */
       private String bankCode;
       /**
        * 银行网点代码
        */
       private String serviceBankCode;
       /**
        * 特殊保单标记
        */
       private String status;
       
   	public String getStatus() {
   		return status;
   	}

   	public void setStatus(String status) {
   		this.status = status;
   	}
	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getChannelOrgCode() {
		return channelOrgCode;
	}

	public void setChannelOrgCode(String channelOrgCode) {
		this.channelOrgCode = channelOrgCode;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getPayNext() {
		return payNext;
	}

	public void setPayNext(String payNext) {
		this.payNext = payNext;
	}

	public Date getDueTime() {
		return dueTime;
	}

	public void setDueTime(Date dueTime) {
		this.dueTime = dueTime;
	}

	public BigDecimal getTotalPremAf() {
		return totalPremAf;
	}

	public void setTotalPremAf(BigDecimal totalPremAf) {
		this.totalPremAf = totalPremAf;
	}

	public String getBankAccount() {
		return bankAccount;
	}

	public void setBankAccount(String bankAccount) {
		this.bankAccount = bankAccount;
	}

	public String getServiceBankBranch() {
		return serviceBankBranch;
	}

	public void setServiceBankBranch(String serviceBankBranch) {
		this.serviceBankBranch = serviceBankBranch;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public Date getLapseDate() {
		return lapseDate;
	}

	public void setLapseDate(Date lapseDate) {
		this.lapseDate = lapseDate;
	}

	

	public String getLastAgentName() {
		return lastAgentName;
	}

	public void setLastAgentName(String lastAgentName) {
		this.lastAgentName = lastAgentName;
	}

	public String getLastAgentCode() {
		return lastAgentCode;
	}

	public void setLastAgentCode(String lastAgentCode) {
		this.lastAgentCode = lastAgentCode;
	}

	public String getNowAgentName() {
		return nowAgentName;
	}

	public void setNowAgentName(String nowAgentName) {
		this.nowAgentName = nowAgentName;
	}

	public String getNowAgentCode() {
		return nowAgentCode;
	}

	public void setNowAgentCode(String nowAgentCode) {
		this.nowAgentCode = nowAgentCode;
	}

	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}
	
	

	public String getSalesOrganCode() {
		return salesOrganCode;
	}

	public void setSalesOrganCode(String salesOrganCode) {
		this.salesOrganCode = salesOrganCode;
	}

	public String getPolicyType() {
		return policyType;
	}

	public void setPolicyType(String policyType) {
		this.policyType = policyType;
	}
	
	public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    @Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

	public String getOriginalAgentCode() {
		return originalAgentCode;
	}

	public void setOriginalAgentCode(String originalAgentCode) {
		this.originalAgentCode = originalAgentCode;
	}

	public String getOriginalAgentName() {
		return originalAgentName;
	}

	public void setOriginalAgentName(String originalAgentName) {
		this.originalAgentName = originalAgentName;
	}

	public String getYinYzu() {
		return yinYzu;
	}

	public void setYinYzu(String yinYzu) {
		this.yinYzu = yinYzu;
	}

	public String getYinYbu() {
		return yinYbu;
	}

	public void setYinYbu(String yinYbu) {
		this.yinYbu = yinYbu;
	}

	public String getYinYqu() {
		return yinYqu;
	}

	public void setYinYqu(String yinYqu) {
		this.yinYqu = yinYqu;
	}

	public String getHandler() {
		return handler;
	}

	public void setHandler(String handler) {
		this.handler = handler;
	}

	public String getHanderCode() {
		return handerCode;
	}

	public void setHanderCode(String handerCode) {
		this.handerCode = handerCode;
	}

	public Date getServiceDate() {
		return serviceDate;
	}

	public void setServiceDate(Date serviceDate) {
		this.serviceDate = serviceDate;
	}

	public String getSalesChannelName() {
		return salesChannelName;
	}

	public void setSalesChannelName(String salesChannelName) {
		this.salesChannelName = salesChannelName;
	}

	public String getOriginalChannelTypeName() {
		return originalChannelTypeName;
	}

	public void setOriginalChannelTypeName(String originalChannelTypeName) {
		this.originalChannelTypeName = originalChannelTypeName;
	}

	public BigDecimal getPaidCount() {
		return paidCount;
	}

	public void setPaidCount(BigDecimal paidCount) {
		this.paidCount = paidCount;
	}

	public Date getPerLapseDate() {
		return perLapseDate;
	}

	public void setPerLapseDate(Date perLapseDate) {
		this.perLapseDate = perLapseDate;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public String getBusiProdName() {
		return busiProdName;
	}

	public void setBusiProdName(String busiProdName) {
		this.busiProdName = busiProdName;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getServiceBankCode() {
		return serviceBankCode;
	}

	public void setServiceBankCode(String serviceBankCode) {
		this.serviceBankCode = serviceBankCode;
	}

	public String getInsuredName() {
		return insuredName;
	}

	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}
	
}
