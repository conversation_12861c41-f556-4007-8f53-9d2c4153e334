package com.nci.tunan.qry.interfaces.model.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

public class QryPayPlanDuePO extends BasePO {
    // planId;
    public BigDecimal getPlanId() {
        return getBigDecimal("PLAN_ID");
    }
    
    public void setPlanId(BigDecimal planId) {
        setBigDecimal("PLAN_ID", planId);
    }
    public String getSurvivalMode() {
        return getString("survival_mode");
    }
    public void setSurvivalMode(String survivalMode) {
        setString("survival_mode", survivalMode);;
    }
    public BigDecimal getSurvivalWMode() {
		return getBigDecimal("survival_w_mode");
	}

	public void setSurvivalWMode(BigDecimal survivalWMode) {
		setBigDecimal("survival_w_mode",survivalWMode);
	}

	public BigDecimal getSurvivalMMode() {
		return getBigDecimal("survival_m_mode");
	}

	public void setSurvivalMMode(BigDecimal survivalMMode) {
		setBigDecimal("survival_m_mode",survivalMMode);
	}
      
    public BigDecimal getBusiItemId() {
        return getBigDecimal("BUSI_ITEM_ID");
    }
    
    
    public void setBusiItemId(BigDecimal busiItemId) {
        setBigDecimal("BUSI_ITEM_ID", busiItemId);
    }
    public BigDecimal getPolicyId() {
        return getBigDecimal("POLICY_ID");
    }
    
    
    public void setPolicyId(BigDecimal policyId) {
        setBigDecimal("POLICY_ID", policyId);
    }

    public String getPolicyCode() {
        return getString("POLICY_CODE");
    }

    public void setPolicyCode(String policyCode) {
        setString("POLICY_CODE", policyCode);
    }

    public String getBusiProdCode() {
        return getString("BUSI_PROD_CODE");
    }

    public void setBusiProdCode(String busiProdCode) {
        setString("BUSI_PROD_CODE", busiProdCode);
    }

    public String getBusiProdName() {
		return getString("busi_prod_name");
	}

	public void setBusiProdName(String busiProdName) {
		setString("busi_prod_name",busiProdName);
	}
    public String getProductCode() {
        return getString("PRODUCT_CODE");
    }

    public void setProductCode(String productCode) {
        setString("PRODUCT_CODE", productCode);
    }

    
    public Date getBeginDate() {
        return getUtilDate("BEGIN_DATE");
    }

    public void setBeginDate(Date beginDate) {
        setUtilDate("BEGIN_DATE", beginDate);
    }

    public Date getEndDate() {
        return getUtilDate("END_DATE");
    }

    public void setEndDate(Date endDate) {
        setUtilDate("END_DATE", endDate);
    }

    public BigDecimal getPayStatus() {
        return getBigDecimal("PAY_STATUS");
    }

    public void setPayStatus(BigDecimal payStatus) {
        setBigDecimal("PAY_STATUS", payStatus);
    }
    public BigDecimal getPayNum() {
        return getBigDecimal("pay_num");
    }

    public void setPayNum(BigDecimal payNum) {
        setBigDecimal("pay_num", payNum);
    }

    public String getPlanFreq() {
        return getString("PLAN_FREQ");
    }

    public void setPlanFreq(String planFreq) {
        setString("PLAN_FREQ", planFreq);
    }

    public BigDecimal getInstalmentAmount() {
        return getBigDecimal("INSTALMENT_AMOUNT");
    }

    public void setInstalmentAmount(BigDecimal instalmentAmount) {
        setBigDecimal("INSTALMENT_AMOUNT", instalmentAmount);
    }

    public BigDecimal getTotalAmount() {
        return getBigDecimal("TOTAL_AMOUNT");
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        setBigDecimal("TOTAL_AMOUNT", totalAmount);
    }
    
    public BigDecimal getPayYear() {
        return getBigDecimal("PAY_YEAR");
    }

    public void setPayYear(BigDecimal payYear) {
        setBigDecimal("PAY_YEAR", payYear);
    }

    public BigDecimal getFeeAmount() {
        return getBigDecimal("FEE_AMOUNT");
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        setBigDecimal("FEE_AMOUNT", feeAmount);
    }

    public String getFeeStatus() {
        return getString("FEE_STATUS");
    }

    public void setFeeStatus(String feeStatus) {
        setString("FEE_STATUS", feeStatus);
    }

    public Date getConfirmDate() {
        return getUtilDate("CONFIRM_DATE");
    }

    public void setConfirmDate(Date confirmDate) {
        setUtilDate("CONFIRM_DATE", confirmDate);
    }

    
  //生调标识
    public void setsurvivalInvestFlag(BigDecimal survivalInvestFlag){
        setBigDecimal("survival_invest_flag",survivalInvestFlag);
    }
    public BigDecimal getsurvivalInvestFlag(){
        return getBigDecimal("survival_invest_flag");
    }
    public Date getValidateDate() {
        return getUtilDate("validate_date");
    }
    
    public void setValidateDate(Date validateDate) {
        setUtilDate("validate_date", validateDate);
    }
    
    public String getFundsRtnCode() {
        return getString("FUNDS_RTN_CODE");
    }

    public void setFundsRtnCode(String fundsRtnCode) {
        setString("FUNDS_RTN_CODE", fundsRtnCode);
    } 
    public String getBankCode() {
        return getString("BANK_CODE");
    }

    public void setBankCode(String bankCode) {
        setString("BANK_CODE", bankCode);
    } 
    //银行账户
    public String getBankAccount() {
        return getString("BANK_ACCOUNT");
    }
   
    public void setBankAccount(String bankAccount) {
        setString("BANK_ACCOUNT", bankAccount);
    } 
    //领取形式
    public String getReceiveFormally() {
        return getString("RECEIVE_FORMALLY");
    }

    public void setReceiveFormally(String receiveFormally) {
        setString("RECEIVE_FORMALLY", receiveFormally);
    }
    //领取年龄
    public BigDecimal getCustomerBirtday() {
        return getBigDecimal("CUSTOMER_BIRTHDAY");
    }

    public void setCustomerBirtday(BigDecimal customerBirtday) {
    	setBigDecimal("CUSTOMER_BIRTHDAY", customerBirtday);
    } 
    
    //开户银行
    
    public String getIssueBankName() {
        return getString("ISSUE_BANK_NAME");
    }

    public void setIssueBankName(String issueBankName) {
        setString("ISSUE_BANK_NAME", issueBankName);
    } 
    //户名
    public String getAccoName() {
        return getString("ACCO_NAME");
    }

    public void setAccoName(String accoName) {
        setString("ACCO_NAME", accoName);
    } 
    
    //领取通知书号
    public String getDocumentNo() {
        return getString("DOCUMENT_NO");
    }

    public void setDocumentNo(String documentNo) {
        setString("DOCUMENT_NO", documentNo);
    } 
   
    public String getBankUserName() {
        return getString("BANK_USER_NAME");
    }
    
    public void setBankUserName(String bankUserName) {
        setString("BANK_USER_NAME", bankUserName);
    } 
    
    
    public BigDecimal getLiabId() {
        return getBigDecimal("LIAB_ID");
    }

    public void setLiabId(BigDecimal liabId) {
        setBigDecimal("LIAB_ID", liabId);
    }
    public String getLiabName() {
        return getString("LIAB_NAME");
    }

    public void setLiabName(String liabName) {
        setString("LIAB_NAME", liabName);
    }
    public String getUnitNumber() {
        return getString("UNIT_NUMBER");
    }

    public void setUnitNumber(String unitNumber) {
        setString("UNIT_NUMBER", unitNumber);
    }
    public Date getRealPayDate() {
        return getUtilDate("REAL_PAY_DATE");
    }
    
    public void setRealPayDate(Date realPayDate) {
        setUtilDate("REAL_PAY_DATE", realPayDate);
    }
    public String getModeName() {
        return getString("mode_name");
    }

    public void setModeName(String modeName) {
        setString("mode_name", modeName);
    }

    public void setProductName(String productName) {
        setString("product_name", productName);
    }
    public String getProductName() {
        return getString("product_name");
    }

    public void setProductNameSys(String productNameSys) {
        setString("product_name_sys", productNameSys);
    }
    public String getProductNameSys() {
        return getString("product_name_sys");
    }
    public Date getFinishTime() {
        return getUtilDate("finish_time");
    }
    
    public void setFinishTime(Date finishTime) {
        setUtilDate("finish_time", finishTime);
    }
   
    public Date getPayDueDate() {
        return getUtilDate("pay_due_date");
    }
    
    public void setPayDueDate(Date payDueDate) {
        setUtilDate("pay_due_date", payDueDate);
    }
    
    
    public Date getBookKeepingTime(){
        return getUtilDate("bookkeeping_time");
    }
    public void setBookKeepingTime(Date bookKeepingTime){
        setUtilDate("bookkeeping_time",bookKeepingTime);
    }
    
    public String getPayRefNo() {
        return getString("payrefno");
    }

    public void setPayRefNo(String payRefNo) {
        setString("payrefno", payRefNo);
    }
    public BigDecimal getObtainWaitMon() {
        return getBigDecimal("obtainwaitmon");
    }

    public void setObtainWaitMon(BigDecimal obtainWaitMon) {
        setBigDecimal("obtainwaitmon", obtainWaitMon); 
    }
    
    
    public BigDecimal getDeduAmount() {
        return getBigDecimal("DEDU_AMOUNT");
    }

    public void setDeduAmount(BigDecimal deduAmount) {
        setBigDecimal("DEDU_AMOUNT", deduAmount);
    }
    public void setDeduAcceptCode(String deduAcceptCode) {
        setString("DEDU_ACCEPT_CODE", deduAcceptCode);
    }
    public String getDeduAcceptCode() {
        return getString("DEDU_ACCEPT_CODE");
    }
    
}
