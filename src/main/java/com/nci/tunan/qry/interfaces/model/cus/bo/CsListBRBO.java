package com.nci.tunan.qry.interfaces.model.cus.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;
/**
 * 银行划款清单BO
 * @description 
 */
public class CsListBRBO extends BaseBO {
    /** 
     * @Fields manageName : 保单管理机构名字 
     */ 
    private String manageName;
    /**
	 * @Fields 费用状态 （这里用作划款当前结果）
	 */
    private String feeStatus;
    /**
     * @Fields 划款次数
     */
    private Date backTextTime;
    /**
     * @Fields 划款次数
     */
    private BigDecimal failTimes;
    private String busiItemId;
	private Date validateStartDate;
	private Date validateEndDate;
	private Date finishStartTime;
	private Date finishEndTime;
	private String menteringerInfo;
	private String agentName;
	
	public Date getBackTextTime() {
		return backTextTime;
	}

	public void setBackTextTime(Date backTextTime) {
		this.backTextTime = backTextTime;
	}

	public String getFeeStatus() {
		return feeStatus;
	}

	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}

	public BigDecimal getFailTimes() {
		return failTimes;
	}

	public void setFailTimes(BigDecimal failTimes) {
		this.failTimes = failTimes;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getMenteringerInfo() {
		return menteringerInfo;
	}

	public void setMenteringerInfo(String menteringerInfo) {
		this.menteringerInfo = menteringerInfo;
	}
	private String organCode;
	
	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public Date getValidateStartDate() {
		return validateStartDate;
	}

	public void setValidateStartDate(Date validateStartDate) {
		this.validateStartDate = validateStartDate;
	}

	public Date getValidateEndDate() {
		return validateEndDate;
	}

	public void setValidateEndDate(Date validateEndDate) {
		this.validateEndDate = validateEndDate;
	}

	public Date getFinishStartTime() {
		return finishStartTime;
	}

	public void setFinishStartTime(Date finishStartTime) {
		this.finishStartTime = finishStartTime;
	}

	public Date getFinishEndTime() {
		return finishEndTime;
	}

	public void setFinishEndTime(Date finishEndTime) {
		this.finishEndTime = finishEndTime;
	}

	public String getBusiItemId() {
		return busiItemId;
	}

	public void setBusiItemId(String busiItemId) {
		this.busiItemId = busiItemId;
	}

	public String getManageName() {
		return manageName;
	}

	public void setManageName(String manageName) {
		this.manageName = manageName;
	}

	public String getChannelOrgName() {
		return channelOrgName;
	}

	public void setChannelOrgName(String channelOrgName) {
		this.channelOrgName = channelOrgName;
	}
	/** 
    * @Fields channelOrgName : 保单销售机构名字 
    */ 
    private String channelOrgName;
	/**
	 * 受理机构
	 */
	private String acceptOrgCode;
	/**
	 * 保单管理机构
	 */
	private String orgCode;
	/**
	 * 保单销售机构代码
	 */
	private String channelOrgCode;
	/**
	 * @Fields channelType : 渠道（销售渠道）
	 */
	private String channelType;
	/**
	 * @Fields sourceType : 受理渠道代码
	 */
	private String sourceType;

	/**
	 * 申请ID
	 */
	private BigDecimal changeId;
	/**
	 * 申请号
	 */
	private String applyCode;
	/**
	 * 受理ID
	 */
	private BigDecimal acceptId;
	/**
	 * 受理号
	 */
	private String acceptCode;
	/**
	 * 保全项目
	 */
	private String serviceName;
	/**
	 * 保全代码
	 */
	private String serviceCode;
	/**
	 * 申请方式
	 */
	private String serviceType;
	/**
	 * 保单ID
	 */
	private BigDecimal policyId;
	/**
	 * 保单变更ID
	 */
	private BigDecimal policyChgId;
	/**
	 * 保单号
	 */
	private String policyCode;
	/**
	 * 申请提交日期
	 */
	private Date applyTime;
	/**
	 * 申请确认日期
	 */
	private Date acceptDate;
	/**
	 * 复核日期
	 */
	private Date reviewTime;

	/**
	 * 险种代码
	 */
	private String busiProdCode;
	/**
	 * 受理人员信息
	 */
	private String accepterInfo;
	/**
	 * 录入人员信息
	 */
	private String enteringerInfo;
	/**
	 * 复核人员信息
	 */
	private String checkerInfo;
	/**
	 * 新契约业务员信息
	 */
	private String nbSalesmanInfo;
	/**
	 * 服务业务员信息
	 */
	private String serviceSalesmanInfo;

	/**
	 * 复核人信息    复核人ID
	 */
	private BigDecimal reviewId;
	/**
	 * 银行编码    银行编码   
	 */
	private String bankCode;
	/**
	 * 银行账号  银行账号   
	 */
	private String bankAccount;
	/**
	 * 户名    银行编码   
	 */
	private String bankUserName;
	/**
	 * 收付费标识     收付费标识 
	 */
	private String arapFlag;
	/**
	 * 应收付金额    应收付金额 
	 */
	private String feeAmount;
	/**
	 * 保单号 或客户号
	 */
	private  String policyOrCustomerCode;
	/**
	 * 受理人ID
	 */
	
	private BigDecimal acceptHolder;
	
	/**
	 * 投保人编码
	 */
	private BigDecimal holderId;
	/**
	 * 被保人编码
	 */
	private BigDecimal insuredId;
	/**
	 * 投保人移动电话
	 */
	private String  holderMobileTel;
	/**
	 *  投保人移动电话
	 */
	private String  holderHouseTel;
	/**
	 * 被保人移动电话
	 */
	private String  insureMobileTel;
	/**
	 * 被保人移动电话
	 */
	private String  insureHouseTel;
	/**
	 * 受理变更号 
	 */
	private String businessCode;
	private Date bankDealDate;
	private String fundsRtnCode;
	private String bankTextStatus;
	/**
	 * 多主险标识
	 */
	private String multimainriskflag;

	public String getMultimainriskflag() {
		return multimainriskflag;
	}

	public void setMultimainriskflag(String multimainriskflag) {
		this.multimainriskflag = multimainriskflag;
	}
	
	public String getBankTextStatus() {
		return bankTextStatus;
	}

	public void setBankTextStatus(String bankTextStatus) {
		this.bankTextStatus = bankTextStatus;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public String getFundsRtnCode() {
		return fundsRtnCode;
	}

	public void setFundsRtnCode(String fundsRtnCode) {
		this.fundsRtnCode = fundsRtnCode;
	}

	public Date getBankDealDate() {
		return bankDealDate;
	}

	public void setBankDealDate(Date bankDealDate) {
		this.bankDealDate = bankDealDate;
	}
	private String feeFlag;
	public String getFeeFlag() {
		return feeFlag;
	}

	public void setFeeFlag(String feeFlag) {
		this.feeFlag = feeFlag;
	}
	private String bankName;
	private String payrefno;
		


	public String getPayrefno() {
		return payrefno;
	}

	public void setPayrefno(String payrefno) {
		this.payrefno = payrefno;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	@Override
	public String getBizId() {
		return null;
	}
	
	public String getAcceptOrgCode() {
		return acceptOrgCode;
	}

	public void setAcceptOrgCode(String acceptOrgCode) {
		this.acceptOrgCode = acceptOrgCode;
	}


	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getChannelOrgCode() {
		return channelOrgCode;
	}

	public void setChannelOrgCode(String channelOrgCode) {
		this.channelOrgCode = channelOrgCode;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public BigDecimal getChangeId() {
		return changeId;
	}

	public void setChangeId(BigDecimal changeId) {
		this.changeId = changeId;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public BigDecimal getAcceptId() {
		return acceptId;
	}

	public void setAcceptId(BigDecimal acceptId) {
		this.acceptId = acceptId;
	}

	public String getAcceptCode() {
		return acceptCode;
	}

	public void setAcceptCode(String acceptCode) {
		this.acceptCode = acceptCode;
	}

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public String getServiceCode() {
		return serviceCode;
	}

	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	public String getServiceType() {
		return serviceType;
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public BigDecimal getPolicyChgId() {
		return policyChgId;
	}

	public void setPolicyChgId(BigDecimal policyChgId) {
		this.policyChgId = policyChgId;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public Date getAcceptDate() {
		return acceptDate;
	}

	public void setAcceptDate(Date acceptDate) {
		this.acceptDate = acceptDate;
	}

	public Date getReviewTime() {
		return reviewTime;
	}

	public void setReviewTime(Date reviewTime) {
		this.reviewTime = reviewTime;
	}

	public String getAccepterInfo() {
		return accepterInfo;
	}

	public void setAccepterInfo(String accepterInfo) {
		this.accepterInfo = accepterInfo;
	}


	public String getEnteringerInfo() {
		return enteringerInfo;
	}

	public void setEnteringerInfo(String enteringerInfo) {
		this.enteringerInfo = enteringerInfo;
	}

	public String getCheckerInfo() {
		return checkerInfo;
	}

	public void setCheckerInfo(String checkerInfo) {
		this.checkerInfo = checkerInfo;
	}

	public String getNbSalesmanInfo() {
		return nbSalesmanInfo;
	}

	public void setNbSalesmanInfo(String nbSalesmanInfo) {
		this.nbSalesmanInfo = nbSalesmanInfo;
	}

	public String getServiceSalesmanInfo() {
		return serviceSalesmanInfo;
	}

	public void setServiceSalesmanInfo(String serviceSalesmanInfo) {
		this.serviceSalesmanInfo = serviceSalesmanInfo;
	}



	public BigDecimal getReviewId() {
		return reviewId;
	}

	public void setReviewId(BigDecimal reviewId) {
		this.reviewId = reviewId;
	}
	
	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankAccount() {
		return bankAccount;
	}

	public void setBankAccount(String bankAccount) {
		this.bankAccount = bankAccount;
	}

	public String getBankUserName() {
		return bankUserName;
	}

	public void setBankUserName(String bankUserName) {
		this.bankUserName = bankUserName;
	}
	
	public String getArapFlag() {
		return arapFlag;
	}

	public void setArapFlag(String arapFlag) {
		this.arapFlag = arapFlag;
	}

	public String getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(String feeAmount) {
		this.feeAmount = feeAmount;
	}

	public BigDecimal getAcceptHolder() {
		return acceptHolder;
	}

	public void setAcceptHolder(BigDecimal acceptHolder) {
		this.acceptHolder = acceptHolder;
	}

	public String getPolicyOrCustomerCode() {
		return policyOrCustomerCode;
	}
	public void setPolicyOrCustomerCode(String policyOrCustomerCode) {
		this.policyOrCustomerCode = policyOrCustomerCode;
	}
	
	public BigDecimal getHolderId() {
		return holderId;
	}

	public void setHolderId(BigDecimal holderId) {
		this.holderId = holderId;
	}

	public BigDecimal getInsuredId() {
		return insuredId;
	}

	public void setInsuredId(BigDecimal insuredId) {
		this.insuredId = insuredId;
	}
	
	
	

	public String getHolderMobileTel() {
		return holderMobileTel;
	}

	public void setHolderMobileTel(String holderMobileTel) {
		this.holderMobileTel = holderMobileTel;
	}

	public String getHolderHouseTel() {
		return holderHouseTel;
	}

	public void setHolderHouseTel(String holderHouseTel) {
		this.holderHouseTel = holderHouseTel;
	}

	public String getInsureMobileTel() {
		return insureMobileTel;
	}

	public void setInsureMobileTel(String insureMobileTel) {
		this.insureMobileTel = insureMobileTel;
	}

	public String getInsureHouseTel() {
		return insureHouseTel;
	}

	public void setInsureHouseTel(String insureHouseTel) {
		this.insureHouseTel = insureHouseTel;
	}
	



	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	@Override
	public String toString() {
		return "CsListBRBO [manageName=" + manageName + ", feeStatus=" + feeStatus + ", backTextTime=" + backTextTime
				+ ", failTimes=" + failTimes + ", busiItemId=" + busiItemId + ", validateStartDate=" + validateStartDate
				+ ", validateEndDate=" + validateEndDate + ", finishStartTime=" + finishStartTime + ", finishEndTime="
				+ finishEndTime + ", menteringerInfo=" + menteringerInfo + ", agentName=" + agentName + ", organCode="
				+ organCode + ", channelOrgName=" + channelOrgName + ", acceptOrgCode=" + acceptOrgCode + ", orgCode="
				+ orgCode + ", channelOrgCode=" + channelOrgCode + ", channelType=" + channelType + ", sourceType="
				+ sourceType + ", changeId=" + changeId + ", applyCode=" + applyCode + ", acceptId=" + acceptId
				+ ", acceptCode=" + acceptCode + ", serviceName=" + serviceName + ", serviceCode=" + serviceCode
				+ ", serviceType=" + serviceType + ", policyId=" + policyId + ", policyChgId=" + policyChgId
				+ ", policyCode=" + policyCode + ", applyTime=" + applyTime + ", acceptDate=" + acceptDate
				+ ", reviewTime=" + reviewTime + ", busiProdCode=" + busiProdCode + ", accepterInfo=" + accepterInfo
				+ ", enteringerInfo=" + enteringerInfo + ", checkerInfo=" + checkerInfo + ", nbSalesmanInfo="
				+ nbSalesmanInfo + ", serviceSalesmanInfo=" + serviceSalesmanInfo + ", reviewId=" + reviewId
				+ ", bankCode=" + bankCode + ", bankAccount=" + bankAccount + ", bankUserName=" + bankUserName
				+ ", arapFlag=" + arapFlag + ", feeAmount=" + feeAmount + ", policyOrCustomerCode="
				+ policyOrCustomerCode + ", acceptHolder=" + acceptHolder + ", holderId=" + holderId + ", insuredId="
				+ insuredId + ", holderMobileTel=" + holderMobileTel + ", holderHouseTel=" + holderHouseTel
				+ ", insureMobileTel=" + insureMobileTel + ", insureHouseTel=" + insureHouseTel + ", businessCode="
				+ businessCode + ", bankDealDate=" + bankDealDate + ", fundsRtnCode=" + fundsRtnCode
				+ ", bankTextStatus=" + bankTextStatus + ", feeFlag=" + feeFlag + ", bankName=" + bankName
				+ ", payrefno=" + payrefno + "]";
	}

	
	
}
