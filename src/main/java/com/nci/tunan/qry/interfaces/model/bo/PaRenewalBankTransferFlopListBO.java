package com.nci.tunan.qry.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class PaRenewalBankTransferFlopListBO extends BaseBO {
	// 保单号
	private String policyCode;
	// 应缴日
	private Date dueTime;
	//划款失败原因
	private String bankRetName;
	// 投保人地址
	private String address;
	// 保单所属分公司
	private String branchCode;
	// 银行
	private String bankCode;
	// 账号
	private String bankAccount;
	// 业务操作人
	private String operatorBy;
	// 银行划款状态
	private String bankTextStatus;
	// 失败次数
	private BigDecimal failTimes;
	// 收费人姓名
	private String customerName;
	// 投保人电话
	private String mobileTel;
	// 业务员姓名
	private String agentName;
	// 区部组
	private String salesOrganName;
	 //划款成功日期
	private Date bankDealDate;
	// 交费次数
	private BigDecimal paidCount;
	// 保单状态
	private String policyType;
	// 起期
	private Date checkEnterStartTime;
	// 止期
	private Date checkEnterEndTime;
	// 主险保费
	private BigDecimal masterExtra;
	// 附加险保费
	private BigDecimal additionalExtra;
	// 健康加费
	private BigDecimal healthExtra;
	// 职业加费
	private BigDecimal jobExtra;
	// 应收保费
	private BigDecimal feeAmount;
	// 失败原因
	private String rtnCode;
	// 投保人
	private String holderName;
	// 管理机构
	private String organCode;
	// 管理机构名称
	private String organName;
	// 销售渠道
	private String channelType;
	// 销售渠道名称
	private String salesChannelName;
	
	// 银行网点
	private String serviceBankBranch;
	// 银行网点名称
	private String serviceBankBranchName;
	
	// 现服务人员区
	private String area;
	// 现服务人员部
	private String part;
	// 现服务人员组
	private String groups;

	/**
	 * @Fields salesOrganCode : 业务员销售机构
	 */
	private String salesOrganCode;
	
	/**
     * 特殊保单标记
     */
    private String status;
    /**
	 * 主险简称
	 */
	private String productAbbrName;
    
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	public String getSalesOrganName() {
		return salesOrganName;
	}

	public void setSalesOrganName(String salesOrganName) {
		this.salesOrganName = salesOrganName;
	}

	public Date getBankDealDate() {
		return bankDealDate;
	}

	public void setBankDealDate(Date bankDealDate) {
		this.bankDealDate = bankDealDate;
	}

	public BigDecimal getPaidCount() {
		return paidCount;
	}

	public void setPaidCount(BigDecimal paidCount) {
		this.paidCount = paidCount;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public Date getDueTime() {
		return dueTime;
	}

	public void setDueTime(Date dueTime) {
		this.dueTime = dueTime;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getBranchCode() {
		return branchCode;
	}

	public void setBranchCode(String branchCode) {
		this.branchCode = branchCode;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankAccount() {
		return bankAccount;
	}

	public void setBankAccount(String bankAccount) {
		this.bankAccount = bankAccount;
	}


	public String getOperatorBy() {
		return operatorBy;
	}

	public void setOperatorBy(String operatorBy) {
		this.operatorBy = operatorBy;
	}

	public String getBankTextStatus() {
		return bankTextStatus;
	}

	public void setBankTextStatus(String bankTextStatus) {
		this.bankTextStatus = bankTextStatus;
	}

	public BigDecimal getFailTimes() {
		return failTimes;
	}

	public void setFailTimes(BigDecimal failTimes) {
		this.failTimes = failTimes;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getMobileTel() {
		return mobileTel;
	}

	public void setMobileTel(String mobileTel) {
		this.mobileTel = mobileTel;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getPolicyType() {
		return policyType;
	}

	public void setPolicyType(String policyType) {
		this.policyType = policyType;
	}

	public Date getCheckEnterStartTime() {
		return checkEnterStartTime;
	}

	public void setCheckEnterStartTime(Date checkEnterStartTime) {
		this.checkEnterStartTime = checkEnterStartTime;
	}

	public Date getCheckEnterEndTime() {
		return checkEnterEndTime;
	}

	public void setCheckEnterEndTime(Date checkEnterEndTime) {
		this.checkEnterEndTime = checkEnterEndTime;
	}

	public BigDecimal getMasterExtra() {
		return masterExtra;
	}

	public void setMasterExtra(BigDecimal masterExtra) {
		this.masterExtra = masterExtra;
	}

	public BigDecimal getAdditionalExtra() {
		return additionalExtra;
	}

	public void setAdditionalExtra(BigDecimal additionalExtra) {
		this.additionalExtra = additionalExtra;
	}

	public BigDecimal getHealthExtra() {
		return healthExtra;
	}

	public void setHealthExtra(BigDecimal healthExtra) {
		this.healthExtra = healthExtra;
	}

	public BigDecimal getJobExtra() {
		return jobExtra;
	}

	public void setJobExtra(BigDecimal jobExtra) {
		this.jobExtra = jobExtra;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public String getRtnCode() {
		return rtnCode;
	}

	public void setRtnCode(String rtnCode) {
		this.rtnCode = rtnCode;
	}

	public String getHolderName() {
		return holderName;
	}

	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getSalesChannelName() {
		return salesChannelName;
	}

	public void setSalesChannelName(String salesChannelName) {
		this.salesChannelName = salesChannelName;
	}

	public String getSalesOrganCode() {
		return salesOrganCode;
	}

	public void setSalesOrganCode(String salesOrganCode) {
		this.salesOrganCode = salesOrganCode;
	}
	public String getBankRetName() {
		return bankRetName;
	}

	public void setBankRetName(String bankRetName) {
		this.bankRetName = bankRetName;
	}
	
	public String getServiceBankBranch() {
		return serviceBankBranch;
	}

	public void setServiceBankBranch(String serviceBankBranch) {
		this.serviceBankBranch = serviceBankBranch;
	}

	public String getServiceBankBranchName() {
		return serviceBankBranchName;
	}

	public void setServiceBankBranchName(String serviceBankBranchName) {
		this.serviceBankBranchName = serviceBankBranchName;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getPart() {
		return part;
	}

	public void setPart(String part) {
		this.part = part;
	}

	public String getGroups() {
		return groups;
	}

	public void setGroups(String groups) {
		this.groups = groups;
	}

	public String getProductAbbrName() {
		return productAbbrName;
	}

	public void setProductAbbrName(String productAbbrName) {
		this.productAbbrName = productAbbrName;
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}
}
