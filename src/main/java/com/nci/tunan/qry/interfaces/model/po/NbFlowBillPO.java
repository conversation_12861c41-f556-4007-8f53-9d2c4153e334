package com.nci.tunan.qry.interfaces.model.po;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.math.BigDecimal;
import java.sql.Clob;
import java.sql.SQLException;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/**
 * 新契约流程清单PO
 * 
 * <AUTHOR>
 * 
 */
/**20180924 提交**/
public class NbFlowBillPO extends BasePO {

	private static final long serialVersionUID = 4312938691584502969L;
	
	
	public String getHolderResidentName() {
		
		return getString("holder_resident_name");
	}

	public void setHolderResidentName(String holderResidentName) {
		
		setString("holder_resident_name", holderResidentName);
	}
	
	
	public String getInsuredResidentName() {
		return getString("insured_resident_name");
	}

	public void setInsuredResidentName(String insuredResidentName) {
		setString("insured_resident_name", insuredResidentName);
	}
	
	
	
	public String getPrintOrg() {
		return getString("print_org");
	}

	public void setPrintOrg(String printOrg) {
		setString("print_org", printOrg);
	}
	
	public String getPrintOrgName() {
		return getString("print_org_name");
	}

	public void setPrintOrgName(String printOrgName) {
		setString("print_org_name", printOrgName);
	}
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	public void setStartDate(Date startDate) {
		setUtilDate("start_date", startDate);
	}

	public Date getStartDate() {
		return getUtilDate("start_date");
	}
	public void setEndDate(Date endDate) {
		setUtilDate("end_date", endDate);
	}

	public Date getEndDate() {
		return getUtilDate("end_date");
	}

	// **************************************************
	//private BigDecimal firstPrem;//首期保费
	public BigDecimal getFirstPrem() {
		return  getBigDecimal("first_prem");
	}

	public void setFirstPrem(BigDecimal firstPrem) {
		setBigDecimal("first_prem", firstPrem);
	}
	public String getReceiveTime() {
		return getString("receive_time");
	}

	public void setReceiveTime(String receiveTime) {
		setString("receive_time", receiveTime);
	}
	public String getSendState() {
		return getString("send_state");
	}

	public void setSendState(String sendState) {
		setString("send_state", sendState);
	}

	public String getReceiveState() {
		return getString("receive_state");
	}

	public void setReceiveState(String receiveState) {
		setString("receive_state" , receiveState);
	}
	public Date getSendTime() {
		return getUtilDate("send_time");
	}
	public void setSendTime(Date sendTime) {
		setUtilDate("send_time", sendTime);
	}
	public Date getReceiveDate() {
		return getUtilDate("receive_date");
	}
	public void setReceiveDate(Date receiveDate) {
		setUtilDate("receive_date", receiveDate);
	}
	
	public String getConclusionDesc() {
		return getString("conclusion_desc");
	}

	public void setConclusionDesc(String conclusionDesc) {
		setString("conclusion_desc", conclusionDesc);
	}

	public String getScanStatus() {
		return getString("scan_status");
	}

	public void setScanStatus(String scanStatus) {
		setString("scan_status", scanStatus);
	}

	public String getPrintStatusName() {
		return getString("print_status_name");
	}

	public void setPrintStatusName(String printStatusName) {
		setString("print_status_name", printStatusName);
	}

	public String getSignResultDesc() {
		return getString("sign_result_desc");
	}

	public void setSignResultDesc(String signResultDesc) {
		setString("sign_result_desc", signResultDesc);
	}

	public String getCauseTypeDesc() {
		return getString("cause_type_desc");
	}

	public void setCauseTypeDesc(String causeTypeDesc) {
		setString("cause_type_desc", causeTypeDesc);
	}

	public String getQtstatusName() {
		return getString("qtstatus_name");
	}

	public void setQtstatusName(String qtstatusName) {
		setString("qtstatus_name", qtstatusName);
	}

	public String getErrorSourceDesc() {
		return getString("error_source_desc");
	}

	public void setErrorSourceDesc(String errorSourceDesc) {
		setString("error_source_desc", errorSourceDesc);
	}

	public String getErrorCorrectTypeName() {
		return getString("error_correct_type_name");
	}

	public void setErrorCorrectTypeName(String errorCorrectTypeName) {
		setString("error_correct_type_name", errorCorrectTypeName);
	}

	public String getErrstatusName() {
		return getString("errstatus_name");
	}

	public void setErrstatusName(String errstatusName) {
		setString("errstatus_name", errstatusName);
	}

	public String getSiteName() {
		return getString("site_name");
	}

	public void setSiteName(String siteName) {
		setString("site_name", siteName);
	}

	// ********************************************

	public BigDecimal getCertiQuestion() {
		return getBigDecimal("certi_question");
	}

	public void setCertiQuestion(BigDecimal certiQuestion) {
		setBigDecimal("certi_question", certiQuestion);
	}

	public BigDecimal getTelQuestion() {
		return getBigDecimal("tel_question");
	}

	public void setTelQuestion(BigDecimal telQuestion) {
		setBigDecimal("tel_question", telQuestion);
	}

	public String getAccountQuestion() {
		return getString("account_question");
	}

	public void setAccountQuestion(String accountQuestion) {
		setString("account_question", accountQuestion);
	}

	// private String comments;//初审原因说明

	public String getComments() {
		return getString("comments");
	}

	public void setComments(String comments) {
		setString("comments", comments);
	}

	// private String statusCode;//纠错状态
	public void setStatusCode(String statusCode) {
		setString("status_code", statusCode);
	}

	public String getStatusCode() {
		return getString("status_code");
	}

	// private String customerLevel;
	public void setCustomerLevel(String customerLevel) {
		setString("customer_level", customerLevel);
	}

	public String getCustomerLevel() {
		return getString("customer_level");
	}
	
//***********************************************************88
	public void setAuditBy(String auditBy) {
		setString("audit_by", auditBy);
	}

	public String getAuditBy() {
		return getString("audit_by");
	}
	
	public void setAppBy(String appBy) {
		setString("app_by", appBy);
	}

	public String getAppBy() {
		return getString("app_by");
	}

	// private String reminderStatus;
	public void setReminderStatus(String reminderStatus) {
		setString("reminder_status", reminderStatus);
	}

	public String getReminderStatus() {
		return getString("reminder_status");
	}

	public void setOrganCode(String organCode) {
		setString("organ_code", organCode);
	}

	public String getOrganCode() {
		return getString("organ_code");
	}
//***************6666666
	public void setApplyCode(String applyCode) {
		setString("apply_code", applyCode);
	}

	public String getApplyCode() {
		return getString("apply_code");
	}

	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}

	public String getPolicyCode() {
		return getString("policy_code");
	}

	public void setAgentCode(String agentCode) {
		setString("agent_code", agentCode);
	}

	public String getAgentCode() {
		return getString("agent_code");
	}

	public void setAgentName(String agentName) {
		setString("agent_name", agentName);
	}

	public String getAgentName() {
		return getString("agent_name");
	}

	//99999999999999999999999999
	public void setSalesOrganName(String salesOrganName) {
		setString("sales_organ_name", salesOrganName);
	}

	public String getSalesOrganName() {
		return getString("sales_organ_name");
	}

	public String getBusinessPrdId() {
		return getString("business_prd_id");
	}

	public void setBusinessPrdId(String businessPrdId) {
		setString("business_prd_id", businessPrdId);
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		setBigDecimal("fee_amount", feeAmount);
	}

	public BigDecimal getFeeAmount() {
		return getBigDecimal("fee_amount");
	}

	public void setSubmitChannel(BigDecimal submitChannel) {
		setBigDecimal("submit_channel", submitChannel);
	}

	public BigDecimal getSubmitChannel() {
		return getBigDecimal("submit_channel");
	}

	public void setOverdueTime(Date overdueTime) {
		setUtilDate("overdue_time", overdueTime);
	}

	public Date getOverdueTime() {
		return getUtilDate("overdue_time");
	}

	public void setMediaType(BigDecimal mediaType) {
		setBigDecimal("media_type", mediaType);
	}

	public BigDecimal getMediaType() {
		return getBigDecimal("media_type");
	}

	public void setConclusionCode(BigDecimal conclusioncode) {
		setBigDecimal("conclusion_code", conclusioncode);
	}

	public BigDecimal getConclusionCode() {
		return getBigDecimal("conclusion_code");
	}

	public void setCauseDesc(String causeDesc) {
		setString("cause_desc", causeDesc);
	}

	public String getCauseDesc() {
		return getString("cause_desc");
	}
 
	//*******************(((
	public void setFeeStatus(String feeStatus) {
		setString("fee_status", feeStatus);
	}

	public String getFeeStatus() {
		return getString("fee_status");
	}

	public void setUnitNumber(String unitNumber) {
		setString("unit_number", unitNumber);
	}

	public String getUnitNumber() {
		return getString("unit_number");
	}
	 
	public void setImageScanStatus(BigDecimal imageScanStatus) {
		setBigDecimal("image_scan_status", imageScanStatus);
	}

	public BigDecimal getImageScanStatus() {
		return getBigDecimal("image_scan_status");
	}

	public void setProposalStatus(String proposalStatus) {
		setString("proposal_status", proposalStatus);
	}

	public String getProposalStatus() {
		return getString("proposal_status");
	}

	public void setInsertTime(Date insertTime) {
		setUtilDate("insert_time", insertTime);
	}

	public Date getInsertTime() {
		return getUtilDate("insert_time");
	}

	public void setUwCompleteTime(Date uwCompleteTime) {
		setUtilDate("uw_complete_time", uwCompleteTime);
	}

	public Date getUwCompleteTime() {
		return getUtilDate("uw_complete_time");
	}

	public void setUwUserCode(String uwUserCode) {
		setString("uw_user_code", uwUserCode);
	}

	public String getUwUserCode() {
		return getString("uw_user_code");
	}
//*******************************************************************

	public void setOperateTime(Date operateTime) {
		setUtilDate("operate_time", operateTime);
	}

	public Date getOperateTime() {
		return getUtilDate("operate_time");
	}

	public void setCauseIdList(String causeIdList) {
		setString("cause_id_list", causeIdList);
	}

	public String getCauseIdList() {
		return getString("cause_id_list");
	}

	public void setPrintStatus(String printStatus) {
		setString("print_status", printStatus);
	}

	public String getPrintStatus() {
		return getString("print_status");
	}

	public void setPrintType(String printType) {
		setString("print_type", printType);
	}

	public String getPrintType() {
		return getString("print_type");
	}

	public void setSiteCode(BigDecimal siteCode) {
		setBigDecimal("site_code", siteCode);
	}

	public BigDecimal getSiteCode() {
		return getBigDecimal("site_code");
	}

	public void setPostId(String postId) {
		setString("post_id", postId);
	}

	public String getPostId() {
		return getString("post_id");
	}

	public void setPrintTimes(BigDecimal printTimes) {
		setBigDecimal("print_times", printTimes);
	}

	public BigDecimal getPrintTimes() {
		return getBigDecimal("print_times");
	}

	public void setSignStatus(String signStatus) {
		setString("sign_status", signStatus);
	}

	public String getSignStatus() {
		return getString("sign_status");
	}

	public void setCauseType(String causeType) {
		setString("cause_type", causeType);
	}

	public String getCauseType() {
		return getString("cause_type");
	}
	
	public void setQtStatus(BigDecimal qtStatus) {
		setBigDecimal("qt_status", qtStatus);
	}

	public BigDecimal getQtStatus() {
		return getBigDecimal("qt_status");
	}
	//ddd
	 //*****************************************************************8
	public void setQtTime(Date qtTime) {
		setUtilDate("qt_time", qtTime);
	}

	public Date getQtTime() {
		return getUtilDate("qt_time");
	}

	public void setQtUser(String qtUser) {
		setString("qt_user", qtUser);
	}

	public String getQtUser() {
		return getString("qt_user");
	}

	public void setErrorSource(String errorSource) {
		setString("error_source", errorSource);
	}

	public String getErrorSource() {
		return getString("error_source");
	}

	public void setCorrectType(String correctType) {
		setString("error_correct_type", correctType);
	}

	public String getCorrectType() {
		return getString("error_correct_type");
	}

	public void setAppDate(Date appDate) {
		setUtilDate("app_date", appDate);
	}

	public Date getAppDate() {
		return getUtilDate("app_date");
	}

	public void setAuditDate(Date auditDate) {
		setUtilDate("audit_date", auditDate);
	}

	public Date getAuditDate() {
		return getUtilDate("audit_date");
	}

	public void setValiDate(Date valiDate) {
		setUtilDate("vali_date", valiDate);
	}

	public Date getValiDate() {
		return getUtilDate("vali_date");
	}

	public void setPolicyRecycleDate(Date policyRecycleDate) {
		setUtilDate("policy_recycle_date", policyRecycleDate);
	}

	public Date getPolicyRecycleDate() {
		return getUtilDate("policy_recycle_date");
	}

	public void setReminderDate(Date reminderDate) {
		setUtilDate("reminder_date", reminderDate);
	}

	public Date getReminderDate() {
		return getUtilDate("reminder_date");
	}

	public void setCustomerName(String customerName) {
		setString("customer_name", customerName);
	}

	public String getCustomerName() {
		return getString("customer_name");
	}

	public void setApplicationName(String applicationName) {
		setString("application_name", applicationName);
	}

	public String getApplicationName() {
		return getString("application_name");
	}

	public String getProductNameSys() {
		return getString("product_name_sys");
	}

	public void setProductNameSys(String productNameSys) {
		setString("product_name_sys", productNameSys);
	}

	public void setErrorDetail(String errorDetail) {
		setString("error_detail", errorDetail);
	}

	public String getErrorDetail() {
		return getString("error_detail");
	}

	public void setInputDate(Date inputDate) {
		setUtilDate("input_date", inputDate);
	}

	public Date getInputDate() {
		return getUtilDate("input_date");
	}

	//*****************************************
	public void setApplyDate(Date applyDate) {
		setUtilDate("apply_date", applyDate);
	}

	public Date getApplyDate() {
		return getUtilDate("apply_date");
	}
	/*承保日期*/
	public void setIssueDate(Date issueDate) {
		setUtilDate("issue_date", issueDate);
	}

	public Date getIssueDate() {
		return getUtilDate("issue_date");
	}

	public void setAcknowledgementInsertTime(Date acknowledgementInsertTime) {
		setUtilDate("acknowledgement_insert_time", acknowledgementInsertTime);
	}

	public Date getAcknowledgementInsertTime() {
		return getUtilDate("acknowledgement_insert_time");
	}

	// 电子保单重出次数
	public BigDecimal getElePrintTimes() {
		return getBigDecimal("ele_printtimes");
	}

	public void setElePrintTimes(BigDecimal elePrintTimes) {
		setBigDecimal("ele_printtimes", elePrintTimes);
	}

	// 展业机构名称
	public void setExhibitionName(String exhibitionName) {
		setString("exhibition_name", exhibitionName);
	}

	public String getExhibitionName() {
		return getString("exhibition_name");
	}

	// 销售渠道 = 所属渠道                   ********************************************************
	public void setChannelType(String channelType) {
		setString("channel_type", channelType);
	}

	public String getChannelType() {
		return getString("channel_type");
	}

	// 销售渠道 = 所属渠道
	public void setChannelTypeName(String channelTypeName) {
		setString("channel_typename", channelTypeName);
	}

	public String getChannelTypeName() {
		return getString("channel_typename");
	}

	// 递交渠道 = 终端类型
	public void setSubmitChannelName(String submitChannelName) {
		setString("submit_channelname", submitChannelName);
	}

	public String getSubmitChannelName() {
		return getString("submit_channelname");
	}

	// 划款次数
	public BigDecimal getPaycount() {
		return getBigDecimal("paycount");
	}

	public void setPaycount(BigDecimal paycount) {
		setBigDecimal("paycount", paycount);
	}

	// 已预收金额
	public BigDecimal getPayAmount() {
		return getBigDecimal("pay_amount");
	}

	public void setPayAmount(BigDecimal payAmount) {
		setBigDecimal("pay_amount", payAmount);
	}

	// 划款状态
	public String getPayStatus() {
		return getString("pay_status");
	}

	public void setPayStatus(String payStatus) {
		setString("pay_status", payStatus);
	}

	// 扫描次数
	public BigDecimal getScanTimes() {
		return getBigDecimal("scan_times");
	}

	public void setScanTimes(BigDecimal scanTimes) {
		setBigDecimal("scan_times", scanTimes);
	}

	// 签单状态时间
	public Date getOutDate() {
		return getUtilDate("out_date");
	}

	public void setOutDate(Date outDate) {
		setUtilDate("out_date", outDate);
	}
	
	// 出单时间 签单时间
	public Date getOperationTime() {
		return getUtilDate("operation_time");
	}

	public void setOperationTime(Date operationTime) {
		setUtilDate("operation_time", operationTime);
	}

	// 录入开始时间
	public Date getAddDate() {
		return getUtilDate("add_date");
	}

	public void setAddDate(Date addDate) {
		setUtilDate("add_date", addDate);
	}

	// 首次核保时间
	public Date getCheckTime() {
		return getUtilDate("check_time");
	}

	public void setCheckTime(Date checkTime) {
		setUtilDate("check_time", checkTime);
	}
///******************************************************************
	// 首次打印时间
	public Date getFirstPrintTime() {
		return getUtilDate("first_print_time");
	}

	public void setFirstPrintTime(Date firstPrintTime) {
		setUtilDate("first_print_time", firstPrintTime);
	}

	// 最后打印时间
	public Date getLastPrintTime() {
		return getUtilDate("last_print_time");
	}

	public void setLastPrintTime(Date lastPrintTime) {
		setUtilDate("last_print_time", lastPrintTime);
	}

	// 付款方式
	public void setPayType(String payType) {
		setString("pay_type", payType);
	}

	public String getPayType() {
		return getString("pay_type");
	}

	// 核保结论
	public void setDecisionDesc(String decisionDesc) {
		setString("decision_desc", decisionDesc);
	}

	public String getDecisionDesc() {
		return getString("decision_desc");
	}

	// 录入状态
	public void setStatusDesc(String statusDesc) {
		setString("status_desc", statusDesc);
	}

	public String getStatusDesc() {
		return getString("status_desc");
	}

	// //问题件ID
	public void setIssueId(BigDecimal issueId) {
		setBigDecimal("issue_id", issueId);
	}

	public BigDecimal getIssueId() {
		return getBigDecimal("issue_id");
	}

	// 问题件类型
	public void setIssueType(String issueType) {
		setString("issueType", issueType);
	}

	public String getIssueType() {
		return getString("issueType");
	}

	// 是否开门红
	public BigDecimal getIfGoodStart() {
		return getBigDecimal("if_good_start");
	}

	public void setIfGoodStart(BigDecimal ifGoodStart) {
		setBigDecimal("if_good_start", ifGoodStart);
	}

	// 录入完成时间
	public void setEndTime(Date endTime) {
		setUtilDate("end_time", endTime);
	}

	public Date getEndTime() {
		return getUtilDate("end_time");
	}

	// 机构名称
	public void setOrgName(String orgName) {
		setString("organ_name", orgName);
	}

	public String getOrgName() {
		return getString("organ_name");
	}

	// 划款失败原因
	public void setStatusName(String statusName) {
		setString("status_name", statusName);
	}

	public String getStatusName() {
		return getString("status_name");
	}
	
	public void setFinishTime(Date finishTime) {
		setUtilDate("finish_time", finishTime);
	}
	
	public Date getFinishTime() {
		return getUtilDate("finish_time");
	}
	
	public void setStartFinishTime(Date startFinishTime) {
		setUtilDate("start_finish_time", startFinishTime);
	}
	
	public Date getStartFinishTime() {
		return getUtilDate("start_finish_time");
	}
	
	public void setEndFinishTime(Date endFinishTime) {
		setUtilDate("end_finish_time", endFinishTime);
	}
	
	public Date getEndFinishTime() {
		return getUtilDate("end_finish_time");
	}
	public BigDecimal getRefundAmount() {
		return getBigDecimal("refund_amount");
	}
	
	public void setRefundAmount(BigDecimal refundAmount) {
		setBigDecimal("refund_amount", refundAmount);
	}
	
	public BigDecimal getSignDate() {
		return getBigDecimal("sign_date");
	}
	
	public void setSignDate(BigDecimal signDate) {
		setBigDecimal("sign_date", signDate);
	}
	
	public void setChangePolicyTime(Date changePolicyTime) {
		setUtilDate("change_policy_time", changePolicyTime);
	}
	
	public Date getChangePolicyTime() {
		return getUtilDate("change_policy_time");
	}
	
	public void setAcknowledgementImageTime(Date acknowledgementImageTime) {
		setUtilDate("acknowledgement_image_time", acknowledgementImageTime);
	}
	
	public Date getAcknowledgementImageTime() {
		return getUtilDate("acknowledgement_image_time");
	}
	
	public void setErrorContent(String errorContent) {
		setString("error_content", errorContent);
	}

	public String getErrorContent() {
		return getString("error_content");
	}
	
	public BigDecimal getPlatformFlag() {
		return getBigDecimal("platform_flag");
	}
	public void setPlatformFlag(BigDecimal platformFlag) {
		setBigDecimal("platform_flag", platformFlag);
	}
	
	public void setConfirmWay(String confirmWay) {
		setString("confirm_way", confirmWay);
	}

	public String getConfirmWay() {
		return getString("confirm_way");
	}
	
	public String getAgentlevel() {
		return getString("agent_level");
	}

	public void setAgentlevel(String agentlevel) {
		setString("agent_level", agentlevel);
	}

	public String getSubinputType() {
		return getString("subinput_Type");
	}

	public void setSubinputType(String subinputType) {
		setString("subinput_Type", subinputType);
	}

	public String getScannum() {
		return getString("scannum");
	}

	public void setScannum(String scannum) {
		setString("scannum", scannum);
	}

	public String getProcessstep() {
		return getString("process_step");
	}

	public void setProcessstep(String processstep) {
		setString("process_step", processstep);
	}

	public String getProblemdoc() {
		return getString("problemdoc");
	}

	public void setProblemdoc(String problemdoc) {
		setString("problemdoc", problemdoc);
	}

	public String getEfficiency() {
		return getString("efficiency");
	}

	public void setEfficiency(String efficiency) {
		setString("efficiency", efficiency);
	}
	public String getAccountquestiontype() {
		return getString("account_question_type");
	}

	public void setAccountquestiontype(String accountquestiontype) {
		setString("account_question_type", accountquestiontype);
	}

	public String getPaymode() {
		return getString("pay_mode");
	}

	public void setPaymode(String paymode) {
		setString("pay_mode", paymode);
	}

	public String getBanktextstatus() {
		return getString("bank_text_status");
	}

	public void setBanktextstatus(String banktextstatus) {
		setString("bank_text_status", banktextstatus);
	}


	public String getPayfailcause() {
		return getString("payfailcause");
	}

	public void setPayfailcause(String payfailcause) {
		setString("payfailcause", payfailcause);
	}
	
	public void setScandate(Date scandate) {
		setUtilDate("scandate", scandate);
	}
	
	public Date getScandate() {
		return getUtilDate("scandate");
	}
	
	public String getProductCode() {
		return getString("product_code");
	}

	public void setProductCode(String productCode) {
		setString("product_code", productCode);
	}
	
	public String getQuestiontype() {
		return getString("question_type");
	}

	public void setQuestiontype(String questiontype) {
		setString("question_type", questiontype);
	}
	
	
	// 保存时调用该方法
		public void saveContent(String content) {
			set("content", content);
		}

		// 查询时调用
		public void setContent(Clob content) {
			set("content", content);
		}

		public String getContent() {
			Object temp = get("content");
			String str = null;
			if (null != temp) {
				if (temp instanceof String) {
					return (String) temp;
					// else if (temp instanceof oracle.sql.CLOB) {
				} else if (temp instanceof Clob) {
					// oracle.sql.CLOB c = (oracle.sql.CLOB) temp;
					Clob c = (java.sql.Clob) temp;
					try {
						// str= c.getSubString((long)1, (int)c.length());
						str = clobToString(c);
					} catch (Exception e) {
						e.printStackTrace();
					}
					return str;
				}
			}
			return null;
		}

		/**
		 * clob 转换 string
		 * 
		 * @description
		 * @version
		 * @title
		 * @<NAME_EMAIL>
		 * @param clob
		 *            通知书内容
		 * @return 通知书内容str
		 * @throws SQLException
		 * @throws IOException
		 */
		public static String clobToString(Clob clob) throws SQLException,
				IOException {
			String reString = null;
			Reader is = clob.getCharacterStream(); // 得到流
			BufferedReader br = new BufferedReader(is);
			String s = br.readLine();
			StringBuffer sb = new StringBuffer();
			while (s != null) { // 执行循环将字符串全部取出付值给 StringBuffer由StringBuffer转成STRING
				sb.append(s);
				s = br.readLine();
			}
			reString = sb.toString();
			return reString;
		}	
	
	@Override
	public String toString() {
		return "NbFlowBillPO [getOrganCode()=" + getOrganCode()
				+ ", getApplyCode()=" + getApplyCode() + ", getPolicyCode()="
				+ getPolicyCode() + ", getAgentCode()=" + getAgentCode()
				+ ", getAgentName()=" + getAgentName()
				+ ", getSalesOrganName()=" + getSalesOrganName()
				+ ", getBusinessPrdId()=" + getBusinessPrdId()
				+ ", getFeeAmount()=" + getFeeAmount()
				+ ", getSubmitChannel()=" + getSubmitChannel()
				+ ", getOverdueTime()=" + getOverdueTime()
				+ ", getMediaType()=" + getMediaType()
				+ ", getConclusionCode()=" + getConclusionCode()
				+ ", getCauseDesc()=" + getCauseDesc() + ", getFeeStatus()="
				+ getFeeStatus() + ", getUnitNumber()=" + getUnitNumber()
				+ ", getImageScanStatus()=" + getImageScanStatus()
				+ ", getProposalStatus()=" + getProposalStatus()
				+ ", getInsertTime()=" + getInsertTime()
				+ ", getUwCompleteTime()=" + getUwCompleteTime()
				+ ", getUwUserCode()=" + getUwUserCode()
				+ ", getOperateTime()=" + getOperateTime()
				+ ", getCauseIdList()=" + getCauseIdList()
				+ ", getPrintStatus()=" + getPrintStatus()
				+ ", getPrintType()=" + getPrintType() + ", getSiteCode()="
				+ getSiteCode() + ", getPostId()=" + getPostId()
				+ ", getPrintTimes()=" + getPrintTimes() + ", getSignStatus()="
				+ getSignStatus() + ", getCauseType()=" + getCauseType()
				+ ", getQtStatus()=" + getQtStatus() + ", getQtTime()="
				+ getQtTime() + ", getQtUser()=" + getQtUser()
				+ ", getErrorSource()=" + getErrorSource()
				+ ", getCorrectType()=" + getCorrectType() + ", getAppDate()="
				+ getAppDate() + ", getAuditDate()=" + getAuditDate()
				+ ", getValiDate()=" + getValiDate()
				+ ", getPolicyRecycleDate()=" + getPolicyRecycleDate()
				+ ", getReminderDate()=" + getReminderDate()
				+ ", getCustomerName()=" + getCustomerName()
				+ ", getApplicationName()=" + getApplicationName()
				+ ", getStartDate()=" + getStartDate() + ", getEndDate()="
				+ getEndDate() + ", getProductNameSys()=" + getProductNameSys()
				+ ", getErrorDetail()=" + getErrorDetail()
				+ ", getFinishtime()=" + getFinishTime()
				+ ", getChangePolicyTime()=" + getChangePolicyTime()
				+ ", getConfirmWay()=" + getConfirmWay()
				+ ", getInputDate()=" + getInputDate()
				+ ", getAcknowledgementInsertTime()="
				+ getAcknowledgementInsertTime()
				+ ", getAcknowledgementImageTime()="
				+ getAcknowledgementImageTime() + "]";
	}
}