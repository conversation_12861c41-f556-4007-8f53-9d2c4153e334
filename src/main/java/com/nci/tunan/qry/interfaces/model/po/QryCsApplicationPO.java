package com.nci.tunan.qry.interfaces.model.po;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;

/** 
 * @description CsApplicationPO对象
 * <AUTHOR> 
 * @date 2016-03-05 11:39:27  
 */
/**20180924提交**/
public class QryCsApplicationPO extends BasePO {
 	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// applicantType  ---  String  ---  VARCHAR2_3_0_申请人类型;
		// agentCertiType  ---  String  ---  CHAR_2_0_代办人证件类型;
		// billSendType  ---  String  ---  VARCHAR2_3_0_单证发放方式（纸质单证）;
		// tryCalcNo  ---  String  ---  VARCHAR2_30_0_试算编号;
		// applyTime  ---  Date  ---  DATE_7_0_保全申请时间;
		// customerId  ---  BigDecimal  ---  NUMBER_16_0_申请人客户号;
		// finishTime  ---  Date  ---  DATE_7_0_结束时间;
		// cancelCause  ---  String  ---  VARCHAR2_3_0_保全撤销原因;
		// applyCode  ---  String  ---  VARCHAR2_20_0_保全申请号;
			// sourceType  ---  String  ---  VARCHAR2_2_0_受理来源：渠道;
		// organCode  ---  String  ---  VARCHAR2_8_0_操作人员机构号;
			// changeId  ---  BigDecimal  ---  NUMBER_16_0_保全申请ID;
		// agentLevel  ---  String  ---  NUMBER_22_0_代办人绩优等级;
		// serviceType  ---  String  ---  VARCHAR2_3_0_保全申请方式:亲办,代办;
		// applyName  ---  String  ---  VARCHAR2_50_0_申请人姓名;
		// toUwDate  ---  Date  ---  DATE_7_0_提交核保日;
			// agentName  ---  String  ---  VARCHAR2_120_0_代办人姓名;
		// agentCertiCode  ---  String  ---  VARCHAR2_30_0_代办人证件号码;
			// cancelNote  ---  String  ---  VARCHAR2_400_0_保全撤销说明;
		// eventCode  ---  String  ---  VARCHAR2_20_0_汇缴事件号;
		// appStatus  ---  String  ---  VARCHAR2_5_0_保全变更状态;
		// cancelId  ---  BigDecimal  ---  NUMBER_10_0_保全撤销操作人;
		// nofillFlag  ---  BigDecimal  ---  NUMBER_1_0_是否免填单;
			// agentTel  ---  String  ---  VARCHAR2_20_0_代办人联系电话;
			// agentCode  ---  String  ---  VARCHAR2_20_0_null;
		// toApproveDate  ---  Date  ---  DATE_7_0_提交复核日;
	public void setPolicyCode(String poicyCode){
		setString("policy_code",poicyCode);
	}
	
	public String getPolicyCode(){
		return getString("policy_code");
	}
	
	public void setPreFlag(BigDecimal preFlag){
		setBigDecimal("pre_flag", preFlag);
	}
	
	public BigDecimal getPreFlag(){
		return getBigDecimal("pre_flag");
	}
	
		public void setApplicantType(String applicantType){
		setString("applicant_type", applicantType);
	}
	
	public String getApplicantType(){
		return getString("applicant_type");
	}
		public void setAgentCertiType(String agentCertiType){
		setString("agent_certi_type", agentCertiType);
	}
		
	public void setIsElecSign(BigDecimal isElecSign){
		setBigDecimal("is_elec_sign", isElecSign);
	}
	
	public BigDecimal getIsElecSign(){
		return getBigDecimal("is_elec_sign");
	}
	public String getAgentCertiType(){
		return getString("agent_certi_type");
	}
		public void setBillSendType(String billSendType){
		setString("bill_send_type", billSendType);
	}
	
	public String getBillSendType(){
		return getString("bill_send_type");
	}
		public void setTryCalcNo(String tryCalcNo){
		setString("try_calc_no", tryCalcNo);
	}
	
	public String getTryCalcNo(){
		return getString("try_calc_no");
	}
		public void setApplyTime(Date applyTime){
		setUtilDate("apply_time", applyTime);
	}
	
	public Date getApplyTime(){
		return getUtilDate("apply_time");
	}
		public void setCustomerId(BigDecimal customerId){
		setBigDecimal("customer_id", customerId);
	}
	
	public BigDecimal getCustomerId(){
		return getBigDecimal("customer_id");
	}
		public void setFinishTime(Date finishTime){
		setUtilDate("finish_time", finishTime);
	}
	
	public Date getFinishTime(){
		return getUtilDate("finish_time");
	}
		public void setCancelCause(String cancelCause){
		setString("cancel_cause", cancelCause);
	}
	
	public String getCancelCause(){
		return getString("cancel_cause");
	}
		public void setApplyCode(String applyCode){
		setString("apply_code", applyCode);
	}
	
	public String getApplyCode(){
		return getString("apply_code");
	}
			public void setSourceType(String sourceType){
		setString("source_type", sourceType);
	}
	
	public String getSourceType(){
		return getString("source_type");
	}
		public void setOrganCode(String organCode){
		setString("organ_code", organCode);
	}
	
	public String getOrganCode(){
		return getString("organ_code");
	}
			public void setChangeId(BigDecimal changeId){
		setBigDecimal("change_id", changeId);
	}
	
	public BigDecimal getChangeId(){
		return getBigDecimal("change_id");
	}
		public void setAgentLevel(String agentLevel){
		setString("agent_level", agentLevel);
	}
	
	public String getAgentLevel(){
		return getString("agent_level");
	}
		public void setServiceType(String serviceType){
		setString("service_type", serviceType);
	}
	
	public String getServiceType(){
		return getString("service_type");
	}
		public void setApplyName(String applyName){
		setString("apply_name", applyName);
	}
	
	public String getApplyName(){
		return getString("apply_name");
	}
		public void setToUwDate(Date toUwDate){
		setUtilDate("to_uw_date", toUwDate);
	}
	
	public Date getToUwDate(){
		return getUtilDate("to_uw_date");
	}
			public void setAgentName(String agentName){
		setString("agent_name", agentName);
	}
	
	public String getAgentName(){
		return getString("agent_name");
	}
		public void setAgentCertiCode(String agentCertiCode){
		setString("agent_certi_code", agentCertiCode);
	}
	
	public String getAgentCertiCode(){
		return getString("agent_certi_code");
	}
			public void setCancelNote(String cancelNote){
		setString("cancel_note", cancelNote);
	}
	
	public String getCancelNote(){
		return getString("cancel_note");
	}
		public void setEventCode(String eventCode){
		setString("event_code", eventCode);
	}
	
	public String getEventCode(){
		return getString("event_code");
	}
		public void setAppStatus(String appStatus){
		setString("app_status", appStatus);
	}
	
	public String getAppStatus(){
		return getString("app_status");
	}
		public void setCancelId(BigDecimal cancelId){
		setBigDecimal("cancel_id", cancelId);
	}
	
	public BigDecimal getCancelId(){
		return getBigDecimal("cancel_id");
	}
		public void setNofillFlag(BigDecimal nofillFlag){
		setBigDecimal("nofill_flag", nofillFlag);
	}
	
	public BigDecimal getNofillFlag(){
		return getBigDecimal("nofill_flag");
	}
			public void setAgentTel(String agentTel){
		setString("agent_tel", agentTel);
	}
	
	public String getAgentTel(){
		return getString("agent_tel");
	}
			public void setAgentCode(String agentCode){
		setString("agent_code", agentCode);
	}
	
	public String getAgentCode(){
		return getString("agent_code");
	}
		public void setToApproveDate(Date toApproveDate){
		setUtilDate("to_approve_date", toApproveDate);
	}
	
	public Date getToApproveDate(){
		return getUtilDate("to_approve_date");
	}
	
	public String getUrgentDetail() {
		return getString("urgent_detail");
	}

	public void setUrgentDetail(String urgentDetail) {
		setString("urgent_detail",urgentDetail);
	}	
	
	public String getFaceFlag() {
		return getString("face_flag");
	}

	public void setFaceFlag(String faceFlag) {
		setString("face_flag", faceFlag);
	}

	public String getSkySign() {
		return getString("sky_sign");
	}

	public void setSkySign(String skySign) {
		setString("sky_sign", skySign);
	}

	public String getIdentityFlag() {
		return getString("identity_flag");
	}

	public void setIdentityFlag(String identityFlag) {
		setString("identity_flag", identityFlag);
	}
	public String getIsIdentityCheck() {
		return getString("is_identity_check");
	}

	public void setIsIdentityCheck(String isIdentityCheck) {
		setString("is_identity_check", isIdentityCheck);
	}

	@Override
	public String toString() {
		return "QryCsApplicationPO [getPreFlag()=" + getPreFlag()
				+ ", getApplicantType()=" + getApplicantType()
				+ ", getIsElecSign()=" + getIsElecSign()
				+ ", getAgentCertiType()=" + getAgentCertiType()
				+ ", getBillSendType()=" + getBillSendType()
				+ ", getTryCalcNo()=" + getTryCalcNo() + ", getApplyTime()="
				+ getApplyTime() + ", getCustomerId()=" + getCustomerId()
				+ ", getFinishTime()=" + getFinishTime()
				+ ", getCancelCause()=" + getCancelCause()
				+ ", getApplyCode()=" + getApplyCode() + ", getSourceType()="
				+ getSourceType() + ", getOrganCode()=" + getOrganCode()
				+ ", getChangeId()=" + getChangeId() + ", getAgentLevel()="
				+ getAgentLevel() + ", getServiceType()=" + getServiceType()
				+ ", getApplyName()=" + getApplyName() + ", getToUwDate()="
				+ getToUwDate() + ", getAgentName()=" + getAgentName()
				+ ", getAgentCertiCode()=" + getAgentCertiCode()
				+ ", getCancelNote()=" + getCancelNote() + ", getEventCode()="
				+ getEventCode() + ", getAppStatus()=" + getAppStatus()
				+ ", getCancelId()=" + getCancelId() + ", getNofillFlag()="
				+ getNofillFlag() + ", getAgentTel()=" + getAgentTel()
				+ ", getAgentCode()=" + getAgentCode()
				+ ", getToApproveDate()=" + getToApproveDate()
				+ ", getUrgentDetail()=" + getUrgentDetail()
				+ ", getFaceFlag()=" + getFaceFlag() + ", getSkySign()="
				+ getSkySign() + ", getIdentityFlag()=" + getIdentityFlag()
				+ ", getIsIdentityCheck()=" + getIsIdentityCheck() + "]";
	}	

 }
