package com.nci.tunan.qry.interfaces.model.po;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description PremArapPO对象
 * <AUTHOR> 
 * @date 2015-12-02 10:02:30  
 */
public class PremArapPO extends BasePO {
 	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// taskMark  ---  BigDecimal  ---  NUMBER_16_0_制返盘进行时，提高效率
//收付费操作，无具体业务意义;
		// policyOrganCode  ---  String  ---  VARCHAR2_20_0_保单直属机构;
		// holderName  ---  String  ---  VARCHAR2_120_0_投保人姓名;
		// isItemMain  ---  BigDecimal  ---  NUMBER_1_0_主组合产品标识;
		// bookkeepingId  ---  BigDecimal  ---  NUMBER_16_0_记账ID;
		// batchNo  ---  String  ---  VARCHAR2_20_0_批次号;
		// busiProdName  ---  String  ---  VARCHAR2_100_0_险种名称;
		// payeePhone  ---  String  ---  VARCHAR2_30_0_收付费人手机号;
		// unitNumber  ---  String  ---  VARCHAR2_20_0_应收付业务流水标识;
		// paidCount  ---  BigDecimal  ---  NUMBER_2_0_缴费次数;
		// fundsRtnCode  ---  String  ---  VARCHAR2_10_0_资金平台返回信息代码
 //E1409-付款行未开通业务
 //E1503-客户信息存在非法字符;
		// customerAccountFlag  ---  BigDecimal  ---  NUMBER_1_0_ 客户账户方案增加
//是否使用客户账户标识
//0;不使用
//1;使用;
		// feeType  ---  String  ---  VARCHAR2_10_0_费用业务类型
//32；实付退费
//41；保费收入
//参考费用类型列表;
		// seqNo  ---  BigDecimal  ---  NUMBER_16_0_制盘明细主键;
		// busiProdCode  ---  String  ---  VARCHAR2_20_0_险种代码;
		// applyCode  ---  String  ---  VARCHAR2_20_0_投保单号码;
		// feeStatusDate  ---  Date  ---  DATE_7_0_收付状态更新时间;
		// organCode  ---  String  ---  VARCHAR2_8_0_管理机构;
		// redBookkeepingTime  ---  Date  ---  DATE_7_0_红冲记录记账时间;
		// channelType  ---  String  ---  CHAR_2_0_个人、银行、团险等;
		// isBankTextDate  ---  Date  ---  DATE_7_0_制盘状态更新时间;
			// chargeYear  ---  BigDecimal  ---  NUMBER_3_0_缴费年期;
		// isRiskMain  ---  BigDecimal  ---  NUMBER_1_0_主险附加险标识;
		// isBankAccount  ---  BigDecimal  ---  NUMBER_1_0_帐号是否有效;
		// frozenStatus  ---  String  ---  CHAR_2_0_冻结状态
//0：正常
//1：已冻结
//2：已挂起;
		// posted  ---  String  ---  CHAR_2_0_记账状态;
		// bookkeepingFlag  ---  BigDecimal  ---  NUMBER_1_0_是否记账
//01；未记账
//02；已记账
//03；不可记账;
		// groupId  ---  BigDecimal  ---  NUMBER_16_0_制返盘记录表主键;
		// redBookkeepingBy  ---  BigDecimal  ---  NUMBER_10_0_红冲记账操作人;
			// frozenStatusDate  ---  Date  ---  DATE_7_0_收付状态更新时间;
		// insuredName  ---  String  ---  VARCHAR2_120_0_被保人姓名;
		// cusAccDetailsId  ---  BigDecimal  ---  NUMBER_16_0_客户账户方案增加：
//记录客户账户交易明细，以及回写账户交易明细状态。;
		// insuredId  ---  BigDecimal  ---  NUMBER_16_0_被保人编码;
		// policyType  ---  String  ---  CHAR_1_0_个单、团单;
		// productChannel  ---  String  ---  CHAR_2_0_产品渠道;
		// isBankAccountDate  ---  Date  ---  DATE_7_0_帐号有效状态更新时间;
		// policyCode  ---  String  ---  VARCHAR2_20_0_保单号码;
		// payMode  ---  String  ---  CHAR_2_0_收付方式
//10;现金
//11;现金送款薄
//20;支票
//21;现金支票
//22;转账支票
//30;银行转账
//31;银行转账（非制返盘）
//32;银行转账（制返盘）
//33;网银转账
//40;实时收费
//41;POS收款
//50;内部转账
//51;普通内部转账
//52;预存内部转账
//60;银保通
//70;第三方支付
//80;客户账户
//90;其它;
		// operatorBy  ---  BigDecimal  ---  NUMBER_10_0_业务操作人;
		// branchCode  ---  String  ---  VARCHAR2_8_0_保单所属分公司;
		// businessType  ---  String  ---  VARCHAR2_5_0_业务类型代码;
		// validateDate  ---  Date  ---  DATE_7_0_业务生效日期;
				// redBelnr  ---  String  ---  VARCHAR2_10_0_SAP红冲凭证号;
		// bankTextStatus  ---  String  ---  VARCHAR2_2_0_制盘状态
//1;待制盘
//2;制盘中
//3;返盘成功
//4;返盘失败 ;
		// groupCode  ---  String  ---  VARCHAR2_20_0_产品组合代码;
		// certiType  ---  String  ---  CHAR_2_0_收付款人证件类型;
		// cusAccUpdateBy  ---  BigDecimal  ---  NUMBER_10_0_null;
		// isBankTextBy  ---  BigDecimal  ---  NUMBER_10_0_制盘状态更新人;
		// moneyCode  ---  String  ---  CHAR_3_0_币种代码
//CNY；人民币元;
		// businessCode  ---  String  ---  VARCHAR2_20_0_投保单号、保单号、保全受理号、赔案号等。
//如果支持代理人佣金，该字段还可以保存代理人ID等等。;
		// isBankAccountBy  ---  BigDecimal  ---  NUMBER_10_0_帐号有效状态更新人;
		// payeeName  ---  String  ---  VARCHAR2_120_0_收付款人姓名;
		// bankAccount  ---  String  ---  VARCHAR2_40_0_银行账号;
		// redBookkeepingFlag  ---  BigDecimal  ---  NUMBER_1_0_红冲标识;
		// cusAccFeeAmount  ---  BigDecimal  ---  NUMBER_18_2_客户账户方案增加：
//记录使用客户账户金额;
		// customerId  ---  BigDecimal  ---  NUMBER_16_0_ 客户账户方案增加：
//收付款客户号;
		// finishTime  ---  Date  ---  DATE_7_0_业务核销时间;
			// dueTime  ---  Date  ---  DATE_7_0_应缴应付日;
		// isBankText  ---  BigDecimal  ---  NUMBER_1_0_是否允许制盘;
		// certiCode  ---  String  ---  VARCHAR2_30_0_收付款人证件号码;
		// busiApplyDate  ---  Date  ---  DATE_7_0_业务申请日;
		// groupName  ---  String  ---  VARCHAR2_200_0_产品组合名称;
		// belnr  ---  String  ---  VARCHAR2_10_0_SAP凭证号;
		// feeAmount  ---  BigDecimal  ---  NUMBER_18_2_应收付的金额;
		// listId  ---  BigDecimal  ---  NUMBER_16_0_流水号，无业务含义;
		// serviceCode  ---  String  ---  VARCHAR2_10_0_null;
		// holderId  ---  BigDecimal  ---  NUMBER_16_0_投保人编码;
		// withdrawType  ---  String  ---  VARCHAR2_10_0_记账业务类型;
		// rollbackUnitNumber  ---  String  ---  VARCHAR2_20_0_回退应收付业务流水标识;
		// failTimes  ---  BigDecimal  ---  NUMBER_2_0_制返盘失败次数;
			// policyYear  ---  BigDecimal  ---  NUMBER_4_0_年度当前年度;
		// frozenStatusBy  ---  BigDecimal  ---  NUMBER_10_0_收付状态更新人;
		// bankUserName  ---  String  ---  VARCHAR2_64_0_户名;
		// feeStatus  ---  String  ---  CHAR_2_0_收付状态
// 01；待收付
// 02；已收付
// 03；回退
// 04；中止
// 05；不可收付;
		// feeStatusBy  ---  BigDecimal  ---  NUMBER_10_0_收付状态更新人;
		// payEndDate  ---  Date  ---  DATE_7_0_宽限期止期;
		// derivType  ---  String  ---  VARCHAR2_3_0_业务来源
//001;新契约
//002;核保
//003;续保
//004;保全
//005;理赔;
		// redBookkeepingId  ---  BigDecimal  ---  NUMBER_16_0_红冲记账中间表主键ID;
		// bookkeepingBy  ---  BigDecimal  ---  NUMBER_10_0_记账操作人;
		// cusAccUpdateTime  ---  Date  ---  DATE_7_0_null;
		// arapFlag  ---  String  ---  CHAR_1_0_应收应付类型;
		// bankCode  ---  String  ---  VARCHAR2_20_0_银行编码;
		// bookkeepingTime  ---  Date  ---  DATE_7_0_记录记账时间;
		// refeflag  ---  String  ---  VARCHAR2_10_0_参考项;
		// agentCode  ---  String  ---  VARCHAR2_20_0_代理人员CODE;
		// premFreq  ---  BigDecimal  ---  NUMBER_1_0_年缴、月缴、趸交等等
//1;趸缴
//2;月缴
//3;季缴
//4;半年缴
//5;年缴
//6;不定期缴
//9;其他;
	//FEE_ID
    //Bene_Id
	
	
	public void setFeeId(BigDecimal feeId){
		setBigDecimal("fee_id",feeId);
	}
	
	public BigDecimal getFeeId(){
		return getBigDecimal("fee_id");
	}
	
	public void setBeneId(BigDecimal beneId){
		setBigDecimal("bene_id",beneId);
	}
	
	public BigDecimal getBeneId(){
		return getBigDecimal("bene_id");
	}
	
	public void setTaskMark(BigDecimal taskMark){
		setBigDecimal("task_mark", taskMark);
	}
	
	public BigDecimal getTaskMark(){
		return getBigDecimal("task_mark");
	}
		public void setPolicyOrganCode(String policyOrganCode){
		setString("policy_organ_code", policyOrganCode);
	}
	
	public String getPolicyOrganCode(){
		return getString("policy_organ_code");
	}
		public void setHolderName(String holderName){
		setString("holder_name", holderName);
	}
	
	public String getHolderName(){
		return getString("holder_name");
	}
		public void setIsItemMain(BigDecimal isItemMain){
		setBigDecimal("is_item_main", isItemMain);
	}
	
	public BigDecimal getIsItemMain(){
		return getBigDecimal("is_item_main");
	}
		public void setBookkeepingId(BigDecimal bookkeepingId){
		setBigDecimal("bookkeeping_id", bookkeepingId);
	}
	
	public BigDecimal getBookkeepingId(){
		return getBigDecimal("bookkeeping_id");
	}
		public void setBatchNo(String batchNo){
		setString("batch_no", batchNo);
	}
	
	public String getBatchNo(){
		return getString("batch_no");
	}
		public void setBusiProdName(String busiProdName){
		setString("busi_prod_name", busiProdName);
	}
	
	public String getBusiProdName(){
		return getString("busi_prod_name");
	}
		public void setPayeePhone(String payeePhone){
		setString("payee_phone", payeePhone);
	}
	
	public String getPayeePhone(){
		return getString("payee_phone");
	}
		public void setUnitNumber(String unitNumber){
		setString("unit_number", unitNumber);
	}
	
	public String getUnitNumber(){
		return getString("unit_number");
	}
		public void setPaidCount(BigDecimal paidCount){
		setBigDecimal("paid_count", paidCount);
	}
	
	public BigDecimal getPaidCount(){
		return getBigDecimal("paid_count");
	}
		public void setFundsRtnCode(String fundsRtnCode){
		setString("funds_rtn_code", fundsRtnCode);
	}
	
	public String getFundsRtnCode(){
		return getString("funds_rtn_code");
	}
		public void setCustomerAccountFlag(BigDecimal customerAccountFlag){
		setBigDecimal("customer_account_flag", customerAccountFlag);
	}
	
	public BigDecimal getCustomerAccountFlag(){
		return getBigDecimal("customer_account_flag");
	}
		public void setFeeType(String feeType){
		setString("fee_type", feeType);
	}
	
	public String getFeeType(){
		return getString("fee_type");
	}
		public void setSeqNo(BigDecimal seqNo){
		setBigDecimal("seq_no", seqNo);
	}
	
	public BigDecimal getSeqNo(){
		return getBigDecimal("seq_no");
	}
		public void setBusiProdCode(String busiProdCode){
		setString("busi_prod_code", busiProdCode);
	}
	
	public String getBusiProdCode(){
		return getString("busi_prod_code");
	}
		public void setApplyCode(String applyCode){
		setString("apply_code", applyCode);
	}
	
	public String getApplyCode(){
		return getString("apply_code");
	}
		public void setFeeStatusDate(Date feeStatusDate){
		setUtilDate("fee_status_date", feeStatusDate);
	}
	
	public Date getFeeStatusDate(){
		return getUtilDate("fee_status_date");
	}
		public void setOrganCode(String organCode){
		setString("organ_code", organCode);
	}
	
	public String getOrganCode(){
		return getString("organ_code");
	}
		public void setRedBookkeepingTime(Date redBookkeepingTime){
		setUtilDate("red_bookkeeping_time", redBookkeepingTime);
	}
	
	public Date getRedBookkeepingTime(){
		return getUtilDate("red_bookkeeping_time");
	}
		public void setChannelType(String channelType){
		setString("channel_type", channelType);
	}
	
	public String getChannelType(){
		return getString("channel_type");
	}
		public void setIsBankTextDate(Date isBankTextDate){
		setUtilDate("is_bank_text_date", isBankTextDate);
	}
	
	public Date getIsBankTextDate(){
		return getUtilDate("is_bank_text_date");
	}
			public void setChargeYear(BigDecimal chargeYear){
		setBigDecimal("charge_year", chargeYear);
	}
	
	public BigDecimal getChargeYear(){
		return getBigDecimal("charge_year");
	}
		public void setIsRiskMain(BigDecimal isRiskMain){
		setBigDecimal("is_risk_main", isRiskMain);
	}
	
	public BigDecimal getIsRiskMain(){
		return getBigDecimal("is_risk_main");
	}
		public void setIsBankAccount(BigDecimal isBankAccount){
		setBigDecimal("is_bank_account", isBankAccount);
	}
	
	public BigDecimal getIsBankAccount(){
		return getBigDecimal("is_bank_account");
	}
		public void setFrozenStatus(String frozenStatus){
		setString("frozen_status", frozenStatus);
	}
	
	public String getFrozenStatus(){
		return getString("frozen_status");
	}
		public void setPosted(String posted){
		setString("posted", posted);
	}
	
	public String getPosted(){
		return getString("posted");
	}
		public void setBookkeepingFlag(BigDecimal bookkeepingFlag){
		setBigDecimal("bookkeeping_flag", bookkeepingFlag);
	}
	
	public BigDecimal getBookkeepingFlag(){
		return getBigDecimal("bookkeeping_flag");
	}
		public void setGroupId(BigDecimal groupId){
		setBigDecimal("group_id", groupId);
	}
	
	public BigDecimal getGroupId(){
		return getBigDecimal("group_id");
	}
		public void setRedBookkeepingBy(BigDecimal redBookkeepingBy){
		setBigDecimal("red_bookkeeping_by", redBookkeepingBy);
	}
	
	public BigDecimal getRedBookkeepingBy(){
		return getBigDecimal("red_bookkeeping_by");
	}
			public void setFrozenStatusDate(Date frozenStatusDate){
		setUtilDate("frozen_status_date", frozenStatusDate);
	}
	
	public Date getFrozenStatusDate(){
		return getUtilDate("frozen_status_date");
	}
		public void setInsuredName(String insuredName){
		setString("insured_name", insuredName);
	}
	
	public String getInsuredName(){
		return getString("insured_name");
	}
		public void setCusAccDetailsId(BigDecimal cusAccDetailsId){
		setBigDecimal("cus_acc_details_id", cusAccDetailsId);
	}
	
	public BigDecimal getCusAccDetailsId(){
		return getBigDecimal("cus_acc_details_id");
	}
		public void setInsuredId(BigDecimal insuredId){
		setBigDecimal("insured_id", insuredId);
	}
	
	public BigDecimal getInsuredId(){
		return getBigDecimal("insured_id");
	}
		public void setPolicyType(String policyType){
		setString("policy_type", policyType);
	}
	
	public String getPolicyType(){
		return getString("policy_type");
	}
		public void setProductChannel(String productChannel){
		setString("product_channel", productChannel);
	}
	
	public String getProductChannel(){
		return getString("product_channel");
	}
		public void setIsBankAccountDate(Date isBankAccountDate){
		setUtilDate("is_bank_account_date", isBankAccountDate);
	}
	
	public Date getIsBankAccountDate(){
		return getUtilDate("is_bank_account_date");
	}
		public void setPolicyCode(String policyCode){
		setString("policy_code", policyCode);
	}
	
	public String getPolicyCode(){
		return getString("policy_code");
	}
		public void setPayMode(String payMode){
		setString("pay_mode", payMode);
	}
	
	public String getPayMode(){
		return getString("pay_mode");
	}
		public void setOperatorBy(BigDecimal operatorBy){
		setBigDecimal("operator_by", operatorBy);
	}
	
	public BigDecimal getOperatorBy(){
		return getBigDecimal("operator_by");
	}
		public void setBranchCode(String branchCode){
		setString("branch_code", branchCode);
	}
	
	public String getBranchCode(){
		return getString("branch_code");
	}
		public void setBusinessType(String businessType){
		setString("business_type", businessType);
	}
	
	public String getBusinessType(){
		return getString("business_type");
	}
		public void setValidateDate(Date validateDate){
		setUtilDate("validate_date", validateDate);
	}
	
	public Date getValidateDate(){
		return getUtilDate("validate_date");
	}
				public void setRedBelnr(String redBelnr){
		setString("red_belnr", redBelnr);
	}
	
	public String getRedBelnr(){
		return getString("red_belnr");
	}
		public void setBankTextStatus(String bankTextStatus){
		setString("bank_text_status", bankTextStatus);
	}
	
	public String getBankTextStatus(){
		return getString("bank_text_status");
	}
		public void setGroupCode(String groupCode){
		setString("group_code", groupCode);
	}
	
	public String getGroupCode(){
		return getString("group_code");
	}
		public void setCertiType(String certiType){
		setString("certi_type", certiType);
	}
	
	public String getCertiType(){
		return getString("certi_type");
	}
		public void setCusAccUpdateBy(BigDecimal cusAccUpdateBy){
		setBigDecimal("cus_acc_update_by", cusAccUpdateBy);
	}
	
	public BigDecimal getCusAccUpdateBy(){
		return getBigDecimal("cus_acc_update_by");
	}
		public void setIsBankTextBy(BigDecimal isBankTextBy){
		setBigDecimal("is_bank_text_by", isBankTextBy);
	}
	
	public BigDecimal getIsBankTextBy(){
		return getBigDecimal("is_bank_text_by");
	}
		public void setMoneyCode(String moneyCode){
		setString("money_code", moneyCode);
	}
	
	public String getMoneyCode(){
		return getString("money_code");
	}
		public void setBusinessCode(String businessCode){
		setString("business_code", businessCode);
	}
	
	public String getBusinessCode(){
		return getString("business_code");
	}
		public void setIsBankAccountBy(BigDecimal isBankAccountBy){
		setBigDecimal("is_bank_account_by", isBankAccountBy);
	}
	
	public BigDecimal getIsBankAccountBy(){
		return getBigDecimal("is_bank_account_by");
	}
		public void setPayeeName(String payeeName){
		setString("payee_name", payeeName);
	}
	
	public String getPayeeName(){
		return getString("payee_name");
	}
		public void setBankAccount(String bankAccount){
		setString("bank_account", bankAccount);
	}
	
	public String getBankAccount(){
		return getString("bank_account");
	}
		public void setRedBookkeepingFlag(BigDecimal redBookkeepingFlag){
		setBigDecimal("red_bookkeeping_flag", redBookkeepingFlag);
	}
	
	public BigDecimal getRedBookkeepingFlag(){
		return getBigDecimal("red_bookkeeping_flag");
	}
		public void setCusAccFeeAmount(BigDecimal cusAccFeeAmount){
		setBigDecimal("cus_acc_fee_amount", cusAccFeeAmount);
	}
	
	public BigDecimal getCusAccFeeAmount(){
		return getBigDecimal("cus_acc_fee_amount");
	}
		public void setCustomerId(BigDecimal customerId){
		setBigDecimal("customer_id", customerId);
	}
	
	public BigDecimal getCustomerId(){
		return getBigDecimal("customer_id");
	}
		public void setFinishTime(Date finishTime){
		setUtilDate("finish_time", finishTime);
	}
	
	public Date getFinishTime(){
		return getUtilDate("finish_time");
	}
			public void setDueTime(Date dueTime){
		setUtilDate("due_time", dueTime);
	}
	
	public Date getDueTime(){
		return getUtilDate("due_time");
	}
		public void setIsBankText(BigDecimal isBankText){
		setBigDecimal("is_bank_text", isBankText);
	}
	
	public BigDecimal getIsBankText(){
		return getBigDecimal("is_bank_text");
	}
		public void setCertiCode(String certiCode){
		setString("certi_code", certiCode);
	}
	
	public String getCertiCode(){
		return getString("certi_code");
	}
		public void setBusiApplyDate(Date busiApplyDate){
		setUtilDate("busi_apply_date", busiApplyDate);
	}
	
	public Date getBusiApplyDate(){
		return getUtilDate("busi_apply_date");
	}
		public void setGroupName(String groupName){
		setString("group_name", groupName);
	}
	
	public String getGroupName(){
		return getString("group_name");
	}
		public void setBelnr(String belnr){
		setString("belnr", belnr);
	}
	
	public String getBelnr(){
		return getString("belnr");
	}
		public void setFeeAmount(BigDecimal feeAmount){
		setBigDecimal("fee_amount", feeAmount);
	}
	
	public BigDecimal getFeeAmount(){
		return getBigDecimal("fee_amount");
	}
		public void setListId(BigDecimal listId){
		setBigDecimal("list_id", listId);
	}
	
	public BigDecimal getListId(){
		return getBigDecimal("list_id");
	}
		public void setServiceCode(String serviceCode){
		setString("service_code", serviceCode);
	}
	
	public String getServiceCode(){
		return getString("service_code");
	}
		public void setHolderId(BigDecimal holderId){
		setBigDecimal("holder_id", holderId);
	}
	
	public BigDecimal getHolderId(){
		return getBigDecimal("holder_id");
	}
		public void setWithdrawType(String withdrawType){
		setString("withdraw_type", withdrawType);
	}
	
	public String getWithdrawType(){
		return getString("withdraw_type");
	}
		public void setRollbackUnitNumber(String rollbackUnitNumber){
		setString("rollback_unit_number", rollbackUnitNumber);
	}
	
	public String getRollbackUnitNumber(){
		return getString("rollback_unit_number");
	}
		public void setFailTimes(BigDecimal failTimes){
		setBigDecimal("fail_times", failTimes);
	}
	
	public BigDecimal getFailTimes(){
		return getBigDecimal("fail_times");
	}
			public void setPolicyYear(BigDecimal policyYear){
		setBigDecimal("policy_year", policyYear);
	}
	
	public BigDecimal getPolicyYear(){
		return getBigDecimal("policy_year");
	}
		public void setFrozenStatusBy(BigDecimal frozenStatusBy){
		setBigDecimal("frozen_status_by", frozenStatusBy);
	}
	
	public BigDecimal getFrozenStatusBy(){
		return getBigDecimal("frozen_status_by");
	}
		public void setBankUserName(String bankUserName){
		setString("bank_user_name", bankUserName);
	}
	
	public String getBankUserName(){
		return getString("bank_user_name");
	}
		public void setFeeStatus(String feeStatus){
		setString("fee_status", feeStatus);
	}
	
	public String getFeeStatus(){
		return getString("fee_status");
	}
		public void setFeeStatusBy(BigDecimal feeStatusBy){
		setBigDecimal("fee_status_by", feeStatusBy);
	}
	
	public BigDecimal getFeeStatusBy(){
		return getBigDecimal("fee_status_by");
	}
		public void setPayEndDate(Date payEndDate){
		setUtilDate("pay_end_date", payEndDate);
	}
	
	public Date getPayEndDate(){
		return getUtilDate("pay_end_date");
	}
		public void setDerivType(String derivType){
		setString("deriv_type", derivType);
	}
	
	public String getDerivType(){
		return getString("deriv_type");
	}
		public void setRedBookkeepingId(BigDecimal redBookkeepingId){
		setBigDecimal("red_bookkeeping_id", redBookkeepingId);
	}
	
	public BigDecimal getRedBookkeepingId(){
		return getBigDecimal("red_bookkeeping_id");
	}
		public void setBookkeepingBy(BigDecimal bookkeepingBy){
		setBigDecimal("bookkeeping_by", bookkeepingBy);
	}
	
	public BigDecimal getBookkeepingBy(){
		return getBigDecimal("bookkeeping_by");
	}
		public void setCusAccUpdateTime(Date cusAccUpdateTime){
		setUtilDate("cus_acc_update_time", cusAccUpdateTime);
	}
	
	public Date getCusAccUpdateTime(){
		return getUtilDate("cus_acc_update_time");
	}
		public void setArapFlag(String arapFlag){
		setString("arap_flag", arapFlag);
	}
	
	public String getArapFlag(){
		return getString("arap_flag");
	}
		public void setBankCode(String bankCode){
		setString("bank_code", bankCode);
	}
	
	public String getBankCode(){
		return getString("bank_code");
	}
		public void setBookkeepingTime(Date bookkeepingTime){
		setUtilDate("bookkeeping_time", bookkeepingTime);
	}
	
	public Date getBookkeepingTime(){
		return getUtilDate("bookkeeping_time");
	}
		public void setRefeflag(String refeflag){
		setString("refeflag", refeflag);
	}
	
	public String getRefeflag(){
		return getString("refeflag");
	}
		public void setAgentCode(String agentCode){
		setString("agent_code", agentCode);
	}
	
	public String getAgentCode(){
		return getString("agent_code");
	}
		public void setPremFreq(BigDecimal premFreq){
		setBigDecimal("prem_freq", premFreq);
	}
	
	public BigDecimal getPremFreq(){
		return getBigDecimal("prem_freq");
	}
		
	@Override
    public String toString() {
        return "PremArapPO [" +
        		"feeId="+getFeeId()+","+
        		"beneId="+getBeneId()+","+
				"taskMark="+getTaskMark()+","+
"policyOrganCode="+getPolicyOrganCode()+","+
"holderName="+getHolderName()+","+
"isItemMain="+getIsItemMain()+","+
"bookkeepingId="+getBookkeepingId()+","+
"batchNo="+getBatchNo()+","+
"busiProdName="+getBusiProdName()+","+
"payeePhone="+getPayeePhone()+","+
"unitNumber="+getUnitNumber()+","+
"paidCount="+getPaidCount()+","+
"fundsRtnCode="+getFundsRtnCode()+","+
"customerAccountFlag="+getCustomerAccountFlag()+","+
"feeType="+getFeeType()+","+
"seqNo="+getSeqNo()+","+
"busiProdCode="+getBusiProdCode()+","+
"applyCode="+getApplyCode()+","+
"feeStatusDate="+getFeeStatusDate()+","+
"organCode="+getOrganCode()+","+
"redBookkeepingTime="+getRedBookkeepingTime()+","+
"channelType="+getChannelType()+","+
"isBankTextDate="+getIsBankTextDate()+","+
"chargeYear="+getChargeYear()+","+
"isRiskMain="+getIsRiskMain()+","+
"isBankAccount="+getIsBankAccount()+","+
"frozenStatus="+getFrozenStatus()+","+
"posted="+getPosted()+","+
"bookkeepingFlag="+getBookkeepingFlag()+","+
"groupId="+getGroupId()+","+
"redBookkeepingBy="+getRedBookkeepingBy()+","+
"frozenStatusDate="+getFrozenStatusDate()+","+
"insuredName="+getInsuredName()+","+
"cusAccDetailsId="+getCusAccDetailsId()+","+
"insuredId="+getInsuredId()+","+
"policyType="+getPolicyType()+","+
"productChannel="+getProductChannel()+","+
"isBankAccountDate="+getIsBankAccountDate()+","+
"policyCode="+getPolicyCode()+","+
"payMode="+getPayMode()+","+
"operatorBy="+getOperatorBy()+","+
"branchCode="+getBranchCode()+","+
"businessType="+getBusinessType()+","+
"validateDate="+getValidateDate()+","+
"redBelnr="+getRedBelnr()+","+
"bankTextStatus="+getBankTextStatus()+","+
"groupCode="+getGroupCode()+","+
"certiType="+getCertiType()+","+
"cusAccUpdateBy="+getCusAccUpdateBy()+","+
"isBankTextBy="+getIsBankTextBy()+","+
"moneyCode="+getMoneyCode()+","+
"businessCode="+getBusinessCode()+","+
"isBankAccountBy="+getIsBankAccountBy()+","+
"payeeName="+getPayeeName()+","+
"bankAccount="+getBankAccount()+","+
"redBookkeepingFlag="+getRedBookkeepingFlag()+","+
"cusAccFeeAmount="+getCusAccFeeAmount()+","+
"customerId="+getCustomerId()+","+
"finishTime="+getFinishTime()+","+
"dueTime="+getDueTime()+","+
"isBankText="+getIsBankText()+","+
"certiCode="+getCertiCode()+","+
"busiApplyDate="+getBusiApplyDate()+","+
"groupName="+getGroupName()+","+
"belnr="+getBelnr()+","+
"feeAmount="+getFeeAmount()+","+
"listId="+getListId()+","+
"serviceCode="+getServiceCode()+","+
"holderId="+getHolderId()+","+
"withdrawType="+getWithdrawType()+","+
"rollbackUnitNumber="+getRollbackUnitNumber()+","+
"failTimes="+getFailTimes()+","+
"policyYear="+getPolicyYear()+","+
"frozenStatusBy="+getFrozenStatusBy()+","+
"bankUserName="+getBankUserName()+","+
"feeStatus="+getFeeStatus()+","+
"feeStatusBy="+getFeeStatusBy()+","+
"payEndDate="+getPayEndDate()+","+
"derivType="+getDerivType()+","+
"redBookkeepingId="+getRedBookkeepingId()+","+
"bookkeepingBy="+getBookkeepingBy()+","+
"cusAccUpdateTime="+getCusAccUpdateTime()+","+
"arapFlag="+getArapFlag()+","+
"bankCode="+getBankCode()+","+
"bookkeepingTime="+getBookkeepingTime()+","+
"refeflag="+getRefeflag()+","+
"agentCode="+getAgentCode()+","+
"premFreq="+getPremFreq()+"]";
    }	
 }
