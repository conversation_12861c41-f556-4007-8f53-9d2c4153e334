package com.nci.tunan.qry.interfaces.model.po;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BasePO;

/**
 * @description 综合查询组合PO---业务综合查询页面
 * <AUTHOR> <EMAIL>
 * @date 2015-2-27 上午10:51:15
 */
public class QryCommonQueryCompPO extends BasePO {

	/**
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
	 */
	private static final long serialVersionUID = 1L;
	public String getQueryMapper() {
		return getString("queryMapper");
	}

	public void setQueryMapper(String queryMapper) {
		setString("querySql", queryMapper);
	}
	public void setApplyCode(String applyCode) {
		setString("apply_code", applyCode);
	}

	public String getApplyCode() {
		return getString("apply_code");
	}

	public void setPolicyId(BigDecimal policyId) {
		setBigDecimal("policy_id", policyId);
	}

	public BigDecimal getPolicyId() {
		return getBigDecimal("policy_id");
	}

	public void setServiceBankBranch(String serviceBankBranch) {
		setString("service_bank_branch", serviceBankBranch);
	}

	public String getServiceBankBranch() {
		return getString("service_bank_branch");
	}

	public void setServiceAgent(String serviceAgent) {
		setString("service_agent", serviceAgent);
	}

	public String getServiceAgent() {
		return getString("service_agent");
	}

	public void setLiabilityState(BigDecimal liabilityState) {
		setBigDecimal("liability_state", liabilityState);
	}

	public BigDecimal getLiabilityState() {
		return getBigDecimal("liability_state");
	}

	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}

	public String getPolicyCode() {
		return getString("policy_code");
	}

	public void setCustomerId(BigDecimal customerId) {
		setBigDecimal("customer_id", customerId);
	}

	public BigDecimal getCustomerId() {
		return getBigDecimal("customer_id");
	}

	public void setAccount(String account) {
		setString("account", account);
	}

	public String getAccount() {
		return getString("account");
	}
	
	//生调标识
	public void setsurvivalInvestFlag(BigDecimal survivalInvestFlag){
		setBigDecimal("survival_invest_flag",survivalInvestFlag);
	}
	public BigDecimal getsurvivalInvestFlag(){
		return getBigDecimal("survival_invest_flag");
	}
	
	public void setSessionId(String sessionId) {
		setString("sessionid", sessionId);
	}

	public String getSessionId() {
		return getString("sessionid");
	}
	
	public String getAcceptCode() {
        return getString("accept_code");
    }

    public void setAcceptCode(String acceptCode) {
    	setString("accept_code",acceptCode);
    }
    
    public String getServiceCode() {
        return getString("service_code");
    }

    public void setServiceCode(String serviceCode) {
    	setString("service_code",serviceCode);
    }
    
	public BigDecimal getAcceptId() {
		return getBigDecimal("accept_id");
	}

	public void setAcceptId(BigDecimal acceptId) {
		setBigDecimal("accept_id",acceptId);
	}
	
	public BigDecimal getChangeId() {
		return getBigDecimal("change_id");
	}

	public void setChangeId(BigDecimal changeId) {
		setBigDecimal("change_id",changeId);
	}
	
	public String getStandardSign() {
		return getString("standardSign");
	}

	public void setStandardSign(String standardSign) {
		setString("standard_sign",standardSign);
	}

    public String getMedicalPayOrder(){
    	return getString("medical_pay_order");
    }
    public void setMedicalPayOrder(String medicalPayOrder){
    	setString("medical_pay_order", medicalPayOrder);
    }
    public String getMedicalPayOrderName(){
    	return getString("medical_pay_order_name");
    }
    public void setMedicalPayOrderName(String medicalPayOrderName){
    	setString("medical_pay_order_name", medicalPayOrderName);
    }
    public String getMedicalNo(){
    	return getString ("medical_no");
    }
    public void setMedicalNo(String medicalNo){
    	setString("medical_no", medicalNo);
    }
    public String getAccountBank(){
    	return getString ("account_bank");
    }
    public void setAccountBank(String accountBank){
    	setString("account_bank", accountBank);
    }
    public String getAccountBankName(){
    	return getString ("account_bank_name");
    }
    public void setAccountBankName(String accountBankName){
    	setString("account_bank_name", accountBankName);
    }
    public String getAccountName(){
    	return getString ("account_name");
    }
    public void setAccountName(String accountName){
    	setString("account_name", accountName);
    }
    public String getAccountId(){
    	return getString ("account_id");
    }
    

    public String getMedicalPayOrderNext(){
    	return getString ("medical_pay_order_next");
    }
    public void setMedicalNoNext(String medicalNoNext){
    	setString("medical_no_next", medicalNoNext);
    }
    
	public String getTaxResidentType() {
		return getString("tax_resident_type");
	}
    public void setAccountId(String accountId){
    	setString("account_id", accountId);
    }
    public void setMedicalPayOrderNext(String medicalPayOrderNext){
    	setString("medical_pay_order_next", medicalPayOrderNext);
    }
    public String getMedicalPayOrderNameNext(){
    	return getString ("medical_pay_order_name_next");
    }
    public void setMedicalPayOrderNameNext(String medicalPayOrderNameNext){
    	setString("medical_pay_order_name_next", medicalPayOrderNameNext);
    }
    public String getMedicalNoNext(){
    	return getString ("medical_no_next");
    }
	public void setTaxResidentType(String taxResidentType) {
		setString("tax_resident_type",taxResidentType);
	}
    public String getNextAccountBank(){
    	return getString ("next_account_bank");
    }
    public void setNextAccountBank(String nextAccountBank){
    	setString("next_account_bank", nextAccountBank);
    }
    public String getNextAccountBankName(){
    	return getString ("next_account_bank_name");
    }
    public void setNextAccountBankName(String nextAccountBankName){
    	setString("next_account_bank_name", nextAccountBankName);
    }
    public String getNextAccountName(){
    	return getString ("next_account_name");
    }
    public void setNextAccountName(String nextAccountName){
    	setString("next_account_name", nextAccountName);
    }
    public String getNextAccountId(){
    	return getString ("next_account_id");
    }
    public void setNextAccountId(String nextAccountId){
    	setString("next_account_id", nextAccountId);
    }
	public String getPhone() {
		return getString("phone");
	}

	public void setPhone(String phone) {
		setString("phone",phone);
	}

	public String getOfficeTel() {
		return getString("office_tel");
	}

	public void setOfficeTel(String officeTel) {
		setString("office_tel",officeTel);
	}

	public String getEmail() {
		return getString("email");
	}

	public void setEmail(String email) {
		setString("email",email);
	}
	
	public String getRealName() {
		return getString("real_name");
	}

	public void setRealName(String realName) {
		setString("real_name",realName);
	}

}
