package com.nci.tunan.qry.interfaces.model.po;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description ContractProductPO对象
 * <AUTHOR> 
 * @date 2016-11-09 14:20:50  
 */
public class ContractProductPO extends BasePO {
    /** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
    // isPause  ---  BigDecimal  ---  NUMBER_1_0_暂缓期结束标志;
        // discntedPremAf  ---  BigDecimal  ---  NUMBER_18_2_折扣后保费;
        // masterProductCode  ---  String  ---  VARCHAR2_20_0_主险种责任组所属的精算产品代码;
        // prodPkgPlanCode  ---  String  ---  VARCHAR2_20_0_产品组合计划;
        // appendPremAf  ---  BigDecimal  ---  NUMBER_18_2_null;
        // isWaived  ---  BigDecimal  ---  NUMBER_1_0_null;
        // interestMode  ---  BigDecimal  ---  NUMBER_1_0_null;
        // applyCode  ---  String  ---  VARCHAR2_20_0_投保单号;
        // norenewReason  ---  String  ---  VARCHAR2_2_0_不续保原因;
        // organCode  ---  String  ---  VARCHAR2_8_0_保单管理机构;
            // chargeYear  ---  BigDecimal  ---  NUMBER_10_0_缴费年期;
        // extraPremAf  ---  BigDecimal  ---  NUMBER_18_2_加费;
        // policyId  ---  BigDecimal  ---  NUMBER_16_0_保单ID;
        // renewalDiscntedExtraPremAf  ---  BigDecimal  ---  NUMBER_18_2_续期折扣后加费;
        // initialAmount  ---  BigDecimal  ---  NUMBER_18_2_null;
        // ccSa  ---  BigDecimal  ---  NUMBER_18_2_累积利差保额;
        // initialExtraPremAf  ---  BigDecimal  ---  NUMBER_18_2_null;
            // payoutRate  ---  BigDecimal  ---  NUMBER_7_6_null;
        // amount  ---  BigDecimal  ---  NUMBER_18_2_客户投保金额（元）;
        // masterProductId  ---  BigDecimal  ---  NUMBER_7_0_主险种责任组所属的精算产品ID;
        // masterItemId  ---  BigDecimal  ---  NUMBER_10_0_所属的必选险种责任组ID;
        // paidupDate  ---  Date  ---  DATE_7_0_null;
        // caseId  ---  BigDecimal  ---  NUMBER_16_0_赔案ID;
        // expiryDate  ---  Date  ---  DATE_7_0_责任终止日期;
        // payYear  ---  BigDecimal  ---  NUMBER_10_0_领取年期;
        // payPeriod  ---  String  ---  CHAR_1_0_领取年期类型;
        // liabilityState  ---  BigDecimal  ---  NUMBER_2_0_责任状态;
        // copyDate  ---  Date  ---  DATE_7_0_理赔抄单基准日;
        // countWay  ---  String  ---  CHAR_1_0_保费计算方向;
        // policyCode  ---  String  ---  VARCHAR2_20_0_保单号码;
        // isGift  ---  BigDecimal  ---  NUMBER_1_0_赠险标记;
        // rerinstateDate  ---  Date  ---  DATE_7_0_复效日期;
        // additionalPremAf  ---  BigDecimal  ---  NUMBER_18_2_null;
        // renewDecision  ---  BigDecimal  ---  NUMBER_1_0_续保的核保决定;
        // validateDate  ---  Date  ---  DATE_7_0_责任生效日期;
        // bonusModeCode  ---  BigDecimal  ---  NUMBER_1_0_红利领取方式;
                // benefitLevel  ---  String  ---  VARCHAR2_20_0_投保档次,不同于投保等级;
        // uwId  ---  BigDecimal  ---  NUMBER_16_0_null;
        // productId  ---  BigDecimal  ---  NUMBER_16_0_所属的精算产品ID;
        // bonusSa  ---  BigDecimal  ---  NUMBER_18_2_累计红利保额;
        // productCode  ---  String  ---  VARCHAR2_20_0_精算产品代码;
        // applyDate  ---  Date  ---  DATE_7_0_投保日期;
        // interestFlag  ---  BigDecimal  ---  NUMBER_1_0_null;
        // coveragePeriod  ---  String  ---  CHAR_1_0_保障年期类型;
        // itemId  ---  BigDecimal  ---  NUMBER_16_0_责任组ID;
        // renewalExtraPremAf  ---  BigDecimal  ---  NUMBER_18_2_null;
        // initialDiscntedExtraPremAf  ---  BigDecimal  ---  NUMBER_18_2_首期折扣后加费;
            // waiverEnd  ---  Date  ---  DATE_7_0_null;
        // maturityDate  ---  Date  ---  DATE_7_0_null;
        // busiItemId  ---  BigDecimal  ---  NUMBER_16_0_所属业务险种ID;
        // healthServiceFlag  ---  BigDecimal  ---  NUMBER_1_0_健康服务标志;
        // lapseDate  ---  Date  ---  DATE_7_0_失效日期;
        // initialType  ---  BigDecimal  ---  NUMBER_1_0_当期缴费频率;
        // payFreq  ---  String  ---  CHAR_1_0_null;
        // unit  ---  BigDecimal  ---  NUMBER_18_2_投保份数;
        // coverageYear  ---  BigDecimal  ---  NUMBER_10_0_保障年期;
            // endCause  ---  String  ---  CHAR_2_0_责任终止原因;
        // lapseCause  ---  String  ---  VARCHAR2_2_0_失效原因;
        // renewalDiscntedPremAf  ---  BigDecimal  ---  NUMBER_18_2_续期折扣后保费;
        // totalPremAf  ---  BigDecimal  ---  NUMBER_18_2_总保费;
        // decisionCode  ---  String  ---  CHAR_2_0_核保决定;
        // pauseDate  ---  Date  ---  DATE_7_0_暂缓期结束日期;
        // logId  ---  BigDecimal  ---  NUMBER_16_0_主键流水号;
        // spreadsMode  ---  BigDecimal  ---  NUMBER_1_0_利差领取方式;
        // chargePeriod  ---  String  ---  VARCHAR2_2_0_缴费年期类型;
        // stdPremAf  ---  BigDecimal  ---  NUMBER_18_2_标准保费;
        // isMasterItem  ---  BigDecimal  ---  NUMBER_1_0_是否必选责任组;
        // uwListId  ---  BigDecimal  ---  NUMBER_16_0_null;
        // suspendDate  ---  Date  ---  DATE_7_0_null;
        // suspendCause  ---  String  ---  CHAR_2_0_null;
        // deductibleFranchise  ---  BigDecimal  ---  NUMBER_18_2_null;
        // waiverStart  ---  Date  ---  DATE_7_0_null;
        // premFreq  ---  BigDecimal  ---  NUMBER_1_0_null;
        // initialDiscntPremAf  ---  BigDecimal  ---  NUMBER_18_2_首期折扣后保费;
        // curFlag  ---  BigDecimal  ---  NUMBER_1_0_当前抄单标记;
    
        public void setBonusWMode(BigDecimal bonusWMode){
        setBigDecimal("bonus_w_mode", bonusWMode);
    }
    
    public BigDecimal getBonusWMode(){
        return getBigDecimal("bonus_w_mode");
    }
    public void setIsPause(BigDecimal isPause){
        setBigDecimal("is_pause", isPause);
    }
    
    public BigDecimal getIsPause(){
        return getBigDecimal("is_pause");
    }
        public void setDiscntedPremAf(BigDecimal discntedPremAf){
        setBigDecimal("discnted_prem_af", discntedPremAf);
    }
    
    public BigDecimal getDiscntedPremAf(){
        return getBigDecimal("discnted_prem_af");
    }
        public void setMasterProductCode(String masterProductCode){
        setString("master_product_code", masterProductCode);
    }
    
    public String getMasterProductCode(){
        return getString("master_product_code");
    }
        public void setProdPkgPlanCode(String prodPkgPlanCode){
        setString("prod_pkg_plan_code", prodPkgPlanCode);
    }
    
    public String getProdPkgPlanCode(){
        return getString("prod_pkg_plan_code");
    }
        public void setAppendPremAf(BigDecimal appendPremAf){
        setBigDecimal("append_prem_af", appendPremAf);
    }
    
    public BigDecimal getAppendPremAf(){
        return getBigDecimal("append_prem_af");
    }
        public void setIsWaived(BigDecimal isWaived){
        setBigDecimal("is_waived", isWaived);
    }
    
    public BigDecimal getIsWaived(){
        return getBigDecimal("is_waived");
    }
        public void setInterestMode(BigDecimal interestMode){
        setBigDecimal("interest_mode", interestMode);
    }
    
    public BigDecimal getInterestMode(){
        return getBigDecimal("interest_mode");
    }
        public void setApplyCode(String applyCode){
        setString("apply_code", applyCode);
    }
    
    public String getApplyCode(){
        return getString("apply_code");
    }
        public void setNorenewReason(String norenewReason){
        setString("norenew_reason", norenewReason);
    }
    
    public String getNorenewReason(){
        return getString("norenew_reason");
    }
        public void setOrganCode(String organCode){
        setString("organ_code", organCode);
    }
    
    public String getOrganCode(){
        return getString("organ_code");
    }
            public void setChargeYear(BigDecimal chargeYear){
        setBigDecimal("charge_year", chargeYear);
    }
    
    public BigDecimal getChargeYear(){
        return getBigDecimal("charge_year");
    }
        public void setExtraPremAf(BigDecimal extraPremAf){
        setBigDecimal("extra_prem_af", extraPremAf);
    }
    
    public BigDecimal getExtraPremAf(){
        return getBigDecimal("extra_prem_af");
    }
        public void setPolicyId(BigDecimal policyId){
        setBigDecimal("policy_id", policyId);
    }
    
    public BigDecimal getPolicyId(){
        return getBigDecimal("policy_id");
    }
        public void setRenewalDiscntedExtraPremAf(BigDecimal renewalDiscntedExtraPremAf){
        setBigDecimal("renewal_discnted_extra_prem_af", renewalDiscntedExtraPremAf);
    }
    
    public BigDecimal getRenewalDiscntedExtraPremAf(){
        return getBigDecimal("renewal_discnted_extra_prem_af");
    }
        public void setInitialAmount(BigDecimal initialAmount){
        setBigDecimal("initial_amount", initialAmount);
    }
    
    public BigDecimal getInitialAmount(){
        return getBigDecimal("initial_amount");
    }
        public void setCcSa(BigDecimal ccSa){
        setBigDecimal("cc_sa", ccSa);
    }
    
    public BigDecimal getCcSa(){
        return getBigDecimal("cc_sa");
    }
        public void setInitialExtraPremAf(BigDecimal initialExtraPremAf){
        setBigDecimal("initial_extra_prem_af", initialExtraPremAf);
    }
    
    public BigDecimal getInitialExtraPremAf(){
        return getBigDecimal("initial_extra_prem_af");
    }
            public void setPayoutRate(BigDecimal payoutRate){
        setBigDecimal("payout_rate", payoutRate);
    }
    
    public BigDecimal getPayoutRate(){
        return getBigDecimal("payout_rate");
    }
        public void setAmount(BigDecimal amount){
        setBigDecimal("amount", amount);
    }
    
    public BigDecimal getAmount(){
        return getBigDecimal("amount");
    }
        public void setMasterProductId(BigDecimal masterProductId){
        setBigDecimal("master_product_id", masterProductId);
    }
    
    public BigDecimal getMasterProductId(){
        return getBigDecimal("master_product_id");
    }
        public void setMasterItemId(BigDecimal masterItemId){
        setBigDecimal("master_item_id", masterItemId);
    }
    
    public BigDecimal getMasterItemId(){
        return getBigDecimal("master_item_id");
    }
        public void setPaidupDate(Date paidupDate){
        setUtilDate("paidup_date", paidupDate);
    }
    
    public Date getPaidupDate(){
        return getUtilDate("paidup_date");
    }
    public void setInitialValiDateDate(Date initialValiDateDate){
        setUtilDate("initial_valiDate_date", initialValiDateDate);
    }
    
    public Date getInitialValiDateDate(){
        return getUtilDate("initial_valiDate_date");
    }
        public void setCaseId(BigDecimal caseId){
        setBigDecimal("case_id", caseId);
    }
    
    public BigDecimal getCaseId(){
        return getBigDecimal("case_id");
    }
        public void setExpiryDate(Date expiryDate){
        setUtilDate("expiry_date", expiryDate);
    }
    
    public Date getExpiryDate(){
        return getUtilDate("expiry_date");
    }
        public void setPayYear(BigDecimal payYear){
        setBigDecimal("pay_year", payYear);
    }
    
    public BigDecimal getPayYear(){
        return getBigDecimal("pay_year");
    }
        public void setPayPeriod(String payPeriod){
        setString("pay_period", payPeriod);
    }
    
    public String getPayPeriod(){
        return getString("pay_period");
    }
        public void setLiabilityState(BigDecimal liabilityState){
        setBigDecimal("liability_state", liabilityState);
    }
    
    public BigDecimal getLiabilityState(){
        return getBigDecimal("liability_state");
    }
        public void setCopyDate(Date copyDate){
        setUtilDate("copy_date", copyDate);
    }
    
    public Date getCopyDate(){
        return getUtilDate("copy_date");
    }
        public void setCountWay(String countWay){
        setString("count_way", countWay);
    }
    
    public String getCountWay(){
        return getString("count_way");
    }
        public void setPolicyCode(String policyCode){
        setString("policy_code", policyCode);
    }
    
    public String getPolicyCode(){
        return getString("policy_code");
    }
        public void setIsGift(BigDecimal isGift){
        setBigDecimal("is_gift", isGift);
    }
    
    public BigDecimal getIsGift(){
        return getBigDecimal("is_gift");
    }
        public void setRerinstateDate(Date rerinstateDate){
        setUtilDate("rerinstate_date", rerinstateDate);
    }
    
    public Date getRerinstateDate(){
        return getUtilDate("rerinstate_date");
    }
        public void setAdditionalPremAf(BigDecimal additionalPremAf){
        setBigDecimal("additional_prem_af", additionalPremAf);
    }
    
    public BigDecimal getAdditionalPremAf(){
        return getBigDecimal("additional_prem_af");
    }
        public void setRenewDecision(BigDecimal renewDecision){
        setBigDecimal("renew_decision", renewDecision);
    }
    
    public BigDecimal getRenewDecision(){
        return getBigDecimal("renew_decision");
    }
        public void setValidateDate(Date validateDate){
        setUtilDate("validate_date", validateDate);
    }
    
    public Date getValidateDate(){
        return getUtilDate("validate_date");
    }
        public void setBonusMode(BigDecimal bonusMode){
        setBigDecimal("bonus_mode", bonusMode);
    }
    
    public BigDecimal getBonusMode(){
        return getBigDecimal("bonus_mode");
    }
                public void setBenefitLevel(String benefitLevel){
        setString("benefit_level", benefitLevel);
    }
    
    public String getBenefitLevel(){
        return getString("benefit_level");
    }
        public void setUwId(BigDecimal uwId){
        setBigDecimal("uw_id", uwId);
    }
    
    public BigDecimal getUwId(){
        return getBigDecimal("uw_id");
    }
        public void setProductId(BigDecimal productId){
        setBigDecimal("product_id", productId);
    }
    
    public BigDecimal getProductId(){
        return getBigDecimal("product_id");
    }
        public void setBonusSa(BigDecimal bonusSa){
        setBigDecimal("bonus_sa", bonusSa);
    }
    
    public BigDecimal getBonusSa(){
        return getBigDecimal("bonus_sa");
    }
        public void setProductCode(String productCode){
        setString("product_code", productCode);
    }
    
    public String getProductCode(){
        return getString("product_code");
    }
        public void setApplyDate(Date applyDate){
        setUtilDate("apply_date", applyDate);
    }
    
    public Date getApplyDate(){
        return getUtilDate("apply_date");
    }
        public void setInterestFlag(BigDecimal interestFlag){
        setBigDecimal("interest_flag", interestFlag);
    }
    
    public BigDecimal getInterestFlag(){
        return getBigDecimal("interest_flag");
    }
        public void setCoveragePeriod(String coveragePeriod){
        setString("coverage_period", coveragePeriod);
    }
    
    public String getCoveragePeriod(){
        return getString("coverage_period");
    }
        public void setItemId(BigDecimal itemId){
        setBigDecimal("item_id", itemId);
    }
    
    public BigDecimal getItemId(){
        return getBigDecimal("item_id");
    }
        public void setRenewalExtraPremAf(BigDecimal renewalExtraPremAf){
        setBigDecimal("renewal_extra_prem_af", renewalExtraPremAf);
    }
    
    public BigDecimal getRenewalExtraPremAf(){
        return getBigDecimal("renewal_extra_prem_af");
    }
        public void setInitialDiscntedExtraPremAf(BigDecimal initialDiscntedExtraPremAf){
        setBigDecimal("initial_discnted_extra_prem_af", initialDiscntedExtraPremAf);
    }
    
    public BigDecimal getInitialDiscntedExtraPremAf(){
        return getBigDecimal("initial_discnted_extra_prem_af");
    }
            public void setWaiverEnd(Date waiverEnd){
        setUtilDate("waiver_end", waiverEnd);
    }
    
    public Date getWaiverEnd(){
        return getUtilDate("waiver_end");
    }
        public void setMaturityDate(Date maturityDate){
        setUtilDate("maturity_date", maturityDate);
    }
    
    public Date getMaturityDate(){
        return getUtilDate("maturity_date");
    }
        public void setBusiItemId(BigDecimal busiItemId){
        setBigDecimal("busi_item_id", busiItemId);
    }
    
    public BigDecimal getBusiItemId(){
        return getBigDecimal("busi_item_id");
    }
        public void setHealthServiceFlag(BigDecimal healthServiceFlag){
        setBigDecimal("health_service_flag", healthServiceFlag);
    }
    
    public BigDecimal getHealthServiceFlag(){
        return getBigDecimal("health_service_flag");
    }
        public void setLapseDate(Date lapseDate){
        setUtilDate("lapse_date", lapseDate);
    }
    
    public Date getLapseDate(){
        return getUtilDate("lapse_date");
    }
        public void setInitialType(BigDecimal initialType){
        setBigDecimal("initial_type", initialType);
    }
    
    public BigDecimal getInitialType(){
        return getBigDecimal("initial_type");
    }
        public void setPayFreq(String payFreq){
        setString("pay_freq", payFreq);
    }
    
    public String getPayFreq(){
        return getString("pay_freq");
    }
        public void setUnit(BigDecimal unit){
        setBigDecimal("unit", unit);
    }
    
    public BigDecimal getUnit(){
        return getBigDecimal("unit");
    }
        public void setCoverageYear(BigDecimal coverageYear){
        setBigDecimal("coverage_year", coverageYear);
    }
    
    public BigDecimal getCoverageYear(){
        return getBigDecimal("coverage_year");
    }
            public void setEndCause(String endCause){
        setString("end_cause", endCause);
    }
    
    public String getEndCause(){
        return getString("end_cause");
    }
        public void setLapseCause(String lapseCause){
        setString("lapse_cause", lapseCause);
    }
    
    public String getLapseCause(){
        return getString("lapse_cause");
    }
        public void setRenewalDiscntedPremAf(BigDecimal renewalDiscntedPremAf){
        setBigDecimal("renewal_discnted_prem_af", renewalDiscntedPremAf);
    }
    
    public BigDecimal getRenewalDiscntedPremAf(){
        return getBigDecimal("renewal_discnted_prem_af");
    }
        public void setTotalPremAf(BigDecimal totalPremAf){
        setBigDecimal("total_prem_af", totalPremAf);
    }
    
    public BigDecimal getTotalPremAf(){
        return getBigDecimal("total_prem_af");
    }
        public void setDecisionCode(String decisionCode){
        setString("decision_code", decisionCode);
    }
    
    public String getDecisionCode(){
        return getString("decision_code");
    }
        public void setPauseDate(Date pauseDate){
        setUtilDate("pause_date", pauseDate);
    }
    
    public Date getPauseDate(){
        return getUtilDate("pause_date");
    }
        public void setLogId(BigDecimal logId){
        setBigDecimal("log_id", logId);
    }
    
    public BigDecimal getLogId(){
        return getBigDecimal("log_id");
    }
        public void setSpreadsMode(BigDecimal spreadsMode){
        setBigDecimal("spreads_mode", spreadsMode);
    }
    
    public BigDecimal getSpreadsMode(){
        return getBigDecimal("spreads_mode");
    }
    
    public void setChargePeriod(String chargePeriod){
        setString("charge_period", chargePeriod);
    }
    
    public String getChargePeriod(){
        return getString("charge_period");
    }
    
    
    public void setStdPremAf(BigDecimal stdPremAf){
        setBigDecimal("std_prem_af", stdPremAf);
    }
    
    public BigDecimal getStdPremAf(){
        return getBigDecimal("std_prem_af");
    }
        public void setIsMasterItem(BigDecimal isMasterItem){
        setBigDecimal("is_master_item", isMasterItem);
    }
    
    public BigDecimal getIsMasterItem(){
        return getBigDecimal("is_master_item");
    }
        public void setUwListId(BigDecimal uwListId){
        setBigDecimal("uw_list_id", uwListId);
    }
    
    public BigDecimal getUwListId(){
        return getBigDecimal("uw_list_id");
    }
        public void setSuspendDate(Date suspendDate){
        setUtilDate("suspend_date", suspendDate);
    }
    
    public Date getSuspendDate(){
        return getUtilDate("suspend_date");
    }
        public void setSuspendCause(String suspendCause){
        setString("suspend_cause", suspendCause);
    }
    
    public String getSuspendCause(){
        return getString("suspend_cause");
    }
        public void setDeductibleFranchise(BigDecimal deductibleFranchise){
        setBigDecimal("deductible_franchise", deductibleFranchise);
    }
    
    public BigDecimal getDeductibleFranchise(){
        return getBigDecimal("deductible_franchise");
    }
        public void setWaiverStart(Date waiverStart){
        setUtilDate("waiver_start", waiverStart);
    }
    
    public Date getWaiverStart(){
        return getUtilDate("waiver_start");
    }
        public void setPremFreq(BigDecimal premFreq){
        setBigDecimal("prem_freq", premFreq);
    }
    
    public BigDecimal getPremFreq(){
        return getBigDecimal("prem_freq");
    }
        public void setInitialDiscntPremAf(BigDecimal initialDiscntPremAf){
        setBigDecimal("initial_discnt_prem_af", initialDiscntPremAf);
    }
    
    public BigDecimal getInitialDiscntPremAf(){
        return getBigDecimal("initial_discnt_prem_af");
    }
        public void setCurFlag(BigDecimal curFlag){
        setBigDecimal("cur_flag", curFlag);
    }
    
    public BigDecimal getCurFlag(){
        return getBigDecimal("cur_flag");
    }
        
    public BigDecimal getChannelType(){
        return getBigDecimal("channel_type");
    }
    
    public void setChannelType(BigDecimal channelType){
        setBigDecimal("channel_type",channelType);
    }

    public String getPolNo(){
        return getString("pol_no");
    }
    public void setPolNo(String polNo){
    	setString("pol_no", polNo);
    }
    
    public BigDecimal getContractpremium(){
    	return getBigDecimal("contractpremium");
    }
    public void setContractpremium(BigDecimal contractpremium){
    	 setBigDecimal("contractpremium",contractpremium);
    }
    public BigDecimal getMaxBenefitPeriod(){
    	return getBigDecimal("max_benefit_period");
    }
    public void setMaxBenefitPeriod(BigDecimal maxBenefitPeriod){
    	 setBigDecimal("max_benefit_period",maxBenefitPeriod);
    }
    @Override
    public String toString() {
        return "ContractProductPO [" +
                "isPause="+getIsPause()+","+
"discntedPremAf="+getDiscntedPremAf()+","+
"masterProductCode="+getMasterProductCode()+","+
"prodPkgPlanCode="+getProdPkgPlanCode()+","+
"appendPremAf="+getAppendPremAf()+","+
"isWaived="+getIsWaived()+","+
"interestMode="+getInterestMode()+","+
"applyCode="+getApplyCode()+","+
"norenewReason="+getNorenewReason()+","+
"organCode="+getOrganCode()+","+
"chargeYear="+getChargeYear()+","+
"extraPremAf="+getExtraPremAf()+","+
"policyId="+getPolicyId()+","+
"renewalDiscntedExtraPremAf="+getRenewalDiscntedExtraPremAf()+","+
"initialAmount="+getInitialAmount()+","+
"ccSa="+getCcSa()+","+
"initialExtraPremAf="+getInitialExtraPremAf()+","+
"payoutRate="+getPayoutRate()+","+
"amount="+getAmount()+","+
"masterProductId="+getMasterProductId()+","+
"masterItemId="+getMasterItemId()+","+
"paidupDate="+getPaidupDate()+","+
"caseId="+getCaseId()+","+
"expiryDate="+getExpiryDate()+","+
"payYear="+getPayYear()+","+
"payPeriod="+getPayPeriod()+","+
"liabilityState="+getLiabilityState()+","+
"copyDate="+getCopyDate()+","+
"countWay="+getCountWay()+","+
"policyCode="+getPolicyCode()+","+
"isGift="+getIsGift()+","+
"rerinstateDate="+getRerinstateDate()+","+
"additionalPremAf="+getAdditionalPremAf()+","+
"renewDecision="+getRenewDecision()+","+
"validateDate="+getValidateDate()+","+
"bonusMode="+getBonusMode()+","+
"benefitLevel="+getBenefitLevel()+","+
"uwId="+getUwId()+","+
"productId="+getProductId()+","+
"bonusSa="+getBonusSa()+","+
"productCode="+getProductCode()+","+
"applyDate="+getApplyDate()+","+
"interestFlag="+getInterestFlag()+","+
"coveragePeriod="+getCoveragePeriod()+","+
"itemId="+getItemId()+","+
"renewalExtraPremAf="+getRenewalExtraPremAf()+","+
"initialDiscntedExtraPremAf="+getInitialDiscntedExtraPremAf()+","+
"waiverEnd="+getWaiverEnd()+","+
"maturityDate="+getMaturityDate()+","+
"busiItemId="+getBusiItemId()+","+
"healthServiceFlag="+getHealthServiceFlag()+","+
"lapseDate="+getLapseDate()+","+
"initialType="+getInitialType()+","+
"payFreq="+getPayFreq()+","+
"unit="+getUnit()+","+
"coverageYear="+getCoverageYear()+","+
"endCause="+getEndCause()+","+
"lapseCause="+getLapseCause()+","+
"renewalDiscntedPremAf="+getRenewalDiscntedPremAf()+","+
"totalPremAf="+getTotalPremAf()+","+
"decisionCode="+getDecisionCode()+","+
"pauseDate="+getPauseDate()+","+
"logId="+getLogId()+","+
"spreadsMode="+getSpreadsMode()+","+
"chargePeriod="+getChargePeriod()+","+
"stdPremAf="+getStdPremAf()+","+
"isMasterItem="+getIsMasterItem()+","+
"uwListId="+getUwListId()+","+
"suspendDate="+getSuspendDate()+","+
"suspendCause="+getSuspendCause()+","+
"deductibleFranchise="+getDeductibleFranchise()+","+
"waiverStart="+getWaiverStart()+","+
"premFreq="+getPremFreq()+","+
"initialDiscntPremAf="+getInitialDiscntPremAf()+","+
"curFlag="+getCurFlag()+"]";
    }   
 }
