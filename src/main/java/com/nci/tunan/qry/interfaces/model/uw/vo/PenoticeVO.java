package com.nci.tunan.qry.interfaces.model.uw.vo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 体检信息
 * 
 * @description
 * <AUTHOR> <EMAIL>
 * @date 2018-1-15 上午10:50:56
 * @.belongToModule 核保-体检信息
 */
public class PenoticeVO extends BaseVO {
	/**
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 是否有过变更
	 */
	private BigDecimal isChangeFlag;
	/**
	 * 体检医院
	 */
	private String resultPeHospital;
	/**
	 * 判断原因
	 */
	private String resultMagnumMsg;
	/**
	 * 客户满意度
	 */
	private String resultCusScore;
	/**
	 * 交互结果
	 */
	private String resultMagnumValue;
	/**
	 * 结果备注
	 */
	private String resultComment;
	/**
	 * 核保ID
	 */
	private BigDecimal uwId;// 核保ID
	/**
	 * 客户名称
	 */
	private String customerName;// 客户名称
	/**
	 * 通知书ID
	 */
	private BigDecimal docListId;// 通知书ID
	/**
	 * 阳性标记
	 */
	private Integer plusIndi;
	/**
	 * 体检件主键
	 */
	private BigDecimal penoticeId;// 体检件主键
	/**
	 * 体检原因
	 */
	private String peReason;// 体检原因
	/**
	 * 插入记录时间
	 */
	private Date insertTime;// 插入记录时间
	/**
	 * 客户ID
	 */
	private BigDecimal customerId;// 客户ID（投保人/被保人）
	/**
	 * 更新时间
	 */
	private Date updateTime;// 更新时间
	/**
	 * 特别原因
	 */
	private String otherComments;// //特别原因
	/**
	 * 插入时间戳
	 */
	private Date insertTimestamp;// 插入时间戳
	/**
	 * 客户角色(
	 */
	private String roleType;// 客户角色(投保人/被保人)
	/**
	 * 更新记录人
	 */
	private BigDecimal updateBy;// 更新记录人
	/**
	 * 更新时间戳
	 */
	private Date updateTimestamp;// 更新时间戳
	/**
	 * 是否空腹
	 */
	private String esIndi;// 是否空腹（empty stomach）
	/**
	 * 插入记录人
	 */
	private BigDecimal insertBy;// 插入记录人
	/**
	 * 保单ID
	 */
	private BigDecimal policyId;// 保单ID
	/**
	 * 客户性别
	 */
	private String genderCode;// 客户性别
	/**
	 * 客户生日
	 */
	private Date customerBirth;// 客户生日
	/**
	 * 体检年龄
	 */
	private String resultAge;// 体检年龄
	/**
	 * 实际联系电话
	 */
	private String resultTel;// 实际联系电话
	/**
	 * 质量评价备注
	 */
	private String qualityComment;// 质量评价备注
	/**
	 * 质量评价
	 */
	private String qualityEvaluation;// 质量评价
	/**
	 * 体检日期
	 */
	private Date resultPeDate;// 体检日期
	/**
	 * 体检医院ID
	 */
	private String uwHospitalId;// 体检医院ID-->体检医院code
	/**
	 * 体检医院名称
	 */
	private String hospitalName;// 体检医院名称
	/**
	 * 阳性标记
	 */
	private BigDecimal positiveIndi;// 阳性标记addbyyouyuan
	/**
	 * 投保单号
	 */
	private String applyCode;// 投保单号
	/**
	 * 通知书编号
	 */
	private String documentNo;// 通知书编号
	/**
	 * 更新标记
	 */
	private String updateFlag;
	/**
	 * 工作流 任务id
	 */
	private String bpmtaskId; // 工作流 任务id 用于追加通知书的任务提交
	/**
	 * 客户生日
	 */
	private String customerBirthdayStr;
	/**
	 * 体检项目名称
	 */
	private String penoticeDetailNames;// 体检项目名称
	/**
	 * 日期
	 */
	private String formatDate;
	/**
	 * 体检通知书体检有效性 继续循环标识
	 */
	private String continueNum; // Modify by xuhp 新增体检通知书体检有效性 继续循环标识
								// 2015年11月24日
	/**
	 * 体检通知书体检有效性 继续循环总数标识
	 */
	private String continueCountNum; // Modify by xuhp 新增体检通知书体检有效性 继续循环总数标识
										// 2015年11月24日
	/**
	 * 通知书发放对象的工作名称
	 */
	private String jobName; // 通知书发放对象的工作名称
	/**
	 * 体检知书新增通知书状态字段（t_penotice），0-新增，1-已发放，2-已回销 2016-12-15
	 */
	private String status; // 体检知书新增通知书状态字段（t_penotice），0-新增，1-已发放，2-已回销
							// 2016-12-15
	/**
	 * 体检通知书新增体检金额字段
	 */
	private BigDecimal peFee; // 体检通知书新增体检金额字段
	/**
	 * 体检通知书新增是否体检字段
	 */
	private BigDecimal isPhysical; // 体检通知书新增是否体检字段
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * 体检开始日期
	 */
	private Date resultPeStartDate;
	/**
	 * 体检结束日期
	 */
	private Date resultPeEndDate;
	/**
	 * 保单机构
	 */
	private String organCode;
	/**
	 * 机构名称
	 */
	private String organName;
	/**
	 * 体检数量
	 */
	private BigDecimal physicalTotal;
	/**
	 * 阳性数量
	 */
	private BigDecimal positiveTotal;
	/**
	 * 婚姻状态
	 */
	private String marriageStatus;
	/**
	 * 核保来源
	 */
	private String uwSource;
	/**
	 * 通知书发放时间
	 */
	private Date sendTime;
	/**
	 * 通知书打印时间
	 */
	private Date printTime;
	/**
	 * 通知书回销时间
	 */
	private Date closeTime;
	/**
	 * 核保来源
	 */
	private String genderName;
	/**
	 * 是否陪检
	 */
	private BigDecimal accompanyingExaFlag;
	/**
	 * 必录项结果是否齐全
	 */
	private BigDecimal resultComplete;
	/**
	 * 陪检信息
	 */
	private String hospitalAccStr;
	/**
	 * 免陪检
	 */
	private BigDecimal accompanyingAvoidFlag;
	/**
	 * 签名备案数据
	 */
	private String imageCode;
	/**
	 * 陪检人员姓名
	 */
	private List<String> accompanyNames;
	/**
	 * 影像上传状态
	 */
	private List<String> imageStatusCodes;
	/**
	 * imageCodeFlag标识
	 */
	private String imageCodeFlag;
	/**
	 * 陪检人员标识
	 */
	private String accompanyNameFlag;
	/**
	 * 签名备案初始化展示标识
	 */
	private String imageFlag;
	
	
	public BigDecimal getAccompanyingExaFlag() {
		return accompanyingExaFlag;
	}

	public void setAccompanyingExaFlag(BigDecimal accompanyingExaFlag) {
		this.accompanyingExaFlag = accompanyingExaFlag;
	}

	public String getHospitalAccStr() {
		return hospitalAccStr;
	}

	public void setHospitalAccStr(String hospitalAccStr) {
		this.hospitalAccStr = hospitalAccStr;
	}

	public BigDecimal getAccompanyingAvoidFlag() {
		return accompanyingAvoidFlag;
	}

	public void setAccompanyingAvoidFlag(BigDecimal accompanyingAvoidFlag) {
		this.accompanyingAvoidFlag = accompanyingAvoidFlag;
	}

	public String getImageCode() {
		return imageCode;
	}

	public void setImageCode(String imageCode) {
		this.imageCode = imageCode;
	}

	public BigDecimal getResultComplete() {
		return resultComplete;
	}

	public void setResultComplete(BigDecimal resultComplete) {
		this.resultComplete = resultComplete;
	}

	public String getContinueCountNum() {
		return continueCountNum;
	}

	public void setContinueCountNum(String continueCountNum) {
		this.continueCountNum = continueCountNum;
	}

	public String getContinueNum() {
		return continueNum;
	}

	public void setContinueNum(String continueNum) {
		this.continueNum = continueNum;
	}

	public String getFormatDate() {
		return formatDate;
	}

	public void setFormatDate(String formatDate) {
		this.formatDate = formatDate;
	}

	/**
	 * @Fields policyCode : 保单号
	 */
	private String policyCode;

	/**
	 * getter method
	 * 
	 * @return the policyCode
	 */

	public String getPolicyCode() {
		return policyCode;
	}

	/**
	 * setter method
	 * 
	 * @param policyCode
	 *            the policyCode to set
	 */

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getPenoticeDetailNames() {
		return penoticeDetailNames;
	}

	public void setPenoticeDetailNames(String penoticeDetailNames) {
		this.penoticeDetailNames = penoticeDetailNames;
	}

	public String getCustomerBirthdayStr() {
		return customerBirthdayStr;
	}

	public void setCustomerBirthdayStr(String customerBirthdayStr) {
		this.customerBirthdayStr = customerBirthdayStr;
	}

	public String getUpdateFlag() {
		return updateFlag;
	}

	public String getBpmtaskId() {
		return bpmtaskId;
	}

	public void setBpmtaskId(String bpmtaskId) {
		this.bpmtaskId = bpmtaskId;
	}

	public String getResultPeHospital() {
		return resultPeHospital;
	}

	public void setResultPeHospital(String resultPeHospital) {
		this.resultPeHospital = resultPeHospital;
	}

	public void setUpdateFlag(String updateFlag) {
		this.updateFlag = updateFlag;
	}

	public String getDocumentNo() {
		return documentNo;
	}

	public void setDocumentNo(String documentNo) {
		this.documentNo = documentNo;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getResultMagnumMsg() {
		return resultMagnumMsg;
	}

	public void setResultMagnumMsg(String resultMagnumMsg) {
		this.resultMagnumMsg = resultMagnumMsg;
	}

	public String getResultCusScore() {
		return resultCusScore;
	}

	public void setResultCusScore(String resultCusScore) {
		this.resultCusScore = resultCusScore;
	}

	public String getResultMagnumValue() {
		return resultMagnumValue;
	}

	public void setResultMagnumValue(String resultMagnumValue) {
		this.resultMagnumValue = resultMagnumValue;
	}

	public String getResultComment() {
		return resultComment;
	}

	public void setResultComment(String resultComment) {
		this.resultComment = resultComment;
	}

	public BigDecimal getUwId() {
		return uwId;
	}

	public void setUwId(BigDecimal uwId) {
		this.uwId = uwId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public BigDecimal getDocListId() {
		return docListId;
	}

	public void setDocListId(BigDecimal docListId) {
		this.docListId = docListId;
	}

	public Integer getPlusIndi() {
		return plusIndi;
	}

	public void setPlusIndi(Integer plusIndi) {
		this.plusIndi = plusIndi;
	}

	public BigDecimal getPenoticeId() {
		return penoticeId;
	}

	public void setPenoticeId(BigDecimal penoticeId) {
		this.penoticeId = penoticeId;
	}

	public String getPeReason() {
		return peReason;
	}

	public void setPeReason(String peReason) {
		this.peReason = peReason;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public String getGenderCode() {
		return genderCode;
	}

	public void setGenderCode(String genderCode) {
		this.genderCode = genderCode;
	}

	public String getQualityEvaluation() {
		return qualityEvaluation;
	}

	public void setQualityEvaluation(String qualityEvaluation) {
		this.qualityEvaluation = qualityEvaluation;
	}

	public String getUwHospitalId() {
		return uwHospitalId;
	}

	public void setUwHospitalId(String uwHospitalId) {
		this.uwHospitalId = uwHospitalId;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getOtherComments() {
		return otherComments;
	}

	public void setOtherComments(String otherComments) {
		this.otherComments = otherComments;
	}

	public Date getInsertTimestamp() {
		return insertTimestamp;
	}

	public void setInsertTimestamp(Date insertTimestamp) {
		this.insertTimestamp = insertTimestamp;
	}

	public String getRoleType() {
		return roleType;
	}

	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}

	public BigDecimal getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(BigDecimal updateBy) {
		this.updateBy = updateBy;
	}

	public Date getUpdateTimestamp() {
		return updateTimestamp;
	}

	public void setUpdateTimestamp(Date updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}

	public String getEsIndi() {
		return esIndi;
	}

	public void setEsIndi(String esIndi) {
		this.esIndi = esIndi;
	}

	public BigDecimal getInsertBy() {
		return insertBy;
	}

	public void setInsertBy(BigDecimal insertBy) {
		this.insertBy = insertBy;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public Date getCustomerBirth() {
		return customerBirth;
	}

	public void setCustomerBirth(Date customerBirth) {
		this.customerBirth = customerBirth;
	}

	public String getResultAge() {
		return resultAge;
	}

	public void setResultAge(String resultAge) {
		this.resultAge = resultAge;
	}

	public String getResultTel() {
		return resultTel;
	}

	public void setResultTel(String resultTel) {
		this.resultTel = resultTel;
	}

	public String getQualityComment() {
		return qualityComment;
	}

	public void setQualityComment(String qualityComment) {
		this.qualityComment = qualityComment;
	}

	public Date getResultPeDate() {
		return resultPeDate;
	}

	public void setResultPeDate(Date resultPeDate) {
		this.resultPeDate = resultPeDate;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public BigDecimal getPositiveIndi() {
		return positiveIndi;
	}

	public void setPositiveIndi(BigDecimal positiveIndi) {
		this.positiveIndi = positiveIndi;
	}

	public BigDecimal getIsChangeFlag() {
		return isChangeFlag;
	}

	public void setIsChangeFlag(BigDecimal isChangeFlag) {
		this.isChangeFlag = isChangeFlag;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public BigDecimal getPeFee() {
		return peFee;
	}

	public void setPeFee(BigDecimal peFee) {
		this.peFee = peFee;
	}

	public BigDecimal getIsPhysical() {
		return isPhysical;
	}

	public void setIsPhysical(BigDecimal isPhysical) {
		this.isPhysical = isPhysical;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Date getResultPeStartDate() {
		return resultPeStartDate;
	}

	public void setResultPeStartDate(Date resultPeStartDate) {
		this.resultPeStartDate = resultPeStartDate;
	}

	public Date getResultPeEndDate() {
		return resultPeEndDate;
	}

	public void setResultPeEndDate(Date resultPeEndDate) {
		this.resultPeEndDate = resultPeEndDate;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public BigDecimal getPhysicalTotal() {
		return physicalTotal;
	}

	public void setPhysicalTotal(BigDecimal physicalTotal) {
		this.physicalTotal = physicalTotal;
	}

	public BigDecimal getPositiveTotal() {
		return positiveTotal;
	}

	public void setPositiveTotal(BigDecimal positiveTotal) {
		this.positiveTotal = positiveTotal;
	}

	public String getMarriageStatus() {
		return marriageStatus;
	}

	public void setMarriageStatus(String marriageStatus) {
		this.marriageStatus = marriageStatus;
	}

	public String getUwSource() {
		return uwSource;
	}

	public void setUwSource(String uwSource) {
		this.uwSource = uwSource;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public Date getPrintTime() {
		return printTime;
	}

	public void setPrintTime(Date printTime) {
		this.printTime = printTime;
	}

	public Date getCloseTime() {
		return closeTime;
	}

	public void setCloseTime(Date closeTime) {
		this.closeTime = closeTime;
	}

	public String getGenderName() {
		return genderName;
	}

	public void setGenderName(String genderName) {
		this.genderName = genderName;
	}

	public List<String> getAccompanyNames() {
		return accompanyNames;
	}

	public void setAccompanyNames(List<String> accompanyNames) {
		this.accompanyNames = accompanyNames;
	}

	public List<String> getImageStatusCodes() {
		return imageStatusCodes;
	}

	public void setImageStatusCodes(List<String> imageStatusCodes) {
		this.imageStatusCodes = imageStatusCodes;
	}

	public String getImageCodeFlag() {
		return imageCodeFlag;
	}

	public void setImageCodeFlag(String imageCodeFlag) {
		this.imageCodeFlag = imageCodeFlag;
	}

	public String getAccompanyNameFlag() {
		return accompanyNameFlag;
	}

	public void setAccompanyNameFlag(String accompanyNameFlag) {
		this.accompanyNameFlag = accompanyNameFlag;
	}

	public String getImageFlag() {
		return imageFlag;
	}

	public void setImageFlag(String imageFlag) {
		this.imageFlag = imageFlag;
	}

	@Override
	public String getBizId() {
		return null;
	}
	

	@Override
	public String toString() {
		return "PenoticeVO [isChangeFlag=" + isChangeFlag
				+ ", resultPeHospital=" + resultPeHospital
				+ ", resultMagnumMsg=" + resultMagnumMsg + ", resultCusScore="
				+ resultCusScore + ", resultMagnumValue=" + resultMagnumValue
				+ ", resultComment=" + resultComment + ", uwId=" + uwId
				+ ", customerName=" + customerName + ", docListId=" + docListId
				+ ", plusIndi=" + plusIndi + ", penoticeId=" + penoticeId
				+ ", peReason=" + peReason + ", insertTime=" + insertTime
				+ ", customerId=" + customerId + ", updateTime=" + updateTime
				+ ", otherComments=" + otherComments + ", insertTimestamp="
				+ insertTimestamp + ", roleType=" + roleType + ", updateBy="
				+ updateBy + ", updateTimestamp=" + updateTimestamp
				+ ", esIndi=" + esIndi + ", insertBy=" + insertBy
				+ ", policyId=" + policyId + ", genderCode=" + genderCode
				+ ", customerBirth=" + customerBirth + ", resultAge="
				+ resultAge + ", resultTel=" + resultTel + ", qualityComment="
				+ qualityComment + ", qualityEvaluation=" + qualityEvaluation
				+ ", resultPeDate=" + resultPeDate + ", uwHospitalId="
				+ uwHospitalId + ", hospitalName=" + hospitalName
				+ ", positiveIndi=" + positiveIndi + ", applyCode=" + applyCode
				+ ", documentNo=" + documentNo + ", updateFlag=" + updateFlag
				+ ", bpmtaskId=" + bpmtaskId + ", customerBirthdayStr="
				+ customerBirthdayStr + ", penoticeDetailNames="
				+ penoticeDetailNames + ", formatDate=" + formatDate
				+ ", continueNum=" + continueNum + ", continueCountNum="
				+ continueCountNum + ", jobName=" + jobName + ", status="
				+ status + ", peFee=" + peFee + ", isPhysical=" + isPhysical
				+ ", userName=" + userName + ", resultPeStartDate="
				+ resultPeStartDate + ", resultPeEndDate=" + resultPeEndDate
				+ ", organCode=" + organCode + ", organName=" + organName
				+ ", physicalTotal=" + physicalTotal + ", positiveTotal="
				+ positiveTotal + ", marriageStatus=" + marriageStatus
				+ ", uwSource=" + uwSource + ", sendTime=" + sendTime
				+ ", printTime=" + printTime + ", closeTime=" + closeTime
				+ ", genderName=" + genderName + ", accompanyingExaFlag="
				+ accompanyingExaFlag + ", resultComplete=" + resultComplete
				+ ", hospitalAccStr=" + hospitalAccStr
				+ ", accompanyingAvoidFlag=" + accompanyingAvoidFlag
				+ ", imageCode=" + imageCode + ", accompanyNames="
				+ accompanyNames + ", imageStatusCodes=" + imageStatusCodes
				+ ", imageCodeFlag=" + imageCodeFlag + ", accompanyNameFlag="
				+ accompanyNameFlag + ", imageFlag=" + imageFlag
				+ ", policyCode=" + policyCode + "]";
	}

}
