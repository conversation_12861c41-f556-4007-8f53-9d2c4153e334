package com.nci.tunan.qry.interfaces.model.cus.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

public class CsPolicyInvalidBO extends BaseBO{

	/**
     * 原贷款起息日
     */
    private Date interestStartDate;
    public Date getInterestStartDate() {
		return interestStartDate;
	}

	public void setInterestStartDate(Date interestStartDate) {
		this.interestStartDate = interestStartDate;
	}
	/**
	 * @Fields batch_name :批处理名称
	 */
	private String batchName;

	/**
	 * @Fields channelType :销售渠道代码
	 */
	private String channelType;

	/**
	 * @Fields channelType : 销售渠道代码字符串
	 */
	private String channelTypes;

	/**
	 * @Fields channelTypeList : 销售渠道集合
	 */
	private List<String> channelTypeList;

	/**
	 * @Fields channelTypeName :销售渠道名称
	 */
	private String channelTypeName;

	/**
	 * @Fields policyCode :保险单号
	 */
	private String policyCode;

	/**
	 * @Fields organCode : 保单管理机构代码
	 */
	private String organCode;

	/**
	 * @Fields organCodes : 保单管理机构代码字符串
	 */
	private String organCodes;

	/**
	 * @Fields channelTypeList : 保单管理机构集合
	 */
	private List<String> organCodeList;

	/**
	 * @Fields organName : 保单管理机构名称
	 */
	private String organName;

	/**
	 * @Fields channelOrgCode : 保单销售机构代码
	 */
	private String channelOrgCode;

	/**
	 * @Fields channelOrgName : 保单销售机构名称
	 */
	private String channelOrgName;

	/**
	 * @Fields holderName : 投保人姓名
	 */
	private String holderName;

	/**
	 * @Fields holderGender : 投保人性别
	 */
	private String holderGender;

	/**
	 * @Fields holderBirthday : 投保人出生日期
	 */
	private Date holderBirthday;

	/**
	 * @Fields insuredName : 被保人姓名
	 */
	private String insuredName;

	/**
	 * @Fields insuredGender : 被保人性别
	 */
	private String insuredGender;

	/**
	 * @Fields insuredBirthday : 被保人出生日期
	 */
	private Date insuredBirthday;

	/**
	 * @Fields insuredApplyAge : 投保年龄
	 */
	private String insuredApplyAge;

	/**
	 * @Fields masterFlagMaster : 主附险标识
	 */
	private String masterFlagMaster;

	/**
	 * @Fields busiProdCode : 险种编码
	 */
	private String busiProdCode;

	/**
	 * @Fields busiProdName : 险种名称
	 */
	private String busiProdName;
	/**
	 * @Fields product_item : 责任组名称
	 */
	private String productitem;

	/**
	 * @Fields policyValidateDate : 保单生效日期
	 */
	private Date policyValidateDate;

	/**
	 * @Fields policyLapseDate : 保单失效日期
	 */
	private Date policyLapseDate;

	/**
	 * @Fields policyExpiryDate : 保单终止日期
	 */
	private Date policyExpiryDate;

	/**
	 * @Fields busiValidateDate : 险种生效日期
	 */
	private Date busiValidateDate;

	/**
	 * @Fields busiLapseDate : 险种失效日期
	 */
	private Date busiLapseDate;

	/**
	 * @Fields busiExpiryDate : 险种终止日期
	 */
	private Date busiExpiryDate;

	/**
	 * @Fields batchDate : 批处理日期
	 */
	private Date batchDate;

	/**
	 * @Fields payDueDate : 险种下期缴费日期
	 */
	private Date payDueDate;

	/**
	 * @Fields policyLapseCauseName : 保单失效原因名称
	 */
	private String policyLapseCauseName;

	/**
	 * @Fields busiLapseCauseName : 险种失效原因名称
	 */
	private String busiLapseCauseName;

	/**
	 * @Fields amount : 保额
	 */
	private BigDecimal amount;

	/**
	 * @Fields totalPremAf : 保费
	 */

	private BigDecimal totalPremAf;

	/**
	 * @Fields loanFirstTransDate : 贷款起期
	 */

	private Date loanFirstTransDate;
	/**
	 * @Fields loanRepayDueDate : 贷款止期
	 */
	private Date loanRepayDueDate;

	/**
	 * @Fields loanCapitalBalance : 贷款本金
	 */
	private BigDecimal loanCapitalBalance;

	/**
	 * @Fields loanApplyCashValue : 贷款申请时现金价值
	 */
	private BigDecimal loanApplyCashValue;

	/**
	 * @Fields maxLoanRadio : 可贷比例
	 */
	private BigDecimal maxLoanRadio;

	/**
	 * @Fields loanAmountLimit : 贷款限额
	 */

	private BigDecimal loanAmountLimit;

	/**
	 * @Fields initLoanRate : 初始贷款利率
	 */
	private BigDecimal initLoanRate;

	/**
	 * @Fields initLoanInterest : 初始贷款利息
	 */
	private BigDecimal initLoanInterest;

	/**
	 * @Fields secondLoanRate : 逾期利率/二阶段利率
	 */
	private BigDecimal secondLoanRate;

	/**
	 * @Fields thirdLoanRate : 三阶段利率
	 */
	private BigDecimal thirdLoanRate;

	/**
	 * @Fields interestOverdue : 逾期利息
	 */
	private BigDecimal interestOverdue;

	/**
	 * @Fields loanInterestCapital : 贷款本息和
	 */
	private BigDecimal loanInterestCapital;
	/**
	 * @Fields policyPeriod : 已交保费期数
	 */
	private BigDecimal policyPeriod;

	/**
	 * @Fields accumulatePrem : 已交保费金额
	 */
	private BigDecimal accumulatePrem;

	/**
	 * @Fields busiLiabilityState : 险种状态
	 */
	private String busiLiabilityState;

	/**
	 * @Fields policyLiabilityState : 保单状态
	 */
	private String policyLiabilityState;

	/**
	 * @Fields policyAgentMsg : 新契约业务员信息
	 */
	private String policyAgentMsg;

	/**
	 * @Fields policyServiceMsg : 服务业务员信息
	 */
	private String policyServiceMsg;

	/**
	 * @Fields holderMobileTel : 投保人移动电话
	 */
	private String holderMobileTel;

	/**
	 * @Fields holderHouseTel : 投保人固定电话
	 */
	private String holderHouseTel;

	/**
	 * @Fields batchStartTime :统计时间起期
	 */
	private Date batchStartTime;

	/**
	 * @Fields batchEndTime : 统计时间止期
	 */
	private Date batchEndTime;

	/**
	 * @Fields endCause : 失效原因
	 */
	private String lapseCause;

	/**
	 * @Fields unit : 单位
	 */
	private String unit;

	/**
	 * @Fields createListTime : 清单生成日期
	 */
	private Date createListTime;

	/**
	 * @Fields insertedTimestamp : 插入时间戳
	 */
	private Date insertTimestamp;
	/**
	 * @Fields cfgValidateTime : 配置生效时间
	 */
	private Date cfgValidateTime;
	/**
	 * @Fields updatedTimestamp : 更新记录时间戳
	 */
	private Date updateTimestamp;

	/**
	 * @Fields premFreq : 缴费频率
	 */
	private String premFreq;
	/**
	 * @Fields multiMainriskFlag :多主险标识
	 */
	private String multiMainriskFlag;

	public String getMultiMainriskFlag() {
		return multiMainriskFlag;
	}

	public void setMultiMainriskFlag(String multiMainriskFlag) {
		this.multiMainriskFlag = multiMainriskFlag;
	}

	public String getBatchName() {
		return batchName;
	}

	public void setBatchName(String batchName) {
		this.batchName = batchName;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getChannelTypes() {
		return channelTypes;
	}

	public void setChannelTypes(String channelTypes) {
		this.channelTypes = channelTypes;
	}

	public List<String> getChannelTypeList() {
		return channelTypeList;
	}

	public void setChannelTypeList(List<String> channelTypeList) {
		this.channelTypeList = channelTypeList;
	}

	public String getChannelTypeName() {
		return channelTypeName;
	}

	public void setChannelTypeName(String channelTypeName) {
		this.channelTypeName = channelTypeName;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganCodes() {
		return organCodes;
	}

	public void setOrganCodes(String organCodes) {
		this.organCodes = organCodes;
	}

	public List<String> getOrganCodeList() {
		return organCodeList;
	}

	public void setOrganCodeList(List<String> organCodeList) {
		this.organCodeList = organCodeList;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getChannelOrgCode() {
		return channelOrgCode;
	}

	public void setChannelOrgCode(String channelOrgCode) {
		this.channelOrgCode = channelOrgCode;
	}

	public String getChannelOrgName() {
		return channelOrgName;
	}

	public void setChannelOrgName(String channelOrgName) {
		this.channelOrgName = channelOrgName;
	}

	public String getHolderName() {
		return holderName;
	}

	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}

	public String getHolderGender() {
		return holderGender;
	}

	public void setHolderGender(String holderGender) {
		this.holderGender = holderGender;
	}

	public Date getHolderBirthday() {
		return holderBirthday;
	}

	public void setHolderBirthday(Date holderBirthday) {
		this.holderBirthday = holderBirthday;
	}

	public String getInsuredName() {
		return insuredName;
	}

	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}

	public String getInsuredGender() {
		return insuredGender;
	}

	public void setInsuredGender(String insuredGender) {
		this.insuredGender = insuredGender;
	}

	public Date getInsuredBirthday() {
		return insuredBirthday;
	}

	public void setInsuredBirthday(Date insuredBirthday) {
		this.insuredBirthday = insuredBirthday;
	}

	public String getInsuredApplyAge() {
		return insuredApplyAge;
	}

	public void setInsuredApplyAge(String insuredApplyAge) {
		this.insuredApplyAge = insuredApplyAge;
	}

	public String getMasterFlagMaster() {
		return masterFlagMaster;
	}

	public void setMasterFlagMaster(String masterFlagMaster) {
		this.masterFlagMaster = masterFlagMaster;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public String getBusiProdName() {
		return busiProdName;
	}

	public void setBusiProdName(String busiProdName) {
		this.busiProdName = busiProdName;
	}

	public String getProductitem() {
		return productitem;
	}

	public void setProductitem(String productitem) {
		this.productitem = productitem;
	}

	public Date getPolicyValidateDate() {
		return policyValidateDate;
	}

	public void setPolicyValidateDate(Date policyValidateDate) {
		this.policyValidateDate = policyValidateDate;
	}

	public Date getPolicyLapseDate() {
		return policyLapseDate;
	}

	public void setPolicyLapseDate(Date policyLapseDate) {
		this.policyLapseDate = policyLapseDate;
	}

	public Date getPolicyExpiryDate() {
		return policyExpiryDate;
	}

	public void setPolicyExpiryDate(Date policyExpiryDate) {
		this.policyExpiryDate = policyExpiryDate;
	}

	public Date getBusiValidateDate() {
		return busiValidateDate;
	}

	public void setBusiValidateDate(Date busiValidateDate) {
		this.busiValidateDate = busiValidateDate;
	}

	public Date getBusiLapseDate() {
		return busiLapseDate;
	}

	public void setBusiLapseDate(Date busiLapseDate) {
		this.busiLapseDate = busiLapseDate;
	}

	public Date getBusiExpiryDate() {
		return busiExpiryDate;
	}

	public void setBusiExpiryDate(Date busiExpiryDate) {
		this.busiExpiryDate = busiExpiryDate;
	}

	public Date getBatchDate() {
		return batchDate;
	}

	public void setBatchDate(Date batchDate) {
		this.batchDate = batchDate;
	}

	public Date getPayDueDate() {
		return payDueDate;
	}

	public void setPayDueDate(Date payDueDate) {
		this.payDueDate = payDueDate;
	}

	public String getPolicyLapseCauseName() {
		return policyLapseCauseName;
	}

	public void setPolicyLapseCauseName(String policyLapseCauseName) {
		this.policyLapseCauseName = policyLapseCauseName;
	}

	public String getBusiLapseCauseName() {
		return busiLapseCauseName;
	}

	public void setBusiLapseCauseName(String busiLapseCauseName) {
		this.busiLapseCauseName = busiLapseCauseName;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getTotalPremAf() {
		return totalPremAf;
	}

	public void setTotalPremAf(BigDecimal totalPremAf) {
		this.totalPremAf = totalPremAf;
	}

	public Date getLoanFirstTransDate() {
		return loanFirstTransDate;
	}

	public void setLoanFirstTransDate(Date loanFirstTransDate) {
		this.loanFirstTransDate = loanFirstTransDate;
	}

	public Date getLoanRepayDueDate() {
		return loanRepayDueDate;
	}

	public void setLoanRepayDueDate(Date loanRepayDueDate) {
		this.loanRepayDueDate = loanRepayDueDate;
	}

	public BigDecimal getLoanCapitalBalance() {
		return loanCapitalBalance;
	}

	public void setLoanCapitalBalance(BigDecimal loanCapitalBalance) {
		this.loanCapitalBalance = loanCapitalBalance;
	}

	public BigDecimal getLoanApplyCashValue() {
		return loanApplyCashValue;
	}

	public void setLoanApplyCashValue(BigDecimal loanApplyCashValue) {
		this.loanApplyCashValue = loanApplyCashValue;
	}

	public BigDecimal getMaxLoanRadio() {
		return maxLoanRadio;
	}

	public void setMaxLoanRadio(BigDecimal maxLoanRadio) {
		this.maxLoanRadio = maxLoanRadio;
	}

	public BigDecimal getLoanAmountLimit() {
		return loanAmountLimit;
	}

	public void setLoanAmountLimit(BigDecimal loanAmountLimit) {
		this.loanAmountLimit = loanAmountLimit;
	}

	public BigDecimal getInitLoanRate() {
		return initLoanRate;
	}

	public void setInitLoanRate(BigDecimal initLoanRate) {
		this.initLoanRate = initLoanRate;
	}

	public BigDecimal getSecondLoanRate() {
		return secondLoanRate;
	}

	public void setSecondLoanRate(BigDecimal secondLoanRate) {
		this.secondLoanRate = secondLoanRate;
	}

	public BigDecimal getThirdLoanRate() {
		return thirdLoanRate;
	}

	public void setThirdLoanRate(BigDecimal thirdLoanRate) {
		this.thirdLoanRate = thirdLoanRate;
	}

	public BigDecimal getInterestOverdue() {
		return interestOverdue;
	}

	public void setInterestOverdue(BigDecimal interestOverdue) {
		this.interestOverdue = interestOverdue;
	}

	public BigDecimal getLoanInterestCapital() {
		return loanInterestCapital;
	}

	public void setLoanInterestCapital(BigDecimal loanInterestCapital) {
		this.loanInterestCapital = loanInterestCapital;
	}

	public BigDecimal getPolicyPeriod() {
		return policyPeriod;
	}

	public void setPolicyPeriod(BigDecimal policyPeriod) {
		this.policyPeriod = policyPeriod;
	}

	public BigDecimal getAccumulatePrem() {
		return accumulatePrem;
	}

	public void setAccumulatePrem(BigDecimal accumulatePrem) {
		this.accumulatePrem = accumulatePrem;
	}

	public String getBusiLiabilityState() {
		return busiLiabilityState;
	}

	public void setBusiLiabilityState(String busiLiabilityState) {
		this.busiLiabilityState = busiLiabilityState;
	}

	public String getPolicyLiabilityState() {
		return policyLiabilityState;
	}

	public void setPolicyLiabilityState(String policyLiabilityState) {
		this.policyLiabilityState = policyLiabilityState;
	}

	public String getPolicyAgentMsg() {
		return policyAgentMsg;
	}

	public void setPolicyAgentMsg(String policyAgentMsg) {
		this.policyAgentMsg = policyAgentMsg;
	}

	public String getPolicyServiceMsg() {
		return policyServiceMsg;
	}

	public void setPolicyServiceMsg(String policyServiceMsg) {
		this.policyServiceMsg = policyServiceMsg;
	}

	public String getHolderMobileTel() {
		return holderMobileTel;
	}

	public void setHolderMobileTel(String holderMobileTel) {
		this.holderMobileTel = holderMobileTel;
	}

	public String getHolderHouseTel() {
		return holderHouseTel;
	}

	public void setHolderHouseTel(String holderHouseTel) {
		this.holderHouseTel = holderHouseTel;
	}

	public Date getBatchStartTime() {
		return batchStartTime;
	}

	public void setBatchStartTime(Date batchStartTime) {
		this.batchStartTime = batchStartTime;
	}

	public Date getBatchEndTime() {
		return batchEndTime;
	}

	public void setBatchEndTime(Date batchEndTime) {
		this.batchEndTime = batchEndTime;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public Date getCreateListTime() {
		return createListTime;
	}

	public void setCreateListTime(Date createListTime) {
		this.createListTime = createListTime;
	}

	public Date getInsertTimestamp() {
		return insertTimestamp;
	}

	public void setInsertTimestamp(Date insertTimestamp) {
		this.insertTimestamp = insertTimestamp;
	}

	public Date getCfgValidateTime() {
		return cfgValidateTime;
	}

	public void setCfgValidateTime(Date cfgValidateTime) {
		this.cfgValidateTime = cfgValidateTime;
	}

	public Date getUpdateTimestamp() {
		return updateTimestamp;
	}

	public void setUpdateTimestamp(Date updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}

	public BigDecimal getInitLoanInterest() {
		return initLoanInterest;
	}

	public void setInitLoanInterest(BigDecimal initLoanInterest) {
		this.initLoanInterest = initLoanInterest;
	}

	public String getPremFreq() {
		return premFreq;
	}

	public void setPremFreq(String premFreq) {
		this.premFreq = premFreq;
	}

	public String getLapseCause() {
		return lapseCause;
	}

	public void setLapseCause(String lapseCause) {
		this.lapseCause = lapseCause;
	}

	@Override
	public String toString() {
		return "CsPolicyTerminatonListBO [channelType=" + channelType
				+ ", channelType=" + channelType + ",channelTypeList="
				+ channelTypeList + ", organCode=" + organCode
				+ ", batchStartTime=" + batchStartTime + ", batchEndTime="
				+ batchEndTime + ", endCause=" + lapseCause + ",  channelType="
				+ channelType + ",  unit=" + unit + ", insertTimestamp="
				+ insertTimestamp + ", cfgValidateTime=" + cfgValidateTime
				+ ", updateTimestamp=" + updateTimestamp + "]";
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}


}
