package com.nci.tunan.qry.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;


/**
  * DocumentVO 
  */
public class DocumentVO  extends BaseVO  {


	/** 
	* @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
	*/ 
	
	private static final long serialVersionUID = 1L;


	private String needSignature;

		
	private String needArchive;

		
	private String sortD2;

		
	private String sortD1;

		
	private String templateCode;

	
	private String status;

		
	private String needDistribute;

		
	private BigDecimal bizId2;

		
	private String sortD7;

		
	private String sortD8;

		
	/*
		
		
	private BigDecimal bizId;

		
	public void setBizId(BigDecimal bizId){
		getData().put("biz_id",bizId);
		this.bizId=bizId;
	}
	
	public BigDecimal getBizId(){
		return (BigDecimal)getData().get("biz_id");
	}
	
		
		*/
	private Date reviewTime;

		
	private String sortD3;

		
	private String needReply;

		
	
	
		
		
	/*private BigDecimal updateBy;

		
	public void setUpdateBy(BigDecimal updateBy){
		getData().put("update_by",updateBy);
		this.updateBy=updateBy;
	}
	
	public BigDecimal getUpdateBy(){
		return (BigDecimal)getData().get("update_by");
	}*/
	
		
		
	private String sortD4;

		
	private String sortD5;

		
	private String sortD6;

		
	private BigDecimal listId;

		
	private BigDecimal updaterId;

		
	private BigDecimal recorderId;

		
	private String bizCode2;

		
	private BigDecimal policyId;

		
	 /** 
	* @Fields organCode :  null
 	*/ 
	private String organCode;

		
	private String holdCase;

		
	private String sourceId;

		
	private BigDecimal signatoryId;

		
	private Date insertedTimestamp;

		
	private Date insertTime;

		
	private String allowEdit;

		
	private String bizSource;

		
	private Date updateTime;

		
	private BigDecimal reprintTimes;

		
	private String submitChannel;

		
	private String documentNo;

		
	private String policyCode;

		
	private String bizCode;

		
	private Date updatedTimestamp;

	private BigDecimal docListId;	
	
	
		
		
	/*private BigDecimal insertBy;

		
	public void setInsertBy(BigDecimal insertBy){
		getData().put("insert_by",insertBy);
		this.insertBy=insertBy;
	}
	
	public BigDecimal getInsertBy(){
		return (BigDecimal)getData().get("insert_by");
	}*/
	
		
		
	private BigDecimal replyDays;

		
	private BigDecimal batchGenerate;

		
	private BigDecimal signatoryId2;
	private String bussCode;
	private String TemplateName;
	private String txtContent; //通知书内容
	private String someName;//发送对象
	private String replyRemark;//回复结论
	
	/**
	 * 发放人
	 */
	private BigDecimal sendBy;
	/**
	 * 生成日期
	 */
	private Date createTime;
	/**
	 * 回销日期
	 */
	private Date closeTime;
	/**
	 * 通知书来源
	 */
	private String bussSourceCode;
	/**
	 * 发放对象id
	 */
	private String sendObjId;
	/**
	 * 发放内容
	 */
	private String sendData;
	/**
	 * 单证类型号码
	 */
	private String cardCode;
	

	public String getCardCode() {
		return cardCode;
	}

	public void setCardCode(String cardCode) {
		this.cardCode = cardCode;
	}
	

	public String getSendData() {
		return sendData;
	}

	public void setSendData(String sendData) {
		this.sendData = sendData;
	}

	public String getSendObjId() {
		return sendObjId;
	}

	public void setSendObjId(String sendObjId) {
		this.sendObjId = sendObjId;
	}

	public String getBussSourceCode() {
		return bussSourceCode;
	}

	public void setBussSourceCode(String bussSourceCode) {
		this.bussSourceCode = bussSourceCode;
	}
	
	
	public Date getCreateTime() {
		return createTime;
	}




	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}




	public Date getCloseTime() {
		return closeTime;
	}




	public void setCloseTime(Date closeTime) {
		this.closeTime = closeTime;
	}




	public BigDecimal getSendBy() {
		return sendBy;
	}




	public void setSendBy(BigDecimal sendBy) {
		this.sendBy = sendBy;
	}




	public String getNeedSignature() {
		return needSignature;
	}




	public void setNeedSignature(String needSignature) {
		this.needSignature = needSignature;
	}




	public String getNeedArchive() {
		return needArchive;
	}




	public void setNeedArchive(String needArchive) {
		this.needArchive = needArchive;
	}




	public String getSortD2() {
		return sortD2;
	}




	public void setSortD2(String sortD2) {
		this.sortD2 = sortD2;
	}




	public String getSortD1() {
		return sortD1;
	}




	public void setSortD1(String sortD1) {
		this.sortD1 = sortD1;
	}


	public String getTemplateCode() {
		return templateCode;
	}




	public void setTemplateCode(String templateCode) {
		this.templateCode = templateCode;
	}




	public String getStatus() {
		return status;
	}




	public void setStatus(String status) {
		this.status = status;
	}




	public String getNeedDistribute() {
		return needDistribute;
	}




	public void setNeedDistribute(String needDistribute) {
		this.needDistribute = needDistribute;
	}




	public BigDecimal getBizId2() {
		return bizId2;
	}




	public void setBizId2(BigDecimal bizId2) {
		this.bizId2 = bizId2;
	}




	public String getSortD7() {
		return sortD7;
	}




	public void setSortD7(String sortD7) {
		this.sortD7 = sortD7;
	}




	public String getSortD8() {
		return sortD8;
	}




	public void setSortD8(String sortD8) {
		this.sortD8 = sortD8;
	}




	public Date getReviewTime() {
		return reviewTime;
	}




	public void setReviewTime(Date reviewTime) {
		this.reviewTime = reviewTime;
	}




	public String getSortD3() {
		return sortD3;
	}




	public void setSortD3(String sortD3) {
		this.sortD3 = sortD3;
	}




	public String getNeedReply() {
		return needReply;
	}




	public void setNeedReply(String needReply) {
		this.needReply = needReply;
	}




	public String getSortD4() {
		return sortD4;
	}




	public void setSortD4(String sortD4) {
		this.sortD4 = sortD4;
	}




	public String getSortD5() {
		return sortD5;
	}




	public void setSortD5(String sortD5) {
		this.sortD5 = sortD5;
	}




	public String getSortD6() {
		return sortD6;
	}




	public void setSortD6(String sortD6) {
		this.sortD6 = sortD6;
	}




	public BigDecimal getListId() {
		return listId;
	}




	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}




	public BigDecimal getUpdaterId() {
		return updaterId;
	}




	public void setUpdaterId(BigDecimal updaterId) {
		this.updaterId = updaterId;
	}




	public BigDecimal getRecorderId() {
		return recorderId;
	}




	public void setRecorderId(BigDecimal recorderId) {
		this.recorderId = recorderId;
	}




	public String getBizCode2() {
		return bizCode2;
	}




	public void setBizCode2(String bizCode2) {
		this.bizCode2 = bizCode2;
	}




	public BigDecimal getPolicyId() {
		return policyId;
	}




	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}



	public String getOrganCode() {
		return organCode;
	}




	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}




	public String getHoldCase() {
		return holdCase;
	}




	public void setHoldCase(String holdCase) {
		this.holdCase = holdCase;
	}




	public String getSourceId() {
		return sourceId;
	}




	public void setSourceId(String sourceId) {
		this.sourceId = sourceId;
	}




	public BigDecimal getSignatoryId() {
		return signatoryId;
	}




	public void setSignatoryId(BigDecimal signatoryId) {
		this.signatoryId = signatoryId;
	}




	public Date getInsertedTimestamp() {
		return insertedTimestamp;
	}




	public void setInsertedTimestamp(Date insertedTimestamp) {
		this.insertedTimestamp = insertedTimestamp;
	}




	public Date getInsertTime() {
		return insertTime;
	}




	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}




	public String getAllowEdit() {
		return allowEdit;
	}




	public void setAllowEdit(String allowEdit) {
		this.allowEdit = allowEdit;
	}




	public String getBizSource() {
		return bizSource;
	}




	public void setBizSource(String bizSource) {
		this.bizSource = bizSource;
	}




	public Date getUpdateTime() {
		return updateTime;
	}




	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}




	public BigDecimal getReprintTimes() {
		return reprintTimes;
	}




	public void setReprintTimes(BigDecimal reprintTimes) {
		this.reprintTimes = reprintTimes;
	}




	public String getSubmitChannel() {
		return submitChannel;
	}




	public void setSubmitChannel(String submitChannel) {
		this.submitChannel = submitChannel;
	}




	public String getDocumentNo() {
		return documentNo;
	}




	public void setDocumentNo(String documentNo) {
		this.documentNo = documentNo;
	}




	public String getPolicyCode() {
		return policyCode;
	}




	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}




	public String getBizCode() {
		return bizCode;
	}




	public void setBizCode(String bizCode) {
		this.bizCode = bizCode;
	}




	public Date getUpdatedTimestamp() {
		return updatedTimestamp;
	}




	public void setUpdatedTimestamp(Date updatedTimestamp) {
		this.updatedTimestamp = updatedTimestamp;
	}




	public BigDecimal getReplyDays() {
		return replyDays;
	}




	public void setReplyDays(BigDecimal replyDays) {
		this.replyDays = replyDays;
	}




	public BigDecimal getBatchGenerate() {
		return batchGenerate;
	}




	public void setBatchGenerate(BigDecimal batchGenerate) {
		this.batchGenerate = batchGenerate;
	}




	public BigDecimal getSignatoryId2() {
		return signatoryId2;
	}




	public void setSignatoryId2(BigDecimal signatoryId2) {
		this.signatoryId2 = signatoryId2;
	}

	public String getBussCode() {
		return bussCode;
	}
	
	public void setBussCode(String bussCode) {
		this.bussCode = bussCode;
	}

	public String getTemplateName() {
		return TemplateName;
	}

	public void setTemplateName(String templateName) {
		TemplateName = templateName;
	}

	public String getTxtContent() {
		return txtContent;
	}

	public void setTxtContent(String txtContent) {
		this.txtContent = txtContent;
	}

	public String getSomeName() {
		return someName;
	}

	public void setSomeName(String someName) {
		this.someName = someName;
	}

	public String getReplyRemark() {
		return replyRemark;
	}

	public void setReplyRemark(String replyRemark) {
		this.replyRemark = replyRemark;
	}


	public BigDecimal getDocListId() {
        return docListId;
    }




    public void setDocListId(BigDecimal docListId) {
        this.docListId = docListId;
    }




    @Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String toString() {
		return "DocumentVO [sendData=" + sendData + "]";
	}
	
}

