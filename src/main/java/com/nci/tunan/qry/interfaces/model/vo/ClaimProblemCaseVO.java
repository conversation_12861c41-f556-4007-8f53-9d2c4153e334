package com.nci.tunan.qry.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.nci.udmp.framework.model.BaseVO;

public class ClaimProblemCaseVO extends BaseVO {
    
	private String caseStatusBPM; //生成赔案之前的案件状态
	
	private String buildReportBPM; //报案信息录入创建工作流用
	
	private BigDecimal buildReportBPMId;//报案信息录入创建工作流用
	
    /**
     * taskId
     */
    private String taskId;
    /**
     * @Fields trusteeCertiCode : 省
     */
    private String province;
    /**
     * @Fields trusteeCertiCode : 市
     */
    private String city;
    /**
     * @Fields trusteeCertiCode : 县
     */
    private String area;
    /**
     * @Fields trusteeCertiCode : 街道
     */
    private String stree;
    /**
     * @Fields trusteeCertiCode : 受托人证件号码
     */
    private String trusteeCertiCode;
    /**
     * @Fields endCaseTime : 结案时间
     */
    private Date endCaseTime;
    /**
     * @Fields updateTime : 备注时间
     */
    private Date updateTime;
    /**
     * @Fields actualPay : 实际给付金额
     */
    private BigDecimal actualPay;
    /**
     * @Fields reportMode : 报案方式
     */
    private BigDecimal reportMode;
    /**
     * @Fields approveRejectReason : 审批不通过原因
     */
    private String approveRejectReason;
    /**
     * @Fields relatedNo : 关联赔案
     */
    private String relatedNo;
    /**
     * @Fields rptrRelation : 报案人与出险人关系
     */
    private String rptrRelation;
    /**
     * @Fields overCompFlag : 超期补偿标识
     */
    private BigDecimal overCompFlag;
    /**
     * @Fields otherReason : 审核其他原因
     */
    private String otherReason;
    /**
     * @Fields repealReason : 案件撤销原因
     */
    private BigDecimal repealReason;
    /**
     * @Fields isCommon : 是否为常规给付
     */
    private BigDecimal isCommon;
    /**
     * @Fields cureHospital : 治疗医院
     */
    private String cureHospital;
    /**
     * @Fields caseNo : 赔案号
     */
    private String caseNo;
    /**
     * @Fields reviewFlag : 复核标识
     */
    private BigDecimal reviewFlag;
    /**
     * @Fields organCode : 报案管理机构
     */
    private String organCode;
    /**
     * @Fields policyOrganCode : 保单管理机构(提前)
     */
    private String policyOrganCode;
    /**
     * @Fields organName : 报案管理机构名称
     */
    private String organName;
    /**
     * @Fields policyOrganName : 保单管理机构名称(提前)
     */
    private String policyOrganName;
    /**
     * @Fields documentsStatus : 实物单证状态(提前)
     */
    private String documentsStatus;
    /**
     * @Fields advanceAskFlag : 客户是否要求预付
     */
    private BigDecimal advanceAskFlag;
    /**
     * @Fields caseFlag : 案件标识 -普通案件 -疑难案件 -诉讼案件
     */
    private BigDecimal caseFlag;
    /**
     * @Fields greenFlag : 绿色通道标识
     */
    private BigDecimal greenFlag;
    /**
     * @Fields auditRejectReason : 审核拒付原因
     */
    private String auditRejectReason;
    /**
     * @Fields balancePay : 结算金额
     */
    private BigDecimal balancePay;
    /**
     * @Fields rejectReason : 不予立案原因
     */
    private String rejectReason;
    /**
     * @Fields auditorId : 审核人ID
     */
    private BigDecimal auditorId;
    /**
     * @Fields rptrMp : 报案人手机号
     */
    private String rptrMp;
    /**
     * @Fields seriousDisease : 重大疾病
     */
    private String seriousDisease;
    /**
     * @Fields registeTime : 立案时间
     */
    private Date registeTime;
    /** 
    * @Fields startTime : 立案起期
    */ 
    private Date startTime;
    /** 
    * @Fields endTime : 立案止期 
    */ 
    private Date endTime;
    /**
     * @Fields caseApplyType : 赔案申请类型
     */
    private BigDecimal caseApplyType;
    /**
     * @Fields repealDesc : 案件撤销原因描述
     */
    private String repealDesc;
    /**
     * @Fields rptrEmail : 报案人EMAIL
     */
    private String rptrEmail;
    /**
     * @Fields trusteeCertiType : 受托人证件类型
     */
    private String trusteeCertiType;
    /**
     * @Fields insuredId : 出险人号
     */
    private BigDecimal insuredId;
    /**
     * @Fields accidentDetail : 意外细节
     */
    private String accidentDetail;
    /**
     * @Fields caseStatus : 案件状态
     */
    private String caseStatus;
    /**
     * @Fields faRenCaseStatus : 案件状态（法人）
     */
    private String faRenCaseStatus;
    /**
     * @Fields caseId : 赔案ID
     */
    private BigDecimal caseId;
    /**
     * @Fields rptrTime : 报案确认时间
     */
    private Date rptrTime;
    /**
     * @Fields auditDecision : 审核结论
     */
    private BigDecimal auditDecision;
    /**
     * @Fields registerId : 立案人ID
     */
    private BigDecimal registerId;
    /**
     * @Fields claimSource : 案件来源 TPA
     */
    private BigDecimal claimSource;
    /**
     * @Fields trusteeType : 受托人类型
     */
    private BigDecimal trusteeType;
    /**
     * @Fields approverId : 审批人ID
     */
    private BigDecimal approverId;
    /**
     * @Fields accidentId : 事件ID
     */
    private BigDecimal accidentId;
    /**
     * @Fields rejectPay : 拒付金额
     */
    private BigDecimal rejectPay;
    /**
     * @Fields signTime : 签收确认时间
     */
    private Date signTime;
    /**
     * @Fields signerId : 签收人ID
     */
    private BigDecimal signerId;
    /**
     * @Fields advancePay : 预付金额
     */
    private BigDecimal advancePay;
    /**
     * @Fields rptrName : 报案人的姓名
     */
    private String rptrName;
    /**
     * @Fields rptrAddr : 报案人地址
     */
    private String rptrAddr;
    /**
     * @Fields approveRemark : 审批意见
     */
    private String approveRemark;
    /**
     * @Fields trusteeName : 受托人姓名
     */
    private String trusteeName;
    /**
     * @Fields applyDate : 申请日期
     */
    private Date applyDate;
    /**
     * @Fields advanceFlag : 预付标识
     */
    private BigDecimal advanceFlag;
    /**
     * @Fields registeConfTime : 立案确认时间
     */
    private Date registeConfTime;
    /**
     * @Fields trusteeTel : 受托人固定电话
     */
    private String trusteeTel;
    /**
     * @Fields rptrZip : 报案人邮编
     */
    private BigDecimal rptrZip;
    /**
     * @Fields isBpo : 是否外包
     */
    private BigDecimal isBpo;
    /**
     * @Fields auditStartTime : 审核开始的时间
     */
    private Date auditStartTime;
    /**
     * @Fields auditTime : 审核时间
     */
    private Date auditTime;
    /**
     * @Fields backAuditDate : 回退审核确认时间
     */
    private Date backAuditDate;
    /**
     * @Fields approveTime : 审批时间
     */
    private Date approveTime;
    /**
     * @Fields auditRemark : 审核意见
     */
    private String auditRemark;
    /**
     * @Fields approveDecision : 审批结论
     */
    private BigDecimal approveDecision;
    /**
     * @Fields faRenApproveDecision : 审批结论(法人)
     */
    private String faRenApproveDecision;
    /**
     * @Fields trusteeCode : 受托人代码
     */
    private String trusteeCode;
    /**
     * @Fields doorSignTime : 上门签收日期
     */
    private Date doorSignTime;
    /**
     * @Fields comfortFlag : 慰问标识
     */
    private BigDecimal comfortFlag;
    /**
     * @Fields acceptTime : 受理时间
     */
    private Date acceptTime;
    /**
     * @Fields isDeductFlag : 是否扣减欠缴费用
     */
    private BigDecimal isDeductFlag;
    /**
     * @Fields medDept : 医疗科室
     */
    private String medDept;
    /**
     * @Fields doctorName : 医生姓名
     */
    private String doctorName;
    /**
     * @Fields trusteeMp : 受托人手机码
     */
    private String trusteeMp;
    /**
     * @Fields cureStatus : 治疗情况
     */
    private String cureStatus;
    /**
     * @Fields acceptDecision : 立案结论
     */
    private BigDecimal acceptDecision;
    /**
     * @Fields acceptorId : 受理人ID
     */
    private BigDecimal acceptorId;
    /**
     * @Fields caseSubStatus : 案件子状态
     */
    private String caseSubStatus;
    /**
     * @Fields calcPay : 理算金额
     */
    private BigDecimal calcPay;
    /**
     * @Fields userRealName :报案受理人的名字
     */
    private String userRealName;
    /**
     * @Fields systemDateString :获取系统的时间
     */
    private String systemDateString;

    private Integer customerSex;
    /**
     * @Fields policyCode : 保单号
     */
    private String policyCode;
    /**
     * @Fields claimType : 理赔类型
     */
    private String claimType;
    /**
     * @Fields uwStatus : 二核状态
     */
    private String uwStatus;
    /**
     * @Fields uwStatus :  出险人的出生日期
     */
    private Date customerBirthday;

    //匹配理算类型 0：预算    1： 理算
    private String calcFlag;

    /**
     * @Fields policyType :保单类型
     */
    private String policyType;
    /**
     * @Fields productAbbrName :险种名称
     */
    private String productAbbrName;
    /**
     * @Fields fee_status :支付状态
     */
    private String feeStatus;
    /**
     * @Fields fee_status_date :支付日期
     */
    private Date feeStatusDate;

    private String approverName; //审批人
    
    private String auditorName; //审核人
    
    /** 
     * @Fields auditDecisionStr : 审核结论
     */ 
     private String auditDecisionStr;
    
    private String userName; //用户代码
    
    private String insuredName; //出险人姓名
    
    private String insuredCode; //出险人证件号码
    /**
     * @Fields surveyStatus : 调查标识
     */
    private String surveyStatus;
    //豁免处理页面相关字段
    /** 
     * @Fields policyId :  保单ID
     */ 
    private BigDecimal policyId;
     /** 
      * @Fields busiItemId :  业务险种ID
      */ 
    private BigDecimal busiItemId;
    /** 
     * @Fields productNameStd :  险种名称
     */ 
    private String productNameStd;
    /** 
     * @Fields productCodeStd :  险种代码
     */ 
    private String productCodeStd;
    /** 
     * @Fields liabilityState :  险种效力状态
     */ 
    private BigDecimal liabilityState;
    /** 
     * @Fields paidupString :缴费终止日期
     */ 
    private String paidupString;
    /** 
     * @Fields paidupDate :  缴费终止日期
     */ 
    private Date paidupDate;
     /** 
      * @Fields totalPremAf :  总保费
      */ 
    private BigDecimal totalPremAf;
      /** 
       * @Fields itemId :  责任组ID
       */ 
    private BigDecimal itemId;
    /** 
     * @Fields initialType :  当期缴费频率
     */ 
     private BigDecimal initialType;
     /** 
      * @Fields initialType :  当期缴费频率
      */ 
     private String chargeName;
     /** 
      * @Fields isWaived :  豁免标识
      */ 
     private BigDecimal isWaived;
     private String isWaivedString;
     /** 
      * @Fields waiveReason :  豁免原因
      */ 
     private String waiveReason;
     /** 
      * @Fields waiveReason :  豁免原因名字
      */ 
     private String waiveReasonName;
     /** 
      * @Fields dueDate :  下期缴费日期
      */ 
      private Date dueDate;
       /** 
      * @Fields waiveStart :  豁免起期
      */ 
      private Date waiveStart;
      /** 
       * @Fields waiveStartString :  豁免起期
       */ 
       private String waiveStartString;
      /** 
       * @Fields waiveAmt :  豁免总金额
       */ 
       private BigDecimal waiveAmt;
        /** 
       * @Fields waiveEnd :  豁免止期
       */ 
     private Date waiveEnd;
    /** 
     * @Fields waiveEndString :  豁免止期
     */ 
     private String waiveEndString;
    /**
     * @Fields waiveDesc : 豁免描述
     */
     private String waiveDesc;
     /** 
      * @Fields payDueDate : 下期应缴费日
      */ 
     private Date payDueDate;
     /** 
      * @Fields payDueDateString : 下期应缴费日
      */ 
     private String payDueDateString;
     /** 
      * @Fields policyPeriod :  缴费次数
      */ 
     private BigDecimal policyPeriod; 
     /** 
      * @Fields nextPrem :  下期应缴费金额
      */ 
     private BigDecimal nextPrem; 
    /** 
     * @Fields expiryDate :  保单终止日期
     */ 
    private String expiryDate;
    /** 
     * @Fields validdateDate :  生效日期
     */ 
    private Date validdateDate;
    /** 
     * @Fields validdateDateString :  生效日期
     */ 
    private String validdateDateString;
     /** 
      * @Fields payeeNum :  领款人数量
      */ 
    private BigDecimal payeeNum;
    /** 
     * @Fields channelType :  渠道类型
     */ 
    private String channelType;
    /** 
     * @Fields liabId :  责任编码
     */ 
    private BigDecimal liabId;
    /** 
     * @Fields claimLiabId :  责任主键Id
     */ 
    private BigDecimal claimLiabId;
    /**
     * 理赔事件信息
     */
    private ClaimAccidentVO claimAccident = new ClaimAccidentVO();
    
    private String OrganCodeDate; //立案时候的管理机构代码
    
    private Date documentSDate; //单证生成时间起始
    
    private Date documentEDate; //单证生成时间结束
    
    private String documentType; //单证类型
    
    private String documentName; //单证名称
    /**
     * 传递报文数据 
     */
    private String context;

    private Date claimDate; //出险日期
    private String claimDateStr; //出险日期
    
    private BigDecimal rePrintTimes; //打印次数
    
    private Date printTime; //打印时间
    
    private String documentNo; //通知书号
    
    private String documentTName; //通知书名称
    
    private BigDecimal docListId; //通知书主键
    
    private String claimDateAll; //赔案关联所有出险日期
    
    private String caseResult; //赔案关联所有出险结果
    
    private String visitorName; //回访人
    
    private String visitMarker; //回访标志
    
    private Date nextVisitDate; //下次回访日期
    
    private BigDecimal careTimes; //关怀次数
    
    private String checkAll; //选择的单证集合
    
    private String seleNo; //每页显示条数
    
    private String seleNohid;
    
    private String casePermissionName; //案件审批权限
    
    private String accidentNo; //事件号
    
    private String problemNo; //问题件号
    
    private String cardCode; //单证代码列表
    private String cardCodeName; //单证代码列表
    private BigDecimal checked; //是否被选择
    private String rptrCode; //当报案人是业务员时，存放业务员代码
    /** 
     * @Fields rate :  抽取比例
     */ 
    private BigDecimal rate;
     /** 
      * @Fields limit :  抽取限额
      */ 
    private BigDecimal limit;
    /** 
     * @Fields claimRate :  赔付金额与已缴纳保费比例
     */ 
    private BigDecimal claimRate;
     
    /** 
     * @Fields rptrId :  报案人ID
     */
    private BigDecimal rptrId;
    
   //当前用户ID
    private BigDecimal currentUserId;
    
    //当前时间
    private Date currentDate;
    //外包商ID
    private BigDecimal outsourceId;
    
    /**
     * 审核提示信息内容
     */
    private String taskMessage;
    
    /** 
     * @Fields isSms :  是否给客户发送短信
	*/ 
    private String isSms;
    /** 
	* @Fields isEml :  是否给客户发送邮件
	*/ 
    private String isEml;
    /** 
	* @Fields isSendCustomer :  是否发送给客户
	*/ 
    private String isSendCustomer;
    
    /** 
	* @Fields  trusteeEmail :  业务员发送邮件
	*/ 
    private String  trusteeEmail;
    
    private String auditPermissionName;//审核权限
    
    private String approvePermissionName;//审批权限
   
    private String isPassIlog;  //审批自核是否通过
    
    private BigDecimal printBy; //打印人
    
    private String printByName; //打印人名称
    
    private String caseStatusName; //案件状态名称
    
    private String checkPrint; //单证打印是否选中
    
    private BigDecimal accReason; //出险原因
    private String accReasonName; //出险原因名称
    
//    private String OrganName; //机构名称
    
    private String memoType; // 备注问题件类型

    private BigDecimal memoId; // 备注ID

    private String memoContent; // 备注内容

    private String memoOption; // 备注选项
    
    private String accDays;  //保单生效日距出险日天数
    
    private String policyCodeStrs; //多个保单号
    
    private String checklistproblem; //问题件
    
    private String checklistproblemFlag; //是否为问题件
    
    private String surveyResultInfo; //调查结果详细信息
    
    private boolean checkType; //新增签收时判断是否要提示阻断信息
    
    private String channelCode; //受理渠道
    
    private String channelCodeName; //受理渠道名称
    
    private BigDecimal isAutoHungup;//是否自动挂起
    
    private String specialRemarkCode;//特殊备注
    
    private String lossReasonCode;//损失原因
    
    private String lossLevelCode;//损失程度
    
    private String subTaskCode;//子任务号
    
    private Date accDate; //事故日期
    /**
     * 外包简易标识
     */
    private String bpoEasyFlag;
    
    /**
     * @Fields uwId : 核保id
     */
    private BigDecimal uwId;
    /**
     * 核保保单Id
     */
    private BigDecimal uwPolicyId;
    /**
     * 打印类型
     */
    private String printType;
    /**
     * 签收机构
     */
    private String signOrgan;
    /**
     * 立案机构
     */
    private String registeOrgan;
    /**
     * 审核机构
     */
    private String auditOrgan;
    /**
     * 审批机构
     */
    private String approveOrgan;
    
    /**
     * 简易案件审核结论
     */
    private BigDecimal easyAuditDecision;
    
    /**
     * 简易案件审核人
     */
    private BigDecimal easyAuditorId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 拒付备注
     */
    private String rejectRemarks;
    
    /**
     * 是否发起二核
     */
    private String isNoUw;
    /**
     * 实时支付标识
     */
    private BigDecimal realtimePay;
    
    //外包商名称
    private String  outSourceName;
    
    //立案人姓名
    private String  registerName;
    //立案人代码
    private String  registerCode;
    
    private BigDecimal isChecklist;
    
    /** 
    * @Fields asignPageSize : 自动分配批处理数量控制
    */ 
    private String asignPageSize;
     /** 
    * @Fields asignTaskType : 自动分配批处理任务类型
    */ 
    private String asignTaskType;
    /** 
    * @Fields asignTaskArea : 自动分配批处理任务片区
    */ 
    private String asignTaskArea;
    
    /**
     *  诉讼案件标识
     */
    private BigDecimal isLawsuits;
    
    
    /** 
     * @Fields auditorCode : 审核人代码
     */ 
     private String auditorCode; 
     /** 
      * @Fields approverCode : 审批人代码
      */ 
      private String approverCode; 
      
      /**
       * 是否发起调查
       */
      private BigDecimal surveyMark;
      /**
       * 阳性标识
       */
      private BigDecimal positiveFlag;
      
      /**
       * @Fields actualPay : 行业共享信息有效性
       */
      private BigDecimal shareConditionValid;
      
      /**
       * @Fields actualPay : 行业共享信息无效原因
       */
      private String shareConditionDecision;
      /**
  	 * @Fields insurancePaidFlag : 是否在商业保司理赔(T_YES_NO)
  	 */
  	private BigDecimal insurancePaidFlag;  
  	
  	/** 
	* @Fields signMessage : 签收警告提示
	*/ 
	private String signMessage;
	 /**
     * @Fields claimIdentIfAtion : 赔案标识
     */
    private BigDecimal claimIdentIfAtion;
	
    /**
     * 出险结果与本次赔案相关性
     */
    private String isCorrelation;
	
	/**
    * 注销验真结果(0-已注销,1-未注销,2-库中无此号)
    */
    private BigDecimal logoutCheckResult;
    
    /**
 	* 险种代码
 	*/
    private String busiProdCode;
    /** 
    * @Fields acquistWay : 采集方式
    */ 
    private BigDecimal acquistWay;
    
    private String acquistWayName;
    
    /** 
    *  outsourceWay : 外包方式
    */ 
    private BigDecimal outsourceWay;
      
    private String outsourceWayName;
	/**
     * @Fields productType : 调用产品类型
     */
    private String productType; 
    
    /**
     * @Fields riskResult : 风险提示结果
     */
    private String riskResult; 
    /** 
    * @Fields elecCheckFlag : 电票校验管控 
    */ 
    private BigDecimal elecCheckFlag;
    /**
     * @Fields lockFlag : 锁定解锁标识
     */
    private BigDecimal lockFlag; 
    /**
     * @Fields lockSys : 锁定解锁系统
     */
    private String lockSys;
    /**
     * @Fields AutoCaseNo : 自助理赔申请号
     */
    private String AutoCaseNo; 
    
    /**
     * @Fields AutoCaseType : 个团案件标识
     */
    private BigDecimal AutoCaseType; 
    /**
     * @Fields servCom : 直赔服务商
     */
    private String servCom; 
    
    
    
  	public String getServCom() {
		return servCom;
	}

	public void setServCom(String servCom) {
		this.servCom = servCom;
	}
    
  	public BigDecimal getLockFlag() {
		return lockFlag;
	}

	public void setLockFlag(BigDecimal lockFlag) {
		this.lockFlag = lockFlag;
	}

	public String getLockSys() {
		return lockSys;
	}

	public void setLockSys(String lockSys) {
		this.lockSys = lockSys;
	}
     
 	public String getAutoCaseNo() {
		return AutoCaseNo;
	}

	public void setAutoCaseNo(String autoCaseNo) {
		AutoCaseNo = autoCaseNo;
	}

	public BigDecimal getAutoCaseType() {
		return AutoCaseType;
	}

	public void setAutoCaseType(BigDecimal autoCaseType) {
		AutoCaseType = autoCaseType;
	}
    
	public BigDecimal getElecCheckFlag() {
		return elecCheckFlag;
	}

	public void setElecCheckFlag(BigDecimal elecCheckFlag) {
		this.elecCheckFlag = elecCheckFlag;
	}

	public String getAcquistWayName() {
		return acquistWayName;
	}

	public void setAcquistWayName(String acquistWayName) {
		this.acquistWayName = acquistWayName;
	}

	public String getOutsourceWayName() {
		return outsourceWayName;
	}

	public void setOutsourceWayName(String outsourceWayName) {
		this.outsourceWayName = outsourceWayName;
	}

  	public BigDecimal getOutsourceWay() {
  		return outsourceWay;
  	}

  	public void setOutsourceWay(BigDecimal outsourceWay) {
  		this.outsourceWay = outsourceWay;
  	}

  	public BigDecimal getAcquistWay() {
  		return acquistWay;
  	}

  	public void setAcquistWay(BigDecimal acquistWay) {
  		this.acquistWay = acquistWay;
  	}

	public String getIsCorrelation() {
		return isCorrelation;
	}
	
	public void setIsCorrelation(String isCorrelation) {
		this.isCorrelation = isCorrelation;
	}

	public BigDecimal getLogoutCheckResult() {
		return logoutCheckResult;
	}

	public void setLogoutCheckResult(BigDecimal logoutCheckResult) {
		this.logoutCheckResult = logoutCheckResult;
	}
	
  	public String getSignMessage() {
		return signMessage;
	}

	public void setSignMessage(String signMessage) {
		this.signMessage = signMessage;
	}
  	
	public BigDecimal getClaimIdentIfAtion() {
		return claimIdentIfAtion;
	}

	public void setClaimIdentIfAtion(BigDecimal claimIdentIfAtion) {
		this.claimIdentIfAtion = claimIdentIfAtion;
	}
    	public BigDecimal getInsurancePaidFlag() {
  		return insurancePaidFlag;
  	}

  	public void setInsurancePaidFlag(BigDecimal insurancePaidFlag) {
  		this.insurancePaidFlag = insurancePaidFlag;
  	}
      
  	public BigDecimal getShareConditionValid() {
  		return shareConditionValid;
  	}

  	public void setShareConditionValid(BigDecimal shareConditionValid) {
  		this.shareConditionValid = shareConditionValid;
  	}

  	public String getShareConditionDecision() {
  		return shareConditionDecision;
  	}

  	public void setShareConditionDecision(String shareConditionDecision) {
  		this.shareConditionDecision = shareConditionDecision;
  	}
     
     public String getAuditorCode() {
		return auditorCode;
	}

	public void setAuditorCode(String auditorCode) {
		this.auditorCode = auditorCode;
	}

	public String getApproverCode() {
		return approverCode;
	}

	public void setApproverCode(String approverCode) {
		this.approverCode = approverCode;
	}

	public BigDecimal getSurveyMark() {
		return surveyMark;
	}

	public void setSurveyMark(BigDecimal surveyMark) {
		this.surveyMark = surveyMark;
	}

	public BigDecimal getPositiveFlag() {
		return positiveFlag;
	}

	public void setPositiveFlag(BigDecimal positiveFlag) {
		this.positiveFlag = positiveFlag;
	}

	public String getProductType() {
		return productType;
	}

	public void setProductType(String productType) {
		this.productType = productType;
	}

	public String getRiskResult() {
		return riskResult;
	}

	public void setRiskResult(String riskResult) {
		this.riskResult = riskResult;
	}
    
 	public String getAsignTaskType()
    {
        return asignTaskType;
    }

    public void setAsignTaskType(String asignTaskType)
    {
        this.asignTaskType = asignTaskType;
    }

    public String getAsignTaskArea()
    {
        return asignTaskArea;
    }

    public void setAsignTaskArea(String asignTaskArea)
    {
        this.asignTaskArea = asignTaskArea;
    }
	public String getAsignPageSize() {
		return asignPageSize;
	}

	public void setAsignPageSize(String asignPageSize) {
		this.asignPageSize = asignPageSize;
	}

	public BigDecimal getIsChecklist() {
		return isChecklist;
	}

	public void setIsChecklist(BigDecimal isChecklist) {
		this.isChecklist = isChecklist;
	}
    
	public String getRegisterName() {
		return registerName;
	}

	public void setRegisterName(String registerName) {
		this.registerName = registerName;
	}

	public String getRegisterCode() {
		return registerCode;
	}

	public void setRegisterCode(String registerCode) {
		this.registerCode = registerCode;
	}

	public String getOutSourceName() {
		return outSourceName;
	}

	public void setOutSourceName(String outSourceName) {
		this.outSourceName = outSourceName;
	}

	public String getIsNoUw() {
		return isNoUw;
	}

	public void setIsNoUw(String isNoUw) {
		this.isNoUw = isNoUw;
	}

	public String getRejectRemarks() {
		return rejectRemarks;
	}

	public void setRejectRemarks(String rejectRemarks) {
		this.rejectRemarks = rejectRemarks;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
    /**
     * 
     * SPECIFIC_CAUSE 具体事由
     */
    private String specificCause;
    
    
    /**
     * 拒付通知书标识
     */
    private String decisionNoticeFlag;
    
    /**
     * 拒付通知书标识
     */
    private String contractRelieveFlag;
    
    
    
    public String getDecisionNoticeFlag() {
		return decisionNoticeFlag;
	}

	public void setDecisionNoticeFlag(String decisionNoticeFlag) {
		this.decisionNoticeFlag = decisionNoticeFlag;
	}

	public String getContractRelieveFlag() {
		return contractRelieveFlag;
	}

	public void setContractRelieveFlag(String contractRelieveFlag) {
		this.contractRelieveFlag = contractRelieveFlag;
	}
	/**
     * 医疗合计金额标准
     */
    private String medicalAmountCriterion;
    /**
     * 医疗合计金额档次
     */
    private BigDecimal medicalAmountFlag;
    /**
     * 医疗合计金额
     */
    private BigDecimal medicalTotalMony;
    
    /**
     * 是否风险
     */
    private BigDecimal isRisk;
    
    /**
     * 
     *  风险选项
     */
    private String riskLabel;
    
    /**
     * 
     * 风险其他原因
     */
    private String riskOtherReason;
    
    /**
     * 是否可撤销二核
     */
    private String isCancelUw;
    /**
     * 三十七
     */
    private int mCaseThreeSeven;
    /**
     * mCaseThreeFourty
     */
    private int mCaseThreeFourty;
    /**
     * 三二一
     */
    private int mCaseThreeTwoOne;
    /**
     * 三二一
     */
    private int mCaseThreeTwoOneH;
    /**
     * eCaseThreeAD11
     */
    private int eCaseThreeAD11;
    /**
     * eCaseThreeAD11
     */
    private int eCaseThreeAD12;
    /**
     * eCaseThreeAD13
     */
    private int eCaseThreeAD13;
    /**
     * eCaseThreeAD14
     */
    private int eCaseThreeAD14;
    /**
     * eCaseThreeAP28
     */
    private int eCaseThreeAP28;
    /**
     * eCaseThreeAP30
     */
    private int eCaseThreeAP30;
    /**
     * 移动签收人员类型
     */
    private BigDecimal signUserType;
    /**
     * 移动签收人员代码
     */
    private String signUserCode;
    /**
     * 移动签收人员姓名
     */
    private String signUserName;
    /**
     * 受益人人数
     */
    private BigDecimal beneCount;
    
    /**
     * 逆选择风险等级
     */
    private String inverseRiskLevel;
    /**
     * 虚假发票风险等级
     */
    private String invoiceRiskLevel;
    /**
     * 整案风险等级
     */
    private String caseRiskLevel;
    /**
     * 代理人层风险等级
     */
    private String agentRiskLevel;
    /**
     * 客户层风险等级
     */
    private String customerRiskLevel;
    /**
     * 大案预警标识
     */
    private BigDecimal  earlyWarning; 
    /**
     * 清单类型
     */
    private String listType;
    /**
     * 风险类型
     */
    private String riskType;
    /**
     * 免材料标识
     */
    private BigDecimal  materialFreeFlag;
    /**
     * 人脸识别标识
     */
    private BigDecimal  faceRecognitionFlag;
    /**
     * 电子签名标识
     */
    private BigDecimal  signatureTraceFlag;
    /**
     * 医疗直连标识
     */
    private BigDecimal  medicalConnectionFlag; 
    /**
     * 重复账单类型
     */
    private String  repeatNumberType;
    /**
     * 其他原因（重复账单类型为其他时的原因）
     */
    private String  repeatNumberReason;
    /**
     * 重复账单标识
     */
    private BigDecimal repeatNumberFlag;
    
    /**
     * 业务员自保件(T_YES_NO)
     */
    private BigDecimal salesmanSelfInsurance;

    /**
     * 欺诈案件占比
     */
    private String fraudRate;
    
    /**
     * 欺诈金额占比
     */
    private String fraudAmountRate;
    
    /**
     * 欺诈案件追诉率
     */
    private String fraudBackRate;
    
    /**
     * 反欺诈挽损比率
     */
    private String fraudBackAmountRate;
    /**
     * 用于校验是否发起再保
     */
    private String calcPayFlag;
    

	public String getCalcPayFlag() {
        return calcPayFlag;
    }

    public void setCalcPayFlag(String calcPayFlag) {
        this.calcPayFlag = calcPayFlag;
    }
    
    /** 
     * @Fields diagnosisTime : 确诊时间
     */ 
    private Date diagnosisTime;
    
    /**
     * @Fields reportSource : 报案渠道来源
     */
    private String reportSource;
    
    /**
     * @Fields isCalledBack :是否已回呼
     */
    private BigDecimal isCalledBack;
    
    /**
     * @Fields isCalledBackName : 是否已回呼名称
     */
    private String isCalledBackName;
    
    /**
    * @Fields reportModeName : 报案方式名称
 	*/
	private String reportModeName;
    
    
    public String getReportSource() {
		return reportSource;
	}

	public void setReportSource(String reportSource) {
		this.reportSource = reportSource;
	}

	public BigDecimal getIsCalledBack() {
		return isCalledBack;
	}

	public void setIsCalledBack(BigDecimal isCalledBack) {
		this.isCalledBack = isCalledBack;
	}
	
	
    public String getIsCalledBackName() {
		return isCalledBackName;
	}

	public void setIsCalledBackName(String isCalledBackName) {
		this.isCalledBackName = isCalledBackName;
	}

	public String getReportModeName() {
		return reportModeName;
	}

	public void setReportModeName(String reportModeName) {
		this.reportModeName = reportModeName;
	}
	/**
     * @Fields assigneeStartDate : 受托人证件有效起期
     */
    private Date assigneeStartDate;
    
    /**
     * @Fields assigneeEndDate : 受托人证件有效止期
     */
    private Date assigneeEndDate;
    
    
	public Date getAssigneeStartDate() {
		return assigneeStartDate;
	}

	public void setAssigneeStartDate(Date assigneeStartDate) {
		this.assigneeStartDate = assigneeStartDate;
	}

	public Date getAssigneeEndDate() {
		return assigneeEndDate;
	}

	public void setAssigneeEndDate(Date assigneeEndDate) {
		this.assigneeEndDate = assigneeEndDate;
	}

    
    /**
     * 审核进入个人池时间
     */
    private Date auditIndividualPoolTime;
    /**
     * 审批进入个人池时间
     */
    private Date approveIndividualPoolTime;
    
    /**
     * @Fields preAuditId : 核赔岗前用户Id
     */
    private BigDecimal preAuditId; 
    /**
     * @Fields reAuditDecision : 复核结论
     */
    private BigDecimal reAuditDecision; 
    
    /**
     * @Fields reAuditDecision : 复核结论
     */
    private String reAuditDecisionStr; 
    /**
 	 * 直连调取标识
 	 */
 	private BigDecimal directApplyFlag;
 	
 	/**
	 * 调取起期
	 */
	private Date directApplyTimeSta;
	
	/**
	 * 调取止期
	 */
	private Date directApplyTimeEnd;
	
	/**
	 * 数据问题件标识
	 */
	private BigDecimal directProFlag;
	
	/**
	 * 调取日期
	 */
	private Date directApplyTime;
	
	/**
	 * 回传日期
	 */
	private Date directBackTime;
     
	/**
	 * 签收起期
	 */
	private Date acceptTimeS;
	/**
	 * 签收止期
	 */
	private Date acceptTimeE;

	/**
	 * 服务商
	 */
	private String businessName;
    
	 /**
     * 二次回退问题件是否完成标识
     */
    private BigDecimal isProblemSolve;
    

	public BigDecimal getIsProblemSolve() {
		return isProblemSolve;
	}

	public void setIsProblemSolve(BigDecimal isProblemSolve) {
		this.isProblemSolve = isProblemSolve;
	}
 	
 /**
  * 超期支付-	应收应付表主键
  */
 private BigDecimal arapListId; 
 /**
  * 超期支付-	超期支付原因（T_OVER_PAY_REASON）
  */
 private BigDecimal overPayReason;
 /**
  * 超期支付-	超期支付天数
  */
 private BigDecimal overPayDays; 
 /**
  * 超期支付-	超期支付补偿金额
  */
 private BigDecimal overPayMoney; 
 /**
  * 超期支付-	超期支付核定人员代码
  */
 private BigDecimal overPayPerson; 
 /**
  * 超期支付-	超期支付核定日期
  */
 private Date overPayDate; 
 /**
  * 超期支付-	是否完成核定(T_YES_NO)
  */
 private BigDecimal overPayFlag;  
 /**
  * 超期支付-	excel清单是否完成核定
  */
 private String overPayFlagExcel;
 /**
  * 超期支付-	excel清单超期支付原因
  */
 private String overPayReasonExcel;
 /**
  * 超期支付-	excel清单超期支付金额
  */
 private String overPayMoneyExcel;
 /**
  * 插入时间
  */
 private Date insertTime;
 
 public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}
 
	public BigDecimal getOriginOverDays() {
	return originOverDays;
}

public void setOriginOverDays(BigDecimal originOverDays) {
	this.originOverDays = originOverDays;
}

public String getOverPayMoneyExcel() {
		return overPayMoneyExcel;
	}

	public void setOverPayMoneyExcel(String overPayMoneyExcel) {
		this.overPayMoneyExcel = overPayMoneyExcel;
	}
 
	public String getOverPayFlagExcel() {
		return overPayFlagExcel;
	}

	public void setOverPayFlagExcel(String overPayFlagExcel) {
		this.overPayFlagExcel = overPayFlagExcel;
	}

	public String getOverPayReasonExcel() {
		return overPayReasonExcel;
	}

	public void setOverPayReasonExcel(String overPayReasonExcel) {
		this.overPayReasonExcel = overPayReasonExcel;
	}
 
 public BigDecimal getArapListId() {
	return arapListId;
}

public void setArapListId(BigDecimal arapListId) {
	this.arapListId = arapListId;
}

public BigDecimal getOverPayReason() {
	return overPayReason;
}

public void setOverPayReason(BigDecimal overPayReason) {
	this.overPayReason = overPayReason;
}

public BigDecimal getOverPayDays() {
	return overPayDays;
}

public void setOverPayDays(BigDecimal overPayDays) {
	this.overPayDays = overPayDays;
}

public BigDecimal getOverPayMoney() {
	return overPayMoney;
}

public void setOverPayMoney(BigDecimal overPayMoney) {
	this.overPayMoney = overPayMoney;
}

public BigDecimal getOverPayPerson() {
	return overPayPerson;
}

public void setOverPayPerson(BigDecimal overPayPerson) {
	this.overPayPerson = overPayPerson;
}

public Date getOverPayDate() {
	return overPayDate;
}

public void setOverPayDate(Date overPayDate) {
	this.overPayDate = overPayDate;
}

public BigDecimal getOverPayFlag() {
	return overPayFlag;
}

public void setOverPayFlag(BigDecimal overPayFlag) {
	this.overPayFlag = overPayFlag;
}
 	public String getBusinessName() {
		return businessName;
	}

	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}

	public Date getAcceptTimeE() {
		return acceptTimeE;
	}

	public void setAcceptTimeE(Date acceptTimeE) {
		this.acceptTimeE = acceptTimeE;
	}

	public Date getAcceptTimeS() {
		return acceptTimeS;
	}

	public void setAcceptTimeS(Date acceptTimeS) {
		this.acceptTimeS = acceptTimeS;
	}
  	public Date getDirectApplyTime() {
		return directApplyTime;
	}

	public void setDirectApplyTime(Date directApplyTime) {
		this.directApplyTime = directApplyTime;
	}

	public Date getDirectBackTime() {
		return directBackTime;
	}

	public void setDirectBackTime(Date directBackTime) {
		this.directBackTime = directBackTime;
	}

	public BigDecimal getDirectApplyFlag() {
		return directApplyFlag;
	}

	public void setDirectApplyFlag(BigDecimal directApplyFlag) {
		this.directApplyFlag = directApplyFlag;
	}

	public Date getDirectApplyTimeSta() {
		return directApplyTimeSta;
	}

	public void setDirectApplyTimeSta(Date directApplyTimeSta) {
		this.directApplyTimeSta = directApplyTimeSta;
	}

	public Date getDirectApplyTimeEnd() {
		return directApplyTimeEnd;
	}

	public void setDirectApplyTimeEnd(Date directApplyTimeEnd) {
		this.directApplyTimeEnd = directApplyTimeEnd;
	}

	public BigDecimal getDirectProFlag() {
		return directProFlag;
	}

	public void setDirectProFlag(BigDecimal directProFlag) {
		this.directProFlag = directProFlag;
	}
    
    public String getReAuditDecisionStr() {
		return reAuditDecisionStr;
	}

	public void setReAuditDecisionStr(String reAuditDecisionStr) {
		this.reAuditDecisionStr = reAuditDecisionStr;
	}
    
    /**
     * 复核意见
     */
    private String reAuditOpinion;
    
    /** 
	* @Fields preAuditId :  核赔岗前用户编码
 	*/ 
	private String preAuditCode;
	
	  /**
     * @Fields isSmalLCase : 小额理赔案件标识(0不是,1是)
     */
    private BigDecimal isSmalLCase;
    
	/**
	 * @Fields jobPrompt :  作业提示
	 */
    private String jobPrompt;
	

  	public String getJobPrompt() {
		return jobPrompt;
	}

	public void setJobPrompt(String jobPrompt) {
		this.jobPrompt = jobPrompt; 
	}
    
    

	public BigDecimal getIsSmalLCase() {
		return isSmalLCase;
	}

	public void setIsSmalLCase(BigDecimal isSmalLCase) {
		this.isSmalLCase = isSmalLCase;
	}
	
	 /** 
	* @Fields preAuditId :  核赔岗前用户姓名
 	*/ 
	private String preAuditName;
	
	 private BigDecimal flagPermission;
    
    
    public BigDecimal getFlagPermission() {
		return flagPermission;
	}

	public void setFlagPermission(BigDecimal flagPermission) {
		this.flagPermission = flagPermission;
	}
	
	
	public void setPreAuditCode(String preAuditCode) {
		this.preAuditCode = preAuditCode;
	}
	
	public String getPreAuditCode() {
		return preAuditCode;
	}
	
	 public void setPreAuditName(String preAuditName) {
			this.preAuditName = preAuditName;
		}
		
		public String getPreAuditName() {
			return preAuditName;
		}
    
    
    public BigDecimal getPreAuditId(){
   	 return preAuditId;
	}

	public void setPreAuditId(BigDecimal preAuditId){
		this.preAuditId = preAuditId;
	}
	
	public BigDecimal getReAuditDecision(){
		return reAuditDecision;
	}

	public void setReAuditDecision(BigDecimal reAuditDecision){
		this.reAuditDecision = reAuditDecision;
	}
	
	
	public void setReAuditOpinion(String reAuditOpinion) {
		this.reAuditOpinion = reAuditOpinion;
	}

	public String getReAuditOpinion() {
		return reAuditOpinion;
		}

	public Date getAuditIndividualPoolTime() {
		return auditIndividualPoolTime;
	}

	public void setAuditIndividualPoolTime(Date auditIndividualPoolTime) {
		this.auditIndividualPoolTime = auditIndividualPoolTime;
	}

	public Date getApproveIndividualPoolTime() {
		return approveIndividualPoolTime;
	}

	public void setApproveIndividualPoolTime(Date approveIndividualPoolTime) {
		this.approveIndividualPoolTime = approveIndividualPoolTime;
	}
    
	public BigDecimal getFaceRecognitionFlag() {
		return faceRecognitionFlag;
	}

	public void setFaceRecognitionFlag(BigDecimal faceRecognitionFlag) {
		this.faceRecognitionFlag = faceRecognitionFlag;
	}

	public Date getDiagnosisTime() {
		return diagnosisTime;
	}

	public void setDiagnosisTime(Date diagnosisTime) {
		this.diagnosisTime = diagnosisTime;
	}

    public String getFraudRate() {
		return fraudRate;
	}

	public void setFraudRate(String fraudRate) {
		this.fraudRate = fraudRate;
	}

	public String getFraudAmountRate() {
		return fraudAmountRate;
	}

	public void setFraudAmountRate(String fraudAmountRate) {
		this.fraudAmountRate = fraudAmountRate;
	}

	public String getFraudBackRate() {
		return fraudBackRate;
	}

	public void setFraudBackRate(String fraudBackRate) {
		this.fraudBackRate = fraudBackRate;
	}

	public String getFraudBackAmountRate() {
		return fraudBackAmountRate;
	}

	public void setFraudBackAmountRate(String fraudBackAmountRate) {
		this.fraudBackAmountRate = fraudBackAmountRate;
	}

	public BigDecimal getSalesmanSelfInsurance() {
        return salesmanSelfInsurance;
    }

    public void setSalesmanSelfInsurance(BigDecimal salesmanSelfInsurance) {
        this.salesmanSelfInsurance = salesmanSelfInsurance;
    }

    public BigDecimal getMaterialFreeFlag() {
		return materialFreeFlag;
	}

	public void setMaterialFreeFlag(BigDecimal materialFreeFlag) {
		this.materialFreeFlag = materialFreeFlag;
	}

	public BigDecimal getMedicalConnectionFlag() {
		return medicalConnectionFlag;
	}

	public void setMedicalConnectionFlag(BigDecimal medicalConnectionFlag) {
		this.medicalConnectionFlag = medicalConnectionFlag;
	}

	public String getRepeatNumberType() {
		return repeatNumberType;
	}

	public void setRepeatNumberType(String repeatNumberType) {
		this.repeatNumberType = repeatNumberType;
	}

	public String getRepeatNumberReason() {
		return repeatNumberReason;
	}

	public void setRepeatNumberReason(String repeatNumberReason) {
		this.repeatNumberReason = repeatNumberReason;
	}

	public BigDecimal getRepeatNumberFlag() {
		return repeatNumberFlag;
	}

	public void setRepeatNumberFlag(BigDecimal repeatNumberFlag) {
		this.repeatNumberFlag = repeatNumberFlag;
	}

	public String getListType() {
		return listType;
	}

	public void setListType(String listType) {
		this.listType = listType;
	}

	public String getRiskType() {
		return riskType;
	}

	public void setRiskType(String riskType) {
		this.riskType = riskType;
	}

	public String getInverseRiskLevel() {
		return inverseRiskLevel;
	}

	public void setInverseRiskLevel(String inverseRiskLevel) {
		this.inverseRiskLevel = inverseRiskLevel;
	}

	public String getInvoiceRiskLevel() {
		return invoiceRiskLevel;
	}

	public void setInvoiceRiskLevel(String invoiceRiskLevel) {
		this.invoiceRiskLevel = invoiceRiskLevel;
	}

	public String getCaseRiskLevel() {
		return caseRiskLevel;
	}

	public void setCaseRiskLevel(String caseRiskLevel) {
		this.caseRiskLevel = caseRiskLevel;
	}

	public String getAgentRiskLevel() {
		return agentRiskLevel;
	}

	public void setAgentRiskLevel(String agentRiskLevel) {
		this.agentRiskLevel = agentRiskLevel;
	}

	public String getCustomerRiskLevel() {
		return customerRiskLevel;
	}

	public void setCustomerRiskLevel(String customerRiskLevel) {
		this.customerRiskLevel = customerRiskLevel;
	}

	public BigDecimal getEarlyWarning() {
		return earlyWarning;
	}

	public void setEarlyWarning(BigDecimal earlyWarning) {
		this.earlyWarning = earlyWarning;
	}

	public BigDecimal getSignUserType() {
		return signUserType;
	}

	public void setSignUserType(BigDecimal signUserType) {
		this.signUserType = signUserType;
	}

	public BigDecimal getBeneCount() {
		return beneCount;
	}

	public void setBeneCount(BigDecimal beneCount) {
		this.beneCount = beneCount;
	}

	public String getSignUserCode() {
		return signUserCode;
	}

	public void setSignUserCode(String signUserCode) {
		this.signUserCode = signUserCode;
	}

	public String getSignUserName() {
		return signUserName;
	}

	public void setSignUserName(String signUserName) {
		this.signUserName = signUserName;
	}
	public int getmCaseThreeSeven() {
		return mCaseThreeSeven;
	}

	public void setmCaseThreeSeven(int mCaseThreeSeven) {
		this.mCaseThreeSeven = mCaseThreeSeven;
	}

	public int getmCaseThreeFourty() {
		return mCaseThreeFourty;
	}

	public void setmCaseThreeFourty(int mCaseThreeFourty) {
		this.mCaseThreeFourty = mCaseThreeFourty;
	}

	public int getmCaseThreeTwoOne() {
		return mCaseThreeTwoOne;
	}

	public void setmCaseThreeTwoOne(int mCaseThreeTwoOne) {
		this.mCaseThreeTwoOne = mCaseThreeTwoOne;
	}

	public int getmCaseThreeTwoOneH() {
		return mCaseThreeTwoOneH;
	}

	public void setmCaseThreeTwoOneH(int mCaseThreeTwoOneH) {
		this.mCaseThreeTwoOneH = mCaseThreeTwoOneH;
	}

	public int geteCaseThreeAD11() {
		return eCaseThreeAD11;
	}

	public void seteCaseThreeAD11(int eCaseThreeAD11) {
		this.eCaseThreeAD11 = eCaseThreeAD11;
	}

	public int geteCaseThreeAD12() {
		return eCaseThreeAD12;
	}

	public void seteCaseThreeAD12(int eCaseThreeAD12) {
		this.eCaseThreeAD12 = eCaseThreeAD12;
	}

	public int geteCaseThreeAD13() {
		return eCaseThreeAD13;
	}

	public void seteCaseThreeAD13(int eCaseThreeAD13) {
		this.eCaseThreeAD13 = eCaseThreeAD13;
	}

	public int geteCaseThreeAD14() {
		return eCaseThreeAD14;
	}

	public void seteCaseThreeAD14(int eCaseThreeAD14) {
		this.eCaseThreeAD14 = eCaseThreeAD14;
	}

	public int geteCaseThreeAP28() {
		return eCaseThreeAP28;
	}

	public void seteCaseThreeAP28(int eCaseThreeAP28) {
		this.eCaseThreeAP28 = eCaseThreeAP28;
	}

	public int geteCaseThreeAP30() {
		return eCaseThreeAP30;
	}

	public void seteCaseThreeAP30(int eCaseThreeAP30) {
		this.eCaseThreeAP30 = eCaseThreeAP30;
	}

	public String getIsCancelUw() {
		return isCancelUw;
	}

	public void setIsCancelUw(String isCancelUw) {
		this.isCancelUw = isCancelUw;
	}
    
	public BigDecimal getIsRisk() {
		return isRisk;
	}

	public void setIsRisk(BigDecimal isRisk) {
		this.isRisk = isRisk;
	}

	public String getRiskLabel() {
		return riskLabel;
	}

	public void setRiskLabel(String riskLabel) {
		this.riskLabel = riskLabel;
	}

	public String getRiskOtherReason() {
		return riskOtherReason;
	}

	public void setRiskOtherReason(String riskOtherReason) {
		this.riskOtherReason = riskOtherReason;
	}

	public String getMedicalAmountCriterion() {
        return medicalAmountCriterion;
    }

    public BigDecimal getMedicalAmountFlag() {
        return medicalAmountFlag;
    }

    public BigDecimal getMedicalTotalMony() {
        return medicalTotalMony;
    }

    public void setMedicalAmountCriterion(String medicalAmountCriterion) {
        this.medicalAmountCriterion = medicalAmountCriterion;
    }

    public void setMedicalAmountFlag(BigDecimal medicalAmountFlag) {
        this.medicalAmountFlag = medicalAmountFlag;
    }

    public void setMedicalTotalMony(BigDecimal medicalTotalMony) {
        this.medicalTotalMony = medicalTotalMony;
    }

    public String getSpecificCause() {
		return specificCause;
	}

	public void setSpecificCause(String specificCause) {
		this.specificCause = specificCause;
	}

	public String getSignOrgan() {
        return signOrgan;
    }

    public void setSignOrgan(String signOrgan) {
        this.signOrgan = signOrgan;
    }

    public String getRegisteOrgan() {
        return registeOrgan;
    }

    public void setRegisteOrgan(String registeOrgan) {
        this.registeOrgan = registeOrgan;
    }

    public String getAuditOrgan() {
        return auditOrgan;
    }

    public void setAuditOrgan(String auditOrgan) {
        this.auditOrgan = auditOrgan;
    }

    public String getApproveOrgan() {
        return approveOrgan;
    }

    public void setApproveOrgan(String approveOrgan) {
        this.approveOrgan = approveOrgan;
    }

	public String getPrintType() {
		return printType;
	}

	public void setPrintType(String printType) {
		this.printType = printType;
	}

	public String getBpoEasyFlag() {
        return bpoEasyFlag;
    }

    public void setBpoEasyFlag(String bpoEasyFlag) {
        this.bpoEasyFlag = bpoEasyFlag;
    }

	public BigDecimal getUwId() {
		return uwId;
	}

	public void setUwId(BigDecimal uwId) {
		this.uwId = uwId;
	}

	public BigDecimal getUwPolicyId() {
		return uwPolicyId;
	}

	public void setUwPolicyId(BigDecimal uwPolicyId) {
		this.uwPolicyId = uwPolicyId;
	}

	public Date getAccDate() {
		return accDate;
	}

	public void setAccDate(Date accDate) {
		this.accDate = accDate;
	}

	/** 
     * @Fields RIState :  是否参与再保
     */
    private String RIState;
    
    /** 
     * @Fields RIType :     参与再保标准
     */
    private String RIType;
    /**
     * @Fields flag :     业务员属性
     */
    private String flag;
    
    private BigDecimal comfortStatus;//移动理赔发起慰问状态
    
    /**
     * @Fields sendBeneDocFlag : 按受益人发送赔付通知书标识
     */
    private BigDecimal sendBeneDocFlag;
    
	public BigDecimal getSendBeneDocFlag() {
		return sendBeneDocFlag;
	}

	public void setSendBeneDocFlag(BigDecimal sendBeneDocFlag) {
		this.sendBeneDocFlag = sendBeneDocFlag;
	}
    private String presave;	//上一步标识

    private String isMigration;//是否为迁移数据标志
    
    
    /**
     * @Fields checkListProblemType : 问题件类型
     */
    private String checkListProblemType; 
    
    /**
     * @Fields checkListProblemType : 问题件选项名称
     */
    private String checkListProblemOption;  
    /**
     * 备注/问题件类型字段    problemPiece 
     */
    private String problemPiece; 
    /**
     * 备注人
     */
    private String problemPiecePeo; 
    
    /**
     * 治疗医院名称
     */
    private String hospitalName;
    
    public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}
    
  public String getProblemPiece() {
		return problemPiece;
	}

	public void setProblemPiece(String problemPiece) {
		this.problemPiece = problemPiece;
	}

	public String getProblemPiecePeo() {
		return problemPiecePeo;
	}

	public void setProblemPiecePeo(String problemPiecePeo) {
		this.problemPiecePeo = problemPiecePeo;
	}
	//投保人性别
    private BigDecimal policyHolderSex;
  //投保人名字
    private String policyHolderName;
    /**
     * 保项结论
     */
    private String liabConclusion;
    /**
     * 是否在理算页面显示风险保额
     */
    private String showRiskFlag;
    //查勘人
    private String surveyedBy;
    /**
     * 申请时效
     */
    private String signTimeAging;
    /**
     * 立案时效
     */
    private String registerTimeAging;
    /**
     * 签收人姓名
     */
    private String signerName;
    /**
     * 签收人代码
     */
    private String signerCode;
    
    /**
     * @Fields seriousDisease : 轻度疾病
     */
    private String seriousDisease1;
    /**
     * @Fields seriousDisease : 中度疾病
     */
    private String seriousDisease2;		
    /**
     * 短信发送标识（T_YES_NO）
     */
    private BigDecimal  smsSendFlag;



    public BigDecimal getSmsSendFlag() {
	    return smsSendFlag;
    }

    public void setSmsSendFlag(BigDecimal smsSendFlag) {
	    this.smsSendFlag = smsSendFlag;
    }
    
    /**
     * @Fields seriousType : 疾病分类
     */
    private String seriousType;		

    /**
     * 出险情况
     */
    private String accDesc;
    /**
     * 出险人联系方式
     */
    private String insuredMp;
    /**
     * 服务人员组织代码
     */
    private String salesOrganCode;
    /**
     * 服务人员代码
     */
    private String agentCode;
    /**
     * 服务人员姓名
     */
    private String agentName;
    /**
     * 服务人员联系电话
     */
    private String agentMobile;
    /**
     * 申请类型名称
     */
    private String applyTypeName;
    /**
     * 理赔类型名称
     */
    private String claimTypeName;
    /**
     * 就诊流水号
     */
    private String medicalNum;
    
    /** 
    * @Fields treatDate : 就诊时间
    */ 
    private Date treatDate;
    /**
     * 住院号
     */
    private String hospitalNum;
    
    /** 
    * @Fields changeStatusTime : 案件状态处理时间
    */ 
    private Date changeStatusTime;
    
    /** 
     * @Fields directSaveMemo : 提示信息是否需要保存问题件信息
     */ 
     private String directSaveMemo;
     /** 
      * @Fields directSumFeeAmount : 直赔各“费用类别”汇总金额的累计总金额
      */ 
     private BigDecimal directSumFeeAmount;
     /** 
      * @Fields isHospitalBackFlag : 是否为医院撤案,是 时，赔付结论只能选择：公司撤案、客户撤案
      */ 
      private BigDecimal isHospitalBackFlag;
      /**
       * 超期核定补偿总金额
       */
      private BigDecimal overdueMoney;
      
      /**
       * 超期核定补偿金额
       */
      private BigDecimal overCompPay;
      
      /**
       * @Fields isOverCompSP : 审批超期
       */
      private BigDecimal isOverCompSP;
  	/**
       * @Fields overCompFlagSP : 审批超期补偿标识
       */
      private BigDecimal overCompFlagSP;
      
      /** 
       * @Fields payType : 支付方式
       */ 
       private String payType;
  	 
  	 /**
       * @Fields isOverComp : 超期
       */
      private BigDecimal isOverComp;
      /**
       * @Fields overReason : 超期原因
       */
      private String overReason;

      /**
       * @Fields overDays : 超期天数
       */
      private BigDecimal overDays; 
  	 /**
       * @Fields overDays : 原始超期天数
       */
      private BigDecimal originOverDays;
      
      /**
       * 超期支付核定人员代码
       */
      private String overUserCode;
      
      /**
       * @Fields overdueDate : 超期支付核定日期
       */
      private Date overdueDate;
  	
  	/** 
       * @Fields payFinishDate : 支付完成日期
       */ 
      private Date payFinishDate;
  	
  	/**
       * @Fields startDate : 开始日期
       */
      private Date startDate;
      
      /**
       * @Fields endDate : 结束日期
       */
      private Date endDate;
      
      /**
       * 领款人姓名
       */
      private String payeeName;
      /**
       * 领款人id
       */
      private String customerId;
      /**
       * 是否完成补偿
       */
      private String finishOver;
      
      /**
       * 理赔金支付银行编码
       */
      private String banCode;
      
      /**
       * 理赔金支付银行账号
       */
      private String bankAccount;
      
      /** 
       * @Fields payMode : 支付方式
       */ 
       private String payMode;
       
       /**
        * @Fields checkListDate : 首次补充单证回销日期
        */
       private Date checkListDate;
       
       /**
        * @Fields claimAging : 理赔核定时效
        */
       private int claimAging;
       
       /**
        * 赔案信息录入人代码
        */
       private String caseInputOr;
       
       /**
        * 赔案信息录入人姓名
        */
       private String caseInputName;
       
       /**
        * 支付失败原因
        */
       private String failReason;
       
       /**
        * 是否已短信通知
        */
       private String isSelected;
       
       /**
        * @Fields sendNocFlag : 按接收人发送短信通知标识
        */
       private String sendNocFlag;
       
       /**
        * @Fields sendNocName : 按接收人姓名发送短信通知
        */
       private String sendNocName;
       
       
       
   	public String getSendNocFlag() {
   		return sendNocFlag;
   	}

   	public void setSendNocFlag(String sendNocFlag) {
   		this.sendNocFlag = sendNocFlag;
   	}

   	public String getSendNocName() {
   		return sendNocName;
   	}

   	public void setSendNocName(String sendNocName) {
   		this.sendNocName = sendNocName;
   	}


       
 
       public String getIsSelected() {
   		return isSelected;
   	}

   	public void setIsSelected(String isSelected) {
   		this.isSelected = isSelected;
   	}
       

       
       /**
        * @Fields actualPay : 最终赔付金额
        */
       private BigDecimal actualPayTatol;
      public BigDecimal getIsHospitalBackFlag() {
		return isHospitalBackFlag;
	}

	public void setIsHospitalBackFlag(BigDecimal isHospitalBackFlag) {
		this.isHospitalBackFlag = isHospitalBackFlag;
	}

	public BigDecimal getDirectSumFeeAmount() {
  		return directSumFeeAmount;
  	}

  	public void setDirectSumFeeAmount(BigDecimal directSumFeeAmount) {
  		this.directSumFeeAmount = directSumFeeAmount;
  	}
     public String getDirectSaveMemo() {
 		return directSaveMemo;
 	}

 	public void setDirectSaveMemo(String directSaveMemo) {
 		this.directSaveMemo = directSaveMemo;
 	}

    public String getMedicalNum() {
		return medicalNum;
	}

	public void setMedicalNum(String medicalNum) {
		this.medicalNum = medicalNum;
	}

	public Date getTreatDate() {
		return treatDate;
	}

	public void setTreatDate(Date treatDate) {
		this.treatDate = treatDate;
	}

	public String getHospitalNum() {
		return hospitalNum;
	}

	public void setHospitalNum(String hospitalNum) {
		this.hospitalNum = hospitalNum;
	}

	public Date getChangeStatusTime() {
		return changeStatusTime;
	}

	public void setChangeStatusTime(Date changeStatusTime) {
		this.changeStatusTime = changeStatusTime;
	}
    
    public String getPolicyOrganCode() {
		return policyOrganCode;
	}

	public void setPolicyOrganCode(String policyOrganCode) {
		this.policyOrganCode = policyOrganCode;
	}

	public String getPolicyOrganName() {
		return policyOrganName;
	}

	public void setPolicyOrganName(String policyOrganName) {
		this.policyOrganName = policyOrganName;
	}
	
	public String getDocumentsStatus() {
		return documentsStatus;
	}

	public void setDocumentsStatus(String documentsStatus) {
		this.documentsStatus = documentsStatus;
	}
    
    public String getClaimTypeName() {
		return claimTypeName;
	}

	public void setClaimTypeName(String claimTypeName) {
		this.claimTypeName = claimTypeName;
	}

	public String getApplyTypeName() {
		return applyTypeName;
	}

	public void setApplyTypeName(String applyTypeName) {
		this.applyTypeName = applyTypeName;
	}

	public String getAccDesc() {
		return accDesc;
	}

	public void setAccDesc(String accDesc) {
		this.accDesc = accDesc;
	}

	public String getInsuredMp() {
		return insuredMp;
	}

	public void setInsuredMp(String insuredMp) {
		this.insuredMp = insuredMp;
	}

	public String getSalesOrganCode() {
		return salesOrganCode;
	}

	public void setSalesOrganCode(String salesOrganCode) {
		this.salesOrganCode = salesOrganCode;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getAgentMobile() {
		return agentMobile;
	}

	public void setAgentMobile(String agentMobile) {
		this.agentMobile = agentMobile;
	}
	
	public String getSignerCode() {
		return signerCode;
	}

	public void setSignerCode(String signerCode) {
		this.signerCode = signerCode;
	}

	public String getSignerName() {
		return signerName;
	}

	public void setSignerName(String signerName) {
		this.signerName = signerName;
	}

    public String getSignTimeAging() {
		return signTimeAging;
	}

	public void setSignTimeAging(String signTimeAging) {
		this.signTimeAging = signTimeAging;
	}

	public String getRegisterTimeAging() {
		return registerTimeAging;
	}

	public void setRegisterTimeAging(String registerTimeAging) {
		this.registerTimeAging = registerTimeAging;
	}

	public String getSurveyedBy() {
		return surveyedBy;
	}

	public void setSurveyedBy(String surveyedBy) {
		this.surveyedBy = surveyedBy;
	}

	public String getShowRiskFlag() {
		return showRiskFlag;
	}

	public void setShowRiskFlag(String showRiskFlag) {
		this.showRiskFlag = showRiskFlag;
	}
	
	public String getLiabConclusion() {
        return liabConclusion;
    }

    public void setLiabConclusion(String liabConclusion) {
        this.liabConclusion = liabConclusion;
    }

    public String getPolicyHolderName() {
		return policyHolderName;
	}

	public void setPolicyHolderName(String policyHolderName) {
		this.policyHolderName = policyHolderName;
	}

	public BigDecimal getPolicyHolderSex() {
		return policyHolderSex;
	}

	public void setPolicyHolderSex(BigDecimal policyHolderSex) {
		this.policyHolderSex = policyHolderSex;
	}
     
    public String getCheckListProblemOption() {
        return checkListProblemOption;
    }

    public void setCheckListProblemOption(String checkListProblemOption) {
        this.checkListProblemOption = checkListProblemOption;
    }
    
     
    public String getCheckListProblemType() {
        return checkListProblemType;
    }

    public void setCheckListProblemType(String checkListProblemType) {
        this.checkListProblemType = checkListProblemType;
    }
    
	public String getPresave() {
		return presave;
	}

	public void setPresave(String presave) {
		this.presave = presave;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getRptrCode() {
		return rptrCode;
	}

	public void setRptrCode(String rptrCode) {
		this.rptrCode = rptrCode;
	}

	public String getSpecialRemarkCode() {
		return specialRemarkCode;
	}

	public void setSpecialRemarkCode(String specialRemarkCode) {
		this.specialRemarkCode = specialRemarkCode;
	}

	public String getLossReasonCode() {
		return lossReasonCode;
	}

	public void setLossReasonCode(String lossReasonCode) {
		this.lossReasonCode = lossReasonCode;
	}

	public String getLossLevelCode() {
		return lossLevelCode;
	}

	public void setLossLevelCode(String lossLevelCode) {
		this.lossLevelCode = lossLevelCode;
	}

	public BigDecimal getIsAutoHungup() {
		return isAutoHungup;
	}

	public void setIsAutoHungup(BigDecimal isAutoHungup) {
		this.isAutoHungup = isAutoHungup;
	}
    
	public String getChecklistproblem() {
		return checklistproblem;
	}

	public void setChecklistproblem(String checklistproblem) {
		this.checklistproblem = checklistproblem;
	}

	public String getPolicyCodeStrs() {
		return policyCodeStrs;
	}

	public void setPolicyCodeStrs(String policyCodeStrs) {
		this.policyCodeStrs = policyCodeStrs;
	}

	public String getIsPassIlog() {
		return isPassIlog;
	}

	public void setIsPassIlog(String isPassIlog) {
		this.isPassIlog = isPassIlog;
	}

	public String getTrusteeEmail() {
		return trusteeEmail;
	}

	public void setTrusteeEmail(String trusteeEmail) {
		this.trusteeEmail = trusteeEmail;
	}

	public String getIsSms() {
		return isSms;
	}

	public void setIsSms(String isSms) {
		this.isSms = isSms;
	}

	public String getIsEml() {
		return isEml;
	}

	public void setIsEml(String isEml) {
		this.isEml = isEml;
	}

	public BigDecimal getOutsourceId() {
		return outsourceId;
	}

	public void setOutsourceId(BigDecimal outsourceId) {
		this.outsourceId = outsourceId;
	}

	public BigDecimal getRptrId() {
		return rptrId;
	}

	public void setRptrId(BigDecimal rptrId) {
		this.rptrId = rptrId;
	}
    
    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getLimit() {
        return limit;
    }

    public void setLimit(BigDecimal limit) {
        this.limit = limit;
    }

    public Date getValiddateDate() {
        return validdateDate;
    }

    public void setValiddateDate(Date validdateDate) {
        this.validdateDate = validdateDate;
    }

    public BigDecimal getChecked() {
        return checked;
    }

    public void setChecked(BigDecimal checked) {
        this.checked = checked;
    }

    public String getCardCodeName() {
        return cardCodeName;
    }

    public void setCardCodeName(String cardCodeName) {
        this.cardCodeName = cardCodeName;
    }

    public String getCardCode() {
        return cardCode;
    }

    public void setCardCode(String cardCode) {
        this.cardCode = cardCode;
    }

    public String getProblemNo() {
        return problemNo;
    }

    public void setProblemNo(String problemNo) {
        this.problemNo = problemNo;
    }

    public String getAccidentNo() {
        return accidentNo;
    }

    public void setAccidentNo(String accidentNo) {
        this.accidentNo = accidentNo;
    }

    public String getCasePermissionName() {
        return casePermissionName;
    }

    public void setCasePermissionName(String casePermissionName) {
        this.casePermissionName = casePermissionName;
    }

    public String getCalcFlag() {
        return calcFlag;
    }

    public void setCalcFlag(String calcFlag) {
        this.calcFlag = calcFlag;
    }
    
    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }
    
    public Date getCustomerBirthday() {
        return customerBirthday;
    }

    public void setCustomerBirthday(Date customerBirthday) {
        this.customerBirthday = customerBirthday;
    }
    
    public BigDecimal getDocListId() {
        return docListId;
    }

    public void setDocListId(BigDecimal docListId) {
        this.docListId = docListId;
    }

    public String getDocumentNo() {
        return documentNo;
    }

    public void setDocumentNo(String documentNo) {
        this.documentNo = documentNo;
    }

    public String getDocumentTName() {
        return documentTName;
    }

    public void setDocumentTName(String documentTName) {
        this.documentTName = documentTName;
    }

    public Date getDocumentSDate() {
        return documentSDate;
    }

    public void setDocumentSDate(Date documentSDate) {
        this.documentSDate = documentSDate;
    }

    public Date getDocumentEDate() {
        return documentEDate;
    }

    public void setDocumentEDate(Date documentEDate) {
        this.documentEDate = documentEDate;
    }

    public String getDocumentType() {
        return documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public Date getClaimDate() {
        return claimDate;
    }

    public void setClaimDate(Date claimDate) {
        this.claimDate = claimDate;
    }

    public BigDecimal getRePrintTimes() {
        return rePrintTimes;
    }

    public void setRePrintTimes(BigDecimal rePrintTimes) {
        this.rePrintTimes = rePrintTimes;
    }

    public Date getPrintTime() {
        return printTime;
    }

    public void setPrintTime(Date printTime) {
        this.printTime = printTime;
    }

    public BigDecimal getPrintBy() {
        return printBy;
    }

    public void setPrintBy(BigDecimal printBy) {
        this.printBy = printBy;
    }

    public String getCaseStatusName() {
        return caseStatusName;
    }

    public void setCaseStatusName(String caseStatusName) {
        this.caseStatusName = caseStatusName;
    }


	public String getFaRenCaseStatus() {
		return faRenCaseStatus;
	}

	public void setFaRenCaseStatus(String faRenCaseStatus) {
		this.faRenCaseStatus = faRenCaseStatus;
	}

	public String getFaRenApproveDecision() {
		return faRenApproveDecision;
	}

	public void setFaRenApproveDecision(String faRenApproveDecision) {
		this.faRenApproveDecision = faRenApproveDecision;
	}

	public String getCheckPrint() {
        return checkPrint;
    }

    public void setCheckPrint(String checkPrint) {
        this.checkPrint = checkPrint;
    }

    public BigDecimal getAccReason() {
        return accReason;
    }

    public void setAccReason(BigDecimal accReason) {
        this.accReason = accReason;
    }

    public String getAccDays() {
        return accDays;
    }

    public void setAccDays(String accDays) {
        this.accDays = accDays;
    }

    public String getMemoType() {
        return memoType;
    }

    public void setMemoType(String memoType) {
        this.memoType = memoType;
    }

    public BigDecimal getMemoId() {
        return memoId;
    }

    public void setMemoId(BigDecimal memoId) {
        this.memoId = memoId;
    }

    public String getMemoContent() {
        return memoContent;
    }

    public void setMemoContent(String memoContent) {
        this.memoContent = memoContent;
    }

    public String getMemoOption() {
        return memoOption;
    }

    public void setMemoOption(String memoOption) {
        this.memoOption = memoOption;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getSurveyStatus() {
        return surveyStatus;
    }

    public void setSurveyStatus(String surveyStatus) {
        this.surveyStatus = surveyStatus;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getInsuredCode() {
        return insuredCode;
    }

    public void setInsuredCode(String insuredCode) {
        this.insuredCode = insuredCode;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getProductAbbrName() {
        return productAbbrName;
    }

    public void setProductAbbrName(String productAbbrName) {
        this.productAbbrName = productAbbrName;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public Date getFeeStatusDate() {
        return feeStatusDate;
    }

    public void setFeeStatusDate(Date feeStatusDate) {
        this.feeStatusDate = feeStatusDate;
    }

    public String getUwStatus() {
        return uwStatus;
    }

    public void setUwStatus(String uwStatus) {
        this.uwStatus = uwStatus;
    }

    public String getClaimType() {
        return claimType;
    }

    public void setClaimType(String claimType) {
        this.claimType = claimType;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public Integer getCustomerSex() {
        return customerSex;
    }

    public void setCustomerSex(Integer customerSex) {
        this.customerSex = customerSex;
    }

    public String getSystemDateString() {
        return systemDateString;
    }

    public void setSystemDateString(String systemDateString) {
        this.systemDateString = systemDateString;
    }

    public String getUserRealName() {
        return userRealName;
    }

    public void setUserRealName(String userRealName) {
        this.userRealName = userRealName;
    }

    public void setTrusteeCertiCode(String trusteeCertiCode) {
        this.trusteeCertiCode = trusteeCertiCode;
    }

    public String getTrusteeCertiCode() {
        return trusteeCertiCode;
    }

    public void setEndCaseTime(Date endCaseTime) {
        this.endCaseTime = endCaseTime;
    }

    public Date getEndCaseTime() {
        return endCaseTime;
    }

    public void setActualPay(BigDecimal actualPay) {
        this.actualPay = actualPay;
    }

    public BigDecimal getActualPay() {
        return actualPay;
    }

    public void setReportMode(BigDecimal reportMode) {
        this.reportMode = reportMode;
    }

    public BigDecimal getReportMode() {
        return reportMode;
    }

    public void setApproveRejectReason(String approveRejectReason) {
        this.approveRejectReason = approveRejectReason;
    }

    public String getApproveRejectReason() {
        return approveRejectReason;
    }

    public void setRelatedNo(String relatedNo) {
        this.relatedNo = relatedNo;
    }

    public String getRelatedNo() {
        return relatedNo;
    }

    public void setRptrRelation(String rptrRelation) {
        this.rptrRelation = rptrRelation;
    }

    public String getRptrRelation() {
        return rptrRelation;
    }

    public void setOverCompFlag(BigDecimal overCompFlag) {
        this.overCompFlag = overCompFlag;
    }

    public BigDecimal getOverCompFlag() {
        return overCompFlag;
    }

    public void setOtherReason(String otherReason) {
        this.otherReason = otherReason;
    }

    public String getOtherReason() {
        return otherReason;
    }

    public void setRepealReason(BigDecimal repealReason) {
        this.repealReason = repealReason;
    }

    public BigDecimal getRepealReason() {
        return repealReason;
    }

    public void setIsCommon(BigDecimal isCommon) {
        this.isCommon = isCommon;
    }

    public BigDecimal getIsCommon() {
        return isCommon;
    }

    public void setCureHospital(String cureHospital) {
        this.cureHospital = cureHospital;
    }

    public String getCureHospital() {
        return cureHospital;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setReviewFlag(BigDecimal reviewFlag) {
        this.reviewFlag = reviewFlag;
    }

    public BigDecimal getReviewFlag() {
        return reviewFlag;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setAdvanceAskFlag(BigDecimal advanceAskFlag) {
        this.advanceAskFlag = advanceAskFlag;
    }

    public BigDecimal getAdvanceAskFlag() {
        return advanceAskFlag;
    }

    public void setCaseFlag(BigDecimal caseFlag) {
        this.caseFlag = caseFlag;
    }

    public BigDecimal getCaseFlag() {
        return caseFlag;
    }

    public void setGreenFlag(BigDecimal greenFlag) {
        this.greenFlag = greenFlag;
    }

    public BigDecimal getGreenFlag() {
        return greenFlag;
    }

    public void setAuditRejectReason(String auditRejectReason) {
        this.auditRejectReason = auditRejectReason;
    }

    public String getAuditRejectReason() {
        return auditRejectReason;
    }

    public void setBalancePay(BigDecimal balancePay) {
        this.balancePay = balancePay;
    }

    public BigDecimal getBalancePay() {
        return balancePay;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setAuditorId(BigDecimal auditorId) {
        this.auditorId = auditorId;
    }

    public BigDecimal getAuditorId() {
        return auditorId;
    }

    public void setRptrMp(String rptrMp) {
        this.rptrMp = rptrMp;
    }

    public String getRptrMp() {
        return rptrMp;
    }

    public void setSeriousDisease(String seriousDisease) {
        this.seriousDisease = seriousDisease;
    }

    public String getSeriousDisease() {
        return seriousDisease;
    }

    public void setRegisteTime(Date registeTime) {
        this.registeTime = registeTime;
    }

    public Date getRegisteTime() {
        return registeTime;
    }

    public void setCaseApplyType(BigDecimal caseApplyType) {
        this.caseApplyType = caseApplyType;
    }

    public BigDecimal getCaseApplyType() {
        return caseApplyType;
    }

    public void setRepealDesc(String repealDesc) {
        this.repealDesc = repealDesc;
    }

    public String getRepealDesc() {
        return repealDesc;
    }

    public void setRptrEmail(String rptrEmail) {
        this.rptrEmail = rptrEmail;
    }

    public String getRptrEmail() {
        return rptrEmail;
    }

    public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public void setTrusteeCertiType(String trusteeCertiType) {
        this.trusteeCertiType = trusteeCertiType;
    }

    public String getTrusteeCertiType() {
        return trusteeCertiType;
    }

    public void setInsuredId(BigDecimal insuredId) {
        this.insuredId = insuredId;
    }

    public BigDecimal getInsuredId() {
        return insuredId;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setCaseStatus(String caseStatus) {
        this.caseStatus = caseStatus;
    }

    public String getCaseStatus() {
        return caseStatus;
    }

    public void setCaseId(BigDecimal caseId) {
        this.caseId = caseId;
    }

    public BigDecimal getCaseId() {
        return caseId;
    }

    public void setRptrTime(Date rptrTime) {
        this.rptrTime = rptrTime;
    }

    public Date getRptrTime() {
        return rptrTime;
    }

    public void setAuditDecision(BigDecimal auditDecision) {
        this.auditDecision = auditDecision;
    }

    public BigDecimal getAuditDecision() {
        return auditDecision;
    }

    public void setRegisterId(BigDecimal registerId) {
        this.registerId = registerId;
    }

    public BigDecimal getRegisterId() {
        return registerId;
    }

    public void setClaimSource(BigDecimal claimSource) {
        this.claimSource = claimSource;
    }

    public BigDecimal getClaimSource() {
        return claimSource;
    }

    public void setTrusteeType(BigDecimal trusteeType) {
        this.trusteeType = trusteeType;
    }

    public BigDecimal getTrusteeType() {
        return trusteeType;
    }

    public void setApproverId(BigDecimal approverId) {
        this.approverId = approverId;
    }

    public BigDecimal getApproverId() {
        return approverId;
    }

    public void setAccidentId(BigDecimal accidentId) {
        this.accidentId = accidentId;
    }

    public BigDecimal getAccidentId() {
        return accidentId;
    }

    public void setRejectPay(BigDecimal rejectPay) {
        this.rejectPay = rejectPay;
    }

    public BigDecimal getRejectPay() {
        return rejectPay;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignerId(BigDecimal signerId) {
        this.signerId = signerId;
    }

    public BigDecimal getSignerId() {
        return signerId;
    }

    public void setAdvancePay(BigDecimal advancePay) {
        this.advancePay = advancePay;
    }

    public BigDecimal getAdvancePay() {
        return advancePay;
    }

    public void setRptrName(String rptrName) {
        this.rptrName = rptrName;
    }

    public String getRptrName() {
        return rptrName;
    }

    public void setRptrAddr(String rptrAddr) {
        this.rptrAddr = rptrAddr;
    }

    public String getRptrAddr() {
        return rptrAddr;
    }

    public void setApproveRemark(String approveRemark) {
        this.approveRemark = approveRemark;
    }

    public String getApproveRemark() {
        return approveRemark;
    }

    public void setTrusteeName(String trusteeName) {
        this.trusteeName = trusteeName;
    }

    public String getTrusteeName() {
        return trusteeName;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setAdvanceFlag(BigDecimal advanceFlag) {
        this.advanceFlag = advanceFlag;
    }

    public BigDecimal getAdvanceFlag() {
        return advanceFlag;
    }

    public void setRegisteConfTime(Date registeConfTime) {
        this.registeConfTime = registeConfTime;
    }

    public Date getRegisteConfTime() {
        return registeConfTime;
    }

    public void setTrusteeTel(String trusteeTel) {
        this.trusteeTel = trusteeTel;
    }

    public String getTrusteeTel() {
        return trusteeTel;
    }

    public void setRptrZip(BigDecimal rptrZip) {
        this.rptrZip = rptrZip;
    }

    public BigDecimal getRptrZip() {
        return rptrZip;
    }

    public void setIsBpo(BigDecimal isBpo) {
        this.isBpo = isBpo;
    }

    public BigDecimal getIsBpo() {
        return isBpo;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setApproveDecision(BigDecimal approveDecision) {
        this.approveDecision = approveDecision;
    }

    public BigDecimal getApproveDecision() {
        return approveDecision;
    }

    public void setTrusteeCode(String trusteeCode) {
        this.trusteeCode = trusteeCode;
    }

    public String getTrusteeCode() {
        return trusteeCode;
    }

    public void setDoorSignTime(Date doorSignTime) {
        this.doorSignTime = doorSignTime;
    }

    public Date getDoorSignTime() {
        return doorSignTime;
    }

    public void setComfortFlag(BigDecimal comfortFlag) {
        this.comfortFlag = comfortFlag;
    }

    public BigDecimal getComfortFlag() {
        return comfortFlag;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public Date getAcceptTime() {
        return acceptTime;
    }

    public void setIsDeductFlag(BigDecimal isDeductFlag) {
        this.isDeductFlag = isDeductFlag;
    }

    public BigDecimal getIsDeductFlag() {
        return isDeductFlag;
    }

    public void setMedDept(String medDept) {
        this.medDept = medDept;
    }

    public String getMedDept() {
        return medDept;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setTrusteeMp(String trusteeMp) {
        this.trusteeMp = trusteeMp;
    }

    public String getTrusteeMp() {
        return trusteeMp;
    }

    public void setCureStatus(String cureStatus) {
        this.cureStatus = cureStatus;
    }

    public String getCureStatus() {
        return cureStatus;
    }

    public void setAcceptDecision(BigDecimal acceptDecision) {
        this.acceptDecision = acceptDecision;
    }

    public BigDecimal getAcceptDecision() {
        return acceptDecision;
    }

    public void setAcceptorId(BigDecimal acceptorId) {
        this.acceptorId = acceptorId;
    }

    public BigDecimal getAcceptorId() {
        return acceptorId;
    }

    public void setCaseSubStatus(String caseSubStatus) {
        this.caseSubStatus = caseSubStatus;
    }

    public String getCaseSubStatus() {
        return caseSubStatus;
    }

    public void setCalcPay(BigDecimal calcPay) {
        this.calcPay = calcPay;
    }

    public BigDecimal getCalcPay() {
        return calcPay;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    
    
    public String getSeriousDisease1() {
		return seriousDisease1;
	}

	public void setSeriousDisease1(String seriousDisease1) {
		this.seriousDisease1 = seriousDisease1;
	}

	public String getSeriousDisease2() {
		return seriousDisease2;
	}

	public void setSeriousDisease2(String seriousDisease2) {
		this.seriousDisease2 = seriousDisease2;
	}

	public String getSeriousType() {
		return seriousType;
	}

	public void setSeriousType(String seriousType) {
		this.seriousType = seriousType;
	}

	@Override
    public String getBizId() {
        return null;
    }

    
    //RM115673-P00001001696 新增 
    /**
     * 涉案险种    claimBusiProdStrs 
     */
    private String claimBusiProdStrs;
    
	public String getClaimBusiProdStrs() {
		return claimBusiProdStrs;
	}

	public void setClaimBusiProdStrs(String claimBusiProdStrs) {
		this.claimBusiProdStrs = claimBusiProdStrs;
	} 
	

    public ClaimAccidentVO getClaimAccident() {
        return claimAccident;
    }

    public void setClaimAccident(ClaimAccidentVO claimAccidentVO) {
        this.claimAccident = claimAccidentVO;
    }

    public String getPrintByName() {
        return printByName;
    }

    public void setPrintByName(String printByName) {
        this.printByName = printByName;
    }

    public String getClaimDateAll() {
        return claimDateAll;
    }

    public void setClaimDateAll(String claimDateAll) {
        this.claimDateAll = claimDateAll;
    }

    public String getCaseResult() {
        return caseResult;
    }

    public void setCaseResult(String caseResult) {
        this.caseResult = caseResult;
    }

    public String getVisitorName() {
        return visitorName;
    }

    public void setVisitorName(String visitorName) {
        this.visitorName = visitorName;
    }

    public String getVisitMarker() {
        return visitMarker;
    }

    public void setVisitMarker(String visitMarker) {
        this.visitMarker = visitMarker;
    }

    public Date getNextVisitDate() {
        return nextVisitDate;
    }

    public void setNextVisitDate(Date nextVisitDate) {
        this.nextVisitDate = nextVisitDate;
    }

    public BigDecimal getCareTimes() {
        return careTimes;
    }

    public void setCareTimes(BigDecimal careTimes) {
        this.careTimes = careTimes;
    }

    public String getCheckAll() {
        return checkAll;
    }

    public void setCheckAll(String checkAll) {
        this.checkAll = checkAll;
    }

    public String getSeleNo() {
        return seleNo;
    }

    public void setSeleNo(String seleNo) {
        this.seleNo = seleNo;
    }

    public String getSeleNohid() {
        return seleNohid;
    }

    public void setSeleNohid(String seleNohid) {
        this.seleNohid = seleNohid;
    }

    public String getProductNameStd() {
        return productNameStd;
    }

    public void setProductNameStd(String productNameStd) {
        this.productNameStd = productNameStd;
    }

    public String getProductCodeStd() {
        return productCodeStd;
    }

    public void setProductCodeStd(String productCodeStd) {
        this.productCodeStd = productCodeStd;
    }

    public BigDecimal getPolicyId() {
        return policyId;
    }

    public void setPolicyId(BigDecimal policyId) {
        this.policyId = policyId;
    }

    public BigDecimal getBusiItemId() {
        return busiItemId;
    }

    public void setBusiItemId(BigDecimal busiItemId) {
        this.busiItemId = busiItemId;
    }

    public BigDecimal getLiabilityState() {
        return liabilityState;
    }

    public void setLiabilityState(BigDecimal liabilityState) {
        this.liabilityState = liabilityState;
    }

    public Date getPaidupDate() {
        return paidupDate;
    }

    public void setPaidupDate(Date paidupDate) {
        this.paidupDate = paidupDate;
    }

    public BigDecimal getTotalPremAf() {
        return totalPremAf;
    }

    public void setTotalPremAf(BigDecimal totalPremAf) {
        this.totalPremAf = totalPremAf;
    }

    public BigDecimal getItemId() {
        return itemId;
    }

    public void setItemId(BigDecimal itemId) {
        this.itemId = itemId;
    }

    public BigDecimal getInitialType() {
        return initialType;
    }

    public void setInitialType(BigDecimal initialType) {
        this.initialType = initialType;
    }

    public String getPaidupString() {
        return paidupString;
    }

    public void setPaidupString(String paidupString) {
        this.paidupString = paidupString;
    }

    public String getChargeName() {
        return chargeName;
    }

    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    public BigDecimal getIsWaived() {
        return isWaived;
    }

    public void setIsWaived(BigDecimal isWaived) {
        this.isWaived = isWaived;
    }

    public String getWaiveReason() {
        return waiveReason;
    }

    public void setWaiveReason(String waiveReason) {
        this.waiveReason = waiveReason;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public Date getWaiveStart() {
        return waiveStart;
    }

    public void setWaiveStart(Date waiveStart) {
        this.waiveStart = waiveStart;
    }

    public BigDecimal getWaiveAmt() {
        return waiveAmt;
    }

    public void setWaiveAmt(BigDecimal waiveAmt) {
        this.waiveAmt = waiveAmt;
    }

    public Date getWaiveEnd() {
        return waiveEnd;
    }

    public void setWaiveEnd(Date waiveEnd) {
        this.waiveEnd = waiveEnd;
    }

    public String getWaiveStartString() {
        return waiveStartString;
    }

    public void setWaiveStartString(String waiveStartString) {
        this.waiveStartString = waiveStartString;
    }

    public String getWaiveEndString() {
        return waiveEndString;
    }

    public void setWaiveEndString(String waiveEndString) {
        this.waiveEndString = waiveEndString;
    }

    public String getWaiveDesc() {
        return waiveDesc;
    }

    public void setWaiveDesc(String waiveDesc) {
        this.waiveDesc = waiveDesc;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public BigDecimal getPayeeNum() {
        return payeeNum;
    }

    public void setPayeeNum(BigDecimal payeeNum) {
        this.payeeNum = payeeNum;
    }

    public String getAccReasonName() {
        return accReasonName;
    }

    public void setAccReasonName(String accReasonName) {
        this.accReasonName = accReasonName;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public BigDecimal getClaimRate() {
        return claimRate;
    }

    public void setClaimRate(BigDecimal claimRate) {
        this.claimRate = claimRate;
    }

    public Date getPayDueDate() {
        return payDueDate;
    }

    public void setPayDueDate(Date payDueDate) {
        this.payDueDate = payDueDate;
    }

    public BigDecimal getPolicyPeriod() {
        return policyPeriod;
    }

    public void setPolicyPeriod(BigDecimal policyPeriod) {
        this.policyPeriod = policyPeriod;
    }

    public BigDecimal getNextPrem() {
        return nextPrem;
    }

    public void setNextPrem(BigDecimal nextPrem) {
        this.nextPrem = nextPrem;
    }

    public String getPayDueDateString() {
        return payDueDateString;
    }

    public void setPayDueDateString(String payDueDateString) {
        this.payDueDateString = payDueDateString;
    }

    public String getValiddateDateString() {
        return validdateDateString;
    }

    public void setValiddateDateString(String validdateDateString) {
        this.validdateDateString = validdateDateString;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

	public BigDecimal getCurrentUserId() {
		return currentUserId;
	}

	public void setCurrentUserId(BigDecimal currentUserId) {
		this.currentUserId = currentUserId;
	}

	public Date getCurrentDate() {
		return currentDate;
	}

	public void setCurrentDate(Date currentDate) {
		this.currentDate = currentDate;
	}

    public String getIsWaivedString() {
        return isWaivedString;
    }

    public void setIsWaivedString(String isWaivedString) {
        this.isWaivedString = isWaivedString;
    }

    public BigDecimal getLiabId() {
        return liabId;
    }

    public void setLiabId(BigDecimal liabId) {
        this.liabId = liabId;
    }

    public String getAuditPermissionName() {
        return auditPermissionName;
    }

    public void setAuditPermissionName(String auditPermissionName) {
        this.auditPermissionName = auditPermissionName;
    }

    public String getApprovePermissionName() {
        return approvePermissionName;
    }

    public void setApprovePermissionName(String approvePermissionName) {
        this.approvePermissionName = approvePermissionName;
    }

	public String getSurveyResultInfo() {
		return surveyResultInfo;
	}

	public void setSurveyResultInfo(String surveyResultInfo) {
		this.surveyResultInfo = surveyResultInfo;
	}

	public boolean isCheckType() {
		return checkType;
	}

	public void setCheckType(boolean checkType) {
		this.checkType = checkType;
	}

	public String getOrganCodeDate() {
		return OrganCodeDate;
	}

	public void setOrganCodeDate(String organCodeDate) {
		OrganCodeDate = organCodeDate;
	}

	public String getChecklistproblemFlag() {
		return checklistproblemFlag;
	}

	public void setChecklistproblemFlag(String checklistproblemFlag) {
		this.checklistproblemFlag = checklistproblemFlag;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getBuildReportBPM() {
		return buildReportBPM;
	}

	public void setBuildReportBPM(String buildReportBPM) {
		this.buildReportBPM = buildReportBPM;
	}

	public String getCaseStatusBPM() {
		return caseStatusBPM;
	}

	public void setCaseStatusBPM(String caseStatusBPM) {
		this.caseStatusBPM = caseStatusBPM;
	}

	public BigDecimal getBuildReportBPMId() {
		return buildReportBPMId;
	}

	public void setBuildReportBPMId(BigDecimal buildReportBPMId) {
		this.buildReportBPMId = buildReportBPMId;
	}

	public String getSubTaskCode() {
		return subTaskCode;
	}

	public void setSubTaskCode(String subTaskCode) {
		this.subTaskCode = subTaskCode;
	}

	public String getRIState() {
		return RIState;
	}

	public void setRIState(String rIState) {
		RIState = rIState;
	}

	public String getRIType() {
		return RIType;
	}

	public void setRIType(String rIType) {
		RIType = rIType;
	}

	public String getWaiveReasonName() {
		return waiveReasonName;
	}

	public void setWaiveReasonName(String waiveReasonName) {
		this.waiveReasonName = waiveReasonName;
	}

	public BigDecimal getComfortStatus() {
		return comfortStatus;
	}

	public void setComfortStatus(BigDecimal comfortStatus) {
		this.comfortStatus = comfortStatus;
	}

	public String getIsMigration() {
		return isMigration;
	}

	public String getClaimDateStr() {
        return claimDateStr;
    }

    public void setClaimDateStr(String claimDateStr) {
        this.claimDateStr = claimDateStr;
    }

    public void setIsMigration(String isMigration) {
		this.isMigration = isMigration;
	}

    public String getStree() {
        return stree;
    }

    public void setStree(String stree) {
        this.stree = stree;
    }

    public String getIsSendCustomer() {
		return isSendCustomer;
	}

	public void setIsSendCustomer(String isSendCustomer) {
		this.isSendCustomer = isSendCustomer;
	}
	
	public BigDecimal getClaimLiabId() {
		return claimLiabId;
	}

	public void setClaimLiabId(BigDecimal claimLiabId) {
		this.claimLiabId = claimLiabId;
	}

	public Date getAuditStartTime() {
		return auditStartTime;
	}

	public void setAuditStartTime(Date auditStartTime) {
		this.auditStartTime = auditStartTime;
	}

	public BigDecimal getEasyAuditDecision() {
		return easyAuditDecision;
	}

	public void setEasyAuditDecision(BigDecimal easyAuditDecision) {
		this.easyAuditDecision = easyAuditDecision;
	}

	public BigDecimal getEasyAuditorId() {
		return easyAuditorId;
	}

	public void setEasyAuditorId(BigDecimal easyAuditorId) {
		this.easyAuditorId = easyAuditorId;
	}

    public Date getBackAuditDate() {
        return backAuditDate;
    }

    public void setBackAuditDate(Date backAuditDate) {
        this.backAuditDate = backAuditDate;
    }

	public String getTaskMessage() {
		return taskMessage;
	}

	public void setTaskMessage(String taskMessage) {
		this.taskMessage = taskMessage;
	}
	public BigDecimal getRealtimePay() {
		return realtimePay;
	}

	public void setRealtimePay(BigDecimal realtimePay) {
		this.realtimePay = realtimePay;
	}

	public String getChannelCodeName() {
		return channelCodeName;
	}

	public void setChannelCodeName(String channelCodeName) {
		this.channelCodeName = channelCodeName;
	}

	public String getAuditDecisionStr() {
		return auditDecisionStr;
	}

	public void setAuditDecisionStr(String auditDecisionStr) {
		this.auditDecisionStr = auditDecisionStr;
	}

	public BigDecimal getSignatureTraceFlag() {
		return signatureTraceFlag;
	}

	public void setSignatureTraceFlag(BigDecimal signatureTraceFlag) {
		this.signatureTraceFlag = signatureTraceFlag;
	}

	public BigDecimal getIsLawsuits() {
		return isLawsuits;
	}

	public void setIsLawsuits(BigDecimal isLawsuits) {
		this.isLawsuits = isLawsuits;
	}

	public BigDecimal getOverdueMoney() {
		return overdueMoney;
	}

	public void setOverdueMoney(BigDecimal overdueMoney) {
		this.overdueMoney = overdueMoney;
	}

	public BigDecimal getOverCompPay() {
		return overCompPay;
	}

	public void setOverCompPay(BigDecimal overCompPay) {
		this.overCompPay = overCompPay;
	}

	public BigDecimal getIsOverCompSP() {
		return isOverCompSP;
	}

	public void setIsOverCompSP(BigDecimal isOverCompSP) {
		this.isOverCompSP = isOverCompSP;
	}

	public BigDecimal getOverCompFlagSP() {
		return overCompFlagSP;
	}

	public void setOverCompFlagSP(BigDecimal overCompFlagSP) {
		this.overCompFlagSP = overCompFlagSP;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public BigDecimal getIsOverComp() {
		return isOverComp;
	}

	public void setIsOverComp(BigDecimal isOverComp) {
		this.isOverComp = isOverComp;
	}

	public String getOverReason() {
		return overReason;
	}

	public void setOverReason(String overReason) {
		this.overReason = overReason;
	}

	public BigDecimal getOverDays() {
		return overDays;
	}

	public void setOverDays(BigDecimal overDays) {
		this.overDays = overDays;
	}

	public String getOverUserCode() {
		return overUserCode;
	}

	public void setOverUserCode(String overUserCode) {
		this.overUserCode = overUserCode;
	}

	public Date getOverdueDate() {
		return overdueDate;
	}

	public void setOverdueDate(Date overdueDate) {
		this.overdueDate = overdueDate;
	}

	public Date getPayFinishDate() {
		return payFinishDate;
	}

	public void setPayFinishDate(Date payFinishDate) {
		this.payFinishDate = payFinishDate;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getPayeeName() {
		return payeeName;
	}

	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}

	public String getFinishOver() {
		return finishOver;
	}

	public void setFinishOver(String finishOver) {
		this.finishOver = finishOver;
	}

	public String getBanCode() {
		return banCode;
	}

	public void setBanCode(String banCode) {
		this.banCode = banCode;
	}

	public String getBankAccount() {
		return bankAccount;
	}

	public void setBankAccount(String bankAccount) {
		this.bankAccount = bankAccount;
	}

	public String getPayMode() {
		return payMode;
	}

	public void setPayMode(String payMode) {
		this.payMode = payMode;
	}

	public Date getCheckListDate() {
		return checkListDate;
	}

	public void setCheckListDate(Date checkListDate) {
		this.checkListDate = checkListDate;
	}

	public int getClaimAging() {
		return claimAging;
	}

	public void setClaimAging(int claimAging) {
		this.claimAging = claimAging;
	}

	public String getCaseInputOr() {
		return caseInputOr;
	}

	public void setCaseInputOr(String caseInputOr) {
		this.caseInputOr = caseInputOr;
	}

	public String getCaseInputName() {
		return caseInputName;
	}

	public void setCaseInputName(String caseInputName) {
		this.caseInputName = caseInputName;
	}

	public String getFailReason() {
		return failReason;
	}

	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}

	public BigDecimal getActualPayTatol() {
		return actualPayTatol;
	}

	public void setActualPayTatol(BigDecimal actualPayTatol) {
		this.actualPayTatol = actualPayTatol;
	}
	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	
	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	@Override
	public String toString() {
		return "ClaimProblemCaseVO [caseStatusBPM=" + caseStatusBPM
				+ ", buildReportBPM=" + buildReportBPM + ", buildReportBPMId="
				+ buildReportBPMId + ", taskId=" + taskId + ", province="
				+ province + ", city=" + city + ", area=" + area + ", stree="
				+ stree + ", trusteeCertiCode=" + trusteeCertiCode
				+ ", endCaseTime=" + endCaseTime + ", updateTime=" + updateTime
				+ ", actualPay=" + actualPay + ", reportMode=" + reportMode
				+ ", approveRejectReason=" + approveRejectReason
				+ ", relatedNo=" + relatedNo + ", rptrRelation=" + rptrRelation
				+ ", overCompFlag=" + overCompFlag + ", otherReason="
				+ otherReason + ", repealReason=" + repealReason
				+ ", isCommon=" + isCommon + ", cureHospital=" + cureHospital
				+ ", caseNo=" + caseNo + ", reviewFlag=" + reviewFlag
				+ ", organCode=" + organCode + ", policyOrganCode="
				+ policyOrganCode + ", organName=" + organName
				+ ", policyOrganName=" + policyOrganName + ", documentsStatus="
				+ documentsStatus + ", advanceAskFlag=" + advanceAskFlag
				+ ", caseFlag=" + caseFlag + ", greenFlag=" + greenFlag
				+ ", auditRejectReason=" + auditRejectReason + ", balancePay="
				+ balancePay + ", rejectReason=" + rejectReason
				+ ", auditorId=" + auditorId + ", rptrMp=" + rptrMp
				+ ", seriousDisease=" + seriousDisease + ", registeTime="
				+ registeTime + ", startTime=" + startTime + ", endTime="
				+ endTime + ", caseApplyType=" + caseApplyType
				+ ", repealDesc=" + repealDesc + ", rptrEmail=" + rptrEmail
				+ ", trusteeCertiType=" + trusteeCertiType + ", insuredId="
				+ insuredId + ", accidentDetail=" + accidentDetail
				+ ", caseStatus=" + caseStatus + ", faRenCaseStatus="
				+ faRenCaseStatus + ", caseId=" + caseId + ", rptrTime="
				+ rptrTime + ", auditDecision=" + auditDecision
				+ ", registerId=" + registerId + ", claimSource=" + claimSource
				+ ", trusteeType=" + trusteeType + ", approverId=" + approverId
				+ ", accidentId=" + accidentId + ", rejectPay=" + rejectPay
				+ ", signTime=" + signTime + ", signerId=" + signerId
				+ ", advancePay=" + advancePay + ", rptrName=" + rptrName
				+ ", rptrAddr=" + rptrAddr + ", approveRemark=" + approveRemark
				+ ", trusteeName=" + trusteeName + ", applyDate=" + applyDate
				+ ", advanceFlag=" + advanceFlag + ", registeConfTime="
				+ registeConfTime + ", trusteeTel=" + trusteeTel + ", rptrZip="
				+ rptrZip + ", isBpo=" + isBpo + ", auditStartTime="
				+ auditStartTime + ", auditTime=" + auditTime
				+ ", backAuditDate=" + backAuditDate + ", approveTime="
				+ approveTime + ", auditRemark=" + auditRemark
				+ ", approveDecision=" + approveDecision
				+ ", faRenApproveDecision=" + faRenApproveDecision
				+ ", trusteeCode=" + trusteeCode + ", doorSignTime="
				+ doorSignTime + ", comfortFlag=" + comfortFlag
				+ ", acceptTime=" + acceptTime + ", isDeductFlag="
				+ isDeductFlag + ", medDept=" + medDept + ", doctorName="
				+ doctorName + ", trusteeMp=" + trusteeMp + ", cureStatus="
				+ cureStatus + ", acceptDecision=" + acceptDecision
				+ ", acceptorId=" + acceptorId + ", caseSubStatus="
				+ caseSubStatus + ", calcPay=" + calcPay + ", userRealName="
				+ userRealName + ", systemDateString=" + systemDateString
				+ ", customerSex=" + customerSex + ", policyCode=" + policyCode
				+ ", claimType=" + claimType + ", uwStatus=" + uwStatus
				+ ", customerBirthday=" + customerBirthday + ", calcFlag="
				+ calcFlag + ", policyType=" + policyType
				+ ", productAbbrName=" + productAbbrName + ", feeStatus="
				+ feeStatus + ", feeStatusDate=" + feeStatusDate
				+ ", approverName=" + approverName + ", auditorName="
				+ auditorName + ", auditDecisionStr=" + auditDecisionStr
				+ ", userName=" + userName + ", insuredName=" + insuredName
				+ ", insuredCode=" + insuredCode + ", surveyStatus="
				+ surveyStatus + ", policyId=" + policyId + ", busiItemId="
				+ busiItemId + ", productNameStd=" + productNameStd
				+ ", productCodeStd=" + productCodeStd + ", liabilityState="
				+ liabilityState + ", paidupString=" + paidupString
				+ ", paidupDate=" + paidupDate + ", totalPremAf=" + totalPremAf
				+ ", itemId=" + itemId + ", initialType=" + initialType
				+ ", chargeName=" + chargeName + ", isWaived=" + isWaived
				+ ", isWaivedString=" + isWaivedString + ", waiveReason="
				+ waiveReason + ", waiveReasonName=" + waiveReasonName
				+ ", dueDate=" + dueDate + ", waiveStart=" + waiveStart
				+ ", waiveStartString=" + waiveStartString + ", waiveAmt="
				+ waiveAmt + ", waiveEnd=" + waiveEnd + ", waiveEndString="
				+ waiveEndString + ", waiveDesc=" + waiveDesc + ", payDueDate="
				+ payDueDate + ", payDueDateString=" + payDueDateString
				+ ", policyPeriod=" + policyPeriod + ", nextPrem=" + nextPrem
				+ ", expiryDate=" + expiryDate + ", validdateDate="
				+ validdateDate + ", validdateDateString="
				+ validdateDateString + ", payeeNum=" + payeeNum
				+ ", channelType=" + channelType + ", liabId=" + liabId
				+ ", claimLiabId=" + claimLiabId + ", claimAccident="
				+ claimAccident + ", OrganCodeDate=" + OrganCodeDate
				+ ", documentSDate=" + documentSDate + ", documentEDate="
				+ documentEDate + ", documentType=" + documentType
				+ ", documentName=" + documentName + ", context=" + context
				+ ", claimDate=" + claimDate + ", claimDateStr=" + claimDateStr
				+ ", rePrintTimes=" + rePrintTimes + ", printTime=" + printTime
				+ ", documentNo=" + documentNo + ", documentTName="
				+ documentTName + ", docListId=" + docListId
				+ ", claimDateAll=" + claimDateAll + ", caseResult="
				+ caseResult + ", visitorName=" + visitorName
				+ ", visitMarker=" + visitMarker + ", nextVisitDate="
				+ nextVisitDate + ", careTimes=" + careTimes + ", checkAll="
				+ checkAll + ", seleNo=" + seleNo + ", seleNohid=" + seleNohid
				+ ", casePermissionName=" + casePermissionName
				+ ", accidentNo=" + accidentNo + ", problemNo=" + problemNo
				+ ", cardCode=" + cardCode + ", cardCodeName=" + cardCodeName
				+ ", checked=" + checked + ", rptrCode=" + rptrCode + ", rate="
				+ rate + ", limit=" + limit + ", claimRate=" + claimRate
				+ ", rptrId=" + rptrId + ", currentUserId=" + currentUserId
				+ ", currentDate=" + currentDate + ", outsourceId="
				+ outsourceId + ", taskMessage=" + taskMessage + ", isSms="
				+ isSms + ", isEml=" + isEml + ", isSendCustomer="
				+ isSendCustomer + ", trusteeEmail=" + trusteeEmail
				+ ", auditPermissionName=" + auditPermissionName
				+ ", approvePermissionName=" + approvePermissionName
				+ ", isPassIlog=" + isPassIlog + ", printBy=" + printBy
				+ ", printByName=" + printByName + ", caseStatusName="
				+ caseStatusName + ", checkPrint=" + checkPrint
				+ ", accReason=" + accReason + ", accReasonName="
				+ accReasonName + ", memoType=" + memoType + ", memoId="
				+ memoId + ", memoContent=" + memoContent + ", memoOption="
				+ memoOption + ", accDays=" + accDays + ", policyCodeStrs="
				+ policyCodeStrs + ", checklistproblem=" + checklistproblem
				+ ", checklistproblemFlag=" + checklistproblemFlag
				+ ", surveyResultInfo=" + surveyResultInfo + ", checkType="
				+ checkType + ", channelCode=" + channelCode
				+ ", channelCodeName=" + channelCodeName + ", isAutoHungup="
				+ isAutoHungup + ", specialRemarkCode=" + specialRemarkCode
				+ ", lossReasonCode=" + lossReasonCode + ", lossLevelCode="
				+ lossLevelCode + ", subTaskCode=" + subTaskCode + ", accDate="
				+ accDate + ", bpoEasyFlag=" + bpoEasyFlag + ", uwId=" + uwId
				+ ", uwPolicyId=" + uwPolicyId + ", printType=" + printType
				+ ", signOrgan=" + signOrgan + ", registeOrgan=" + registeOrgan
				+ ", auditOrgan=" + auditOrgan + ", approveOrgan="
				+ approveOrgan + ", easyAuditDecision=" + easyAuditDecision
				+ ", easyAuditorId=" + easyAuditorId + ", remark=" + remark
				+ ", rejectRemarks=" + rejectRemarks + ", isNoUw=" + isNoUw
				+ ", realtimePay=" + realtimePay + ", outSourceName="
				+ outSourceName + ", registerName=" + registerName
				+ ", registerCode=" + registerCode + ", isChecklist="
				+ isChecklist + ", asignPageSize=" + asignPageSize
				+ ", asignTaskType=" + asignTaskType + ", asignTaskArea="
				+ asignTaskArea + ", isLawsuits=" + isLawsuits
				+ ", auditorCode=" + auditorCode + ", approverCode="
				+ approverCode + ", surveyMark=" + surveyMark
				+ ", positiveFlag=" + positiveFlag + ", shareConditionValid="
				+ shareConditionValid + ", shareConditionDecision="
				+ shareConditionDecision + ", insurancePaidFlag="
				+ insurancePaidFlag + ", signMessage=" + signMessage
				+ ", claimIdentIfAtion=" + claimIdentIfAtion
				+ ", isCorrelation=" + isCorrelation + ", logoutCheckResult="
				+ logoutCheckResult + ", busiProdCode=" + busiProdCode
				+ ", acquistWay=" + acquistWay + ", acquistWayName="
				+ acquistWayName + ", outsourceWay=" + outsourceWay
				+ ", outsourceWayName=" + outsourceWayName + ", productType="
				+ productType + ", riskResult=" + riskResult
				+ ", elecCheckFlag=" + elecCheckFlag + ", lockFlag=" + lockFlag
				+ ", lockSys=" + lockSys + ", AutoCaseNo=" + AutoCaseNo
				+ ", AutoCaseType=" + AutoCaseType + ", servCom=" + servCom
				+ ", specificCause=" + specificCause + ", decisionNoticeFlag="
				+ decisionNoticeFlag + ", contractRelieveFlag="
				+ contractRelieveFlag + ", medicalAmountCriterion="
				+ medicalAmountCriterion + ", medicalAmountFlag="
				+ medicalAmountFlag + ", medicalTotalMony=" + medicalTotalMony
				+ ", isRisk=" + isRisk + ", riskLabel=" + riskLabel
				+ ", riskOtherReason=" + riskOtherReason + ", isCancelUw="
				+ isCancelUw + ", mCaseThreeSeven=" + mCaseThreeSeven
				+ ", mCaseThreeFourty=" + mCaseThreeFourty
				+ ", mCaseThreeTwoOne=" + mCaseThreeTwoOne
				+ ", mCaseThreeTwoOneH=" + mCaseThreeTwoOneH
				+ ", eCaseThreeAD11=" + eCaseThreeAD11 + ", eCaseThreeAD12="
				+ eCaseThreeAD12 + ", eCaseThreeAD13=" + eCaseThreeAD13
				+ ", eCaseThreeAD14=" + eCaseThreeAD14 + ", eCaseThreeAP28="
				+ eCaseThreeAP28 + ", eCaseThreeAP30=" + eCaseThreeAP30
				+ ", signUserType=" + signUserType + ", signUserCode="
				+ signUserCode + ", signUserName=" + signUserName
				+ ", beneCount=" + beneCount + ", inverseRiskLevel="
				+ inverseRiskLevel + ", invoiceRiskLevel=" + invoiceRiskLevel
				+ ", caseRiskLevel=" + caseRiskLevel + ", agentRiskLevel="
				+ agentRiskLevel + ", customerRiskLevel=" + customerRiskLevel
				+ ", earlyWarning=" + earlyWarning + ", listType=" + listType
				+ ", riskType=" + riskType + ", materialFreeFlag="
				+ materialFreeFlag + ", faceRecognitionFlag="
				+ faceRecognitionFlag + ", signatureTraceFlag="
				+ signatureTraceFlag + ", medicalConnectionFlag="
				+ medicalConnectionFlag + ", repeatNumberType="
				+ repeatNumberType + ", repeatNumberReason="
				+ repeatNumberReason + ", repeatNumberFlag=" + repeatNumberFlag
				+ ", salesmanSelfInsurance=" + salesmanSelfInsurance
				+ ", fraudRate=" + fraudRate + ", fraudAmountRate="
				+ fraudAmountRate + ", fraudBackRate=" + fraudBackRate
				+ ", fraudBackAmountRate=" + fraudBackAmountRate
				+ ", calcPayFlag=" + calcPayFlag + ", diagnosisTime="
				+ diagnosisTime + ", reportSource=" + reportSource
				+ ", isCalledBack=" + isCalledBack + ", isCalledBackName="
				+ isCalledBackName + ", reportModeName=" + reportModeName
				+ ", assigneeStartDate=" + assigneeStartDate
				+ ", assigneeEndDate=" + assigneeEndDate
				+ ", auditIndividualPoolTime=" + auditIndividualPoolTime
				+ ", approveIndividualPoolTime=" + approveIndividualPoolTime
				+ ", preAuditId=" + preAuditId + ", reAuditDecision="
				+ reAuditDecision + ", reAuditDecisionStr="
				+ reAuditDecisionStr + ", directApplyFlag=" + directApplyFlag
				+ ", directApplyTimeSta=" + directApplyTimeSta
				+ ", directApplyTimeEnd=" + directApplyTimeEnd
				+ ", directProFlag=" + directProFlag + ", directApplyTime="
				+ directApplyTime + ", directBackTime=" + directBackTime
				+ ", acceptTimeS=" + acceptTimeS + ", acceptTimeE="
				+ acceptTimeE + ", businessName=" + businessName
				+ ", isProblemSolve=" + isProblemSolve + ", arapListId="
				+ arapListId + ", overPayReason=" + overPayReason
				+ ", overPayDays=" + overPayDays + ", overPayMoney="
				+ overPayMoney + ", overPayPerson=" + overPayPerson
				+ ", overPayDate=" + overPayDate + ", overPayFlag="
				+ overPayFlag + ", overPayFlagExcel=" + overPayFlagExcel
				+ ", overPayReasonExcel=" + overPayReasonExcel
				+ ", overPayMoneyExcel=" + overPayMoneyExcel + ", insertTime="
				+ insertTime + ", reAuditOpinion=" + reAuditOpinion
				+ ", preAuditCode=" + preAuditCode + ", isSmalLCase="
				+ isSmalLCase + ", jobPrompt=" + jobPrompt + ", preAuditName="
				+ preAuditName + ", flagPermission=" + flagPermission
				+ ", RIState=" + RIState + ", RIType=" + RIType + ", flag="
				+ flag + ", comfortStatus=" + comfortStatus
				+ ", sendBeneDocFlag=" + sendBeneDocFlag + ", presave="
				+ presave + ", isMigration=" + isMigration
				+ ", checkListProblemType=" + checkListProblemType
				+ ", checkListProblemOption=" + checkListProblemOption
				+ ", problemPiece=" + problemPiece + ", problemPiecePeo="
				+ problemPiecePeo + ", hospitalName=" + hospitalName
				+ ", policyHolderSex=" + policyHolderSex
				+ ", policyHolderName=" + policyHolderName
				+ ", liabConclusion=" + liabConclusion + ", showRiskFlag="
				+ showRiskFlag + ", surveyedBy=" + surveyedBy
				+ ", signTimeAging=" + signTimeAging + ", registerTimeAging="
				+ registerTimeAging + ", signerName=" + signerName
				+ ", signerCode=" + signerCode + ", seriousDisease1="
				+ seriousDisease1 + ", seriousDisease2=" + seriousDisease2
				+ ", smsSendFlag=" + smsSendFlag + ", seriousType="
				+ seriousType + ", accDesc=" + accDesc + ", insuredMp="
				+ insuredMp + ", salesOrganCode=" + salesOrganCode
				+ ", agentCode=" + agentCode + ", agentName=" + agentName
				+ ", agentMobile=" + agentMobile + ", applyTypeName="
				+ applyTypeName + ", claimTypeName=" + claimTypeName
				+ ", medicalNum=" + medicalNum + ", treatDate=" + treatDate
				+ ", hospitalNum=" + hospitalNum + ", changeStatusTime="
				+ changeStatusTime + ", directSaveMemo=" + directSaveMemo
				+ ", directSumFeeAmount=" + directSumFeeAmount
				+ ", isHospitalBackFlag=" + isHospitalBackFlag
				+ ", overdueMoney=" + overdueMoney + ", overCompPay="
				+ overCompPay + ", isOverCompSP=" + isOverCompSP
				+ ", overCompFlagSP=" + overCompFlagSP + ", payType=" + payType
				+ ", isOverComp=" + isOverComp + ", overReason=" + overReason
				+ ", overDays=" + overDays + ", originOverDays="
				+ originOverDays + ", overUserCode=" + overUserCode
				+ ", overdueDate=" + overdueDate + ", payFinishDate="
				+ payFinishDate + ", startDate=" + startDate + ", endDate="
				+ endDate + ", payeeName=" + payeeName + ", customerId="
				+ customerId + ", finishOver=" + finishOver + ", banCode="
				+ banCode + ", bankAccount=" + bankAccount + ", payMode="
				+ payMode + ", checkListDate=" + checkListDate
				+ ", claimAging=" + claimAging + ", caseInputOr=" + caseInputOr
				+ ", caseInputName=" + caseInputName + ", failReason="
				+ failReason + ", isSelected=" + isSelected + ", sendNocFlag="
				+ sendNocFlag + ", sendNocName=" + sendNocName
				+ ", actualPayTatol=" + actualPayTatol + ", claimBusiProdStrs="
				+ claimBusiProdStrs + "]";
	}
	
}
