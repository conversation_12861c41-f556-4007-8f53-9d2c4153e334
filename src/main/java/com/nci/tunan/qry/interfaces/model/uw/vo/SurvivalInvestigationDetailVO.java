package com.nci.tunan.qry.interfaces.model.uw.vo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;

/**
 * @description SurvivalInvestigationDetailVO对象
 * <AUTHOR>
 * @date 2015-08-17 14:55:49
 */
public class SurvivalInvestigationDetailVO extends BaseVO {
	/**
	 * @Fields examineOther : null
	 */
	private String examineOther;
	/**
	 * @Fields positiveIndi : 体检结果值为【↑↓*】时，记录阳性标记
	 */
	private BigDecimal positiveIndi;
	/**
	 * @Fields customerName : null
	 */
	private String customerName;
	/**
	 * @Fields docListId : null
	 */
	private BigDecimal docListId;
	/**
	 * @Fields uwId : null
	 */
	private BigDecimal uwId;
	/**
	 * @Fields plusIndi : null
	 */
	private Date insertTime;
	/**
	 * @Fields rreportId : null
	 */
	private BigDecimal rreportId;
	/**
	 * @Fields customerId : null
	 */
	private BigDecimal customerId;
	/**
	 * @Fields applyCode : null
	 */
	private String applyCode;
	/**
	 * @Fields rreportCode : null
	 */
	private String rreportCode;
	/**
	 * @Fields roleType : null
	 */
	private BigDecimal roleType;
	/**
	 * @Fields policyCode : null
	 */
	private String policyCode;
	/**
	 * @Fields investigatorLevel : null
	 */
	private BigDecimal investigatorLevel;
	/**
	 * @Fields genderCode : null
	 */
	private BigDecimal genderCode;
	/**
	 * @Fields policyId : null
	 */
	private BigDecimal policyId;
	/**
	 * @Fields customerBirth : null
	 */
	private Date customerBirth;
	/**
	 * @Fields examineReason : null
	 */
	private String examineReason;
	/**
	 * @Fields examineCode : null
	 */
	private String examineCode;
	/**
	 * @Fields investigateType : null
	 */
	private BigDecimal investigateType;
	/**
	 * @Fields examineType : null
	 */
	private String examineType;
	/**
	 * @Fields examineName : null
	 */
	private String examineName;

	public void setExamineCode(String examineCode) {
		this.examineCode = examineCode;
	}

	public String getExamineCode() {
		return examineCode;
	}

	public void setInvestigateType(BigDecimal investigateType) {
		this.investigateType = investigateType;
	}

	public BigDecimal getInvestigateType() {
		return investigateType;
	}
	
	public void setExamineType(String examineType) {
		this.examineType = examineType;
	}

	public String getExamineType() {
		return examineType;
	}

	public void setexamineName(String examineName) {
		this.examineName = examineName;
	}

	public String getExamineName() {
		return examineName;
	}

	public void setExamineOther(String examineOther) {
		this.examineOther = examineOther;
	}

	public String getExamineOther() {
		return examineOther;
	}

	public void setPositiveIndi(BigDecimal positiveIndi) {
		this.positiveIndi = positiveIndi;
	}

	public BigDecimal getPositiveIndi() {
		return positiveIndi;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setDocListId(BigDecimal docListId) {
		this.docListId = docListId;
	}

	public BigDecimal getDocListId() {
		return docListId;
	}

	public void setUwId(BigDecimal uwId) {
		this.uwId = uwId;
	}

	public BigDecimal getUwId() {
		return uwId;
	}

	public void setRreportId(BigDecimal rreportId) {
		this.rreportId = rreportId;
	}

	public BigDecimal getRreportId() {
		return rreportId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setRreportCode(String rreportCode) {
		this.rreportCode = rreportCode;
	}

	public String getRreportCode() {
		return rreportCode;
	}

	public void setRoleType(BigDecimal roleType) {
		this.roleType = roleType;
	}

	public BigDecimal getRoleType() {
		return roleType;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setInvestigatorLevel(BigDecimal investigatorLevel) {
		this.investigatorLevel = investigatorLevel;
	}

	public BigDecimal getInvestigatorLevel() {
		return investigatorLevel;
	}

	public void setGenderCode(BigDecimal genderCode) {
		this.genderCode = genderCode;
	}

	public BigDecimal getGenderCode() {
		return genderCode;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setCustomerBirth(Date customerBirth) {
		this.customerBirth = customerBirth;
	}

	public Date getCustomerBirth() {
		return customerBirth;
	}

	public void setExamineReason(String examineReason) {
		this.examineReason = examineReason;
	}

	public String getExamineReason() {
		return examineReason;
	}

	@Override
	public String getBizId() {
		return null;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	@Override
	public String toString() {
		return "SurvivalInvestigationVO [" + "examineOther=" + examineOther
				+ "," + "positiveIndi=" + positiveIndi + "," + "customerName="
				+ customerName + "," + "docListId=" + docListId + "," + "uwId="
				+ uwId + "," + "plusIndi=" + insertTime + "," + "rreportId="
				+ rreportId + "," + "customerId=" + customerId + ","
				+ "applyCode=" + applyCode + "," + "rreportCode=" + rreportCode
				+ "," + "roleType=" + roleType + "," + "policyCode="
				+ policyCode + "," + "investigatorLevel=" + investigatorLevel
				+ "," + "genderCode=" + genderCode + "," + "policyId="
				+ policyId + "," + "customerBirth=" + customerBirth + ","
				+ "examineReason=" + examineReason + "," + "examineCode="
				+ examineCode + "," + "investigateType=" + investigateType
				+ "," + "examineType=" + examineType + "," + "examineName="
				+ examineName + "]";

	}
}