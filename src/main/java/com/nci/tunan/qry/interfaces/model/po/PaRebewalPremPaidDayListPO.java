package com.nci.tunan.qry.interfaces.model.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

public class PaRebewalPremPaidDayListPO extends BasePO{
	/** 
	* @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
	*/ 
	
	private static final long serialVersionUID = 1L;

	public String getOrganName(){
		
		return getString("organName");
	}
	public void setOrganName(String organName) {
		setString("organName",organName);
	}
	public String getName() {
		return getString("name");
	}
	public void setName(String name) {
		setString("name",name);
	}
	public String getChargeName() {
		return getString("chargeName");
	}
	public void setChargeName(String chargeName) {
		setString("chargeName",chargeName);
	}
	public String getPolicyCode() {
		return getString("policyCode");
	}
	public void setPolicyCode(String policyCode) {
		setString("policyCode",policyCode);
	}
	public String getBusiProdCode() {
		return getString("busiProdCode");
	}
	public void setBusiProdCode(String busiProdCode) {
		setString("busiProdCode",busiProdCode);
	}
	public Date getDueTime() {
		return getUtilDate("dueTime");
	}
	public void setDueTime(Date dueTime) {
		setUtilDate("dueTime", dueTime);
	}
	public BigDecimal getFeeAmount() {
		return getBigDecimal("feeAmount");
	}
	public void setFeeAmount(BigDecimal feeAmount) {
		setBigDecimal("feeAmount", feeAmount);
	}
	public BigDecimal getPaidCount() {
		return getBigDecimal("paid_count");
	}
	public void setPaidCount(BigDecimal paidCount) {
		setBigDecimal("paid_count", paidCount);
	}
	public String getHolderName() {
		return getString("holderName");
	}
	public void setHolderName(String holderName) {
		setString("holderName", holderName);
	}
	public String getAgentName() {
		return getString("agent_name");
	}
	public void setAgentName(String agentName) {
		setString("agent_name",agentName);
	}
	public String getCustomerName() {
		return getString("customer_name");
	}
	public void setCustomerName(String customerName) {
		this.setString("customer_name", customerName);
	}
	public Date getCusAccUpdateTime() {
		return getUtilDate("cusAccUpdateTime");
	}
	public void setCusAccUpdateTime(Date cusAccUpdateTime) {
		setUtilDate("cusAccUpdateTime", cusAccUpdateTime);
	}
	public BigDecimal getCusAccFeeAmount() {
		return getBigDecimal("cusAccFeeAmount");
	}
	public void setCusAccFeeAmount(BigDecimal cusAccFeeAmount) {
		setBigDecimal("cusAccFeeAmount", cusAccFeeAmount);
	}
	public String getPayMode() {
		return getString("pay_mode");
	}
	public void setPayMode(String payMode) {
		this.setString("pay_mode", payMode);
	}
	public String getPremFreq() {
		return getString("premFreq");
	}
	public void setPremFreq(String premFreq) {
		setString("premFreq", premFreq);
	}
	public String getSalesOrganName() {
		return getString("salesOrganName");
	}
	public void setSalesOrganName(String salesOrganName) {
		setString("salesOrganName",salesOrganName);
	}
	public String getPolicyType() {
		return getString("policy_type");
	}
	public void setPolicyType(String policyType) {
		setString("policy_type",policyType);
	}
	public String getChannelType() {
		return getString("channelType");
	}
	public void setChannelType(String channelType) {
		setString("channelType",channelType);
	}
	public String getOrganCode() {
		return getString("organCode");
	}
	public void setOrganCode(String organCode) {
		setString("organCode",organCode);
	}
	public Date getStartUpdateTime() {
		return getUtilDate("startUpdateTime");
	}
	public void setStartUpdateTime(Date startUpdateTime) {
		setUtilDate("startUpdateTime", startUpdateTime);
	}
	public Date getEndUpdateTime() {
		return getUtilDate("endUpdateTime");
	}
	public void setEndUpdateTime(Date endUpdateTime) {
		setUtilDate("endUpdateTime", endUpdateTime);
	}
	public String getBankCode() {
		return getString("bankCode");
	}
	public void setBankCode(String bankCode) {
		setString("bankCode",bankCode);
	}
	public String getServiceBankBranch() {
		return getString("serviceBankBranch");
	}
	public void setServiceBankBranch(String serviceBankBranch) {
		setString("serviceBankBranch",serviceBankBranch);
	}

	public String getPayLocation() {
		return getString("pay_location");
	}
	public void setPayLocation(String payLocation) {
		setString("pay_location",payLocation);
	}
	public String getAgentCode() {
		return getString("agent_code");
	}
	public void SetAgentCode(String agentCode) {
		setString("agent_code",agentCode);
	}
	public String getHandlerPayMode() {
		return getString("handler_pay_mode");
	}
	public void SetHandlerPayMode(String handlerPayMode) {
		setString("handler_pay_mode",handlerPayMode);
	}
	
	public void setSalesOrganCode(String salesOrganCode) {
		setString("sales_organ_code", salesOrganCode);
	}

	public String getSalesOrganCode() {
		return getString("sales_organ_code");
	}
	public String getNowAgentName() {
		return getString("now_agent_name");
	}

	public void setNowAgentName(String nowAgentName) {
		setString("now_agent_name", nowAgentName);
	}

	public String getNowAgentCode() {
		return getString("now_agent_code");
	}
	public void setNowAgentCode(String nowAgentName) {
		setString("now_agent_code", nowAgentName);
	}
	public String getSalesChannelName() {
		return getString("sales_channel_name");
	}
	
	public void setSalesChannelName(String salesChannelName) {
		setString("sales_channel_name", salesChannelName);
	}
	public String getYinYZu() {
		return getString("yinyzu");
	}
	
	public void setYinYZu(String yinYZu) {
		setString("yinyzu", yinYZu);
	}
	public String getYinYBu() {
		return getString("yinybu");
	}
	
	public void setYinYBu(String yinYBu) {
		setString("yinybu", yinYBu);
	}
	public String getYinYQu() {
		return getString("yinyqu");
	}
	
	public void setYinYQu(String yinYQu) {
		setString("yinyqu", yinYQu);
	}
	
	public String getComName() {
		return getString("com_name");
	}
	
	public void setComName(String comName) {
		setString("com_name", comName);
	}
	public String getComCode() {
		return getString("com_code");
	}
	
	public void setComCode(String comCode) {
		setString("com_code", comCode);
	}
	public String getAgentNo() {
		return getString("agent_no");
	}
	
	public void setAgentNo(String agentNo) {
		setString("agent_no", agentNo);
	}
	public String getAgentStatus() {
		return getString("agent_status");
	}
	
	public void setAgentStatus(String agentStatus) {
		setString("agent_status", agentStatus);
	}
	public String getBusiProdName() {
		return getString("busi_prod_name");
	}
	
	public void setBusiProdName(String busiProdName) {
		setString("busi_prod_name", busiProdName);
	}
	public String getChargePeriod() {
		return getString("charge_period");
	}
	
	public void setChargePeriod(String chargePeriod) {
		setString("charge_period", chargePeriod);
	}
	public BigDecimal getChargeYear() {
		return getBigDecimal("charge_year");
	}
	
	public void setChargeYear(BigDecimal chargeYear) {
		setBigDecimal("charge_year", chargeYear);
	}

	public Date getValidateDate() {
		return getUtilDate("validate_date");
	}
	public void setValidateDate(Date validateDate) {
		setUtilDate("validate_date", validateDate);
	}
	
}
