package com.nci.tunan.qry.interfaces.model.nb.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 双录信息
 * <AUTHOR> <EMAIL> 
 * @date 2015-5-15
 * @.belongToModule 综合查询-双录信息
 */
public class DRQualityAllVO extends BaseVO {
	/**
	 * 序列
	 */
	private static final long serialVersionUID = -1061155735776746241L;
	/**
	 * 质检任务状态
	 */
	private BigDecimal qtStatus;
	
	public BigDecimal getQtStatus() {
		return qtStatus;
	}
	public void setQtStatus(BigDecimal qtStatus) {
		this.qtStatus = qtStatus;
	}

	/**
	 * 查询条件
	 * 管理机构
	 */
	private String organCode;
	/**
	 * 销售渠道
	 */
	private String channelType;
	/**
	 * 统计开始时间
	 */
	private String startDate;
	/**
	 * 统计结束时间
	 */
	private String endDate; 
	/**
	 * 结论修改
	 */
	private String ifChgDecio;
	/**
	 * 是否超期—质检申请超期
	 */
	private String ifQualityOver;
	/**
	 * 是否超期—整改超期
	 */
	private String ifChgOver; 
	/**
	 * 是否已导入质检结论
	 */
	private String ifImportConclusion;
	/**
	 * 绩优等级
	 */
	private String agentLevel; 

	
	
	/**
	 * 查询结果
	 * 销售渠道名称
	 */
	private String salesChannelName;
	/**
	 * 投保单号
	 */
	private String applyCode;
	/**
	 * 保单号
	 */
	private String policyCode;
	/**
	 * 投保人姓名
	 */
	private String holderName;
	/**
	 * 业务员代码
	 */
	private String agentCode;
	/**
	 * 业务员姓名
	 */
	private String agentName;
	/**
	 * 绩优等级名称
	 */
	private String agentLevelDesc;
	/**
	 * 营业部
	 */
	private String salesOrganNameOne;
	/**
	 * 营业区
	 */
	private String salesOrganNameTwo;
	/**
	 * 营业组
	 */
	private String salesOrganNameThree;
	/**
	 * 银行代码
	 */
	private String serviceBank;
	/**
	 * 网点代码
	 */
	private String serviceBankBranch;
	/**
	 * 银行名称
	 */
	private String bankName;
	/**
	 * 银行网点名称
	 */
	private String bankBranchName;
	/**
	 * 是否银保通
	 */
	private String ifBankSale;
	/**
	 * 是否预承保
	 */
	private String ifWillPas;
	/**
	 * 是否已出单前撤保
	 */
	private String ifPasEd;
	/**
	 * 是否退保
	 */
	private String ifBackPas;
	/**
	 * 是否自保件
	 */
	private String ifSelfInsured;
	/**
	 * 投保日期
	 */
	private Date applyDate;
	/**
	 * 签单完成时间
	 */
	private Date issueDate;
	/**
	 * 客户签署回执日期
	 */
	private Date acknowledgeDate;
	/**
	 * 双录上传时间
	 */
	private Date drqUploadDate;
	/**
	 * 双录开始时间
	 */
	private Date drqStartDate;
	/**
	 * 双录结束时间
	 */
	private Date drqEndDate;
	/**
	 * 质检申请时间
	 */
	private Date createTime;
	/**
	 * 首次下发质检结论时间
	 */
	private Date drqConclusionDate;
	/**
	 * 质检结论名称
	 */
	private String qtResultName;
	/**
	 * 质检次数
	 */
	private BigDecimal qtCounts;
	/**
	 * 最新一次质检不通过原因
	 */
	private String lastNotPassReason;
	/**
	 * 首次质检不通过后仍未完成整改的工作日
	 */
	private String firstNotPassTime;
	/**
	 * 质检员
	 */
	private String drqOperator;
	/**
	 * 是否进行复检
	 */
	private String ifDrqAgain;
	/**
	 * 复检结论
	 */
	private String drqAgainConclusion;
	/**
	 * 复检员
	 */
	private String drqAgaOperator;
	/**
	 * 是否申请修正
	 */
	private String ifAppChg;
	/**
	 * 修正复核员
	 */
	private String appChgOperator;
	/**
	 * 是否导入质检结论
	 */
	private BigDecimal isImportDqr;
	/**
	 * 投保人年龄是否大于60
	 */
	private String isAgeFlag;
	/**
	 * 投保单状态
	 */
	private String proposalStatus;
	/**
	 * 首次质检时间
	 */
	private Date qtTime;
	/**
	 * 首次上传时间
	 */
	private Date videoUploadDrst;
	/**
	 * 质检时长
	 */
	private String drqTime;
	/**
	 * 质检结论
	 */
	private String qtResult;
	
	
	/**
	 * 二次质检时间
	 */
	private Date qtTimeTwo;
	/**
	 * 二次上传时间
	 */
	private Date videoUploadDrstTwo;
	/**
	 * 二次质检时长
	 */
	private String drqTimeTwo;
	/**
	 * 二次质检结论
	 */
	private String qtResultTwo;
	
	
	/**
	 * 三次质检时间
	 */
	private Date qtTimeThree;
	/**
	 * 三次上传时间
	 */
	private Date videoUploadDrstThree;
	/**
	 * 三次质检时长
	 */
	private String drqTimeThree;
	/**
	 * 三次质检结论
	 */
	private String qtResultThree;
	
	
	/**
	 * 四次质检时间
	 */
	private Date qtTimeFour;
	/**
	 * 四次上传时间
	 */
	private Date videoUploadDrstFour;
	/**
	 * 四次质检时长
	 */
	private String drqTimeFour;
	/**
	 * 四次质检结论
	 */
	private String qtResultFour;
	
	
	
	
	/**
	 * 总保费
	 */
	private BigDecimal totalPremAf;
	/**
	 * 类型
	 */
	private String queryType;
	
	
	/**
	 * 签单开始日期
	 */
	private String issueStartDate;
	/**
	 * 签单截止日期
	 */
	private String issueEndDate;
	/**
	 * 是否绩优
	 */
	private String isQualityAgent;
	/**
	 * 修正结论
	 */
	private String isApplyResult;
	/**
	 * 是否互保件
	 */
	private String isMutualInsured;//是否互保件：‘是’、‘否’
	/**影音资料大小*/
	private String audioInfoSize;
	/**抽取结果 */
	private String isDrqDraw;
	
	/**
	 * 数据来源
	 */
	private String dataSource;
	
	/**
	 * 第三方机构质检标识，使用码表T_THIRD_ORG_TYPE
	 */
	private BigDecimal thirdOrgFlag ;

	/**
	 * 质检通过日期
	 */
	private Date qtSuccTime;
	
	/**
	 * 修正日期
	 */
	private Date applyResultDate;
	

	private String qtUser;
	/**
	 * 投保人、被保险人或受益人与业务员关系，使用码表：t_agent_relationship
	 */
	private String relationToAll;
	
	private BigDecimal isOverdue;
	
	/**
	 * 录制方式
	 * 1-本地双录
	 * 2-小程序双录
	 * 3-掌上新华双录
	 */
	private BigDecimal recordType;
	
	/**
	 * 第一被保险人人证比对结果
	 */
	private String insuredIdcardRet;	
	/**
	 * 第二被保险人人证比对结果
	 */
	private String insuredIdcardRetSecond;	
	/**
	 * 第一被保险人人像比对结果
	 */
	private String insuredFaceRet;	
	/**
	 * 第二被保险人人像比对结果
	 */
	private String insuredFaceRetSecond;	
	/**
	 * 录制方式
	 * 1-本地双录
	 * 2-小程序双录
	 * 3-掌上新华双录
	 */
	public BigDecimal getRecordType() {
		return recordType;
	}
	/**
	 * 录制方式
	 * 1-本地双录
	 * 2-小程序双录
	 * 3-掌上新华双录
	 */
	public void setRecordType(BigDecimal recordType) {
		this.recordType = recordType;
	}
	
	public BigDecimal getIsOverdue() {
		return isOverdue;
	}

	public void setIsOverdue(BigDecimal isOverdue) {
		this.isOverdue = isOverdue;
	}

	public String getQtUser() {
		return qtUser;
	}

	public void setQtUser(String qtUser) {
		this.qtUser = qtUser;
	}

	
	public String getRelationToAll() {
		return relationToAll;
	}

	public void setRelationToAll(String relationToAll) {
		this.relationToAll = relationToAll;
	}

	public Date getQtSuccTime() {
		return qtSuccTime;
	}

	public void setQtSuccTime(Date qtSuccTime) {
		this.qtSuccTime = qtSuccTime;
	}

	public Date getApplyResultDate() {
		return applyResultDate;
	}

	public void setApplyResultDate(Date applyResultDate) {
		this.applyResultDate = applyResultDate;
	}

	public BigDecimal getThirdOrgFlag() {
		return thirdOrgFlag;
	}

	public void setThirdOrgFlag(BigDecimal thirdOrgFlag) {
		this.thirdOrgFlag = thirdOrgFlag;
	}
	public String getDataSource() {
		return dataSource;
	}

	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}

	public String getAudioInfoSize() {
		return audioInfoSize;
	}

	public void setAudioInfoSize(String audioInfoSize) {
		this.audioInfoSize = audioInfoSize;
	}

	public String getIsDrqDraw() {
		return isDrqDraw;
	}

	public void setIsDrqDraw(String isDrqDraw) {
		this.isDrqDraw = isDrqDraw;
	}

	public String getIsMutualInsured() {
		return isMutualInsured;
	}

	public void setIsMutualInsured(String isMutualInsured) {
		this.isMutualInsured = isMutualInsured;
	}
	public String getIsApplyResult() {
		return isApplyResult;
	}

	public void setIsApplyResult(String isApplyResult) {
		this.isApplyResult = isApplyResult;
	}

	public String getIsQualityAgent() {
		return isQualityAgent;
	}

	public void setIsQualityAgent(String isQualityAgent) {
		this.isQualityAgent = isQualityAgent;
	}

	public Date getQtTimeTwo() {
		return qtTimeTwo;
	}

	public void setQtTimeTwo(Date qtTimeTwo) {
		this.qtTimeTwo = qtTimeTwo;
	}

	public Date getVideoUploadDrstTwo() {
		return videoUploadDrstTwo;
	}

	public void setVideoUploadDrstTwo(Date videoUploadDrstTwo) {
		this.videoUploadDrstTwo = videoUploadDrstTwo;
	}

	public String getDrqTimeTwo() {
		return drqTimeTwo;
	}

	public void setDrqTimeTwo(String drqTimeTwo) {
		this.drqTimeTwo = drqTimeTwo;
	}

	public String getQtResultTwo() {
		return qtResultTwo;
	}

	public void setQtResultTwo(String qtResultTwo) {
		this.qtResultTwo = qtResultTwo;
	}

	public Date getQtTimeThree() {
		return qtTimeThree;
	}

	public void setQtTimeThree(Date qtTimeThree) {
		this.qtTimeThree = qtTimeThree;
	}

	public Date getVideoUploadDrstThree() {
		return videoUploadDrstThree;
	}

	public void setVideoUploadDrstThree(Date videoUploadDrstThree) {
		this.videoUploadDrstThree = videoUploadDrstThree;
	}

	public String getDrqTimeThree() {
		return drqTimeThree;
	}

	public void setDrqTimeThree(String drqTimeThree) {
		this.drqTimeThree = drqTimeThree;
	}

	public String getQtResultThree() {
		return qtResultThree;
	}

	public void setQtResultThree(String qtResultThree) {
		this.qtResultThree = qtResultThree;
	}

	public Date getQtTimeFour() {
		return qtTimeFour;
	}

	public void setQtTimeFour(Date qtTimeFour) {
		this.qtTimeFour = qtTimeFour;
	}

	public Date getVideoUploadDrstFour() {
		return videoUploadDrstFour;
	}

	public void setVideoUploadDrstFour(Date videoUploadDrstFour) {
		this.videoUploadDrstFour = videoUploadDrstFour;
	}

	public String getDrqTimeFour() {
		return drqTimeFour;
	}

	public void setDrqTimeFour(String drqTimeFour) {
		this.drqTimeFour = drqTimeFour;
	}

	public String getQtResultFour() {
		return qtResultFour;
	}

	public void setQtResultFour(String qtResultFour) {
		this.qtResultFour = qtResultFour;
	}

	public String getIssueStartDate() {
		return issueStartDate;
	}

	public void setIssueStartDate(String issueStartDate) {
		this.issueStartDate = issueStartDate;
	}

	public String getIssueEndDate() {
		return issueEndDate;
	}

	public void setIssueEndDate(String issueEndDate) {
		this.issueEndDate = issueEndDate;
	}

	public String getSalesOrganNameThree() {
		return salesOrganNameThree;
	}

	public void setSalesOrganNameThree(String salesOrganNameThree) {
		this.salesOrganNameThree = salesOrganNameThree;
	}

	public BigDecimal getTotalPremAf() {
		return totalPremAf;
	}

	public void setTotalPremAf(BigDecimal totalPremAf) {
		this.totalPremAf = totalPremAf;
	}

	public String getIsAgeFlag() {
		return isAgeFlag;
	}

	public void setIsAgeFlag(String isAgeFlag) {
		this.isAgeFlag = isAgeFlag;
	}

	public String getProposalStatus() {
		return proposalStatus;
	}

	public void setProposalStatus(String proposalStatus) {
		this.proposalStatus = proposalStatus;
	}

	public Date getVideoUploadDrst() {
		return videoUploadDrst;
	}

	public void setVideoUploadDrst(Date videoUploadDrst) {
		this.videoUploadDrst = videoUploadDrst;
	}

	public Date getQtTime() {
		return qtTime;
	}

	public void setQtTime(Date qtTime) {
		this.qtTime = qtTime;
	}

	
	public BigDecimal getIsImportDqr() {
		return isImportDqr;
	}

	public void setIsImportDqr(BigDecimal isImportDqr) {
		this.isImportDqr = isImportDqr;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getSalesChannelName() {
		return salesChannelName;
	}

	public void setSalesChannelName(String salesChannelName) {
		this.salesChannelName = salesChannelName;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getHolderName() {
		return holderName;
	}

	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getAgentLevel() {
		return agentLevel;
	}

	public void setAgentLevel(String agentLevel) {
		this.agentLevel = agentLevel;
	}

	public String getAgentLevelDesc() {
		return agentLevelDesc;
	}

	public void setAgentLevelDesc(String agentLevelDesc) {
		this.agentLevelDesc = agentLevelDesc;
	}

	public String getSalesOrganNameOne() {
		return salesOrganNameOne;
	}

	public void setSalesOrganNameOne(String salesOrganNameOne) {
		this.salesOrganNameOne = salesOrganNameOne;
	}

	public String getSalesOrganNameTwo() {
		return salesOrganNameTwo;
	}

	public void setSalesOrganNameTwo(String salesOrganNameTwo) {
		this.salesOrganNameTwo = salesOrganNameTwo;
	}

	public String getServiceBank() {
		return serviceBank;
	}

	public void setServiceBank(String serviceBank) {
		this.serviceBank = serviceBank;
	}

	public String getServiceBankBranch() {
		return serviceBankBranch;
	}

	public void setServiceBankBranch(String serviceBankBranch) {
		this.serviceBankBranch = serviceBankBranch;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankBranchName() {
		return bankBranchName;
	}

	public void setBankBranchName(String bankBranchName) {
		this.bankBranchName = bankBranchName;
	}

	public String getIfBankSale() {
		return ifBankSale;
	}

	public void setIfBankSale(String ifBankSale) {
		this.ifBankSale = ifBankSale;
	}

	public String getIfWillPas() {
		return ifWillPas;
	}

	public void setIfWillPas(String ifWillPas) {
		this.ifWillPas = ifWillPas;
	}

	public String getIfPasEd() {
		return ifPasEd;
	}

	public void setIfPasEd(String ifPasEd) {
		this.ifPasEd = ifPasEd;
	}

	public String getIfSelfInsured() {
		return ifSelfInsured;
	}

	public void setIfSelfInsured(String ifSelfInsured) {
		this.ifSelfInsured = ifSelfInsured;
	}

	public String getIfBackPas() {
		return ifBackPas;
	}

	public void setIfBackPas(String ifBackPas) {
		this.ifBackPas = ifBackPas;
	}

	public String getIfImportConclusion() {
		return ifImportConclusion;
	}

	public void setIfImportConclusion(String ifImportConclusion) {
		this.ifImportConclusion = ifImportConclusion;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public Date getIssueDate() {
		return issueDate;
	}

	public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}

	public Date getAcknowledgeDate() {
		return acknowledgeDate;
	}

	public void setAcknowledgeDate(Date acknowledgeDate) {
		this.acknowledgeDate = acknowledgeDate;
	}

	public Date getDrqUploadDate() {
		return drqUploadDate;
	}

	public void setDrqUploadDate(Date drqUploadDate) {
		this.drqUploadDate = drqUploadDate;
	}

	public Date getDrqStartDate() {
		return drqStartDate;
	}

	public void setDrqStartDate(Date drqStartDate) {
		this.drqStartDate = drqStartDate;
	}

	public Date getDrqEndDate() {
		return drqEndDate;
	}

	public void setDrqEndDate(Date drqEndDate) {
		this.drqEndDate = drqEndDate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getDrqConclusionDate() {
		return drqConclusionDate;
	}

	public void setDrqConclusionDate(Date drqConclusionDate) {
		this.drqConclusionDate = drqConclusionDate;
	}

	public String getDrqTime() {
		return drqTime;
	}

	public void setDrqTime(String drqTime) {
		this.drqTime = drqTime;
	}

	public String getQtResult() {
		return qtResult;
	}

	public void setQtResult(String qtResult) {
		this.qtResult = qtResult;
	}

	public String getQtResultName() {
		return qtResultName;
	}

	public void setQtResultName(String qtResultName) {
		this.qtResultName = qtResultName;
	}

	public BigDecimal getQtCounts() {
		return qtCounts;
	}

	public void setQtCounts(BigDecimal qtCounts) {
		this.qtCounts = qtCounts;
	}

	public String getLastNotPassReason() {
		return lastNotPassReason;
	}

	public void setLastNotPassReason(String lastNotPassReason) {
		this.lastNotPassReason = lastNotPassReason;
	}

	public String getFirstNotPassTime() {
		return firstNotPassTime;
	}

	public void setFirstNotPassTime(String firstNotPassTime) {
		this.firstNotPassTime = firstNotPassTime;
	}

	public String getIfQualityOver() {
		return ifQualityOver;
	}

	public void setIfQualityOver(String ifQualityOver) {
		this.ifQualityOver = ifQualityOver;
	}

	public String getIfChgOver() {
		return ifChgOver;
	}

	public void setIfChgOver(String ifChgOver) {
		this.ifChgOver = ifChgOver;
	}

	public String getDrqOperator() {
		return drqOperator;
	}

	public void setDrqOperator(String drqOperator) {
		this.drqOperator = drqOperator;
	}

	public String getIfDrqAgain() {
		return ifDrqAgain;
	}

	public void setIfDrqAgain(String ifDrqAgain) {
		this.ifDrqAgain = ifDrqAgain;
	}

	public String getDrqAgainConclusion() {
		return drqAgainConclusion;
	}

	public void setDrqAgainConclusion(String drqAgainConclusion) {
		this.drqAgainConclusion = drqAgainConclusion;
	}
	public String getInsuredIdcardRetSecond() {
		return insuredIdcardRetSecond;
	}
	public void setInsuredIdcardRetSecond(String insuredIdcardRetSecond) {
		this.insuredIdcardRetSecond = insuredIdcardRetSecond;
	}
	public String getInsuredFaceRetSecond() {
		return insuredFaceRetSecond;
	}
	public void setInsuredFaceRetSecond(String insuredFaceRetSecond) {
		this.insuredFaceRetSecond = insuredFaceRetSecond;
	}
	public String getDrqAgaOperator() {
		return drqAgaOperator;
	}

	public void setDrqAgaOperator(String drqAgaOperator) {
		this.drqAgaOperator = drqAgaOperator;
	}

	public String getIfAppChg() {
		return ifAppChg;
	}

	public void setIfAppChg(String ifAppChg) {
		this.ifAppChg = ifAppChg;
	}

	public String getAppChgOperator() {
		return appChgOperator;
	}

	public void setAppChgOperator(String appChgOperator) {
		this.appChgOperator = appChgOperator;
	}

	public String getIfChgDecio() {
		return ifChgDecio;
	}

	public void setIfChgDecio(String ifChgDecio) {
		this.ifChgDecio = ifChgDecio;
	}
	
	public String getQueryType() {
		return queryType;
	}

	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}
	public String getInsuredFaceRet() {
		return insuredFaceRet;
	}
	public void setInsuredFaceRet(String insuredFaceRet) {
		this.insuredFaceRet = insuredFaceRet;
	}
	public String getInsuredIdcardRet() {
		return insuredIdcardRet;
	}
	public void setInsuredIdcardRet(String insuredIdcardRet) {
		this.insuredIdcardRet = insuredIdcardRet;
	}
	

	@Override
	public String toString() {
		return "DRQualityAllVO [qtStatus=" + qtStatus + ", organCode="
				+ organCode + ", channelType=" + channelType + ", startDate="
				+ startDate + ", endDate=" + endDate + ", ifChgDecio="
				+ ifChgDecio + ", ifQualityOver=" + ifQualityOver
				+ ", ifChgOver=" + ifChgOver + ", ifImportConclusion="
				+ ifImportConclusion + ", agentLevel=" + agentLevel
				+ ", salesChannelName=" + salesChannelName + ", applyCode="
				+ applyCode + ", policyCode=" + policyCode + ", holderName="
				+ holderName + ", agentCode=" + agentCode + ", agentName="
				+ agentName + ", agentLevelDesc=" + agentLevelDesc
				+ ", salesOrganNameOne=" + salesOrganNameOne
				+ ", salesOrganNameTwo=" + salesOrganNameTwo
				+ ", salesOrganNameThree=" + salesOrganNameThree
				+ ", serviceBank=" + serviceBank + ", serviceBankBranch="
				+ serviceBankBranch + ", bankName=" + bankName
				+ ", bankBranchName=" + bankBranchName + ", ifBankSale="
				+ ifBankSale + ", ifWillPas=" + ifWillPas + ", ifPasEd="
				+ ifPasEd + ", ifBackPas=" + ifBackPas + ", ifSelfInsured="
				+ ifSelfInsured + ", applyDate=" + applyDate + ", issueDate="
				+ issueDate + ", acknowledgeDate=" + acknowledgeDate
				+ ", drqUploadDate=" + drqUploadDate + ", drqStartDate="
				+ drqStartDate + ", drqEndDate=" + drqEndDate + ", createTime="
				+ createTime + ", drqConclusionDate=" + drqConclusionDate
				+ ", qtResultName=" + qtResultName + ", qtCounts=" + qtCounts
				+ ", lastNotPassReason=" + lastNotPassReason
				+ ", firstNotPassTime=" + firstNotPassTime + ", drqOperator="
				+ drqOperator + ", ifDrqAgain=" + ifDrqAgain
				+ ", drqAgainConclusion=" + drqAgainConclusion
				+ ", drqAgaOperator=" + drqAgaOperator + ", ifAppChg="
				+ ifAppChg + ", appChgOperator=" + appChgOperator
				+ ", isImportDqr=" + isImportDqr + ", isAgeFlag=" + isAgeFlag
				+ ", proposalStatus=" + proposalStatus + ", videoUploadDrst="
				+ videoUploadDrst + ", qtTime=" + qtTime + ", insuredIdcardRet=" + insuredIdcardRet
				+ ", insuredIdcardRetSecond=" + insuredIdcardRetSecond+", insuredFaceRet="
						+ insuredFaceRet + ", insuredFaceRetSecond="
						+ insuredFaceRetSecond + "]";
	}
	@Override
	public String getBizId() {
		return null;
	}


}
