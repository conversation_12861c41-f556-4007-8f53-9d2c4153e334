package com.nci.tunan.qry.interfaces.model.nb.po;

import com.nci.udmp.framework.model.*;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;

/**
 * @description NbContractMasterPO对象
 * <AUTHOR>
 * @date 2015-01-05 16:59:03
 */
public class NbContractMasterPO extends BasePO { 
	/**
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
	 */

	private static final long serialVersionUID = 1L;

	/** 属性 --- java类型 --- oracle类型_数据长度_小数位长度_注释信息 */
	// operatorUserCode --- String --- VARCHAR2_20_0_null;
	// channelId --- BigDecimal --- NUMBER_10_0_null;
	// paUserCode --- String --- VARCHAR2_20_0_null;
	// nbDeclareIndi --- BigDecimal --- NUMBER_1_0_null;
	// nextAyerName --- String --- VARCHAR2_120_0_null;
	// proposalStatus --- String --- CHAR_2_0_null;
	// applyCode --- String --- VARCHAR2_20_0_null;
	// organCode --- String --- VARCHAR2_8_0_null;
	// reviewTime --- String --- VARCHAR2_10_0_null;
	// channelType --- String --- CHAR_1_0_null;
	// overdueTime --- Date --- DATE_7_0_null;
	// saleAgentName --- String --- VARCHAR2_120_0_null;
	// insuredFamily --- BigDecimal --- NUMBER_1_0_null;
	// serviceHandlerName --- String --- VARCHAR2_120_0_null;
	// nextPayBankAccount --- String --- VARCHAR2_30_0_null;
	// policyId --- BigDecimal --- NUMBER_10_0_null;
	// scanCompleteTime --- Date --- DATE_7_0_null;
	// campaignCode --- String --- VARCHAR2_30_0_null;
	// advancePremIndi --- BigDecimal --- NUMBER_1_0_null;
	// derivation --- String --- CHAR_1_0_null;
	// initialPayMode --- String --- CHAR_2_0_null;
	// channelOrgCode --- String --- VARCHAR2_30_0_null;
	// policyType --- String --- CHAR_1_0_null;
	// expiryDate --- Date --- DATE_7_0_null;
	// submitChannel --- String --- VARCHAR2_2_0_null;
	// liabilityState --- BigDecimal --- NUMBER_2_0_null;
	// policyCode --- String --- VARCHAR2_20_0_null;
	// saleAgentCode --- String --- VARCHAR2_20_0_null;
	// validateDate --- Date --- DATE_7_0_null;
	// ePolicyFlag --- BigDecimal --- NUMBER_1_0_null;
	// bodyExamIndi --- BigDecimal --- NUMBER_1_0_null;
	// moneyCode --- String --- CHAR_3_0_null;
	// serviceHandlerCode --- String --- VARCHAR2_20_0_null;
	// applyDate --- Date --- DATE_7_0_null;
	// branchOrganCode --- String --- VARCHAR2_8_0_null;
	// manualUwIndi --- BigDecimal --- NUMBER_1_0_null;
	// nextPayMode --- String --- CHAR_2_0_null;
	// submissionDate --- Date --- DATE_7_0_null;
	// uwUserCode --- String --- VARCHAR2_20_0_null;
	// serviceHandler --- String --- VARCHAR2_120_0_null;
	// eServiceFlag --- BigDecimal --- NUMBER_1_0_null;
	// survivalInvstIndi --- BigDecimal --- NUMBER_1_0_null;
	// uwCompleteTime --- Date --- DATE_7_0_null;
	// riskIndi --- BigDecimal --- NUMBER_1_0_null;
	// serviceBankBranch --- String --- VARCHAR2_20_0_null;
	// issueUserCode --- String --- VARCHAR2_20_0_null;
	// initialPayBankCode --- String --- VARCHAR2_30_0_null;
	// issueDate --- Date --- DATE_7_0_null;
	// initialPayerName --- String --- VARCHAR2_120_0_null;
	// highSaIndi --- BigDecimal --- NUMBER_1_0_null;
	// decisionCode --- String --- CHAR_2_0_null;
	// agentOrgId --- String --- VARCHAR2_20_0_null;
	// billChecked --- BigDecimal --- NUMBER_1_0_null;
	// serviceBank --- String --- VARCHAR2_30_0_null;
	// paCompleteTime --- Date --- DATE_7_0_null;
	// initialPayBankAccount --- String --- VARCHAR2_30_0_null;
	// birthdayPolIndi --- BigDecimal --- NUMBER_1_0_null;
	// langCode --- String --- VARCHAR2_3_0_null;
	// nextPayBankCode --- String --- VARCHAR2_30_0_null;
	// scanUserCode --- String --- VARCHAR2_20_0_null;
	// statusDesc --- String --- VARCHAR2_100_0_null;
	/**
	 * 50694 移动平台确认方式start
	 */
	public String getConfirmWay(){
		return getString("confirm_way");
	}

	public void setConfirmWay(String confirmWay){
		setString("confirm_way",confirmWay);
	}

	public BigDecimal getBankAgencyFlag() {
		return getBigDecimal("bank_agency_flag");
	}

	public void setBankAgencyFlag(BigDecimal bankAgencyFlag) {
		setBigDecimal("bank_agency_flag", bankAgencyFlag);
	}
	//173020综合查询增加渠道销售方式、是否为互联网保单标识
	//销售方式
	public void setSaleType(String saleType) {
		setString("sale_type", saleType);
	}
	public String getSaleType() {
		return getString("sale_type");
	}
	// 是否为互联网保单标识
	public void setInternetCooperationBusiness(String internetCooperationBusiness) {
		setString("internet_cooperation_business", internetCooperationBusiness);
	}
	public String getInternetCooperationBusiness() {
		return getString("internet_cooperation_business");
	}		
	// 签单日期起期
	public void setPolicySignDateS(Date policySignDateS) {
		setUtilDate("signDateS", policySignDateS);
	}

	public Date getPolicySignDateS() {
		return getUtilDate("signDateS");
	}

	// 签单日期止期
	public void setPolicySignDateE(Date policySignDateE) {
		setUtilDate("signDateE", policySignDateE);
	}

	public Date getPolicySignDateE() {
		return getUtilDate("signDateE");
	}

	public void setOperatorUserCode(String operatorUserCode) {
		setString("operator_user_code", operatorUserCode);
	}

	public String getOperatorUserCode() {
		return getString("operator_user_code");
	}

	public void setChannelId(String channelId) {
		setString("channel_id", channelId);
	}

	public String getChannelId() {
		return getString("channel_id");
	}

	public void setPaUserCode(String paUserCode) {
		setString("pa_user_code", paUserCode);
	}

	public String getPaUserCode() {
		return getString("pa_user_code");
	}

	public void setProposalStatus(String proposalStatus) {
		setString("proposal_status", proposalStatus);
	}

	public String getProposalStatus() {
		return getString("proposal_status");
	}

	public void setApplyCode(String applyCode) {
		setString("apply_code", applyCode);
	}

	public String getApplyCode() {
		return getString("apply_code");
	}

	public void setOrganCode(String organCode) {
		setString("organ_code", organCode);
	}

	public String getOrganCode() {
		return getString("organ_code");
	}

	public void setChannelType(String channelType) {
		setString("channel_type", channelType);
	}

	public String getChannelType() {
		return getString("channel_type");
	}

	public String getOrganCodeName() {
		return getString("organ_code_name");
	}

	public void setOrganCodeName(String organCodeName) {
		setString("organ_code_name", organCodeName);
	}


	public void setOverdueTime(Date overdueTime) {
		setUtilDate("overdue_time", overdueTime);
	}

	public Date getOverdueTime() {
		return getUtilDate("overdue_time");
	}

	public void setSaleAgentName(String saleAgentName) {
		setString("sale_agent_name", saleAgentName);
	}

	public String getSaleAgentName() {
		return getString("sale_agent_name");
	}

	public void setInsuredFamily(BigDecimal insuredFamily) {
		setBigDecimal("insured_family", insuredFamily);
	}

	public BigDecimal getInsuredFamily() {
		return getBigDecimal("insured_family");
	}

	public void setServiceHandlerName(String serviceHandlerName) {
		setString("service_handler_name", serviceHandlerName);
	}

	public String getServiceHandlerName() {
		return getString("service_handler_name");
	}

	public void setPolicyId(BigDecimal policyId) {
		setBigDecimal("policy_id", policyId);
	}

	public BigDecimal getPolicyId() {
		return getBigDecimal("policy_id");
	}

	public void setScanCompleteTime(Date scanCompleteTime) {
		setUtilDate("scan_complete_time", scanCompleteTime);
	}

	public Date getScanCompleteTime() {
		return getUtilDate("scan_complete_time");
	}

	public void setDerivation(String derivation) {
		setString("derivation", derivation);
	}

	public String getDerivation() {
		return getString("derivation");
	}

	// modified by yangyp_wb1 #20206变更修改
	/*
	 * public void setChannelOrgCode(String channelOrgCode) {
	 * setString("channel_org_code", channelOrgCode); }
	 * 
	 * public String getChannelOrgCode() { return getString("channel_org_code");
	 * }
	 */
	// modified end.

	public void setPolicyType(String policyType) {
		setString("policy_type", policyType);
	}

	public String getPolicyType() {
		return getString("policy_type");
	}

	public void setExpiryDate(Date expiryDate) {
		setUtilDate("expiry_date", expiryDate);
	}

	public Date getExpiryDate() {
		return getUtilDate("expiry_date");
	}

	public void setSubmitChannel(BigDecimal submitChannel) {
		setBigDecimal("submit_channel", submitChannel);
	}

	public BigDecimal getSubmitChannel() {
		return getBigDecimal("submit_channel");
	}

	public void setLiabilityState(BigDecimal liabilityState) {
		setBigDecimal("liability_state", liabilityState);
	}

	public BigDecimal getLiabilityState() {
		return getBigDecimal("liability_state");
	}

	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}

	public String getPolicyCode() {
		return getString("policy_code");
	}

	public void setSaleAgentCode(String saleAgentCode) {
		setString("sale_agent_code", saleAgentCode);
	}

	public String getSaleAgentCode() {
		return getString("sale_agent_code");
	}

	public void setValidateDate(Date validateDate) {
		setUtilDate("validate_date", validateDate);
	}

	public Date getValidateDate() {
		return getUtilDate("validate_date");
	}

	public void setMoneyCode(String moneyCode) {
		setString("money_code", moneyCode);
	}

	public String getMoneyCode() {
		return getString("money_code");
	}

	public void setServiceHandlerCode(String serviceHandlerCode) {
		setString("service_handler_code", serviceHandlerCode);
	}

	public String getServiceHandlerCode() {
		return getString("service_handler_code");
	}

	public void setApplyDate(Date applyDate) {
		setUtilDate("apply_date", applyDate);
	}

	public Date getApplyDate() {
		return getUtilDate("apply_date");
	}

	public void setBranchOrganCode(String branchOrganCode) {
		setString("branch_organ_code", branchOrganCode);
	}

	public String getBranchOrganCode() {
		return getString("branch_organ_code");
	}

	public void setManualUwIndi(BigDecimal manualUwIndi) {
		setBigDecimal("manual_uw_indi", manualUwIndi);
	}

	public BigDecimal getManualUwIndi() {
		return getBigDecimal("manual_uw_indi");
	}

	public void setSubmissionDate(Date submissionDate) {
		setUtilDate("submission_date", submissionDate);
	}

	public Date getSubmissionDate() {
		return getUtilDate("submission_date");
	}

	public void setUwUserCode(BigDecimal uwUserCode) {
		setBigDecimal("uw_user_code", uwUserCode);
	}

	public BigDecimal getUwUserCode() {
		return getBigDecimal("uw_user_code");
	}

	public void setServiceHandler(String serviceHandler) {
		setString("service_handler", serviceHandler);
	}

	public String getServiceHandler() {
		return getString("service_handler");
	}

	public void setEServiceFlag(BigDecimal eServiceFlag) {
		setBigDecimal("e_service_flag", eServiceFlag);
	}

	public BigDecimal getEServiceFlag() {
		return getBigDecimal("e_service_flag");
	}

	public void setUwCompleteTime(Date uwCompleteTime) {
		setUtilDate("uw_complete_time", uwCompleteTime);
	}

	public Date getUwCompleteTime() {
		return getUtilDate("uw_complete_time");
	}

	public void setRiskIndi(BigDecimal riskIndi) {
		setBigDecimal("risk_indi", riskIndi);
	}

	public BigDecimal getRiskIndi() {
		return getBigDecimal("risk_indi");
	}

	public void setServiceBankBranch(String serviceBankBranch) {
		setString("service_bank_branch", serviceBankBranch);
	}

	public String getServiceBankBranch() {
		return getString("service_bank_branch");
	}

	public void setIssueUserCode(String issueUserCode) {
		setString("issue_user_code", issueUserCode);
	}

	public String getIssueUserCode() {
		return getString("issue_user_code");
	}

	public void setIssueDate(Date issueDate) {
		setUtilDate("issue_date", issueDate);
	}

	public Date getIssueDate() {
		return getUtilDate("issue_date");
	}

	public void setHighSaIndi(BigDecimal highSaIndi) {
		setBigDecimal("high_sa_indi", highSaIndi);
	}

	public BigDecimal getHighSaIndi() {
		return getBigDecimal("high_sa_indi");
	}

	public void setDecisionCode(String decisionCode) {
		setString("decision_code", decisionCode);
	}

	public String getDecisionCode() {
		return getString("decision_code");
	}

	public void setAgentOrgId(String agentOrgId) {
		setString("agent_org_id", agentOrgId);
	}

	public String getAgentOrgId() {
		return getString("agent_org_id");
	}

	public void setBillChecked(BigDecimal billChecked) {
		setBigDecimal("bill_checked", billChecked);
	}

	public BigDecimal getBillChecked() {
		return getBigDecimal("bill_checked");
	}

	public void setServiceBank(String serviceBank) {
		setString("service_bank", serviceBank);
	}

	public String getServiceBank() {
		return getString("service_bank");
	}

	public void setPaCompleteTime(Date paCompleteTime) {
		setUtilDate("pa_complete_time", paCompleteTime);
	}

	public Date getPaCompleteTime() {
		return getUtilDate("pa_complete_time");
	}

	public void setBirthdayPolIndi(BigDecimal birthdayPolIndi) {
		setBigDecimal("birthday_pol_indi", birthdayPolIndi);
	}

	public BigDecimal getBirthdayPolIndi() {
		return getBigDecimal("birthday_pol_indi");
	}

	public void setLangCode(String langCode) {
		setString("lang_code", langCode);
	}

	public String getLangCode() {
		return getString("lang_code");
	}

	public void setScanUserCode(String scanUserCode) {
		setString("scan_user_code", scanUserCode);
	}

	public String getScanUserCode() {
		return getString("scan_user_code");
	}// statusDesc --- String --- VARCHAR2_100_0_null;

	public void setStatusDesc(String statusDesc) {
		setString("status_desc", statusDesc);
	}

	public String getStatusDesc() {
		return getString("status_desc");
	}

	public void setStartDate(Date startDate) {
		setUtilDate("start_date", startDate);
	}

	public Date getStartDate() {
		return getUtilDate("start_date");
	}

	public void setEndDate(Date endDate) {
		setUtilDate("end_date", endDate);
	}

	public Date getEndDate() {
		return getUtilDate("end_date");
	}

	public void setMediaType(BigDecimal mediaType) {
		setBigDecimal("media_type", mediaType);
	}

	public BigDecimal getMediaType() {
		return getBigDecimal("media_type");
	}

	public void setAplPermit(BigDecimal aplPermit) {
		setBigDecimal("apl_permit", aplPermit);
	}

	public BigDecimal getAplPermit() {
		return getBigDecimal("apl_permit");
	}

	public void setPolicyPwd(String policyPwd) {
		setString("policy_pwd", policyPwd);
	}

	public String getPolicyPwd() {
		return getString("policy_pwd");
	}

	public void setAgencyCode(String agencyCode) {
		setString("agency_code", agencyCode);
	}

	public String getAgencyCode() {
		return getString("agency_code");
	}

	public void setEventCode(String eventCode) {
		setString("event_code", eventCode);
	}

	public String getEventCode() {
		return getString("event_code");
	}

	public void setOrganName(String organName) {
		setString("organ_name", organName);
	}

	public String getOrganName() {
		return getString("organ_name");
	}

	public void setIcount(BigDecimal iCount) {
		setBigDecimal("iCount", iCount);
	}

	public BigDecimal getIcount() {
		return getBigDecimal("iCount");
	}

	public void setTypeName(String typeName) {
		setString("type_name", typeName);
	}

	public String getTypeName() {
		return getString("type_name");
	}

	public Date getInputDate() {
		return getUtilDate("input_date");
	}

	public void setInputDate(Date inputDate) {
		setUtilDate("input_date", inputDate);
	}

	// 签单用
	// 通知书编号
	public void setDocumentNo(String documentNo) {
		setString("document_no", documentNo);
	}

	public String getDocumentNo() {
		return getString("document_no");
	}

	// 模板编码
	public void setTemplateCode(String templateCode) {
		setString("template_code", templateCode);
	}

	public String getTemplateCode() {
		return getString("template_code");
	}

	public void setCallTimeList(String callTimeList) {
		setString("call_time_list", callTimeList);
	}

	public String getCallTimeList() {
		return getString("call_time_list");
	}

	public void setAppointValidate(Date appointValidate) {
		setUtilDate("appoint_validate", appointValidate);
	}

	public Date getAppointValidate() {
		return getUtilDate("appoint_validate");
	}

	public void setPrintId(String printId) {
		setString("print_id", printId);
	}

	public String getPrintId() {
		return getString("print_id");
	}

	public BigDecimal getDialectIndi() {
		return getBigDecimal("dialect_indi");
	}

	public void setDialectIndi(BigDecimal dialectIndi) {
		setBigDecimal("dialect_indi", dialectIndi);
	}

	public BigDecimal getWinningStartFlag() {
		return getBigDecimal("winning_start_flag");
	}

	public void setWinningStartFlag(BigDecimal winningStartFlag) {
		setBigDecimal("winning_start_flag", winningStartFlag);
	}

	public void setTransactionNo(String transactionNo) {
		setString("transaction_no", transactionNo);
	}

	public String getTransactionNo() {
		return getString("transaction_no");
	}

	public void setInputType(String inputType) {
		setString("input_type", inputType);
	}

	public String getInputType() {
		return getString("input_type");
	}

	public void setIsBankLoan(String isBankLoan) {
		setString("is_bank_loan", isBankLoan);
	}

	public String getIsBankLoan() {
		return getString("is_bank_loan");
	}

	public void setAgentCode(String agentCode) {
		setString("agent_code", agentCode);
	}

	public String getAgentCode() {
		return getString("agent_code");
	}

	public void setAgentOrganCodeName(String agentOrganCodeName) {
		setString("agent_organ_code_name", agentOrganCodeName);
	}

	public String getAgentOrganCodeName() {
		return getString("agent_organ_code_name");
	}


	public void setAgentName(String agentName) {
		setString("agent_name", agentName);
	}

	public String getAgentName() {
		return getString("agent_name");
	}

	public void setAgentLevel(String agentLevel) {
		setString("agent_level", agentLevel);
	}

	public String getAgentLevel() {
		return getString("agent_level");
	}

	public void setAgentMobile(String agentMobile) {
		setString("agent_mobile", agentMobile);
	}

	public String getAgentMobile() {
		return getString("agent_mobile");
	}

	public void setAgentOrganCode(String agentOrganCode) {
		setString("agent_organ_code", agentOrganCode);
	}

	public String getAgentOrganCode() {
		return getString("agent_organ_code");
	}

	public void setRelationToPh(String relationToPh) {
		setString("relation_to_ph", relationToPh);
	}

	public String getRelationToPh() {
		return getString("relation_to_ph");
	}

	public String getBankCode() {
		return getString("bank_code");
	}

	public void setBankCode(String bankCode) {
		setString("bank_code",bankCode);
	}

	public String getBankBranchCode() {
		return getString("bank_branch_code");
	}

	public void setBankBranchCode(String bankBranchCode) {
		setString("bank_branch_code",bankBranchCode);
	}
	public Date getAcknowledgeDate() {
		return getUtilDate("acknowledge_date");
	}

	public void setAcknowledgeDate(Date acknowledgeDate) {
		setUtilDate("acknowledge_date",acknowledgeDate);
	}

	//是否双录标识
	public BigDecimal getDrqFlag() {
		return getBigDecimal("drq_flag");
	}

	public void setDrqFlag(BigDecimal drqFlag) {
		setBigDecimal("drq_flag",drqFlag);
	}

	public Date getUwSubmitTime() {
		return getUtilDate("uw_submit_time");
	}

	public void setUwSubmitTime(Date uwSubmitTime) {
		setUtilDate("uw_submit_time",uwSubmitTime);
	}

	public String getRuleMag() {
		return getString("rule_mag");
	}

	public void setRuleMag(String ruleMag) {
		setString("rule_mag",ruleMag);
	}

	/**
	 * #55326
	 * @return
	 */
	public String getGroupTypeCode(){ 
		return getString("group_sale_type");
	}

	public void setGroupTypeCode(String groupTypeCode){
		setString("group_sale_type",groupTypeCode);
	}

	/**
	 * 52031 中介机构码  中介机构名称 start
	 */
	public String getOtherManageCom(){
		return getString("other_manage_com");
	}

	public void setOtherManageCom(String otherManageCom){
		setString("other_manage_com",otherManageCom);
	}

	public String getOtherManageName(){
		return getString("other_manage_name");
	}

	public void setOtherManageName(String otherManageName){
		setString("other_manage_name",otherManageName);
	}
	/**
	 * #48816 增加 “中介机构代码”，“中介机构名称”，“协议号” 字段
	 */
	public String getManageComOuter(){
		return getString("manage_com_outer");
	}

	public void setManageComOuter(String manageComOuter){
		setString("manage_com_outer",manageComOuter);
	}

	public String getProtocolId(){
		return getString("protocol_id");
	}

	public void setProtocolId(String protocolId){
		setString("protocol_id",protocolId);
	}

	//中介机构编码
	public String getEntryNo(){
		return getString("entry_no");
	}

	public void setEntryNo(String entryNo){
		setString("entry_no",entryNo);
	}

	//投保人、被保险人或受益人与业务员关系
	public String getRelationToAll(){
		return getString("relation_to_all");
	}

	public void setRelationToAll(String relationToAll){
		setString("relation_to_all",relationToAll);
	}
	public void setOldPolicyCode(String oldPolicyCode){
		setString("old_policy_code",oldPolicyCode);
	}

	public String getOldPolicyCode(){
		return getString("old_policy_code");
	}

	public void setPolicyReinsureFlag(String policyReinsureFlag){
		setString("policy_reinsure_flag",policyReinsureFlag);
	}

	public String getPolicyReinsureFlag(){
		return getString("policy_reinsure_flag");
	}

	public void setSubinputType(String subinputType){
		setString("subinput_type",subinputType);
	}

	public String getSubinputType(){
		return getString("subinput_type");
	}

	/*是否互保件*/
	public String getIsMutualInsured(){
		return getString("is_mutual_insured");
	}

	public void setIsMutualInsured(String isMutualInsured){
		setString("is_mutual_insured",isMutualInsured);
	}
	/*是否自保件*/
	public String getIsSelfInsured() {
		return getString("is_self_insured");
	}

	public void setIsSelfInsured(String isSelfInsured) {
		setString("is_self_insured",isSelfInsured);
	}
	//91763 契约常量表

	public void setConstantsDesc(String constantsDesc) {
		setString("constants_desc", constantsDesc);
	}

	public String getConstantsDesc() {
		return getString("constants_desc");
	}

	public void setConstantsValue(String constantsValue) {
		setString("constants_value", constantsValue);
	}

	public String getConstantsValue() {
		return getString("constants_value");
	}

	public void setConstantsKey(String constantsKey) {
		setString("constants_key", constantsKey);
	}

	public String getConstantsKey() {
		return getString("constants_key");
	}

	public void setConstantsId(BigDecimal constantsId) {
		setBigDecimal("constants_id", constantsId);
	}

	public BigDecimal getConstantsId() {
		return getBigDecimal("constants_id");
	}

	public void setSubId(String subId) {
		setString("sub_id", subId);
	}

	public String getSubId() {
		return getString("sub_id");
	}

	public void setOnlineFlag(String onlineFlag) {
		setString("online_flag", onlineFlag);
	}

	public String getOnlineFlag() {
		return getString("online_flag");
	}

	public void setPersonalInfoProtect(String personalInfoProtect) {
		setString("personal_info_protect", personalInfoProtect);
	}

	public String getPersonalInfoProtect() {
		return getString("personal_info_protect");
	}
	public String getSpecialAccountFlag() {
		return getString("special_account_flag");
	}
	public void setSpecialAccountFlag(String specialAccountFlag) {
		setString("special_account_flag", specialAccountFlag);
	}

	public String getBankManagerLicenseNo() {
		return getString("bank_manager_licenseno");
	}
	public void setBankManagerLicenseNo(String bankManagerLicenseNo) {
		setString("bank_manager_licenseno",bankManagerLicenseNo);
	}

	public void setApplySource(BigDecimal applySource){
		setBigDecimal("apply_source", applySource);
	}

	public BigDecimal getApplySource(){
		return getBigDecimal("apply_source");
	}
	
	public void setApplySourceName(String applySource){
		setString("apply_source_name", applySource);
	}
	
	public String getApplySourceName(){
		return getString("apply_source_name");
	}
	
	/**
	 * 纸质回执回销日期，记录纸质保单回执录入系统日期
	 */
	public void setpAcknowledgeDate(Date pAcknowledgeDate){
        setUtilDate("p_acknowledge_date", pAcknowledgeDate);
    }
	
	/**
	 * 纸质回执回销日期，记录纸质保单回执录入系统日期
	 */
    public Date getpAcknowledgeDate(){
        return getUtilDate("p_acknowledge_date");
    }
    
    public BigDecimal getNotificationReceiveMethod() {
		return getBigDecimal("notification_receive_method");
	}
	public void setNotificationReceiveMethod(BigDecimal notificationReceiveMethod) {
		setBigDecimal("notification_receive_method", notificationReceiveMethod);
	}
	public void setBanknrtFalg(BigDecimal banknrtFalg) {
		setBigDecimal("banknrt_falg", banknrtFalg);
	}

	public BigDecimal getBanknrtFalg() {
		return getBigDecimal("banknrt_falg");
	}
	/**
	 * 被保人录入顺序    984产品
	 */
	public BigDecimal getInputSequence() {
		return getBigDecimal("input_sequence");
	}

	public void setInputSequence(BigDecimal inputSequence) {
		setBigDecimal("input_sequence", inputSequence);
	}
	/**
	 * 160232 合作中介机构协议号
	 */	
	public String getCooperationProtocolId() {
		return getString("COOPERATION_PROTOCOL_ID");
	}

	public void setCooperationProtocolId(String cooperationProtocolId) {
		setString("COOPERATION_PROTOCOL_ID",cooperationProtocolId);
	}


	/**
	 * EAST自保件 YES_OR_NO
	 */
	public BigDecimal getEastSelfInsured() {
		return getBigDecimal("east_self_insured");
	}
	/**
	 * EAST自保件 YES_OR_NO
	 */
	public void setEastSelfInsured(BigDecimal eastSelfInsured) {
		this.setBigDecimal("east_self_insured",eastSelfInsured);
	}
	/**
	 * EAST互保件 YES_OR_NO
	 */
	public BigDecimal getEastMutualInsured() {
		return this.getBigDecimal("east_mutual_insured");
	}
	/**
	 * EAST互保件 YES_OR_NO
	 */
	public void setEastMutualInsured(BigDecimal eastMutualInsured) {
		this.setBigDecimal("east_mutual_insured",eastMutualInsured);
	}
	@Override
	public String toString() {
		return "NbContractMasterPO [getBankAgencyFlag()=" + getBankAgencyFlag()
				+ ", getPolicySignDateS()=" + getPolicySignDateS()
				+ ", getPolicySignDateE()=" + getPolicySignDateE()
				+ ", getOperatorUserCode()=" + getOperatorUserCode()
				+ ", getChannelId()=" + getChannelId() + ", getPaUserCode()="
				+ getPaUserCode() + ", getProposalStatus()="
				+ getProposalStatus() + ", getApplyCode()=" + getApplyCode()
				+ ", getOrganCode()=" + getOrganCode()
				+ ", getOrganCodeName()=" + getOrganCodeName()
				+ ", getChannelType()=" + getChannelType()
				+ ", getOverdueTime()=" + getOverdueTime()
				+ ", getSaleAgentName()=" + getSaleAgentName()
				+ ", getInsuredFamily()=" + getInsuredFamily()
				+ ", getServiceHandlerName()=" + getServiceHandlerName()
				+ ", getPolicyId()=" + getPolicyId()
				+ ", getScanCompleteTime()=" + getScanCompleteTime()
				+ ", getDerivation()=" + getDerivation() + ", getPolicyType()="
				+ getPolicyType() + ", getExpiryDate()=" + getExpiryDate()
				+ ", getSubmitChannel()=" + getSubmitChannel()
				+ ", getLiabilityState()=" + getLiabilityState()
				+ ", getPolicyCode()=" + getPolicyCode()
				+ ", getSaleAgentCode()=" + getSaleAgentCode()
				+ ", getValidateDate()=" + getValidateDate()
				+ ", getMoneyCode()=" + getMoneyCode()
				+ ", getServiceHandlerCode()=" + getServiceHandlerCode()
				+ ", getApplyDate()=" + getApplyDate()
				+ ", getBranchOrganCode()=" + getBranchOrganCode()
				+ ", getManualUwIndi()=" + getManualUwIndi()
				+ ", getSubmissionDate()=" + getSubmissionDate()
				+ ", getUwUserCode()=" + getUwUserCode()
				+ ", getServiceHandler()=" + getServiceHandler()
				+ ", getEServiceFlag()=" + getEServiceFlag()
				+ ", getUwCompleteTime()=" + getUwCompleteTime()
				+ ", getRiskIndi()=" + getRiskIndi()
				+ ", getServiceBankBranch()=" + getServiceBankBranch()
				+ ", getIssueUserCode()=" + getIssueUserCode()
				+ ", getIssueDate()=" + getIssueDate() + ", getHighSaIndi()="
				+ getHighSaIndi() + ", getDecisionCode()=" + getDecisionCode()
				+ ", getAgentOrgId()=" + getAgentOrgId()
				+ ", getBillChecked()=" + getBillChecked()
				+ ", getServiceBank()=" + getServiceBank()
				+ ", getPaCompleteTime()=" + getPaCompleteTime()
				+ ", getBirthdayPolIndi()=" + getBirthdayPolIndi()
				+ ", getLangCode()=" + getLangCode() + ", getScanUserCode()="
				+ getScanUserCode() + ", getStatusDesc()=" + getStatusDesc()
				+ ", getStartDate()=" + getStartDate() + ", getEndDate()="
				+ getEndDate() + ", getMediaType()=" + getMediaType()
				+ ", getAplPermit()=" + getAplPermit() + ", getPolicyPwd()="
				+ getPolicyPwd() + ", getAgencyCode()=" + getAgencyCode()
				+ ", getEventCode()=" + getEventCode() + ", getOrganName()="
				+ getOrganName() + ", getIcount()=" + getIcount()
				+ ", getTypeName()=" + getTypeName() + ", getInputDate()="
				+ getInputDate() + ", getDocumentNo()=" + getDocumentNo()
				+ ", getTemplateCode()=" + getTemplateCode()
				+ ", getCallTimeList()=" + getCallTimeList()
				+ ", getAppointValidate()=" + getAppointValidate()
				+ ", getPrintId()=" + getPrintId() + ", getDialectIndi()="
				+ getDialectIndi() + ", getWinningStartFlag()="
				+ getWinningStartFlag() + ", getTransactionNo()="
				+ getTransactionNo() + ", getInputType()=" + getInputType()
				+ ", getIsBankLoan()=" + getIsBankLoan() + ", getAgentCode()="
				+ getAgentCode() + ", getAgentOrganCodeName()="
				+ getAgentOrganCodeName() + ", getAgentName()="
				+ getAgentName() + ", getAgentLevel()=" + getAgentLevel()
				+ ", getAgentMobile()=" + getAgentMobile()
				+ ", getAgentOrganCode()=" + getAgentOrganCode()
				+ ", getRelationToPh()=" + getRelationToPh()
				+ ", getUwSubmitTime()=" + getUwSubmitTime()
				+ ", getRuleMag()=" + getRuleMag() + ", getDrqFlag()="
				+ getDrqFlag() + ", getAcknowledgeDate()="
				+ getAcknowledgeDate() + ", getBankCode()=" + getBankCode()
				+ ", getBankBranchCode()=" + getBankBranchCode()
				+ ", getGroupTypeCode()=" + getGroupTypeCode()
				+ ", getManageComOuter()=" + getManageComOuter()
				+ ", getOtherManageName()=" + getOtherManageName()
				+ ", getProtocolId()=" + getProtocolId() +",getBanknrtFalg()="+getBanknrtFalg()
				+", getEntryNo()="+ getEntryNo() + "]";
	}

}
