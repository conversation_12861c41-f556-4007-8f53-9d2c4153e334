package com.nci.tunan.qry.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * @discript BPO任务清单VO
 * <AUTHOR>
 * 
 */
public class BpoTaskBillVO extends BaseVO {

	private static final long serialVersionUID = -1061155735776746241L;
	
	private String applyCode;
	private String billcardNo;
	private String organCode;
	private String organName;
	private BigDecimal oldOrgId;
	private BigDecimal newOrgId;
	private String oldOrgName;
	private String newOrgName;
	private String billcardCode;
	private Date sendDate;
	private Date sendTime;
	private Date receiveDate;
	private String receiveTime;
	private String receiveState;
	private String backState;
	private String sendState;
	private String receiveError;
	private String startDate;// 统计开始时间
	private String endDate;// 统计开始时间
	
	private BigDecimal issueSign;//标准件标识
	private String billcardName ;//单证类型名称
	
	//add by chenjb 20180511 BPO任务清单页面查询条件及结果修改   begin
	private String salesChannelCode;// 销售渠道
	private String salesChannelName;
	private String isQualityAgent; // 是否绩优 0/1
	private String customerLevelCode;// 客户等级
	private String customerLevelDesc; // 等级描述
	private String agentCode; // 业务员编号
	private String agentLevel; // 业务员等级
	private String agentLevelDesc;
	private String holderCustomerLevel; // 投保人客户等级
	private String holderCustomerLevelDesc;
	private String insuredCustomerLevel; // 被保人客户等级
	private String insuredCustomerLevelDesc;
	private String beneCustomerLevel; // 受益人客户等级
	private String beneCustomerLevelDesc;
	
	
	private String agentName; //业务员姓名
	private String branchOrganCode; //分公司机构
	private String branchOrganName; //分公司名称
	private String serviceBank; //银行
	private String bankCode; 
	private String bankName; 	//银行名称
	private String serviceBankBranch; 	//银行网点
	private String bankBranchName; 	//银行网点名称
	private String adminOrganCode; 	//营销服务部机构
	private String adminOrganName; 	//营销服务部名称
	private BigDecimal relationId; 	//报文号码
	private String  receiveFileName; 	//报文号码
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	@Override
	public String toString() {
		return "BpoTaskBillVO [applyCode=" + applyCode + ", billcardNo="
				+ billcardNo + ", organCode=" + organCode + ", organName="
				+ organName + ", oldOrgId=" + oldOrgId + ", newOrgId="
				+ newOrgId + ", oldOrgName=" + oldOrgName + ", newOrgName="
				+ newOrgName + ", billcardCode=" + billcardCode + ", sendDate="
				+ sendDate + ", sendTime=" + sendTime + ", receiveDate="
				+ receiveDate + ", receiveTime=" + receiveTime
				+ ", receiveState=" + receiveState + ", backState=" + backState
				+ ", sendState=" + sendState + ", receiveError=" + receiveError
				+ ", startDate=" + startDate + ", endDate=" + endDate
				+ ", issueSign=" + issueSign + ", billcardName=" + billcardName
				+ ", salesChannelCode=" + salesChannelCode
				+ ", salesChannelName=" + salesChannelName
				+ ", isQualityAgent=" + isQualityAgent + ", customerLevelCode="
				+ customerLevelCode + ", customerLevelDesc="
				+ customerLevelDesc + ", agentCode=" + agentCode
				+ ", agentLevel=" + agentLevel + ", agentLevelDesc="
				+ agentLevelDesc + ", holderCustomerLevel="
				+ holderCustomerLevel + ", holderCustomerLevelDesc="
				+ holderCustomerLevelDesc + ", insuredCustomerLevel="
				+ insuredCustomerLevel + ", insuredCustomerLevelDesc="
				+ insuredCustomerLevelDesc + ", beneCustomerLevel="
				+ beneCustomerLevel + ", beneCustomerLevelDesc="
				+ beneCustomerLevelDesc + ", agentName=" + agentName
				+ ", branchOrganCode=" + branchOrganCode + ", branchOrganName="
				+ branchOrganName + ", serviceBank=" + serviceBank
				+ ", bankCode=" + bankCode + ", bankName=" + bankName
				+ ", serviceBankBranch=" + serviceBankBranch
				+ ", bankBranchName=" + bankBranchName + ", adminOrganCode="
				+ adminOrganCode + ", adminOrganName=" + adminOrganName
				+ ", relationId=" + relationId + ", receiveFileName="
				+ receiveFileName + ", getReceiveFileName()="
				+ getReceiveFileName() + ", getAgentName()=" + getAgentName()
				+ ", getBranchOrganCode()=" + getBranchOrganCode()
				+ ", getBranchOrganName()=" + getBranchOrganName()
				+ ", getServiceBank()=" + getServiceBank() + ", getBankCode()="
				+ getBankCode() + ", getBankName()=" + getBankName()
				+ ", getServiceBankBranch()=" + getServiceBankBranch()
				+ ", getBankBranchName()=" + getBankBranchName()
				+ ", getAdminOrganCode()=" + getAdminOrganCode()
				+ ", getAdminOrganName()=" + getAdminOrganName()
				+ ", getRelationId()=" + getRelationId()
				+ ", getSalesChannelCode()=" + getSalesChannelCode()
				+ ", getSalesChannelName()=" + getSalesChannelName()
				+ ", getIsQualityAgent()=" + getIsQualityAgent()
				+ ", getCustomerLevelCode()=" + getCustomerLevelCode()
				+ ", getCustomerLevelDesc()=" + getCustomerLevelDesc()
				+ ", getAgentCode()=" + getAgentCode() + ", getAgentLevel()="
				+ getAgentLevel() + ", getAgentLevelDesc()="
				+ getAgentLevelDesc() + ", getHolderCustomerLevel()="
				+ getHolderCustomerLevel() + ", getHolderCustomerLevelDesc()="
				+ getHolderCustomerLevelDesc() + ", getInsuredCustomerLevel()="
				+ getInsuredCustomerLevel()
				+ ", getInsuredCustomerLevelDesc()="
				+ getInsuredCustomerLevelDesc() + ", getBeneCustomerLevel()="
				+ getBeneCustomerLevel() + ", getBeneCustomerLevelDesc()="
				+ getBeneCustomerLevelDesc() + ", getBillcardName()="
				+ getBillcardName() + ", getIssueSign()=" + getIssueSign()
				+ ", getApplyCode()=" + getApplyCode() + ", getOrganName()="
				+ getOrganName() + ", getBackState()=" + getBackState()
				+ ", getBillcardNo()=" + getBillcardNo() + ", getOldOrgId()="
				+ getOldOrgId() + ", getNewOrgId()=" + getNewOrgId()
				+ ", getOldOrgName()=" + getOldOrgName() + ", getNewOrgName()="
				+ getNewOrgName() + ", getBillcardCode()=" + getBillcardCode()
				+ ", getSendDate()=" + getSendDate() + ", getSendTime()="
				+ getSendTime() + ", getReceiveDate()=" + getReceiveDate()
				+ ", getReceiveTime()=" + getReceiveTime()
				+ ", getReceiveState()=" + getReceiveState()
				+ ", getSendState()=" + getSendState() + ", getReceiveError()="
				+ getReceiveError() + ", getStartDate()=" + getStartDate()
				+ ", getEndDate()=" + getEndDate() + ", getOrganCode()="
				+ getOrganCode() + ", getBizId()=" + getBizId()
				+ ", getClass()=" + getClass() + ", hashCode()=" + hashCode()
				+ ", toString()=" + super.toString() + "]";
	}

	public String getReceiveFileName() {
		return receiveFileName;
	}

	public void setReceiveFileName(String receiveFileName) {
		this.receiveFileName = receiveFileName;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getBranchOrganCode() {
		return branchOrganCode;
	}

	public void setBranchOrganCode(String branchOrganCode) {
		this.branchOrganCode = branchOrganCode;
	}

	public String getBranchOrganName() {
		return branchOrganName;
	}

	public void setBranchOrganName(String branchOrganName) {
		this.branchOrganName = branchOrganName;
	}

	public String getServiceBank() {
		return serviceBank;
	}

	public void setServiceBank(String serviceBank) {
		this.serviceBank = serviceBank;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getServiceBankBranch() {
		return serviceBankBranch;
	}

	public void setServiceBankBranch(String serviceBankBranch) {
		this.serviceBankBranch = serviceBankBranch;
	}

	public String getBankBranchName() {
		return bankBranchName;
	}

	public void setBankBranchName(String bankBranchName) {
		this.bankBranchName = bankBranchName;
	}

	public String getAdminOrganCode() {
		return adminOrganCode;
	}

	public void setAdminOrganCode(String adminOrganCode) {
		this.adminOrganCode = adminOrganCode;
	}

	public String getAdminOrganName() {
		return adminOrganName;
	}

	public void setAdminOrganName(String adminOrganName) {
		this.adminOrganName = adminOrganName;
	}

	public BigDecimal getRelationId() {
		return relationId;
	}

	public void setRelationId(BigDecimal relationId) {
		this.relationId = relationId;
	}

	public String getSalesChannelCode() {
		return salesChannelCode;
	}

	public void setSalesChannelCode(String salesChannelCode) {
		this.salesChannelCode = salesChannelCode;
	}

	public String getSalesChannelName() {
		return salesChannelName;
	}

	public void setSalesChannelName(String salesChannelName) {
		this.salesChannelName = salesChannelName;
	}

	public String getIsQualityAgent() {
		return isQualityAgent;
	}

	public void setIsQualityAgent(String isQualityAgent) {
		this.isQualityAgent = isQualityAgent;
	}

	public String getCustomerLevelCode() {
		return customerLevelCode;
	}

	public void setCustomerLevelCode(String customerLevelCode) {
		this.customerLevelCode = customerLevelCode;
	}

	public String getCustomerLevelDesc() {
		return customerLevelDesc;
	}

	public void setCustomerLevelDesc(String customerLevelDesc) {
		this.customerLevelDesc = customerLevelDesc;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentLevel() {
		return agentLevel;
	}

	public void setAgentLevel(String agentLevel) {
		this.agentLevel = agentLevel;
	}

	public String getAgentLevelDesc() {
		return agentLevelDesc;
	}

	public void setAgentLevelDesc(String agentLevelDesc) {
		this.agentLevelDesc = agentLevelDesc;
	}

	public String getHolderCustomerLevel() {
		return holderCustomerLevel;
	}

	public void setHolderCustomerLevel(String holderCustomerLevel) {
		this.holderCustomerLevel = holderCustomerLevel;
	}

	public String getHolderCustomerLevelDesc() {
		return holderCustomerLevelDesc;
	}

	public void setHolderCustomerLevelDesc(String holderCustomerLevelDesc) {
		this.holderCustomerLevelDesc = holderCustomerLevelDesc;
	}

	public String getInsuredCustomerLevel() {
		return insuredCustomerLevel;
	}

	public void setInsuredCustomerLevel(String insuredCustomerLevel) {
		this.insuredCustomerLevel = insuredCustomerLevel;
	}

	public String getInsuredCustomerLevelDesc() {
		return insuredCustomerLevelDesc;
	}

	public void setInsuredCustomerLevelDesc(String insuredCustomerLevelDesc) {
		this.insuredCustomerLevelDesc = insuredCustomerLevelDesc;
	}

	public String getBeneCustomerLevel() {
		return beneCustomerLevel;
	}

	public void setBeneCustomerLevel(String beneCustomerLevel) {
		this.beneCustomerLevel = beneCustomerLevel;
	}

	public String getBeneCustomerLevelDesc() {
		return beneCustomerLevelDesc;
	}

	public void setBeneCustomerLevelDesc(String beneCustomerLevelDesc) {
		this.beneCustomerLevelDesc = beneCustomerLevelDesc;
	}

	// add by chenjb 20180511 BPO任务清单页面查询条件及结果修改 end

	public String getBillcardName() {
		return billcardName;
	}

	public void setBillcardName(String billcardName) {
		this.billcardName = billcardName;
	}

	public BigDecimal getIssueSign() {
		return issueSign;
	}

	public void setIssueSign(BigDecimal issueSign) {
		this.issueSign = issueSign;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getOrganName() {
		return organName;
	}

	public String getBackState() {
		return backState;
	}

	public void setBackState(String backState) {
		this.backState = backState;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getBillcardNo() {
		return billcardNo;
	}

	public void setBillcardNo(String billcardNo) {
		this.billcardNo = billcardNo;
	}

	public BigDecimal getOldOrgId() {
		return oldOrgId;
	}

	public void setOldOrgId(BigDecimal oldOrgId) {
		this.oldOrgId = oldOrgId;
	}

	public BigDecimal getNewOrgId() {
		return newOrgId;
	}

	public void setNewOrgId(BigDecimal newOrgId) {
		this.newOrgId = newOrgId;
	}

	public String getOldOrgName() {
		return oldOrgName;
	}

	public void setOldOrgName(String oldOrgName) {
		this.oldOrgName = oldOrgName;
	}

	public String getNewOrgName() {
		return newOrgName;
	}

	public void setNewOrgName(String newOrgName) {
		this.newOrgName = newOrgName;
	}

	public String getBillcardCode() {
		return billcardCode;
	}

	public void setBillcardCode(String billcardCode) {
		this.billcardCode = billcardCode;
	}

	public Date getSendDate() {
		return sendDate;
	}

	public void setSendDate(Date sendDate) {
		this.sendDate = sendDate;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public Date getReceiveDate() {
		return receiveDate;
	}

	public void setReceiveDate(Date receiveDate) {
		this.receiveDate = receiveDate;
	}

	public String getReceiveTime() {
		return receiveTime;
	}

	public void setReceiveTime(String receiveTime) {
		this.receiveTime = receiveTime;
	}

	public String getReceiveState() {
		return receiveState;
	}

	public void setReceiveState(String receiveState) {
		this.receiveState = receiveState;
	}

	public String getSendState() {
		return sendState;
	}

	public void setSendState(String sendState) {
		this.sendState = sendState;
	}

	public String getReceiveError() {
		return receiveError;
	}

	public void setReceiveError(String receiveError) {
		this.receiveError = receiveError;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	@Override
	public String getBizId() {
		// TODO Auto-generated method stub
		return null;
	}

}
