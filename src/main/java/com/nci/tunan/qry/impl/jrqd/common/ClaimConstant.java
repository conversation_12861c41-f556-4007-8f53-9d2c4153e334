package com.nci.tunan.qry.impl.jrqd.common;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.nci.tunan.qry.interfaces.model.jrqd.po.clm.ClaimPayeePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.clm.ClaimRiskLevelLiabPO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.generatekey.api.GenerateBizKeyUtil;
import com.nci.udmp.util.lang.DateUtilsEx;

/**
 * @description 参数管理(工具) 复制自com.nci.tunan.clm.impl.util.ClaimConstant
 * @<NAME_EMAIL>
 * @date Apr 11, 2022 5:24:17 PM
 * @.belongToModule
 */
public final class ClaimConstant extends Constants {
	/**
	 * 私有构造函数，不允许new
	 */
	private ClaimConstant() {
	}

	/**
	 * 赔案号
	 */
	public static final String CASENO = "";

	/**
	 * 个人池标识
	 */
	public static final String PERSONALQUERYTASKTYPE = "2";
	/**
	 * 共享池标识
	 */
	public static final String SHAREQUERYTASKTYPE = "1";
	/**
	 * 个人池标识
	 */
	public static final String IS_GROUP_F = "F";
	/**
	 * 共享池标识
	 */
	public static final String IS_GROUP_T = "T";
	/**
	 * 报案节点编码
	 */
	public static final String REPORTSHARETASKCODE = "CLM010201";
	/**
	 * 协谈开始节点编码
	 */
	public static final String TREATYSTATATASKCODE = "CLM070000";

	/**
	 * 分配协谈任务 - 节点编码
	 */
	public static final String TREATYTASKCODE = "CLM070201";

	/**
	 * 理赔协谈节点编码
	 */
	public static final String CLMTREATYTASKCODE = "CLM070202";

	/**
	 * 出具机构协谈结论 - 节点编码
	 */
	public static final String CLMORGTREATYTASKCODE = "CLM070203";
	/**
	 * 保全组号
	 */
	public static final String LOCK_EXCEPT_GROUP1 = "[06701]";
	/**
	 * 数字38
	 */
	public static final int THRITY_EIGHT = 38;
	/**
	 * 续期组号
	 */
	public static final String LOCK_EXCEPT_GROUP2 = "[06702]";
	/**
	 * 影像操作类型常量
	 */
	public static final String IMAGE_OPERATE_TYPE_CREATE = "21";
	/**
	 * 查询
	 */
	public static final String IMAGE_OPERATE_TYPE_RETRIVE = "22";
	/**
	 * 更新
	 */
	public static final String IMAGE_OPERATE_TYPE_UPDATE = "23";
	/**
	 * 删除
	 */
	public static final String IMAGE_OPERATE_TYPE_DELETE = "24";

	/**
	 * 事中质检节点编码
	 */
	public static final String MIDDLECHECKTASKCODE = "CLM010215";

	/**
	 * 报案状态
	 */
	public static final String REPORTSTATUSE = "10";

	/**
	 * 签收节点编码
	 */
	public static final String SIGNSHARETASKCODE = "CLM010202";

	/**
	 * 待签收状态
	 */
	public static final String SIGNSTATUSE = "20";

	/**
	 * 立案节点编码
	 */
	public static final String REGISTERSHARETASKCODE = "CLM010203";
	/**
	 * 自动立案节点编码（外包录入）
	 */
	public static final String AUTOREGISTERSHARETASKCODE = "CLM010220";
	/**
	 * 直赔问题案件池
	 */
	public static final String DIRECT_PROBLEM_CASE_TASKCODE = "CLM010209";

	/**
	 * 待立案状态
	 */
	public static final String REGISTERSTATUSE = "30";

	/**
	 * 审核节点编码
	 */
	public static final String AUDITSHARETASKCODE = "CLM010207";
	/**
	 * 二核节点编码
	 */
	public static final String BINUCLEARTASKCODE = "UW020201";

	/**
	 * 简易审核节点编码
	 */
	public static final String EASYAUDITSHARETASKCODE = "CLM010206";

	/**
	 * 审核状态
	 */
	public static final String AUDITSTATUSE = "60";
	/**
	 * 案件状态审核中
	 */
	public static final String AUDITSTATUSEZ = "61";
	/**
	 * 案件状态审批
	 */
	public static final String APPROVESTATUSE = "70";
	/**
	 * 案件状态审批中
	 */
	public static final String APPROVESTATUSEZ = "71";

	/**
	 * 预付中
	 */
	public static final String ADVANCE_MID_STATUS = "51";

	/**
	 * 待预付录入
	 */
	public static final String ADVANCE_ENTER_STATUS = "52";

	/**
	 * 待预付复核
	 */
	public static final String ADVANCE_CHECK_STATUS = "53";

	/**
	 * 预付复核中
	 */
	public static final String ADVANCE_CHECKING_STATUS = "54";
	/**
	 * 简易自核
	 */
	public static final String SIMPLE_SELF_CHECK_STATUS = "33";
	/**
	 * 审批自核
	 */
	public static final String APPROVE_SELF_CHECK_STATUS = "62";
	/**
	 * 回退中
	 */
	public static final String REDO_CHECKING_ING_STATUS = "91";
	/**
	 * 已回退
	 */
	public static final String REDO_CHECKING_STATUS = "99";

	/**
	 * 模板标识
	 */
	public static final String PROCESSFLAG = "CLMMain";
	/**
	 * 二核模板标识
	 */
	public static final String UW_PROCESSFLAG = "UWMC";

	/**
	 * 协谈模板标识
	 */
	public static final String TREATYFLAG = "CLMC";

	/**
	 * 合议模板标识
	 */
	public static final String DISCUSSFLAG = "CLMPD";

	/**
	 * 回退模板标识
	 */
	public static final String ROLLBACKFLAG = "CLMRM";

	/**
	 * 付费变更模板标识
	 */
	public static final String PAYCHANGEFLAG = "CLMPC";

	/**
	 * 补充问题件模板标识
	 */
	public static final String SUPPLYCHECKLISTFLAG = "CLMRFP";

	/**
	 * 用与批量操作的模板标识
	 */
	public static final String BATCHFLAG = "CLMAF";
	/**
	 * 实物单证收齐模板标识
	 */
	public static final String PHYSICALDOCUMENTSFLAG = "CLMPDoc";

	/**
	 * 实物单证收齐节点编码
	 */
	public static final String PHYSICALDOCUMENTSTASKCODE = "CLM201123";

	/**
	 * 审批结案处理节点编码
	 */
	public static final String APPROVETASKCODE = "CLM010409";

	/**
	 * 预付申请节点编码
	 */
	public static final String PREPAREPAYAPPLYTASKCODE = "CLM010204";

	/**
	 * 预付复核节点编码
	 */
	public static final String PREPAREPAYCHECKTASKCODE = "CLM010205";

	/**
	 * 预付审批生效节点编码
	 */
	public static final String PREPAREPAYCHECKEFFICETASKCODE = "CLM010406";

	/**
	 * 回退申请节点编码
	 */
	public static final String ROLLBACKAPPLYTASKCODE = "CLM040201";

	/**
	 * 回退复核节点编码
	 */
	public static final String ROLLBACKCHECKTASKCODE = "CLM040202";

	/**
	 * 付费变更申请节点编码
	 */
	public static final String PAYCHANGEAPPLYTASKCODE = "CLM060201";

	/**
	 * 付费变更复核节点编码
	 */
	public static final String PAYCHANGECHECKTASKCODE = "CLM060202";

	/**
	 * 分配合议任务 - 节点编码
	 */
	public static final String ALLOTDISCUSSIONREPLYTASKCODE = "CLM030201";

	/**
	 * 合议回复节点编码
	 */
	public static final String DISCUSSIONREPLYTASKCODE = "CLM030202";

	/**
	 * 出具合议结论 - 节点编码
	 */
	public static final String ORGDISCUSSIONREPLYTASKCODE = "CLM030203";

	/**
	 * 补充问题件 - 节点编码
	 */
	public static final String SUPPLYCLAIMCHECKLISTTASKCODE = "CLM100201";

	/**
	 * 审批处理节点编码
	 */
	public static final String APPROVEDISPOSETASKCODE = "CLM010208";

	/**
	 * 复核案件节点编码
	 */
	public static final String AGAINCHECKTASKCODE = "CLM010212";

	/**
	 * 案件抽检(事中质检)处理节点编码
	 */
	public static final String MIDCTASKCODE = "CLM010215";

	/**
	 * 案件质检(事后质检)处理节点编码
	 */
	public static final String AFCTASKCODE = "CLM050201";

	/**
	 * 回传数据处理(直连问题件)节点编码
	 */
	public static final String RDDTASKCODE = "CLM010300";

	/**
	 * 数据调整(直连问题件)节点编码
	 */
	public static final String DATASKCODE = "CLM010301";
	/**
	 * 结案状态
	 */
	public static final String CLOSECASESTATUSE = "80";
	/**
	 * 待审批状态
	 */
	public static final String WAITCASESTATUSE = "70";
	/**
	 * 待审核状态
	 */
	public static final String CASE_STATUS_AUDIT = "60";

	/**
	 * 业务类型
	 */
	public static final String BUSINESSTYPE = "CLM";
	/**
	 * 任务创建类型 0: 创建流程不提交,1：创建流程且自动提交到下一节点
	 */
	public static final String CREATETASKCREATETYPE = "0";
	/**
	 * 任务创建类型 1：创建流程且自动提交到下一节点
	 */
	public static final String SUBMITTASKCREATETYPE = "1";
	/**
	 * 理赔创建类型
	 */
	public static final String CLAIMCREATETYPE = "0";
	/**
	 * 立案共享池非空验证提示
	 */
	public static final String SHAREPOLLNOTNULL = "请至少输入一项查询条件";

	/**
	 * 立案共享池操作失败提示
	 */
	public static final String SHAREPOLLFAIL = "查询失败";

	/**
	 * 查询出险人操作失败提示
	 */
	public static final String QUERYINSUREDFAIL = "查询出险人失败";

	/**
	 * 查询出险人操作失败提示
	 */
	public static final String QUERYINSUREDNOTEXIST = "您要查询的客户不存在，请重新输入查询条件";

	/**
	 * 保单挂起抄单操作成功提示
	 */
	public static final String CONTRACTHANGUPSUCCEED = "抄单成功";

	/**
	 * 保单挂起抄单操作失败提示
	 */
	public static final String CONTRACTHANGUPFAIL = "抄单失败";

	/**
	 * 保单挂起抄单操作失败提示
	 */
	public static final String CONTRACTHANGUPDATE = "该出险日期没有对应的保单,或抄单保存失败";

	/**
	 * 定义复核机制删除复核规则操作成功提示
	 */
	public static final String DATACHECKDELETESUCCEED = "复核规则删除成功";

	/**
	 * 匹配理算成功提示
	 */
	public static final String MATCHCALCSUCCEED = "匹配理算成功";

	/**
	 * 匹配理算失败提示
	 */
	public static final String MATCHCALCFAIL = "匹配理算失败";

	/**
	 * 结论保存成功提示
	 */
	public static final String CONCLUSIONSUCCEED = "结论保存成功";

	/**
	 * 结论保存失败提示
	 */
	public static final String CONCLUSIONFAIL = "结论保存失败";

	/**
	 * 险种设计类型-投连险
	 */
	public static final String PRODUCT_CATEGORY1_TL = "20004";
	/**
	 * 投连险卖出标识 0-按照申请日的上一个计价日计算
	 */
	public static final String SELL_FALG_0 = "0";
	/**
	 * 投连险卖出标识 1-按照申请日的下一个计价日计算
	 */
	public static final String SELL_FALG_1 = "1";
	/**
	 * 卖出类型 0-试算
	 */
	public static final String SELL_TYPE_CALCULATION = "0";
	/**
	 * 卖出类型 1-卖出
	 */
	public static final String SELL_TYPE_SOLD = "1";
	/**
	 * 卖出类型 3-撤销卖出
	 */
	public static final String SELL_TYPE_UNSOLD = "3";
	/**
	 * 匹配理算类型 0-匹配预算
	 */
	public static final String MATCH_CALC_PRE = "0";
	/**
	 * 匹配理算类型 1-匹配理算
	 */
	public static final String MATCH_CALC_CALCULATION = "1";
	/**
	 * 累加器类型 1-扣款
	 */
	public static final BigDecimal ACCUMU_TYPE_DEDUCT = new BigDecimal(1);
	/**
	 * 累加器类型 2-共担
	 */
	public static final BigDecimal ACCUMU_TYPE_COPAY = new BigDecimal(2);
	/**
	 * 累加器类型 3-责任限额
	 */
	public static final BigDecimal ACCUMU_TYPE_DSL = new BigDecimal(3);
	/**
	 * 累加器类型 4-双重账单住院天数
	 */
	public static final BigDecimal ACCUMU_TYPE_DOUBLE_HOSPITALIZATION = new BigDecimal(4);
	/**
	 * 累加器类型 5-乘法累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_MULTIPLIER = new BigDecimal(5);
	/**
	 * 累加器类型 6-最小值累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_MINVALUE = new BigDecimal(6);
	/**
	 * 累加器类型 7-账单住院天数
	 */
	public static final BigDecimal ACCUMU_TYPE_HOSPITALIZATION = new BigDecimal(7);
	/**
	 * 累加器类型 8-双重乘法累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_DOUBLE_MULTIPLIER = new BigDecimal(8);
	/**
	 * 累加器类型 9-双重最小值累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_DOUBLE_MINVALUE = new BigDecimal(9);
	/**
	 * 累加器类型 10-双重限额累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_DOUBLELIMIT = new BigDecimal(10);
	/**
	 * 累加器类型 10-汇总金额限制累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_SUMMARY_LIMIT = new BigDecimal(11);
	/**
	 * 累加器模式 0-无关
	 */
	public static final String CONDITION_TYPE_NA = "0";
	/**
	 * 累加器模式 1-单个事故
	 */
	public static final String CONDITION_TYPE_PER_EVENT = "1";
	/**
	 * 累加器模式 2-单个住院期间
	 */
	public static final String CONDITION_TYPE_PRE_HOSPITALIZATION = "2";
	/**
	 * 累加器模式 3-单次诊断
	 */
	public static final String CONDITION_TYPE_PRE_DIAGNOSIS = "3";
	/**
	 * 累加器模式 4-同一事故原因的事故
	 */
	public static final String CONDITION_TYPE_SAME_EVENT = "4";
	/**
	 * 累加器模式 5-单个保单年度
	 */
	public static final String CONDITION_TYPE_PER_POLICY_YEAR = "5";
	/**
	 * 抄单日期 当前标示 0 - 出险日期
	 */
	public static final BigDecimal FLAG_CLAIM_DATE = new BigDecimal(0);
	/**
	 * 抄单日期 当前标示 1 - 当前日期
	 */
	public static final BigDecimal FLAG_CURRENT_DATE = new BigDecimal(1);
	/**
	 * 相关性判断结果 0 - 否
	 */
	public static final String NO = "0";
	/**
	 * 相关性判断结果 1 - 是
	 */
	public static final String YES = "1";
	/**
	 * 治疗类型 0-门诊
	 */
	public static final String TREAT_TYPE_CLINIC = "0";
	/**
	 * 治疗类型 1-住院
	 */
	public static final String TREAT_TYPE_HOSPITALIZATION = "1";
	/**
	 * 相关性类型 1 - T_PRODUCT_RELATIVITY
	 */
	public static final BigDecimal RELATIVITY_TYPE_1 = new BigDecimal(1);
	/**
	 * 相关性类型2 - T_PARAM
	 */
	public static final BigDecimal RELATIVITY_TYPE_2 = new BigDecimal(2);
	/**
	 * 1- 社保
	 */
	public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_1 = new BigDecimal(1);
	/**
	 * 2- 第三方支付
	 */
	public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_2 = new BigDecimal(2);
	/**
	 * 3- 公费
	 */
	public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_3 = new BigDecimal(3);
	/**
	 * 调用打印时加入,读取配置问题件路径
	 */
	public static final String NB_UW_LOGINADMIN_PATH = "META-INF/conf/config.properties";
	/**
	 * 登陆用户名
	 */
	public static final String NB_UW_LOGIN_USERNAME = "login.userName";
	/**
	 * 登陆密码
	 */
	public static final String NB_UW_LOGIN_PASSWORD = "login.passWord";
	/**
	 * 体检结果录入访问URL
	 */
	public static final String NB_UW_LOGIN_PHYSICALURL = "login.physicalUrl";
	/**
	 * 核保结论通知书录入访问URL----2015-01-27
	 */
	public static final String NB_UW_LOGIN_UWDESITIONURL = "login.uwDesitionUrl";
	/**
	 * 应用上下文名称
	 */
	public static final String NB_UW_LOGIN_WEBCONTEXT = "login.webcontext";
	/**
	 * 体检结果录入访问的IP地址
	 */
	public static final String NB_UW_LOGIN_IP = "login.ip";
	/**
	 * 核保结论通知书录入访问的IP地址
	 */
	public static final String NB_UW_LOGIN_UWDESITIONIP = "login.uwDesitionIp";
	/**
	 * 端口号
	 */
	public static final String NB_UW_LOGIN_PORT = "login.port";
	/**
	 * 用户名
	 */
	public static final String NB_BMP_userCode = "syn";
	/**
	 * ProcessFlag
	 */
	public static final String NB_BMP_ProcessFlag = "NBEnter";
	/**
	 * 投保单标示
	 */
	public static final String NB_BMP_Question_ProcessFlag = "NBIM";
	/**
	 * 投保单录入-->TaskCode
	 */
	public static final String NB_BMP_TaskCode = "NB030202";
	/**
	 * 复核任务-->TaskCode
	 */
	public static final String NB_BMP_TaskCode2 = "NB030203";
	/**
	 * 核保标示NB120201
	 */
	public static final String NB_BMP_Question_TaskCode = "NB120201";
	/**
	 * 核保标示NB120202
	 */
	public static final String NB_BMP_Question_Review_TaskCode = "NB120202";
	/**
	 * QueryTaskType,共享池
	 */
	public static final String NB_BMP_QueryTaskType_Pubpool = "1";
	/**
	 * 个人池
	 */
	public static final String NB_BMP_QueryTaskType_Perpool = "2";
	/**
	 * 通知书下载临时路径
	 */
	public static final String NB_NOTICE_URL = "notice.url";
	/**
	 * 初审不通过通知书类型
	 */
	public static final String NB_AUDIT_NOT_TYPE = "audit.notice.type";
	/**
	 * 初审回执通知书类型
	 */
	public static final String NB_AUDIT_RECEIPT_TYPE = "audit.receipt.type";
	/**
	 * 业务来源编码
	 */
	public static final String BUSS_SOURCE_CODE = "buss.source.code";
	/**
	 * 发送对象类型
	 */
	public static final String SEND_OBJECT_TYPE = "send.obj.type";
	/**
	 * 通知书状态
	 */
	public static final String NOTICE_STATUS = "1";
	/**
	 * 记事本访问路径
	 */
	public static final String NB_UW_LOGIN_NOTEBOOK = "login.uwNoteUrl";
	/**
	 * 记事本访问ip
	 */
	public static final String NB_UW_LOGIN_UWNOTE = "login.uwnoteIp";
	/**
	 * 退回重扫模板标识-->ProcessFlag
	 */
	public static final String NB_BMP_ProcessFlag_Reset = "NBIFS";
	/**
	 * 退回重扫-->TaskCode
	 */
	public static final String NB_BMP_TaskCode_Reset = "NB040201";
	/**
	 * 通知书打印ip地址
	 */
	public static final String NB_DOCUMENT_IP = "documentIp";
	/**
	 * 通知书单证类型
	 */
	public static final String LTR_SENDTYPE = "ltr.sendType";
	/**
	 * 通知书单证类型
	 */
	public static final String PIP_SENDTYPE = "pip.sendType";
	/**
	 * 打印类型
	 */
	public static final String PRINT_PRINTTYPE = "print.printType";
	/**
	 * 通知书单证类型
	 */
	public static final String PREVIEW_PRINTTYPE = "preview.printType";
	/**
	 * 通知书单证类型
	 */
	public static final String PREVIEWANDPRINT_PRINTTYPE = "previewAndPrint.printType";
	/**
	 * 规则引擎ip地址
	 */
	public static final String NB_ILOG_IP = "ilogIp";
	/**
	 * 签单模板标识-->ProcessFlag
	 */
	public static final String NB_BMP_SIGN_PROCESSFLAG = "NBSignin";
	/**
	 * 待签单处理节点编码-->TaskCode
	 */
	public static final String NB_BMP_SIGN_WAITTING_TASKCODE = "NB050202";
	/**
	 * 事后质检模板标识-->ProcessFlag
	 */
	public static final String NB_BMP_CHECK_PROCESSFLAG = "NBCheck";
	/**
	 * 进行质检操作节点编码-->TaskCode
	 */
	public static final String NB_BMP_CHECK_ING_TASKCODE = "NB100201";
	/**
	 * 纠错模板标识-->ProcessFlag
	 */
	public static final String NB_BMP_CORRECT_PROCESSFLAG = "NBCorrect";
	/**
	 * 纠错申请节点编码-->TaskCode
	 */
	public static final String NB_BMP_CORRECT_APPLY_TASKCODE = "NB090201";
	/**
	 * 纠错审核节点编码-->TaskCode
	 */
	public static final String NB_BMP_CORRECT_VERIFY_TASKCODE = "NB090202";
	/**
	 * 保单重打模板标识-->ProcessF审核lag
	 */
	public static final String NB_BMP_REPRINT_PROCESSFLAG = "NBPR";
	/**
	 * 重打审核节点编码-->TaskCode
	 */
	public static final String NB_BMP_REPRINT_VERIFY_TASKCODE = "NB070203";
	/**
	 * 通知书管理模板标识-->ProcessFlag
	 */
	public static final String NB_BMP_DOCUMENT_PROCESSFLAG = "NM";
	/**
	 * 通知书打印节点编码-->TaskCode
	 */
	public static final String NB_BMP_DOCUMENT_PRINT_TASKCODE = "NM010201";
	/**
	 * 通知书录入节点编码-->TaskCode
	 */
	public static final String NB_BMP_DOCUMENT_ENTRY_TASKCODE = "NM010203";
	/**
	 * 保单更新回执信息成功的标识
	 */
	public static final String NB_PAS_RECEIPT_SUCCESS = "0";
	/**
	 * 任务提醒的方式-->清单类
	 */
	public static final String NB_TASK_DL = "1";
	/**
	 * 任务提醒的方式-->任务池类
	 */
	public static final String NB_TASK_POOL = "2";
	/**
	 * 理赔类型---医疗
	 */
	public static final String ClAIMTYPE_MEDICAL = "03";
	/**
	 * 理赔类型---医疗
	 */
	public static final String ClAIMTYPE_ILLNESS = "08";
	/**
	 * 案件状态预付未完成状态
	 */
	public static final String ClAIMSTATUS = "50";
	/**
	 * 调用工作流成功标识
	 */
	public static final String BIZRESCD = "0";
	/**
	 * 调用工作流失败标识
	 */
	public static final String BIZRESCDNO = "1";
	/**
	 * 出险人性别常量
	 */
	public static final String SEX = "9L";
	/**
	 * 案件状态为签收中
	 */
	public static final String CASESTATUS = "21";
	/**
	 * 案件状态为立案中
	 */
	public static final String REGISTERCASESTATUS = "31";
	/**
	 * 案件状态为待立案确认
	 */
	public static final String PENDING_REGISTER_CASES_TATUS = "32";
	/**
	 * 案件状态为关闭
	 */
	public static final String CANCELSTATUS = "90";
	/**
	 * 理赔关怀中受益人的年龄
	 */
	public static final String BENEAGE = "18";
	/**
	 * 应收应付理赔业务来源
	 */
	public static final String DERIVTYPE = "5";
	/**
	 * 数字0
	 */
	public static final int ZERO = 0;

	/**
	 * 数字1
	 */
	public static final int ONE = 1;

	/**
	 * 数字2
	 */
	public static final int TWO = 2;

	/**
	 * 数字3
	 */
	public static final int THREE = 3;

	/**
	 * 数字4
	 */
	public static final int FORE = 4;

	/**
	 * 数字4
	 */
	public static final int FOUR = 4;
	/**
	 * 数字-4
	 */
	public static final int FOUR_M = -4;
	/**
	 * 数字5
	 */
	public static final int FIVE = 5;
	/**
	 * 数字-5
	 */
	public static final int FIVE_M = -5;

	/**
	 * 数字6
	 */
	public static final int SIX = 6;
	/**
	 * 数字7
	 */
	public static final int SEVEN = 7;
	/**
	 * 数字8
	 */
	public static final int EIGHT = 8;
	/**
	 * 数字9
	 */
	public static final int NINE = 9;
	/**
	 * 数字-9
	 */
	public static final int NINE_M = -9;
	/**
	 * 数字-6
	 */
	public static final int SIX_M = -6;
	/**
	 * 数字10
	 */
	public static final int TEN = 10;
	/**
	 * 数字11
	 */
	public static final int ELENEN = 11;
	/**
	 * 数字12
	 */
	public static final int TWELVE = 12;
	/**
	 * 数字13
	 */
	public static final int THIRTEEN = 13;
	/**
	 * 数字14
	 */
	public static final int FOURTEEN = 14;
	/**
	 * 数字15
	 */
	public static final int FIFTEEN = 15;
	/**
	 * 数字16
	 */
	public static final int SIXTEEN = 16;
	/**
	 * 数字17
	 */
	public static final int SEVENTEEN = 17;
	/**
	 * 数字18
	 */
	public static final int EIGHTEEN = 18;
	/**
	 * 数字19
	 */
	public static final int NINETEEN = 19;
	/**
	 * 数字20
	 */
	public static final int TWENTY = 20;
	/**
	 * 数字21
	 */
	public static final int TWENTY_ONE = 21;
	/**
	 * 数字22
	 */
	public static final int TWENTY_TWO = 22;

	/**
	 * 数字23
	 */
	public static final int TWENTY_THREE = 23;
	/**
	 * 数字24
	 */
	public static final int TWENTY_FOUR = 24;
	/**
	 * 数字29
	 */
	public static final int TWENTY_NINE = 29;
	/**
	 * 数字30
	 */
	public static final int THIRTY = 30;
	/**
	 * 数字36
	 */
	public static final int THIRTY_SIX = 36;
	/**
	 * 数字40
	 */
	public static final int FOURTY = 40;
	/**
	 * 数字50
	 */
	public static final int FIFTY = 50;
	/**
	 * 数字99
	 */
	public static final int NINETY_NINE = 99;
	/**
	 * 数字100
	 */
	public static final int ONE_HUNDRED = 100;
	/**
	 * 数字-100
	 */
	public static final int ONE_HUNDRED_M = -100;
	/**
	 * 数字348
	 */
	public static final int THREE_HUNDRED_FORTY_EIGHT = 348;
	/**
	 * 数字500
	 */
	public static final int FIVE_HUNDRED = 500;
	/**
	 * 数字999
	 */
	public static final int NINE_HUNDRED_NINETY_NINE = 999;
	/**
	 * 数字1000
	 */
	public static final int ONE_THOUSAND = 1000;
	/**
	 * 数字1024
	 */
	public static final int ONE_THOUSAND_TWENTY_FOUR = 1024;
	/**
	 * 数字1864
	 */
	public static final int ONE_THOUSAND_SIXTY_FOUR = 1864;
	/**
	 * 数字1900
	 */
	public static final int ONE_THOUSAND_NINE_HUNDRED = 1900;
	/**
	 * 数字2050
	 */
	public static final int TWO_THOUSAND_FIFTY = 2050;
	/**
	 * 数字5000
	 */
	public static final int FIVE_THOUSAND = 5000;
	/**
	 * 数字9999
	 */
	public static final int NINE_THOUSAND_NINE_HUNDRED_NINTY_NINE = 9999;

	/**
	 * 外包录入FTP端口号
	 */
	public static final int FTP_PORT = 21;

	/**
	 * 关系代码，"00"代表本人
	 */
	public static final String RELATION = "00";

	/**
	 * 关系代码，"24"代表子女
	 */
	public static final String RELATION_TWENTY_FOUR = "24";

	/**
	 * 北京市编码
	 */
	public static final String BEIJING = "110000";

	/**
	 * 东城区编码
	 */
	public static final String DONGCHENG = "110100";
	/**
	 * 结算类型代码，"8"代表退还保费
	 */
	public static final String ADJUST_TYPE = "8";

	/**
	 * 两个BigDecimal的数据比较大小，小于的返回值为-1
	 */
	public static final int COMPARE_RESULT = -1;

	/**
	 * 反馈质检结果模板
	 */
	public static final String CHECKAFTERFLAG = "CLMCheck";
	/**
	 * 反馈质检结果节点号
	 */
	public static final String CHECKAFTERCODE = "CLM050201";
	/**
	 * 出具审核结论 常量 add xuyz_wb
	 */
	/**
	 * @Fields Medical_YES : 是否医疗赔付明细：Y
	 */
	public static final String Medical_YES = "Y";
	/**
	 * @Fields Medical_NO : 是否医疗赔付明细：N
	 */
	public static final String Medical_NO = "N";
	/**
	 * @Fields NUM_THREE : 数字：3
	 */
	public static final int NUM_THREE = 3;
	/**
	 * @Fields NUM_FOUR : 数字：4
	 */
	public static final int NUM_FOUR = 4;
	/**
	 * @Fields EXCEPT_GROUP_1 : 理赔可维护挂起保全项设置的组id : 1
	 */
	public static final String EXCEPT_GROUP_1 = "1";
	/**
	 * @Fields NUM_FIVE : 数字：5
	 */
	public static final int NUM_FIVE = 5;
	/**
	 * @Fields NUM_SIX : 数字：6
	 */
	public static final int NUM_SIX = 6;
	/**
	 * @Fields NUM_EIGHT : 数字：8
	 */
	public static final int NUM_EIGHT = 8;
	/**
	 * @Fields NUM_NINE : 数字：9
	 */
	public static final int NUM_NINE = 9;
	/**
	 * @Fields NUM_TEN : 数字：10
	 */
	public static final int NUM_TEN = 10;
	/**
	 * @Fields NUM_SEVENTEEN : 数字：17
	 */
	public static final int NUM_SEVENTEEN = 17;

	/**
	 * @Fields NUM_SEVENTEEN : 数字：100
	 */
	public static final int NUM_HUNDRED = 100;

	/**
	 * @Fields NUM_SEVENTEEN : 数字：99999
	 */
	public static final int NINE_MAX = 99999;
	/**
	 * @Fields NUM_SEVENTEEN : 数字：10000
	 */
	public static final int TEN_THOUSAND = 10000;

	/**
	 * @Fields COMMA_CHINA : 中文逗号："，"
	 */
	public static final String COMMA_CHINA = "，";
	/**
	 * @Fields COMMA : 逗号：","
	 */
	public static final String COMMA = ",";
	/**
	 * @Fields SEMICOLON : 分号：";"
	 */
	public static final String SEMICOLON = ";";
	/**
	 * @Fields SEMICOLON_CHAR : 分号：";"
	 */
	public static final char SEMICOLON_CHAR = ';';
	/**
	 * @Fields SEMICOLON_CHAR : 句号："。"
	 */
	public static final char SEMICOLON_CHARJ = '。';

	/**
	 * 责任层赔付结论和赔案层赔付结论比较
	 */
	public static final String LIABCONCLUSION_FLAG_ZERO = "0";
	/**
	 * 责任层赔付结论和赔案层赔付结论比较
	 */
	public static final String LIABCONCLUSION_FLAG_ONE = "1";
	/**
	 * 责任层赔付结论和赔案层赔付结论比较
	 */
	public static final String LIABCONCLUSION_FLAG_TWO = "2";
	/**
	 * 责任层赔付结论和赔案层赔付结论比较
	 */
	public static final String LIABCONCLUSION_FLAG_THREE = "3";
	/**
	 * 责任层赔付结论和赔案层赔付结论比较
	 */
	public static final String LIABCONCLUSION_FLAG_FIVE = "5";
	/**
	 * 责任层赔付结论和赔案层赔付结论比较
	 */
	public static final String CONDITION_TYPE_NAME = "审批权限";
	/**
	 * 退保终止03、解约终止05 理赔终止02 满期终止01
	 */
	public static final String END_CAUSE_ONE = "01";
	/**
	 * 退保终止03、解约终止05 理赔终止02 满期终止01
	 */
	public static final String END_CAUSE_THREE = "03";
	/**
	 * 退保终止03、解约终止05 理赔终止02 满期终止01
	 */
	public static final String END_CAUSE_FIVE = "05";
	/**
	 * 退保终止03、解约终止05 理赔终止02 满期终止01
	 */
	public static final String END_CAUSE_TWO = "02";
	/**
	 * 打印类型
	 */
	public static final String PRINT_TYPE_ = "Print";
	/**
	 * 打印类型
	 */
	public static final String PREVIEW_TYPE = "Preview";
	/**
	 * 打印类型
	 */
	public static final String PREVIEWANDPRINT_TYPE = "PreviewAndPrint";
	/**
	 * @Fields MODUBLE_TYPE : 模块类型："05"
	 */
	public static final String MODUBLE_TYPE = "05";
	/**
	 * 支付状态失败
	 */
	public static final String PAYMENT_STATUS_FAIL = "00";
	/**
	 * 支付状态成功
	 */
	public static final String PAYMENT_STATUS_SUCCEED = "01";
	/**
	 * 分次给付模板标识
	 */
	public static final String PROCESSFLA = "CLMGO";
	/**
	 * 分次给付任务创建类型
	 */
	public static final String TASKCREATETYPE = "1";

	/**
	 * 单证打印查询无记录提示信息
	 */
	public static final String DOCUMENT_PRINT_CLAIM = "您要查询的赔案不存在，请重新输入查询条件!";

	/**
	 * 1秒 = 1000毫秒数
	 */
	public static final int MILLISECONDS = 1000;

	/**
	 * 1分钟 = 60秒
	 */
	public static final int MINUTE = 60;

	/**
	 * 1小时 = 60分钟
	 */
	public static final int HOUR = 60;

	/**
	 * 任务类型，审核类型
	 */
	public static final String AUDIT_TASK_TYPE = "3";
	/**
	 * 任务类型，总时效
	 */
	public static final String SUM_TIME_TASK_TYPE = "17";
	/**
	 * 任务类型，审批类型
	 */
	public static final String APPROVED_TASK_TYPE = "4";

	/**
	 * 任务类型，事中质检类型
	 */
	public static final String MIDC_TASK_TYPE = "8";

	/**
	 * 任务类型，事后质检类型
	 */
	public static final String AFC_TASK_TYPE = "9";
	/**
	 * 事后质检完成标识
	 */
	public static final String AFC_CON_PASS = "2";

	/**
	 * 分页标识,不进行分页
	 */
	public static final String ISPAGEFLAG_NO = "0";

	/**
	 * 分页标识，进行分页
	 */
	public static final String ISPAGEFLAG_YES = "1";

	/**
	 * 空字符串用于判断
	 */
	public static final String NULL_STR = "";

	/**
	 * 4：查询全部个人池待办
	 */
	public static final String QUERY_ALL_PERSONAL_TASK_TYPE = "4";

	/**
	 * 任务查询：3-全部查询
	 */
	public static final String QUERY_ALL_TASK_TYPE = "3";

	/**
	 * -1用于进行判断
	 */
	public static final int ADJUST = -1;

	/**
	 * 用于分割字符串
	 */
	public static final String SPLIT_STR = ":";

	/**
	 * 机构级别第一级
	 */
	public static final String ORGAN_GRADE_ONE = "01";
	/**
	 * 机构级别第二级
	 */
	public static final String ORGAN_GRADE_TWO = "02";
	/**
	 * 机构级别第三级
	 */
	public static final String ORGAN_GRADE_THREE = "03";
	/**
	 * 机构级别第四级
	 */
	public static final String ORGAN_GRADE_FORE = "04";
	/**
	 * 二级机构长度
	 */
	public static final int ORGAN_TWO_LENTGH = 4;
	/**
	 * 三级机构长度
	 */
	public static final int ORGAN_THREE_LENTGH = 6;

	/**
	 * 四级机构长度
	 */
	public static final int ORGAN_FOUR_LENTGH = 8;

	/**
	 * 单证打印查询赔案失败
	 */
	public static final String DOCUMENT_PRINT_ERROR = "查询赔案失败!";
	/**
	 * 单证打印查询赔案失败
	 */
	public static final String POLICY_CLAUSE_BYCASEID = "该赔案无关联保单!";
	/**
	 * ip地址
	 */
	public static final String CLAIM_PRINT_IP = "**********";
	/**
	 * 案件标识正常
	 */
	public static final String CASE_FLAG_YES = "0";
	/**
	 * 案件标识非正常
	 */
	public static final String CASE_FLAG_NO = "1";
	/**
	 * 提示信息
	 */
	public static final String CLAIM_REPORT_QUERY_ERROR = "查询报案信息失败!";
	/**
	 * 提示信息
	 */
	public static final String CLAIM_REGIS_QUERY_ERROR = "查询立案信息失败!";
	/**
	 * 提示信息
	 */
	public static final String CLAIM_SIGN_QUERY_ERROR = "查询签批信息失败!";

	/**
	 * 事中质检参数类型----理赔类型
	 */
	public static final String CHECK_PARA_CLAIM_TYPE = "01";
	/**
	 * 事中质检参数类型----险种
	 */
	public static final String CHECK_PARA_PRODUCT = "02";
	/**
	 * 事中质检参数类型----操作机构
	 */
	public static final String CHECK_PARA_ORGANIZATION = "03";
	/**
	 * 事中质检参数类型----片区
	 */
	public static final String CHECK_PARA_AREA = "11";
	/**
	 * 事中质检参数类型----操作人员
	 */
	public static final String CHECK_PARA_OPERATOR = "04";
	/**
	 * 事中质检参数类型----案件标识
	 */
	public static final String CHECK_PARA_CASE_FLAG = "08";
	/**
	 * 签收确认创建工作流标识
	 */
	public static final String SIGN_CONFIRM_FLAG = "1";
	/**
	 * 工作流成功标识
	 */
	public static final String WORKFLOW_SUCCESS = "0";
	/**
	 * 工作流失败标识
	 */
	public static final String WORKFLOW_FAIL = "1";

	/**
	 * 领取频率 1:年领
	 */
	public static final String PAY_TYPE_ONE = "1";
	/**
	 * 领取频率 2:半年领
	 */
	public static final String PAY_TYPE_TWO = "2";
	/**
	 * 领取频率 3:季领
	 */
	public static final String PAY_TYPE_THREE = "3";
	/**
	 * 领取频率 4:月领
	 */
	public static final String PAY_TYPE_FORE = "4";
	/**
	 * 领取频率 5:趸领
	 */
	public static final String PAY_TYPE_FIVE = "5";

	/**
	 * 生调频率：月
	 */
	public static final String FREQ_MONTH = "01";
	/**
	 * 生调频率：季度
	 */
	public static final String FREQ_QUARTER = "02";
	/**
	 * 生调频率：半年
	 */
	public static final String FREQ_HARF_YEAR = "03";
	/**
	 * 生调频率：年
	 */
	public static final String FREQ_YEAR = "04";

	/**
	 * 权限名称 ：审核权限
	 */
	public static final String PERMISSIONNAME_AUDIT = "审核权限";
	/**
	 * 权限名称 ：审批权限
	 */
	public static final String PERMISSIONNAME_APPROVED = "审批权限(普通)";
	/**
	 * 权限名称 ：审批权限(疑难)
	 */
	public static final String PERMISSIONNAME_DIFFICULT_APPROVED = "审批权限(疑难)";
	/**
	 * 权限名称 ：事中质检权限
	 */
	public static final String PERMISSIONNAME_MIDC = "事中质检";
	/**
	 * 权限名称 ：事后质检权限
	 */
	public static final String PERMISSIONNAME_AFC = "事后质检";
	/**
	 * 权限名称 ：签收登记
	 */
	public static final String PERMISSIONNAME_SIGN = "签收登记";
	/**
	 * 权限名称 ：立案登记
	 */
	public static final String PERMISSIONNAME_REGISTER = "立案登记";
	/**
	 * 权限名称 ：立案登记复核
	 */
	public static final String PERMISSIONNAME_REGISTER_CHECK = "立案登记复核";
	/**
	 * 权限名称 ：预付申请
	 */
	public static final String PERMISSIONNAME_ADVANCE_APPLY = "预付申请";
	/**
	 * 权限名称 ：预付复核
	 */
	public static final String PERMISSIONNAME_ADVANCE_AUDIT = "预付复核";
	/**
	 * 权限名称 ：回退申请
	 */
	public static final String PERMISSIONNAME_ROLLBACK_APPLY = "回退申请";
	/**
	 * 权限名称 ：付费变更申请
	 */
	public static final String PERMISSIONNAME_PAYCHANGE_APPLY = "付费变更申请";
	/**
	 * 权限名称 ：付费变更复核
	 */
	public static final String PERMISSIONNAME_PAYCHANGE_AUDIT = "付费变更复核";
	/**
	 * 权限名称 ：补充单证问题件
	 */
	public static final String PERMISSIONNAME_CHECKLIST_PROBLEM = "补充单证问题件";
	/**
	 * 权限名称 ：协谈
	 */
	public static final String PERMISSIONNAME_TREATY_TALK = "协谈权限";
	/**
	 * 权限名称 ：合议
	 */
	public static final String PERMISSIONNAME_DISCUSS = "合议权限";
	/**
	 * 权限名称 ：调查
	 */
	public static final String PERMISSIONNAME_SURVEY = "调查";
	/**
	 * 权限名称 ：二核
	 */
	public static final String PERMISSIONNAME_UW = "二核 ";
	/**
	 * 权限名称 ：回退审核
	 */
	public static final String PERMISSIONNAME_ROLLBACK_AUDIT = "回退审核";
	/**
	 * 权限名称 ：回退审核
	 */
	public static final String PERMISSIONNAME_REGISTER_AUDIT = "立案登记复核";

	/**
	 * 业务来源 005：其他
	 */
	public static final String BIZ_OURCE = "005";

	/**
	 * 事中
	 */
	public static final String THINGS = "01";
	/**
	 * 提示信息
	 */
	public static final String CARE_SERVER = "您要查询的服务人员不存在，请重新输入查询条件！";

	/**
	 * 补充单证模板标识
	 */
	public static final String START_SUPPLY_CREATE_TEMPLATE = "CLMRFP";

	/**
	 * 补充单证节点编码
	 */
	public static final String START_SUPPLY_CREATE_NUMB = "CLM100201";
	/**
	 * 补充单证节点编码
	 */
	public static final String START_SUPPLY_CREATE_SUBMIT = "CLM109999";

	/**
	 * 理赔回退管理流程-审核节点编码
	 */
	public static final String ROLLBACK_AUDIT_TASK_CODE = "CLM040203";

	/**
	 * 分配标识 自动分配 （1 个人申请 2人工分配 3 自动分配）
	 */
	public static final String ASSIGNFLAG_AUTO = "3";

	/**
	 * 分配标识 手工分配 （1 个人申请 2人工分配 3 自动分配）
	 */
	public static final String ASSIGNFLAG_HAND = "2";

	/**
	 * 分配标识 个人申请 （1 个人申请 2人工分配 3 自动分配）
	 */
	public static final String ASSIGNFLAG_PERSONAL = "1";

	/**
	 * 发起二核调工作流模板标志
	 */
	public static final String START_UNDWER_TWICE_TEMPLATE = "CLMSU";

	/**
	 * 结论是否生效工作流节点
	 */
	public static final String START_UNDWER_TWICE_CODECON = "CLM110202";

	/**
	 * 加费工作流节点
	 */
	public static final String START_UNDWER_TWICE_CODEFEE = "CLM110203";

	/**
	 * 核销工作流节点
	 */
	public static final String START_UNDWER_TWICE_CODECANCLE = "CLM110204";

	/**
	 * 分次给付审核节点编码
	 */
	public static final String GRADATION_PAY_AUDIT = "CLM090201";

	/**
	 * 超期补偿标识，1为超期
	 */
	public static final BigDecimal EXCEEDDUEY = new BigDecimal(1);

	/**
	 * 超期补偿标识，0为未超期
	 */
	public static final BigDecimal EXCEEDDUEN = new BigDecimal(0);

	/**
	 * 审批结论，1为通过
	 */
	public static final BigDecimal APPROVECONCLUSIONY = new BigDecimal(1);

	/**
	 * 审批结论，2为未通过
	 */
	public static final BigDecimal APPROVECONCLUSIONN = new BigDecimal(2);

	/**
	 * 受检机构的CODE值
	 */
	public static final String ROLECODEONE = "1";
	/**
	 * 受检机构的CODE值
	 */
	public static final String ROLECODETWO = "2";
	/**
	 * 受检机构的CODE值
	 */
	public static final String ROLECODETHREE = "3";
	/**
	 * 受检机构的CODE值
	 */
	public static final String ROLECODEFOUR = "4";
	/**
	 * 受检机构的CODE值
	 */
	public static final String ROLECODEFIVE = "5";
	/**
	 * 受检机构的CODE值
	 */
	public static final String ROLECODESIX = "6";
	/**
	 * 受检机构的CODE值
	 */
	public static final String ROLECODESEVEN = "7";
	/**
	 * 事中质检参数类型----出险原因
	 */
	public static final String CHECK_PARA_REASON = "09";

	/**
	 * 参数定义表名称
	 */
	public static final String DEFINDEMNITYDUE = "indemnityDue";

	/**
	 * 参数定义表名称 撤销保单挂起批作业天数设定
	 */
	public static final String CUSTOMTIME = "customTime";
	/**
	 * 临时设定时间
	 */
	public static final String TEMPTIME = "tempTime";

	/**
	 * 发送方式 - 短信
	 */
	public static final BigDecimal SMSSENDWAY = new BigDecimal(1);

	/**
	 * 发送方式 - 邮件
	 */
	public static final BigDecimal MAILSENDWAY = new BigDecimal(2);

	/**
	 * 一年的天数
	 */
	public static final BigDecimal YEARDAYCOUNT = new BigDecimal(365);

	/**
	 * 审核结论 - 全部给付
	 */
	public static final BigDecimal CONCLUSIONALLPAY = new BigDecimal(1);

	/**
	 * 审核结论 - 部分给付
	 */
	public static final BigDecimal CONCLUSIONPARTPAY = new BigDecimal(2);

	/**
	 * 审核结论 - 3为拒付
	 */
	public static final BigDecimal CONCLUSIONREJECT = new BigDecimal(3);

	/**
	 * 审核结论 - 4公司撤案
	 */
	public static final BigDecimal CONCLUSIONCOMPANYREVOKE = new BigDecimal(4);

	/**
	 * 审核结论 - 5客户撤案
	 */
	public static final BigDecimal CONCLUSIONCUSTOMERREVOKE = new BigDecimal(5);

	/**
	 * 审核结论 - 6 审核不通过
	 */
	public static final BigDecimal CONCLUSIONNOTPASS = new BigDecimal(6);

	/**
	 * 是否为常规给付 - 1为否
	 */
	public static final BigDecimal ISCOMMONN = new BigDecimal(1);

	/**
	 * 结算类型 - 现金价值
	 */
	public static final String CASHVALUE = "1";

	/**
	 * 结算类型 - 账户价值
	 */
	public static final String ACCOUNTVALUE = "2";

	/**
	 * 结算类型 - 自垫本金
	 */
	public static final String PADPRINCIPAL = "3";

	/**
	 * 结算类型 - 自垫利息
	 */
	public static final String PADINTEREST = "4";

	/**
	 * 结算类型 - 贷款本金
	 */
	public static final String LOANPRINCIPAL = "5";

	/**
	 * 结算类型 - 贷款利息
	 */
	public static final String LOANINTEREST = "6";

	/**
	 * 结算类型 - 扣除保费
	 */
	public static final String DEDUCTPREMIUM = "7";

	/**
	 * 结算类型 - 返还保费
	 */
	public static final String RETURNPREMIUM = "8";

	/**
	 * 结算类型 - 终了红利
	 */
	public static final String FINALBONUS = "9";

	/**
	 * 结算类型 - 利差返还
	 */
	public static final String PRICERETURN = "10";

	/**
	 * 结算类型 - 风险保费
	 */
	public static final String RISKPREMIUM = "11";

	/**
	 * 结算类型 - 保单管理费
	 */
	public static final String CONTRACTMANAGECOST = "12";

	/**
	 * 结算类型 - 应领未领的保证领取年金
	 */
	public static final String NOTGETANNUITY = "13";

	/**
	 * 结算类型 - 出险日后发放的年金/生存金
	 */
	public static final String ACCIDENTBACKANNUITY = "14";

	/**
	 * 结算类型 - 未追回满期金
	 */
	public static final String NOTRECOVEREXPIRE = "15";
	/**
	 * 结算类型 - 险种终止退当期保费
	 */
	public static final String ADJUST_TYPE_SIXTEEN = "16";
	/**
	 * 结算类型 - 险种终止退未满期保费
	 */
	public static final String ADJUST_TYPE_SEVETEEN = "17";
	/**
	 * 结算类型 - 险种终止退全部保费
	 */
	public static final String ADJUST_TYPE_EIGHTEEN = "18";
	/**
	 * 结算类型 - 现金红利
	 */
	public static final String ADJUST_TYPE_NINETEEN = "19";
	/**
	 * 结算类型 - 现金红利利息
	 */
	public static final String ADJUST_TYPE_TWENTY = "20";
	/**
	 * 结算类型 - 扣除累计声息账户
	 */
	public static final String ADJUST_TYPE_TWENTYONE = "21";
	/**
	 * 结算类型 - 终了结算利息
	 */
	public static final String ADJUST_TYPE_TWENTYTWO = "22";
	/**
	 * 结算类型 - 基本保额现价
	 */
	public static final String ADJUST_TYPE_TWENTYFIVE = "25";
	/**
	 * 结算类型 - 累计红利保额现价
	 */
	public static final String ADJUST_TYPE_TWENTYSIX = "26";
	/**
	 * 结算类型 - 资产管理费
	 */
	public static final String ADJUST_TYPE_TWENTYTHREE = "23";
	/**
	 * 结算类型 - 扣除红利累积生息
	 */
	public static final String ADJUST_TYPE_TWENTYEIGHT = "28";
	/**
	 * 结算类型 - 扣除账户部分领取
	 */
	public static final String ADJUST_TYPE_TWENTYNINE = "29";

	/**
	 * 结算为偿还(付费)的数量
	 */
	public static final int ADJUST_PAY_NUM = 11;
	/**
	 * 关联到T_YES_NO表,0为否
	 */
	public static final BigDecimal BIGDECIMALNO = new BigDecimal(0);

	/**
	 * 关联到T_YES_NO表,1为是
	 */
	public static final BigDecimal BIGDECIMALYES = new BigDecimal(1);
	/**
	 * 关联到T_YES_NO表,1为是
	 */
	public static final String STRINGYES = "是";
	/**
	 * 关联到T_YES_NO表,0为否
	 */
	public static final String STRINGNO = "否";
	/**
	 * 用于截取字符串的数
	 */
	public static final int SIXINT = 6;
	/**
	 * 案件状态为待复核
	 */
	public static final String CASESTATUSISREVIEW = "40";
	/**
	 * 案件状态为复核中
	 */
	public static final String CASESTATUSISREVIEWZ = "41";
	/**
	 * 复核节点编码
	 */
	public static final String CASESTATUSISREVIEWJ = "CLM010212";

	/**
	 * 是否已回复
	 */
	public static final BigDecimal YETREPLY = new BigDecimal(1);
	/**
	 * 是否已回复
	 */
	public static final BigDecimal NOTREPLY = new BigDecimal(0);
	/**
	 * 生调标识
	 */
	public static final String ONESURVEYFLAG = "1";
	/**
	 * 调查类型标识 t_survey_type 预付类
	 */
	public static final String ONESURVEYTYPE = "2";
	/**
	 * 调查类型标识 t_survey_type 紧急类
	 */
	public static final String ONESURVEYTYPE_THREE = "3";
	/**
	 * 调查类型标识 biz_Type 常规类
	 */
	public static final BigDecimal ONESURVEYTYPE_ONE = BigDecimal.valueOf(1);
	/**
	 * 调查类型标识 复勘调查 biz_Type
	 */
	public static final BigDecimal ONESURVEYTYPE_RE = BigDecimal.valueOf(4);

	/**
	 * 调查类型标识 前置调查 biz_Type
	 */
	public static final BigDecimal ONESURVEYTYPE_PRO = BigDecimal.valueOf(5);
	/**
	 * 调查原因其他
	 */
	public static final String ONESURVEYREASON = "99";
	/**
	 * 调查原因先赔慰问
	 */
	public static final String TWEETYSURVEYREASON = "20";
	/**
	 * 调查提起阶段标识 t_survey_section 批处理
	 */
	public static final String ONESURVEYSECTION = "4";

	/**
	 * 调查提起阶段标识 t_survey_section 前置调查
	 */
	public static final BigDecimal ONESURVEYSECTION_PRO = BigDecimal.valueOf(5);

	/**
	 * 调查提起阶段标识 t_survey_section 复勘
	 */
	public static final BigDecimal ONESURVEYSECTION_SIX = BigDecimal.valueOf(6);

	/**
	 * 调查提起阶段标识 t_survey_section 报案
	 */
	public static final BigDecimal ONESURVEYSECTION_ONE = BigDecimal.valueOf(1);

	/**
	 * 调查提起阶段标识 移动理赔 01-报案
	 */
	public static final String ONESURVEYSECTION_ZONE = "01";
	/**
	 * 调查提起阶段标识 移动理赔 02-审核
	 */
	public static final String ONESURVEYSECTION_ZTWO = "02";
	/**
	 * 调查提起阶段标识 t_survey_section 审核
	 */
	public static final BigDecimal ONESURVEYSECTION_TWO = BigDecimal.valueOf(2);

	/**
	 * 调查提起阶段标识 t_survey_section 预付
	 */
	public static final BigDecimal ONESURVEYSECTION_THREE = BigDecimal.valueOf(3);
	/**
	 * 调查提起阶段标识 t_survey_section 预付
	 */
	public static final String SURVEYITEM_FOUR = "C1,C2,C3,C4";

	/**
	 * 回退状态 - 回退申请中
	 */
	public static final BigDecimal BACKSTATUSAPPLY = new BigDecimal(0);
	/**
	 * 回退状态 - 回退审核中
	 */
	public static final BigDecimal BACKSTATUSAUDIT = new BigDecimal(1);
	/**
	 * 回退状态 - 回退完成
	 */
	public static final BigDecimal BACKSTATUSFINISH = new BigDecimal(2);
	/**
	 * 回退状态 - 回退撤销
	 */
	public static final BigDecimal BACKSTATUSREVOKE = new BigDecimal(3);
	/**
	 * 业务锁名称 AC
	 */
	public static final String LOCKAC = "AC";
	/**
	 * 账单类型 - 门诊
	 */
	public static final BigDecimal BILL_TYPE_OUTPATIENT = new BigDecimal(1);
	/**
	 * 账单类型 - 门诊
	 */
	public static final BigDecimal BILL_TYPE_HIGH = new BigDecimal(2);
	/**
	 * 账单类型 - 门诊
	 */
	public static final BigDecimal BILL_TYPE_CANCER = new BigDecimal(3);

	/**
	 * 账单类型 - 0-门诊住院
	 */
	public static final BigDecimal BILL_TYPE_SYNTHESIS = new BigDecimal(0);

	/**
	 * 公共参数类型 -预付比例
	 */
	public static final String ADVANCE_PARA_TYPE = "1";

	/**
	 * 非取消预付的标识
	 */
	public static final String NON_CANCEL_ADVANCE_FLAG = "0";

	/**
	 * 取消预付的标识
	 */
	public static final String CANCEL_ADVANCE_FLAG = "1";

	/**
	 * 预付审批结论为通过
	 */
	public static final String ADVANCE_AUDIT_DECISION = new String("1");

	/**
	 * 传值到工作流，预付审批结论通过为1
	 */
	public static final String BPM_ADVANCE_AUDIT_PASS = "1";

	/**
	 * 传值到工作流，预付审批结论不通过为0
	 */
	public static final String NON_BPM_ADVANCE_AUDIT_PASS = "0";

	/**
	 * 支付类型为返盘制
	 */
	public static final String PAYMODE_BACK_SYSTEM = "32";

	/**
	 * 接口调用成功
	 */
	public static final String INVOKING_SUCCEED = "01";

	/**
	 * 理赔编码
	 */
	public static final String CLM = "067";
	/**
	 * 保全编码
	 */
	public static final String CUS = "068";
	/**
	 * 新契约编码
	 */
	public static final String NB = "064";
	/**
	 * 保单锁：保全
	 */
	public static final String POLICYLOCK_BQ = "OA";
	/**
	 * 理赔锁(目前理赔挂起使用的service_code)
	 */
	public static final String POLICYLOCK_CLM = "OA";

	/**
	 * 保单锁：续期
	 */
	public static final String POLICYLOCK_XQ = "OB";

	/**
	 * 保单锁：解锁流程代码
	 */
	public static final String POLICYLOCK_CLEAR = "2";
	/**
	 * 理赔锁：理赔锁lock_service_id挂起解挂
	 */
	public static final String POLICYLOCK_CLM_LOCKSERVICEID = "93";
	/**
	 * 保全锁：理赔锁lock_service_id挂起解挂
	 */
	public static final BigDecimal POLICYLOCK_CLM_PERSEVATIONLOCK = BigDecimal.valueOf(94);
	/**
	 * 续期锁：理赔锁lock_service_id挂起解挂
	 */
	public static final BigDecimal POLICYLOCK_CLM_RENEWALLOCK = BigDecimal.valueOf(95);
	/**
	 * 黑名单挂起：lock_service_id挂起解挂
	 */
	public static final String POLICYLOCK_CLM_BLACKLOCK = "100";

	/**
	 * 保单锁：加锁流程代码
	 */
	public static final String POLICYLOCK_SET = "1";

	/**
	 * 通知书号
	 */
	public static final String ADVICE_NOTE_NUMBER = "DOCUMENTNO";

	/**
	 * 通知书类型 - 分割单
	 */
	public static final String CARVE_UP_LIST = "分割单";

	/**
	 * 分割单 - 编码
	 */
	public static final String CARVE_UP_LIST_CODE = "CLM_00001";

	/**
	 * 通知书类型 - 理赔回退收费通知书
	 */
	public static final String BACK_AMOUNT_LIST = "理赔回退收费通知书";

	/**
	 * 理赔回退收费通知书 - 编码
	 */
	public static final String BACK_AMOUNT_LIST_CODE = "CLM_00005";

	/**
	 * 通知书类型 - 拒付通知书
	 */
	public static final String PROTEST_ADVICE_NOTE = "理赔决定通知书（拒付）";

	/**
	 * 拒付通知书 - 编码
	 */
	public static final String PROTEST_ADVICE_NOTE_CODE = "CLM_00002";

	/**
	 * 通知书类型 - 赔付依据与说明
	 */
	public static final String PAY_GOES_AND_EXPLAIN = "赔付依据与说明";

	/**
	 * 赔付依据与说明 - 编码
	 */
	public static final String PAY_GOES_AND_EXPLAIN_CODE = "CLM_00010";
	/**
	 * 通知书类型 - 索赔申请书
	 */
	public static final String INDEMNITY_APPLICATION = "索赔申请书";

	/**
	 * 索赔申请书 - 编码
	 */
	public static final String INDEMNITY_APPLICATION_CODE = "CLM_00008";

	/**
	 * 理赔单证通知书 - 编码
	 */
	public static final String DOCUMENT_NOTE = "理赔单证通知书";

	/**
	 * 理赔单证通知书 - 编码
	 */
	public static final String DOCUMENT_NOTE_CODE = "CLM_00004";
	/**
	 * 理赔单证通知书 - 编码
	 */
	public static final String CASE_RISK_NOTE = "案件风险报告";
	/**
	 * 理赔单证通知书 - 编码
	 */
	public static final String CASE_RISK_NOTE_CODE = "CLM_00022";
	/**
	 * 理赔单证通知书 - 通知书状态
	 */
	public static final String STATUS_ZERO = "0";
	/**
	 * 理赔单证通知书 - 通知书业务来源
	 */
	public static final String BUSS_SOURCE_CODE_FIVE = "005";
	/**
	 * 理赔单证通知书 - 通知书发送对象
	 */
	public static final String SEND_OBJ_TYPE_FOUR = "04";

	/**
	 * 收费付费标识 - 收费
	 */
	public static final String COLLECT_FEES = "1";

	/**
	 * 收费付费标识 - 付费
	 */
	public static final String PAY = "2";

	/**
	 * 应收应付的收付状态 - 已收付
	 */
	public static final String FEE_STATUS = "01";

	/**
	 * 应收应付的收付状态 - 支付失败
	 */
	public static final String FEE_STATUS_FAIL = "03";
	/**
	 * 备注问题件类型
	 */
	public static final String REMARK_COUNT_ONE_TYPE = "1";
	/**
	 * 备注选项
	 */
	public static final String REMARK_COUNT_ONE_OPTION = "101";
	/**
	 * 备注类型：客户问题记录
	 */
	public static final String REMARK_COUNT_EIGHT_TYPE = "8";
	/**
	 * 备注选项:申请人录入错误
	 */
	public static final String REMARK_COUNT_EIGHT_OPTION = "802";
	/**
	 * 备注类型：实名制查验证明
	 */
	public static final String REMARK_COUNT_SIXTEEN_TYPE = "16";
	/**
	 * 备注选项：实名制查验证明上载
	 */
	public static final String REMARK_COUNT_SIXTEEN_OPTION = "162";
	/**
	 * 备注选项：实名制查验证明下载
	 */
	public static final String REMARK_COUNT_SIXTEEN_DOWN = "161";
	/**
	 * 备注选项
	 */
	public static final String REMARK_COUNT_THIRTEEN_ONE_OPTION = "131";
	/**
	 * 备注问题件类型 15 直赔案件问题记录
	 */
	public static final String REMARK_COUNT_FIFTEEN_TYPE = "15";
	/**
	 * 存在直连数据问题件19
	 */
	public static final String DIRECT_CONN_MEMO_TYPE = "19";
	/**
	 * 备注选项 10 移动签收类型10
	 */
	public static final String REMARK_COUNT_MOBILE_TEN_OPTION = "10";
	/**
	 * 备注选项 11 移动签收类型10对应新核心11
	 */
	public static final String REMARK_COUNT_ELEVEN_OPTION = "11";
	/**
	 * 备注选项 11 移动签收类型11
	 */
	public static final String REMARK_COUNT_MOBILE_ELEVEN_OPTION = "11";
	/**
	 * 备注选项 13 移动签收类型11对应新核心13
	 */
	public static final String REMARK_COUNT_THIRTEEN_OPTION = "13";
	/**
	 * 备注类型：18-直连立案问题件
	 */
	public static final String REMARK_COUNT_EIGHTEEN_TYPE = "18";
	/**
	 * 备注选项：直连立案问题件 -182-特殊险种校验
	 */
	public static final String REMARK_COUNT_EIGHTEEN_TWO_OPTION = "182";
	/**
	 * 任务分配状态 ：全部
	 */
	public static final String ASSIGN_TASK_ALL = "1";

	/**
	 * 任务分配状态 ：已分配
	 */
	public static final String ASSIGN_TASK_ALREADY = "3";

	/**
	 * 任务分配状态 ：未分配
	 */
	public static final String ASSIGN_TASK_NON = "2";

	/**
	 * 理赔类型 ：豁免
	 */
	public static final String CLAIMTYPE_ELEVEN = "11";

	/**
	 * 业务员类型：星级
	 */
	public static final BigDecimal STAR_AGENT_TYPE = new BigDecimal("1");
	/**
	 * 受托人类型：业务员
	 */
	public static final BigDecimal TRUSTEE_TYPE_YWY = new BigDecimal("0");
	/**
	 * 受托人类型：客户
	 */
	public static final BigDecimal TRUSTEE_TYPE_CUSTOMER = new BigDecimal("1");
	/**
	 * 受托人类型：其它
	 */
	public static final BigDecimal TRUSTEE_TYPE_OTHER = new BigDecimal("2");

	/**
	 * 受托人类型：不在受托人类型之内
	 */
	public static final BigDecimal TRUSTEE_TYPE_THREE = new BigDecimal("3");

	/**
	 * 续保决定：不续保
	 */
	public static final BigDecimal DECISION_DESC_NO = new BigDecimal("1");
	/**
	 * 续保决定：可以续保
	 */
	public static final BigDecimal DECISION_DESC_AGREE = new BigDecimal("2");
	/**
	 * 续保决定：已经续保
	 */
	public static final BigDecimal DECISION_DESC_ALREADY = new BigDecimal("3");
	/**
	 * 续保决定：豁免标识-YES
	 */
	public static final BigDecimal WAIVE_YES = new BigDecimal("1");
	/**
	 * 续保决定：豁免标识-NO
	 */
	public static final BigDecimal WAIVE_NO = new BigDecimal("0");
	/**
	 * 附加险
	 */
	public static final String SHORT_RISK = "10002";
	/**
	 * 意外伤害险
	 */
	public static final String PRODUCT__CATEGORY2_UNEXPECTED = "30002";
	/**
	 * 意外险
	 */
	public static final String PRODUCT__CATEGORY3_UNEXPECTED = "40012";
	/**
	 * 全部给付
	 */
	public static final BigDecimal CLAIM_LIAB_DECISION_ALLPAY = new BigDecimal("1");
	/**
	 * 部分给付
	 */
	public static final BigDecimal CLAIM_LIAB_DECISION_PARTPAY = new BigDecimal("2");
	/**
	 * 协议给付
	 */
	public static final BigDecimal CLAIM_LIAB_DECISION_AGREEMENTPAY = new BigDecimal("3");
	/**
	 * 通融给付
	 */
	public static final BigDecimal CLAIM_LIAB_DECISION_PARDONPAY = new BigDecimal("4");
	/**
	 * 拒付
	 */
	public static final BigDecimal CLAIM_LIAB_DECISION_REFUSEPAY = new BigDecimal("5");
	/**
	 * 生效
	 */
	public static final BigDecimal LIABILITY_STATUS_EFFECTIVE = new BigDecimal("1");
	/**
	 * 中止
	 */
	public static final BigDecimal LIABILITY_STATUS_SUSPEND = new BigDecimal("2");
	/**
	 * 终止
	 */
	public static final BigDecimal LIABILITY_STATUS_STOP = new BigDecimal("3");
	/**
	 * 失效
	 */
	public static final BigDecimal LIABILITY_STATUS_LOSE_EFFECTIVENESS = new BigDecimal("4");
	/**
	 * 其他
	 */
	public static final BigDecimal LIABILITY_STATUS_LOSE_RESTS = new BigDecimal("9");
	/**
	 * 未生效
	 */
	public static final BigDecimal LIABILITY_STATUS_LOSE_INVALID = new BigDecimal("0");
	/**
	 * 立案通过
	 */
	public static final BigDecimal ACCEPT_DECISION_PASS = new BigDecimal("1");
	/**
	 * 不予立案
	 */
	public static final BigDecimal ACCEPT_DECISION_NOT = new BigDecimal("2");
	/**
	 * 延迟立案
	 */
	public static final BigDecimal ACCEPT_DECISION_DELAY = new BigDecimal("3");

	/**
	 * 保项赔付结论 5：拒付
	 */
	public static final BigDecimal LIAB_CONSION_FIVE = new BigDecimal(5);
	/**
	 * 理赔后保单状态 -继续有效
	 */
	public static final String CLM_AFTER_STATE_ONE = "1";
	/**
	 * 理赔后保单状态 -责任终止
	 */
	public static final String CLM_AFTER_STATE_TWO = "2";
	/**
	 * 理赔后保单状态 -责任组终止
	 */
	public static final String CLM_AFTER_STATE_THREE = "3";
	/**
	 * 理赔后保单状态 -险种终止
	 */
	public static final String CLM_AFTER_STATE_FOUR = "4";
	/**
	 * 理赔后保单状态 -本险种及其附加险种终止
	 */
	public static final String CLM_AFTER_STATE_FIVE = "5";
	/**
	 * 理赔后保单状态 -同一被保险人所有责任终止
	 */
	public static final String CLM_AFTER_STATE_SIX = "6";
	/**
	 * 理赔后保单状态 -保单终止
	 */
	public static final String CLM_AFTER_STATE_SEVEN = "7";

	/**
	 * 费用支付状态-待处理
	 */
	public static final String FEE_STATUS_00 = "00";
	/**
	 * 费用支付状态-锁定客户账号
	 */
	public static final String FEE_STATUS_17 = "17";
	/**
	 * 费用支付状态-已确认
	 */
	public static final String FEE_STATUS_01 = "01";

	/**
	 * 费用支付状态-取消
	 */
	public static final String FEE_STATUS_02 = "02";
	/**
	 * 费用支付状态-转账失败
	 */
	public static final String FEE_STATUS_03 = "03";
	/**
	 * 费用支付状态-转账途中
	 */
	public static final String FEE_STATUS_04 = "04";
	/**
	 * 费用支付状态-追缴
	 */
	public static final String FEE_STATUS_05 = "05";
	/**
	 * 费用支付状态-支票未打印
	 */
	public static final String FEE_STATUS_06 = "06";
	/**
	 * 费用支付状态-账号待复核
	 */
	public static final String FEE_STATUS_07 = "07";
	/**
	 * 费用支付状态-账号复核退回
	 */
	public static final String FEE_STATUS_08 = "08";
	/**
	 * 费用支付状态-再保账单已出
	 */
	public static final String FEE_STATUS_09 = "09";
	/**
	 * 费用支付状态-转账暂停
	 */
	public static final String FEE_STATUS_10 = "10";
	/**
	 * 费用支付状态-记账后取消
	 */
	public static final String FEE_STATUS_11 = "11";
	/**
	 * 费用支付状态-临时费用状态
	 */
	public static final String FEE_STATUS_12 = "12";
	/**
	 * 费用支付状态-用于审批的临时费用状态
	 */
	public static final String FEE_STATUS_13 = "13";
	/**
	 * 费用支付状态-中止
	 */
	public static final String FEE_STATUS_14 = "14";
	/**
	 * 费用支付状态-票据待确认
	 */
	public static final String FEE_STATUS_15 = "15";
	/**
	 * 费用支付状态-不可收付
	 */
	public static final String FEE_STATUS_16 = "16";
	/**
	 * 费用支付状态-核销
	 */
	public static final String FEE_STATUS_19 = "19";
	/**
	 * 费用支付类型-应收
	 */
	public static final String ARAP_FLAG_AR = "1";
	/**
	 * 费用支付类型-应付
	 */
	public static final String ARAP_FLAG_AP = "2";
	/**
	 * 回退申请复核结论，1为通过
	 */
	public static final BigDecimal BACK_AUDIT_DECISION_YES = new BigDecimal(1);

	/**
	 * 回退申请复核结论，2为未通过
	 */
	public static final BigDecimal BACK_AUDIT_DECISION_NO = new BigDecimal(2);
	/**
	 * 收付费接口，业务响应码-成功
	 */
	public static final String ARAP_RESULTCODE_01 = "01";
	/**
	 * 费用支付方式-现金
	 */
	public static final String PAY_MODE_10 = "10";
	/**
	 * 费用支付方式-现金送款薄
	 */
	public static final String PAY_MODE_11 = "11";
	/**
	 * 费用支付方式-支票
	 */
	public static final String PAY_MODE_20 = "20";
	/**
	 * 费用支付方式-现金支票
	 */
	public static final String PAY_MODE_21 = "21";
	/**
	 * 费用支付方式-转账支票
	 */
	public static final String PAY_MODE_22 = "22";
	/**
	 * 费用支付方式-银行转账
	 */
	public static final String PAY_MODE_30 = "30";
	/**
	 * 费用支付方式-银行转账（非制返盘）
	 */
	public static final String PAY_MODE_31 = "31";
	/**
	 * 费用支付方式-银行转账（制返盘）
	 */
	public static final String PAY_MODE_32 = "32";
	/**
	 * 费用支付方式-网银转账
	 */
	public static final String PAY_MODE_33 = "33";
	/**
	 * 费用支付方式-网上银行
	 */
	public static final String PAY_MODE_34 = "34";
	/**
	 * 费用支付方式-实时收费
	 */
	public static final String PAY_MODE_40 = "40";
	/**
	 * 费用支付方式-POS收款
	 */
	public static final String PAY_MODE_41 = "41";
	/**
	 * 费用支付方式-内部转账
	 */
	public static final String PAY_MODE_50 = "50";
	/**
	 * 费用支付方式-普通内部转账
	 */
	public static final String PAY_MODE_51 = "51";
	/**
	 * 费用支付方式-预存内部转账
	 */
	public static final String PAY_MODE_52 = "52";
	/**
	 * 费用支付方式-银保通
	 */
	public static final String PAY_MODE_60 = "60";
	/**
	 * 费用支付方式-第三方支付
	 */
	public static final String PAY_MODE_70 = "70";
	/**
	 * 费用支付方式-客户暂存
	 */
	public static final String PAY_MODE_80 = "80";
	/**
	 * 费用支付方式-客户账户
	 */
	public static final String PAY_MODE_42 = "42";
	/**
	 * 费用支付方式-其它
	 */
	public static final String PAY_MODE_90 = "90";
	/**
	 * 费用支付方式-内部扣回
	 */
	public static final String PAY_MODE_99 = "99";
	/**
	 * 保单挂起解挂场景-理赔报案
	 */
	public static final String POLICYLOCK_CLM_REPORT = "4001";
	/**
	 * 保单挂起解挂场景-理赔报案
	 */
	public static final String POLICYLOCK_REPORT_SERVICECODE = "LPBA";
	/**
	 * 保单挂起解挂场景-理赔签收
	 */
	public static final String POLICYLOCK_CLM_SIGN = "4002";
	/**
	 * 保单挂起解挂场景-理赔签收
	 */
	public static final String POLICYLOCK_SIGN_SERVICECODE = "LPQS";
	/**
	 * 保单挂起解挂场景-理赔立案
	 */
	public static final String POLICYLOCK_CLM_REGISTER = "4003";
	/**
	 * 保单挂起解挂场景-理赔立案
	 */
	public static final String POLICYLOCK_REGISTER_SERVICECODE = "LPLA";
	/**
	 * 保单挂起解挂场景-理赔审核
	 */
	public static final String POLICYLOCK_CLM_AUDIT = "4004";
	/**
	 * 保单挂起解挂场景-理赔审核
	 */
	public static final String POLICYLOCK_AUDIT_SERVICECODE = "LPSH";
	/**
	 * 保单挂起解挂场景-理赔审批
	 */
	public static final String POLICYLOCK_CLM_APPROVE = "4005";
	/**
	 * 保单挂起解挂场景-理赔审批
	 */
	public static final String POLICYLOCK_APPROVE_SERVICECODE = "LPSP";
	/**
	 * 宽限期止期 天数 60
	 */
	public static final int PayEnd_Days = 60;

	/**
	 * 医疗账单类型-医疗/门诊
	 */
	public static final BigDecimal CLM_BILL_ONE = new BigDecimal("1");
	/**
	 * 医疗账单类型-高端医疗
	 */
	public static final BigDecimal CLM_BILL_TWO = new BigDecimal("2");
	/**
	 * 医疗账单类型-防癌医疗
	 */
	public static final BigDecimal CLM_BILL_THREE = new BigDecimal("3");
	/**
	 * 保单终止结论:解除合同退还保费
	 */
	public static final BigDecimal POLICY_DEAL_CONCLUSION_ONE = new BigDecimal("1");
	/**
	 * 保单终止结论:解除合同退还现金价值
	 */
	public static final BigDecimal POLICY_DEAL_CONCLUSION_TWO = new BigDecimal("2");
	/**
	 * 保单终止结论:解除合同退还账户价值
	 */
	public static final BigDecimal POLICY_DEAL_CONCLUSION_THREE = new BigDecimal("3");
	/**
	 * 保单终止结论:解除合同不退费
	 */
	public static final BigDecimal POLICY_DEAL_CONCLUSION_FOUR = new BigDecimal("4");
	/**
	 * 保单终止结论:合同终止
	 */
	public static final BigDecimal POLICY_DEAL_CONCLUSION_FIVE = new BigDecimal("5");
	/**
	 * 险种终止结论:险种终止
	 */
	public static final BigDecimal PROD_DEAL_CONCLUSION_ONE = new BigDecimal("1");
	/**
	 * 险种终止结论:险种终止退现金价值
	 */
	public static final BigDecimal PROD_DEAL_CONCLUSION_TWO = new BigDecimal("2");
	/**
	 * 险种终止结论:险种终止退未满期保费
	 */
	public static final BigDecimal PROD_DEAL_CONCLUSION_THREE = new BigDecimal("3");
	/**
	 * 险种终止结论:险种终止退当期保费
	 */
	public static final BigDecimal PROD_DEAL_CONCLUSION_FOUR = new BigDecimal("4");
	/**
	 * 险种终止结论:险种终止退账户价值
	 */
	public static final BigDecimal PROD_DEAL_CONCLUSION_FIVE = new BigDecimal("5");
	/**
	 * 险种终止结论:险种终止退保费
	 */
	public static final BigDecimal PROD_DEAL_CONCLUSION_SIX = new BigDecimal("6");
	/**
	 * 保障年期类型：Y;定期(年)
	 */
	public static final String COVERAGE_PERIOD_Y = "Y";
	/**
	 * 保障年期类型：A;定期(岁)
	 */
	public static final String COVERAGE_PERIOD_A = "A";
	/**
	 * 保障年期类型： W;终身
	 */
	public static final String COVERAGE_PERIOD_W = "W";
	/**
	 * 保障年期类型：M;定期(月 )
	 */
	public static final String COVERAGE_PERIOD_M = "M";
	/**
	 * 保障年期类型：D;定期(天)
	 */
	public static final String COVERAGE_PERIOD_D = "D";
	/**
	 * 理赔类型：身故
	 */
	public static final String CLAIM_TYPE_ONG = "01";
	/**
	 * 理赔类型：伤残
	 */
	public static final String CLAIM_TYPE_TWO = "02";
	/**
	 * 理赔类型：重大疾病
	 */
	public static final String CLAIM_TYPE_THREE = "03";
	/**
	 * 理赔类型：高残
	 */
	public static final String CLAIM_TYPE_FOUR = "04";
	/**
	 * 理赔类型：一般失能
	 */
	public static final String CLAIM_TYPE_SIX = "06";
	/**
	 * 理赔类型：重度失能
	 */
	public static final String CLAIM_TYPE_SEVEN = "07";
	/**
	 * 理赔类型：医疗
	 */
	public static final String CLAIM_TYPE_EIGHT = "08";

	/**
	 * 理赔类型：特种疾病
	 */
	public static final String CLAIM_TYPE_TEN = "10";
	/**
	 * 理赔类型：豁免
	 */
	public static final String CLAIM_TYPE_ELEVEN = "11";

	/**
	 * 医疗险
	 */
	public static final String CLM_TYPE_YI = "40007";
	/**
	 * 被保人豁免责任list
	 */
	public static final List<String> WAIVE_INSURED_List = new ArrayList<String>();
	/**
	 * 投保人豁免责任list
	 */
	public static final List<String> WAIVE_HOLDER_List = new ArrayList<String>();
	/**
	 * 静态值
	 */
	static {
		/**
		 * 为被保人 豁免责任赋值
		 */
		WAIVE_INSURED_List.add("9011");
		WAIVE_INSURED_List.add("9008");
		WAIVE_INSURED_List.add("9002");
		WAIVE_INSURED_List.add("9005");
		WAIVE_INSURED_List.add("9004");
		WAIVE_INSURED_List.add("9010");
		WAIVE_INSURED_List.add("9009");

		/**
		 * 为投保人 豁免责任赋值
		 */
		WAIVE_HOLDER_List.add("9007");
		WAIVE_HOLDER_List.add("9006");
		WAIVE_HOLDER_List.add("9003");
		WAIVE_HOLDER_List.add("9001");
	}
	/**
	 * 是否生存 ：是；1
	 */
	public static final String LIFE_STATE_YES = "1";
	/**
	 * 是否生存 ：否；0
	 */
	public static final String LIFE_STATE_NO = "0";
	/**
	 * 是否超时：是；1
	 */
	public static final String OVER_TIME_YES = "1";
	/**
	 * 是否超时：否；0
	 */
	public static final String OVER_TIME_NO = "0";

	/**
	 * 受理渠道 03：柜面受理
	 */
	public static final String CHANNEL_CODE_PANEL = "03";
	/**
	 * 受理渠道 07：移动受理
	 */
	public static final String CHANNEL_CODE_MOVE = "07";
	/**
	 * 受理渠道 09：官微
	 */
	public static final String CHANNEL_CODE_NINE = "09";
	/**
	 * 受理渠道10：直赔
	 */
	public static final String CHANNEL_CODE_TEN = "11";
	/**
	 * 受理渠道 11：快赔
	 */
	public static final String CHANNEL_CODE_ELEVEN = "12";

	/**
	 * 客户权益部 最高审批权限
	 * 
	 * @Fields AP_SEVENTEEN : int
	 */
	public static final int AP_CUSTOMER_POWER = 29;
	/**
	 * 缴费频率-趸缴
	 */
	public static final BigDecimal PREM_FREQ_ONE = new BigDecimal(1);
	/**
	 * 缴费频率-月缴
	 */
	public static final BigDecimal PREM_FREQ_TWO = new BigDecimal(2);
	/**
	 * 缴费频率 -季缴
	 */
	public static final BigDecimal PREM_FREQ_THREE = new BigDecimal(3);
	/**
	 * 缴费频率-半年缴
	 */
	public static final BigDecimal PREM_FREQ_FOUR = new BigDecimal(4);
	/**
	 * 缴费频率-年缴
	 */
	public static final BigDecimal PREM_FREQ_FIVE = new BigDecimal(5);
	/**
	 * 缴费频率-不定期缴
	 */
	public static final BigDecimal PREM_FREQ_SIX = new BigDecimal(6);
	/**
	 * 缴费频率-其他
	 */
	public static final BigDecimal PREM_FREQ_SEVEN = new BigDecimal(7);
	/**
	 * 缴费年期类型-无关
	 */
	public static final BigDecimal CHARGE_PERIOD_ZERO = new BigDecimal(0);
	/**
	 * 缴费年期类型-趸缴
	 */
	public static final BigDecimal CHARGE_PERIOD_ONE = new BigDecimal(1);
	/**
	 * 缴费年期类型-年缴
	 */
	public static final BigDecimal CHARGE_PERIOD_TWO = new BigDecimal(2);
	/**
	 * 缴费年期类型-交至某确定年龄
	 */
	public static final BigDecimal CHARGE_PERIOD_THREE = new BigDecimal(3);
	/**
	 * 缴费年期类型-终身交费
	 */
	public static final BigDecimal CHARGE_PERIOD_FOUR = new BigDecimal(4);
	/**
	 * 缴费年期类型-不定期交
	 */
	public static final BigDecimal CHARGE_PERIOD_FIVE = new BigDecimal(5);
	/**
	 * 缴费年期类型-按月限交
	 */
	public static final BigDecimal CHARGE_PERIOD_SIX = new BigDecimal(6);
	/**
	 * 缴费年期类型-按天限交
	 */
	public static final BigDecimal CHARGE_PERIOD_SEVEN = new BigDecimal(7);
	/**
	 * 单证扫描状态 - 未核销
	 */
	public static final BigDecimal CHECKLIST_STATUS_ONE = new BigDecimal(1);
	/**
	 * 单证扫描状态 - 已核销
	 */
	public static final BigDecimal CHECKLIST_STATUS_TWO = new BigDecimal(2);
	/**
	 * 回退审核结论 - 通过
	 */
	public static final BigDecimal BACK_AUDIT_DECISION_ONE = new BigDecimal(1);
	/**
	 * 回退审核结论 - 未通过
	 */
	public static final BigDecimal BACK_AUDIT_DECISION_TWO = new BigDecimal(2);
	/**
	 * 保障期间类型： 一年期以上
	 */
	public static final BigDecimal COVER_PERIOD_TYPE_ZERO = new BigDecimal(0);
	/**
	 * 险种设计类型 - 普通型
	 */
	public static final String PRODUCT_CATEGORY1_20001 = "20001";
	/**
	 * 险种设计类型 - 分红型
	 */
	public static final String PRODUCT_CATEGORY1_20002 = "20002";
	/**
	 * 险种设计类型 - 万能型
	 */
	public static final String PRODUCT_CATEGORY1_20003 = "20003";
	/**
	 * 险种设计类型 - 投资连结型
	 */
	public static final String PRODUCT_CATEGORY1_20004 = "20004";
	/**
	 * 责任类型：身故
	 */
	public static final String LIAB_CATEGORY_ONE = "01";
	/**
	 * 责任类型：伤残
	 */
	public static final String LIAB_CATEGORY_TWO = "02";
	/**
	 * 责任类型：重疾
	 */
	public static final String LIAB_CATEGORY_THREE = "03";
	/**
	 * 责任类型：高残
	 */
	public static final String LIAB_CATEGORY_FOUR = "04";
	/**
	 * 责任类型：一般失能
	 */
	public static final String LIAB_CATEGORY_FIVE = "05";
	/**
	 * 责任类型：重度失能
	 */
	public static final String LIAB_CATEGORY_SIX = "06";
	/**
	 * 责任类型：医疗
	 */
	public static final String LIAB_CATEGORY_SEVEN = "07";
	/**
	 * 责任类型：特种疾病
	 */
	public static final String LIAB_CATEGORY_EIGHT = "08";
	/**
	 * 责任类型：豁免
	 */
	public static final String LIAB_CATEGORY_NINE = "09";
	/**
	 * 责任类型：生存金或年金
	 */
	public static final String LIAB_CATEGORY_ELEVEN = "11";
	/**
	 * 责任类型：满期金
	 */
	public static final String LIAB_CATEGORY_TWELVE = "12";
	/**
	 * 出现原因 ： 疾病
	 */
	public static final BigDecimal ACC_REASON_ONE = new BigDecimal("1");
	/**
	 * 出险原因: 意外
	 */
	public static final BigDecimal ACC_REASON_TWO = new BigDecimal("2");
	/**
	 * 计算投连万能扣费金额 -费用类型 -退保费用
	 */
	public static final int CHARGE_TYPE = 4;
	/**
	 * 累加器类型:扣款
	 */
	public static final BigDecimal ACCUMU_TYPE_ONE = new BigDecimal("1");
	/**
	 * 累加器类型:共担
	 */
	public static final BigDecimal ACCUMU_TYPE_TWO = new BigDecimal("2");
	/**
	 * 累加器类型:责任限额
	 */
	public static final BigDecimal ACCUMU_TYPE_THREE = new BigDecimal("3");
	/**
	 * 累加器类型:双重账单住院天数
	 */
	public static final BigDecimal ACCUMU_TYPE_FOUR = new BigDecimal("4");
	/**
	 * 累加器类型:乘法累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_FIVE = new BigDecimal("5");
	/**
	 * 累加器类型:最小值累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_SIX = new BigDecimal("6");
	/**
	 * 累加器类型:账单住院天数
	 */
	public static final BigDecimal ACCUMU_TYPE_SEVEN = new BigDecimal("7");
	/**
	 * 累加器类型:双重乘法累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_EIGHT = new BigDecimal("8");
	/**
	 * 累加器类型:双重最小值累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_NINE = new BigDecimal("9");
	/**
	 * 累加器类型:双重责任限额
	 */
	public static final BigDecimal ACCUMU_TYPE_TEN = new BigDecimal("10");
	/**
	 * 累加器类型:汇总金额限制累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_ELEVEN = new BigDecimal("11");
	/**
	 * 累加器类型:汇总天数限制累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_TWELVE = new BigDecimal("12");
	/**
	 * 累加器类型:限次累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_THIRTEEN = new BigDecimal("13");
	/**
	 * 累加器类型:双重限次累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_FOURTEE = new BigDecimal("14");
	/**
	 * 累加器类型:免赔额累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_FIFTEEN = new BigDecimal("15");
	/**
	 * 累加器类型:免赔额累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_SIXTEEN = new BigDecimal("16");
	/**
	 * 累加器类型:汇总限次累加器
	 */
	public static final BigDecimal ACCUMU_TYPE_SEVENTEEN = new BigDecimal("17");
	/**
	 * 调整原因:扣除自垫本金
	 */
	public static final String ADJUST_REASON_ONE = "01";
	/**
	 * 调整原因:扣除自垫利息
	 */
	public static final String ADJUST_REASON_TWO = "02";
	/**
	 * 调整原因:扣除保单质押贷款本金
	 */
	public static final String ADJUST_REASON_THREE = "03";
	/**
	 * 调整原因:扣除保单质押贷款利息
	 */
	public static final String ADJUST_REASON_FOUR = "04";
	/**
	 * 调整原因:扣除理赔事故发生后超额给付年金
	 */
	public static final String ADJUST_REASON_FIVE = "05";
	/**
	 * 调整原因:延迟报案导致查勘费扣除
	 */
	public static final String ADJUST_REASON_SIX = "06";
	/**
	 * 调整原因:投保前疾病
	 */
	public static final String ADJUST_REASON_SEVEN = "07";
	/**
	 * 调整原因:其他
	 */
	public static final String ADJUST_REASON_NUM = "99";
	/**
	 * claimCase FLAG 禁用
	 */
	public static final String CLAIM_CASE_FLAG_ONE = "1";
	/**
	 * claimCase FLAG 不禁用
	 */
	public static final String CLAIM_CASE_FLAG_ZERO = "0";
	/**
	 * 业务来源 :
	 */
	public static final String DOCUMENTVON_BUSS_SOURCE_CODE_FOUR = "4";
	/**
	 * 通知书状态 ：待发放
	 */
	public static final String DOCUMENTVON_STATUS_ZERO = "0";
	/**
	 * BigDecimal(0)
	 */
	public static final BigDecimal BIGDECIMAL_ZERO = new BigDecimal(0);
	/**
	 * BigDecimal(1)
	 */
	public static final BigDecimal BIGDECIMAL_ONE = new BigDecimal(1);
	/**
	 * BigDecimal(2)
	 */
	public static final BigDecimal BIGDECIMAL_TWO = new BigDecimal(2);
	/**
	 * BigDecimal(3)
	 */
	public static final BigDecimal BIGDECIMAL_THREE = new BigDecimal(3);
	/**
	 * BigDecimal(4)
	 */
	public static final BigDecimal BIGDECIMAL_FOUR = new BigDecimal(4);
	/**
	 * BigDecimal(5)
	 */
	public static final BigDecimal BIGDECIMAL_FIVE = new BigDecimal(5);
	/**
	 * BigDecimal(6)
	 */
	public static final BigDecimal BIGDECIMAL_SIX = new BigDecimal(6);
	/**
	 * BigDecimal(1000)
	 */
	public static final BigDecimal BIGDECIMAL_ONE_THOUSAND = new BigDecimal(1000);
	/**
	 * BigDecimal(2000)
	 */
	public static final BigDecimal BIGDECIMAL_TWO_THOUSAND = new BigDecimal(2000);
	/**
	 * BigDecimal(3000)
	 */
	public static final BigDecimal BIGDECIMAL_THREE_THOUSAND = new BigDecimal(3000);
	/**
	 * BigDecimal(4000)
	 */
	public static final BigDecimal BIGDECIMAL_FOUR_THOUSAND = new BigDecimal(4000);
	/**
	 * BigDecimal(5000)
	 */
	public static final BigDecimal BIGDECIMAL_FIVE_THOUSAND = new BigDecimal(5000);
	/**
	 * 投保人
	 */
	public static final String PEOPLEFLAG_ZERO = "0";
	/**
	 * 被保人
	 */
	public static final String PEOPLEFLAG_ONE = "1";
	/**
	 * 是否执行标识：已执行
	 */
	public static final String CHECK_STRING_ONE = "1";

	/**
	 * String 0
	 */
	public static final String STRING_ZERO = "0";
	/**
	 * String 1
	 */
	public static final String STRING_ONE = "1";
	/**
	 * String 2
	 */
	public static final String STRING_TWO = "2";
	/**
	 * String 3
	 */
	public static final String STRING_THREE = "3";
	/**
	 * String 4
	 */
	public static final String STRING_FOUR = "4";
	/**
	 * String 5
	 */
	public static final String STRING_FIVE = "5";
	/**
	 * String 6
	 */
	public static final String STRING_SIX = "6";
	/**
	 * String 7
	 */
	public static final String STRING_SEVEN = "7";
	/**
	 * String 8
	 */
	public static final String STRING_EIGHT = "8";
	/**
	 * String 9
	 */
	public static final String STRING_NINE = "9";
	/**
	 * String 10
	 */
	public static final String STRING_TEN = "10";
	/**
	 * String 11
	 */
	public static final String STRING_EVELEN = "11";
	/**
	 * String 12
	 */
	public static final String STRING_TWELVE = "12";
	/**
	 * String 13
	 */
	public static final String STRING_THIRTEEN = "13";
	/**
	 * String 14
	 */
	public static final String STRING_FOURTEEN = "14";
	/**
	 * String 15
	 */
	public static final String STRING_FIFTEENTH = "15";
	/**
	 * String 16
	 */
	public static final String STRING_SIXTEEN = "16";
	/**
	 * String 17
	 */
	public static final String STRING_SEVENTEEN = "17";
	/**
	 * String 18
	 */
	public static final String STRING_EIGHTEEN = "18";
	/**
	 * String 19
	 */
	public static final String STRING_NINETEEN = "19";
	/**
	 * String 20
	 */
	public static final String STRING_TWENTY = "20";
	/**
	 * String 21
	 */
	public static final String STRING_TWENTY_ONE = "21";
	/**
	 * String 22
	 */
	public static final String STRING_TWENTY_TWO = "22";
	/**
	 * String 23
	 */
	public static final String STRING_TWENTY_THREE = "23";
	/**
	 * String 25
	 */
	public static final String STRING_TWENTYFIVE = "25";
	/**
	 * 字符编号01
	 */
	public static final String STRING_SORT_ONE = "01";
	/**
	 * 字符编号02
	 */
	public static final String STRING_SORT_TWO = "02";
	/**
	 * 字符编号03
	 */
	public static final String STRING_SORT_THREE = "03";
	/**
	 * 字符编号04
	 */
	public static final String STRING_SORT_FOUR = "04";
	/**
	 * 字符编号05
	 */
	public static final String STRING_SORT_FIVE = "05";
	/**
	 * 字符编号06
	 */
	public static final String STRING_SORT_SIX = "06";
	/**
	 * 字符编号07
	 */
	public static final String STRING_SORT_SEVEN = "07";
	/**
	 * 字符编号08
	 */
	public static final String STRING_SORT_EIGHT = "08";

	/**
	 * 抄单标记:当前
	 */
	public static final BigDecimal CURFLAG_ONE = new BigDecimal(1);
	/**
	 * 当前代理人
	 */
	public static final BigDecimal AGENT_CURRENT = new BigDecimal(1);
	/**
	 * 绿色通道标识：是
	 */
	public static final BigDecimal GREEN_FALG_ONE = new BigDecimal(1);
	/**
	 * 绿色通道标识：否
	 */
	public static final BigDecimal GREEN_FALG_ZERRO = new BigDecimal(0);
	/**
	 * 获取移动理赔移动签收uuid
	 */
	public static final String CLAIM_MOBILE_UUID = "claimMobileUUid";
	/**
	 * 获取移动理赔移动签收地址
	 */
	public static final String CLAIM_MOBILE = "claimMobile";
	/**
	 * 获取意健险平台结果查询网址
	 */
	public static final String CLAIM_CIITQUERY = "ciitQuery";
	/**
	 * 获取移动理赔渠道页面uuid
	 */
	public static final String MOBILE_CHANNEL_UUID = "mobileChannelUUid";
	/**
	 * 获取移动理赔渠道页面地址
	 */
	public static final String MOBILE_CHANNEL = "mobileChannel";

	/**
	 * BigDecimal 类型数值2-9
	 */

	public static final BigDecimal B_ZERO = new BigDecimal(0);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_ONE = new BigDecimal(1);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_TWO = new BigDecimal(2);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_THREE = new BigDecimal(3);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_FOUR = new BigDecimal(4);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_FIVE = new BigDecimal(5);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_SIX = new BigDecimal(6);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_SEVEN = new BigDecimal(7);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_EIGHT = new BigDecimal(8);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_NINE = new BigDecimal(9);
	/**
	 * BigDecimal 类型数值2-9
	 */
	public static final BigDecimal B_TEN = new BigDecimal(10);
	/**
	 * 重复案件的校验：立案之前为1
	 */
	public static final String REGISTER_BEFORE_ONE = "1";
	/**
	 * 重复案件的校验：立案之后为2
	 */
	public static final String REGISTER_AFTER_TWO = "2";
	/**
	 * 重复案件的校验：不存在重复案件
	 */
	public static final String REGISTER_ZERRO = "0";
	/**
	 * 关于无效保单的校验：1为是
	 */
	public static final String INVALID_POLICY_ONE = "1";
	/**
	 * 出险结果2是否为空的情况：-1为是
	 */
	public static final String ACCIDENT_RESULTTWO_NULL = "-1";
	/**
	 * 判断保险责任是否与理赔类型匹配：10为是
	 */
	public static final String CLAIM_TYPE_MATCHING_TEN = "10";
	/**
	 * 保存或初始化时报错提示
	 */
	public static final String REPORT_ERROR_ONE = "1";
	/**
	 * 记录报案登记的CODE值为17
	 */
	public static final String REPORT_CODE_SEVENTEEN = "17";
	/**
	 * 记录报案登记页面编码，报案信息录入为1
	 */
	public static final String REPORT_PAGE_CODING_ONE = "1";
	/**
	 * 参数类型：2 分次给付
	 */
	public static final String CLAIM_INSTALMENT_TWO = "2";
	/**
	 * 预付标识否
	 */
	public static final BigDecimal ADVANCE_FLAG_ZERRO = new BigDecimal(0);
	/**
	 * VIP客户标识：1为是
	 */
	public static final String CUSTOMER_VIP_ONE = "1";
	/**
	 * 生存金
	 */
	public static final String LIVE_THREE = "3";
	/**
	 * 理赔金转年金
	 */
	public static final String LIVE_TEN = "10";
	/**
	 * 生存金转年金
	 */
	public static final String LIVE_ELEVEN = "11";
	/**
	 * 现金红利
	 */
	public static final String LIVE_ONE = "1";
	/**
	 * 已领取生存金或年金
	 */
	public static final String DRAW_ONE = "01";
	/**
	 * 未领取生存金或年金
	 */
	public static final String DRAW_ZERRO = "00";
	/**
	 * 累计生息账户
	 */
	public static final String ADD_UP_LIVE_ACCOUNT = "1";
	/**
	 * 贷款账户
	 */
	public static final String LOAN_ACCOUNT = "4";
	/**
	 * 自垫账户
	 */
	public static final String FROM_THE_PAD_ACCOUNT = "5";
	/**
	 * 保费余额账户
	 */
	public static final String PREMIUM_BALANCE_ACCOUNT = "13";
	/**
	 * 客户账户余额
	 */
	public static final String CUSTOMER_ACCOUNT_BALANCE = "12";
	/**
	 * 现金红利余额
	 */
	public static final String CASH_BONUS_ACCOUNT = "2";
	/**
	 * 累计生息账户余额
	 */
	public static final String ADD_UP_LIVE_ELEVEN = "11";
	/**
	 * 贷款起息日（保单接口中约定）
	 */
	public static final String LOAN_VALUE_DATE = "interest_start_date";
	/**
	 * 未决任务标识
	 */
	public static final String LOAN_VALUE_FLAG = "21";
	/**
	 * 未决任务标识
	 */
	public static final String LOAN_VALUE_TWENTYTWO = "22";
	/**
	 * @Fields SIGN_NOTICE_CODE_1 : 签收确认给业务员发送信息接口的通知编码
	 */
	public static final BigDecimal SIGN_NOTICE_CODE_1 = new BigDecimal(211);

	/**
	 * @Fields SIGN_NOTICE_CODE_2 :签收确认给客户发送信息接口的通知编码
	 */
	public static final BigDecimal SIGN_NOTICE_CODE_2 = new BigDecimal(212);
	/**
	 * @Fields SIGN_NOTICE_CODE_2 :签收确认给客户发送信息接口的通知编码VIP
	 */
	public static final BigDecimal SIGN_NOTICE_CODE_3 = new BigDecimal(213);
	/**
	 * 绿色通道标识：是（String类型的）
	 */
	public static final String GREEN_FALG_ONE_STR = "1";
	/**
	 * 绿色通道标识：否(String类型的)
	 */
	public static final String GREEN_FALG_ZERRO_STR = "0";
	/**
	 * 用于共享池判断是否传过来的绿色标识为双选
	 */
	public static final String GREEN_FALG_ZERRO_ONE = "1, 0";
	/**
	 * 申请人与被保险人关系为本人
	 */
	public static final String CLM_INSUR_RELATION = "00";
	/**
	 * 收费项目-理赔收费
	 */
	public static final String CHARGE_ITEM_01 = "01";
	/**
	 * 质子重离子治疗及靶向治疗保险金
	 */
	public static final BigDecimal LIAB_ID_8027 = new BigDecimal("8027");
	/**
	 * 癌症放、化疗保险金
	 */
	public static final BigDecimal LIAB_ID_8005 = new BigDecimal("8005");
	/**
	 * 恶性肿瘤确诊保险金
	 */
	public static final BigDecimal LIAB_ID_8051 = new BigDecimal("8051");
	/**
	 * 癌症确诊保险金
	 */
	public static final BigDecimal LIAB_ID_8003 = new BigDecimal("8003");
	/**
	 * 重大疾病保险金组别1
	 */
	public static final BigDecimal LIAB_ID_3005 = new BigDecimal("3005");
	/**
	 * 重大疾病保险金组别2
	 */
	public static final BigDecimal LIAB_ID_3007 = BigDecimal.valueOf(3007);
	/**
	 * 重大疾病保险金组别3
	 */
	public static final BigDecimal LIAB_ID_3009 = BigDecimal.valueOf(3009);
	/**
	 * 重大疾病保险金组别4
	 */
	public static final BigDecimal LIAB_ID_3011 = BigDecimal.valueOf(3011);
	/**
	 * 重大疾病保险金组别5
	 */
	public static final BigDecimal LIAB_ID_3013 = BigDecimal.valueOf(3013);
	/**
	 * 前10年关爱保险金
	 */
	public static final BigDecimal LIAB_ID_3015 = BigDecimal.valueOf(3015);
	/**
	 * 特定严重疾病保险金
	 */
	public static final BigDecimal LIAB_ID_3016 = BigDecimal.valueOf(3016);
	/**
	 * 特定重大疾病保险金
	 */
	public static final BigDecimal LIAB_ID_3023 = BigDecimal.valueOf(3023);

	/**
	 * 重大疾病保险金组别3-未满18周岁
	 */
	public static final BigDecimal LIAB_ID_3018 = BigDecimal.valueOf(3018);
	/**
	 * 重大疾病保险金组别4-未满18周岁
	 */
	public static final BigDecimal LIAB_ID_3019 = BigDecimal.valueOf(3019);

	/**
	 * 重大疾病保险金组别5-未满18周岁
	 */
	public static final BigDecimal LIAB_ID_3020 = BigDecimal.valueOf(3020);

	/**
	 * 责任编码：1001 身故保险金
	 */
	public static final BigDecimal LIAB_ID_1001 = BigDecimal.valueOf(1001);
	/**
	 * 责任编码：1106 养老年金
	 */
	public static final BigDecimal LIAB_ID_1106 = BigDecimal.valueOf(1106);
	/**
	 * 责任编码：3001 重大疾病保险金
	 */
	public static final BigDecimal LIAB_ID_3001 = BigDecimal.valueOf(3001);
	/**
	 * 责任编码：7094_门急诊手术医疗费用保险金
	 */
	public static final BigDecimal LIAB_ID_7094 = BigDecimal.valueOf(7094);
	/**
	 * 责任编码：7093_门急诊非手术医疗费用保险金
	 */
	public static final BigDecimal LIAB_ID_7093 = BigDecimal.valueOf(7094);
	/**
	 * 责任编码：3035_成人意外伤害特定疾病关爱保险金
	 */
	public static final BigDecimal LIAB_ID_3035 = BigDecimal.valueOf(3035);
	/**
	 * 责任编码：1033_成人意外伤害身故关爱保险金
	 */
	public static final BigDecimal LIAB_ID_1033 = BigDecimal.valueOf(1033);
	/**
	 * 责任编码：3040_少儿前10年关爱保险金
	 */
	public static final BigDecimal LIAB_ID_3040 = BigDecimal.valueOf(3040);
	/**
	 * 责任编码：3042_少儿前10年关爱保险金
	 */
	public static final BigDecimal LIAB_ID_3042 = BigDecimal.valueOf(3042);
	/**
	 * 责任编码：8071_特定疾病关爱保险金
	 */
	public static final BigDecimal LIAB_ID_8071 = BigDecimal.valueOf(8071);
	/**
	 * 责任编码：3032_轻度疾病保险金
	 */
	public static final BigDecimal LIAB_ID_3032 = BigDecimal.valueOf(8070);
	/**
	 * 责任编码：3033_中度疾病保险金
	 */
	public static final BigDecimal LIAB_ID_3033 = BigDecimal.valueOf(8070);
	/**
	 * 责任编码：3034_重度疾病保险金
	 */
	public static final BigDecimal LIAB_ID_3034 = BigDecimal.valueOf(8070);
	/**
	 * 责任编码：8070_少儿特定疾病关爱保险金
	 */
	public static final BigDecimal LIAB_ID_8070 = BigDecimal.valueOf(8070);

	/**
	 * 意外伤害残疾保险金
	 */
	public static final BigDecimal LIAB_ID_2005 = new BigDecimal("2005");
	/**
	 * 意外骨折、关节脱位、关节替换保险金
	 */
	public static final BigDecimal LIAB_ID_2019 = new BigDecimal("2019");
	/**
	 * 祝寿金
	 */
	public static final BigDecimal LIAB_ID_1103 = new BigDecimal("1103");
	/**
	 * 7067_轻症癌症保险金
	 */
	public static final BigDecimal LIAB_ID_7067 = new BigDecimal("7067");
	/**
	 * 7069_轻症疾病保险金
	 */
	public static final BigDecimal LIAB_ID_7069 = new BigDecimal("7069");
	/**
	 * 7069_极早期恶性肿瘤或恶性病变保险金
	 */
	public static final BigDecimal LIAB_ID_8055 = new BigDecimal("8055");
	/**
	 * 第三方给付类型--自费报销
	 */
	public static final BigDecimal EXPENSE_REIMBURSE = new BigDecimal("7");
	/**
	 * 第三方给付类型--其他
	 */
	public static final BigDecimal EXPENSE_REIMBURSE_SIX = new BigDecimal("6");
	/**
	 * 赔案标识--个险
	 */
	public static final String PEO_CASE = "个险";
	/**
	 * 赔案标识--团险
	 */
	public static final String GROUP_CASE = "团险";
	/**
	 * 赔案标识--团险
	 */
	public static final String HOSPITAL_CODE_UNMAINTAINED = "团险";
	/**
	 * 365天
	 */
	public static final int DAY_365 = 365;
	/**
	 * 首次新增可选责任日期
	 */
	public static final String ADD_OPTLIAB_DATE = "addOptLiabDate";
	/**
	 * 出险保单年度可选责任的新增保额
	 */
	public static final String TOP_UP_SA = "topUpSA";
	/**
	 * 是否在首次年金领取日之前
	 */
	public static final String IS_STAR_ANNUITY = "isStarAnnuity";
	/**
	 * 上次分红日
	 */
	public static final String LAST_ALLOCATE_DATE = "lastAllocateDate";
	/**
	 * 年金领取频率
	 */
	public static final String IS_PAY_FREQ = "isPayFreq";
	/**
	 * 出险日后的现金红利
	 */
	public static final String CASH_BONUS = "cashBonus";
	/**
	 * 出险日后是否现金领取过现金红利
	 */
	public static final String CASH_FLAG = "cashFlag";
	/**
	 * 出险日后的现金红利补发利息
	 */
	public static final String REISSUE_INTEREST = "reissueInterest";
	/**
	 * 是否计算出险日后的进入附加账户的现金红利和补发利息
	 */
	public static final String CASH_AND_INTEREST = "cashAndInterest";
	/**
	 * 下一个计价日单位价格是否公布
	 */
	public static final String IS_PUBISH_PRICES = "isPubishPrices";
	/**
	 * 整年度分红累计红利保额
	 */
	public static final String BONUS_SA = "bonusSa";
	/**
	 * 出险日前部分领取的金额
	 */
	public static final String PG_AMOUNT_FLAG = "pgAmountFlag";
	/**
	 * 出险日后账户部分领取金额
	 */
	public static final String PG_TOTAL_PREM = "PGTotalPrem";
	/**
	 * 出险日后生存金年金红利挂起解挂取消功能
	 */
	public static final String LOCK_PREM_ARAP = "LockPremArap";
	/**
	 * 特定手术疾病给付-费用类型
	 */
	public static final String SURGERY_TYPE_ONE = "1";
	/**
	 * 特定手术疾病给付-费用类型
	 */
	public static final String SURGERY_TYPE_TWO = "2";
	/**
	 * 特定手术疾病给付-费用类型
	 */
	public static final String SURGERY_TYPE_THREE = "3";
	/**
	 * 预付的费用类型
	 */
	public static final String P005140100 = "P005140100";
	/**
	 * 理算特殊判断的费用类型代码
	 */
	public static final String ITEM_CODE_ONE = "FT017";
	/**
	 * 理算特殊判断的费用类型代码
	 */
	public static final String ITEM_CODE_TWO = "CC007";

	/**
	 * 理算特殊判断的费用类型代码：FT012_癌症保险金
	 */
	public static final String ITEM_CODE_THREE = "FT012";
	/**
	 * 理算特殊判断的费用类型代码
	 */
	public static final String ITEM_CODE_FOUR = "FT013";
	/**
	 * 理算特殊判断的费用类型代码
	 */
	public static final String ITEM_CODE_FIVE = "FT014";
	/**
	 * 理算特殊判断的费用类型代码
	 */
	public static final String ITEM_CODE_SIX = "FT015";
	/**
	 * 理算特殊判断的费用类型代码
	 */
	public static final String ITEM_CODE_SEVEN = "FT016";
	/**
	 * 理算特殊判断的费用类型代码：FT020_轻症癌症保险金
	 */
	public static final String ITEM_CODE_EIGHT = "FT020";
	/**
	 * 理算特殊判断的特定参数类型代码：TD006、TD007、TD008、TD009、TD025、TD026
	 */
	public static final List<String> SURGERY_CODE_LIST_FIVEONETWO = new ArrayList<String>();
	/**
	 * 静态值
	 */
	static {
		SURGERY_CODE_LIST_FIVEONETWO.add("TD006");
		SURGERY_CODE_LIST_FIVEONETWO.add("TD007");
		SURGERY_CODE_LIST_FIVEONETWO.add("TD008");
		SURGERY_CODE_LIST_FIVEONETWO.add("TD009");
		SURGERY_CODE_LIST_FIVEONETWO.add("TD025");
		SURGERY_CODE_LIST_FIVEONETWO.add("TD026");
	}
	/**
	 * list集合
	 */
	public static final List<String> SURGERY_CODE_LIST_SEVENSIXTWO = new ArrayList<String>();
	static {
		SURGERY_CODE_LIST_SEVENSIXTWO.add("FT012");
		SURGERY_CODE_LIST_SEVENSIXTWO.add("FT013");
		SURGERY_CODE_LIST_SEVENSIXTWO.add("FT014");
		SURGERY_CODE_LIST_SEVENSIXTWO.add("FT015");
		SURGERY_CODE_LIST_SEVENSIXTWO.add("FT016");
	}
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINEFOURSIX = "00946000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_ONE = "00528000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWO = "00515000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THREE = "00512000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FOUR = "00513000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FIVE = "00555000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_SIX = "00556000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_SEVEN = "00786000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHT = "00521000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINE = "00892000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TEN = "00369001";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_ELEVEN = "00369004";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWELVE = "00552000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTEEN = "00553000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FOURTEEN = "00558000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FIFTEEN = "00144000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_SIXTEEN = "00118000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_SEVETEEN = "00378000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTEEN = "00369002";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINETEEN = "00369003";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTY = "00369004";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYONE = "00547000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYTWO = "00782000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYEIGHT = "00731000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYTHREE = "00732000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYFOUR = "00562000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYFIVEZERO = "00563000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FIVESEVENDOUBLE = "00577000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FIVESEVEN = "00570000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYFIVE = "00563100";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYSIX = "00567000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYSEVEN = "00564000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYFIVEO = "00565000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FOURZERO = "00540000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FOURONE = "00541000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_TWENTYNIGHT = "00779200";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTY = "00779400";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTYONE = "00954000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FIVEONE = "00955000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTYTWO = "00431000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTYTHREE = "00955000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINEFIVESIX = "00956000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINEFIVENINE = "00959000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINESIXZERO = "00960000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINESIXONE = "00961000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINESIXTWO = "00962000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTYFOURZERO = "00958000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTYFOUR = "00958100";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTYFIVE = "00963000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTYSIX = "00907000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_THIRTYSEVEN = "00909000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINEFIVEZERO = "00950000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_SEVENSIXTWO = "00762000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_ONEONEEIGHTONE = "00118100";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINESEVENEIGHT = "00978000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINESEVENENINE = "00979000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTTHREEONE = "00831000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_SEVENSIXTHREETWO = "00763200";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTTHREESIX = "00836000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINETWOFOUR = "00924000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINETWOFIVE = "00925000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTFOURFOUR = "00844000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTFIVESIX = "00856000";
	/**
	 * 859000产品
	 */
	public static final BigDecimal PRODUCT_CODE_STD_859000 = new BigDecimal("859000");
	/**
	 * 869000产品
	 */
	public static final BigDecimal PRODUCT_CODE_STD_869000 = new BigDecimal("869000");
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTFIVESEVEN = "00857000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTFIVEFOUR = "00854000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTFIVEFIVE = "00855000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_NINETWOEIGHT = "00928000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTSEVENSEVEN = "00877000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_FOURFIVEFIVE = "00455000";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_BUSI_PRODCODE_EIGHTSEVENEIGHT = "00878000";

	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String CLM_PRODCODE_EIGHTTHREESIX = "836000";

	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String POLICY_HOLDER_ONE = "1";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String INSRUED_LIST_TWO = "2";
	/**
	 * 理算特殊处理的产品编码
	 */
	public static final String INSRUED_HOLDER_THREE = "3";

	/**
	 * 参数类型：分期给付类型码值：分次给付
	 */
	public static final String INSTAL_TYPE_PAYMENT = "01";
	/**
	 * 参数类型：分期给付类型码值：分次豁免
	 */
	public static final String INSTAL_TYPE_WAIVER = "02";
	/**
	 * 参数类型：分期给付类型名称：分次给付
	 */
	public static final String INSTAL_TYPE_PAYMENT_NAME = "分次给付";
	/**
	 * 参数类型：分期给付类型名称：分次豁免
	 */
	public static final String INSTAL_TYPE_WAIVER_NAME = "分次豁免";

	/**
	 * 赔付金领取方式:1-一次统一给付
	 */
	public static final String GET_MODE_CODE_ONE = "1";

	/**
	 * 赔付金领取方式:2-按年金方式领取
	 */
	public static final String GET_MODE_CODE_TWO = "2";

	/**
	 * 赔付金领取方式:3-分期支付
	 */
	public static final String GET_MODE_CODE_THREE = "3";

	/**
	 * 万能险
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 记账类型集合
	 */
	public static final List getBusiProdCodeList() {
		// 万能险
		List<String> busiProdCodeList = new ArrayList<String>();
		busiProdCodeList.add("00913000");
		busiProdCodeList.add("00914000");
		busiProdCodeList.add("00902000");
		busiProdCodeList.add("00905000");
		busiProdCodeList.add("00907000");
		busiProdCodeList.add("00912100");
		busiProdCodeList.add("00909000");
		busiProdCodeList.add("00910100");
		busiProdCodeList.add("00903000");
		busiProdCodeList.add("00904000");
		busiProdCodeList.add("00915000");
		busiProdCodeList.add("00916000");
		busiProdCodeList.add("00917000");
		busiProdCodeList.add("00918000");
		busiProdCodeList.add("00920000");
		busiProdCodeList.add("00921000");
		busiProdCodeList.add("00919000");
		return busiProdCodeList;
	}

	/**
	 * 投连万能险
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 记账类型集合
	 */
	public static final List getBusiProdCode9List() {
		List<String> busiProdCode9List = new ArrayList<String>(); // 投连万能险
		busiProdCode9List.add("00913000");
		busiProdCode9List.add("00914000");
		busiProdCode9List.add("00902000");
		busiProdCode9List.add("00905000");
		busiProdCode9List.add("00907000");
		busiProdCode9List.add("00912100");
		busiProdCode9List.add("00909000");
		busiProdCode9List.add("00910100");
		busiProdCode9List.add("00903000");
		busiProdCode9List.add("00904000");
		busiProdCode9List.add("00915000");
		busiProdCode9List.add("00916000");
		busiProdCode9List.add("00917000");
		busiProdCode9List.add("00918000");
		busiProdCode9List.add("00920000");
		busiProdCode9List.add("00921000");
		busiProdCode9List.add("00919000");
		busiProdCode9List.add("00890000");
		busiProdCode9List.add("00888000");
		busiProdCode9List.add("00892000");
		return busiProdCode9List;
	}

	/**
	 * 非保险合同
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 记账类型集合
	 */
	public static final List getBusiProdCode10List() {

		List<String> busiProdCode10List = new ArrayList<String>(); // 非保险合同
		busiProdCode10List.add("00913000");
		busiProdCode10List.add("00914000");
		busiProdCode10List.add("00902000");
		busiProdCode10List.add("00905000");
		busiProdCode10List.add("00907000");
		busiProdCode10List.add("00912100");
		busiProdCode10List.add("00909000");
		busiProdCode10List.add("00910100");
		busiProdCode10List.add("00903000");
		busiProdCode10List.add("00904000");
		busiProdCode10List.add("00915000");
		busiProdCode10List.add("00916000");
		busiProdCode10List.add("00917000");
		busiProdCode10List.add("00918000");
		busiProdCode10List.add("00920000");
		busiProdCode10List.add("00921000");
		busiProdCode10List.add("00919000");
		busiProdCode10List.add("00890000");
		busiProdCode10List.add("00888000");
		busiProdCode10List.add("00892000");
		return busiProdCode10List;
	}

	/**
	 * 需要查询账户价值的费用类型list
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 记账类型集合
	 */
	public static final List getFeeTypeList() {
		// 询账户价值的费用类型list
		List<String> feeTypeList = new ArrayList<String>();
		feeTypeList.add("P005010000"); // @invalid 理赔金
		feeTypeList.add("G005070300"); // @invalid 扣回保单贷款本金（无账户价值）1
		feeTypeList.add("G005080300"); // @invalid 扣回保单贷款利息（无账户价值）2
		feeTypeList.add("G005090300"); // @invalid 扣回自垫保费（无账户价值）3
		feeTypeList.add("G005100300"); // @invalid 扣回自垫利息（无账户价值）4
		feeTypeList.add("G005080301"); // @invalid 扣回保单贷款利息增值税（无账户价值）5
		feeTypeList.add("G005100301"); // @invalid 扣回自垫利息增值税（无账户价值）6
		feeTypeList.add("G005380300"); // @invalid 理赔追回年金（无账户价值）
		feeTypeList.add("G005060000"); // @invalid 扣回欠缴保费（无账户价值）
		return feeTypeList;
	}

	/**
	 * 万能险结算组别
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return set集合
	 */
	public static final Set settlementGroupOne() {
		// 万能险结算组别
		Set<String> settlementGroup = new HashSet<String>();
		// @invalid 自垫及贷款
		settlementGroup.add("3|4|5|6");
		// @invalid 保费相关
		settlementGroup.add("7");
		settlementGroup.add("8");
		// @invalid 终了红利
		settlementGroup.add("9");
		// @invalid 利差返还
		settlementGroup.add("10");
		// @invalid 风险保费及保单管理费
		settlementGroup.add("11|12");
		// @invalid 保证领取年金
		settlementGroup.add("13");
		// @invalid 追回费用
		settlementGroup.add("14|15|21");
		// @invalid 终了计算利息
		settlementGroup.add("22");
		// @invalid 现金红利及利息
		settlementGroup.add("19|20");
		return settlementGroup;
	}

	/**
	 * 结算组别2--责任组终止
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return set集合
	 */
	public static final Set settlementGroupTwo() {
		Set<String> settlementGroup = new HashSet<String>();
		// 终了红利
		settlementGroup.add("9");
		return settlementGroup;
	}

	/**
	 * 判断是否自动发起慰问 1:是
	 */
	public static final String IS_COMFORT = "1";

	/**
	 * 业务员属性
	 */
	public static final String AGENT_FLAG = "2";

	/**
	 * 是否发送短信 Y 发送
	 */
	public static final String IS_MSG_Y = "Y";

	/**
	 * 分期提前抽档天数 公共参数配置code
	 */
	public static final String INSTALMENT_DAY_CODE = "2";

	/**
	 * 每小时3600秒
	 */
	public static final int SECONDS_POR_HOUR = 3600;
	/**
	 * 每年12个月
	 */
	public static final int MONTH_POR_YEAR = 12;
	/**
	 * 一个季度3个月
	 */
	public static final int MONTH_POR_QUARTER = 3;
	/**
	 * 半年6个月
	 */
	public static final int MONTH_HALF_YEAR = 6;
	/**
	 * 一年四个季度
	 */
	public static final int QUARTER_YEAR = 4;
	/**
	 * 一天的分钟数
	 */
	public static final int HOUR_PER_DAY = 1440;
	/**
	 * 每小时3600000秒
	 */
	public static final int MILLISECONDS_POR_HOUR = 3600000;
	/**
	 * 每分钟60秒
	 */
	public static final int SECONDS_POR_MINS = 60;
	/**
	 * 数字星期天
	 */
	public static final int SUNDAY_NUM = 7;
	/**
	 * 1990年
	 */
	public static final int EARLIST_DATE = 1990;
	/**
	 * 权限级别14(审核)
	 */
	public static final int PREM_LEAVE_AUDIT = 14;

	/**
	 * 权限级别10
	 */
	public static final int PREM_LEAVE_TEN = 10;
	/**
	 * 权限级别30(审批)
	 */
	public static final int PREM_LEAVE_APPROVE = 30;

	/**
	 * 审批疑难权限级别10
	 */
	public static final int DIFFICULT_LEAVE_TEN = 10;

	/**
	 * 总公司机构86
	 */
	public static final int PARENT_ORG_CODE = 86;
	/**
	 * 日期年截取位置 substring（0,4）
	 */
	public static final int SUBSTRING_DATE_YEAR = 4;
	/**
	 * 日期年月日截取位置 substring（0,10）
	 */
	public static final int SUBSTRING_DATE = 10;
	/**
	 * 日期时分秒截取位置 substring（10,18）
	 */
	public static final int SUBSTRING_TIME = 18;
	/**
	 * 日期时分秒中间有空格截取位置 substring（10,19）
	 */
	public static final int SUBSTRING_TIME2 = 19;

	/**
	 * 数字0.5
	 */
	public static final BigDecimal ZERO_POINT_FIVE = BigDecimal.valueOf(0.5);
	/**
	 * 短信通知模板代码 80
	 */
	public static final BigDecimal MESSAGE_MODE_EIGHTY = BigDecimal.valueOf(80);

	/**
	 * 短信通知模板代码 115
	 */
	public static final BigDecimal MESSAGE_MODE_ONEHUNDREDFIFTEEN = BigDecimal.valueOf(115);
	/**
	 * 短信通知模板代码 123
	 */
	public static final BigDecimal MESSAGE_MODE_ONEHUNDREDTWENTYTHREE = BigDecimal.valueOf(123);

	/**
	 * 短信通知模板代码 201 报案确认
	 */
	public static final BigDecimal MESSAGE_MODE_TWOZEROONE = BigDecimal.valueOf(201);
	/**
	 * 短信通知模板代码202 报案确认 给业务员发送邮件
	 */
	public static final BigDecimal MESSAGE_MODE_TWOZEROTWO = BigDecimal.valueOf(202);

	/**
	 * 短信通知模板代码 251 理赔关怀任务时限提醒
	 */
	public static final BigDecimal MESSAGE_MODE_TWOFIVEONE = BigDecimal.valueOf(251);

	/**
	 * 短信通知模板代码 261 理赔关怀任务时限提醒
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSIXTYONE = BigDecimal.valueOf(261);

	/**
	 * 短信通知模板代码 263
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSIXTYTHREE = BigDecimal.valueOf(263);
	/**
	 * 短信通知模板代码 264 理赔关怀任务提醒
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSIXTYFOUR = BigDecimal.valueOf(264);

	/**
	 * 短信通知模板代码 211 签收确认节点
	 */
	public static final BigDecimal MESSAGE_MODE_TWOELEVEN = BigDecimal.valueOf(211);

	/**
	 * 短信通知模板代码 212 签收确认节点
	 */
	public static final BigDecimal MESSAGE_MODE_TWOTWELVE = BigDecimal.valueOf(212);
	/**
	 * 短信通知模板代码 214 签收确认受托人节点
	 */
	public static final BigDecimal MESSAGE_MODE_TWOTWFOUR = BigDecimal.valueOf(214);
	/**
	 * 短信通知模板代码 213 优质客户
	 */
	public static final BigDecimal MESSAGE_MODE_TWOTHREE = BigDecimal.valueOf(213);

	/**
	 * 短信通知模板代码277 调查完成
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSEVENTYSEVEN = BigDecimal.valueOf(277);

	/**
	 * 短信通知模板代码278 理赔二核任务完成
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSEVENTYEIGHT = BigDecimal.valueOf(278);

	/**
	 * 短信通知模板代码 279 审核任务提醒
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSEVENTYNINE = BigDecimal.valueOf(279);

	/**
	 * 短信通知模板代码 280 审核节点提醒
	 */
	public static final BigDecimal MESSAGE_MODE_TWOEIGHTY = BigDecimal.valueOf(280);

	/**
	 * 短信通知模板代码 272 事中质检不通过
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSEVENTYTWO = BigDecimal.valueOf(272);

	/**
	 * 短信通知模板代码 273 个人质检任务提醒
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSEVENTYTHREE = BigDecimal.valueOf(273);

	/**
	 * 短信通知模板代码 274 告知赔案完成回退
	 */
	public static final BigDecimal MESSAGE_MODE_TWOSEVENTYFOUR = BigDecimal.valueOf(274);

	/**
	 * 短信通知模板代码241 审批
	 */
	public static final BigDecimal MESSAGE_MODE_TWOFOURTYONE = BigDecimal.valueOf(241);
	/**
	 * 短信通知模板代码242 审批
	 */
	public static final BigDecimal MESSAGE_MODE_TWOFOURTYTWO = BigDecimal.valueOf(242);
	/**
	 * 短信通知模板代码243 审批
	 */
	public static final BigDecimal MESSAGE_MODE_TWOFOURTYTHREE = BigDecimal.valueOf(243);
	/**
	 * 短信通知模板代码244 审批支付成功（单人申请人）
	 */
	public static final BigDecimal MESSAGE_MODE_TWOFOURTYFOUR = BigDecimal.valueOf(244);

	/**
	 * 短信通知模板代码245 审批支付成功（赔付金额为0）
	 */
	public static final BigDecimal MESSAGE_MODE_TWOFOURTYFIVE = BigDecimal.valueOf(245);

	/**
	 * 短信通知模板代码282 审批
	 */
	public static final BigDecimal MESSAGE_MODE_TWOEIGTHTYTWO = BigDecimal.valueOf(282);

	/**
	 * 短信通知模板代码281 问题件任务提醒
	 */
	public static final BigDecimal MESSAGE_MODE_TWOEIGTHTYONE = BigDecimal.valueOf(281);

	/**
	 * 短信通知模板代码222 给受托人发送信息
	 */
	public static final BigDecimal MESSAGE_MODE_TWOTWENTYTWO = BigDecimal.valueOf(222);
	/**
	 * 短信通知模板代码221 给申请人发送信息
	 */
	public static final BigDecimal MESSAGE_MODE_TWOTWENTYONE = BigDecimal.valueOf(221);
	/**
	 * 邮件通知模板代码 295 发起调查时给调查机构对应的人员发送邮件通知
	 */
	public static final BigDecimal MESSAGE_MODE_TWONINEFIVE = BigDecimal.valueOf(295);

	/**
	 * 邮件通知模板代码 296 审核任务进入简易案件共享池时给改赔案签收机构对应的消息配置人员发送邮件
	 */
	public static final BigDecimal MESSAGE_MODE_TWONINESIX = BigDecimal.valueOf(296);

	/**
	 * 邮件通知模板代码 297 点击【审核确认】后，有拒付的给发送拒付审批通知邮件给消息配置人员
	 */
	public static final BigDecimal MESSAGE_MODE_TWONINESEVEN = BigDecimal.valueOf(297);

	/**
	 * 邮件通知模板代码 298 收付费返回转账失败结果时给签收机构对应消息配合人员发送邮件
	 */
	public static final BigDecimal MESSAGE_MODE_TWONINEEIGHT = BigDecimal.valueOf(298);

	/**
	 * 邮件通知模板代码 299 审核、审批管理的共享池、个人任务列表中的“理赔时效”达到配置的天数后时给签收机构对应消息配合人员发送邮件
	 */
	public static final BigDecimal MESSAGE_MODE_TWONINENINE = BigDecimal.valueOf(299);
	/**
	 * 邮件通知模板代码 402 拒付通知书寄送提醒
	 */
	public static final BigDecimal MESSAGE_MODE_FOURZEROTWO = BigDecimal.valueOf(402);
	/**
	 * 邮件通知模板代码 409 直连直赔报案
	 */
	public static final BigDecimal MESSAGE_MODE_FOURZERONINE = BigDecimal.valueOf(409);
	/**
	 * 邮件通知模板代码 410 直连直赔问题件
	 */
	public static final BigDecimal MESSAGE_MODE_FOURONEZERO = BigDecimal.valueOf(410);
	/**
	 * 邮件通知模板代码 411 直赔案件报案数据异常提醒
	 */
	public static final BigDecimal MESSAGE_MODE_FOURONEONE = BigDecimal.valueOf(411);
	/**
	 * 短信通知模板代码 412 直赔案件申请人消息提醒模板
	 */
	public static final BigDecimal MESSAGE_MODE_FOURONETWO = BigDecimal.valueOf(412);

	/**
	 * 短信通知模板代码 414 实名查验预警提醒模板
	 */
	public static final BigDecimal MESSAGE_MODE_FOURONEFOUR = BigDecimal.valueOf(414);

	/**
	 * 邮件通知模板taskID 409 直连直赔报案
	 */
	public static final String FOURZERONINE_TASK_ID = "0002zhlzhpba";
	/**
	 * 邮件通知模板taskID 410 直连直赔问题件
	 */
	public static final String FOURONEZERO_TASK_ID = "0002zhlzhpwtj";
	/**
	 * 邮件通知模板taskID 411 直赔案件报案数据异常提醒
	 */
	public static final String FOURONEONE_TASK_ID = "0002zhpajbasjyctx";

	/**
	 * 邮件通知模板taskID 414 实名查验预警提醒
	 */
	public static final String CHECKREALNAME_TASK_ID = "0002smcyyj";

	/**
	 * 60天
	 */
	public static final int SIXTY_DAYS = 60;
	/**
	 * 30天
	 */
	public static final int THRITY_DAYS = 30;

	/**
	 * 默认分页页数
	 */
	public static final int PAGE_SIZE_TEN = 10;
	/**
	 * 调查状态t_Survey_Status 1：已申请
	 */
	public static final BigDecimal SURVEY_STATUS_APPLY = BigDecimal.valueOf(1);

	/**
	 * 调查状态t_Survey_Status 2：完成
	 */
	public static final BigDecimal SURVEY_STATUS_FINISH = BigDecimal.valueOf(2);
	/**
	 * 调查状态t_Survey_Status 3：已撤销
	 */
	public static final BigDecimal SURVEY_STATUS_REVOKE = BigDecimal.valueOf(3);
	/**
	 * 调查状态t_Survey_Status 4：进行中
	 */
	public static final BigDecimal SURVEY_STATUS_CONDUCT = BigDecimal.valueOf(4);
	/**
	 * 调查方式t_survey_mode 01：现场查勘
	 */
	public static final String SURVEY_MODE_ONE = "01";

	/**
	 * 是否问题件：2 正常件
	 */
	public static final BigDecimal PROBLEM_CHECK_FLAG_TWO = BigDecimal.valueOf(2);
	/**
	 * 是否问题件：3 问题件
	 */
	public static final BigDecimal PROBLEM_CHECK_FLAG_THREE = BigDecimal.valueOf(3);

	/**
	 * 外包状态：0 录入失败标识
	 */
	public static final String OUT_SOURCE_FLAG_ZERO = "0";
	/**
	 * 外包状态：1 录入成功标识
	 */
	public static final String OUT_SOURCE_FLAG_ONE = "0";
	/**
	 * 外包状态：2 已回复
	 */
	public static final BigDecimal OUTSOURCE_STATUS_TWO = BigDecimal.valueOf(2);
	/**
	 * 外包状态：3 问题件（外包商问题件）
	 */
	public static final BigDecimal OUTSOURCE_STATUS_THREE = BigDecimal.valueOf(3);
	/**
	 * 外包类型:医疗1
	 */
	public static final BigDecimal OUTSOURCE_TYPE_ONE = BigDecimal.valueOf(1);
	/**
	 * 外包类型:非医疗2
	 */
	public static final BigDecimal OUTSOURCE_TYPE_TWO = BigDecimal.valueOf(2);

	/**
	 * 外包状态 4 待发送
	 */
	public static final BigDecimal OUTSOURCE_STATUS_FOUR = BigDecimal.valueOf(4);
	/**
	 * 外包案件表影像问题件状态:无 0
	 */
	public static final String IMAGE_ISSUE_STATUS_ZERO = "0";
	/**
	 * 外包案件表影像问题件状态:‘1-待处理’
	 */
	public static final String IMAGE_ISSUE_STATUS_ONE = "1";

	/**
	 * 外包商问题件 1-外包商问题件
	 */
	public static final String OUTER_ISSUEFLAG_ONE = "1";
	/**
	 * 外包商问题件 2-没有问题
	 */
	public static final String OUTER_ISSUEFLAG_TWO = "2";
	/**
	 * 问题件类型 6 影像质检不通过
	 */
	public static final String ISSUE_TYPE_SIX = "6";

	/**
	 * 日期28号
	 */
	public static final int DATE_TWENTY_EIGHT = 28;

	/**
	 * 银行账户类型 其他银行账号
	 */
	public static final BigDecimal ACCOUNTYPE_OTHER = BigDecimal.valueOf(9);
	/**
	 * 账户状态 可用
	 */
	public static final String ACCOUNT_STATUS = "1";

	/**
	 * 账户信息是否已复核通过
	 */
	public static final BigDecimal BANK_CONFIRM = BigDecimal.valueOf(1);
	/**
	 * 查询计算投连万能扣费金额 类型退保费用
	 */
	public static final int TYPE_FORE = 4;

	/**
	 * 申请人属性默认值为指定受益人:1
	 */
	public static final String APPLICANT_NATURE = "1";
	/**
	 * 利率类型
	 */
	public static final BigDecimal RATA_TYPE = BigDecimal.valueOf(23);

	/**
	 * 主管作业管理页面 任务类型 签收登记
	 */
	public static final int PAGE_TASKCODE_SIGN = 0;
	/**
	 * 主管作业管理页面 任务类型 立案登记
	 */
	public static final int PAGE_TASKCODE_REGISTER = 1;
	/**
	 * 主管作业管理页面 任务类型 审核
	 */
	public static final int PAGE_TASKCODE_AUDIT = 2;
	/**
	 * 主管作业管理页面 任务类型 审批
	 */
	public static final int PAGE_TASKCODE_APPROVE = 3;
	/**
	 * 主管作业管理页面 任务类型 预付申请
	 */
	public static final int PAGE_TASKCODE_PERPAID = 4;
	/**
	 * 主管作业管理页面 任务类型 预付复核
	 */
	public static final int PAGE_TASKCODE_PERPAID_AUDIT = 5;
	/**
	 * 主管作业管理页面 任务类型 回退申请
	 */
	public static final int PAGE_TASKCODE_FALLBACK = 6;
	/**
	 * 主管作业管理页面 任务类型 回退审核
	 */
	public static final int PAGE_TASKCODE_FALLBACK_AUDIT = 7;
	/**
	 * 主管作业管理页面 任务类型 事中质检
	 */
	public static final int PAGE_TASKCODE_QUALITY_IN = 8;
	/**
	 * 主管作业管理页面 任务类型 事后质检
	 */
	public static final int PAGE_TASKCODE_QUALITY_AFTER = 9;
	/**
	 * 主管作业管理页面 任务类型 付费变更申请
	 */
	public static final int PAGE_TASKCODE_FEE_CHANGE = 10;
	/**
	 * 主管作业管理页面 任务类型 付费变更复核
	 */
	public static final int PAGE_TASKCODE_FEE_CHANGE_AUDIT = 11;
	/**
	 * 主管作业管理页面 任务类型 补充单证问题件
	 */
	public static final int PAGE_TASKCODE_PROBLEM = 12;
	/**
	 * 主管作业管理页面 任务类型 协谈
	 */
	public static final int PAGE_TASKCODE_CHAT = 13;
	/**
	 * 主管作业管理页面 任务类型 合议
	 */
	public static final int PAGE_TASKCODE_DISCUSSION = 14;
	/**
	 * 主管作业管理页面 任务类型 立案登记复核
	 */
	public static final int PAGE_TASKCODE_REREGISTER = 15;
	/**
	 * 主管作业管理页面 任务类型 分次给付审核
	 */
	public static final int PAGE_TASKCODE_PAYAUDIT = 16;
	/**
	 * 查询作业监控信息 监控范围为全公司
	 */
	public static final String MONITOR_COMPANY = "01";
	/**
	 * 查询作业监控信息 监控范围为片区
	 */
	public static final String MONITOR_AREA = "02";
	/**
	 * 查询作业监控信息 监控范围为机构
	 */
	public static final String MONITOR_ORG = "03";
	/**
	 * 查询作业监控信息 监控范围为人员
	 */
	public static final String MONITOR_PERSON = "04";

	/**
	 * 查询作业监控信息 监控周期为自定义时间
	 */
	public static final String MONITOR_PERIOD_SALF = "04";

	/**
	 * 查询作业监控信息 监控周期非自定义时间 按月
	 */
	public static final String MONITOR_MONTH = "01";
	/**
	 * 查询作业监控信息 监控周期非自定义时间 按周
	 */
	public static final String MONITOR_WEEK = "02";
	/**
	 * 查询作业监控信息 监控周期非自定义时间 按天
	 */
	public static final String MONITOR_DAY = "03";

	/**
	 * 理赔系统 性别 男
	 */
	public static final BigDecimal SEX_MALE = BigDecimal.valueOf(1);
	/**
	 * 理赔系统 性别 女
	 */
	public static final BigDecimal SEX_FEMALE = BigDecimal.valueOf(2);
	/**
	 * 理赔系统 性别 未知
	 */
	public static final BigDecimal SEX_OTHER = BigDecimal.valueOf(9);

	/**
	 * 其他系统 性别 男
	 */
	public static final String SEX_MALE_OTHER_SYS = "0";
	/**
	 * 其他系统 性别 女
	 */
	public static final String SEX_FEMALE_OTHER_SYS = "1";
	/**
	 * 其他系统 性别 未知
	 */
	public static final String SEX_OTHER_OTHER_SYS = "2";
	/**
	 * 判断是否为无限次分期 9999或是999
	 */
	public static final BigDecimal INDEFINITE_STAGING_ONE = BigDecimal.valueOf(9999);
	/**
	 * 判断是否为无限次分期 9999或是999
	 */
	public static final BigDecimal INDEFINITE_STAGING_TWO = BigDecimal.valueOf(999);
	/**
	 * 支付计划页面未转年金提示
	 */
	public static final BigDecimal PAY_PLAN_MSG_TWO = BigDecimal.valueOf(2);
	/**
	 * 支付计划页面未分期提示
	 */
	public static final BigDecimal PAY_PLAN_MSG_THREE = BigDecimal.valueOf(3);
	/**
	 * 支付计划页面未分期提示
	 */
	public static final BigDecimal PAY_PLAN_MSG_FORE = BigDecimal.valueOf(4);
	/**
	 * 支付计划页面 险种循环扣减最大次数
	 */
	public static final int MAX_NUM = 30;
	/**
	 * 审核校验提示信息ID：赔案有未完成的调查任务，请完成未决任务后再进行合同处理。
	 */
	public static final String AUDIT_TASK_ID_TWO = "2";

	/**
	 * 审核校验提示信息ID：赔案有未完成的协谈任务，请完成未决任务后再进行合同处理。
	 */
	public static final String AUDIT_TASK_ID_THREE = "3";
	/**
	 * 审核校验提示信息ID：赔案有未完成的合议任务，请完成未决任务后再进行合同处理。
	 */
	public static final String AUDIT_TASK_ID_FOUR = "4";
	/**
	 * 审核校验提示信息ID：赔案有未完成的签报任务，请完成未决任务后再进行合同处理。
	 */
	public static final String AUDIT_TASK_ID_FIVE = "5";
	/**
	 * 审核校验提示信息ID：险种存在欠缴保费，需要补交保费后，才能继续进行合同处理。
	 */
	public static final String AUDIT_TASK_ID_SIXTEEN = "16";

	/**
	 * 签报任务 5拒绝终止
	 */
	public static final String SIGN_TASK_FIVE = "5";
	/**
	 * 签报任务 6正常终止
	 */
	public static final String SIGN_TASK_SIX = "6";
	/**
	 * 签报任务7撤销终止
	 */
	public static final String SIGN_TASK_SEVEN = "7";
	/**
	 * 转办任务天数 距离当前日期
	 */
	public static final int TURN_DAYS = 7;
	/**
	 * 转办任务 7拒绝终止
	 */
	public static final String TURN_TASK_SEVEN = "7";
	/**
	 * 转办任务 8终止
	 */
	public static final String TURN_TASK_EIGHT = "8";
	/**
	 * 转办任务 9撤销终止
	 */
	public static final String TURN_TASK_NIE = "9";
	/**
	 * 转办任务 10回复终止-已处理
	 */
	public static final String TURN_TASK_TEN = "10";
	/**
	 * 转办任务 11回复终止-未处理
	 */
	public static final String TURN_TASK_ELVEN = "11";
	/**
	 * 转办场景 3 满期金追回
	 */
	public static final String TURN_SCENE_THREE = "3";

	/**
	 * DECIOSN_CODE 标准体
	 */
	public static final String DECIOSN_CODE_STANDARD = "10";
	/**
	 * DECIOSN_CODE 限额
	 */
	public static final String DECIOSN_CODE_QUOTA = "32";

	/**
	 * DECIOSN_CODE 特别约定
	 */
	public static final String DECIOSN_CODE_SPECIAL = "33";

	/**
	 * DECIOSN_CODE 延期
	 */
	public static final String DECIOSN_CODE_DELAY = "40";
	/**
	 * DECIOSN_CODE 拒保
	 */
	public static final String DECIOSN_CODE_DECLINATURE = "50";

	/**
	 * 核保结论原因选项 ： T_DECISION_REASON_TYPE 1
	 */
	public static final BigDecimal DECISION_REASON_TYPE_ONE = BigDecimal.valueOf(1);

	/**
	 * 核保结论原因选项 ：T_DECISION_REASON_TYPE 2
	 */
	public static final BigDecimal DECISION_REASON_TYPE_TWO = BigDecimal.valueOf(2);

	/**
	 * 核保结论原因选项 ：T_DECISION_REASON_TYPE 3
	 */
	public static final BigDecimal DECISION_REASON_TYPE_THREE = BigDecimal.valueOf(3);

	/**
	 * 核保结论原因选项 ：T_DECISION_REASON_TYPE 4
	 */
	public static final BigDecimal DECISION_REASON_TYPE_FOUR = BigDecimal.valueOf(4);
	/**
	 * 核保结论原因选项 ：T_DECISION_REASON_TYPE 5
	 */
	public static final BigDecimal DECISION_REASON_TYPE_FIVE = BigDecimal.valueOf(5);
	/**
	 * biaozhun原因选项 ：T_DECISION_REASON_TYPE 6
	 */
	public static final BigDecimal DECISION_REASON_TYPE_SIX = BigDecimal.valueOf(6);
	/**
	 * 核保结论原因选项 ：T_DECISION_REASON_TYPE 7
	 */
	public static final BigDecimal DECISION_REASON_TYPE_SEVEN = BigDecimal.valueOf(7);
	/**
	 * 核保结论原因选项 ： T_DECISION_REASON_TYPE 8
	 */
	public static final BigDecimal DECISION_REASON_TYPE_EIGHT = BigDecimal.valueOf(8);
	/**
	 * 电话回访 用户角色 1 ：申请人
	 */
	public static final String REVISIT_USER_TYPE_ONE = "1";
	/**
	 * 电话回访 用户角色 2 ：出险人
	 */
	public static final String REVISIT_USER_TYPE_TWO = "2";

	/**
	 * 电话回访 用户角色 3 ：报案人
	 */
	public static final String REVISIT_USER_TYPE_THREE = "3";
	/**
	 * 案件标识：普通案件 t_case_level
	 */
	public static final BigDecimal CASE_FLAG_NOMAL = BigDecimal.valueOf(1);
	/**
	 * 案件标识： -诉讼案件
	 */
	public static final BigDecimal REVISIT_USER_SIGN = BigDecimal.valueOf(2);
	/**
	 * 案件标识：-疑难案件
	 */
	public static final BigDecimal CASE_FLAG_DIFFI = BigDecimal.valueOf(3);
	/**
	 * 案件标识：-简易案件
	 */
	public static final BigDecimal CASE_FLAG_EAZY = BigDecimal.valueOf(4);
	/**
	 * 案件标识码值转化：-普通案件
	 */
	public static final String CASE_FLAG_NOMAL_STR = "11";
	/**
	 * 案件标识码值转化： -诉讼案件
	 */
	public static final String REVISIT_USER_SIGN_STR = "12";
	/**
	 * 案件标识码值转化：-疑难案件
	 */
	public static final String CASE_FLAG_DIFFI_STR = "14";

	/**
	 * 申请任务类型：-简易审核
	 */
	public static final String APPLY_TASK_TYPE_ONE = "easyAudit";
	/**
	 * 申请任务类型：-普通审核
	 */
	public static final String APPLY_TASK_TYPE_TWO = "audit";
	/**
	 * 申请任务类型：-普通审批
	 */
	public static final String APPLY_TASK_TYPE_THREE = "approve";
	/**
	 * 申请任务类型：-疑难审批
	 */
	public static final String APPLY_TASK_TYPE_FOUR = "difficultApprove";

	/**
	 * 本次出险时间距离5631首次生效日在3年以内(含3年)，且出险客户3年内名下所有医疗险（包括其他医疗险和5631、563）累计赔付金额（含本次出险的赔付金额）大于5万元
	 */
	public static final BigDecimal MONEY_FIVTY_THOUSAND = BigDecimal.valueOf(50000);
	/**
	 * 睡眠时间
	 */
	public static final int SIXTY_THOUSAND = 60000;

	/**
	 * 质检状态：-待质检
	 */
	public static final BigDecimal QC_STATUS_ZERO = BigDecimal.valueOf(0);
	/**
	 * 质检状态：-质检中
	 */
	public static final BigDecimal QC_STATUS_ONE = BigDecimal.valueOf(1);
	/**
	 * 质检状态：-质检完成
	 */
	public static final BigDecimal QC_STATUS_TWO = BigDecimal.valueOf(2);

	/**
	 * 复核状态：待处理/待复核
	 */
	public static final BigDecimal REAUDIT_STATUS = BigDecimal.valueOf(4);

	/**
	 * 复核状态：复核修改
	 */
	public static final BigDecimal REAUDIT_STATUS_ONE = BigDecimal.valueOf(1);

	/**
	 * 复核状态：复核通过
	 */
	public static final BigDecimal REAUDIT_STATUS_TWO = BigDecimal.valueOf(2);

	/**
	 * 复核状态：复核终止
	 */
	public static final BigDecimal REAUDIT_STATUS_THREE = BigDecimal.valueOf(3);
	/**
	 * 险种：短期险 60002
	 */
	public static final BigDecimal BUSI_PRODUCT_SHORT = BigDecimal.valueOf(60002);
	/**
	 * 险种：长期险 60001
	 */
	public static final BigDecimal BUSI_PRODUCT_LONG = BigDecimal.valueOf(60001);
	/**
	 * 等待打印返回 设置线程时间 10秒 10000毫秒
	 */
	public static final int SEND_MAIL_SLEEP = 10000;
	/**
	 * 发送邮件控制方法超时 设置延迟获取 3秒 3000毫秒
	 */
	public static final int SEND_MAIL_SLEEP_THREE = 3000;
	/**
	 * 发送带附件的邮件 设置延迟获取 2秒 2000毫秒
	 */
	public static final int SEND_MAIL_SLEEP_GET = 2000;
	/**
	 * 代理人：五星级A3
	 */
	public static final String AGENT_LEVEL_FIVE_ATHREE = "3";
	/**
	 * 代理人等级：三星级-A1等级
	 */
	public static final String AGENT_LEVEL_THREE_LEVEL = "A1";
	/**
	 * 代理人等级：四星级-A2等级
	 */
	public static final String AGENT_LEVEL_FOUR_LEVEL = "A2";
	/**
	 * 代理人等级：五星级-A3等级
	 */
	public static final String AGENT_LEVEL_FIVE_LEVEL = "A3";
	/**
	 * 多倍保障重大疾病保险前10年关爱保险金需与身故或重大疾病保险金同时给付，请予以调整 提示标识
	 */
	public static final BigDecimal IS_TEN_YEARS = BigDecimal.valueOf(10);

	/**
	 * Lunar工具类参数1
	 */
	public static final int LUNAR_INFO_ONE = 0x8000;
	/**
	 * Lunar工具类参数2
	 */
	public static final int LUNAR_INFO_TWO = 0x8;
	/**
	 * Lunar工具类参数3
	 */
	public static final int LUNAR_INFO_THREE = 0x10000;
	/**
	 * Lunar工具类参数4
	 */
	public static final long LUNAR_INFO_FOUR = 86400000L;
	/**
	 * Lunar工具类参数5
	 */
	public static final int LUNAR_INFO_FIVE = 0xf;

	/**
	 * @Fields COMMA : 中括号："]"
	 */
	public static final String BIGCOMMA = "]";
	/**
	 * @Fields BRACES : 大括号："}"
	 */
	public static final String BRACES = "}";

	/**
	 * base64字符串转化成图片 功能使用
	 */
	public static final int BASE64_USE = 256;
	/**
	 * 收付费相关：场景编码-00501 （理赔金）
	 */
	public static final String SCENE_CODE_ONE = "00501";
	/**
	 * 收付费相关：场景编码-00501 （理赔金-预付款）
	 */
	public static final String SCENE_CODE_TEN = "00510";
	/**
	 * 收付费相关：场景编码-00502 （多交保费退还）
	 */
	public static final String SCENE_CODE_TWO = "00502";
	/**
	 * 收付费相关：场景编码-00503 （未满期保费）
	 */
	public static final String SCENE_CODE_THREE = "00503";
	/**
	 * 收付费相关：场景编码-00505 （保证年金一次性领取）
	 */
	public static final String SCENE_CODE_FIVE = "00505";

	/**
	 * 收付费相关：场景编码-00507 （理赔分期给付）
	 */
	public static final String SCENE_CODE_SEVEN = "00507";

	/**
	 * 收付费相关：场景编码-00519 （资产管理费）追回
	 */
	public static final String SCENE_CODE_NINTEEN = "00519";
	/**
	 * 收付费相关：场景编码-00520 （资产管理费）扣除
	 */
	public static final String SCENE_CODE_TWENTY = "00520";
	/**
	 * 收付费相关：场景编码-00512 （理赔退费）
	 */
	public static final String SCENE_CODE_TWELVE = "00512";
	/**
	 * 收付费相关：场景编码-00513 （理赔退保）
	 */
	public static final String SCENE_CODE_THIRTEEN = "00513";

	/**
	 * 收付费相关：场景编码-00370 资产管理费 -投连险
	 */
	public static final String SCENE_CODE_THIRTYSEVEN = "00370";

	/**
	 * 收付费相关（T_ARAP_SOURCE_TABLE）：数据来源-067001 理算表
	 */
	public static final String ARAP_SOURCE_TABLE_LIAB = "067001";

	/**
	 * 收付费相关（T_ARAP_SOURCE_TABLE）：数据来源-067002 结算表
	 */
	public static final String ARAP_SOURCE_TABLE_ADJUST = "067002";

	/**
	 * 时间跨度3000年
	 */
	public static final int TIME_SPAN_3000 = 3000;
	/**
	 * 短期险
	 */
	public static final BigDecimal PERIOD_TYPE_60002 = new BigDecimal(60002);
	/**
	 * 144产品
	 */
	public static final String PRODUCT_CODE_STD_144 = "144";
	/**
	 * 118产品
	 */
	public static final String PRODUCT_CODE_STD_118 = "118";
	/**
	 * 859产品
	 */
	public static final String PRODUCT_CODE_STD_859 = "859";
	/**
	 * 住院天数30
	 */
	public static final int BILL_DAY_30 = 30;
	/**
	 * 月数
	 */
	public static final int MONTH_SIX = 6;
	/**
	 * 月数
	 */
	public static final int MONTH_THREE = 3;
	/**
	 * 月数
	 */
	public static final int MONTH_TWELVE = 12;
	/**
	 * 18岁
	 */
	public static final int AGE_EIGHTEEN = 18;
//@invalid     /* calcParamCfg的key和value含义:
//@invalid     3_累加器累计区间类型:                                6_产品保障期  || 5_单个保单年度
//@invalid     2_责任延长期（天）                              (天数)
//@invalid     5_一次住院最长天数                              (天数)
//@invalid     4_住院间隔长度（天）                             (天数)
//@invalid     8_累加器费用计算类型                             11_门诊||10_住院||12_住院和门诊
//@invalid     1_事故日后多少天内                              (天数)
//@invalid     11_保险期间届满时治疗最长至事故日起第多少日止                (天数)
//@invalid     6_既往赔付天数关联责任代码                          (责任代码)
//@invalid     7_既往赔付金额关联责任代码                          (责任代码)
//@invalid     9_累加器费用计算区间                             2_单个住院期间
//@invalid     13_每次住院期间前后各多少天内                            (天数)
//@invalid     12_每次住院不超过多少元                               (金额)
//@invalid     14_此责任赔付不参与到所定义的累加器中进行累计                (累加器id)
//@invalid     15_意外伤害延续至本合同到期日后多少日内的治疗                (天数)
//@invalid     16_保险期间届满时住院治疗最长至事故日起第多少日止          (天数)
//@invalid     17_保险期间届满时门诊治疗最长至事故日起第多少日止          (天数)
//@invalid      * */
	/**
	 * 累加器累计区间类型
	 */
	public static final String STATIC_PARAM_KEY3 = "3";
	/**
	 * 责任延长期（天）
	 */
	public static final String STATIC_PARAM_KEY2 = "2";
	/**
	 * 5_一次住院最长天数 (天数)
	 */
	public static final String STATIC_PARAM_KEY5 = "5";
	/**
	 * 4_住院间隔长度（天） (天数)
	 */
	public static final String STATIC_PARAM_KEY4 = "4";
	/**
	 * 8_累加器费用计算类型
	 */
	public static final String STATIC_PARAM_KEY8 = "8";
	/**
	 * 28_门诊治疗类型
	 */
	public static final String STATIC_PARAM_KEY28 = "28";
	/**
	 * 1_事故日后多少天内
	 */
	public static final String STATIC_PARAM_KEY1 = "1";
	/**
	 * 24_合并住院是否考虑同一事故
	 */
	public static final String STATIC_PARAM_KEY24 = "24";
	/**
	 * 31_前后门急诊考虑同一事故
	 */
	public static final String STATIC_PARAM_KEY31 = "31";
	/**
	 * 32_是否为中度疾病责任
	 */
	public static final String STATIC_PARAM_KEY32 = "32";
	/**
	 * 29_前后门急诊考虑同一医院
	 */
	public static final String STATIC_PARAM_KEY29 = "29";
	/**
	 * 11_保险期间届满时治疗最长至事故日起第多少日止
	 */
	public static final String STATIC_PARAM_KEY11 = "11";
	/**
	 * 6_既往赔付关联责任代码
	 */
	public static final String STATIC_PARAM_KEY6 = "6";
	/**
	 * 7_既往赔付金额关联责任代码
	 */
	public static final String STATIC_PARAM_KEY7 = "7";
	/**
	 * 9_累加器费用计算区间
	 */
	public static final String STATIC_PARAM_KEY9 = "9";
	/**
	 * 13_每次住院期间前后各多少天内
	 */
	public static final String STATIC_PARAM_KEY13 = "13";
	/**
	 * 12_每次住院不超过多少元
	 */
	public static final String STATIC_PARAM_KEY12 = "12";
	/**
	 * 14_此责任赔付不参与到所定义的累加器中进行累计
	 */
	public static final String STATIC_PARAM_KEY14 = "14";
	/**
	 * 15_意外伤害延续至本合同到期日后多少日内的治疗
	 */
	public static final String STATIC_PARAM_KEY15 = "15";
	/**
	 * 16_保险期间届满时住院治疗最长至事故日起第多少日止
	 */
	public static final String STATIC_PARAM_KEY16 = "16";
	/**
	 * 17_保险期间届满时门诊治疗最长至事故日起第多少日止
	 */
	public static final String STATIC_PARAM_KEY17 = "17";
	/**
	 * 18_是否为津贴责任
	 */
	public static final String STATIC_PARAM_KEY18 = "18";
	/**
	 * 22_分次给付后保单状态
	 */
	public static final String STATIC_PARAM_KEY22 = "22";
	/**
	 * 23_分次给付后保单状态
	 */
	public static final String STATIC_PARAM_KEY23 = "23";
	/**
	 * 25_本险种是否癌症确诊金已赔付
	 */
	public static final String STATIC_PARAM_KEY25 = "25";
	/**
	 * 26_每次住院期间前多少天内
	 */
	public static final String STATIC_PARAM_KEY26 = "26";
	/**
	 * 27_每次住院期间后多少天内
	 */
	public static final String STATIC_PARAM_KEY27 = "27";
	/**
	 * 30_本险种是否恶性肿瘤确诊金已赔付
	 */
	public static final String STATIC_PARAM_KEY30 = "30";
	/**
	 * 33_与本责任互斥的责任代码
	 */
	public static final String STATIC_PARAM_KEY33 = "33";
	/**
	 * 34_确诊日后责任延长期（计算公式）
	 */
	public static final String STATIC_PARAM_KEY34 = "34";
	/**
	 * 35_确诊日前多少天（含确诊日）内或确诊日后
	 */
	public static final String STATIC_PARAM_KEY35 = "35";
	/**
	 * 36_是否因同一疾病产生的住院费用
	 */
	public static final String STATIC_PARAM_KEY36 = "36";
	/**
	 * 37_是否在事故保单年度内进行匹配
	 */
	public static final String STATIC_PARAM_KEY37 = "37";
	/**
	 * 6_产品保障期
	 */
	public static final String STATIC_PARAM_KEY3_VALUE6 = "6";
	/**
	 * 5_单个保单年度
	 */
	public static final String STATIC_PARAM_KEY3_VALUE5 = "5";
	/**
	 * 11_门诊
	 */
	public static final String STATIC_PARAM_KEY8_VALUE11 = "11";
	/**
	 * 10_住院
	 */
	public static final String STATIC_PARAM_KEY8_VALUE10 = "10";
	/**
	 * 12_住院和门诊
	 */
	public static final String STATIC_PARAM_KEY8_VALUE12 = "12";
	/**
	 * 2_单个住院期间
	 */
	public static final String STATIC_PARAM_KEY9_VALUE2 = "2";
	/**
	 * 3_单次门诊
	 */
	public static final String STATIC_PARAM_KEY9_VALUE3 = "3";
	/**
	 * 2_单个住院期间累计
	 */
	public static final String CONDITION_TYPE2 = "2";
	/**
	 * 3_单次门诊累计
	 */
	public static final String CONDITION_TYPE3 = "3";
	/**
	 * 1_单个事故内累计
	 */
	public static final String CONDITION_TYPE1 = "1";
	/**
	 * 5_单个保单年度内累计
	 */
	public static final String CONDITION_TYPE5 = "5";
	/**
	 * 6_产品保障期内累计
	 */
	public static final String CONDITION_TYPE6 = "6";
	/**
	 * 7_生效日后365天的倍数累计
	 */
	public static final String CONDITION_TYPE7 = "7";
	/**
	 * 9_出险时间距事故日期3年（3*365日）内
	 */
	public static final String CONDITION_TYPE9 = "9";
	/**
	 * 公式参数-医疗费用
	 */
	public static final String PARAM_ID70 = "70";
	/**
	 * 公式参数-住院杂项费及手术费
	 */
	public static final String PARAM_ID86 = "86";
	/**
	 * 公式参数-合理医疗费
	 */
	public static final String PARAM_ID59 = "59";
	/**
	 * 公式参数-住院天数
	 */
	public static final String PARAM_ID69 = "69";
	/**
	 * 公式参数-本责任既往免赔额
	 */
	public static final String PARAM_ID145 = "145";
	/**
	 * 公式参数-社保支付
	 */
	public static final String PARAM_ID60 = "60";
	/**
	 * 公式参数-第三方支付
	 */
	public static final String PARAM_ID58 = "58";
	/**
	 * 公式参数-单保单年度内本责任账单总和(540/541用)
	 */
	public static final String PARAM_ID151 = "151";
	/**
	 * 公式参数-本责任对应的自费医疗费用
	 */
	public static final String PARAM_ID154 = "154";
	/**
	 * 公式参数-住院自费医疗费用
	 */
	public static final String PARAM_ID155 = "155";
	/**
	 * 公式参数-其它途径给付的住院自费医疗费用
	 */
	public static final String PARAM_ID156 = "156";
	/**
	 * 公式参数-本次住院或门诊是否已赔付社保或第三方
	 */
	public static final String PARAM_ID169 = "169";
	/**
	 * 公式参数-单保单年度内本责任社保第三方总和(540/541用)
	 */
	public static final String PARAM_ID175 = "175";
	/**
	 * 公式参数-癌症实际住院天数
	 */
	public static final String PARAM_ID174 = "174";
	/**
	 * 公式参数-三方给付项下给付类型（除2-农合支付,9城镇居民）的金额
	 */
	public static final String PARAM_ID165 = "165";
	/**
	 * 公式参数-确诊日后的住院天数
	 */
	public static final String PARAM_ID177 = "177";
	/**
	 * 公式参数-确诊日前的住院天数
	 */
	public static final String PARAM_ID178 = "178";
	/**
	 * 公式参数-确诊日后的第三方费用
	 */
	public static final String PARAM_ID179 = "179";
	/**
	 * 公式参数-确诊日前的第三方费用
	 */
	public static final String PARAM_ID180 = "180";
	/**
	 * 公式参数-确诊日后的社保费用
	 */
	public static final String PARAM_ID181 = "181";
	/**
	 * 公式参数-确诊日前的社保费用
	 */
	public static final String PARAM_ID182 = "182";
	/**
	 * 公式参数-确诊日后的合理医疗费用
	 */
	public static final String PARAM_ID183 = "183";
	/**
	 * 公式参数-确诊日前的合理医疗费用
	 */
	public static final String PARAM_ID184 = "184";
	/**
	 * 参数分类：时间段
	 */
	public static final String PARA_TYPE_SIX = "06";
	/**
	 * 邮件发送者地址
	 */
	public static final String SENDER_ADDRESS = "<EMAIL>";
	/**
	 * 险种销售渠道：个人营销
	 */
	public static final String SALES_CHANNEL_ONE = "01";
	/**
	 * 险种销售渠道：团体渠道
	 */
	public static final String SALES_CHANNEL_TWO = "02";
	/**
	 * 险种销售渠道：银行代理
	 */
	public static final String SALES_CHANNEL_THREE = "03";
	/**
	 * 险种销售渠道：收费员渠道
	 */
	public static final String SALES_CHANNEL_FOUR = "04";
	/**
	 * 险种销售渠道：电话直销
	 */
	public static final String SALES_CHANNEL_FIVE = "05";
	/**
	 * 险种销售渠道：银代续期
	 */
	public static final String SALES_CHANNEL_SIX = "06";
	/**
	 * 险种销售渠道：续期督导
	 */
	public static final String SALES_CHANNEL_SEVEN = "07";
	/**
	 * 险种销售渠道：财富管理
	 */
	public static final String SALES_CHANNEL_EIGHT = "08";
	/**
	 * 险种销售渠道：至尊理财
	 */
	public static final String SALES_CHANNEL_NINE = "09";
	/**
	 * 险种销售渠道：网站销售
	 */
	public static final String SALES_CHANNEL_TEN = "10";
	/**
	 * 险种销售渠道：柜面直销
	 */
	public static final String SALES_CHANNEL_ELEVEN = "11";
	/**
	 * 险种销售渠道：客户管理
	 */
	public static final String SALES_CHANNEL_TWELVE = "12";
	/**
	 * 续保和保证续保标识：不可续保
	 */
	public static final String RENEW_OPTION_ZERO = "0";
	/**
	 * 续保和保证续保标识：可续保（不可保证续保）
	 */
	public static final String RENEW_OPTION_ONE = "1";
	/**
	 * 续保和保证续保标识：可保证续保
	 */
	public static final String RENEW_OPTION_TWO = "2";

	/**
	 * 报案方式：柜台报案——3
	 */
	public static final BigDecimal REPORT_MODE_COUNTER = new BigDecimal(3);

	/**
	 * 理赔阶段代码：报案——01
	 */
	public static final String CLAIM_STAGE_REPORT = "01";
	/**
	 * 理赔阶段代码：签收——02
	 */
	public static final String CLAIM_STAGE_SIGN = "02";

	/**
	 * 阶段代码，1：为报案阶段
	 */
	public static final String REPORT_ONE = "01";

	/**
	 * 是否扣减欠缴费用标识，0：否
	 */
	public static final String IS_DEDUCT_ZERRO = "0";

	/**
	 * 是否扣减欠缴费用标识，1：是
	 */
	public static final String IS_DEDUCT_ONE = "1";

	/**
	 * 系统用户ID
	 */
	public static final BigDecimal SURVEY_USER = new BigDecimal("11111111");
	/**
	 * 二级管理机构
	 */
	public static final String SECOND_ORGAN_GRADE = "02";
	/**
	 * 保单终止原因为：超限终止
	 */
	public static final String END_CASE_FOURTEEN = "14";

	/**
	 * 批单-合同处理批注
	 */
	public static final String REVOICE_CONTRACT_SIXTEEN = "CLM_00016";

	/**
	 * 批单-保费豁免批注
	 */
	public static final String REVOICE_CONTRACT_SEVEENTEEN = "CLM_00017";

	/**
	 * 批单-理赔给付批注
	 */
	public static final String REVOICE_CONTRACT_EIGHTTEEN = "CLM_00018";

	/**
	 * 批单-预付保险金给付批注
	 */
	public static final String REVOICE_CONTRACT_FIVETEEN = "CLM_00015";

	/**
	 * 通知书类型 批单-合同处理批注
	 */
	public static final String REVOICE_CONTRACT_SIXTEEN_NAME = "批单-合同处理批注";

	/**
	 * 通知书类型 批单-保费豁免批注
	 */
	public static final String REVOICE_CONTRACT_SEVEENTEEN_NAME = "批单-保费豁免批注";

	/**
	 * 通知书类型 批单-理赔给付批注
	 */
	public static final String REVOICE_CONTRACT_EIGHTTEEN_NAME = " 批单-理赔给付批注";

	/**
	 * 通知书类型 批单-预付保险金给付批注
	 */
	public static final String REVOICE_CONTRACT_FIVETEEN_NAME = "批单-预付保险金给付批注";

	/**
	 * 高风险客户
	 */
	public static final String CUSTOMER_RISK_LEVEL = "A";
	/**
	 * 风险测评问卷打印
	 */
	public static final String CLM_ZERRO_THIRTEEN = "CLM_00013";

	/**
	 * 身故注销人员延期验真类型
	 */
	public static final String CLM_LOGOUT_VERIFY_TYPE = "2";

	/**
	 * 身故注销人员延期验真返回状态
	 */
	public static final String CLM_LOGOUT_VERIFY_STATUS = "注销状态";

	/**
	 * 身故注销人员延期验真结果
	 */
	public static final String CLM_LOGOUT_VERIFY_RESULT0 = "已注销";

	/**
	 * 身故注销人员延期验真结果
	 */
	public static final String CLM_LOGOUT_VERIFY_RESULT1 = "未注销";

	/**
	 * 身故注销人员延期验真人工核实代码
	 */
	public static final String CLM_LOGOUT_VERIFY_CHECKUSER = "系统批处理";

	/**
	 * 身故注销人员延期验真结果
	 */
	public static final String CLM_LOGOUT_VERIFY_RESULT2 = "不能确定客户状态";

	/**
	 * 身故注销人员延期验真结果
	 */
	public static final String CLM_LOGOUT_VERIFY_RESULT3 = "库中无此号";

	/**
	 * 身故注销人员延期验真结果
	 */
	public static final String CLM_LOGOUT_VERIFY_RESULT4 = "身份证和姓名信息数据不合法";

	/**
	 * 身故注销人员延期验真结果
	 */
	public static final String CLM_LOGOUT_VERIFY_RESULT5 = "客户信息验真总开关已关闭，不允许验真。";

	/**
	 * 身故注销人员延期验真人工核实结果，表示系统批处理
	 */
	public static final String CLM_LOGOUT_VERIFY_CHECKRESULT3 = "3";

	/**
	 * 身故注销人员延期校验清单
	 */
	public static final String CLM_LOGOUT_VERIFY_LIST = "延期校验清单";

	/**
	 * 理赔黑名单的权限比较
	 */
	public static final int ZERRO_SEVEN_BLACK = 07;
	/**
	 * 审批权限截取4
	 */
	public static final int BLACK_FOUR = 4;
	/**
	 * 审批权限截取6
	 */
	public static final int BLACK_SIX = 6;
	/**
	 * 身故注销人员延期校验清单
	 */
	public static final BigDecimal CLAIM_BLACK_FLAG = new BigDecimal(1);
	/**
	 * 审批疑难案件权限1
	 */
	public static final String CLM_AC_ONE = "CLM-AC01";
	/**
	 * 审批疑难案件权限2
	 */
	public static final String CLM_AC_TWO = "CLM-AC02";
	/**
	 * 审批疑难案件权限3
	 */
	public static final String CLM_AC_THREE = "CLM-AC03";
	/**
	 * 审批疑难案件权限4
	 */
	public static final String CLM_AC_FOUR = "CLM-AC04";
	/**
	 * 审批疑难案件权限5
	 */
	public static final String CLM_AC_FIVE = "CLM-AC05";
	/**
	 * 审批疑难案件权限6
	 */
	public static final String CLM_AC_SIX = "CLM-AC06";
	/**
	 * 审批疑难案件权限7
	 */
	public static final String CLM_AC_SEVEN = "CLM-AC07";
	/**
	 * 审批疑难案件权限8
	 */
	public static final String CLM_AC_EIGHT = "CLM-AC08";
	/**
	 * 审批疑难案件权限9
	 */
	public static final String CLM_AC_NINE = "CLM-AC09";
	/**
	 * 审批疑难案件权限10
	 */
	public static final String CLM_AC_TEN = "CLM-AC010";
//@invalid   /*//疑难案件金额判断-0。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_ZERRO = new BigDecimal(0);
//@invalid   //疑难案件金额判断-5。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_FIVE = new BigDecimal(50000);
//@invalid   //疑难案件金额判断-10。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_TEN = new BigDecimal(100000);
//@invalid   //疑难案件金额判断-15。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_FIFTEEN = new BigDecimal(150000);
//@invalid   //疑难案件金额判断-20。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_TWENTY = new BigDecimal(200000);
//@invalid   //疑难案件金额判断-30。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_THIRTY = new BigDecimal(300000);
//@invalid   //疑难案件金额判断-50。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_FIFTY = new BigDecimal(500000);
//@invalid   //疑难案件金额判断-80。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_EIGHTY = new BigDecimal(800000);
//@invalid   //疑难案件金额判断-100。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_ONE_HUNDRED = new BigDecimal(1000000);
//@invalid   //疑难案件金额判断-200。
//@invalid   public static final BigDecimal APPROVE_PERMISSION_TWO_HUNDRED = new BigDecimal(2000000);*/
//@invalid   //审批结论 - 不通过（2）
	/**
	 * @Fields APPROVE_DECISION_CODE_TWO : 审批结论不通过
	 */
	public static final BigDecimal APPROVE_DECISION_CODE_TWO = new BigDecimal(2);

	/**
	 * 递交渠道 - 续保转投
	 */
	public static final BigDecimal SUBMIT_CHANNEL_NINE = new BigDecimal("9");

	/**
	 * 案件状态转换
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param newcaseStatus 新核心案件状态
	 * @return 老核心案件状态
	 */
	public static String caseStatusMarray(String newcaseStatus) {
		String oldCaseStatus = "";
		if (StringUtils.isNotBlank(newcaseStatus)) {
			// 案件状态转换
			if ("10".equals(newcaseStatus)) {
				oldCaseStatus = "10";// @invalid 报案
			} else if ("20".equals(newcaseStatus) || "21".equals(newcaseStatus)) {
				oldCaseStatus = "15";// @invalid 受理
			} else if ("30".equals(newcaseStatus) || "31".equals(newcaseStatus)) {
				oldCaseStatus = "20";// @invalid 立案
			} else if ("60".equals(newcaseStatus) || "61".equals(newcaseStatus)) {
				oldCaseStatus = "30";// @invalid 审核
			} else if ("70".equals(newcaseStatus) || "71".equals(newcaseStatus)) {
				oldCaseStatus = "40";// @invalid 审批
			} else if ("80".equals(newcaseStatus)) {
				oldCaseStatus = "50";// @invalid 结案
			} else if ("90".equals(newcaseStatus)) {
				oldCaseStatus = "70";// @invalid 关闭
			}
			return oldCaseStatus;
		} else {
			return "";
		}
	}

	/**
	 * 案件标识
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param newcaseFlag 新核心案件状态
	 * @return 老核心案件状态
	 */
	public static String caseFlagMarray(String newcaseFlag) {
		String oldCaseFlag = "";
		if (StringUtils.isNotBlank(newcaseFlag)) {
			// 判断理赔类型
			if ("1".equals(newcaseFlag)) {
				oldCaseFlag = "11";// @invalid 普通案件
			} else if ("2".equals(newcaseFlag)) {
				oldCaseFlag = "12";// @invalid 诉讼案件
			} else if ("3".equals(newcaseFlag)) {
				oldCaseFlag = "14";// @invalid 疑难案件
			} else if ("4".equals(newcaseFlag)) {
				oldCaseFlag = "01";// @invalid 简易案件
			} else if ("5".equals(newcaseFlag)) {
				oldCaseFlag = "13";// @invalid 申诉案件
			} else if ("6".equals(newcaseFlag)) {
				oldCaseFlag = "11";// @invalid 单人审核案件
			}
			return oldCaseFlag;
		} else {
			return "";
		}
	}

	/**
	 * 出险原因+理赔类型 转换老核心
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param accReason 出险原因
	 * @param claimType 理赔类型
	 * @return 转换后结果
	 */
	public static String claimTypeMarray(String accReason, String claimType) {
		String oldReasonType = "";
		// 判断出险原因
		if ("1".equals(accReason)) { // 疾病
			oldReasonType = oldReasonType + "2";
		} else if ("2".equals(accReason)) { // 意外
			oldReasonType = oldReasonType + "1";
		}
		// 判断理赔类型
		if (ClaimConstant.CLAIM_TYPE_ONG.equals(claimType)) { // 身故
			oldReasonType = oldReasonType + "02";
		} else if (ClaimConstant.CLAIM_TYPE_TWO.equals(claimType)) { // 伤残
			oldReasonType = oldReasonType + "01";
		} else if (ClaimConstant.CLAIM_TYPE_THREE.equals(claimType)) { // 重大疾病
			oldReasonType = oldReasonType + "04";
		} else if (ClaimConstant.CLAIM_TYPE_FOUR.equals(claimType)) { // 高残
			oldReasonType = oldReasonType + "03";
		} else if (ClaimConstant.CLAIM_TYPE_SIX.equals(claimType)) { // 一般失能
			oldReasonType = oldReasonType + "06";
		} else if (ClaimConstant.CLAIM_TYPE_SEVEN.equals(claimType)) { // 重度失能
			oldReasonType = oldReasonType + "07";
		} else if (ClaimConstant.CLAIM_TYPE_EIGHT.equals(claimType)) { // 医疗
			oldReasonType = oldReasonType + "00";
		} else if (ClaimConstant.CLAIM_TYPE_TEN.equals(claimType)) { // 特种疾病
			oldReasonType = oldReasonType + "05";
		} else if (ClaimConstant.CLAIM_TYPE_ELEVEN.equals(claimType)) { // 豁免
			oldReasonType = oldReasonType + "09";
		}
		return oldReasonType;
	}

	/**
	 * 寿险
	 */
	public static final String PRODUCT_CATEGORY_30001 = "30001";
	/**
	 * 健康险
	 */
	public static final String PRODUCT_CATEGORY_30003 = "30003";

	/**
	 * @Fields PRODUCT_CATEGORY_30004 : 年金保险
	 */
	public static final String PRODUCT_CATEGORY_30004 = "30004";

	/**
	 * ----------收付费费用类型-------
	 */

	/**
	 * 退保费用
	 */
	public static final String FEE_CODE_G005290000 = "G005290000";
	/**
	 * 预付款应收
	 */
	public static final String FEE_CODE_G005140200 = "G005140200";
	/**
	 * 退还账户价值（不扣除退保费用）
	 */
	public static final String FEE_CODE_P005260000 = "P005260000";
	/**
	 * 扣回自垫保费（无账户价值）
	 */
	public static final String FEE_CODE_G005090300 = "G005090300";
	/**
	 * 扣回自垫保费（账户价值）
	 */
	public static final String FEE_CODE_G005090100 = "G005090100";
	/**
	 * 扣回自垫保费（超账户价值）
	 */
	public static final String FEE_CODE_G005090200 = "G005090200";
	/**
	 * 扣回自垫利息（无账户价值）
	 */
	public static final String FEE_CODE_G005100300 = "G005100300";
	/**
	 * 扣回自垫利息（账户价值）
	 */
	public static final String FEE_CODE_G005100100 = "G005100100";
	/**
	 * 扣回自垫利息（超账户价值）
	 */
	public static final String FEE_CODE_G005100200 = "G005100200";
	/**
	 * 扣回保单贷款本金（无账户价值）
	 */
	public static final String FEE_CODE_G005070300 = "G005070300";
	/**
	 * 扣回保单贷款本金（账户价值）
	 */
	public static final String FEE_CODE_G005070100 = "G005070100";
	/**
	 * 扣回保单贷款本金（超账户价值）
	 */
	public static final String FEE_CODE_G005070200 = "G005070200";
	/**
	 * 扣回保单贷款利息（无账户价值）
	 */
	public static final String FEE_CODE_G005080300 = "G005080300";
	/**
	 * 扣回保单贷款利息（账户价值）
	 */
	public static final String FEE_CODE_G005080100 = "G005080100";
	/**
	 * 扣回保单贷款利息（超账户价值）
	 */
	public static final String FEE_CODE_G005080200 = "G005080200";
	/**
	 * 扣回欠缴保费
	 */
	public static final String FEE_CODE_G005060000 = "G005060000";
	/**
	 * 扣回欠缴保费(账户价值)
	 */
	public static final String FEE_CODE_G005060100 = "G005060100";
	/**
	 * 扣回欠缴保费（超账户价值）
	 */
	public static final String FEE_CODE_G005060200 = "G005060200";
	/**
	 * 退还多交保费（续期）
	 */
	public static final String FEE_CODE_P005160200 = "P005160200";
	/**
	 * 理赔金（终了红利）
	 */
	public static final String FEE_CODE_P005020200 = "P005020200";
	/**
	 * 利差本金
	 */
	public static final String FEE_CODE_P005050000 = "P005050000";
	/**
	 * 风险保费（首期）
	 */
	public static final String FEE_CODE_T003140100 = "T003140100";
	/**
	 * 风险保费（续期）
	 */
	public static final String FEE_CODE_T003140200 = "T003140200";
	/**
	 * 保单管理费
	 */
	public static final String FEE_CODE_T003040000 = "T003040000";
	/**
	 * 保证年金一次性领取
	 */
	public static final String FEE_CODE_P005120000 = "P005120000";
	/**
	 * 理赔追回年金（无账户价值)
	 */
	public static final String FEE_CODE_G005380300 = "G005380300";
	/**
	 * 理赔追回年金（账户价值）
	 */
	public static final String FEE_CODE_G005380100 = "G005380100";
	/**
	 * 理赔追回年金（超账户价值）
	 */
	public static final String FEE_CODE_G005380200 = "G005380200";
	/**
	 * 理赔追回年金部分 改为已实付
	 */
	public static final String FEE_CODE_P005470200 = "P005470200";
	/**
	 * 理赔追回年金 已实付
	 */
	public static final String FEE_CODE_P005510000 = "P005510000";
	/**
	 * 理赔追回满期金部分
	 */
	public static final String FEE_CODE_P005480200 = "P005480200";
	/**
	 * 理赔退费（首期）
	 */
	public static final String FEE_CODE_P005180100 = "P005180100";
	/**
	 * 理赔退费（续期）
	 */
	public static final String FEE_CODE_P005180200 = "P005180200";
	/**
	 * 理赔退费（首期账户价值）
	 */
	public static final String FEE_CODE_P005190100 = "P005190100";
	/**
	 * 理赔退费（续期账户价值）
	 */
	public static final String FEE_CODE_P005190200 = "P005190200";
	/**
	 * 理赔退费（首期初始费用）-基本保费
	 */
	public static final String FEE_CODE_P005410100 = "P005410100";
	/**
	 * 理赔退费（首期初始费用）-额外保费
	 */
	public static final String FEE_CODE_P005410200 = "P005410200";
	/**
	 * 理赔退费（首期初始费用）-追加保费
	 */
	public static final String FEE_CODE_P005410300 = "P005410300";
	/**
	 * 理赔退费（续期初始费用）-基本保费
	 */
	public static final String FEE_CODE_P005420100 = "P005420100";
	/**
	 * 理赔退费（续期初始费用）-额外保费
	 */
	public static final String FEE_CODE_P005420200 = "P005420200";
	/**
	 * 理赔退费（续期初始费用）-追加保费
	 */
	public static final String FEE_CODE_P005420300 = "P005420300";
	/**
	 * 退回未满期保费（首期）
	 */
	public static final String FEE_CODE_P005170100 = "P005170100";
	/**
	 * 退回未满期保费（续期）
	 */
	public static final String FEE_CODE_P005170200 = "P005170200";
	/**
	 * 现金红利
	 */
	public static final String FEE_CODE_P004620000 = "P004620000";
	/**
	 * 补发红利利息
	 */
	public static final String FEE_CODE_P004630000 = "P004630000";
	/**
	 * 扣除累积生息帐户
	 */
	public static final String FEE_CODE_P005470300 = "P005470300";
	/**
	 * 理赔追回年金部分首期(从累积生息账户扣减的金额)
	 */
	public static final String FEE_CODE_P005490100 = "P005490100";
	/**
	 * 理赔追回年金部分续期(从累积生息账户扣减的金额)
	 */
	public static final String FEE_CODE_P005490200 = "P005490200";
	/**
	 * 理赔金（账户价值）
	 */
	public static final String FEE_CODE_P005030100 = "P005030100";
	/**
	 * 资产管理费
	 */
	public static final String FEE_CODE_T003060000 = "T003060000";
	/**
	 * 理赔金总额
	 */
	public static final String FEE_CODE_P005040000 = "P005040000";
	/**
	 * 理赔金（无账户价值）
	 */
	public static final String FEE_CODE_P005010000 = "P005010000";

	/**
	 * 理赔金（超账户价值）
	 */
	public static final String FEE_CODE_P005030200 = "P005030200";
	/**
	 * 预付赔款
	 */
	public static final String FEE_CODE_P005140100 = "P005140100";
	/**
	 * 延期给付利息
	 */
	public static final String FEE_CODE_P005110000 = "P005110000";
	/**
	 * 理赔二核加费
	 */
	public static final String FEE_CODE_G005130000 = "G005130000";

	/**
	 * --------------20180312 增加------------
	 */
	/**
	 * 扣回保单贷款利息增值税（无账户价值）
	 */
	public static final String FEE_CODE_G005080301 = "G005080301";
	/**
	 * 扣回自垫利息增值税（超账户价值）
	 */
	public static final String FEE_CODE_G005100201 = "G005100201";
	/**
	 * 理赔退保（扣除退保费用后的待支付的部分）
	 */
	public static final String FEE_CODE_P005280000 = "P005280000";
	/**
	 * 扣回保单贷款利息增值税（超账户价值）
	 */
	public static final String FEE_CODE_G005080201 = "G005080201";
	/**
	 * 扣回保单贷款利息增值税（账户价值）
	 */
	public static final String FEE_CODE_G005080101 = "G005080101";
	/**
	 * 扣回自垫利息增值税（无账户价值）
	 */
	public static final String FEE_CODE_G005100301 = "G005100301";
	/**
	 * 账户管理费用
	 */
	public static final String FEE_CODE_T003070000 = "T003070000";

	/**
	 * 扣回自垫利息增值税（账户价值）
	 */
	public static final String FEE_CODE_G005100101 = "G005100101";
	/**
	 * 理赔退保
	 */
	public static final String FEE_CODE_P005150000 = "P005150000";
	/**
	 * 理赔追回年金部分(未实付)
	 */
	public static final String FEE_CODE_P005470100 = "P005470100";

	/**
	 * @Fields FEE_CODE_P005390200 : 理赔退费（首期净投资）-额外保费
	 */
	public static final String FEE_CODE_P005390200 = "P005390200";

	/**
	 * 退还初始扣费（续期）-基本保费 02 付费
	 */
	public static final String FEE_CODE_P005460100 = "P005460100";
	/**
	 * 退还初始扣费（续期）-额外保费 02 付费
	 */
	public static final String FEE_CODE_P005460200 = "P005460200";
	/**
	 * 退还初始扣费（续期）-额外保费 02 付费
	 */
	public static final String FEE_CODE_NEW_P005460200 = "P005460200";
	/**
	 * 退还初始扣费（续期）-追加保费 02 付费
	 */
	public static final String FEE_CODE_P005460300 = "P005460300";
	/**
	 * 反记账的记账业务类型
	 */
	public static final String FEE_CODE_P005550100 = "P005550100";
	/**
	 * 追回现金红利部分（进账户）
	 */
	public static final String FEE_CODE_P005550000 = "P005550000";
	/**
	 * 追回现金红利（已实付）
	 */
	public static final String FEE_CODE_P005530000 = "P005530000";
	/**
	 * 追回补发现金红利利息（已实付
	 */
	public static final String FEE_CODE_P005540000 = "P005540000";

	/**
	 * 理赔退费拆出来的首期保单年度
	 */
	public static final BigDecimal POLICY_YEAR_ONE = new BigDecimal("1");
	/**
	 * 理赔退费拆出来的首期缴费次数
	 */
	public static final BigDecimal PAID_COUNT_ONE = new BigDecimal("1");
	/**
	 * 理赔退费 需要处理的计算项
	 */
	public static final List<String> CLAIM_REFUND_ADJUST_TYPE_LIST = new ArrayList<String>();
	static {
		CLAIM_REFUND_ADJUST_TYPE_LIST.add("18");
	}

	/**
	 * 简易自核规则状态（通过）
	 */
	public static final String CHECK_RULE_PASS_ONE = "V01";
	/**
	 * 简易自核规则状态（未通过）
	 */
	public static final String CHECK_RULE_PASS_TWO = "V02";
	/**
	 * 简易自核规则状态（未通过）
	 */
	public static final String CHECK_RULE_PASS_THREE = "V03";
	/**
	 * @Fields STATUSCODE_999 : 登录失败，返回登录页面 回退界面确认取消框
	 */
	public static final String STATUSCODE_999 = "999";
	/**
	 * 重大疾病编码：zj001
	 */
	public static final String SERIOUS_DISEASE_ZJ001 = "zj001";
	/**
	 * 轻症疾病编码：qz001
	 */
	public static final String SERIOUS_DISEASE_QZ001 = "qz001";
	/**
	 * 新冠病毒肺炎(普通型)
	 */
	public static final String SERIOUS_DISEASE_QZ065 = "qz065";
	/**
	 * 新冠病毒肺炎(重型)
	 */
	public static final String SERIOUS_DISEASE_QZ066 = "qz066";
	/**
	 * 新冠病毒肺炎出险最晚日期
	 */
	public static final String SERIOUS_DISEASE_DATE_STR = "2020-04-30";
	/**
	 * 新冠病毒肺炎名称
	 */
	public static final String SERIOUS_DISEASE_LIAB_NAME = "2019-nCoV保险金";
	/**
	 * 受益人字段是否必填立案判断标识
	 */
	public static final String BENE_REGISTER_IS_NOT_NULL = "1";
	/**
	 * 受益人字段是否必填审核判断标识
	 */
	public static final String BENE_AUDIT_IS_NOT_NULL = "2";
	/**
	 * 特约信息产品列表（先写死）
	 */
	/**
	 * 特约产品：193
	 */
	public static final String SPECIAL_PRODUCT_ONE = "193";
	/**
	 * 特约产品：507
	 */
	public static final String SPECIAL_PRODUCT_TWO = "507";
	/**
	 * 特约产品：508
	 */
	public static final String SPECIAL_PRODUCT_THREE = "508";
	/**
	 * 特约产品：388
	 */
	public static final String SPECIAL_PRODUCT_FOUR = "388";
	/**
	 * 特约产品：717
	 */
	public static final String SPECIAL_PRODUCT_FIVE = "717";
	/**
	 * 特约产品：718
	 */
	public static final String SPECIAL_PRODUCT_SIX = "718";
	/**
	 * 特约产品：918
	 */
	public static final String SPECIAL_PRODUCT_SEVEN = "918";
	/**
	 * 特约产品：527
	 */
	public static final String SPECIAL_PRODUCT_EIGHT = "527";
	/**
	 * 特约产品：528
	 */
	public static final String SPECIAL_PRODUCT_NINE = "528";
	/**
	 * 特约产品：784
	 */
	public static final String SPECIAL_PRODUCT_TEN = "784";
	/**
	 * 特约产品：390
	 */
	public static final String SPECIAL_PRODUCT_ELEVEN = "390";
	/**
	 * 特约产品：391
	 */
	public static final String SPECIAL_PRODUCT_TWELVE = "391";
	/**
	 * 特约产品：183
	 */
	public static final String SPECIAL_PRODUCT_THIRTEEN = "183";
	/**
	 * 特约产品：538
	 */
	public static final String SPECIAL_PRODUCT_FOURTEEN = "538";
	/**
	 * 特约产品：425
	 */
	public static final String SPECIAL_PRODUCT_FIVTEEN = "425";
	/**
	 * 特约产品：557
	 */
	public static final String SPECIAL_PRODUCT_SIXTEEN = "557";
	/**
	 * 特约产品：558
	 */
	public static final String SPECIAL_PRODUCT_SEVENTEEN = "558";
	/**
	 * 特约产品：577
	 */
	public static final String SPECIAL_PRODUCT_EIGHTEEN = "577";
	/**
	 * 特约产品：5631
	 */
	public static final String SPECIAL_PRODUCT_NINTEEN = "5631";
	/**
	 * 特约产品：389
	 */
	public static final String SPECIAL_PRODUCT_TWENTY = "389";
	/**
	 * 特约产品：430
	 */
	public static final String SPECIAL_PRODUCT_TWENTY_ONE = "430";
	/**
	 * 特约产品：924
	 */
	public static final String SPECIAL_PRODUCT_TWENTY_TWO = "924";

	/**
	 * --------------20180312------------
	 */
	/**
	 * 重大疾病组别1
	 */
	public static final List<String> SERIOUS_DISEASE_GROUP1 = new ArrayList<String>();
	static {
		SERIOUS_DISEASE_GROUP1.add("zj001");
	}
	/**
	 * 重大疾病组别2
	 */
	public static final List<String> SERIOUS_DISEASE_GROUP2 = new ArrayList<String>();
	static {
		SERIOUS_DISEASE_GROUP2.add("zj002");
		SERIOUS_DISEASE_GROUP2.add("zj005");
		SERIOUS_DISEASE_GROUP2.add("zj016");
		SERIOUS_DISEASE_GROUP2.add("zj021");
		SERIOUS_DISEASE_GROUP2.add("zj025");
		SERIOUS_DISEASE_GROUP2.add("zj041");
		SERIOUS_DISEASE_GROUP2.add("zj042");
		SERIOUS_DISEASE_GROUP2.add("zj049");
		SERIOUS_DISEASE_GROUP2.add("zj051");
		SERIOUS_DISEASE_GROUP2.add("zj052");
		SERIOUS_DISEASE_GROUP2.add("zj053");
		SERIOUS_DISEASE_GROUP2.add("zj060");
		SERIOUS_DISEASE_GROUP2.add("zj062");
		SERIOUS_DISEASE_GROUP2.add("zj071");
	}
	/**
	 * 重大疾病组别3
	 */
	public static final List<String> SERIOUS_DISEASE_GROUP3 = new ArrayList<String>();
	static {
		SERIOUS_DISEASE_GROUP3.add("zj003");
		SERIOUS_DISEASE_GROUP3.add("zj009");
		SERIOUS_DISEASE_GROUP3.add("zj011");
		SERIOUS_DISEASE_GROUP3.add("zj012");
		SERIOUS_DISEASE_GROUP3.add("zj045");
		SERIOUS_DISEASE_GROUP3.add("zj017");
		SERIOUS_DISEASE_GROUP3.add("zj018");
		SERIOUS_DISEASE_GROUP3.add("zj019");
		SERIOUS_DISEASE_GROUP3.add("zj022");
		SERIOUS_DISEASE_GROUP3.add("zj064");
		SERIOUS_DISEASE_GROUP3.add("zj023");
		SERIOUS_DISEASE_GROUP3.add("zj026");
		SERIOUS_DISEASE_GROUP3.add("zj029");
		SERIOUS_DISEASE_GROUP3.add("zj043");
		SERIOUS_DISEASE_GROUP3.add("zj044");
		SERIOUS_DISEASE_GROUP3.add("zj015");
		SERIOUS_DISEASE_GROUP3.add("zj054");
		SERIOUS_DISEASE_GROUP3.add("zj072");
		SERIOUS_DISEASE_GROUP3.add("zj073");
		SERIOUS_DISEASE_GROUP3.add("zj067");
		SERIOUS_DISEASE_GROUP3.add("zj066");
		SERIOUS_DISEASE_GROUP3.add("zj074");
	}
	/**
	 * 重大疾病组别4
	 */
	public static final List<String> SERIOUS_DISEASE_GROUP4 = new ArrayList<String>();
	static {
		SERIOUS_DISEASE_GROUP4.add("zj006");
		SERIOUS_DISEASE_GROUP4.add("zj007");
		SERIOUS_DISEASE_GROUP4.add("zj008");
		SERIOUS_DISEASE_GROUP4.add("zj010");
		SERIOUS_DISEASE_GROUP4.add("zj014");
		SERIOUS_DISEASE_GROUP4.add("zj030");
		SERIOUS_DISEASE_GROUP4.add("zj031");
		SERIOUS_DISEASE_GROUP4.add("zj061");
		SERIOUS_DISEASE_GROUP4.add("zj048");
		SERIOUS_DISEASE_GROUP4.add("zj032");
		SERIOUS_DISEASE_GROUP4.add("zj024");
		SERIOUS_DISEASE_GROUP4.add("zj004");
		SERIOUS_DISEASE_GROUP4.add("zj035");
		SERIOUS_DISEASE_GROUP4.add("zj046");
		SERIOUS_DISEASE_GROUP4.add("zj047");
		SERIOUS_DISEASE_GROUP4.add("zj055");
		SERIOUS_DISEASE_GROUP4.add("zj058");
		SERIOUS_DISEASE_GROUP4.add("zj028");
		SERIOUS_DISEASE_GROUP4.add("zj063");
		SERIOUS_DISEASE_GROUP4.add("zj065");
		SERIOUS_DISEASE_GROUP4.add("zj056");
		SERIOUS_DISEASE_GROUP4.add("zj069");
		SERIOUS_DISEASE_GROUP4.add("zj059");
	}
	/**
	 * 重大疾病组别5
	 */
	public static final List<String> SERIOUS_DISEASE_GROUP5 = new ArrayList<String>();
	static {
		SERIOUS_DISEASE_GROUP5.add("zj013");
		SERIOUS_DISEASE_GROUP5.add("zj020");
		SERIOUS_DISEASE_GROUP5.add("zj057");
		SERIOUS_DISEASE_GROUP5.add("zj034");
		SERIOUS_DISEASE_GROUP5.add("zj040");
		SERIOUS_DISEASE_GROUP5.add("zj050");
		SERIOUS_DISEASE_GROUP5.add("zj027");
		SERIOUS_DISEASE_GROUP5.add("zj075");
		SERIOUS_DISEASE_GROUP5.add("zj068");
		SERIOUS_DISEASE_GROUP5.add("zj076");
	}

	/**
	 * ----------收付费费用类型-------
	 */
	/**
	 * NB 承保
	 */
	public static final String FEE_SCENE_CODE_NB = "NB";
	/**
	 * NP 承保
	 */
	public static final String FEE_SCENE_CODE_NP = "NP";
	/**
	 * 2 RN 续期
	 */
	public static final String FEE_SCENE_CODE_RN = "RN";
	/**
	 * 3 RW 豁免
	 */
	public static final String FEE_SCENE_CODE_RW = "RW";
	/**
	 * 4 RA 自垫
	 */
	public static final String FEE_SCENE_CODE_RA = "RA";
	/**
	 * 5 CS 保全
	 */
	public static final String FEE_SCENE_CODE_CS = "CS";
	/**
	 * 6 CE 理赔二核加费
	 */
	public static final String FEE_SCENE_CODE_CE = "CE";
	/**
	 * 7 CD 理赔扣回欠缴保费
	 */
	public static final String FEE_SCENE_CODE_CD = "CD";
	/**
	 * 8 CR 理赔退费
	 */
	public static final String FEE_SCENE_CODE_CR = "CR";
	/**
	 * 9 RR 续期回退
	 */
	public static final String FEE_SCENE_CODE_RR = "RR";
	/**
	 * @Fields FEE_SCENE_CODE_PM : 年金满期金转保费
	 */
	public static final String FEE_SCENE_CODE_PM = "PM";
	/**
	 * @Fields FEE_SCENE_CODE_PB : 红利转保费
	 */
	public static final String FEE_SCENE_CODE_PB = "PB";

	/**
	 * PT
	 */
	public static final String FEE_SCENE_CODE_PT = "PT";

	/**
	 * 审批不通过记录表 不通过类型 审批
	 */
	public static final BigDecimal REJECT_TYPE_APPROVE = new BigDecimal(1);
	/**
	 * 审批不通过记录表 不通过类型 回退案件
	 */
	public static final BigDecimal REJECT_TYPE_ROLLBACK = new BigDecimal(2);
	/**
	 * 理赔责任匹配参数类型，0-意外细节
	 */
	public static final BigDecimal PARAM_TYPE_ZERO = BigDecimal.ZERO;
	/**
	 * 理赔责任匹配参数类型，1-特种疾病
	 */
	public static final BigDecimal PARAM_TYPE_ONE = new BigDecimal("1");
	/**
	 * 理赔责任匹配参数类型，2-特定手术
	 */
	public static final BigDecimal PARAM_TYPE_TWO = new BigDecimal("2");
	/**
	 * 理赔责任匹配参数类型，3-特定给付
	 */
	public static final BigDecimal PARAM_TYPE_THREE = new BigDecimal("3");
	/**
	 * 理赔责任匹配参数类型，4-特种费用类型
	 */
	public static final BigDecimal PARAM_TYPE_FOUR = new BigDecimal("4");
	/**
	 * 理赔责任匹配参数类型，5-医疗费用类型
	 */
	public static final BigDecimal PARAM_TYPE_FIVE = new BigDecimal("5");
	/**
	 * 理赔责任匹配参数类型，6-重大疾病
	 */
	public static final BigDecimal PARAM_TYPE_SIX = new BigDecimal("6");
	/**
	 * 理赔责任匹配参数类型，7-出险结果
	 */
	public static final BigDecimal PARAM_TYPE_SEVEN = new BigDecimal("7");
	/**
	 * 业务公共黑名单检验-冻结
	 */
	public static final String BLACK_FLAG_FREEZE = "3";

	/**
	 * 业务公共黑名单检验-疑似
	 */
	public static final String BLACK_FLAG_DOUBTFUL = "1";

	/**
	 * 环境mode
	 */
	public static final String ENVIRONMEN_MODE_ONE = "PE";
	/**
	 * 重复账单号标识 “是”
	 */
	public static final String REPEAT_NUMBER_FLAG = "1";

	/**
	 * 测试方法
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param args 控制台输入参数
	 */
	public static void main(String args[]) {
		// 测试数据
		BigDecimal bigde = new BigDecimal(2323);
		BigDecimal bigdeCopy = bigde;

		bigde = bigde.subtract(new BigDecimal(20));

	}

	/**
	 * 查看风险保额-风险类型 -16_公共交通意外险
	 */
	public static final String RISK_TYPE_SIXTEEN = "16";
	/**
	 * 查看风险保额-风险类型 -17_航空意外险
	 */
	public static final String RISK_TYPE_SEVENTEEN = "17";

	/**
	 * 查询该豁免责任是投保人豁免还是被保人豁免-被保人
	 */
	public static final String INSURED = "被保人";

	/**
	 * 查询该豁免责任是投保人豁免还是被保人豁免-投保人
	 */
	public static final String HOLDER = "投保人";
	/**
	 * 收付费挂起解挂接口操作类型：01解挂
	 */
	public static final String OPERATE_TYPE_ONE = "01";
	/**
	 * 收付费挂起解挂接口操作类型：03挂起
	 */
	public static final String OPERATE_TYPE_THREE = "03";

	/**
	 * 直连问题件类型:存在必传字段为空:1
	 */
	public static final String BACK_IS_NULL = "1";
	/**
	 * 直连问题件类型:金额冲突(柜员录入的发票总金额、已报销金额与外包商回传结果不一致):2
	 */
	public static final String MONEY_CONFLICT = "2";
	/**
	 * 直连问题件类型:(出险日期不匹配)出险日期相差5日以上:3
	 */
	public static final String CLAIM_DATE_DIFFER = "3";
	/**
	 * 直连问题件类型:(发票号重复)同一医院的发票号在核心已存在:4
	 */
	public static final String INVOICE_REPETITION = "4";
	/**
	 * 直连问题件类型:数据回传超时(收包时间-发包时间大于2小时10分钟，或系统时间-发包时间大于2小时10分钟):5
	 */
	public static final String DATA_OVERTIME = "5";
	/**
	 * 直连问题件类型:数据解析失败:6
	 */
	public static final String ANALYSIS_FAILED = "6";
	/**
	 * 直连问题件类型:疑似虚假就诊(未在就诊医院系统查到相关就诊记录):7
	 */
	public static final String FALSE_INFO = "7";

	/**
	 * 邮件通知模板代码 直连问题件邮件通知
	 */
	public static final BigDecimal MESSAGE_MODE_FOURZEROONE = BigDecimal.valueOf(401);

	/**
	 * 二核状态（已撤销）：2
	 */
	public static final String UW_REVOCATION = "2";

	/**
	 * 二核处理结论（不生效）：0
	 */
	public static final String UW_INT_VALUE = "0";

	/**
	 * 直连返回就诊类型（门诊）：11
	 */
	public static final String MEDICALTYPE_OUTPATIENT = "11";

	/**
	 * 直连返回就诊类型（住院）：21
	 */
	public static final String MEDICALTYPE_HOSPITALIZATION = "21";

	/**
	 * 直连返回就诊类型综合（住院+门诊）：31
	 */
	public static final String MEDICALTYPE_SYNTHESIS = "31";

	/**
	 * 治疗情况（门诊）：01
	 */
	public static final String CURESTATUS_OUTPATIENT = "01";

	/**
	 * 治疗情况（住院）：02
	 */
	public static final String CURESTATUS_HOSPITALIZATION = "02";

	/**
	 * 治疗情况综合（住院+门诊）：03
	 */
	public static final String CURESTATUS_SYNTHESIS = "03";

	/**
	 * 治疗类型--0-门诊
	 */
	public static final String TREATTYPE_OUTPATIENT = "0";

	/**
	 * 治疗类型--1-住院
	 */
	public static final String TREATTYPE_HOSPITALIZATION = "1";

	/**
	 * 社保/第三方类型:0-社保
	 */
	public static final BigDecimal OTHERTYPE_SOCIAL = new BigDecimal(0);

	/**
	 * 收费项目等级为---2----乙类
	 */
	public static final String ITEMLEVEL_SECOND = "2";

	/**
	 * 收费项目等级为---3----自费
	 */
	public static final String ITEMLEVEL_EXPENSE = "3";

	/**
	 * 备注类型-直连数据问题件
	 */
	public static final String MEMOTYPE_DIRECT_PROBLEM = "12";

	/**
	 * 备注选项--121--存在必传字段为空
	 */
	public static final String MEMOOPTION_IS_NOT_NULL = "121";

	/**
	 * 备注选项--122--数据解析失败
	 */
	public static final String MEMOOPTION_ANALYSIS_FAILED = "122";

	/**
	 * @Fields OTH_FEE_ITEM : 社保第三方费用明细项目：1 医疗类型
	 */
	public static final BigDecimal OTH_FEE_ITEM = new BigDecimal(1);

	/**
	 * @Fields OTH_FEE_ITEM : 社保第三方费用代码：1 社保给付
	 */
	public static final BigDecimal FEE_CODE = new BigDecimal(1);

	/**
	 * @Fields OTH_FEE_ITEM : 社保第三方费用代码：1 社保统筹支付
	 */
	public static final BigDecimal PAID_TYPE = new BigDecimal(1);

	/**
	 * 备注选项--123--金额冲突(柜员录入的发票总金额、已报销金额与外包商回传结果不一致)
	 */
	public static final String MEMOOPTION_MONEY_CONFLICT = "123";

	/**
	 * 备注选项--124--（出险日期不匹配）出险日期相差5日以上
	 */
	public static final String MEMOOPTION_CLAIM_DATE_DIFFER = "124";
	/**
	 * 备注选项--125--同一医院的发票号在核心已存在（发票号重复）
	 */
	public static final String MEMOOPTION_INVOICE_REPETITION = "125";

	/**
	 * 备注选项--126--数据回传超时(收包时间-发包时间大于2小时10分钟，或系统时间-发包时间大于2小时10分钟)
	 */
	public static final String MEMOOPTION_DATA_OVERTIME = "126";

	/**
	 * @Fields errorCode : 直连错误代码 1 失败
	 */
	public static final BigDecimal ERROR_CODE = new BigDecimal(1);

	/**
	 * 任务结论 0-通过 1-不通过 2-关闭
	 */
	public static final String TASK_RESULT_ZERO = "0";
	/**
	 * 2不进入报案共享池
	 */
	public static final String SIGN_CONCLUSION_TWO = "2";
	/**
	 * TASK_GRADE_TWO
	 */
	public static final String TASK_GRADE_TWO = "2";
	/**
	 * IMAGE_FLAG_ZERO
	 */
	public static final String IMAGE_FLAG_ZERO = "0";
	/**
	 * CLAIM_STATE_ONE
	 */
	public static final String CLAIM_STATE_ONE = "1";
	/**
	 * ADVANCE_PAY_FLAG_TWO
	 */
	public static final String ADVANCE_PAY_FLAG_TWO = "2";
	/**
	 * MATERIAL_FREE_FLAG_ZERO 柜面签收确认免材料标识为空，但是不能传空，传0代替
	 */
	public static final String MATERIAL_FREE_FLAG_ZERO = "0";
	/**
	 * QUERYTASKTYPE_SIX
	 */
	public static final String QUERYTASKTYPE_SIX = "6";

	/**
	 * 发起过补充单证
	 */
	public static final BigDecimal SUP_FLAG_YES = BigDecimal.valueOf(1);

	/**
	 * @Fields PRD_RULE_ONE :
	 *         如果主险的合同处理决定为险种终止类，但是导致终止的理赔责任非本附加险责任时，此附加险的合同处理决定自动设置为“险种终止退现金价值”
	 */
	public static final String PRD_RULE_ONE = "PRD_RULE_ONE";
	/**
	 * @Fields PRD_RULE_TWO :
	 *         主险非身故理赔，系统自动合同处理为主险终止时，此时附加险继续有效至当期保险期间末，当期期满后此附加险自动终止。
	 */
	public static final String PRD_RULE_TWO = "PRD_RULE_TWO";
	/**
	 * @Fields PRD_RULE_THREE : 主险非身故终止，自动处理时随主险即时终止，若未发生过赔付，则同时退现价，若发生过赔付，则不退还现价。
	 */
	public static final String PRD_RULE_THREE = "PRD_RULE_THREE";
	/**
	 * @Fields PRD_RULE_FOUR : 主险因各种条件进行自动理赔终止之后，其附加险种继续有效，到其保险期间届满时再终止，并可继续续保。
	 */
	public static final String PRD_RULE_FOUR = "PRD_RULE_FOUR";
	/**
	 * @Fields PRD_RULE_FIVE : 重大疾病冠状病毒挂靠轻症责任规则
	 */
	public static final String PRD_RULE_FIVE = "PRD_RULE_FIVE";
	/**
	 * @Fields PRD_RULE_SIX : 重大疾病冠状病毒挂靠特定严重疾病保险金规则
	 */
	public static final String PRD_RULE_SIX = "PRD_RULE_SIX";
	/**
	 * @Fields PRD_RULE_SEVEN : 重大疾病冠状病毒挂靠极早期恶性肿瘤或恶性病变保险金规则
	 */
	public static final String PRD_RULE_SEVEN = "PRD_RULE_SEVEN";
	/**
	 * @Fields PRD_RULE_EIGHT : 重大疾病冠状病毒挂靠特定重大疾病保险金规则
	 */
	public static final String PRD_RULE_EIGHT = "PRD_RULE_EIGHT";
	/**
	 * @Fields PRD_RULE_NINE :
	 *         客户确诊的恶性肿瘤为脑癌、白血病、胰腺癌、肾癌、前列腺癌、睾丸癌、膀胱癌、乳腺癌、宫颈癌、卵巢癌、子宫癌、肝癌、胃癌,提示信息
	 */
	public static final String PRD_RULE_NINE = "PRD_RULE_NINE";
	/**
	 * @Fields PRD_RULE_TEN : 若只录入了重疾代码，但是确诊日期无录入，或只录入了确诊日期，无重疾代码，增加提示
	 */
	public static final String PRD_RULE_TEN = "PRD_RULE_TEN";
	/**
	 * @Fields PRD_RULE_ELEVEN : 重疾代码与首次相同，但是确诊日期不同
	 */
	public static final String PRD_RULE_ELEVEN = "PRD_RULE_ELEVEN";
	/**
	 * @Fields PRD_RULE_ELEVEN : 同一事故，相同程度重疾只赔一次
	 */
	public static final String PRD_RULE_TWELVE = "PRD_RULE_TWELVE";
	/**
	 * @Fields PRD_RULE_ELEVEN : 如果本次赔案的重大疾病保险金出险日期在两次已结案赔案之间的赔案，理算时阻断
	 */
	public static final String PRD_RULE_THIRTEEN = "PRD_RULE_THIRTEEN";
	/**
	 * @Fields PRD_RULE_ELEVEN : 主险终止豁免险没有匹配到任何责任，则退豁免险现价
	 */
	public static final String PRD_RULE_FOURTEEN = "PRD_RULE_FOURTEEN";
	/**
	 * @Fields PRD_RULE_ELEVEN : 当录入zj001时，如客户确诊的恶性肿瘤为口腔恶性肿瘤、鼻咽恶性肿瘤提示
	 */
	public static final String PRD_RULE_FIFTEEN = "PRD_RULE_FIFTEEN";
	/**
	 * @Fields PRD_RULE_SIXTEEN : 既往赔付未达到保额，则不匹配出豁免责任
	 */
	public static final String PRD_RULE_SIXTEEN = "PRD_RULE_SIXTEEN";
	/**
	 * @Fields PRD_RULE_SIXTEEN : 当主险发生被保险人豁免时，附加的豁免险自动终止并退现价
	 */
	public static final String PRD_RULE_SEVENTEEN = "PRD_RULE_SEVENTEEN";

	/**
	 * @Fields UW_BACK : 二核状态编码-二核回退
	 */
	public static final String UW_BACK = "3";

	/**
	 * @Fields UW_BACK : 二核状态编码-待二核
	 */
	public static final String UW_BACK_ZERO = "0";

	/**
	 * @Fields UW_BACK : 二核状态名称-待二核
	 */
	public static final String UW_BACK_ZERO_NAME = "待二核";

	/**
	 * @Fields UW_BACK : 二核状态编码-待二核加费
	 */
	public static final String UW_BACK_FOUR = "4";

	/**
	 * @Fields UNDWER_BACK_FLAG : 核保回退
	 */
	public static final String UNDWER_BACK_FLAG = "02";

	/**
	 * @Fields LIKE_REPEAT_BILL : 理赔信息查看类型：1-疑似重复账单
	 */
	public static final String LIKE_REPEAT_BILL = "1";
	/**
	 * @Fields LIKE_REPEAT_BILL : 理赔信息查看类型：2-审批回退意见
	 */
	public static final String APPROVE_BACK_IDEA = "2";
	/**
	 * @Fields RECORD_TYPE : 理赔信息查看类型：3-费用补偿型医疗保险申请顺序意愿
	 */
	public static final String RECORD_TYPE_THREE = "3";
	/**
	 * @Fields RECORD_TYPE : 理赔信息查看类型：4-风险评估报告存在黑名单人
	 */
	public static final String RECORD_TYPE_FOUR = "4";
	/**
	 * @Fields RECORD_TYPE : 理赔信息查看类型：5-行业共享信息
	 */
	public static final String RECORD_TYPE_FIVE = "5";
	/**
	 * @Fields RECORD_TYPE : 理赔信息查看类型：6-直赔案件问题记录
	 */
	public static final String RECORD_TYPE_SIX = "6";
	/**
	 * @Fields RECORD_TYPE : 理赔信息查看类型：7-电子病历
	 */
	public static final String RECORD_TYPE_SEVEN = "7";
	/**
	 * @Fields RECORD_TYPE : 理赔信息查看类型：8-实名制查验结果
	 */
	public static final String RECORD_TYPE_EIGTH = "8";
	/**
	 * @Fields RECORD_TYPE : 理赔信息查看类型：9-直连数据问题件
	 */
	public static final String RECORD_TYPE_NINE = "9";

	/**
	 * 再保配置额度转换
	 */
	public static final BigDecimal BIGDECIMAL_TEN_THOUSAND = new BigDecimal(10000);
	/**
	 * 用户名AUTO
	 */
	public static final String AUTO = "AUTO";

	/**
	 * 账单金额10000（含）元
	 */
	public static final BigDecimal BILL_ITEM_TEN_THOUSAND = BigDecimal.valueOf(10000);
	/**
	 * 实际给付10000（含）元
	 */
	public static final BigDecimal ACTUAL_PAY_TEN_THOUSAND = BigDecimal.valueOf(10000);
	/**
	 * 反洗钱客户 受益人
	 */
	public static final String CLAIM_ANTI_MONEY_CUSTOMER_ONE = "1";
	/**
	 * 反洗钱客户 领款人
	 */
	public static final String CLAIM_ANTI_MONEY_CUSTOMER_TWO = "2";
	/**
	 * 反洗钱客户 被保人
	 */
	public static final String CLAIM_ANTI_MONEY_CUSTOMER_THREE = "3";

	/**
	 * 理赔公共参数配置-转储天数配置
	 */
	public static final String CLAIM_COMMON_PARA_ = "1";
	/**
	 * 用于年度减1
	 */
	public static final int YEAR_REDUCE_ONE = -1;

	/**
	 * 模型类型 01逆选择
	 */
	public static final String CLAIM_RISK_MODE_ONE = "01";
	/**
	 * 模型类型 02虚假发票
	 */
	public static final String CLAIM_RISK_MODE_TWO = "02";
	/**
	 * 模型类型 01逆选择
	 */
	public static final String CLAIM_RISK_MODE_ONE_STR = "逆选择";
	/**
	 * 模型类型 02虚假发票
	 */
	public static final String CLAIM_RISK_MODE_TWO_STR = "虚假发票";
	/**
	 * 模型类型 01逆选择
	 */
	public static final BigDecimal BIGDECIMAL_ONE_HUNDRED = new BigDecimal(100);
	/**
	 * 风险等级 01高
	 */
	public static final String MODEL_LEVEL_ONE = "01";
	/**
	 * 风险等级 02中
	 */
	public static final String MODEL_LEVEL_TWO = "02";
	/**
	 * 风险等级 03低
	 */
	public static final String MODEL_LEVEL_THREE = "03";
	/**
	 * 模型类型 建设中
	 */
	public static final String CLAIM_RISK_MODE_BUILD = "建设中";
	/**
	 * 模型类型 客户层风险聚集
	 */
	public static final String CLAIM_RISK_MODE_THREE_STR = "客户层风险聚集";
	/**
	 * 模型类型 代理人层风险聚集
	 */
	public static final String CLAIM_RISK_MODE_FOUR_STR = "代理人层风险聚集";
	/**
	 * 关联图谱疑点代码 [个银_身故]代理人客户密集出险---------代理人层风险聚集
	 */
	public static final String DOUBTFUL_ATLAS_L_AG01 = "l_ag01";
	/**
	 * 关联图谱疑点代码 [个银_身故]代理人客户密集意外出险---------代理人层风险聚集
	 */
	public static final String DOUBTFUL_ATLAS_L_AG02 = "l_ag02";
	/**
	 * 关联图谱疑点代码 [个银_重疾/特疾]代理人客户严重疾病密集出险---------代理人层风险聚集
	 */
	public static final String DOUBTFUL_ATLAS_C_AG01 = "c_ag01";
	/**
	 * 关联图谱疑点代码 [个银_重疾/特疾]代理人客户恶性肿瘤密集出险---------代理人层风险聚集
	 */
	public static final String DOUBTFUL_ATLAS_C_AG02 = "c_ag02";
	/**
	 * 关联图谱疑点代码 [个银/团个_不限]冒名报案且存在逆选择拒付
	 */
	public static final String DOUBTFUL_ATLAS_A_RM01 = "a_rm01";
	/**
	 * 关联图谱疑点代码 [个银/团个_不限]冒名报案且存在逆选择风险
	 */
	public static final String DOUBTFUL_ATLAS_A_RM02 = "a_rm02";
	/**
	 * 关联图谱疑点代码 [个银/团个_不限]冒名理赔且存在逆选择拒付
	 */
	public static final String DOUBTFUL_ATLAS_A_AM01 = "a_am01";
	/**
	 * 关联图谱疑点代码 [个银/团个_不限]冒名理赔且存在逆选择风险
	 */
	public static final String DOUBTFUL_ATLAS_A_AM02 = "a_am02";
	/**
	 * 关联图谱疑点代码 [个银/团个_不限]出险保单客户身份关系存疑
	 */
	public static final String DOUBTFUL_ATLAS_A_RT01 = "a_rt01";
	/**
	 * 关联图谱疑点代码 [个银/团个_医疗]医院黑名单审查
	 */
	public static final String DOUBTFUL_ATLAS_M_BHIS = "m_bhis";

	/**
	 * 关联图谱疑点代码 [个银/团个_医疗]医院科室黑名单审查
	 */
	public static final String DOUBTFUL_ATLAS_M_BOFF = "m_boff";

	/**
	 * 关联图谱疑点代码 [个银/团个_医疗]医院科室医生黑名单审查
	 */
	public static final String DOUBTFUL_ATLAS_M_BDOC = "m_bdoc";

	/**
	 * @Fields AGENT_RISK_DOUBTFUL_STR : 代理人层风险聚集,后续有新增拼接
	 */
	public static final String AGENT_RISK_DOUBTFUL_AGENT_STR = "'l_ag01','l_ag02','c_ag01','c_ag02'";
	/**
	 * @Fields AGENT_RISK_DOUBTFUL_STR : 客户层风险聚集,后续有新增拼接
	 */
	public static final String AGENT_RISK_DOUBTFUL_CUS_STR = "'a_rm01','a_rm02','a_am01','a_am02','a_rt01','m_bhis','m_boff','m_bdoc'";
	/**
	 * 2019冠状病毒病码值
	 */
	public static final String U_ZERRO_SEVENTY_ONE = "U071";
	/**
	 * 2019冠状病毒病码值（对应意外）
	 */
	public static final String U_ZERRO_SEVENTYONE = "U071/1";
	/**
	 * 出险结果2码值新冠装病毒
	 */
	public static final String U_ZERRO_SEVENTY_ONE_ONE = "U0711";
	/**
	 * 出险结果2码值新冠装病毒(出险原因意外)
	 */
	public static final String U_ZERRO_SEVENTYONE_ONE = "U0711/1";
	/**
	 * 出险结果2码值新冠装病毒(出险原因疾病)
	 */
	public static final String U_ZERRO_SEVENTY_ONE_TWO = "U0712";
	/**
	 * 出险结果2码值新冠装病毒(出险原因意外)
	 */
	public static final String U_ZERRO_SEVENTYONE_TWO = "U0712/1";
	/**
	 * 出险结果2码值新冠装病毒(出险原因疾病)
	 */
	public static final String U_ZERRO_SEVENTY_ONE_THREE = "U0713";
	/**
	 * 出险结果2码值新冠装病毒(出险原因意外)
	 */
	public static final String U_ZERRO_SEVENTYONE_THREE = "U0713/1";
	/**
	 * 出险结果2码值新冠装病毒(出险原因疾病)
	 */
	public static final String U_ZERRO_SEVENTY_ONE_FOUR = "U0714";
	/**
	 * 出险结果2码值新冠装病毒(出险原因意外)
	 */
	public static final String U_ZERRO_SEVENTYONE_FOUR = "U0714/1";

	/**
	 * 接口调用成功
	 */
	public static final String INTERFACE_CALL_ZERO = "0";
	/**
	 * 接口调用成功 中文
	 */
	public static final String INTERFACE_CALL_ZERO_CHINESE = "BPM_OUT_0001:服务调用成功";
	/**
	 * 接口调用成功 中文
	 */
	public static final String INTERFACE_CALL_ZERO_CHINESE_MSG = "存储成功";
	/**
	 * 接口调用成功 , 已进行过存储
	 */
	public static final String INTERFACE_CALL_TWO = "2";
	/**
	 * 接口调用成功 中文 , 已进行过存储
	 */
	public static final String INTERFACE_CALL_TWO_CHINESE_MSG = "已进行过存储";

	/**
	 * 接口调用失败
	 */
	public static final String INTERFACE_CALL_ONE = "1";
	/**
	 * 接口调用失败 中文
	 */
	public static final String INTERFACE_CALL_ONE_CHINESE = "系统异常";
	/**
	 * 接口调用失败 中文
	 */
	public static final String INTERFACE_CALL_ONE_CHINESE_MSG = "存储失败";
	/**
	 * 外包报文返回D代表特定手术
	 */
	public static final String D_STRING = "D";
	/**
	 * 外包报文返回E代表特种疾病
	 */
	public static final String E_STRING = "E";
	/**
	 * 外包报文返回F代表特定给付
	 */
	public static final String F_STRING = "F";
	/**
	 * 新核心单证提交形式 原件
	 */
	public static final BigDecimal DOC_SUBM_MODE_ONE = new BigDecimal(1);
	/**
	 * 新核心单证提交形式 原件
	 */
	public static final BigDecimal DOC_SUBM_MODE_TWO = new BigDecimal(2);
	/**
	 * 新核心单证提交形式 原件
	 */
	public static final BigDecimal DOC_SUBM_MODE_THREE = new BigDecimal(3);

	/**
	 * @description 单证提交形式(移动平台0 原件 1 复印件 2 影像件。新核心1原件，2复印件，3影像件)
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param docSubmMode 移动平台码值
	 * @return docSubmMode 新核心码值
	 */
	public static BigDecimal docSubmModeChange(BigDecimal docSubmMode) {
		// 单证提交形式设值
		if (docSubmMode.compareTo(new BigDecimal(0)) == 0) {// @invalid 原件
			docSubmMode = DOC_SUBM_MODE_ONE;
		} else if (docSubmMode.compareTo(new BigDecimal(1)) == 0) {// @invalid 复印件
			docSubmMode = DOC_SUBM_MODE_TWO;
		} else if (docSubmMode.compareTo(new BigDecimal(2)) == 0) {// @invalid 影像件
			docSubmMode = DOC_SUBM_MODE_THREE;
		}
		return docSubmMode;

	}

	/**
	 * 获取收付费UnitNumber
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 返回UnitNumber
	 * @throws BizException
	 * @throws SQLException
	 */
	public static String getUnitNumber() throws BizException, SQLException {

		String unitNumberNew = GenerateBizKeyUtil.generateBizKey("CLM_UNITNUMBER").toString();

		Date workDate = WorkDateUtil.getWorkDate();
		String workDateStr = DateUtilsEx.date2String(workDate, "yyyyMMdd");

		return "LP" + workDateStr + unitNumberNew.substring(unitNumberNew.length() - 8, unitNumberNew.length());
	}

	/**
	 * 欺诈风险默认值
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 返回风险默认值信息
	 */
	public static List<ClaimRiskLevelLiabPO> getRiskLevelDemo() {
		// 欺诈风险默认值
		List<ClaimRiskLevelLiabPO> claimRiskLevelLiabPOList = new ArrayList<ClaimRiskLevelLiabPO>();

		ClaimRiskLevelLiabPO claimRiskLevelLiabPOA = new ClaimRiskLevelLiabPO();
		// @invalid 风险名称
		claimRiskLevelLiabPOA.setRiskName("逆选择风险");
		// @invalid 风险分数
		claimRiskLevelLiabPOA.setRiskFraction(new BigDecimal(30));

		ClaimRiskLevelLiabPO claimRiskLevelLiabPOB = new ClaimRiskLevelLiabPO();
		// @invalid 风险名称
		claimRiskLevelLiabPOB.setRiskName("虚假发票风险");
		// @invalid 风险分数
		claimRiskLevelLiabPOB.setRiskFraction(new BigDecimal(0));

		ClaimRiskLevelLiabPO claimRiskLevelLiabPOC = new ClaimRiskLevelLiabPO();
		// @invalid 风险名称
		claimRiskLevelLiabPOC.setRiskName(CLAIM_RISK_MODE_FOUR_STR);
		// @invalid 风险分数
		claimRiskLevelLiabPOC.setRiskFraction(new BigDecimal(30));

		ClaimRiskLevelLiabPO claimRiskLevelLiabPOD = new ClaimRiskLevelLiabPO();
		// @invalid 风险名称
		claimRiskLevelLiabPOD.setRiskName(CLAIM_RISK_MODE_THREE_STR);
		// @invalid 风险分数
		claimRiskLevelLiabPOD.setRiskFraction(new BigDecimal(30));

		ClaimRiskLevelLiabPO claimRiskLevelLiabPOE = new ClaimRiskLevelLiabPO();
		// @invalid 风险名称
		claimRiskLevelLiabPOE.setRiskName("建设中");
		// @invalid 风险分数
		claimRiskLevelLiabPOE.setRiskFraction(new BigDecimal(30));

		claimRiskLevelLiabPOList.add(claimRiskLevelLiabPOA);
		claimRiskLevelLiabPOList.add(claimRiskLevelLiabPOB);
		claimRiskLevelLiabPOList.add(claimRiskLevelLiabPOC);
		claimRiskLevelLiabPOList.add(claimRiskLevelLiabPOD);
		claimRiskLevelLiabPOList.add(claimRiskLevelLiabPOE);

		return claimRiskLevelLiabPOList;
	}

	/**
	 * @description 风险等级转换汉字
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param modelLevel 风险等级
	 * @return
	 */
	public static String modelLevelChange(String modelLevel) {
		// 风险等级转换汉字
		if (modelLevel != null && !ClaimConstant.NULL_STR.equals(modelLevel)
				&& modelLevel.equals(ClaimConstant.MODEL_LEVEL_ONE)) {
			modelLevel = "高";
		} else if (modelLevel != null && !ClaimConstant.NULL_STR.equals(modelLevel)
				&& modelLevel.equals(ClaimConstant.MODEL_LEVEL_TWO)) {
			modelLevel = "中";
		} else if (modelLevel != null && !ClaimConstant.NULL_STR.equals(modelLevel)
				&& modelLevel.equals(ClaimConstant.MODEL_LEVEL_THREE)) {
			modelLevel = "低";
		}
		return modelLevel;

	}

	/**
	 * @Fields TWO_HUNDRED_THOUSAND : 20万元
	 */
	public static final BigDecimal TWO_HUNDRED_THOUSAND = new BigDecimal("200000");

	/**
	 * 续期挂起解挂场景----00501自核规则不通过时挂起续期
	 */
	public static final String FROZEN_SITUATION_ONE = "00501";
	/**
	 * 续期挂起解挂场景----00502回退复核确认时挂起续期操作
	 */
	public static final String FROZEN_SITUATION_TWO = "00502";
	/**
	 * 续期挂起解挂场景----00503审批通过结案时解挂续期收费
	 */
	public static final String FROZEN_SITUATION_THREE = "00503";
	/**
	 * 续期挂起解挂场景----00504审核结案时解挂续期收费
	 */
	public static final String FROZEN_SITUATION_FOUR = "00504";
	/**
	 * 续期挂起解挂场景----00505审核不通过关闭时解挂续期操作
	 */
	public static final String FROZEN_SITUATION_FIVE = "00505";
	/**
	 * 续期挂起解挂场景----00506审核匹配理算时挂起新增涉案保单续期记录和解挂删除保单的续期记录
	 */
	public static final String FROZEN_SITUATION_SIX = "00506";
	/**
	 * 续期挂起解挂场景----00507付费变更挂起续期记录
	 */
	public static final String FROZEN_SITUATION_SEVEN = "00507";
	/**
	 * 续期挂起解挂码值---00核心
	 */
	public static final String FROZEN_SOURCE_ZERO = "00";
	/**
	 * 重大疾病 -- 病种分类轻症
	 */
	public static final String DISEASE_TYPE_ONE = "1";
	/**
	 * 重大疾病 -- 病种分类中症
	 */
	public static final String DISEASE_TYPE_TWO = "2";
	/**
	 * 重大疾病 -- 病种分类重症
	 */
	public static final String DISEASE_TYPE_THREE = "3";
	/**
	 * 可以进行转投的险种
	 */
	public static final List<String> ALLOW_CHANGE_POLICY = new ArrayList<String>();
	static {
		ALLOW_CHANGE_POLICY.add("00958100");
		ALLOW_CHANGE_POLICY.add("00563100");
		ALLOW_CHANGE_POLICY.add("00846000");
		ALLOW_CHANGE_POLICY.add("0057900");
		ALLOW_CHANGE_POLICY.add("0083300");
		ALLOW_CHANGE_POLICY.add("00956000");
		ALLOW_CHANGE_POLICY.add("00238000");
		ALLOW_CHANGE_POLICY.add("00717001");
		ALLOW_CHANGE_POLICY.add("00717000");
		ALLOW_CHANGE_POLICY.add("00718001");
		ALLOW_CHANGE_POLICY.add("00718000");
		ALLOW_CHANGE_POLICY.add("00843000");
	}
	/**
	 * 清单导出最大数
	 */
	public static final int NINE_TEN_NINE_WAN = 999999;

	/**
	 * 免材料标识——是
	 */
	public static final BigDecimal MATERIAL_FT = new BigDecimal(1);

	/**
	 * 短信通知模板代码(NOTICE_CODE=406) 签收确认受托人节点且免材料标识为否
	 */
	public static final BigDecimal MESSAGE_MODE_STRF = BigDecimal.valueOf(406);

	/**
	 * 短信通知模板代码(NOTICE_CODE=407) 签收确认申请人节点且免材料标识为否且是绩优业务员
	 */
	public static final BigDecimal MESSAGE_MODE_SQRJY = BigDecimal.valueOf(407);

	/**
	 * 短信通知模板代码(NOTICE_CODE=408) 签收确认申请人节点且免材料标识为否且是非绩优业务员
	 */
	public static final BigDecimal MESSAGE_MODE_SQRF = BigDecimal.valueOf(408);

	/**
	 * @Fields BATCH_ASIGN_TASK_TYPE_AUDIT : 自动分配批处理审核任务
	 */
	public static final String BATCH_ASIGN_TASK_TYPE_AUDIT = "审核";

	/**
	 * @Fields BATCH_ASIGN_TASK_TYPE_APPROVE : 自动分配批处理审批任务
	 */
	public static final String BATCH_ASIGN_TASK_TYPE_APPROVE = "审批";

	/**
	 * @Fields BATCH_ASIGN_TASK_TYPE_OTHER : 自动分配批处理其他任务
	 */
	public static final String BATCH_ASIGN_TASK_TYPE_OTHER = "其他";

	/**
	 * @Fileds 上海医保产品集合
	 */
	public static final List<String> BUSI_PROD_CODE_SH = Arrays.asList("00557000", "00558000", "00842000", "00843000",
			"00868000");

	/**
	 * 诉讼案件
	 */
	public static final BigDecimal SS_CASE = new BigDecimal(1);

	/**
	 * 非诉讼案件
	 */
	public static final BigDecimal FF_CASE = new BigDecimal(0);

	/**
	 * 深圳医保851产品
	 */
	public static final String CLM_PRODCODE_EIGHTFIVEONE = "00851000";
	/**
	 * 深圳医保865产品
	 */
	public static final String CLM_PRODCODE_EIGHTSIXFIVE = "00865000";
	/**
	 * 深圳医保"身故结算"责任
	 */
	public static final BigDecimal CLM_SZ_1041 = new BigDecimal(1041);
	/**
	 * 直赔接口1 处理成功 0 处理失败
	 */
	public static final String DIRECT_INTERFACE_ONE = "1";
	/**
	 * 直赔接口1 处理成功 0 处理失败
	 */
	public static final String DIRECT_INTERFACE_ZERO = "0";
	/**
	 * 直赔接口0、都不支持；1、快赔；2、直赔；
	 */
	public static final String DIRECT_PAY_TYPE_ZERO = "0";
	/**
	 * 直赔接口0、都不支持；1、快赔；2、直赔；
	 */
	public static final String DIRECT_PAY_TYPE_ONE = "1";
	/**
	 * 直赔接口0、都不支持；1、快赔；2、直赔；
	 */
	public static final String DIRECT_PAY_TYPE_TWO = "2";

	/**
	 * 直赔接口 -新华人寿保险股份有限公司
	 */
	public static final String INSURANCE_COMPANY_NAME = "新华人寿保险股份有限公司";

	/**
	 * 直赔接口 -身份确认接口（客户查询）
	 */
	public static final String DIRECT_INTERFACE_NAME_ONE = "身份确认接口（客户查询）";
	/**
	 * 直赔接口 -理赔申请
	 */
	public static final String DIRECT_INTERFACE_NAME_TWO = "理赔申请";
	/**
	 * 直赔接口 -直赔数据接口
	 */
	public static final String DIRECT_INTERFACE_NAME_THREE = "直赔数据接口";
	/**
	 * 直赔接口 -预结算接口
	 */
	public static final String DIRECT_INTERFACE_NAME_FOUR = "预结算接口";
	/**
	 * 直赔接口 -结算接口
	 */
	public static final String DIRECT_INTERFACE_NAME_FIVE = "结算接口";
	/**
	 * 直赔接口 -运行病历接口
	 */
	public static final String DIRECT_INTERFACE_NAME_SIX = "运行病历接口";
	/**
	 * 直赔接口 -理赔查询接口
	 */
	public static final String DIRECT_INTERFACE_NAME_SEVEN = "理赔查询接口";
	/**
	 * 直赔接口 -理赔状态推送接口
	 */
	public static final String DIRECT_INTERFACE_NAME_EIGHT = "理赔状态推送接口";
	/**
	 * 直赔接口 -影像上传回写接口
	 */
	public static final String DIRECT_INTERFACE_NAME_NINE = "影像上传回写接口";
	/**
	 * 直赔接口 -补充资料通知
	 */
	public static final String DIRECT_INTERFACE_NAME_TEN = "补充资料通知";
	/**
	 * 直赔接口 -取消明细
	 */
	public static final String DIRECT_INTERFACE_NAME_ELEVEN = "取消明细";
	/**
	 * 直连接口 -就诊信息返回接口
	 */
	public static final String DIRECT_INTERFACE_QRYNSUINFO = "就诊信息返回接口";
	/**
	 * 乐约码值转换 治疗情况 11_门诊
	 */
	public static final String MEDICAL_TYPE_ELEVEN = "11";
	/**
	 * 乐约码值转换 治疗情况 21普通住院
	 */
	public static final String MEDICAL_TYPE_TWENTY_ONE = "21";
	/**
	 * 报案方式：直赔
	 */
	public static final BigDecimal REPORT_MODE_TEN = new BigDecimal(10);
	/**
	 * 直赔报案机构，签收机构 8641
	 */
	public static final String DIRECT_HE_NAN_ORGAN = "8641";
	/**
	 * 直赔单证集合
	 */
	public static final String DIRECT_CHECKLIST_STRS = "CLM201,CLM202,CLM205,CLM207";
	/**
	 * 直赔问题件备注
	 */
	public static final String DIRECT_MEMO_TYPE = "15";
	/**
	 * 直赔问题件选项 报案
	 */
	public static final String DIRECT_MEMO_ITEM_ONE = "151";
	/**
	 * 直赔问题件选项 签收
	 */
	public static final String DIRECT_MEMO_ITEM_TWO = "152";
	/**
	 * 直赔问题件选项 立案
	 */
	public static final String DIRECT_MEMO_ITEM_THREE = "153";

	/**
	 * 核保保全查询标识---0
	 */
	public static final String UWCSFLAG = "0";

	/**
	 * 核保保全查询标识---1
	 */
	public static final String CSUWFLAG = "1";

	/**
	 * 核保保全查询结果---0
	 */
	public static final String CSUWRESULT = "0";

	/**
	 * 核保保全查询结果---1
	 */
	public static final String UWCSRESULT = "1";

	/**
	 * 核保保全查询数量
	 */
	public static final int CXRESULT = 0;

	/**
	 * 消息提醒类型 8-直赔案件报案数据异常提醒
	 */
	public static final String NOTICE_TYPE_EIGHT = "8";

	/**
	 * 乐约案件状态：1_未受理
	 */
	public static final String LY_CASE_STATUS_ONE = "1";

	/**
	 * 乐约案件状态：3_受理成功
	 */
	public static final String LY_CASE_STATUS_THREE = "3";

	/**
	 * 乐约案件状态：7_理赔完成
	 */
	public static final String LY_CASE_STATUS_SEVEN = "7";

	/**
	 * 乐约案件状态：4_审核中
	 */
	public static final String LY_CASE_STATUS_FOUR = "4";
	/**
	 * 乐约案件状态：8_已支付
	 */
	public static final String LY_CASE_STATUS_EIGHT = "8";
	/**
	 * 预结算接口 - 医疗类别：11-门诊
	 */
	public static final String LY_TREAT_TYPE_ZERO = "11";
	/**
	 * 预结算接口 - 医疗类别：21-普通住院
	 */
	public static final String LY_TREAT_TYPE_ONE = "21";
	/**
	 * 工作流直赔案件标识 1-直赔结案
	 */
	public static final String DIRECT_CLAIM_FLAG_END = "1";
	/**
	 * 工作流直赔案件标识 2-审核共享池
	 */
	public static final String DIRECT_CLAIM_FLAG_AUDIT = "2";
	/**
	 * 工作流直赔案件标识 3-直赔案件节点
	 */
	public static final String DIRECT_CLAIM_FLAG_THREE = "3";
	/**
	 * 回退案件支付时点-回退前
	 */
	public static final String DIRECT_PAYMENT_TIME_BEFORE = "回退前";
	/**
	 * 回退案件支付时点-回退后
	 */
	public static final String DIRECT_PAYMENT_TIME_AFTER = "回退后";
	/**
	 * 928险种
	 */
	public static final String STRING_NINE_TWO_EIGHT = "00928000";

	/**
	 * @Fields DIRECT_CLM_BUSI_PRODCODE : 直赔案件需要特殊处理的产品
	 */
	public static final List<String> DIRECT_CLM_BUSI_PRODCODE = new ArrayList<String>();
	static {
		DIRECT_CLM_BUSI_PRODCODE.add("00540000");
		DIRECT_CLM_BUSI_PRODCODE.add("00541000");
	}
	/**
	 * @Fields DIRECT_CLM_BUSI_PRODCODE : 对公支付姓名
	 */
	public static final List<String> DIRECT_PAYEE_COMPANY_NAME = new ArrayList<String>();
	static {
		DIRECT_PAYEE_COMPANY_NAME.add("乐约电子科技（上海）有限公司");
	}
	/**
	 * @Fields DIRECT_CLM_BUSI_PRODCODE : 对公支付领款人信息
	 */
	public static final ClaimPayeePO DIRECT_CLAIM_PAYEE = new ClaimPayeePO();
	static {
		DIRECT_CLAIM_PAYEE.setPayeeCertiType("8");
		DIRECT_CLAIM_PAYEE.setPayeeCertiNo("91310114324393883B");
		DIRECT_CLAIM_PAYEE
				.setPayeeCertiStart(DateUtilsEx.formatToDate("2015-01-12", ClaimConstant.SHORT_DATE_FORMAT_STR));
		DIRECT_CLAIM_PAYEE
				.setPayeeCertiEnd(DateUtilsEx.formatToDate("2045-01-11", ClaimConstant.SHORT_DATE_FORMAT_STR));
		DIRECT_CLAIM_PAYEE.setPayeeName("乐约电子科技（上海）有限公司");
		DIRECT_CLAIM_PAYEE.setPayeeSex(new BigDecimal(9));
		DIRECT_CLAIM_PAYEE.setPayeeBirth(DateUtilsEx.formatToDate("2015-01-12", ClaimConstant.SHORT_DATE_FORMAT_STR));
		DIRECT_CLAIM_PAYEE.setPayeePhone("010-********");
		DIRECT_CLAIM_PAYEE.setPayeeNation("");
		DIRECT_CLAIM_PAYEE.setBankCode("8600010");
		DIRECT_CLAIM_PAYEE.setAccountName("乐约电子科技（上海）有限公司");
		DIRECT_CLAIM_PAYEE.setAccountNo("0200242409020213464");
		DIRECT_CLAIM_PAYEE.setPayMode(ClaimConstant.PAY_MODE_32);
		DIRECT_CLAIM_PAYEE.setPayeeRelation("22");
		DIRECT_CLAIM_PAYEE.setPayeeState("110000");
		DIRECT_CLAIM_PAYEE.setPayeeCity("110100");
		DIRECT_CLAIM_PAYEE.setPayeeDistrict("110105");
		DIRECT_CLAIM_PAYEE.setPayeeAddress("北大街8号富华大厦D座8A");
	}
	/**
	 * @Fields 直赔接口补充资料接口URL
	 */
	public static final String DIRECT_LYCLAIMCL513 = "LYCLAIMCL513";
	/**
	 * @Fields 理赔状态推送接口URL
	 */
	public static final String DIRECT_LYCLAIMCL502 = "LYCLAIMCL502";
	/**
	 * @Fields 直赔项目等级(chargeLevel)1甲类，2乙类，3丙类
	 */
	public static final String DIRECT_CHARGE_LEVEL_ONE = "1";
	/**
	 * @Fields 直赔项目等级(chargeLevel)1甲类，2乙类，3丙类
	 */
	public static final String DIRECT_CHARGE_LEVEL_TWO = "2";
	/**
	 * @Fields 直赔项目等级(chargeLevel)1甲类，2乙类，3丙类
	 */
	public static final String DIRECT_CHARGE_LEVEL_THREE = "3";

	/**
	 * @Fields REINSURED_TYPE_ONE : 已续投
	 */
	public static final String REINSURED_TYPE_ONE = "1";
	/**
	 * @Fields REINSURED_TYPE_TWO : 已转保（契约转保）
	 */
	public static final String REINSURED_TYPE_TWO = "2";
	/**
	 * @Fields REINSURED_TYPE_THERR : 已转保（RR保全转保）
	 */
	public static final String REINSURED_TYPE_THERR = "3";

	/**
	 * 特别约定编码 当前标示 1
	 */
	public static final BigDecimal UW_CONDITION_TYPE = new BigDecimal(1);

	/**
	 * @Fields FIVEFIVEEIGHT_AMOUNT : 558限额
	 */
	public static final BigDecimal FIVEFIVEEIGHT_AMOUNT = new BigDecimal(200000);
	/**
	 * @Fields REINSURED_ONE : 已续保
	 */
	public static final String REINSURED_ONE = "1";
	/**
	 * @Fields REINSURED_TWO : 已转保（契约转保）
	 */
	public static final String REINSURED_TWO = "2";
	/**
	 * @Fields REINSURED_THREE : 已转保（RR保全转保）
	 */
	public static final String REINSURED_THREE = "3";

	/**
	 * 超期原因---1-
	 */
	public static final String OVER_REASON = "1";
	/**
	 * 超期原因---2-
	 */
	public static final String OVER_REASON_CUS = "2";
	/**
	 * 数值---9
	 */
	public static final BigDecimal OVER_NINE = new BigDecimal(9);

	/**
	 * 总公司机构86
	 */
	public static final String ZONG_ORG_CODE = "86";
	/**
	 * 北京分公司机构8621
	 */
	public static final String ZONG_ORG_CODE_8621 = "8621";

	/**
	 * 外包标识--是
	 */
	public static final BigDecimal OUTSOURCE_YES = BigDecimal.valueOf(1);

	/**
	 * @Fields DIRECT_CLM_BUSI_PRODCODE : 直赔案件需要特殊处理的产品
	 */
	public static final List<String> MEDICAL_AGGRRISK_TYPE_LIST = new ArrayList<String>();
	static {
		MEDICAL_AGGRRISK_TYPE_LIST.add("11");
		MEDICAL_AGGRRISK_TYPE_LIST.add("8");
		MEDICAL_AGGRRISK_TYPE_LIST.add("13");
	}

	/**
	 * 意健险业务公共API接口为服务名:serverName accident 个人意外险核保风险提示
	 */
	public static final String SERVERNAME_ACCIDENT = "accident";
	/**
	 * 意健险业务公共API接口为服务名:serverName supplementaryMedicalPayment 个人健康险理赔风险提示（补偿医疗）
	 */
	public static final String SERVERNAME_SUPPLEMENTARYMEDICALPAYMENT = "supplementaryMedicalPayment";
	/**
	 * 意健险业务公共API接口为服务名:serverName majorDiseasePayment 个人健康险理赔风险提示（重大疾病）
	 */
	public static final String SERVERNAME_MAJORDISEASEPAYMENT = "majorDiseasePayment";
	/**
	 * 意健险业务公共API接口为服务名:serverName allowancePayment 个人健康险理赔风险提示（个人津贴）
	 */
	public static final String SERVERNAME_ALLOWANCEPAYMENT = "allowancePayment";
	/**
	 * 意健险业务公共API接口为服务名:serverName paymentRepeat 理赔重复收据提示
	 */
	public static final String SERVERNAME_PAYMENTREPEAT = "paymentRepeat";

	/**
	 * @Fields DIRECT_CLM_BUSI_PRODCODE : 直赔案件需要特殊处理的产品
	 */
	public static final List<String> CIITC_MEDICAL_AGGRRISK_TYPE_LIST = new ArrayList<String>();
	static {
		CIITC_MEDICAL_AGGRRISK_TYPE_LIST.add("6");
		CIITC_MEDICAL_AGGRRISK_TYPE_LIST.add("9");
	}

	/**
	 * 人员标记：bene-受益人
	 */
	public static final String PERSON_BENE = "bene";
	/**
	 * 人员标记： payee-领款人
	 */
	public static final String PERSON_PAYEE = "payee";
	/**
	 * 调用场景：0-立案确认
	 */
	public static final BigDecimal CALL_SCENARIO_REGISTER = new BigDecimal(0);
	/**
	 * 调用场景：1-审核确认
	 */
	public static final BigDecimal CALL_SCENARIO_AUDIT = new BigDecimal(1);
	/**
	 * 调用场景：2-结案
	 */
	public static final BigDecimal CALL_SCENARIO_NODE = new BigDecimal(2);
	/**
	 * 实名制查验配置状态：1-新增
	 */
	public static final BigDecimal ADD = new BigDecimal("1");
	/**
	 * 实名制查验配置状态：0-删除
	 */
	public static final BigDecimal DELETE = new BigDecimal("0");

	/**
	 * 实名查验接口需要的静态常量值 机构代码？000019
	 */
	public static final String COMPANY_CODE = "000019";
	/**
	 * 实名查验接口需要的静态常量值 更新天数(传180)？180
	 */
	public static final String UPDATE_DAY = "180";
	/**
	 * 实名查验接口需要的静态常量值 信源选择？03
	 */
	public static final String SOURCE_INFO = "03";
	/**
	 * 实名查验接口需要的静态常量值 服务代码？203
	 */
	public static final String SERVICE_CODE = "203";
	/**
	 * 实名查验接口需要的静态常量值 渠道代码？003
	 */
	public static final String CHANNEL_CODE = "003";
	/**
	 * 实名查验接口需要的静态常量值 监管辖区代码？110000
	 */
	public static final String SUP_PREF_CODE = "110000";
	/**
	 * 实名查验接口需要的静态常量值 业务类型？201_理赔申请
	 */
	public static final String BUS_LINK_CODE_ASK = "201";
	/**
	 * 实名查验接口需要的静态常量值 业务类型？202_理赔确认
	 */
	public static final String BUS_LINK_CODE_SURE = "202";
	/**
	 * 实名查验接口需要的静态常量值 业务类型？502_批改确认
	 */
	public static final String BUS_LINK_CODE_PG = "502";
	/**
	 * 实名查验接口需要的静态常量值 批改类型代码？02_变更受益人或收益比例
	 */
	public static final String ENDOR_TYPE_04 = "04";
	/**
	 * 实名查验接口需要的静态常量值 批改类型代码？99_其他
	 */
	public static final String ENDOR_TYPE_99 = "99";
	/**
	 * 实名查验接口需要的静态常量值 请求用户类型？1.请求用户姓名为保险机构名称时，传000。
	 */
	public static final String BUS_SRE_TYPE_CODE_ORGAN = "000";
	/**
	 * 实名查验接口需要的静态常量值 请求用户类型？2.请求用户姓名为非保险机构，如销售人员时，传111。
	 */
	public static final String BUS_SRE_TYPE_CODE_PEOPLE = "111";

	/**
	 * 实名查验接口需要的静态常量值 国籍-中国
	 */
	public static final String NATION_CODE_CHN = "CHN";
	/**
	 * 实名查验接口需要的静态常量值 新华人寿保险股份有限公司总公司的统一社会信用代码
	 */
	public static final String BUS_SRE_IDCARD = "911100001000238000";
	/**
	 * 实名查验接口需要的静态常量值 新华人寿保险股份有限公司总公司的电话号码
	 */
	public static final String BUS_SRE_PHONE = "01085213233";
	/**
	 * 实名查验接口需要的静态常量值 新华人寿保险股份有限公司总公司的电话号码
	 */
	public static final String BUS_SRE_NAME = "新华人寿保险股份有限公司总公司";
	/**
	 * 实名查验接口需要的静态常量值 业务种类代码?险种分类对应的业务种类代码
	 */
	public static final HashMap<String, String> CLASS_CODE = new HashMap<String, String>();
	static {
		CLASS_CODE.put("30001", "13000");
		CLASS_CODE.put("30002", "16000");
		CLASS_CODE.put("30003", "15000");
		CLASS_CODE.put("30004", "14000");
	}
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:200_成功（业务请求成功）
	 */
	public static final String RES_CODE_200 = "200";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:201_调用XXXX（外部接口名称）超时
	 */
	public static final String RES_CODE_201 = "201";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:202_系统当前请求数过多，请稍后再试
	 */
	public static final String RES_CODE_202 = "202";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:203_xx机构的xxx服务瞬时请求数过多，请稍后再试
	 */
	public static final String RES_CODE_203 = "203";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:300_请求参数有误。（参见数据基本合法性检查校验规则）
	 */
	public static final String RES_CODE_300 = "300";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:301_无访问权限（参见通用规则中用户名、密码和密钥校验规则）
	 */
	public static final String RES_CODE_301 = "301";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:302_XXXX（机构名称）下XXXX（服务名称）已超当日调用上限，请明天再进行访问
	 */
	public static final String RES_CODE_302 = "302";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:303_XXXX的值不在有效范围内 （参见通用规则中代码匹配校验）
	 */
	public static final String RES_CODE_303 = "303";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:304_XXXX认证服务未启用
	 */
	public static final String RES_CODE_304 = "304";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:305_XXXX不在XXXX服务项下已启用的信源代码内
	 */
	public static final String RES_CODE_305 = "305";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:306_XXXX（信源代码）已超信源日调用上限，请使用其它信源认证
	 */
	public static final String RES_CODE_306 = "306";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:307_无可使用的信源代码
	 */
	public static final String RES_CODE_307 = "307";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:311_不支持的证件类型
	 */
	public static final String RES_CODE_311 = "311";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:999_其他未知系统异常
	 */
	public static final String RES_CODE_999 = "999";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:E01_校验开关关闭
	 */
	public static final String RES_CODE_E01 = "E01";
	/**
	 * 实名查验接口需要的静态常量值 接口响应码:E02_不在服务时间范围内
	 */
	public static final String RES_CODE_E02 = "E02";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:10301_核查一致
	 */
	public static final String AUTH_RESULT_10301 = "10301";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:20301_姓名不一致
	 */
	public static final String AUTH_RESULT_20301 = "20301";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:20302_姓名不一致，有效期不一致
	 */
	public static final String AUTH_RESULT_20302 = "20302";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:20303_姓名不一致，有效期一致
	 */
	public static final String AUTH_RESULT_20303 = "20303";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:20304_姓名一致，有效期不一致
	 */
	public static final String AUTH_RESULT_20304 = "20304";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:20305_姓名核查一致，证件已记录失效
	 */
	public static final String AUTH_RESULT_20305 = "20305";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:20306_姓名核查一致，失效信息查询异常
	 */
	public static final String AUTH_RESULT_20306 = "20306";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:20307_核查不一致
	 */
	public static final String AUTH_RESULT_20307 = "20307";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:30301_其它异常
	 */
	public static final String AUTH_RESULT_30301 = "30301";
	/**
	 * 实名查验接口需要的静态常量值 查验服务的认证结果:30302_无匹配记录
	 */
	public static final String AUTH_RESULT_30302 = "30302";
	/**
	 * 实名查验接口需要的静态常量值 默认的二三级机构代码
	 */
	public static final String ORGAN_CODE = "000000";
	/**
	 * 实名查验接口需要的静态常量值 默认日期
	 */
	public static final String DEFULT_DATE = "9999-01-01";
	/**
	 * 实名查验接口需要的静态常量值 证件类型
	 */
	public static final HashMap<String, String> CERT_TYPE = new HashMap<String, String>();
	static {
		CERT_TYPE.put("0", "111");// @invalid 身份证
		CERT_TYPE.put("i", "516");// @invalid 港澳居民来往内地通行证
		CERT_TYPE.put("d", "511");// @invalid 台湾居民来往内地通行证
		CERT_TYPE.put("h", "550");// @invalid 港澳台居民居住证
		CERT_TYPE.put("e", "553");// @invalid 外国人永久居留证
		CERT_TYPE.put("1", "414");// @invalid 护照（同时需要判断国籍非中国）
		CERT_TYPE.put("g", "554");// @invalid 外国人居留证或居留许可
		CERT_TYPE.put("f", "555");// @invalid 外国人临时居留证
	}

	/**
	 * 险种的常量集合
	 */
	public static final List<String> INSURANCETYPELIST = new ArrayList();

	static {
		INSURANCETYPELIST.add("00104000");
		INSURANCETYPELIST.add("00104001");
		INSURANCETYPELIST.add("00104002");
		INSURANCETYPELIST.add("00107000");
		INSURANCETYPELIST.add("00108000");
		INSURANCETYPELIST.add("00108001");
		INSURANCETYPELIST.add("00108002");
		INSURANCETYPELIST.add("00108003");
		INSURANCETYPELIST.add("00109000");
		INSURANCETYPELIST.add("00109001");
		INSURANCETYPELIST.add("00109002");
		INSURANCETYPELIST.add("00110A00");
		INSURANCETYPELIST.add("00110B00");
		INSURANCETYPELIST.add("00110B01");
		INSURANCETYPELIST.add("00110B02");
		INSURANCETYPELIST.add("00115000");
		INSURANCETYPELIST.add("00115001");
		INSURANCETYPELIST.add("00118000");
		INSURANCETYPELIST.add("00118001");
		INSURANCETYPELIST.add("00132000");
		INSURANCETYPELIST.add("00133000");
		INSURANCETYPELIST.add("00134000");
		INSURANCETYPELIST.add("00135000");
		INSURANCETYPELIST.add("00136000");
		INSURANCETYPELIST.add("00137000");
		INSURANCETYPELIST.add("00141000");
		INSURANCETYPELIST.add("00143000");
		INSURANCETYPELIST.add("00144000");
		INSURANCETYPELIST.add("00146000");
		INSURANCETYPELIST.add("00147000");
		INSURANCETYPELIST.add("00150000");
		INSURANCETYPELIST.add("00153000");
		INSURANCETYPELIST.add("00155000");
		INSURANCETYPELIST.add("00156000");
		INSURANCETYPELIST.add("00159000");
		INSURANCETYPELIST.add("00167000");
		INSURANCETYPELIST.add("00170000");
		INSURANCETYPELIST.add("00170100");
		INSURANCETYPELIST.add("00172000");
		INSURANCETYPELIST.add("00173000");
		INSURANCETYPELIST.add("00174000");
		INSURANCETYPELIST.add("00177000");
		INSURANCETYPELIST.add("00178000");
		INSURANCETYPELIST.add("00181000");
		INSURANCETYPELIST.add("00187000");
		INSURANCETYPELIST.add("00191000");
		INSURANCETYPELIST.add("00192000");
		INSURANCETYPELIST.add("00194000");
		INSURANCETYPELIST.add("00195000");
		INSURANCETYPELIST.add("00196000");
		INSURANCETYPELIST.add("00197000");
		INSURANCETYPELIST.add("00201000");
		INSURANCETYPELIST.add("00201001");
		INSURANCETYPELIST.add("00205000");
		INSURANCETYPELIST.add("00207000");
		INSURANCETYPELIST.add("00208000");
		INSURANCETYPELIST.add("00209000");
		INSURANCETYPELIST.add("00210000");
		INSURANCETYPELIST.add("00210001");
		INSURANCETYPELIST.add("00215000");
		INSURANCETYPELIST.add("00216000");
		INSURANCETYPELIST.add("00221000");
		INSURANCETYPELIST.add("00225000");
		INSURANCETYPELIST.add("00227000");
		INSURANCETYPELIST.add("00231000");
		INSURANCETYPELIST.add("00232000");
		INSURANCETYPELIST.add("00233000");
		INSURANCETYPELIST.add("00234000");
		INSURANCETYPELIST.add("00235000");
		INSURANCETYPELIST.add("00236000");
		INSURANCETYPELIST.add("00237000");
		INSURANCETYPELIST.add("00238000");
		INSURANCETYPELIST.add("00240000");
		INSURANCETYPELIST.add("00264000");
		INSURANCETYPELIST.add("00265000");
		INSURANCETYPELIST.add("00266000");
		INSURANCETYPELIST.add("00267000");
		INSURANCETYPELIST.add("00268000");
		INSURANCETYPELIST.add("00277000");
		INSURANCETYPELIST.add("00278000");
		INSURANCETYPELIST.add("00279000");
		INSURANCETYPELIST.add("00280000");
		INSURANCETYPELIST.add("00286000");
		INSURANCETYPELIST.add("00293000");
		INSURANCETYPELIST.add("00294000");
		INSURANCETYPELIST.add("00301000");
		INSURANCETYPELIST.add("00301001");
		INSURANCETYPELIST.add("00314000");
		INSURANCETYPELIST.add("00325000");
		INSURANCETYPELIST.add("00325001");
		INSURANCETYPELIST.add("00332000");
		INSURANCETYPELIST.add("00344000");
		INSURANCETYPELIST.add("00361000");
		INSURANCETYPELIST.add("00362000");
		INSURANCETYPELIST.add("00367000");
		INSURANCETYPELIST.add("00369001");
		INSURANCETYPELIST.add("00369002");
		INSURANCETYPELIST.add("00369003");
		INSURANCETYPELIST.add("00369004");
		INSURANCETYPELIST.add("00370000");
		INSURANCETYPELIST.add("00371000");
		INSURANCETYPELIST.add("00378000");
		INSURANCETYPELIST.add("00387000");
		INSURANCETYPELIST.add("00388000");
		INSURANCETYPELIST.add("00389000");
		INSURANCETYPELIST.add("00390000");
		INSURANCETYPELIST.add("00391000");
		INSURANCETYPELIST.add("00392000");
		INSURANCETYPELIST.add("00397000");
		INSURANCETYPELIST.add("00398000");
		INSURANCETYPELIST.add("00399000");
		INSURANCETYPELIST.add("00411000");
		INSURANCETYPELIST.add("00412000");
		INSURANCETYPELIST.add("00414000");
		INSURANCETYPELIST.add("00415000");
		INSURANCETYPELIST.add("00422000");
		INSURANCETYPELIST.add("00423000");
		INSURANCETYPELIST.add("00424000");
		INSURANCETYPELIST.add("00428000");
		INSURANCETYPELIST.add("00435000");
		INSURANCETYPELIST.add("00438000");
		INSURANCETYPELIST.add("00440000");
		INSURANCETYPELIST.add("00441000");
		INSURANCETYPELIST.add("00445000");
		INSURANCETYPELIST.add("00509000");
		INSURANCETYPELIST.add("00510000");
		INSURANCETYPELIST.add("00512000");
		INSURANCETYPELIST.add("00513000");
		INSURANCETYPELIST.add("00513100");
		INSURANCETYPELIST.add("00515000");
		INSURANCETYPELIST.add("00517000");
		INSURANCETYPELIST.add("00519000");
		INSURANCETYPELIST.add("00521000");
		INSURANCETYPELIST.add("00527000");
		INSURANCETYPELIST.add("00528000");
		INSURANCETYPELIST.add("00533000");
		INSURANCETYPELIST.add("00534000");
		INSURANCETYPELIST.add("00535000");
		INSURANCETYPELIST.add("00536000");
		INSURANCETYPELIST.add("00537100");
		INSURANCETYPELIST.add("00537200");
		INSURANCETYPELIST.add("00542000");
		INSURANCETYPELIST.add("00543000");
		INSURANCETYPELIST.add("00544000");
		INSURANCETYPELIST.add("00547000");
		INSURANCETYPELIST.add("00550000");
		INSURANCETYPELIST.add("00555000");
		INSURANCETYPELIST.add("00556000");
		INSURANCETYPELIST.add("00557000");
		INSURANCETYPELIST.add("00559000");
		INSURANCETYPELIST.add("00560000");
		INSURANCETYPELIST.add("00561000");
		INSURANCETYPELIST.add("00562000");
		INSURANCETYPELIST.add("00563000");
		INSURANCETYPELIST.add("00563100");
		INSURANCETYPELIST.add("00564000");
		INSURANCETYPELIST.add("00565000");
		INSURANCETYPELIST.add("00567000");
		INSURANCETYPELIST.add("00578000");
		INSURANCETYPELIST.add("00579000");
		INSURANCETYPELIST.add("00581000");
		INSURANCETYPELIST.add("00601000");
		INSURANCETYPELIST.add("00602000");
		INSURANCETYPELIST.add("00607000");
		INSURANCETYPELIST.add("00608000");
		INSURANCETYPELIST.add("00609000");
		INSURANCETYPELIST.add("00610000");
		INSURANCETYPELIST.add("00611000");
		INSURANCETYPELIST.add("00613000");
		INSURANCETYPELIST.add("00614000");
		INSURANCETYPELIST.add("00615000");
		INSURANCETYPELIST.add("00616000");
		INSURANCETYPELIST.add("00617000");
		INSURANCETYPELIST.add("00623000");
		INSURANCETYPELIST.add("00627000");
		INSURANCETYPELIST.add("00628000");
		INSURANCETYPELIST.add("00629000");
		INSURANCETYPELIST.add("00630000");
		INSURANCETYPELIST.add("00634000");
		INSURANCETYPELIST.add("00635000");
		INSURANCETYPELIST.add("00636000");
		INSURANCETYPELIST.add("00637000");
		INSURANCETYPELIST.add("00638000");
		INSURANCETYPELIST.add("00639000");
		INSURANCETYPELIST.add("00639100");
		INSURANCETYPELIST.add("00640000");
		INSURANCETYPELIST.add("00640100");
		INSURANCETYPELIST.add("00641000");
		INSURANCETYPELIST.add("00645000");
		INSURANCETYPELIST.add("00646000");
		INSURANCETYPELIST.add("00647000");
		INSURANCETYPELIST.add("00650100");
		INSURANCETYPELIST.add("00650200");
		INSURANCETYPELIST.add("00650300");
		INSURANCETYPELIST.add("00651000");
		INSURANCETYPELIST.add("00653000");
		INSURANCETYPELIST.add("00655000");
		INSURANCETYPELIST.add("00656000");
		INSURANCETYPELIST.add("00657000");
		INSURANCETYPELIST.add("00659000");
		INSURANCETYPELIST.add("00660100");
		INSURANCETYPELIST.add("00660200");
		INSURANCETYPELIST.add("00660300");
		INSURANCETYPELIST.add("00660400");
		INSURANCETYPELIST.add("00663000");
		INSURANCETYPELIST.add("00664000");
		INSURANCETYPELIST.add("00665000");
		INSURANCETYPELIST.add("00668000");
		INSURANCETYPELIST.add("00669000");
		INSURANCETYPELIST.add("00670000");
		INSURANCETYPELIST.add("00672000");
		INSURANCETYPELIST.add("00675000");
		INSURANCETYPELIST.add("00676100");
		INSURANCETYPELIST.add("00676200");
		INSURANCETYPELIST.add("00676300");
		INSURANCETYPELIST.add("00679000");
		INSURANCETYPELIST.add("00681000");
		INSURANCETYPELIST.add("00684000");
		INSURANCETYPELIST.add("00684100");
		INSURANCETYPELIST.add("00687000");
		INSURANCETYPELIST.add("00688000");
		INSURANCETYPELIST.add("00696000");
		INSURANCETYPELIST.add("00701000");
		INSURANCETYPELIST.add("00705000");
		INSURANCETYPELIST.add("00706000");
		INSURANCETYPELIST.add("00708000");
		INSURANCETYPELIST.add("00715000");
		INSURANCETYPELIST.add("00717000");
		INSURANCETYPELIST.add("00717001");
		INSURANCETYPELIST.add("00718000");
		INSURANCETYPELIST.add("00718001");
		INSURANCETYPELIST.add("00724000");
		INSURANCETYPELIST.add("00725000");
		INSURANCETYPELIST.add("00726000");
		INSURANCETYPELIST.add("00727000");
		INSURANCETYPELIST.add("00729000");
		INSURANCETYPELIST.add("00737000");
		INSURANCETYPELIST.add("00740000");
		INSURANCETYPELIST.add("00751000");
		INSURANCETYPELIST.add("00752000");
		INSURANCETYPELIST.add("00756000");
		INSURANCETYPELIST.add("00758000");
		INSURANCETYPELIST.add("00762000");
		INSURANCETYPELIST.add("00763100");
		INSURANCETYPELIST.add("00763200");
		INSURANCETYPELIST.add("00765000");
		INSURANCETYPELIST.add("00771000");
		INSURANCETYPELIST.add("00772000");
		INSURANCETYPELIST.add("00772100");
		INSURANCETYPELIST.add("00775000");
		INSURANCETYPELIST.add("00777000");
		INSURANCETYPELIST.add("00778000");
		INSURANCETYPELIST.add("00779100");
		INSURANCETYPELIST.add("00779200");
		INSURANCETYPELIST.add("00782000");
		INSURANCETYPELIST.add("00784000");
		INSURANCETYPELIST.add("00786000");
		INSURANCETYPELIST.add("00787000");
		INSURANCETYPELIST.add("00790000");
		INSURANCETYPELIST.add("00791000");
		INSURANCETYPELIST.add("00799000");
		INSURANCETYPELIST.add("00804000");
		INSURANCETYPELIST.add("00833000");
		INSURANCETYPELIST.add("00836000");
		INSURANCETYPELIST.add("00837000");
		INSURANCETYPELIST.add("00838000");
		INSURANCETYPELIST.add("00840000");
		INSURANCETYPELIST.add("00841000");
		INSURANCETYPELIST.add("00844000");
		INSURANCETYPELIST.add("00845000");
		INSURANCETYPELIST.add("00849000");
		INSURANCETYPELIST.add("00890000");
		INSURANCETYPELIST.add("00902000");
		INSURANCETYPELIST.add("00903000");
		INSURANCETYPELIST.add("00904000");
		INSURANCETYPELIST.add("00905000");
		INSURANCETYPELIST.add("00907000");
		INSURANCETYPELIST.add("00909000");
		INSURANCETYPELIST.add("00949000");
		INSURANCETYPELIST.add("00950000");
		INSURANCETYPELIST.add("00956000");
		INSURANCETYPELIST.add("00958000");
		INSURANCETYPELIST.add("00958100");
		INSURANCETYPELIST.add("00959000");
		INSURANCETYPELIST.add("00960000");
		INSURANCETYPELIST.add("00973000");
		INSURANCETYPELIST.add("00974000");
		INSURANCETYPELIST.add("00976000");
		INSURANCETYPELIST.add("00978000");
		INSURANCETYPELIST.add("00979000");
		INSURANCETYPELIST.add("01227000");
		INSURANCETYPELIST.add("01266000");
		INSURANCETYPELIST.add("02232000");
		INSURANCETYPELIST.add("02266000");
		INSURANCETYPELIST.add("03235000");
		INSURANCETYPELIST.add("03266000");
		// 以下险种再保暂未上线
		INSURANCETYPELIST.add("00859000");
		INSURANCETYPELIST.add("00861000");
		INSURANCETYPELIST.add("00862000");
		INSURANCETYPELIST.add("00807000");
		INSURANCETYPELIST.add("00858000");
		INSURANCETYPELIST.add("00863000");
		INSURANCETYPELIST.add("00860000");

	}
	/**
	 * 需要豁免的险种的集合
	 */
	public static final List<String> EXEMPT_INSURANCE_TYPE_LIST = new ArrayList();

	static {
		EXEMPT_INSURANCE_TYPE_LIST.add("00861000");
		EXEMPT_INSURANCE_TYPE_LIST.add("00862000");
		EXEMPT_INSURANCE_TYPE_LIST.add("00859000");
	}
	/**
	 * 获取请求的参数
	 */
	public static final String[] ADDR_HEADER = { "X-Forwarded-For", "Proxy-Client-IP", "WL-Proxy-Client-IP",
			"X-Real-IP" };
	/**
	 * 获取请求的参数不存在的值
	 */
	public static final String NUKNOWN = "unknown";
	/**
	 * 直连调取失败原因1,数据回传失败
	 */
	public static final String DIRECT_FAIL_REASON_ONE = "数据回传超时";
	/**
	 * 治疗类型--1门诊
	 */
	public static final String DIRECT_MEDICAL_TYPE_ONE = "1";
	/**
	 * 治疗类型--2住院
	 */
	public static final String DIRECT_MEDICAL_TYPE_TWO = "2";
	/**
	 * 治疗类型--3全部(门诊+住院)
	 */
	public static final String DIRECT_MEDICAL_TYPE_THREE = "3";
	/**
	 * 理赔直连-宝信健康保
	 */
	public static final String DIRECT_CONN_CITIC = "DIRECT_CONN_CITIC";
	/**
	 * 理赔直连-保医通
	 */
	public static final String DIRECT_CONN_BAOYITONG = "DIRECT_CONN_BAOYITONG";

	/**
	 * 一天毫秒值
	 */
	public static final int ONE_DAY = 24 * 60 * 60 * 1000;

	/**
	 * 102171核保内容变更通知数据需要特殊处理豁免险
	 */
	public static final List<String> WAIVER_BUSI_PROD_CODE_LIST = new ArrayList<String>();

	static {
		WAIVER_BUSI_PROD_CODE_LIST.add("00193000");
		WAIVER_BUSI_PROD_CODE_LIST.add("00425000");
		WAIVER_BUSI_PROD_CODE_LIST.add("00952000");
		WAIVER_BUSI_PROD_CODE_LIST.add("00953000");
		WAIVER_BUSI_PROD_CODE_LIST.add("00861000");
		WAIVER_BUSI_PROD_CODE_LIST.add("00862000");
	}
	/**
	 * 102171核保内容变更通知数据需要特殊处理非豁免险00454000、00440000
	 */
	public static final List<String> BUSI_PROD_CODE_LIST = new ArrayList<String>();

	static {
		BUSI_PROD_CODE_LIST.add("00454000");
		BUSI_PROD_CODE_LIST.add("00440000");
	}
	/**
	 * 保费豁免
	 */
	public static final String PREM_WAIVER = "0052100000";
	/**
	 * 保费豁免-确认赔付
	 */
	public static final String PREM_WAIVER_COMPENSATE = "P005570100";
	/**
	 * 保费豁免-确认合同保费
	 */
	public static final String PREM_WAIVER_CONTRACT = "P005570200";
	/**
	 * 收付费业务类型
	 */
	public static final String THREE_THOUSAND_AND_ONE = "3001";

	/**
	 * 字符串-成功
	 */
	public static final String STRING_TRUE = "true";
	/**
	 * 字符串-失败
	 */
	public static final String STRING_FALSE = "false";

	/**
	 * 881险种编码
	 */
	public static final String BUSI_PRO_CODE_ENGITONE = "00881000";

	/**
	 * 881险种自动匹配理算校验责任名称
	 */
	public static final String LIAB_NAME_ENGHT_ONE = "癌症住院医疗费用保险金";

	/**
	 * 881险种自动匹配理算校验责任编码
	 */
	public static final BigDecimal LIAB_ID_ENGHT_ONE = new BigDecimal("7107");

	/**
	 * 881险种自动匹配理算校验状态码
	 */
	public static final String STATUS_CODE_ENGHT_ONE = "881";

	/**
	 * 29
	 */
	public static final int TWOTY_NINE = 29;

	/**
	 * 险种责任组核保结论 加费
	 */
	public static final String BUSI_DECISION_CODE_THIRTY_ONE = "31";
	/**
	 * 险种责任组核保结论 延期
	 */
	public static final String BUSI_DECISION_CODE_FORTY = "40";
	/**
	 * 险种责任组核保结论 拒保
	 */
	public static final String BUSI_DECISION_CODE_FIFTY = "50";
	/**
	 * 险种责任组核保结论 撤保
	 */
	public static final String BUSI_DECISION_CODE_SEVENTY = "70";

	/**
	 * 案件状态为预结案
	 */
	public static final String CASESTATUS_SEVENTY_TWO = "72";
	/**
	 * 实物单证任务-校验 14-该赔案有发生预付理赔金，不允许关闭。
	 */
	public static final String PRE_CLOSING_CHECK_FOURTEEN = "该赔案有发生预付理赔金，不允许关闭。";
	/**
	 * 实物单证任务-校验 15-只允许预结案的案件关闭任务，请核实。
	 */
	public static final String PRE_CLOSING_CHECK_FIFTEEN = "只允许预结案的案件关闭任务，请核实。";
	/**
	 * 实物单证任务-校验 16-单证提交形式均为影像件，请确认是否已收到客户提交的纸质材料并修改提交形式。
	 */
	public static final String PRE_CLOSING_CHECK_SIXTEEN = "单证提交形式均为影像件，请确认是否已收到客户提交的纸质材料并修改提交形式。";
	/**
	 * 实物单证任务-校验失败 1
	 */
	public static final String CHECK_RESULT_ONE = "1";
	/**
	 * 校验成功
	 */
	public static final String CHECK_RESULT_ZERO = "0";
	/**
	 * 校验成功 收齐结论（1-已收齐,2-关闭）
	 */
	public static final String COLLECTALL_CONCLUSIONS_ONE = "1";
	/**
	 * 校验成功 收齐结论（1-已收齐,2-关闭）
	 */
	public static final String COLLECTALL_CONCLUSIONS_TWO = "2";

	/**
	 * 出险结果处理规则-出险结果1
	 */
	public static final List<String> ACCIDENT1 = new ArrayList();
	static {
		ACCIDENT1.add("CC1");
		ACCIDENT1.add("CC2");
		ACCIDENT1.add("CC3");
		ACCIDENT1.add("CC4");
		ACCIDENT1.add("CC5");
		ACCIDENT1.add("CC6");
		ACCIDENT1.add("CC7");
		ACCIDENT1.add("CC8");
		ACCIDENT1.add("CC9");
		ACCIDENT1.add("CC10");
		ACCIDENT1.add("CC11");
		ACCIDENT1.add("CC12");

	}
	/**
	 * 出险结果处理规则-出险结果2
	 */
	public static final List<String> ACCIDENT2 = new ArrayList();
	static {
		ACCIDENT2.add("D00");
		ACCIDENT2.add("D01");
		ACCIDENT2.add("D02");
		ACCIDENT2.add("D03");
		ACCIDENT2.add("D04");
		ACCIDENT2.add("D05");
		ACCIDENT2.add("D06");
		ACCIDENT2.add("D07");
		ACCIDENT2.add("D09");

	}

	/**
	 * 轻度疾病编码 qz068-原位癌
	 */
	public static final String SERIOUS_DISEASE_QZ068 = "qz068";

	/**
	 * 冻结状态
	 */
	public static final BigDecimal FROZENSTATE = new BigDecimal(87);

	/**
	 * 查验业务节点:01:投保单录入完成 02:投保单签单 03:保全录入完成 04:理赔立案 05:理赔审核
	 */
	public static final String BUSINESS_NODE_FOUR = "04";
	public static final String BUSINESS_NODE_FIVE = "05";

	/*
	 * 保留小数点后两位参数
	 */
	public static final String POINT_RIGHT_TWO = "0.00";
}
