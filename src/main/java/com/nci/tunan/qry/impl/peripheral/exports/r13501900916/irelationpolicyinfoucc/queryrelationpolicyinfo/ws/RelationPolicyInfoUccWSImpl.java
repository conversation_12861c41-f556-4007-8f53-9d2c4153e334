package com.nci.tunan.qry.impl.peripheral.exports.r13501900916.irelationpolicyinfoucc.queryrelationpolicyinfo.ws;

import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExA;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.json.DataSerialJSon;
import javax.jws.WebService;
import javax.xml.ws.Holder;
import org.slf4j.Logger;
import java.util.Map;
import com.nci.tunan.qry.interfaces.peripheral.exports.r13501900916.irelationpolicyinfoucc.queryrelationpolicyinfo.SrvReqBody;
import com.nci.tunan.qry.interfaces.peripheral.exports.r13501900916.irelationpolicyinfoucc.queryrelationpolicyinfo.SrvResBizBody;
import com.nci.tunan.qry.interfaces.peripheral.exports.r13501900916.irelationpolicyinfoucc.queryrelationpolicyinfo.SrvResBody;
import com.nci.tunan.qry.interfaces.peripheral.exports.r13501900916.irelationpolicyinfoucc.queryrelationpolicyinfo.ws.IRelationPolicyInfoUccWS;
import com.nci.tunan.qry.impl.peripheral.ucc.r13501900916.IRelationPolicyInfoUcc;

@WebService(endpointInterface = "com.nci.tunan.qry.interfaces.peripheral.exports.r13501900916.irelationpolicyinfoucc.queryrelationpolicyinfo.ws.IRelationPolicyInfoUccWS", serviceName = "RelationPolicyInfoUccWSImplqueryRelationPolicyInfo")
public class RelationPolicyInfoUccWSImpl implements IRelationPolicyInfoUccWS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    
    private IRelationPolicyInfoUcc ucc = null;
    
    public IRelationPolicyInfoUcc getUcc() {
        return ucc;
    }
    public void setUcc(IRelationPolicyInfoUcc ucc) {
        this.ucc = ucc;
    }
    
    @Override
    public void queryRelationPolicyInfo(SysHeader parametersReqHeader, SrvReqBody parametersReqBody, 
            Holder<SysHeader> parametersResHeader, Holder<SrvResBody> parametersResBody) {
        
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
        CommonHeaderDeal.setBIZHEADERTHREAD_EXB(parametersReqBody.getBizHeader());
        com.nci.tunan.qry.interfaces.peripheral.exports.r13501900916.vo.InputData inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        try {
            com.nci.tunan.qry.interfaces.peripheral.exports.r13501900916.vo.OutputData output = ucc.queryRelationPolicyInfo(inputVO);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
            BizHeaderExB  bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
            SrvResBizBody bizResBody = new SrvResBizBody();
            bizResBody.setOutputData(output);
            srvResBody.setBizBody(bizResBody);
            srvResBody.setBizHeader(bizHeader);
            parametersResHeader.value = sysHeader;
            parametersResBody.value = srvResBody;
        } catch (Exception e2) {
        	logger.error("调用接口过程中产生异常!",e2);
        }
        try {
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(parametersResHeader.value));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersResBody.value.getBizHeader()));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody.value.getBizBody()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
        } 
    }
}
 

