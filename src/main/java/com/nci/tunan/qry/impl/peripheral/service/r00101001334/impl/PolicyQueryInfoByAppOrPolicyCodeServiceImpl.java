package com.nci.tunan.qry.impl.peripheral.service.r00101001334.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import com.nci.tunan.qry.dao.INbContractMasterDao;
import org.slf4j.Logger;
import com.nci.tunan.qry.dao.IContractBusiProdDao;
import com.nci.tunan.qry.common.TransferUtil;
import com.nci.tunan.qry.dao.IQryContractMasterDao;
import com.nci.tunan.qry.dao.IUwPolicyDao;
import com.nci.tunan.qry.impl.peripheral.service.r00101001334.IPolicyQueryInfoByAppOrPolicyCodeService;
import com.nci.tunan.qry.imports.nb.impl.NbProcessStatus;
import com.nci.tunan.qry.interfaces.model.nb.po.NbContractMasterPO;
import com.nci.tunan.qry.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.qry.interfaces.model.po.NbJointlyInsuredPolicyPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractMasterPO;
import com.nci.tunan.qry.interfaces.model.uw.po.UwPolicyPO;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.vo.BeneInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.vo.InputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.vo.OutputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.vo.Result;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.vo.SRiskInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.vo.SRiskLevel;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;
import com.nci.tunan.qry.dao.impl.INbJointlyInsuredPolicyDao;

/**
 * 保单查询接口-按投保单号或保单号查询   
 * <AUTHOR>
 *
 */
public class PolicyQueryInfoByAppOrPolicyCodeServiceImpl implements IPolicyQueryInfoByAppOrPolicyCodeService {
	
	private static Logger logger = LoggerFactory.getLogger();
	
	private IQryContractMasterDao qryContractMasterDao;
	
	private IUwPolicyDao uwPolicyDao ;
	/**
	 * 注入共同参保人保单信息
	 */
	
	private INbJointlyInsuredPolicyDao nbJointlyInsuredPolicyDao;
	/**
	 * 注入查询契约投保单主表信息
	 */
	private INbContractMasterDao nbContractMasterDao;
	/**
	 * 注入查询投保单险种信息表
	 */
	private IContractBusiProdDao contractBusiProdDao;
	  
	@Override
	public OutputData queryPolicyInfoByAppOrPolicyCode(InputData inputData) {
		OutputData outputData=new OutputData();
		List<Result>results=new ArrayList<Result>();
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		QryContractMasterPO qryContractMasterPO=new QryContractMasterPO();
		qryContractMasterPO.set("policy_code", inputData.getContNo().trim());
		qryContractMasterPO.set("organ_code", inputData.getMngCom().trim());
		qryContractMasterPO.set("agent_code", inputData.getAgentCode().trim());
		qryContractMasterPO.set("apply_code", inputData.getProposalContNo().trim());
		List<QryContractMasterPO> paList = qryContractMasterDao.policyQueryInfoByAppOrPolicyCode(qryContractMasterPO);
		if(CollectionUtilEx.isNotEmpty(paList)){
			logger.debug("<=========保单库数据查询========policyCode:"+inputData.getContNo().trim()+"======>");
			for (QryContractMasterPO qryContractMasterPO2 : paList) {
				Result result=new Result();
				result.setContNo(qryContractMasterPO2.get("policy_code")==null?StringUtilsEx.nullToString(qryContractMasterPO2.get("apply_code")):
					StringUtilsEx.nullToString(qryContractMasterPO2.get("policy_code")));
				result.setProposalContNo(StringUtilsEx.nullToString(qryContractMasterPO2.get("apply_code")));
				if(null!=qryContractMasterPO2.get("policy_type")&&"1".equals(qryContractMasterPO2.get("policy_type").toString())){
					result.setContType("1");
					result.setContPlanName("");
					result.setMainRiskCode(StringUtilsEx.nullToString(qryContractMasterPO2.get("product_name_sys")));
					result.setmRiskCode(StringUtilsEx.nullToString(qryContractMasterPO2.get("product_code_sys")));
				}else if(null!=qryContractMasterPO2.get("policy_type")&&"2".equals(qryContractMasterPO2.get("policy_type").toString())){
					result.setContType("2");
					result.setContPlanName(StringUtilsEx.nullToString(qryContractMasterPO2.get("prd_pkg_code")));
					result.setMainRiskCode("");
					result.setmRiskCode("");
				}
				result.setPolApplyDate(null==qryContractMasterPO2.get("apply_date")?"":sdf.format(qryContractMasterPO2.get("apply_date")));
				result.setAppntNo(StringUtilsEx.nullToString(qryContractMasterPO2.get("ph_customer_id")));
				result.setAppntIdNo(StringUtilsEx.nullToString(qryContractMasterPO2.get("ph_customer_id_code")));
				result.setAppntName(StringUtilsEx.nullToString(qryContractMasterPO2.get("ph_customer_name")));
				result.setInsuredNo(StringUtilsEx.nullToString(qryContractMasterPO2.get("in_customer_id")));
				result.setInsuredIdNo(StringUtilsEx.nullToString(qryContractMasterPO2.get("in_customer_id_code")));
				result.setInsuredName(StringUtilsEx.nullToString(qryContractMasterPO2.get("in_customer_name")));
				/**需求#80359、#81035变更**/
				result.setAppntIdNumber(StringUtilsEx.nullToString(qryContractMasterPO2.get("appnt_id_number")));
				result.setAppntSex(StringUtilsEx.nullToString(qryContractMasterPO2.get("appnt_sex")));
				result.setAppntBirthday(StringUtilsEx.nullToString(qryContractMasterPO2.get("appnt_birthday")));
				result.setAppntMobile(StringUtilsEx.nullToString(qryContractMasterPO2.get("appnt_mobile")));
				result.setInsuredIdType(StringUtilsEx.nullToString(qryContractMasterPO2.get("insured_id_type")));
				result.setInsuredIdNumber(StringUtilsEx.nullToString(qryContractMasterPO2.get("insured_id_number")));
				result.setAppntAndInsuredRelation(StringUtilsEx.nullToString(qryContractMasterPO2.get("appnt_and_insured_relation")));
				result.setBankCardNumber(StringUtilsEx.nullToString(qryContractMasterPO2.get("bank_card_number")));
				result.setContPlatFlag(StringUtilsEx.nullToString(qryContractMasterPO2.get("cont_plat_flag")));

				//报文并行对比缺少证件类型该字段
				result.setAppntIdType(StringUtilsEx.nullToString(qryContractMasterPO2.get("ph_customer_cert_type")));
				result.setState(StringUtilsEx.nullToString(qryContractMasterPO2.get("proposal_status")));
				result.setIsBPOFlag(StringUtilsEx.nullToString(qryContractMasterPO2.get("is_bpo_flag")));
				result.setIsDocpolicyWay(StringUtilsEx.nullToString(qryContractMasterPO2.get("policy_reinsure_flag")));/*重投或转投标识，01-已重投；02-已转保*/
				
				//银行service_code   签单日期  通信地址  续期银行账号RenewaLtransferAccount
				/* 需求#99695变更*/
				result.setBankCode(StringUtilsEx.nullToString(qryContractMasterPO2.get("service_bank")));//银行
				result.setIssuingDate(StringUtilsEx.nullToString(null==qryContractMasterPO2.get("issue_date")?"":sdf.format(qryContractMasterPO2.get("issue_date"))));//签单日期
				result.setRenewaLtransferAccount(StringUtilsEx.nullToString(qryContractMasterPO2.get("nextaccount")));//续费银行账号
				result.setAddress(StringUtilsEx.nullToString(qryContractMasterPO2.get("address")));//通信地址
				/*需求#146696-【接口需求】 822产品承保业务需求（投保流程）-新时代对接新核心_QRY_3/3-------start*/
				//共同参保保单类型
				result.setJointlyInsuredType(StringUtilsEx.nullToString(qryContractMasterPO2.get("jointly_insured_type")));
				logger.debug("result.setJointlyInsuredType"+result.getJointlyInsuredType());
				//查询共同参保人信息
				//当共同参保保单类型为空或者为子女单时共同参保保单撤保规则校验结果赋值为空
				if(result.getJointlyInsuredType()==null||"".equals(result.getJointlyInsuredType())||result.getJointlyInsuredType().equals(TransferUtil.STRING2)){
					result.setCheckJointlyCancleRuleResult("");
				}else if(result.getJointlyInsuredType().equals(TransferUtil.STRING1)){
					//当共同参保保单类型为父母单时，
						String isNbJointlyInsuredPolicy =this.isNbJointlyInsuredPolicy(qryContractMasterPO2);
						if(isNbJointlyInsuredPolicy!=null&&!"".equals(isNbJointlyInsuredPolicy)){
							if(isNbJointlyInsuredPolicy.equals(TransferUtil.STRING0)){
								result.setCheckJointlyCancleRuleResult(TransferUtil.STRING0);
							}else{
								result.setCheckJointlyCancleRuleResult(TransferUtil.STRING1);
							}
						
						}else{
							result.setCheckJointlyCancleRuleResult("");
						}
				}
				logger.debug("result.setCheckJointlyCancleRuleResult"+result.getCheckJointlyCancleRuleResult());
				/*需求#146696-【接口需求】 822产品承保业务需求（投保流程）-新时代对接新核心_QRY_3/3-------end*/
				
				/***需求#95909变更***/
				// 保全转保信息
				String rrInfo = StringUtilsEx.nullToString(qryContractMasterPO2.get("rr_info"));
				String mediaType = StringUtilsEx.nullToString(qryContractMasterPO2.get("media_type"));
				if (null!=rrInfo && null!=mediaType && "20".equals(rrInfo) && (("0".equals(mediaType)) || ("2".equals(mediaType)))) {
					result.setRrInfo("0");
				} else if (null!=rrInfo 
						&& null!=mediaType 
						&& (("1".equals(rrInfo)) || ("2".equals(rrInfo)) || ("3".equals(rrInfo))) 
						&& (("0".equals(mediaType)) || ("2".equals(mediaType)))) {
					result.setRrInfo("1");
				} else {
					result.setRrInfo("");
				}
				String submitChannel = StringUtilsEx.nullToString(qryContractMasterPO2.get("submit_channel"));
				
				/*需求 #122752-保单查询接口-按投保单号或保单号查询 变更----start */
				result.setSecondInsuredNo(StringUtilsEx.nullToString(qryContractMasterPO2.get("in_customer_id_sec")));
				result.setSecondInsuredIdNo(StringUtilsEx.nullToString(qryContractMasterPO2.get("in_customer_id_code_sec")));
				result.setSecondInsuredName(StringUtilsEx.nullToString(qryContractMasterPO2.get("in_customer_name_sec")));
				result.setSecondInsuredIdType(StringUtilsEx.nullToString(qryContractMasterPO2.get("insured_id_type_sec")));
				result.setSecondInsuredIdNumber(StringUtilsEx.nullToString(qryContractMasterPO2.get("insured_id_number_sec")));
				result.setAppntAndSecondInsuredRelation(StringUtilsEx.nullToString(qryContractMasterPO2.get("appnt_and_insured_relation_sec")));
				/*需求 #122752-保单查询接口-按投保单号或保单号查询 变更----end */
				
				if(null!=mediaType && null!=submitChannel){
					if("1".equals(mediaType) && "1".equals(submitChannel)){
						QryContractMasterPO contractMasterPO = new QryContractMasterPO();
						contractMasterPO.setPolicyCode(result.getContNo());
						List<QryContractMasterPO> contractMasterPOList = qryContractMasterDao.findRrInfoByPolicyCode(contractMasterPO);
						if(!contractMasterPOList.isEmpty()){
							for(QryContractMasterPO  contractMasterPOlist : contractMasterPOList){
								String printtype = StringUtilsEx.nullToString(contractMasterPOlist.get("print_type"));
								if("1".equals(printtype)){
									result.setRrInfo("2");
								}else{
									result.setRrInfo("4");//4-电子渠道业务里未申请纸质保单业务
								}
							}
						}
					}else if("2".equals(mediaType) && "1".equals(submitChannel)){ 
						QryContractMasterPO contractMasterPO = new QryContractMasterPO();
						contractMasterPO.setPolicyCode(result.getContNo());
						List<QryContractMasterPO> contractMasterPOList = qryContractMasterDao.findPrintDetailInfo(contractMasterPO);
						if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
							result.setRrInfo("2");
						}else{
							result.setRrInfo("4");//4-电子渠道业务里未申请纸质保单业务
						}
					}
				}
				if(result.getState().equals("03")||result.getState().equals("04")){
					result.setState("01");//录单
				}else if(result.getState().equals("08")){
					//拒保的话 判断是投保规则拒保还是核保拒保
					UwPolicyPO uwPolicyPO=new UwPolicyPO();
					uwPolicyPO.setApplyCode(inputData.getProposalContNo());
					uwPolicyPO=uwPolicyDao.findUwPolicyDecodeByapplicyCode(uwPolicyPO);
					if(null!=uwPolicyPO&&uwPolicyPO.getData().size()>0){
						result.setState("06");//拒保
					}else{
						result.setState("01");//投保规则拒保了返回01
					}
					
				}else if(result.getState().equals("28")||result.getState().equals("30")){
					result.setState("08");//逾期
				}else if(result.getState().equals("07")||result.getState().equals("22")){
					result.setState("03");//人核
				}else if(result.getState().equals("09")){
					result.setState("04");//待签单
					//bug5244,与E保通 李光雄沟通再保页面做出单前撤保提交申请 =》查询保单信息=》投保单状态应该是在撤保中。make-by zhangxj2_wb
					//redmine37997,与吴明丽确认撤销17状态，增加34、35、36、37状态码值判断。 modify shenpeng_wb
					}else if(result.getState().equals("15")||result.getState().equals("18")||result.getState().equals("19")||
							result.getState().equals("20")||result.getState().equals("21")||result.getState().equals("34")||
							result.getState().equals("35")||result.getState().equals("36")||result.getState().equals("37")
							||result.getState().equals("53")){
					result.setState("0701");//再保那边0701 表示撤保中。
				}else if(result.getState().equals("12")){
					result.setState("0702");//撤保完成
				}else if(result.getState().equals("13") || result.getState().equals("14")){
					QryContractMasterPO qryNbContractMasterPO=new QryContractMasterPO();
					qryNbContractMasterPO.set("organ_code", inputData.getMngCom().trim());
					//qryNbContractMasterPO.set("agent_code", inputData.getAgentCode().trim());
					qryNbContractMasterPO.set("apply_code", qryContractMasterPO2.get("apply_code"));
					List<QryContractMasterPO>nbList=qryContractMasterDao.applyQueryInfoByAppOrPolicyCode(qryNbContractMasterPO);
					//纸质并且非电子签名05   电子签名0501
					if(nbList!=null && !nbList.isEmpty()){
				
					if("1".equals(nbList.get(0).get("confirm_way"))&&"0".equals(nbList.get(0).get("media_type"))){
						result.setState("05");
					}else{
						if(result.getState().equals("14")){
							result.setState("0502");//已承保已签收回执
						}else if(result.getState().equals("13")){
							result.setState("0501");//已承保待签收回执
						}
					}
					}
                }
				
				result.setIfCheck(ifcheck(qryContractMasterPO2));
				//邮件沟通 InsMode字段业务含义是是否电子签名   新核心1是"非电子签名" 2是"电子签名"
				if(null!=qryContractMasterPO2.get("confirm_way")&&"1".equals(qryContractMasterPO2.get("confirm_way").toString())){
					result.setInsMode("2");//纸质
				}else if(null!=qryContractMasterPO2.get("confirm_way")&&"2".equals(qryContractMasterPO2.get("confirm_way").toString())){
					result.setInsMode("1");//电子
				}else{
					result.setInsMode("");
				}
				
				List<BeneInfo> beneInfos=new ArrayList<BeneInfo>();
				if(null!=qryContractMasterPO2.get("busi_item_id")&&null!=qryContractMasterPO2.get("policy_id")){
					QryContractMasterPO contractMasterPO=new QryContractMasterPO();
					contractMasterPO.set("busi_item_id", qryContractMasterPO2.get("busi_item_id"));
					contractMasterPO.set("policy_id", qryContractMasterPO2.get("policy_id"));
					contractMasterPO.set("flag1", "flag1");
					List<QryContractMasterPO>bebeList=qryContractMasterDao.queryContractBeneInfo(contractMasterPO);
					if(CollectionUtilEx.isNotEmpty(bebeList)){
						for (int i = 0; i < bebeList.size(); i++) {
							BeneInfo beneInfo=new BeneInfo();
							beneInfo.setBeneName(StringUtilsEx.nullToString(bebeList.get(i).get("customer_name")));
							/***需求#80359、#81035变更***/
							beneInfo.setInsuredAndBeneRelation(StringUtilsEx.nullToString(bebeList.get(i).get("insured_and_bene_relation")));
							beneInfo.setBeneType(StringUtilsEx.nullToString(bebeList.get(i).get("bene_type")));
							beneInfo.setBeneIdType(StringUtilsEx.nullToString(bebeList.get(i).get("bene_id_type")));
							beneInfo.setBeneIdNo(StringUtilsEx.nullToString(bebeList.get(i).get("bene_id_no")));
							beneInfo.setBeneSex(StringUtilsEx.nullToString(bebeList.get(i).get("bene_sex")));
							beneInfo.setBeneBirthday(StringUtilsEx.nullToString(bebeList.get(i).get("bene_birthday")));
						
							beneInfos.add(beneInfo);
						}
						result.setBeneInfos(beneInfos);
						result.setBeneCont(Integer.toString(bebeList.size()));
					}else{
						BeneInfo beneInfo=new BeneInfo();
						beneInfo.setBeneName("");
						/***需求#80359、#81035变更***/
						beneInfo.setInsuredAndBeneRelation("");
						beneInfo.setBeneType("");
						beneInfo.setBeneIdType("");
						beneInfo.setBeneIdNo("");
						beneInfo.setBeneSex("");
						beneInfo.setBeneBirthday("");
						
						beneInfos.add(beneInfo);
						result.setBeneCont("0");
						result.setBeneInfos(beneInfos);
					}
				}
				//result.setSubInputType(StringUtilsEx.nullToString(qryContractMasterPO2.get("submit_channel")));
				/*String channel = (null==qryContractMasterPO2.get("submit_channel")?"":qryContractMasterPO2.get("submit_channel").toString());
				try {
					
					result.setSubInputType(CodeMapperUtils.getOldCodeByNewCode("ILOG_SUBMIT_CHANNEL", channel,"NBS"));
					//转不出来去0
					 if("".equals(result.getSubInputType())){
						 result.setSubInputType("0"); 
					 }
				} catch (Exception e) {
					
				}*/
				
				String channel = (null==qryContractMasterPO2.get("subinput_type")?"":qryContractMasterPO2.get("subinput_type").toString());
				//result.setSubInputType(CodeMapperUtils.getOldCodeByNewCode("ILOG_SUBMIT_CHANNEL", channel,"NBS"));
				//转不出来去0
				if("".equals(channel)){
					 result.setSubInputType("0");
				}else{
					result.setSubInputType(channel); 
				}
				
				
				//result.setDualRecFlag("否");
				
				try{
					if(!qryContractMasterPO2.getData().isEmpty()){
						if(qryContractMasterPO2.getData().get("drq_flag")!=null && qryContractMasterPO2.getData().get("drq_flag").toString().equals("1")||
								qryContractMasterPO2.getData().get("drq_flag").toString().equals("2")){
							result.setDualRecFlag("是");
						}else{
							result.setDualRecFlag("否");
						}
					}
				}catch(Exception e){
					result.setDualRecFlag("否");
				}
				
				List<SRiskInfo>sRiskInfos=new ArrayList<SRiskInfo>();
				SRiskLevel	sRiskLevel=new SRiskLevel();
				if(null!=qryContractMasterPO2.get("busi_item_id")){
					QryContractMasterPO contractBusiProdPO=new QryContractMasterPO();
					contractBusiProdPO.set("busi_item_id", qryContractMasterPO2.get("busi_item_id"));
					contractBusiProdPO.set("policy_code", qryContractMasterPO2.get("policy_code"));
					contractBusiProdPO.set("pas", "pas");
					List<QryContractMasterPO>busiProdPOs=qryContractMasterDao.queryPasOrNbBuisItemInf(contractBusiProdPO);
					if(CollectionUtilEx.isNotEmpty(busiProdPOs)){
						for (QryContractMasterPO qryContractBusiProdPO : busiProdPOs) {
							SRiskInfo sRiskInfo=new SRiskInfo();
							sRiskInfo.setsRiskCode(StringUtilsEx.nullToString(qryContractBusiProdPO.get("busi_prod_code")));
							sRiskInfos.add(sRiskInfo);
						}
						sRiskLevel.setsRiskInfos(sRiskInfos);
						result.setsRiskLevel(sRiskLevel);
					}else{//附险
						//SRiskInfo sRiskInfo=new SRiskInfo();
						//sRiskInfo.setsRiskCode("");
						//sRiskInfos.add(sRiskInfo);
						result.setsRiskLevel(sRiskLevel);
					}
				}
				//sRiskLevel.setsRiskInfos(sRiskInfos);
				//result.setsRiskLevel(sRiskLevel);
				results.add(result);
			}
			
		}else{//查询契约
			logger.debug("<=========契约库数据查询========policyCode:"+inputData.getContNo().trim()+"======>");
			QryContractMasterPO qryNbContractMasterPO=new QryContractMasterPO();
			qryNbContractMasterPO.set("policy_code", inputData.getContNo().trim());
			qryNbContractMasterPO.set("organ_code", inputData.getMngCom().trim());
			qryNbContractMasterPO.set("agent_code", inputData.getAgentCode().trim());
			qryNbContractMasterPO.set("apply_code", inputData.getProposalContNo().trim());
			List<QryContractMasterPO> nbList = qryContractMasterDao.applyQueryInfoByAppOrPolicyCode(qryNbContractMasterPO);
			if(CollectionUtilEx.isNotEmpty(nbList)){
				for (QryContractMasterPO qryContractMasterPO3 : nbList) {
					Result result=new Result();
					result.setContNo(qryContractMasterPO3.get("policy_code")==null ? StringUtilsEx.nullToString(qryContractMasterPO3.get("apply_code")):StringUtilsEx.nullToString(qryContractMasterPO3.get("policy_code")));
					result.setProposalContNo(StringUtilsEx.nullToString(qryContractMasterPO3.get("apply_code")));
					if(null!=qryContractMasterPO3.get("policy_type")&&"1".equals(qryContractMasterPO3.get("policy_type").toString())){
						result.setContType("1");
						result.setContPlanName("");
						result.setMainRiskCode(StringUtilsEx.nullToString(qryContractMasterPO3.get("product_name_sys")));
						result.setmRiskCode(StringUtilsEx.nullToString(qryContractMasterPO3.get("product_code_sys")));
					}else if(null!=qryContractMasterPO3.get("policy_type")&&"2".equals(qryContractMasterPO3.get("policy_type").toString())){
						result.setContType("2");
						result.setContPlanName(StringUtilsEx.nullToString(qryContractMasterPO3.get("prd_pkg_code")));
						result.setMainRiskCode("");
						result.setmRiskCode("");
					}
					result.setPolApplyDate(null==qryContractMasterPO3.get("apply_date")?"":sdf.format(qryContractMasterPO3.get("apply_date")));
					result.setAppntNo(StringUtilsEx.nullToString(qryContractMasterPO3.get("ph_customer_id")));
					result.setAppntIdNo(StringUtilsEx.nullToString(qryContractMasterPO3.get("ph_customer_id_code")));
					result.setAppntName(StringUtilsEx.nullToString(qryContractMasterPO3.get("ph_customer_name")));
					result.setInsuredNo(StringUtilsEx.nullToString(qryContractMasterPO3.get("in_customer_id")));
					result.setInsuredIdNo(StringUtilsEx.nullToString(qryContractMasterPO3.get("in_customer_id_code")));
					result.setInsuredName(StringUtilsEx.nullToString(qryContractMasterPO3.get("in_customer_name")));
					/**需求#80359、#81035变更**/
					result.setAppntIdNumber(StringUtilsEx.nullToString(qryContractMasterPO3.get("appnt_id_number")));
					result.setAppntSex(StringUtilsEx.nullToString(qryContractMasterPO3.get("appnt_sex")));
					result.setAppntBirthday(StringUtilsEx.nullToString(qryContractMasterPO3.get("appnt_birthday")));
					result.setAppntMobile(StringUtilsEx.nullToString(qryContractMasterPO3.get("appnt_mobile")));
					result.setInsuredIdType(StringUtilsEx.nullToString(qryContractMasterPO3.get("insured_id_type")));
					result.setInsuredIdNumber(StringUtilsEx.nullToString(qryContractMasterPO3.get("insured_id_number")));
					result.setAppntAndInsuredRelation(StringUtilsEx.nullToString(qryContractMasterPO3.get("appnt_and_insured_relation")));
					result.setBankCardNumber(StringUtilsEx.nullToString(qryContractMasterPO3.get("bank_card_number")));
					result.setContPlatFlag(StringUtilsEx.nullToString(qryContractMasterPO3.get("cont_plat_flag")));

					//报文并行对比缺少证件类型该字段
					result.setAppntIdType(StringUtilsEx.nullToString(qryContractMasterPO3.get("ph_customer_cert_type")));
					result.setState(StringUtilsEx.nullToString(qryContractMasterPO3.get("proposal_status")));
					result.setIsBPOFlag(StringUtilsEx.nullToString(qryContractMasterPO3.get("is_bpo_flag")));
					result.setIsDocpolicyWay(StringUtilsEx.nullToString(qryContractMasterPO3.get("policy_reinsure_flag")));/*重投或转投标识，01-已重投；02-已转保*/
					//银行service_code   签单日期  通信地址  续期银行账号RenewaLtransferAccount
					/* 需求#99695变更*/
					result.setBankCode(StringUtilsEx.nullToString(qryContractMasterPO3.get("service_bank")));//银行
					result.setIssuingDate(StringUtilsEx.nullToString(null==qryContractMasterPO3.get("issue_date")?"":sdf.format(qryContractMasterPO3.get("issue_date"))));//签单日期
					result.setRenewaLtransferAccount(StringUtilsEx.nullToString(qryContractMasterPO3.get("nextaccount")));//续费银行账号
					result.setAddress(StringUtilsEx.nullToString(qryContractMasterPO3.get("address")));//通信地址
					/***需求#95909变更***/
					
					/*需求#146696-【接口需求】 822产品承保业务需求（投保流程）-新时代对接新核心_QRY_3/3-------start*/
					//共同参保保单类型
					result.setJointlyInsuredType(StringUtilsEx.nullToString(qryContractMasterPO3.get("jointly_insured_type")));
					logger.debug("result.setJointlyInsuredType"+result.getJointlyInsuredType());
					//查询共同参保人信息
					//当共同参保保单类型为空或者为子女单时共同参保保单撤保规则校验结果赋值为空
					if(result.getJointlyInsuredType()==null||"".equals(result.getJointlyInsuredType())||result.getJointlyInsuredType().equals(TransferUtil.STRING2)){
						result.setCheckJointlyCancleRuleResult("");
					}else if(result.getJointlyInsuredType().equals(TransferUtil.STRING1)){
						//当共同参保保单类型为父母单时，
							String isNbJointlyInsuredPolicy =this.isNbJointlyInsuredPolicy(qryContractMasterPO3);
							if(isNbJointlyInsuredPolicy!=null&&!"".equals(isNbJointlyInsuredPolicy)){
								if(isNbJointlyInsuredPolicy.equals(TransferUtil.STRING0)){
									result.setCheckJointlyCancleRuleResult(TransferUtil.STRING0);
								}else{
									result.setCheckJointlyCancleRuleResult(TransferUtil.STRING1);
								}
							
							}else{
								result.setCheckJointlyCancleRuleResult("");
							}
					}
					logger.debug("result.setCheckJointlyCancleRuleResult"+result.getCheckJointlyCancleRuleResult());
					/*需求#146696-【接口需求】 822产品承保业务需求（投保流程）-新时代对接新核心_QRY_3/3-------end*/
					
					// 保全转保信息
					String rrInfo = StringUtilsEx.nullToString(qryContractMasterPO3.get("rr_info"));
					String mediaType = StringUtilsEx.nullToString(qryContractMasterPO3.get("media_type"));
					if (null!=rrInfo && null!=mediaType && "20".equals(rrInfo) && (("0".equals(mediaType)) || ("2".equals(mediaType)))) {
						result.setRrInfo("0");
					} else if (null!=rrInfo && null!=mediaType && (("1".equals(rrInfo)) || ("2".equals(rrInfo)) || ("3".equals(rrInfo))) && (("0".equals(mediaType)) || ("2".equals(mediaType)))) {
						result.setRrInfo("1");
					} else {
						result.setRrInfo("");
					}
					
					/*需求 #122752-保单查询接口-按投保单号或保单号查询 变更----start */
					result.setSecondInsuredNo(StringUtilsEx.nullToString(qryContractMasterPO3.get("in_customer_id_sec")));
					result.setSecondInsuredIdNo(StringUtilsEx.nullToString(qryContractMasterPO3.get("in_customer_id_code_sec")));
					result.setSecondInsuredName(StringUtilsEx.nullToString(qryContractMasterPO3.get("in_customer_name_sec")));
					result.setSecondInsuredIdType(StringUtilsEx.nullToString(qryContractMasterPO3.get("insured_id_type_sec")));
					result.setSecondInsuredIdNumber(StringUtilsEx.nullToString(qryContractMasterPO3.get("insured_id_number_sec")));
					result.setAppntAndSecondInsuredRelation(StringUtilsEx.nullToString(qryContractMasterPO3.get("appnt_and_insured_relation_sec")));
					/*需求 #122752-保单查询接口-按投保单号或保单号查询 变更----end */
					
					if(result.getState().equals("13") || result.getState().equals("14")){
						//纸质并且非电子签名05   电子签名0501					
						if("1".equals(qryContractMasterPO3.get("confirm_way"))&&"0".equals(qryContractMasterPO3.get("media_type"))){
							result.setState("05");
						}else{
							if(result.getState().equals("14")){
								result.setState("0502");//已承保已签收回执
							}else if(result.getState().equals("13")){
								result.setState("0501");//已承保待签收回执
							}
						}
	                }else if(result.getState().equals("03")||result.getState().equals("04")){
						result.setState("01");//录单
					}else if(result.getState().equals("08")){
						//拒保的话 判断是投保规则拒保还是核保拒保
						UwPolicyPO uwPolicyPO=new UwPolicyPO();
						uwPolicyPO.setApplyCode(inputData.getProposalContNo());
						uwPolicyPO=uwPolicyDao.findUwPolicyDecodeByapplicyCode(uwPolicyPO);
						if(null!=uwPolicyPO&&uwPolicyPO.getData().size()>0){
							result.setState("06");//拒保
						}else{
							result.setState("01");//投保规则拒保了返回01
						}
					}else if(result.getState().equals("28")||result.getState().equals("30")){
						result.setState("08");//已承保已签收回执
					}else if(result.getState().equals("07")||result.getState().equals("22")){
						result.setState("03");//人核
					}else if(result.getState().equals("09")){
						result.setState("04");//待签单
						//bug5244 ,与E保通 李光雄沟通再保页面做出钱单撤保提交申请 =》查询保单信息=》投保单状态应该是在撤保中。make-by zhangxj2_wb
						//redmine37997,与吴明丽确认撤销17状态，增加34、35、36、37状态码值判断。 modify shenpeng_wb
						}else if(result.getState().equals("15")||result.getState().equals("18")||result.getState().equals("19")||
								result.getState().equals("20")||result.getState().equals("21")||result.getState().equals("34")||
								result.getState().equals("35")||result.getState().equals("36")||result.getState().equals("37")
								||result.getState().equals("53")){
						result.setState("0701");//再保那边0701 表示撤保中。
					}else if(result.getState().equals("12")){
						result.setState("0702");//撤保完成
					}
					
					result.setIfCheck(ifcheck(qryContractMasterPO3));
					//邮件沟通 InsMode字段业务含义是是否电子签名   新核心1是"非电子签名" 2是"电子签名"
					if(null!=qryContractMasterPO3.get("confirm_way")&&"1".equals(qryContractMasterPO3.get("confirm_way").toString())){
						result.setInsMode("2");//纸质
					}else if(null!=qryContractMasterPO3.get("confirm_way")&&"2".equals(qryContractMasterPO3.get("confirm_way").toString())){
						result.setInsMode("1");//电子
					}else{
						result.setInsMode("");
					}
					
					List<BeneInfo> beneInfos=new ArrayList<BeneInfo>();
					if(null!=qryContractMasterPO3.get("busi_item_id")&&null!=qryContractMasterPO3.get("policy_id")){
						QryContractMasterPO contractMasterPO=new QryContractMasterPO();
						contractMasterPO.set("busi_item_id", qryContractMasterPO3.get("busi_item_id"));
						contractMasterPO.set("policy_id", qryContractMasterPO3.get("policy_id"));
						contractMasterPO.set("flag2", "flag2");
						List<QryContractMasterPO>bebeList=qryContractMasterDao.queryContractBeneInfo(contractMasterPO);
						if(CollectionUtilEx.isNotEmpty(bebeList)){
							for (int i = 0; i < bebeList.size(); i++) {
								BeneInfo beneInfo=new BeneInfo();
								beneInfo.setBeneName(StringUtilsEx.nullToString(bebeList.get(i).get("customer_name")));
								/***需求#80359、#81035变更***/
								beneInfo.setInsuredAndBeneRelation(StringUtilsEx.nullToString(bebeList.get(i).get("insured_and_bene_relation")));
								beneInfo.setBeneType(StringUtilsEx.nullToString(bebeList.get(i).get("bene_type")));
								beneInfo.setBeneIdType(StringUtilsEx.nullToString(bebeList.get(i).get("bene_id_type")));
								beneInfo.setBeneIdNo(StringUtilsEx.nullToString(bebeList.get(i).get("bene_id_no")));
								beneInfo.setBeneSex(StringUtilsEx.nullToString(bebeList.get(i).get("bene_sex")));
								beneInfo.setBeneBirthday(StringUtilsEx.nullToString(bebeList.get(i).get("bene_birthday")));
								
								beneInfos.add(beneInfo);
							}
							result.setBeneCont(Integer.toString(bebeList.size()));
							result.setBeneInfos(beneInfos);
						}else{
							BeneInfo beneInfo=new BeneInfo();
							beneInfo.setBeneName("");
							/***需求#80359、#81035变更***/
							beneInfo.setInsuredAndBeneRelation("");
							beneInfo.setBeneType("");
							beneInfo.setBeneIdType("");
							beneInfo.setBeneIdNo("");
							beneInfo.setBeneSex("");
							beneInfo.setBeneBirthday("");
							
							beneInfos.add(beneInfo);
							result.setBeneCont("0");
							result.setBeneInfos(beneInfos);
						}
					}
					//result.setSubInputType(StringUtilsEx.nullToString(qryContractMasterPO3.get("submit_channel").toString()));
					String channel = (null==qryContractMasterPO3.get("subinput_type")?"":qryContractMasterPO3.get("subinput_type").toString());
					//result.setSubInputType(CodeMapperUtils.getOldCodeByNewCode("ILOG_SUBMIT_CHANNEL", channel,"NBS"));
					//转不出来去0
					if("".equals(channel)){
						 result.setSubInputType("0");
					}else{
						result.setSubInputType(channel); 
					}
					
					//result.setDualRecFlag("否");
					//是否是双录保单
					try{
					if(!qryContractMasterPO3.getData().isEmpty()){
						
						if(qryContractMasterPO3.getData().get("drq_flag")!=null && qryContractMasterPO3.getData().get("drq_flag").toString().equals("1")||
								qryContractMasterPO3.getData().get("drq_flag").toString().equals("2")){
							result.setDualRecFlag("是");
						}else{
							result.setDualRecFlag("否");
						}
						
					}
					}catch(Exception e){
						result.setDualRecFlag("否");
					}	
					List<SRiskInfo>sRiskInfos=new ArrayList<SRiskInfo>();
					SRiskLevel	sRiskLevel=new SRiskLevel();
					if(null!=qryContractMasterPO3.get("busi_item_id")){
						QryContractMasterPO contractBusiProdPO=new QryContractMasterPO();
						contractBusiProdPO.set("busi_item_id", qryContractMasterPO3.get("busi_item_id"));
						contractBusiProdPO.set("apply_code", qryContractMasterPO3.get("apply_code"));
						contractBusiProdPO.set("nb", "nb");
						List<QryContractMasterPO>busiProdPOs=qryContractMasterDao.queryPasOrNbBuisItemInf(contractBusiProdPO);
						if(CollectionUtilEx.isNotEmpty(busiProdPOs)){
							for (QryContractMasterPO qryContractBusiProdPO : busiProdPOs) {
								SRiskInfo sRiskInfo=new SRiskInfo();
								sRiskInfo.setsRiskCode(StringUtilsEx.nullToString(qryContractBusiProdPO.get("busi_prod_code")));
								sRiskInfos.add(sRiskInfo);
							}
							sRiskLevel.setsRiskInfos(sRiskInfos);
							result.setsRiskLevel(sRiskLevel);
						}else{//附险
							//SRiskInfo sRiskInfo=new SRiskInfo();
							//sRiskInfo.setsRiskCode("");
							//sRiskInfos.add(sRiskInfo);
							result.setsRiskLevel(sRiskLevel);
						}
					}
					//sRiskLevel.setsRiskInfos(sRiskInfos);
					//result.setsRiskLevel(sRiskLevel);
					
					results.add(result);
				}
			}
		}
		outputData.setResults(results);
		return outputData;
	}
	
	private String ifcheck(QryContractMasterPO qryContractMasterPO3) {
		String ziLiaoCheck = qryContractMasterPO3.get("ziliao") == null ? "0" : qryContractMasterPO3.get("ziliao").toString();
		String huiZhiCheck = qryContractMasterPO3.get("huizhi") == null ? "0" : qryContractMasterPO3.get("huizhi").toString();
		//若投保资料和保单回执质检都通过，则输出“1”
		if(!"0".equals(ziLiaoCheck) && !"0".equals(huiZhiCheck)){
			return "1";
		}
		//若仅投保资料质检通过，则输出“2”；
		if(!"0".equals(ziLiaoCheck)){
			return "2";
		}
		//若仅保单回执质检通过，则输出“3”；
		if(!"0".equals(huiZhiCheck)){
			return "3";
		}
		//投保资料和保单回执都未质检或未质检通过，则输出空。
		return "";
	}
	
	/**
	 * 
	 * 根据主投保单号查询判断共同参保保单撤保规则校验结果
	 * */
	private String isNbJointlyInsuredPolicy(QryContractMasterPO qryContractMasterPO2) {
		NbJointlyInsuredPolicyPO nbJointlyInsuredPolicyPO=new NbJointlyInsuredPolicyPO();
		ContractBusiProdPO contractBusiProdPO=new ContractBusiProdPO();
		nbJointlyInsuredPolicyPO.setApplyCode(qryContractMasterPO2.getApplyCode());
		contractBusiProdPO.setApplyCode(qryContractMasterPO2.getApplyCode());
	
		List<ContractBusiProdPO> contractBusiProdPOList=contractBusiProdDao.findAllContractDecsion(contractBusiProdPO);
		//查询共同参保人保单信息得到子女单的投保单号
		List<NbJointlyInsuredPolicyPO> nbJointlyInsuredPolicyPOList=nbJointlyInsuredPolicyDao.getAllJointlyInsuredPolicyByApplyCode(nbJointlyInsuredPolicyPO);
		logger.debug("按投保单号保单号查询共同参保人保单信息数据="+nbJointlyInsuredPolicyPOList.get(0).getApplyCode());
		int i=0;
		int j=0;
		if(nbJointlyInsuredPolicyPOList!=null&&nbJointlyInsuredPolicyPOList.size()>0){
			for(NbJointlyInsuredPolicyPO  nbJointlyInsuredPolicy:nbJointlyInsuredPolicyPOList){
				if(nbJointlyInsuredPolicy.getApplyCode()!=null&&!TransferUtil.STRING00.equals(nbJointlyInsuredPolicy.getRelationToMainInsured())){
					//统计子女单的投保单号不为空的数量，
					i=i+1;
					NbContractMasterPO nbContractMasterPO=new NbContractMasterPO();
					nbContractMasterPO.setApplyCode(nbJointlyInsuredPolicy.getApplyCode());
					//查询主表得到子女单的投保单状态
					//终止状态：拒保/延期、出单前撤保、签单逾期、投保单逾期、催缴逾期
					nbContractMasterPO=nbContractMasterDao.findNbContractMaster(nbContractMasterPO);
					if(nbContractMasterPO.getProposalStatus().equals(NbProcessStatus.WARRANTY_DEFER)||
							nbContractMasterPO.getProposalStatus().equals(NbProcessStatus.CUIJIAODOCUMENT_OVERDUE)||
							nbContractMasterPO.getProposalStatus().equals(NbProcessStatus.POLICY_OVERDUE_BYOVERDUEDOCUMENT)||
							nbContractMasterPO.getProposalStatus().equals(NbProcessStatus.SIGN_OVERDUE)||
							nbContractMasterPO.getProposalStatus().equals(NbProcessStatus.POLICY_CANCEL)){
								logger.debug("按投保单号保单号查询投保单主表信息数据=nbContractMasterPO.getProposalStatus()="+nbContractMasterPO.getProposalStatus());
								//统计子女单的投保单状态为终止状态的数量
								j=j+1;
					}
					
				}
			}
			logger.debug("i="+i);
			logger.debug("j="+j);
			//若父母单共同参保人保单（子女单）提交至核心的子女单存在未终止的子女单时，返回0-不允许撤单
			if(i>0&&j>=0&&j<i){
				if(contractBusiProdPOList!=null&&contractBusiProdPOList.size()>0){
					return TransferUtil.STRING1;
				}else{
					return TransferUtil.STRING0;
				}
			}else if(i==nbJointlyInsuredPolicyPOList.size()-1&&j==nbJointlyInsuredPolicyPOList.size()-1){
				//父母单共同参保人保单（子女单）全部已提交至核心，且子女单已全部终止时，返回1-允许撤单。
				return TransferUtil.STRING1;
			}else if(i>0&&i<nbJointlyInsuredPolicyPOList.size()-1&&j==i){
				//父母单共同参保人保单（子女单）部分未提交至核心，且部分提交核心的子女单已全部终止时，返回1-允许撤单。
				return TransferUtil.STRING2;
			}else if(i==0){
				//父母单共同参保人保单（子女单）全部未提交至核心，返回1-允许撤单。
				return TransferUtil.STRING3;
			}
		}
		return "";
	}
	
	
	
	public IQryContractMasterDao getQryContractMasterDao() {
		return qryContractMasterDao;
	}

	public void setQryContractMasterDao(IQryContractMasterDao qryContractMasterDao) {
		this.qryContractMasterDao = qryContractMasterDao;
	}

	public void setUwPolicyDao(IUwPolicyDao uwPolicyDao) {
		this.uwPolicyDao = uwPolicyDao;
	}
	public INbJointlyInsuredPolicyDao getNbJointlyInsuredPolicyDao() {
		return nbJointlyInsuredPolicyDao;
	}

	public void setNbJointlyInsuredPolicyDao(
			INbJointlyInsuredPolicyDao nbJointlyInsuredPolicyDao) {
		this.nbJointlyInsuredPolicyDao = nbJointlyInsuredPolicyDao;
	}
	public IContractBusiProdDao getContractBusiProdDao() {
		return contractBusiProdDao;
	}

	public void setContractBusiProdDao(IContractBusiProdDao contractBusiProdDao) {
		this.contractBusiProdDao = contractBusiProdDao;
	}
	public INbContractMasterDao getNbContractMasterDao() {
		return nbContractMasterDao;
	}

	public void setNbContractMasterDao(INbContractMasterDao nbContractMasterDao) {
		this.nbContractMasterDao = nbContractMasterDao;
	}
}
