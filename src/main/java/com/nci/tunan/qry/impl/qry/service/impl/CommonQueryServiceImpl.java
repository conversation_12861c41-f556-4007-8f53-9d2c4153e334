package com.nci.tunan.qry.impl.qry.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.nci.tunan.qry.dao.*;
import com.nci.tunan.qry.interfaces.model.nb.po.*;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

import sun.misc.BASE64Encoder;

import com.nci.tunan.qry.impl.util.ClaimConstant;
import com.nci.tunan.qry.impl.qry.service.IClmQueryService;
import com.nci.tunan.qry.dao.IBenefitInsuredDao;
import com.nci.tunan.qry.dao.ICustomerDao;
import com.nci.tunan.qry.interfaces.model.po.BenefitInsuredPO;
import com.git.common.string.StringUtil;
import com.nci.tunan.qry.common.Constants;
import com.nci.tunan.qry.common.TransferUtil;
import com.nci.tunan.qry.impl.qry.service.ICommonQueryService;
import com.nci.tunan.qry.imports.IPAServiceUtils;
import com.nci.tunan.qry.interfaces.model.bo.AgentBO;
import com.nci.tunan.qry.interfaces.model.bo.AgentCompBO;
import com.nci.tunan.qry.interfaces.model.bo.AgentLicenseBO;
import com.nci.tunan.qry.interfaces.model.bo.BusiProdInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.CustomerAccouontBO;
import com.nci.tunan.qry.interfaces.model.bo.CustomerBO;
import com.nci.tunan.qry.interfaces.model.bo.DocumentBO;
import com.nci.tunan.qry.interfaces.model.bo.IdentityCheckInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.InsuredListBO;
import com.nci.tunan.qry.interfaces.model.bo.LockPolicyDefCompBO;
import com.nci.tunan.qry.interfaces.model.bo.PolicyRiskQuestionnaireBO;
import com.nci.tunan.qry.interfaces.model.bo.PolicyStatusBO;
import com.nci.tunan.qry.interfaces.model.bo.QryAccountBO;
import com.nci.tunan.qry.interfaces.model.bo.QryBusiItemInfoCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryClmQueryBO;
import com.nci.tunan.qry.interfaces.model.bo.QryCommonPolicyInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.QryCommonQueryCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryContractMasterBO;
import com.nci.tunan.qry.interfaces.model.bo.QryContractMasterCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryCustomerBO;
import com.nci.tunan.qry.interfaces.model.bo.QryDocumentBO;
import com.nci.tunan.qry.interfaces.model.bo.QryHolderInsuredCustCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryNbDataInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.QryNotifyInfoDetailBO;
import com.nci.tunan.qry.interfaces.model.bo.QryNotifyInfoListBO;
import com.nci.tunan.qry.interfaces.model.bo.QryNotifyInfoLogBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPayPlanDueBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPolicyAccountBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPolicyAcknowledgementBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPolicyHolderBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPolicyMarkingInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPremArapBO;
import com.nci.tunan.qry.interfaces.model.bo.QryProposalProcessBO;
import com.nci.tunan.qry.interfaces.model.bo.QryReissuedPrintBO;
import com.nci.tunan.qry.interfaces.model.bo.QryUwBusiProdBO;
import com.nci.tunan.qry.interfaces.model.bo.QueryCustomerPhoneChangePathBO;
import com.nci.tunan.qry.interfaces.model.bo.QueryPolicyBonusBO;
import com.nci.tunan.qry.interfaces.model.bo.QueryPolicyPasswordPathBO;
import com.nci.tunan.qry.interfaces.model.bo.SecondPolicyHolderBO;
import com.nci.tunan.qry.interfaces.model.bo.ServiceBO;
import com.nci.tunan.qry.interfaces.model.bo.SurvInvCfmInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.TrustCompanyBO;
import com.nci.tunan.qry.interfaces.model.cus.bo.CsPolicyHolderBO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.InsuredListPO;
import com.nci.tunan.qry.interfaces.model.nb.bo.ContractAgentBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbContractBeneBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbContractBusiProdBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbContractCustomerHisBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbContractMasterBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbInsuredListBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbPayerAccountBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbPolicyHolderBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.OrgRelBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsAcceptChangeBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsApplicationBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsBenefitInsuredBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsContractBusiProdBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsContractMasterBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsInfoQueryAcceptBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsInfoQueryBackMoneyBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsInsuredListBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsPolicyAcknowledgementBO;
import com.nci.tunan.qry.interfaces.model.pa.po.CsAcceptChangePO;
import com.nci.tunan.qry.interfaces.model.pa.po.CsApplicationPO;
import com.nci.tunan.qry.interfaces.model.pa.po.CsInfoQueryAcceptPO;
import com.nci.tunan.qry.interfaces.model.pa.po.CsInfoQueryBackMoneyPO;
import com.nci.tunan.qry.interfaces.model.pa.po.CsPolicyInfoChangeThreeaPO;
import com.nci.tunan.qry.interfaces.model.pa.po.CsPolicyInfoChangeThreebPO;
import com.nci.tunan.qry.interfaces.model.pa.po.FreeLookPeriodCfgPO;
import com.nci.tunan.qry.interfaces.model.pa.po.LoanBusiRateCfgPO;
import com.nci.tunan.qry.interfaces.model.pa.po.QryPaContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.pa.vo.PolicyLoanSetMsgVO;
import com.nci.tunan.qry.interfaces.model.po.AgentLicensePO;
import com.nci.tunan.qry.interfaces.model.po.AgentPO;
import com.nci.tunan.qry.interfaces.model.po.BusiProdInfoPO;
import com.nci.tunan.qry.interfaces.model.po.ContractBenePO;
import com.nci.tunan.qry.interfaces.model.po.CustomerAccountPO;
import com.nci.tunan.qry.interfaces.model.po.CustomerPO;
import com.nci.tunan.qry.interfaces.model.po.DocumentPO;
import com.nci.tunan.qry.interfaces.model.po.IdentityCheckInfoPO;
import com.nci.tunan.qry.interfaces.model.po.LockPolicyDefCompPO;
import com.nci.tunan.qry.interfaces.model.po.PolicyHolderPO;
import com.nci.tunan.qry.interfaces.model.po.PolicyRiskQuestionnairePO;
import com.nci.tunan.qry.interfaces.model.po.PolicyStatusPO;
import com.nci.tunan.qry.interfaces.model.po.QryAccountPO;
import com.nci.tunan.qry.interfaces.model.po.QryBusiItemInfoCompPO;
import com.nci.tunan.qry.interfaces.model.po.QryClmQueryPO;
import com.nci.tunan.qry.interfaces.model.po.QryCommonPolicyInfoCompPO;
import com.nci.tunan.qry.interfaces.model.po.QryCommonQueryCompPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractExtendPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractMasterCompPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractMasterLogPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractMasterPO;
import com.nci.tunan.qry.interfaces.model.po.QryCustomerPO;
import com.nci.tunan.qry.interfaces.model.po.QryDocumentPO;
import com.nci.tunan.qry.interfaces.model.po.QryHolderInsuredCustCompPO;
import com.nci.tunan.qry.interfaces.model.po.QryNotifyInfoDetailPO;
import com.nci.tunan.qry.interfaces.model.po.QryNotifyInfoListPO;
import com.nci.tunan.qry.interfaces.model.po.QryNotifyInfoLogPO;
import com.nci.tunan.qry.interfaces.model.po.QryPayPlanDuePO;
import com.nci.tunan.qry.interfaces.model.po.QryPolicyAccountPO;
import com.nci.tunan.qry.interfaces.model.po.QryPolicyAcknowledgementPO;
import com.nci.tunan.qry.interfaces.model.po.QryPolicyHolderPO;
import com.nci.tunan.qry.interfaces.model.po.QryPolicyMarkingInfoPO;
import com.nci.tunan.qry.interfaces.model.po.QryPremArapPO;
import com.nci.tunan.qry.interfaces.model.po.QryProposalProcessPO;
import com.nci.tunan.qry.interfaces.model.po.QryReissuedPrintPO;
import com.nci.tunan.qry.interfaces.model.po.QryUwBusiProdPO;
import com.nci.tunan.qry.interfaces.model.po.QueryCustomerPhoneChangePathPO;
import com.nci.tunan.qry.interfaces.model.po.QueryPolicyBonusPO;
import com.nci.tunan.qry.interfaces.model.po.QueryPolicyPasswordPathPO;
import com.nci.tunan.qry.interfaces.model.po.SecondPolicyHolderPO;
import com.nci.tunan.qry.interfaces.model.po.ServicePO;
import com.nci.tunan.qry.interfaces.model.po.SurvInvCfmInfoPO;
import com.nci.tunan.qry.interfaces.model.po.TrustCompanyPO;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.framework.bizservice.IBizService;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagReqVO;
import com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagResVO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.qry.impl.jrqd.dao.pa.IInsuredListDao;
import com.nci.tunan.qry.impl.jrqd.service.pa.IPRDService;
import com.nci.tunan.qry.util.StrTool;
import com.nci.udmp.util.lang.CollectionUtilEx;
/**
 * @description 综合查询Service实现类
 * <AUTHOR> <EMAIL>
 * @date 2015-1-22 下午5:24:42
 */
public class CommonQueryServiceImpl implements ICommonQueryService, IBizService { 
    /**
     * 保单失效状态
     */
    private final static BigDecimal POLICY_LIABILITY_STATE_END = new BigDecimal(3);

    private IQryContractMasterDao contractMasterDao;
    private IQryContractBusiProdDao contractBusiProdDao;
    private IQryContractProductDao contractProductDao;
    private IQryContractExtendDao contractExtendDao;
    private IQryPolicyAcknowledgementDao policyAcknowledgementDao;
    private IQryCustomerDao customerDao;
    private IQryPolicyHolderDao policyHolderDao;
    private IQryContractBeneDao contractBeneDao;
    private IQryInsuredListDao insuredListDao;
    private IQryPayerAccountDao payerAccountDao;
    private IQryCommonQueryDao commonQueryDao;
    private IQryContractMasterLogDao contractMasterLogDao;
    private IQryPolicyOperationDao policyOperationDao;
    private IQryPolicyStatusDao policyStatusDao;
    private IQryFindUwBusiProdInfoDao findUwBusiProdInfoDao;
    private ILockPolicyQueryDao lockPolicyQueryDao;
    private CommonLoanInfoServiceImpl commonLoanInfoService;
    
	@Autowired
	@Qualifier("JRQD_PA_PRDService")
	private IPRDService prdIAS;
	/**145832
	 * @Fields insuredListDao : 被保人Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_InsuredListDao")
	private IInsuredListDao insuredDao;
	
    /**
     * 被保险人
   	 */
    @Autowired
	@Qualifier("CLM_insuredListDao")
   	private com.nci.tunan.qry.dao.IInsuredListDao insuredListDaos;
   	/**
   	 * 客户表
   	 */
   	private ICustomerDao customerDaos;
   	/**
   	 * 险种被保人
   	 */
   	private IBenefitInsuredDao benefitInsuredDao;
   	
   	private IClmQueryService clmQueryService;   	

	public IClmQueryService getClmQueryService() {
		return clmQueryService;
	}

	public void setClmQueryService(IClmQueryService clmQueryService) {
		this.clmQueryService = clmQueryService;
	}

	public com.nci.tunan.qry.dao.IInsuredListDao getInsuredListDaos() {
		return insuredListDaos;
	}

	public void setInsuredListDaos(
			com.nci.tunan.qry.dao.IInsuredListDao insuredListDaos) {
		this.insuredListDaos = insuredListDaos;
	}

	public ICustomerDao getCustomerDaos() {
		return customerDaos;
	}

	public void setCustomerDaos(ICustomerDao customerDaos) {
		this.customerDaos = customerDaos;
	}

	public IBenefitInsuredDao getBenefitInsuredDao() {
		return benefitInsuredDao;
	}

	public void setBenefitInsuredDao(IBenefitInsuredDao benefitInsuredDao) {
		this.benefitInsuredDao = benefitInsuredDao;
	}
	
	/**
     * 查询保单打标信息Dao接口
     */
    @Autowired
	@Qualifier("qry_PolicyMarkingInfoDaoImpl")
    private IQryPolicyMarkingInfoDao qryPolicyMarkingInfoDao;
    
	/**
	 * 第二投保人dao
	 */
	private ISecondPolicyHolderDao secondPolicyHolderDao;
	

	public ISecondPolicyHolderDao getSecondPolicyHolderDao() {
		return secondPolicyHolderDao;
	}

	public void setSecondPolicyHolderDao(ISecondPolicyHolderDao secondPolicyHolderDao) {
		this.secondPolicyHolderDao = secondPolicyHolderDao;
	}

	public void setCommonLoanInfoService(CommonLoanInfoServiceImpl commonLoanInfoService) {
        this.commonLoanInfoService = commonLoanInfoService;
    }
	
	private IPAServiceUtils paServiceUtil;
	

	public IPAServiceUtils getPaServiceUtil() {
		return paServiceUtil;
	}

	public void setPaServiceUtil(IPAServiceUtils paServiceUtil) {
		this.paServiceUtil = paServiceUtil;
	}
	
	@Autowired
	@Qualifier("PA_contractBeneDao")
	private IContractBeneDao paContractBeneDao;
	
	@Autowired
	@Qualifier("QRY_policyHolderDao")
	private IPolicyHolderDao PaPolicyHolderDao;

    /**
     * 保单信息拓展表
     * */
    private INbPolicyMasterExtendDao nbPolicyMasterExtendDao;


    //用于判断销售渠道是否为“个人中介渠道”
	private static final String CONSTANT_AGENT_CHANNEL = "13";
	@Override
    public QryContractMasterCompBO findPolicyInfoByCode(QryContractMasterCompBO contractMasterCompBO) {
        LOGGER.debug("<======CommonQueryServiceImpl--findPolicyInfoComp======>");
        long start=0;
        long end=0;
        QryContractMasterCompPO qryContractMasterCompPO = BeanUtils.copyProperties(QryContractMasterCompPO.class,
                contractMasterCompBO);
        QryContractMasterCompBO qryContractMasterCompBO = new QryContractMasterCompBO();
        //判断网销的单子
        QryContractMasterCompPO contractMasterCompPO = new QryContractMasterCompPO();
        if(contractMasterCompBO != null && contractMasterCompBO.getPolicyCode() !=null){
        	start=DateUtilsEx.getTodayDate().getTime();
            if(contractMasterCompBO.getPolicyCode().startsWith("A")){
                 contractMasterCompPO = commonQueryDao.findPolicyInfoByCodeA(qryContractMasterCompPO);
            }else{
                 contractMasterCompPO = commonQueryDao.findPolicyInfoByCode(qryContractMasterCompPO);
            }
            end=DateUtilsEx.getTodayDate().getTime();
            LOGGER.debug("2-1:查询保单主体消息耗时："+(end-start));
        }
       
        if (contractMasterCompPO.getLiabilityState() == POLICY_LIABILITY_STATE_END) {
            qryContractMasterCompBO.setEndDate(contractMasterCompPO.getExpiryDate());
            qryContractMasterCompBO.setExpiryDate(null);
        }
        if (null != contractMasterCompPO.getSpecialAccountFlag()&&"1".equals(contractMasterCompPO.getSpecialAccountFlag())) {
            qryContractMasterCompBO.setSpecialAccountFlag("1");
        }
        
            qryContractMasterCompBO = BeanUtils.copyProperties(QryContractMasterCompBO.class, contractMasterCompPO);
            /**** RM:146494 2023年新核心保全复核环节系统功能优化（1）--综合查询 START ****/
			if (contractMasterCompPO.getPolicyCode() != null && contractMasterCompPO.getMediaType() != null
					&& contractMasterCompPO.getMediaType().compareTo(BigDecimal.ONE) == 0) {// 电子保单
				// 1）当保单类型为“电子保单”，且该保单历史存在已生效的“重新出单”保全项目，则显示为“电子与纸质并行”；
				// 2）当保单类型为“电子保单”，且该保单历史存在已生效的“减保”、“减额交清及险种转换”保全项目，如果此保全项目录入“是否重新出单”选择“是”，则显示为“电子与纸质并行”；
				QryContractMasterCompPO queryMediaTypePO = new QryContractMasterCompPO();
				queryMediaTypePO.setPolicyCode(contractMasterCompPO.getPolicyCode());
				List<QryContractMasterCompPO> queryMediaTypePOs = commonQueryDao.findMediaTypeByCode(queryMediaTypePO);
				if (CollectionUtilEx.isNotEmpty(queryMediaTypePOs)) {
					for (QryContractMasterCompPO queryMediaType : queryMediaTypePOs) {
						if (queryMediaType != null && queryMediaType.getMediaType() != null) {
							qryContractMasterCompBO.setMediaType(queryMediaType.getMediaType());
						}
						break;
					}
				}
			}
			/**** RM:146494 2023年新核心保全复核环节系统功能优化（1）--综合查询 END ****/
            if (null != contractMasterCompPO.getIsSelfInsured() && contractMasterCompPO.getIsSelfInsured().compareTo(new BigDecimal(1)) == 0) {
    			qryContractMasterCompBO.setIsSelfMutualInsured("自保件");
    		} else if (!"1".equals(contractMasterCompPO.getIsSelfInsured() + "") && "1".equals(contractMasterCompPO.getIsMutualInsured() + "")) {
    			qryContractMasterCompBO.setIsSelfMutualInsured("互保件");
    		} else {
    			qryContractMasterCompBO.setIsSelfMutualInsured("");
    		}
            String organCode = contractMasterCompPO.getOrganCode().substring(0, 4);
            if("8621".equals(organCode)){
            	qryContractMasterCompBO.setBfflag("1");
            }
            qryContractMasterCompBO.setePolicySendTime(contractMasterCompPO.getEPolicySendTime());
            QryContractMasterCompPO qryCompPO;
            start=DateUtilsEx.getTodayDate().getTime();
            qryCompPO = commonQueryDao.findCallPolicyInfoByCode(contractMasterCompPO);
            end=DateUtilsEx.getTodayDate().getTime();
            LOGGER.debug("2-2:查询保单回访信息耗时："+(end-start));
            //@invalid 显示电话中心返回该保单的最新一条记录的“回访时间（核心）”+“回访结果（核心）”
            //@invalid 回访时间（核心） yyyy-MM-dd HH:mm:ss
            Date callResultSaveDate = qryCompPO.get("call_result_date_sys") == null ? null : (Date)qryCompPO.get("call_result_date_sys") ;
            qryContractMasterCompBO.setCallResultSaveDate(callResultSaveDate);
            String callResultSave = callResultSaveDate == null ? "" : DateUtilsEx.formatToString(callResultSaveDate, "yyyy-MM-dd HH:mm:ss");
            
            //@invalid 回访结果（核心）
            String callResultSys = qryCompPO.get("call_result_sys") == null ? "" : qryCompPO.get("call_result_sys").toString() ; 
             
            //@invalid 回访时间 + 回访结果 
            String callSaveDateAndStatus = callResultSave + callResultSys; 
            qryContractMasterCompBO.setCallSaveDateAndStatus(callSaveDateAndStatus);
            
            //@invalid 是否完成回访
            String callFinish = qryCompPO.get("call_finish") == null ? "0" : qryCompPO.get("call_finish").toString() ; 
            qryContractMasterCompBO.setIsCallResultSave("1".equals(callFinish) ? "是" : "否" );
            
            //@invalid 处理电话回访预约时间段
            String reviewTimePeriod = qryContractMasterCompBO.getReviewTimePeriod();
            if (!StringUtils.isNumeric(reviewTimePeriod)) {
            	qryContractMasterCompBO.setReviewTimePeriod(null);
			}
            
            start=DateUtilsEx.getTodayDate().getTime();
            qryCompPO = commonQueryDao.findOrphanPolicyInfoByCode(qryContractMasterCompPO);
            end=DateUtilsEx.getTodayDate().getTime();
            LOGGER.debug("2-3:查询保单孤儿单耗时："+(end-start));
            // 判断保单是否孤儿单
            if (null != contractMasterCompPO.getPolicyFlag()) {
                if ("2".equals(contractMasterCompPO.getPolicyFlag())) {
                	qryContractMasterCompBO.setOrphanPolicyFlag("是");
                } else {
                    qryContractMasterCompBO.setOrphanPolicyFlag("否");
                }
            }
            //判断医保标识是否为上海医保业务
            if(!StringUtilsEx.isNullOrEmpty(contractMasterCompBO.getPolicyCode())||
            		!StringUtilsEx.isNullOrEmpty(contractMasterCompBO.getApplyCode())){
            	if(!StringUtilsEx.isNullOrEmpty(contractMasterCompBO.getMedicalInsuranceCard())){
            		qryContractMasterCompBO.setSociSecu(new BigDecimal("1"));
            	}else{
            		int sociCount = commonQueryDao.findSociSecubyCode(qryContractMasterCompPO);
	            	if(sociCount == 1){
	            		qryContractMasterCompBO.setSociSecu(new BigDecimal("1"));
	            		qryContractMasterCompBO.setMedicalInsuranceCard("上海医保卡");/*设置为上海医保*/
	            	}
            	}
            }
            //判断医保标识是否为上海医保业务end
            // 判断保单状态
            if (!StringUtilsEx.isNullOrEmpty(contractMasterCompBO.getPolicyCode())) {
                //查询保单补发次数
                qryContractMasterCompPO.setPolicyId(contractMasterCompPO.getPolicyId());
                start=DateUtilsEx.getTodayDate().getTime();
                Map<String, Object> reissue = commonQueryDao.findPolicyReissue(qryContractMasterCompPO);
                end=DateUtilsEx.getTodayDate().getTime();
                LOGGER.debug("2-4:查询保单补发次数耗时："+(end-start));
                if (reissue!=null) {
                    qryContractMasterCompBO.setReissue(reissue.get("REISSUE").toString());
                }
                /*#45810 新核心业务系统青岛上线优化需求——清单、查询、承保功能优化（1）开始*/
                /*电子质检状态，双录质*/
                 qryContractMasterCompPO.setApplyCode(contractMasterCompPO.getApplyCode());
                 List<Map<String, Object>> eqtStatus = commonQueryDao.findPolicyQtStatus(qryContractMasterCompPO);
                 for(Map<String,Object> status:eqtStatus){
                     if ("4".equals(status.get("QA_TYPE").toString())){ //投保资料质检状态
                         qryContractMasterCompBO.seteQtStatus(status.get("STATUS_NAME").toString());
                     }else if ("5".equals(status.get("QA_TYPE").toString())){ //回执质检状态
                         qryContractMasterCompBO.setAcknowledgementQtStatus(status.get("STATUS_NAME").toString());
                     }else if ("6".equals(status.get("QA_TYPE").toString())){ //双录质检状态
                         qryContractMasterCompBO.setDoubleRecord(status.get("STATUS_NAME").toString());
                     }
                 }
                 
                 
                 /*#45810 新核心业务系统青岛上线优化需求——清单、查询、承保功能优化（1）结束 */
                // 6.销售渠道为'互联网渠道'时,查询保单销售人员和保单服务人员信息 查询表 代理人表 T_AGENT、T_AGENT_MEDIATION_AGR、T_AGENT_INTERMEDIARY 等等
                start=DateUtilsEx.getTodayDate().getTime();
                List<Map<String, Object>> policyAgentInfosInt = commonQueryDao.findAgentInfoFromInternet(qryContractMasterCompPO);
                end=DateUtilsEx.getTodayDate().getTime();
                LOGGER.debug("1-5:查询保单服务人员信息耗费时间："+String.valueOf(end-start));
                if(CollectionUtilEx.isNotEmpty(policyAgentInfosInt)){
                	for(Map<String,Object> policyAgentInfoInt:policyAgentInfosInt){
                		if (policyAgentInfoInt.get("IS_NB_AGENT")!=null && policyAgentInfoInt.get("IS_NB_AGENT").toString().equals("1")){
                            qryContractMasterCompBO.setSaleAgentCode(policyAgentInfoInt.get("AGENT_CODE").toString());
                            qryContractMasterCompBO.setSaleAgentName(policyAgentInfoInt.get("AGENT_NAME").toString());
                            qryContractMasterCompBO.setSaleAgentOrganCode(policyAgentInfoInt.get("AGENT_ORGAN_CODE").toString());
                            qryContractMasterCompBO.setChannelType(policyAgentInfoInt.get("AGENT_CHANNEL").toString());
                		}
                		if(policyAgentInfoInt.get("IS_CURRENT_AGENT")!=null && policyAgentInfoInt.get("IS_CURRENT_AGENT").toString().equals("1")){
                            qryContractMasterCompBO.setAgentCode(policyAgentInfoInt.get("AGENT_CODE").toString());
                            qryContractMasterCompBO.setAgentName(policyAgentInfoInt.get("AGENT_NAME").toString());
                            qryContractMasterCompBO.setAgentOrganCode(policyAgentInfoInt.get("AGENT_ORGAN_CODE").toString());
                		}
                       /* if (policyAgentInfoInt.get("AGENT_COM")!=null){
                            qryContractMasterCompBO.setOtherManageCom(policyAgentInfoInt.get("AGENT_COM").toString());//@invalid 中介机构代码
                        }*/
                		//#160232 支持互联网渠道新增慧择平台出单渠道信息查询需求-新契约
                		if (policyAgentInfoInt.get("OTHER_MANAGE_COM")!=null){
                        	qryContractMasterCompBO.setOtherManageCom(policyAgentInfoInt.get("OTHER_MANAGE_COM").toString());//@invalid 中介机构代码
                        }
                        if (policyAgentInfoInt.get("OTHER_MANAGE_NAME")!=null){
                           	qryContractMasterCompBO.setOtherManageName(policyAgentInfoInt.get("OTHER_MANAGE_NAME").toString());//@invalid 中介机构名称
                        }
                        if (policyAgentInfoInt.get("PROTOCOL_ID")!=null){
                            qryContractMasterCompBO.setProtocolId(policyAgentInfoInt.get("PROTOCOL_ID").toString());//@invalid 协议号
                        }
                	}
                }else{
                	// 6.查询保单销售人员和保单服务人员信息 查询表 代理人表 T_AGENT 投保单主表信息 T_NB_CONTRACT_MASTER 等等
                	start=DateUtilsEx.getTodayDate().getTime();
                	List<Map<String, Object>>  policyAgentInfos=commonQueryDao.findAgentInfo(qryContractMasterCompPO);
                	end=DateUtilsEx.getTodayDate().getTime();
                	LOGGER.debug("2-5:查询保单代理人耗时："+(end-start));
                    for(Map<String,Object> policyAgentInfo:policyAgentInfos){
                        if (policyAgentInfo.get("IS_NB_AGENT")!=null && policyAgentInfo.get("IS_NB_AGENT").toString().equals("1")){
                            qryContractMasterCompBO.setSaleAgentCode(policyAgentInfo.get("AGENT_CODE").toString());
                            qryContractMasterCompBO.setSaleAgentName(policyAgentInfo.get("AGENT_NAME").toString());
                            qryContractMasterCompBO.setSaleAgentOrganCode(policyAgentInfo.get("AGENT_ORGAN_CODE").toString());
                            if (policyAgentInfo.get("AGENT_CHANNEL")!=null){
                            	qryContractMasterCompBO.setChannelType(policyAgentInfo.get("AGENT_CHANNEL").toString());
                            }else{
                            	qryContractMasterCompBO.setChannelType("");
                            }
                        }
                        if (policyAgentInfo.get("IS_CURRENT_AGENT")!=null && policyAgentInfo.get("IS_CURRENT_AGENT").toString().equals("1")){
                            qryContractMasterCompBO.setAgentCode(policyAgentInfo.get("AGENT_CODE").toString());
                            qryContractMasterCompBO.setAgentName(policyAgentInfo.get("AGENT_NAME").toString());
                            qryContractMasterCompBO.setAgentOrganCode(policyAgentInfo.get("AGENT_ORGAN_CODE").toString());
                        }
                        // 52031 中介机构号 中介机构名称 销售渠道为“个人中介渠道”
                        if(policyAgentInfo.get("AGENT_CHANNEL")!=null && policyAgentInfo.get("AGENT_CHANNEL").toString().equals(CONSTANT_AGENT_CHANNEL)){
                            if (policyAgentInfo.get("OTHER_MANAGE_COM")!=null){
                                qryContractMasterCompBO.setOtherManageCom(policyAgentInfo.get("OTHER_MANAGE_COM").toString());//中介机构代码
                            }
                            if (policyAgentInfo.get("OTHER_MANAGE_NAME")!=null){
                                qryContractMasterCompBO.setOtherManageName(policyAgentInfo.get("OTHER_MANAGE_NAME").toString());//中介机构代码
                            }
                            if(policyAgentInfo.get("PROTOCOL_ID")!=null){ //48816 投保书改版（2019年）需求
                            	qryContractMasterCompBO.setProtocolId(policyAgentInfo.get("PROTOCOL_ID").toString());//协议号
                            }
                        }
                    }
                }
	            LOGGER.debug("2-7:查询保单代理人耗时："+(end-start));
                LockPolicyDefCompPO lockPolicyDefCompPO = new LockPolicyDefCompPO();
                LockPolicyDefCompPO lockPolicyDefCompPO2 = new LockPolicyDefCompPO();
                LockPolicyDefCompPO lockPolicyDefCompPO3 = new LockPolicyDefCompPO();
                lockPolicyDefCompPO.setPolicyCode(contractMasterCompBO.getPolicyCode());
                start=DateUtilsEx.getTodayDate().getTime();
                List<LockPolicyDefCompPO> lockPolicyDefCompPOs = lockPolicyQueryDao
                        .queryLockPolicyInfo(lockPolicyDefCompPO);
                end=DateUtilsEx.getTodayDate().getTime();
	            LOGGER.debug("2-8:查询保单锁耗时："+(end-start));
	            boolean policyFreezeFlag = false;
                // qryContractMasterCompBO.setLockList(BeanUtils.copyList(LockPolicyDefCompBO.class,
                // lockPolicyDefCompPOs));
                qryContractMasterCompBO.setPolicyStatePlFlag("否");
                qryContractMasterCompBO.setPolicyStateCsFlag("否");
                qryContractMasterCompBO.setPolicyStateFzFlag("否");
                qryContractMasterCompBO.setPolicyStateHangUp("否");
                if (lockPolicyDefCompPOs.size() > 0) {
                    for (LockPolicyDefCompPO lockPolicyDefPO : lockPolicyDefCompPOs) {
                        if (Constants.POLICY_STATE_PL.compareTo(lockPolicyDefPO.getLockServiceId())==0) {
                            // 保单挂失
                            qryContractMasterCompBO.setPolicyStatePlFlag("是");
                        }
                        if (Constants.POLICY_STATE_CS.compareTo(lockPolicyDefPO.getLockServiceId())==0) {
                            // 保单质押第三方
                            qryContractMasterCompBO.setPolicyStateCsFlag("是");
                        } 
                        if (Constants.POLICY_STATE_FZ.compareTo(lockPolicyDefPO.getLockServiceId())==0) {
                            // 保单冻结
                            qryContractMasterCompBO.setPolicyStateFzFlag(" 是");
                            policyFreezeFlag = true;
                        } 
                    	if (lockPolicyDefPO.getLockServiceId()!=null && Constants.LOCK_SERVICE_TYPE_PROCESS.equals(lockPolicyDefPO.getLockServiceType())) {
                    		
//                		if (lockPolicyDefPO.getLockServiceId().equals(new BigDecimal(93))) {
                            // 是
                            qryContractMasterCompBO.setPolicyStateHangUp("是");
//                            if (lockPolicyDefPO.getExceptGroup1() == null) {
//                                lockPolicyDefCompPO2.setLockServiceName("保全挂起");
//                                lockPolicyDefCompPO2.setInsertTime(lockPolicyDefPO.getInsertTime());
//                            }
//                            if (lockPolicyDefPO.getExceptGroup2() == null) {
//                                lockPolicyDefCompPO3.setLockServiceName("续期挂起");
//                                lockPolicyDefCompPO3.setInsertTime(lockPolicyDefPO.getInsertTime());
//                            }
                        } 
                    }
                    if (lockPolicyDefCompPO2 != null) {
                        lockPolicyDefCompPOs.add(lockPolicyDefCompPO2);
                    }
                    if (lockPolicyDefCompPO3 != null) {
                        lockPolicyDefCompPOs.add(lockPolicyDefCompPO3);
                    }

                }
                if (!policyFreezeFlag) {
                    LockPolicyDefCompPO lockPolDefCompPO = new LockPolicyDefCompPO();
                    lockPolDefCompPO.setPolicyCode(contractMasterCompBO.getPolicyCode());
                    lockPolDefCompPO.getData().put("freeze_date" , WorkDateUtil.getWorkDate());
                    lockPolDefCompPO = lockPolicyQueryDao.queryPolicyFreezeInfo(lockPolDefCompPO);
                    if (lockPolDefCompPO != null && lockPolDefCompPO.get("policy_id") != null) {
                        qryContractMasterCompBO.setPolicyStateFzFlag(" 是");
                        lockPolDefCompPO.setLockServiceName("保单冻结状态");
                        lockPolDefCompPO.setInsertTime((Date)lockPolDefCompPO.get("freeze_date"));
                        lockPolicyDefCompPOs.add(lockPolDefCompPO);
                    }
                }
                if (lockPolicyDefCompPOs.size() > 0) {
                    qryContractMasterCompBO
                            .setLockList(BeanUtils.copyList(LockPolicyDefCompBO.class, lockPolicyDefCompPOs));
                }
            }
            // 查询客户信息
            // 投保人信息
            QryHolderInsuredCustCompPO holderCustomerCompPO = BeanUtils.copyProperties(
                    QryHolderInsuredCustCompPO.class, contractMasterCompBO);
            QryHolderInsuredCustCompPO holderCompPO = commonQueryDao.findHoderInfoByPolicyCode(holderCustomerCompPO);
            qryContractMasterCompBO.setCustomerName(holderCompPO.getCustomerName());
            qryContractMasterCompBO.setJobCode(holderCompPO.getJobCode());
            qryContractMasterCompBO.setCustomerCertiCode(holderCompPO.getCustomerCertiCode());
            qryContractMasterCompBO.setCustomerCertType(holderCompPO.getCustomerCertType());
            qryContractMasterCompBO.setCallResultSaveDate(callResultSaveDate);
            
            // 被保人信息
            List<QryHolderInsuredCustCompPO> insuredInfoList = commonQueryDao
                    .findInsuredInfoByPolicyCode(holderCustomerCompPO);
            qryContractMasterCompBO.setInsuredInfoList(BeanUtils.copyList(QryHolderInsuredCustCompBO.class,
                    insuredInfoList));
            // 受益人信息
            List<QryHolderInsuredCustCompPO> beneInfoList = commonQueryDao
                    .findBeneInfoByPolicyCode(holderCustomerCompPO);
            qryContractMasterCompBO.setBeneInfoList(BeanUtils.copyList(QryHolderInsuredCustCompBO.class, beneInfoList));
            qryContractMasterCompBO.setCallResultSaveDate(callResultSaveDate);   
            //查询保单附加险（关联附加险保单号，老客户关联关系：保单存在关联的附加险合同时，显示为“是”）
            QryContractMasterCompPO policyAccessoryPO = commonQueryDao.findPolicyAccessoryByPolicyCode(qryContractMasterCompPO);
            if(policyAccessoryPO != null && !StringUtilsEx.isNullOrEmpty(policyAccessoryPO.getSubPolicyCode())){
            	qryContractMasterCompBO.setSubPolicyCode(policyAccessoryPO.getSubPolicyCode());
            	qryContractMasterCompBO.setOldCustomerRelation("是");
            }else{
            	qryContractMasterCompBO.setSubPolicyCode("");
            	qryContractMasterCompBO.setOldCustomerRelation("");
            }
        
          //缴费信息#37569
            QryCommonQueryCompPO qryCommonQueryCompPO = BeanUtils.copyProperties(
            		QryCommonQueryCompPO.class, contractMasterCompBO);
            qryCommonQueryCompPO.setPolicyCode(contractMasterCompBO.getPolicyCode());
            qryCommonQueryCompPO = commonQueryDao.findMedicalInsuranceInfoByCode(qryCommonQueryCompPO);
            qryContractMasterCompBO.setMedicalPayOrder(qryCommonQueryCompPO.getMedicalPayOrder());
            qryContractMasterCompBO.setMedicalPayOrderName(qryCommonQueryCompPO.getMedicalPayOrderName());
            qryContractMasterCompBO.setMedicalNo(qryCommonQueryCompPO.getMedicalNo());
            qryContractMasterCompBO.setAccountBank(qryCommonQueryCompPO.getAccountBank());
            qryContractMasterCompBO.setAccountBankName(qryCommonQueryCompPO.getAccountBankName());
            qryContractMasterCompBO.setAccountId(qryCommonQueryCompPO.getAccountId());
            qryContractMasterCompBO.setMedicalPayOrderNext(qryCommonQueryCompPO.getMedicalPayOrderNext());
            qryContractMasterCompBO.setMedicalPayOrderNameNext(qryCommonQueryCompPO.getMedicalPayOrderNameNext());
            qryContractMasterCompBO.setMedicalNoNext(qryCommonQueryCompPO.getMedicalNoNext());
            qryContractMasterCompBO.setNextAccountBank(qryCommonQueryCompPO.getNextAccountBank());
            qryContractMasterCompBO.setNextAccountBankName(qryCommonQueryCompPO.getNextAccountBankName());
            qryContractMasterCompBO.setNextAccountName(qryCommonQueryCompPO.getNextAccountName());
            qryContractMasterCompBO.setNextAccountId(qryCommonQueryCompPO.getNextAccountId());
            if("".equals(qryCommonQueryCompPO.getAccountId()) || qryCommonQueryCompPO.getAccountId() == null || "".equals(qryCommonQueryCompPO.getNextAccountId()) || qryCommonQueryCompPO.getNextAccountId() == null){
            	qryContractMasterCompBO.setFirstCompareWithNext("");
            }else{
            	if((!"".equals(qryCommonQueryCompPO.getAccountId()) || qryCommonQueryCompPO.getAccountId() != null ) && (!"".equals(qryCommonQueryCompPO.getNextAccountId())) || qryCommonQueryCompPO.getNextAccountId() != null){
            		if(qryCommonQueryCompPO.getAccountId().equals(qryCommonQueryCompPO.getNextAccountId())){
                    	qryContractMasterCompBO.setFirstCompareWithNext("1");
                    }else{
                    	qryContractMasterCompBO.setFirstCompareWithNext("");
                    }
            	}else{
            		qryContractMasterCompBO.setFirstCompareWithNext("");
            	}
            }
            
            
			// #145849
			Boolean hasBeneTrust = false;// 受益人包含信托公司
			Boolean hasHolderTrust = false;// 投保人为信托公司
			ContractBenePO contractBenePO = new ContractBenePO();
			contractBenePO.setPolicyCode(qryContractMasterCompPO.getPolicyCode());
			List<ContractBenePO> contractBenePOs = paContractBeneDao.findAllContractBene(contractBenePO);
			for (ContractBenePO contractBene : contractBenePOs) {
				if (null != contractBene.getCompanyId() && !"".equals(contractBene.getCompanyId())) {
					hasBeneTrust = true;
				}
			}
			PolicyHolderPO policyHolderPo = new PolicyHolderPO();
			policyHolderPo.setPolicyCode(qryContractMasterCompPO.getPolicyCode());
			Integer count = PaPolicyHolderDao.findPolicyHolderIsTrust(policyHolderPo);
			if (count > 0) {
				hasHolderTrust = true;
			}
			if (hasBeneTrust && hasHolderTrust) {
				qryContractMasterCompBO.setTrustBusiFlagHB(Constants.TRUST_BUSI_FLAG_BH);
			} else if (hasBeneTrust) {
				qryContractMasterCompBO.setTrustBusiFlagHB(Constants.TRUST_BUSI_FLAG_B);
			} else {
				qryContractMasterCompBO.setTrustBusiFlagHB(Constants.TRUST_BUSI_FLAG_NBH);
			}
			// #145849
            // 10、查询保单的信托业务标识
            QryContractMasterCompPO  qryContractMasterCompPO1 = commonQueryDao.findContractMasterTrustBusiFlag(qryContractMasterCompPO);
            if(null != qryContractMasterCompPO1 && qryContractMasterCompPO1.getTrustBusiFlag() != null){
                qryContractMasterCompBO.setTrustBusiFlag(qryContractMasterCompPO1.getTrustBusiFlag());
            }else{
                qryContractMasterCompBO.setTrustBusiFlag(Constants.YES_NO__YES);
            }
            BigDecimal flag = contractMasterCompPO.getTrustBusiFlag();
          //设置信托业务标识
            qryContractMasterCompBO.setTrustBusiFlag(contractMasterCompPO.getTrustBusiFlag());
            //转投8462时，页面新契约出单形式为续保转投，SubinputType有值时，页面显示错误
            if(qryContractMasterCompBO.getSubmitChannel()!=null && qryContractMasterCompBO.getSubmitChannel().compareTo(new BigDecimal("9"))==0){
            	qryContractMasterCompBO.setSubinputType(null);
            }
            

            //#150224 查询保单打标信息表，查询睡眠保单相关信息
            if (contractMasterCompBO != null && contractMasterCompBO.getPolicyCode() != null) {
                QryPolicyMarkingInfoPO qryPolicyMarkingInfoPO = new QryPolicyMarkingInfoPO();
                qryPolicyMarkingInfoPO.setPolicyCode(contractMasterCompBO.getPolicyCode());
                List<QryPolicyMarkingInfoPO> qryPolicyMarkingInfoPOList = commonQueryDao.queryPolicyMarkingInfoByPolicyCode(qryPolicyMarkingInfoPO);
                qryContractMasterCompBO.setQryPolicyMarkingInfoList(BeanUtils.copyList(QryPolicyMarkingInfoBO.class, qryPolicyMarkingInfoPOList));
            }
            //#160232 支持互联网渠道新增慧择平台出单渠道信息查询需求-新契约 begin

            QryContractMasterCompPO qryContractMasterCompPOCPID = commonQueryDao.queryCooperationProtocolIdByPolicyCode(qryContractMasterCompPO);
            	if(qryContractMasterCompPOCPID != null){
            		if(StringUtils.isNotEmpty(qryContractMasterCompPOCPID.getCooperationProtocolId())){
            			qryContractMasterCompBO.setCooperationProtocolId(qryContractMasterCompPOCPID.getCooperationProtocolId());
            		}
            	}
              //#160232 支持互联网渠道新增慧择平台出单渠道信息查询需求-新契约   end
        return qryContractMasterCompBO;
    }
	
	
	
    
    /**
     * 
     * @description 查询信托公司详细信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#findBeneCustomerInfo(com.nci.tunan.qry.interfaces.model.bo.QryHolderInsuredCustCompBO)
     * @param holderInsuredCustCompBO 投被保人信息BO对象
     * @return
     */
    @Override
    public QryHolderInsuredCustCompBO findBeneCompanyInfo(QryHolderInsuredCustCompBO qBo) {
    	QryHolderInsuredCustCompPO hCompPO = commonQueryDao.findBeneCompanyInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompPO.class, qBo));
        return BeanUtils.copyProperties(QryHolderInsuredCustCompBO.class, hCompPO);
    }

    
    /**
     * 
     * @description 根据保单号查询客户信息(受益人信息（信托业务新增查询字段）)
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#findBeneCustomerInfo(com.nci.tunan.qry.interfaces.model.bo.QryHolderInsuredCustCompBO)
     * @param holderInsuredCustCompBO 投被保人信息BO对象
     * @return
     */
    @Override
    public List<QryHolderInsuredCustCompBO> findBeneForTrustCustomerInfo(QryHolderInsuredCustCompBO holderInsuredCustCompBO) {
        List<QryHolderInsuredCustCompPO> holderCustomerCompPOs = commonQueryDao.findBeneForTrustCustomerInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompPO.class, holderInsuredCustCompBO));
        return BeanUtils.copyList(QryHolderInsuredCustCompBO.class, holderCustomerCompPOs);
    }

	
	/**
	 * 
	 */
	@Override
	public QryContractMasterCompBO findRelationPolicyInfoByCode(
			QryContractMasterCompBO contractMasterCompBO) {
		LOGGER.debug("<======CommonQueryServiceImpl--findRelationPolicyInfoByCode======>");
        QryContractMasterCompPO qryContractMasterCompPO = BeanUtils.copyProperties(QryContractMasterCompPO.class,
                contractMasterCompBO);
        QryContractMasterCompBO qryContractMasterCompBO = new QryContractMasterCompBO();
        QryContractMasterCompPO contractMasterCompPO = new QryContractMasterCompPO();
        if(contractMasterCompBO != null && contractMasterCompBO.getPolicyCode() !=null){
            contractMasterCompPO = commonQueryDao.findRelationPolicyInfoByCode(qryContractMasterCompPO);
        }
        qryContractMasterCompBO = BeanUtils.copyProperties(QryContractMasterCompBO.class, contractMasterCompPO);
        return qryContractMasterCompBO;
	}

	/**
	 * 查询保单的简单信息，判断保单是否存在。 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#findPolicySimpleInfo(com.nci.tunan.qry.interfaces.model.vo.QryPolicyInfoVO)
	 * @param policyInfoVO
	 * @return
	 */
   public QryContractMasterCompBO findPolicySimpleInfo(QryContractMasterCompBO contractMasterCompBO){
       QryContractMasterCompPO qryContractMasterCompPO = BeanUtils.copyProperties(QryContractMasterCompPO.class,
               contractMasterCompBO);
       QryContractMasterCompBO qryContractMasterCompBO = new QryContractMasterCompBO();
       //判断网销的单子
       QryContractMasterCompPO contractMasterCompPO = new QryContractMasterCompPO();
       contractMasterCompPO = commonQueryDao.findPolicySimpleInfo(qryContractMasterCompPO);
       qryContractMasterCompBO = BeanUtils.copyProperties(QryContractMasterCompBO.class, contractMasterCompPO);
       return qryContractMasterCompBO;
   }
   /**
    * 通过受理号查询保单信息
    */
   @Override
	public QryContractMasterCompBO findCSAcceptCode(
			QryContractMasterCompBO contractMasterCompBO) {
	   QryContractMasterCompPO qryContractMasterCompPO = BeanUtils.copyProperties(QryContractMasterCompPO.class,
			   contractMasterCompBO);
	   QryContractMasterCompBO qryContractMasterCompBO = new QryContractMasterCompBO();
       //判断网销的单子
	   QryContractMasterCompPO contractMasterCompPO = new QryContractMasterCompPO();
	   contractMasterCompPO = commonQueryDao.findCSAcceptCode(qryContractMasterCompPO);
	   qryContractMasterCompBO = BeanUtils.copyProperties(QryContractMasterCompBO.class, contractMasterCompPO);
       return qryContractMasterCompBO;
	}
   
   /**
    * 根据赔案号查询理赔信息
    */
    @Override
	public QryClmQueryBO findClmCaseNo(QryClmQueryBO qryClmQueryBO) {
    	QryClmQueryPO qryClmQueryPO = BeanUtils.copyProperties(QryClmQueryPO.class,
    			qryClmQueryBO);
    	QryClmQueryBO qryClmQueryBOOne = new QryClmQueryBO();
         //判断网销的单子
    	//QryClmQueryPO qryClmQueryPO = new QryClmQueryPO();
    	qryClmQueryPO = commonQueryDao.findClmCaseNo(qryClmQueryPO);
    	qryClmQueryBOOne = BeanUtils.copyProperties(QryClmQueryBO.class, qryClmQueryPO);
    	//将姓名进行脱敏处理
		String oldCustomerName = qryClmQueryBOOne.getCustomerName();
		String newCustomer = "";
		if(oldCustomerName!=null && !oldCustomerName.isEmpty()){
			newCustomer = nameHyposensitization(oldCustomerName);
		}
		qryClmQueryBOOne.setCustomerName(newCustomer);
         return qryClmQueryBOOne;
	}
    @Override
    public List<QryHolderInsuredCustCompBO> findHoderCustomerInfo(QryHolderInsuredCustCompBO holderInsuredCustCompBO) {
    	long start=0;
        long end=0;
        start=DateUtilsEx.getTodayDate().getTime();
        List<QryHolderInsuredCustCompPO> holderCustomerCompPOs = commonQueryDao.findHoderCustomerInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompPO.class, holderInsuredCustCompBO));
        end=DateUtilsEx.getTodayDate().getTime();
        LOGGER.debug("3-1:查询投保人信息耗时："+(end-start));
        List<QryHolderInsuredCustCompBO> qryHolderInsuredCustCompBOs = BeanUtils.copyList(QryHolderInsuredCustCompBO.class, holderCustomerCompPOs);
        for(QryHolderInsuredCustCompBO qryHolderInsuredCustCompBO : qryHolderInsuredCustCompBOs){
        	//#145849
    		Boolean hasHolderTrust = false;// 投保人为信托公司
    		PolicyHolderPO policyHolderPo = new PolicyHolderPO();
    		policyHolderPo.setPolicyCode(qryHolderInsuredCustCompBO.getPolicyCode());
    		policyHolderPo.setCustomerId(qryHolderInsuredCustCompBO.getCustomerId());
    		Integer count = PaPolicyHolderDao.findPolicyHolderIsTrust(policyHolderPo);
    		if (count > 0) {
    			hasHolderTrust = true;
    		}
    		
    		if (hasHolderTrust) {
    			qryHolderInsuredCustCompBO.setTrustBusiFlagHB(Constants.TRUST_BUSI_FLAG_BH);
    		} else {
    			qryHolderInsuredCustCompBO.setTrustBusiFlagHB(Constants.TRUST_BUSI_FLAG_NBH);
    		}
    		//#145849
        }
        return qryHolderInsuredCustCompBOs;
    }

    @Override
    public List<QryHolderInsuredCustCompBO> findInsuredCustomerInfo(QryHolderInsuredCustCompBO holderInsuredCustCompBO) {
    	long start=0;
        long end=0;
        start=DateUtilsEx.getTodayDate().getTime();
        List<QryHolderInsuredCustCompPO> holderCustomerCompPOs = commonQueryDao.findInsuredCustomerInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompPO.class, holderInsuredCustCompBO));
        end=DateUtilsEx.getTodayDate().getTime();
        LOGGER.debug("3-2:查询被保人信息耗时："+(end-start));
        return BeanUtils.copyList(QryHolderInsuredCustCompBO.class, holderCustomerCompPOs);
    }

    @Override
    public List<QryHolderInsuredCustCompBO> findBeneCustomerInfo(QryHolderInsuredCustCompBO holderInsuredCustCompBO) {
    	long start=0;
        long end=0;
        start=DateUtilsEx.getTodayDate().getTime();
        List<QryHolderInsuredCustCompPO> holderCustomerCompPOs = commonQueryDao.findBeneCustomerInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompPO.class, holderInsuredCustCompBO));
        end=DateUtilsEx.getTodayDate().getTime();
        LOGGER.debug("3-3:查询受益人信息耗时："+(end-start));
        return BeanUtils.copyList(QryHolderInsuredCustCompBO.class, holderCustomerCompPOs);
    }

    /**
     * @description 获取保单状态最晚变更为失效的日期,预失效日期,保单年度
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyYear
     * @param preLapseDate
     * @date 2015-3-4 下午4:43:15
     * @param policyId
     * @return
     */
    public Date getLapseDate(BigDecimal busiItemId, Date preLapseDate, BigDecimal policyYear, BigDecimal policyID) {
        // 根据BusiItemId查询ContractExtendPO
        QryContractExtendPO contractExtendPO = new QryContractExtendPO();
        contractExtendPO.setBusiItemId(busiItemId);
        List<QryContractExtendPO> contractExtendPOs = contractExtendDao
                .findContractExtendByBusiItemId(contractExtendPO);

        if (contractExtendPOs != null && contractExtendPOs.size() > 0)
            policyYear = contractExtendPOs.get(0).getPolicyYear();

        Date dueDate = null;
        for (int i = 0; i < contractExtendPOs.size(); i++) {
            QryContractExtendPO poi = contractExtendPOs.get(i);
            for (int j = i + 1; j < contractExtendPOs.size(); j++) {
                QryContractExtendPO poj = contractExtendPOs.get(j);
                if (poi.getPayDueDate()/* DueDate() */.compareTo(poj.getPayDueDate()/*
                                                                                     * getDueDate
                                                                                     * (
                                                                                     * )
                                                                                     */) > 0) {// 获取最晚的应交日期
                    dueDate = poi.getPayDueDate()/* getDueDate() */;
                }
            }
        }
        preLapseDate = dueDate;
        QryContractMasterLogPO contractMasterLogPO = new QryContractMasterLogPO();
        // contractMasterLogPO.setPolicyId(contractExtendPOs.get(0).getPolicyId());
        contractMasterLogPO.setPolicyId(policyID);
        ;
        contractMasterLogPO.setLiabilityState(Constants.LIABILITY_STATE__LAPSED);// 保单状态变为失效
        contractMasterLogPO = contractMasterLogDao.findLiabilityStateChange(contractMasterLogPO);
        if (null != contractMasterLogPO && !contractMasterLogPO.getData().isEmpty()
                && contractMasterLogPO.getInsertTime().compareTo(DateUtilsEx.getTodayDate()) < 0) {
            return dueDate;
        }

        return null;
    }

//    public CurrentPage findCustomerInfos(QryPolicyAccountBO accountBO, CurrentPage currentPage) {
//        QryCustomerPO customerPO = new QryCustomerPO();
//        customerPO.set("account_type", accountBO.getAccountType());
//        customerPO.set("account_id", accountBO.getAccountId());
//        CurrentPage<QryCustomerPO> customerPOs = customerDao.queryCustomerForAccountForPage(customerPO, currentPage);
//        return BeanUtils.copyCurrentPage(QryCustomerBO.class, customerPOs);
//    }

    /**
     * @description 获取回执信息
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-1-26 上午9:33:01
     * @param contractMasterBO
     * @param contractMasterCompBO
     */
    private QryPolicyAcknowledgementBO getPolicyAcknowledgementBO(QryContractMasterBO contractMasterBO) {
        LOGGER.debug("<======CommonQueryServiceImpl--getPolicyAcknowledgementBO======>");
        // 调用policyAcknowledgementDao.findPolicyAcknowledgement()获取回执信息
        QryPolicyAcknowledgementPO policyAcknowledgementPO = new QryPolicyAcknowledgementPO();
        policyAcknowledgementPO.setPolicyId(contractMasterBO.getPolicyId());
        policyAcknowledgementPO = policyAcknowledgementDao.findPolicyAcknowledgement(policyAcknowledgementPO);
        // 回执信息属性值复制
        return BeanUtils.copyProperties(QryPolicyAcknowledgementBO.class, policyAcknowledgementPO);
    }

    /**
     * @description 查询客户作为保险角色投保人或被保人的保单列表
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-2-27 上午11:47:07
     * @see com.com.nci.tunan.qry.impl.qry.service.pa.impl.commonQuery.service.ICommonQueryService#findPolicyInfos(com.nci.tunan.qry.interfaces.model.qry.bo.QryCommonQueryCompBO.interfaces.model.bocomp.CommonQueryCompBO)
     * @param commonQueryCompBO
     * @return
     */
    public List<QryCommonQueryCompBO> findPolicyInfos(QryCommonQueryCompBO commonQueryCompBO) {
        LOGGER.debug("<======CommonQueryServiceImpl--findPolicyInfos======>");
        List<QryCommonQueryCompBO> commonQueryCompBOs = new ArrayList<QryCommonQueryCompBO>();
        if (commonQueryCompBO.getCustomerId() != null && !commonQueryCompBO.getCustomerId().equals(0)) {
            // 获取客户作为投保人的保单信息
            List<QryCommonQueryCompPO> holderPolicyInfos = commonQueryDao.findHolderPolicyInfos(BeanUtils
                    .copyProperties(QryCommonQueryCompPO.class, commonQueryCompBO));
            commonQueryCompBOs = BeanUtils.copyList(QryCommonQueryCompBO.class, holderPolicyInfos);
            for (QryCommonQueryCompBO bo : commonQueryCompBOs) {
                bo.setCustomerRoles(Constants.ROLE__POLICY_HOLDER_CHINESE);
            }
            // 获取客户作为被保人的保单信息
            List<QryCommonQueryCompPO> insuredPolicyInfos = commonQueryDao.findInsuredPolicyInfos(BeanUtils
                    .copyProperties(QryCommonQueryCompPO.class, commonQueryCompBO));
            for (QryCommonQueryCompBO bo : BeanUtils.copyList(QryCommonQueryCompBO.class, insuredPolicyInfos)) {
                bo.setCustomerRoles(Constants.ROLE__INSURED_CHINESE);
                commonQueryCompBOs.add(bo);
            }
            // 判断是否存在在同一保单中，同一客户既是投保人又是被保人
            // for(int i=0; i < commonQueryCompBOs.size(); i++){
            // CommonQueryCompBO boi = commonQueryCompBOs.get(i);
            // for(int j=i+1; j < commonQueryCompBOs.size(); j++){
            // CommonQueryCompBO boj = commonQueryCompBOs.get(j);
            // if(boi.getPolicyId().compareTo(boj.getPolicyId()) == 0){
            // if(!boi.getCustomerRoles().equals(boj.getCustomerRoles())){
            // boi.setCustomerRoles(boi.getCustomerRoles()+"/"+boj.getCustomerRoles());
            // }
            // commonQueryCompBOs.remove(j);
            // }
            // }
            // }
        } else {
            // 获取保单信息
            List<QryCommonQueryCompPO> contractMasterPOs = commonQueryDao.findAllCommonQueryComp(BeanUtils
                    .copyProperties(QryCommonQueryCompPO.class, commonQueryCompBO));
            commonQueryCompBOs = BeanUtils.copyList(QryCommonQueryCompBO.class, contractMasterPOs);
        }

        return commonQueryCompBOs;
    }

    /**
     * @description 险种信息页面---查询险种责任组列表信息
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-3-4 下午3:08:16
     * @see com.com.nci.tunan.qry.impl.qry.service.pa.impl.commonQuery.service.ICommonQueryService#findBusiItemInfos(com.nci.tunan.pa.interfaces.model.bo.ContractBusiProdBO)
     * @param contractBusiProdBO
     * @return
     */
    public List<QryBusiItemInfoCompBO> findBusiItemInfos(QryBusiItemInfoCompBO busiItemInfoCompBO) {
        LOGGER.debug("<======CommonQueryServiceImpl--findBusiItemInfos======>");
        long start=0;
        long end=0;
        QryBusiItemInfoCompPO busiItemInfoCompPO = BeanUtils.copyProperties(QryBusiItemInfoCompPO.class,
                busiItemInfoCompBO);
        start=DateUtilsEx.getTodayDate().getTime();
        List<QryBusiItemInfoCompPO> busiItemInfos = commonQueryDao.findBusiItemInfos(busiItemInfoCompPO);
        end=DateUtilsEx.getTodayDate().getTime();
        LOGGER.debug("4-1:查询险中信息耗时："+(end-start));
       List <QryBusiItemInfoCompBO> busiItemInfoBos = new ArrayList<QryBusiItemInfoCompBO>();
       
       Map<BigDecimal, String> findBusiItemInsureds = commonQueryDao.findBusiItemInsureds(busiItemInfoCompPO);
       if(busiItemInfos!=null && busiItemInfos.size()>0){
       for(QryBusiItemInfoCompPO po : busiItemInfos){
            QryBusiItemInfoCompBO bo =  BeanUtils.copyProperties(QryBusiItemInfoCompBO.class, po);
            bo.setInsuredNames(findBusiItemInsureds.get(bo.getBusiItemId()));
            busiItemInfoBos.add(bo);
        }
       }
        return busiItemInfoBos;
    }
    
    /**
     * 关联保单信息页面---查询关联保单信息
     */
    @Override
	public List<QryContractMasterCompBO> findRelationPolicyInfos(
			QryContractMasterCompBO qryContractMasterCompBO) {
    	LOGGER.debug("<======CommonQueryServiceImpl--findBusiItemInfos======>");
    	QryContractMasterCompPO qryContractMasterCompPO = BeanUtils.copyProperties(QryContractMasterCompPO.class,
        		qryContractMasterCompBO);
    	if(qryContractMasterCompBO.getBusiItemId() != null){
    		qryContractMasterCompPO.set("busi_item_id", qryContractMasterCompBO.getBusiItemId());
    	}
        List<QryContractMasterCompPO> qryContractMasterCompInfos = commonQueryDao.findRelationPolicyInfos(qryContractMasterCompPO);
       List <QryContractMasterCompBO> qryContractMasterCompInfoBos = new ArrayList<QryContractMasterCompBO>();
     
       if(qryContractMasterCompInfos!=null && qryContractMasterCompInfos.size()>0){
	       for(QryContractMasterCompPO po : qryContractMasterCompInfos){
	    	   QryContractMasterCompBO bo =  BeanUtils.copyProperties(QryContractMasterCompBO.class, po);
	            qryContractMasterCompInfoBos.add(bo);
	        }
       }
       return qryContractMasterCompInfoBos;
	}
    
    @Override
	public List<NbContractMasterBO> queryNotAutoProcess(
			NbContractMasterBO nbContractMasterBO) {
		LOGGER.debug("<======CommonQueryServiceImpl--queryNotAutoProcess======>");
		List<NbContractMasterPO> nbContractMasterPO = commonQueryDao.queryNotAutoProcess(BeanUtils
                .copyProperties(NbContractMasterPO.class, nbContractMasterBO));
		
		return BeanUtils.copyList(NbContractMasterBO.class, nbContractMasterPO);
	}
    
    public List<QryPolicyAccountBO> findPolicyAccountInfo(QryCommonQueryCompBO bo) {
        LOGGER.debug("<======CommonQueryServiceImpl--findPolicyAccountInfo======>");
        QryCommonQueryCompPO po = BeanUtils.copyProperties(QryCommonQueryCompPO.class, bo);
        List<QryPolicyAccountPO> listpos = commonQueryDao.findPolicyAccountInfo(po);
        return BeanUtils.copyList(QryPolicyAccountBO.class, listpos);
    }

    public List<Map<String, Object>> queryClmObject(QryCommonQueryCompBO taskbO) {
        QryCommonQueryCompPO po = BeanUtils.copyProperties(QryCommonQueryCompPO.class, taskbO);
        return commonQueryDao.queryClmObject(po);
    }

    /**
     * @description 查询保单信息
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-3-6 上午11:43:15
     * @see com.com.nci.tunan.qry.impl.qry.service.pa.impl.commonQuery.service.ICommonQueryService#findContractMaster(com.nci.tunan.qry.interfaces.model.qry.bo.QryContractMasterBO.interfaces.model.bo.ContractMasterBO)
     * @param contractMasterBO
     * @return
     */
    public QryContractMasterBO findContractMaster(QryContractMasterBO contractMasterBO) {
        LOGGER.debug("<======CommonQueryServiceImpl--findBusiItemInfos======>");
        QryContractMasterPO contractMasterPO = contractMasterDao.findContractMaster(BeanUtils.copyProperties(
                QryContractMasterPO.class, contractMasterBO));
        return BeanUtils.copyProperties(QryContractMasterBO.class, contractMasterPO);
    }

    /**
     * @description 根据客户姓名、客户生日、客户证件号查询客户基本信息
     */
    public CurrentPage findCustomerInfos(QryCustomerBO customerBO, CurrentPage currentPage) {
        LOGGER.debug("<======CommonQueryServiceImpl--findCustomerInfos======>");
        CurrentPage<QryCustomerPO> customerPOs = customerDao.queryCustomerForPage(
                BeanUtils.copyProperties(QryCustomerPO.class, customerBO), currentPage);
        return BeanUtils.copyCurrentPage(QryCustomerBO.class, customerPOs);
    }

    /**
     * 
     * @description
     * @version
     * @title
     * <AUTHOR>
     * @param bo
     * @param currentPage
     * @return
     */
    @Override
    public CurrentPage<QryUwBusiProdBO> findUwBusiProdInfo(QryUwBusiProdBO bo, CurrentPage<QryUwBusiProdBO> currentPage) {
    	QryUwBusiProdPO po = BeanUtils.copyProperties(QryUwBusiProdPO.class, bo);
    	QryContractMasterCompPO contractMasterCompPO = new QryContractMasterCompPO();
    	contractMasterCompPO.setApplyCode(bo.getApplyCode());
    	contractMasterCompPO.setPolicyCode(bo.getPolicyCode());
    	QryContractMasterCompPO contractMasterPO = commonQueryDao.findRelationPolicyInfoByCode(contractMasterCompPO);
        if(contractMasterPO.getDoubleMainRiskFlag() != null && contractMasterPO.getDoubleMainRiskFlag().compareTo(BigDecimal.ONE) == 0 ){
        	po.setApplyCode(contractMasterPO.getRelationApplyCode());
        }
        CurrentPage<QryUwBusiProdPO> Current = findUwBusiProdInfoDao.findUwBusiProdInfoForPage(po,
                BeanUtils.copyCurrentPage(QryUwBusiProdPO.class, currentPage));
        CurrentPage<QryUwBusiProdBO> curr = BeanUtils.copyCurrentPage(QryUwBusiProdBO.class, Current);
        return curr;
    }

    /**
     * @description 查询保单贷款信息
     * <AUTHOR>
     * @param po
     * @param currentPage
     * @return
     */
    @Override
    public CurrentPage findCSProductInfo(QryCommonQueryCompBO bo, CurrentPage currentPage) {
        QryCommonQueryCompPO po = new QryCommonQueryCompPO();
        po = BeanUtils.copyProperties(QryCommonQueryCompPO.class, bo);
        return commonQueryDao.findCSProductInfo(po, currentPage);
    }

    /**
     * @description 查询保全信息
     * <AUTHOR>
     * @param bo
     * @param currentPage
     * @return
     */
    @Override
    public CurrentPage findCSInfoList(QryCommonQueryCompBO bo, CurrentPage currentPage) {
        QryCommonQueryCompPO po = new QryCommonQueryCompPO();
        po = BeanUtils.copyProperties(QryCommonQueryCompPO.class, bo);
        return commonQueryDao.findCSInfoList(po, currentPage);
    }

    public IQryContractBeneDao getContractBeneDao() {
        return contractBeneDao;
    }

    public void setContractBeneDao(IQryContractBeneDao contractBeneDao) {
        this.contractBeneDao = contractBeneDao;
    }

    public IQryPolicyHolderDao getPolicyHolderDao() {
        return policyHolderDao;
    }

    public void setPolicyHolderDao(IQryPolicyHolderDao policyHolderDao) {
        this.policyHolderDao = policyHolderDao;
    }

    public IQryInsuredListDao getInsuredListDao() {
        return insuredListDao;
    }

    public void setInsuredListDao(IQryInsuredListDao insuredListDao) {
        this.insuredListDao = insuredListDao;
    }

    public IQryPayerAccountDao getPayerAccountDao() {
        return payerAccountDao;
    }

    public void setPayerAccountDao(IQryPayerAccountDao payerAccountDao) {
        this.payerAccountDao = payerAccountDao;
    }

    public IQryCustomerDao getCustomerDao() {
        return customerDao;
    }

    public void setCustomerDao(IQryCustomerDao customerDao) {
        this.customerDao = customerDao;
    }

    public IQryContractMasterDao getContractMasterDao() {
        return contractMasterDao;
    }

    public void setContractMasterDao(IQryContractMasterDao contractMasterDao) {
        this.contractMasterDao = contractMasterDao;
    }

    public IQryContractBusiProdDao getContractBusiProdDao() {
        return contractBusiProdDao;
    }

    public void setContractBusiProdDao(IQryContractBusiProdDao contractBusiProdDao) {
        this.contractBusiProdDao = contractBusiProdDao;
    }

    public IQryContractProductDao getContractProductDao() {
        return contractProductDao;
    }

    public void setContractProductDao(IQryContractProductDao contractProductDao) {
        this.contractProductDao = contractProductDao;
    }

    public IQryContractExtendDao getContractExtendDao() {
        return contractExtendDao;
    }

    public void setContractExtendDao(IQryContractExtendDao contractExtendDao) {
        this.contractExtendDao = contractExtendDao;
    }

    public IQryPolicyAcknowledgementDao getPolicyAcknowledgementDao() {
        return policyAcknowledgementDao;
    }

    public void setPolicyAcknowledgementDao(IQryPolicyAcknowledgementDao policyAcknowledgementDao) {
        this.policyAcknowledgementDao = policyAcknowledgementDao;
    }

    public IQryCommonQueryDao getCommonQueryDao() {
        return commonQueryDao;
    }

    public void setCommonQueryDao(IQryCommonQueryDao commonQueryDao) {
        this.commonQueryDao = commonQueryDao;
    }

    public IQryContractMasterLogDao getContractMasterLogDao() {
        return contractMasterLogDao;
    }

    public void setContractMasterLogDao(IQryContractMasterLogDao contractMasterLogDao) {
        this.contractMasterLogDao = contractMasterLogDao;
    }

    public IQryPolicyOperationDao getPolicyOperationDao() {
        return policyOperationDao;
    }

    public void setPolicyOperationDao(IQryPolicyOperationDao policyOperationDao) {
        this.policyOperationDao = policyOperationDao;
    }

    public IQryPolicyStatusDao getPolicyStatusDao() {
        return policyStatusDao;
    }

    public void setPolicyStatusDao(IQryPolicyStatusDao policyStatusDao) {
        this.policyStatusDao = policyStatusDao;
    }

    public IQryFindUwBusiProdInfoDao getFindUwBusiProdInfoDao() {
        return findUwBusiProdInfoDao;
    }

    public void setFindUwBusiProdInfoDao(IQryFindUwBusiProdInfoDao findUwBusiProdInfoDao) {
        this.findUwBusiProdInfoDao = findUwBusiProdInfoDao;
    }

    public ILockPolicyQueryDao getLockPolicyQueryDao() {
        return lockPolicyQueryDao;
    }

    public void setLockPolicyQueryDao(ILockPolicyQueryDao lockPolicyQueryDao) {
        this.lockPolicyQueryDao = lockPolicyQueryDao;
    }

    @Override
    public List<QryProposalProcessBO> findNBuwInfo(QryProposalProcessBO bo) {
        QryProposalProcessPO po = new QryProposalProcessPO();
        List<QryProposalProcessPO> listPO = new ArrayList<QryProposalProcessPO>();
        po = BeanUtils.copyProperties(QryProposalProcessPO.class, bo);
        listPO = commonQueryDao.findNBuwInfo(po);
        return BeanUtils.copyList(QryProposalProcessBO.class, listPO);
    }

    @Override
    public List<QryProposalProcessBO> findNBinsInfo(QryProposalProcessBO bo) {
        QryProposalProcessPO po = new QryProposalProcessPO();
        List<QryProposalProcessPO> listPO = new ArrayList<QryProposalProcessPO>();
        po = BeanUtils.copyProperties(QryProposalProcessPO.class, bo);
        listPO = commonQueryDao.findNBinsInfo(po);
        return BeanUtils.copyList(QryProposalProcessBO.class, listPO);
    }

    /**
	 * 获取新契约履历带分页
	 * @param bo
	 * @param currentPage
	 * @return
	 */
    @Override
	public CurrentPage findNBProcessPageInfo(QryProposalProcessBO bo,CurrentPage currentPage){
    	QryProposalProcessPO po=BeanUtils.copyProperties(QryProposalProcessPO.class, bo);
    	 return commonQueryDao.findNBProcessPageInfo(po, currentPage);
	}
    /**
     * 根据投保单号查询
     */
    @Override
	public List<QryProposalProcessBO> findNBApplyCode(QryProposalProcessBO bo) {
    	QryProposalProcessPO po = new QryProposalProcessPO();
        List<QryProposalProcessPO> listPO = new ArrayList<QryProposalProcessPO>();
        po = BeanUtils.copyProperties(QryProposalProcessPO.class, bo);
        listPO = commonQueryDao.findNBApplyCode(po);
        return BeanUtils.copyList(QryProposalProcessBO.class, listPO);
	}
    @Override
    public List<QryProposalProcessBO> findNBProcessInfo(QryProposalProcessBO bo) {
        QryProposalProcessPO po = new QryProposalProcessPO();
        List<QryProposalProcessPO> listPO = new ArrayList<QryProposalProcessPO>();
        po = BeanUtils.copyProperties(QryProposalProcessPO.class, bo);
        listPO = commonQueryDao.findNBProcessInfo(po);
        return BeanUtils.copyList(QryProposalProcessBO.class, listPO);
    }

    @Override
    public List<QryDocumentBO> findNBDocumentInfo(QryDocumentBO bo) {
        QryDocumentPO po = new QryDocumentPO();
        po = BeanUtils.copyProperties(QryDocumentPO.class, bo);
        List<QryDocumentPO> listPO = new ArrayList<QryDocumentPO>();
        po.set("apply_code", bo.getApplyCode());
        listPO = commonQueryDao.findNBDocumentInfo(po);
        return BeanUtils.copyList(QryDocumentBO.class, listPO);
    }

    @Override
    public CurrentPage<QryCommonPolicyInfoBO> findPolicyInfoByCustomerId(QryCommonPolicyInfoBO commonPolicyInfoBO,
            CurrentPage<QryCommonPolicyInfoBO> currentPage) {
        QryCommonPolicyInfoCompPO commonPolicyInfoCompPO = BeanUtils.copyProperties(QryCommonPolicyInfoCompPO.class,
                commonPolicyInfoBO);
        CurrentPage<QryCommonPolicyInfoCompPO> cuur = commonQueryDao.findPolicyInfoByCustomerId(commonPolicyInfoCompPO,
                BeanUtils.copyCurrentPage(QryCommonPolicyInfoCompPO.class, currentPage));
        return BeanUtils.copyCurrentPage(QryCommonPolicyInfoBO.class, cuur);
    }
    
    @Override
    public CurrentPage<QryCommonPolicyInfoBO> findPolicyInfoByCustomerIdOne(QryCommonPolicyInfoBO commonPolicyInfoBO,
            CurrentPage<QryCommonPolicyInfoBO> currentPage) {
        QryCommonPolicyInfoCompPO commonPolicyInfoCompPO = BeanUtils.copyProperties(QryCommonPolicyInfoCompPO.class,
                commonPolicyInfoBO);
        CurrentPage<QryCommonPolicyInfoCompPO> cuur = commonQueryDao.findPolicyInfoByCustomerIdOne(commonPolicyInfoCompPO,
                BeanUtils.copyCurrentPage(QryCommonPolicyInfoCompPO.class, currentPage));
        return BeanUtils.copyCurrentPage(QryCommonPolicyInfoBO.class, cuur);
    }
    
    public List<QryCommonPolicyInfoBO> findPolicyInfoByCustomerId(QryCommonPolicyInfoBO commonPolicyInfoBO){
        QryCommonPolicyInfoCompPO commonPolicyInfoCompPO = BeanUtils.copyProperties(QryCommonPolicyInfoCompPO.class,
                commonPolicyInfoBO);
        List<QryCommonPolicyInfoCompPO> cuur = commonQueryDao.findPolicyInfoByCustomerId(commonPolicyInfoCompPO);
        return BeanUtils.copyList(QryCommonPolicyInfoBO.class, cuur);
    }

    @Override
    public QryCommonPolicyInfoBO findCustomerInfoById(QryCommonPolicyInfoBO commonPolicyInfoBO) {
        QryCommonPolicyInfoCompPO commonPolicyInfoCompPO = commonQueryDao.findCustomerInfoById(BeanUtils
                .copyProperties(QryCommonPolicyInfoCompPO.class, commonPolicyInfoBO));
        return BeanUtils.copyProperties(QryCommonPolicyInfoBO.class, commonPolicyInfoCompPO);
    }

    @Override
    public AgentBO findAgentByAgentCode(AgentBO agentBO) {
        AgentPO agentPO = commonQueryDao.findAgentByAgentCode(BeanUtils.copyProperties(AgentPO.class, agentBO));
        return BeanUtils.copyProperties(AgentBO.class, agentPO);
    }

    @Override
    public CurrentPage<QryCommonPolicyInfoBO> findPolicyInfoByagentCode(QryCommonPolicyInfoBO commonPolicyInfoBO,
            CurrentPage<QryCommonPolicyInfoBO> currentPage) {
        QryCommonPolicyInfoCompPO commonPolicyInfoCompPO = BeanUtils.copyProperties(QryCommonPolicyInfoCompPO.class,
                commonPolicyInfoBO);
        CurrentPage<QryCommonPolicyInfoCompPO> cuur = commonQueryDao.findPolicyInfoByagentCode(commonPolicyInfoCompPO,
                BeanUtils.copyCurrentPage(QryCommonPolicyInfoCompPO.class, currentPage));
        return BeanUtils.copyCurrentPage(QryCommonPolicyInfoBO.class, cuur);
    }

    @Override
    public CurrentPage<QryPremArapBO> findPremArapListInfo(QryPremArapBO bo,CurrentPage<QryPremArapBO> currentPage) {
    	QryPremArapPO po = BeanUtils.copyProperties(QryPremArapPO.class, bo);
    	//上海医保单独处理
    	QryPremArapBO shBO = this.checkSHPolicyInfo(bo);
    	//页面增加被保人显示
    	CurrentPage<QryPremArapPO> qryPremArapPage = null;
    	CurrentPage<QryPremArapBO> copyCurrentPage = null;
    	Boolean flag = false;
    	//查询保单下是否有多个被保险人
    	if(!StringUtilsEx.isBlank(po.getPolicyCode())){
    		InsuredListBO insuredListBo = new InsuredListBO();
    		insuredListBo.setPolicyCode(po.getPolicyCode());
    		flag = IsMultipleInsuredPolicys(insuredListBo);    		
    	}
    	if(flag){
    		if(null != shBO && !StringUtilsEx.isBlank(shBO.getApplyCode())){
        		qryPremArapPage = commonQueryDao.findSHPremArapListInfossForPage(po,BeanUtils.copyCurrentPage(QryPremArapPO.class, currentPage));
        	}else{
        		qryPremArapPage = commonQueryDao.findPremArapListInfossForPage(po,BeanUtils.copyCurrentPage(QryPremArapPO.class, currentPage));
        	}
    		copyCurrentPage = BeanUtils.copyCurrentPage(QryPremArapBO.class, qryPremArapPage);
    		List<QryPremArapBO> qryPremArapList = new ArrayList<QryPremArapBO>();
            //查询结果处理
            if (qryPremArapPage != null) {
                List<QryPremArapPO> pageItems = qryPremArapPage.getPageItems();
                for (QryPremArapPO qryPremArapPage2 : pageItems) {
                	QryPremArapBO qryPremArapBo = new QryPremArapBO();
                	//险种名称
                	if(qryPremArapPage2.getBusiProdName() != null){
                		qryPremArapBo.setBusiProdName(qryPremArapPage2.getBusiProdName());
                	}
                	//收付标志
                	if(qryPremArapPage2.getArapFlag() != null){
                		qryPremArapBo.setArapFlag(qryPremArapPage2.getArapFlag());
                	}
                	//业务号
                	if(qryPremArapPage2.getBusinessCode() != null){
                		qryPremArapBo.setBusinessCode(qryPremArapPage2.getBusinessCode());
                	}
                	//操作日期
                	if(qryPremArapPage2.getArapDate() != null){
                		qryPremArapBo.setArapDate(qryPremArapPage2.getArapDate());
                	}
                	//收付日期
                	if(qryPremArapPage2.getFinishTime() != null){
                		qryPremArapBo.setFinishTime(qryPremArapPage2.getFinishTime());
                	}
                	//收付机构
                	if(qryPremArapPage2.getCapOrganCode() != null){
                		qryPremArapBo.setCapOrganCode(qryPremArapPage2.getCapOrganCode());
                	}
                	//业务类型
                	if(qryPremArapPage2.getBusinessType() != null){
                		qryPremArapBo.setBusinessType(qryPremArapPage2.getBusinessType());
                	}
                	//收付方式
                	if(qryPremArapPage2.getFeeStatus() != null){
                		qryPremArapBo.setFeeStatus(qryPremArapPage2.getFeeStatus());
                	}
                	//金额
                	if(qryPremArapPage2.getFeeAmount() != null){
                		qryPremArapBo.setFeeAmount(qryPremArapPage2.getFeeAmount());
                	}
                	//状态
                	if(qryPremArapPage2.getDerivType() != null){
                		qryPremArapBo.setDerivType(qryPremArapPage2.getDerivType());
                	}
                	//收付方式
                	if(qryPremArapPage2.getPayMode() != null){
                		qryPremArapBo.setPayMode(qryPremArapPage2.getPayMode());
                	}
                	if(qryPremArapPage2.getUnitNumber() != null){
                		qryPremArapBo.setUnitNumber(qryPremArapPage2.getUnitNumber());
                	}
                	//保单号
                	if(qryPremArapPage2.getPolicyCode() != null){
                		qryPremArapBo.setPolicyCode(qryPremArapPage2.getPolicyCode());
                	}
                	if(qryPremArapPage2.getPayModeCode() != null){
                		qryPremArapBo.setPayModeCode(qryPremArapPage2.getPayModeCode());
                	}
                	if(qryPremArapPage2.getBusiProdCode() != null){
                		qryPremArapBo.setBusiProdCode(qryPremArapPage2.getBusiProdCode());
                	}
                	if(qryPremArapPage2.getPolicyCode() != null && qryPremArapPage2.getData().get("busi_item_id") != null){            		
                		//查询险种被保人
                    	BenefitInsuredPO benefitInsuredPO = new BenefitInsuredPO();
                    	benefitInsuredPO.setPolicyCode(qryPremArapPage2.getPolicyCode());
                    	benefitInsuredPO.setBusiItemId(new BigDecimal(qryPremArapPage2.getData().get("busi_item_id").toString()));
                    	List<BenefitInsuredPO> benefitInsuredPOList = benefitInsuredDao.findBenefitInsuredNameByCaseId(benefitInsuredPO);
                    	if(benefitInsuredPOList != null && benefitInsuredPOList.size() > ClaimConstant.ZERO){                 		             
                    		qryPremArapBo.setInsuredName(benefitInsuredPOList.get(0).getData().get("insured_names") == null ? "" : benefitInsuredPOList.get(0).getData().get("insured_names").toString());
                    	}	
                	}
                	qryPremArapList.add(qryPremArapBo);
                }
            }
            copyCurrentPage.setPageItems(qryPremArapList);
    	}else{
    		if(null != shBO && !StringUtilsEx.isBlank(shBO.getApplyCode())){
        		qryPremArapPage = commonQueryDao.findSHPremArapListInfoForPage(po,BeanUtils.copyCurrentPage(QryPremArapPO.class, currentPage));
        	}else{
        		qryPremArapPage = commonQueryDao.findPremArapListInfoForPage(po,BeanUtils.copyCurrentPage(QryPremArapPO.class, currentPage));
        	}
    		copyCurrentPage = BeanUtils.copyCurrentPage(QryPremArapBO.class, qryPremArapPage);
    		//查询结果处理
            if (copyCurrentPage != null) {
                List<QryPremArapBO> pageItems = copyCurrentPage.getPageItems();
                for (QryPremArapBO qryPremArapPage2 : pageItems) {
                	if(qryPremArapPage2.getPolicyCode() != null && qryPremArapPage2.getBusiProdCode() != null){            		
                		//查询险种被保人
                    	BenefitInsuredPO benefitInsuredPO = new BenefitInsuredPO();
                    	benefitInsuredPO.setPolicyCode(qryPremArapPage2.getPolicyCode());
                    	benefitInsuredPO.getData().put("busi_prod_code", qryPremArapPage2.getBusiProdCode());
                    	List<BenefitInsuredPO> benefitInsuredPOList = benefitInsuredDao.findBenefitInsuredNameByBusiProdCode(benefitInsuredPO);
                    	if(benefitInsuredPOList != null && benefitInsuredPOList.size() > ClaimConstant.ZERO){                 		             
                    		qryPremArapPage2.setInsuredName(benefitInsuredPOList.get(0).getData().get("insured_names") == null ? "" : benefitInsuredPOList.get(0).getData().get("insured_names").toString());
                    	}	
                	}
                	
                }
            }
    	}    	  	       
        return copyCurrentPage;
    }

    @Override
    public QryPremArapBO findPremArapDetailInfo(QryPremArapBO bo) {
        QryPremArapPO po = BeanUtils.copyProperties(QryPremArapPO.class, bo);
        QryPremArapPO po2 = commonQueryDao.findPremArapDetailInfo(po);
        return BeanUtils.copyProperties(QryPremArapBO.class, po2);
    }

    @Override
    public CurrentPage getPayPlanInfoPage(QryPayPlanDueBO bo, CurrentPage currentPage) {
        QryPayPlanDuePO po = BeanUtils.copyProperties(QryPayPlanDuePO.class, bo);
        CurrentPage c = BeanUtils.copyCurrentPage(QryPayPlanDuePO.class, currentPage);
        CurrentPage c2 = commonQueryDao.getPayPlanInfoPage(po, c);
        CurrentPage c1 = BeanUtils.copyCurrentPage(QryPayPlanDueBO.class, c2);
        return c1;
    }

    @Override
    public CurrentPage<Object> getPayPayDuePage(QryPayPlanDueBO bo, CurrentPage currentPage) {
        QryPayPlanDuePO po = BeanUtils.copyProperties(QryPayPlanDuePO.class, bo);
        CurrentPage c = BeanUtils.copyCurrentPage(QryPayPlanDuePO.class, currentPage);
        CurrentPage c2 = commonQueryDao.getPayPayDuePage(po, c);
        CurrentPage c1 = BeanUtils.copyCurrentPage(QryPayPlanDueBO.class, c2);
        return c1;
    }

    @Override
    public CurrentPage<Object> getPayPayDueDetailPage(QryPayPlanDueBO bo, CurrentPage currentPage) {
        QryPayPlanDuePO po = BeanUtils.copyProperties(QryPayPlanDuePO.class, bo);
        CurrentPage c = BeanUtils.copyCurrentPage(QryPayPlanDuePO.class, currentPage);
        CurrentPage c2 = commonQueryDao.getPayPayDueDetailPage(po, c);
        CurrentPage c1 = BeanUtils.copyCurrentPage(QryPayPlanDueBO.class, c2);
        return c1;
    }

    @Override
    public CurrentPage<CustomerAccouontBO> findCustomersByCustomerAccount(CustomerAccouontBO customerAccouontBO,
            CurrentPage<CustomerAccouontBO> currentPage) {
        CustomerAccountPO customerAccountPO = BeanUtils.copyProperties(CustomerAccountPO.class, customerAccouontBO);
        CurrentPage<CustomerAccountPO> currPage = commonQueryDao.findCustomersByCustomerAccount(customerAccountPO,
                BeanUtils.copyCurrentPage(CustomerAccountPO.class, currentPage));
        return BeanUtils.copyCurrentPage(CustomerAccouontBO.class, currPage);
    }

    @Override
    public QryProposalProcessBO findNBPolicyCode(QryProposalProcessBO bo) {
        QryProposalProcessPO po = new QryProposalProcessPO();
        po = BeanUtils.copyProperties(QryProposalProcessPO.class, bo);
        po = commonQueryDao.findNBPolicyCode(po);
        return BeanUtils.copyProperties(QryProposalProcessBO.class, po);
    }

    // 获取投保万连客户基本信息
    @Override
    public CurrentPage<Object> getAccountInfoPage(QryAccountBO bo, CurrentPage accountPage) {
        // TODO Auto-generated method stub
        QryAccountPO po = new QryAccountPO();
        po = BeanUtils.copyProperties(QryAccountPO.class, bo);
        CurrentPage currPage = commonQueryDao.getAccountInfoPage(po, accountPage);
        return currPage;
    }

    // 获取万连账户运行轨迹
    @Override
    public CurrentPage<Object> getAccountPathInfoPage(QryAccountBO bo, CurrentPage currentPage) {
        // TODO Auto-generated method stub
    	CurrentPage currPage = null;
    	String accountTypeFlag = "1";
    	if(bo.getInvestAccountType() != null){
    		if(bo.getInvestAccountType().compareTo(BigDecimal.ONE) == 0){
    			accountTypeFlag = "1";
    		}else {
    			accountTypeFlag = "2";
			}
    	}
        QryAccountPO po = new QryAccountPO();
        po = BeanUtils.copyProperties(QryAccountPO.class, bo);
        if("1".equals(accountTypeFlag)){
        	currPage = commonQueryDao.getUnitLinkedAccountPathInfoPage(po, currentPage);
        }else if("2".equals(accountTypeFlag)) {
        	currPage = commonQueryDao.getAccountPathInfoPage(po, currentPage);
		}
        return currPage;
    }

    // @Override
    // public NbContractMasterBO selectOne(NbContractMasterBO
    // nbContractMasterBO)
    // throws BizException {
    // NbContractMasterPO nbContractMasterPO = new NbContractMasterPO();
    // nbContractMasterPO = BeanUtils.copyProperties(NbContractMasterPO.class,
    // nbContractMasterBO);
    // NbContractMasterPO po = commonQueryDao
    // .findNbContractMaster(nbContractMasterPO);
    // nbContractMasterBO = BeanUtils.copyProperties(NbContractMasterBO.class,
    // po);
    // nbContractMasterBO.setApplyDate((Date) po.get("applyDate"));
    // return nbContractMasterBO;
    // }

    @Override
    public ContractAgentBO findAgentInfos(ContractAgentBO contractAgentBO) throws BizException {
        ContractAgentPO contractAgentPO = new ContractAgentPO();
        BeanUtils.copyProperties(contractAgentPO, contractAgentBO);
        contractAgentPO = commonQueryDao.findContractAgent(contractAgentPO);
        BeanUtils.copyProperties(contractAgentBO, contractAgentPO);
        return contractAgentBO;
    }

    @Override
    public OrgRelBO findByOrgenCode(OrgRelBO orgRelBO) throws BizException {
        OrgRelPO orgRelPO = new OrgRelPO();
        BeanUtils.copyProperties(orgRelPO, orgRelBO);
        orgRelPO = commonQueryDao.findOrgRelByOrgenCode(orgRelPO);
        BeanUtils.copyProperties(orgRelBO, orgRelPO);
        return orgRelBO;
    }

    @Override
    public CurrentPage<QryAccountBO> getLiveAccountPage(QryAccountBO bo, CurrentPage<QryAccountBO> accountPage) {
        QryAccountPO qryAccountPO = BeanUtils.copyProperties(QryAccountPO.class, bo);

        CurrentPage<QryAccountPO> currPage = commonQueryDao.getLiveAccountPage(qryAccountPO,
                BeanUtils.copyCurrentPage(QryAccountPO.class, accountPage));
        return BeanUtils.copyCurrentPage(QryAccountBO.class, currPage);
    }

    /**
     * 累计生息账户变动轨迹
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param bo
     * @param accountPage
     * @return
     */
    @Override
    public CurrentPage<QryAccountBO> getLiveAccountPathPage(QryAccountBO bo, CurrentPage<QryAccountBO> accountPage) {
        QryAccountPO qryAccountPO = BeanUtils.copyProperties(QryAccountPO.class, bo);

        CurrentPage<QryAccountPO> currPage = commonQueryDao.getLiveAccountPathPage(qryAccountPO,
                BeanUtils.copyCurrentPage(QryAccountPO.class, accountPage));
        return BeanUtils.copyCurrentPage(QryAccountBO.class, currPage);
    }
    @Override
    public CurrentPage<Object> getSinceMatPage(QryAccountBO bo, CurrentPage currentPage) {
        QryAccountPO po = new QryAccountPO();
        po = BeanUtils.copyProperties(QryAccountPO.class, bo);
        CurrentPage currPage = commonQueryDao.getSinceMatPage(po, currentPage);
        return currPage;
    }

    @Override
    public CurrentPage<Object> getPasswordPath(QueryPolicyPasswordPathBO queryPolicyPasswordPathbo,
            CurrentPage currentPage) {
        // TODO Auto-generated method stub
        QueryPolicyPasswordPathPO po = new QueryPolicyPasswordPathPO();
        po = BeanUtils.copyProperties(QueryPolicyPasswordPathPO.class, queryPolicyPasswordPathbo);
        CurrentPage currPage = commonQueryDao.getPasswordPath(po, currentPage);
        return currPage;
    }

    
    @Override
    public CurrentPage<QueryPolicyBonusBO> getPolicyBonusPage( QueryPolicyBonusBO queryBonusBo, CurrentPage<QueryPolicyBonusBO> currentPage) {
        QueryPolicyBonusPO po = new QueryPolicyBonusPO();
        po = BeanUtils.copyProperties(QueryPolicyBonusPO.class, queryBonusBo);
        CurrentPage<QueryPolicyBonusPO> currPage = 
        		commonQueryDao.getPolicyBonusPage(po, BeanUtils.copyCurrentPage(QueryPolicyBonusPO.class, currentPage));
        return BeanUtils.copyCurrentPage(QueryPolicyBonusBO.class, currPage);
    }
    @Override
    public QueryPolicyBonusBO getBonusDetailPage(QueryPolicyBonusBO queryBonusBo) {
        QueryPolicyBonusPO queryPolicyBonusPO =  commonQueryDao.getBonusDetailPage(BeanUtils.copyProperties(QueryPolicyBonusPO.class, queryBonusBo));
        return BeanUtils.copyProperties(QueryPolicyBonusBO.class, queryPolicyBonusPO);
    }
    @Override
    public CurrentPage getReissuedPrint(QryReissuedPrintBO bo, CurrentPage currentPage) {
        QryReissuedPrintPO po = BeanUtils.copyProperties(QryReissuedPrintPO.class, bo);
        return commonQueryDao.getReissuedPrint(po, currentPage);
    }

    /**
     * 贷款险种信息
     */
    @Override
    public List<CsPolicyInfoChangeThreebPO> findCsPolicyInfoChangeThreebPO(
            CsPolicyInfoChangeThreebPO csPolicyInfoChangeThreebPO) {
        List<CsPolicyInfoChangeThreebPO> pos = commonQueryDao.findCsPolicyInfoChangeThreeb(csPolicyInfoChangeThreebPO);
        if (pos==null) {
            return pos;
        } else {
            QryPaContractBusiProdPO contractBusiProd = new QryPaContractBusiProdPO();
            for (int i = 0; i < pos.size(); i++) {
                contractBusiProd.setPolicyId(pos.get(i).getPolicyId());
                contractBusiProd.setBusiItemId(pos.get(i).getBusiItemId());
                List<QryPaContractBusiProdPO> contractBusiProds = commonQueryDao.findAllContractBusiProd(contractBusiProd);
                
                if (contractBusiProds != null && contractBusiProds.size() > 0) {
                	
                    pos.get(i).setBusiPrdId(contractBusiProds.get(0).getBusiPrdId());
                    
                    PolicyLoanSetMsgVO policyLoanSetMsgVO = queryLoanSetMsg(
                    		contractBusiProd.getPolicyId(),
                    		contractBusiProd.getBusiItemId(), 
                    		contractBusiProds.get(0).getBusiPrdId(),
                            WorkDateUtil.getWorkDate(), null,pos.get(i).getStreamId());
                    
                    // 初始利率、逾期利率、二阶段利率、三阶段利率initialRate
                    pos.get(i).setinitialRate(
                    		policyLoanSetMsgVO.getRateValue() == null ? new BigDecimal(0) : policyLoanSetMsgVO.getRateValue());
                    pos.get(i).setoverdueRate(
                            policyLoanSetMsgVO.getOverDueRateValue() == null ? new BigDecimal(0) : policyLoanSetMsgVO.getOverDueRateValue());
                    pos.get(i).setsecondRate(
                            policyLoanSetMsgVO.getSecondStageRateVal() == null ? new BigDecimal(0) : policyLoanSetMsgVO.getSecondStageRateVal());
                    pos.get(i).setthirdRate(
                            policyLoanSetMsgVO.getThirdStageRateVal() == null ? new BigDecimal(0) : policyLoanSetMsgVO.getThirdStageRateVal());
                }
            }
            return pos;
        }
    }

    /**
     * @description 保单层、险种层贷款要素利率配置查询 规则：查询保单层、险种层最新的配置 若没有 使用产品的配置
     * @param policyId
     *            为保单Id；calculateDate:时间段内的；timePeridoCode：贷款要素时间分段代码，
     *            在Constants中LOAN_FACTOR_PERIOD_CODE_；
     * <AUTHOR>
     * @return PolicyLoanSetMsgVO
     */
    public PolicyLoanSetMsgVO queryLoanSetMsg(BigDecimal policyId, BigDecimal busiItemId, BigDecimal busiPrdId,
            Date calculateDate, BigDecimal changeId,BigDecimal streamId) {
        // TODO Auto-generated method stub
        PolicyLoanSetMsgVO policyLoanSetMsgVO = new PolicyLoanSetMsgVO();
        policyLoanSetMsgVO.setPolicyId(policyId);
        policyLoanSetMsgVO.setBusiItemId(busiItemId);
        policyLoanSetMsgVO.setBusiPrdId(busiPrdId);
        LoanBusiRateCfgPO loanBusiRateCfgPO = new LoanBusiRateCfgPO();
        loanBusiRateCfgPO.setStreamId(streamId);
        List<LoanBusiRateCfgPO> loanBusiRateCfgPOs = commonQueryDao.findAllLoanBusiRateCfg(loanBusiRateCfgPO);
        //判断利率时间区间，赋值正常利率，逾期利率，二阶段,三阶段利率,1正常利率,2逾期利率,3正常利率, 4二阶段,5三阶段
        if (loanBusiRateCfgPOs.size() > 0) {
            for (LoanBusiRateCfgPO LoanBusiRateCfg : loanBusiRateCfgPOs) {
                if (LoanBusiRateCfg.getTimePeridoCode() != null
                        && LoanBusiRateCfg.getTimePeridoCode().compareTo(new BigDecimal(1)) == 0) {
                    policyLoanSetMsgVO.setRateValue(LoanBusiRateCfg.getLoanRate());
                }
                if (LoanBusiRateCfg.getTimePeridoCode() != null
                        && LoanBusiRateCfg.getTimePeridoCode().compareTo(new BigDecimal(2)) == 0) {
                    policyLoanSetMsgVO.setOverDueRateValue(LoanBusiRateCfg.getLoanRate());
                    policyLoanSetMsgVO.setIsCarefullyChoose("0");
                }
                if (LoanBusiRateCfg.getTimePeridoCode() != null
                        && LoanBusiRateCfg.getTimePeridoCode().compareTo(new BigDecimal(3)) == 0) {
                    policyLoanSetMsgVO.setRateValue(LoanBusiRateCfg.getLoanRate());
                }
                if (LoanBusiRateCfg.getTimePeridoCode() != null
                        && LoanBusiRateCfg.getTimePeridoCode().compareTo(new BigDecimal(4)) == 0) {
                    policyLoanSetMsgVO.setSecondStageRateVal(LoanBusiRateCfg.getLoanRate());
                    policyLoanSetMsgVO.setIsCarefullyChoose("1");
                }
                if (LoanBusiRateCfg.getTimePeridoCode() != null
                        && LoanBusiRateCfg.getTimePeridoCode().compareTo(new BigDecimal(5)) == 0) {
                    policyLoanSetMsgVO.setThirdStageRateVal(LoanBusiRateCfg.getLoanRate());
                }
            }
        }

        return policyLoanSetMsgVO;
    }


    /**
     * 
     * @description 计算险种是否在犹豫期
     * @version
     * @title
     * <AUTHOR>
     * @param changeId
     * @param policyId
     * @param busiPrdId
     * @return
     */
    public boolean isInHesitate(BigDecimal changeId, BigDecimal policyId, BigDecimal busiPrdId, Date calDate) {
        CsPolicyAcknowledgementBO csPolicyAcknowledgementBO = new CsPolicyAcknowledgementBO();
        csPolicyAcknowledgementBO.setPolicyId(policyId);
        // 查询保单回执表，得到保单回执日期（也就是保单签收日期）
        List<CsPolicyAcknowledgementBO> csPolicyAcknowledgementBOs = commonLoanInfoService
                .findPolicyAcknowledgements(csPolicyAcknowledgementBO);

        // 查询保全申请信息表
        CsApplicationBO csApplicationBO = new CsApplicationBO();
        List<CsApplicationBO> csApplicationBOs = null;
        if (changeId != null) {
            csApplicationBO.setChangeId(changeId);
            csApplicationBOs = commonLoanInfoService.findCsApplications(csApplicationBO);
        }

        // 申请时间
        Date applyDate = null;
        DateTime applyTime = null;

        // 此修改针对没有生成对应保全申请的情况下查询
        if (calDate != null) {
            applyDate = calDate;
            applyTime = new DateTime(calDate);
        } else if (csApplicationBOs != null && csApplicationBOs.size() > 0) {
            applyDate = csApplicationBOs.get(0).getApplyTime();
            applyTime = new DateTime(csApplicationBOs.get(0).getApplyTime());
        }

        if (csPolicyAcknowledgementBOs != null && csPolicyAcknowledgementBOs.size() > 0 && applyDate != null) {
            DateTime acknowledgeDate = new DateTime(csPolicyAcknowledgementBOs.get(0).getAcknowledgeDate());

            int hesitateDay = 10;

            int includeNowWorkDays = 1;

            CsContractMasterBO csContractMasterBO = new CsContractMasterBO();
            List<CsContractMasterBO> csContractMasterBOs = null;

            csContractMasterBO.setChangeId(changeId);
            csContractMasterBO.setOldNew("1");
            csContractMasterBO.setPolicyId(policyId);
            if (changeId != null) {
                csContractMasterBOs = commonLoanInfoService.findCsContractMasters(csContractMasterBO);
            }
            if (csContractMasterBOs == null || csContractMasterBOs.size() == 0) {
                csContractMasterBOs = commonLoanInfoService.findContractMasterList(csContractMasterBO);
            }
            csContractMasterBO = csContractMasterBOs.get(0);

            FreeLookPeriodCfgPO freeLookPeriodCfgPO = new FreeLookPeriodCfgPO();
            freeLookPeriodCfgPO.setBusinessProdId(busiPrdId);
            freeLookPeriodCfgPO.setOrganCode(csContractMasterBO.getOrganCode());
            freeLookPeriodCfgPO.setChannelType(csContractMasterBO.getChannelType());
            // TODO 银贷新政策未加入判断，因为目前字段未确定
            // 申请时间，生效时间在其之前
            freeLookPeriodCfgPO.setCfgValidateTime(applyDate);

            // 查询对应险种
            CsContractBusiProdBO csContractBusiProdBO = new CsContractBusiProdBO();
            csContractBusiProdBO.setPolicyId(policyId);
            csContractBusiProdBO.setBusiPrdId(busiPrdId);
            csContractBusiProdBO.setOldNew("1");
            csContractBusiProdBO.setChangeId(changeId);
            List<CsContractBusiProdBO> csContractBusiProdBOs = commonLoanInfoService
                    .findContractBusiProdList(csContractBusiProdBO);

            // 被保人关系,第一被保人。
            CsBenefitInsuredBO csBenefitInsuredBO = new CsBenefitInsuredBO();
            List<CsBenefitInsuredBO> csBenefitInsuredBOs = null;

            csBenefitInsuredBO.setPolicyId(policyId);
            csBenefitInsuredBO.setChangeId(changeId);
            csBenefitInsuredBO.setOldNew("1");
            csBenefitInsuredBO.setOrderId(new BigDecimal(1));// 第一被保人
            if (changeId != null) {
                csBenefitInsuredBO.setBusiItemId(csContractBusiProdBOs.get(0).getBusiItemId());
                csBenefitInsuredBOs = commonLoanInfoService.findCsBenefitInsureds(csBenefitInsuredBO);
            }
            if (csBenefitInsuredBOs == null || csBenefitInsuredBOs.size() == 0) {
                csBenefitInsuredBOs = commonLoanInfoService.findBenefitInsuredList(csBenefitInsuredBO);
            }
            if (csBenefitInsuredBOs != null && csBenefitInsuredBOs.size() > 0) {
                csBenefitInsuredBO = csBenefitInsuredBOs.get(0);
            }

            CsInsuredListBO csInsuredListBO = new CsInsuredListBO();
            List<CsInsuredListBO> csInsuredListBOs = null;

            csInsuredListBO.setPolicyId(policyId);
            csInsuredListBO.setChangeId(changeId);
            csInsuredListBO.setOldNew("1");
            if (csBenefitInsuredBO != null && csBenefitInsuredBO.getInsuredId() != null) {
                csInsuredListBO.setListId(csBenefitInsuredBO.getInsuredId());
            }
            if (changeId != null) {
                csInsuredListBOs = commonLoanInfoService.findCsInsuredLists(csInsuredListBO);
            }
            if (csInsuredListBOs == null || csInsuredListBOs.size() == 0) {
                csInsuredListBOs = commonLoanInfoService.findInsuredList(csInsuredListBO);
            }
            csInsuredListBO = csInsuredListBOs.get(0);

            // 投保人对象
            CsPolicyHolderBO csPolicyHolderBO = new CsPolicyHolderBO();
            List<CsPolicyHolderBO> csPolicyHolderBOs = null;

            csPolicyHolderBO.setChangeId(changeId);
            csPolicyHolderBO.setPolicyId(policyId);
            csPolicyHolderBO.setOldNew("1");
            if (changeId != null) {
                csPolicyHolderBOs = commonLoanInfoService.findCsPolicyHolders(csPolicyHolderBO);
            }
            if (csPolicyHolderBOs == null || csPolicyHolderBOs.size() == 0) {
                csPolicyHolderBOs = commonLoanInfoService.findPolicyHolderList(csPolicyHolderBO);
            }
            if (csPolicyHolderBOs.size() > 0) {
                csPolicyHolderBO = csPolicyHolderBOs.get(0);
            }
            CustomerPO customerPO = new CustomerPO();
            // 被保人对象
            customerPO.setCustomerId(csInsuredListBO.getCustomerId());
            customerPO = customerDao.findCustomer(customerPO);

            CustomerPO customerPO1 = new CustomerPO();
            // 投保人对象
            if (csPolicyHolderBO != null && csPolicyHolderBO.getCustomerId() != null) {
                customerPO1.setCustomerId(csPolicyHolderBO.getCustomerId());
            }
            customerPO1 = customerDao.findCustomer(customerPO1);

            if (customerPO != null && customerPO.getCustomerBirthday() != null) {
                Double age1 = DateUtilsEx.getYearAmount(customerPO.getCustomerBirthday(), applyDate);
                freeLookPeriodCfgPO.setInsuredAge(new BigDecimal(age1));
            }

            if (customerPO1 != null && customerPO1.getCustomerBirthday() != null) {
                Double age2 = DateUtilsEx.getYearAmount(customerPO1.getCustomerBirthday(), applyDate);
                freeLookPeriodCfgPO.setPolicyHolderAge(new BigDecimal(age2));
            }
            // 投被保人年龄，性别，性别需对应，年龄需要大于配置值
            if (customerPO != null && customerPO.getCustomerGender() != null) {
                freeLookPeriodCfgPO.setInsuredGender(customerPO.getCustomerGender().toString());
            }
            if (customerPO1 != null && customerPO1.getCustomerGender() != null) {
                freeLookPeriodCfgPO.setPolicyHolderGender(customerPO1.getCustomerGender());
            }
            List<FreeLookPeriodCfgPO> freeLookPeriodCfgPOs = commonQueryDao
                    .findAllFreeLookPeriodCfgSelectByValidate(freeLookPeriodCfgPO);

            // 若有查询到对应的犹豫期。
            if (freeLookPeriodCfgPOs != null && freeLookPeriodCfgPOs.size() > 0) {
                // 若有多条数据匹配，取生效时间最后的一个
                hesitateDay = freeLookPeriodCfgPOs.get(0).getFreeLookPerid().intValue();
                includeNowWorkDays = freeLookPeriodCfgPOs.get(0).getIncludeNonworkdaysFlag().intValue();
            }
            if (includeNowWorkDays == 1) {
                // 犹豫期包含非工作日
                // 若保全申请提交日期 < 保单回执日期 + 犹豫期天数（10天）,则在犹豫期内，返回true
                if (applyTime.isBefore(acknowledgeDate.plusDays(hesitateDay))) {
                    return true;
                }
            } else {
                // 犹豫期不包含非工作日
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                String applyTimes = sdf.format(applyDate);
                String acknowledgeDates = sdf.format(csPolicyAcknowledgementBOs.get(0).getAcknowledgeDate());
                freeLookPeriodCfgPO = new FreeLookPeriodCfgPO();
                freeLookPeriodCfgPO.getData().put("start_time", acknowledgeDates);
                freeLookPeriodCfgPO.getData().put("end_time", applyTimes);
                // 目前工作日表的organid写死。
                freeLookPeriodCfgPO.getData().put("organ_id", new BigDecimal(101));

                // 非工作日天数
                int vacationDays = commonQueryDao.findAllVacationDays(freeLookPeriodCfgPO);
                // policydays本来就少算一天，所以以下用小于，而不是小于等于
                Double policydays = DateUtilsEx.getDayAmount(csPolicyAcknowledgementBOs.get(0).getAcknowledgeDate(),
                        csApplicationBOs.get(0).getApplyTime());
                if (policydays - vacationDays < hesitateDay) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 贷款操作记录
     */
    public List<CsPolicyInfoChangeThreeaPO> findCsPolicyInfoChangeThreeaPO(
            CsPolicyInfoChangeThreeaPO csPolicyInfoChangeThreeaPO) {
        List<CsPolicyInfoChangeThreeaPO> pos = commonQueryDao.findCsPolicyInfoChangeThreea(csPolicyInfoChangeThreeaPO);
        return pos;
    }

    @Override
    public CsApplicationBO findCsInfoQueryApplication(CsApplicationBO csApplicationBO) {
        return BeanUtils.copyProperties(CsApplicationBO.class,
                commonQueryDao.findCsApplication(BeanUtils.copyProperties(CsApplicationPO.class, csApplicationBO)));
    }

    @Override
    public List<CsAcceptChangeBO> findAllCsAcceptChange(CsAcceptChangeBO csAcceptChangeBO) {
        return BeanUtils.copyList(CsAcceptChangeBO.class, commonQueryDao.findAllCsAcceptChange(BeanUtils
                .copyProperties(CsAcceptChangePO.class, csAcceptChangeBO)));
    }

    @Override
    public List<CsInfoQueryAcceptBO> findAllCsInfoQueryAccept(CsInfoQueryAcceptBO csInfoQueryAcceptBO) {
        return BeanUtils.copyList(CsInfoQueryAcceptBO.class, commonQueryDao.queryAllCsInfoQueryAccept(BeanUtils
                .copyProperties(CsInfoQueryAcceptPO.class, csInfoQueryAcceptBO)));
    }

    @Override
    public List<CsInfoQueryBackMoneyBO> findAllCsInfoQueryBackMoney(CsInfoQueryBackMoneyBO csInfoQueryBackMoneyBO) {
        return BeanUtils.copyList(CsInfoQueryBackMoneyBO.class, commonQueryDao.queryAllCsInfoQueryBackMoney(BeanUtils
                .copyProperties(CsInfoQueryBackMoneyPO.class, csInfoQueryBackMoneyBO)));
    }

    @Override
    public QryAccountBO queryCountBalancePeriodsInfo(QryAccountBO copyProperties) {
        QryAccountPO po = commonQueryDao.queryCountBalancePeriodsInfo(BeanUtils.copyProperties(QryAccountPO.class,
                copyProperties));
        return BeanUtils.copyProperties(QryAccountBO.class, po);
    }

    @Override
    public QryNbDataInfoBO queryNbDataInfo(NbContractMasterBO nbContractMasterBO,int loadType) {
        if (!StringUtilsEx.isNullOrEmpty(nbContractMasterBO.getApplyCode())) {
            String applyCode = nbContractMasterBO.getApplyCode();
            QryNbDataInfoBO qryNbDataInfoBO = new QryNbDataInfoBO();
            NbContractMasterPO nbContractMasterPO = new NbContractMasterPO();
            NbPolicyHolderPO nbPolicyHolderPO = new NbPolicyHolderPO();
            NbInsuredListPO nbInsuredListPO = new NbInsuredListPO();
            NbContractBenePO nbContractBenePO = new NbContractBenePO();
            NbPayerAccountPO nbPayerAccountPO = new NbPayerAccountPO();
            NbContractBusiProdPO nbContractBusiProdPO = new NbContractBusiProdPO();
            nbContractMasterPO.setApplyCode(applyCode);
            nbPolicyHolderPO.setApplyCode(applyCode);
            nbInsuredListPO.setApplyCode(applyCode);
            nbContractBenePO.setApplyCode(applyCode);
            nbPayerAccountPO.setApplyCode(applyCode);
            nbContractBusiProdPO.setApplyCode(applyCode);
            switch (loadType){
                case 0:
                    // 投保单基本信息
                    NbContractMasterPO contractMasterPO = commonQueryDao.queryNbContractMasterInfo(nbContractMasterPO);
                    
                    NbContractMasterPO contractMasterAppSource = commonQueryDao.queryNbContractMasterInfoForApplySource(nbContractMasterPO);
                    contractMasterPO.setApplySource(contractMasterAppSource.getApplySource());
                    NbContractMasterPO contantpo = new NbContractMasterPO();
                    contantpo.setSubId(Constants.DOCUMNET_NBS_SYSTEM_ID);
                    contantpo.setConstantsKey(Constants.POLICY_DETAIL_DATE);
                    List<NbContractMasterPO> findAllConstantsInfo = commonQueryDao.findAllConstantsInfo(contantpo);
                    if(findAllConstantsInfo!=null && findAllConstantsInfo.size()>0 && 
                    		contractMasterPO.getApplyDate()!=null && contractMasterPO.getSubmitChannel()!=null){
            			String constantsValue = findAllConstantsInfo.get(0).getConstantsValue();
            			try {
            				/**@invalid 
            				 * 投保日期小于等于7月21日：
            				1.所有渠道：“投保人财务及其他告知”、“被保人财务及其他告知”按钮正常显示，投被保人信息中年收入、收入来源、居民类型、社保状态录入框不显示

            					投保日期大于7月21日：
            				2.公司自录：“投保人财务及其他告知”、“被保人财务及其他告知”按钮不显示，投被保人信息中年收入、收入来源、居民类型、社保状态录入框正常显示
            				3.其他渠道：“投保人财务及其他告知”、“被保人财务及其他告知”按钮正常显示，投被保人信息中年收入、收入来源、居民类型、社保状态录入框正常显示
            				 */
            				Date onlineDate = DateUtilsEx.toDate(constantsValue, "yyyy-MM-dd");				
            				if(contractMasterPO.getApplyDate().after(onlineDate)){
            					if(contractMasterPO.getSubmitChannel().compareTo(new BigDecimal(4))==0){
            						contractMasterPO.setOnlineFlag("2");
            					}else{
            						contractMasterPO.setOnlineFlag("3");
            					}
            				}else{
            					contractMasterPO.setOnlineFlag("1");
            				}
            			} catch (Exception e) {
            				e.printStackTrace();
            			}
            			
            		
            		}

                    NbPolicyMasterExtendPO queryExtendPO = new NbPolicyMasterExtendPO();
                    queryExtendPO.setApplyCode(applyCode);
                    NbPolicyMasterExtendPO extendPO= nbPolicyMasterExtendDao.queryExtendListByApplyCode(queryExtendPO);

                    contractMasterPO.setEastSelfInsured(extendPO.getEastSelfInsured());
                    contractMasterPO.setEastMutualInsured(extendPO.getEastMutualInsured());

                    qryNbDataInfoBO.setNbContractMasterBO(BeanUtils.copyProperties(NbContractMasterBO.class, contractMasterPO));
                    
                    if(StringUtils.isNotBlank(contractMasterPO.getPersonalInfoProtect())&&contractMasterPO.getPersonalInfoProtect().contains("|")){
            			String[] personalInfoProtect = contractMasterPO.getPersonalInfoProtect().split("\\|",-1);
            			if(StringUtils.isNotBlank(personalInfoProtect[0])){
            				qryNbDataInfoBO.getNbContractMasterBO().setPersonalInfoProtect01(personalInfoProtect[0]);
            			}
            			if(StringUtils.isNotBlank(personalInfoProtect[1])){
            				qryNbDataInfoBO.getNbContractMasterBO().setPersonalInfoProtect02(personalInfoProtect[1]);
            			}
            			if(StringUtils.isNotBlank(personalInfoProtect[2])){
            				qryNbDataInfoBO.getNbContractMasterBO().setPersonalInfoProtect03(personalInfoProtect[2]);
            			}
            			if(StringUtils.isNotBlank(personalInfoProtect[3])){
            				qryNbDataInfoBO.getNbContractMasterBO().setPersonalInfoProtect04(personalInfoProtect[3]);
            			}
            			if(StringUtils.isNotBlank(personalInfoProtect[4])){
            				qryNbDataInfoBO.getNbContractMasterBO().setPersonalInfoProtect05(personalInfoProtect[4]);
            			}
            		}
                    
                    
                    // 1.2 查询投保人信息 查询表 投保人信息表 T_NB_POLICY_HOLDER 客户表 T_CUSTOMER 地址表 T_ADDRESS
                    List<NbPolicyHolderPO> nbPolicyHolderPOs = commonQueryDao.queryNbPolicyHolderInfo(nbPolicyHolderPO);
                    qryNbDataInfoBO.setNbPolicyHolderBOs(BeanUtils.copyList(NbPolicyHolderBO.class, nbPolicyHolderPOs));
                    NbContractCustomerHisPO contractCustomerHisPO = new NbContractCustomerHisPO();
                    contractCustomerHisPO.setApplyCode(applyCode);
                    List<NbContractCustomerHisPO> contractCustomerHisLst = commonQueryDao.queryNbContractCustomerHis(contractCustomerHisPO);
                    qryNbDataInfoBO.setNbContractCustomerHisList(BeanUtils.copyList(NbContractCustomerHisBO.class, contractCustomerHisLst));
                    break;
                case 1: 
                    // 被保险人信息
                	if(!StringUtilsEx.isNullOrEmpty(nbContractMasterBO.getHaveInsureds()) && nbContractMasterBO.getHaveInsureds().equals(TransferUtil.STRING1)){
                		nbInsuredListPO.setHaveInsureds(TransferUtil.STRING1);
                	}
                    List<NbInsuredListPO> nbInsuredListPOs = commonQueryDao.queryNbInsuredListInfo(nbInsuredListPO);
                    if(!StringUtilsEx.isNullOrEmpty(nbContractMasterBO.getHaveInsureds()) && nbContractMasterBO.getHaveInsureds().equals(TransferUtil.STRING1)){
                    	if(nbInsuredListPOs !=null && nbInsuredListPOs.size()>0){
                    		//去重后集合
                    		Set<String> setList = new HashSet<>();
                		    // 去重后集合
                		    List<NbInsuredListPO> nbInsuredListPOsNew = new ArrayList<NbInsuredListPO>();
                		    for(NbInsuredListPO cust : nbInsuredListPOs){
                		    boolean addFalg = setList.add(cust.getCustomerCertiCode());
                		    if(addFalg){
                		    	nbInsuredListPOsNew.add(cust);
                		    }
                		    }
                		    nbInsuredListPOs.clear();
                		    nbInsuredListPOs.addAll(nbInsuredListPOsNew);
                    	}
                    }
                    // 缴费信息
                    NbPayerAccountPO payerAccountPO = commonQueryDao.queryNbPayerAccountInfo(nbPayerAccountPO);
                    qryNbDataInfoBO.setNbInsuredListBOs(BeanUtils.copyList(NbInsuredListBO.class, nbInsuredListPOs));
                    qryNbDataInfoBO.setNbPayerAccountBO(BeanUtils.copyProperties(NbPayerAccountBO.class, payerAccountPO));
                    break;
                case 2:
                     // 受益人信息
                    List<NbContractBenePO> nbContractBenePOs = commonQueryDao.queryNbContractBeneInfo(nbContractBenePO);
                    // 险种信息
                    List<NbContractBusiProdBO> nbContractBusiProdBOs= new LinkedList<NbContractBusiProdBO>();
                    List<NbContractBusiProdPO> nbContractBusiProdPOs = commonQueryDao
                            .queryNbContractBusiProdInfo(nbContractBusiProdPO);
                    nbContractBusiProdBOs.addAll(BeanUtils.copyList(NbContractBusiProdBO.class, nbContractBusiProdPOs));
                    for(NbContractBusiProdPO po : nbContractBusiProdPOs){
                    	//如果是双主险的,取出万能险关联保单号,进行查询
                    	if (po.getDoubleMainriskFlag()!=null){
	                    	if(new BigDecimal(1).compareTo(po.getDoubleMainriskFlag())==0 && po.getRelationPolicyCode()!=null){
	                    		po.setPolicyCode(po.getRelationPolicyCode());
	                    		List<NbContractBusiProdPO> pos = commonQueryDao
	                                    .queryNbContractBusiProdInfo(po);
	                    		nbContractBusiProdBOs.addAll(BeanUtils.copyList(NbContractBusiProdBO.class, pos));
	                    	}
                    	}
                    }
                    //@invalid #89884 修改是否续保取值逻辑
                    int productPlanningCount = 0;//险种显示产品计划顺序
                    List<NbContractBusiProdBO> nbContractBusiProdBOWNList = new ArrayList<NbContractBusiProdBO>();
                    Iterator<NbContractBusiProdBO> iterator = nbContractBusiProdBOs.iterator();
                    while (iterator.hasNext()) {
                    	NbContractBusiProdBO nbContractBusiProdBO = iterator.next();

						if(!StringUtilsEx.isNullOrEmpty(nbContractBusiProdBO.getPolicyReinsureFlag())){
							if(Constants.CONTRACT_BUSI_PROD.contains(nbContractBusiProdBO.getProductCode())){
								nbContractBusiProdBO.setRenew(null);
							}
						}
						//常量引用地方需重新提交，否则增量发布不编译
						if(!StringUtilsEx.isNullOrEmpty(nbContractBusiProdBO.getProductCode())
								&& BusiItemDetailInfoServiceImpl.isHolderWaiverRole(TransferUtil.STRING00+nbContractBusiProdBO.getProductCode())){
							List<NbPolicyHolderPO> holderPOs = commonQueryDao.queryNbPolicyHolderInfo(nbPolicyHolderPO);
							nbContractBusiProdBO.setCustomerName(holderPOs.get(0).getCustomerName());
						}
						
						if(StringUtil.isNotBlank(nbContractBusiProdBO.getOrderId()) 
								&& nbContractBusiProdBO.getMasterBusiItemId() == null
								&& nbContractBusiProdBO.getMultiMainriskFlag() != null 
								&& nbContractBusiProdBO.getMultiMainriskFlag().compareTo(BigDecimal.ONE)==0){
							if("20003".equals(nbContractBusiProdBO.getProductCategory1())){
								//查询关联关系表
								NbContractRelationPO nbContractRelationPO = new NbContractRelationPO();
								nbContractRelationPO.setMasterPolicyId(nbContractBusiProdBO.getPolicyId());
								nbContractRelationPO.setSubPolicyId(nbContractBusiProdBO.getPolicyId());
								nbContractRelationPO.setSubBusiItemId(nbContractBusiProdBO.getBusiItemId());
								List<NbContractRelationPO> nbContractRelationPOList = commonQueryDao.queryContractRelationByPolicyId(nbContractRelationPO);
								if(nbContractRelationPOList!=null && nbContractRelationPOList.size()>0){
									nbContractBusiProdBO.setProductPlanningName("新增关联万能险");
									nbContractBusiProdBOWNList.add(nbContractBusiProdBO);
									iterator.remove();
								}else{
									productPlanningCount++;
									nbContractBusiProdBO.setProductPlanningName("产品计划"+StrTool.numberToChinese(productPlanningCount));
								}
							}else{
								productPlanningCount++;
								nbContractBusiProdBO.setProductPlanningName("产品计划"+StrTool.numberToChinese(productPlanningCount));
							}
							
						}
                    }
                    nbContractBusiProdBOs.addAll(nbContractBusiProdBOWNList);
                   /* for (NbContractBusiProdBO nbContractBusiProdBO : nbContractBusiProdBOs) {
						if(!StringUtilsEx.isNullOrEmpty(nbContractBusiProdBO.getPolicyReinsureFlag())){
							if(Constants.CONTRACT_BUSI_PROD.contains(nbContractBusiProdBO.getProductCode())){
								nbContractBusiProdBO.setRenew(null);
							}
						}
						if(!StringUtilsEx.isNullOrEmpty(nbContractBusiProdBO.getProductCode())
								&& Constants.WAIVER_HOLDER_PROD.contains(nbContractBusiProdBO.getProductCode())){
							List<NbPolicyHolderPO> holderPOs = commonQueryDao.queryNbPolicyHolderInfo(nbPolicyHolderPO);
							nbContractBusiProdBO.setCustomerName(holderPOs.get(0).getCustomerName());
						}
					}*/
                    
                    
                    
                    
                    NbContractMasterPO contractMasterPO1 = commonQueryDao.queryNbContractMasterInfo(nbContractMasterPO);
                    qryNbDataInfoBO.setNbContractMasterBO(BeanUtils.copyProperties(NbContractMasterBO.class, contractMasterPO1));
                    qryNbDataInfoBO.setNbContractBeneBOs(BeanUtils.copyList(NbContractBeneBO.class, nbContractBenePOs));
                    qryNbDataInfoBO.setNbContractBusiProdBOs(nbContractBusiProdBOs);
                    break;
            }
            return qryNbDataInfoBO;
        } else {
            return null;
        }

    }
    
    @Override
    public QryCommonQueryCompBO queryInfoBySessionId(QryCommonQueryCompBO qryCommonQueryBO) {
    	QryCommonQueryCompPO qryCommonQueryPO = commonQueryDao.queryInfoBySessionId(BeanUtils.copyProperties(QryCommonQueryCompPO.class,
    			qryCommonQueryBO));
        return BeanUtils.copyProperties(QryCommonQueryCompBO.class, qryCommonQueryPO);
    }
    
    public List<ServiceBO> findServiceByPolicyCode(ServiceBO serviceBO) {
        return BeanUtils.copyList(ServiceBO.class, commonQueryDao.findServiceByPolicyCode(BeanUtils
                .copyProperties(ServicePO.class, serviceBO)));
    }
    
    /**
	 * 
	 * @description 获取互联网回溯资料
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#qeryWebInputTrace(java.lang.String)
	 * @param applyCode 投保单号
	 * @return
	 */
	@Override
	public String queryWebInputTrace(String applyCode) {
		String username= this.encode(Constants.YEEFX_NAME);
		String password= this.encode(Constants.YEEFX_PWD);
		Map<String, Object> applicationMap = ParaDefInitConst.getSystemConst();
		String url = "";
		if (applicationMap.containsKey(Constants.YEEFX_PARA_NAME)) {
			ParaDefBO paraDefInfo = (ParaDefBO) applicationMap.get(Constants.YEEFX_PARA_NAME);
			url = paraDefInfo.getParaValue()+"orderno="+applyCode+"&username="+username+"&password="+password;
        }
		return url;
	}
	/**
	 * 
	 * @description 加密方法
	 * @param str_txt 加密字符
	 * @return
	 */
	public String encode(String str_txt) {
		   String encode_txt = "";
		   String enkey = "phpstat_license_file";
		   String str_key_array[] = enkey.split("");
		   try {
		      byte text_byte[] = str_txt.getBytes();
		      //编码
			  String encoded_text = new BASE64Encoder().encodeBuffer(text_byte);
		      String str_txt_array[] = encoded_text.trim().split("");
		      for(int keyid = 0; keyid < str_key_array.length; keyid++) {
		         if(keyid < str_txt_array.length ) {
		            str_txt_array[keyid] += str_key_array[keyid];
		         }
		      }
		      for(String str:str_txt_array){
		    	  encode_txt+=str;
		      }
		      
		      encode_txt = encode_txt.replace("=", "O01O0O");
		      encode_txt = encode_txt.replace("+", "o0200o");
		      encode_txt = encode_txt.replace("/", "oo030o");
		   }
		   catch(Exception e){

		   }
		   return encode_txt;
		}

	/**
	 * 
	 * @description 返回投保单投保人、被保人是否存在身份验真和身份识别 
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#isExistsVerifyHistory(com.nci.tunan.qry.interfaces.model.bo.QryPolicyHolderBO)
	 * @param qryPolicyHolderBO
	 * @return
	 */
	@Override
	public Integer isExistsVerifyHistory(QryPolicyHolderBO qryPolicyHolderBO) {
		QryPolicyHolderPO po = BeanUtils.copyProperties(QryPolicyHolderPO.class, qryPolicyHolderBO);
		return commonQueryDao.isExistsVerifyHistory(po);
	}
	
	/**
	 * @description 通过保单号、投保单号检验是否上海保单
	 * <AUTHOR>
	 * @param 
	 * @return 
	 */
	public QryPremArapBO checkSHPolicyInfo(QryPremArapBO bo){
		QryPremArapBO shBO = BeanUtils.copyProperties(QryPremArapBO.class, 
				commonQueryDao.checkSHPolicyInfo(BeanUtils.copyProperties(QryPremArapPO.class, bo)));
		return shBO;
	}
	/**
	 * 客户电话变更轨迹
	 */
	@Override
	public CurrentPage<Object> getCustomerPhoneChangePath(
			QueryCustomerPhoneChangePathBO queryCustomerPhoneChangePathBO,
			CurrentPage currentPage) {
    	QueryCustomerPhoneChangePathPO po = new QueryCustomerPhoneChangePathPO();
        po = BeanUtils.copyProperties(QueryCustomerPhoneChangePathPO.class, queryCustomerPhoneChangePathBO);
        CurrentPage currPage = commonQueryDao.getCustomerPhoneChangePath(po, currentPage);
        return currPage;
	}
	/**
	 * 据保单号查询客户电话变更轨迹表的数据条数
	 */
	@Override
	public Integer queryCustomerPhoneChangePathCount(QueryCustomerPhoneChangePathBO queryCustomerPhoneChangePathBO) {
		QueryCustomerPhoneChangePathPO po = new QueryCustomerPhoneChangePathPO();
        po = BeanUtils.copyProperties(QueryCustomerPhoneChangePathPO.class, queryCustomerPhoneChangePathBO);
		return commonQueryDao.queryCustomerPhoneChangePathCount(po);
	}
	/**
	 * 
	 * @description 投资组合账户-保单账户基本信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#getPolicyAccountInfo(com.nci.tunan.qry.interfaces.model.bo.QryAccountBO)
	 * @param accountBO 账户信息BO对象
	 * @return
	 */
	@Override
	public List<QryAccountBO> getPolicyAccountInfo(QryAccountBO accountBO) {
		List<QryAccountPO> qryAccountPOs = commonQueryDao.getPolicyAccountInfo(BeanUtils.copyProperties(QryAccountPO.class, accountBO));
		return BeanUtils.copyList(QryAccountBO.class, qryAccountPOs);
	}

	/**
	 * 
	 * @description 投资组合账户-保单账户轨迹记录
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#getPolicyAccountTrailInfo(com.nci.tunan.qry.interfaces.model.bo.QryAccountBO, com.nci.udmp.framework.model.CurrentPage)
	 * @param bo 账户信息BO对象
	 * @param currentPage 分页信息
	 * @return
	 */
	@Override
	public CurrentPage getPolicyAccountTrailInfo(QryAccountBO bo,
			CurrentPage currentPage) {
		QryAccountPO po = new QryAccountPO();
		if("********".equals(bo.getBusiProdCode())){
			bo.setBusiProdCode(null);
		}
        po = BeanUtils.copyProperties(QryAccountPO.class, bo);
        CurrentPage currPage = commonQueryDao.getPolicyAccountTrailInfo(po, currentPage);
        return currPage;
	}

	/**
	 * 
	 * @description 投资组合账户基本信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#getCombAccountInfo(com.nci.tunan.qry.interfaces.model.bo.QryAccountBO)
	 * @param bo 账户信息BO对象
	 * @return
	 */
	@Override
	public List<QryAccountBO> getCombAccountInfo(QryAccountBO bo) {
		List<QryAccountPO> po = commonQueryDao.getCombAccountInfo(BeanUtils.copyProperties(QryAccountPO.class, bo));
		return BeanUtils.copyList(QryAccountBO.class, po);
	}

	/**
	 * 
	 * @description 投资组合账户轨迹信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#getCombAccountTrailInfo(com.nci.tunan.qry.interfaces.model.bo.QryAccountBO, com.nci.udmp.framework.model.CurrentPage)
	 * @param bo 账户信息BO对象
	 * @param currentPage 分页信息
	 * @return
	 */
	@Override
	public CurrentPage getCombAccountTrailInfo(QryAccountBO bo,
			CurrentPage currentPage) {
		QryAccountPO po = new QryAccountPO();
        po = BeanUtils.copyProperties(QryAccountPO.class, bo);
        CurrentPage currPage = commonQueryDao.getCombAccountTrailInfo(po, currentPage);
        return currPage;
	}
	
	

	/**
	 * 
	 * @description 根据统一社会组织代码查询查询信托公司详细信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#getCombAccountTrailInfo(com.nci.tunan.qry.interfaces.model.bo.QryAccountBO, com.nci.udmp.framework.model.CurrentPage)
	 * @param trustCompanyBO 账户信息BO对象
	 * @return
	 */
	@Override
	public TrustCompanyBO findBeneCompanyInfoByOragnCode(TrustCompanyBO trustCompanyBO) {
		// TODO Auto-generated method stub
		TrustCompanyPO trustCompanyPO = commonQueryDao.findBeneCompanyInfoByOragnCode(BeanUtils.copyProperties(TrustCompanyPO.class, trustCompanyBO));
		return BeanUtils.copyProperties(TrustCompanyBO.class, trustCompanyPO);
	}
	/**
	 * 
	 * @description 根据保单号查询保单第二投保人信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.dao.IQryCommonQueryDao#findPolicyStatusHistory(com.nci.tunan.qry.interfaces.model.bo.PolicyStausBO)
	 * @param policyStatusBO 保单历史状态信息BO对象
	 * @return List<PolicyStatusBO>
	 */
	@Override
	public SecondPolicyHolderBO findSecondPolicyHolder(SecondPolicyHolderBO secondPolicyHolderBO) {
		SecondPolicyHolderPO secondPolicyHolderPO = BeanUtils.copyProperties(SecondPolicyHolderPO.class,secondPolicyHolderBO);
		return BeanUtils.copyProperties(SecondPolicyHolderBO.class, secondPolicyHolderDao.findSecondPolicyHolder(secondPolicyHolderPO));
	}

	/**
     * @description 通知书查询
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see 
     * @param currentPage分页对象
     * @param documentBO通知书对象
     * @return
     */
	@Override
	public CurrentPage<DocumentBO> findDocumentInfos(CurrentPage currentPage,DocumentBO documentBO) {
		return BeanUtils.copyCurrentPage(DocumentBO.class, commonQueryDao.findDocumentInfos(currentPage,BeanUtils.copyProperties(DocumentPO.class,documentBO)));
	}

	/**
	 * @description 险种信息查询
	 * @version
	 * @return 
	 */
	@Override
	public List<BusiProdInfoBO> findBusiProdInfos(BusiProdInfoBO busiProdInfoBO) {
		return BeanUtils.copyList(BusiProdInfoBO.class, commonQueryDao.findBusiProdInfos(BeanUtils.copyProperties(BusiProdInfoPO.class, busiProdInfoBO)));
	}

	
	/**
     * 
     * @description 根据保单号查询保单第二投保人信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.qry.impl.qry.service.impl.CommonQueryServiceImpl.findSecondPolicyHolderInfo(QryHolderInsuredCustCompBO)
     * @param holderInsuredCustCompBO 投被保人信息BO对象
     * @return List<QryHolderInsuredCustCompBO>
     */
    @Override
    public List<QryHolderInsuredCustCompBO> findSecondPolicyHolderInfo(QryHolderInsuredCustCompBO holderInsuredCustCompBO) {
        List<QryHolderInsuredCustCompPO> holderCustomerCompPOs = commonQueryDao.findSecondPolicyHolderPO(BeanUtils
                .copyProperties(QryHolderInsuredCustCompPO.class, holderInsuredCustCompBO));
        return BeanUtils.copyList(QryHolderInsuredCustCompBO.class, holderCustomerCompPOs);
    }

    @Override
	public PolicyRiskQuestionnaireBO queryRiskQuestionnaire(PolicyRiskQuestionnaireBO policyRiskQuestionnaireBO) {
		PolicyRiskQuestionnairePO PolicyRiskQuestionnairePO = BeanUtils.copyProperties(PolicyRiskQuestionnairePO.class,policyRiskQuestionnaireBO);
		return BeanUtils.copyProperties(PolicyRiskQuestionnaireBO.class,commonQueryDao.queryRiskQuestionnaire(PolicyRiskQuestionnairePO));
	}

	@Override
	public List<PolicyRiskQuestionnaireBO> queryAllRiskQuestionnaire(PolicyRiskQuestionnaireBO policyRiskQuestionnaireBO,Set<String> proCode) {
		PolicyRiskQuestionnairePO policyRiskPO = new PolicyRiskQuestionnairePO();
		policyRiskPO = BeanUtils.copyProperties(PolicyRiskQuestionnairePO.class,policyRiskQuestionnaireBO);
		policyRiskPO.getData().put("proCode", proCode);
		List<PolicyRiskQuestionnairePO> policyRiskQuestionnairePO = commonQueryDao.queryAllRiskQuestionnaire(policyRiskPO);
		return BeanUtils.copyList(PolicyRiskQuestionnaireBO.class, policyRiskQuestionnairePO);
	}
    
    @Override
    public AgentCompBO findAgentByAgentCodeCard(AgentCompBO agentCompBO)  {
        //根据代理人编码查询代理人及资格证信息
        AgentBO agentBO = agentCompBO.getAgentBO(); // 获取代理人基本信息

        List<AgentLicenseBO> agentLicenseBOList = agentCompBO.getAgentLicenseBOList();  // 获取代理人证件信息
        AgentCompBO outAgentOperBO = new AgentCompBO(); // 创建返回代理人操作组合BO
        try {
            AgentPO agentPO = BeanUtils.copyProperties(AgentPO.class, agentBO); // 拷贝BO到PO
            if(null==agentPO.getAgentCode()){
                return outAgentOperBO;
            }
            agentPO = commonQueryDao.findAgentByAgentCode(agentPO);	// 通过代理人编码查询代理人基本信息  
            AgentLicensePO agentLicensePO = new AgentLicensePO();
            List<AgentLicensePO> agentLicensePOList = new ArrayList<AgentLicensePO>();
            if (null != agentPO.getAgentCode() && !"".equals(agentPO.getAgentCode())) {
                agentLicensePO.setAgentCode(agentPO.getAgentCode());
                //@invalid agentProductPO.setAgentCode(agentPO.getAgentCode());
                agentLicensePOList = commonQueryDao.findAgLicByAgentCode(agentLicensePO);  // 通过代理人编码获取证件信息
                //@invalid agentProductPOList = agentProductDao.findAgProdByAgentCode(agentProductPO); // 通过代理人编码获取产品权限信息
                agentLicenseBOList = BeanUtils.copyList(AgentLicenseBO.class, agentLicensePOList);  // 拷贝PO
                outAgentOperBO.setAgentLicenseBOList((ArrayList<AgentLicenseBO>) agentLicenseBOList);
            }
          
            agentBO = BeanUtils.copyProperties(AgentBO.class, agentPO); // 拷贝PO到BO
            outAgentOperBO.setAgentBO(agentBO);
        } catch (BizException e) {
            e.printStackTrace();
            throw new BizException("通过代理人编码查询代理人信息出错！");
        }
        return outAgentOperBO;
    }
    

	@Override
	public List<QryNotifyInfoListBO> findNotifyInfo(QryContractMasterCompBO contractMasterCompBO) {
		QryContractMasterCompPO qryContractMasterCompPO = BeanUtils.copyProperties(QryContractMasterCompPO.class,
                contractMasterCompBO);
		QryContractMasterCompPO findPolicyInfoByCode = commonQueryDao.findPolicyInfoByCode(qryContractMasterCompPO);
		//获取险种对应通知
		
		QryNotifyInfoListBO qryNotifyInfoListBo = new QryNotifyInfoListBO();
		qryNotifyInfoListBo.setPolicyCode(findPolicyInfoByCode.getPolicyCode());
		QryNotifyInfoListPO listPO = BeanUtils.copyProperties(QryNotifyInfoListPO.class,qryNotifyInfoListBo);
		List<QryNotifyInfoListPO> findNotifyInfoByBusiProd = commonQueryDao.findNotifyInfoByBusiProd(listPO);
		
		List<QryNotifyInfoListBO> list = new ArrayList<QryNotifyInfoListBO>();
		if(findNotifyInfoByBusiProd !=null && findNotifyInfoByBusiProd.size()>0){
			//单独处理现金分红产品
			StringBuffer productCode = new StringBuffer();
			StringBuffer productName = new StringBuffer();
			boolean flag = false;
			
			for (QryNotifyInfoListPO qryNotifyListPO : findNotifyInfoByBusiProd) {
				String productCategory = qryNotifyListPO.getProductCategory();
				QryNotifyInfoListBO result = new QryNotifyInfoListBO();
				if("20002".equals(productCategory)){
					boolean string = true; 
		        	List<BigDecimal> productIdList = new ArrayList<BigDecimal>();
		    	    PrdQueryTBFlagReqVO inputData = new PrdQueryTBFlagReqVO();
		    	    productIdList.add(qryNotifyListPO.getProductId());
		            inputData.setProductId(productIdList);
		            PrdQueryTBFlagResVO outputData = prdIAS.prdiprdquerytbflaguccqueryTBFlag(inputData);
		            if (outputData != null &&  !CollectionUtilEx.isEmpty(outputData.getPrdQueryTBFlagInfoResVOList())) {
		            	if("1".equals(outputData.getPrdQueryTBFlagInfoResVOList().get(0).getCashBonusFlag())){//现金红利
		            		string = false;
		            	}
		            }
					if(string){
						result.setProductCode(qryNotifyListPO.getProductCode());
						result.setProductName(qryNotifyListPO.getProductName());
						result.setNotifyCode("PAS_00010");
						result.setNotifyName("红利通知书");
						list.add(result);
					}else{
						flag = true;
						productCode.append(qryNotifyListPO.getProductCode()+"、");
						productName.append(qryNotifyListPO.getProductName()+"、");
					}
				}else if("20003".equals(productCategory)){
					result.setProductCode(qryNotifyListPO.getProductCode());
					result.setProductName(qryNotifyListPO.getProductName());
					result.setNotifyCode("PAS_00014");
					result.setNotifyName("万能险结算状态报告书");
					list.add(result);
				}else if("20004".equals(productCategory)){
					result.setProductCode(qryNotifyListPO.getProductCode());
					result.setProductName(qryNotifyListPO.getProductName());
					result.setNotifyCode("PAS_00006");
					result.setNotifyName("投连状态报告书");
					list.add(result);
				}
			}
			if(flag){
				QryNotifyInfoListBO res = new QryNotifyInfoListBO();
				res.setProductCode(productCode.toString().substring(0,productCode.toString().length()-1));
				res.setProductName(productName.toString().substring(0,productName.toString().length()-1));
				res.setNotifyCode("PAS_00017");
				res.setNotifyName("现金分红红利通知书");
				list.add(res);
			}
		}
		return list;
	}

	@Override
	public String findNotificationReceiveMethod(
			QryContractMasterCompBO contractMasterCompBO) {
		QryContractMasterCompPO qryContractMasterCompPO = BeanUtils.copyProperties(QryContractMasterCompPO.class,
                contractMasterCompBO);
		//获取新型产品通知书内容
		String notificationReceiveMethod = "";
		QryContractMasterCompPO findPolicyInfoByCode = commonQueryDao.findPolicyInfoByCode(qryContractMasterCompPO);
		notificationReceiveMethod = findPolicyInfoByCode.getNotificationReceiveMethod();
		
		return notificationReceiveMethod;
	}

	@Override
	public List<QryNotifyInfoDetailBO> findNotifyInfoDetail(
			QryNotifyInfoDetailBO notifyInfoDetailBO) {
		List<QryNotifyInfoDetailPO> findNotifyInfoDetail = commonQueryDao.findNotifyInfoDetail(BeanUtils.copyProperties(QryNotifyInfoDetailPO.class,notifyInfoDetailBO));
		notifyInfoDetailBO.setDocumentPrintStatus(new BigDecimal(2));//查询待打印数据
		//List<QryNotifyInfoDetailPO> findDocument = commonQueryDao.findDocument(BeanUtils.copyProperties(QryNotifyInfoDetailPO.class,notifyInfoDetailBO));
		//findNotifyInfoDetail.addAll(findDocument);
		return BeanUtils.copyList(QryNotifyInfoDetailBO.class, findNotifyInfoDetail);
	}

	@Override
	public List<QryNotifyInfoLogBO> findNotifyInfoLog(
			QryNotifyInfoLogBO notifyInfoLogBO) {
		List<QryNotifyInfoLogPO> findNotifyInfoLog = commonQueryDao.findNotifyInfoLog(BeanUtils.copyProperties(QryNotifyInfoLogPO.class, notifyInfoLogBO));
		return BeanUtils.copyList(QryNotifyInfoLogBO.class, findNotifyInfoLog);
	}
	/**
	 * 
	 * @description 查询常量配置表数据信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#findAllConstantsInfo(com.nci.tunan.qry.interfaces.model.nb.bo.NbContractMasterBO)
	 * @param constantsInfoBO 常量配置表信息
	 * @return
	 */
	@Override
	public List<NbContractMasterBO> findAllConstantsInfo(NbContractMasterBO constantsInfoBO) {
        List<NbContractMasterPO> findAllConstantsInfo = commonQueryDao.findAllConstantsInfo(BeanUtils.copyProperties(NbContractMasterPO.class, constantsInfoBO));
		return BeanUtils.copyList(NbContractMasterBO.class, findAllConstantsInfo);
	}
	/**145832 查询续期年金生存调查确认生存信息
	 * @param payPlanDueBO
	 * @return
	 */
	@Override
	public List<SurvInvCfmInfoBO> getSurvInvCfmInfoPage(QryPayPlanDueBO payPlanDueBO){
		List<SurvInvCfmInfoBO> survInvCfmInfoMapList = new ArrayList<>();
		String policyCode = payPlanDueBO.getPolicyCode();
		//查询第一被保人列表
		InsuredListPO insuredListPO = new InsuredListPO();
		insuredListPO.setPolicyCode(policyCode);
		List<InsuredListPO> insuredListPOList = insuredDao.findFristInsuredByPolicyCode(insuredListPO);
		Set<BigDecimal> customerIdSet = new HashSet<>();//去重
		//查询续期年金生存调查确认生存信息
		for(InsuredListPO po : insuredListPOList){
			if(customerIdSet.contains(po.getCustomerId())){
				continue;
			}else{
				customerIdSet.add(po.getCustomerId());
			}
			SurvInvCfmInfoPO survInvCfmInfoPO = new SurvInvCfmInfoPO();
			survInvCfmInfoPO.setCustomerId(po.getCustomerId());
			List<SurvInvCfmInfoPO> survInvCfmInfoPOList = commonQueryDao.getSurvInvCfmInfo(survInvCfmInfoPO);
			for(SurvInvCfmInfoPO spo : survInvCfmInfoPOList){
				if(spo!=null && spo.getData() !=null && !spo.getData().isEmpty()){
					survInvCfmInfoMapList.add(BeanUtils.copyProperties(SurvInvCfmInfoBO.class, spo));
				}
			}
		}
		return survInvCfmInfoMapList;
	}
	/**145832查询生存调查验真信息
	 * @param copyProperties
	 * @return
	 */
	public IdentityCheckInfoBO getIdentityCheckInfo(IdentityCheckInfoBO identityCheckInfoBO){
		IdentityCheckInfoPO identityCheckInfoPO = BeanUtils.copyProperties(IdentityCheckInfoPO.class, identityCheckInfoBO);
		if(identityCheckInfoPO != null && identityCheckInfoPO.getPhotoConpareId() != null){
			identityCheckInfoPO = commonQueryDao.getIdentityCheckInfo(identityCheckInfoPO);
		}
		return BeanUtils.copyProperties(IdentityCheckInfoBO.class, identityCheckInfoPO);
	}
	/**
	 * 
	 * @description 根据客户5要素查询客户税收居民身份
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.service.ICommonQueryService#queryCustomerTaxType(com.nci.tunan.qry.interfaces.model.bo.CustomerBO)
	 * @param customerBO 客户5要素
	 * @return
	 */
	@Override
	public CustomerBO queryCustomerTaxType(CustomerBO customerBO) {
		CustomerPO queryCustomerTaxType = commonQueryDao.queryCustomerTaxType(BeanUtils.copyProperties(CustomerPO.class, customerBO));
		return BeanUtils.copyProperties(CustomerBO.class,queryCustomerTaxType);
	}
	/**
	 * @description 保单状态信息查询
	 * @version
	 * @return 
	 */
	@SuppressWarnings("unchecked")
	@Override
	public CurrentPage<PolicyStatusBO> findPolicyStatusByCode(PolicyStatusBO policyStatusBO,CurrentPage currentPage) {	
		PolicyStatusPO policyStatusPO = BeanUtils.copyProperties(PolicyStatusPO.class,policyStatusBO);	
		CurrentPage<PolicyStatusPO> returnCurrentPage = commonQueryDao.findPolicyStatusInfos(policyStatusPO,currentPage);	
		List<PolicyStatusPO> policyStatusPOList = returnCurrentPage.getPageItems();		
		CurrentPage<PolicyStatusBO> copyCurrentPage = BeanUtils.copyCurrentPage(PolicyStatusBO.class, returnCurrentPage);	
		return copyCurrentPage;
	}
	
	/**
     * 
     * @description 姓名脱敏
     * @version
     * @title
     * <AUTHOR>
     * @see 
     * @param String oldName 原始姓名
     * @return String newName 脱敏后姓名
     */
	public String nameHyposensitization(String oldName){
		String newName = "";
		if(oldName!=null && !oldName.isEmpty()){
			if (oldName.length() == 1) {   
				//张 -> 张
				newName = oldName;
			}else if (oldName.length() == 2 ) {
				//张三 -> 张****
				StringBuffer sb = new StringBuffer(); 
				sb.append(oldName.charAt(0));
				sb.append("****");
				newName = sb.toString();
			}else{
				//张三丰 -> 张*丰    张三三凤 -> 张**凤
				StringBuffer sb = new StringBuffer(); 
				sb.append(oldName.charAt(0));
				sb.append("****");
				sb.append(oldName.charAt(oldName.length()-1));
				newName = sb.toString();
			}
		}
		
	    return newName;
	}
	 /**
	  *  #160232 支持互联网渠道新增慧择平台出单渠道信息查询需求-新契约 
	  * 
	  * */
	 public NbContractMasterBO queryCooperationProtocolIdByPolicyCode(NbContractMasterBO nbContractMasterBO){
		 //#160232 支持互联网渠道新增慧择平台出单渠道信息查询需求-新契约 begin
        QryContractMasterCompPO qryContractMasterCompPO = new QryContractMasterCompPO();
        NbContractMasterBO nbContractMasterBOCoo = new NbContractMasterBO();
        qryContractMasterCompPO.setApplyCode(nbContractMasterBO.getApplyCode());
        qryContractMasterCompPO.setPolicyCode(nbContractMasterBO.getPolicyCode());
        QryContractMasterCompPO qryContractMasterCompPOCPID = commonQueryDao.queryCooperationProtocolIdByPolicyCode(qryContractMasterCompPO);
        	if(qryContractMasterCompPOCPID != null){
        		if(StringUtils.isNotEmpty(qryContractMasterCompPOCPID.getCooperationProtocolId())){
        			nbContractMasterBOCoo.setCooperationProtocolId(qryContractMasterCompPOCPID.getCooperationProtocolId());
        		}
        		if(StringUtils.isNotEmpty(qryContractMasterCompPOCPID.getCooperationCode())){
         			nbContractMasterBOCoo.setCooperationCode(qryContractMasterCompPOCPID.getCooperationCode());
         		}
         		if(StringUtils.isNotEmpty(qryContractMasterCompPOCPID.getCooperationName())){
         			nbContractMasterBOCoo.setCooperationName(qryContractMasterCompPOCPID.getCooperationName());
         		}
        	}
          //#160232 支持互联网渠道新增慧择平台出单渠道信息查询需求-新契约   end
        	return nbContractMasterBOCoo;
	 }

	@Override
	public List<NbContractBusiProdBO> queryNbContractBusiProdInfo(NbContractBusiProdBO nbContractBusiProdBO) {
		 List<NbContractBusiProdPO> nbContractBusiProdPOs = commonQueryDao.queryNbContractBusiProdInfo(BeanUtils.copyProperties(NbContractBusiProdPO.class, nbContractBusiProdBO));
			return BeanUtils.copyList(NbContractBusiProdBO.class, nbContractBusiProdPOs);
	}
	
	/**
     * 判断该保单是否符合多被保险人规则
     * @description
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param insuredListPO 对象
     * @return Boolean
     * @throws BizException
     */
	@Override
    public Boolean IsMultipleInsuredPolicys(InsuredListBO insuredListBo) throws BizException {
		//判断保单下是否有多被保险人
		Boolean flag = false;
		InsuredListPO insuredListPo = new InsuredListPO();
		insuredListPo = BeanUtils.copyProperties(InsuredListPO.class, insuredListBo);
		List<InsuredListPO> insuredListPos = insuredDao.findAllInsuredList(insuredListPo);
		if(insuredListPos.size() > ClaimConstant.ONE){
			QryContractBusiProdPO contractBusiProdPo = new QryContractBusiProdPO();
        	contractBusiProdPo.setPolicyCode(insuredListPo.getPolicyCode());
        	List<QryContractBusiProdPO> contractBusiProdPoList = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPo);
        	Set<String> busiProdCodes = new HashSet<>();
        	if(contractBusiProdPoList != null && contractBusiProdPoList.size() > ClaimConstant.ZERO){
        		//判断保单下是否存在相同的险种
        		for(QryContractBusiProdPO contractBusiProd : contractBusiProdPoList){
        			if(!busiProdCodes.add(contractBusiProd.getBusiProdCode())){
        				flag = true;
        			}
        		}
        		if(flag){
	        		for(QryContractBusiProdPO contractBusiProdP : contractBusiProdPoList){
	        			QryContractBusiProdPO contractBusiProdPr = new QryContractBusiProdPO();
	        			contractBusiProdPr.setPolicyCode(insuredListPo.getPolicyCode());
	        			contractBusiProdPr.setBusiItemId(contractBusiProdP.getBusiItemId());
	        			List<QryContractBusiProdPO> contractBusiProdPoss = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPr);
	        			//险种下均只有一个被保险人
	        			if(contractBusiProdPoss == null || contractBusiProdPoss.size() == ClaimConstant.ZERO || contractBusiProdPoss.size() > ClaimConstant.ONE){
	        				return false;
	        			}
	        		}
	        		return true;
        		}
        	}
		}
		return false;
	}

    public INbPolicyMasterExtendDao getNbPolicyMasterExtendDao() {
        return nbPolicyMasterExtendDao;
    }

    public void setNbPolicyMasterExtendDao(INbPolicyMasterExtendDao nbPolicyMasterExtendDao) {
        this.nbPolicyMasterExtendDao = nbPolicyMasterExtendDao;
    }
}
