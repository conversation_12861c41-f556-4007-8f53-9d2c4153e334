package com.nci.tunan.qry.impl.jrqd.service.pa.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsAccountFeeInfoReqVO;
import com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeReqVO;
import com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeResVO;
import com.nci.tunan.mms.interfaces.calc.exports.calcspecialbasicsa.vo.MmsCalcRiskSAVO;
import com.nci.tunan.mms.interfaces.calc.exports.calcspecialbasicsa.vo.MmsCalcSpecialBasicSAReqVO;
import com.nci.tunan.mms.interfaces.calc.exports.calcspecialbasicsa.vo.MmsCalcSpecialBasicSAResVO;
import com.nci.tunan.mms.interfaces.query.exports.universalFeeQuery.vo.MmsUniversalFeeQueryReqVO;
import com.nci.tunan.mms.interfaces.query.exports.universalFeeQuery.vo.MmsUniversalFeeQueryResVO;
import com.nci.tunan.mms.interfaces.query.exports.universalFeeQuery.vo.MmsUniversalFeeQueryVO;
import com.nci.tunan.prd.interfaces.calc.exports.calcuniversalamount.vo.PrdCalcUniversalAmountReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.calcuniversalamount.vo.PrdCalcUniversalAmountResVO;
import com.nci.tunan.qry.impl.jrqd.common.Constants;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IBenefitInsuredDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractBusiProdDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractExtendDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractMasterDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractProductDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.ICustomerDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IExtraPremDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IFundSettlementDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IFundTransDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IILPUChargeDeductionDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IInsuredListDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPolicyChangeDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPolicyFundChargeDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPremArapDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.ISaChangeDao;
import com.nci.tunan.qry.impl.jrqd.service.pa.ICalculateIlpuDueFee;
import com.nci.tunan.qry.impl.jrqd.service.pa.IPRDService;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.BenefitInsuredPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractExtendPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractInvestPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractMasterPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractProductPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.CustomerPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ExtraPremPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.FundSettlementPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.FundTransPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.InsuredListPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PolicyChangePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PolicyFundChargePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PremArapPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ProductLiabilityChangePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.SaChangePO;
import com.nci.tunan.qry.interfaces.model.jrqd.vo.pa.ILPUFeeType;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;

/**
 * @description 计算投连万能费用扣除金额Service接口实现类
 *              复制自com.nci.tunan.pa.common.service.impl.CalculateIlpuDueFee
 * @<NAME_EMAIL>
 * @date Mar 29, 2022 5:10:30 PM
 * @.belongToModule
 */
public class CalculateIlpuDueFee implements ICalculateIlpuDueFee {

	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * @Fields prdIAS : 产品Service接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PRDService")
	private IPRDService prdIAS;

	/**
	 * @Fields contractBusiProdDao : 保单险种信息Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractBusiProdDao")
	private IContractBusiProdDao contractBusiProdDao;

	/**
	 * @Fields contractMasterDao : 保单主表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractMasterDao")
	private IContractMasterDao contractMasterDao;

	/**
	 * @Fields ilpuChargeDeductionDao : 投连万能费用扣除Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ILPUChargeDeductionDao")
	private IILPUChargeDeductionDao ilpuChargeDeductionDao;

	/**
	 * @Fields policyFundChargeDao : 基金收费Dao接口类
	 */
	@Autowired
	@Qualifier("JRQD_PA_PolicyFundChargeDao")
	private IPolicyFundChargeDao policyFundChargeDao;

	/**
	 * @Fields contractProductDao : 责任组Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractProductDao")
	private IContractProductDao contractProductDao;

	/**
	 * @Fields contractExtendDao : 保单缴费计划信息Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractExtendDao")
	private IContractExtendDao contractExtendDao;

	/**
	 * @Fields fundSettlementDao : 万能险结算Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_FundSettlementDao")
	private IFundSettlementDao fundSettlementDao;

	/**
	 * @Fields extraPremDao : 险种责任组加费Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ExtraPremDao")
	private IExtraPremDao extraPremDao;

	/**
	 * @Fields benefitInsuredDao : 险种被保人表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_BenefitInsuredDao")
	private IBenefitInsuredDao benefitInsuredDao;

	/**
	 * @Fields insuredListDao : 保单被保人表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_InsuredListDao")
	private IInsuredListDao insuredListDao;

	/**
	 * @Fields customerDao : 客户Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_CustomerDao")
	private ICustomerDao customerDao;

	/**
	 * @Fields premArapDao : 应收应付表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PremArapDao")
	private IPremArapDao premArapDao;

	/**
	 * @Fields fundTransDao : 保单投资连结交易Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_FundTransDao")
	private IFundTransDao fundTransDao;

	/**
	 * @Fields saChangeDao : 可选责任组保额变更记录表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_SaChangeDao")
	private ISaChangeDao saChangeDao;

	/**
	 * @Fields policyChangeDao : 保单变更Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PolicyChangeDao")
	private IPolicyChangeDao policyChangeDao;

	/**
	 * 
	 * @description 查询险种对应的扣费项目配置列表
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.common.service.ICalculateIlpuDueFee#queryILPUFeeTypesInOrder(com.nci.tunan.pa.interfaces.model.po.ContractInvestPO,
	 *      java.math.BigDecimal)
	 * @param contractInvestPO 投连信息
	 * @param businessPrdId    产品ID
	 * @return
	 */
	public List<ILPUFeeType> queryILPUFeeTypesInOrder(ContractInvestPO contractInvestPO, BigDecimal businessPrdId) {
		// 1.调用接口查询扣费项配置列表
		List<ILPUFeeType> feeTypeList = new ArrayList<ILPUFeeType>();
		MmsUniversalFeeQueryReqVO mmsUniversalFeeQueryReqVO = new MmsUniversalFeeQueryReqVO();
		mmsUniversalFeeQueryReqVO.setBusinessPrdId(businessPrdId);// @invalid 产品id
		mmsUniversalFeeQueryReqVO.setChargeLink("1");
		logger.info("---------万能保单id:" + contractInvestPO.getPolicyId() + "-----调用接口查询扣费项配置列表入参----"
				+ XmlHelper.classToXml(mmsUniversalFeeQueryReqVO));
		MmsUniversalFeeQueryResVO mmsUniversalFeeQueryResVO = prdIAS
				.prdimmsuniversalfeequeryuccuniversalFeeQuery(mmsUniversalFeeQueryReqVO);
		logger.info("------------万能保单id:" + contractInvestPO.getPolicyId() + "--调用接口查询扣费项配置列表出参----"
				+ XmlHelper.classToXml(mmsUniversalFeeQueryResVO));
		if (mmsUniversalFeeQueryResVO != null) {
			List<MmsUniversalFeeQueryVO> mmsUniversalFeeQueryVOs = mmsUniversalFeeQueryResVO
					.getMmsUniversalFeeQueryVOList();
			for (MmsUniversalFeeQueryVO mmsUFeeQuery : mmsUniversalFeeQueryVOs) {
				if (Constants.MMS_CHARGETYPE_1.equals(mmsUFeeQuery.getChargeType())
						|| Constants.MMS_CHARGETYPE_2.equals(mmsUFeeQuery.getChargeType())
						|| Constants.MMS_CHARGETYPE_3.equals(mmsUFeeQuery.getChargeType())) {
					feeTypeList.add(BeanUtils.copyProperties(ILPUFeeType.class, mmsUFeeQuery));
				}
			}
		}

		if (!CollectionUtilEx.isEmpty(feeTypeList)) {
			// @invalid 排序
			ILPUFeeType feeType = new ILPUFeeType();
			for (int i = 0; i < feeTypeList.size(); i++) {
				for (int j = 0; j < feeTypeList.size() - i - 1; j++) {
					int num1 = Integer.parseInt(feeTypeList.get(j).getPaymentOrder());
					int num2 = Integer.parseInt(feeTypeList.get(j + 1).getPaymentOrder());
					if (num1 > num2) {
						feeType = feeTypeList.get(j);
						feeTypeList.set(j, feeTypeList.get(j + 1));
						feeTypeList.set(j + 1, feeType);
					}
				}
			}
			ContractBusiProdPO conBusiProdPO = new ContractBusiProdPO();
			if (contractInvestPO != null && contractInvestPO.getBusiItemId() != null) {
				// @invalid 查询险种信息
				conBusiProdPO.setBusiItemId(contractInvestPO.getBusiItemId());
				conBusiProdPO = contractBusiProdDao.findContractBusiProdByBusiItemId(conBusiProdPO);
			}
			ContractMasterPO contractMasterPO = new ContractMasterPO();
			if (conBusiProdPO != null && conBusiProdPO.getPolicyId() != null) {
				// @invalid 查询保单信息
				contractMasterPO.setPolicyId(conBusiProdPO.getPolicyId());
				contractMasterPO = contractMasterDao.findContractMasterByPolicyId(contractMasterPO);
			}

			// 2.查询所有类型的上次扣费日
			PolicyFundChargePO policyFundChargePO = new PolicyFundChargePO();
			policyFundChargePO.setPolicyId(conBusiProdPO.getPolicyId());// @invalid 保单id
			policyFundChargePO.setBusiItemId(conBusiProdPO.getBusiItemId());// @invalid 险种id
			policyFundChargePO.setString("SkipLimit", "");// @invalid 去除条数约束
			List<PolicyFundChargePO> policyFundChargePOs = ilpuChargeDeductionDao.queryLastDate(policyFundChargePO);
			for (ILPUFeeType ilpuFeeType : feeTypeList) {
				String charCode = ilpuFeeType.getChargeType();// @invalid 扣费类型
				Date lastChargeDate = null;// @invalid 上次扣费日

				for (PolicyFundChargePO pfChargePO : policyFundChargePOs) {
					// @invalid 找到对应费用类型的上次扣费日信息
					if (pfChargePO.getChargeCode().trim().equals(charCode)) {
						lastChargeDate = pfChargePO.getLastChargeDate();
						break;
					}
				}

				if (lastChargeDate == null) {
					lastChargeDate = contractMasterPO.getValidateDate();
					// 3.创建扣费项对应的实际扣费日记录
					policyFundChargePO = new PolicyFundChargePO();
					policyFundChargePO.setPolicyId(conBusiProdPO.getPolicyId()); // @invalid 保单id
					policyFundChargePO.setBusiItemId(conBusiProdPO.getBusiItemId());// @invalid 险种id
					policyFundChargePO.setProductId(conBusiProdPO.getBusiPrdId()); // @invalid 精算产品id
					policyFundChargePO.setChargeCode(charCode); // @invalid 扣费类型
					policyFundChargePO.setLastChargeDate(lastChargeDate);// @invalid 上次扣费日
					policyFundChargePO.setItemId(contractInvestPO.getItemId());// @invalid 责任组id
//					policyFundChargePO = policyFundChargeDao.addPolicyFundCharge(policyFundChargePO); // 重庆农商行销售数据提数阻断 ORA-01031 权限不足 保单提数为什么需要更新表待排查
				}
				ilpuFeeType.setLastChargeDate(lastChargeDate);
			}
		}
		return feeTypeList;
	}

	/**
	 * 
	 * @description 计算扣除金额
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.common.service.ICalculateIlpuDueFee#calcFeeAmount(com.nci.tunan.pa.interfaces.model.po.ContractInvestPO,
	 *      com.nci.tunan.pa.batch.ilpuchargededuction.vo.ILPUFeeType,
	 *      java.util.Date, java.util.Date, java.math.BigDecimal, java.lang.String,
	 *      java.math.BigDecimal)
	 * @param conInvestPO    投连信息
	 * @param ilpuFeeType    类型
	 * @param lastChargeDate 开始时间
	 * @param curPriceDate   结束时间
	 * @param price          价格
	 * @param serviceCode    业务项
	 * @param lastBalance    上一次价值
	 * @return
	 */
	public MmsCalcAccountFeeResVO calcFeeAmount(ContractInvestPO conInvestPO, ILPUFeeType ilpuFeeType,
			Date lastChargeDate, Date curPriceDate, BigDecimal price, String serviceCode, BigDecimal lastBalance) {
		ContractBusiProdPO conBusiProdPO = new ContractBusiProdPO();
		ContractMasterPO contractMasterPO = new ContractMasterPO();
		Date applyDate = null;
		// @invalid 若是保单复效调此逻辑，查询险种和保单信息用入参中的conInvestPO值
		if ("RE".equals(serviceCode) && conInvestPO.getBusiItemId() != null && conInvestPO.getPolicyId() != null) {
			conBusiProdPO.setBusiItemId(conInvestPO.getBusiItemId());
			conBusiProdPO = contractBusiProdDao.findContractBusiProdByBusiItemId(conBusiProdPO);

			contractMasterPO.setPolicyId(conInvestPO.getPolicyId());
			contractMasterPO = contractMasterDao.findContractMasterByPolicyId(contractMasterPO);
			applyDate = contractMasterPO.getApplyDate();
		} else {
			if (conInvestPO != null && conInvestPO.getBusiItemId() != null) {
				// @invalid 查询险种信息
				conBusiProdPO.setBusiItemId(conInvestPO.getBusiItemId());
				conBusiProdPO = contractBusiProdDao.findContractBusiProdByBusiItemId(conBusiProdPO);
			}
			if (conInvestPO != null && conInvestPO.getPolicyId() != null) {
				// @invalid 查询保单信息
				contractMasterPO.setPolicyId(conInvestPO.getPolicyId());
				contractMasterPO = contractMasterDao.findContractMasterByPolicyId(contractMasterPO);
				applyDate = contractMasterPO.getApplyDate();
			}
		}

		BigDecimal dueFeeAmount = new BigDecimal(0);

		// 1.查询责任组信息
		ContractProductPO conProduct = new ContractProductPO();
		conProduct.setPolicyId(conBusiProdPO.getPolicyId());
		conProduct.setBusiItemId(conBusiProdPO.getBusiItemId());
		List<ContractProductPO> contractProductPOs = contractProductDao.findAllContractProduct(conProduct);
		if (!CollectionUtilEx.isEmpty(contractProductPOs)) {
			conProduct = contractProductPOs.get(0);
		}

		// @invalid 万能型个人产品保全精算原则 :暂缓期满时仍未交纳续期保费的，将停止收取风险保险费但仍将收取保单管理费
		// @invalid 查询扣费时点，是否缴纳保费 ：判断扣费时点>下期应缴日 则未交
		if (ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_3)) {

			if (conProduct.getPauseDate() != null && conProduct.getPauseDate().before(curPriceDate)
					&& conProduct.getIsPause() != null) {
				// @invalid 计算扣费日和暂缓期日所在的年份和月份 add by zhouly
				int pauseYear = DateUtilsEx.getYear(conProduct.getPauseDate());// @invalid 暂缓日所在年份
				int curYear = DateUtilsEx.getYear(curPriceDate);// @invalid 扣费日所在年份
				int pauseMonth = DateUtilsEx.getMonth(conProduct.getPauseDate());// @invalid 暂缓日所在月份
				int curMonth = DateUtilsEx.getMonth(curPriceDate);// @invalid 扣费日所在月份
				if (pauseYear == curYear && pauseMonth == curMonth) {
					// @invalid 若两个日期之间的月份相等，则需要计算暂缓期满日到上一扣费日之间的风险保费
					curPriceDate = conProduct.getPauseDate();// @invalid 将本次扣费日变为暂缓日进行扣费
				} else {
					// @invalid 若两个日期之间的月份不相等，则不需要计算风险保费
					ContractExtendPO contractExtendPO;
					contractExtendPO = new ContractExtendPO();
					contractExtendPO.setItemId(conProduct.getItemId());
					contractExtendPO = contractExtendDao.findContractExtend(contractExtendPO);

					if (contractExtendPO != null && contractExtendPO.getPayDueDate() != null
							&& contractExtendPO.getPayDueDate().before(curPriceDate)) {
						return null;
					}
				}
			}

		}

		// 2.查询被保人信息
		CustomerPO customerPO = queryInsured(contractProductPOs.get(0));

		// 3. 结算时的年龄

		int age = DateUtilsEx.getAge(customerPO.getCustomerBirthday(), curPriceDate);
		double policyMonth = DateUtilsEx.getMonthAmount(contractMasterPO.getValidateDate(), curPriceDate);
		int policyYear = (int) DateUtilsEx.getYearAmount(contractMasterPO.getValidateDate(), curPriceDate);

		HashMap<String, Object> map = new HashMap<String, Object>();

		if (contractProductPOs != null && contractProductPOs.size() > 0) {
			map.put("productId", contractProductPOs.get(0).getProductId());// @invalid 产品id
		} else {
			map.put("productId", conBusiProdPO.getBusiPrdId());// @invalid 产品id
		}
		map.put("FundCode", conInvestPO.getAccountCode()); // @invalid 基金代码

		// 3.保单实际经过日期集合
		List<Date> dataList = new ArrayList<Date>();
		List<List<Date>> dfList = new ArrayList<List<Date>>();

		// 4.账户注销的保全项
		// @invalid 1保单管理费、2资产管理费、3风险保费
		if (ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_1)) {
			int durDay = 0;
			if (conInvestPO.getInvestAccountType().compareTo(BigDecimal.valueOf(1)) == 0) {
				// @invalid 投连保单实际经过天数
				dataList = getIlpPolicyDurDay(lastChargeDate, curPriceDate, ilpuFeeType, conBusiProdPO);
				durDay = dataList.size();
			} else if (conInvestPO.getInvestAccountType().compareTo(BigDecimal.valueOf(2)) == 0) {
				// @invalid 万能保单实际经过天数
				dataList = getUpPolicyDurDay(lastChargeDate, curPriceDate, ilpuFeeType, conBusiProdPO);
				durDay = dataList.size();
			}
			logger.info("保单实际经过天数为：" + durDay);
			map.put("DurDay", durDay);// @invalid 保单实际经过天数
			int isWholeMonthFlag = 0;
			int max = DateUtilsEx.getMaxDayOfMonth(curPriceDate);
			if (max <= durDay && !"RE".equals(serviceCode)) {
				isWholeMonthFlag = 1;
			}
			map.put("IsWholeMonthFlag", isWholeMonthFlag);// @invalid 是否满整月
		} else if (ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_2)) {
			// @invalid 本个计价日距上次扣费计价日的天数
			int day = (int) DateUtilsEx.getDayAmount(lastChargeDate, curPriceDate);
			// @invalid 若首次计价日和当前计价日在同一天，扣费需求中描述（保单生效日【含】止计价日【含】之间实际经过的天数）
			if (ilpuFeeType.getFirstPriceDate() != null
					&& ilpuFeeType.getFirstPriceDate().compareTo(curPriceDate) == 0) {
				day = (int) DateUtilsEx.getDayAmount(conProduct.getValidateDate(), curPriceDate) + 1;
			}
			// @invalid 投连保单实际经过天数
			dataList = getIlpPolicyDurDay(lastChargeDate, curPriceDate, ilpuFeeType, conBusiProdPO);
			if (dataList != null && dataList.size() > 0) {
				if (dataList.size() > 1) {
					Date end = dataList.get(dataList.size() - 1);
					map.put("EndAccountDate", DateUtilsEx.formatToString(end, "yyyy-MM-dd"));
				} else {
					Date end = dataList.get(0);
					map.put("EndAccountDate", DateUtilsEx.formatToString(end, "yyyy-MM-dd"));
				}
			}

			map.put("PolicyMonth", BigDecimal.valueOf(policyMonth));// @invalid 保单月度
			map.put("PolicyEffectiveDate",
					DateUtilsEx.formatToString(contractMasterPO.getValidateDate(), "yyyy-MM-dd"));// @invalid 保单生效日
			map.put("OffPrice", price);// @invalid 卖出价
			// @invalid add by zhouly 账户注销情况，算风险保费不包含当天
			logger.info("保单实际经过天数为：" + day);
			map.put("ValuationDay", day);// @invalid 本个计价日距上个计价日的天数
			// @invalid 费用收取日投资账户的投资单位数
			map.put("Unit", conInvestPO.getAccumUnits());
		} else if (ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_3)) {

			if (conInvestPO.getInvestAccountType().compareTo(BigDecimal.valueOf(1)) == 0) {
				// @invalid 投连保单实际经过天数
				dataList = getIlpPolicyDurDay(lastChargeDate, curPriceDate, ilpuFeeType, conBusiProdPO);
			} else if (conInvestPO.getInvestAccountType().compareTo(BigDecimal.valueOf(2)) == 0) {
				// @invalid 万能保单实际经过天数
				dataList = getUpPolicyDurDay(lastChargeDate, curPriceDate, ilpuFeeType, conBusiProdPO);

				if (dataList.size() < 30 && dataList.size() >= 0) {// @invalid 整月情况下，就不需要考虑分段了
					// @invalid 计算万能风险保费时，如果保单状态发生变更，则需要分段计算，然后将结果进行汇总
					List<Date> list = new ArrayList<Date>();
					for (int i = 1; i < dataList.size(); i++) {
						if (i == 1) {
							list.add(dataList.get(0));
						}
						int day = (int) DateUtilsEx.getDayAmount(dataList.get(i - 1), dataList.get(i));
						if (day > 1) {
							dfList.add(list);// @invalid 添加到分段集合中
							list = new ArrayList<Date>();
						} else {
							list.add(dataList.get(i));
						}
					}

				}
			}
			// @invalid 本月保单实际经过的天数
			int durDay = dataList.size();
			// @invalid 扣费频率为每月的
			if (Constants.MMS_PAYMENTFREQ_2.equals(ilpuFeeType.getPaymentFreq())) {
				durDay = DateUtilsEx.getMaxDayOfMonth(curPriceDate);
			}
			if (durDay > 0) {
				// @invalid add by zhouly 账户注销情况，算风险保费不包含当天
				logger.info("保单实际经过天数为：" + durDay);

				ContractProductPO contractProductPO = new ContractProductPO();
				contractProductPO.setPolicyId(conBusiProdPO.getPolicyId());
				contractProductPO.setBusiItemId(conBusiProdPO.getBusiItemId());
				contractProductPO = ilpuChargeDeductionDao.queryPolicyAmount(contractProductPO);

				map.put("Unit", conInvestPO.getAccumUnits()); // @invalid 费用收取日投资账户的投资单位数
				map.put("OffPrice", price);// @invalid 卖出价
				map.put("lastDayBasicSA", contractProductPO.get("sum_amount")); // @invalid 结算日前一日的基本保险金额
				// @invalid add by zhouly 结算日前一日风险保额=max（所交保费，保单账户价值）针对903、904险种
				if (Constants.BUSIPROD_CODE_903.equals(conBusiProdPO.getBusiProdCode())
						|| Constants.BUSIPROD_CODE_904.equals(conBusiProdPO.getBusiProdCode())) {
					if (conInvestPO.getInterestCapital()
							.compareTo(new BigDecimal(contractProductPO.get("sum_total_prem_af").toString())) > 0) {
						FundSettlementPO fundSettlementPo = new FundSettlementPO();
						fundSettlementPo.setInvestId(conInvestPO.getListId());
						FundSettlementPO fundSettlement = fundSettlementDao.findFundSettlement(fundSettlementPo);
						if (fundSettlement != null && fundSettlement.getLastBalance() != null) {
							lastBalance = fundSettlement.getLastBalance();
							if (serviceCode != null && "CT".equals(serviceCode)) {
								lastBalance = fundSettlement.getBalance();
							}
						} else {
							lastBalance = new BigDecimal(0);
						}
						BigDecimal lastBasicSA = getLastBasicSA(lastBalance, lastChargeDate, curPriceDate,
								conBusiProdPO.getPolicyId());
						map.put("LastBasicSA", lastBasicSA); // @invalid 结算日前一日的基本保险金额
					} else {
						map.put("LastBasicSA", contractProductPO.get("sum_total_prem_af")); // @invalid 结算日前一日的基本保险金额
					}
				}
				if (conInvestPO.getAccumUnits().compareTo(new BigDecimal(0)) == 1
						&& conInvestPO.getInterestCapital().compareTo(new BigDecimal(1)) == -1) {
					BigDecimal accountValue = new BigDecimal(0);
					accountValue = conInvestPO.getAccumUnits();// @invalid 单位数
					accountValue = accountValue.multiply(price);// @invalid .setScale(2, RoundingMode.HALF_EVEN);
					map.put("AccountValue", accountValue);
				} else {
					if (serviceCode != null && serviceCode.startsWith("RATE")) {// @invalid 理赔结案，时段内未结算的利息
						String[] str = serviceCode.split(",");
						BigDecimal rateMoney = new BigDecimal(str[1]);
						map.put("AccountValue", conInvestPO.getInterestCapital().add(rateMoney));// @invalid 账户价值
					} else {
						// 如果是投连险按单位数乘以单位价格等于账户价值
						if (conInvestPO.getInvestAccountType().equals(Constants.INVEST_ACCOUNT_TYPE_ILP)) {
							// 如果单位为小于等于0，账户价值为0
							if (conInvestPO.getAccumUnits().compareTo(new BigDecimal(0)) <= 0) {
								map.put("AccountValue", BigDecimal.ZERO);// @invalid 账户价值
							} else {// 如果单位为大于0，账户价值为单位数乘以单位价格
								BigDecimal accountValue = new BigDecimal(0);
								accountValue = conInvestPO.getAccumUnits();// @invalid 单位数
								accountValue = accountValue.multiply(price);// @invalid .setScale(2,
																			// RoundingMode.HALF_EVEN);
								map.put("AccountValue", accountValue);
							}
						} else {
							map.put("AccountValue", conInvestPO.getInterestCapital());// @invalid 账户价值
						}
					}

				}

				Date start = DateUtilsEx.addDay(dataList.get(0), -1);
				Date end = dataList.get(dataList.size() - 1);
				map.put("Premium", contractProductPO.get("sum_total_prem_af")); // @invalid 当期保费
				map.put("PolicyMonth", BigDecimal.valueOf(policyMonth)); // @invalid 保单月度
				map.put("PolicyYear", policyYear); // @invalid 保单年度
				map.put("PolicyEffectiveDate",
						DateUtilsEx.formatToString(contractMasterPO.getValidateDate(), "yyyy-MM-dd"));// @invalid 保单生效日
				map.put("StartAccountDate", DateUtilsEx.formatToString(start, "yyyy-MM-dd"));
				map.put("EndAccountDate", DateUtilsEx.formatToString(end, "yyyy-MM-dd"));
				map.put("RiskPremPasseddays", new BigDecimal(durDay));
				map.put("DurDay", durDay);// @invalid 保单实际经过天数
				map.put("CustomerBirthday", DateUtilsEx.formatToString(customerPO.getCustomerBirthday(), "yyyy-MM-dd"));// @invalid
																														// 被保险人出生日期
				// @invalid 保单已经贷款终止了的单子，计算年龄按当时算，产品要用
				if (end.before(curPriceDate)) {
					age = DateUtilsEx.getAge(customerPO.getCustomerBirthday(), end);
				}
				map.put("Age", age);// @invalid 被保人投保年龄
				if (customerPO.getCustomerGender() != null) {
					map.put("Gender", customerPO.getCustomerGender().intValue());// @invalid 被保人性别
				}
				// @invalid 查询加非点评值
				ExtraPremPO extraPremPO = new ExtraPremPO();
				extraPremPO.setPolicyId(conProduct.getPolicyId());
				extraPremPO.setPolicyCode(conProduct.getPolicyCode());
				extraPremPO.setItemId(conProduct.getItemId());
				List<ExtraPremPO> findAllExtraPrem = extraPremDao.findAllExtraPrem(extraPremPO);
				if (findAllExtraPrem != null && findAllExtraPrem.size() > 0) {
					ExtraPremPO expr = findAllExtraPrem.get(0);
					BigDecimal emValue = expr.getEmValue();
					if (expr.getStartDate().after(curPriceDate) || expr.getEndDate().before(curPriceDate)) {
						emValue = new BigDecimal(0);
					}
					map.put("EmValue", emValue);// @invalid EmValue em值
				} else {
					map.put("EmValue", BigDecimal.valueOf(0));// @invalid EmValue em值
				}

				if (dfList.size() <= 0 || dfList == null) {
					// @invalid 时间不分段，直接进行计算
					String riskSA = "0";
					if (Constants.BUSIPROD_CODE_909.equals(conBusiProdPO.getBusiProdCode())
							|| Constants.BUSIPROD_CODE_907.equals(conBusiProdPO.getBusiProdCode())
							|| Constants.BUSIPROD_CODE_903.equals(conBusiProdPO.getBusiProdCode())
							|| Constants.BUSIPROD_CODE_904.equals(conBusiProdPO.getBusiProdCode())
							|| Constants.BUSIPROD_CODE_905.equals(conBusiProdPO.getBusiProdCode())
							|| Constants.BUSIPROD_CODE_925.equals(conBusiProdPO.getBusiProdCode())
							|| Constants.BUSIPROD_CODE_926.equals(conBusiProdPO.getBusiProdCode())
							|| Constants.BUSIPROD_CODE_927.equals(conBusiProdPO.getBusiProdCode())) {
						conProduct.set("busi_prod_code", conBusiProdPO.getBusiProdCode());
						riskSA = calcRiskSA(dataList, conInvestPO, conProduct, start, end, lastBalance, conBusiProdPO);
					}
					map.put("RiskSA", riskSA);
				}

				map.put("SA", conProduct.getAmount());// @invalid 算风险保费的入参中增加参数SA（基本保险金额），在入参的map中赋值即可
				int isWholeMonthFlag = 0;
				int max = DateUtilsEx.getMaxDayOfMonth(curPriceDate);
				if (max <= durDay) {
					isWholeMonthFlag = 1;
				}
				map.put("IsWholeMonthFlag", isWholeMonthFlag);// @invalid 是否满整月
				map.put("Channel", 0);// @invalid 渠道，如果为新核心的默认为0即可
			} else {
				return null;
			}

		}

		if ("CT".equals(serviceCode) || "EA".equals(serviceCode) || "XT".equals(serviceCode)
				|| "IT".equals(serviceCode)) {
			map.put("IsWholeMonthFlag", 0);// @invalid 非整月
		}
		map.put("MonthIssuRate", Constants.douH);// @invalid 投资账户管理费用日收取比例
		map.put("PolicyInsuranceDate", applyDate);// @invalid 投保日期
		List<MmsAccountFeeInfoReqVO> mmsAccountFeeInfoReqVOs = new ArrayList<MmsAccountFeeInfoReqVO>();

		if (dfList.size() > 0) {
			// @invalid 分段计算
			for (List<Date> list : dfList) {
				Date start = DateUtilsEx.addDay(list.get(0), -1);
				Date end = list.get(list.size() - 1);

				HashMap<String, Object> param = map;// @invalid 复制之前的参数信息
				// @invalid 给需要变化的参数重新复制
				param.put("StartAccountDate", DateUtilsEx.formatToString(start, "yyyy-MM-dd"));
				param.put("EndAccountDate", DateUtilsEx.formatToString(end, "yyyy-MM-dd"));
				param.put("DurDay", list.size());// @invalid 保单实际经过天数
				param.put("RiskPremPasseddays", new BigDecimal(list.size()));
				String riskSA = "0";
				if (Constants.BUSIPROD_CODE_909.equals(conBusiProdPO.getBusiProdCode())
						|| Constants.BUSIPROD_CODE_907.equals(conBusiProdPO.getBusiProdCode())
						|| Constants.BUSIPROD_CODE_903.equals(conBusiProdPO.getBusiProdCode())
						|| Constants.BUSIPROD_CODE_904.equals(conBusiProdPO.getBusiProdCode())
						|| Constants.BUSIPROD_CODE_905.equals(conBusiProdPO.getBusiProdCode())
						|| Constants.BUSIPROD_CODE_925.equals(conBusiProdPO.getBusiProdCode())
						|| Constants.BUSIPROD_CODE_926.equals(conBusiProdPO.getBusiProdCode())
						|| Constants.BUSIPROD_CODE_927.equals(conBusiProdPO.getBusiProdCode())) {
					conProduct.set("busi_prod_code", conBusiProdPO.getBusiProdCode());
					riskSA = calcRiskSA(list, conInvestPO, conProduct, start, end, lastBalance, conBusiProdPO);
				}
				param.put("RiskSA", riskSA);

				List<HashMap<String, Object>> keyValueList = new ArrayList<HashMap<String, Object>>();
				keyValueList.add(param);

				MmsAccountFeeInfoReqVO mmsCalcParam = new MmsAccountFeeInfoReqVO();
				mmsCalcParam.setChargeType(Integer.parseInt(ilpuFeeType.getChargeType().trim()));
				mmsCalcParam.setProductCodeSys(conBusiProdPO.getBusiProdCode());// @invalid 产品编码
				mmsCalcParam.setBusinessPrdId(conBusiProdPO.getBusiPrdId());// @invalid 产品id
				mmsCalcParam.setKeyValueList(keyValueList);// @invalid 扣费项配置列表
				mmsCalcParam.setChargeLink("1");// @invalid 投连万能扣费
				// @invalid 计算参数添加到集合中
				mmsAccountFeeInfoReqVOs.add(mmsCalcParam);
			}

		} else {
			List<HashMap<String, Object>> keyValueList = new ArrayList<HashMap<String, Object>>();
			keyValueList.add(map);

			MmsAccountFeeInfoReqVO mmsAccountFeeInfoReqVO = new MmsAccountFeeInfoReqVO();
			mmsAccountFeeInfoReqVO.setChargeType(Integer.parseInt(ilpuFeeType.getChargeType().trim()));
			mmsAccountFeeInfoReqVO.setProductCodeSys(conBusiProdPO.getBusiProdCode());// @invalid 产品编码
			mmsAccountFeeInfoReqVO.setBusinessPrdId(conBusiProdPO.getBusiPrdId());// @invalid 产品id
			mmsAccountFeeInfoReqVO.setKeyValueList(keyValueList);// @invalid 扣费项配置列表
			mmsAccountFeeInfoReqVO.setChargeLink("1");// @invalid 投连万能扣费
			mmsAccountFeeInfoReqVOs.add(mmsAccountFeeInfoReqVO);
		}
		MmsCalcAccountFeeReqVO mmsCalcAccountFeeReqVO = new MmsCalcAccountFeeReqVO();
		mmsCalcAccountFeeReqVO.setAccountFeeInfoReqVOList(mmsAccountFeeInfoReqVOs);

		logger.info("---------------------计算投连万能扣费金额-------------------------");
		logger.info("——————————计算投连万能扣费金额--参数：" + conBusiProdPO.getPolicyId()
				+ XmlHelper.classToXml(mmsCalcAccountFeeReqVO));
		// @invalid 计算投连万能扣费金额
		MmsCalcAccountFeeResVO mmsCalcAccountFeeResVO = prdIAS
				.prdimmscalcaccountfeeucccalcAccountFee(mmsCalcAccountFeeReqVO);
		logger.info("——————————计算投连万能扣费金额--结果：" + conBusiProdPO.getPolicyId() + " "
				+ XmlHelper.classToXml(mmsCalcAccountFeeResVO));
		if (mmsCalcAccountFeeResVO != null
				&& CollectionUtils.isNotEmpty(mmsCalcAccountFeeResVO.getAccountFeeInfoResVOList())) {
			dueFeeAmount = mmsCalcAccountFeeResVO.getTotalFeeAmount();
			// @invalid 对于公式返回单位数换算为金额
			if (dueFeeAmount != null && Constants.FORMULA_RESULT_TYPE
					.equals(mmsCalcAccountFeeResVO.getAccountFeeInfoResVOList().get(0).getFormulaResultType())) {
				dueFeeAmount = dueFeeAmount.multiply(price);
			}
		}
		if (dueFeeAmount == null) {
			dueFeeAmount = new BigDecimal(0);
		}
		logger.info("——————————计算投连万能扣费金额--结果：" + dueFeeAmount);
		return mmsCalcAccountFeeResVO;
	}

	/*
	 * 查询被保人信息
	 */
	/**
	 * 
	 * @description 查询被保人信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param conProductPO 责任组PO
	 * @return
	 */
	private CustomerPO queryInsured(ContractProductPO conProductPO) {
		// 1.查询被保人信息
		BenefitInsuredPO benefitInsuredPO = new BenefitInsuredPO();
		benefitInsuredPO.setPolicyId(conProductPO.getPolicyId());// @invalid 保单id
		benefitInsuredPO.setBusiItemId(conProductPO.getBusiItemId());// @invalid 险种id
		benefitInsuredPO.setOrderId(BigDecimal.valueOf(1));// @invalid 第一被保人
		List<BenefitInsuredPO> benefitInsuredPOs = benefitInsuredDao.findAllBenefitInsured(benefitInsuredPO);
		CustomerPO customerPO = null;
		InsuredListPO insuredListPO = null;
		if (!CollectionUtilEx.isEmpty(benefitInsuredPOs)) {
			benefitInsuredPO = benefitInsuredPOs.get(0);
			// @invalid 查询被保人信息
			insuredListPO = new InsuredListPO();
			insuredListPO.setListId(benefitInsuredPO.getInsuredId());
			insuredListPO = insuredListDao.findInsuredList(insuredListPO);

			customerPO = new CustomerPO();
			customerPO.setCustomerId(insuredListPO.getCustomerId());
			customerPO = customerDao.findCustomerByCustomerId(customerPO);
		}
		return customerPO;
	}

	/**
	 * 
	 * @description 计算投连保单实际经过天数
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param lastChargeDate 上次扣费日
	 * @param curPriceDate   本次扣费日
	 * @param ilpuFeeType    费用类型
	 * @param conBusiProdPO  险种PO
	 * @return
	 */
	private List<Date> getIlpPolicyDurDay(Date lastChargeDate, Date curPriceDate, ILPUFeeType ilpuFeeType,
			ContractBusiProdPO conBusiProdPO) {
		// @invalid 保单实际经过日期
		List<Date> dateList = new ArrayList<Date>();

		// 1.查询账户对应险种的责任状态变化历史
		ProductLiabilityChangePO productLiabilityChangePO = new ProductLiabilityChangePO();
		productLiabilityChangePO.setPolicyId(conBusiProdPO.getPolicyId());
		productLiabilityChangePO.setBusiItemId(conBusiProdPO.getBusiItemId());
		productLiabilityChangePO.set("start", lastChargeDate);// @invalid 上次扣费日
		productLiabilityChangePO.set("end", curPriceDate);// @invalid 当前日期
		List<ProductLiabilityChangePO> proLiabilityChangePOs = ilpuChargeDeductionDao
				.queryLiabilityChangeHistory(productLiabilityChangePO);

		// @invalid 契约投保时，实际经过天数为保单生效日（含）至计价日（含）之间实际经过的天数；
		if (CollectionUtilEx.isEmpty(proLiabilityChangePOs)) {
			// @invalid 含保单生效日
			if (lastChargeDate.compareTo(conBusiProdPO.getValidateDate()) == 0) {
				lastChargeDate = DateUtilsEx.addDay(lastChargeDate, -1);
			}
			dateList.addAll(addDateList(lastChargeDate, curPriceDate));
			return dateList;
		}

		Date ineffectDate = null;// @invalid 失效日期=上次扣费日+宽限期+1
		Date effectDate = null;// @invalid 复效日期 [宽限期内不会失效]
		Date endDate = null;// @invalid 终止日期

		for (int i = 0; i < proLiabilityChangePOs.size(); i++) {
			ProductLiabilityChangePO proLiabilityChg = proLiabilityChangePOs.get(i);
			if (proLiabilityChg != null) {
				// @invalid 失效日期
				if (proLiabilityChg.getLiabilityChanged().equals(Constants.LIABILITY_STATE_INEFFECT)) {
					ineffectDate = proLiabilityChg.getChangeDate();
				}

				// @invalid 复效日期
				if (proLiabilityChg.getLiabilityChanged().equals(Constants.LIABILITY_STATE_EFFECT)) {
					effectDate = proLiabilityChg.getChangeDate();
				}

				// @invalid 终止日期（查询的轨迹表，存在退保终止之后又回退的情况）
				if (proLiabilityChg.getLiabilityChanged().equals(Constants.LIABILITY_STATE__TERMINATED)
						&& conBusiProdPO.getLiabilityState().equals(Constants.LIABILITY_STATE__TERMINATED)) {
					endDate = proLiabilityChg.getChangeDate();
				}
			}
		}

		if (ilpuFeeType.getChargeType().equals(Constants.MMS_CHARGETYPE_1)
				|| ilpuFeeType.getChargeType().equals(Constants.MMS_CHARGETYPE_3)) {
			// @invalid 保单管理费、风险保费
			// @invalid 复效时计算宽限期内应收的保单管理费对应的实际经过天数为：宽限期开始前一计价日（不含）至宽限期满日（含）间实际经过天数
			// @invalid 上次扣费日<失效日
			if (ineffectDate != null && ineffectDate.compareTo(lastChargeDate) > 0) {
				dateList.addAll(addDateList(DateUtilsEx.addDay(lastChargeDate, -1), ineffectDate));
			}

			// @invalid 复效后最近一个计价日结算情况下：保单管理费实际经过天数为复效日次日（含）至该计价日（含）间实际经过天数。
			// @invalid 上次扣费日>复效日
			if (effectDate != null && curPriceDate.compareTo(effectDate) > 0) {
				dateList.addAll(addDateList(DateUtilsEx.addDay(effectDate, 1), curPriceDate));
			}
		}

		// @invalid 合同终止结算情况下，实际经过天数为前一个结算日（不含）至合同终止日（不含）之间实际经过的天数
		if (endDate != null) {
			dateList.addAll(addDateList(DateUtilsEx.addDay(curPriceDate, -1), endDate));
		}

		return dateList;
	}

	/**
	 * 
	 * @description 计算万能险 保单实际经过天数
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param lastChargeDate 上次扣费日
	 * @param curPriceDate   本次扣费日
	 * @param ilpuFeeType    费用类型
	 * @param conBusiProdPO  险种
	 * @return
	 */
	private List<Date> getUpPolicyDurDay(Date lastChargeDate, Date curPriceDate, ILPUFeeType ilpuFeeType,
			ContractBusiProdPO conBusiProdPO) {
		// 1. 保单实际经过日期
		List<Date> dateList = new ArrayList<Date>();

		// 2. 查询责任组信息：暂缓期满日
		ContractProductPO contractProductPO = new ContractProductPO();
		contractProductPO.setPolicyId(conBusiProdPO.getPolicyId());
		contractProductPO.setBusiItemId(conBusiProdPO.getBusiItemId());
		List<ContractProductPO> conProductPOs = contractProductDao.findAllContractProduct(contractProductPO);
		if (CollectionUtilEx.isEmpty(conProductPOs)) {
			return dateList;
		}
		contractProductPO = conProductPOs.get(0);

		// 3. 查询账户对应险种的责任状态变化历史
		ProductLiabilityChangePO productLiabilityChangePO = new ProductLiabilityChangePO();
		productLiabilityChangePO.setPolicyId(conBusiProdPO.getPolicyId());
		productLiabilityChangePO.setBusiItemId(conBusiProdPO.getBusiItemId());
		productLiabilityChangePO.set("start", lastChargeDate);// @invalid 上次扣费日
		productLiabilityChangePO.set("end", curPriceDate);// @invalid 当前日期
		List<ProductLiabilityChangePO> proLiabilityChangePOs = ilpuChargeDeductionDao
				.queryLiabilityChangeHistory(productLiabilityChangePO);

		// 4. 契约投保时，实际经过天数为保单生效日（含）至该结算日（含）之间实际经过的天数；
		if (CollectionUtilEx.isEmpty(proLiabilityChangePOs)) {
			// @invalid 保单管理费
			if (ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_1)) {
				// @invalid 含保单生效日
				if (lastChargeDate.compareTo(conBusiProdPO.getValidateDate()) == 0) {
					lastChargeDate = DateUtilsEx.addDay(lastChargeDate, -1);
				}
				dateList.addAll(addDateList(lastChargeDate, curPriceDate));
				return dateList;
			}

			// @invalid 风险保费 [无暂缓操作]
			if (ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_3)
					&& contractProductPO.getPauseDate() == null) {
				// @invalid 含保单生效日
				if (lastChargeDate.compareTo(conBusiProdPO.getValidateDate()) == 0) {
					lastChargeDate = DateUtilsEx.addDay(lastChargeDate, -1);
				}
				dateList.addAll(addDateList(lastChargeDate, curPriceDate));
				return dateList;
			}

		}

		Date ineffectDate = null;// @invalid 失效日期
		Date effectDate = null;// @invalid 复效日期
		Date endDate = null;// @invalid 终止日期

		for (int i = 0; i < proLiabilityChangePOs.size(); i++) {
			ProductLiabilityChangePO proLiabilityChg = proLiabilityChangePOs.get(i);
			if (proLiabilityChg != null) {
				// @invalid 失效日期
				if (proLiabilityChg.getLiabilityChanged().equals(Constants.LIABILITY_STATE_INEFFECT)) {
					ineffectDate = proLiabilityChg.getChangeDate();
				}
				// @invalid 复效日期
				if (proLiabilityChg.getLiabilityChanged().equals(Constants.LIABILITY_STATE_EFFECT)) {
					effectDate = proLiabilityChg.getChangeDate();
				}

				// @invalid 终止日期（存在保单退保终止以后回退的情况）
				if (proLiabilityChg.getLiabilityChanged().equals(Constants.LIABILITY_STATE__TERMINATED)) {
					// @invalid Redmine:61333，涉及缺陷6818
					if (conBusiProdPO.getLiabilityState().equals(Constants.LIABILITY_STATE__TERMINATED)) {
						endDate = proLiabilityChg.getChangeDate();
					} else {
						endDate = curPriceDate;
					}

				}
			}
		}

		// 5.保单管理费
		if (ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_1)
				|| ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_3)) {
			// @invalid 复效时计算宽限期内应收的保单管理费对应的实际经过天数为：宽限期开始前一日（不含）至宽限期满日（含）间实际经过天数；
			// @invalid 上次扣费日<失效日
			if (ineffectDate != null && ineffectDate.compareTo(lastChargeDate) > 0) {
				dateList.addAll(addDateList(DateUtilsEx.addDay(lastChargeDate, 1), ineffectDate));
			}

			// @invalid 复效后最近一个结算日结算情况下：保单管理费实际经过天数为复效日次日（含）至该结算日（含）间实际经过天数。
			// @invalid 当前扣费日>复效日
			if (effectDate != null && curPriceDate.compareTo(effectDate) > 0) {
				dateList.addAll(addDateList(effectDate, curPriceDate));
			}

			// @invalid 合同终止结算情况下，实际经过天数为前一个结算日（不含）至合同终止日（不含）之间实际经过的天数
			if (endDate != null) {
				dateList.addAll(addDateList(DateUtilsEx.addDay(lastChargeDate, -1), endDate));
			}
		}

		// 6.风险保险费
		if (ilpuFeeType.getChargeType().trim().equals(Constants.MMS_CHARGETYPE_3)) {

			if (contractProductPO.getPauseDate() != null) {
				// @invalid 暂缓期满日后的最近一个结算日结算情况下，实际经过天数为上一结算日（不含）至暂缓期满日（含）间实际经过天数；
				dateList.addAll(addDateList(lastChargeDate, contractProductPO.getPauseDate()));

				PremArapPO premArapPO = new PremArapPO();
				premArapPO.setPolicyCode(conBusiProdPO.getPolicyCode());
				premArapPO.setBusiProdCode(conBusiProdPO.getBusiProdCode());
				premArapPO.setArapFlag("1");// @invalid 应收
				premArapPO.setFeeType("G004740100");// @invalid 补收净投资-基本保费
				// @invalid premArapPO.setDueTime(contractProductPO.getPauseDate());//@invalid
				// 暂缓期满后
				List<PremArapPO> premArapPOs = premArapDao.findAllPremArap(premArapPO);
				if (premArapPOs != null && premArapPOs.size() > 0
						&& curPriceDate.compareTo(premArapPOs.get(0).getDueTime()) >= 0
						&& lastChargeDate.compareTo(premArapPOs.get(0).getDueTime()) <= 0) {
					// @invalid 暂缓期满后补交期交保险费后最近一个结算日结算利息情况下，实际经过天数为补交日次日（含）至该结算日（含）间实际经过的天数；
					Date start = premArapPOs.get(0).getDueTime();
					dateList.addAll(addDateList(start, curPriceDate));
				}
			}
		}

		return dateList;
	}

	/**
	 * @description 针对903 904计算结算日前一日账户价值
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param lastBalance    上一次价格
	 * @param lastChargeDate 上一时间
	 * @param curPriceDate   时间
	 * @param policyId       保单id
	 * @return
	 */
	private BigDecimal getLastBasicSA(BigDecimal lastBalance, Date lastChargeDate, Date curPriceDate,
			BigDecimal policyId) {
		FundTransPO fundTransPO = new FundTransPO();
		fundTransPO.setPolicyId(policyId);
		fundTransPO.set("start_date", DateUtilsEx.addDay(lastChargeDate, 1));
		fundTransPO.set("end_date", DateUtilsEx.addDay(curPriceDate, -1));
		List<FundTransPO> findAllFundTrans = fundTransDao.findAllFundTrans(fundTransPO);
		if (CollectionUtils.isNotEmpty(findAllFundTrans)) {
			for (FundTransPO fundTransPO1 : findAllFundTrans) {
				if (fundTransPO1.getTransType().equals(Constants.INVEST_TRANS_TYPE__IN)) {
					lastBalance.add(fundTransPO1.getTransAmount());
				} else if (fundTransPO1.getTransType().equals(Constants.INVEST_TRANS_TYPE__OUT)) {
					lastBalance.subtract(fundTransPO1.getTransAmount());
				}
			}
		}
		return lastBalance;
	}

	/**
	 * 
	 * @description 计算特殊基本保额
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param dateList       时间集合
	 * @param conInvestPO    投连交易PO
	 * @param conProductPO   责任组PO
	 * @param lastChargeDate 上次扣费日
	 * @param curPriceDate   计价日
	 * @param lastBalance    上次价值
	 * @param conBusiProdPO  险种PO
	 * @return
	 */
	private String calcRiskSA(List<Date> dateList, ContractInvestPO conInvestPO, ContractProductPO conProductPO,
			Date lastChargeDate, Date curPriceDate, BigDecimal lastBalance, ContractBusiProdPO conBusiProdPO) {
		String riskSA = "";

		// 1.查询自上次扣费日到当前扣费日之间的交易记录
		FundTransPO fundTransPO = new FundTransPO();
		fundTransPO.setListId(conInvestPO.getListId());// @invalid 账户id
		// @invalid fundTransPO.setTransType(BigDecimal.valueOf(1));//@invalid 卖出
		fundTransPO.set("start_date", lastChargeDate);
		fundTransPO.set("end_date", curPriceDate);
		List<FundTransPO> fundTransPOList = ilpuChargeDeductionDao.findFundTransList(fundTransPO);

		int isPayFlag = 0;// @invalid 是否发生部分领取 默认为没有发生过
		int policyMonth = 0;// @invalid 第一次部分领取保单年度月数

		double basicSA = 0;// @invalid 投保时的基本保额
		if (conProductPO.getInitialAmount() != null) {
			basicSA = conProductPO.getInitialAmount().intValue();
		}

		// 2.上次扣费日当天累积增加的保险费
		BigDecimal lastPrem = calcLastChargeDayPrem(conInvestPO, lastChargeDate);
		// 3.上次扣费日当天账户价值
		BigDecimal lastAv = new BigDecimal(0);
		logger.info("计算扣费保单号:" + conProductPO.getPolicyCode() + ",lastBalance值:" + lastBalance);
		if (lastBalance != null && lastBalance.compareTo(BigDecimal.ZERO) > 0) {
			lastAv = lastBalance;
		} else {
			lastAv = calcLastChargeDayAV(conInvestPO, conBusiProdPO.getValidateDate());
		}

		List<MmsCalcRiskSAVO> mmsCalcRiskSAVOList = new ArrayList<MmsCalcRiskSAVO>();
		Date validateDate = conBusiProdPO.getValidateDate();

		for (int i = 0; i < dateList.size(); i++) {
			Date curDate = dateList.get(i);// @invalid 保单实际经过某一天
			// @invalid 默认当天所在的保单月度
			policyMonth = (int) DateUtilsEx.getMonthAmount(conBusiProdPO.getValidateDate(), curDate);
			// @invalid 查询当天前的买入卖出交易记录
			List<FundTransPO> fundTransPOs = new ArrayList<FundTransPO>();
			for (FundTransPO po : fundTransPOList) {
				if (po.getDealTime().before(curDate) || po.getDealTime().equals(curDate)) {
					fundTransPOs.add(po);
				}
			}

			// @invalid 某一天当天累积增加的保险费（即不扣除初始费用的保费）
			BigDecimal prem = calcCurDayPrem(fundTransPOs, curDate, lastPrem);
			// @invalid 某一天当天账户价值
			BigDecimal av = calcCurDayAV(fundTransPOs, curDate, lastAv, validateDate);

			// @invalid 查询部分领取比例信息
			Map<String, Object> map = new HashMap<String, Object>();
			map = calcPartPayRate(conInvestPO.getPolicyId(), conInvestPO.getBusiItemId(), curDate); // @invalid
																									// 部分领取比例和部分领取的保费
			String partPayRate = map.get("partPayRate").toString();
			boolean isSaChangeFlag = querySachange(conInvestPO, conProductPO.getValidateDate(), curDate);
			if ("".equals(partPayRate)) {
				partPayRate = "0";
			}
			isPayFlag = getIsPayFlag(conBusiProdPO, curDate);

			// @invalid 发生部分领取 并且第一次部分领取保单月数不为0则计算第一次部分领取保单年度月数
			if (isPayFlag == 1) {
				fundTransPO = new FundTransPO();
				fundTransPO.setPolicyId(conInvestPO.getPolicyId());
				fundTransPO.setDealTime(curPriceDate);
				fundTransPO = ilpuChargeDeductionDao.findPartPayFistMonth(fundTransPO);
				if (fundTransPO != null && fundTransPO.getDealTime() != null) {
					// @invalid 第一次部分领取保单年度月数
					policyMonth = (int) DateUtilsEx.getMonthAmount(conBusiProdPO.getValidateDate(),
							fundTransPO.getDealTime());
				}
			}
			// @invalid 查询保额是否变更
			// @invalid 计算变更前的保额
			SaChangePO saChangePO = new SaChangePO();
			List<SaChangePO> saChangePOs = new ArrayList<SaChangePO>();
			if (dateList.size() > 0) {
				saChangePO.set("start_date_after", dateList.get(0));
				saChangePO.setPolicyId(conInvestPO.getPolicyId());
				saChangePO.setItemId(conInvestPO.getItemId());
				saChangePOs = saChangeDao.findAllSaChange(saChangePO);
			}
			BigDecimal sa = conProductPO.getAmount();

			// @invalid 调产品接口计算907 909 保额
			if (Constants.BUSIPROD_CODE_907.equals(conBusiProdPO.getBusiProdCode())
					|| Constants.BUSIPROD_CODE_909.equals(conBusiProdPO.getBusiProdCode())) {
				sa = findSA(conBusiProdPO, sa, policyMonth, isPayFlag, conProductPO.getTotalPremAf());
			}

			for (SaChangePO changePO : saChangePOs) {
				// @invalid 如果保额变更时间大于当前循环时间 并且 保额变更时间大于险种生效日期
				if (changePO.getStartDate().getTime() >= curDate.getTime()
						&& changePO.getStartDate().getTime() > conBusiProdPO.getValidateDate().getTime()) {
					sa = sa.subtract(changePO.getSa()); // @invalid 使用当前的保额减去本结算期内发生过的保额变更的变化量
				}
			}

			// @invalid 代表冲减后的已交保费
			BigDecimal paidPrem = conProductPO.getTotalPremAf();
			if (partPayRate != null) {
				paidPrem = paidPrem.subtract(new BigDecimal(map.get("pgPrem").toString())).setScale(2,
						BigDecimal.ROUND_HALF_UP);
			}
			// @invalid ============================================
			MmsCalcRiskSAVO mmsCalcRiskSAVO = new MmsCalcRiskSAVO();
			mmsCalcRiskSAVO.setBusinessPrdId(conBusiProdPO.getBusiPrdId());// @invalid 险种代码
			mmsCalcRiskSAVO.setPolicyMonth(policyMonth);// @invalid 第一次部分领取保单年度月数
			mmsCalcRiskSAVO.setPartPayRate(partPayRate);// @invalid 每次部分领取的比例串 [从投保开始到现在的所有部分领取比例]
			mmsCalcRiskSAVO.setBasicSA(basicSA);// @invalid 投保时的基本保额
			mmsCalcRiskSAVO.setIsPayFlag(isPayFlag);// @invalid 是否发生部分领取
			mmsCalcRiskSAVO.setPrem(paidPrem);// @invalid 当天累积增加的保险费 因计算风险保费需要 此处传值为累计所交保费(扣除初始费用)
			if (av.compareTo(new BigDecimal(0)) < 0) {
				mmsCalcRiskSAVO.setAV(new BigDecimal(0));// @invalid 当天账户价值(若为负值，则对应的账户价值按0处理)
			} else {
				mmsCalcRiskSAVO.setAV(av);// @invalid 当天账户价值
			}
			mmsCalcRiskSAVO.setPaidPrem(paidPrem);// @invalid 代表冲减后的已交保费
			mmsCalcRiskSAVO.setSA(sa);// @invalid 保额

			// @invalid 当日0时前已交保费 925产品使用 (本合同已交保险费指投保人依据本合同已经向本公司交纳的保险费，未扣除累计已部分领取金额)
			if (conProductPO.getTotalPremAf() != null) {
				mmsCalcRiskSAVO.setTotalPaidPremium(prem);
			}
			// @invalid 当日0时前累计已部分领取金额 925产品使用
			mmsCalcRiskSAVO.setTotalPartPayAmount(
					new BigDecimal(map.get("receivePrem").toString()).setScale(2, BigDecimal.ROUND_HALF_UP));

			mmsCalcRiskSAVOList.add(mmsCalcRiskSAVO);
		}

		// 4.计算特殊基本保额
		MmsCalcSpecialBasicSAReqVO mmsInputData = new MmsCalcSpecialBasicSAReqVO();
		mmsInputData.setMmsCalcRiskSAVOs(mmsCalcRiskSAVOList);

		logger.info("---------------------计算特殊基本保额-------------------------");
		logger.info("计算特殊基本保额--参数：" + XmlHelper.classToXml(mmsInputData));
		MmsCalcSpecialBasicSAResVO mmsOutPutData = prdIAS.prdimmscalcspecialbasicsaucccalcSpeBasicSA(mmsInputData);
		logger.info("计算特殊基本保额--结果：" + XmlHelper.classToXml(mmsOutPutData));
		if (conProductPO.get("busi_prod_code") != null && mmsOutPutData != null) {
			riskSA = mmsOutPutData.getRiskSA();
		}
		logger.info("----------------------------计算特殊基本保额为: " + riskSA + "-------------------------------");
		return riskSA;
	}

	/**
	 * 
	 * @description 计算两个日期之间的所有日期列表
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param startDate 开始时间
	 * @param endDate   结束时间
	 * @return
	 */
	private List<Date> addDateList(Date startDate, Date endDate) {
		List<Date> dateList = new ArrayList<Date>();
		int day = (int) DateUtilsEx.getDayAmount(startDate, endDate);
		for (int i = 1; i <= day; i++) {
			Date newDate = DateUtilsEx.addDay(startDate, i);
			if (!dateList.contains(newDate)) {
				dateList.add(newDate);
			}
		}
		return dateList;
	}

	/**
	 * 
	 * @description 计算现金价值
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param conInvestPO conInvestPO
	 * @param curDate     curDate
	 * @return
	 */
	private BigDecimal calcLastChargeDayPrem(ContractInvestPO conInvestPO, Date curDate) {
		FundTransPO fundTransPO = new FundTransPO();
		fundTransPO.setListId(conInvestPO.getListId());// @invalid 账户id
		fundTransPO.setDealTime(curDate);// @invalid 处理时间
		fundTransPO = ilpuChargeDeductionDao.calcTotalAddAmount(fundTransPO);
		BigDecimal result = new BigDecimal(0);
		if (fundTransPO != null && fundTransPO.get("sum_amount") != null) {
			result = (BigDecimal) fundTransPO.get("sum_amount");
		}
		return result;
	}

	/**
	 * 
	 * @description 计算卖出总额
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param conInvestPO 保单投资连结交易PO
	 * @param curDate     时间
	 * @return
	 */
	private BigDecimal calcLastChargeDayAV(ContractInvestPO conInvestPO, Date curDate) {
		BigDecimal result = new BigDecimal(0);
		FundTransPO addFundTransPO = new FundTransPO();
		addFundTransPO.setListId(conInvestPO.getListId());// @invalid 账户id
		addFundTransPO.setDealTime(curDate);// @invalid 处理时间
		List<FundTransPO> fundTransPOs = ilpuChargeDeductionDao.calcSumAmount(addFundTransPO);
		BigDecimal add = new BigDecimal(0);
		BigDecimal sub = new BigDecimal(0);

		for (FundTransPO fundTransPO : fundTransPOs) {
			if (fundTransPO != null && fundTransPO.get("sum_amount") != null) {
				// @invalid 买入
				if (fundTransPO.getTransType().equals(Constants.INVEST_TRANS_TYPE__IN)) {
					add = (BigDecimal) fundTransPO.get("sum_amount");
				}
			}
		}
		result = add.subtract(sub);
		return result;
	}

	/**
	 * 
	 * @description 保单投资连结交易查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param fundTransPOList 保单投资连结交易PO
	 * @param curDate         时间
	 * @param lastPrem        上次保费
	 * @return
	 */
	private BigDecimal calcCurDayPrem(List<FundTransPO> fundTransPOList, Date curDate, BigDecimal lastPrem) {
		for (FundTransPO fundTransPO : fundTransPOList) {
			// @invalid 当天进入的不算
			// @invalid if (!fundTransPO.getDealTime().equals(curDate)) {
			// @invalid 买入、初始扣费
			if (fundTransPO.getTransType().equals(Constants.INVEST_TRANS_TYPE__NOTING)
					|| fundTransPO.getTransType().equals(Constants.INVEST_TRANS_TYPE__IN)) {
				lastPrem = lastPrem.add(fundTransPO.getTransAmount());
			}
			// @invalid }
		}

		return lastPrem;
	}

	/**
	 * 
	 * @description 账户价值
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param fundTransPOList 保单投资连结交易PO
	 * @param curDate         时间
	 * @param lastAv          上次价值
	 * @param validateDate    生效日
	 * @return
	 */
	private BigDecimal calcCurDayAV(List<FundTransPO> fundTransPOList, Date curDate, BigDecimal lastAv,
			Date validateDate) {
		for (FundTransPO fundTransPO : fundTransPOList) {
			// @invalid 当天进入的要算，但是生效日当天不包含
			if (!fundTransPO.getDealTime().equals(validateDate)) {
				if (curDate.getTime() >= fundTransPO.getDealTime().getTime()) { // @invalid 按照老核心处理 发生交易 当日不计入账户价值
					// @invalid 买入
					if (fundTransPO.getTransType().equals(Constants.INVEST_TRANS_TYPE__IN)) {
						lastAv = lastAv.add(fundTransPO.getTransAmount());
					}
					// @invalid 卖出、无关（初始扣费）、万能扣费
					if (fundTransPO.getTransType().equals(Constants.INVEST_TRANS_TYPE__OUT)) {
						lastAv = lastAv.subtract(fundTransPO.getTransAmount());
					}
				}
			}
		}
		return lastAv;
	}

	/**
	 * 
	 * @description 部分领取比例[从投保开始到现在的所有部分领取比例]
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyId   保单ID
	 * @param busiItemId 险种ID
	 * @param curDate    时间
	 * @return
	 */
	private Map<String, Object> calcPartPayRate(BigDecimal policyId, BigDecimal busiItemId, Date curDate) {
		Map<String, Object> result = new HashMap<String, Object>();
		StringBuffer partPayRate = new StringBuffer("");
		FundTransPO fundTransPO = new FundTransPO();
		fundTransPO.setPolicyId(policyId);
		fundTransPO.setBusiItemId(busiItemId);
		String transCode = "22,57"; // @invalid 22 部分领取 57部分领取（交纳关联保单的续期或续保保费）
		fundTransPO.set("trans_code", transCode); // @invalid 交易代码
		fundTransPO.set("cur_date", curDate);
		List<FundTransPO> fundTransPOs = fundTransDao.findPayRates(fundTransPO);

		BigDecimal pgPrem = new BigDecimal(0); // @invalid 部分领取部分对应的保费
		BigDecimal payPrem = new BigDecimal(0); // @invalid 部分领取后实际缴纳的保费(所交保费-部分领取对应的保费)
		BigDecimal receivePrem = new BigDecimal(0); // @invalid 部分领取金额
		for (int i = 0; i < fundTransPOs.size(); i++) {
			FundTransPO feFundTransPO = fundTransPOs.get(i);
			BigDecimal rate = feFundTransPO.getTransProportion();
			if (rate == null) {
				rate = feFundTransPO.getTransAmount().divide(feFundTransPO.getBalanceUnitsBf(), 8,
						BigDecimal.ROUND_HALF_UP);
			}

			// @invalid 根据领取比例计算此次领取对应的保费
			// @invalid 查询本次部分领取前进入的保费
			FundTransPO fundTransPO1 = new FundTransPO();
			fundTransPO1.setPolicyId(policyId);
			fundTransPO1.setBusiItemId(busiItemId);
			String transCodes = "05,06,11,12,13,27,28,29,30,31,35,36,37"; // @invalid 所有保费的交易代码
			fundTransPO1.set("trans_code", transCodes); // @invalid 交易代码
			if (i != 0) {
				fundTransPO1.set("deal_time", fundTransPOs.get(i - 1).getDealTime());
			}
			fundTransPO1.set("deal_timea", feFundTransPO.getDealTime());
			fundTransPO1 = fundTransDao.sumAmountByTypeAndPolicyId(fundTransPO1);
			if (fundTransPO1 != null && fundTransPO1.getTransAmount() != null) {
				payPrem = payPrem.add(fundTransPO1.getTransAmount());
			}
			pgPrem = pgPrem.add(payPrem.multiply(rate)); // @invalid 部分领取对应的保费
			payPrem = payPrem.multiply(new BigDecimal(1).subtract(rate));

			partPayRate.append(rate);
			if (i != fundTransPOs.size() - 1) {
				partPayRate.append(",");
			}
			// @invalid 部分领取金额
			receivePrem = receivePrem.add(feFundTransPO.getTransAmount());
		}
		result.put("pgPrem", pgPrem.toString()); // @invalid 部分领取对应的保费(此处已累加)
		result.put("partPayRate", partPayRate.toString()); // @invalid 部分领取比例字符串
		result.put("receivePrem", receivePrem); // @invalid 部分领取金额
		return result;
	}

	/**
	 * @description 获取是否发生部分领取标识
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param conBusiProdPO 险种PO
	 * @param curDate       时间
	 * @return
	 */
	private int getIsPayFlag(ContractBusiProdPO conBusiProdPO, Date curDate) {
		// @invalid 此处需加上对万能险保额变更的判断
		int isPayFlag = 0;
		PolicyChangePO policyChangePO = new PolicyChangePO();
		policyChangePO.setPolicyId(conBusiProdPO.getPolicyId());
		List<String> serviceCodeList = new ArrayList<String>();
		serviceCodeList.add("PG"); // @invalid 万能险、投连险账户部分领取
		serviceCodeList.add("CB"); // @invalid 万能险基本保额减少
		serviceCodeList.add("CA"); // @invalid 万能险基本保额增加
		serviceCodeList.add("DC"); // @invalid 万能险基本保额约定变更
		policyChangePO.set("service_code_list", serviceCodeList);
		policyChangePO.set("validate_time_before", curDate);
		List<PolicyChangePO> policyChanges = policyChangeDao.findAllPolicyChange(policyChangePO);
		if (CollectionUtilEx.isNotEmpty(policyChanges)) {
			isPayFlag = 1;
		}
		return isPayFlag;
	}

	/**
	 * 
	 * @description 判断是否发生过保额变更
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param conInvestPO 投连PO
	 * @param valiDate    生效日
	 * @param curDate     时间
	 * @return
	 */
	private boolean querySachange(ContractInvestPO conInvestPO, Date valiDate, Date curDate) {
		SaChangePO saChangePO = new SaChangePO();
		saChangePO.setPolicyId(conInvestPO.getPolicyId());
		saChangePO.setItemId(conInvestPO.getItemId());
		saChangePO.set("after_date", valiDate); // @invalid 排除生效日当天产生的记录
		saChangePO.set("start_date_between", curDate);
		List<SaChangePO> saChangePOs = saChangeDao.findAllSaChange(saChangePO);
		if (CollectionUtilEx.isNotEmpty(saChangePOs)) {
			return true;
		}
		return false;
	}

	/**
	 * @description 调产品接口查询当前保额
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param conBusiProdPO 险种PO
	 * @param sa            保额
	 * @param policyMonth   月数
	 * @param isPayFlag     是否领取
	 * @param totalPrem     总保费
	 * @return
	 */
	private BigDecimal findSA(ContractBusiProdPO conBusiProdPO, BigDecimal sa, int policyMonth, int isPayFlag,
			BigDecimal totalPrem) {
		PrdCalcUniversalAmountReqVO inputData = new PrdCalcUniversalAmountReqVO();
		inputData.setWithdrawRate(new BigDecimal(0)); // @invalid 本次的部分领取比例
		inputData.setBusinessId(conBusiProdPO.getBusiPrdId()); // @invalid 产品id
		inputData.setIsPayFlag(isPayFlag); // @invalid 是否发生部分领取 0-未发生 1-发生
		inputData.setGrossPremiunAddFee(totalPrem); // @invalid 实际缴纳保费
		inputData.setPolicyMonth(new BigDecimal(policyMonth)); // @invalid 保单月度
		inputData.setSA(sa); // @invalid 当前保险金额

		logger.info("查询万能险保额接口入参为------\n" + XmlHelper.classToXml(inputData));
		PrdCalcUniversalAmountResVO outputData = prdIAS.prdiPrdCalcUniversalAmount(inputData);
		if (outputData != null && outputData.getBasicSA() != null) {
			sa = outputData.getBasicSA();
		}
		return sa;
	}

}
