package com.nci.tunan.qry.impl.peripheral.ucc.querynbpolicyinfo.impl;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.qry.impl.peripheral.service.querynbpolicyinfo.IQueryNBPolicyInfoService;
import com.nci.tunan.qry.impl.peripheral.ucc.querynbpolicyinfo.IQueryNBPolicyInfoUcc;
import com.nci.tunan.qry.interfaces.model.bo.QueryNBPolicyInfoReqBO;
import com.nci.tunan.qry.interfaces.peripheral.exports.querynbpolicyinfo.vo.InputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.querynbpolicyinfo.vo.OutputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.querynbpolicyinfo.vo.QueryNBPolicyInfoRspVO;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * @description 非实时承保保单查询Ucc接口实现类
 * <AUTHOR>
 * @date Mar 24, 2022 5:11:46 PM
 * @.belongToModule
 */
public class QueryNBPolicyInfoUccImpl implements IQueryNBPolicyInfoUcc{
	
	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();
	
	
	/**
	 * @Fields queryPolicyInfoService : 非实时承保保单查询-保单库Service接口
	 */
	@Autowired
	@Qualifier("qry_QueryNBPolicyInfoService")
	private IQueryNBPolicyInfoService queryNBPolicyInfoService;
	
	/**
	 * @description 非实时承保保单查询
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param inputData
	 * @return
	 */
	@Override
	public OutputData queryNBPolicyInfo(InputData inputData) {
		logger.debug("-----非实时承保保单查询-----入参为：" + inputData + "-----");
		OutputData outputData = new OutputData();
		outputData.setQueryNBPolicyInfoRspVO(BeanUtils.copyList(QueryNBPolicyInfoRspVO.class, queryNBPolicyInfoService.queryNBPolicyInfo(BeanUtils.copyProperties(QueryNBPolicyInfoReqBO.class,inputData.getQueryNBPolicyInfoReqVO())))); ;
		logger.debug("-----非实时承保保单查询-----查询结果为：" + outputData + "-----");
		return outputData;
	}

	public IQueryNBPolicyInfoService getQueryNBPolicyInfoService() {
		return queryNBPolicyInfoService;
	}

	public void setQueryNBPolicyInfoService(
			IQueryNBPolicyInfoService queryNBPolicyInfoService) {
		this.queryNBPolicyInfoService = queryNBPolicyInfoService;
	}
	
	

}
