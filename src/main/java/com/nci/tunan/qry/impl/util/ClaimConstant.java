package com.nci.tunan.qry.impl.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.nci.udmp.framework.util.Constants;

public final class ClaimConstant extends Constants {
    /**
     * 私有构造函数，不允许new
     */
    private ClaimConstant() {
    }
    /**
     * 赔案号
     */
    public static final String CASENO = "";

    /**
     * 个人池标识
     */
    public static final String PERSONALQUERYTASKTYPE = "2";
    /**
     * 共享池标识
     */
    public static final String SHAREQUERYTASKTYPE = "1";
    /**
     * 个人池标识
     */
    public static final String IS_GROUP_F = "F";
    /**
     * 共享池标识
     */
    public static final String IS_GROUP_T = "T";
    /**
     * 报案节点编码
     */
    public static final String REPORTSHARETASKCODE = "CLM010201";
    /**
     * 协谈开始节点编码
     */
    public static final String TREATYSTATATASKCODE = "CLM070000";

    /**
     * 分配协谈任务 - 节点编码
     */
    public static final String TREATYTASKCODE = "CLM070201";

    /**
     * 理赔协谈节点编码
     */
    public static final String CLMTREATYTASKCODE = "CLM070202";

    /**
     * 出具机构协谈结论 - 节点编码
     */
    public static final String CLMORGTREATYTASKCODE = "CLM070203";
    /**
     * 保全组号
     */
    public static final String LOCK_EXCEPT_GROUP1 = "[06701]";
    /**
     * 数字38
     */
    public static final int THRITY_EIGHT = 38; 
    /**
     * 续期组号
     */
    public static final String LOCK_EXCEPT_GROUP2 = "[06702]";
    /**
     * 影像操作类型常量
     */
    public static final String IMAGE_OPERATE_TYPE_CREATE = "21";// 创建
    public static final String IMAGE_OPERATE_TYPE_RETRIVE = "22";// 查询
    public static final String IMAGE_OPERATE_TYPE_UPDATE = "23";// 更新
    public static final String IMAGE_OPERATE_TYPE_DELETE = "24";// 删除

    /**
     * 事中质检节点编码
     */
    public static final String MIDDLECHECKTASKCODE = "CLM010215";

    /**
     * 报案状态
     */
    public static final String REPORTSTATUSE = "10";

    /**
     * 签收节点编码
     */
    public static final String SIGNSHARETASKCODE = "CLM010202";

    /**
     * 待签收状态
     */
    public static final String SIGNSTATUSE = "20";

    /**
     * 立案节点编码
     */
    public static final String REGISTERSHARETASKCODE = "CLM010203";
    /**
     * 自动立案节点编码（外包录入）
     */
    public static final String AUTOREGISTERSHARETASKCODE = "CLM010220";

    /**
     * 待立案状态
     */
    public static final String REGISTERSTATUSE = "30";

    /**
     * 审核节点编码
     */
    public static final String AUDITSHARETASKCODE = "CLM010207";
    /**
     * 二核节点编码
     */
    public static final String BINUCLEARTASKCODE = "UW020201";
    
    /**
     * 简易审核节点编码
     */
    public static final String EASYAUDITSHARETASKCODE = "CLM010206";

    /**
     * 审核状态
     */
    public static final String AUDITSTATUSE = "60";
    /**
     * 案件状态审核中
     */
    public static final String AUDITSTATUSEZ = "61";
    /**
     * 案件状态审批
     */
    public static final String APPROVESTATUSE = "70";
    /**
     * 案件状态审批中
     */
    public static final String APPROVESTATUSEZ = "71";

    /**
     * 预付中
     */
    public static final String ADVANCE_MID_STATUS = "51";

    /**
     * 待预付录入
     */
    public static final String ADVANCE_ENTER_STATUS = "52";

    /**
     * 待预付复核
     */
    public static final String ADVANCE_CHECK_STATUS = "53";

    /**
     * 预付复核中
     */
    public static final String ADVANCE_CHECKING_STATUS = "54";
    /**
     * 已回退
     */
    public static final String REDO_CHECKING_STATUS = "99";

    /**
     * 模板标识
     */
    public static final String PROCESSFLAG = "CLMMain";
    /**
     * 二核模板标识
     */
    public static final String UW_PROCESSFLAG = "UWMC";

    /**
     * 协谈模板标识
     */
    public static final String TREATYFLAG = "CLMC";

    /**
     * 合议模板标识
     */
    public static final String DISCUSSFLAG = "CLMPD";

    /**
     * 回退模板标识
     */
    public static final String ROLLBACKFLAG = "CLMRM";

    /**
     * 付费变更模板标识
     */
    public static final String PAYCHANGEFLAG = "CLMPC";

    /**
     * 补充问题件模板标识
     */
    public static final String SUPPLYCHECKLISTFLAG = "CLMRFP";
    
    /**
     * 用与批量操作的模板标识
     */
    public static final String BATCHFLAG = "CLMAF";

    /**
     * 审批结案处理节点编码
     */
    public static final String APPROVETASKCODE = "CLM010409";

    /**
     * 预付申请节点编码
     */
    public static final String PREPAREPAYAPPLYTASKCODE = "CLM010204";

    /**
     * 预付复核节点编码
     */
    public static final String PREPAREPAYCHECKTASKCODE = "CLM010205";

    /**
     * 预付审批生效节点编码
     */
    public static final String PREPAREPAYCHECKEFFICETASKCODE = "CLM010406";

    /**
     * 回退申请节点编码
     */
    public static final String ROLLBACKAPPLYTASKCODE = "CLM040201";

    /**
     * 回退复核节点编码
     */
    public static final String ROLLBACKCHECKTASKCODE = "CLM040202";

    /**
     * 付费变更申请节点编码
     */
    public static final String PAYCHANGEAPPLYTASKCODE = "CLM060201";

    /**
     * 付费变更复核节点编码
     */
    public static final String PAYCHANGECHECKTASKCODE = "CLM060202";

    /**
     * 分配合议任务 - 节点编码
     */
    public static final String ALLOTDISCUSSIONREPLYTASKCODE = "CLM030201";

    /**
     * 合议回复节点编码
     */
    public static final String DISCUSSIONREPLYTASKCODE = "CLM030202";

    /**
     * 出具合议结论 - 节点编码
     */
    public static final String ORGDISCUSSIONREPLYTASKCODE = "CLM030203";

    /**
     * 补充问题件 - 节点编码
     */
    public static final String SUPPLYCLAIMCHECKLISTTASKCODE = "CLM100201";

    /**
     * 审批处理节点编码
     */
    public static final String APPROVEDISPOSETASKCODE = "CLM010208";

    /**
     * 复核案件节点编码
     */
    public static final String AGAINCHECKTASKCODE = "CLM010212";

    /**
     * 案件抽检(事中质检)处理节点编码
     */
    public static final String MIDCTASKCODE = "CLM010215";

    /**
     * 案件质检(事后质检)处理节点编码
     */
    public static final String AFCTASKCODE = "CLM050201";

    /**
     * 结案状态
     */
    public static final String CLOSECASESTATUSE = "80";
    /**
     * 待审批状态
     */
    public static final String WAITCASESTATUSE = "70";
    /**
     * 待审核状态
     */
    public static final String CASE_STATUS_AUDIT = "60";

    /**
     * 业务类型
     */
    public static final String BUSINESSTYPE = "CLM";
    /**
     * 任务创建类型 0: 创建流程不提交,1：创建流程且自动提交到下一节点
     */
    public static final String CREATETASKCREATETYPE = "0";
    /**
     * 任务创建类型 1：创建流程且自动提交到下一节点
     */
    public static final String SUBMITTASKCREATETYPE = "1";
    /**
     * 理赔创建类型
     */
    public static final String CLAIMCREATETYPE = "0";
    /**
     * 立案共享池非空验证提示
     */
    public static final String SHAREPOLLNOTNULL = "请至少输入一项查询条件";

    /**
     * 立案共享池操作失败提示
     */
    public static final String SHAREPOLLFAIL = "查询失败";

    /**
     * 查询出险人操作失败提示
     */
    public static final String QUERYINSUREDFAIL = "查询出险人失败";

    /**
     * 查询出险人操作失败提示
     */
    public static final String QUERYINSUREDNOTEXIST = "您要查询的客户不存在，请重新输入查询条件";

    /**
     * 保单挂起抄单操作成功提示
     */
    public static final String CONTRACTHANGUPSUCCEED = "抄单成功";

    /**
     * 保单挂起抄单操作失败提示
     */
    public static final String CONTRACTHANGUPFAIL = "抄单失败";

    /**
     * 保单挂起抄单操作失败提示
     */
    public static final String CONTRACTHANGUPDATE = "该出险日期没有对应的保单,或抄单保存失败";

    /**
     * 定义复核机制删除复核规则操作成功提示
     */
    public static final String DATACHECKDELETESUCCEED = "复核规则删除成功";

    /**
     * 匹配理算成功提示
     */
    public static final String MATCHCALCSUCCEED = "匹配理算成功";

    /**
     * 匹配理算失败提示
     */
    public static final String MATCHCALCFAIL = "匹配理算失败";

    /**
     * 结论保存成功提示
     */
    public static final String CONCLUSIONSUCCEED = "结论保存成功";

    /**
     * 结论保存失败提示
     */
    public static final String CONCLUSIONFAIL = "结论保存失败";
    // 投连险卖出标识 0-按照申请日的上一个计价日计算
    public static final String SELL_FALG_0 = "0";
    // 投连险卖出标识 1-按照申请日的下一个计价日计算
    public static final String SELL_FALG_1 = "1";
    // 卖出类型 0-试算
    public static final String SELL_TYPE_CALCULATION = "0";
    // 卖出类型 1-卖出
    public static final String SELL_TYPE_SOLD = "1";
    // 卖出类型 3-撤销卖出
    public static final String SELL_TYPE_UNSOLD = "3";
    // 匹配理算类型 0-匹配预算
    public static final String MATCH_CALC_PRE = "0";
    // 匹配理算类型 1-匹配理算
    public static final String MATCH_CALC_CALCULATION = "1";
    // 累加器类型 1-扣款
    public static final BigDecimal ACCUMU_TYPE_DEDUCT = new BigDecimal(1);
    // 累加器类型 2-共担
    public static final BigDecimal ACCUMU_TYPE_COPAY = new BigDecimal(2);
    // 累加器类型 3-责任限额
    public static final BigDecimal ACCUMU_TYPE_DSL = new BigDecimal(3);
    // 累加器类型 4-双重账单住院天数
    public static final BigDecimal ACCUMU_TYPE_DOUBLE_HOSPITALIZATION = new BigDecimal(4);
    // 累加器类型 5-乘法累加器
    public static final BigDecimal ACCUMU_TYPE_MULTIPLIER = new BigDecimal(5);
    // 累加器类型 6-最小值累加器
    public static final BigDecimal ACCUMU_TYPE_MINVALUE = new BigDecimal(6);
    // 累加器类型 7-账单住院天数
    public static final BigDecimal ACCUMU_TYPE_HOSPITALIZATION = new BigDecimal(7);
    // 累加器类型 8-双重乘法累加器
    public static final BigDecimal ACCUMU_TYPE_DOUBLE_MULTIPLIER = new BigDecimal(8);
    // 累加器类型 9-双重最小值累加器
    public static final BigDecimal ACCUMU_TYPE_DOUBLE_MINVALUE = new BigDecimal(9);
    // 累加器类型 10-双重限额累加器
    public static final BigDecimal ACCUMU_TYPE_DOUBLELIMIT = new BigDecimal(10);
    // 累加器类型 10-汇总金额限制累加器
    public static final BigDecimal ACCUMU_TYPE_SUMMARY_LIMIT = new BigDecimal(11);
    // 累加器模式 0-无关
    public static final String CONDITION_TYPE_NA = "0";
    // 累加器模式 1-单个事故
    public static final String CONDITION_TYPE_PER_EVENT = "1";
    // 累加器模式 2-单个住院期间
    public static final String CONDITION_TYPE_PRE_HOSPITALIZATION = "2";
    // 累加器模式 3-单次诊断
    public static final String CONDITION_TYPE_PRE_DIAGNOSIS = "3";
    // 累加器模式 4-同一事故原因的事故
    public static final String CONDITION_TYPE_SAME_EVENT = "4";
    // 累加器模式 5-单个保单年度
    public static final String CONDITION_TYPE_PER_POLICY_YEAR = "5";
    // 抄单日期 当前标示 0 - 出险日期
    public static final BigDecimal FLAG_CLAIM_DATE = new BigDecimal(0);
    // 抄单日期 当前标示 1 - 当前日期
    public static final BigDecimal FLAG_CURRENT_DATE = new BigDecimal(1);
    // 相关性判断结果 0 - 否
    public static final String NO = "0";
    // 相关性判断结果 1 - 是
    public static final String YES = "1";
    // 治疗类型 0-门诊
    public static final String TREAT_TYPE_CLINIC = "0";
    // 治疗类型 1-住院
    public static final String TREAT_TYPE_HOSPITALIZATION = "1";
    // 相关性类型 1 - T_PRODUCT_RELATIVITY
    public static final BigDecimal RELATIVITY_TYPE_1 = new BigDecimal(1);
    // 相关性类型2 - T_PARAM
    public static final BigDecimal RELATIVITY_TYPE_2 = new BigDecimal(2);
    // 1- 社保
    public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_1 = new BigDecimal(1);
    // 2- 第三方支付
    public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_2 = new BigDecimal(2);
    // 3- 公费
    public static final BigDecimal CLAIM_BILL_PAID_OTHER_TYPE_3 = new BigDecimal(3);

    // 调用打印时加入
    // 读取配置问题件路径
    public static final String NB_UW_LOGINADMIN_PATH = "META-INF/conf/config.properties";
    // 登陆用户名
    public static final String NB_UW_LOGIN_USERNAME = "login.userName";
    // 登陆密码
    public static final String NB_UW_LOGIN_PASSWORD = "login.passWord";
    // 体检结果录入访问URL
    public static final String NB_UW_LOGIN_PHYSICALURL = "login.physicalUrl";
    // 核保结论通知书录入访问URL----2015-01-27
    public static final String NB_UW_LOGIN_UWDESITIONURL = "login.uwDesitionUrl";
    // 应用上下文名称
    public static final String NB_UW_LOGIN_WEBCONTEXT = "login.webcontext";
    // 体检结果录入访问的IP地址
    public static final String NB_UW_LOGIN_IP = "login.ip";
    // 核保结论通知书录入访问的IP地址
    public static final String NB_UW_LOGIN_UWDESITIONIP = "login.uwDesitionIp";
    // 端口号
    public static final String NB_UW_LOGIN_PORT = "login.port";
    // 用户名
    public static final String NB_BMP_userCode = "syn";
    // ProcessFlag
    public static final String NB_BMP_ProcessFlag = "NBEnter";
    public static final String NB_BMP_Question_ProcessFlag = "NBIM";
    // 投保单录入-->TaskCode
    public static final String NB_BMP_TaskCode = "NB030202";
    // 复核任务-->TaskCode
    public static final String NB_BMP_TaskCode2 = "NB030203";
    public static final String NB_BMP_Question_TaskCode = "NB120201";
    public static final String NB_BMP_Question_Review_TaskCode = "NB120202";
    // QueryTaskType,共享池
    public static final String NB_BMP_QueryTaskType_Pubpool = "1";
    // 个人池
    public static final String NB_BMP_QueryTaskType_Perpool = "2";
    // 通知书下载临时路径
    public static final String NB_NOTICE_URL = "notice.url";

    // 初审不通过通知书类型
    public static final String NB_AUDIT_NOT_TYPE = "audit.notice.type";
    // 初审回执通知书类型
    public static final String NB_AUDIT_RECEIPT_TYPE = "audit.receipt.type";
    // 业务来源编码
    public static final String BUSS_SOURCE_CODE = "buss.source.code";
    // 发送对象类型
    public static final String SEND_OBJECT_TYPE = "send.obj.type";
    // 通知书状态
    public static final String NOTICE_STATUS = "1";
    // 记事本访问路径
    public static final String NB_UW_LOGIN_NOTEBOOK = "login.uwNoteUrl";

    // 记事本访问ip
    public static final String NB_UW_LOGIN_UWNOTE = "login.uwnoteIp";
    // 退回重扫模板标识-->ProcessFlag
    public static final String NB_BMP_ProcessFlag_Reset = "NBIFS";
    // 退回重扫-->TaskCode
    public static final String NB_BMP_TaskCode_Reset = "NB040201";

    // 通知书打印ip地址
    public static final String NB_DOCUMENT_IP = "documentIp";
    // 通知书单证类型
    public static final String LTR_SENDTYPE = "ltr.sendType";
    public static final String PIP_SENDTYPE = "pip.sendType";
    // 打印类型
    public static final String PRINT_PRINTTYPE = "print.printType";
    public static final String PREVIEW_PRINTTYPE = "preview.printType";
    public static final String PREVIEWANDPRINT_PRINTTYPE = "previewAndPrint.printType";
    // 规则引擎ip地址
    public static final String NB_ILOG_IP = "ilogIp";
    // 签单模板标识-->ProcessFlag
    public static final String NB_BMP_SIGN_PROCESSFLAG = "NBSignin";
    // 待签单处理节点编码-->TaskCode
    public static final String NB_BMP_SIGN_WAITTING_TASKCODE = "NB050202";
    // 事后质检模板标识-->ProcessFlag
    public static final String NB_BMP_CHECK_PROCESSFLAG = "NBCheck";
    // 进行质检操作节点编码-->TaskCode
    public static final String NB_BMP_CHECK_ING_TASKCODE = "NB100201";
    // 纠错模板标识-->ProcessFlag
    public static final String NB_BMP_CORRECT_PROCESSFLAG = "NBCorrect";
    // 纠错申请节点编码-->TaskCode
    public static final String NB_BMP_CORRECT_APPLY_TASKCODE = "NB090201";
    // 纠错审核节点编码-->TaskCode
    public static final String NB_BMP_CORRECT_VERIFY_TASKCODE = "NB090202";
    // 保单重打模板标识-->ProcessF审核lag
    public static final String NB_BMP_REPRINT_PROCESSFLAG = "NBPR";
    // 重打审核节点编码-->TaskCode
    public static final String NB_BMP_REPRINT_VERIFY_TASKCODE = "NB070203";
    // 通知书管理模板标识-->ProcessFlag
    public static final String NB_BMP_DOCUMENT_PROCESSFLAG = "NM";
    // 通知书打印节点编码-->TaskCode
    public static final String NB_BMP_DOCUMENT_PRINT_TASKCODE = "NM010201";
    // 通知书录入节点编码-->TaskCode
    public static final String NB_BMP_DOCUMENT_ENTRY_TASKCODE = "NM010203";
    // 保单更新回执信息成功的标识
    public static final String NB_PAS_RECEIPT_SUCCESS = "0";
    // 任务提醒的方式-->清单类
    public static final String NB_TASK_DL = "1";
    // 任务提醒的方式-->任务池类
    public static final String NB_TASK_POOL = "2";
    // 理赔类型---医疗
    public static final String ClAIMTYPE_MEDICAL = "03";
    // 理赔类型---医疗
    public static final String ClAIMTYPE_ILLNESS = "08";
    // 案件状态预付未完成状态
    public static final String ClAIMSTATUS = "50";
    // 调用工作流成功标识
    public static final String BIZRESCD = "0";
    // 调用工作流失败标识
    public static final String BIZRESCDNO = "1";
    // 出险人性别常量
    public static final String SEX = "9L";
    // 案件状态为签收中
    public static final String CASESTATUS = "21";
    // 案件状态为立案中
    public static final String REGISTERCASESTATUS = "31";
    // 案件状态为待立案确认
    public static final String PENDING_REGISTER_CASES_TATUS = "32";
    // 案件状态为关闭
    public static final String CANCELSTATUS = "90";
    // 理赔关怀中受益人的年龄
    public static final String BENEAGE = "18";
    // 应收应付理赔业务来源
    public static final String DERIVTYPE = "5";

    /**
     * 数字0
     */
    public static final int ZERO = 0;

    /**
     * 数字1
     */
    public static final int ONE = 1;

    /**
     * 数字2
     */
    public static final int TWO = 2;

    /**
     * 数字3
     */
    public static final int THREE = 3;

    /**
     * 数字4
     */
    public static final int FORE = 4;
    
    /**
     * 数字4
     */
    public static final int FOUR = 4;
    /**
     * 数字-4
     */
    public static final int FOUR_M = -4;
    /**
     * 数字5
     */
    public static final int FIVE = 5;
    /**
     * 数字-5
     */
    public static final int FIVE_M = -5;

    /**
     * 数字6
     */
    public static final int SIX = 6;
    /**
     * 数字7
     */
    public static final int SEVEN = 7;
    /**
     * 数字8
     */
    public static final int EIGHT = 8;
    /**
     * 数字9
     */
    public static final int NINE = 9;
    /**
     * 数字10
     */
    public static final int TEN = 10;
    /**
     * 数字11
     */
    public static final int ELENEN = 11;
    /**
     * 数字12
     */
    public static final int TWELVE = 12;
    /**
     * 数字13
     */
    public static final int THIRTEEN = 13;
    /**
     * 数字14
     */
    public static final int FOURTEEN = 14;
    /**
     * 数字15
     */
    public static final int FIFTEEN = 15;
    /**
     * 数字16
     */
    public static final int SIXTEEN = 16;
    /**
     * 数字17
     */
    public static final int SEVENTEEN = 17;
    /**
     * 数字18
     */
    public static final int EIGHTEEN = 18;
    /**
     * 数字19
     */
    public static final int NINETEEN = 19;
    /**
     * 数字20
     */
    public static final int TWENTY = 20;
    /**
     * 数字21
     */
    public static final int TWENTY_ONE = 21;
    /**
     * 数字22
     */
    public static final int TWENTY_TWO = 22;
    
    /**
     * 数字23
     */
    public static final int TWENTY_THREE = 23;
    /**
     * 数字24
     */
    public static final int TWENTY_FOUR = 24;
    /**
     * 数字29
     */
    public static final int TWENTY_NINE = 29;
    /**
     * 数字30
     */
    public static final int THIRTY = 30;
    /**
     * 数字36
     */
    public static final int THIRTY_SIX= 36;
    /**
     *  数字40
     */
    public static final int FOURTY = 40;
    /**
     *  数字50
     */
    public static final int FIFTY = 50;
    /**
     *  数字100
     */
    public static final int ONE_HUNDRED = 100;
    /**
     *  数字-100
     */
    public static final int ONE_HUNDRED_M = -100;
    /**
     *  数字348
     */
    public static final int THREE_HUNDRED_FORTY_EIGHT = 348; 
    /**
     *  数字500
     */
    public static final int FIVE_HUNDRED = 500;
    /**
     *  数字1000
     */
    public static final int ONE_THOUSAND = 1000;
    /**
     *  数字1024
     */
    public static final int ONE_THOUSAND_TWENTY_FOUR = 1024;
    /**
     *  数字1864
     */
    public static final int ONE_THOUSAND_SIXTY_FOUR = 1864;
    /**
     *  数字1900
     */
    public static final int ONE_THOUSAND_NINE_HUNDRED = 1900;
    /**
     *  数字2050
     */
    public static final int TWO_THOUSAND_FIFTY = 2050;
    /**
     *  数字5000
     */
    public static final int FIVE_THOUSAND = 5000;
    /**
     *  数字9999
     */
    public static final int NINE_THOUSAND_NINE_HUNDRED_NINTY_NINE = 9999;
    
    /**
     *  外包录入FTP端口号
     */
    public static final int FTP_PORT = 21;

    /**
     * 关系代码，"00"代表本人
     */
    public static final String RELATION = "00";
    
    /**
     * 关系代码，"24"代表子女
     */
    public static final String RELATION_TWENTY_FOUR = "24";
    
   

    /**
     * 北京市编码
     */
    public static final String BEIJING = "110000";

    /**
     * 东城区编码
     */
    public static final String DONGCHENG = "110100";
    /**
     * 结算类型代码，"8"代表退还保费
     */
    public static final String ADJUST_TYPE = "8";

    /**
     * 两个BigDecimal的数据比较大小，小于的返回值为-1
     */
    public static final int COMPARE_RESULT = -1;

    // 反馈质检结果模板
    public static final String CHECKAFTERFLAG = "CLMCheck";
    // 反馈质检结果节点号
    public static final String CHECKAFTERCODE = "CLM050201";
    /**
     * 出具审核结论 常量 add xuyz_wb
     */
    /**
     * @Fields Medical_YES : 是否医疗赔付明细：Y
     */
    public static final String Medical_YES = "Y";
    /**
     * @Fields Medical_NO : 是否医疗赔付明细：N
     */
    public static final String Medical_NO = "N";
    /**
     * @Fields NUM_THREE : 数字：3
     */
    public static final int NUM_THREE = 3;
    /**
     * @Fields NUM_FOUR : 数字：4
     */
    public static final int NUM_FOUR = 4;
    /**
     * @Fields EXCEPT_GROUP_1 : 理赔可维护挂起保全项设置的组id : 1
     */
    public static final String EXCEPT_GROUP_1 = "1";
    /**
     * @Fields NUM_FIVE : 数字：5
     */
    public static final int NUM_FIVE = 5;
    /**
     * @Fields NUM_SIX : 数字：6
     */
    public static final int NUM_SIX = 6;
    /**
     * @Fields NUM_EIGHT : 数字：8
     */
    public static final int NUM_EIGHT = 8;
    /**
     * @Fields NUM_NINE : 数字：9
     */
    public static final int NUM_NINE = 9;
    /**
     * @Fields NUM_TEN : 数字：10
     */
    public static final int NUM_TEN = 10;
    /**
     * @Fields NUM_SEVENTEEN : 数字：17
     */
    public static final int NUM_SEVENTEEN = 17;

    /**
     * @Fields NUM_SEVENTEEN : 数字：100
     */
    public static final int NUM_HUNDRED = 100;
    
    /**
     * @Fields NUM_SEVENTEEN : 数字：99999
     */
    public static final int NINE_MAX = 99999;
    /**
     * @Fields NUM_SEVENTEEN : 数字：10000
     */
    public static final int TEN_THOUSAND = 10000;

    /**
     * @Fields COMMA_CHINA : 中文逗号："，"
     */
    public static final String COMMA_CHINA = "，";
    /**
     * @Fields COMMA : 逗号：","
     */
    public static final String COMMA = ",";
    /**
     * @Fields SEMICOLON : 分号：";"
     */
    public static final String SEMICOLON = ";";
    /**
     * @Fields SEMICOLON_CHAR : 分号：";"
     */
    public static final  char SEMICOLON_CHAR= ';';
    /**
     * @Fields SEMICOLON_CHAR : 句号："。"
     */
    public static final  char SEMICOLON_CHARJ= '。';
    
    /**
     * 责任层赔付结论和赔案层赔付结论比较
     */
    public static final String LIABCONCLUSION_FLAG_ZERO = "0";
    public static final String LIABCONCLUSION_FLAG_ONE = "1";
    public static final String LIABCONCLUSION_FLAG_TWO = "2";
    public static final String LIABCONCLUSION_FLAG_THREE = "3";
    public static final String LIABCONCLUSION_FLAG_FIVE = "5";
    public static final String CONDITION_TYPE_NAME = "审批权限";
    /**
     * 退保终止03、解约终止05 理赔终止02 满期终止01
     */
    public static final String END_CAUSE_ONE = "01";
    public static final String END_CAUSE_THREE = "03";
    public static final String END_CAUSE_FIVE = "05";
    public static final String END_CAUSE_TWO = "02";
    /**
     * 打印类型
     */
    public static final String PRINT_TYPE_ = "Print";
    public static final String PREVIEW_TYPE = "Preview";
    public static final String PREVIEWANDPRINT_TYPE = "PreviewAndPrint";
    /**
     * @Fields MODUBLE_TYPE : 模块类型："05"
     */
    public static final String MODUBLE_TYPE = "05";
    // 支付状态失败
    public static final String PAYMENT_STATUS_FAIL = "00";
    // 支付状态成功
    public static final String PAYMENT_STATUS_SUCCEED = "01";
    /**
     * 分次给付模板标识
     */
    public static final String PROCESSFLA = "CLMGO";
    // 分次给付任务创建类型
    public static final String TASKCREATETYPE = "1";

    /**
     * 单证打印查询无记录提示信息
     */
    public static final String DOCUMENT_PRINT_CLAIM = "您要查询的赔案不存在，请重新输入查询条件!";

    /**
     * 1秒 = 1000毫秒数
     */
    public static final int MILLISECONDS = 1000;

    /**
     * 1分钟 = 60秒
     */
    public static final int MINUTE = 60;

    /**
     * 1小时 = 60分钟
     */
    public static final int HOUR = 60;

    /**
     * 任务类型，审核类型
     */
    public static final String AUDIT_TASK_TYPE = "3";
    /**
     * 任务类型，总时效
     */
    public static final String SUM_TIME_TASK_TYPE = "17";
    /**
     * 任务类型，审批类型
     */
    public static final String APPROVED_TASK_TYPE = "4";

    /**
     * 任务类型，事中质检类型
     */
    public static final String MIDC_TASK_TYPE = "8";

    /**
     * 任务类型，事后质检类型
     */
    public static final String AFC_TASK_TYPE = "9";
    /**
     * 事后质检完成标识
     */
    public static final String AFC_CON_PASS = "2";

    /**
     * 分页标识,不进行分页
     */
    public static final String ISPAGEFLAG_NO = "0";

    /**
     * 分页标识，进行分页
     */
    public static final String ISPAGEFLAG_YES = "1";

    /**
     * 空字符串用于判断
     */
    public static final String NULL_STR = "";

    /**
     * 4：查询全部个人池待办
     */
    public static final String QUERY_ALL_PERSONAL_TASK_TYPE = "4";

    /**
     * 任务查询：3-全部查询
     */
    public static final String QUERY_ALL_TASK_TYPE = "3";

    /**
     * -1用于进行判断
     */
    public static final int ADJUST = -1;

    /**
     * 用于分割字符串
     */
    public static final String SPLIT_STR = ":";

    /**
     * 机构级别第一级
     */
    public static final String ORGAN_GRADE_ONE = "01";
    /**
     * 机构级别第二级
     */
    public static final String ORGAN_GRADE_TWO = "02";
    /**
     * 机构级别第三级
     */
    public static final String ORGAN_GRADE_THREE = "03";
    /**
     * 机构级别第四级
     */
    public static final String ORGAN_GRADE_FORE = "04";
    /**
     * 二级机构长度
     */
    public static final int ORGAN_TWO_LENTGH = 4;
    /**
     * 三级机构长度
     */
    public static final int ORGAN_THREE_LENTGH = 6;

    /**
     * 四级机构长度
     */
    public static final int ORGAN_FOUR_LENTGH = 8;


    /**
     * 单证打印查询赔案失败
     */
    public static final String DOCUMENT_PRINT_ERROR = "查询赔案失败!";

    public static final String POLICY_CLAUSE_BYCASEID = "该赔案无关联保单!";

    public static final String CLAIM_PRINT_IP = "**********";
    // 案件标识正常
    public static final String CASE_FLAG_YES = "0";
    // 案件标识非正常
    public static final String CASE_FLAG_NO = "1";
    //
    public static final String CLAIM_REPORT_QUERY_ERROR = "查询报案信息失败!";
    public static final String CLAIM_REGIS_QUERY_ERROR = "查询立案信息失败!";
    public static final String CLAIM_SIGN_QUERY_ERROR = "查询签批信息失败!";

    /**
     * 事中质检参数类型----理赔类型
     */
    public static final String CHECK_PARA_CLAIM_TYPE = "01";
    /**
     * 事中质检参数类型----险种
     */
    public static final String CHECK_PARA_PRODUCT = "02";
    /**
     * 事中质检参数类型----操作机构
     */
    public static final String CHECK_PARA_ORGANIZATION = "03";
    /**
     * 事中质检参数类型----片区
     */
    public static final String CHECK_PARA_AREA = "11";
    /**
     * 事中质检参数类型----操作人员
     */
    public static final String CHECK_PARA_OPERATOR = "04";
    /**
     * 事中质检参数类型----案件标识
     */
    public static final String CHECK_PARA_CASE_FLAG = "08";
    /**
     * 签收确认创建工作流标识
     */
    public static final String SIGN_CONFIRM_FLAG = "1";
    /**
     * 工作流成功标识
     */
    public static final String WORKFLOW_SUCCESS = "0";
    /**
     * 工作流失败标识
     */
    public static final String WORKFLOW_FAIL = "1";

    /**
     * 领取频率 1:年领
     */
    public static final String PAY_TYPE_ONE = "1";
    /**
     * 领取频率 2:半年领
     */
    public static final String PAY_TYPE_TWO = "2";
    /**
     * 领取频率 3:季领
     */
    public static final String PAY_TYPE_THREE = "3";
    /**
     * 领取频率 4:月领
     */
    public static final String PAY_TYPE_FORE = "4";
    /**
     * 领取频率 5:趸领
     */
    public static final String PAY_TYPE_FIVE = "5";
    
    /**
     * 生调频率：月
     */
    public static final String FREQ_MONTH = "01";
    /**
     * 生调频率：季度
     */
    public static final String FREQ_QUARTER = "02";
    /**
     * 生调频率：半年
     */
    public static final String FREQ_HARF_YEAR = "03";
    /**
     * 生调频率：年
     */
    public static final String FREQ_YEAR = "04";
    
    /**
     * 权限名称 ：审核权限
     */
    public static final String PERMISSIONNAME_AUDIT = "审核权限";
    /**
     * 权限名称 ：审批权限
     */
    public static final String PERMISSIONNAME_APPROVED = "审批权限(普通)";
    /**
     * 权限名称 ：审批权限(疑难)
     */
    public static final String PERMISSIONNAME_DIFFICULT_APPROVED = "审批权限(疑难)";
    /**
     * 权限名称 ：事中质检权限
     */
    public static final String PERMISSIONNAME_MIDC = "事中质检";
    /**
     * 权限名称 ：事后质检权限
     */
    public static final String PERMISSIONNAME_AFC = "事后质检";
    /**
     * 权限名称 ：签收登记
     */
    public static final String PERMISSIONNAME_SIGN = "签收登记";
    /**
     * 权限名称 ：立案登记
     */
    public static final String PERMISSIONNAME_REGISTER = "立案登记";
    /**
     * 权限名称 ：立案登记复核
     */
    public static final String PERMISSIONNAME_REGISTER_CHECK = "立案登记复核";
    /**
     * 权限名称 ：预付申请
     */
    public static final String PERMISSIONNAME_ADVANCE_APPLY = "预付申请";
    /**
     * 权限名称 ：预付复核
     */
    public static final String PERMISSIONNAME_ADVANCE_AUDIT = "预付复核";
    /**
     * 权限名称 ：回退申请
     */
    public static final String PERMISSIONNAME_ROLLBACK_APPLY = "回退申请";
    /**
     * 权限名称 ：付费变更申请
     */
    public static final String PERMISSIONNAME_PAYCHANGE_APPLY = "付费变更申请";
    /**
     * 权限名称 ：付费变更复核
     */
    public static final String PERMISSIONNAME_PAYCHANGE_AUDIT = "付费变更复核";
    /**
     * 权限名称 ：补充单证问题件
     */
    public static final String PERMISSIONNAME_CHECKLIST_PROBLEM = "补充单证问题件";
    /**
     * 权限名称 ：协谈
     */
    public static final String PERMISSIONNAME_TREATY_TALK = "协谈权限";
    /**
     * 权限名称 ：合议
     */
    public static final String PERMISSIONNAME_DISCUSS = "合议权限";
    /**
     * 权限名称 ：调查
     */
    public static final String PERMISSIONNAME_SURVEY = "调查";
    /**
     * 权限名称 ：二核 
     */
    public static final String PERMISSIONNAME_UW = "二核 ";
    /**
     * 权限名称 ：回退审核
     */
    public static final String PERMISSIONNAME_ROLLBACK_AUDIT = "回退审核";
    /**
     * 权限名称 ：回退审核
     */
    public static final String PERMISSIONNAME_REGISTER_AUDIT = "立案登记复核";
      
    /**
     * 业务来源 005：其他
     */
    public static final String BIZ_OURCE = "005";

    /**
     * 事中
     */
    public static final String THINGS = "01";

    public static final String CARE_SERVER = "您要查询的服务人员不存在，请重新输入查询条件！";

    /**
     * 补充单证模板标识
     */
    public static final String START_SUPPLY_CREATE_TEMPLATE = "CLMRFP";

    /**
     * 补充单证节点编码
     */
    public static final String START_SUPPLY_CREATE_NUMB = "CLM100201"; // 创建的节点

    public static final String START_SUPPLY_CREATE_SUBMIT = "CLM109999"; //

    /**
     * 理赔回退管理流程-审核节点编码
     */
    public static final String ROLLBACK_AUDIT_TASK_CODE = "CLM040203";

    /**
     * 分配标识 自动分配 （1 个人申请 2人工分配 3 自动分配）
     */
    public static final String ASSIGNFLAG_AUTO = "3";

    /**
     * 分配标识 手工分配 （1 个人申请 2人工分配 3 自动分配）
     */
    public static final String ASSIGNFLAG_HAND = "2";

    /**
     * 分配标识 个人申请 （1 个人申请 2人工分配 3 自动分配）
     */
    public static final String ASSIGNFLAG_PERSONAL = "1";

    /**
     * 发起二核调工作流模板标志
     */
    public static final String START_UNDWER_TWICE_TEMPLATE = "CLMSU";

    /**
     * 结论是否生效工作流节点
     */
    public static final String START_UNDWER_TWICE_CODECON = "CLM110202";

    /**
     * 加费工作流节点
     */
    public static final String START_UNDWER_TWICE_CODEFEE = "CLM110203";

    /**
     * 核销工作流节点
     */
    public static final String START_UNDWER_TWICE_CODECANCLE = "CLM110204";

    /**
     * 分次给付审核节点编码
     */
    public static final String GRADATION_PAY_AUDIT = "CLM090201";

    /**
     * 超期补偿标识，1为超期
     */
    public static final BigDecimal EXCEEDDUEY = new BigDecimal(1);

    /**
     * 超期补偿标识，0为未超期
     */
    public static final BigDecimal EXCEEDDUEN = new BigDecimal(0);

    /**
     * 审批结论，1为通过
     */
    public static final BigDecimal APPROVECONCLUSIONY = new BigDecimal(1);

    /**
     * 审批结论，2为未通过
     */
    public static final BigDecimal APPROVECONCLUSIONN = new BigDecimal(2);

    // 受检机构的CODE值
    public static final String ROLECODEONE = "1";
    public static final String ROLECODETWO = "2";
    public static final String ROLECODETHREE = "3";
    public static final String ROLECODEFOUR = "4";
    public static final String ROLECODEFIVE = "5";
    public static final String ROLECODESIX = "6";
    public static final String ROLECODESEVEN = "7";
    
    /**
     * 清单导出最大数
     */
    public static final int NINE_TEN_NINE_WAN=999999;
    /**
     * 事中质检参数类型----出险原因
     */
    public static final String CHECK_PARA_REASON = "09";

    /**
     * 参数定义表名称
     */
    public static final String DEFINDEMNITYDUE = "indemnityDue";

    /**
     * 参数定义表名称 撤销保单挂起批作业天数设定
     */
    public static final String CUSTOMTIME = "customTime";
    /**
     * 临时设定时间
     */
    public static final String TEMPTIME = "tempTime";

    /**
     * 发送方式 - 短信
     */
    public static final BigDecimal SMSSENDWAY = new BigDecimal(1);

    /**
     * 发送方式 - 邮件
     */
    public static final BigDecimal MAILSENDWAY = new BigDecimal(2);

    /**
     * 一年的天数
     */
    public static final BigDecimal YEARDAYCOUNT = new BigDecimal(365);

    /**
     * 审核结论 - 全部给付
     */
    public static final BigDecimal CONCLUSIONALLPAY = new BigDecimal(1);

    /**
     * 审核结论 - 部分给付
     */
    public static final BigDecimal CONCLUSIONPARTPAY = new BigDecimal(2);

    /**
     * 审核结论 - 3为拒付
     */
    public static final BigDecimal CONCLUSIONREJECT = new BigDecimal(3);
    
    /**
     * 审核结论 - 4公司撤案
     */
    public static final BigDecimal CONCLUSIONCOMPANYREVOKE = new BigDecimal(4);
    
    /**
     * 审核结论 - 5客户撤案
     */
    public static final BigDecimal CONCLUSIONCUSTOMERREVOKE = new BigDecimal(5);

    /**
     * 审核结论 - 6 审核不通过
     */
    public static final BigDecimal CONCLUSIONNOTPASS = new BigDecimal(6);

    /**
     * 是否为常规给付 - 1为否
     */
    public static final BigDecimal ISCOMMONN = new BigDecimal(1);

    /**
     * 结算类型 - 现金价值
     */
    public static final String CASHVALUE = "1";

    /**
     * 结算类型 - 账户价值
     */
    public static final String ACCOUNTVALUE = "2";

    /**
     * 结算类型 - 自垫本金
     */
    public static final String PADPRINCIPAL = "3";

    /**
     * 结算类型 - 自垫利息
     */
    public static final String PADINTEREST = "4";

    /**
     * 结算类型 - 贷款本金
     */
    public static final String LOANPRINCIPAL = "5";

    /**
     * 结算类型 - 贷款利息
     */
    public static final String LOANINTEREST = "6";

    /**
     * 结算类型 - 扣除保费
     */
    public static final String DEDUCTPREMIUM = "7";

    /**
     * 结算类型 - 返还保费
     */
    public static final String RETURNPREMIUM = "8";

    /**
     * 结算类型 - 终了红利
     */
    public static final String FINALBONUS = "9";

    /**
     * 结算类型 - 利差返还
     */
    public static final String PRICERETURN = "10";

    /**
     * 结算类型 - 风险保费
     */
    public static final String RISKPREMIUM = "11";

    /**
     * 结算类型 - 保单管理费
     */
    public static final String CONTRACTMANAGECOST = "12";

    /**
     * 结算类型 - 应领未领的保证领取年金
     */
    public static final String NOTGETANNUITY = "13";

    /**
     * 结算类型 - 出险日后发放的年金/生存金
     */
    public static final String ACCIDENTBACKANNUITY = "14";

    /**
     * 结算类型 - 未追回满期金
     */
    public static final String NOTRECOVEREXPIRE = "15";
    /**
     * 结算类型 - 险种终止退当期保费
     */
    public static final String ADJUST_TYPE_SIXTEEN = "16";
    /**
     * 结算类型 - 险种终止退未满期保费
     */
    public static final String ADJUST_TYPE_SEVETEEN = "17";
    /**
     * 结算类型 - 险种终止退全部保费
     */
    public static final String ADJUST_TYPE_EIGHTEEN = "18";
    /**
     * 结算类型 - 现金红利
     */
    public static final String ADJUST_TYPE_NINETEEN = "19";
    /**
     * 结算类型 - 现金红利利息
     */
    public static final String ADJUST_TYPE_TWENTY = "20";
    /**
     * 结算类型 - 扣除累计声息账户
     */
    public static final String ADJUST_TYPE_TWENTYONE = "21";
    /**
     * 结算类型 - 终了结算利息
     */
    public static final String ADJUST_TYPE_TWENTYTWO = "22";
    /**
     * 结算类型 - 基本保额现价
     */
    public static final String ADJUST_TYPE_TWENTYFIVE = "25";
    /**
     * 结算类型 - 累计红利保额现价
     */
    public static final String ADJUST_TYPE_TWENTYSIX = "26";
    /**
     * 结算类型 - 资产管理费
     */
    public static final String ADJUST_TYPE_TWENTYTHREE = "23";
    /**
     * 结算类型 - 扣除红利累积生息
     */
    public static final String ADJUST_TYPE_TWENTYEIGHT = "28";
    /**
     * 结算类型 - 扣除账户部分领取
     */
    public static final String ADJUST_TYPE_TWENTYNINE = "29";
    
    /**
     * 结算为偿还(付费)的数量
     */
    public static final int ADJUST_PAY_NUM= 11;
    /**
     * 关联到T_YES_NO表,0为否
     */
    public static final BigDecimal BIGDECIMALNO = new BigDecimal(0);

    /**
     * 关联到T_YES_NO表,1为是
     */
    public static final BigDecimal BIGDECIMALYES = new BigDecimal(1);
    /**
     * 用于截取字符串的数
     */
    public static final int SIXINT = 6;
    /**
     * 案件状态为待复核
     */
    public static final String CASESTATUSISREVIEW = "40";
    /**
     * 案件状态为复核中
     */
    public static final String CASESTATUSISREVIEWZ = "41";
    /**
     * 复核节点编码
     */
    public static final String CASESTATUSISREVIEWJ = "CLM010212";

    /**
     * 是否已回复
     */
    public static final BigDecimal YETREPLY = new BigDecimal(1);
    public static final BigDecimal NOTREPLY = new BigDecimal(0);
    /**
     * 生调标识
     */
    public static final String ONESURVEYFLAG = "1";
    /**
     * 调查类型标识 t_survey_type 预付类
     */
    public static final String ONESURVEYTYPE = "2";
    /**
     * 调查类型标识 t_survey_type 紧急类
     */
    public static final String ONESURVEYTYPE_THREE = "3";
    /**
     * 调查类型标识   biz_Type 常规类
     */
    public static final BigDecimal ONESURVEYTYPE_ONE = BigDecimal.valueOf(1);
    /**
     * 调查类型标识 复勘调查  biz_Type
     */
    public static final BigDecimal ONESURVEYTYPE_RE = BigDecimal.valueOf(4);
    
    /**
     * 调查类型标识  前置调查  biz_Type
     */
    public static final BigDecimal ONESURVEYTYPE_PRO = BigDecimal.valueOf(5);
    /**
     * 调查原因
     */
    public static final String ONESURVEYREASON = "99";
    /**
     * 调查提起阶段标识 t_survey_section  批处理
     */
    public static final String ONESURVEYSECTION = "4";
    
    /**
     * 调查提起阶段标识 t_survey_section 前置调查
     */
    public static final BigDecimal ONESURVEYSECTION_PRO = BigDecimal.valueOf(5);
    
    /**
     * 调查提起阶段标识 t_survey_section 复勘
     */
    public static final BigDecimal ONESURVEYSECTION_SIX = BigDecimal.valueOf(6);
    
    /**
     * 调查提起阶段标识 t_survey_section 报案
     */
    public static final BigDecimal ONESURVEYSECTION_ONE = BigDecimal.valueOf(1);
    
    /**
    * 调查提起阶段标识 移动理赔 01-报案
    */
    public static final String ONESURVEYSECTION_ZONE = "01";
    /**
     * 调查提起阶段标识 移动理赔 02-审核
     */
    public static final String ONESURVEYSECTION_ZTWO = "02";
    /**
     * 调查提起阶段标识 t_survey_section 审核
     */
    public static final BigDecimal ONESURVEYSECTION_TWO = BigDecimal.valueOf(2);
    
    /**
     * 调查提起阶段标识 t_survey_section 预付
     */
    public static final BigDecimal ONESURVEYSECTION_THREE = BigDecimal.valueOf(3);
    
    /**
     * 回退状态 - 回退申请中
     */
    public static final BigDecimal BACKSTATUSAPPLY = new BigDecimal(0);
    /**
     * 回退状态 - 回退审核中
     */
    public static final BigDecimal BACKSTATUSAUDIT = new BigDecimal(1);
    /**
     * 回退状态 - 回退完成
     */
    public static final BigDecimal BACKSTATUSFINISH = new BigDecimal(2);
    /**
     * 回退状态 - 回退撤销
     */
    public static final BigDecimal BACKSTATUSREVOKE = new BigDecimal(3);
    /**
     * 业务锁名称 AC
     */
    public static final String LOCKAC = "AC";
    /**
     * 账单类型 - 门诊
     */
    public static final BigDecimal BILL_TYPE_OUTPATIENT = new BigDecimal(1);
    public static final BigDecimal BILL_TYPE_HIGH = new BigDecimal(2);
    public static final BigDecimal BILL_TYPE_CANCER = new BigDecimal(3);
    /**
     * 公共参数类型 -预付比例
     */
    public static final String ADVANCE_PARA_TYPE = "1";

    /**
     * 非取消预付的标识
     */
    public static final String NON_CANCEL_ADVANCE_FLAG = "0";

    /**
     * 取消预付的标识
     */
    public static final String CANCEL_ADVANCE_FLAG = "1";

    /**
     * 预付审批结论为通过
     */
    public static final String ADVANCE_AUDIT_DECISION = new String("1");

    /**
     * 传值到工作流，预付审批结论通过为1
     */
    public static final String BPM_ADVANCE_AUDIT_PASS = "1";

    /**
     * 传值到工作流，预付审批结论不通过为0
     */
    public static final String NON_BPM_ADVANCE_AUDIT_PASS = "0";

    /**
     * 支付类型为返盘制
     */
    public static final String PAYMODE_BACK_SYSTEM = "32";

    /**
     * 接口调用成功
     */
    public static final String INVOKING_SUCCEED = "01";

    /**
     * 理赔编码
     */
    public static final String CLM = "067";
    /**
     * 保全编码
     */
    public static final String CUS = "068";
    /**
     * 新契约编码
     */
    public static final String NB = "064";
    /**
     * 保单锁：保全
     */
    public static final String POLICYLOCK_BQ = "OA";
    /**
     * 理赔锁(目前理赔挂起使用的service_code)
     */
    public static final String POLICYLOCK_CLM = "OA";

    /**
     * 保单锁：续期
     */
    public static final String POLICYLOCK_XQ = "OB";

    /**
     * 保单锁：解锁流程代码
     */
    public static final String POLICYLOCK_CLEAR = "2";
    /**
     * 理赔锁：理赔锁lock_service_id挂起解挂
     */
    public static final String POLICYLOCK_CLM_LOCKSERVICEID = "93";
    /**
     * 保全锁：理赔锁lock_service_id挂起解挂
     */
    public static final BigDecimal POLICYLOCK_CLM_PERSEVATIONLOCK = BigDecimal.valueOf(94);
    /**
     * 续期锁：理赔锁lock_service_id挂起解挂
     */
    public static final BigDecimal POLICYLOCK_CLM_RENEWALLOCK = BigDecimal.valueOf(95);
    /**
     * 黑名单挂起：lock_service_id挂起解挂
     */
    public static final String POLICYLOCK_CLM_BLACKLOCK = "100";

    /**
     * 保单锁：加锁流程代码
     */
    public static final String POLICYLOCK_SET = "1";

    /**
     * 通知书号
     */
    public static final String ADVICE_NOTE_NUMBER = "DOCUMENTNO";

    /**
     * 通知书类型 - 分割单
     */
    public static final String CARVE_UP_LIST = "分割单";

    /**
     * 分割单 - 编码
     */
    public static final String CARVE_UP_LIST_CODE = "CLM_00001";

    /**
     * 通知书类型 - 理赔回退收费通知书
     */
    public static final String BACK_AMOUNT_LIST = "理赔回退收费通知书";

    /**
     * 理赔回退收费通知书 - 编码
     */
    public static final String BACK_AMOUNT_LIST_CODE = "CLM_00005";

    /**
     * 通知书类型 - 拒付通知书
     */
    public static final String PROTEST_ADVICE_NOTE = "理赔决定通知书（拒付）";

    /**
     * 拒付通知书 - 编码
     */
    public static final String PROTEST_ADVICE_NOTE_CODE = "CLM_00002";

    /**
     * 通知书类型 - 赔付依据与说明
     */
    public static final String PAY_GOES_AND_EXPLAIN = "赔付依据与说明";

    /**
     * 赔付依据与说明 - 编码
     */
    public static final String PAY_GOES_AND_EXPLAIN_CODE = "CLM_00010";
    /**
     * 通知书类型 - 索赔申请书
     */
    public static final String INDEMNITY_APPLICATION = "索赔申请书";

    /**
     * 索赔申请书 - 编码
     */
    public static final String INDEMNITY_APPLICATION_CODE = "CLM_00008";

    /**
     * 理赔单证通知书 - 编码
     */
    public static final String DOCUMENT_NOTE = "理赔单证通知书";

    /**
     * 理赔单证通知书 - 编码
     */
    public static final String DOCUMENT_NOTE_CODE = "CLM_00004";

    /**
     * 收费付费标识 - 收费
     */
    public static final String COLLECT_FEES = "1";

    /**
     * 收费付费标识 - 付费
     */
    public static final String PAY = "2";

    /**
     * 应收应付的收付状态 - 已收付
     */
    public static final String FEE_STATUS = "01";
    
    /**
     * 应收应付的收付状态 - 支付失败
     */
    public static final String FEE_STATUS_FAIL = "03";
    /**
     * 备注问题件类型
     */
    public static final String REMARK_COUNT_ONE_TYPE = "1";
    /**
     * 备注选项
     */
    public static final String REMARK_COUNT_ONE_OPTION = "101";

    /**
     * 任务分配状态 ：全部
     */
    public static final String ASSIGN_TASK_ALL = "1";

    /**
     * 任务分配状态 ：已分配
     */
    public static final String ASSIGN_TASK_ALREADY = "3";

    /**
     * 任务分配状态 ：未分配
     */
    public static final String ASSIGN_TASK_NON = "2";

    /**
     * 理赔类型 ：豁免
     */
    public static final String CLAIMTYPE_ELEVEN = "11";

    /**
     * 业务员类型：星级
     */
    public static final BigDecimal STAR_AGENT_TYPE = new BigDecimal("1");
    /**
     * 受托人类型：业务员
     */
    public static final BigDecimal TRUSTEE_TYPE_YWY = new BigDecimal("0");
    /**
     * 受托人类型：客户
     */
    public static final BigDecimal TRUSTEE_TYPE_CUSTOMER = new BigDecimal("1");
    /**
     * 受托人类型：其它
     */
    public static final BigDecimal TRUSTEE_TYPE_OTHER = new BigDecimal("2");
    
    /**
     * 受托人类型：不在受托人类型之内
     */
    public static final BigDecimal TRUSTEE_TYPE_THREE = new BigDecimal("3");
    
    /**
     * 续保决定：不续保
     */
    public static final BigDecimal DECISION_DESC_NO = new BigDecimal("1");
    /**
     * 续保决定：可以续保
     */
    public static final BigDecimal DECISION_DESC_AGREE = new BigDecimal("2");
    /**
     * 续保决定：已经续保
     */
    public static final BigDecimal DECISION_DESC_ALREADY = new BigDecimal("3");
    /**
     * 续保决定：豁免标识-YES
     */
    public static final BigDecimal WAIVE_YES = new BigDecimal("1");
    /**
     * 续保决定：豁免标识-NO
     */
    public static final BigDecimal WAIVE_NO = new BigDecimal("0");
    /**
     * 附加险
     */
    public static final String SHORT_RISK = "10002";
    /**
     * 意外伤害险
     */
    public static final String PRODUCT__CATEGORY2_UNEXPECTED = "30002";
    /**
     * 意外险
     */
    public static final String PRODUCT__CATEGORY3_UNEXPECTED = "40012";
    /**
     * 全部给付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_ALLPAY = new BigDecimal("1");
    /**
     * 部分给付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_PARTPAY = new BigDecimal("2");
    /**
     * 协议给付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_AGREEMENTPAY = new BigDecimal("3");
    /**
     * 通融给付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_PARDONPAY = new BigDecimal("4");
    /**
     * 拒付
     */
    public static final BigDecimal CLAIM_LIAB_DECISION_REFUSEPAY = new BigDecimal("5");
    /**
     * 生效
     */
    public static final BigDecimal LIABILITY_STATUS_EFFECTIVE = new BigDecimal("1");
    /**
     * 中止
     */
    public static final BigDecimal LIABILITY_STATUS_SUSPEND = new BigDecimal("2");
    /**
     * 终止
     */
    public static final BigDecimal LIABILITY_STATUS_STOP = new BigDecimal("3");
    /**
     * 失效
     */
    public static final BigDecimal LIABILITY_STATUS_LOSE_EFFECTIVENESS = new BigDecimal("4");
    /**
     * 其他
     */
    public static final BigDecimal LIABILITY_STATUS_LOSE_RESTS = new BigDecimal("9");
    /**
     * 未生效
     */
    public static final BigDecimal LIABILITY_STATUS_LOSE_INVALID = new BigDecimal("0");
    /**
     * 立案通过
     */
    public static final BigDecimal ACCEPT_DECISION_PASS = new BigDecimal("1");
    /**
     * 不予立案
     */
    public static final BigDecimal ACCEPT_DECISION_NOT = new BigDecimal("2");
    /**
     * 延迟立案
     */
    public static final BigDecimal ACCEPT_DECISION_DELAY = new BigDecimal("3");

    /**
     * 保项赔付结论 5：拒付
     */
    public static final BigDecimal LIAB_CONSION_FIVE = new BigDecimal(5);
    /**
     * 理赔后保单状态 -继续有效
     */
    public static final String CLM_AFTER_STATE_ONE = "1";
    /**
     * 理赔后保单状态 -责任终止
     */
    public static final String CLM_AFTER_STATE_TWO = "2";
    /**
     * 理赔后保单状态 -责任组终止
     */
    public static final String CLM_AFTER_STATE_THREE = "3";
    /**
     * 理赔后保单状态 -险种终止
     */
    public static final String CLM_AFTER_STATE_FOUR = "4";
    /**
     * 理赔后保单状态 -本险种及其附加险种终止
     */
    public static final String CLM_AFTER_STATE_FIVE = "5";
    /**
     * 理赔后保单状态 -同一被保险人所有责任终止
     */
    public static final String CLM_AFTER_STATE_SIX = "6";
    /**
     * 理赔后保单状态 -保单终止
     */
    public static final String CLM_AFTER_STATE_SEVEN = "7";

    /**
     * 费用支付状态-待处理
     */
    public static final String FEE_STATUS_00 = "00";
    /**
     * 费用支付状态-锁定客户账号
     */
    public static final String FEE_STATUS_17 = "17";
    /**
     * 费用支付状态-已确认
     */
    public static final String FEE_STATUS_01 = "01";

    /**
     * 费用支付状态-取消
     */
    public static final String FEE_STATUS_02 = "02";
    /**
     * 费用支付状态-转账失败
     */
    public static final String FEE_STATUS_03 = "03";
    /**
     * 费用支付状态-转账途中
     */
    public static final String FEE_STATUS_04 = "04";
    /**
     * 费用支付状态-追缴
     */
    public static final String FEE_STATUS_05 = "05";
    /**
     * 费用支付状态-支票未打印
     */
    public static final String FEE_STATUS_06 = "06";
    /**
     * 费用支付状态-账号待复核
     */
    public static final String FEE_STATUS_07 = "07";
    /**
     * 费用支付状态-账号复核退回
     */
    public static final String FEE_STATUS_08 = "08";
    /**
     * 费用支付状态-再保账单已出
     */
    public static final String FEE_STATUS_09 = "09";
    /**
     * 费用支付状态-转账暂停
     */
    public static final String FEE_STATUS_10 = "10";
    /**
     * 费用支付状态-记账后取消
     */
    public static final String FEE_STATUS_11 = "11";
    /**
     * 费用支付状态-临时费用状态
     */
    public static final String FEE_STATUS_12 = "12";
    /**
     * 费用支付状态-用于审批的临时费用状态
     */
    public static final String FEE_STATUS_13 = "13";
    /**
     * 费用支付状态-中止
     */
    public static final String FEE_STATUS_14 = "14";
    /**
     * 费用支付状态-票据待确认
     */
    public static final String FEE_STATUS_15 = "15";
    /**
     * 费用支付状态-不可收付
     */
    public static final String FEE_STATUS_16 = "16";
    /**
     * 费用支付状态-核销
     */
    public static final String FEE_STATUS_19 = "19";
    /**
     * 费用支付类型-应收
     */
    public static final String ARAP_FLAG_AR = "1";
    /**
     * 费用支付类型-应付
     */
    public static final String ARAP_FLAG_AP = "2";
    /**
     * 回退申请复核结论，1为通过
     */
    public static final BigDecimal BACK_AUDIT_DECISION_YES = new BigDecimal(1);

    /**
     * 回退申请复核结论，2为未通过
     */
    public static final BigDecimal BACK_AUDIT_DECISION_NO = new BigDecimal(2);
    /**
     * 收付费接口，业务响应码-成功
     */
    public static final String ARAP_RESULTCODE_01 = "01";
    /**
     * 费用支付方式-现金
     */
    public static final String PAY_MODE_10 = "10";
    /**
     * 费用支付方式-现金送款薄
     */
    public static final String PAY_MODE_11 = "11";
    /**
     * 费用支付方式-社保缴纳
     */
    public static final String PAY_MODE_18 = "18";
    /**
     * 费用支付方式-支票
     */
    public static final String PAY_MODE_20 = "20";
    /**
     * 费用支付方式-现金支票
     */
    public static final String PAY_MODE_21 = "21";
    /**
     * 费用支付方式-转账支票
     */
    public static final String PAY_MODE_22 = "22";
    /**
     * 费用支付方式-银行转账
     */
    public static final String PAY_MODE_30 = "30";
    /**
     * 费用支付方式-银行转账（非制返盘）
     */
    public static final String PAY_MODE_31 = "31";
    /**
     * 费用支付方式-银行转账（制返盘）
     */
    public static final String PAY_MODE_32 = "32";
    /**
     * 费用支付方式-网银转账
     */
    public static final String PAY_MODE_33 = "33";
    /**
     * 费用支付方式-网上银行
     */
    public static final String PAY_MODE_34 = "34"; 
    /**
     * 费用支付方式-实时收费
     */
    public static final String PAY_MODE_40 = "40";
    /**
     * 费用支付方式-POS收款
     */
    public static final String PAY_MODE_41 = "41";
    /**
     * 费用支付方式-内部转账
     */
    public static final String PAY_MODE_50 = "50";
    /**
     * 费用支付方式-普通内部转账
     */
    public static final String PAY_MODE_51 = "51";
    /**
     * 费用支付方式-预存内部转账
     */
    public static final String PAY_MODE_52 = "52";
    /**
     * 费用支付方式-银保通
     */
    public static final String PAY_MODE_60 = "60";
    /**
     * 费用支付方式-第三方支付
     */
    public static final String PAY_MODE_70 = "70";
    /**
     * 费用支付方式-客户暂存
     */
    public static final String PAY_MODE_80 = "80";
    /**
     * 费用支付方式-客户账户
     */
    public static final String PAY_MODE_42 = "42";
    /**
     * 费用支付方式-其它
     */
    public static final String PAY_MODE_90 = "90";
    /**
     * 费用支付方式-内部扣回
     */
    public static final String PAY_MODE_99 = "99";
    /**
     * 保单挂起解挂场景-理赔报案
     */
    public static final String POLICYLOCK_CLM_REPORT = "4001";
    public static final String POLICYLOCK_REPORT_SERVICECODE = "LPBA";
    /**
     * 保单挂起解挂场景-理赔签收
     */
    public static final String POLICYLOCK_CLM_SIGN = "4002";
    public static final String POLICYLOCK_SIGN_SERVICECODE = "LPQS";
    /**
     * 保单挂起解挂场景-理赔立案
     */
    public static final String POLICYLOCK_CLM_REGISTER = "4003";
    public static final String POLICYLOCK_REGISTER_SERVICECODE = "LPLA";
    /**
     * 保单挂起解挂场景-理赔审核
     */
    public static final String POLICYLOCK_CLM_AUDIT = "4004";
    public static final String POLICYLOCK_AUDIT_SERVICECODE = "LPSH";
    /**
     * 保单挂起解挂场景-理赔审批
     */
    public static final String POLICYLOCK_CLM_APPROVE = "4005";
    public static final String POLICYLOCK_APPROVE_SERVICECODE = "LPSP";
    /**
     * 宽限期止期 天数 60
     */
    public static final int PayEnd_Days = 60;

    /**
     * 医疗账单类型-医疗/门诊
     */
    public static final BigDecimal CLM_BILL_ONE = new BigDecimal("1");
    /**
     * 医疗账单类型-高端医疗
     */
    public static final BigDecimal CLM_BILL_TWO = new BigDecimal("2");
    /**
     * 医疗账单类型-防癌医疗
     */
    public static final BigDecimal CLM_BILL_THREE = new BigDecimal("3");
    /**
     * 保单终止结论:解除合同退还保费
     */
    public static final BigDecimal POLICY_DEAL_CONCLUSION_ONE = new BigDecimal("1");
    /**
     * 保单终止结论:解除合同退还现金价值
     */
    public static final BigDecimal POLICY_DEAL_CONCLUSION_TWO = new BigDecimal("2");
    /**
     * 保单终止结论:解除合同退还账户价值
     */
    public static final BigDecimal POLICY_DEAL_CONCLUSION_THREE = new BigDecimal("3");
    /**
     * 保单终止结论:解除合同不退费
     */
    public static final BigDecimal POLICY_DEAL_CONCLUSION_FOUR = new BigDecimal("4");
    /**
     * 保单终止结论:合同终止
     */
    public static final BigDecimal POLICY_DEAL_CONCLUSION_FIVE = new BigDecimal("5");
    /**
     * 险种终止结论:险种终止
     */
    public static final BigDecimal PROD_DEAL_CONCLUSION_ONE = new BigDecimal("1");
    /**
     * 险种终止结论:险种终止退现金价值
     */
    public static final BigDecimal PROD_DEAL_CONCLUSION_TWO = new BigDecimal("2");
    /**
     * 险种终止结论:险种终止退未满期保费
     */
    public static final BigDecimal PROD_DEAL_CONCLUSION_THREE = new BigDecimal("3");
    /**
     * 险种终止结论:险种终止退当期保费
     */
    public static final BigDecimal PROD_DEAL_CONCLUSION_FOUR = new BigDecimal("4");
    /**
     * 险种终止结论:险种终止退账户价值
     */
    public static final BigDecimal PROD_DEAL_CONCLUSION_FIVE = new BigDecimal("5");
    /**
     * 险种终止结论:险种终止退保费
     */
    public static final BigDecimal PROD_DEAL_CONCLUSION_SIX = new BigDecimal("6");
    /**
     * 保障年期类型：Y;定期(年)
     */
    public static final String COVERAGE_PERIOD_Y = "Y";
    /**
     * 保障年期类型：A;定期(岁)
     */
    public static final String COVERAGE_PERIOD_A = "A";
    /**
     * 保障年期类型： W;终身
     */
    public static final String COVERAGE_PERIOD_W = "W";
    /**
     * 保障年期类型：M;定期(月 )
     */
    public static final String COVERAGE_PERIOD_M = "M";
    /**
     * 保障年期类型：D;定期(天)
     */
    public static final String COVERAGE_PERIOD_D = "D";
    /**
     * 理赔类型：身故
     */
    public static final String CLAIM_TYPE_ONG = "01";
    /**
     * 理赔类型：伤残
     */
    public static final String CLAIM_TYPE_TWO = "02";
    /**
     * 理赔类型：重大疾病
     */
    public static final String CLAIM_TYPE_THREE = "03";
    /**
     * 理赔类型：高残
     */
    public static final String CLAIM_TYPE_FOUR = "04";
    /**
     * 理赔类型：一般失能
     */
    public static final String CLAIM_TYPE_SIX = "06";
    /**
     * 理赔类型：重度失能
     */
    public static final String CLAIM_TYPE_SEVEN = "07";
    /**
     * 理赔类型：医疗
     */
    public static final String CLAIM_TYPE_EIGHT = "08";
   
    /**
     * 理赔类型：特种疾病
     */
    public static final String CLAIM_TYPE_TEN = "10";
    /**
     * 理赔类型：豁免
     */
    public static final String CLAIM_TYPE_ELEVEN = "11";

    // 医疗险
    public static final String CLM_TYPE_YI = "40007";

    // 被保人豁免责任list
    public static final List<String> WAIVE_INSURED_List = new ArrayList<String>();
    // 投保人豁免责任list
    public static final List<String> WAIVE_HOLDER_List = new ArrayList<String>();
    static {
        // 为被保人 豁免责任赋值
        WAIVE_INSURED_List.add("9011");
        WAIVE_INSURED_List.add("9008");
        WAIVE_INSURED_List.add("9002");
        WAIVE_INSURED_List.add("9005");
        WAIVE_INSURED_List.add("9004");
        WAIVE_INSURED_List.add("9010");
        WAIVE_INSURED_List.add("9009");

        // 为投保人 豁免责任赋值
        WAIVE_HOLDER_List.add("9007");
        WAIVE_HOLDER_List.add("9006");
        WAIVE_HOLDER_List.add("9003");
        WAIVE_HOLDER_List.add("9001");
    }
    /**
     * 是否生存 ：是；1
     */
    public static final String LIFE_STATE_YES = "1";
    /**
     * 是否生存 ：否；0
     */
    public static final String LIFE_STATE_NO = "0";
    /**
     * 是否超时：是；1
     */
    public static final String OVER_TIME_YES = "1";
    /**
     * 是否超时：否；0
     */
    public static final String OVER_TIME_NO = "0";

    /**
     * 受理渠道 03：柜面受理
     */
    public static final String CHANNEL_CODE_PANEL = "03";
    /**
     * 受理渠道 07：移动受理
     */
    public static final String CHANNEL_CODE_MOVE = "07";
    /**
     * 受理渠道 09：官微
     */
    public static final String CHANNEL_CODE_NINE = "09";
    /**
     * 客户权益部 最高审批权限
     * 
     * @Fields AP_SEVENTEEN : int
     */
    public static final int AP_CUSTOMER_POWER = 29;
    /**
     * 缴费频率-趸缴
     */
    public static final BigDecimal PREM_FREQ_ONE = new BigDecimal(1);
    /**
     * 缴费频率-月缴
     */
    public static final BigDecimal PREM_FREQ_TWO = new BigDecimal(2);
    /**
     * 缴费频率 -季缴
     */
    public static final BigDecimal PREM_FREQ_THREE = new BigDecimal(3);
    /**
     * 缴费频率-半年缴
     */
    public static final BigDecimal PREM_FREQ_FOUR = new BigDecimal(4);
    /**
     * 缴费频率-年缴
     */
    public static final BigDecimal PREM_FREQ_FIVE = new BigDecimal(5);
    /**
     * 缴费频率-不定期缴
     */
    public static final BigDecimal PREM_FREQ_SIX = new BigDecimal(6);
    /**
     * 缴费频率-其他
     */
    public static final BigDecimal PREM_FREQ_SEVEN = new BigDecimal(7);
    /**
     * 缴费年期类型-无关
     */
    public static final BigDecimal CHARGE_PERIOD_ZERO = new BigDecimal(0);
    /**
     * 缴费年期类型-趸缴
     */
    public static final BigDecimal CHARGE_PERIOD_ONE = new BigDecimal(1);
    /**
     * 缴费年期类型-年缴
     */
    public static final BigDecimal CHARGE_PERIOD_TWO = new BigDecimal(2);
    /**
     * 缴费年期类型-交至某确定年龄
     */
    public static final BigDecimal CHARGE_PERIOD_THREE = new BigDecimal(3);
    /**
     * 缴费年期类型-终身交费
     */
    public static final BigDecimal CHARGE_PERIOD_FOUR = new BigDecimal(4);
    /**
     * 缴费年期类型-不定期交
     */
    public static final BigDecimal CHARGE_PERIOD_FIVE = new BigDecimal(5);
    /**
     * 缴费年期类型-按月限交
     */
    public static final BigDecimal CHARGE_PERIOD_SIX = new BigDecimal(6);
    /**
     * 缴费年期类型-按天限交
     */
    public static final BigDecimal CHARGE_PERIOD_SEVEN = new BigDecimal(7);
    /**
     * 单证扫描状态 - 未核销
     */
    public static final BigDecimal CHECKLIST_STATUS_ONE = new BigDecimal(1);
    /**
     * 单证扫描状态 - 已核销
     */
    public static final BigDecimal CHECKLIST_STATUS_TWO = new BigDecimal(2);
    /**
     * 回退审核结论 - 通过
     */
    public static final BigDecimal BACK_AUDIT_DECISION_ONE = new BigDecimal(1);
    /**
     * 回退审核结论 - 未通过
     */
    public static final BigDecimal BACK_AUDIT_DECISION_TWO = new BigDecimal(2);
    /**
     * 保障期间类型： 一年期以上
     */
    public static final BigDecimal COVER_PERIOD_TYPE_ZERO = new BigDecimal(0);
    /**
     * 险种设计类型 - 普通型
     */
    public static final String PRODUCT_CATEGORY1_20001 = "20001";
    /**
     * 险种设计类型 - 分红型
     */
    public static final String PRODUCT_CATEGORY1_20002 = "20002";
    /**
     * 险种设计类型 - 万能型
     */
    public static final String PRODUCT_CATEGORY1_20003 = "20003";
    /**
     * 险种设计类型 - 投资连结型
     */
    public static final String PRODUCT_CATEGORY1_20004 = "20004";
    /**
     * 责任类型：身故
     */
    public static final String LIAB_CATEGORY_ONE = "01";
    /**
     * 责任类型：伤残
     */
    public static final String LIAB_CATEGORY_TWO = "02";
    /**
     * 责任类型：重疾
     */
    public static final String LIAB_CATEGORY_THREE = "03";
    /**
     * 责任类型：高残
     */
    public static final String LIAB_CATEGORY_FOUR = "04";
    /**
     * 责任类型：一般失能
     */
    public static final String LIAB_CATEGORY_FIVE = "05";
    /**
     * 责任类型：重度失能
     */
    public static final String LIAB_CATEGORY_SIX = "06";
    /**
     * 责任类型：医疗
     */
    public static final String LIAB_CATEGORY_SEVEN = "07";
    /**
     * 责任类型：特种疾病
     */
    public static final String LIAB_CATEGORY_EIGHT = "08";
    /**
     * 责任类型：豁免
     */
    public static final String LIAB_CATEGORY_NINE = "09";
    /**
     * 责任类型：生存金或年金
     */
    public static final String LIAB_CATEGORY_ELEVEN = "11";
    /**
     * 责任类型：满期金
     */
    public static final String LIAB_CATEGORY_TWELVE = "12";
    /**
     * 出现原因 ： 疾病
     */
    public static final BigDecimal ACC_REASON_ONE = new BigDecimal("1");
    /**
     * 出险原因: 意外
     */
    public static final BigDecimal ACC_REASON_TWO = new BigDecimal("2");
    /**
     * 计算投连万能扣费金额 -费用类型 -退保费用
     */
    public static final int CHARGE_TYPE = 4;
    /**
     * 累加器类型:扣款
     */
    public static final BigDecimal ACCUMU_TYPE_ONE = new BigDecimal("1");
    /**
     * 累加器类型:共担
     */
    public static final BigDecimal ACCUMU_TYPE_TWO = new BigDecimal("2");
    /**
     * 累加器类型:责任限额
     */
    public static final BigDecimal ACCUMU_TYPE_THREE = new BigDecimal("3");
    /**
     * 累加器类型:双重账单住院天数
     */
    public static final BigDecimal ACCUMU_TYPE_FOUR = new BigDecimal("4");
    /**
     * 累加器类型:乘法累加器
     */
    public static final BigDecimal ACCUMU_TYPE_FIVE = new BigDecimal("5");
    /**
     * 累加器类型:最小值累加器
     */
    public static final BigDecimal ACCUMU_TYPE_SIX = new BigDecimal("6");
    /**
     * 累加器类型:账单住院天数
     */
    public static final BigDecimal ACCUMU_TYPE_SEVEN = new BigDecimal("7");
    /**
     * 累加器类型:双重乘法累加器
     */
    public static final BigDecimal ACCUMU_TYPE_EIGHT = new BigDecimal("8");
    /**
     * 累加器类型:双重最小值累加器
     */
    public static final BigDecimal ACCUMU_TYPE_NINE = new BigDecimal("9");
    /**
     * 累加器类型:双重责任限额
     */
    public static final BigDecimal ACCUMU_TYPE_TEN = new BigDecimal("10");
    /**
     * 累加器类型:汇总金额限制累加器
     */
    public static final BigDecimal ACCUMU_TYPE_ELEVEN = new BigDecimal("11");
    /**
     * 累加器类型:汇总天数限制累加器
     */
    public static final BigDecimal ACCUMU_TYPE_TWELVE = new BigDecimal("12");
    /**
     * 累加器类型:限次累加器
     */
    public static final BigDecimal ACCUMU_TYPE_THIRTEEN = new BigDecimal("13");
    /**
     * 累加器类型:双重限次累加器
     */
    public static final BigDecimal ACCUMU_TYPE_FOURTEE = new BigDecimal("14");
    /**
     * 累加器类型:免赔额累加器
     */
    public static final BigDecimal ACCUMU_TYPE_FIFTEEN = new BigDecimal("15");
    /**
     * 调整原因:扣除自垫本金
     */
    public static final String ADJUST_REASON_ONE = "01";
    /**
     * 调整原因:扣除自垫利息
     */
    public static final String ADJUST_REASON_TWO = "02";
    /**
     * 调整原因:扣除保单质押贷款本金
     */
    public static final String ADJUST_REASON_THREE = "03";
    /**
     * 调整原因:扣除保单质押贷款利息
     */
    public static final String ADJUST_REASON_FOUR = "04";
    /**
     * 调整原因:扣除理赔事故发生后超额给付年金
     */
    public static final String ADJUST_REASON_FIVE = "05";
    /**
     * 调整原因:延迟报案导致查勘费扣除
     */
    public static final String ADJUST_REASON_SIX = "06";
    /**
     * 调整原因:投保前疾病
     */
    public static final String ADJUST_REASON_SEVEN = "07";
    /**
     * 调整原因:其他
     */
    public static final String ADJUST_REASON_NUM = "99";
    /**
     * claimCase FLAG //禁用
     */
    public static final String CLAIM_CASE_FLAG_ONE = "1";
    /**
     * claimCase FLAG //不禁用
     */
    public static final String CLAIM_CASE_FLAG_ZERO = "0";
    /**
     * 业务来源 :
     */
    public static final String DOCUMENTVON_BUSS_SOURCE_CODE_FOUR = "4";
    /**
     * 通知书状态 ：待发放
     */
    public static final String DOCUMENTVON_STATUS_ZERO = "0";
    /**
     * BigDecimal(0)
     */
    public static final BigDecimal BIGDECIMAL_ZERO = new BigDecimal(0);
    /**
     * BigDecimal(1)
     */
    public static final BigDecimal BIGDECIMAL_ONE = new BigDecimal(1);
    /**
     * BigDecimal(2)
     */
    public static final BigDecimal BIGDECIMAL_TWO = new BigDecimal(2);
    /**
     * BigDecimal(3)
     */
    public static final BigDecimal BIGDECIMAL_THREE = new BigDecimal(3);
    /**
     * BigDecimal(4)
     */
    public static final BigDecimal BIGDECIMAL_FOUR = new BigDecimal(4);
    /**
     * BigDecimal(5)
     */
    public static final BigDecimal BIGDECIMAL_FIVE = new BigDecimal(5);
    /**
     * BigDecimal(6)
     */
    public static final BigDecimal BIGDECIMAL_SIX = new BigDecimal(6);
    /**
     * BigDecimal(1000)
     */
    public static final BigDecimal BIGDECIMAL_ONE_THOUSAND = new BigDecimal(1000);
    /**
     * BigDecimal(2000)
     */
    public static final BigDecimal BIGDECIMAL_TWO_THOUSAND = new BigDecimal(2000);
    /**
     * BigDecimal(3000)
     */
    public static final BigDecimal BIGDECIMAL_THREE_THOUSAND = new BigDecimal(3000);
    /**
     * BigDecimal(4000)
     */
    public static final BigDecimal BIGDECIMAL_FOUR_THOUSAND = new BigDecimal(4000);
    /**
     * BigDecimal(5000)
     */
    public static final BigDecimal BIGDECIMAL_FIVE_THOUSAND = new BigDecimal(5000);
    /**
     * 投保人
     */
    public static final String PEOPLEFLAG_ZERO = "0";
    /**
     * 被保人
     */
    public static final String PEOPLEFLAG_ONE = "1";
    /**
     * 是否执行标识：已执行
     */
    public static final String CHECK_STRING_ONE = "1";

    /**
     * String 0
     */
    public static final String STRING_ZERO = "0";
    /**
     * String 1
     */
    public static final String STRING_ONE = "1";
    /**
     * String 2
     */
    public static final String STRING_TWO = "2";
    /**
     * String 3
     */
    public static final String STRING_THREE = "3";
    /**
     * String 4
     */
    public static final String STRING_FOUR = "4";
    /**
     * String 5
     */
    public static final String STRING_FIVE = "5";
    /**
     * String 6
     */
    public static final String STRING_SIX = "6";
    /**
     * String 7
     */
    public static final String STRING_SEVEN = "7";
    /**
     * String 8
     */
    public static final String STRING_EIGHT = "8";
    /**
     * String 9
     */
    public static final String STRING_NINE = "9";
    /**
     * String 10
     */
    public static final String STRING_TEN = "10";
    /**
     * String 11
     */
    public static final String STRING_EVELEN = "11";
    /**
     * String 25
     */
    public static final String STRING_TWENTYFIVE = "25";
    /**
     * 抄单标记:当前
     */
    public static final BigDecimal CURFLAG_ONE = new BigDecimal(1);
    /**
     *  当前代理人  
     */
    public static final BigDecimal AGENT_CURRENT = new BigDecimal(1);
    /**
     * 绿色通道标识：是
     */
    public static final BigDecimal GREEN_FALG_ONE = new BigDecimal(1);
    /**
     * 绿色通道标识：否
     */
    public static final BigDecimal GREEN_FALG_ZERRO = new BigDecimal(0);
    /**
     * 获取移动理赔移动签收uuid
     */
    public static final String CLAIM_MOBILE_UUID = "claimMobileUUid";
    /**
     * 获取移动理赔移动签收地址
     */
    public static final String CLAIM_MOBILE = "claimMobile";
    /**
     * 获取移动理赔渠道页面uuid
     */
    public static final String MOBILE_CHANNEL_UUID = "mobileChannelUUid";
    /**
     * 获取移动理赔渠道页面地址
     */
    public static final String MOBILE_CHANNEL = "mobileChannel";

    /**
     * BigDecimal 类型数值2-9
     */

    public static final BigDecimal B_ZERO = new BigDecimal(0);
    public static final BigDecimal B_ONE = new BigDecimal(1);
    public static final BigDecimal B_TWO = new BigDecimal(2);
    public static final BigDecimal B_THREE = new BigDecimal(3);
    public static final BigDecimal B_FOUR = new BigDecimal(4);
    public static final BigDecimal B_FIVE = new BigDecimal(5);
    public static final BigDecimal B_SIX = new BigDecimal(6);
    public static final BigDecimal B_SEVEN = new BigDecimal(7);
    public static final BigDecimal B_EIGHT = new BigDecimal(8);
    public static final BigDecimal B_NINE = new BigDecimal(9);
    public static final BigDecimal B_TEN = new BigDecimal(10);
    /**
     * 重复案件的校验：立案之前为1
     */
    public static final String REGISTER_BEFORE_ONE = "1";
    /**
     * 重复案件的校验：立案之后为2
     */
    public static final String REGISTER_AFTER_TWO = "2";
    /**
     * 重复案件的校验：不存在重复案件
     */
    public static final String REGISTER_ZERRO = "0";
    /**
     * 关于无效保单的校验：1为是
     */
    public static final String INVALID_POLICY_ONE = "1";
    /**
     * 出险结果2是否为空的情况：-1为是
     */
    public static final String ACCIDENT_RESULTTWO_NULL = "-1";
    /**
     * 判断保险责任是否与理赔类型匹配：10为是
     */
    public static final String CLAIM_TYPE_MATCHING_TEN = "10";
    /**
     * 保存或初始化时报错提示
     */
    public static final String REPORT_ERROR_ONE = "1";
    /**
     * 记录报案登记的CODE值为17
     */
    public static final String REPORT_CODE_SEVENTEEN = "17";
    /**
     * 记录报案登记页面编码，报案信息录入为1
     */
    public static final String REPORT_PAGE_CODING_ONE = "1";
    /**
     * 参数类型：2 分次给付
     */
    public static final String CLAIM_INSTALMENT_TWO = "2";
    /**
     * 预付标识否
     */
    public static final BigDecimal ADVANCE_FLAG_ZERRO = new BigDecimal(0);
    /**
     * VIP客户标识：1为是
     */
    public static final String CUSTOMER_VIP_ONE = "1";
    /**
     * 生存金
     */
    public static final String LIVE_THREE = "3";
    /**
     * 理赔金转年金
     */
    public static final String LIVE_TEN = "10";
    /**
     * 生存金转年金
     */
    public static final String LIVE_ELEVEN = "11";
    /**
     * 现金红利
     */
    public static final String LIVE_ONE = "1";
    /**
     * 已领取生存金或年金
     */
    public static final String DRAW_ONE = "01";
    /**
     * 未领取生存金或年金
     */
    public static final String DRAW_ZERRO = "00";
    /**
     * 累计生息账户
     */
    public static final String ADD_UP_LIVE_ACCOUNT = "1";
    /**
     * 贷款账户
     */
    public static final String LOAN_ACCOUNT = "4";
    /**
     * 自垫账户
     */
    public static final String FROM_THE_PAD_ACCOUNT = "5";
    /**
     * 保费余额账户
     */
    public static final String PREMIUM_BALANCE_ACCOUNT = "13";
    /**
     * 客户账户余额
     */
    public static final String CUSTOMER_ACCOUNT_BALANCE = "12";
    /**
     * 现金红利余额
     */
    public static final String CASH_BONUS_ACCOUNT = "2";
    /**
     * 累计生息账户余额
     */
    public static final String ADD_UP_LIVE_ELEVEN = "11";

    /**
     * @Fields SIGN_NOTICE_CODE_1 : 签收确认给业务员发送信息接口的通知编码
     */
    public static final BigDecimal SIGN_NOTICE_CODE_1 = new BigDecimal(211);

    /**
     * @Fields SIGN_NOTICE_CODE_2 :签收确认给客户发送信息接口的通知编码
     */
    public static final BigDecimal SIGN_NOTICE_CODE_2 = new BigDecimal(212);
    /**
     * @Fields SIGN_NOTICE_CODE_2 :签收确认给客户发送信息接口的通知编码VIP
     */
    public static final BigDecimal SIGN_NOTICE_CODE_3 = new BigDecimal(213);
    /**
     * 绿色通道标识：是（String类型的）
     */
    public static final String GREEN_FALG_ONE_STR = "1";
    /**
     * 绿色通道标识：否(String类型的)
     */
    public static final String GREEN_FALG_ZERRO_STR = "0";
    /**
     * 用于共享池判断是否传过来的绿色标识为双选
     */
    public static final String GREEN_FALG_ZERRO_ONE = "1, 0";
    /**
     * 申请人与被保险人关系为本人
     */
    public static final String CLM_INSUR_RELATION = "00";
    /**
     * 收费项目-理赔收费
     */
    public static final String CHARGE_ITEM_01 = "01";
    /**
     * 质子重离子治疗及靶向治疗保险金
     */
    public static final BigDecimal LIAB_ID_8027 = new BigDecimal("8027");
    /**
     * 癌症放、化疗保险金
     */
    public static final BigDecimal LIAB_ID_8005 = new BigDecimal("8005");
    /**
     * 恶性肿瘤确诊保险金
     */
    public static final BigDecimal LIAB_ID_8051 = new BigDecimal("8051");
    /**
     * 癌症确诊保险金
     */
    public static final BigDecimal LIAB_ID_8003 = new BigDecimal("8003");
    /**
     * 重大疾病保险金组别1
     */
    public static final BigDecimal LIAB_ID_3005 = new BigDecimal("3005");
    /**
     * 重大疾病保险金组别2
     */
    public static final BigDecimal LIAB_ID_3007 = BigDecimal.valueOf(3007);
    /**
     * 重大疾病保险金组别3
     */
    public static final BigDecimal LIAB_ID_3009 = BigDecimal.valueOf(3009);
    /**
     * 重大疾病保险金组别4
     */
    public static final BigDecimal LIAB_ID_3011 = BigDecimal.valueOf(3011);
    /**
     * 重大疾病保险金组别5
     */
    public static final BigDecimal LIAB_ID_3013 = BigDecimal.valueOf(3013);
    /**
     * 前10年关爱保险金
     */
    public static final BigDecimal LIAB_ID_3015 = BigDecimal.valueOf(3015);
    /**
     * 特定严重疾病保险金
     */
    public static final BigDecimal LIAB_ID_3016 = BigDecimal.valueOf(3016);
    
    /**
     * 重大疾病保险金组别3-未满18周岁
     */
    public static final BigDecimal LIAB_ID_3018 = BigDecimal.valueOf(3018);
    /**
     * 重大疾病保险金组别4-未满18周岁
     */
    public static final BigDecimal LIAB_ID_3019 = BigDecimal.valueOf(3019);
    
    /**
     * 重大疾病保险金组别5-未满18周岁
     */
    public static final BigDecimal LIAB_ID_3020 = BigDecimal.valueOf(3020);
    
    /**
     * 责任编码：1001 身故保险金
     */
    public static final BigDecimal LIAB_ID_1001=BigDecimal.valueOf(1001);
    /**
     * 责任编码：1106 养老年金
     */
    public static final BigDecimal LIAB_ID_1106=BigDecimal.valueOf(1106);
    /**
     * 责任编码：3001 重大疾病保险金
     */
    public static final BigDecimal LIAB_ID_3001=BigDecimal.valueOf(3001);
    /**
     * 责任编码：7094_门急诊手术医疗费用保险金
     */
    public static final BigDecimal LIAB_ID_7094=BigDecimal.valueOf(7094);
    /**
     * 责任编码：7093_门急诊非手术医疗费用保险金
     */
    public static final BigDecimal LIAB_ID_7093=BigDecimal.valueOf(7094);
    
    /**
     * 意外伤害残疾保险金
     */
    public static final BigDecimal LIAB_ID_2005 = new BigDecimal("2005");
    /**
     * 意外骨折、关节脱位、关节替换保险金
     */
    public static final BigDecimal LIAB_ID_2019 = new BigDecimal("2019");
    /**
     * 祝寿金
     */
    public static final BigDecimal LIAB_ID_1103 = new BigDecimal("1103");
    /**
     * 7067_轻症癌症保险金
     */
    public static final BigDecimal LIAB_ID_7067 = new BigDecimal("7067");
    /**
     * 7069_轻症疾病保险金
     */
    public static final BigDecimal LIAB_ID_7069 = new BigDecimal("7069");
    /**
     * 第三方给付类型--自费报销
     */
    public static final BigDecimal EXPENSE_REIMBURSE = new BigDecimal("7");
    /**
     * 365天
     */
    public static final int DAY_365 = 365;
    /**
     * 首次新增可选责任日期
     */
    public static final String ADD_OPTLIAB_DATE = "addOptLiabDate";
    /**
     * 出险保单年度可选责任的新增保额
     */
    public static final String TOP_UP_SA = "topUpSA";
    /**
     * 是否在首次年金领取日之前
     */
    public static final String IS_STAR_ANNUITY = "isStarAnnuity";
    /**
     * 上次分红日
     */
    public static final String LAST_ALLOCATE_DATE = "lastAllocateDate";
    /**
     * 年金领取频率
     */
    public static final String IS_PAY_FREQ = "isPayFreq";
    /**
     * 出险日后的现金红利
     */
    public static final String CASH_BONUS = "cashBonus";
    /**
     * 出险日后是否现金领取过现金红利
     */
    public static final String CASH_FLAG = "cashFlag";
    /**
     * 出险日后的现金红利补发利息
     */
    public static final String REISSUE_INTEREST = "reissueInterest";
    /**
     * 是否计算出险日后的进入附加账户的现金红利和补发利息
     */
    public static final String CASH_AND_INTEREST = "cashAndInterest";
    /**
     * 下一个计价日单位价格是否公布
     */
    public static final String IS_PUBISH_PRICES = "isPubishPrices";
    /**
     * 整年度分红累计红利保额
     */
    public static final String BONUS_SA = "bonusSa";
    /**
     * 出险日后部分领取的金额
     */
    public static final String PG_AMOUNT_FLAG = "pgAmountFlag";
    /**
     * 出险日账户部分领取金额
     */
    public static final String PG_TOTAL_PREM = "PGTotalPrem";
    /**
     * 出险日后生存金年金红利挂起解挂取消功能
     */
    public static final String LOCK_PREM_ARAP = "LockPremArap";
    /**
     * 特定手术疾病给付-费用类型
     */
    public static final String SURGERY_TYPE_ONE = "1";
    public static final String SURGERY_TYPE_TWO = "2";
    public static final String SURGERY_TYPE_THREE = "3";
    /**
     * 预付的费用类型
     */
    public static final String P005140100 = "P005140100";
    /**
     * 理算特殊判断的费用类型代码
     */
    public static final String ITEM_CODE_ONE = "FT017";
    
    public static final String ITEM_CODE_TWO ="CC007";
    
    /**
     * 理算特殊判断的费用类型代码：FT012_癌症保险金
     */
    public static final String ITEM_CODE_THREE = "FT012";
    /**
     * 理算特殊判断的费用类型代码
     */
    public static final String ITEM_CODE_FOUR = "FT013";
    /**
     * 理算特殊判断的费用类型代码
     */
    public static final String ITEM_CODE_FIVE = "FT014";
    /**
     * 理算特殊判断的费用类型代码
     */
    public static final String ITEM_CODE_SIX = "FT015";
    /**
     * 理算特殊判断的费用类型代码
     */
    public static final String ITEM_CODE_SEVEN = "FT016";
    /**
     * 理算特殊判断的费用类型代码：FT020_轻症癌症保险金
     */
    public static final String ITEM_CODE_EIGHT = "FT020";
    /**
     * 理算特殊判断的特定参数类型代码：TD006、TD007、TD008、TD009、TD025、TD026
     */
    public static final List<String> SURGERY_CODE_LIST_FIVEONETWO = new ArrayList<String>();
    static{
    	SURGERY_CODE_LIST_FIVEONETWO.add("TD006");
    	SURGERY_CODE_LIST_FIVEONETWO.add("TD007");
    	SURGERY_CODE_LIST_FIVEONETWO.add("TD008");
    	SURGERY_CODE_LIST_FIVEONETWO.add("TD009");
    	SURGERY_CODE_LIST_FIVEONETWO.add("TD025");
    	SURGERY_CODE_LIST_FIVEONETWO.add("TD026");
    }
    
    public static final List<String> SURGERY_CODE_LIST_SEVENSIXTWO = new ArrayList<String>();
    static{
    	SURGERY_CODE_LIST_SEVENSIXTWO.add("FT012");
    	SURGERY_CODE_LIST_SEVENSIXTWO.add("FT013");
    	SURGERY_CODE_LIST_SEVENSIXTWO.add("FT014");
    	SURGERY_CODE_LIST_SEVENSIXTWO.add("FT015");
    	SURGERY_CODE_LIST_SEVENSIXTWO.add("FT016");
    }
    /**
     * 理算特殊处理的产品编码
     */
    public static final String CLM_BUSI_PRODCODE_NINEFOURSIX = "00946000";
    
    public static final String CLM_BUSI_PRODCODE_ONE = "00528000";

    public static final String CLM_BUSI_PRODCODE_TWO = "00515000";

    public static final String CLM_BUSI_PRODCODE_THREE = "00512000";

    public static final String CLM_BUSI_PRODCODE_FOUR = "00513000";

    public static final String CLM_BUSI_PRODCODE_FIVE = "00555000";

    public static final String CLM_BUSI_PRODCODE_SIX = "00556000";

    public static final String CLM_BUSI_PRODCODE_SEVEN = "00786000";

    public static final String CLM_BUSI_PRODCODE_EIGHT = "00521000";

    public static final String CLM_BUSI_PRODCODE_NINE = "00892000";

    public static final String CLM_BUSI_PRODCODE_TEN = "00369001";

    public static final String CLM_BUSI_PRODCODE_ELEVEN = "00369004";

    public static final String CLM_BUSI_PRODCODE_TWELVE = "00552000";

    public static final String CLM_BUSI_PRODCODE_THIRTEEN = "00553000";

    public static final String CLM_BUSI_PRODCODE_FOURTEEN = "00558000";

    public static final String CLM_BUSI_PRODCODE_FIFTEEN = "00144000";

    public static final String CLM_BUSI_PRODCODE_SIXTEEN = "00118000";

    public static final String CLM_BUSI_PRODCODE_SEVETEEN = "00378000";

    public static final String CLM_BUSI_PRODCODE_EIGHTEEN = "00369002";

    public static final String CLM_BUSI_PRODCODE_NINETEEN = "00369003";

    public static final String CLM_BUSI_PRODCODE_TWENTY = "00369004";

    public static final String CLM_BUSI_PRODCODE_TWENTYONE = "00547000";

    public static final String CLM_BUSI_PRODCODE_TWENTYTWO = "00782000";

    public static final String CLM_BUSI_PRODCODE_TWENTYEIGHT = "00731000";
    
    public static final String CLM_BUSI_PRODCODE_TWENTYTHREE = "00732000";

    public static final String CLM_BUSI_PRODCODE_TWENTYFOUR = "00562000";
    
    public static final String CLM_BUSI_PRODCODE_TWENTYFIVEZERO = "00563000";
    
    public static final String CLM_BUSI_PRODCODE_FIVESEVENDOUBLE = "00577000";
    
    public static final String CLM_BUSI_PRODCODE_FIVESEVEN = "00570000";
    
    public static final String CLM_BUSI_PRODCODE_TWENTYFIVE = "00563100";

    public static final String CLM_BUSI_PRODCODE_TWENTYSIX = "00567000";
    
    public static final String CLM_BUSI_PRODCODE_TWENTYSEVEN = "00564000";
    
    public static final String CLM_BUSI_PRODCODE_TWENTYFIVEO = "00565000";
    
    public static final String CLM_BUSI_PRODCODE_FOURZERO = "00540000";
    
    public static final String CLM_BUSI_PRODCODE_FOURONE = "00541000";
    
    public static final String CLM_BUSI_PRODCODE_TWENTYNIGHT = "00779200";
    
    public static final String CLM_BUSI_PRODCODE_THIRTY = "00779400";
    
    public static final String CLM_BUSI_PRODCODE_THIRTYONE = "00954000";
    
    public static final String CLM_BUSI_PRODCODE_FIVEONE = "00955000";
    
    public static final String CLM_BUSI_PRODCODE_THIRTYTWO = "00431000";

    public static final String CLM_BUSI_PRODCODE_THIRTYTHREE = "00955000";
    
    public static final String CLM_BUSI_PRODCODE_NINEFIVESIX = "00956000";
    
    public static final String CLM_BUSI_PRODCODE_NINEFIVENINE = "00959000";
    
    public static final String CLM_BUSI_PRODCODE_NINESIXZERO = "00960000";
    
    public static final String CLM_BUSI_PRODCODE_NINESIXONE = "00961000";
    
    public static final String CLM_BUSI_PRODCODE_NINESIXTWO = "00962000";
    
    public static final String CLM_BUSI_PRODCODE_THIRTYFOURZERO = "00958000";
    
    public static final String CLM_BUSI_PRODCODE_THIRTYFOUR = "00958100";
    
    public static final String CLM_BUSI_PRODCODE_THIRTYFIVE = "00963000";
    
    public static final String CLM_BUSI_PRODCODE_THIRTYSIX = "00907000";
    
    public static final String CLM_BUSI_PRODCODE_THIRTYSEVEN = "00909000";
    
    public static final String CLM_BUSI_PRODCODE_NINEFIVEZERO = "00950000";
    
    public static final String CLM_BUSI_PRODCODE_SEVENSIXTWO = "00762000";
    
    public static final String CLM_BUSI_PRODCODE_ONEONEEIGHTONE = "00118100";
    
    public static final String CLM_BUSI_PRODCODE_NINESEVENEIGHT = "00978000";
    
    public static final String CLM_BUSI_PRODCODE_NINESEVENENINE = "00979000";
    
    public static final String CLM_BUSI_PRODCODE_EIGHTTHREEONE = "00831000";
    
    public static final String CLM_BUSI_PRODCODE_SEVENSIXTHREETWO = "00763200";
    
    public static final String CLM_BUSI_PRODCODE_EIGHTTHREESIX = "00836000";
    
    public static final String CLM_BUSI_PRODCODE_EIGHTTHREESEVEN = "00837000";
    
    public static final String CLM_BUSI_PRODCODE_EIGHTTHREEEIGHT = "00838000";
    
    public static final String CLM_PRODCODE_EIGHTTHREESIX = "836000";
    public static final String POLICY_HOLDER_ONE = "1";

    public static final String INSRUED_LIST_TWO = "2";

    public static final String INSRUED_HOLDER_THREE = "3";
    
    /**
     * 参数类型：分期给付类型码值：分次给付
     */
    public static final String INSTAL_TYPE_PAYMENT = "01";
    /**
     * 参数类型：分期给付类型码值：分次豁免
     */
    public static final String INSTAL_TYPE_WAIVER = "02";
    /**
     * 参数类型：分期给付类型名称：分次给付
     */
    public static final String INSTAL_TYPE_PAYMENT_NAME = "分次给付";
    /**
     * 参数类型：分期给付类型名称：分次豁免
     */
    public static final String INSTAL_TYPE_WAIVER_NAME = "分次豁免";

    /**
     * 赔付金领取方式:1-一次统一给付
     */
    public static final String GET_MODE_CODE_ONE = "1";

    /**
     * 赔付金领取方式:2-按年金方式领取
     */
    public static final String GET_MODE_CODE_TWO = "2";

    /**
     * 赔付金领取方式:3-分期支付
     */
    public static final String GET_MODE_CODE_THREE = "3";

    public static final List getBusiProdCodeList() { // 万能险
        List<String> busiProdCodeList = new ArrayList<String>(); // 万能险
        busiProdCodeList.add("00913000");
        busiProdCodeList.add("00914000");
        busiProdCodeList.add("00902000");
        busiProdCodeList.add("00905000");
        busiProdCodeList.add("00907000");
        busiProdCodeList.add("00912100");
        busiProdCodeList.add("00909000");
        busiProdCodeList.add("00910100");
        busiProdCodeList.add("00903000");
        busiProdCodeList.add("00904000");
        busiProdCodeList.add("00915000");
        busiProdCodeList.add("00916000");
        busiProdCodeList.add("00917000");
        busiProdCodeList.add("00918000");
        busiProdCodeList.add("00920000");
        busiProdCodeList.add("00921000");
        busiProdCodeList.add("00919000");
        return busiProdCodeList;
    }

    public static final List getBusiProdCode9List() {
        List<String> busiProdCode9List = new ArrayList<String>(); // 投连万能险
        busiProdCode9List.add("00913000");
        busiProdCode9List.add("00914000");
        busiProdCode9List.add("00902000");
        busiProdCode9List.add("00905000");
        busiProdCode9List.add("00907000");
        busiProdCode9List.add("00912100");
        busiProdCode9List.add("00909000");
        busiProdCode9List.add("00910100");
        busiProdCode9List.add("00903000");
        busiProdCode9List.add("00904000");
        busiProdCode9List.add("00915000");
        busiProdCode9List.add("00916000");
        busiProdCode9List.add("00917000");
        busiProdCode9List.add("00918000");
        busiProdCode9List.add("00920000");
        busiProdCode9List.add("00921000");
        busiProdCode9List.add("00919000");
        busiProdCode9List.add("00890000");
        busiProdCode9List.add("00888000");
        busiProdCode9List.add("00892000");
        return busiProdCode9List;
    }

    public static final List getBusiProdCode10List() {

        List<String> busiProdCode10List = new ArrayList<String>(); // 非保险合同
        busiProdCode10List.add("00913000");
        busiProdCode10List.add("00914000");
        busiProdCode10List.add("00902000");
        busiProdCode10List.add("00905000");
        busiProdCode10List.add("00907000");
        busiProdCode10List.add("00912100");
        busiProdCode10List.add("00909000");
        busiProdCode10List.add("00910100");
        busiProdCode10List.add("00903000");
        busiProdCode10List.add("00904000");
        busiProdCode10List.add("00915000");
        busiProdCode10List.add("00916000");
        busiProdCode10List.add("00917000");
        busiProdCode10List.add("00918000");
        busiProdCode10List.add("00920000");
        busiProdCode10List.add("00921000");
        busiProdCode10List.add("00919000");
        busiProdCode10List.add("00890000");
        busiProdCode10List.add("00888000");
        busiProdCode10List.add("00892000");
        return busiProdCode10List;
    }
    /**
     * 
     * @description 需要查询账户价值的费用类型list 
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @return
     */
    public static final List getFeeTypeList() {
        //询账户价值的费用类型list
        List<String> feeTypeList = new ArrayList<String>();
        feeTypeList.add("P005010000");     //理赔金
        feeTypeList.add("G005070300");  //  扣回保单贷款本金（无账户价值）1
        feeTypeList.add("G005080300");  //  扣回保单贷款利息（无账户价值）2
        feeTypeList.add("G005090300");  //  扣回自垫保费（无账户价值）3
        feeTypeList.add("G005100300");  //  扣回自垫利息（无账户价值）4
        feeTypeList.add("G005080301");  //  扣回保单贷款利息增值税（无账户价值）5
        feeTypeList.add("G005100301");  //  扣回自垫利息增值税（无账户价值）6 
        feeTypeList.add("G005380300");  //理赔追回年金（无账户价值） 
        feeTypeList.add("G005060000");  //扣回欠缴保费（无账户价值）   
        return feeTypeList;
    }
    
    
    

    /**
     * 结算组别1--险种终止
     * 
     * @return
     */
    public static final Set settlementGroupOne() { // 万能险
        Set<String> settlementGroup = new HashSet<String>(); // 万能险
        settlementGroup.add("3|4|5|6");
        settlementGroup.add("7");
        settlementGroup.add("8");
        settlementGroup.add("9");
        settlementGroup.add("10");
        settlementGroup.add("11|12");
        settlementGroup.add("13");
        settlementGroup.add("14|15|21");
        settlementGroup.add("22");
        settlementGroup.add("19|20");
        return settlementGroup;
    }

    /**
     * 结算组别2--责任组终止
     * 
     * @return
     */
    public static final Set settlementGroupTwo() { // 万能险
        Set<String> settlementGroup = new HashSet<String>(); // 万能险
        settlementGroup.add("9");
        return settlementGroup;
    }

    /**
     * 判断是否自动发起慰问 1:是
     */
    public static final String IS_COMFORT = "1";

    /**
     * 业务员属性
     */
    public static final String AGENT_FLAG = "2";

    /**
     * 是否发送短信 Y 发送
     */
    public static final String IS_MSG_Y = "Y";

    /**
     * 分期提前抽档天数 公共参数配置code
     */
    public static final String INSTALMENT_DAY_CODE = "2";

    /**
     * 每小时3600秒
     */
    public static final int SECONDS_POR_HOUR = 3600;
    /**
     * 每年12个月
     */
    public static final int MONTH_POR_YEAR = 12;
    /**
     * 一个季度3个月
     */
    public static final int MONTH_POR_QUARTER = 3;
    /**
     * 半年6个月
     */
    public static final int MONTH_HALF_YEAR = 6;
    /**
     * 一年四个季度
     */
    public static final int QUARTER_YEAR = 4;
    /**
     * 一天的分钟数
     */
    public static final int HOUR_PER_DAY = 1440;
    /**
     * 每小时3600000秒
     */
    public static final int MILLISECONDS_POR_HOUR = 3600000;
    /**
     * 每分钟60秒
     */
    public static final int SECONDS_POR_MINS = 60; 
    /**
     * 数字星期天
     */
    public static final int SUNDAY_NUM = 7; 
    /**
     * 1990年
     */
    public static final int EARLIST_DATE = 1990;
    /**
     * 权限级别14(审核)
     */
    public static final int PREM_LEAVE_AUDIT = 14;
    
    /**
     * 权限级别10
     */
    public static final int PREM_LEAVE_TEN = 10;
    /**
     * 权限级别30(审批)
     */
    public static final int PREM_LEAVE_APPROVE =  30;
    
    /**
     * 审批疑难权限级别10
     */
    public static final int DIFFICULT_LEAVE_TEN = 10;
    
    /**
     * 总公司机构86
     */
    public static final int PARENT_ORG_CODE =  86;
    /**
     * 日期年截取位置 substring（0,4）
     */
    public static final int SUBSTRING_DATE_YEAR =  4;
    /**
     * 日期年月日截取位置 substring（0,10）
     */
    public static final int SUBSTRING_DATE =  10;
    /**
     * 日期时分秒截取位置 substring（10,18）
     */
    public static final int SUBSTRING_TIME =  18;
    /**
     * 日期时分秒中间有空格截取位置 substring（10,19）
     */
    public static final int SUBSTRING_TIME2 =  19;
    
    /**
     * 数字0.5
     */
    public static final BigDecimal ZERO_POINT_FIVE = BigDecimal.valueOf(0.5);
    /**
     *  短信通知模板代码 80
     */
    public static final BigDecimal MESSAGE_MODE_EIGHTY  = BigDecimal.valueOf(80);
   
    
    /**
     *  短信通知模板代码 115
     */
    public static final BigDecimal MESSAGE_MODE_ONEHUNDREDFIFTEEN   = BigDecimal.valueOf(115);
    /**
     *  短信通知模板代码 123
     */
    public static final BigDecimal MESSAGE_MODE_ONEHUNDREDTWENTYTHREE   = BigDecimal.valueOf(123);
   
    /**
     *  短信通知模板代码 201  报案确认
     */
    public static final BigDecimal MESSAGE_MODE_TWOZEROONE  = BigDecimal.valueOf(201);
    /**
     *  短信通知模板代码202 报案确认 给业务员发送邮件
     */
    public static final BigDecimal MESSAGE_MODE_TWOZEROTWO  = BigDecimal.valueOf(202);
    
    /**
     *  短信通知模板代码 251  理赔关怀任务时限提醒
     */
    public static final BigDecimal MESSAGE_MODE_TWOFIVEONE  = BigDecimal.valueOf(251);
    
    /**
     *  短信通知模板代码 261  理赔关怀任务时限提醒
     */
    public static final BigDecimal MESSAGE_MODE_TWOSIXTYONE   = BigDecimal.valueOf(261);
    
    /**
     *  短信通知模板代码 263
     */
    public static final BigDecimal MESSAGE_MODE_TWOSIXTYTHREE   = BigDecimal.valueOf(263);
    /**
     *  短信通知模板代码 264 理赔关怀任务提醒
     */
    public static final BigDecimal MESSAGE_MODE_TWOSIXTYFOUR   = BigDecimal.valueOf(264);
    
    /**
     *  短信通知模板代码 211  签收确认节点
     */
    public static final BigDecimal MESSAGE_MODE_TWOELEVEN   = BigDecimal.valueOf(211);
    
    /**
     *  短信通知模板代码 212  签收确认节点
     */
    public static final BigDecimal MESSAGE_MODE_TWOTWELVE   = BigDecimal.valueOf(212);
    /**
     *  短信通知模板代码 214  签收确认受托人节点
     */
    public static final BigDecimal MESSAGE_MODE_TWOTWFOUR   = BigDecimal.valueOf(214);
    /**
     *  短信通知模板代码 213  优质客户
     */
    public static final BigDecimal MESSAGE_MODE_TWOTHREE   = BigDecimal.valueOf(213);
  
    /**
     *  短信通知模板代码277 调查完成
     */
    public static final BigDecimal MESSAGE_MODE_TWOSEVENTYSEVEN   = BigDecimal.valueOf(277);
   
    /**
     *  短信通知模板代码278 理赔二核任务完成
     */
    public static final BigDecimal MESSAGE_MODE_TWOSEVENTYEIGHT   = BigDecimal.valueOf(278);
   
    /**
     *  短信通知模板代码 279 审核任务提醒
     */
    public static final BigDecimal MESSAGE_MODE_TWOSEVENTYNINE   = BigDecimal.valueOf(279);
   
    /**
     *  短信通知模板代码 280 审核节点提醒
     */
    public static final BigDecimal MESSAGE_MODE_TWOEIGHTY   = BigDecimal.valueOf(280);
   
    /**
     *  短信通知模板代码 272 事中质检不通过
     */
    public static final BigDecimal MESSAGE_MODE_TWOSEVENTYTWO   = BigDecimal.valueOf(272);
    
    /**
     *  短信通知模板代码 273 个人质检任务提醒
     */
    public static final BigDecimal MESSAGE_MODE_TWOSEVENTYTHREE   = BigDecimal.valueOf(273);
   
    /**
     *  短信通知模板代码 274 告知赔案完成回退
     */
    public static final BigDecimal MESSAGE_MODE_TWOSEVENTYFOUR   = BigDecimal.valueOf(274);
    
    /**
     *  短信通知模板代码241 审批
     */
    public static final BigDecimal MESSAGE_MODE_TWOFOURTYONE   = BigDecimal.valueOf(241);
    /**
     *  短信通知模板代码242 审批
     */
    public static final BigDecimal MESSAGE_MODE_TWOFOURTYTWO   = BigDecimal.valueOf(242);
    /**
     *  短信通知模板代码243 审批
     */
    public static final BigDecimal MESSAGE_MODE_TWOFOURTYTHREE   = BigDecimal.valueOf(243);
    /**
     *  短信通知模板代码244 审批支付成功（单人申请人）
     */
    public static final BigDecimal MESSAGE_MODE_TWOFOURTYFOUR   = BigDecimal.valueOf(244);
   
    /**
     *  短信通知模板代码245 审批支付成功（赔付金额为0）
     */
    public static final BigDecimal MESSAGE_MODE_TWOFOURTYFIVE   = BigDecimal.valueOf(245);
   
    /**
     *  短信通知模板代码282 审批
     */
    public static final BigDecimal MESSAGE_MODE_TWOEIGTHTYTWO   = BigDecimal.valueOf(282);
   
    /**
     *  短信通知模板代码281 问题件任务提醒
     */
    public static final BigDecimal MESSAGE_MODE_TWOEIGTHTYONE   = BigDecimal.valueOf(281);
   
    /**
     *  短信通知模板代码222 给受托人发送信息
     */
    public static final BigDecimal MESSAGE_MODE_TWOTWENTYTWO   = BigDecimal.valueOf(222);
    /**
     *  短信通知模板代码221 给申请人发送信息
     */
    public static final BigDecimal MESSAGE_MODE_TWOTWENTYONE   = BigDecimal.valueOf(221);
    /**
     *  邮件通知模板代码 295  发起调查时给调查机构对应的人员发送邮件通知
     */
    public static final BigDecimal MESSAGE_MODE_TWONINEFIVE  = BigDecimal.valueOf(295);
    
    /**
     *  邮件通知模板代码 296  审核任务进入简易案件共享池时给改赔案签收机构对应的消息配置人员发送邮件
     */
    public static final BigDecimal MESSAGE_MODE_TWONINESIX  = BigDecimal.valueOf(296);
    
    /**
     *  邮件通知模板代码 297  点击【审核确认】后，有拒付的给发送拒付审批通知邮件给消息配置人员
     */
    public static final BigDecimal MESSAGE_MODE_TWONINESEVEN  = BigDecimal.valueOf(297);
    
    /**
     *  邮件通知模板代码 298  收付费返回转账失败结果时给签收机构对应消息配合人员发送邮件
     */
    public static final BigDecimal MESSAGE_MODE_TWONINEEIGHT  = BigDecimal.valueOf(298);
    
    /**
     *  邮件通知模板代码 299  审核、审批管理的共享池、个人任务列表中的“理赔时效”达到配置的天数后时给签收机构对应消息配合人员发送邮件
     */
    public static final BigDecimal MESSAGE_MODE_TWONINENINE  = BigDecimal.valueOf(299);
    
    /**
     *  60天
     */
    public static final int SIXTY_DAYS  = 60; 
    /**
     *  30天
     */
    public static final int THRITY_DAYS = 30;
    
    /**
     * 默认分页页数
     */
    public static final int PAGE_SIZE_TEN = 10;
    /**
     * 调查状态t_Survey_Status  1：已申请
     */
    public static final BigDecimal SURVEY_STATUS_APPLY = BigDecimal.valueOf(1);
    
    /**
     * 调查状态t_Survey_Status  2：完成
     */
    public static final BigDecimal SURVEY_STATUS_FINISH = BigDecimal.valueOf(2);
    /**
     * 调查状态t_Survey_Status  3：已撤销
     */
    public static final BigDecimal SURVEY_STATUS_REVOKE = BigDecimal.valueOf(3);
    /**
     * 调查状态t_Survey_Status  4：进行中 
     */
    public static final BigDecimal SURVEY_STATUS_CONDUCT = BigDecimal.valueOf(4);
    /**
     * 调查方式t_survey_mode  01：现场查勘
     */
    public static final String SURVEY_MODE_ONE = "01";
    
    /**
     * 是否问题件：2 正常件
     */
    public static final BigDecimal PROBLEM_CHECK_FLAG_TWO = BigDecimal.valueOf(2);
    /**
     * 是否问题件：3 问题件
     */
    public static final BigDecimal PROBLEM_CHECK_FLAG_THREE = BigDecimal.valueOf(3);
    
    /**
     * 外包状态：0  录入失败标识
     */
    public static final String OUT_SOURCE_FLAG_ZERO = "0";
    /**
     * 外包状态：1  录入成功标识
     */
    public static final String OUT_SOURCE_FLAG_ONE = "0";
    /**
     * 外包状态：2  已回复
     */
    public static final BigDecimal OUTSOURCE_STATUS_TWO = BigDecimal.valueOf(2);
    /**
     * 外包状态：3  问题件（外包商问题件）
     */
    public static final BigDecimal OUTSOURCE_STATUS_THREE = BigDecimal.valueOf(3);
  /**
   * 外包类型:医疗1
   */
    public static final BigDecimal OUTSOURCE_TYPE_ONE = BigDecimal.valueOf(1);
    /**
     * 外包类型:非医疗2
     */
      public static final BigDecimal OUTSOURCE_TYPE_TWO = BigDecimal.valueOf(2);
    
    /**
     * 外包状态 4 待发送
     */
    public static final BigDecimal OUTSOURCE_STATUS_FOUR =  BigDecimal.valueOf(4);
    /**
     * 外包案件表影像问题件状态:无 0
     */
    public static final String IMAGE_ISSUE_STATUS_ZERO = "0";
    /**
     * 外包案件表影像问题件状态:‘1-待处理’
     */
    public static final String IMAGE_ISSUE_STATUS_ONE = "1";
   
    /**
     * 外包商问题件 1-外包商问题件
     */
    public static final String OUTER_ISSUEFLAG_ONE = "1";
    /**
     * 外包商问题件 2-没有问题
     */
    public static final String OUTER_ISSUEFLAG_TWO = "2";
    /**
     * 问题件类型 6 影像质检不通过
     */
    public static final String ISSUE_TYPE_SIX = "6";
    
    /**
     * 日期28号
     */
    public static final int DATE_TWENTY_EIGHT  = 28;

    /**
     * 银行账户类型 其他银行账号
     */
    public static final BigDecimal ACCOUNTYPE_OTHER  = BigDecimal.valueOf(9);
    /**
     * //账户状态 可用
     */
    public static final String ACCOUNT_STATUS  = "1";
    
    /**
     * 账户信息是否已复核通过
     */
    public static final BigDecimal BANK_CONFIRM  = BigDecimal.valueOf(1);
    /**
     * 查询计算投连万能扣费金额   类型退保费用
     */
    public static final int TYPE_FORE = 4;
    
    /**
     *    申请人属性默认值为指定受益人:1
     */
    public static final String APPLICANT_NATURE = "1";
    /**
     * RateType
     */
    public static final BigDecimal RATA_TYPE = BigDecimal.valueOf(23);
    
    
    /**
    * 主管作业管理页面 任务类型   签收登记
    */
    public static final int PAGE_TASKCODE_SIGN=0;
    /**
    * 主管作业管理页面 任务类型  立案登记
    */
    public static final int PAGE_TASKCODE_REGISTER=1;
    /**
    * 主管作业管理页面 任务类型  审核
    */
    public static final int PAGE_TASKCODE_AUDIT=2;
    /**
    * 主管作业管理页面 任务类型  审批
    */
    public static final int PAGE_TASKCODE_APPROVE=3;
    /**
    * 主管作业管理页面 任务类型  预付申请
    */
    public static final int PAGE_TASKCODE_PERPAID=4;
    /**
    * 主管作业管理页面 任务类型  预付复核
    */
    public static final int PAGE_TASKCODE_PERPAID_AUDIT=5;
    /**
    * 主管作业管理页面 任务类型  回退申请
    */
    public static final int PAGE_TASKCODE_FALLBACK=6;
    /**
    * 主管作业管理页面 任务类型  回退审核
    */
    public static final int PAGE_TASKCODE_FALLBACK_AUDIT=7;
    /**
    * 主管作业管理页面 任务类型  事中质检
    */
    public static final int PAGE_TASKCODE_QUALITY_IN=8;
    /**
    * 主管作业管理页面 任务类型  事后质检
    */
    public static final int PAGE_TASKCODE_QUALITY_AFTER=9;
    /**
    * 主管作业管理页面 任务类型  付费变更申请
    */
    public static final int PAGE_TASKCODE_FEE_CHANGE=10;
    /**
    * 主管作业管理页面 任务类型  付费变更复核
    */
    public static final int PAGE_TASKCODE_FEE_CHANGE_AUDIT=11;
    /**
    * 主管作业管理页面 任务类型  补充单证问题件
    */
    public static final int PAGE_TASKCODE_PROBLEM=12;
    /**
    * 主管作业管理页面 任务类型  协谈
    */
    public static final int PAGE_TASKCODE_CHAT=13;
    /**
    * 主管作业管理页面 任务类型  合议
    */
    public static final int PAGE_TASKCODE_DISCUSSION=14;
    /**
     * 主管作业管理页面 任务类型  立案登记复核
     */
     public static final int PAGE_TASKCODE_REREGISTER=15;
     /**
      * 主管作业管理页面 任务类型 分次给付审核
      */
      public static final int PAGE_TASKCODE_PAYAUDIT=16;
    /**
     * 查询作业监控信息 监控范围为全公司
     */
    public static final String MONITOR_COMPANY="01";
    /**
     * 查询作业监控信息 监控范围为片区
     */
    public static final String MONITOR_AREA="02";
    /**
     * 查询作业监控信息 监控范围为机构
     */
    public static final String MONITOR_ORG="03";
    /**
     * 查询作业监控信息 监控范围为人员 
     */
    public static final String MONITOR_PERSON="04";
    
    /**
     * 查询作业监控信息 监控周期为自定义时间
     */
    public static final String  MONITOR_PERIOD_SALF="04";
    
    /**
     * 查询作业监控信息 监控周期非自定义时间 按月
     */
    public static final String MONITOR_MONTH="01";
    /**
     * 查询作业监控信息 监控周期非自定义时间 按周
     */
    public static final String MONITOR_WEEK="02";
    /**
     * 查询作业监控信息 监控周期非自定义时间 按天
     */
    public static final String MONITOR_DAY="03";
    
    /**
     * 理赔系统 性别 男
     */
    public static final BigDecimal SEX_MALE=BigDecimal.valueOf(1) ;
    /**
     * 理赔系统 性别 女
     */
    public static final BigDecimal SEX_FEMALE=BigDecimal.valueOf(2) ;
    /**
     * 理赔系统 性别 未知
     */
    public static final BigDecimal SEX_OTHER=BigDecimal.valueOf(9) ;
    
    /**
     * 其他系统 性别 男
     */
    public static final String SEX_MALE_OTHER_SYS= "0";
    /**
     * 其他系统 性别 女
     */
    public static final String SEX_FEMALE_OTHER_SYS= "1" ;
    /**
     * 其他系统 性别 未知
     */
    public static final String SEX_OTHER_OTHER_SYS= "2" ;
    /**
     * 判断是否为无限次分期 9999或是999
     */
    public static final BigDecimal INDEFINITE_STAGING_ONE= BigDecimal.valueOf(9999) ;
    /**
     * 判断是否为无限次分期 9999或是999
     */
    public static final BigDecimal INDEFINITE_STAGING_TWO=BigDecimal.valueOf(999);
    /**
     * 支付计划页面未转年金提示
     */
    public static final BigDecimal PAY_PLAN_MSG_TWO=BigDecimal.valueOf(2);
    /**
     * 支付计划页面未分期提示
     */
    public static final BigDecimal PAY_PLAN_MSG_THREE=BigDecimal.valueOf(3);
    /**
     * 支付计划页面未分期提示
     */
    public static final BigDecimal PAY_PLAN_MSG_FORE=BigDecimal.valueOf(4);
    /**
     * 支付计划页面 险种循环扣减最大次数
     */
    public static final int MAX_NUM=30;
    /**
     * 审核校验提示信息ID：赔案有未完成的调查任务，请完成未决任务后再进行合同处理。
     */
    public static final String AUDIT_TASK_ID_TWO="2";
    
    /**
     * 审核校验提示信息ID：赔案有未完成的协谈任务，请完成未决任务后再进行合同处理。
     */
    public static final String AUDIT_TASK_ID_THREE="3";
    /**
     * 审核校验提示信息ID：赔案有未完成的合议任务，请完成未决任务后再进行合同处理。
     */
    public static final String AUDIT_TASK_ID_FOUR="4";
    /**
     * 审核校验提示信息ID：赔案有未完成的签报任务，请完成未决任务后再进行合同处理。
     */
    public static final String AUDIT_TASK_ID_FIVE="5";
    /**
     * 审核校验提示信息ID：险种存在欠缴保费，需要补交保费后，才能继续进行合同处理。
     */
    public static final String AUDIT_TASK_ID_SIXTEEN="16";
    
    /**
     *  签报任务 5拒绝终止
     */
    public static final String SIGN_TASK_FIVE="5";
    /**
     *  签报任务 6正常终止
     */
    public static final String SIGN_TASK_SIX="6";
    /**
     *  签报任务7撤销终止
     */
    public static final String SIGN_TASK_SEVEN="7";
    /**
     *  转办任务天数  距离当前日期
     */
    public static final int TURN_DAYS=7;
    /**
     *  转办任务 7拒绝终止
     */
    public static final String TURN_TASK_SEVEN="7";
    /**
     *  转办任务 8终止
     */
    public static final String TURN_TASK_EIGHT="8";
    /**
     *  转办任务 9撤销终止
     */
    public static final String TURN_TASK_NIE="9";
    /**
     *  转办任务 10回复终止-已处理
     */
    public static final String TURN_TASK_TEN="10";
    /**
     *  转办任务 11回复终止-未处理
     */
    public static final String TURN_TASK_ELVEN="11";
    /**
     *  转办场景  3 满期金追回
     */
    public static final String TURN_SCENE_THREE="3";
    
    /**
     * DECIOSN_CODE 标准体
     */
    public static final String DECIOSN_CODE_STANDARD="10";
    /**
     * DECIOSN_CODE 限额
     */
    public static final String DECIOSN_CODE_QUOTA="32";
    /**
     * DECIOSN_CODE 延期
     */
    public static final String DECIOSN_CODE_DELAY="40";
    /**
     * DECIOSN_CODE 拒保
     */
    public static final String DECIOSN_CODE_DECLINATURE="50";
    
    /**
     * 核保结论原因选项 ： T_DECISION_REASON_TYPE 1
     */
    public static final BigDecimal DECISION_REASON_TYPE_ONE=BigDecimal.valueOf(1);
    
    /**
     * 核保结论原因选项 ：T_DECISION_REASON_TYPE 2
     */
    public static final BigDecimal DECISION_REASON_TYPE_TWO=BigDecimal.valueOf(2);
    
    /**
     * 核保结论原因选项 ：T_DECISION_REASON_TYPE 3
     */
    public static final BigDecimal DECISION_REASON_TYPE_THREE=BigDecimal.valueOf(3);
    
    /**
     * 核保结论原因选项 ：T_DECISION_REASON_TYPE 4
     */
    public static final BigDecimal DECISION_REASON_TYPE_FOUR=BigDecimal.valueOf(4);
    /**
     * 核保结论原因选项 ：T_DECISION_REASON_TYPE 5
     */
    public static final BigDecimal DECISION_REASON_TYPE_FIVE=BigDecimal.valueOf(5);
    /**
     * 核保结论原因选项 ：T_DECISION_REASON_TYPE 6
     */
    public static final BigDecimal DECISION_REASON_TYPE_SIX=BigDecimal.valueOf(6);
    /**
     * 核保结论原因选项 ：T_DECISION_REASON_TYPE 7
     */
    public static final BigDecimal DECISION_REASON_TYPE_SEVEN=BigDecimal.valueOf(7);
    /**
     * 核保结论原因选项 ： T_DECISION_REASON_TYPE 8
     */
    public static final BigDecimal DECISION_REASON_TYPE_EIGHT=BigDecimal.valueOf(8);
    /**
     * 电话回访  用户角色  1 ：申请人  
     */
    public static final String REVISIT_USER_TYPE_ONE="1";
    /**
     * 电话回访  用户角色  2 ：出险人
     */
    public static final String REVISIT_USER_TYPE_TWO="2";
     
    /**
     * 电话回访  用户角色   3 ：报案人
     */
    public static final String REVISIT_USER_TYPE_THREE="3";
    /**
     * 案件标识：普通案件  t_case_level
     */
    public static final BigDecimal CASE_FLAG_NOMAL=BigDecimal.valueOf(1);
    /**
     * 案件标识： -诉讼案件
     */
    public static final BigDecimal REVISIT_USER_SIGN=BigDecimal.valueOf(2);
    /**
     * 案件标识：-疑难案件
     */
    public static final BigDecimal CASE_FLAG_DIFFI=BigDecimal.valueOf(3);
    /**
     * 案件标识：-简易案件
     */
    public static final BigDecimal CASE_FLAG_EAZY=BigDecimal.valueOf(4);
    /**
     * 案件标识码值转化：-普通案件
     */
    public static final String CASE_FLAG_NOMAL_STR="11";
    /**
     * 案件标识码值转化： -诉讼案件
     */
    public static final String REVISIT_USER_SIGN_STR="12";
    /**
     * 案件标识码值转化：-疑难案件
     */
    public static final String CASE_FLAG_DIFFI_STR="14";
    
    /**
     * 申请任务类型：-简易审核
     */
    public static final String APPLY_TASK_TYPE_ONE="easyAudit";
    /**
     *  申请任务类型：-普通审核
     */
    public static final String APPLY_TASK_TYPE_TWO="audit";
    /**
     * 申请任务类型：-普通审批
     */
    public static final String APPLY_TASK_TYPE_THREE="approve";
    /**
     *  申请任务类型：-疑难审批
     */
    public static final String APPLY_TASK_TYPE_FOUR="difficultApprove";
  
     
    
    /**
     * 本次出险时间距离5631首次生效日在3年以内(含3年)，且出险客户3年内名下所有医疗险（包括其他医疗险和5631、563）累计赔付金额（含本次出险的赔付金额）大于5万元
     */
    public static final BigDecimal MONEY_FIVTY_THOUSAND=BigDecimal.valueOf(50000);
    /**
     * 睡眠时间
     */
    public static final int SIXTY_THOUSAND= 60000;
    
   
    /**
     * 质检状态：-待质检 
     */
    public static final BigDecimal QC_STATUS_ZERO=BigDecimal.valueOf(0);
    /**
     * 质检状态：-质检中 
     */
    public static final BigDecimal QC_STATUS_ONE=BigDecimal.valueOf(1);
    /**
     * 质检状态：-质检完成
     */
    public static final BigDecimal QC_STATUS_TWO=BigDecimal.valueOf(2);
    
    
    /**
     * 复核状态：待处理/待复核
     */
    public static final BigDecimal REAUDIT_STATUS=BigDecimal.valueOf(4);
    
    /**
     * 复核状态：复核修改
     */
    public static final BigDecimal REAUDIT_STATUS_ONE=BigDecimal.valueOf(1);
    
    /**
     * 复核状态：复核通过
     */
    public static final BigDecimal REAUDIT_STATUS_TWO=BigDecimal.valueOf(2);
    
    /**
     * 复核状态：复核终止
     */
    public static final BigDecimal REAUDIT_STATUS_THREE=BigDecimal.valueOf(3);
    /**
     * 险种：短期险 60002
     */
    public static final BigDecimal BUSI_PRODUCT_SHORT=BigDecimal.valueOf(60002);
    /**
     * 险种：长期险 60001
     */
    public static final BigDecimal BUSI_PRODUCT_LONG=BigDecimal.valueOf(60001);
    /**
     * 等待打印返回 设置线程时间 10秒 10000毫秒
     */
    public static final int SEND_MAIL_SLEEP=10000;
    /**
     *发送邮件控制方法超时 设置延迟获取 3秒 3000毫秒
     */
    public static final int SEND_MAIL_SLEEP_THREE=3000;
    /**
     * 发送带附件的邮件 设置延迟获取 2秒 2000毫秒
     */
    public static final int SEND_MAIL_SLEEP_GET=2000;
    /**
     * 代理人：五星级A3
     */
    public static final String AGENT_LEVEL_FIVE_ATHREE="3";
   /**
    * 多倍保障重大疾病保险前10年关爱保险金需与身故或重大疾病保险金同时给付，请予以调整 提示标识
    */
    public static final BigDecimal IS_TEN_YEARS=BigDecimal.valueOf(10);
    
    /**
     *  Lunar工具类参数1
     */
     public static final int LUNAR_INFO_ONE=0x8000;
     /**
      *  Lunar工具类参数2
      */
      public static final int LUNAR_INFO_TWO=0x8;
      /**
       *  Lunar工具类参数3
       */
       public static final int LUNAR_INFO_THREE=0x10000;
       /**
        *  Lunar工具类参数4
        */
        public static final long LUNAR_INFO_FOUR=86400000L;
        /**
         *  Lunar工具类参数5
         */
         public static final int LUNAR_INFO_FIVE=0xf;
        

    /**
     * @Fields COMMA : 中括号："]"
     */
    public static final String BIGCOMMA = "]";
    /**
     * @Fields BRACES : 大括号："}"
     */
    public static final String BRACES = "}";

    /**
     * base64字符串转化成图片 功能使用
     */
    public static final int BASE64_USE = 256;
    /**
     * 收付费相关：场景编码-00501 （理赔金）
     */
    public static final String SCENE_CODE_ONE = "00501";
    /**
     * 收付费相关：场景编码-00501 （理赔金-预付款）
     */
    public static final String SCENE_CODE_TEN = "00510";
    /**
     * 收付费相关：场景编码-00502 （多交保费退还）
     */
    public static final String SCENE_CODE_TWO = "00502";
    /**
     * 收付费相关：场景编码-00503 （未满期保费）
     */
    public static final String SCENE_CODE_THREE = "00503";
    /**
     * 收付费相关：场景编码-00505 （保证年金一次性领取）
     */
    public static final String SCENE_CODE_FIVE = "00505";
    
    /**
     * 收付费相关：场景编码-00507 （理赔分期给付）
     */
    public static final String SCENE_CODE_SEVEN = "00507"; 
    
    /**
     * 收付费相关：场景编码-00519 （资产管理费）追回
     */
    public static final String SCENE_CODE_NINTEEN = "00519"; 
    /**
     * 收付费相关：场景编码-00520 （资产管理费）扣除
     */
    public static final String SCENE_CODE_TWENTY = "00520"; 
    /**
     * 收付费相关：场景编码-00512 （理赔退费）
     */
    public static final String SCENE_CODE_TWELVE = "00512";
    /**
     * 收付费相关：场景编码-00513 （理赔退保）
     */
    public static final String SCENE_CODE_THIRTEEN = "00513"; 
    
    /**
     * 收付费相关：场景编码-00370 //资产管理费 -投连险
     */
    public static final String SCENE_CODE_THIRTYSEVEN = "00370"; 
    
    /**
     * 收付费相关（T_ARAP_SOURCE_TABLE）：数据来源-067001 理算表
     */
    public static final String ARAP_SOURCE_TABLE_LIAB = "067001"; 
    
    /**
     * 收付费相关（T_ARAP_SOURCE_TABLE）：数据来源-067002 结算表
     */
    public static final String ARAP_SOURCE_TABLE_ADJUST = "067002"; 
   
    
     /**
      * 时间跨度3000年
      */
     public static final int TIME_SPAN_3000=3000;
     /**
      * 短期险
      */
     public static final BigDecimal PERIOD_TYPE_60002=new BigDecimal(60002);
     /**
      * 144产品
      */
     public static final String PRODUCT_CODE_STD_144 = "144";
     /**
      * 118产品
      */
     public static final String PRODUCT_CODE_STD_118 = "118";
     /**
      * 住院天数30
      */
     public static final int BILL_DAY_30=30;
     /**
      * 月数
      */
     public static final int MONTH_SIX = 6;
     /**
      * 月数
      */
     public static final int MONTH_THREE = 3;
     /**
      * 月数
      */
     public static final int MONTH_TWELVE = 12;
     /**
      * 18岁
      */
     public static final int AGE_EIGHTEEN=18;
     /* calcParamCfg的key和value含义:
     3_累加器累计区间类型:                                6_产品保障期  || 5_单个保单年度
     2_责任延长期（天）                              (天数)
     5_一次住院最长天数                              (天数)
     4_住院间隔长度（天）                             (天数)
     8_累加器费用计算类型                             11_门诊||10_住院||12_住院和门诊
     1_事故日后多少天内                              (天数)
     11_保险期间届满时治疗最长至事故日起第多少日止                (天数)
     6_既往赔付天数关联责任代码                          (责任代码)
     7_既往赔付金额关联责任代码                          (责任代码)
     9_累加器费用计算区间                             2_单个住院期间
     13_每次住院期间前后各多少天内                            (天数)
     12_每次住院不超过多少元                               (金额)
     14_此责任赔付不参与到所定义的累加器中进行累计                (累加器id)
     15_意外伤害延续至本合同到期日后多少日内的治疗                (天数)
     16_保险期间届满时住院治疗最长至事故日起第多少日止          (天数)
     17_保险期间届满时门诊治疗最长至事故日起第多少日止          (天数)
      * */
     /**
      * 累加器累计区间类型
      */
     public static final String STATIC_PARAM_KEY3="3";
     /**
      * 责任延长期（天）
      */
     public static final String STATIC_PARAM_KEY2="2";
     /**
      * 5_一次住院最长天数                              (天数)
      */
     public static final String STATIC_PARAM_KEY5="5";
     /**
      * 4_住院间隔长度（天）                             (天数)
      */
     public static final String STATIC_PARAM_KEY4="4";
     /**
      * 8_累加器费用计算类型
      */
     public static final String STATIC_PARAM_KEY8="8";
     /**
      * 28_门诊治疗类型
      */
     public static final String STATIC_PARAM_KEY28="28";
     /**
      * 1_事故日后多少天内  
      */
     public static final String STATIC_PARAM_KEY1="1";
     /**
      * 24_合并住院是否考虑同一事故
      */
     public static final String STATIC_PARAM_KEY24="24";
     /**
      * 31_前后门急诊考虑同一事故
      */
     public static final String STATIC_PARAM_KEY31="31";
     /**
      * 32_是否因同一疾病产生的住院费用
      */
     public static final String STATIC_PARAM_KEY32="32";
     /**
      * 29_前后门急诊考虑同一医院
      */
     public static final String STATIC_PARAM_KEY29="29";
     /**
      * 11_保险期间届满时治疗最长至事故日起第多少日止
      */
     public static final String STATIC_PARAM_KEY11="11";
     /**
      * 6_既往赔付关联责任代码
      */
     public static final String STATIC_PARAM_KEY6="6";
     /**
      * 7_既往赔付金额关联责任代码 
      */
     public static final String STATIC_PARAM_KEY7="7";
     /**
      * 9_累加器费用计算区间 
      */
     public static final String STATIC_PARAM_KEY9="9";
     /**
      * 13_每次住院期间前后各多少天内
      */
     public static final String STATIC_PARAM_KEY13="13";
     /**
      * 12_每次住院不超过多少元 
      */
     public static final String STATIC_PARAM_KEY12="12";
     /**
      * 14_此责任赔付不参与到所定义的累加器中进行累计
      */
     public static final String STATIC_PARAM_KEY14="14";
     /**
      *15_意外伤害延续至本合同到期日后多少日内的治疗 
      */
     public static final String STATIC_PARAM_KEY15="15";
     /**
      * 16_保险期间届满时住院治疗最长至事故日起第多少日止
      */
     public static final String STATIC_PARAM_KEY16="16";
     /**
      * 17_保险期间届满时门诊治疗最长至事故日起第多少日止
      */
     public static final String STATIC_PARAM_KEY17="17";
     /**
      * 18_是否为津贴责任
      */
     public static final String STATIC_PARAM_KEY18="18";
     /**
      * 22_分次给付后保单状态
      */
     public static final String STATIC_PARAM_KEY22="22";
     /**
      * 23_分次给付后保单状态
      */
     public static final String STATIC_PARAM_KEY23="23";
     /**
      * 25_本险种是否癌症确诊金已赔付
      */
     public static final String STATIC_PARAM_KEY25="25";
     /**
      * 26_每次住院期间前多少天内
      */
     public static final String STATIC_PARAM_KEY26="26";
     /**
      * 27_每次住院期间后多少天内
      */
     public static final String STATIC_PARAM_KEY27="27";
     /**
      * 30_本险种是否恶性肿瘤确诊金已赔付
      */
     public static final String STATIC_PARAM_KEY30="30";
     /**
      * 6_产品保障期
      */
     public static final String STATIC_PARAM_KEY3_VALUE6="6";
     /**
      * 5_单个保单年度
      */
     public static final String STATIC_PARAM_KEY3_VALUE5="5";
     /**
      *  11_门诊
      */
     public static final String STATIC_PARAM_KEY8_VALUE11="11";
     /**
      * 10_住院
      */
     public static final String STATIC_PARAM_KEY8_VALUE10="10";
     /**
      * 12_住院和门诊
      */
     public static final String STATIC_PARAM_KEY8_VALUE12="12";
     /**
      * 2_单个住院期间
      */
     public static final String STATIC_PARAM_KEY9_VALUE2="2";
     /**
      * 3_单次门诊
      */
     public static final String STATIC_PARAM_KEY9_VALUE3="3";
     /**
      * 2_单个住院期间累计
      */
     public static final String CONDITION_TYPE2="2";
     /**
      * 3_单次门诊累计
      */
     public static final String CONDITION_TYPE3="3";
     /**
      * 1_单个事故内累计
      */
     public static final String CONDITION_TYPE1="1";
     /**
      * 5_单个保单年度内累计
      */
     public static final String CONDITION_TYPE5="5";
     /**
      * 6_产品保障期内累计
      */
     public static final String CONDITION_TYPE6="6";
     /**
      * 7_生效日后365天的倍数累计
      */
     public static final String CONDITION_TYPE7="7";
     /**
      * 公式参数-医疗费用
      */
     public static final String PARAM_ID70="70";
     /**
      * 公式参数-住院杂项费及手术费
      */
     public static final String PARAM_ID86="86";
     /**
      * 公式参数-合理医疗费
      */
     public static final String PARAM_ID59="59";
     /**
      * 公式参数-住院天数
      */
     public static final String PARAM_ID69="69";
     /**
      * 公式参数-本责任既往免赔额
      */
     public static final String PARAM_ID145="145";
     /**
      * 公式参数-社保支付
      */
     public static final String PARAM_ID60="60";
     /**
      * 公式参数-第三方支付
      */
     public static final String PARAM_ID58="58";
     /**
      * 公式参数-单保单年度内本责任账单总和(540/541用)
      */
     public static final String PARAM_ID151="151";
     /**
      * 公式参数-本责任对应的自费医疗费用
      */
     public static final String PARAM_ID154="154";
     /**
      * 公式参数-住院自费医疗费用
      */
     public static final String PARAM_ID155="155";
     /**
      * 公式参数-其它途径给付的住院自费医疗费用
      */
     public static final String PARAM_ID156="156";
     /**
      * 公式参数-本次住院或门诊是否已赔付社保或第三方
      */
     public static final String PARAM_ID169="169";
	  /**
      * 公式参数-单保单年度内本责任社保第三方总和(540/541用)
      */
     public static final String PARAM_ID175="175";
     /**
      * 公式参数-癌症实际住院天数
      */
     public static final String PARAM_ID174="174";
     /**
      * 公式参数-三方给付项下给付类型（除2-农合支付,9城镇居民）的金额
      */
     public static final String PARAM_ID165="165";
     /**
      * 参数分类：时间段
      */
     public static final String PARA_TYPE_SIX = "06";
     /**
      * 邮件发送者地址
      */
     public static final String SENDER_ADDRESS = "<EMAIL>";
     /**
      * 险种销售渠道：个人营销
      */
     public static final String SALES_CHANNEL_ONE = "01";
     /**
      * 险种销售渠道：团体渠道
      */
     public static final String SALES_CHANNEL_TWO = "02";
     /**
      * 险种销售渠道：银行代理
      */
     public static final String SALES_CHANNEL_THREE = "03";
     /**
      * 险种销售渠道：收费员渠道
      */
     public static final String SALES_CHANNEL_FOUR= "04";
     /**
      * 险种销售渠道：电话直销
      */
     public static final String SALES_CHANNEL_FIVE= "05";
     /**
      * 险种销售渠道：银代续期
      */
     public static final String SALES_CHANNEL_SIX= "06";
     /**
      * 险种销售渠道：续期督导
      */
     public static final String SALES_CHANNEL_SEVEN= "07";
     /**
      * 险种销售渠道：财富管理
      */
     public static final String SALES_CHANNEL_EIGHT= "08";
     /**
      * 险种销售渠道：至尊理财
      */
     public static final String SALES_CHANNEL_NINE= "09";
     /**
      * 险种销售渠道：网站销售
      */
     public static final String SALES_CHANNEL_TEN= "10";
     /**
      * 险种销售渠道：柜面直销
      */
     public static final String SALES_CHANNEL_ELEVEN= "11";
     /**
      * 险种销售渠道：客户管理
      */
     public static final String SALES_CHANNEL_TWELVE= "12";
     /**
      * 续保和保证续保标识：不可续保
      */
     public static final String RENEW_OPTION_ZERO= "0";
     /**
      * 续保和保证续保标识：可续保（不可保证续保）
      */
     public static final String RENEW_OPTION_ONE= "1";
     /**
      * 续保和保证续保标识：可保证续保
      */
     public static final String RENEW_OPTION_TWO= "2";
     
     /**
      * 报案方式：柜台报案——3
      */
     public static final BigDecimal REPORT_MODE_COUNTER = new BigDecimal(3);
     
     /**
      * 理赔阶段代码：报案——01
      */
     public static final String CLAIM_STAGE_REPORT = "01";
     /**
      * 理赔阶段代码：签收——02
      */
     public static final String CLAIM_STAGE_SIGN = "02";

     /**
      * 阶段代码，1：为报案阶段
      */
     public static final String REPORT_ONE = "01";
     
     /**
      * 是否扣减欠缴费用标识，0：否
      */
     public static final String IS_DEDUCT_ZERRO = "0";
     
     /**
      * 是否扣减欠缴费用标识，1：是
      */
     public static final String IS_DEDUCT_ONE = "1";
     
     
     public static final BigDecimal SURVEY_USER = new BigDecimal("11111111");
     /**
      * 二级管理机构
      */
     public static final String SECOND_ORGAN_GRADE = "02";
     /**
      * 保单终止原因为：超限终止
      */
     public static final String END_CASE_FOURTEEN = "14"; 

     /**
      * 批单-合同处理批注
      */
     public static final String REVOICE_CONTRACT_SIXTEEN = "CLM_00016";
     
     /**
      * 批单-保费豁免批注
      */
     public static final String REVOICE_CONTRACT_SEVEENTEEN = "CLM_00017";
     
     /**
      * 批单-理赔给付批注
      */
     public static final String REVOICE_CONTRACT_EIGHTTEEN = "CLM_00018";
     
     /**
      * 批单-预付保险金给付批注
      */
     public static final String REVOICE_CONTRACT_FIVETEEN = "CLM_00015";
     
     /**
      * 通知书类型 批单-合同处理批注
      */
     public static final String REVOICE_CONTRACT_SIXTEEN_NAME = "批单-合同处理批注";
     
     /**
      * 通知书类型 批单-保费豁免批注
      */
     public static final String REVOICE_CONTRACT_SEVEENTEEN_NAME = "批单-保费豁免批注";
     
     /**
      * 通知书类型 批单-理赔给付批注
      */
     public static final String REVOICE_CONTRACT_EIGHTTEEN_NAME = " 批单-理赔给付批注";
     
     /**
      * 通知书类型 批单-预付保险金给付批注
      */
     public static final String REVOICE_CONTRACT_FIVETEEN_NAME = "批单-预付保险金给付批注";
     
     /**
      * 高风险客户
      */
     public static final String CUSTOMER_RISK_LEVEL = "A";
     //风险测评问卷打印
     public static final String CLM_ZERRO_THIRTEEN = "CLM_00013";
     
     //身故注销人员延期验真类型
     public static final String CLM_LOGOUT_VERIFY_TYPE = "2";
     
     //身故注销人员延期验真返回状态
     public static final String CLM_LOGOUT_VERIFY_STATUS = "注销状态";
     
     //身故注销人员延期验真结果
     public static final String CLM_LOGOUT_VERIFY_RESULT0 = "已注销";
     
     //身故注销人员延期验真结果
     public static final String CLM_LOGOUT_VERIFY_RESULT1 = "未注销";
     
     //身故注销人员延期验真人工核实代码
     public static final String CLM_LOGOUT_VERIFY_CHECKUSER = "系统批处理";
     
     //身故注销人员延期验真人工核实结果，表示系统批处理
     public static final String CLM_LOGOUT_VERIFY_CHECKRESULT3 = "3";
     
     //身故注销人员延期校验清单
     public static final String CLM_LOGOUT_VERIFY_LIST = "延期校验清单";
     
     //理赔黑名单的权限比较
     public static final int ZERRO_SEVEN_BLACK = 07;
     //审批权限截取4
     public static final int BLACK_FOUR = 4;
     //审批权限截取6
     public static final int BLACK_SIX = 6;
     //身故注销人员延期校验清单
     public static final BigDecimal CLAIM_BLACK_FLAG = new BigDecimal(1);
   //审批疑难案件权限1
     public static final String CLM_AC_ONE = "CLM-AC01";
     //审批疑难案件权限2
     public static final String CLM_AC_TWO = "CLM-AC02";
     //审批疑难案件权限3
     public static final String CLM_AC_THREE = "CLM-AC03";
     //审批疑难案件权限4
     public static final String CLM_AC_FOUR = "CLM-AC04";
     //审批疑难案件权限5
     public static final String CLM_AC_FIVE = "CLM-AC05";
     //审批疑难案件权限6
     public static final String CLM_AC_SIX = "CLM-AC06";
     //审批疑难案件权限7
     public static final String CLM_AC_SEVEN = "CLM-AC07";
     //审批疑难案件权限8
     public static final String CLM_AC_EIGHT = "CLM-AC08";
     //审批疑难案件权限9
     public static final String CLM_AC_NINE = "CLM-AC09";
     //审批疑难案件权限10
     public static final String CLM_AC_TEN = "CLM-AC010";
     /*//疑难案件金额判断-0。
     public static final BigDecimal APPROVE_PERMISSION_ZERRO = new BigDecimal(0);
     //疑难案件金额判断-5。
     public static final BigDecimal APPROVE_PERMISSION_FIVE = new BigDecimal(50000);
     //疑难案件金额判断-10。
     public static final BigDecimal APPROVE_PERMISSION_TEN = new BigDecimal(100000);
     //疑难案件金额判断-15。
     public static final BigDecimal APPROVE_PERMISSION_FIFTEEN = new BigDecimal(150000);
     //疑难案件金额判断-20。
     public static final BigDecimal APPROVE_PERMISSION_TWENTY = new BigDecimal(200000);
     //疑难案件金额判断-30。
     public static final BigDecimal APPROVE_PERMISSION_THIRTY = new BigDecimal(300000);
     //疑难案件金额判断-50。
     public static final BigDecimal APPROVE_PERMISSION_FIFTY = new BigDecimal(500000);
     //疑难案件金额判断-80。
     public static final BigDecimal APPROVE_PERMISSION_EIGHTY = new BigDecimal(800000);
     //疑难案件金额判断-100。
     public static final BigDecimal APPROVE_PERMISSION_ONE_HUNDRED = new BigDecimal(1000000);
     //疑难案件金额判断-200。
     public static final BigDecimal APPROVE_PERMISSION_TWO_HUNDRED = new BigDecimal(2000000);*/
     //审批结论 - 不通过（2）
     public static final BigDecimal APPROVE_DECISION_CODE_TWO = new BigDecimal(2);
     
     /**
      * 递交渠道 - 续保转投
      */
     public static final BigDecimal SUBMIT_CHANNEL_NINE = new BigDecimal("9");

     /**
     *  案件状态转换
     * @param  NewcaseStatus 新核心案件状态
     * @return  老核心案件状态
     */
    public static String caseStatusMarray(String newcaseStatus){
        String oldCaseStatus="";
        if(StringUtils.isNotBlank(newcaseStatus)){
            //判断理赔类型
            if("10".equals(newcaseStatus)){ 
                oldCaseStatus = "10";//报案
            }else if("20".equals(newcaseStatus)||"21".equals(newcaseStatus)){
                oldCaseStatus = "15";//受理
            }else if("30".equals(newcaseStatus)||"31".equals(newcaseStatus)){
                oldCaseStatus = "20";//立案
            }else if("60".equals(newcaseStatus)||"61".equals(newcaseStatus)){
                oldCaseStatus = "30";//审核
            }else if("70".equals(newcaseStatus)||"71".equals(newcaseStatus)){
                oldCaseStatus = "40";//审批
            }else if("80".equals(newcaseStatus)){
                oldCaseStatus = "50";//结案
            }else if("90".equals(newcaseStatus)){
                oldCaseStatus = "70";//关闭
            }
            return oldCaseStatus;
        }else{
            return "";
        }
    }
    
    /**
     *  案件标识
     * @param  NewcaseFlag 新核心案件状态
     * @return  老核心案件状态
     */
    public static String caseFlagMarray(String newcaseFlag){
        String oldCaseFlag="";
        if(StringUtils.isNotBlank(newcaseFlag)){
            //判断理赔类型
            if("1".equals(newcaseFlag)){ 
                oldCaseFlag = "11";//普通案件
            }else if("2".equals(newcaseFlag)){
                oldCaseFlag = "12";//诉讼案件
            }else if("3".equals(newcaseFlag)){
                oldCaseFlag = "14";//疑难案件
            }else if("4".equals(newcaseFlag)){
                oldCaseFlag = "01";//简易案件
            }else if("5".equals(newcaseFlag)){
                oldCaseFlag = "13";//申诉案件 
            }else if("6".equals(newcaseFlag)){
                oldCaseFlag = "11";//单人审核案件
            } 
            return oldCaseFlag;
        }else{
            return "";
        }
    }
    
    // 寿险
    public static final String PRODUCT_CATEGORY_30001 = "30001";
    // 健康险
    public static final String PRODUCT_CATEGORY_30003 = "30003";
    
    public static final String PRODUCT_CATEGORY_30004 = "30004";
    
    
    //----------收付费费用类型-------
  
  //退保费用
    public static final String FEE_CODE_G005290000 = "G005290000";
  //预付款应收
    public static final String FEE_CODE_G005140200 = "G005140200";
  //退还账户价值（不扣除退保费用）
    public static final String FEE_CODE_P005260000 = "P005260000";
  //扣回自垫保费（无账户价值）
    public static final String FEE_CODE_G005090300 = "G005090300";
  //扣回自垫保费（账户价值）
    public static final String FEE_CODE_G005090100 = "G005090100";
  //扣回自垫保费（超账户价值）
    public static final String FEE_CODE_G005090200 = "G005090200";
  //扣回自垫利息（无账户价值）
    public static final String FEE_CODE_G005100300 = "G005100300";
  //扣回自垫利息（账户价值）
    public static final String FEE_CODE_G005100100 = "G005100100";
  //扣回自垫利息（超账户价值）
    public static final String FEE_CODE_G005100200 = "G005100200";
  //扣回保单贷款本金（无账户价值）
    public static final String FEE_CODE_G005070300 = "G005070300";
  //扣回保单贷款本金（账户价值）
    public static final String FEE_CODE_G005070100 = "G005070100";
  //扣回保单贷款本金（超账户价值）
    public static final String FEE_CODE_G005070200 = "G005070200";
  //扣回保单贷款利息（无账户价值）
    public static final String FEE_CODE_G005080300 = "G005080300";
  //扣回保单贷款利息（账户价值）
    public static final String FEE_CODE_G005080100 = "G005080100";
  //扣回保单贷款利息（超账户价值）
    public static final String FEE_CODE_G005080200 = "G005080200";
  //扣回欠缴保费
    public static final String FEE_CODE_G005060000 = "G005060000";
  //扣回欠缴保费(账户价值)
    public static final String FEE_CODE_G005060100 = "G005060100";
  //扣回欠缴保费（超账户价值）
    public static final String FEE_CODE_G005060200 = "G005060200";
  //退还多交保费（续期）
    public static final String FEE_CODE_P005160200 = "P005160200";
  //理赔金（终了红利）
    public static final String FEE_CODE_P005020200 = "P005020200";
  //利差本金
    public static final String FEE_CODE_P005050000 = "P005050000";
  //风险保费（首期）
    public static final String FEE_CODE_T003140100 = "T003140100";
  //风险保费（续期）
    public static final String FEE_CODE_T003140200 = "T003140200";
  //保单管理费
    public static final String FEE_CODE_T003040000 = "T003040000";
  //保证年金一次性领取
    public static final String FEE_CODE_P005120000 = "P005120000";
  //理赔追回年金（无账户价值)
    public static final String FEE_CODE_G005380300 = "G005380300";
  //理赔追回年金（账户价值）
    public static final String FEE_CODE_G005380100 = "G005380100";
  //理赔追回年金（超账户价值）
    public static final String FEE_CODE_G005380200 = "G005380200";
  //理赔追回年金部分  改为已实付
    public static final String FEE_CODE_P005470200 = "P005470200";
    //理赔追回年金 已实付
    public static final String FEE_CODE_P005510000 = "P005510000";
  //理赔追回满期金部分
    public static final String FEE_CODE_P005480200 = "P005480200";
  //理赔退费（首期）
    public static final String FEE_CODE_P005180100 = "P005180100";
  //理赔退费（续期）
    public static final String FEE_CODE_P005180200 = "P005180200";
  //理赔退费（首期账户价值）
    public static final String FEE_CODE_P005190100 = "P005190100";
  //理赔退费（续期账户价值）
    public static final String FEE_CODE_P005190200 = "P005190200";
  //理赔退费（首期初始费用）-基本保费
    public static final String FEE_CODE_P005410100 = "P005410100";
  //理赔退费（首期初始费用）-额外保费
    public static final String FEE_CODE_P005410200 = "P005410200";
  //理赔退费（首期初始费用）-追加保费
    public static final String FEE_CODE_P005410300 = "P005410300";
  //理赔退费（续期初始费用）-基本保费
    public static final String FEE_CODE_P005420100 = "P005420100";
  //理赔退费（续期初始费用）-额外保费
    public static final String FEE_CODE_P005420200 = "P005420200";
  //理赔退费（续期初始费用）-追加保费
    public static final String FEE_CODE_P005420300 = "P005420300";
  //退回未满期保费（首期）
    public static final String FEE_CODE_P005170100 = "P005170100";
  //退回未满期保费（续期）
    public static final String FEE_CODE_P005170200 = "P005170200";
  //现金红利
    public static final String FEE_CODE_P004620000 = "P004620000";
  //补发红利利息
    public static final String FEE_CODE_P004630000 = "P004630000";
  //扣除累积生息帐户
    public static final String FEE_CODE_P005470300 = "P005470300";
  //理赔追回年金部分首期(从累积生息账户扣减的金额)
    public static final String FEE_CODE_P005490100 = "P005490100";
  //理赔追回年金部分续期(从累积生息账户扣减的金额)
    public static final String FEE_CODE_P005490200 = "P005490200";
  //理赔金（账户价值）
    public static final String FEE_CODE_P005030100 = "P005030100";
  //资产管理费
    public static final String FEE_CODE_T003060000 = "T003060000";
  //理赔金总额
    public static final String FEE_CODE_P005040000 = "P005040000";
  //理赔金（无账户价值）
    public static final String FEE_CODE_P005010000 = "P005010000";
    
  //理赔金（超账户价值）
    public static final String FEE_CODE_P005030200 = "P005030200";
  //预付赔款
    public static final String FEE_CODE_P005140100 = "P005140100";
  //延期给付利息
    public static final String FEE_CODE_P005110000 = "P005110000";
  //理赔二核加费
    public static final String FEE_CODE_G005130000 = "G005130000";
    
    
    //--------------20180312 增加------------
  //扣回保单贷款利息增值税（无账户价值）
    public static final String FEE_CODE_G005080301 = "G005080301";
  //扣回自垫利息增值税（超账户价值）
    public static final String FEE_CODE_G005100201 = "G005100201";
  //理赔退保（扣除退保费用后的待支付的部分）
    public static final String FEE_CODE_P005280000 = "P005280000";
  //扣回保单贷款利息增值税（超账户价值）
    public static final String FEE_CODE_G005080201 = "G005080201";
  //扣回保单贷款利息增值税（账户价值）
    public static final String FEE_CODE_G005080101 = "G005080101";
  //扣回自垫利息增值税（无账户价值）
    public static final String FEE_CODE_G005100301 = "G005100301";
    //账户管理费用
    public static final String FEE_CODE_T003070000 = "T003070000";
    
  //扣回自垫利息增值税（账户价值）
    public static final String FEE_CODE_G005100101 = "G005100101";
  //理赔退保
    public static final String FEE_CODE_P005150000 = "P005150000";
    //理赔追回年金部分(未实付) 
    public static final String FEE_CODE_P005470100 = "P005470100";
      
    public static final String FEE_CODE_P005390200 = "P005390200";
    
    //退还初始扣费（续期）-基本保费 02  付费  
    public static final String FEE_CODE_P005460100 = "P005460100";
    //退还初始扣费（续期）-额外保费 02  付费  
    public static final String FEE_CODE_P005460200 = " P005460200";
    //退还初始扣费（续期）-追加保费 02  付费  
    public static final String FEE_CODE_P005460300 = "P005460300";
    public static final String FEE_CODE_P005550100 = "P005550100";
    
    
    
    //追回现金红利部分（进账户）
    public static final String FEE_CODE_P005550000 = "P005550000";
    //追回现金红利（已实付）  
    public static final String FEE_CODE_P005530000 = "P005530000";
    //追回补发现金红利利息（已实付 
    public static final String FEE_CODE_P005540000 = "P005540000";
    
    
    /**
     * 简易自核规则状态（通过）
     */
    public static final String CHECK_RULE_PASS_ONE = "V01";
    /**
     * 简易自核规则状态（未通过）
     */
    public static final String CHECK_RULE_PASS_TWO = "V02";
    /**
     * 简易自核规则状态（未通过）
     */
    public static final String CHECK_RULE_PASS_THREE = "V03";
    /**
     * @Fields STATUSCODE_999 : 登录失败，返回登录页面 回退界面确认取消框
     */
    public static final String STATUSCODE_999 = "999";
    /**
     * 重大疾病编码：zj001
     */
    public static final String SERIOUS_DISEASE_ZJ001 = "zj001";
  
    //特约信息产品列表（先写死）
    /**
     * 特约产品：193
     */
    public static final String SPECIAL_PRODUCT_ONE = "193";
    /**
     * 特约产品：507
     */
    public static final String SPECIAL_PRODUCT_TWO = "507";
    /**
     * 特约产品：508
     */
    public static final String SPECIAL_PRODUCT_THREE = "508";
    /**
     * 特约产品：388
     */
    public static final String SPECIAL_PRODUCT_FOUR = "388";
    /**
     * 特约产品：717
     */
    public static final String SPECIAL_PRODUCT_FIVE = "717";
    /**
     * 特约产品：718
     */
    public static final String SPECIAL_PRODUCT_SIX = "718";
    /**
     * 特约产品：918
     */
    public static final String SPECIAL_PRODUCT_SEVEN = "918";
    /**
     * 特约产品：527
     */
    public static final String SPECIAL_PRODUCT_EIGHT = "527";
    /**
     * 特约产品：528
     */
    public static final String SPECIAL_PRODUCT_NINE = "528";
    /**
     * 特约产品：784
     */
    public static final String SPECIAL_PRODUCT_TEN = "784";
    /**
     * 特约产品：390
     */
    public static final String SPECIAL_PRODUCT_ELEVEN = "390";
    /**
     * 特约产品：391
     */
    public static final String SPECIAL_PRODUCT_TWELVE = "391";
    /**
     * 特约产品：183
     */
    public static final String SPECIAL_PRODUCT_THIRTEEN = "183";
    /**
     * 特约产品：538
     */
    public static final String SPECIAL_PRODUCT_FOURTEEN = "538";
    /**
     * 特约产品：425
     */
    public static final String SPECIAL_PRODUCT_FIVTEEN = "425";
    /**
     * 特约产品：557
     */
    public static final String SPECIAL_PRODUCT_SIXTEEN = "557";
    /**
     * 特约产品：558
     */
    public static final String SPECIAL_PRODUCT_SEVENTEEN = "558";
    /**
     * 特约产品：577
     */
    public static final String SPECIAL_PRODUCT_EIGHTEEN = "577";
    /**
     * 特约产品：5631
     */
    public static final String SPECIAL_PRODUCT_NINTEEN = "5631";
    /**
     * 特约产品：389
     */
    public static final String SPECIAL_PRODUCT_TWENTY = "389";
    /**
     * 特约产品：430
     */
    public static final String SPECIAL_PRODUCT_TWENTY_ONE = "430";
    /**
     * 特约产品：924
     */
    public static final String SPECIAL_PRODUCT_TWENTY_TWO = "924";
    
  //--------------20180312------------
    //重大疾病组别1
    public static final List<String> SERIOUS_DISEASE_GROUP1 = new ArrayList<String>();
    static {
    	SERIOUS_DISEASE_GROUP1.add("zj001");
    }
  //重大疾病组别2
    public static final List<String> SERIOUS_DISEASE_GROUP2 = new ArrayList<String>();
    static {
    	SERIOUS_DISEASE_GROUP2.add("zj002");
    	SERIOUS_DISEASE_GROUP2.add("zj005");
    	SERIOUS_DISEASE_GROUP2.add("zj016");
    	SERIOUS_DISEASE_GROUP2.add("zj021");
    	SERIOUS_DISEASE_GROUP2.add("zj025");
    	SERIOUS_DISEASE_GROUP2.add("zj041");
    	SERIOUS_DISEASE_GROUP2.add("zj042");
    	SERIOUS_DISEASE_GROUP2.add("zj049");
    	SERIOUS_DISEASE_GROUP2.add("zj051");
    	SERIOUS_DISEASE_GROUP2.add("zj052");
    	SERIOUS_DISEASE_GROUP2.add("zj053");
    	SERIOUS_DISEASE_GROUP2.add("zj060");
    	SERIOUS_DISEASE_GROUP2.add("zj062");
    	SERIOUS_DISEASE_GROUP2.add("zj071");
    }
    //重大疾病组别3
    public static final List<String> SERIOUS_DISEASE_GROUP3 = new ArrayList<String>();
    static {
    	SERIOUS_DISEASE_GROUP3.add("zj003");
    	SERIOUS_DISEASE_GROUP3.add("zj009");
    	SERIOUS_DISEASE_GROUP3.add("zj011");
    	SERIOUS_DISEASE_GROUP3.add("zj012");
    	SERIOUS_DISEASE_GROUP3.add("zj045");
    	SERIOUS_DISEASE_GROUP3.add("zj017");
    	SERIOUS_DISEASE_GROUP3.add("zj018");
    	SERIOUS_DISEASE_GROUP3.add("zj019");
    	SERIOUS_DISEASE_GROUP3.add("zj022");
    	SERIOUS_DISEASE_GROUP3.add("zj064");
    	SERIOUS_DISEASE_GROUP3.add("zj023");
    	SERIOUS_DISEASE_GROUP3.add("zj026");
    	SERIOUS_DISEASE_GROUP3.add("zj029");
    	SERIOUS_DISEASE_GROUP3.add("zj043");
    	SERIOUS_DISEASE_GROUP3.add("zj044");
    	SERIOUS_DISEASE_GROUP3.add("zj015");
    	SERIOUS_DISEASE_GROUP3.add("zj054");
    	SERIOUS_DISEASE_GROUP3.add("zj072");
    	SERIOUS_DISEASE_GROUP3.add("zj073");
    	SERIOUS_DISEASE_GROUP3.add("zj067");
    	SERIOUS_DISEASE_GROUP3.add("zj066");
    	SERIOUS_DISEASE_GROUP3.add("zj074");
    }
    //重大疾病组别4
    public static final List<String> SERIOUS_DISEASE_GROUP4 = new ArrayList<String>();
    static {
    	SERIOUS_DISEASE_GROUP4.add("zj006");
    	SERIOUS_DISEASE_GROUP4.add("zj007");
    	SERIOUS_DISEASE_GROUP4.add("zj008");
    	SERIOUS_DISEASE_GROUP4.add("zj010");
    	SERIOUS_DISEASE_GROUP4.add("zj014");
    	SERIOUS_DISEASE_GROUP4.add("zj030");
    	SERIOUS_DISEASE_GROUP4.add("zj031");
    	SERIOUS_DISEASE_GROUP4.add("zj061");
    	SERIOUS_DISEASE_GROUP4.add("zj048");
    	SERIOUS_DISEASE_GROUP4.add("zj032");
    	SERIOUS_DISEASE_GROUP4.add("zj024");
    	SERIOUS_DISEASE_GROUP4.add("zj004");
    	SERIOUS_DISEASE_GROUP4.add("zj035");
    	SERIOUS_DISEASE_GROUP4.add("zj046");
    	SERIOUS_DISEASE_GROUP4.add("zj047");
    	SERIOUS_DISEASE_GROUP4.add("zj055");
    	SERIOUS_DISEASE_GROUP4.add("zj058");
    	SERIOUS_DISEASE_GROUP4.add("zj028");
    	SERIOUS_DISEASE_GROUP4.add("zj063");
    	SERIOUS_DISEASE_GROUP4.add("zj065");
    	SERIOUS_DISEASE_GROUP4.add("zj056");
    	SERIOUS_DISEASE_GROUP4.add("zj069");
    	SERIOUS_DISEASE_GROUP4.add("zj059");
    }
    //重大疾病组别5
    public static final List<String> SERIOUS_DISEASE_GROUP5 = new ArrayList<String>();
    static {
    	SERIOUS_DISEASE_GROUP5.add("zj013");
    	SERIOUS_DISEASE_GROUP5.add("zj020");
    	SERIOUS_DISEASE_GROUP5.add("zj057");
    	SERIOUS_DISEASE_GROUP5.add("zj034");
    	SERIOUS_DISEASE_GROUP5.add("zj040");
    	SERIOUS_DISEASE_GROUP5.add("zj050");
    	SERIOUS_DISEASE_GROUP5.add("zj027");
    	SERIOUS_DISEASE_GROUP5.add("zj075");
    	SERIOUS_DISEASE_GROUP5.add("zj068");
    	SERIOUS_DISEASE_GROUP5.add("zj076");
    }

    //----------收付费费用类型-------
    //NP	承保
    public static final String FEE_SCENE_CODE_NP = "NP";
    //2	RN	续期
    public static final String FEE_SCENE_CODE_RN = "RN";
    //3	RW	豁免
    public static final String FEE_SCENE_CODE_RW = "RW";
    //4	RA	自垫
    public static final String FEE_SCENE_CODE_RA = "RA";
    //5	CS	保全
    public static final String FEE_SCENE_CODE_CS = "CS";
    //6	CE	理赔二核加费
    public static final String FEE_SCENE_CODE_CE = "CE";
    //7	CD	理赔扣回欠缴保费
    public static final String FEE_SCENE_CODE_CD = "CD";
    //8	CR	理赔退费
    public static final String FEE_SCENE_CODE_CR = "CR";
    //9	RR	续期回退
    public static final String FEE_SCENE_CODE_RR = "RR";

    //审批不通过记录表 不通过类型 审批
    public static final BigDecimal REJECT_TYPE_APPROVE = new BigDecimal(1);
    //审批不通过记录表 不通过类型 回退案件
    public static final BigDecimal REJECT_TYPE_ROLLBACK = new BigDecimal(2);
    /**
     * 理赔责任匹配参数类型，0-意外细节
     */
    public static final BigDecimal PARAM_TYPE_ZERO = BigDecimal.ZERO;
    /**
     * 理赔责任匹配参数类型，1-特种疾病
     */
    public static final BigDecimal PARAM_TYPE_ONE = new BigDecimal("1");
    /**
     * 理赔责任匹配参数类型，2-特定手术
     */
    public static final BigDecimal PARAM_TYPE_TWO = new BigDecimal("2");
    /**
     * 理赔责任匹配参数类型，3-特定给付
     */
    public static final BigDecimal PARAM_TYPE_THREE = new BigDecimal("3");
    /**
     * 理赔责任匹配参数类型，4-特种费用类型
     */
    public static final BigDecimal PARAM_TYPE_FOUR = new BigDecimal("4");
    /**
     * 理赔责任匹配参数类型，5-医疗费用类型
     */
    public static final BigDecimal PARAM_TYPE_FIVE = new BigDecimal("5");
    /**
     * 理赔责任匹配参数类型，6-重大疾病
     */
    public static final BigDecimal PARAM_TYPE_SIX = new BigDecimal("6");
    /**
     * 理赔责任匹配参数类型，7-出险结果
     */
    public static final BigDecimal PARAM_TYPE_SEVEN = new BigDecimal("7");
    /**
     * 业务公共黑名单检验-冻结
     */
    public static final String BLACK_FLAG_FREEZE = "3";
    
    /**
     * 业务公共黑名单检验-疑似
     */
    public static final String BLACK_FLAG_DOUBTFUL = "1";
    
    /**
     * 环境mode
     */
    public static final String ENVIRONMEN_MODE_ONE = "PE";
    /**
     * 重复账单号标识 “是”
     */
    public static final String  REPEAT_NUMBER_FLAG= "1";
    public static void main(String args[]){
        BigDecimal bigde=new BigDecimal(2323);
        BigDecimal bigdeCopy=bigde;
        
        bigde=bigde.subtract(new BigDecimal(20));
        System.out.println(bigde);
        System.out.println(bigdeCopy);
        
    }
    /**
     * 查看风险保额-风险类型 -16_公共交通意外险
     */
    public static final String RISK_TYPE_SIXTEEN = "16";
    /**
     * 查看风险保额-风险类型 -17_航空意外险
     */
    public static final String RISK_TYPE_SEVENTEEN = "17";
    
    /**
     * 查询该豁免责任是投保人豁免还是被保人豁免-被保人
     */
    public static final String INSURED = "被保人";
    
    /**
     * 查询该豁免责任是投保人豁免还是被保人豁免-投保人
     */
    public static final String HOLDER = "投保人";
    
    /**
     * 用户名AUTO
     */
    public static final String AUTO = "AUTO";
    /**
     * 发起过补充单证
     */
    public static final BigDecimal SUP_FLAG_YES =BigDecimal.valueOf(1) ;
    /**
     * 总公司机构86
     */
    public static final String ZONG_ORG_CODE =  "86";
    /**
     * 超期原因---2-
     */
    public static final String OVER_REASON_CUS = "2";
    /**
     * 理算特殊处理的产品编码
     */
    public static final String CLM_BUSI_PRODCODE_NINEEIGHTFOUR = "00984000";
}
