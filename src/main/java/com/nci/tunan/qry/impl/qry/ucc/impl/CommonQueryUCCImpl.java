package com.nci.tunan.qry.impl.qry.ucc.impl;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.nci.udmp.util.xml.transform.XmlHelper;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.git.common.string.StringUtil;
import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.interfaces.model.bo.SurveyConclusionBO;
import com.nci.core.common.interfaces.model.bo.SurveyQualityEvaluateBO;
import com.nci.core.common.interfaces.vo.SurveyApplyVO;
import com.nci.tunan.qry.common.Constants;
import com.nci.tunan.qry.common.DictionaryUtil;
import com.nci.tunan.qry.common.TransferUtil;
import com.nci.tunan.qry.dao.IQryCommonQueryDao;
import com.nci.tunan.qry.dao.IQryCustomerDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractBusiProdDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractMasterDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractProductDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IInsuredListDao;
import com.nci.tunan.qry.impl.nb.service.INbEvaluateAnswerService;
import com.nci.tunan.qry.impl.qry.service.ICommonQueryService;
import com.nci.tunan.qry.impl.qry.service.IHolderInsuredBeneCustCompService;
import com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC;
import com.nci.tunan.qry.impl.util.UwDocumentNO;
import com.nci.tunan.qry.impl.uw.service.IPenoticeDetailService;
import com.nci.tunan.qry.impl.uw.service.IPenoticeService;
import com.nci.tunan.qry.impl.uw.service.ISurveyApplyService;
import com.nci.tunan.qry.impl.uw.service.IUWAskforinfoDetailService;
import com.nci.tunan.qry.impl.uw.service.IUWAskforinfoService;
import com.nci.tunan.qry.interfaces.model.bo.AgentBO;
import com.nci.tunan.qry.interfaces.model.bo.AgentCompBO;
import com.nci.tunan.qry.interfaces.model.bo.BusiProdInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.CsSpecialAccountInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.CustomerAccouontBO;
import com.nci.tunan.qry.interfaces.model.bo.CustomerBO;
import com.nci.tunan.qry.interfaces.model.bo.DocumentBO;
import com.nci.tunan.qry.interfaces.model.bo.IdentityCheckInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.PolicyRiskQuestionnaireBO;
import com.nci.tunan.qry.interfaces.model.bo.PolicyStatusBO;
import com.nci.tunan.qry.interfaces.model.bo.QryAccountBO;
import com.nci.tunan.qry.interfaces.model.bo.QryBeneCustomerCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryBusiItemInfoCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryClmQueryBO;
import com.nci.tunan.qry.interfaces.model.bo.QryCommonPolicyInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.QryCommonQueryCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryContractMasterCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryCustomerBO;
import com.nci.tunan.qry.interfaces.model.bo.QryDocumentBO;
import com.nci.tunan.qry.interfaces.model.bo.QryHolderCustomerCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryHolderInsuredCustCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryInsuredCustomerCompBO;
import com.nci.tunan.qry.interfaces.model.bo.QryNbDataInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.QryNotifyInfoDetailBO;
import com.nci.tunan.qry.interfaces.model.bo.QryNotifyInfoListBO;
import com.nci.tunan.qry.interfaces.model.bo.QryNotifyInfoLogBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPayPlanDueBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPolicyAccountBO;
import com.nci.tunan.qry.interfaces.model.bo.QryPremArapBO;
import com.nci.tunan.qry.interfaces.model.bo.QryProposalProcessBO;
import com.nci.tunan.qry.interfaces.model.bo.QryReissuedPrintBO;
import com.nci.tunan.qry.interfaces.model.bo.QryUwBusiProdBO;
import com.nci.tunan.qry.interfaces.model.bo.QueryCustomerPhoneChangePathBO;
import com.nci.tunan.qry.interfaces.model.bo.QueryPolicyBonusBO;
import com.nci.tunan.qry.interfaces.model.bo.QueryPolicyPasswordPathBO;
import com.nci.tunan.qry.interfaces.model.bo.SecondPolicyHolderBO;
import com.nci.tunan.qry.interfaces.model.bo.ServiceBO;
import com.nci.tunan.qry.interfaces.model.bo.SurvInvCfmInfoBO;
import com.nci.tunan.qry.interfaces.model.bo.TrustCompanyBO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractMasterPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractProductPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.InsuredListPO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbContractBusiProdBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbContractMasterBO;
import com.nci.tunan.qry.interfaces.model.nb.bo.NbEvaluateAnswerBO;
import com.nci.tunan.qry.interfaces.model.nb.po.NbContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.nb.vo.CustomerVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.NbContractBeneVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.NbContractBusiProdVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.NbContractCustomerHisVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.NbContractMasterVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.NbEvaluateAnswerVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.NbInsuredListVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.NbPayerAccountVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.NbPolicyHolderVO;
import com.nci.tunan.qry.interfaces.model.nb.vo.PolicyRiskQuestionnaireVO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsAcceptChangeBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsApplicationBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsInfoQueryAcceptBO;
import com.nci.tunan.qry.interfaces.model.pa.bo.CsInfoQueryBackMoneyBO;
import com.nci.tunan.qry.interfaces.model.pa.po.CsPolicyInfoChangeThreeaPO;
import com.nci.tunan.qry.interfaces.model.pa.po.CsPolicyInfoChangeThreebPO;
import com.nci.tunan.qry.interfaces.model.pa.vo.CsAcceptChangeVO;
import com.nci.tunan.qry.interfaces.model.pa.vo.CsApplicationVO;
import com.nci.tunan.qry.interfaces.model.pa.vo.CsInfoQueryAcceptVO;
import com.nci.tunan.qry.interfaces.model.pa.vo.CsInfoQueryBackMoneyVO;
import com.nci.tunan.qry.interfaces.model.pa.vo.CsPolicyInfoChangeThreeaVO;
import com.nci.tunan.qry.interfaces.model.pa.vo.CsPolicyInfoChangeThreebVO;
import com.nci.tunan.qry.interfaces.model.po.CsSpecialAccountInfoPO;
import com.nci.tunan.qry.interfaces.model.po.QryCustomerPO;
import com.nci.tunan.qry.interfaces.model.po.TrustCompanyBenePO;
import com.nci.tunan.qry.interfaces.model.uw.bo.AskforinfoBO;
import com.nci.tunan.qry.interfaces.model.uw.bo.AskforinfoDetailBO;
import com.nci.tunan.qry.interfaces.model.uw.bo.HcMedicalBO;
import com.nci.tunan.qry.interfaces.model.uw.bo.PenoticeBO;
import com.nci.tunan.qry.interfaces.model.uw.bo.PenoticeDetailBO;
import com.nci.tunan.qry.interfaces.model.uw.bo.ResidentInfoBO;
import com.nci.tunan.qry.interfaces.model.uw.bo.ResidentInfoDetailBO;
import com.nci.tunan.qry.interfaces.model.uw.bo.SurveyApplyBO;
import com.nci.tunan.qry.interfaces.model.uw.bo.UwDocumentVerifyBO;
import com.nci.tunan.qry.interfaces.model.vo.AgentVO;
import com.nci.tunan.qry.interfaces.model.vo.BeneCustomerVO;
import com.nci.tunan.qry.interfaces.model.vo.BusiProdInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.CsSpecialAccountInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.CustomerAccountVO;
import com.nci.tunan.qry.interfaces.model.vo.DocumentVO;
import com.nci.tunan.qry.interfaces.model.vo.IdentityCheckInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.PolicyStatusVO;
import com.nci.tunan.qry.interfaces.model.vo.QryAccountVO;
import com.nci.tunan.qry.interfaces.model.vo.QryBusiItemInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.QryClmQueryVO;
import com.nci.tunan.qry.interfaces.model.vo.QryCommonPolicyInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.QryCommonQueryVO;
import com.nci.tunan.qry.interfaces.model.vo.QryCustomerInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.QryCustomerVO;
import com.nci.tunan.qry.interfaces.model.vo.QryDocumentVO;
import com.nci.tunan.qry.interfaces.model.vo.QryHolderInsuredCustCompVO;
import com.nci.tunan.qry.interfaces.model.vo.QryNBPolicyCommVO;
import com.nci.tunan.qry.interfaces.model.vo.QryNbDataInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.QryNotifyInfoDetailVO;
import com.nci.tunan.qry.interfaces.model.vo.QryNotifyInfoListVO;
import com.nci.tunan.qry.interfaces.model.vo.QryNotifyInfoLogVO;
import com.nci.tunan.qry.interfaces.model.vo.QryPayPlanDueVO;
import com.nci.tunan.qry.interfaces.model.vo.QryPolicyAccountVO;
import com.nci.tunan.qry.interfaces.model.vo.QryPolicyInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.QryPremArapVO;
import com.nci.tunan.qry.interfaces.model.vo.QryProposalProcessVO;
import com.nci.tunan.qry.interfaces.model.vo.QryReissuedPrintVO;
import com.nci.tunan.qry.interfaces.model.vo.QryUwBusiProdVO;
import com.nci.tunan.qry.interfaces.model.vo.QueryCustomerPhoneChangePathVO;
import com.nci.tunan.qry.interfaces.model.vo.QueryPolicyBonusVO;
import com.nci.tunan.qry.interfaces.model.vo.QueryPolicyPasswordPathVO;
import com.nci.tunan.qry.interfaces.model.vo.SecondPolicyHolderVO;
import com.nci.tunan.qry.interfaces.model.vo.ServiceVO;
import com.nci.tunan.qry.interfaces.model.vo.SurvInvCfmInfoVO;
import com.nci.tunan.qry.interfaces.model.vo.TrustCompanyBeneVO;
import com.nci.tunan.qry.interfaces.model.vo.TrustCompanyVO;
import com.nci.tunan.qry.util.CustomerRightsQualEnum;
import com.nci.udmp.component.aop.TablePrevThreadLocal;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.signature.IUCC;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.StringUtilsEx;

/**
 * @description 综合查询Ucc实现类
 * <AUTHOR> <EMAIL>
 * @date 2015-1-22 下午5:15:12
 */
public class CommonQueryUCCImpl implements ICommonQueryUCC, IUCC { 

    private ICommonQueryService commonQueryService;
    private IHolderInsuredBeneCustCompService holderInsuredBeneCustCompService;

    // private INBService nbIAS;

    /**
	 * 调查申请表增删改查操作接口
	 */
	private ISurveyApplyService surveyApplyService;
	/**
	 * 体检处理Service
	 */
	private IPenoticeService iPenoticeService;
	/**
	 * 操作体检信息明细
	 */
	private IPenoticeDetailService penoticeDetailService;
	/**
	 * 操作索要资料与修改事项表的service接口
	 */
	private IUWAskforinfoService uwAskforinfoService;
	/**
	 * 操作索要资料与修改事项细项表的service接口
	 */
	private IUWAskforinfoDetailService uwAskforinfoDetailService;
	/**
	 * 投保单评测问卷问题以及答案记录表
	 */
	private INbEvaluateAnswerService nbEvaluateAnswerService;
	
	public INbEvaluateAnswerService getNbEvaluateAnswerService() {
		return nbEvaluateAnswerService;
	}

	public void setNbEvaluateAnswerService(
			INbEvaluateAnswerService nbEvaluateAnswerService) {
		this.nbEvaluateAnswerService = nbEvaluateAnswerService;
	}
	@Autowired
	@Qualifier("JRQD_PA_ContractMasterDao")
	private IContractMasterDao contractMasterDao;
    @Autowired
	@Qualifier("JRQD_PA_ContractBusiProdDao")
	private IContractBusiProdDao contractBusiProdDao;
	/**
     * 查询险种责任组表Dao接口
     */
    @Autowired
	@Qualifier("JRQD_PA_ContractProductDao")
    private IContractProductDao contractProductDao;
    /**
	 * @Fields insuredListDao : 保单被保人表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_InsuredListDao")
	private IInsuredListDao insuredListDao;
    /**
     * 客户信息查询Dao接口
     */
	@Autowired
	private IQryCustomerDao customerDao;
    /**
     * 
     * @description 根据保单号查询保单信息
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-1-22 下午5:15:38
     * @see com.nci.tunan.pa.impl.commonQuery.ucc.impl.ICsCommonQueryUCC#findPolicyInfo(com.nci.tunan.qry.interfaces.model.qry.vo.QryPolicyInfoVO.interfaces.model.vo.PolicyInfoVO)
     * @param policyInfoVO
     * @return
     */
    public QryPolicyInfoVO findPolicyInfoByCode(QryPolicyInfoVO policyInfoVO) {
        LOGGER.debug("<======CommonQueryUCCImpl--findPolicyInfoByCode======>");
        QryContractMasterCompBO contractMasterCompBO = commonQueryService.findPolicyInfoByCode(BeanUtils
                .copyProperties(QryContractMasterCompBO.class, policyInfoVO));

        return BeanUtils.copyProperties(QryPolicyInfoVO.class, contractMasterCompBO);
    }
    
    /**
     * 获取双主险下的保单信息
     */
    @Override
	public QryPolicyInfoVO findRelationPolicyInfoByCode(
			QryPolicyInfoVO policyInfoVO) {
    	LOGGER.debug("<======CommonQueryUCCImpl--findRelationPolicyInfoByCode======>");
        QryContractMasterCompBO contractMasterCompBO = commonQueryService.findRelationPolicyInfoByCode(BeanUtils
                .copyProperties(QryContractMasterCompBO.class, policyInfoVO));

        return BeanUtils.copyProperties(QryPolicyInfoVO.class, contractMasterCompBO);
	}
    
    /**
     * 根据受理号查询保单信息
     */
    @Override
	public QryPolicyInfoVO findCSAcceptCode(QryPolicyInfoVO qryPolicyInfoVO) {
    	LOGGER.debug("<======CommonQueryUCCImpl--findCSAcceptCode======>");
    	QryContractMasterCompBO contractMasterCompBO = commonQueryService.findCSAcceptCode(BeanUtils
                .copyProperties(QryContractMasterCompBO.class, qryPolicyInfoVO));

        return BeanUtils.copyProperties(QryPolicyInfoVO.class, contractMasterCompBO);
	}
    
    /**
     * 根据赔案号查询理赔信息
     */
	@Override
	public QryClmQueryVO findClmCaseNo(QryClmQueryVO qryClmQueryVO) {
		LOGGER.debug("<======CommonQueryUCCImpl--findClmCaseNo======>");
		QryClmQueryBO qryClmQueryBO = commonQueryService.findClmCaseNo(BeanUtils
                .copyProperties(QryClmQueryBO.class, qryClmQueryVO));

        return BeanUtils.copyProperties(QryClmQueryVO.class, qryClmQueryBO);
	}
    /**
     * 获取保单的简单信息，用与判断保单是否存在
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#findPolicySimpleInfo(com.nci.tunan.qry.interfaces.model.vo.QryPolicyInfoVO)
     * @param policyInfoVO
     * @return
     */
    public QryPolicyInfoVO findPolicySimpleInfo(QryPolicyInfoVO policyInfoVO){
        QryContractMasterCompBO contractMasterCompBO = commonQueryService.findPolicySimpleInfo(BeanUtils
                .copyProperties(QryContractMasterCompBO.class, policyInfoVO));
        return BeanUtils.copyProperties(QryPolicyInfoVO.class, contractMasterCompBO);
    }

    /**
     * @description 查询保单列表
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-1-23 下午6:05:46
     * @see com.com.nci.tunan.qry.impl.qry.ucc.pa.impl.commonQuery.ucc.ICommonQueryUCC#findPolicyInfos(com.nci.tunan.qry.interfaces.model.qry.vo.QryCommonQueryVO.interfaces.model.vo.CommonQueryVO)
     * @param commonQueryVO
     * @return
     * @throws Exception
     */
    public List<QryCommonQueryVO> findPolicyInfos(QryCommonQueryVO commonQueryVO) throws Exception {
        LOGGER.debug("<======CommonQueryUCCImpl--findPolicyInfos======>");
        QryCommonQueryCompBO commonQueryCompBO = new QryCommonQueryCompBO();
        String[] checkedRadios = commonQueryVO.getCheckedRadio();
        for (String checkedRadio : checkedRadios) {
            /*
             * if(checkedRadio.equals(Constants.CHECKEDRADIO_POLICYCODE)){//
             * 保单信息(保单号、投保单号、代理人/网点代码) if(null != commonQueryVO.getPolicyCode()
             * && !commonQueryVO.getPolicyCode().equals("")){// 保单号
             * commonQueryCompBO.setPolicyCode(commonQueryVO.getPolicyCode()); }
             * } else if(checkedRadio.equals(Constants.CHECKEDRADIO_APPLYCODE)){
             * if(null != commonQueryVO.getApplyCode() &&
             * !commonQueryVO.getApplyCode().equals("")){// 投保单号
             * commonQueryCompBO.setApplyCode(commonQueryVO.getApplyCode()); } }
             * else
             */if (checkedRadio.equals(Constants.CHECKEDRADIO_AGENTCODE)) {
                if (null != commonQueryVO.getAgentOrBankBranch() && !commonQueryVO.getAgentOrBankBranch().equals("")) {// 代理人\网点代码
                    commonQueryCompBO.setServiceAgent(commonQueryVO.getAgentOrBankBranch());
                    commonQueryCompBO.setServiceBankBranch(commonQueryVO.getAgentOrBankBranch());
                }
            } else if (checkedRadio.equals(Constants.CHECKEDRADIO_CUSTOMERNAME)) {
                // 客户信息(客户姓名)
                if (null != commonQueryVO.getCustomerVO()) {
                    if (null != commonQueryVO.getCustomerVO().getCustomerId()) {
                        commonQueryCompBO.setCustomerId(commonQueryVO.getCustomerVO().getCustomerId());
                    } else if (null != commonQueryVO.getCustomerVO().getCustomerName()) {
                        QryCustomerBO customerBO = new QryCustomerBO();
                        customerBO.setCustomerName(commonQueryVO.getCustomerVO().getCustomerName());
                        commonQueryCompBO.setCustomerBO(customerBO);
                    }
                }
            } else if (checkedRadio.equals(Constants.CHECKEDRADIO_CUSTOMERCERTICODE)) {
                // 客户信息(客户证件号码)
                if (null != commonQueryVO.getCustomerVO()) {
                    if (null != commonQueryVO.getCustomerVO().getCustomerId()) {
                        commonQueryCompBO.setCustomerId(commonQueryVO.getCustomerVO().getCustomerId());
                    } else if (null != commonQueryVO.getCustomerVO().getCustomerCertiCode()/*
                                                                                            * getCustomerCertiCode
                                                                                            * (
                                                                                            * )
                                                                                            */) {
                        QryCustomerBO customerBO = new QryCustomerBO();
                        customerBO.setCustomerCertiCode/*
                                                        * (customerCertiCode)
                                                        * setCustomerCertiCode
                                                        */(commonQueryVO.getCustomerVO().getCustomerCertiCode()/*
                                                                                                                * getCustomerCertiCode
                                                                                                                * (
                                                                                                                * )
                                                                                                                */);
                        commonQueryCompBO.setCustomerBO(customerBO);
                    } else if (null != commonQueryVO.getCustomerVO().getCustomerBirthday()) {
                        QryCustomerBO customerBO = new QryCustomerBO();
                        customerBO.setCustomerBirthday(commonQueryVO.getCustomerVO().getCustomerBirthday());
                        commonQueryCompBO.setCustomerBO(customerBO);
                    }
                }
            } else if (checkedRadio.equals(Constants.CHECKEDRADIO_CUSTOMERBIRTHDAY)) {
                // 客户信息(客户出生日期)
                if (null != commonQueryVO.getCustomerVO()) {
                    if (null != commonQueryVO.getCustomerVO().getCustomerId()) {
                        commonQueryCompBO.setCustomerId(commonQueryVO.getCustomerVO().getCustomerId());
                    } else if (null != commonQueryVO.getCustomerVO().getCustomerBirthday()) {
                        QryCustomerBO customerBO = new QryCustomerBO();
                        customerBO.setCustomerBirthday(commonQueryVO.getCustomerVO().getCustomerBirthday());
                        commonQueryCompBO.setCustomerBO(customerBO);
                    }
                }
            } else if (checkedRadio.equals(Constants.CHECKEDRADIO_ACCOUNT)) {// 银行转账帐户号
                if (null != commonQueryVO.getAccount() && !commonQueryVO.getAccount().equals("")) {
                    commonQueryCompBO.setAccount(commonQueryVO.getAccount());
                }
            }
        }
        List<QryCommonQueryCompBO> commonQueryCompBOs = commonQueryService.findPolicyInfos(commonQueryCompBO);

        // 调用新契约的接口获取投保单信息
        List<QryCommonQueryVO> commonQueryVOList = new ArrayList<QryCommonQueryVO>();
        // 原调用
        // commonQueryVOList = nbIAS.queryUnsign(commonQueryCompBO);
        // 新调用
        // com.nci.tunan.nb.interfaces.unsign.exports.queryUnsign.vo.InputData
        // inputData = new
        // com.nci.tunan.nb.interfaces.unsign.exports.queryUnsign.vo.InputData();
        // BeanUtils.copyProperties(inputData, commonQueryCompBO);
        // nbIAS.nbinbqueryunsignuccqueryPolicyList(inputData);

        for (QryCommonQueryVO vo : BeanUtils.copyList(QryCommonQueryVO.class, commonQueryCompBOs)) {
            vo.setIsApplyOrPolicy(Constants.ISAPPLYORPOLICY_PA);// 确定数据是保单还是投保单
            commonQueryVOList.add(vo);
        }
        return commonQueryVOList;
    }

    /**
     * @description 根据客户信息查询客户列表
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-2-27 上午9:46:47
     * @param commonQueryVO
     * @return
     */
   public CurrentPage findCustomersByCustomerInfo(QryCommonQueryVO commonQueryVO, CurrentPage currentPage) {
        LOGGER.debug("<======CommonQueryUCCImpl--findCustomersByCustomerInfo======>");
        List<QryCommonQueryVO> commonQueryVOs = new ArrayList<QryCommonQueryVO>();
        if (null != commonQueryVO && commonQueryVO.getCustomerVO() != null) {
            QryCustomerBO customerBO = BeanUtils.copyProperties(QryCustomerBO.class, commonQueryVO.getCustomerVO());
            currentPage = commonQueryService.findCustomerInfos(customerBO, currentPage);
            // 缺陷10980修改
            List<QryCustomerBO> pageItems = currentPage.getPageItems();
            for (QryCustomerBO qryCustomerBO2 : pageItems) {
            	String incomeSource = qryCustomerBO2.getIncomeSource();
            	if(null != incomeSource && ""!=incomeSource){
            		String [] list = incomeSource.split("/");
            		List<String> sourceList = new ArrayList<String>();
            		for(int i =0;i<list.length;i++){
            			String source = list[i];
            			sourceList.add(source);
            		}
            		qryCustomerBO2.setIncomeSourceList(sourceList);
            	}
            	StringBuffer sb=new StringBuffer("");
            	// 脱贫户 边缘户 标识
            	if(!StringUtilsEx.isBlank(qryCustomerBO2.getInsSpePeople())){
            		//qryCustomerBO2.setApplicantSpePeople(new BigDecimal(qryCustomerBO2.getInsSpePeople()));
            		if("1".equals(qryCustomerBO2.getInsSpePeople())){
            			sb.append("/脱贫户");
            		}else if("2".equals(qryCustomerBO2.getInsSpePeople())){
            			sb.append("/边缘户");
            		}
            	}
            	//乡村人口
            	if(null !=qryCustomerBO2.getRuralPopulationFlag()){
            		if(BigDecimal.ONE.compareTo(qryCustomerBO2.getRuralPopulationFlag())==0){
            			sb.append("/乡村人口");
            		}
            	}
            	// 残疾人标识 
            	if(null !=qryCustomerBO2.getDisabilityFlag()){
            		if(BigDecimal.ONE.compareTo(qryCustomerBO2.getDisabilityFlag())==0){
            			sb.append("/残疾人");
            		}
            		
            	}// 残疾人号
//            	if(!StringUtilsEx.isBlank(qryCustomerBO2.getDisabilityNo())){
//            		sb.append("(").append(qryCustomerBO2.getDisabilityNo()).append(")");
//            	}
            	
            	 LOGGER.debug("findCustomersByCustomerInfo====sb.toString=="+sb.toString());
            	
            	if(!StringUtilsEx.isBlank(sb.toString())){

            		qryCustomerBO2.setSpecialPeople(sb.toString().substring(1));
            	}
            	
            	/**
        		 * #173776个人核心系统展示“客户服务权益资格”等客户标识需求
        		 * 新华尊等级: 11-黑钻,12-蓝钻,13-新钻
        		 * 新华瑞等级: 21-五星级,22-四星级,23-三星级,24-二星级,25-一星级
        		 * 新华安等级: VIP1,VIP2,VIP3
        		 */
        		List<QryCustomerPO> customerPORights = customerDao.findCustRightsQual(BeanUtils.copyProperties(QryCustomerPO.class,qryCustomerBO2));
        		if(customerPORights!=null && customerPORights.size()>0){
        			//新华尊等级
        			switch (StringUtilsEx.nullToString(customerPORights.get(0).get("xhz_level"))) {
        			case "11":
        				qryCustomerBO2.setXhzLevel(CustomerRightsQualEnum.Xhz_11.getName());
        				break;
        			case "12":
        				qryCustomerBO2.setXhzLevel(CustomerRightsQualEnum.Xhz_12.getName());
        				break;
        			case "13":
        				qryCustomerBO2.setXhzLevel(CustomerRightsQualEnum.Xhz_13.getName());
        				break;
        			default:
        				qryCustomerBO2.setXhzLevel("");
        				break;
        			}
        			//新华瑞等级
        			switch (StringUtilsEx.nullToString(customerPORights.get(0).getXhrLevel())) {
        			case "21":
        				qryCustomerBO2.setXhrLevel(CustomerRightsQualEnum.Xhr_21.getName());
        				break;
        			case "22":
        				qryCustomerBO2.setXhrLevel(CustomerRightsQualEnum.Xhr_22.getName());
        				break;
        			case "23":
        				qryCustomerBO2.setXhrLevel(CustomerRightsQualEnum.Xhr_23.getName());
        				break;
        			case "24":
        				qryCustomerBO2.setXhrLevel(CustomerRightsQualEnum.Xhr_24.getName());
        				break;
        			case "25":
        				qryCustomerBO2.setXhrLevel(CustomerRightsQualEnum.Xhr_25.getName());
        				break;
        			default:
        				qryCustomerBO2.setXhrLevel("");
        				break;
        			}
        			//新华安等级
        			qryCustomerBO2.setXhaLevel(customerPORights.get(0).getXhaLevel());
        		}
			}
            currentPage = BeanUtils.copyCurrentPage(QryCustomerVO.class, currentPage);
            LOGGER.debug("==currentPage=="+XmlHelper.classToXml(currentPage));
        }
        return currentPage;
    }

    /**
     * @description 通过客户号查询客户信息（客户角色）
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-3-9 下午3:29:37
     * @see com.com.nci.tunan.qry.impl.qry.ucc.pa.impl.commonQuery.ucc.ICommonQueryUCC#findCustomerInfosByCustomerId(com.nci.tunan.qry.interfaces.model.qry.vo.QryCommonQueryVO.interfaces.model.vo.CommonQueryVO)
     * @param commonQueryVO
     * @return
     */
    public List<QryCommonQueryVO> findCustomerInfosByCustomerId(QryCommonQueryVO commonQueryVO) {
        LOGGER.debug("<======CommonQueryUCCImpl--findCustomerInfosByPolicyId======>");
        List<QryCommonQueryVO> commonQueryVOs = new ArrayList<QryCommonQueryVO>();
        if (null != commonQueryVO && commonQueryVO.getCustomerVO() != null) {
            QryCustomerBO customerBO = BeanUtils.copyProperties(QryCustomerBO.class, commonQueryVO.getCustomerVO());
            // 查询投保人信息（包含客户信息）
            QryHolderCustomerCompBO holderCustomerCompBO = new QryHolderCustomerCompBO();
            holderCustomerCompBO.setCustomerBO(customerBO);
            List<QryHolderCustomerCompBO> holderCustomerCompBOs = holderInsuredBeneCustCompService
                    .findHolderCustomerInfo(holderCustomerCompBO);
            // BOChangeVO
            for (QryHolderCustomerCompBO holderCustCompBO : holderCustomerCompBOs) {
                commonQueryVO = new QryCommonQueryVO();
                commonQueryVO.setCustomerVO(BeanUtils.copyProperties(QryCustomerVO.class,
                        holderCustCompBO.getCustomerBO()));
                commonQueryVO.setCustomerRoles(Constants.ROLE__POLICY_HOLDER);
                commonQueryVO.setPolicyId(holderCustCompBO.getPolicyHolderBO().getPolicyId());
                commonQueryVOs.add(commonQueryVO);
            }

            // 查询被保人信息（包含客户信息）
            QryInsuredCustomerCompBO insuredCustomerCompBO = new QryInsuredCustomerCompBO();
            insuredCustomerCompBO.setCustomerBO(customerBO);
            List<QryInsuredCustomerCompBO> insuredCustomerCompBOs = holderInsuredBeneCustCompService
                    .findInsuredCustomerInfo(insuredCustomerCompBO);
            // BOChangeVO
            for (QryInsuredCustomerCompBO insuredCustCompBO : insuredCustomerCompBOs) {
                commonQueryVO = new QryCommonQueryVO();
                commonQueryVO.setCustomerVO(BeanUtils.copyProperties(QryCustomerVO.class,
                        insuredCustCompBO.getCustomerBO()));
                commonQueryVO.setCustomerRoles(Constants.ROLE__INSURED);
                commonQueryVO.setPolicyId(insuredCustCompBO.getInsuredListBO().getPolicyId());
                commonQueryVOs.add(commonQueryVO);
            }

            // 查询受益人信息（包含客户信息）
            QryBeneCustomerCompBO beneCustomerCompBO = new QryBeneCustomerCompBO();
            beneCustomerCompBO.setCustomerBO(customerBO);
            List<QryBeneCustomerCompBO> beneCustomerCompBOs = holderInsuredBeneCustCompService
                    .findBeneCustomerInfo(beneCustomerCompBO);
            // BOChangeVO
            for (QryBeneCustomerCompBO beneCustCompBO : beneCustomerCompBOs) {
                commonQueryVO = new QryCommonQueryVO();
                commonQueryVO.setCustomerVO(BeanUtils.copyProperties(QryCustomerVO.class,
                        beneCustCompBO.getCustomerBO()));
                commonQueryVO.setCustomerRoles(Constants.ROLE__CONTRACT_BENE);
                commonQueryVO.setPolicyId(beneCustCompBO.getContractBeneBO().getPolicyId());
                commonQueryVOs.add(commonQueryVO);
            }

            // 判断是否存在在同一保单中，同一客户既是投保人又是被保人又是受益人
            for (int i = 0; i < commonQueryVOs.size(); i++) {
                QryCommonQueryVO voi = commonQueryVOs.get(i);
                for (int j = i + 1; j < commonQueryVOs.size(); j++) {
                    QryCommonQueryVO voj = commonQueryVOs.get(j);
                    if (voi.getPolicyId().equals(voj.getPolicyId())) {
                        voi.setCustomerRoles(voi.getCustomerRoles() + "/" + voj.getCustomerRoles());
                        commonQueryVOs.remove(j);
                    }
                }
            }
        }

        return commonQueryVOs;
    }

    
    
    
    
    
    /**
     * 综合查询DAO接口类
     */
    @Autowired
    private IQryCommonQueryDao commonQueryDao;
    
    
    
    

    
    public IQryCommonQueryDao getCommonQueryDao() {
		return commonQueryDao;
	}

	public void setCommonQueryDao(IQryCommonQueryDao commonQueryDao) {
		this.commonQueryDao = commonQueryDao;
	}

	/**
     * 查询信托公司信息、受益人信息、受益比例信息
     * @param customerInfoVO
     * @return
     */
    public QryHolderInsuredCustCompVO findCompanyInfo(QryHolderInsuredCustCompVO qryHolderInsuredCustCompVO){
    	QryHolderInsuredCustCompVO qHolderInsuredCustCompVO = new QryHolderInsuredCustCompVO();
    	TrustCompanyVO trustCompanyVO = new TrustCompanyVO();
    	BeneCustomerVO beneCustomerVO = new BeneCustomerVO();
    	try {
    		// 1.查询信托受益公司信息
    		TrustCompanyBO tCompanyBO = commonQueryService.findBeneCompanyInfoByOragnCode(BeanUtils.copyProperties(TrustCompanyBO.class,
    				qryHolderInsuredCustCompVO.getTrustCompanyVO()));
    		trustCompanyVO = BeanUtils.copyProperties(TrustCompanyVO.class, tCompanyBO);
    		qHolderInsuredCustCompVO.setTrustCompanyVO(trustCompanyVO);
    		
    		
    		// 2.查询信托公司受益人基本信息
    		TrustCompanyBenePO trustCompanyBenePO = new TrustCompanyBenePO();
    		if (qryHolderInsuredCustCompVO.getTrustCompanyVO().getCompanyId() != null) {
    			trustCompanyBenePO.setCompanyId(qryHolderInsuredCustCompVO.getTrustCompanyVO().getCompanyId());
    			List<TrustCompanyBenePO> trustCompanyBenePOList = commonQueryDao.findAllTrustCompanyBene(trustCompanyBenePO);
        		List<TrustCompanyBeneVO> trustCompanyBeneVOList = BeanUtils.copyList(TrustCompanyBeneVO.class, trustCompanyBenePOList);
        		qHolderInsuredCustCompVO.setTrustCompanyBeneVOList(trustCompanyBeneVOList);
			}
    		//3.获取受益人类型、受益比例、受益顺序
    		if(null != qryHolderInsuredCustCompVO.getBeneCustomerVO() && null != qryHolderInsuredCustCompVO.getBeneCustomerVO().getBeneType()){
    			beneCustomerVO.setBeneType(qryHolderInsuredCustCompVO.getBeneCustomerVO().getBeneType());
        		beneCustomerVO.setShareOrder(qryHolderInsuredCustCompVO.getBeneCustomerVO().getShareOrder());
        		beneCustomerVO.setShareRate(qryHolderInsuredCustCompVO.getBeneCustomerVO().getShareRate());
        		qHolderInsuredCustCompVO.setBeneCustomerVO(beneCustomerVO);
    		}
		} catch (Exception e) {
			e.printStackTrace();
		}
      return qHolderInsuredCustCompVO;
    }

    
    /**
     * @description 根据保单号查询客户列表（投保人、被保人、受益人）
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-1-28 上午9:30:00
     * @see com.com.nci.tunan.qry.impl.qry.ucc.pa.impl.commonQuery.ucc.ICommonQueryUCC#findCustomerInfo(com.nci.tunan.qry.interfaces.model.qry.vo.QryCustomerInfoVO.interfaces.model.vo.CustomerInfoVO)
     * @param customerInfoVO
     * @return
     */
    public QryCustomerInfoVO findCustomerInfo(QryCustomerInfoVO customerInfoVO) {
        LOGGER.debug("<======CommonQueryUCCImpl--findCustomerInfo======>");
        QryCustomerInfoVO custInfoVO = new QryCustomerInfoVO();
        QryHolderInsuredCustCompBO holderInsuredCustCompBO = BeanUtils.copyProperties(QryHolderInsuredCustCompBO.class,
                customerInfoVO);
        // 查询投保人信息
        List<QryHolderInsuredCustCompBO> holderCustomerCompBOs = commonQueryService
                .findHoderCustomerInfo(holderInsuredCustCompBO);
        custInfoVO
                .setHolderCustomerCompVOs(BeanUtils.copyList(QryHolderInsuredCustCompVO.class, holderCustomerCompBOs));
        // 查询被保险人信息
        List<QryHolderInsuredCustCompBO> insuredCustCompBOs = commonQueryService.findInsuredCustomerInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompBO.class, customerInfoVO));
        custInfoVO.setInsuredCustomerCompVOs(BeanUtils.copyList(QryHolderInsuredCustCompVO.class, insuredCustCompBOs));
        // 查询受益人信息
        List<QryHolderInsuredCustCompBO> beneCustCompBOs = commonQueryService.findBeneCustomerInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompBO.class, customerInfoVO));
        custInfoVO.setContractBenes(BeanUtils.copyList(QryHolderInsuredCustCompVO.class, beneCustCompBOs));
		// 4.查询第二投保人信息 
		List<QryHolderInsuredCustCompBO> secondPolicyHolderCompBOs = commonQueryService
				.findSecondPolicyHolderInfo(BeanUtils.copyProperties(QryHolderInsuredCustCompBO.class, customerInfoVO));
		custInfoVO.setSecondPolicyHolders(BeanUtils.copyList(QryHolderInsuredCustCompVO.class, secondPolicyHolderCompBOs));
        custInfoVO.setPolicyCode(customerInfoVO.getPolicyCode());
        custInfoVO.setPolicyId(customerInfoVO.getPolicyId());
        return custInfoVO;
    }

    public CurrentPage<CustomerAccountVO> findCustomersByCustomerAccount(QryCommonQueryVO commonQueryVO,
            CurrentPage<CustomerAccountVO> currentPage) {
        CustomerAccouontBO accountBO = BeanUtils.copyProperties(CustomerAccouontBO.class, commonQueryVO);
        CurrentPage<CustomerAccouontBO> currenPage = commonQueryService.findCustomersByCustomerAccount(accountBO,
                BeanUtils.copyCurrentPage(CustomerAccouontBO.class, currentPage));
        return BeanUtils.copyCurrentPage(CustomerAccountVO.class, currenPage);
    }

    
    
    
    /**
     * @description 根据保单号查询客户列表（投保人、被保人、受益人(新增信托公司字段)）
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-1-28 上午9:30:00
     * @see com.com.nci.tunan.qry.impl.qry.ucc.pa.impl.commonQuery.ucc.ICommonQueryUCC#findCustomerInfo(com.nci.tunan.qry.interfaces.model.qry.vo.QryCustomerInfoVO.interfaces.model.vo.CustomerInfoVO)
     * @param customerInfoVO 客户信息VO对象
     * @return
     */
    public QryCustomerInfoVO findTrustInfoAndCustomerInfo(QryCustomerInfoVO customerInfoVO) {
        LOGGER.debug("<======CommonQueryUCCImpl--findCustomerInfo======>");
        QryCustomerInfoVO custInfoVO = new QryCustomerInfoVO();
        QryHolderInsuredCustCompBO holderInsuredCustCompBO = BeanUtils.copyProperties(QryHolderInsuredCustCompBO.class,
                customerInfoVO);
        // 1.查询投保人信息 查询表投保人信息表 T_POLICY_HOLDER 客户表 T_CUSTOMER 地址表 T_ADDRESS（保单层数据取值）
        List<QryHolderInsuredCustCompBO> holderCustomerCompBOs = commonQueryService
                .findHoderCustomerInfo(holderInsuredCustCompBO);
        custInfoVO
                .setHolderCustomerCompVOs(BeanUtils.copyList(QryHolderInsuredCustCompVO.class, holderCustomerCompBOs));
        // 2.查询被保险人信息 查询表 被保人信息表T_INSURED_LIST 客户表T_CUSTOMER 地址表 T_ADDRESS（保单层数据取值）
        List<QryHolderInsuredCustCompBO> insuredCustCompBOs = commonQueryService.findInsuredCustomerInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompBO.class, customerInfoVO));
        custInfoVO.setInsuredCustomerCompVOs(BeanUtils.copyList(QryHolderInsuredCustCompVO.class, insuredCustCompBOs));
        // 3.查询受益人信息 查询表 受益人信息表 T_CONTRACT_BENE 客户表T_CUSTOMER 地址表 T_ADDRESS（保单层数据取值）信托公司表（T_TRUST_COMPANY） 、保单主表
        List<QryHolderInsuredCustCompBO> beneCustCompBOs = commonQueryService.findBeneForTrustCustomerInfo(BeanUtils
                .copyProperties(QryHolderInsuredCustCompBO.class, customerInfoVO));
        custInfoVO.setContractBenes(BeanUtils.copyList(QryHolderInsuredCustCompVO.class, beneCustCompBOs));
        custInfoVO.setPolicyCode(customerInfoVO.getPolicyCode());
        custInfoVO.setPolicyId(customerInfoVO.getPolicyId());
        return custInfoVO;
    }

    
    
    /**
     * @description 险种信息页面---查询险种责任组列表信息
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-2-2 上午9:58:10
     * @see com.com.nci.tunan.qry.impl.qry.ucc.pa.impl.commonQuery.ucc.ICommonQueryUCC#findBusiItemInfos(com.nci.tunan.qry.interfaces.model.qry.vo.QryBusiItemInfoVO.interfaces.model.vo.BusiItemInfoVO)
     * @param busiItemInfoVO
     * @return
     */
    public List<QryBusiItemInfoVO> findBusiItemInfos(QryBusiItemInfoVO busiItemInfoVO) {
        LOGGER.debug("<======CommonQueryUCCImpl--findBusiItemInfos======>");
        List<QryBusiItemInfoCompBO> busiItemInfoCompBOs = commonQueryService.findBusiItemInfos(BeanUtils
                .copyProperties(QryBusiItemInfoCompBO.class, busiItemInfoVO));
        return BeanUtils.copyList(QryBusiItemInfoVO.class, busiItemInfoCompBOs);
    }
    
    /**
     * 关联保单信息页面---查询关联保单信息
     */
    @Override
	public List<QryPolicyInfoVO> findRelationPolicyInfos(
			QryPolicyInfoVO policyInfoVO) {
    	LOGGER.debug("<======CommonQueryUCCImpl--findRelationPolicyInfos======>");
        List<QryContractMasterCompBO> qryContractMasterCompBO = commonQueryService.findRelationPolicyInfos(BeanUtils
                .copyProperties(QryContractMasterCompBO.class, policyInfoVO));
        return BeanUtils.copyList(QryPolicyInfoVO.class, qryContractMasterCompBO);
	}
    
	@Override
	public List<NbContractMasterVO> queryNotAutoProcess(
			NbContractMasterVO nbContractMasterVO) {
		LOGGER.debug("<======CommonQueryUCCImpl--queryNotAutoProcess======>");
		List<NbContractMasterBO> nbContractMasterBO = commonQueryService.queryNotAutoProcess(BeanUtils
                .copyProperties(NbContractMasterBO.class, nbContractMasterVO));
		
		return BeanUtils.copyList(NbContractMasterVO.class, nbContractMasterBO);
	}

    /**
     * @description 贷款信息页面---查询保单贷款信息列表信息
     * @version 0.1
     * @title
     * <AUTHOR>
     * @date
     * @see com.com.nci.tunan.qry.impl.qry.ucc.pa.impl.commonQuery.ucc.ICommonQueryUCC#findBusiItemInfos(com.nci.tunan.qry.interfaces.model.qry.vo.QryBusiItemInfoVO.interfaces.model.vo.BusiItemInfoVO)
     * @param
     * @return
     */
    public List<QryPolicyAccountVO> findPolicyAccountInfo(QryPolicyInfoVO vo) {
        LOGGER.debug("<======CommonQueryUCCImpl--findPolicyAccountInfo======>");
        List<QryPolicyAccountBO> listbos = commonQueryService.findPolicyAccountInfo(BeanUtils.copyProperties(
                QryCommonQueryCompBO.class, vo));
        return BeanUtils.copyList(QryPolicyAccountVO.class, listbos);
    }

    public List<Map<String, Object>> queryClmObject(QryPolicyInfoVO taskbO) {
        QryCommonQueryCompBO Bo = BeanUtils.copyProperties(QryCommonQueryCompBO.class, taskbO);
        return commonQueryService.queryClmObject(Bo);
    }

    public ICommonQueryService getCommonQueryService() {
        return commonQueryService;
    }

    public void setCommonQueryService(ICommonQueryService commonQueryService) {
        this.commonQueryService = commonQueryService;
    }

    public IHolderInsuredBeneCustCompService getHolderInsuredBeneCustCompService() {
        return holderInsuredBeneCustCompService;
    }

    public void setHolderInsuredBeneCustCompService(IHolderInsuredBeneCustCompService holderInsuredBeneCustCompService) {
        this.holderInsuredBeneCustCompService = holderInsuredBeneCustCompService;
    }

    /*
     * public INBService getNbIAS() { return nbIAS; }
     * 
     * public void setNbIAS(INBService nbIAS) { this.nbIAS = nbIAS; }
     */
    /**
     * @description 查询保单贷款信息
     * <AUTHOR>
     * @param vo
     * @param currentPage
     * @return
     */
    @Override
    public CurrentPage findCSProductInfo(QryPolicyInfoVO vo, CurrentPage currentPage) {
        QryCommonQueryCompBO bo = new QryCommonQueryCompBO();
        bo = BeanUtils.copyProperties(QryCommonQueryCompBO.class, vo);
        return commonQueryService.findCSProductInfo(bo, currentPage);
    }

    /**
     * @description 查询保全信息
     * <AUTHOR>
     * @param vo
     * @param currentPage
     * @return
     */
    @Override
    public CurrentPage findCSInfoList(QryPolicyInfoVO vo, CurrentPage currentPage) {
        QryCommonQueryCompBO bo = new QryCommonQueryCompBO();
        bo = BeanUtils.copyProperties(QryCommonQueryCompBO.class, vo);
        return commonQueryService.findCSInfoList(bo, currentPage);
    }

    @Override
    public CurrentPage<QryUwBusiProdVO> findUwBusiProdInfo(QryUwBusiProdVO uwBusiProdVO,
            CurrentPage<QryUwBusiProdVO> currentPage) {
        QryUwBusiProdBO bo = BeanUtils.copyProperties(QryUwBusiProdBO.class, uwBusiProdVO);
        if(!StringUtilsEx.isNullOrEmpty(bo.getApplyCode())){
        	return BeanUtils.copyCurrentPage(QryUwBusiProdVO.class, commonQueryService.findUwBusiProdInfo(bo,
        			BeanUtils.copyCurrentPage(QryUwBusiProdBO.class, currentPage)));
        }
        return null;
    }

    @Override
    public QryNBPolicyCommVO findNBPolicyInfo(QryPolicyInfoVO vo) {
        QryNBPolicyCommVO commVO = new QryNBPolicyCommVO();
        QryProposalProcessBO qryProposalProcessBO = BeanUtils.copyProperties(QryProposalProcessBO.class, vo);
        // QryProposalProcessBO bo = new QryProposalProcessBO();
        // bo.setPolicyCode(vo.getPolicyCode());

        List<QryProposalProcessBO> listuwBO = new ArrayList<QryProposalProcessBO>();
        listuwBO = commonQueryService.findNBuwInfo(qryProposalProcessBO);
        commVO.setNbUWs(BeanUtils.copyList(QryProposalProcessVO.class, listuwBO));

        List<QryProposalProcessBO> listinsBO = new ArrayList<QryProposalProcessBO>();
        listinsBO = commonQueryService.findNBinsInfo(qryProposalProcessBO);
        commVO.setNbInss(BeanUtils.copyList(QryProposalProcessVO.class, listinsBO));

        return commVO;
    }

    /**
     * 获取新契约履历带分页
     * @param vo
     * @param currentPage
     * @return
     */
    @Override
    public CurrentPage findNBProcessPageInfo(QryProposalProcessVO vo,CurrentPage currentPage) {
    	 QryProposalProcessBO bo = new QryProposalProcessBO();
         bo = BeanUtils.copyProperties(QryProposalProcessBO.class, vo);
         return BeanUtils.copyCurrentPage(QryProposalProcessVO.class, commonQueryService.findNBProcessPageInfo(bo, BeanUtils.copyCurrentPage(QryProposalProcessBO.class, currentPage)));
    }
    /**
     * 根据投保单号查询保单信息
     */
    @Override
	public List<QryPolicyInfoVO> findNBApplyCode(QryCommonQueryVO commonQueryVO) {
    	QryProposalProcessBO bo = new QryProposalProcessBO();
         bo = BeanUtils.copyProperties(QryProposalProcessBO.class, commonQueryVO);
         List<QryProposalProcessBO> listBO = new ArrayList<QryProposalProcessBO>();
         listBO = commonQueryService.findNBApplyCode(bo);
         return BeanUtils.copyList(QryPolicyInfoVO.class, listBO);
	}
    
    @Override
    public List<QryProposalProcessVO> findNBProcessInfo(QryPolicyInfoVO vo) {
        QryProposalProcessBO bo = new QryProposalProcessBO();
        bo = BeanUtils.copyProperties(QryProposalProcessBO.class, vo);
        List<QryProposalProcessBO> listBO = new ArrayList<QryProposalProcessBO>();
        listBO = commonQueryService.findNBProcessInfo(bo);
        return BeanUtils.copyList(QryProposalProcessVO.class, listBO);
    }

    @Override
    public List<QryDocumentVO> findNBDocumentInfo(QryPolicyInfoVO vo) {
        QryDocumentBO bo = new QryDocumentBO();
        bo = BeanUtils.copyProperties(QryDocumentBO.class, vo);
        List<QryDocumentBO> listBO = new ArrayList<QryDocumentBO>();
        listBO = commonQueryService.findNBDocumentInfo(bo);
        return BeanUtils.copyList(QryDocumentVO.class, listBO);
    }

    @Override
    public CurrentPage<QryCommonPolicyInfoVO> findPolicyInfoByCustomerId(QryCommonPolicyInfoVO commonPolicyInfoVO,
            CurrentPage<QryCommonPolicyInfoVO> currentPage) {
        QryCommonPolicyInfoBO commonPolicyInfoBO = BeanUtils.copyProperties(QryCommonPolicyInfoBO.class,
                commonPolicyInfoVO);
        CurrentPage<QryCommonPolicyInfoBO> cuur = commonQueryService.findPolicyInfoByCustomerId(commonPolicyInfoBO,
                BeanUtils.copyCurrentPage(QryCommonPolicyInfoBO.class, currentPage));
        return BeanUtils.copyCurrentPage(QryCommonPolicyInfoVO.class, cuur);
    }
    
    @Override
    public CurrentPage<QryCommonPolicyInfoVO> findPolicyInfoByCustomerIdOne(QryCommonPolicyInfoVO commonPolicyInfoVO,
            CurrentPage<QryCommonPolicyInfoVO> currentPage) {
        QryCommonPolicyInfoBO commonPolicyInfoBO = BeanUtils.copyProperties(QryCommonPolicyInfoBO.class,
                commonPolicyInfoVO);
        CurrentPage<QryCommonPolicyInfoBO> cuur = commonQueryService.findPolicyInfoByCustomerIdOne(commonPolicyInfoBO,
                BeanUtils.copyCurrentPage(QryCommonPolicyInfoBO.class, currentPage));
        return BeanUtils.copyCurrentPage(QryCommonPolicyInfoVO.class, cuur);
    }
    
    @Override
    public QryCommonPolicyInfoVO findCustomerInfoById(QryCommonPolicyInfoVO commonPolicyInfoVO) {
        QryCommonPolicyInfoBO commonPolicyInfoBO = commonQueryService.findCustomerInfoById(BeanUtils.copyProperties(
                QryCommonPolicyInfoBO.class, commonPolicyInfoVO));
        return BeanUtils.copyProperties(QryCommonPolicyInfoVO.class, commonPolicyInfoBO);
    }

    @Override
    public AgentVO findAgentByAgentCode(AgentVO agentVO) {
        AgentBO agentBO = commonQueryService.findAgentByAgentCode(BeanUtils.copyProperties(AgentBO.class, agentVO));
        return BeanUtils.copyProperties(AgentVO.class, agentBO);
    }
    
    @Override
    public CurrentPage<QryCommonPolicyInfoVO> findPolicyInfoByagentCode(QryCommonPolicyInfoVO commonPolicyInfoVO,
            CurrentPage<QryCommonPolicyInfoVO> currentPage) {
        QryCommonPolicyInfoBO commonPolicyInfoBO = BeanUtils.copyProperties(QryCommonPolicyInfoBO.class,
                commonPolicyInfoVO);
        CurrentPage<QryCommonPolicyInfoBO> cuur = commonQueryService.findPolicyInfoByagentCode(commonPolicyInfoBO,
                BeanUtils.copyCurrentPage(QryCommonPolicyInfoBO.class, currentPage));
        return BeanUtils.copyCurrentPage(QryCommonPolicyInfoVO.class, cuur);
    }

    @Override
    public CurrentPage<QryPremArapVO> findPremArapListInfo(QryPremArapVO premArapVO, CurrentPage<QryPremArapVO> currentPage) {
        QryPremArapBO bo = BeanUtils.copyProperties(QryPremArapBO.class, premArapVO);
        return BeanUtils.copyCurrentPage(QryPremArapVO.class, commonQueryService.findPremArapListInfo(bo,
                BeanUtils.copyCurrentPage(QryPremArapBO.class, currentPage)));
    }

    @Override
    public QryPremArapVO findPremArapDetailInfo(QryPremArapVO premArapVO) {
        QryPremArapBO bo = BeanUtils.copyProperties(QryPremArapBO.class, premArapVO);
        QryPremArapBO bo2 = commonQueryService.findPremArapDetailInfo(bo);
        return BeanUtils.copyProperties(QryPremArapVO.class, bo2);
    }

    @Override
    public CurrentPage getPayPlanInfoPage(QryPayPlanDueVO payPlanDueVO, CurrentPage currentPage) {
        QryPayPlanDueBO bo = BeanUtils.copyProperties(QryPayPlanDueBO.class, payPlanDueVO);
        return BeanUtils.copyCurrentPage(QryPayPlanDueVO.class, commonQueryService.getPayPlanInfoPage(bo,
                BeanUtils.copyCurrentPage(QryPayPlanDueBO.class, currentPage)));
    }

    @Override
    public CurrentPage getPayPayDuePage(QryPayPlanDueVO payPlanDueVO, CurrentPage currentPage) {
        QryPayPlanDueBO bo = BeanUtils.copyProperties(QryPayPlanDueBO.class, payPlanDueVO);
        return BeanUtils.copyCurrentPage(QryPayPlanDueVO.class,
                commonQueryService.getPayPayDuePage(bo, BeanUtils.copyCurrentPage(QryPayPlanDueBO.class, currentPage)));
    }

    @Override
    public CurrentPage getPayPayDueDetailPage(QryPayPlanDueVO payPlanDueVO, CurrentPage currentPage) {
        QryPayPlanDueBO bo = BeanUtils.copyProperties(QryPayPlanDueBO.class, payPlanDueVO);
        return BeanUtils.copyCurrentPage(
                QryPayPlanDueVO.class,
                commonQueryService.getPayPayDueDetailPage(bo,
                        BeanUtils.copyCurrentPage(QryPayPlanDueBO.class, currentPage)));
    }

    @Override
    public QryPolicyInfoVO findNBPolicyCode(QryCommonQueryVO commonQueryVO) {
        QryProposalProcessBO bo = new QryProposalProcessBO();
        bo = BeanUtils.copyProperties(QryProposalProcessBO.class, commonQueryVO);
        bo = commonQueryService.findNBPolicyCode(bo);
        return BeanUtils.copyProperties(QryPolicyInfoVO.class, bo);
    }

    // 投连万能账户基本信息
    @Override
    public CurrentPage getAccountInfoPage(QryAccountVO accountVO, CurrentPage accountPage) {
        // TODO Auto-generated method stub
        QryAccountBO bo = new QryAccountBO();
        bo = BeanUtils.copyProperties(QryAccountBO.class, accountVO);
        return commonQueryService.getAccountInfoPage(bo, accountPage);
    }

    @Override
    public CurrentPage getAccountPathInfoPage(QryAccountVO accountVO, CurrentPage currentPage) {
        // TODO Auto-generated method stub
        QryAccountBO bo = new QryAccountBO();
        bo = BeanUtils.copyProperties(QryAccountBO.class, accountVO);
        return commonQueryService.getAccountPathInfoPage(bo, currentPage);
    }


    @Override
    public CurrentPage getPasswordPath(QueryPolicyPasswordPathVO queryPolicyPasswordPathvo, CurrentPage currentPage) {
        // TODO Auto-generated method stub
        QueryPolicyPasswordPathBO bo = new QueryPolicyPasswordPathBO();
        bo = BeanUtils.copyProperties(QueryPolicyPasswordPathBO.class, queryPolicyPasswordPathvo);
        return commonQueryService.getPasswordPath(bo, currentPage);
    }

    @Override
    public CurrentPage<QueryPolicyBonusVO> getPolicyBonusPage(QueryPolicyBonusVO queryBonusVo, CurrentPage<QueryPolicyBonusVO> currentPage) {
    	CurrentPage<QueryPolicyBonusBO> currPage =commonQueryService.getPolicyBonusPage(
        		BeanUtils.copyProperties(QueryPolicyBonusBO.class, queryBonusVo), 
        		BeanUtils.copyCurrentPage(QueryPolicyBonusBO.class, currentPage));
        return BeanUtils.copyCurrentPage(QueryPolicyBonusVO.class, currPage);
    }
    @Override
    public QueryPolicyBonusVO getBonusDetailPage(QueryPolicyBonusVO queryBonusVo) {
        QueryPolicyBonusBO queryPolicyBonusBO = 
                commonQueryService.getBonusDetailPage(BeanUtils.copyProperties(QueryPolicyBonusBO.class, queryBonusVo));
        return BeanUtils.copyProperties(QueryPolicyBonusVO.class, queryPolicyBonusBO);
    }
    @Override
    public CurrentPage getReissuedPrint(QryReissuedPrintVO vo, CurrentPage currentPage) {
        QryReissuedPrintBO bo = BeanUtils.copyProperties(QryReissuedPrintBO.class, vo);
        return commonQueryService.getReissuedPrint(bo, currentPage);
    }

    @Override
    public List<CsPolicyInfoChangeThreebVO> queryCsPolictInfoChangeListThreeb(
            CsPolicyInfoChangeThreebVO CsPolicyInfoChangeThreebVO) {
        List<CsPolicyInfoChangeThreebVO> csPolicyInfoChangeThreebVOS = new ArrayList<CsPolicyInfoChangeThreebVO>();
        CsPolicyInfoChangeThreebPO csPolicyInfoChangeThreebPO = new CsPolicyInfoChangeThreebPO();
        csPolicyInfoChangeThreebPO.setPolicyCode(CsPolicyInfoChangeThreebVO.getPolicyCode());
        List<CsPolicyInfoChangeThreebPO> csPolicyInfoChangeThreebPOs = commonQueryService
                .findCsPolicyInfoChangeThreebPO(csPolicyInfoChangeThreebPO);
        csPolicyInfoChangeThreebVOS = BeanUtils.copyList(CsPolicyInfoChangeThreebVO.class, csPolicyInfoChangeThreebPOs);
        return csPolicyInfoChangeThreebVOS;
    }

    @Override
    public List<CsPolicyInfoChangeThreeaVO> queryCsPolictInfoChangeListThreea(
            CsPolicyInfoChangeThreeaVO CsPolicyInfoChangeThreeaVO) {
        List<CsPolicyInfoChangeThreeaVO> csPolicyInfoChangeThreeaaVOS = new ArrayList<CsPolicyInfoChangeThreeaVO>();
        CsPolicyInfoChangeThreeaPO csPolicyInfoChangeThreeaPO = new CsPolicyInfoChangeThreeaPO();
        csPolicyInfoChangeThreeaPO.setPolicyCode(CsPolicyInfoChangeThreeaVO.getPolicyCode());
        List<CsPolicyInfoChangeThreeaPO> csPolicyInfoChangeThreeaPOs = commonQueryService
                .findCsPolicyInfoChangeThreeaPO(csPolicyInfoChangeThreeaPO);
        csPolicyInfoChangeThreeaaVOS = BeanUtils
                .copyList(CsPolicyInfoChangeThreeaVO.class, csPolicyInfoChangeThreeaPOs);

//        List<CsPolicyInfoChangeThreeaVO> csPolicyInfoChangeThreeaVOS = new ArrayList<CsPolicyInfoChangeThreeaVO>();
//        for (int i = 0; i < csPolicyInfoChangeThreeaaVOS.size(); i++) {
//            if (csPolicyInfoChangeThreeaaVOS.get(i).getServiceName() != null
//                    || "".equals(csPolicyInfoChangeThreeaaVOS.get(i).getServiceName())) {
//                csPolicyInfoChangeThreeaVOS.add(csPolicyInfoChangeThreeaaVOS.get(i));
//            }
//        }

        return csPolicyInfoChangeThreeaaVOS;
    }

    @Override
    public CurrentPage<QryAccountVO> getLiveAccountPage(QryAccountVO accountVO, CurrentPage<QryAccountVO> currentPage) {
        QryAccountBO accountBO = BeanUtils.copyProperties(QryAccountBO.class, accountVO);
        CurrentPage<QryAccountBO> accountPage = commonQueryService.getLiveAccountPage(accountBO,
                BeanUtils.copyCurrentPage(QryAccountBO.class, currentPage));
        return BeanUtils.copyCurrentPage(QryAccountVO.class, accountPage);
    }
    
    /**
     * 
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param accountVO
     * @param currentPage
     * @return
     */
    @Override
    public CurrentPage<QryAccountVO> getLiveAccountPathPage(QryAccountVO accountVO, CurrentPage<QryAccountVO> currentPage) {
        QryAccountBO accountBO = BeanUtils.copyProperties(QryAccountBO.class, accountVO);
        CurrentPage<QryAccountBO> accountPage = commonQueryService.getLiveAccountPathPage(accountBO,
                BeanUtils.copyCurrentPage(QryAccountBO.class, currentPage));
        return BeanUtils.copyCurrentPage(QryAccountVO.class, accountPage);
    }

    @Override
    public CurrentPage getSinceMatPage(QryAccountVO accountVO, CurrentPage currentPage) {
        // TODO Auto-generated method stub
        QryAccountBO bo = new QryAccountBO();
        bo = BeanUtils.copyProperties(QryAccountBO.class, accountVO);
        return commonQueryService.getSinceMatPage(bo, currentPage);
    }

    @Override
    public CsApplicationVO findCsInfoQueryApplicationByConditions(CsApplicationVO csApplicationVO) {
        return BeanUtils.copyProperties(CsApplicationVO.class, commonQueryService.findCsInfoQueryApplication(BeanUtils
                .copyProperties(CsApplicationBO.class, csApplicationVO)));
    }

    @Override
    public List<CsAcceptChangeVO> findAllCsAcceptChangeByConditions(CsAcceptChangeVO csAcceptChangeVO) {
        List<CsAcceptChangeBO> csAcceptChangeBOs = commonQueryService.findAllCsAcceptChange(BeanUtils.copyProperties(
                CsAcceptChangeBO.class, csAcceptChangeVO));
        return BeanUtils.copyList(CsAcceptChangeVO.class, csAcceptChangeBOs);
    }

    @Override
    public List<CsInfoQueryAcceptVO> findAllCsInfoQueryAcceptByConditions(CsInfoQueryAcceptVO csInfoQueryAcceptVO) {
        List<CsInfoQueryAcceptBO> csInfoQueryAcceptBOs = commonQueryService.findAllCsInfoQueryAccept(BeanUtils
                .copyProperties(CsInfoQueryAcceptBO.class, csInfoQueryAcceptVO));
        return BeanUtils.copyList(CsInfoQueryAcceptVO.class, csInfoQueryAcceptBOs);
    }

    @Override
    public List<CsInfoQueryBackMoneyVO> findAllCsInfoQueryBackMoneyByConditions(
            CsInfoQueryBackMoneyVO csInfoQueryBackMoneyVO) {
        List<CsInfoQueryBackMoneyBO> csInfoQueryAcceptBOs = commonQueryService.findAllCsInfoQueryBackMoney(BeanUtils
                .copyProperties(CsInfoQueryBackMoneyBO.class, csInfoQueryBackMoneyVO));
        return BeanUtils.copyList(CsInfoQueryBackMoneyVO.class, csInfoQueryAcceptBOs);
    }

    @Override
    public QryAccountVO queryCountBalancePeriodsInfo(QryAccountVO accountVO) {
        QryAccountBO bo = commonQueryService.queryCountBalancePeriodsInfo(BeanUtils.copyProperties(QryAccountBO.class,
                accountVO));
        return BeanUtils.copyProperties(QryAccountVO.class, bo);
    }

    @Override
    public QryNbDataInfoVO queryNbDataInfo(NbContractMasterVO nbContractMasterVO,int loadType) {
    	if(loadType==1){
    		//查被保人先查询险种表是否有984险种
        	NbContractBusiProdBO nbContractBusiProdBO = new NbContractBusiProdBO();
        	nbContractBusiProdBO.setApplyCode(nbContractMasterVO.getApplyCode());
    	    List<NbContractBusiProdBO> nbcbs = commonQueryService.queryNbContractBusiProdInfo(nbContractBusiProdBO);
    	    int Insureds =0;
    		for(NbContractBusiProdBO nbcb:nbcbs){
    			if("984000".equals(nbcb.getProductCode()) || "984001".equals(nbcb.getProductCode())){				
    				Insureds++;
    			}
    		}
    		if(Insureds>1){
    			nbContractMasterVO.setHaveInsureds(TransferUtil.STRING1);
    		}
    	}    	
        QryNbDataInfoBO queryNbDataInfo = commonQueryService.queryNbDataInfo(BeanUtils.copyProperties(
                NbContractMasterBO.class, nbContractMasterVO),loadType);
        QryNbDataInfoVO qryNbDataInfoVO = new QryNbDataInfoVO();
        switch (loadType){
	        case 0:
	            // 投保单基本信息
	            qryNbDataInfoVO.setNbContractMasterVO(BeanUtils.copyProperties(NbContractMasterVO.class,
	                    queryNbDataInfo.getNbContractMasterBO()));
	            // 投保人信息
	            qryNbDataInfoVO.setNbPolicyHolderVOs(BeanUtils.copyList(NbPolicyHolderVO.class,
	                    queryNbDataInfo.getNbPolicyHolderBOs()));
	            qryNbDataInfoVO.setNbContractCustomerHisList(BeanUtils.copyList(NbContractCustomerHisVO.class,
                        queryNbDataInfo.getNbContractCustomerHisList()));
	           break;
	        case 1: 	        	
	            // 被保险人信息
	            qryNbDataInfoVO.setNbInsuredListVOs(BeanUtils.copyList(NbInsuredListVO.class,
	                    queryNbDataInfo.getNbInsuredListBOs()));
	            // 账户信息
	            qryNbDataInfoVO.setNbPayerAccountVO(BeanUtils.copyProperties(NbPayerAccountVO.class,
	                    queryNbDataInfo.getNbPayerAccountBO()));
	            break;
	        case 2:
	             // 受益人信息
	            qryNbDataInfoVO.setNbContractBeneVOs(BeanUtils.copyList(NbContractBeneVO.class,
	                    queryNbDataInfo.getNbContractBeneBOs()));
	            // 险种信息
	            qryNbDataInfoVO.setNbContractBusiProdVOs(BeanUtils.copyList(NbContractBusiProdVO.class,
	                    queryNbDataInfo.getNbContractBusiProdBOs()));
	            qryNbDataInfoVO.setNbContractMasterVO(BeanUtils.copyProperties(NbContractMasterVO.class,queryNbDataInfo.getNbContractMasterBO()));
            break;
    }
        return qryNbDataInfoVO;
    }
    @Override
    public QryCommonQueryVO queryInfoBySessionId(QryCommonQueryVO qryCommonQueryVO) {
    	QryCommonQueryCompBO qryCommonQueryCompBO = commonQueryService.queryInfoBySessionId(BeanUtils.copyProperties(QryCommonQueryCompBO.class,
    			qryCommonQueryVO));
         return BeanUtils.copyProperties(QryCommonQueryVO.class, qryCommonQueryCompBO);
    }
    public List<ServiceVO> findServiceByPolicyCode(ServiceVO serviceVO) {
        List<ServiceBO> serviceBO = commonQueryService.findServiceByPolicyCode(BeanUtils.copyProperties(
        		ServiceBO.class, serviceVO));
        return BeanUtils.copyList(ServiceVO.class, serviceBO);
    }
    
    /**
	 * 
	 * @description 获取互联网回溯接口参数
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#qeryWebInputTrace(java.lang.String)
	 * @param applyCode 投保单号
	 * @return
	 */
	@Override
	public String queryWebInputTrace(String applyCode) {
		return commonQueryService.queryWebInputTrace(applyCode);
	}
	
	/**
	 * 客户电话变更轨迹
	 */
	@Override
    public CurrentPage getCustomerPhoneChangePath(QueryCustomerPhoneChangePathVO queryCustomerPhoneChangePathVO, CurrentPage currentPage) {
    	QueryCustomerPhoneChangePathBO bo = new QueryCustomerPhoneChangePathBO();
    	bo = BeanUtils.copyProperties(QueryCustomerPhoneChangePathBO.class, queryCustomerPhoneChangePathVO);
    	return commonQueryService.getCustomerPhoneChangePath(bo, currentPage);
    }
	/**
	 * 查询客户电话变更轨迹表的数据条数
	 */
	@Override
	public Integer queryCustomerPhoneChangePathCount(QueryCustomerPhoneChangePathVO queryCustomerPhoneChangePathVO) {
		QueryCustomerPhoneChangePathBO bo = new QueryCustomerPhoneChangePathBO();
    	bo = BeanUtils.copyProperties(QueryCustomerPhoneChangePathBO.class, queryCustomerPhoneChangePathVO);
		return commonQueryService.queryCustomerPhoneChangePathCount(bo);
	}
	/**
	 * 
	 * @description 投资组合账户-保单账户基本信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#getPolicyAccountInfo(com.nci.tunan.qry.interfaces.model.vo.QryAccountVO)
	 * @param accountVO 账户信息VO对象
	 * @return
	 */
	@Override
	public List<QryAccountVO> getPolicyAccountInfo(QryAccountVO accountVO) {
		List<QryAccountBO> qryAccountBOs = commonQueryService.getPolicyAccountInfo(BeanUtils.copyProperties(QryAccountBO.class, accountVO));
		return BeanUtils.copyList(QryAccountVO.class, qryAccountBOs);
	}

	/**
	 * 
	 * @description 投资组合账户-保单账户轨迹记录
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#getPolicyAccountTrailInfo(com.nci.tunan.qry.interfaces.model.vo.QryAccountVO)
	 * @param accountVO 账户信息VO对象
	 * @return
	 */
	@Override
	public CurrentPage getPolicyAccountTrailInfo(
			QryAccountVO accountVO,CurrentPage currentPage) {
		QryAccountBO bo = new QryAccountBO();
        bo = BeanUtils.copyProperties(QryAccountBO.class, accountVO);
        return commonQueryService.getPolicyAccountTrailInfo(bo, currentPage);
	}

	/**
	 * 
	 * @description 投资组合账户基本信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#getCombAccountInfo(com.nci.tunan.qry.interfaces.model.vo.QryAccountVO)
	 * @param accountVO 账户信息VO对象
	 * @return
	 */
	@Override
	public List<QryAccountVO> getCombAccountInfo(QryAccountVO accountVO) {
		List<QryAccountBO> bo = commonQueryService.getCombAccountInfo(BeanUtils.copyProperties(QryAccountBO.class, accountVO));
		return BeanUtils.copyList(QryAccountVO.class, bo);
	}

	/**
	 * 
	 * @description 投资组合账户轨迹信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#getCombAccountTrailInfo(com.nci.tunan.qry.interfaces.model.vo.QryAccountVO, com.nci.udmp.framework.model.CurrentPage)
	 * @param accountVO 账户信息VO对象
	 * @param currentPage 分页信息
	 * @return
	 */
	@Override
	public CurrentPage getCombAccountTrailInfo(QryAccountVO accountVO,
			CurrentPage currentPage) {
		QryAccountBO bo = new QryAccountBO();
        bo = BeanUtils.copyProperties(QryAccountBO.class, accountVO);
        return commonQueryService.getCombAccountTrailInfo(bo, currentPage);
	}
	/**
	 * 
	 * @description 根据保单号查询保单第二投保人信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.ucc.impl.CommonQueryUCCImpl.findSecondPolicyHolder(String)
	 * @param policyCode 保单号
	 * @return SecondPolicyHolderVO
	 */
	@Override
	public SecondPolicyHolderVO findSecondPolicyHolder(String policyCode) {
		SecondPolicyHolderBO secondPolicyHolderBO = new SecondPolicyHolderBO();
		secondPolicyHolderBO.setPolicyCode(policyCode);
		return BeanUtils.copyProperties(SecondPolicyHolderVO.class, commonQueryService.findSecondPolicyHolder(secondPolicyHolderBO));
	}
	
	@Override
	public CsSpecialAccountInfoVO individualPensionAccount(String policyCode) {
		CsSpecialAccountInfoPO csSpecialAccountInfoPO = new CsSpecialAccountInfoPO(); 
		CsSpecialAccountInfoVO csSpecialAccountInfoVO = new CsSpecialAccountInfoVO(); 
		csSpecialAccountInfoPO.setPolicyCode(policyCode);
		csSpecialAccountInfoPO=commonQueryDao.individualPensionAccount(csSpecialAccountInfoPO);
		
		//综合查询有效期至特殊处理：
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date date1 =null;
				if(csSpecialAccountInfoPO.getValidiyEndDate()==null){//如果有效期至为空，则
					//1)官微投保保单：
					ContractMasterPO contractmasterpo = new ContractMasterPO();
					contractmasterpo.setPolicyCode(policyCode);
					//4、查询T_CONTRACT_MASTER表获取保单信息
					contractmasterpo=contractMasterDao.findContractMaster(contractmasterpo);
					if("11".equals(String.valueOf(contractmasterpo.getSubmitChannel()))){//官微渠道
						//投保时交费方式为“不定期交”，“有效期限”为“空”； 
						/*******************************/
						//查询险种表
						ContractBusiProdPO bpPo = new ContractBusiProdPO();
						bpPo.setPolicyCode(policyCode);
						List<ContractBusiProdPO> cpPoList1 = contractBusiProdDao.findAllContractBusiProd(bpPo);
						for(ContractBusiProdPO ContractBusiProdPOList:cpPoList1){
							if("********".equals(ContractBusiProdPOList.getBusiProdCode())
							 ||"00928000".equals(ContractBusiProdPOList.getBusiProdCode())){
								ContractProductPO contractProductPO=new ContractProductPO();
								contractProductPO.setBusiItemId(ContractBusiProdPOList.getBusiItemId());
								contractProductPO.setPolicyId(ContractBusiProdPOList.getPolicyId());
								//9、查询T险种责任组信息
								contractProductPO=contractProductDao.findContractProduct(contractProductPO);
								if("2".equals(String.valueOf(contractProductPO.getChargePeriod()))
								 ||"6".equals(String.valueOf(contractProductPO.getChargePeriod()))
								 ||"7".equals(String.valueOf(contractProductPO.getChargePeriod()))){
									//投保时交费方式为“期交”， “有效期限”为“投保时领取年龄（对应年份） 保单生效对应日 的前一日”；
									//拿到投保时领取年龄（对应年份）
									int CustomerBirthday=Integer.valueOf(sdf.format(contractProductPO.getStartPayDate()).substring(0, 4));
									//2.拿到保单生效日期的月和日减去1天
							          Calendar cal=Calendar.getInstance();
							          cal.setTime(contractmasterpo.getValidateDate());
							          cal.add(Calendar.DATE,-1); //减1天
							          //sdf.format(cal.getTime())
							          sdf.format(cal.getTime()).substring(4, 10);
							          try {
							        	//拿投保时领取年龄（对应年份）拼接保单生效日期的月和日减去1天
							 			 date1 = sdf.parse(String.valueOf(CustomerBirthday)+sdf.format(cal.getTime()).substring(4, 10));
							           } catch (ParseException e) {
							 			e.printStackTrace();
							           }
							          csSpecialAccountInfoPO.setValidiyEndDate(date1);//最终赋值
								}
							}
						}
					}else if("5".equals(String.valueOf(contractmasterpo.getSubmitChannel()))){//新时代渠道
						//2)新时代投保保单：投保时交费方式为“不定期交”，“有效期限”为“空”； 投保时交费方式为“期交”， “有效期限”为“投保时的交费期限+60天宽限期”。
					       //（1）交费期限=当前投保录入日期+投保时“交费期间”-1年；
						//查询险种表
						ContractBusiProdPO bpPo = new ContractBusiProdPO();
						bpPo.setPolicyCode(policyCode);
						List<ContractBusiProdPO> cpPoList1 = contractBusiProdDao.findAllContractBusiProd(bpPo);
						for(ContractBusiProdPO ContractBusiProdPOList:cpPoList1){
							if("********".equals(ContractBusiProdPOList.getBusiProdCode())
							 ||"00928000".equals(ContractBusiProdPOList.getBusiProdCode())){
								ContractProductPO contractProductPO=new ContractProductPO();
								contractProductPO.setBusiItemId(ContractBusiProdPOList.getBusiItemId());
								contractProductPO.setPolicyId(ContractBusiProdPOList.getPolicyId());
								//9、查询T险种责任组信息
								contractProductPO=contractProductDao.findContractProduct(contractProductPO);
								if("2".equals(String.valueOf(contractProductPO.getChargePeriod()))
								 ||"6".equals(String.valueOf(contractProductPO.getChargePeriod()))
								 ||"7".equals(String.valueOf(contractProductPO.getChargePeriod()))){
									//投保时交费方式为“期交”， “有效期限”为“投保时的交费期限+60天宽限期”。
									//（1）交费期限=当前投保录入日期+投保时“交费期间”-1年；
									//投保录入日期取值为保单主表(T_CONTRACT_MASTER)的 APPLY_DATE字段
									int CustomerBirthday=Integer.valueOf(sdf.format(contractmasterpo.getApplyDate()).substring(0, 4));//拿到年份
									//拿到投保时“交费期间”，取值为责任组表(T_CONTRACT_PRODUCT)的缴费年期CHARGE_YEAR
									int chargeyear=contractProductPO.getChargeYear().intValue();
									//拿到计算完成后的年份。
									int endchargeyear=CustomerBirthday+chargeyear-1;
									//拿到计算完成后的年份+月份日份
									String nyrchargeyear=String.valueOf(endchargeyear)+sdf.format(contractmasterpo.getApplyDate()).substring(4, 10);
									
									try {
							            //需要处理一下异常，字符串转日期类型
										date1 = sdf.parse(nyrchargeyear);
							        } catch (ParseException e) {
							            e.printStackTrace();
							        }
									//最后日期+60天
									 Calendar cal=Calendar.getInstance();
							         cal.setTime(date1);
							         cal.add(Calendar.DATE,+60); //加60天
							         csSpecialAccountInfoPO.setValidiyEndDate(cal.getTime());//最终赋值
									
								}else if("********".equals(ContractBusiProdPOList.getBusiProdCode())&&"8".equals(String.valueOf(contractProductPO.getChargePeriod()))){
									//（2）9281产品的投保时“交费期间”选择“交至领取年龄保单生效对应日前一日”时，则交费期间=养老年金开始领取日期选择的年龄-被保险人年龄
									//养老年金开始领取日期选择的年龄 取值为责任组表(T_CONTRACT_PRODUCT)的START_PAY_AGE字段
									//被保险人年龄取值为被保人表(T_INSURED_LIST)的INSURED_AGE字段
									InsuredListPO insuredListPO = new InsuredListPO();
									insuredListPO.setPolicyCode(policyCode);
									insuredListPO = insuredListDao.findInsuredList(insuredListPO);
									
									//交费期间=养老年金开始领取日期选择的年龄-被保险人年龄
									int StartPayAge= contractProductPO.getStartPayAge().subtract(insuredListPO.getInsuredAge()).intValue();
									//拿到年份
									int CustomerBirthday=Integer.valueOf(sdf.format(contractmasterpo.getApplyDate()).substring(0, 4));
									
									//拿到计算完成后的年份。
									int endchargeyear=CustomerBirthday+StartPayAge;
									
									//拿到计算完成后的年份+月份日份
									String nyrchargeyear=String.valueOf(endchargeyear)+sdf.format(contractmasterpo.getApplyDate()).substring(4, 10);
									
									try {
							            //需要处理一下异常，字符串转日期类型
										date1 = sdf.parse(nyrchargeyear);
							        } catch (ParseException e) {
							            e.printStackTrace();
							        }
									//最后日期+60天
									 Calendar cal=Calendar.getInstance();
							         cal.setTime(date1);
							         cal.add(Calendar.DATE,+60); //加60天
							         csSpecialAccountInfoPO.setValidiyEndDate(cal.getTime());//最终赋值
								}
							}
						}
					}
					
				}
		
		CsSpecialAccountInfoBO csSpecialAccountInfoBO = BeanUtils.copyProperties(CsSpecialAccountInfoBO.class,csSpecialAccountInfoPO);
		csSpecialAccountInfoVO=BeanUtils.copyProperties(CsSpecialAccountInfoVO.class,csSpecialAccountInfoBO);
		return csSpecialAccountInfoVO;
	}
	/**
	 * 投保单风险告知问卷
	 */
	@Override
	public PolicyRiskQuestionnaireVO queryRiskQuestionnaire(NbContractMasterVO contractMasterVO) {
		return BeanUtils.copyProperties(PolicyRiskQuestionnaireVO.class,commonQueryService.queryRiskQuestionnaire(BeanUtils.copyProperties(PolicyRiskQuestionnaireBO.class,contractMasterVO)));
	}
	   /**
     * 通过代理人编码查询代理人信息(AGENT_CODE)。
     * 
     */
    @Override
    public AgentCompBO findAgentByAgentCodeCard(AgentCompBO agentCompBO){
    	AgentCompBO agentBO = commonQueryService.findAgentByAgentCodeCard(BeanUtils.copyProperties(AgentCompBO.class, agentCompBO));
        return BeanUtils.copyProperties(AgentCompBO.class, agentBO);
    }
	/**
	 * @description 通知书查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see 
	 * @param currentPage分页对象
	 * @param documentVO通知书对象
	 * @return
	 */
	@Override
	public CurrentPage<DocumentVO> findDocumentInfos(CurrentPage currentPage,DocumentVO documentVO){
		documentVO.setBussSourceCode("002");
//		return BeanUtils.copyCurrentPage(DocumentVO.class,commonQueryService.findDocumentInfos(currentPage,BeanUtils.copyProperties( DocumentBO.class,documentVO)));
		CurrentPage<DocumentVO> currentPageo = BeanUtils.copyCurrentPage(DocumentVO.class,commonQueryService.findDocumentInfos(currentPage,BeanUtils.copyProperties( DocumentBO.class,documentVO)));
		List<DocumentVO> documentVOs = currentPageo.getPageItems();
		String sendContentStr = "";
		for (int i = 0; i < documentVOs.size();i++ ) {
			DocumentVO documentVOo = documentVOs.get(i);
			if (null == documentVOo || StringUtils.isEmpty(documentVOo.getDocumentNo())) {
				continue;
			}
			String customerName = "";
			// 新建一个集合，用来保存居民纳税声明确认通知书所勾选下发的客户
			Set<String> customerNameSet = new HashSet<String>();
			if (DictionaryUtil.TEMPLATE_CODE_SURVIVAL.equals(documentVOo.getTemplateCode())) { // 生调
				SurveyApplyBO surveyApply = new SurveyApplyBO();
				surveyApply.setSurveyDocId(documentVOo.getDocListId());
				surveyApply = surveyApplyService.querySurveyMessage(surveyApply);
				
				TablePrevThreadLocal.setTABLEPREV("DEV_UW.");
				SurveyQualityEvaluateBO surveyQualityEvaluateBO = new SurveyQualityEvaluateBO();
				SurveyApplyVO surveyVO = new SurveyApplyVO();
				surveyVO.setSurveyDocId(documentVOo.getDocListId());
				surveyVO = BOServiceFactory.getSurveyApplyUCC().findSurveyApply(surveyVO);// 调查申请表
				if (surveyVO.getApplyId() != null) {
					surveyQualityEvaluateBO.setApplyId(surveyVO.getApplyId());
					surveyQualityEvaluateBO = BOServiceFactory.getSurveyApplyBOService().findSurveyQualityEvaluateByApplyId(
							surveyQualityEvaluateBO);
				}
				
				SurveyConclusionBO surveyConclusionBO = new SurveyConclusionBO();
				if (surveyApply.getApplyId() != null) {
					surveyConclusionBO.setApplyId(surveyApply.getApplyId());
					surveyConclusionBO = BOServiceFactory.getSurveyApplyBOService().findSurveyConclusionByApplyId(
							surveyConclusionBO);
					documentVOo.setCloseTime(surveyConclusionBO.getFinishDate());// 生调通知书的回销时间
				}
				
				sendContentStr = surveyApply.getExamineCode();
			} else if (DictionaryUtil.TEMPLATE_CODE_PENOTICE.equals(documentVOo.getTemplateCode())) { // 体检
				PenoticeBO penoticeVO = new PenoticeBO();
				penoticeVO.setDocListId(documentVOo.getDocListId());
				penoticeVO = iPenoticeService.queryPenoticeByDocListId(penoticeVO);
				
				if (penoticeVO.getPenoticeId() != null) {
					PenoticeDetailBO penoticeDetailVO = new PenoticeDetailBO();
					penoticeDetailVO.setPenoticeId(penoticeVO.getPenoticeId());
					List<PenoticeDetailBO> penoticeDetailVOs = penoticeDetailService.queryPeitemNameByPenoticeId(penoticeDetailVO);

					for (PenoticeDetailBO penoticeDetailVOo : penoticeDetailVOs) {
						if(StringUtils.isNotBlank(penoticeDetailVOo.getOtherContent())){
							sendContentStr += penoticeDetailVOo.getPeitemName() + "(" + penoticeDetailVOo.getOtherContent() + "),";
						} else {
							sendContentStr += penoticeDetailVOo.getPeitemName() + ",";
						}
						
					}
					if(StringUtils.isNotBlank(penoticeVO.getOtherComments())){
						sendContentStr += penoticeVO.getOtherComments() + ",";
					}
					if (StringUtil.isNotBlank(sendContentStr)) {
						sendContentStr = sendContentStr.substring(0, sendContentStr.length() - 1);
					}
				}
			} else if (DictionaryUtil.TEMPLATE_CODE_ASKFOR.equals(documentVOo.getTemplateCode())
					|| UwDocumentNO.UW_WJ_DOC_NO.equals(documentVOo.getTemplateCode())) {
				AskforinfoBO tAskforinfoVO = new AskforinfoBO();
				tAskforinfoVO.setDocListId(documentVOo.getDocListId());
				tAskforinfoVO = uwAskforinfoService.queryAskforinfoByDocListId(tAskforinfoVO);

				// 新建一个集合，用来保存问卷通知书所勾选下发的客户
				if (tAskforinfoVO.getAskforinfoId() != null) {
					AskforinfoDetailBO tAskforinfoDetailVO = new AskforinfoDetailBO();
					tAskforinfoDetailVO.setAskforinfoId(tAskforinfoVO.getAskforinfoId());
					List<AskforinfoDetailBO> tAskforinfoDetailVOS = uwAskforinfoDetailService.queryAskforinfoDetailByAskforinfoId(tAskforinfoDetailVO);

					for (AskforinfoDetailBO askforinfoDetailVO : tAskforinfoDetailVOS) {
						sendContentStr += askforinfoDetailVO.getQuestionContent() + ",";
						if (StringUtils.isNotEmpty(askforinfoDetailVO.getCustomerName())) {
							// 目前只有问卷通知书下发后，detail表保存了客户id
							customerNameSet.add(askforinfoDetailVO.getCustomerName());
						}
					}
					if (StringUtil.isNotBlank(sendContentStr)) {
						sendContentStr = sendContentStr.substring(0, sendContentStr.length() - 1);
					}
				}
			} else if("UWS_00011".equals(documentVOo.getTemplateCode())){
				ResidentInfoBO residentInfoBO = new ResidentInfoBO();
				residentInfoBO.setDocListId(documentVOo.getDocListId());
				residentInfoBO = uwAskforinfoService.queryResidentInfoByDocListId(residentInfoBO);
				sendContentStr = "采集人员声明确认信息";
				if (residentInfoBO.getResidentinfoId() != null) {
					ResidentInfoDetailBO residentInfoDetailBO = new ResidentInfoDetailBO();
					residentInfoDetailBO.setResidentinfoId(residentInfoBO.getResidentinfoId());
					List<ResidentInfoDetailBO> residentInfoDetailBOs = uwAskforinfoService.findResidentinfoDetailById(residentInfoDetailBO);
					if (residentInfoDetailBOs != null && residentInfoDetailBOs.size() > DictionaryUtil.TRANS0) {
						for (ResidentInfoDetailBO residentInfoDetailBO2 : residentInfoDetailBOs) {
							// 目前居民纳税声明通知书下发后，detail表保存了客户id
							if (StringUtils.isNotEmpty(residentInfoDetailBO2.getCustomerName())) {
								customerNameSet.add(residentInfoDetailBO2.getCustomerName());
							}
						}
					}
				}
			} else if(UwDocumentNO.UW_HC_DOC_NO.equals(documentVOo.getTemplateCode())){//上海医保卡核保通知书
				HcMedicalBO medicalBO = new HcMedicalBO();
				medicalBO.setDoc_list_id(documentVOo.getDocListId());
				medicalBO.setDocument_no(documentVOo.getDocumentNo());
				medicalBO = uwAskforinfoService.queryHealthcareInfo(medicalBO);
				if(null != medicalBO && null != medicalBO.getMedical_id()){
					sendContentStr = medicalBO.getDocument_desc();
				}
			}  else if (UwDocumentNO.DECISION_DOCUMENT_LIST.contains(documentVOo.getTemplateCode())) {
				// 核保结论类通知书
				UwDocumentVerifyBO uwDocumentVerifyBO = new UwDocumentVerifyBO();
				uwDocumentVerifyBO.setDocumentNo(documentVOo.getDocumentNo());
				List<UwDocumentVerifyBO> uwDocumentVerifyBOs = uwAskforinfoService.findAllUwDocumentVerify(uwDocumentVerifyBO);
				if (uwDocumentVerifyBOs != null && uwDocumentVerifyBOs.size() > DictionaryUtil.TRANS0) {
					for (UwDocumentVerifyBO uwDocumentVerifyBO1 : uwDocumentVerifyBOs) {
						// 目前居民纳税声明通知书下发后，detail表保存了客户id
						if (StringUtils.isNotEmpty(uwDocumentVerifyBO1.getCustomerName())) {
							customerNameSet.add(uwDocumentVerifyBO1.getCustomerName());
						}
					}
				}
			}
			
			if (customerNameSet != null && customerNameSet.size() > DictionaryUtil.TRANS0) {
				// 发放对象字段：对于不同的客户下发了一份通知书，发放对象应展示所有发放对象的姓名，多个姓名之间用“、”隔开
				for (String name : customerNameSet) {
					customerName += name + "、";
				}
				customerName = customerName.substring(DictionaryUtil.TRANS0, customerName.length() - DictionaryUtil.TRANS1);
			}
			
			if (StringUtils.isNotEmpty(customerName)) {
				documentVOo.setSomeName(customerName);
			}
			documentVOo.setSendData(sendContentStr);
			sendContentStr = "";
		}
		
		return currentPageo;
	}

	@Override
	public List<PolicyRiskQuestionnaireVO> queryAllRiskQuestionnaire(PolicyRiskQuestionnaireVO policyRiskQuestionnaireVO,Set<String> proCode) {
		return BeanUtils.copyList(PolicyRiskQuestionnaireVO.class, commonQueryService.queryAllRiskQuestionnaire(BeanUtils.copyProperties(PolicyRiskQuestionnaireBO.class,policyRiskQuestionnaireVO),proCode));
	}

	@Override
	public List<QryNotifyInfoListVO> findNotifyInfo(QryPolicyInfoVO policyInfoVO) {
		List<QryNotifyInfoListBO> findNotifyInfo = commonQueryService.findNotifyInfo(BeanUtils.copyProperties(QryContractMasterCompBO.class,policyInfoVO));
		return BeanUtils.copyList(QryNotifyInfoListVO.class, findNotifyInfo);
	}

	@Override
	public String findNotificationReceiveMethod(QryPolicyInfoVO policyInfoVO) {
		String findNotificationReceiveMethod = commonQueryService.findNotificationReceiveMethod(BeanUtils.copyProperties(QryContractMasterCompBO.class,policyInfoVO));
		return findNotificationReceiveMethod;
	}

	@Override
	public List<QryNotifyInfoDetailVO> findNotifyInfoDetail(
			QryNotifyInfoDetailVO notifyInfoDetailVO) {
		List<QryNotifyInfoDetailBO> findNotifyInfoDetail = commonQueryService.findNotifyInfoDetail(BeanUtils.copyProperties(QryNotifyInfoDetailBO.class,notifyInfoDetailVO));
		return BeanUtils.copyList(QryNotifyInfoDetailVO.class, findNotifyInfoDetail);
	}

	@Override
	public List<QryNotifyInfoLogVO> findNotifyInfoLog(
			QryNotifyInfoLogVO notifyInfoLogVO) {
		List<QryNotifyInfoLogBO> findNotifyInfoLog = commonQueryService.findNotifyInfoLog(BeanUtils.copyProperties(QryNotifyInfoLogBO.class,notifyInfoLogVO));
		return BeanUtils.copyList(QryNotifyInfoLogVO.class, findNotifyInfoLog);
	}
	
	/**
	 * @description 险种信息查询
	 * @version
	 * @return 
	 */
	@Override
	public List<BusiProdInfoVO> findBusiProdInfos(BusiProdInfoVO busiProdInfoVO) {
		return BeanUtils.copyList(BusiProdInfoVO.class, commonQueryService.findBusiProdInfos(BeanUtils.copyProperties(BusiProdInfoBO.class,busiProdInfoVO)));
	}
	
    /**
     * 
     * @description 根据保单号查询保单状态信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#findStatusInfoByCode(com.nci.tunan.qry.interfaces.model.vo.PolicyStatusVO)
     * @param policyStatusVO 保单状态信息VO对象
     * @return PolicyStatusVO
     */
    @SuppressWarnings("unchecked")
	@Override
	public CurrentPage<PolicyStatusVO> findStatusByPolicyCode(PolicyStatusVO policyStatusVO,CurrentPage currentPage) {
    	 LOGGER.debug("<======CommonQueryUCCImpl--findStatusByPolicyCode======>");
    	 PolicyStatusBO policyStatusBO = BeanUtils.copyProperties(PolicyStatusBO.class, policyStatusVO);
    	 
         return BeanUtils.copyCurrentPage(PolicyStatusVO.class, commonQueryService.findPolicyStatusByCode(policyStatusBO,currentPage));
	}
    /**
	 * 
	 * @description 查询常量表信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#findAllConstantsInfo(com.nci.tunan.qry.interfaces.model.nb.vo.NbContractMasterVO)
	 * @param constantsInfoVO 常量表数据
	 * @return
	 */
	@Override
	public List<NbContractMasterVO> findAllConstantsInfo(NbContractMasterVO constantsInfoVO) {
		List<NbContractMasterBO> findAllConstantsInfo = commonQueryService.findAllConstantsInfo(BeanUtils.copyProperties(NbContractMasterBO.class,constantsInfoVO));
		return BeanUtils.copyList(NbContractMasterVO.class, findAllConstantsInfo);
	}
	/**145832 查询续期年金生存调查确认生存信息
	 * @param payPlanDueVO
	 * @return
	 */
	@Override
	public List<SurvInvCfmInfoVO> getSurvInvCfmInfoPage(QryPayPlanDueVO payPlanDueVO){
		
		List<SurvInvCfmInfoBO> survInvCfmInfoList = commonQueryService.getSurvInvCfmInfoPage(BeanUtils.copyProperties(QryPayPlanDueBO.class,payPlanDueVO));
		return BeanUtils.copyList(SurvInvCfmInfoVO.class, survInvCfmInfoList);
	}
	/**145832查询生存调查验真信息
	 * @param identityCheckInfoVO
	 * @return
	 */
	public IdentityCheckInfoVO getIdentityCheckInfo(IdentityCheckInfoVO identityCheckInfoVO){
		IdentityCheckInfoBO identityCheckInfoBO = commonQueryService.getIdentityCheckInfo(BeanUtils.copyProperties(IdentityCheckInfoBO.class,identityCheckInfoVO));
		return BeanUtils.copyProperties(IdentityCheckInfoVO.class, identityCheckInfoBO);
	}
	/**
	 * 
	 * @description 根据5要素查询客户税收居民身份
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.qry.ucc.ICommonQueryUCC#queryCustomerTaxType(com.nci.tunan.qry.interfaces.model.nb.vo.CustomerVO)
	 * @param customerVO 客户5要素
	 * @return
	 */
	@Override
	public CustomerVO queryCustomerTaxType(CustomerVO customerVO) {
		CustomerBO customerBO = BeanUtils.copyProperties(CustomerBO.class,customerVO);
		customerBO.setCustomerCertiCode(customerVO.getCustomerCertCode());
		CustomerBO queryCustomerTaxType = commonQueryService.queryCustomerTaxType(customerBO);
		return BeanUtils.copyProperties(CustomerVO.class,queryCustomerTaxType);
	}
	public ISurveyApplyService getSurveyApplyService() {
		return surveyApplyService;
	}

	public void setSurveyApplyService(ISurveyApplyService surveyApplyService) {
		this.surveyApplyService = surveyApplyService;
	}

	public IPenoticeService getiPenoticeService() {
		return iPenoticeService;
	}

	public void setiPenoticeService(IPenoticeService iPenoticeService) {
		this.iPenoticeService = iPenoticeService;
	}

	public IPenoticeDetailService getPenoticeDetailService() {
		return penoticeDetailService;
	}

	public void setPenoticeDetailService(
			IPenoticeDetailService penoticeDetailService) {
		this.penoticeDetailService = penoticeDetailService;
	}

	public IUWAskforinfoService getUwAskforinfoService() {
		return uwAskforinfoService;
	}

	public void setUwAskforinfoService(IUWAskforinfoService uwAskforinfoService) {
		this.uwAskforinfoService = uwAskforinfoService;
	}

	public IUWAskforinfoDetailService getUwAskforinfoDetailService() {
		return uwAskforinfoDetailService;
	}

	public void setUwAskforinfoDetailService(
			IUWAskforinfoDetailService uwAskforinfoDetailService) {
		this.uwAskforinfoDetailService = uwAskforinfoDetailService;
	}
	 /**
	  *  #160232 支持互联网渠道新增慧择平台出单渠道信息查询需求-新契约 
	  * 
	  * */
	 public NbContractMasterVO queryCooperationProtocolIdByPolicyCode(NbContractMasterVO nbContractMasterVO){
		 NbContractMasterBO nbContractMasterBO = commonQueryService.queryCooperationProtocolIdByPolicyCode(BeanUtils.copyProperties(
	                NbContractMasterBO.class, nbContractMasterVO));
		 NbContractMasterVO nbContractMasterVOCoo = BeanUtils.copyProperties(
				 NbContractMasterVO.class, nbContractMasterBO);
		 return nbContractMasterVOCoo;
	 }
	/**
	 * 
	 * @description 通过投保单号查询投保人问卷信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.nb.impl.process.ucc.IInsuranceEnterUCC#queryEvaluateAnswerByApployCode(nbEvaluateAnswerVO)
	 * @param nbEvaluateAnswerVO
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<NbEvaluateAnswerVO> queryEvaluateAnswerByApployCode(NbEvaluateAnswerVO nbEvaluateAnswerVO) {
		NbEvaluateAnswerBO nbEvaluateAnswerBO=BeanUtils.copyProperties(NbEvaluateAnswerBO.class, nbEvaluateAnswerVO);
		List<NbEvaluateAnswerBO> list=nbEvaluateAnswerService.findNbEvaluateAnswer(nbEvaluateAnswerBO);
		return BeanUtils.copyList(NbEvaluateAnswerVO.class, list);
	}
}
