package com.nci.tunan.qry.impl.peripheral.service.r06401001837.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.nci.tunan.qry.interfaces.model.po.ContractAgentExtPO;
import com.nci.core.common.impl.util.CodeMapperUtils;
import com.nci.tunan.qry.dao.AppentsDao;
import com.nci.tunan.qry.dao.IAgentDao;
import com.nci.tunan.qry.dao.IContractAgentDao;
import com.nci.tunan.qry.dao.IPAContractMasterDao;
import com.nci.tunan.qry.dao.IQryBankDao;
import com.nci.tunan.qry.dao.IQuestionCall;
import com.nci.tunan.qry.dao.InsureQueryDao;
import com.nci.tunan.qry.dao.InsuredInfosDao;
import com.nci.tunan.qry.dao.InsuresdDao;
import com.nci.tunan.qry.dao.IquryContsDao;
import com.nci.tunan.qry.dao.IriskInfoDao;
import com.nci.tunan.qry.impl.peripheral.service.r06401001837.IPaDetailQueryService;
import com.nci.tunan.qry.interfaces.model.nb.po.ContractAgentPO;
import com.nci.tunan.qry.interfaces.model.po.AgentPO;
import com.nci.tunan.qry.interfaces.model.po.AppntsPo;
import com.nci.tunan.qry.interfaces.model.po.InsuredInfosPo;
import com.nci.tunan.qry.interfaces.model.po.InsuredsPo;
import com.nci.tunan.qry.interfaces.model.po.PAContractMasterPO;
import com.nci.tunan.qry.interfaces.model.po.PaQuestionaireCustomerPO;
import com.nci.tunan.qry.interfaces.model.po.RiskInfoPo;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.AgentImparts;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.AppntImparts;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.Appnts;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.Conts;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.InputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.InsuredImparts;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.InsuredInfos;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.Insureds;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.OutputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.vo.Risks;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.lang.DateUtilsEx;

public class PaDetailQueryServiceImpl implements IPaDetailQueryService{
	
	
   private IPAContractMasterDao paContractMasterDao ;
	  
	private IContractAgentDao contractAgentDAO;
	  
	public IContractAgentDao getContractAgentDao() {
		return contractAgentDAO;
	}

	public void setContractAgentDao(IContractAgentDao contractAgentDao) {
		this.contractAgentDAO = contractAgentDao;
	}

	private IAgentDao  iAgentDao ;
	  
	private InsureQueryDao insureQueryDao;
	  
	private  IQryBankDao iQryBankDao;
	          
	private IriskInfoDao iriskInfoDao;
	           
	private AppentsDao   appentsDao;
	
	private IQuestionCall iQuestionCall;
	
	private InsuresdDao insuresdDao;
	//被保人详信息;
	private InsuredInfosDao insuredInfosDao;
	
	//
	private IquryContsDao iquryContsDao;
	
	
	public void setIquryContsDao(IquryContsDao iquryContsDao) {
		this.iquryContsDao = iquryContsDao;
	}

	public void setInsuredInfosDao(InsuredInfosDao insuredInfosDao) {
		this.insuredInfosDao = insuredInfosDao;
	}

	public void setInsuresdDao(InsuresdDao insuresdDao) {
		this.insuresdDao = insuresdDao;
	}

	public void setiQuestionCall(IQuestionCall iQuestionCall) {
		this.iQuestionCall = iQuestionCall;
	}

	public void setAppentsDao(AppentsDao appentsDao) {
		this.appentsDao = appentsDao;
	}

	public IPAContractMasterDao getPaContractMasterDao() {
		return paContractMasterDao;
	}

	public void setPaContractMasterDao(IPAContractMasterDao paContractMasterDao) {
		this.paContractMasterDao = paContractMasterDao;
	}


	public IAgentDao getiAgentDao() {
		return iAgentDao;
	}
							
	public void setiAgentDao(IAgentDao iAgentDao) {
		this.iAgentDao = iAgentDao;
	}

	public InsureQueryDao getInsureQueryDao() {
		return insureQueryDao;
	}


	public void setInsureQueryDao(InsureQueryDao insureQueryDao) {
		this.insureQueryDao = insureQueryDao;
	}


	public IQryBankDao getiQryBankDao() {
		return iQryBankDao;
	}

	public void setiQryBankDao(IQryBankDao iQryBankDao) {
		this.iQryBankDao = iQryBankDao;
	}

	public IriskInfoDao getIriskInfoDao() {
		return iriskInfoDao;
	}


	public void setIriskInfoDao(IriskInfoDao iriskInfoDao) {
		this.iriskInfoDao = iriskInfoDao;
	}

	//保单明细查询;
	@Override
	public OutputData findPadetailByPolicyId(InputData inputDataVo) /*throws ApplyCodeExpection*/ {
		  
	     OutputData outputData=new OutputData();
	     //根据保单号查询承保的
		 //List<PAContractMasterPO> contractMaster = paContractMasterDao.findContractMasterByPolicyId(contractMasterPO);
		
		if("1".equals(inputDataVo.getNumType())){//保单号
			PAContractMasterPO contractMasterPA = new PAContractMasterPO();
		    contractMasterPA.setPolicyCode(inputDataVo.getPrtNo());
		    //根据保单号查询承保的;
			List<PAContractMasterPO> contractMaster = paContractMasterDao.findContractMasterByPolicyId(contractMasterPA);
			outputData = buildChengBao(contractMaster);
			System.out.println("承保下查询");
		}else{//投保单号
			PAContractMasterPO contractMasterNB = new PAContractMasterPO();
			contractMasterNB.setApplyCode(inputDataVo.getPrtNo());
			List<PAContractMasterPO> NbContractMaster = paContractMasterDao.findNbContractMasterByApplyCode(contractMasterNB);
		    if(NbContractMaster!=null&&NbContractMaster.size()>0){
		    	//非承保;
		    	outputData = buildFeiChengBao(NbContractMaster);
		    	System.out.println("非承保下查询");
		    }else{
		    	
		    return null;
		    }
			
		}
		return outputData;
	    
	}
	//承保查询;
	private OutputData buildChengBao(List<PAContractMasterPO> contractMaster){
	
	    OutputData outputData = new OutputData();
		//conts节点;
		List<Conts> conts = buildConts(contractMaster);
		//appnts节点;
		List<Appnts> appnts = buildAppnts(contractMaster);
		//conts节点;
		//List<Conts> conts = buildConts(contractMaster);
		//Insureds节点;
		List<Insureds> insureds = buildInsureds(contractMaster);
		//险种信息;
		for (PAContractMasterPO contract: contractMaster) {
			//查询险种信息;
			RiskInfoPo riskInfoPo=new RiskInfoPo();
			riskInfoPo.setPolicyId(contract.getPolicyId());
			System.out.println(contract.getPolicyId()+"保单Id");
			List<RiskInfoPo> riskList =iriskInfoDao.findContractRiskByPolicyId(riskInfoPo);
			List<Risks> listRisks=new ArrayList<Risks>();
			if(listRisks!=null&&riskList.size()>0){
				//System.out.println(riskList.size()+"==========");
				for(RiskInfoPo riskInfo:riskList){
					Risks risks=new Risks();
					//险种编码;
					risks.setRiskcode((riskInfo.getBusiProdCode()==null?"":riskInfo.getBusiProdCode()));
					//险种名称;
					risks.setRiskname((riskInfo.getProductNameSys()==null?"":riskInfo.getProductNameSys()));
					//保费;
					risks.setPrem(null==riskInfo.getTotalPremAf()?"":riskInfo.getTotalPremAf().toString());
					//保额
					risks.setAmnt((riskInfo.getAmount()==null?"":riskInfo.getAmount()).toString());
					listRisks.add(risks);
				}
			}
			outputData.setRisks(listRisks);
			outputData.setConts(conts);
			outputData.setAppnts(appnts);
			outputData.setInsureds(insureds);
		}
		return outputData;
	}
	
	//Insureds节点 (被保人)
	
	private List<Insureds> buildInsureds(List<PAContractMasterPO> contractMaster) {
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); 
	    List<Insureds> listInsureds=new ArrayList<Insureds>();
	    for (PAContractMasterPO contract: contractMaster) {
	    	InsuredsPo insuredsPo=new InsuredsPo();
	    	insuredsPo.setPolicyId(contract.getPolicyId());
	    	//查询被保人
	    	List<InsuredsPo> listInsuredsInfo = insuresdDao.findInsuredByPolicyId(insuredsPo); 
	    	if(listInsuredsInfo!=null&&listInsuredsInfo.size()>0){
	    		for(InsuredsPo po:listInsuredsInfo){
		    		Insureds nsureds=new Insureds();
		    		//客户号
		    		nsureds.setCustomerNo((po.getCustomerId()==null?"": po.getCustomerId()).toString());
		    		//姓名
		    		nsureds.setName((po.getCustomerName()==null?"":po.getCustomerName()));
		    		//性别
		    		//nsureds.setSex((po.getCustomerGender()==null?"":po.getCustomerGender()).toString());
		    		//码制转换;
		    		if(po.getCustomerGender()!=null&&!po.getCustomerGender().equals("")){
		    			nsureds.setSex(CodeMapperUtils.getOldCodeByNewCode("GENDER",po.getCustomerGender().toString(),"PAS"));
		    		}else{
		    			nsureds.setSex("");
		    		}
		    		//出生日期
		    		nsureds.setBirthday((po.getCustomerBirthday()==null?"":sdf.format(po.getCustomerBirthday())).toString());
		    		//与第一被保险人关系
		    		//nsureds.setRelationToMainInsured((po.getRelationToInsured1()==null?"":po.getRelationToInsured1()));
		    		//码制转换;
		    		if(po.getRelationToInsured1()!=null&&!po.getRelationToInsured1().equals("")){
		    		 nsureds.setRelationToMainInsured(CodeMapperUtils.getOldCodeByNewCode("LA_PH_RELA",po.getRelationToInsured1(),"PAS"));
		    		}else{
		    		 nsureds.setRelationToMainInsured("");	
		    		}
		    		//客户内部号码
		    		nsureds.setSequenceNo((po.getInnerCustomerId()==null?"":po.getInnerCustomerId()).toString());
		    		//查询被保人详细信息;
		    		InsuredInfosPo insuredInfosPo=new InsuredInfosPo();
		    		List<InsuredInfos> listInsuredInfos2=new ArrayList<InsuredInfos>();
		    		insuredInfosPo.setPolicyId(contract.getPolicyId());
		    		  List<InsuredInfosPo> findListInsuredInfos = insuredInfosDao.findListInsuredInfosByPolicyId(insuredInfosPo);
		    			 if(findListInsuredInfos!=null&&findListInsuredInfos.size()>0){
		    				 for(InsuredInfosPo infos:findListInsuredInfos){
		    					 InsuredInfos showInsuredInfos=new  InsuredInfos();
		    					//VIP客户
		    					 showInsuredInfos.setVipvalue((infos.getCustomerVip()==null?"":infos.getCustomerVip()).toString());
		    				    //客户内部号码 
		    					 showInsuredInfos.setSequenceNo((infos.getInnerCustomerId()==null?"":infos.getInnerCustomerId()).toString());
		    					//与第一被保险人关系 
		    					//showInsuredInfos.setRelationToMainInsured((infos.getRelationToInsured1()==null?"":infos.getRelationToInsured1()));
		    					//码制转换;
		    			    		if(po.getRelationToInsured1()!=null&&!po.getRelationToInsured1().equals("")){
		    			    		 nsureds.setRelationToMainInsured(CodeMapperUtils.getOldCodeByNewCode("LA_PH_RELA",po.getRelationToInsured1(),"PAS"));
		    			    		}else{
		    			    		 nsureds.setRelationToMainInsured("");	
		    			    		}
		    					//与投保人关系
		    					//showInsuredInfos.setRelationShip((infos.getRelationToPh()==null?"":infos.getRelationToPh()));
		    			    		 if(infos.getRelationToPh()!=null&&!infos.getRelationToPh().equals("")){
			    						 showInsuredInfos.setRelationShip(CodeMapperUtils.getOldNameByNewCode("RELATIONSHIP",infos.getRelationToPh(),"PAS"));
			    					 }else{
			    						 showInsuredInfos.setRelationShip("");
			    					 }
		    					 //姓名
		    					showInsuredInfos.setName((infos.getCustomerName()==null?"":infos.getCustomerName()));
		    					 //证件类型
		    					//showInsuredInfos.setiDType((infos.getCustomerCertType()==null?"":infos.getCustomerCertType()));
			  					 if(infos.getCustomerCertType()!=null&&!infos.getCustomerCertType().equals("")){
			  					 showInsuredInfos.setiDType(CodeMapperUtils.getOldCodeByNewCode("CERTI_TYPE",infos.getCustomerCertType(),"PAS"));	 
			  					 }else{
			  					 showInsuredInfos.setiDType("");	 
			  					 }
		    					//证件号码
		    					showInsuredInfos.setiDNo((infos.getCustomerCertiCode()==null?"":infos.getCustomerCertiCode()));
			  					  //证件有效期起
		    					showInsuredInfos.setiDEffStartDate((infos.getCustCertStarDate()==null?"":sdf.format(infos.getCustCertStarDate())).toString());
			  					  //证件有效期止
		    					showInsuredInfos.setiDEffEndDate((infos.getCustCertEndDate()==null?"": sdf.format(infos.getCustCertEndDate())).toString());
			  					  //国籍
		    					//showInsuredInfos.setNativePlace((infos.getCountryCode()==null?"":infos.getCountryCode()));
			  					if(infos.getCountryCode()!=null&&!infos.getCountryCode().equals("")){
			  					 showInsuredInfos.setNativePlace(CodeMapperUtils.getOldCodeByNewCode("COUNTRY",infos.getCountryCode(),"NBS"));	
			  					}else{
			  					 showInsuredInfos.setNativePlace("");	
			  					}
		    					//性别
		    					//showInsuredInfos.setSex((infos.getCustomerGender()==null?"": infos.getCustomerGender()).toString());
			  				   //码制转换;
					    		if(infos.getCustomerGender()!=null&&!infos.getCustomerGender().equals("")){
					    			showInsuredInfos.setSex(CodeMapperUtils.getOldCodeByNewCode("GENDER",infos.getCustomerGender().toString(),"PAS"));
					    		}else{
					    			showInsuredInfos.setSex("");
					    		}
			  					//出生日期
		    					showInsuredInfos.setBirthday((infos.getCustomerBirthday()==null?"": sdf.format(infos.getCustomerBirthday())).toString());
			  					  //投保人年龄
		    					//showInsuredInfos.setAge((infos.getInsuredAge()==null?"":infos.getInsuredAge()).toString());
		    					if(!"".equals(infos.getCustomerBirthday())&&null!=infos.getCustomerBirthday()){
		  						  double age=DateUtilsEx.getYearAmount(infos.getCustomerBirthday(), WorkDateUtil.getWorkDate());
		  						  age=Math.floor(age);
		  						  int a=(int)age;
		  						showInsuredInfos.setAge(a+"");
		  					  }else{
		  						showInsuredInfos.setAge(""); 
		  					  }
		    					
		    					//婚姻状况
		    					showInsuredInfos.setMarriage((infos.getMarriageStatus()==null?"":infos.getMarriageStatus()));
			  					   //子女状况
		    					showInsuredInfos.setChildStatus((infos.getIsParent()==null?"":infos.getIsParent()).toString());
			  					  //职业编码
		    					showInsuredInfos.setOccupationCode((infos.getJobCode()==null?"":infos.getJobCode()));
			  					  //职业类别
		    					//showInsuredInfos.setOccupationType((infos.getJobNature()==null?"":infos.getJobNature()));
			  					//码制转换
		    					if(infos.getJobNature()!=null&&!infos.getJobNature().equals("")){
		    						showInsuredInfos.setOccupationType(CodeMapperUtils.getOldCodeByNewCode("JOB_UNDERWRITE",infos.getJobNature(),"PAS"));	
		    					}else{
		    						showInsuredInfos.setOccupationType("");
		    					}
		    					//职务
		    					//showInsuredInfos.setOffice((infos.getJobTitle()==null?"":infos.getJobTitle()));
			  					//码制转换;
		    					if(infos.getJobTitle()!=null&&!infos.getJobTitle().equals("")){
			  						showInsuredInfos.setOffice(CodeMapperUtils.getOldCodeByNewCode("JOB_TITLE",infos.getJobTitle(),"NBS"));	 
			  					 }else{
			  						showInsuredInfos.setOffice(""); 
			  					 }
		    					//驾照类型
		    					//showInsuredInfos.setLicenseType((infos.getDriverLicenseType()==null?"":infos.getDriverLicenseType()));
			  					//码制转换;
		    					if(infos.getDriverLicenseType()!=null&&!infos.getDriverLicenseType().equals("")){
			  						showInsuredInfos.setLicenseType(CodeMapperUtils.getOldCodeByNewCode("LICENSE_TYPE",infos.getDriverLicenseType(),"NBS"));	
			  					}else{
			  						showInsuredInfos.setLicenseType("");
			  					}
		    					//地址代码
		    					showInsuredInfos.setAddressNo((infos.getAddressId()==null?0:infos.getAddressId()).toString());
			  					  //省
		    					showInsuredInfos.setProvince((infos.getState()==null?"":infos.getState()));
			  					  //市
		    					showInsuredInfos.setCity((infos.getCity()==null?"":infos.getCity()));
			  					  //区/县
		    					showInsuredInfos.setCounty((infos.getDistrict()==null?"":infos.getDistrict()));
			  					 //街道
		    					showInsuredInfos.setPostalAddress((infos.getAddress()==null?"":infos.getAddress()));
			  					//邮政编码
		    					showInsuredInfos.setInsuredZipCode(infos.getPostCode()==null?"":infos.getPostCode());
		    					//移动电话
		    					showInsuredInfos.setMobile((infos.getMobileTel()==null?"":infos.getMobileTel()));
			  					  //固定电话
		    					showInsuredInfos.setPhone((infos.getOfficeTel()==null?"":infos.getOfficeTel()));
			  					  //传真电话
		    					showInsuredInfos.setHomeFax((infos.getFaxTel()==null?"":infos.getFaxTel()));
			  					  //住宅电话
		    					showInsuredInfos.setHomePhone((infos.getHouseTel()==null?"":infos.getHouseTel()));
			  					  //工作单位
		    					showInsuredInfos.setGrpName((infos.getCompanyName()==null?"":infos.getCompanyName()));
			  					  //电子邮箱
		    					showInsuredInfos.seteMail((infos.getEmail()==null?"":infos.getEmail()));
		    					 //固定收入
		    					showInsuredInfos.setIncome((infos.getAnnualIncome()==null?"": infos.getAnnualIncome()).toString());
		  					    //身高
		    					showInsuredInfos.setStature((infos.getCustomerHeight()==null?"":infos.getCustomerHeight()).toString());
		  					    //主要收入来源(留空)
		  					    //体重
		    					showInsuredInfos.setAvoirdupois((infos.getCustomerWeight()==null?"":infos.getCustomerWeight()).toString());
		    					//listInsuredInfos2.add(showInsuredInfos);
		    					//根据policy_id 查  InsuredImparts 下面的子节点(投保人告知信息);
		  					  PaQuestionaireCustomerPO paQuestion=new PaQuestionaireCustomerPO();
		  					  
		  					  paQuestion.setPolicyId(contract.getPolicyId());
		  					  List<PaQuestionaireCustomerPO> QuestionCallList = iQuestionCall.findPAQuestionCallByPolicyId(paQuestion);
		  				      List<InsuredImparts> listagentImparts=new ArrayList<InsuredImparts>();
		  					  if(QuestionCallList!=null&&QuestionCallList.size()>0){
		  				    	
		  						   for(PaQuestionaireCustomerPO  s:QuestionCallList){
		  							 InsuredImparts insuredImparts=new InsuredImparts();
		  							insuredImparts.setImpartVer(s.getSurveyVersion());
		  							insuredImparts.setImpartCode(s.getSurveyCode());
		  							insuredImparts.setImpartContent(s.getQuestionContent());
		  							//再保问题，ImpartParamModle改为查t_questionaire_customer.survey_module_result字段
		  							insuredImparts.setImpartParamModle(s.getSurveyModuleResult());
		  							listagentImparts.add(insuredImparts);
		  						   }
		  						 showInsuredInfos.setInsuredImparts(listagentImparts);
		  				    	 }else{
		  				    		InsuredImparts insuredImparts=new InsuredImparts();
		  							insuredImparts.setImpartVer("");
		  							insuredImparts.setImpartCode("");
		  							insuredImparts.setImpartContent("");
		  							insuredImparts.setImpartParamModle("");
		  							listagentImparts.add(insuredImparts);
		  							showInsuredInfos.setInsuredImparts(listagentImparts);
		  				    	 }
		  					    listInsuredInfos2.add(showInsuredInfos);
		    					nsureds.setInsuredInfos(listInsuredInfos2);
		    				 }
		    	  }
		    			 listInsureds.add(nsureds);
	    	}
	    	
	    }
	    	
	}
		return listInsureds;
	}
    //Conts节点;
	private List<Conts> buildConts(List<PAContractMasterPO> contractMaster){
		  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); 
		  List<Conts> listCont=new ArrayList<Conts>();
		  for (PAContractMasterPO contract: contractMaster) {
			  ContractAgentExtPO contractAgentPO=new ContractAgentExtPO();
  		    PAContractMasterPO pAContractMasterPO=new PAContractMasterPO();
  		    pAContractMasterPO.setPolicyId(contract.getPolicyId());
  		    AgentPO agentPo=new AgentPO();
  		    //QryBankPO qryBankPO=new QryBankPO();
  			Conts conts=new Conts();
  			 //投保单号;
  			conts.setContno(contract.getApplyCode()==null?"":contract.getApplyCode());
  			//投保日期;
  			conts.setPolAppntDate((contract.getApplyDate()==null?"":sdf.format(contract.getApplyDate())).toString());
	    		//生效日期;
  			conts.setcValiDate((contract.getValidateDate()==null?"":sdf.format(contract.getValidateDate())).toString());
	    		//销售方式;
  			//conts.setSaleChnl(contract.getChannelType()==null?"":contract.getChannelType());
  			String sale_type="";
  			sale_type=contract.getChannelType()==null?"":contract.getChannelType();	
  			if(!sale_type.equals("")){
  				sale_type=CodeMapperUtils.getOldCodeByNewCode("SALE_TYPE", sale_type,"PAS");
  			}
  			conts.setSaleChnl(sale_type);
	    		//管理机构
  			conts.setManageCom(contract.getOrganCode()==null?"":contract.getOrganCode());
	    		//经办人编号;
  			conts.setManagerCode(contract.getServiceHandlerCode()==null?"":contract.getServiceHandlerCode());
	    		//保单类型;
  			//conts.setPolicyType(contract.getPolicyType()==null?"":contract.getPolicyType());
	    	   //保单类型码制转换;
  			if(contract.getPolicyType()!=null&&!contract.getPolicyType().equals("")){
  				conts.setPolicyType(CodeMapperUtils.getOldCodeByNewCode("POLICY_TYPE", contract.getPolicyType(),"NBS"));
  			}else{
  				conts.setPolicyType("");
  			}
  			
  			
  			//电子函件服务;
  			conts.setRiskEmailFlag((contract.getEServiceFlag()==null?"":contract.getEServiceFlag()).toString());
	    		
  			contractAgentPO.setPolicyId(contract.getPolicyId());
	    		System.out.println(contract.getPolicyId()+"================policy_id");
	    		//根据policy_id查询业务员信息;
	    		 ContractAgentExtPO  agent = contractAgentDAO.findContractAgentByPolicyId(contractAgentPO);
	    			if(!agent.getData().isEmpty()){
	    			
	    			 //业务员电话;
	                 conts.setMobile(agent.getAgentMobile()==null?"":agent.getAgentMobile());
		    		 //业务员代码;
	                 conts.setAgentcode(agent.getAgentCode()==null?"":agent.getAgentCode());
		    		 //业务员姓名;
	                 conts.setName(agent.getAgentName()==null?"":agent.getAgentName());
	                 conts.setAgentManageCom(null==agent.getData().get("agent_organ_code")?"":agent.getData().get("agent_organ_code").toString());
	                 agentPo.setAgentCode(agent.getAgentCode());
		    		 agentPo = iAgentDao.findAgentByAgentId(agentPo);
		    		 if(!agentPo.getData().isEmpty()){
		    			 //业务员分级
		    			 conts.setAgentstar((null==agentPo.getData().get("agent_level")?"":agentPo.getData().get("agent_level")).toString());
		    			 System.out.println(agentPo.getAgentLeval()+"业务员分级");
		    			 //营业部
		    			 conts.setAgentGroup(agentPo.getAgentSalesOrganCode()==null?"":agentPo.getAgentSalesOrganCode());
		    			
		    		 }
		    		 
		    		 }else{
		    			//业务员电话;
		                 conts.setMobile("");
			    		 //业务员代码;
		                 conts.setAgentcode("");
			    		 //业务员姓名;
		                 conts.setName(""); 
		                 //业务员分级
		                 conts.setAgentstar("");
		                 //营业部
		    			 conts.setAgentGroup("");
		    			 //业务员所属机构
		    			 conts.setAgentManageCom("");
		    		 }
	    			//银行编码
	    			conts.setAgentBankCode(contract.getServiceBank()==null?"":contract.getServiceBank());
	    			//银行网点
	    			conts.setBankCode(contract.getServiceBankBranch()==null?"":contract.getServiceBankBranch());
	    			//与投保人关系
	    			pAContractMasterPO=iquryContsDao.queryPaRelationtoPaByPolicyId(pAContractMasterPO);
	    			if(!pAContractMasterPO.getData().isEmpty()){
	    				//conts.setRelationToAppnt(pAContractMasterPO.getRelationToPh()==null?"":pAContractMasterPO.getRelationToPh());
	    		        //码制转换
	    				if(pAContractMasterPO.getRelationToPh()!=null&&!pAContractMasterPO.getRelationToPh().equals("")){
	    					conts.setRelationToAppnt(CodeMapperUtils.getOldNameByNewCode("RELATIONSHIP", pAContractMasterPO.getRelationToPh(),"PAS"));
	    				}else{
	    					conts.setRelationToAppnt("");
	    				}
	    			}
	                //查询T_NB_CONTRACT_MASTER
	    			PAContractMasterPO contractMasterPO2 = new PAContractMasterPO();
	    			contractMasterPO2.setApplyCode(contract.getApplyCode());
	    			contractMasterPO2 = iquryContsDao.findPAContractMasterByApplyCode(contractMasterPO2);
	    	        
	    			if(!contractMasterPO2.getData().isEmpty()){
	    	          //高额件标识	
	    	          conts.setHighAmntFlag( (contractMasterPO2.getHighSaIndi()==null?"":contractMasterPO2.getHighSaIndi()).toString());
	    	          //需人工核保
	    	          conts.setForceuwflag( (contractMasterPO2.getManualUwIndi()==null?"":contractMasterPO2.getManualUwIndi()).toString());
	    	          System.out.println(contractMasterPO2.getManualUwIndi()+"需人工核保");
	    	          //核保原因(留空);
	    	          conts.setForceuwreason("");
	    	          //客户分级
	    	          PAContractMasterPO po=new PAContractMasterPO();
	    	          Map<String,Object> map=new HashMap<String,Object>();
	    	          map.put("policy_id",contract.getPolicyId());
	    	          po.setData(map);
	    	          po=iquryContsDao.queryCostomerLevel(po);
	    	          if(po.getData().size()>0){
	    	          //conts.setSeniorCustomer((String)po.getData().get("customerlevel"));	 
	    	        	//码制转换;
	    	        	 String  customerlevel=(String)po.getData().get("customerlevel");
	    	        	 if(customerlevel!=null&&!customerlevel.equals("")){
	    	        		 conts.setSeniorCustomer(CodeMapperUtils.getOldCodeByNewCode("CUSTOMER_LEVEL", customerlevel,"NBS")); 
	    	        	 }
	    	          }
	    	          else{
	    	          conts.setSeniorCustomer("");	  
	    	          }
	    	          //是否抄写了风险提示语
	    	          conts.setRiskWarnFlag((contractMasterPO2.getRiskIndi()==null?"":contractMasterPO2.getRiskIndi()).toString());
	    	        	
	    	        }
	    			//查询审核人信息;
	    			PAContractMasterPO contractMasterPO3 = new PAContractMasterPO();
	    			contractMasterPO3.setApplyCode(contract.getApplyCode());
	    			contractMasterPO3=iquryContsDao.queryShengheInfosByApplyCode(contractMasterPO3);
	    			//审核人
	    			conts.setFirstTrialOperatorName((contractMasterPO3.getAuditBy()==null?"":contractMasterPO3.getAuditBy()).toString());
	    			//审核日期
	    			conts.setAuditDate((contractMasterPO3.getInputDate()==null?"":sdf.format(contractMasterPO3.getInputDate())).toString());
	    			
	    			//查询险种信息查询自动续保标示;
	    			contractMasterPO3.setPolicyId(contract.getPolicyId());
	    			List<PAContractMasterPO> quaryRiskInfoByPolicyId = iquryContsDao.quaryPaRiskInfoByPolicyId(contractMasterPO3);
	    			
	    			if(quaryRiskInfoByPolicyId!=null&&quaryRiskInfoByPolicyId.size()>0){
	    				
	    				for(PAContractMasterPO p:quaryRiskInfoByPolicyId){
	    					  if(p.getRenew()==null||p.getRenew().toString().equals("0")){
	    						  conts.setrNewFlag("0");
	    						  break;
	    					  }
	    					  else{
	    						  conts.setrNewFlag("1");
	    					  }
	    				}
	    					  
	    				}else{
	    					 conts.setrNewFlag("");
	    				}
	    			//电话回访时间;
	    			PAContractMasterPO contractMasterPO4 = new PAContractMasterPO();
	    			contractMasterPO4.setPolicyCode(contract.getPolicyCode());
	    			contractMasterPO4=iquryContsDao.queryPaCallBackTime(contractMasterPO4);
	    			conts.setCallTime((contractMasterPO4.getReplyDate()==null?"":sdf.format(contractMasterPO4.getReplyDate())));
	    			
	    			
  	                //是否重庆医保卡保单
	    			conts.setcQYBKFlag("0");
	    			//移动平台客户确认方式(留空)
	    			conts.settYBContTransfer("");
	    			//AgentImparts 查询下面子节点;
	    			 PaQuestionaireCustomerPO paQuestion=new PaQuestionaireCustomerPO();
					  
					  paQuestion.setPolicyId(contract.getPolicyId());
					  List<PaQuestionaireCustomerPO> QuestionCallList = iQuestionCall.findPAQuestionCallByPolicyId(paQuestion);
				      List<AgentImparts> listagentImparts=new ArrayList<AgentImparts>();
					  if(QuestionCallList!=null&&QuestionCallList.size()>0){
				    	
						   for(PaQuestionaireCustomerPO  s:QuestionCallList){
							 AgentImparts agentImparts=new AgentImparts();
							 agentImparts.setImpartVer(s.getSurveyVersion());
							 agentImparts.setImpartCode(s.getSurveyCode());
							 agentImparts.setImpartContent(s.getQuestionContent());
							 agentImparts.setImpartParamModle(s.getSurveyModuleResult());
							 listagentImparts.add(agentImparts);
						   }
				    	conts.setAgentImparts(listagentImparts);
				     }else{
				    	 AgentImparts agentImparts=new AgentImparts();
						 agentImparts.setImpartVer("");
						 agentImparts.setImpartCode("");
						 agentImparts.setImpartContent("");
						 agentImparts.setImpartParamModle("");
						 listagentImparts.add(agentImparts);
						 conts.setAgentImparts(listagentImparts);
				     }
					  //添加字段;
					  conts.setFYJFlag("");
					  listCont.add(conts);  
		  }
  	        
		return listCont;
	}
	
	//appnts信息;
	private List<Appnts> buildAppnts(List<PAContractMasterPO> contractMaster){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); 
		List<Appnts> listAppents=new ArrayList<Appnts>();
		for (PAContractMasterPO contract: contractMaster) {
			AppntsPo appntsPo=new AppntsPo();
			//System.out.println("投保人信息查询");
			appntsPo.setPolicyId(contract.getPolicyId());
			List<AppntsPo> list = appentsDao.findAppentsByPolicyId(appntsPo);
			if(list!=null&&list.size()>0){
				for(AppntsPo appent:list){
					Appnts appnts=new Appnts();
					  //主要收入来源
					appnts.setIncomeWay("");
					  //VIP客户
					  appnts.setVipvalue((appent.getCustomerVip()==null?"":appent.getCustomerVip()).toString());
					  //姓名
					  appnts.setName((appent.getCustomerName()==null?"":appent.getCustomerName()));
					  //证件类型
					  //appnts.setiDType((appent.getCustomerCertType()==null?"":appent.getCustomerCertType()));
					  if(appent.getCustomerCertType()!=null&&!appent.getCustomerCertType().equals("")){
						  appnts.setiDType(CodeMapperUtils.getOldCodeByNewCode("CERTI_TYPE",appent.getCustomerCertType(),"PAS"));	 
		  					 }else{
		  						appnts.setiDType("");	 
		  					 }
					  //证件号码
					  appnts.setiDNo((appent.getCustomerCertiCode()==null?"":appent.getCustomerCertiCode()));
					  //证件有效期起
					  appnts.setiDEffStartDate((appent.getCustCertStarDate()==null?"":sdf.format(appent.getCustCertStarDate())).toString());
					  //证件有效期止
					  appnts.setiDEffEndDate((appent.getCustCertEndDate()==null?"":sdf.format(appent.getCustCertEndDate())).toString());
					  //国籍
					  //appnts.setNativePlace((appent.getCountryCode()==null?"":appent.getCountryCode()));
					  if(appent.getCountryCode()!=null&&!appent.getCountryCode().equals("")){
						  appnts.setNativePlace(CodeMapperUtils.getOldCodeByNewCode("COUNTRY",appent.getCountryCode(),"NBS"));	
		  					}else{
		  				  appnts.setNativePlace("");	
		  					}
					  //性别
					  //appnts.setSex((appent.getCustomerGender()==null?"": appent.getCustomerGender()).toString());
					  //码制转换;
			    		if(appent.getCustomerGender()!=null&&!appent.getCustomerGender().equals("")){
			    			appnts.setSex(CodeMapperUtils.getOldCodeByNewCode("GENDER",appent.getCustomerGender().toString(),"PAS"));
			    		}else{
			    			appnts.setSex("");
			    		}
					  //出生日期
					  appnts.setBirthday((appent.getCustomerBirthday()==null?"": sdf.format(appent.getCustomerBirthday())).toString());
					  //被保人年龄
					//  appnts.setAge((appent.getInsuredAge()==null?"":appent.getInsuredAge()).toString());
					  /**Add by zhuyi*/ //投保人年龄
					  if(!"".equals(appent.getCustomerBirthday())&&null!=appent.getCustomerBirthday()){
						  double age=DateUtilsEx.getYearAmount(appent.getCustomerBirthday(), WorkDateUtil.getWorkDate());
						  age=Math.floor(age);
						  int a=(int)age;
						  appnts.setAge(a+"");
					  }else{
						  appnts.setAge("");
					  }
					  //婚姻状况
					  appnts.setMarriage((appent.getMarriageStatus()==null?"":appent.getMarriageStatus()));
					   //子女状况
					  appnts.setChildStatus((appent.getIsParent()==null?"":appent.getIsParent()).toString());
					  //职业编码
					  appnts.setOccupationCode((appent.getJobCode()==null?"":appent.getJobCode()));
					  //职业类别
					  //appnts.setOccupationType((appent.getJobNature()==null?"":appent.getJobNature()));
					//码制转换
  					if(appent.getJobNature()!=null&&!appent.getJobNature().equals("")){
  						appnts.setOccupationType(CodeMapperUtils.getOldCodeByNewCode("JOB_UNDERWRITE",appent.getJobNature(),"PAS"));	
  					}else{
  						appnts.setOccupationType("");
  					}
					  //职务
					 // appnts.setOffice((appent.getJobTitle()==null?"":appent.getJobTitle()));
  				     //码制转换;
					if(appent.getJobTitle()!=null&&!appent.getJobTitle().equals("")){
						appnts.setOffice(CodeMapperUtils.getOldCodeByNewCode("JOB_TITLE",appent.getJobTitle(),"NBS"));	 
  					 }else{
  						appnts.setOffice(""); 
  					 } 
  					 //驾照类型
					  //appnts.setLicenseType((appent.getDriverLicenseType()==null?"":appent.getDriverLicenseType()));
						//码制转换;
  					if(appent.getDriverLicenseType()!=null&&!appent.getDriverLicenseType().equals("")){
  						appnts.setLicenseType(CodeMapperUtils.getOldCodeByNewCode("LICENSE_TYPE",appent.getDriverLicenseType(),"NBS"));	
	  					}else{
	  					appnts.setLicenseType("");
	  					}
					  //地址代码
					  appnts.setAddressNo((appent.getAddressId()==null?"":appent.getAddressId()).toString());
					  //省
					  appnts.setProvince((appent.getState()==null?"":appent.getState()));
					  //市
					  appnts.setCity((appent.getCity()==null?"":appent.getCity()));
					  //区/县
					  appnts.setCounty((appent.getDistrict()==null?"":appent.getDistrict()));
					  //街道
					  appnts.setPostalAddress((appent.getAddress()==null?"":appent.getAddress()));
					  //邮政编码;
					  appnts.setAppntZipCode(appent.getPostCode()==null?"":appent.getPostCode());
					  //移动电话
					  appnts.setMobile((appent.getMobileTel()==null?"":appent.getMobileTel()));
					  //固定电话
					  appnts.setPhone((appent.getOfficeTel()==null?"":appent.getOfficeTel()));
					  //传真电话
					  appnts.setHomeFax((appent.getFaxTel()==null?"":appent.getFaxTel()));
					  //住宅电话
					  appnts.setHomePhone((appent.getHouseTel()==null?"":appent.getHouseTel()));
					  //工作单位
					  appnts.setGrpName((appent.getCompanyName()==null?"":appent.getCompanyName()));
					  //电子邮箱
					  appnts.seteMail((appent.getEmail()==null?"":appent.getEmail()));
					  //首期交费形式
					  //appnts.setPayMode((appent.getPayMode()==null?"":appent.getPayMode()));
					  //码制转换;
					   if(appent.getPayMode()!=null&&!appent.getPayMode().equals("")){
						   appnts.setPayMode(CodeMapperUtils.getOldCodeByNewCode("PAY_MODE",appent.getPayMode(),"PAS")); 
					   }else{
						   appnts.setPayMode("");
					   }
					  //首期转帐开户行
					  appnts.setBankCode((appent.getAccountBank()==null?"":appent.getAccountBank()));
					  //首期帐户姓名
					  appnts.setAccName((appent.getAccountName()==null?"":appent.getAccountName()));
					  //首期账号
					  appnts.setBankAccNo((appent.getAccount()==null?"":appent.getAccount()));
					  //续期交费形式
					  //appnts.setSecPayMode((appent.getPayNext()==null?"":appent.getPayNext()));
					  //码制转换;
					  if(appent.getPayNext()!=null&&!appent.getPayNext().equals("")){
						  appnts.setSecPayMode(CodeMapperUtils.getOldCodeByNewCode("PAY_LOCATION",appent.getPayNext(),"PAS"));
					  }else{
						  appnts.setSecPayMode("");
					  }
					  //首、续期账号一致(由程序判断)
					  if(appent.getAccountBank()==null||appent.getNextAccountBank()==null){
						  appnts.setTheSameAccountFlag("1"); 
					  }
					  else {
						      if(appent.getAccountBank().equals(appent.getNextAccountBank())){
						    	  appnts.setTheSameAccountFlag("0"); 
						      }
						      else{
						    	  appnts.setTheSameAccountFlag("1"); 
						      }
					  }
					  //续期转帐开户行
					  appnts.setSecAppntBankCode((appent.getNextAccountBank()==null?"":appent.getNextAccountBank()));
					  //续期帐户姓名
					  appnts.setSecAppntAccName((appent.getNextAccountName()==null?"":appent.getNextAccountName()));
					  //续期账号
					  appnts.setSecAppntBankAccNo((appent.getNextAccount()==null?"":appent.getNextAccount()));
					  //固定收入
					  appnts.setIncome((appent.getAnnualIncome()==null?"": appent.getAnnualIncome()).toString());
					  //身高
					  appnts.setStature((appent.getCustomerHeight()==null?"":appent.getCustomerHeight()).toString());
					  //主要收入来源(留空)
					  //体重
					  appnts.setAvoirdupois((appent.getCustomerWeight()==null?"":appent.getCustomerWeight()).toString());
					  //其它声明
					  appnts.setRemark("");//未承保留空
					   
					  //根据policy_id 查  AppntImparts 下面的子节点;
					  PaQuestionaireCustomerPO paQuestion=new PaQuestionaireCustomerPO();
					  
					  paQuestion.setPolicyId(contract.getPolicyId());
					  List<PaQuestionaireCustomerPO> QuestionCallList = iQuestionCall.findPAQuestionCallByPolicyId(paQuestion);
				      List<AppntImparts> listagentImparts=new ArrayList<AppntImparts>();
					  if(QuestionCallList!=null&&QuestionCallList.size()>0){
				    	
						   for(PaQuestionaireCustomerPO  s:QuestionCallList){
							 AppntImparts agentImparts=new AppntImparts();
							 agentImparts.setImpartVer(s.getSurveyVersion());
							 agentImparts.setImpartCode(s.getSurveyCode());
							 agentImparts.setImpartContent(s.getQuestionContent());
							 agentImparts.setImpartParamModle(s.getSurveyModuleResult());
							 listagentImparts.add(agentImparts);
						   }
				    	
				    	 }else{
				    		 AppntImparts agentImparts=new AppntImparts();
							 agentImparts.setImpartVer("");
							 agentImparts.setImpartCode("");
							 agentImparts.setImpartContent("");
							 agentImparts.setImpartParamModle("");
							 listagentImparts.add(agentImparts);
				    	 }
					  //AgentImparts循环子节点;
					  appnts.setAppntImparts(listagentImparts);
					  //添加节点;
					  appnts.setLXLeve(appent.getData().get("customer_risk_level")==null?"":appent.getData().get("customer_risk_level").toString());
					  listAppents.add(appnts);
				 }
			}
			  
			
		 }
		  
		return listAppents;
	}
	//非承保;
	private OutputData buildFeiChengBao(List<PAContractMasterPO> NbContractMaster){
		  
		  OutputData outputData = new OutputData();
		  //cints节点;
		  List<Conts> conts = buildNbConts(NbContractMaster);
		  //Appnts节点;
		  List<Appnts> appnts = buildNbAppnts(NbContractMaster);
		  //Insureds节点;
		  List<Insureds> insureds = buildNbInsureds(NbContractMaster);
		  //Risks节点战士险种信息;
		  for (PAContractMasterPO contract: NbContractMaster) {
			//查询险种信息;
				RiskInfoPo riskInfoPo=new RiskInfoPo();
				riskInfoPo.setPolicyId(contract.getPolicyId());
				System.out.println(contract.getPolicyId()+"保单Id");
				List<RiskInfoPo> riskList =iriskInfoDao.findNbContractRiskByPolicyId(riskInfoPo);
				List<Risks> listRisks=new ArrayList<Risks>();
				if(listRisks!=null&&riskList.size()>0){
					//System.out.println(riskList.size()+"==========");
					for(RiskInfoPo riskInfo:riskList){
						Risks risks=new Risks();
						//险种编码;
						risks.setRiskcode(riskInfo.getProductCode()==null?"":riskInfo.getProductCode());
						//险种名称;
						risks.setRiskname(riskInfo.getProductNameSys()==null?"":riskInfo.getProductNameSys());
						//保费;
						risks.setPrem(null==riskInfo.getTotalPremAf()?"":riskInfo.getTotalPremAf().toString());
						//保额
						risks.setAmnt((riskInfo.getAmount()==null?"":riskInfo.getAmount()).toString());
						listRisks.add(risks);
					}
					outputData.setRisks(listRisks);
				}
			   }
				 outputData.setConts(conts);
				 outputData.setAppnts(appnts);
				 outputData.setInsureds(insureds);
				 return outputData;
	}
	//非承保Insureds(被保人)节点;
     private List<Insureds> buildNbInsureds(List<PAContractMasterPO> nbContractMaster) {
    	 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    	 List<Insureds> listNbInsureds=new ArrayList<Insureds>();
    	 for (PAContractMasterPO contract: nbContractMaster) {
    		InsuredsPo insuredsPo=new InsuredsPo();
 	    	insuredsPo.setPolicyId(contract.getPolicyId());
 	    	System.out.println("被保人信息"+contract.getPolicyId());
 	    	//查询被保人 
 	    	List<InsuredsPo> findNbInsured = insuresdDao.findNbInsuredByPolicyId(insuredsPo);
 	    	if(findNbInsured!=null&&findNbInsured.size()>0){
 	    		for(InsuredsPo po:findNbInsured){
		    		Insureds nbInsureds=new Insureds();
		    		//客户号
		    		nbInsureds.setCustomerNo((po.getCustomerId()==null?"": po.getCustomerId()).toString());
		    		//姓名
		    		nbInsureds.setName(po.getCustomerName()==null?"":po.getCustomerName());
		    		//性别
		    		//nbInsureds.setSex((po.getCustomerGender()==null?"":po.getCustomerGender()).toString());
		    		 //码制转换;
		    		if(po.getCustomerGender()!=null&&!po.getCustomerGender().equals("")){
		    			nbInsureds.setSex(CodeMapperUtils.getOldCodeByNewCode("GENDER",po.getCustomerGender().toString(),"PAS"));
		    		}else{
		    			nbInsureds.setSex("");
		    		}
		    		//出生日期
		    		nbInsureds.setBirthday((po.getCustomerBirthday()==null?"":sdf.format(po.getCustomerBirthday())).toString());
		    		//与第一被保险人关系
		    		//nbInsureds.setRelationToMainInsured(po.getRelationToInsured1()==null?"":po.getRelationToInsured1());
		    		//码制转换;
		    		if(po.getRelationToInsured1()!=null&&!po.getRelationToInsured1().equals("")){
		    			nbInsureds.setRelationToMainInsured(CodeMapperUtils.getOldCodeByNewCode("LA_PH_RELA",po.getRelationToInsured1(),"PAS"));
		    		}else{
		    			nbInsureds.setRelationToMainInsured("");	
		    		}
		
		    		//客户内部号码
		    		nbInsureds.setSequenceNo((po.getInnerCustomerId()==null?"":po.getInnerCustomerId()).toString());
		    		//查询被保人详细信息;
		    		InsuredInfosPo insuredInfosPo2=new InsuredInfosPo();
		    		List<InsuredInfos> listInsuredInfos3=new ArrayList<InsuredInfos>();
		    		insuredInfosPo2.setPolicyId(contract.getPolicyId());
		    		List<InsuredInfosPo> NbListInsuredInfos= insuredInfosDao.findNbListInsuredInfosByPolicyId(insuredInfosPo2);
		    		  if(NbListInsuredInfos!=null&&NbListInsuredInfos.size()>0){
		    			  for(InsuredInfosPo infos:NbListInsuredInfos){
		    				  InsuredInfos showInsuredInfos=new  InsuredInfos();
		    				//VIP客户
		    					 showInsuredInfos.setVipvalue((infos.getCustomerVip()==null?"":infos.getCustomerVip()).toString());
		    				    //客户内部号码 
		    					 showInsuredInfos.setSequenceNo((infos.getInnerCustomerId()==null?"":infos.getInnerCustomerId()).toString());
		    					//与第一被保险人关系 
		    					// showInsuredInfos.setRelationToMainInsured(infos.getRelationToInsured1()==null?"":infos.getRelationToInsured1());
		    					//码制转换;
		    			    		if(po.getRelationToInsured1()!=null&&!po.getRelationToInsured1().equals("")){
		    			    			showInsuredInfos.setRelationToMainInsured(CodeMapperUtils.getOldCodeByNewCode("LA_PH_RELA",infos.getRelationToInsured1(),"PAS"));
		    			    		}else{
		    			    			showInsuredInfos.setRelationToMainInsured("");	
		    			    		}
		    					 //与投保人关系
		    					//showInsuredInfos.setRelationShip(infos.getRelationToPh()==null?"":infos.getRelationToPh());
		    					 if(infos.getRelationToPh()!=null&&!infos.getRelationToPh().equals("")){
		    						 showInsuredInfos.setRelationShip(CodeMapperUtils.getOldNameByNewCode("RELATIONSHIP",infos.getRelationToPh(),"PAS"));
		    					 }else{
		    						 showInsuredInfos.setRelationShip("");
		    					 }
		    					 //姓名
		    					showInsuredInfos.setName(infos.getCustomerName()==null?"":infos.getCustomerName());
		    					 //证件类型
		    					//showInsuredInfos.setiDType(infos.getCustomerCertType()==null?"":infos.getCustomerCertType());
		    					if(infos.getCustomerCertType()!=null&&!infos.getCustomerCertType().equals("")){
				  					 showInsuredInfos.setiDType(CodeMapperUtils.getOldCodeByNewCode("CERTI_TYPE",infos.getCustomerCertType(),"PAS"));	 
				  					 }else{
				  					 showInsuredInfos.setiDType("");	 
				  					 } 
		    					//证件号码
		    					showInsuredInfos.setiDNo(infos.getCustomerCertiCode()==null?"":infos.getCustomerCertiCode());
			  					  //证件有效期起
		    					showInsuredInfos.setiDEffStartDate((infos.getCustCertStarDate()==null?"":sdf.format(infos.getCustCertStarDate())).toString());
			  					  //证件有效期止
		    					showInsuredInfos.setiDEffEndDate((infos.getCustCertEndDate()==null?"": sdf.format(infos.getCustCertEndDate())).toString());
			  					  //国籍
		    					//showInsuredInfos.setNativePlace(infos.getCountryCode()==null?"":infos.getCountryCode());
		    					if(infos.getCountryCode()!=null&&!infos.getCountryCode().equals("")){
				  					 showInsuredInfos.setNativePlace(CodeMapperUtils.getOldCodeByNewCode("COUNTRY",infos.getCountryCode(),"NBS"));	
				  					}else{
				  					 showInsuredInfos.setNativePlace("");	
				  					}
			  					  //性别
		    					//showInsuredInfos.setSex((infos.getCustomerGender()==null?"": infos.getCustomerGender()).toString());
		    					 //码制转换;
		    		    		if(infos.getCustomerGender()!=null&&!infos.getCustomerGender().equals("")){
		    		    			showInsuredInfos.setSex(CodeMapperUtils.getOldCodeByNewCode("GENDER",infos.getCustomerGender().toString(),"NBS"));
		    		    		}else{
		    		    			showInsuredInfos.setSex("");
		    		    		} 
		    					//出生日期
		    					showInsuredInfos.setBirthday((infos.getCustomerBirthday()==null?"": sdf.format(infos.getCustomerBirthday())).toString());
			  					  //投保人年龄
		    					if(!"".equals(infos.getCustomerBirthday())&&null!=infos.getCustomerBirthday()){
			  						  double age=DateUtilsEx.getYearAmount(infos.getCustomerBirthday(), WorkDateUtil.getWorkDate());
			  						  age=Math.floor(age);
			  						  int a=(int)age;
			  						showInsuredInfos.setAge(a+"");
			  					  }else{
			  						showInsuredInfos.setAge(""); 
			  					  }
		    					//showInsuredInfos.setAge((infos.getInsuredAge()==null?"":infos.getInsuredAge()).toString());
			  					  
		    					//婚姻状况
		    					showInsuredInfos.setMarriage(infos.getMarriageStatus()==null?"":infos.getMarriageStatus());
			  					   //子女状况
		    					showInsuredInfos.setChildStatus((infos.getIsParent()==null?"":infos.getIsParent()).toString());
			  					  //职业编码
		    					showInsuredInfos.setOccupationCode(infos.getJobCode()==null?"":infos.getJobCode());
			  					  //职业类别
		    					//showInsuredInfos.setOccupationType(infos.getJobNature()==null?"":infos.getJobNature());
		    					//码制转换
		      					if(infos.getJobNature()!=null&&!infos.getJobNature().equals("")){
		      						showInsuredInfos.setOccupationType(CodeMapperUtils.getOldCodeByNewCode("JOB_UNDERWRITE",infos.getJobNature(),"PAS"));	
		      					}else{
		      						showInsuredInfos.setOccupationType("");
		      					} 
		      	    		    //主要收入来源
		      					showInsuredInfos.setIncomeWay("");
		    					
		    					//职务
		    					//showInsuredInfos.setOffice(infos.getJobTitle()==null?"":infos.getJobTitle());
		      				   //码制转换;
		    					if(infos.getJobTitle()!=null&&!infos.getJobTitle().equals("")){
		    						showInsuredInfos.setOffice(CodeMapperUtils.getOldCodeByNewCode("JOB_TITLE",infos.getJobTitle(),"NBS"));	 
		      					 }else{
		      						showInsuredInfos.setOffice(""); 
		      					 } 
			  					  //驾照类型
		    					//showInsuredInfos.setLicenseType(infos.getDriverLicenseType()==null?"":infos.getDriverLicenseType());
		    					//码制转换;
		      					if(infos.getDriverLicenseType()!=null&&!infos.getDriverLicenseType().equals("")){
		      						showInsuredInfos.setLicenseType(CodeMapperUtils.getOldCodeByNewCode("LICENSE_TYPE",infos.getDriverLicenseType(),"NBS"));	
		    	  					}else{
		    	  					showInsuredInfos.setLicenseType("");
		    	  					}
		    					//地址代码
		    					showInsuredInfos.setAddressNo((infos.getAddressId()==null?0:infos.getAddressId()).toString());
			  					  //省
		    					showInsuredInfos.setProvince(infos.getState()==null?"":infos.getState());
			  					  //市
		    					showInsuredInfos.setCity(infos.getCity()==null?"":infos.getCity());
			  					  //区/县
		    					showInsuredInfos.setCounty(infos.getDistrict()==null?"":infos.getDistrict());
			  					  //街道
		    					showInsuredInfos.setPostalAddress(infos.getAddress()==null?"":infos.getAddress());
			  					//邮局编码;
		    					showInsuredInfos.setInsuredZipCode(infos.getPostCode()==null?"":infos.getPostCode());
		    					//移动电话
		    					showInsuredInfos.setMobile(infos.getMobileTel()==null?"":infos.getMobileTel());
			  					  //固定电话
		    					showInsuredInfos.setPhone(infos.getOfficeTel()==null?"":infos.getOfficeTel());
			  					  //传真电话
		    					showInsuredInfos.setHomeFax(infos.getFaxTel()==null?"":infos.getFaxTel());
			  					  //住宅电话
		    					showInsuredInfos.setHomePhone(infos.getHouseTel()==null?"":infos.getHouseTel());
			  					  //工作单位
		    					showInsuredInfos.setGrpName(infos.getCompanyName()==null?"":infos.getCompanyName());
			  					  //电子邮箱
		    					showInsuredInfos.seteMail(infos.getEmail()==null?"":infos.getEmail());
		    					 //固定收入
		    					showInsuredInfos.setIncome((infos.getAnnualIncome()==null?"": infos.getAnnualIncome()).toString());
		  					    //身高
		    					showInsuredInfos.setStature((infos.getCustomerHeight()==null?"":infos.getCustomerHeight()).toString());
		  					    //主要收入来源(留空)
		  					    //体重
		    					showInsuredInfos.setAvoirdupois((infos.getCustomerWeight()==null?"":infos.getCustomerWeight()).toString());
		    					//查询投保人告知;
		    					PaQuestionaireCustomerPO paQuestion=new PaQuestionaireCustomerPO();
			  					paQuestion.setPolicyId(contract.getPolicyId());
			  					List<PaQuestionaireCustomerPO> QuestionCallList = iQuestionCall.findQuestionCallByPolicyId(paQuestion);
			  				    List<InsuredImparts> listagentImparts=new ArrayList<InsuredImparts>();
			  				    if(QuestionCallList!=null&&QuestionCallList.size()>0){
			  				    	
		  						   for(PaQuestionaireCustomerPO  s:QuestionCallList){
		  							 InsuredImparts insuredImparts=new InsuredImparts();
		  							insuredImparts.setImpartVer(s.getSurveyVersion());
		  							insuredImparts.setImpartCode(s.getSurveyCode());
		  							insuredImparts.setImpartContent(s.getQuestionContent());
		  							insuredImparts.setImpartParamModle(s.getSurveyModuleResult());
		  							listagentImparts.add(insuredImparts);
		  						   }
		  						  showInsuredInfos.setInsuredImparts(listagentImparts);
		  				    	 }else{
		  				    		 InsuredImparts insuredImparts=new InsuredImparts();
			  						 insuredImparts.setImpartVer("");
			  						 insuredImparts.setImpartCode("");
			  						 insuredImparts.setImpartContent("");
			  						 insuredImparts.setImpartParamModle("");
			  						 listagentImparts.add(insuredImparts); 
			  						 showInsuredInfos.setInsuredImparts(listagentImparts);
		  				    	 }
		    					
		    					listInsuredInfos3.add(showInsuredInfos);
		    			  }
		    			  nbInsureds.setInsuredInfos(listInsuredInfos3);
		    		  }
		    		listNbInsureds.add(nbInsureds);
		     }
    	   }
    	  }
		return listNbInsureds;
 	} 

	//非承保Appents节点;
	 private List<Appnts> buildNbAppnts(List<PAContractMasterPO> nbContractMaster) {
		 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		 List<Appnts> nBlistAppents=new ArrayList<Appnts>();
		for(PAContractMasterPO appnts:nbContractMaster){
			//System.out.println("投保人信息查询");
			AppntsPo appntsPo=new AppntsPo();
			appntsPo.setPolicyId(appnts.getPolicyId());
			List<AppntsPo> NbAppentsList= appentsDao.findNbAppentsByPolicyId(appntsPo);
			if(NbAppentsList!=null&&NbAppentsList.size()>0){
				for(AppntsPo appents:NbAppentsList){
					Appnts nbAppnts=new Appnts();
					//主要收入来源;
					nbAppnts.setIncomeWay("");
					  //VIP客户
					nbAppnts.setVipvalue((appents.getCustomerVip()==null?"":appents.getCustomerVip()).toString());
					  //姓名
					nbAppnts.setName(appents.getCustomerName()==null?"":appents.getCustomerName());
					  //证件类型
					//nbAppnts.setiDType(appents.getCustomerCertType()==null?"":appents.getCustomerCertType());
					 if(appents.getCustomerCertType()!=null&&!appents.getCustomerCertType().equals("")){
						 nbAppnts.setiDType(CodeMapperUtils.getOldCodeByNewCode("CERTI_TYPE",appents.getCustomerCertType(),"PAS"));	 
	  					 }else{
	  					nbAppnts.setiDType("");	 
	  					 }
				
					//证件号码
					nbAppnts.setiDNo(appents.getCustomerCertiCode()==null?"":appents.getCustomerCertiCode());
					  //证件有效期起
					nbAppnts.setiDEffStartDate((appents.getCustCertStarDate()==null?"":sdf.format(appents.getCustCertStarDate())).toString());
					  //证件有效期止
					nbAppnts.setiDEffEndDate((appents.getCustCertEndDate()==null?"": sdf.format(appents.getCustCertEndDate())).toString());
					  //国籍
					//nbAppnts.setNativePlace(appents.getCountryCode()==null?"":appents.getCountryCode());
					if(appents.getCountryCode()!=null&&!appents.getCountryCode().equals("")){
						nbAppnts.setNativePlace(CodeMapperUtils.getOldCodeByNewCode("COUNTRY",appents.getCountryCode(),"NBS"));	
	  					}else{
	  					nbAppnts.setNativePlace("");	
	  					}
					  //性别
					//nbAppnts.setSex((appents.getCustomerGender()==null?"": appents.getCustomerGender()).toString());
					 //码制转换;
		    		if(appents.getCustomerGender()!=null&&!appents.getCustomerGender().equals("")){
		    			nbAppnts.setSex(CodeMapperUtils.getOldCodeByNewCode("GENDER",appents.getCustomerGender().toString(),"PAS"));
		    		}else{
		    			nbAppnts.setSex("");
		    		}  
		    		 //其它声明
					nbAppnts.setRemark("");//未承保留空
					//出生日期
					nbAppnts.setBirthday((appents.getCustomerBirthday()==null?"": sdf.format(appents.getCustomerBirthday())).toString());
					  //投保人年龄
					//nbAppnts.setAge((appents.getInsuredAge()==null?"":appents.getInsuredAge()).toString());
					if(!"".equals(appents.getCustomerBirthday())&&null!=appents.getCustomerBirthday()){
						  double age=DateUtilsEx.getYearAmount(appents.getCustomerBirthday(), WorkDateUtil.getWorkDate());
						  age=Math.floor(age);
						  int a=(int)age;
						  nbAppnts.setAge(a+"");
					  }else{
						  nbAppnts.setAge(""); 
					  }
					//婚姻状况
					nbAppnts.setMarriage(appents.getMarriageStatus()==null?"":appents.getMarriageStatus());
					   //子女状况
					nbAppnts.setChildStatus((appents.getIsParent()==null?"":appents.getIsParent()).toString());
					  //职业编码
					nbAppnts.setOccupationCode(appents.getJobCode()==null?"":appents.getJobCode());
					  //职业类别
					//nbAppnts.setOccupationType(appents.getJobNature()==null?"":appents.getJobNature());
					//码制转换
  					if(appents.getJobNature()!=null&&!appents.getJobNature().equals("")){
  						nbAppnts.setOccupationType(CodeMapperUtils.getOldCodeByNewCode("JOB_UNDERWRITE",appents.getJobNature(),"PAS"));	
  					}else{
  						nbAppnts.setOccupationType("");
  					}  
					//职务
					//nbAppnts.setOffice(appents.getJobTitle()==null?"":appents.getJobTitle());
					 
  				  //码制转换;
					if(appents.getJobTitle()!=null&&!appents.getJobTitle().equals("")){
						nbAppnts.setOffice(CodeMapperUtils.getOldCodeByNewCode("JOB_TITLE",appents.getJobTitle(),"NBS"));	 
  					 }else{
  						nbAppnts.setOffice(""); 
  					 } 
  					//驾照类型
					//nbAppnts.setLicenseType(appents.getDriverLicenseType()==null?"":appents.getDriverLicenseType());
					if(appents.getDriverLicenseType()!=null&&!appents.getDriverLicenseType().equals("")){
						nbAppnts.setLicenseType(CodeMapperUtils.getOldCodeByNewCode("LICENSE_TYPE",appents.getDriverLicenseType(),"NBS"));	
	  					}else{
	  					nbAppnts.setLicenseType("");
	  					}  
					//地址代码
					nbAppnts.setAddressNo((appents.getAddressId()==null?"":appents.getAddressId()).toString());
					  //省
					nbAppnts.setProvince(appents.getState()==null?"":appents.getState());
					  //市
					nbAppnts.setCity(appents.getCity()==null?"":appents.getCity());
					  //区/县
					nbAppnts.setCounty(appents.getDistrict()==null?"":appents.getDistrict());
					  //街道
					nbAppnts.setPostalAddress(appents.getAddress()==null?"":appents.getAddress());
				     //邮政编码;
					nbAppnts.setAppntZipCode(appents.getPostCode()==null?"":appents.getPostCode());
					//移动电话
					nbAppnts.setMobile(appents.getMobileTel()==null?"":appents.getMobileTel());
					  //固定电话
					nbAppnts.setPhone(appents.getOfficeTel()==null?"":appents.getOfficeTel());
					  //传真电话
					nbAppnts.setHomeFax(appents.getFaxTel()==null?"":appents.getFaxTel());
					  //住宅电话
					nbAppnts.setHomePhone(appents.getHouseTel()==null?"":appents.getHouseTel());
					  //工作单位
					nbAppnts.setGrpName(appents.getCompanyName()==null?"":appents.getCompanyName());
					  //电子邮箱
					nbAppnts.seteMail(appents.getEmail()==null?"":appents.getEmail());
					  //首期交费形式
					//nbAppnts.setPayMode(appents.getPayMode()==null?"":appents.getPayMode());
					//码制转换;
					   if(nbAppnts.getPayMode()!=null&&!appents.getPayMode().equals("")){
						   nbAppnts.setPayMode(CodeMapperUtils.getOldCodeByNewCode("PAY_MODE",nbAppnts.getPayMode(),"PAS")); 
					   }else{
						   nbAppnts.setPayMode("");
					   }
					  //首期转帐开户行
					nbAppnts.setBankCode(appents.getAccountBank()==null?"":appents.getAccountBank());
					  //首期帐户姓名
					nbAppnts.setAccName(appents.getAccountName()==null?"":appents.getAccountName());
					  //首期账号
					nbAppnts.setBankAccNo(appents.getAccount()==null?"":appents.getAccount());
					  //续期交费形式
					//nbAppnts.setSecPayMode(appents.getPayNext()==null?"":appents.getPayNext());
					 //码制转换;
					  if(appents.getPayNext()!=null&&!appents.getPayNext().equals("")){
						  nbAppnts.setSecPayMode(CodeMapperUtils.getOldCodeByNewCode("PAY_LOCATION",appents.getPayNext(),"PAS"));
					  }else{
						  nbAppnts.setSecPayMode("");
					  }
					  //首、续期账号一致(由程序判断)
					  if(appents.getAccountBank()==null||appents.getNextAccountBank()==null){
						  nbAppnts.setTheSameAccountFlag("1"); 
					  }
					  else {
						      if(appents.getAccountBank().equals(appents.getNextAccountBank())){
						    	  nbAppnts.setTheSameAccountFlag("0"); 
						      }
						      else{
						    	  nbAppnts.setTheSameAccountFlag("1"); 
						      }
					  }
					  //续期转帐开户行
					nbAppnts.setSecAppntBankCode(appents.getNextAccountBank()==null?"":appents.getNextAccountBank());
					  //续期帐户姓名
					nbAppnts.setSecAppntAccName(appents.getNextAccountName()==null?"":appents.getNextAccountName());
					  //续期账号
					nbAppnts.setSecAppntBankAccNo(appents.getNextAccount()==null?"":appents.getNextAccount());
					  //固定收入
					nbAppnts.setIncome((appents.getAnnualIncome()==null?"": appents.getAnnualIncome()).toString());
					  //身高
					nbAppnts.setStature((appents.getCustomerHeight()==null?"":appents.getCustomerHeight()).toString());
					  //主要收入来源(留空)
					  //体重
					nbAppnts.setAvoirdupois((appents.getCustomerWeight()==null?"":appents.getCustomerWeight()).toString());
					  //其它声明(留空);
					//根据policy_id 查  AppntImparts 下面的子节点;
					  PaQuestionaireCustomerPO paQuestion=new PaQuestionaireCustomerPO();
					  
					  paQuestion.setPolicyId(appnts.getPolicyId());
					  List<PaQuestionaireCustomerPO> QuestionCallList = iQuestionCall.findQuestionCallByPolicyId(paQuestion);
				      List<AppntImparts> listAppntImparts=new ArrayList<AppntImparts>();
					  if(QuestionCallList!=null&&QuestionCallList.size()>0){
				    	
						   for(PaQuestionaireCustomerPO  s:QuestionCallList){
							 AppntImparts agentImparts=new AppntImparts();
							 agentImparts.setImpartVer(s.getSurveyVersion());
							 agentImparts.setImpartCode(s.getSurveyCode());
							 agentImparts.setImpartContent(s.getQuestionContent());
							 agentImparts.setImpartParamModle(s.getSurveyModuleResult());
							 listAppntImparts.add(agentImparts);
						   }
						   nbAppnts.setAppntImparts(listAppntImparts);
				    	 }else{
				    		 AppntImparts agentImparts=new AppntImparts();
				    		 agentImparts.setImpartVer("");
							 agentImparts.setImpartCode("");
							 agentImparts.setImpartContent("");
							 agentImparts.setImpartParamModle("");
							 listAppntImparts.add(agentImparts);
							 nbAppnts.setAppntImparts(listAppntImparts);
				    	 }
					  //添加节点;
					  nbAppnts.setLXLeve(appents.getData().get("customer_risk_level")==null?"":appents.getData().get("customer_risk_level").toString());
					nBlistAppents.add(nbAppnts);
				}
			}
			
		 }
		 return nBlistAppents;
	}

	//非承保中conts节点;
	private List<Conts>buildNbConts(List<PAContractMasterPO> nbContractMaster) {
		
		 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		 List<Conts> listCont=new ArrayList<Conts>();
		 for (PAContractMasterPO contract: nbContractMaster) {
			ContractAgentPO contractAgentPO=new ContractAgentPO();
			PAContractMasterPO pAContractMasterPO=new PAContractMasterPO();
			AgentPO agentPo=new AgentPO();
	  		//QryBankPO qryBankPO=new QryBankPO();
	  		pAContractMasterPO.setPolicyId(contract.getPolicyId());
	  	    Conts conts=new Conts(); 
	  	         //投保单号;
  			conts.setContno(contract.getApplyCode()==null?"":contract.getApplyCode());
  			     //投保日期;
  			conts.setPolAppntDate((contract.getApplyDate()==null?"":sdf.format(contract.getApplyDate()).toString()));
	    		//生效日期;
  			conts.setcValiDate((contract.getValidateDate()==null?"":sdf.format(contract.getValidateDate())).toString());
	    		//销售方式;
  			//conts.setSaleChnl(contract.getChannelType()==null?"":contract.getChannelType());
  			String sale_type="";
  			sale_type=contract.getChannelType()==null?"":contract.getChannelType();	
  			if(!sale_type.equals("")){
  				sale_type=CodeMapperUtils.getOldCodeByNewCode("SALE_TYPE", sale_type,"PAS");
  			}
  			conts.setSaleChnl(sale_type);
	    		//管理机构
  			conts.setManageCom(contract.getOrganCode()==null?"":contract.getOrganCode());
	    		//经办人编号;
  			conts.setManagerCode(contract.getServiceHandlerCode()==null?"":contract.getServiceHandlerCode());
	    		//保单类型;
  			//conts.setPolicyType(contract.getPolicyType()==null?"":contract.getPolicyType());
  			 //保单类型码制转换;
  			if(contract.getPolicyType()!=null&&!contract.getPolicyType().equals("")){
  				conts.setPolicyType(CodeMapperUtils.getOldCodeByNewCode("POLICY_TYPE", contract.getPolicyType(),"NBS"));
  			}else{
  				conts.setPolicyType("");
  			}
  			//电子函件服务;
  			conts.setRiskEmailFlag((contract.getEServiceFlag()==null?"":contract.getEServiceFlag()).toString());
  			    //高额件标识
  			conts.setHighAmntFlag((contract.getHighSaIndi()==null?"":contract.getHighSaIndi()).toString());
  			   //需人工核保
  			conts.setForceuwflag((contract.getManualUwIndi()==null?"":contract.getManualUwIndi()).toString());
  			   //核保原因(留空)
  			   //客户分级
  		    PAContractMasterPO po=new PAContractMasterPO();
            Map<String,Object> map=new HashMap<String,Object>();
            map.put("policy_id",contract.getPolicyId());
            po.setData(map);
            po=iquryContsDao.queryCostomerLevel(po);
            if(po.getData().size()>0){
            //conts.setSeniorCustomer((String)po.getData().get("customerlevel"));
            	//码制转换;
	        	 String  customerlevel=(String)po.getData().get("customerlevel");
	        	 if(customerlevel!=null&&!customerlevel.equals("")){
	        		 conts.setSeniorCustomer(CodeMapperUtils.getOldCodeByNewCode("CUSTOMER_LEVEL", customerlevel,"NBS")); 
	         }
	       }
            else{
            conts.setSeniorCustomer("");	  
            }
  			 
  			//是否抄写了风险提示语
  			conts.setRiskWarnFlag((contract.getRiskIndi()==null?"":contract.getRiskIndi()).toString());
  			contractAgentPO.setPolicyId(contract.getPolicyId());
    		System.out.println(contract.getPolicyId()+"================policy_id");
    		//根据policy_id查询业务员信息;
    		ContractAgentPO  agent = contractAgentDAO.findContractAgentByPolicyId(contractAgentPO);
    			if(!agent.getData().isEmpty()){
    			
    			 //业务员电话;
                 conts.setMobile(agent.getAgentMobile()==null?"":agent.getAgentMobile());
	    		 //业务员代码;
                 conts.setAgentcode(agent.getAgentCode()==null?"":agent.getAgentCode());
	    		 //业务员姓名;
                 conts.setName(agent.getAgentName()==null?"":agent.getAgentName());
                 //业务员所属结构
                 conts.setAgentManageCom(agent.getData().get("agent_organ_code").toString());
                 agentPo.setAgentCode(agent.getAgentCode());
	    		 agentPo = iAgentDao.findAgentByAgentId(agentPo);
	    		 if(!agentPo.getData().isEmpty()){
	    			 //业务员分级
	    			 conts.setAgentstar((agentPo.getAgentLeval()==null?"":agentPo.getAgentLeval()).toString());
	    			 //营业部、营业组
	    			 conts.setAgentGroup(agentPo.getAgentSalesOrganCode()==null?"":agentPo.getAgentSalesOrganCode());
	    			
	    		 }
	    		 
	    		 }else{
	    			//业务员电话;
	                 conts.setMobile("");
		    		 //业务员代码;
	                 conts.setAgentcode("");
		    		 //业务员姓名;
	                 conts.setName("");
	               //业务员所属结构
	                 conts.setAgentManageCom("");
	                 agentPo.setAgentCode(agent.getAgentCode());
	                //业务员分级
	    			conts.setAgentstar("");
	    			//营业部、营业组
	    			 conts.setAgentGroup("");
	    			 
	    		 }
    			//银行编码
    			conts.setAgentBankCode(contract.getServiceBank()==null?"":contract.getServiceBank());
    			//银行网点
    			conts.setBankCode(contract.getServiceBankBranch()==null?"":contract.getServiceBankBranch());
    			//与投保人关系 T_NB_INSURED_LIST表;
    			pAContractMasterPO=iquryContsDao.queryRelationtoPaByPolicyId(pAContractMasterPO);
    			if(!pAContractMasterPO.getData().isEmpty()){
    				//conts.setRelationToAppnt(pAContractMasterPO.getRelationToPh()==null?"":pAContractMasterPO.getRelationToPh());
    			    //码制转换;
    				 if(pAContractMasterPO.getRelationToPh()!=null&&!pAContractMasterPO.getRelationToPh().equals("")){
    					 conts.setRelationToAppnt(CodeMapperUtils.getOldNameByNewCode("RELATIONSHIP", pAContractMasterPO.getRelationToPh(),"PAS")); 
    				 } else{
    					 conts.setRelationToAppnt(""); 
    				 }
    			}else{
    				conts.setRelationToAppnt(""); 
    			}
    			//自动续保标记
    			PAContractMasterPO contractMaster=new PAContractMasterPO();
    			contractMaster.setPolicyId(contract.getPolicyId());
    			List<PAContractMasterPO> riskInfoList = iquryContsDao.quaryRiskInfoByPolicyId(contractMaster);
    			if(riskInfoList!=null&&riskInfoList.size()>0){
    				
    				for(PAContractMasterPO p:riskInfoList){
    					  if(p.getRenew()==null||p.getRenew().equals("0")){
    						  conts.setrNewFlag("0");
    						  break;
    					  }
    					  else{
    						  conts.setrNewFlag("1");
    					  }
    				}
    			  }else{
    				  conts.setrNewFlag("1");
    			  }
    			//电话回访时间;
    			PAContractMasterPO contractMaster1=new PAContractMasterPO();
    			contractMaster1.setApplyCode(contract.getApplyCode());
    			contractMaster1=iquryContsDao.queryNbPaCallBackTime(contractMaster1);
    			if(!contractMaster1.getData().isEmpty()){
    				conts.setCallTime((contractMaster1.getReplyDate()==null?"":contractMaster1.getReplyDate()).toString());
    			}else{
    				conts.setCallTime("");
    			}
    			//核保原因(留空)
    			conts.setForceuwreason("");
    			//查询审核人信息;
    			PAContractMasterPO contractMaster2 = new PAContractMasterPO();
    			contractMaster2.setApplyCode(contract.getApplyCode());
    			contractMaster2=iquryContsDao.queryShengheInfosByApplyCode(contractMaster2);
    			//审核人
    			conts.setFirstTrialOperatorName((contractMaster2.getAuditBy()==null?"":contractMaster2.getAuditBy()).toString());
    			//审核日期
    			conts.setAuditDate((contractMaster2.getInputDate()==null?"":sdf.format(contractMaster2.getInputDate())).toString());
    			//是否重庆医保卡保单
    			conts.setcQYBKFlag("0");
    			//移动平台客户确认方式(留空)
    			conts.settYBContTransfer("");
    			//AgentImparts 查询下面子节点;
   			 PaQuestionaireCustomerPO paQuestion=new PaQuestionaireCustomerPO();
				  
				  paQuestion.setPolicyId(contract.getPolicyId());
				  List<PaQuestionaireCustomerPO> QuestionCallList = iQuestionCall.findQuestionCallByPolicyId(paQuestion);
			      List<AgentImparts> listagentImparts=new ArrayList<AgentImparts>();
				  if(QuestionCallList!=null&&QuestionCallList.size()>0){
			    	
					   for(PaQuestionaireCustomerPO  s:QuestionCallList){
						 AgentImparts agentImparts=new AgentImparts();
						 agentImparts.setImpartVer(s.getSurveyVersion());
						 agentImparts.setImpartCode(s.getSurveyCode());
						 agentImparts.setImpartContent(s.getQuestionContent());
						 agentImparts.setImpartParamModle(s.getSurveyModuleResult());
						 listagentImparts.add(agentImparts);
					   }
			    	conts.setAgentImparts(listagentImparts);
			     }else{
			    	 AgentImparts agentImparts=new AgentImparts();
					 agentImparts.setImpartVer("");
					 agentImparts.setImpartCode("");
					 agentImparts.setImpartContent("");
					 agentImparts.setImpartParamModle("");
					 listagentImparts.add(agentImparts);
					 conts.setAgentImparts(listagentImparts);
			     }
				  //添加字段;
				  conts.setFYJFlag("");
    			listCont.add(conts);
    			
		 }
		return listCont;
	}
	
	

	
	
	
         
}