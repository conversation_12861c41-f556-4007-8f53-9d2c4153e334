package com.nci.tunan.qry.impl.peripheral.service.queryuwdocument.impl;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.Node;
import org.dom4j.io.SAXReader;
import org.fusesource.hawtbuf.ByteArrayInputStream;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.nci.core.common.impl.util.JsonUtil;
import com.nci.tunan.qry.common.DictionaryUtil;
import com.nci.tunan.qry.dao.IAskforinfoDao;
import com.nci.tunan.qry.dao.IAskforinfoDetailDao;
import com.nci.tunan.qry.dao.IBusinessProductDao;
import com.nci.tunan.qry.dao.ICsUwDocumentListQueryDao;
import com.nci.tunan.qry.dao.ICustomerDao;
import com.nci.tunan.qry.impl.css.service.impl.CodeUtils;
import com.nci.tunan.qry.impl.peripheral.service.queryuwdocument.ICsUwDocumentListQueryService;
import com.nci.tunan.qry.impl.util.CommonUtils;
import com.nci.tunan.qry.interfaces.model.po.BusinessProductPO;
import com.nci.tunan.qry.interfaces.model.po.CustomerPO;
import com.nci.tunan.qry.interfaces.model.po.QryCsUWDocumentPO;
import com.nci.tunan.qry.interfaces.model.po.UwAgentPO;
import com.nci.tunan.qry.interfaces.model.uw.po.AskforinfoDetailPO;
import com.nci.tunan.qry.interfaces.model.uw.po.AskforinfoPO;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.ConditionInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.ConditionInfoList;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.ExtraPremInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.ExtraPremInfoList;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.InsuredInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.ItemReasonList;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.LimitInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.LimitInfoList;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.LimitProductInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.ProductInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.RiskInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.RiskInfoList;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.AppCustomer;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.DocumentInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.DocumentList;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.InputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.InsCustomer;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.InsCustomerInfo;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.OutputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.PolicyItem;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.PolicyItemList;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.Result;
import com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.SendObjTypeInfo;
import com.nci.udmp.framework.encrypt.AvoidSqlInjectUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
/**
 * 
 * @description 核保通知书查询
 * <AUTHOR> <EMAIL> 
 * @date 2021-6-24 下午6:11:55 
 * @.belongToModule 综合查询-接口-核保/次标通知书查询 
 */
public class CsUwDocumentListQueryServiceImpl implements
		ICsUwDocumentListQueryService {
	/** 
     * 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
	/**
	 * 次标/核保通知书查询dao
	 */
	private ICsUwDocumentListQueryDao uwDocumentDao;

	/**
	 * 客户信息查询Dao
	 */
	private ICustomerDao customerDao;
	/**
	 * 操作索要资料与修改事项表的DAO
	 */
	private IAskforinfoDao askforinfoDao;
	/**
	 * 操作索要资料与修改事项细项表的DAO
	 */
	private IAskforinfoDetailDao askforinfoDetailDao;
	/**
	 * 查询险种编码
	 */
	@Autowired
	private IBusinessProductDao businessProductDao;
	
	/**
	 * 
	 * @description 初始化核保通知书响应报文
	 * @return
	 */
	public Result initDocumentListResultValue(){
		Result result = new Result();
		PolicyItemList policyItemList = new PolicyItemList();
		List<PolicyItem> policyItemListInfo = new ArrayList<PolicyItem>();
		PolicyItem policyItem = new PolicyItem();
		List<InsCustomer> insCustomer = new ArrayList<InsCustomer>();
		List<DocumentList> documentList = new ArrayList<DocumentList>();
		DocumentList document = new DocumentList();
//		QryCsUWDocumentPO po = new QryCsUWDocumentPO();
		document.setCardCode("");
		document.setDocNo("");
		document.setDocStatus("");
		document.setDocType("");
		document.setDocTypeName("");
		document.setPremDetail("");
		document.setPrintDate("");
		documentList.add(document);
		documentList.add(document);
		DocumentInfo documentInfo = new DocumentInfo();
		documentInfo.setDocumentInfoList(documentList);
		policyItem.setDocumentInfoList(documentInfo);
		InsCustomer customerIns = new InsCustomer();
		customerIns.setAppAndInsCode("");
		customerIns.setAppAndInsName("");
		customerIns.setCustomerNo("");
		customerIns.setInsBirthday("");
		customerIns.setInsIDEffEndDate("");
		customerIns.setInsIDEffStartDate("");
	    customerIns.setInsIdNo("");
	    customerIns.setInsIdType("");
	    customerIns.setInsName("");
	    customerIns.setInsSex("");
	    customerIns.setMainInsured("");
	    customerIns.setMobile("");
	    insCustomer.add(customerIns);
	    InsCustomerInfo insCustomerInfo = new InsCustomerInfo();
	    insCustomerInfo.setInsCustomerList(insCustomer);
	    policyItem.setInsCustomerList(insCustomerInfo);
	    AppCustomer cusomerApp = new AppCustomer();
	    cusomerApp.setAppBirthday("");
	    cusomerApp.setAppIdNo("");
	    cusomerApp.setAppIdType("");
	    cusomerApp.setAppName("");
	    cusomerApp.setAppntIdEffEndDate("");
	    cusomerApp.setAppntIdEffStartDate("");
	    cusomerApp.setAppntSex("");
	    cusomerApp.setCustomerNo("");
	    cusomerApp.setMobile("");
	    policyItem.setAppCustomer(cusomerApp);
	    policyItem.setAcceptCode("");
	    policyItem.setAgentCode("");
	    policyItem.setAgentName("");
	    policyItem.setAppCustNo("");
	    policyItem.setApplyCode("");
	    policyItem.setApplyDate("");
	    policyItem.setAppName("");
	    policyItem.setBank("");
	    policyItem.setBusiDePartment("");
	    policyItem.setContNo("");
	    policyItem.setMandgecomName("");
	    policyItem.setSalesDepartment("");
	    policyItem.setServiceName("");
	    policyItemListInfo.add(policyItem);
	    policyItemList.setPolicyItem(policyItemListInfo);
	    result.setPolicyItemList(policyItemList);
	    return result;
	}
	/**
	 * 
	 * @description 核保通知书查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.qry.impl.peripheral.service.queryuwdocument.ICsUwDocumentListQueryService#csUwDocumentListQuery(com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.vo.InputData)
	 * @param inputVO 入参对象
	 * @return
	 */
	@Override
	public OutputData csUwDocumentListQuery(InputData inputVO) {
		logger.debug("核保通知书查询");
		OutputData outputData = new OutputData();
		Result result = new Result();
		result = initDocumentListResultValue();
		if (StringUtils.isNotEmpty(inputVO.getAgentCode())
				&& CommonUtils.equalsManyAnd("", inputVO.getAcceptCode(), inputVO.getAppName(), inputVO.getContNo())) {
			/**
			 * 仅存在业务员号查询因素
			 */
			result = this.createCsUwDocumentListOnlyAgentCode(inputVO.getAgentCode());
		} else {
			result = this.createCsUwDocumentListQuery(inputVO);
		}
	    outputData.setResult(result);
		return outputData; 
	}

	/**
	 * 
	 * @description 当仅存在业务员号查询因素时，走此方法
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param inputVO
	 * @return
	 */
	private Result createCsUwDocumentListOnlyAgentCode(String agentCode){
		Result result = new Result();
		//根据业务员号查询业务员信息
		UwAgentPO uwAgentPO = new UwAgentPO();
		uwAgentPO.setAgentCode(agentCode);
		uwAgentPO =	uwDocumentDao.queryAgentByAgentCode(uwAgentPO);
		QryCsUWDocumentPO tQryCsUWDocumentPO = new QryCsUWDocumentPO();
		tQryCsUWDocumentPO.setAgentCode(agentCode);
		tQryCsUWDocumentPO.setOption(CommonUtils.equalsManyOr(uwAgentPO.getAgentChannel(), "03", "08") ? "1" : "0");
		//根据核保的uwId以及policyCode，查询通知书信息
		List<QryCsUWDocumentPO> qryCsUWDocumentPOs = uwDocumentDao.queryUwMsgByUwIdAndPolicyCode(tQryCsUWDocumentPO);
		if (qryCsUWDocumentPOs != null && qryCsUWDocumentPOs.size() > 0) {
			result = this.initPolicyResult(qryCsUWDocumentPOs);
		} else {
			result.setResultCode("0");
			result.setResultMsg("没有查询到对应数据");
		}
		return result;
	}
	
	
	/**
	 * 
	 * @description 当存在受理号或者保单号等其他查询因素时，走此方法
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param inputVO 入参
	 * @return
	 */
	private Result createCsUwDocumentListQuery(InputData inputVO){
		Result result = new Result();
		QryCsUWDocumentPO po = BeanUtils.copyProperties(QryCsUWDocumentPO.class, inputVO);
		List<QryCsUWDocumentPO> qryCsUWDocumentPOs = uwDocumentDao.csUwDocumentListQuery(po);
		if (qryCsUWDocumentPOs != null && qryCsUWDocumentPOs.size() > 0) {
			result = this.initPolicyResult(qryCsUWDocumentPOs);
		} else {
			result.setResultCode("0");
			result.setResultMsg("没有查询到对应数据");
		}
		return result;
	}
	
	/**
	 * 
	 * @description 组装返回参数
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param qryCsUWDocumentPOs
	 * @return
	 */
	private Result initPolicyResult (List<QryCsUWDocumentPO> qryCsUWDocumentPOs){
		Result result = new Result();
		Map<String, List<DocumentList>> documentMap = new HashMap<String, List<DocumentList>>();
		Set<QryCsUWDocumentPO> setDocumentPO = new HashSet<QryCsUWDocumentPO>();
		Map<String, String> finishedAddAcceptPolicy = new HashMap<String, String>();

		for (QryCsUWDocumentPO qryCsUWDocumentPO : qryCsUWDocumentPOs) {
			
			if (!finishedAddAcceptPolicy.containsKey(qryCsUWDocumentPO.getContNo() + "_" + qryCsUWDocumentPO.getAcceptCode())) {
				setDocumentPO.add(qryCsUWDocumentPO);
				finishedAddAcceptPolicy.put(qryCsUWDocumentPO.getContNo() + "_" + qryCsUWDocumentPO.getAcceptCode(), "1");
			}
			
			String premDetail = "";
			if ("1".equals(qryCsUWDocumentPO.getDocStatus()) && qryCsUWDocumentPO.getExistsExtraPrem().compareTo(BigDecimal.ZERO) > 0) {
				if (qryCsUWDocumentPO.getData().get("fee_status1") == null || qryCsUWDocumentPO.getData().get("fee_status1").toString().isEmpty()) {
					premDetail = "N";
				} else {
					if (StringUtils.isBlank(qryCsUWDocumentPO.getFeeStatus())) {
						premDetail = "收费失败，请至柜面办理。";
					} else {
						String[] feeStatus = qryCsUWDocumentPO.getFeeStatus().split(",");
						if ("01".equals(feeStatus[0]) || "19".equals(feeStatus[0])) {
							premDetail = "收费成功。";
						} else if ("03".equals(feeStatus[0])) {
							premDetail = "收费失败，请至柜面办理。";
							if ("E1004".equals(feeStatus[1])) {
								premDetail = "收费失败，余额不足请充值。";
							}
						} else if ("00".equals(feeStatus[0]) || "02".equals(feeStatus[0])) {
							premDetail = "待处理。";
						} else {
							premDetail = "N";
						}
					}
				}
			}
			/* #109121 当为投保问卷（自驾车意外险适用）通知书时，返回：投保问卷（自驾车意外险适用） 其他类别核保问卷通知书返回：核保问卷 */
			if (qryCsUWDocumentPO.getDocType().equals(DictionaryUtil.TEMPLATE_CARD_CODE_QUESTION)) {
				AskforinfoPO tAskforinfoPO = new AskforinfoPO();
				tAskforinfoPO.setDocListId(qryCsUWDocumentPO.getDocListId());
				tAskforinfoPO = askforinfoDao.findAskforinfoByDocListId(tAskforinfoPO);
				if (tAskforinfoPO != null) {
					AskforinfoDetailPO askforinfoDetailPO = new AskforinfoDetailPO();
					askforinfoDetailPO.setAskforinfoId(tAskforinfoPO.getAskforinfoId());
					List<AskforinfoDetailPO> tAskforinfoDetailBOS = askforinfoDetailDao.findAskforinfoDetailByAskforinfoId(askforinfoDetailPO);
					for (AskforinfoDetailPO askforinfoDetail : tAskforinfoDetailBOS) {
						if (askforinfoDetail.getQuestionContent().equals(DictionaryUtil.TEMPLATE_QUESTION_CAR)) {
							qryCsUWDocumentPO.setDocTypeName(askforinfoDetail.getQuestionContent());
							qryCsUWDocumentPO.setDocType(DictionaryUtil.TEMPLATE_CODE_QUESTION_CAR);
							break;
						}
					}
				}
				if (!qryCsUWDocumentPO.getDocTypeName().equals(DictionaryUtil.TEMPLATE_QUESTION_CAR)) {
					qryCsUWDocumentPO.setDocTypeName("核保问卷");
					qryCsUWDocumentPO.setDocType(DictionaryUtil.TEMPLATE_CODE_QUESTION_OTHER);
				}
			}
			
			DocumentList document = new DocumentList();
			document.setCardCode(StringUtilsEx.isNullOrEmpty(qryCsUWDocumentPO.getCardCode()) ? "" : qryCsUWDocumentPO.getCardCode());
			document.setDocNo(StringUtilsEx.isNullOrEmpty(qryCsUWDocumentPO.getDocNo()) ? "" : qryCsUWDocumentPO.getDocNo());
			document.setDocStatus(qryCsUWDocumentPO.getDocStatus());
			document.setDocType(StringUtilsEx.isNullOrEmpty(qryCsUWDocumentPO.getDocType()) ? "" : qryCsUWDocumentPO.getDocType());
			document.setDocTypeName(StringUtilsEx.isNullOrEmpty(qryCsUWDocumentPO.getDocTypeName()) ? "" : qryCsUWDocumentPO.getDocTypeName());
			document.setPremDetail(premDetail);
			document.setPrintDate(StringUtilsEx.isNullOrEmpty(qryCsUWDocumentPO.getPrintDate()) ? "" : qryCsUWDocumentPO.getPrintDate());
			// 核保员姓名-下发该通知书的核保员登录用户
			document.setUwUserName(StringUtilsEx.isNullOrEmpty(qryCsUWDocumentPO.getUwUserName()) ? "" : qryCsUWDocumentPO.getUwUserName());
			// 核保日期
			document.setUwDate(StringUtilsEx.isNullOrEmpty(qryCsUWDocumentPO.getUwDateDoc()) ? "" : qryCsUWDocumentPO.getUwDateDoc());
			// 接收对象信息
			List<SendObjTypeInfo> sendObjTypeInfos = new ArrayList<>();
			if(!StringUtils.isBlank(qryCsUWDocumentPO.getSendObjTypeInfo())){
				String[] sendObjTYpeInfoArrays = qryCsUWDocumentPO.getSendObjTypeInfo().split(",");
				SendObjTypeInfo sendObjTypeInfo = new SendObjTypeInfo();
				for (int i = 0; i < sendObjTYpeInfoArrays.length; i++) {
					String[] sendObjTYpeInfoArray =  sendObjTYpeInfoArrays[i].split("、");
					//接收对象
					sendObjTypeInfo.setSendObjType(sendObjTYpeInfoArray[0] == null || DictionaryUtil.ZERO_STRING.equals(sendObjTYpeInfoArray[0])? DictionaryUtil.NOTICE_REPLY_FLAG_NO : sendObjTYpeInfoArray[0]); 
					//接收对象客户号
					if(sendObjTYpeInfoArray.length >= 2){
						sendObjTypeInfo.setSendObjTypeCustomerNo(StringUtils.isBlank(sendObjTYpeInfoArray[1])? "" : sendObjTYpeInfoArray[1]); 
					}
					//接收对象姓名
					if(sendObjTYpeInfoArray.length >= 3){
						sendObjTypeInfo.setSendObjTypeName(StringUtils.isBlank(sendObjTYpeInfoArray[2])? "" : sendObjTYpeInfoArray[2]); 
					}
					sendObjTypeInfos.add(sendObjTypeInfo);
					sendObjTypeInfo = new SendObjTypeInfo();
				}
			}
			document.setSendObjTypeInfo(sendObjTypeInfos);
			List<DocumentList> documentList = new ArrayList<DocumentList>();
			String documentKey = qryCsUWDocumentPO.getContNo() + qryCsUWDocumentPO.getAcceptCode();
			if (!documentMap.containsKey(documentKey)) {
				documentList.add(document);
				documentMap.put(documentKey, documentList);
			} else {
				documentList = documentMap.get(documentKey);
				documentList.add(document);
				documentMap.put(documentKey, documentList);
			}
		}
		
		/**
		 * 查询投保人信息
		 */
		List<QryCsUWDocumentPO> holderInfo = customerDao.findPolicyHolderCustomersInfo(setDocumentPO);
		Map<String, AppCustomer> holderMap = new HashMap<String, AppCustomer>();
		for (QryCsUWDocumentPO holderPO : holderInfo) {
			AppCustomer cusomerApp = new AppCustomer();
			cusomerApp.setAppBirthday(StringUtilsEx.isNullOrEmpty(holderPO.getAppBirthday()) ? "" : holderPO.getAppBirthday());
			cusomerApp.setAppIdNo(StringUtilsEx.isNullOrEmpty(holderPO.getAppIdNo()) ? "" : holderPO.getAppIdNo());
			cusomerApp.setAppIdType(StringUtilsEx.isNullOrEmpty(holderPO.getAppIdType()) ? "" : holderPO.getAppIdType());
			cusomerApp.setAppName(StringUtilsEx.isNullOrEmpty(holderPO.getApplyName()) ? "" : holderPO.getApplyName());
			cusomerApp.setAppntIdEffEndDate(StringUtilsEx.isNullOrEmpty(holderPO.getAppntIdEffEndDate()) ? "" : holderPO.getAppntIdEffEndDate());
			cusomerApp.setAppntIdEffStartDate(StringUtilsEx.isNullOrEmpty(holderPO.getAppntIdEffStartDate()) ? "" : holderPO.getAppntIdEffStartDate());
			cusomerApp.setAppntSex(holderPO.getAppntSex() == null ? "" : holderPO.getAppntSex().toString());
			cusomerApp.setCustomerNo(StringUtilsEx.isNullOrEmpty(holderPO.getCustomerNo()) ? "" : holderPO.getCustomerNo());
			cusomerApp.setMobile(StringUtilsEx.isNullOrEmpty(holderPO.getMobile()) ? "" : holderPO.getMobile());
			holderMap.put(holderPO.getContNo(), cusomerApp);
		}
		
		/**
		 * 查询被保人信息
		 */
		List<QryCsUWDocumentPO> insuredInfo = customerDao.findInsuredCustomersInfo(setDocumentPO);
		Map<String, List<InsCustomer>> insuredMap = new HashMap<String, List<InsCustomer>>();
		for (QryCsUWDocumentPO insuredPO : insuredInfo) {
			InsCustomer customerIns = new InsCustomer();
			customerIns.setAppAndInsCode(StringUtilsEx.isNullOrEmpty(insuredPO.getAppAndInsCode()) ? "" : insuredPO.getAppAndInsCode());
			customerIns.setAppAndInsName(StringUtilsEx.isNullOrEmpty(insuredPO.getAppAndInsName()) ? "" : insuredPO.getAppAndInsName());
			customerIns.setCustomerNo(StringUtilsEx.isNullOrEmpty(insuredPO.getCustomerNo()) ? "" : insuredPO.getCustomerNo());
			customerIns.setInsBirthday(StringUtilsEx.isNullOrEmpty(insuredPO.getInsBirthday()) ? "" : insuredPO.getInsBirthday());
			customerIns.setInsIDEffEndDate(StringUtilsEx.isNullOrEmpty(insuredPO.getInsIdEffEndDate()) ? "" : insuredPO.getInsIdEffEndDate());
			customerIns.setInsIDEffStartDate(StringUtilsEx.isNullOrEmpty(insuredPO.getInsIdEffStartDate()) ? "" : insuredPO.getInsIdEffStartDate());
			customerIns.setInsIdNo(StringUtilsEx.isNullOrEmpty(insuredPO.getInsIdNo()) ? "" : insuredPO.getInsIdNo());
			customerIns.setInsIdType(StringUtilsEx.isNullOrEmpty(insuredPO.getInsIdType()) ? "" : insuredPO.getInsIdType());
			customerIns.setInsName(StringUtilsEx.isNullOrEmpty(insuredPO.getInsName()) ? "" : insuredPO.getInsName());
			customerIns.setInsSex(insuredPO.getInsSex() == null ? "" : insuredPO.getInsSex().toString());
			customerIns.setMainInsured(insuredPO.getMainInsured());
			customerIns.setMobile(StringUtilsEx.isNullOrEmpty(insuredPO.getMobile()) ? "" : insuredPO.getMobile());
			if (!insuredMap.containsKey(insuredPO.getContNo())) {
				List<InsCustomer> insCustomer = new ArrayList<InsCustomer>();
				insCustomer.add(customerIns);
				insuredMap.put(insuredPO.getContNo(), insCustomer);
			} else {
				List<InsCustomer> insCustomer = insuredMap.get(insuredPO.getContNo());
				insCustomer.add(customerIns);
				insuredMap.put(insuredPO.getContNo(), insCustomer);
			}
		}
		
		PolicyItemList policyItemList = new PolicyItemList();
		List<PolicyItem> policyItemListInfo = new ArrayList<PolicyItem>();
		for (QryCsUWDocumentPO policyInfo : setDocumentPO) {
			PolicyItem policyItem = new PolicyItem();
			policyItem.setContNo(policyInfo.getContNo());
			policyItem.setAcceptCode(policyInfo.getAcceptCode());
			policyItem.setServiceName(policyInfo.getServiceName());
			policyItem.setApplyCode(policyInfo.getApplyCode());
			if (StringUtils.isNotEmpty(policyInfo.getMandgecomName())) {
				if (policyInfo.getMandgecomName().length() > 4) {
					policyInfo.setMandgecomName(policyInfo.getMandgecomName().substring(0, 4));
				}
			}
			policyItem.setMandgecomName(CodeUtils.getValueByCode("DEV_PAS.T_UDMP_ORG", policyInfo.getMandgecomName()));
			policyItem.setAppName(policyInfo.getAppName());
			CustomerPO customerPO = new CustomerPO();
			customerPO.setCustomerId(policyInfo.getAppCustNO());
			CustomerPO customer = customerDao.findCustomerByCustomerIdOrOldCustomerId(customerPO);
			policyItem.setAppCustNo(StringUtilsEx.nullToString(customer.getOldCustomerId()));
			policyItem.setApplyDate(policyInfo.getApplyDate());
			policyItem.setBank(policyInfo.getBank());
			policyItem.setBusiDePartment(policyInfo.getBusiDePartment());
			policyItem.setSalesDepartment(policyInfo.getSalesDepartment());
			policyItem.setAgentName(policyInfo.getAgentName());
			policyItem.setAgentCode(policyInfo.getAgentCode());
			DocumentInfo documentInfo = new DocumentInfo();
			documentInfo.setDocumentInfoList(documentMap.get(policyInfo.getContNo() + policyInfo.getAcceptCode()));
			policyItem.setDocumentInfoList(documentInfo);
			InsCustomerInfo insCustomerInfo = new InsCustomerInfo();
			insCustomerInfo.setInsCustomerList(insuredMap.get(policyInfo.getContNo()));
			policyItem.setInsCustomerList(insCustomerInfo);
			policyItem.setAppCustomer(holderMap.get(policyInfo.getContNo()));
			policyItemListInfo.add(policyItem);
		}
		policyItemList.setPolicyItem(policyItemListInfo);
		result.setPolicyItemList(policyItemList);
		result.setResultCode("0");
		result.setResultMsg("");
		return result;
	}
	
	
	/**
	 * 
	 * @description 初始化次标通知书报文
	 * @return
	 */
	public com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.Result initSubStandardResult(){
		com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.Result result = 
				new com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.Result();
		result.setAdverseExtraPremFlag("");
		result.setExtraPremFlag907("");
		result.setInitExtraPrem("");
		result.setInterest("");
		result.setPayedFlag("");
		result.setReinExtraPrem("");
		RiskInfoList riskInfoList = new RiskInfoList();
		List<RiskInfo> riskInfos = new ArrayList<RiskInfo>();
		RiskInfo risk =initRiskInfo();
		riskInfos.add(risk);
		riskInfoList.setRiskInfo(riskInfos);
		result.setRiskInfoList(riskInfoList);
		result.setReinExtraPrem("");
		result.setPayedFlag("");
		result.setTotalExtraPrem("");
		result.setUwDate("");
		result.setUwUser("");
		result.setExtraPremFlag907("");
		result.setInitExtraPrem("");
		result.setInterest("");
		result.setAdverseExtraPremFlag("");
		result.setBankAccount("");
		result.setResultCode("0");
		result.setResultMsg("");
		return result;
	}
	
	public com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.RiskInfo initRiskInfo()
	{
		RiskInfo risk = new RiskInfo();
		risk.setAdjustAmount("N");
		risk.setAdjustAmount440("");
		
		ConditionInfoList conditionInfoList = new ConditionInfoList();
		List<ConditionInfo> conditionList = new ArrayList<ConditionInfo>();
		ConditionInfo conditionInfo = new ConditionInfo();
		conditionInfo.setCodition("");
		conditionInfo.setConditionFlag("");
		conditionInfo.setConditionProductName("");
		conditionInfo.setConditionReason("");
		conditionList.add(conditionInfo);
		conditionInfoList.setConditionInfo(conditionList);
		risk.setConditionInfoList(conditionInfoList);
		risk.setDelayReason("");
		risk.setExistCondition("");
		risk.setExistExtraPrem("");
		risk.setExPremWaivedFlag("N");
		risk.setExtraPrem440("");
		ExtraPremInfoList extraPremInfoList = new ExtraPremInfoList();
		List<ExtraPremInfo> extraPremList = new ArrayList<com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.ExtraPremInfo>();
		ExtraPremInfo extraPremInfo = new ExtraPremInfo();
		extraPremInfo.setOption("");
		extraPremInfo.setReason("");
		extraPremInfo.setProductName("");
		List<ProductInfo> productList = new ArrayList<ProductInfo>();
		ProductInfo productInfo = new ProductInfo();
		productInfo.setEndDate("");
		productInfo.setExtraPrem("");
		productInfo.setPrem("");
		productInfo.setStartDate("");
		productList.add(productInfo);
		extraPremInfo.setProductInfo(productList);
		
		extraPremList.add(extraPremInfo);
		extraPremInfoList.setExtraPremInfo(extraPremList);
		risk.setExtraPremInfoList(extraPremInfoList);
		
		risk.setLimitFlag("");
		LimitInfoList limitInfoList = new LimitInfoList();
		List<LimitInfo> limitList = 
				new ArrayList<com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.LimitInfo>();
		LimitInfo limitInfo = new LimitInfo();
		limitInfo.setLimitReason("");
		limitInfo.setLimitProductName("");
		List<LimitProductInfo> limitPorductList = 
				new ArrayList<com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.LimitProductInfo>();
		LimitProductInfo limitProductInfo = new LimitProductInfo();
		limitProductInfo.setAmount("");
		limitProductInfo.setChargePeriod("");
		limitProductInfo.setPrem("");
		limitPorductList.add(limitProductInfo);
		limitInfo.setLimitProductInfo(limitPorductList);
		limitList.add(limitInfo);
		limitInfoList.setLimitInfo(limitList);
		risk.setLimitInfoList(limitInfoList);
		risk.setPremChgFlag440("");
		risk.setRefualReason("");
		risk.setRiskCode("");
		risk.setRiskName("");
		risk.setWaiverExtraPrem("N");
		risk.setWaiverProductName("");
		// #152437 次标通知书新增字段初始化 start
		List<InsuredInfo> infos = new ArrayList<com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.InsuredInfo>();
		InsuredInfo info = new InsuredInfo();
		info.setInsuredCustomerNo("");
		info.setInsuredName("");
		info.setMainInsured("");
		infos.add(info);
		risk.setInsuredInfo(infos);
		risk.setIsMedicare(DictionaryUtil.NO_WORD_STRING);
		risk.setuWVerdict("");
		// #152437 次标通知书新增字段初始化 end
		return risk;
	}
	/***
	 * 次标通知书查询接口
	 */
	@SuppressWarnings("unchecked")
	@Override
	public com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.OutputData csUwSubStandardDocumentQuery(
			com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.InputData inputVO) {
		com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.OutputData outputData = 
				new com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.OutputData();
		QryCsUWDocumentPO po = BeanUtils.copyProperties(QryCsUWDocumentPO.class, inputVO);
		logger.debug("次标通知书查询"+JsonUtil.toJson(inputVO));
		List<QryCsUWDocumentPO> standardDocument = uwDocumentDao.csUwSubStandardDocumentQuery(po);
		com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.vo.Result result=initSubStandardResult();
		if(standardDocument==null || standardDocument.size()<=0){
			result.setResultCode("1");
			result.setResultMsg("未查询到数据");
			
		}else{
			result.setResultCode("0");
			List<RiskInfo> riskInfos=new ArrayList<RiskInfo>();
			
			String extraPremFlag907="N";//涉及 907加费标识 
			for (QryCsUWDocumentPO qryCsUWDocumentPO : standardDocument) {
				try {
					String clobContent = "";
					result.setUwDate(qryCsUWDocumentPO.getUwDate());
					result.setUwUser(qryCsUWDocumentPO.getUwUser());
					if (qryCsUWDocumentPO.getContent()!=null){
						clobContent = AvoidSqlInjectUtil.decodeAngleBrackets(qryCsUWDocumentPO.getContent());// 预览通知书内容;
						
						// 创建SAXReader的对象reader
						SAXReader saxReader = new SAXReader();
						// 通过reader对象的read方法加载content,获取docuemnt对象。
						Document document = saxReader.read(new ByteArrayInputStream(clobContent.getBytes("UTF-8")));
						List<Node> productLists=document.selectNodes("/dataSets/dataSet/data/productListInfo/productList"); //险种集合
						List<Node> remarkNodes=document.selectNodes("/dataSets/dataSet/data/remarkInfo/remarkList");
						RiskInfo riskInfo=null; 
						for (Node productList : productLists) {
							Node productList1 = productList.selectSingleNode("productList1");	//加费、限额、特约产品
							Node productList3 = productList.selectSingleNode("productList3");	//延期 
							Node productList2 = productList.selectSingleNode("productList2");	//拒保 
							if(productList1 != null){
								riskInfo = getProductList1(productList1,remarkNodes,qryCsUWDocumentPO);
								riskInfos.add(riskInfo);
							}else if(productList3 != null){
								riskInfo = getProductList3(productList3,remarkNodes,qryCsUWDocumentPO);
								riskInfos.add(riskInfo);
							}else if(productList2 != null){
								riskInfo = getProductList2(productList2,remarkNodes,qryCsUWDocumentPO);
								riskInfos.add(riskInfo);
							}
						}
						RiskInfoList riskInfoList=new RiskInfoList();
						qryCsUWDocumentPO.getPolicyCodes();
						riskInfoList.setRiskInfo(riskInfos);
						Node addReasonNode=document.selectSingleNode("/dataSets/dataSet/data/productListInfo/productList/productList1/reasonInfo1");
						Node addFeeInfoNode=document.selectSingleNode("/dataSets/dataSet/data/productListInfo/productList/productList1/reasonInfo1/dutyInfo1/dutyList1/addFeeInfo");
						result.setAdverseExtraPremFlag(addFeeInfoNode!=null?"Y":"N");
						if ("Y".equals(result.getAdverseExtraPremFlag())){
							Node interestNode=document.selectSingleNode("/dataSets/dataSet/data/interest");
							Node fristAddMoneyNode=document.selectSingleNode("/dataSets/dataSet/data/fristAddMoney");//首期加费金额
							Node reinstateMoney=document.selectSingleNode("/dataSets/dataSet/data/reinstateMoney");//复效交费金额
							Node sumAddMoney=document.selectSingleNode("/dataSets/dataSet/data/sumAddMoney");//合计加费金额
							if (interestNode==null)
							{
								interestNode=addReasonNode.selectSingleNode("interest");//利息
							}
							if (fristAddMoneyNode==null){
								fristAddMoneyNode=addReasonNode.selectSingleNode("fristAddMoney");//首期加费金额
							}
							if(reinstateMoney==null){
								reinstateMoney=addReasonNode.selectSingleNode("reinstateMoney");//复效交费金额
							}
							if (sumAddMoney==null){
								sumAddMoney=addReasonNode.selectSingleNode("sumAddMoney");
							}
							result.setInitExtraPrem(fristAddMoneyNode!=null?fristAddMoneyNode.getText():"0");
							result.setTotalExtraPrem(sumAddMoney!=null ?sumAddMoney.getText():"0");
							result.setInterest(interestNode!=null?interestNode.getText():"0");
							result.setReinExtraPrem(reinstateMoney!=null?reinstateMoney.getText():"0");
						}else
						{
							result.setInitExtraPrem("N");
							result.setTotalExtraPrem("N");
							result.setInterest("N");
							result.setReinExtraPrem("N");
						}
						Node payWayFlagNode=document.selectSingleNode("/dataSets/dataSet/data/payWayFlag");
						if(payWayFlagNode!=null && "1".equals(payWayFlagNode.getText())){
							result.setPayedFlag("Y");
						}else
						{
							result.setPayedFlag("N");
						}
						String bankAccount="N";
						if ("Y".equals(result.getPayedFlag())){
							Node bankAccountNode=document.selectSingleNode("/dataSets/dataSet/data/bankAccount");
							bankAccount=bankAccountNode!=null?bankAccountNode.getText():"N";
						}
						result.setBankAccount(bankAccount);
						if ("Y".equals(result.getAdverseExtraPremFlag())){
							Node riskNameNode=document.selectSingleNode("/dataSets/dataSet/data/productListInfo/productList/productList1/productName");
							if (riskNameNode!=null){
								String riskName=riskNameNode.getText();
								if (!riskName.isEmpty() && riskName.length()>=3 && "907".equals(riskName.substring(0,3))){
									extraPremFlag907="Y";
									result.setExtraPremFlag907("Y");
								}
							}
						}
						result.setExtraPremFlag907(extraPremFlag907);
						result.setRiskInfoList(riskInfoList);
						//160001
						String remark = "";
						for(Node remarkNode :remarkNodes){
							remark = remark +remarkNode.selectSingleNode("remark").getText();
						}
						if(remark!=null){
							result.setRemark(remark.toString());
						}
					}
					
				} catch (UnsupportedEncodingException | DocumentException e) {
					e.printStackTrace();
				}
			}
		}
		outputData.setResult(result);
		return outputData;
	}
	
	/**
	 * 拒保产品处理
	 * @param productList2
	 * @param remarkNodes
	 * @param qryCsUWDocumentPO
	 * @return
	 */
	private RiskInfo getProductList2(Node productList2, List<Node> remarkNodes,
			QryCsUWDocumentPO qryCsUWDocumentPO) {
		RiskInfo riskInfo = new RiskInfo();
		riskInfo=getRiskInfo(productList2,"2");//拒保产品
		riskInfo.setItemReasonFlag("Y");
		List<ItemReasonList> itemreslist = new ArrayList<ItemReasonList>();
		ItemReasonList reason = new ItemReasonList();
		BigDecimal uwId = qryCsUWDocumentPO.getUwId();
		QryCsUWDocumentPO queryProductLifeInfo = new QryCsUWDocumentPO();
		queryProductLifeInfo.setUwId(uwId);
		queryProductLifeInfo.setDecisionCode("50");							
		if (!riskInfo.getRiskName().isEmpty() && riskInfo.getRiskName().length()>=3 ){									
			queryProductLifeInfo.setBusiProdCode("00"+riskInfo.getRiskName().substring(0,3));
		}							
		// #152437 次标通知书接口被保人信息查询
		Node busiItemId = productList2.selectSingleNode("busiItemId");
		queryProductLifeInfo.setBusiItemId(busiItemId == null ? "" : busiItemId.getText());
		queryProductLifeInfo.setContNo(qryCsUWDocumentPO.getContNo());
		List<QryCsUWDocumentPO> queryProductLifeInfos = uwDocumentDao.queryProductLifeInfo(queryProductLifeInfo);	
		// #152437 新增字段传值
		riskInfo = getRiskInfoByInsured(queryProductLifeInfos,riskInfo);
		// 险种为豁免险则查询投保人信息
		if(CommonUtils.equalsManyOr(riskInfo.getRiskCode(), DictionaryUtil.TYPES_OF_INSURANCE_425,DictionaryUtil.TYPES_OF_INSURANCE_952,DictionaryUtil.TYPES_OF_INSURANCE_861)){
			QryCsUWDocumentPO policyHolder = uwDocumentDao.queryPolicyHolderInfo(queryProductLifeInfo);
			riskInfo = getRiskInfoByPolicyHolder(policyHolder,riskInfo);
		}
		reason.setItemName(queryProductLifeInfos.get(0).getProductName());
		reason.setRefualReasonItem(riskInfo.getRefualReason());
		itemreslist.add(reason);
		riskInfo.setItemReasonList(itemreslist);
		return riskInfo;
	}
	
	/**
	 * 延期产品处理
	 * @param productList3
	 * @param remarkNodes
	 * @param qryCsUWDocumentPO
	 * @return
	 */
	private RiskInfo getProductList3(Node productList3, List<Node> remarkNodes,
			QryCsUWDocumentPO qryCsUWDocumentPO) {
		RiskInfo riskInfo = new RiskInfo();
		riskInfo=getRiskInfo(productList3,"3");//延期产品
		riskInfo.setItemReasonFlag("Y");
		List<ItemReasonList> itemreslist = new ArrayList<ItemReasonList>();							
		ItemReasonList reason = new ItemReasonList();
		BigDecimal uwId = qryCsUWDocumentPO.getUwId();
		QryCsUWDocumentPO queryProductLifeInfo = new QryCsUWDocumentPO();
		queryProductLifeInfo.setUwId(uwId);
		queryProductLifeInfo.setDecisionCode("40");							
		if (!riskInfo.getRiskName().isEmpty() && riskInfo.getRiskName().length()>=3 ){									
			queryProductLifeInfo.setBusiProdCode("00"+riskInfo.getRiskName().substring(0,3));
		}	
		// #152437 次标通知书接口被保人信息查询
		Node busiItemId = productList3.selectSingleNode("busiItemId");
		queryProductLifeInfo.setBusiItemId(busiItemId == null ? "" : busiItemId.getText());
		queryProductLifeInfo.setContNo(qryCsUWDocumentPO.getContNo());
		List<QryCsUWDocumentPO> queryProductLifeInfos = uwDocumentDao.queryProductLifeInfo(queryProductLifeInfo);	
		// #152437 新增字段传值
		riskInfo = getRiskInfoByInsured(queryProductLifeInfos,riskInfo);
		// 险种为豁免险则查询投保人信息
		if(CommonUtils.equalsManyOr(riskInfo.getRiskCode(), DictionaryUtil.TYPES_OF_INSURANCE_425,DictionaryUtil.TYPES_OF_INSURANCE_952,DictionaryUtil.TYPES_OF_INSURANCE_861)){
			QryCsUWDocumentPO policyHolder = uwDocumentDao.queryPolicyHolderInfo(queryProductLifeInfo);
			riskInfo = getRiskInfoByPolicyHolder(policyHolder,riskInfo);
		}
		reason.setItemName(queryProductLifeInfos.get(0).getProductName());
		reason.setDelayReasonItem(riskInfo.getDelayReason());
		itemreslist.add(reason);
		riskInfo.setItemReasonList(itemreslist);
		return riskInfo;
	}
	
	/**
	 * 加费、限额、特约产品处理
	 * @param productNode
	 * @param remarkNodes
	 * @param qryCsUWDocumentPO
	 * @return
	 */
	private RiskInfo getProductList1(Node productNode, List<Node> remarkNodes, QryCsUWDocumentPO qryCsUWDocumentPO) {
		RiskInfo riskInfo = new RiskInfo();
		Node remakDetailNode=null;
		riskInfo=getRiskInfo(productNode,"1");
		String remark = "";
		String premChgFlag440 = "N";
		String exPremWaivedFlag = "N";
		String waiverProductName = "";// 豁免险名称
		String adjustAmount = "N", adjustAmount440 = "N";
		String waiverExtraPrem = "N", extraPrem440 = "N";
		if (CommonUtils.equalsManyOr(riskInfo.getExistExtraPrem(), "Y")) {
			// 查询险种对应的险种标准名称
			BusinessProductPO businessProductPO = new BusinessProductPO();
			businessProductPO.setProductCodeSys(riskInfo.getRiskCode());
			businessProductPO = businessProductDao.findBusinessProductQryOfNb(businessProductPO );
			if(remarkNodes != null && remarkNodes.size() > 0){
				remarkNodes.get(0).selectSingleNode("remark").getText();
				for (Node remarkNode : remarkNodes) {
					if (businessProductPO == null || StringUtils.isEmpty(businessProductPO.getProductNameStd())) {
						break;
					}
					remakDetailNode = remarkNode.selectSingleNode("remark");
					remark = remakDetailNode != null ? remakDetailNode.getText() : "";
					if (remark.contains(businessProductPO.getProductNameStd())) {
						Map<String, String> waviverValues = parserWaverRisk(remark);
						String waiverProductName_new = waviverValues.get("waiverProductName");
						if (StringUtils.isNotEmpty(waiverProductName_new)) {
							exPremWaivedFlag = waviverValues.get("exPremWaivedFlag");
							waiverProductName = waviverValues.get("waiverProductName");
							waiverExtraPrem = waviverValues.get("waiverExtraPrem");
							adjustAmount = waviverValues.get("adjustAmount");
						} else {
							premChgFlag440 = waviverValues.get("premChgFlag440");
							adjustAmount440 = waviverValues.get("adjustAmount440");
							extraPrem440 = waviverValues.get("extraPrem440");
						}
					}
				}
			}
		}
		riskInfo.setWaiverProductName(waiverProductName);
		riskInfo.setPremChgFlag440(premChgFlag440);
		riskInfo.setExPremWaivedFlag(exPremWaivedFlag);
		riskInfo.setAdjustAmount440(adjustAmount440);
		riskInfo.setExtraPrem440(extraPrem440);
		riskInfo.setAdjustAmount(adjustAmount);
		riskInfo.setWaiverExtraPrem(waiverExtraPrem);
		
		if(riskInfo.getItemReasonList()!=null && riskInfo.getItemReasonList().size()>0){
			riskInfo.setItemReasonFlag("Y");
		}else{
			riskInfo.setItemReasonFlag("N");	
		}			
		BigDecimal uwId = qryCsUWDocumentPO.getUwId();
		QryCsUWDocumentPO queryProductLifeInfo = new QryCsUWDocumentPO();
		queryProductLifeInfo.setUwId(uwId);
		if (!riskInfo.getRiskName().isEmpty() && riskInfo.getRiskName().length()>=3 ){									
			queryProductLifeInfo.setBusiProdCode("00"+riskInfo.getRiskName().substring(0,3));
		}	
		// #152437 次标通知书接口被保人信息查询
		Node busiItemId = productNode.selectSingleNode("busiItemId");
		queryProductLifeInfo.setBusiItemId(busiItemId == null ? "" : busiItemId.getText());
		queryProductLifeInfo.setContNo(qryCsUWDocumentPO.getContNo());
		
		List<QryCsUWDocumentPO> queryProductLifeInfos = uwDocumentDao.queryProductLifeInfo(queryProductLifeInfo);
		// #152437 新增字段传值
		riskInfo = getRiskInfoByInsured(queryProductLifeInfos,riskInfo);
		// 险种为豁免险则查询投保人信息
		if(CommonUtils.equalsManyOr(riskInfo.getRiskCode(), DictionaryUtil.TYPES_OF_INSURANCE_425,DictionaryUtil.TYPES_OF_INSURANCE_952,DictionaryUtil.TYPES_OF_INSURANCE_861)){
			QryCsUWDocumentPO policyHolder = uwDocumentDao.queryPolicyHolderInfo(queryProductLifeInfo);
			riskInfo = getRiskInfoByPolicyHolder(policyHolder,riskInfo);
		}
		return riskInfo;
	}
	/**
	 * 豁免险处理被保人信息
	 * @param policyHolder
	 * @param riskInfo
	 * @return
	 */
	private RiskInfo getRiskInfoByPolicyHolder(QryCsUWDocumentPO policyHolder,RiskInfo riskInfo) {
		List<InsuredInfo> infos = new ArrayList<>();
		InsuredInfo info = new InsuredInfo();
		if(!StringUtils.isEmpty(policyHolder.getOldCustomerId())){
			info.setInsuredCustomerNo(policyHolder.getOldCustomerId());
		}
		if(!StringUtils.isEmpty(policyHolder.getCustomerName())){
			info.setInsuredName(policyHolder.getCustomerName());
		}
		// 是否是主被保人 Y：是    N：否
		info.setMainInsured("");
		infos.add(info);
		riskInfo.setInsuredInfo(infos);
		return riskInfo;
	}
	/**
	 * #152437 次标通知书新增被保人信息，核保结论
	 * @param queryProductLifeInfos
	 * @param riskInfo
	 * @return
	 */
	private RiskInfo getRiskInfoByInsured(List<QryCsUWDocumentPO> queryProductLifeInfos, RiskInfo riskInfo) {
		// #152437 次标通知书新增被保人信息 start
		List<InsuredInfo> infos = getInsuredInfos(queryProductLifeInfos);
		riskInfo.setInsuredInfo(infos);
		// #152437 次标通知书新增被保人信息 end
		for (QryCsUWDocumentPO qryCsUWDocumentPO2 : queryProductLifeInfos) {
			riskInfo.setuWVerdict(qryCsUWDocumentPO2.getDecisionCode() == null ? "" : qryCsUWDocumentPO2.getDecisionCode());// 核保结论
		}
		return riskInfo;
	}
	private Map<String,String> parserWaverRisk(String remark){
		String premChgFlag440="N",exPremWaivedFlag="N",waiverProductName="",adjustAmount440="N",extraPrem440="N",adjustAmount="N",waiverExtraPrem="N";
		int startIndex=0;
		int endIndex=0;
		Map<String,String> map=new HashMap<String,String>();
		if (remark.contains("《附加金满多两全保险》每期应交纳的保险费")){
			premChgFlag440="Y";
			exPremWaivedFlag="N";
			waiverProductName="";
			startIndex=remark.lastIndexOf("每期应交纳的保险费相应调整为");
			endIndex=remark.lastIndexOf("元，每期增加");
			adjustAmount440=remark.substring(startIndex+14,endIndex+1);
			startIndex=remark.indexOf("元，每期增加");
			endIndex=remark.lastIndexOf("元");
			extraPrem440=remark.substring(startIndex+6,endIndex+1);
		}else if (remark.contains("》每期豁免保险费发生变化，《") && remark.contains("》每期应交纳的保险费相应调整为")){
			exPremWaivedFlag="Y";
			premChgFlag440="N";
			startIndex=remark.lastIndexOf("《");
			endIndex=remark.lastIndexOf("》");
			waiverProductName=remark.substring(startIndex+1,endIndex);
			startIndex=remark.indexOf("每期应交纳的保险费相应调整为");
			endIndex=remark.indexOf("元，每期增加");
			adjustAmount=remark.substring(startIndex+14,endIndex+1);
			startIndex=remark.indexOf("元，每期增加");
			endIndex=remark.lastIndexOf("元");
			waiverExtraPrem=remark.substring(startIndex+6,endIndex+1);
		}
		map.put("premChgFlag440", premChgFlag440);
		map.put("exPremWaivedFlag", exPremWaivedFlag);
		map.put("waiverProductName", waiverProductName);
		map.put("adjustAmount440", adjustAmount440);
		map.put("extraPrem440", extraPrem440);
		map.put("adjustAmount", adjustAmount);
		map.put("waiverExtraPrem", waiverExtraPrem);
		return map;
	}
	/**
	 * 
	 * @description 递归查询报文节点信息
	 * @param ele 报文element对象
	 * @param map 重组map
	 */
	public Map<String,Object> getElementChildNodes(Element ele,Map<String,Object> map){
		Iterator<Node> nodeIterator = ele.nodeIterator();
		while (nodeIterator.hasNext()) {
			Node node = (Node) nodeIterator.next();
			if(node != null && !StringUtilsEx.isNullOrEmpty(node.getName())){
				String name = node.getName();
				String text = node.getText();
				Element parent = node.getParent();
				String parentName = parent.getName();
				if(!StringUtilsEx.isNullOrEmpty(parentName) && parentName != "data"){
					String key = parentName+name;
					if(map.containsKey(key)){
						String mapValue = map.get(key).toString();
						map.put(key, mapValue+","+text);
					}else{
						map.put(key, text);
					}
				}else{
					String key = name;
					if(map.containsKey(key)){
						String mapValue = map.get(key).toString();
						map.put(key, mapValue+","+text);
					}else{
						map.put(key, text);
					}
				}
			}
		}
		return map;
	}
	
	private RiskInfo getRiskInfo(Node productNode,String productListIndex){
		RiskInfo riskInfo=initRiskInfo();
		riskInfo.setRiskName(productNode.selectSingleNode("productName")!=null?productNode.selectSingleNode("productName").getText():"");
		riskInfo.setRiskCode(productNode.selectSingleNode("productNo")!=null?productNode.selectSingleNode("productNo").getText():"");
		productNode.selectSingleNode("bussItemId");
		if ("1".equals(productListIndex)){//加费、特约、限额产品处理
			riskInfo=setRiskInfoMoreInfo(productNode,"1",riskInfo);//加费
			riskInfo=setRiskInfoMoreInfo(productNode,"2",riskInfo);//限额
			riskInfo=setRiskInfoMoreInfo(productNode,"3",riskInfo);//特约
			riskInfo=setRiskInfoMoreInfo(productNode,"4",riskInfo);//多责任延期
			riskInfo=setRiskInfoMoreInfo(productNode,"5",riskInfo);//多责任拒保
			if (riskInfo.getConditionInfoList().getConditionInfo()!=null && riskInfo.getConditionInfoList().getConditionInfo().size()>0){
				ConditionInfo conditionInfo=riskInfo.getConditionInfoList().getConditionInfo().get(0);
				if (!conditionInfo.getConditionProductName().isEmpty()){
					riskInfo.setExistCondition("Y");
				}else
				{
					riskInfo.setExistCondition("N");
				}
			}
			if (riskInfo.getLimitInfoList().getLimitInfo()!=null && riskInfo.getLimitInfoList().getLimitInfo().size()>0){
				LimitInfo limitInfo=riskInfo.getLimitInfoList().getLimitInfo().get(0);
				if (!limitInfo.getLimitProductName().isEmpty()){
					riskInfo.setLimitFlag("Y");
				}else
				{
					riskInfo.setLimitFlag("N");
				}
			}
			if (riskInfo.getExtraPremInfoList().getExtraPremInfo()!=null && riskInfo.getExtraPremInfoList().getExtraPremInfo().size()>0){
				ExtraPremInfo extraPremInfo=riskInfo.getExtraPremInfoList().getExtraPremInfo().get(0);
				if (!extraPremInfo.getProductName().isEmpty()){
					riskInfo.setExistExtraPrem("Y");
				}else
				{
					riskInfo.setExistExtraPrem("N");
				}
			}
			if(riskInfo.getRiskCode().isEmpty()){
				if (!riskInfo.getRiskName().isEmpty() && riskInfo.getRiskName().length()>=3 ){
					riskInfo.setRiskCode("00"+riskInfo.getRiskName().substring(0,3)+"000");
				}
			}
			// 152437 是否为医疗险
			riskInfo.setIsMedicare(DictionaryUtil.NO_WORD_STRING);
			Node reasonInfo3 = productNode.selectSingleNode("reasonInfo3");
			if(reasonInfo3 != null){
				Node isMediRisk = reasonInfo3.selectSingleNode("isMediRisk");
				if(isMediRisk != null){
					if(DictionaryUtil.ONE_STRING.equals(isMediRisk.getText())){
						riskInfo.setIsMedicare(DictionaryUtil.IS_WORD_STRING);
					}
				}
			}
		}else if ("2".equals(productListIndex)){//拒保产品处理
			Node refualReasonNode=productNode.selectSingleNode("refuseReason");
			riskInfo.setRefualReason(refualReasonNode!=null?refualReasonNode.getText():"");
			// 152437  获取险种编码
			if(riskInfo.getRiskCode().isEmpty()){
				if (!riskInfo.getRiskName().isEmpty() && riskInfo.getRiskName().length()>=3 ){
					riskInfo.setRiskCode("00"+riskInfo.getRiskName().substring(0,3)+"000");
				}
			}
		}else if ("3".equals(productListIndex)){//延期产品处理
			Node delayReasonNode=productNode.selectSingleNode("deferReason");
			riskInfo.setDelayReason(delayReasonNode!=null?delayReasonNode.getText():"");
			// 152437  获取险种编码
			if(riskInfo.getRiskCode().isEmpty()){
				if (!riskInfo.getRiskName().isEmpty() && riskInfo.getRiskName().length()>=3 ){
					riskInfo.setRiskCode("00"+riskInfo.getRiskName().substring(0,3)+"000");
				}
			}
		}
		// 152437  获取豁免责任标识 
		String isWarverFlag = getIsWaiver(riskInfo.getRiskCode());
		riskInfo.setIsWaiver(isWarverFlag);
		return riskInfo;
	}
	
	private RiskInfo setRiskInfoMoreInfo(Node productNode,String keyIndex,RiskInfo riskInfo){
		List<ConditionInfo> conditionInfos=new ArrayList<ConditionInfo>();
		List<LimitInfo> limitInfos=new ArrayList<LimitInfo>();;
		List<ExtraPremInfo> extraPremInfos=new ArrayList<ExtraPremInfo>();
		String refualReason=null;
		String delayReason=null;
		ConditionInfoList conditionInfoList=new ConditionInfoList();
		LimitInfoList limitInfoList=new LimitInfoList();
		ExtraPremInfoList extraPremInfoList=new ExtraPremInfoList();;
		Node reasonNode=productNode.selectSingleNode("reasonInfo"+keyIndex);
		if (reasonNode!=null && reasonNode.hasContent()){
			Node reasonTypeCode=null;
			List<Node> dutyInfoNodes=null;
			reasonTypeCode=reasonNode.selectSingleNode("reasonTypeCode");
			dutyInfoNodes=reasonNode.selectNodes("dutyInfo"+keyIndex+"/dutyList"+keyIndex);//特约责任列表
			Node refualReasonNode=reasonNode.selectSingleNode("refuseReason");//多责任组拒保原因
			Node delayReasonNode=reasonNode.selectSingleNode("deferReason");//多责任组延期原因						
			Node isMediRiskNode=reasonNode.selectSingleNode("isMediRisk");
			for (Node dutyInfo:dutyInfoNodes){
				if (reasonTypeCode!=null && reasonTypeCode.getText()!=null && "33".equals(reasonTypeCode.getText())){//特约
					Node specialReason=null;//特约原因
					Node dutyNameNode=dutyInfo.selectSingleNode("dutyName");
					specialReason=reasonNode.selectSingleNode("specialReason");//特约原因
					List<Node> conditionNodes=dutyInfo.selectNodes("specialInfo/specialList/specialArrage");//特约内容
					String condition="";
					for (Node conditonNode:conditionNodes){
						if(conditonNode.getText()!=null){
							if (!condition.isEmpty()){
								condition=condition+","+conditonNode.getText();
							}else
							{
								condition=conditonNode.getText();
							}
						}
					}
					
					ConditionInfo conditionInfo=new ConditionInfo();
					conditionInfo.setConditionReason(specialReason!=null ?specialReason.getText():"");
					conditionInfo.setConditionProductName(dutyNameNode!=null ?dutyNameNode.getText():"");
					conditionInfo.setCodition(condition);
					if (isMediRiskNode!=null && "1".equals(isMediRiskNode.getText())){
						conditionInfo.setConditionFlag("Y");
					}else{
						conditionInfo.setConditionFlag("N");
					}
					conditionInfos.add(conditionInfo);
					if (conditionInfos!=null && conditionInfos.size()>0){
						conditionInfoList.setConditionInfo(conditionInfos);
						riskInfo.setConditionInfoList(conditionInfoList);
						riskInfo.setExistCondition("Y");
					}else
					{
						riskInfo.setExistCondition("N");
					}
				}else if (reasonTypeCode!=null && reasonTypeCode.getText()!=null && "32".equals(reasonTypeCode.getText())){//限额
					LimitInfo limitInfo=new LimitInfo();
					List<LimitProductInfo> limiProductInfos=new ArrayList<LimitProductInfo>();
					Node dutyNameNode=dutyInfo.selectSingleNode("dutyName");
					Node limitReasonNode=reasonNode.selectSingleNode("limitReason");
					Node newCoverageCopyNode=dutyInfo.selectSingleNode("newCoverageCopy");
					Node payPeriodNode=dutyInfo.selectSingleNode("payPeriod");
					Node periodPayPremNode=dutyInfo.selectSingleNode("periodPayPrem");
					limitInfo.setLimitProductName(dutyNameNode!=null?dutyNameNode.getText():"");
					limitInfo.setLimitReason(limitReasonNode!=null?limitReasonNode.getText():"");
					LimitProductInfo limitProductInfo=new LimitProductInfo();
					limitProductInfo.setAmount(newCoverageCopyNode!=null?newCoverageCopyNode.getText():"");
					limitProductInfo.setChargePeriod(payPeriodNode!=null ?payPeriodNode.getText():"");
					limitProductInfo.setPrem(periodPayPremNode!=null ?periodPayPremNode.getText():"");
					limiProductInfos.add(limitProductInfo);
					limitInfo.setLimitProductInfo(limiProductInfos);
					limitInfos.add(limitInfo);
					if (limitInfos!=null && limitInfos.size()>0){
						limitInfoList.setLimitInfo(limitInfos);
						riskInfo.setLimitInfoList(limitInfoList);
						riskInfo.setLimitFlag("Y");
					}else
					{
						riskInfo.setLimitFlag("N");
					}
				}else if (reasonTypeCode!=null && reasonTypeCode.getText()!=null && "31".equals(reasonTypeCode.getText())){//加费 
					
					Node dutyNameNode=dutyInfo.selectSingleNode("dutyName");
					Node addFeeInfoNode=dutyInfo.selectSingleNode("addFeeInf");
					Node addFeeReasonNode=reasonNode.selectSingleNode("addFeeReason");
					Node addFeeSeclectNode=reasonNode.selectSingleNode("addFeeSeclect");
					ExtraPremInfo extraPremInfo=new ExtraPremInfo();
					extraPremInfo.setProductName(dutyNameNode!=null?dutyNameNode.getText():"");
					extraPremInfo.setOption(addFeeSeclectNode!=null?addFeeSeclectNode.getText():"");
					extraPremInfo.setReason(addFeeReasonNode!=null?addFeeReasonNode.getText():"");
					List<ProductInfo> productInfos=new ArrayList<ProductInfo>();
					Node productInfoNode=dutyInfo.selectSingleNode("addFeeInfo/addFeeList");
					if (productInfoNode.hasContent()){
						ProductInfo productInfo=new ProductInfo();
						Node childNode=null;
						childNode=productInfoNode.selectSingleNode("addFeeEnd");
						productInfo.setEndDate(childNode!=null?childNode.getText():"");
						childNode=productInfoNode.selectSingleNode("addFeeStart");
						productInfo.setStartDate(childNode!=null?childNode.getText():"");
						childNode=productInfoNode.selectSingleNode("addMoney");
						productInfo.setExtraPrem(childNode!=null?childNode.getText():"");
						childNode=productInfoNode.selectSingleNode("periodPayPrem");
						productInfo.setPrem(childNode!=null?childNode.getText():"");
						productInfos.add(productInfo);
						extraPremInfo.setProductInfo(productInfos);
					}
					extraPremInfos.add(extraPremInfo);
					if (extraPremInfos!=null && extraPremInfos.size()>0){
						extraPremInfoList.setExtraPremInfo(extraPremInfos);
						riskInfo.setExtraPremInfoList(extraPremInfoList);
						riskInfo.setExistExtraPrem("Y");
					}else
					{
						riskInfo.setExistExtraPrem("N");
					}
				}else if (reasonTypeCode!=null && reasonTypeCode.getText()!=null && "40".equals(reasonTypeCode.getText())){//延期
					riskInfo.setItemReasonFlag("Y");
					Node dutyNameNode=dutyInfo.selectSingleNode("dutyName");
					List<ItemReasonList> itemreslist = new ArrayList<ItemReasonList>();
					ItemReasonList reason = new ItemReasonList();												
					reason.setItemName(dutyNameNode!=null ?dutyNameNode.getText():"");
					reason.setDelayReasonItem(delayReasonNode!=null?delayReasonNode.getText():"");
					itemreslist.add(reason);
					riskInfo.setItemReasonList(itemreslist);
				}else if (reasonTypeCode!=null && reasonTypeCode.getText()!=null && "50".equals(reasonTypeCode.getText())){//拒保
					riskInfo.setItemReasonFlag("Y");
					Node dutyNameNode=dutyInfo.selectSingleNode("dutyName");
					List<ItemReasonList> itemreslist = new ArrayList<ItemReasonList>();
					ItemReasonList reason = new ItemReasonList();												
					reason.setItemName(dutyNameNode!=null ?dutyNameNode.getText():"");
					reason.setRefualReasonItem(refualReasonNode!=null ?refualReasonNode.getText():"");
					itemreslist.add(reason);
					riskInfo.setItemReasonList(itemreslist);
				}
			}
		}
		return riskInfo;
	}
	/**
	 * 
	 * @description 获取加费金额
	 * @param remark 报文节点
	 * @return
	 */
	private String[] extractAmont(String remark) {
		Pattern pattern = Pattern.compile("\\d+\\.\\d+"); 
		Matcher matcher = pattern.matcher(remark); 
		String str = "";
		while (matcher.find()) { 
			str += matcher.group(0)+",";
		} 
		String[] amount = str.substring(0, str.length()-1).split(",");
		return amount;
	}
	
	/**
	 * 获取豁免责任标识
	 * 00193000/00425000/00952000/00861000产品代码 返回是，否则返回否
	 * @param riskCode
	 * @return
	 */
	private String getIsWaiver(String riskCode) {	
		// 获取险种豁免责任标识
		BusinessProductPO businessProductPO = new BusinessProductPO();
		businessProductPO.setProductCodeSys(riskCode);
		BusinessProductPO businessProduct = businessProductDao.findBusinessProductQryOfNb(businessProductPO);
		if (null !=businessProduct 
				&& StringUtils.isNotBlank(businessProduct.getWaiverCustomerRole())
				//豁免投保人
				&& DictionaryUtil.WAIVER_HOLDER_FLAG.equals(businessProduct.getWaiverCustomerRole())) {
			return DictionaryUtil.IS_WORD_STRING;
		} else {
			return DictionaryUtil.NO_WORD_STRING;
		}
	}
	
	/**
	 * 获取被保人信息
	 * @param queryProductLifeInfos
	 * @return
	 */
	public List<InsuredInfo> getInsuredInfos(List<QryCsUWDocumentPO> queryProductLifeInfos){
		List<InsuredInfo> infos = new ArrayList<InsuredInfo>();
		Set<String> oldCustomerIds = new HashSet<String>();
		for (QryCsUWDocumentPO qryCsUWDocumentPO : queryProductLifeInfos) {
			if(qryCsUWDocumentPO != null){
				BigDecimal orderId = qryCsUWDocumentPO.getOrderId();		// 是否为主被保人
				String oldCustomerId = qryCsUWDocumentPO.getOldCustomerId();// 被保人客户号
				String customerName = qryCsUWDocumentPO.getCustomerName();	// 被保人姓名
				InsuredInfo info = new InsuredInfo();
				if(!StringUtils.isEmpty(oldCustomerId)){
					if (oldCustomerIds.contains(oldCustomerId)) {
						continue;
					}
					info.setInsuredCustomerNo(oldCustomerId);
				}
				if(!StringUtils.isEmpty(customerName)){
					info.setInsuredName(customerName);
				}
				// 是否是主被保人 Y：是    N：否
				if(CommonUtils.equalsManyOr(DictionaryUtil.ONE_STRING, orderId.toString())){
					info.setMainInsured(DictionaryUtil.YES_STRING);
				}else{
					info.setMainInsured(DictionaryUtil.NO_STRING);
				}
				oldCustomerIds.add(oldCustomerId);
				infos.add(info);
				info = new InsuredInfo();
			}
		}
		return infos;
	}
	
	/**
	 * 获取险种顺序
	 * @param riskCodes
	 * @return
	 */
	private List<String> getRiskCodeList(List<Node> riskCodes) {
		// 险种顺序集合
		List<String> riskCodeList = new ArrayList<>();
		for (Node risk : riskCodes) {
			Node riskCodeByProduct1 = risk.selectSingleNode("productList1/productNo");	//加费、限额、特约产品
			Node riskCodeByProduct3 = risk.selectSingleNode("productList3/productNo");	//延期 
			Node riskCodeByProduct2 = risk.selectSingleNode("productList2/productNo");	//拒保 
			if(riskCodeByProduct1 != null){
				riskCodeList.add(riskCodeByProduct1.getText());
			}else if(riskCodeByProduct3 != null){
				riskCodeList.add(riskCodeByProduct3.getText());
			}else if(riskCodeByProduct2 != null){
				riskCodeList.add(riskCodeByProduct2.getText());
			}else{
				riskCodeList.add("");
			}
		}
		return riskCodeList;
	}
	
	/**
	 * 险种顺序排序
	 * @param riskCodeList
	 * @param riskInfos
	 * @return
	 */
	private List<RiskInfo> getRiskInfos(List<String> riskCodeList,
			List<RiskInfo> riskInfos) {
		List<RiskInfo> riskInfos1 = new ArrayList<>();
		for (String riskCode : riskCodeList) {
			for (RiskInfo risk : riskInfos) {
				if(riskCode.equals(risk.getRiskCode())){
					riskInfos1.add(risk);
				}
				continue;
			}
		}
		return riskInfos1;
	}
	
	public ICustomerDao getCustomerDao() {
		return customerDao;
	}

	public void setCustomerDao(ICustomerDao customerDao) {
		this.customerDao = customerDao;
	}

	public ICsUwDocumentListQueryDao getUwDocumentDao() {
		return uwDocumentDao;
	}

	public void setUwDocumentDao(ICsUwDocumentListQueryDao uwDocumentDao) {
		this.uwDocumentDao = uwDocumentDao;
	}
	
	public IAskforinfoDao getAskforinfoDao() {
		return askforinfoDao;
	}
	public void setAskforinfoDao(IAskforinfoDao askforinfoDao) {
		this.askforinfoDao = askforinfoDao;
	}
	public IAskforinfoDetailDao getAskforinfoDetailDao() {
		return askforinfoDetailDao;
	}
	public void setAskforinfoDetailDao(IAskforinfoDetailDao askforinfoDetailDao) {
		this.askforinfoDetailDao = askforinfoDetailDao;
	}
	
}
