package com.nci.tunan.qry.impl.jrqd.service.cs.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsAccountFeeInfoReqVO;
import com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeReqVO;
import com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeResVO;
import com.nci.tunan.mms.interfaces.query.exports.universalFeeQuery.vo.MmsUniversalFeeQueryReqVO;
import com.nci.tunan.mms.interfaces.query.exports.universalFeeQuery.vo.MmsUniversalFeeQueryResVO;
import com.nci.tunan.mms.interfaces.query.exports.universalFeeQuery.vo.MmsUniversalFeeQueryVO;
import com.nci.tunan.qry.impl.jrqd.common.Constants;
import com.nci.tunan.qry.impl.jrqd.common.ConstantsBusiProdCode;
import com.nci.tunan.qry.impl.jrqd.common.SERVICE;
import com.nci.tunan.qry.impl.jrqd.dao.cs.ICsEntryDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractBusiProdDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractMasterDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractProductDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IFormulaDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IFundSettlementDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IFundTransDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IILPUChargeDeductionDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPayDueDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPayPlanDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPolicyFundChargeDao;
import com.nci.tunan.qry.impl.jrqd.service.cs.ICsEndorseCTCalculateService;
import com.nci.tunan.qry.impl.jrqd.service.cs.ICsEndorseCTSaveService;
import com.nci.tunan.qry.impl.jrqd.service.cs.ICsEndorseCTService;
import com.nci.tunan.qry.impl.jrqd.service.cs.ICsGeneralInfoService;
import com.nci.tunan.qry.impl.jrqd.service.pa.ICalculateIlpuDueFee;
import com.nci.tunan.qry.impl.jrqd.service.pa.IContinueBonusDService;
import com.nci.tunan.qry.impl.jrqd.service.pa.IFormulaService;
import com.nci.tunan.qry.impl.jrqd.service.pa.IPRDService;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.cs.CsContractMasterBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.cs.SurrenderBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.pa.ContinueBonusBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.pa.FormulaBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.pa.ParamOrResultBO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.cs.CsQueryParamPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractInvestPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractMasterPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractProductPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.FundSettlementPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.FundTransPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PayDuePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PayPlanPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PolicyFundChargePO;
import com.nci.tunan.qry.interfaces.model.jrqd.vo.pa.ILPUFeeType;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;

/**
 * @description 退保Service接口实现类
 *              复制自com.nci.tunan.cs.impl.csItem.service.impl.CsEndorseCTCalculateServiceImpl
 * @<NAME_EMAIL>
 * @date Mar 30, 2022 4:27:29 PM
 * @.belongToModule
 */
public class CsEndorseCTCalculateServiceImpl extends CSItemBaseServiceImpl implements ICsEndorseCTCalculateService {

	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * @Fields csEndorseCTSaveService : 退保保存补退费的信息Service接口
	 */
	@Autowired
	@Qualifier("JRQD_CS_CsEndorseCTSaveService")
	private ICsEndorseCTSaveService csEndorseCTSaveService;

	/**
	 * @Fields csEndorseCTService : 退保Service接口
	 */
	@Autowired
	@Qualifier("JRQD_CS_CsEndorseCTService")
	private ICsEndorseCTService csEndorseCTService;

	/**
	 * @Fields calculateIlpuDueFee : 计算投连万能费用扣除金额Service接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_CalculateIlpuDueFee")
	private ICalculateIlpuDueFee calculateIlpuDueFee;

	/**
	 * @Fields formulaService : 万能结算Service接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_FormulaService")
	private IFormulaService formulaService;

	/**
	 * @Fields continueBonusDService : 持续奖金批处理service接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContinueBonusDService")
	private IContinueBonusDService continueBonusDService;

	/**
	 * @Fields pQueryService : 产品Service接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PRDService")
	private IPRDService pQueryService;

	/**
	 * @Fields csGeneralInfoService : 保全生效：保单基本相关Service接口
	 */
	@Autowired
	@Qualifier("JRQD_CS_CsGeneralInfoService")
	private ICsGeneralInfoService csGeneralInfoService;

	/**
	 * @Fields payDueDao : 生存给付信息Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PayDueDao")
	private IPayDueDao payDueDao;

	/**
	 * @Fields contractMasterDao : 保单主表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractMasterDao")
	private IContractMasterDao contractMasterDao;

	/**
	 * @Fields payPlanDao : 给付计划Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PayPlanDao")
	private IPayPlanDao payPlanDao;

	/**
	 * @Fields fundTransDao : 保单投资连结交易Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_FundTransDao")
	private IFundTransDao fundTransDao;

	/**
	 * @Fields contractProductDao : 责任组Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractProductDao")
	private IContractProductDao contractProductDao;

	/**
	 * @Fields csEntryDao : 保全录入功能信息查询功能实现类Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_CS_CsEntryDao")
	private ICsEntryDao csEntryDao;

	/**
	 * @Fields contractBusiProdDao : 保单险种信息Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractBusiProdDao")
	private IContractBusiProdDao contractBusiProdDao;

	/**
	 * @Fields fundSettlementDao : 万能险结算Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_FundSettlementDao")
	private IFundSettlementDao fundSettlementDao;

	/**
	 * @Fields formulaDao : 投连交易查询Dao接口类
	 */
	@Autowired
	@Qualifier("JRQD_PA_FormulaDao")
	private IFormulaDao formulaDao;

	/**
	 * @Fields ilpuChargeDeductionDao : 投连万能费用扣除Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ILPUChargeDeductionDao")
	private IILPUChargeDeductionDao ilpuChargeDeductionDao;

	/**
	 * @Fields policyFundChargeDao : 基金收费Dao接口类
	 */
	@Autowired
	@Qualifier("JRQD_PA_PolicyFundChargeDao")
	private IPolicyFundChargeDao policyFundChargeDao;

	/**
	 * 
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseCTCalculateService#findInvestAmountInfo(com.nci.tunan.cs.model.bo.SurrenderBO,
	 *      java.util.List)
	 * @param surrenderBO     参数
	 * @param conInvestPOList 参数
	 * @return
	 */
	public SurrenderBO findInvestAmountInfo(SurrenderBO surrenderBO, List<ContractInvestPO> conInvestPOList) {
		if (CollectionUtilEx.isEmpty(conInvestPOList)) {
			return surrenderBO;
		}

		if (ConstantsBusiProdCode.BUSI_CODE_LIST_928.contains(surrenderBO.getBusiProdCode())) {
			// 1.928被保险人已办理养老年金申请的，开始领取养老年金后的保险单的现金价值为0。
			PayDuePO payDuePO = new PayDuePO();
			payDuePO.setBusiItemId(surrenderBO.getBusiItemId());
			payDuePO.setLiabId(Constants.LIAB_ID_1106);
			payDuePO.setFeeStatus(Constants.FEE_STATUS_01);
			payDuePO.setPolicyId(surrenderBO.getPolicyId());
			int count = payDueDao.findPayDueTotal(payDuePO);
			if (count > 0) {
				BigDecimal cashValue = calcCashValueFor928(surrenderBO);
				surrenderBO.setCashValue(cashValue);
				surrenderBO.setInvestCashValue(cashValue);
				surrenderBO.setUniversallCashValue(cashValue);
				surrenderBO.setInvestReissuedInterest(BigDecimal.ZERO);
				surrenderBO.setPolicyFee(BigDecimal.ZERO);
				surrenderBO.setRiskFee(BigDecimal.ZERO);
				surrenderBO.setConBonuse(BigDecimal.ZERO);
				return surrenderBO;
			}

			/*
			 * @invalid 被保险人80周岁保单生效对应日仍未办理的，按照条款约定，本公司将以被保险人80周岁保单生效对应日作为养老年金开始领取日。
			 * 
			 * @invalid 应按被保险人80周岁保单生效对应日的账户价值转换养老年金领取
			 */
			Date calcDate = queryCalcEffectDate(surrenderBO);
			surrenderBO.setApplyTime(calcDate);
		}

		BigDecimal cashValue = BigDecimal.ZERO;
		BigDecimal policyFee = BigDecimal.ZERO;
		BigDecimal riskFee = BigDecimal.ZERO;
		BigDecimal conBonuse = BigDecimal.ZERO;
		BigDecimal investReissuedInterest = BigDecimal.ZERO;
		for (ContractInvestPO conInvestPO : conInvestPOList) {
			SurrenderBO tempSurrenderBO = BeanUtils.copyProperties(SurrenderBO.class, surrenderBO);
			tempSurrenderBO = findInvestAmountInfo(tempSurrenderBO, conInvestPO);
			// 1.1 本息合计
			if (tempSurrenderBO.getUniversallCashValue() != null) {
				cashValue = cashValue.add(tempSurrenderBO.getUniversallCashValue());
			}
			// 1.2 保单管理费
			if (tempSurrenderBO.getPolicyFee() != null) {
				policyFee = policyFee.add(tempSurrenderBO.getPolicyFee());
			}
			// 1.3 风险费
			if (tempSurrenderBO.getRiskFee() != null) {
				riskFee = riskFee.add(tempSurrenderBO.getRiskFee());
			}
			// 1.4 持续奖金
			if (tempSurrenderBO.getConBonuse() != null) {
				conBonuse = conBonuse.add(tempSurrenderBO.getConBonuse());
			}

			if (tempSurrenderBO.getInvestReissuedInterest() != null) {
				investReissuedInterest = investReissuedInterest.add(tempSurrenderBO.getInvestReissuedInterest());
			}

			if (ConstantsBusiProdCode.BUSI_CODE_LIST_928.contains(surrenderBO.getBusiProdCode())
					&& !StringUtilsEx.isNullOrEmpty(tempSurrenderBO.getAcceptCode())
					&& !"1".equals(surrenderBO.getQueryFlag())) {
				// 1.5保存账户交易记录
				tempSurrenderBO.setInvestCashValue(tempSurrenderBO.getUniversallCashValue());
				List<String> serviceCodeList = new ArrayList<String>();
				serviceCodeList.add(SERVICE.CsEndorseAG);
				serviceCodeList.add(SERVICE.CsEndorseCT);
				serviceCodeList.add(SERVICE.CsEndorseXT);
				serviceCodeList.add(SERVICE.CsEndorseEA);
				if (tempSurrenderBO.getServiceCode() != null
						&& serviceCodeList.contains(tempSurrenderBO.getServiceCode())) {
//					csEndorseCTSaveService.saveFundServiceFor928(tempSurrenderBO, conInvestPO.getAccountCode()); // 重庆农商行销售数据提数阻断 ORA-01031 权限不足 保单提数为什么需要更新表待排查
				}
			}

		}

		if (ConstantsBusiProdCode.BUSI_CODE_LIST_928.contains(surrenderBO.getBusiProdCode())) {
			// surrenderBO.setCashValue(cashValue);
			surrenderBO.setInvestCashValue(cashValue);
			cashValue = calcCashValueFor928(surrenderBO);
		}
		surrenderBO.setInvestReissuedInterest(investReissuedInterest);
		surrenderBO.setCashValue(cashValue);
		surrenderBO.setInvestCashValue(cashValue);
		surrenderBO.setUniversallCashValue(cashValue);
		surrenderBO.setPolicyFee(policyFee);
		surrenderBO.setRiskFee(riskFee);
		surrenderBO.setConBonuse(conBonuse);

		return surrenderBO;
	}

	/**
	 * 
	 * @description 查询金额
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param surrenderBO      参数
	 * @param contractInvestPO 参数
	 * @return
	 */
	private BigDecimal calcCashValueFor928(SurrenderBO surrenderBO) {
		Date applyDate = surrenderBO.getApplyTime();
		BigDecimal cashValue = surrenderBO.getInvestCashValue();
		BigDecimal SpecialFlag = surrenderBO.getSpecialFlag();
		BigDecimal busiItemId = surrenderBO.getBusiItemId();
		BigDecimal policyId = surrenderBO.getPolicyId();
		Date validateDate = surrenderBO.getValidateBDay();

		if (validateDate == null) {
			ContractMasterPO contractMasterPO = new ContractMasterPO();
			contractMasterPO.setPolicyId(policyId);
			contractMasterPO = contractMasterDao.findContractMaster(contractMasterPO);
			validateDate = contractMasterPO.getValidateDate();
		}

		if (SpecialFlag != null && SpecialFlag.compareTo(BigDecimal.ONE) == 0) {// @invalid 判断是否特殊退保
			// 1.若在被保险人养老年金开始领取日（不含）前申请解除本合同，公司退还申请解除合同时的保单账户价值。
			// 2.若在被保险人养老年金开始领取日（含）后申请解除本合同，退还应领未领的保证领取养老年金。
			PayPlanPO payPlanPO = new PayPlanPO();
			payPlanPO.setBusiItemId(busiItemId);
			payPlanPO.setLiabId(Constants.LIAB_ID_1106);
			List<PayPlanPO> payPlanPOs = payPlanDao.findAllPayPlan(payPlanPO);
			if (CollectionUtilEx.isNotEmpty(payPlanPOs)) {
				payPlanPO = payPlanPOs.get(0);
			}
			Date beginDate = payPlanPO.getBeginDate();
			if (applyDate.compareTo(beginDate) < 0) {
				return cashValue;
			} else {
				Date payDueDate = payPlanPO.getPayDueDate();// @invalid 下次领取日期
				Date endDate = payPlanPO.getEndDate();// @invalid 领取结束日期
				String periodType = payPlanPO.getGuaranteePeriodType();
				BigDecimal payTotal = payPlanPO.getTotalAmount() == null ? BigDecimal.ZERO : payPlanPO.getTotalAmount();

				if (periodType != null && periodType.equals(Constants.GUARANTEE_PERIOD_TYPE_V)) {// 3.保证返还账户价值终身领取
					FundTransPO fundTransPO = new FundTransPO();
					fundTransPO.setPolicyId(payPlanPO.getPolicyId());
					fundTransPO.setBusiItemId(payPlanPO.getBusiItemId());
					fundTransPO.setTransCode(Constants.TRANS_CODE_21);
					List<FundTransPO> fundTransPOList = fundTransDao.findAllFundTrans(fundTransPO);
					BigDecimal feeAmount = BigDecimal.ZERO;
					for (FundTransPO fundTrans : fundTransPOList) {
						if (fundTrans.getTransAmount() != null) {
							feeAmount = feeAmount.add(fundTrans.getTransAmount());
						}
					}

					cashValue = feeAmount.subtract(payTotal);
					return cashValue;
				} else if (periodType != null && periodType.equals("Y")) {// 4.保证领取某确定年限
					BigDecimal payPeriod = payPlanPO.getGurntPayPeriod();
					endDate = DateUtilsEx.addYear(payDueDate, payPeriod.intValue());
				}

				String planFreq = payPlanPO.getPlanFreq();// @invalid 领取频率
				BigDecimal instalmentAmount = payPlanPO.getInstalmentAmount() == null ? BigDecimal.ZERO
						: payPlanPO.getInstalmentAmount();
				int num = 0;// @invalid 应领未领期数
				if ("1".equals(planFreq)) {
					// @invalid 年领
					num = (int) DateUtilsEx.getYearAmount(payDueDate, endDate);
				} else if ("4".equals(planFreq)) {
					// @invalid 月领
					num = (int) DateUtilsEx.getMonthAmount(payDueDate, endDate);
				}
				cashValue = instalmentAmount.multiply(new BigDecimal(num));
				cashValue = cashValue.subtract(payTotal);
				return cashValue;

			}
		} else {
			// @invalid @invalid 被保险人已办理养老年金申请的，开始领取养老年金后的保险单的现金价值为0。
			PayDuePO payDuePO = new PayDuePO();
			payDuePO.setBusiItemId(busiItemId);
			payDuePO.setLiabId(Constants.LIAB_ID_1106);
			payDuePO.setFeeStatus(Constants.FEE_STATUS_01);
			payDuePO.setPolicyId(policyId);
			int count = payDueDao.findPayDueTotal(payDuePO);
			if (count > 0) {
				return BigDecimal.ZERO;
			}

			double policyYear = (double) DateUtilsEx.getYearAmount(validateDate, applyDate);
			ContractProductPO conProductPO = new ContractProductPO();
			conProductPO.setPolicyId(policyId);
			conProductPO.setBusiItemId(busiItemId);
			// 3.查询险种信息表
			conProductPO = contractProductDao.findAllContractProductByPolicyCodePrem(conProductPO);
			BigDecimal totalPrem = conProductPO.getTotalPremAf();// @invalid 已交保费
			BigDecimal diffAmount = cashValue.subtract(totalPrem);// @invalid 保单账户价值 与已交保费差额

			if (policyYear <= 1) {
				// 3.1第1个保单年度 合同已交保险费的95%
				cashValue = totalPrem.multiply(new BigDecimal(0.95)).setScale(2, RoundingMode.HALF_UP);
			} else if (policyYear <= 2) {
				// 3.2第2个保单年度 合同已交保险费的97%
				cashValue = totalPrem.multiply(new BigDecimal(0.97)).setScale(2, RoundingMode.HALF_UP);
			} else if (policyYear <= 3) {
				// 3.3第3个保单年度 合同已交保险费的99%
				cashValue = totalPrem.multiply(new BigDecimal(0.99)).setScale(2, RoundingMode.HALF_UP);
			} else if (policyYear <= 5) {
				// @invalid 第4个保单年度 合同已交保险费的100%
				// @invalid 第5个保单年度 合同已交保险费的100%
				cashValue = totalPrem;
			} else if (policyYear <= 10) {
				// @invalid 第6-10个保单年度 合同已交保险费与个人账户累计收益部分的75%二者之和
				if (diffAmount.compareTo(BigDecimal.ZERO) > 0) {
					cashValue = totalPrem
							.add(diffAmount.multiply(new BigDecimal(0.75)).setScale(2, RoundingMode.HALF_UP));
				} else {
					cashValue = totalPrem;
				}
			} else {
				// @invalid 第11个保单年度及以后 合同已交保险费与个人账户累计收益部分的90%二者之和
				if (diffAmount.compareTo(BigDecimal.ZERO) > 0) {
					cashValue = totalPrem
							.add(diffAmount.multiply(new BigDecimal(0.9)).setScale(2, RoundingMode.HALF_UP));
				} else {
					cashValue = totalPrem;
				}
			}
		}
		return cashValue;
	}

	/**
	 * 
	 * @description 928计算80岁保单生效对应日
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @param tempSurrenderBO tempSurrenderBO
	 * @return
	 */
	private Date queryCalcEffectDate(SurrenderBO tempSurrenderBO) {

		// 1.查询被保人信息
		CsQueryParamPO csQueryParamPO = new CsQueryParamPO();
		csQueryParamPO.setPolicyId(tempSurrenderBO.getPolicyId());
		csQueryParamPO.setBusiItemId(tempSurrenderBO.getBusiItemId());
		csQueryParamPO.setOrderId(Constants.ORDER_ID__ONE);
		List<CsQueryParamPO> queryList = csEntryDao.queryBenefitListInfo(csQueryParamPO);

		Date applyTime = tempSurrenderBO.getApplyTime();
		Date calcDate = applyTime;
		if (CollectionUtilEx.isNotEmpty(queryList)) {
			ContractMasterPO conMasterPO = new ContractMasterPO();
			conMasterPO.setPolicyId(tempSurrenderBO.getPolicyId());
			conMasterPO = contractMasterDao.findContractMasterByPolicyId(conMasterPO);

			csQueryParamPO = queryList.get(0);
			Date birthday = csQueryParamPO.getCustomerBirthday();// 1.1被保人出生日期
			Date newBirthday = DateUtilsEx.addYear(birthday, 80);
			Date validateDate = conMasterPO.getValidateDate();// 1.2保单生效对应日

			int year = DateUtilsEx.getYear(birthday) + 80;
			int month = DateUtilsEx.getMonth(validateDate);
			int day = DateUtilsEx.getDay(validateDate);

			Calendar calDate = Calendar.getInstance(); // @invalid 80岁保单生效对应日
			calDate.set(Calendar.YEAR, year);
			calDate.set(Calendar.MONTH, month - 1);
			calDate.set(Calendar.DAY_OF_MONTH, day);

			if (newBirthday.compareTo(calDate.getTime()) > 0) {
				calDate.set(Calendar.YEAR, year + 1);
			}

			if (applyTime.compareTo(calDate.getTime()) < 0) {
				// @invalid 被保人80周岁保单生效对应日前
				calcDate = applyTime;
			} else {
				calcDate = calDate.getTime();
			}
		}
		return calcDate;
	}

	/**
	 * 
	 * @description 获取万能账户价值数据 用
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param surrenderBO      参数
	 * @param contractInvestPO 参数
	 * @return
	 */
	public SurrenderBO findInvestAmountInfo(SurrenderBO surrenderBO, ContractInvestPO contractInvestPO) {
		// 1.万能险现价
		surrenderBO.setListId(contractInvestPO.getListId());
		BigDecimal totalPowerPrice = calculateUniversallCashValue(surrenderBO, contractInvestPO);

		surrenderBO.setUniversallCashValue(totalPowerPrice.divide(new BigDecimal(1), 2, RoundingMode.HALF_UP));
		// 2.投连万能扣费
		Map<String, BigDecimal> feeMap = calculateAllInvestAndUniversallSurrenderFee(surrenderBO, contractInvestPO);
		// @invalidkey为1，value为保单管理费 key为2，value为资产管理费 key为3，value为风险保费
		surrenderBO.setPolicyFee(feeMap.get("1"));
		surrenderBO.setRiskFee(feeMap.get("3"));
		// @invalid 保单管理费 */
		if (surrenderBO.getPolicyFee() == null) {
			surrenderBO.setPolicyFee(new BigDecimal(0));
		}
		// @invalid 风险保费 */
		if (surrenderBO.getRiskFee() == null) {
			surrenderBO.setRiskFee(new BigDecimal(0));
		}

		logger.info(
				"CsEndorseCTCalculateServiceImpl===findInvestAmountInfo======policyFee:" + surrenderBO.getPolicyFee());
		logger.info("CsEndorseCTCalculateServiceImpl===findInvestAmountInfo=======riskFee:" + surrenderBO.getRiskFee());

		BigDecimal UniversallCashValue = surrenderBO.getUniversallCashValue();
		if (ConstantsBusiProdCode.BUSI_CODE_LIST_928.contains(surrenderBO.getBusiProdCode())) {
			if (csEndorseCTService.isChargePolicyFeeFor928(surrenderBO, contractInvestPO.getAccountCode(),
					UniversallCashValue)) {
				// 3.保单管理费优先在稳健回报型投资组合账户价值中扣除，如未选择该投资组合或其账户价值余额不足，则在积极进取型投资组合账户价值中扣除。
				UniversallCashValue = UniversallCashValue.subtract(surrenderBO.getPolicyFee())
						.subtract(surrenderBO.getRiskFee());
			}
		} else {
			UniversallCashValue = UniversallCashValue.subtract(surrenderBO.getPolicyFee())
					.subtract(surrenderBO.getRiskFee());
		}
		surrenderBO.setUniversallCashValue(UniversallCashValue);
		return surrenderBO;
	}

	/**
	 * 
	 * @description 查询金额
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param surrenderBO      参数
	 * @param contractInvestPO 参数
	 * @return
	 */
	public BigDecimal calculateUniversallCashValue(SurrenderBO surrenderBO, ContractInvestPO contractInvestPO) {
		BigDecimal cashValue = new BigDecimal(0);
		ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
		contractBusiProdPO.setBusiItemId(surrenderBO.getBusiItemId());
		contractBusiProdPO = contractBusiProdDao.findContractBusiProd(contractBusiProdPO);
		cashValue = calInterest(contractInvestPO, surrenderBO.getApplyTime(), contractBusiProdPO);

		logger.info("CsEndorseCTCalculateServiceImpl-------policyCode" + contractBusiProdPO.getPolicyCode());
		logger.info("CsEndorseCTCalculateServiceImpl-------busiProdCode" + contractBusiProdPO.getBusiProdCode());
		logger.info("CsEndorseCTCalculateServiceImpl-------cashValue" + cashValue);
		// @invalid 2.计算持续奖金
		BigDecimal conBonuse = calContinueBonuse(contractInvestPO, surrenderBO);
		logger.info("CsEndorseCTCalculateServiceImpl-------持续奖金" + conBonuse);
		// @invalid songdf 在surrenderBO存入持续奖金 本息合计
		surrenderBO.setConBonuse(conBonuse);
		surrenderBO.setCashValue(cashValue);
		if (conBonuse != null) {
			cashValue = cashValue.add(conBonuse);
		}
		// 1.把退保时那部分补发结算利息也存起来
		if (null != contractInvestPO.getData() && null != contractInvestPO.getData().get("invest_reissued_interest")) {
			surrenderBO
					.setInvestReissuedInterest((BigDecimal) contractInvestPO.getData().get("invest_reissued_interest"));
			logger.info("CsEndorseCTCalculateServiceImpl-------InvestReissuedInterest"
					+ contractInvestPO.getData().get("invest_reissued_interest"));
		}
		return cashValue;
	}

	/**
	 * 
	 * @description 计算投连万能所有退保费用
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param surrenderBO      参数
	 * @param contractInvestPO 参数 @return|
	 */
	public HashMap<String, BigDecimal> calculateAllInvestAndUniversallSurrenderFee(SurrenderBO surrenderBO,
			ContractInvestPO contractInvestPO) {
		// 1.查询险种扣费类型列表
		List<ILPUFeeType> iLPUFeeTypeList = queryILPUFeeTypesInOrder(surrenderBO);
		HashMap<String, BigDecimal> resValue = new HashMap<String, BigDecimal>();
		// @invalid 建立传入值list
		BigDecimal feeAmount = new BigDecimal(0);
		if (iLPUFeeTypeList != null && iLPUFeeTypeList.size() > 0) {
			for (ILPUFeeType feILPUFeeType : iLPUFeeTypeList) {

				Calendar cal = Calendar.getInstance();
				cal.setTime(surrenderBO.getApplyTime());
				cal.add(Calendar.DAY_OF_YEAR, -1);

				feeAmount = new BigDecimal(0);
				FundTransPO ftPO = new FundTransPO();
				List<String> transCodeList = new ArrayList<String>();
				transCodeList.add(Constants.TRANS_CODE_41);
				ftPO.getData().put("transCodeList", transCodeList);
				ftPO.setListId(contractInvestPO.getListId());// @invalid 万能账户id
				List<FundTransPO> fundTrans = fundTransDao.findAllFundTrans(ftPO);
				BigDecimal lastBalance = null;
				FundSettlementPO fundSettlementPo = new FundSettlementPO();
				fundSettlementPo.setInvestId(contractInvestPO.getListId());
				FundSettlementPO fundSettlement = fundSettlementDao.findFundSettlement(fundSettlementPo);
				// 2.没有风险保费记录，则是计算首期风险保费
				if (CollectionUtils.isEmpty(fundTrans)) {
					if (fundSettlement != null && fundSettlement.getBalance() != null) {
						lastBalance = fundSettlement.getBalance();
					}
				} else {
					// 3.比较申请日当月是否存在结算记录
					if (compareDate(surrenderBO.getApplyTime(), fundSettlement.getSettleDate())) {
						lastBalance = fundSettlement.getLastBalance();
					} else {
						lastBalance = fundSettlement.getBalance();
					}

				}

				MmsCalcAccountFeeResVO mmsCalcAccountFeeResVO = calculateIlpuDueFee.calcFeeAmount(contractInvestPO,
						feILPUFeeType, feILPUFeeType.getLastChargeDate(), cal.getTime(), BigDecimal.ZERO, "CT",
						lastBalance);
				if (mmsCalcAccountFeeResVO != null
						&& CollectionUtils.isNotEmpty(mmsCalcAccountFeeResVO.getAccountFeeInfoResVOList())) {
					feeAmount = mmsCalcAccountFeeResVO.getTotalFeeAmount();
				}
				resValue.put(feILPUFeeType.getChargeType().toString(), feeAmount);
			}
		}
		return resValue;
	}

	/**
	 * 
	 * @description 计算退保前未计算的利息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param contractInvestPO   参数
	 * @param applyDate          参数
	 * @param contractBusiProdPO 参数
	 * @return
	 */
	public BigDecimal calInterest(ContractInvestPO contractInvestPO, Date applyDate,
			ContractBusiProdPO contractBusiProdPO) {
		BigDecimal cashValue = new BigDecimal(0);
		FormulaBO inputDataBO = new FormulaBO();
		inputDataBO.setInvestId(contractInvestPO.getListId());
		inputDataBO.setBusiItemId(contractInvestPO.getBusiItemId());
		Calendar calTemp = Calendar.getInstance();
		FundSettlementPO fundSettlementPO = new FundSettlementPO();
		fundSettlementPO.setInvestId(inputDataBO.getInvestId());
		calTemp.setTime(applyDate);
		calTemp.add(Calendar.DAY_OF_YEAR, 1);
		fundSettlementPO.getData().put("end_deal_time", calTemp.getTime());
		// @invalid 申请提交日亲的最后一次结算日
		fundSettlementPO = formulaDao.findLastSettlementByDate(fundSettlementPO);
		// 1.退保时的保单账户价值
		BigDecimal accountValue = getAccountValue(contractInvestPO, applyDate, fundSettlementPO,
				contractBusiProdPO.getPolicyCode());
		logger.info("CsEndorseCTCalculateServiceImpl-----calInterest退保时账户合计：" + accountValue);

		if (accountValue == null) {
			accountValue = new BigDecimal(0);
		}

		if (!ConstantsBusiProdCode.BUSI_CODE_LIST_928.contains(contractBusiProdPO.getBusiProdCode())) {
			// @invalid**** rm:61333 tc：6953 账户价值<=0不做结息处理 start ******/
			if (accountValue.compareTo(BigDecimal.ZERO) <= 0) {
				return accountValue;
			}
			// @invalid**** rm:61333 tc：6953 账户价值<=0不做结息处理 end ******/
		}

		if (fundSettlementPO.getBalance() != null) {
			cashValue = cashValue.add(fundSettlementPO.getBalance());
		}
		inputDataBO.setStartDate(fundSettlementPO.getSettleDate());
		if (fundSettlementPO.getData().isEmpty()) {
			ContractBusiProdPO contractBusiProdPara = new ContractBusiProdPO();
			contractBusiProdPara.setBusiItemId(inputDataBO.getBusiItemId());
			ContractBusiProdPO contractBusiProdReturn = contractBusiProdDao
					.findContractBusiProdByBusiItemId(contractBusiProdPara);
			Calendar cal = Calendar.getInstance();
			cal.setTime(contractBusiProdReturn.getValidateDate());
			cal.add(Calendar.DAY_OF_YEAR, -1);
			inputDataBO.setStartDate(cal.getTime());
			inputDataBO.setInitialValue(new BigDecimal(0));
		} else {
			Calendar cal = Calendar.getInstance();
			cal.setTime(fundSettlementPO.getSettleDate());
			cal.add(Calendar.DAY_OF_YEAR, 1);
			inputDataBO.setStartDate(cal.getTime());
		}
		// 2.计算利息经过天数为0
		if (inputDataBO.getStartDate().compareTo(applyDate) == 0 && fundSettlementPO.getGurntBalance() != null) {
			if (fundSettlementPO.getGurntBalance().compareTo(fundSettlementPO.getBalance()) == 1) {
				accountValue = accountValue
						.add(fundSettlementPO.getGurntBalance().subtract(fundSettlementPO.getBalance()));
			}
			return accountValue;
		}
		// @invalid 结束时间
		calTemp.setTime(applyDate);
		calTemp.add(Calendar.DAY_OF_YEAR, -1);
		inputDataBO.setEndDate(calTemp.getTime());
		// 3.计算使用的保证利率时间，保证利率,分割利息
		ParamOrResultBO paraBO = new ParamOrResultBO();
		paraBO.setAccountCode(contractInvestPO.getAccountCode());
		paraBO.setBusinessPrdId(contractBusiProdPO.getBusiPrdId());
		paraBO.setEndDate(applyDate);
		paraBO.setValidateDate(contractBusiProdPO.getValidateDate());
		if (fundSettlementPO.getSettleDate() == null) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(contractBusiProdPO.getValidateDate());
			cal.add(Calendar.DAY_OF_YEAR, -1);
			paraBO.setStartDate(cal.getTime());
			fundSettlementPO.setBalanceBa(new BigDecimal(0));
			fundSettlementPO.setGurntBalance(new BigDecimal(0));
		} else {
			calTemp.setTime(fundSettlementPO.getSettleDate());
			calTemp.add(Calendar.DAY_OF_YEAR, 1);
			paraBO.setStartDate(calTemp.getTime());
		}
		ParamOrResultBO[] rateSet = formulaService.getGurIntereRateSet(paraBO);
		List<FormulaBO> formulaBOList = new ArrayList<FormulaBO>();
		FormulaBO tempFormulaBO = null;
		Calendar cal = Calendar.getInstance();

		for (int i = 0; i < rateSet.length; i++) {
			inputDataBO.setInterestRate(rateSet[i].getInterestRate());
			if (!rateSet[i].getEndDate().before(inputDataBO.getStartDate())
					&& !rateSet[i].getEndDate().after(inputDataBO.getEndDate())) {
				tempFormulaBO = BeanUtils.copyProperties(FormulaBO.class, inputDataBO);
				tempFormulaBO.setEndDate(rateSet[i].getEndDate());
				formulaBOList.add(tempFormulaBO);
				cal.setTime(rateSet[i].getEndDate());
				cal.add(Calendar.DAY_OF_YEAR, 1);
				inputDataBO.setStartDate(cal.getTime());
			} else {
				formulaBOList.add(inputDataBO);
			}
		}
		FormulaBO feFormulaBO;
		BigDecimal balancesinvest = new BigDecimal(0);
		BigDecimal gurntinvest = new BigDecimal(0);
		BigDecimal balances = fundSettlementPO.getBalance();// @invalid 期初账户价值及利息
		BigDecimal gurntBalances = fundSettlementPO.getGurntBalance();// @invalid 保证价值及利息
		if (balances == null) {
			balances = new BigDecimal(0);
		}

		if (gurntBalances == null) {
			gurntBalances = new BigDecimal(0);
		}
		BigDecimal balancesinvestSum = new BigDecimal(0);
		BigDecimal gurntinvestSum = new BigDecimal(0);
		for (int indexFormulaBO = 0; indexFormulaBO < formulaBOList.size(); indexFormulaBO++) {
			feFormulaBO = formulaBOList.get(indexFormulaBO);
			feFormulaBO.setInitialValue(cashValue);
			// 4.查询万能保单结算记录表
			// @invalid 利息
			try {
				// 5.提供两种计算万能险现价及利息的方法，然后取值较大的一个
				// @invalid 设置 结息方式 0日单利 1日复利
				if (contractBusiProdPO.getSettleMethod() != null) {
					feFormulaBO.setInterestType(contractBusiProdPO.getSettleMethod().toString());
				} else {
					feFormulaBO.setInterestType("0");
				}
				// 5.1.根据期初账户价值及利率计算价值利息和
				feFormulaBO.setServiceCode(Constants.SERVICE_CODE__CT);
				if (fundSettlementPO != null && fundSettlementPO.getBalanceBa() != null) {
					feFormulaBO.setInitialValue(balances);
					balancesinvest = formulaService.calInterestUniversalSpecial(feFormulaBO);
					balancesinvest = balancesinvest.setScale(2, RoundingMode.HALF_UP);
					balancesinvestSum = balancesinvestSum.add(balancesinvest);
				}
				// 5.2.根据保证价值及利率计算价值利息和
				if (fundSettlementPO != null && fundSettlementPO.getGurntBalance() != null) {
					feFormulaBO.setInitialValue(gurntBalances);
					gurntinvest = formulaService.calInterestUniversalSpecial(feFormulaBO);
					gurntinvest = gurntinvest.setScale(2, RoundingMode.HALF_UP);
					gurntinvestSum = gurntinvestSum.add(gurntinvest);
				}

			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		logger.info("CsEndorseCTCalculateServiceImpl-----balances：" + balances);
		logger.info("CsEndorseCTCalculateServiceImpl-----gurntBalances：" + gurntBalances);
		logger.info("CsEndorseCTCalculateServiceImpl-----balancesinvestSum：" + balancesinvestSum);
		logger.info("CsEndorseCTCalculateServiceImpl-----gurntinvestSum：" + gurntinvestSum);
		if (balances.compareTo(gurntBalances) == 1) { // @invalid 不调整
			accountValue = accountValue.add(balancesinvestSum);
			// @invalid48105需求分析任务-925尊盈优选两全保险（万能型）保全业务系统需求.把退保时那部分补发利息也存起来*/
			contractInvestPO.getData().put("invest_reissued_interest", balancesinvestSum);
		} else {// @invalid 调整账户价值
			accountValue = accountValue.add(gurntinvestSum).add(gurntBalances.subtract(balances));
			// @invalid48105需求分析任务-925尊盈优选两全保险（万能型）保全业务系统需求.把退保时那部分补发利息也存起来*/
			contractInvestPO.getData().put("invest_reissued_interest",
					gurntinvestSum.add(gurntBalances.subtract(balances)));
		}

		logger.info("CsEndorseCTCalculateServiceImpl-----accountValue：" + accountValue);
		return accountValue;
	}

	/**
	 * 
	 * @description 计算持续奖金
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param contractInvestPO 参数
	 * @param surrenderBO      参数
	 * @return
	 */
	public BigDecimal calContinueBonuse(ContractInvestPO contractInvestPO, SurrenderBO surrenderBO) {
		Map<BigDecimal, BigDecimal> calBonuseMap = new HashMap<BigDecimal, BigDecimal>();
		List<ContinueBonusBO> continueBonusParaBOList = new ArrayList<ContinueBonusBO>();
		FundSettlementPO fundSettlementPO = new FundSettlementPO();
		fundSettlementPO.setInvestId(contractInvestPO.getListId());
		// @invalid 上次结算日
		fundSettlementPO = formulaDao.findLastSettlementByDate(fundSettlementPO);
		// @invalid 计算持续奖金
		// 1. 添加持续奖金计划参数
		ContinueBonusBO continueBonusBO = new ContinueBonusBO();
		continueBonusBO.setPolicyId(surrenderBO.getPolicyId());
		continueBonusBO.setBusiItemId(surrenderBO.getBusiItemId());
		continueBonusBO.setInvestId(contractInvestPO.getListId());
		continueBonusBO.setLastSettleDate(fundSettlementPO.getSettleDate());
		continueBonusBO.setSettleDate(surrenderBO.getApplyTime());
		continueBonusBO.setPayDueDate(surrenderBO.getApplyTime());
		continueBonusBO.setPrizeReleasePoint(Constants.PRIZE_RELEASE_POINT__SETTLEMENT); // @invalid 结算时点
		continueBonusParaBOList.add(continueBonusBO);

		try {
			calBonuseMap = continueBonusDService.calBonuse(continueBonusParaBOList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return calBonuseMap.get(contractInvestPO.getListId());
	}

	/**
	 * 
	 * @description 查询险种对应的扣费项目配置列表
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param surrenderBO 参数
	 * @return
	 */
	public List<ILPUFeeType> queryILPUFeeTypesInOrder(SurrenderBO surrenderBO) {
		// 1.调用接口查询扣费项配置列表
		List<ILPUFeeType> feeTypeList = new ArrayList<ILPUFeeType>();
		MmsUniversalFeeQueryReqVO mmsUniversalFeeQueryReqVO = new MmsUniversalFeeQueryReqVO();
		mmsUniversalFeeQueryReqVO.setBusinessPrdId(surrenderBO.getBusiPrdId());// @invalid 产品id
		mmsUniversalFeeQueryReqVO.setChargeLink("1");
		MmsUniversalFeeQueryResVO mmsUniversalFeeQueryResVO = pQueryService
				.prdimmsuniversalfeequeryuccuniversalFeeQuery(mmsUniversalFeeQueryReqVO);
		if (mmsUniversalFeeQueryResVO != null) {
			List<MmsUniversalFeeQueryVO> mmsUniversalFeeQueryVOs = mmsUniversalFeeQueryResVO
					.getMmsUniversalFeeQueryVOList();
			for (MmsUniversalFeeQueryVO mmsUFeeQuery : mmsUniversalFeeQueryVOs) {
				if (Constants.MMS_CHARGETYPE_1.equals(mmsUFeeQuery.getChargeType())
						|| Constants.MMS_CHARGETYPE_2.equals(mmsUFeeQuery.getChargeType())
						|| Constants.MMS_CHARGETYPE_3.equals(mmsUFeeQuery.getChargeType())) {
					feeTypeList.add(BeanUtils.copyProperties(ILPUFeeType.class, mmsUFeeQuery));
				}
			}
		}

		if (!CollectionUtilEx.isEmpty(feeTypeList)) {
			// @invalid 排序
			ILPUFeeType feeType = new ILPUFeeType();
			for (int i = 0; i < feeTypeList.size(); i++) {
				for (int j = 0; j < feeTypeList.size() - i - 1; j++) {
					int num1 = Integer.parseInt(feeTypeList.get(j).getPaymentOrder());
					int num2 = Integer.parseInt(feeTypeList.get(j + 1).getPaymentOrder());
					if (num1 > num2) {
						feeType = feeTypeList.get(j);
						feeTypeList.set(j, feeTypeList.get(j + 1));
						feeTypeList.set(j + 1, feeType);
					}
				}
			}

			// 2.查询所有类型的上次扣费日
			PolicyFundChargePO policyFundChargePO = new PolicyFundChargePO();
			policyFundChargePO.setPolicyId(surrenderBO.getPolicyId());// @invalid 保单id
			policyFundChargePO.setBusiItemId(surrenderBO.getBusiItemId());// @invalid 险种id
			List<PolicyFundChargePO> policyFundChargePOs = ilpuChargeDeductionDao.queryLastDate(policyFundChargePO);
			for (ILPUFeeType ilpuFeeType : feeTypeList) {
				String charCode = ilpuFeeType.getChargeType();// @invalid 扣费类型
				Date lastChargeDate = null;// @invalid 上次扣费日

				for (PolicyFundChargePO pfChargePO : policyFundChargePOs) {
					// @invalid 找到对应费用类型的上次扣费日信息
					if (pfChargePO.getChargeCode().trim().equals(charCode)) {
						lastChargeDate = pfChargePO.getLastChargeDate();
						break;
					}
				}

				if (lastChargeDate == null && surrenderBO.getValidateBDay() != null) {
					lastChargeDate = surrenderBO.getValidateBDay();
					// 3.创建扣费项对应的实际扣费日记录
					policyFundChargePO = new PolicyFundChargePO();
					policyFundChargePO.setPolicyId(surrenderBO.getPolicyId()); // @invalid 保单id
					policyFundChargePO.setBusiItemId(surrenderBO.getBusiItemId());// @invalid 险种id
					policyFundChargePO.setProductId(surrenderBO.getBusiPrdId()); // @invalid 精算产品id
					policyFundChargePO.setChargeCode(charCode); // @invalid 扣费类型
					policyFundChargePO.setLastChargeDate(lastChargeDate);// @invalid 上次扣费日
//					policyFundChargePO = policyFundChargeDao.addPolicyFundCharge(policyFundChargePO); // 重庆农商行销售数据提数阻断 ORA-01031 权限不足 保单提数为什么需要更新表待排查
				} else if (!surrenderBO.getApplyTime().after(lastChargeDate)) {
					// 4.追溯退保：通过交易记录查询上次扣费日
					FundTransPO fundTransPO = new FundTransPO();
					fundTransPO.setPolicyId(surrenderBO.getPolicyId());
					fundTransPO.setListId(surrenderBO.getListId());
					if (Constants.MMS_CHARGETYPE_1.equals(charCode)) {
						fundTransPO.setTransCode(Constants.TRANS_CODE_04);
					} else if (Constants.MMS_CHARGETYPE_2.equals(charCode)) {
						fundTransPO.setTransCode(Constants.TRANS_CODE_02);
					} else if (Constants.MMS_CHARGETYPE_3.equals(charCode)) {
						fundTransPO.setTransCode(Constants.TRANS_CODE_03);
					} else {
						break;
					}
					fundTransPO.getData().put("end_date", surrenderBO.getApplyTime());
					fundTransPO = fundTransDao.findFundTransFeeDate(fundTransPO);
					if (fundTransPO.getDealTime() != null) {
						lastChargeDate = fundTransPO.getDealTime();
					}
				}
				ilpuFeeType.setLastChargeDate(lastChargeDate);
			}
		}
		return feeTypeList;
	}

	/**
	 * 
	 * @description 判断时候在同一个月
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param date1 参数
	 * @param date2 参数
	 * @return
	 */
	public static boolean compareDate(Date date1, Date date2) {
		try {
			Calendar cal1 = Calendar.getInstance();
			cal1.setTime(date1);

			Calendar cal2 = Calendar.getInstance();
			cal2.setTime(date2);

			boolean isSameYear = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
			boolean isSameMonth = isSameYear && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);

			return isSameMonth;
		} catch (Exception e) {
			logger.info("比较日期报错", date1.toString() + date2.toString());
		}
		return false;
	}

	/**
	 * 
	 * @description 计算万能险账户价值
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param contractInvestPO     参数
	 * @param applyDate            参数
	 * @param lastFundSettlementPO 参数
	 * @param policyCode           参数
	 * @return
	 */
	public BigDecimal getAccountValue(ContractInvestPO contractInvestPO, Date applyDate,
			FundSettlementPO lastFundSettlementPO, String policyCode) {
		BigDecimal accountValue = new BigDecimal(0);

		FundTransPO fundTransPO1 = new FundTransPO();
		fundTransPO1.set("account_code", contractInvestPO.getAccountCode());
		fundTransPO1.set("policy_code", policyCode);
		fundTransPO1.set("flag1", "flag1");
		// 1.如果申请提交日在最后一次交易记录日，或者最后一次交易记录日之后，则当前账户价值为，申请提交日账户价值
		fundTransPO1 = fundTransDao.findLastOrFirstFundTransPO(fundTransPO1);
		if (fundTransPO1 != null && fundTransPO1.getDealTime() != null
				&& !fundTransPO1.getDealTime().after(applyDate)) {
			return contractInvestPO.getInterestCapital();
		} else if (lastFundSettlementPO.getBalance() != null) {// @invalid
																// 最后一次交易记录日在申请提交日后，推算申请提交日的账户价值，得到申请日前最后一次结算的钱，然后加减到申请日中间的交易记录，得到申请日账户价值
			accountValue = lastFundSettlementPO.getBalance();
		}

		// 2.合计进入账户交易
		FundTransPO fundTransPO = new FundTransPO();
		fundTransPO.getData().put("start_Date", lastFundSettlementPO.getSettleDate());
		fundTransPO.getData().put("end_Date", applyDate);
		fundTransPO.setListId(contractInvestPO.getListId());
		fundTransPO.setTransType(Constants.INVEST_TRANS_TYPE__IN);
		fundTransPO = formulaDao.findSumTransAmount(fundTransPO);
		if (fundTransPO != null && !fundTransPO.getData().isEmpty()) {
			accountValue = accountValue.add((BigDecimal) fundTransPO.getData().get("sum_trans_amount"));
		}

		fundTransPO = new FundTransPO();
		fundTransPO.getData().put("start_Date", lastFundSettlementPO.getSettleDate());
		fundTransPO.getData().put("end_Date", applyDate);
		fundTransPO.setListId(contractInvestPO.getListId());
		fundTransPO.setTransType(Constants.INVEST_TRANS_TYPE__OUT);
		fundTransPO = formulaDao.findSumTransAmount(fundTransPO);
		if (fundTransPO != null && !fundTransPO.getData().isEmpty()) {
			accountValue = accountValue.subtract((BigDecimal) fundTransPO.getData().get("sum_trans_amount"));
		}
		return accountValue;
	}

	/**
	 * 
	 * @description 计算退保手续费 用
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param surrenderBO 参数
	 * @return
	 */
	public BigDecimal calculateInvestSurrenderFee(SurrenderBO surrenderBO) {
		List<HashMap<String, Object>> keyValueList = new ArrayList<HashMap<String, Object>>();
		HashMap<String, Object> map = new HashMap<String, Object>();
		map.put("ProductId", surrenderBO.getProductId());// @invalid 责任组id
		int policyMonth = 0;
		CsContractMasterBO contractMasterBO = new CsContractMasterBO();
		contractMasterBO.setPolicyId(surrenderBO.getPolicyId());
		contractMasterBO.setChangeId(surrenderBO.getChangeId());
		contractMasterBO.setPolicyChgId(surrenderBO.getPolicyChgId());
		contractMasterBO.setOldNew(Constants.OLD);
		List<CsContractMasterBO> contractMasterBOs = csGeneralInfoService.findCsContractMasters(contractMasterBO);
		if (contractMasterBOs.size() > 0) {
			policyMonth = (int) DateUtilsEx.getMonthAmount(contractMasterBOs.get(0).getValidateDate(),
					surrenderBO.getApplyTime());
			String policyInsuranceDate = DateUtilsEx.formatToString(contractMasterBOs.get(0).getApplyDate(),
					"yyyy-MM-dd");
			map.put("PolicyInsuranceDate", policyInsuranceDate);
		}
		// 1.查询受理生效日期
		map.put("PolicyMonth", policyMonth); // @invalid 保单月度
		map.put("AccountValue", surrenderBO.getUniversallCashValue());// @invalid 账户价值

		keyValueList.add(map);
		// 2.先查询一下是否需要扣费 如果不扣费则不需要调
		MmsAccountFeeInfoReqVO mmsAccountFeeInfoReqVO = new MmsAccountFeeInfoReqVO();
		mmsAccountFeeInfoReqVO.setChargeType(Constants.FOURInt);
		mmsAccountFeeInfoReqVO.setProductCodeSys(surrenderBO.getBusiProdCode());// @invalid 产品编码
		mmsAccountFeeInfoReqVO.setBusinessPrdId(surrenderBO.getBusiPrdId());// @invalid 产品id
		mmsAccountFeeInfoReqVO.setKeyValueList(keyValueList);

		List<MmsAccountFeeInfoReqVO> mmsAccountFeeInfoReqVOs = new ArrayList<MmsAccountFeeInfoReqVO>();
		mmsAccountFeeInfoReqVOs.add(mmsAccountFeeInfoReqVO);
		MmsCalcAccountFeeReqVO mmsCalcAccountFeeReqVO = new MmsCalcAccountFeeReqVO();
		mmsCalcAccountFeeReqVO.setAccountFeeInfoReqVOList(mmsAccountFeeInfoReqVOs);
		// 3.计算投连万能扣费金额
		logger.info("——————————计算投连万能退保手续费--参数：" + surrenderBO.getPolicyId()
				+ XmlHelper.classToXml(mmsCalcAccountFeeReqVO));
		MmsCalcAccountFeeResVO mmsCalcAccountFeeResVO = pQueryService
				.prdimmscalcaccountfeeucccalcAccountFee(mmsCalcAccountFeeReqVO);
		logger.info("——————————计算投连万能退保手续费--返回参数：" + surrenderBO.getPolicyId()
				+ XmlHelper.classToXml(mmsCalcAccountFeeResVO));
		if (mmsCalcAccountFeeResVO != null && mmsCalcAccountFeeResVO.getAccountFeeInfoResVOList() != null) {
			String fee = mmsCalcAccountFeeResVO.getAccountFeeInfoResVOList().get(0).getFees().get(0).get("Value");
			/****** rm:61333 tc：6953 如果账户价值为负数时处理start ******/
			BigDecimal feeAmount = new BigDecimal(fee);
			if (feeAmount.compareTo(BigDecimal.ZERO) < 0) {
				return feeAmount.abs();
			}
			// @invalid****rm:61333 tc：6953 如果账户价值为负数时处理end******/
			// @invalid需求分析任务 #61333:bug7269正常的万能险退保start*/
			else {
				return feeAmount;
			}
			// @invalid需求分析任务 #61333:bug7269正常的万能险退保end*/
		}
		return BigDecimal.valueOf(0);
	}

}
