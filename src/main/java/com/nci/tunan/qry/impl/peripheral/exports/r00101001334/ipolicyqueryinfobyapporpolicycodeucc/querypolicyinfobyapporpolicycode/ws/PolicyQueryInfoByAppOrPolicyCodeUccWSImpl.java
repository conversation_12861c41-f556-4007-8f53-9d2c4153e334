package com.nci.tunan.qry.impl.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.ws;
import com.nci.udmp.component.aop.TablePrevThreadLocal;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExA;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.component.serviceinvoke.util.CommonDealManagement;
import com.nci.udmp.component.dealmanagement.constant.DealTrigger;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;

import javax.jws.WebService;
import javax.xml.ws.Holder;

import org.slf4j.Logger;

import java.util.Map;

import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.SrvReqBody;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.SrvResBizBody;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.SrvResBody;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.ws.IPolicyQueryInfoByAppOrPolicyCodeUccWS;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.vo.InputData;
import com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.vo.OutputData;
import com.nci.tunan.qry.impl.peripheral.ucc.r00101001334.IPolicyQueryInfoByAppOrPolicyCodeUcc;

@WebService(endpointInterface = "com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.ws.IPolicyQueryInfoByAppOrPolicyCodeUccWS", serviceName = "PolicyQueryInfoByAppOrPolicyCodeUccWSImplqueryPolicyInfoByAppOrPolicyCode")
public class PolicyQueryInfoByAppOrPolicyCodeUccWSImpl implements IPolicyQueryInfoByAppOrPolicyCodeUccWS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    
    private IPolicyQueryInfoByAppOrPolicyCodeUcc ucc = null;
    
    public IPolicyQueryInfoByAppOrPolicyCodeUcc getUcc() {
        return ucc;
    }
    public void setUcc(IPolicyQueryInfoByAppOrPolicyCodeUcc ucc) {
        this.ucc = ucc;
    }
    
    @Override
    public void queryPolicyInfoByAppOrPolicyCode(SysHeader parametersReqHeader, SrvReqBody parametersReqBody, 
            Holder<SysHeader> parametersResHeader, Holder<SrvResBody> parametersResBody) {
    	String formerPrev = TablePrevThreadLocal.getTABLEPREV();
        TablePrevThreadLocal.setTABLEPREV("APP___PAS__DBUSER.");
        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        Long startTimes = DateUtilsEx.getTodayDate().getTime();
        String systemName = StringUtilsEx.getStr("com.nci.tunan.qry.impl.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.ws", 4, ".");
        String dealNo = systemName + "_" + "IPolicyQueryInfoByAppOrPolicyCodeUcc" + "_" + "queryPolicyInfoByAppOrPolicyCode";
        logger.info("P00001001335接口开始执行，交易流水"+parametersReqHeader.getMsgId());
        if (dealSwitch) {
            logger.debug("开始记录交易请求日志");
            /*CommonDealManagement.beforeCommonDealManagement(parametersReqHeader, parametersReqBody.getBizHeader(),
                parametersReqBody.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime);*/
        }
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
                CommonHeaderDeal.setBIZHEADERTHREAD_EXB(parametersReqBody.getBizHeader());
                InputData inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        String dealStatus = "2";
        try {
            OutputData output = ucc.queryPolicyInfoByAppOrPolicyCode(inputVO);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
             BizHeaderExB  bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
            SrvResBizBody bizResBody = new SrvResBizBody();
            bizResBody.setOutputData(output);
            srvResBody.setBizBody(bizResBody);
            srvResBody.setBizHeader(bizHeader);
            parametersResHeader.value = sysHeader;
            parametersResBody.value = srvResBody;
        } catch (Exception e2) {
            dealStatus = "3";
        	logger.error("调用接口过程中产生异常!",e2);
        } finally{
        	TablePrevThreadLocal.setTABLEPREV(formerPrev);
            if (dealSwitch) {
            	logger.debug("开始记录交易响应日志");
                /*CommonDealManagement.afterCommonDealManagement(sysHeader,
                        srvResBody.getBizHeader(), srvResBody.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime,dealStatus);*/
            }    
        }
        try {
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody.value.getBizBody()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
        } 
        logger.info("P00001001335接口结束执行，交易流水"+parametersReqHeader.getMsgId()+"执行时长："+(DateUtilsEx.getTodayDate().getTime()-startTimes));
    }
}
 

