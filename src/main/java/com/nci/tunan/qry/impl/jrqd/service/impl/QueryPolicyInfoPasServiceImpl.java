package com.nci.tunan.qry.impl.jrqd.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.qry.impl.jrqd.common.Constants;
import com.nci.tunan.qry.impl.jrqd.dao.cap.ICapQueryPayedPremDao;
import com.nci.tunan.qry.impl.jrqd.dao.cs.ICsPolicyChangeDao;
import com.nci.tunan.qry.impl.jrqd.dao.cs.ISurrenderDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IAddressDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IBonusAllocateDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractBeneDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractBusiProdDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractInvestDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractMasterDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractMasterLogDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IContractProductDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.ICustomerDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IInsuredListDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IInvestUnitPriceDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPayDueDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPayPlanDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPolicyAccountDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPolicyAcknowledgementDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPolicyHolderDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IPremDao;
import com.nci.tunan.qry.impl.jrqd.dao.pa.IQueryPolicyInfoDao;
import com.nci.tunan.qry.impl.jrqd.service.IQueryPolicyInfoPasService;
import com.nci.tunan.qry.impl.jrqd.service.pa.IQueryCashValueListService;
import com.nci.tunan.qry.impl.jrqd.util.BatchDateUtil;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.PolicyQueryDelayedPasBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.QueryPolicyInfoInputBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.QueryPolicyInfoOutputBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.pa.QueryCashValueInfoBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.pa.QueryCashValueListReqBO;
import com.nci.tunan.qry.interfaces.model.jrqd.bo.pa.QueryCashValueListResBO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.cap.CapPayedPremPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.cs.CsPolicyChangePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.cs.SurrenderPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.AddressPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.BonusAllocatePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractBenePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractInvestPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractMasterLogPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractMasterPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.ContractProductPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.CustomerPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.InsuredListPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.InvestUnitPricePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PayDuePO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PayPlanPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PolicyAccountPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PolicyAcknowledgementPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PolicyHolderPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PremArapPO;
import com.nci.tunan.qry.interfaces.model.jrqd.po.pa.PremPO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;

/**
 * @description 非实时保单状态查询-保单库Service接口实现类
 * @<NAME_EMAIL>
 * @date Mar 28, 2022 3:34:58 PM
 * @.belongToModule
 */
public class QueryPolicyInfoPasServiceImpl implements IQueryPolicyInfoPasService {

	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * @Fields queryCashValueListService : 查询保单下的责任组现价Service接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_QueryCashValueListService")
	private IQueryCashValueListService queryCashValueListService;

	/**
	 * @Fields queryPolicyInfoDao : 非实时保单状态查询Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_QueryPolicyInfoDao")
	private IQueryPolicyInfoDao queryPolicyInfoDao;

	/**
	 * @Fields contractMasterDao : 保单主表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractMasterDao")
	private IContractMasterDao contractMasterDao;

	/**
	 * @Fields contractMasterLogDao : 保单主表log表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractMasterLogDao")
	private IContractMasterLogDao contractMasterLogDao;

	/**
	 * @Fields contractProductDao : 责任组Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractProductDao")
	private IContractProductDao contractProductDao;

	/**
	 * @Fields policyAcknowledgementDao : 回执信息Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PolicyAcknowledgementDao")
	private IPolicyAcknowledgementDao policyAcknowledgementDao;

	/**
	 * @Fields bonusAllocateDao : 分红信息Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_BonusAllocateDao")
	private IBonusAllocateDao bonusAllocateDao;

	/**
	 * @Fields surrenderDao : 退保记录Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_CS_SurrenderDao")
	private ISurrenderDao surrenderDao;

	/**
	 * @Fields contractBusiProdDao : 保单险种信息Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractBusiProdDao")
	private IContractBusiProdDao contractBusiProdDao;

	/**
	 * @Fields policyAccountDao : 保单账户Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PolicyAccountDao")
	private IPolicyAccountDao policyAccountDao;

	/**
	 * @Fields payDueDao : 生存给付信息Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PayDueDao")
	private IPayDueDao payDueDao;

	/**
	 * @Fields contractInvestDao : 投连万能账户Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractInvestDao")
	private IContractInvestDao contractInvestDao;

	/**
	 * @Fields payPlanDao : 给付计划Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PayPlanDao")
	private IPayPlanDao payPlanDao;

	/**
	 * @Fields investUnitPriceDao : 投资单位价格Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_InvestUnitPriceDao")
	private IInvestUnitPriceDao investUnitPriceDao;

	/**
	 * @Fields premDao : 保单费用记录表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PremDao")
	private IPremDao premDao;

	/**
	 * @Fields policyHolderDao : 投保人Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_PolicyHolderDao")
	private IPolicyHolderDao policyHolderDao;

	/**
	 * @Fields insuredListDao : 被保人Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_InsuredListDao")
	private IInsuredListDao insuredListDao;

	/**
	 * @Fields contractBeneDao : 受益人Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_ContractBeneDao")
	private IContractBeneDao contractBeneDao;

	/**
	 * @Fields customerDao : 客户Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_CustomerDao")
	private ICustomerDao customerDao;

	/**
	 * @Fields addressDao : 地址Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_PA_AddressDao")
	private IAddressDao addressDao;

	/**
	 * @Fields capQueryPayedPremDao : 查询保险缴纳金额Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_CAP_CapQueryPayedPremDao")
	private ICapQueryPayedPremDao capQueryPayedPremDao;

	/**
	 * @Fields csPolicyChangeDao : 保单变更履历表Dao接口
	 */
	@Autowired
	@Qualifier("JRQD_CS_CsPolicyChangeDao")
	private ICsPolicyChangeDao csPolicyChangeDao;

	/**
	 * @description 非实时保单状态查询-保单库查询方法
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.qry.impl.jrqd.service.IQueryPolicyInfoPasService#queryPolicyInfo(com.nci.tunan.qry.interfaces.model.jrqd.bo.QueryPolicyInfoInputBO,
	 *      com.nci.tunan.qry.interfaces.model.jrqd.bo.QueryPolicyInfoOutputBO)
	 * @param queryPolicyInfoInputBO
	 * @param outputBo
	 * @return
	 */
	@Override
	public QueryPolicyInfoOutputBO queryPolicyInfo(QueryPolicyInfoInputBO queryPolicyInfoInputBO,
			QueryPolicyInfoOutputBO outputBo) {
		// 总记录数
		Integer totalCount = 0;
		// 总金额
		BigDecimal totalAmount = new BigDecimal(0);
		// 犹豫期退保总记录数
		Integer hesitateCTCount = 0;
		// 犹豫期退保金额
		BigDecimal totalHesitateCTAmount = new BigDecimal(0);
		// 退保总记录数
		Integer CTCount = 0;
		// 退保总金额
		BigDecimal totalCTAmount = new BigDecimal(0);
		// 满期总记录数
		Integer maturityCount = 0;
		// 满期总金额
		BigDecimal totalMaturityAmount = new BigDecimal(0);
		// 返回集合初始化
		List<PolicyQueryDelayedPasBO> policyQueryDelayedPasBOList = new ArrayList<PolicyQueryDelayedPasBO>();

		// 历史数据全量回传标志
		boolean allDataInHistoryFlag = Integer.valueOf(1).equals(queryPolicyInfoInputBO.getAllDataInHistoryFlag());

		// 查询符合条件的非实时保单信息
		ContractMasterPO contractMasterPO = new ContractMasterPO();
		if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getPolicyCodeList()) && !allDataInHistoryFlag) {
			contractMasterPO.setList("policy_code_list",
					new ArrayList<String>(Arrays.asList(queryPolicyInfoInputBO.getPolicyCodeList().split(","))));
		}
		if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getApplyCodeList()) && !allDataInHistoryFlag) {
			contractMasterPO.setList("apply_code_list",
					new ArrayList<String>(Arrays.asList(queryPolicyInfoInputBO.getApplyCodeList().split(","))));
		}
		// 销售渠道
		if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getBankSaleChannelList())) {
			contractMasterPO.setList("bank_sale_channel_list",
					new ArrayList<String>(Arrays.asList(queryPolicyInfoInputBO.getBankSaleChannelList().split(","))));
		}
		// 递交渠道（默认为银保通）
		if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getSubmitChannelList())) {
			contractMasterPO.setList("submit_channel_list",
					new ArrayList<String>(Arrays.asList(queryPolicyInfoInputBO.getSubmitChannelList().split(","))));
		} else {
			contractMasterPO.setSubmitChannel(new BigDecimal(1));
		}
		/** 同契约，固定业务员销售渠道为银代 */
		contractMasterPO.setChannelType("03");// 银行代理

		if (queryPolicyInfoInputBO.getDateFlag() == null) {
			// 默认时间区间为按生效日查询
			contractMasterPO.getData().put("date_flag", 1);
		} else {
			contractMasterPO.getData().put("date_flag", queryPolicyInfoInputBO.getDateFlag().toString());
		}
		if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getBusiProdCodeList())) {
			contractMasterPO.setList("busi_prod_code_list",
					new ArrayList<String>(Arrays.asList(queryPolicyInfoInputBO.getBusiProdCodeList().split(","))));
		}

		if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getServiceBank())) {
			contractMasterPO.setServiceBank(queryPolicyInfoInputBO.getServiceBank()); // 银行码
		}
		if (queryPolicyInfoInputBO.getBatchDate() != null && !allDataInHistoryFlag) {
			contractMasterPO.set("batch_date", queryPolicyInfoInputBO.getBatchDate());// 查询时间
		}
		if (queryPolicyInfoInputBO.getBatchStartDate() != null && !allDataInHistoryFlag) {
			contractMasterPO.set("batch_start_date", queryPolicyInfoInputBO.getBatchStartDate());// 查询开始时间
		}
		if (queryPolicyInfoInputBO.getBatchEndDate() != null && !allDataInHistoryFlag) {
			contractMasterPO.set("batch_end_date", queryPolicyInfoInputBO.getBatchEndDate());// 查询结束时间
		}
		// 方便测试，截止时间超过昨天也有效
//		if (allDataInHistoryFlag) {
//			contractMasterPO.set("batch_end_date",
//					DateUtilsEx.addDay(DateUtilsEx.formatDate(new Date(), "yyyy-MM-dd"), -1));// 查询结束时间
//		}

		List<ContractMasterPO> contractMasterList = new ArrayList<ContractMasterPO>();
		// 判断业务类型是否为空，若类型不为空,则根据业务类型查询保单 PREF-续期
		ArrayList<String> queryTypeList = new ArrayList<String>();
		if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getQueryTypeList())) {
			queryTypeList = new ArrayList<String>(Arrays.asList(queryPolicyInfoInputBO.getQueryTypeList().split(",")));
		}
		if (CollectionUtilEx.isNotEmpty(queryTypeList)) {
			List<String> serviceCodes = new ArrayList<String>();
			// 退保(犹豫期内【犹豫期内部分、全部退保】、犹豫期外【犹豫期外部分、全部退保】)
			if (queryTypeList.contains("02") || queryTypeList.contains("03") || queryTypeList.contains("021")
					|| queryTypeList.contains("022") || queryTypeList.contains("031")
					|| queryTypeList.contains("032")) {
				serviceCodes.add("XT");// 协议退保
				serviceCodes.add("IT");// 投连险退保
				serviceCodes.add("CT");// 退保
				serviceCodes.add("EA");// 公司解约
			}
			// 满期给付
			if (queryTypeList.contains("05")) {
				serviceCodes.add("AG");
			}
			// 加保
			if (queryTypeList.contains("06") || queryTypeList.contains("061") || queryTypeList.contains("062")) {
				serviceCodes.add("PA");
			}
			// 减保
			if (queryTypeList.contains("07") || queryTypeList.contains("071") || queryTypeList.contains("072")) {
				serviceCodes.add("PT");
			}
			// 理赔终止
			if (queryTypeList.contains("08")) {
				serviceCodes.add("CLMEND");
			}
			// 保单失效
			if (queryTypeList.contains("09")) {
				serviceCodes.add("PNL");// 普通失效
				serviceCodes.add("PILPI");// 投连万能失效
			}
			// 保单贷款
			if (queryTypeList.contains("11") || queryTypeList.contains("14")) {
				serviceCodes.add("LN");// 保单贷款
				serviceCodes.add("RF");// 保单清偿
			}
			// 保单质押
			if (queryTypeList.contains("12") || queryTypeList.contains("14")) {
				serviceCodes.add("CS");// 保单质押第三方止付
				serviceCodes.add("CP");// 保单质押第三方解付
			}
			// 保全（当天做过涉及保费变化的保全项目）
			if (queryTypeList.contains("16")) {
				if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
					// 重庆农商行销售数据回传，涉及保费变化只提复效与退保的保全回退 #114106
					serviceCodes.add("RE");// 保单复效
					serviceCodes.add("SR");// 特殊复效
					serviceCodes.add("RB");// 保全回退
				} else {
					serviceCodes.add("PA");// 加保
					serviceCodes.add("AM");// 追加保费
					serviceCodes.add("FM");// 交费方式及期限变更
//	                serviceCodes.add("JD");// 普通失效
					serviceCodes.add("NS");// 新增附加险
					serviceCodes.add("PT");// 减保（只减险种保额）
					serviceCodes.add("RA");// 保单复缴
					serviceCodes.add("RE");// 保单复效
					serviceCodes.add("RG");// 生存保险金追回
					serviceCodes.add("XD");// 新增可选责任
					serviceCodes.add("CM");// 客户重要资料变更
					serviceCodes.add("HI");// 增补告知
					serviceCodes.add("RB");// 保全回退
					serviceCodes.add("RL");// 保单贷款续贷
					serviceCodes.add("SR");// 特殊复效
				}
			}

			// 追加保费
			if (queryTypeList.contains("20")) {
				serviceCodes.add("AM");// 追加保费
			}

			// 部分领取(减保)
			if (queryTypeList.contains("21")) {
				serviceCodes.add("PG");// 投连万能部分领取
				serviceCodes.add("PT");// 减保
				serviceCodes.add("AI");// 累积生息账户领取注销
			}

			if (serviceCodes.size() > 0) {
				contractMasterPO.set("service_codes", serviceCodes);
				logger.info("【非实时保单状态查询-保单库】查询保全场景入参：" + XmlHelper.classToXml(contractMasterPO));
				contractMasterList = queryContractMasterList(contractMasterPO, "findDelayedPolicyByType");
				if (CollectionUtilEx.isNotEmpty(contractMasterList)) {
					logger.info("【非实时保单状态查询-保单库】查询保全场景条数：" + contractMasterList.size());
				}
				contractMasterPO.set("service_codes", null);
			}

			// 满期账户状态【针对建行增量提取AG、PG、AI】【先查询增量是否有业务数据，并且为满期终止；否则查询满期终止的数据，判断对应的生命周期状态】
			if (queryTypeList.contains("23")) {
				serviceCodes = new ArrayList<String>();
				serviceCodes.add("AG");
				serviceCodes.add("PG");
				serviceCodes.add("AI");
				contractMasterPO.set("service_codes", serviceCodes);
				contractMasterPO.setLiabilityState(Constants.LIABILITY_STATE_END);
				contractMasterPO.setEndCause(Constants.END_CAUSE_MATURITY);
				logger.info("【非实时保单状态查询-保单库】查询生命周期状态场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterLife = queryContractMasterList(contractMasterPO, "findDelayedPolicyByType");
				if (CollectionUtilEx.isNotEmpty(contractMasterLife)) {
					logger.info("【非实时保单状态查询-保单库】查询生命周期场景条数：" + contractMasterLife.size());
					contractMasterList.addAll(contractMasterLife);
				}
				serviceCodes = new ArrayList<String>();
				serviceCodes.add("PME");
				contractMasterPO.set("service_codes", serviceCodes);

				// 考虑到满期终止和账户领取相关的保全项，有可能是同一天，故对查出来的数据不再查询满期终止的数据
				List<String> noPolicyCodes = new ArrayList<String>();
				if (CollectionUtilEx.isNotEmpty(contractMasterLife)) {
					for (ContractMasterPO contractMasterPO2 : contractMasterLife) {
						noPolicyCodes.add(contractMasterPO2.getPolicyCode());
					}
				}
				contractMasterPO.set("no_policyCodes", noPolicyCodes);
				logger.info("【非实时保单状态查询-保单库】查询生命周期状态场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterLife1 = queryContractMasterList(contractMasterPO, "findDelayedPolicyByType");
				if (CollectionUtilEx.isNotEmpty(contractMasterLife1)) {
					logger.info("【非实时保单状态查询-保单库】查询生命周期1场景条数：" + contractMasterLife1.size());
					contractMasterList.addAll(contractMasterLife1);
				}
				contractMasterPO.set("service_codes", null);
				contractMasterPO.setLiabilityState(null);
				contractMasterPO.setEndCause(null);
			}

			// 续期缴费
			if (queryTypeList.contains("04")) {
				contractMasterPO.set("service_code", "PREF");
				logger.info("【非实时保单状态查询-保单库】查询续期缴费场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPolicyByServiceCode");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询续期缴费场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
				contractMasterPO.set("service_code", null);
			}
			// 其他原因导致的保单终止(除退保、满期外的终止)
			if (queryTypeList.contains("19")) {
				contractMasterPO.set("query_type", "19");
				contractMasterPO.setLiabilityState(Constants.LIABILITY_STATE_END);
				contractMasterPO.setExpiryDate(new Date());
				logger.info("【非实时保单状态查询-保单库】查询其他原因导致的保单终止场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPolicyByDate");
				for (ContractMasterPO masterPO : contractMasterPOList) {
					if (!Constants.END_CAUSE_MATURITY.equals(masterPO.getEndCause())
							&& !Constants.END_CAUSE_CANCEL.equals(masterPO.getEndCause())) {
						contractMasterList.add(masterPO);
					}
				}
				contractMasterPO.setLiabilityState(null);
				contractMasterPO.set("query_type", null);
				contractMasterPO.setExpiryDate(null);
			}
			// 保单终止
			if (queryTypeList.contains("13")) {
				contractMasterPO.set("liability_state_end", Constants.LIABILITY_STATE_END);
				logger.info("【非实时保单状态查询-保单库】查询终止场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPolicyByType");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询终止场景条数：" + contractMasterPOList.size());
					// 若查询类型包含退保，则类型13-终止 不统计退保数据，以免重复
					for (ContractMasterPO contractMaster : contractMasterPOList) {
						if (queryTypeList.contains("02") || queryTypeList.contains("03")
								|| queryTypeList.contains("021") || queryTypeList.contains("022")
								|| queryTypeList.contains("031") || queryTypeList.contains("032")) {
							if (!Constants.END_CAUSE_CANCEL.equals(contractMaster.getEndCause())
									&& !Constants.END_CAUSE_CANCEL_ORDER.equals(contractMaster.getEndCause())) {
								contractMasterList.add(contractMaster);
							}
						} else {
							contractMasterList.add(contractMaster);
						}
					}
				}
				contractMasterPO.setLiabilityState(null);
			}
			// 保单中止（失效状态）
			if (queryTypeList.contains("15")) {
				contractMasterPO.set("liability_state_end", null);
				contractMasterPO.set("liability_state_ineffect", Constants.LIABILITY_STATE_INEFFECT);
				List<String> list = new ArrayList<String>();
				list.add(Constants.LAPSE_CAUSE_NORMAL);// 正常失效
				list.add(Constants.LAPSE_CAUSE_6);// 贷款中止
				contractMasterPO.set("lapse_cause_list", list);
				logger.info("【非实时保单状态查询-保单库】查询保单中止（失效状态）场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPolicyByType");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询保单中止（失效状态）场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
				contractMasterPO.set("liability_state_ineffect", null);
				contractMasterPO.set("lapse_cause_list", null);
			}
			// 保单生效（当日承保的数据）
			if (queryTypeList.contains("01")) {
				contractMasterPO.setLiabilityState(Constants.LIABILITY_STATE__INFORCE);
				contractMasterPO.set("query_type", "01");
				logger.info("【非实时保单状态查询-保单库】查询保单生效场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPolicyByDate");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询保单生效场景场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
				contractMasterPO.setLiabilityState(null);
				contractMasterPO.set("query_type", null);
			}
			// 过犹豫期生效
			if (queryTypeList.contains("17")) {
				contractMasterPO.setLiabilityState(Constants.LIABILITY_STATE__INFORCE);
				logger.info("【非实时保单状态查询-保单库】查询过犹豫期生效场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPolicyByHesitationDate");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询过犹豫期生效场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
				contractMasterPO.setLiabilityState(null);
			}

			// 缴费信息
			if (queryTypeList.contains("22")) {
				logger.info("【非实时保单状态查询-保单库】查询缴费信息场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPayInfo");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询缴费信息场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
			}
			/**
			 * 新增代码查询，新增码值 24,25
			 */
			if (queryTypeList.contains("24")) {
				logger.info("【非实时保单状态查询-保单库】查询保单状态信息场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<String> list = contractMasterPO.getPolicyCodeList();
				if (CollectionUtils.isNotEmpty(list)) {
					for (String str : list) {
						if (StringUtils.isBlank(str)) {
							contractMasterPO.getData().put("policy_code_list", null);
						}
					}
				}
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPolicyState");

				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询保单状态信息场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
			}
			if (queryTypeList.contains("25")) {
				logger.info("【非实时保单状态查询-保单库】查询万能险账户变动信息场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<String> list = contractMasterPO.getPolicyCodeList();
				List<String> listsub = (contractMasterPO.getSubmitChannelList());
				if (CollectionUtils.isNotEmpty(list)) {
					for (String str : list) {
						if (StringUtils.isBlank(str)) {
							contractMasterPO.getData().put("policy_code_list", null);
						}
					}
				}
				if (CollectionUtils.isNotEmpty(listsub)) {
					if (StringUtils.isBlank(listsub.get(0))) {
						contractMasterPO.getData().put("submit_channel_list", null);
					}

				}
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedFundTransChange");

				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询万能险账户变动信息场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
			}

			// 26-开门红撤单
			if (queryTypeList.contains("26")) {
				logger.info("【非实时保单状态查询-保单库】查询开门红撤单场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedCancellation");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询开门红撤单场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
			}

			// 27-犹豫期
			if (queryTypeList.contains("27")) {
				BigDecimal hesitationPeriodDay = null;// 初始化犹豫期天数
				if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
					// 重庆农商行犹豫期为15天 #114106
					hesitationPeriodDay = new BigDecimal(15l);
				} else {
					hesitationPeriodDay = new BigDecimal(10l);
				}
				contractMasterPO.setLiabilityState(Constants.LIABILITY_STATE__INFORCE);
				contractMasterPO.setBigDecimal("hesitation_period_day", hesitationPeriodDay);
				logger.info("【非实时保单状态查询-保单库】查询犹豫期场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDelayedPolicyBeforeHesitationDate");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询犹豫期场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
				contractMasterPO.setLiabilityState(null);
				contractMasterPO.setBigDecimal("hesitation_period_day", null);
			}

			// #125834 不再返回当日撤单状态对应的核心业务场景数据
			// 28-当日撤单
			/*if (queryTypeList.contains("28")) {
				logger.info("【非实时保单状态查询-保单库】查询当日撤单场景入参：" + XmlHelper.classToXml(contractMasterPO));
				List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO, "findDrcd");
				if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
					logger.info("【非实时保单状态查询-保单库】查询当日撤单场景条数：" + contractMasterPOList.size());
					contractMasterList.addAll(contractMasterPOList);
				}
			}*/

			/**
			 * 新增代码结束
			 */

			// 手工单承保（入参中递交渠道需要区分手工单或银保通出单，18与01冲突）
//            if (queryTypeList.contains("18")) {
//            	contractMasterPO.set("query_type", "18");
//            	List<String> list = new ArrayList<String>();
//            	list.add("3");// BPO
//            	list.add("4");// 公司录入
//            	contractMasterPO.set("submit_channel_list", list);
//                List<ContractMasterPO> contractMasterPOList = policyQueryDelayedDao
//                        .findDelayedPolicyByDate(contractMasterPO);
//                if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
//                    contractMasterList.addAll(contractMasterPOList);
//                }
//
//            }
			// 拒保
//            if (queryTypeList.contains("10")
//                    && queryPolicyInfoInputBO.getServiceBank() != null) {
//                // 调用核保接口查询当日拒保的投保单信息
//                InputDataBO inputDataBO = new InputDataBO();
//                inputDataBO.setBankCode(queryPolicyInfoInputBO.getServiceBank());
////                inputDataBO.setStartDate(queryPolicyInfoInputBO.getBatchStartDate());
////                inputDataBO.setEndDate(queryPolicyInfoInputBO.getBatchEndDate());
//                logger.info("【非实时保单状态查询-保单库】调用核保接口查询拒保信息入参：" + XmlHelper.classToXml(inputDataBO));
//                OutputDataBO qutputDataBO = uWService.uwiuwqueryrefusedpolicyuccqueryRefusedPolicy(inputDataBO);
//                logger.info("【非实时保单状态查询-保单库】调用核保接口查询拒保信息出参：" + XmlHelper.classToXml(qutputDataBO));
//                if (qutputDataBO != null && CollectionUtilEx.isNotEmpty(qutputDataBO.getRefusedPolicys())) {
//                    ContractMasterPO contractMaster = new ContractMasterPO();
//                    for (ResultReturnRefused resultReturnRefused : qutputDataBO.getRefusedPolicys()) {
//                        contractMaster.setApplyCode(resultReturnRefused.getApplyCode());
//                        contractMaster.set("service_code", "10");// 返回类型
//                        contractMaster.setExpiryDate(resultReturnRefused.getRefusedTime());// 拒保时间
//                        contractMaster.set("busi_prod_code", resultReturnRefused.getBusiprodCode());// 主险代码
//                        contractMasterList.add(contractMaster);
//                    }
//                }
//            }
		} else {
			// 若类型为空，保单集合不为空，则查询保单的信息，生效状态;
			if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getPolicyCodeList())) {
				ContractMasterPO masterPO = new ContractMasterPO();
				if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getPolicyCodeList())) {
					masterPO.setList("policy_code_list", new ArrayList<String>(
							Arrays.asList(queryPolicyInfoInputBO.getPolicyCodeList().split(","))));
				}
				if (StringUtils.isNotBlank(queryPolicyInfoInputBO.getSubmitChannelList())) {
					masterPO.setList("submit_channel_list", new ArrayList<String>(
							Arrays.asList(queryPolicyInfoInputBO.getSubmitChannelList().split(","))));
				} else {
					masterPO.setSubmitChannel(new BigDecimal(1));
				}

				if (queryPolicyInfoInputBO.getDateFlag() == null) {
					// 默认时间区间为按生效日查询
					masterPO.getData().put("date_flag", 1);
				} else {
					masterPO.getData().put("date_flag", queryPolicyInfoInputBO.getDateFlag());
				}

				contractMasterList = queryContractMasterList(masterPO, "findDelayedPolicyByDate");
			} else {
				// 类型为空，保单集合为空，则根据查询时间查询当天生效的保单
				contractMasterList = queryContractMasterList(contractMasterPO, "findDelayedPolicyByDate");
			}
		}

		// 按保单号去重
		if (contractMasterList.size() > 1) {
			Map<String, ContractMasterPO> map = new TreeMap<String, ContractMasterPO>();
			// 无需去重数据
			List<ContractMasterPO> contractMasterPOs = new ArrayList<ContractMasterPO>();
			for (ContractMasterPO po : contractMasterList) {
				String policyCode = po.getPolicyCode();
				ContractMasterPO oldPo = map.get(policyCode);
				// 重庆农商行 续期复效类数据不进行去重处理--已和需求确认，提取时需注意日期
				if (null!=queryPolicyInfoInputBO.getServiceBank()&&"64".equals(queryPolicyInfoInputBO.getServiceBank())&& null!=po.getString("service_code")
						&&("PREF".equals(po.getString("service_code"))||"RE".equals(po.getString("service_code"))||"SR".equals(po.getString("service_code")))) {
					contractMasterPOs.add(po);
					continue;
				}
				
				if (oldPo == null || StringUtils.isBlank(oldPo.getPolicyCode())) {
					// 新保单号
					map.put(policyCode, po);
					continue;
				}
				// 存在相同保单号
				do {
					Date validateTime = po.getUtilDate("validate_time");
					Date oldValidateTime = oldPo.getUtilDate("validate_time");
					if (validateTime != null && oldValidateTime == null) {
						// 存在涉及保费变化或者特殊状态的保全项目，优先
						map.put(policyCode, po);
						continue;
					}
					if (validateTime != null && validateTime.compareTo(oldValidateTime) >= 0) {
						// 涉及保费变化或者特殊状态的保全项目，业务结束时间靠后，优先
						map.put(policyCode, po);
						continue;
					}
					if (validateTime != null) {
						// 涉及保费变化或者特殊状态的保全项目，业务结束时间靠前，跳过
						continue;
					}
					if (oldValidateTime != null) {
						// 不存在涉及保费变化或者特殊状态的保全项目，跳过
						continue;
					}
					String queryType = po.getString("service_code");
					if (!"17".equals(queryType) && !"27".equals(queryType)) {
						// 非 犹豫期/犹豫期生效 优先
						map.put(policyCode, po);
						continue;
					}
					String oldQueryType = oldPo.getString("service_code");
					if (!"17".equals(oldQueryType) && !"27".equals(oldQueryType)) {
						// 已存在非 犹豫期/犹豫期生效 跳过
						continue;
					}
					if ("17".equals(queryType)) {
						// 过犹豫期生效 优先
						map.put(policyCode, po);
						continue;
					}
				} while (false);
			}
			contractMasterList = new ArrayList<ContractMasterPO>(map.values());
			if (CollectionUtilEx.isNotEmpty(contractMasterPOs)) {
				contractMasterList.addAll(contractMasterPOs);
			}
		}

		if (CollectionUtilEx.isNotEmpty(contractMasterList)) {
			logger.info("【非实时保单状态查询-保单库】查询符合条件的保单数：" + contractMasterList.size());
			int policyCount = 0;// 保单序号
			for (ContractMasterPO contractMaster : contractMasterList) {
				if (contractMaster.getData().get("service_code") != null
						&& "CLMEND".equals(contractMaster.getData().get("service_code").toString())
						&& !Constants.LIABILITY_STATE__TERMINATED.equals(contractMaster.getLiabilityState())) {
					continue;
				}
				// 满期领取只查询已满期，并且操作满期领取的
				if (contractMaster.getData().get("service_code") != null
						&& "AG".equals(contractMaster.getData().get("service_code").toString())
						&& (!Constants.LIABILITY_STATE__TERMINATED.equals(contractMaster.getLiabilityState())
								|| (Constants.LIABILITY_STATE__TERMINATED.equals(contractMaster.getLiabilityState()))
										&& !Constants.END_CAUSE_MATURITY.equals(contractMaster.getEndCause()))) {
					continue;
				}

				policyCount++;

				BigDecimal surrenderAmount = new BigDecimal(0);// 退保金额

				// 给返回对象赋值
				PolicyQueryDelayedPasBO policyQueryDelayedPasBO = new PolicyQueryDelayedPasBO();
				policyQueryDelayedPasBO.setServiceBank(contractMaster.getServiceBank()); // 服务银行
				policyQueryDelayedPasBO.setBankSaleChannel(contractMaster.getInputType()); // 销售渠道
				policyQueryDelayedPasBO.setSuspendDate(contractMaster.getSuspendDate()); // 保单中止日

				// 双主险标识
				BigDecimal doubleMainriskFlag = Constants.YES_NO__NO;

				// 校验是否为双主险，若为双主险【返回退保、犹退、满期给付】
				ContractMasterPO contractMasterPO1 = new ContractMasterPO();
				contractMasterPO1.setRelationPolicyCode(contractMaster.getPolicyCode());
				contractMasterPO1.setDoubleMainRiskFlag(Constants.YES_NO__YES);// 双主险标识
				contractMasterPO1 = contractMasterDao.findContractMasterByPolicyCode(contractMasterPO1);
				if (contractMasterPO1 != null && contractMasterPO1.getPolicyCode() != null) {
					continue;// 双主险保单，万能险不单独返回
				}
				// 查询log表判断-双主险保单，万能险不单独返回
				String relationPolicyCode = null; // 关联保单号
				String doubleMainRiskFlagString = null; // 双主险标识
				ContractMasterLogPO contractMasterLogPO = new ContractMasterLogPO();
				contractMasterLogPO.setPolicyCode(contractMaster.getPolicyCode());
				List<ContractMasterLogPO> contractMasterLogPOs = contractMasterLogDao
						.findMinContractMasterLog(contractMasterLogPO);
				if (CollectionUtilEx.isNotEmpty(contractMasterLogPOs)) {
					relationPolicyCode = contractMasterLogPOs.get(0).getRelationPolicyCode();
					doubleMainRiskFlagString = StringUtils
							.isBlank(contractMasterLogPOs.get(0).get("double_mainrisk_flag").toString()) ? ""
									: contractMasterLogPOs.get(0).get("double_mainrisk_flag").toString();
					if ("1".equals(doubleMainRiskFlagString) && (StringUtils.isBlank(relationPolicyCode))) {
						continue;
					}
				}
				ContractMasterPO contractMasterPO2 = new ContractMasterPO();
				contractMasterPO2.setPolicyCode(contractMaster.getPolicyCode());
				contractMasterPO2.setDoubleMainRiskFlag(Constants.YES_NO__YES);// 双主险标识
				contractMasterPO2 = contractMasterDao.findContractMasterByPolicyCode(contractMasterPO2);
				if ("1".equals(doubleMainRiskFlagString)) {
					BigDecimal doublePremium = new BigDecimal(0);
					doubleMainriskFlag = Constants.YES_NO__YES;// 双主险保单
					policyQueryDelayedPasBO.setDoubleMainriskFlag(doubleMainriskFlag);
					ContractProductPO contractProductPO = new ContractProductPO();
					contractProductPO.setPolicyCode(contractMaster.getPolicyCode());
					List<ContractProductPO> contractProductList = contractProductDao
							.findAllContractProduct(contractProductPO);
					if (CollectionUtilEx.isNotEmpty(contractProductList)) {
						doublePremium = doublePremium.add(contractProductList.get(0).getTotalPremAf());
					}
					contractProductPO.setPolicyCode(relationPolicyCode);
					List<ContractProductPO> contractProductList1 = contractProductDao
							.findAllContractProduct(contractProductPO);
					if (CollectionUtilEx.isNotEmpty(contractProductList1)) {
						doublePremium = doublePremium.add(contractProductList1.get(0).getTotalPremAf());
					}
					policyQueryDelayedPasBO.setDoublePremium(doublePremium);

					// 如果是双主险保单，年金险保单终止原因为15-撤保，返回的surrenderAmount为年金险的总保费字段，
					// 如果关联的万能险保单也为终止原因15，加上万能险保费
					if (CollectionUtilEx.isNotEmpty(contractProductList)
							&& "3".equals(contractProductList.get(0).getLiabilityState().toString())
							&& "15".equals(contractProductList.get(0).getEndCause())) {
						surrenderAmount = contractProductList.get(0).getTotalPremAf();
						if (CollectionUtilEx.isNotEmpty(contractProductList1)
								&& "3".equals(contractProductList1.get(0).getLiabilityState().toString())
								&& "15".equals(contractProductList1.get(0).getEndCause())) {
							surrenderAmount = surrenderAmount.add(contractProductList1.get(0).getTotalPremAf());
						}
						policyQueryDelayedPasBO.setSurrenderAmount(surrenderAmount);
					}
				}

				BigDecimal policyHesitateFlag = BigDecimal.ZERO;
				if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
					// 重庆农商行使用保单层犹豫期标志
					CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
					csPolicyChangePO.setPolicyId(contractMaster.getPolicyId());
					List<CsPolicyChangePO> csPolicyChangePOList = csPolicyChangeDao
							.findAllCsPolicyChangeOrderByFinishTimeDesc(csPolicyChangePO);
					if (CollectionUtilEx.isNotEmpty(csPolicyChangePOList)) {
						if (csPolicyChangePOList.get(0).get("hesitate_flag") != null) {
							policyHesitateFlag = csPolicyChangePOList.get(0).getHesitateFlag();
							if (Constants.YES_NO__YES.equals(policyHesitateFlag)) {
								policyQueryDelayedPasBO.setPolicyHesitateFlag(BigDecimal.ONE);
							} else if (Constants.YES_NO__NO.equals(policyHesitateFlag)) {
								policyQueryDelayedPasBO.setPolicyHesitateFlag(BigDecimal.ZERO);
							}
						}
					}
				}

				// 终止原因
				if (contractMaster.getEndCause() != null) {
					policyQueryDelayedPasBO.setEndCause(contractMaster.getEndCause());
					if (Constants.END_CAUSE_CANCEL.equals(contractMaster.getEndCause())) {
						// 查询退保信息
						if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
							// 重庆农商行使用保单层犹豫期标志
							if (Constants.YES_NO__YES.equals(policyHesitateFlag)) {
								policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(2));
							} else if (Constants.YES_NO__NO.equals(policyHesitateFlag)) {
								policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(3));
							}
						} else {
							List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMaster, "findSurrenderInfo");
							if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
								if (contractMasterPOList.get(0).get("hesitate_flag") != null) {
									BigDecimal hesitateFlag = (BigDecimal) contractMasterPOList.get(0)
											.get("hesitate_flag");
									if (Constants.YES_NO__YES.equals(hesitateFlag)) {
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(2));
									} else if (Constants.YES_NO__NO.equals(hesitateFlag)) {
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(3));
									}
								}
							}
						}
					}
				}

				// 承保标志
				if (contractMaster.getSubmitChannel() != null
						&& contractMaster.getSubmitChannel().compareTo(new BigDecimal(1)) == 0
						&& !"5".equals(contractMaster.getSubinputType())) {
					policyQueryDelayedPasBO.setYbtField1("00");// 线上保单
				} else {
					policyQueryDelayedPasBO.setYbtField1("01");// 线下保单
				}
				
				// 仅 04-续期、16-RE\SR 使用，其他状态提取需验证是否适用   RenewalNum、FeeAmount、BankAccount
				policyQueryDelayedPasBO.setRenewalNum(contractMaster.getData().get("renewal_num") == null?"":contractMaster.getData().get("renewal_num").toString());
				policyQueryDelayedPasBO.setFeeAmount(contractMaster.getData().get("re_fee_amount") == null?"":contractMaster.getData().get("re_fee_amount").toString());
				policyQueryDelayedPasBO.setBankAccount(contractMaster.getData().get("bank_account") == null?"":contractMaster.getData().get("bank_account").toString());
				policyQueryDelayedPasBO.setRenewalDate(contractMaster.getData().get("renewal_date") == null?null:DateUtilsEx.formatToDate(contractMaster.getData().get("renewal_date").toString(), "yyyy-MM-dd"));
				
				// 交易渠道(00-柜面 21-个人网银 30-自助渠道 51-手机银行)
				if ("15".equals(contractMaster.getSubinputType())) {
					policyQueryDelayedPasBO.setYbtField3("00");
				} else if ("12".equals(contractMaster.getSubinputType())) {
					policyQueryDelayedPasBO.setYbtField3("21");
				} else if ("13".equals(contractMaster.getSubinputType())) {
					policyQueryDelayedPasBO.setYbtField3("30");
				} else if ("14".equals(contractMaster.getSubinputType())) {
					policyQueryDelayedPasBO.setYbtField3("51");
				}
				// 拒保 特殊处理
				if (contractMaster.getData().get("service_code") != null
						&& "10".equals(contractMaster.getData().get("service_code").toString())) {
					policyQueryDelayedPasBO.setApplyCode(contractMaster.getApplyCode());// 投保单号
					policyQueryDelayedPasBO.setLiabilityChgDate(contractMaster.getExpiryDate());// 拒保时间
					if (contractMaster.getData().get("busi_prod_code") != null) {
						policyQueryDelayedPasBO
								.setBusiProdCode1(contractMaster.getData().get("busi_prod_code").toString());// 主险代码1
					}
				} else {
					// 由回执表得出签单日期
					PolicyAcknowledgementPO policyAcknowledgementPO = new PolicyAcknowledgementPO();
					policyAcknowledgementPO.setPolicyId(contractMaster.getPolicyId());
					policyAcknowledgementPO = policyAcknowledgementDao
							.findPolicyAcknowledgement(policyAcknowledgementPO);
					Integer hesitationPeriodDay = 10;// 初始化犹豫期天数(退保、加保、减保区分犹豫期内外)
					if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
						// 重庆农商行犹豫期为15天 #114106
						hesitationPeriodDay = 15;
					}
					if (queryTypeList != null) {
						if (Constants.END_CAUSE_CANCEL.equals(contractMaster.getEndCause()) || (contractMaster.getData()
								.get("service_code") != null
								&& ("XT".equals(contractMaster.getData().get("service_code").toString())
										|| "IT".equals(contractMaster.getData().get("service_code").toString())
										|| "CT".equals(contractMaster.getData().get("service_code").toString())
										|| "EA".equals(contractMaster.getData().get("service_code").toString())
										|| "PA".equals(contractMaster.getData().get("service_code").toString())
										|| "PT".equals(contractMaster.getData().get("service_code").toString())))) {
							BigDecimal hesitateFlag = (BigDecimal) contractMaster.getData().get("hesitate_flag");
							if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
								// 重庆农商行使用保单层犹豫期标志
								contractMaster.setBigDecimal("hesitate_flag", policyHesitateFlag);
							}
							if (contractMaster.getData().get("hesitate_flag") == null
									&& contractMaster.getData().get("validate_time") != null) {
								// 根据签单日期+犹豫期天数和业务变更日期进行比较，判断是否在犹豫期内
								if (contractMaster.getData().get("hesitation_period_day") != null) {
									hesitationPeriodDay = Integer
											.valueOf(contractMaster.getData().get("hesitation_period_day").toString());
								}
								Date compareDate1 = DateUtilsEx.addDay(policyAcknowledgementPO.getAcknowledgeDate(),
										hesitationPeriodDay);
								if (contractMaster.getData().get("validate_time") != null) {
									if (compareDate1.getTime() >= ((Date) contractMaster.getData().get("validate_time"))
											.getTime()) {
										// 赋值犹豫期内标识
										contractMaster.getData().put("hesitate_flag", 1);
									} else {
										// 赋值犹豫期外标识
										contractMaster.getData().put("hesitate_flag", 0);
									}
								}
							}
							contractMaster.setBigDecimal("hesitate_flag", hesitateFlag);
						}
					}

					/** 查询保单上的缴费信息 */
					ContractMasterPO contractMasters = queryPolicyInfoDao.findBCMDelayedPolicyByDate(contractMaster);
					if (contractMasters.getPolicyCode() != null) {
						// 网点经办人代码
						if (contractMasters.getServiceHandlerCode() != null) {
							policyQueryDelayedPasBO.setServiceHandlerCode(contractMasters.getServiceHandlerCode());// 网点经办人代码
						}
						// 业务日期
						if (contractMasters.getData().get("due_time") != null) {
							policyQueryDelayedPasBO.setDueTime(DateUtilsEx
									.formatToDate(contractMasters.getData().get("due_time").toString(), "yyyy-MM-dd"));// 网点经办人代码
						}
						// 交易流水号
						if (contractMasters.getData().get("unit_number") != null) {
							policyQueryDelayedPasBO
									.setUnitNumber((String) contractMasters.getData().get("unit_number"));
						}
						// 交易日期
						if (contractMasters.getData().get("update_time") != null) {
							policyQueryDelayedPasBO.setUpdateTime(DateUtilsEx.formatToDate(
									contractMasters.getData().get("update_time").toString(), "yyyy-MM-dd"));
						}
						// 投保人 省
						if (contractMasters.getData().get("state") != null) {
							policyQueryDelayedPasBO.setState((String) contractMasters.getData().get("state"));
						}
						// 投保人 市
						if (contractMasters.getData().get("city") != null) {
							policyQueryDelayedPasBO.setCity((String) contractMasters.getData().get("city"));
						}
						// 投保人 区
						if (contractMasters.getData().get("district") != null) {
							policyQueryDelayedPasBO.setDistrict((String) contractMasters.getData().get("district"));
						}
						// 主险简称
						if (contractMasters.getData().get("product_name_std") != null) {
							policyQueryDelayedPasBO
									.setProductNameStd((String) contractMasters.getData().get("product_name_std"));
						}
						// 每期缴费金额
						if (contractMasters.getData().get("std_prem_af") != null) {
							policyQueryDelayedPasBO
									.setStdPremAf((BigDecimal) contractMasters.getData().get("std_prem_af"));
						}
						// 续期总期数
						if (contractMasters.getData().get("total_period") != null) {
							policyQueryDelayedPasBO
									.setTotalPeriod(contractMasters.getData().get("total_period").toString());
						}
						// 已缴保费
						if (contractMasters.getData().get("total_prem_af") != null) {
							policyQueryDelayedPasBO
									.setTotalPrem((BigDecimal) contractMasters.getData().get("total_prem_af"));
						}
						// 已缴期数
						if (contractMasters.getData().get("policy_period") != null) {
							policyQueryDelayedPasBO
									.setPolicyPeriod(contractMasters.getData().get("policy_period").toString());
						}
						// 续期缴费账号
						if (contractMasters.getData().get("next_account") != null) {
							policyQueryDelayedPasBO
									.setNextAccount((String) contractMasters.getData().get("next_account"));
						}

						// 下期交费日期
						if (contractMasters.getData().get("pay_due_date") != null) {
							policyQueryDelayedPasBO.setNextDueDate(DateUtilsEx.formatToDate(
									contractMasters.getData().get("pay_due_date").toString(), "yyyy-MM-dd"));
						}

						// 犹豫期天数
						if (contractMasters.getData().get("hesitation_period_day") != null) {
							policyQueryDelayedPasBO.setHesitationPeriodDay(
									(BigDecimal) contractMasters.getData().get("hesitation_period_day"));
						}

					}

					if (queryPolicyInfoInputBO.getBatchDate() == null) {
						queryPolicyInfoInputBO.setBatchDate(new Date());
					}

					// 计算保单现金价值(调用保单接口) querytype-22 查询止期对应的现价
					if (contractMaster.getData().get("service_code") != null
							&& "22".equals(contractMaster.getData().get("service_code").toString())
							&& queryPolicyInfoInputBO.getBatchEndDate() != null) {
						queryPolicyInfoInputBO.setBatchDate(queryPolicyInfoInputBO.getBatchEndDate());
					}
					if (contractMaster.getPolicyCode() != null && queryPolicyInfoInputBO.getBatchDate() != null
							&& contractMaster.getLiabilityState() != null
							&& Constants.LIABILITY_STATE__INFORCE.equals(contractMaster.getLiabilityState())
							&& policyAcknowledgementPO.getAcknowledgeDate() != null) {
						// 判断是否犹豫期，犹豫期内返回总保费，犹豫期外调产品计算
						// 根据签单日期+犹豫期天数和业务变更日期进行比较，判断是否在犹豫期内
						if (contractMasters.getPolicyCode() != null
								&& contractMasters.getData().get("hesitation_period_day") != null) {
							hesitationPeriodDay = Integer
									.valueOf(contractMasters.getData().get("hesitation_period_day").toString());
						}
						Date compareDate1 = DateUtilsEx.addDay(policyAcknowledgementPO.getAcknowledgeDate(),
								hesitationPeriodDay);
						if (compareDate1.getTime() >= queryPolicyInfoInputBO.getBatchDate().getTime()) {
							// 犹豫期内返回总保费
							if (contractMasters.getData().get("total_prem_af") != null) {
								policyQueryDelayedPasBO.setPolicyCashValue(
										(BigDecimal) contractMasters.getData().get("total_prem_af"));// 保单层现金价值
							}
						} else {
							// 犹豫期外调产品计算现价
							QueryCashValueListReqBO queryCashValueListReqBO = new QueryCashValueListReqBO();
							queryCashValueListReqBO.setPolicyCode(contractMaster.getPolicyCode());
							queryCashValueListReqBO.setCurrentDate(queryPolicyInfoInputBO.getBatchDate());
							logger.info(
									"【非实时保单状态查询-保单库】查询保单下的责任组现价入参：" + XmlHelper.classToXml(queryCashValueListReqBO));
							QueryCashValueListResBO queryCashValueListResBO = queryCashValueListService
									.queryCashValueList(queryCashValueListReqBO);
							if (queryCashValueListResBO != null && CollectionUtilEx
									.isNotEmpty(queryCashValueListResBO.getQueryCashValueInfoList())) {
								logger.info("【非实时保单状态查询-保单库】查询保单下的责任组现价出参："
										+ XmlHelper.classToXml(queryCashValueListResBO));
								BigDecimal policyCashValue = new BigDecimal(0);
								// 循环保单下责任组层现金价值，统计总的现金价值
								if (null!=queryCashValueListResBO.getQueryCashValueInfoList()) {
									for (QueryCashValueInfoBO queryCashValueInfoBO : queryCashValueListResBO
											.getQueryCashValueInfoList()) {
										if (queryCashValueInfoBO.getTotalCashValue() != null) {
											policyCashValue = policyCashValue.add(queryCashValueInfoBO.getTotalCashValue());
										}
									}
								}
								policyQueryDelayedPasBO.setPolicyCashValue(policyCashValue);// 保单层现金价值
							}
						}

					}
					if (CollectionUtilEx.isNotEmpty(queryTypeList)) {
						BigDecimal hesitateFlag = (BigDecimal) contractMaster.getData().get("hesitate_flag");
						if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
							// 重庆农商行使用保单层犹豫期标志
							contractMaster.setBigDecimal("hesitate_flag", policyHesitateFlag);
						}
						// 犹豫期退保总记录数(不包含加保、减保)
						if (contractMaster.getData().get("hesitate_flag") != null
								&& contractMaster.getData().get("service_code") != null
								&& (!"PA".equals(contractMaster.getData().get("service_code").toString())
										|| !"PT".equals(contractMaster.getData().get("service_code").toString()))) {
							if (contractMaster.getData().get("surrender_amount") != null) {
								surrenderAmount = (BigDecimal) contractMaster.getData().get("surrender_amount");
							}
							if ("1".equals(contractMaster.getData().get("hesitate_flag").toString())) {
								hesitateCTCount = hesitateCTCount + 1;// 犹豫期退保总记录数
								if (contractMaster.getData().get("surrender_amount") != null) {
									totalHesitateCTAmount = totalHesitateCTAmount.add(surrenderAmount);// 犹豫期退保总金额
								}
							} else {
								CTCount = CTCount + 1;// 退保总记录数
								if (contractMaster.getData().get("surrender_amount") != null) {
									totalCTAmount = totalCTAmount.add(surrenderAmount);// 退保总金额
								}
							}
							// 判断是否双主险退保，若为双主险退保，则退保金额返回年金险和万能险的总费用
							if ("1".equals(doubleMainRiskFlagString) && contractMaster.getEndCause() != null
									&& Constants.END_CAUSE_CANCEL.equals(contractMasterPO2.getEndCause())) {
								// 查询万能险退保信息
								ContractMasterPO contractMasterPO3 = new ContractMasterPO();
								contractMasterPO3.setPolicyCode(relationPolicyCode);
								List<ContractMasterPO> contractMasterPOList = queryContractMasterList(contractMasterPO3, "findSurrenderInfo");
								if (CollectionUtilEx.isNotEmpty(contractMasterPOList)) {
									if (contractMasterPOList.get(0).get("surrender_amount") != null) {
										surrenderAmount = surrenderAmount
												.add((BigDecimal) contractMasterPOList.get(0).get("surrender_amount"));
									}
								}
							}
							policyQueryDelayedPasBO.setSurrenderAmount(surrenderAmount);
						}
						contractMaster.setBigDecimal("hesitate_flag", hesitateFlag);
					}

					logger.info("【非实时保单状态查询-保单库】拼接保单基本信息 start");
					policyQueryDelayedPasBO.setPolicyCode(contractMaster.getPolicyCode());// 保单号
					policyQueryDelayedPasBO.setApplyCode(contractMaster.getApplyCode());// 投保单号
					policyQueryDelayedPasBO.setValidateDate(contractMaster.getValidateDate());// 生效日
					policyQueryDelayedPasBO.setLiabilityState(contractMaster.getLiabilityState());// 效力状态
					// 003-新华人寿
					policyQueryDelayedPasBO.setCarrierCode("003");// 保险公司代码
					// 受理渠道
					if (contractMaster.getData().get("service_type") != null) {
						if (contractMaster.getData().get("service_type").toString().equals("10")) {
							policyQueryDelayedPasBO.setAcceptChannel("1");// 银保通受理
						} else {
							policyQueryDelayedPasBO.setAcceptChannel("0");// 默认柜面受理
						}

					}
					// 分红账户余额、保单总保费、续期缴费方式、投保人缴费账号、业务变更时间(保单层)
					if (contractMaster.getData().get("bonus_sa") != null) {
						policyQueryDelayedPasBO.setBonusSa((BigDecimal) contractMaster.getData().get("bonus_sa"));// 红利保额
						// 查询最近分红日
						BonusAllocatePO bonusAllocatePO = new BonusAllocatePO();
						bonusAllocatePO.setPolicyCode(contractMaster.getPolicyCode());
						bonusAllocatePO.setAllocateType("02");
						BonusAllocatePO bonusAllocate = bonusAllocateDao.findLastBonusTime(bonusAllocatePO);
						if (bonusAllocate.getAllocateDate() != null) {
							bonusAllocatePO.setAllocateDate(bonusAllocate.getAllocateDate());
							policyQueryDelayedPasBO.setBonusDate(bonusAllocate.getAllocateDate());// 红利发放日
							// 查询分红表最后一次分红金额
							// 查询终了红利金额
							List<BonusAllocatePO> bonusAllocatePOs = bonusAllocateDao
									.findAllBonusAllocate(bonusAllocatePO);
							if (bonusAllocatePOs.size() > 0) {
								policyQueryDelayedPasBO.setThisBonusSa(bonusAllocatePOs.get(0).getBonusSa());// 本期发放金额
								policyQueryDelayedPasBO.setTerminalBonus(bonusAllocatePOs.get(0).getTerminalBonus());// 终了红利
							}
						}

					}
					if (contractMaster.getData().get("next_account") != null) {
						policyQueryDelayedPasBO.setNextAccount(contractMaster.getData().get("next_account").toString());// 缴费账号
					}
					if (contractMaster.getData().get("pay_next") != null) {
						policyQueryDelayedPasBO.setPayNext(contractMaster.getData().get("pay_next").toString());// 续期缴费方式
					}
					if (contractMaster.getData().get("total_prem_af") != null) {
						policyQueryDelayedPasBO
								.setTotalPremAf((BigDecimal) contractMaster.getData().get("total_prem_af"));// 保单总保费
					}
					if (contractMaster.getData().get("validate_time") != null) {
						policyQueryDelayedPasBO.setLiabilityChgDate(DateUtilsEx
								.formatToDate(contractMaster.getData().get("validate_time").toString(), "yyyy-MM-dd"));// 保单业务变更日期
					}
					if (contractMaster.getLapseDate() != null) {
						policyQueryDelayedPasBO.setLapseDate(contractMaster.getLapseDate());// 保单失效日
					}
					if (contractMaster.getExpiryDate() != null) {
						policyQueryDelayedPasBO.setExpiryDate(DateUtilsEx.addDay(contractMaster.getExpiryDate(), -1));// 保单满期日，不知道为什么以前要减1天，重庆农商行不减
						if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
							policyQueryDelayedPasBO.setExpiryDate(contractMaster.getExpiryDate());// 保单满期日
						}
					}
					if (contractMaster.getServiceBankBranch() != null) {
						policyQueryDelayedPasBO.setServiceBankBranch(contractMaster.getServiceBankBranch());// 银行地区号
					}
					if (contractMaster.getData().get("initial_amount") != null) {
						policyQueryDelayedPasBO
								.setTotalInitialAmount((BigDecimal) contractMaster.getData().get("initial_amount"));// 保单总投保金额
					}
					if (contractMaster.getApplyDate() != null) {
						policyQueryDelayedPasBO.setApplyDate(contractMaster.getApplyDate());// 投保日
					}
					if (contractMaster.getIssueDate() != null) {
						policyQueryDelayedPasBO.setIssueDate(contractMaster.getIssueDate());// 签单日
																							// 同承保日
					}
					if (contractMaster.getAcknowledgeDate() != null) {
						policyQueryDelayedPasBO.setAcknowledgeDate(contractMaster.getAcknowledgeDate());// 回执日期
					}
					// 查询申请人信息
					if (contractMaster.getData().get("customer_id") != null) {
						CustomerPO customerPO = queryCustomerInfo(
								(BigDecimal) contractMaster.getData().get("customer_id"));
						if (customerPO.getCustomerName() != null) {
							policyQueryDelayedPasBO.setApplyName(customerPO.getCustomerName());// 申请人信息
						}
					}
					// 退费渠道
					if (contractMaster.get("bank_channel") != null) {
						if (contractMaster.get("bank_channel").toString().equals("1")) {
							policyQueryDelayedPasBO.setBankChannel("01");// 银行柜面
						}
						if (contractMaster.get("bank_channel").toString().equals("2")) {
							policyQueryDelayedPasBO.setBankChannel("02");// 网银
						}
						if (contractMaster.get("bank_channel").toString().equals("3")) {
							policyQueryDelayedPasBO.setBankChannel("03");// 自助通
						}
						if (contractMaster.get("bank_channel").toString().equals("4")) {
							policyQueryDelayedPasBO.setBankChannel("05");// 手机银行
						}
					} else {
						policyQueryDelayedPasBO.setBankChannel("00");// 默认保险公司
					}
					// 机构号
					if (contractMaster.get("organ_code") != null) {
						policyQueryDelayedPasBO.setOrganCode(contractMaster.get("organ_code").toString());
					}
					// 保单最新状态，判断返回终止类型
					BigDecimal hesitateFlag = (BigDecimal) contractMaster.getData().get("hesitate_flag");
					if ("64".equals(queryPolicyInfoInputBO.getServiceBank())) {
						// 重庆农商行使用保单层犹豫期标志
						contractMaster.setBigDecimal("hesitate_flag", policyHesitateFlag);
					}
					if (contractMaster.getData().get("hesitate_flag") != null
							&& contractMaster.getData().get("service_code") != null) {
						// 若返回的类型中有加保、减保，并且查询类型包含犹豫期内加保、加保，则走此逻辑
						if ("PA".equals(contractMaster.getData().get("service_code").toString())
								|| "PT".equals(contractMaster.getData().get("service_code").toString())
										&& (queryTypeList.contains("06") || queryTypeList.contains("061")
												|| queryTypeList.contains("062") || queryTypeList.contains("07")
												|| queryTypeList.contains("071") || queryTypeList.contains("072"))) {

							if (contractMaster.getData().get("fee_amount") != null) {
								surrenderAmount = (BigDecimal) contractMaster.getData().get("fee_amount");
								policyQueryDelayedPasBO.setSurrenderAmount(surrenderAmount);
							}

							if ("1".equals(contractMaster.getData().get("hesitate_flag").toString())) {
								if ("PA".equals(contractMaster.getData().get("service_code").toString())) {
									if (queryTypeList.contains("06")) {
										policyQueryDelayedPasBO.setQueryType("06");// 查询类型【犹豫期内加保】
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(6));
									} else {
										policyQueryDelayedPasBO.setQueryType("061");// 查询类型【犹豫期内加保】
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(61));
									}
								} else if ("PT".equals(contractMaster.getData().get("service_code").toString())) {
									if (queryTypeList.contains("07")) {
										policyQueryDelayedPasBO.setQueryType("07");// 查询类型【犹豫期内减保】
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(7));
									} else {
										policyQueryDelayedPasBO.setQueryType("071");// 查询类型【犹豫期内减保】
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(71));
									}
								}
							} else {
								if ("PA".equals(contractMaster.getData().get("service_code").toString())) {
									if (queryTypeList.contains("06")) {
										policyQueryDelayedPasBO.setQueryType("06");// 查询类型【犹豫期内加保】
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(6));
									} else {
										policyQueryDelayedPasBO.setQueryType("062");// 查询类型【犹豫期外加保】
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(62));
									}
								} else if ("PT".equals(contractMaster.getData().get("service_code").toString())) {
									if (queryTypeList.contains("07")) {
										policyQueryDelayedPasBO.setQueryType("07");// 查询类型【犹豫期内减保】
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(7));
									} else {
										policyQueryDelayedPasBO.setQueryType("072");// 查询类型【犹豫期外减保】
										policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(72));
									}
								}
							}
						} else {
							if ("XT".equals(contractMaster.getData().get("service_code").toString())
									|| "IT".equals(contractMaster.getData().get("service_code").toString())
									|| "CT".equals(contractMaster.getData().get("service_code").toString())
									|| "EA".equals(contractMaster.getData().get("service_code").toString())) {
								/**
								 * #readmine 56537
								 */
								policyQueryDelayedPasBO
										.setServiceCode(contractMaster.getData().get("service_code") == null ? ""
												: contractMaster.getData().get("service_code").toString());
								// 区分犹豫期内、外退保
								if ("1".equals(contractMaster.getData().get("hesitate_flag").toString())) {
									if (queryTypeList.contains("02")) {
										policyQueryDelayedPasBO.setQueryType("02");// 查询类型【犹豫期内退保】
									} else if (queryTypeList.contains("021") || queryTypeList.contains("022")) {// 犹豫期部分和全额退保判断
										if (Constants.LIABILITY_STATE_END.equals(contractMaster.getLiabilityState())) {// 保单状态为终止，则为全额退保
											policyQueryDelayedPasBO.setQueryType("022");// 查询类型【犹豫期内全额退保】
										} else {
											policyQueryDelayedPasBO.setQueryType("021");// 查询类型【犹豫期内部分退保】
										}
									}
									policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(2));// 保单最新状态
									// 犹豫期内退保】
									if ("EA".equals(contractMaster.getData().get("service_code").toString())
											|| "CT".equals(contractMaster.getData().get("service_code").toString())) {
										policyQueryDelayedPasBO.setLifeCycle("076024");// 公司解约
										// 如果附加险退保，则返回对应的生命周期
										if ("02".equals(queryPolicyInfoInputBO.getServiceBank().toString())) {
											ContractBusiProdPO contractbusiProdPO = new ContractBusiProdPO();
											contractbusiProdPO.setPolicyCode(contractMaster.getPolicyCode());
											List<ContractBusiProdPO> contractBusiProdPOList = contractBusiProdDao
													.findAddBusiProdByPolicyId(contractbusiProdPO);
											if (!contractBusiProdPOList.isEmpty()) {
												for (ContractBusiProdPO contractBusiProdList : contractBusiProdPOList) {
													if (contractBusiProdList.getMasterBusiItemId() != null) {
														SurrenderPO surrenderPO = new SurrenderPO();
														surrenderPO.setBusiItemId(contractBusiProdList.getBusiItemId());
														surrenderPO.setHesitateFlag(new BigDecimal(1));
														List<SurrenderPO> surrenderPOList = surrenderDao
																.findAllSurrender(surrenderPO);
														if (!surrenderPOList.isEmpty()) {
															policyQueryDelayedPasBO.setLifeCycle("076027");// 附加险犹豫期内发生退保
														}
													}
												}
											}
										}
									}
								} else {
									if (queryTypeList.contains("03")) {
										policyQueryDelayedPasBO.setQueryType("03");// 查询类型【犹豫期外退保】
									} else if (queryTypeList.contains("031") || queryTypeList.contains("032")) {
										if (Constants.LIABILITY_STATE_END.equals(contractMaster.getLiabilityState())) {// 保单状态为终止，则为全额退保
											policyQueryDelayedPasBO.setQueryType("032");// 查询类型【犹豫期外全额退保】
										} else {
											policyQueryDelayedPasBO.setQueryType("031");// 查询类型【犹豫期外部分退保】
										}
									}
									policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(3));// 保单最新状态【犹豫期外退保】
									if ("EA".equals(contractMaster.getData().get("service_code").toString())
											|| "CT".equals(contractMaster.getData().get("service_code").toString())) {
										policyQueryDelayedPasBO.setLifeCycle("076025");// 公司解约
										// 如果附加险退保，则返回对应的生命周期
										if ("02".equals(queryPolicyInfoInputBO.getServiceBank().toString())) {
											ContractBusiProdPO contractbusiProdPO = new ContractBusiProdPO();
											contractbusiProdPO.setPolicyCode(contractMaster.getPolicyCode());
											List<ContractBusiProdPO> contractBusiProdPOList = contractBusiProdDao
													.findAddBusiProdByPolicyId(contractbusiProdPO);
											if (!contractBusiProdPOList.isEmpty()) {
												for (ContractBusiProdPO contractBusiProdList : contractBusiProdPOList) {
													if (contractBusiProdList.getMasterBusiItemId() != null) {
														SurrenderPO surrenderPO = new SurrenderPO();
														surrenderPO.setBusiItemId(contractBusiProdList.getBusiItemId());
														surrenderPO.setHesitateFlag(new BigDecimal(0));
														List<SurrenderPO> surrenderPOList = surrenderDao
																.findAllSurrender(surrenderPO);
														if (!surrenderPOList.isEmpty()) {
															policyQueryDelayedPasBO.setLifeCycle("079001");// 附加险犹豫期外发生退保
														}
													}
												}
											}
										}
									}
								}
							}
						}
						if ("XT".equals(contractMaster.getData().get("service_code").toString())
								&& Constants.LIABILITY_STATE_END.equals(contractMaster.getLiabilityState())) {
							policyQueryDelayedPasBO.setLifeCycle("076029");// 协议退保
							policyQueryDelayedPasBO
									.setServiceCode(contractMaster.getData().get("service_code") == null ? ""
											: contractMaster.getData().get("service_code").toString());
						}
					} else if (contractMaster.getData().get("service_code") != null
							&& "AG".equals(contractMaster.getData().get("service_code").toString())
							&& queryTypeList != null && !queryTypeList.contains("23")) {
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(4));// 保单最新状态【
						policyQueryDelayedPasBO.setQueryType("05");// 查询类型【满期给付】
					} else if (contractMaster.getData().get("service_code") != null
							&& "CLMEND".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal("5"));// 保单最新状态【
						policyQueryDelayedPasBO.setQueryType("08");// 查询类型【满期给付终止】
						// 查询理赔金额
						PremArapPO premArap = new PremArapPO();
						premArap.setPolicyCode(contractMaster.getPolicyCode());
						premArap = queryPolicyInfoDao.findPolicyClaimPrem(premArap);
						if (premArap.getFeeAmount() != null) {
							BigDecimal claimSurrenderAmount = new BigDecimal(0);
							claimSurrenderAmount = claimSurrenderAmount.add(premArap.getFeeAmount());
							policyQueryDelayedPasBO.setSurrenderAmount(claimSurrenderAmount);
						}
					} else if (contractMaster.getData().get("service_code") != null
							&& ("PNL".equals(contractMaster.getData().get("service_code").toString())
									|| "PILPI".equals(contractMaster.getData().get("service_code").toString()))) {
						policyQueryDelayedPasBO.setQueryType("09");// 查询类型【保单失效】
						policyQueryDelayedPasBO.setNewLiabilityState(contractMaster.getLiabilityState());
					} else if (contractMaster.getData().get("service_code") != null
							&& "PREF".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("04");// 查询类型【续期缴费】
						policyQueryDelayedPasBO.setNewLiabilityState(contractMaster.getLiabilityState());
						policyQueryDelayedPasBO.setLifeCycle("076012");// 续期缴费 生命周期
					} else if (contractMaster.getData().get("service_code") != null
							&& "01".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("01");// 查询类型【有效】
						policyQueryDelayedPasBO.setNewLiabilityState(contractMaster.getLiabilityState());
						policyQueryDelayedPasBO.setLiabilityChgDate(contractMaster.getValidateDate());
					} else if (contractMaster.getData().get("service_code") != null
							&& "LN".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("11");// 查询类型【贷款】
						policyQueryDelayedPasBO.setNewLiabilityState(contractMaster.getLiabilityState());
					} else if (contractMaster.getData().get("service_code") != null
							&& "18".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("18");// 查询类型【手工单承保】
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(18));
					} else if (contractMaster.getData().get("service_code") != null
							&& "17".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("17");// 保单最新状态【过犹豫期生效】
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(17));
					} else if (contractMaster.getData().get("service_code") != null
							&& "27".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("27");// 保单最新状态【犹豫期】
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(27));
					} else if (contractMaster.getData().get("service_code") != null
							&& "28".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("28");// 保单最新状态【当日撤单】
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(28));
					} else if (contractMaster.getData().get("service_code") != null
							&& "10".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("10");// 保单最新状态【拒保】
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(10));
					} else if (contractMaster.getData().get("service_code") != null
							&& "19".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("19");// 保单最新状态【拒保】
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(19));
					} else if (contractMaster.getData().get("service_code") != null
							&& "22".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("22");// 保单最新状态【缴费信息】
						// 银行代码
						Object bankCodeObj = contractMaster.getData().get("bank_code");
						// 缴费方式
						Object payModeObj = contractMaster.getData().get("pay_mode");
						// 缴费方式 0-本行转账 1-他行转账 2-现金缴费 9-其它
						if ("06".equals(queryPolicyInfoInputBO.getServiceBank())) { // 民生银行缴费方式转换
							if (payModeObj != null && "10".equals(payModeObj.toString())) {
								policyQueryDelayedPasBO.setPayMode("2");
							} else if (bankCodeObj != null
									&& (queryPolicyInfoInputBO.getServiceBank().equals(bankCodeObj.toString())
											|| "8600060".equals(bankCodeObj.toString())
											|| "305".equals(bankCodeObj.toString()))) { // 若续期实收扣款民生银行代码为8600060或305，或首期，则视为本行转账
								policyQueryDelayedPasBO.setPayMode("0");
							} else if (payModeObj != null && ("31".equals(payModeObj.toString())
									|| "32".equals(payModeObj.toString()) || "40".equals(payModeObj.toString())
									|| "41".equals(payModeObj.toString()))) { // 如民生银行代码不为8600060或305，缴费方式在31，32，40，41，为他行转账
								policyQueryDelayedPasBO.setPayMode("1");
							} else {
								policyQueryDelayedPasBO.setPayMode("9");
							}
						}
						// 缴费类型 0：首期 1：续期 2：追加
						if (contractMaster.getData().get("pay_type") != null) {
							policyQueryDelayedPasBO.setPayType(contractMaster.getData().get("pay_type").toString());
						}
						// 卡号
						if (contractMaster.getData().get("bank_account") != null) {
							policyQueryDelayedPasBO
									.setPayAccount(contractMaster.getData().get("bank_account").toString());
						}
						// 本次缴费金额
						if (contractMaster.getData().get("fee_amount") != null) {
							policyQueryDelayedPasBO
									.setPayAmount((BigDecimal) contractMaster.getData().get("fee_amount"));
						}
						// 缴费次数(首次缴费为“1”，后续每次缴费，缴费次数“+1”)
						if (contractMaster.getData().get("total_pay_count") != null) {
							policyQueryDelayedPasBO.setTotalPayCount(
									Integer.valueOf(contractMaster.getData().get("total_pay_count").toString()));
						}
						// 累计缴费金额 承保+续期+追加
						if (contractMaster.getData().get("total_pay_amount") != null) {
							policyQueryDelayedPasBO
									.setTotalPayAmount((BigDecimal) contractMaster.getData().get("total_pay_amount"));
						}
						// 续期缴费期数(首期时值为“1”。续期时值为续缴期数【第一次续期时，值为2】。追加时值为“0”)
						if (contractMaster.getData().get("renewal_count") != null) {
							policyQueryDelayedPasBO.setRenewalCount(
									Integer.valueOf(contractMaster.getData().get("renewal_count").toString()));
						}
						// 累计续期缴费金额(承保+续期)
						if (contractMaster.getData().get("renewal_pay_amount") != null) {
							policyQueryDelayedPasBO.setRenewalPayAmount(
									(BigDecimal) contractMaster.getData().get("renewal_pay_amount"));
						}
						// 缴费日期
						if (contractMaster.getData().get("finish_time") != null) {
							policyQueryDelayedPasBO
									.setLiabilityChgDate((Date) contractMaster.getData().get("finish_time"));
						}

					} else if (contractMaster.getData().get("service_code") != null && queryTypeList != null
							&& queryTypeList.contains("23")
							&& ("AG".equals(contractMaster.getData().get("service_code").toString())
									|| "PG".equals(contractMaster.getData().get("service_code").toString())
									|| "AI".equals(contractMaster.getData().get("service_code").toString()))
							&& !"PME".equals(contractMaster.getData().get("service_code").toString())) {
						// 若账户满期终止，并且操作了满期金领取，则返回满期已给付
						if (contractMaster.getEndCause() != null
								&& Constants.END_CAUSE_MATURITY.equals(contractMaster.getEndCause())
								&& "AG".equals(contractMaster.getData().get("service_code").toString())) {
							policyQueryDelayedPasBO.setLifeCycle("076030");// 满期已给付
						} else {
							PolicyAccountPO policyAccountPO11 = new PolicyAccountPO();
							policyAccountPO11.setPolicyId(contractMaster.getPolicyId());
							policyAccountPO11.setAccountType(Constants.POLICY_ACCOUNT_TYPE_11); // 累积生息账户
							policyAccountPO11 = policyAccountDao.findPolicyAccount(policyAccountPO11);

							PolicyAccountPO policyAccountPO2 = new PolicyAccountPO();
							policyAccountPO2.setPolicyId(contractMaster.getPolicyId());
							policyAccountPO2.setAccountType(Constants.POLICY_ACCOUNT_TYPE_ONE); // 现金红利账户
							policyAccountPO2 = policyAccountDao.findPolicyAccount(policyAccountPO2);

							ContractInvestPO contractInvestPO = new ContractInvestPO();
							contractInvestPO.setPolicyId(contractMaster.getPolicyId());
							List<ContractInvestPO> contractInvests = contractInvestDao
									.findAllByPolicyId(contractInvestPO);

							PayDuePO payDuePO = new PayDuePO();
							payDuePO.setPolicyId(contractMaster.getPolicyId());
							payDuePO.setFeeStatus("00");
							int payDueCount = payDueDao.findPayDueTotal(payDuePO);

							if ((policyAccountPO11 == null || policyAccountPO11.getInterestCapital() == null
									|| policyAccountPO11.getInterestCapital().compareTo(new BigDecimal("0")) < 1)
									&& (policyAccountPO2 == null || policyAccountPO11.getInterestCapital() == null
											|| policyAccountPO2.getInterestCapital().compareTo(new BigDecimal("0")) < 1)
									&& (CollectionUtilEx.isEmpty(contractInvests) || contractInvests.get(0)
											.getInterestCapital().compareTo(new BigDecimal("0")) < 1)
									&& payDueCount <= 0) {
								policyQueryDelayedPasBO.setLifeCycle("076030");// 满期已给付
							} else {
								policyQueryDelayedPasBO.setLifeCycle("076031");// 满期未给付
							}
						}
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(23));
						policyQueryDelayedPasBO.setQueryType("23");// 生命周期状态

					} else if (contractMaster.getData().get("service_code") != null
							&& "PME".equals(contractMaster.getData().get("service_code").toString())) {
						// 查询未发生保全变更，但已经满期终止的数据
						// 若账户满期终止，则返回满期已给付【判断是否有满期金计划，若有计划，则返回满期未给付，否则返回满期无给付】
						PayPlanPO payPlanPO = new PayPlanPO();
						payPlanPO.setPolicyId(contractMaster.getPolicyId());
						payPlanPO.setPayPlanType(Constants.PAY_PLAN_TYPE_MATURITYBENEFIT);
						List<PayPlanPO> payPlanList = payPlanDao.findAllPayPlan(payPlanPO);
						if (CollectionUtilEx.isNotEmpty(payPlanList)) {
							policyQueryDelayedPasBO.setLifeCycle("076031");// 满期未给付
						} else {
							policyQueryDelayedPasBO.setLifeCycle("076032");// 满期无给付
						}

						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(23));
						policyQueryDelayedPasBO.setQueryType("23");// 生命周期状态
					} else if (contractMaster.getData().get("service_code") != null
							&& "25".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("25"); // 万能险账户变动
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(25));
					}
					contractMaster.setBigDecimal("hesitate_flag", hesitateFlag);

					// 查询类型(追加金额)
					if (contractMaster.getData().get("service_code") != null
							&& "AM".equals(contractMaster.getData().get("service_code").toString())) {
						// 查询追加保费
						PremArapPO premArap = new PremArapPO();
						premArap.setBusinessCode(contractMaster.getData().get("business_code").toString());
						premArap = queryPolicyInfoDao.findPolicyPayPrem(premArap);
						if (premArap.getFeeAmount() != null) {
							policyQueryDelayedPasBO.setSurrenderAmount(premArap.getFeeAmount());
						}
						policyQueryDelayedPasBO.setQueryType("20");// 追加金额
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(20));
					}

					// 查询类型(部分领取(减保))
					if (contractMaster.getData().get("service_code") != null
							&& ("PG".equals(contractMaster.getData().get("service_code").toString())
									|| "PT".equals(contractMaster.getData().get("service_code").toString())
									|| "AI".equals(contractMaster.getData().get("service_code").toString()))
							&& queryTypeList != null && queryTypeList.contains("21")) {
						// 部分领取(减保)
						PremArapPO premArap = new PremArapPO();
						premArap.setBusinessCode(contractMaster.getData().get("business_code").toString());
						premArap = queryPolicyInfoDao.findPolicyPayPrem(premArap);
						if (premArap.getFeeAmount() != null) {
							policyQueryDelayedPasBO.setSurrenderAmount(premArap.getFeeAmount());
						}
						policyQueryDelayedPasBO.setQueryType("21");// 部分领取(减保)
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(21));
					}

					if (queryTypeList != null && queryTypeList.contains("13") && contractMaster.getEndCause() != null) {
						if (Constants.END_CAUSE_CANCEL.equals(contractMaster.getEndCause())
								&& (queryTypeList.contains("02") || queryTypeList.contains("03"))) {

						} else {
							policyQueryDelayedPasBO.setQueryType("13");
							policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(13));// 保单最新状态【终止】
						}
					}

					if (queryTypeList != null && queryTypeList.contains("16")
							&& contractMaster.getData().get("service_code") != null
							&& ("PA".equals(contractMaster.getData().get("service_code").toString())
									|| "AM".equals(contractMaster.getData().get("service_code").toString())
									|| "FM".equals(contractMaster.getData().get("service_code").toString())
									|| "NS".equals(contractMaster.getData().get("service_code").toString())
									|| "PT".equals(contractMaster.getData().get("service_code").toString())
									|| "RA".equals(contractMaster.getData().get("service_code").toString())
									|| "RE".equals(contractMaster.getData().get("service_code").toString())
									|| "RG".equals(contractMaster.getData().get("service_code").toString())
									|| "XD".equals(contractMaster.getData().get("service_code").toString())
									|| "SR".equals(contractMaster.getData().get("service_code").toString())
									|| "CM".equals(contractMaster.getData().get("service_code").toString())
									|| "HI".equals(contractMaster.getData().get("service_code").toString())
									|| "RB".equals(contractMaster.getData().get("service_code").toString())
									|| "RL".equals(contractMaster.getData().get("service_code").toString()))) {
						policyQueryDelayedPasBO.setQueryType("16");
						policyQueryDelayedPasBO.setServiceCode(contractMaster.getData().get("service_code") == null ? ""
								: contractMaster.getData().get("service_code").toString());
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(16));// 保单最新状态【保全】
						if ("RE".equals(contractMaster.getData().get("service_code").toString())
								|| "SR".equals(contractMaster.getData().get("service_code").toString())) {
							policyQueryDelayedPasBO.setLifeCycle("076035");// 保单复效
						}
					}
					
					//续期缴费使用
					//续期缴费使用
					if (queryTypeList != null && queryTypeList.contains("04")
							&& contractMaster.getData().get("service_code") != null&&"PREF".equals(contractMaster.getData().get("service_code").toString())) {
						policyQueryDelayedPasBO.setQueryType("04");
						policyQueryDelayedPasBO.setServiceCode(contractMaster.getData().get("service_code") == null ? ""
								: contractMaster.getData().get("service_code").toString());
					}
					
					if (contractMaster.getData().get("service_code") != null) {
						if ("CS".equals(contractMaster.getData().get("service_code").toString())) {
							policyQueryDelayedPasBO.setQueryType("12");
							policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(12));// 保单最新状态【质押状态】
						}
						if ("CP".equals(contractMaster.getData().get("service_code").toString())) {
							policyQueryDelayedPasBO.setQueryType("14");
							policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(14));// 保单最新状态【正常】
						}
						if ("LN".equals(contractMaster.getData().get("service_code").toString())) {
							policyQueryDelayedPasBO.setQueryType("11");
							policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(11));// 保单最新状态【贷款状态】
						}
						if ("RF".equals(contractMaster.getData().get("service_code").toString())) {
							policyQueryDelayedPasBO.setQueryType("14");
							policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(14));// 保单最新状态【正常】
						}
//                    	PolicyChangePO policyChangePO = new PolicyChangePO();
//                    	policyChangePO.setPolicyId(contractMaster.getPolicyId());
//                    	policyChangePO.setServiceCode("CP");
//						int i = policyChangeDao.findPolicyChangeTotalByCon(policyChangePO);
//						if (i > 0) {
//						} else {
//						}
					}
					if (contractMaster.getData().get("service_code") != null
							&& "LN".equals(contractMaster.getData().get("service_code").toString())) {
						PolicyAccountPO policyAccountPO = new PolicyAccountPO();
						policyAccountPO.setPolicyId(contractMaster.getPolicyId());
						policyAccountPO.setAccountType(Constants.POLICY_ACCOUNT_TYPE__4);// 贷款
						policyAccountPO = policyAccountDao.findPolicyAccount(policyAccountPO);
						if (policyAccountPO.getInterestCapital() != null) {
							if (policyAccountPO.getInterestCapital().compareTo(BigDecimal.valueOf(0)) > 0) {
								policyQueryDelayedPasBO.setQueryType("11");
								policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(11));// 保单最新状态【保单贷款】
							} else {
								policyQueryDelayedPasBO.setQueryType("14");
								policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(14));// 保单最新状态【正常】
							}
						}
					}
					if (queryTypeList != null && queryTypeList.contains("15")
							&& Constants.LIABILITY_STATE_INEFFECT.equals(contractMaster.getLiabilityState())) {
						policyQueryDelayedPasBO.setQueryType("15");
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(15));// 保单最新状态【中止】
					}

					if (contractMaster.getEndCause() != null && "15".equals(contractMaster.getEndCause())) {
						policyQueryDelayedPasBO.setQueryType("26");
						policyQueryDelayedPasBO.setNewLiabilityState(new BigDecimal(26));// 保单最新状态【开门红撤单】
						policyQueryDelayedPasBO.setLiabilityChgDate(contractMaster.getExpiryDate());// 开门红撤单时间
					}

					// 查询保单账户价值(针对投连万能险)
					ContractInvestPO contractInvestPO = new ContractInvestPO();
					contractInvestPO.setPolicyId(contractMaster.getPolicyId());
					List<ContractInvestPO> contractInvests = contractInvestDao.findAllByPolicyId(contractInvestPO);
					if (CollectionUtilEx.isNotEmpty(contractInvests)) {
						logger.info("【非实时保单状态查询-保单库】拼接保单账户价值 start");
						if (Constants.INVEST_ACCOUNT_TYPE_ILP.equals(contractInvests.get(0).getInvestAccountType())) {
							// 投连险
							// 查询账户当天的卖出价
							InvestUnitPricePO investUnitPricePO = new InvestUnitPricePO();
							investUnitPricePO.setInvestAccountCode(contractInvests.get(0).getAccountCode());
							investUnitPricePO.set("end_date", BatchDateUtil.getCurrentDate());
							List<InvestUnitPricePO> investUnitPricePOList = investUnitPriceDao
									.findAllInvestUnitPrice(investUnitPricePO);
							if (CollectionUtilEx.isNotEmpty(investUnitPricePOList)) {
								// 账户价值=单位数*卖出价
								policyQueryDelayedPasBO.setAccountValue(contractInvests.get(0).getAccumUnits()
										.multiply(investUnitPricePOList.get(0).getBidPrice())
										.setScale(2, RoundingMode.HALF_UP));
							}
						} else {
							// 万能险
							policyQueryDelayedPasBO.setAccountValue(contractInvests.get(0).getInterestCapital());
						}
						logger.info("【非实时保单状态查询-保单库】拼接保单账户价值 end");
					}

					// 若返回的queryType字段包含退保场景，则校验保单主表是否为退保终止，否则不予返回
					if (policyQueryDelayedPasBO.getQueryType() != null
							&& ("02".equals(policyQueryDelayedPasBO.getQueryType())
									|| "03".equals(policyQueryDelayedPasBO.getQueryType().contains("03")))) {
						// 判断保单状态是否为终止，如果不是终止，直接跳出此循环
						if (policyQueryDelayedPasBO.getLiabilityState().compareTo(new BigDecimal("3")) != 0) {
							continue;
						}
					}

					// 查询累计生存金、满期保险金
					PayPlanPO payPlanPO = new PayPlanPO();
					payPlanPO.setPolicyId(contractMaster.getPolicyId());
					List<PayPlanPO> payPlanList = payPlanDao.findAllPayPlan(payPlanPO);
					if (CollectionUtilEx.isNotEmpty(payPlanList)) {
						logger.info("【非实时保单状态查询-保单库】拼接生存金、满期金 start");
						BigDecimal survivalAmount = new BigDecimal(0);// 生存金
						BigDecimal maturityAmount = new BigDecimal(0);// 满期金
						for (PayPlanPO payPlan : payPlanList) {
							// 生存金
							if (Constants.PAY_PLAN_TYPE_SURVIVALBENEFIT.equals(payPlan.getPayPlanType())
									&& payPlan.getInstalmentAmount() != null
									&& payPlan.getInstalmentAmount().compareTo(new BigDecimal(0)) > 0) {
								survivalAmount = survivalAmount.add(payPlan.getInstalmentAmount());
							}
							// 满期金
							if (Constants.PAY_PLAN_TYPE_MATURITYBENEFIT.equals(payPlan.getPayPlanType())
									&& contractMaster.getData().get("service_code") != null
									&& "AG".equals(contractMaster.getData().get("service_code").toString())
									&& payPlan.getInstalmentAmount() != null
									&& payPlan.getInstalmentAmount().compareTo(new BigDecimal(0)) > 0) {
								maturityAmount = maturityAmount.add(payPlan.getInstalmentAmount());
								maturityCount = maturityCount + 1;// 满期总记录数
								totalMaturityAmount = totalMaturityAmount.add(maturityAmount);// 满期总金额
							}
							// 针对查询类型为13，并且满期终止且含有满期金额的保单，返回满期金
							if (Constants.PAY_PLAN_TYPE_MATURITYBENEFIT.equals(payPlan.getPayPlanType())
									&& CollectionUtilEx.isNotEmpty(queryTypeList) && queryTypeList.contains("13")
									&& payPlan.getInstalmentAmount() != null
									&& payPlan.getInstalmentAmount().compareTo(new BigDecimal(0)) > 0) {
								maturityAmount = maturityAmount.add(payPlan.getInstalmentAmount());
								maturityCount = maturityCount + 1;// 满期总记录数
								totalMaturityAmount = totalMaturityAmount.add(maturityAmount);// 满期总金额
							}
						}
						policyQueryDelayedPasBO.setSurvivalAmount(survivalAmount);// 生存金
						policyQueryDelayedPasBO.setMaturityAmount(maturityAmount);// 满期金
						logger.info("【非实时保单状态查询-保单库】拼接生存金、满期金 end");
					}
					// 查询上次缴费日
					PremArapPO premArapPO = new PremArapPO();
					premArapPO.setPolicyCode(contractMaster.getPolicyCode());
					premArapPO.setDueTime(queryPolicyInfoInputBO.getBatchEndDate());
					premArapPO = queryPolicyInfoDao.findContractDueTime(premArapPO);
					if (premArapPO != null) {
						policyQueryDelayedPasBO.setPayDueDate(premArapPO.getDueTime());// 上次缴费日
					}
					// 查询首期缴费金额
					PremArapPO premArap = new PremArapPO();
					premArap.setPolicyCode(contractMaster.getPolicyCode());
					premArap.setDueTime(queryPolicyInfoInputBO.getBatchEndDate());
					premArap = queryPolicyInfoDao.findPolicyInitialPrem(premArap);
					policyQueryDelayedPasBO.setInitialPrem(premArap.getFeeAmount());

					// 查询最近缴费账号
					PremPO premPO = new PremPO();
					premPO.setPolicyCode(contractMaster.getPolicyCode());
					premPO = premDao.findLastAccountByPolicyCode(premPO);
					policyQueryDelayedPasBO.setLastAccount(premPO.getBankAccount());

					logger.info("【非实时保单状态查询-保单库】拼接保单基本信息 end");

					// 查询险种、责任组信息
					ContractProductPO contractProductPO = new ContractProductPO();
					contractProductPO.setPolicyCode(contractMaster.getPolicyCode());
					List<ContractProductPO> contractProductPOList = queryPolicyInfoDao
							.findContractBusiProduct(contractProductPO);
					if (CollectionUtilEx.isNotEmpty(contractProductPOList)) {
						// 最多返回五个险种
						logger.info("【非实时保单状态查询-保单库】拼接险种信息 start");
						for (int i = 0; i < contractProductPOList.size(); i++) {
							if (contractProductPOList.get(i).getData().get("product_category") != null) {
								// 主险险种类型
								if ("10001".equals(contractProductPOList.get(i).get("product_category").toString())) {
									// 保单满期日 (主险对应的满期日)
									if (contractProductPOList.get(i).getMaturityDate() != null) {
										policyQueryDelayedPasBO
												.setMaturityDate(contractProductPOList.get(i).getMaturityDate());
									}
									if ("20001"
											.equals(contractProductPOList.get(i).get("product_category1").toString())) {
										if ("30002".equals(
												contractProductPOList.get(i).get("product_category2").toString())) {
											policyQueryDelayedPasBO.setYbtField2("003");// 意外
										} else if ("30003".equals(
												contractProductPOList.get(i).get("product_category2").toString())) {
											policyQueryDelayedPasBO.setYbtField2("002");// 健康
										} else if ("30004".equals(
												contractProductPOList.get(i).get("product_category2").toString())) {
											policyQueryDelayedPasBO.setYbtField2("005");// 年金
										} else {
											policyQueryDelayedPasBO.setYbtField2("010");// 其他
										}
									} else if ("20002"
											.equals(contractProductPOList.get(i).get("product_category1").toString())) {
										policyQueryDelayedPasBO.setYbtField2("001");// 分红
									} else if ("20003"
											.equals(contractProductPOList.get(i).get("product_category1").toString())) {
										policyQueryDelayedPasBO.setYbtField2("004");// 万能
									} else if ("20004"
											.equals(contractProductPOList.get(i).get("product_category1").toString())) {
										policyQueryDelayedPasBO.setYbtField2("006");// 投连
									} else {
										policyQueryDelayedPasBO.setYbtField2("010");// 其他
									}
								}
							}
							if (i == 0) {
								logger.info("【非实时保单状态查询-保单库】拼接险种1信息 start");
								if (contractProductPOList.get(i).getData().get("product_code_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdCode1(
											contractProductPOList.get(i).getData().get("product_code_sys").toString());// 险种代码1
								}
								if (contractProductPOList.get(i).getData().get("product_name_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdName1(
											contractProductPOList.get(i).getData().get("product_name_sys").toString());// 险种名称1
								}
								if (contractProductPOList.get(i).getData().get("product_category") != null) {
									policyQueryDelayedPasBO.setProductCategory1(
											contractProductPOList.get(i).getData().get("product_category").toString());// 主附险标识1
								}
								if (contractProductPOList.get(i).getData().get("coverage_period") != null) {
									policyQueryDelayedPasBO.setCoveragePeriod1(
											contractProductPOList.get(i).getData().get("coverage_period").toString());// 保障年期类型1
								}
								if (contractProductPOList.get(i).getData().get("coverage_year") != null) {
									policyQueryDelayedPasBO.setCoverageYear1(
											(BigDecimal) contractProductPOList.get(i).getData().get("coverage_year"));// 保障年期1
								}
								if (contractProductPOList.get(i).getData().get("charge_period") != null) {
									policyQueryDelayedPasBO.setChargePeriod1(
											contractProductPOList.get(i).getData().get("charge_period").toString());// 缴费年期类型1
								}
								if (contractProductPOList.get(i).getData().get("charge_year") != null) {
									policyQueryDelayedPasBO.setChargeYear1(
											(BigDecimal) contractProductPOList.get(i).getData().get("charge_year"));// 缴费年期1
								}
								if (contractProductPOList.get(i).getData().get("bonus_mode") != null) {
									policyQueryDelayedPasBO.setBonusMode1(
											(BigDecimal) contractProductPOList.get(i).getData().get("bonus_mode"));// 红利领取方式1
								}
								if (contractProductPOList.get(i).getData().get("prem_freq") != null) {
									policyQueryDelayedPasBO.setPremFreq1(
											(BigDecimal) contractProductPOList.get(i).getData().get("prem_freq"));// 缴费方式1
								}
								if (contractProductPOList.get(i).getData().get("initial_amount") != null) {
									policyQueryDelayedPasBO.setInitialAmount1(
											(BigDecimal) contractProductPOList.get(i).getData().get("initial_amount"));// 投保金额1
								}
								if (contractProductPOList.get(i).getData().get("field1") != null) {
									policyQueryDelayedPasBO.setSafeGuardPlan1(
											contractProductPOList.get(i).getData().get("field1").toString());// 保障计划1
								}
								if (contractProductPOList.get(i).getData().get("renew") != null) {
									policyQueryDelayedPasBO.setRenew1(
											(BigDecimal) contractProductPOList.get(i).getData().get("renew")); // 是否自动续保1
								}
								if (contractProductPOList.get(i).getData().get("unit") != null) {
									policyQueryDelayedPasBO
											.setUnit1((BigDecimal) contractProductPOList.get(i).getData().get("unit"));// 份数1
								}
								if (contractProductPOList.get(i).getData().get("std_prem_af") != null) {
									policyQueryDelayedPasBO.setStdPremAf1(
											(BigDecimal) contractProductPOList.get(i).getData().get("std_prem_af"));// 期交保费1
								}
								if (contractProductPOList.get(i).getData().get("total_prem_af") != null) {
									policyQueryDelayedPasBO.setTotalPremAf1(
											(BigDecimal) contractProductPOList.get(i).getData().get("total_prem_af"));// 总保费1
								}
								if (contractProductPOList.get(i).getData().get("initial_validate_date") != null) {
									// 险种原始生效日期1
									policyQueryDelayedPasBO.setInitialValidateDate1(
											DateUtilsEx.formatToDate(contractProductPOList.get(i).getData()
													.get("initial_validate_date").toString(), "yyyy-MM-dd"));
								}
								// 产品类型1
								policyQueryDelayedPasBO
										.setProductType1(contractProductPOList.get(i).get("category_name2").toString());
								// 是否理财型产品1
								if ("2".equals(contractProductPOList.get(i).get("busi_prod_type3_code").toString())) {
									policyQueryDelayedPasBO.setIsmmProduct1("1"); // 非保险合同-理财型产品
								} else {
									policyQueryDelayedPasBO.setIsmmProduct1("0"); // 保险合同-非理财型产品
								}
								logger.info("【非实时保单状态查询-保单库】拼接险种1信息 end");
							}

							if (i == 1) {
								logger.info("【非实时保单状态查询-保单库】拼接险种2信息 start");
								if (contractProductPOList.get(i).getData().get("product_code_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdCode2(
											contractProductPOList.get(i).getData().get("product_code_sys").toString());// 险种代码2
								}
								if (contractProductPOList.get(i).getData().get("product_name_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdName2(
											contractProductPOList.get(i).getData().get("product_name_sys").toString());// 险种名称2
								}
								if (contractProductPOList.get(i).getData().get("product_category") != null) {
									policyQueryDelayedPasBO.setProductCategory2(
											contractProductPOList.get(i).getData().get("product_category").toString());// 主附险标识2
								}
								if (contractProductPOList.get(i).getData().get("coverage_period") != null) {
									policyQueryDelayedPasBO.setCoveragePeriod2(
											contractProductPOList.get(i).getData().get("coverage_period").toString());// 保障年期类型2
								}
								if (contractProductPOList.get(i).getData().get("coverage_year") != null) {
									policyQueryDelayedPasBO.setCoverageYear2(
											(BigDecimal) contractProductPOList.get(i).getData().get("coverage_year"));// 保障年期2
								}
								if (contractProductPOList.get(i).getData().get("charge_period") != null) {
									policyQueryDelayedPasBO.setChargePeriod2(
											contractProductPOList.get(i).getData().get("charge_period").toString());// 缴费年期类型2
								}
								if (contractProductPOList.get(i).getData().get("charge_year") != null) {
									policyQueryDelayedPasBO.setChargeYear2(
											(BigDecimal) contractProductPOList.get(i).getData().get("charge_year"));// 缴费年期2
								}
								if (contractProductPOList.get(i).getData().get("bonus_mode") != null) {
									policyQueryDelayedPasBO.setBonusMode2(
											(BigDecimal) contractProductPOList.get(i).getData().get("bonus_mode"));// 红利领取方式2
								}
								if (contractProductPOList.get(i).getData().get("prem_freq") != null) {
									policyQueryDelayedPasBO.setPremFreq2(
											(BigDecimal) contractProductPOList.get(i).getData().get("prem_freq"));// 缴费方式2
								}
								if (contractProductPOList.get(i).getData().get("initial_amount") != null) {
									policyQueryDelayedPasBO.setInitialAmount2(
											(BigDecimal) contractProductPOList.get(i).getData().get("initial_amount"));// 投保金额2
								}

								if (contractProductPOList.get(i).getData().get("filed1") != null) {
									policyQueryDelayedPasBO.setSafeGuardPlan2(
											contractProductPOList.get(i).getData().get("filed1").toString());// 保障计划2
								}

								if (contractProductPOList.get(i).getData().get("renew") != null) {
									policyQueryDelayedPasBO.setRenew2(
											(BigDecimal) contractProductPOList.get(i).getData().get("renew")); // 是否自动续保2
								}

								if (contractProductPOList.get(i).getData().get("unit") != null) {
									policyQueryDelayedPasBO
											.setUnit2((BigDecimal) contractProductPOList.get(i).getData().get("unit"));// 份数2
								}
								if (contractProductPOList.get(i).getData().get("std_prem_af") != null) {
									policyQueryDelayedPasBO.setStdPremAf2(
											(BigDecimal) contractProductPOList.get(i).getData().get("std_prem_af"));// 期交保费2
								}
								if (contractProductPOList.get(i).getData().get("total_prem_af") != null) {
									policyQueryDelayedPasBO.setTotalPremAf2(
											(BigDecimal) contractProductPOList.get(i).getData().get("total_prem_af"));// 总保费2
								}
								if (contractProductPOList.get(i).getData().get("initial_validate_date") != null) {
									// 险种原始生效日期2
									policyQueryDelayedPasBO.setInitialValidateDate2(
											DateUtilsEx.formatToDate(contractProductPOList.get(i).getData()
													.get("initial_validate_date").toString(), "yyyy-MM-dd"));
								}
								// 产品类型2
								policyQueryDelayedPasBO
										.setProductType2(contractProductPOList.get(i).get("category_name2").toString());
								logger.info("【非实时保单状态查询-保单库】拼接险种2信息 end");
							}

							if (i == 2) {
								logger.info("【非实时保单状态查询-保单库】拼接险种3信息 start");
								if (contractProductPOList.get(i).getData().get("product_code_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdCode3(
											contractProductPOList.get(i).getData().get("product_code_sys").toString());// 险种代码3
								}
								if (contractProductPOList.get(i).getData().get("product_name_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdName3(
											contractProductPOList.get(i).getData().get("product_name_sys").toString());// 险种名称3
								}
								if (contractProductPOList.get(i).getData().get("product_category") != null) {
									policyQueryDelayedPasBO.setProductCategory3(
											contractProductPOList.get(i).getData().get("product_category").toString());// 主附险标识3
								}
								if (contractProductPOList.get(i).getData().get("coverage_period") != null) {
									policyQueryDelayedPasBO.setCoveragePeriod3(
											contractProductPOList.get(i).getData().get("coverage_period").toString());// 保障年期类型3
								}
								if (contractProductPOList.get(i).getData().get("coverage_year") != null) {
									policyQueryDelayedPasBO.setCoverageYear3(
											(BigDecimal) contractProductPOList.get(i).getData().get("coverage_year"));// 保障年期3
								}
								if (contractProductPOList.get(i).getData().get("charge_period") != null) {
									policyQueryDelayedPasBO.setChargePeriod3(
											contractProductPOList.get(i).getData().get("charge_period").toString());// 缴费年期类型3
								}
								if (contractProductPOList.get(i).getData().get("charge_year") != null) {
									policyQueryDelayedPasBO.setChargeYear3(
											(BigDecimal) contractProductPOList.get(i).getData().get("charge_year"));// 缴费年期3
								}

								if (contractProductPOList.get(i).getData().get("filed1") != null) {
									policyQueryDelayedPasBO.setSafeGuardPlan3(
											contractProductPOList.get(i).getData().get("filed1").toString());// 保障计划3
								}

								if (contractProductPOList.get(i).getData().get("renew") != null) {
									policyQueryDelayedPasBO.setRenew3(
											(BigDecimal) contractProductPOList.get(i).getData().get("renew")); // 是否自动续保3
								}

								if (contractProductPOList.get(i).getData().get("bonus_mode") != null) {
									policyQueryDelayedPasBO.setBonusMode3(
											(BigDecimal) contractProductPOList.get(i).getData().get("bonus_mode"));// 红利领取方式3
								}
								if (contractProductPOList.get(i).getData().get("prem_freq") != null) {
									policyQueryDelayedPasBO.setPremFreq3(
											(BigDecimal) contractProductPOList.get(i).getData().get("prem_freq"));// 缴费方式3
								}
								if (contractProductPOList.get(i).getData().get("initial_amount") != null) {
									policyQueryDelayedPasBO.setInitialAmount3(
											(BigDecimal) contractProductPOList.get(i).getData().get("initial_amount"));// 投保金额3
								}
								if (contractProductPOList.get(i).getData().get("unit") != null) {
									policyQueryDelayedPasBO
											.setUnit3((BigDecimal) contractProductPOList.get(i).getData().get("unit"));// 份数3
								}
								if (contractProductPOList.get(i).getData().get("std_prem_af") != null) {
									policyQueryDelayedPasBO.setStdPremAf3(
											(BigDecimal) contractProductPOList.get(i).getData().get("std_prem_af"));// 期交保费3
								}
								if (contractProductPOList.get(i).getData().get("total_prem_af") != null) {
									policyQueryDelayedPasBO.setTotalPremAf3(
											(BigDecimal) contractProductPOList.get(i).getData().get("total_prem_af"));// 总保费3
								}
								if (contractProductPOList.get(i).getData().get("initial_validate_date") != null) {
									// 险种原始生效日期3
									policyQueryDelayedPasBO.setInitialValidateDate3(
											DateUtilsEx.formatToDate(contractProductPOList.get(i).getData()
													.get("initial_validate_date").toString(), "yyyy-MM-dd"));
								}
								// 产品类型3
								policyQueryDelayedPasBO
										.setProductType3(contractProductPOList.get(i).get("category_name2").toString());
								logger.info("【非实时保单状态查询-保单库】拼接险种3信息 end");
							}

							if (i == 3) {
								logger.info("【非实时保单状态查询-保单库】拼接险种4信息 start");
								if (contractProductPOList.get(i).getData().get("product_code_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdCode4(
											contractProductPOList.get(i).getData().get("product_code_sys").toString());// 险种代码4
								}
								if (contractProductPOList.get(i).getData().get("product_name_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdName4(
											contractProductPOList.get(i).getData().get("product_name_sys").toString());// 险种名称4
								}
								if (contractProductPOList.get(i).getData().get("product_category") != null) {
									policyQueryDelayedPasBO.setProductCategory4(
											contractProductPOList.get(i).getData().get("product_category").toString());// 主附险标识4
								}
								if (contractProductPOList.get(i).getData().get("coverage_period") != null) {
									policyQueryDelayedPasBO.setCoveragePeriod4(
											contractProductPOList.get(i).getData().get("coverage_period").toString());// 保障年期类型4
								}
								if (contractProductPOList.get(i).getData().get("coverage_year") != null) {
									policyQueryDelayedPasBO.setCoverageYear4(
											(BigDecimal) contractProductPOList.get(i).getData().get("coverage_year"));// 保障年期4
								}
								if (contractProductPOList.get(i).getData().get("charge_period") != null) {
									policyQueryDelayedPasBO.setChargePeriod4(
											contractProductPOList.get(i).getData().get("charge_period").toString());// 缴费年期类型4
								}
								if (contractProductPOList.get(i).getData().get("charge_year") != null) {
									policyQueryDelayedPasBO.setChargeYear4(
											(BigDecimal) contractProductPOList.get(i).getData().get("charge_year"));// 缴费年期4
								}
								if (contractProductPOList.get(i).getData().get("bonus_mode") != null) {
									policyQueryDelayedPasBO.setBonusMode4(
											(BigDecimal) contractProductPOList.get(i).getData().get("bonus_mode"));// 红利领取方式4
								}
								if (contractProductPOList.get(i).getData().get("prem_freq") != null) {
									policyQueryDelayedPasBO.setPremFreq4(
											(BigDecimal) contractProductPOList.get(i).getData().get("prem_freq"));// 缴费方式4
								}

								if (contractProductPOList.get(i).getData().get("filed1") != null) {
									policyQueryDelayedPasBO.setSafeGuardPlan3(
											contractProductPOList.get(i).getData().get("filed1").toString());// 保障计划4
								}

								if (contractProductPOList.get(i).getData().get("renew") != null) {
									policyQueryDelayedPasBO.setRenew4(
											(BigDecimal) contractProductPOList.get(i).getData().get("renew")); // 是否自动续保4
								}

								if (contractProductPOList.get(i).getData().get("initial_amount") != null) {
									policyQueryDelayedPasBO.setInitialAmount4(
											(BigDecimal) contractProductPOList.get(i).getData().get("initial_amount"));// 投保金额4
								}
								if (contractProductPOList.get(i).getData().get("unit") != null) {
									policyQueryDelayedPasBO
											.setUnit4((BigDecimal) contractProductPOList.get(i).getData().get("unit"));// 份数4
								}
								if (contractProductPOList.get(i).getData().get("std_prem_af") != null) {
									policyQueryDelayedPasBO.setStdPremAf4(
											(BigDecimal) contractProductPOList.get(i).getData().get("std_prem_af"));// 期交保费4
								}
								if (contractProductPOList.get(i).getData().get("total_prem_af") != null) {
									policyQueryDelayedPasBO.setTotalPremAf4(
											(BigDecimal) contractProductPOList.get(i).getData().get("total_prem_af"));// 总保费4
								}
								if (contractProductPOList.get(i).getData().get("initial_validate_date") != null) {
									// 险种原始生效日期4
									policyQueryDelayedPasBO.setInitialValidateDate4(
											DateUtilsEx.formatToDate(contractProductPOList.get(i).getData()
													.get("initial_validate_date").toString(), "yyyy-MM-dd"));
								}
								// 产品类型4
								policyQueryDelayedPasBO
										.setProductType4(contractProductPOList.get(i).get("category_name2").toString());
								logger.info("【非实时保单状态查询-保单库】拼接险种4信息 end");
							}

							if (i == 4) {
								logger.info("【非实时保单状态查询-保单库】拼接险种5信息 start");
								if (contractProductPOList.get(i).getData().get("product_code_sys") != null) {
									policyQueryDelayedPasBO.setBusiProdCode5(
											contractProductPOList.get(i).getData().get("product_code_sys").toString());// 险种代码5
									if (contractProductPOList.get(i).getData().get("product_name_sys") != null) {
										policyQueryDelayedPasBO.setBusiProdName5(contractProductPOList.get(i).getData()
												.get("product_name_sys").toString());// 险种名称5
									}
									if (contractProductPOList.get(i).getData().get("product_category") != null) {
										policyQueryDelayedPasBO.setProductCategory5(contractProductPOList.get(i)
												.getData().get("product_category").toString());// 主附险标识5
									}
									if (contractProductPOList.get(i).getData().get("coverage_period") != null) {
										policyQueryDelayedPasBO.setCoveragePeriod5(contractProductPOList.get(i)
												.getData().get("coverage_period").toString());// 保障年期类型5
									}
									if (contractProductPOList.get(i).getData().get("coverage_year") != null) {
										policyQueryDelayedPasBO.setCoverageYear5((BigDecimal) contractProductPOList
												.get(i).getData().get("coverage_year"));// 保障年期5
									}
									if (contractProductPOList.get(i).getData().get("charge_period") != null) {
										policyQueryDelayedPasBO.setChargePeriod5(
												contractProductPOList.get(i).getData().get("charge_period").toString());// 缴费年期类型5
									}
									if (contractProductPOList.get(i).getData().get("charge_year") != null) {
										policyQueryDelayedPasBO.setChargeYear5(
												(BigDecimal) contractProductPOList.get(i).getData().get("charge_year"));// 缴费年期5
									}

									if (contractProductPOList.get(i).getData().get("filed1") != null) {
										policyQueryDelayedPasBO.setSafeGuardPlan5(
												contractProductPOList.get(i).getData().get("filed1").toString());// 保障计划5
									}

									if (contractProductPOList.get(i).getData().get("bonus_mode") != null) {
										policyQueryDelayedPasBO.setBonusMode5(
												(BigDecimal) contractProductPOList.get(i).getData().get("bonus_mode"));// 红利领取方式5
									}
									if (contractProductPOList.get(i).getData().get("prem_freq") != null) {
										policyQueryDelayedPasBO.setPremFreq5(
												(BigDecimal) contractProductPOList.get(i).getData().get("prem_freq"));// 缴费方式5
									}
									if (contractProductPOList.get(i).getData().get("initial_amount") != null) {
										policyQueryDelayedPasBO.setInitialAmount5((BigDecimal) contractProductPOList
												.get(i).getData().get("initial_amount"));// 投保金额5
									}

									if (contractProductPOList.get(i).getData().get("filed1") != null) {
										policyQueryDelayedPasBO.setSafeGuardPlan5(
												contractProductPOList.get(i).getData().get("filed1").toString());// 保障计划5
									}

									if (contractProductPOList.get(i).getData().get("renew") != null) {
										policyQueryDelayedPasBO.setRenew5(
												(BigDecimal) contractProductPOList.get(i).getData().get("renew")); // 是否自动续保5
									}

									if (contractProductPOList.get(i).getData().get("unit") != null) {
										policyQueryDelayedPasBO.setUnit5(
												(BigDecimal) contractProductPOList.get(i).getData().get("unit"));// 份数5
									}

									if (contractProductPOList.get(i).getData().get("std_prem_af") != null) {
										policyQueryDelayedPasBO.setStdPremAf5(
												(BigDecimal) contractProductPOList.get(i).getData().get("std_prem_af"));// 期交保费5
									}
									if (contractProductPOList.get(i).getData().get("total_prem_af") != null) {
										policyQueryDelayedPasBO.setTotalPremAf5((BigDecimal) contractProductPOList
												.get(i).getData().get("total_prem_af"));// 总保费5
									}
									if (contractProductPOList.get(i).getData().get("initial_validate_date") != null) {
										// 险种原始生效日期5
										policyQueryDelayedPasBO
												.setInitialValidateDate5(DateUtilsEx.formatToDate(
														contractProductPOList.get(i).getData()
																.get("initial_validate_date").toString(),
														"yyyy-MM-dd"));
									}
									// 产品类型5
									policyQueryDelayedPasBO.setProductType5(
											contractProductPOList.get(i).get("category_name2").toString());
									logger.info("【非实时保单状态查询-保单库】拼接险种5信息 end");
								}
							}
						}
						logger.info("【非实时保单状态查询-保单库】拼接险种信息 end");
					}

					// 查询投保人信息
					CustomerPO customerPO = new CustomerPO();
					PolicyHolderPO policyHolderPO = new PolicyHolderPO();
					policyHolderPO.setPolicyId(contractMaster.getPolicyId());
					policyHolderPO = policyHolderDao.findPolicyHolderByPolicyId(policyHolderPO);
					// 查询客户信息
					if (policyHolderPO != null && policyHolderPO.getCustomerId() != null) {
						logger.info("【非实时保单状态查询-保单库】拼接投保人信息 start");
						customerPO = queryCustomerInfo(policyHolderPO.getCustomerId());
						// 查询地址表
						AddressPO addressPO = new AddressPO();
						if (policyHolderPO != null && policyHolderPO.getAddressId() != null) {
							addressPO = queryAddressInfo(policyHolderPO.getAddressId());
						}

						if (customerPO != null && customerPO.getJobCode() != null) {
							policyQueryDelayedPasBO.setHolderJobCode(customerPO.getJobCode());// 投保人职业代码
						}
						if (customerPO != null && customerPO.getCustomerName() != null) {
							policyQueryDelayedPasBO.setHolderName(customerPO.getCustomerName());// 投保人姓名
						}
						if (customerPO != null && customerPO.getCustomerGender() != null) {
							policyQueryDelayedPasBO.setHolderGender(customerPO.getCustomerGender());// 投保人性别
						}
						if (customerPO != null && customerPO.getCustomerBirthday() != null) {
							policyQueryDelayedPasBO.setHolderBirthday(customerPO.getCustomerBirthday());// 投保人出生日期
						}
						if (customerPO != null && customerPO.getCustomerCertType() != null) {
							policyQueryDelayedPasBO.setHolderCertType(customerPO.getCustomerCertType());// 投保人证件类型代码
						}
						if (customerPO != null && customerPO.getCustomerCertiCode() != null) {
							policyQueryDelayedPasBO.setHolderCertiCode(customerPO.getCustomerCertiCode());// 投保人证件号码
						}
						if (customerPO != null && customerPO.getCustCertStarDate() != null) {
							policyQueryDelayedPasBO.setHolderCerStarDate(customerPO.getCustCertStarDate());// 投保人证件生效日期
						}
						if (customerPO != null && customerPO.getCustCertEndDate() != null) {
							policyQueryDelayedPasBO.setHolderCerEndDate(customerPO.getCustCertEndDate());// 投保人证件失效日期
						}
						if (addressPO != null && addressPO.getAddress() != null) {
							policyQueryDelayedPasBO.setHolderAddress(addressPO.getAddress());// 投保人详细地址内容
							policyQueryDelayedPasBO.setHolderTXAddress(addressPO.getAddress());// 投保人通讯地址
						}
						if (customerPO != null && customerPO.getCountryCode() != null) {
							policyQueryDelayedPasBO.setHolderCountryCode(customerPO.getCountryCode());// 投保人国家地区代码
						}
						if (addressPO != null && addressPO.getPostCode() != null) {
							policyQueryDelayedPasBO.setHolderPostCode(addressPO.getPostCode());// 投保人邮政编码
						}
						if (customerPO != null && customerPO.getOffenUseTel() != null) {
							policyQueryDelayedPasBO.setHolderOffenUseTel(customerPO.getOffenUseTel());// 投保人固定电话
						}
						if (customerPO != null && customerPO.getMobileTel() != null) {
							policyQueryDelayedPasBO.setHolderMobileTel(customerPO.getMobileTel());// 投保人移动电话
						}
						if (customerPO != null && customerPO.getAnnualIncome() != null) {
							policyQueryDelayedPasBO.setHolderAnnualIncome(customerPO.getAnnualIncome());// 投保人年收入额
						}
						if (customerPO != null && customerPO.getAnnualIncome() != null) {
							policyQueryDelayedPasBO.setHolderAnnualIncome(customerPO.getAnnualIncome());// 居民类型代码
						}
						if (customerPO != null && customerPO != null) {
							policyQueryDelayedPasBO.setHolderAnnualIncome(customerPO.getAnnualIncome());// 居民类型代码
						}
						if (customerPO != null && customerPO.getResidentType() != null) {
							policyQueryDelayedPasBO.setHolderResidentType(customerPO.getResidentType());// 居民类型代码
							// 1城镇2农村
						}
						logger.info("【非实时保单状态查询-保单库】拼接投保人信息 end");
					}

					// 查询被保人列表
					InsuredListPO insuredListPO = new InsuredListPO();
					insuredListPO.setPolicyId(contractMaster.getPolicyId());
					List<InsuredListPO> insuredListPOList = insuredListDao.findAllInsuredList(insuredListPO);
					if (CollectionUtilEx.isNotEmpty(insuredListPOList)) {
						logger.info("【非实时保单状态查询-保单库】拼接被保人信息 start");
						if (insuredListPOList.get(0).getRelationToPh() != null) {
							policyQueryDelayedPasBO.setRelationToPh(insuredListPOList.get(0).getRelationToPh());// 投被保人关系
						}
						// 查询客户信息
						CustomerPO insuredCustomerPO = queryCustomerInfo(insuredListPOList.get(0).getCustomerId());
						// 查询地址信息
						AddressPO addressPO = queryAddressInfo(insuredListPOList.get(0).getAddressId());
						if (insuredCustomerPO != null && insuredCustomerPO.getCustomerName() != null) {
							policyQueryDelayedPasBO.setInsuredName(insuredCustomerPO.getCustomerName());// 被保人名称
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getCustomerGender() != null) {
							policyQueryDelayedPasBO.setInsuredGender(insuredCustomerPO.getCustomerGender());// 被保人性别代码
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getCustomerBirthday() != null) {
							policyQueryDelayedPasBO.setInsuredBirthday(insuredCustomerPO.getCustomerBirthday());// 被保人出生日期
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getCustomerCertiCode() != null) {
							policyQueryDelayedPasBO.setInsuredCertiCode(insuredCustomerPO.getCustomerCertiCode());// 被保人证件号码
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getCustomerCertType() != null) {
							policyQueryDelayedPasBO.setInsuredCertType(insuredCustomerPO.getCustomerCertType());// 被保人证件类型代码
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getCustCertStarDate() != null) {
							policyQueryDelayedPasBO.setInsuredCerStarDate(insuredCustomerPO.getCustCertStarDate());// 被保人证件生效日期
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getCustCertEndDate() != null) {
							policyQueryDelayedPasBO.setInsuredCerEndDate(insuredCustomerPO.getCustCertEndDate());// 被保人证件失效日期
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getCountryCode() != null) {
							policyQueryDelayedPasBO.setInsuredCountryCode(insuredCustomerPO.getCountryCode());// 被保人国家地区代码
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getJobCode() != null) {
							policyQueryDelayedPasBO.setInsuredJobCode(insuredCustomerPO.getJobCode());// 被保人职业代码
						}
						if (addressPO != null && addressPO.getAddress() != null) {
							policyQueryDelayedPasBO.setInsuredAddress(addressPO.getAddress());// 被保人详细地址内容
						}
						if (addressPO != null && addressPO.getPostCode() != null) {
							policyQueryDelayedPasBO.setInsuredPostCode(addressPO.getPostCode());// 被保人邮政编码
						}
						if (insuredCustomerPO != null && insuredCustomerPO.getSociSecu() != null) {
							policyQueryDelayedPasBO.setInsuredSociSecu(insuredCustomerPO.getSociSecu());// 被保人社保标识
																										// update
																										// about 99789
						}
						logger.info("【非实时保单状态查询-保单库】拼接被保人信息 end");
					}

					// 查询受益人
					ContractBenePO contractBenePO = new ContractBenePO();
					contractBenePO.setPolicyId(contractMaster.getPolicyId());
					List<ContractBenePO> contractBenePOs = contractBeneDao.findAllContractBene(contractBenePO);
					if (CollectionUtilEx.isNotEmpty(contractBenePOs)) {
						policyQueryDelayedPasBO.setBeneCount(contractBenePOs.size());// 受益人个数
						// 最多返回三个受益人信息
						logger.info("【非实时保单状态查询-保单库】拼接受益人信息 start");
						for (int i = 0; i < contractBenePOs.size(); i++) {
							// 查询客户信息
							CustomerPO shareCustomerPO = queryCustomerInfo(contractBenePOs.get(i).getCustomerId());
							if (i == 0) {
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerName())) {
									policyQueryDelayedPasBO.setShareName1(shareCustomerPO.getCustomerName());// 受益人名称1
								}
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerCertType())) {
									policyQueryDelayedPasBO.setShareCertType1(shareCustomerPO.getCustomerCertType());// 受益人证件类型代码1
								}
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerCertiCode())) {
									policyQueryDelayedPasBO.setShareCertiCode1(shareCustomerPO.getCustomerCertiCode());// 受益人证件号码1
								}
								if (contractBenePOs.get(i).getShareOrder() != null) {
									policyQueryDelayedPasBO.setShareOrder1(contractBenePOs.get(i).getShareOrder());// 受益次序值1
								}
								if (contractBenePOs.get(i).getShareRate() != null) {
									policyQueryDelayedPasBO.setShareRate1(contractBenePOs.get(i).getShareRate());// 受益比例1
								}
							}
							if (i == 1) {
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerName())) {
									policyQueryDelayedPasBO.setShareName2(shareCustomerPO.getCustomerName());// 受益人名称2
								}
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerCertType())) {
									policyQueryDelayedPasBO.setShareCertType2(shareCustomerPO.getCustomerCertType());// 受益人证件类型代码2
								}
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerCertiCode())) {
									policyQueryDelayedPasBO.setShareCertiCode2(shareCustomerPO.getCustomerCertiCode());// 受益人证件号码2
								}
								if (contractBenePOs.get(i).getShareOrder() != null) {
									policyQueryDelayedPasBO.setShareOrder2(contractBenePOs.get(i).getShareOrder());// 受益次序值2
								}
								if (contractBenePOs.get(i).getShareRate() != null) {
									policyQueryDelayedPasBO.setShareRate2(contractBenePOs.get(i).getShareRate());// 受益比例2
								}
							}
							if (i == 2) {
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerName())) {
									policyQueryDelayedPasBO.setShareName3(shareCustomerPO.getCustomerName());// 受益人名称3
								}
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerCertType())) {
									policyQueryDelayedPasBO.setShareCertType3(shareCustomerPO.getCustomerCertType());// 受益人证件类型代码3
								}
								if (shareCustomerPO != null
										&& StringUtils.isNotBlank(shareCustomerPO.getCustomerCertiCode())) {
									policyQueryDelayedPasBO.setShareCertiCode3(shareCustomerPO.getCustomerCertiCode());// 受益人证件号码3
								}
								if (contractBenePOs.get(i).getShareOrder() != null) {
									policyQueryDelayedPasBO.setShareOrder3(contractBenePOs.get(i).getShareOrder());// 受益次序值3
								}
								if (contractBenePOs.get(i).getShareRate() != null) {
									policyQueryDelayedPasBO.setShareRate3(contractBenePOs.get(i).getShareRate());// 受益比例3
								}
							}
						}
						logger.info("【非实时保单状态查询-保单库】拼接受益人信息 end");
					}
				}

				// 查询该单保险累计缴纳金额      #125834原数据字段“当年续期保险缴纳金额”修改为“该单保险累计缴纳金额”
				PremArapPO premArap = new PremArapPO();
				CapPayedPremPO payedPremPO = new CapPayedPremPO();
				CapPayedPremPO payedPremTotalPO = new CapPayedPremPO();
				CapPayedPremPO payedPremByGatherPO = new CapPayedPremPO();
				CapPayedPremPO payedPremByPaymentPO = new CapPayedPremPO();
				CapPayedPremPO payedPremExPO = new CapPayedPremPO();
				premArap.setPolicyCode(contractMaster.getPolicyCode());
				payedPremPO.setPolicyCode(contractMaster.getPolicyCode());
				logger.info("【非实时保单状态查询-保单库】查询该单保险累计缴纳金额");
				
				premArap = queryPolicyInfoDao.findPolicyInitialPrem(premArap);// 查询首期缴费金额
				payedPremTotalPO = capQueryPayedPremDao.queryPayedPremTotal(payedPremPO);//查询续期保费累计缴纳金额
				payedPremByGatherPO = capQueryPayedPremDao.queryPayedPremByGather(payedPremPO);//查询该单保险累计加保的补费金额
				payedPremByPaymentPO = capQueryPayedPremDao.queryPayedPremByPayment(payedPremPO);//查询该单保险累计减保的退费金额
				payedPremExPO = capQueryPayedPremDao.queryPayedPremEx(payedPremPO);//查询该单保险追加保费累计金额
				BigDecimal payedPremInitial = premArap.getFeeAmount()==null?new BigDecimal(0):premArap.getFeeAmount();
				BigDecimal payedPremTotal = payedPremTotalPO.getFeeAmount()==null?new BigDecimal(0):payedPremTotalPO.getFeeAmount();
				BigDecimal payedPremByGather = payedPremByGatherPO.getFeeAmount()==null?new BigDecimal(0):payedPremByGatherPO.getFeeAmount();
				BigDecimal payedPremByPayment = payedPremByPaymentPO.getFeeAmount()==null?new BigDecimal(0):payedPremByPaymentPO.getFeeAmount();
				BigDecimal payedPremByEx = payedPremExPO.getFeeAmount()==null?new BigDecimal(0):payedPremExPO.getFeeAmount();
				logger.info(contractMaster.getPolicyCode()+"保单的首期缴费金额="+payedPremInitial+";续期保费累计缴纳金额="+payedPremTotal+";累计加保的补费金额="+payedPremByGather+";累计减保的退费金额="+payedPremByPayment+";追加保费累计金额="+payedPremByEx);
				policyQueryDelayedPasBO.setPayedPremInCurrentYear((((payedPremInitial.add(payedPremTotal)).add(payedPremByGather)).add(payedPremByEx)).subtract(payedPremByPayment));
				logger.info(contractMaster.getPolicyCode()+"该单保险累计缴纳金额="+policyQueryDelayedPasBO.getPayedPremInCurrentYear());
				

				// 查询基础变更以外的生效保全项（倒序）
				CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
				csPolicyChangePO.setPolicyId(contractMaster.getPolicyId());
				List<CsPolicyChangePO> csPolicyChangePOList = csPolicyChangeDao
						.findAllCsPolicyChangeOrderByFinishTimeDesc(csPolicyChangePO);
				if (CollectionUtilEx.isNotEmpty(csPolicyChangePOList)) {
					Date yesterDay = DateUtilsEx.addDay(DateUtilsEx.formatDate(new Date(), "yyyy-MM-dd"), -1);// 查询结束时间
					StringBuilder serviceCodeStrb = new StringBuilder();
					for (CsPolicyChangePO po : csPolicyChangePOList) {
						if (yesterDay.compareTo(po.getFinishTime()) <= 0) {
							// 尚未生效
//							continue; // 方便测试，截止时间超过昨天也有效
						}
						serviceCodeStrb.append(po.getServiceCode().trim() + ",");
					}
					int length = serviceCodeStrb.length();
					if (length > 0 && ',' == serviceCodeStrb.charAt(length - 1)) {
						serviceCodeStrb.setLength(length - 1);
					}
					policyQueryDelayedPasBO.setServiceCodes(serviceCodeStrb.toString());
				}

				logger.info("【非实时保单状态查询-保单库】保单信息" + policyCount + ":" + XmlHelper.classToXml(policyQueryDelayedPasBO));
				policyQueryDelayedPasBOList.add(policyQueryDelayedPasBO);
			}
			if (policyQueryDelayedPasBOList != null && policyQueryDelayedPasBOList.size() > 0) {
				outputBo.setPolicyQueryDelayedPasBOList(policyQueryDelayedPasBOList);
				outputBo.setCTCount(CTCount);// 退保总笔数
				outputBo.setTotalCTAmount(totalCTAmount);
				// 退保总金额
				outputBo.setHesitateCTCount(hesitateCTCount);// 犹豫期退保总笔数
				outputBo.setTotalHesitateCTAmount(totalHesitateCTAmount);// 犹豫期退保总金额
				outputBo.setMaturityCount(maturityCount);// 满期总笔数
				outputBo.setTotalMaturityAmount(totalMaturityAmount);// 满期总金额
				outputBo.setTotalCount(totalCount + CTCount + hesitateCTCount + maturityCount);// 总记录数
				outputBo.setTotalAmount(
						totalAmount.add(totalCTAmount).add(totalHesitateCTAmount).add(totalMaturityAmount));// 总记录数
			}
		} else {
			logger.info("【非实时保单状态查询-保单库】无符合条件的数据");
		}
		logger.info("【非实时保单状态查询-保单库】非实时保单查询出参：" + XmlHelper.classToXml(outputBo));
		return outputBo;
	}

	/**
	 * @description 分页查询保单信息公共方法
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param po
	 * @param queryMethod
	 * @return
	 */
	private List<ContractMasterPO> queryContractMasterList(ContractMasterPO contractMasterPO, String queryMethod) {

		int PAGE_SIZE = 1800;
		List<ContractMasterPO> list = new ArrayList<ContractMasterPO>();
		CurrentPage<ContractMasterPO> currentPage = null;
		if ("findDelayedPolicyByType".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDelayedPolicyByTypeCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(
						queryPolicyInfoDao.findDelayedPolicyByTypeDetail(contractMasterPO, currentPage).getPageItems());
			}
		} else if ("findDelayedPolicyByServiceCode".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDelayedPolicyByServiceCodeCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(queryPolicyInfoDao.findDelayedPolicyByServiceCodeDetail(contractMasterPO, currentPage)
						.getPageItems());
			}
		} else if ("findDelayedPolicyByDate".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDelayedPolicyByDateCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(
						queryPolicyInfoDao.findDelayedPolicyByDateDetail(contractMasterPO, currentPage).getPageItems());
			}
		} else if ("findDelayedPolicyByHesitationDate".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDelayedPolicyByHesitationDateCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(queryPolicyInfoDao.findDelayedPolicyByHesitationDateDetail(contractMasterPO, currentPage)
						.getPageItems());
			}
		} else if ("findDelayedPayInfo".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDelayedPayInfoCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(queryPolicyInfoDao.findDelayedPayInfoDetail(contractMasterPO, currentPage).getPageItems());
			}
		} else if ("findDelayedPolicyState".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDelayedPolicyStateCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(
						queryPolicyInfoDao.findDelayedPolicyStateDetail(contractMasterPO, currentPage).getPageItems());
			}
		} else if ("findDelayedCancellation".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDelayedCancellationCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(
						queryPolicyInfoDao.findDelayedCancellationDetail(contractMasterPO, currentPage).getPageItems());
			}
		} else if ("findDelayedPolicyBeforeHesitationDate".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao
					.findDelayedPolicyBeforeHesitationDateCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(queryPolicyInfoDao
						.findDelayedPolicyBeforeHesitationDateDetail(contractMasterPO, currentPage).getPageItems());
			}
		} else if ("findDrcd".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDrcdCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(queryPolicyInfoDao.findDrcdDetail(contractMasterPO, currentPage).getPageItems());
			}
		} else if ("findDelayedFundTransChange".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findDelayedFundTransChangeCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(queryPolicyInfoDao.findDelayedFundTransChangeDetail(contractMasterPO, currentPage).getPageItems());
			}
		} else if ("findSurrenderInfo".equals(queryMethod)) {
			ContractMasterPO resultCntPO = queryPolicyInfoDao.findSurrenderInfoCount(contractMasterPO);
			if (resultCntPO == null || BigDecimal.ZERO.equals(resultCntPO.getQueryCount())) {
				return list;
			}
			int cnt = resultCntPO.getQueryCount().intValue();
			currentPage = new CurrentPage<ContractMasterPO>();
			for (int i = 1; (i - 1) * PAGE_SIZE < cnt; i++) {
				currentPage.setPageNo(i);
				currentPage.setPageSize(PAGE_SIZE);
				list.addAll(queryPolicyInfoDao.findSurrenderInfoDetail(contractMasterPO, currentPage).getPageItems());
			}
		}

		return list;
	}
	
	/**
	 * @description 查询客户信息公用方法
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param customerId
	 * @return
	 */
	public CustomerPO queryCustomerInfo(BigDecimal customerId) {
		CustomerPO customerPO = new CustomerPO();
		customerPO.setCustomerId(customerId);
		return customerDao.findCustomerByCustomerId(customerPO);
	}

	/**
	 * @description 查询地址信息公用方法
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param addressId
	 * @return
	 */
	public AddressPO queryAddressInfo(BigDecimal addressId) {
		AddressPO addressPO = new AddressPO();
		addressPO.setAddressId(addressId);
		return addressDao.findAddress(addressPO);
	}

}
