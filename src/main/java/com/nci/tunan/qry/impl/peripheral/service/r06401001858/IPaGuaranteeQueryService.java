package com.nci.tunan.qry.impl.peripheral.service.r06401001858;

import java.util.List;
import java.util.Map;

import com.nci.tunan.qry.interfaces.model.po.NbDocumentPO;
import com.nci.tunan.qry.interfaces.model.po.PaQuestionaireCustomerPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractBusiProdPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractMasterPO;
import com.nci.tunan.qry.interfaces.model.po.QryContractProductPO;
import com.nci.tunan.qry.interfaces.model.po.QryInsuredListPO;
import com.nci.tunan.qry.interfaces.model.po.QryPolicyHolderPO;
import com.nci.tunan.qry.interfaces.model.po.UwPenoticePO;

public interface IPaGuaranteeQueryService {

	public List<Map<String, Object>> queryApplyCode(QryPolicyHolderPO policyHolderPO);

	public QryContractMasterPO queryContractMaster(
			QryContractMasterPO contractMasterPO);

	public int queryCount(PaQuestionaireCustomerPO paQuestionaireCustomerPO);

	public int queryCountP(UwPenoticePO penoticePO);

	public List<Map<String, Object>> queryBusiProd(
			QryContractBusiProdPO contractBusiProdPO);

	public List<Map<String, Object>> querySumContractProduct(
			QryContractProductPO contractProductPO);

	public int queryDocument(NbDocumentPO documentPO);

	public int queryDocumentUws(NbDocumentPO documentPO);

	public List<Map<String, Object>> queryAppntname(
			QryPolicyHolderPO policyHolderPO);

	public List<Map<String, Object>> queryApplyCodeIl(
			QryInsuredListPO insuredListPO);

}
