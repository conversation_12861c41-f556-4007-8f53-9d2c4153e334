<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="claimPayRejectPO">
	<!-- 给付拒付清单-->
	<select id="findClaimPayRejectListTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT count(1)
					  FROM (  
					  SELECT DISTINCT    
                                TCAGENT.AGENT_CODE AGENT_NAME,
                                TCAGENT.AGENT_ORGAN_CODE,
                                CM.ORGAN_CODE,
                                (SELECT UO.ORGAN_NAME
                                   FROM DEV_PAS.T_UDMP_ORG UO
                                  WHERE UO.ORGAN_CODE = CM.ORGAN_CODE) AS ORGAN_NAME,
                                CA.ACCIDENT_NO,
                                CC.CASE_NO,
                                CM.POLICY_CODE,
                                (SELECT CLL.NAME
                                   FROM DEV_CLM.T_CASE_LEVEL CLL
                                  WHERE CC.CASE_FLAG = CLL.CODE
                                    AND ROWNUM = 1) AS CASE_FLAG_NAME,
                                CBP.BUSI_PROD_CODE,
                                BP.PRODUCT_NAME_SYS AS BUSI_NAME,
                                DECODE(BP.COVER_PERIOD_TYPE, '0', '长险', '短险') AS BUSI_FLAG,
                                (SELECT R.NAME
                                   FROM APP___CLM__DBUSER.T_CLAIM_TYPE R
                                  WHERE R.CODE = CL.CLAIM_TYPE) AS CLAIM_NAME,
                                CL.LIAB_CODE,
                                CL.LIAB_NAME,
                                CASE
                                  WHEN CL.IS_WAIVED = 1 THEN
                                   CL.WAIVE_AMT
                                  ELSE
                                   CL.ADJUST_PAY
                                END AS ADJUST_PAY,
                                CASE
                                  WHEN CL.LIAB_CONCLUSION = 5 THEN
                                   CL.ADJUST_PAY
                                  ELSE
                                   0
                                END AS REJECT_PAY,
                                CL.ACTUAL_PAY AS PAY_AMOUNT,
                                 (SELECT TCAR.NAME
                                   FROM DEV_CLM.T_CLAIM_AUDIT_REJECT TCAR
                                  WHERE TCAR.CODE = SUBSTR(CL.REJECT_CODE, 0, 2)) REJECT_REASON,
                                TO_CHAR((SELECT NVL(DETAIL.RETURNS_PAY_PREM, 0) +
                                                NVL(DETAIL.CLAIM_SURRENDER, 0) +
                                                NVL(DETAIL.FINISH_INTEREST, 0) +
                                                NVL(DETAIL.CASH_DIVIDEND, 0) +
                                                NVL(DETAIL.CASH_DIVIDEND_INTEREST, 0) +
                                                NVL(DETAIL.MARGIN_BACK, 0) +
                                                NVL(DETAIL.NO_COLLAR_BOND, 0) +
                                                NVL(DETAIL.ENDD_ADJUST_INTEREST, 0) +
                                                NVL(DETAIL.CLAIM_RETURNS, 0)
                                                  AS RETURN_PAY
                                          FROM DEV_CLM.T_CLAIM_FEE_DETAIL DETAIL
                                         WHERE DETAIL.CLAIM_LIAB_ID = CL.CLAIM_LIAB_ID
                                           AND ROWNUM = 1)) RETURN_PAY, 
                                NVL(TO_CHAR((SELECT SUM(A.PAY_AMOUNT)
                                              FROM DEV_CLM.T_CLAIM_ADJUST_BUSI A
                                             WHERE A.ADJUST_TYPE = 7
                                               AND A.CASE_ID = CC.CASE_ID)),
                                    '0') AS DETAIN_PAY,
                                    (SELECT COUNT(1)
						                   FROM DEV_CLM.T_SURVEY_APPLY SA
						                  WHERE CC.CASE_ID = SA.CASE_ID) AS SURVEY_NUM,
						(SELECT COUNT(1)
						                   FROM DEV_CLM.T_CLAIM_UW TCU
						                  WHERE CL.CASE_ID = TCU.CASE_ID) AS UW_SUM,
	                  	CL.LIAB_START_DATE,
	                    CL.LIAB_END_DATE 
  FROM DEV_CLM.T_CLAIM_ACCIDENT   CA,
       DEV_PAS.T_CUSTOMER         TC,
       DEV_PAS.T_CONTRACT_AGENT   TCAGENT,
       DEV_CLM.T_CLAIM_SUB_CASE   CSC,
       DEV_CLM.T_CLAIM_PRODUCT    CP,
       DEV_CLM.T_CLAIM_PAY        TCP,
       DEV_PDS.T_BUSINESS_PRODUCT BP,
       DEV_CLM.T_CLAIM_BUSI_PROD  CBP,
       DEV_PAS.T_CONTRACT_MASTER  CM,
       DEV_CLM.T_CLAIM_LIAB       CL,
       DEV_CLM.T_CLAIM_CASE       CC ]]>
                       <if test="is_uw != null and is_uw != ''"><![CDATA[ ,dev_clm.t_claim_uw tcu ]]></if>
                       <![CDATA[ 

 WHERE CC.CASE_ID = CL.CASE_ID
   AND TCAGENT.POLICY_ID = CM.POLICY_ID
   AND CL.POLICY_CODE = CM.POLICY_CODE
   AND CL.CASE_ID = CBP.CASE_ID
   AND CL.POLICY_CODE = CBP.POLICY_CODE
   AND CL.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
   AND CL.BUSI_PROD_CODE =CBP.BUSI_PROD_CODE
   AND CL.CASE_ID = CSC.CASE_ID
   AND CL.SUB_CASE_ID = CSC.SUB_CASE_ID
   AND CC.ACCIDENT_ID = CA.ACCIDENT_ID
   AND CL.CASE_ID = TCP.CASE_ID(+)
   AND CL.POLICY_CODE = TCP.POLICY_CODE(+)
   AND CL.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID(+)
   AND CBP.CASE_ID = CP.CASE_ID
   AND CBP.POLICY_CODE = CP.POLICY_CODE
   AND CBP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
   AND BP.PRODUCT_CODE_SYS = CBP.BUSI_PROD_CODE
   AND TC.CUSTOMER_ID = CC.INSURED_ID
   AND TCAGENT.is_current_agent  = '1'
   AND CC.CASE_STATUS = '80'
		   ]]>
	        <if test="sear_organ_code != null and sear_organ_code != ''"><![CDATA[ AND cm.ORGAN_CODE like  #{sear_organ_code}||'%' ]]></if>
	       <if test="sear_sta_begin_date != null and sear_sta_begin_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) >= #{sear_sta_begin_date} ]]></if>
	       <if test="sear_sta_end_date != null and sear_sta_end_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) <= #{sear_sta_end_date} ]]></if>
		   <if test="channel_code != null and channel_code != ''"><![CDATA[ and cc.channel_code = #{channel_code} ]]></if>
		   <if test="sear_busi_item_id != null and sear_busi_item_id != ''"><![CDATA[ AND CBP.BUSI_PROD_CODE = (SELECT A.PRODUCT_CODE_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT A WHERE A.BUSINESS_PRD_ID  = #{sear_busi_item_id}) ]]></if>
		   <if test="policyType != null and policyType != ''"><![CDATA[ AND cm.policy_type = #{policyType} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag != '' and sear_case_flag != 4 and sear_case_flag != 6"><![CDATA[ AND CC.CASE_FLAG = #{sear_case_flag} 		    ]]></if>
		  <if test="sear_case_flag != null and sear_case_flag == 4"><![CDATA[ AND ((CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID  = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO')) AND CC.EASY_AUDIT_DECISION IS NOT NULL  AND CC.EASY_AUDITOR_ID IS NULL) ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag == 6"><![CDATA[  AND  CC.CASE_STATUS = '80' AND CC.APPROVER_ID  = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO') AND ((CC.AUDITOR_ID  != (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO')) OR (CC.AUDITOR_ID = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO') AND CC.EASY_AUDITOR_ID IS NOT NULL))]]></if>
		   <if test="sear_claim_type != null and sear_claim_type != ''"><![CDATA[ AND CSC.CLAIM_TYPE = #{sear_claim_type} ]]></if>
		   <if test="sear_liab_conclusion != null and sear_liab_conclusion != ''"><![CDATA[ AND CL.LIAB_CONCLUSION = #{sear_liab_conclusion} ]]></if>
		  <if test="is_uw != null and is_uw != ''"><![CDATA[ and tcu.case_id=cl.case_id  ]]></if>
		   <![CDATA[ order by cc.case_no) C WHERE 1=1]]>
		    <if test="is_uw != null and is_uw != ''"><![CDATA[ AND C.uw_sum > 0 ]]></if>
		   <if test="is_survey != null and is_survey != ''"><![CDATA[ AND C.survey_num > 0 ]]></if>
	</select>	
				
	
	<select id="findClaimPayRejectListForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[ SELECT B.*,(select a1.name
  									from dev_clm.t_accident1 a1, 
  									     dev_clm.t_claim_accident_result car
 								   where a1.code = car.acc_result1
   									 and car.case_id = b.case_id
   									 and rownum = 1) as acc_name1,
             					 (select a2.name
                   					from dev_clm.t_accident2 a2,
                        				 dev_clm.t_claim_accident_result car
                  				   where a2.code = car.acc_result2
                    				 and car.case_id = b.case_id
                    				 and rownum = 1) as acc_name2 FROM(
            SELECT C.*,ROWNUM RN
            FROM (
            
           SELECT DISTINCT    
                                TCAGENT.AGENT_CODE AGENT_NAME,
                                TCAGENT.AGENT_ORGAN_CODE,
                                CM.ORGAN_CODE,
                                (SELECT UO.ORGAN_NAME
                                   FROM DEV_PAS.T_UDMP_ORG UO
                                  WHERE UO.ORGAN_CODE = CM.ORGAN_CODE) AS ORGAN_NAME,
                                CA.ACCIDENT_NO,
                                CC.CASE_NO,cl.case_id,
                                CM.POLICY_CODE,
                                (SELECT CLL.NAME
                                   FROM DEV_CLM.T_CASE_LEVEL CLL
                                  WHERE CC.CASE_FLAG = CLL.CODE
                                    AND ROWNUM = 1) AS CASE_FLAG_NAME,
                                CBP.BUSI_PROD_CODE,
                                BP.PRODUCT_NAME_SYS AS BUSI_NAME,
                                DECODE(BP.COVER_PERIOD_TYPE, '0', '长险', '短险') AS BUSI_FLAG,
                                (SELECT R.NAME
                                   FROM APP___CLM__DBUSER.T_CLAIM_TYPE R
                                  WHERE R.CODE = CL.CLAIM_TYPE) AS CLAIM_NAME,
                                CL.LIAB_CODE,
                                CL.LIAB_NAME,
                                CASE
                          WHEN TC.CUSTOMER_NAME IS NULL THEN
                           ' '
                          ELSE
                           SUBSTR(TC.CUSTOMER_NAME, 1, 1) || DECODE(LENGTH(TC.CUSTOMER_NAME),'2','****','****'||SUBSTR(TC.CUSTOMER_NAME,length(TC.CUSTOMER_NAME),1))  
                           END  CUSTOMER_NAME,
                                FLOOR(TO_NUMBER(SYSDATE - TC.CUSTOMER_BIRTHDAY) / 365) AS AGE,
                                TC.CUSTOMER_BIRTHDAY,
                                (SELECT TG.GENDER_DESC
                                   FROM DEV_CLM.T_GENDER TG
                                  WHERE TC.CUSTOMER_GENDER = TG.GENDER_CODE) AS GENDER,
                                (CASE
                                  WHEN CM.INITIAL_VALIDATE_DATE IS NOT NULL THEN
                                   CM.INITIAL_VALIDATE_DATE
                                  ELSE
                                   CM.VALIDATE_DATE
                                END) INITIAL_VALIDATE_DATE,
                                CBP.VALID_DATE VALIDDATE_DATE,
                                CA.ACC_DATE,
                                CSC.CLAIM_DATE,
                                (SELECT MIN(TCB.TREAT_START)
                                   FROM DEV_CLM.T_CLAIM_BILL TCB
                                  WHERE TCB.CASE_ID = CC.CASE_ID) AS TREAT_START,
                                CA.ACC_DESC,
  							CASE
                          WHEN CC.RPTR_NAME IS NULL THEN
                           ' '
                          ELSE
                           SUBSTR(CC.RPTR_NAME, 1, 1) || DECODE(LENGTH(CC.RPTR_NAME),'2','****','****'||SUBSTR(CC.RPTR_NAME,length(CC.RPTR_NAME),1))  
                           END  RPTR_NAME,                               
                            (CASE WHEN CC.RPTR_TIME < to_date('2023/01/01', 'yyyy/MM/dd') 
                           		  THEN
				                	CC.INSERT_TIME 
				                  ELSE
				                	CC.FIRST_RPTR_TIME
				                  END) AS RPTR_TIME,
                                CC.SIGN_TIME, 
                                (SELECT UU.USER_NAME
                                   FROM DEV_PAS.T_UDMP_USER UU
                                  WHERE UU.USER_ID = CC.SIGNER_ID) AS SIGNER_NAME,
                                CC.REGISTE_TIME,
                                (SELECT CN.NAME
                                   FROM DEV_CLM.T_CLAIM_NATURE CN
                                  WHERE CN.CODE = CSC.ACC_REASON) AS ACC_REASON_DESC,
                                (CASE
                                  WHEN CC.CASE_STATUS = '80' AND CC.IS_BPO = 1 AND CC.ACQUIST_WAY = '' THEN
                                   'BPO_CLM'
                                  WHEN CC.CASE_STATUS = '80' AND CC.ACQUIST_WAY = '1' THEN
                                   'BPO_CLM'
                                  WHEN CC.CASE_STATUS = '80' AND CC.ACQUIST_WAY = '3' THEN
                                   'DATA_CLM'
                                  ELSE
                                   (SELECT UU.USER_NAME
                                      FROM DEV_PAS.T_UDMP_USER UU
                                     WHERE UU.USER_ID = CC.REGISTER_ID
                                       AND ROWNUM = 1AND ROWNUM = 1)
                                END) AS REGISTER_NAME,
                               
                                   (CASE
                                      WHEN (CC.CASE_STATUS = '80' AND
                                           CC.AUDITOR_ID = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO') AND 
                                           CC.EASY_AUDITOR_ID IS NOT NULL AND
                                           CC.EASY_AUDIT_DECISION IS NOT NULL) OR
                                           (CC.CASE_FLAG = 4) THEN
                                       (SELECT UU.USER_NAME
                                          FROM DEV_PAS.T_UDMP_USER UU
                                         WHERE UU.USER_ID = CC.EASY_AUDITOR_ID
                                           AND ROWNUM = 1)
                                      ELSE
                                       (SELECT UU.USER_NAME
                                          FROM DEV_PAS.T_UDMP_USER UU
                                         WHERE UU.USER_ID = CC.AUDITOR_ID
                                           AND ROWNUM = 1)
                                    END) AS AUDITOR_NAME,
                                  
                                  
                                (SELECT CAD.NAME
                                   FROM DEV_CLM.T_CLAIM_AUDIT_DECISION CAD
                                  WHERE CAD.CODE = CC.AUDIT_DECISION) AS AUDIT_DECISION,
                                (SELECT UU.USER_NAME
                                   FROM DEV_PAS.T_UDMP_USER UU
                                  WHERE UU.USER_ID = CC.APPROVER_ID) AS APPROVER_NAME,
                               
                                CC.APPROVE_TIME,
 								  CASE
                          WHEN CC.TRUSTEE_NAME IS NULL THEN
                           ' '
                          ELSE
                           SUBSTR(CC.TRUSTEE_NAME, 1, 1) || DECODE(LENGTH(CC.TRUSTEE_NAME),'2','****','****'||SUBSTR(CC.TRUSTEE_NAME,length(CC.TRUSTEE_NAME),1))  
                           END   TRUSTEE_NAME,
                                  CASE
                                  WHEN CC.TRUSTEE_NAME IS NULL THEN
                                   CC.TRUSTEE_MP || '0'
                                  ELSE
                                   CC.TRUSTEE_MP
                                END AS TRUSTEE_MP,
                                CASE
                                  WHEN (CC.TRUSTEE_TYPE != '2') THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_AGENT TA, DEV_PAS.T_UDMP_ORG UO
                                     WHERE TA.AGENT_CODE = CC.TRUSTEE_CODE
                                       AND TA.AGENT_ORGAN_CODE = UO.ORGAN_CODE)
                                END AS TRUSTEE_ORGAN_NAME,
                                (SELECT CLD.NAME
                                   FROM DEV_CLM.T_CLAIM_LIAB_DECISION CLD
                                  WHERE CL.LIAB_CONCLUSION = CLD.CODE
                                    AND ROWNUM = 1) AS LIAB_CONCLUSION, 
                                DECODE(CC.CASE_FLAG, '4', '是', '', '是', '否') AS CASE_FLAG,
                                nvl((SELECT SUM(CB.SUM_AMOUNT)
                      				FROM DEV_CLM.T_CLAIM_BILL CB
                     				WHERE CL.CASE_ID = CB.CASE_ID),0) SUM_AMOUNT,
                                nvl((SELECT SUM(CB.DEDUCT_AMOUNT)
                      				FROM DEV_CLM.T_CLAIM_BILL CB
                     				WHERE CL.CASE_ID = CB.CASE_ID), 0) AS DEDUCT_AMOUNT, 
                                CASE
                                  WHEN CL.IS_WAIVED = 1 THEN
                                   CL.WAIVE_AMT
                                  ELSE
                                   CL.ADJUST_PAY
                                END AS ADJUST_PAY,
                                CL.IS_WAIVED,
                                CASE
                                  WHEN CL.LIAB_CONCLUSION = 5 THEN
                                   CL.CALC_PAY
                                  ELSE
                                   0
                                END AS REJECT_PAY,
                                CL.ACTUAL_PAY AS PAY_AMOUNT,
                                 (SELECT TCAR.NAME
                                   FROM DEV_CLM.T_CLAIM_AUDIT_REJECT TCAR
                                  WHERE TCAR.CODE = SUBSTR(CL.REJECT_CODE, 0, 2)) REJECT_REASON,
                                NVL(TO_CHAR((SELECT SUM(A.PAY_AMOUNT)
                                              FROM DEV_CLM.T_CLAIM_ADJUST_BUSI A
                                             WHERE  A.CASE_ID = CC.CASE_ID
                                             and a.adjust_type in (1,2,8,9,10,13,16,17,18,19,20,22,25,26))),
                                    '0') RETURN_PAY, 
                                NVL(TO_CHAR((SELECT SUM(A.PAY_AMOUNT)
                                              FROM DEV_CLM.T_CLAIM_ADJUST_BUSI A
                                             WHERE A.ADJUST_TYPE = 7
                                               AND A.CASE_ID = CC.CASE_ID)),
                                    '0') AS DETAIN_PAY, 
                                (SELECT LS.NAME
                                   FROM DEV_CLM.T_LIFE_STATE LS
                                  WHERE TC.LIVE_STATUS = LS.CODE) AS LIVE_STATUS,
									(SELECT SUBSTR(TCB.PAYEE_NAME, 1, 1) || DECODE(LENGTH(TCB.PAYEE_NAME),'3','****','2','****','4','****')
                                  || SUBSTR(TCB.PAYEE_NAME,length(TCB.PAYEE_NAME),1)
                                  FROM DEV_CLM.T_CLAIM_PAYEE TCB
                                  WHERE TCP.CASE_ID = TCB.CASE_ID
                                    AND ROWNUM = 1) AS BENE_NAME,
                               DECODE((SELECT COUNT(1)
                                         FROM DEV_CLM.T_SURVEY_APPLY SA
                                        WHERE CC.CASE_ID = SA.CASE_ID
                                          AND ROWNUM = 1),
                                       0,
                                       '否',
                                       '是') AS SURVEY_FLAG,
                
                (SELECT COUNT(1)
                   FROM DEV_CLM.T_SURVEY_APPLY SA
                  WHERE CC.CASE_ID = SA.CASE_ID) AS SURVEY_NUM,
                CASE (SELECT COUNT(SC.POSITIVE_FLAG)
                    FROM DEV_CLM.T_SURVEY_CONCLUSION SC,
                         DEV_CLM.T_SURVEY_APPLY      TSA
                   WHERE SC.APPLY_ID = TSA.APPLY_ID
                     AND TSA.CASE_ID = CC.CASE_ID
                     AND SC.POSITIVE_FLAG = '1'
                     AND ROWNUM = 1)
                  WHEN 1 THEN
                   '阳性'
                  WHEN 0 THEN
                   '阴性'
                  ELSE
                   ''
                END POSITIVE_FLAG,
                (SELECT TO_CHAR(WM_CONCAT(TSC.SURVEY_BY_ID1))
                   FROM DEV_CLM.T_SURVEY_COURSE TSC,
                        DEV_CLM.T_SURVEY_APPLY  TSA
                  WHERE TSC.APPLY_ID = TSA.APPLY_ID
                    AND TSA.CASE_ID = CC.CASE_ID) SURVEY_BY_ID1_NAME,
                (SELECT MIN(TSA.APPLY_DATE)
                   FROM DEV_CLM.T_SURVEY_APPLY TSA
                  WHERE TSA.CASE_ID = CC.CASE_ID) AS FIRST_APPLY_DATE,
                (SELECT MAX(TSA.APPLY_DATE)
                   FROM DEV_CLM.T_SURVEY_APPLY TSA
                  WHERE TSA.CASE_ID = CC.CASE_ID) AS LAST_APPLY_DATE,
                (SELECT COUNT(TSF.SURVEY_FEE)
                   FROM DEV_CLM.T_SURVEY_FEE TSF, DEV_CLM.T_SURVEY_APPLY TCA
                  WHERE TSF.APPLY_ID = TCA.APPLY_ID
                    AND TCA.CASE_ID = CC.CASE_ID) SURVEY_FEE,
                CC.AUDIT_TIME,
                CC.END_CASE_TIME,
                (SELECT T.NAME
                   FROM DEV_CLM.T_PAY_MODE T
                  WHERE T.CODE = (SELECT R.PAY_MODE
                                    FROM DEV_CLM.T_PREM_ARAP R
                                   WHERE CL.IS_WAIVED != 1
                                     AND R.FEE_STATUS = '01'
                                     AND CC.CASE_NO = R.BUSINESS_CODE
                                     AND ROWNUM = 1)) AS PAY_MODE, 
                
                (SELECT  TO_CHAR(WM_CONCAT(DISTINCT R.TREAT_TYPE))
                   FROM DEV_CLM.T_CLAIM_BILL R
                  WHERE R.CASE_ID = CC.CASE_ID) CURE_STATUS,
                (SELECT TH.HOSPITAL_NAME
                   FROM DEV_CLM.T_HOSPITAL TH,DEV_CLM.T_CLAIM_CASE
                  WHERE TH.HOSPITAL_CODE = CURE_HOSPITAL
                  and  CASE_ID = CC.CASE_ID
                    AND ROWNUM = 1 ) HOSPITAL_NAME,
                DECODE(CC.CURE_HOSPITAL,
                       NULL,
                       '',
                       DECODE((SELECT CHS.IS_DESIGNATED
                                FROM DEV_CLM.T_CLAIM_HOSPITAL_SERVICE CHS
                               WHERE CHS.HOSPITAL_CODE = CC.CURE_HOSPITAL
                                 AND ROWNUM = 1),
                              NULL,
                              '否',
                              0,
                              '否',
                              '是')) AS IS_DESIGNATED,
                
                DECODE(CM.POLICY_TYPE, 1, '个人') AS POLICY_TYPE,
                
                (SELECT MAX(R.FINISH_TIME)
                   FROM DEV_CLM.T_PREM_ARAP R
                  WHERE R.FEE_STATUS = 01
                    AND CC.CASE_NO = R.BUSINESS_CODE
                    AND CBP.POLICY_CODE = R.POLICY_CODE
                    AND CBP.BUSI_PROD_CODE = R.BUSI_PROD_CODE) AS DUE_TIME,
                
                CC.ACCIDENT_DETAIL,
                (SELECT AD.DETAIL_DESC
                   FROM DEV_CLM.T_ACCIDENT_DETAIL AD
                  WHERE AD.DETAIL_CODE = CC.ACCIDENT_DETAIL
                    AND ROWNUM = 1) AS ADDIDENT_DESC,
                
                (SELECT CAR.ACC_RESULT1
                   FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT CAR
                  WHERE CAR.CASE_ID = CL.CASE_ID
                    AND ROWNUM = 1) ACC_RESULT1,
                (SELECT CAR.ACC_RESULT2
                   FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT CAR
                  WHERE CAR.CASE_ID = CL.CASE_ID
                    AND ROWNUM = 1) ACC_RESULT2,
                CASE
                  WHEN LENGTH(CM.ORGAN_CODE) > 3 THEN
                   (SELECT UO.ORGAN_NAME
                      FROM DEV_PAS.T_UDMP_ORG UO
                     WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 1, 4))
                END AS SECOND_ORGAN_CODE,
                CASE
                  WHEN LENGTH(CM.ORGAN_CODE) > 5 THEN
                   (SELECT UO.ORGAN_NAME
                      FROM DEV_PAS.T_UDMP_ORG UO
                     WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 1, 6))
                END AS THIRD_ORGAN_CODE,
                CASE
                  WHEN LENGTH(CM.ORGAN_CODE) > 7 THEN
                   (SELECT UO.ORGAN_NAME
                      FROM DEV_PAS.T_UDMP_ORG UO
                     WHERE UO.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 1, 8))
                END AS FOURTH_ORGAN_CODE,
                (case when (select count(1) from DEV_CLM.t_claim_memo cme where cme.case_id = cc.case_id and cme.memo_type = '14') > 0
                then '是' else '否' end) AS it_problem_case,
                (SELECT SUBSTR(TCA.CLMT_NAME, 1, 1) || DECODE(LENGTH(TCA.CLMT_NAME),'3','****','2','****','4','****')
                  || SUBSTR(TCA.CLMT_NAME, 3, 1) || SUBSTR(TCA.CLMT_NAME, 4, 1)  CLMT_NAME
                   FROM DEV_CLM.T_CLAIM_APPLICANT TCA
                  WHERE CC.CASE_ID = TCA.CASE_ID
                    AND ROWNUM = 1) CLMT_NAME,
                (SELECT LPR.RELATION_NAME
                   FROM DEV_CLM.T_LA_PH_RELA      LPR,
                        DEV_CLM.T_CLAIM_APPLICANT TCA
                  WHERE TCA.CLMT_INSUR_RELATION = LPR.RELATION_CODE
                    AND CC.CASE_ID = TCA.CASE_ID
                    AND ROWNUM = 1) CLMT_INSUR_RELATION,
                
                
                (SELECT CASE
         WHEN SUBSTR(TCA.CLMT_MP, 1, 1) = '0' AND
              (SUBSTR(TCA.CLMT_MP, 2, 1) = '1' OR SUBSTR(TCA.CLMT_MP, 2, 1) = '2') 
              THEN
         	  SUBSTR(TCA.CLMT_MP, 1, 4) || '****'||SUBSTR(TCA.CLMT_MP,length(TCA.CLMT_MP)-1,2)
         WHEN SUBSTR(TCA.CLMT_MP, 1, 1) = '0' AND
              (SUBSTR(TCA.CLMT_MP, 2, 1) != '1' OR SUBSTR(TCA.CLMT_MP, 2, 1) != '2')
              THEN 
              SUBSTR(TCA.CLMT_MP, 1, 5) || '****'||SUBSTR(TCA.CLMT_MP,length(TCA.CLMT_MP)-1,2)
              
         WHEN LENGTH(TCA.CLMT_MP) = 7
              THEN 
               '****'||SUBSTR(TCA.CLMT_MP,length(TCA.CLMT_MP)-1,2)
         WHEN LENGTH(TCA.CLMT_MP) = 8
              THEN 
               '****'||SUBSTR(TCA.CLMT_MP,length(TCA.CLMT_MP)-2,3)
         WHEN LENGTH(TCA.CLMT_MP) = 11 AND
              SUBSTR(TCA.CLMT_MP, 1, 1) = '1'
              THEN 
              	REPLACE(TCA.CLMT_MP, SUBSTR(TCA.CLMT_MP, 4, 4), '****')
         	ELSE
          		TCA.CLMT_MP
       			END CLMT_MP
                   FROM DEV_CLM.T_CLAIM_APPLICANT TCA
                  WHERE CC.CASE_ID = TCA.CASE_ID
                    AND ROWNUM = 1) CLMT_MP,
                
                DECODE(CL.RESIST_FLAG, 0, '否', '', '否', '是') RESIST_FLAG,
                (SELECT COUNT(1)
                   FROM DEV_CLM.T_CLAIM_UW TCU
                  WHERE CL.CASE_ID = TCU.CASE_ID) AS UW_SUM,
                CC.CASE_FLAG AS CASE_FLAG_1,
                CC.AUDITOR_ID,
                CC.APPROVER_ID,
             (SELECT B.PRIORITY_CLAIM
                FROM DEV_CLM.T_SURVEY_APPLY A, DEV_CLM.T_SURVEY_CONCLUSION B
               WHERE A.APPLY_ID = B.APPLY_ID
                 AND A.CASE_ID = CC.CASE_ID
                 AND A.SURVEY_TYPE = 5
                 AND ROWNUM =1)  AS PRIORITY_CLAIM,
             CL.LIAB_START_DATE,
             CL.LIAB_END_DATE, 
             CASE WHEN MAX(CL.LIAB_CONCLUSION) OVER(PARTITION BY CL.CASE_ID) = 5  THEN
                  '拒付' ELSE CLD.NAME END LIAB_DECISION,
             CC.AUDIT_PERMISSION_NAME AUDIT_PERMISSION,
             CC.APPROVE_PERMISSION_NAME APPROVE_PERMISSION,
             CASE WHEN (SELECT COUNT(1) FROM DEV_CLM.T_SURVEY_APPLY SA WHERE SA.CASE_ID=CL.CASE_ID AND SA.APPLY_SECTION=2 AND SA.SURVEY_STATUS != 3) > 0
                  THEN '是' ELSE '否' END SURVEY_STATUS,
             TCBP.INITIAL_VALIDATE_DATE INITIAL_VALIDATE,
             SUM(CL.CALC_PAY) OVER(PARTITION BY CL.CASE_ID) ADJUST_PAY_AMOUNT,
             U.ORGAN_CODE AUDIT_ORGAN_CODE,
             U1.ORGAN_CODE APPROVE_ORGAN_CODE,
             SUBSTR(CC.AUDIT_REMARK,0,4) AUDIT_REMARK,
             CC.AUDIT_REMARK AUDIT_REMARK_DESC,
             SUBSTR(CC.APPROVE_REMARK,0,4) APPROVE_REMARK,
             CC.APPROVE_REMARK APPROVE_REMARK_DESC,
             CC.CHANNEL_CODE,  
                  (SELECT AC.CHANNEL_DESC
                 FROM DEV_CLM.T_ACCEPT_CHANNEL AC
                WHERE AC.CHANNEL_CODE = CC.CHANNEL_CODE
                  AND ROWNUM =1)  AS CHANNEL_NAME
             
  FROM DEV_CLM.T_CLAIM_ACCIDENT   CA,
       DEV_PAS.T_CUSTOMER         TC,
       DEV_PAS.T_CONTRACT_AGENT   TCAGENT,
       DEV_CLM.T_CLAIM_SUB_CASE   CSC,
       DEV_CLM.T_CLAIM_PRODUCT    CP,
       DEV_CLM.T_CLAIM_PAY        TCP,
       DEV_PDS.T_BUSINESS_PRODUCT BP,
       DEV_CLM.T_CLAIM_BUSI_PROD  CBP,
       DEV_PAS.T_CONTRACT_MASTER  CM,
       DEV_CLM.T_CLAIM_LIAB       CL,
       DEV_CLM.T_CLAIM_CASE       CC,
       DEV_CLM.T_CLAIM_LIAB_DECISION CLD,
       DEV_PAS.T_CONTRACT_BUSI_PROD  TCBP,
       DEV_PAS.T_UDMP_USER           U,
       DEV_PAS.T_UDMP_USER           U1]]>
                       <if test="is_uw != null and is_uw != ''"><![CDATA[ ,dev_clm.t_claim_uw tcu ]]></if>
                       <![CDATA[ 

 WHERE CC.CASE_ID = CL.CASE_ID
   AND TCAGENT.POLICY_ID = CM.POLICY_ID
   AND CL.POLICY_CODE = CM.POLICY_CODE
   AND CL.CASE_ID = CBP.CASE_ID
   AND CL.POLICY_CODE = CBP.POLICY_CODE
   AND CL.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
   AND CL.BUSI_PROD_CODE =CBP.BUSI_PROD_CODE
   AND CL.CASE_ID = CSC.CASE_ID
   AND CL.SUB_CASE_ID = CSC.SUB_CASE_ID
   AND CC.ACCIDENT_ID = CA.ACCIDENT_ID
   AND CL.CASE_ID = TCP.CASE_ID(+)
   AND CL.POLICY_CODE = TCP.POLICY_CODE(+)
   AND CL.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID(+)
   AND CBP.CASE_ID = CP.CASE_ID
   AND CBP.POLICY_CODE = CP.POLICY_CODE
   AND CBP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
   AND BP.PRODUCT_CODE_SYS = CBP.BUSI_PROD_CODE
   AND TC.CUSTOMER_ID = CC.INSURED_ID
   AND CL.LIAB_CONCLUSION = CLD.CODE
   AND TCBP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
   AND U.USER_ID = CC.AUDITOR_ID
   AND U1.USER_ID = CC.APPROVER_ID
   AND TCAGENT.is_current_agent  = '1'
   AND CC.CASE_STATUS = '80'
            
		   ]]> 
	       <if test="sear_organ_code != null and sear_organ_code != ''"><![CDATA[ AND cm.ORGAN_CODE like  #{sear_organ_code}||'%' ]]></if>
	       <if test="sear_sta_begin_date != null and sear_sta_begin_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) >= #{sear_sta_begin_date} ]]></if>
	       <if test="sear_sta_end_date != null and sear_sta_end_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) <= #{sear_sta_end_date} ]]></if>
		   <if test="channel_code != null and channel_code != ''"><![CDATA[ and cc.channel_code = #{channel_code} ]]></if>
		   <if test="sear_busi_item_id != null and sear_busi_item_id != ''"><![CDATA[ AND CBP.BUSI_PROD_CODE = (SELECT A.PRODUCT_CODE_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT A WHERE A.BUSINESS_PRD_ID  = #{sear_busi_item_id}) ]]></if>
		   <if test="policyType != null and policyType != ''"><![CDATA[ AND cm.policy_type = #{policyType} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag != '' and sear_case_flag != 4 and sear_case_flag != 6"><![CDATA[ AND CC.CASE_FLAG = #{sear_case_flag} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag == 4"><![CDATA[  AND ((CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID  = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO')) AND CC.EASY_AUDIT_DECISION IS NOT NULL  AND CC.EASY_AUDITOR_ID IS NULL) ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag == 6"><![CDATA[  AND  CC.CASE_STATUS = '80' AND CC.APPROVER_ID  = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO') AND ((CC.AUDITOR_ID  != (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO')) OR (CC.AUDITOR_ID = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO') AND CC.EASY_AUDITOR_ID IS NOT NULL))]]></if>
		   <if test="sear_claim_type != null and sear_claim_type != ''"><![CDATA[ AND CSC.CLAIM_TYPE = #{sear_claim_type} ]]></if>
		   <if test="sear_liab_conclusion != null and sear_liab_conclusion != ''"><![CDATA[ AND CL.LIAB_CONCLUSION = #{sear_liab_conclusion} ]]></if>
		   <if test="is_uw != null and is_uw != ''"><![CDATA[ and tcu.case_id=cl.case_id  ]]></if>
		   <![CDATA[ order by cc.case_no)C WHERE ]]> 
		   <if test="is_uw != null and is_uw != ''"><![CDATA[ C.uw_sum > 0 AND ]]></if>
		   <if test="is_survey != null and is_survey != ''"><![CDATA[ C.survey_num > 0 AND ]]></if>
		    <![CDATA[ROWNUM <= #{LESS_NUM}  ]]>
		   <![CDATA[ )B WHERE B.RN > #{GREATER_NUM} order by B.end_case_time ]]>
	</select>
	
	
	
	<!-- 调查案件总数 -->
	<!-- <select id="findClaimPayRejectListOfSurveyTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[  SELECT COUNT(1)
					  FROM (select distinct   
            (select TCAGENT.AGENT_NAME from  dev_clm.T_CONTRACT_AGENT TCAGENT  WHERE TCAGENT.CASE_ID=CC.CASE_ID AND TCAGENT.POLICY_CODE=CM.POLICY_CODE
                 AND TCAGENT.CUR_FLAG='1' AND ROWNUM=1
                 ) agent_name,
                 (select TCAGENT.Agent_Organ_Code from  dev_clm.T_CONTRACT_AGENT TCAGENT  WHERE TCAGENT.CASE_ID=CC.CASE_ID AND TCAGENT.POLICY_CODE=CM.POLICY_CODE
                 AND TCAGENT.CUR_FLAG='1' AND ROWNUM=1
                 ) Agent_Organ_Code,
                        cm.organ_code  as organ_code, 
            (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code=cm.organ_code and rownum=1)        as organ_name,
              ca.accident_no,
            cc.case_no,
            cm.policy_code,
            (SELECT CL.NAME FROM DEV_CLM.T_CASE_LEVEL CL WHERE CC.CASE_FLAG = CL.CODE and rownum=1) as case_flag_name,
            cbp.busi_prod_code,
            (select bp.product_name_sys from dev_pds.t_business_product bp where bp.product_code_sys = cbp.busi_prod_code and rownum=1) as busi_name,
            (case when ((select bp.cover_period_type from dev_pds.t_business_product bp where bp.product_code_sys = cbp.busi_prod_code )='0') then '长险' else '短险' end)as busi_flag,
            (select ct.name from dev_clm.t_claim_type ct where ct.code = csc.claim_type and rownum=1) as claim_name,
            cl.liab_code,
                        cl.liab_name,
            (select tc.customer_name from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1) as customer_name,
                        (select tc.mobile_tel from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1) as mobile_tel,
            FLOOR(TO_NUMBER(SYSDATE -(select tc.customer_birthday from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1)) / 365) AS AGE,
            (select tc.customer_birthday
                                                   from dev_clm.t_customer tc
                                                  where tc.customer_id =
                                                        cc.insured_id and rownum=1) as customer_birthday,
                        (select tg.gender_desc from dev_clm.t_customer tc,dev_clm.t_gender tg where tc.customer_id = cc.insured_id and tc.customer_gender = tg.gender_code AND ROWNUM=1) as gender,
            (case when cm.initial_validate_date is not null then cm.initial_validate_date else cm.validdate_date end) initial_validate_date,
            (select a.validate_date from dev_clm.t_contract_busi_prod a where a.copy_date = csc.claim_date and a.cur_flag=0 and a.busi_item_id = tcbp.busi_item_id AND ROWNUM=1) validdate_date,
                ca.acc_date,
            csc.claim_date,
                cb.treat_start,
              (select ca.acc_desc from dev_clm.t_claim_accident ca where ca.accident_id = cc.accident_id and rownum=1) as acc_desc,
            cc.rptr_name,
            cc.rptr_mp,
            cc.rptr_time,
            cc.sign_time,
              (select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.signer_id and rownum=1) as signer_name,
            cc.registe_time,
            (select cn.name from dev_clm.t_claim_nature cn where cn.code = csc.acc_reason and rownum=1) as acc_reason_desc,
                       nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.register_id and rownum=1),
             (CASE WHEN CC.CASE_STATUS = '80' AND  CC.IS_BPO =1 THEN 'BPO_CLM'
              ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) END ) )
            as register_name,
                        nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) ,
              (CASE WHEN (CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID is null ) OR (CC.CASE_FLAG =4) THEN 'AUTO'
            ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) END  ))  
            as auditor_name,
            (select cad.name from dev_clm.t_claim_audit_decision cad where cad.code = cc.audit_decision and rownum=1) as audit_decision,
            nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.approver_id and rownum=1), 
                ( CASE WHEN CC.CASE_STATUS = '80' AND CC.APPROVER_ID is null AND CC.AUDITOR_ID is not null THEN 'AUTO'
              WHEN (CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID is null ) OR (CC.CASE_FLAG =4) THEN ''
            ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.approver_id and rownum=1) END  ))  
              as approver_name,
                        ( CASE WHEN CC.CASE_STATUS = '80' AND CC.APPROVER_ID is null AND CC.AUDITOR_ID is not null THEN cc.audit_time
            ELSE cc.approve_time END  )
              as approve_time,
            nvl(cc.trustee_name,'空值')as trustee_name,
            case when cc.trustee_name is null then cc.trustee_mp || '0' else cc.trustee_mp end as trustee_mp,
            case when (cc.trustee_type !=  '2') then  (select uo.organ_name from DEV_PAS.T_AGENT ta,DEV_PAS.T_UDMP_ORG uo where  ta.agent_code = cc.trustee_code and ta.agent_organ_code = uo.organ_code  AND ROWNUM=1) end as trustee_organ_name,
            (select cld.name from dev_clm.t_claim_liab_decision cld where cl.liab_conclusion = cld.code and rownum=1) as liab_conclusion,
            decode(cc.case_flag,'4','是','否') as case_flag,
            (select sum(cb.sum_amount) from dev_clm.t_claim_bill cb where cl.case_id = cb.case_id group by cl.case_id )sum_amount,
            nvl(cb.deduct_amount,0) deduct_amount,
           (select ADJUST_PAY
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1) as ADJUST_PAY,
                                case when cl.liab_conclusion=5 then cl.adjust_pay else 0 end as REJECT_PAY,
                                (select FINAL_PAY_AMOUNT
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1) as PAY_AMOUNT,
                                (select tcar.name from dev_clm.T_CLAIM_AUDIT_REJECT tcar where tcar.code = cl.reject_code) reject_reason,
                                
                                   to_char((select nvl(RETURNS_PAY_PREM, 0) +
                                               nvl(FINISH_INTEREST, 0) +
                                               nvl(MARGIN_BACK, 0) +
                                               nvl(NO_COLLAR_BOND, 0) +
                                               nvl(RETURNS_UNEXPIRED_PREM, 0) +
                                               nvl(CASH_DIVIDEND, 0) +
                                               nvl(CASH_DIVIDEND_INTEREST, 0) as return_pay
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1)) return_pay,
                                to_char((select nvl(DEDUCT_PREM, 0) +
                                               nvl(DEDUCT_RESK_PREM, 0) +
                                               nvl(DEDUCT_POLICY_MANA_FEE, 0) +
                                               nvl(DEDUCT_ANNUITY_SURVIVAL_FUND,
                                                   0) +
                                               nvl(DEDUCT_FULL_GOLD, 0) +
                                               nvl(DEDUCT_HEAP_LIVE_ACCOUNT,
                                                   0)+nvl(DEDUCT_LOAN_PRINC, 0)+nvl(DEDUCT_LOAN_INTEREST, 0) as detain_pay
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1)) detain_pay,
           (select ls.name from dev_clm.t_customer tc ,dev_clm.t_life_state ls where tc.customer_id = cc.insured_id and tc.live_status = ls.code and rownum=1) as live_status,
            (select tcb.bene_name from dev_clm.t_claim_bene tcb where  tcp.bene_id = tcb.bene_id and rownum=1) as bene_name,
            decode((select count(*) from dev_clm.t_survey_apply sa where cc.case_id = sa.case_id and rownum=1),0,'否','是') as survey_flag,
              (select count(*) from dev_clm.t_survey_apply sa where cc.case_id = sa.case_id) as survey_num,
            
             case
                  when (select sc.positive_flag
                          from dev_clm.t_survey_conclusion sc
                         where sc.apply_id in (select tsa.apply_id from dev_clm.t_survey_apply tsa where tsa.case_id=cc.case_id)
                           and rownum = 1) = 1 then
                   '阳性'
                  when (select sc.positive_flag
                          from dev_clm.t_survey_conclusion sc
                         where sc.apply_id in (select tsa.apply_id from dev_clm.t_survey_apply tsa where tsa.case_id=cc.case_id)
                           and rownum = 1) = 0 then
                   '阴性'
                  else
                   ''
                end positive_flag,   
                (select TO_CHAR(WM_CONCAT(tsc.survey_by_id1))
                  from dev_clm.t_survey_course tsc
                 where tsc.apply_id in
                       (select apply_id
                          from dev_clm.t_survey_apply tsa
                         where tsa.case_id = cc.case_id)) survey_by_id1_name,          
                (select tsa.apply_date
                   from dev_clm.t_survey_apply tsa
                  where tsa.apply_id =
                        (select min(tsp.apply_id)
                           from dev_clm.t_survey_apply tsp
                          where tsp.case_id = cc.case_id)) as first_apply_date,
                (select tsa.apply_date
                   from dev_clm.t_survey_apply tsa
                  where tsa.apply_id =
                        (select max(tsp.apply_id)
                           from dev_clm.t_survey_apply tsp
                          where tsp.case_id = cc.case_id)) as last_apply_date,
                (select count(tsf.survey_fee) from dev_clm.t_survey_fee tsf, dev_clm.t_survey_apply tca where tsf.apply_id=tca.apply_id and tca.case_id=cc.case_id and rownum=1) survey_fee,
    
            cc.audit_time,
            cc.end_case_time,
            decode(pa.fee_status, 01,(select pm.name from dev_clm.t_pay_mode pm  where pm.code = pa.pay_mode and rownum=1), '') as pay_mode,
            decode(cb.TREAT_TYPE,'0','门诊','1','住院','') cure_status,
              (select th.hospital_name from dev_clm.t_Hospital th
                               where th.hospital_code = (select CURE_HOSPITAL from dev_clm.t_claim_case where case_id=cc.case_id and rownum=1)) hospital_name,
            case when (select CURE_HOSPITAL from dev_clm.t_claim_case where case_id=cc.case_id) is null then ''
                 when (select chs.is_designated from dev_clm.t_claim_hospital_service chs where chs.hospital_code = cb.hospital_code and rownum=1) is not null 
               then  decode((select chs.is_designated from dev_clm.t_claim_hospital_service chs where chs.hospital_code = cc.cure_hospital and rownum=1),0,'否','是')  else '否' end as is_designated ,
            decode(cm.policy_type,1,'个人')as policy_type,
            decode(pa.fee_status, 01,pa.due_time, '') as due_time,
            cc.accident_detail,
            (select ad.detail_desc from dev_clm.t_accident_detail ad where cc.accident_detail = ad.detail_code and rownum=1) as addident_desc,
            car.acc_result1,
            (select a1.name from dev_clm.t_accident1 a1 where car.acc_result1 = a1.code and rownum=1) as acc_name1,
            car.acc_result2,
            (select a2.name from dev_clm.t_accident2 a2 where car.acc_result2 = a2.code and ROWNUM = 1) as acc_name2,
            case when length(cm.organ_code)>3 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,4)) end as second_organ_code,
            case when  length(cm.organ_code)>5 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,6)) end as third_organ_code,
            case when  length(cm.organ_code)>7 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,8)) end as fourth_organ_code,
            tca.clmt_name,
            (select lpr.relation_name from dev_clm.T_LA_PH_RELA lpr where  tca.clmt_insur_relation = lpr.relation_code)as clmt_insur_relation,
            tca.clmt_mp,
            decode(cl.resist_flag, 0, '否','','否','是') resist_flag,
            (select count(*) from dev_clm.t_claim_uw tcu where cl.case_id = tcu.case_id ) as uw_sum,
            cc.case_flag as case_flag_1
      from 
                        dev_clm.t_claim_liab cl,
            dev_clm.t_claim_case cc ,
            dev_clm.t_contract_master cm,
            dev_clm.t_claim_busi_prod cbp,
            dev_clm.t_claim_sub_case csc,
            dev_clm.t_claim_liab_bill_relation clbr,
            dev_clm.t_claim_bill cb,
            dev_clm.t_claim_accident ca,
            dev_clm.t_claim_applicant tca,
            dev_clm.t_survey_apply sa,
            dev_clm.t_claim_accident_result car,
            dev_clm.t_claim_pay tcp ,
            dev_clm.t_prem_arap pa,
            dev_clm.t_claim_adjust_busi cab,
            dev_clm.t_contract_product cp,
            dev_clm.t_contract_busi_prod tcbp

      where 
                    cl.case_id = cc.case_id

            and
                          cc.case_id = cm.case_id
            and
                          cm.cur_flag='1'
            and
                          cl.policy_id = cm.policy_id

            and
                          cc.case_id = cbp.case_id
            and
                          cm.policy_id = cbp.policy_id
            and
                          cl.busi_item_id = cbp.busi_item_id

            and
                          cc.case_id = csc.case_id
            and
                          cl.sub_case_id = csc.sub_case_id  
                          
            and
                          cl.claim_liab_id = clbr.claim_liab_id(+)  
                          
            and
                          clbr.bill_id = cb.bill_id(+)
            and
                          cc.accident_id = ca.accident_id(+)
            and
                          cc.case_id = tca.case_id(+)
            and
                          cc.case_id = sa.case_id(+)
            and
                          cc.case_id = car.case_id(+)
            and
                          cc.case_id = tcp.case_id
            and
                          cm.policy_id = tcp.policy_id(+)
            and
                          cbp.busi_item_id = tcp.busi_item_id
            and
                          cc.case_no = pa.business_code
            and
                    cbp.busi_item_id = cab.busi_item_id(+)
            and 
                    cbp.busi_item_id = cp.busi_item_id(+)       
              and
                    cl.busi_item_id = tcbp.busi_item_id        
            and           cc.case_status = '80'         
		   ]]>
	       <if test="sear_organ_code != null and sear_organ_code != ''"><![CDATA[ AND cm.ORGAN_CODE like  #{sear_organ_code}||'%' ]]></if>
	       <if test="sear_sta_begin_date != null and sear_sta_begin_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) >= #{sear_sta_begin_date} ]]></if>
	       <if test="sear_sta_end_date != null and sear_sta_end_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) <= #{sear_sta_end_date} ]]></if>
		   <if test="sear_busi_item_id != null and sear_busi_item_id != ''"><![CDATA[ AND TCBP.busi_prd_id = #{sear_busi_item_id} ]]></if>
		   <if test="policyType != null and policyType != ''"><![CDATA[ AND cm.policy_type = #{policyType} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag != ''"><![CDATA[ AND CC.CASE_FLAG = #{sear_case_flag} 		    ]]></if>
		   <if test="sear_claim_type != null and sear_claim_type != ''"><![CDATA[ AND CSC.CLAIM_TYPE = #{sear_claim_type} ]]></if>
		   <if test="sear_liab_conclusion != null and sear_liab_conclusion != ''"><![CDATA[ AND CL.LIAB_CONCLUSION = #{sear_liab_conclusion} ]]></if>
		   <if test="user_id != null and user_id != ''">
		   <![CDATA[ 	AND   EXISTS(select UPORGAN_CODE from  T_UDMP_ORG_REL  TUOR
                         WHERE TUOR.UPORGAN_CODE=CC.ORGAN_CODE
                         start with TUOR.UPORGAN_CODE=( select ORGAN_CODE from   T_UDMP_USER WHERE USER_ID=#{user_id})
                         connect by prior TUOR.ORGAN_CODE=TUOR.UPORGAN_CODE
                      )   ]]></if>
		    <![CDATA[) ]]>
	</select>	
	查询调查案件的清单
	<select id="queryClaimPayRejectListOfSurveyForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[ SELECT B.* FROM(
					  SELECT C.*,ROWNUM RN
					  FROM (select distinct   
            (select TCAGENT.AGENT_NAME from  dev_clm.T_CONTRACT_AGENT TCAGENT  WHERE TCAGENT.CASE_ID=CC.CASE_ID AND TCAGENT.POLICY_CODE=CM.POLICY_CODE
                 AND TCAGENT.CUR_FLAG='1' AND ROWNUM=1
                 ) agent_name,
                 (select TCAGENT.Agent_Organ_Code from  dev_clm.T_CONTRACT_AGENT TCAGENT  WHERE TCAGENT.CASE_ID=CC.CASE_ID AND TCAGENT.POLICY_CODE=CM.POLICY_CODE
                 AND TCAGENT.CUR_FLAG='1' AND ROWNUM=1
                 ) Agent_Organ_Code,
                        cm.organ_code  as organ_code, 
            (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code=cm.organ_code and rownum=1)        as organ_name,
              ca.accident_no,
            cc.case_no,
            cm.policy_code,
            (SELECT CL.NAME FROM DEV_CLM.T_CASE_LEVEL CL WHERE CC.CASE_FLAG = CL.CODE and rownum=1) as case_flag_name,
            cbp.busi_prod_code,
            (select bp.product_name_sys from dev_pds.t_business_product bp where bp.product_code_sys = cbp.busi_prod_code and rownum=1) as busi_name,
            (case when ((select bp.cover_period_type from dev_pds.t_business_product bp where bp.product_code_sys = cbp.busi_prod_code )='0') then '长险' else '短险' end)as busi_flag,
            (select ct.name from dev_clm.t_claim_type ct where ct.code = csc.claim_type and rownum=1) as claim_name,
            cl.liab_code,
                        cl.liab_name,
            (select tc.customer_name from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1) as customer_name,
                        (select tc.mobile_tel from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1) as mobile_tel,
            FLOOR(TO_NUMBER(SYSDATE -(select tc.customer_birthday from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1)) / 365) AS AGE,
            (select tc.customer_birthday
                                                   from dev_clm.t_customer tc
                                                  where tc.customer_id =
                                                        cc.insured_id and rownum=1) as customer_birthday,
                        (select tg.gender_desc from dev_clm.t_customer tc,dev_clm.t_gender tg where tc.customer_id = cc.insured_id and tc.customer_gender = tg.gender_code AND ROWNUM=1) as gender,
            (case when cm.initial_validate_date is not null then cm.initial_validate_date else cm.validdate_date end) initial_validate_date,
            (select a.validate_date from dev_clm.t_contract_busi_prod a where a.copy_date = csc.claim_date and a.cur_flag=0 and a.busi_item_id = tcbp.busi_item_id AND ROWNUM=1) validdate_date,
                ca.acc_date,
            csc.claim_date,
                cb.treat_start,
              (select ca.acc_desc from dev_clm.t_claim_accident ca where ca.accident_id = cc.accident_id and rownum=1) as acc_desc,
            cc.rptr_name,
            cc.rptr_mp,
            cc.rptr_time,
            cc.sign_time,
              (select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.signer_id and rownum=1) as signer_name,
            cc.registe_time,
            (select cn.name from dev_clm.t_claim_nature cn where cn.code = csc.acc_reason and rownum=1) as acc_reason_desc,
                       nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.register_id and rownum=1),
             (CASE WHEN CC.CASE_STATUS = '80' AND  CC.IS_BPO =1 THEN 'BPO_CLM'
              ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) END ) )
            as register_name,
                        nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) ,
              (CASE WHEN (CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID is null ) OR (CC.CASE_FLAG =4) THEN 'AUTO'
            ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) END  ))  
            as auditor_name,
            (select cad.name from dev_clm.t_claim_audit_decision cad where cad.code = cc.audit_decision and rownum=1) as audit_decision,
            nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.approver_id and rownum=1), 
                ( CASE WHEN CC.CASE_STATUS = '80' AND CC.APPROVER_ID is null AND CC.AUDITOR_ID is not null THEN 'AUTO'
              WHEN (CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID is null ) OR (CC.CASE_FLAG =4) THEN ''
            ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.approver_id and rownum=1) END  ))  
              as approver_name,
                        ( CASE WHEN CC.CASE_STATUS = '80' AND CC.APPROVER_ID is null AND CC.AUDITOR_ID is not null THEN cc.audit_time
            ELSE cc.approve_time END  )
              as approve_time,
            nvl(cc.trustee_name,'空值')as trustee_name,
            case when cc.trustee_name is null then cc.trustee_mp || '0' else cc.trustee_mp end as trustee_mp,
            case when (cc.trustee_type !=  '2') then  (select uo.organ_name from DEV_PAS.T_AGENT ta,DEV_PAS.T_UDMP_ORG uo where  ta.agent_code = cc.trustee_code and ta.agent_organ_code = uo.organ_code  AND ROWNUM=1) end as trustee_organ_name,
            (select cld.name from dev_clm.t_claim_liab_decision cld where cl.liab_conclusion = cld.code and rownum=1) as liab_conclusion,
            decode(cc.case_flag,'4','是','否') as case_flag,
            (select sum(cb.sum_amount) from dev_clm.t_claim_bill cb where cl.case_id = cb.case_id group by cl.case_id )sum_amount,
            nvl(cb.deduct_amount,0) deduct_amount,
           (select ADJUST_PAY
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1) as ADJUST_PAY,
                                case when cl.liab_conclusion=5 then cl.adjust_pay else 0 end as REJECT_PAY,
                                (select FINAL_PAY_AMOUNT
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1) as PAY_AMOUNT,
                                (select tcar.name from dev_clm.T_CLAIM_AUDIT_REJECT tcar where tcar.code = cl.reject_code) reject_reason,
                                
                                   to_char((select nvl(RETURNS_PAY_PREM, 0) +
                                               nvl(FINISH_INTEREST, 0) +
                                               nvl(MARGIN_BACK, 0) +
                                               nvl(NO_COLLAR_BOND, 0) +
                                               nvl(RETURNS_UNEXPIRED_PREM, 0) +
                                               nvl(CASH_DIVIDEND, 0) +
                                               nvl(CASH_DIVIDEND_INTEREST, 0) as return_pay
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1)) return_pay,
                                to_char((select nvl(DEDUCT_PREM, 0) +
                                               nvl(DEDUCT_RESK_PREM, 0) +
                                               nvl(DEDUCT_POLICY_MANA_FEE, 0) +
                                               nvl(DEDUCT_ANNUITY_SURVIVAL_FUND,
                                                   0) +
                                               nvl(DEDUCT_FULL_GOLD, 0) +
                                               nvl(DEDUCT_HEAP_LIVE_ACCOUNT,
                                                   0)+nvl(DEDUCT_LOAN_PRINC, 0)+nvl(DEDUCT_LOAN_INTEREST, 0) as detain_pay
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1)) detain_pay,
           (select ls.name from dev_clm.t_customer tc ,dev_clm.t_life_state ls where tc.customer_id = cc.insured_id and tc.live_status = ls.code and rownum=1) as live_status,
            (select tcb.bene_name from dev_clm.t_claim_bene tcb where  tcp.bene_id = tcb.bene_id and rownum=1) as bene_name,
            decode((select count(*) from dev_clm.t_survey_apply sa where cc.case_id = sa.case_id and rownum=1),0,'否','是') as survey_flag,
              (select count(*) from dev_clm.t_survey_apply sa where cc.case_id = sa.case_id) as survey_num,
            
             case
                  when (select sc.positive_flag
                          from dev_clm.t_survey_conclusion sc
                         where sc.apply_id in (select tsa.apply_id from dev_clm.t_survey_apply tsa where tsa.case_id=cc.case_id)
                           and rownum = 1) = 1 then
                   '阳性'
                  when (select sc.positive_flag
                          from dev_clm.t_survey_conclusion sc
                         where sc.apply_id in (select tsa.apply_id from dev_clm.t_survey_apply tsa where tsa.case_id=cc.case_id)
                           and rownum = 1) = 0 then
                   '阴性'
                  else
                   ''
                end positive_flag,   
                (select TO_CHAR(WM_CONCAT(tsc.survey_by_id1))
                  from dev_clm.t_survey_course tsc
                 where tsc.apply_id in
                       (select apply_id
                          from dev_clm.t_survey_apply tsa
                         where tsa.case_id = cc.case_id)) survey_by_id1_name,          
                (select tsa.apply_date
                   from dev_clm.t_survey_apply tsa
                  where tsa.apply_id =
                        (select min(tsp.apply_id)
                           from dev_clm.t_survey_apply tsp
                          where tsp.case_id = cc.case_id)) as first_apply_date,
                (select tsa.apply_date
                   from dev_clm.t_survey_apply tsa
                  where tsa.apply_id =
                        (select max(tsp.apply_id)
                           from dev_clm.t_survey_apply tsp
                          where tsp.case_id = cc.case_id)) as last_apply_date,
                (select count(tsf.survey_fee) from dev_clm.t_survey_fee tsf, dev_clm.t_survey_apply tca where tsf.apply_id=tca.apply_id and tca.case_id=cc.case_id and rownum=1) survey_fee,
    
            cc.audit_time,
            cc.end_case_time,
            decode(pa.fee_status, 01,(select pm.name from dev_clm.t_pay_mode pm  where pm.code = pa.pay_mode and rownum=1), '') as pay_mode,
            decode(cb.TREAT_TYPE,'0','门诊','1','住院','') cure_status,
              (select th.hospital_name from dev_clm.t_Hospital th
                               where th.hospital_code = (select CURE_HOSPITAL from dev_clm.t_claim_case where case_id=cc.case_id and rownum=1)) hospital_name,
            case when (select CURE_HOSPITAL from dev_clm.t_claim_case where case_id=cc.case_id) is null then ''
                 when (select chs.is_designated from dev_clm.t_claim_hospital_service chs where chs.hospital_code = cb.hospital_code and rownum=1) is not null 
               then  decode((select chs.is_designated from dev_clm.t_claim_hospital_service chs where chs.hospital_code = cc.cure_hospital and rownum=1),0,'否','是')  else '否' end as is_designated ,
            decode(cm.policy_type,1,'个人')as policy_type,
            decode(pa.fee_status, 01,pa.due_time, '') as due_time,
            cc.accident_detail,
            (select ad.detail_desc from dev_clm.t_accident_detail ad where cc.accident_detail = ad.detail_code and rownum=1) as addident_desc,
            car.acc_result1,
            (select a1.name from dev_clm.t_accident1 a1 where car.acc_result1 = a1.code and rownum=1) as acc_name1,
            car.acc_result2,
            (select a2.name from dev_clm.t_accident2 a2 where car.acc_result2 = a2.code and ROWNUM = 1) as acc_name2,
            case when length(cm.organ_code)>3 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,4)) end as second_organ_code,
            case when  length(cm.organ_code)>5 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,6)) end as third_organ_code,
            case when  length(cm.organ_code)>7 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,8)) end as fourth_organ_code,
            tca.clmt_name,
            (select lpr.relation_name from dev_clm.T_LA_PH_RELA lpr where  tca.clmt_insur_relation = lpr.relation_code)as clmt_insur_relation,
            tca.clmt_mp,
            decode(cl.resist_flag, 0, '否','','否','是') resist_flag,
            (select count(*) from dev_clm.t_claim_uw tcu where cl.case_id = tcu.case_id ) as uw_sum,
            cc.case_flag as case_flag_1
      from 
                        dev_clm.t_claim_liab cl,
            dev_clm.t_claim_case cc ,
            dev_clm.t_contract_master cm,
            dev_clm.t_claim_busi_prod cbp,
            dev_clm.t_claim_sub_case csc,
            dev_clm.t_claim_liab_bill_relation clbr,
            dev_clm.t_claim_bill cb,
            dev_clm.t_claim_accident ca,
            dev_clm.t_claim_applicant tca,
            dev_clm.t_survey_apply sa,
            dev_clm.t_claim_accident_result car,
            dev_clm.t_claim_pay tcp ,
            dev_clm.t_prem_arap pa,
            dev_clm.t_claim_adjust_busi cab,
            dev_clm.t_contract_product cp,
            dev_clm.t_contract_busi_prod tcbp

      where 
                    cl.case_id = cc.case_id

            and
                          cc.case_id = cm.case_id
            and
                          cm.cur_flag='1'
            and
                          cl.policy_id = cm.policy_id

            and
                          cc.case_id = cbp.case_id
            and
                          cm.policy_id = cbp.policy_id
            and
                          cl.busi_item_id = cbp.busi_item_id

            and
                          cc.case_id = csc.case_id
            and
                          cl.sub_case_id = csc.sub_case_id  
                          
            and
                          cl.claim_liab_id = clbr.claim_liab_id(+)  
                          
            and
                          clbr.bill_id = cb.bill_id(+)
            and
                          cc.accident_id = ca.accident_id(+)
            and
                          cc.case_id = tca.case_id(+)
            and
                          cc.case_id = sa.case_id(+)
            and
                          cc.case_id = car.case_id(+)
            and
                          cc.case_id = tcp.case_id
            and
                          cm.policy_id = tcp.policy_id(+)
            and
                          cbp.busi_item_id = tcp.busi_item_id
            and
                          cc.case_no = pa.business_code
            and
                    cbp.busi_item_id = cab.busi_item_id(+)
            and 
                    cbp.busi_item_id = cp.busi_item_id(+)       
              and
                    cl.busi_item_id = tcbp.busi_item_id        
            and           cc.case_status = '80'        
		   ]]>
		    <if test="sear_organ_code != null and sear_organ_code != ''"><![CDATA[ AND cm.ORGAN_CODE like  #{sear_organ_code}||'%' ]]></if>
	       <if test="sear_sta_begin_date != null and sear_sta_begin_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) >= #{sear_sta_begin_date} ]]></if>
	       <if test="sear_sta_end_date != null and sear_sta_end_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) <= #{sear_sta_end_date} ]]></if>
		   <if test="sear_busi_item_id != null and sear_busi_item_id != ''"><![CDATA[ AND TCBP.busi_prd_id = #{sear_busi_item_id} ]]></if>
		   <if test="policyType != null and policyType != ''"><![CDATA[ AND cm.policy_type = #{policyType} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag != ''"><![CDATA[ AND CC.CASE_FLAG = #{sear_case_flag} 		    ]]></if>
		   <if test="sear_claim_type != null and sear_claim_type != ''"><![CDATA[ AND CSC.CLAIM_TYPE = #{sear_claim_type} ]]></if>
		   <if test="sear_liab_conclusion != null and sear_liab_conclusion != ''"><![CDATA[ AND CL.LIAB_CONCLUSION = #{sear_liab_conclusion} ]]></if>
		   <if test="user_id != null and user_id != ''">
		   <![CDATA[ 	AND   EXISTS(select UPORGAN_CODE from  T_UDMP_ORG_REL  TUOR
                         WHERE TUOR.UPORGAN_CODE=CC.ORGAN_CODE
                         start with TUOR.UPORGAN_CODE=( select ORGAN_CODE from   T_UDMP_USER WHERE USER_ID=#{user_id})
                         connect by prior TUOR.ORGAN_CODE=TUOR.UPORGAN_CODE
                      )   ]]></if>
		     <![CDATA[)C WHERE  ROWNUM <= #{LESS_NUM}  ]]>
		   <![CDATA[ )B WHERE B.RN > #{GREATER_NUM}  ]]>
	</select>
	
	二核案件总数
		<select id="findClaimPayRejectListOfUwTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[    SELECT count(1)
					  FROM (select distinct   
            (select TCAGENT.AGENT_NAME from  dev_clm.T_CONTRACT_AGENT TCAGENT  WHERE TCAGENT.CASE_ID=CC.CASE_ID AND TCAGENT.POLICY_CODE=CM.POLICY_CODE
                 AND TCAGENT.CUR_FLAG='1' AND ROWNUM=1
                 ) agent_name,
                 (select TCAGENT.Agent_Organ_Code from  dev_clm.T_CONTRACT_AGENT TCAGENT  WHERE TCAGENT.CASE_ID=CC.CASE_ID AND TCAGENT.POLICY_CODE=CM.POLICY_CODE
                 AND TCAGENT.CUR_FLAG='1' AND ROWNUM=1
                 ) Agent_Organ_Code,
                        cm.organ_code  as organ_code, 
            (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code=cm.organ_code and rownum=1)        as organ_name,
              ca.accident_no,
            cc.case_no,
            cm.policy_code,
            (SELECT CL.NAME FROM DEV_CLM.T_CASE_LEVEL CL WHERE CC.CASE_FLAG = CL.CODE and rownum=1) as case_flag_name,
            cbp.busi_prod_code,
            (select bp.product_name_sys from dev_pds.t_business_product bp where bp.product_code_sys = cbp.busi_prod_code and rownum=1) as busi_name,
            (case when ((select bp.cover_period_type from dev_pds.t_business_product bp where bp.product_code_sys = cbp.busi_prod_code )='0') then '长险' else '短险' end)as busi_flag,
            (select ct.name from dev_clm.t_claim_type ct where ct.code = csc.claim_type and rownum=1) as claim_name,
            cl.liab_code,
                        cl.liab_name,
            (select tc.customer_name from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1) as customer_name,
                        (select tc.mobile_tel from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1) as mobile_tel,
            FLOOR(TO_NUMBER(SYSDATE -(select tc.customer_birthday from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1)) / 365) AS AGE,
            (select tc.customer_birthday
                                                   from dev_clm.t_customer tc
                                                  where tc.customer_id =
                                                        cc.insured_id and rownum=1) as customer_birthday,
                        (select tg.gender_desc from dev_clm.t_customer tc,dev_clm.t_gender tg where tc.customer_id = cc.insured_id and tc.customer_gender = tg.gender_code AND ROWNUM=1) as gender,
            (case when cm.initial_validate_date is not null then cm.initial_validate_date else cm.validdate_date end) initial_validate_date,
            (select a.validate_date from dev_clm.t_contract_busi_prod a where a.copy_date = csc.claim_date and a.cur_flag=0 and a.busi_item_id = tcbp.busi_item_id AND ROWNUM=1) validdate_date,
                ca.acc_date,
            csc.claim_date,
                cb.treat_start,
              (select ca.acc_desc from dev_clm.t_claim_accident ca where ca.accident_id = cc.accident_id and rownum=1) as acc_desc,
            cc.rptr_name,
            cc.rptr_mp,
            cc.rptr_time,
            cc.sign_time,
              (select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.signer_id and rownum=1) as signer_name,
            cc.registe_time,
            (select cn.name from dev_clm.t_claim_nature cn where cn.code = csc.acc_reason and rownum=1) as acc_reason_desc,
                       nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.register_id and rownum=1),
             (CASE WHEN CC.CASE_STATUS = '80' AND  CC.IS_BPO =1 THEN 'BPO_CLM'
              ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) END ) )
            as register_name,
                        nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) ,
              (CASE WHEN (CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID is null ) OR (CC.CASE_FLAG =4) THEN 'AUTO'
            ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) END  ))  
            as auditor_name,
            (select cad.name from dev_clm.t_claim_audit_decision cad where cad.code = cc.audit_decision and rownum=1) as audit_decision,
            nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.approver_id and rownum=1), 
                ( CASE WHEN CC.CASE_STATUS = '80' AND CC.APPROVER_ID is null AND CC.AUDITOR_ID is not null THEN 'AUTO'
              WHEN (CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID is null ) OR (CC.CASE_FLAG =4) THEN ''
            ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.approver_id and rownum=1) END  ))  
              as approver_name,
                        ( CASE WHEN CC.CASE_STATUS = '80' AND CC.APPROVER_ID is null AND CC.AUDITOR_ID is not null THEN cc.audit_time
            ELSE cc.approve_time END  )
              as approve_time,
            nvl(cc.trustee_name,'空值')as trustee_name,
            case when cc.trustee_name is null then cc.trustee_mp || '0' else cc.trustee_mp end as trustee_mp,
            case when (cc.trustee_type !=  '2') then  (select uo.organ_name from DEV_PAS.T_AGENT ta,DEV_PAS.T_UDMP_ORG uo where  ta.agent_code = cc.trustee_code and ta.agent_organ_code = uo.organ_code  AND ROWNUM=1) end as trustee_organ_name,
            (select cld.name from dev_clm.t_claim_liab_decision cld where cl.liab_conclusion = cld.code and rownum=1) as liab_conclusion,
            decode(cc.case_flag,'4','是','否') as case_flag,
            (select sum(cb.sum_amount) from dev_clm.t_claim_bill cb where cl.case_id = cb.case_id group by cl.case_id )sum_amount,
            nvl(cb.deduct_amount,0) deduct_amount,
           (select ADJUST_PAY
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1) as ADJUST_PAY,
                                case when cl.liab_conclusion=5 then cl.adjust_pay else 0 end as REJECT_PAY,
                                (select FINAL_PAY_AMOUNT
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1) as PAY_AMOUNT,
                                (select tcar.name from dev_clm.T_CLAIM_AUDIT_REJECT tcar where tcar.code = cl.reject_code) reject_reason,
                                
                                   to_char((select nvl(RETURNS_PAY_PREM, 0) +
                                               nvl(FINISH_INTEREST, 0) +
                                               nvl(MARGIN_BACK, 0) +
                                               nvl(NO_COLLAR_BOND, 0) +
                                               nvl(RETURNS_UNEXPIRED_PREM, 0) +
                                               nvl(CASH_DIVIDEND, 0) +
                                               nvl(CASH_DIVIDEND_INTEREST, 0) as return_pay
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1)) return_pay,
                                to_char((select nvl(DEDUCT_PREM, 0) +
                                               nvl(DEDUCT_RESK_PREM, 0) +
                                               nvl(DEDUCT_POLICY_MANA_FEE, 0) +
                                               nvl(DEDUCT_ANNUITY_SURVIVAL_FUND,
                                                   0) +
                                               nvl(DEDUCT_FULL_GOLD, 0) +
                                               nvl(DEDUCT_HEAP_LIVE_ACCOUNT,
                                                   0)+nvl(DEDUCT_LOAN_PRINC, 0)+nvl(DEDUCT_LOAN_INTEREST, 0) as detain_pay
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1)) detain_pay,
           (select ls.name from dev_clm.t_customer tc ,dev_clm.t_life_state ls where tc.customer_id = cc.insured_id and tc.live_status = ls.code and rownum=1) as live_status,
            (select tcb.bene_name from dev_clm.t_claim_bene tcb where  tcp.bene_id = tcb.bene_id and rownum=1) as bene_name,
            decode((select count(*) from dev_clm.t_survey_apply sa where cc.case_id = sa.case_id and rownum=1),0,'否','是') as survey_flag,
              (select count(*) from dev_clm.t_survey_apply sa where cc.case_id = sa.case_id) as survey_num,
            
             case
                  when (select sc.positive_flag
                          from dev_clm.t_survey_conclusion sc
                         where sc.apply_id in (select tsa.apply_id from dev_clm.t_survey_apply tsa where tsa.case_id=cc.case_id)
                           and rownum = 1) = 1 then
                   '阳性'
                  when (select sc.positive_flag
                          from dev_clm.t_survey_conclusion sc
                         where sc.apply_id in (select tsa.apply_id from dev_clm.t_survey_apply tsa where tsa.case_id=cc.case_id)
                           and rownum = 1) = 0 then
                   '阴性'
                  else
                   ''
                end positive_flag,   
                (select TO_CHAR(WM_CONCAT(tsc.survey_by_id1))
                  from dev_clm.t_survey_course tsc
                 where tsc.apply_id in
                       (select apply_id
                          from dev_clm.t_survey_apply tsa
                         where tsa.case_id = cc.case_id)) survey_by_id1_name,          
                (select tsa.apply_date
                   from dev_clm.t_survey_apply tsa
                  where tsa.apply_id =
                        (select min(tsp.apply_id)
                           from dev_clm.t_survey_apply tsp
                          where tsp.case_id = cc.case_id)) as first_apply_date,
                (select tsa.apply_date
                   from dev_clm.t_survey_apply tsa
                  where tsa.apply_id =
                        (select max(tsp.apply_id)
                           from dev_clm.t_survey_apply tsp
                          where tsp.case_id = cc.case_id)) as last_apply_date,
                (select count(tsf.survey_fee) from dev_clm.t_survey_fee tsf, dev_clm.t_survey_apply tca where tsf.apply_id=tca.apply_id and tca.case_id=cc.case_id and rownum=1) survey_fee,
    
            cc.audit_time,
            cc.end_case_time,
            decode(pa.fee_status, 01,(select pm.name from dev_clm.t_pay_mode pm  where pm.code = pa.pay_mode and rownum=1), '') as pay_mode,
            decode(cb.TREAT_TYPE,'0','门诊','1','住院','') cure_status,
              (select th.hospital_name from dev_clm.t_Hospital th
                               where th.hospital_code = (select CURE_HOSPITAL from dev_clm.t_claim_case where case_id=cc.case_id and rownum=1)) hospital_name,
            case when (select CURE_HOSPITAL from dev_clm.t_claim_case where case_id=cc.case_id) is null then ''
                 when (select chs.is_designated from dev_clm.t_claim_hospital_service chs where chs.hospital_code = cb.hospital_code and rownum=1) is not null 
               then  decode((select chs.is_designated from dev_clm.t_claim_hospital_service chs where chs.hospital_code = cc.cure_hospital and rownum=1),0,'否','是')  else '否' end as is_designated ,
            decode(cm.policy_type,1,'个人')as policy_type,
            decode(pa.fee_status, 01,pa.due_time, '') as due_time,
            cc.accident_detail,
            (select ad.detail_desc from dev_clm.t_accident_detail ad where cc.accident_detail = ad.detail_code and rownum=1) as addident_desc,
            car.acc_result1,
            (select a1.name from dev_clm.t_accident1 a1 where car.acc_result1 = a1.code and rownum=1) as acc_name1,
            car.acc_result2,
            (select a2.name from dev_clm.t_accident2 a2 where car.acc_result2 = a2.code and ROWNUM = 1) as acc_name2,
            case when length(cm.organ_code)>3 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,4)) end as second_organ_code,
            case when  length(cm.organ_code)>5 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,6)) end as third_organ_code,
            case when  length(cm.organ_code)>7 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,8)) end as fourth_organ_code,
            tca.clmt_name,
            (select lpr.relation_name from dev_clm.T_LA_PH_RELA lpr where  tca.clmt_insur_relation = lpr.relation_code)as clmt_insur_relation,
            tca.clmt_mp,
            decode(cl.resist_flag, 0, '否','','否','是') resist_flag,
            (select count(*) from dev_clm.t_claim_uw tcu where cl.case_id = tcu.case_id ) as uw_sum,
            cc.case_flag as case_flag_1
      from 
                        dev_clm.t_claim_liab cl,
            dev_clm.t_claim_case cc ,
            dev_clm.t_contract_master cm,
            dev_clm.t_claim_busi_prod cbp,
            dev_clm.t_claim_sub_case csc,
            dev_clm.t_claim_liab_bill_relation clbr,
            dev_clm.t_claim_bill cb,
            dev_clm.t_claim_accident ca,
            dev_clm.t_claim_applicant tca,
            dev_clm.t_survey_apply sa,
            dev_clm.t_claim_accident_result car,
            dev_clm.t_claim_pay tcp ,
            dev_clm.t_prem_arap pa,
            dev_clm.t_claim_adjust_busi cab,
            dev_clm.t_contract_product cp,
            dev_clm.t_contract_busi_prod tcbp

      where 
                    cl.case_id = cc.case_id

            and
                          cc.case_id = cm.case_id
            and
                          cm.cur_flag='1'
            and
                          cl.policy_id = cm.policy_id

            and
                          cc.case_id = cbp.case_id
            and
                          cm.policy_id = cbp.policy_id
            and
                          cl.busi_item_id = cbp.busi_item_id

            and
                          cc.case_id = csc.case_id
            and
                          cl.sub_case_id = csc.sub_case_id  
                          
            and
                          cl.claim_liab_id = clbr.claim_liab_id(+)  
                          
            and
                          clbr.bill_id = cb.bill_id(+)
            and
                          cc.accident_id = ca.accident_id(+)
            and
                          cc.case_id = tca.case_id(+)
            and
                          cc.case_id = sa.case_id(+)
            and
                          cc.case_id = car.case_id(+)
            and
                          cc.case_id = tcp.case_id
            and
                          cm.policy_id = tcp.policy_id(+)
            and
                          cbp.busi_item_id = tcp.busi_item_id
            and
                          cc.case_no = pa.business_code
            and
                    cbp.busi_item_id = cab.busi_item_id(+)
            and 
                    cbp.busi_item_id = cp.busi_item_id(+)       
              and
                    cl.busi_item_id = tcbp.busi_item_id        
            and           cc.case_status = '80'   
		   ]]>
	       <if test="sear_organ_code != null and sear_organ_code != ''"><![CDATA[ AND cm.ORGAN_CODE like  #{sear_organ_code}||'%' ]]></if>
	       <if test="sear_sta_begin_date != null and sear_sta_begin_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) >= #{sear_sta_begin_date} ]]></if>
	       <if test="sear_sta_end_date != null and sear_sta_end_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) <= #{sear_sta_end_date} ]]></if>
		   <if test="sear_busi_item_id != null and sear_busi_item_id != ''"><![CDATA[ AND TCBP.busi_prd_id = #{sear_busi_item_id} ]]></if>
		   <if test="policyType != null and policyType != ''"><![CDATA[ AND cm.policy_type = #{policyType} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag != ''"><![CDATA[ AND CC.CASE_FLAG = #{sear_case_flag} 		    ]]></if>
		   <if test="sear_claim_type != null and sear_claim_type != ''"><![CDATA[ AND CSC.CLAIM_TYPE = #{sear_claim_type} ]]></if>
		   <if test="sear_liab_conclusion != null and sear_liab_conclusion != ''"><![CDATA[ AND CL.LIAB_CONCLUSION = #{sear_liab_conclusion} ]]></if>
		   <if test="user_id != null and user_id != ''">
		   <![CDATA[ 	AND   EXISTS(select UPORGAN_CODE from  T_UDMP_ORG_REL  TUOR
                         WHERE TUOR.UPORGAN_CODE=CC.ORGAN_CODE
                         start with TUOR.UPORGAN_CODE=( select ORGAN_CODE from   T_UDMP_USER WHERE USER_ID=#{user_id})
                         connect by prior TUOR.ORGAN_CODE=TUOR.UPORGAN_CODE
                      )   ]]></if>
		    <![CDATA[) ]]>
	</select>
	查询二核案件的清单
	<select id="queryClaimCaseOfUwForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[ SELECT B.* FROM(
					  SELECT C.*,ROWNUM RN
					  FROM (select distinct   
            (select TCAGENT.AGENT_NAME from  dev_clm.T_CONTRACT_AGENT TCAGENT  WHERE TCAGENT.CASE_ID=CC.CASE_ID AND TCAGENT.POLICY_CODE=CM.POLICY_CODE
                 AND TCAGENT.CUR_FLAG='1' AND ROWNUM=1
                 ) agent_name,
                 (select TCAGENT.Agent_Organ_Code from  dev_clm.T_CONTRACT_AGENT TCAGENT  WHERE TCAGENT.CASE_ID=CC.CASE_ID AND TCAGENT.POLICY_CODE=CM.POLICY_CODE
                 AND TCAGENT.CUR_FLAG='1' AND ROWNUM=1
                 ) Agent_Organ_Code,
                        cm.organ_code  as organ_code, 
            (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code=cm.organ_code and rownum=1)        as organ_name,
              ca.accident_no,
            cc.case_no,
            cm.policy_code,
            (SELECT CL.NAME FROM DEV_CLM.T_CASE_LEVEL CL WHERE CC.CASE_FLAG = CL.CODE and rownum=1) as case_flag_name,
            cbp.busi_prod_code,
            (select bp.product_name_sys from dev_pds.t_business_product bp where bp.product_code_sys = cbp.busi_prod_code and rownum=1) as busi_name,
            (case when ((select bp.cover_period_type from dev_pds.t_business_product bp where bp.product_code_sys = cbp.busi_prod_code )='0') then '长险' else '短险' end)as busi_flag,
            (select ct.name from dev_clm.t_claim_type ct where ct.code = csc.claim_type and rownum=1) as claim_name,
            cl.liab_code,
                        cl.liab_name,
            (select tc.customer_name from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1) as customer_name,
                        (select tc.mobile_tel from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1) as mobile_tel,
            FLOOR(TO_NUMBER(SYSDATE -(select tc.customer_birthday from dev_clm.t_customer tc where tc.customer_id = cc.insured_id and rownum=1)) / 365) AS AGE,
            (select tc.customer_birthday
                                                   from dev_clm.t_customer tc
                                                  where tc.customer_id =
                                                        cc.insured_id and rownum=1) as customer_birthday,
                        (select tg.gender_desc from dev_clm.t_customer tc,dev_clm.t_gender tg where tc.customer_id = cc.insured_id and tc.customer_gender = tg.gender_code AND ROWNUM=1) as gender,
            (case when cm.initial_validate_date is not null then cm.initial_validate_date else cm.validdate_date end) initial_validate_date,
            (select a.validate_date from dev_clm.t_contract_busi_prod a where a.copy_date = csc.claim_date and a.cur_flag=0 and a.busi_item_id = tcbp.busi_item_id AND ROWNUM=1) validdate_date,
                ca.acc_date,
            csc.claim_date,
                cb.treat_start,
              (select ca.acc_desc from dev_clm.t_claim_accident ca where ca.accident_id = cc.accident_id and rownum=1) as acc_desc,
            cc.rptr_name,
            cc.rptr_mp,
            cc.rptr_time,
            cc.sign_time,
              (select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.signer_id and rownum=1) as signer_name,
            cc.registe_time,
            (select cn.name from dev_clm.t_claim_nature cn where cn.code = csc.acc_reason and rownum=1) as acc_reason_desc,
                       nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.register_id and rownum=1),
             (CASE WHEN CC.CASE_STATUS = '80' AND  CC.IS_BPO =1 THEN 'BPO_CLM'
              ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) END ) )
            as register_name,
                        nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) ,
              (CASE WHEN (CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID is null ) OR (CC.CASE_FLAG =4) THEN 'AUTO'
            ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu  where uu.user_id = cc.auditor_id and rownum=1) END  ))  
            as auditor_name,
            (select cad.name from dev_clm.t_claim_audit_decision cad where cad.code = cc.audit_decision and rownum=1) as audit_decision,
            nvl((select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.approver_id and rownum=1), 
                ( CASE WHEN CC.CASE_STATUS = '80' AND CC.APPROVER_ID is null AND CC.AUDITOR_ID is not null THEN 'AUTO'
              WHEN (CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID is null ) OR (CC.CASE_FLAG =4) THEN ''
            ELSE (select uu.real_name from DEV_PAS.T_UDMP_USER uu where uu.user_id = cc.approver_id and rownum=1) END  ))  
              as approver_name,
                        ( CASE WHEN CC.CASE_STATUS = '80' AND CC.APPROVER_ID is null AND CC.AUDITOR_ID is not null THEN cc.audit_time
            ELSE cc.approve_time END  )
              as approve_time,
            nvl(cc.trustee_name,'空值')as trustee_name,
            case when cc.trustee_name is null then cc.trustee_mp || '0' else cc.trustee_mp end as trustee_mp,
            case when (cc.trustee_type !=  '2') then  (select uo.organ_name from DEV_PAS.T_AGENT ta,DEV_PAS.T_UDMP_ORG uo where  ta.agent_code = cc.trustee_code and ta.agent_organ_code = uo.organ_code  AND ROWNUM=1) end as trustee_organ_name,
            (select cld.name from dev_clm.t_claim_liab_decision cld where cl.liab_conclusion = cld.code and rownum=1) as liab_conclusion,
            decode(cc.case_flag,'4','是','否') as case_flag,
            (select sum(cb.sum_amount) from dev_clm.t_claim_bill cb where cl.case_id = cb.case_id group by cl.case_id )sum_amount,
            nvl(cb.deduct_amount,0) deduct_amount,
           (select ADJUST_PAY
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1) as ADJUST_PAY,
                                case when cl.liab_conclusion=5 then cl.adjust_pay else 0 end as REJECT_PAY,
                                (select FINAL_PAY_AMOUNT
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1) as PAY_AMOUNT,
                                (select tcar.name from dev_clm.T_CLAIM_AUDIT_REJECT tcar where tcar.code = cl.reject_code) reject_reason,
                                
                                   to_char((select nvl(RETURNS_PAY_PREM, 0) +
                                               nvl(FINISH_INTEREST, 0) +
                                               nvl(MARGIN_BACK, 0) +
                                               nvl(NO_COLLAR_BOND, 0) +
                                               nvl(RETURNS_UNEXPIRED_PREM, 0) +
                                               nvl(CASH_DIVIDEND, 0) +
                                               nvl(CASH_DIVIDEND_INTEREST, 0) as return_pay
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1)) return_pay,
                                to_char((select nvl(DEDUCT_PREM, 0) +
                                               nvl(DEDUCT_RESK_PREM, 0) +
                                               nvl(DEDUCT_POLICY_MANA_FEE, 0) +
                                               nvl(DEDUCT_ANNUITY_SURVIVAL_FUND,
                                                   0) +
                                               nvl(DEDUCT_FULL_GOLD, 0) +
                                               nvl(DEDUCT_HEAP_LIVE_ACCOUNT,
                                                   0)+nvl(DEDUCT_LOAN_PRINC, 0)+nvl(DEDUCT_LOAN_INTEREST, 0) as detain_pay
                                          from dev_clm.t_claim_fee_detail detail
                                         where detail.claim_liab_id =
                                               cl.claim_liab_id and rownum=1)) detain_pay,
           (select ls.name from dev_clm.t_customer tc ,dev_clm.t_life_state ls where tc.customer_id = cc.insured_id and tc.live_status = ls.code and rownum=1) as live_status,
            (select tcb.bene_name from dev_clm.t_claim_bene tcb where  tcp.bene_id = tcb.bene_id and rownum=1) as bene_name,
            decode((select count(*) from dev_clm.t_survey_apply sa where cc.case_id = sa.case_id and rownum=1),0,'否','是') as survey_flag,
              (select count(*) from dev_clm.t_survey_apply sa where cc.case_id = sa.case_id) as survey_num,
            
             case
                  when (select sc.positive_flag
                          from dev_clm.t_survey_conclusion sc
                         where sc.apply_id in (select tsa.apply_id from dev_clm.t_survey_apply tsa where tsa.case_id=cc.case_id)
                           and rownum = 1) = 1 then
                   '阳性'
                  when (select sc.positive_flag
                          from dev_clm.t_survey_conclusion sc
                         where sc.apply_id in (select tsa.apply_id from dev_clm.t_survey_apply tsa where tsa.case_id=cc.case_id)
                           and rownum = 1) = 0 then
                   '阴性'
                  else
                   ''
                end positive_flag,   
                (select TO_CHAR(WM_CONCAT(tsc.survey_by_id1))
                  from dev_clm.t_survey_course tsc
                 where tsc.apply_id in
                       (select apply_id
                          from dev_clm.t_survey_apply tsa
                         where tsa.case_id = cc.case_id)) survey_by_id1_name,          
                (select tsa.apply_date
                   from dev_clm.t_survey_apply tsa
                  where tsa.apply_id =
                        (select min(tsp.apply_id)
                           from dev_clm.t_survey_apply tsp
                          where tsp.case_id = cc.case_id)) as first_apply_date,
                (select tsa.apply_date
                   from dev_clm.t_survey_apply tsa
                  where tsa.apply_id =
                        (select max(tsp.apply_id)
                           from dev_clm.t_survey_apply tsp
                          where tsp.case_id = cc.case_id)) as last_apply_date,
                (select count(tsf.survey_fee) from dev_clm.t_survey_fee tsf, dev_clm.t_survey_apply tca where tsf.apply_id=tca.apply_id and tca.case_id=cc.case_id and rownum=1) survey_fee,
    
            cc.audit_time,
            cc.end_case_time,
            decode(pa.fee_status, 01,(select pm.name from dev_clm.t_pay_mode pm  where pm.code = pa.pay_mode and rownum=1), '') as pay_mode,
            decode(cb.TREAT_TYPE,'0','门诊','1','住院','') cure_status,
              (select th.hospital_name from dev_clm.t_Hospital th
                               where th.hospital_code = (select CURE_HOSPITAL from dev_clm.t_claim_case where case_id=cc.case_id and rownum=1)) hospital_name,
            case when (select CURE_HOSPITAL from dev_clm.t_claim_case where case_id=cc.case_id) is null then ''
                 when (select chs.is_designated from dev_clm.t_claim_hospital_service chs where chs.hospital_code = cb.hospital_code and rownum=1) is not null 
               then  decode((select chs.is_designated from dev_clm.t_claim_hospital_service chs where chs.hospital_code = cc.cure_hospital and rownum=1),0,'否','是')  else '否' end as is_designated ,
            decode(cm.policy_type,1,'个人')as policy_type,
            decode(pa.fee_status, 01,pa.due_time, '') as due_time,
            cc.accident_detail,
            (select ad.detail_desc from dev_clm.t_accident_detail ad where cc.accident_detail = ad.detail_code and rownum=1) as addident_desc,
            car.acc_result1,
            (select a1.name from dev_clm.t_accident1 a1 where car.acc_result1 = a1.code and rownum=1) as acc_name1,
            car.acc_result2,
            (select a2.name from dev_clm.t_accident2 a2 where car.acc_result2 = a2.code and ROWNUM = 1) as acc_name2,
            case when length(cm.organ_code)>3 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,4)) end as second_organ_code,
            case when  length(cm.organ_code)>5 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,6)) end as third_organ_code,
            case when  length(cm.organ_code)>7 then (select uo.organ_name from DEV_PAS.T_UDMP_ORG uo where uo.organ_code = substr(cm.organ_code,1,8)) end as fourth_organ_code,
            tca.clmt_name,
            (select lpr.relation_name from dev_clm.T_LA_PH_RELA lpr where  tca.clmt_insur_relation = lpr.relation_code)as clmt_insur_relation,
            tca.clmt_mp,
            decode(cl.resist_flag, 0, '否','','否','是') resist_flag,
            (select count(*) from dev_clm.t_claim_uw tcu where cl.case_id = tcu.case_id ) as uw_sum,
            cc.case_flag as case_flag_1
      from 
                        dev_clm.t_claim_liab cl,
            dev_clm.t_claim_case cc ,
            dev_clm.t_contract_master cm,
            dev_clm.t_claim_busi_prod cbp,
            dev_clm.t_claim_sub_case csc,
            dev_clm.t_claim_liab_bill_relation clbr,
            dev_clm.t_claim_bill cb,
            dev_clm.t_claim_accident ca,
            dev_clm.t_claim_applicant tca,
            dev_clm.t_survey_apply sa,
            dev_clm.t_claim_accident_result car,
            dev_clm.t_claim_pay tcp ,
            dev_clm.t_prem_arap pa,
            dev_clm.t_claim_adjust_busi cab,
            dev_clm.t_contract_product cp,
            dev_clm.t_contract_busi_prod tcbp

      where 
                    cl.case_id = cc.case_id

            and
                          cc.case_id = cm.case_id
            and
                          cm.cur_flag='1'
            and
                          cl.policy_id = cm.policy_id

            and
                          cc.case_id = cbp.case_id
            and
                          cm.policy_id = cbp.policy_id
            and
                          cl.busi_item_id = cbp.busi_item_id

            and
                          cc.case_id = csc.case_id
            and
                          cl.sub_case_id = csc.sub_case_id  
                          
            and
                          cl.claim_liab_id = clbr.claim_liab_id(+)  
                          
            and
                          clbr.bill_id = cb.bill_id(+)
            and
                          cc.accident_id = ca.accident_id(+)
            and
                          cc.case_id = tca.case_id(+)
            and
                          cc.case_id = sa.case_id(+)
            and
                          cc.case_id = car.case_id(+)
            and
                          cc.case_id = tcp.case_id
            and
                          cm.policy_id = tcp.policy_id(+)
            and
                          cbp.busi_item_id = tcp.busi_item_id
            and
                          cc.case_no = pa.business_code
            and
                    cbp.busi_item_id = cab.busi_item_id(+)
            and 
                    cbp.busi_item_id = cp.busi_item_id(+)       
              and
                    cl.busi_item_id = tcbp.busi_item_id        
            and           cc.case_status = '80'  
		   ]]>
	       <if test="sear_organ_code != null and sear_organ_code != ''"><![CDATA[ AND cm.ORGAN_CODE like  #{sear_organ_code}||'%' ]]></if>
	       <if test="sear_sta_begin_date != null and sear_sta_begin_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) >= #{sear_sta_begin_date} ]]></if>
	       <if test="sear_sta_end_date != null and sear_sta_end_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) <= #{sear_sta_end_date} ]]></if>
		   <if test="sear_busi_item_id != null and sear_busi_item_id != ''"><![CDATA[ AND TCBP.busi_prd_id = #{sear_busi_item_id} ]]></if>
		   <if test="policyType != null and policyType != ''"><![CDATA[ AND cm.policy_type = #{policyType} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag != ''"><![CDATA[ AND CC.CASE_FLAG = #{sear_case_flag} 		    ]]></if>
		   <if test="sear_claim_type != null and sear_claim_type != ''"><![CDATA[ AND CSC.CLAIM_TYPE = #{sear_claim_type} ]]></if>
		   <if test="sear_liab_conclusion != null and sear_liab_conclusion != ''"><![CDATA[ AND CL.LIAB_CONCLUSION = #{sear_liab_conclusion} ]]></if>
		   <if test="user_id != null and user_id != ''">
		   <![CDATA[ 	AND   EXISTS(select UPORGAN_CODE from  T_UDMP_ORG_REL  TUOR
                         WHERE TUOR.UPORGAN_CODE=CC.ORGAN_CODE
                         start with TUOR.UPORGAN_CODE=( select ORGAN_CODE from   T_UDMP_USER WHERE USER_ID=#{user_id})
                         connect by prior TUOR.ORGAN_CODE=TUOR.UPORGAN_CODE
                      )   ]]></if>
		  <![CDATA[)C WHERE  ROWNUM <= #{LESS_NUM}  ]]>
		   <![CDATA[ )B WHERE B.RN > #{GREATER_NUM}  ]]>
	</select>
	 -->
	
	
	<select id="queryAgentCodeByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[select distinct ab.receive_obj, ac.busi_code,ta.agent_name,ta.agent_organ_code
   				from dev_clm.t_send_content_log ab, dev_clm.t_send_detail ac,DEV_PAS.T_AGENT ta
  				where ab.contend_id = ac.contend_id
  				and ab.receive_obj = ta.agent_code
  				and ac.busi_code = #{case_no}
  				]]>
	</select>
	
	<select id="findDescContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT * FROM (SELECT  A.AGENT_NAME, 
			A.AGENT_TYPE,A.POLICY_CODE, 
			 A.AGENT_ORGAN_CODE, A.POLICY_ID, A.AGENT_CODE, 
			A.CUR_FLAG,ROWNUM RN FROM DEV_CLM.T_CONTRACT_AGENT A WHERE A.CUR_FLAG = '1' 
			AND A.POLICY_CODE = #{policy_code}
			ORDER BY A.Insert_Time DESC) WHERE RN='1'
		]]>
	
	</select>
	<!-- 查询总应付金额，总拒付金额，总给付金额 -->
	<select id="findSumPayAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT sum(ADJUST_PAY) as sum_adjust_pay, sum(REJECT_PAY) as sum_reject_pay, sum(PAY_AMOUNT) as sum_pay_amount        FROM(
             SELECT C.*, ROWNUM RN
               FROM (SELECT CASE
                          WHEN CL.IS_WAIVED = 1 THEN
                           CL.WAIVE_AMT
                          ELSE
                           CL.ADJUST_PAY
                        END AS ADJUST_PAY,
                        CASE
                          WHEN CL.LIAB_CONCLUSION = 5 THEN
                           CL.calc_pay
                          ELSE
                           0
                        END AS REJECT_PAY,
                        CL.ACTUAL_PAY AS PAY_AMOUNT
                       FROM DEV_CLM.T_CLAIM_ACCIDENT      CA,
                            DEV_PAS.T_CUSTOMER            TC,
                            DEV_PAS.T_CONTRACT_AGENT      TCAGENT,
                            DEV_CLM.T_CLAIM_SUB_CASE      CSC,
                            DEV_CLM.T_CLAIM_PRODUCT       CP,
                            DEV_CLM.T_CLAIM_PAY           TCP,
                            DEV_PDS.T_BUSINESS_PRODUCT    BP,
                            DEV_CLM.T_CLAIM_BUSI_PROD     CBP,
                            DEV_PAS.T_CONTRACT_MASTER     CM,
                            DEV_CLM.T_CLAIM_LIAB          CL,
                            DEV_CLM.T_CLAIM_CASE          CC,
                            DEV_CLM.T_CLAIM_LIAB_DECISION CLD,
                            DEV_PAS.T_CONTRACT_BUSI_PROD  TCBP,
                            DEV_PAS.T_UDMP_USER           U,
                            DEV_PAS.T_UDMP_USER           U1
                      WHERE CC.CASE_ID = CL.CASE_ID
                        AND TCAGENT.POLICY_ID = CM.POLICY_ID
                        AND CL.POLICY_CODE = CM.POLICY_CODE
                        AND CL.CASE_ID = CBP.CASE_ID
                        AND CL.POLICY_CODE = CBP.POLICY_CODE
                        AND CL.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
                        AND CL.BUSI_PROD_CODE = CBP.BUSI_PROD_CODE
                        AND CL.CASE_ID = CSC.CASE_ID
                        AND CL.SUB_CASE_ID = CSC.SUB_CASE_ID
                        AND CC.ACCIDENT_ID = CA.ACCIDENT_ID
                        AND CL.CASE_ID = TCP.CASE_ID(+)
                        AND CL.POLICY_CODE = TCP.POLICY_CODE(+)
                        AND CL.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID(+)
                        AND CBP.CASE_ID = CP.CASE_ID
                        AND CBP.POLICY_CODE = CP.POLICY_CODE
                        AND CBP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
                        AND BP.PRODUCT_CODE_SYS = CBP.BUSI_PROD_CODE
                        AND TC.CUSTOMER_ID = CC.INSURED_ID
                        AND CL.LIAB_CONCLUSION = CLD.CODE
                        AND TCBP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
                        AND U.USER_ID = CC.AUDITOR_ID
                        AND U1.USER_ID = CC.APPROVER_ID
                        AND TCAGENT.is_current_agent = '1'
                        AND CC.CASE_STATUS = '80'
		   ]]> 
	       <if test="sear_organ_code != null and sear_organ_code != ''"><![CDATA[ AND cm.ORGAN_CODE like  #{sear_organ_code}||'%' ]]></if>
	       <if test="sear_sta_begin_date != null and sear_sta_begin_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) >= #{sear_sta_begin_date} ]]></if>
	       <if test="sear_sta_end_date != null and sear_sta_end_date != ''"><![CDATA[ and trunc(cc.END_CASE_TIME) <= #{sear_sta_end_date} ]]></if>
		   <if test="channel_code != null and channel_code != ''"><![CDATA[ and cc.channel_code = #{channel_code} ]]></if>
		   <if test="sear_busi_item_id != null and sear_busi_item_id != ''"><![CDATA[ AND CBP.BUSI_PROD_CODE = (SELECT A.PRODUCT_CODE_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT A WHERE A.BUSINESS_PRD_ID  = #{sear_busi_item_id}) ]]></if>
		   <if test="policyType != null and policyType != ''"><![CDATA[ AND cm.policy_type = #{policyType} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag != '' and sear_case_flag != 4 and sear_case_flag != 6"><![CDATA[ AND CC.CASE_FLAG = #{sear_case_flag} ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag == 4"><![CDATA[  AND ((CC.CASE_STATUS = '80' AND  CC.AUDITOR_ID  = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO')) AND CC.EASY_AUDIT_DECISION IS NOT NULL  AND CC.EASY_AUDITOR_ID IS NULL) ]]></if>
		   <if test="sear_case_flag != null and sear_case_flag == 6"><![CDATA[  AND  CC.CASE_STATUS = '80' AND CC.APPROVER_ID  = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO') AND ((CC.AUDITOR_ID  != (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO')) OR (CC.AUDITOR_ID = (SELECT US.USER_ID FROM DEV_PAS.T_UDMP_USER US WHERE US.USER_NAME ='AUTO') AND CC.EASY_AUDITOR_ID IS NOT NULL))]]></if>
		   <if test="sear_claim_type != null and sear_claim_type != ''"><![CDATA[ AND CSC.CLAIM_TYPE = #{sear_claim_type} ]]></if>
		   <if test="sear_liab_conclusion != null and sear_liab_conclusion != ''"><![CDATA[ AND CL.LIAB_CONCLUSION = #{sear_liab_conclusion} ]]></if>
		   <if test="is_uw != null and is_uw != ''"><![CDATA[ and tcu.case_id=cl.case_id  ]]></if>
		   <![CDATA[ order by cc.case_no)C WHERE 1=1 ]]> 
		   <if test="is_uw != null and is_uw != ''"><![CDATA[ C.uw_sum > 0 AND ]]></if>
		   <if test="is_survey != null and is_survey != ''"><![CDATA[ C.survey_num > 0 AND ]]></if>
		   <![CDATA[ )B WHERE 1=1 ]]>
	</select>
	<!-- 查询IT问题件操作 -->
	<select id="findAllClaimMemoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		 SELECT A.* FROM DEV_CLM.T_CLAIM_MEMO A,DEV_CLM.T_CLAIM_CASE B WHERE 1=1 AND A.CASE_ID = B.CASE_ID]]>
		 <if test=" memo_type != null and memo_type != ''  "><![CDATA[ AND A.MEMO_TYPE = #{memo_type} ]]></if>
		 <if test=" case_no  != null and case_no != ''"><![CDATA[ AND B.CASE_NO = #{case_no} ]]></if>
		<![CDATA[ ORDER BY A.update_time  ]]> 
	</select>	

	<!-- 立案外包案件清单 -->
	<select id="queryReportOutsideListPage" resultType="java.util.Map"
			parameterType="java.util.Map">
		<![CDATA[select * from 	
			(select rownum rn , A.* from 
			(select distinct
				   toc.case_no,--赔案号
		       	   cc.organ_code ||'-'|| (select tuo.organ_name  from DEV_PAS.t_udmp_org tuo where tuo.organ_code = cc.organ_code) organ_name, --机构名称
		           toc.OUTSOURCE_TYPE_CODE, --2是简易案件  其他都不是简易案件big型（T_OUTSOURCE_TYPE）
		           toc.OUTSOURCE_STATUS_CODE, --回复状态 2是已回复 big型（T_OUTSOURCE_STATE）
		           toi.outsource_name,--外包商名称
		           toc.SEND_PACKAGE_TIME,--发包日期
		           toc.RECEIVED_PACKAGE_TIME,--收包日期
		           toc.BYTE_QUANTITY, --数据量big
		           cc.MEDICAL_AMOUNT_CRITERION,
		           cc.MEDICAL_TOTAL_MONY,
		           (select TO_CHAR(WM_CONCAT(DISTINCT ct.name)) from DEV_CLM.T_CLAIM_SUB_CASE csc,DEV_CLM.T_CLAIM_TYPE ct where csc.case_id=toc.case_id and ct.code = csc.claim_type) name,
		           toc.ISSUE_TYPE_CODE ||'-'||(select tmt.name from DEV_CLM.T_MEMO_TYPE tmt where tmt.code=toc.ISSUE_TYPE_CODE) remark,--备注
		           toi.outsource_way  --外包方式
		    from DEV_CLM.t_outsource_case toc
		    left join DEV_CLM.T_OUTSOURCE_IFNO toi on toc.Outsource_Id = toi.OUTSOURCE_ID
		    left join DEV_CLM.T_CLAIM_CASE cc on toc.case_no=cc.case_no
		    where 1 = 1 and cc.case_status !=99 ]]>
		<if test=" ORGAN_CODE!=null and ORGAN_CODE!='' ">
			<![CDATA[ AND cc.ORGAN_CODE like '${ORGAN_CODE}%']]>
		</if>
		<if test=" outsource_name!=null and outsource_name!='' ">
			<![CDATA[ AND toi.outsource_name like '%${outsource_name}%']]>
		</if>
		<if test=" outsource_id!=null and outsource_id!='' ">
			<![CDATA[ AND toi.outsource_id = #{outsource_id}]]>
		</if>
		<if test=" SEND_PACKAGE_TIME_BEGIN!=null and SEND_PACKAGE_TIME_BEGIN!='' ">
			<![CDATA[ AND toc.SEND_PACKAGE_TIME >= TO_DATE(TO_CHAR(#{SEND_PACKAGE_TIME_BEGIN}, 'YYYY-MM-DD'), 'YYYY-MM-DD') ]]>
		</if>
		<if test=" SEND_PACKAGE_TIME_END!=null and SEND_PACKAGE_TIME_END!='' ">
			<![CDATA[ AND toc.SEND_PACKAGE_TIME < TO_DATE(TO_CHAR(#{SEND_PACKAGE_TIME_END}, 'YYYY-MM-DD'), 'YYYY-MM-DD') + 1 ]]>
		</if>
		<if test=" outsource_way==null or outsource_way==''">
			<![CDATA[ AND toi.outsource_way in(1,2)]]>
		</if>
		<if test=" outsource_way!=null and outsource_way!='' ">
			<![CDATA[ AND toi.outsource_way like '%${outsource_way}%' ]]>
		</if>
		<![CDATA[ order by toc.case_no )A where ROWNUM<=#{LESS_NUM} )B where B.rn>#{GREATER_NUM} ]]>
	</select>

	<select id="queryReportOutsideListTotal" resultType="java.lang.Integer"
			parameterType="java.util.Map">
		<![CDATA[select count(1) from 
			(select distinct
				   toc.case_no,--赔案号
		       	   cc.organ_code ||'-'|| (select tuo.organ_name  from DEV_PAS.t_udmp_org tuo where tuo.organ_code = cc.organ_code) organ_name, --机构名称
		           toc.OUTSOURCE_TYPE_CODE, --2是简易案件  其他都不是简易案件big型（T_OUTSOURCE_TYPE）
		           toc.OUTSOURCE_STATUS_CODE, --回复状态 2是已回复 big型（T_OUTSOURCE_STATE）
		           toi.outsource_name,--外包商名称
		           toc.SEND_PACKAGE_TIME,--发包日期
		           toc.RECEIVED_PACKAGE_TIME,--收包日期
		           toc.BYTE_QUANTITY, --数据量big
		           toc.ISSUE_TYPE_CODE ||'-'||(select tmt.name from DEV_CLM.T_MEMO_TYPE tmt where tmt.code=toc.ISSUE_TYPE_CODE) remark,--备注
		           toi.outsource_way --外包方式
		    from DEV_CLM.t_outsource_case toc
		    left join DEV_CLM.T_OUTSOURCE_IFNO toi on toc.Outsource_Id = toi.OUTSOURCE_ID
		    left join DEV_CLM.T_CLAIM_CASE cc on toc.case_no=cc.case_no
		    where 1 = 1 and cc.case_status !=99 ]]>
		<if test=" ORGAN_CODE!=null and ORGAN_CODE!='' ">
			<![CDATA[ AND cc.ORGAN_CODE like '${ORGAN_CODE}%']]>
		</if>
		<if test=" outsource_name!=null and outsource_name!='' ">
			<![CDATA[ AND toi.outsource_name like '%${outsource_name}%']]>
		</if>
		<if test=" outsource_id!=null and outsource_id!='' ">
			<![CDATA[ AND toi.outsource_id = #{outsource_id}]]>
		</if>
		<if test=" SEND_PACKAGE_TIME_BEGIN!=null and SEND_PACKAGE_TIME_BEGIN!='' ">
			<![CDATA[ AND toc.SEND_PACKAGE_TIME >= TO_DATE(TO_CHAR(#{SEND_PACKAGE_TIME_BEGIN}, 'YYYY-MM-DD'), 'YYYY-MM-DD') ]]>
		</if>
		<if test=" SEND_PACKAGE_TIME_END!=null and SEND_PACKAGE_TIME_END!='' ">
			<![CDATA[ AND toc.SEND_PACKAGE_TIME < TO_DATE(TO_CHAR(#{SEND_PACKAGE_TIME_END}, 'YYYY-MM-DD'), 'YYYY-MM-DD') + 1 ]]>
		</if>
		<if test=" outsource_way!=null and outsource_way!='' ">
			<![CDATA[ AND toi.outsource_way like '%${outsource_way}%' ]]>
		</if>
		<if test=" outsource_way==null or outsource_way=='' ">
			<![CDATA[ AND toi.outsource_way in(1,2)]]>
		</if>
		<![CDATA[)A]]>
	</select>
</mapper>

 