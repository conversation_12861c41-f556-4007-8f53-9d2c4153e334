<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.impl.offlinereport.dao.IReportOffLineTaskDao">

	<select id="queryOffLineTaskForPage" parameterType="java.util.Map"
	   resultType="java.util.Map">
	   <![CDATA[SELECT B.* FROM
	   		(SELECT A.*, ROWNUM RN FROM 
	   		(SELECT RT.LIST_ID,
		       RT.REPORT_CODE,
		       RT.CREATE_TIME,
		       RT.CREATE_BY,
		       (SELECT RS.STATUS_NAME FROM DEV_PAS.T_REPORT_OFFLINE_TASK_STATUS RS WHERE RS.STATUS_CODE = RT.STATUS) AS STATUS,
		       RT.QUERY_CONDITION,
		       RT.QUERY_CONDITION_CODE,
		       RT.FILE_DEAL_START,
		       RT.FILE_DEAL_END,
		       RT.FILE_RECORD_COUNT,
		       RT.FILE_PATH,
		       RT.FILE_NAME,
		       RT.UPDATE_TIMESTAMP,
		       RT.UPDATE_TIME,
		       RT.UPDATE_BY,
		       RT.INSERT_TIMESTAMP,
		       RT.INSERT_TIME,
		       RT.INSERT_BY，
		       U.USER_NAME,
		       U.REAL_NAME,
		       U.ORGAN_CODE,
		       O.ORGAN_NAME
		FROM QUERY.T_REPORT_OFFLINE_TASK RT 
		INNER JOIN DEV_PAS.T_UDMP_USER U ON RT.CREATE_BY=U.USER_ID
		INNER JOIN DEV_PAS.T_UDMP_ORG O ON U.ORGAN_CODE=O.ORGAN_CODE
		WHERE 1=1 ]]>
		<include refid="public_select_where"></include>
		<![CDATA[ORDER BY RT.CREATE_TIME DESC )A WHERE ROWNUM <= #{LESS_NUM})B WHERE B.RN > #{GREATER_NUM}]]>		
	</select>
	<select id="queryOffLineTaskForPageTotal" parameterType="java.util.Map" 
		resultType="java.lang.Integer">
		<![CDATA[SELECT COUNT(1) FROM QUERY.T_REPORT_OFFLINE_TASK RT
					INNER JOIN DEV_PAS.T_UDMP_USER U ON RT.CREATE_BY=U.USER_ID
					INNER JOIN DEV_PAS.T_UDMP_ORG O ON U.ORGAN_CODE=O.ORGAN_CODE
		WHERE 1=1 ]]>
		<include refid="public_select_where"></include>
	</select>
	
	<sql id="public_select_where">
		<![CDATA[
		AND RT.CREATE_TIME >= (SYSDATE-#{remain_days}) 
		AND RT.CREATE_TIME <= SYSDATE]]>
		<if test="query_creater_organ != null and query_creater_organ != '' and query_creater_organ == '01' ">
			AND EXISTS (SELECT ORGAN_CODE FROM T_UDMP_ORG_REL TD WHERE TD.ORGAN_CODE=U.ORGAN_CODE AND (TD.ORGAN_GRADE = '02' OR TD.ORGAN_CODE = '86'))
		</if>
		<if test="(report_code =='CheBaoListNB'  or report_code == 'ChengBaoListNB' or report_code =='HuiZhiListNB'  or report_code == 'YuShouListNB') and organ_code == '86'">AND U.ORGAN_CODE = #{organ_code}</if>
		<if test="query_creater_organ != null and query_creater_organ != '' and query_creater_organ != '01' ">
			AND U.ORGAN_CODE LIKE #{organ_code} || '%'
		</if>
		<!-- <if test="create_by != null and create_by != '' ">AND RT.CREATE_BY = #{create_by}</if> -->
		<if test="create_by != null and create_by != '' and query_creater_organ != null and query_creater_organ != '' and query_creater_organ == '02' and report_code == null">
	    AND (RT.CREATE_BY = #{create_by} OR U.ORGAN_CODE = #{organ_code} ) 
		</if>
		<if test="create_by != null and create_by != '' and query_creater_organ != null and query_creater_organ != '' and query_creater_organ == '02' and report_code != null and report_code != 'payRejectList'  and report_code != 'CsEndorsePayEBusiListPA' and report_code != 'premPayList'">
	    AND (RT.CREATE_BY = #{create_by} OR U.ORGAN_CODE = #{organ_code} ) 
		</if>
		<if test="create_by != null and create_by != '' and query_creater_organ != null and query_creater_organ != '' and query_creater_organ == '02' and report_code != null and report_code == 'CsEndorsePayEBusiListPA' ">
	    AND (RT.CREATE_BY = #{create_by} OR ( U.ORGAN_CODE LIKE #{organ_code} || '%' AND U.ORGAN_CODE != #{organ_code} ))  
		</if>
		<if test="task_source != null and task_source != '' ">AND RT.TASK_SOURCE = #{task_source}</if>
		<if test="report_code != null and report_code != '' ">AND RT.REPORT_CODE = #{report_code}</if>
		<if test="report_code != null and report_code == 'ClaimDetailListCLM' and organ_code != null and organ_code == '86' ">
			AND U.ORGAN_CODE = #{organ_code}
		</if>
		<if test="task_source != null and task_source == 'group' and organ_code != null and organ_code == '86' ">
			AND U.ORGAN_CODE = #{organ_code}
		</if>
		<if test="report_code != null and report_code == 'ClaimDetailListCLM' and organ_code != null and organ_code == '86' ">
			AND U.ORGAN_CODE = #{organ_code}
		</if>
		   	    <if test="report_code != null and report_code == 'CliamRiskEventBroadListCLM' and organ_code != null and organ_code == '86' ">
            AND U.ORGAN_CODE = #{organ_code}
   	    </if>
   	    <!-- 135886 add  -->
		<if test="create_by != null and create_by != '' and queryCreaterOwner ">
			and RT.CREATE_BY = #{create_by} 
		</if>
		<!-- 115443 add -->
		<if test="create_by != null and create_by != '' and report_code == 'CsEndorsePhoneChecklistCUS' ">
			and RT.CREATE_BY = #{create_by} 
		</if>
		<!-- 116569 add -->
		<if test="create_by != null and create_by != '' and report_code == 'CsImgImpListCUS' ">
			and RT.CREATE_BY = #{create_by} 
		</if>
		<!-- 100889 add -->
		<if test="create_by != null and create_by != '' and report_code == 'CsSuspendList' ">
			and RT.CREATE_BY = #{create_by} 
		</if>
		 <!-- 120220 -->
   	    <if test="report_code != null and report_code == 'ClaimLiabCloseListCLM' and organ_code != null and organ_code == '86' ">
            AND U.ORGAN_CODE = #{organ_code}
   	    </if>
   	    <!-- 178954 -->
   	    <if test="report_code != null and report_code == 'payRejectList' and organ_code != null and organ_code == '86' ">
            AND U.ORGAN_CODE = #{organ_code}
   	    </if>
   	    <if test="report_code != null and report_code == 'premPayList' and organ_code != null and organ_code == '86' ">
            AND U.ORGAN_CODE = #{organ_code}
   	    </if>
	</sql>
	
	<select id="findUnDealsReportTaskCount" parameterType="java.util.Map"  resultType="java.lang.Integer">
		<![CDATA[
		  SELECT COUNT(1) FROM  QUERY.T_REPORT_OFFLINE_TASK RT 
		WHERE 1=1 ]]>
		<include refid="findUnDealsConditon"/>  
	</select>
	<select id="findUnDealsReportTaskList" parameterType="java.util.Map"  resultType="java.util.Map">
		<![CDATA[
		  SELECT RT.LIST_ID,
		       RT.REPORT_CODE,
		       RT.CREATE_TIME,
		       RT.CREATE_BY,
		       RT.STATUS,
		       RT.QUERY_CONDITION,
		       RT.QUERY_CONDITION_CODE,
		       RT.FILE_DEAL_START,
		       RT.FILE_DEAL_END,
		       RT.FILE_RECORD_COUNT,
		       RT.FILE_PATH,
		       RT.FILE_NAME,
		       OC.REPORT_CNAME AS REPORT_NAME,
		       RT.UPDATE_TIMESTAMP,
		       RT.UPDATE_TIME,
		       RT.UPDATE_BY,
		       RT.INSERT_TIMESTAMP,
		       RT.INSERT_TIME,
		       RT.INSERT_BY,
		       U.USER_NAME,
		       U.REAL_NAME,
		       U.Email			     
		FROM QUERY.T_REPORT_OFFLINE_TASK RT 
		INNER JOIN QUERY.T_REPORT_OFFLINE_CONFIG OC ON RT.REPORT_CODE=OC.REPORT_CODE
		INNER JOIN DEV_PAS.T_UDMP_USER U ON RT.CREATE_BY=U.USER_ID
		WHERE 1=1 ]]>
		<include refid="findUnDealsConditon"/>  
		<![CDATA[ORDER BY RT.CREATE_TIME ASC ]]>	
	</select>
	<sql id="findUnDealsConditon">
		<if test="report_code != null and report_code != '' "> AND RT.REPORT_CODE = #{report_code}</if>
		 AND RT.status=1
		 <if test=" modnum != null and modnum != '' and start != null and start != '' or modnum >= 0 or start >= 0"><![CDATA[AND MOD(RT.LIST_ID, #{modnum})= #{start}  ]]></if> 
		
	</sql>
	
	<select id="findUnDealsReportTaskListForRPCSTH" parameterType="java.util.Map"  resultType="java.util.Map">
		<![CDATA[
		  SELECT RT.LIST_ID,
		       RT.REPORT_CODE,
		       RT.CREATE_TIME,
		       RT.CREATE_BY,
		       RT.STATUS,
		       RT.QUERY_CONDITION,
		       RT.QUERY_CONDITION_CODE,
		       RT.FILE_DEAL_START,
		       RT.FILE_DEAL_END,
		       RT.FILE_RECORD_COUNT,
		       RT.FILE_PATH,
		       RT.FILE_NAME,
		       OC.REPORT_CNAME AS REPORT_NAME,
		       RT.UPDATE_TIMESTAMP,
		       RT.UPDATE_TIME,
		       RT.UPDATE_BY,
		       RT.INSERT_TIMESTAMP,
		       RT.INSERT_TIME,
		       RT.INSERT_BY,
		       U.USER_NAME,
		       U.REAL_NAME		     
		FROM QUERY.T_REPORT_OFFLINE_TASK RT 
		INNER JOIN QUERY.T_REPORT_OFFLINE_CONFIG OC ON RT.REPORT_CODE=OC.REPORT_CODE
		INNER JOIN DEV_PAS.T_UDMP_USER U ON RT.CREATE_BY=U.USER_ID
		WHERE 1=1 ]]>
		<include refid="findUnDealsConditonForRPCSTH"/>  
		<![CDATA[ORDER BY RT.CREATE_TIME ASC ]]>	
	</select>
	<sql id="findUnDealsConditonForRPCSTH">
		<if test="report_code != null and report_code != '' "> AND RT.REPORT_CODE = #{report_code}</if>
		 AND RT.status=1
		 
		
	</sql>
	
	
	
	
	
	
	
	<select id="findReportConfig" parameterType="java.util.Map"  resultType="java.util.Map">
		<![CDATA[
		SELECT OC.CONFIG_ID,
	       OC.REPORT_CODE,
	       OC.REPORT_CNAME,
	       OC.OFFLINE_SUBMIT_INTERVAL,
	       OC.OFFLINE_QUERY_SCOPE,
	       OC.ONLINE_QUERY_SCOPE,
	       OC.BATCH_JOB_ID
	  FROM QUERY.T_REPORT_OFFLINE_CONFIG OC WHERE 1=1]]>
	  <if test="report_code != null and report_code != '' "> AND OC.REPORT_CODE = #{report_code}</if>
	</select>
	<select id="qry_getOrganGrade" parameterType="java.util.Map"  resultType="java.util.Map">
		<![CDATA[
		    SELECT UR.ORGAN_ID,UR.ORGAN_CODE,UR.ORGAN_GRADE FROM T_UDMP_ORG_REL UR WHERE UR.ORGAN_CODE = #{organ_code}
		]]>
	</select>
	
	<!-- 修改操作 -->
    <update id="updateReportTask" parameterType="java.util.Map">
        UPDATE QUERY.T_REPORT_OFFLINE_TASK 
            set STATUS = #{status,jdbcType=VARCHAR} ,
            <if test="file_path != null and file_path != ''"> FILE_PATH = #{file_path,jdbcType=VARCHAR} ,</if>
             <if test="file_record_count != null"> FILE_RECORD_COUNT = #{file_record_count,jdbcType=NUMERIC} ,</if>
             <if test="file_name != null and file_name != '' ">FILE_NAME = #{file_name, jdbcType=VARCHAR},</if>
             <if test="file_deal_start_str != null and file_deal_start_str!='' "> FILE_DEAL_START = TO_Date(#{file_deal_start_str, jdbcType=VARCHAR},'yyyy-MM-dd HH24:MI:SS') ,</if>
            <if test="file_deal_end_str != null and file_deal_end_str!='' "> FILE_DEAL_END = TO_Date(#{file_deal_end_str, jdbcType=VARCHAR},'yyyy-MM-dd HH24:MI:SS') ,</if>
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,
                UPDATE_TIME=CURRENT_TIMESTAMP,
                UPDATE_BY=11111111
        WHERE LIST_ID = #{list_id}
    </update>
     <!-- 添加操作 -->
    <insert id="addReportTask" useGeneratedKeys="false" parameterType="java.util.Map">
 		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
            keyProperty="list_id">
            SELECT QUERY.S_REPORT_OFFLINE_TASK.NEXTVAL FROM
            DUAL
        </selectKey>       
		<![CDATA[
            INSERT INTO QUERY.T_REPORT_OFFLINE_TASK(
                LIST_ID,REPORT_CODE,CREATE_TIME,CREATE_BY,STATUS,QUERY_CONDITION,QUERY_CONDITION_CODE,
                FILE_DEAL_START,FILE_DEAL_END,FILE_RECORD_COUNT,FILE_PATH,FILE_NAME,INSERT_BY,TASK_SOURCE,INSERT_TIME,INSERT_TIMESTAMP,
                 UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP ) 
            VALUES (
            	#{list_id, jdbcType=NUMERIC},
                #{report_code, jdbcType=VARCHAR},
                sysdate,
                #{create_by, jdbcType=NUMERIC},
                #{status, jdbcType=VARCHAR},
                #{query_condition, jdbcType=VARCHAR},
                #{query_condition_code, jdbcType=VARCHAR},
                #{file_deal_start, jdbcType=DATE},
                #{file_deal_end, jdbcType=DATE},
                #{file_record_count, jdbcType=NUMERIC},
                #{file_path, jdbcType=VARCHAR},
                #{file_name, jdbcType=VARCHAR},
                #{insert_by, jdbcType=NUMERIC},
                #{task_source, jdbcType=VARCHAR},
                sysdate,
				CURRENT_TIMESTAMP, 
				#{update_by, jdbcType=NUMERIC},
				sysdate,
				CURRENT_TIMESTAMP
                ) 
         ]]>
    </insert>
    
    <select id="queryTaskByCreaterAndQryCondition" parameterType="java.util.Map"  resultType="java.util.Map">
		<![CDATA[
		SELECT RT.LIST_ID,RT.CREATE_TIME,RT.CREATE_BY,RT.QUERY_CONDITION,RT.STATUS FROM  QUERY.T_REPORT_OFFLINE_TASK RT
		  WHERE RT.LIST_ID = (SELECT MAX(T.LIST_ID) FROM  QUERY.T_REPORT_OFFLINE_TASK T 
		WHERE T.REPORT_CODE = #{report_code} AND T.QUERY_CONDITION = #{query_condition}
		 ]]>
		 <if test="create_by != null and create_by != '' ">
		 	AND T.CREATE_BY = #{create_by}
		 </if>
		  <if test=" report_code != null and report_code != ''">
		 	<if test="report_code=='ServicePolicyListPA' or report_code=='AlreadyFailureListPA' or report_code=='KuaYueListPA' or report_code=='BankTransferFlopListPA' or report_code=='BankTransferSuccessListPA' or report_code=='ImntForeverFailureListPA' or report_code=='ImminentFailureListPA' or report_code=='PremListPA' or report_code=='PremPaidDayListPA'">
		 		<![CDATA[ AND T.STATUS!='4']]>
		 	</if>
		 </if>
		 <![CDATA[)]]>
	</select>
	
	<select id="queryOffLineTaskByListId" parameterType="java.util.Map"  resultType="java.util.Map">
		<![CDATA[
		  SELECT RT.LIST_ID,RT.STATUS,RT.CREATE_BY,RT.FILE_PATH,RT.FILE_NAME,OC.REPORT_CNAME AS REPORT_NAME,RT.REPORT_CODE AS REPORT_CODE FROM  QUERY.T_REPORT_OFFLINE_TASK RT 
		  INNER JOIN QUERY.T_REPORT_OFFLINE_CONFIG OC ON RT.REPORT_CODE=OC.REPORT_CODE
		WHERE 1=1 AND RT.LIST_ID = #{list_id}]]>
	</select>
	
	<select id="findReportCount" parameterType="java.util.Map"  resultType="java.lang.Integer">
	select count(1) from  QUERY.T_REPORT_OFFLINE_TASK where file_path=#{file_path} and file_name=#{file_name}
	</select>
	
	<select id="findOverDaysReportTaskList" parameterType="java.util.Map"  resultType="java.util.Map">
		<![CDATA[
		SELECT * FROM (
	SELECT RT.LIST_ID,TO_DATE(TO_CHAR(RT.CREATE_TIME, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS CREATE_TIME,
	       RT.REPORT_CODE,
	       RT.FILE_PATH,
	       RT.FILE_NAME,
	       RT.STATUS
	  FROM QUERY.T_REPORT_OFFLINE_TASK RT
	 WHERE 1 = 1
	   AND RT.CREATE_TIME <= SYSDATE -#{overdays}
	   AND RT.STATUS!='5' AND RT.STATUS='3'
	   ORDER BY CREATE_TIME,REPORT_CODE
	) A 
	]]>
	<if test=" modnum != null and modnum != '' and start != null and start != '' or modnum >= 0 or start >= 0"><![CDATA[WHERE MOD(A.LIST_ID, #{modnum})= #{start}  ]]></if>
	</select>
	<!-- 116569 -->
	<delete id="deleteOfflineTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM QUERY.T_REPORT_OFFLINE_TASK WHERE LIST_ID = #{list_id} ]]>
	</delete>
</mapper>