<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
        http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 离线报表任务-->
	<bean id="reportOffLineTaskUCC"
		class="com.nci.tunan.qry.impl.offlinereport.ucc.impl.ReportOffLineTaskUCCImpl">
		<property name="reportOffLineTaskService" ref="reportOffLineTaskService" />
	</bean>
	<!-- 离线任务批处理JOB -->
	<bean id="reportOffLinePremListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLinePremListPATaskJob">
     	 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="paPremListDao" ref="premListDao"></property>
    </bean> 
    <bean id="reportOffLinePremListCSSTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLinePremListCSSTaskJob">
     	 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    </bean> 
    <bean id="ftpReportFileDealOverDaysJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.FtpReportFileDealOverDaysJob">
    </bean> 
    <bean id="uWConditionContractBusiProdBatchJob"  class="com.nci.tunan.qry.impl.batch.UWConditionContractBusiProdBatchJob">
    	 <property name="uwConditionDao" ref="uwConditionDao"></property>
    </bean>
	<!-- 离线任务批处理JOB续保险种转换清单 -->
    <bean id="reportOffLineCsEndorseRPClistPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseRPClistPATaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="csEndorseRPClistDao" ref="csEndorseRPClistDao"></property>
    </bean>  
    <!-- 离线任务批处理JOB短期健康险处理清单 -->
    <bean id="reportOffLineCsEndorseSTHlistPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseSTHlistPATaskJob">
     	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="csEndorseSTHlistDao" ref="csEndorseSTHlistDao"></property>
    </bean>  
     <!-- 离线任务批处理JOB迁移保单清单 -->
	<bean id="reportOffLineMigrationPolicyListTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineMigrationPolicyListTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="migrationPolicyListDao" ref="migrationPolicyListDao"></property>
    </bean> 
      <!-- 离线任务批处理JOB退保清单 -->
	<bean id="reportOffLinePaCtListTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLinePaCtListTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="paCtListDao" ref="paCtListDao"></property>
    </bean> 
    <!-- 离线任务批处理JOB大额保单清单 -->
	<bean id="reportOffLineLargeInsuranceListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineLargeInsuranceListPATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="largeInsuranceListDao" ref="largeInsuranceListDao"></property>
    </bean> 
     <!-- 离线任务批处理JOB非客户申请业务清单 -->
    <bean id="reportOffLineCsEndorseNcaBusinessDetaillistTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseNcaBusinessDetaillistTaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="businessDetaillistDao" ref="csEndorseBusinessDetaillistDao"></property>
    	<property name="udmpOrgRelDao" ref="csUdmpOrgRelDao"></property>
    	<property name="csEndorseBusinessDetaillistService" ref="csEndorseBusinessDetaillistService"></property>
    </bean>
    <!-- 离线任务批处理JOB保全业务明细清单 -->
    <bean id="reportOffLineCsEndorseBusinessDetaillistTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseBusinessDetaillistTaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="businessDetaillistDao" ref="csEndorseBusinessDetaillistDao"></property>
    	<property name="udmpOrgRelDao" ref="csUdmpOrgRelDao"></property>
    	<property name="csEndorseBusinessDetaillistService" ref="csEndorseBusinessDetaillistService"></property>
    </bean> 
    
     <!-- 离线任务批处理JOB保全工作时效清单 -->
    <bean id="reportOffLineWorkEffectListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineWorkEffectListPATaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	
    	<property name="csWorkEffectListDao" ref="csWorkEffectListDao"></property>
    	<property name="PSPlistDao" ref="csEndorsePSPlistDao"></property>
    	
    	<property name="csEndorseHistoryInvalidatelistUCC" ref="csEndorseHistoryInvalidatelistUCC"></property> 	
    </bean>
     
    <!-- 离线任务批处理JOB保全扫描清单 -->
    <bean id="reportOffLineCsEndorseScanlistTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseScanlistTaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="scanlistDao" ref="csEndorseScanlistDao"></property>
    </bean>  
    <!-- 离线任务批处理JOB保单质押贷款清单 -->
    <bean id="reportOffLineCsEndorseLRlistTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseLRlistTaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="PSPlistDao" ref="csEndorsePSPlistDao"></property>
    	<property name="csEndorseHistoryInvalidatelistUCC" ref="csEndorseHistoryInvalidatelistUCC"></property>
    </bean>  
    	<!-- 离线任务批处理JOB应收清单（个人中介渠道） -->
	<bean id="reportOffLinePersonaPremListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLinePremListPATaskJob">
     	 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="paPremListDao" ref="premListDao"></property>
    </bean> 
    <!-- 离线任务批处理JOB续期保费实收日结(个人中介渠道) -->
	<bean id="reportOffLinePersonalPremPaidDayListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLinePremPaidDayListPATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="paRebewalPremPaidDayListDao" ref="rebewalPremPaidDayListDao"></property>
     	 <property name="renewalBankTransferFlopListDao" ref="renewalBankTransferFlopListDao"></property>
    </bean> 
     <!-- 离线任务批处理承保明细清单  -->
    <bean id="ReportOffLineChengBaoListPATaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineChengBaoListPATaskJob">
     <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="nbFlowBillDao" ref="nbFlowBillDao"></property>
     	 <property name="nbFlowBillUcc" ref="nbFlowBillUcc"></property>
    </bean>
     <!--  离线任务批处理预收清单 -->
    <bean id="ReportOffLineYuShouListPATaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineYuShouListPATaskJob">
     <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     <property name="nbFlowBillDao" ref="nbFlowBillDao"></property>
    </bean> 
        <!-- 离线任务批处理回执清单  -->
   <bean id="ReportOffLineHuiZhiListPATaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineHuiZhiListPATaskJob">
     <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    <property name="receiptReportDao" ref="NB_receiptReportDao"></property>
    </bean>
        <!--   离线任务批处理撤保清单  -->
    <bean id="ReportOffLineCheBaoistPATaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCheBaoistPATaskJob">
     <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="nbFlowBillDao" ref="nbFlowBillDao"></property>
        <property name="nbFlowBillUcc" ref="nbFlowBillUcc"></property>
    </bean>
     <!-- 离线任务批处理银代机构业绩清单 -->
    <bean id="reportOffLineBankPerformanceListTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineBankPerformanceListTaskJob">
     	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
		<property name="bankPerformanceListDao" ref="bankPerformanceListDao"></property>
    </bean>  
    <!-- 离线任务批处理银代机构业绩清单（含非实时） -->
    <bean id="reportOffLineBankDirectSellirListTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineBankDirectSellirListTaskJob">
     	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
		<property name="bankDirectSellirListDao" ref="bankDirectSellirListDao"></property>
    </bean>  
    
	<!-- 离线任务批处理案件明细清单 -->
    <bean id="reportOffLineClaimCaseDetailListTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineClaimCaseDetailListTaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="clmDetailListDao" ref="clmDetailListDao"></property>
    </bean>


	<!-- 离线任务-银代财富业务清单(新契约)  -->
	<bean id="reportOffLineBankWealthNbActionTaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineBankWealthNbActionTaskJob">
		<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
		<property name="bankWealthNbDao" ref="bankWealthNbDao"></property>
	</bean>

	<!-- 离线任务-银代财富业务清单（续期）  -->
	<bean id="reportOffLineBankWealthRNActionTaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineBankWealthRNActionTaskJob">
		<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
		<property name="bankWealthRNDao" ref="bankWealthRNDao"></property>
	</bean>

    <!-- 90086离线任务批处理配置信息start -->
	<!-- 离线任务批处理JOB已失效清单 -->
	<bean id="reportOffLineAlreadyFailurePATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineAlreadyFailurePATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="alreadyFailureDao" ref="alreadyFailureDao"></property>
    </bean>
    <bean id="reportOffLineRenewalPayListTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineRenewalPayListTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
		 <property name="renewalPayListDao" ref="renewalPayListDao"></property>
    </bean>  
    <!-- 离线任务批处理JOB即将失效清单 -->
	<bean id="reportOffLineImntFailureListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineImntFailureListPATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
 	     <property name="imminentFailureDao" ref="imminentFailureDao"></property>
    </bean> 
    <!-- 离线任务批处理JOB即将永久失效清单 -->
	<bean id="reportOffLineImntForeverFailureListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineImntForeverFailureListPATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
 	     <property name="imntForeverFailureDao" ref="imntForeverFailureDao"></property>
    </bean> 
    <!-- 离线任务批处理JOB续期保费实收日结 -->
	<bean id="reportOffLinePremPaidDayListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLinePremPaidDayListPATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="paRebewalPremPaidDayListDao" ref="rebewalPremPaidDayListDao"></property>
     	 <property name="renewalBankTransferFlopListDao" ref="renewalBankTransferFlopListDao"></property>
    </bean>
    <!-- 离线任务批处理JOB续期银行划款不成功 -->
	<bean id="reportOffLineBankTransferFlopPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineBankTransferFlopPATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="renewalBankTransferFlopListDao" ref="renewalBankTransferFlopListDao"></property>
    </bean> 
    <!-- 离线任务批处理JOB续期银行划款成功 -->
	<bean id="reportOffLineBankTransferSuccessPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineBankTransferSuccessPATaskJob">
     	 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="renewalBankTransferSuccessListDao" ref="renewalBankTransferSuccessListDao"></property>
     	 <property name="renewalBankTransferFlopListDao" ref="renewalBankTransferFlopListDao"></property>
    </bean> 
    <!-- 离线任务批处理JOB服务保单 -->
	<bean id="reportOffLineServicePolicyListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineServicePolicyListPATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="servicePolicyListDao" ref="servicePolicyListDao"></property>
    </bean> 
    <!-- 离线任务批处理JOB跨月清单 -->
	<bean id="reportOffLineKuaYueListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineKuaYueListPATaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="kuaYueListDao" ref="kuaYueListDao"></property>
    </bean> 
    <!-- 90086离线任务批处理配置信息end -->
    
    <!-- 团体值域-离线报表配置信息start -->
    <!-- 团体值域报表-已失效清单 -->
	<bean id="reportOffLineAlreadyFailureGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLineAlreadyFailureGroupTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="alreadyFailureDao" ref="alreadyFailureDao"></property>
    </bean> 
    <!-- 团体值域报表-即将失效清单 -->
	<bean id="reportOffLineImntFailureListGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLineImntFailureListGroupTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
 	     <property name="imminentFailureDao" ref="imminentFailureDao"></property>
    </bean> 
	<!-- 团体值域报表-即将永久失效清单 -->
	<bean id="reportOffLineImntForeverFailureListGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLineImntForeverFailureListGroupTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
 	     <property name="imntForeverFailureDao" ref="imntForeverFailureDao"></property>
    </bean> 
	<!-- 团体值域报表-应收清单 -->
	<bean id="reportOffLinePremListGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLinePremListGroupTaskJob">
     	 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="paPremListDao" ref="premListDao"></property>
    </bean> 
    <!-- 团体值域报表-续期保费实收日结清单 -->
	<bean id="reportOffLinePremPaidDayListGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLinePremPaidDayListGroupTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="paRebewalPremPaidDayListDao" ref="rebewalPremPaidDayListDao"></property>
     	 <property name="renewalBankTransferFlopListDao" ref="renewalBankTransferFlopListDao"></property>
    </bean> 
    <!-- 离线任务批处理JOB续期银行划款不成功 -->
	<bean id="reportOffLineBankTransferFlopGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLineBankTransferFlopGroupTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="renewalBankTransferFlopListDao" ref="renewalBankTransferFlopListDao"></property>
    </bean> 
     <!-- 团体值域报表-续期银行划款成功清单 -->
	<bean id="reportOffLineBankTransferSuccessGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLineBankTransferSuccessGroupTaskJob">
     	 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="renewalBankTransferSuccessListDao" ref="renewalBankTransferSuccessListDao"></property>
     	 <property name="renewalBankTransferFlopListDao" ref="renewalBankTransferFlopListDao"></property>
    </bean> 
    <!-- 团体值域报表-服务保单清单 -->
	<bean id="reportOffLineServicePolicyListGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLineServicePolicyListGroupTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="servicePolicyListDao" ref="servicePolicyListDao"></property>
    </bean> 
    <!-- 团体值域报表-跨月清单 -->
	<bean id="reportOffLineKuaYueListGroupTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.group.ReportOffLineKuaYueListGroupTaskJob">
		 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="kuaYueListDao" ref="kuaYueListDao"></property>
    </bean> 
    <!-- 团体值域-离线报表配置信息end -->
    <!-- 离线任务-银代渠道业务业绩清单（含直销单）   -->
	<bean id="reportOffLineBankBusinessListTaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineBankBusinessListTaskJob">
		<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
		<property name="bankBusinessListDao" ref="bankBusinessListDao"></property>
	</bean>
	<!-- 102098 离线任务批处理直连案件案件清单 -->
    <bean id="reportOffLineClaimDirectCaseListTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineClaimDirectCaseListTaskJob">
    	<property name="claimDirectCaseListService" ref="claimDirectCaseListService"></property>
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    </bean>
    <!-- 离线任务批处理JOB返盘成功清单 -->
	<bean id="reportOffLineReturnDiskSuccessPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineReturnDiskSuccessPATaskJob">
     	 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="bankBackSucessfulDao" ref="bankBackSucessfulDao"></property>
     	 <property name="renewalBankTransferFlopListDao" ref="renewalBankTransferFlopListDao"></property>
    </bean>
    <!-- 116569 本地上传影像业务清单  -->
	<bean id="reportOffLineCsImgImpListTaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsImgImpListTaskJob">
		<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
		<property name="csImgImpListDao" ref="csImgImpListDao"></property>
	</bean>
	<!-- 135886 年金(满期金)领取清单   -->
	<bean id="reportOffLineCsEndorseAGListJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseAGListJob">
		<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
	</bean>
    <!-- 92663 离线任务批处理风险事件广播清单 -->
    <bean id="claimRiskBroadcastJobBatch"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCliamRiskEventBroadListTaskJob">
    	<property name="cliamRiskEventBroadListDao" ref="cliamRiskEventBroadListDao"></property>
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    </bean>
    
        <!-- 离线任务批处理JOB应收清单(保全挂起清单) -->
	<bean id="ReportOffLineSuspendInputListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineSuspendInputListPATaskJob">
     	 <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
     	 <property name="csSuspendListDao" ref="csSuspendListDao"></property>
    </bean>
        <!--离线任务批处理保全付费至非权益人清单  -->
     <bean id="reportOffLineCsPayToNonStakeHoldersJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsPayToNonStakeHoldersJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="csPayToNonStakeHoldersListDao" ref="csPayToNonStakeHoldersListDao" ></property>
    	<property name="csPayToNonStakeHoldersListUCC" ref="csPayToNonStakeHoldersListUCC" />
    </bean> 
       <!--离线任务批处理保单质押贷款到期未偿清单  -->
     <bean id="reportOffLineCsEndorseSAlistJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseSAlistJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="csEndorsePSPlistDao" ref="csEndorsePSPlistDao"></property>
    </bean> 
    <!-- 115443 移动电话核验清单  -->
	<bean id="reportOffLineCsEndorsePhoneChecklistTaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorsePhoneChecklistTaskJob">
		<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
	</bean>
	<bean id="ReportOffLineClaimLiabCloseListJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineClaimLiabCloseListJob">
			<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
			<property name="claimCaseDao" ref="ClaimCaseDao"></property>
	</bean>    
	<!-- 离线任务批处理 人像识别清单 -->
	<bean id="reportOffLineCsCpmCheckListTaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsCpmCheckListTaskJob">
		<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
		<property name="csPhotoCompareDao" ref="csPhotoCompareDao"></property>
	</bean>
	
	<!-- 保额分红短信批处理 -->
    <bean id="sendInsuredDividendSMSJob" class="com.nci.tunan.qry.impl.pa.batch.SendInsuredDividendSMSJob">
     	<property name="sendNoteAnnuityService" ref="sendNoteAnnuityService"></property>
    </bean> 
    
    <!-- 现金分红短信批处理 -->
    <bean id="sendCashDividendSMSJob" class="com.nci.tunan.qry.impl.pa.batch.SendCashDividendSMSJob">
     	<property name="sendNoteAnnuityService" ref="sendNoteAnnuityService"></property>
    </bean> 
    
    <!-- 万能险短信推送批处理 -->
    <bean id="sendUniversalSMSJob" class="com.nci.tunan.qry.impl.pa.batch.SendUniversalSMSJob">
     	<property name="updateUniversalSMSService" ref="updateUniversalSMSService"></property>
    </bean> 
    
    <!-- 推送通知书批处理 -->
    <bean id="sendDocumentJob" class="com.nci.tunan.qry.impl.pa.batch.SendDocumentJob">
     	<property name="sendDocumentService" ref="sendDocumentService"></property>
    </bean> 
    
    <!-- PDF文件生成结果批处理 -->
    <bean id="pdfGenerationResultsJob" class="com.nci.tunan.qry.impl.pa.batch.PDFGenerationResultsJob">
     	<property name="pdfGenerationResultsService" ref="pdfGenerationResultsService"></property>
    </bean> 
     <!-- 离线任务批处理历史失效清单 -->
    <bean id="reportOffLineCsEndorseHistoryInvalidDateTaskJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseHistoryInvalidDateTaskJob">
     	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="csEndorseHistoryInvalidatelistDao" ref="csEndorseHistoryInvalidatelistDao"></property>
    </bean>
    <!--离线任务批处理退减保清单  -->
      <bean id="reportOffLineCsEndorsePTListTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorsePTListTaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="csEndorsePTlistDao" ref="csEndorsePTlistDao"></property>
    </bean>
    <!--离线任务批处理保单终止清单  -->
      <bean id="reportOffLineCsEndorsepolicyTermTaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorsepolicyTermTaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="csServicePolicyTerminationlistDao" ref="csServicePolicyTerminationlistDao" ></property>
    </bean> 
    
    <!-- 慧择业务续期数据同步任务抽取批处理 -->
    <bean id="PA_HZPremSendTaskCreateJob" class="com.nci.tunan.qry.impl.pa.batch.HZPremSendTaskCreateJob" scope="prototype">
     	<property name="hzPremSendTaskService" ref="PA_HZPremSendTaskService"></property>
    </bean> 
    
          <!-- 慧择业务续期数据同步批处理  -->
    <bean id="PA_HZPremSendBatchJob" class="com.nci.tunan.qry.impl.pa.batch.HZPremSendBatchJob" scope="prototype">
     	<property name="hzPremSendTaskService" ref="PA_HZPremSendTaskService"></property>
    </bean> 
    
        <!-- 一年期保证续保自动续保成功告知推送（批处理） -->
    <bean id="renewalSuccessfulSMSJob" class="com.nci.tunan.qry.impl.pa.batch.RenewalSuccessfulSMSJob"  scope="prototype">
     	<property name="sendNoteAnnuityService" ref="sendNoteAnnuityService"></property>
    </bean> 
    
    <!--178954  给付拒付清单离线批处理 -->
	<bean id="ReportOffLinePayRejectClmJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLinePayRejectClmJob">
	    <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
        <property name="claimPayRejectListService" ref="claimPayRejectListService"></property>
	</bean>
	
	<!-- 应付实付清单离线批处理 -->
	<bean id="ReportOffLinePremPayClmJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLinePremPayClmJob">
			<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
			<property name="premPayDao" ref="premPayDao"></property>
	</bean>
	
	    <!-- 生存金未自动抵扣贷款清单离线批处理 -->
    <bean id="reportOffLineSurvivalNotAutoDeduJob" class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineSurvivalNotAutoDeduJob">
    <property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
	</bean>
	
	    <!-- 离线任务批处理付费类保全异常业务统计清单 -->
    <bean id="reportOffLineCsEndorsePayEBusiListPATaskJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorsePayEBusiListPATaskJob">
    	<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
    	<property name="csEndorsePayEBusiListDao" ref="csEndorsePayEBusiListDao"></property>
    </bean>
	
	<!-- 离线任务批处理JOB银保通业务明细清单 -->
	<bean id="reportOffLineCsEndorseYBTlistJob"  class="com.nci.tunan.qry.impl.offlinereport.batch.ReportOffLineCsEndorseYBTlistJob">
		<property name="reportOffLineTaskDao" ref="reportOffLineTaskDao"></property>
	</bean>
</beans>
