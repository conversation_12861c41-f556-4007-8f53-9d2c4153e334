<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:c="http://www.springframework.org/schema/c"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/task 
		http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch 
		http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache 
		http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx 
		http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/context 
		http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/aop 
		http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

	<!-- <import resource="classpath:META-INF/cxf/cxf.xml" /> -->
	<context:component-scan base-package="com.nci.tunan.uw"></context:component-scan>

	<bean id="transactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>
	
	<!-- 配置事务传播特性 start -->
	<tx:advice id="PAAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="save*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="add*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="insert*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="update*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="modify*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="edit*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="remove*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="delete*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="find*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="get*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="query*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="load*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="manul*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
			<tx:method name="create*" propagation="REQUIRED" read-only="false"
				rollback-for="java.lang.Exception" />
		</tx:attributes>
	</tx:advice>
	<!-- 配置参与事务的类 end -->

	<!--切面 start -->
	<aop:config>
		<aop:pointcut id="allPAServiceMethod"
			expression="execution(* com.nci.tunan.qry.impl.*.ucc.*.*(..))" />

		<aop:advisor pointcut-ref="allPAServiceMethod" advice-ref="PAAdvice" />
	</aop:config>
	<!--切面 end -->

<!-- 	<bean id="propertiesReader" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="locations">
			<list>
				<value>classpath:META-INF/conf/commonbiz_common.properties</value>
			</list>
		</property>
	</bean> -->

<!-- 	<import resource="classpath:META-INF/uw/spring/applicationContext-service.xml"/> -->
<!-- 	<import resource="classpath:META-INF/uw/spring/applicationContext-dao.xml"/> -->
<!-- 	<import resource="classpath:META-INF/uw/spring/applicationContext-ucc.xml"/> -->
	<!-- <import resource="classpath:META-INF/common/spring/applicationContext-common-hessian.xml"/> -->
	
</beans>
