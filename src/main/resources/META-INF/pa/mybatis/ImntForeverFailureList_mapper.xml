<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 续期清单-即将永久失效清单 -->
<mapper namespace=" com.nci.tunan.pa.report.imminentforeverfailure.dao.impl.ImntForeverFailureDaoImpl">	
    
    <sql id="queryImntForeverFailureByCondition">
		<if test=" organ_code != null and  organ_code !=''"><![CDATA[ AND TCM.ORGAN_CODE like #{organ_code} || '%' ]]></if>
		<if test=" agent_code != null and  agent_code !=''"><![CDATA[ AND TCA.AGENT_CODE = #{agent_code}   ]]></if>
		<if test=" service_bank_branch != null and  service_bank_branch !=''"><![CDATA[ AND TCM.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" statistics_date != null and  statistics_date !='' and  failure_year == '00'"><![CDATA[ AND Add_Months(TCBP.lapse_date,24) < = (#{statistics_date} + 20) and Add_Months(TCBP.lapse_date,24) > = #{statistics_date}+1 ]]></if>
		<if test=" statistics_date != null and  statistics_date !='' and  failure_year == '01'"><![CDATA[ AND Add_Months(TCBP.lapse_date,60) < = (#{statistics_date} + 20) and Add_Months(TCBP.lapse_date,60) > = #{statistics_date}+1 ]]></if>
		<!-- 销售渠道 -->
		<if test=" channeltype != null and channeltype != '' ">
			<![CDATA[ AND TCM.CHANNEL_TYPE in (${channeltype}) ]]>
		</if>
	</sql>
	<sql id="queryGroupImntForeverFailureByCondition">
		<if test=" organ_code != null and  organ_code !=''"><![CDATA[ AND TCM.ORGAN_CODE like #{organ_code} || '%' ]]></if>
		<if test=" agent_code != null and  agent_code !=''"><![CDATA[ AND TCA.AGENT_CODE = #{agent_code}   ]]></if>
		<if test=" service_bank_branch != null and  service_bank_branch !=''"><![CDATA[ AND TCM.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" statistics_date != null and  statistics_date !=''"><![CDATA[ AND Add_Months(TCM.lapse_date,24) < = (#{statistics_date} + 20) and Add_Months(TCM.lapse_date,24) > = #{statistics_date}+1 ]]></if>
		<!-- 销售渠道 -->
		<if test=" channeltype != null and channeltype != '' ">
			<![CDATA[ AND TCM.CHANNEL_TYPE in (${channeltype}) ]]>
		</if>
	</sql>
    <!-- 查询符合条件的即将永久失效的清单 -->
    <select id="PA_queryImntForeverFailureReport" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[SELECT ROWNUM RN,x.* from ( SELECT
                       Z.POLICY_CODE,
                     Z.HOLDER_NAME,
                     Z.INSURED_NAME,
                     Z.BANK_ACCOUNT,
                     Z.LAPSE_DATE,
                     Z.NOW_AGENT_NAME,
                     Z.NOW_AGENT_CODE,
                     SUM(Z.TOTAL_PREM_AF) TOTAL_PREM_AF,
                     Z.DUE_TIME,
                     Z.CHANNEL_ORG_CODE,
                     Z.PAY_NEXT,
                     Z.SERVICE_BANK_BRANCH,
                     Z.STATE , Z.CITY , Z.DISTRICT , Z.ADDRESS,
                     Z.TEL,
                     Z.POLICY_TYPE,
                     Z.LAST_AGENT_CODE,
                     Z.LAST_AGENT_NAME,
                     Z.ORGAN_CODE,
                     Z.SALES_ORGAN_CODE,
                     Z.STATUS_NAME,
                     Z.ORIGINAL_CHANNEL_TYPE_NAME, 
                             Z.SALES_CHANNEL_NAME, 
                             Z.YINYZU,
                             Z.YINYBU,
                            Z.YINYQU, 
                             Z.AGENT_START_DATE,
                             Z.SERVICE_HANDLER_CODE,
                             Z.SERVICE_HANDLER,
                             Z.ORIGINAL_AGENT_CODE,
                             Z.ORIGINAL_AGENT_NAME,
                             Z.BUSI_PROD_CODE,
                             Z.BUSI_PROD_NAME,
                             Z.PAID_COUNT,
                             Z.PER_LAPSE_DATE,
                             Z.BANK_CODE,
                             Z.SERVICE_BANK_CODE,
                                      CASE
                                WHEN Z.MULTI_MAINRISK_FLAG = '1' 
                                THEN CASE WHEN (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_MASTER CM1, DEV_PAS.T_CONTRACT_MASTER CM2
                          WHERE CM1.POLICY_CODE = CM2.RELATION_POLICY_CODE
                            AND (CM1.RELATION_POLICY_CODE = Z.POLICY_CODE OR
                              CM2.POLICY_CODE = Z.POLICY_CODE))>0 OR
                        (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_RELATION TCR
                          WHERE Z.POLICY_CODE = TCR.MASTER_POLICY_CODE
                            AND (TCR.MASTER_BUSI_ITEM_ID = Z.BUSI_ITEM_ID OR
                              TCR.SUB_BUSI_ITEM_ID = Z.BUSI_ITEM_ID)
                              AND TCR.RELATION_TYPE IN ('2','3','4') )>0
                              THEN '多主险且关联'
                                 ELSE '多主险保单' END
                                  WHEN  
                                  (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_MASTER CM1, DEV_PAS.T_CONTRACT_MASTER CM2
                          WHERE CM1.POLICY_CODE = CM2.RELATION_POLICY_CODE
                            AND (CM1.RELATION_POLICY_CODE = Z.POLICY_CODE OR
                              CM2.POLICY_CODE = Z.POLICY_CODE))>0 OR
                        (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_RELATION TCR
                          WHERE Z.POLICY_CODE = TCR.MASTER_POLICY_CODE
                            AND (TCR.MASTER_BUSI_ITEM_ID = Z.BUSI_ITEM_ID OR
                              TCR.SUB_BUSI_ITEM_ID = Z.BUSI_ITEM_ID)
                              AND TCR.RELATION_TYPE IN ('2','3','4') )>0
                            THEN '关联险保单'
                                             
                                                          
            /*  #123018 新核心系统清单优化需求四期  start.............. */
                           
                 when (SELECT COUNT(1)
                         FROM DEV_PAS.t_Contract_Busi_Prod ccbp
                        WHERE Z.POLICY_CODE = ccbp.POLICY_CODE
                          AND ccbp.REINSURED in ('1')) > 0 and
                      (SELECT COUNT(1)
                         FROM DEV_PAS.t_Contract_Busi_Prod ccbp
                        WHERE Z.POLICY_CODE = ccbp.POLICY_CODE
                          AND ccbp.REINSURED in ('2', '3')) > 0 then
                  '续转保单'
                            when (SELECT count(1)
                         FROM DEV_PAS.T_CONTRACT_MASTER CM1
                        WHERE CM1.POLICY_CODE = Z.POLICY_CODE
                          AND (cm1.former_id is not null and
                              cm1.POLICY_REINSURE_FLAG = '1')) > 0 OR
                      (SELECT COUNT(1)
                         FROM DEV_PAS.t_Contract_Busi_Prod ccbp
                        WHERE Z.POLICY_CODE = ccbp.POLICY_CODE
                          AND ccbp.REINSURED = '1') > 0 then
                  '续保保单'
               
                 when (SELECT count(1)
                         FROM DEV_PAS.T_CONTRACT_MASTER CM1
                        WHERE CM1.POLICY_CODE = Z.POLICY_CODE
                          AND (cm1.former_id is not null and
                              cm1.POLICY_REINSURE_FLAG in ('2', '3'))) > 0 OR
                      (SELECT COUNT(1)
                         FROM DEV_PAS.t_Contract_Busi_Prod ccbp
                        WHERE Z.POLICY_CODE = ccbp.POLICY_CODE
                          AND ccbp.REINSURED in ('2', '3')) > 0 then
                  '转保保单'

           /*  #123018 新核心系统清单优化需求四期  end.............. */  
                            
                            
                                  ELSE ''
                              END  AS STATUS
               FROM(SELECT ROWNUM RN,
                             A.POLICY_CODE,
                             A.HOLDER_NAME HOLDER_NAME,
                             A.BANK_ACCOUNT BANK_ACCOUNT,
                             A.LAPSE_DATE LAPSE_DATE,
                             A.AGENT_NAME NOW_AGENT_NAME,
                             A.AGENT_CODE NOW_AGENT_CODE,
                             A.INSURED_NAME,
                             A.organ_code,
                             A.STATUS_NAME,
                              (SELECT CASE WHEN TP.LAST_AGENT_CODE IS NOT NULL THEN   TP.LAST_AGENT_CODE ELSE  TP.AGENT_CODE  END
                                    FROM DEV_PAS.V_Prem tp
                                   WHERE Tp.POLICY_CODE = A.POLICY_CODE
                                      AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                      AND TP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                                     AND ROWNUM = 1) LAST_AGENT_CODE,
                                 (SELECT CASE WHEN TP.LAST_AGENT_NAME IS NOT NULL THEN TP.LAST_AGENT_NAME ELSE  TP.AGENT_NAME END
                                    FROM DEV_PAS.V_PREM TP
                                   WHERE TP.POLICY_CODE = A.POLICY_CODE
                                   AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                   AND TP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                                     AND ROWNUM = 1) LAST_AGENT_NAME,
                             A.FEE_AMOUNT as TOTAL_PREM_AF,
                             (SELECT MAX(BB.DUE_TIME)
                                FROM DEV_PAS.V_PREM_ARAP BB
                               WHERE BB.UNIT_NUMBER = A.UNIT_NUMBER
                               AND BB.BUSI_PROD_CODE = A.BUSI_PROD_CODE) DUE_TIME,
                             (SELECT TUO.SALES_ORGAN_NAME
                                FROM DEV_PAS.T_SALES_ORGAN TUO
                               WHERE A.AGENT_ORGAN_CODE = TUO.SALES_ORGAN_CODE
                                 AND ROWNUM = 1) CHANNEL_ORG_CODE,
                             (SELECT TPM.NAME
                                FROM DEV_PAS.T_PAYER_ACCOUNT TPAC,
                                     DEV_PAS.T_PAY_MODE      TPM
                               WHERE TPAC.POLICY_ID = A.POLICY_ID
                                 AND TPAC.PAY_MODE = TPM.CODE
                                 AND ROWNUM = 1) PAY_NEXT,
                             (SELECT T.BANK_BRANCH_NAME
                                FROM DEV_PAS.T_BANK_BRANCH T
                               WHERE T.BANK_BRANCH_CODE = a.SERVICE_BANK_BRANCH) SERVICE_BANK_BRANCH,
                             (SELECT TA.ADDRESS
                                FROM DEV_PAS.T_ADDRESS       TA,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TA.ADDRESS_ID = TPH.ADDRESS_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) ADDRESS,
                                    (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.state) state,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.city) city,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.district) district,
                                 (SELECT distinct TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_AGENT TAA,
                                                                   DEV_PAS.T_SALES_ORGAN TSO WHERE TAA.AGENT_CODE = A.AGENT_CODE
                                                                   AND TSO.SALES_ORGAN_CODE = TAA.SALES_ORGAN_CODE )SALES_ORGAN_CODE,
                    (SELECT CASE
                                   WHEN TC.MOBILE_TEL IS NOT NULL THEN
                                        TC.MOBILE_TEL || '(M)'
                                       ELSE
                                        TC.HOUSE_TEL || '(H)'
                                     END
                                FROM DEV_PAS.T_CUSTOMER      TC,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) TEL,
                             (SELECT TPF.FLAG_NAME FROM APP___PAS__DBUSER.T_POLICY_FLAG TPF WHERE TPF.FLAG_CODE = A.POLICY_FLAG) AS POLICY_TYPE,
                             A.Original_Channel_Type_Name, /* 原销售渠道 */
                             A.sales_channel_name, /* 现服务渠道 */
                            A.YINYZU, /* 现服务人员组 */
                             A.YINYBU, /* 现服务人员部 */
                             A.YINYQU, 
                             a.AGENT_START_DATE,
                             a.SERVICE_HANDLER_CODE,
                             a.SERVICE_HANDLER,
                             a.Original_Agent_Code,
                             a.Original_Agent_Name,
                             a.busi_prod_code,
                             a.busi_prod_name,
                             a.paid_count,
                             a.bank_code,
                             a.per_lapse_date,
                             a.Service_Bank_Branch as Service_Bank_code,
                             A.ORDER_ID,
                             a.MULTI_MAINRISK_FLAG,
                             a.BUSI_ITEM_ID,
                             a.master_BUSI_ITEM_ID
                        FROM (SELECT distinct TPA.POLICY_CODE,
                                     TCA.AGENT_CODE,
                                     TCA.AGENT_NAME,
                                     TCM.POLICY_ID,
                                     TPA.BANK_ACCOUNT,
                                     TPA.INSURED_NAME,
                                     TPA.HOLDER_NAME,
                                     TCA.AGENT_ORGAN_CODE,
                                      CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN
                                     TPA.BUSI_PROD_CODE
                                     ELSE
                                       (SELECT A.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE A.BUSI_ITEM_ID = TCBP.MASTER_BUSI_ITEM_ID)
                                       END AS BUSI_PROD_CODE,
                                     CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN
                                     TPA.BUSI_PROD_NAME
                                     ELSE
                                       (SELECT B.PRODUCT_NAME_SYS FROM DEV_PAS.T_CONTRACT_BUSI_PROD A,DEV_PDS.T_BUSINESS_PRODUCT B WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID AND  A.BUSI_ITEM_ID = TCBP.MASTER_BUSI_ITEM_ID)
                                       END AS BUSI_PROD_NAME,
                                       tpa.FEE_AMOUNT,
                                     tpa.paid_count,
                                    add_months(TCBP.lapse_date,
                                      ]]>
           		   <choose> 
           		   <when test="failure_year == '00'">24</when>
           		   <otherwise>60</otherwise>
           		   </choose> 
           		   <![CDATA[
                                     ) as per_lapse_date,
                                     TCBP.LAPSE_DATE,
                                     TCA.LAST_AGENT_CODE,
                                     TCA.LAST_AGENT_NAME,
                                     tpa.bank_code,
                                     tcm.organ_code,
                                     TCC.STATUS_NAME,
                                     TPA.UNIT_NUMBER,
                                     TCM.POLICY_FLAG,
                                     tcm.Service_Bank_Branch,
                                    (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.Policy_Id = TCM.Policy_Id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_NB_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS ORIGINAL_CHANNEL_TYPE_NAME,
                                (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.Policy_Id = TCM.Policy_Id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_CURRENT_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS SALES_CHANNEL_NAME,
                              (SELECT TSO.SALES_ORGAN_NAME
                                   FROM DEV_PAS.T_AGENT       B,
                                        DEV_PAS.T_SALES_ORGAN TSO,
                                        DEV_PAS.T_CONTRACT_AGENT TCA
                                  WHERE B.AGENT_CODE = TCA.AGENT_CODE 
                   AND TCA.POLICY_CODE = TPA.POLICY_CODE
                   AND TCA.IS_CURRENT_AGENT = '1'
                   AND TSO.SALES_ORGAN_CODE = B.GROUP_CODE
                                    AND ROWNUM = 1) YINYZU, /* 现服务人员组 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN NULL 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN A.SALES_ORGAN_NAME
      WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '2' AND ROWNUM =1 START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A,DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYBU, /* 现服务人员部 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN A.SALES_ORGAN_NAME 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                           WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A, DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYQU, 
                                      (SELECT TCA.AGENT_START_DATE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as AGENT_START_DATE, /* 现服务人员服务起始日期 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER_CODE, /* 现服务人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER, /* 现服务人员姓名 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Code, /* 原始销售人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Name, /* 原始销售人员姓名 */
                                             TCBP.ORDER_ID,
                                       TCM.MULTI_MAINRISK_FLAG,
                                       CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN 
                                       TCBP.BUSI_ITEM_ID
                                       ELSE TCBP.MASTER_BUSI_ITEM_ID
                                         END AS BUSI_ITEM_ID,
                             TCBP.MASTER_BUSI_ITEM_ID
                                FROM DEV_PAS.V_PREM_ARAP       TPA,
                                DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                                     DEV_PAS.T_CONTRACT_MASTER TCM,
                                     DEV_PAS.T_LIABILITY_STATUS TCC,
                                     DEV_PAS.T_CONTRACT_AGENT  TCA
                               WHERE TPA.POLICY_CODE = TCBP.POLICY_CODE
                                 AND TPA.Busi_Prod_Code = TCBP.Busi_Prod_Code
                               	 AND TCBP.POLICY_ID = TCM.POLICY_ID
                                 AND TCM.POLICY_ID = TCA.POLICY_ID
                  				 AND TCBP.LIABILITY_STATE <>'3'
                                  AND ((TCM.LIABILITY_STATE = '4'
                          		AND TCM.LAPSE_CAUSE = '1')or
                          		(TCM.LIABILITY_STATE = '1' and '4' = tcbp.LIABILITY_STATE))
                                 
                                 AND TCM.LIABILITY_STATE = TCC.STATUS_CODE
                                 AND TCA.IS_CURRENT_AGENT='1'
                                 AND TPA.FEE_STATUS = '02'
                                 AND TPA.SERVICE_CODE = 'PNL'
                                 AND TPA.DERIV_TYPE = '003'
                                 AND TPA.DUE_TIME = (SELECT MAX(P.DUE_TIME)
                                 						FROM DEV_PAS.V_PREM_ARAP P
                                					WHERE P.POLICY_CODE = TPA.POLICY_CODE
                                  					  AND P.FEE_STATUS = '02'
                                  					  AND P.SERVICE_CODE = 'PNL'
                                                      AND P.IS_RISK_MAIN = '1'
                                                      AND P.DERIV_TYPE = '003')
								]]>
				           <include refid="queryImntForeverFailureByCondition" />
				          <![CDATA[   GROUP BY TPA.POLICY_CODE,
                            TCA.LAST_AGENT_CODE,
                            TCA.LAST_AGENT_NAME,
                            TCA.AGENT_CODE,
                            TCA.AGENT_NAME,
                            TCM.POLICY_ID,
                            TCC.STATUS_NAME,
                            TPA.BANK_ACCOUNT,
                            TPA.HOLDER_NAME,
                            TPA.INSURED_NAME,
                            TCA.AGENT_ORGAN_CODE,
                            tcm.organ_code,
                            TCBP.LAPSE_DATE,
                            TPA.UNIT_NUMBER,
                            TCM.SERVICE_BANK_BRANCH,
                            TCM.POLICY_FLAG,
                            TCM.CHANNEL_TYPE,
                            tpa.AGENT_CODE,
                            tpa.busi_prod_code,
                            tpa.busi_prod_name,
                            tpa.paid_count,
                            tpa.bank_code,
                           	TCM.RELATION_POLICY_CODE,
                           	TCM.MULTI_MAINRISK_FLAG,
                            tcbp.ORDER_ID,
                            TCBP.BUSI_ITEM_ID,
                            TCBP.POLICY_CODE,
                            TCM.POLICY_CODE,
                            tpa.FEE_AMOUNT,
                            tcbp.master_BUSI_ITEM_ID
                            ) A
                 ORDER BY A.POLICY_CODE,A.ORDER_ID)Z GROUP BY Z.POLICY_CODE,
                     Z.HOLDER_NAME,
                     Z.INSURED_NAME,
                     Z.BANK_ACCOUNT,
                     Z.LAPSE_DATE,
                     Z.NOW_AGENT_NAME,
                     Z.NOW_AGENT_CODE,
                     Z.DUE_TIME,
                     Z.CHANNEL_ORG_CODE,
                     Z.PAY_NEXT,
                     Z.SERVICE_BANK_BRANCH,
                     Z.STATE,
           Z.CITY,
           Z.DISTRICT,
           Z.ADDRESS,
                     Z.TEL,
                     Z.POLICY_TYPE,
                     Z.LAST_AGENT_CODE,
                     Z.LAST_AGENT_NAME,
                     Z.ORGAN_CODE,
                     Z.SALES_ORGAN_CODE,
                     Z.STATUS_NAME,
                     Z.ORIGINAL_CHANNEL_TYPE_NAME, 
                             Z.SALES_CHANNEL_NAME, 
                             Z.YINYZU,
                             Z.YINYBU,
                            Z.YINYQU, 
                             Z.AGENT_START_DATE,
                             Z.SERVICE_HANDLER_CODE,
                             Z.SERVICE_HANDLER,
                             Z.ORIGINAL_AGENT_CODE,
                             Z.ORIGINAL_AGENT_NAME,
                             Z.BUSI_PROD_CODE,
                             Z.BUSI_PROD_NAME,
                             Z.PAID_COUNT,
                             Z.PER_LAPSE_DATE,
                             Z.BANK_CODE,
                             Z.SERVICE_BANK_CODE,
                             Z.MULTI_MAINRISK_FLAG,
                             Z.BUSI_ITEM_ID )X
				          ]]>
    </select>
	<!-- 查询个数操作 -->
	<select id="PA_findImntForeverFailureTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1)FROM (SELECT distinct Z.POLICY_CODE,
                     Z.HOLDER_NAME,
                     Z.INSURED_NAME,
                     Z.BANK_ACCOUNT,
                     Z.LAPSE_DATE,
                     Z.NOW_AGENT_NAME,
                     Z.NOW_AGENT_CODE,
                     SUM(Z.TOTAL_PREM_AF) TOTAL_PREM_AF,
                     Z.DUE_TIME,
                     Z.CHANNEL_ORG_CODE,
                     Z.PAY_NEXT,
                     Z.SERVICE_BANK_BRANCH,
                     Z.STATE , Z.CITY , Z.DISTRICT , Z.ADDRESS,
                     Z.TEL,
                     Z.POLICY_TYPE,
                     Z.LAST_AGENT_CODE,
                     Z.LAST_AGENT_NAME,
                     Z.ORGAN_CODE,
                     Z.SALES_ORGAN_CODE,
                     Z.STATUS_NAME,
                     Z.ORIGINAL_CHANNEL_TYPE_NAME, 
                             Z.SALES_CHANNEL_NAME, 
                             Z.YINYZU,
                             Z.YINYBU,
                            Z.YINYQU, 
                             Z.AGENT_START_DATE,
                             Z.SERVICE_HANDLER_CODE,
                             Z.SERVICE_HANDLER,
                             Z.ORIGINAL_AGENT_CODE,
                             Z.ORIGINAL_AGENT_NAME,
                             Z.BUSI_PROD_CODE,
                             Z.BUSI_PROD_NAME,
                             Z.PAID_COUNT,
                             Z.PER_LAPSE_DATE,
                             Z.BANK_CODE,
                             Z.SERVICE_BANK_CODE,
                                      CASE
                                WHEN Z.MULTI_MAINRISK_FLAG = '1' 
                                THEN CASE WHEN (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_MASTER CM1, DEV_PAS.T_CONTRACT_MASTER CM2
                          WHERE CM1.POLICY_CODE = CM2.RELATION_POLICY_CODE
                            AND (CM1.RELATION_POLICY_CODE = Z.POLICY_CODE OR
                              CM2.POLICY_CODE = Z.POLICY_CODE))>0 OR
                        (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_RELATION TCR
                          WHERE Z.POLICY_CODE = TCR.MASTER_POLICY_CODE
                            AND (TCR.MASTER_BUSI_ITEM_ID = Z.BUSI_ITEM_ID OR
                              TCR.SUB_BUSI_ITEM_ID = Z.BUSI_ITEM_ID)
                              AND TCR.RELATION_TYPE IN ('2','3','4') )>0
                              THEN '多主险且关联'
                                 ELSE '多主险保单' END
                                  WHEN  
                                  (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_MASTER CM1, DEV_PAS.T_CONTRACT_MASTER CM2
                          WHERE CM1.POLICY_CODE = CM2.RELATION_POLICY_CODE
                            AND (CM1.RELATION_POLICY_CODE = Z.POLICY_CODE OR
                              CM2.POLICY_CODE = Z.POLICY_CODE))>0 OR
                        (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_RELATION TCR
                          WHERE Z.POLICY_CODE = TCR.MASTER_POLICY_CODE
                            AND (TCR.MASTER_BUSI_ITEM_ID = Z.BUSI_ITEM_ID OR
                              TCR.SUB_BUSI_ITEM_ID = Z.BUSI_ITEM_ID)
                              AND TCR.RELATION_TYPE IN ('2','3','4') )>0
                            THEN '关联险保单'
                                  ELSE ''
                              END  AS STATUS
               FROM(SELECT ROWNUM RN,
                             A.POLICY_CODE,
                             A.INSURED_NAME,
                             A.HOLDER_NAME HOLDER_NAME,
                             A.BANK_ACCOUNT BANK_ACCOUNT,
                             A.LAPSE_DATE LAPSE_DATE,
                             A.AGENT_NAME NOW_AGENT_NAME,
                             A.AGENT_CODE NOW_AGENT_CODE,
                             A.organ_code,
                             A.STATUS_NAME,
                             (SELECT CASE WHEN TP.LAST_AGENT_CODE IS NOT NULL THEN   TP.LAST_AGENT_CODE ELSE  TP.AGENT_CODE  END
                                    FROM DEV_PAS.V_Prem tp
                                   WHERE Tp.POLICY_CODE = A.POLICY_CODE
                                   AND TP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                                      AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                     AND ROWNUM = 1) LAST_AGENT_CODE,
                                 (SELECT CASE WHEN TP.LAST_AGENT_NAME IS NOT NULL THEN TP.LAST_AGENT_NAME ELSE  TP.AGENT_NAME END
                                    FROM DEV_PAS.V_Prem TP
                                   WHERE TP.POLICY_CODE = A.POLICY_CODE
                                    AND TP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                                   AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                     AND ROWNUM = 1) LAST_AGENT_NAME,
                              A.FEE_AMOUNT as TOTAL_PREM_AF,
                             (SELECT MAX(BB.DUE_TIME)
                                FROM DEV_PAS.V_PREM_ARAP BB
                               WHERE BB.UNIT_NUMBER = A.UNIT_NUMBER
                                AND BB.BUSI_PROD_CODE = A.BUSI_PROD_CODE) DUE_TIME,
                             (SELECT TUO.SALES_ORGAN_NAME
                                FROM DEV_PAS.T_SALES_ORGAN TUO
                               WHERE A.AGENT_ORGAN_CODE = TUO.SALES_ORGAN_CODE
                                 AND ROWNUM = 1) CHANNEL_ORG_CODE,
                             (SELECT TPM.NAME
                                FROM DEV_PAS.T_PAYER_ACCOUNT TPAC,
                                     DEV_PAS.T_PAY_MODE      TPM
                               WHERE TPAC.POLICY_ID = A.POLICY_ID
                                 AND TPAC.PAY_MODE = TPM.CODE
                                 AND ROWNUM = 1) PAY_NEXT,
                             (SELECT T.BANK_BRANCH_NAME
                                FROM DEV_PAS.T_BANK_BRANCH T
                               WHERE T.BANK_BRANCH_CODE = a.SERVICE_BANK_BRANCH) SERVICE_BANK_BRANCH,
                             (SELECT TA.ADDRESS
                                FROM DEV_PAS.T_ADDRESS       TA,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TA.ADDRESS_ID = TPH.ADDRESS_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) ADDRESS,
                                    (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.state) state,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.city) city,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.district) district,
                                 (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_AGENT TAA,
                                                                   DEV_PAS.T_SALES_ORGAN TSO WHERE TAA.AGENT_CODE = A.AGENT_CODE
                                                                   AND TSO.SALES_ORGAN_CODE = TAA.SALES_ORGAN_CODE )SALES_ORGAN_CODE,
                    (SELECT CASE
                                   WHEN TC.MOBILE_TEL IS NOT NULL THEN
                                        TC.MOBILE_TEL || '(M)'
                                       ELSE
                                        TC.HOUSE_TEL || '(H)'
                                     END
                                FROM DEV_PAS.T_CUSTOMER      TC,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) TEL,
                             (SELECT TPF.FLAG_NAME FROM APP___PAS__DBUSER.T_POLICY_FLAG TPF WHERE TPF.FLAG_CODE = A.POLICY_FLAG) AS POLICY_TYPE,
                             A.Original_Channel_Type_Name, /* 原销售渠道 */
                             A.sales_channel_name, /* 现服务渠道 */
                            A.YINYZU, /* 现服务人员组 */
                             A.YINYBU, /* 现服务人员部 */
                             A.YINYQU, 
                             a.AGENT_START_DATE,
                             a.SERVICE_HANDLER_CODE,
                             a.SERVICE_HANDLER,
                             a.Original_Agent_Code,
                             a.Original_Agent_Name,
                             a.busi_prod_code,
                             a.busi_prod_name,
                             a.paid_count,
                             a.bank_code,
                             a.per_lapse_date,
                             a.Service_Bank_Branch as Service_Bank_code，
                             A.ORDER_ID,
                             a.MULTI_MAINRISK_FLAG,
                             a.BUSI_ITEM_ID,
                             a.master_BUSI_ITEM_ID
                        FROM (SELECT TPA.POLICY_CODE,
                                     TCA.AGENT_CODE,
                                     TCA.AGENT_NAME,
                                     TCM.POLICY_ID,
                                     TPA.BANK_ACCOUNT,
                                     TPA.INSURED_NAME,
                                     TPA.HOLDER_NAME,
                                     TCA.AGENT_ORGAN_CODE,
                                      CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN
                                     TPA.BUSI_PROD_CODE
                                     ELSE
                                       (SELECT A.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE A.BUSI_ITEM_ID = TCBP.MASTER_BUSI_ITEM_ID)
                                       END AS BUSI_PROD_CODE,
                                     CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN
                                     TPA.BUSI_PROD_NAME
                                     ELSE
                                       (SELECT B.PRODUCT_NAME_SYS FROM DEV_PAS.T_CONTRACT_BUSI_PROD A,DEV_PDS.T_BUSINESS_PRODUCT B WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID AND  A.BUSI_ITEM_ID = TCBP.MASTER_BUSI_ITEM_ID)
                                       END AS BUSI_PROD_NAME,
                                       tpa.FEE_AMOUNT,
                                     tpa.paid_count,
                                     add_months(TCBP.lapse_date,24) as per_lapse_date,
                                     TCBP.LAPSE_DATE,
                                     TCA.LAST_AGENT_CODE,
                                     TCA.LAST_AGENT_NAME,
                                     tpa.bank_code,
                                     tcm.organ_code,
                                     TCC.STATUS_NAME,
                                     TPA.UNIT_NUMBER,
                                     TCM.POLICY_FLAG,
                                     tcm.Service_Bank_Branch,
                                     (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.POLICY_id = TCM.POLICY_id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_NB_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS ORIGINAL_CHANNEL_TYPE_NAME,
                                (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.POLICY_id = TCM.POLICY_id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_CURRENT_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS SALES_CHANNEL_NAME,
                              (SELECT TSO.SALES_ORGAN_NAME
                                   FROM DEV_PAS.T_AGENT       B,
                                        DEV_PAS.T_SALES_ORGAN TSO,
                                        DEV_PAS.T_CONTRACT_AGENT TCA
                                  WHERE B.AGENT_CODE = TCA.AGENT_CODE 
                   AND TCA.POLICY_CODE = TPA.POLICY_CODE
                   AND TCA.IS_CURRENT_AGENT = '1'
                   AND TSO.SALES_ORGAN_CODE = B.GROUP_CODE
                                    AND ROWNUM = 1) YINYZU, /* 现服务人员组 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN NULL 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN A.SALES_ORGAN_NAME
      WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '2' AND ROWNUM =1 START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A,DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYBU, /* 现服务人员部 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN A.SALES_ORGAN_NAME 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                           WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A, DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYQU, 
                                      (SELECT TCA.AGENT_START_DATE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as AGENT_START_DATE, /* 现服务人员服务起始日期 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER_CODE, /* 现服务人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER, /* 现服务人员姓名 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Code, /* 原始销售人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Name, /* 原始销售人员姓名 */
                                            TCBP.ORDER_ID,
                                       TCM.MULTI_MAINRISK_FLAG,
                                       CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN 
                                       TCBP.BUSI_ITEM_ID
                                       ELSE TCBP.MASTER_BUSI_ITEM_ID
                                         END AS BUSI_ITEM_ID,
                             TCBP.MASTER_BUSI_ITEM_ID
                                FROM DEV_PAS.V_PREM_ARAP       TPA,
                                DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                                     DEV_PAS.T_CONTRACT_MASTER TCM,
                                     DEV_PAS.T_LIABILITY_STATUS TCC,
                                     DEV_PAS.T_CONTRACT_AGENT  TCA
                               WHERE TPA.POLICY_CODE = TCBP.POLICY_CODE
                                 AND TPA.Busi_Prod_Code = TCBP.Busi_Prod_Code
                               	 AND TCBP.POLICY_ID = TCM.POLICY_ID
                                 AND TCM.POLICY_ID = TCA.POLICY_ID
                  				 AND TCBP.LIABILITY_STATE <>'3'
                                 AND ((TCM.LIABILITY_STATE = '4'
                          AND TCM.LAPSE_CAUSE = '1')or
                          (TCM.LIABILITY_STATE = '1' and '4' = tcbp.LIABILITY_STATE))
                                 AND TCM.LIABILITY_STATE = TCC.STATUS_CODE
                                 AND TCA.IS_CURRENT_AGENT='1'
                                  AND TPA.FEE_STATUS = '02'
                                  AND TPA.SERVICE_CODE = 'PNL'
                                  and tpa.deriv_type = '003'
                                  AND TPA.DUE_TIME = (SELECT MAX(P.DUE_TIME)
                                 						FROM DEV_PAS.V_PREM_ARAP P
                                					  WHERE P.POLICY_CODE = TPA.POLICY_CODE
                                  						AND P.FEE_STATUS = '02'
                                  						AND P.SERVICE_CODE = 'PNL'
                                  						AND P.IS_RISK_MAIN = '1'
                                  						AND P.DERIV_TYPE = '003')
						]]>
		           <include refid="queryImntForeverFailureByCondition" />
		          <![CDATA[
		          		  GROUP BY TPA.POLICY_CODE,
                            TCA.LAST_AGENT_CODE,
                            TCA.LAST_AGENT_NAME,
                            TCA.AGENT_CODE,
                            TCA.AGENT_NAME,
                            TCM.POLICY_ID,
                            TCC.STATUS_NAME,
                            TPA.BANK_ACCOUNT,
                            TPA.HOLDER_NAME,
                            TPA.INSURED_NAME,
                            TCA.AGENT_ORGAN_CODE,
                            tcm.organ_code,
                            TCBP.LAPSE_DATE,
                            TPA.UNIT_NUMBER,
                            TCM.SERVICE_BANK_BRANCH,
                            TCM.POLICY_FLAG,
                            TCM.CHANNEL_TYPE,
                            tpa.AGENT_CODE,
                            tpa.busi_prod_code,
                            tpa.busi_prod_name,
                            tpa.paid_count,
                            tpa.bank_code,
                            TCM.RELATION_POLICY_CODE,
                            TCM.MULTI_MAINRISK_FLAG,
                            tcbp.ORDER_ID,
                            TCBP.BUSI_ITEM_ID,
                            TCBP.POLICY_CODE,
                            TCM.POLICY_CODE,
                            tpa.FEE_AMOUNT,
                            tcbp.master_BUSI_ITEM_ID
                            ) A
                 ORDER BY A.POLICY_CODE,A.ORDER_ID)Z GROUP BY Z.POLICY_CODE,
                     Z.HOLDER_NAME,
                     Z.INSURED_NAME,
                     Z.BANK_ACCOUNT,
                     Z.LAPSE_DATE,
                     Z.NOW_AGENT_NAME,
                     Z.NOW_AGENT_CODE,
                     Z.DUE_TIME,
                     Z.CHANNEL_ORG_CODE,
                     Z.PAY_NEXT,
                     Z.SERVICE_BANK_BRANCH,
                     Z.STATE,
           Z.CITY,
           Z.DISTRICT,
           Z.ADDRESS,
                     Z.TEL,
                     Z.POLICY_TYPE,
                     Z.LAST_AGENT_CODE,
                     Z.LAST_AGENT_NAME,
                     Z.ORGAN_CODE,
                     Z.SALES_ORGAN_CODE,
                     Z.STATUS_NAME,
                     Z.ORIGINAL_CHANNEL_TYPE_NAME, 
                             Z.SALES_CHANNEL_NAME, 
                             Z.YINYZU,
                             Z.YINYBU,
                            Z.YINYQU, 
                             Z.AGENT_START_DATE,
                             Z.SERVICE_HANDLER_CODE,
                             Z.SERVICE_HANDLER,
                             Z.ORIGINAL_AGENT_CODE,
                             Z.ORIGINAL_AGENT_NAME,
                             Z.BUSI_PROD_CODE,
                             Z.BUSI_PROD_NAME,
                             Z.PAID_COUNT,
                             Z.PER_LAPSE_DATE,
                             Z.BANK_CODE,
                             Z.SERVICE_BANK_CODE,
                             Z.MULTI_MAINRISK_FLAG,
                             Z.BUSI_ITEM_ID)
		          ]]>
	</select>

    <!-- 分页查询操作 -->
	<select id="PA_findImntForeverFailureForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT distinct F.POLICY_CODE,
             F.HOLDER_NAME,
             F.INSURED_NAME,
             F.BANK_ACCOUNT,
             F.LAPSE_DATE,
             F.NOW_AGENT_NAME,
             F.NOW_AGENT_CODE,
             F.TOTAL_PREM_AF,
             F.DUE_TIME,
             F.CHANNEL_ORG_CODE,
             F.PAY_NEXT,
             F.SERVICE_BANK_BRANCH,
             F.ADDRESS,
             F.TEL,
             F.POLICY_TYPE,
             F.LAST_AGENT_CODE,
             F.LAST_AGENT_NAME,
             F.ORGAN_CODE,
             F.SALES_ORGAN_CODE,
             F.STATUS_NAME,
             F.Original_Channel_Type_Name, /* 原销售渠道 */
             F.sales_channel_name, /* 现服务渠道 */
             F.YINYZU, /* 现服务人员组 */
             F.YINYBU, /* 现服务人员部 */
             F.YINYQU, 
             F.AGENT_START_DATE,
             F.SERVICE_HANDLER_CODE,
             F.SERVICE_HANDLER,
             F.Original_Agent_Code,
             F.Original_Agent_Name,
             F.busi_prod_code,
             F.busi_prod_name,
             F.paid_count,
            F.per_lapse_date,
            F.bank_code,
            F.Service_Bank_code,
            F.status
        FROM (SELECT distinct
                     E.POLICY_CODE,
                     E.HOLDER_NAME,
                     E.INSURED_NAME,
                     E.BANK_ACCOUNT,
                     E.LAPSE_DATE,
                     E.NOW_AGENT_NAME,
                     E.NOW_AGENT_CODE,
                     E.TOTAL_PREM_AF,
                     E.DUE_TIME,
                     E.CHANNEL_ORG_CODE,
                     E.PAY_NEXT,
                     E.SERVICE_BANK_BRANCH,
                     E.state || E.city || E.district || E.ADDRESS as ADDRESS,
                     E.TEL,
                     E.POLICY_TYPE,
                     E.LAST_AGENT_CODE,
                     E.LAST_AGENT_NAME,
                     E.ORGAN_CODE,
                     E.SALES_ORGAN_CODE,
                      ROWNUM RN,
                     E.STATUS_NAME,
                     E.Original_Channel_Type_Name, /* 原销售渠道 */
                             E.sales_channel_name, /* 现服务渠道 */
                             E.YINYZU, /* 现服务人员组 */
                             E.YINYBU, /* 现服务人员部 */
                            E.YINYQU, 
                             E.AGENT_START_DATE,
                             E.SERVICE_HANDLER_CODE,
                             E.SERVICE_HANDLER,
                             E.Original_Agent_Code,
                             E.Original_Agent_Name,
                             E.busi_prod_code,
                             E.busi_prod_name,
                             E.paid_count,
                             E.per_lapse_date,
                             E.bank_code,
                             E.Service_Bank_code,
                             E.status
                FROM (SELECT distinct Z.POLICY_CODE,
                     Z.HOLDER_NAME,
                     Z.INSURED_NAME,
                     Z.BANK_ACCOUNT,
                     Z.LAPSE_DATE,
                     Z.NOW_AGENT_NAME,
                     Z.NOW_AGENT_CODE,
                     SUM(Z.TOTAL_PREM_AF) TOTAL_PREM_AF,
                     Z.DUE_TIME,
                     Z.CHANNEL_ORG_CODE,
                     Z.PAY_NEXT,
                     Z.SERVICE_BANK_BRANCH,
                     Z.STATE , Z.CITY , Z.DISTRICT , Z.ADDRESS,
                     Z.TEL,
                     Z.POLICY_TYPE,
                     Z.LAST_AGENT_CODE,
                     Z.LAST_AGENT_NAME,
                     Z.ORGAN_CODE,
                     Z.SALES_ORGAN_CODE,
                     Z.STATUS_NAME,
                     Z.ORIGINAL_CHANNEL_TYPE_NAME, 
                             Z.SALES_CHANNEL_NAME, 
                             Z.YINYZU,
                             Z.YINYBU,
                            Z.YINYQU, 
                             Z.AGENT_START_DATE,
                             Z.SERVICE_HANDLER_CODE,
                             Z.SERVICE_HANDLER,
                             Z.ORIGINAL_AGENT_CODE,
                             Z.ORIGINAL_AGENT_NAME,
                             Z.BUSI_PROD_CODE,
                             Z.BUSI_PROD_NAME,
                             Z.PAID_COUNT,
                             Z.PER_LAPSE_DATE,
                             Z.BANK_CODE,
                             Z.SERVICE_BANK_CODE,
                                      CASE
                                WHEN Z.MULTI_MAINRISK_FLAG = '1' 
                                THEN CASE WHEN (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_MASTER CM1, DEV_PAS.T_CONTRACT_MASTER CM2
                          WHERE CM1.POLICY_CODE = CM2.RELATION_POLICY_CODE
                            AND (CM1.RELATION_POLICY_CODE = Z.POLICY_CODE OR
                              CM2.POLICY_CODE = Z.POLICY_CODE))>0 OR
                        (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_RELATION TCR
                          WHERE Z.POLICY_CODE = TCR.MASTER_POLICY_CODE
                            AND (TCR.MASTER_BUSI_ITEM_ID = Z.BUSI_ITEM_ID OR
                              TCR.SUB_BUSI_ITEM_ID = Z.BUSI_ITEM_ID)
                              AND TCR.RELATION_TYPE IN ('2','3','4') )>0
                              THEN '多主险且关联'
                                 ELSE '多主险保单' END
                                  WHEN  
                                  (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_MASTER CM1, DEV_PAS.T_CONTRACT_MASTER CM2
                          WHERE CM1.POLICY_CODE = CM2.RELATION_POLICY_CODE
                            AND (CM1.RELATION_POLICY_CODE = Z.POLICY_CODE OR
                              CM2.POLICY_CODE = Z.POLICY_CODE))>0 OR
                        (SELECT COUNT(1)
                            FROM DEV_PAS.T_CONTRACT_RELATION TCR
                          WHERE Z.POLICY_CODE = TCR.MASTER_POLICY_CODE
                            AND (TCR.MASTER_BUSI_ITEM_ID = Z.BUSI_ITEM_ID OR
                              TCR.SUB_BUSI_ITEM_ID = Z.BUSI_ITEM_ID)
                              AND TCR.RELATION_TYPE IN ('2','3','4') )>0
                            THEN '关联险保单'
                                  ELSE ''
                              END  AS STATUS
               FROM(SELECT ROWNUM RN,
                             A.POLICY_CODE,
                             A.HOLDER_NAME HOLDER_NAME,
                             A.INSURED_NAME,
                             A.BANK_ACCOUNT BANK_ACCOUNT,
                             A.LAPSE_DATE LAPSE_DATE,
                             A.AGENT_NAME NOW_AGENT_NAME,
                             A.AGENT_CODE NOW_AGENT_CODE,
                             A.organ_code,
                             A.STATUS_NAME,
                              (SELECT CASE WHEN TP.LAST_AGENT_CODE IS NOT NULL THEN   TP.LAST_AGENT_CODE ELSE  TP.AGENT_CODE  END
                                    FROM DEV_PAS.V_Prem tp
                                   WHERE Tp.POLICY_CODE = A.POLICY_CODE
                                      AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                       AND TP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                                     AND ROWNUM = 1) LAST_AGENT_CODE,
                                 (SELECT CASE WHEN TP.LAST_AGENT_NAME IS NOT NULL THEN TP.LAST_AGENT_NAME ELSE  TP.AGENT_NAME END
                                    FROM DEV_PAS.V_PREM TP
                                   WHERE TP.POLICY_CODE = A.POLICY_CODE
                                   AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                   AND TP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                                     AND ROWNUM = 1) LAST_AGENT_NAME,
                               A.FEE_AMOUNT as TOTAL_PREM_AF,
                             (SELECT MAX(BB.DUE_TIME)
                                FROM DEV_PAS.V_PREM_ARAP BB
                               WHERE BB.UNIT_NUMBER = A.UNIT_NUMBER
                               AND BB.BUSI_PROD_CODE = A.BUSI_PROD_CODE) DUE_TIME,
                             (SELECT TUO.SALES_ORGAN_NAME
                                FROM DEV_PAS.T_SALES_ORGAN TUO
                               WHERE A.AGENT_ORGAN_CODE = TUO.SALES_ORGAN_CODE
                                 AND ROWNUM = 1) CHANNEL_ORG_CODE,
                             (SELECT TPM.NAME
                                FROM DEV_PAS.T_PAYER_ACCOUNT TPAC,
                                     DEV_PAS.T_PAY_MODE      TPM
                               WHERE TPAC.POLICY_ID = A.POLICY_ID
                                 AND TPAC.PAY_MODE = TPM.CODE
                                 AND ROWNUM = 1) PAY_NEXT,
                             (SELECT T.BANK_BRANCH_NAME
                                FROM DEV_PAS.T_BANK_BRANCH T
                               WHERE T.BANK_BRANCH_CODE = a.SERVICE_BANK_BRANCH) SERVICE_BANK_BRANCH,
                             (SELECT TA.ADDRESS
                                FROM DEV_PAS.T_ADDRESS       TA,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TA.ADDRESS_ID = TPH.ADDRESS_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) ADDRESS,
                                    (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.state) state,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.city) city,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.district) district,
                                 (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_AGENT TAA,
                                                                   DEV_PAS.T_SALES_ORGAN TSO WHERE TAA.AGENT_CODE = A.AGENT_CODE
                                                                   AND TSO.SALES_ORGAN_CODE = TAA.SALES_ORGAN_CODE )SALES_ORGAN_CODE,
                    (SELECT CASE
                                   WHEN TC.MOBILE_TEL IS NOT NULL THEN
                                        TC.MOBILE_TEL || '(M)'
                                       ELSE
                                        TC.HOUSE_TEL || '(H)'
                                     END
                                FROM DEV_PAS.T_CUSTOMER      TC,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) TEL,
                             (SELECT TPF.FLAG_NAME FROM APP___PAS__DBUSER.T_POLICY_FLAG TPF WHERE TPF.FLAG_CODE = A.POLICY_FLAG) AS POLICY_TYPE,
                             A.Original_Channel_Type_Name, /* 原销售渠道 */
                             A.sales_channel_name, /* 现服务渠道 */
                            A.YINYZU, /* 现服务人员组 */
                             A.YINYBU, /* 现服务人员部 */
                             A.YINYQU, 
                             a.AGENT_START_DATE,
                             a.SERVICE_HANDLER_CODE,
                             a.SERVICE_HANDLER,
                             a.Original_Agent_Code,
                             a.Original_Agent_Name,
                             a.busi_prod_code,
                             a.busi_prod_name,
                             a.paid_count,
                             a.bank_code,
                             a.per_lapse_date,
                             a.Service_Bank_Branch as Service_Bank_code,
                             A.ORDER_ID,
                             a.MULTI_MAINRISK_FLAG,
                             a.BUSI_ITEM_ID,
                             a.master_BUSI_ITEM_ID
                        FROM (SELECT distinct TPA.POLICY_CODE,
                                     TCA.AGENT_CODE,
                                     TCA.AGENT_NAME,
                                     TCM.POLICY_ID,
                                     TPA.BANK_ACCOUNT,
                                     TPA.HOLDER_NAME,
                                     TPA.INSURED_NAME,
                                     TCA.AGENT_ORGAN_CODE,
                                      CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN
                                     TPA.BUSI_PROD_CODE
                                     ELSE
                                       (SELECT A.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE A.BUSI_ITEM_ID = TCBP.MASTER_BUSI_ITEM_ID)
                                       END AS BUSI_PROD_CODE,
                                     CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN
                                     TPA.BUSI_PROD_NAME
                                     ELSE
                                       (SELECT B.PRODUCT_NAME_SYS FROM DEV_PAS.T_CONTRACT_BUSI_PROD A,DEV_PDS.T_BUSINESS_PRODUCT B WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID AND  A.BUSI_ITEM_ID = TCBP.MASTER_BUSI_ITEM_ID)
                                       END AS BUSI_PROD_NAME,
                                       tpa.FEE_AMOUNT,
                                     tpa.paid_count,
                                     add_months(TCBP.lapse_date,24) as per_lapse_date,
                                     TCBP.LAPSE_DATE,
                                     TCA.LAST_AGENT_CODE,
                                     TCA.LAST_AGENT_NAME,
                                     tpa.bank_code,
                                     tcm.organ_code,
                                     TCC.STATUS_NAME,
                                     TPA.UNIT_NUMBER,
                                     TCM.POLICY_FLAG,
                                     tcm.Service_Bank_Branch,
                                     (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.POLICY_id = TCM.POLICY_id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_NB_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS ORIGINAL_CHANNEL_TYPE_NAME,
                                (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.POLICY_id = TCM.POLICY_id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_CURRENT_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS SALES_CHANNEL_NAME,
                               (SELECT TSO.SALES_ORGAN_NAME
                                   FROM DEV_PAS.T_AGENT       B,
                                        DEV_PAS.T_SALES_ORGAN TSO,
                                        DEV_PAS.T_CONTRACT_AGENT TCA
                                  WHERE B.AGENT_CODE = TCA.AGENT_CODE 
                   AND TCA.POLICY_CODE = TPA.POLICY_CODE
                   AND TCA.IS_CURRENT_AGENT = '1'
                   AND TSO.SALES_ORGAN_CODE = B.GROUP_CODE
                                    AND ROWNUM = 1) YINYZU, /* 现服务人员组 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN NULL 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN A.SALES_ORGAN_NAME
      WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '2' AND ROWNUM =1 START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A,DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYBU, /* 现服务人员部 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN A.SALES_ORGAN_NAME 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                           WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A, DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYQU, 
                                      (SELECT TCA.AGENT_START_DATE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as AGENT_START_DATE, /* 现服务人员服务起始日期 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER_CODE, /* 现服务人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER, /* 现服务人员姓名 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Code, /* 原始销售人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Name, /* 原始销售人员姓名 */
                                      TCBP.ORDER_ID,
                                       TCM.MULTI_MAINRISK_FLAG,
                                       CASE WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN 
                                       TCBP.BUSI_ITEM_ID
                                       ELSE TCBP.MASTER_BUSI_ITEM_ID
                                         END AS BUSI_ITEM_ID,
                             TCBP.MASTER_BUSI_ITEM_ID
                                FROM DEV_PAS.V_PREM_ARAP       TPA,
                                 DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                                     DEV_PAS.T_CONTRACT_MASTER TCM,
                                     DEV_PAS.T_LIABILITY_STATUS TCC,
                                     DEV_PAS.T_CONTRACT_AGENT  TCA
                               WHERE TPA.POLICY_CODE = TCBP.POLICY_CODE
                                 AND TPA.Busi_Prod_Code = TCBP.Busi_Prod_Code
                               	 AND TCBP.POLICY_ID = TCM.POLICY_ID
                                 AND TCM.POLICY_ID = TCA.POLICY_ID
                  				 AND TCBP.LIABILITY_STATE <>'3'
                                 AND ((TCM.LIABILITY_STATE = '4'
                          AND TCM.LAPSE_CAUSE = '1')or
                          (TCM.LIABILITY_STATE = '1' and '4' = tcbp.LIABILITY_STATE))
                                 AND TCM.LIABILITY_STATE = TCC.STATUS_CODE
                                 AND TCA.IS_CURRENT_AGENT='1'
                                  AND TPA.FEE_STATUS ='02'
                                  AND TPA.SERVICE_CODE = 'PNL'
                                  AND TPA.DERIV_TYPE = '003'
                                 AND TPA.DUE_TIME = (SELECT MAX(P.DUE_TIME)
                                 						FROM DEV_PAS.V_PREM_ARAP P
                                					  WHERE P.POLICY_CODE = TPA.POLICY_CODE
                                  						AND P.FEE_STATUS = '02'
                                  						AND P.SERVICE_CODE = 'PNL'
                                  						AND P.IS_RISK_MAIN = '1'
                                  						AND P.DERIV_TYPE = '003')
			]]>
         <include refid="queryImntForeverFailureByCondition" />
         <![CDATA[     GROUP BY TPA.POLICY_CODE,
                            TCA.LAST_AGENT_CODE,
                            TCA.LAST_AGENT_NAME,
                            TCA.AGENT_CODE,
                            TCA.AGENT_NAME,
                            TCM.POLICY_ID,
                            TCC.STATUS_NAME,
                            TPA.BANK_ACCOUNT,
                            TPA.HOLDER_NAME,
                            TPA.INSURED_NAME,
                            TCA.AGENT_ORGAN_CODE,
                            tcm.organ_code,
                            TCBP.LAPSE_DATE,
                            TPA.UNIT_NUMBER,
                            TCM.SERVICE_BANK_BRANCH,
                            TCM.POLICY_FLAG,
                            TCM.CHANNEL_TYPE,
                            tpa.AGENT_CODE,
                            tpa.busi_prod_code,
                            tpa.busi_prod_name,
                            tpa.paid_count,
                            tpa.bank_code,
                            TCM.RELATION_POLICY_CODE,
                            TCM.MULTI_MAINRISK_FLAG,
                            tcbp.ORDER_ID,
                            TCBP.BUSI_ITEM_ID,
                            TCBP.POLICY_CODE,
                            TCM.POLICY_CODE,
                            tpa.FEE_AMOUNT,
                            tcbp.master_BUSI_ITEM_ID
                            ) A
                 ORDER BY A.POLICY_CODE,A.ORDER_ID)Z GROUP BY Z.POLICY_CODE,
                     Z.HOLDER_NAME,
                     Z.INSURED_NAME,
                     Z.BANK_ACCOUNT,
                     Z.LAPSE_DATE,
                     Z.NOW_AGENT_NAME,
                     Z.NOW_AGENT_CODE,
                     Z.DUE_TIME,
                     Z.CHANNEL_ORG_CODE,
                     Z.PAY_NEXT,
                     Z.SERVICE_BANK_BRANCH,
                     Z.STATE,
           Z.CITY,
           Z.DISTRICT,
           Z.ADDRESS,
                     Z.TEL,
                     Z.POLICY_TYPE,
                     Z.LAST_AGENT_CODE,
                     Z.LAST_AGENT_NAME,
                     Z.ORGAN_CODE,
                     Z.SALES_ORGAN_CODE,
                     Z.STATUS_NAME,
                     Z.ORIGINAL_CHANNEL_TYPE_NAME, 
                             Z.SALES_CHANNEL_NAME, 
                             Z.YINYZU,
                             Z.YINYBU,
                            Z.YINYQU, 
                             Z.AGENT_START_DATE,
                             Z.SERVICE_HANDLER_CODE,
                             Z.SERVICE_HANDLER,
                             Z.ORIGINAL_AGENT_CODE,
                             Z.ORIGINAL_AGENT_NAME,
                             Z.BUSI_PROD_CODE,
                             Z.BUSI_PROD_NAME,
                             Z.PAID_COUNT,
                             Z.PER_LAPSE_DATE,
                             Z.BANK_CODE,
                             Z.SERVICE_BANK_CODE,
                             Z.MULTI_MAINRISK_FLAG,
                             Z.BUSI_ITEM_ID
                 ) E WHERE ROWNUM <= #{LESS_NUM})F WHERE RN > #{GREATER_NUM}
         ]]>
	</select>

	<!-- 团险值域-查询符合条件的即将永久失效的清单 -->
    <select id="PA_queryGroupImntForeverFailureReport" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[SELECT ROWNUM RN,
                             A.POLICY_CODE,
                             A.HOLDER_NAME HOLDER_NAME,
                             A.BANK_ACCOUNT BANK_ACCOUNT,
                             A.LAPSE_DATE LAPSE_DATE,
                             A.AGENT_NAME NOW_AGENT_NAME,
                             A.AGENT_CODE NOW_AGENT_CODE,
                             A.organ_code,
                             A.STATUS_NAME,
                              (SELECT CASE WHEN TP.LAST_AGENT_CODE IS NOT NULL THEN   TP.LAST_AGENT_CODE ELSE  TP.AGENT_CODE  END
                                    FROM DEV_PAS.V_Prem tp
                                   WHERE Tp.POLICY_CODE = A.POLICY_CODE
                                      AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                     AND ROWNUM = 1) LAST_AGENT_CODE,
                                 (SELECT CASE WHEN TP.LAST_AGENT_NAME IS NOT NULL THEN TP.LAST_AGENT_NAME ELSE  TP.AGENT_NAME END
                                    FROM DEV_PAS.V_PREM TP
                                   WHERE TP.POLICY_CODE = A.POLICY_CODE
                                   AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                     AND ROWNUM = 1) LAST_AGENT_NAME,
                             (SELECT SUM(AA.FEE_AMOUNT)
                                FROM DEV_PAS.V_PREM_ARAP AA
                               WHERE AA.POLICY_CODE = A.POLICY_CODE
                               AND AA.UNIT_NUMBER = A.UNIT_NUMBER
                               GROUP BY AA.UNIT_NUMBER) TOTAL_PREM_AF,
                             (SELECT MAX(BB.DUE_TIME)
                                FROM DEV_PAS.V_PREM_ARAP BB
                               WHERE BB.UNIT_NUMBER = A.UNIT_NUMBER) DUE_TIME,
                             (SELECT TUO.SALES_ORGAN_NAME
                                FROM DEV_PAS.T_SALES_ORGAN TUO
                               WHERE A.AGENT_ORGAN_CODE = TUO.SALES_ORGAN_CODE
                                 AND ROWNUM = 1) CHANNEL_ORG_CODE,
                             (SELECT TPM.NAME
                                FROM DEV_PAS.T_PAYER_ACCOUNT TPAC,
                                     DEV_PAS.T_PAY_MODE      TPM
                               WHERE TPAC.POLICY_ID = A.POLICY_ID
                                 AND TPAC.PAY_MODE = TPM.CODE
                                 AND ROWNUM = 1) PAY_NEXT,
                             (SELECT T.BANK_BRANCH_NAME
                                FROM DEV_PAS.T_BANK_BRANCH T
                               WHERE T.BANK_BRANCH_CODE = a.SERVICE_BANK_BRANCH) SERVICE_BANK_BRANCH,
                             (SELECT TA.ADDRESS
                                FROM DEV_PAS.T_ADDRESS       TA,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TA.ADDRESS_ID = TPH.ADDRESS_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) ADDRESS,
                                    (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.state) state,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.city) city,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.district) district,
                                 (SELECT distinct TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_AGENT TAA,
                                                                   DEV_PAS.T_SALES_ORGAN TSO WHERE TAA.AGENT_CODE = A.AGENT_CODE
                                                                   AND TSO.SALES_ORGAN_CODE = TAA.SALES_ORGAN_CODE )SALES_ORGAN_CODE,
                    (SELECT CASE
                                   WHEN TC.MOBILE_TEL IS NOT NULL THEN
                                        TC.MOBILE_TEL || '(M)'
                                       ELSE
                                        TC.HOUSE_TEL || '(H)'
                                     END
                                FROM DEV_PAS.T_CUSTOMER      TC,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) TEL,
                             (SELECT TPF.FLAG_NAME FROM APP___PAS__DBUSER.T_POLICY_FLAG TPF WHERE TPF.FLAG_CODE = A.POLICY_FLAG) AS POLICY_TYPE,
                             A.Original_Channel_Type_Name, /* 原销售渠道 */
                             A.sales_channel_name, /* 现服务渠道 */
                            A.YINYZU, /* 现服务人员组 */
                             A.YINYBU, /* 现服务人员部 */
                             A.YINYQU, 
                             a.AGENT_START_DATE,
                             a.SERVICE_HANDLER_CODE,
                             a.SERVICE_HANDLER,
                             a.Original_Agent_Code,
                             a.Original_Agent_Name,
                             a.busi_prod_code,
                             a.busi_prod_name,
                             a.paid_count,
                             a.bank_code,
                             a.per_lapse_date,
                             a.Service_Bank_Branch as Service_Bank_code
                        FROM (SELECT distinct TPA.POLICY_CODE,
                                     TCA.AGENT_CODE,
                                     TCA.AGENT_NAME,
                                     TCM.POLICY_ID,
                                     TPA.BANK_ACCOUNT,
                                     TPA.HOLDER_NAME,
                                     TCA.AGENT_ORGAN_CODE,
                                     tpa.busi_prod_code,
                                     tpa.busi_prod_name,
                                     tpa.paid_count,
                                    add_months(TCM.lapse_date,
                                      ]]>
           		   <choose> 
           		   <when test="failure_year == '00'">24</when>
           		   <otherwise>60</otherwise>
           		   </choose> 
           		   <![CDATA[
                                     ) as per_lapse_date,
                                     TCM.LAPSE_DATE,
                                     TCA.LAST_AGENT_CODE,
                                     TCA.LAST_AGENT_NAME,
                                     tpa.bank_code,
                                     tcm.organ_code,
                                     TCC.STATUS_NAME,
                                     TPA.UNIT_NUMBER,
                                     TCM.POLICY_FLAG,
                                     tcm.Service_Bank_Branch,
                                    (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.Policy_Id = TCM.Policy_Id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_NB_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS ORIGINAL_CHANNEL_TYPE_NAME,
                                (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.Policy_Id = TCM.Policy_Id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_CURRENT_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS SALES_CHANNEL_NAME,
                              (SELECT TSO.SALES_ORGAN_NAME
                                   FROM DEV_PAS.T_AGENT       B,
                                        DEV_PAS.T_SALES_ORGAN TSO,
                                        DEV_PAS.T_CONTRACT_AGENT TCA
                                  WHERE B.AGENT_CODE = TCA.AGENT_CODE 
                   AND TCA.POLICY_CODE = TPA.POLICY_CODE
                   AND TCA.IS_CURRENT_AGENT = '1'
                   AND TSO.SALES_ORGAN_CODE = B.GROUP_CODE
                                    AND ROWNUM = 1) YINYZU, /* 现服务人员组 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN NULL 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN A.SALES_ORGAN_NAME
      WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '2' AND ROWNUM =1 START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A,DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYBU, /* 现服务人员部 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN A.SALES_ORGAN_NAME 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                           WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A, DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYQU, 
                                      (SELECT TCA.AGENT_START_DATE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as AGENT_START_DATE, /* 现服务人员服务起始日期 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER_CODE, /* 现服务人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER, /* 现服务人员姓名 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Code, /* 原始销售人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Name /* 原始销售人员姓名 */
                                FROM DEV_PAS.V_PREM_ARAP       TPA,
                                     DEV_PAS.T_CONTRACT_MASTER TCM,
                                     DEV_PAS.T_LIABILITY_STATUS TCC,
                                     DEV_PAS.T_CONTRACT_AGENT  TCA
                               WHERE TPA.POLICY_CODE = TCM.POLICY_CODE
                                 AND TCM.POLICY_ID = TCA.POLICY_ID
                                 AND TCM.LIABILITY_STATE = '4'
                                 AND TCM.LAPSE_CAUSE IN ('1', '2', '6')
                                 AND TCM.LIABILITY_STATE = TCC.STATUS_CODE
                                 AND TCA.IS_CURRENT_AGENT='1'
                                  AND TPA.FEE_STATUS = '02'
                                  AND TPA.SERVICE_CODE = 'PNL'
                                 AND TPA.IS_RISK_MAIN = '1'
                                 AND TPA.DERIV_TYPE = '003'
                                 AND TPA.DUE_TIME = (SELECT MAX(P.DUE_TIME)
                                 						FROM DEV_PAS.V_PREM_ARAP P
                                					WHERE P.POLICY_CODE = TPA.POLICY_CODE
                                  					  AND P.FEE_STATUS = '02'
                                  					  AND P.SERVICE_CODE = 'PNL'
                                                      AND P.IS_RISK_MAIN = '1'
                                                      AND P.DERIV_TYPE = '003')
								]]>
				           <include refid="queryImntForeverFailureByCondition" />
				          <![CDATA[   GROUP BY TPA.POLICY_CODE,
                            TCA.LAST_AGENT_CODE,
                            TCA.LAST_AGENT_NAME,
                            TCA.AGENT_CODE,
                            TCA.AGENT_NAME,
                            TCM.POLICY_ID,
                            TCC.STATUS_NAME,
                            TPA.BANK_ACCOUNT,
                            TPA.HOLDER_NAME,
                            TCA.AGENT_ORGAN_CODE,
                            tcm.organ_code,
                            TCM.LAPSE_DATE,
                            TPA.UNIT_NUMBER,
                            TCM.SERVICE_BANK_BRANCH,
                            TCM.POLICY_FLAG,
                            TCM.CHANNEL_TYPE,
                            tpa.AGENT_CODE,
                            tpa.busi_prod_code,
                            tpa.busi_prod_name,
                            tpa.paid_count,
                            tpa.bank_code
                            ) A where 1 = 1
				          ]]>
    </select>


	<!-- 团险值域-查询个数操作 -->
	<select id="PA_findGroupImntForeverFailureTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1)
                        FROM (SELECT distinct TPA.POLICY_CODE,
                                     TCA.AGENT_CODE,
                                     TCA.AGENT_NAME,
                                     TCM.POLICY_ID,
                                     TPA.BANK_ACCOUNT,
                                     TPA.HOLDER_NAME,
                                     TCA.AGENT_ORGAN_CODE,
                                     tpa.busi_prod_code,
                                     tpa.busi_prod_name,
                                     tpa.paid_count,
                                     add_months(tcm.lapse_date,24) as per_lapse_date,
                                     TCM.LAPSE_DATE,
                                     TCA.LAST_AGENT_CODE,
                                     TCA.LAST_AGENT_NAME,
                                     tpa.bank_code,
                                     tcm.organ_code,
                                     TCC.STATUS_NAME,
                                     TPA.UNIT_NUMBER,
                                     TCM.POLICY_FLAG,
                                     tcm.Service_Bank_Branch,
                                     (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.POLICY_id = TCM.POLICY_id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_NB_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS ORIGINAL_CHANNEL_TYPE_NAME,
                                (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.POLICY_id = TCM.POLICY_id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_CURRENT_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS SALES_CHANNEL_NAME,
                              (SELECT TSO.SALES_ORGAN_NAME
                                   FROM DEV_PAS.T_AGENT       B,
                                        DEV_PAS.T_SALES_ORGAN TSO,
                                        DEV_PAS.T_CONTRACT_AGENT TCA
                                  WHERE B.AGENT_CODE = TCA.AGENT_CODE 
                   AND TCA.POLICY_CODE = TPA.POLICY_CODE
                   AND TCA.IS_CURRENT_AGENT = '1'
                   AND TSO.SALES_ORGAN_CODE = B.GROUP_CODE
                                    AND ROWNUM = 1) YINYZU, /* 现服务人员组 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN NULL 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN A.SALES_ORGAN_NAME
      WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '2' AND ROWNUM =1 START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A,DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYBU, /* 现服务人员部 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN A.SALES_ORGAN_NAME 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                           WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A, DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYQU, 
                                      (SELECT TCA.AGENT_START_DATE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as AGENT_START_DATE, /* 现服务人员服务起始日期 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER_CODE, /* 现服务人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER, /* 现服务人员姓名 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Code, /* 原始销售人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Name /* 原始销售人员姓名 */
                                FROM DEV_PAS.V_PREM_ARAP       TPA,
                                     DEV_PAS.T_CONTRACT_MASTER TCM,
                                     DEV_PAS.T_LIABILITY_STATUS TCC,
                                     DEV_PAS.T_CONTRACT_AGENT  TCA
                               WHERE TPA.POLICY_CODE = TCM.POLICY_CODE
                                 AND TCM.POLICY_ID = TCA.POLICY_ID
                                 AND TCM.LIABILITY_STATE = '4'
                                 AND TCM.LAPSE_CAUSE IN ('1', '2', '6')
                                 AND TCM.LIABILITY_STATE = TCC.STATUS_CODE
                                 AND TCA.IS_CURRENT_AGENT='1'
                                  AND TPA.FEE_STATUS = '02'
                                  AND TPA.SERVICE_CODE = 'PNL'
                                  AND TPA.IS_RISK_MAIN = '1'
                                  and tpa.deriv_type = '003'
                                   AND TPA.DUE_TIME = (SELECT MAX(P.DUE_TIME)
                                 						FROM DEV_PAS.V_PREM_ARAP P
                                					  WHERE P.POLICY_CODE = TPA.POLICY_CODE
                                  						AND P.FEE_STATUS = '02'
                                  						AND P.SERVICE_CODE = 'PNL'
                                  						AND P.IS_RISK_MAIN = '1'
                                  						AND P.DERIV_TYPE = '003')
						]]>
		           <include refid="queryImntForeverFailureByCondition" />
		          <![CDATA[
		          		  GROUP BY TPA.POLICY_CODE,
                            TCA.LAST_AGENT_CODE,
                            TCA.LAST_AGENT_NAME,
                            TCA.AGENT_CODE,
                            TCA.AGENT_NAME,
                            TCM.POLICY_ID,
                            TCC.STATUS_NAME,
                            TPA.BANK_ACCOUNT,
                            TPA.HOLDER_NAME,
                            TCA.AGENT_ORGAN_CODE,
                            tcm.organ_code,
                            TCM.LAPSE_DATE,
                            TPA.UNIT_NUMBER,
                            TCM.SERVICE_BANK_BRANCH,
                            TCM.POLICY_FLAG,
                            TCM.CHANNEL_TYPE,
                            tpa.AGENT_CODE,
                            tpa.busi_prod_code,
                            tpa.busi_prod_name,
                            tpa.paid_count,
                            tpa.bank_code
                            ) A
		          ]]>
	</select>

		<!-- 团险值域-分页查询操作 -->
	<select id="PA_findGroupImntForeverFailureForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT F.POLICY_CODE,
             F.HOLDER_NAME,
             F.BANK_ACCOUNT,
             F.LAPSE_DATE,
             F.NOW_AGENT_NAME,
             F.NOW_AGENT_CODE,
             F.TOTAL_PREM_AF,
             F.DUE_TIME,
             F.CHANNEL_ORG_CODE,
             F.PAY_NEXT,
             F.SERVICE_BANK_BRANCH,
             F.ADDRESS,
             F.TEL,
             F.POLICY_TYPE,
             F.LAST_AGENT_CODE,
             F.LAST_AGENT_NAME,
             F.ORGAN_CODE,
             F.SALES_ORGAN_CODE,
             F.STATUS_NAME,
             F.Original_Channel_Type_Name, /* 原销售渠道 */
             F.sales_channel_name, /* 现服务渠道 */
             F.YINYZU, /* 现服务人员组 */
             F.YINYBU, /* 现服务人员部 */
             F.YINYQU, 
             F.AGENT_START_DATE,
             F.SERVICE_HANDLER_CODE,
             F.SERVICE_HANDLER,
             F.Original_Agent_Code,
             F.Original_Agent_Name,
             F.busi_prod_code,
             F.busi_prod_name,
             F.paid_count,
            F.per_lapse_date,
            F.bank_code,
            F.Service_Bank_code
        FROM (SELECT 
                     E.POLICY_CODE,
                     E.HOLDER_NAME,
                     E.BANK_ACCOUNT,
                     E.LAPSE_DATE,
                     E.NOW_AGENT_NAME,
                     E.NOW_AGENT_CODE,
                     E.TOTAL_PREM_AF,
                     E.DUE_TIME,
                     E.CHANNEL_ORG_CODE,
                     E.PAY_NEXT,
                     E.SERVICE_BANK_BRANCH,
                     E.state || E.city || E.district || E.ADDRESS as ADDRESS,
                     E.TEL,
                     E.POLICY_TYPE,
                     E.LAST_AGENT_CODE,
                     E.LAST_AGENT_NAME,
                     E.ORGAN_CODE,
                     E.SALES_ORGAN_CODE,
                      ROWNUM RN,
                     E.STATUS_NAME,
                     E.Original_Channel_Type_Name, /* 原销售渠道 */
                             E.sales_channel_name, /* 现服务渠道 */
                             E.YINYZU, /* 现服务人员组 */
                             E.YINYBU, /* 现服务人员部 */
                            E.YINYQU, 
                             E.AGENT_START_DATE,
                             E.SERVICE_HANDLER_CODE,
                             E.SERVICE_HANDLER,
                             E.Original_Agent_Code,
                             E.Original_Agent_Name,
                             E.busi_prod_code,
                             E.busi_prod_name,
                             E.paid_count,
                             E.per_lapse_date,
                             E.bank_code,
                             E.Service_Bank_code
                FROM (SELECT ROWNUM RN,
                             A.POLICY_CODE,
                             A.HOLDER_NAME HOLDER_NAME,
                             A.BANK_ACCOUNT BANK_ACCOUNT,
                             A.LAPSE_DATE LAPSE_DATE,
                             A.AGENT_NAME NOW_AGENT_NAME,
                             A.AGENT_CODE NOW_AGENT_CODE,
                             A.organ_code,
                             A.STATUS_NAME,
                              (SELECT CASE WHEN TP.LAST_AGENT_CODE IS NOT NULL THEN   TP.LAST_AGENT_CODE ELSE  TP.AGENT_CODE  END
                                    FROM DEV_PAS.V_Prem tp
                                   WHERE Tp.POLICY_CODE = A.POLICY_CODE
                                      AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                     AND ROWNUM = 1) LAST_AGENT_CODE,
                                 (SELECT CASE WHEN TP.LAST_AGENT_NAME IS NOT NULL THEN TP.LAST_AGENT_NAME ELSE  TP.AGENT_NAME END
                                    FROM DEV_PAS.V_PREM TP
                                   WHERE TP.POLICY_CODE = A.POLICY_CODE
                                   AND   TP.UNIT_NUMBER = A.UNIT_NUMBER
                                     AND ROWNUM = 1) LAST_AGENT_NAME,
                             (SELECT SUM(AA.FEE_AMOUNT)
                                FROM DEV_PAS.V_PREM_ARAP AA
                               WHERE AA.POLICY_CODE = A.POLICY_CODE
                               AND AA.UNIT_NUMBER = A.UNIT_NUMBER
                               GROUP BY AA.UNIT_NUMBER) TOTAL_PREM_AF,
                             (SELECT MAX(BB.DUE_TIME)
                                FROM DEV_PAS.V_PREM_ARAP BB
                               WHERE BB.UNIT_NUMBER = A.UNIT_NUMBER) DUE_TIME,
                             (SELECT TUO.SALES_ORGAN_NAME
                                FROM DEV_PAS.T_SALES_ORGAN TUO
                               WHERE A.AGENT_ORGAN_CODE = TUO.SALES_ORGAN_CODE
                                 AND ROWNUM = 1) CHANNEL_ORG_CODE,
                             (SELECT TPM.NAME
                                FROM DEV_PAS.T_PAYER_ACCOUNT TPAC,
                                     DEV_PAS.T_PAY_MODE      TPM
                               WHERE TPAC.POLICY_ID = A.POLICY_ID
                                 AND TPAC.PAY_MODE = TPM.CODE
                                 AND ROWNUM = 1) PAY_NEXT,
                             (SELECT T.BANK_BRANCH_NAME
                                FROM DEV_PAS.T_BANK_BRANCH T
                               WHERE T.BANK_BRANCH_CODE = a.SERVICE_BANK_BRANCH) SERVICE_BANK_BRANCH,
                             (SELECT distinct TA.ADDRESS
                                FROM DEV_PAS.T_ADDRESS       TA,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TA.ADDRESS_ID = TPH.ADDRESS_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) ADDRESS,
                                    (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.state) state,
                           
                       (select  distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.city) city,
                           
                       (select distinct td.name
                          from dev_pas.t_address       ta,
                               dev_pas.t_policy_holder tp,
                               dev_pas.t_district      td
                         where TA.ADDRESS_ID = TP.ADDRESS_ID
                           and tp.policy_id = a.policy_id
                           and td.code = ta.district) district,
                                 (SELECT distinct  TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_AGENT TAA,
                                                                   DEV_PAS.T_SALES_ORGAN TSO WHERE TAA.AGENT_CODE = A.AGENT_CODE
                                                                   AND TSO.SALES_ORGAN_CODE = TAA.SALES_ORGAN_CODE )SALES_ORGAN_CODE,
                    (SELECT CASE
                                   WHEN TC.MOBILE_TEL IS NOT NULL THEN
                                        TC.MOBILE_TEL || '(M)'
                                       ELSE
                                        TC.HOUSE_TEL || '(H)'
                                     END
                                FROM DEV_PAS.T_CUSTOMER      TC,
                                     DEV_PAS.T_POLICY_HOLDER TPH
                               WHERE TC.CUSTOMER_ID = TPH.CUSTOMER_ID
                                 AND TPH.POLICY_ID = A.POLICY_ID
                                 AND ROWNUM = 1) TEL,
                             (SELECT TPF.FLAG_NAME FROM APP___PAS__DBUSER.T_POLICY_FLAG TPF WHERE TPF.FLAG_CODE = A.POLICY_FLAG) AS POLICY_TYPE,
                             A.Original_Channel_Type_Name, /* 原销售渠道 */
                             A.sales_channel_name, /* 现服务渠道 */
                            A.YINYZU, /* 现服务人员组 */
                             A.YINYBU, /* 现服务人员部 */
                             A.YINYQU, 
                             a.AGENT_START_DATE,
                             a.SERVICE_HANDLER_CODE,
                             a.SERVICE_HANDLER,
                             a.Original_Agent_Code,
                             a.Original_Agent_Name,
                             a.busi_prod_code,
                             a.busi_prod_name,
                             a.paid_count,
                             a.bank_code,
                             a.per_lapse_date,
                             a.Service_Bank_Branch as Service_Bank_code
                        FROM (SELECT distinct TPA.POLICY_CODE,
                                     TCA.AGENT_CODE,
                                     TCA.AGENT_NAME,
                                     TCM.POLICY_ID,
                                     TPA.BANK_ACCOUNT,
                                     TPA.HOLDER_NAME,
                                     TCA.AGENT_ORGAN_CODE,
                                     tpa.busi_prod_code,
                                     tpa.busi_prod_name,
                                     tpa.paid_count,
                                     add_months(tcm.lapse_date,24) as per_lapse_date,
                                     TCM.LAPSE_DATE,
                                     TCA.LAST_AGENT_CODE,
                                     TCA.LAST_AGENT_NAME,
                                     tpa.bank_code,
                                     tcm.organ_code,
                                     TCC.STATUS_NAME,
                                     TPA.UNIT_NUMBER,
                                     TCM.POLICY_FLAG,
                                     tcm.Service_Bank_Branch,
                                     (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.POLICY_id = TCM.POLICY_id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_NB_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS ORIGINAL_CHANNEL_TYPE_NAME,
                                (SELECT TSC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL  TSC,
                                        DEV_PAS.T_CONTRACT_AGENT TCA,
                                        DEV_PAS.T_AGENT          TA
                                  WHERE TCA.POLICY_id = TCM.POLICY_id
                                    AND TCA.AGENT_CODE = TA.AGENT_CODE
                                    AND TCA.IS_CURRENT_AGENT = '1'
                                    AND TSC.SALES_CHANNEL_CODE =
                                        TA.AGENT_CHANNEL) AS SALES_CHANNEL_NAME,
                               (SELECT TSO.SALES_ORGAN_NAME
                                   FROM DEV_PAS.T_AGENT       B,
                                        DEV_PAS.T_SALES_ORGAN TSO,
                                        DEV_PAS.T_CONTRACT_AGENT TCA
                                  WHERE B.AGENT_CODE = TCA.AGENT_CODE 
                   AND TCA.POLICY_CODE = TPA.POLICY_CODE
                   AND TCA.IS_CURRENT_AGENT = '1'
                   AND TSO.SALES_ORGAN_CODE = B.GROUP_CODE
                                    AND ROWNUM = 1) YINYZU, /* 现服务人员组 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN NULL 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN A.SALES_ORGAN_NAME
      WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '2' AND ROWNUM =1 START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A,DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYBU, /* 现服务人员部 */
                                (SELECT (CASE WHEN A.ORGAN_LEVEL_CODE = '1' THEN A.SALES_ORGAN_NAME 
                                       WHEN A.ORGAN_LEVEL_CODE = '2' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                           WHEN A.ORGAN_LEVEL_CODE = '3' THEN (SELECT TSO.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN TSO WHERE TSO.ORGAN_LEVEL_CODE = '1' START WITH TSO.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE)
                                       ELSE A.SALES_ORGAN_NAME END) AREA
                                FROM DEV_PAS.T_SALES_ORGAN A, DEV_PAS.T_AGENT TA            
                  WHERE TA.AGENT_CODE = TPa.AGENT_CODE AND A.SALES_ORGAN_CODE =TA.SALES_ORGAN_CODE AND ROWNUM =1
                                ) YINYQU, 
                                      (SELECT TCA.AGENT_START_DATE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as AGENT_START_DATE, /* 现服务人员服务起始日期 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER_CODE, /* 现服务人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_CURRENT_AGENT = '1') as SERVICE_HANDLER, /* 现服务人员姓名 */
                                        (SELECT TCA.AGENT_CODE
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Code, /* 原始销售人员工号 */
                                        (SELECT TCA.Agent_Name
                                           FROM Dev_Pas.T_Contract_Agent TCA
                                          WHERE TCA.Policy_Id = TCM.Policy_Id
                                            AND TCA.IS_NB_AGENT = '1') as Original_Agent_Name /* 原始销售人员姓名 */
                                FROM DEV_PAS.V_PREM_ARAP       TPA,
                                     DEV_PAS.T_CONTRACT_MASTER TCM,
                                     DEV_PAS.T_LIABILITY_STATUS TCC,
                                     DEV_PAS.T_CONTRACT_AGENT  TCA
                               WHERE TPA.POLICY_CODE = TCM.POLICY_CODE
                                 AND TCM.POLICY_ID = TCA.POLICY_ID
                                 AND TCM.LIABILITY_STATE = '4'
                                 AND TCM.LAPSE_CAUSE IN ('1', '2', '6')
                                 AND TCM.LIABILITY_STATE = TCC.STATUS_CODE
                                 AND TCA.IS_CURRENT_AGENT='1'
                                  AND TPA.FEE_STATUS ='02'
                                  AND TPA.SERVICE_CODE = 'PNL'
                                  AND TPA.IS_RISK_MAIN = '1'
                                  AND TPA.DERIV_TYPE = '003'
                                  AND TPA.DUE_TIME = (SELECT MAX(P.DUE_TIME)
                                 						FROM DEV_PAS.V_PREM_ARAP P
                                					  WHERE P.POLICY_CODE = TPA.POLICY_CODE
                                  						AND P.FEE_STATUS = '02'
                                  						AND P.SERVICE_CODE = 'PNL'
                                  						AND P.IS_RISK_MAIN = '1'
                                  						AND P.DERIV_TYPE = '003')
			]]>
         <include refid="queryImntForeverFailureByCondition" />
         <![CDATA[     GROUP BY TPA.POLICY_CODE,
                            TCA.LAST_AGENT_CODE,
                            TCA.LAST_AGENT_NAME,
                            TCA.AGENT_CODE,
                            TCA.AGENT_NAME,
                            TCM.POLICY_ID,
                            TCC.STATUS_NAME,
                            TPA.BANK_ACCOUNT,
                            TPA.HOLDER_NAME,
                            TCA.AGENT_ORGAN_CODE,
                            tcm.organ_code,
                            TCM.LAPSE_DATE,
                            TPA.UNIT_NUMBER,
                            TCM.SERVICE_BANK_BRANCH,
                            TCM.POLICY_FLAG,
                            TCM.CHANNEL_TYPE,
                            tpa.AGENT_CODE,
                            tpa.busi_prod_code,
                            tpa.busi_prod_name,
                            tpa.paid_count,
                            tpa.bank_code
                            ) A
                 ORDER BY A.POLICY_CODE) E WHERE ROWNUM <= #{LESS_NUM})F WHERE RN > #{GREATER_NUM}
         ]]>
	</select>

</mapper>