<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 领取形式变更 _mass_wb -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 保单信息 -->
	<select id="QRY_GC_find_Policy_Info" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
       SELECT 
        CU.OLD_CUSTOMER_ID,/*投保人客户号*/
        CU.CUSTOMER_NAME,/*投保人姓名*/
        PA.PAY_NEXT/*交费方式*/,
        PA.PAY_LOCATION,/*交费形式*/
        PA.NEXT_ACCOUNT_BANK,/*续期银行代码*/
        BK.BANK_NAME,/*银行名称*/
        PA.NEXT_ACCOUNT,/*银行账号:(续期缴费银行帐号)*/
        PA.NEXT_ACCOUNT_NAME/*续期账户名*/,
        (SELECT SUM(NVL(CP.STD_PREM_AF,0)+NVL(CP.EXTRA_PREM_AF,0)) FROM 
        DEV_PAS.T_CONTRACT_PRODUCT CP
        WHERE CP.POLICY_CODE=PH.POLICY_CODE 
        AND CP.LIABILITY_STATE!=3
        ) AS PREM_AF, /*保费*/
        (SELECT SUM(CP.AMOUNT) FROM DEV_PAS.T_CONTRACT_PRODUCT CP WHERE CP.POLICY_CODE=PH.POLICY_CODE AND CP.LIABILITY_STATE!=3) AS AMOUNT /*保额*/
    FROM 
    DEV_PAS.T_POLICY_HOLDER PH
    INNER JOIN DEV_PAS.T_CUSTOMER CU ON PH.CUSTOMER_ID=CU.CUSTOMER_ID
    INNER JOIN DEV_PAS.T_PAYER_ACCOUNT  PA ON PA.POLICY_ID=PH.POLICY_ID 
    INNER JOIN DEV_PAS.T_BANK BK ON BK.BANK_CODE=PA.NEXT_ACCOUNT_BANK
    WHERE PH.POLICY_CODE=#{policy_code}
         ]]>
	</select>
    <!-- 查询领取形式变更 -->
    <select id="QRY_GC_find_Get_Info" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[        
  SELECT DISTINCT '' AS PAY_MODE, /*领取形式代码*/
      '' AS ADD_NAME,  /*领取形式*/
       B.BANK_CODE,       /*领取银行编码*/
       B.ISSUE_BANK_NAME, /*领取银行名称*/
       B.BANK_ACCOUNT,    /*领取银行账户*/
       B.ACCO_NAME        /*领取银行户名*/
  FROM 
  DEV_PAS.T_CS_ACCEPT_CHANGE ACC
  INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE CHA on ACC.CHANGE_ID=CHA.CHANGE_ID AND ACC.ACCEPT_ID=CHA.ACCEPT_ID 
  LEFT JOIN DEV_PAS.T_CS_BANK_ACCOUNT B ON  ACC.CHANGE_ID=B.CHANGE_ID
 WHERE 1=1 AND B.OLD_NEW=1  AND CHA.POLICY_CODE =#{policy_code}
       AND ACC.ACCEPT_CODE =#{accept_code}  
         ]]>
    </select>
</mapper>