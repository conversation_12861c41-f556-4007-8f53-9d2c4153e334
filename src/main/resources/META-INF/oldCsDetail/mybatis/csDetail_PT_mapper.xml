<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="kehuPT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
        	  SELECT A.*, T.TYPE, /*证件类型*/
        G.GENDER_DESC /*性别*/
          FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
                       '投保人' CUSTOMER_ROLE, /*角色*/
                       A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY/*出生日期*/
                  FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
                  DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
                   AND H.POLICY_CODE = #{accept_status}) A,
                    DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G/**性别代码表*/
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
           AND A.CUSTOMER_GENDER = G.GENDER_CODE/**客户性别*/
        UNION
        SELECT A.*, T.TYPE, G.GENDER_DESC
          FROM (SELECT A.OLD_CUSTOMER_ID,
                       '被保人' CUSTOMER_ROLE,
                       A.CUSTOMER_NAME,
                       A.CUSTOMER_CERT_TYPE,
                       A.CUSTOMER_CERTI_CODE,
                       A.CUSTOMER_GENDER,
                       A.CUSTOMER_BIRTHDAY
                  FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                   DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                   AND H.POLICY_CODE = #{accept_status}) A,
               DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE
           AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="baodanPT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT CBP.BUSI_PROD_CODE,
		       BP.PRODUCT_NAME_SYS,
		       CUS.CUSTOMER_NAME,
		       AA.AMOUNT           YBE,
		       AA.UNIT             YFS,
		       AA.STD_PREM_AF      YBF,
		       B.AMOUNT            JBE,
		       B.UNIT              JFS,
		       B.STD_PREM_AF       JBF
		  FROM DEV_PAS.T_CS_POLICY_CHANGE PC,
		       DEV_PAS.T_CS_ACCEPT_CHANGE AC,
		       (SELECT DISTINCT P.BUSI_ITEM_ID A,
		                        P.CHANGE_ID,
		                        P.AMOUNT,
		                        P.UNIT,
		                        P.STD_PREM_AF
		          FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P
		         WHERE P.CHANGE_ID = (SELECT AC.CHANGE_ID
		      FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		     WHERE AC.CHANGE_ID = PC.CHANGE_ID
		       AND AC.ACCEPT_CODE = #{accept_code}
				   AND PC.POLICY_CODE =#{accept_status})
		           AND P.OLD_NEW = '0') AA,
		       (SELECT DISTINCT P.CHANGE_ID, P.AMOUNT, P.UNIT, P.STD_PREM_AF
		          FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P
		         WHERE P.CHANGE_ID = (SELECT AC.CHANGE_ID
		      FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		     WHERE AC.CHANGE_ID = PC.CHANGE_ID
		       AND AC.ACCEPT_CODE = #{accept_code}
				   AND PC.POLICY_CODE =#{accept_status})
		           AND P.OLD_NEW = '1') B,
		       DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
		       DEV_PAS.T_BUSINESS_PRODUCT BP,
		       DEV_PAS.T_INSURED_LIST H,
		       DEV_PAS.T_CUSTOMER CUS
		 WHERE PC.CHANGE_ID = AC.CHANGE_ID
		   AND PC.CHANGE_ID = PC.CHANGE_ID
		   AND AA.A = CBP.BUSI_ITEM_ID
		   AND CBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
		   AND H.POLICY_CODE = CBP.POLICY_CODE
		   AND H.CUSTOMER_ID = CUS.CUSTOMER_ID
		   AND PC.POLICY_CODE = #{accept_status}
		   AND AC.ACCEPT_CODE = #{accept_code}
         ]]>
	</select>
	<select id="jianPT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
        	SELECT L.DECREASE_CAUSE, /**减保原因*/
		        D.CAUSE_NAME/**减保原因名称原因名称*/
		     FROM DEV_PAS.T_DECREASE_PRODUCT L,
		     DEV_PAS.T_DECREASE_CAUSE D/**减保原因表*/
		    WHERE L.DECREASE_CAUSE=D.CAUSE_CODE
		    AND L.CHANGE_ID =
		    (SELECT AC.CHANGE_ID
				  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
				 WHERE AC.CHANGE_ID = PC.CHANGE_ID
				   AND AC.ACCEPT_CODE = #{accept_code}
				   AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
	
	<select id="btfhPT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
		SELECT X.FEE_AMOUNT /*补退费合计*/
		  FROM DEV_PAS.T_CS_POLICY_CHANGE X
			 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		 	 AND AC.ACCEPT_CODE = #{accept_code}
			 AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
	<select id="xianPT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT CBP.BUSI_PROD_CODE,/**险种代码*/
			CBP.POLICY_CODE,/**险种号*/
			BP.PRODUCT_NAME_SYS,/**险种名称*/
			X.FEE_AMOUNT /**补退费合计*/
			 FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP,
			DEV_PAS.T_BUSINESS_PRODUCT BP,
			DEV_PAS.T_CS_POLICY_CHANGE X
			 WHERE CBP.OLD_NEW='1'
			       AND BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID
			       AND X.CHANGE_ID=CBP.CHANGE_ID
			       AND CBP.CHANGE_ID= (SELECT AC.CHANGE_ID
			      FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
			     WHERE AC.CHANGE_ID = PC.CHANGE_ID
			       AND AC.ACCEPT_CODE = #{accept_code}
					   AND PC.POLICY_CODE =#{accept_status})
         ]]>
	</select>
	<select id="xzjbPT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			  SELECT CBP.BUSI_PRD_ID,
		         PA.BUSI_PROD_CODE, /*险种代码*/
		         PA.BUSI_PROD_NAME, /*险种名称*/
		         DECODE(PA.ARAP_FLAG, 2, PA.FEE_AMOUNT * (-1), PA.FEE_AMOUNT) PAY, /*费用金额*/
		         (T.TYPE_NAME) TNAME, /*费用类型*/
		         (W.TYPE_NAME) WNAME /*费用名称*/
		    FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
		    LEFT JOIN　DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP
		      ON AC.CHANGE_ID = CBP.CHANGE_ID
		    LEFT JOIN DEV_CAP.V_PREM_ARAP PA
		      ON PA.POLICY_CODE = CBP.POLICY_CODE
		     AND CBP.BUSI_PROD_CODE = PA.BUSI_PROD_CODE
		     AND AC.ACCEPT_CODE = PA.BUSINESS_CODE
		    LEFT JOIN DEV_PAS.T_FEE_TYPE T
		      ON PA.FEE_TYPE = T.CODE
		    LEFT JOIN DEV_PAS.T_WITHDRAW_TYPE W
		      ON PA.WITHDRAW_TYPE = W.TYPE_CODE   
		   WHERE AC.ACCEPT_CODE = #{accept_code}
		     AND CBP.OLD_NEW = '1'
         ]]>
	</select>
</mapper>