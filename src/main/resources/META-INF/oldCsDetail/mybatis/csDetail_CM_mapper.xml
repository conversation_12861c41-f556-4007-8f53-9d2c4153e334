<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="yuanCM_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
              SELECT DISTINCT A.* FROM(
                       SELECT A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY,/*出生日期*/
                       T.TYPE,/*证件类型代码*/
                       G.GENDER_DESC/*性别代码*/
                       FROM  DEV_PAS.T_CS_CUSTOMER A,/**客户表*/
                       DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                       DEV_PAS.T_CERTI_TYPE T,
                       DEV_PAS.T_GENDER G
                       WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                       AND H.POLICY_CODE =#{accept_status}
                       AND A.OLD_NEW='0'
                       AND A.CUSTOMER_CERT_TYPE = T.CODE
                       AND A.CUSTOMER_GENDER = G.GENDER_CODE)A
                     
         ]]>
  
	</select>
	<select id="gengCM_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
              SELECT DISTINCT A.* FROM(
                       SELECT A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY,/*出生日期*/
                       T.TYPE,/*证件类型代码*/
                       G.GENDER_DESC/*性别代码*/
                       FROM  DEV_PAS.T_CS_CUSTOMER A,/**客户表*/
                       DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                       DEV_PAS.T_CERTI_TYPE T,
                       DEV_PAS.T_GENDER G
                       WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                       AND H.POLICY_CODE = #{accept_status}
                       AND A.OLD_NEW='1'
                       AND A.CUSTOMER_CERT_TYPE = T.CODE
                       AND A.CUSTOMER_GENDER = G.GENDER_CODE)A
                     
         ]]>
  
	</select>
	<select id="bdxzCM_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT 
			CBP.POLICY_CODE,/**保单号码*/
			CBP.BUSI_PROD_CODE,/**险种代码*/
			CBP.VALIDATE_DATE,/**生效日期*/
			IL.INSURED_AGE,/**投保年龄*/
			/**补缴保费*//**补缴利息*//**退还保费*/                      
			CP.STD_PREM_AF,/**保费标准*/
			CP.AMOUNT,/**基本保额*/
			CP.UNIT,/**份数*/
			CP.BONUS_SA/**累计红利保额*/
			FROM DEV_PAS.T_CS_INSURED_LIST IL,/**被保人表*/
			DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP,/**险种表*/
			DEV_PAS.T_CS_CONTRACT_PRODUCT CP/**险种责任组表*/
			 WHERE CBP.POLICY_CODE=IL.POLICY_CODE
			 AND CBP.POLICY_CODE=CP.POLICY_CODE
			AND IL.POLICY_CODE=#{accept_status}
			AND IL.OLD_NEW='1'
         ]]>
	</select>
	<select id="btfhCM_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT X.FEE_AMOUNT 
		  FROM DEV_PAS.T_CS_POLICY_CHANGE X
		 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
	
		
</mapper>