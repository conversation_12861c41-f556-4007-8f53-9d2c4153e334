<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
    <!-- 综合查询_ER满期降低保额续保-->
    <!-- 查询附加险降额信息 -->
	<select id="QRY_ER_find_Add_Info" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[/*无销售方式*/
     SELECT 
       T.STD_PREM_AF,                                                /*保费标准*/
       (SELECT A.CUSTOMER_ID /*被保人客户号*/
  FROM DEV_PAS.T_INSURED_LIST A
 WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_ID,
       (SELECT B.CUSTOMER_NAME /*被保人姓名*/
  FROM DEV_PAS.T_INSURED_LIST A 
  LEFT JOIN DEV_PAS.T_CUSTOMER B 
    ON A.CUSTOMER_ID = B.CUSTOMER_ID 
 WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_NAME,
 C.BUSI_PROD_CODE,                                                    /*险种代码*/
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = C.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
  C.VALIDATE_DATE,                                                    /*险种生效日期*/
  (SELECT CC.INITIAL_PREM_DATE FROM DEV_PAS.T_CONTRACT_MASTER CC WHERE CC.POLICY_CODE= T.POLICY_CODE ) INITIAL_PREM_DATE,/*交费对应日*/
  C.MASTER_BUSI_ITEM_ID,         /*所属的主险险种ID*/                  /*主险编码*/
  RC.OLD_AMOUNT AMOUNT,                                                           /*原保额*/
  RC.NEW_AMOUNT RENEWAL_AMOUNT,  /*续保保额*/
  RC.OLD_UNIT,    /*原份数*/     
  RC.NEW_UNIT   RENEWAL_UNIT  /*续保份数*/                                          
  FROM DEV_PAS.T_CONTRACT_PRODUCT T /*保单险种责任组表*/
  LEFT JOIN DEV_PAS.T_EXTRA_PREM B  /*险种责任组加费表*/
    ON T.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C   /*险种表*/
   ON T.POLICY_CODE = C.POLICY_CODE
  AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID
  LEFT JOIN DEV_PAS.T_RENEW_CHANGE RC  /*续保险种变更表*/
   ON T.POLICY_ID = RC.POLICY_ID AND T.ITEM_ID = RC.ITEM_ID AND RENEW_CHANGE_TYPE=2 
 WHERE T.POLICY_CODE = #{policy_code}
  AND C.MASTER_BUSI_ITEM_ID IS NOT NULL
         ]]>
	</select>
</mapper>