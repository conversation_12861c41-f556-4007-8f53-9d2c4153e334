<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	
	<select id="kehutIT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
       		SELECT A.*, T.TYPE, /*证件类型*/
        G.GENDER_DESC /*性别*/
          FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
                       '投保人' CUSTOMER_ROLE, /*角色*/
                       A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY /*出生日期*/
                  FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
                  DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
                   AND H.POLICY_CODE = #{accept_status}) A,
                    DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G/**性别代码表*/
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
           AND A.CUSTOMER_GENDER = G.GENDER_CODE/**客户性别*/
        UNION
        SELECT A.*, T.TYPE, G.GENDER_DESC
          FROM (SELECT A.OLD_CUSTOMER_ID,
                       '被保人' CUSTOMER_ROLE,
                       A.CUSTOMER_NAME,
                       A.CUSTOMER_CERT_TYPE,
                       A.CUSTOMER_CERTI_CODE,
                       A.CUSTOMER_GENDER,
                       A.CUSTOMER_BIRTHDAY
                  FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                   DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                   AND H.POLICY_CODE =#{accept_status}) A,
               DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE
           AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="baodanIT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
       		SELECT  CBP.BUSI_PROD_CODE,/**险种代码*/
        BP.PRODUCT_NAME_SYS,/**险种名称*/
            ( SELECT A.CUSTOMER_NAME /*姓名*/ 
            FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
            DEV_PAS.T_CUSTOMER A/**客户表*/ WHERE H.CUSTOMER_ID=A.CUSTOMER_ID AND H.POLICY_CODE=#{accept_status} ) TNAME,
            ( SELECT A.CUSTOMER_NAME /*姓名*/ 
            FROM DEV_PAS.T_INSURED_LIST H, /**保单被保人列表*/
            DEV_PAS.T_CUSTOMER A/**客户表*/ WHERE H.CUSTOMER_ID=A.CUSTOMER_ID AND H.POLICY_CODE=#{accept_status} ) BNAME,
        CP.STD_PREM_AF,/**保费标准*/
       (SELECT A.EXTRA_PARA FROM DEV_PAS.T_EXTRA_PREM A/**险种责任组加费表*/
     WHERE  A.POLICY_CODE=#{accept_status} AND A.EXTRA_TYPE='1') EXTRA_PARA1,/*健康加费*/
    (SELECT A.EXTRA_PARA FROM DEV_PAS.T_EXTRA_PREM A 
    WHERE A.POLICY_CODE=#{accept_status} AND A.EXTRA_TYPE='2') EXTRA_PARA2 /*职业加费*/
        FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
        DEV_PAS.T_BUSINESS_PRODUCT BP,
        DEV_PAS.T_CS_CONTRACT_PRODUCT CP 
        WHERE BP.PRODUCT_CODE_SYS=CBP.BUSI_PROD_CODE
        AND CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
            AND CP.POLICY_CODE=#{accept_status}
            GROUP BY CBP.BUSI_PROD_CODE,BP.PRODUCT_NAME_SYS,CP.STD_PREM_AF
         ]]>
	</select>
	<select id="zhangIT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
       		SELECT PA.CREATE_DATE,/*账户创建日期*/
		     FT.FUND_CODE,/*账户代码*/
		 FT.TRANS_UNITS,/*账户单位数*/
		    IUP.PRICING_DATE,/* 最近计价日*/
		 IUP.BID_PRICE/*单位卖出价*/
		   FROM  DEV_PAS.T_POLICY_ACCOUNT PA/*保单账户基本信息表*/
		  LEFT JOIN  DEV_PAS.T_INVEST_UNIT_PRICE IUP /*投资单位价格表*/    
		  ON PA.ACCOUNT_ID=IUP.INVEST_ACCOUNT_ID
		  LEFT JOIN DEV_PAS.T_FUND_TRANS FT/*保单投资连结交易表*/
		  ON FT.POLICY_ID=PA.POLICY_ID
		  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP/*保单险种责任组表*/
		  ON CP.POLICY_ID=PA.POLICY_ID
		  WHERE CP.POLICY_CODE=#{accept_status}
         ]]>
	</select>
	<select id="tuidateIT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT CM.VALIDATE_DATE, /*保单生效日期*/
			 PA.ACKNOWLEDGE_DATE/*保单签收日期*/
			 /*交费对应日*/
			FROM DEV_PAS.T_CONTRACT_MASTER CM/*保单基本信息表*/
			LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT PA/*保单回执表*/
			ON CM.POLICY_ID=PA.POLICY_ID
			WHERE CM.POLICY_CODE=#{accept_status}
         ]]>
	</select>
	<select id="tuibaoIT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT A.SURRENDER_CAUSE, /*退保原因编码*/
			SC.SURRENDER_CAUSE_NAME/*退保原因*/
			  FROM DEV_PAS.T_SURRENDER A
			  LEFT JOIN DEV_PAS.T_SURRENDER_CAUSE SC
			  ON A.SURRENDER_CAUSE=SC.SURRENDER_CAUSE
			 WHERE A.CHANGE_ID =(SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
	<select id="butuiIT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT X.FEE_AMOUNT 
		  FROM DEV_PAS.T_CS_POLICY_CHANGE X
		 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
	<select id="bjfyIT_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			 SELECT C.FEE_AMOUNT, /**费用金额*/
		          T.TYPE_NAME,   /**费用类型*/
		          (W.TYPE_NAME) WNAME  /*费用名称*/
		     FROM DEV_PAS.V_PREM_ARAP C,
		     DEV_PAS.T_FEE_TYPE T,
		     DEV_PAS.T_WITHDRAW_TYPE W
		     WHERE T.CODE=C.FEE_TYPE
		     AND C.WITHDRAW_TYPE=W.TYPE_CODE
		    AND C.POLICY_CODE=#{accept_status}
         ]]>
	</select>
	
</mapper>