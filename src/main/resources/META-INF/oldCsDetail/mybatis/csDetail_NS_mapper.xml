<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
  <select id="tkehuNS_Page_Customer" resultType="java.util.Map"
    parameterType="java.util.Map"  >
    <![CDATA[
           SELECT A.CUSTOMER_NAME, /*姓名*/
                   A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                   A.CUSTOMER_CERTI_CODE, /*证件号码*/
                   T.TYPE/*证件类型*/
              FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
              DEV_PAS.T_CUSTOMER A,/**客户表*/
              DEV_PAS.T_CERTI_TYPE T
             WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
             AND A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
             AND H.POLICY_CODE = #{accept_status}
         ]]>
  </select>
  <select id="bkehuNS_Page_Customer" resultType="java.util.Map"
    parameterType="java.util.Map"  >
    <![CDATA[
  		     SELECT A.CUSTOMER_NAME, /*姓名*/
                  A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                  A.CUSTOMER_CERTI_CODE, /*证件号码*/
                  T.TYPE/*证件类型*/
             FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
             DEV_PAS.T_CUSTOMER A,/**客户表*/
             DEV_PAS.T_CERTI_TYPE T
            WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
            AND A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
            AND H.POLICY_CODE = #{accept_status}
         ]]>
	</select>
  <select id="dangqNS_Page_Customer" resultType="java.util.Map"
    parameterType="java.util.Map"  >
    <![CDATA[
	  		   SELECT DISTINCT B.OLD_CUSTOMER_ID, /*被保险人号码*/
                B.CUSTOMER_NAME, /*姓名*/
                C.BUSI_PROD_CODE, /*险种代码*/
                D.PRODUCT_NAME_SYS, /*险种名称*/
                E.STD_PREM_AF, /*保费标准*/
                E.AMOUNT, /*基本保额*/
                E.VALIDATE_DATE/*生效日期*/
			  FROM DEV_PAS.T_INSURED_LIST A/*保单被保人列表*/
			  LEFT JOIN DEV_PAS.T_CUSTOMER B/*客户表*/
			    ON A.CUSTOMER_ID = B.CUSTOMER_ID
			  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C/*险种表*/
			    ON A.POLICY_CODE = C.POLICY_CODE
			  LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT D/*业务产品*/
			    ON C.BUSI_PRD_ID = D.BUSINESS_PRD_ID
			  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT E/*业务产品*/
			    ON C.POLICY_CODE = E.POLICY_CODE
			   AND C.BUSI_ITEM_ID = E.BUSI_ITEM_ID
			 WHERE A.POLICY_CODE = #{accept_status}
         ]]>
	</select>
  <select id="xinzNS_Page_Customer" resultType="java.util.Map"
    parameterType="java.util.Map"  >
    <![CDATA[
		SELECT DISTINCT B.OLD_CUSTOMER_ID, /*被保险人号码*/
            B.CUSTOMER_NAME, /*姓名*/
            CP.BUSI_PROD_CODE, /*险种代码*/
            D.PRODUCT_NAME_SYS, /*险种名称*/
            E.STD_PREM_AF, /*保费标准*/
            E.AMOUNT /*基本保额*/
		    FROM(
		    SELECT CCBP.* FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC 
		INNER JOIN  DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL CCBP ON AC.CHANGE_ID=CCBP.CHANGE_ID 
		WHERE AC.ACCEPT_CODE=#{accept_code} AND CCBP.POLICY_CODE=#{accept_status}
		    ) CP INNER JOIN 
		     DEV_PAS.T_INSURED_LIST A ON CP.POLICY_CODE=A.POLICY_CODE/*保单被保人列表*/
		    INNER  JOIN DEV_PAS.T_CUSTOMER B/*客户表*/
		      ON A.CUSTOMER_ID = B.CUSTOMER_ID
		         INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT D/*业务产品*/
		      ON CP.BUSI_PROD_CODE = D.PRODUCT_CODE_SYS
		    INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT E/*业务产品*/
		      ON CP.POLICY_CODE = E.POLICY_CODE
		     AND CP.BUSI_ITEM_ID = E.BUSI_ITEM_ID
		   WHERE A.POLICY_CODE =#{accept_status}
         ]]>
	</select>
  <select id="toubaoNS_Page_Customer" resultType="java.util.Map"
    parameterType="java.util.Map"  >
    <![CDATA[
  		      SELECT B.SURVEY_VERSION,/**告知版别*/
		          B.SURVEY_CODE,/**告知编码*/
		           B.QUESTION_CONTENT,/**告知内容*/
		           A.SURVEY_MODULE_RESULT /**填写内容*/
		      FROM DEV_PAS.T_CS_QUESTIONAIRE_CUSTOMER A/**客户告知表*/ 
		      INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE PC ON  A.ACCEPT_ID=PC.ACCEPT_ID
		      LEFT JOIN DEV_PAS.T_QUESTIONAIRE_INFO B/**告知信息表*/ 
		      ON A.SURVEY_QUESTION_ID=B.SURVEY_QUESTION_ID 
			WHERE A.POLICY_CODE = #{accept_status} AND PC.ACCEPT_CODE=#{accept_code}
         ]]>
	</select>
</mapper>