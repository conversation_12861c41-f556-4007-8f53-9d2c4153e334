<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="xianCA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
          SELECT BP.PRODUCT_NAME_SYS/**险种名称*/
       FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP,
      DEV_PAS.T_BUSINESS_PRODUCT BP
       WHERE CBP.OLD_NEW='1'
             AND BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID
             AND CBP.CHANGE_ID=(SELECT AC.CHANGE_ID
          FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
         WHERE AC.CHANGE_ID = PC.CHANGE_ID
				   AND AC.ACCEPT_CODE =#{accept_code}
				   AND PC.POLICY_CODE =  #{accept_status})
         ]]>
	</select>
	<select id="yuanCA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
          SELECT T.AMOUNT /**原基本保额*/
		FROM DEV_PAS.T_CS_CONTRACT_PRODUCT T
		 WHERE T.CHANGE_ID =(SELECT AC.CHANGE_ID
          FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
         WHERE AC.CHANGE_ID = PC.CHANGE_ID
				   AND AC.ACCEPT_CODE =#{accept_code}
				   AND PC.POLICY_CODE = #{accept_status})
 				 AND T.OLD_NEW = '0'
         ]]>
	</select>
	<select id="bianCA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
          SELECT T.AMOUNT /**变更后基本保额*/
		FROM DEV_PAS.T_CS_CONTRACT_PRODUCT T
		 WHERE T.CHANGE_ID =(SELECT AC.CHANGE_ID
          FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
         WHERE AC.CHANGE_ID = PC.CHANGE_ID
				   AND AC.ACCEPT_CODE =#{accept_code}
				   AND PC.POLICY_CODE = #{accept_status})
 				   AND T.OLD_NEW = '1'
         ]]>
	</select>
</mapper>