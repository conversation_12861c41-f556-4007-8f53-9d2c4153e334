<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 保全查询 公司解约 -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="csDetailEA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT A.*,
			       T.TYPE,/*证件类型*/
			       G.GENDER_DESC/*性别*/
			  FROM (SELECT A.OLD_CUSTOMER_ID,/*客户号码*/
			               '投保人' CUSTOMER_ROLE,/*角色*/
			               A.CUSTOMER_NAME,/*姓名*/
			               A.CUSTOMER_CERT_TYPE,/*证件类型代码*/
			               A.CUSTOMER_CERTI_CODE,/*证件号码*/
			               A.CUSTOMER_GENDER,/*性别代码*/
			               A.CUSTOMER_BIRTHDAY,/*出生日期*/
			               H.POLICY_CODE/*代码编号*/
			          FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
			UNION 
			 SELECT A.*,
			       T.TYPE,
			       G.GENDER_DESC
			  FROM (SELECT A.OLD_CUSTOMER_ID,
			               '被保人' CUSTOMER_ROLE,
			               A.CUSTOMER_NAME,
			               A.CUSTOMER_CERT_TYPE,
			               A.CUSTOMER_CERTI_CODE,
			               A.CUSTOMER_GENDER,
			               A.CUSTOMER_BIRTHDAY,
			               H.POLICY_CODE
			          FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="csDetailEA_Page_Insur" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		  SELECT DISTINCT A.BUSI_PROD_CODE BUSI_PROD_CODE, /*险种代码*/
		                 B.PRODUCT_NAME_SYS PRODUCT_NAME_SYS, /*险种名称*/
		                 CASE
		                   WHEN A.MASTER_BUSI_ITEM_ID IS NULL THEN
		                    '是'
		                   ELSE
		                    '否'
		                 END IS_MASTER, /*是否主险*/
		                 E.CUSTOMER_NAME CUSTOMER_NAME, /*被保人姓名*/
		                 C.AMOUNT AMOUNT, /*基本保额*/
		                 C.STD_PREM_AF STD_PREM_AF, /*标准保费*/
		                 CASE
		                   WHEN F.EXTRA_TYPE = '1' THEN
		                    F.EXTRA_PREM
		                   ELSE
		                    0
		                 END JK_ADD, /*健康加费*/
		                 CASE
		                   WHEN F.EXTRA_TYPE = '0' THEN
		                    F.EXTRA_PREM
		                   ELSE
		                    0
		                 END ZY_ADD /*职业加费*/
		   FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD A
       	   LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT B
			ON A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
           LEFT JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT C
         	ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
       	   LEFT JOIN DEV_PAS.T_CS_INSURED_LIST D
         	ON A.Policy_Id = D.Policy_Id
       	   LEFT JOIN DEV_PAS.T_CS_CUSTOMER E
        	ON D.CUSTOMER_ID = E.CUSTOMER_ID
       	   LEFT JOIN DEV_PAS.T_CS_EXTRA_PREM F
         	ON C.CHANGE_ID = F.CHANGE_ID
		  WHERE A.POLICY_CODE = #{accept_status}
		    AND A.CHANGE_ID =
		        (SELECT C.CHANGE_ID
		           FROM DEV_PAS.T_CS_POLICY_CHANGE C, DEV_PAS.T_CS_ACCEPT_CHANGE A
		          WHERE C.CHANGE_ID = A.CHANGE_ID
		            AND C.POLICY_CODE = #{accept_status}
		            AND A.ACCEPT_CODE = #{accept_code})
		]]>
	</select>
	<select id="csDetailEA_Page_Surrender" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT M.CHANGE_ID CHANGE_ID,
			                M.VALIDATE_DATE VALIDATE_DATE, /*保单生效日*/
			                A.ACKNOWLEDGE_DATE ACKNOWLEDGE_DATE, /*客户签收日期*/
			                E.PAY_DUE_DATE PAY_DUE_DATE, /*交费对应日（下期缴费日）*/
			                E.POLICY_YEAR POLICY_YEAR, /*保单年度*/
			                CASE
			                  WHEN T.HESITATE_FLAG = '1' THEN
			                   '犹豫期内'
			                  ELSE
			                   '犹豫期外'
			                END HESITATE_FLAG_CH, /*犹豫期 (1,犹豫期内；2,犹豫期外)*/
			                T.SURRENDER_CAUSE 	, /*解约原因主键*/
			                TC.SURRENDER_CAUSE_NAME SURRENDER_CAUSE_NAME,
			                T.AGENT_HOLDER_RELATION AGENT_HOLDER_RELATION, /*投保人与业务员关系*/
			                L.RELATION_NAME RELATION_NAME,
			                T.CAUSE_REMARK CAUSE_REMARK /*公司解约原因备注*/
			  FROM DEV_PAS.T_CS_CONTRACT_MASTER M /*【保单主表*/
			  LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT A
			    ON M.POLICY_ID = A.POLICY_ID /*【保单回执表*/
			  LEFT JOIN DEV_PAS.T_CS_CONTRACT_EXTEND E
			    ON M.POLICY_ID = E.POLICY_ID /*【险种下期缴费计划表*/
			  LEFT JOIN DEV_PAS.T_SURRENDER T
			    ON M.CHANGE_ID = T.CHANGE_ID /*【退保记录表*/
			  LEFT JOIN DEV_PAS.T_SURRENDER_CAUSE TC
	           ON T.SURRENDER_CAUSE=TC.SURRENDER_CAUSE  
	          LEFT JOIN DEV_PAS.T_LA_PH_RELA L   
	           ON L.RELATION_CODE = T.AGENT_HOLDER_RELATION   
			 WHERE M.OLD_NEW = '0'
			   AND M.POLICY_CODE = #{accept_status}
			   AND M.CHANGE_ID =
			       (SELECT C.CHANGE_ID
			          FROM DEV_PAS.T_CS_POLICY_CHANGE C, DEV_PAS.T_CS_ACCEPT_CHANGE A
			         WHERE C.CHANGE_ID = A.CHANGE_ID
		               AND C.POLICY_CODE = #{accept_status}
		           	   AND A.ACCEPT_CODE = #{accept_code})
		]]>
	</select>
	<select id="csDetailEA_Page_Returns" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			        SELECT CBP.BUSI_PRD_ID,
                CBP.BUSI_PROD_CODE, /*险种代码*/
                BP.PRODUCT_NAME_SYS, /*险种名称*/
                (T.TYPE_NAME) TNAME, /*费用类型*/
                (W.TYPE_NAME) WNAME, /*费用名称*/
                TS.STAND_FEE_AMOUNT, /*调整前解约退费_退费合计金额*/
                TS.ADJUST_FEE_AMOUNT /*调整后解约退费*/
           FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
           LEFT JOIN　DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP
             ON AC.CHANGE_ID = CBP.CHANGE_ID
           LEFT JOIN (SELECT *
                        FROM DEV_PAS.T_SURRENDER
                       WHERE ACCEPT_CODE = #{accept_code}) TS /*退保记录表*/
             ON CBP.CHANGE_ID = TS.CHANGE_ID
            AND CBP.BUSI_ITEM_ID = TS.BUSI_ITEM_ID
           LEFT JOIN DEV_CAP.V_PREM_ARAP PA
             ON PA.POLICY_CODE = CBP.POLICY_CODE
            AND CBP.BUSI_PROD_CODE = PA.BUSI_PROD_CODE
            AND TS.ACCEPT_CODE = PA.BUSINESS_CODE
           LEFT JOIN DEV_PAS.T_FEE_TYPE T
             ON PA.FEE_TYPE = T.CODE
           LEFT JOIN DEV_PAS.T_WITHDRAW_TYPE W
             ON PA.WITHDRAW_TYPE = W.TYPE_CODE
           LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON BP.PRODUCT_CODE_SYS=CBP.BUSI_PROD_CODE
          WHERE AC.ACCEPT_CODE = #{accept_code}
            AND CBP.OLD_NEW = '0'
		]]>
	</select>
	<select id="csDetailEA_Page_polRets" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT X.FEE_AMOUNT 
		  FROM DEV_PAS.T_CS_POLICY_CHANGE X
		 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
	<select id="csDetailEA_Page_poMakes" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			 SELECT CBP.BUSI_PRD_ID,
                CBP.BUSI_PROD_CODE, /*险种代码*/
                BP.PRODUCT_NAME_SYS, /*险种名称*/
                SUM(TS.STAND_FEE_AMOUNT) STAND_FEE_AMOUNT, /*调整前解约退费_退费合计金额*/
                SUM (TS.ADJUST_FEE_AMOUNT) ADJUST_FEE_AMOUNT/*调整后解约退费*/
           FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
           LEFT JOIN　DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP
             ON AC.CHANGE_ID = CBP.CHANGE_ID
           LEFT JOIN (SELECT *
                        FROM DEV_PAS.T_SURRENDER
                       WHERE ACCEPT_CODE = #{accept_code}) TS /*退保记录表*/
             ON CBP.CHANGE_ID = TS.CHANGE_ID
            AND CBP.BUSI_ITEM_ID = TS.BUSI_ITEM_ID
            LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON BP.PRODUCT_CODE_SYS=CBP.BUSI_PROD_CODE
          WHERE AC.ACCEPT_CODE = #{accept_code}
            AND CBP.OLD_NEW = '0'
            GROUP BY CBP.BUSI_PRD_ID,CBP.BUSI_PROD_CODE,BP.PRODUCT_NAME_SYS
         ]]>
	</select>
</mapper>