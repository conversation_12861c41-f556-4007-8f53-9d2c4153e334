<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 保全查询	保费自垫清偿 -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="csDetailTR_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT A.*,
			       T.TYPE,/*证件类型*/
			       G.GENDER_DESC/*性别*/
			  FROM (SELECT A.OLD_CUSTOMER_ID,/*客户号码*/
			               '投保人' CUSTOMER_ROLE,/*角色*/
			               A.CUSTOMER_NAME,/*姓名*/
			               A.CUSTOMER_CERT_TYPE,/*证件类型代码*/
			               A.CUSTOMER_CERTI_CODE,/*证件号码*/
			               A.CUSTOMER_GENDER,/*性别代码*/
			               A.CUSTOMER_BIRTHDAY,/*出生日期*/
			               H.POLICY_CODE/*代码编号*/
			          FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
			UNION 
			 SELECT A.*,
			       T.TYPE,
			       G.GENDER_DESC
			  FROM (SELECT A.OLD_CUSTOMER_ID,
			               '被保人' CUSTOMER_ROLE,
			               A.CUSTOMER_NAME,
			               A.CUSTOMER_CERT_TYPE,
			               A.CUSTOMER_CERTI_CODE,
			               A.CUSTOMER_GENDER,
			               A.CUSTOMER_BIRTHDAY,
			               H.POLICY_CODE
			          FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="csDetailTR_Page_insur" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		 select P.BUSI_PROD_CODE BUSI_PROD_CODE, /*险种代码*/
		        SUM(P1.UNIT) UNIT, /*份数*/
		        SUM(P1.AMOUNT) AMOUNT, /*基本保额*/
		        SUM(P1.STD_PREM_AF) STD_PREM_AF, /*保费标准*/
		        (select distinct e.pay_due_date
		           from dev_pas.T_CS_CONTRACT_EXTEND e
		          where e.old_new = '1'
	              	and e.change_id =		 /*下期交费日期*/
	                (SELECT ACC.CHANGE_ID
	                   FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,
	                        DEV_PAS.T_CS_ACCEPT_CHANGE ACC
	                  WHERE CHA.CHANGE_ID = ACC.CHANGE_ID
	                    AND CHA.POLICY_CODE = #{accept_status}
	                    AND ACC.ACCEPT_CODE = #{accept_code})) pay_due_date,
		        (select p.product_name_sys
		           from dev_pas.t_business_product p
		          where p.business_prd_id = #{review_id}) product_name_sys /*险种名称*/
		   from dev_pas.t_contract_busi_prod p, dev_pas.T_CONTRACT_PRODUCT P1 /*p险种表-p1险种责任人表*/
		  where P.POLICY_CODE = P1.POLICY_CODE
		    AND P.BUSI_ITEM_ID = P1.BUSI_ITEM_ID
		    AND P.POLICY_CODE = #{accept_status} /*保单号*/
		    AND P.BUSI_ITEM_ID = #{related_id} /*代接受 险种id 参数*/
		  GROUP BY P.BUSI_PROD_CODE
		]]>
	</select>
	<!-- 查找主险 险种ID 业务产品ID 通过保单号 -->
	<select id="csfind_insur_id" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT DISTINCT P.BUSI_ITEM_ID, P.BUSI_PRD_ID 
		  FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD P
		 WHERE P.POLICY_CODE = #{accept_status}
		   AND P.MASTER_BUSI_ITEM_ID IS NULL
		]]>
	</select>
	<!--  保费自垫清偿信息-->
	<select id="csDetailTR_Page_bao" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT T.CAPITALIZED_DATE, /*自垫时间*/
		       T.BALANCE_DATE, /*清偿时间*/
		       T.CAPITAL_BALANCE, /*自垫保费*/
		       T.INTEREST_BALANCE, /*自垫利息*/
		       (T.CAPITAL_BALANCE+T.INTEREST_BALANCE) BENXI /*垫交本息*/
		  FROM DEV_PAS.T_CS_POLICY_ACCOUNT_STREAM T
		 WHERE T.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status})
		 AND  T.OLD_NEW='1'
		 ORDER BY T.CAPITALIZED_DATE
 
		]]>
	</select>
</mapper>
	<!-- 保费自垫清偿信息(上面sql如果不对，请参考以下sql)
	SELECT t.Capitalized_Date 自垫时间, 自垫时间
       t.Capital_Balance  自垫本金,
       t.Interest_Balance 自垫利息,
       t.Interest_Sum     清偿利息,
       t.Interest_Capital 清偿本息和,
       t.Balance_Date     清偿时间 
  FROM dev_pas.t_Cs_Policy_Account_Stream t
 WHERE t.old_new = '1'
   and t.Change_Id =
       (select ac.change_id
          from dev_pas.t_cs_accept_change ac, dev_pas.t_cs_policy_change pc
         where ac.change_id = pc.change_id
           and ac.accept_code = '****************'
           and pc.policy_code = '*****************')
 ORDER BY t.Capitalized_Date;
	 -->