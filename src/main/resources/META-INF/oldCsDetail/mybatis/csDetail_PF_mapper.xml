<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
<!-- 综合查询_保单解挂_解除挂失 mass_wb-->
	<select id="QRY_find_PF_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
(SELECT OLD_CUSTOMER_ID
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID) OLD_CUSTOMER_ID, /*客户ID*//*被保人号码*/ 
(SELECT CUSTOMER_NAME
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID) CUSTOMER_NAME, /*姓名*/
 B.BUSI_PROD_CODE, /*险种代码*/
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
 P.STD_PREM_AF,        /*保费标准*/
 P.AMOUNT,/*基本保额*/
 B.VALIDATE_DATE /*生效日期*/
 FROM DEV_PAS.T_CONTRACT_PRODUCT P, DEV_PAS.T_CONTRACT_BUSI_PROD B,
 DEV_PAS.T_INSURED_LIST L
 WHERE P.POLICY_CODE = B.POLICY_CODE
   AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
   AND L.POLICY_CODE = P.POLICY_CODE
   AND P.POLICY_CODE = #{policy_code} 
 GROUP BY L.CUSTOMER_ID,L.UPDATE_TIME,B.BUSI_PROD_CODE,B.VALIDATE_DATE,P.STD_PREM_AF,P.AMOUNT
		]]>
	</select>
</mapper>