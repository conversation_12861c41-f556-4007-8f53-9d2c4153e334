<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 综合查询 保障计划类别约定变更 -->
	<!-- 客户基本信息 -->
	<select id="QRY_XX_find_Customer_Info" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT A.*, T.TYPE, /*证件类型*/
        G.GENDER_DESC /*性别*/
          FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
                       '投保人' CUSTOMER_ROLE, /*角色*/
                       A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY /*出生日期*/
                  FROM DEV_PAS.T_CS_POLICY_HOLDER H, /**保单投保人列表*/
                  DEV_PAS.T_CS_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
                   AND H.POLICY_CODE = #{accept_status}) A,
                    DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G/**性别代码表*/
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
           AND A.CUSTOMER_GENDER = G.GENDER_CODE/**客户性别*/
        UNION
        SELECT A.*, T.TYPE, G.GENDER_DESC
          FROM (SELECT A.OLD_CUSTOMER_ID,
                       '被保人' CUSTOMER_ROLE,
                       A.CUSTOMER_NAME,
                       A.CUSTOMER_CERT_TYPE,
                       A.CUSTOMER_CERTI_CODE,
                       A.CUSTOMER_GENDER,
                       A.CUSTOMER_BIRTHDAY
                  FROM DEV_PAS.T_CS_INSURED_LIST H,/**被保人表*/
                   DEV_PAS.T_CS_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                   AND H.POLICY_CODE = #{accept_status}) A,
               DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE
           AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<!-- 保单险种信息 -->
	<select id="QRY_XX_find_Types_Info" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT T.BUSI_PROD_CODE, /*险种代码*/
               B.PRODUCT_NAME_SYS, /*险种名称*/
        (SELECT DISTINCT  A.CUSTOMER_NAME
               FROM DEV_PAS.T_INSURED_LIST l, DEV_PAS.T_CUSTOMER A
              WHERE l.CUSTOMER_ID = A.CUSTOMER_ID
                AND l.Policy_Id = t.policy_id ) B_CUSTOMER_NAME,  /*被保人姓名*/       
       (SELECT DISTINCT A.CUSTOMER_NAME
               FROM DEV_PAS.T_POLICY_HOLDER H,
                    DEV_PAS.T_CUSTOMER      A
              WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                AND H.Policy_Id = t.policy_id ) T_CUSTOMER_NAME, /*投保人姓名*/     
              C.STD_PREM_AF, /*保费*/
              C.UNIT, /*投保份数*/
              C.AMOUNT, /*投保金额*/
              T.VALIDATE_DATE, /*生效日期*/
              CE.PAY_DUE_DATE /*下期应缴费日*/
       FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD T /*险种表*/
       LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT B /*业务产品*/
             ON T.BUSI_PRD_ID = B.BUSINESS_PRD_ID
       LEFT JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT C /*保单险种责任组表*/
             ON T.POLICY_ID = C.POLICY_ID AND C.BUSI_ITEM_ID = T.BUSI_ITEM_ID AND C.OLD_NEW = '1' AND T.CHANGE_ID = C.CHANGE_ID
       LEFT JOIN DEV_PAS.T_CS_CONTRACT_EXTEND CE
             ON CE.POLICY_CODE = T.POLICY_CODE AND CE.BUSI_ITEM_ID = T.BUSI_ITEM_ID AND CE.OLD_NEW = '1' AND T.CHANGE_ID = CE.CHANGE_ID 
       WHERE T.CHANGE_ID =
       (SELECT ACC.CHANGE_ID /*受理号*/
          FROM DEV_PAS.T_CS_POLICY_CHANGE CHA, /*保单变更管理表*/
               DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
         WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{accept_status}
       AND ACC.ACCEPT_CODE = #{accept_code}) 
       AND T.OLD_NEW ='1'
         ]]>
	</select>
	<!-- 保障计划 -->
	<select id="QRY_XX_find_Plan_Info" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
        SELECT PP.OLD_PLAN_ID,			/*原保障计划编码*/
        PP.NEW_PLAN_ID,					/*现保障计划编码*/
        OSP.PLAN_NAME OLD_PLAN_NAME,	/*原保障计划名字*/
        NSP.PLAN_NAME NEW_PLAN_NAME,	/*现保障计划名字*/
        PP.OLD_STAND_AMOUNT,			/*原基本保额*/
        PP.NEW_STAND_AMOUNT,			/*新基本保额*/
        PP.NEW_PREM 					/*新保费*/
        FROM DEV_PAS.T_CS_PRECONT_PRODUCT PP 
        LEFT JOIN  DEV_PAS.T_SAFEGUARD_PLAN OSP 
        ON PP.OLD_PLAN_ID=OSP.PLAN_ID 
        LEFT JOIN DEV_PAS.T_SAFEGUARD_PLAN NSP 
        ON PP.NEW_PLAN_ID=NSP.PLAN_ID
       WHERE PP.CHANGE_ID=
       (SELECT 
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{accept_status}
       AND ACC.ACCEPT_CODE = #{accept_code})
        ]]>
	</select>
	<!-- 被保人健康告知信息 -->
	<select id="QRY_XX_find_Survey_Info" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
        SELECT B.SURVEY_VERSION,	/*告知版别*/
        B.SURVEY_CODE,				/*告知编码*/
        B.QUESTION_CONTENT,			/*告知内容*/
        A.SURVEY_MODULE_RESULT		/*填写内容*/
		FROM 
		DEV_PAS.T_CS_QUESTIONAIRE_CUSTOMER A 
		INNER JOIN DEV_PAS.T_QUESTIONAIRE_INFO B 
		ON A.SURVEY_QUESTION_ID=B.SURVEY_QUESTION_ID
		WHERE A.POLICY_CODE=
		(SELECT 
           CHA.POLICY_CODE
        FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
        WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
        AND CHA.POLICY_CODE = #{accept_status}
        AND ACC.ACCEPT_CODE = #{accept_code})
        ]]>
	</select>
	<!-- 查询补退费合计 -->
	<select id="QRY_XX_find_Charge_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT X.FEE_AMOUNT /**补退费合计*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE X
     WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
      FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
     WHERE AC.CHANGE_ID = PC.CHANGE_ID
      AND AC.ACCEPT_CODE = #{accept_code}
       AND PC.POLICY_CODE = #{accept_status})
		]]>
	</select>
</mapper>