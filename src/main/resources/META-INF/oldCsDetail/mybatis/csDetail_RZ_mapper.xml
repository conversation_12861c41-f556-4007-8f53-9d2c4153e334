<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="kehuRZ_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
               SELECT A.CUSTOMER_NAME, /*姓名*/
                G.GENDER_DESC, /*性别*/ 
                A.CUSTOMER_BIRTHDAY, /*出生日期*/
                T.TYPE, /*证件类型*/
                A.CUSTOMER_CERTI_CODE /*证件号码*/
           FROM DEV_PAS.T_POLICY_HOLDER H/*投保人表*/
           LEFT JOIN DEV_PAS.T_CUSTOMER A 
           ON H.CUSTOMER_ID = A.CUSTOMER_ID
           LEFT JOIN DEV_PAS.T_CERTI_TYPE T
             ON A.CUSTOMER_CERT_TYPE = T.CODE
           LEFT JOIN DEV_PAS.T_GENDER G
             ON A.CUSTOMER_GENDER = G.GENDER_CODE
          WHERE H.POLICY_CODE = #{accept_status}
         ]]>
	</select>		
</mapper>