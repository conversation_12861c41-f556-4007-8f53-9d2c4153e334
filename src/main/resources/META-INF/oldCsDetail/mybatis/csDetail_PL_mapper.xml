<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="kehuPL_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
        	 SELECT A.*, T.TYPE, /*证件类型*/
        G.GENDER_DESC /*性别*/
          FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
                       '投保人' CUSTOMER_ROLE, /*角色*/
                       A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY /*出生日期*/
                  FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
                  DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
                   AND H.POLICY_CODE = #{accept_status}) A,
                    DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G/**性别代码表*/
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
           AND A.CUSTOMER_GENDER = G.GENDER_CODE/**客户性别*/
        UNION
        SELECT A.*, T.TYPE, G.GENDER_DESC
          FROM (SELECT A.OLD_CUSTOMER_ID,
                       '被保人' CUSTOMER_ROLE,
                       A.CUSTOMER_NAME,
                       A.CUSTOMER_CERT_TYPE,
                       A.CUSTOMER_CERTI_CODE,
                       A.CUSTOMER_GENDER,
                       A.CUSTOMER_BIRTHDAY
                  FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                   DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                   AND H.POLICY_CODE =#{accept_status}) A,
               DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE
           AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="baodanPL_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT TC.OLD_CUSTOMER_ID,/**被保险人号码*/
			    TC.CUSTOMER_NAME,/**姓名*/
			    CBP.BUSI_PROD_CODE,/**险种代码*/
			    BP.PRODUCT_NAME_SYS,/**险种名称*/
			    CP.STD_PREM_AF,/**保费标准*/
			    CP.AMOUNT,/**基本保额*/
			    CBP.VALIDATE_DATE/**生效日期*/
			    FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
			    DEV_PAS.T_BUSINESS_PRODUCT BP,
			    DEV_PAS.T_CS_CONTRACT_PRODUCT CP,
			    DEV_PAS.T_CUSTOMER TC,
			    DEV_PAS.T_INSURED_LIST IL    
			    WHERE BP.PRODUCT_CODE_SYS=CBP.BUSI_PROD_CODE
			    AND CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
			    AND TC.CUSTOMER_ID = IL.CUSTOMER_ID 
			    AND IL.POLICY_ID=CP.POLICY_ID
			    AND CP.POLICY_CODE=#{accept_status}
         ]]>
	</select>
	<select id="guashiPL_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			 SELECT PL.LOST_TYPE,/**挂失类型编码*/
			 LT.LOSE_TYPE_NAME,/**挂失类型*/
			  PL.LOST_CAUSE, /**挂失原因编码*/
			 LC.LOSE_CAUSE_NAME,/**挂失原因*/
			 PL.LOST_DATE /**挂失日期*/
			    FROM DEV_PAS.T_POLICY_LOSE PL,/**保单挂失表*/
			    DEV_PAS.T_LOSE_CAUSE LC,/**保单挂失原因表*/
			    DEV_PAS.T_LOSE_TYPE LT/**保单挂失类型表*/
			     WHERE PL.LOST_TYPE=LT.LOSE_TYPE
			     AND PL.LOST_CAUSE=LC.LOSE_CAUSE
			     AND PL.CHANGE_ID=(SELECT AC.CHANGE_ID
			          FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
			         WHERE AC.CHANGE_ID = PC.CHANGE_ID
							   AND AC.ACCEPT_CODE =#{accept_code}
							   AND PC.POLICY_CODE =  #{accept_status})
         ]]>
	</select>
</mapper>