<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
    <!-- 综合查询_领取日期变更 -->
    <!-- 保单险种信息 -->
	<select id="QRY_LC_find_Types_Info" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[SELECT 
       T.BUSI_PROD_CODE,        /*险种代码*/
       B.PRODUCT_NAME_SYS,       /*险种名称*/
       T.VALIDATE_DATE,           /*生效日期*/
       C.STD_PREM_AF             /*保费*/
       FROM  
           DEV_PAS.T_CONTRACT_BUSI_PROD T /*险种表*/
 LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT B  /*业务产品*/
      ON   T.BUSI_PRD_ID = B.BUSINESS_PRD_ID
 LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT C   /*保单险种责任组表*/
      ON T.POLICY_CODE = C.POLICY_CODE
      WHERE C.POLICY_CODE = #{policy_code}
         ]]>
	</select>
     <!-- 领取项目信息 -->
    <select id="QRY_CL_find_Get_Info" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT PRODUCT_CODE /*责任编码*/
  FROM DEV_PAS.T_PAY_PLAN
  WHERE POLICY_ID = 
   ( SELECT 
           CHA.POLICY_ID /*保单ID*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
        ]]>
    </select> 
</mapper>