<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="tkehuAG_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
  		     SELECT A.CUSTOMER_NAME, /*姓名*/
                   A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                   A.CUSTOMER_CERTI_CODE, /*证件号码*/
                   T.TYPE/*证件类型*/
              FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
              DEV_PAS.T_CUSTOMER A,/**客户表*/
              DEV_PAS.T_CERTI_TYPE T
             WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
             AND A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
             AND H.POLICY_CODE = #{accept_status}
         ]]>
	</select>
	<select id="bkehuAG_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
  		     SELECT A.CUSTOMER_NAME, /*姓名*/
                  A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                  A.CUSTOMER_CERTI_CODE, /*证件号码*/
                  T.TYPE/*证件类型*/
             FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
             DEV_PAS.T_CUSTOMER A,/**客户表*/
             DEV_PAS.T_CERTI_TYPE T
            WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
            AND A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
            AND H.POLICY_CODE = #{accept_status}
         ]]>
	</select>
	<select id="nianAG_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
        
        SELECT Distinct 
        T.Change_Id,
        '' AS SURVIVAL_W_MODE, /*生存金支取形式, 即领取形式编码*/
        '' As MODE_NAME,/*领取形式名称*/
        B.BANK_CODE, /*领取银行编码*/
        B.BANK_ACCOUNT, /*领取银行账户*/
        B.ACCO_NAME, /*领取银行户名*/
        BA.BANK_NAME /*银行名称*/
      FROM
       DEV_PAS.T_CS_POLICY_CHANGE T 
      LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC  ON AC.CHANGE_ID=T.Change_Id/*险种生存给付计划表*/  
     LEFT JOIN DEV_PAS.T_CS_BANK_ACCOUNT B /*银行账户信息*/ 
     INNER JOIN DEV_PAS.T_BANK BA ON B.BANK_CODE=BA.BANK_CODE
      ON  B.CHANGE_ID=T.CHANGE_ID
      WHERE  AC.ACCEPT_CODE= #{accept_code}
      AND  T.POLICY_CODE=#{accept_status}
       AND B.OLD_NEW=1    
         ]]>
	</select>
</mapper>