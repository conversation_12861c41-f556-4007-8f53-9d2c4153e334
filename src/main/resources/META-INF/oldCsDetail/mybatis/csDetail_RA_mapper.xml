<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="basicRA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
          SELECT A.*, T.TYPE, /*证件类型*/
        G.GENDER_DESC /*性别*/
          FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
                       '投保人' CUSTOMER_ROLE, /*角色*/
                       A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY, /*出生日期*/
                       H.POLICY_CODE /*代码编号*/
                  FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
                  DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
                   AND H.POLICY_CODE = #{accept_status}) A,
                    DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G/**性别代码表*/
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
           AND A.CUSTOMER_GENDER = G.GENDER_CODE/**客户性别*/
        UNION
        SELECT A.*, T.TYPE, G.GENDER_DESC
          FROM (SELECT A.OLD_CUSTOMER_ID,
                       '被保人' CUSTOMER_ROLE,
                       A.CUSTOMER_NAME,
                       A.CUSTOMER_CERT_TYPE,
                       A.CUSTOMER_CERTI_CODE,
                       A.CUSTOMER_GENDER,
                       A.CUSTOMER_BIRTHDAY,
                       H.POLICY_CODE
                  FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                   DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                   AND H.POLICY_CODE = #{accept_status}) A,
               DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE
           AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="xianRA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
                SELECT B.BUSI_PROD_CODE, /*险种代码*/
	             S.PRODUCT_NAME_SYS, /*险种名称*/
	             D.CUSTOMER_NAME,
	             P.STD_PREM_AF, /*保费标准*/
	             P.AMOUNT, /*基本保额*/
	             B.VALIDATE_DATE, /*生效日期*/
	             P.UNIT, /*客户投保份数*/
	             NVL((SELECT A.EXTRA_PARA
	                   FROM DEV_PAS.T_EXTRA_PREM A /*险种责任组加费表*/
	                  WHERE A.POLICY_CODE = #{accept_status}
	                    AND A.EXTRA_TYPE = '1'),
	                 0) AS EXTRA_PARA1, /*健康加费*/
	             NVL((SELECT A.EXTRA_PARA
	                   FROM DEV_PAS.T_EXTRA_PREM A /*险种责任组加费表*/
	                  WHERE A.POLICY_CODE = #{accept_status}
	                    AND A.EXTRA_TYPE = '2'),
	                 0) AS EXTRA_PARA2 /*职业加费*/
	        FROM DEV_PAS.T_CONTRACT_PRODUCT   P, /*保单险种责任组表*/
	             DEV_PAS.T_CONTRACT_BUSI_PROD B, /*险种表*/
	             DEV_PAS.T_INSURED_LIST       L, /*保单被保人列表*/
	             DEV_PAS.T_CUSTOMER           D, /*客户表*/
	             DEV_PAS.T_BUSINESS_PRODUCT   S /*业务产品*/
	       WHERE P.POLICY_CODE = B.POLICY_CODE
	         AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
	         AND L.POLICY_CODE = P.POLICY_CODE
	         AND L.CUSTOMER_ID = D.CUSTOMER_ID
	         AND S.PRODUCT_CODE_SYS = B.BUSI_PROD_CODE
	         AND P.POLICY_CODE =#{accept_status}
         ]]>
	</select>
	<select id="jianRA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
          SELECT B.SURVEY_VERSION,/**告知版别*/
			B.SURVEY_CODE,/**告知编码*/
			B.QUESTION_CONTENT,/**告知内容*/
			A.SURVEY_MODULE_RESULT/**填写内容*/
			FROM DEV_PAS.V_QUESTIONAIRE_CUSTOMER_ALL A/**客户告知表*/
			 LEFT JOIN DEV_PAS.T_QUESTIONAIRE_INFO B /**告知信息表*/
			ON A.SURVEY_QUESTION_ID=B.SURVEY_QUESTION_ID/**告别信息ID*/
			 WHERE POLICY_CODE=#{accept_status}
			  GROUP BY B.SURVEY_VERSION,B.SURVEY_CODE,QUESTION_CONTENT,SURVEY_MODULE_RESULT
         ]]>
	</select>
</mapper>