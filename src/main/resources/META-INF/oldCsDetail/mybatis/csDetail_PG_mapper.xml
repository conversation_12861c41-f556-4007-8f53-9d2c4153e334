<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	
	<select id="kehutPG_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
       		SELECT A.*, T.TYPE, /*证件类型*/
        G.GENDER_DESC /*性别*/
          FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
                       '投保人' CUSTOMER_ROLE, /*角色*/
                       A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY /*出生日期*/
                  FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
                  DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
                   AND H.POLICY_CODE = #{accept_status}) A,
                    DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G/**性别代码表*/
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
           AND A.CUSTOMER_GENDER = G.GENDER_CODE/**客户性别*/
        UNION
        SELECT A.*, T.TYPE, G.GENDER_DESC
          FROM (SELECT A.OLD_CUSTOMER_ID,
                       '被保人' CUSTOMER_ROLE,
                       A.CUSTOMER_NAME,
                       A.CUSTOMER_CERT_TYPE,
                       A.CUSTOMER_CERTI_CODE,
                       A.CUSTOMER_GENDER,
                       A.CUSTOMER_BIRTHDAY
                  FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                   DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                   AND H.POLICY_CODE =#{accept_status}) A,
               DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE
           AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	  <select id="baodanPG_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
       		SELECT  CBP.BUSI_PROD_CODE,/**险种代码*/
        BP.PRODUCT_NAME_SYS,/**险种名称*/
            ( SELECT A.CUSTOMER_NAME /*姓名*/ 
            FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
            DEV_PAS.T_CUSTOMER A/**客户表*/ WHERE H.CUSTOMER_ID=A.CUSTOMER_ID AND H.POLICY_CODE=CBP.POLICY_CODE ) TNAME,
            ( SELECT A.CUSTOMER_NAME /*姓名*/ 
            FROM DEV_PAS.T_INSURED_LIST H, /**保单被保人列表*/
            DEV_PAS.T_CUSTOMER A/**客户表*/ WHERE H.CUSTOMER_ID=A.CUSTOMER_ID AND H.POLICY_CODE=CBP.POLICY_CODE ) BNAME,
         (SELECT SUM(CP.STD_PREM_AF) FROM DEV_PAS.T_CS_CONTRACT_PRODUCT CP WHERE CP.LIABILITY_STATE = '1' AND CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS STD_PREM_AF,/**保费标准*/
       NVL((SELECT A.EXTRA_PARA
           FROM DEV_PAS.T_EXTRA_PREM A /*险种责任组加费表*/
          WHERE A.POLICY_ID = CBP.POLICY_ID AND A.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
            AND A.EXTRA_TYPE = '1'),
         0) AS EXTRA_PARA1, /*健康加费*/
     NVL((SELECT A.EXTRA_PARA
           FROM DEV_PAS.T_EXTRA_PREM A /*险种责任组加费表*/
          WHERE A.POLICY_ID = CBP.POLICY_ID AND A.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
            AND A.EXTRA_TYPE = '2'),
         0) AS EXTRA_PARA2 /*职业加费*/
        FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
        DEV_PDS.T_BUSINESS_PRODUCT BP
        WHERE BP.PRODUCT_CODE_SYS=CBP.BUSI_PROD_CODE
            AND CBP.POLICY_CODE=#{accept_status}
         ]]>
	</select>

	<select id="zhangPG_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
	      SELECT A.ACCOUNT_CODE, /*账户代码*/
	             B.FUND_NAME, /*账户名称*/
	             A.TOTAL_PREM, /*累计交费*/
	             A.INTEREST_CAPITAL, /*账户金额*/
	             A.SUR_AMOUNT /*部分领取金额对应的保单价值*/
	        FROM DEV_PAS.T_CS_CONTRACT_INVEST A
	        LEFT JOIN DEV_PAS.T_FUND B
	          ON A.ACCOUNT_CODE = B.FUND_CODE
	       INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
	       			 ON A.POLICY_ID = PC.POLICY_ID
	                 AND A.CHANGE_ID = PC.CHANGE_ID
	       INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
	          ON A.CHANGE_ID = AC.CHANGE_ID
	       WHERE A.OLD_NEW = '1'
	         AND AC.ACCEPT_CODE = #{accept_code}
	         AND PC.POLICY_CODE = #{accept_status}

		]]>
	</select>

	<select id="butuiPG_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT X.FEE_AMOUNT 
		  FROM DEV_PAS.T_CS_POLICY_CHANGE X
		 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
	<select id="bjfyPG_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			 SELECT  DECODE(C.ARAP_FLAG, 2, C.FEE_AMOUNT * (-1), C.FEE_AMOUNT) FEE_AMOUNT, /**费用金额*/
		          T.TYPE_NAME,   /**费用类型*/
		          (W.TYPE_NAME) WNAME  /*费用名称*/
		     FROM DEV_PAS.V_PREM_ARAP_ALL C,
		     DEV_PAS.T_FEE_TYPE T,
		     DEV_PAS.T_WITHDRAW_TYPE W
		     WHERE T.CODE=C.FEE_TYPE
         AND C.WITHDRAW_TYPE=W.TYPE_CODE
		    AND C.POLICY_CODE=#{accept_status}
		     AND C.DERIV_TYPE = '004' /*保全*/
   			AND C.BUSINESS_CODE = #{accept_code}
         ]]>
	</select>

</mapper>