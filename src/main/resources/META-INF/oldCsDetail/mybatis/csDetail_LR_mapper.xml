<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="jiaofeiLR_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
            SELECT A.FEE_AMOUNT /*缴费金额*/
 		    FROM DEV_PAS.V_PREM_ARAP A
            WHERE A.WITHDRAW_TYPE = 'G004060000'/*记账业务类型*/
            AND A.FEE_TYPE = '0042500000'/*费用业务类型 32；实付退费 41；保费收入 参考费用类型列表*/
            AND A.DERIV_TYPE = '004'/*业务来源001；新契约 002；核保 003；续保 004；保全 005；理赔*/
            AND A.BUSINESS_CODE=#{accept_code}/*受理号（投保单号、保单号、保全受理号、赔案号等。如果支持代理人佣金，该字段还可以保存代理人ID等等。）*/
            AND A.POLICY_CODE=#{accept_status}/*保单号码*/
         ]]>
  
	</select>
	<select id="reissueLR_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
               SELECT T.REISSUE_CAUSE,/*补发原因编码*/
     		   A.REISSUE_CAUSE_NAME/*补发原因*/
               FROM  
        	   DEV_PAS.T_POLICY_REISSUE T 
        	   LEFT JOIN DEV_PAS.T_CS_CONTRACT_MASTER CS
      		   ON T.CHANGE_ID=CS.POLICY_CHG_ID/**保全变更ID*/
               AND T.POLICY_ID=CS.POLICY_ID/**保单ID*/ 
               LEFT JOIN DEV_PAS.T_POLICY_REISSUE_CAUSE A
               ON T.REISSUE_CAUSE=A.REISSUE_CAUSE
               WHERE T.ACCEPT_CODE=#{accept_code}/**保全受理号*/
         ]]>
  
	</select>
</mapper>