<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="kehutIO_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
         SELECT A.*, T.TYPE /*证件类型*/
          FROM (SELECT '投保人' CUSTOMER_ROLE, /*角色*/
                       A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE /*证件号码*/
                  FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
                  DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
                   AND H.POLICY_CODE = #{accept_status}) A,
                    DEV_PAS.T_CERTI_TYPE T
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
         ]]>
	</select>
	<select id="kehubIO_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
         SELECT A.*, T.TYPE
          FROM (SELECT '被保人' CUSTOMER_ROLE,
                       A.CUSTOMER_NAME,
                       A.CUSTOMER_CERT_TYPE,
                       A.CUSTOMER_CERTI_CODE
                  FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                   DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                   AND H.POLICY_CODE = #{accept_status}) A,
               DEV_PAS.T_CERTI_TYPE T
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE
         ]]>
	</select>
	<select id="zhiyeIO_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
       SELECT T.OLD_JOB_CODE, /**职业代码*/
           C.JOB_NAME,   /**职业名称*/
       T.OLD_JOB_UNDERWRITE,/**职业类别*/
       J.JOB_UW_LEVEL_NAME/**职业类别名称*/
		  FROM DEV_PAS.T_JOBKIND_CHANGE T,/**职业类别变更信息表*/
		   DEV_PAS.T_JOB_CODE C,/**职业代码表*/
		  DEV_PAS.T_JOB_UNDERWRITE J /**职业代码表*/
		 WHERE C.JOB_CODE=T.OLD_JOB_CODE
		  AND J.JOB_UW_LEVEL_CODE=T.OLD_JOB_UNDERWRITE
		  AND T.ACCEPT_CODE=#{accept_code}/**保单受理号*/
         ]]>
	</select>
	<select id="baodanIO_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
      SELECT
	 B.BUSI_PROD_CODE, /*险种代码*/
	 (SELECT PRODUCT_NAME_SYS
	          FROM DEV_PAS.T_BUSINESS_PRODUCT
	         WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/  
	         P.AMOUNT,/**基本保额*/
	     P.EXTRA_PREM_AF,/**职业加费*/
	           P.STD_PREM_AF/**保费标准*/    
	 FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P,/**险种责任组表*/
	      DEV_PAS.T_CONTRACT_BUSI_PROD B /**险种表*/
	 WHERE P.POLICY_CODE = B.POLICY_CODE
	   AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
	   AND P.POLICY_CODE = #{accept_status}
	   AND OLD_NEW = '0' 
	   GROUP BY B.BUSI_PROD_CODE,P.AMOUNT,P.EXTRA_PREM_AF,P.STD_PREM_AF
         ]]>
	</select>
	<select id="zhiyebgIO_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
           SELECT T.NEW_JOB_CODE, /**职业代码*/
           C.JOB_NAME,   /**职业名称*/
	       T.NEW_JOB_UNDERWRITE,/**职业类别*/
	       J.JOB_UW_LEVEL_NAME/**职业类别名称*/
		   FROM DEV_PAS.T_JOBKIND_CHANGE T,/**职业类别变更信息表*/
		   DEV_PAS.T_JOB_CODE C,/**职业代码表*/
		   DEV_PAS.T_JOB_UNDERWRITE J /**职业代码表*/
		   WHERE C.JOB_CODE=T.NEW_JOB_CODE
		   AND J.JOB_UW_LEVEL_CODE=T.NEW_JOB_UNDERWRITE
		   AND T.ACCEPT_CODE=#{accept_code}/**保单号*/
         ]]>
	</select>
	<select id="baodanbgIO_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
        SELECT
	 	B.BUSI_PROD_CODE, /*险种代码*/
	 	(SELECT PRODUCT_NAME_SYS
	     FROM DEV_PAS.T_BUSINESS_PRODUCT
	     WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/  
	     P.AMOUNT,/**基本保额*/
	     P.EXTRA_PREM_AF,/**职业加费*/
	     P.STD_PREM_AF/**保费标准*/    
	     FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P,/**险种责任组表*/
	     DEV_PAS.T_CONTRACT_BUSI_PROD B /**险种表*/ WHERE P.POLICY_CODE = B.POLICY_CODE
	   AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
	   AND P.POLICY_CODE = #{accept_status}
	   AND OLD_NEW = '1' 
	   GROUP BY B.BUSI_PROD_CODE,P.AMOUNT,P.EXTRA_PREM_AF,P.STD_PREM_AF
         ]]>
	</select>
</mapper>