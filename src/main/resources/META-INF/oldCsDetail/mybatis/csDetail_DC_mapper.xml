<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询万能险基本保险金额约定变更_20180309mass_wb-->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 保单险种信息 -->
	<select id="QRY_DC_FindAppointInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.AMOUNT, /*基本保额*/
       T.STD_PREM_AF, /*保费标准*/
       (SELECT B.CUSTOMER_NAME /*被保人姓名*/
  FROM DEV_PAS.T_INSURED_LIST A
  LEFT JOIN DEV_PAS.T_CUSTOMER B 
    ON A.CUSTOMER_ID = B.CUSTOMER_ID 
 WHERE A.POLICY_CODE = #{policy_code}) CUSTOMER_NAME,
 C.BUSI_PROD_CODE, /*险种代码*/
 C.BUSI_ITEM_ID,
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = C.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
       NVL(CASE
             WHEN B.EXTRA_TYPE = '1' THEN
              B.EXTRA_PARA
           END,
           0) AS MONEY_1, /*健康加费*/
       NVL(CASE
             WHEN B.EXTRA_TYPE = '2' THEN
              B.EXTRA_PARA
           END,
           0) AS MONEY_2 /*职业加费*/
  FROM DEV_PAS.T_CONTRACT_PRODUCT T /*保单险种责任组表*/
  LEFT JOIN DEV_PAS.T_EXTRA_PREM B
    ON T.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C
   ON T.POLICY_CODE = C.POLICY_CODE
  AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID
 WHERE T.POLICY_CODE = #{policy_code}
		]]>
	</select>
    <!-- 账户信息 -->
    <select id="QRY_DC_AccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT T.ACCOUNT_CODE,    /*投资基金账户代码*/
       T.TOTAL_PREM,       /*已缴保费*/
       T.INTEREST_CAPITAL, /*本息合计*/
       T.SETTLE_DUE_DATE,  /*结算应当日*/
       (SELECT A.INVEST_ACCOUNT_NAME FROM DEV_PAS.T_INVEST_ACCOUNT_INFO A WHERE A.INVEST_ACCOUNT_CODE = T.ACCOUNT_CODE) AS IN_NAME /*账户名称*/
  FROM DEV_PAS.T_CONTRACT_INVEST T /*保单投资连结表*/
 WHERE T.POLICY_ID = 
 (SELECT
         CHA.POLICY_ID /*保单ID*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA, /*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC  /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
        ]]>
    </select>
    <!-- 账户信息：基本保额约定、调整前基本保额 -->
    <select id="QRY_DC_AppointInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT PP.OLD_STAND_AMOUNT,   /*原基本保额*/
       PP.NEW_STAND_AMOUNT   /*新基本保额*/
 FROM DEV_PAS.T_CS_PRECONT_PRODUCT PP /*预约险种信息变更表*/
   WHERE PP.CHANGE_ID = 
   (SELECT 
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
        ]]>
    </select>
    <!-- 约定年龄 -->
    <select id="QRY_DC_Appoint_AgeInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT FLOOR(TO_NUMBER(PRECONT_TIME-CUSTOMER_BIRTHDAY)/365) AGE FROM (
      SELECT PP.PRECONT_TIME,
            (SELECT
                 B.CUSTOMER_BIRTHDAY
                FROM DEV_PAS.T_INSURED_LIST A
                LEFT JOIN DEV_PAS.T_CUSTOMER B
                  ON A.CUSTOMER_ID = B.CUSTOMER_ID 
               WHERE A.POLICY_ID=PP.POLICY_ID)  AS CUSTOMER_BIRTHDAY
                FROM DEV_PAS.T_PRECONT_PRODUCT PP
               WHERE PP.POLICY_ID = (SELECT 
           CHA.POLICY_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
          ) B
        ]]>
    </select>
</mapper>