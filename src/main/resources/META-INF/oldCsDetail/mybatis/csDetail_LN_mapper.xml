<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 保全查询 保单贷款 -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="csDetailLN_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT A.*,
			       T.TYPE,/*证件类型*/
			       G.GENDER_DESC/*性别*/
			  FROM (SELECT A.OLD_CUSTOMER_ID,/*客户号码*/
			               '投保人' CUSTOMER_ROLE,/*角色*/
			               A.CUSTOMER_NAME,/*姓名*/
			               A.CUSTOMER_CERT_TYPE,/*证件类型代码*/
			               A.CUSTOMER_CERTI_CODE,/*证件号码*/
			               A.CUSTOMER_GENDER,/*性别代码*/
			               A.CUSTOMER_BIRTHDAY,/*出生日期*/
			               H.POLICY_CODE/*代码编号*/
			          FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
			UNION 
			 SELECT A.*,
			       T.TYPE,
			       G.GENDER_DESC
			  FROM (SELECT A.OLD_CUSTOMER_ID,
			               '被保人' CUSTOMER_ROLE,
			               A.CUSTOMER_NAME,
			               A.CUSTOMER_CERT_TYPE,
			               A.CUSTOMER_CERTI_CODE,
			               A.CUSTOMER_GENDER,
			               A.CUSTOMER_BIRTHDAY,
			               H.POLICY_CODE
			          FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="csDetailLN_Page_policy" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT A.BUSI_PROD_CODE, /*险种代码*/
			   B.PRODUCT_NAME_SYS,/*险种名称*/
			   C.AMOUNT, /*保额*/
			   C.STD_PREM_AF /*保费*/
		 FROM DEV_PAS.T_CONTRACT_BUSI_PROD A LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT B
		ON A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
		LEFT JOIN
		DEV_PAS.T_CONTRACT_PRODUCT C
		ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
		WHERE
		A.POLICY_CODE = #{accept_status}
		]]>
	</select>
	<select id="csDetailLN_Page_Loan" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT T.CAPITAL_BALANCE CAPITAL_BALANCE, /*贷款金额*/
		       T.LOAN_START_DATE LOAN_START_DATE, /*贷款日期*/
		       TO_CHAR(ADD_MONTHS(T.LOAN_START_DATE, 6) - 1, 'YYYY-MM-DD') LOAN_END_DATE, /*贷款到期日期*/
		       T.INTEREST_BALANCE INTEREST_BALANCE, /*清偿利息合计*/
		       T.IS_AUTO_RENEW IS_AUTO_RENEW, /*是否自动续贷*/
		       T.IS_AUTHORITY IS_AUTHORITY, /*是否已进行授权*/
		       PC.HESITATE_FLAG /*是否在犹豫期*/
		  FROM DEV_PAS.T_CS_POLICY_ACCOUNT_STREAM T, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE T.Change_Id = pc.change_id
		   and T.OLD_NEW = '0'
		   AND T.POLICY_ID = #{policy_id}
		   AND T.CHANGE_ID = #{change_id}
		]]>
	</select>

	<!-- 通过 页面保全受理号 和 保单号 去查询数据 -->
	<select id="csfind_Pol_Acc_cha_id" resultType="java.util.Map"
		parameterType="java.util.Map"> 
		<![CDATA[
		SELECT CHA.POLICY_CHG_ID,/*保单变更ID*/
		       CHA.POLICY_ID,/*保单ID*/
		       CHA.POLICY_CODE,/*保单号*/
		       ACC.ACCEPT_ID,/*保全ID*/
		       ACC.ACCEPT_CODE,/*保全号*/
		       ACC.CHANGE_ID/*受理号*/
		  FROM DEV_PAS.T_CS_POLICY_CHANGE CHA, DEV_PAS.T_CS_ACCEPT_CHANGE ACC
		 WHERE CHA.CHANGE_ID = ACC.CHANGE_ID
		   AND CHA.POLICY_CODE = #{accept_status}
		   AND ACC.ACCEPT_CODE = #{accept_code}
   		]]>
	</select>
</mapper>