<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 领取形式变更 _mass_wb -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 保单信息 -->
	<select id="QRY_GB_find_Policy_Info" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
       SELECT 
        CU.OLD_CUSTOMER_ID,/*投保人客户号*/
        CU.CUSTOMER_NAME,/*投保人姓名*/
        PA.PAY_NEXT/*交费方式*/,
        PA.PAY_LOCATION,/*交费形式*/
        PA.NEXT_ACCOUNT_BANK,/*续期银行代码*/
        BK.BANK_NAME,/*银行名称*/
        PA.NEXT_ACCOUNT,/*银行账号:(续期缴费银行帐号)*/
        PA.NEXT_ACCOUNT_NAME/*续期账户名*/,
        (SELECT SUM(NVL(CP.STD_PREM_AF,0)+NVL(CP.EXTRA_PREM_AF,0)) FROM 
        DEV_PAS.T_CONTRACT_PRODUCT CP
        WHERE CP.POLICY_CODE=PH.POLICY_CODE 
        AND CP.LIABILITY_STATE!=3
        ) AS PREM_AF, /*保费*/
        (SELECT SUM(CP.AMOUNT) FROM DEV_PAS.T_CONTRACT_PRODUCT CP WHERE CP.POLICY_CODE=PH.POLICY_CODE AND CP.LIABILITY_STATE!=3) AS AMOUNT /*保额*/
    FROM 
    DEV_PAS.T_POLICY_HOLDER PH
    INNER JOIN DEV_PAS.T_CUSTOMER CU ON PH.CUSTOMER_ID=CU.CUSTOMER_ID
    INNER JOIN DEV_PAS.T_PAYER_ACCOUNT  PA ON PA.POLICY_ID=PH.POLICY_ID 
    INNER JOIN DEV_PAS.T_BANK BK ON BK.BANK_CODE=PA.NEXT_ACCOUNT_BANK
    WHERE PH.POLICY_CODE=#{policy_code}
         ]]>
	</select>
    <!-- 查询领取形式变更 -->
    <select id="QRY_GB_find_Get_Info" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[      
        
select d1.pay_year              pay_year_A, /*变更后领取年龄*/
       b1.pre_instalment_amount pre_instalment_amount_A, /*变更后领取标准*/
       d2.pay_year              pay_year_B, /*变更前领取年龄*/
       b2.pre_instalment_amount pre_instalment_amount_B /*变更前领取标准*/
  from dev_pas.t_cs_accept_change    a,
       dev_pas.t_cs_pay_plan         b1,
       dev_pas.t_cs_pay_plan         b2,
       dev_pas.t_cs_policy_change    c1,
       dev_pas.t_cs_contract_product d1,
       dev_pas.t_cs_contract_product d2

 WHERE a.accept_id = c1.accept_id
   and c1.policy_chg_id = b1.policy_chg_id
   and c1.policy_chg_id = b2.policy_chg_id
   and b1.old_new = '1'
   and b1.operation_type = '2'
   and b2.plan_id = b1.plan_id
   and b2.old_new = '0'
   and c1.policy_chg_id = d2.policy_chg_id
   and d1.policy_chg_id = c1.policy_chg_id
   and d1.old_new = '1'
   and d2.old_new = '0'
   and b1.item_id = d1.item_id
   and b1.item_id = d2.item_id
   and a.accept_code = #{accept_code} 
               
         ]]>
    </select>
</mapper>