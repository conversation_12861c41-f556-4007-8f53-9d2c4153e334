<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
    <!-- 综合查询_满期不续保申请-->
    <!-- 查询主险基本信息 -->
	<select id="QRY_EN_find_MainInfo" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[ SELECT T.AMOUNT, /*基本保额*/
       T.STD_PREM_AF, /*保费标准*/
       (SELECT A.CUSTOMER_ID /*被保人客户号*/
  FROM DEV_PAS.T_INSURED_LIST A
 WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_ID,
       (SELECT B.CUSTOMER_NAME /*被保人姓名*/
  FROM DEV_PAS.T_INSURED_LIST A
  LEFT JOIN DEV_PAS.T_CUSTOMER B 
    ON A.CUSTOMER_ID = B.CUSTOMER_ID 
 WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_NAME,
 C.BUSI_PROD_CODE, /*险种代码*/
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = C.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
  T.UNIT,                               /*份数*/
  C.VALIDATE_DATE,                      /*险种生效日期*/
  (SELECT CC.INITIAL_PREM_DATE FROM DEV_PAS.T_CONTRACT_MASTER CC WHERE CC.POLICY_CODE= T.POLICY_CODE ) INITIAL_PREM_DATE,
  C.MASTER_BUSI_ITEM_ID /*主险险种代码*/
  FROM DEV_PAS.T_CONTRACT_PRODUCT T /*保单险种责任组表*/
  LEFT JOIN DEV_PAS.T_EXTRA_PREM B
    ON T.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C
   ON T.POLICY_CODE = C.POLICY_CODE
  AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID
 WHERE T.POLICY_CODE =  #{policy_code}
  AND C.MASTER_BUSI_ITEM_ID IS NULL
         ]]>
	</select>
    <!-- 查询附加险不续保选项 -->
    <select id="QRY_EN_find_Not_MainInfo" resultType="java.util.Map"
        parameterType="java.util.Map"  >
        <![CDATA[ SELECT T.AMOUNT, /*基本保额*/
       T.STD_PREM_AF, /*保费标准*/
       (SELECT A.CUSTOMER_ID /*被保人客户号*/
  FROM DEV_PAS.T_INSURED_LIST A
 WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_ID,
       (SELECT B.CUSTOMER_NAME /*被保人姓名*/
  FROM DEV_PAS.T_INSURED_LIST A
  LEFT JOIN DEV_PAS.T_CUSTOMER B 
    ON A.CUSTOMER_ID = B.CUSTOMER_ID 
 WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_NAME,
 C.BUSI_PROD_CODE, /*险种代码*/
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = C.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
  T.UNIT,                               /*份数*/
  C.VALIDATE_DATE,                      /*险种生效日期*/
  (SELECT CC.INITIAL_PREM_DATE FROM DEV_PAS.T_CONTRACT_MASTER CC WHERE CC.POLICY_CODE= T.POLICY_CODE ) INITIAL_PREM_DATE,
  C.MASTER_BUSI_ITEM_ID /*主险险种代码*/
  FROM DEV_PAS.T_CONTRACT_PRODUCT T /*保单险种责任组表*/
  LEFT JOIN DEV_PAS.T_EXTRA_PREM B
    ON T.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C
   ON T.POLICY_CODE = C.POLICY_CODE
  AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID
 WHERE T.POLICY_CODE =  #{policy_code}
  AND C.MASTER_BUSI_ITEM_ID IS NOT NULL
         ]]>
    </select>
</mapper>