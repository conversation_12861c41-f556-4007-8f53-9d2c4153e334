<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 保全查询	保单质押第三方止付 -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="csDetailCS_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[		
			SELECT A.*, T.TYPE, /*证件类型*/ G.GENDER_DESC /*性别*/
			  FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
			               '投保人' CUSTOMER_ROLE, /*角色*/
			               A.CUSTOMER_NAME, /*姓名*/
			               A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
			               A.CUSTOMER_CERTI_CODE, /*证件号码*/
			               A.CUSTOMER_GENDER, /*性别代码*/
			               A.CUSTOMER_BIRTHDAY, /*出生日期*/
			               H.POLICY_CODE /*代码编号*/
			          FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
			UNION
			SELECT A.*, T.TYPE, G.GENDER_DESC
			  FROM (SELECT A.OLD_CUSTOMER_ID,
			               '被保人' CUSTOMER_ROLE,
			               A.CUSTOMER_NAME,
			               A.CUSTOMER_CERT_TYPE,
			               A.CUSTOMER_CERTI_CODE,
			               A.CUSTOMER_GENDER,
			               A.CUSTOMER_BIRTHDAY,
			               H.POLICY_CODE
			          FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
			UNION
			SELECT A.*, T.TYPE, G.GENDER_DESC
			  FROM (SELECT A.OLD_CUSTOMER_ID,
			               '保单受益人' CUSTOMER_ROLE,
			               A.CUSTOMER_NAME,
			               A.CUSTOMER_CERT_TYPE,
			               A.CUSTOMER_CERTI_CODE,
			               A.CUSTOMER_GENDER,
			               A.CUSTOMER_BIRTHDAY,
			               H.POLICY_CODE
			          FROM DEV_PAS.T_CONTRACT_BENE H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE            
        ]]>
	</select>
	<!-- 保单险种信息 -->
	<select id="csDetailCS_Page_Policy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.BUSI_PROD_CODE, /*险种代码*/  A.POLICY_ID,A.BUSI_ITEM_ID,
			      B.PRODUCT_NAME_SYS,/*险种名称*/
			      C.AMOUNT, /*保额*/
			      C.STD_PREM_AF, /*保费*/
			      D.CASH_VALUE, /*现金价值*/
			      D.INTEREST_CAPITAL LOAN_TOTAL,  /*贷款本息*/
			      '自垫本息字段需要确认一下，不清楚怎么存的' APL_TOTAL /*自垫本息*/
			FROM
			    DEV_PAS.T_CONTRACT_BUSI_PROD A /*险种表*/
			LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT B /*业务产品*/
			ON A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
			LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT C /*保单险种责任组表*/
			ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
			LEFT JOIN (
			  SELECT S.BUSI_ITEM_ID,
			         SUM(S.INTEREST_CAPITAL) AS INTEREST_CAPITAL,
			         SUM(S.CASH_VALUE) AS CASH_VALUE
			    FROM (SELECT POLICY_ID,
			                 CASH_VALUE,
			                 (CASE
			                   WHEN REGULAR_REPAY = 1 THEN
			                    CAPITAL_BALANCE + INTEREST_SUM
			                   WHEN REGULAR_REPAY = 0 THEN
			                    CAPITAL_BALANCE + INTEREST_BALANCE
			                 END) AS INTEREST_CAPITAL,
			                 BUSI_ITEM_ID
			            FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM S) S,
			         DEV_PAS.T_CONTRACT_MASTER M
			   WHERE S.POLICY_ID = M.POLICY_ID
			     AND M.POLICY_CODE = #{accept_status}
			   GROUP BY S.BUSI_ITEM_ID
			) D ON D.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			WHERE
			    A.POLICY_CODE = #{accept_status}
		]]>
	</select>
	<!-- 保单详情 -->
	<select id="csDetailCS_Page_PolicyDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 SELECT AA.POLICY_CODE,/*保单号*/
		     AA.VALIDATE_DATE, /*生效日期*/
		     AA.VALIDATE_DATE START_DATA, /*暂时保险起期*/
		     AA.EXPIRY_DATE, /*暂时保险终止期*/
		     (SELECT MIN(PD.PAY_DUE_DATE) FROM DEV_PAS.T_PAY_DUE PD WHERE PD.POLICY_CODE = #{accept_status}) SUBMISSION_DATE /*暂时的首次领取日期*/ 
		FROM DEV_PAS.T_CONTRACT_MASTER AA
		WHERE AA.POLICY_CODE = #{accept_status}
		]]>
	</select>
</mapper>