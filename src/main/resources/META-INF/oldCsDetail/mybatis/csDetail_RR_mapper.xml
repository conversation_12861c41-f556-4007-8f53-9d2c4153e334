<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询  续保险种转换 mass_wb -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="QRY_find_Types_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
（SELECT OLD_CUSTOMER_ID
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID） OLD_CUSTOMER_ID, /*客户ID*//*被保人号码*/ 
（SELECT CUSTOMER_NAME
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID） CUSTOMER_NAME, /*姓名*/
 B.BUSI_PROD_CODE, /*险种代码*/
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
 P.STD_PREM_AF,        /*保费标准*/
 P.UNIT,/*份数*/
 P.AMOUNT,/*基本保额*/
 C.INITIAL_PREM_DATE /*首期缴费日期*/
 FROM DEV_PAS.T_CONTRACT_PRODUCT P, DEV_PAS.T_CONTRACT_BUSI_PROD B,
 DEV_PAS.T_INSURED_LIST L,DEV_PAS.T_CONTRACT_MASTER C
 WHERE P.POLICY_CODE = B.POLICY_CODE
   AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
   AND B.POLICY_CODE = C.POLICY_CODE 
   AND L.POLICY_CODE = P.POLICY_CODE
   AND P.POLICY_CODE = #{policy_code} 
 GROUP BY L.CUSTOMER_ID,L.UPDATE_TIME,B.BUSI_PROD_CODE,P.STD_PREM_AF,P.AMOUNT,P.UNIT,C.INITIAL_PREM_DATE
		]]>
	</select>
    <!-- 查询险种基本信息 -->
    <!--BUG104_755 迁移险种转换信息修改 -->
    <select id="QRY_RR_find_Types_Info" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[  SELECT BP.PRODUCT_NAME_SYS, /*险种名称*/
       BP.PRODUCT_CODE_SYS AS BUSI_PROD_CODE, /*险种代码*/
       (SELECT PRODUCT_NAME_SYS
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE BUSINESS_PRD_ID = RC.NEW_BUSI_PRD_ID) AS NEW_PRODUCT_NAME_SYS, /*险种名称*/
           (SELECT PRODUCT_CODE_SYS
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE BUSINESS_PRD_ID = RC.NEW_BUSI_PRD_ID) AS NEW_BUSI_PROD_CODE, /*险种代码*/
       RC.OLD_PREM, /*保费标准*/
       RC.OLD_UNIT, /*份数*/
       RC.OLd_AMOUNT, /*基本保额*/
       (SELECT C.INITIAL_PREM_DATE
          FROM DEV_PAS.T_CONTRACT_MASTER C
         WHERE RC.POLICY_CODE = C.POLICY_CODE) INITIAL_PREM_DATE /*首期缴费日期*/
  FROM DEV_PAS.T_RENEW_CHANGE RC 
  INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC    ON RC.CHANGE_ID=AC.CHANGE_ID
  INNER JOIN  DEV_PDS.T_BUSINESS_PRODUCT BP ON BP.BUSINESS_PRD_ID=RC.OLD_BUSI_PRD_ID
 WHERE RC.POLICY_CODE =#{policy_code}
   AND AC.ACCEPT_CODE=#{accept_code}
        ]]>
        
    </select>
     <select id="QRY_RR_find_Types_Info_type2" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[  SELECT BP.PRODUCT_NAME_SYS, /*险种名称*/
       BP.PRODUCT_CODE_SYS AS BUSI_PROD_CODE, /*险种代码*/
       (SELECT PRODUCT_NAME_SYS
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE BUSINESS_PRD_ID = RC.NEW_BUSI_PRD_ID) AS NEW_PRODUCT_NAME_SYS, /*险种名称*/
           (SELECT PRODUCT_CODE_SYS
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE BUSINESS_PRD_ID = RC.NEW_BUSI_PRD_ID) AS NEW_BUSI_PROD_CODE, /*险种代码*/
       RC.New_Prem, /*保费标准*/
       RC.New_Unit, /*份数*/
       RC.New_Amount, /*基本保额*/
       (SELECT C.INITIAL_PREM_DATE
          FROM DEV_PAS.T_CONTRACT_MASTER C
         WHERE RC.POLICY_CODE = C.POLICY_CODE) INITIAL_PREM_DATE /*首期缴费日期*/
  FROM DEV_PAS.T_RENEW_CHANGE RC 
  INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC    ON RC.CHANGE_ID=AC.CHANGE_ID
  INNER JOIN  DEV_PDS.T_BUSINESS_PRODUCT BP ON BP.BUSINESS_PRD_ID=RC.OLD_BUSI_PRD_ID
 WHERE RC.POLICY_CODE =#{policy_code}
   AND AC.ACCEPT_CODE=#{accept_code}
   and rc.renew_change_type = '1'
        ]]>
        
    </select>
     <select id="QRY_RR_find_Types_Info_type3" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[  SELECT BP.PRODUCT_NAME_SYS, /*险种名称*/
       BP.PRODUCT_CODE_SYS AS BUSI_PROD_CODE, /*险种代码*/
       (SELECT PRODUCT_NAME_SYS
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE BUSINESS_PRD_ID = RC.NEW_BUSI_PRD_ID) AS NEW_PRODUCT_NAME_SYS, /*险种名称*/
           (SELECT PRODUCT_CODE_SYS
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE BUSINESS_PRD_ID = RC.NEW_BUSI_PRD_ID) AS NEW_BUSI_PROD_CODE, /*险种代码*/
       RC.OLD_PREM, /*保费标准*/
       RC.OLD_UNIT, /*份数*/
       RC.OLd_AMOUNT, /*基本保额*/
       (SELECT C.INITIAL_PREM_DATE
          FROM DEV_PAS.T_CONTRACT_MASTER C
         WHERE RC.POLICY_CODE = C.POLICY_CODE) INITIAL_PREM_DATE /*首期缴费日期*/
  FROM DEV_PAS.T_RENEW_CHANGE RC 
  INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC    ON RC.CHANGE_ID=AC.CHANGE_ID
  INNER JOIN  DEV_PDS.T_BUSINESS_PRODUCT BP ON BP.BUSINESS_PRD_ID=RC.OLD_BUSI_PRD_ID
 WHERE RC.POLICY_CODE =#{policy_code}
   AND AC.ACCEPT_CODE=#{accept_code}
   and rc.renew_change_type = '3'
        ]]>
        
    </select>
</mapper>