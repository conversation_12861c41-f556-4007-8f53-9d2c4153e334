<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="zerenXD_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
			<![CDATA[
          SELECT P.PRODUCT_CODE,/**精算产品代码:责任代码 */
			A.PRODUCT_NAME,/**责任组名称：责任名称*/
			P.EXPIRY_DATE,/**责任终止日期:祝寿金领取日期*/
			P.AMOUNT/**客户投保金额:保额*/
			FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P /**保单险种责任组表*/
			LEFT JOIN DEV_PDS.T_PRODUCT_LIFE A/**精算产品/责任组T_PRODUCT_LIFE*/
			ON P.PRODUCT_ID=A.PRODUCT_ID /**精算产品ID*/
			 WHERE P.POLICY_CODE=#{accept_status}
			AND P.IS_MASTER_ITEM='0'/**判断他是可选责任组的*/
			 AND ROWNUM<2

         ]]>
	</select>
	<select id="addzeXD_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
			<![CDATA[
			SELECT P.PRODUCT_CODE,/**精算产品代码:责任代码*/
		      A.PRODUCT_NAME,/**责任组名称：责任名称*/
		      P.AMOUNT,/**客户投保金额:原保额*/
		      P.STD_PREM_AF,/**原保费*/
		      P.APPEND_PREM_AF/**追加保费：增加保费*/
		      FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P /**保单险种责任组表*/ 
		      LEFT JOIN DEV_PDS.T_PRODUCT_LIFE A /**精算产品/责任组T_PRODUCT_LIFE*/
		      ON P.PRODUCT_ID=A.PRODUCT_ID /**精算产品ID*/
		      WHERE P.Change_Id=(SELECT AC.CHANGE_ID
				  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
				 WHERE AC.CHANGE_ID = PC.CHANGE_ID
				   AND AC.ACCEPT_CODE = #{accept_code}
				   AND PC.POLICY_CODE = #{accept_status})
		      AND P.IS_MASTER_ITEM='0'/**判断他是可选责任组的*/
		      AND P.OLD_NEW='1'
         ]]>
	</select>
	<select id="btfhXD_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
			<![CDATA[
          		SELECT X.FEE_AMOUNT 
		  FROM DEV_PAS.T_CS_POLICY_CHANGE X
		 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
</mapper>