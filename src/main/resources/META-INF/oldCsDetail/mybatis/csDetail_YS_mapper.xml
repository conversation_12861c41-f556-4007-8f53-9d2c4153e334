<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询保险起期变更（个人）mass_wb-->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 查询 险种信息-险种生效日期-险种终止日期-险种代码-险种名称-保费-保额 -->
	<select id="QRY_YS_find_Insurance1_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT T.VALIDATE_DATE,         /*险种生效日期*/
       T.EXPIRY_DATE,           /*险种终止日期 */  
       T.BUSI_PROD_CODE,        /*险种代码*/
       A.PRODUCT_NAME_SYS,      /*险种名称*/
       B.STD_PREM_AF,           /*保费*/
       B.AMOUNT                 /*保额*/
  FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD T /*险种表*/
  LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT A  /*业务产品*/
    ON T.BUSI_PRD_ID = A.BUSINESS_PRD_ID
  LEFT JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT B /*险种责任组表*/
    ON T.CHANGE_ID = B.CHANGE_ID
 WHERE T.CHANGE_ID = 
 ( SELECT 
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
		]]>
	</select>
	<!-- 查询 被保人客户号-被保人姓名-被保人出生日期-被保人投保年龄 --> 
	<select id="QRY_YS_find_Insurance_B_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
  SELECT B.OLD_CUSTOMER_ID,        /*被保人客户号*/
       B.CUSTOMER_NAME,           /*被保人姓名*/
       B.CUSTOMER_BIRTHDAY,      /*被保人出生日期*/
       A.INSURED_AGE             /*被保人投保年龄*/
  FROM DEV_PAS.T_INSURED_LIST A,   /*被保人表*/
  DEV_PAS.T_CUSTOMER B  /*客户表*/
  WHERE A.CUSTOMER_ID = B.CUSTOMER_ID  
  AND  A.POLICY_CODE = #{policy_code} 
		]]>
	</select>
	<!-- 查询新保险期间 -->
	<select id="QRY_YS_find_new_Insurance_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
       T.VALIDATE_DATE, /*险种生效日期*/
       T.EXPIRY_DATE,    /*险种终止日期*/
       B.STD_PREM_AF,      /*保费*/ 
       B.AMOUNT,          /*保额*/
       A.INSURED_AGE       /*被保人投保年龄*/
  FROM 
    DEV_PAS.T_CONTRACT_BUSI_PROD T /*险种表*/
  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT B /*险种责任组表*/
  ON T.BUSI_ITEM_ID = B.BUSI_ITEM_ID
  LEFT JOIN DEV_PAS.T_INSURED_LIST A   /*被保人表*/ 
  ON A.POLICY_ID = B.POLICY_ID
  WHERE T.POLICY_CODE = #{policy_code} 
		]]>
	</select>
	<!-- 查询补退费合计 -->
	<select id="QRY_YS_find_Charge_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT X.FEE_AMOUNT /**补退费合计*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE X
     WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
      FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
     WHERE AC.CHANGE_ID = PC.CHANGE_ID
      AND AC.ACCEPT_CODE = #{accept_code}
       AND PC.POLICY_CODE = #{policy_code})
		]]>
	</select> 
</mapper>