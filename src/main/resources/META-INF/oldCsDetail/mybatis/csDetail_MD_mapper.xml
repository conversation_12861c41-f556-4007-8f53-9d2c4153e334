<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
<!-- 综合查询保单迁移 mass_wb-->
	<select id="QRY_findMDInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT D.OLD_CUSTOMER_ID, /*客户号码*/
        '被保人' CUSTOMER_ROLE, /*被保人角色*/
        D.CUSTOMER_NAME, /*被保人姓名*/
        D.CUSTOMER_CERT_TYPE, /*证件类型编号*/
        T.TYPE,/*证件类型*/
        D.CUSTOMER_CERTI_CODE, /*证件号码*/
        D.CUSTOMER_GENDER, /*性别编号*/
        G.GENDER_DESC,    /*性别*/
        D.CUSTOMER_BIRTHDAY /*出生日期*/
   FROM DEV_PAS.T_INSURED_LIST C
   LEFT JOIN DEV_PAS.T_CUSTOMER D 
     ON C.CUSTOMER_ID = D.CUSTOMER_ID
   LEFT JOIN DEV_PAS.T_CERTI_TYPE T /*关联证件号*/
     ON D.CUSTOMER_CERT_TYPE=T.CODE
   LEFT JOIN DEV_PAS.T_GENDER G  /*关联性别*/
     ON D.CUSTOMER_GENDER=G.GENDER_CODE 
  WHERE TRIM (C.POLICY_CODE)  = #{policy_code}
		]]>
	</select>
	<!-- 原出境目的地国家 -->
	<select id="QRY_findMD_RAW_LEAVE_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT C.COUNTRY_CODE,/*国家代码*/
        C.RISK_LEVEL, /*风险等级*/
        T.COUNTRY_NAME,/*国家名称*/
        T.COUNTRY_ENGLISH_NAME /*国家风险类别*/
  FROM DEV_PAS.T_DESTINATION_COUNTRY  C 
  LEFT JOIN DEV_PAS.T_COUNTRY T ON C.COUNTRY_CODE=T.COUNTRY_CODE /*链接国家名称表*/
  WHERE C.CHANGE_ID=( SELECT
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
        AND ACC.ACCEPT_CODE = #{accept_code}
        AND CHA.POLICY_CODE = #{policy_code})
  AND C.OLD_NEW='0'
		]]>
	</select>
	<!-- 变更出境目的地国家 -->
	<select id="QRY_findMD_CHANGE_LEAVE_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT C.COUNTRY_CODE,/*国家代码*/
        C.RISK_LEVEL, /*风险等级*/
        T.COUNTRY_NAME,/*国家名称*/
        T.COUNTRY_ENGLISH_NAME /*国家风险类别*/
  FROM DEV_PAS.T_DESTINATION_COUNTRY  C 
  LEFT JOIN DEV_PAS.T_COUNTRY T ON C.COUNTRY_CODE=T.COUNTRY_CODE /*链接国家名称表*/
  WHERE C.CHANGE_ID=(SELECT
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
        AND ACC.ACCEPT_CODE = #{accept_code} 
        AND CHA.POLICY_CODE = #{policy_code})
  AND C.OLD_NEW='1'
		]]>
	</select>
	<!-- 出境事由 -->
	<select id="QRY_findMD_RAW_THING_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT DEPART_REASON,DEPART_REASON_DESC 
  FROM DEV_PAS.T_DESTINATION_COUNTRY  C WHERE C.CHANGE_ID = 
  (SELECT
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
        AND ACC.ACCEPT_CODE = #{accept_code} 
        AND CHA.POLICY_CODE = #{policy_code})
  AND C.OLD_NEW='1'
		]]>
	</select>
	<!-- 公共的查询投保人和被保人 -->
	<select id="QRY_TB_find_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
      SELECT A.*,
             T.TYPE,/*证件类型*/
             G.GENDER_DESC/*性别*/
        FROM (SELECT A.OLD_CUSTOMER_ID,/*客户号码*/
                     '投保人' CUSTOMER_ROLE,/*角色*/
                     A.CUSTOMER_NAME,/*姓名*/
                     A.CUSTOMER_CERT_TYPE,/*证件类型代码*/
                     A.CUSTOMER_CERTI_CODE,/*证件号码*/
                     A.CUSTOMER_GENDER,/*性别代码*/
                     A.CUSTOMER_BIRTHDAY,/*出生日期*/
                     H.POLICY_CODE/*代码编号*/
                FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
               WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                 AND H.POLICY_CODE = #{policy_code}) A,
             DEV_PAS.T_CERTI_TYPE T,
             DEV_PAS.T_GENDER G
       WHERE A.CUSTOMER_CERT_TYPE = T.CODE
         AND A.CUSTOMER_GENDER = G.GENDER_CODE
      UNION 
       SELECT A.*,
             T.TYPE,
             G.GENDER_DESC 
        FROM (SELECT A.OLD_CUSTOMER_ID,
                     '被保人' CUSTOMER_ROLE,
                     A.CUSTOMER_NAME,
                     A.CUSTOMER_CERT_TYPE,
                     A.CUSTOMER_CERTI_CODE,
                     A.CUSTOMER_GENDER,
                     A.CUSTOMER_BIRTHDAY,
                     H.POLICY_CODE
                FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
               WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                 AND H.POLICY_CODE = #{policy_code}) A,
             DEV_PAS.T_CERTI_TYPE T,
             DEV_PAS.T_GENDER G
       WHERE A.CUSTOMER_CERT_TYPE = T.CODE
         AND A.CUSTOMER_GENDER = G.GENDER_CODE
		]]>
	</select>
</mapper>