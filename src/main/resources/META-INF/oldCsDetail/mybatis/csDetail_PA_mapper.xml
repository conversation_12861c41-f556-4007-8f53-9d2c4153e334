<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 老核心的名字:附加险加保,新核心名字:加保 _mass_wb -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 告知信息 -->
	<select id="QRY_PA_Infrom_find_Info" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT B.QUESTION_CONTENT,A.SURVEY_MODULE_RESULT 
FROM DEV_PAS.V_QUESTIONAIRE_CUSTOMER_ALL A LEFT JOIN DEV_PAS.T_QUESTIONAIRE_INFO B  
ON A.SURVEY_QUESTION_ID=B.SURVEY_QUESTION_ID WHERE POLICY_CODE = #{policy_code}
         ]]>
	</select> 
    <!-- 险种基本信息 -->
    <select id="QRY_PA_find_TypesInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[SELECT T.AMOUNT, /*基本保额*/
       T.STD_PREM_AF, /*保费标准*/
       (SELECT A.CUSTOMER_ID /*被保人客户号*/
  FROM DEV_PAS.T_INSURED_LIST A
 WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_ID,
       (SELECT B.CUSTOMER_NAME /*被保人姓名*/
  FROM DEV_PAS.T_INSURED_LIST A
  LEFT JOIN DEV_PAS.T_CUSTOMER B 
    ON A.CUSTOMER_ID = B.CUSTOMER_ID 
 WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_NAME,
 C.BUSI_PROD_CODE, /*险种代码*/
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = C.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
  T.UNIT,                               /*份数*/
  C.VALIDATE_DATE,                      /*险种生效日期*/
  C.EXPIRY_DATE /*险种失效日期*/ 
  FROM DEV_PAS.T_CONTRACT_PRODUCT T /*保单险种责任组表*/
  LEFT JOIN DEV_PAS.T_EXTRA_PREM B
    ON T.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C
   ON T.POLICY_CODE = C.POLICY_CODE
  AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID
 WHERE T.POLICY_CODE = #{policy_code}
  AND C.MASTER_BUSI_ITEM_ID IS NULL
         ]]>
    </select>
    <!-- 附加险基本信息 -->
    <select id="QRY_PA_find_AdditionTypesInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT DISTINCT A.BUSI_PROD_CODE,A.PRODUCT_NAME_SYS,A.CUSTOMER_NAME,
       A.AMOUNT,
       A.STD_PREM_AF,
       A.AMOUNTB,A.UNIT,A.APPEND_PREM_AF 
       FROM (
 SELECT A.*, B.*
   FROM (SELECT C.BUSI_PROD_CODE, /*险种代码*/
                (SELECT PRODUCT_NAME_SYS
                   FROM DEV_PAS.T_BUSINESS_PRODUCT
                  WHERE PRODUCT_CODE_SYS = C.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
                (SELECT B.CUSTOMER_NAME /*被保人姓名*/
                   FROM DEV_PAS.T_INSURED_LIST A
                   LEFT JOIN DEV_PAS.T_CUSTOMER B
                     ON A.CUSTOMER_ID = B.CUSTOMER_ID
                  WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_NAME,
                T.AMOUNT, /*原保额*/
                T.STD_PREM_AF, /*保费标准*/ /*原保费*/
                T.CHANGE_ID ACI
           FROM DEV_PAS.T_CS_CONTRACT_PRODUCT T /*保单险种责任组表*/
           LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C  /*险种表*/
             ON T.POLICY_CODE = C.POLICY_CODE 
             AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID /*险种ID*/
          WHERE T.CHANGE_ID = 
          (SELECT 
                ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
        AND ACC.ACCEPT_CODE = #{accept_code}
        AND CHA.POLICY_CODE = #{policy_code})
            AND C.MASTER_BUSI_ITEM_ID IS NOT NULL
            AND T.OLD_NEW = '0') A,
        (SELECT T.AMOUNT AS AMOUNTB, /*原保额*/ /*更新后保额*/
                T.UNIT, /*份数*/
                T.APPEND_PREM_AF /*追加保费*/ /*加保保费*/ /*更新后追加保费*/,
                T.CHANGE_ID BCI
           FROM DEV_PAS.T_CS_CONTRACT_PRODUCT T /*保单险种责任组表*/
           LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C /*险种表*/
             ON T.POLICY_CODE = C.POLICY_CODE 
             AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID /*险种ID*/
          WHERE T.CHANGE_ID = 
                (SELECT 
                        ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
        AND ACC.ACCEPT_CODE = #{accept_code}
        AND CHA.POLICY_CODE = #{policy_code})
            AND C.MASTER_BUSI_ITEM_ID IS NOT NULL
            AND T.OLD_NEW = '1') B
  WHERE A.ACI = B.BCI
 ) A
         ]]>
    </select>
</mapper>