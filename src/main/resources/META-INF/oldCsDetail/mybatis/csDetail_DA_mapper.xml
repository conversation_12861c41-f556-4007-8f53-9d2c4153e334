<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="kehuDA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
        	 SELECT A.*, T.TYPE, /*证件类型*/
        G.GENDER_DESC /*性别*/
          FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
                       '投保人' CUSTOMER_ROLE, /*角色*/
                       A.CUSTOMER_NAME, /*姓名*/
                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
                       A.CUSTOMER_GENDER, /*性别代码*/
                       A.CUSTOMER_BIRTHDAY /*出生日期*/
                  FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
                  DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
                   AND H.POLICY_CODE = #{accept_status}) A,
                    DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G/**性别代码表*/
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
           AND A.CUSTOMER_GENDER = G.GENDER_CODE/**客户性别*/
        UNION
        SELECT A.*, T.TYPE, G.GENDER_DESC
          FROM (SELECT A.OLD_CUSTOMER_ID,
                       '被保人' CUSTOMER_ROLE,
                       A.CUSTOMER_NAME,
                       A.CUSTOMER_CERT_TYPE,
                       A.CUSTOMER_CERTI_CODE,
                       A.CUSTOMER_GENDER,
                       A.CUSTOMER_BIRTHDAY
                  FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
                   DEV_PAS.T_CUSTOMER A/**客户表*/
                 WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
                   AND H.POLICY_CODE =#{accept_status}) A,
               DEV_PAS.T_CERTI_TYPE T,
               DEV_PAS.T_GENDER G
         WHERE A.CUSTOMER_CERT_TYPE = T.CODE
           AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="QRY_DA_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[ SELECT T.AMOUNT, /*基本保额*/
       T.STD_PREM_AF, /*保费标准*/
       A.CUSTOMER_ID, /*被保人客户号*/
         (SELECT B.CUSTOMER_NAME /*被保人姓名*/ FROM DEV_PAS.T_INSURED_LIST A LEFT JOIN DEV_PAS.T_CUSTOMER B 
        ON A.CUSTOMER_ID = B.CUSTOMER_ID 
     WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_NAME,
     C.BUSI_PROD_CODE, /*险种代码*/
     (SELECT PRODUCT_NAME_SYS FROM DEV_PAS.T_BUSINESS_PRODUCT WHERE PRODUCT_CODE_SYS = C.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
      T.UNIT,                               /*份数*/
      C.VALIDATE_DATE,                      /*险种生效日期*/
      C.EXPIRY_DATE /*险种失效日期*/ 
      FROM DEV_PAS.T_CONTRACT_PRODUCT T /*保单险种责任组表*/
      LEFT JOIN DEV_PAS.T_EXTRA_PREM B
        ON T.POLICY_ID = B.POLICY_ID
      INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD C /*险种表*/
       ON T.POLICY_CODE = C.POLICY_CODE
      AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID /*险种ID*/
      LEFT JOIN DEV_PAS.T_INSURED_LIST A
       ON A.POLICY_CODE = T.POLICY_CODE
     WHERE T.POLICY_CODE =  #{policy_code}
      AND C.MASTER_BUSI_ITEM_ID IS NULL
         ]]>
	</select>
	<select id="QRY_DA_find_Inform_Info" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[SELECT 
       DISTINCT
       B.SURVEY_VERSION,      /*告知版别*/
       B.SURVEY_CODE,         /*告知编码*/
       B.QUESTION_CONTENT,    /*告知内容*/
       A.SURVEY_MODULE_RESULT /*填写内容*/
   FROM DEV_PAS.T_POLICY_HOLDER C /*投保人表*/
  LEFT JOIN DEV_PAS.V_QUESTIONAIRE_CUSTOMER_ALL A   /*客户告知表*/
    ON C.POLICY_CODE = A.POLICY_CODE
  LEFT JOIN DEV_PAS.T_QUESTIONAIRE_INFO B  /*告知信息表*/
    ON A.SURVEY_QUESTION_ID = B.SURVEY_QUESTION_ID
 WHERE C.POLICY_CODE = #{policy_code} 
         ]]>
	</select>
	<select id="QRY_DA_Page_TypesCustomer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
        	 SELECT DISTINCT 
           T.AMOUNT,                              /*基本保额*/
           T.STD_PREM_AF,                      /*保费标准*/
           A.CUSTOMER_ID,                       /*被保人客户号*/
  (SELECT B.CUSTOMER_NAME FROM DEV_PAS.T_INSURED_LIST A LEFT JOIN DEV_PAS.T_CUSTOMER B 
  ON A.CUSTOMER_ID = B.CUSTOMER_ID WHERE A.POLICY_CODE = T.POLICY_CODE) CUSTOMER_NAME,/*被保人姓名*/
     C.BUSI_PROD_CODE,                      /*险种代码*/
     (SELECT PRODUCT_NAME_SYS FROM DEV_PAS.T_BUSINESS_PRODUCT WHERE PRODUCT_CODE_SYS = C.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
      T.UNIT,                               /*份数*/
      C.VALIDATE_DATE,                      /*险种生效日期*/
      C.EXPIRY_DATE                         /*险种失效日期*/ 
      FROM DEV_PAS.T_CS_CONTRACT_PRODUCT T     /*保单险种责任组表*/
      LEFT JOIN DEV_PAS.T_EXTRA_PREM B
        ON T.POLICY_ID = B.POLICY_ID
      INNER JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD C /*险种表*/
       ON T.POLICY_CODE = C.POLICY_CODE
      AND T.BUSI_ITEM_ID = C.BUSI_ITEM_ID /*险种ID*/
      and t.change_id = c.change_id
      LEFT JOIN DEV_PAS.T_INSURED_LIST A
       ON A.POLICY_CODE = T.POLICY_CODE 
     where t.change_id =    (SELECT
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
        AND ACC.ACCEPT_CODE = #{accept_code}
        AND CHA.POLICY_CODE = #{policy_code}) 
     and t.old_new = '1'
     and c.old_new = '1'
         ]]>
	</select>
</mapper>