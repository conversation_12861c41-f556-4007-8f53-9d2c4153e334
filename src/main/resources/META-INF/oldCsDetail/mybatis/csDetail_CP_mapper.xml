<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 保全查询 公司解约 -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="csDetailCP_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT A.*, T.TYPE, /*证件类型*/ G.GENDER_DESC /*性别*/
				  FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
				               '投保人' CUSTOMER_ROLE, /*角色*/
				               A.CUSTOMER_NAME, /*姓名*/
				               A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
				               A.CUSTOMER_CERTI_CODE, /*证件号码*/
				               A.CUSTOMER_GENDER, /*性别代码*/
				               A.CUSTOMER_BIRTHDAY, /*出生日期*/
				               H.POLICY_CODE /*代码编号*/
				          FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
				         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
				           AND H.POLICY_CODE = #{accept_status}) A,
				       DEV_PAS.T_CERTI_TYPE T,
				       DEV_PAS.T_GENDER G
				 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
				   AND A.CUSTOMER_GENDER = G.GENDER_CODE
				UNION
				SELECT A.*, T.TYPE, G.GENDER_DESC
				  FROM (SELECT A.OLD_CUSTOMER_ID,
				               '被保人' CUSTOMER_ROLE,
				               A.CUSTOMER_NAME,
				               A.CUSTOMER_CERT_TYPE,
				               A.CUSTOMER_CERTI_CODE,
				               A.CUSTOMER_GENDER,
				               A.CUSTOMER_BIRTHDAY,
				               H.POLICY_CODE
				          FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
				         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
				           AND H.POLICY_CODE = #{accept_status}) A,
				       DEV_PAS.T_CERTI_TYPE T,
				       DEV_PAS.T_GENDER G
				 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
				   AND A.CUSTOMER_GENDER = G.GENDER_CODE
				UNION
				SELECT A.*, T.TYPE, G.GENDER_DESC
				  FROM (SELECT A.OLD_CUSTOMER_ID,
				               '受益人' CUSTOMER_ROLE,
				               A.CUSTOMER_NAME,
				               A.CUSTOMER_CERT_TYPE,
				               A.CUSTOMER_CERTI_CODE,
				               A.CUSTOMER_GENDER,
				               A.CUSTOMER_BIRTHDAY,
				               H.POLICY_CODE
				          FROM DEV_PAS.T_CONTRACT_BENE H, DEV_PAS.T_CUSTOMER A
				         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
				           AND H.POLICY_CODE = #{accept_status}) A,
				       DEV_PAS.T_CERTI_TYPE T,
				       DEV_PAS.T_GENDER G
				 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
				   AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>

</mapper>