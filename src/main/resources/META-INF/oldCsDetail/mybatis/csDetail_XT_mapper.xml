<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询协议退保mass_wb-->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 退保信息+退费合计 -->
	<select id="QRY_XT_ReturnInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select 
		
		       SDATE.SURRENDER_TYPE, /*退保业务类型*/
               SDATE.CAUSE_REMARK, /*退保原因备注*/
               SDATE.AGENT_HOLDER_RELATION, /*投保人与业务员关系*/
               SDATE.SURRENDER_CAUSE, /*退保原因代码*/
               SDATE.POLICY_CHG_ID,
               SDATE.HESITATE_FLAG,
               SDATE.STAND_FEE_AMOUNT,
               SDATE.ADJUST_FEE_AMOUNT,
       (SELECT A.ACKNOWLEDGE_DATE /*客户签收日期*/
          FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT A
         WHERE A.POLICY_ID =
               (SELECT CHA.POLICY_ID /*保单ID*/
                  FROM DEV_PAS.T_CS_POLICY_CHANGE CHA, /*保单变更管理表*/
                       DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
                 WHERE CHA.CHANGE_ID = ACC.CHANGE_ID
                   AND ACC.ACCEPT_CODE =  #{accept_code}
                   AND CHA.POLICY_CODE = #{policy_code})) ACKNOWLEDGE_DATE,
       (SELECT L.RELATION_NAME
          FROM DEV_PAS.T_LA_PH_RELA L
         WHERE L.RELATION_CODE = SDATE.AGENT_HOLDER_RELATION) RELATION_NAME, /*关系*/
       (SELECT S.SURRENDER_CAUSE_NAME
          FROM DEV_PAS.T_SURRENDER_CAUSE S
         WHERE S.SURRENDER_CAUSE = SDATE.SURRENDER_CAUSE) SURRENDER_CAUSE_NAME, /*退保原因*/
       (SELECT C.VALIDATE_DATE
          FROM DEV_PAS.T_CS_CONTRACT_MASTER C
         WHERE C.POLICY_CHG_ID = SDATE.POLICY_CHG_ID
           AND C.OLD_NEW = '1') VALIDATE_DATE, /*生效日期*/
       (SELECT CC.INITIAL_PREM_DATE
          FROM DEV_PAS.T_CONTRACT_MASTER CC
         WHERE CC.POLICY_CODE = #{policy_code}) INITIAL_PREM_DATE /*交费对应日*/
		from (SELECT 
           T.SURRENDER_TYPE,        /*退保业务类型*/ 
           T.CAUSE_REMARK,          /*退保原因备注*/
           T.AGENT_HOLDER_RELATION, /*投保人与业务员关系*/ 
           T.SURRENDER_CAUSE,        /*退保原因代码*/
           sum(T.STAND_FEE_AMOUNT) STAND_FEE_AMOUNT,  /*调整前解约退费_退费合计金额*/
           sum(T.ADJUST_FEE_AMOUNT) ADJUST_FEE_AMOUNT,  /*调整后解约退费*/
           T.HESITATE_FLAG, /*是否在犹豫期1-犹豫期内2-犹豫期外3-非特定的补发原因4-特定的补发原因*/
           T.POLICY_CHG_ID
           
      FROM DEV_PAS.T_SURRENDER T /*退保记录表*/
        LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE C
       ON T.CHANGE_ID = C.CHANGE_ID
      WHERE T.CHANGE_ID = 
      (SELECT
             CHA.CHANGE_ID  /*受理号*/
          FROM DEV_PAS.T_CS_POLICY_CHANGE CHA, /*保单变更管理表*/ 
               DEV_PAS.T_CS_ACCEPT_CHANGE ACC  /*受理变更管理表*/
         WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
       ]]>
       <if test=" delay_cause != null and delay_cause != ''  ">
           <![CDATA[  AND T.BUSI_ITEM_ID in (${delay_cause})    ]]>
             </if>
  <![CDATA[
        group by T.SURRENDER_TYPE, /*退保业务类型*/
                  T.CAUSE_REMARK, /*退保原因备注*/
                  T.AGENT_HOLDER_RELATION, /*投保人与业务员关系*/
                  T.SURRENDER_CAUSE, /*退保原因代码*/
                  T.POLICY_CHG_ID,
                  T.HESITATE_FLAG
                 
       )SDATE
		]]>
	</select>
    <!-- 险种退费合计 -->
    <select id="QRY_XT_Types_ReturnInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[   SELECT DISTINCT B.BUSI_ITEM_ID, /*险种ID,*/
                B.BUSI_PROD_CODE,  /*险种代码*/
                C.PRODUCT_NAME_SYS,/*险种名称*/
                 sum(A.STAND_FEE_AMOUNT) STAND_FEE_AMOUNT, /*调整前解约退费*/
                 sum(A.ADJUST_FEE_AMOUNT) ADJUST_FEE_AMOUNT/*调整后解约退费*/ 
  FROM DEV_PAS.T_SURRENDER A
  LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD B
    ON A.CHANGE_ID = B.CHANGE_ID AND B.BUSI_ITEM_ID=A.BUSI_ITEM_ID
  LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT C
    ON B.BUSI_PRD_ID = C.BUSINESS_PRD_ID
 WHERE A.CHANGE_ID = 
 (SELECT
         CHA.CHANGE_ID /*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/ 
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
       AND B.OLD_NEW = '0'
       group by B.BUSI_ITEM_ID,B.BUSI_PROD_CODE,C.PRODUCT_NAME_SYS
        ]]>
    </select>
    <!-- 退保费用调整 -->
    <select id="QRY_XT_Types_Return_ChargeInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT 
                DISTINCT B.BUSI_ITEM_ID,   /*险种ID*/
                B.BUSI_PROD_CODE,        /*险种代码*/
                C.PRODUCT_NAME_SYS,      /*险种名称*/
                A.SURRENDER_TYPE,        /*退保业务类型代码*/ 
                S.TYPE_NAME,             /*退保业务类型*/
               sum(A.STAND_AMOUNT) as STAND_AMOUNT ,          /*调整前基本保额现价*/
                sum(A.ADJUST_STAND_AMOUNT) as ADJUST_STAND_AMOUNT,   /*调整后基本保额现价*/ 
                sum(A.BONUS_AMOUNT) as BONUS_AMOUNT,          /*调整前红利保额现价*/
                sum(A.ADJUST_BONUS_AMOUNT)as ADJUST_BONUS_AMOUNT ,   /*调整后红利保额现价*/
               sum(A.END_BONUS) as END_BONUS,             /*调整前终了红利*/
                sum(A.ADJUST_END_BONUS) as ADJUST_END_BONUS ,      /*调整后终了红利*/
               sum(A.LOAN_CAPITAL) as LOAN_CAPITAL,    /* 贷款清偿本金*/
               sum(A.LOAN_INTEREST) as LOAN_INTEREST  /* 贷款清偿利息*/
  FROM DEV_PAS.T_SURRENDER A
  LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD B
    ON A.CHANGE_ID = B.CHANGE_ID AND B.BUSI_ITEM_ID=A.BUSI_ITEM_ID
    and B.OLD_NEW='1'
  LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT C
    ON B.BUSI_PRD_ID = C.BUSINESS_PRD_ID
  LEFT JOIN DEV_PAS.T_SURRENDER_TYPE S
    ON A.SURRENDER_TYPE = S.TYPE_CODE
 WHERE A.CHANGE_ID = 
  (SELECT
         CHA.CHANGE_ID /*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/ 
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
       group by    B.BUSI_ITEM_ID,   /*险种ID*/
                  B.BUSI_PROD_CODE ,      /*险种代码*/
                  C.PRODUCT_NAME_SYS,      /*险种名称*/
                  A.SURRENDER_TYPE,        /*退保业务类型代码*/ 
                  S.TYPE_NAME 
       
        ]]>
    </select>
    <!-- 回访电话 -->
    <select id="QRY_XT_Find_TELInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT BC.BIZ_CODE,       /*业务号码*/ 
       CO.MOBILE_TEL,     /*联系电话*/
       CO.BOJ_CALL_TEL,   /*回访电话*/
       BC.BIZ_SOURCE      /*业务类型*/
  FROM DEV_PAS.T_BIZ_CALL BC /*管理电话服务主表*/
 INNER JOIN DEV_PAS.T_BIZ_CALL_OBJECT CO   /*管理电话服务回访对象表*/
    ON BC.BIZ_CALL_ID = CO.BIZ_CALL_ID
 WHERE BC.BIZ_SOURCE = '004'
   AND BC.BIZ_CODE = #{accept_code} /*业务号码*/ 
        ]]>
    </select>
    <!-- 1、获取保单主险生效日期和主险状态，主险 -->
    <select id="QRY_XT_Find_PolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT CP.VALIDATE_DATE,                  /*保单生效日期*/
       CP.LIABILITY_STATE,                /*保单效力状态*/
       CP.BUSI_PROD_CODE,                 /*所属业务产品代码*/
       CP.BUSI_ITEM_ID                    /*险种ID*/
  FROM DEV_PAS.T_CONTRACT_BUSI_PROD CP    /*险种表*/
 WHERE CP.POLICY_CODE = #{policy_code}
   AND CP.MASTER_BUSI_ITEM_ID IS NULL
        ]]>
    </select>
    <!-- 2、获取保单主险交至日期： -->
    <select id="QRY_XT_Find_HandTimeInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT CE.PAY_DUE_DATE
  FROM DEV_PAS.T_CONTRACT_EXTEND CE
 WHERE CE.POLICY_CODE = #{policy_code}
   AND CE.BUSI_ITEM_ID = #{busi_item_id}
        ]]>
    </select>
    <!-- 3、获取保全申请日期： -->
    <select id="QRY_XT_Find_ApplyTimeInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT CA.APPLY_TIME
  FROM DEV_PAS.T_CS_APPLICATION CA
 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
    ON CA.CHANGE_ID = AC.CHANGE_ID
 WHERE AC.ACCEPT_CODE = #{accept_code} 
        ]]>
    </select>
</mapper>