<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="bdxzBC_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
              SELECT CBP.BUSI_PROD_CODE, /**险种代码 */
			       BP.PRODUCT_NAME_SYS, /**险种名称 */
			       CBP.VALIDATE_DATE, /**生效日期 */
			       SUM(CP.STD_PREM_AF) AS TOTAL_PREM_AF, /**总保费 */
			       CE.PAY_DUE_DATE/**交至日期 -缴费终止日期*/
			  FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP
			  LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT BP
			    ON BP.PRODUCT_CODE_SYS = CBP.BUSI_PROD_CODE
			  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
			    ON CP.POLICY_CODE = CBP.POLICY_CODE
			  LEFT JOIN DEV_PAS.T_CONTRACT_EXTEND CE 
			    ON ce.busi_item_id = cbp.busi_item_id  
			 WHERE 1 = 1
			   AND CBP.POLICY_CODE = #{accept_status}
			   AND CBP.CHANGE_ID = ${change_id}
			   AND CBP.OLD_NEW = '1'
			   AND CBP.MASTER_BUSI_ITEM_ID IS NULL
			 GROUP BY CBP.BUSI_PROD_CODE, BP.PRODUCT_NAME_SYS, CBP.VALIDATE_DATE,ce.pay_due_date
         ]]>
  
	</select>
	<select id="bdkhBC_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
              SELECT A.*,
	             T.TYPE,/*证件类型*/
	             G.GENDER_DESC/*性别*/
		        FROM (SELECT A.OLD_CUSTOMER_ID,/*客户号码*/
		                     '1-投保人' CUSTOMER_ROLE,/*角色*/
		                     A.CUSTOMER_NAME,/*姓名*/
		                     A.CUSTOMER_CERT_TYPE,/*证件类型代码*/
		                     A.CUSTOMER_CERTI_CODE,/*证件号码*/
		                     A.CUSTOMER_GENDER,/*性别代码*/
		                     A.CUSTOMER_BIRTHDAY,/*出生日期*/
		                     H.POLICY_CODE/*代码编号*/
		                FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
		               WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
		                 AND H.POLICY_CODE = #{accept_status}) A,
		             DEV_PAS.T_CERTI_TYPE T,
		             DEV_PAS.T_GENDER G
		       WHERE A.CUSTOMER_CERT_TYPE = T.CODE
		         AND A.CUSTOMER_GENDER = G.GENDER_CODE
		      UNION 
		       SELECT A.*,
		             T.TYPE,
		             G.GENDER_DESC 
		        FROM (SELECT A.OLD_CUSTOMER_ID,
		                     '2-被保人' CUSTOMER_ROLE,
		                     A.CUSTOMER_NAME,
		                     A.CUSTOMER_CERT_TYPE,
		                     A.CUSTOMER_CERTI_CODE,
		                     A.CUSTOMER_GENDER,
		                     A.CUSTOMER_BIRTHDAY,
		                     H.POLICY_CODE
		                FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
		               WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
		                 AND H.POLICY_CODE = #{accept_status}) A,
		             DEV_PAS.T_CERTI_TYPE T,
		             DEV_PAS.T_GENDER G
		       WHERE A.CUSTOMER_CERT_TYPE = T.CODE
		         AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
  
	</select>
	
	<select id="yuanbeneBC_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT IL.CUSTOMER_ID, /**被保人号 */
		       IC.CUSTOMER_NAME, /**被保人姓名 */
		       CCB.BENE_TYPE, /**受益人类别 */
		       bc.customer_name as BENE_CUSTOMER_NAME,/**受益人姓名 */
		       bc.customer_gender,/**受益人性别 */
		       bc.customer_cert_type,/**受益人证件类型 */
		       bc.customer_certi_code,/**受益人证件号码 */
		       ccb.designation,/**与被保人关系 */
		       ccb.share_order,/**受益顺序 */
		       /**受益分子*/
       		   /**受益分母 */
       		   ccb.SHARE_RATE,/**受益比例 */
		       /**速填 */
		       bc.customer_birthday,/**出生日期 */
		       bc.job_code,/**职业代码 */
		       bc.mobile_tel,/**联系电话 */
		       a.address,/** 通讯地址*/
		       a.post_code,/** 邮编 */
		       bc.cust_cert_star_date,/**证件有效起期 */
		       bc.cust_cert_end_date,/**证件有效止期 */
		       (CASE 
		         WHEN to_char(BC.CUST_CERT_END_DATE,'yyyy-mm-dd')='9999-12-31' 
		           THEN
		             '是'
		            ELSE
		             '否'
		             END) AS is_cust_long,/**长期 */
		       bc.country_code/**国籍 */
		  FROM DEV_PAS.T_CS_CONTRACT_BENE CCB
		  LEFT JOIN DEV_PAS.T_INSURED_LIST IL
		    ON CCB.INSURED_ID = IL.LIST_ID
		  LEFT JOIN DEV_PAS.T_CUSTOMER IC
		    ON IL.CUSTOMER_ID = IC.CUSTOMER_ID
		  LEFT JOIN DEV_PAS.T_CUSTOMER BC
		    ON CCB.CUSTOMER_ID = BC.CUSTOMER_ID
		  LEFT JOIN DEV_PAS.T_ADDRESS A  
		    ON bc.customer_id = a.customer_id
		    AND ccb.address_id = a.address_id
		 WHERE CCB.OLD_NEW = '0'
		   AND CCB.OPERATION_TYPE = '0'
		   AND ccb.pOLICY_CODE =#{accept_status}
		   AND ccb.change_id=#{change_id}
         ]]>
	</select>
	<sql id="xinbeneBC_Page_Customer_SQL">
        <![CDATA[
        SELECT IL.CUSTOMER_ID, /**被保人号 */
               IC.CUSTOMER_NAME, /**被保人姓名 */
               CCB.BENE_TYPE, /**受益人类别 */
               bc.customer_name as BENE_CUSTOMER_NAME,/**受益人姓名 */
               bc.customer_gender,/**受益人性别 */
               bc.customer_cert_type,/**受益人证件类型 */
               bc.customer_certi_code,/**受益人证件号码 */
               ccb.designation,/**与被保人关系 */
               ccb.share_order,/**受益顺序 */
               /**受益分子*/
               /**受益分母 */
               ccb.SHARE_RATE,/**受益比例 */
               /**速填 */
               bc.customer_birthday,/**出生日期 */
               bc.job_code,/**职业代码 */
               bc.mobile_tel,/**联系电话 */
               a.address,/** 通讯地址*/
               a.post_code,/** 邮编 */
               bc.cust_cert_star_date,/**证件有效起期 */
               bc.cust_cert_end_date,/**证件有效止期 */
               (CASE 
                 WHEN to_char(BC.CUST_CERT_END_DATE,'yyyy-mm-dd')='9999-12-31' 
                   THEN
                     '是'
                    ELSE
                     '否'
                     END) AS is_cust_long,/**长期 */
               bc.country_code/**国籍 */
          FROM DEV_PAS.T_CS_CONTRACT_BENE CCB
          LEFT JOIN DEV_PAS.T_INSURED_LIST IL
            ON CCB.INSURED_ID = IL.LIST_ID
          LEFT JOIN DEV_PAS.T_CUSTOMER IC
            ON IL.CUSTOMER_ID = IC.CUSTOMER_ID
          LEFT JOIN DEV_PAS.T_CUSTOMER BC
            ON CCB.CUSTOMER_ID = BC.CUSTOMER_ID
          LEFT JOIN DEV_PAS.T_ADDRESS A  
            ON bc.customer_id = a.customer_id
            AND ccb.address_id = a.address_id
         WHERE CCB.OLD_NEW = '1'
           AND ccb.POLICY_CODE =#{accept_status}
           and ccb.CHANGE_ID=#{change_id}
          ]]>
    </sql>
	<select id="xinbeneBC_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
        <include refid="xinbeneBC_Page_Customer_SQL" />
		<![CDATA[		
          AND CCB.OPERATION_TYPE != '0'
         ]]>
	</select>
	<select id="xinbeneBC_Page_Customer_ALL" resultType="java.util.Map"
        parameterType="java.util.Map">
        <include refid="xinbeneBC_Page_Customer_SQL" />
    </select>	
</mapper>