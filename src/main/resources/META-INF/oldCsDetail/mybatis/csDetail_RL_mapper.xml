<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 保全查询	保单贷款续贷 -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="csDetailRL_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT A.*,
			       T.TYPE,/*证件类型*/
			       G.GENDER_DESC/*性别*/
			  FROM (SELECT A.OLD_CUSTOMER_ID,/*客户号码*/
			               '投保人' CUSTOMER_ROLE,/*角色*/
			               A.CUSTOMER_NAME,/*姓名*/
			               A.CUSTOMER_CERT_TYPE,/*证件类型代码*/
			               A.CUSTOMER_CERTI_CODE,/*证件号码*/
			               A.CUSTOMER_GENDER,/*性别代码*/
			               A.CUSTOMER_BIRTHDAY,/*出生日期*/
			               H.POLICY_CODE/*代码编号*/
			          FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
			UNION 
			 SELECT A.*,
			       T.TYPE,
			       G.GENDER_DESC
			  FROM (SELECT A.OLD_CUSTOMER_ID,
			               '被保人' CUSTOMER_ROLE,
			               A.CUSTOMER_NAME,
			               A.CUSTOMER_CERT_TYPE,
			               A.CUSTOMER_CERTI_CODE,
			               A.CUSTOMER_GENDER,
			               A.CUSTOMER_BIRTHDAY,
			               H.POLICY_CODE
			          FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="csDetailRL_Page_policy" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT A.BUSI_PROD_CODE, /*险种代码*/
		B.PRODUCT_NAME_SYS,/*险种名称*/
		C.AMOUNT, /*保额*/
		C.STD_PREM_AF /*保费*/
		FROM
		DEV_PAS.T_CONTRACT_BUSI_PROD A
		LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT B
		ON A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
		LEFT JOIN
		DEV_PAS.T_CONTRACT_PRODUCT C
		ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
		WHERE
		A.POLICY_CODE = #{accept_status}
		]]>
	</select>
	<select id="csDetailRL_Page_Loan" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(T.CAPITAL_BALANCE) CAPITAL_BALANCE, /*续贷金额*/
			       MAX(T.LOAN_START_DATE) LOAN_START_DATE, /*续贷日期*/
			       MAX(TO_CHAR(ADD_MONTHS(T.LOAN_START_DATE, 6) - 1, 'YYYY-MM-DD')) LOAN_END_DATE, /*续贷到期日期*/
			       SUM(T.INTEREST_BALANCE) INTEREST_BALANCE, /*续贷到期利息*/
			       MAX(T.IS_AUTO_RENEW) IS_AUTO_RENEW, /*是否自动续贷*/
			       MAX(T.IS_AUTHORITY) IS_AUTHORITY, /*是否已进行授权*/
			       MAX(PC.HESITATE_FLAG) HESITATE_FLAG /*是否在犹豫期*/
			  FROM DEV_PAS.T_CS_POLICY_ACCOUNT_STREAM T, DEV_PAS.T_CS_POLICY_CHANGE PC
			 WHERE T.CHANGE_ID = PC.CHANGE_ID
			   AND T.OLD_NEW = '1' AND T.REGULAR_REPAY=0
			   AND T.POLICY_ID = #{related_id}
			   AND T.CHANGE_ID = #{change_id}
		]]>
	</select>
	<select id="csDetailRL_Page_Bef_Loan" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT *
		  FROM (SELECT T.CHANGE_ID,
		               
		               MAX(CASE
		                     WHEN T.OLD_NEW = '0' and t.operation_type = '0' THEN
		                      T.Capital_Balance
		                   END) CAPITAL_BALANCE, /*贷款本金*/
		               MAX(CASE
		                     WHEN T.OLD_NEW = '0' and t.operation_type = '0' THEN
		                      T.LOAN_START_DATE
		                   END) LOAN_START_DATE, /*贷款开始日期*/
		               MAX(CASE
		                     WHEN T.OLD_NEW = '0' and t.operation_type = '0' THEN
		                      TO_CHAR(ADD_MONTHS(T.LOAN_START_DATE, 6) - 1, 'YYYY-MM-DD')
		                   END) LOAN_END_DATE, /*贷款到期日期*/
		               MAX(CASE
		                     WHEN T.OLD_NEW = '0' and t.operation_type = '0' THEN
		                      T.INTEREST_BALANCE
		                   END) INTEREST_BALANCE, /*贷款到期利息*/
		               MAX(CASE
		                     WHEN T.OLD_NEW = '1' and t.operation_type = '2' THEN
		                      T.INTEREST_CAPITAL
		                   
		                   END) INTEREST_CAPITAL, /*清偿金额合计*/
		               MAX(CASE
		                     WHEN T.OLD_NEW = '1' and t.operation_type = '2' THEN
		                      T.INTEREST_SUM
		                   
		                   END) INTEREST_SUM, /*清偿利息合计*/
	                   MAX(CASE
                         	WHEN T.OLD_NEW = '0' and t.operation_type = '0' THEN
                          	T.REPAY_DUE_DATE
                       
                       	   END) REPAY_DUE_DATE /*清偿日期*/
		        
		          FROM DEV_PAS.T_CS_POLICY_ACCOUNT_STREAM T
		         WHERE T.POLICY_ID = #{related_id}
		              --and t.regular_repay = '0'
		           and t.change_id = #{change_id}
		        -- and t.old_new = '0'
		         GROUP BY T.CHANGE_ID
		         ORDER BY LOAN_START_DATE DESC) A
		 WHERE ROWNUM = 1
		]]>
	</select>
	<!-- SELECT T.CHANGE_ID,
			       MAX(T.CAPITAL_BALANCE) CAPITAL_BALANCE,/*贷款本金*/
			       MAX(T.LOAN_START_DATE) LOAN_START_DATE,/*贷款开始日期*/
			       MAX(TO_CHAR(ADD_MONTHS(T.LOAN_START_DATE, 6) - 1, 'YYYY-MM-DD')) LOAN_END_DATE,/*贷款到期日期*/
			       MAX(T.INTEREST_BALANCE) INTEREST_BALANCE,/*贷款到期利息*/
			       MAX(CASE
			             WHEN T.OLD_NEW = '1' THEN
			              T.INTEREST_CAPITAL
			             ELSE
			              0
			           END) INTEREST_CAPITAL,/*清偿金额合计*/
			       MAX(T.INTEREST_SUM) INTEREST_SUM,/*清偿利息合计*/
			       MAX(CASE
			             WHEN T.OLD_NEW = '1' THEN
			              T.REPAY_DUE_DATE
			           END) REPAY_DUE_DATE/*清偿日期*/
			  FROM DEV_PAS.T_CS_POLICY_ACCOUNT_STREAM T,
			       DEV_PAS.T_CS_CONTRACT_MASTER       M,
			       DEV_PAS.T_CS_ACCEPT_CHANGE         C
			 WHERE T.POLICY_ID = M.POLICY_ID
			   AND T.CHANGE_ID = C.CHANGE_ID
			   AND M.POLICY_CODE = #{accept_status}
			   AND C.ACCEPT_CODE = #{accept_code}
			 GROUP BY T.CHANGE_ID -->
	
	<select id="csDetailRL_infoMsg" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT PC.CHANGE_ID, /*受理号*/
			       /*AC.CHANGE_ID, 受理号*/
			       AC.ACCEPT_ID, /*保全变更ID*/
			       AC.ACCEPT_CODE, /*保全变更号*/
			       PC.POLICY_ID, /*保单变更ID*/
			       PC.POLICY_CODE, /*保单变更号*/
			       PC.SERVICE_CODE /*保全项代码*/
			  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
			 WHERE AC.CHANGE_ID = PC.CHANGE_ID
			   AND AC.ACCEPT_CODE = #{accept_code}
		]]>
	</select>
	
		<select id="csDetailRL_Page_Returns" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			        SELECT CBP.BUSI_PRD_ID,
                CBP.BUSI_PROD_CODE, /*险种代码*/
                BP.PRODUCT_NAME_SYS, /*险种名称*/
                (T.TYPE_NAME) TNAME, /*费用类型*/
                (W.TYPE_NAME) WNAME, /*费用名称*/
                TS.STAND_FEE_AMOUNT, /*调整前解约退费_退费合计金额*/
                TS.ADJUST_FEE_AMOUNT /*调整后解约退费*/
           FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
           LEFT JOIN　DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP
             ON AC.CHANGE_ID = CBP.CHANGE_ID
           LEFT JOIN (SELECT *
                        FROM DEV_PAS.T_SURRENDER
                       WHERE ACCEPT_CODE = #{accept_code}) TS /*退保记录表*/
             ON CBP.CHANGE_ID = TS.CHANGE_ID
            AND CBP.BUSI_ITEM_ID = TS.BUSI_ITEM_ID
           LEFT JOIN DEV_CAP.V_PREM_ARAP PA
             ON PA.POLICY_CODE = CBP.POLICY_CODE
            AND CBP.BUSI_PROD_CODE = PA.BUSI_PROD_CODE
            AND TS.ACCEPT_CODE = PA.BUSINESS_CODE
           LEFT JOIN DEV_PAS.T_FEE_TYPE T
             ON PA.FEE_TYPE = T.CODE
           LEFT JOIN DEV_PAS.T_WITHDRAW_TYPE W
             ON PA.WITHDRAW_TYPE = W.TYPE_CODE
           LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON BP.PRODUCT_CODE_SYS=CBP.BUSI_PROD_CODE
          WHERE AC.ACCEPT_CODE = #{accept_code}
            AND CBP.OLD_NEW = '0'
		]]>
	</select>
	
	<select id="csDetailRL_Page_polRets" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT X.FEE_AMOUNT 
		  FROM DEV_PAS.T_CS_POLICY_CHANGE X
		 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status}
           AND AC.CHANGE_ID=#{change_id}
           )
         ]]>
	</select>
	
	<select id="csDetailRL_Page_poMakes" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			 SELECT CBP.BUSI_PRD_ID,
                CBP.BUSI_PROD_CODE, /*险种代码*/
                BP.PRODUCT_NAME_SYS, /*险种名称*/
                SUM(TS.STAND_FEE_AMOUNT) STAND_FEE_AMOUNT, /*调整前解约退费_退费合计金额*/
                SUM (TS.ADJUST_FEE_AMOUNT) ADJUST_FEE_AMOUNT/*调整后解约退费*/
           FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
           LEFT JOIN　DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP
             ON AC.CHANGE_ID = CBP.CHANGE_ID
           LEFT JOIN (SELECT *
                        FROM DEV_PAS.T_SURRENDER
                       WHERE ACCEPT_CODE = #{accept_code}) TS /*退保记录表*/
             ON CBP.CHANGE_ID = TS.CHANGE_ID
            AND CBP.BUSI_ITEM_ID = TS.BUSI_ITEM_ID
           LEFT JOIN DEV_CAP.V_PREM_ARAP PA
             ON PA.POLICY_CODE = CBP.POLICY_CODE
            AND CBP.BUSI_PROD_CODE = PA.BUSI_PROD_CODE
            AND TS.ACCEPT_CODE = PA.BUSINESS_CODE
           LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON BP.PRODUCT_CODE_SYS=CBP.BUSI_PROD_CODE
          WHERE AC.ACCEPT_CODE = #{accept_code}
            AND CBP.OLD_NEW = '0'
            GROUP BY CBP.BUSI_PRD_ID,CBP.BUSI_PROD_CODE,BP.PRODUCT_NAME_SYS
         ]]>
	</select>
    
     
    <select id="csDetailRL_Page_callTel" resultType="java.util.Map" parameterType="java.util.Map"  >
        <![CDATA[
             SELECT 
                A.MOBILE_TEL,
                A.BOJ_CALL_TEL
            FROM DEV_PAS.T_BIZ_CALL_OBJECT A
            INNER JOIN DEV_PAS.T_BIZ_CALL B
              ON A.OBJ_ID = B.BIZ_CALL_ID
            AND b.biz_code=#{accept_code}
         ]]>
    </select>
</mapper>