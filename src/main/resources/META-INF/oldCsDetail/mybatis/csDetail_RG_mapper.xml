<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询  生存领取追回mass_wb -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 查询被保险人信息 -->
	<select id="QRY_RG_find_Types_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.OLD_CUSTOMER_ID,     /*被保人号*/
       B.CUSTOMER_NAME,       /*姓名*/
       B.CUSTOMER_GENDER,     /*性别代码*/
       G.GENDER_DESC,         /*性别*/
       B.CUSTOMER_BIRTHDAY,   /*出生日期*/
       B.CUSTOMER_CERT_TYPE,  /*证件类型代码*/
       T.TYPE,                /*证件类型*/
       B.CUSTOMER_CERTI_CODE, /*证件号码*/
       B.DEATH_DATE           /*死亡日期*/
  FROM DEV_PAS.T_INSURED_LIST A
  LEFT JOIN DEV_PAS.T_CUSTOMER B
    ON A.CUSTOMER_ID = B.CUSTOMER_ID /*性别*/
  LEFT JOIN DEV_PAS.T_GENDER G
    ON B.CUSTOMER_GENDER = G.GENDER_CODE
  LEFT JOIN DEV_PAS.T_CERTI_TYPE T  /*证件类型*/
    ON B.B.CUSTOMER_CERT_TYPE = T.CODE
 WHERE A.POLICY_CODE = #{policy_code}
		]]>
	</select>
    <select id="QRY_RG_find_Return_Info" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT 
       PP.LIAB_ID,           /*保障责任ID*/
       PP.LIAB_CODE,         /*保障责任代码*/
       L.LIAB_NAME,          /*保障责任名称*/
       PD.FEE_AMOUNT,        /*定期给付金额*/ 
       PD.PAY_DUE_DATE,      /*本次领取日期*/
       AP.UNIT_NUMBER,       /*实付收据号*/
       AP.DUE_TIME  
     FROM DEV_PAS.T_CS_ACCEPT_CHANGE  ACC 
     LEFT JOIN DEV_CAP.V_PREM_ARAP AP
     ON ACC.ACCEPT_CODE = AP.BUSINESS_CODE
     LEFT JOIN  DEV_PAS.T_PAY_PLAN PP  ON PP.POLICY_CODE=AP.POLICY_CODE AND PP.BUSI_PROD_CODE=AP.BUSI_PROD_CODE/*险种生存给付计划表*/ 
  LEFT JOIN DEV_PAS.T_PAY_DUE  PD ON PD.PLAN_ID=PP.PLAN_ID AND PP.BUSI_ITEM_ID=PD.BUSI_ITEM_ID
  LEFT JOIN DEV_PAS.T_LIABILITY L ON L.LIAB_ID=PP.LIAB_ID
     WHERE ACC.CHANGE_ID = 
     (SELECT CHA.POLICY_ID    /*保单ID*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID
       AND CHA.POLICY_CODE = #{policy_code}
       AND ACC.ACCEPT_CODE = #{accept_code})
        ]]>
    </select>
    <!-- 查询 领取追回信息：追回原因-追回至日期-金额总计 -->
    <select id="QRY_RG_find_TakeBack_Info" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT 
      PR.RETRIEVE_CAUSE,      /*追回原因代码*/
      (SELECT CC.RETRIEVE_CAUSE_NAME FROM DEV_PAS.T_CS_RETRIEVE_CAUSE CC 
      WHERE CC.RETRIEVE_CAUSE_CODE = PR.RETRIEVE_CAUSE ) RETRIEVE_CAUSE_NAME,/*追回原因*/
      RETRIEVE_DATE,       /*追至日期*/
      RETRIEVE_PREM        /*追回金额*/
 FROM DEV_PAS.T_POLICY_RETRIEVE PR     /*生存金追回表*/
 WHERE PR.CHANGE_ID = 
 (  SELECT
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
        AND CHA.POLICY_CODE = #{policy_code}
        AND ACC.ACCEPT_CODE = #{accept_code})
        ]]>
    </select>
    <!-- 查询保单险种信息 -->
    <select id="QRY_RG_find_Types_Take_Info" resultType="java.util.Map"
        parameterType="java.util.Map"  >
        <![CDATA[   SELECT 
       T.BUSI_PROD_CODE,        /*险种代码*/
       B.PRODUCT_NAME_SYS,       /*险种名称*/
       T.VALIDATE_DATE,           /*生效日期*/
       C.STD_PREM_AF,             /*保费*/
       E.PAY_DUE_DATE               /*交至日期*/
       FROM  
           DEV_PAS.T_CONTRACT_BUSI_PROD T /*险种表*/
 LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT B  /*业务产品*/
      ON   T.BUSI_PRD_ID = B.BUSINESS_PRD_ID
 LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT C   /*保单险种责任组表*/
      ON T.POLICY_CODE = C.POLICY_CODE
 LEFT JOIN DEV_PAS.T_CONTRACT_EXTEND  E   /*保单应缴日表*/
      ON E.POLICY_CODE = T.POLICY_CODE
      WHERE C.POLICY_CODE = #{policy_code}
         ]]>
    </select>
</mapper>