<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="daohang_Page_Customer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT  CAC.ACCEPT_CODE,/*受理号*/
		      CA.NOFILL_FLAG,/*是否免填单*/
		       /*是否预录入*/
		       CPC.POLICY_CODE, /*保单号*/
		       /*号码类型*/
		        /*批改状态*/
		       CA.APPLY_NAME,/*申请人姓名*/
		      ST.TYPE_NAME,/*申请方式*/
		      CA.APPLY_TIME/*保全申请时间*/
		      FROM DEV_PAS.T_CS_APPLICATION CA/*保全申请信息表*/
		      LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC/*受理变更管理表*/
		      ON CA.CHANGE_ID=CAC.CHANGE_ID
		      LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC /*保单变更管理表*/
		       ON CAC.ACCEPT_ID = CPC.ACCEPT_ID
		       LEFT JOIN  DEV_PAS.T_SERVICE_TYPE ST/*申请方式定义表*/
		       ON CA.SERVICE_TYPE=ST.SERVICE_TYPE
		      WHERE CA.APPLY_CODE=#{apply_code}
		]]> 
	</select>
	<select id="xian_Page_Customer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			      SELECT 
		       		 CBP.BUSI_PROD_CODE,/**险种代码*/
				    BP.PRODUCT_NAME_SYS,/**险种名称*/
				    TC.CUSTOMER_NAME,/**姓名*/
				     CP.AMOUNT,/**基本保额*/
		        	  CP.UNIT,/*份数*/
				    CP.STD_PREM_AF,/**保费标准*/
				     (SELECT A.EXTRA_PARA FROM DEV_PAS.T_EXTRA_PREM A/**险种责任组加费表*/
					  WHERE A.APPLY_CODE=#{apply_code} AND A.EXTRA_TYPE='1') EXTRA_PARA1,/*健康加费*/
					 (SELECT A.EXTRA_PARA FROM DEV_PAS.T_EXTRA_PREM A 
					 WHERE A.APPLY_CODE=#{apply_code} AND A.EXTRA_TYPE='2') EXTRA_PARA2 /*职业加费*/
		        /*交至对应日*/
				    FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
				    DEV_PAS.T_BUSINESS_PRODUCT BP,
				    DEV_PAS.T_CS_CONTRACT_PRODUCT CP,
				    DEV_PAS.T_CUSTOMER TC,
				    DEV_PAS.T_INSURED_LIST IL    
				    WHERE BP.PRODUCT_CODE_SYS=CBP.BUSI_PROD_CODE
				    AND CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
				    AND TC.CUSTOMER_ID = IL.CUSTOMER_ID 
				    AND IL.POLICY_ID=CP.POLICY_ID
		        AND CP.APPLY_CODE= #{apply_code}
		]]> 
	</select>
</mapper>