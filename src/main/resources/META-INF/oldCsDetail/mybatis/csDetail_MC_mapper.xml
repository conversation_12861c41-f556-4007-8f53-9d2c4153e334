<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询  基本信息变更备注项mass_wb -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
    <!-- 查询投保人与被保人已用id：QRY_TB_find_Info -->
	<!-- 查询基本备注 -->
	<select id="QRY_MC_find_Remark_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT nvl2(b.BASIC_REMARK,b.basic_remark,M.BASIC_REMARK) as BASIC_REMARK
  FROM DEV_PAS.T_CONTRACT_MASTER M
  LEFT JOIN (SELECT CM.BASIC_REMARK, CM.POLICY_CODE
               FROM DEV_PAS.T_CS_CONTRACT_MASTER CM
              INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CC
                 ON CM.CHANGE_ID = CC.CHANGE_ID
              WHERE CM.POLICY_CODE = #{policy_code}
                AND CC.ACCEPT_CODE = #{accept_code}
                AND CM.OLD_NEW = 1
                AND CC.ACCEPT_STATUS = '18') B
    ON M.POLICY_CODE = B.POLICY_CODE
 WHERE M.POLICY_CODE = #{policy_code}
		]]>
	</select>
</mapper>