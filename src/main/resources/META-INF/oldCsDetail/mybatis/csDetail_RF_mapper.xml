<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 保全查询 保单贷款清偿 -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="csDetailRF_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT A.*,
			       T.TYPE,/*证件类型*/
			       G.GENDER_DESC/*性别*/
			  FROM (SELECT A.OLD_CUSTOMER_ID,/*客户号码*/
			               '投保人' CUSTOMER_ROLE,/*角色*/
			               A.CUSTOMER_NAME,/*姓名*/
			               A.CUSTOMER_CERT_TYPE,/*证件类型代码*/
			               A.CUSTOMER_CERTI_CODE,/*证件号码*/
			               A.CUSTOMER_GENDER,/*性别代码*/
			               A.CUSTOMER_BIRTHDAY,/*出生日期*/
			               H.POLICY_CODE/*代码编号*/
			          FROM DEV_PAS.T_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
			UNION 
			 SELECT A.*,
			       T.TYPE,
			       G.GENDER_DESC
			  FROM (SELECT A.OLD_CUSTOMER_ID,
			               '被保人' CUSTOMER_ROLE,
			               A.CUSTOMER_NAME,
			               A.CUSTOMER_CERT_TYPE,
			               A.CUSTOMER_CERTI_CODE,
			               A.CUSTOMER_GENDER,
			               A.CUSTOMER_BIRTHDAY,
			               H.POLICY_CODE
			          FROM DEV_PAS.T_INSURED_LIST H, DEV_PAS.T_CUSTOMER A
			         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
			           AND H.POLICY_CODE = #{accept_status}) A,
			       DEV_PAS.T_CERTI_TYPE T,
			       DEV_PAS.T_GENDER G
			 WHERE A.CUSTOMER_CERT_TYPE = T.CODE
			   AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	<select id="csDetailRF_Page_Loan" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT T.CHANGE_ID,
			      MAX(T.CAPITAL_BALANCE) CAPITAL_BALANCE, /*贷款金额*/
			      MAX(T.LOAN_START_DATE) LOAN_START_DATE, /*贷款日期*/
			      MAX(TO_CHAR(ADD_MONTHS(T.LOAN_START_DATE, 6) - 1, 'YYYY-MM-DD')) LOAN_END_DATE, /*贷款到期日期*/
			      MAX(T.INTEREST_BALANCE) INTEREST_BALANCE, /*贷款到期利息合计*/
			      MAX(CASE
			            WHEN T.OLD_NEW = '1' THEN
			             T.INTEREST_CAPITAL
			            ELSE
			             0
			          END) INTEREST_CAPITAL, /*清偿金额合计*/
			      MAX(T.INTEREST_SUM) INTEREST_SUM, /*清偿利息合计*/
			      MAX(CASE
			            WHEN T.OLD_NEW = '1' THEN
			             T.REPAY_DUE_DATE
			          END) REPAY_DUE_DATE /*还款日期*/
			 FROM DEV_PAS.T_CS_POLICY_ACCOUNT_STREAM T
			WHERE T.POLICY_ID = #{policy_id}
			  AND T.CHANGE_ID = #{change_id}
			GROUP BY T.CHANGE_ID
		]]>
	</select>
</mapper>