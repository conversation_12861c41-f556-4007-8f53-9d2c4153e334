<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="tkehuPU_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
  		     SELECT A.CUSTOMER_NAME, /*姓名*/
                   A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                   A.CUSTOMER_CERTI_CODE, /*证件号码*/
                   T.TYPE/*证件类型*/
              FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
              DEV_PAS.T_CUSTOMER A,/**客户表*/
              DEV_PAS.T_CERTI_TYPE T
             WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
             AND A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
             AND H.POLICY_CODE = #{accept_status}
         ]]>
	</select>
	<select id="bkehuPU_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
  		     SELECT A.CUSTOMER_NAME, /*姓名*/
                  A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
                  A.CUSTOMER_CERTI_CODE, /*证件号码*/
                  T.TYPE/*证件类型*/
             FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
             DEV_PAS.T_CUSTOMER A,/**客户表*/
             DEV_PAS.T_CERTI_TYPE T
            WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
            AND A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
            AND H.POLICY_CODE = #{accept_status}
         ]]>
	</select>
	<select id="xianPU_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
  		     SELECT CBP.BUSI_PROD_CODE,/**险种代码*/
			BP.PRODUCT_NAME_SYS,/**险种名称*/
			CE.PAY_DUE_DATE,/**交费对应日*/
			CP.AMOUNT,/**基本保额*/
			CP.UNIT,/**份数*/
			CP.STD_PREM_AF/**保费标准*/
			 FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP,/**险种表*/
			DEV_PAS.T_BUSINESS_PRODUCT BP,/**业务产品*/
			DEV_PAS.T_CS_CONTRACT_EXTEND CE,/**险种下期缴费计划表*/
			DEV_PAS.T_CS_CONTRACT_PRODUCT CP/**险种责任组表*/
			 WHERE CBP.OLD_NEW='0'
			      AND CE.OLD_NEW='0'
			     AND CP.OLD_NEW='0'
			       AND BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID
			       AND CE.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
			       AND CP.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
			       AND CBP.CHANGE_ID= (SELECT AC.CHANGE_ID
			      FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
			     WHERE AC.CHANGE_ID = PC.CHANGE_ID
			       AND AC.ACCEPT_CODE = #{accept_code}
					   AND PC.POLICY_CODE =#{accept_status})
			       GROUP BY BUSI_PROD_CODE,BP.PRODUCT_NAME_SYS,CE.PAY_DUE_DATE,CP.AMOUNT,CP.UNIT,CP.STD_PREM_AF
         ]]>
	</select>
	<select id="jianPU_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
			SELECT CBP.BUSI_PROD_CODE,/**险种代码*/
			BP.PRODUCT_NAME_SYS,/**险种名称*/
			CP.AMOUNT,/**基本保额*/
			CP.UNIT/**份数*/
			 FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP,/**险种表*/
			DEV_PAS.T_BUSINESS_PRODUCT BP,/**业务产品*/
			DEV_PAS.T_CS_CONTRACT_PRODUCT CP/**险种责任组表*/
			 WHERE CBP.OLD_NEW='1'
		     AND CP.OLD_NEW='1'
		       AND BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID
		       --AND CE.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
		       AND CP.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
		       AND CBP.CHANGE_ID= (SELECT AC.CHANGE_ID
		      FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		     WHERE AC.CHANGE_ID = PC.CHANGE_ID
		       AND AC.ACCEPT_CODE = #{accept_code}
				   AND PC.POLICY_CODE =#{accept_status})
		       GROUP BY BUSI_PROD_CODE,BP.PRODUCT_NAME_SYS,CP.AMOUNT,CP.UNIT,CP.STD_PREM_AF
        ]]>
	</select>
</mapper>