<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询保单迁移mass_wb-->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 保单迁移查询投保人 -->
	<select id="QRY_find_Applicant_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
        D.CUSTOMER_NAME,/*投保人人姓名*/
        D.CUSTOMER_CERT_TYPE,/*证件类型*/
        T.TYPE,
        D.CUSTOMER_CERTI_CODE/*证件号*/
   FROM  DEV_PAS.T_POLICY_HOLDER C
   LEFT JOIN  DEV_PAS.T_CUSTOMER D
     ON C.CUSTOMER_ID = D.CUSTOMER_ID
   LEFT JOIN DEV_PAS.T_CERTI_TYPE T 
     ON D.CUSTOMER_CERT_TYPE=T.CODE 
  WHERE C.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 保单迁移查询被保人 -->
	<select id="QRY_find_Recognizee_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
        D.CUSTOMER_NAME,/*被保人姓名*/
        D.CUSTOMER_CERT_TYPE,/*证件类型*/
        T.TYPE,
        D.CUSTOMER_CERTI_CODE/*证件号*/
   FROM DEV_PAS.T_INSURED_LIST C
   LEFT JOIN  DEV_PAS.T_CUSTOMER D
     ON C.CUSTOMER_ID = D.CUSTOMER_ID
   LEFT JOIN DEV_PAS.T_CERTI_TYPE T 
     ON D.CUSTOMER_CERT_TYPE=T.CODE 
  WHERE C.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 原管理机构 -->
	<select id="QRY_find_Raw_Manage_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
     SELECT 
     M.ORGAN_CODE,/*原管理机构*/
     (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = M.ORGAN_CODE) ORGAN_NAME
     FROM DEV_PAS.T_CS_CONTRACT_MASTER M WHERE M.POLICY_CODE = #{policy_code}
     AND M.CHANGE_ID = ( 
     SELECT
           ACC.CHANGE_ID/*受理号*/
      FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/ 
           DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/ 
     WHERE CHA.CHANGE_ID = ACC.CHANGE_ID 
       AND ACC.ACCEPT_CODE = #{accept_code}
       AND CHA.POLICY_CODE = #{policy_code}) 
     AND M.OLD_NEW='0'
		]]>
	</select>
	<!-- 迁移至管理机构 -->
	<select id="QRY_find_Migration_Manage_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 
      M.ORGAN_CODE,/*迁移至管理机构*/ 
      (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = M.ORGAN_CODE) ORGAN_NAME
      FROM DEV_PAS.T_CONTRACT_MASTER M WHERE M.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 保单险种信息 -->
	<select id="QRY_Types_Insurance_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT 
（SELECT OLD_CUSTOMER_ID
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID） OLD_CUSTOMER_ID, /*客户ID*//*被保人号码*/ 
  （SELECT CUSTOMER_NAME
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID） CUSTOMER_NAME, /*姓名*/
 B.BUSI_PROD_CODE, /*险种代码*/
 (SELECT PRODUCT_NAME_SYS
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
 P.STD_PREM_AF,/*保费标准*/                                                                                     /*保费标准*/
 P.AMOUNT,/*基本保额*/
 B.VALIDATE_DATE /*生效日期*/
 FROM DEV_PAS.T_CONTRACT_PRODUCT P, DEV_PAS.T_CONTRACT_BUSI_PROD B,
 DEV_PAS.T_INSURED_LIST L
 WHERE P.POLICY_CODE = B.POLICY_CODE
   AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
   AND L.POLICY_CODE = P.POLICY_CODE
   AND P.POLICY_CODE = #{policy_code}
 GROUP BY L.CUSTOMER_ID,L.UPDATE_TIME,B.BUSI_PROD_CODE,b.Validate_Date,P.STD_PREM_AF,P.AMOUNT
		]]>
	</select>
</mapper>