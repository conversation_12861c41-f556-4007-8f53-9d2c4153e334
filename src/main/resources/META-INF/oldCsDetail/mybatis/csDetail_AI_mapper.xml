<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">

<!-- 查询客户下保险账户信息 -->
	 <select id="kehuAI_Page_Customer" resultType="java.util.Map" parameterType="java.util.Map"  >
		<![CDATA[
		SELECT CM.POLICY_CODE,
		       CP.PRODUCT_CODE,
		       ACT.ACCOUNT_NAME,
		       PL.INTERNAL_ID,
		       PL.PRODUCT_NAME,
		       PA.INTEREST_CAPITAL,
		       (SELECT SUM(ATL.TRANS_AMOUNT)
		          		FROM  DEV_PAS.V_POL_ACC_TRANS_LIST_ALL ATL            
		         WHERE ATL.ACCOUNT_ID = PA.ACCOUNT_ID AND ATL.TRANS_TIME = B.VALIDATE_TIME         
		        ) AS TRANS_AMOUNT
	  	FROM DEV_PAS.T_POLICY_ACCOUNT PA
	  	INNER JOIN 
	        (SELECT PC.POLICY_ID,AC.ACCEPT_TIME,AC.VALIDATE_TIME
	          FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
	         WHERE AC.CHANGE_ID = PC.CHANGE_ID
	           AND AC.ACCEPT_CODE = #{accept_code}) B 
	      ON PA.POLICY_ID = B.POLICY_ID
	  	LEFT JOIN DEV_PAS.T_POLICY_ACCOUNT_TYPE ACT
	    	ON PA.ACCOUNT_TYPE = ACT.ACCOUNT_TYPE
	  	LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
	    	ON CP.ITEM_ID = PA.ITEM_ID
	  	LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PL
	    	ON CP.PRODUCT_CODE = PL.INTERNAL_ID
	  	LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
	    	ON CM.POLICY_ID = PA.POLICY_ID
	 	WHERE ACT.ACCOUNT_TYPE = '11' 			
			
         ]]>
	</select>
	
	
<!-- 查询回访电话录入 -->
	<select id="callBackAI_Page_Customer" resultType="java.util.Map" parameterType="java.util.Map"  >
	
		<![CDATA[
		
			select 
			bc.biz_code,co.mobile_tel,co.boj_call_tel,bc.biz_source
			from dev_pas.t_biz_call bc
			inner join dev_pas.t_biz_call_object co on bc.biz_call_id=co.biz_call_id
			where bc.biz_source='004' and bc.biz_code=#{accept_code}
						
		
         ]]>
		
	</select>







	
	
<!-- 查询补退费金额合计 -->	
	<select id="btfAI_Page_Customer" resultType="java.util.Map" parameterType="java.util.Map"  >
		<![CDATA[
	          		SELECT X.FEE_AMOUNT 
				  FROM DEV_PAS.T_CS_POLICY_CHANGE X
				 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
				  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
				 WHERE AC.CHANGE_ID = PC.CHANGE_ID
				   AND AC.ACCEPT_CODE = #{accept_code}
				   AND PC.POLICY_CODE = #{accept_status})
	         ]]>
	</select> 
	
	
</mapper>