<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="lnsurAP_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
           SELECT B.BUSI_PROD_CODE,/**现在代码(险种代码)*/
     	   B.OLD_POL_NO,/**老核心险种号*/
           L.INSURED_AGE,/**投保年龄*/
           P.PRODUCT_NAME_SYS/**险种名称*/
           FROM DEV_PAS.T_CONTRACT_BUSI_PROD B,
           DEV_PAS.T_BUSINESS_PRODUCT P,
           DEV_PAS.T_INSURED_LIST L
           WHERE B.POLICY_CODE = #{accept_status}	          
           AND L.POLICY_ID = B.POLICY_ID 
           AND B.BUSI_PROD_CODE=P.PRODUCT_CODE_SYS
         ]]>
	</select>
	<select id="policyAP_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
           SELECT CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER T ,DEV_PAS.T_POLICY_HOLDER P
     	   WHERE T.CUSTOMER_ID=P.CUSTOMER_ID
           AND POLICY_CODE=#{accept_status}
         ]]>
	</select>
	<select id="recognAP_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map"  >
		<![CDATA[
          	SELECT CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER T ,DEV_PAS.T_INSURED_LIST I
     	    WHERE T.CUSTOMER_ID=I.CUSTOMER_ID
      		AND POLICY_CODE=#{accept_status}
         ]]>
	</select>
</mapper>