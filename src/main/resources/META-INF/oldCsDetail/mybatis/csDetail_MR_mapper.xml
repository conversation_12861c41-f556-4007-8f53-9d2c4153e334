<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="kehuMR_Page_Customer" resultType="java.util.Map" parameterType="java.util.Map"  >
		<![CDATA[
			SELECT A.*, T.TYPE, /*证件类型*/
	        			G.GENDER_DESC /*性别*/
	        FROM (SELECT A.OLD_CUSTOMER_ID, /*客户号码*/
	                       '投保人' CUSTOMER_ROLE, /*角色*/
	                       A.CUSTOMER_NAME, /*姓名*/
	                       A.CUSTOMER_CERT_TYPE, /*证件类型代码*/
	                       A.CUSTOMER_CERTI_CODE, /*证件号码*/
	                       A.CUSTOMER_GENDER, /*性别代码*/
	                       A.CUSTOMER_BIRTHDAY /*出生日期*/
	        FROM DEV_PAS.T_POLICY_HOLDER H, /**保单投保人列表*/
	        DEV_PAS.T_CUSTOMER A/**客户表*/
	        WHERE H.CUSTOMER_ID = A.CUSTOMER_ID/**客户id*/
	        AND H.POLICY_CODE = #{accept_status}) A,
	        DEV_PAS.T_CERTI_TYPE T,
	        DEV_PAS.T_GENDER G/**性别代码表*/
	        WHERE A.CUSTOMER_CERT_TYPE = T.CODE/**证件类型*/
	        AND A.CUSTOMER_GENDER = G.GENDER_CODE/**客户性别*/
	        UNION
	        SELECT A.*, T.TYPE, G.GENDER_DESC
	        FROM (SELECT A.OLD_CUSTOMER_ID,
	                       '被保人' CUSTOMER_ROLE,
	                       A.CUSTOMER_NAME,
	                       A.CUSTOMER_CERT_TYPE,
	                       A.CUSTOMER_CERTI_CODE,
	                       A.CUSTOMER_GENDER,
	                       A.CUSTOMER_BIRTHDAY
	        FROM DEV_PAS.T_INSURED_LIST H,/**被保人表*/
	        DEV_PAS.T_CUSTOMER A/**客户表*/
	        WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
	        AND H.POLICY_CODE =#{accept_status}) A,
	        DEV_PAS.T_CERTI_TYPE T,
	        DEV_PAS.T_GENDER G
	        WHERE A.CUSTOMER_CERT_TYPE = T.CODE
	        AND A.CUSTOMER_GENDER = G.GENDER_CODE
         ]]>
	</select>
	
	<select id="xianzhongMR_Page_Customer" resultType="java.util.Map" parameterType="java.util.Map"  >
	<![CDATA[
			SELECT 
			(SELECT CUSTOMER_NAME
			 FROM DEV_PAS.T_CUSTOMER
			 WHERE CUSTOMER_ID = L.CUSTOMER_ID) CUSTOMER_NAME, /*姓名*/
			 B.BUSI_PROD_CODE, /*险种代码*/
			 (SELECT PRODUCT_NAME_SYS 
			 FROM DEV_PAS.T_BUSINESS_PRODUCT
			 WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
			 P.STD_PREM_AF,        /*保费标准*/
			 P.UNIT,/*份数*/
			 P.AMOUNT,/*基本保额*/
			 B.VALIDATE_DATE, /*生效日期*/
			 B.EXPIRY_DATE /*险种失效日期*/ 
			 FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P, DEV_PAS.T_CONTRACT_BUSI_PROD B,
			 DEV_PAS.T_INSURED_LIST L
			 WHERE P.POLICY_CODE = B.POLICY_CODE
			 AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			 AND L.POLICY_CODE = P.POLICY_CODE
			 AND P.POLICY_CODE =#{accept_status}
			 AND P.OLD_NEW='0'  /*1变更险种信息_0险种基本信息*/
			 GROUP BY L.CUSTOMER_ID,L.UPDATE_TIME,B.BUSI_PROD_CODE,B.VALIDATE_DATE,P.STD_PREM_AF,
			 P.AMOUNT,B.EXPIRY_DATE,P.UNIT
	]]>
	</select>
	
	
	<select id="xubaoMR_Page_Customer" resultType="java.util.Map" parameterType="java.util.Map"  >
	<![CDATA[
			SELECT 
			(SELECT CUSTOMER_NAME
			 FROM DEV_PAS.T_CUSTOMER
			 WHERE CUSTOMER_ID = L.CUSTOMER_ID) CUSTOMER_NAME, /*姓名*/
			 B.BUSI_PROD_CODE, /*险种代码*/
			 (SELECT PRODUCT_NAME_SYS 
			 FROM DEV_PAS.T_BUSINESS_PRODUCT
			 WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
			 P.STD_PREM_AF,         /*保费标准*/
			 P.UNIT,				/*份数*/
			 P.AMOUNT,				/*基本保额*/
			 B.VALIDATE_DATE, 		/*生效日期*/
			 B.EXPIRY_DATE 			/*险种失效日期*/ 
			 FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P, DEV_PAS.T_CONTRACT_BUSI_PROD B,
			 DEV_PAS.T_INSURED_LIST L
			 WHERE P.POLICY_CODE = B.POLICY_CODE
			 AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			 AND L.POLICY_CODE = P.POLICY_CODE
			 AND P.POLICY_CODE =#{accept_status}
			 AND P.OLD_NEW='1'	  /*1变更险种信息_0险种基本信息*/
			 GROUP BY L.CUSTOMER_ID,L.UPDATE_TIME,B.BUSI_PROD_CODE,B.VALIDATE_DATE,P.STD_PREM_AF,
			 P.AMOUNT,B.EXPIRY_DATE,P.UNIT
	]]>
	</select>
	
	
</mapper>