<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<select id="baodanFM_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT DISTINCT A.* FROM (SELECT
       CP.POLICY_CODE,
       CBP.BUSI_PROD_CODE,
       (SELECT BP.PRODUCT_NAME_SYS
          FROM DEV_PAS.T_BUSINESS_PRODUCT BP
         WHERE BP.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID ) PRODUCT_NAME_SYS,
       (SELECT A.CUSTOMER_NAME
          FROM DEV_PAS.T_POLICY_HOLDER H, 
               DEV_PAS.T_CUSTOMER A 
         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID
           AND H.POLICY_CODE = 'NJG10322651000221') HOLDER_NAME,
       (SELECT A.CUSTOMER_NAME
          FROM DEV_PAS.T_INSURED_LIST H,
               DEV_PAS.T_CUSTOMER A 
         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID 
           AND H.POLICY_CODE = 'NJG10322651000221') INSURED_NAME,
       (SELECT H.INSURED_AGE 
          FROM DEV_PAS.T_INSURED_LIST H,
               DEV_PAS.T_CUSTOMER A 
         WHERE H.CUSTOMER_ID = A.CUSTOMER_ID 
           AND H.POLICY_CODE = 'NJG10322651000221') INSURED_AGE,
       CP.STD_PREM_AF,
       CP.AMOUNT,
       CP.UNIT,
       CP.CHARGE_YEAR,
       (SELECT TCP.CHARGE_DESC
          FROM DEV_PAS.T_CHARGE_PERIOD TCP
         WHERE TCP.CHARGE_PERIOD = CP.CHARGE_PERIOD) CHARGE_TYPE
  FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP,
       DEV_PAS.T_CS_CONTRACT_PRODUCT CP,
       DEV_PAS.T_CS_ACCEPT_CHANGE AC
	 WHERE CBP.CHANGE_ID = CP.CHANGE_ID
	   AND CBP.OLD_NEW = '0'
	   AND CBP.CHANGE_ID = AC.CHANGE_ID
	   AND CP.OLD_NEW = '0'
	   AND AC.ACCEPT_CODE =  #{accept_code}
	   AND CBP.POLICY_CODE =#{accept_status}) A
         ]]>
	</select>
	<select id="biangengFM_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT A.* FROM (SELECT
	       CP.STD_PREM_AF,
	       CP.CHARGE_YEAR,
	       CP.CHARGE_PERIOD,
	       (SELECT TCP.CHARGE_DESC
	          FROM DEV_PAS.T_CHARGE_PERIOD TCP
	         WHERE TCP.CHARGE_PERIOD = CP.CHARGE_PERIOD) CHARGE_TYPE
	  FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD CBP,
	       DEV_PAS.T_CS_CONTRACT_PRODUCT CP,
	       DEV_PAS.T_CS_ACCEPT_CHANGE AC
		 WHERE CBP.CHANGE_ID = CP.CHANGE_ID
		   AND CBP.OLD_NEW = '0'
		   AND CBP.CHANGE_ID = AC.CHANGE_ID
		   AND CP.OLD_NEW = '1'
		   AND AC.ACCEPT_CODE =  #{accept_code}
		   AND CBP.POLICY_CODE = #{accept_status}) A
         ]]>
	</select>
	<select id="bufeiFM_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT X.FEE_AMOUNT 
		  FROM DEV_PAS.T_CS_POLICY_CHANGE X
		 WHERE X.CHANGE_ID = (SELECT AC.CHANGE_ID
		  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC, DEV_PAS.T_CS_POLICY_CHANGE PC
		 WHERE AC.CHANGE_ID = PC.CHANGE_ID
		   AND AC.ACCEPT_CODE = #{accept_code}
		   AND PC.POLICY_CODE = #{accept_status})
         ]]>
	</select>
	<select id="bujiaoFM_Page_Customer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			    SELECT C.FEE_AMOUNT, /**费用金额*/
		            T.TYPE_NAME,   /**费用类型*/
		            (W.TYPE_NAME) WNAME  /*费用名称*/
		       FROM DEV_PAS.v_prem_arap_all C,
		       DEV_PAS.T_FEE_TYPE T,
		       DEV_PAS.T_WITHDRAW_TYPE W
		       WHERE T.CODE=C.FEE_TYPE
		         AND C.WITHDRAW_TYPE=W.TYPE_CODE
		    AND C.POLICY_CODE=#{accept_status}
         ]]>
	</select>
</mapper>