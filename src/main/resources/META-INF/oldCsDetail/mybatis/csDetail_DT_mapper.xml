<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询附加特约责任终止mass_wb-->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 险种基本信息 -->
	<select id="QRY_DT_find_Types_old_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[           SELECT 
（SELECT OLD_CUSTOMER_ID
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID） OLD_CUSTOMER_ID, /*客户ID*//*被保人号码*/ 
（SELECT CUSTOMER_NAME
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID） CUSTOMER_NAME, /*姓名*/
 B.BUSI_PROD_CODE, /*险种代码*/
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
 P.STD_PREM_AF,        /*保费标准*/
 P.UNIT,/*份数*/
 P.AMOUNT,/*基本保额*/
 B.VALIDATE_DATE, /*生效日期*/
 B.EXPIRY_DATE /*险种失效日期*/ 
 FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P, DEV_PAS.T_CONTRACT_BUSI_PROD B,
 DEV_PAS.T_INSURED_LIST L
 WHERE P.POLICY_CODE = B.POLICY_CODE
   AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
   AND L.POLICY_CODE = P.POLICY_CODE
   AND P.POLICY_CODE =  #{policy_code} 
   AND P.OLD_NEW='0'  /*1变更险种信息_0险种基本信息*/
 GROUP BY L.CUSTOMER_ID,L.UPDATE_TIME,B.BUSI_PROD_CODE,B.VALIDATE_DATE,P.STD_PREM_AF,P.AMOUNT,B.EXPIRY_DATE,P.UNIT
		]]>
	</select>
	<!-- 变更险种信息 -->
	<select id="QRY_DT_find_Types_new_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[           SELECT 
（SELECT OLD_CUSTOMER_ID
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID） OLD_CUSTOMER_ID, /*客户ID*//*被保人号码*/ 
（SELECT CUSTOMER_NAME
  FROM DEV_PAS.T_CUSTOMER
 WHERE CUSTOMER_ID = L.CUSTOMER_ID） CUSTOMER_NAME, /*姓名*/
 B.BUSI_PROD_CODE, /*险种代码*/
 (SELECT PRODUCT_NAME_SYS 
          FROM DEV_PAS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
 P.STD_PREM_AF,        /*保费标准*/
 P.UNIT,/*份数*/
 P.AMOUNT,/*基本保额*/
 B.VALIDATE_DATE, /*生效日期*/
 B.EXPIRY_DATE /*险种失效日期*/ 
 FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P, DEV_PAS.T_CONTRACT_BUSI_PROD B,
 DEV_PAS.T_INSURED_LIST L
 WHERE P.POLICY_CODE = B.POLICY_CODE
   AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
   AND L.POLICY_CODE = P.POLICY_CODE
   AND P.POLICY_CODE =  #{policy_code} 
   AND P.OLD_NEW='1'  /*1变更险种信息_0险种基本信息*/
 GROUP BY L.CUSTOMER_ID,L.UPDATE_TIME,B.BUSI_PROD_CODE,B.VALIDATE_DATE,P.STD_PREM_AF,P.AMOUNT,B.EXPIRY_DATE,P.UNIT
		]]>
	</select>
</mapper>