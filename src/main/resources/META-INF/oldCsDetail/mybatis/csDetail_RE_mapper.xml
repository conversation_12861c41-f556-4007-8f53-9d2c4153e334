<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 综合查询保单复效mass_wb-->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 保单复效查询投保人 -->
	<select id="QRY_find_Reinastate_Applicant_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
		'1' test,
		B.OLD_CUSTOMER_ID,/*客户号码*/
       '投保人' CAST,/*角色*/
       B.CUSTOMER_NAME,/*客户姓名*/
       B.CUSTOMER_CERT_TYPE,/*证件*/
       T.TYPE,/*证件类型*/ 
       B.CUSTOMER_CERTI_CODE,/*证件号码*/ 
       B.CUSTOMER_GENDER,/*性别代码*/
       G.GENDER_DESC,/*性别*/
       B.CUSTOMER_BIRTHDAY/*出生日期*/
  FROM DEV_PAS.T_POLICY_HOLDER A
  LEFT JOIN DEV_PAS.T_CUSTOMER B
    ON A.CUSTOMER_ID = B.CUSTOMER_ID
  LEFT JOIN DEV_PAS.T_GENDER G
  ON B.CUSTOMER_GENDER=G.GENDER_CODE 
  LEFT JOIN DEV_PAS.T_CERTI_TYPE T/*关联证件号*/
     ON B.CUSTOMER_CERT_TYPE=T.CODE
 WHERE A.POLICY_CODE = #{policy_code}
	]]>
	</select>
	<!-- 查询被保人 -->
	<select id="QRY_find_Reinastate_Recognizee_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
		'2' test,
		D.OLD_CUSTOMER_ID,/*客户号码*/
       '被保人' QUILT,/*角色*/
       D.CUSTOMER_NAME,/*客户姓名*/
       D.CUSTOMER_CERT_TYPE,/*证件类型*/
       T.TYPE,/*证件类型*/
       D.CUSTOMER_CERTI_CODE,/*证件号码*/
       D.CUSTOMER_GENDER,/*性别*/
       G.GENDER_DESC,/*性别*/
       D.CUSTOMER_BIRTHDAY/*出生日期*/
  FROM DEV_PAS.T_INSURED_LIST C
  LEFT JOIN DEV_PAS.T_CUSTOMER D
    ON C.CUSTOMER_ID = D.CUSTOMER_ID 
  LEFT JOIN DEV_PAS.T_CERTI_TYPE T/*关联证件号*/
     ON D.CUSTOMER_CERT_TYPE=T.CODE 
  LEFT JOIN DEV_PAS.T_GENDER G
  ON D.CUSTOMER_GENDER=G.GENDER_CODE/*关联性别*/
 WHERE C.POLICY_CODE =  #{policy_code}
		]]>
	</select>
	<!-- 查询险种基本信息 -->
	<!-- 有问题 -->
	<select id="QRY_find_Reinastate_Types_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
 SELECT 
 B.BUSI_PROD_CODE,                                                                                                      /*险种代码*/
 (SELECT PRODUCT_NAME_SYS  FROM DEV_PAS.T_BUSINESS_PRODUCT WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*险种名称*/
 (SELECT D.CUSTOMER_NAME FROM DEV_PAS.T_INSURED_LIST C LEFT JOIN DEV_PAS.T_CUSTOMER D ON C.CUSTOMER_ID = D.CUSTOMER_ID  /*被保人姓名*/WHERE C.POLICY_CODE = #{policy_code}) CUSTOMER_NAME,
 P.STD_PREM_AF,                                                                                          /*保费标准*/
 P.AMOUNT,                                                                                                /*基本保额*/
 B.VALIDATE_DATE,                                                                                                        /*生效日期*/
 P.UNIT,/*份数*/
 (SELECT SUM(A.EXTRA_PREM) EXTRA_PREM 
 FROM DEV_PAS.T_EXTRA_PREM A 
 WHERE  A.POLICY_CODE = #{policy_code} AND A.EXTRA_TYPE='1') EXTRA_PREMA,/*健康加费*/
 (SELECT SUM(A.EXTRA_PREM) EXTRA_PREM 
 FROM DEV_PAS.T_EXTRA_PREM A  
 WHERE  A.POLICY_CODE = #{policy_code} AND A.EXTRA_TYPE='2') EXTRA_PREMB/*职业加费*/
 FROM DEV_PAS.T_CONTRACT_PRODUCT P
 	LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD B
 		ON P.POLICY_CODE = B.POLICY_CODE AND P.BUSI_ITEM_ID = B.BUSI_ITEM_ID
 WHERE P.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 被保人告知信息 -->
	<select id="QRY_find_Inform_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT
		    B.SURVEY_VERSION,
		    B.SURVEY_CODE,
		    B.QUESTION_CONTENT,
		    A.SURVEY_MODULE_RESULT,A.POLICY_CODE,A.SURVEY_QUESTION_ID,A.APPLY_CODE 
		FROM
		    DEV_PAS.V_QUESTIONAIRE_CUSTOMER_ALL A 
		LEFT JOIN
		    DEV_PAS.T_QUESTIONAIRE_INFO B 
		        ON A.SURVEY_QUESTION_ID=B.SURVEY_QUESTION_ID 
		LEFT JOIN 
		    DEV_PAS.T_CONTRACT_MASTER CM
		        ON A.POLICY_ID = CM.POLICY_ID
		WHERE CM.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 健康加费 -->
	<select id="QRY_find_Reinastate_health_Types_Info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 SELECT EXTRA_PREM 
 FROM DEV_PAS.T_EXTRA_PREM A 
 WHERE  A.POLICY_CODE = #{policy_code} AND A.EXTRA_TYPE='1'/*健康加费*/ 
		]]>
	</select>
</mapper>