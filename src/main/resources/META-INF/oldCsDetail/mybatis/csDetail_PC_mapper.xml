<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 缴费信息变更 _mass_wb -->
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCSBusinessQueryDAOImpl">
	<!-- 保单信息 -->
	<select id="QRY_PC_find_Policy_Info" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT 
      C.PAY_MODE,/* 交费形式:(首期缴费方式) */
      C.PAY_NEXT,/* 交费形式:(续期缴费方式) */
      C.ACCOUNT, /*银行账号:(首期缴费银行帐号)*/  
      C.NEXT_ACCOUNT, /*银行账号:(续期缴费银行帐号)*/
      C.ACCOUNT_NAME, /*银行账户名:(首期缴费银行账号户名)*/
      C.NEXT_ACCOUNT_NAME,/*银行账户名:(续期缴费账户持有人姓名)*/
      K.BANK_CODE, /*银行编码*/
      K.BANK_NAME,
      M.NAME /*缴费形式。首期缴费方式*/
FROM DEV_PAS.T_CS_PAYER_ACCOUNT C /*付款账户表*/
LEFT JOIN  DEV_PAS.T_BANK K 
ON K.BANK_CODE = C.NEXT_ACCOUNT_BANK
LEFT JOIN DEV_PAS.T_PAY_MODE M
ON M.CODE = C.PAY_MODE
WHERE C.CHANGE_ID =
 (SELECT CHA.CHANGE_ID /*保单ID*/
          FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/        
               DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/            
         WHERE CHA.CHANGE_ID = ACC.CHANGE_ID   
           AND CHA.POLICY_CODE = #{policy_code}
           AND ACC.ACCEPT_CODE = #{accept_code} AND CHA.ACCEPT_ID=ACC.ACCEPT_ID)
     AND C.OLD_NEW = '0'
         ]]>
	</select>
	<select id="QRY_PC_find_Policy_Info1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
       SELECT SUM(A.TOTAL_PREM_AF) AS TOTAL_PREM_AF, SUM(A.AMOUNT) AS AMOUNT
  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
    ON PC.CHANGE_ID = AC.CHANGE_ID
   AND PC.ACCEPT_ID = AC.ACCEPT_ID
 INNER JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT A
    ON PC.CHANGE_ID = A.CHANGE_ID
   AND PC.POLICY_ID = A.POLICY_ID
 WHERE AC.ACCEPT_CODE = #{accept_code}
   AND PC.POLICY_CODE =  #{policy_code}
   AND A.OLD_NEW = 0
UNION (SELECT CP.TOTAL_PREM_AF, CP.AMOUNT
         FROM DEV_PAS.T_CONTRACT_PRODUCT CP
        WHERE CP.POLICY_CODE =  #{policy_code}
          AND NOT EXISTS (SELECT *
                 FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
                INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
                   ON PC.CHANGE_ID = AC.CHANGE_ID
                  AND PC.ACCEPT_ID = AC.ACCEPT_ID
                INNER JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT A
                   ON PC.CHANGE_ID = A.CHANGE_ID
                  AND PC.POLICY_ID = A.POLICY_ID
                WHERE AC.ACCEPT_CODE = #{accept_code}
                  AND PC.POLICY_CODE =  #{policy_code}
                  AND A.OLD_NEW = 0))

         ]]>
	</select>
	<!-- 查询_原缴费信息 -->
	<select id="QRY_PCFindPay_oldInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT  
       A.NEXT_ACCOUNT,                /*续期缴费银行帐号*/    
       A.NEXT_ACCOUNT_NAME,           /*续期银行姓名*/  
       A.NEXT_ACCOUNT_BANK           /*续期银行ID*/
     ,(SELECT T.BANK_NAME FROM DEV_PAS.T_BANK T /*银行码表*/ WHERE T.BANK_CODE = A.NEXT_ACCOUNT_BANK) AS BANK_NAME
     ,A.PAY_MODE /*交费形式首期缴费方式ID*/
     ,(SELECT M.NAME FROM DEV_PAS.T_PAY_MODE M /*缴费方式码表*/ WHERE A.PAY_MODE = M.CODE) AS NAME
    FROM DEV_PAS.T_CS_PAYER_ACCOUNT A /*付款账户表*/  WHERE A.CHANGE_ID = 
       (SELECT CHA.CHANGE_ID /*保单ID*/
          FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/        
               DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/            
         WHERE CHA.CHANGE_ID = ACC.CHANGE_ID   
           AND CHA.POLICY_CODE = #{policy_code}
           AND ACC.ACCEPT_CODE = #{accept_code}) 
     AND A.OLD_NEW = '0'
         ]]>
	</select>
    <!-- 查询_新缴费信息 -->
    <select id="QRY_PCFindPay_newInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[SELECT  
       A.NEXT_ACCOUNT,                /*续期缴费银行帐号*/    
       A.NEXT_ACCOUNT_NAME,           /*续期银行姓名*/  
       A.NEXT_ACCOUNT_BANK           /*续期银行ID*/
     ,(SELECT T.BANK_NAME FROM DEV_PAS.T_BANK T /*银行码表*/ WHERE T.BANK_CODE = A.NEXT_ACCOUNT_BANK) AS BANK_NAME
     ,A.PAY_MODE /*交费形式首期缴费方式ID*/
     ,(SELECT M.NAME FROM DEV_PAS.T_PAY_MODE M /*缴费方式码表*/ WHERE A.PAY_MODE = M.CODE) AS NAME
    FROM DEV_PAS.T_CS_PAYER_ACCOUNT A /*付款账户表*/  WHERE A.CHANGE_ID = 
       (SELECT CHA.CHANGE_ID /*保单ID*/
          FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/        
               DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/            
         WHERE CHA.CHANGE_ID = ACC.CHANGE_ID   
           AND CHA.POLICY_CODE = #{policy_code}
           AND ACC.ACCEPT_CODE = #{accept_code}) 
     AND A.OLD_NEW = '1'
         ]]>
    </select>
    <select id="QRY_PCFindMedicalInsurance_newInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[SELECT  A.NEXT_ACCOUNT,                /*续期缴费银行帐号*/    
                         A.NEXT_ACCOUNT_NAME,           /*续期银行姓名*/  
                         A.NEXT_ACCOUNT_BANK,           /*续期银行ID*/
                         (SELECT T.BANK_NAME FROM DEV_PAS.T_BANK T /*银行码表*/ WHERE T.BANK_CODE = A.NEXT_ACCOUNT_BANK) AS BANK_NAME,
                         A.MEDICAL_PAY_ORDER_NEXT AS PAY_MODE, /*交费形式*/
                         (SELECT TMT.PAY_ORDER_NAME
                            FROM DEV_PAS.T_MEDICAL_PAY_ORDER_TYPE TMT
                           WHERE A.MEDICAL_PAY_ORDER_NEXT = TMT.PAY_ORDER_CODE) AS NAME,
                         A.MEDICAL_NO_NEXT AS MEDICAL_NO_NEXT
                    FROM DEV_PAS.T_CS_PAYER_ACCOUNT A /*付款账户表*/  
                   WHERE A.CHANGE_ID = 
                         (SELECT CHA.CHANGE_ID /*保单ID*/
                            FROM DEV_PAS.T_CS_POLICY_CHANGE CHA,/*保单变更管理表*/        
                                 DEV_PAS.T_CS_ACCEPT_CHANGE ACC /*受理变更管理表*/            
                           WHERE CHA.CHANGE_ID = ACC.CHANGE_ID   
                             AND CHA.ORGAN_CODE LIKE '8622%'
                             AND CHA.POLICY_CODE = #{policy_code}
                             AND ACC.ACCEPT_CODE = #{accept_code}) 
                         AND A.PAY_MODE = '18'
                         AND (A.MEDICAL_PAY_ORDER = '1'
                          OR A.MEDICAL_PAY_ORDER_NEXT = '1')
                         AND A.OLD_NEW = '1'
        ]]>
    </select>
</mapper>