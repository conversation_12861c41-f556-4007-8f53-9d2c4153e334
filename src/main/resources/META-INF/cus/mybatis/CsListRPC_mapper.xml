<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.csDetailList.ICsEndorseRPClistDao">
	<!-- 查询条件 -->
	<sql id="queryCsListRPCforPage_info">
		<!-- 受理机构的查询条件 -->
		<if test=" organCode!=null and organCode!='' ">
        	<![CDATA[ and ac.organ_code like #{organCode}||'%' ]]>
	    </if>
	    <!-- 保单管理机构  -->
	    <if test=" policyOrganCode!=null and policyOrganCode!='' ">
	        <![CDATA[ and cm.organ_code like #{policyOrganCode}||'%' ]]>
	    </if>
	  
	      <!-- 变更前险种查询条件 -->
	    <if test=" busiPrdIdsXD != null">
		<if test="busiPrdIdsXD.size() > 0">
            <![CDATA[ and rc.old_busi_prd_id IN ]]>
            <foreach item="item" index="id" collection="busiPrdIdsXD"
                open="(" separator="," close=")">
                #{item}
            </foreach>
		</if>
    	</if>
	    <!-- 变更后险种查询条件 -->
	      <if test=" laterbBusiPrdIdsXD != null">
		<if test="laterbBusiPrdIdsXD.size() > 0">
            <![CDATA[  and rc.new_busi_prd_id in]]>
            <foreach item="item" index="id" collection="laterbBusiPrdIdsXD"
                open="(" separator="," close=")">
                #{item}
            </foreach>
		</if>
    	</if>
	  
	    <!-- 申请日期的查询条件 -->
	    <if test=" entryBegin != null and entryBegin != '' and  entryEnd != null and entryEnd != '' ">
	        <![CDATA[
	         AND ca.apply_time >= #{entryBegin} AND  ca.apply_time <= #{entryEnd} 
	        ]]>
	    </if>
	    <!-- 复核日期查询条件 -->
	    <if test=" recombinaBegin != null and recombinaBegin != '' and  recombinaEnd != null and recombinaEnd != '' ">
	        <![CDATA[
	          AND ac.review_time >= #{recombinaBegin}  AND  ac.review_time <= #{recombinaEnd} 
	        ]]>
	    </if>
	    <!-- 生效日期查询条件 -->
	    <if test=" takeEffectBegin != null and takeEffectBegin != '' and  takeEffectEnd != null and takeEffectEnd != '' ">
	        <![CDATA[
	          AND to_Char( rc.valid_time, 'MMdd') >= to_Char(#{takeEffectBegin}, 'MMdd')  AND  to_Char( rc.valid_time, 'MMdd') <= to_Char(#{takeEffectEnd}, 'MMdd')  
	        ]]>
	    </if>
	    <!-- 申请方式 -->
	    <if test=" service_type != null and service_type != ''">
	        <![CDATA[
	          AND ca.service_type in (#{service_type}) 
	        ]]>
	    </if>
	      <!-- 销售渠道 -->
	     <if test=" channelTypeList != null">
		<if test="channelTypeList.size() > 0">
            <![CDATA[ and tca.channel_type IN ]]>
            <foreach item="item" index="id" collection="channelTypeList"
                open="(" separator="," close=")">
                #{item}
            </foreach>
		</if>
    	</if>
	</sql>
	
	

	
	<!-- 查询复效清单总条数 -->
	<select id="queryCsListRPCforCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
	<![CDATA[ 
		select count(1) from (
		
	select (select uo.organ_name
          from dev_pas.t_udmp_org uo
         where uo.organ_code = ca.organ_code) application_organ_code, --受理机构
       (select a.organ_name
          from dev_pas.t_udmp_org a
         where a.organ_code = ac.organ_code) accept_organ_code, --分公司
       ca.apply_code, --保全申请号
       ac.accept_code, --保全受理号
       cm.policy_code, --保单号
       ac.service_code || '-' ||
       (select s.service_name
          from dev_pas.t_service s
         where s.service_code = ac.service_code) service_name, --保全项目
       (select st.type_name
          from dev_pas.t_service_type st
         where st.service_type = ca.service_type) service_type, --申请方式
       (select sc.sales_channel_name
          from dev_pas.t_sales_channel sc
         where sc.sales_channel_code = tca.channel_type) sales_channel, --销售渠道
       cm.organ_code, --保单管理机构代码
  
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 4)) = 4
           and substr(cm.organ_code, 0, 4) = gi.organ_code) || '-' ||
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 6)) = 6
           and substr(cm.organ_code, 0, 6) = gi.organ_code) || '-' ||
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 8)) = 8
           and substr(cm.organ_code, 0, 8) = gi.organ_code) policy_organ_name, --保单管理机构名称
       (select c.agent_organ_code
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_nb_agent = 1
           and a.policy_id = cm.policy_id
           and rownum = 1) channel_org_code, --保单销售机构代码
       (select (
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 4)) = 4
                    and substr(c.agent_organ_code, 0, 4) = gi.organ_code) || '-' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 6)) = 6
                    and substr(c.agent_organ_code, 0, 6) = gi.organ_code) || '-' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 8)) = 8
                    and substr(c.agent_organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_nb_agent = 1
           and a.policy_id = cm.policy_id
           and rownum = 1) channel_org_name, --保单销售机构名称
       (select tc.customer_name
          from dev_pas.t_customer tc
         where tc.customer_id =
               (select ph.customer_id
                  from dev_pas.t_policy_holder ph
                 where ph.policy_id = cm.policy_id)) holder_name, --投保人
       (select tc.customer_name
          from dev_pas.t_customer tc
         where tc.customer_id = (select il.customer_id
                                   from dev_pas.t_insured_list    il,
                                        dev_pas.t_benefit_insured bi
                                  where il.policy_id = cm.policy_id
                                    and bi.insured_id = il.list_id
                                    and bi.order_id = '1'
                                    and rownum = 1)) insured_name, --被保人
       (select cbp.busi_prod_code
          from dev_pas.t_contract_busi_prod cbp
         where cbp.policy_id = cm.policy_id
           and cbp.master_busi_item_id is null
           and rownum = 1) || '-' ||
       (select bp.PRODUCT_NAME_STD
          from dev_pds.t_business_product bp
         where bp.product_code_sys =
               (select cbp.busi_prod_code
                  from dev_pas.t_contract_busi_prod cbp
                 where cbp.policy_id = cm.policy_id
                   and cbp.master_busi_item_id is null
                   and rownum = 1)) master_busi_item_id, --保单主险（代码+险种名称）
       (select bp.product_code_sys
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.old_busi_prd_id) || '-' ||
       (select bp.product_name_std
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.old_busi_prd_id) old_product_code, --变更前险种（代码+险种名称）
       (select bp.product_code_sys
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.new_busi_prd_id) || '-' ||
       (select bp.product_name_std
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.new_busi_prd_id) new_product_code, --变更后险种（代码+险种名称）
      (select (case
         when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 1 then '保障计划一'
           when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 2 then '保障计划二'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0'and rc.item_id = po.item_id ) = 3 then '保障计划三'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 4 then '保障计划四'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id ) = 5 then '保障计划五'
           else (
             case
         when cp.count_way = '1' then
          to_char(rc.old_amount)
          when cp.count_way = '2' then
          to_char(rc.old_amount)
          when cp.count_way = '5' then
            to_char(rc.old_unit)
            end )
       end) from dev_pas.t_cs_contract_product cp 
       where cp.policy_chg_id = pc.policy_chg_id 
       and cp.old_new = '0' and rc.item_id = cp.item_id
       ) old_unit, -- 保额/份数/保障计划（变更前）
       (select (case
         when rc.safeguard_plan = 1 then
             '保障计划一'
             when rc.safeguard_plan = 2 then
             '保障计划二'
             when rc.safeguard_plan = 3 then
             '保障计划三'
             when rc.safeguard_plan = 4 then
              '保障计划四'
             when rc.safeguard_plan = 5 then
             '保障计划五'
          else
            (case when 
            cp.count_way = '1' then
          to_char(rc.new_amount)
          when cp.count_way = '2' then
          to_char(rc.new_amount)
          when cp.count_way = '5' then
            to_char(rc.new_unit)
             end)
       end) from dev_pas.t_cs_contract_product cp 
       where cp.policy_chg_id = pc.policy_chg_id 
       and cp.old_new = '1' and rc.item_id = cp.item_id) new_unit, -- 保额/份数/保障计划（变更后）
       rc.old_std_prem_af, --变更险种前保费
       rc.new_std_prem_af, --变更险种后保费
       cm.validate_date, --保单生效日期
       (select yn.type_name
          from dev_pas.t_yes_no yn
         where yn.yes_no = rc.new_renew) new_renew, --转换后险种续保标识
       (select yn.type_name
          from dev_pas.t_yes_no yn
         where yn.yes_no = (select il.soci_secu
                              from dev_pas.t_insured_list    il,
                                   dev_pas.t_benefit_insured bi
                             where il.policy_id = cm.policy_id
                               and bi.insured_id = il.list_id
                               and bi.order_id = '1'
                               and rownum = 1)) soci_secu, --被保险人社保状态
       ca.apply_time, --申请提交日期
       ac.accept_time, --申请确认日期
       ac.review_time, --复核日期
       ac.validate_time, --生效日期
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               (
               (case when ui.organ_code = '86' then '新华人寿保险股份有限公司总公司' end
               )|| '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code)  || '' || 
                    (
                        case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                    (
                        case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.insert_by = ui.user_id) as accepter_info, --受理人信息*
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               (
                 (case when ui.organ_code = '86' then '新华人寿保险股份有限公司总公司' end
               )|| '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) || '' ||
                ( case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.insert_operator_id = ui.user_id) as enteringer_info, --录入人信息*
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               ((case when ui.organ_code='86' then '新华人寿保险股份有限公司总公司' end) || '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) || '' ||
               (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.review_id = ui.user_id) as checker_info, --复核人信息*
       (case
         when tca.channel_type = '03' then
          aa.agent_code || '-' || aa.agent_name || '-' ||
          cm.service_bank_branch || '-' ||
          (select bb.bank_branch_name
             from dev_pas.t_bank_branch bb
            where bb.bank_branch_code = cm.service_bank_branch)
         when tca.channel_type != '03' then
          aa.agent_code || '-' || aa.agent_name || '-' ||
          (select to_char(listagg(t.sales_organ_name, '-') within
                          group(order by t.organ_level_code asc))
             from (select *
                     from dev_pas.t_sales_organ tso
                    where tso.sales_organ_code != tso.parent_code
                       or tso.parent_code is null) t
            start with t.sales_organ_code = aa.sales_organ_code
           connect by prior t.parent_code = t.sales_organ_code)
       end) as nb_salesmaninfo, --新契约业务员信息
       (select max(case
                     when cm.channel_type = '03' then
                      c.agent_code || '-' || c.agent_name || '-' ||
                      cm.service_bank_branch || '-' ||
                      (select bb.bank_branch_name
                         from dev_pas.t_bank_branch bb
                        where bb.bank_branch_code = cm.service_bank_branch)
                     when cm.channel_type != '03' then
                      c.agent_code || '-' || c.agent_name || '-' ||
                      (select to_char(listagg(t.sales_organ_name, '-') within
                                      group(order by t.organ_level_code asc))
                         from (select *
                                 from dev_pas.t_sales_organ tso
                                where tso.sales_organ_code != tso.parent_code
                                   or tso.parent_code is null) t
                        start with t.sales_organ_code = c.sales_organ_code
                       connect by prior t.parent_code = t.sales_organ_code)
                   end)
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_current_agent = 1
           and a.policy_id = cm.policy_id) as service_salesmaninfo --服务业务员信息
  from dev_pas.t_cs_accept_change ac
  left join dev_pas.t_cs_application ca
    on ac.change_id = ca.change_id
  left join dev_pas.t_cs_policy_change pc
    on ac.accept_id = pc.accept_id
  left join dev_pas.t_contract_master cm
    on cm.policy_id = pc.policy_id
  left join dev_pas.t_contract_agent tca
    on tca.policy_id = cm.policy_id
   and tca.is_nb_agent = '1'
  left join dev_pas.t_agent aa
  on aa.agent_code = tca.agent_code
  left join dev_pas.t_renew_change rc
    on rc.policy_chg_id = pc.policy_chg_id
    left join dev_pas.t_cs_contract_busi_prod cbp
    on cbp.policy_id = rc.policy_id
   and cbp.change_id = ac.change_id
   and cbp.old_new = '1'
   and cbp.operation_type = '1'
 where ac.service_code = 'RR'
   and ac.accept_status = '18'
   and not exists (select 1
          from dev_pas.t_policy_reversal pr
         where pr.accept_code = ac.accept_code)
          
           
			      
			      ]]>
			      <include refid="queryCsListRPCforPage_info" />
			       <![CDATA[
	          	) A
	        ]]>
	</select>

	<!-- 分页查询复效清单操作 -->
	<select id="queryCsListRPCforPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select * from (
		select ROWNUM RN, D.* from (
		
		select (select uo.organ_name
          from dev_pas.t_udmp_org uo
         where uo.organ_code = ca.organ_code) application_organ_code, --受理机构
       (select a.organ_name
          from dev_pas.t_udmp_org a
         where a.organ_code = ac.organ_code) accept_organ_code, --分公司
       ca.apply_code, --保全申请号
       ac.accept_code, --保全受理号
       cm.policy_code, --保单号
       ac.service_code || '-' ||
       (select s.service_name
          from dev_pas.t_service s
         where s.service_code = ac.service_code) service_name, --保全项目
       (select st.type_name
          from dev_pas.t_service_type st
         where st.service_type = ca.service_type) service_type, --申请方式
       (select sc.sales_channel_name
          from dev_pas.t_sales_channel sc
         where sc.sales_channel_code = tca.channel_type) sales_channel, --销售渠道
       cm.organ_code, --保单管理机构代码
  
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 4)) = 4
           and substr(cm.organ_code, 0, 4) = gi.organ_code) || '-' ||
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 6)) = 6
           and substr(cm.organ_code, 0, 6) = gi.organ_code) || '-' ||
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 8)) = 8
           and substr(cm.organ_code, 0, 8) = gi.organ_code) policy_organ_name, --保单管理机构名称
       (select c.agent_organ_code
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_nb_agent = 1
           and a.policy_id = cm.policy_id
           and rownum = 1) channel_org_code, --保单销售机构代码
       (select (
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 4)) = 4
                    and substr(c.agent_organ_code, 0, 4) = gi.organ_code) || '-' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 6)) = 6
                    and substr(c.agent_organ_code, 0, 6) = gi.organ_code) || '-' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 8)) = 8
                    and substr(c.agent_organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_nb_agent = 1
           and a.policy_id = cm.policy_id
           and rownum = 1) channel_org_name, --保单销售机构名称
       (select tc.customer_name
          from dev_pas.t_customer tc
         where tc.customer_id =
               (select ph.customer_id
                  from dev_pas.t_policy_holder ph
                 where ph.policy_id = cm.policy_id)) holder_name, --投保人
       (select tc.customer_name
          from dev_pas.t_customer tc
         where tc.customer_id = (select il.customer_id
                                   from dev_pas.t_insured_list    il,
                                        dev_pas.t_benefit_insured bi
                                  where il.policy_id = cm.policy_id
                                    and bi.insured_id = il.list_id
                                    and bi.order_id = '1'
                                    and rownum = 1)) insured_name, --被保人
       (select cbp.busi_prod_code
          from dev_pas.t_contract_busi_prod cbp
         where cbp.policy_id = cm.policy_id
           and cbp.master_busi_item_id is null
           and rownum = 1) || '-' ||
       (select bp.PRODUCT_NAME_STD
          from dev_pds.t_business_product bp
         where bp.product_code_sys =
               (select cbp.busi_prod_code
                  from dev_pas.t_contract_busi_prod cbp
                 where cbp.policy_id = cm.policy_id
                   and cbp.master_busi_item_id is null
                   and rownum = 1)) master_busi_item_id, --保单主险（代码+险种名称）
       (select bp.product_code_sys
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.old_busi_prd_id) || '-' ||
       (select bp.product_name_std
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.old_busi_prd_id) old_product_code, --变更前险种（代码+险种名称）
       (select bp.product_code_sys
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.new_busi_prd_id) || '-' ||
       (select bp.product_name_std
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.new_busi_prd_id) new_product_code, --变更后险种（代码+险种名称）
      (select (case
         when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 1 then '保障计划一'
           when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 2 then '保障计划二'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0'and rc.item_id = po.item_id ) = 3 then '保障计划三'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 4 then '保障计划四'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id ) = 5 then '保障计划五'
           else (
             case
         when cp.count_way = '1' then
          to_char(rc.old_amount)
          when cp.count_way = '2' then
          to_char(rc.old_amount)
          when cp.count_way = '5' then
            to_char(rc.old_unit)
            end )
       end) from dev_pas.t_cs_contract_product cp 
       where cp.policy_chg_id = pc.policy_chg_id 
       and cp.old_new = '0' and rc.item_id = cp.item_id
       ) old_unit, -- 保额/份数/保障计划（变更前）
       (select (case
         when rc.safeguard_plan = 1 then
             '保障计划一'
             when rc.safeguard_plan = 2 then
             '保障计划二'
             when rc.safeguard_plan = 3 then
             '保障计划三'
             when rc.safeguard_plan = 4 then
              '保障计划四'
             when rc.safeguard_plan = 5 then
             '保障计划五'
          else
            (case when 
            cp.count_way = '1' then
          to_char(rc.new_amount)
          when cp.count_way = '2' then
          to_char(rc.new_amount)
          when cp.count_way = '5' then
            to_char(rc.new_unit)
             end)
       end) from dev_pas.t_cs_contract_product cp 
       where cp.policy_chg_id = pc.policy_chg_id 
       and cp.old_new = '1' and rc.item_id = cp.item_id) new_unit, -- 保额/份数/保障计划（变更后）
       rc.old_std_prem_af, --变更险种前保费
       rc.new_std_prem_af, --变更险种后保费
       cm.validate_date, --保单生效日期
       (select yn.type_name
          from dev_pas.t_yes_no yn
         where yn.yes_no = rc.new_renew) new_renew, --转换后险种续保标识
       (select yn.type_name
          from dev_pas.t_yes_no yn
         where yn.yes_no = (select il.soci_secu
                              from dev_pas.t_insured_list    il,
                                   dev_pas.t_benefit_insured bi
                             where il.policy_id = cm.policy_id
                               and bi.insured_id = il.list_id
                               and bi.order_id = '1'
                               and rownum = 1)) soci_secu, --被保险人社保状态
       ca.apply_time, --申请提交日期
       ac.accept_time, --申请确认日期
       ac.review_time, --复核日期
       ac.validate_time, --生效日期
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               (
               (case when ui.organ_code = '86' then '新华人寿保险股份有限公司总公司' end
               )|| '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code)  || '' || 
                    (
                        case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                    (
                        case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.insert_by = ui.user_id) as accepter_info, --受理人信息*
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               (
                 (case when ui.organ_code = '86' then '新华人寿保险股份有限公司总公司' end
               )|| '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) || '' ||
                ( case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.insert_operator_id = ui.user_id) as enteringer_info, --录入人信息*
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               ((case when ui.organ_code='86' then '新华人寿保险股份有限公司总公司' end) || '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) || '' ||
               (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.review_id = ui.user_id) as checker_info, --复核人信息*
       (case
         when tca.channel_type = '03' then
          aa.agent_code || '-' || aa.agent_name || '-' ||
          cm.service_bank_branch || '-' ||
          (select bb.bank_branch_name
             from dev_pas.t_bank_branch bb
            where bb.bank_branch_code = cm.service_bank_branch)
         when tca.channel_type != '03' then
          aa.agent_code || '-' || aa.agent_name || '-' ||
          (select to_char(listagg(t.sales_organ_name, '-') within
                          group(order by t.organ_level_code asc))
             from (select *
                     from dev_pas.t_sales_organ tso
                    where tso.sales_organ_code != tso.parent_code
                       or tso.parent_code is null) t
            start with t.sales_organ_code = aa.sales_organ_code
           connect by prior t.parent_code = t.sales_organ_code)
       end) as nb_salesmaninfo, --新契约业务员信息
       (select max(case
                     when cm.channel_type = '03' then
                      c.agent_code || '-' || c.agent_name || '-' ||
                      cm.service_bank_branch || '-' ||
                      (select bb.bank_branch_name
                         from dev_pas.t_bank_branch bb
                        where bb.bank_branch_code = cm.service_bank_branch)
                     when cm.channel_type != '03' then
                      c.agent_code || '-' || c.agent_name || '-' ||
                      (select to_char(listagg(t.sales_organ_name, '-') within
                                      group(order by t.organ_level_code asc))
                         from (select *
                                 from dev_pas.t_sales_organ tso
                                where tso.sales_organ_code != tso.parent_code
                                   or tso.parent_code is null) t
                        start with t.sales_organ_code = c.sales_organ_code
                       connect by prior t.parent_code = t.sales_organ_code)
                   end)
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_current_agent = 1
           and a.policy_id = cm.policy_id) as service_salesmaninfo --服务业务员信息
  from dev_pas.t_cs_accept_change ac
  left join dev_pas.t_cs_application ca
    on ac.change_id = ca.change_id
  left join dev_pas.t_cs_policy_change pc
    on ac.accept_id = pc.accept_id
  left join dev_pas.t_contract_master cm
    on cm.policy_id = pc.policy_id
  left join dev_pas.t_contract_agent tca
    on tca.policy_id = cm.policy_id
   and tca.is_nb_agent = '1'
  left join dev_pas.t_agent aa
  on aa.agent_code = tca.agent_code
  left join dev_pas.t_renew_change rc
    on rc.policy_chg_id = pc.policy_chg_id
    left join dev_pas.t_cs_contract_busi_prod cbp
    on cbp.policy_id = rc.policy_id
   and cbp.change_id = ac.change_id
   and cbp.old_new = '1'
   and cbp.operation_type = '1'
 where ac.service_code = 'RR'
   and ac.accept_status = '18'
   and not exists (select 1
          from dev_pas.t_policy_reversal pr
         where pr.accept_code = ac.accept_code)
		          
		      
		      ]]>
		      <include refid="queryCsListRPCforPage_info" />
		      <![CDATA[
  	            order by organ_code,sales_channel,policy_code,validate_time  ]]>
		      	<![CDATA[
  	             )D where ROWNUM <= #{LESS_NUM} )G where G.RN > #{GREATER_NUM}]]>
		    
	</select>
	
	
	
	
	<!-- 分页查询复效清单操作 -->
	<select id="queryCsListRPC" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select * from (
		
		select (select uo.organ_name
          from dev_pas.t_udmp_org uo
         where uo.organ_code = ca.organ_code) application_organ_code, --受理机构
       (select a.organ_name
          from dev_pas.t_udmp_org a
         where a.organ_code = ac.organ_code) accept_organ_code, --分公司
       ca.apply_code, --保全申请号
       ac.accept_code, --保全受理号
       cm.policy_code, --保单号
       ac.service_code || '-' ||
       (select s.service_name
          from dev_pas.t_service s
         where s.service_code = ac.service_code) service_name, --保全项目
       (select st.type_name
          from dev_pas.t_service_type st
         where st.service_type = ca.service_type) service_type, --申请方式
       (select sc.sales_channel_name
          from dev_pas.t_sales_channel sc
         where sc.sales_channel_code = tca.channel_type) sales_channel, --销售渠道
       cm.organ_code, --保单管理机构代码
  
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 4)) = 4
           and substr(cm.organ_code, 0, 4) = gi.organ_code) || '-' ||
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 6)) = 6
           and substr(cm.organ_code, 0, 6) = gi.organ_code) || '-' ||
       (select gi.organ_name
          from dev_pas.t_udmp_org gi
         where length(substr(cm.organ_code, 0, 8)) = 8
           and substr(cm.organ_code, 0, 8) = gi.organ_code) policy_organ_name, --保单管理机构名称
       (select c.agent_organ_code
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_nb_agent = 1
           and a.policy_id = cm.policy_id
           and rownum = 1) channel_org_code, --保单销售机构代码
       (select (
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 4)) = 4
                    and substr(c.agent_organ_code, 0, 4) = gi.organ_code) || '-' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 6)) = 6
                    and substr(c.agent_organ_code, 0, 6) = gi.organ_code) || '-' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(c.agent_organ_code, 0, 8)) = 8
                    and substr(c.agent_organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_nb_agent = 1
           and a.policy_id = cm.policy_id
           and rownum = 1) channel_org_name, --保单销售机构名称
       (select tc.customer_name
          from dev_pas.t_customer tc
         where tc.customer_id =
               (select ph.customer_id
                  from dev_pas.t_policy_holder ph
                 where ph.policy_id = cm.policy_id)) holder_name, --投保人
       (select tc.customer_name
          from dev_pas.t_customer tc
         where tc.customer_id = (select il.customer_id
                                   from dev_pas.t_insured_list    il,
                                        dev_pas.t_benefit_insured bi
                                  where il.policy_id = cm.policy_id
                                    and bi.insured_id = il.list_id
                                    and bi.order_id = '1'
                                    and rownum = 1)) insured_name, --被保人
       (select cbp.busi_prod_code
          from dev_pas.t_contract_busi_prod cbp
         where cbp.policy_id = cm.policy_id
           and cbp.master_busi_item_id is null
           and rownum = 1) || '-' ||
       (select bp.PRODUCT_NAME_STD
          from dev_pds.t_business_product bp
         where bp.product_code_sys =
               (select cbp.busi_prod_code
                  from dev_pas.t_contract_busi_prod cbp
                 where cbp.policy_id = cm.policy_id
                   and cbp.master_busi_item_id is null
                   and rownum = 1)) master_busi_item_id, --保单主险（代码+险种名称）
       (select bp.product_code_sys
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.old_busi_prd_id) || '-' ||
       (select bp.product_name_std
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.old_busi_prd_id) old_product_code, --变更前险种（代码+险种名称）
       (select bp.product_code_sys
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.new_busi_prd_id) || '-' ||
       (select bp.product_name_std
          from dev_pds.t_business_product bp
         where bp.business_prd_id = rc.new_busi_prd_id) new_product_code, --变更后险种（代码+险种名称）
     (select (case
         when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 1 then '保障计划一'
           when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 2 then '保障计划二'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0'and rc.item_id = po.item_id ) = 3 then '保障计划三'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id) = 4 then '保障计划四'
          when (select po.field1
          from dev_pas.t_cs_contract_product_other po
         where po.policy_chg_id = pc.policy_chg_id
           and po.old_new = '0' and rc.item_id = po.item_id ) = 5 then '保障计划五'
           else (
             case
         when cp.count_way = '1' then
          to_char(rc.old_amount)
          when cp.count_way = '2' then
          to_char(rc.old_amount)
          when cp.count_way = '5' then
            to_char(rc.old_unit)
            end )
       end) from dev_pas.t_cs_contract_product cp 
       where cp.policy_chg_id = pc.policy_chg_id 
       and cp.old_new = '0' and rc.item_id = cp.item_id
       ) old_unit, -- 保额/份数/保障计划（变更前）
       (select (case
         when rc.safeguard_plan = 1 then
             '保障计划一'
             when rc.safeguard_plan = 2 then
             '保障计划二'
             when rc.safeguard_plan = 3 then
             '保障计划三'
             when rc.safeguard_plan = 4 then
              '保障计划四'
             when rc.safeguard_plan = 5 then
             '保障计划五'
          else
            (case when 
            cp.count_way = '1' then
          to_char(rc.new_amount)
          when cp.count_way = '2' then
          to_char(rc.new_amount)
          when cp.count_way = '5' then
            to_char(rc.new_unit)
             end)
       end) from dev_pas.t_cs_contract_product cp 
       where cp.policy_chg_id = pc.policy_chg_id 
       and cp.old_new = '1' and rc.item_id = cp.item_id) new_unit, -- 保额/份数/保障计划（变更后）
       rc.old_std_prem_af, --变更险种前保费
       rc.new_std_prem_af, --变更险种后保费
       cm.validate_date, --保单生效日期
       (select yn.type_name
          from dev_pas.t_yes_no yn
         where yn.yes_no = rc.new_renew) new_renew, --转换后险种续保标识
       (select yn.type_name
          from dev_pas.t_yes_no yn
         where yn.yes_no = (select il.soci_secu
                              from dev_pas.t_insured_list    il,
                                   dev_pas.t_benefit_insured bi
                             where il.policy_id = cm.policy_id
                               and bi.insured_id = il.list_id
                               and bi.order_id = '1'
                               and rownum = 1)) soci_secu, --被保险人社保状态
       ca.apply_time, --申请提交日期
       ac.accept_time, --申请确认日期
       ac.review_time, --复核日期
       ac.validate_time, --生效日期
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               (
               (case when ui.organ_code = '86' then '新华人寿保险股份有限公司总公司' end
               )|| '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code)  || '' || 
                    (
                        case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                    (
                        case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.insert_by = ui.user_id) as accepter_info, --受理人信息*
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               (
                 (case when ui.organ_code = '86' then '新华人寿保险股份有限公司总公司' end
               )|| '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) || '' ||
                ( case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.insert_operator_id = ui.user_id) as enteringer_info, --录入人信息*
       (select ui.user_name || '-' || ui.real_name || '-' || ui.organ_code || '-' ||
               ((case when ui.organ_code='86' then '新华人寿保险股份有限公司总公司' end) || '' ||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 4)) = 4
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) || '' ||
               (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 6) = gi.organ_code) || '' ||
                (case when (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 6)) = 6
                    and substr(ui.organ_code, 0, 4) = gi.organ_code) is not null then  '-' end
                    ) || ''||
               (select gi.organ_name
                   from dev_pas.t_udmp_org gi
                  where length(substr(ui.organ_code, 0, 8)) = 8
                    and substr(ui.organ_code, 0, 8) = gi.organ_code))
          from dev_pas.t_udmp_user ui
         where ac.review_id = ui.user_id) as checker_info, --复核人信息*
       (case
         when tca.channel_type = '03' then
          aa.agent_code || '-' || aa.agent_name || '-' ||
          cm.service_bank_branch || '-' ||
          (select bb.bank_branch_name
             from dev_pas.t_bank_branch bb
            where bb.bank_branch_code = cm.service_bank_branch)
         when tca.channel_type != '03' then
          aa.agent_code || '-' || aa.agent_name || '-' ||
          (select to_char(listagg(t.sales_organ_name, '-') within
                          group(order by t.organ_level_code asc))
             from (select *
                     from dev_pas.t_sales_organ tso
                    where tso.sales_organ_code != tso.parent_code
                       or tso.parent_code is null) t
            start with t.sales_organ_code = aa.sales_organ_code
           connect by prior t.parent_code = t.sales_organ_code)
       end) as nb_salesmaninfo, --新契约业务员信息
       (select max(case
                     when cm.channel_type = '03' then
                      c.agent_code || '-' || c.agent_name || '-' ||
                      cm.service_bank_branch || '-' ||
                      (select bb.bank_branch_name
                         from dev_pas.t_bank_branch bb
                        where bb.bank_branch_code = cm.service_bank_branch)
                     when cm.channel_type != '03' then
                      c.agent_code || '-' || c.agent_name || '-' ||
                      (select to_char(listagg(t.sales_organ_name, '-') within
                                      group(order by t.organ_level_code asc))
                         from (select *
                                 from dev_pas.t_sales_organ tso
                                where tso.sales_organ_code != tso.parent_code
                                   or tso.parent_code is null) t
                        start with t.sales_organ_code = c.sales_organ_code
                       connect by prior t.parent_code = t.sales_organ_code)
                   end)
          from dev_pas.t_contract_agent a, dev_pas.t_agent c
         where c.agent_code = a.agent_code
           and a.is_current_agent = 1
           and a.policy_id = cm.policy_id) as service_salesmaninfo --服务业务员信息
  from dev_pas.t_cs_accept_change ac
  left join dev_pas.t_cs_application ca
    on ac.change_id = ca.change_id
  left join dev_pas.t_cs_policy_change pc
    on ac.accept_id = pc.accept_id
  left join dev_pas.t_contract_master cm
    on cm.policy_id = pc.policy_id
  left join dev_pas.t_contract_agent tca
    on tca.policy_id = cm.policy_id
   and tca.is_nb_agent = '1'
  left join dev_pas.t_agent aa
  on aa.agent_code = tca.agent_code
  left join dev_pas.t_renew_change rc
    on rc.policy_chg_id = pc.policy_chg_id
    left join dev_pas.t_cs_contract_busi_prod cbp
    on cbp.policy_id = rc.policy_id
   and cbp.change_id = ac.change_id
   and cbp.old_new = '1'
   and cbp.operation_type = '1'
 where ac.service_code = 'RR'
   and ac.accept_status = '18'
   and not exists (select 1
          from dev_pas.t_policy_reversal pr
         where pr.accept_code = ac.accept_code)
		          
		      
		      ]]>
		      
		      <include refid="queryCsListRPCforPage_info" />
		      <![CDATA[
  	            order by organ_code,sales_channel,policy_code,validate_time  ]]>
		      	<![CDATA[
  	             )D ]]>
		    
	</select>
	
	
	
	
	
	
	
	
	<!-- 查询RR-续保险种转换险种 -->
	<select id="findAllRRProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select
			A.PRIMARY_SALES_CHANNEL,
	       A.SINGLE_JOINT_LIFE,
	       A.COVER_PERIOD_TYPE,
	       A.PRODUCT_CATEGORY4,
	       A.BUSINESS_PRD_ID,
	       A.PRODUCT_CATEGORY3,
	       A.PRODUCT_CODE_ORIGINAL,
	       A.PRODUCT_CATEGORY2,
	       A.PRODUCT_CATEGORY1,
	       A.PRODUCT_NAME_SYS,
	       A.PRODUCT_ABBR_NAME,
	       A.PRODUCT_STATIC_CODE,
	       A.INSURED_COUNT_MAX,
	       A.INSURED_COUNT_MIN,
	       A.PRODUCT_DESC,
	       A.RELEASE_DATE,
	       A.PREMIUM_RATE_LAYER,
	       A.PREMIUM_CURRENCY,
	       A.PRODUCT_NAME_STD,
	       A.PRODUCT_CODE_STD,
	       A.RENEW_OPTION,
	       A.PRODUCT_CATEGORY,
	       A.SCHEDULE_RATE,
	       A.PRODUCT_CODE_SYS,
	       A.WAIVER_FLAG
			 from app___pds__dbuser.t_business_product A
			right join app___pds__dbuser.t_business_product_service c
			on c.business_prd_id = A.business_prd_id
			left join dev_pas.t_service s
			on s.service_code = c.service_code
			where s.service_code = 'RR'
			and A.business_prd_id is not null
			order by product_code_sys 
			]]>
	
			
	</select>
	
	
	<!-- 根据转换险种查询变更后险种 -->
	<select id="findBusiPrdID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT s.conversion_busi_prd_id  businessPrdId
  				FROM dev_pds.t_business_prod_cs s
		 	where  1 = 1
			]]>
			  	 <if test=" busiPrdIdList != null">
				<if test="busiPrdIdList.size() > 0">
		            <![CDATA[ AND  s.business_prd_id in  ]]>
		            <foreach item="item" index="id" collection="busiPrdIdList"
		                open="(" separator="," close=")">
		                #{item}
		            </foreach>
				</if>
	    	</if>
        	
	</select>
	
	
	
	
	
	<!-- 根据转换险种查询变更后险种 -->
	<select id="findLaterBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select 
		         A.SINGLE_JOINT_LIFE,
		       A.COVER_PERIOD_TYPE,
		       A.PRODUCT_CATEGORY4,
		       A.BUSINESS_PRD_ID,
		       A.PRODUCT_CATEGORY3,
		       A.PRODUCT_CODE_ORIGINAL,
		       A.PRODUCT_CATEGORY2,
		       A.PRODUCT_CATEGORY1,
		       A.PRODUCT_NAME_SYS,
		       A.PRODUCT_ABBR_NAME,
		       A.PRODUCT_STATIC_CODE,
		       A.INSURED_COUNT_MAX,
		       A.INSURED_COUNT_MIN,
		       A.PRODUCT_DESC,
		       A.RELEASE_DATE,
		       A.PREMIUM_RATE_LAYER,
		       A.PREMIUM_CURRENCY,
		       A.PRODUCT_NAME_STD,
		       A.PRODUCT_CODE_STD,
		       A.RENEW_OPTION,
		       A.PRODUCT_CATEGORY,
		       A.SCHEDULE_RATE,
		       A.PRODUCT_CODE_SYS,
		       A.WAIVER_FLAG
		  from  dev_pas.t_business_product A
		 where  1 = 1
			]]>
			  	 <if test=" busiPrdIdList != null">
				<if test="busiPrdIdList.size() > 0">
		            <![CDATA[ AND  A.Product_Code_Sys in ]]>
		            <foreach item="item" index="id" collection="busiPrdIdList"
		                open="(" separator="," close=")">
		                #{item}
		            </foreach>
				</if>
	    	</if>
            	<![CDATA[
					order by product_code_sys 
				]]>
	</select>
	
	
	<!-- 申请方式 -->
	<select id="findAllServiceTypes" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from APP___PAS__DBUSER.T_SERVICE_TYPE
		 
			]]>
			
	
	
			
	</select>
	

	
	

		
</mapper>
