<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.ICsEndorseHistoryInvalidatelistDao">
	<sql id="csServiceHistoryInvalidateWhereCondition">
<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND TEMP.organ_code like #{organ_code} || '%' ]]></if>  
		<if test="lapse_cause != null and lapse_cause != ''"><![CDATA[ AND TEMP.lapse_cause = #{lapse_cause} ]]></if>
		<if test="channel_type != null and channel_type != ''"><![CDATA[ AND TEMP.channel_type in (${channel_type}) ]]></if>	
		 <if test="policy_liability_state != null and policy_liability_state != ''"><![CDATA[ AND TEMP.liability_state = #{policy_liability_state } ]]></if>	
		<if test="batch_start_time != null and batch_start_time != ''"><![CDATA[ AND TEMP.lapse_date BETWEEN #{batch_start_time } AND #{batch_end_time } ]]></if> 
	</sql>

	<!-- 查询个数操作 -->
	<select id="findCsServiceHistoryInvalidatelistTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1) FROM( 
                SELECT DISTINCT TCM.POLICY_CODE, TCM.POLICY_ID, TA.CHANNEL_TYPE, TCM.LIABILITY_STATE,TCM.VALIDATE_DATE,TCM.SERVICE_BANK_BRANCH,TCM.SERVICE_BANK,TCM.ORGAN_CODE,
                                TBI.INSURED_ID, TBI.ORDER_ID,
                                TPLC.BUSI_CASH_VALUE,TPLC.PAY_DUE,TPLC.LOAN_CAPITAL,TPLC.LOAN_INTEREST,/*TPLC.POLICY_CHG_ID,*/
                                TCBP.EXPIRY_DATE, TCBP.SUSPEND_DATE, TCBP.LAPSE_DATE, TCBP.BUSI_ITEM_ID, TCBP.MASTER_BUSI_ITEM_ID, TCBP.BUSI_PROD_CODE,TCBP.LAPSE_CAUSE,TCBP.END_CAUSE,(CASE TCBP.APL_PERMIT WHEN 0 THEN '否' WHEN 1 THEN '是' END)AS APL_PERMIT
                                

        FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
        INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
        ON TCBP.POLICY_ID = TCM.POLICY_ID
		 LEFT JOIN DEV_PAS.T_CONTRACT_AGENT TA ON TA.POLICY_ID = TCM.POLICY_ID and TA.is_nb_agent = '1'        
           INNER JOIN DEV_PAS.T_BENEFIT_INSURED TBI
           ON TCBP.BUSI_ITEM_ID = TBI.BUSI_ITEM_ID
           AND TCBP.POLICY_ID = TBI.POLICY_ID
              LEFT JOIN DEV_PAS.T_PRODUCT_LIABILITY_CHANGE TPLC
              ON TCBP.BUSI_ITEM_ID = TPLC.BUSI_ITEM_ID 
             left join dev_pas.t_policy_account_stream tpas 
       		   on  tpas.policy_id =   TCM.Policy_Id 
      		   and tpas.busi_item_id =   TCBP.Busi_Item_Id 
       		   and tpas.regular_repay = '0'    
            WHERE TPLC.LIABILITY_CHANGED = '4' 
              AND TPLC.LIABILITY_STATUS IN ('1','2','3') --获取可能包含23终止中止
              AND TPLC.ITEM_ID IS NOT NULL --过滤数据库垃圾数据
              AND TPLC.LAPSE_CAUSE IN ('1','2','3','6'))TEMP
            WHERE 1=1    
		    ]]>
		 <include refid="csServiceHistoryInvalidateWhereCondition" /> 
	</select>

	<!-- 分页查询历史失效清单操作 -->
	<select id="queryCsServiceHistoryInvalidatelistForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT PP.* FROM (SELECT P.* FROM (   SELECT ROWNUM AS RN,
                           (SELECT A.FINISH_TIME
                               FROM DEV_PAS.T_POLICY_CHANGE A
                              WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID) BATCH_DATE, /* 批处理日期*/
                            
                           (SELECT B.SERVICE_NAME
                               FROM DEV_PAS.T_POLICY_CHANGE      A,
                                    DEV_PAS.T_BACK_CROSS_SERVICE B
                              WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID
                                AND A.SERVICE_CODE = B.SERVICE_CODE) BATCH_NAME, /* 批处理名称*/
                            
                            
                         /*销售渠道名称*/
                         (SELECT SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL WHERE T_SALES_CHANNEL.SALES_CHANNEL_CODE = T.CHANNEL_TYPE) 
                                 AS CHANNEL_TYPES,
                         /*保单管理机构代码*/
                         T.ORGAN_CODE AS ORGAN_CODE,
                         /*保单管理机构名称*/
                         ((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE SUBSTR(T.ORGAN_CODE, 0, 2) = GI.ORGAN_CODE)||        
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 4)) = 4 AND SUBSTR(T.ORGAN_CODE, 0, 4) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 4)) = 4 AND SUBSTR(T.ORGAN_CODE, 0, 4) = GI.ORGAN_CODE),'')||
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 6)) = 6 AND SUBSTR(T.ORGAN_CODE, 0, 6) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 6)) = 6 AND SUBSTR(T.ORGAN_CODE, 0, 6) = GI.ORGAN_CODE),'')||
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 8)) = 8 AND SUBSTR(T.ORGAN_CODE, 0, 8) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 8)) = 8 AND SUBSTR(T.ORGAN_CODE, 0, 8) = GI.ORGAN_CODE),'')) 
                                  AS ORGAN_NAME,
                         /*保单销售机构代码*/
                         (SELECT C.AGENT_ORGAN_CODE FROM DEV_PAS.T_CONTRACT_AGENT A, DEV_PAS.T_AGENT C WHERE C.AGENT_CODE = A.AGENT_CODE
                                 AND A.IS_NB_AGENT = 1 AND A.POLICY_ID = T.POLICY_ID) 
                                 AS CHANNEL_ORG_CODE,
                         /*保单销售机构名称*/
                         (SELECT ((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE SUBSTR(C.AGENT_ORGAN_CODE, 0, 2) = GI.ORGAN_CODE)||        
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 4)) = 4 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 4) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 4)) = 4 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 4) = GI.ORGAN_CODE),'')||
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 6)) = 6 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 6) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 6)) = 6 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 6) = GI.ORGAN_CODE),'')||
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 8)) = 8 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 8) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 8)) = 8 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 8) = GI.ORGAN_CODE),''))
                                          FROM DEV_PAS.T_CONTRACT_AGENT A,DEV_PAS.T_AGENT C WHERE C.AGENT_CODE = A.AGENT_CODE
                                          AND A.IS_NB_AGENT = 1 AND A.POLICY_ID = T.POLICY_ID) 
                                          AS CHANNEL_ORG_NAME, 
                         /*投保人性别*/ 
                         (SELECT DECODE(TC.CUSTOMER_GENDER, 1, '男', 2, '女', 9, '未知') FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_CUSTOMER TC
                              WHERE TPH.POLICY_ID = T.POLICY_ID AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                              AS HOLDER_GENDER,   
                         /*投保人出生日期*/  
                         (SELECT TC.CUSTOMER_BIRTHDAY FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_CUSTOMER TC
                              WHERE TPH.POLICY_ID = T.POLICY_ID AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                              AS HOLDER_BIRTHDAY, 
                         /*投保人姓名*/
                         (SELECT TC.CUSTOMER_NAME FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_CUSTOMER TC WHERE TPH.POLICY_ID = T.POLICY_ID AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                                 AS HOLDER_NAME,
                         /*投保人移动电话*/
                         (SELECT TC.MOBILE_TEL FROM DEV_PAS.T_POLICY_HOLDER TPH,DEV_PAS.T_CUSTOMER TC WHERE TPH.POLICY_ID = T.POLICY_ID
                                 AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                                 AS HOLDER_MOBILE_TEL,
                         /*需求26132添加是否自垫标识*/
                         T.APL_PERMIT AS APL_PERMIT, 
                         /*需求26132添加被保人移动电话*/
                         (SELECT TC.MOBILE_TEL FROM DEV_PAS.T_CUSTOMER TC INNER JOIN DEV_PAS.T_INSURED_LIST TIL
                                 ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID WHERE TIL.LIST_ID = T.INSURED_ID AND T.ORDER_ID = '1'/*第一被保人*/) 
                                 AS INSURED_MOBILE_TEL, 
                         /*需求26132添加主险交费方式--多行重复 DEV_PAS.T_PREM*/
                         (CASE (
                                 SELECT 
                                    PA.PAY_NEXT AS PAY_MODE
                                    FROM DEV_PAS.T_PAYER_ACCOUNT PA
                                    INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
                                    ON PA.POLICY_ID=CM.POLICY_ID
                                    LEFT JOIN DEV_PAS.T_BANK B
                                    ON B.BANK_CODE = PA.NEXT_ACCOUNT_BANK
                                    WHERE 1=1 AND CM.POLICY_CODE=T.POLICY_CODE   AND ROWNUM=1
                          ) 
                              WHEN  '10' THEN '现金'
                              WHEN  '11' THEN '现金送款薄'
                              WHEN  '18' THEN '社保缴纳'
                              WHEN  '20' THEN '支票'
                              WHEN  '21' THEN '现金支票'
                              WHEN  '22' THEN '转账支票'
                              WHEN  '30' THEN '银行转账'
                              WHEN  '31' THEN '银行转账（非制返盘）'
                              WHEN  '32' THEN '银行转账（制返盘）'
                              WHEN  '33' THEN '网银支付'
                              WHEN  '34' THEN '网上银行'
                              WHEN  '40' THEN '实时转账'
                              WHEN  '41' THEN '智能POS收费'
                              WHEN  '42' THEN '客户账户'
                              WHEN  '50' THEN '内部转账'
                              WHEN  '51' THEN '普通内部转账'
                              WHEN  '52' THEN '预存内部转账'
                              WHEN  '60' THEN '银保通'
                              WHEN  '70' THEN '第三方支付'
                              WHEN  '71' THEN '网络销售'
                              WHEN  '80' THEN '客户暂存'
                              WHEN  '90' THEN '其它'
                              WHEN  '91' THEN '中介代收'
                              WHEN  '92' THEN '老系统转入'
                              WHEN  '93' THEN '民生银行代收'
                              WHEN  '94' THEN '客户预存'
                              WHEN  '95' THEN '银行收款'
                              WHEN  '96' THEN 'POS收款'
                              WHEN  '97' THEN '邮储业务'
                              WHEN  '99' THEN '内部扣回' END)
                                  AS MASTER_PAY_LOCATION,
                         /*需求26132添加与被保人关系*/
                         (CASE (SELECT OTIL.RELATION_TO_PH FROM DEV_PAS.T_INSURED_LIST OTIL WHERE OTIL.POLICY_CODE=T.POLICY_CODE AND OTIL.LIST_ID=T.INSURED_ID and ROWNUM = 1 )
                            WHEN '00' THEN '本人'
                            WHEN '00' THEN '本人'
                            WHEN '01' THEN  '父子'
                            WHEN  '02' THEN '父女'
                            WHEN  '03' THEN '母子'
                            WHEN  '04' THEN '母女'
                            WHEN  '05' THEN '祖孙'
                            WHEN  '07' THEN '夫妻'
                            WHEN  '08' THEN '兄弟'
                            WHEN  '09' THEN '兄妹'
                            WHEN  '10' THEN '姐弟'
                            WHEN  '11' THEN '姐妹'
                            WHEN  '12' THEN '叔侄'
                            WHEN  '13' THEN '姑侄'
                            WHEN  '14' THEN '外甥'
                            WHEN  '15' THEN '媳'
                            WHEN  '16' THEN '婿'
                            WHEN  '17' THEN '姐夫'
                            WHEN  '18' THEN '朋友'
                            WHEN  '19' THEN '同事'
                            WHEN  '20' THEN '师生'
                            WHEN  '21' THEN '劳动关系'
                            WHEN  '22' THEN '其他'
                            WHEN  '23' THEN '法定'
                            WHEN  '24' THEN '子女'
                            WHEN  '25' THEN '父母'
                         END) AS RELATION_TO_INSURED,
                         /*投保人固定电话 */
                         (SELECT TC.OFFEN_USE_TEL FROM DEV_PAS.T_POLICY_HOLDER TPH,DEV_PAS.T_CUSTOMER TC WHERE TPH.POLICY_ID = T.POLICY_ID
                                 AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID and rownum =1) 
                                 AS HOLDER_HOUSE_TEL, 
                         /*被保人姓名*/ 
                         (SELECT TC.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER TC INNER JOIN DEV_PAS.T_INSURED_LIST TIL ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                                 WHERE TIL.LIST_ID = T.INSURED_ID AND T.ORDER_ID = '1'  and rownum =1) 
                                 AS INSURED_NAME,
                          /*被保人性别*/ 
                         (SELECT DECODE(TC.CUSTOMER_GENDER, 1, '男', 2, '女', 9, '未知') FROM DEV_PAS.T_CUSTOMER TC INNER JOIN DEV_PAS.T_INSURED_LIST TIL
                                 ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID WHERE TIL.LIST_ID = T.INSURED_ID AND T.ORDER_ID = '1'  and rownum =1) 
                                 AS INSURED_GENDER,   
                         /*被保人出生日期*/                           
                        (SELECT TC.CUSTOMER_BIRTHDAY FROM DEV_PAS.T_CUSTOMER TC INNER JOIN DEV_PAS.T_INSURED_LIST TIL ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                                 WHERE TIL.LIST_ID = T.INSURED_ID AND T.ORDER_ID = '1'  and rownum =1) 
                                 AS INSURED_BIRTHDAY, 
                         /*被保人投保年龄*/                            
                         TO_CHAR((SELECT TIL.INSURED_AGE FROM DEV_PAS.T_INSURED_LIST TIL WHERE TIL.LIST_ID = T.INSURED_ID  and rownum =1))
                                 AS INSURED_APPLY_AGE, 
                         /*主附险标识*/
                         (CASE WHEN T.MASTER_BUSI_ITEM_ID IS NULL THEN '主险' ELSE '附加险' END)
                               AS MASTERFLAG_MASTER,  
                         /*险种名称 */
                         (SELECT TBP.PRODUCT_NAME_STD FROM DEV_PAS.T_BUSINESS_PRODUCT TBP WHERE TBP.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE  and rownum =1) 
                                 AS BUSI_PROD_NAME, 
                         /*责任组信息 */
                         (SELECT TCP.PRODUCT_CODE || '-' || TPL.PRODUCT_NAME 
                                 FROM DEV_PAS.T_PRODUCT_LIFE TPL,DEV_PAS.T_CONTRACT_PRODUCT TCP
                                 WHERE TPL.PRODUCT_ID = TCP.PRODUCT_ID AND TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID AND ROWNUM = 1) 
                                 AS PRODUCT_ITEM, 
                         /*险种状态*/
                         (SELECT TLS.STATUS_NAME FROM DEV_PAS.T_LIABILITY_STATUS TLS WHERE TLS.STATUS_CODE = T.LIABILITY_STATE  and rownum =1) 
                                 AS BUSI_LIABILITY_STATE,
                         /*保单状态*/  
                         (SELECT TLS.STATUS_NAME FROM DEV_PAS.T_LIABILITY_STATUS TLS WHERE TLS.STATUS_CODE = T.LIABILITY_STATE  and rownum =1) 
                                 AS POLICY_LIABILITY_STATE,
                         /*保单状态码*/  
                         T.LIABILITY_STATE AS LIABILITY_STATE,
                         /*保额*/
                         (SELECT SUM(TCP.AMOUNT) FROM DEV_PAS.T_CONTRACT_PRODUCT TCP WHERE TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID  and rownum =1) 
                                 AS AMOUNT,
                         /*保费*/
                         (SELECT SUM(TCP.TOTAL_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT TCP WHERE TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID  and rownum =1) 
                                 AS TOTAL_PREM_AF, 
                          /*新契约业务员信息*/
                         (SELECT TA.AGENT_CODE || '-' || TA.AGENT_NAME || '-' || CASE WHEN T.CHANNEL_TYPE = '03' THEN (T.SERVICE_BANK_BRANCH || '-' || T.SERVICE_BANK) ELSE
                          (SELECT TO_CHAR(LISTAGG(TSO.SALES_ORGAN_NAME,'-') WITHIN GROUP(ORDER BY TSO.ORGAN_LEVEL_CODE ASC)) FROM (SELECT *
                                                      FROM DEV_PAS.T_SALES_ORGAN TORG
                                                     WHERE TORG.PARENT_CODE !=
                                                           TORG.SALES_ORGAN_CODE) TSO
                            START WITH TSO.SALES_ORGAN_CODE = TA.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE) END
                            FROM DEV_PAS.T_CONTRACT_AGENT TCA, DEV_PAS.T_AGENT TA WHERE TA.AGENT_CODE = TCA.AGENT_CODE
                            AND TCA.IS_NB_AGENT = 1 AND TCA.POLICY_ID = T.POLICY_ID  and rownum =1) 
                            AS POLICY_AGENT_MSG, 
                          /*服务业务员信息*/
                         (SELECT TA.AGENT_CODE || '-' || TA.AGENT_NAME || '-' || CASE WHEN T.CHANNEL_TYPE = '03' THEN (T.SERVICE_BANK_BRANCH || '-' || T.SERVICE_BANK) ELSE 
                                 (SELECT TO_CHAR(LISTAGG(TSO.SALES_ORGAN_NAME,'-') WITHIN GROUP(ORDER BY TSO.ORGAN_LEVEL_CODE ASC)) FROM (SELECT *
                                                      FROM DEV_PAS.T_SALES_ORGAN TORG
                                                     WHERE TORG.PARENT_CODE !=
                                                           TORG.SALES_ORGAN_CODE) TSO
                                         START WITH TSO.SALES_ORGAN_CODE = TA.SALES_ORGAN_CODE CONNECT BY PRIOR TSO.PARENT_CODE = TSO.SALES_ORGAN_CODE) END
                          FROM DEV_PAS.T_CONTRACT_AGENT TCA, DEV_PAS.T_AGENT TA
                          WHERE TA.AGENT_CODE = TCA.AGENT_CODE AND TCA.IS_CURRENT_AGENT = 1 AND TCA.POLICY_ID = T.POLICY_ID  and rownum =1) 
                                AS POLICY_SERVICE_MSG,
                            T.BUSI_CASH_VALUE AS LOAN_APPLY_CASH_VALUE, /*现金价值*/ 
                            T.interest_balance   AS INIT_LOAN_INTEREST, /*利息*/
                            T.interest_capital    AS LOAN_INTEREST_CAPITAL, /*贷款本息和*/
                            T.POLICY_CODE AS POLICY_CODE, /*保险单号 */
                            (CASE WHEN T.MULTI_MAINRISK_FLAG = 1 THEN '是' ELSE '否' END) AS MULTI_MAINRISK_FLAG_NAME, /*多主险标识*/
                            T.VALIDATE_DATE  AS POLICY_VALIDATE_DATE, /*保单生效日期*/ 
                            T.LAPSE_DATE     AS POLICY_LAPSE_DATE, /* 保单失效日期  */
                            T.VALIDATE_DATE AS BUSI_VALIDATE_DATE, /*险种生效日期  */
                            T.LAPSE_DATE    AS BUSI_LAPSE_DATE, /*险种失效日期  */
                            T.BUSI_PROD_CODE AS BUSI_PROD_CODE, /* 险种代码*/         
                            T.CHANNEL_TYPE, /* 销售渠道代码*/
                            (SELECT A.PAY_DUE_DATE
                               FROM DEV_PAS.T_CONTRACT_EXTEND A
                              WHERE A.POLICY_ID = T.POLICY_ID
                                AND A.BUSI_ITEM_ID = T.BUSI_ITEM_ID  and rownum =1) PAY_DUE_DATE, /*险种下期缴费日期  */
                            (SELECT A.POLICY_PERIOD
                               FROM DEV_PAS.T_CONTRACT_EXTEND A
                              WHERE A.POLICY_ID = T.POLICY_ID
                                AND A.BUSI_ITEM_ID = T.BUSI_ITEM_ID  and rownum =1) POLICY_PERIOD, /*已交保费期数*/
                            (SELECT SUM(TCP.TOTAL_PREM_AF)
                               FROM DEV_PAS.T_CONTRACT_PRODUCT TCP
                              WHERE TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID  and rownum =1) AS ACCUMULATE_PREM, /*已交保费金额*/
                            (SELECT TLC.CAUSE_DESC
                               FROM DEV_PAS.T_LAPSE_CAUSE TLC
                              WHERE TLC.CAUSE_CODE = T.LAPSE_CAUSE  and rownum =1) AS POLICY_LAPSE_CAUSE_NAME, /*保单失效原因*/
                            (SELECT TLC.CAUSE_DESC
                               FROM DEV_PAS.T_LAPSE_CAUSE TLC
                              WHERE TLC.CAUSE_CODE = T.LAPSE_CAUSE  and rownum =1) AS BUSI_LAPSE_CAUSE_NAME, /*险种失效原因*/
                             /*贷款起期*/
                            (SELECT TPAS.LOAN_START_DATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4  and rownum =1) AS LOAN_FIRST_TRANS_DATE,
                           
                                /*贷款起息日*/
                       (SELECT (SELECT (CASE
					                 WHEN CSPAS.INTEREST_START_DATE IS NOT NULL THEN
					                  CSPAS.INTEREST_START_DATE
					                 ELSE
					                  (CASE
					                    WHEN CSAC.SERVICE_CODE = 'LN' AND
					                         TO_DATE(CI.CONSTANTS_VALUE, 'YYYY-MM-DD') <
					                         CSPC.VALIDATE_TIME THEN
					                     (SELECT TRUNC(L.FINISH_TIME)
					                        FROM DEV_PAS.T_PREM_ARAP L
					                       WHERE L.BUSINESS_CODE = CSAC.ACCEPT_CODE
					                         AND ROWNUM = '1')
					                    ELSE
					                     CSPC.APPLY_TIME
					                  END)
					               END)
					          FROM DEV_PAS.T_CONSTANTS_INFO CI
					         WHERE CI.CONSTANTS_KEY = 'LOAN_GO_ONLINE_FRONTGE') AS INTEREST_START_DATE
                          from  dev_pas.t_cs_accept_change csac ,
                                dev_pas.t_Cs_Policy_Change         cspc,
                               dev_PAs.t_Cs_Policy_Account_Stream cspas,
                               dev_pas.t_policy_Account_Stream    tpas
                        where  cspc.policy_chg_id = cspas.policy_chg_id
                           and csac.accept_id = cspc.accept_id 
                           and T.POLICY_ID = cspc.policy_id
                           and cspas.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                           and tpas.busi_item_id = T.BUSI_ITEM_ID
                           and tpas.policy_id = T.POLICY_ID
                           and tpas.stream_id = cspas.stream_id
                           and cspas.regular_repay = '0'
                           and cspas.account_type = '4'
                           and cspas.old_new = '1'
                           and tpas.regular_repay = '0'
                           and tpas.account_type = '4'
                           and cspc.service_code in ('LN', 'RL')
                           and csac.accept_status = '18'
                           and rownum = '1') AS INTEREST_START_DATE, 
                           
                            /*贷款止期*/
                            (SELECT TPAS.REPAY_DUE_DATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4  and rownum =1) AS LOAN_REPAY_DUE_DATE, 
                            /*贷款本金*/
                            (SELECT TPAS.CAPITAL_BALANCE
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4  and rownum =1) AS LOAN_CAPITAL_BALANCE, 
                             /*贷款限额*/
                            (SELECT TPAS.LOAN_LIMIT
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4  and rownum =1) AS LOAN_AMOUNT_LIMIT,
                            /*初始利率*/
                            (SELECT TPASR.LOAN_RATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT             TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM      TPAS,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM_RATE TPASR
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPASR.STREAM_ID = TPAS.STREAM_ID
                                AND TPASR.TIME_PERIDO_CODE = '1'
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4  and rownum =1) AS INIT_LOAN_RATE, 
                            /*逾期利率*/
                            (SELECT TPASR.LOAN_RATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT             TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM      TPAS,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM_RATE TPASR
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPASR.STREAM_ID = TPAS.STREAM_ID
                                AND TPASR.TIME_PERIDO_CODE = '2'
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4  and rownum =1) AS SECOND_LOAN_RATE, 
                             /*三阶段利率*/
                            (SELECT TPASR.LOAN_RATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT             TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM      TPAS,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM_RATE TPASR
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPASR.STREAM_ID = TPAS.STREAM_ID
                                AND TPASR.TIME_PERIDO_CODE = '3'
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4  and rownum =1) AS THIRD_LOAN_RATE,
                             /*可贷比例*/
                            (SELECT  MAX(TLPC.MAX_LOAN_RATIO)
                               FROM DEV_PAS.T_LOAN_POLICY_CFG TLPC
                              WHERE TLPC.POLICY_ID = T.POLICY_ID
                                AND TLPC.BUSI_ITEM_ID = T.BUSI_ITEM_ID  and rownum =1) AS MAX_LOAN_RATIO, 
                            /* 逾期利息 
                            (SELECT TEMP.INTEREST_SUM FROM (SELECT TPAS.INTEREST_SUM,TPA.BUSI_ITEM_ID,TPA.POLICY_ID FROM DEV_PAS.T_POLICY_ACCOUNT TPA, DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                                   WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID AND TPA.ACCOUNT_TYPE = 4 ORDER BY TPAS.UPDATE_TIME)TEMP 
                               WHERE ROWNUM=1 AND TEMP.BUSI_ITEM_ID = T.BUSI_ITEM_ID AND TEMP.POLICY_ID = T.POLICY_ID) 
                                AS INTEREST_OVERDUE*/
                                
                                
                            (SELECT TPAS.INTEREST_SUM FROM DEV_PAS.T_POLICY_ACCOUNT TPA, DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                                   WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID AND TPA.ACCOUNT_TYPE = 4 AND TPA.BUSI_ITEM_ID = T.BUSI_ITEM_ID AND TPA.POLICY_ID = T.POLICY_ID and TPAS.Regular_Repay = '0'  AND ROWNUM=1) 
                                AS INTEREST_OVERDUE
                                
                            /*(SELECT CASE
                                      WHEN TPAS.INTEREST_SUM - T.LOAN_INTEREST >= 0 THEN
                                       TPAS.INTEREST_SUM - T.LOAN_INTEREST
                                      ELSE
                                       0
                                    END
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPA.ACCOUNT_TYPE = 4) AS INTEREST_OVERDUE*/
                               
              FROM (
              SELECT * FROM( 
                SELECT DISTINCT TCM.POLICY_CODE,TCM.MULTI_MAINRISK_FLAG, TCM.POLICY_ID, TA.CHANNEL_TYPE, TCM.LIABILITY_STATE,TCM.VALIDATE_DATE,TCM.SERVICE_BANK_BRANCH,TCM.SERVICE_BANK,TCM.ORGAN_CODE,
                                TBI.INSURED_ID, TBI.ORDER_ID,
                                TPLC.BUSI_CASH_VALUE,TPLC.PAY_DUE,TPLC.LOAN_CAPITAL,TPLC.LOAN_INTEREST,TPLC.POLICY_CHG_ID,--因ID会造成数据重复
                                TCBP.EXPIRY_DATE, TCBP.SUSPEND_DATE, TCBP.LAPSE_DATE, TCBP.BUSI_ITEM_ID, TCBP.MASTER_BUSI_ITEM_ID, TCBP.BUSI_PROD_CODE,TCBP.LAPSE_CAUSE,TCBP.END_CAUSE,(CASE TCBP.APL_PERMIT WHEN 0 THEN '否' WHEN 1 THEN '是' END)AS APL_PERMIT
                                 ,tpas.interest_capital,tpas.interest_balance,tpas.interest_sum  

        FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
        INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
        ON TCBP.POLICY_ID = TCM.POLICY_ID
        LEFT JOIN DEV_PAS.T_CONTRACT_AGENT TA ON TA.POLICY_ID = TCM.POLICY_ID and TA.is_nb_agent = '1'
           INNER JOIN DEV_PAS.T_BENEFIT_INSURED TBI
           ON TCBP.BUSI_ITEM_ID = TBI.BUSI_ITEM_ID
           AND TCBP.POLICY_ID = TBI.POLICY_ID
              LEFT JOIN DEV_PAS.T_PRODUCT_LIABILITY_CHANGE TPLC
              ON TCBP.BUSI_ITEM_ID = TPLC.BUSI_ITEM_ID
               left join dev_pas.t_policy_account_stream tpas 
       		   on  tpas.policy_id =   TCM.Policy_Id 
      		   and tpas.busi_item_id =   TCBP.Busi_Item_Id 
       		   and tpas.regular_repay = '0'    
              WHERE TPLC.LIABILITY_CHANGED = '4' 
              AND TPLC.LIABILITY_STATUS IN ('1','2','3') --获取可能包含23终止中止
              AND TPLC.ITEM_ID IS NOT NULL --过滤数据库垃圾数据
              AND TPLC.LAPSE_CAUSE IN ('1','2','3','6'))TEMP 
		     WHERE ROWNUM<= #{LESS_NUM} 
				  ]]> 
		          <include refid="csServiceHistoryInvalidateWhereCondition" />
		  <![CDATA[ )T)P ORDER BY P.ORGAN_CODE, P.CHANNEL_TYPE, P.POLICY_CODE)PP WHERE PP.RN > #{GREATER_NUM} ]]> 
	</select>
	
	
	



	<select id="queryCsSzyblistForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
					
				SELECT PP.*
  FROM (SELECT P.*
          FROM (
                
                SELECT ROWNUM AS RN,
                        ac.accept_code,
                        ac.service_code,
                        ac.accept_time AS apply_time ,
                        (select pa.insured_name
                           from dev_pas.t_prem_arap pa
                          where pa.business_code = ac.accept_code
                            and rownum = 1) as insured_name,
                        sf.Refund_Confirm_Code,
                        (case
                          when sf.REFUND_MONEY_STATUS = '01' then
                           '医保退费成功'
                          when sf.REFUND_MONEY_STATUS = '02' then
                           '自行退费'
                          else
                           '失败'
                        end) as REFUND_MONEY_STATUS1,
                        
                        sf.FAIL_REASON,
                        sf.REFUND_MONEY_AMOUNT,
                        (case
                          when sf.REFUND_MONEY_STATUS = '01' then
                           '医保退费成功'
                          when sf.REFUND_MONEY_STATUS = '02' then
                           '自行退费'
                          else
                           '失败'
                        end) as REFUND_MONEY_STATUS,
                        sf.YB_FAIL_REASON,
                        sf.STOP_CONFIRM_CODE
                  from dev_pas.t_cs_accept_change ac
                 inner join dev_pas.t_cs_szyb_fee sf
                    on ac.accept_code = sf.accept_code
                  inner join dev_pas.t_contract_master cm
                 on cm.policy_code = sf.policy_code     
                 where ac.service_code in ('CT', 'EA', 'XT')
                   and ac.accept_status = '18'
                   and ROWNUM<= #{LESS_NUM}]]>
                   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND cm.organ_code like #{organ_code} || '%' ]]></if>  
                   <if test="batch_start_time != null and batch_start_time != ''"><![CDATA[ AND ac.validate_time BETWEEN #{batch_start_time } AND #{batch_end_time } ]]></if> 
                    <![CDATA[   ) P) PP
 WHERE PP.RN > #{GREATER_NUM}
					  ]]>
		 
	</select>
	
	<!-- 查询个数操作 -->
	<select id="queryCsSzyblistTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			  SELECT   COUNT(1) 
                  from dev_pas.t_cs_accept_change ac
                 inner join dev_pas.t_cs_szyb_fee sf
                    on ac.accept_code = sf.accept_code
                  inner join dev_pas.t_contract_master cm
                 on cm.policy_code = sf.policy_code     
                 where ac.service_code in ('CT', 'EA', 'XT')
                   and ac.accept_status = '18'
                  ]]>
                   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND cm.organ_code like #{organ_code} || '%' ]]></if>  
                   <if test="batch_start_time != null and batch_start_time != ''"><![CDATA[ AND ac.validate_time BETWEEN #{batch_start_time } AND #{batch_end_time } ]]></if> 
			
	</select>

<select id="queryRecordUploadedList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
 select * from (Select cm.organ_code, /*管理机构*/
       (select sc.sales_channel_name
          from dev_pas.T_SALES_CHANNEL sc
         where sc.sales_channel_code = cm.channel_type) as channel_type, /*销售渠道*/
       
       (case
         when cm.channel_type = '03' then
          (select (select bk.bank_name
                     from dev_pas.t_bank bk
                    where bk.bank_code = pac.account_bank)
             from dev_pas.t_payer_account pac
            where pac.policy_id = cm.policy_id)
         else
          ''
       end) as bank_name, /*银行名称*/
       (case
         when cm.channel_type = '03' then
          (select (select bbk.bank_branch_name
                     from dev_pas.t_Bank_Branch bbk
                    where bbk.bank_code = pac.account_bank
                      and rownum = '1')
             from dev_pas.t_payer_account pac
            where pac.policy_id = cm.policy_id)
         else
          ''
       end) as bank_branch_name, /*银行网点名称*/
       cm.policy_code, /*保单号*/
       ac.accept_code, /*保全受理号*/
       (select se.service_name FROM dev_pas.t_service se where se.service_code = ac.service_code ) service_code,/*保全项*/
       (select st.type_name
          from dev_pas.t_service_type st
         where st.service_type = app.service_type) as service_type, /*申请方式*/
       (select ser.service_name
          from dev_pas.t_service ser
         where ser.service_code = ac.service_code) as service_name, /*保全批改状态*/
       drvi.audit_code, /*业务员编码*/    
       drvi.audit_name, /*业务员姓名*/ 
       ca.agent_code, /*服务人员编码*/
       ta.agent_name, /*服务人员姓名*/
       ta.agent_level, /*服务人员绩优等级*/
       to_char(ac.accept_time, 'yyyy-mm-dd') as accept_time, /*保全操作日期*/
       to_char(ac.validate_time, 'yyyy-mm-dd') as validate_time, /*确认生效日期*/
        (select cdvi.up_time
            from dev_pas.t_cs_dr_relation cdr, dev_pas.t_cs_drq_video_info cdvi
           where cdr.accept_code = ac.accept_code
             and cdr.video_name = cdvi.video_name
             and cdvi.bantch_no = '0' and cdvi.video_length > 0  and rownum = '1') as up_time, /*双录首次上传日期*/
                drvi.video_length, /*时长*/
                drvi.video_size, /*大小*/
                
                (select cdvi.up_time
            from dev_pas.t_cs_dr_relation cdr, dev_pas.t_cs_drq_video_info cdvi
           where cdr.accept_code = ac.accept_code
             and cdr.video_name = cdvi.video_name
             and cdvi.bantch_no = '1' and cdvi.video_length > 0  and rownum = '1') as up_time_two, /*双录第二次上传日期*/
                
                (select cdvi.up_time
            from dev_pas.t_cs_dr_relation cdr, dev_pas.t_cs_drq_video_info cdvi
           where cdr.accept_code = ac.accept_code
             and cdr.video_name = cdvi.video_name
             and cdvi.bantch_no = '2' and cdvi.video_length > 0  and rownum = '1') as up_time_three, /*双录第三次上传日期*/
       (select (select drts.task_status
                  from dev_pas.t_DRQ_TASK_STATUS drts
                 where drts.task_code = drfo.drq_task_status)
          from dev_pas.T_CS_DR_INFO drfo
         where drfo.accept_code = ac.accept_code) as dr_task_status, /*质检状态*/
       (select to_char(drre.RECHECK_TIME, 'yyyy-mm-dd')
          from dev_pas.T_CS_DRQ_RECHECK drre
         where drre.accept_code = ac.accept_code) as RECHECK_TIME, /*最后一次质检审核日期*/
       (case
         when cm.is_self_insured = '1' then
          '是'
         else
          '否'
       end) as is_self_insured, /*双录是否自保件*/
       '' as EnroFlag, /*语音播报标识*/
       (select uu.user_name
          from dev_Pas.t_Udmp_User uu
         where uu.user_id = ac.insert_operator_id) as user_name, /*操作人员代码*/
      (select sum(tcp.total_prem_af)
                      from dev_pas.t_cs_contract_product tcp
                     where tcp.policy_chg_id = pc.policy_chg_id
                       and tcp.operation_type in  ('1','2')
                       and tcp.liability_state = '1'
                        and tcp.old_new = '1'
                       ) as total_prem_af, /*保费总额*/
       (select sum(tcpa.fee_amount)
          from dev_pas.T_CS_PREM_ARAP tcpa
         where tcpa.business_code = ac.accept_code
           ) as fee_amount, /*补费合计*/
       '保全新增' as dr_flag, /*双录标识*/
      (select ast.status_desc
          from dev_pas.t_accept_status ast
         where ast.accept_status = ac.accept_status) as status_desc ,row_number() over (partition by ac.accept_code order by drvi.up_time desc) as group_index

  from dev_pas.t_cs_accept_change ac
 inner join dev_pas.t_cs_policy_change pc
    on pc.accept_id = ac.accept_id
 inner join dev_pas.t_contract_master cm
    on cm.policy_code = pc.policy_code
 inner join dev_pas.t_cs_application app
    on app.change_id = ac.change_id
 inner join dev_pas.t_contract_agent ca
    on ca.policy_code = cm.policy_code
        and ca.is_current_agent = '1' 
 inner join dev_pas.t_agent ta
   on ta.agent_code = ca.agent_code
 inner join dev_pas.t_cs_dr_info  drinfo
  on   drinfo.accept_code = ac.accept_code   
 inner join dev_pas.T_CS_DR_RELATION csdrre
 on    csdrre.accept_code = ac.accept_code
 inner join dev_pas.T_CS_DRQ_VIDEO_INFO drvi
  on  csdrre.video_name = drvi.video_name 
   ]]>
  <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND cm.organ_code like #{organ_code} || '%' ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND  cm.channel_type =  #{channel_type} ]]></if>
		<if test="batch_start_time != null and batch_start_time != ''"><![CDATA[ AND ac.validate_time BETWEEN #{batch_start_time } AND #{batch_end_time } ]]></if>
		<if test=" agent_level != null and agent_level != ''  "><![CDATA[ AND app.agent_level =  #{agent_level} ]]></if>
		<if test="service_code != null and service_code != '' "><![CDATA[ AND ac.service_code =  #{service_code} ]]></if>
 <![CDATA[  ) b
  
  where b.group_index = '1'
  ]]>
		

	</select>

		<select id="queryRecordNotUploadedList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
select ccm.organ_code,
       (select sc.sales_channel_name
          from dev_pas.T_SALES_CHANNEL sc
         where sc.sales_channel_code = ccm.channel_type) as channel_type, /*销售渠道*/
       (case
         when ccm.channel_type = '03' then
          (select (select bk.bank_name
                     from dev_pas.t_bank bk
                    where bk.bank_code = pac.account_bank)
             from dev_pas.t_payer_account pac
            where pac.policy_id = ccm.policy_id)
         else
          ''
       end) as bank_name, /*银行名称*/
       
       (case
         when ccm.channel_type = '03' then
          (select (select bbk.bank_branch_name
                     from dev_pas.t_Bank_Branch bbk
                    where bbk.bank_code = pac.account_bank
                      and rownum = '1')
             from dev_pas.t_payer_account pac
            where pac.policy_id = ccm.policy_id)
         else
          ''
       end) as bank_branch_name, /*银行网点名称*/
       ccm.policy_code, /*保单号*/
       cac.accept_code, /*保全受理号*/
       (select se.service_name
          FROM dev_pas.t_service se
         where se.service_code = cac.service_code) service_code, /*保全项*/
       (select st.type_name
          from dev_pas.t_service_type st
         where st.service_type = app.service_type) as service_type, /*申请方式*/
       to_char(cac.accept_time, 'yyyy-mm-dd') as accept_time, /*保全操作日期*/
       (select uu.user_name
          from dev_Pas.t_Udmp_User uu
         where uu.user_id = cac.insert_operator_id) as user_name, /*操作人员代码*/
       
       (select ser.service_name
          from dev_pas.t_service ser
         where ser.service_code = cac.service_code) as service_name, /*保全批改状态*/
       (case
         when ccm.is_self_insured = '1' then
          '是'
         else
          '否'
       end) as is_self_insured, /*双录是否自保件*/
       (select sum(tcp.total_prem_af)
          from dev_pas.t_cs_contract_product tcp
         where tcp.policy_chg_id = cpc.policy_chg_id
           and tcp.operation_type in  ('1','2')
           and tcp.liability_state = '1'
            and tcp.old_new = '1'
           ) as total_prem_af, /*保费总额*/
          (select sum(tcpa.fee_amount)
          from dev_pas.T_CS_PREM_ARAP tcpa
         where tcpa.business_code = cac.accept_code
           ) as fee_amount, /*补费合计*/
           ca.agent_code, /*服务人员编码*/
           ta.agent_name, /*服务人员姓名*/
           ta.agent_level, /*服务人员绩优等级*/
       '保全新增' as dr_flag, /*双录标识*/
       (select ast.status_desc
          from dev_pas.t_accept_status ast
         where ast.accept_status = cac.accept_status) as status_desc
  from dev_pas.t_cs_dr_info         cdi,
       dev_pas.t_cs_accept_change   cac,
       dev_pas.t_cs_policy_change   cpc,
       dev_pas.t_cs_contract_master ccm,
       dev_pas.t_cs_application     app,
       dev_pas.t_contract_agent     ca,
       dev_pas.t_agent              ta
 where cac.accept_code = cdi.accept_code
   and cpc.accept_id = cac.accept_id
   and app.change_id = cac.change_id
   and ca.policy_code = ccm.policy_code
   and ta.agent_code = ca.agent_code
   and ca.is_current_agent = '1'
   and ccm.policy_chg_id = cpc.policy_chg_id
   and ccm.old_new = '1'
   and not exists
 (select 1
          from dev_pas.t_cs_dr_info cdi, dev_pas.t_cs_dr_relation cdr
         where cdr.accept_code = cdi.accept_code
           and cdr.accept_code = cac.accept_code) 
	 ]]>

		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND ccm.organ_code like #{organ_code} || '%' ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND  ccm.channel_type =  #{channel_type} ]]></if>
		<if test="batch_start_time != null and batch_start_time != ''"><![CDATA[ AND cac.validate_time BETWEEN #{batch_start_time } AND #{batch_end_time } ]]></if>
		<if test=" agent_level != null and agent_level != ''  "><![CDATA[ AND app.agent_level =  #{agent_level} ]]></if>
		<if test="service_code != null and service_code != '' "><![CDATA[ AND cac.service_code =  #{service_code} ]]></if>

	</select>

	<select id="queryFailedQualityInspectionList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	  select distinct cac.accept_code,/*保全受理号*/
       ccm.organ_code, /*管理机构*/
       (select sc.sales_channel_name
          from dev_pas.T_SALES_CHANNEL sc
         where sc.sales_channel_code = ccm.channel_type) as channel_type, /*销售渠道*/
       (case
         when ccm.channel_type = '03' then
          (select (select bk.bank_name
                     from dev_pas.t_bank bk
                    where bk.bank_code = pac.account_bank)
             from dev_pas.t_payer_account pac
            where pac.policy_id = ccm.policy_id)
         else
          ''
       end) as bank_name, /*银行名称*/
       (case
         when ccm.channel_type = '03' then
          (select (select bbk.bank_branch_code
                     from dev_pas.t_Bank_Branch bbk
                    where bbk.bank_code = pac.account_bank
                      and rownum = '1')
             from dev_pas.t_payer_account pac
            where pac.policy_id = ccm.policy_id)
         else
          ''
       end) as bank_branch_code, /*网点代码*/
       (case
         when ccm.channel_type = '03' then
          (select (select bbk.bank_branch_name
                     from dev_pas.t_Bank_Branch bbk
                    where bbk.bank_code = pac.account_bank
                      and rownum = '1')
             from dev_pas.t_payer_account pac
            where pac.policy_id = ccm.policy_id)
         else
          ''
       end) as bank_branch_name, /*银行网点名称*/
       (select se.service_name FROM dev_pas.t_service se where se.service_code = cac.service_code ) service_code,/*保全项*/
       
       ccm.policy_code, /*保单号*/
        (case
          when cpc.hesitate_flag = '2' and
               cac.service_code in ('CT', 'XT', 'EA') then
           '是'
          else
           '否'
        end) as hesitation_period, /*是否犹豫期内退保*/
        (case
          when cpc.hesitate_flag = '3' and
               cac.service_code in ('CT', 'XT', 'EA') then
           '是'
          else
           '否'
        end) as beyond_hesitation, /*是否犹豫期外退保*/
        (case
          when cac.accept_status = '19' then
           '是'
          else
           '否'
        end) as is_rb, /*是否保全回退*/
        (case
          when app.service_type = '10' then
           '是'
          else
           '否'
        end) as is_ybt, /*是否银保通业务*/
        (select (select cu.customer_name
                   from dev_pas.t_customer cu
                  where cu.customer_id = ph.customer_id)
           from dev_pas.t_policy_holder ph
          where ph.policy_code = ccm.policy_code) as customer_name, /*投保人姓名*/
        drvi.audit_code, /*业务员编码*/    
        drvi.audit_name, /*业务员姓名*/ 
        ca.agent_code, /*服务人员编码*/
        ta.agent_name, /*服务人员姓名*/
        ta.agent_level, /*服务人员绩优等级*/
        to_char(cac.accept_time, 'yyyy-mm-dd') as accept_time, /*保全操作日期*/
        (select uu.user_name
           from dev_Pas.t_Udmp_User uu
          where uu.user_id = cac.insert_operator_id) as user_name, /*操作人员代码*/
          (select st.type_name
                    from dev_pas.t_service_type st
                   where st.service_type = app.service_type) as type_name,/*申请方式*/ 
        csdrin.audit_user, /*质检审核人代码*/
       ((select count(*)
                      from dev_pas.T_CS_DRQ_TASK_TRACE drqtt
                     where drqtt.accept_code = cac.accept_code and drqtt.task_status = '3') - 
       (select count(*)
                      from dev_pas.T_CS_DRQ_TASK_TRACE drqtt1
                     where drqtt1.accept_code = cac.accept_code and drqtt1.task_status = '5'  )          
                     ) as quality_frequency,/*质检不通过次数*/ 
      (select count(*)
                      from dev_pas.T_CS_DRQ_TASK_TRACE drqtt
                     where drqtt.accept_code = cac.accept_code and drqtt.task_status = '3'
                      ) as recheck_frequency, /*质检次数*/
        
        (case
          when ccm.is_self_insured = '1' then
           '是'
          else
           '否'
        end) as is_self_insured, /*双录是否自保件*/
        '' as EnroFlag, /*语音播报标识*/
       (select sum(tcp.total_prem_af)
                      from dev_pas.t_cs_contract_product tcp
                     where tcp.policy_chg_id = cpc.policy_chg_id
                           and tcp.operation_type in  ('1','2')
                           and tcp.old_new = '1'
                       and tcp.liability_state = '1') as total_prem_af, /*保费总额*/
       (select sum(tcpa.fee_amount)
          from dev_pas.T_CS_PREM_ARAP tcpa
         where tcpa.business_code = cac.accept_code
           ) as fee_amount, /*补费合计*/               
        '保全新增' as dr_flag /*双录标识*/
  from dev_pas.t_cs_accept_change   cac,
       dev_pas.t_cs_policy_change   cpc,
       dev_pas.t_cs_contract_master ccm,
       dev_pas.t_cs_application app,
       dev_pas.t_contract_agent ca,
       dev_pas.T_CS_DR_INFO csdrin,
       dev_pas.T_CS_DRQ_VIDEO_INFO drvi,
       dev_pas.t_cs_dr_relation csdrre,
       dev_pas.t_agent ta
 where 1 = 1
   and cac.accept_id = cpc.accept_id
   and cpc.policy_chg_id = ccm.policy_chg_id
   and app.change_id = cac.change_id
   and ca.policy_code = ccm.policy_code
   and ca.is_current_agent = '1'
   and ccm.old_new = '1'
   and csdrin.accept_code = cac.accept_code
   and drvi.video_name = csdrre.video_name
   and csdrin.accept_code = csdrre.accept_code
   and ta.agent_code = ca.agent_code
   and csdrre.validate_flag = '1'
   and exists (select 1
          from dev_pas.T_CS_DRQ_TASK_TRACE l
         where l.accept_code = cac.accept_code
           and l.task_status in ('3', '4', '6'))
  
  and not exists (select 1
              from dev_pas.T_CS_DRQ_TASK_TRACE l
              where l.accept_code = cac.accept_code
               and l.task_status  in('5')
           )    
                
	
	 ]]>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND ccm.organ_code like #{organ_code} || '%' ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND  ccm.channel_type =  #{channel_type} ]]></if>
		<if test="batch_start_time != null and batch_start_time != ''"><![CDATA[ AND cac.validate_time BETWEEN #{batch_start_time } AND #{batch_end_time } ]]></if>
		<if test=" agent_level != null and agent_level != ''  "><![CDATA[ AND app.agent_level =  #{agent_level} ]]></if>
		<if test="service_code != null and service_code != '' "><![CDATA[ AND cac.service_code =  #{service_code} ]]></if>

	</select>
	<select id="cs_queryContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
select cbp.busi_prod_code,
       cscp.std_prem_af as TOTAL_PREM_AF,
       tcpa.fee_amount
  from dev_pas.t_cs_accept_change csac
 inner join dev_pas.t_cs_policy_change cspc
    on cspc.accept_id = csac.accept_id
 inner join dev_Pas.t_Cs_Contract_Busi_Prod cbp
    on cbp.policy_chg_id = cspc.policy_chg_id
   and cbp.policy_code = cspc.policy_code
 inner join dev_pas.t_Cs_Contract_Product cscp
    on cspc.policy_code = cscp.policy_code
   and cscp.policy_chg_id = cspc.policy_chg_id
   and cscp.busi_item_id = cbp.busi_item_id
   and cscp.old_new = '1'
   and cscp.operation_type = '1'
  left join dev_pas.t_cs_prem_arap tcpa
    on csac.accept_code = tcpa.business_code
    and tcpa.busi_prod_code = cbp.busi_prod_code
 where csac.accept_code = #{accept_code}

union

select (select t.product_code_sys
          from dev_pas.t_business_product t
         where t.business_prd_id = rc.new_busi_prd_id) as BUSI_PROD_CODE,
       rc.new_std_prem_af as TOTAL_PREM_AF,
       tcpa.fee_amount
  from dev_pas.t_cs_accept_change cac
 inner join dev_pas.t_cs_policy_change cpc
    on cac.accept_id = cpc.accept_id
 inner join dev_pas.t_renew_change rc
    on cpc.policy_chg_id = rc.policy_chg_id
  left join dev_pas.t_cs_prem_arap tcpa
    on cac.accept_code = tcpa.business_code
     and tcpa.busi_prod_code = BUSI_PROD_CODE
 where cac.accept_code = #{accept_code}

    ]]>
	</select>

	<select id="cs_queryCsDrqResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	select l.*
  from dev_Pas.T_CS_DRQ_TASK_TRACE l
 where l.accept_code = #{accept_code}
 order by l.insert_time desc 
	
  
    ]]>
	</select>
	<select id="cs_queryCsDrqRecheck" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	select l.*
  from dev_Pas.T_CS_DRQ_TASK_TRACE l
 where l.accept_code = #{accept_code}
 order by l.insert_time asc

    ]]>
	</select>
	
	<!-- 历史失效清单job任务 -->
  <select id="queryCsServiceHistoryInvalidateOffLine" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT PP.* FROM (SELECT P.* FROM (		SELECT ROWNUM AS RN,
                           (SELECT A.FINISH_TIME
                               FROM DEV_PAS.T_POLICY_CHANGE A
                              WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID) BATCH_DATE, /* 批处理日期*/
                            
                           (SELECT B.SERVICE_NAME
                               FROM DEV_PAS.T_POLICY_CHANGE      A,
                                    DEV_PAS.T_BACK_CROSS_SERVICE B
                              WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID
                                AND A.SERVICE_CODE = B.SERVICE_CODE) BATCH_NAME, /* 批处理名称*/
                            
                            
                         /*销售渠道名称*/
                         (SELECT SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL WHERE T_SALES_CHANNEL.SALES_CHANNEL_CODE = T.CHANNEL_TYPE) 
                                 AS CHANNEL_TYPES,
                         /*保单管理机构代码*/
                         T.ORGAN_CODE AS ORGAN_CODE,
                         /*保单管理机构名称*/
                         ((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE SUBSTR(T.ORGAN_CODE, 0, 2) = GI.ORGAN_CODE)||        
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 4)) = 4 AND SUBSTR(T.ORGAN_CODE, 0, 4) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 4)) = 4 AND SUBSTR(T.ORGAN_CODE, 0, 4) = GI.ORGAN_CODE),'')||
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 6)) = 6 AND SUBSTR(T.ORGAN_CODE, 0, 6) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 6)) = 6 AND SUBSTR(T.ORGAN_CODE, 0, 6) = GI.ORGAN_CODE),'')||
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 8)) = 8 AND SUBSTR(T.ORGAN_CODE, 0, 8) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(T.ORGAN_CODE, 0, 8)) = 8 AND SUBSTR(T.ORGAN_CODE, 0, 8) = GI.ORGAN_CODE),'')) 
                                  AS ORGAN_NAME,
                         /*保单销售机构代码*/
                         (SELECT C.AGENT_ORGAN_CODE FROM DEV_PAS.T_CONTRACT_AGENT A, DEV_PAS.T_AGENT C WHERE C.AGENT_CODE = A.AGENT_CODE
                                 AND A.IS_NB_AGENT = 1 AND A.POLICY_ID = T.POLICY_ID) 
                                 AS CHANNEL_ORG_CODE,
                         /*保单销售机构名称*/
                         (SELECT ((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE SUBSTR(C.AGENT_ORGAN_CODE, 0, 2) = GI.ORGAN_CODE)||        
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 4)) = 4 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 4) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 4)) = 4 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 4) = GI.ORGAN_CODE),'')||
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 6)) = 6 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 6) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 6)) = 6 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 6) = GI.ORGAN_CODE),'')||
                          NVL2((SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 8)) = 8 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 8) = GI.ORGAN_CODE), '-' || (SELECT GI.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG GI WHERE LENGTH(SUBSTR(C.AGENT_ORGAN_CODE, 0, 8)) = 8 AND SUBSTR(C.AGENT_ORGAN_CODE, 0, 8) = GI.ORGAN_CODE),''))
                                          FROM DEV_PAS.T_CONTRACT_AGENT A,DEV_PAS.T_AGENT C WHERE C.AGENT_CODE = A.AGENT_CODE
                                          AND A.IS_NB_AGENT = 1 AND A.POLICY_ID = T.POLICY_ID) 
                                          AS CHANNEL_ORG_NAME, 
                         /*投保人性别*/ 
                         (SELECT DECODE(TC.CUSTOMER_GENDER, 1, '男', 2, '女', 9, '未知') FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_CUSTOMER TC
                              WHERE TPH.POLICY_ID = T.POLICY_ID AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                              AS HOLDER_GENDER,   
                         /*投保人出生日期*/  
                         (SELECT TC.CUSTOMER_BIRTHDAY FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_CUSTOMER TC
                              WHERE TPH.POLICY_ID = T.POLICY_ID AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                              AS HOLDER_BIRTHDAY, 
                         /*投保人姓名*/
                         (SELECT TC.CUSTOMER_NAME FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_CUSTOMER TC WHERE TPH.POLICY_ID = T.POLICY_ID AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                                 AS HOLDER_NAME,
                         /*投保人移动电话*/
                         (SELECT TC.MOBILE_TEL FROM DEV_PAS.T_POLICY_HOLDER TPH,DEV_PAS.T_CUSTOMER TC WHERE TPH.POLICY_ID = T.POLICY_ID
                                 AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                                 AS HOLDER_MOBILE_TEL,
                         /*需求26132添加是否自垫标识*/
                         T.APL_PERMIT AS APL_PERMIT, 
                         /*需求26132添加被保人移动电话*/
                         (SELECT TC.MOBILE_TEL FROM DEV_PAS.T_CUSTOMER TC INNER JOIN DEV_PAS.T_INSURED_LIST TIL
                                 ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID WHERE TIL.LIST_ID = T.INSURED_ID AND T.ORDER_ID = '1'/*第一被保人*/) 
                                 AS INSURED_MOBILE_TEL, 
                         /*需求26132添加主险交费方式--多行重复*/
                        --(CASE (SELECT TP.PAY_LOCATION FROM DEV_PAS.T_PREM TP WHERE TP.POLICY_CODE = T.POLICY_CODE AND TP.IS_RISK_MAIN = 1/*主险*/ AND ROWNUM = 1) 
                        (CASE (SELECT TP.PAY_LOCATION FROM DEV_PAS.T_PAYER_ACCOUNT TP WHERE TP.POLICY_ID = T.POLICY_ID AND ROWNUM = 1)     
                              WHEN  '0' THEN '委托银行转账'
                              WHEN  '1' THEN '自行缴纳'
                              WHEN  '2' THEN '上门收费'
                              WHEN  '3' THEN '社保缴纳' END) AS MASTER_PAY_LOCATION,
                         /*需求26132添加与被保人关系*/
                         (CASE (SELECT TIL.RELATION_TO_PH FROM DEV_PAS.T_INSURED_LIST TIL WHERE TIL.POLICY_CODE=T.POLICY_CODE /*去重*/AND ROWNUM=1)
                            WHEN '00' THEN '本人'
                            WHEN '01' THEN  '父子'
                            WHEN  '02' THEN '父女'
                            WHEN  '03' THEN '母子'
                            WHEN  '04' THEN '母女'
                            WHEN  '05' THEN '祖孙'
                            WHEN  '07' THEN '夫妻'
                            WHEN  '08' THEN '兄弟'
                            WHEN  '09' THEN '兄妹'
                            WHEN  '10' THEN '姐弟'
                            WHEN  '11' THEN '姐妹'
                            WHEN  '12' THEN '叔侄'
                            WHEN  '13' THEN '姑侄'
                            WHEN  '14' THEN '外甥'
                            WHEN  '15' THEN '媳'
                            WHEN  '16' THEN '婿'
                            WHEN  '17' THEN '姐夫'
                            WHEN  '18' THEN '朋友'
                            WHEN  '19' THEN '同事'
                            WHEN  '20' THEN '师生'
                            WHEN  '21' THEN '劳动关系'
                            WHEN  '22' THEN '其他'
                            WHEN  '23' THEN '法定'
                            WHEN  '24' THEN '子女'
                            WHEN  '25' THEN '父母'
                         END) AS RELATION_TO_INSURED,
                         /*投保人固定电话 */
                         (SELECT TC.OFFEN_USE_TEL FROM DEV_PAS.T_POLICY_HOLDER TPH,DEV_PAS.T_CUSTOMER TC WHERE TPH.POLICY_ID = T.POLICY_ID
                                 AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID) 
                                 AS HOLDER_HOUSE_TEL, 
                         /*被保人姓名*/ 
                         (SELECT TC.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER TC INNER JOIN DEV_PAS.T_INSURED_LIST TIL ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                                 WHERE TIL.LIST_ID = T.INSURED_ID AND T.ORDER_ID = '1') 
                                 AS INSURED_NAME,
                          /*被保人性别*/ 
                         (SELECT DECODE(TC.CUSTOMER_GENDER, 1, '男', 2, '女', 9, '未知') FROM DEV_PAS.T_CUSTOMER TC INNER JOIN DEV_PAS.T_INSURED_LIST TIL
                                 ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID WHERE TIL.LIST_ID = T.INSURED_ID AND T.ORDER_ID = '1') 
                                 AS INSURED_GENDER,   
                         /*被保人出生日期*/                           
                        (SELECT TC.CUSTOMER_BIRTHDAY FROM DEV_PAS.T_CUSTOMER TC INNER JOIN DEV_PAS.T_INSURED_LIST TIL ON TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                                 WHERE TIL.LIST_ID = T.INSURED_ID AND T.ORDER_ID = '1') 
                                 AS INSURED_BIRTHDAY, 
                         /*被保人投保年龄*/                            
                         TO_CHAR((SELECT TIL.INSURED_AGE FROM DEV_PAS.T_INSURED_LIST TIL WHERE TIL.LIST_ID = T.INSURED_ID))
                                 AS INSURED_APPLY_AGE, 
                         /*主附险标识*/
                         (CASE WHEN T.MASTER_BUSI_ITEM_ID IS NULL THEN '主险' ELSE '附加险' END)
                               AS MASTERFLAG_MASTER,  
                         /*险种名称 */
                         (SELECT TBP.PRODUCT_NAME_STD FROM DEV_PAS.T_BUSINESS_PRODUCT TBP WHERE TBP.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE) 
                                 AS BUSI_PROD_NAME, 
                         /*责任组信息 */
                         (SELECT TCP.PRODUCT_CODE || '-' || TPL.PRODUCT_NAME 
                                 FROM DEV_PAS.T_PRODUCT_LIFE TPL,DEV_PAS.T_CONTRACT_PRODUCT TCP
                                 WHERE TPL.PRODUCT_ID = TCP.PRODUCT_ID AND TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID AND ROWNUM = 1) 
                                 AS PRODUCT_ITEM, 
                         /*险种状态*/
                         (SELECT TLS.STATUS_NAME FROM DEV_PAS.T_LIABILITY_STATUS TLS WHERE TLS.STATUS_CODE = T.LIABILITY_STATE) 
                                 AS BUSI_LIABILITY_STATE,
                         /*保单状态*/  
                         (SELECT TLS.STATUS_NAME FROM DEV_PAS.T_LIABILITY_STATUS TLS WHERE TLS.STATUS_CODE = T.LIABILITY_STATE) 
                                 AS POLICY_LIABILITY_STATE,
                         /*保单状态码*/  
                         T.LIABILITY_STATE AS LIABILITY_STATE,
                         /*保额*/
                         (SELECT SUM(TCP.AMOUNT) FROM DEV_PAS.T_CONTRACT_PRODUCT TCP WHERE TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID) 
                                 AS AMOUNT,
                         /*保费*/
                         (SELECT SUM(TCP.TOTAL_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT TCP WHERE TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID) 
                                 AS TOTAL_PREM_AF, 
                          /*新契约业务员信息*/
                         (SELECT TA.AGENT_CODE || '-' || TA.AGENT_NAME || '-' || CASE WHEN T.CHANNEL_TYPE = '03' THEN (T.SERVICE_BANK_BRANCH || '-' || T.SERVICE_BANK) ELSE
                          (SELECT TO_CHAR(LISTAGG(DD.SALES_ORGAN_NAME,'-') WITHIN GROUP(ORDER BY DD.ORGAN_LEVEL_CODE ASC)) FROM ( SELECT *
				             FROM DEV_PAS.T_SALES_ORGAN TSO
				             where TSO.PARENT_CODE<>TSO.SALES_ORGAN_CODE ) DD
				            START WITH DD.SALES_ORGAN_CODE = TA.SALES_ORGAN_CODE
				           CONNECT BY PRIOR DD.PARENT_CODE = DD.SALES_ORGAN_CODE  
           						) END
                            FROM DEV_PAS.T_CONTRACT_AGENT TCA, DEV_PAS.T_AGENT TA WHERE TA.AGENT_CODE = TCA.AGENT_CODE
                            AND TCA.IS_NB_AGENT = 1 AND TCA.POLICY_ID = T.POLICY_ID) 
                            AS POLICY_AGENT_MSG, 
                          /*服务业务员信息*/
                         (SELECT TA.AGENT_CODE || '-' || TA.AGENT_NAME || '-' || CASE WHEN T.CHANNEL_TYPE = '03' THEN (T.SERVICE_BANK_BRANCH || '-' || T.SERVICE_BANK) ELSE 
                                 (
                                 SELECT TO_CHAR(LISTAGG(DD.SALES_ORGAN_NAME,'-') WITHIN GROUP(ORDER BY DD.ORGAN_LEVEL_CODE ASC)) FROM ( SELECT *
				             FROM DEV_PAS.T_SALES_ORGAN TSO
				             where TSO.PARENT_CODE<>TSO.SALES_ORGAN_CODE ) DD
				            START WITH DD.SALES_ORGAN_CODE = TA.SALES_ORGAN_CODE
				           CONNECT BY PRIOR DD.PARENT_CODE = DD.SALES_ORGAN_CODE  
                                 ) END
                          FROM DEV_PAS.T_CONTRACT_AGENT TCA, DEV_PAS.T_AGENT TA
                          WHERE TA.AGENT_CODE = TCA.AGENT_CODE AND TCA.IS_CURRENT_AGENT = 1 AND TCA.POLICY_ID = T.POLICY_ID) 
                                AS POLICY_SERVICE_MSG,
                            T.BUSI_CASH_VALUE AS LOAN_APPLY_CASH_VALUE, /*现金价值*/ 
                            T.interest_balance   AS INIT_LOAN_INTEREST, /*利息*/
                            T.interest_capital    AS LOAN_INTEREST_CAPITAL, /*贷款本息和*/
                            T.POLICY_CODE AS POLICY_CODE, /*保险单号 */
                             (CASE WHEN T.MULTI_MAINRISK_FLAG = 1 THEN '是' ELSE '否' END) AS MULTI_MAINRISK_FLAG_NAME, /*多主险标识*/
                            T.VALIDATE_DATE  AS POLICY_VALIDATE_DATE, /*保单生效日期*/ 
                            T.LAPSE_DATE     AS POLICY_LAPSE_DATE, /* 保单失效日期  */
                            T.VALIDATE_DATE AS BUSI_VALIDATE_DATE, /*险种生效日期  */
                            T.LAPSE_DATE    AS BUSI_LAPSE_DATE, /*险种失效日期  */
                            T.BUSI_PROD_CODE AS BUSI_PROD_CODE, /* 险种代码*/         
                            T.CHANNEL_TYPE, /*销售渠道代码*/
                            (SELECT A.PAY_DUE_DATE
                               FROM DEV_PAS.T_CONTRACT_EXTEND A
                              WHERE A.POLICY_ID = T.POLICY_ID
                                AND A.BUSI_ITEM_ID = T.BUSI_ITEM_ID /*去重*/AND ROWNUM=1) PAY_DUE_DATE, /*险种下期缴费日期  */
                            (SELECT A.POLICY_PERIOD
                               FROM DEV_PAS.T_CONTRACT_EXTEND A
                              WHERE A.POLICY_ID = T.POLICY_ID
                                AND A.BUSI_ITEM_ID = T.BUSI_ITEM_ID /*去重*/AND ROWNUM=1) POLICY_PERIOD, /*已交保费期数*/
                            (SELECT SUM(TCP.TOTAL_PREM_AF)
                               FROM DEV_PAS.T_CONTRACT_PRODUCT TCP
                              WHERE TCP.BUSI_ITEM_ID = T.BUSI_ITEM_ID) AS ACCUMULATE_PREM, /*已交保费金额*/
                            (SELECT TLC.CAUSE_DESC
                               FROM DEV_PAS.T_LAPSE_CAUSE TLC
                              WHERE TLC.CAUSE_CODE = T.LAPSE_CAUSE) AS POLICY_LAPSE_CAUSE_NAME, /*保单失效原因*/
                            (SELECT TLC.CAUSE_DESC
                               FROM DEV_PAS.T_LAPSE_CAUSE TLC
                              WHERE TLC.CAUSE_CODE = T.LAPSE_CAUSE) AS BUSI_LAPSE_CAUSE_NAME, /*险种失效原因*/
                             /*贷款起期*/
                            (SELECT TPAS.LOAN_START_DATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4) AS LOAN_FIRST_TRANS_DATE,
                                /*贷款起息日*/
                       			(SELECT (SELECT (CASE
					                 WHEN CSPAS.INTEREST_START_DATE IS NOT NULL THEN
					                  CSPAS.INTEREST_START_DATE
					                 ELSE
					                  (CASE
					                    WHEN CSAC.SERVICE_CODE = 'LN' AND
					                         TO_DATE(CI.CONSTANTS_VALUE, 'YYYY-MM-DD') <
					                         CSPC.VALIDATE_TIME THEN
					                     (SELECT TRUNC(L.FINISH_TIME)
					                        FROM DEV_PAS.T_PREM_ARAP L
					                       WHERE L.BUSINESS_CODE = CSAC.ACCEPT_CODE
					                         AND ROWNUM = '1')
					                    ELSE
					                     CSPC.APPLY_TIME
					                  END)
					               END)
					          FROM DEV_PAS.T_CONSTANTS_INFO CI
					         WHERE CI.CONSTANTS_KEY = 'LOAN_GO_ONLINE_FRONTGE') AS INTEREST_START_DATE
                          from  dev_pas.t_cs_accept_change csac ,
                                dev_pas.t_Cs_Policy_Change         cspc,
                                dev_PAs.t_Cs_Policy_Account_Stream cspas,
                                dev_pas.t_policy_Account_Stream    tpas
                         where  cspc.policy_chg_id = cspas.policy_chg_id
                           and csac.accept_id = cspc.accept_id 
                           and T.POLICY_ID = cspc.policy_id
                           and cspas.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                           and tpas.busi_item_id = T.BUSI_ITEM_ID
                           and tpas.policy_id = T.POLICY_ID
                           and tpas.stream_id = cspas.stream_id
                           and cspas.regular_repay = '0'
                           and cspas.account_type = '4'
                           and cspas.old_new = '1'
                           and tpas.regular_repay = '0'
                           and tpas.account_type = '4'
                           and cspc.service_code in ('LN', 'RL')
                           and csac.accept_status = '18'
                           and rownum = '1') AS INTEREST_START_DATE, 
                            /*贷款止期*/
                            (SELECT TPAS.REPAY_DUE_DATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4) AS LOAN_REPAY_DUE_DATE, 
                            /*贷款本金*/
                            (SELECT TPAS.CAPITAL_BALANCE
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4) AS LOAN_CAPITAL_BALANCE, 
                             /*贷款限额*/
                            (SELECT TPAS.LOAN_LIMIT
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4) AS LOAN_AMOUNT_LIMIT,
                            /*初始利率*/
                            (SELECT TPASR.LOAN_RATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT             TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM      TPAS,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM_RATE TPASR
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPASR.STREAM_ID = TPAS.STREAM_ID
                                AND TPASR.TIME_PERIDO_CODE = '1'
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4) AS INIT_LOAN_RATE, 
                            /*逾期利率*/
                            (SELECT TPASR.LOAN_RATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT             TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM      TPAS,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM_RATE TPASR
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPASR.STREAM_ID = TPAS.STREAM_ID
                                AND TPASR.TIME_PERIDO_CODE = '2'
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4) AS SECOND_LOAN_RATE, 
                             /*三阶段利率*/
                            (SELECT TPASR.LOAN_RATE
                               FROM DEV_PAS.T_POLICY_ACCOUNT             TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM      TPAS,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM_RATE TPASR
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.POLICY_ID = T.POLICY_ID
                                AND TPAS.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPASR.STREAM_ID = TPAS.STREAM_ID
                                AND TPASR.TIME_PERIDO_CODE = '3'
                                AND TPAS.REGULAR_REPAY = 0
                                AND TPA.ACCOUNT_TYPE = 4) AS THIRD_LOAN_RATE,
                             /*可贷比例*/
                            (SELECT MAX(TLPC.MAX_LOAN_RATIO)
                               FROM DEV_PAS.T_LOAN_POLICY_CFG TLPC
                              WHERE TLPC.POLICY_ID = T.POLICY_ID
                                AND TLPC.BUSI_ITEM_ID = T.BUSI_ITEM_ID) AS MAX_LOAN_RATIO, 
                            /* 逾期利息 
                            (SELECT TEMP.INTEREST_SUM FROM (SELECT TPAS.INTEREST_SUM,TPA.BUSI_ITEM_ID,TPA.POLICY_ID FROM DEV_PAS.T_POLICY_ACCOUNT TPA, DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                                   WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID AND TPA.ACCOUNT_TYPE = 4 ORDER BY TPAS.UPDATE_TIME)TEMP 
                               WHERE ROWNUM=1 AND TEMP.BUSI_ITEM_ID = T.BUSI_ITEM_ID AND TEMP.POLICY_ID = T.POLICY_ID) 
                                AS INTEREST_OVERDUE*/
                                
                                
                            (SELECT TPAS.INTEREST_SUM FROM DEV_PAS.T_POLICY_ACCOUNT TPA, DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                                   WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID AND TPA.ACCOUNT_TYPE = 4 AND TPA.BUSI_ITEM_ID = T.BUSI_ITEM_ID AND TPA.POLICY_ID = T.POLICY_ID   and TPAS.Regular_Repay = '0'   AND ROWNUM=1  ) 
                                AS INTEREST_OVERDUE
                                
                            /*(SELECT CASE
                                      WHEN TPAS.INTEREST_SUM - T.LOAN_INTEREST >= 0 THEN
                                       TPAS.INTEREST_SUM - T.LOAN_INTEREST
                                      ELSE
                                       0
                                    END
                               FROM DEV_PAS.T_POLICY_ACCOUNT        TPA,
                                    DEV_PAS.T_POLICY_ACCOUNT_STREAM TPAS
                              WHERE TPA.ACCOUNT_ID = TPAS.ACCOUNT_ID
                                AND TPA.BUSI_ITEM_ID = T.BUSI_ITEM_ID
                                AND TPA.ACCOUNT_TYPE = 4) AS INTEREST_OVERDUE*/
                               
              FROM (
              SELECT * FROM( 
                SELECT DISTINCT TCM.POLICY_CODE, TCM.MULTI_MAINRISK_FLAG, TCM.POLICY_ID, TCM.CHANNEL_TYPE, TCM.LIABILITY_STATE,TCM.VALIDATE_DATE,TCM.SERVICE_BANK_BRANCH,TCM.SERVICE_BANK,TCM.ORGAN_CODE,
                                TBI.INSURED_ID, TBI.ORDER_ID,
                                TPLC.BUSI_CASH_VALUE,TPLC.PAY_DUE,TPLC.LOAN_CAPITAL,TPLC.LOAN_INTEREST,TPLC.POLICY_CHG_ID,--因ID会造成数据重复
                                TCBP.EXPIRY_DATE, TCBP.SUSPEND_DATE, TCBP.LAPSE_DATE, TCBP.BUSI_ITEM_ID, TCBP.MASTER_BUSI_ITEM_ID, TCBP.BUSI_PROD_CODE,TCBP.LAPSE_CAUSE,TCBP.END_CAUSE,(CASE TCBP.APL_PERMIT WHEN 0 THEN '否' WHEN 1 THEN '是' END)AS APL_PERMIT
                                ,tpas.interest_capital,tpas.interest_balance,tpas.interest_sum 

        FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
        INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
        ON TCBP.POLICY_ID = TCM.POLICY_ID
           INNER JOIN DEV_PAS.T_BENEFIT_INSURED TBI
           ON TCBP.BUSI_ITEM_ID = TBI.BUSI_ITEM_ID
           AND TCBP.POLICY_ID = TBI.POLICY_ID
              LEFT JOIN DEV_PAS.T_PRODUCT_LIABILITY_CHANGE TPLC
              ON TCBP.BUSI_ITEM_ID = TPLC.BUSI_ITEM_ID 
               left join dev_pas.t_policy_account_stream tpas 
       		   on  tpas.policy_id =   TCM.Policy_Id 
      		   and tpas.busi_item_id =   TCBP.Busi_Item_Id 
       		   and tpas.regular_repay = '0'   
              WHERE TPLC.LIABILITY_CHANGED = '4' 
              AND TPLC.LIABILITY_STATUS IN ('1','2','3') --获取可能包含23终止中止
              AND TPLC.ITEM_ID IS NOT NULL --过滤数据库垃圾数据
              AND TPLC.LAPSE_CAUSE IN ('1','2','3','6') 
              )TEMP 
              
				 WHERE 1=1
				  ]]> 
		         <include refid="csServiceHistoryInvalidateWhereCondition" /> 
		  <![CDATA[ )T)P ORDER BY P.ORGAN_CODE, P.CHANNEL_TYPE, P.POLICY_CODE)PP ]]> 
		
		
		
		</select>
	
</mapper>