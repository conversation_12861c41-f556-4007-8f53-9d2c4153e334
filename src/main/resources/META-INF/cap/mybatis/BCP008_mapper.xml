<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="BCP008">

    <!-- 内部转账—应收保费-->
    <select id="findBcp008" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT NVL(SUM(CASE WHEN G.BUSI_PROD_TYPE3_CODE='1' THEN T.FEE_AMOUNT 
      	       WHEN G.BUSI_PROD_TYPE3_CODE='2' 
      		   AND EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1') 
      		   THEN T.FEE_AMOUNT
      	       ELSE 0 END),0) NUMBER1,
      		   NVL(SUM(CASE WHEN G.BUSI_PROD_TYPE3_CODE='2' 
      		   AND NOT EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1') 
      		   THEN T.FEE_AMOUNT 
      		   ELSE 0 END),0) NUMBER2
        FROM DEV_CAP.T_CASH_DETAIL T, DEV_PDS.T_BUSINESS_PRODUCT B, DEV_PDS.T_BUSI_PROD_TYPE_CONFIG G
        WHERE T.FEE_TYPE IN ('G001100000') AND T.WITHDRAW_TYPE IN ('0010800000')
        AND B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE
        AND SUBSTR(T.BUSI_PROD_CODE,3,3) = G.BUSI_PROD_CODE 
        AND T.PAY_MODE = '50'
        AND T.ARAP_FLAG = '2'
        AND T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE 
              FROM DEV_PAS.T_UDMP_ORG_REL O
              START WITH O.ORGAN_CODE=#{organ_code}
              CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
        AND T.FINISH_TIME>=TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
		AND T.FINISH_TIME<TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1
        AND T.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O START WITH O.ORGAN_CODE=#{cap_organ_code}
                    CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
      ]]>
    </select>
    
    
    <!-- 内部转帐—应收质押贷款及利息 -->
    <select id="findBcp008a" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' 
          THEN DECODE(C.IS_POSITIVE,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' 
          THEN DECODE(C.IS_POSITIVE,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND T.ARAP_FLAG = '1'
	 AND C.ARAP_FLAG = '2'
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ITEM_CODE = 'NBZZ-YSZYDKJLX'
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	   WHERE  D.ARAP_FLAG IN ('2') 
	   AND D.CAP_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86'
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ('001', '003', '004', '005', '006')
	   AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	   AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
	   AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	   AND D.BOOKKEEPING_FLAG = '1'
	   AND D.PAY_MODE NOT IN ('50','53','54','55') )
     ]]>
    </select>
    
    <!-- 内部转帐—应付退保金 -->
    <select id="findBcp008b" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT NVL(SUM(CASE WHEN G.BUSI_PROD_TYPE3_CODE='1' AND T.ROLLBACK_UNIT_NUMBER IS NOT NULL THEN T.FEE_AMOUNT 
      	       WHEN G.BUSI_PROD_TYPE3_CODE !='1' AND T.ROLLBACK_UNIT_NUMBER IS NOT NULL 
               AND EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1') 
               THEN T.FEE_AMOUNT
      	       END),0) NUMBER1,
               NVL(SUM(CASE WHEN G.BUSI_PROD_TYPE3_CODE !='1' AND T.ROLLBACK_UNIT_NUMBER IS NOT NULL 
               AND NOT EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1') 
               THEN T.FEE_AMOUNT END),0) NUMBER2
        FROM DEV_CAP.T_CASH_DETAIL T , DEV_PDS.T_BUSINESS_PRODUCT B,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG G
        WHERE T.FEE_TYPE IN ('P004130000') AND T.WITHDRAW_TYPE IN ('0040604000')
        AND B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE
        AND SUBSTR(T.BUSI_PROD_CODE,3,3) = G.BUSI_PROD_CODE 
        AND G.BUSI_PROD_TYPE3_CODE IN ('1','2')
        AND T.PAY_MODE = '50'
        AND T.ARAP_FLAG = '2'
        AND T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE 
              FROM DEV_PAS.T_UDMP_ORG_REL O
              START WITH O.ORGAN_CODE=#{organ_code}
              CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
        AND T.FINISH_TIME>=TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
        AND T.FINISH_TIME<TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1
        AND T.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O START WITH O.ORGAN_CODE=#{cap_organ_code}
                    CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
      ]]>
    </select>
    
    <!-- 内部转帐—应付退保金 -->
    <select id="findBcp008b_new" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT SUM(NUMBER1) NUMBER1,SUM(NUMBER2) NUMBER2 FROM (
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ARAP_FLAG ='2'
	 AND C.PAY_MODE IS NULL
	 AND C.ITEM_CODE = 'NBZZ-YFTBJ'
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	  WHERE  D.ARAP_FLAG IN ('2') 
	  AND D.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86' CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ( '003', '004', '005', '006')
	  AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	  AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
	  AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.BOOKKEEPING_FLAG = '1'
	  AND D.PAY_MODE NOT IN ('50','53','54','55') )
	  UNION ALL
	  SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
       NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '1'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER1,
		NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '2'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER2
 FROM DEV_CAP.T_PREM_ARAP T
 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) 
   AND (T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
   ,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
   WHERE T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND C.ITEM_CODE = 'NBZZ-YFTBJ'
   AND T.PAY_MODE IN ('53','54','55')
   AND T.DERIV_TYPE IN ('003', '004', '006')
   AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
   AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
   AND T.BOOKKEEPING_FLAG = '1' ) B
      ]]>
    </select>
    
    <!-- 内部转账-应付出单后退费 -->
    <select id="findBcp008g_new" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT SUM(NUMBER1) NUMBER1,SUM(NUMBER2) NUMBER2 FROM (
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ARAP_FLAG ='2'
	 AND C.PAY_MODE IS NULL
	 AND C.ITEM_CODE = 'NBZZ-YFCDHTF'
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	  WHERE  D.ARAP_FLAG IN ('2') 
	  AND D.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86' CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ( '003', '004', '005', '006')
	  AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	  AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
	  AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.BOOKKEEPING_FLAG = '1'
	  AND D.PAY_MODE NOT IN ('50','53','54','55') )
	  UNION ALL
	  SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
       NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '1'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER1,
		NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '2'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER2
 FROM DEV_CAP.T_PREM_ARAP T
 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) 
   AND (T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
   ,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
   WHERE T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND C.ITEM_CODE = 'NBZZ-YFCDHTF'
   AND T.PAY_MODE IN ('53','54','55')
   AND T.DERIV_TYPE IN ('003', '004', '006')
   AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
   AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
   AND T.BOOKKEEPING_FLAG = '1' ) B
      ]]>
    </select>
    
    <!-- 付费日结单-内部转账清单 -->
	<select id="findbcp008qd" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
	SELECT STRING1,STRING2,STRING3,STRING4,STRING5,STRING6,STRING7,STRING8,SUM(NUMBER1) NUMBER1 FROM (
      SELECT /*+INDEX(T IDX_DTAFOCFTCOC_N)*/
      ROWNUM RN,
      T.UNIT_NUMBER STRING1, --实收号/结算号
      T.BUSINESS_CODE STRING2, --业务号
      NVL(T.POLICY_CODE,(SELECT MAX(A.APPLY_CODE) FROM DEV_CAP.T_PREM_ARAP A WHERE A.UNIT_NUMBER=T.UNIT_NUMBER)) STRING3, --保单号/投保单号 STRING3, --保单号/投保单号
      (SELECT T.POLICY_ORGAN_CODE||'-'||A.ORGAN_NAME
         FROM DEV_PAS.T_UDMP_ORG A
         WHERE A.ORGAN_CODE = T.POLICY_ORGAN_CODE) STRING4, --管理机构
      (SELECT T.CAP_ORGAN_CODE||'-'||A.ORGAN_NAME
         FROM DEV_PAS.T_UDMP_ORG A
         WHERE A.ORGAN_CODE = T.CAP_ORGAN_CODE) STRING5, --收付机构
      T.BUSI_PROD_NAME STRING6, --险种
      TO_CHAR(T.FINISH_TIME, 'yyyy/MM/dd') STRING7, --财务确认日期
      CASE 
        WHEN T.ROLLBACK_UNIT_NUMBER IS NULL AND C.CR_SEG4 ='2149161204'
          THEN '应付退保金'
        WHEN T.ROLLBACK_UNIT_NUMBER IS NULL AND C.CR_SEG4 ='2149161201'
          THEN '应付账户领取'
        WHEN T.ROLLBACK_UNIT_NUMBER IS NULL AND C.CR_SEG4 ='**********'
          THEN '应收质押贷款及利息'
        WHEN T.ROLLBACK_UNIT_NUMBER IS NULL
        THEN REGEXP_SUBSTR(B.NAME, '[^-]+$',1,1 )
        WHEN T.ROLLBACK_UNIT_NUMBER IS NOT NULL AND T.DERIV_TYPE = '004'
        THEN  '保全回退' 
        ELSE '' END STRING8,--判断续期回退、保全回退 、理赔回退
 T.FEE_AMOUNT NUMBER1 --金额
 FROM DEV_CAP.T_CASH_DETAIL T
 LEFT JOIN DEV_CAP.T_GL_ACCOUNTING_RULE C  
 ON C.BASE1 = T.FEE_TYPE AND C.BASE2 = T.WITHDRAW_TYPE AND C.FEE_TABLE = '1'
 LEFT JOIN DEV_CAP.T_WITHDRAW_ACCUITEM B
 ON (T.ROLLBACK_UNIT_NUMBER IS NULL AND  C.CR_SEG4 = B.CODE) 
 WHERE T.ARAP_FLAG IN ('2')
   AND T.PAY_MODE = '50'
   AND T.WITHDRAW_TYPE NOT IN ('**********','**********','**********','**********','**********','**********')
   AND T.CAP_ORGAN_CODE IN
       (SELECT O.ORGAN_CODE
          FROM DEV_PAS.T_UDMP_ORG_REL O
         START WITH O.ORGAN_CODE = #{cap_organ_code}
        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
  AND T.DERIV_TYPE IN ('001', '003', '004', '005', '006')
   AND T.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
   AND T.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1
   AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
         START WITH O.ORGAN_CODE = #{organ_code}
        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND (T.RED_BOOKKEEPING_FLAG IS NULL OR T.RED_BOOKKEEPING_FLAG != '1')
   AND T.BOOKKEEPING_FLAG = '1'
  UNION ALL
  
  SELECT 
  	   ROWNUM RN,
       T.UNIT_NUMBER STRING1, --实收号/结算号
        T.BUSINESS_CODE STRING2, --业务号
        NVL(T.POLICY_CODE,(SELECT MAX(A.APPLY_CODE) FROM DEV_CAP.T_PREM_ARAP A WHERE A.UNIT_NUMBER=T.UNIT_NUMBER)) STRING3, --保单号/投保单号
        (SELECT T.POLICY_ORGAN_CODE||'-'||A.ORGAN_NAME
          FROM DEV_PAS.T_UDMP_ORG A
          WHERE A.ORGAN_CODE = T.POLICY_ORGAN_CODE) STRING4, --管理机构
        (SELECT T.POLICY_ORGAN_CODE||'-'||A.ORGAN_NAME
          FROM DEV_PAS.T_UDMP_ORG A
          WHERE A.ORGAN_CODE = T.CAP_ORGAN_CODE) STRING5, --收付机构
        T.BUSI_PROD_NAME STRING6, --险种
        TO_CHAR(T.FINISH_TIME, 'yyyy/MM/dd') STRING7, --财务确认日期
       C.ITEM_NAME STRING8,
       DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT) NUMBER1 --金额
   FROM DEV_CAP.T_FEE_DETAIL T
   LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
   WHERE T.POLICY_ORGAN_CODE IN
         (SELECT O.ORGAN_CODE
            FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code}
          CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
      AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{organ_code}
          CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND C.PAY_MODE IS NULL
   AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
     WHERE  D.ARAP_FLAG IN ('2')
      AND D.CAP_ORGAN_CODE IN
         (SELECT O.ORGAN_CODE
            FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = '86'
          CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
    AND D.DERIV_TYPE IN ('001', '003', '004', '005', '006')
     AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   		AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
     AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{organ_code}
          CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
     AND (D.RED_BOOKKEEPING_FLAG IS NULL OR D.RED_BOOKKEEPING_FLAG != '1')
     AND D.BOOKKEEPING_FLAG = '1'
     AND D.PAY_MODE NOT IN ('50','53','54','55') ) 
    
          
   UNION ALL
	SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
	ROWNUM RN,T.UNIT_NUMBER STRING1, --实收号/结算号
       T.BUSINESS_CODE STRING2, --业务号
       NVL(T.POLICY_CODE,T.APPLY_CODE) STRING3, --保单号/投保单号 STRING3, --保单号/投保单号
       (SELECT T.POLICY_ORGAN_CODE||'-'||A.ORGAN_NAME
         FROM DEV_PAS.T_UDMP_ORG A
         WHERE A.ORGAN_CODE = T.POLICY_ORGAN_CODE) STRING4, --管理机构
      (SELECT T.POLICY_ORGAN_CODE ||'-'||A.ORGAN_NAME
         FROM DEV_PAS.T_UDMP_ORG A
         WHERE A.ORGAN_CODE = T.POLICY_ORGAN_CODE) STRING5, --收付机构
         T.BUSI_PROD_NAME STRING6, --险种
      TO_CHAR(T.FINISH_TIME, 'YYYY/MM/DD') STRING7, --财务确认日期
      C.ITEM_NAME STRING8,--日结项目
        DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)  NUMBER1 --金额
             FROM DEV_CAP.T_PREM_ARAP T
             LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   		ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) AND 
   		(T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
             WHERE T.FEE_STATUS = '01' --后续需要增加16的数据
             AND T.PAY_MODE in ('53','54','55')
             AND C.REPORT_CODE = 'BCP008'
             AND C.ARAP_FLAG ='2'
             AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
             AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')+1
             AND T.ROLLBACK_UNIT_NUMBER IS NOT NULL --内部划转类只有回退场景才是付费日结
             AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
                 START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
             AND T.BOOKKEEPING_FLAG = '1' 
   ) GROUP BY STRING1,STRING2,STRING3,STRING4,STRING5,STRING6,STRING7,STRING8 ORDER BY STRING8,STRING6
      
		]]>
    </select>
    
    <!-- 内部转账—应付账户领取 -->
    <select id="findBcp008c_new" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT SUM(NUMBER1) NUMBER1,SUM(NUMBER2) NUMBER2 FROM (
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ARAP_FLAG ='2'
	 AND C.PAY_MODE IS NULL
	 AND C.ITEM_CODE = 'NBZZ-YFZHLQ'
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	  WHERE  D.ARAP_FLAG IN ('2') 
	  AND D.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86' CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ( '003', '004', '005', '006')
	  AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	  AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
	  AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.BOOKKEEPING_FLAG = '1'
	  AND D.PAY_MODE NOT IN ('50','53','54','55') )
	  UNION ALL
	  SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
       NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '1'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER1,
		NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '2'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER2
 FROM DEV_CAP.T_PREM_ARAP T
 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) 
   AND (T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
   ,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
   WHERE T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND C.ITEM_CODE = 'NBZZ-YFZHLQ'
   AND T.PAY_MODE IN ('53','54','55')
   AND T.DERIV_TYPE IN ('003', '004', '006')
   AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
   AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
   AND T.BOOKKEEPING_FLAG = '1' ) B
      ]]>
    </select>
    
    <!-- 内部转账—应付保户红利 -->
    <select id="findBcp008h_new" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT SUM(NUMBER1) NUMBER1,SUM(NUMBER2) NUMBER2 FROM (
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ARAP_FLAG ='2'
	 AND C.PAY_MODE IS NULL
	 AND C.ITEM_CODE = 'NBZZ-YFBHHL'
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	  WHERE  D.ARAP_FLAG IN ('2') 
	  AND D.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86' CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ( '003', '004', '005', '006')
	  AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	  AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
	  AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.BOOKKEEPING_FLAG = '1'
	  AND D.PAY_MODE NOT IN ('50','53','54','55') )
	  UNION ALL
	  SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
       NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '1'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER1,
		NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '2'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER2
 FROM DEV_CAP.T_PREM_ARAP T
 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) 
   AND (T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
   ,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
   WHERE T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND C.ITEM_CODE = 'NBZZ-YFBHHL'
   AND T.PAY_MODE IN ('53','54','55')
   AND T.DERIV_TYPE IN ('003', '004', '006')
   AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
   AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
   AND T.BOOKKEEPING_FLAG = '1' ) B
      ]]>
    </select>
    
    <!-- 内部转账—应付延期给付利息 -->
    <select id="findBcp008i_new" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT SUM(NUMBER1) NUMBER1,SUM(NUMBER2) NUMBER2 FROM (
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ARAP_FLAG ='2'
	 AND C.PAY_MODE IS NULL
	 AND C.ITEM_CODE = 'NBZZ-YFYQJFLX'
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	  WHERE  D.ARAP_FLAG IN ('2') 
	  AND D.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86' CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ( '003', '004', '005', '006')
	  AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	  AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
	  AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.BOOKKEEPING_FLAG = '1'
	  AND D.PAY_MODE NOT IN ('50','53','54','55') )
	  UNION ALL
	  SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
       NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '1'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER1,
		NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '2'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER2
 FROM DEV_CAP.T_PREM_ARAP T
 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) 
   AND (T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
   ,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
   WHERE T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND C.ITEM_CODE = 'NBZZ-YFYQJFLX'
   AND T.PAY_MODE IN ('53','54','55')
   AND T.DERIV_TYPE IN ('003', '004', '006')
   AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
   AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
   AND T.BOOKKEEPING_FLAG = '1' ) B
      ]]>
    </select>
    
    <!-- 内部转账—应付保户质押贷款-->
    <select id="findBcp008d_new" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	--贷款状态下生存金自动抵扣
      					SELECT SUM(NUMBER1) NUMBER1,SUM(NUMBER2) NUMBER2 FROM (
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ARAP_FLAG ='2'
	 AND C.PAY_MODE IS NULL
	 AND C.ITEM_CODE = 'NBZZ-YFBHZYDK'
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	  WHERE  D.ARAP_FLAG IN ('2') 
	  AND D.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86' CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ( '003', '004', '005', '006')
	  AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	  AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
	  AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.BOOKKEEPING_FLAG = '1'
	  AND D.PAY_MODE NOT IN ('50','53','54','55') )
	  UNION ALL
	  SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
       NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '1'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER1,
		NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '2'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER2
 FROM DEV_CAP.T_PREM_ARAP T
 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) 
   AND (T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
   ,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
   WHERE T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND C.ITEM_CODE = 'NBZZ-YFBHZYDK'
   AND T.PAY_MODE IN ('53','54','55')
   AND T.DERIV_TYPE IN ('003', '004', '006')
   AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
   AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
   AND T.BOOKKEEPING_FLAG = '1' ) B
      ]]>
    </select>
    
    <!-- 内部转账-应付年金给付-->
    <select id="findBcp008e_new" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT SUM(NUMBER1) NUMBER1,SUM(NUMBER2) NUMBER2 FROM (
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' AND P.BUSI_PROD_TYPE1_CODE NOT IN ('1','2')
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' AND P.BUSI_PROD_TYPE1_CODE NOT IN ('1','2')
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ARAP_FLAG ='2'
	 AND C.PAY_MODE IS NULL
	 AND C.ITEM_CODE = 'NBZZ-YFNJJF'
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	  WHERE  D.ARAP_FLAG IN ('2') 
	  AND D.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86' CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ( '003', '004', '005', '006')
	  AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
	  AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
	  AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.BOOKKEEPING_FLAG = '1'
	  AND D.PAY_MODE NOT IN ('50','53','54','55') )
	  UNION ALL
	  SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
       NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '1' AND P.BUSI_PROD_TYPE1_CODE NOT IN ('1','2')
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER1,
		NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '2' AND P.BUSI_PROD_TYPE1_CODE NOT IN ('1','2')
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER2
 FROM DEV_CAP.T_PREM_ARAP T
 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) 
   AND (T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
   ,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
   WHERE T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND C.ITEM_CODE = 'NBZZ-YFNJJF'
   AND T.PAY_MODE IN ('53','54','55')
   AND T.DERIV_TYPE IN ('003', '004', '006')
   AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
   AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
   AND T.BOOKKEEPING_FLAG = '1' ) B
      ]]>
    </select>
    
    <!-- 内部转账-应付满期给付 -->
    <select id="findBcp008f_new" resultType="java.util.Map"
        parameterType="java.util.Map">
      <![CDATA[
      	SELECT SUM(NUMBER1) NUMBER1,SUM(NUMBER2) NUMBER2 FROM (
      	SELECT 
	  NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='1' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER1,
          NVL(SUM(CASE 
          WHEN P.BUSI_PROD_TYPE3_CODE='2' 
          THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
          ELSE 0 END ),0) NUMBER2
	 FROM DEV_CAP.T_FEE_DETAIL T
	 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
	 ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1)
	,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
	 WHERE T.POLICY_ORGAN_CODE IN
	       (SELECT O.ORGAN_CODE
	          FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{cap_organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	    AND T.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = #{organ_code}
	        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	 AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
	 AND C.REPORT_CODE = 'BCP008'
	 AND C.ARAP_FLAG ='2'
	 AND C.PAY_MODE IS NULL
	 AND ( (C.ITEM_CODE = 'NBZZ-YFNJJF' AND P.BUSI_PROD_TYPE1_CODE IN ('1','2')) OR C.ITEM_CODE = 'NBZZ-YFMQJF' )
	 AND T.UNIT_NUMBER IN (SELECT /*+INDEX(D IDX_DTAFOCFTCOC_N)*/ D.UNIT_NUMBER FROM DEV_CAP.T_CASH_DETAIL D
	  WHERE  D.ARAP_FLAG IN ('2') 
	  AND D.CAP_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE = '86' CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.DERIV_TYPE IN ( '003', '004', '005', '006')
	  AND D.FINISH_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
	  AND D.FINISH_TIME < TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') + 1
	  AND D.POLICY_ORGAN_CODE IN  (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
	         START WITH O.ORGAN_CODE =#{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
	  AND D.BOOKKEEPING_FLAG = '1'
	  AND D.PAY_MODE NOT IN ('50','53','54','55') )
	  UNION ALL
	  SELECT /*+INDEX(T IDX_PREM_ARAP__FWSBP_N)*/
       NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '1'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER1,
		NVL(SUM(CASE 
		WHEN P.BUSI_PROD_TYPE3_CODE = '2'
		THEN DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)
		ELSE 0 END ),0) NUMBER2
 FROM DEV_CAP.T_PREM_ARAP T
 LEFT JOIN DEV_CAP.T_REPORT_ITEM_CFG C  
   ON C.FEE_TYPE = T.FEE_TYPE AND C.WITHDRAW_TYPE = T.WITHDRAW_TYPE AND C.ROLLBACK_FLAG = DECODE(T.ROLLBACK_UNIT_NUMBER,'',0,1) 
   AND (T.PAY_MODE = C.PAY_MODE  OR  (C.PAY_MODE IS NULL AND T.PAY_MODE = '55') )
   ,DEV_PDS.T_BUSI_PROD_TYPE_CONFIG P
   WHERE T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
           START WITH O.ORGAN_CODE = #{cap_organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
   AND C.REPORT_CODE = 'BCP008'
   AND C.ARAP_FLAG ='2'
   AND ( (C.ITEM_CODE = 'NBZZ-YFNJJF' AND P.BUSI_PROD_TYPE1_CODE IN ('1','2')) OR C.ITEM_CODE = 'NBZZ-YFMQJF' )
   AND T.PAY_MODE IN ('53','54','55')
   AND T.DERIV_TYPE IN ('003', '004', '006')
   AND SUBSTR(T.BUSI_PROD_CODE,3,3)=P.BUSI_PROD_CODE AND P.BUSI_PROD_TYPE3_CODE IN ('1','2')
   AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
   AND T.BOOKKEEPING_FLAG = '1' ) B
      ]]>
    </select>

</mapper>