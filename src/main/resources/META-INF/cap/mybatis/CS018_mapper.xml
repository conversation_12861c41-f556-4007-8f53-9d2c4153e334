<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CS018">
    <!-- 年金险积累期与领取期转换日结 -->
    <select id="findCS018" resultType="java.util.Map"
        parameterType="java.util.Map">        
        <![CDATA[ 
        SELECT B.STRING1,B.STRING2,B.STRING3,NVL(SUM(NUMBER1), 0) NUMBER1, COUNT(DISTINCT B.POLICY_CODE) NUMBER2  FROM (
            SELECT
            T.POLICY_CODE ,
                NVL(T.BUSI_PROD_NAME,'无') STRING1,
                (SELECT Z.SALES_CHANNEL_NAME
                     FROM DEV_CAP.T_SALES_CHANNEL Z
                    WHERE Z.SALES_CHANNEL_CODE = T.CHANNEL_TYPE) STRING2,
                   T.BUSI_PROD_CODE STRING3,
                CASE
                 WHEN T.ROLLBACK_UNIT_NUMBER IS NULL THEN
                  NVL(ABS(SUM(DECODE(T.ARAP_FLAG, '1', T.FEE_AMOUNT, -T.FEE_AMOUNT))), 0)
                 ELSE
                  NVL(-ABS(SUM(DECODE(T.ARAP_FLAG, '1', T.FEE_AMOUNT, -T.FEE_AMOUNT))), 0)
               END NUMBER1
             FROM DEV_CAP.T_PREM_ARAP_JS T 
            WHERE T.ARAP_FLAG IN ('1','2') --优化语句 
             AND T.BOOKKEEPING_FLAG ='1'
              AND T.FEE_TYPE IN ('G004390600')
              AND T.WITHDRAW_TYPE IN ('0041210000')
              AND T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE 
		              FROM DEV_PAS.T_UDMP_ORG_REL O
		              START WITH O.ORGAN_CODE=#{organ_code}
		              CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
           	  AND T.STATISTICAL_DATE>=TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
			  AND T.STATISTICAL_DATE<TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1 
            GROUP BY T.BUSI_PROD_NAME,T.BUSI_PROD_CODE,T.CHANNEL_TYPE,T.UNIT_NUMBER,T.ROLLBACK_UNIT_NUMBER,T.POLICY_CODE
          ) B GROUP BY B.STRING1,B.STRING3,B.STRING2 ORDER BY STRING1
        ]]>
    
    </select> 
	<!-- 清单SQL -->
    <select id="findCS018qd" resultType="java.util.Map"
        parameterType="java.util.Map">        
        <![CDATA[ 
         	SELECT A.STRING1,A.STRING2,A.STRING3,A.STRING4,A.STRING5,SUM(NUMBER1) NUMBER1 FROM (
        SELECT 
               T.POLICY_CODE STRING1,
               POLICY_ORGAN_CODE || (SELECT O.ORGAN_NAME
                  FROM DEV_PAS.T_UDMP_ORG_REL O
                 WHERE O.ORGAN_CODE = T.POLICY_ORGAN_CODE) STRING2,
               T.BUSI_PROD_NAME STRING3,
               (SELECT Z.SALES_CHANNEL_NAME
                        FROM DEV_CAP.T_SALES_CHANNEL Z
                       WHERE Z.SALES_CHANNEL_CODE = T.CHANNEL_TYPE) STRING4,
 
               TO_CHAR(T.STATISTICAL_DATE, 'yyyy-MM-dd') STRING5,
               CASE
                 WHEN T.ROLLBACK_UNIT_NUMBER IS NULL THEN
                  NVL(ABS(SUM(DECODE(T.ARAP_FLAG, '1', T.FEE_AMOUNT, -T.FEE_AMOUNT))), 0)
                 ELSE
                  NVL(-ABS(SUM(DECODE(T.ARAP_FLAG, '1', T.FEE_AMOUNT, -T.FEE_AMOUNT))), 0)
               END NUMBER1
          FROM DEV_CAP.T_PREM_ARAP_JS T
          WHERE T.ARAP_FLAG IN ('1','2') --优化语句 
		              AND T.BOOKKEEPING_FLAG ='1'
		              AND T.FEE_TYPE IN ('G004390600')
		              AND T.WITHDRAW_TYPE IN ('0041210000')
		              AND T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE 
				              FROM DEV_PAS.T_UDMP_ORG_REL O
				              START WITH O.ORGAN_CODE=#{organ_code}
				              CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
		           	  AND T.STATISTICAL_DATE>=TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
					  AND T.STATISTICAL_DATE<TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1
				 GROUP BY T.POLICY_CODE,T.BUSI_PROD_NAME,T.UNIT_NUMBER,T.ROLLBACK_UNIT_NUMBER,T.POLICY_ORGAN_CODE,T.CHANNEL_TYPE,T.STATISTICAL_DATE) A
         		 GROUP BY A.STRING1,A.STRING2,A.STRING3,A.STRING4,A.STRING5
         		 ORDER BY A.STRING3,A.STRING5,A.STRING1
        ]]>
    </select> 
</mapper>