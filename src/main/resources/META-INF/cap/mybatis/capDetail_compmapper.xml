<?xml version="1.0" encoding="UTF-8"?> 
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cap_detail">
<!-- 排序 --> 
<sql id="queryCashDetailByOrderby_cjk">
<if test="OrderFlag !=null and '1'.toString()==OrderFlag and PayCount=='PayCount'" >
<![CDATA[ order by A.PAID_COUNT ]]>
</if>
<if test="OrderFlag !=null and '1'.toString()==OrderFlag and PayDate=='PayDate'">
<![CDATA[ order by A.DUE_TIME ]]>
</if>
<if test="OrderFlag !=null and '1'.toString()==OrderFlag and RiskName=='RiskName'" >
<![CDATA[ order by A.BUSI_PROD_CODE ]]>
</if>

<if test="OrderFlag !=null and '2'.toString()==OrderFlag and PayCount=='PayCount'">
<![CDATA[ order by A.PAID_COUNT desc ]]>
</if>
<if test="OrderFlag !=null and '2'.toString()==OrderFlag and PayDate=='PayDate'">
<![CDATA[ order by A.DUE_TIME  desc ]]>
</if>
<if test="OrderFlag !=null and '2'.toString()==OrderFlag and RiskName=='RiskName'">
<![CDATA[ order by A.BUSI_PROD_CODE desc ]]>
</if>
</sql>

    <!--查询 应收付 表时候要带的查询条件   -->
    <sql id="capPremWhereCondition">
        <!-- 查询 应收付表时 收付费机构字段无效 因为 应收付表中没有此字段 要关联实收付表去显示此列 -->
        <if test="payrefno != null and payrefno != ''">
        <![CDATA[ AND 1=0 ]]>
        </if>
        <if test="POLICY_ORGAN_CODE != null and POLICY_ORGAN_CODE != ''">
        <![CDATA[ AND POLICY_ORGAN_CODE in (select org_rel.organ_code from DEV_PAS.T_UDMP_ORG_REL org_rel
             start with org_rel.ORGAN_CODE = #{POLICY_ORGAN_CODE} 
             connect by prior org_rel.organ_code = org_rel.uporgan_code) 
        ]]>
        </if>
        <!-- <if test="ARAP_FLAG != null and ARAP_FLAG != ''"> <![CDATA[ 
            AND ARAP_FLAG = #{ARAP_FLAG} ]]> </if> -->
        <if test="BUSINESS_CODE != null and BUSINESS_CODE != ''">
            <![CDATA[ AND BUSINESS_CODE = #{BUSINESS_CODE} ]]>
        </if>
        <if test="BANK_CODE != null and BANK_CODE != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND BANK_CODE = #{BANK_CODE} ]]>
        </if>
        
        <choose>
        <when test="HOLDER_NAME != null and HOLDER_NAME != ''">
        <![CDATA[ AND HOLDER_NAME = #{HOLDER_NAME} ]]>
        </when>
        <when test="PAYEE_NAME != null and PAYEE_NAME != ''">
        <![CDATA[ AND PAYEE_NAME = #{PAYEE_NAME} ]]>
        </when>
        <when test="PAYEE_NAME != null and PAYEE_NAME != '' and HOLDER_NAME != null and HOLDER_NAME != ''">
        <![CDATA[ AND (PAYEE_NAME = #{PAYEE_NAME} OR HOLDER_NAME = #{HOLDER_NAME})]]>
        </when>
        <otherwise>
        </otherwise>
        </choose>
        
        <if test="POLICY_CODE != null and POLICY_CODE != ''">
            <![CDATA[ AND POLICY_CODE = #{POLICY_CODE} ]]>
        </if>
        <if test="CHANNEL_TYPE != null and CHANNEL_TYPE != ''">
            <![CDATA[ AND TRIM(CHANNEL_TYPE) = #{CHANNEL_TYPE} ]]>
        </if>
      <if test="FEE_STATUS != null and FEE_STATUS != ''">
            <![CDATA[ AND FEE_STATUS = #{FEE_STATUS} ]]>
        </if> 
        <if test="DERIV_TYPE != null and DERIV_TYPE != ''">
            <![CDATA[ AND DERIV_TYPE = #{DERIV_TYPE} ]]>
        </if>
        <if test="PAY_MODE != null and PAY_MODE != ''">
            <![CDATA[ AND PAY_MODE = #{PAY_MODE} ]]>
        </if>
        <if test="dueStartTime != null and dueStartTime != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND DUE_TIME >= #{dueStartTime} ]]>
        </if>
        <if test="dueEndTime != null and dueEndTime != ''">
            <![CDATA[ AND DUE_TIME <= #{dueEndTime} ]]>
        </if>
        <if test="offsetStartTime != null and offsetStartTime != ''">
            <![CDATA[ AND FINISH_TIME >= #{offsetStartTime} ]]>
        </if>
        <if test="offsetEndTime != null and offsetEndTime != ''">
            <![CDATA[ AND FINISH_TIME <= #{offsetEndTime} ]]>
        </if>
        <if test="INSERT_BY != null and INSERT_BY != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND INSERT_BY = #{INSERT_BY} ]]>
        </if>
        <if test="belnr != null and belnr != ''">
            <![CDATA[ AND BELNR = #{belnr} ]]>
        </if>
        <if test="BANK_ACCOUNT != null and BANK_ACCOUNT != ''">
            <![CDATA[ AND BANK_ACCOUNT = #{BANK_ACCOUNT} ]]>
        </if>
        <if test="OPERATOR_BY != null and OPERATOR_BY != ''">
            <![CDATA[ AND OPERATOR_BY = #{OPERATOR_BY} ]]>
        </if>
        <if test="UNIT_NUMBER !=null and UNIT_NUMBER !='' ">
        	<![CDATA[ AND UNIT_NUMBER= #{UNIT_NUMBER}]]>
        </if>
        <if test="is_corporate != null and is_corporate !='' and is_corporate=='1'.toString()" >
        	<![CDATA[ AND IS_CORPORATE =#{is_corporate}]]>
        </if>
         <if test="is_corporate != null and is_corporate !='' and is_corporate=='0'.toString()" >
        	<![CDATA[ AND (IS_CORPORATE IS NULL OR IS_CORPORATE ='0')  ]]>
        </if>
        <if test="arapDateStart != null and arapDateStart != ''" >
        	<![CDATA[ AND EXISTS ( SELECT 1 FROM DEV_CAP.T_BILL_DETAILS T WHERE T.UNIT_NUMBER = Y.UNIT_NUMBER
        	AND T.ARAP_DATE >=#{arapDateStart} )  ]]>
        </if>
        <if test="arapDateEnd != null and arapDateEnd != '' " >
        	<![CDATA[ AND EXISTS ( SELECT 1 FROM DEV_CAP.T_BILL_DETAILS T WHERE T.UNIT_NUMBER = Y.UNIT_NUMBER
        	AND T.ARAP_DATE < #{arapDateEnd} + 1 )  ]]>
        </if>
        
    </sql>
    <!-- 交费信息引入Sql -->
    <sql id="queryCashDetailByStartDateAndEndDate">
        <if test="start_pay_date != null and start_pay_date != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND A.INSERT_TIME >= #{start_pay_date} ]]>
        </if>
        <if test="end_pay_date != null and end_pay_date != ''">
            <![CDATA[ AND A.INSERT_TIME <= #{end_pay_date} ]]>
        </if>
        <if test="POLICY_CODE != null and POLICY_CODE != ''">
            <![CDATA[ AND A.POLICY_CODE = #{POLICY_CODE} ]]>
        </if>
    </sql>
   
    <sql id="queryCashDetailByStartEndDate">
        <if test="start_pay_date != null and start_pay_date != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND MAKE_DATE >= #{start_pay_date} ]]>
        </if>
        <if test="end_pay_date != null and end_pay_date != ''">
            <![CDATA[ AND MAKE_DATE <= #{end_pay_date} ]]>
        </if>
    </sql>

    <!--查询 实收付 表时候要带的查询条件 -->
    <sql id="capCashWhereCondition">
        <if test="payrefno != null and payrefno != ''">
            <![CDATA[ AND PAYREFNO = #{payrefno} ]]>
        </if>
        <if test="UNIT_NUMBER != null and UNIT_NUMBER != ''">
            <![CDATA[ AND UNIT_NUMBER = #{UNIT_NUMBER} ]]>
        </if>
        <if test="CAP_ORGAN_CODE != null and CAP_ORGAN_CODE != ''">
            <![CDATA[ AND CAP_ORGAN_CODE in (select org_rel.organ_code from DEV_PAS.T_UDMP_ORG_REL org_rel
                    start with org_rel.organ_code = #{CAP_ORGAN_CODE} 
                    connect by prior org_rel.organ_code = org_rel.uporgan_code) 
        ]]>
        </if>
        <if test="POLICY_ORGAN_CODE != null and POLICY_ORGAN_CODE != ''">
        <![CDATA[ AND POLICY_ORGAN_CODE in (select org_rel.organ_code from DEV_PAS.T_UDMP_ORG_REL org_rel
                 start with org_rel.organ_code = #{POLICY_ORGAN_CODE} 
                 connect by prior org_rel.organ_code = org_rel.uporgan_code)  ]]>
        </if>
        <!-- <if test="ARAP_FLAG != null and ARAP_FLAG != ''"> <![CDATA[ 
            AND ARAP_FLAG = #{ARAP_FLAG} ]]> </if> -->
        <if test="BUSINESS_CODE != null and BUSINESS_CODE != ''">
            <![CDATA[ AND BUSINESS_CODE = #{BUSINESS_CODE} ]]>
        </if>
        <if test="BANK_CODE != null and BANK_CODE != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND ACTUAL_BANK_CODE = #{BANK_CODE} ]]>
        </if>
        <choose>
        <when test="HOLDER_NAME != null and HOLDER_NAME != ''">
        <![CDATA[ AND HOLDER_NAME = #{HOLDER_NAME} ]]>
        </when>
        <when test="PAYEE_NAME != null and PAYEE_NAME != ''">
        <![CDATA[ AND PAYEE_NAME = #{PAYEE_NAME} ]]>
        </when>
        <when test="PAYEE_NAME != null and PAYEE_NAME != '' and HOLDER_NAME != null and HOLDER_NAME != ''">
        <![CDATA[ AND (PAYEE_NAME = #{PAYEE_NAME} OR HOLDER_NAME = #{HOLDER_NAME})]]>
        </when>
        <otherwise>
        </otherwise>
        </choose>
        <if test="POLICY_CODE != null and POLICY_CODE != ''">
            <![CDATA[ AND POLICY_CODE = #{POLICY_CODE} ]]>
        </if>
        <if test="CHANNEL_TYPE != null and CHANNEL_TYPE != ''">
            <![CDATA[ AND TRIM(CHANNEL_TYPE) = #{CHANNEL_TYPE} ]]>
        </if>
        <if test="FEE_STATUS != null and FEE_STATUS != ''">
            <![CDATA[ AND FEE_STATUS = #{FEE_STATUS} ]]>
        </if>
        <if test="DERIV_TYPE != null and DERIV_TYPE != ''">
            <![CDATA[ AND DERIV_TYPE = #{DERIV_TYPE} ]]>
        </if>
        <if test="PAY_MODE != null and PAY_MODE != ''">
            <![CDATA[ AND PAY_MODE = #{PAY_MODE} ]]>
        </if>
        <if test="feeStartTime != null and feeStartTime != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND FINISH_TIME >= to_date(to_char(#{feeStartTime}, 'yyyy-mm-dd'), 'yyyy-mm-dd') ]]>
        </if>
        <if test="feeEndTime != null and feeEndTime != ''">
            <![CDATA[ AND FINISH_TIME < to_date(to_char(#{feeEndTime}, 'yyyy-mm-dd'), 'yyyy-mm-dd') + 1 ]]>
        </if>
        <if test="offsetStartTime != null and offsetStartTime != ''">
            <![CDATA[ AND FINISH_TIME >= to_date(to_char(#{offsetStartTime}, 'yyyy-mm-dd'), 'yyyy-mm-dd') ]]>
        </if>
        <if test="offsetEndTime != null and offsetEndTime != ''">
            <![CDATA[ AND FINISH_TIME < to_date(to_char(#{offsetEndTime}, 'yyyy-mm-dd'), 'yyyy-mm-dd') + 1 ]]>
        </if>
        <if test="INSERT_BY != null and INSERT_BY != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND INSERT_BY = #{INSERT_BY} ]]>
        </if>
        <if test="belnr != null and belnr != ''">
            <![CDATA[ AND BELNR = #{belnr} ]]>
        </if>
        <if test="CERTI_CODE != null and CERTI_CODE != ''">
            <![CDATA[ AND CERTI_CODE = #{CERTI_CODE} ]]>
        </if>
        <if test="payModeByDeduction != null and payModeByDeduction != ''">
            <![CDATA[ AND PAY_MODE != #{payModeByDeduction}  and PAY_MODE != '50']]>
        </if>
        <if test="ACTUAL_BANK_ACCOUNT != null and ACTUAL_BANK_ACCOUNT != ''">
            <![CDATA[ AND ACTUAL_BANK_ACCOUNT = #{ACTUAL_BANK_ACCOUNT} ]]>
        </if>
        <if test="OPERATOR_BY != null and OPERATOR_BY != ''">
            <![CDATA[ AND U.USER_NAME = #{OPERATOR_BY} ]]>
        </if>
        <if test="redBookkeepingFlag != null and redBookkeepingFlag != ''">
            <![CDATA[ AND (RED_BOOKKEEPING_FLAG IS NULL OR RED_BOOKKEEPING_FLAG != #{redBookkeepingFlag}) ]]>
        </if>
        <if test="CODE != null and CODE != '' ">
        <choose>
        <when test="CODE == 'FNGRD-8600050'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600050') ]]>
        </when>
        <when test=" CODE == 'FNGRD-8600010'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600010') ]]>
        </when>
        
        <when test=" CODE == 'FNGRD-8600090'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600090') ]]>
        </when>
        <when test="CODE == 'FNGRD-8600040'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600040') ]]>
        </when>
        
         <!-- 新增 -->
    
           <when test="CODE == 'FNGRD-8600020'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600020') ]]>
        </when>
           <when test="CODE == 'FNGRD-8600070'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600070') ]]>
        </when>
           <when test="CODE == 'FNGRD-8600080'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600080') ]]>
        </when>
           <when test="CODE == 'FNGRD-8600100'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600100') ]]>
        </when>
           <when test="CODE == 'FNGRD-8600110'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600110') ]]>
        </when>
           <when test="CODE == 'FNGRD-8600120'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600120') ]]>
        </when>
           <when test="CODE == 'FNGRD-8600150'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600150') ]]>
        </when>
           <when test="CODE == 'FNGRD-8600490'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' and B.BANK_CODE='8600490') ]]>
        </when>
           <when test="CODE == 'FNGRD-OTHER'">
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE ='FNGRD' 
            AND B.BANK_CODE NOT IN ('8600050','8600010','8600090','8600040','8600020','8600070','8600080',
            '8600120','8600150','8600490','8600110','8600100')
            ) ]]>
        </when>
        
        <otherwise>
             <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE =#{CODE}) ]]>
        </otherwise>
        </choose>
        </if>
    </sql>
    
     <!--查询 实收付 表时候要带的查询条件 -->
    <sql id="capCashWhereCondition1">
        <if test="payrefno != null and payrefno != ''">
            <![CDATA[ AND PAYREFNO = #{payrefno} ]]>
        </if>
        <if test="CAP_ORGAN_CODE != null and CAP_ORGAN_CODE != ''">
            <![CDATA[ AND CAP_ORGAN_CODE in (select org_rel.organ_code from DEV_PAS.T_UDMP_ORG_REL org_rel
                    start with org_rel.organ_code = #{CAP_ORGAN_CODE} 
                    connect by prior org_rel.organ_code = org_rel.uporgan_code) 
        ]]>
        </if>
        <if test="POLICY_ORGAN_CODE != null and POLICY_ORGAN_CODE != ''">
        <![CDATA[ AND POLICY_ORGAN_CODE in (select org_rel.organ_code from DEV_PAS.T_UDMP_ORG_REL org_rel
                 start with org_rel.organ_code = #{POLICY_ORGAN_CODE} 
                 connect by prior org_rel.organ_code = org_rel.uporgan_code) ]]>
        </if>
        <!-- <if test="ARAP_FLAG != null and ARAP_FLAG != ''"> <![CDATA[ 
            AND ARAP_FLAG = #{ARAP_FLAG} ]]> </if> -->
        <if test="BUSINESS_CODE != null and BUSINESS_CODE != ''">
            <![CDATA[ AND BUSINESS_CODE = #{BUSINESS_CODE} ]]>
        </if>
        <if test="BANK_CODE != null and BANK_CODE != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND ACTUAL_BANK_CODE = #{BANK_CODE} ]]>
        </if>
        <choose>
        <when test="HOLDER_NAME != null and HOLDER_NAME != ''">
        <![CDATA[ AND HOLDER_NAME = #{HOLDER_NAME} ]]>
        </when>
        <when test="PAYEE_NAME != null and PAYEE_NAME != ''">
        <![CDATA[ AND PAYEE_NAME = #{PAYEE_NAME} ]]>
        </when>
        <when test="PAYEE_NAME != null and PAYEE_NAME != '' and HOLDER_NAME != null and HOLDER_NAME != ''">
        <![CDATA[ AND (PAYEE_NAME = #{PAYEE_NAME} OR HOLDER_NAME = #{HOLDER_NAME})]]>
        </when>
        <otherwise>
        </otherwise>
        </choose>
        <if test="POLICY_CODE != null and POLICY_CODE != ''">
            <![CDATA[ AND POLICY_CODE = #{POLICY_CODE} ]]>
        </if>
        <if test="CHANNEL_TYPE != null and CHANNEL_TYPE != ''">
            <![CDATA[ AND TRIM(CHANNEL_TYPE) = #{CHANNEL_TYPE} ]]>
        </if>
        <if test="FEE_STATUS != null and FEE_STATUS != ''">
            <![CDATA[ AND FEE_STATUS = #{FEE_STATUS} ]]>
        </if>
        <if test="DERIV_TYPE != null and DERIV_TYPE != ''">
            <![CDATA[ AND DERIV_TYPE = #{DERIV_TYPE} ]]>
        </if>
        <if test="PAY_MODE != null and PAY_MODE != ''">
            <![CDATA[ AND PAY_MODE = #{PAY_MODE} ]]>
        </if>
        <if test="feeStartTime != null and feeStartTime != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND FINISH_TIME >= #{feeStartTime} ]]>
        </if>
        <if test="feeEndTime != null and feeEndTime != ''">
            <![CDATA[ AND FINISH_TIME <= #{feeEndTime} ]]>
        </if>
        <if test="offsetStartTime != null and offsetStartTime != ''">
            <![CDATA[ AND FINISH_TIME >= #{offsetStartTime} ]]>
        </if>
        <if test="offsetEndTime != null and offsetEndTime != ''">
            <![CDATA[ AND FINISH_TIME <= #{offsetEndTime} ]]>
        </if>
        <if test="INSERT_BY != null and INSERT_BY != ''">  <!-- 到实收付 需要修改 -->
            <![CDATA[ AND INSERT_BY = #{INSERT_BY} ]]>
        </if>
        <if test="belnr != null and belnr != ''">
            <![CDATA[ AND BELNR = #{belnr} ]]>
        </if>
        <if test="CERTI_CODE != null and CERTI_CODE != ''">
            <![CDATA[ AND CERTI_CODE = #{CERTI_CODE} ]]>
        </if>
        <if test="payModeByDeduction != null and payModeByDeduction != ''">
            <![CDATA[ AND d.PAY_MODE != #{payModeByDeduction} and d.PAY_MODE !='50']]>
        </if>
        <if test="ACTUAL_BANK_ACCOUNT != null and ACTUAL_BANK_ACCOUNT != ''">
            <![CDATA[ AND ACTUAL_BANK_ACCOUNT = #{ACTUAL_BANK_ACCOUNT} ]]>
        </if>
        <if test="OPERATOR_BY != null and OPERATOR_BY != ''">
            <![CDATA[ AND OPERATOR_BY = #{OPERATOR_BY} ]]>
        </if>
        <if test="redBookkeepingFlag != null and redBookkeepingFlag != ''">
            <![CDATA[ AND (RED_BOOKKEEPING_FLAG IS NULL OR RED_BOOKKEEPING_FLAG != #{redBookkeepingFlag}) ]]>
        </if>
        <if test="CODE != null and CODE != ''">
            <![CDATA[ AND C.UNIT_NUMBER  IN(SELECT  B.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.t_fms_interface B WHERE B.SERVICER_CODE =#{CODE}) ]]>
        </if>
    </sql>

    <sql id="capHavingCondition">
    	<!-- mybatis会把0作为空字符串判断，增加==0增强判断 -->
        <if test="minFeeAmount != null and minFeeAmount !='' or minFeeAmount == 0">
            <![CDATA[ AND SUM(CASE WHEN ARAP_FLAG ='${ar}' THEN FEE_AMOUNT ELSE -1*FEE_AMOUNT END) >= #{minFeeAmount} ]]>
        </if>
        <if test="maxFeeAmount != null and maxFeeAmount !='' or maxFeeAmount == 0">
            <![CDATA[ AND SUM(CASE WHEN ARAP_FLAG ='${ar}' THEN FEE_AMOUNT ELSE -1*FEE_AMOUNT END) <= #{maxFeeAmount} ]]>
        </if>
    </sql>

    <sql id="capDetailCondition">
        <if test="policy_code != null and policy_code != ''">
            <![CDATA[ AND Y.POLICY_CODE = #{policy_code} ]]>
        </if>
        <if test="business_code != null and business_code != ''">
            <![CDATA[ AND Y.BUSINESS_CODE = #{business_code} ]]>
        </if>
    </sql>

    <select id="findCapDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
    <![CDATA[ select *
   from (select y.unit_number,
                max(y.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                case
                  when max(y.fee_status) = min(y.fee_status) then
                   max(y.fee_status)
                  else
                   case
                     when max(y.fee_status) = '16' then
                      min(y.fee_status)
                     else
                      max(y.fee_status)
                   end
                end fee_status,
                max(y.payee_name) payee_name,
                max(y.BUSINESS_CODE) BUSINESS_CODE,
                max(y.deriv_type) deriv_type,
                sum(case
                      when y.arap_flag = 1 then
                       y.fee_amount
                      else
                       -1 * y.fee_amount
                    end) arap_fee_sum,
                max(y.due_time) due_time
           from APP___CAP__DBUSER.T_PREM_ARAP y

            WHERE 1=1  
            and y.FEE_STATUS != #{FEE_STATUS__DISABLE} 
            ]]>
        <include refid="capDetailCondition" /> 
        <![CDATA[ 
          group by y.unit_number) a
   left join
 
  (select t.unit_number unit_number_cash,
          MAX(t.CAP_ORGAN_CODE) cap_organ_code,
          sum(case
                when t.arap_flag = 1 then
                 t.fee_amount
                else
                 -1 * t.fee_amount
              end) feeSum,
          max(t.insert_time) insert_time,
          max(t.payrefno) payrefno,
          max(t.OPERATOR_BY) OPERATOR_BY,
          max(t.FINISH_TIME) FINISH_TIME
     from APP___CAP__DBUSER.T_CASH_DETAIL t
    where (t.red_bookkeeping_flag is null or t.red_bookkeeping_flag <> #{RED_BOOKKEEPING_FLAG})
        and t.unit_number in (select unit_number from APP___CAP__DBUSER.T_PREM_ARAP y where 1=1 
    ]]>
     <include refid="capDetailCondition" /> 
     <![CDATA[
    )
    group by t.unit_number) b
     on a.unit_number = b.unit_number_cash
        ]]>
        <![CDATA[ ]]>
    </select>
    <select id="capDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
    <![CDATA[ select b.rn as rowNumber,b.unit_number,b.cap_organ_code,b.policy_organ_code,b.fee_status,b.payee_name,b.business_code,b.deriv_type,b.arap_fee_sum,b.feesum,b.due_time,b.insert_time,b.payrefno,b.OPERATOR_BY ,b.finish_time from (
    select ROWNUM RN,c.*  from (select *
   from (select y.unit_number,
                max(y.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                case
                  when max(y.fee_status) = min(y.fee_status) then
                   max(y.fee_status)
                  else
                   case
                     when max(y.fee_status) = '16' then
                      min(y.fee_status)
                     else
                      max(y.fee_status)
                   end
                end fee_status,
                max(y.payee_name) payee_name,
                max(y.BUSINESS_CODE) BUSINESS_CODE,
                max(y.deriv_type) deriv_type,
                sum(case
                      when y.arap_flag = 1 then
                       y.fee_amount
                      else
                       -1 * y.fee_amount
                    end) arap_fee_sum,
                max(y.due_time) due_time
           from APP___CAP__DBUSER.T_PREM_ARAP y
          where 1 = 1
            and y.FEE_STATUS != #{FEE_STATUS__DISABLE}              
            ]]>
        <include refid="capDetailCondition" /> 
        <![CDATA[ 
 group by y.unit_number) a
   left join
 
  (select t.unit_number unit_number_cash,
          MAX(t.CAP_ORGAN_CODE) cap_organ_code,
          sum(case
                when t.arap_flag = 1 then
                 t.fee_amount
                else
                 -1 * t.fee_amount
              end) feeSum,
          max(t.insert_time) insert_time,
          max(t.payrefno) payrefno,
          max(t.OPERATOR_BY) OPERATOR_BY,
          max(t.FINISH_TIME) FINISH_TIME
     from APP___CAP__DBUSER.T_CASH_DETAIL t
    where (t.red_bookkeeping_flag is null or t.red_bookkeeping_flag <> #{RED_BOOKKEEPING_FLAG})
            and t.unit_number in (select unit_number from APP___CAP__DBUSER.T_PREM_ARAP y where 1=1 
    ]]>
     <include refid="capDetailCondition" /> 
     <![CDATA[
    )
    group by t.unit_number) b
     on a.unit_number = b.unit_number_cash where ROWNUM <=#{LESS_NUM}
        ]]>
        <![CDATA[ ) c ) b where b.rn >  #{GREATER_NUM} ]]>
    </select>

    <select id="capDetailTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
            SELECT COUNT(1)
  FROM (select *
   from (select y.unit_number,
                max(y.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                case
                  when max(y.fee_status) = min(y.fee_status) then
                   max(y.fee_status)
                  else
                   case
                     when max(y.fee_status) = '16' then
                      min(y.fee_status)
                     else
                      max(y.fee_status)
                   end
                end fee_status,
                max(y.payee_name) payee_name,
                max(y.BUSINESS_CODE) BUSINESS_CODE,
                max(y.deriv_type) deriv_type,
                sum(case
                      when y.arap_flag = 1 then
                       y.fee_amount
                      else
                       -1 * y.fee_amount
                    end) arap_fee_sum,
                max(y.due_time) due_time
           from APP___CAP__DBUSER.T_PREM_ARAP y
          where 1 = 1
            and y.FEE_STATUS != #{FEE_STATUS__DISABLE} 
        ]]>
        <include refid="capDetailCondition" />
        <!--GROUP BY LIST_ID -->
         <![CDATA[
          group by y.unit_number) a
   left join
 
  (select t.unit_number unit_number_cash,
          MAX(t.CAP_ORGAN_CODE) cap_organ_code,
          sum(case
                when t.arap_flag = 1 then
                 t.fee_amount
                else
                 -1 * t.fee_amount
              end) feeSum,
          max(t.insert_time) insert_time,
          max(t.payrefno) payrefno,
          max(t.OPERATOR_BY) OPERATOR_BY,
          max(t.FINISH_TIME) FINISH_TIME
     from APP___CAP__DBUSER.T_CASH_DETAIL t
    where (t.red_bookkeeping_flag is null or t.red_bookkeeping_flag <> #{RED_BOOKKEEPING_FLAG})
            and t.unit_number in (select unit_number from APP___CAP__DBUSER.T_PREM_ARAP y where 1=1 
    ]]>
     <include refid="capDetailCondition" /> 
     <![CDATA[
    )
    group by t.unit_number) b
     on a.unit_number = b.unit_number_cash
        ]]>   
        <![CDATA[)]]>
    </select>
    <!-- 查询 应收付详细信息的sql语句 -->
    <select id="findCapPremDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select ROWNUM RN,b.policy_organ_code,CASE WHEN b.fee_amount >= 0 THEN '1' ELSE '2' end arap_flag,b.policy_code,B.fee_Amount,b.deriv_type,b.payee_name
,b.business_code,case when b.agent_code is not null then (select AGENT_NAME from DEV_CAP.t_agent where agent_code = b.agent_code) else null end agent_code,
b.due_time,b.fee_status,b.OPERATOR_BY,b.finish_time,b.pay_mode,b.unit_number,b.HOLDER_NAME,b.CERTI_CODE,b.bank_code,b.bank_account,b.BUSINESS_TYPE,b.SPECIAL_ACCOUNT_FLAG,b.IS_CORPORATE
  from ( SELECT MAX(y.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                MAX(y.ARAP_FLAG) ARAP_FLAG,
                MAX(y.POLICY_CODE) POLICY_CODE,
                SUM(                                                                
                   CASE WHEN 
                      y.ARAP_FLAG ='${ar}' 
                       THEN 
                        y.FEE_AMOUNT 
                      ELSE 
                        -1*y.FEE_AMOUNT 
                       END
                     ) FEE_AMOUNT,
                MAX(y.DERIV_TYPE) DERIV_TYPE,
                MAX(y.PAYEE_NAME) PAYEE_NAME,
                MAX(y.BUSINESS_CODE) BUSINESS_CODE,
                MAX(y.AGENT_CODE) AGENT_CODE,
                MAX(y.DUE_TIME) DUE_TIME,
                MAX(y.FEE_STATUS) FEE_STATUS,
                MAX(U.USER_NAME) OPERATOR_BY,
                MAX(y.FINISH_TIME) FINISH_TIME,    
                MAX(y.PAY_MODE) PAY_MODE,  
                MAX(y.UNIT_NUMBER) UNIT_NUMBER,
                MAX(y.CERTI_CODE) CERTI_CODE,
                MAX(y.HOLDER_NAME) HOLDER_NAME,
                MAX(y.BANK_CODE) BANK_CODE,
                MAX(y.BANK_ACCOUNT) BANK_ACCOUNT,
                MAX(y.BUSINESS_TYPE) BUSINESS_TYPE,
                MAX(CASE WHEN y.SPECIAL_ACCOUNT_FLAG ='1' THEN '是 ' ELSE '否' END ) SPECIAL_ACCOUNT_FLAG,
                MAX(y.IS_CORPORATE) IS_CORPORATE
                FROM DEV_CAP.T_PREM_ARAP y LEFT JOIN DEV_PAS.T_UDMP_USER U  ON y.OPERATOR_BY = U.USER_ID
                WHERE  y.FEE_STATUS != #{fee_status16} 
                AND y.FEE_STATUS != #{fee_status01}
                AND y.FEE_STATUS != #{fee_status02}
        ]]>
        <include refid="capPremWhereCondition" />
        <if test="ACTUAL_BANK_ACCOUNT != null and ACTUAL_BANK_ACCOUNT != ''">
            <![CDATA[ AND BANK_ACCOUNT = #{ACTUAL_BANK_ACCOUNT} ]]>
        </if>
        <if test="special_account_flag != null and special_account_flag != '' and special_account_flag == '1'.toString() ">
            <![CDATA[ AND SPECIAL_ACCOUNT_FLAG = '1' ]]>
        </if>
	    <if test="special_account_flag != null and special_account_flag != '' and special_account_flag == '0'.toString() ">
            <![CDATA[ AND (SPECIAL_ACCOUNT_FLAG <> '1' or SPECIAL_ACCOUNT_FLAG IS NULL )]]>
        </if>
        <include refid="capPermStatus" />    
        <![CDATA[ 
            GROUP BY y.unit_number
              having 1 = 1    
        ]]>
        <if test=" ARAP_FLAG != null and ARAP_FLAG != '' ">
        <![CDATA[ and CASE WHEN SUM(CASE
                WHEN y.ARAP_FLAG = '${ar}' THEN
                 y.FEE_AMOUNT
                ELSE
                  -1 * y.FEE_AMOUNT
                END) >=0  THEN '${ar}' ELSE '${ap}' END = #{ARAP_FLAG}  ]]>
        </if>
        <include refid="capHavingCondition" />
         
        <![CDATA[ order by  DUE_TIME desc ) b ]]>
        
    </select>
    <!-- 查询 应收付详细信息的sql语句 -->
    <select id="capPremDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT  B.POLICY_ORGAN_CODE,CASE WHEN B.FEE_AMOUNT >= 0 THEN '1' ELSE '2' END ARAP_FLAG,B.POLICY_CODE,B.FEE_AMOUNT,B.DERIV_TYPE,
                B.PAYEE_NAME,B.BUSINESS_CODE,CASE WHEN B.AGENT_CODE IS NOT NULL THEN (SELECT AGENT_NAME FROM DEV_PAS.T_AGENT 
                WHERE AGENT_CODE = B.AGENT_CODE) ELSE NULL END AGENT_CODE,B.DUE_TIME,B.FEE_STATUS,B.OPERATOR_BY,B.FINISH_TIME,B.PAY_MODE,
                B.UNIT_NUMBER,B.BANK_CODE,B.ARAP_DATE,B.HOLDER_NAME,B.BANK_ACCOUNT,B.BUSINESS_TYPE,B.SEQ_NO,B.SPECIAL_ACCOUNT_FLAG,B.IS_CORPORATE
 		 FROM (SELECT ROWNUM RN,A.* FROM (
   					SELECT  MAX(y.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                		    MAX(y.ARAP_FLAG) ARAP_FLAG,
			                TO_CHAR(SUBSTR(WM_CONCAT(DISTINCT y.POLICY_CODE),1,2000)) POLICY_CODE,
			                SUM(                                                                
			                   CASE WHEN 
			                      y.ARAP_FLAG ='${ar}' 
			                       THEN 
			                        y.FEE_AMOUNT 
			                      ELSE 
			                        -1*y.FEE_AMOUNT 
			                       END
			                     ) FEE_AMOUNT,
			                MAX(y.DERIV_TYPE) DERIV_TYPE,
			                MAX(y.PAYEE_NAME) PAYEE_NAME,
			                MAX(y.BUSINESS_CODE) BUSINESS_CODE,
			                MAX(y.AGENT_CODE) AGENT_CODE,
			                MAX(y.DUE_TIME) DUE_TIME,
			                MAX(y.FEE_STATUS) FEE_STATUS,
			                MAX(U.USER_NAME) OPERATOR_BY,
			                MAX(y.FINISH_TIME) FINISH_TIME,    
			                MAX(y.PAY_MODE) PAY_MODE,   
			                MAX(y.UNIT_NUMBER) UNIT_NUMBER ,
			                MAX(y.BANK_CODE) BANK_CODE,
			                MAX(y.HOLDER_NAME) HOLDER_NAME,
			                MAX(y.BANK_ACCOUNT) BANK_ACCOUNT,
			                MAX(y.SEQ_NO) SEQ_NO,
			                MAX(y.BUSINESS_TYPE) BUSINESS_TYPE,
			                (SELECT MAX(T.ARAP_DATE) ARAP_DATE
                	  		  FROM DEV_CAP.T_BILL_DETAILS T
                				WHERE T.UNIT_NUMBER = Y.UNIT_NUMBER) AS ARAP_DATE ,
                			MAX(                                                                
			                   CASE WHEN y.SPECIAL_ACCOUNT_FLAG ='1' THEN '是 '
			                        ELSE '否' 
			                        END
			                     ) SPECIAL_ACCOUNT_FLAG,
               			    MAX(y.IS_CORPORATE) IS_CORPORATE
                	FROM DEV_CAP.T_PREM_ARAP y LEFT JOIN DEV_PAS.T_UDMP_USER U  ON y.OPERATOR_BY = U.USER_ID
                WHERE  y.FEE_STATUS != #{fee_status16} 
                AND y.FEE_STATUS != #{fee_status01}
                AND y.FEE_STATUS != #{fee_status02}
        ]]>
        <include refid="capPremWhereCondition" />
        <if test="ACTUAL_BANK_ACCOUNT != null and ACTUAL_BANK_ACCOUNT != ''">
            <![CDATA[ AND BANK_ACCOUNT = #{ACTUAL_BANK_ACCOUNT} ]]>
        </if>
        <if test="special_account_flag != null and special_account_flag != '' and special_account_flag == '1'.toString() ">
            <![CDATA[ AND SPECIAL_ACCOUNT_FLAG = '1' ]]>
        </if>
	    <if test="special_account_flag != null and special_account_flag != '' and special_account_flag == '0'.toString() ">
            <![CDATA[ AND (SPECIAL_ACCOUNT_FLAG <> '1' or SPECIAL_ACCOUNT_FLAG IS NULL )]]>
        </if>
         <include refid="capPermStatus" />
        <![CDATA[ 
            GROUP BY y.unit_number
                  having 1 = 1
        ]]>
        <if test=" ARAP_FLAG != null and ARAP_FLAG != '' ">
        <![CDATA[ and CASE WHEN SUM(CASE
                WHEN y.ARAP_FLAG = '${ar}' THEN
                 y.FEE_AMOUNT
                ELSE
                  -1 * y.FEE_AMOUNT
                END) >=0  THEN '${ar}' ELSE '${ap}' END = #{ARAP_FLAG}  ]]>
        </if>
        <include refid="capHavingCondition" />
        <![CDATA[ order by  DUE_TIME desc) a where rownum > 0) B where B.RN > #{GREATER_NUM}  AND B.RN  <=#{LESS_NUM}]]>
    </select>

    <select id="capPremDetailTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
             select  count (UNIT_NUMBER) from (SELECT 
                MAX(y.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                MAX(y.ARAP_FLAG) ARAP_FLAG,
                MAX(y.POLICY_CODE) POLICY_CODE,
                SUM(                                                                
                   CASE WHEN 
                      y.ARAP_FLAG ='${ar}' 
                       THEN 
                        y.FEE_AMOUNT 
                      ELSE 
                        -1*y.FEE_AMOUNT 
                       END
                     ) FEE_AMOUNT,
                MAX(y.DERIV_TYPE) DERIV_TYPE,
                MAX(y.PAYEE_NAME) PAYEE_NAME,
                MAX(y.BUSINESS_CODE) BUSINESS_CODE,
                MAX(y.AGENT_CODE) AGENT_CODE,
                MAX(y.DUE_TIME) DUE_TIME,
                MAX(y.FEE_STATUS) FEE_STATUS,
                MAX(y.FINISH_TIME) FINISH_TIME,    
                MAX(y.PAY_MODE) PAY_MODE,   
                MAX(y.UNIT_NUMBER) UNIT_NUMBER,
                MAX(y.HOLDER_NAME) HOLDER_NAME    
                FROM DEV_CAP.T_PREM_ARAP y 
                WHERE  y.FEE_STATUS != #{fee_status16} 
                AND y.FEE_STATUS != #{fee_status01}
                AND y.FEE_STATUS != #{fee_status02}
        ]]>
        <include refid="capPremWhereCondition" />
        <if test="ACTUAL_BANK_ACCOUNT != null and ACTUAL_BANK_ACCOUNT != ''">
            <![CDATA[ AND BANK_ACCOUNT = #{ACTUAL_BANK_ACCOUNT} ]]>
        </if>
        <if test="special_account_flag != null and special_account_flag != '' and special_account_flag == '1'.toString() ">
            <![CDATA[ AND SPECIAL_ACCOUNT_FLAG = '1' ]]>
        </if>
	    <if test="special_account_flag != null and special_account_flag != '' and special_account_flag == '0'.toString() ">
            <![CDATA[ AND (SPECIAL_ACCOUNT_FLAG <> '1' or SPECIAL_ACCOUNT_FLAG IS NULL )]]>
        </if>
         <include refid="capPermStatus" />  
        <![CDATA[ 
            GROUP BY y.unit_number
               having 1 = 1
        ]]>
        <if test=" ARAP_FLAG != null and ARAP_FLAG != '' ">
        <![CDATA[ and CASE WHEN SUM(CASE
                WHEN y.ARAP_FLAG = '${ar}' THEN
                 y.FEE_AMOUNT
                ELSE
                  -1 * y.FEE_AMOUNT
                END) >=0  THEN '${ar}' ELSE '${ap}' END = #{ARAP_FLAG}  ]]>
        </if>
        <include refid="capHavingCondition" />
        <![CDATA[)]]>
    </select>
    <!-- 查询实收付表中的全部记录导出excle用 -->
    <select id="findCapCashDetail" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
     		SELECT ROWNUM RN, 
     			(select f.business_type_name from APP___CAP__DBUSER.t_business_type_def f where f.BUSINESS_TYPE =  B.BUSINESS_TYPE) BUSINESS_TYPE,
     			B.CAP_ORGAN_CODE,
     			B.INVOICE_NO,
     			B.CHEQUE_END_DATE,
     			B.POLICY_ORGAN_CODE,
     			B.FEE_AMOUNT,
     			B.PAY_MODE,
     			CASE WHEN B.FEE_AMOUNT >= 0 THEN '1' ELSE '2' END ARAP_FLAG,
     			B.BUSINESS_CODE,
     			B.PAYEE_NAME,
     			B.ACTUAL_BANK_CODE BANK_CODE,
     			B.ACTUAL_BANK_ACCOUNT BANK_ACCOUNT,
     			B.FEE_STATUS,
     			B.OPERATOR_BY,
     			B.BELNR,
     			B.FINISH_TIME,
     			B.UNIT_NUMBER,
     			B.CASH_DETAIL_ID,
     			B.PAYREFNO,
     			B.POLICY_CODE,
     			B.ARAP_DATE,
     			B.DUE_TIME,
     			B.DERIV_TYPE,
     			B.HOLDER_NAME,
     			(CASE WHEN B.PAY_MODE ='71' THEN 
                    (SELECT MAX(F.TRADE_SN) FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
                WHERE f.UNIT_NUMBER IN ( select D.unit_number
                from APP___CAP__DBUSER.T_PREM_ARAP D
                where D.business_code = B.business_code ) )
                WHEN B.PAY_MODE ='42' and b.DERIV_TYPE='003' then (   select ctos.service_order_id from dev_cap.t_order_service ctos where ctos.biz_code=B.BUSINESS_CODE and ctos.order_trans_result = '0000')
  				WHEN B.PAY_MODE ='42' and b.DERIV_TYPE='004' then (   select ctos.service_order_id from dev_pas.t_order_service ctos where ctos.biz_code=B.BUSINESS_CODE)      
                      ELSE '' END) TRADE_SN,
     			B.CERTI_CODE,
     			(CASE WHEN B.PAY_MODE = '71' AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.SUBMIT_CHANNEL = '11' AND A.POLICY_CODE =B.POLICY_CODE) THEN 'YDWX'
     				 ELSE B.CODE END) CODE,
     			(CASE WHEN B.PAY_MODE = '71' AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.SUBMIT_CHANNEL = '8' AND A.POLICY_CODE =B.POLICY_CODE)
                    THEN '电商'
                    WHEN B.PAY_MODE = '71' AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.SUBMIT_CHANNEL = '11' AND A.POLICY_CODE =B.POLICY_CODE) 
                    THEN '微信' 
                    ELSE '' END ) network_sales
     		FROM (SELECT P.*
     			  FROM (SELECT *
     					FROM (SELECT MAX(C.CAP_ORGAN_CODE) CAP_ORGAN_CODE,
                               		 MAX(CASH_DETAIL_ID) CASH_DETAIL_ID,
                               		 CASE
                               		 	WHEN MAX(C.PAY_MODE) = '11' THEN
                               		 		(SELECT CAB_NO
                               		 		FROM APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK
                               		 		WHERE CAB_ID =
                               		 			(SELECT MAX(BILL_MAIN_ID)
                               		 			FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
                               		 			WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
                               		 	WHEN MAX(C.PAY_MODE) = '20' THEN
                               		 		(SELECT CHEQUE_NO
                               		 		FROM APP___CAP__DBUSER.T_CHEQUE
                               		 		WHERE CHEQUE_ID =
                               		 			(SELECT MAX(BILL_MAIN_ID)
                               		 			FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
                               		 			WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
                               		 	WHEN MAX(C.PAY_MODE) = '31' THEN
                               		 		(SELECT BTR_NO
                               		 		FROM APP___CAP__DBUSER.T_BANK_TRANSFER
                               		 		WHERE BTR_ID =
                               		 			(SELECT MAX(BILL_MAIN_ID)
                               		 			FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
                               		 			WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
                               		 END INVOICE_NO,
                               		 CASE
                               		 	WHEN MAX(C.PAY_MODE) = '20' THEN
                               		 		(SELECT CHEQUE_END_DATE
                               		 		FROM APP___CAP__DBUSER.T_CHEQUE
                               		 		WHERE CHEQUE_ID =
                               		 			(SELECT MAX(BILL_MAIN_ID)
                               		 			FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
                               		 			WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
                               		 		ELSE
                               		 			NULL
                               		 END CHEQUE_END_DATE,
                               		 MAX(C.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                               		 SUM(CASE WHEN ARAP_FLAG = '1' THEN FEE_AMOUNT ELSE -1 * FEE_AMOUNT END) FEE_AMOUNT,
                               		 MAX(C.PAY_MODE) PAY_MODE,
                               		 MAX(C.BUSINESS_CODE) BUSINESS_CODE,
                               		 MAX(C.PAYEE_NAME) PAYEE_NAME,
                               		 MAX(C.ACTUAL_BANK_CODE) ACTUAL_BANK_CODE,
                               		 MAX(C.ACTUAL_BANK_ACCOUNT) ACTUAL_BANK_ACCOUNT,
                               		 MAX(C.FEE_STATUS) FEE_STATUS,
                               		 MAX(U.USER_NAME) OPERATOR_BY,
                               		 MAX(C.BELNR) BELNR,
                               		 MAX(C.FINISH_TIME) FINISH_TIME,
                               		 C.UNIT_NUMBER,
                               		 MAX(C.PAYREFNO) PAYREFNO,
                               		 MAX(C.POLICY_CODE) POLICY_CODE,
                               		 MAX(C.ARAP_DATE) ARAP_DATE,
                               		 MAX(C.DUE_TIME) DUE_TIME,
                               		 MAX(C.DERIV_TYPE) DERIV_TYPE,
                               		 MAX(C.HOLDER_NAME) HOLDER_NAME,
                               		 MAX(C.CERTI_CODE) CERTI_CODE,
                               		 max(C.BUSINESS_TYPE) BUSINESS_TYPE,
                               		 (SELECT F.SERVICER_CODE
                               		 FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
                               		 WHERE F.UNIT_NUMBER = C.UNIT_NUMBER) CODE
                               FROM APP___CAP__DBUSER.T_CASH_DETAIL C LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON 
                               C.OPERATOR_BY = U.USER_ID
                               WHERE (C.PAY_MODE <> '60' OR
								       (C.PAY_MODE = '60'AND C.BOOKKEEPING_FLAG = '1')) 
     	]]>
     	<include refid="capCashWhereCondition" />
     	<![CDATA[ 
     		GROUP BY UNIT_NUMBER, PAY_MODE
            HAVING 1 = 1 
     	]]>
     	<if test=" ARAP_FLAG != null and ARAP_FLAG != '' ">
     		<![CDATA[
     			AND CASE WHEN SUM(CASE
                WHEN ARAP_FLAG = '${ar}' THEN
                 FEE_AMOUNT
                ELSE
                  -1 * FEE_AMOUNT
                END) >=0  THEN '${ar}' ELSE '${ap}' END = #{ARAP_FLAG}
     		]]>
     	</if>
     	<include refid="capHavingCondition" />
     	<![CDATA[ ORDER BY C.FINISH_TIME DESC) ]]>
     	<![CDATA[ UNION ]]>
     	<![CDATA[
     		SELECT *
     		FROM (SELECT MAX(BD.CAP_ORGAN_CODE) CAP_ORGAN_CODE,
                         MAX(BD.BILL_ID) CASH_DETAIL_ID,
                         CASE
                           WHEN MAX(BD.BILL_MODE) = '11' THEN
                            (SELECT CAB_NO
                               FROM APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK
                              WHERE CAB_ID = BD.BILL_MAIN_ID)
                           WHEN MAX(BD.BILL_MODE) = '20' THEN
                            (SELECT CHEQUE_NO
                               FROM APP___CAP__DBUSER.T_CHEQUE
                              WHERE CHEQUE_ID = BD.BILL_MAIN_ID)
                           WHEN MAX(BD.BILL_MODE) = '31' THEN
                            (SELECT BTR_NO
                               FROM APP___CAP__DBUSER.T_BANK_TRANSFER
                              WHERE BTR_ID = BD.BILL_MAIN_ID)
                         END INVOICE_NO,
                         CASE
                           WHEN MAX(BD.BILL_MODE) = '20' THEN
                            (SELECT CHEQUE_END_DATE
                               FROM APP___CAP__DBUSER.T_CHEQUE
                              WHERE CHEQUE_ID = BD.BILL_MAIN_ID)
                           ELSE
                            NULL
                         END CHEQUE_END_DATE,
                         MAX(D.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                         CASE
                           WHEN MAX(BD.BILL_MODE) = '11' THEN
                            (SELECT SUM(FEE_AMOUNT)
                               FROM APP___CAP__DBUSER.T_BILL_DETAILS
                              WHERE UNIT_NUMBER = D.UNIT_NUMBER
                                AND BILL_MAIN_ID =
                                    (SELECT CAB_ID
                                       FROM APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK
                                      WHERE CAB_ID = BD.BILL_MAIN_ID))
                           WHEN MAX(BD.BILL_MODE) = '20' THEN
                            (SELECT SUM(FEE_AMOUNT)
                               FROM APP___CAP__DBUSER.T_BILL_DETAILS
                              WHERE UNIT_NUMBER = D.UNIT_NUMBER
                                AND BILL_MAIN_ID =
                                    (SELECT CHEQUE_ID
                                       FROM APP___CAP__DBUSER.T_CHEQUE
                                      WHERE CHEQUE_ID = BD.BILL_MAIN_ID))
                           WHEN MAX(BD.BILL_MODE) = '31' THEN
                            (SELECT SUM(FEE_AMOUNT)
                               FROM APP___CAP__DBUSER.T_BILL_DETAILS
                              WHERE UNIT_NUMBER = D.UNIT_NUMBER
                                AND BILL_MAIN_ID =
                                    (SELECT BTR_ID
                                       FROM APP___CAP__DBUSER.T_BANK_TRANSFER
                                      WHERE BTR_ID = BD.BILL_MAIN_ID))
                           ELSE
                            0
                         END FEE_AMOUNT,
                         MAX(BD.BILL_MODE) PAY_MODE,
                         MAX(D.BUSINESS_CODE) BUSINESS_CODE,
                         MAX(D.PAYEE_NAME) PAYEE_NAME,
                         MAX(D.BANK_CODE) ACTUAL_BANK_CODE,
                         MAX(D.BANK_ACCOUNT) ACTUAL_BANK_ACCOUNT,
                         MAX(D.FEE_STATUS) FEE_STATUS,
                         MAX(U.USER_NAME) OPERATOR_BY,
                         MAX(D.BELNR) BELNR,
                         MAX(D.FINISH_TIME) FINISH_TIME,
                         MAX(D.UNIT_NUMBER) UNIT_NUMBER,
                         MAX(BD.PAYREFNO) PAYREFNO,
                         MAX(D.POLICY_CODE) POLICY_CODE,
                         MAX(BD.ARAP_DATE) ARAP_DATE,
                         MAX(D.DUE_TIME) DUE_TIME,
                         MAX(D.DERIV_TYPE) DERIV_TYPE,
                         MAX(D.HOLDER_NAME) HOLDER_NAME,
                         MAX(D.CERTI_CODE) CERTI_CODE,
                          MAX(D.BUSINESS_TYPE) BUSINESS_TYPE,
                         (SELECT F.SERVICER_CODE
                            FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
                           WHERE F.UNIT_NUMBER = D.UNIT_NUMBER) CODE
                    FROM APP___CAP__DBUSER.T_BILL_DETAILS BD, APP___CAP__DBUSER.T_PREM_ARAP D LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON D.OPERATOR_BY = U.USER_ID
                    WHERE 1 = 1
                    AND BD.UNIT_NUMBER = D.UNIT_NUMBER
     	]]>
     	<if test="PAY_MODE != null and PAY_MODE != ''">
            <![CDATA[ AND BD.BILL_MODE = #{PAY_MODE} ]]>
        </if>
     	<include refid="capCashForBillAndCustomerWhereCondition" />
     	<![CDATA[
            GROUP BY D.UNIT_NUMBER,BD.BILL_MODE,BD.BILL_MAIN_ID
            HAVING 1 = 1
     	]]>
     	<include refid="capCashHavingForBillAndCustomer" />
        <![CDATA[ORDER BY D.FINISH_TIME DESC)]]>
     	<![CDATA[UNION]]>
     	<![CDATA[
     		SELECT *
            FROM (SELECT MAX(BD.CAP_ORGAN_CODE) CAP_ORGAN_CODE,
                         MAX(BD.BILL_ID) CASH_DETAIL_ID,
                         NULL INVOICE_NO,
                         NULL CHEQUE_END_DATE,
                         MAX(D.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                         S.FEE_AMOUNT FEE_AMOUNT,
                         S.PAY_MODE PAY_MODE,
                         MAX(D.BUSINESS_CODE) BUSINESS_CODE,
                         MAX(D.PAYEE_NAME) PAYEE_NAME,
                         MAX(D.BANK_CODE) ACTUAL_BANK_CODE,
                         MAX(D.BANK_ACCOUNT) ACTUAL_BANK_ACCOUNT,
                         '01' FEE_STATUS,
                         MAX(U.USER_NAME) OPERATOR_BY,
                         MAX(D.BELNR) BELNR,
                         MAX(D.FINISH_TIME) FINISH_TIME,
                         D.UNIT_NUMBER UNIT_NUMBER,
                         MAX(BD.PAYREFNO) PAYREFNO,
                         MAX(D.POLICY_CODE) POLICY_CODE,
                         MAX(BD.ARAP_DATE) ARAP_DATE,
                         MAX(D.DUE_TIME) DUE_TIME,
                         MAX(D.DERIV_TYPE) DERIV_TYPE,
                         MAX(D.HOLDER_NAME) HOLDER_NAME,
                         MAX(D.CERTI_CODE) CERTI_CODE,
                         MAX(D.BUSINESS_TYPE) BUSINESS_TYPE,
                         (SELECT F.SERVICER_CODE
                            FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
                           WHERE F.UNIT_NUMBER = D.UNIT_NUMBER) CODE
                    FROM APP___CAP__DBUSER.T_CUSTOMER_ACCOUNT_DETAILS S, APP___CAP__DBUSER.T_BILL_DETAILS BD , APP___CAP__DBUSER.T_PREM_ARAP D
                    LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON D.OPERATOR_BY = U.USER_ID
                    WHERE 1 = 1
                    AND S.UNIT_NUMBER = D.UNIT_NUMBER
                    AND S.UNIT_NUMBER = BD.UNIT_NUMBER
     	]]>
     	<include refid="capCashForCustomerWhereCondition" />
     	<include refid="capCashForBillAndCustomerWhereCondition" />
     	<![CDATA[
            GROUP BY S.CUS_ACC_DETAILS_ID, S.PAY_MODE, D.UNIT_NUMBER, S.FEE_AMOUNT
            HAVING 1 = 1
     	]]>
     	<include refid="capCashHavingForBillAndCustomer" />
     	<![CDATA[ORDER BY D.FINISH_TIME DESC)]]>
     	<![CDATA[
     		)P
     		WHERE 1 = 1
     	]]>
     	<include refid="capCashAmountCondition" />
     	<include refid="capCashWherenetworkSales" />
     	<![CDATA[) B]]>
    </select>
	
	<select id="bankReconciliationPremList" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT MAX(ROWNUM) RN,
		MAX(TD.BANK_CODE) BANK_CODE,
       TPA.UNIT_NUMBER,
       MAX(TPA.BUSINESS_CODE) BUSINESS_CODE,
       MAX(TD.FEE_AMOUNT) FEE_AMOUNT,
       MAX(TPA.ORGAN_CODE) POLICY_ORGAN_CODE,
       TRUNC(MAX(TPA.FINISH_TIME), 'dd') FINISH_TIME,
       MAX(T.ARAP_FLAG) ARAP_FLAG
	  FROM DEV_CAP.T_BANK_TEXT T
	  LEFT JOIN DEV_CAP.T_BANK_TEXT_DETAIL TD
	    ON TD.SEND_ID = T.SEND_ID
	  LEFT JOIN DEV_CAP.T_PREM_ARAP TPA
	    ON TPA.SEQ_NO = TD.SEQ_NO
	 WHERE 1=1
	 	<if test="feeStartTime != null and feeStartTime != '' ">
	  <![CDATA[AND TPA.FINISH_TIME >= TO_DATE(to_char(#{feeStartTime},'yyyy-MM-dd'), 'yyyy-MM-dd') ]]>
	  </if>
	  <if test="feeEndTime != null and feeEndTime != '' ">
	  <![CDATA[AND TPA.FINISH_TIME < TO_DATE(to_char(#{feeEndTime},'yyyy-MM-dd'), 'yyyy-MM-dd')+1 ]]>
	  </if>
	    <if test="ARAP_FLAG != null and ARAP_FLAG != '' ">
	   <![CDATA[AND T.ARAP_FLAG = #{ARAP_FLAG} ]]>
	   </if>
	   AND (TPA.SPECIAL_ACCOUNT_FLAG != '1' OR TPA.SPECIAL_ACCOUNT_FLAG IS NULL)
	   AND T.BANK_TEXT_STATUS = '7'
	   AND TD.BANK_TEXT_STATUS = '7'
	 GROUP BY TPA.UNIT_NUMBER ORDER BY BANK_CODE
	</select>
	
	<select id="zBankReconciliationPremList" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT MAX(ROWNUM) RN,
		MAX(TD.BANK_CODE) BANK_CODE,
       TPA.UNIT_NUMBER,
       MAX(TPA.BUSINESS_CODE) BUSINESS_CODE,
       MAX(TD.FEE_AMOUNT) FEE_AMOUNT,
       MAX(TPA.ORGAN_CODE) POLICY_ORGAN_CODE,
       TRUNC(MAX(TPA.FINISH_TIME), 'dd') FINISH_TIME,
       MAX(T.ARAP_FLAG) ARAP_FLAG
	  FROM DEV_CAP.T_BANK_TEXT T
	  LEFT JOIN DEV_CAP.T_BANK_TEXT_DETAIL TD
	    ON TD.SEND_ID = T.SEND_ID
	  LEFT JOIN DEV_CAP.T_PREM_ARAP TPA
	    ON TPA.SEQ_NO = TD.SEQ_NO
	 WHERE 1=1
	 	<if test="feeStartTime != null and feeStartTime != '' ">
	  <![CDATA[AND TPA.FINISH_TIME >= TO_DATE(to_char(#{feeStartTime},'yyyy-MM-dd'), 'yyyy-MM-dd') ]]>
	  </if>
	  <if test="feeEndTime != null and feeEndTime != '' ">
	  <![CDATA[AND TPA.FINISH_TIME < TO_DATE(to_char(#{feeEndTime},'yyyy-MM-dd'), 'yyyy-MM-dd')+1 ]]>
	  </if>
	    <if test="ARAP_FLAG != null and ARAP_FLAG != '' ">
	   <![CDATA[AND T.ARAP_FLAG = #{ARAP_FLAG} ]]>
	   </if>
	   AND TPA.SPECIAL_ACCOUNT_FLAG = '1'
	   AND T.BANK_TEXT_STATUS = '7'
	   AND TD.BANK_TEXT_STATUS = '7'
	 GROUP BY TPA.UNIT_NUMBER ORDER BY BANK_CODE
	</select>
	
	<select id="bankReconciliationCashList" resultType="java.util.Map" parameterType="java.util.Map">
		 SELECT MAX(ROWNUM) RN,
		 MAX(T.ACTUAL_BANK_CODE) BANK_CODE,
        T.UNIT_NUMBER,
        MAX(T.ITSM_NO) ITSM_NO,
        MAX(T.BUSINESS_CODE) BUSINESS_CODE,
        SUM(T.FEE_AMOUNT) FEE_AMOUNT,
        MAX(T.ORGAN_CODE) POLICY_ORGAN_CODE,
        TRUNC(MAX(T.FINISH_TIME), 'dd') FINISH_TIME,
        MAX(T.ARAP_FLAG) ARAP_FLAG
   FROM DEV_CAP.T_CASH_DETAIL T
   WHERE T.PAY_MODE = '32' 
   AND (T.SPECIAL_ACCOUNT_FLAG != '1' OR T.SPECIAL_ACCOUNT_FLAG IS NULL)
    <![CDATA[ AND  (T.RED_BOOKKEEPING_FLAG <> 1 OR T.RED_BOOKKEEPING_FLAG IS NULL) ]]>
    <if test="ARAP_FLAG != null and ARAP_FLAG != '' ">
    <![CDATA[AND T.ARAP_FLAG = #{ARAP_FLAG} ]]>
    </if>
    <if test="feeStartTime != null and feeStartTime != '' ">
    <![CDATA[ AND T.FINISH_TIME >= TO_DATE(to_char(#{feeStartTime},'yyyy-MM-dd'), 'yyyy-MM-dd') ]]>
    </if>
    <if test="feeEndTime != null and feeEndTime != '' ">
    <![CDATA[ AND T.FINISH_TIME < TO_DATE(to_char(#{feeEndTime},'yyyy-MM-dd'), 'yyyy-MM-dd') + 1 ]]>
    </if>
  GROUP BY T.UNIT_NUMBER ORDER BY BANK_CODE
	</select>
	
	<select id="zBankReconciliationCashList" resultType="java.util.Map" parameterType="java.util.Map">
		 SELECT MAX(ROWNUM) RN,
		 MAX(T.ACTUAL_BANK_CODE) BANK_CODE,
        T.UNIT_NUMBER,
        MAX(T.ITSM_NO) ITSM_NO,
        MAX(T.BUSINESS_CODE) BUSINESS_CODE,
        SUM(T.FEE_AMOUNT) FEE_AMOUNT,
        MAX(T.ORGAN_CODE) POLICY_ORGAN_CODE,
        TRUNC(MAX(T.FINISH_TIME), 'dd') FINISH_TIME,
        MAX(T.ARAP_FLAG) ARAP_FLAG
   FROM DEV_CAP.T_CASH_DETAIL T
   WHERE T.PAY_MODE = '32'
     AND T.SPECIAL_ACCOUNT_FLAG = '1'
    <![CDATA[ AND  (T.RED_BOOKKEEPING_FLAG <> 1 OR T.RED_BOOKKEEPING_FLAG IS NULL) ]]>
    <if test="ARAP_FLAG != null and ARAP_FLAG != '' ">
    <![CDATA[AND T.ARAP_FLAG = #{ARAP_FLAG} ]]>
    </if>
    <if test="feeStartTime != null and feeStartTime != '' ">
    <![CDATA[ AND T.FINISH_TIME >= TO_DATE(to_char(#{feeStartTime},'yyyy-MM-dd'), 'yyyy-MM-dd') ]]>
    </if>
    <if test="feeEndTime != null and feeEndTime != '' ">
    <![CDATA[ AND T.FINISH_TIME < TO_DATE(to_char(#{feeEndTime},'yyyy-MM-dd'), 'yyyy-MM-dd') + 1 ]]>
    </if>
  GROUP BY T.UNIT_NUMBER ORDER BY BANK_CODE
	</select>
	
    <!-- 查询实收付表中的记录 -->
     <select id="capCashDetail" resultType="java.util.Map" parameterType="java.util.Map">
     	<![CDATA[
     		SELECT B.CAP_ORGAN_CODE,
     			B.INVOICE_NO,
     			B.CHEQUE_END_DATE,
     			B.POLICY_ORGAN_CODE,
     			B.FEE_AMOUNT,
     			B.PAY_MODE,
     			CASE WHEN B.FEE_AMOUNT >= 0 THEN '1' ELSE '2' END ARAP_FLAG,
     			B.BUSINESS_CODE,
     			B.PAYEE_NAME,
     			B.ACTUAL_BANK_CODE BANK_CODE,
     			B.ACTUAL_BANK_ACCOUNT BANK_ACCOUNT,
     			B.FEE_STATUS,
     			B.OPERATOR_BY,
     			B.BELNR,
     			B.FINISH_TIME,
     			B.UNIT_NUMBER,
     			B.CASH_DETAIL_ID,
     			B.PAYREFNO,
     			B.POLICY_CODE,
     			B.ARAP_DATE,
     			B.DUE_TIME,
     			B.DERIV_TYPE,
     			B.HOLDER_NAME,
     			B.CERTI_CODE,
     			(CASE WHEN B.PAY_MODE ='71' THEN 
       		(SELECT MAX(F.TRADE_SN) FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
           		WHERE f.UNIT_NUMBER IN ( select D.unit_number
         		from APP___CAP__DBUSER.T_PREM_ARAP D
        		where D.business_code = B.business_code )
           	) 
           	WHEN B.PAY_MODE ='42' and b.DERIV_TYPE='003' then (   select ctos.service_order_id from dev_cap.t_order_service ctos where ctos.biz_code=B.BUSINESS_CODE and ctos.order_trans_result = '0000')
     		WHEN B.PAY_MODE ='42' and b.DERIV_TYPE='004' then (   select ctos.service_order_id from dev_pas.t_order_service ctos where ctos.biz_code=B.BUSINESS_CODE)
           	ELSE ''
        	 END) TRADE_SN,
     			B.BUSINESS_TYPE,
     			(CASE WHEN B.PAY_MODE = '71' AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.SUBMIT_CHANNEL = '11' AND A.POLICY_CODE =B.POLICY_CODE) THEN 'YDWX'
     				 ELSE B.CODE END) CODE,
     			(CASE WHEN B.PAY_MODE = '71' AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.SUBMIT_CHANNEL = '8' AND A.POLICY_CODE =B.POLICY_CODE)
                    THEN '电商'
                    WHEN B.PAY_MODE = '71' AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.SUBMIT_CHANNEL = '11' AND A.POLICY_CODE =B.POLICY_CODE) 
                    THEN '微信' 
                    ELSE '' END ) network_sales
     		FROM (SELECT Y.*, ROWNUM RN
     		      FROM (SELECT P.*
     			  FROM (SELECT *
     					FROM (SELECT MAX(C.CAP_ORGAN_CODE) CAP_ORGAN_CODE,
                               		 MAX(CASH_DETAIL_ID) CASH_DETAIL_ID,
                               		 CASE
                               		 	WHEN MAX(C.PAY_MODE) = '11' THEN
                               		 		(SELECT CAB_NO
                               		 		FROM APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK
                               		 		WHERE CAB_ID =
                               		 			(SELECT MAX(BILL_MAIN_ID)
                               		 			FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
                               		 			WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
                               		 	WHEN MAX(C.PAY_MODE) = '20' THEN
                               		 		(SELECT CHEQUE_NO
                               		 		FROM APP___CAP__DBUSER.T_CHEQUE
                               		 		WHERE CHEQUE_ID =
                               		 			(SELECT MAX(BILL_MAIN_ID)
                               		 			FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
                               		 			WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
                               		 	WHEN MAX(C.PAY_MODE) = '31' THEN
                               		 		(SELECT BTR_NO
                               		 		FROM APP___CAP__DBUSER.T_BANK_TRANSFER
                               		 		WHERE BTR_ID =
                               		 			(SELECT MAX(BILL_MAIN_ID)
                               		 			FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
                               		 			WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
                               		 END INVOICE_NO,
                               		 CASE
                               		 	WHEN MAX(C.PAY_MODE) = '20' THEN
                               		 		(SELECT CHEQUE_END_DATE
                               		 		FROM APP___CAP__DBUSER.T_CHEQUE
                               		 		WHERE CHEQUE_ID =
                               		 			(SELECT MAX(BILL_MAIN_ID)
                               		 			FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
                               		 			WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
                               		 		ELSE
                               		 			NULL
                               		 END CHEQUE_END_DATE,
                               		 MAX(C.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                               		 SUM(CASE WHEN ARAP_FLAG = '1' THEN FEE_AMOUNT ELSE -1 * FEE_AMOUNT END) FEE_AMOUNT,
                               		 MAX(C.PAY_MODE) PAY_MODE,
                               		 MAX(C.BUSINESS_CODE) BUSINESS_CODE,
                               		 MAX(C.PAYEE_NAME) PAYEE_NAME,
                               		 MAX(C.ACTUAL_BANK_CODE) ACTUAL_BANK_CODE,
                               		 MAX(C.ACTUAL_BANK_ACCOUNT) ACTUAL_BANK_ACCOUNT,
                               		 MAX(C.FEE_STATUS) FEE_STATUS,
                               		 MAX(U.USER_NAME) OPERATOR_BY,
                               		 MAX(C.BELNR) BELNR,
                               		 MAX(C.FINISH_TIME) FINISH_TIME,
                               		 C.UNIT_NUMBER,
                               		 MAX(C.PAYREFNO) PAYREFNO,
                               		 TO_CHAR(SUBSTR(WM_CONCAT(DISTINCT C.POLICY_CODE),1,2000)) POLICY_CODE,
                               		 MAX(C.ARAP_DATE) ARAP_DATE,
                               		 MAX(C.DUE_TIME) DUE_TIME,
                               		 MAX(C.DERIV_TYPE) DERIV_TYPE,
                               		 MAX(C.BUSINESS_TYPE) BUSINESS_TYPE,
                               		 MAX(C.HOLDER_NAME) HOLDER_NAME,
                               		 MAX(C.CERTI_CODE) CERTI_CODE,
                               		 (SELECT F.SERVICER_CODE
                               		 FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
                               		 WHERE F.UNIT_NUMBER = C.UNIT_NUMBER) CODE
                               FROM APP___CAP__DBUSER.T_CASH_DETAIL C LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON C.OPERATOR_BY = U.USER_ID
                               WHERE (C.PAY_MODE <> '60' OR
								       (C.PAY_MODE = '60'AND C.BOOKKEEPING_FLAG = '1')) 
     	]]>
     	<include refid="capCashWhereCondition" />
     	<![CDATA[ 
     		GROUP BY UNIT_NUMBER, PAY_MODE
            HAVING 1 = 1 
     	]]>
     	<if test=" ARAP_FLAG != null and ARAP_FLAG != '' ">
     		<![CDATA[
     			AND CASE WHEN SUM(CASE
                WHEN ARAP_FLAG = '${ar}' THEN
                 FEE_AMOUNT
                ELSE
                  -1 * FEE_AMOUNT
                END) >=0  THEN '${ar}' ELSE '${ap}' END = #{ARAP_FLAG}
     		]]>
     	</if>
     	<include refid="capHavingCondition" />
     	<![CDATA[ ORDER BY C.FINISH_TIME DESC) ]]>
     	<![CDATA[ UNION ]]>
     	<![CDATA[
     		SELECT *
     		FROM (SELECT MAX(BD.CAP_ORGAN_CODE) CAP_ORGAN_CODE,
                         MAX(BD.BILL_ID) CASH_DETAIL_ID,
                         CASE
                           WHEN MAX(BD.BILL_MODE) = '11' THEN
                            (SELECT CAB_NO
                               FROM APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK
                              WHERE CAB_ID = BD.BILL_MAIN_ID)
                           WHEN MAX(BD.BILL_MODE) = '20' THEN
                            (SELECT CHEQUE_NO
                               FROM APP___CAP__DBUSER.T_CHEQUE
                              WHERE CHEQUE_ID = BD.BILL_MAIN_ID)
                           WHEN MAX(BD.BILL_MODE) = '31' THEN
                            (SELECT BTR_NO
                               FROM APP___CAP__DBUSER.T_BANK_TRANSFER
                              WHERE BTR_ID = BD.BILL_MAIN_ID)
                         END INVOICE_NO,
                         CASE
                           WHEN MAX(BD.BILL_MODE) = '20' THEN
                            (SELECT CHEQUE_END_DATE
                               FROM APP___CAP__DBUSER.T_CHEQUE
                              WHERE CHEQUE_ID = BD.BILL_MAIN_ID)
                           ELSE
                            NULL
                         END CHEQUE_END_DATE,
                         MAX(D.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                         CASE
                           WHEN MAX(BD.BILL_MODE) = '11' THEN
                            (SELECT SUM(FEE_AMOUNT)
                               FROM APP___CAP__DBUSER.T_BILL_DETAILS
                              WHERE UNIT_NUMBER = D.UNIT_NUMBER
                                AND BILL_MAIN_ID =
                                    (SELECT CAB_ID
                                       FROM APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK
                                      WHERE CAB_ID = BD.BILL_MAIN_ID))
                           WHEN MAX(BD.BILL_MODE) = '20' THEN
                            (SELECT SUM(FEE_AMOUNT)
                               FROM APP___CAP__DBUSER.T_BILL_DETAILS
                              WHERE UNIT_NUMBER = D.UNIT_NUMBER
                                AND BILL_MAIN_ID =
                                    (SELECT CHEQUE_ID
                                       FROM APP___CAP__DBUSER.T_CHEQUE
                                      WHERE CHEQUE_ID = BD.BILL_MAIN_ID))
                           WHEN MAX(BD.BILL_MODE) = '31' THEN
                            (SELECT SUM(FEE_AMOUNT)
                               FROM APP___CAP__DBUSER.T_BILL_DETAILS
                              WHERE UNIT_NUMBER = D.UNIT_NUMBER
                                AND BILL_MAIN_ID =
                                    (SELECT BTR_ID
                                       FROM APP___CAP__DBUSER.T_BANK_TRANSFER
                                      WHERE BTR_ID = BD.BILL_MAIN_ID))
                           ELSE
                            0
                         END FEE_AMOUNT,
                         MAX(BD.BILL_MODE) PAY_MODE,
                         MAX(D.BUSINESS_CODE) BUSINESS_CODE,
                         MAX(D.PAYEE_NAME) PAYEE_NAME,
                         MAX(D.BANK_CODE) ACTUAL_BANK_CODE,
                         MAX(D.BANK_ACCOUNT) ACTUAL_BANK_ACCOUNT,
                         MAX(D.FEE_STATUS) FEE_STATUS,
                         MAX(U.USER_NAME) OPERATOR_BY,
                         MAX(D.BELNR) BELNR,
                         MAX(D.FINISH_TIME) FINISH_TIME,
                         MAX(D.UNIT_NUMBER) UNIT_NUMBER,
                         MAX(BD.PAYREFNO) PAYREFNO,
                         TO_CHAR(SUBSTR(WM_CONCAT(DISTINCT D.POLICY_CODE),1,2000)) POLICY_CODE,
                         MAX(BD.ARAP_DATE) ARAP_DATE,
                         MAX(D.DUE_TIME) DUE_TIME,
                         MAX(D.DERIV_TYPE) DERIV_TYPE,
                         MAX(D.BUSINESS_TYPE) BUSINESS_TYPE,
                         MAX(D.HOLDER_NAME) HOLDER_NAME,
                         MAX(D.CERTI_CODE) CERTI_CODE,
                         (SELECT F.SERVICER_CODE
                            FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
                           WHERE F.UNIT_NUMBER = D.UNIT_NUMBER) CODE
                    FROM APP___CAP__DBUSER.T_BILL_DETAILS BD, APP___CAP__DBUSER.T_PREM_ARAP D LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON D.OPERATOR_BY = U.USER_ID
                    WHERE 1 = 1
                    AND BD.UNIT_NUMBER = D.UNIT_NUMBER 
     	]]>
     	<if test="PAY_MODE != null and PAY_MODE != ''">
            <![CDATA[ AND BD.BILL_MODE = #{PAY_MODE} ]]>
        </if>
     	<include refid="capCashForBillAndCustomerWhereCondition" />
     	<![CDATA[
            GROUP BY D.UNIT_NUMBER,BD.BILL_MODE,BD.BILL_MAIN_ID
            HAVING 1 = 1
     	]]>
     	<include refid="capCashHavingForBillAndCustomer" />
     	<![CDATA[ORDER BY D.FINISH_TIME DESC)]]>
     	<![CDATA[UNION]]>
     	<![CDATA[
     		SELECT *
            FROM (SELECT MAX(BD.CAP_ORGAN_CODE) CAP_ORGAN_CODE,
                         MAX(BD.BILL_ID) CASH_DETAIL_ID,
                         NULL INVOICE_NO,
                         NULL CHEQUE_END_DATE,
                         MAX(D.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
                         S.FEE_AMOUNT FEE_AMOUNT,
                         S.PAY_MODE PAY_MODE,
                         MAX(D.BUSINESS_CODE) BUSINESS_CODE,
                         MAX(D.PAYEE_NAME) PAYEE_NAME,
                         MAX(D.BANK_CODE) ACTUAL_BANK_CODE,
                         MAX(D.BANK_ACCOUNT) ACTUAL_BANK_ACCOUNT,
                         '01' FEE_STATUS,
                         MAX(U.USER_NAME) OPERATOR_BY,
                         MAX(D.BELNR) BELNR,
                         MAX(D.FINISH_TIME) FINISH_TIME,
                         D.UNIT_NUMBER UNIT_NUMBER,
                         MAX(BD.PAYREFNO) PAYREFNO,
                         TO_CHAR(SUBSTR(WM_CONCAT(DISTINCT D.POLICY_CODE),1,2000)) POLICY_CODE,
                         MAX(BD.ARAP_DATE) ARAP_DATE,
                         MAX(D.DUE_TIME) DUE_TIME,
                         MAX(D.DERIV_TYPE) DERIV_TYPE,
                         MAX(D.BUSINESS_TYPE) BUSINESS_TYPE,
                         MAX(D.HOLDER_NAME) HOLDER_NAME,
                         MAX(D.CERTI_CODE) CERTI_CODE,
                         (SELECT F.SERVICER_CODE
                            FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
                           WHERE F.UNIT_NUMBER = D.UNIT_NUMBER) CODE
                    FROM APP___CAP__DBUSER.T_CUSTOMER_ACCOUNT_DETAILS S,APP___CAP__DBUSER.T_BILL_DETAILS BD, APP___CAP__DBUSER.T_PREM_ARAP D  LEFT JOIN
                    APP___CAP__DBUSER.T_UDMP_USER U ON D.OPERATOR_BY = U.USER_ID
                    WHERE 1 = 1  
                    AND S.UNIT_NUMBER = D.UNIT_NUMBER
                    AND S.UNIT_NUMBER = BD.UNIT_NUMBER
     	]]>
     	<include refid="capCashForCustomerWhereCondition" />
     	<include refid="capCashForBillAndCustomerWhereCondition" />
     	<![CDATA[
            GROUP BY S.CUS_ACC_DETAILS_ID, S.PAY_MODE, D.UNIT_NUMBER, S.FEE_AMOUNT
            HAVING 1 = 1
     	]]>
     	<include refid="capCashHavingForBillAndCustomer" />
     	<![CDATA[ORDER BY D.FINISH_TIME DESC)]]>
     	<![CDATA[
     		)P
     		WHERE 1 = 1
     	]]>
     	<include refid="capCashAmountCondition" />
     	<include refid="capCashWherenetworkSales" />
     	<![CDATA[)Y]]>
     	<![CDATA[WHERE ROWNUM <= #{LESS_NUM}) B]]>
     	<![CDATA[WHERE B.RN > #{GREATER_NUM}]]>
    </select>


    <select id="capCashDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
        <![CDATA[
     		SELECT COUNT(1)
     		FROM (SELECT P.*
     			  FROM (SELECT *
     					FROM (SELECT C.UNIT_NUMBER,C.PAY_MODE,abs(SUM( CASE WHEN C.ARAP_FLAG ='1'THEN C.FEE_AMOUNT ELSE  -1* C.FEE_AMOUNT END)) FEE_AMOUNT, MAX(C.POLICY_CODE) POLICY_CODE,
     					     (SELECT F.SERVICER_CODE FROM APP___CAP__DBUSER.T_FMS_INTERFACE F
                              WHERE F.UNIT_NUMBER = C.UNIT_NUMBER) CODE
                               FROM APP___CAP__DBUSER.T_CASH_DETAIL C LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U  ON C.OPERATOR_BY = U.USER_ID
                               WHERE (C.PAY_MODE <> '60' OR 
								       (C.PAY_MODE = '60'AND C.BOOKKEEPING_FLAG = '1'))
     	]]>
     	<include refid="capCashWhereCondition" />
     	<![CDATA[ 
     		GROUP BY UNIT_NUMBER,PAY_MODE
            HAVING 1 = 1 
     	]]>
     	<if test=" ARAP_FLAG != null and ARAP_FLAG != '' ">
     		<![CDATA[
     			AND CASE WHEN SUM(CASE
                WHEN ARAP_FLAG = '${ar}' THEN
                 FEE_AMOUNT
                ELSE
                  -1 * FEE_AMOUNT
                END) >=0  THEN '${ar}' ELSE '${ap}' END = #{ARAP_FLAG}
     		]]>
     	</if>
     	<include refid="capHavingCondition" />
     	<![CDATA[)
         UNION ]]>
     	<![CDATA[
     		SELECT *
     		FROM (SELECT  D.UNIT_NUMBER,BD.BILL_MODE,abs(SUM( CASE WHEN D.ARAP_FLAG ='1'THEN D.FEE_AMOUNT ELSE  -1* D.FEE_AMOUNT END)) FEE_AMOUNT, MAX(D.POLICY_CODE) POLICY_CODE,
     		      NULL CODE
                    FROM APP___CAP__DBUSER.T_BILL_DETAILS BD, APP___CAP__DBUSER.T_PREM_ARAP D LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON D.OPERATOR_BY = U.USER_ID
                    WHERE 1 = 1
                    AND BD.UNIT_NUMBER = D.UNIT_NUMBER 
     	]]>
        <if test="PAY_MODE != null and PAY_MODE != ''">
            <![CDATA[ AND BD.BILL_MODE = #{PAY_MODE} ]]>
        </if>
       
     	<include refid="capCashForBillAndCustomerWhereCondition" />
     	<![CDATA[
            GROUP BY D.UNIT_NUMBER,BD.BILL_MAIN_ID,BD.BILL_MODE
            HAVING 1 = 1
     	]]>
     	<include refid="capCashHavingForBillAndCustomer" />
     	<![CDATA[)
        UNION]]>
     	<![CDATA[
     		SELECT *
            FROM (SELECT D.UNIT_NUMBER,S.PAY_MODE BILL_MODE,abs(SUM( CASE WHEN D.ARAP_FLAG ='1'THEN D.FEE_AMOUNT ELSE  -1* D.FEE_AMOUNT END)) FEE_AMOUNT, MAX(D.POLICY_CODE) POLICY_CODE,
                  NULL CODE
                    FROM APP___CAP__DBUSER.T_CUSTOMER_ACCOUNT_DETAILS S, 
                    APP___CAP__DBUSER.T_BILL_DETAILS BD, APP___CAP__DBUSER.T_PREM_ARAP D LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON D.OPERATOR_BY = U.USER_ID
                    WHERE 1 = 1
                    AND S.UNIT_NUMBER = D.UNIT_NUMBER
                    AND S.UNIT_NUMBER = BD.UNIT_NUMBER
     	]]>
     	<include refid="capCashForCustomerWhereCondition" />
     	<include refid="capCashForBillAndCustomerWhereCondition" />
     	<![CDATA[
            GROUP BY D.UNIT_NUMBER,S.PAY_MODE,S.FEE_AMOUNT
            HAVING 1 = 1
     	]]>
     	<include refid="capCashHavingForBillAndCustomer" />
     	<![CDATA[))P
     	   where 1=1
     	   ]]>
     	  <include refid="capCashWherenetworkSales" />
     	<![CDATA[
     	)]]>
    </select>
    <!-- 查询页面显示的总金额 -->
    <select id="capCashDetailSumAmount" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
     		SELECT SUM(R.FEE_AMOUNT) FEE_AMOUNT
     		FROM (SELECT P.*
     			  FROM (SELECT *
     					FROM (SELECT 
                               		 C.UNIT_NUMBER,SUM(CASE WHEN ARAP_FLAG = '1' THEN FEE_AMOUNT ELSE -1 * FEE_AMOUNT END) FEE_AMOUNT,MAX(C.POLICY_CODE) POLICY_CODE
                               FROM APP___CAP__DBUSER.T_CASH_DETAIL C LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON C.OPERATOR_BY = U.USER_ID
                               WHERE (C.PAY_MODE not in ('60','99','50') OR
								       (C.PAY_MODE = '60'AND C.BOOKKEEPING_FLAG = '1')) 
     	]]>
     	<include refid="capCashWhereCondition" />
     	<if test=" ARAP_FLAG != null and ARAP_FLAG != '' ">
     		<![CDATA[
     			AND ARAP_FLAG = #{ARAP_FLAG}
     		]]>
     	</if>
     	<![CDATA[ 
     		GROUP BY UNIT_NUMBER
		HAVING 1 = 1 
     	]]>
     	<include refid="capHavingCondition" />
     	<![CDATA[ ) ]]>
     	
     	<![CDATA[
     		)P
     		WHERE 1 = 1
     	]]>
     	<include refid="capCashAmountCondition" />
     	<include refid="capCashWherenetworkSales" />
     	<![CDATA[)R]]>
    </select> 
    <sql id="CapDetailByUnitNoWhereClause">
        <if test="unit_number != null and unit_number !='' ">
            <![CDATA[ AND UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ]]>
        </if>
    </sql>
    <!-- 根据实际收费付费日期 对实收表进行查询 的where 条件 -->
    <sql id="CapDetailByFeeTimeWhereClause">
        <if test="FINISH_TIME != null">
            <![CDATA[ AND C.FINISH_TIME = #{FINISH_TIME} ]]>
        </if>
    </sql>

    <!-- 根据实际收费付费流水号 对实收表进行查询 的where 条件 -->
    <sql id="CapCashDetailByUnitNoWhereClause">
        <!-- 已红冲的记录不查询 -->      
        <if test="red_bookkeeping_flag != null">
            <![CDATA[ AND (C.RED_BOOKKEEPING_FLAG is null  or C.RED_BOOKKEEPING_FLAG <> #{red_bookkeeping_flag} )]]>
        </if>
        <if test="UNIT_NUMBER != null">
            <![CDATA[ AND C.UNIT_NUMBER = #{UNIT_NUMBER, jdbcType=VARCHAR} ]]>
        </if>
    </sql>
 
    <!--通过实收付时间来查询相应的记录 -->
    <select id="queryCapDetailByFeeTime" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT C.POLICY_CODE,C.PAY_MODE,C.FEE_AMOUNT,C.FINISH_TIME,C.BELNR,
        C.ACTUAL_BANK_CODE BANK_CODE,C.ACTUAL_BANK_ACCOUNT,C.PAYEE_NAME,
        CASE WHEN C.PAY_MODE = '11' THEN
        (SELECT CAB_NO FROM APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK WHERE CAB_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
        WHEN C.PAY_MODE = '20' THEN
        (SELECT CHEQUE_NO FROM APP___CAP__DBUSER.T_CHEQUE WHERE CHEQUE_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
        WHEN C.PAY_MODE = '30' THEN
         (SELECT BTR_NO FROM APP___CAP__DBUSER.T_BANK_TRANSFER WHERE BTR_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER) )       
         END INVOICE_NO,
         CASE WHEN C.PAY_MODE = '20' THEN
        (SELECT CHEQUE_END_DATE FROM APP___CAP__DBUSER.T_CHEQUE WHERE CHEQUE_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
        ELSE
          NULL END CHEQUE_END_DATE,  
        CASE WHEN C.PAY_MODE = '20' THEN
        (SELECT FEE_AMOUNT FROM APP___CAP__DBUSER.T_CHEQUE WHERE CHEQUE_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
        ELSE
          0 END CHEQUE_FEE_AMOUNT  FROM APP___CAP__DBUSER.T_CASH_DETAIL C WHERE 1=1          
        ]]>
        <include refid="CapDetailByFeeTimeWhereClause" />

    </select>

    <select id="queryCapDetailTotalByUnitNo" resultType="java.lang.Integer"
        parameterType="java.lang.Integer">
        <![CDATA[
            SELECT COUNT(*) FROM APP___CAP__DBUSER.T_PREM_ARAP WHERE 1=1 AND UNIT_NUMBER = #{unit_number}
        ]]>
    </select>

    <!--通过实收付流水号来查询相应的记录 -->
    <select id="queryCapCashDetailByUnitNo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT C.POLICY_CODE,C.PAY_MODE,C.FEE_AMOUNT,C.FINISH_TIME,C.BELNR,
        C.ACTUAL_BANK_CODE BANK_CODE,C.ACTUAL_BANK_ACCOUNT BANK_ACCOUNT,C.PAYEE_NAME,
        CASE WHEN C.PAY_MODE = '11' THEN
        (SELECT CAB_NO FROM APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK WHERE CAB_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
        WHEN C.PAY_MODE = '20' THEN
        (SELECT CHEQUE_NO FROM APP___CAP__DBUSER.T_CHEQUE WHERE CHEQUE_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
        WHEN C.PAY_MODE = '30' THEN
         (SELECT BTR_NO FROM APP___CAP__DBUSER.T_BANK_TRANSFER WHERE BTR_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER) )       
         END INVOICE_NO,
         CASE WHEN C.PAY_MODE = '20' THEN
        (SELECT CHEQUE_END_DATE FROM APP___CAP__DBUSER.T_CHEQUE WHERE CHEQUE_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
        ELSE
          NULL END CHEQUE_END_DATE,  
        CASE WHEN C.PAY_MODE = '20' THEN
        (SELECT FEE_AMOUNT FROM APP___CAP__DBUSER.T_CHEQUE WHERE CHEQUE_ID = (SELECT max(BILL_MAIN_ID) FROM APP___CAP__DBUSER.T_BILL_DETAILS BD
        WHERE BD.UNIT_NUMBER = C.UNIT_NUMBER))
        ELSE
          0 END CHEQUE_FEE_AMOUNT  FROM APP___CAP__DBUSER.T_CASH_DETAIL C WHERE 1=1          
        ]]>
        <include refid="CapCashDetailByUnitNoWhereClause" />
    </select>

    <!-- 付款信息操作 -->
    <select id="findCapDetailByPolicyCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
	       SELECT DISTINCT TCD.UNIT_NUMBER,
                TCD.BUSINESS_CODE,
                TCD.PAY_MODE,
                (SELECT  SUM(FEE_AMOUNT) FROM APP___CAP__DBUSER.T_CASH_DETAIL WHERE UNIT_NUMBER=TCD.UNIT_NUMBER)FEE_AMOUNT,
                TCD.PAYEE_NAME AS PAYEE_NAME,
                TCD.CERTI_CODE AS CERTI_CODE,
                TCD.ARAP_BANK_CODE,
                TCD.ARAP_BANK_ACCOUNT,
                NVL(TCD.DUE_TIME,TCD.INSERT_TIME)DUE_TIME,
                TCD.FINISH_TIME
           FROM APP___CAP__DBUSER.T_CASH_DETAIL TCD
	        WHERE TCD.BUSINESS_CODE = #{POLICY_CODE}
	          AND TCD.DERIV_TYPE = #{deriv_type} 
	          AND TCD.ARAP_FLAG='2'                
	   ]]>
    </select>


	<!-- 交费信息查询 -->
    <select id="queryPaymentInformationList" resultType="java.util.Map" parameterType="java.util.Map">
		<if test=" count_page == 'page' "><![CDATA[
			SELECT * FROM (SELECT ROWNUM RN,RES.* FROM (
		]]></if>
		<choose>
			<when test=" count_page == 'count' "><![CDATA[ 
			SELECT COUNT(1) TOTAL_NUM,SUM(FEE_AMOUNT) FEE_AMOUNT_SUM 
			FROM (SELECT DISTINCT (SELECT SUM(CASE WHEN ARAP_FLAG='2' THEN -FEE_AMOUNT ELSE FEE_AMOUNT END) 
				FROM APP___CAP__DBUSER.V_PREM_ARAP WHERE UNIT_NUMBER=A.UNIT_NUMBER AND POLICY_CODE=A.POLICY_CODE 
				AND FEE_STATUS IN ('01','19')) FEE_AMOUNT, A.UNIT_NUMBER 
			]]></when>
			<otherwise><![CDATA[
			SELECT * FROM( 
				SELECT A.UNIT_NUMBER,A.BUSI_PROD_CODE,A.POLICY_CODE,A.FEE_TYPE_SUFFIX,A.PAID_COUNT,
				(SELECT PRODUCT_NAME_SYS FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT 
					WHERE PRODUCT_CODE_SYS=A.BUSI_PROD_CODE) AS BUSI_PROD_NAME,
				MAX(A.PAY_MODE) AS PAY_MODE,
				MAX(A.FEE_STATUS) AS FEE_STATUS,
				MAX(A.DERIV_TYPE) AS DERIV_TYPE,
				MAX(A.PREM_FREQ) AS PREM_FREQ,
				MAX(A.DUE_TIME) AS DUE_TIME,
				MAX(A.MAKE_DATE) AS MAKE_DATE,
				MAX(A.FEE_TYPE) AS FEE_TYPE_CODE,
				MAX(A.FINISH_TIME) AS FINISH_TIME,
				SUM(A.FEE_AMOUNT) AS FEE_AMOUNT_TOTAL,
				(SELECT PAY_LOCATION FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT WHERE POLICY_ID=(SELECT POLICY_ID 
					FROM APP___PAS__DBUSER.T_CONTRACT_MASTER WHERE POLICY_CODE=A.POLICY_CODE) AND ROWNUM=1) AS SALE_CHNL
			]]></otherwise>
		</choose>
		<![CDATA[ 
			FROM (SELECT A.POLICY_CODE,A.UNIT_NUMBER,A.BUSINESS_CODE,A.BUSI_PROD_CODE,
			A.PAY_MODE,A.FEE_STATUS,A.DERIV_TYPE,A.PREM_FREQ,A.DUE_TIME,A.FEE_TYPE,A.PAID_COUNT,A.FINISH_TIME,
			(CASE WHEN A.FINISH_TIME > A.INSERT_TIME THEN A.FINISH_TIME ELSE A.INSERT_TIME END) AS MAKE_DATE,
			(CASE WHEN A.FEE_TYPE like '%000' THEN '001' ELSE SUBSTR(A.FEE_TYPE,LENGTH(A.FEE_TYPE) - 2) END) FEE_TYPE_SUFFIX,
			(CASE WHEN A.ARAP_FLAG='2' THEN -A.FEE_AMOUNT ELSE A.FEE_AMOUNT END) FEE_AMOUNT
			FROM APP___CAP__DBUSER.V_PREM_ARAP A
			WHERE 1 = 1
			AND A.POLICY_CODE = #{POLICY_CODE}
		]]>
   		<include refid="capPermForLjapaypersonWhereCondition"/>
   		<![CDATA[
			) A WHERE 1=1
	   	]]>
   		<include refid="queryCashDetailByStartEndDate"/>
		<![CDATA[
			GROUP BY A.UNIT_NUMBER, A.POLICY_CODE, A.BUSI_PROD_CODE, A.PAID_COUNT, A.FEE_TYPE_SUFFIX) A WHERE 1=1
		]]>
		<include refid="queryCashDetailByOrderby_cjk"/>
		<if test=" count_page == 'page' ">
			<![CDATA[) RES) WHERE RN>=#{row_num_start} AND RN<#{row_num_end}]]>
		</if>
    </select>
	<!-- 查询老核心实收表LJAPAYPERSON -->
    <sql id="capPermForLjapaypersonWhereCondition">
		<![CDATA[ 
			AND A.FEE_STATUS IN ('01','19')
			AND A.BUSI_PROD_CODE IS NOT NULL AND A.PAID_COUNT IS NOT NULL
			AND A.FEE_TYPE IN ('G001010000', 'G001010001', 'G001070100', 'G001070200', 'G001070300', 'G001080100',
                               'G001080101', 'G001080200', 'G001080201', 'G001080300', 'G001080301', 'G003010000', 
                               'G003010001', 'G003100000', 'G003100001', 'G003020100', 'G003020200', 'G003020300', 
                               'G003030100', 'G003030101', 'G003030200', 'G003030201', 'G003030300', 'T003160100', 
                               'T003160200', 'G004090000', 'G004090001', 'G004010000', 'G004010001', 'G004040100',
                               'G004040101', 'G004040200', 'G004040201', 'G004040300', 'G004040301', 'G004740100', 
                               'G004740200', 'G004740300', 'G004510100', 'G004510101', 'G004510200', 'G004510201', 
                               'G004510300', 'G004510301', 'G004520100',  'G004520200', 'G004520300', 'G005130000', 
                               'G005130001', 'G005330000', 'G005330001', 'G003090000')
			AND A.WITHDRAW_TYPE IN('**********', '**********', '**********', '**********', '**********', 
				'**********', '**********', '**********', '**********', '003050100A', '**********', '**********', 
				'**********', '**********', '**********', '**********', '**********', '**********', '**********',
				'**********')
		]]>
    </sql>

<!-- 状态为取消的时，查询rollbankUnitNumber为空的数据  -->
    <sql id="capPermStatus">
        
            <![CDATA[ AND (fee_status != '${fee_status02}' OR (fee_status = '${fee_status02}' AND y.ROLLBACK_UNIT_NUMBER is null ))]]>
        
    </sql>

	<sql id="capCashForBillAndCustomerWhereCondition">
        <if test="feeStartTime != null and feeStartTime != ''">
            <![CDATA[ AND BD.ARAP_DATE >= to_date(to_char(#{feeStartTime}, 'yyyy-mm-dd'), 'yyyy-mm-dd') ]]>
        </if>
        <if test="feeEndTime != null and feeEndTime != ''">
            <![CDATA[ AND BD.ARAP_DATE < to_date(to_char(#{feeEndTime}, 'yyyy-mm-dd'), 'yyyy-mm-dd') + 1 ]]>
        </if>
		<if test="fee_status15 != null and fee_status15 != ''">
            <![CDATA[ AND D.FEE_STATUS = #{fee_status15} ]]>
        </if>
        <if test="payrefno != null and payrefno != ''">
            <![CDATA[ AND BD.PAYREFNO = #{payrefno} ]]>
        </if>
        <if test="UNIT_NUMBER != null and UNIT_NUMBER != ''">
            <![CDATA[ AND BD.UNIT_NUMBER = #{UNIT_NUMBER} ]]>
        </if>
        <if test="CAP_ORGAN_CODE != null and CAP_ORGAN_CODE != ''">
            <![CDATA[ AND (BD.CAP_ORGAN_CODE IN (SELECT ORG_REL.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL ORG_REL
                    START WITH ORG_REL.UPORGAN_CODE = #{CAP_ORGAN_CODE} 
                    CONNECT BY PRIOR ORG_REL.ORGAN_CODE = ORG_REL.UPORGAN_CODE) OR BD.CAP_ORGAN_CODE = #{CAP_ORGAN_CODE})
        ]]>
        </if>
        <if test="POLICY_ORGAN_CODE != null and POLICY_ORGAN_CODE != ''">
        <![CDATA[ AND (D.POLICY_ORGAN_CODE IN (SELECT ORG_REL.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL ORG_REL
                 START WITH ORG_REL.UPORGAN_CODE = #{POLICY_ORGAN_CODE} 
                 CONNECT BY PRIOR ORG_REL.ORGAN_CODE = ORG_REL.UPORGAN_CODE) OR D.POLICY_ORGAN_CODE = #{POLICY_ORGAN_CODE}) ]]>
        </if>
        <if test="BUSINESS_CODE != null and BUSINESS_CODE != ''">
            <![CDATA[ AND D.BUSINESS_CODE = #{BUSINESS_CODE} ]]>
        </if>
        <if test="BANK_CODE != null and BANK_CODE != ''">
            <![CDATA[ AND D.BANK_CODE = #{BANK_CODE} ]]>
        </if>
        <choose>
        	<when test="HOLDER_NAME != null and HOLDER_NAME != ''">
        		<![CDATA[ AND D.HOLDER_NAME = #{HOLDER_NAME} ]]>
        	</when>
        	<when test="PAYEE_NAME != null and PAYEE_NAME != ''">
        		<![CDATA[ AND D.PAYEE_NAME = #{PAYEE_NAME} ]]>
        	</when>
        	<when test="PAYEE_NAME != null and PAYEE_NAME != '' and HOLDER_NAME != null and HOLDER_NAME != ''">
        		<![CDATA[ AND (D.PAYEE_NAME = #{PAYEE_NAME} OR D. HOLDER_NAME = #{HOLDER_NAME})]]>
        	</when>
        	<otherwise></otherwise>
        </choose>
        <if test="POLICY_CODE != null and POLICY_CODE != ''">
            <![CDATA[ AND D.POLICY_CODE = #{POLICY_CODE} ]]>
        </if>
        <if test="CHANNEL_TYPE != null and CHANNEL_TYPE != ''">
            <![CDATA[ AND TRIM(D.CHANNEL_TYPE) = #{CHANNEL_TYPE} ]]>
        </if>
        <if test="FEE_STATUS != null and FEE_STATUS != ''">
            <![CDATA[ AND D.FEE_STATUS = #{FEE_STATUS} ]]>
        </if>
        <if test="DERIV_TYPE != null and DERIV_TYPE != ''">
            <![CDATA[ AND D.DERIV_TYPE = #{DERIV_TYPE} ]]>
        </if>
       
        <if test="INSERT_BY != null and INSERT_BY != ''">
            <![CDATA[ AND D.INSERT_BY = #{INSERT_BY} ]]>
        </if>
        <if test="belnr != null and belnr != ''">
            <![CDATA[ AND D.BELNR = #{belnr} ]]>
        </if>
        <if test="CERTI_CODE != null and CERTI_CODE != ''">
            <![CDATA[ AND D.CERTI_CODE = #{CERTI_CODE} ]]>
        </if>
        <if test="payModeByDeduction != null and payModeByDeduction != ''">
            <![CDATA[ AND D.PAY_MODE != #{payModeByDeduction}  and D.PAY_MODE != '50' ]]>
        </if>
        <if test="BANK_ACCOUNT != null and BANK_ACCOUNT != ''">
            <![CDATA[ AND D.BANK_ACCOUNT = #{BANK_ACCOUNT} ]]>
        </if>
        <if test="OPERATOR_BY != null and OPERATOR_BY != ''">
            <![CDATA[ AND U.USER_NAME = #{OPERATOR_BY} ]]>
        </if>
        <if test="redBookkeepingFlag != null and redBookkeepingFlag != ''">
            <![CDATA[ AND (D.RED_BOOKKEEPING_FLAG IS NULL OR D.RED_BOOKKEEPING_FLAG != #{redBookkeepingFlag}) ]]>
        </if>
        <if test="CODE != null and CODE != ''">
            <![CDATA[ AND BD.UNIT_NUMBER  IN(SELECT F.UNIT_NUMBER 
            FROM APP___CAP__DBUSER.T_FMS_INTERFACE F WHERE F.SERVICER_CODE = #{CODE}) ]]>
        </if>
    </sql>
    <!-- mybatis会把0作为空字符串判断，增加==0增强判断 -->
    <sql id="capCashAmountCondition">
        <if test="minFeeAmount != null and minFeeAmount !='' or minFeeAmount == 0">
            <![CDATA[ AND P.FEE_AMOUNT >= #{minFeeAmount} ]]>
        </if>
        <if test="maxFeeAmount != null and maxFeeAmount !='' or maxFeeAmount == 0">
            <![CDATA[ AND P.FEE_AMOUNT <= #{maxFeeAmount} ]]>
        </if>
    </sql>
    <sql id="capCashWherenetworkSales">
        <if test="network_sales != null and network_sales !='' and network_sales == '71'">
            <![CDATA[ AND EXISTS (select 1 from APP___PAS__DBUSER.t_contract_master a where a.submit_channel = '8' and a.policy_code = P.policy_code) ]]>
        </if>
        <if test="network_sales != null and network_sales !='' and network_sales == '13'">
            <![CDATA[ AND EXISTS (select 1 from APP___PAS__DBUSER.t_contract_master a where a.submit_channel = '11' and a.policy_code = P.policy_code) ]]>
        </if>
    </sql>
    <sql id="capCashForCustomerWhereCondition">
    	<if test="PAY_MODE != null and PAY_MODE != ''">
            <![CDATA[ AND S.PAY_MODE = #{PAY_MODE} ]]>
        </if>
    	<if test="transactionType != null and transactionType != ''">
     		<![CDATA[ AND S.TRANSACTION_TYPE = #{transactionType} ]]>
     	</if>
     	<if test="tempCashStatus != null and tempCashStatus != ''">
     		<![CDATA[ AND S.TEMP_CASH_STATUS = #{tempCashStatus} ]]>
     	</if>
    </sql>
    <sql id="capCashHavingForBillAndCustomer">
    	<if test=" ARAP_FLAG != null and ARAP_FLAG != '' ">
     		<![CDATA[
	        	AND CASE WHEN SUM(CASE WHEN D.ARAP_FLAG = '1' THEN D.FEE_AMOUNT ELSE -1 * D.FEE_AMOUNT END) >=0  THEN '${ar}' ELSE '${ap}' END = #{ARAP_FLAG}
	        ]]>
     	</if>
    	<if test=" CHEQUE_STATUS != null and CHEQUE_STATUS != '' ">
     		<![CDATA[
	        	AND (SELECT COUNT(1) CH_COUNT FROM APP___CAP__DBUSER.T_CHEQUE ,APP___CAP__DBUSER.T_BILL_DETAILS BDD 
                WHERE CHEQUE_ID = BDD.BILL_MAIN_ID AND BDD.UNIT_NUMBER= D.UNIT_NUMBER
                AND CHEQUE_STATUS=#{CHEQUE_STATUS})>0
	        ]]>
     	</if>
    </sql>
    
    <select id="findAllManipulationWechat" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT   
         		 MAX( T.Business_Code) as certify_no,
         		 MAX( T.UNIT_NUMBER) as unit_number,
             	 SUM(-T.FEE_AMOUNT) as premium,
             	 MAX( T.Organ_Code)  as organname,
                 MAX(to_char(T.FINISH_TIME, 'YYYY-MM-dd')) AS remarks,
                 MAX(NVL(T.ITSM_NO,'无')) as ITSM_NO,
                 CASE WHEN T.PAY_MODE='71' THEN
                 MAX((SELECT TFI.TRADE_SN  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND ROWNUM=1)) 
                 WHEN  T.PAY_MODE='72' THEN 
                    MAX((SELECT TFI.TRADE_SN  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND TFI.STATUS='1'AND  ROWNUM=1)) END
                  AS TRADE_SN,
                  CASE WHEN T.PAY_MODE='71' THEN
                 MAX((SELECT TFI.BATCH_ID  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND ROWNUM=1)) 
                 WHEN  T.PAY_MODE='72' and t.arap_flag='2' THEN 
                    MAX((SELECT TFI.BATCH_ID  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND TFI.STATUS='2'AND  ROWNUM=1)) 
                 WHEN  T.PAY_MODE='72' and t.arap_flag='1' THEN 
                    MAX((SELECT TFI.BATCH_ID  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND TFI.STATUS='1'AND  ROWNUM=1)) END
                  AS BATCH_ID
  			    FROM APP___CAP__DBUSER.T_CASH_DETAIL T
 				WHERE (T.RED_BOOKKEEPING_FLAG <> 1 OR T.RED_BOOKKEEPING_FLAG IS NULL) ]]>
   					<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND T.ARAP_FLAG = #{arap_flag} ]]></if>
   					<if test=" pay_type != null "><![CDATA[ AND T.Pay_Mode = #{pay_type} ]]></if>
   					<if test=" startDate != null and startDate != ''"> <![CDATA[ AND T.FINISH_TIME >= to_date(#{startDate},'YYYY/MM/DD') ]]></if>
   					<if test=" endDate != null and endDate != ''"><![CDATA[ AND T.FINISH_TIME < to_date(#{endDate},'YYYY/MM/DD') + 1]]></if>
   			<![CDATA[ GROUP BY T.Business_Code,T.Pay_Mode,t.arap_flag ]]>
   			<![CDATA[
   			UNION ALL 
   			SELECT   
         		 MAX( T.Business_Code) as certify_no,
         		 MAX( T.UNIT_NUMBER) as unit_number,
             	 SUM( T.FEE_AMOUNT) as premium,
             	 MAX( T.Organ_Code)  as organname,
                 MAX(to_char(T.FINISH_TIME, 'YYYY-MM-dd')) AS remarks,
                 MAX(NVL(T.ITSM_NO,'无')) as ITSM_NO,
                 CASE WHEN T.PAY_MODE='71' THEN
                 MAX((SELECT TFI.TRADE_SN  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND ROWNUM=1)) 
                 WHEN  T.PAY_MODE='72' THEN 
                    MAX((SELECT TFI.TRADE_SN  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND TFI.STATUS='1'AND  ROWNUM=1)) END
                  AS TRADE_SN,
                  CASE WHEN T.PAY_MODE='71' THEN
                 MAX((SELECT TFI.BATCH_ID  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND ROWNUM=1)) 
                 WHEN  T.PAY_MODE='72' and t.arap_flag='2' THEN 
                    MAX((SELECT TFI.BATCH_ID  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND TFI.STATUS='2'AND  ROWNUM=1)) 
                 WHEN  T.PAY_MODE='72' and t.arap_flag='1' THEN 
                    MAX((SELECT TFI.BATCH_ID  FROM DEV_CAP.T_FMS_INTERFACE TFI WHERE TFI.UNIT_NUMBER=T.UNIT_NUMBER AND TFI.STATUS='1'AND  ROWNUM=1)) END
                  AS BATCH_ID
  			    FROM APP___CAP__DBUSER.T_CASH_DETAIL T
 				WHERE (T.RED_BOOKKEEPING_FLAG <> 1 OR T.RED_BOOKKEEPING_FLAG IS NULL)
 				  AND T.ARAP_FLAG = '1'
 				  AND EXISTS (SELECT 1 FROM DEV_CAP.T_CASH_DETAIL TT WHERE TT.UNIT_NUMBER = T.ROLLBACK_UNIT_NUMBER]]>
 				  <if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND TT.ARAP_FLAG = #{arap_flag} ]]></if>	
 				  <![CDATA[  )  ]]>
   					<if test=" pay_type != null "><![CDATA[ AND T.Pay_Mode = #{pay_type} ]]></if>
   					<if test=" startDate != null and startDate != ''"> <![CDATA[ AND T.FINISH_TIME >= to_date(#{startDate},'YYYY/MM/DD') ]]></if>
   					<if test=" endDate != null and endDate != ''"><![CDATA[ AND T.FINISH_TIME < to_date(#{endDate},'YYYY/MM/DD') + 1]]></if>
   			<![CDATA[ GROUP BY T.Business_Code,T.Pay_Mode,t.arap_flag ]]>
   			
    </select>
    
    <select id="findAllCashDetailMedicare" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[SELECT   
    			 MAX(T.POLICY_CODE) POLICY_CODE,
         		 DECODE(T.DERIV_TYPE,'004',MAX(T.BUSINESS_CODE),'') BUSINESS_CODE,
             	 SUM(DECODE(T.ARAP_FLAG,'1',T.FEE_AMOUNT,-T.FEE_AMOUNT)) FEE_AMOUNT,
             	 MAX(T.ORGAN_CODE) ORGAN_CODE,
                 TRUNC(MAX(FINISH_TIME)) FINISH_TIME,
                 MAX(NVL(T.ITSM_NO,'无')) ITSM_NO,
                 (SELECT SC.CHANNEL_NAME FROM DEV_PAS.T_SUBMIT_CHANNEL SC WHERE SC.SUBMIT_CHANNEL =  CM.SUBMIT_CHANNEL) SUBMIT_CHANNEL,
                 MAX((SELECT TPA.POS_BELNR  FROM DEV_CAP.T_PREM_ARAP TPA WHERE TPA.UNIT_NUMBER=T.UNIT_NUMBER AND ROWNUM=1)) BUSI_SERIAL_NUM
  			    FROM DEV_CAP.T_CASH_DETAIL T
  			    LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM ON T.POLICY_CODE = CM.POLICY_CODE
 				WHERE T.BOOKKEEPING_FLAG = 1 AND T.CAP_ORGAN_CODE LIKE '8674%']]>
   					<if test=" finish_time != null and finish_time != '' ">
        <![CDATA[  AND T.FINISH_TIME >= TRUNC(#{finish_time}) AND T.FINISH_TIME < TRUNC(#{finish_time}) + 1 ]]> </if> 
        <if test="pay_mode != null and pay_mode != '' "><![CDATA[ AND T.PAY_MODE = #{pay_mode} ]]></if>
        <![CDATA[ GROUP BY T.UNIT_NUMBER,T.DERIV_TYPE ,CM.SUBMIT_CHANNEL ]]>
    </select>
    
    <select id="queryOrganBankCount" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ SELECT COUNT(1) FROM (SELECT MAX(T.APPLY_CODE) APPLY_CODE,
               T.BANK_CODE,
               MAX(T.HOLDER_NAME) HOLDER_NAME,
               MAX(T.CHANNEL_TYPE) CHANNEL_TYPE,
               MAX(T.ORGAN_CODE) ORGAN_CODE,
               MAX(T.BANK_ACCOUNT) BANK_ACCOUNT,
               (SELECT MAX(C.MOBILE_TEL)
                  FROM APP___PAS__DBUSER.T_CUSTOMER C
                 WHERE C.CUSTOMER_ID = T.HOLDER_ID) holder_mobile
          FROM APP___CAP__DBUSER.T_PREM_ARAP T
         WHERE 1=1 ]]>
    	<include refid="queryOrganBankWhere"/>
    	<![CDATA[ GROUP BY T.UNIT_NUMBER, T.BANK_CODE, T.HOLDER_ID
         ORDER BY MAX(T.APPLY_CODE))]]>
    </select>
    
    <select id="queryOrganBankPage" parameterType="java.util.Map" resultType="java.util.Map">
    	<![CDATA[ SELECT ROWNUM LIST_ID, A.*
  FROM (SELECT MAX(T.APPLY_CODE) APPLY_CODE,
               T.BANK_CODE,
               MAX(T.HOLDER_NAME) HOLDER_NAME,
               MAX(T.CHANNEL_TYPE) CHANNEL_TYPE,
               MAX(T.ORGAN_CODE) ORGAN_CODE,
               MAX(T.BANK_ACCOUNT) BANK_ACCOUNT,
               (SELECT MAX(C.MOBILE_TEL)
                  FROM APP___PAS__DBUSER.T_CUSTOMER C
                 WHERE C.CUSTOMER_ID = T.HOLDER_ID) holder_mobile
          FROM APP___CAP__DBUSER.T_PREM_ARAP T
         WHERE 1=1 ]]>
    	<include refid="queryOrganBankWhere"/>
    <![CDATA[ GROUP BY T.UNIT_NUMBER, T.BANK_CODE, T.HOLDER_ID
         ORDER BY MAX(T.APPLY_CODE) ) A 
         WHERE ROWNUM > #{GREATER_NUM} AND ROWNUM <= #{LESS_NUM} ]]>
    </select>
    
    <select id="queryOrganBankDetail" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[	SELECT ROWNUM LIST_ID, A.*
  FROM (SELECT MAX(T.APPLY_CODE) APPLY_CODE,
               T.BANK_CODE,
               MAX(T.HOLDER_NAME) HOLDER_NAME,
               MAX(T.CHANNEL_TYPE) CHANNEL_TYPE,
               MAX(T.ORGAN_CODE) ORGAN_CODE,
               MAX(T.BANK_ACCOUNT) BANK_ACCOUNT,
               (SELECT MAX(C.MOBILE_TEL)
                  FROM APP___PAS__DBUSER.T_CUSTOMER C
                 WHERE C.CUSTOMER_ID = T.HOLDER_ID) holder_mobile
          FROM APP___CAP__DBUSER.T_PREM_ARAP T WHERE 1=1 ]]>
         <include refid="queryOrganBankWhere"/>
        <![CDATA[  GROUP BY T.UNIT_NUMBER, T.BANK_CODE, T.HOLDER_ID
         ORDER BY MAX(T.APPLY_CODE) ) A]]>
    </select>
    
    
    <sql id="queryOrganBankWhere">
    	<![CDATA[AND T.FEE_STATUS IN ('00','03') AND
    	T.FROZEN_STATUS='01' AND T.IS_BANK_TEXT='1' AND T.BANK_TEXT_STATUS IN ('1','8')  ]]>
    	<if test="pay_mode != null and pay_mode != ''">
            <![CDATA[ AND T.PAY_MODE = #{pay_mode} ]]>
        </if>
    	<if test="branch_code != null and branch_code != ''">
     		<![CDATA[ AND T.BRANCH_CODE = #{branch_code} ]]>
     	</if>
     	<if test=" channelTypeList != null and channelTypeList.size()>0">
            <![CDATA[ AND T.CHANNEL_TYPE in   ]]>
            <foreach item="item" index="id" collection="channelTypeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" derivTypeList != null and derivTypeList.size()>0">
            <![CDATA[ AND T.DERIV_TYPE in   ]]>
            <foreach item="item" index="id" collection="derivTypeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if
            test=" bank_trans_start_time != null and bank_trans_start_time != ''  "><![CDATA[ AND T.DUE_TIME >= to_date(#{bank_trans_start_time},'yyyy-MM-dd') ]]></if>
        <if
            test=" bank_trans_end_time != null and bank_trans_end_time != ''  "><![CDATA[ AND T.DUE_TIME <= to_date(#{bank_trans_end_time},'yyyy-MM-dd') ]]></if>
     	<if test="bankCodeList != null and bankCodeList.size()>0 ">
     		<![CDATA[ AND T.BANK_CODE in   ]]>
            <foreach item="item" index="id" collection="bankCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
     	</if>
    </sql>
</mapper>