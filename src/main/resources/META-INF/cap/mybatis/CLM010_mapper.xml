<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CLM010">
	<!-- 死亡给付日结 -->
	<select id="findCLM010" resultType="java.util.Map"
        parameterType="java.util.Map">        
        <![CDATA[ 
	 SELECT TO_CHAR(ROWNUM) STRING1,J.* FROM ( 
	    SELECT 
	       T.BUSI_PROD_NAME STRING2,T.BUSI_PROD_CODE STRING5,
	       (SELECT SALES_CHANNEL_NAME FROM DEV_CAP.T_SALES_CHANNEL  WHERE SALES_CHANNEL_CODE = T.CHANNEL_TYPE) STRING3,
	       SUM( CASE WHEN T.ROLLBACK_UNIT_NUMBER IS NULL
               THEN ( CASE WHEN T.FEE_TYPE IN ('P005010000','P005020100','P005020200','P005050000','P005310000','P005030100','P005030200','P005040000','P005260000','P005270000') 
               AND T.WITHDRAW_TYPE IN ('0050102010','0050103010','0050104010','0050704010','***********','0051003010','0051004010','0051102010','0051103010','0051802010','0051802020','0051802030') THEN T.FEE_AMOUNT 
               WHEN T.FEE_TYPE IN ('P004620000','P004630000') AND T.WITHDRAW_TYPE IN ('0050103010') 
               AND EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1')
               THEN T.FEE_AMOUNT ELSE 0 END)
               ELSE (CASE WHEN T.FEE_TYPE IN ('P005010000','P005020100','P005020200','P005050000','P005310000','P005030100','P005030200','P005040000','P005260000','P005270000') 
               AND T.WITHDRAW_TYPE IN ('0050102010','0050103010','0050104010','0050704010','***********','0051003010','0051004010','0051102010','0051103010','0051802010','0051802020','0051802030') THEN T.FEE_AMOUNT * -1 
               WHEN T.FEE_TYPE IN ('P004620000','P004630000') AND T.WITHDRAW_TYPE IN ('0050103010') 
               AND EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1')
               THEN T.FEE_AMOUNT ELSE 0 END) END) NUMBER1,
         SUM(CASE WHEN T.FEE_TYPE ='P005030200' AND T.WITHDRAW_TYPE IN ('0050102010','0050103010','0050104010','0051003010','0051004010','0051802010','0051802020','0051802030') THEN T.FEE_AMOUNT ELSE 0 END) NUMBER2,
         SUM(CASE WHEN T.FEE_TYPE ='P004620000' AND T.WITHDRAW_TYPE IN ('0050102010','0050102011','0050102012','0050103010') 
         AND NOT EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1')
         THEN T.FEE_AMOUNT ELSE 0 END) NUMBER3,
         SUM(CASE WHEN T.FEE_TYPE ='P004630000' AND T.WITHDRAW_TYPE IN ('0050102010','0050102011','0050102012','0050103010') 
         AND NOT EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1')
         THEN T.FEE_AMOUNT ELSE 0 END) NUMBER4,
         COUNT(DISTINCT T.Business_Code) NUMBER5 
         FROM DEV_CAP.T_PREM_ARAP T 
       WHERE 1=1 
       AND T.ARAP_FLAG IN ('1','2') 
       AND T.BOOKKEEPING_FLAG ='1'
       AND T.FEE_TYPE IN ('P005010000','P005020100','P005020200','P005050000','P005310000','P005030100','P005030200','P005040000','P005260000','P005270000','P004620000','P004630000')
       AND T.WITHDRAW_TYPE IN('0050102010','0050102011','0050102012','0050103010','0050104010','0051003010','0051004010','0051102010','0051103010','0051802010','0051802020','0051802030')
       AND T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE 
			              FROM DEV_PAS.T_UDMP_ORG_REL O
			              START WITH O.ORGAN_CODE=#{organ_code}
			              CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
		AND T.STATISTICAL_DATE>=TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
		AND T.STATISTICAL_DATE<TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1	    
		GROUP BY T.BUSI_PROD_NAME, T.BUSI_PROD_CODE,T.CHANNEL_TYPE
		) J
       
        ]]>
	
	</select>
	
	<select id="findCLM010b" resultType="java.util.Map"
        parameterType="java.util.Map">        
        <![CDATA[ 
	SELECT ROWNUM RN,J.* FROM ( 
      SELECT 
         T.Business_Code STRING1,
                T.policy_code STRING2,
                (SELECT CONCAT( CONCAT(UR.ORGAN_CODE,' '),UR.ORGAN_NAME) FROM DEV_PAS.T_UDMP_ORG_REL UR WHERE UR.ORGAN_CODE = T.POLICY_ORGAN_CODE) STRING3, 
                T.BUSI_PROD_NAME   STRING4,
                MAX(C.SALES_CHANNEL_NAME) STRING5,
                TO_CHAR(MAX(ee.audit_time),'YYYY-MM-DD') STRING6,
		            TO_CHAR(MAX(ee.end_case_time),'YYYY-MM-DD') STRING7,
		          (CASE WHEN T.ROLLBACK_UNIT_NUMBER IS NOT NULL THEN '回退' ELSE '' END ) STRING8,    
               SUM( CASE WHEN T.ROLLBACK_UNIT_NUMBER IS NULL
               THEN ( CASE WHEN T.FEE_TYPE IN ('P005010000','P005020100','P005020200','P005050000','P005310000','P005030100','P005030200','P005040000','P005260000','P005270000') 
               AND T.WITHDRAW_TYPE IN ('0050102010','0050103010','0050104010','0050704010','***********','0051003010','0051004010','0051102010','0051103010','0051802010','0051802020','0051802030') THEN T.FEE_AMOUNT 
               WHEN T.FEE_TYPE IN ('P004620000','P004630000') AND T.WITHDRAW_TYPE IN ('0050103010') 
               AND EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1')
               THEN T.FEE_AMOUNT ELSE 0 END) 
               ELSE (CASE WHEN T.FEE_TYPE IN ('P005010000','P005020100','P005020200','P005050000','P005310000','P005030100','P005030200','P005040000','P005260000','P005270000') 
               AND T.WITHDRAW_TYPE IN ('0050102010','0050103010','0050104010','0050704010','***********','0051003010','0051004010','0051102010','0051103010','0051802010','0051802020','0051802030') THEN T.FEE_AMOUNT * -1 
               WHEN T.FEE_TYPE IN ('P004620000','P004630000') AND T.WITHDRAW_TYPE IN ('0050103010') 
               AND EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1')
               THEN T.FEE_AMOUNT ELSE 0 END) END) NUMBER1,
               NVL(SUM(CASE WHEN T.FEE_TYPE ='P005030200' AND T.WITHDRAW_TYPE IN ('0050102010','0050103010','0050104010','0051003010','0051004010','0051802010','0051802020','0051802030')
               THEN  DECODE(T.ROLLBACK_UNIT_NUMBER,NULL,T.FEE_AMOUNT,-T.FEE_AMOUNT)   
               ELSE 0 END),0)NUMBER2,
               NVL(SUM(CASE WHEN T.FEE_TYPE ='P004620000' AND T.WITHDRAW_TYPE IN ('0050102010','0050102011','0050102012','0050103010') 
               AND NOT EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1')
               THEN  DECODE(T.ROLLBACK_UNIT_NUMBER,NULL,T.FEE_AMOUNT,-T.FEE_AMOUNT)   
               ELSE 0 END),0)NUMBER3,
               NVL(SUM(CASE WHEN T.FEE_TYPE ='P004630000' AND T.WITHDRAW_TYPE IN ('0050102010','0050102011','0050102012','0050103010') 
               AND NOT EXISTS(SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT B WHERE B.PRODUCT_CODE_SYS = T.BUSI_PROD_CODE AND B.TAX_EXTENSION_FLAG='1')
               THEN  DECODE(T.ROLLBACK_UNIT_NUMBER,NULL,T.FEE_AMOUNT,-T.FEE_AMOUNT)   
               ELSE 0 END),0)NUMBER4
         FROM DEV_CAP.T_PREM_ARAP T 
         left join DEV_CLM.T_CLAIM_CASE ee 
                on t.business_code = ee.case_no
                LEFT JOIN DEV_CAP.T_SALES_CHANNEL C ON C.SALES_CHANNEL_CODE = T.CHANNEL_TYPE
       WHERE 1=1 
       AND T.BOOKKEEPING_FLAG ='1'
       AND T.FEE_TYPE IN ('P005010000','P005020100','P005020200','P005050000','P005310000','P005030100','P005030200','P005040000','P005260000','P005270000','P004620000','P004630000')
       AND T.WITHDRAW_TYPE IN('0050102010','0050102011','0050102012','0050103010','0050104010','0051003010','0051004010','0051102010','0051103010','0051802010','0051802020','0051802030')
       AND T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE 
                    FROM DEV_PAS.T_UDMP_ORG_REL O
                    START WITH O.ORGAN_CODE=#{organ_code}
                    CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
    AND T.STATISTICAL_DATE>=TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
		AND T.STATISTICAL_DATE<TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1 
    GROUP BY T.Business_Code,
                T.policy_code,
                T.policy_organ_code,
                T.BUSI_PROD_NAME, T.CHANNEL_TYPE,T.ROLLBACK_UNIT_NUMBER,T.DERIV_TYPE) J
                ORDER BY j.STRING4,j.STRING6,j.STRING1,j.STRING2
        ]]>
	
	</select>

</mapper>