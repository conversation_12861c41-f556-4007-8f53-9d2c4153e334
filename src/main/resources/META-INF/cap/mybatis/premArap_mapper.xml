<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="prem_arap">
   
    
    <sql id="premArapWhereCondition">
        <if test=" task_mark != null "><![CDATA[ AND A.TASK_MARK = #{task_mark} ]]></if>
        <if test=" related_id != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
        <if test=" is_item_main != null and is_item_main != '' "><![CDATA[ AND A.IS_ITEM_MAIN = #{is_item_main} ]]></if>
        <if test=" payee_phone != null and payee_phone != '' "><![CDATA[ AND A.PAYEE_PHONE = #{payee_phone} ]]></if>
        <if test=" paid_count != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
        <if test=" seq_no != null "><![CDATA[ AND A.SEQ_NO = #{seq_no} ]]></if>
        <if test=" prem_purpose != null and prem_purpose != '' "><![CDATA[ AND A.PREM_PURPOSE = #{prem_purpose} ]]></if>
        <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code}]]></if>
        <if test=" organ_code != null "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
        <if test=" fee_status_date != null and fee_status_date != '' "><![CDATA[ AND A.FEE_STATUS_DATE = #{fee_status_date} ]]></if>
        <if test=" red_bookkeeping_time != null and red_bookkeeping_time != '' "><![CDATA[ AND A.RED_BOOKKEEPING_TIME = #{red_bookkeeping_time} ]]></if>
        <if test=" is_bank_text_date != null and is_bank_text_date != '' "><![CDATA[ AND A.IS_BANK_TEXT_DATE = #{is_bank_text_date} ]]></if>
        <if test=" charge_year != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
        <if test=" is_risk_main != null and is_risk_main != '' "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
        <if test=" proposal_id != null "><![CDATA[ AND A.PROPOSAL_ID = #{proposal_id} ]]></if>
        <if test=" is_bank_account != null and is_bank_account != '' "><![CDATA[ AND A.IS_BANK_ACCOUNT = #{is_bank_account} ]]></if>
        <if test=" frozen_status != null and frozen_status != '' "><![CDATA[ AND A.FROZEN_STATUS = #{frozen_status} ]]></if>
        <if test=" posted != null and posted != '' "><![CDATA[ AND A.POSTED = #{posted} ]]></if>
        <if test=" pay_collection_indi != null and pay_collection_indi != '' "><![CDATA[ AND A.PAY_COLLECTION_INDI = #{pay_collection_indi} ]]></if>
        <if test=" group_id != null "><![CDATA[ AND A.GROUP_ID = #{group_id} ]]></if>
        <if test=" insured_name != null and insured_name != '' "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
        <if test=" insured_id != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
        <if test=" check_no != null and check_no != '' "><![CDATA[ AND A.CHECK_NO = #{check_no} ]]></if>
        <if test=" case_id != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
        <if test=" product_channel != null and product_channel != '' "><![CDATA[ AND A.PRODUCT_CHANNEL = #{product_channel} ]]></if>
        <if test=" pay_mode != null and pay_mode != '' "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
        <if test=" branch_code != null and branch_code != '' "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
        <if test=" business_type != null "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
        <if test=" withdraw_type != null "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
        <if test=" validate_date != null and validate_date != '' "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
        <if test=" red_belnr != null and red_belnr != '' "><![CDATA[ AND A.RED_BELNR = #{red_belnr} ]]></if>
        <if test=" is_bank_text_by != null "><![CDATA[ AND A.IS_BANK_TEXT_BY = #{is_bank_text_by} ]]></if>
        <if test=" reserved_field3 != null and reserved_field3 != '' "><![CDATA[ AND A.RESERVED_FIELD3 = #{reserved_field3} ]]></if>
        <if test=" reserved_field4 != null and reserved_field4 != '' "><![CDATA[ AND A.RESERVED_FIELD4 = #{reserved_field4} ]]></if>
        <if test=" payee_name != null and payee_name != '' "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
        <if test=" reserved_field5 != null and reserved_field5 != '' "><![CDATA[ AND A.RESERVED_FIELD5 = #{reserved_field5} ]]></if>
        <if test=" reserved_field6 != null and reserved_field6 != '' "><![CDATA[ AND A.RESERVED_FIELD6 = #{reserved_field6} ]]></if>
        <if test=" reserved_field7 != null and reserved_field7 != '' "><![CDATA[ AND A.RESERVED_FIELD7 = #{reserved_field7} ]]></if>
        <if test=" reserved_field8 != null and reserved_field8 != '' "><![CDATA[ AND A.RESERVED_FIELD8 = #{reserved_field8} ]]></if>
        <if test=" reserved_field9 != null and reserved_field9 != '' "><![CDATA[ AND A.RESERVED_FIELD9 = #{reserved_field9} ]]></if>
        <if test=" check_enter_time != null and check_enter_time != '' "><![CDATA[ AND A.CHECK_ENTER_TIME = #{check_enter_time}]]></if>
        <if test=" item_id != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
        <if test=" finish_time != null and finish_time != '' "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
        <if test=" is_bank_text != null and is_bank_text != '' "><![CDATA[ AND A.IS_BANK_TEXT = #{is_bank_text} ]]></if>
        <if test=" busi_apply_date != null and busi_apply_date != '' "><![CDATA[ AND A.BUSI_APPLY_DATE = #{busi_apply_date} ]]></if>
        <if test=" belnr != null and belnr != '' "><![CDATA[ AND A.BELNR = #{belnr} ]]></if>
        <if test=" policy_chg_id != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
        <if test=" busi_item_id != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
        <if test=" reserved_field2 != null and reserved_field2 != '' "><![CDATA[ AND A.RESERVED_FIELD2 = #{reserved_field2} ]]></if>
        <if test=" reserved_field1 != null and reserved_field1 != '' "><![CDATA[ AND A.RESERVED_FIELD1 = #{reserved_field1} ]]></if>
        <if test=" rollback_unit_number != null and rollback_unit_number != '' "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number} ]]></if>
        <if test=" customer_id != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
        <if test=" fail_times != null "><![CDATA[ AND A.FAIL_TIMES = #{fail_times} ]]></if>
        <if test=" policy_year != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
        <if test=" frozen_status_by != null "><![CDATA[ AND A.FROZEN_STATUS_BY = #{frozen_status_by} ]]></if>
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
        <if test=" sync_flag != null "><![CDATA[ AND A.SYNC_FLAG = #{sync_flag} ]]></if>
        <if test=" charge_period != null and charge_period != '' "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
        <if test=" bank_code != null and bank_code != '' "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
        <if test=" bookkeeping_time != null and bookkeeping_time != '' "><![CDATA[ AND A.BOOKKEEPING_TIME = #{bookkeeping_time} ]]></if>
        <if test=" agent_code != null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
        <if test=" prem_freq != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
        <if test=" policy_organ_code != null and policy_organ_code != '' "><![CDATA[ AND A.POLICY_ORGAN_CODE = #{policy_organ_code} ]]></if>
        <if test=" holder_name != null and holder_name != '' "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
        <if test=" bookkeeping_id != null "><![CDATA[ AND A.BOOKKEEPING_ID = #{bookkeeping_id} ]]></if>
        <if test=" busi_prod_name != null and busi_prod_name != '' "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
        <if test=" unit_number != null "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" case_no != null and case_no != '' "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
        <if test=" fee_type != null and fee_type != '' "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
        <if test=" channel_type != null and channel_type != '' "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
        <if test=" policy_id != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
        <if test=" sales_channel != null and sales_channel != '' "><![CDATA[ AND A.SALES_CHANNEL = #{sales_channel} ]]></if>
        <if test=" bookkeeping_flag != null and bookkeeping_flag != '' "><![CDATA[ AND A.BOOKKEEPING_FLAG = #{bookkeeping_flag} ]]></if>
        <if test=" red_bookkeeping_by != null "><![CDATA[ AND A.RED_BOOKKEEPING_BY = #{red_bookkeeping_by} ]]></if>
        <if test=" frozen_status_date != null and frozen_status_date != '' "><![CDATA[ AND A.FROZEN_STATUS_DATE = #{frozen_status_date} ]]></if>
        <if test=" policy_type != null and policy_type != '' "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
        <if test=" pay_year != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
        <if test=" pay_period != null and pay_period != '' "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
        <if test=" is_bank_account_date != null and is_bank_account_date != '' "><![CDATA[ AND A.IS_BANK_ACCOUNT_DATE = #{is_bank_account_date} ]]></if>
        <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" cs_accept_code != null and cs_accept_code != '' "><![CDATA[ AND A.CS_ACCEPT_CODE = #{cs_accept_code} ]]></if>
        <if test=" bank_text_status != null and bank_text_status != '' "><![CDATA[ AND A.BANK_TEXT_STATUS = #{bank_text_status} ]]></if>
        <if test=" arap_fee_id != null "><![CDATA[ AND A.ARAP_FEE_ID = #{arap_fee_id} ]]></if>
        <if test=" certi_type != null and certi_type != '' "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
        <if test=" money_code != null and money_code != '' "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
        <if test=" is_bank_account_by != null "><![CDATA[ AND A.IS_BANK_ACCOUNT_BY = #{is_bank_account_by} ]]></if>
        <if test=" business_code != null and business_code != '' "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
        <if test=" owner_id != null "><![CDATA[ AND A.OWNER_ID = #{owner_id} ]]></if>
        <if test=" bank_account != null and bank_account != '' "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
        <if test=" red_bookkeeping_flag != null and red_bookkeeping_flag != '' "><![CDATA[ AND A.RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag} ]]></if>
        <if test=" product_code != null and product_code != '' "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
        <if test=" coverage_period != null and coverage_period != '' "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
        <if test=" reserved_field10 != null and reserved_field10 != '' "><![CDATA[ AND A.RESERVED_FIELD10 = #{reserved_field10} ]]></if>
        <if test=" due_time != null and due_time != '' "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
        <if test=" certi_code != null and certi_code != '' "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
        <if test=" list_id != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
        <if test=" fee_amount != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
        <if test=" holder_id != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
        <if test=" withdraw_type != null and withdraw_type != '' "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
        <if test=" pay_freq != null "><![CDATA[ AND A.PAY_FREQ = #{pay_freq} ]]></if>
        <if test=" coverage_year != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
        <if test=" bank_user_name != null and bank_user_name != '' "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
        <if test=" pay_end_date != null and pay_end_date != '' "><![CDATA[ AND A.PAY_END_DATE = #{pay_end_date} ]]></if>
        <if test=" fee_status_by != null "><![CDATA[ AND A.FEE_STATUS_BY = #{fee_status_by} ]]></if>
        <if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
        <if test=" red_bookkeeping_id != null "><![CDATA[ AND A.RED_BOOKKEEPING_ID = #{red_bookkeeping_id} ]]></if>
        <if test=" bookkeeping_by != null "><![CDATA[ AND A.BOOKKEEPING_BY = #{bookkeeping_by} ]]></if>
        <if test=" arap_flag != null and arap_flag != '' "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
        <if test=" audit_date != null and audit_date != '' "><![CDATA[ AND A.AUDIT_DATE = #{audit_date} ]]></if>
        <if test=" statistical_date != null and statistical_date != '' "><![CDATA[ AND A.STATISTICAL_DATE = #{statistical_date} ]]></if>
        <if test=" temp_unit_numbers != null and temp_unit_numbers  != '' ">
        <![CDATA[
            AND A.FEE_STATUS = '16'
            AND A.APPLY_CODE IN (SELECT TT.APPLY_CODE
                          FROM APP___CAP__DBUSER.T_PREM_ARAP TT
                         WHERE TT.UNIT_NUMBER IN ]]>
           <foreach item="item" index="id" collection="temp_unit_numbers"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
           </foreach>
           <![CDATA[ )   ]]>
        </if>
    </sql>
    <sql id="findRecordsCondition">
        <if test=" branchCodeList != null and branchCodeList.size()>0">
            <![CDATA[ AND A.BRANCH_CODE in   ]]>
            <foreach item="item" index="id" collection="branchCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" applyCodeList != null and applyCodeList.size()>0">
            <![CDATA[ AND (A.APPLY_CODE in   ]]>
            <foreach item="item" index="id" collection="applyCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
            <![CDATA[ OR A.POLICY_CODE in   ]]>
            <foreach item="item" index="id" collection="applyCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
            <![CDATA[ ) ]]>
        </if>
        <if test=" businessCodeList != null and businessCodeList.size()>0">
            <![CDATA[ AND A.BUSINESS_CODE in   ]]>
            <foreach item="item" index="id" collection="businessCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" derivTypeList != null and derivTypeList.size()>0">
            <![CDATA[ AND A.DERIV_TYPE in   ]]>
            <foreach item="item" index="id" collection="derivTypeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" channelTypeList != null and channelTypeList.size()>0">
            <![CDATA[ AND A.CHANNEL_TYPE in   ]]>
            <foreach item="item" index="id" collection="channelTypeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" arapFlagList != null and arapFlagList.size()>0">
            <![CDATA[ AND A.ARAP_FLAG in   ]]>
            <foreach item="item" index="id" collection="arapFlagList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if
            test=" bankTextStatusList != null and bankTextStatusList.size()>0">
            <![CDATA[ AND RTRIM(A.BANK_TEXT_STATUS) in   ]]>
            <foreach item="item" index="id" collection="bankTextStatusList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
        <if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
        <if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND rtrim(A.CHANNEL_TYPE) = #{channel_type} ]]></if>
        <if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
        <if
            test=" bank_trans_start_time != null and bank_trans_start_time != ''  "><![CDATA[ AND A.DUE_TIME >= to_date(#{bank_trans_start_time},'yyyy-MM-dd') ]]></if>
        <if
            test=" bank_trans_end_time != null and bank_trans_end_time != ''  "><![CDATA[ AND A.DUE_TIME <= to_date(#{bank_trans_end_time},'yyyy-MM-dd') ]]></if>
        <if test=" busi_apply_date != null and busi_apply_date != ''  ">
            <!-- <![CDATA[ AND (A.BUSI_APPLY_DATE <= trunc(#{busi_apply_date},'dd') 
                OR A.DUE_TIME <= trunc(#{busi_apply_date},'dd')) AND (trunc(#{busi_apply_date},'dd') 
                <= A.DUE_TIME OR trunc(#{busi_apply_date},'dd') <= A.PAY_END_DATE ) ]]> -->
 			<![CDATA[ 
 				AND  A.DUE_TIME <= trunc(#{busi_apply_date},'dd') 
 			]]>
        </if>
        <!-- <if test=" is_bank_account != null and is_bank_account != '' 
            "><![CDATA[ AND RTRIM(A.is_bank_account) = #{is_bank_account} ]]></if> -->
        <if test=" bank_text_status != null and bank_text_status != ''  "><![CDATA[ AND  RTRIM(A.BANK_TEXT_STATUS) = #{bank_text_status} ]]></if>
        <if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
        <if
            test=" is_bank_text != null and is_bank_text != '' and  pay_end_date != null and pay_end_date != '' ">
        	<![CDATA[ AND (A.IS_BANK_TEXT = #{is_bank_text} OR A.PAY_END_DATE = trunc(#{pay_end_date},'dd')) ]]></if>
        <if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>

        <if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND (A.APPLY_CODE = #{business_code} OR A.POLICY_CODE=#{business_code} ) ]]></if>
        <if test=" task_mark != null "><![CDATA[ AND A.TASK_MARK = #{task_mark} ]]></if>
        <if test=" organ_code != null "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
    </sql>

    <sql id="findRecordsGetOrganDate">
        <if test=" branchCodeList != null and branchCodeList.size()>0">
            <![CDATA[ AND A.BRANCH_CODE in   ]]>
            <foreach item="item" index="id" collection="branchCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" applyCodeList != null and applyCodeList.size()>0">
            <![CDATA[ AND (A.APPLY_CODE in   ]]>
            <foreach item="item" index="id" collection="applyCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
            <![CDATA[ OR A.POLICY_CODE in   ]]>
            <foreach item="item" index="id" collection="applyCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
            <![CDATA[ ) ]]>
        </if>
        <if test=" businessCodeList != null and businessCodeList.size()>0">
            <![CDATA[ AND A.BUSINESS_CODE in   ]]>
            <foreach item="item" index="id" collection="businessCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" derivTypeList != null and derivTypeList.size()>0">
            <![CDATA[ AND A.DERIV_TYPE in   ]]>
            <foreach item="item" index="id" collection="derivTypeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" channelTypeList != null and channelTypeList.size()>0">
            <![CDATA[ AND A.CHANNEL_TYPE in   ]]>
            <foreach item="item" index="id" collection="channelTypeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" arapFlagList != null and arapFlagList.size()>0">
            <![CDATA[ AND A.ARAP_FLAG in   ]]>
            <foreach item="item" index="id" collection="arapFlagList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" bankTextStatusList != null and bankTextStatusList.size()>0">
            <![CDATA[ AND A.BANK_TEXT_STATUS in   ]]>
            <foreach item="item" index="id" collection="bankTextStatusList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
        <if test=" bankCodeList != null and bankCodeList != '' ">
        	<![CDATA[ AND A.BANK_CODE IN ]]>
        	<foreach collection="bankCodeList" item="item" index="id" open="(" separator="," close=")">
        		#{item, jdbcType=VARCHAR}
        	</foreach>
        </if>
        <if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
        <if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
        <if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
        <if test=" bank_trans_start_time != null and bank_trans_start_time != ''  "><![CDATA[ AND A.DUE_TIME >= to_date(#{bank_trans_start_time},'yyyy-MM-dd') ]]></if>
        <if test=" bank_trans_end_time != null and bank_trans_end_time != ''  "><![CDATA[ AND A.DUE_TIME <= to_date(#{bank_trans_end_time},'yyyy-MM-dd') ]]></if>
        <if test=" busi_apply_date != null and busi_apply_date != ''  ">
            <!-- <![CDATA[ AND (A.BUSI_APPLY_DATE <= trunc(#{busi_apply_date},'dd') 
                OR A.DUE_TIME <= trunc(#{busi_apply_date},'dd')) AND (trunc(#{busi_apply_date},'dd') 
                <= A.DUE_TIME OR trunc(#{busi_apply_date},'dd') <= A.PAY_END_DATE ) ]]> -->
 			<![CDATA[ 
 				AND   A.DUE_TIME <= trunc(#{busi_apply_date},'dd') 
 				
 			]]>
        </if>
        <!-- <if test=" is_bank_account != null and is_bank_account != '' 
            "><![CDATA[ AND RTRIM(A.is_bank_account) = #{is_bank_account} ]]></if> -->
        <if test=" bank_text_status != null and bank_text_status != ''  "><![CDATA[ AND  A.BANK_TEXT_STATUS = #{bank_text_status} ]]></if>
        <if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
        <if test=" is_bank_text != null and is_bank_text != '' ">
        	<![CDATA[ AND A.IS_BANK_TEXT = #{is_bank_text}  ]]></if>
        	<!-- 修改   改成 和的关系   并判断是否是业务来源deriv_type为续期003，是续期才有宽限止期    取消宽限期限制-->
        <!-- <if test=" pay_end_date != null and pay_end_date != '' ">
        	<![CDATA[ AND( (A.DERIV_TYPE = #{deriv_type_pa} AND A.PAY_END_DATE >= trunc(#{pay_end_date},'dd')  AND A.DUE_TIME <= trunc(#{busi_apply_date},'dd') ) OR (A.DERIV_TYPE = #{deriv_type_pa} AND A.PAY_END_DATE IS NULL) OR A.DERIV_TYPE <> #{deriv_type_pa} ) ]]>
        </if> -->
        	<!-- 修改 -->
        <!-- 制盘次数控制 保全15次失败 -->
        <if test="fail_times_cs !=null and fail_times_cs !='' ">
        	<![CDATA[
        		AND ( ( NVL(A.FAIL_TIMES,0) < #{fail_times_cs} AND A.DERIV_TYPE = #{deriv_type_cs} ) OR A.DERIV_TYPE <> #{deriv_type_cs})
        	]]></if>
        	<!--新契约首轮2次失败，次轮6次失败  ,上海医保失败8次,续保转投截止到宽限期止期-->
        <if test="fail_times_nb_fir !=null and fail_times_nb_fir !='' ">
        	<![CDATA[
        		AND ( ( A.DERIV_TYPE = #{deriv_type_nb} AND NVL(A.IS_RECOVER_DOCUMENT,0) = #{recover_document_no} AND NVL(A.FAIL_TIMES,0) < #{fail_times_nb_fir} )
        		OR( A.DERIV_TYPE = #{deriv_type_nb} AND NVL(A.IS_RECOVER_DOCUMENT,0) = #{recover_document_yes} AND NVL(A.FAIL_TIMES,0) < #{fail_times_nb_sec}  ) 
        		]]>
        		<if test="recover_document_two !=null and recover_document_two !='' and fail_times_nb_yddzqd !=null and fail_times_nb_yddzqd !=''">
	        		<![CDATA[
	        		OR( A.DERIV_TYPE = #{deriv_type_nb} AND NVL(A.IS_RECOVER_DOCUMENT,0) = #{recover_document_two} AND NVL(A.FAIL_TIMES,0) < #{fail_times_nb_yddzqd}  ) 
        			]]>
        		</if>
        		<![CDATA[OR A.DERIV_TYPE <> #{deriv_type_nb}]]> 
        		<if test="business_type_xbzt !=null and  business_type_xbzt != '' ">
        		OR( A.DERIV_TYPE = #{deriv_type_nb} AND A.BUSINESS_TYPE =#{business_type_xbzt} AND A.PAY_END_DATE >= TRUNC(#{busi_apply_date}) )
	            </if>
	            <if test="shybList != null and shybList.size()>0  ">
	            OR (A.DERIV_TYPE = #{deriv_type_nb} AND A.BUSI_PROD_CODE in 
	        <foreach item="item" index="id" collection="shybList"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
	       <![CDATA[ AND NVL(A.FAIL_TIMES,0) < #{fail_times_shyb}  )
	        ]]> </if>
	        )
        	</if>
        <if test="feeStatusList != null and feeStatusList.size()>0  ">
	        <![CDATA[ AND A.FEE_STATUS in ]]>
	        <foreach item="item" index="id" collection="feeStatusList"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>

        <if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND (A.APPLY_CODE = #{business_code} OR A.POLICY_CODE=#{business_code} ) ]]></if>
        <if test=" task_mark != null "><![CDATA[ AND A.TASK_MARK = #{task_mark} ]]></if>
        <if test=" organ_code != null "><![CDATA[ AND (A.ORGAN_CODE in (select org_rel.organ_code from APP___CAP__DBUSER.T_UDMP_ORG_REL org_rel 
            start with org_rel.uporgan_code = #{organ_code}
             connect by prior org_rel.organ_code = org_rel.uporgan_code ) 
             or ORGAN_CODE = #{organ_code})  ]]>
        </if>
        <!-- 收费只查询本行数据，付费查询本行以及其他附属分行的数据 -->
        <choose>
        	<when test="apBankCodeList != null">
	        	<![CDATA[ AND A.BANK_CODE IN ]]>
	        	<foreach collection="apBankCodeList" item="item" index="id" open="(" separator="," close=")">
	        		#{item, jdbcType=VARCHAR}
	        	</foreach>
        	</when>
        	<when test="arBankCodeList != null">
	        	<![CDATA[ AND A.BANK_CODE IN ]]>
	        	<foreach collection="arBankCodeList" item="item" index="id" open="(" separator="," close=")">
	        		#{item, jdbcType=VARCHAR}
	        	</foreach>
        	</when>
        </choose>
        
        <!-- xuxj_wb 2017-01-18 挂起状态 -->
        <if test="frozen_status != null and frozen_status != ''"><![CDATA[ AND NVL(A.FROZEN_STATUS, #{frozen_status_solution}) = #{frozen_status} ]]></if>
         <if test="ytbFlag !=null and ytbFlag !='' and ytbFlag == '1'.toString() ">
         <![CDATA[ 
         AND A.IS_YBT_BANK_TAXT =1 
         ]]>
         </if>
         <if test="ytbFlag !=null and ytbFlag !='' and ytbFlag == '0'.toString() "><![CDATA[ AND 0=NVL(A.IS_YBT_BANK_TAXT,0)]]></if>
         <if test="special_account_flag != null and special_account_flag !='' and special_account_flag == '0'.toString() ">
           AND NVL(A.SPECIAL_ACCOUNT_FLAG,'0') = #{special_account_flag}
         </if>
         <if test="special_account_flag != null and special_account_flag !='' and special_account_flag == '1'.toString() ">
           AND A.SPECIAL_ACCOUNT_FLAG = #{special_account_flag}
         </if>
    </sql>

    <!-- 按索引生成的查询条件 -->
    <sql id="queryPremArapByListIdCondition">
        <if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
    </sql>
    <sql id="queryPremArapByArapFeeIdCondition">
        <if test=" arap_fee_id  != null "><![CDATA[ AND A.ARAP_FEE_ID = #{arap_fee_id} ]]></if>
    </sql>
    <sql id="queryPremArapByOrganCodeCondition">
        <if test=" organ_code  != null "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
    </sql>
    <sql id="queryPremArapByApplyCodeCondition">
        <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
    </sql>
    <sql id="queryPremArapByPolicyCodeCondition">
        <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
    </sql>
    <sql id="findPremArapByRollbackUnitNumCondition">
        <if
            test=" rollback_unit_number != null and rollback_unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{rollback_unit_number} ]]></if>
    </sql>
    <sql id="queryPremArapByDueTimeCondition">
        <if test=" due_time  != null "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
    </sql>
   
    <sql id="queryPremArapInfoByPolicyCode">
        <if test=" policy_code  != null "><![CDATA[ AND A.policy_code = #{policy_code} ]]></if>
        <if test="feeStatusList != null and feeStatusList.size() > 0">
        	<![CDATA[ AND A.fee_status in ]]>
            <foreach item="item" index="id" collection="feeStatusList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test=" unit_number != null"><![CDATA[ AND A.Unit_Number = #{unit_number} ]]></if>
        <choose>
        <when test="paid_count != null and paid_count != ''">
        <![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]>
        </when> 
        <when test="policy_code != null and policy_code != '' and fs != null and fs != ''">
        <![CDATA[ AND A.POLICY_YEAR in(select max(paid_count) FROM APP___CAP__DBUSER.T_PREM_ARAP  where fee_status = '${fs}' and policy_code = #{policy_code}) ]]>
        </when>
        <otherwise>        
        </otherwise>
        </choose>       
    </sql>
    <!-- liqd_wb添加查询条件 ，并修改查询条件paid_count缴费次数替换为POLICY_YEAR保单年度 -->
    <sql id="renewalqueryNewPremArapByListPolicyCode">
        <if test=" policy_code  != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" due_time != null and due_time != '' "><![CDATA[ AND A.DUE_TIME < TO_DATE(TO_CHAR(#{due_time},'YYYYMMDD'),'YYYYMMDD')+1 ]]></if>
        <if test=" paid_count != null and paid_count != '' "><![CDATA[ AND A.POLICY_YEAR = #{paid_count} ]]></if>
    </sql>
    <sql id="queryNearPremArapByPolicyCode">
        <if test=" policy_code  != null "><![CDATA[ AND A.policy_code = #{policy_code} ]]></if>
        <if test=" paid_count  != null "><![CDATA[ AND A.paid_count = #{paid_count} ]]></if>
        <if test=" finish_time  != null "><![CDATA[ AND trunc(finish_time) = trunc(#{finish_time}, 'dd')]]></if>
    </sql>
    <sql id="findPremArapByUnitNumCondition">
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
    </sql>
    <!-- 添加操作 -->
    <insert id="addPremArap" useGeneratedKeys="true"
        parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE"
            keyProperty="list_id">
            select APP___CAP__DBUSER.S_PREM_ARAP__LIST_ID.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
            INSERT INTO APP___CAP__DBUSER.T_PREM_ARAP(
				TASK_MARK, IS_ITEM_MAIN, PAYEE_PHONE, BATCH_NO, IS_SEND, PAID_COUNT, CUSTOMER_ACCOUNT_FLAG, 
				FUNDS_RTN_CODE, SEQ_NO, APPLY_CODE, ORGAN_CODE, FEE_STATUS_DATE, RED_BOOKKEEPING_TIME, IS_BANK_TEXT_DATE, 
				UPDATE_BY, STATISTICAL_DATE, CHARGE_YEAR, IS_RISK_MAIN, IS_BANK_ACCOUNT, FROZEN_STATUS, POSTED, 
				GROUP_ID, INSURED_NAME, INSURED_ID, CUS_ACC_DETAILS_ID, PRODUCT_CHANNEL, PAY_MODE, OPERATOR_BY, 
				AUDIT_DATE, BRANCH_CODE, TAX_RATE, VALIDATE_DATE, BUSINESS_TYPE, UPDATE_TIMESTAMP, INSERT_BY, 
				GROUP_CODE, RED_BELNR, IS_BANK_TEXT_BY, PAYEE_NAME, CIP_DISTRICT_BANK_CODE, FINISH_TIME, INSERT_TIMESTAMP, 
				IS_BANK_TEXT, BUSI_APPLY_DATE, BELNR, CIP_BRANCH_BANK_CODE, SERVICE_CODE, "GROUP", AREA, 
				ROLLBACK_UNIT_NUMBER, FAIL_TIMES, POLICY_YEAR, INSERT_TIME, FROZEN_STATUS_BY, FEE_STATUS, CUS_ACC_UPDATE_TIME, 
				BANK_CODE, REFEFLAG, BOOKKEEPING_TIME, AGENT_CODE, PREM_FREQ, POLICY_ORGAN_CODE, HOLDER_NAME, 
				BOOKKEEPING_ID, BUSI_PROD_NAME, UNIT_NUMBER, PART, PRODUCT_ABBR_NAME, FEE_TYPE, BUSI_PROD_CODE, 
				CHANNEL_TYPE, BOOKKEEPING_FLAG, SEND_DATE, RED_BOOKKEEPING_BY, AGENT_NAME, IS_SPLIT, FROZEN_STATUS_DATE, 
				UPDATE_TIME, POLICY_TYPE, IS_BANK_ACCOUNT_DATE, POLICY_CODE, BANK_TEXT_STATUS, CERTI_TYPE, CUS_ACC_UPDATE_BY, 
				IS_BANK_ACCOUNT_BY, BUSINESS_CODE, MONEY_CODE, BANK_ACCOUNT, RED_BOOKKEEPING_FLAG, CUS_ACC_FEE_AMOUNT, CUSTOMER_ID, 
				DUE_TIME, CERTI_CODE, GROUP_NAME, CIP_BANK_CODE, LIST_ID, FEE_AMOUNT, HOLDER_ID, 
				WITHDRAW_TYPE, BANK_USER_NAME, PAY_END_DATE, FEE_STATUS_BY, DERIV_TYPE, RED_BOOKKEEPING_ID, BOOKKEEPING_BY, 
				ARAP_FLAG,CLAIM_NATURE,CLAIM_TYPE,COVERAGE_PERIOD,COVERAGE_YEAR,
				PRODUCT_CODE,PAY_LIAB_CODE,SOURCE_TABLE,SOURCE_TABLE_PK,IS_RECOVER_DOCUMENT,PAYEE_EMAIL,PRE_UNIT_NUMBER,FEE_AMOUNT_BEFOR_TAX,FEE_AMOUNT_TAX
				,IS_YBT_BANK_TAXT,IS_CORPORATE,POS_BELNR,MEDICAL_INSURANCE_CARD,CHARGE_PERIOD,BUSI_ITEM_ID,SPECIAL_ACCOUNT_FLAG,VALIDATE_DATE2,STATISTICAL_DATE2) 
			VALUES (
				#{task_mark, jdbcType=NUMERIC}, #{is_item_main, jdbcType=NUMERIC} , #{payee_phone, jdbcType=VARCHAR} , #{batch_no, jdbcType=VARCHAR} , #{is_send, jdbcType=NUMERIC} , #{paid_count, jdbcType=NUMERIC} , #{customer_account_flag, jdbcType=NUMERIC} 
				, #{funds_rtn_code, jdbcType=VARCHAR} , #{seq_no, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{fee_status_date, jdbcType=DATE} , #{red_bookkeeping_time, jdbcType=DATE} , #{is_bank_text_date, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC} , #{statistical_date, jdbcType=DATE} , #{charge_year, jdbcType=NUMERIC} , #{is_risk_main, jdbcType=NUMERIC} , #{is_bank_account, jdbcType=NUMERIC} , #{frozen_status, jdbcType=VARCHAR} , #{posted, jdbcType=VARCHAR} 
				, #{group_id, jdbcType=NUMERIC} , #{insured_name, jdbcType=VARCHAR} , #{insured_id, jdbcType=NUMERIC} , #{cus_acc_details_id, jdbcType=NUMERIC} , #{product_channel, jdbcType=VARCHAR} , #{pay_mode, jdbcType=VARCHAR} , #{operator_by, jdbcType=NUMERIC} 
				, #{audit_date, jdbcType=DATE} , #{branch_code, jdbcType=VARCHAR} , #{tax_rate, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , #{business_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, NVL(#{cipFlagUser, jdbcType=NUMERIC}, #{insert_by, jdbcType=NUMERIC})
				, #{group_code, jdbcType=VARCHAR} , #{red_belnr, jdbcType=VARCHAR} , #{is_bank_text_by, jdbcType=NUMERIC} , #{payee_name, jdbcType=VARCHAR} , #{cip_district_bank_code, jdbcType=VARCHAR} , #{finish_time, jdbcType=TIMESTAMP} , CURRENT_TIMESTAMP
				, #{is_bank_text, jdbcType=NUMERIC} , #{busi_apply_date, jdbcType=DATE} , #{belnr, jdbcType=VARCHAR} , #{cip_branch_bank_code, jdbcType=VARCHAR} , #{service_code, jdbcType=VARCHAR} , #{group, jdbcType=VARCHAR} , #{area, jdbcType=VARCHAR} 
				, #{rollback_unit_number, jdbcType=VARCHAR} , #{fail_times, jdbcType=NUMERIC} , #{policy_year, jdbcType=NUMERIC} , SYSDATE , #{frozen_status_by, jdbcType=NUMERIC} , #{fee_status, jdbcType=VARCHAR} , #{cus_acc_update_time, jdbcType=DATE} 
				, #{bank_code, jdbcType=VARCHAR} , #{refeflag, jdbcType=VARCHAR} , #{bookkeeping_time, jdbcType=DATE} , #{agent_code, jdbcType=VARCHAR} , #{prem_freq, jdbcType=NUMERIC} , #{policy_organ_code, jdbcType=VARCHAR} , #{holder_name, jdbcType=VARCHAR} 
				, #{bookkeeping_id, jdbcType=NUMERIC} , #{busi_prod_name, jdbcType=VARCHAR} , #{unit_number, jdbcType=VARCHAR} , #{part, jdbcType=VARCHAR} , #{product_abbr_name, jdbcType=VARCHAR} , #{fee_type, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{channel_type, jdbcType=VARCHAR} , #{bookkeeping_flag, jdbcType=NUMERIC} , #{send_date, jdbcType=DATE} , #{red_bookkeeping_by, jdbcType=NUMERIC} , #{agent_name, jdbcType=VARCHAR} , #{is_split, jdbcType=NUMERIC} , #{frozen_status_date, jdbcType=DATE} 
				, SYSDATE , #{policy_type, jdbcType=VARCHAR} , #{is_bank_account_date, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} , #{bank_text_status, jdbcType=VARCHAR} , #{certi_type, jdbcType=VARCHAR} , #{cus_acc_update_by, jdbcType=NUMERIC} 
				, #{is_bank_account_by, jdbcType=NUMERIC} , #{business_code, jdbcType=VARCHAR} , #{money_code, jdbcType=VARCHAR} , #{bank_account, jdbcType=VARCHAR} , #{red_bookkeeping_flag, jdbcType=NUMERIC} , #{cus_acc_fee_amount, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} 
				, #{due_time, jdbcType=DATE} , #{certi_code, jdbcType=VARCHAR} , #{group_name, jdbcType=VARCHAR} , #{cip_bank_code, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{holder_id, jdbcType=NUMERIC} 
				, #{withdraw_type, jdbcType=VARCHAR} , #{bank_user_name, jdbcType=VARCHAR} , #{pay_end_date, jdbcType=DATE} , #{fee_status_by, jdbcType=NUMERIC} , #{deriv_type, jdbcType=VARCHAR} , #{red_bookkeeping_id, jdbcType=NUMERIC} , #{bookkeeping_by, jdbcType=NUMERIC} 
				, #{arap_flag, jdbcType=VARCHAR} , #{claim_nature, jdbcType=NUMERIC}, #{claim_type, jdbcType=VARCHAR},#{coverage_period, jdbcType=VARCHAR},#{coverage_year, jdbcType=NUMERIC}
				, #{product_code, jdbcType=VARCHAR}, #{pay_liab_code, jdbcType=VARCHAR}, #{source_table, jdbcType=VARCHAR}, #{source_table_pk, jdbcType=NUMERIC}, #{is_recover_document, jdbcType=NUMERIC}, #{payee_email, jdbcType=VARCHAR}, #{pre_unit_number, jdbcType=VARCHAR}
				, #{fee_amount_befor_tax, jdbcType=NUMERIC}, #{fee_amount_tax, jdbcType=NUMERIC},#{is_ybt_bank_taxt, jdbcType=NUMERIC}, #{is_corporate, jdbcType=VARCHAR},#{pos_belnr, jdbcType=VARCHAR},#{medical_insurance_card, jdbcType=NUMERIC},#{charge_period, jdbcType=VARCHAR}
				,#{busi_item_id, jdbcType=NUMERIC},#{special_account_flag, jdbcType=VARCHAR} , #{validate_date2, jdbcType=DATE} ,#{statistical_date2, jdbcType=DATE}) 
         ]]>
    </insert>

    <!-- 删除操作 -->
    <delete id="deletePremArap" parameterType="java.util.Map">
        <![CDATA[ DELETE FROM APP___CAP__DBUSER.T_PREM_ARAP WHERE LIST_ID = #{list_id} ]]>
    </delete>
    <!-- 修改操作 -->
    <update id="updatePremArapForStatus" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
           A.FINISH_TIME = #{finish_time, jdbcType=TIMESTAMP},
        <if test="due_time != null and due_time != '' ">
        	<![CDATA[A.DUE_TIME = #{due_time, jdbcType=DATE},]]>
        </if>
        <if test="statistical_date != null and statistical_date != '' ">
        	<![CDATA[A.STATISTICAL_DATE = #{statistical_date, jdbcType=DATE},  ]]>
        </if>
            A.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
            A.FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            A.FEE_STATUS_DATE = SYSDATE,
			A.UPDATE_TIME = SYSDATE,
            A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE 1 = 1 ]]>
        <if test=" old_fee_status != null and old_fee_status != '' "><![CDATA[ AND A.FEE_STATUS in ( #{old_fee_status},'03' )]]></if>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" pre_unit_number != null and pre_unit_number != '' "><![CDATA[ AND A.PRE_UNIT_NUMBER = #{pre_unit_number} ]]></if>
    </update>
    
    <!-- 单条修改操作 -->
    <update id="updatePremArapFeeStatusCount" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
           A.FINISH_TIME = #{finish_time, jdbcType=TIMESTAMP},
        <if test="due_time != null and due_time != '' ">
        	<![CDATA[A.DUE_TIME = #{due_time, jdbcType=DATE},]]>
        </if>
        <if test="statistical_date != null and statistical_date != '' ">
        	<![CDATA[A.STATISTICAL_DATE = #{statistical_date, jdbcType=DATE},  ]]>
        </if>
        <if test=" frozen_status != null and frozen_status != '' ">
            <![CDATA[FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} , 
            		 FROZEN_STATUS_DATE = SYSDATE , ]]>
		</if>
            A.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
            A.FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            A.FEE_STATUS_DATE = SYSDATE
        </set>
        <![CDATA[ WHERE 1 = 1 ]]>
        <if test=" old_fee_status != null and old_fee_status != '' "><![CDATA[ AND A.FEE_STATUS in ( #{old_fee_status},'03' )]]></if>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" pre_unit_number != null and pre_unit_number != '' "><![CDATA[ AND A.PRE_UNIT_NUMBER = #{pre_unit_number} ]]></if>
    </update>
    
    <!-- 修改支付状态和挂起状态操作 -->
    <update id="updatePremArapForzenStatus" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
            A.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
            A.FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            A.FEE_STATUS_DATE = SYSDATE,
            <if test=" pay_mode != null and pay_mode != '' "><![CDATA[  A.PAY_MODE = #{pay_mode, jdbcType=VARCHAR}, ]]></if>
	        A.FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} 
        </set>
        <![CDATA[ WHERE 1 = 1 ]]>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" business_code != null and business_code != '' "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
    </update>
    
    <select id="findPremArapRestAll" resultType="java.util.Map"  parameterType="java.util.Map">
           <![CDATA[
            select DISTINCT A.UNIT_NUMBER,A.HOLDER_NAME FROM APP___CAP__DBUSER.T_PREM_ARAP A 
            WHERE A.DERIV_TYPE IN ('001','003','004')
           ]]>
           <if test=" policy_codes != null and policy_codes.size()>0 ">
           <![CDATA[ AND A.POLICY_CODE IN ]]>
           <foreach item="item" index="id" collection="policy_codes"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
           </foreach>
           </if>
           <![CDATA[
                AND A.HOLDER_ID = #{holder_id}
                AND EXISTS (select 1
                      FROM APP___CAP__DBUSER.T_PREM_ARAP T
                     WHERE T.UNIT_NUMBER = A.UNIT_NUMBER
                     GROUP BY T.UNIT_NUMBER
                    HAVING COUNT(DISTINCT T.HOLDER_ID) = 1)
            ]]>            
    </select>
    
    <update id="updatePremArapRestAll" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A]]> 
		<set>
			HOLDER_ID = #{new_holder_id, jdbcType=NUMERIC},
			HOLDER_NAME = #{new_holder_name, jdbcType=VARCHAR},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
		</set>
		<![CDATA[
	         WHERE A.DERIV_TYPE IN ('001','003','004')
        ]]>
           <if test=" policy_codes != null and policy_codes.size()>0 ">
           <![CDATA[ AND A.POLICY_CODE IN ]]>
           <foreach item="item" index="id" collection="policy_codes"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
           </foreach>
           </if>
           <![CDATA[
                AND A.HOLDER_ID = #{holder_id}
                AND EXISTS (select 1
                      FROM APP___CAP__DBUSER.T_PREM_ARAP T
                     WHERE T.UNIT_NUMBER = A.UNIT_NUMBER
                     GROUP BY T.UNIT_NUMBER
                    HAVING COUNT(DISTINCT T.HOLDER_ID) = 1)
		]]>    
    </update>
    
    
	<!-- 按unit_number修改操作 -->
    <update id="updatePremArapForStatusByUnitNumbers" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            FEE_STATUS = #{updateFeeStatus, jdbcType=VARCHAR} ,
            FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            FEE_STATUS_DATE = SYSDATE ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE FEE_STATUS NOT IN ('04') ]]>
        <if test=" UnitNumbers != null and UnitNumbers.length>0">
            <![CDATA[ AND UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="UnitNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
	<!-- 回写应收表信息 -->
	<update id="updatePremArapForCash" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP  SET ]]>
		<if test=" fee_status != null and fee_status != '' ">
            <![CDATA[ FEE_STATUS = #{fee_status, jdbcType=VARCHAR} , ]]>
		</if>
		<if test="funds_rtn_code!=null and funds_rtn_code != ''">
            <![CDATA[ FUNDS_RTN_CODE = #{funds_rtn_code,jdbcType=VARCHAR}, ]]>
        </if>
        <if test="bank_text_status !=null and bank_text_status != ''">
            <![CDATA[ BANK_TEXT_STATUS = #{bank_text_status,jdbcType=VARCHAR}, ]]>
        </if>
            <![CDATA[
            FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            FEE_STATUS_DATE = SYSDATE ,
            FINISH_TIME = #{finish_time, jdbcType=TIMESTAMP},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP]]>
		<if test=" frozen_status != null and frozen_status != '' ">
            <![CDATA[ 
            ,FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} 
            ,FROZEN_STATUS_DATE = SYSDATE
            ]]>
		</if>
		<if test=" fail_times  != null and fail_times != '' ">
			,FAIL_TIMES = #{fail_times, jdbcType=NUMERIC}
		</if>
		<if test=" cus_acc_details_id  != null or cus_acc_fee_amount != null ">
            <![CDATA[ 
            ,CUS_ACC_FEE_AMOUNT= #{cus_acc_fee_amount, jdbcType=NUMERIC},
            CUS_ACC_DETAILS_ID= #{cus_acc_details_id, jdbcType=NUMERIC},
            CUS_ACC_UPDATE_BY= #{update_by, jdbcType=NUMERIC},
            CUS_ACC_UPDATE_TIME=CURRENT_TIMESTAMP ]]>
		</if>
		<if test="holder_name  != null or holder_name != null">
			, HOLDER_NAME= #{holder_name, jdbcType=VARCHAR}
		</if>
		<if test="cip_branch_bank_code  != null or cip_branch_bank_code != null">
			, CIP_BRANCH_BANK_CODE= #{cip_branch_bank_code, jdbcType=VARCHAR}
		</if>
		<if test="cip_bank_code  != null or cip_bank_code != null">
			, CIP_BANK_CODE= #{cip_bank_code, jdbcType=VARCHAR}
		</if>
		<if
			test="cip_district_bank_code  != null or cip_district_bank_code != null">
			, CIP_DISTRICT_BANK_CODE= #{cip_district_bank_code, jdbcType=VARCHAR}
		</if>
		<if test="pay_mode  != null or pay_mode != '' ">
			, PAY_MODE= #{pay_mode, jdbcType=VARCHAR}
		</if>
		<if test="bookkeeping_flag  != null or bookkeeping_flag != '' ">
			, BOOKKEEPING_FLAG= #{bookkeeping_flag, jdbcType=NUMERIC}
		</if>
		<if test="is_corporate  != null or is_corporate != '' ">
			, IS_CORPORATE= #{is_corporate, jdbcType=VARCHAR}
		</if>
        <![CDATA[WHERE LIST_ID = #{list_id}]]>
	</update>
	
	<!-- 回写应收表信息(退票) -->
	<update id="updatePremArapForRefund" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP  SET ]]>
		<if test=" fee_status != null and fee_status != '' ">
            <![CDATA[ FEE_STATUS = #{fee_status, jdbcType=VARCHAR} , ]]>
		</if>
		<if test="funds_rtn_code!=null and funds_rtn_code != ''">
            <![CDATA[ FUNDS_RTN_CODE = #{funds_rtn_code,jdbcType=VARCHAR}, ]]>
        </if>
        <if test="bank_text_status !=null and bank_text_status != ''">
            <![CDATA[ BANK_TEXT_STATUS = #{bank_text_status,jdbcType=VARCHAR}, ]]>
        </if>
            <![CDATA[
            FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            FEE_STATUS_DATE = SYSDATE ,
            FINISH_TIME = #{finish_time, jdbcType=TIMESTAMP},
            IS_BANK_TEXT = 0 ]]>
		<if test=" frozen_status != null and frozen_status != '' ">
            <![CDATA[ 
            ,FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} 
            ]]>
		</if>
		<if test=" fail_times  != null and fail_times != '' ">
			,FAIL_TIMES = #{fail_times, jdbcType=NUMERIC}
		</if>
		<if test=" cus_acc_details_id  != null or cus_acc_fee_amount != null ">
            <![CDATA[ 
            ,CUS_ACC_FEE_AMOUNT= #{cus_acc_fee_amount, jdbcType=NUMERIC},
            CUS_ACC_DETAILS_ID= #{cus_acc_details_id, jdbcType=NUMERIC},
            CUS_ACC_UPDATE_BY= #{update_by, jdbcType=NUMERIC},
            CUS_ACC_UPDATE_TIME=CURRENT_TIMESTAMP ]]>
		</if>
		<if test="holder_name  != null or holder_name != null">
			, HOLDER_NAME= #{holder_name, jdbcType=VARCHAR}
		</if>
		<if test="cip_branch_bank_code  != null or cip_branch_bank_code != null">
			, CIP_BRANCH_BANK_CODE= #{cip_branch_bank_code, jdbcType=VARCHAR}
		</if>
		<if test="cip_bank_code  != null or cip_bank_code != null">
			, CIP_BANK_CODE= #{cip_bank_code, jdbcType=VARCHAR}
		</if>
		<if
			test="cip_district_bank_code  != null or cip_district_bank_code != null">
			, CIP_DISTRICT_BANK_CODE= #{cip_district_bank_code, jdbcType=VARCHAR}
		</if>
		<if test="pay_mode  != null or pay_mode != '' ">
			, PAY_MODE= #{pay_mode, jdbcType=VARCHAR}
		</if>
		<if test="bookkeeping_flag  != null or bookkeeping_flag != '' ">
			, BOOKKEEPING_FLAG= #{bookkeeping_flag, jdbcType=NUMERIC}
		</if>
        <![CDATA[WHERE LIST_ID = #{list_id}]]>
	</update>
	
		
	<!-- 回写应收表信息(微信支付结果回传接口) -->
	<update id="updatePremArapByUnitnumber" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP  SET ]]>
		<if test=" fee_status != null and fee_status != '' ">
            <![CDATA[ FEE_STATUS = #{fee_status, jdbcType=VARCHAR} , ]]>
		</if>
            <![CDATA[
            FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            FEE_STATUS_DATE = SYSDATE ,
            FINISH_TIME = #{finish_time, jdbcType=TIMESTAMP},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP]]>
		<if test=" frozen_status != null and frozen_status != '' ">
            <![CDATA[ 
            ,FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} 
            ]]>
		</if>
		<if test=" fail_times  != null and fail_times != '' ">
			,FAIL_TIMES = #{fail_times, jdbcType=NUMERIC}
		</if>
		<if test=" cus_acc_details_id  != null or cus_acc_fee_amount != null ">
            <![CDATA[ 
            ,CUS_ACC_FEE_AMOUNT= #{cus_acc_fee_amount, jdbcType=NUMERIC},
            CUS_ACC_DETAILS_ID= #{cus_acc_details_id, jdbcType=NUMERIC},
            CUS_ACC_UPDATE_BY= #{update_by, jdbcType=NUMERIC},
            CUS_ACC_UPDATE_TIME=CURRENT_TIMESTAMP ]]>
		</if>
		<if test="holder_name  != null or holder_name != null">
			, HOLDER_NAME= #{holder_name, jdbcType=VARCHAR}
		</if>
		<if test="cip_branch_bank_code  != null or cip_branch_bank_code != null">
			, CIP_BRANCH_BANK_CODE= #{cip_branch_bank_code, jdbcType=VARCHAR}
		</if>
		<if test="cip_bank_code  != null or cip_bank_code != null">
			, CIP_BANK_CODE= #{cip_bank_code, jdbcType=VARCHAR}
		</if>
		<if
			test="cip_district_bank_code  != null or cip_district_bank_code != null">
			, CIP_DISTRICT_BANK_CODE= #{cip_district_bank_code, jdbcType=VARCHAR}
		</if>
		<if test="pay_mode  != null or pay_mode != '' ">
			, PAY_MODE= #{pay_mode, jdbcType=VARCHAR}
		</if>
		<if test="bookkeeping_flag  != null or bookkeeping_flag != '' ">
			, BOOKKEEPING_FLAG= #{bookkeeping_flag, jdbcType=NUMERIC}
		</if>
        <![CDATA[WHERE UNIT_NUMBER = #{unit_number}
                  and fee_status <> '01'
        ]]>
	</update>
	
	<!-- 根据受理号和失败查询应收应付信息(保全收付费状态回写核心接口) -->
    <select id="findQueryArapByBusinessCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
           select 
              A.UNIT_NUMBER, 
              A.BUSINESS_CODE,
              MAX(A.FEE_STATUS) FEE_STATUS,
              MAX(A.DERIV_TYPE) DERIV_TYPE
        FROM APP___CAP__DBUSER.T_PREM_ARAP A
        WHERE 1=1
        AND A.DERIV_TYPE = '004'
        AND A.BUSINESS_CODE = #{business_code}
        GROUP BY A.BUSINESS_CODE,A.UNIT_NUMBER 
        ]]>
    </select>
	
	<!-- 根据受理号修改应收应付状态(保全收付费状态回写核心接口) -->
	<update id="updateFeeStatus" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP  SET ]]>
		<if test=" fee_status != null and fee_status != '' ">
           <![CDATA[ FEE_STATUS = #{fee_status, jdbcType=VARCHAR} , ]]>
		</if>
           <![CDATA[ FEE_STATUS_BY = #{fee_status_by, jdbcType=NUMERIC} ,
                     FEE_STATUS_DATE = SYSDATE ,
                     UPDATE_BY = 930020665,
		             UPDATE_TIME = SYSDATE,
                     UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
               WHERE 1 = 1
                 AND FEE_STATUS IN ('00','03','04')
                 AND BUSINESS_CODE = #{business_code}
                 AND UNIT_NUMBER = #{unit_number}
        ]]>
	</update>
	
	<!-- 根据受理号和失败查询应收应付信息(保全收付费状态回写核心接口) -->
    <select id="capQueryArapByBusinessCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
     select D.* FROM
        (select 
              A.UNIT_NUMBER UNIT_NUMBER, 
              MAX(A.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
              SUM(A.POLICY_BALANCE) POLICY_BALANCE,
              A.BUSINESS_CODE,
              MAX(A.FEE_STATUS) FEE_STATUS,
              MAX(BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(DERIV_TYPE) DERIV_TYPE,
              MIN(CUSTOMER_ID) CUSTOMER_ID,
              MIN(APPLY_CODE) APPLY_CODE,
              MAX(BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(BANK_CODE) BANK_CODE,
              MAX(BANK_USER_NAME) BANK_USER_NAME,
              MAX(CERTI_TYPE) CERTI_TYPE,
              MAX(CERTI_CODE) CERTI_CODE,
              MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              CASE WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN        
                   MIN(POLICY_CODE)
                   WHEN MAX(WITHDRAW_TYPE) = '**********' THEN (SELECT MIN(T.POLICY_CODE) FROM APP___CAP__DBUSER.T_PREM_ARAP T WHERE A.UNIT_NUMBER=T.UNIT_NUMBER AND T.ARAP_FLAG='${ar}')
              ELSE
                   NULL
              END POLICY_CODE,
              MAX(WITHDRAW_TYPE) WITHDRAW_TYPE, 
              MIN(PAYEE_NAME) PAYEE_NAME,                                    
              MIN(PAY_MODE) PAY_MODE,                                           
              MAX(ARAP_FLAG) ARAP_FLAG,                                         
              SUM(                                                                
                   CASE WHEN 
                      ARAP_FLAG ='1' 
                       THEN 
                        FEE_AMOUNT 
                      ELSE 
                        -1*FEE_AMOUNT 
                       END
                     ) FEE_AMOUNT,       
              MIN(DUE_TIME) DUE_TIME,                               
              MAX(HOLDER_NAME)  HOLDER_NAME,                             
              MAX(INSURED_NAME)  INSURED_NAME,
              MAX(GROUP_CODE)  GROUP_CODE,                                 
              MAX(GROUP_NAME)  GROUP_NAME, 
              MAX(OPERATOR_BY)  OPERATOR_BY,
              MAX(A.SPECIAL_ACCOUNT_FLAG) SPECIAL_ACCOUNT_FLAG
        FROM APP___CAP__DBUSER.T_PREM_ARAP A
        WHERE 1=1
        AND A.BUSINESS_CODE = #{business_code} 
        AND A.UNIT_NUMBER = #{unit_number}
        GROUP BY A.UNIT_NUMBER,A.BUSINESS_CODE) D
         ]]>
    </select>
	
    <!-- 修改记账状态操作 -->
    <update id="updatePremArapForBookkeepingFlag" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            BOOKKEEPING_FLAG = #{bookkeeping_flag, jdbcType=NUMERIC},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number}]]>
    </update>
    <!-- 修改操作 -->
    <update id="updatePremArap" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            <trim suffixOverrides=",">
                TASK_MARK = #{task_mark, jdbcType=NUMERIC},
                POLICY_ORGAN_CODE = #{policy_organ_code, jdbcType=VARCHAR} ,
                HOLDER_NAME = #{holder_name, jdbcType=VARCHAR} ,
                IS_ITEM_MAIN = #{is_item_main, jdbcType=NUMERIC} ,
                BOOKKEEPING_ID = #{bookkeeping_id, jdbcType=NUMERIC} ,
                BUSI_PROD_NAME = #{busi_prod_name, jdbcType=VARCHAR} ,
                PAYEE_PHONE = #{payee_phone, jdbcType=VARCHAR} ,
                UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
                PAID_COUNT = #{paid_count, jdbcType=NUMERIC} ,
                FEE_TYPE = #{fee_type, jdbcType=VARCHAR} ,
                SEQ_NO = #{seq_no, jdbcType=NUMERIC},
                BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
                APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
                FEE_STATUS_DATE = #{fee_status_date, jdbcType=DATE} ,
                ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
                RED_BOOKKEEPING_TIME = #{red_bookkeeping_time, jdbcType=DATE} ,
                CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
                IS_BANK_TEXT_DATE = #{is_bank_text_date, jdbcType=DATE} ,
                UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
                CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
                IS_RISK_MAIN = #{is_risk_main, jdbcType=NUMERIC} ,
                IS_BANK_ACCOUNT = #{is_bank_account, jdbcType=NUMERIC} ,
                FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} ,
                POSTED = #{posted, jdbcType=VARCHAR} ,
                BOOKKEEPING_FLAG = #{bookkeeping_flag, jdbcType=NUMERIC} ,
                GROUP_ID = #{group_id, jdbcType=NUMERIC} ,
                RED_BOOKKEEPING_BY = #{red_bookkeeping_by, jdbcType=NUMERIC} ,
                UPDATE_TIME = SYSDATE ,
                FROZEN_STATUS_DATE = #{frozen_status_date,jdbcType=DATE} ,
                INSURED_NAME = #{insured_name,jdbcType=VARCHAR} ,
                INSURED_ID = #{insured_id,jdbcType=NUMERIC} ,
                POLICY_TYPE = #{policy_type,jdbcType=VARCHAR} ,
                PRODUCT_CHANNEL = #{product_channel,jdbcType=VARCHAR} ,
                IS_BANK_ACCOUNT_DATE = #{is_bank_account_date, jdbcType=DATE} ,
                POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
                PAY_MODE = #{pay_mode,jdbcType=VARCHAR} ,
                BRANCH_CODE = #{branch_code,jdbcType=VARCHAR} ,
                VALIDATE_DATE = #{validate_date,jdbcType=DATE} ,
                BUSINESS_TYPE = #{business_type,jdbcType=VARCHAR} ,
                WITHDRAW_TYPE = #{withdraw_type,jdbcType=VARCHAR} ,
                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,
                RED_BELNR = #{red_belnr, jdbcType=VARCHAR} ,
                BANK_TEXT_STATUS = #{bank_text_status,jdbcType=VARCHAR},
                CERTI_TYPE = #{certi_type,jdbcType=VARCHAR} ,
                IS_BANK_TEXT_BY = #{is_bank_text_by,jdbcType=NUMERIC} ,
                MONEY_CODE = #{money_code,jdbcType=VARCHAR} ,
                BUSINESS_CODE = #{business_code,jdbcType=VARCHAR} ,
                IS_BANK_ACCOUNT_BY = #{is_bank_account_by, jdbcType=NUMERIC} ,
                PAYEE_NAME = #{payee_name, jdbcType=VARCHAR} ,
                BANK_ACCOUNT = #{bank_account, jdbcType=VARCHAR} ,
                RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag, jdbcType=NUMERIC} ,
                FINISH_TIME = #{finish_time, jdbcType=TIMESTAMP} ,
                DUE_TIME = #{due_time, jdbcType=DATE},
                IS_BANK_TEXT = #{is_bank_text, jdbcType=NUMERIC} ,
                CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
                BUSI_APPLY_DATE = #{busi_apply_date, jdbcType=DATE} ,
                BELNR = #{belnr,jdbcType=VARCHAR} ,
                FEE_AMOUNT = #{fee_amount,jdbcType=NUMERIC} ,
                HOLDER_ID = #{holder_id,jdbcType=NUMERIC} ,
                ROLLBACK_UNIT_NUMBER = #{rollback_unit_number, jdbcType=VARCHAR} ,
                FAIL_TIMES = #{fail_times, jdbcType=NUMERIC} ,
                CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
                POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
                FROZEN_STATUS_BY = #{frozen_status_by, jdbcType=NUMERIC} ,
                BANK_USER_NAME = #{bank_user_name, jdbcType=VARCHAR} ,
                FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
                FEE_STATUS_BY = #{fee_status_by, jdbcType=NUMERIC} ,
                PAY_END_DATE = #{pay_end_date, jdbcType=DATE} ,
                DERIV_TYPE = #{deriv_type, jdbcType=VARCHAR} ,
                RED_BOOKKEEPING_ID = #{red_bookkeeping_id, jdbcType=NUMERIC} ,
                BOOKKEEPING_BY = #{bookkeeping_by, jdbcType=NUMERIC} ,
                ARAP_FLAG = #{arap_flag, jdbcType=VARCHAR} ,
                BANK_CODE = #{bank_code,jdbcType=VARCHAR} ,
                BOOKKEEPING_TIME = #{bookkeeping_time, jdbcType=DATE} ,
                AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
                PREM_FREQ = #{prem_freq, jdbcType=VARCHAR} ,
                AUDIT_DATE = #{audit_date, jdbcType=DATE} ,
                STATISTICAL_DATE = #{statistical_date, jdbcType=DATE} ,
                POS_BELNR = #{pos_belnr, jdbcType=VARCHAR} ,
            </trim>
        </set>
        <![CDATA[ WHERE LIST_ID = #{list_id} ]]>
    </update>

    <!-- 按索引查询操作 -->
    <select id="findPremArapByListId" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1   ]]>
        <include refid="queryPremArapByListIdCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>

    <select id="findPremArapByArapFeeId" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1   ]]>
        <include refid="queryPremArapByArapFeeIdCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>
    <select id="findPremArapByOrganCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1   ]]>
        <include refid="queryPremArapByOrganCodeCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>

    <select id="findPremArapByApplyCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1   ]]>
        <include refid="queryPremArapByApplyCodeCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>

    <select id="findPremArapByDueTime" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1  ]]>
        <include refid="queryPremArapByDueTimeCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>
    <!-- 按map查询操作 -->
    <select id="findAllMapPremArap" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
        <include refid="premArapWhereCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>

    <!-- 查询所有操作 -->
    <select id="findAllPremArap" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID,  A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, A.VALIDATE_DATE2,
            A.PREM_FREQ, A.GROUP_CODE, A.GROUP_NAME, A.AUDIT_DATE,A.STATISTICAL_DATE,A.POS_BELNR , A.FUNDS_RTN_CODE, A.INSERT_TIME 
            FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
        <include refid="premArapWhereCondition" /> 
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>

    <!-- 查询个数操作 -->
    <select id="findPremArapTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ select COUNT(1) FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1  ]]>
    </select>
    <!-- 查询个数操作 -->
    <select id="findPremArapTotalByUnitNumber" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ select COUNT(1) FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1  ]]>
        <![CDATA[ AND A.UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ]]>
    </select>

    <!-- 分页查询操作 -->
    <select id="queryPremArapForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select B.RN AS rowNumber, B.TASK_MARK, B.POLICY_ORGAN_CODE, B.HOLDER_NAME, B.IS_ITEM_MAIN, B.BOOKKEEPING_ID, B.BUSI_PROD_NAME, 
            B.PAYEE_PHONE, B.UNIT_NUMBER, B.PAID_COUNT, B.FEE_TYPE, B.SEQ_NO, B.BUSI_PROD_CODE, B.APPLY_CODE, 
            B.FEE_STATUS_DATE, B.ORGAN_CODE, B.RED_BOOKKEEPING_TIME, B.CHANNEL_TYPE, B.IS_BANK_TEXT_DATE, 
            B.CHARGE_YEAR, B.IS_RISK_MAIN, B.IS_BANK_ACCOUNT, B.FROZEN_STATUS, B.POSTED, B.BOOKKEEPING_FLAG, 
            B.GROUP_ID, B.RED_BOOKKEEPING_BY, B.FROZEN_STATUS_DATE, B.INSURED_NAME, B.INSURED_ID, B.POLICY_TYPE, 
            B.PRODUCT_CHANNEL, B.IS_BANK_ACCOUNT_DATE, B.POLICY_CODE, B.PAY_MODE, B.BRANCH_CODE, B.VALIDATE_DATE, B.BUSINESS_TYPE, 
            B.WITHDRAW_TYPE, B.RED_BELNR, B.BANK_TEXT_STATUS, B.CERTI_TYPE, B.IS_BANK_TEXT_BY, B.MONEY_CODE, 
            B.BUSINESS_CODE, B.IS_BANK_ACCOUNT_BY, B.PAYEE_NAME, B.BANK_ACCOUNT, B.RED_BOOKKEEPING_FLAG, B.FINISH_TIME, 
            B.DUE_TIME, B.IS_BANK_TEXT, B.CERTI_CODE, B.BUSI_APPLY_DATE, B.BELNR, B.FEE_AMOUNT, B.LIST_ID, 
            B.HOLDER_ID, B.WITHDRAW_TYPE, B.ROLLBACK_UNIT_NUMBER, B.FAIL_TIMES, B.CUSTOMER_ID, B.POLICY_YEAR, 
            B.FROZEN_STATUS_BY, B.BANK_USER_NAME, B.FEE_STATUS, B.FEE_STATUS_BY, B.PAY_END_DATE, B.DERIV_TYPE, 
            B.RED_BOOKKEEPING_ID, B.BOOKKEEPING_BY, B.ARAP_FLAG, B.BANK_CODE, B.BOOKKEEPING_TIME, B.AGENT_CODE, 
            B.PREM_FREQ, B.GROUP_CODE, B.GROUP_NAME, B.AUDIT_DATE,B.STATISTICAL_DATE FROM (
                    select ROWNUM RN, A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID,  A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.GROUP_CODE, A.GROUP_NAME, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE ROWNUM <= #{LESS_NUM} ]]>
        <include refid="premArapWhereCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]> 
        <![CDATA[ )  B
            WHERE B.RN > #{GREATER_NUM} ]]>
    </select>

    <select id="findPremArap" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            select A.* FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1 
            ]]>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ]]></if>
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ]]></if>
        <if test=" customer_id != null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
        <if test=" business_code != null and business_code != '' "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
        <if test=" holder_id != null and holder_id != '' "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>        
    </select>
    
    
    
    
    <!-- 柜台收付费检索where条件 -->
    <sql id="capQueryWhereClause">
        <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND (A.APPLY_CODE = #{policy_code} or A.POLICY_CODE = #{policy_code} )]]></if>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code}  ]]></if>
        <if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
        <if test=" withdraw_type  != null and withdraw_type != '' "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
        <if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
        <if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
        <!-- 收费状态为04,03的是制返盘的转账途中，不需要制盘状态来限定，huangshuang 17-04-14 -->
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND (A.FEE_STATUS =#{fee_status} OR (A.FEE_STATUS IN ('04','03','20'))) ]]></if>
        <if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
        <if test=" owner_id  != null "><![CDATA[ AND A.OWNER_ID = #{owner_id} ]]></if>
        <if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CUSTOMER_ID IN (SELECT T.CUSTOMER_ID FROM APP___PAS__DBUSER.T_CUSTOMER T WHERE  T.CUSTOMER_CERTI_CODE = #{certi_code} )  ]]></if>
        <if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
        <if test=" bank_code != null and bank_code != ''"><![CDATA[ AND (A.BANK_CODE =#{bank_code} ]]></if>
        <if test=" not_withdraw_type  != null and not_withdraw_type != '' "><![CDATA[ AND A.WITHDRAW_TYPE not in (#{not_withdraw_type}) ]]></if>
        <if test=" start_date != null and start_date != '' "><![CDATA[ AND A.DUE_TIME >= #{start_date} ]]></if>
        <if test=" end_date != null and end_date != '' "><![CDATA[ AND A.DUE_TIME < #{end_date} + 1 ]]></if>
        <if test=" organ_code  != null and organ_code != '' ">
        AND A.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM APP___CAP__DBUSER.T_UDMP_ORG_REL O START WITH O.ORGAN_CODE=#{organ_code}
                    CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
        </if>
    </sql>

    <!-- 柜台收付费检索having条件 -->
    <sql id="capQueryHavingClause">
        <if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ 
             and
             case when                                                       
                sum(
                      case when 
                        arap_flag =1 
                      then 
                        fee_amount 
                      else 
                        -1*fee_amount 
                       end
                     )  < 0 
              then
                 '${ap}'
              else
                 '${ar}'
              end =#{arap_flag}
         ]]></if>

    </sql>

    <!-- 柜台收付费检索查询个数 -->
    <select id="capQueryTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
    <![CDATA[ 
    select 
        COUNT(UNIT_NUMBER)
    FROM (
        select 
              A.UNIT_NUMBER UNIT_NUMBER 
        FROM 
                    APP___CAP__DBUSER.T_PREM_ARAP A
       
                    WHERE
                        1=1 
                        AND A.BANK_TEXT_STATUS <> '7' AND A.FEE_AMOUNT <> 0  AND A.PAY_MODE  IN ('10','11','20','22','32','31','34','98')
                        AND NOT EXISTS (SELECT 1 FROM APP___CAP__DBUSER.T_FMS_NONREALTIME T WHERE T.UNIT_NUMBER=A.UNIT_NUMBER AND  T.TRANS_STATE='4')
                        AND NOT EXISTS (SELECT 1 FROM APP___CAP__DBUSER.T_BILL_DETAILS T WHERE T.UNIT_NUMBER=A.UNIT_NUMBER AND T.BILL_MODE='10' AND T.DELETE_FLAG is null)
                        AND ( SPECIAL_ACCOUNT_FLAG <> '1' OR SPECIAL_ACCOUNT_FLAG IS NULL )
          ]]>
        <include refid="capQueryWhereClause" />
          <![CDATA[
        GROUP BY 
                A.UNIT_NUMBER
       
         HAVING
                1=1
        
         ]]>
        <include refid="capQueryHavingClause" />
        )

    </select>
    <!-- 柜台收付费检索查询 -->
    <select id="capQuery" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
      select C.* FROM 
        (select  ROWNUM RN ,B.*, CASE WHEN FEE_AMOUNT >= 0 THEN '${ar}' ELSE '${ap}' END ARAP_FLAG FROM
                 (select  A.UNIT_NUMBER UNIT_NUMBER, MIN(BUSINESS_CODE) BUSINESS_CODE, MIN(DERIV_TYPE) DERIV_TYPE, MIN(CERTI_CODE) CERTI_CODE,      
                      MIN(CUSTOMER_ID) CUSTOMER_ID, MIN(APPLY_CODE) APPLY_CODE, MIN(BANK_ACCOUNT) BANK_ACCOUNT,MIN(APPLY_CODE), MAX(BUSINESS_TYPE) BUSINESS_TYPE, 
                      CASE WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN MIN(POLICY_CODE)ELSE NULL END POLICY_CODE, MAX(WITHDRAW_TYPE) WITHDRAW_TYPE, 
                      MIN(PAYEE_NAME) PAYEE_NAME, MIN(PAY_MODE) PAY_MODE, SUM( CASE WHEN ARAP_FLAG ='${ar}'THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END) FEE_AMOUNT,                                                                  
                      MIN(DUE_TIME) DUE_TIME, MAX(HOLDER_NAME) HOLDER_NAME, MAX(INSURED_NAME) INSURED_NAME, MIN(PAY_END_DATE)PAY_END_DATE, MAX(FROZEN_STATUS)FROZEN_STATUS,
                      MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS, MIN(GROUP_CODE) GROUP_CODE, MIN(GROUP_NAME) GROUP_NAME, MIN(OPERATOR_BY) OPERATOR_BY, MAX(FEE_STATUS) FEE_STATUS
                      FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1=1
                      AND A.BANK_TEXT_STATUS <> '7' AND A.FEE_AMOUNT <> 0  AND A.PAY_MODE IN ('10','11','20','22','32','31','34','98')
                      AND NOT EXISTS (SELECT 1 FROM APP___CAP__DBUSER.T_FMS_NONREALTIME T WHERE T.UNIT_NUMBER=A.UNIT_NUMBER AND  T.TRANS_STATE='4')
                      AND NOT EXISTS (SELECT 1 FROM APP___CAP__DBUSER.T_BILL_DETAILS T WHERE T.UNIT_NUMBER=A.UNIT_NUMBER AND T.BILL_MODE='10' AND T.DELETE_FLAG is null)
                      AND ( SPECIAL_ACCOUNT_FLAG <> '1' OR SPECIAL_ACCOUNT_FLAG IS NULL )
          ]]>
        <include refid="capQueryWhereClause" />
          <![CDATA[
        GROUP BY 
                A.UNIT_NUMBER ,A.BUSINESS_CODE, A.DERIV_TYPE
        HAVING  CASE WHEN SUM(CASE
                    WHEN ARAP_FLAG = '${ar}' THEN
                              FEE_AMOUNT
                             ELSE
                              -1 * FEE_AMOUNT
                           END) >=0  THEN '${ar}' ELSE '${ap}' END = #{arap_flag}
        ORDER BY 
                MAX(A.INSERT_TIME) DESC ) B
         ]]>
         <![CDATA[
          WHERE ROWNUM <= #{LESS_NUM} )C
         ]]>
         <![CDATA[
          WHERE C.RN > #{GREATER_NUM}
         ]]>
    </select>

    <!-- 柜台收付费检索查询结束 -->

    <!-- 柜台收付费明细查询 -->
    <select id="arapInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
           select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID,  A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.LIST_ID, A.HOLDER_ID,
            A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, 
            A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY,A.MEDICAL_INSURANCE_CARD,
            A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, A.PREM_FREQ, A.GROUP_CODE, A.GROUP_NAME,A.BUSI_ITEM_ID,
            A.CUS_ACC_DETAILS_ID,A.CUS_ACC_FEE_AMOUNT, A.CUS_ACC_UPDATE_BY, A.CUS_ACC_UPDATE_TIME,A.CIP_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.CIP_BRANCH_BANK_CODE,
            A.CUSTOMER_ACCOUNT_FLAG, A.BATCH_NO, A.FUNDS_RTN_CODE, A.OPERATOR_BY, A.REFEFLAG, A.SERVICE_CODE,A.POLICY_BALANCE,A.IS_CORPORATE,
            NVL(DECODE(R.FILTER2,'Y',R.CR_SEG4,R.DR_SEG4),'DR_NULL') DR_ACCUITEM, NVL(DECODE(R.FILTER2,'Y',R.DR_SEG4,R.CR_SEG4),'CR_NULL') CR_ACCUITEM,A.SPECIAL_ACCOUNT_FLAG,
	    (CASE WHEN A.DERIV_TYPE = '001' THEN (SELECT MAX(U.USER_NAME) FROM APP___CAP__DBUSER.T_UDMP_USER U WHERE U.USER_ID = A.INSERT_BY) END) insert_name,
        ]]>
        <if test=" ar != null and ar != '' ">
            <![CDATA[
             CASE WHEN 
                    ARAP_FLAG ='${ar}'
                THEN
                    FEE_AMOUNT
                ELSE
                    -1*FEE_AMOUNT
                END FEE_AMOUNT
            FROM APP___CAP__DBUSER.T_PREM_ARAP A LEFT JOIN APP___CAP__DBUSER.T_GL_ACCOUNTING_RULE R ON R.FEE_TABLE = '2' AND  A.FEE_TYPE = R.BASE1 AND A.WITHDRAW_TYPE = R.BASE2  
            WHERE 1 = 1 
            ]]>
        </if>
        <if test=" ar == null or ar == '' ">
            <![CDATA[
            FEE_AMOUNT
            FROM APP___CAP__DBUSER.T_PREM_ARAP A LEFT JOIN APP___CAP__DBUSER.T_GL_ACCOUNTING_RULE R ON R.FEE_TABLE = '2' AND  A.FEE_TYPE = R.BASE1 AND A.WITHDRAW_TYPE = R.BASE2  
            WHERE 1 = 1 
            ]]>
        </if>
        <if test=" UnitNumbers != null and UnitNumbers.length>0">
            <![CDATA[ AND UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="UnitNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type, jdbcType=VARCHAR} ]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ]]></if>
    	<!-- <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND (A.FEE_STATUS in (${fee_status})  OR (A.FEE_STATUS='04' AND A.BANK_TEXT_STATUS='4') OR (A.FEE_STATUS='03' AND A.BANK_TEXT_STATUS='8'))]]></if> -->
    	<if test=" fee_status != null and fee_status != '' and fee_status != '17' and fee_status != '15' "><![CDATA[ AND (A.FEE_STATUS in (${fee_status})  OR (A.FEE_STATUS='04') OR (A.FEE_STATUS='03'))]]></if>
    	<if test=" fee_status != null and fee_status != '' and fee_status == '17' and fee_status == '15' "><![CDATA[ AND A.FEE_STATUS in (${fee_status}) ]]></if>
    </select>
    
      <!--查询是否退保  -->
    <select id="querybypolcode" resultType="java.util.Map"
        parameterType="java.util.Map">
			SELECT A.UNIT_NUMBER
			FROM APP___CAP__DBUSER.t_Prem_Arap A
			WHERE A.SERVICE_CODE IN ('CT','XT','EA')
   		 <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
    </select>
    <!-- 收费信息提交校验开始 -->
    <select id="capQueryCheckFeeAmountByUnitNumber" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        select T.UNIT_NUMBER,
        	SUM(CASE WHEN T.DERIV_TYPE = #{deriv_type, jdbcType=VARCHAR} THEN T.FEE_AMOUNT END) FEE_AMOUNT,
       		SUM(T.FEE_AMOUNT) PAID_COUNT
        FROM APP___CAP__DBUSER.T_PREM_ARAP T WHERE 1=1 
        ]]>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND T.UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ]]></if>
        <!-- <if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND T.DERIV_TYPE = #{deriv_type, jdbcType=VARCHAR} ]]></if> -->
<!--         <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND (T.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} OR (T.FEE_STATUS='04' AND T.BANK_TEXT_STATUS='4') OR (FEE_STATUS='03' AND BANK_TEXT_STATUS='8'))]]></if>-->
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND (T.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} OR (T.FEE_STATUS='04') OR (FEE_STATUS='03'))]]></if>
        <![CDATA[
        AND NOT EXISTS(SELECT 1 FROM DEV_CAP.T_BILL_DETAILS TBD WHERE T.UNIT_NUMBER = TBD.UNIT_NUMBER AND TBD.DELETE_FLAG IS NULL)
        GROUP BY T.UNIT_NUMBER,T.BUSINESS_CODE
         ]]>
    </select>
    <!-- 收费信息提交校验结束 -->

    <!-- 收费信息合计查询 -->
    <select id="capQueryAmountByUnitNumber" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
     select D.* FROM
        (select 
              A.UNIT_NUMBER UNIT_NUMBER, 
              MAX(A.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
              SUM(A.POLICY_BALANCE) POLICY_BALANCE,
              MIN(BUSINESS_CODE) BUSINESS_CODE,
              MAX(A.FEE_STATUS) FEE_STATUS,
              MAX(BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(DERIV_TYPE) DERIV_TYPE,
              MIN(CUSTOMER_ID) CUSTOMER_ID,
              MIN(APPLY_CODE) APPLY_CODE,
              MAX(BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(BANK_CODE) BANK_CODE,
              MAX(BANK_USER_NAME) BANK_USER_NAME,
              MAX(CERTI_TYPE) CERTI_TYPE,
              MAX(CERTI_CODE) CERTI_CODE,
              MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              CASE WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN        
                   MIN(POLICY_CODE)
                   WHEN MAX(WITHDRAW_TYPE) = '**********' THEN (SELECT MIN(T.POLICY_CODE) FROM APP___CAP__DBUSER.T_PREM_ARAP T WHERE A.UNIT_NUMBER=T.UNIT_NUMBER AND T.ARAP_FLAG='${ar}')
              ELSE
                   NULL
              END POLICY_CODE,
              MAX(WITHDRAW_TYPE) WITHDRAW_TYPE, 
              MIN(PAYEE_NAME) PAYEE_NAME,                                    
              MIN(PAY_MODE) PAY_MODE,                                           
              MAX(ARAP_FLAG) ARAP_FLAG,                                         
              SUM(                                                                
                   CASE WHEN 
                      ARAP_FLAG ='${ar}' 
                       THEN 
                        FEE_AMOUNT 
                      ELSE 
                        -1*FEE_AMOUNT 
                       END
                     ) FEE_AMOUNT,                                            
              
              MIN(DUE_TIME) DUE_TIME,                               
              MAX(HOLDER_NAME)  HOLDER_NAME,   
              MAX(HOLDER_ID)  HOLDER_ID,                          
              MAX(INSURED_NAME)  INSURED_NAME,
              MAX(GROUP_CODE)  GROUP_CODE,                                 
              MAX(GROUP_NAME)  GROUP_NAME, 
              MAX(OPERATOR_BY)  OPERATOR_BY,
              (CASE WHEN MAX(A.DERIV_TYPE) = '001' THEN MAX(U.USER_NAME) END) insert_name,
              MAX(A.SPECIAL_ACCOUNT_FLAG) SPECIAL_ACCOUNT_FLAG
        FROM APP___CAP__DBUSER.T_PREM_ARAP A
        LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U
        ON A.INSERT_BY = U.USER_ID
        WHERE 1=1
        ]]>
        <if test=" UnitNumbers != null and UnitNumbers.length>0">
            <![CDATA[ AND UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="UnitNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" customer_id != null and customer_id !='' ">
            <![CDATA[ AND CUSTOMER_ID = #{customer_id} ]]>
        </if>
        <!-- <if test=" fee_status != null and fee_status != ''">
        	<![CDATA[ AND (A.FEE_STATUS IN (${fee_status}) OR (A.FEE_STATUS='04' AND A.BANK_TEXT_STATUS='4') OR (A.FEE_STATUS='03' AND A.BANK_TEXT_STATUS='8')) ]]>
        </if> -->
        <if test=" fee_status != null and fee_status != '' and fee_status != '15' ">
        	<![CDATA[ AND (A.FEE_STATUS IN (${fee_status}) OR (A.FEE_STATUS='04') OR (A.FEE_STATUS='03')) ]]>
        </if>
        <if test=" fee_status != null and fee_status != '' and fee_status == '15' ">
        	<![CDATA[ AND A.FEE_STATUS IN (${fee_status}) ]]>
        </if>
        <if test="due_time != null and due_time != '' ">
        	<![CDATA[ AND A.DUE_TIME > #{due_time} ]]>
        </if>
        <![CDATA[
        GROUP BY A.UNIT_NUMBER) D
         ]]>
    </select>

    <!-- 单笔实时收付合计信息查询 -->
    <select id="capQueryForRealTimePayment" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
     select D.* FROM
        (select 
              A.UNIT_NUMBER UNIT_NUMBER, 
              A.DERIV_TYPE DERIV_TYPE,
              MIN(BUSINESS_CODE) BUSINESS_CODE,
              MAX(BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(CUSTOMER_ID) CUSTOMER_ID,
              MIN(APPLY_CODE) APPLY_CODE,
              MAX(BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(BANK_USER_NAME) BANK_USER_NAME,
              MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              CASE WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN        
                   MIN(POLICY_CODE)
              ELSE
                   NULL
              END POLICY_CODE,
              MAX(CERTI_CODE) CERTI_CODE,
              MAX(CERTI_TYPE) CERTI_TYPE,
              MAX(WITHDRAW_TYPE) WITHDRAW_TYPE,
              MAX(FEE_STATUS) FEE_STATUS,
              MAX(MONEY_CODE) MONEY_CODE,
              MAX(BANK_CODE) BANK_CODE,   
              MAX(A.ORGAN_CODE) ORGAN_CODE,
              MAX(PAYEE_PHONE) PAYEE_PHONE,                                
              MIN(PAYEE_NAME) PAYEE_NAME,                                    
              MIN(PAY_MODE) PAY_MODE,                                           
              MAX(ARAP_FLAG) ARAP_FLAG,                                         
              SUM(                                                                
                   CASE WHEN 
                      ARAP_FLAG ='${ar}' 
                       THEN 
                        FEE_AMOUNT 
                      ELSE 
                        -1*FEE_AMOUNT 
                       END
                     ) FEE_AMOUNT,                                            
              
              MIN(DUE_TIME) DUE_TIME,                               
              MAX(HOLDER_NAME)  HOLDER_NAME,                             
              MAX(INSURED_NAME)  INSURED_NAME,
              MAX(GROUP_CODE)  GROUP_CODE,                                 
              MAX(GROUP_NAME)  GROUP_NAME,
              MAX(FINISH_TIME) FINISH_TIME,
              MAX(STATISTICAL_DATE) STATISTICAL_DATE,
              MAX(AGENT_CODE) AGENT_CODE,
              MAX(FROZEN_STATUS) FROZEN_STATUS,
              (CASE WHEN A.DERIV_TYPE = '001' THEN MAX(U.USER_NAME) END) insert_name,
              MAX(SPECIAL_ACCOUNT_FLAG) SPECIAL_ACCOUNT_FLAG
        FROM APP___CAP__DBUSER.T_PREM_ARAP A
        LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U
        ON A.INSERT_BY = U.USER_ID
        WHERE 1=1 AND A.UNIT_NUMBER = #{unit_number}
        ]]>
        <if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
        <![CDATA[
        GROUP BY A.UNIT_NUMBER,A.DERIV_TYPE ) D 
         ]]>
    </select>
    
    <!-- 柜台收费内部转账信息查询 -->
    <select id="capQueryForInnerTrans" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
     SELECT D.* FROM
        (SELECT 
              A.UNIT_NUMBER UNIT_NUMBER,
              MAX(A.DERIV_TYPE) DERIV_TYPE,
              MIN(BUSINESS_CODE) BUSINESS_CODE,
              MAX(BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
              MIN(APPLY_CODE) APPLY_CODE,
              MAX(BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(BANK_USER_NAME) BANK_USER_NAME,
              MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              MAX(CERTI_CODE) CERTI_CODE,
              MAX(CERTI_TYPE) CERTI_TYPE,
              MAX(FEE_STATUS) FEE_STATUS,
              MAX(MONEY_CODE) MONEY_CODE,
              MAX(BANK_CODE) BANK_CODE,   
              MAX(ORGAN_CODE) ORGAN_CODE,
              MAX(PAYEE_PHONE) PAYEE_PHONE,
              MIN(PAYEE_NAME) PAYEE_NAME,                                    
              MIN(PAY_MODE) PAY_MODE,                                           
              CASE WHEN SUM(CASE WHEN ARAP_FLAG = '1' THEN
                              FEE_AMOUNT
                             ELSE
                              -1 * FEE_AMOUNT
                           END) >=0 THEN '1' ELSE '2' END  ARAP_FLAG,
              ABS(SUM( CASE WHEN   ARAP_FLAG ='1'
                       THEN   FEE_AMOUNT 
                      ELSE   -1* FEE_AMOUNT 
                       END )) FEE_AMOUNT,                                            
              MIN(DUE_TIME) DUE_TIME,                               
              MAX(HOLDER_NAME)  HOLDER_NAME
        FROM APP___CAP__DBUSER.T_PREM_ARAP A
        WHERE 1=1 AND A.DERIV_TYPE ='004' AND A.FEE_STATUS NOT IN ('02','16')
        ]]>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" business_code != null and business_code != '' "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
        <![CDATA[
        GROUP BY A.UNIT_NUMBER ) D WHERE 1=1 
         ]]>
         <if test=" arap_flag != null and arap_flag != '' "><![CDATA[ AND D.ARAP_FLAG = #{arap_flag} ]]></if>
        <if test=" fee_amount != null and fee_amount != '' "><![CDATA[ AND D.FEE_AMOUNT = #{fee_amount} ]]></if>
    </select>

    <!-- 收费信息录入查询结束 -->

    <select id="findPremArapByApplyPolicyCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID,  A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.GROUP_CODE, A.GROUP_NAME, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1   ]]>
        <include refid="queryPremArapByApplyCodeCondition" />
        <include refid="queryPremArapByPolicyCodeCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>

    <!-- 按照业务回退流水号条件查询开始 -->
    <select id="findPremArapByRollbackUnitNum" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select MAX(A.TASK_MARK) TASK_MARK, MAX(A.POLICY_ORGAN_CODE) POLICY_ORGAN_CODE, MAX(A.HOLDER_NAME) HOLDER_NAME,
            MAX(A.IS_ITEM_MAIN) IS_ITEM_MAIN, MAX(A.BOOKKEEPING_ID) BOOKKEEPING_ID,  MAX(A.BUSI_PROD_NAME) BUSI_PROD_NAME, 
            MAX(A.PAYEE_PHONE) PAYEE_PHONE, MAX(A.UNIT_NUMBER) UNIT_NUMBER, MAX(A.PAID_COUNT) PAID_COUNT, MAX(A.FEE_TYPE) FEE_TYPE,
             MAX(A.SEQ_NO) SEQ_NO, MAX(A.BUSI_PROD_CODE) BUSI_PROD_CODE, MAX(A.APPLY_CODE) APPLY_CODE, 
            MAX(A.FEE_STATUS_DATE) FEE_STATUS_DATE, MAX(A.ORGAN_CODE) ORGAN_CODE, MAX(A.RED_BOOKKEEPING_TIME) RED_BOOKKEEPING_TIME,
             MAX(A.CHANNEL_TYPE) CHANNEL_TYPE, MAX(A.IS_BANK_TEXT_DATE) IS_BANK_TEXT_DATE,
            MAX(A.CHARGE_YEAR) CHARGE_YEAR, MAX(A.IS_RISK_MAIN) IS_RISK_MAIN, MAX(A.IS_BANK_ACCOUNT) IS_BANK_ACCOUNT,
             MAX(A.FROZEN_STATUS) FROZEN_STATUS, MAX(A.POSTED) POSTED, MAX(A.BOOKKEEPING_FLAG) BOOKKEEPING_FLAG, 
            MAX(A.GROUP_ID) GROUP_ID, MAX(A.RED_BOOKKEEPING_BY) RED_BOOKKEEPING_BY, MAX(A.FROZEN_STATUS_DATE) FROZEN_STATUS_DATE,
             MAX(A.INSURED_NAME) INSURED_NAME, MAX(A.INSURED_ID) INSURED_ID, MAX(A.POLICY_TYPE) POLICY_TYPE, 
            MAX(A.PRODUCT_CHANNEL) PRODUCT_CHANNEL, MAX(A.IS_BANK_ACCOUNT_DATE) IS_BANK_ACCOUNT_DATE, MAX(A.POLICY_CODE) POLICY_CODE,
             MAX(A.PAY_MODE) PAY_MODE, MAX(A.BRANCH_CODE) BRANCH_CODE, MAX(A.VALIDATE_DATE) VALIDATE_DATE, MAX(A.BUSINESS_TYPE) BUSINESS_TYPE, 
            MAX(A.WITHDRAW_TYPE) WITHDRAW_TYPE, MAX(A.RED_BELNR) RED_BELNR, MAX(A.BANK_TEXT_STATUS) BANK_TEXT_STATUS,
             MAX(A.CERTI_TYPE) CERTI_TYPE, MAX(A.IS_BANK_TEXT_BY) IS_BANK_TEXT_BY, MAX(A.MONEY_CODE) MONEY_CODE, 
            MAX(A.BUSINESS_CODE) BUSINESS_CODE, MAX(A.IS_BANK_ACCOUNT_BY) IS_BANK_ACCOUNT_BY, MAX(A.PAYEE_NAME) PAYEE_NAME,
             MAX(A.BANK_ACCOUNT) BANK_ACCOUNT, MAX(A.RED_BOOKKEEPING_FLAG) RED_BOOKKEEPING_FLAG, MAX(A.FINISH_TIME) FINISH_TIME, 
            MAX(A.DUE_TIME) DUE_TIME, MAX(A.IS_BANK_TEXT) IS_BANK_TEXT, MAX(A.CERTI_CODE) CERTI_CODE, MAX(A.BUSI_APPLY_DATE) BUSI_APPLY_DATE,
             MAX(A.BELNR) BELNR, MAX(A.FEE_AMOUNT) FEE_AMOUNT, MAX(A.LIST_ID) LIST_ID, 
            MAX(A.HOLDER_ID) HOLDER_ID, MAX(A.WITHDRAW_TYPE) WITHDRAW_TYPE, MAX(A.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
             MAX(A.FAIL_TIMES) FAIL_TIMES, MAX(A.CUSTOMER_ID) CUSTOMER_ID, MAX(A.POLICY_YEAR) POLICY_YEAR, 
            MAX(A.FROZEN_STATUS_BY) FROZEN_STATUS_BY, MAX(A.BANK_USER_NAME) BANK_USER_NAME, MAX(A.FEE_STATUS) FEE_STATUS,
             MAX(A.FEE_STATUS_BY) FEE_STATUS_BY, MAX(A.PAY_END_DATE) PAY_END_DATE, MAX(A.DERIV_TYPE) DERIV_TYPE, 
            MAX(A.RED_BOOKKEEPING_ID) RED_BOOKKEEPING_ID, MAX(A.BOOKKEEPING_BY) BOOKKEEPING_BY, MAX(A.ARAP_FLAG) ARAP_FLAG,
             MAX(A.BANK_CODE) BANK_CODE, MAX(A.BOOKKEEPING_TIME) BOOKKEEPING_TIME, MAX(A.AGENT_CODE) AGENT_CODE, 
            MAX(A.PREM_FREQ) PREM_FREQ, MAX(A.GROUP_CODE) GROUP_CODE, MAX(A.GROUP_NAME) GROUP_NAME  FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1    ]]>
        <include refid="findPremArapByRollbackUnitNumCondition" />
        <![CDATA[  Group BY A.Unit_Number ]]>
    </select>
    <!-- 按照业务回退流水号条件查询结束 -->

    <!-- 新契约是否存在未到账数据开始 -->
    <select id="countNotFinished" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[
            select 
                 count(1)
             FROM 
                 APP___CAP__DBUSER.T_PREM_ARAP ARAP,
                 APP___CAP__DBUSER.T_FEE_STATUS STA 
             WHERE 
                 ARAP.FEE_STATUS = STA.STATUS_CODE AND 
                 STA.STATUS_NAME = '已确认'
         ]]>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND ARAP.APPLY_CODE = #{apply_code} ]]></if>
    </select>
    <!-- 新契约是否存在未到账数据结束 -->

    <!-- 新契约最近到账时间查询开始 -->
    <select id="recentFinishTimeForNB" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
         select 
             MAX(A.FINISH_TIME) finish_time    
         FROM 
             APP___CAP__DBUSER.T_PREM_ARAP A
         WHERE
             1 = 1
         ]]>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
    </select>
    <!-- 新契约最近到账时间查询结束 -->

    <select id="countPremArapByApplyCode" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[
         select 
             count(1)    
         FROM 
             APP___CAP__DBUSER.T_PREM_ARAP A
         WHERE
             1 = 1
         ]]>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
    </select>

    <!-- zht add ******** 服务接口根据unitNumber查询费用状态(不包含不可收付的信息) 开始 -->
    <select id="divAarapInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
             select 
         UNIT_NUMBER,
         POLICY_CODE,
         APPLY_CODE,
         DERIV_TYPE,
         WITHDRAW_TYPE,
         BUSINESS_CODE,
         BUSI_APPLY_DATE,
         FEE_TYPE,
         BUSI_PROD_NAME,
         INSURED_NAME,
         DUE_TIME,
         HOLDER_NAME,
         FEE_AMOUNT,
         FEE_STATUS,
         ORGAN_CODE,
         (select min(t.pay_mode)
                     from APP___CAP__DBUSER.t_Cash_Detail t
                    where t.unit_number = d.unit_number) PAY_MODE
      FROM APP___CAP__DBUSER.T_PREM_ARAP d
      WHERE ROWNUM = 1
       and FEE_STATUS != '16'
        ]]>
        <if test=" UnitNumbers != null and UnitNumbers.size>0">
            <![CDATA[ AND UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="UnitNumbers"
                open="(" separator="," close=")">
                #{item.unitNumber,
                jdbcType=VARCHAR}
            </foreach>
        </if>

    </select>
    <!-- zht add ******** 服务接口根据unitNumber查询费用状态 结束 -->

    <!-- yuxb add ******** 查询重复数据 开始 -->
    <!-- 新契约 -->
    <select id="NBSameRecords" resultType="java.util.Map"
        parameterType="java.util.Map">
    <![CDATA[ select  MAX(A.TASK_MARK) TASK_MARK, MAX(A.BUSINESS_CODE)BUSINESS_CODE, MAX(A.MONEY_CODE)MONEY_CODE, MAX(A.PAYEE_NAME)PAYEE_NAME, MAX(A.BANK_ACCOUNT)BANK_ACCOUNT, 
            MAX(A.PAYEE_PHONE)PAYEE_PHONE, A.UNIT_NUMBER, MAX(A.SEQ_NO)SEQ_NO, MAX(A.CHANNEL_TYPE)CHANNEL_TYPE, MAX(A.IS_BANK_TEXT)IS_BANK_TEXT, MAX(A.CERTI_CODE)CERTI_CODE, 
            MAX(A.GROUP_ID)GROUP_ID, MAX(A.CUSTOMER_ID)CUSTOMER_ID, MAX(A.POLICY_CODE)POLICY_CODE,MAX(A.DUE_TIME)DUE_TIME,ABS(SUM(CASE  WHEN ARAP_FLAG = #{ar} THEN
                              FEE_AMOUNT   ELSE
                              -1 * FEE_AMOUNT
                           END)) AS FEE_AMOUNT,
            MAX(A.BANK_USER_NAME) BANK_USER_NAME, MAX(A.DERIV_TYPE) DERIV_TYPE,  MAX(A.ARAP_FLAG)ARAP_FLAG, MAX(A.BRANCH_CODE)BRANCH_CODE, 
            MAX(A.BANK_CODE) BANK_CODE, MAX(A.BANK_TEXT_STATUS)BANK_TEXT_STATUS, MAX(A.CERTI_TYPE)CERTI_TYPE  from APP___CAP__DBUSER.T_PREM_ARAP A  
    	where (
	    	A.BRANCH_CODE,
	    	A.DUE_TIME, 
	    	A.POLICY_CODE, 
	    	A.APPLY_CODE, 
	    	A.BANK_CODE,
	        A.BANK_ACCOUNT
	       
        ) in
       (select  
	        NVL(A.BRANCH_CODE,0),
	    	NVL(A.DUE_TIME,SYSDATE), 
	    	NVL(A.POLICY_CODE,0), 
	    	NVL(A.APPLY_CODE,0), 
	    	NVL(A.BANK_CODE,0),
	        NVL(A.BANK_ACCOUNT,0)
	       
	      FROM  ( select  MAX(A.BRANCH_CODE) BRANCH_CODE,
		  MAX(A.DUE_TIME) DUE_TIME,
		  MAX(A.POLICY_CODE) POLICY_CODE,
		  MAX(A.APPLY_CODE) APPLY_CODE,
		  MAX(A.BANK_CODE) BANK_CODE,
		  MAX(A.BANK_ACCOUNT) BANK_ACCOUNT,
		  ABS(SUM(CASE   WHEN ARAP_FLAG = #{ar} THEN
                              FEE_AMOUNT   ELSE
                              -1 * FEE_AMOUNT
                           END)) AS FEE_AMOUNT   
         from APP___CAP__DBUSER.T_PREM_ARAP A WHERE  1=1 ]]>
        <include refid="findRecordsCondition" />
        <![CDATA[ GROUP BY A.UNIT_NUMBER ) A
        group by A.BRANCH_CODE,
                  A.DUE_TIME,
                  A.POLICY_CODE,
                  A.APPLY_CODE,
                  A.BANK_CODE,
                  A.BANK_ACCOUNT,
                  A.FEE_AMOUNT   having count(1) > 1 ) AND A.fee_status not in ('01','16') GROUP BY A.UNIT_NUMBER]]>
    </select>
    <!-- 续期 -->
    <select id="XQSameRecords" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
    select  MAX(A.TASK_MARK) TASK_MARK, MAX(A.BUSINESS_CODE)BUSINESS_CODE, MAX(A.MONEY_CODE)MONEY_CODE, MAX(A.PAYEE_NAME)PAYEE_NAME, MAX(A.BANK_ACCOUNT)BANK_ACCOUNT, 
            MAX(A.PAYEE_PHONE)PAYEE_PHONE, A.UNIT_NUMBER, MAX(A.SEQ_NO)SEQ_NO, MAX(A.CHANNEL_TYPE)CHANNEL_TYPE, MAX(A.IS_BANK_TEXT)IS_BANK_TEXT, MAX(A.CERTI_CODE)CERTI_CODE, 
            MAX(A.GROUP_ID)GROUP_ID, MAX(A.CUSTOMER_ID)CUSTOMER_ID, MAX(A.POLICY_CODE)POLICY_CODE,MAX(A.DUE_TIME)DUE_TIME,ABS(SUM(CASE  WHEN ARAP_FLAG = #{ar} THEN
                              FEE_AMOUNT   ELSE
                              -1 * FEE_AMOUNT
                           END)) AS FEE_AMOUNT,
            MAX(A.BANK_USER_NAME) BANK_USER_NAME, MAX(A.DERIV_TYPE) DERIV_TYPE,  MAX(A.ARAP_FLAG)ARAP_FLAG, MAX(A.BRANCH_CODE)BRANCH_CODE, 
            MAX(A.BANK_CODE) BANK_CODE, MAX(A.BANK_TEXT_STATUS)BANK_TEXT_STATUS, MAX(A.CERTI_TYPE)CERTI_TYPE  from APP___CAP__DBUSER.T_PREM_ARAP A  
	 WHERE (
		 A.BRANCH_CODE, 
		 A.DUE_TIME, 
		 A.POLICY_CODE, 
		 A.PAID_COUNT, 
		 A.BANK_CODE,
         A.BANK_ACCOUNT
       
         ) IN
       (select NVL(A.BRANCH_CODE,0),
               NVL(A.DUE_TIME,SYSDATE),
               NVL(A.POLICY_CODE,0),
               NVL(A.PAID_COUNT,0),
               NVL(A.BANK_CODE,0),
               NVL(A.BANK_ACCOUNT,0)
               
         FROM 
          ( select  MAX(A.BRANCH_CODE) BRANCH_CODE,
                    MAX(A.DUE_TIME) DUE_TIME,
                    MAX(A.POLICY_CODE) POLICY_CODE,
                    MAX(A.PAID_COUNT) PAID_COUNT,
		    MAX(A.BANK_CODE) BANK_CODE,
                    MAX(A.BANK_ACCOUNT) BANK_ACCOUNT,
                    ABS(SUM(CASE   WHEN ARAP_FLAG = #{ar} THEN
                              FEE_AMOUNT   ELSE
                              -1 * FEE_AMOUNT END)) AS FEE_AMOUNT
          FROM APP___CAP__DBUSER.T_PREM_ARAP A  WHERE 1=1 ]]>
        <include refid="findRecordsCondition" />
        <![CDATA[ GROUP BY A.UNIT_NUMBER ) A
        GROUP BY A.BRANCH_CODE,
                  A.DUE_TIME,
                  A.POLICY_CODE,
                  A.PAID_COUNT,
                  A.BANK_CODE,
                  A.BANK_ACCOUNT,
                  A.FEE_AMOUNT
        HAVING count(1) > 1 
        ) AND A.fee_status not in ('01','16') GROUP BY A.UNIT_NUMBER
       ]]>
    </select>
    <!-- 保全 -->
    <select id="BQSameRecords" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
   select  MAX(A.TASK_MARK) TASK_MARK, MAX(A.BUSINESS_CODE)BUSINESS_CODE, MAX(A.MONEY_CODE)MONEY_CODE, MAX(A.PAYEE_NAME)PAYEE_NAME, MAX(A.BANK_ACCOUNT)BANK_ACCOUNT, 
            MAX(A.PAYEE_PHONE)PAYEE_PHONE, A.UNIT_NUMBER, MAX(A.SEQ_NO)SEQ_NO, MAX(A.CHANNEL_TYPE)CHANNEL_TYPE, MAX(A.IS_BANK_TEXT)IS_BANK_TEXT, MAX(A.CERTI_CODE)CERTI_CODE, 
            MAX(A.GROUP_ID)GROUP_ID, MAX(A.CUSTOMER_ID)CUSTOMER_ID, MAX(A.POLICY_CODE)POLICY_CODE,MAX(A.DUE_TIME)DUE_TIME,ABS(SUM(CASE  WHEN ARAP_FLAG = #{ar} THEN
                              FEE_AMOUNT   ELSE
                              -1 * FEE_AMOUNT
                           END)) AS FEE_AMOUNT,
            MAX(A.BANK_USER_NAME) BANK_USER_NAME, MAX(A.DERIV_TYPE) DERIV_TYPE,  MAX(A.ARAP_FLAG)ARAP_FLAG, MAX(A.BRANCH_CODE)BRANCH_CODE, 
            MAX(A.BANK_CODE) BANK_CODE, MAX(A.BANK_TEXT_STATUS)BANK_TEXT_STATUS, MAX(A.CERTI_TYPE)CERTI_TYPE  from APP___CAP__DBUSER.T_PREM_ARAP A  
 	WHERE (
 		A.BRANCH_CODE, 
 		A.DUE_TIME, 
 		A.BUSINESS_CODE, 
 		A.BANK_CODE,
        A.BANK_ACCOUNT
       
        ) IN
       (select NVL(A.BRANCH_CODE,0),
               NVL(A.DUE_TIME,SYSDATE),
               NVL(A.BUSINESS_CODE,0),
               NVL(A.BANK_CODE,0),
               NVL(A.BANK_ACCOUNT,0)
              
        FROM 
          ( select  MAX(A.BRANCH_CODE) BRANCH_CODE,
                    MAX(A.DUE_TIME) DUE_TIME,
                    MAX(A.BUSINESS_CODE) BUSINESS_CODE,

		    MAX(A.BANK_CODE) BANK_CODE,
                    MAX(A.BANK_ACCOUNT) BANK_ACCOUNT,
                    ABS(SUM(CASE   WHEN ARAP_FLAG = #{ar} THEN FEE_AMOUNT   ELSE
                        -1 * FEE_AMOUNT END)) AS FEE_AMOUNT
          FROM APP___CAP__DBUSER.T_PREM_ARAP A  WHERE 1=1 ]]>
        <include refid="findRecordsCondition" />
      <![CDATA[   GROUP BY A.UNIT_NUMBER ) A
      GROUP BY A.BRANCH_CODE,
                  A.DUE_TIME,
                  A.BUSINESS_CODE,
                  A.BANK_CODE,
                  A.BANK_ACCOUNT,
                  A.FEE_AMOUNT
        HAVING COUNT(1) > 1) AND A.fee_status not in ('01','16') GROUP BY A.UNIT_NUMBER
     ]]>

    </select>

    <!-- 保单管理 -->
    <select id="PASameRecords" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
    select  MAX(A.TASK_MARK) TASK_MARK, MAX(A.BUSINESS_CODE)BUSINESS_CODE, MAX(A.MONEY_CODE)MONEY_CODE, MAX(A.PAYEE_NAME)PAYEE_NAME, MAX(A.BANK_ACCOUNT)BANK_ACCOUNT, 
            MAX(A.PAYEE_PHONE)PAYEE_PHONE, A.UNIT_NUMBER, MAX(A.SEQ_NO)SEQ_NO, MAX(A.CHANNEL_TYPE)CHANNEL_TYPE, MAX(A.IS_BANK_TEXT)IS_BANK_TEXT, MAX(A.CERTI_CODE)CERTI_CODE, 
            MAX(A.GROUP_ID)GROUP_ID, MAX(A.CUSTOMER_ID)CUSTOMER_ID, MAX(A.POLICY_CODE)POLICY_CODE,MAX(A.DUE_TIME)DUE_TIME,ABS(SUM(CASE  WHEN ARAP_FLAG = #{ar} THEN
                              FEE_AMOUNT   ELSE
                              -1 * FEE_AMOUNT
                           END)) AS FEE_AMOUNT,
            MAX(A.BANK_USER_NAME) BANK_USER_NAME, MAX(A.DERIV_TYPE) DERIV_TYPE,  MAX(A.ARAP_FLAG)ARAP_FLAG, MAX(A.BRANCH_CODE)BRANCH_CODE, 
            MAX(A.BANK_CODE) BANK_CODE, MAX(A.BANK_TEXT_STATUS)BANK_TEXT_STATUS, MAX(A.CERTI_TYPE)CERTI_TYPE  from APP___CAP__DBUSER.T_PREM_ARAP A  
 		WHERE (
 			A.DUE_TIME, 
 			A.POLICY_CODE
 			
 			) IN
       (select 
       		NVL(A.DUE_TIME,SYSDATE), 
       		NVL(A.POLICY_CODE,0)
       	
       		FROM 
          ( select  
                    MAX(A.DUE_TIME) DUE_TIME,
                    MAX(A.POLICY_CODE) POLICY_CODE,
                    ABS(SUM(CASE   WHEN ARAP_FLAG = #{ar} THEN FEE_AMOUNT   ELSE
                          -1 * FEE_AMOUNT END)) AS FEE_AMOUNT
          FROM APP___CAP__DBUSER.T_PREM_ARAP A  WHERE 1=1 ]]>
        <include refid="findRecordsCondition" />
       <![CDATA[  GROUP BY A.UNIT_NUMBER ) A
       GROUP BY A.DUE_TIME, A.POLICY_CODE, A.FEE_AMOUNT
        HAVING COUNT(1) > 1) AND A.fee_status not in ('01','16') GROUP BY A.UNIT_NUMBER
       ]]>

    </select>
    <!-- 理赔 -->
    <select id="LPSameRecords" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
   select  MAX(A.TASK_MARK) TASK_MARK, MAX(A.BUSINESS_CODE)BUSINESS_CODE, MAX(A.MONEY_CODE)MONEY_CODE, MAX(A.PAYEE_NAME)PAYEE_NAME, MAX(A.BANK_ACCOUNT)BANK_ACCOUNT, 
            MAX(A.PAYEE_PHONE)PAYEE_PHONE, A.UNIT_NUMBER, MAX(A.SEQ_NO)SEQ_NO, MAX(A.CHANNEL_TYPE)CHANNEL_TYPE, MAX(A.IS_BANK_TEXT)IS_BANK_TEXT, MAX(A.CERTI_CODE)CERTI_CODE, 
            MAX(A.GROUP_ID)GROUP_ID, MAX(A.CUSTOMER_ID)CUSTOMER_ID, MAX(A.POLICY_CODE)POLICY_CODE,MAX(A.DUE_TIME)DUE_TIME,ABS(SUM(CASE  WHEN ARAP_FLAG = #{ar} THEN
                              FEE_AMOUNT   ELSE
                              -1 * FEE_AMOUNT
                           END)) AS FEE_AMOUNT,
            MAX(A.BANK_USER_NAME) BANK_USER_NAME, MAX(A.DERIV_TYPE) DERIV_TYPE,  MAX(A.ARAP_FLAG)ARAP_FLAG, MAX(A.BRANCH_CODE)BRANCH_CODE, 
            MAX(A.BANK_CODE) BANK_CODE, MAX(A.BANK_TEXT_STATUS)BANK_TEXT_STATUS, MAX(A.CERTI_TYPE)CERTI_TYPE  from APP___CAP__DBUSER.T_PREM_ARAP A  
 		WHERE (
 			A.BRANCH_CODE, 
 			A.DUE_TIME,
 			A.BUSINESS_CODE, 
 			A.BANK_CODE,
        	A.BANK_ACCOUNT, 
        	
        	A.CUSTOMER_ID
        	) IN
       (select NVL(A.BRANCH_CODE,0),
               NVL(A.DUE_TIME,SYSDATE),
               NVL(A.BUSINESS_CODE,0),
               NVL(A.BANK_CODE,0),
               NVL(A.BANK_ACCOUNT,0),
             
               NVL(A.CUSTOMER_ID,0)
          FROM 
          ( select 	MAX(A.BRANCH_CODE) BRANCH_CODE,
                    MAX(A.DUE_TIME) DUE_TIME,
                    MAX(A.BUSINESS_CODE) BUSINESS_CODE,
                    MAX(A.BANK_CODE) BANK_CODE,
                    MAX(A.BANK_ACCOUNT) BANK_ACCOUNT,
                    ABS(SUM(CASE   WHEN ARAP_FLAG = #{ar} THEN
                              FEE_AMOUNT   ELSE
                              -1 * FEE_AMOUNT
                           END)) AS FEE_AMOUNT,
                    MAX(A.CUSTOMER_ID) CUSTOMER_ID FROM
          APP___CAP__DBUSER.T_PREM_ARAP A   WHERE 1=1 ]]>
        <include refid="findRecordsCondition" />
        <![CDATA[ GROUP BY A.UNIT_NUMBER ) A
        GROUP BY A.BRANCH_CODE,
                  A.DUE_TIME,
                  A.BUSINESS_CODE,
                  A.BANK_CODE,
                  A.BANK_ACCOUNT,
                  A.FEE_AMOUNT,
                  A.CUSTOMER_ID
        HAVING COUNT(1) > 1) AND A.fee_status not in ('01','16') GROUP BY A.UNIT_NUMBER
]]>
    </select>
    
    <select id="findDifferentRecordsForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        select C.* FROM (select ROWNUM RN, B.*
  FROM (select MAX(LIST_ID) LIST_ID,
               MAX(UNIT_NUMBER) UNIT_NUMBER,
               MAX(BUSINESS_CODE) BUSINESS_CODE,
               MAX(APPLY_CODE) APPLY_CODE,
               MAX(POLICY_CODE) POLICY_CODE,
               MAX(DERIV_TYPE) DERIV_TYPE,
               MAX(BUSINESS_TYPE) BUSINESS_TYPE,
               MAX(BRANCH_CODE) BRANCH_CODE,
               MAX(ORGAN_CODE) ORGAN_CODE,
               MAX(POLICY_ORGAN_CODE) POLICY_ORGAN_CODE,
               MAX(AGENT_CODE) AGENT_CODE,
               MAX(POLICY_TYPE) POLICY_TYPE,
               MAX(CHANNEL_TYPE) CHANNEL_TYPE,
               MAX(PRODUCT_CHANNEL) PRODUCT_CHANNEL,
               MAX(BUSI_APPLY_DATE) BUSI_APPLY_DATE,
               MAX(VALIDATE_DATE) VALIDATE_DATE,
               MAX(POLICY_YEAR) POLICY_YEAR,
               MAX(PREM_FREQ) PREM_FREQ,
               MAX(CHARGE_YEAR) CHARGE_YEAR,
               MAX(PAID_COUNT) PAID_COUNT,
               MAX(HOLDER_ID) HOLDER_ID,
               MAX(HOLDER_NAME) HOLDER_NAME,
               MAX(INSURED_ID) INSURED_ID,
               MAX(INSURED_NAME) INSURED_NAME,
               MAX(IS_RISK_MAIN) IS_RISK_MAIN,
               MAX(IS_ITEM_MAIN) IS_ITEM_MAIN,
               MAX(BUSI_PROD_CODE) BUSI_PROD_CODE,
               MAX(BUSI_PROD_NAME) BUSI_PROD_NAME,
               MAX(FEE_TYPE) FEE_TYPE,
               MAX(WITHDRAW_TYPE) WITHDRAW_TYPE,
               MAX(DUE_TIME) DUE_TIME,
               MAX(FINISH_TIME) FINISH_TIME,
               MAX(PAY_END_DATE) PAY_END_DATE,
               (CASE WHEN SUM(CASE
                      WHEN ARAP_FLAG = #{ar} THEN
                       FEE_AMOUNT - (CASE WHEN CUSTOMER_ACCOUNT_FLAG = #{CUSTOMER_ACCOUNT_FLAG} THEN NVL(CUS_ACC_FEE_AMOUNT, 0)
                         				ELSE 0
                         			END)
                      - NVL(POLICY_BALANCE, 0)
                      ELSE -1 * FEE_AMOUNT
                    END)<0 THEN #{ap} ELSE #{ar} END) ARAP_FLAG,
               MAX(CUSTOMER_ID) CUSTOMER_ID,
               MAX(PAYEE_NAME) PAYEE_NAME,
               MAX(CERTI_TYPE) CERTI_TYPE,
               MAX(CERTI_CODE) CERTI_CODE,
               MAX(PAYEE_PHONE) PAYEE_PHONE,
               MAX(BANK_CODE) BANK_CODE,
               MAX(BANK_ACCOUNT) BANK_ACCOUNT,
               MAX(BANK_USER_NAME) BANK_USER_NAME,
               MAX(IS_BANK_ACCOUNT) IS_BANK_ACCOUNT,
               MAX(IS_BANK_ACCOUNT_BY) IS_BANK_ACCOUNT_BY,
               MAX(IS_BANK_ACCOUNT_DATE) IS_BANK_ACCOUNT_DATE,
               MAX(MONEY_CODE) MONEY_CODE,
               ABS(SUM(CASE
                      WHEN ARAP_FLAG = #{ar} THEN
                       FEE_AMOUNT - (CASE
                         WHEN CUSTOMER_ACCOUNT_FLAG = #{CUSTOMER_ACCOUNT_FLAG} THEN
                          NVL(CUS_ACC_FEE_AMOUNT, 0)
                         ELSE
                          0
                       END)
                      - NVL(POLICY_BALANCE, 0)
                      ELSE
                       -1 * FEE_AMOUNT
                    END)) FEE_AMOUNT,
               MAX(PAY_MODE) PAY_MODE,
               MAX(ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
               MAX(FEE_STATUS) FEE_STATUS,
               MAX(FEE_STATUS_BY) FEE_STATUS_BY,
               MAX(FEE_STATUS_DATE) FEE_STATUS_DATE,
               MAX(FROZEN_STATUS) FROZEN_STATUS,
               MAX(FROZEN_STATUS_BY) FROZEN_STATUS_BY,
               MAX(FROZEN_STATUS_DATE) FROZEN_STATUS_DATE,
               MAX(IS_BANK_TEXT) IS_BANK_TEXT,
               MAX(FAIL_TIMES) FAIL_TIMES,
               MAX(GROUP_ID) GROUP_ID,
               MAX(SEQ_NO) SEQ_NO,
               MAX(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
               MAX(TASK_MARK) TASK_MARK,
               MAX(IS_BANK_TEXT_BY) IS_BANK_TEXT_BY,
               MAX(IS_BANK_TEXT_DATE) IS_BANK_TEXT_DATE,
               MAX(BOOKKEEPING_FLAG) BOOKKEEPING_FLAG,
               MAX(POSTED) POSTED,
               MAX(BOOKKEEPING_ID) BOOKKEEPING_ID,
               MAX(BELNR) BELNR,
               MAX(BOOKKEEPING_BY) BOOKKEEPING_BY,
               MAX(BOOKKEEPING_TIME) BOOKKEEPING_TIME,
               MAX(RED_BOOKKEEPING_FLAG) RED_BOOKKEEPING_FLAG,
               MAX(RED_BOOKKEEPING_ID) RED_BOOKKEEPING_ID,
               MAX(RED_BELNR) RED_BELNR,
               MAX(RED_BOOKKEEPING_BY) RED_BOOKKEEPING_BY,
               MAX(RED_BOOKKEEPING_TIME) RED_BOOKKEEPING_TIME,
               MAX(REFEFLAG) REFEFLAG
          FROM APP___CAP__DBUSER.T_PREM_ARAP A
			WHERE 1=1]]>
        		<include refid="findRecordsCondition" /> 
			<![CDATA[ 
			  GROUP BY unit_number
			  ORDER BY LIST_ID) B )C
			  WHERE C.RN <=#{LESS_NUM}
			  AND C.RN > #{GREATER_NUM}
			]]>
    </select>
    <!-- 更新本次制盘任务标识流水号EXISTS (select UNIT_NUMBER FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 
        B.UNIT_NUMBER -->
    <update id="updatePremArapForTaskMark" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
            <trim suffixOverrides=",">
                TASK_MARK = #{task_mark, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
            </trim>
        </set>
        <![CDATA[  WHERE  A.BANK_CODE = #{bank_code} AND A.ARAP_FLAG = #{arap_flag} AND rtrim(A.CHANNEL_TYPE) = #{channel_type} 
         AND A.DERIV_TYPE = #{deriv_type}  AND A.DUE_TIME >= to_date(#{bank_trans_start_time},'yyyy-MM-dd')  AND A.DUE_TIME <= to_date(#{bank_trans_end_time},'yyyy-MM-dd') 
         AND RTRIM(A.BANK_TEXT_STATUS) = #{bank_text_status}  AND A.PAY_MODE = #{pay_mode}  AND A.IS_BANK_TEXT = #{is_bank_text} 
         AND A.FEE_STATUS = #{fee_status} 
        ]]>
        <if test=" branchCodeList != null and branchCodeList.size()>0">
            <![CDATA[ AND A.BRANCH_CODE in   ]]>
            <foreach item="item" index="id" collection="branchCodeList"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </update>
    <!-- 集中制返盘手工制盘 保单号业务号非必录信息 无法根据固定条件进行更新 只能查询出所有数据遍历更新每一条 -->
    <!-- 更新本次制盘任务标识流水号EXISTS (select UNIT_NUMBER FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 
        B.UNIT_NUMBER -->
    <update id="updatePremArapForTaskMarkByOrganCode"
        parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
            <trim suffixOverrides=",">
                TASK_MARK = #{task_mark, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,
            </trim>
        </set>
        <![CDATA[  WHERE  A.BANK_CODE = #{bank_code} AND A.ARAP_FLAG = #{arap_flag}
         AND A.DERIV_TYPE = #{deriv_type}  AND A.DUE_TIME >= to_date(#{bank_trans_start_time},'yyyy-MM-dd')  AND A.DUE_TIME <= to_date(#{bank_trans_end_time},'yyyy-MM-dd') 
         AND RTRIM(A.BANK_TEXT_STATUS) = #{bank_text_status}  AND A.PAY_MODE = #{pay_mode}  AND A.IS_BANK_TEXT = #{is_bank_text} 
         AND A.FEE_STATUS = #{fee_status} AND A.ORGAN_CODE = #{organ_code}
        ]]>

    </update>
    <!-- 集中制返盘手工制盘 保单号业务号非必录信息 无法根据固定条件进行更新 只能查询出所有数据遍历更新每一条 -->
    <select id="findPremArapForHandWork" resultType="java.util.Map"
        parameterType="java.util.Map">
          <![CDATA[ 
          	select D.UNIT_NUMBER,ROWNUM RN 
          	FROM (select CASE WHEN SUM(CASE WHEN A.ARAP_FLAG = '${ar}' THEN FEE_AMOUNT ELSE -1 * FEE_AMOUNT END) > 0 THEN '${ar}' ELSE '${ap}' END FLAG, 
          	A.UNIT_NUMBER, 
          	MAX(A.BANK_CODE) BANK_CODE 
          	FROM APP___CAP__DBUSER.T_PREM_ARAP A 
          	WHERE 1 = 1
          ]]>
        <if test=" autofalg != null and autofalg !='' "><![CDATA[ AND TRUNC(NVL(A.BACK_TEXT_TIME, #{defultDate}), 'dd') <> TRUNC(#{autoDate}, 'dd') ]]></if>
        <include refid="findRecordsGetOrganDate" />
        <![CDATA[ 
        	GROUP BY A.UNIT_NUMBER ) D
        	WHERE 1=1
        ]]>
        <!-- 收费只查询本行数据，付费查询本行以及其他附属分行的数据 -->
        <if test=" arapFlag_bank != null and arapFlag_bank.size()>0">
            <![CDATA[ AND D.FLAG IN ]]>
            <foreach item="item" index="id" collection="arapFlag_bank"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <!-- 
        <choose>
        	<when test="apBankCodeList != null">
	        	<![CDATA[ AND D.BANK_CODE IN ]]>
	        	<foreach collection="apBankCodeList" item="item" index="id" open="(" separator="," close=")">
	        		#{item, jdbcType=VARCHAR}
	        	</foreach>
        	</when>
        	<when test="arBankCodeList != null">
	        	<![CDATA[ AND D.BANK_CODE IN ]]>
	        	<foreach collection="arBankCodeList" item="item" index="id" open="(" separator="," close=")">
	        		#{item, jdbcType=VARCHAR}
	        	</foreach>
        	</when>
        </choose>
         -->
    </select>
        <!-- 集中制返盘手工制盘 保单号业务号非必录信息 无法根据固定条件进行更新 只能查询出所有数据遍历更新每一条 查询总条数-->
    <select id="findPremArapForHandWorkTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
          <![CDATA[ 
          	select count(D.UNIT_NUMBER)
          	FROM (select CASE WHEN SUM(CASE WHEN A.ARAP_FLAG = '${ar}' THEN FEE_AMOUNT ELSE -1 * FEE_AMOUNT END) > 0 THEN '${ar}' ELSE '${ap}' END FLAG, 
          	ABS(SUM(CASE WHEN A.ARAP_FLAG = '${ar}' THEN
				                      A.FEE_AMOUNT - NVL(A.POLICY_BALANCE, 0)
				                     ELSE
				                      -1 * A.FEE_AMOUNT
				                   END)) as FEE_AMOUNT,
          	A.UNIT_NUMBER, 
          	MAX(A.BANK_CODE) BANK_CODE 
          	FROM APP___CAP__DBUSER.T_PREM_ARAP A 
          	WHERE 1 = 1
          ]]>
        <if test=" autofalg != null and autofalg !='' "><![CDATA[ AND TRUNC(NVL(A.BACK_TEXT_TIME, #{defultDate}), 'dd') <> TRUNC(#{autoDate}, 'dd') ]]></if>
        <include refid="findRecordsGetOrganDate" />
        <![CDATA[ 
        	GROUP BY A.UNIT_NUMBER ) D
        	WHERE 1=1 AND D.FEE_AMOUNT > 0
        ]]>
        <!-- 收费只查询本行数据，付费查询本行以及其他附属分行的数据 -->
        <if test=" arapFlag_bank != null and arapFlag_bank.size()>0">
            <![CDATA[ AND D.FLAG IN ]]>
            <foreach item="item" index="id" collection="arapFlag_bank"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    
    <!-- 集中制返盘手工制盘 根据应收付流水号更新制盘流水号 -->
    <update id="batchUpdatePremArapForTaskMarkByUnitNumber"
        parameterType="java.util.Map">      
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
        <![CDATA[TASK_MARK = #{task_mark,jdbcType=NUMERIC},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ]]>
        </set> 
        <![CDATA[ where A.UNIT_NUMBER= #{unit_number} ]]>
    </update>
        <!-- 集中制返盘手工制盘 更新制盘流水号 add 2017-4-10 -->
    <update id="batchUpdatePremArapForTaskMark"
        parameterType="java.util.Map" >      
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
        <![CDATA[TASK_MARK = #{task_mark_update,jdbcType=NUMERIC},
        	FEE_STATUS = #{fee_status_way},
        	FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            FEE_STATUS_DATE = SYSDATE ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ]]>
        </set> 
        <![CDATA[ WHERE A.UNIT_NUMBER in (]]>
         <![CDATA[ 
          	select D.UNIT_NUMBER
          	FROM (select CASE WHEN SUM(CASE WHEN A.ARAP_FLAG = '${ar}' THEN FEE_AMOUNT ELSE -1 * FEE_AMOUNT END) > 0 THEN '${ar}' ELSE '${ap}' END FLAG, 
          	A.UNIT_NUMBER, 
          	MAX(A.BANK_CODE) BANK_CODE 
          	FROM APP___CAP__DBUSER.T_PREM_ARAP A 
          	WHERE 1 = 1
          ]]>
        <if test=" autofalg != null and autofalg !='' "><![CDATA[ AND TRUNC(NVL(A.BACK_TEXT_TIME, #{defultDate}), 'dd') <> TRUNC(#{autoDate}, 'dd') ]]></if>
        <include refid="findRecordsGetOrganDate" />
        <![CDATA[ 
        	GROUP BY A.UNIT_NUMBER ) D
        	WHERE 1=1
        ]]>
        <!-- 收费只查询本行数据，付费查询本行以及其他附属分行的数据 -->
        <if test=" arapFlag_bank != null and arapFlag_bank.size()>0">
            <![CDATA[ AND D.FLAG IN ]]>
            <foreach item="item" index="id" collection="arapFlag_bank"
                open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <![CDATA[)]]>
        <if test="feeStatusList != null and feeStatusList.size()>0  ">
	        <![CDATA[ AND A.FEE_STATUS in ]]>
	        <foreach item="item" index="id" collection="feeStatusList"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>
        <if test="frozen_status != null and frozen_status != ''"><![CDATA[ AND NVL(A.FROZEN_STATUS, #{frozen_status_solution}) = #{frozen_status} ]]></if>
    </update>

    <!-- 重复数据记录打上标记 -->
    <update id="updatePremArapForIsBankText" parameterType="java.util.Map">      
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
        	<![CDATA[A.IS_BANK_TEXT= #{is_bank_text, jdbcType=NUMERIC} ,A.TASK_MARK = #{task_mark, jdbcType=NUMERIC},A.FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR},]]>
        	<if test=" fee_status !=null and fee_status != '' and bank_text_status != null and bank_text_status != '' ">
        	<![CDATA[A.FEE_STATUS =#{fee_status,jdbcType=VARCHAR},A.BANK_TEXT_STATUS =#{bank_text_status,jdbcType=VARCHAR }, ]]>
        	</if>
			<![CDATA[A.UPDATE_TIME = SYSDATE,
            A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP]]>
        </set> 
        <![CDATA[ where A.UNIT_NUMBER= #{unit_number}]]>
    </update>
    
    <!--  -->
    <update id="updateByFeeStatic" parameterType="java.util.Map">      
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
        	<![CDATA[A.FEE_STAUTS = #{fee_status},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP]]>
        </set> 
        <![CDATA[ where A.UNIT_NUMBER= #{unit_number}]]>
    </update>

    <!-- 查询总数 -->
    <select id="findDifferentRecordsCounts" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
    select COUNT(1)
  FROM (select UNIT_NUMBER
          FROM APP___CAP__DBUSER.T_PREM_ARAP A
         WHERE 1=1  ]]>
        <include refid="findRecordsCondition" />
           <![CDATA[
          GROUP BY unit_number)
        ]]>
    </select>

    <!-- 修改arap的序列号 -->
    <update id="updateArapSeqNo" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
        <![CDATA[A.SEQ_NO= #{seq_no} ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP]]>
        </set> 
        <![CDATA[ where A.UNIT_NUMBER= #{unit_number}
]]>
    </update>

    <!-- yuxb 查询不重复数据结束 -->

    <select id="findPremArapForGL" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        select B.* FROM (
            select ROWNUM RN ,A.* FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE ROWNUM <= #{end_num} 
            ]]>
        <if test=" date_formatter!=null and date_formatter!= '' and statistical_date != null and statistical_date != '' ">
            <![CDATA[ AND A.STATISTICAL_DATE >= TO_DATE(to_char(#{statistical_date},#{date_formatter}),#{date_formatter})
            		  AND A.STATISTICAL_DATE < TO_DATE(to_char(#{statistical_date},#{date_formatter}),#{date_formatter})+1
             ]]>
        </if>
        <if test=" bookkeeping_flag!= null and bookkeeping_flag != '' ">
            <![CDATA[ AND A.BOOKKEEPING_FLAG = #{bookkeeping_flag}]]>
        </if>
        <if test=" posted != null and posted != '' "><![CDATA[ AND A.POSTED IN ('01','04') ]]></if>
        <![CDATA[ ) B WHERE B.RN>=#{start_num}   ]]>
    </select>
    <select id="findPremArapForGLTotalCount" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
            select count(1) FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1=1
            ]]>
        <if test=" date_formatter!=null and date_formatter!= '' and  staDate != null and staDate != '' ">
            <![CDATA[  AND A.STATISTICAL_DATE = to_date(#{staDate},#{date_formatter}) ]]>
        </if>
        <if test=" bookkeeping_flag!= null and bookkeeping_flag != '' ">
            <![CDATA[ AND A.BOOKKEEPING_FLAG = #{bookkeeping_flag}]]>
        </if>
        <if test=" posted != null and posted != '' "><![CDATA[ AND A.POSTED IN ('01','04') ]]></if>
    </select>

    <!-- 修改操作 -->
    <update id="updatePremArapBySeqNo" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            <trim suffixOverrides=",">
                BANK_TEXT_STATUS =#{bank_text_status, jdbcType=VARCHAR} ,
                FEE_STATUS=#{fee_status,jdbcType=VARCHAR},
                FAIL_TIMES = nvl(FAIL_TIMES,0) + 1,
				BACK_TEXT_TIME = SYSDATE  ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP                
            </trim>
            <!--  
                IS_BANK_TEXT = (case when FAIL_TIMES = #{fail_times, jdbcType=NUMERIC} then
				                #{is_bank_text,jdbcType=NUMERIC}
				                	else
				                IS_BANK_TEXT
				                end)
			-->
        </set>
        <![CDATA[ WHERE SEQ_NO = #{seq_no} ]]>
    </update>

    <!-- 根据盘文件下的具体的盘明细的seq_no 更新制盘状态 -->
    <update id="updatePABySeqNoForBankTextStat" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            <trim suffixOverrides=",">
                BANK_TEXT_STATUS =
                #{bank_text_status, jdbcType=VARCHAR} ,
                FEE_STATUS=#{fee_status,jdbcType=VARCHAR},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
            </trim>
        </set>
        <![CDATA[ WHERE SEQ_NO = #{seq_no} ]]>
    </update>

    <!-- 修改操作 返盘成功 并且该明细扣款成功 -->
    <update id="updatePremArapForSuc" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            BANK_TEXT_STATUS = #{bank_text_status, jdbcType=VARCHAR},
             <if test="bank_text_status != null and bank_text_status == 8 ">
                FAIL_TIMES = NVL(FAIL_TIMES,0) + 1,
             </if>
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE SEQ_NO = #{seq_no} ]]>
    </update>

    <!-- 修改操作 返盘成功 并且该明细扣款失败 原因是由于账号不存在 或者 账号信息有误 -->
    <update id="updatePremArapForAccountFail" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            <trim suffixOverrides=",">
                BANK_TEXT_STATUS = #{bank_text_status, jdbcType=VARCHAR} ,
                FEE_STATUS = #{fee_status,jdbcType=VARCHAR},
                FAIL_TIMES = nvl(FAIL_TIMES,0) + 1,
                BACK_TEXT_TIME = SYSDATE ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
            </trim>
            <!-- 
                IS_BANK_ACCOUNT = #{is_bank_account,
                jdbcType=NUMERIC} ,

                IS_BANK_TEXT = (case
                when FAIL_TIMES =
                #{fail_times, jdbcType=NUMERIC} then
                #{is_bank_text,
                jdbcType=NUMERIC}
                else
                IS_BANK_TEXT
                end)
             -->
        </set>
        <![CDATA[ WHERE SEQ_NO = #{seq_no} ]]>
    </update>

    <!-- 修改操作 返盘成功 并且该明细扣款失败 原因是由于账号不存在 或者 账号信息有误 -->
    <update id="updatePremArapBySeqNoAndFeeStatus" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            <trim suffixOverrides=",">
                <if test="funds_rtn_code!=null and funds_rtn_code != ''">
                FUNDS_RTN_CODE = #{funds_rtn_code,jdbcType=VARCHAR},
                 </if>
                FEE_STATUS = #{update_fee_status,jdbcType=VARCHAR} ,
                FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
                FEE_STATUS_DATE = SYSDATE ,
                FINISH_TIME =#{finish_time,jdbcType=TIMESTAMP} ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,
            </trim>
        </set>
        <![CDATA[ WHERE SEQ_NO = #{seq_no}  ]]>
        <if test="fee_status != null and fee_status != '' ">
         <![CDATA[ AND FEE_STATUS <> #{fee_status} ]]>
         </if>
    </update>
    <!-- 根据seqNoList批量修改fee_status状态 wugz_wb -->
    <update id="updatePremArapBySeqNoes" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            <trim suffixOverrides=",">
                FEE_STATUS = #{update_fee_status,jdbcType=VARCHAR} ,
                FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
                FEE_STATUS_DATE = SYSDATE ,
                update_timestamp=SYSDATE,
                update_time=SYSDATE,
                FINISH_TIME =#{finish_time,jdbcType=TIMESTAMP} ,
            </trim>
        </set>
        <![CDATA[ WHERE SEQ_NO IN ( select　SEQ_NO FROM APP___CAP__DBUSER.T_BANK_TEXT_DETAIL TD WHERE TD.SEND_ID = #{sendId}
        AND  TD.BANK_TEXT_STATUS = #{bank_text_status_succ} )  ]]>
    </update>
    
    <update id="updatePremArapBySeqNoFeeStatus" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            <trim suffixOverrides=",">
                FEE_STATUS = #{update_fee_status,
                jdbcType=VARCHAR} ,
                bank_text_status = #{bank_text_status,jdbcType=VARCHAR},
                FEE_STATUS_DATE = SYSDATE ,
                FINISH_TIME =
                #{finish_time,
                jdbcType=TIMESTAMP} ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,
            </trim>
        </set>
        <![CDATA[ WHERE SEQ_NO = #{seq_no} ]]>
    </update>
    

    <update id="updatePremArapToDetachCash" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            <trim suffixOverrides=",">
                IS_BANK_TEXT =
                #{is_bank_text,jdbcType=NUMERIC} ,
                BANK_TEXT_STATUS =
                #{bank_text_status,jdbcType=VARCHAR} ,
                GROUP_ID = null ,
                SEQ_NO = null ,
                TASK_MARK = null ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,
            </trim>
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number} ]]>
    </update>

    <select id="findPremArapBySeqNo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            select A.CIP_BANK_CODE, A.TASK_MARK,A.SERVICE_CODE,A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN,  A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, A.BUSI_ITEM_ID,
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.IS_CORPORATE,
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE,A.OPERATOR_BY ,
            A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE,NVL(DECODE(R.FILTER2,'Y',R.CR_SEG4,R.DR_SEG4),'DR_NULL') DR_ACCUITEM, NVL(DECODE(R.FILTER2,'Y',R.DR_SEG4,R.CR_SEG4),'CR_NULL') CR_ACCUITEM, A.SPECIAL_ACCOUNT_FLAG,
            (CASE WHEN A.DERIV_TYPE = '001' THEN (SELECT MAX(U.USER_NAME) FROM APP___CAP__DBUSER.T_UDMP_USER U WHERE U.USER_ID = A.INSERT_BY) END) insert_name,
            (select c.cash_date from APP___CAP__DBUSER.T_CASH_INPUT_INFO C,APP___CAP__DBUSER.T_BILL_DETAILS B where A.Unit_Number=b.unit_number and b.bill_main_id=c.list_id) cashDate
			FROM APP___CAP__DBUSER.T_PREM_ARAP A LEFT JOIN APP___CAP__DBUSER.T_GL_ACCOUNTING_RULE R ON R.FEE_TABLE = '2' AND  A.FEE_TYPE = R.BASE1 AND A.WITHDRAW_TYPE = R.BASE2
 			WHERE 1 = 1 
            ]]>
        <if test=" seq_no != null and seq_no != '' "><![CDATA[ AND A.SEQ_NO = #{seq_no, jdbcType=NUMERIC} ]]></if>
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ]]></if>
    </select>

	<!-- 资金推送挂起 -->
    <update id="updatePremArapForFrozenStatusByNoRealTIme" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP T ]]>
        <set>
            FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} ,
            FROZEN_STATUS_BY = #{update_by, jdbcType=NUMERIC},
            FROZEN_STATUS_DATE = SYSDATE
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number} AND (FEE_STATUS IN ('00','03','20','21')
        OR EXISTS (SELECT 1 FROM APP___CAP__DBUSER.T_BILL_DETAILS A WHERE A.UNIT_NUMBER = T.UNIT_NUMBER AND T.FEE_STATUS = '15' AND A.BILL_MODE = '10') )]]>
        
    </update>
    
    <select id="findToCashAttr" parameterType="java.util.Map"
        resultType="java.util.Map">
        <![CDATA[select (CASE
         WHEN SUM(CASE
                    WHEN ARAP_FLAG = #{ar} THEN
                     FEE_AMOUNT - (CASE
                       WHEN CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} THEN
                        NVL(CUS_ACC_FEE_AMOUNT, 0)
                       ELSE
                        0
                     END) - NVL(POLICY_BALANCE, 0)
                    ELSE
                     -1 * FEE_AMOUNT
                  END) < 0 THEN
          #{ap}
         ELSE
          #{ar}
       END) ARAP_FLAG,
       MAX(PAYEE_NAME) PAYEE_NAME,
       MAX(CERTI_TYPE) CERTI_TYPE,
       MAX(CERTI_CODE) CERTI_CODE,
       MAX(PAYEE_PHONE) PAYEE_PHONE,
       MAX(BANK_CODE) BANK_CODE,
       MAX(BANK_ACCOUNT) BANK_ACCOUNT,
       MAX(BANK_USER_NAME) BANK_USER_NAME,
       ABS(SUM(CASE
                 WHEN ARAP_FLAG = #{ar} THEN
                  FEE_AMOUNT - (CASE
                    WHEN CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} THEN
                     NVL(CUS_ACC_FEE_AMOUNT, 0)
                    ELSE
                     0
                  END) - NVL(POLICY_BALANCE, 0)
                 ELSE
                  -1 * FEE_AMOUNT
               END)) FEE_AMOUNT,
       MAX(PAY_MODE) PAY_MODE,
       MAX(FEE_STATUS) FEE_STATUS,
       MAX(GROUP_ID) GROUP_ID,
       MAX(SEQ_NO) SEQ_NO,
       MAX(TASK_MARK) TASK_MARK
  FROM APP___CAP__DBUSER.t_prem_arap 
  WHERE 1 = 1 ]]>
        <if test=" seq_no != null and seq_no != '' "><![CDATA[ AND SEQ_NO = #{seq_no, jdbcType=NUMERIC} ]]></if>
    </select>
    <update id="updatePremArapForGlLoad" parameterType="java.util.Map">
        <![CDATA[
                UPDATE APP___CAP__DBUSER.T_PREM_ARAP T
                   SET (BOOKKEEPING_ID, POSTED, BOOKKEEPING_TIME,UPDATE_TIME,UPDATE_TIMESTAMP) =
                       (select BOOKKEEPING_ID, '02', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                          FROM APP___CAP__DBUSER.T_PREM_ARAP_GL GL
                         WHERE GL.BOOKKEEPING_ID = T.LIST_ID)
                 WHERE EXISTS (select 1
                          FROM APP___CAP__DBUSER.T_PREM_ARAP_GL GL
                         WHERE GL.BOOKKEEPING_ID = T.LIST_ID)
        ]]>
        <if test="business_code != null and business_code != '' ">
            <![CDATA[ 
                      AND T.BUSINESS_CODE = #{business_code}
             ]]>
        </if>
        <if test="statistical_date != null and statistical_date != '' ">
            <![CDATA[ 
                      AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{statistical_date}, 'YYYYMMDD'), 'YYYYMMDD') + 1
             ]]>
        </if>
        <if test="statistical_date_start != null and statistical_date_start != '' ">
            <![CDATA[ 
                      AND T.STATISTICAL_DATE >= TO_DATE(TO_CHAR(#{statistical_date_start}, 'YYYYMMDD'), 'YYYYMMDD')
             ]]>
        </if>
        <if test="branch_code_list != null and branch_code_list.size()>0  ">
	        <![CDATA[ AND T.BRANCH_CODE IN ]]>
	        <foreach item="item" index="id" collection="branch_code_list"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>
    </update>
    
    <update id="updatePremArapPlanForGlLoad" parameterType="java.util.Map">
        <![CDATA[
                UPDATE APP___CAP__DBUSER.T_PREM_ARAP_PLAN T
                   SET (BOOKKEEPING_ID, POSTED, UPDATE_TIME,UPDATE_TIMESTAMP) =
                       (select BOOKKEEPING_ID, '02', TRUNC(SYSDATE),CURRENT_TIMESTAMP
                          FROM APP___CAP__DBUSER.T_PREM_ARAP_GL GL
                         WHERE GL.BOOKKEEPING_ID = T.LIST_ID)
                 WHERE EXISTS (select 1
                          FROM APP___CAP__DBUSER.T_PREM_ARAP_GL GL
                         WHERE GL.BOOKKEEPING_ID = T.LIST_ID)
        ]]>
        <if test="business_code != null and business_code != '' ">
            <![CDATA[ 
                      AND T.BUSINESS_CODE = #{business_code}
             ]]>
        </if>
        <if test="branch_code_list != null and branch_code_list.size()>0  ">
	        <![CDATA[ AND T.BRANCH_CODE IN ]]>
	        <foreach item="item" index="id" collection="branch_code_list"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>
    </update>
    
    <update id="updatePremArapForGlLoadYSWS" parameterType="java.util.Map">
        <![CDATA[
                UPDATE APP___CAP__DBUSER.T_PREM_ARAP T
                   SET POSTED = '04', BOOKKEEPING_TIME = CURRENT_TIMESTAMP,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
                 WHERE (T.DERIV_TYPE = '003'
                 	AND T.WITHDRAW_TYPE = '0030101000'
                    AND T.DUE_TIME < TO_DATE(TO_CHAR(#{statistical_date}, 'YYYYMMDD'), 'YYYYMMDD') + 1
                    AND T.PAY_END_DATE >= TO_DATE(TO_CHAR(#{statistical_date}, 'YYYYMMDD'), 'YYYYMMDD')
                    AND (
                        T.FEE_STATUS IN ('00','03','04','15','20')
	                    OR (
	                        T.FEE_STATUS = '01' 
	                        AND T.FINISH_TIME >= TO_DATE(TO_CHAR(#{statistical_date}, 'YYYYMMDD'), 'YYYYMMDD') + 1
	                        )
	                    )
                    AND T.BOOKKEEPING_FLAG = '1'
                    AND (
                        T.BOOKKEEPING_TIME IS NULL
                        OR TO_CHAR(T.BOOKKEEPING_TIME, 'YYYYMM') != TO_CHAR(SYSDATE, 'YYYYMM')
                        )
                    AND T.ARAP_FLAG = '1'
                    ]]>
	                <if test="branch_code_list != null and branch_code_list.size()>0  ">
				        <![CDATA[ AND T.BRANCH_CODE IN ]]>
				        <foreach item="item" index="id" collection="branch_code_list"
				                open="(" separator="," close=")">
				                #{item, jdbcType=VARCHAR}
				        </foreach>
			        </if>
			        <if test="business_code != null and business_code != '' ">
			            <![CDATA[ 
			                     AND T.BUSINESS_CODE = #{business_code}
			            ]]>
			        </if>
	                <![CDATA[
                    )
                    OR (
                    T.DERIV_TYPE = '003'
                    AND T.FEE_STATUS = '01'
                    AND T.FINISH_TIME < TO_DATE(TO_CHAR(#{statistical_date}, 'YYYYMMDD'), 'YYYYMMDD') + 1
                    AND T.BOOKKEEPING_FLAG = '1'
                    AND (
                        T.BOOKKEEPING_TIME IS NULL
                        OR TO_CHAR(T.BOOKKEEPING_TIME, 'YYYYMM') != TO_CHAR(SYSDATE, 'YYYYMM')
                        )
                    AND T.STATISTICAL_DATE IS NULL
                    AND T.ARAP_FLAG = '1'
                    ]]>
	                <if test="branch_code_list != null and branch_code_list.size()>0  ">
				        <![CDATA[ AND T.BRANCH_CODE IN ]]>
				        <foreach item="item" index="id" collection="branch_code_list"
				                open="(" separator="," close=")">
				                #{item, jdbcType=VARCHAR}
				        </foreach>
			        </if>
			        <if test="business_code != null and business_code != '' ">
			            <![CDATA[ 
			                     AND T.BUSINESS_CODE = #{business_code}
			            ]]>
			        </if>
                    )
	                <![CDATA[
        ]]>
    </update>
    <update id="updatePremArapBelnr" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            BELNR = #{belnr, jdbcType=VARCHAR},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE BOOKKEEPING_ID = #{bookkeeping_id}]]>
    </update>
    
    <!-- 收付费变更调整使用 -->
    <select id="findPremArapByUnitnumber" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select SUM(case when t.arap_flag = '1' then t.fee_amount 
                                  when t.arap_flag = '2' then - 1 * t.fee_amount 
                                  end) fee__amount 
                    FROM APP___CAP__DBUSER.T_PREM_ARAP t WHERE 1 = 1 
                     AND t.UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} 
                     ]]>
    </select>
    

    <!-- 方式变更修改应收信息zht add 20150806 -->
    <update id="updatePremArapPaymode" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
        <if test=" customer_id != null and customer_id != '' ">
        <![CDATA[ CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC}, ]]></if>
        <if test=" payee_name != null and payee_name != '' ">
        <![CDATA[   PAYEE_NAME = #{payee_name, jdbcType=VARCHAR}, ]]></if>
        <if test=" certi_type != null and certi_type != '' ">   
        <![CDATA[   CERTI_TYPE = #{certi_type, jdbcType=VARCHAR}, ]]></if>
        <if test=" certi_code != null and certi_code != '' ">       
        <![CDATA[   CERTI_CODE = #{certi_code, jdbcType=VARCHAR}, ]]></if>
        <if test=" payee_phone != null and payee_phone != '' ">       
        <![CDATA[  PAYEE_PHONE = #{payee_phone, jdbcType=VARCHAR},]]></if>
        <if test=" cip_district_bank_code != null and cip_district_bank_code != '' ">       
        <![CDATA[  CIP_DISTRICT_BANK_CODE = #{cip_district_bank_code, jdbcType=VARCHAR},]]></if>
        <if test=" is_corporate != null and is_corporate != '' ">       
        <![CDATA[  IS_CORPORATE = #{is_corporate, jdbcType=VARCHAR},]]></if>
        <if test=" is_bank_text != null and is_bank_text != '' ">       
        <![CDATA[  IS_BANK_TEXT = #{is_bank_text, jdbcType=NUMERIC},]]></if>
            BANK_CODE = #{bank_code,
            jdbcType=VARCHAR},
            BANK_ACCOUNT = #{bank_account,
            jdbcType=VARCHAR},
            BANK_USER_NAME = #{bank_user_name,
            jdbcType=VARCHAR},
            PAY_MODE = #{pay_mode, jdbcType=VARCHAR},
            FAIL_TIMES =#{fail_times, jdbcType=NUMERIC},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
            <if test="pay_end_date != null">
                 ,PAY_END_DATE = #{pay_end_date, jdbcType=DATE}
            </if>
            <if test="holder_name != null">
                ,holder_name = #{holder_name, jdbcType=VARCHAR}
            </if>
            <if test="holder_id != null">
                ,holder_id = #{holder_id, jdbcType=NUMERIC}
            </if>
<!-- 允许制盘中修改收付费信息，但是不能修改收付费状态            FEE_STATUS=#{fee_status,jdbcType=VARCHAR} -->
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number}]]>
        <if test="feeStatusList != null and feeStatusList.size()>0  ">
	        <![CDATA[ AND rtrim(FEE_STATUS) in ]]>
	        <foreach item="item" index="id" collection="feeStatusList"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>
    </update>
    <!-- 方式变更修改应收信息zht add 20150806 -->
    <update id="updatePremArapBookKeeping" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            BOOKKEEPING_FLAG = #{bookkeeping_flag, jdbcType=NUMERIC},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number}]]>
    </update>
    
    <!-- 通过赔案号查询收付费装态-->
    <select id="queryPremStatusByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
		  	   SELECT A.POLICY_CODE, A.BUSINESS_CODE, A.FEE_STATUS
     FROM APP___CAP__DBUSER.T_PREM_ARAP A
    WHERE A.FEE_STATUS NOT IN ('01', '19','02')
      AND A.BUSINESS_CODE IN ]]>
       <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item}
       </foreach>
		
	</select>
    
    <!-- 通过unit_number查询费用状态-->
    <select id="queryFeeTypeByUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
		  	   SELECT A.POLICY_CODE, A.BUSINESS_CODE, A.FEE_STATUS, A.UNIT_NUMBER
     FROM APP___CAP__DBUSER.T_PREM_ARAP A
    WHERE A.FEE_STATUS IN ('00', '04')
      AND A.UNIT_NUMBER IN ]]>
       <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
        #{item.data.unit_number,jdbcType=VARCHAR}
       </foreach>
		
	</select>	
    
    <!-- 通过保单号查询续期交费期间和保全收付费银行划款期间-->
    <select id="queryRenewalBypolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
		SELECT A.POLICY_CODE,
		          A.BUSINESS_CODE,
		          A.UNIT_NUMBER,
		          A.BUSINESS_TYPE,
		          A.FEE_STATUS,
		          A.DERIV_TYPE
		     FROM APP___CAP__DBUSER.T_PREM_ARAP A
		    WHERE A.FEE_STATUS = '04'
		      AND A.ARAP_FLAG='1'
		      AND A.BUSINESS_TYPE IN ('4003','1005') 
		      AND A.DERIV_TYPE = '003'
		      AND A.POLICY_CODE=#{policy_code,jdbcType=VARCHAR}
		]]>
	</select>
    
    <!-- 月末计提数据查询 -->
    <select id="findPremArapForGlMonthly" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        select B.* FROM (
            select ROWNUM RN ,A.* FROM (
                select * FROM (select LIST_ID,
                    UNIT_NUMBER,
                    BUSINESS_CODE,
                    APPLY_CODE,
                    POLICY_CODE,
                    BATCH_NO,
                    DERIV_TYPE,
                    BRANCH_CODE,
                    ORGAN_CODE,
                    POLICY_ORGAN_CODE,
                    AGENT_CODE,
                    POLICY_TYPE,
                    CHANNEL_TYPE,
                    PRODUCT_CHANNEL,
                    BUSI_APPLY_DATE,
                    VALIDATE_DATE,
                    POLICY_YEAR,
                    PREM_FREQ,
                    CHARGE_YEAR,
                    PAID_COUNT,
                    HOLDER_ID,
                    HOLDER_NAME,
                    INSURED_ID,
                    INSURED_NAME,
                    IS_RISK_MAIN,
                    IS_ITEM_MAIN,
                    GROUP_CODE,
                    GROUP_NAME,
                    BUSI_PROD_CODE,
                    BUSI_PROD_NAME,
                    FEE_TYPE,
                    WITHDRAW_TYPE,
                    REFEFLAG,
                    DUE_TIME,
                    FINISH_TIME,
                    PAY_END_DATE,
                    ARAP_FLAG,
                    CUSTOMER_ID,
                    PAYEE_NAME,
                    CERTI_TYPE,
                    CERTI_CODE,
                    PAYEE_PHONE,
                    BANK_CODE,
                    BANK_ACCOUNT,
                    BANK_USER_NAME,
                    CUSTOMER_ACCOUNT_FLAG,
                    IS_BANK_ACCOUNT,
                    IS_BANK_ACCOUNT_BY,
                    IS_BANK_ACCOUNT_DATE,
                    MONEY_CODE,
                    FEE_AMOUNT,
                    PAY_MODE,
                    ROLLBACK_UNIT_NUMBER,
                    FEE_STATUS,
                    FEE_STATUS_BY,
                    FEE_STATUS_DATE,
                    FROZEN_STATUS,
                    FROZEN_STATUS_BY,
                    FROZEN_STATUS_DATE,
                    IS_BANK_TEXT,
                    FAIL_TIMES,
                    GROUP_ID,
                    SEQ_NO,
                    BANK_TEXT_STATUS,
                    TASK_MARK,
                    IS_BANK_TEXT_BY,
                    IS_BANK_TEXT_DATE,
                    BOOKKEEPING_FLAG,
                    BOOKKEEPING_ID,
                    BELNR,
                    BOOKKEEPING_BY,
                    BOOKKEEPING_TIME,
                    RED_BOOKKEEPING_FLAG,
                    RED_BOOKKEEPING_ID,
                    RED_BELNR,
                    RED_BOOKKEEPING_BY,
                    RED_BOOKKEEPING_TIME,
                    SERVICE_CODE,
                    OPERATOR_BY,
                    INSERT_BY,
                    UPDATE_BY,
                    INSERT_TIME,
                    UPDATE_TIME,
                    INSERT_TIMESTAMP,
                    UPDATE_TIMESTAMP,
                    BUSINESS_TYPE,
                    FUNDS_RTN_CODE,
                    CUS_ACC_FEE_AMOUNT,
                    CUS_ACC_DETAILS_ID,
                    CUS_ACC_UPDATE_BY,
                    CUS_ACC_UPDATE_TIME,
                    CIP_BANK_CODE,
                    CIP_DISTRICT_BANK_CODE,
                    CIP_BRANCH_BANK_CODE,
                    AUDIT_DATE,
                    STATISTICAL_DATE,
                    BANK_DEAL_DATE,
                    IS_SPLIT,
                    IS_SEND,
                    TAX_RATE,
                    AGENT_NAME,
                    AREA,
                    PART,
                    PRODUCT_ABBR_NAME,
                    SEND_DATE,
                    CLAIM_NATURE,
                    CLAIM_TYPE,
                    '20' POSTED
                      FROM APP___CAP__DBUSER.T_PREM_ARAP T1
                     WHERE T1.DERIV_TYPE = '003'
                       AND T1.DUE_TIME < TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD') +1
                       AND T1.PAY_END_DATE >= TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD')
                       AND (T1.FEE_STATUS = '00' OR
                           (T1.FEE_STATUS != '00' AND T1.FEE_STATUS_DATE >= TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD')+1))
                       AND T1.BOOKKEEPING_FLAG = #{bookkeeping_flag}
                       AND (T1.BOOKKEEPING_TIME IS NULL OR TO_CHAR(T1.BOOKKEEPING_TIME, 'YYYYMM') != TO_CHAR(SYSDATE, 'YYYYMM'))
                       AND T1.ARAP_FLAG=#{arap_flag}
                    UNION
                    select LIST_ID,
                        UNIT_NUMBER,
                        BUSINESS_CODE,
                        APPLY_CODE,
                        POLICY_CODE,
                        BATCH_NO,
                        DERIV_TYPE,
                        BRANCH_CODE,
                        ORGAN_CODE,
                        POLICY_ORGAN_CODE,
                        AGENT_CODE,
                        POLICY_TYPE,
                        CHANNEL_TYPE,
                        PRODUCT_CHANNEL,
                        BUSI_APPLY_DATE,
                        VALIDATE_DATE,
                        POLICY_YEAR,
                        PREM_FREQ,
                        CHARGE_YEAR,
                        PAID_COUNT,
                        HOLDER_ID,
                        HOLDER_NAME,
                        INSURED_ID,
                        INSURED_NAME,
                        IS_RISK_MAIN,
                        IS_ITEM_MAIN,
                        GROUP_CODE,
                        GROUP_NAME,
                        BUSI_PROD_CODE,
                        BUSI_PROD_NAME,
                        FEE_TYPE,
                        WITHDRAW_TYPE,
                        REFEFLAG,
                        DUE_TIME,
                        FINISH_TIME,
                        PAY_END_DATE,
                        ARAP_FLAG,
                        CUSTOMER_ID,
                        PAYEE_NAME,
                        CERTI_TYPE,
                        CERTI_CODE,
                        PAYEE_PHONE,
                        BANK_CODE,
                        BANK_ACCOUNT,
                        BANK_USER_NAME,
                        CUSTOMER_ACCOUNT_FLAG,
                        IS_BANK_ACCOUNT,
                        IS_BANK_ACCOUNT_BY,
                        IS_BANK_ACCOUNT_DATE,
                        MONEY_CODE,
                        FEE_AMOUNT,
                        PAY_MODE,
                        ROLLBACK_UNIT_NUMBER,
                        FEE_STATUS,
                        FEE_STATUS_BY,
                        FEE_STATUS_DATE,
                        FROZEN_STATUS,
                        FROZEN_STATUS_BY,
                        FROZEN_STATUS_DATE,
                        IS_BANK_TEXT,
                        FAIL_TIMES,
                        GROUP_ID,
                        SEQ_NO,
                        BANK_TEXT_STATUS,
                        TASK_MARK,
                        IS_BANK_TEXT_BY,
                        IS_BANK_TEXT_DATE,
                        BOOKKEEPING_FLAG,
                        BOOKKEEPING_ID,
                        BELNR,
                        BOOKKEEPING_BY,
                        BOOKKEEPING_TIME,
                        RED_BOOKKEEPING_FLAG,
                        RED_BOOKKEEPING_ID,
                        RED_BELNR,
                        RED_BOOKKEEPING_BY,
                        RED_BOOKKEEPING_TIME,
                        SERVICE_CODE,
                        OPERATOR_BY,
                        INSERT_BY,
                        UPDATE_BY,
                        INSERT_TIME,
                        UPDATE_TIME,
                        INSERT_TIMESTAMP,
                        UPDATE_TIMESTAMP,
                        BUSINESS_TYPE,
                        FUNDS_RTN_CODE,
                        CUS_ACC_FEE_AMOUNT,
                        CUS_ACC_DETAILS_ID,
                        CUS_ACC_UPDATE_BY,
                        CUS_ACC_UPDATE_TIME,
                        CIP_BANK_CODE,
                        CIP_DISTRICT_BANK_CODE,
                        CIP_BRANCH_BANK_CODE,
                        AUDIT_DATE,
                        STATISTICAL_DATE,
                        BANK_DEAL_DATE,
                        IS_SPLIT,
                        IS_SEND,
                        TAX_RATE,
                        AGENT_NAME,
                        AREA,
                        PART,
                        PRODUCT_ABBR_NAME,
                        SEND_DATE,
                        CLAIM_NATURE,
                        CLAIM_TYPE,
                        '30' POSTED
                      FROM APP___CAP__DBUSER.T_PREM_ARAP T2
                     WHERE T2.DERIV_TYPE = '003'
                       AND T2.FEE_STATUS = '01'
                       AND T2.FEE_STATUS_DATE < TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD')+1
                       AND T2.BOOKKEEPING_FLAG = #{bookkeeping_flag}
                       AND (T2.BOOKKEEPING_TIME IS NULL OR TO_CHAR(T2.BOOKKEEPING_TIME, 'YYYYMM') != TO_CHAR(SYSDATE, 'YYYYMM'))
                       AND T2.STATISTICAL_DATE IS NULL
                    UNION
                    select LIST_ID,
                        UNIT_NUMBER,
                        BUSINESS_CODE,
                        APPLY_CODE,
                        POLICY_CODE,
                        BATCH_NO,
                        DERIV_TYPE,
                        BRANCH_CODE,
                        ORGAN_CODE,
                        POLICY_ORGAN_CODE,
                        AGENT_CODE,
                        POLICY_TYPE,
                        CHANNEL_TYPE,
                        PRODUCT_CHANNEL,
                        BUSI_APPLY_DATE,
                        VALIDATE_DATE,
                        POLICY_YEAR,
                        PREM_FREQ,
                        CHARGE_YEAR,
                        PAID_COUNT,
                        HOLDER_ID,
                        HOLDER_NAME,
                        INSURED_ID,
                        INSURED_NAME,
                        IS_RISK_MAIN,
                        IS_ITEM_MAIN,
                        GROUP_CODE,
                        GROUP_NAME,
                        BUSI_PROD_CODE,
                        BUSI_PROD_NAME,
                        FEE_TYPE,
                        WITHDRAW_TYPE,
                        REFEFLAG,
                        DUE_TIME,
                        FINISH_TIME,
                        PAY_END_DATE,
                        ARAP_FLAG,
                        CUSTOMER_ID,
                        PAYEE_NAME,
                        CERTI_TYPE,
                        CERTI_CODE,
                        PAYEE_PHONE,
                        BANK_CODE,
                        BANK_ACCOUNT,
                        BANK_USER_NAME,
                        CUSTOMER_ACCOUNT_FLAG,
                        IS_BANK_ACCOUNT,
                        IS_BANK_ACCOUNT_BY,
                        IS_BANK_ACCOUNT_DATE,
                        MONEY_CODE,
                        FEE_AMOUNT,
                        PAY_MODE,
                        ROLLBACK_UNIT_NUMBER,
                        FEE_STATUS,
                        FEE_STATUS_BY,
                        FEE_STATUS_DATE,
                        FROZEN_STATUS,
                        FROZEN_STATUS_BY,
                        FROZEN_STATUS_DATE,
                        IS_BANK_TEXT,
                        FAIL_TIMES,
                        GROUP_ID,
                        SEQ_NO,
                        BANK_TEXT_STATUS,
                        TASK_MARK,
                        IS_BANK_TEXT_BY,
                        IS_BANK_TEXT_DATE,
                        BOOKKEEPING_FLAG,
                        BOOKKEEPING_ID,
                        BELNR,
                        BOOKKEEPING_BY,
                        BOOKKEEPING_TIME,
                        RED_BOOKKEEPING_FLAG,
                        RED_BOOKKEEPING_ID,
                        RED_BELNR,
                        RED_BOOKKEEPING_BY,
                        RED_BOOKKEEPING_TIME,
                        SERVICE_CODE,
                        OPERATOR_BY,
                        INSERT_BY,
                        UPDATE_BY,
                        INSERT_TIME,
                        UPDATE_TIME,
                        INSERT_TIMESTAMP,
                        UPDATE_TIMESTAMP,
                        BUSINESS_TYPE,
                        FUNDS_RTN_CODE,
                        CUS_ACC_FEE_AMOUNT,
                        CUS_ACC_DETAILS_ID,
                        CUS_ACC_UPDATE_BY,
                        CUS_ACC_UPDATE_TIME,
                        CIP_BANK_CODE,
                        CIP_DISTRICT_BANK_CODE,
                        CIP_BRANCH_BANK_CODE,
                        AUDIT_DATE,
                        STATISTICAL_DATE,
                        BANK_DEAL_DATE,
                        IS_SPLIT,
                        IS_SEND,
                        TAX_RATE,
                        AGENT_NAME,
                        AREA,
                        PART,
                        PRODUCT_ABBR_NAME,
                        SEND_DATE,
                        CLAIM_NATURE,
                        CLAIM_TYPE,
                        '40' POSTED
                      FROM APP___CAP__DBUSER.T_PREM_ARAP T3
                     WHERE T3.FEE_TYPE IN ('T003030100', 'T003030300','T003020000','T003020001')
                       AND T3.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD') + 1
                       AND T3.BOOKKEEPING_FLAG = #{bookkeeping_flag}
                       AND (T3.BOOKKEEPING_TIME IS NULL OR TO_CHAR(T3.BOOKKEEPING_TIME, 'YYYYMM') != TO_CHAR(SYSDATE, 'YYYYMM'))
                   )
            ) A WHERE ROWNUM < #{end_num} 
        ) B WHERE B.RN>=#{start_num}   
        ]]>
    </select>
    <!-- 月末计提数据统计 -->
    <select id="findPremArapForGlMonthlyTotalCount" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
        select COUNT(1)
          FROM (select LIST_ID
                  FROM APP___CAP__DBUSER.T_PREM_ARAP T
                 WHERE T.DERIV_TYPE = '003'
                   AND T.DUE_TIME < TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD')+1
                   AND T.PAY_END_DATE >= TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD')
                   AND (T.FEE_STATUS = '00' OR
                       (T.FEE_STATUS != '00' AND T.FEE_STATUS_DATE >= TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD') +1))
                   AND T.BOOKKEEPING_FLAG = #{bookkeeping_flag}
                   AND (T.BOOKKEEPING_TIME IS NULL OR TO_CHAR(T.BOOKKEEPING_TIME, 'YYYYMM') != TO_CHAR(SYSDATE, 'YYYYMM'))
                   AND T.ARAP_FLAG=#{arap_flag}
                UNION
                select LIST_ID
                  FROM APP___CAP__DBUSER.T_PREM_ARAP T
                 WHERE T.DERIV_TYPE = '003'
                   AND T.FEE_STATUS = '01'
                   AND T.FEE_STATUS_DATE < TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD')+1
                   AND T.BOOKKEEPING_FLAG = #{bookkeeping_flag}
                   AND (T.BOOKKEEPING_TIME IS NULL OR TO_CHAR(T.BOOKKEEPING_TIME, 'YYYYMM') != TO_CHAR(SYSDATE, 'YYYYMM'))
                   AND T.STATISTICAL_DATE IS NULL
                UNION
                select LIST_ID
                  FROM APP___CAP__DBUSER.T_PREM_ARAP T
                 WHERE T.FEE_TYPE IN ('T003030100', 'T003030300','T003020000','T003020001')
                   AND T.STATISTICAL_DATE < TO_DATE(TO_CHAR(#{due_time}, 'YYYYMMDD'), 'YYYYMMDD') + 1
                   AND T.BOOKKEEPING_FLAG = #{bookkeeping_flag}
                   AND (T.BOOKKEEPING_TIME IS NULL OR TO_CHAR(T.BOOKKEEPING_TIME, 'YYYYMM') != TO_CHAR(SYSDATE, 'YYYYMM'))
               )

        ]]>
    </select>
    <!-- 查询totalFeeAmount -->
    <select id="findTotalFee" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        select ABS(SUM(CASE
                      WHEN A.ARAP_FLAG = #{ar} THEN
                       A.FEE_AMOUNT - (CASE
                         WHEN A.CUSTOMER_ACCOUNT_FLAG = #{CUSTOMER_ACCOUNT_FLAG} 
                          THEN
                          NVL(A.CUS_ACC_FEE_AMOUNT, 0)
                         ELSE
                          0
                       END)
                      - NVL(A.policy_balance, 0)
                      ELSE
                       -1 * A.FEE_AMOUNT
                    END)) AS FEE_AMOUNT
       FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1=1 
		 ]]>
        <include refid="findRecordsCondition" />
    </select>
    <!-- 修改groupid、seqno、 -->
    <update id="updateGS" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            GROUP_ID = #{group_id, jdbcType=NUMERIC} ,
            SEQ_NO = #{seq_no, jdbcType=NUMERIC},
            BANK_TEXT_STATUS=#{bank_text_status, jdbcType=VARCHAR},
            FEE_STATUS = #{fee_status, jdbcType=VARCHAR},
            FEE_STATUS_DATE = #{fee_status_date, jdbcType=DATE}
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number}]]>
    </update>
        <!-- 修改groupid、seqno、 -->
    <update id="updateGSBySubtabulation" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP T]]>
          <set>
		       (BANK_TEXT_STATUS, GROUP_ID, SEQ_NO,UPDATE_TIME,UPDATE_TIMESTAMP) =
		       (select #{bank_text_status_udpate, jdbcType=VARCHAR},
		               GROUP_ID,
		               SEQ_NO,
		               TRUNC(SYSDATE),
		               SYSDATE
		          FROM ${bankTextDetailTableName} TB
		         WHERE TB.UNIT_NUMBER = T.UNIT_NUMBER
		         AND TB.TASK_MARK = #{task_mark}
                 AND TB.BANK_TEXT_STATUS = #{bank_text_status_condition}
                 AND TB.BANK_FLAG = #{bank_flag})
            </set>
        <![CDATA[ WHERE EXISTS (select 1
          FROM ${bankTextDetailTableName} TB
         WHERE TB.UNIT_NUMBER = T.UNIT_NUMBER
           AND TB.TASK_MARK = #{task_mark}
           AND TB.BANK_TEXT_STATUS = #{bank_text_status_condition}
           AND TB.BANK_FLAG = #{bank_flag}) ]]>
<!--         <set>
            GROUP_ID = #{group_id, jdbcType=NUMERIC} ,
            SEQ_NO = #{seq_no, jdbcType=NUMERIC},
            BANK_TEXT_STATUS=#{bank_text_status, jdbcType=VARCHAR},
            FEE_STATUS = #{fee_status, jdbcType=VARCHAR},
            FEE_STATUS_DATE = #{fee_status_date, jdbcType=DATE}
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number}]]> -->
    </update>
    <!-- 未预制盘数据还原FEE_STSTUS状态 -->
    <update id="updateFSBySubtabulation" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP T]]>
          <set>
		      T.FEE_STATUS = '03', T.UPDATE_BY =#{update_by, jdbcType=NUMERIC},T.UPDATE_TIME =SYSDATE,T.UPDATE_TIMESTAMP =SYSDATE
            </set>
        <![CDATA[ WHERE  T.TASK_MARK = #{task_mark} AND NOT EXISTS (select 1
          FROM ${bankTextDetailTableName} TB
         WHERE TB.UNIT_NUMBER = T.UNIT_NUMBER
           AND TB.TASK_MARK = #{task_mark}
           AND TB.BANK_TEXT_STATUS = #{bank_text_status_condition}
           AND TB.BANK_FLAG = #{bank_flag}) ]]>
    </update>
    <!-- 查询所有业务来源 -->
    <select id="queryAllBizSource" parameterType="java.util.Map"
        resultType="java.util.Map">
        <![CDATA[select BIZ_SOURCE_CODE,BIZ_SOURCE_NAME FROM APP___CAP__DBUSER.T_BIZ_SOURCE WHERE 1 = 1 ORDER BY  BIZ_SOURCE_CODE ]]>
    </select>
    <!-- 查询所有渠道 -->
    <select id="queryAllChannelType" parameterType="java.util.Map"
        resultType="java.util.Map">
        <![CDATA[select A.INDIVIDUAL_GROUP,A.TYPE_NAME FROM APP___CAP__DBUSER.T_CHANNEL_TYPE A  WHERE 1 = 1 AND A.INDIVIDUAL_GROUP IN ('1','2','3') ORDER BY A.INDIVIDUAL_GROUP]]>
    </select>
    <!-- 查询所有管理机构 -->
    <select id="querySubOrgan" parameterType="java.util.Map"
        resultType="java.util.Map">
        <![CDATA[ select A.ORGAN_CODE, ORGAN_NAME FROM APP___CAP__DBUSER.T_UDMP_ORG_REL A WHERE ORGAN_GRADE = '02' AND ROWNUM<4 ORDER BY A.ORGAN_CODE ]]>
    </select>
     <!-- 按保单号、缴费次数查询续期缴费明细 -->
    <select id="renewalquery" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, 
            A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, 
            A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.REFEFLAG, 
            A.BOOKKEEPING_TIME, A.AGENT_CODE, A.PREM_FREQ, A.OPERATOR_BY, A.AUDIT_DATE,A.STATISTICAL_DATE,DECODE(R.FILTER2,'Y',R.CR_SEG4,R.DR_SEG4) DR_ACCUITEM, DECODE(R.FILTER2,'Y',R.DR_SEG4,R.CR_SEG4) CR_ACCUITEM
            FROM APP___CAP__DBUSER.T_PREM_ARAP A LEFT JOIN APP___CAP__DBUSER.T_GL_ACCOUNTING_RULE R ON R.FEE_TABLE = '2' AND  A.FEE_TYPE = R.BASE1 AND A.WITHDRAW_TYPE = R.BASE2
            WHERE 1 = 1 AND A.DERIV_TYPE != '001' AND A.STATISTICAL_DATE IS NULL AND A.FEE_STATUS='01'
             AND A.UNIT_NUMBER = #{unit_number} 
        ]]>
    </select>
    
    <select id="queryRenewalInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, 
            A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, 
            A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.REFEFLAG, 
            A.BOOKKEEPING_TIME, A.AGENT_CODE, A.PREM_FREQ, A.OPERATOR_BY, A.AUDIT_DATE,A.STATISTICAL_DATE,DECODE(R.FILTER2,'Y',R.CR_SEG4,R.DR_SEG4) DR_ACCUITEM, DECODE(R.FILTER2,'Y',R.DR_SEG4,R.CR_SEG4) CR_ACCUITEM
            FROM APP___CAP__DBUSER.T_PREM_ARAP A LEFT JOIN APP___CAP__DBUSER.T_GL_ACCOUNTING_RULE R ON R.FEE_TABLE = '2' AND  A.FEE_TYPE = R.BASE1 AND A.WITHDRAW_TYPE = R.BASE2
            WHERE A.DERIV_TYPE = '003' ]]>
        <include refid="queryPremArapInfoByPolicyCode" />
        <![CDATA[ ORDER BY A.DUE_TIME DESC ]]>
    </select>
    
    
    <!-- 查询续期缴费明细 -->
    <select id="renewalqueryNew" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select (select U.ORGAN_NAME FROM APP___CAP__DBUSER.T_UDMP_ORG U WHERE U.ORGAN_CODE = C.ORGAN_CODE) ORGAN_CODE,C.holder_name,
               C.PAY_MODE,
               C.PREM_FREQ,
               C.CHARGE_YEAR,
               C.DUE_TIME,
               C.PAY_END_DATE,
               C.APPLY_CODE,
               C.CERTI_TYPE,
               C.CERTI_CODE,
               C.AGENT_NAME,
               C.AGENT_CODE,
               C.BANK_ACCOUNT,
               C.BUSI_PROD_CODE,
               C.IS_RISK_MAIN,
               C.BUSI_PROD_NAME,
               C.COVERAGE_PERIOD,
               C.COVERAGE_YEAR,
               C.INSURED_NAME,
               C.POLICY_CODE,
               C.PAID_COUNT,
               C.FEE_AMOUNT FROM (select max(A.PAID_COUNT)AS paid_count, MAX(A.ORGAN_CODE)AS organ_code,MAX(A.HOLDER_NAME)AS holder_name,MAX(A.PAY_MODE)AS pay_mode,MAX(A.PREM_FREQ)AS prem_freq,MAX(A.CHARGE_YEAR)AS charge_year,MAX(A.DUE_TIME)AS due_time,MAX(A.PAY_END_DATE)AS pay_end_date,MAX(A.APPLY_CODE)AS apply_code,MAX(A.CERTI_TYPE)AS certi_type,MAX(A.CERTI_CODE)AS certi_code,MAX(A.AGENT_NAME)AS agent_name,MAX(A.AGENT_CODE)AS agent_code,MAX(A.BANK_ACCOUNT)AS bank_account,MAX(A.BUSI_PROD_CODE)AS busi_prod_code,MAX(A.IS_RISK_MAIN)AS is_risk_main,MAX(A.BUSI_PROD_NAME)AS busi_prod_name,MAX(A.COVERAGE_PERIOD)AS coverage_period,MAX(A.COVERAGE_YEAR)AS coverage_year,MAX(A.INSURED_NAME)AS insured_name,SUM(A.FEE_AMOUNT) AS fee_amount,MAX(A.POLICY_CODE) AS POLICY_CODE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 
        			A.FEE_STATUS in ('00','03') AND A.ARAP_FLAG = '1' AND  A.DERIV_TYPE = '003' AND A.FROZEN_STATUS != '03']]>
        <include refid="renewalqueryNewPremArapByListPolicyCode" />
        <![CDATA[ GROUP BY A.POLICY_CODE,A.BUSI_PROD_CODE) C ]]>
    </select>

    <!-- 按保单号查询该保单最近续期缴费明细 -->
    <select id="queryNearPremArap" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, 
            A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, 
            A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.REFEFLAG, 
            A.BOOKKEEPING_TIME, A.AGENT_CODE, A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1 = 1  ]]>
        <include refid="queryNearPremArapByPolicyCode" />
        <![CDATA[ ORDER BY A.FINISH_TIME DESC]]>
    </select>
    <update id="updatePremArapForFrozenStatus" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} ,
            FROZEN_STATUS_BY = #{update_by, jdbcType=NUMERIC},
            FROZEN_STATUS_DATE = SYSDATE,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number} AND FEE_STATUS IN ('00','03','20','21')]]>
        
    </update>
    <!-- 查询所有操作 -->
    <select id="findFeeStatusByUnitNumber" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select DISTINCT A.FEE_STATUS, TRIM(A.FROZEN_STATUS) FROZEN_STATUS FROM APP___CAP__DBUSER.T_PREM_ARAP A  ]]>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number}]]>
    </select>
    <!-- 客户账户锁定应收付信息 -->
    <update id="lockPremArapByCustomerAcc" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
            CUS_ACC_FEE_AMOUNT = FEE_AMOUNT,
            CUS_ACC_DETAILS_ID =
            #{cus_acc_details_id, jdbcType=NUMERIC},
            CUS_ACC_UPDATE_BY =
            #{update_by, jdbcType=NUMERIC},
            CUS_ACC_UPDATE_TIME =
            CURRENT_TIMESTAMP,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number} AND FEE_STATUS = #{old_fee_status} AND FEE_AMOUNT = #{fee_amount} AND LIST_ID = #{list_id}]]>
    </update>
    <!-- 客户账户取消锁定应收付信息 -->
    <update id="unlockPremArapByCustomerAcc" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP C]]>
        <set>
            C.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
            C.CUS_ACC_FEE_AMOUNT = '',
            C.CUS_ACC_DETAILS_ID = '',
            C.CUS_ACC_UPDATE_BY = '',
            C.CUS_ACC_UPDATE_TIME = '',
			C.UPDATE_TIME = SYSDATE,
            C.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE C.UNIT_NUMBER IN (
            select A.UNIT_NUMBER
            FROM APP___CAP__DBUSER.T_CUSTOMER_ACCOUNT_DETAILS A, APP___CAP__DBUSER.T_CUSTOMER_ACCOUNT_DETAILS B
            WHERE A.BLOCK_STATUS = #{qa_block_status, jdbcType=VARCHAR}
               AND A.TRANSACTION_TYPE = #{qa_transaction_type, jdbcType=VARCHAR}
               AND A.TEMP_CASH_STATUS = #{qa_temp_cash_status, jdbcType=VARCHAR}
               AND A.BLOCK_LINK_ID = B.TRANSACTION_ID
               AND B.TEMP_CASH_STATUS != #{qb_temp_cash_status, jdbcType=VARCHAR}
               AND A.CUSTOMER_ACCOUNT_ID = #{customer_account_id, jdbcType=NUMERIC} )
           ]]>
    </update>
    <!-- 制返盘应收抵扣客户账户收费 -->
    <select id="findHasCustomerInfoPremCount" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
            select COUNT(DISTINCT UNIT_NUMBER)
            FROM APP___CAP__DBUSER.T_PREM_ARAP A
            WHERE A.CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} 
            AND A.ARAP_FLAG = #{arap_flag} 
            AND A.PAY_MODE = #{pay_mode} 
            AND A.CUSTOMER_ID IS NOT NULL
            AND A.DUE_TIME < TO_DATE(TO_CHAR(#{due_time},'YYYYMMDD'),'YYYYMMDD')+1
            AND A.FEE_STATUS = #{fee_status}
            ]]>
    </select>
    <select id="findHasCustomerInfoPrem" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        select * FROM (
            select ROWNUM RN, Z.* FROM(
                select DISTINCT A.UNIT_NUMBER,A.CUSTOMER_ID
                FROM APP___CAP__DBUSER.T_PREM_ARAP A
                WHERE A.CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} 
                AND A.ARAP_FLAG = #{arap_flag} 
                AND A.PAY_MODE = #{pay_mode} 
                AND A.CUSTOMER_ID IS NOT NULL
                AND A.DUE_TIME < TO_DATE(TO_CHAR(#{due_time},'YYYYMMDD'),'YYYYMMDD')+1
                AND A.FEE_STATUS = #{fee_status}
            ) Z WHERE ROWNUM < #{end_num} 
         ) Y WHERE Y.RN >= #{start_num}
            ]]>
    </select>

    <!-- 按现金方式根据UNITNUMBER查询开始 -->
    <select id="findPremArapByUnitNum" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID,  A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, 
            A.PREM_FREQ, A.GROUP_CODE, A.GROUP_NAME, A.AUDIT_DATE,A.STATISTICAL_DATE FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1=1   ]]>
        <include refid="findPremArapByUnitNumCondition" />
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>
    
    <!-- 根据UNITNUMBER修改VMS发送状态操作 -->
    <update id="updatePremArapStatusByReceipt" parameterType="java.util.Map">
        <![CDATA[ UPDATE DEV_CAP.T_PREM_ARAP ]]>
        <set>
            SEND_DATE = CURRENT_TIMESTAMP ,
            IS_SEND = 1 ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,
        </set>
        <![CDATA[ WHERE 1=1]]>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND UNIT_NUMBER = #{unit_number}]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND BUSI_PROD_CODE = #{busi_prod_code}]]></if>
    </update>
    
     <!-- 根据UNITNUMBER和险种号查询vms推送的listId -->
    <select id="findVMSInfoByUnitNumBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.LIST_ID, A.UNIT_NUMBER FROM DEV_CAP.T_PREM_ARAP A WHERE 1=1   ]]>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number}]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code}]]></if>
        <if test=" busi_item_id != null and busi_item_id != '' "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id}]]></if>
        <if test=" fee_type != null and fee_type != '' "><![CDATA[ AND SUBSTR(A.FEE_TYPE,0,9) = #{fee_type}]]></if>
        
    </select>
    
    <update id="updatePremArapStatus" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            BANK_TEXT_STATUS=#{bank_text_status, jdbcType=VARCHAR},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE SEQ_NO = #{seq_no}]]>
    </update>
    
    <!-- 签单成功后，更新预收信息的保单号 -->
    <update id="updatePremArapPolicyByUNumber" parameterType="java.util.Map">
        <![CDATA[ begin ]]>
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
             POLICY_CODE = #{policy_code, jdbcType=VARCHAR},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR};]]>
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_CASH_DETAIL ]]>
        <set>
             POLICY_CODE = #{policy_code, jdbcType=VARCHAR}
        </set>
        <![CDATA[ WHERE UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR};]]>
        <![CDATA[ end; ]]>
    </update>

    <!-- 续期拆分批处理 -->
    <select id="findPremArapByRenewalCount" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
            select count(1) FROM APP___CAP__DBUSER.T_PREM_ARAP A 
             WHERE (A.IS_SPLIT IS NULL OR  A.IS_SPLIT = '0')  
               and a.bookkeeping_flag = '1'
               and a.posted = '01'
			   and EXISTS(  select 1
					          from APP___CAP__DBUSER.t_valueaddedtax_feetype_conf f
					         where f.fee_type = a.fee_type
					           and f.withdraw_type = a.withdraw_type
					           and (f.busi_is_relevance = '0' or
					               (f.busi_is_relevance = '1' and exists
					                (select 1
					                    from APP___CAP__DBUSER.t_valueaddedtax_organ_conf o
					                   where o.busi_prod_code = a.busi_prod_code))))
            ]]>
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status}]]></if>
        <!-- <if test=" fee_status == null or fee_status == '' "><![CDATA[ AND A.FEE_STATUS = '00' ]]></if> -->
        <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE IN
       		   (select O.ORGAN_CODE FROM APP___CAP__DBUSER.T_UDMP_ORG_REL O START WITH 
       		   O.ORGAN_CODE = #{organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE) ]]></if>
        <if test=" start_date != null and start_date != '' "><![CDATA[ AND A.DUE_TIME >= #{start_date} ]]></if>
        <if test=" end_date != null and end_date != '' "><![CDATA[ AND A.DUE_TIME < #{end_date} ]]></if>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
    </select>
    
    <!-- 续期拆分批处理 -->
    <select id="findPremArapByRenewal" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            select ROWNUM RN,A.* FROM APP___CAP__DBUSER.T_PREM_ARAP A 
             WHERE (A.IS_SPLIT IS NULL OR  A.IS_SPLIT = '0')  
               and a.bookkeeping_flag = '1'
               and a.posted = '01'
			   and EXISTS(  select 1
					          from APP___CAP__DBUSER.t_valueaddedtax_feetype_conf f
					         where f.fee_type = a.fee_type
					           and f.withdraw_type = a.withdraw_type
					           and (f.busi_is_relevance = '0' or
					               (f.busi_is_relevance = '1' and exists
					                (select 1
					                    from APP___CAP__DBUSER.t_valueaddedtax_organ_conf o
					                   where o.busi_prod_code = a.busi_prod_code))))
            ]]>
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status}]]></if>
        <!-- <if test=" fee_status == null or fee_status == '' "><![CDATA[ AND A.FEE_STATUS = '00' ]]></if> -->
        <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE IN
       		   (select O.ORGAN_CODE FROM APP___CAP__DBUSER.T_UDMP_ORG_REL O START WITH 
       		   O.ORGAN_CODE = #{organ_code} CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE) ]]></if>
        <if test=" start_date != null and start_date != '' "><![CDATA[ AND A.DUE_TIME >= #{start_date} ]]></if>
        <if test=" end_date != null and end_date != '' "><![CDATA[ AND A.DUE_TIME < #{end_date} ]]></if>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
    </select>
    <!-- 返盘文件错误报告数据查询 -->
    <select id="returnBankFailedQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select A.UNIT_NUMBER UNIT_NUMBER,
			       SUM(FEE_AMOUNT) FEE_AMOUNT,
			       MAX(BANK_ACCOUNT) BANK_ACCOUNT,
			       MAX(BANK_USER_NAME) BANK_USER_NAME,
			       MAX(BRANCH_CODE) BRANCH_CODE,
			       MAX(POLICY_CODE) POLICY_CODE
			FROM APP___CAP__DBUSER.T_PREM_ARAP A
			WHERE SEQ_NO = #{seq_no}
			GROUP BY A.UNIT_NUMBER
			ORDER BY MAX(A.INSERT_TIME) DESC
		]]>
	</select>

    <!-- VMS发票信息查询（非税延） -->
    <select id="arapReceiptInfoQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 
         rownum rn,
         T.POLICY_CODE,
         FEE_TYPE,
         UNIT_NUMBER,
               LIST_ID,
           BATCH_NO,
           TAX_RATE,
           BRANCH_CODE,
           ORGAN_CODE,
           HOLDER_NAME,
           BUSINESS_CODE,
           BUSI_ITEM_ID,
           VALIDATE_DATE,
           BUSI_PROD_CODE,
           PRODUCT_ABBR_NAME,
           BUSI_PROD_NAME,
           DECODE(DERIV_TYPE,'004',SERVICE_CODE,'') SERVICE_CODE,
           (SELECT SERVICE_NAME FROM DEV_CAP.T_SERVICE S WHERE S.SERVICE_CODE = T.SERVICE_CODE) SERVICE_NAME,
           POLICY_TYPE,
           AMOUNT,
           NVL(TAXAMOUNT,0) TAXAMOUNT,
           FEE_AMOUNT,
           null JYRMBJE,
           null MONEY_CODE,
           APPLY_CODE,
           null AGENT_CODE,
           null AGENT_NAME,
           null AREA,
           null PART,
           null "GROUP",
           ROLLBACK_UNIT_NUMBER,
           (case when DERIV_TYPE = '001' then '1'
                 when DERIV_TYPE = '003' then '3'
                 when DERIV_TYPE = '004' then '2'
                 when DERIV_TYPE = '005' then '4'
                 else '9' end)  DERIV_TYPE,
           (CASE
               WHEN T.AMOUNT < 0 AND T.DERIV_TYPE = '004' THEN
                  (select max(P.BUSINESS_CODE)
                     FROM DEV_CAP.T_PREM_ARAP P
                    WHERE P.UNIT_NUMBER = T.ROLLBACK_UNIT_NUMBER
                         AND P.BUSI_PROD_CODE = T.BUSI_PROD_CODE)
               WHEN T.AMOUNT < 0 AND T.DERIV_TYPE !='004' THEN
                  (select max(P.POLICY_CODE)
                     FROM DEV_CAP.T_PREM_ARAP P
                    WHERE P.UNIT_NUMBER = T.ROLLBACK_UNIT_NUMBER
                        AND P.BUSI_PROD_CODE = T.BUSI_PROD_CODE)
                     WHEN T.DERIV_TYPE ='004' THEN T.POLICY_CODE
               ELSE
                ''
             END) OLDBUSSCODE,
           CHARGE_YEAR_START,
           CERTI_CODE,
           CERTI_TYPE,
           null PAYEE_PHONE,
           PAYEE_EMAIL,
           ISSQYW,
           CHARGE_YEAR_END
           
         FROM ( select PR.UNIT_NUMBER,
                   MAX(PR.LIST_ID) LIST_ID,
                   'N1' || LPAD(MAX(PR.LIST_ID),18,'0')  BATCH_NO,
                   MAX(PR.TAX_RATE) TAX_RATE,
                   MAX(SC.SAPCOMCODE) BRANCH_CODE,
                   MAX(PR.ORGAN_CODE) ORGAN_CODE,
                   MAX(PR.HOLDER_NAME) HOLDER_NAME,
                   MAX(DECODE(PR.DERIV_TYPE,'001', PR.POLICY_CODE, PR.BUSINESS_CODE)) BUSINESS_CODE,
                   MAX(PR.POLICY_CODE) POLICY_CODE,
                   MAX(PR.STATISTICAL_DATE) VALIDATE_DATE,
                   PR.BUSI_PROD_CODE BUSI_PROD_CODE,
                   PR.BUSI_ITEM_ID BUSI_ITEM_ID,
                   SUBSTR(PR.FEE_TYPE,0,9) FEE_TYPE,
                   MAX(BP.PRODUCT_ABBR_NAME) PRODUCT_ABBR_NAME,
                   MAX(PR.BUSI_PROD_NAME) BUSI_PROD_NAME,
                   MAX(PR.SERVICE_CODE) SERVICE_CODE,
                   DECODE(MAX(PR.POLICY_TYPE),'','1',MAX(PR.POLICY_TYPE))  POLICY_TYPE,
                   SUM(CASE
                         WHEN PR.FEE_TYPE LIKE '%0' AND PR.ARAP_FLAG = '1' THEN
                          PR.FEE_AMOUNT
                           WHEN PR.FEE_TYPE LIKE '%0' AND PR.ARAP_FLAG = '2' THEN
                            PR.FEE_AMOUNT * -1
                     END) AMOUNT,
                   SUM(CASE
                         WHEN PR.FEE_TYPE LIKE '%1' AND PR.ARAP_FLAG = '1' THEN
                          PR.FEE_AMOUNT
                           WHEN PR.FEE_TYPE LIKE '%1' AND PR.ARAP_FLAG = '2' THEN
                            PR.FEE_AMOUNT * -1
                       END) TAXAMOUNT,
                   SUM(CASE
                         WHEN PR.ARAP_FLAG = '1' THEN
                          PR.FEE_AMOUNT
                           WHEN PR.ARAP_FLAG = '2' THEN
                            PR.FEE_AMOUNT * -1
                       END) FEE_AMOUNT,
                   SUM(CASE
                         WHEN PR.ARAP_FLAG = '1' THEN
                          PR.FEE_AMOUNT
                           WHEN PR.ARAP_FLAG = '2' THEN
                            PR.FEE_AMOUNT * -1
                       END) * 1 JYRMBJE,
                   MAX(PR.MONEY_CODE) MONEY_CODE,
                   MAX(PR.APPLY_CODE) APPLY_CODE,
                   MAX(PR.AGENT_CODE) AGENT_CODE,
                   MAX(PR.AGENT_NAME) AGENT_NAME,
                   MAX(PR.AREA) AREA,
                   MAX(PR.PART) PART,
                   MAX(PR."GROUP") "GROUP",
                   MAX(PR.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
                   MAX(PR.DERIV_TYPE) DERIV_TYPE,
                   MAX(PR.DUE_TIME) CHARGE_YEAR_START,
                   MAX(PR.CERTI_CODE) CERTI_CODE,
                   MAX(PR.CERTI_TYPE) CERTI_TYPE,
                   MAX(PR.PAYEE_PHONE) PAYEE_PHONE,
                   MAX(PR.PAYEE_EMAIL) PAYEE_EMAIL,
                   MAX(CASE WHEN PR.DERIV_TYPE = '003' THEN '0' ELSE '1' END) ISSQYW,
                   MAX(CASE
                         WHEN PR.PREM_FREQ = '1' THEN
                          PR.DUE_TIME
                         WHEN PR.PREM_FREQ = '2' THEN
                          ADD_MONTHS(PR.DUE_TIME, 1)
                         WHEN PR.PREM_FREQ = '3' THEN
                          ADD_MONTHS(PR.DUE_TIME, 3)
                         WHEN PR.PREM_FREQ = '4' THEN
                          ADD_MONTHS(PR.DUE_TIME, 6)
                         WHEN PR.PREM_FREQ = '5' THEN
                          ADD_MONTHS(PR.DUE_TIME, 12)
                         ELSE
                          PR.DUE_TIME
                       END) CHARGE_YEAR_END
       FROM DEV_CAP.T_PREM_ARAP PR, DEV_CAP.T_SERVICE SE ,DEV_CAP.T_SAPCOM_CONFIG SC,
        DEV_PDS.t_Business_Product bp
    WHERE PR.SERVICE_CODE = SE.SERVICE_CODE(+) 
    AND PR.UNIT_NUMBER IS NOT NULL
    AND PR.POLICY_ORGAN_CODE =  SC.SYSCODE(+)
    AND PR.BUSI_PROD_CODE =BP.PRODUCT_CODE_SYS(+)
    AND BP.TAX_EXTENSION_FLAG!='1'
		AND PR.FEE_TYPE NOT IN ('G004270000','G004270001') --排除退保手续费（退保费用）
		AND ((PR.DERIV_TYPE <> '001') OR (PR.FEE_STATUS = '16' AND PR.DERIV_TYPE = '001') AND PR.STATISTICAL_DATE IS NOT NULL)
		AND (((PR.ARAP_FLAG='1' OR (PR.ARAP_FLAG='2' AND PR.ROLLBACK_UNIT_NUMBER IS NOT NULL))
		AND EXISTS (SELECT 1 FROM DEV_CAP.T_GL_ACCOUNTING_RULE R
			 WHERE R.CR_SEG4 IN ('**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********')
       AND R.BASE1 = PR.FEE_TYPE
       AND R.BASE2 = PR.WITHDRAW_TYPE
       AND PR.FEE_TYPE <> 'G004270001' --退保费用不推送VMS
			 AND PR.WITHDRAW_TYPE <>  '003050100A' --年金/生存金转存万能账户不推送VMS
       AND R.FEE_TABLE ='2'))
       OR (PR.ARAP_FLAG='2'
        AND EXISTS (SELECT 1 FROM DEV_CAP.T_GL_ACCOUNTING_RULE R
         WHERE R.DR_SEG4 IN ('**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********') 
         AND R.BASE1 = PR.FEE_TYPE
         AND R.BASE2 = PR.WITHDRAW_TYPE
         AND PR.FEE_TYPE <> 'G004270001' --退保费用不推送VMS
			 	AND PR.WITHDRAW_TYPE <>  '003050100A' --年金/生存金转存万能账户不推送VMS
         AND R.FEE_TABLE ='2')))
		]]>
		<if test=" is_send != null and is_send != '' or is_send == 0 or is_send == 1 "><![CDATA[ AND PR.IS_SEND = #{is_send} ]]></if>
		<if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND PR.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND PR.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" unit_numbers != null and unit_numbers != '' ">
        <![CDATA[ AND PR.UNIT_NUMBER IN ]]>
        <foreach item="item" index="id" collection="unit_numbers"
            open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        </if>
        <if test=" branch_codes != null and branch_codes != '' ">
        <![CDATA[ AND PR.BRANCH_CODE IN ]]>
        <foreach item="item" index="id" collection="branch_codes"
            open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        </if>
		<if test=" start_insert_time != null and start_insert_time != '' "><![CDATA[ AND PR.STATISTICAL_DATE >= to_date(#{start_insert_time},'YYYY-MM-DD HH24:MI:SS') ]]></if>
		<if test=" end_insert_time != null and end_insert_time != '' "><![CDATA[ AND PR.STATISTICAL_DATE <= to_date(#{end_insert_time},'YYYY-MM-DD HH24:MI:SS') ]]></if>
		<![CDATA[ GROUP BY PR.UNIT_NUMBER,PR.BUSI_PROD_CODE,PR.BUSI_ITEM_ID,PR.POLICY_CODE,SUBSTR(PR.FEE_TYPE,0,9)) T WHERE NOT EXISTS (SELECT 1 FROM DEV_CAP.T_PREM_VMS_INFO A WHERE A.PREM_ARAP_ID = T.LIST_ID)]]>
		<if test=" modnum != null and modnum != '' and start != null and start != '' or modnum >= 0 or start >= 0"><![CDATA[AND MOD(T.LIST_ID, #{modnum})= #{start}  ]]></if>
	</select>
    
    <!-- VMS发票信息数据条数查询（非税延、税延）-->
    <select id="arapReceiptInfoQueryCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			select count(1)
		     FROM ( select PR.UNIT_NUMBER
   		FROM DEV_CAP.T_PREM_ARAP PR, DEV_CAP.T_SERVICE SE ,DEV_CAP.T_SAPCOM_CONFIG SC,
   		 DEV_PDS.t_Business_Product bp
		WHERE PR.SERVICE_CODE = SE.SERVICE_CODE(+) 
		AND PR.IS_SEND = '0'
		AND NOT EXISTS (SELECT 1 FROM DEV_CAP.T_PREM_VMS_INFO A WHERE A.PREM_ARAP_ID=PR.LIST_ID)
		AND PR.UNIT_NUMBER IS NOT NULL
		AND PR.POLICY_ORGAN_CODE =  SC.SYSCODE(+)
		AND PR.BUSI_PROD_CODE =BP.PRODUCT_CODE_SYS(+)
		AND ((PR.DERIV_TYPE <> '001') OR (PR.FEE_STATUS = '16' AND PR.DERIV_TYPE = '001') AND PR.STATISTICAL_DATE IS NOT NULL)
		AND (((PR.ARAP_FLAG='1' OR (PR.ARAP_FLAG='2' AND PR.ROLLBACK_UNIT_NUMBER IS NOT NULL))
		AND EXISTS (SELECT 1 FROM DEV_CAP.T_GL_ACCOUNTING_RULE R
			 WHERE R.CR_SEG4 IN ( '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********'
				)
			 AND R.BASE1 = PR.FEE_TYPE
			 AND R.BASE2 = PR.WITHDRAW_TYPE
			 AND PR.FEE_TYPE <> 'G004270001' --退保费用不推送VMS
			 AND PR.WITHDRAW_TYPE <>  '003050100A' --年金/生存金转存万能账户不推送VMS
			 AND R.FEE_TABLE ='2'))
			 OR (PR.ARAP_FLAG='2'
				AND EXISTS (SELECT 1 FROM DEV_CAP.T_GL_ACCOUNTING_RULE R
			 	WHERE R.DR_SEG4 IN ( '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********'
				) 
			 	AND R.BASE1 = PR.FEE_TYPE
			 	AND R.BASE2 = PR.WITHDRAW_TYPE
			 	AND PR.FEE_TYPE <> 'G004270001' --退保费用不推送VMS
			 	AND PR.WITHDRAW_TYPE <>  '003050100A' --年金/生存金转存万能账户不推送VMS
			 	AND R.FEE_TABLE ='2')))
		 ]]>
		<if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND PR.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND PR.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" unit_numbers != null and unit_numbers != '' ">
        <![CDATA[ AND PR.UNIT_NUMBER IN ]]>
        <foreach item="item" index="id" collection="unit_numbers"
            open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        </if>
        <if test=" branch_codes != null and branch_codes != '' ">
        <![CDATA[ AND PR.BRANCH_CODE IN ]]>
        <foreach item="item" index="id" collection="branch_codes"
            open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        </if>
		<if test=" start_insert_time != null and start_insert_time != '' "><![CDATA[ AND PR.STATISTICAL_DATE >= to_date(#{start_insert_time},'YYYY-MM-DD HH24:MI:SS') ]]></if>
		<if test=" end_insert_time != null and end_insert_time != '' "><![CDATA[ AND PR.STATISTICAL_DATE <= to_date(#{end_insert_time},'YYYY-MM-DD HH24:MI:SS') ]]></if>
		<![CDATA[ GROUP BY PR.UNIT_NUMBER,PR.BUSI_PROD_CODE,PR.BUSI_ITEM_ID,PR.POLICY_CODE,SUBSTR(PR.FEE_TYPE,0,9) ) T  ]]>
	</select>
	
	<!-- 保单余额接口查询 -->
	<select id="queryPremArapByUNBEForFeeAmountASC" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.UNIT_NUMBER, A.FEE_AMOUNT, A.FEE_TYPE, A.WITHDRAW_TYPE, A.LIST_ID, 
						A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ACCOUNT_FLAG, A.DERIV_TYPE,A.BUSINESS_TYPE,
						A.POLICY_CODE
  					FROM APP___CAP__DBUSER.T_PREM_ARAP A
 					WHERE A.FEE_STATUS = '00'
 					AND A.ARAP_FLAG = '1'
   					AND A.UNIT_NUMBER = #{unit_number}
 					ORDER BY A.FEE_AMOUNT
		 ]]>
	</select>
	
	<!-- 保单余额接口更新保单余额 -->
	<update id="updatePolicyBalance" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
		<set>
		<trim suffixOverrides=",">
			A.POLICY_BALANCE = #{policy_balance, jdbcType=NUMERIC},
			A.FEE_STATUS = #{fee_status,jdbcType=VARCHAR} ,
			A.UPDATE_TIME = SYSDATE,
            A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
		</trim>
			<if test="finish_time != null and finish_time != '' "><![CDATA[,A.FINISH_TIME = #{finish_time,jdbcType=TIMESTAMP}]]></if>
		</set>
		<![CDATA[ WHERE A.UNIT_NUMBER = #{unit_number} ]]>
		<include refid="updatePolicyBalanceSQL" />
	</update>
	
	<sql id="updatePolicyBalanceSQL">
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" fee_type != null and fee_type != '' "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" withdraw_type != null and withdraw_type != '' "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
		<if test=" fee_amount != null and fee_amount != '' "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
	</sql>
	
	<!-- 支票收据打印查询应收数据 -->
	<select id="queryPremAeapForChequeReceipt" resultType='java.util.Map' parameterType='java.util.Map'>
		<![CDATA[ 
			 select MAX(A.TASK_MARK) TASK_MARK,
			       MAX(A.POLICY_ORGAN_CODE)POLICY_ORGAN_CODE,
			       MAX(A.HOLDER_NAME)HOLDER_NAME,
			       MAX(A.BUSI_PROD_NAME)BUSI_PROD_NAME,
			       MAX(A.PAYEE_PHONE)PAYEE_PHONE,
			       MAX(A.UNIT_NUMBER)UNIT_NUMBER,
			       MAX(A.PAID_COUNT)PAID_COUNT,
			       MAX(A.FEE_TYPE)FEE_TYPE,
			       MAX(A.SEQ_NO)SEQ_NO,
			       A.BUSI_PROD_CODE,
			       MAX(A.ORGAN_CODE)ORGAN_CODE,
			       MAX(A.CHARGE_YEAR)CHARGE_YEAR,
			       MAX(A.FROZEN_STATUS)FROZEN_STATUS,
			       MAX(A.GROUP_ID)GROUP_ID,
			       MAX(A.POLICY_TYPE)POLICY_TYPE,
			       MAX(A.PRODUCT_CHANNEL)PRODUCT_CHANNEL,
			       MAX(A.POLICY_CODE)POLICY_CODE,
			       MAX(A.PAY_MODE)PAY_MODE,
			       MAX(A.BRANCH_CODE)BRANCH_CODE,
			       MAX(A.VALIDATE_DATE)VALIDATE_DATE,
			       MAX(A.CERTI_TYPE)CERTI_TYPE,
			       MAX(A.MONEY_CODE)MONEY_CODE,
			       MAX(A.BUSINESS_CODE)BUSINESS_CODE,
			       MAX(A.PAYEE_NAME)PAYEE_NAME,
			       MAX(A.BANK_ACCOUNT)BANK_ACCOUNT,
			       MAX(A.FINISH_TIME)FINISH_TIME,
			       MAX(A.DUE_TIME)DUE_TIME,
			       MAX(A.CERTI_CODE)CERTI_CODE,
			       MAX(A.BUSI_APPLY_DATE)BUSI_APPLY_DATE,
			       MAX(A.BELNR)BELNR,
			       MAX(A.HOLDER_ID)HOLDER_ID,
			       MAX(A.BANK_USER_NAME)BANK_USER_NAME,
			       MAX(A.PAY_END_DATE)PAY_END_DATE,
			       MAX(A.DERIV_TYPE)DERIV_TYPE,
			       MAX(A.BANK_CODE)BANK_CODE,
			       MAX(A.AGENT_CODE)AGENT_CODE,
			       MAX(A.AGENT_NAME)AGENT_NAME,
			       MAX(A.PREM_FREQ)PREM_FREQ,
			       MAX(A.GROUP_CODE)GROUP_CODE,
			       MAX(A.GROUP_NAME)GROUP_NAME,
			       MAX(A.FUNDS_RTN_CODE)FUNDS_RTN_CODE,
			       MAX(A.REFEFLAG)REFEFLAG,
			       MAX(A.SERVICE_CODE)SERVICE_CODE,
			       SUM(CASE
			         WHEN ARAP_FLAG = '1' THEN
			          FEE_AMOUNT
			         ELSE
			          -1 * FEE_AMOUNT
			       END) FEE_AMOUNT
			  FROM APP___CAP__DBUSER.T_PREM_ARAP A
			 WHERE A.UNIT_NUMBER IN (select B.UNIT_NUMBER
			                           FROM APP___CAP__DBUSER.T_BILL_DETAILS B
			                          WHERE B.BILL_MAIN_ID = #{cheque_id}
			                            AND B.BILL_MODE = '20')
			  GROUP BY A.UNIT_NUMBER,A.BUSI_PROD_CODE,A.POLICY_CODE
			  
		]]> 
	</select>
	
	 <!-- 交退费接口 SQL -->
    <select id="countPremArapByBusinessCode" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
        	select COUNT(1) FROM  APP___CAP__DBUSER.T_PREM_ARAP T WHERE T.UNIT_NUMBER = #{business_code} 
        ]]>
    </select>
    
    <!-- pos加解锁 -->
	<update id="updatePosReceivablePlusUnlock" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
		<set>
			<trim suffixOverrides=","> A.FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR},
			<if test="frozen_status_by != null and frozen_status_by != '' ">
				A.FROZEN_STATUS_BY = #{frozen_status_by, jdbcType=NUMERIC},
			</if>
			A.FROZEN_STATUS_DATE = SYSDATE,
			A.UPDATE_TIME = SYSDATE,
            A.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP,</trim> 
		</set>	 
		<![CDATA[ WHERE A.UNIT_NUMBER = (SELECT T2.UNIT_NUMBER FROM APP___CAP__DBUSER.T_FMS_INTERFACE T2 WHERE 1=1 ]]>
		<if test="batch_id != null and batch_id != '' ">
			<![CDATA[ AND T2.BATCH_ID = #{batch_id} ]]>
		</if>
		<if test="unit_number != null and unit_number != '' ">
			<![CDATA[ AND T2.UNIT_NUMBER = #{unit_number} ]]>
		</if>
		<if test="trade_sn != null and trade_sn != '' ">
			<![CDATA[ AND T2.TRADE_SN = #{trade_sn} ]]>
		</if>
		<![CDATA[ ) AND A.FEE_STATUS in (#{fee_status},#{fee_status_fail},#{fee_status_way}) AND A.BANK_TEXT_STATUS <> '7']]>
	</update>
	
    <!-- 智能POS应收查询 -->
    <select id="findArapInfoByBusinessCodeAndDerivType" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ 
        	select D.* FROM
        	(select T.UNIT_NUMBER,MAX( SPECIAL_ACCOUNT_FLAG) SPECIAL_ACCOUNT_FLAG, MAX(FROZEN_STATUS_BY) FROZEN_STATUS_BY,MAX(FROZEN_STATUS) FROZEN_STATUS,MAX(T.APPLY_CODE) APPLY_CODE,MAX(T.BUSINESS_TYPE) BUSINESS_TYPE, MAX(T.POLICY_CODE) POLICY_CODE, MAX(T.HOLDER_NAME) HOLDER_NAME, 
        		MAX(T.BANK_ACCOUNT) BANK_ACCOUNT, MAX(T.BANK_USER_NAME) BANK_USER_NAME, MAX(T.CERTI_TYPE) CERTI_TYPE, MAX(T.CERTI_CODE) CERTI_CODE, 
        		MAX(T.BANK_CODE) BANK_CODE, MAX(T.BUSI_PROD_CODE) BUSI_PROD_CODE, MAX(T.BUSI_PROD_NAME) BUSI_PROD_NAME, MAX(T.IS_RISK_MAIN) IS_RISK_MAIN, MAX(T.AGENT_NAME) AGENT_NAME, MAX(A.AGENT_MOBILE) AGENT_CODE,
        		MAX(T.MONEY_CODE) MONEY_CODE,MAX(T.PAY_MODE) PAY_MODE,MAX(T.ORGAN_CODE) ORGAN_CODE,MAX(T.PAYEE_PHONE) PAYEE_PHONE, ABS(SUM(CASE WHEN T.ARAP_FLAG = #{ar} THEN T.FEE_AMOUNT ELSE -1 * T.FEE_AMOUNT END)) FEE_AMOUNT,
        		CASE WHEN SUM(CASE WHEN T.ARAP_FLAG = #{ar} THEN FEE_AMOUNT ELSE -1 * FEE_AMOUNT END) > 0 THEN #{ar} ELSE #{ap} END ARAP_FLAG
        	FROM APP___CAP__DBUSER.T_PREM_ARAP T LEFT JOIN APP___PAS__DBUSER.T_AGENT A ON T.AGENT_CODE=A.AGENT_CODE
        	WHERE 1 = 1 AND NVL(T.BANK_TEXT_STATUS,'1') <> '7'
        ]]>
        <if test="fee_status != null and fee_status != '' and fee_status__fail != null and fee_status__fail != '' and fee_status__way != null and fee_status__way != '' ">
        	<![CDATA[ --制返盘途中允许智能POS进行收费，实时支付、移动支付、微信支付、银保通不允许智能POS收费
        		AND ( (T.FEE_STATUS IN (#{fee_status}, #{fee_status__fail}) AND T.PAY_MODE NOT IN ('40','43','72','60')) OR 
        		(T.FEE_STATUS =  #{fee_status__way} AND T.BANK_TEXT_STATUS IN ('4','6') AND T.PAY_MODE = '32') )
        	]]>
        </if>
        <if test="frozen_status != null and frozen_status != ''">
        	<![CDATA[
        		AND T.FROZEN_STATUS = #{frozen_status}
        	]]>
        </if>
        
        <if test="not_withdraw_type != null and not_withdraw_type != ''">
        	<![CDATA[
        		AND T.WITHDRAW_TYPE NOT IN  (#{not_withdraw_type})
        	]]>
        </if>
        
        <if test="due_time != null and due_time != ''">
        	<![CDATA[
        		AND T.DUE_TIME <= to_date(to_char(#{due_time},'yyyy-MM-dd') ,'yyyy-MM-dd')
        	]]>
        </if>
        <if test="pay_mode != null and pay_mode != ''">
        	<![CDATA[
        		AND T.PAY_MODE = #{pay_mode}
        	]]>
        </if>
        <if test="business_code != null and business_code != ''">
        	<![CDATA[
        		AND T.BUSINESS_CODE = #{business_code}
        	]]>
        </if>
        <if test="deriv_type != null and deriv_type != ''">
        	<![CDATA[
        		AND T.DERIV_TYPE = #{deriv_type}
        	]]>
        </if>
        <if test="business_type != null and business_type != ''">
         <choose>
         	<when test="business_type == '1004' ">
         		<![CDATA[
        		AND T.BUSINESS_TYPE IN (#{business_type},'1007')
        	  ]]>
         	</when>
         	<otherwise>
         		<![CDATA[
        		AND T.BUSINESS_TYPE  IN ( #{business_type},'1001','1002','1003')
        	]]>
         	</otherwise>
         </choose>
        </if>
       	<![CDATA[
       		GROUP BY T.UNIT_NUMBER) D WHERE D.ARAP_FLAG = #{ar}
       	]]>
    </select>
    
    <select id="findPremArapByUnitNumber" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        	select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID,  A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
            A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, 
            A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
            A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE,A.OPERATOR_BY ,
            A.PREM_FREQ, A.AUDIT_DATE,A.STATISTICAL_DATE,
            (CASE WHEN A.DERIV_TYPE = '001' THEN U.USER_NAME END) insert_name,
            (select max(C.PAYREFNO) from APP___CAP__DBUSER.T_CASH_DETAIL C where C.UNIT_NUMBER = A.UNIT_NUMBER) AS payrefno FROM APP___CAP__DBUSER.T_PREM_ARAP A 
            RIGHT JOIN APP___CAP__DBUSER.T_RECALL_SERVICE B ON A.UNIT_NUMBER = B.UNIT_NUMBER 
            LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U ON A.INSERT_BY = U.USER_ID
            WHERE 1 = 1 AND B.STATUS IN ('0', '2') 
            ]]>
    </select>
    
    <!-- huangshuang add ******** 服务接口根据unitNumber或者policyCode查询费用状态(不包含不可收付的信息) 开始 -->
    <select id="queryFeeStatusForUnitNumberOrPolicyCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            select 
            	UNIT_NUMBER,
                POLICY_CODE,
                MIN(FEE_STATUS) FEE_STATUS
            FROM
                APP___CAP__DBUSER.T_PREM_ARAP
            WHERE
                  FEE_STATUS != '16'
        ]]>
        <if test=" unit_number != null and unit_number != ''"><![CDATA[ AND UNIT_NUMBER = #{unit_number}]]></if>
        <if test=" policy_code != null and policy_code != ''"><![CDATA[ AND POLICY_CODE = #{policy_code}]]></if>
        <if test=" deriv_type != null and deriv_type != ''"><![CDATA[ AND DERIV_TYPE = #{deriv_type}]]></if>
        <if test=" business_code != null and business_code != ''"><![CDATA[ AND BUSINESS_CODE = #{business_code}]]></if>
		<![CDATA[ GROUP BY UNIT_NUMBER, POLICY_CODE]]>
    </select>
    <!-- huangshuang add ******** 服务接口根据unitNumber或者policyCode查询费用状态 结束 -->
    
    <select id="findPolicyWaitingPayment"  resultType="java.util.Map" parameterType="java.util.Map">
    	 <![CDATA[ SELECT MAX(A.POLICY_CODE) AS POLICY_CODE,SUM(A.FEE_AMOUNT) AS FEE_AMOUNT1,
      (SELECT SUM(CASE
                     WHEN Z.ARAP_FLAG = '0' THEN
                      Z.FEE_AMOUNT * -1
                     ELSE
                      Z.FEE_AMOUNT
                   END)      
      FROM DEV_CAP.T_PREM_ARAP Z WHERE Z.UNIT_NUMBER=A.UNIT_NUMBER) AS FEE_AMOUNT,
      SUM(FEE_AMOUNT_BEFOR_TAX),A.UNIT_NUMBER 
      FROM APP___CAP__DBUSER.T_PREM_ARAP A,
      APP___PAS__DBUSER.T_CONTRACT_MASTER B,
      APP___PAS__DBUSER.T_POLICY_HOLDER C 
      WHERE B.POLICY_CODE=C.POLICY_CODE
      AND B.LIABILITY_STATE='1'
      AND A.HOLDER_ID = #{holder_id, jdbcType=NUMERIC}
      AND C.POLICY_CODE=A.POLICY_CODE
      AND A.FEE_STATUS IN ('00','03','04')
      AND A.DERIV_TYPE = '003' AND
                       (A.FEE_TYPE = 'G003100000' OR
                       A.FEE_TYPE = 'G003010000' OR
                       A.FEE_TYPE = 'G003020100' OR
                       A.FEE_TYPE = 'G003020200' OR
                       A.FEE_TYPE = 'G003030100' OR
                       A.FEE_TYPE = 'G003030200' OR
                       A.FEE_TYPE = 'G003040100' OR
                       A.FEE_TYPE = 'G003040200')
                       GROUP BY A.UNIT_NUMBER   	 	
    	  ]]>
    </select>
   
    <!--E保通查询核心收费信息按老核心格式返回,--> 
    <select id="queryCentreChargeInfomation" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
             select TPA.APPLY_CODE,
                    TPA.UNIT_NUMBER,
                    TPA.POLICY_CODE,
                    TPA.FEE_STATUS,
                    (select sum(FEE_AMOUNT)
                       FROM APP___CAP__DBUSER.T_PREM_ARAP A
                      WHERE A.APPLY_CODE = #{apply_code}
                        AND A.DERIV_TYPE = '001'
                        AND A.ARAP_FLAG = '1'
                        AND A.FEE_STATUS not in ('16', '02')) AS FEE_AMOUNT,
                    TPA.FINISH_TIME,
                    TPA.PAY_MODE,
                    (select T.STATUS_NAME
                       FROM APP___CAP__DBUSER.T_FEE_STATUS T
                      WHERE T.STATUS_CODE = TPA.FEE_STATUS) STATUS_NAME,
                    NVL((SELECT TB.STD_BANK_CODE
                          FROM DEV_PAS.T_BANK TB
                         WHERE TB.BANK_CODE = TPA.BANK_CODE),
                        TPA.BANK_CODE) BANK_CODE,
                    TPA.BANK_USER_NAME,
                    TPA.BANK_ACCOUNT,
                    TPA.PAYEE_NAME,
                    TPA.CERTI_TYPE,
                    TPA.CERTI_CODE,
                    (select sum(FEE_AMOUNT)
                       FROM APP___CAP__DBUSER.T_PREM_ARAP A
                      WHERE A.APPLY_CODE = #{apply_code}
                        AND A.DERIV_TYPE = '001'
                        AND A.BUSINESS_TYPE = '1004'
                        AND A.ARAP_FLAG = '1'
                        AND A.FEE_STATUS not in ('16', '02')) AS FEE_AMOUNT_TAX  --首期收费金额
               FROM APP___CAP__DBUSER.T_PREM_ARAP TPA
              WHERE TPA.APPLY_CODE = #{apply_code}
                AND TPA.DERIV_TYPE = '001'
                AND TPA.BUSINESS_TYPE = '1004'
                AND TPA.ARAP_FLAG = '1'
                AND TPA.FEE_STATUS not in ('16', '02')
                AND ROWNUM = 1
         ]]>
    </select>
    <!--E保通查询核心收费信息--> 
    <select id="queryArapInfoForVerification" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
             select TPA.LIST_ID,
                    TPA.UNIT_NUMBER,
                    TPA.VALIDATE_DATE,
                    TPA.VALIDATE_DATE2,
                    TPA.AUDIT_DATE,
                    TPA.FINISH_TIME,
                    TPA.BUSI_PROD_CODE,
                    TPA.FEE_STATUS,
                    TPA.PAY_MODE,
                    TPA.statistical_date
             FROM APP___CAP__DBUSER.T_PREM_ARAP TPA 
             WHERE (TPA.FEE_STATUS = '01' OR (TPA.PAY_MODE = '60' OR TPA.FEE_STATUS = '02'))
         ]]>
        <if test=" unit_number != null and unit_number  != '' "><![CDATA[ AND TPA.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND TPA.DERIV_TYPE = #{deriv_type} ]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND TPA.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
    </select>
    <update id="updatePremArapVerificationDate" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP T ]]>
        <set>
                STATISTICAL_DATE = #{statistical_date, jdbcType=DATE} ,
                STATISTICAL_DATE2 = #{statistical_date2, jdbcType=DATE},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
	        <if test="bookkeeping_flag != null and bookkeeping_flag != '' ">
	        	<![CDATA[ ,bookkeeping_flag = #{bookkeeping_flag, jdbcType = NUMERIC}]]>
	        </if>
        </set>
        <![CDATA[ WHERE 1= 1 ]]>
        <if test=" list_id != null and list_id  != '' "><![CDATA[ AND T.LIST_ID = #{list_id} ]]></if>
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND T.FEE_STATUS = #{fee_status} ]]></if>
        <if test=" unit_number != null and unit_number  != '' "><![CDATA[ AND T.UNIT_NUMBER = #{unit_number} ]]></if>
    </update>
    <!--查询实收实付信息  -->
    <select id="queryfindCashDetail" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
             select C.UNIT_NUMBER, C.BUSI_PROD_CODE FROM APP___CAP__DBUSER.T_CASH_DETAIL C
             WHERE  C.UNIT_NUMBER = #{unit_number} 
         ]]>
    </select>
    <!--查询应收应付信息  -->
     <select id="queryfindysPremArap" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[ 
             SELECT P.UNIT_NUMBER FROM APP___CAP__DBUSER.T_PREM_ARAP  P
             WHERE  P.UNIT_NUMBER =  #{unit_number} and p.fee_status in ('00','03','04','15','20')
         ]]>
    </select>
    
     <!--查询下期交费日期  -->
     <select id="CAP_findNextPayDateByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
      SELECT A.VALIDATE_DATE, A.RENEW, C.COVER_PERIOD_TYPE, B.PAY_DUE_DATE,A.PAIDUP_DATE,D.PREM_FREQ
      FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
           APP___PAS__DBUSER.T_CONTRACT_EXTEND    B,
           APP___PDS__DBUSER.T_BUSINESS_PRODUCT   C,
           APP___PAS__DBUSER.t_contract_product D
     WHERE A.BUSI_PRD_ID = C.BUSINESS_PRD_ID
       AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
       AND A.BUSI_ITEM_ID=D.BUSI_ITEM_ID
       AND A.BUSI_ITEM_ID = #{busi_item_id}
         ]]>
    </select>
    
    <!-- 查询续期保险费银行划款险种信息  -->
    <select id="queryRenewalFeeRiskInfo" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
			SELECT A.UNIT_NUMBER,A.POLICY_CODE,A.BUSI_PROD_CODE,A.BUSI_PROD_NAME,A.INSURED_NAME,A.PAID_COUNT,A.BUSI_ITEM_ID,
		(SELECT PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID) NEXTPAYDATE, 
			(Select Bp.Product_Abbr_Name
            From App___Pds__Dbuser.t_Business_Product Bp
            Where BP.PRODUCT_CODE_SYS = A.BUSI_PROD_CODE ) Product_Abbr_Name,
			A.BANK_ACCOUNT,SUM(decode(a.arap_flag,'1',A.FEE_AMOUNT,-A.Fee_Amount))  fee_amount,
			(SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE POLICY_CODE=A.POLICY_CODE 
				AND BUSI_PROD_CODE=A.BUSI_PROD_CODE AND MASTER_BUSI_ITEM_ID IS NULL AND ROWNUM=1) IS_RISK_MASTER
			FROM APP___CAP__DBUSER.T_PREM_ARAP A
			WHERE 1=1
			AND A.POLICY_CODE=#{policy_code}
		]]>
        <if test=" unit_number != null and unit_number  != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <![CDATA[
			AND A.ARAP_FLAG='1'
			AND A.DERIV_TYPE='003' AND A.FEE_TYPE IN
			        ('G003100000','G003010000','G003020100','G003020200','G003030100','G003040100','G003040200')
			        GROUP BY A.UNIT_NUMBER,A.POLICY_CODE,A.BUSI_PROD_CODE,A.BUSI_PROD_NAME,A.INSURED_NAME,A.BANK_ACCOUNT,A.PAID_COUNT,A.BUSI_ITEM_ID
        ]]>
    </select>
   
    <!-- 续期信息-暂交费查询 -->
    <select id="queryRenewalInfoTempPay" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
	    	SELECT DISTINCT B.UNIT_NUMBER,A.PAYREFNO,A.APPLY_CODE,A.BUSI_PROD_CODE,A.BUSI_PROD_NAME,A.PAY_MODE,
          	(SELECT OLD_CODE_NAME FROM APP___PAS__DBUSER.T_CODE_MAPPER WHERE CODETYPE='PAY_MODE' 
          	AND NEW_CODE=A.PAY_MODE AND FROM_MODLE='PAS') PAYMODENAME,
          	(SELECT SUM(C.FEE_AMOUNT) FROM APP___CAP__DBUSER.V_PREM_ARAP C 
          	WHERE C.UNIT_NUMBER = A.UNIT_NUMBER AND C.BUSI_PROD_CODE = A.BUSI_PROD_CODE AND C.FEE_STATUS <> '02') FEE_AMOUNT,
          	A.DUE_TIME,A.AGENT_CODE,A.FINISH_TIME,B.BANK_DEAL_DATE
          	FROM APP___CAP__DBUSER.V_PREM_ARAP A,APP___CAP__DBUSER.V_PREM_ARAP B
          	WHERE 1=1 AND A.POLICY_CODE=B.POLICY_CODE
          	AND A.ARAP_FLAG='1' AND A.DERIV_TYPE='001' AND A.BUSINESS_CODE=A.POLICY_CODE
          	AND A.ARAP_FLAG=B.ARAP_FLAG AND A.DERIV_TYPE=B.DERIV_TYPE AND B.BUSINESS_CODE=B.APPLY_CODE
          	AND A.BOOKKEEPING_FLAG='1'  AND A.POLICY_CODE = #{policy_code}
	    ]]>
    </select>

    <!-- 付款信息查询 -->
    <select id="queryPremArapInfoByClaimNo" resultType="java.util.Map" parameterType="java.util.Map">
     <![CDATA[
     select  A.UNIT_NUMBER,
                  A.BUSINESS_CODE, 
                  A.BANK_CODE,
                  A.Pay_Mode,
                  NVL(ABS(SUM(DECODE(A.ARAP_FLAG,'1',A.FEE_AMOUNT,-A.FEE_AMOUNT))),0) FEE_AMOUNT,
                  A.PAYEE_NAME,
                  A.CERTI_CODE,
                  A.BANK_ACCOUNT,
                  A.DUE_TIME,
                  A.FINISH_TIME
        from APP___CAP__DBUSER.T_PREM_ARAP A
            WHERE A.BUSINESS_CODE =#{business_code}
                 AND A.DERIV_TYPE =#{deriv_type}
           GROUP BY A.UNIT_NUMBER,A.BUSINESS_CODE,A.BANK_CODE,A.PAY_MODE,A.PAYEE_NAME,
            A.CERTI_CODE,A.BANK_ACCOUNT,A.DUE_TIME, A.FINISH_TIME
            HAVING SUM(DECODE(A.ARAP_FLAG,'1',A.FEE_AMOUNT,-A.FEE_AMOUNT)) < 0 ]]>
 </select>
     <!-- 根据支票id查询退票数据 -->
    <select id="queryPremArapForChequeId" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
	    	select 
            	UNIT_NUMBER,
                POLICY_CODE,
                BUSINESS_CODE,
                FEE_STATUS
            FROM APP___CAP__DBUSER.T_PREM_ARAP
            WHERE FEE_STATUS != '16'
            AND BUSINESS_CODE IN (SELECT CHEQUE_NO FROM APP___CAP__DBUSER.T_CHEQUE WHERE CHEQUE_ID = #{chequeId})
	    ]]>
    </select>

    

   
    
    

    <!-- 续期信息-暂交费查询 -->
    <select id="queryDueTimeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
	    	SELECT A.DUE_TIME FROM APP___CAP__DBUSER.T_CASH_DETAIL A WHERE A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} AND A.UNIT_NUMBER = #{unit_number,jdbcType=VARCHAR}
	    ]]>
    </select>
    
   	<!-- 续期交费回写并实时核销接口 使用 -->
	<select id="queryPremArapForRenewalPayment" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT UNIT_NUMBER, DUE_TIME, FEE_AMOUNT
			  FROM (SELECT T.UNIT_NUMBER, MAX(T.DUE_TIME) DUE_TIME,SUM(T.FEE_AMOUNT_BEFOR_TAX) FEE_AMOUNT
			          FROM APP___CAP__DBUSER.T_PREM_ARAP T
			         WHERE T.POLICY_CODE = #{policy_code}
			          AND T.UNIT_NUMBER = #{unit_number}
			          AND T.DERIV_TYPE = '003'
			          AND T.FEE_STATUS IN ('00', '03')
			          AND T.Fee_Type in ('G003100000',
                             'G003010000',
                             'G003020100',
                             'G003020200',
                             'G003030100',
                             'G003030200',
                             'G003040100',
                             'G003040200')
			         GROUP BY T.UNIT_NUMBER
			  		ORDER BY UNIT_NUMBER )
		]]>
	</select>
	<!-- 查询保单下付费最近一笔制返盘划款结果，保单生存领取信息查询接口 使用 -->
	<select id="queryBankSuccByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM (
			SELECT B.BANK_DEAL_DATE,A.RTN_CODE,B.UNIT_NUMBER,B.POLICY_CODE,B.BUSINESS_CODE,B.FUNDS_RTN_CODE 
			FROM  APP___CAP__DBUSER.T_PREM_ARAP B 
			LEFT JOIN APP___CAP__DBUSER.T_BANK_TEXT_DETAIL A ON B.SEQ_NO = A.SEQ_NO
			WHERE B.ARAP_FLAG = '2' AND B.PAY_MODE = '32' AND B.FUNDS_RTN_CODE = 'S0000' 
			AND B.FEE_STATUS = '01' AND B.POLICY_CODE = #{policy_code} 
			ORDER BY B.BANK_DEAL_DATE DESC )
			WHERE ROWNUM=1
		]]>
	</select>
    
    <update id="updatePremArapForIsSplitAndFeeAmount" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP 
            SET
            FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
            AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
            AREA = #{area, jdbcType=VARCHAR} ,
            PART = #{part, jdbcType=VARCHAR} ,
            "GROUP" = #{group, jdbcType=VARCHAR} ,
            IS_SPLIT = #{is_split, jdbcType=NUMERIC} ,
            IS_SEND = #{is_send, jdbcType=NUMERIC} ,
            TAX_RATE = #{tax_rate, jdbcType=NUMERIC} ,
            FEE_AMOUNT_TAX = #{fee_amount_tax, jdbcType=NUMERIC} ,
            FEE_AMOUNT_BEFOR_TAX = #{fee_amount_befor_tax, jdbcType=NUMERIC},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        ]]>
        <![CDATA[WHERE LIST_ID = #{list_id}]]>
    </update>
    
	<!-- 查询收付费最大的交费次数，个人保单信息查询接口 使用 -->
	<select id="queryMaxPaidCount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT MAX(A.PAID_COUNT) AS PAID_COUNT FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1=1 
			AND A.FEE_STATUS IN('01','16','19') AND ARAP_FLAG='1'
		]]>
		<include refid="premArapWhereCondition" />
        <if test="feeStatusList != null and feeStatusList.size()>0  ">
	        <![CDATA[ AND A.FEE_STATUS in ]]>
	        <foreach item="item" index="id" collection="feeStatusList"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>
	</select>
	<!-- 银保通修改撤单数据 -->
	<update id="updatePremArapRollbackUnitNumber" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP T ]]>
        <set>
                rollback_unit_number = null ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE 1= 1 ]]>
        <if test=" rollback_unit_number != null and rollback_unit_number != '' "><![CDATA[ AND ROLLBACK_UNIT_NUMBER = #{unit_number} ]]></if>
    </update>
        <!-- 根据unit_number修改应收应付表数据 -->
    <update id="updatepremArapLsts" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP P]]>
        <set>
            POLICY_ORGAN_CODE = #{policy_organ_code, jdbcType=VARCHAR},
            ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
            BRANCH_CODE = #{branch_code,jdbcType=VARCHAR} ,
            AGENT_NAME= #{agent_name, jdbcType=VARCHAR},
            AGENT_CODE= #{agent_code, jdbcType=VARCHAR},
            CHANNEL_TYPE= #{channel_type, jdbcType=VARCHAR},
            AREA = #{area, jdbcType=VARCHAR}, 
            PART = #{part, jdbcType=VARCHAR}, 
            "GROUP" = #{group, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[
	         WHERE P.FEE_STATUS in ('00','03','04','15','20') AND P.UNIT_NUMBER = #{unit_number} 
        ]]>
    </update>
    
     <!-- 查询失败原因 -->
	<select id="queryReasonsForfailure" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[     
     select (select g.bank_ret_name
                     from APP___CAP__DBUSER.t_bank_ret_conf g
                    where g.bank_ret_code = t.rtn_code) as bank_ret_name
             from APP___CAP__DBUSER.t_bank_text_detail t
            where t.seq_no = #{seq_no}
		]]>
	</select>
    
    <!-- 查询理赔信息 --> 
	<select id="queryClaimsInformation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
     SELECT C.PAYEE_NAME,
       C.CERTI_CODE,
       C.PAY_MODE,
       (SELECT M.NAME FROM APP___CAP__DBUSER.T_PAY_MODE M WHERE M.CODE = C.PAY_MODE) as PAY_MODE_NAME,
       C.FEE_STATUS,
       (SELECT M.STATUS_NAME
          FROM APP___CAP__DBUSER.T_FEE_STATUS M
         WHERE M.STATUS_CODE = C.FEE_STATUS) as FEE_STATUS_NAME,
       C.VALIDATE_DATE,
       C.BUSINESS_CODE,
       C.BANK_ACCOUNT,
       C.CUSTOMER_ID,
       C.FINISH_TIME,
       C.SEQ_NO,
       C.DUE_TIME,
       C.UNIT_NUMBER,
       C.FEE_AMOUNT,
       (SELECT MAX(D.PAYEE_PHONE) FROM DEV_CLM.T_CLAIM_PAYEE D WHERE D.CASE_ID=C.CASE_ID AND D.PAYEE_CERTI_NO=C.CERTI_CODE) PAYEE_PHONE,
       NVL(C.STD_BANK_CODE,C.BANK_CODE) as BANK_CODE,
       (select T.BANK_NAME from APP___CLM__DBUSER.T_BANK T where T.BANK_CODE = C.BANK_CODE) as BANK_NAME
  FROM (SELECT A.PAYEE_NAME,
               MAX(A.CERTI_CODE) AS CERTI_CODE,
               MAX(A.PAY_MODE) AS PAY_MODE,
               MAX(A.CERTI_TYPE) AS CERTI_TYPE,
               MAX(A.FEE_STATUS) AS FEE_STATUS,
               MAX(A.VALIDATE_DATE) AS VALIDATE_DATE,
               MAX(A.BUSINESS_CODE) AS BUSINESS_CODE,
               MAX(A.BANK_ACCOUNT) AS BANK_ACCOUNT,
               MAX(A.CUSTOMER_ID) AS CUSTOMER_ID,
               MAX(A.FINISH_TIME) AS FINISH_TIME,
               MAX(A.SEQ_NO) AS SEQ_NO,
               MAX(A.DUE_TIME) AS DUE_TIME,
               MAX(B.CASE_ID) AS CASE_ID,
               A.UNIT_NUMBER,
               SUM(CASE
                     WHEN A.ARAP_FLAG = '1' THEN
                      A.FEE_AMOUNT * -1
                     ELSE
                      A.FEE_AMOUNT
                   END) AS FEE_AMOUNT,
               MAX(A.BANK_CODE) as BANK_CODE,
               MAX(E.STD_BANK_CODE) AS STD_BANK_CODE 
          FROM APP___CAP__DBUSER.V_PREM_ARAP A
         LEFT JOIN APP___CLM__DBUSER.T_CLAIM_CASE B ON B.CASE_NO = A.BUSINESS_CODE
         LEFT JOIN  APP___CLM__DBUSER.T_BANK E ON A.BANK_CODE = E.BANK_CODE 
         WHERE A.FEE_STATUS <> '02' AND A.BUSINESS_CODE = #{business_code}
         GROUP BY A.PAYEE_NAME, A.UNIT_NUMBER) C
		]]>
	</select>
	
    <!-- 查询应收应付合计金额为0，且待收付的数据，准备转实收 -->
    <select id="querySumZeroPremArap" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
		  		SELECT ROWNUM RN,P.* FROM (SELECT T.UNIT_NUMBER
		          FROM APP___CAP__DBUSER.T_PREM_ARAP T
		         WHERE 1=1 
		         AND T.FEE_AMOUNT != 0.00
		]]>
		<if test="fee_status_list != null and fee_status_list.size()>0  ">
	        <![CDATA[ AND T.FEE_STATUS IN ]]>
	        <foreach item="item" index="id" collection="fee_status_list"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>
		<if test='include_old_data == "N" '>
			<![CDATA[ AND T.LIST_ID < #{old_id}		]]>
		</if>
	    <![CDATA[ 
		           AND T.DUE_TIME < TRUNC(SYSDATE)+1
		         GROUP BY T.UNIT_NUMBER
		        HAVING SUM(DECODE(T.ARAP_FLAG, 1, T.FEE_AMOUNT, -T.FEE_AMOUNT)) = 0 ) P
		]]>
	</select>
	
	    <!-- 查询应收应付合计金额为0，且待收付的数据，准备转实收 -->
    <select id="queryDoubleMainriskFlag" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
		  	SELECT A.DOUBLE_MAINRISK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.POLICY_CODE = #{policy_code}
		]]>
	</select>
	
	  <!-- 查询撤单订单状态 -->
    <select id="findQueryCancelState" resultType="java.util.Map" parameterType="java.util.Map">
          select ap.fee_status,ap.pay_mode,ap.payrefno from APP___CAP__DBUSER.T_PREM_ARAP ap where ap.DERIV_TYPE='001' and  ap.PAY_MODE in('40','41','32')  and ap.APPLY_CODE=#{apply_code}
    </select>
    
    
    <!-- 通过unit_number查询费用状态-->
    <select id="queryBankStausTime" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
		  	   SELECT P.UNIT_NUMBER,
				       CASE
				         WHEN MAX(P.FEE_STATUS) = #{fee_status_fail} THEN
				          MAX(G.UPDATE_TIMESTAMP)
				         WHEN MAX(P.FEE_STATUS) = #{fee_status_finish} THEN
				          MAX(P.FINISH_TIME)
				         ELSE
				          NULL
				       END BANK_STATUS_TIME
				  FROM APP___CAP__DBUSER.T_PREM_ARAP P, APP___CAP__DBUSER.T_BANK_TEXT_GROUP G
				 WHERE P.UNIT_NUMBER = G.UNIT_NUMBER ]]>
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND P.UNIT_NUMBER = #{unit_number} ]]></if>
		 <![CDATA[ 
				   AND P.BUSINESS_CODE = #{business_code}
				   AND P.PAY_MODE = #{pay_mode}
				   GROUP BY P.UNIT_NUMBER,P.BUSINESS_CODE
		 ]]>
		
	</select>
	
	
	<update id="backTextUpdatePremArAp" parameterType="list">
	<foreach collection="list" item="i" index="index" open="begin" close=";end;" separator=";">
		UPDATE APP___CAP__DBUSER.T_PREM_ARAP T
		<set>
			UPDATE_TIME=SYSDATE,UPDATE_TIMESTAMP=SYSDATE,BACK_TEXT_TIME=SYSDATE,
			FUNDS_RTN_CODE = #{i.fundsRtnCode,jdbcType=VARCHAR},BANK_TEXT_STATUS =  #{i.bankTextStatus,jdbcType=VARCHAR},
			<if test="i.bankTextStatus !=null and i.bankTextStatus == 7 ">
				BANK_CODE = #{i.bankCode,jdbcType=VARCHAR},BANK_ACCOUNT= #{i.bankAccount,jdbcType=VARCHAR},
				BANK_USER_NAME=#{i.bankUserName,jdbcType=VARCHAR}
			</if>
			<if test="i.bankTextStatus !=null and i.bankTextStatus == 8 ">
				FEE_STATUS= '03',FAIL_TIMES = NVL(FAIL_TIMES, 0) + 1
			</if>
		</set>
		WHERE FEE_STATUS IN ('03','04') AND SEQ_NO = #{i.seqNo,jdbcType=NUMERIC}
		<if test="i.bankTextStatus !=null and i.bankTextStatus == 8 ">
		AND NOT EXISTS (SELECT 1 FROM APP___CAP__DBUSER.T_BILL_DETAILS A WHERE A.UNIT_NUMBER = T.UNIT_NUMBER AND A.BILL_MODE ='10' AND A.DELETE_FLAG IS NULL)
		</if>
	</foreach>
</update>
		
	 <!-- 修改社保交易流水号操作 -->
    <update id="updatePremArapPosBelnr" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
        <![CDATA[ UPDATE_TIME=SYSDATE,UPDATE_TIMESTAMP=SYSDATE,POS_BELNR = #{pos_belnr, jdbcType=VARCHAR},
        UPDATE_BY = #{update_by, jdbcType=NUMERIC} ]]>
        </set>
        <![CDATA[ WHERE 1=1 AND DERIV_TYPE='004' AND  BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ]]>
    </update>
	
	<select id="findTaskCallbackByFileName" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select COUNT(1) FROM APP___CAP__DBUSER.T_TASK_CALLBACK A WHERE 1 = 1 AND A.BUSI_TYPE='7' AND A.FIELD_1 <>'006'  ]]>
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.FILE_NAME = #{unit_number} ]]></if>
	</select>
	
	<!-- 柜台收付费检索查询个数 -->
    <select id="capAPQueryTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
    <![CDATA[ 
      SELECT COUNT(1) FROM (
        SELECT 
              A.UNIT_NUMBER
        FROM   APP___CAP__DBUSER.T_PREM_ARAP A WHERE 1=1 AND A.DERIV_TYPE <> '006'
        AND A.BANK_TEXT_STATUS <> '7' AND A.FEE_AMOUNT <> 0  ]]>
        <if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
                        <if test="start_date != null and start_date != '' " >
                        <![CDATA[AND A.DUE_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]>
                        </if>
                        <if test="end_date != null and end_date != '' " >
                       <![CDATA[ AND A.DUE_TIME <= TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]>
                        </if>
                        <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND (A.APPLY_CODE = #{policy_code} or A.POLICY_CODE = #{policy_code} )]]></if>
                        <if test=" bank_code != null and bank_code != ''"><![CDATA[ AND A.BANK_CODE =#{bank_code} ]]></if>
                        <choose >
                        <when test="pay_mode != null and pay_mode != '' ">
                        	 <![CDATA[ AND A.PAY_MODE = #{pay_mode}]]>
                        </when>
                        <otherwise>
                        	<![CDATA[ AND A.PAY_MODE  IN ('22','34','10')]]>
                        </otherwise>
                        </choose>
                        <choose>
                        <when test="(unit_number != null and unit_number != '') or (business_code != null and business_code != '') ">
        					<if test=" unit_number != null and unit_number != '' ">
        						<![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        					<if test=" business_code != null and business_code != ''  ">
        						<![CDATA[ AND A.BUSINESS_CODE = #{business_code}  ]]></if>
                        </when>
                        <otherwise>
                        	<![CDATA[AND NVL(A.FROZEN_STATUS,'01') = '01']]>
                        	<if test=" fee_status != null and fee_status != '' ">
                        	<![CDATA[ AND ( ( A.FEE_STATUS IN (#{fee_status},'03','21') AND A.PAY_MODE != '10') OR (A.PAY_MODE = '10' AND A.FEE_STATUS IN ('15') ) )]]></if>
                        </otherwise>
                        </choose>
        				<if test="organ_code  != null and organ_code != '' ">
        AND A.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM APP___CAP__DBUSER.T_UDMP_ORG_REL O 
        START WITH O.ORGAN_CODE=#{organ_code} CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE) </if>
        <if test=" is_corporate  != null and is_corporate != '' "><![CDATA[ AND NVL(A.IS_CORPORATE,'0') = #{is_corporate} ]]></if>
          <![CDATA[
        GROUP BY 
                A.UNIT_NUMBER
         HAVING 1=1 ]]>
        <include refid="capQueryHavingClause" />
        ) B
        </select>
        
        <!-- 柜台付费检索查询 -->
    <select id="capAPQuery" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
         select  ROWNUM RN ,B.* FROM
                 (select  A.UNIT_NUMBER ,MAX(ORGAN_CODE) ORGAN_CODE, A.BUSINESS_CODE, MIN(DERIV_TYPE) DERIV_TYPE, MIN(CERTI_CODE) CERTI_CODE,      
                      MIN(APPLY_CODE) APPLY_CODE, MIN(BANK_ACCOUNT) BANK_ACCOUNT,MIN(BANK_USER_NAME) BANK_USER_NAME, MAX(A.BANK_CODE) BANK_CODE,MAX(A.CIP_DISTRICT_BANK_CODE) CIP_DISTRICT_BANK_CODE,MAX(T.CORRESPONDENT_NAME) CIP_BRANCH_BANK_CODE, MAX(BUSINESS_TYPE) BUSINESS_TYPE, 
                      CASE WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN MIN(POLICY_CODE)ELSE NULL END POLICY_CODE, MAX(WITHDRAW_TYPE) WITHDRAW_TYPE, 
                      MIN(PAYEE_NAME) PAYEE_NAME, MIN(PAY_MODE) PAY_MODE, SUM( CASE WHEN ARAP_FLAG ='${ar}'THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END) FEE_AMOUNT,                                                                  
                      MIN(DUE_TIME) DUE_TIME, MAX(FROZEN_STATUS)FROZEN_STATUS,MAX(IS_CORPORATE) IS_CORPORATE,
                      (SELECT MIN(CAP_ORGAN_CODE) KEEP(DENSE_RANK FIRST ORDER BY O.INSERT_TIMESTAMP DESC) FROM APP___CAP__DBUSER.T_FMS_NONREALTIME O WHERE O.UNIT_NUMBER=A.UNIT_NUMBER GROUP BY O.UNIT_NUMBER) GROUP_NAME,
                      (SELECT MIN(RTN_MSG) KEEP(DENSE_RANK FIRST ORDER BY O.INSERT_TIMESTAMP DESC) FROM APP___CAP__DBUSER.T_FMS_NONREALTIME O WHERE O.UNIT_NUMBER=A.UNIT_NUMBER AND O.TRANS_STATE in ('3','6') GROUP BY O.UNIT_NUMBER) FUNDS_RTN_CODE,
                      MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS, MAX(FEE_STATUS) FEE_STATUS
                      FROM APP___CAP__DBUSER.T_PREM_ARAP A  LEFT JOIN APP___CAP__DBUSER.T_BANK_OF_DEPOSIT T ON T.CORRESPONDENT_NO=A.CIP_DISTRICT_BANK_CODE WHERE 1=1 AND A.DERIV_TYPE <> '006'
					  AND A.BANK_TEXT_STATUS <> '7' AND A.FEE_AMOUNT <> 0  ]]>
                    <if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
                        <if test="start_date != null and start_date != '' " >
                        <![CDATA[AND A.DUE_TIME >= TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]>
                        </if>
                        <if test="end_date != null and end_date != '' " >
                       <![CDATA[ AND A.DUE_TIME <= TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]>
                        </if>
                        <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND (A.APPLY_CODE = #{policy_code} or A.POLICY_CODE = #{policy_code} )]]></if>
                        <if test=" bank_code != null and bank_code != ''"><![CDATA[ AND A.BANK_CODE =#{bank_code} ]]></if>
                        <choose >
                        <when test="pay_mode != null and pay_mode != '' ">
                        	 <![CDATA[ AND A.PAY_MODE = #{pay_mode}]]>
                        </when>
                        <otherwise>
                        	<![CDATA[ AND A.PAY_MODE  IN ('22','34','10')]]>
                        </otherwise>
                        </choose>
                        <choose>
                        <when test="(unit_number != null and unit_number != '') or (business_code != null and business_code != '') ">
        					<if test=" unit_number != null and unit_number != '' ">
        						<![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        					<if test=" business_code != null and business_code != ''  ">
        						<![CDATA[ AND A.BUSINESS_CODE = #{business_code}  ]]></if>
                        </when>
                        <otherwise>
                        	<![CDATA[AND NVL(A.FROZEN_STATUS,'01') = '01']]>
                        	<if test=" fee_status != null and fee_status != '' ">
                        	<![CDATA[ AND ( ( A.FEE_STATUS IN (#{fee_status},'03','21') AND A.PAY_MODE != '10') OR (A.PAY_MODE = '10' AND A.FEE_STATUS IN ('15') ) ) ]]></if>
                        </otherwise>
                        </choose>
        				<if test="organ_code  != null and organ_code != '' ">
        AND A.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM APP___CAP__DBUSER.T_UDMP_ORG_REL O 
        START WITH O.ORGAN_CODE=#{organ_code} CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE) </if>
        <if test=" is_corporate  != null and is_corporate != '' "><![CDATA[ AND NVL(A.IS_CORPORATE,'0') = #{is_corporate} ]]></if>
          <![CDATA[
        GROUP BY 
                A.UNIT_NUMBER ,A.BUSINESS_CODE, A.DERIV_TYPE
        HAVING  CASE WHEN SUM(CASE
                    WHEN ARAP_FLAG = '${ar}' THEN
                              FEE_AMOUNT
                             ELSE
                              -1 * FEE_AMOUNT
                           END) >=0  THEN '${ar}' ELSE '${ap}' END = #{arap_flag}
        ORDER BY 
                MAX(A.INSERT_TIME) DESC ) B  WHERE ROWNUM <= #{LESS_NUM} AND ROWNUM > #{GREATER_NUM}
         ]]>
    </select>
    
    <select id="queryApPayInfo" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[	SELECT A.UNIT_NUMBER,
       MAX(ORGAN_CODE) ORGAN_CODE,
       MAX(A.BUSINESS_CODE) BUSINESS_CODE,
       MIN(DERIV_TYPE) DERIV_TYPE,
       MIN(CERTI_CODE) CERTI_CODE,
       MIN(BANK_ACCOUNT) BANK_ACCOUNT,
       MIN(BANK_USER_NAME) BANK_USER_NAME,
       MAX(A.BANK_CODE) BANK_CODE,
       MAX(CIP_DISTRICT_BANK_CODE) CIP_DISTRICT_BANK_CODE,
       MAX(T.CORRESPONDENT_NAME) CIP_BRANCH_BANK_CODE,
       MIN(APPLY_CODE) APPLY_CODE,
       MAX(A.PAYEE_PHONE) PAYEE_PHONE,
       MAX(BUSINESS_TYPE) BUSINESS_TYPE,
       CASE
         WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN
          MIN(POLICY_CODE)
         ELSE
          NULL
       END POLICY_CODE,
       ABS(SUM(DECODE(A.ARAP_FLAG,'1',A.FEE_AMOUNT,-A.FEE_AMOUNT))) FEE_AMOUNT,
       CASE WHEN SUM(DECODE(A.ARAP_FLAG,'1',A.FEE_AMOUNT,-A.FEE_AMOUNT)) >=0 THEN '1' ELSE '2' END ARAP_FLAG,
       MAX(WITHDRAW_TYPE) WITHDRAW_TYPE,
       MIN(PAYEE_NAME) PAYEE_NAME,
       MIN(PAY_MODE) PAY_MODE,
       MIN(DUE_TIME) DUE_TIME,
       MAX(FROZEN_STATUS) FROZEN_STATUS,
       MAX(MONEY_CODE) MONEY_CODE,
       MAX(A.IS_CORPORATE) IS_CORPORATE,
       MAX(A.CERTI_TYPE) CERTI_TYPE,
       MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
       MAX(FEE_STATUS) FEE_STATUS
  FROM APP___CAP__DBUSER.T_PREM_ARAP A LEFT JOIN APP___CAP__DBUSER.T_BANK_OF_DEPOSIT T ON T.CORRESPONDENT_NO=A.CIP_DISTRICT_BANK_CODE
 WHERE 1 = 1
   AND A.FEE_AMOUNT <> 0 ]]>
   <if test=" unitNumbers != null and unitNumbers.length>0 ">
            <![CDATA[ AND A.UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="unitNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
     <if test=" feeStatuses != null and feeStatuses.length>0 ">
            <![CDATA[ AND A.FEE_STATUS IN]]>
            <foreach item="item" index="id" collection="feeStatuses"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="frozen_status != null and frozen_status != '' ">
          <![CDATA[ AND NVL(A.FROZEN_STATUS,'01') = #{frozen_status} ]]>
        </if>
 <![CDATA[ GROUP BY A.UNIT_NUMBER  ]]>
    </select>
    
    
    <!-- 按unit_number修改操作 -->
    <update id="updatePremArapForNonRealTime" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
            FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            FEE_STATUS_DATE = SYSDATE ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <![CDATA[ WHERE FEE_STATUS NOT IN ('04') AND NVL(FROZEN_STATUS,'01') = '01']]>
        <if test=" unit_number != null and unit_number != '' ">
            <![CDATA[ AND UNIT_NUMBER = #{unit_number}]]>
        </if>
    </update>
    
     <!-- 按unit_number与business_code查询操作 -->
    <select id="qryTPremArap" parameterType="java.util.Map" resultType="java.util.Map">
    	select  SUM( CASE WHEN ARAP_FLAG ='1' THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END) FEE_AMOUNT, max(t2.name) PAY_MODE, max(t3.status_name) FEE_STATUS, max(BANK_CODE) BANK_CODE, max(BANK_ACCOUNT) BANK_ACCOUNT, max(BANK_USER_NAME) BANK_USER_NAME  
    	from APP___CAP__DBUSER.T_PREM_ARAP t1 left join T_PAY_MODE t2 on t1.pay_mode = t2.code left join T_FEE_STATUS t3 on t1.fee_status = t3.status_code
		where 1=1
		<if test=" unit_number != null and unit_number != '' ">
            <![CDATA[ AND UNIT_NUMBER = #{unit_number}]]>
        </if>
        <if test=" business_code != null and business_code != '' ">
            <![CDATA[ AND BUSINESS_CODE = #{business_code}]]>
        </if> 
		group by unit_number
    </select>
    
    <!-- 微信支付退费提交失败修改操作 -->
    <update id="updatePremArapFailTimes" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
         <if test="fee_status !=null and fee_status != '' and fee_status == '00'.toString() ">
         	<![CDATA[ A.FAIL_TIMES = CASE WHEN NVL(A.FAIL_TIMES, 0) <= 1 THEN NVL(A.FAIL_TIMES, 0) + 1 ELSE A.FAIL_TIMES END,
			A.FEE_STATUS = CASE WHEN NVL(A.FAIL_TIMES, 0) < 1 THEN '00'  ELSE '03' END, ]]>
         </if>
         <if test="fee_status !=null and fee_status != '' and fee_status == '03'.toString()">
           <![CDATA[ A.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
                     A.FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
                     A.FEE_STATUS_DATE = SYSDATE ,
            ]]>
         </if>
         
         <if test="fee_status !=null and fee_status != '' and fee_status == '04'.toString()">
           <![CDATA[ A.FEE_STATUS = #{fee_status, jdbcType=VARCHAR} , 
           			 A.FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            	     A.FEE_STATUS_DATE = SYSDATE ,
           ]]>
         </if>
        <![CDATA[
        	
            A.UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
            UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ]]>
        </set>
        <![CDATA[ WHERE 1 = 1 AND A.FEE_STATUS = '00' ]]>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
    </update>
    
    <!-- 按apply_code和pay_mode(固定为'72':微信支付)查询操作 -->
    <select id="findWeChatPayPremArap" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
     select D.* FROM
        (select 
              A.UNIT_NUMBER UNIT_NUMBER, 
              MAX(A.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
              SUM(A.POLICY_BALANCE) POLICY_BALANCE,
              MIN(BUSINESS_CODE) BUSINESS_CODE,
              MAX(A.FEE_STATUS) FEE_STATUS,
              MAX(BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(DERIV_TYPE) DERIV_TYPE,
              MIN(CUSTOMER_ID) CUSTOMER_ID,
              MIN(APPLY_CODE) APPLY_CODE,
              MAX(BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(BANK_CODE) BANK_CODE,
              MAX(BANK_USER_NAME) BANK_USER_NAME,
              MAX(CERTI_TYPE) CERTI_TYPE,
              MAX(CERTI_CODE) CERTI_CODE,
              MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              CASE WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN        
                   MIN(POLICY_CODE)
                   WHEN MAX(WITHDRAW_TYPE) = '**********' THEN (SELECT MIN(T.POLICY_CODE) FROM APP___CAP__DBUSER.T_PREM_ARAP T WHERE A.UNIT_NUMBER=T.UNIT_NUMBER AND T.ARAP_FLAG='1')
              ELSE
                   NULL
              END POLICY_CODE,
              MAX(WITHDRAW_TYPE) WITHDRAW_TYPE, 
              MIN(PAYEE_NAME) PAYEE_NAME,                                    
              MIN(PAY_MODE) PAY_MODE,                                           
              MAX(ARAP_FLAG) ARAP_FLAG,                                         
              SUM(                                                                
                   CASE WHEN 
                      ARAP_FLAG ='1' 
                       THEN 
                        FEE_AMOUNT 
                      ELSE 
                        -1*FEE_AMOUNT 
                       END
                     ) FEE_AMOUNT,                                            
              
              MIN(DUE_TIME) DUE_TIME,                               
              MAX(HOLDER_NAME)  HOLDER_NAME,                             
              MAX(INSURED_NAME)  INSURED_NAME,
              MAX(GROUP_CODE)  GROUP_CODE,                                 
              MAX(GROUP_NAME)  GROUP_NAME, 
              MAX(OPERATOR_BY)  OPERATOR_BY                        
        FROM APP___CAP__DBUSER.T_PREM_ARAP A
        WHERE 1=1
        ]]>
        <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} AND A.PAY_MODE = '72' AND A.FEE_STATUS in ( '00' , '01' )]]></if>
        <![CDATA[
        GROUP BY A.UNIT_NUMBER) D
         ]]>
    </select>
    
    <!-- 按apply_code和unit_number查询操作 -->
    <select id="findPremArapByPolicyCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
     select D.* FROM
        (select 
              A.UNIT_NUMBER UNIT_NUMBER, 
              MAX(A.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
              SUM(A.POLICY_BALANCE) POLICY_BALANCE,
              MIN(BUSINESS_CODE) BUSINESS_CODE,
              MAX(A.ORGAN_CODE) ORGAN_CODE,
              MAX(A.AGENT_CODE) AGENT_CODE,
              MAX(A.FROZEN_STATUS) FROZEN_STATUS,
              MAX(A.FEE_STATUS) FEE_STATUS,
              MAX(BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(DERIV_TYPE) DERIV_TYPE,
              MIN(CUSTOMER_ID) CUSTOMER_ID,
              MIN(APPLY_CODE) APPLY_CODE,
              MAX(BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(BANK_CODE) BANK_CODE,
              MAX(BANK_USER_NAME) BANK_USER_NAME,
              MAX(CERTI_TYPE) CERTI_TYPE,
              MAX(CERTI_CODE) CERTI_CODE,
              MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              CASE WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN        
                   MIN(POLICY_CODE)
                   WHEN MAX(WITHDRAW_TYPE) = '**********' THEN (SELECT MIN(T.POLICY_CODE) FROM APP___CAP__DBUSER.T_PREM_ARAP T WHERE A.UNIT_NUMBER=T.UNIT_NUMBER AND T.ARAP_FLAG='1')
              ELSE
                   NULL
              END POLICY_CODE,
              MAX(WITHDRAW_TYPE) WITHDRAW_TYPE, 
              MIN(PAYEE_NAME) PAYEE_NAME,                                    
              MIN(PAY_MODE) PAY_MODE,                                           
              MAX(ARAP_FLAG) ARAP_FLAG,                                         
              SUM(                                                                
                   CASE WHEN 
                      ARAP_FLAG ='1' 
                       THEN 
                        FEE_AMOUNT 
                      ELSE 
                        -1*FEE_AMOUNT 
                       END
                     ) FEE_AMOUNT,                                            
              
              MIN(DUE_TIME) DUE_TIME,                               
              MAX(HOLDER_NAME)  HOLDER_NAME,                             
              MAX(INSURED_NAME)  INSURED_NAME,
              MAX(GROUP_CODE)  GROUP_CODE,                                 
              MAX(GROUP_NAME)  GROUP_NAME, 
              MAX(OPERATOR_BY)  OPERATOR_BY                        
        FROM APP___CAP__DBUSER.T_PREM_ARAP A
        WHERE 1=1
        ]]>
        <if test="deriv_type != null and deriv_type != '' "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
        <if test="unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test="business_code != null and business_code != '' "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
        <if test="fee_status_list != null and fee_status_list.size()>0  ">
	        <![CDATA[ AND A.FEE_STATUS IN ]]>
	        <foreach item="item" index="id" collection="fee_status_list"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>
        <![CDATA[
        GROUP BY A.UNIT_NUMBER) D
         ]]>
    </select>
    
    <select id="findPremDetailByUnitNumber" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT ROWNUM RN,C.* FROM(
          SELECT  MAX(BC.PRODUCT_CATEGORY_BOCIC) BUSI_PROD_NAME,
       A.BUSI_PROD_CODE,A.BUSINESS_CODE,
       MAX(A.POLICY_CODE) POLICY_CODE,
       SUM(DECODE(A.ARAP_FLAG, '1', A.FEE_AMOUNT, -A.FEE_AMOUNT)) FEE_AMOUNT
  FROM APP___CAP__DBUSER.T_PREM_ARAP A
  LEFT JOIN APP___CAP__DBUSER.T_BUSINESS_PRODUCT B
    ON A.BUSI_PROD_CODE = B.PRODUCT_CODE_SYS
    LEFT JOIN APP___CAP__DBUSER.T_PRODUCT_BOCIC BC ON BC.BUSINESS_PRD_ID=B.BUSINESS_PRD_ID
 WHERE A.UNIT_NUMBER = #{unit_number}
 GROUP BY A.BUSINESS_CODE,
          A.UNIT_NUMBER,
          A.BUSI_PROD_CODE) C ]]>
    </select>
    <select id="queryPremAp" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
    select D.* FROM
        (select 
              A.UNIT_NUMBER UNIT_NUMBER, 
              A.DERIV_TYPE DERIV_TYPE,
              MIN(A.BUSINESS_CODE) BUSINESS_CODE,
              MAX(A.BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(A.CUSTOMER_ID) CUSTOMER_ID,
              MIN(A.APPLY_CODE) APPLY_CODE,
              MAX(A.BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(A.BANK_USER_NAME) BANK_USER_NAME,
              MIN(A.BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              CASE WHEN MIN(A.POLICY_CODE) = MAX(A.POLICY_CODE) THEN        
                   MIN(A.POLICY_CODE)
              ELSE
                   NULL
              END POLICY_CODE,
              MAX(A.CERTI_CODE) CERTI_CODE,
              MAX(A.CERTI_TYPE) CERTI_TYPE,
              MAX(A.WITHDRAW_TYPE) WITHDRAW_TYPE,
              MAX(A.FEE_STATUS) FEE_STATUS,
              MAX(A.MONEY_CODE) MONEY_CODE,
              MAX(A.BANK_CODE) BANK_CODE,   
              MAX(A.ORGAN_CODE) ORGAN_CODE,
              MAX(A.PAYEE_PHONE) PAYEE_PHONE,                                
              MIN(A.PAYEE_NAME) PAYEE_NAME,                                    
              MIN(A.PAY_MODE) PAY_MODE,                                           
              MAX(A.ARAP_FLAG) ARAP_FLAG,
              MAX(B.PREMIUM) FEE_AMOUNT,                                         
              MIN(A.DUE_TIME) DUE_TIME,                               
              MAX(A.HOLDER_NAME)  HOLDER_NAME,                             
              MAX(A.INSURED_NAME)  INSURED_NAME,
              MAX(A.GROUP_CODE)  GROUP_CODE,                                 
              MAX(A.GROUP_NAME)  GROUP_NAME,
              MAX(A.FINISH_TIME) FINISH_TIME,
              MAX(A.STATISTICAL_DATE) STATISTICAL_DATE,
              MAX(A.AGENT_CODE) AGENT_CODE,
              MAX(A.FROZEN_STATUS) FROZEN_STATUS,
              MAX(A.FAIL_TIMES) FAIL_TIMES,
              DECODE(MAX(A.IS_RECOVER_DOCUMENT),1,1,0) IS_RECOVER_DOCUMENT,
              MAX(A.SPECIAL_ACCOUNT_FLAG) SPECIAL_ACCOUNT_FLAG
        FROM APP___CAP__DBUSER.T_PREM_ARAP A 
        LEFT JOIN APP___CAP__DBUSER.T_FMS_INTERFACE B  ON A.UNIT_NUMBER=B.UNIT_NUMBER
        WHERE 1=1 
        ]]>
        <if test=" pay_mode != null and pay_mode != '' "><![CDATA[ AND A.PAY_MODE = #{pay_mode} 
        AND B.PAY_MODE = #{pay_mode}]]></if>
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status}
        AND B.STATUS ='2' ]]></if>
        <if test=" arap_flag != null and arap_flag != '' "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag}
        AND B.ARAP_FLAG = #{arap_flag} ]]></if>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" special_account_flag != null and special_account_flag != '' "><![CDATA[ AND A.SPECIAL_ACCOUNT_FLAG = #{special_account_flag}
        ]]></if>
		<if test="deriv_type != null and deriv_type != '' "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]>
		</if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND B.SUBMIT_CHANNEL = #{channel_type} ]]>
		</if>
		<if test=" Send_Date_Flag != null and Send_Date_Flag != '' and Send_Date_Flag == '0'.toString() "><![CDATA[ AND A.DUE_TIME <= trunc(SYSDATE) ]]>
		</if>
		<if test=" Send_Date_Flag != null and Send_Date_Flag != '' and Send_Date_Flag == '1'.toString() "><![CDATA[ AND A.DUE_TIME < trunc(SYSDATE) ]]>
		</if>
        <![CDATA[
        GROUP BY A.UNIT_NUMBER,A.DERIV_TYPE ) D ORDER BY D.DUE_TIME ASC
         ]]>
    </select>
    <select id="queryFailPremAp" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
    select D.* FROM
        (select 
              A.UNIT_NUMBER UNIT_NUMBER, 
              A.DERIV_TYPE DERIV_TYPE,
              MIN(A.BUSINESS_CODE) BUSINESS_CODE,
              MAX(A.BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(A.CUSTOMER_ID) CUSTOMER_ID,
              MIN(A.APPLY_CODE) APPLY_CODE,
              MAX(A.BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(A.BANK_USER_NAME) BANK_USER_NAME,
              MIN(A.BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              CASE WHEN MIN(A.POLICY_CODE) = MAX(A.POLICY_CODE) THEN        
                   MIN(A.POLICY_CODE)
              ELSE
                   NULL
              END POLICY_CODE,
              MAX(A.CERTI_CODE) CERTI_CODE,
              MAX(A.CERTI_TYPE) CERTI_TYPE,
              MAX(A.WITHDRAW_TYPE) WITHDRAW_TYPE,
              MAX(A.FEE_STATUS) FEE_STATUS,
              MAX(A.MONEY_CODE) MONEY_CODE,
              MAX(A.BANK_CODE) BANK_CODE,   
              MAX(A.ORGAN_CODE) ORGAN_CODE,
              MAX(A.PAYEE_PHONE) PAYEE_PHONE,                                
              MIN(A.PAYEE_NAME) PAYEE_NAME,                                    
              MIN(A.PAY_MODE) PAY_MODE,                                           
              MAX(A.ARAP_FLAG) ARAP_FLAG,
              MAX(B.PREMIUM) FEE_AMOUNT,                                         
              MIN(A.DUE_TIME) DUE_TIME,                               
              MAX(A.HOLDER_NAME)  HOLDER_NAME,                             
              MAX(A.INSURED_NAME)  INSURED_NAME,
              MAX(A.GROUP_CODE)  GROUP_CODE,                                 
              MAX(A.GROUP_NAME)  GROUP_NAME,
              MAX(A.FINISH_TIME) FINISH_TIME,
              MAX(A.STATISTICAL_DATE) STATISTICAL_DATE,
              MAX(A.AGENT_CODE) AGENT_CODE,
              MAX(A.FROZEN_STATUS) FROZEN_STATUS,
              MAX(A.SPECIAL_ACCOUNT_FLAG) SPECIAL_ACCOUNT_FLAG
        FROM APP___CAP__DBUSER.T_PREM_ARAP A 
        LEFT JOIN APP___CAP__DBUSER.T_FMS_INTERFACE B  ON A.UNIT_NUMBER=B.UNIT_NUMBER
        WHERE 1=1 
        ]]>
        <if test=" pay_mode != null and pay_mode != '' "><![CDATA[ AND A.PAY_MODE = #{pay_mode} 
        AND B.PAY_MODE = #{pay_mode}]]></if>
        <if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS = #{fee_status}
        AND B.STATUS ='3' ]]></if>
        <if test=" arap_flag != null and arap_flag != '' "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag}
        AND B.ARAP_FLAG = #{arap_flag} ]]></if>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" special_account_flag != null and special_account_flag != '' "><![CDATA[ AND A.SPECIAL_ACCOUNT_FLAG = #{special_account_flag}
        ]]></if>
        <if test=" fail_times  != null and fail_times != '' ">
        <![CDATA[
			AND (FAIL_TIMES < #{fail_times, jdbcType=NUMERIC}  OR FAIL_TIMES IS NULL )
			]]>
		</if>
		<if test="deriv_type != null and deriv_type != '' "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]>
		</if>
		<if test=" Send_Date_Flag != null and Send_Date_Flag != '' and Send_Date_Flag == '0'.toString() "><![CDATA[ AND A.DUE_TIME <= trunc(SYSDATE) ]]>
		</if>
		<if test=" Send_Date_Flag != null and Send_Date_Flag != '' and Send_Date_Flag == '1'.toString() "><![CDATA[ AND A.DUE_TIME < trunc(SYSDATE) ]]>
		</if>
        <![CDATA[
        GROUP BY A.UNIT_NUMBER,A.DERIV_TYPE ) D ORDER BY D.DUE_TIME ASC
         ]]> 
    </select>
    
    <!-- VMS发票信息查询（税延） -->
    <select id="findArapByTaxExtionSionFlag" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 
         rownum rn,
         T.POLICY_CODE,
         UNIT_NUMBER,
               LIST_ID,
           BATCH_NO,
           TAX_RATE,
           BRANCH_CODE,
           ORGAN_CODE,
           HOLDER_NAME,
           BUSINESS_CODE,
           VALIDATE_DATE,
           BUSI_PROD_CODE,
           BUSI_ITEM_ID,
           PRODUCT_ABBR_NAME,
           BUSI_PROD_NAME,
           DECODE(DERIV_TYPE,'004',SERVICE_CODE,'') SERVICE_CODE,
           (SELECT SERVICE_NAME FROM DEV_CAP.T_SERVICE S WHERE S.SERVICE_CODE = T.SERVICE_CODE) SERVICE_NAME,
           POLICY_TYPE,
           AMOUNT,
           NVL(TAXAMOUNT,0) TAXAMOUNT,
           FEE_AMOUNT,
           null JYRMBJE,
           null MONEY_CODE,
           APPLY_CODE,
           null AGENT_CODE,
           null AGENT_NAME,
           null AREA,
           null PART,
           null "GROUP",
           ROLLBACK_UNIT_NUMBER,
           (case when DERIV_TYPE = '001' then '1'
                 when DERIV_TYPE = '003' then '3'
                 when DERIV_TYPE = '004' then '2'
                 when DERIV_TYPE = '005' then '4'
                 else '9' end)  DERIV_TYPE,
           (CASE
               WHEN T.AMOUNT < 0 AND T.DERIV_TYPE = '004' THEN
                  (select max(P.BUSINESS_CODE)
                     FROM DEV_CAP.T_PREM_ARAP P
                    WHERE P.UNIT_NUMBER = T.ROLLBACK_UNIT_NUMBER
                         AND P.BUSI_PROD_CODE = T.BUSI_PROD_CODE)
               WHEN T.AMOUNT < 0 AND T.DERIV_TYPE !='004' THEN
                  (select max(P.POLICY_CODE)
                     FROM DEV_CAP.T_PREM_ARAP P
                    WHERE P.UNIT_NUMBER = T.ROLLBACK_UNIT_NUMBER
                        AND P.BUSI_PROD_CODE = T.BUSI_PROD_CODE)
                     WHEN T.DERIV_TYPE ='004' THEN T.POLICY_CODE
               ELSE
                ''
             END) OLDBUSSCODE,
           CHARGE_YEAR_START,
           CERTI_CODE,
           CERTI_TYPE,
           null PAYEE_PHONE,
           PAYEE_EMAIL,
           ISSQYW,
           CHARGE_YEAR_END
           
         FROM ( select PR.UNIT_NUMBER,
                   MAX(PR.LIST_ID) LIST_ID,
                   'N1' || LPAD(MAX(PR.LIST_ID),18,'0')  BATCH_NO,
                   0 TAX_RATE,
                   MAX(SC.SAPCOMCODE) BRANCH_CODE,
                   MAX(PR.ORGAN_CODE) ORGAN_CODE,
                   MAX(PR.HOLDER_NAME) HOLDER_NAME,
                   MAX(DECODE(PR.DERIV_TYPE,'001', PR.POLICY_CODE, PR.BUSINESS_CODE)) BUSINESS_CODE,
                   MAX(PR.POLICY_CODE) POLICY_CODE,
                   MAX(PR.STATISTICAL_DATE) VALIDATE_DATE,
                   PR.BUSI_PROD_CODE BUSI_PROD_CODE,
                   PR.BUSI_ITEM_ID BUSI_ITEM_ID,
                   MAX(BP.PRODUCT_ABBR_NAME) PRODUCT_ABBR_NAME,
                   MAX(PR.BUSI_PROD_NAME) BUSI_PROD_NAME,
                   MAX(PR.SERVICE_CODE) SERVICE_CODE,
                   DECODE(MAX(PR.POLICY_TYPE),'','1',MAX(PR.POLICY_TYPE))  POLICY_TYPE,
                   SUM(CASE
                         WHEN PR.ARAP_FLAG = '1' THEN
                          PR.FEE_AMOUNT
                           WHEN PR.ARAP_FLAG = '2' THEN
                            PR.FEE_AMOUNT * -1
                       END) AMOUNT,
                   0 TAXAMOUNT,
                   SUM(CASE
                         WHEN PR.ARAP_FLAG = '1' THEN
                          PR.FEE_AMOUNT
                           WHEN PR.ARAP_FLAG = '2' THEN
                            PR.FEE_AMOUNT * -1
                       END) FEE_AMOUNT,
                   SUM(CASE
                         WHEN PR.ARAP_FLAG = '1' THEN 
                          PR.FEE_AMOUNT
                           WHEN PR.ARAP_FLAG = '2'   THEN
                            PR.FEE_AMOUNT * -1
                       END) * 1 JYRMBJE,
                   MAX(PR.MONEY_CODE) MONEY_CODE,
                   MAX(PR.APPLY_CODE) APPLY_CODE,
                   MAX(PR.AGENT_CODE) AGENT_CODE,
                   MAX(PR.AGENT_NAME) AGENT_NAME,
                   MAX(PR.AREA) AREA,
                   MAX(PR.PART) PART,
                   MAX(PR."GROUP") "GROUP",
                   MAX(PR.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
                   MAX(PR.DERIV_TYPE) DERIV_TYPE,
                   MAX(PR.DUE_TIME) CHARGE_YEAR_START,
                   MAX(PR.CERTI_CODE) CERTI_CODE,
                   MAX(PR.CERTI_TYPE) CERTI_TYPE,
                   MAX(PR.PAYEE_PHONE) PAYEE_PHONE,
                   MAX(PR.PAYEE_EMAIL) PAYEE_EMAIL,
                   MAX(CASE WHEN PR.DERIV_TYPE = '003' THEN '0' ELSE '1' END) ISSQYW,
                   MAX(CASE
                         WHEN PR.PREM_FREQ = '1' THEN
                          PR.DUE_TIME
                         WHEN PR.PREM_FREQ = '2' THEN
                          ADD_MONTHS(PR.DUE_TIME, 1)
                         WHEN PR.PREM_FREQ = '3' THEN
                          ADD_MONTHS(PR.DUE_TIME, 3)
                         WHEN PR.PREM_FREQ = '4' THEN
                          ADD_MONTHS(PR.DUE_TIME, 6)
                         WHEN PR.PREM_FREQ = '5' THEN
                          ADD_MONTHS(PR.DUE_TIME, 12)
                         ELSE
                          PR.DUE_TIME
                       END) CHARGE_YEAR_END
       FROM DEV_CAP.T_PREM_ARAP PR, DEV_CAP.T_SERVICE SE ,DEV_CAP.T_SAPCOM_CONFIG SC,
        DEV_PDS.t_Business_Product bp
    WHERE PR.SERVICE_CODE = SE.SERVICE_CODE(+) 
    AND PR.UNIT_NUMBER IS NOT NULL
    AND PR.POLICY_ORGAN_CODE =  SC.SYSCODE(+)
    AND PR.BUSI_PROD_CODE =BP.PRODUCT_CODE_SYS(+)
    AND BP.TAX_EXTENSION_FLAG='1'
    AND PR.FEE_TYPE NOT IN ('G004270000','G004270001') --排除退保手续费（退保费用）
    AND ((PR.DERIV_TYPE <> '001') OR (PR.FEE_STATUS = '16' AND PR.DERIV_TYPE = '001') AND PR.STATISTICAL_DATE IS NOT NULL)
    AND (((PR.ARAP_FLAG='1' OR (PR.ARAP_FLAG='2' AND PR.ROLLBACK_UNIT_NUMBER IS NOT NULL))
    AND EXISTS (SELECT 1 FROM DEV_CAP.T_GL_ACCOUNTING_RULE R
       WHERE R.CR_SEG4 IN ('**********', 
                                 '**********', 
                                 '**********', 
                                 '**********', 
                                 '**********', 
                                 '**********', 
                                 '**********')
             AND R.DR_SEG17 != 'ZC01'
       AND R.BASE1 = PR.FEE_TYPE
       AND R.BASE2 = PR.WITHDRAW_TYPE
       AND PR.FEE_TYPE <> 'G004270001' --退保费用不推送VMS
       AND PR.WITHDRAW_TYPE <>  '003050100A' --年金/生存金转存万能账户不推送VMS
       AND R.FEE_TABLE ='2'))
       OR (PR.ARAP_FLAG='2'
        AND EXISTS (SELECT 1 FROM DEV_CAP.T_GL_ACCOUNTING_RULE R
        WHERE R.DR_SEG4 IN ('**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********',
                                 '**********') 
                AND R.DR_SEG17 != 'ZC01'
        AND R.BASE1 = PR.FEE_TYPE
        AND R.BASE2 = PR.WITHDRAW_TYPE
        AND PR.FEE_TYPE <> 'G004270001' --退保费用不推送VMS
        AND PR.WITHDRAW_TYPE <>  '003050100A' --年金/生存金转存万能账户不推送VMS
        AND R.FEE_TABLE ='2')))
		]]>
		<if test=" is_send != null and is_send != '' or is_send == 0 or is_send == 1 "><![CDATA[ AND PR.IS_SEND = #{is_send} ]]></if>
		<if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND PR.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND PR.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" unit_numbers != null and unit_numbers != '' ">
        <![CDATA[ AND PR.UNIT_NUMBER IN ]]>
        <foreach item="item" index="id" collection="unit_numbers"
            open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        </if>
        <if test=" branch_codes != null and branch_codes != '' ">
        <![CDATA[ AND PR.BRANCH_CODE IN ]]>
        <foreach item="item" index="id" collection="branch_codes"
            open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        </if>
		<if test=" start_insert_time != null and start_insert_time != '' "><![CDATA[ AND PR.STATISTICAL_DATE >= to_date(#{start_insert_time},'YYYY-MM-DD HH24:MI:SS') ]]></if>
		<if test=" end_insert_time != null and end_insert_time != '' "><![CDATA[ AND PR.STATISTICAL_DATE <= to_date(#{end_insert_time},'YYYY-MM-DD HH24:MI:SS') ]]></if>
		<![CDATA[ GROUP BY PR.UNIT_NUMBER,PR.BUSI_PROD_CODE,PR.POLICY_CODE,PR.BUSI_ITEM_ID) T WHERE NOT EXISTS (SELECT 1 FROM DEV_CAP.T_PREM_VMS_INFO A WHERE A.PREM_ARAP_ID = T.LIST_ID)]]>
		<if test=" modnum != null and modnum != '' and start != null and start != '' or modnum >= 0 or start >= 0"><![CDATA[AND MOD(T.LIST_ID, #{modnum})= #{start}  ]]></if>
	</select>
	
	<!-- 查询实付是否重复数据 -->
    <select id="queryCashDetailStatus" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ select COUNT(1) FROM APP___CAP__DBUSER.T_CASH_DETAIL A WHERE 1 = 1 AND A.FEE_STATUS='01']]>
        <if test=" unit_number != null and unit_number != '' "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
    </select>
    
    	   <!-- 集中制返盘手工制盘 分批更新制盘流水号 add 2023-11-3 -->
    <update id="batchUpdatePremArapForTaskMark1"
        parameterType="java.util.Map" >      
         
        <![CDATA[ DECLARE 
				TYPE TYPE_UNIT_NUMBER IS TABLE OF APP___CAP__DBUSER.T_PREM_ARAP.UNIT_NUMBER%TYPE;
				V_UNIT_NUMBER TYPE_UNIT_NUMBER;
				CURSOR TEMP_CURSOR IS
          	select D.UNIT_NUMBER
          	FROM (select CASE WHEN SUM(CASE WHEN A.ARAP_FLAG = '${ar}' THEN FEE_AMOUNT ELSE -1 * FEE_AMOUNT END) > 0 THEN '${ar}' ELSE '${ap}' END FLAG, 
          	A.UNIT_NUMBER, 
          	MAX(A.BANK_CODE) BANK_CODE 
          	FROM APP___CAP__DBUSER.T_PREM_ARAP A 
          	WHERE 1 = 1
          ]]>
        <if test=" autofalg != null and autofalg !='' "><![CDATA[ AND TRUNC(NVL(A.BACK_TEXT_TIME, #{defultDate}), 'dd') <> TRUNC(#{autoDate}, 'dd') ]]></if>
        <include refid="findRecordsGetOrganDate" />
        <![CDATA[ 
        	GROUP BY A.UNIT_NUMBER ) D
        	WHERE 1=1
        ]]>
        <!-- 收费只查询本行数据，付费查询本行以及其他附属分行的数据 -->
        <if test=" arapFlag_bank != null and arapFlag_bank.size()>0">
            <![CDATA[ AND D.FLAG IN ]]>
            <foreach item="item" index="id" collection="arapFlag_bank"
                open="(" separator="," close=");">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        
        <![CDATA[
    BEGIN 
	OPEN TEMP_CURSOR;
	LOOP
		FETCH TEMP_CURSOR BULK COLLECT ]]>
		<choose>
			<when test="commitNum != null and commitNum !='' ">
				<![CDATA[ INTO V_UNIT_NUMBER LIMIT TO_NUMBER(#{commitNum}); ]]>
			</when>
			<otherwise>
				<![CDATA[ INTO V_UNIT_NUMBER LIMIT 10000; ]]>
			</otherwise>
		</choose>
			
<![CDATA[FORALL I IN 1 .. V_UNIT_NUMBER.COUNT 
		UPDATE APP___CAP__DBUSER.T_PREM_ARAP A ]]>
        <set>
        <![CDATA[TASK_MARK = #{task_mark_update,jdbcType=NUMERIC},
        	FEE_STATUS = #{fee_status_way},
        	FEE_STATUS_BY = #{update_by, jdbcType=NUMERIC} ,
            FEE_STATUS_DATE = SYSDATE ,
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ]]>
        </set>
        <![CDATA[ WHERE A.UNIT_NUMBER = V_UNIT_NUMBER(I) ]]>
        <if test="feeStatusList != null and feeStatusList.size()>0  ">
	        <![CDATA[ AND A.FEE_STATUS in ]]>
	        <foreach item="item" index="id" collection="feeStatusList"
	                open="(" separator="," close=")">
	                #{item, jdbcType=VARCHAR}
	        </foreach>
        </if>
        <if test="frozen_status != null and frozen_status != ''"><![CDATA[ AND NVL(A.FROZEN_STATUS, #{frozen_status_solution}) = #{frozen_status} ]]></if>
        <![CDATA[;
         COMMIT;
         EXIT WHEN TEMP_CURSOR%NOTFOUND;
	END LOOP;
	CLOSE TEMP_CURSOR;
	EXCEPTION WHEN OTHERS THEN NULL;
END;]]>
        
    </update>
    
    <select id="arapInfo_Cash" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
           select A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID,  A.BUSI_PROD_NAME, 
            A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, 
            A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE,
            A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
            A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.INSURED_ID, A.POLICY_TYPE, 
            A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
            A.WITHDRAW_TYPE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.CERTI_TYPE, A.IS_BANK_TEXT_BY, A.MONEY_CODE, 
            A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.FINISH_TIME, 
            A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.BELNR, A.LIST_ID, A.HOLDER_ID,
            A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.CUSTOMER_ID, A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, 
            A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY,A.MEDICAL_INSURANCE_CARD,
            A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.AGENT_CODE, A.PREM_FREQ, A.GROUP_CODE, A.GROUP_NAME,A.BUSI_ITEM_ID,
            A.CUS_ACC_DETAILS_ID,A.CUS_ACC_FEE_AMOUNT, A.CUS_ACC_UPDATE_BY, A.CUS_ACC_UPDATE_TIME,A.CIP_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.CIP_BRANCH_BANK_CODE,
            A.CUSTOMER_ACCOUNT_FLAG, A.BATCH_NO, A.FUNDS_RTN_CODE, A.OPERATOR_BY, A.REFEFLAG, A.SERVICE_CODE,A.POLICY_BALANCE,A.IS_CORPORATE,
            NVL(DECODE(R.FILTER2,'Y',R.CR_SEG4,R.DR_SEG4),'DR_NULL') DR_ACCUITEM, NVL(DECODE(R.FILTER2,'Y',R.DR_SEG4,R.CR_SEG4),'CR_NULL') CR_ACCUITEM,A.SPECIAL_ACCOUNT_FLAG,
        ]]>
        <if test=" ar != null and ar != '' ">
            <![CDATA[
             CASE WHEN 
                    ARAP_FLAG ='${ar}'
                THEN
                    FEE_AMOUNT
                ELSE
                    -1*FEE_AMOUNT
                END FEE_AMOUNT
            FROM APP___CAP__DBUSER.T_PREM_ARAP A LEFT JOIN APP___CAP__DBUSER.T_GL_ACCOUNTING_RULE R ON R.FEE_TABLE = '2' AND  A.FEE_TYPE = R.BASE1 AND A.WITHDRAW_TYPE = R.BASE2  
            WHERE 1 = 1 
            ]]>
        </if>
        <if test=" ar == null or ar == '' ">
            <![CDATA[
            FEE_AMOUNT
            FROM APP___CAP__DBUSER.T_PREM_ARAP A LEFT JOIN APP___CAP__DBUSER.T_GL_ACCOUNTING_RULE R ON R.FEE_TABLE = '2' AND  A.FEE_TYPE = R.BASE1 AND A.WITHDRAW_TYPE = R.BASE2  
            WHERE 1 = 1 
            ]]>
        </if>
        <if test=" UnitNumbers != null and UnitNumbers.length>0">
            <![CDATA[ AND UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="UnitNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" deriv_type != null and deriv_type != '' "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type, jdbcType=VARCHAR} ]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ]]></if>
        <![CDATA[ AND A.FEE_STATUS IN ('15','04')]]>
    </select>
    
    <select id="capQueryAmountByUnitNumber_Cash" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
     select D.* FROM
        (select 
              A.UNIT_NUMBER UNIT_NUMBER, 
              MAX(A.ROLLBACK_UNIT_NUMBER) ROLLBACK_UNIT_NUMBER,
              SUM(A.POLICY_BALANCE) POLICY_BALANCE,
              MIN(BUSINESS_CODE) BUSINESS_CODE,
              MAX(A.FEE_STATUS) FEE_STATUS,
              MAX(BUSINESS_TYPE) BUSINESS_TYPE,
              MIN(DERIV_TYPE) DERIV_TYPE,
              MIN(CUSTOMER_ID) CUSTOMER_ID,
              MIN(APPLY_CODE) APPLY_CODE,
              MAX(BANK_ACCOUNT) BANK_ACCOUNT,
              MAX(BANK_CODE) BANK_CODE,
              MAX(BANK_USER_NAME) BANK_USER_NAME,
              MAX(CERTI_TYPE) CERTI_TYPE,
              MAX(CERTI_CODE) CERTI_CODE,
              MIN(BANK_TEXT_STATUS) BANK_TEXT_STATUS,
              CASE WHEN MIN(POLICY_CODE) = MAX(POLICY_CODE) THEN        
                   MIN(POLICY_CODE)
                   WHEN MAX(WITHDRAW_TYPE) = '**********' THEN (SELECT MIN(T.POLICY_CODE) FROM APP___CAP__DBUSER.T_PREM_ARAP T WHERE A.UNIT_NUMBER=T.UNIT_NUMBER AND T.ARAP_FLAG='${ar}')
              ELSE
                   NULL
              END POLICY_CODE,
              MAX(WITHDRAW_TYPE) WITHDRAW_TYPE, 
              MIN(PAYEE_NAME) PAYEE_NAME,                                    
              MIN(PAY_MODE) PAY_MODE,                                           
              MAX(ARAP_FLAG) ARAP_FLAG,                                         
              SUM(                                                                
                   CASE WHEN 
                      ARAP_FLAG ='${ar}' 
                       THEN 
                        FEE_AMOUNT 
                      ELSE 
                        -1*FEE_AMOUNT 
                       END
                     ) FEE_AMOUNT,                                            
              
              MIN(DUE_TIME) DUE_TIME,                               
              MAX(HOLDER_NAME)  HOLDER_NAME,   
              MAX(HOLDER_ID)  HOLDER_ID,                          
              MAX(INSURED_NAME)  INSURED_NAME,
              MAX(GROUP_CODE)  GROUP_CODE,                                 
              MAX(GROUP_NAME)  GROUP_NAME, 
              MAX(OPERATOR_BY)  OPERATOR_BY,
	      (CASE WHEN MAX(A.DERIV_TYPE) = '001' THEN MAX(U.USER_NAME) END) insert_name,
              MAX(A.SPECIAL_ACCOUNT_FLAG) SPECIAL_ACCOUNT_FLAG
        FROM APP___CAP__DBUSER.T_PREM_ARAP A
	LEFT JOIN APP___CAP__DBUSER.T_UDMP_USER U
        ON A.INSERT_BY = U.USER_ID
        WHERE 1=1
        ]]>
        <if test=" UnitNumbers != null and UnitNumbers.length>0">
            <![CDATA[ AND UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="UnitNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test=" customer_id != null and customer_id !='' ">
            <![CDATA[ AND CUSTOMER_ID = #{customer_id} ]]>
        </if>
        <if test="due_time != null and due_time != '' ">
        	<![CDATA[ AND A.DUE_TIME > #{due_time}  AND A.FEE_STATUS IN ('15','04')]]>
        </if>
        <![CDATA[
        GROUP BY A.UNIT_NUMBER) D
         ]]>
    </select>
    
    <!-- 通过保单号+险种代码查询首期收费LIST_ID (保单调收费)-->
    <select id="capQueryListId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ 
	        SELECT A.POLICY_CODE, 
			       A.BUSI_PROD_CODE, 
			       A.LIST_ID
			  FROM APP___CAP__DBUSER.T_PREM_ARAP A
			 WHERE 1 = 1
			   AND A.ARAP_FLAG = '1'
			   AND A.DERIV_TYPE = '001'
        ]]>
        <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
    </select>
    
    <!-- 根据unitNumber查询应收应付，校验费用状态、金额-->
    <select id="findPremArapByUnitNumberCheck" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ 
	        SELECT MAX(PA.UNIT_NUMBER),
       MAX(PA.FEE_STATUS),
       SUM(CASE
             WHEN ARAP_FLAG = '1' THEN
              FEE_AMOUNT
             ELSE
              -1 * FEE_AMOUNT
           END) FEE_AMOUNT
			  FROM APP___CAP__DBUSER.T_PREM_ARAP PA
			 WHERE 1 = 1]]>
         <if test=" unit_number != null and unit_number != '' ">
         <![CDATA[ AND PA.UNIT_NUMBER = #{unit_number} ]]></if>
    </select>
    
    <!-- 根据unitNumber更新应收应付表 -->
    <update id="updatePremArapStatusByUnitNumbers" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CAP__DBUSER.T_PREM_ARAP ]]>
        <set>
            FEE_STATUS = '01',
            FINISH_TIME = SYSDATE,
            STATISTICAL_DATE = SYSDATE,
            UPDATE_BY = #{update_by, jdbcType=NUMERIC},
			UPDATE_TIME = SYSDATE,
            UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
        </set>
        <if test=" UnitNumbers != null and UnitNumbers.length>0">
            <![CDATA[ WHERE UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="UnitNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
    
    <select id="queryIsRollback" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
     		SELECT COUNT(1) FROM DEV_CAP.T_PREM_ARAP T WHERE 1=1 ]]>
     		<if test="rel_unit_number != null and rel_unit_number != '' ">
        	<![CDATA[ AND T.ROLLBACK_UNIT_NUMBER = #{rel_unit_number}]]>
        </if>
    </select>
    
    <select id="querypolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[SELECT MAX(T.APPLY_CODE) APPLY_CODE,
       MAX(T.POLICY_CODE) POLICY_CODE,
       MAX(T.CUSTOMER_ID) CUSTOMER_ID,
       MAX(T.HOLDER_ID) HOLDER_ID,
       MAX(T.HOLDER_NAME) HOLDER_NAME
		  FROM APP___CAP__DBUSER.T_PREM_ARAP T
		 WHERE 1 = 1]]>
   <if test=" unitNumbers != null and unitNumbers.length>0 ">
            <![CDATA[ AND T.UNIT_NUMBER IN]]>
            <foreach item="item" index="id" collection="unitNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
 	<![CDATA[ GROUP BY T.UNIT_NUMBER ]]>
    </select> 
</mapper>       
