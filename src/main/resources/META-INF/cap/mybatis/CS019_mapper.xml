<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="CS019">
    <!-- 产品转移日结-->
    <!-- 产品转移日结  非回退项zhanggh6_wb 修改2023-08-11-->
    <select id="findCS019Part1" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
       SELECT T.BUSI_PROD_NAME STRING1,
           (SELECT Z.SALES_CHANNEL_NAME
             FROM DEV_CAP.T_SALES_CHANNEL Z
             WHERE Z.SALES_CHANNEL_CODE = T.CHANNEL_TYPE) STRING2,
            (CASE WHEN T.CHANNEL_TYPE IN ('03', '08') AND LENGTH(T.CIP_BANK_CODE) = 2 THEN T.CIP_BANK_CODE 
           WHEN T.CHANNEL_TYPE IN ('03', '08') AND LENGTH(T.CIP_BANK_CODE) > 2 THEN SUBSTR(T.CIP_BANK_CODE,0,2)
           ELSE '' END) STRING3,  --添加银代、财富渠道的银行代码 
          T.BUSI_PROD_CODE STRING4, 
            SUM(CASE
                 WHEN (T.FEE_TYPE = 'P004910500' AND
                       T.WITHDRAW_TYPE IN ('**********'))
                       THEN  DECODE(T.ROLLBACK_UNIT_NUMBER,NULL,T.FEE_AMOUNT,-T.FEE_AMOUNT) 
                 ELSE
                  0
               END) NUMBER1, --金额
           0 NUMBER2, --转出费用
           0 NUMBER3, --增值税
           COUNT(DISTINCT T.UNIT_NUMBER) NUMBER4
      FROM DEV_CAP.T_PREM_ARAP T
     WHERE T.ARAP_FLAG IN ('1', '2') --优化语句
       AND (T.FEE_TYPE = 'P004910500' AND T.WITHDRAW_TYPE IN ('**********')) 
       AND T.BOOKKEEPING_FLAG = '1'
       AND T.POLICY_ORGAN_CODE IN
           (SELECT O.ORGAN_CODE
              FROM DEV_PAS.T_UDMP_ORG_REL O
             START WITH O.ORGAN_CODE = #{organ_code}
            CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
       AND T.STATISTICAL_DATE >=
           TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
       AND T.STATISTICAL_DATE <
           TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
     GROUP BY T.BUSI_PROD_NAME,T.BUSI_PROD_CODE, T.CHANNEL_TYPE,T.CIP_BANK_CODE
        ]]>
    </select>
    
    <!--产品转移日结  非回退项zhanggh6_wb修改2023-08-11  -->
    <select id="findCS019Part2" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
     	SELECT T.BUSI_PROD_NAME STRING1,
		       (SELECT Z.SALES_CHANNEL_NAME
		        FROM DEV_CAP.T_SALES_CHANNEL Z
		        WHERE Z.SALES_CHANNEL_CODE = T.CHANNEL_TYPE) STRING2,
		        (CASE WHEN T.CHANNEL_TYPE IN ('03', '08') AND LENGTH(T.CIP_BANK_CODE) = 2 THEN T.CIP_BANK_CODE 
			     WHEN T.CHANNEL_TYPE IN ('03', '08') AND LENGTH(T.CIP_BANK_CODE) > 2 THEN SUBSTR(T.CIP_BANK_CODE,0,2)
			     ELSE '' END) STRING3,  --添加银代、财富渠道的银行代码 
			    T.BUSI_PROD_CODE STRING4, 
		        SUM(CASE
		             WHEN (T.FEE_TYPE = 'G004910500' AND
		                   T.WITHDRAW_TYPE IN ('**********'))
		             THEN  DECODE(T.ROLLBACK_UNIT_NUMBER,NULL,T.FEE_AMOUNT,-T.FEE_AMOUNT) 
		             ELSE
		              0
		           END) NUMBER1, --金额
		       0 NUMBER2, --转出费用
		       0 NUMBER3, --增值税
		       COUNT(DISTINCT T.UNIT_NUMBER) NUMBER4
		  FROM DEV_CAP.T_PREM_ARAP T
		 WHERE T.ARAP_FLAG IN ('1', '2') --优化语句
		   AND (T.FEE_TYPE = 'G004910500' AND T.WITHDRAW_TYPE IN ('**********')
		       )
		   AND T.BOOKKEEPING_FLAG = '1'
		   AND T.POLICY_ORGAN_CODE IN
		       (SELECT O.ORGAN_CODE
		          FROM DEV_PAS.T_UDMP_ORG_REL O
		         START WITH O.ORGAN_CODE = #{organ_code}
		        CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
		   AND T.STATISTICAL_DATE >=
		       TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
		   AND T.STATISTICAL_DATE <
		       TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
		 GROUP BY T.BUSI_PROD_NAME,T.BUSI_PROD_CODE ,  T.CHANNEL_TYPE,T.CIP_BANK_CODE
        ]]>
    </select>
    
    <!-- 清单SQL -->
    <select id="findCS019qd" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT 
           A.STRING1,
           A.STRING2,
          MAX(A.STRING3) STRING3,
          MAX((SELECT Z.SALES_CHANNEL_NAME
          FROM DEV_CAP.T_SALES_CHANNEL Z
           WHERE Z.SALES_CHANNEL_CODE = A.STRING4)) STRING4,
          MAX(A.STRING5) STRING5,
          MAX(A.STRING6) STRING6,
          MAX(A.STRING7) STRING7,
          MAX(A.STRING8) STRING8,
          MAX(A.STRING9) STRING9,
          MAX(A.NUMBER1) NUMBER1,
          MAX(A.NUMBER2) NUMBER2,
          MAX(A.NUMBER3) NUMBER3,
          MAX(A.NUMBER4) NUMBER4,
          MAX(A.NUMBER5) NUMBER5,
          MAX(A.NUMBER6)NUMBER6
      FROM (SELECT MAX(CASE
                         WHEN T.DERIV_TYPE = '004' THEN
                          T.BUSINESS_CODE
                         ELSE
                          ''
                       END) STRING1, --保全受理号
                   MAX(T.POLICY_CODE) STRING2,
                   (CASE WHEN T.FEE_TYPE IN  ('G004910500') AND T.WITHDRAW_TYPE IN ('**********')
                   THEN  MAX((SELECT CONCAT( CONCAT(UR.ORGAN_CODE,' '),UR.ORGAN_NAME) FROM DEV_PAS.T_UDMP_ORG_REL UR WHERE UR.ORGAN_CODE = T.POLICY_ORGAN_CODE))
                   ELSE '' END)
                    STRING3, --管理机构 
                   (CASE WHEN T.FEE_TYPE IN  ('G004910500') AND T.WITHDRAW_TYPE IN ('**********')
                   THEN   MAX(T.CHANNEL_TYPE)
                   ELSE '' END)
                   STRING4, --销售渠道
                   (CASE WHEN T.FEE_TYPE IN  ('G004910500') AND T.WITHDRAW_TYPE IN ('**********')
                   THEN  
                   (CASE WHEN T.CHANNEL_TYPE IN ('03', '08') AND LENGTH(T.CIP_BANK_CODE) = 2 THEN T.CIP_BANK_CODE 
                 WHEN T.CHANNEL_TYPE IN ('03', '08') AND LENGTH(T.CIP_BANK_CODE) > 2 THEN SUBSTR(T.CIP_BANK_CODE,0,2)
                 ELSE '' END) ELSE '' END) STRING5, 
                   TO_CHAR(MAX(CST.REVIEW_TIME), 'yyyy-MM-dd') STRING6, --保全复核通过日
                   TO_CHAR(MAX(CST.VALIDATE_TIME), 'yyyy-MM-dd') STRING7, --保全确认日期
                   MAX(CASE
                         WHEN T.FEE_TYPE IN
                              ('P004910500') AND T.WITHDRAW_TYPE IN ('**********') THEN
                         T.BUSI_PROD_NAME
                         ELSE
                          ''
                       END) STRING8, --原险种
                   SUM(CASE
                         WHEN T.ROLLBACK_UNIT_NUMBER IS NULL AND
                              T.FEE_TYPE IN
                              ('P004910500') AND T.WITHDRAW_TYPE IN ('**********') THEN
                          T.FEE_AMOUNT
                         WHEN T.ROLLBACK_UNIT_NUMBER IS NOT NULL AND
                              T.FEE_TYPE IN
                              ('P004910500') AND T.WITHDRAW_TYPE IN ('**********') THEN
                          T.FEE_AMOUNT * -1
                         ELSE
                          0
                       END) NUMBER1, --原险种金额
                   0 NUMBER2, --原险种转出费用
                   0 NUMBER3, --原险种转出费用增值税
                   MAX(CASE
                         WHEN T.FEE_TYPE IN ('G004910500') AND T.WITHDRAW_TYPE IN ('**********') THEN
                         T.BUSI_PROD_NAME
                         ELSE
                          ''
                       END) STRING9, --转移后险种
                   SUM(CASE
                         WHEN T.ROLLBACK_UNIT_NUMBER IS NULL AND
                              T.FEE_TYPE IN  ('G004910500') AND T.WITHDRAW_TYPE IN ('**********') THEN
                          T.FEE_AMOUNT
                         WHEN T.ROLLBACK_UNIT_NUMBER IS NOT NULL AND
                              T.FEE_TYPE IN  ('G004910500') AND T.WITHDRAW_TYPE IN ('**********') THEN
                          T.FEE_AMOUNT * -1
                         ELSE
                          0
                       END) NUMBER4, --转换后金额
                   0 NUMBER5, --转出费用
                       0 NUMBER6 --转出费用增值税
              FROM DEV_CAP.T_PREM_ARAP T
              LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CST
                ON T.BUSINESS_CODE = CST.ACCEPT_CODE
             WHERE T.ARAP_FLAG IN ('1', '2') --优化语句 
               AND T.BOOKKEEPING_FLAG = '1'
               AND T.FEE_TYPE IN ('G004910500', 'P004910500') 
               AND T.WITHDRAW_TYPE IN ('**********')  
               AND T.POLICY_ORGAN_CODE IN
                   (SELECT O.ORGAN_CODE
                      FROM DEV_PAS.T_UDMP_ORG_REL O
                     START WITH O.ORGAN_CODE = #{organ_code}
                    CONNECT BY PRIOR O.ORGAN_CODE = O.UPORGAN_CODE)
               AND T.STATISTICAL_DATE >=
                   TO_DATE(TO_CHAR(#{start_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
               AND T.STATISTICAL_DATE <
                  TO_DATE(TO_CHAR(#{end_date}, 'yyyy-MM-dd'), 'yyyy-MM-dd') + 1
                 GROUP BY T.BUSINESS_CODE,T.POLICY_CODE,T.CHANNEL_TYPE,T.FEE_TYPE,T.WITHDRAW_TYPE,T.CIP_BANK_CODE) A
                 GROUP BY A.STRING1, A.STRING2
     ORDER BY A.STRING1, A.STRING2
        ]]>
    </select>
</mapper>
