<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="STATEMENT_TO_SAP">
<!-- 按索引生成的查询条件 -->	
	<sql id="CAP_queryStatementToSapByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="CAP_queryStatementToSapByFileNameCondition">
		<if test=" file_name != null and file_name != '' "><![CDATA[ AND A.FILE_NAME = #{file_name} ]]></if>
		<if test=" review_status != null and review_status != ''  "><![CDATA[ AND A.REVIEW_STATUS = #{review_status} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="CAP_addStatementToSap"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___CAP__DBUSER.S_STA_TO_SAP__ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_CAP.T_STATEMENT_TO_SAP(
				STATEMENT_PERIOD, INSERT_TIMESTAMP, FILE_NAME, UPDATE_BY, INSERT_TIME, VARIANCE_DATA, REVIEW_STATUS, 
				LIST_ID, STATEMENT_DATA, UPDATE_TIMESTAMP, UPDATE_TIME, INSERT_BY ) 
			VALUES (
				#{statement_period, jdbcType=VARCHAR}, CURRENT_TIMESTAMP, #{file_name, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{variance_data, jdbcType=CLOB} , #{review_status, jdbcType=VARCHAR} 
				, #{list_id, jdbcType=NUMERIC} , #{statement_data, jdbcType=CLOB} , CURRENT_TIMESTAMP, SYSDATE , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="CAP_deleteStatementToSap" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_CAP.T_STATEMENT_TO_SAP WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="CAP_batchUpdateStatementToSap" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_CAP.T_STATEMENT_TO_SAP ]]>
		<set>
		<trim suffixOverrides=",">
		    <if test=" variance_data != null and variance_data != ''  ">
		    	<![CDATA[VARIANCE_DATA = #{variance_data, jdbcType=CLOB} ,]]>
		    </if>
			REVIEW_STATUS = #{review_status, jdbcType=VARCHAR},
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC},
			UPDATE_TIME = SYSDATE, 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} AND REVIEW_STATUS <> '1' ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="CAP_findStatementToSapByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATEMENT_PERIOD, A.FILE_NAME, A.VARIANCE_DATA, A.REVIEW_STATUS, 
			A.LIST_ID, A.STATEMENT_DATA FROM DEV_CAP.T_STATEMENT_TO_SAP A WHERE 1 = 1  ]]>
		<include refid="CAP_queryStatementToSapByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="CAP_findStatementToSapByFileName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATEMENT_PERIOD, A.FILE_NAME, A.VARIANCE_DATA VARIANCE_DATA, A.REVIEW_STATUS, 
			A.LIST_ID, A.STATEMENT_DATA STATEMENT_DATA FROM DEV_CAP.T_STATEMENT_TO_SAP A WHERE 1 = 1  ]]>
		<include refid="CAP_queryStatementToSapByFileNameCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="CAP_findAllMapStatementToSap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATEMENT_PERIOD, A.FILE_NAME, A.VARIANCE_DATA, A.REVIEW_STATUS, 
			A.LIST_ID, A.STATEMENT_DATA FROM DEV_CAP.T_STATEMENT_TO_SAP A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="CAP_findAllStatementToSap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATEMENT_PERIOD, A.FILE_NAME, A.VARIANCE_DATA, A.REVIEW_STATUS, 
			A.LIST_ID, A.STATEMENT_DATA FROM DEV_CAP.T_STATEMENT_TO_SAP A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="CAP_findStatementToSapTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_CAP.T_STATEMENT_TO_SAP A WHERE 1 = 1  ]]>
		<if test=" file_name != null and file_name != ''  "><![CDATA[ AND A.FILE_NAME = #{file_name} ]]></if>
		<if test=" review_status != null and review_status != ''  "><![CDATA[ AND A.REVIEW_STATUS = #{review_status} ]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="CAP_queryStatementToSapForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.STATEMENT_PERIOD, B.FILE_NAME, B.VARIANCE_DATA, B.REVIEW_STATUS, 
			B.LIST_ID, B.STATEMENT_DATA FROM (
					SELECT ROWNUM RN, A.STATEMENT_PERIOD, A.FILE_NAME, A.VARIANCE_DATA, A.REVIEW_STATUS, 
			A.LIST_ID, A.STATEMENT_DATA FROM DEV_CAP.T_STATEMENT_TO_SAP A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
		<update id="CAP_updateStatementToSap" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_CAP.T_STATEMENT_TO_SAP ]]>
		<set>
		<trim suffixOverrides=",">
		<![CDATA[	STATEMENT_PERIOD = #{statement_period, jdbcType=VARCHAR} ,
			FILE_NAME = #{file_name, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    VARIANCE_DATA = #{variance_data, jdbcType=CLOB} ,
			REVIEW_STATUS = #{review_status, jdbcType=VARCHAR} ,
		    STATEMENT_DATA = #{statement_data, jdbcType=CLOB} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , ]]>
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<select id="CAP_findMaxStatementToSapByFileName" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT *
  					FROM (SELECT *
			          FROM DEV_CAP.T_STATEMENT_TO_SAP S
			         WHERE S.FILE_NAME = #{file_name, jdbcType=VARCHAR}
			           AND S.REVIEW_STATUS = '1'
			         ORDER BY S.UPDATE_TIME DESC) B
			 	WHERE ROWNUM = 1 ]]>
	</select>
</mapper>
