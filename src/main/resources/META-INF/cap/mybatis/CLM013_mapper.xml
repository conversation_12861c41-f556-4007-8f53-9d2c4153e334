<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CLM013">
    <!-- 预付赔款日结 -->
    <select id="findpart1CLM013" resultType="java.util.Map"
        parameterType="java.util.Map">        
        <![CDATA[ 
			   
			SELECT TO_CHAR(ROWNUM) STRING1,A.* FROM (SELECT B.STRING2,B.STRING5,B.STRING3,STRING4,SUM(B.NUMBER1) NUMBER1 FROM 
				(SELECT T.BUSI_PROD_NAME STRING2,T.BUSI_PROD_CODE STRING5, 
			            NVL(SUM(CASE WHEN T.FEE_TYPE IN ('P005140100', 'G005140200') 
			            AND T.WITHDRAW_TYPE IN ('0050901000','0051001000','0050902020','0051002020','0050902010','0051002010','0050903020','0051003020','0051003010','0051004020','0051004010','0050903010') THEN DECODE(T.ARAP_FLAG,'2',T.FEE_AMOUNT,-T.FEE_AMOUNT) ELSE 0 END),0) NUMBER1,
			            (CASE WHEN T.WITHDRAW_TYPE IN ('0050902010','0050903010','0051002010','0051003010') THEN '死亡给付' 
			                  WHEN T.WITHDRAW_TYPE IN ('0050901000','0051001000') THEN '赔款支出'
			                  WHEN T.WITHDRAW_TYPE IN ('0050902020','0051002020') THEN '医疗给付' ELSE '' END ) STRING3,
			            (select SALES_CHANNEL_NAME from DEV_CAP.T_SALES_CHANNEL S
                           where S.Sales_Channel_Code = T.Channel_Type) STRING4
			            FROM DEV_CAP.T_PREM_ARAP T
			            WHERE 1 = 1
			                AND T.ARAP_FLAG in ('1','2')
			                AND T.BOOKKEEPING_FLAG ='1'
			                AND T.FEE_TYPE IN ('P005140100', 'G005140200')
			                AND T.WITHDRAW_TYPE IN ('0050901000','0051001000','0050902020','0051002020','0050902010','0051002010','0050903020','0051003020','0051003010','0051004020','0051004010','0050903010') 
			                AND T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
			                    START WITH O.ORGAN_CODE=#{organ_code}
			                    CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
			                AND T.STATISTICAL_DATE>=TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
							AND T.STATISTICAL_DATE<TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1
			 GROUP BY T.BUSI_PROD_NAME,T.BUSI_PROD_CODE,T.WITHDRAW_TYPE,T.Channel_Type )B GROUP BY B.STRING2, B.STRING5, B.STRING3,STRING4
			 ) A 
		 ]]>
    </select>
    
     <select id="findpart1CLM013b" resultType="java.util.Map"
        parameterType="java.util.Map">        
        <![CDATA[ 
               SELECT  ROWNUM RN ,J.*  FROM 
        (SELECT STRING1,STRING2,STRING3,STRING4,STRING5,STRING6,STRING7,STRING8,STRING9,SUM(NUMBER1) NUMBER1 FROM
        (SELECT T.Business_Code STRING1,
                T.policy_code STRING2,
                (SELECT CONCAT( CONCAT(UR.ORGAN_CODE,' '),UR.ORGAN_NAME) FROM DEV_PAS.T_UDMP_ORG_REL UR WHERE UR.ORGAN_CODE = T.POLICY_ORGAN_CODE) STRING3, 
                T.BUSI_PROD_NAME   STRING4,
                TO_CHAR(MAX(A.ADVANCE_FINISH_TIME),'YYYY-MM-DD') STRING5,
            TO_CHAR(MAX(A.ADVANCE_FINISH_TIME),'YYYY-MM-DD') STRING6,
            (CASE WHEN T.ROLLBACK_UNIT_NUMBER IS NOT NULL THEN '回退' ELSE '' END ) STRING8,
            (CASE WHEN T.WITHDRAW_TYPE IN ('0050902010','0050903010','0051002010','0051003010') THEN '死亡给付' 
                  WHEN T.WITHDRAW_TYPE IN ('0050901000','0051001000') THEN '赔款支出'
                  WHEN T.WITHDRAW_TYPE IN ('0050902020','0051002020') THEN '医疗给付' ELSE '' END ) STRING9,
               SUM(CASE WHEN T.ARAP_FLAG = '2' then T.FEE_AMOUNT ELSE -1*T.FEE_AMOUNT END) NUMBER1,
               (select SALES_CHANNEL_NAME
                  from DEV_CAP.T_SALES_CHANNEL S
                 where S.Sales_Channel_Code = T.Channel_Type) STRING7
                  FROM DEV_CAP.T_PREM_ARAP T
                  left join DEV_CLM.T_CLAIM_CASE ee 
       ON T.BUSINESS_CODE = EE.CASE_NO
       LEFT JOIN DEV_CLM.T_CLAIM_ADVANCE_PAY A ON A.CASE_ID=EE.CASE_ID
                  WHERE 1 = 1
                     -- AND T.ARAP_FLAG = '2'
                      AND T.BOOKKEEPING_FLAG ='1'
                      AND T.FEE_TYPE IN ('P005140100', 'G005140200')
                      AND T.WITHDRAW_TYPE IN ('0050901000','0051001000','0050902020','0051002020','0050902010','0051002010','0050903020','0051003020','0051003010','0051004020','0051004010','0050903010') 
                      AND T.POLICY_ORGAN_CODE IN (SELECT O.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL O
                          START WITH O.ORGAN_CODE= #{organ_code}
                          CONNECT BY PRIOR O.ORGAN_CODE=O.UPORGAN_CODE)
                       AND T.STATISTICAL_DATE>=TO_DATE(TO_CHAR(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')
            AND T.STATISTICAL_DATE<TO_DATE(TO_CHAR(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd')+1
       GROUP BY T.Business_Code,
                T.policy_code,
                T.policy_organ_code,
                T.BUSI_PROD_NAME,
                T.Channel_Type,
                T.ROLLBACK_UNIT_NUMBER,
                T.WITHDRAW_TYPE
   )B  GROUP BY B.STRING1,B.STRING2,B.STRING3,B.STRING4,B.STRING5,B.STRING6,B.STRING7,B.STRING8,B.STRING9
  )J
  ORDER BY j.STRING4,j.STRING5,j.STRING1,j.STRING2
			 ]]>
    </select>
</mapper>