<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="changCapTempfee">


    <!-- 查询可变更的收付费信息总数 -->
    <select id="changeTempfeeTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT COUNT(UNIT_NUMBER) 
        FROM (
                SELECT TBD.UNIT_NUMBER
                 FROM DEV_CAP.T_BILL_DETAILS TBD
                  WHERE TBD.DELETE_FLAG IS NULL
                  AND TBD.UNIT_NUMBER IN (SELECT T.UNIT_NUMBER FROM DEV_CAP.T_PREM_ARAP T WHERE T.UNIT_NUMBER = TBD.UNIT_NUMBER 
                      AND T.Fee_Status = '15'
                  ]]>
                  <if test=" business_code != null and business_code != ''  ">
       			 <![CDATA[ 
                            AND T.BUSINESS_CODE = #{business_code}
          			]]></if>
                  <![CDATA[)
                  AND TBD.UNIT_NUMBER LIKE 'S%'
                  AND NOT EXISTS
                 (SELECT 1
                          FROM DEV_CAP.T_TEMPFEE_CHANGE_LOG T
                         WHERE TBD.UNIT_NUMBER = T.UNIT_NUMBER
                           AND T.CHECK_STATUS IN ('1', '2'))]]>
        <include refid="changeOfTempfeeWhereClause" />
        <![CDATA[
        )
        ]]>
    </select>
    <!-- 查询可变更的收付费信息 -->
    <select id="changeTempfeeSearch" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT M.*
   					FROM (SELECT ROWNUM RN, D.*
          					 FROM (
          					 SELECT
          					  TCL.TEMPFEE_LOG_ID TEMPFEE_LOG_ID,
          					  TBD.BILL_ID BILL_ID,
          					  DECODE(TBD.Bill_Mode,
                               '22', (SELECT TC.CHEQUE_NO  FROM DEV_CAP.T_CHEQUE TC
                                 WHERE TC.CHEQUE_ID = TBD.BILL_MAIN_ID),
                               '') CHEQUE_NO,
                        		((SELECT TPA.BUSINESS_CODE
                           			FROM DEV_CAP.T_PREM_ARAP TPA
                          		 WHERE TPA.UNIT_NUMBER = TBD.UNIT_NUMBER
                            	   AND ROWNUM = 1)) BUSINESS_CODE,
                        TBD.UNIT_NUMBER UNIT_NUMBER,    	   
                        TBD.FEE_AMOUNT FEE_AMOUNT,
                        DECODE(TBD.BILL_MODE,
                               '31',
                               (SELECT TBT.BANK_CODE
                                  FROM DEV_CAP.T_BANK_TRANSFER TBT
                                 WHERE TBT.BTR_ID = TBD.BILL_MAIN_ID),
                               '22',
                               (SELECT TC.BANK_CODE
                                  FROM DEV_CAP.T_CHEQUE TC
                                 WHERE TC.CHEQUE_ID = TBD.BILL_MAIN_ID),
                               '') BANK_CODE,
                        DECODE(TBD.BILL_MODE,
                               '31',
                               (SELECT TBT.BANK_ACCOUNT
                                  FROM DEV_CAP.T_BANK_TRANSFER TBT
                                 WHERE TBT.BTR_ID = TBD.BILL_MAIN_ID),
                               '') BANK_ACCOUNT,
                        TBD.BILL_MODE BILL_MODE,
                        decode(TBD.Bill_Mode,
                               '31',
                               (SELECT tbt.bank_user_name
                                  FROM DEV_CAP.T_BANK_TRANSFER tbt
                                 where tbt.btr_id = tbd.bill_main_id),
                               '') BANK_USER_NAME,
                        TCL.APPLY_TYPE APPLY_TYPE,
                        TCL.APPLY_USER APPLY_USER,
                        TBD.ARAP_DATE ARAP_DATE,
                        TCL.APPLY_DATE APPLY_DATE,
                        TCL.CHECK_DATE CHECK_DATE,
                        (SELECT TPCCS.STATUS_NAME  FROM APP___PAS__DBUSER.T_PREM_CHG_CHECK_STATUS TPCCS WHERE TPCCS.STATUS_CODE=TCL.CHECK_STATUS)
                         CHECK_STATUS,
                        TCL.REJECT_REASON REJECT_REASON,
                        TCL.APPLY_REASON APPLY_REASON,
                        TCL.REMARK REMARK
                   FROM DEV_CAP.T_BILL_DETAILS TBD
                   LEFT JOIN (SELECT T.TEMPFEE_LOG_ID,
			                    T.UNIT_NUMBER,
			                    T.APPLY_TYPE,
			                    T.APPLY_USER,
			                    T.APPLY_REASON,
			                    T.REJECT_REASON,
			                    T.APPLY_DATE,
			                    T.CHECK_DATE,
			                    T.CHECK_STATUS,
			                    T.REMARK
			               FROM DEV_CAP.T_TEMPFEE_CHANGE_LOG T
			              WHERE T.CHECK_STATUS NOT IN ('1', '2')
			                 OR T.CHECK_STATUS IS NULL) TCL
                     ON TBD.UNIT_NUMBER = TCL.UNIT_NUMBER
                  WHERE  1 = 1 
                 	AND TBD.DELETE_FLAG IS NULL
                    AND TBD.UNIT_NUMBER IN (SELECT T.UNIT_NUMBER  FROM DEV_CAP.T_PREM_ARAP T 
                        		  WHERE T.UNIT_NUMBER= TBD.UNIT_NUMBER
                    	 		    AND T.Fee_Status = '15'
                  ]]>
                  <if test=" business_code != null and business_code != ''  ">
       			 <![CDATA[ 
                    			   AND T.BUSINESS_CODE = #{business_code}
          			]]></if>
                  <![CDATA[)
                 	AND TBD.UNIT_NUMBER LIKE 'S%'
                 	 ]]>
                  <include refid="changeOfTempfeeWhereClause" />
                 <![CDATA[
                 	
                	  ) D
          			WHERE ROWNUM <= #{LESS_NUM}) M
 					 WHERE M.RN > #{GREATER_NUM}
                 ]]>                              
    </select>
    <!-- 票据信息查询 -->
    <sql id="changeOfTempfeeWhereClause">
       <if test="cheque_no !=null and cheque_no !=''">
       <![CDATA[
        AND EXISTS( SELECT 1  FROM DEV_CAP.T_CHEQUE TC WHERE 
                     TC.CHEQUE_ID=TBD.BILL_MAIN_ID AND TC.CHEQUE_NO=#{cheque_no}
		 )]]></if>
        <if test=" apply_user != null and apply_user != '' "><![CDATA[ AND TCL.APPLY_USER = #{apply_user} ]]></if>
        <if test=" apply_date != null and apply_date != '' "><![CDATA[ AND TCL.APPLY_DATE = TRUNC(#{apply_date})]]></if>
        <if test=" apply_type != null and apply_type != '' "><![CDATA[ AND TCL.APPLY_TYPE = #{apply_type} ]]></if>
        <if test=" bill_mode != null and bill_mode != ''  "><![CDATA[ AND TBD.BILL_MODE = #{bill_mode} ]]></if>
        
		<if test=" ar_startdate !=null and ar_startdate != '' "><![CDATA[ AND TBD.ARAP_DATE >=  TRUNC(#{ar_startdate}) ]]></if>
		<if test=" ar_enddate != null and ar_enddate != '' "><![CDATA[ AND TBD.ARAP_DATE <= TRUNC(#{ar_enddate}) ]]></if>
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND TBD.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" check_status != null and check_status != '' "><![CDATA[ AND TCL.CHECK_STATUS = #{check_status}]]></if>
		<if test=" check_user != null and check_user != '' "><![CDATA[ AND TCL.apply_user <> #{check_user}]]></if>
		<if test=" tempfee_log_id != null and tempfee_log_id != '' "><![CDATA[ AND TCL.TEMPFEE_LOG_ID = #{tempfee_log_id}]]></if>
    </sql>
    
    <sql id="changerOfChangelogWhereClause">
    	<if test="cheque_no !=null and cheque_no !=''">
       <![CDATA[
       		 AND EXISTS( SELECT 1  FROM DEV_CAP.T_CHEQUE TC,DEV_CAP.T_BILL_DETAILS TBD WHERE TBD.UNIT_NUMBER=TCL.UNIT_NUMBER 
       		 AND TC.CHEQUE_ID=TBD.BILL_MAIN_ID AND TC.CHEQUE_NO=#{cheque_no}
		 )]]></if>
		<if test=" tempfee_log_id != null and tempfee_log_id != '' "><![CDATA[ AND TCL.TEMPFEE_LOG_ID = #{tempfee_log_id}]]></if>
		<if test=" unit_number != null and unit_number != '' "><![CDATA[ AND TCL.UNIT_NUMBER = #{unit_number} ]]></if>
        <if test=" apply_user != null and apply_user != '' "><![CDATA[ AND TCL.APPLY_USER = #{apply_user} ]]></if>
        <if test=" apply_date != null and apply_date != '' "><![CDATA[ AND TCL.APPLY_DATE = TRUNC(#{apply_date})]]></if>
        <if test=" apply_type != null and apply_type != '' "><![CDATA[ AND TCL.APPLY_TYPE = #{apply_type} ]]></if>
        <if test=" bill_mode != null and bill_mode != ''  "> <![CDATA[ AND TCL.PAY_MODE = #{bill_mode} ]]></if>
        <if test=" check_status != null and check_status != '' "><![CDATA[ AND TCL.CHECK_STATUS = #{check_status}]]></if>
        <if test=" check_user != null and check_user != '' "><![CDATA[ AND TCL.apply_user <> #{check_user}]]></if>
    </sql>
    
    <!-- 查询轨迹表TEMPFEE_LOG_ID -->
    <select id="findTempfeeLogId" parameterType="java.util.Map" resultType="java.util.Map">
        <![CDATA[ select T.TEMPFEE_LOG_ID from APP___CAP__DBUSER.T_TEMPFEE_CHANGE_LOG T 
                   WHERE (T.CHECK_STATUS NOT IN ('1','2') OR T.CHECK_STATUS IS NULL)
                     AND UNIT_NUMBER = #{unit_number}  ]]>
    </select>
    
    <!-- 查询支票申请明细信息 -->
    <select id="queryTempfeeByUnitNumber" parameterType="java.util.Map" resultType="java.util.Map">
    <![CDATA[ SELECT MAX((SELECT TPA.BUSINESS_CODE
		                	FROM DEV_CAP.T_PREM_ARAP TPA
		               	   WHERE TPA.UNIT_NUMBER = TBD.UNIT_NUMBER AND ROWNUM = 1)) BUSINESS_CODE,
		            TBD.UNIT_NUMBER,
		            MAX(TBT.Ybt_Offline_Flag) Ybt_Offline_Flag_old,
		            MAX(TBD.ARAP_DATE) ARAP_DATE,
		            MAX(TBD.FEE_AMOUNT) FEE_AMOUNT,
		            MAX(TBD.BILL_MODE) BILL_MODE,
		            MAX(TBD.BILL_ID) BILL_ID,
		            MAX(TBT.BANK_USER_NAME) BANK_USER_NAME,
		            MAX(DECODE(TBD.BILL_MODE,'31',TBT.BANK_CODE,'22',TC.BANK_CODE,'')) BANK_CODE,
		            MAX(TBT.BANK_ACCOUNT) BANK_ACCOUNT,
		            MAX(TC.CHEQUE_NO) CHEQUE_NO,
		            MAX(TC.INVOICE_DATE) INVOICE_DATE_OLD,
		            MAX(TC.CHEQUE_COMPANY) CHEQUE_COMPANY_OLD,
		            MAX(TC.CHEQUE_END_DATE) CHEQUE_END_DATE_OLD,
		            MAX(TL.TEMPFEE_LOG_ID) TEMPFEE_LOG_ID,
		            MAX(TL.APPLY_USER) APPLY_USER,
		            MAX(TL.APPLY_DATE) APPLY_DATE,
		            MAX(TL.APPLY_REASON) APPLY_REASON,
		            MAX(TL.REMARK) REMARK,
		            MAX(TL.SCAN_FLAG) SCAN_FLAG,
		            MAX((SELECT TPCCS.STATUS_NAME  FROM APP___CAP__DBUSER.T_PREM_CHG_CHECK_STATUS TPCCS WHERE TPCCS.STATUS_CODE=TL.CHECK_STATUS)) CHECK_STATUS,
		            MAX(TL.REJECT_REASON) REJECT_REASON,
		            MAX(DECODE(TC.CORP_ACTION,'',DECODE(TBT.CORP_ACTION,'',TCA.CORP_ACTION ,TBT.CORP_ACTION), TC.CORP_ACTION ) ) CORP_ACTION_OLD,
                    MAX(DECODE(TC.COMPANY_BANK_CODE,'',DECODE(TBT.COMPANY_BANK_CODE,'',TCA.COMPANY_BANK_CODE ,TBT.COMPANY_BANK_CODE), TC.COMPANY_BANK_CODE ) ) COMPANY_BANK_CODE_OLD
  			FROM DEV_CAP.T_BILL_DETAILS TBD
  				LEFT JOIN APP___CAP__DBUSER.T_CHEQUE TC
    				 ON TBD.BILL_MAIN_ID = TC.CHEQUE_ID AND TBD.BILL_MODE='22'
                LEFT JOIN APP___CAP__DBUSER.T_BANK_TRANSFER TBT
                     ON TBD.BILL_MAIN_ID = TBT.BTR_ID AND TBD.BILL_MODE='31'
                LEFT JOIN APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK TCA
                     ON TBD.BILL_MAIN_ID = TCA.CAB_ID AND TBD.BILL_MODE='11'
 				LEFT JOIN (SELECT   T.TEMPFEE_LOG_ID,
				                    T.UNIT_NUMBER,
				                    T.APPLY_TYPE,
				                    T.APPLY_USER,
				                    T.APPLY_REASON,
				                    T.REJECT_REASON,
				                    T.APPLY_DATE,
				                    T.CHECK_DATE,
				                    T.CHECK_STATUS,
				                    T.SCAN_FLAG,
				                    T.REMARK
               FROM APP___CAP__DBUSER.T_TEMPFEE_CHANGE_LOG T
              WHERE T.CHECK_STATUS NOT IN ('1', '2')
			     OR T.CHECK_STATUS IS NULL) TL
    				ON TBD.UNIT_NUMBER = TL.UNIT_NUMBER
  				LEFT JOIN APP___CAP__DBUSER.T_BANK_TRANSFER TBT
    				ON TBD.BILL_MAIN_ID=TBT.BTR_ID 
 				WHERE TBD.UNIT_NUMBER = #{unit_number}
 				  AND TBD.DELETE_FLAG IS NULL
    	 		GROUP BY TBD.UNIT_NUMBER
    		]]>
   		 
    </select>
    
    <!-- 查询支票申请明细信息 -->
    <select id="findTempPrembyUnitNumber" parameterType="java.util.Map" resultType="java.util.Map">
    <![CDATA[ SELECT MAX((SELECT TPA.BUSINESS_CODE
		                	FROM DEV_CAP.T_PREM_ARAP TPA
		               	   WHERE TPA.UNIT_NUMBER = TBD.UNIT_NUMBER AND ROWNUM = 1)) BUSINESS_CODE,
		            TBD.UNIT_NUMBER,
		            MAX(TL.SCAN_FLAG) SCAN_FLAG,
		            MAX(TL.CHECK_STATUS) CHECK_STATUS
  			FROM DEV_CAP.T_BILL_DETAILS TBD
  				LEFT JOIN APP___CAP__DBUSER.T_CHEQUE TC
    				 ON TBD.BILL_MAIN_ID = TC.CHEQUE_ID AND TBD.BILL_MODE='22'
                LEFT JOIN APP___CAP__DBUSER.T_BANK_TRANSFER TBT
                     ON TBD.BILL_MAIN_ID = TBT.BTR_ID AND TBD.BILL_MODE='31'
                LEFT JOIN APP___CAP__DBUSER.T_CASH_ACCOUNTBOOK TCA
                     ON TBD.BILL_MAIN_ID = TCA.CAB_ID AND TBD.BILL_MODE='11'
 				LEFT JOIN (SELECT   T.UNIT_NUMBER,T.CHECK_STATUS,
				                    T.SCAN_FLAG
               FROM APP___CAP__DBUSER.T_TEMPFEE_CHANGE_LOG T
              WHERE 1 = 1
                AND T.UNIT_NUMBER = #{unit_number}) TL
    				ON TBD.UNIT_NUMBER = TL.UNIT_NUMBER
  				LEFT JOIN APP___CAP__DBUSER.T_BANK_TRANSFER TBT
    				ON TBD.BILL_MAIN_ID=TBT.BTR_ID 
 				WHERE TBD.UNIT_NUMBER = #{unit_number}
 				  AND TBD.DELETE_FLAG IS NULL
    	 		GROUP BY TBD.UNIT_NUMBER
    		]]>
   		 
    </select>
    
    <insert id="addTempfeeLog" useGeneratedKeys="true" parameterType="java.util.Map">
   		 <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="tempfee_log_id">
    		SELECT DEV_CAP.S_TEMPFEE_CHANGE_LOG__ID.NEXTVAL FROM DUAL
   		 </selectKey>
   		 <![CDATA[
   		 	 INSERT INTO DEV_CAP.T_TEMPFEE_CHANGE_LOG (
				 TEMPFEE_LOG_ID,
				 UNIT_NUMBER,
				 BUSINESS_CODE,
				 APPLY_TYPE,
				 PAY_MODE,
				 ARAP_DATE,
				 TEMP_FEE_AMOUNT,
				 CHEQUE_NO_OLD,
				 CHEQUE_NO_NEW,
				 INVOICE_DATE_OLD,
				 INVOICE_DATE_NEW,
				 CHEQUE_COMPANY_OLD,
				 CHEQUE_COMPANY_NEW,
				 CHEQUE_END_DATE_OLD,
				 CHEQUE_END_DATE_NEW,
				 BANK_CODE_OLD,
				 BANK_CODE_NEW,
				 BANK_NAME_OLD,
				 BANK_NAME_NEW,
				 BANK_USER_NAME_OLD,
				 BANK_USER_NAME_NEW,
				 BANK_ACCOUNT_OLD,
				 BANK_ACCOUNT_NEW,
				 CHECK_STATUS,
				 APPLY_USER,
				 APPLY_DATE,
				 APPLY_REASON,
				 REMARK,
				 SCAN_FLAG,
				 INSERT_BY,
				 UPDATE_BY,
				 INSERT_TIME,
				 UPDATE_TIME,
				 INSERT_TIMESTAMP,
				 UPDATE_TIMESTAMP
				 )
				 VALUES
				(
				 #{tempfee_log_id,jdbcType = NUMERIC},
				 #{unit_number,jdbcType = VARCHAR},
				 #{business_code,jdbcType = VARCHAR},
				 #{apply_type,jdbcType = VARCHAR},
				 #{pay_mode,jdbcType = VARCHAR},
				 #{arap_date,jdbcType = DATE},
				 #{temp_fee_amount,jdbcType = NUMERIC},
				 #{cheque_no_old,jdbcType = VARCHAR},
				 #{cheque_no_new,jdbcType = VARCHAR},
				 #{invoice_date_old,jdbcType = DATE},
				 #{invoice_date_new,jdbcType = DATE},
				 #{cheque_company_old,jdbcType = VARCHAR},
				 #{cheque_company_new,jdbcType = VARCHAR},
				 #{cheque_end_date_old,jdbcType = DATE},
				 #{cheque_end_date_new,jdbcType = DATE},
				 #{bank_code_old,jdbcType = VARCHAR},
				 #{bank_code_new,jdbcType = VARCHAR},
				 #{bank_name_old,jdbcType = VARCHAR},
				 #{bank_name_new,jdbcType = VARCHAR},
				 #{bank_user_name_old,jdbcType = VARCHAR},
				 #{bank_user_name_new,jdbcType = VARCHAR},
				 #{bank_account_old,jdbcType = VARCHAR},
				 #{bank_account_new,jdbcType = VARCHAR},
				 #{check_status,jdbcType = VARCHAR},
				 #{apply_user,jdbcType = NUMERIC},
				 SYSDATE,
				 #{apply_reason,jdbcType = VARCHAR},
				 #{remark,jdbcType = VARCHAR},
				 #{scan_flag,jdbcType=NUMERIC},
				 #{insert_by,jdbcType = NUMERIC},
				 #{update_by,jdbcType = NUMERIC},
				 SYSDATE,
				 SYSDATE,
				 CURRENT_TIMESTAMP,
				 CURRENT_TIMESTAMP
				)
   		 ]]>
    </insert>
	<!-- 修改操作 -->
    <update id="updateTempfeeInfo" parameterType="java.util.Map">
        <![CDATA[ UPDATE DEV_CAP.T_TEMPFEE_CHANGE_LOG ]]>
        <set>
            <trim suffixOverrides=",">
            <if test="cheque_no_old !=null and cheque_no_old != '' ">
            	<![CDATA[CHEQUE_NO_OLD = #{cheque_no_old,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="cheque_no_new !=null and cheque_no_new != '' ">
            	<![CDATA[CHEQUE_NO_NEW = #{cheque_no_new,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="invoice_date_old !=null and invoice_date_old !='' ">
            	<![CDATA[INVOICE_DATE_OLD = #{invoice_date_old,jdbcType=DATE} ,]]>
            </if>    
            <if test="invoice_date_new !=null and invoice_date_new !='' ">
            	<![CDATA[INVOICE_DATE_NEW = #{invoice_date_new,jdbcType=DATE} ,]]>
            </if>
            <if test="cheque_company_old !=null and cheque_company_old !='' ">
            	<![CDATA[CHEQUE_COMPANY_OLD = #{cheque_company_old,jdbcType=DATE} ,]]>
            </if>
             <if test="cheque_company_new !=null and cheque_company_new !='' ">
            	<![CDATA[CHEQUE_COMPANY_NEW = #{cheque_company_new,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="cheque_end_date_old !=null and cheque_end_date_old !='' ">
            	<![CDATA[CHEQUE_END_DATE_OLD = #{cheque_end_date_old,jdbcType=DATE} ,]]>
            </if>
            <if test="cheque_end_date_new !=null and cheque_end_date_new !='' ">
            	<![CDATA[CHEQUE_END_DATE_NEW = #{cheque_end_date_new,jdbcType=DATE} ,]]>
            </if>
            <if test="bank_code_old !=null and bank_code_old !='' ">
            	<![CDATA[BANK_CODE_OLD = #{bank_code_old,jdbcType=VARCHAR} ,]]>
            </if>
             <if test="bank_code_new !=null and bank_code_new !='' ">
            	<![CDATA[BANK_CODE_NEW = #{bank_code_new,jdbcType=VARCHAR} ,]]>
            </if>   
            <if test="bank_user_name_old !=null and bank_user_name_old !='' ">
            	<![CDATA[BANK_USER_NAME_OLD = #{bank_user_name_old,jdbcType=VARCHAR} ,]]>
            </if>
             <if test="bank_user_name_new !=null and bank_user_name_new !='' ">
            	<![CDATA[BANK_USER_NAME_NEW = #{bank_user_name_new,jdbcType=VARCHAR} ,]]>
            </if> 
            <if test="bank_account_old !=null and bank_account_old !='' ">
            	<![CDATA[BANK_ACCOUNT_OLD = #{bank_account_old,jdbcType=VARCHAR} ,]]>
            </if> 
            <if test="bank_account_new !=null and bank_account_new !='' ">
            	<![CDATA[BANK_ACCOUNT_NEW = #{bank_account_new,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="check_status !=null and check_status !='' ">
            	<![CDATA[CHECK_STATUS = #{check_status,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="check_date !=null and check_date !='' ">
            	<![CDATA[CHECK_DATE = TRUNC(#{check_date,jdbcType=DATE}) ,]]>
            </if>
             <if test="check_user !=null and check_user !='' ">
            	<![CDATA[CHECK_USER = TRUNC(#{check_user,jdbcType=NUMERIC}) ,]]>
            </if>
            <if test="check_result !=null and check_result !='' ">
            	<![CDATA[check_result = TRUNC(#{check_result,jdbcType=NUMERIC}) ,]]>
            </if>
            <if test="apply_type !=null and apply_type !='' ">
            	<![CDATA[APPLY_TYPE = #{apply_type,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="apply_user !=null and apply_user !='' ">
            	<![CDATA[APPLY_USER = #{apply_user,jdbcType=NUMERIC} ,]]>
            </if>
             <if test="apply_date !=null and apply_date !='' ">
            	<![CDATA[APPLY_DATE = #{apply_date,jdbcType=DATE} ,]]>
            </if>
            <if test="apply_reason !=null and apply_reason !='' ">
            	<![CDATA[APPLY_REASON = #{apply_reason,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="reject_reason !=null and reject_reason !='' ">
            	<![CDATA[REJECT_REASON = #{reject_reason,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="remark !=null and remark !='' ">
            	<![CDATA[REMARK = #{remark,jdbcType=VARCHAR} ,]]>
            </if>
             <if test="arap_date !=null and arap_date !='' ">
            	<![CDATA[ARAP_DATE = TRUNC(#{arap_date,jdbcType=DATE}),]]>
            </if>
             <if test="bill_mode !=null and bill_mode !='' ">
            	<![CDATA[PAY_MODE = #{bill_mode,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="bill_id !=null and bill_id !='' ">
            	<![CDATA[BILL_ID = #{bill_id,jdbcType=NUMERIC} ,]]>
            </if>
               <if test="fee_amount !=null and fee_amount !='' ">
            	<![CDATA[TEMP_FEE_AMOUNT = #{fee_amount,jdbcType=NUMERIC} ,]]>
            </if>
            <if test="scan_flag != null and scan_flag !='' ">
            	<![CDATA[SCAN_FLAG = #{scan_flag,jdbcType=NUMERIC} ,]]>
            </if>
            
            <if test="corp_action_old != null and corp_action_old !='' ">
            	<![CDATA[CORP_ACTION_OLD = #{corp_action_old,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="corp_action_new != null and corp_action_new !='' ">
            	<![CDATA[CORP_ACTION_NEW = #{corp_action_new,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="company_bank_code_old != null and company_bank_code_old !='' ">
            	<![CDATA[COMPANY_BANK_CODE_OLD = #{company_bank_code_old,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="company_bank_code_new != null and company_bank_code_new !='' ">
            	<![CDATA[COMPANY_BANK_CODE_NEW = #{company_bank_code_new,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="ybt_offline_flag_old != null">
            	<![CDATA[YBT_OFFLINE_FLAG_OLD = #{ybt_offline_flag_old,jdbcType=NUMERIC} ,]]>
            </if>
            <if test="ybt_offline_flag_new != null">
            	<![CDATA[ybt_offline_flag_new = #{ybt_offline_flag_new,jdbcType=NUMERIC} ,]]>
            </if>
            <if test="update_by !=null and update_by !='' ">
            	<![CDATA[UPDATE_BY = #{update_by,jdbcType=NUMERIC} ,]]>
            </if>
            	<![CDATA[UPDATE_TIME = SYSDATE ,]]>
            </trim>
        </set>
        <![CDATA[ WHERE 1=1 AND (CHECK_STATUS IN ('3') OR CHECK_STATUS IS NULL)]]>
        <if test="unit_number !=null and unit_number !='' ">
        <![CDATA[ AND UNIT_NUMBER = #{unit_number,jdbcType=VARCHAR} ]]>
        </if>
        <if test="tempfee_log_id !=null and tempfee_log_id !='' ">
        <![CDATA[ AND TEMPFEE_LOG_ID = #{tempfee_log_id,jdbcType=NUMERIC} ]]>
        </if>
    </update>
    <update id="updateByScanFlag" parameterType="java.util.Map">
     <![CDATA[ UPDATE DEV_CAP.T_TEMPFEE_CHANGE_LOG  set SCAN_FLAG =#{scan_flag} WHERE UNIT_NUMBER = #{unit_number} ]]>
    </update>
    
    <select id="queryTempPremForScanFlag" parameterType="java.util.Map" resultType="java.util.Map">
     <![CDATA[ SELECT TCL.SCAN_FLAG
				  FROM DEV_CAP.T_TEMPFEE_CHANGE_LOG TCL
				WHERE TCL.BUSINESS_CODE = #{business_code}
				  AND TCL.UNIT_NUMBER =#{unit_number}
	 ]]>
    </select>
    
    
        <!-- 查询申请中记录总数 -->
    <select id="changeTempfeeChargeTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT COUNT(UNIT_NUMBER) 
        FROM (SELECT C.* FROM (
                select TCL.UNIT_NUMBER
                 FROM DEV_CAP.T_TEMPFEE_CHANGE_LOG TCL
                  WHERE 1 = 1
           ]]>
        <include refid="changerOfChangelogWhereClause" />
        <![CDATA[
        ) C )
        ]]>
    </select>
    <!-- 查询申请中数据 -->
    <select id="changeTempfeeCharge" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT M.*
   					FROM (SELECT ROWNUM RN, D.*
          					 FROM (
          					 SELECT
          					        TCL.BUSINESS_CODE BUSINESS_CODE,
                            	    nvl(TCL.CHEQUE_NO_OLD,'') CHEQUE_NO,
			                        TCL.UNIT_NUMBER UNIT_NUMBER,         
			                        TCL.TEMP_FEE_AMOUNT FEE_AMOUNT,
			                        TCl.PAY_MODE BILL_MODE,
			                        TCL.ARAP_DATE ARAP_DATE,
			                        TCL.TEMPFEE_LOG_ID,
			                        TCL.CHEQUE_NO_OLD,
			                        TCL.CHEQUE_NO_NEW,
			                        TCL.INVOICE_DATE_OLD,
			                        TCL.INVOICE_DATE_NEW,
			                        TCL.CHEQUE_COMPANY_OLD,
			                        TCL.CHEQUE_COMPANY_NEW,
			                        TCL.CHEQUE_END_DATE_OLD,
			                        TCL.CHEQUE_END_DATE_NEW,
			                        TCL.BANK_CODE_OLD,
			                        TCL.BANK_CODE_NEW,
			                        TCL.BANK_USER_NAME_OLD,
			                        TCL.BANK_USER_NAME_NEW,
			                        TCL.BANK_ACCOUNT_OLD,
			                        TCL.BANK_ACCOUNT_NEW,
			                        TCL.APPLY_TYPE APPLY_TYPE,
			                        TCL.APPLY_USER APPLY_USER,
			                        TCL.APPLY_DATE APPLY_DATE,
			                        TCL.CHECK_STATUS CHECK_STATUS,
			                        TCL.CHECK_RESULT CHECK_RESULT,
			                        TCL.CHECK_USER CHECK_USER,
			                        TCL.CHECK_DATE CHECK_DATE,
			                        TCL.REJECT_REASON REJECT_REASON,
			                        TCL.APPLY_REASON APPLY_REASON,
			                        TCL.REMARK REMARK,
			                        TCL.CORP_ACTION_OLD,
			                        TCL.CORP_ACTION_NEW,
			                        TCL.COMPANY_BANK_CODE_OLD,
			                        TCL.COMPANY_BANK_CODE_NEW
			                   FROM DEV_CAP.T_TEMPFEE_CHANGE_LOG TCL
			                  WHERE 1 = 1 AND TCL.CHECK_STATUS IS NOT NULL
                  ]]>
                  <include refid="changerOfChangelogWhereClause" />
                 <![CDATA[
                	  ) D
          			WHERE ROWNUM <= #{LESS_NUM}) M
 					 WHERE M.RN > #{GREATER_NUM}
                 ]]>                              
    </select>
     <!-- 轨迹查询 -->
    <select id="queryChangeTempPremLogTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT COUNT(UNIT_NUMBER) 
        FROM (SELECT C.* FROM (
                select TCL.UNIT_NUMBER
                 FROM DEV_CAP.T_TEMPFEE_CHANGE_LOG TCL
                  WHERE 1 = 1
           ]]>
        <include refid="changerOfChangelogWhereClause" />
        <![CDATA[
        ) C )
        ]]>
    </select>
    <!-- 轨迹查询 -->
    <select id="queryChangeTempPremLogInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT M.*
   					FROM (SELECT ROWNUM RN, D.*
          					 FROM (
          					 SELECT
          					  		TCL.BUSINESS_CODE BUSINESS_CODE,
                            	    nvl(TCL.CHEQUE_NO_OLD,'') CHEQUE_NO,
			                        TCL.UNIT_NUMBER UNIT_NUMBER,         
			                        TCL.TEMP_FEE_AMOUNT FEE_AMOUNT,
			                        TCl.PAY_MODE BILL_MODE,
			                        TCL.ARAP_DATE ARAP_DATE,
			                        TCL.TEMPFEE_LOG_ID,
			                        TCL.CHEQUE_NO_OLD,
			                        TCL.CHEQUE_NO_NEW,
			                        TCL.INVOICE_DATE_OLD,
			                        TCL.INVOICE_DATE_NEW,
			                        TCL.CHEQUE_COMPANY_OLD,
			                        TCL.CHEQUE_COMPANY_NEW,
			                        TCL.CHEQUE_END_DATE_OLD,
			                        TCL.CHEQUE_END_DATE_NEW,
			                        TCL.BANK_CODE_OLD,
			                        TCL.BANK_CODE_NEW,
			                        TCL.BANK_USER_NAME_OLD,
			                        TCL.BANK_USER_NAME_NEW,
			                        TCL.BANK_ACCOUNT_OLD,
			                        TCL.BANK_ACCOUNT_NEW,
			                        TCL.APPLY_TYPE APPLY_TYPE,
			                        TCL.APPLY_USER APPLY_USER,
			                        TCL.APPLY_DATE APPLY_DATE,
			                        TCL.CHECK_STATUS CHECK_STATUS,
			                        TCL.CHECK_RESULT CHECK_RESULT,
			                        TCL.CHECK_USER CHECK_USER,
			                        TCL.CHECK_DATE CHECK_DATE,
			                        TCL.REJECT_REASON REJECT_REASON,
			                        TCL.APPLY_REASON APPLY_REASON,
			                        TCL.REMARK REMARK
			                   FROM DEV_CAP.T_TEMPFEE_CHANGE_LOG TCL
			                  WHERE 1 = 1 AND TCL.CHECK_STATUS IS NOT NULL
                  ]]>
                  <include refid="changerOfChangelogWhereClause" />
                 <![CDATA[ORDER BY TCL.UPDATE_TIME DESC
                	  ) D
          			WHERE ROWNUM <= #{LESS_NUM}) M
 					 WHERE M.RN > #{GREATER_NUM}
                 ]]>                              
    </select>
     <!-- 查询申请中数据明细 -->
    <select id="changeTempfeebyUnitNumber" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT            TCL.BUSINESS_CODE BUSINESS_CODE,
                            	    nvl(TCL.CHEQUE_NO_OLD,'') CHEQUE_NO,
			                        TCL.UNIT_NUMBER UNIT_NUMBER,         
			                        TCL.TEMP_FEE_AMOUNT FEE_AMOUNT,
			                        TCl.PAY_MODE BILL_MODE,
			                        TCL.ARAP_DATE ARAP_DATE,
			                        TCL.TEMPFEE_LOG_ID,
			                        TCL.CHEQUE_NO_OLD,
			                        TCL.CHEQUE_NO_NEW,
			                        TCL.INVOICE_DATE_OLD,
			                        TCL.INVOICE_DATE_NEW,
			                        TCL.CHEQUE_COMPANY_OLD,
			                        TCL.CHEQUE_COMPANY_NEW,
			                        TCL.CHEQUE_END_DATE_OLD,
			                        TCL.CHEQUE_END_DATE_NEW,
			                        TCL.BANK_CODE_OLD,
			                        TCL.BANK_CODE_NEW,
			                        TCL.BANK_USER_NAME_OLD,
			                        TCL.BANK_USER_NAME_NEW,
			                        TCL.BANK_ACCOUNT_OLD,
			                        TCL.BANK_ACCOUNT_NEW,
			                        TCL.APPLY_TYPE APPLY_TYPE,
			                        TCL.APPLY_USER APPLY_USER,
			                        TCL.APPLY_DATE APPLY_DATE,
			                        TCL.CHECK_DATE CHECK_DATE,
			                        TCL.CHECK_RESULT CHECK_RESULT,
			                        TCL.REJECT_REASON REJECT_REASON,
			                        TCL.APPLY_REASON APPLY_REASON,
			                        TCL.REMARK REMARK,
			                        TCL.CORP_ACTION_OLD,
			                        TCL.CORP_ACTION_NEW,
			                        TCL.Ybt_Offline_Flag_Old,
                                    TCL.Ybt_Offline_Flag_New,
                                    TCL.BILL_ID,
			                        (SELECT MAX(A.COMPANY_BANK_NAME) FROM DEV_CAP.T_COMPANY_BANK_ACCOUNT A WHERE A.COMPANY_BANK_CODE=TCL.COMPANY_BANK_CODE_OLD) COMPANY_BANK_CODE_OLD,
			                        (SELECT MAX(A.COMPANY_BANK_NAME) FROM DEV_CAP.T_COMPANY_BANK_ACCOUNT A WHERE A.COMPANY_BANK_CODE=TCL.COMPANY_BANK_CODE_NEW) COMPANY_BANK_CODE_NEW
			                   FROM DEV_CAP.T_TEMPFEE_CHANGE_LOG TCL
			                  WHERE 1 = 1 AND TCL.CHECK_STATUS IS NOT NULL
                  ]]>
                  <include refid="changerOfChangelogWhereClause" />
    </select>
    
    	<!-- 修改银行转账单 -->
    <update id="updateBankTransfer" parameterType="java.util.Map">
        <![CDATA[ UPDATE DEV_CAP.T_BANK_TRANSFER TBT ]]>
        <set>
            <trim suffixOverrides=",">
             <if test="bank_code_new !=null and bank_code_new !='' ">
            	<![CDATA[TBT.BANK_CODE = #{bank_code_new,jdbcType=VARCHAR} ,]]>
            </if>   
             <if test="bank_user_name_new !=null and bank_user_name_new !='' ">
            	<![CDATA[TBT.BANK_USER_NAME = #{bank_user_name_new,jdbcType=VARCHAR} ,]]>
            </if> 
            <if test="bank_account_new !=null and bank_account_new !='' ">
            	<![CDATA[TBT.BANK_ACCOUNT = #{bank_account_new,jdbcType=VARCHAR} ,]]>
            </if> 
            
            <if test="corp_action_new !=null and corp_action_new !='' ">
            	<![CDATA[TBT.CORP_ACTION = #{corp_action_new,jdbcType=VARCHAR} ,]]>
            </if> 
            <if test="company_bank_code_new !=null and company_bank_code_new !='' ">
            	<![CDATA[TBT.COMPANY_BANK_CODE = #{company_bank_code_new,jdbcType=VARCHAR} ,]]>
            </if> 
            <!-- <if test="ybt_offline_flag_new !=null and ybt_offline_flag_new !='' ">
            	<![CDATA[TBT.ybt_offline_flag = #{ybt_offline_flag_new,jdbcType=NUMERIC} ,]]>
            </if> -->
            <if test="update_by !=null and update_by !='' ">
            	<![CDATA[TBT.UPDATE_BY =#{update_by,jdbcType=NUMERIC} ,]]>
            </if> 
            	<![CDATA[TBT.UPDATE_TIME =SYSDATE ,]]>
            	
            </trim>
        </set>
        <![CDATA[ WHERE 1=1 
        			AND EXISTS(SELECT 1  FROM DEV_CAP.T_BILL_DETAILS TBD 
                                          WHERE TBD.BILL_MAIN_ID=TBT.BTR_ID AND TBD.UNIT_NUMBER =#{unit_number,jdbcType=VARCHAR} )
        ]]>
    </update>
    
    
    	<!-- 修改现金送款簿 -->
    <update id="updateCashAccountbook" parameterType="java.util.Map">
        <![CDATA[ UPDATE DEV_CAP.T_CASH_ACCOUNTBOOK TCA ]]>
        <set>
            <trim suffixOverrides=",">
            <if test="corp_action_new !=null and corp_action_new !='' ">
            	<![CDATA[TCA.CORP_ACTION = #{corp_action_new,jdbcType=VARCHAR} ,]]>
            </if> 
            <if test="company_bank_code_new !=null and company_bank_code_new !='' ">
            	<![CDATA[TCA.COMPANY_BANK_CODE = #{company_bank_code_new,jdbcType=VARCHAR} ,]]>
            </if> 
            
            <if test="update_by !=null and update_by !='' ">
            	<![CDATA[TCA.UPDATE_BY =#{update_by,jdbcType=NUMERIC} ,]]>
            </if> 
            	<![CDATA[TCA.UPDATE_TIME =SYSDATE ,]]>
            </trim>
        </set>
        <![CDATA[ WHERE 1=1 
        			AND EXISTS(SELECT 1  FROM DEV_CAP.T_BILL_DETAILS TBD 
                                          WHERE TBD.BILL_MAIN_ID=TCA.CAB_ID AND TBD.UNIT_NUMBER =#{unit_number,jdbcType=VARCHAR} )
        ]]>
    </update>
    
    	<!-- 修改支票信息表 -->
    <update id="updateTCheque" parameterType="java.util.Map">
        <![CDATA[ UPDATE DEV_CAP.T_CHEQUE TC ]]>
        <set>
            <trim suffixOverrides=",">
            
            <if test="cheque_no_new !=null and cheque_no_new != '' ">
            	<![CDATA[TC.CHEQUE_NO = #{cheque_no_new,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="invoice_date_new !=null and invoice_date_new !='' ">
            	<![CDATA[TC.CHEQUE_START_DATE = #{invoice_date_new,jdbcType=DATE} ,]]>
            </if>
           <if test="invoice_date_new !=null and invoice_date_new !='' ">
            	<![CDATA[TC.INVOICE_DATE = #{invoice_date_new,jdbcType=DATE} ,]]>
            </if>
             <if test="cheque_company_new !=null and cheque_company_new !='' ">
            	<![CDATA[TC.CHEQUE_COMPANY = #{cheque_company_new,jdbcType=VARCHAR} ,]]>
            </if>
            <if test="cheque_end_date_new !=null and cheque_end_date_new !='' ">
            	<![CDATA[TC.CHEQUE_END_DATE = #{cheque_end_date_new,jdbcType=DATE} ,]]>
            </if>
             <if test="bank_code_new !=null and bank_code_new !='' ">
            	<![CDATA[TC.BANK_CODE = #{bank_code_new,jdbcType=VARCHAR} ,]]>
            </if>   
            
            <if test="corp_action_new !=null and corp_action_new !='' ">
            	<![CDATA[TC.CORP_ACTION = #{corp_action_new,jdbcType=VARCHAR} ,]]>
            </if> 
            <if test="company_bank_code_new !=null and company_bank_code_new !='' ">
            	<![CDATA[TC.COMPANY_BANK_CODE = #{company_bank_code_new,jdbcType=VARCHAR} ,]]>
            </if> 
            
      		<if test="update_by !=null and update_by !='' ">
            	<![CDATA[TC.UPDATE_BY =#{update_by,jdbcType=NUMERIC} ,]]>
            </if> 
           
            	<![CDATA[TC.UPDATE_TIME =SYSDATE ,]]>
           
            </trim>
        </set>
        <![CDATA[ WHERE 1=1 AND EXISTS(SELECT 1  FROM DEV_CAP.T_BILL_DETAILS TBD 
                                          WHERE TBD.BILL_MAIN_ID=TC.CHEQUE_ID AND TBD.UNIT_NUMBER =#{unit_number,jdbcType=VARCHAR} )]]>
    </update>
        	<!-- 修改票据明细信息表 -->
    <update id="updateBillDetails" parameterType="java.util.Map">
        <![CDATA[ UPDATE DEV_CAP.T_BILL_DETAILS TBD set TBD.DELETE_FLAG = 1
                    WHERE TBD.UNIT_NUMBER =#{unit_number,jdbcType=VARCHAR}
           ]]>
    </update>
          	<!-- 修改应收应付信息表 -->
    <update id="updateArFlagInfo" parameterType="java.util.Map">
         <![CDATA[ UPDATE DEV_CAP.T_PREM_ARAP TPA ]]>
        <set>
            <trim suffixOverrides=",">
            <if test="fee_status !=null and fee_status != '' ">
            	<![CDATA[TPA.FEE_STATUS = #{fee_status,jdbcType=VARCHAR} ,]]>
            </if>
      		<if test="update_by !=null and update_by !='' ">
            	<![CDATA[TPA.UPDATE_BY =#{update_by,jdbcType=NUMERIC} ,]]>
            </if> 
            	<![CDATA[TPA.UPDATE_TIME =SYSDATE ,]]>
            </trim>
        </set>
        <![CDATA[ WHERE TPA.UNIT_NUMBER= #{unit_number,jdbcType=VARCHAR} ]]>
    </update>
</mapper>
