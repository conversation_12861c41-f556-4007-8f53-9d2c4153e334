<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.nci.tunan.qry.impl.nb.dao.impl.CheckYBTCustomerDataDaoImpl">

	<sql id="NB_queryCheckYBTCustomerInfoWhereCondition">
		<!-- 销售渠道 -->
		<if test="channel_type != null and channel_type != ''"><![CDATA[ AND A.channel_type in (${channel_type}) ]]></if>
		<!-- 管理机构 ******** -->
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		<!-- 签单日期 -->
		<if test=" issue_start_date != null and issue_end_date != null  "><![CDATA[ AND A.ISSUE_DATE BETWEEN to_date(#{issue_start_date}, 'yyyy-MM-dd')  AND to_date(#{issue_end_date}, 'yyyy-MM-dd')]]></if>
		<!-- 银行代码 -->
		<if test=" service_bank  != null and service_bank != '' "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<!-- 客户属性 -->
		<if test=" customer_type  != null and customer_type != '' "><![CDATA[ AND A.CUSTOMER_TYPE = #{customer_type} ]]></if>
		<!-- 客户九要素 -->
		<if test=" customer_base_info  != null and customer_base_info != '' "><!-- A.ADDRESS IS NULL OR 这个地址条件先剔除，因为新老客户对应地址表个数不统一，所以会查出多条数据-->
			<!-- <if test=" customer_base_info == 0 "><![CDATA[ AND ( A.CUSTOMER_NAME 
				IS NULL OR A.customer_gender IS NULL OR A.customer_cert_type IS NULL OR A.customer_certi_code 
				IS NULL OR A.country_code IS NULL OR A.job_code IS NULL OR A.cust_cert_star_date 
				IS NULL OR A.cust_cert_end_date IS NULL OR (A.MOBILE_TEL IS NULL AND A.OFFEN_USE_TEL 
				IS NULL)) ]]></if> -->
			<if test=" customer_base_info == 1 "><![CDATA[ AND A.CUSTOMER_NAME IS NULL ]]></if>
			<if test=" customer_base_info == 2 "><![CDATA[ AND A.customer_gender IS NULL ]]></if>
			<if test=" customer_base_info == 3 "><![CDATA[ AND A.customer_cert_type IS NULL ]]></if>
			<if test=" customer_base_info == 4 "><![CDATA[ AND A.customer_certi_code IS NULL ]]></if>
			<if test=" customer_base_info == 5 "><![CDATA[ AND A.country_code IS NULL ]]></if>
			<if test=" customer_base_info == 6 "><![CDATA[ AND A.job_code IS NULL ]]></if>
			<if test=" customer_base_info == 7 "><![CDATA[ AND A.cust_cert_star_date IS NULL ]]></if>
			<if test=" customer_base_info == 8 "><![CDATA[ AND A.cust_cert_end_date IS NULL ]]></if>
			<if test=" customer_base_info == 13 "><![CDATA[ AND A.ADDRESS IS NULL ]]></if>
			<if test=" customer_base_info == 14 "><![CDATA[ AND (A.MOBILE_TEL IS NULL AND A.OFFEN_USE_TEL IS NULL) ]]></if>
			<!-- 含有address相关表的条件选择都剔除 原语句   AND (A.ADDRESS IS NULL OR A.COMPANY_NAME IS NULL)   -->
			<if test=" customer_type  != null and customer_type != '' and customer_type == 2 and flag == 1 ">
				<if test=" customer_base_info == 9 "><![CDATA[ AND A.COMPANY_NAME IS NULL ]]></if>
			</if> 
			
			<!-- 只有客户类型为投保人时才查询联系方式 -->
			<if test=" customer_base_info == 10 ">
				<if test=" customer_type  != null and customer_type != '' and customer_type == 1 "><![CDATA[ AND (A.MOBILE_TEL IS NULL AND A.OFFEN_USE_TEL IS NULL)]]></if>
				<if test=" customer_type  != null and customer_type != '' and customer_type == 2 "><!--下面的条件拼接隐藏该条件   OR  A.ADDRESS IS NULL -->
					<![CDATA[ AND (
					A.CUSTOMER_NAME IS NULL OR A.customer_gender IS NULL OR A.customer_cert_type IS NULL OR 
					A.customer_certi_code IS NULL OR A.country_code IS NULL OR A.job_code IS NULL OR 
					A.cust_cert_star_date IS NULL OR A.cust_cert_end_date IS NULL 
					)]]>
				</if>
				<if test=" customer_type  != null and customer_type != '' and customer_type == 3 "><!--下面的条件拼接隐藏该条件 OR  A.ADDRESS IS NULL -->
					<![CDATA[AND (
					A.CUSTOMER_NAME IS NULL OR A.customer_gender IS NULL OR A.customer_cert_type IS NULL OR 
					A.customer_certi_code IS NULL OR A.country_code IS NULL OR A.job_code IS NULL OR 
					A.cust_cert_star_date IS NULL OR A.cust_cert_end_date IS NULL )]]>
				</if>
			</if>
		</if>
		<!-- 承保方式 -->
		<if test=" input_type  != null and input_type != '' "><![CDATA[ AND A.SUBINPUT_TYPE = #{input_type}  ]]></if>
		<!-- 合同保费 -->
		<if test=" total_prem_af  != null and total_prem_af != '' ">
			<if test=" total_prem_af == 1 ">
				<![CDATA[ AND A.TOTAL_PREM_AF < 20000 ]]>
			</if>
			<if test=" total_prem_af == 2 ">
				<![CDATA[ AND A.TOTAL_PREM_AF >= 20000 ]]>
			</if>
		</if>
		<!-- 反洗钱 -->
		<if test=" aml_flag == 0 "> 
		  <![CDATA[ AND (A.AML_FLAG =#{aml_flag} OR  A.AML_FLAG IS NULL) ]]>
		</if>
		<if test=" aml_flag == 1 "> 
		  <![CDATA[ AND  A.AML_FLAG =#{aml_flag} ]]>
		</if>
		<if test=" customer_type  != null and customer_type != '' ">
			<if
				test=" (customer_base_info == null or customer_base_info == '') and customer_type == 1"><![CDATA[ AND (
					A.CUSTOMER_NAME IS NULL OR A.customer_gender IS NULL OR A.customer_cert_type IS NULL OR 
					A.customer_certi_code IS NULL OR A.country_code IS NULL OR A.job_code IS NULL OR 
					A.cust_cert_star_date IS NULL OR A.cust_cert_end_date IS NULL OR A.ADDRESS IS NULL  OR
					(A.MOBILE_TEL IS NULL AND A.OFFEN_USE_TELIS IS NULL)) ]]></if>

			<if
				test="  (customer_base_info == null or customer_base_info == '') and (customer_type == 2 or customer_type == 3)"><![CDATA[ AND (
					A.CUSTOMER_NAME IS NULL OR A.customer_gender IS NULL OR A.customer_cert_type IS NULL OR 
					A.customer_certi_code IS NULL   OR 
					A.cust_cert_star_date IS NULL OR A.cust_cert_end_date IS NULL OR  
					(A.MOBILE_TEL IS NULL AND A.OFFEN_USE_TELIS IS NULL)) ]]></if>
		</if>
		<!-- 当客户属性为空的时候客户要素选择 国籍 职业 住所地或工作单位地址 时 不查询被保人 受益人数据 -->
		<if
			test=" (customer_type  == null or customer_type == '') and ( customer_base_info == 5 or  customer_base_info == 6 or customer_base_info == 13 )">	
			<![CDATA[ AND A.CUSTOMER_TYPE not in('2','3')]]>
		</if>
	</sql>
	<!-- 查询 银保通业务客户基本信息核实清单 -->
	<!-- <select id="NB_findCheckYBTCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  select B.apply_code,B.aml_flag,
       B.policy_Code,
       B.total_prem_af,
       B.service_bank,
       B.service_bank_branch,
       B.branch_organ_code,
       B.organ_code,
       B.submit_channel,
       B.subinput_type,
       B.input_type,
       B.issue_date,
       B.agent_code,
       B.agent_name,
       B.customer_name,
       B.customer_gender,
       B.customer_cert_type,
       B.customer_certi_code,
       B.country_code,
       B.job_code,
       B.cust_cert_star_date,
       B.cust_cert_end_date,
       B.channel_type,
       B.mobile_tel,
       B.house_tel,
       B.office_tel,
       B.offen_use_tel,
       B.company_name,
       B.customer_type,
       B.bank_branch_name
  from (select rownum RN,
               A.Apply_Code,A.AML_FLAG,
               A.Policy_Code,
               A.total_prem_af,
               A.service_bank,
               A.service_bank_branch,
               A.branch_organ_code,
               A.organ_code,
               A.submit_channel,
               A.subinput_type,
               A.input_type,
               A.issue_date,
               A.agent_code,
               A.agent_name,
               A.customer_name,
               A.customer_gender,
               A.customer_cert_type,
               A.customer_certi_code,
               A.country_code,
               A.job_code,
               A.cust_cert_star_date,
               A.cust_cert_end_date,
               A.channel_type,
               A.mobile_tel,
               A.house_tel,
               A.office_tel,
               A.offen_use_tel,
               A.company_name,
               A.customer_type,
               A.bank_branch_name
          from (select ncm.apply_code,NCM.AML_FLAG,
                       ncm.policy_code,
                       ncp.total_prem_af,
                       ncm.service_bank,
                       ncm.service_bank_branch,
                       ncm.branch_organ_code,
                       ncm.organ_code,
                       ncm.submit_channel,
                       ncm.subinput_type,
                       ncm.input_type,
                       ncm.issue_date,
                       ncm.channel_type,
                       ta.agent_code,
                       ta.agent_name,
                       c.customer_name,
                       c.customer_gender,
                       c.customer_cert_type,
                       c.customer_certi_code,
                       c.country_code,
                       c.job_code,
                       c.cust_cert_star_date,
                       c.cust_cert_end_date,
                       c.mobile_tel,
                       c.house_tel,
                       c.office_tel,
                       c.offen_use_tel,
                       c.company_name,
                       
                       br.bank_branch_name,
                       '1' as customer_type
                  from APP___NB__DBUSER.t_nb_contract_master ncm,
                       (select cp.apply_code,
                               sum(cp.total_prem_af) as total_prem_af
                          from APP___NB__DBUSER.t_nb_contract_product cp
                         group by cp.apply_code) ncp,
                       APP___NB__DBUSER.t_nb_policy_holder h,
                       APP___NB__DBUSER.t_customer c,
                      
                       APP___NB__DBUSER.t_nb_contract_agent a,
                       APP___NB__DBUSER.t_agent ta,
                       APP___NB__DBUSER.t_Bank_Branch br
                 where ncm.apply_code = ncp.apply_code
                   and ncm.apply_code = h.apply_code
                   and h.customer_id = c.customer_id
                 
                   and ncm.apply_code = a.apply_code
                   and a.agent_code = ta.agent_code
                   and ncm.service_bank=br.bank_branch_code
                   and ncm.proposal_status in ('10', '13', '14')
                   and ncm.submit_channel = '1'
                union all
                select ncm.apply_code,NCM.AML_FLAG,
                       ncm.policy_code,
                       ncp.total_prem_af,
                       ncm.service_bank,
                       ncm.service_bank_branch,
                       ncm.branch_organ_code,
                       ncm.organ_code,
                       ncm.submit_channel,
                       ncm.subinput_type,
                       ncm.input_type,
                       ncm.issue_date,
                       ncm.channel_type,
                       ta.agent_code,
                       ta.agent_name,
                       c.customer_name,
                       c.customer_gender,
                       c.customer_cert_type,
                       c.customer_certi_code,
                       c.country_code,
                       c.job_code,
                       c.cust_cert_star_date,
                       c.cust_cert_end_date,
                       c.mobile_tel,
                       c.house_tel,
                       c.office_tel,
                       c.offen_use_tel,
                       c.company_name,
                      
                       br.bank_branch_name,
                       '2' as customer_type
                  from APP___NB__DBUSER.t_nb_contract_master ncm,
                       (select cp.apply_code,
                               sum(cp.total_prem_af) as total_prem_af
                          from APP___NB__DBUSER.t_nb_contract_product cp
                         group by cp.apply_code) ncp,
                       APP___NB__DBUSER.t_nb_insured_list l,
                       APP___NB__DBUSER.t_customer c,
                       
                       APP___NB__DBUSER.t_nb_contract_agent a,
                       APP___NB__DBUSER.t_agent ta,
                       APP___NB__DBUSER.t_Bank_Branch br
                 where ncm.apply_code = ncp.apply_code
                   and ncm.apply_code = l.apply_code
                   and l.customer_id = c.customer_id
                  
                   and ncm.apply_code = a.apply_code
                   and a.agent_code = ta.agent_code
                   and ncm.service_bank=br.bank_branch_code
                   and ncm.proposal_status in ('10', '13', '14')
                   and ncm.submit_channel = '1'
                union all
                select ncm.apply_code,NCM.AML_FLAG,
                       ncm.policy_code,
                       ncp.total_prem_af,
                       ncm.service_bank,
                       ncm.service_bank_branch,
                       ncm.branch_organ_code,
                       ncm.organ_code,
                       ncm.submit_channel,
                       ncm.subinput_type,
                       ncm.input_type,
                       ncm.issue_date,
                       ncm.channel_type,
                       ta.agent_code,
                       ta.agent_name,
                       c.customer_name,
                       c.customer_gender,
                       c.customer_cert_type,
                       c.customer_certi_code,
                       c.country_code,
                       c.job_code,
                       c.cust_cert_star_date,
                       c.cust_cert_end_date,
                       c.mobile_tel,
                       c.house_tel,
                       c.office_tel,
                       c.offen_use_tel,
                       c.company_name,
                       
                       br.bank_branch_name,
                       '3' as customer_type
                  from APP___NB__DBUSER.t_nb_contract_master ncm,
                       (select cp.apply_code,
                               sum(cp.total_prem_af) as total_prem_af
                          from APP___NB__DBUSER.t_nb_contract_product cp
                         group by cp.apply_code) ncp,
                       APP___NB__DBUSER.t_nb_contract_bene b,
                       APP___NB__DBUSER.t_customer c,
                      
                       APP___NB__DBUSER.t_nb_contract_agent a,
                       APP___NB__DBUSER.t_agent ta,
                       APP___NB__DBUSER.t_Bank_Branch br
                 where ncm.apply_code = ncp.apply_code
                   and ncm.apply_code = b.apply_code
                   and b.customer_id = c.customer_id
                  
                   and ncm.apply_code = a.apply_code
                   and a.agent_code = ta.agent_code
                   and ncm.service_bank=br.bank_branch_code
                   and ncm.proposal_status in ('10', '13', '14')
                   and ncm.submit_channel = '1'
                   and b.bene_type=1
                   ) A
         where 1 = 1 
         ]]>  
         <include refid="NB_queryCheckYBTCustomerInfoWhereCondition"></include>
           <![CDATA[ 
	           and rownum <= #{LESS_NUM}  
	           order by A.issue_date, A.customer_type ) B
			 where 1 = 1  
			   and RN > #{GREATER_NUM}
			]]>
		
	</select> -->
	
	<!-- 查询 银保通业务客户基本信息核实清单 -->
	<select id="NB_findCheckYBTCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		select
		B.apply_code,B.aml_flag,B.address,
		B.policy_Code,
		B.total_prem_af,
		B.service_bank,
		B.service_bank_branch,
		B.branch_organ_code,
		B.organ_code,
		B.submit_channel,
		B.subinput_type,
		B.input_type,
		B.issue_date,
		B.agent_code,
		B.agent_name,
		B.customer_name,
		B.customer_gender,
		B.customer_cert_type,
		B.customer_certi_code,
		B.country_code,
		B.job_code,
		B.cust_cert_star_date,
		B.cust_cert_end_date,
		B.channel_type,
		B.mobile_tel,
		B.house_tel,
		B.office_tel,
		B.offen_use_tel,
		B.company_name,
		B.customer_type,
		B.bank_branch_name 
	from (
		select rownum RN,
		A.Apply_Code,A.AML_FLAG,a.address,
		A.Policy_Code,
		A.total_prem_af,
		A.service_bank,
		A.service_bank_branch,
		A.branch_organ_code,
		A.organ_code,
		A.submit_channel,
		A.subinput_type,
		A.input_type,
		A.issue_date,
		A.agent_code,
		A.agent_name,
		A.customer_name,
		A.customer_gender,
		A.customer_cert_type,
		A.customer_certi_code,
		A.country_code,
		A.job_code,
		A.cust_cert_star_date,
		A.cust_cert_end_date,
		A.channel_type,
		A.mobile_tel,
		A.house_tel,
		A.office_tel,
		A.offen_use_tel,
		A.company_name,
		A.customer_type,
		A.bank_branch_name
	from (
		select
		ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c.house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address,
		br.bank_branch_name,
		'1' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm
		left join APP___NB__DBUSER.t_nb_policy_holder h on ncm.apply_code = h.apply_code
		left join APP___NB__DBUSER.t_customer c on h.customer_id = c.customer_id
		left join APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code = ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on ncm.service_bank = br.bank_branch_code
		left join APP___NB__DBUSER.t_address tad on tad.address_id = h.address_id
		left join (select cp.apply_code,
		sum(cp.total_prem_af) as total_prem_af 
		   from  APP___NB__DBUSER.t_nb_contract_product cp group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where 
		 ncm.proposal_status in ('10', '13', '14')
		 and ncm.submit_channel = '1'
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR
							 c.customer_cert_type IS NULL OR
							 c.customer_certi_code IS NULL OR c.country_code IS NULL OR
							 c.job_code IS NULL OR c.cust_cert_star_date IS NULL OR
							 c.cust_cert_end_date IS NULL OR
							 tad.ADDRESS IS NULL  OR
							 (tad.MOBILE_TEL IS NULL AND c.OFFEN_USE_TEL IS NULL)) ]]>
			</when>
		</choose>
		union all
		select ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c.house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address,
		br.bank_branch_name,
		'2' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm 
		left join	APP___NB__DBUSER.t_nb_insured_list l on ncm.apply_code = l.apply_code
		left join APP___NB__DBUSER.t_customer c on l.customer_id =c.customer_id
		left join APP___NB__DBUSER.t_address tad on tad.address_id = l.address_id
		left join	APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code =	ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on	ncm.service_bank =	br.bank_branch_code
		left JOIN (select cp.apply_code,sum(cp.total_prem_af) as total_prem_af
		 from APP___NB__DBUSER.t_nb_contract_product cp	group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where
		ncm.proposal_status in ('10','13', '14')
		and ncm.submit_channel = '1'
		and ( C.country_code IS not NULL OR 
		 C.job_code IS NULL OR C.CUSTOMER_NAME IS NULL OR
		 C.customer_gender IS NULL OR C.customer_cert_type IS NULL OR
		 C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR  tad.MOBILE_TEL IS NULL or c.offen_use_tel is null)
		and ( C.job_code IS not NULL OR
		 C.CUSTOMER_NAME IS NULL OR  C.customer_gender IS NULL  OR
		 C.customer_cert_type IS NULL OR	C.customer_certi_code IS NULL OR
		 C.cust_cert_star_date IS NULL OR	C.cust_cert_end_date IS NULL OR
		 tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		and (tad.ADDRESS IS not NULL or
		C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR 
		C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR
		C.cust_cert_star_date IS NULL OR C.cust_cert_end_date IS NULL or
		tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (
					c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR c.customer_cert_type IS NULL OR 
					c.customer_certi_code IS NULL   OR 
					c.cust_cert_star_date IS NULL OR c.cust_cert_end_date IS NULL OR  
					(tad.MOBILE_TEL IS NULL AND c.offen_use_tel IS NULL))
											   ]]>
			</when>
		</choose>
		union all
		select ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c. house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address ,
		br.bank_branch_name,
		'3' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm
		left join APP___NB__DBUSER.t_nb_contract_bene b on ncm.apply_code =b.apply_code
		left join APP___NB__DBUSER.t_customer c on b.customer_id =c.customer_id 
		left join APP___NB__DBUSER.t_address tad on tad.address_id = b.address_id
		left join APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code =ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on ncm.service_bank =br.bank_branch_code
		left JOIN (select cp.apply_code,sum(cp.total_prem_af) as total_prem_af
		 from APP___NB__DBUSER.t_nb_contract_product cp group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where
		ncm.proposal_status in ('10','13', '14')
		and ncm.submit_channel = '1'
		and b.bene_type = 1
		and ( C.country_code IS not NULL OR
		 C.job_code IS NULL OR	C.CUSTOMER_NAME IS NULL OR
		 C.customer_gender IS NULL OR C.customer_cert_type IS NULL OR
		 C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR tad.MOBILE_TEL IS NULL or
		  c.offen_use_tel IS NULL )
		and ( C.job_code IS not NULL OR
		 C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR
		 C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		and (tad.ADDRESS IS not NULL or
		 C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR 
		 C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR
		 C.cust_cert_star_date IS NULL OR C.cust_cert_end_date IS NULL or
		tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (
					c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR c.customer_cert_type IS NULL OR 
					c.customer_certi_code IS NULL   OR 
					c.cust_cert_star_date IS NULL OR c.cust_cert_end_date IS NULL OR  
					(tad.MOBILE_TEL IS NULL AND c.offen_use_tel IS NULL))
											   ]]>
			</when>
		</choose>
		) A
		where 1 = 1

		<include refid="NB_queryCheckYBTCustomerInfoWhereCondition"></include>
           <![CDATA[ 
	           and rownum <= #{LESS_NUM}  
	           order by A.issue_date, A.customer_type ) B
			 where 1 = 1  
			   and RN > #{GREATER_NUM}
			]]>

	</select>
	
	<!-- 查询 银保通业务客户基本信息核实清单（总数） -->
	<select id="NB_findCheckYBTCustomerInfoTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">

		select count(1)
		from (select rownum RN,
		A.Apply_Code,A.AML_FLAG,a.address,
		A.Policy_Code,
		A.total_prem_af,
		A.service_bank,
		A.service_bank_branch,
		A.branch_organ_code,
		A.organ_code,
		A.submit_channel,
		A.subinput_type,
		A.input_type,
		A.issue_date,
		A.agent_code,
		A.agent_name,
		A.customer_name,
		A.customer_gender,
		A.customer_cert_type,
		A.customer_certi_code,
		A.country_code,
		A.job_code,
		A.cust_cert_star_date,
		A.cust_cert_end_date,
		A.channel_type,
		A.mobile_tel,
		A.house_tel,
		A.office_tel,
		A.offen_use_tel,
		A.company_name,
		A.customer_type,
		A.bank_branch_name
	from (
	select
		ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c.house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address,
		br.bank_branch_name,
		'1' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm
		left join APP___NB__DBUSER.t_nb_policy_holder h on ncm.apply_code = h.apply_code
		left join APP___NB__DBUSER.t_customer c on h.customer_id = c.customer_id
		left join APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code = ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on ncm.service_bank = br.bank_branch_code
		left join APP___NB__DBUSER.t_address tad on tad.address_id = h.address_id
		left join (select cp.apply_code,
		sum(cp.total_prem_af) as total_prem_af 
		   from  APP___NB__DBUSER.t_nb_contract_product cp group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where 
		 ncm.proposal_status in ('10', '13', '14')
		 and ncm.submit_channel = '1'
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR
							 c.customer_cert_type IS NULL OR
							 c.customer_certi_code IS NULL OR c.country_code IS NULL OR
							 c.job_code IS NULL OR c.cust_cert_star_date IS NULL OR
							 c.cust_cert_end_date IS NULL OR
							 tad.ADDRESS IS NULL  OR
							 (tad.MOBILE_TEL IS NULL AND c.OFFEN_USE_TEL IS NULL)) ]]>
			</when>
		</choose>
		union all
		select ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c.house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address,
		br.bank_branch_name,
		'2' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm 
		left join	APP___NB__DBUSER.t_nb_insured_list l on ncm.apply_code = l.apply_code
		left join APP___NB__DBUSER.t_customer c on l.customer_id =c.customer_id
		left join APP___NB__DBUSER.t_address tad on tad.address_id = l.address_id
		left join	APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code =	ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on	ncm.service_bank =	br.bank_branch_code
		left JOIN (select cp.apply_code,sum(cp.total_prem_af) as total_prem_af
		 from APP___NB__DBUSER.t_nb_contract_product cp	group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where
		ncm.proposal_status in ('10','13', '14')
		and ncm.submit_channel = '1'
		and ( C.country_code IS not NULL OR 
		 C.job_code IS NULL OR C.CUSTOMER_NAME IS NULL OR
		 C.customer_gender IS NULL OR C.customer_cert_type IS NULL OR
		 C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR  tad.MOBILE_TEL IS NULL or c.offen_use_tel is null)
		and ( C.job_code IS not NULL OR
		 C.CUSTOMER_NAME IS NULL OR  C.customer_gender IS NULL  OR
		 C.customer_cert_type IS NULL OR	C.customer_certi_code IS NULL OR
		 C.cust_cert_star_date IS NULL OR	C.cust_cert_end_date IS NULL OR
		 tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		and (tad.ADDRESS IS not NULL or
		C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR 
		C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR
		C.cust_cert_star_date IS NULL OR C.cust_cert_end_date IS NULL or
		tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (
					c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR c.customer_cert_type IS NULL OR 
					c.customer_certi_code IS NULL   OR 
					c.cust_cert_star_date IS NULL OR c.cust_cert_end_date IS NULL OR  
					(tad.MOBILE_TEL IS NULL AND c.offen_use_tel IS NULL))
											   ]]>
			</when>
		</choose>
		union all
		select ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c. house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address ,
		br.bank_branch_name,
		'3' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm
		left join APP___NB__DBUSER.t_nb_contract_bene b on ncm.apply_code =b.apply_code
		left join APP___NB__DBUSER.t_customer c on b.customer_id =c.customer_id 
		left join APP___NB__DBUSER.t_address tad on tad.address_id = b.address_id
		left join APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code =ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on ncm.service_bank =br.bank_branch_code
		left JOIN (select cp.apply_code,sum(cp.total_prem_af) as total_prem_af
		 from APP___NB__DBUSER.t_nb_contract_product cp group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where
		ncm.proposal_status in ('10','13', '14')
		and ncm.submit_channel = '1'
		and b.bene_type = 1
		and ( C.country_code IS not NULL OR
		 C.job_code IS NULL OR	C.CUSTOMER_NAME IS NULL OR
		 C.customer_gender IS NULL OR C.customer_cert_type IS NULL OR
		 C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR tad.MOBILE_TEL IS NULL or
		  c.offen_use_tel IS NULL )
		and ( C.job_code IS not NULL OR
		 C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR
		 C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		and (tad.ADDRESS IS not NULL or
		 C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR 
		 C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR
		 C.cust_cert_star_date IS NULL OR C.cust_cert_end_date IS NULL or
		tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (
					c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR c.customer_cert_type IS NULL OR 
					c.customer_certi_code IS NULL   OR 
					c.cust_cert_star_date IS NULL OR c.cust_cert_end_date IS NULL OR  
					(tad.MOBILE_TEL IS NULL AND c.offen_use_tel IS NULL))
											   ]]>
			</when>
		</choose>
		) A
		where 1 = 1

		<include refid="NB_queryCheckYBTCustomerInfoWhereCondition"></include>
           <![CDATA[ 
	           order by A.issue_date, A.customer_type ) B
			]]>
	</select>
	
	<!-- <select id="NB_findCheckYBTCustomerInfoTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
	<![CDATA[
		select count(1)
  			from (select rownum RN,
               A.Apply_Code,A.AML_FLAG,
               A.Policy_Code,
               A.total_prem_af,
               A.service_bank,
               A.service_bank_branch,
               A.branch_organ_code,
               A.organ_code,
               A.submit_channel,
               A.subinput_type,
               A.input_type,
               A.issue_date,
               A.agent_code,
               A.agent_name,
               A.customer_name,
               A.customer_gender,
               A.customer_cert_type,
               A.customer_certi_code,
               A.country_code,
               A.job_code,
               A.cust_cert_star_date,
               A.cust_cert_end_date,
               A.channel_type,
               A.mobile_tel,
               A.house_tel,
               A.office_tel,
               A.offen_use_tel,
               A.company_name,
               A.customer_type,
               A.bank_branch_name
          from (select ncm.apply_code,NCM.AML_FLAG,
                       ncm.policy_code,
                       ncp.total_prem_af,
                       ncm.service_bank,
                       ncm.service_bank_branch,
                       ncm.branch_organ_code,
                       ncm.organ_code,
                       ncm.submit_channel,
                       ncm.subinput_type,
                       ncm.channel_type,
                       ncm.input_type,
                       ncm.issue_date,
                       ta.agent_code,
                       ta.agent_name,
                       c.customer_name,
                       c.customer_gender,
                       c.customer_cert_type,
                       c.customer_certi_code,
                       c.country_code,
                       c.job_code,
                       c.cust_cert_star_date,
                       c.cust_cert_end_date,
                       c.mobile_tel,
                       c.house_tel,
                       c.office_tel,
                       c.offen_use_tel,
                       c.company_name,
                       
                       br.bank_branch_name,
                       '1' as customer_type
                  from APP___NB__DBUSER.t_nb_contract_master ncm,
                       (select cp.apply_code,
                               sum(cp.total_prem_af) as total_prem_af
                          from APP___NB__DBUSER.t_nb_contract_product cp
                         group by cp.apply_code) ncp,
                       APP___NB__DBUSER.t_nb_policy_holder h,
                       APP___NB__DBUSER.t_customer c,
                       
                       APP___NB__DBUSER.t_nb_contract_agent a,
                       APP___NB__DBUSER.t_agent ta,
                       APP___NB__DBUSER.t_Bank_Branch br
                 where ncm.apply_code = ncp.apply_code
                   and ncm.apply_code = h.apply_code
                   and h.customer_id = c.customer_id
                 
                   and ncm.apply_code = a.apply_code
                   and a.agent_code = ta.agent_code
                   and ncm.service_bank=br.bank_branch_code
                   and ncm.proposal_status in ('10', '13', '14')
                   and ncm.submit_channel = '1'
                union all
                select ncm.apply_code,NCM.AML_FLAG,
                       ncm.policy_code,
                       ncp.total_prem_af,
                       ncm.service_bank,
                       ncm.service_bank_branch,
                       ncm.branch_organ_code,
                       ncm.organ_code,
                       ncm.submit_channel,
                       ncm.subinput_type,
                       ncm.channel_type,
                       ncm.input_type,
                       ncm.issue_date,
                       ta.agent_code,
                       ta.agent_name,
                       c.customer_name,
                       c.customer_gender,
                       c.customer_cert_type,
                       c.customer_certi_code,
                       c.country_code,
                       c.job_code,
                       c.cust_cert_star_date,
                       c.cust_cert_end_date,
                       c.mobile_tel,
                       c.house_tel,
                       c.office_tel,
                       c.offen_use_tel,
                       c.company_name,
                       
                       br.bank_branch_name,
                       '2' as customer_type
                  from APP___NB__DBUSER.t_nb_contract_master ncm,
                       (select cp.apply_code,
                               sum(cp.total_prem_af) as total_prem_af
                          from APP___NB__DBUSER.t_nb_contract_product cp
                         group by cp.apply_code) ncp,
                       APP___NB__DBUSER.t_nb_insured_list l,
                       APP___NB__DBUSER.t_customer c,
                       
                       APP___NB__DBUSER.t_nb_contract_agent a,
                       APP___NB__DBUSER.t_agent ta,
                       APP___NB__DBUSER.t_Bank_Branch br
                 where ncm.apply_code = ncp.apply_code
                   and ncm.apply_code = l.apply_code
                   and l.customer_id = c.customer_id
                   
                   and ncm.apply_code = a.apply_code
                   and a.agent_code = ta.agent_code
                   and ncm.service_bank=br.bank_branch_code
                   and ncm.proposal_status in ('10', '13', '14')
                   and ncm.submit_channel = '1'
                union all
                select ncm.apply_code,NCM.AML_FLAG,
                       ncm.policy_code,
                       ncp.total_prem_af,
                       ncm.service_bank,
                       ncm.service_bank_branch,
                       ncm.branch_organ_code,
                       ncm.organ_code,
                       ncm.submit_channel,
                       ncm.subinput_type,
                       ncm.channel_type,
                       ncm.input_type,
                       ncm.issue_date,
                       ta.agent_code,
                       ta.agent_name,
                       c.customer_name,
                       c.customer_gender,
                       c.customer_cert_type,
                       c.customer_certi_code,
                       c.country_code,
                       c.job_code,
                       c.cust_cert_star_date,
                       c.cust_cert_end_date,
                       c.mobile_tel,
                       c.house_tel,
                       c.office_tel,
                       c.offen_use_tel,
                       c.company_name,
                       
                       br.bank_branch_name,
                       '3' as customer_type
                  from APP___NB__DBUSER.t_nb_contract_master ncm,
                       (select cp.apply_code,
                               sum(cp.total_prem_af) as total_prem_af
                          from APP___NB__DBUSER.t_nb_contract_product cp
                         group by cp.apply_code) ncp,
                       APP___NB__DBUSER.t_nb_contract_bene b,
                       APP___NB__DBUSER.t_customer c,
                       
                       APP___NB__DBUSER.t_nb_contract_agent a,
                       APP___NB__DBUSER.t_agent ta,
                       APP___NB__DBUSER.t_Bank_Branch br
                 where ncm.apply_code = ncp.apply_code
                   and ncm.apply_code = b.apply_code
                   and b.customer_id = c.customer_id
                   
                   and ncm.apply_code = a.apply_code
                   and a.agent_code = ta.agent_code
                   and ncm.service_bank=br.bank_branch_code
                   and ncm.proposal_status in ('10', '13', '14')
                   and ncm.submit_channel = '1'
                   and b.bene_type=1
                   ) A
         where 1 = 1 
         	]]>  
         	<include refid="NB_queryCheckYBTCustomerInfoWhereCondition"></include>
           <![CDATA[ 
	           order by A.issue_date, A.customer_type ) B
			]]>
	</select> -->
	
	<!-- 查询 银保通业务客户基本信息核实清单 -->
	<select id="NB_exportCheckYBTCustomerInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		select
		B.apply_code,B.aml_flag,B.address,
		B.policy_Code,
		B.total_prem_af,
		B.service_bank,
		B.service_bank_branch,
		B.branch_organ_code,
		B.organ_code,
		B.submit_channel,
		B.subinput_type,
		B.input_type,
		B.issue_date,
		B.agent_code,
		B.agent_name,
		B.customer_name,
		B.customer_gender,
		B.customer_cert_type,
		B.customer_certi_code,
		B.country_code,
		B.job_code,
		B.cust_cert_star_date,
		B.cust_cert_end_date,
		B.mobile_tel,
		B.house_tel,
		B.office_tel,
		B.offen_use_tel,
		B.company_name,
		B.channel_type,
		B.customer_type,
		B.bank_branch_name 
	from ( 
	  select rownum RN,
		A.Apply_Code,A.AML_FLAG,a.address,
		A.Policy_Code,
		A.total_prem_af,
		A.service_bank,
		A.service_bank_branch,
		A.branch_organ_code,
		A.organ_code,
		A.submit_channel,
		A.subinput_type,
		A.input_type,
		A.issue_date,
		A.agent_code,
		A.agent_name,
		A.customer_name,
		A.customer_gender,
		A.customer_cert_type,
		A.customer_certi_code,
		A.country_code,
		A.job_code,
		A.cust_cert_star_date,
		A.cust_cert_end_date,
		A.channel_type,
		A.mobile_tel,
		A.house_tel,
		A.office_tel,
		A.offen_use_tel,
		A.company_name,
		A.customer_type,
		A.bank_branch_name
		from (
		select
		ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c.house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address,
		br.bank_branch_name,
		'1' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm
		left join APP___NB__DBUSER.t_nb_policy_holder h on ncm.apply_code = h.apply_code
		left join APP___NB__DBUSER.t_customer c on h.customer_id = c.customer_id
		left join APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code = ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on ncm.service_bank = br.bank_branch_code
		left join APP___NB__DBUSER.t_address tad on tad.address_id = h.address_id
		left join (select cp.apply_code,
		sum(cp.total_prem_af) as total_prem_af 
		   from  APP___NB__DBUSER.t_nb_contract_product cp group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where 
		 ncm.proposal_status in ('10', '13', '14')
		 and ncm.submit_channel = '1'
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR
							 c.customer_cert_type IS NULL OR
							 c.customer_certi_code IS NULL OR c.country_code IS NULL OR
							 c.job_code IS NULL OR c.cust_cert_star_date IS NULL OR
							 c.cust_cert_end_date IS NULL OR
							 tad.ADDRESS IS NULL  OR
							 (tad.MOBILE_TEL IS NULL AND c.OFFEN_USE_TEL IS NULL)) ]]>
			</when>
		</choose>
		union all
		select ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c.house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address,
		br.bank_branch_name,
		'2' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm 
		left join	APP___NB__DBUSER.t_nb_insured_list l on ncm.apply_code = l.apply_code
		left join APP___NB__DBUSER.t_customer c on l.customer_id =c.customer_id
		left join APP___NB__DBUSER.t_address tad on tad.address_id = l.address_id
		left join	APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code =	ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on	ncm.service_bank =	br.bank_branch_code
		left JOIN (select cp.apply_code,sum(cp.total_prem_af) as total_prem_af
		 from APP___NB__DBUSER.t_nb_contract_product cp	group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where
		ncm.proposal_status in ('10','13', '14')
		and ncm.submit_channel = '1'
		and ( C.country_code IS not NULL OR 
		 C.job_code IS NULL OR C.CUSTOMER_NAME IS NULL OR
		 C.customer_gender IS NULL OR C.customer_cert_type IS NULL OR
		 C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR  tad.MOBILE_TEL IS NULL or c.offen_use_tel is null)
		and ( C.job_code IS not NULL OR
		 C.CUSTOMER_NAME IS NULL OR  C.customer_gender IS NULL  OR
		 C.customer_cert_type IS NULL OR	C.customer_certi_code IS NULL OR
		 C.cust_cert_star_date IS NULL OR	C.cust_cert_end_date IS NULL OR
		 tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		and (tad.ADDRESS IS not NULL or
		C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR 
		C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR
		C.cust_cert_star_date IS NULL OR C.cust_cert_end_date IS NULL or
		tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (
					c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR c.customer_cert_type IS NULL OR 
					c.customer_certi_code IS NULL   OR 
					c.cust_cert_star_date IS NULL OR c.cust_cert_end_date IS NULL OR  
					(tad.MOBILE_TEL IS NULL AND c.offen_use_tel IS NULL))
											   ]]>
			</when>
		</choose>
		union all
		select ncm.apply_code,NCM.AML_FLAG,
		ncm.policy_code,
		ncp.total_prem_af,
		ncm.service_bank,
		ncm.service_bank_branch,
		ncm.branch_organ_code,
		ncm.organ_code,
		ncm.submit_channel,
		ncm.subinput_type,
		ncm.channel_type,
		ncm.input_type,
		ncm.issue_date,
		ta.agent_code,
		ta.agent_name,
		c.customer_name,
		c.customer_gender,
		c.customer_cert_type,
		c.customer_certi_code,
		c.country_code,
		c.job_code,
		c.cust_cert_star_date,
		c.cust_cert_end_date,
		tad.mobile_tel,
		c. house_tel,
		c.office_tel,
		c.offen_use_tel,
		c.company_name,
		tad.address ,
		br.bank_branch_name,
		'3' as customer_type
		from APP___NB__DBUSER.t_nb_contract_master ncm
		left join APP___NB__DBUSER.t_nb_contract_bene b on ncm.apply_code =b.apply_code
		left join APP___NB__DBUSER.t_customer c on b.customer_id =c.customer_id 
		left join APP___NB__DBUSER.t_address tad on tad.address_id = b.address_id
		left join APP___NB__DBUSER.t_nb_contract_agent a on ncm.apply_code = a.apply_code
		left join APP___NB__DBUSER.t_agent ta on a.agent_code =ta.agent_code
		left join APP___NB__DBUSER.t_Bank_Branch br on ncm.service_bank =br.bank_branch_code
		left JOIN (select cp.apply_code,sum(cp.total_prem_af) as total_prem_af
		 from APP___NB__DBUSER.t_nb_contract_product cp group by cp.apply_code) ncp on ncm.apply_code = ncp.apply_code
		where
		ncm.proposal_status in ('10','13', '14')
		and ncm.submit_channel = '1'
		and b.bene_type = 1
		and ( C.country_code IS not NULL OR
		 C.job_code IS NULL OR	C.CUSTOMER_NAME IS NULL OR
		 C.customer_gender IS NULL OR C.customer_cert_type IS NULL OR
		 C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR tad.MOBILE_TEL IS NULL or
		  c.offen_use_tel IS NULL )
		and ( C.job_code IS not NULL OR
		 C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR
		 C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR C.cust_cert_star_date IS NULL OR
		 C.cust_cert_end_date IS NULL OR tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		and (tad.ADDRESS IS not NULL or
		 C.CUSTOMER_NAME IS NULL OR C.customer_gender IS NULL OR 
		 C.customer_cert_type IS NULL OR C.customer_certi_code IS NULL OR
		 C.cust_cert_star_date IS NULL OR C.cust_cert_end_date IS NULL or
		tad.MOBILE_TEL IS NULL or c.offen_use_tel IS NULL )
		<choose>
			<when test=" customer_base_info == null or customer_base_info == '' ">
				<![CDATA[ AND (
					c.CUSTOMER_NAME IS NULL OR c.customer_gender IS NULL OR c.customer_cert_type IS NULL OR 
					c.customer_certi_code IS NULL   OR 
					c.cust_cert_star_date IS NULL OR c.cust_cert_end_date IS NULL OR  
					(tad.MOBILE_TEL IS NULL AND c.offen_use_tel IS NULL))
											   ]]>
			</when>
		</choose>
		) A
		where 1 = 1
		
		<include refid="NB_queryCheckYBTCustomerInfoWhereCondition"></include>
           <![CDATA[ 
	           order by A.issue_date, A.customer_type ) B
			]]>

	</select>
	
</mapper>
