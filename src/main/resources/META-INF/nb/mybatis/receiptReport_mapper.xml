<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.impl.nb.dao.impl.ReceiptReportDaoImpl">

	<!-- 查询所有销售渠道操作  -->
	<select id="NB_findAllChannelType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT A.SALES_CHANNEL_NAME TYPE_NAME, A.SALES_CHANNEL_CODE INDIVIDUAL_GROUP FROM APP___NB__DBUSER.t_sales_channel A  ]]>
		<![CDATA[ ORDER BY INDIVIDUAL_GROUP ]]>
	</select>
	
	<!-- 查询所有回执签收方式 -->
	<select id="NB_findAllSignSource" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT SOURCE_CODE, SOURCE_DESC FROM APP___NB__DBUSER.t_sign_source ]]>
		<![CDATA[ ORDER BY SOURCE_CODE ]]>
	</select>
	
	<!-- 查询总条数 -->
	<select id="NB_queryAllReceiptforCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[	
			SELECT	COUNT(1)
				FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				    LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON CM.POLICY_CODE=PA.POLICY_CODE
				    
				    LEFT JOIN APP___NB__DBUSER.T_NB_QT_TASK QT ON  CM.APPLY_CODE  = QT.APPLY_CODE AND QT.QA_TYPE = '5'
				    INNER JOIN APP___NB__DBUSER.T_NB_POLICY_HOLDER PH ON CM.POLICY_CODE=PH.POLICY_CODE
		    		LEFT JOIN  APP___NB__DBUSER.T_IMAGE_SCAN IMS ON IMS.APPLY_CODE = CM.APPLY_CODE
                       AND IMS.BILLCARD_CODE = 'UN004' 
				    LEFT JOIN APP___NB__DBUSER.T_CUSTOMER CU ON PH.CUSTOMER_ID=CU.CUSTOMER_ID
				    LEFT JOIN APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA ON CM.POLICY_CODE=CA.POLICY_CODE
				    LEFT JOIN APP___NB__DBUSER.T_AGENT AG ON CA.AGENT_CODE=AG.AGENT_CODE
			        LEFT JOIN APP___NB__DBUSER.T_AGENT_LEVEL TAL  ON TAL.AGENT_LEVEL_CODE = AG.AGENT_LEVEL
			        LEFT JOIN  APP___NB__DBUSER.T_UDMP_USER UU ON UU.USER_ID = PA.OPERATOR_ID       
                    LEFT JOIN  APP___NB__DBUSER.T_UDMP_ORG_REL RE ON RE.ORGAN_CODE = UU.ORGAN_CODE
			        LEFT JOIN APP___NB__DBUSER.T_BANK_BRANCH BANK ON BANK.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH
			        LEFT JOIN DEV_NB.T_SALES_ORGAN SO1 ON SO1.SALES_ORGAN_CODE=AG.SALES_ORGAN_CODE
             		LEFT JOIN DEV_NB.T_SALES_ORGAN SO2 ON SO1.PARENT_CODE = SO2.SALES_ORGAN_CODE
             		LEFT JOIN DEV_NB.T_SALES_ORGAN SO3 ON SO2.PARENT_CODE = SO3.SALES_ORGAN_CODE 
             		LEFT JOIN APP___NB__DBUSER.T_SALES_CHANNEL SC ON CM.CHANNEL_TYPE=SC.SALES_CHANNEL_CODE
		    			WHERE 1=1 
		    			 AND EXISTS (SELECT TU.ORGAN_CODE FROM DEV_NB.T_UDMP_ORG TU WHERE TU.ORGAN_CODE LIKE CONCAT(#{organ_code}, '%') AND CM.ORGAN_CODE =TU.ORGAN_CODE)  
		    				AND CM.PROPOSAL_STATUS   IN  ('01','02','03','04','05','06','07','09','10','13','14','15','16','17','18','19','20','21','23','28','30','31','32','42','43','51','52','53','34','35','36','37','38') ]]>
		    <include refid="NB_queryAllReceipt"></include>
		
		<if test=" is_scan  != null and is_scan !='' "><!-- 回访扫描 -->
			<![CDATA[  AND IS_SCAN = (${is_scan}) ]]>   
		</if>
		<if test=" sign_type  != null and sign_type !='' "><!-- 签收方式 -->
	    	<![CDATA[  AND T.SIGN_TYPE in ( ${sign_type} )]]>   
	  	</if>
	</select>
	
	<!-- 分页查询 -->
	<select id="NB_queryAllReceiptforPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	
			SELECT	AL.POLICY_CODE,AL.APPLY_CODE,AL.CHANNEL_TYPE,AL.ORGAN_CODE,AL.BUSINESS_PRODUCT_CODE,
					AL.PREM,AL.APPLY_DATE,AL.SIGN_DATE,AL.INPUT_DATE,AL.RECEIPT_SIGN_DATE,AL.ORGAN2_NAME,AL.ORGAN3_NAME,
					AL.ORGAN4_NAME,AL.SCAN_DATE,AL.IS_SCAN,AL.CUSTOMER,AL.SALE_PERSON,AL.SIGN_TYPE,AL.QUALITY_FLAG,
					AL.SUBINPUT_TYPE,
					AL.SALES_ORGAN_NAME_R,
					AL.SALES_ORGAN_NAME_D,
					AL.SALES_ORGAN_NAME_G,
					AL.BANK_BRANCH_NAME AS BANK_BRANCH_NAME,
					OPERATOR,
					AL.AGENT_LEVEL,
					AL.AGENT_LEVEL_DESC   
			FROM ( SELECT 	
						ROWNUM RN,T.POLICY_CODE,T.APPLY_CODE,T.CHANNEL_TYPE,T.ORGAN_CODE,T.BUSINESS_PRODUCT_CODE,
						T.PREM,T.APPLY_DATE,T.SIGN_DATE,T.INPUT_DATE,T.RECEIPT_SIGN_DATE,T.ORGAN2_NAME,T.ORGAN3_NAME,
						T.ORGAN4_NAME,T.SCAN_DATE,T.IS_SCAN,T.CUSTOMER,T.SALE_PERSON,T.SIGN_TYPE,T.QUALITY_FLAG,
						T.SUBINPUT_TYPE,
						T.SALES_ORGAN_NAME_R,
	                    T.SALES_ORGAN_NAME_D,
	                    T.SALES_ORGAN_NAME_G,
	                    T.BANK_BRANCH_NAME AS BANK_BRANCH_NAME,
						OPERATOR,
	                    T.AGENT_LEVEL,
	                    T.AGENT_LEVEL_DESC 
					FROM (
				    	SELECT  CM.POLICY_CODE,
						    CM.APPLY_CODE,
						    SC.SALES_CHANNEL_NAME CHANNEL_TYPE,
						    CM.ORGAN_CODE,
						    (SELECT TO_CHAR(WM_CONCAT(P.PRODUCT_CODE)) BUSINESS_PRODUCT_CODE 
			                     FROM APP___NB__DBUSER.T_NB_CONTRACT_BUSI_PROD P
			                     WHERE P.MASTER_BUSI_ITEM_ID IS NULL AND P.POLICY_CODE=CM.POLICY_CODE GROUP BY P.POLICY_CODE) BUSINESS_PRODUCT_CODE,
						     ( SELECT
                                   SUM(PROD.TOTAL_PREM_AF) PREM
                              FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT PROD
                              WHERE  CM.APPLY_CODE = PROD.APPLY_CODE
                             ) PREM,
						    to_char(CM.APPLY_DATE, 'yyyy-mm-dd') APPLY_DATE,
						    CM.PROPOSAL_STATUS,
						    to_char(CM.ISSUE_DATE, 'yyyy-mm-dd') SIGN_DATE,
						    to_char(PA.BRANCH_RECEIVE_DATE, 'yyyy-mm-dd') INPUT_DATE,
						    to_char(PA.ACKNOWLEDGE_DATE, 'yyyy-mm-dd') RECEIPT_SIGN_DATE,
						    (SELECT O1.ORGAN_NAME FROM  DEV_NB.T_UDMP_ORG O1 WHERE  O1.ORGAN_CODE=SUBSTR(CM.ORGAN_CODE, 0, 4)) ORGAN2_NAME,
                            (SELECT O1.ORGAN_NAME FROM  DEV_NB.T_UDMP_ORG O1 WHERE  O1.ORGAN_CODE=SUBSTR(CM.ORGAN_CODE, 0, 6)) ORGAN3_NAME,
                            (SELECT O1.ORGAN_NAME FROM  DEV_NB.T_UDMP_ORG O1 WHERE  O1.ORGAN_CODE=SUBSTR(CM.ORGAN_CODE, 0, 8)) ORGAN4_NAME,
						    to_char(IMS.SCAN_TIME, 'yyyy-mm-dd') SCAN_DATE,
						    (CASE WHEN IMS.SCAN_TIME IS NOT NULL THEN '1'
		                     		ELSE '0' END)AS IS_SCAN,
						    CU.CUSTOMER_NAME CUSTOMER,
						    CASE WHEN CM.POLICY_CODE IS NULL THEN ''
		         					ELSE CONCAT(CONCAT(AG.AGENT_CODE, '-'), AG.AGENT_NAME) END SALE_PERSON,
						    (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL 
      								THEN '1'
          						  WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 
      								THEN '0' 
          						  WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 
      								THEN '0' 
        						  WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0
      								THEN '1'
        						  ELSE '' END) AS SIGN_TYPE,/*40588 LIUPENGIT1*/
						      CASE WHEN QT.QT_STATUS = 7 THEN '是' WHEN QT.QT_STATUS IS NULL THEN '' ELSE '否' END AS QUALITY_FLAG,
						   
						    CM.SUBINPUT_TYPE,
						    CASE
               			    WHEN UU.USER_NAME IS NOT NULL THEN
               			     UU.USER_NAME||'-'||UU.REAL_NAME||'-'||RE.ORGAN_CODE||'-'||RE.ORGAN_NAME
                		    ELSE
                 			     ''
                			    END AS OPERATOR,
						    SO3.SALES_ORGAN_NAME AS SALES_ORGAN_NAME_R,
                            SO2.SALES_ORGAN_NAME AS SALES_ORGAN_NAME_D,
                            SO1.SALES_ORGAN_NAME AS SALES_ORGAN_NAME_G,
		               		BANK.BANK_BRANCH_NAME AS BANK_BRANCH_NAME,
		                    T.AGENT_LEVEL,
		                    T.AGENT_LEVEL_DESC 
						FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
						    LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON CM.POLICY_CODE=PA.POLICY_CODE
						    
						    LEFT JOIN APP___NB__DBUSER.T_NB_QT_TASK QT ON CM.APPLY_CODE  = QT.APPLY_CODE AND QT.QA_TYPE = '5'
						    
						    INNER JOIN APP___NB__DBUSER.T_NB_POLICY_HOLDER PH ON CM.POLICY_CODE=PH.POLICY_CODE
						    
				    		LEFT JOIN  APP___NB__DBUSER.T_IMAGE_SCAN IMS ON IMS.APPLY_CODE = CM.APPLY_CODE
                       			AND IMS.BILLCARD_CODE = 'UN004'
						    LEFT JOIN APP___NB__DBUSER.T_CUSTOMER CU ON PH.CUSTOMER_ID=CU.CUSTOMER_ID
						    LEFT JOIN APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA ON CM.POLICY_CODE=CA.POLICY_CODE
						    LEFT JOIN APP___NB__DBUSER.T_AGENT AG ON CA.AGENT_CODE=AG.AGENT_CODE
					        LEFT JOIN APP___NB__DBUSER.T_AGENT_LEVEL TAL  ON TAL.AGENT_LEVEL_CODE = AG.AGENT_LEVEL
					        LEFT JOIN  APP___NB__DBUSER.T_UDMP_USER UU ON UU.USER_ID = PA.OPERATOR_ID       
                 		    LEFT JOIN  APP___NB__DBUSER.T_UDMP_ORG_REL RE ON RE.ORGAN_CODE = UU.ORGAN_CODE
					        LEFT JOIN APP___NB__DBUSER.T_BANK_BRANCH BANK ON BANK.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH
					        LEFT JOIN DEV_NB.T_SALES_ORGAN SO1 ON SO1.SALES_ORGAN_CODE=AG.SALES_ORGAN_CODE
                  			LEFT JOIN DEV_NB.T_SALES_ORGAN SO2 ON SO1.PARENT_CODE = SO2.SALES_ORGAN_CODE
                  			LEFT JOIN DEV_NB.T_SALES_ORGAN SO3 ON SO2.PARENT_CODE = SO3.SALES_ORGAN_CODE 
                  			LEFT JOIN APP___NB__DBUSER.T_SALES_CHANNEL SC ON CM.CHANNEL_TYPE=SC.SALES_CHANNEL_CODE
				    			WHERE 1=1 
				    			 AND EXISTS (SELECT TU.ORGAN_CODE FROM DEV_NB.T_UDMP_ORG TU WHERE TU.ORGAN_CODE LIKE CONCAT(#{organ_code}, '%') AND CM.ORGAN_CODE =TU.ORGAN_CODE)  
				    				AND CM.PROPOSAL_STATUS   IN  ('01','02','03','04','05','06','07','09','10','13','14','15','16','17','18','19','20','21','23','28','30','31','32','42','43','51','52','53','34','35','36','37','38') ]]>
				    <include refid="NB_queryAllReceipt"></include>				    
				    ORDER BY CM.POLICY_ID DESC
				) T WHERE 1=1
				<if test=" is_scan  != null and is_scan !='' "><!-- 回访扫描 -->
					<![CDATA[  AND IS_SCAN = (${is_scan}) ]]>   
				</if>
				<if test=" sign_type  != null and sign_type !='' "><!-- 签收方式 -->
	    			<![CDATA[  AND T.SIGN_TYPE in ( ${sign_type} ) ]]>   
	  			</if>
				) AL
		<![CDATA[  WHERE 1=1 AND AL.RN <= #{LESS_NUM} AND AL.RN > #{GREATER_NUM} 
		]]>
	</select>
	
	
	<!-- 回执操作人-->
	<select id="NB_queryOperator" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT U.USER_NAME OPERATOR,U.REAL_NAME CUSTOMER,O.ORGAN_CODE ORGAN_CODE,O.ORGAN_NAME ORGAN_NAME
         			 FROM APP___NB__DBUSER.T_UDMP_USER U LEFT JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE=O.ORGAN_CODE
         			 WHERE U.USER_ID=(SELECT CASE WHEN (A.OPERATOR_ID=0 OR A.OPERATOR_ID IS NULL) THEN A.UPDATE_BY
       					ELSE A.OPERATOR_ID END OPERATOR_ID FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT A
         			 WHERE A.POLICY_CODE=#{policy_code,jdbcType=VARCHAR})
		]]>
	</select>
	
	<!-- 销售渠道查询 -->
	<select id="NB_getChannelType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT to_char(wm_concat(C.SALES_CHANNEL_NAME)) CHANNEL_TYPE FROM APP___NB__DBUSER.T_SALES_CHANNEL C
					WHERE C.SALES_CHANNEL_CODE IN (${channel_type})
		]]>
	</select>
	
	<sql id="NB_queryAllReceipt">
    	<if test=" channel_type  != null and channel_type !='' "><!-- 销售渠道 -->
		    <![CDATA[  AND CM.CHANNEL_TYPE IN (${channel_type}) ]]>
		</if>
		<if test=" sign_start_date  != null and sign_start_date !='' "><!-- 签单起期 -->
	    	<![CDATA[  AND CM.ISSUE_DATE BETWEEN TO_DATE(CONCAT(#{sign_start_date},' 00:00:00'),'yyyy-mm-dd hh24:mi:ss') AND  TO_DATE(CONCAT(#{sign_end_date},' 23:59:59'),'yyyy-mm-dd hh24:mi:ss')]]>   
	  	</if>  
	    <if test=" start_date != null and start_date !='' and policy_type != '0'.toString() "><!-- 录入起期 -->
		<![CDATA[ AND PA.BRANCH_RECEIVE_DATE >= to_date(#{start_date} || '00:00:00','YYYY-MM-DD HH24:MI:SS') AND PA.BRANCH_RECEIVE_DATE <= TO_DATE(#{end_date} ||'23:59:59','YYYY-MM-DD HH24:MI:SS') ]]>
	  	</if>
	    <if test=" policy_type  != null and policy_type !='' and policy_type == '1'.toString() "><!-- 统计类型 -->
	    	<![CDATA[  AND PA.ACKNOWLEDGE_DATE IS NOT NULL ]]>   
	  	</if>
	    <if test=" policy_type  != null and policy_type !='' and policy_type == '0'.toString() "><!-- 统计类型 -->
	    	<![CDATA[  AND PA.ACKNOWLEDGE_DATE IS NULL]]>   
	  	</if>
	    <if test=" quality_flag  != null and quality_flag !='' and quality_flag == '1'.toString() "><!-- 质检 -->
	    	<![CDATA[  AND QT.qt_status = 7 ]]>   
	  	</if>
	  	<if test=" quality_flag  != null and quality_flag !='' and quality_flag == '0'.toString() "><!-- 质检 -->
	    	<![CDATA[  AND ( QT.qt_status != 7 OR QT.qt_status IS NULL) ]]>   
	  	</if>
		<if test=" subinput_type  != null and subinput_type !='' "><!-- 电子渠道 -->
        	<![CDATA[  AND CM.SUBINPUT_TYPE = ${subinput_type} ]]>   
      	</if>
	 </sql>
	 	 
	 
	<!-- 回执清单优化:2021-03-06 --> 
	<select id="NB_queryAllReceiptforNewCount1" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(*) FROM (SELECT * FROM (SELECT * FROM (
				SELECT TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
					   TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
					   (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
					   (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		          FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
	        	INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
		                      FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				 INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
				LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST 
		        ON MST.APPLY_CODE = CM.APPLY_CODE
				LEFT JOIN (SELECT IMG.SCAN_TIME,IMG.BUSS_CODE FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG
		        WHERE IMG.BILLCARD_CODE = 'UN004'
		        )IMSCAN ON IMSCAN.BUSS_CODE = CM.APPLY_CODE
		         WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
		           AND  (CM.POLICY_REINSURE_FLAG !='3' OR CM.POLICY_REINSURE_FLAG IS NULL)
		           AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
	 	<!-- 续保转保 -->        
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if>	 		 		 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>       	
	  	<![CDATA[ ) A WHERE 1 = 1 ]]>		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND A.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ) B  ]]>
	</select>
	
	<!-- 回执清单优化:2021-03-06 -->
	<select id="NB_queryAllReceiptforNewPage1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM ROM,B.*  FROM (SELECT ROWNUM RN, AA.* FROM (SELECT A.* FROM (
		  	   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				      (SELECT TO_CHAR(WM_CONCAT(P.PRODUCT_CODE)) FROM APP___NB__DBUSER.T_NB_CONTRACT_BUSI_PROD P WHERE P.MASTER_BUSI_ITEM_ID IS NULL AND P.POLICY_CODE = CM.POLICY_CODE GROUP BY P.POLICY_CODE) AS BUSINESS_PRODUCT_CODE,
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
              		  TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
					  (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE = cm.POLICY_CODE AND pp.PRINT_TYPE = '1' AND pp.PRINT_STATUS = '3') > 0 
					  	THEN
				          TO_CHAR((SELECT max(pp.BPO_PRINT_DATE)
				                    FROM DEV_NB.T_POLICY_PRINT PP
				                   WHERE pp.POLICY_CODE = cm.POLICY_CODE),
				                  'YYYY-MM-DD')
				        ELSE ''
				      END) AS PRINT_TIME,
					  (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      /*TO_CHAR((SELECT MAX(PA.ACKNOWLEDGE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,*/
				      TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
				      /*TO_CHAR((SELECT MAX(PA.BRANCH_RECEIVE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS INPUT_DATE,*/
				      (CASE WHEN TO_DATE(TO_CHAR(PA.BRANCH_RECEIVE_DATE,'HH24:MI:SS'),'HH24:MI:SS')>TO_DATE('00:00:00', 'HH24:MI:SS')
                  		THEN TO_CHAR(PA.BRANCH_RECEIVE_DATE, 'YYYY-MM-DD HH24:MI:SS')
                    	ELSE TO_CHAR(PA.BRANCH_RECEIVE_DATE, 'YYYY-MM-DD') END )BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(SELECT (CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.UPDATE_BY) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.OPERATOR_ID) END) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) AS OPERATOR,*/
					  (case when CM.media_type = '1' then 
				           (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY)
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source = '0' and PA.OPERATOR_ID is not null then 
				                 (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.OPERATOR_ID) 
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source != '0' then 
				                (select
				                    CA.Agent_Code || '-' || CA.AGENT_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                from APP___NB__DBUSER.T_UDMP_ORG_REL O
				                WHERE O.ORGAN_CODE = CA.Agent_Organ_Code)
				         else 
				         	(CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) 
						        THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME 
						             FROM APP___NB__DBUSER.T_UDMP_USER U 
						              INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE      
						              WHERE U.USER_ID = PA.UPDATE_BY) 
						         ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME 
						              FROM APP___NB__DBUSER.T_UDMP_USER U 
						              INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE 
						              WHERE U.USER_ID = PA.OPERATOR_ID) 
						          END) 
				         end) as OPERATOR,  

				      /*(CASE WHEN CM.MEDIA_TYPE = '1' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NOT NULL AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, \*40588 LIUPENGIT1*\*/
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		 ,(case  
		 when (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = cm.apply_code) > 0 /*电子保单申请过纸质保单*/
		 then (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD LEFT JOIN DEV_NB.T_POLICY_PRINT_SOURCE PPS ON PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE WHERE PPD.APPLY_CODE = cm.APPLY_CODE AND ROWNUM <= 1)
		 when to_number(cm.subinput_type) <> 15 and cm.submit_channel = '1' and tt.media_type in ('0','2') /*保全做过变更并且是电子保单非银保通电子渠道出单*/
		 then  (SELECT tst.type_desc FROM dev_nb.t_input_type tst where  cm.input_type = tst.type_code)
		 when to_number(cm.subinput_type) <> 15 and cm.submit_channel = '1' and cm.media_type = '0' and mst.media_type = '1' and tt.media_type = '1' /*判断完保全再判断契约*/
		 then  ''
		 when tt.media_type is not null and (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = cm.apply_code) = 0 and cm.media_type <> tt.media_type
		  then ''
		 ELSE ''
		  end )as PRINT_SOURCE_NAME/*申请渠道#64327*/
	   
	   ,(case when tt.media_type is not null 
		   then (SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  tt.media_type=TMT.MEDIA_TYPE_CODE)
		   else (SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  mst.media_type=TMT.MEDIA_TYPE_CODE)
		   end
		) as MEDIA_TYPE_DESC/*保单类型#64327*/,PA.SIGN_SOURCE /*签收平台*/,
		(CASE
         WHEN CM.JOINTLY_INSURED_TYPE = '1' THEN
          (SELECT LISTAGG(APPLY_CODE, '/') WITHIN GROUP(ORDER BY APPLY_CODE) AS A1
             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
            WHERE JIP.MAIN_APPLY_CODE = CM.APPLY_CODE
            AND JIP.APPLY_CODE  in (SELECT A3.APPLY_CODE  FROM DEV_NB.T_NB_CONTRACT_MASTER A3 WHERE A3.LIABILITY_STATE  not in ('2','3','4') AND A3.JOINTLY_INSURED_TYPE = '2')
            AND JIP.MAIN_APPLY_CODE not in
				(SELECT MAS1.APPLY_CODE
				FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
				LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
					ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
				WHERE MAS1.Jointly_Insured_Type = '1'
					AND MAS1.Multi_Mainrisk_Flag = '1'
					AND PRO1.PRODUCT_CODE = '00822000'
					AND PRO1.DECISION_CODE in ('40', '50')))
         WHEN CM.JOINTLY_INSURED_TYPE = '2' THEN
          (SELECT JIP.MAIN_APPLY_CODE AS A2
             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
            WHERE JIP.APPLY_CODE = CM.APPLY_CODE
            AND JIP.MAIN_APPLY_CODE not in
				(SELECT MAS1.APPLY_CODE
				FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
				LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
					ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
				WHERE MAS1.Jointly_Insured_Type = '1'
					AND MAS1.Multi_Mainrisk_Flag = '1'
					AND PRO1.PRODUCT_CODE = '00822000'
					AND PRO1.DECISION_CODE in ('40', '50')))
         ELSE
          ''
       END) AS JOINTLY_INSURED_POLICY
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
				LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST 
				ON MST.APPLY_CODE = CM.APPLY_CODE
				 LEFT JOIN 
				 (select log_id,policy_code,media_type  from 
	                   (select    mstg.log_id,mstg.policy_code,mstg.media_type 
	                   ,row_number() over(partition by mstg.policy_code order by mstg.log_id  asc ) rn
	                   from dev_pas.t_contract_master_log mstg)
	                   where rn = 1) tt ON tt.policy_code = CM.policy_code
	            LEFT JOIN (SELECT IMG.SCAN_TIME,IMG.BUSS_CODE FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG
		         WHERE IMG.BILLCARD_CODE = 'UN004'
		         )IMSCAN ON IMSCAN.BUSS_CODE = CM.APPLY_CODE
				WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
				AND  (CM.POLICY_REINSURE_FLAG !='3' OR CM.POLICY_REINSURE_FLAG IS NULL)
				  AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
				   ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if><!-- 续保转保 -->  				
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>		
	  	<![CDATA[ ) A WHERE 1 = 1 ]]>		<![CDATA[ ) AA WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND AA.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND AA.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (AA.QT_STATUS != 7 OR AA.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND AA.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND AA.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY AA.INPUT_DATE) B  WHERE B.RN  BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>
	
	<!-- 回执清单优化:2021-03-06 --> 
	<select id="NB_queryAllReceiptforNewCount2" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT C.* FROM (SELECT B.* FROM (SELECT COUNT(1) FROM (
				SELECT CM.POLICY_CODE,CM.APPLY_CODE,CM.ORGAN_CODE,
        		TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
        		'' AS SIGN_TYPE,
        		(SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
         		FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
        		INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
                     FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
           		LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
        		LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST ON MST.APPLY_CODE = CM.APPLY_CODE
         		LEFT JOIN 
		         (SELECT LOG_ID,POLICY_CODE,MEDIA_TYPE  FROM 
		                    (SELECT    MSTG.LOG_ID,MSTG.POLICY_CODE,MSTG.MEDIA_TYPE 
		                    ,ROW_NUMBER() OVER(PARTITION BY MSTG.POLICY_CODE ORDER BY MSTG.LOG_ID  ASC ) RN
		                    FROM DEV_PAS.T_CONTRACT_MASTER_LOG MSTG)
		                    WHERE RN = 1) TT ON TT.POLICY_CODE = CM.POLICY_CODE
                LEFT JOIN (SELECT IMG.SCAN_TIME,IMG.BUSS_CODE FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG
		         WHERE IMG.BILLCARD_CODE = 'UN004'
		         )IMSCAN ON IMSCAN.BUSS_CODE = CM.APPLY_CODE
      			WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33') 
      			AND  (CM.POLICY_REINSURE_FLAG !='3' OR CM.POLICY_REINSURE_FLAG IS NULL)      				
      				AND PA.ACKNOWLEDGE_DATE IS NULL
		           AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
	 	<!-- 续保转保 -->        
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if>	 		 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ) B) C ]]>
	</select>
	
	<!-- 回执清单优化:2021-03-06 -->
	<select id="NB_queryAllReceiptforNewPage2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM ROM, C.* FROM (SELECT ROWNUM RN, B.* FROM (SELECT A.* FROM (
			   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				      (SELECT TO_CHAR(WM_CONCAT(P.PRODUCT_CODE)) FROM APP___NB__DBUSER.T_NB_CONTRACT_BUSI_PROD P WHERE P.MASTER_BUSI_ITEM_ID IS NULL AND P.POLICY_CODE = CM.POLICY_CODE GROUP BY P.POLICY_CODE) AS BUSINESS_PRODUCT_CODE,
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE = cm.POLICY_CODE AND pp.PRINT_TYPE = '1' AND pp.PRINT_STATUS = '3') > 0 
					  	THEN
				          TO_CHAR((SELECT max(pp.BPO_PRINT_DATE)
				                    FROM DEV_NB.T_POLICY_PRINT PP
				                   WHERE pp.POLICY_CODE = cm.POLICY_CODE),
				                  'YYYY-MM-DD')
				        ELSE ''
				      END) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      NULL AS RECEIPT_SIGN_DATE,
				      NULL AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(CASE WHEN (NULL = 0 OR NULL IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) END) AS OPERATOR,*/
              		  NULL AS OPERATOR,
				      '' AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		 ,(case  
		 when (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = cm.apply_code) > 0 /*电子保单申请过纸质保单*/
		 then (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD LEFT JOIN DEV_NB.T_POLICY_PRINT_SOURCE PPS ON PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE WHERE PPD.APPLY_CODE = cm.APPLY_CODE AND ROWNUM <= 1)
		 when to_number(cm.subinput_type) <> 15 and cm.submit_channel = '1' and tt.media_type in ('0','2') /*保全做过变更并且是电子保单非银保通电子渠道出单*/
		 then  (SELECT tst.type_desc FROM dev_nb.t_input_type tst where  cm.input_type = tst.type_code)
		 when to_number(cm.subinput_type) <> 15 and cm.submit_channel = '1' and cm.media_type = '0' and mst.media_type = '1' and tt.media_type = '1' /*判断完保全再判断契约*/
		 then  ''
		 when tt.media_type is not null and (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = cm.apply_code) = 0 and cm.media_type <> tt.media_type
		  then ''
		 ELSE ''
		  end )as PRINT_SOURCE_NAME/*申请渠道#64327*/
	   
	   ,(case when tt.media_type is not null 
		   then (SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  tt.media_type=TMT.MEDIA_TYPE_CODE)
		   else (SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  mst.media_type=TMT.MEDIA_TYPE_CODE)
		   end
		) as MEDIA_TYPE_DESC/*保单类型#64327*/,
		(CASE
         WHEN CM.JOINTLY_INSURED_TYPE = '1' THEN
          (SELECT LISTAGG(APPLY_CODE, '/') WITHIN GROUP(ORDER BY APPLY_CODE) AS A1
             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
            WHERE JIP.MAIN_APPLY_CODE = CM.APPLY_CODE
            AND JIP.APPLY_CODE  in (SELECT A3.APPLY_CODE  FROM DEV_NB.T_NB_CONTRACT_MASTER A3 WHERE A3.LIABILITY_STATE  not in ('2','3','4') AND A3.JOINTLY_INSURED_TYPE = '2')
            AND JIP.MAIN_APPLY_CODE not in
				(SELECT MAS1.APPLY_CODE
				FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
				LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
					ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
				WHERE MAS1.Jointly_Insured_Type = '1'
					AND MAS1.Multi_Mainrisk_Flag = '1'
					AND PRO1.PRODUCT_CODE = '00822000'
					AND PRO1.DECISION_CODE in ('40', '50')))
         WHEN CM.JOINTLY_INSURED_TYPE = '2' THEN
          (SELECT JIP.MAIN_APPLY_CODE AS A2
             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
            WHERE JIP.APPLY_CODE = CM.APPLY_CODE
            AND JIP.MAIN_APPLY_CODE not in
				(SELECT MAS1.APPLY_CODE
				FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
				LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
					ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
				WHERE MAS1.Jointly_Insured_Type = '1'
					AND MAS1.Multi_Mainrisk_Flag = '1'
					AND PRO1.PRODUCT_CODE = '00822000'
					AND PRO1.DECISION_CODE in ('40', '50')))
         ELSE
          ''
       END) AS JOINTLY_INSURED_POLICY
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
            LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
				LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST 
				ON MST.APPLY_CODE = CM.APPLY_CODE
				 LEFT JOIN 
				 (select log_id,policy_code,media_type  from 
                    (select    mstg.log_id,mstg.policy_code,mstg.media_type 
                    ,row_number() over(partition by mstg.policy_code order by mstg.log_id  asc ) rn
                    from dev_pas.t_contract_master_log mstg)
                    where rn = 1) tt ON tt.policy_code = CM.policy_code
                    LEFT JOIN (SELECT IMG.SCAN_TIME,IMG.BUSS_CODE FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG
		         WHERE IMG.BILLCARD_CODE = 'UN004'
		         )IMSCAN ON IMSCAN.BUSS_CODE = CM.APPLY_CODE
				  WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
  	              AND  (CM.POLICY_REINSURE_FLAG !='3' OR CM.POLICY_REINSURE_FLAG IS NULL)  	             
  	              AND PA.ACKNOWLEDGE_DATE IS NULL
				  AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
		<if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if><!-- 续保转保 -->  	 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND A.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY A.INPUT_DATE) B) C WHERE C.RN BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>
	
	<select id="NB_queryAllReceiptData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN,A.* FROM (
		  		SELECT ROWNUM RN,
		  			   CM.POLICY_CODE,
		               CM.APPLY_CODE,
		               (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
		               (SELECT TO_CHAR(WM_CONCAT(P.PRODUCT_CODE)) FROM APP___NB__DBUSER.T_NB_CONTRACT_BUSI_PROD P WHERE P.MASTER_BUSI_ITEM_ID IS NULL AND P.POLICY_CODE = CM.POLICY_CODE GROUP BY P.POLICY_CODE) AS BUSINESS_PRODUCT_CODE,
		               CM.ORGAN_CODE,               
		               (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM 
		               APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
		               (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
		               (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
		               CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
		               (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
		               (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
		               (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
		               TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,
		               TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		               TO_CHAR((SELECT MAX(PA.ACKNOWLEDGE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
		               TO_CHAR((SELECT MAX(PA.BRANCH_RECEIVE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS INPUT_DATE,
		               TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
					  (case when CM.media_type = '1' then 
				           (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY)
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source = '0' and PA.OPERATOR_ID is not null then 
				                 (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.OPERATOR_ID) 
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source != '0' then 
				                (select
				                    CA.Agent_Code || '-' || CA.AGENT_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                from APP___NB__DBUSER.T_UDMP_ORG_REL O
				                WHERE O.ORGAN_CODE = CA.Agent_Organ_Code)
				         else 
				         	(CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) 
						        THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME 
						             FROM APP___NB__DBUSER.T_UDMP_USER U 
						              INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE      
						              WHERE U.USER_ID = PA.UPDATE_BY) 
						         ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME 
						              FROM APP___NB__DBUSER.T_UDMP_USER U 
						              INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE 
						              WHERE U.USER_ID = PA.OPERATOR_ID) 
						          END) 
				         end) as OPERATOR,             
		               (CASE WHEN CM.MEDIA_TYPE = '1' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA 
		               WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE 
		               FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NOT NULL AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
		               DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
		               CA.SALES_ORGAN_NAME_R,
		               CA.SALES_ORGAN_NAME_D,
		               CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,
		               (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
		               (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		          FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
		         INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
		                      FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
            LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
		           WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
		           AND PA.ACKNOWLEDGE_DATE IS NULL
      			AND  (CM.POLICY_REINSURE_FLAG !='3' OR CM.POLICY_REINSURE_FLAG IS NULL)		           
		           AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<!-- 签单起止期 -->
		<if test=" sign_start_date != null and sign_start_date !='' and sign_end_date != null and sign_end_date !='' ">
		<![CDATA[ AND CM.ISSUE_DATE >= TO_DATE(#{sign_start_date} ||'00:00:00','YYYY-MM-DD HH24:MI:SS') 
				AND CM.ISSUE_DATE <= TO_DATE(#{sign_end_date} ||'23:59:59','YYYY-MM-DD HH24:MI:SS') ]]></if>
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
		<!-- 续保转保 -->        
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if>	 		 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<!-- 回执录入起止期 -->
		<!-- <if test=" start_date != null and start_date !='' and policy_type != '0'.toString() ">
		<![CDATA[ AND PA.BRANCH_RECEIVE_DATE >= to_date(#{start_date} || '00:00:00','YYYY-MM-DD HH24:MI:SS') AND PA.BRANCH_RECEIVE_DATE <= TO_DATE(#{end_date} ||'23:59:59','YYYY-MM-DD HH24:MI:SS') ]]></if> -->
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if>
	    <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if>
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND A.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY A.INPUT_DATE ]]>
	</select>
	
		<!-- 查询总条数深圳医保卡承保清单 -->
	<select id="NB_queryMedicalDataforCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[	
				SELECT COUNT(1) FROM (SELECT DISTINCT M.POLICY_CODE AS POLICY_CODE,
                                M.APPLY_CODE AS APPLY_CODE,
                                NCM.DRAINAGE_AGENT_CODE AS DRAINAGE_CODE,/*引流业务员代码*/
                                CASE
                                  WHEN NCM.DRAINAGE_AGENT_CODE IS NULL THEN
                                   '否'
                                  ELSE
                                   '是'
                                END AS DRAINAGE,/*是否引流业务*/
                                (SELECT TA.AGENT_NAME
                                   FROM DEV_NB.T_AGENT TA
                                  WHERE TA.AGENT_CODE = NCM.DRAINAGE_AGENT_CODE) AS DRAINAGE_NAME,/*引流业务员姓名*/
                                M.ORGAN_CODE AS ORGAN_CODE,
                                TO_CHAR(M.ISSUE_DATE, 'yyyy-mm-dd') ISSUE_DATE,
                                TO_CHAR(M.APPLY_DATE, 'yyyy-mm-dd') APPLY_DATE,
                                TO_CHAR(M.VALIDATE_DATE, 'yyyy-mm-dd') EFFECTIVE_DATE,
                                P.TOTAL_PREM_AF AS PREMIUM,
                                P.AMOUNT AS INSURED_AMOUNT,                                                               
                                CASE A.FEE_STATUS
                                  WHEN '16' THEN
                                   TO_CHAR(A.FINISH_TIME, 'YYYY-MM-DD')
                                  ELSE
                                   ''
                                END AS ARRIVAL_DATE,
                                CASE A.PAY_MODE
                                  WHEN '18' THEN
                                   '社保缴纳'
                                  WHEN '40' THEN
                                   '实时转账'
                                  ELSE
                                   ''
                                END AS PAY_MODE_NAME,
                                CASE A.PAY_MODE
                                  WHEN '18' THEN
                                   TO_CHAR(TPM.FINISH_TIME, 'yyyy-mm-dd')
                                  ELSE
                                   ''
                                END AS DEDUCT_MONEY,
                                CASE A.PAY_MODE
                                  WHEN '40' THEN
                                   A.BANK_CODE
                                  ELSE
                                   ''
                                END AS BANK_CODE,
                                A.BUSI_PROD_CODE AS INSURANCE_CODE,
                                A.BUSI_PROD_NAME AS INSURANCE_NAME,
                                C.AGENT_CODE AS HANDLER_CODE,
                                C.AGENT_NAME AS HANDLER_NAME,
                                M.AGENCY_CODE AS INTERMEDIARY_NGENT_CODE,
                                '' AS INTERMEDIARY_NGENT_NAME,
                                A.CIP_BRANCH_BANK_CODE AS BRANCH_CODE,
                                '' AS BRANCH_NAME,
                                (SELECT L.STATUS_NAME
                                   FROM DEV_NB.T_LIABILITY_STATUS L
                                  WHERE L.STATUS_CODE = M.LIABILITY_STATE) AS LIABILITY_STATE,
                                '心圆福' AS PLATFORM_FLAG
                  FROM DEV_PAS.T_CONTRACT_MASTER M
                 INNER JOIN DEV_NB.T_NB_MEDICAL_CARD NCM
                    ON M.APPLY_CODE = NCM.APPLY_CODE
                  LEFT JOIN DEV_NB.T_PREM_ARAP A
                    ON A.POLICY_CODE = M.POLICY_CODE
                  LEFT JOIN DEV_NB.T_NB_CONTRACT_PRODUCT P
                    ON M.APPLY_CODE = P.APPLY_CODE
                  LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT C
                    ON M.APPLY_CODE = C.APPLY_CODE
                  LEFT JOIN DEV_CAP.T_PREM_ARAP TPM
                    ON M.APPLY_CODE = TPM.APPLY_CODE
                  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TNCM
					ON TNCM.APPLY_CODE=M.APPLY_CODE
                 WHERE 1 = 1
                   AND M.SUBMIT_CHANNEL = 15
                   AND TPM.FEE_STATUS='16'
                   AND TNCM.PROPOSAL_STATUS !='55' /**不是生效前退保的保单*/
                   AND M.SUBMIT_CHANNEL = 15
                   AND M.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
		]]>
			<include refid="NB_queryAllMedical"></include>)
			
	</select>
	<!-- 深圳医保卡承保业务分页查询-->
	<select id="NB_queryMedicalDataforPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	SELECT AL.POLICY_CODE,
       AL.APPLY_CODE,
       AL.ORGAN_CODE,
       AL.ISSUE_DATE,
       AL.APPLY_DATE,
       AL.EFFECTIVE_DATE,
       AL.PREMIUM,
       AL.INSURED_AMOUNT,   
       AL.ARRIVAL_DATE,
       AL.PAY_MODE_NAME,
       AL.DEDUCT_MONEY,
       AL.BANK_CODE,
       AL.INSURANCE_CODE,
       AL.INSURANCE_NAME,
       AL.HANDLER_CODE,
       AL.HANDLER_NAME,
       AL.INTERMEDIARY_NGENT_CODE,
       AL.INTERMEDIARY_NGENT_NAME,
       AL.BRANCH_CODE,
       AL.BRANCH_NAME,
       AL.LIABILITY_STATE,
       AL.PLATFORM_FLAG,
       AL.DRAINAGE,
       AL.DRAINAGE_CODE,
       AL.DRAINAGE_NAME
  FROM (SELECT ROWNUM RN,
               T.POLICY_CODE,
               T.APPLY_CODE,
               T.ORGAN_CODE,
               T.ISSUE_DATE,
               T.APPLY_DATE,
               T.EFFECTIVE_DATE,
               T.PREMIUM,
               T.INSURED_AMOUNT,      
               T.ARRIVAL_DATE,
               T.PAY_MODE_NAME,
               T.DEDUCT_MONEY,
               T.BANK_CODE,
               T.INSURANCE_CODE,
               T.INSURANCE_NAME,
               T.HANDLER_CODE,
               T.HANDLER_NAME,
               T.INTERMEDIARY_NGENT_CODE,
               T.INTERMEDIARY_NGENT_NAME,
               T.BRANCH_CODE,
               T.BRANCH_NAME,
               T.LIABILITY_STATE,
               T.PLATFORM_FLAG,
               T.DRAINAGE,
               T.DRAINAGE_CODE,
               T.DRAINAGE_NAME
          FROM (SELECT DISTINCT M.POLICY_CODE AS POLICY_CODE,
                                M.APPLY_CODE AS APPLY_CODE,
                                NCM.DRAINAGE_AGENT_CODE AS DRAINAGE_CODE,/*引流业务员代码*/
                                CASE
                                  WHEN NCM.DRAINAGE_AGENT_CODE IS NULL THEN
                                   '否'
                                  ELSE
                                   '是'
                                END AS DRAINAGE,/*是否引流业务*/
                                (SELECT TA.AGENT_NAME
                                   FROM DEV_NB.T_AGENT TA
                                  WHERE TA.AGENT_CODE = NCM.DRAINAGE_AGENT_CODE) AS DRAINAGE_NAME,/*引流业务员姓名*/
                                M.ORGAN_CODE AS ORGAN_CODE,
                                TO_CHAR(M.ISSUE_DATE, 'yyyy-mm-dd') ISSUE_DATE,
                                TO_CHAR(M.APPLY_DATE, 'yyyy-mm-dd') APPLY_DATE,
                                TO_CHAR(M.VALIDATE_DATE, 'yyyy-mm-dd') EFFECTIVE_DATE,
                                P.TOTAL_PREM_AF AS PREMIUM,
                                P.AMOUNT AS INSURED_AMOUNT,                                                               
                                CASE A.FEE_STATUS
                                  WHEN '16' THEN
                                   TO_CHAR(A.FINISH_TIME, 'YYYY-MM-DD')
                                  ELSE
                                   ''
                                END AS ARRIVAL_DATE,
                                CASE A.PAY_MODE
                                  WHEN '18' THEN
                                   '社保缴纳'
                                  WHEN '40' THEN
                                   '实时转账'
                                  ELSE
                                   ''
                                END AS PAY_MODE_NAME,
                                CASE A.PAY_MODE
                                  WHEN '18' THEN
                                   TO_CHAR(TPM.FINISH_TIME, 'yyyy-mm-dd')
                                  ELSE
                                   ''
                                END AS DEDUCT_MONEY,
                                CASE A.PAY_MODE
                                  WHEN '40' THEN
                                   A.BANK_CODE
                                  ELSE
                                   ''
                                END AS BANK_CODE,
                                A.BUSI_PROD_CODE AS INSURANCE_CODE,
                                A.BUSI_PROD_NAME AS INSURANCE_NAME,
                                C.AGENT_CODE AS HANDLER_CODE,
                                C.AGENT_NAME AS HANDLER_NAME,
                                M.AGENCY_CODE AS INTERMEDIARY_NGENT_CODE,
                                '' AS INTERMEDIARY_NGENT_NAME,
                                A.CIP_BRANCH_BANK_CODE AS BRANCH_CODE,
                                '' AS BRANCH_NAME,
                                (SELECT L.STATUS_NAME
                                   FROM DEV_NB.T_LIABILITY_STATUS L
                                  WHERE L.STATUS_CODE = M.LIABILITY_STATE) AS LIABILITY_STATE,
                                '心圆福' AS PLATFORM_FLAG
                  FROM DEV_PAS.T_CONTRACT_MASTER M
                 INNER JOIN DEV_NB.T_NB_MEDICAL_CARD NCM
                    ON M.APPLY_CODE = NCM.APPLY_CODE
                  LEFT JOIN DEV_NB.T_PREM_ARAP A
                    ON A.POLICY_CODE = M.POLICY_CODE
                  LEFT JOIN DEV_NB.T_NB_CONTRACT_PRODUCT P
                    ON M.APPLY_CODE = P.APPLY_CODE
                  LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT C
                    ON M.APPLY_CODE = C.APPLY_CODE
                  LEFT JOIN DEV_CAP.T_PREM_ARAP TPM
                    ON M.APPLY_CODE = TPM.APPLY_CODE
                 WHERE 1 = 1
                   AND M.SUBMIT_CHANNEL = 15
                   /**and M.END_CAUSE !='82' 生效前退保的保单-报单表*/
                   AND TPM.FEE_STATUS='16'
							    AND M.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') 
							    ]]>
							    <include refid="NB_queryAllMedical"></include>
							    ORDER BY M.POLICY_CODE DESC
							    ) T ) AL
							    
		<![CDATA[  WHERE 1=1 AND AL.RN <= #{LESS_NUM} AND AL.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 导出深圳医保卡承保清单数据  -->
	<select id="NB_queryAllMedicalData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	
		SELECT AL.POLICY_CODE,
       AL.APPLY_CODE,
       AL.ORGAN_CODE,
       AL.ISSUE_DATE,
       AL.APPLY_DATE,
       AL.EFFECTIVE_DATE,
       AL.PREMIUM,
       AL.INSURED_AMOUNT,   
       AL.ARRIVAL_DATE,
       AL.PAY_MODE_NAME,
       AL.DEDUCT_MONEY,
       AL.BANK_CODE,
       AL.INSURANCE_CODE,
       AL.INSURANCE_NAME,
       AL.HANDLER_CODE,
       AL.HANDLER_NAME,
       AL.INTERMEDIARY_NGENT_CODE,
       AL.INTERMEDIARY_NGENT_NAME,
       AL.BRANCH_CODE,
       AL.BRANCH_NAME,
       AL.LIABILITY_STATE,
       AL.PLATFORM_FLAG,
       AL.DRAINAGE,
       AL.DRAINAGE_CODE,
       AL.DRAINAGE_NAME
  FROM (SELECT ROWNUM RN,
               T.POLICY_CODE,
               T.APPLY_CODE,
               T.ORGAN_CODE,
               T.ISSUE_DATE,
               T.APPLY_DATE,
               T.EFFECTIVE_DATE,
               T.PREMIUM,
               T.INSURED_AMOUNT,      
               T.ARRIVAL_DATE,
               T.PAY_MODE_NAME,
               T.DEDUCT_MONEY,
               T.BANK_CODE,
               T.INSURANCE_CODE,
               T.INSURANCE_NAME,
               T.HANDLER_CODE,
               T.HANDLER_NAME,
               T.INTERMEDIARY_NGENT_CODE,
               T.INTERMEDIARY_NGENT_NAME,
               T.BRANCH_CODE,
               T.BRANCH_NAME,
               T.LIABILITY_STATE,
               T.PLATFORM_FLAG,
               T.DRAINAGE,
               T.DRAINAGE_CODE,
               T.DRAINAGE_NAME
          FROM (SELECT DISTINCT M.POLICY_CODE AS POLICY_CODE,
                                M.APPLY_CODE AS APPLY_CODE,
                                NCM.DRAINAGE_AGENT_CODE AS DRAINAGE_CODE,/*引流业务员代码*/
                                CASE
                                  WHEN NCM.DRAINAGE_AGENT_CODE IS NULL THEN
                                   '否'
                                  ELSE
                                   '是'
                                END AS DRAINAGE,/*是否引流业务*/
                                (SELECT TA.AGENT_NAME
                                   FROM DEV_NB.T_AGENT TA
                                  WHERE TA.AGENT_CODE = NCM.DRAINAGE_AGENT_CODE) AS DRAINAGE_NAME,/*引流业务员姓名*/
                                M.ORGAN_CODE AS ORGAN_CODE,
                                TO_CHAR(M.ISSUE_DATE, 'yyyy-mm-dd') ISSUE_DATE,
                                TO_CHAR(M.APPLY_DATE, 'yyyy-mm-dd') APPLY_DATE,
                                TO_CHAR(M.VALIDATE_DATE, 'yyyy-mm-dd') EFFECTIVE_DATE,
                                P.TOTAL_PREM_AF AS PREMIUM,
                                P.AMOUNT AS INSURED_AMOUNT,                                                               
                                CASE A.FEE_STATUS
                                  WHEN '16' THEN
                                   TO_CHAR(A.FINISH_TIME, 'YYYY-MM-DD')
                                  ELSE
                                   ''
                                END AS ARRIVAL_DATE,
                                CASE A.PAY_MODE
                                 WHEN '18' THEN
                                   '社保缴纳'
                                  WHEN '40' THEN
                                   '实时转账'
                                  ELSE
                                   ''
                                END AS PAY_MODE_NAME,
                                CASE A.PAY_MODE
                                  WHEN '18' THEN
                                   TO_CHAR(TPM.FINISH_TIME, 'yyyy-mm-dd')
                                  ELSE
                                   ''
                                END AS DEDUCT_MONEY,
                                CASE A.PAY_MODE
                                  WHEN '40' THEN
                                   A.BANK_CODE
                                  ELSE
                                   ''
                                END AS BANK_CODE,
                                A.BUSI_PROD_CODE AS INSURANCE_CODE,
                                A.BUSI_PROD_NAME AS INSURANCE_NAME,
                                C.AGENT_CODE AS HANDLER_CODE,
                                C.AGENT_NAME AS HANDLER_NAME,
                                M.AGENCY_CODE AS INTERMEDIARY_NGENT_CODE,
                                '' AS INTERMEDIARY_NGENT_NAME,
                                A.CIP_BRANCH_BANK_CODE AS BRANCH_CODE,
                                '' AS BRANCH_NAME,
                                (SELECT L.STATUS_NAME
                                   FROM DEV_NB.T_LIABILITY_STATUS L
                                  WHERE L.STATUS_CODE = M.LIABILITY_STATE) AS LIABILITY_STATE,
                                '心圆福' AS PLATFORM_FLAG
                  FROM DEV_PAS.T_CONTRACT_MASTER M
                 INNER JOIN DEV_NB.T_NB_MEDICAL_CARD NCM
                    ON M.APPLY_CODE = NCM.APPLY_CODE
                  LEFT JOIN DEV_NB.T_PREM_ARAP A
                    ON A.POLICY_CODE = M.POLICY_CODE
                  LEFT JOIN DEV_NB.T_NB_CONTRACT_PRODUCT P
                    ON M.APPLY_CODE = P.APPLY_CODE
                  LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT C
                    ON M.APPLY_CODE = C.APPLY_CODE
                  LEFT JOIN DEV_CAP.T_PREM_ARAP TPM
                    ON M.APPLY_CODE = TPM.APPLY_CODE
                  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TNCM
					ON TNCM.APPLY_CODE=M.APPLY_CODE
                 WHERE 1 = 1
                   AND TNCM.PROPOSAL_STATUS !='55' /**不是生效前退保的保单*/
                   AND M.SUBMIT_CHANNEL = 15
                   AND TPM.FEE_STATUS='16'
							   AND M.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
							    <include refid="NB_queryAllMedical"></include>
							    ORDER BY M.POLICY_CODE DESC
							    ) T ) AL
							    
	</select>
	
	<sql id="NB_queryAllMedical">
    	<if test=" sign_start_date  != null and sign_start_date !='' "><!-- 签单起期 -->
	    	<![CDATA[  AND TRUNC(M.ISSUE_DATE) >= TO_DATE(#{sign_start_date},'yyyy-mm-dd') ]]>   
	  	</if>  
	    <if test=" sign_end_date  != null and sign_end_date !='' "><!-- 签单止期 -->
	    	<![CDATA[  AND TRUNC(M.ISSUE_DATE) <= TO_DATE(#{sign_end_date},'yyyy-mm-dd') + 1 ]]>   
	  	</if>
	    <if test=" business_channel != null and business_channel !='' "><!-- 业务员渠道-->
	    	<![CDATA[  AND M.CHANNEL_TYPE  IN (${business_channel}) ]]>   
	  	</if>
	  
	 </sql>
	 
	 <sql id="NB_queryAllReceiptforNewPageQueryType">
	 	<if test=" query_type != null and query_type !='' ">
		 	<!-- 签收方式为0-纸质回执时，签收来源默认为0-核心系统;其他情况则根据签收平台选择来判断(#rm118936) -->
			<choose>
	            <when test="sign_type != null and sign_type != '' and sign_typ=='0'">
	                AND PA.SIGN_SOURCE ='0'
	            </when>
	            <when test="sign_source != null and sign_source != '' ">
	                AND PA.SIGN_SOURCE IN (${sign_source}) 
	            </when>
	        </choose>
			<!-- 投保日期 -->
			<if test=' query_type =="1" '>
				<![CDATA[ AND CM.APPLY_DATE BETWEEN TO_DATE(#{start_date} ||'00:00:00','YYYY-MM-DD HH24:MI:SS') AND TO_DATE(#{end_date} ||'23:59:59','YYYY-MM-DD HH24:MI:SS') ]]>
			</if>
			<!-- 签单日期 -->
			<if test=' query_type =="2" '>
				<![CDATA[ AND CM.ISSUE_DATE BETWEEN TO_DATE(#{start_date} ||'00:00:00','YYYY-MM-DD HH24:MI:SS') AND TO_DATE(#{end_date} ||'23:59:59','YYYY-MM-DD HH24:MI:SS') ]]>
			</if>
			<!-- 回执签收日期 -->
			<if test=' query_type =="3" '>
				<![CDATA[ AND PA.ACKNOWLEDGE_DATE BETWEEN TO_DATE(#{start_date} ||'00:00:00','YYYY-MM-DD HH24:MI:SS') AND TO_DATE(#{end_date} ||'23:59:59','YYYY-MM-DD HH24:MI:SS') ]]>
			</if>
			<!-- 回执录入日期 -->
			<if test=' query_type =="4" '>
				<![CDATA[ AND PA.BRANCH_RECEIVE_DATE >= to_date(#{start_date} || '00:00:00','YYYY-MM-DD HH24:MI:SS') AND PA.BRANCH_RECEIVE_DATE <= TO_DATE(#{end_date} ||'23:59:59','YYYY-MM-DD HH24:MI:SS') ]]>
			</if>
			<!-- 回执扫描日期 -->
			<if test=' query_type =="5" '>
			<!-- UN004回扫日期？ -->
				<![CDATA[ AND IMSCAN.SCAN_TIME  >= to_date(#{start_date} || '00:00:00','YYYY-MM-DD HH24:MI:SS') AND IMSCAN.SCAN_TIME <= TO_DATE(#{end_date} ||'23:59:59','YYYY-MM-DD HH24:MI:SS') ]]>
			</if>
		</if>
	 </sql>
	 
	<select id="NB_queryAllReceiptforNewPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM ROM, C.* FROM (SELECT ROWNUM RN, B.* FROM (SELECT AA.* FROM (SELECT A.* FROM (]]>   
		<![CDATA[	   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				      (SELECT TO_CHAR(WM_CONCAT(P.PRODUCT_CODE)) FROM APP___NB__DBUSER.T_NB_CONTRACT_BUSI_PROD P WHERE P.MASTER_BUSI_ITEM_ID IS NULL AND P.POLICY_CODE = CM.POLICY_CODE GROUP BY P.POLICY_CODE) AS BUSINESS_PRODUCT_CODE,
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE = cm.POLICY_CODE AND pp.PRINT_TYPE = '1' AND pp.PRINT_STATUS = '3') > 0 
					  	THEN
				          TO_CHAR((SELECT max(pp.BPO_PRINT_DATE)
				                    FROM DEV_NB.T_POLICY_PRINT PP
				                   WHERE pp.POLICY_CODE = cm.POLICY_CODE),
				                  'YYYY-MM-DD')
				        ELSE ''
				      END) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      NULL AS RECEIPT_SIGN_DATE,
				      NULL AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(CASE WHEN (NULL = 0 OR NULL IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) END) AS OPERATOR,*/
              		  NULL AS OPERATOR,
				      '' AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		 ,(case  
		 when (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = cm.apply_code) > 0 /*电子保单申请过纸质保单*/
		 then (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD LEFT JOIN DEV_NB.T_POLICY_PRINT_SOURCE PPS ON PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE WHERE PPD.APPLY_CODE = cm.APPLY_CODE AND ROWNUM <= 1)
		 when to_number(cm.subinput_type) <> 15 and cm.submit_channel = '1' and tt.media_type in ('0','2') /*保全做过变更并且是电子保单非银保通电子渠道出单*/
		 then  (SELECT tst.type_desc FROM dev_nb.t_input_type tst where  cm.input_type = tst.type_code)
		 when to_number(cm.subinput_type) <> 15 and cm.submit_channel = '1' and cm.media_type = '0' and mst.media_type = '1' and tt.media_type = '1' /*判断完保全再判断契约*/
		 then  ''
		 when tt.media_type is not null and (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = cm.apply_code) = 0 and cm.media_type <> tt.media_type
		  then ''
		 ELSE ''
		  end )as PRINT_SOURCE_NAME/*申请渠道#64327*/
	   
	   ,(case when tt.media_type is not null 
		   then (SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  tt.media_type=TMT.MEDIA_TYPE_CODE)
		   else (SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  mst.media_type=TMT.MEDIA_TYPE_CODE)
		   end
		) as MEDIA_TYPE_DESC/*保单类型#64327*/,PA.SIGN_SOURCE /*签收来源*/,
		(CASE
         WHEN CM.JOINTLY_INSURED_TYPE = '1' THEN
          (SELECT LISTAGG(APPLY_CODE, '/') WITHIN GROUP(ORDER BY APPLY_CODE) AS A1
             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
            WHERE JIP.MAIN_APPLY_CODE = CM.APPLY_CODE
            AND JIP.APPLY_CODE  in (SELECT A3.APPLY_CODE  FROM DEV_NB.T_NB_CONTRACT_MASTER A3 WHERE A3.LIABILITY_STATE  not in ('2','3','4') AND A3.JOINTLY_INSURED_TYPE = '2')
            AND JIP.MAIN_APPLY_CODE not in
				(SELECT MAS1.APPLY_CODE
				FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
				LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
					ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
				WHERE MAS1.Jointly_Insured_Type = '1'
					AND MAS1.Multi_Mainrisk_Flag = '1'
					AND PRO1.PRODUCT_CODE = '00822000'
					AND PRO1.DECISION_CODE in ('40', '50')))
         WHEN CM.JOINTLY_INSURED_TYPE = '2' THEN
          (SELECT JIP.MAIN_APPLY_CODE AS A2
             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
            WHERE JIP.APPLY_CODE = CM.APPLY_CODE
            AND JIP.MAIN_APPLY_CODE not in
				(SELECT MAS1.APPLY_CODE
				FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
				LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
					ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
				WHERE MAS1.Jointly_Insured_Type = '1'
					AND MAS1.Multi_Mainrisk_Flag = '1'
					AND PRO1.PRODUCT_CODE = '00822000'
					AND PRO1.DECISION_CODE in ('40', '50')))
         ELSE
          ''
       END) AS JOINTLY_INSURED_POLICY
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
            LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
				LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST 
				ON MST.APPLY_CODE = CM.APPLY_CODE
				 LEFT JOIN 
				 (select log_id,policy_code,media_type  from 
                    (select    mstg.log_id,mstg.policy_code,mstg.media_type 
                    ,row_number() over(partition by mstg.policy_code order by mstg.log_id  asc ) rn
                    from dev_pas.t_contract_master_log mstg)
                    where rn = 1) tt ON tt.policy_code = CM.policy_code
                    LEFT JOIN (SELECT IMG.SCAN_TIME,IMG.BUSS_CODE FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG
		         WHERE IMG.BILLCARD_CODE = 'UN004'
		         )IMSCAN ON IMSCAN.BUSS_CODE = CM.APPLY_CODE
				  WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
  	              AND PA.ACKNOWLEDGE_DATE IS NULL
				  AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if><!-- 续保转保 --> 	 		 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
       	
		<![CDATA[ UNION ALL ]]>
		<![CDATA[
		 SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				      (SELECT TO_CHAR(WM_CONCAT(P.PRODUCT_CODE)) FROM APP___NB__DBUSER.T_NB_CONTRACT_BUSI_PROD P WHERE P.MASTER_BUSI_ITEM_ID IS NULL AND P.POLICY_CODE = CM.POLICY_CODE GROUP BY P.POLICY_CODE) AS BUSINESS_PRODUCT_CODE,
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
              		  TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
					  (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE = cm.POLICY_CODE AND pp.PRINT_TYPE = '1' AND pp.PRINT_STATUS = '3') > 0 
					  	THEN
				          TO_CHAR((SELECT max(pp.BPO_PRINT_DATE)
				                    FROM DEV_NB.T_POLICY_PRINT PP
				                   WHERE pp.POLICY_CODE = cm.POLICY_CODE),
				                  'YYYY-MM-DD')
				        ELSE ''
				      END) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      /*TO_CHAR((SELECT MAX(PA.ACKNOWLEDGE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,*/
				      TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
				      /*TO_CHAR((SELECT MAX(PA.BRANCH_RECEIVE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS INPUT_DATE,*/
				      (CASE WHEN TO_DATE(TO_CHAR(PA.BRANCH_RECEIVE_DATE,'HH24:MI:SS'),'HH24:MI:SS')>TO_DATE('00:00:00', 'HH24:MI:SS')
                  		THEN TO_CHAR(PA.BRANCH_RECEIVE_DATE, 'YYYY-MM-DD HH24:MI:SS')
                    	ELSE TO_CHAR(PA.BRANCH_RECEIVE_DATE, 'YYYY-MM-DD') END )BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(SELECT (CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.UPDATE_BY) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.OPERATOR_ID) END) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) AS OPERATOR,*/
					  (case when CM.media_type = '1' then 
				           (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY)
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source = '0' and PA.OPERATOR_ID is not null then 
				                 (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.OPERATOR_ID) 
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source != '0' then 
				                (select
				                    CA.Agent_Code || '-' || CA.AGENT_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                from APP___NB__DBUSER.T_UDMP_ORG_REL O
				                WHERE O.ORGAN_CODE = CA.Agent_Organ_Code)
				         else 
				         	(CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) 
						        THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME 
						             FROM APP___NB__DBUSER.T_UDMP_USER U 
						              INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE      
						              WHERE U.USER_ID = PA.UPDATE_BY) 
						         ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME 
						              FROM APP___NB__DBUSER.T_UDMP_USER U 
						              INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE 
						              WHERE U.USER_ID = PA.OPERATOR_ID) 
						          END) 
				         end) as OPERATOR,  

				      /*(CASE WHEN CM.MEDIA_TYPE = '1' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NOT NULL AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, \*40588 LIUPENGIT1*\*/
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		 ,(case  
		 when (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = cm.apply_code) > 0 /*电子保单申请过纸质保单*/
		 then (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD LEFT JOIN DEV_NB.T_POLICY_PRINT_SOURCE PPS ON PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE WHERE PPD.APPLY_CODE = cm.APPLY_CODE AND ROWNUM <= 1)
		 when to_number(cm.subinput_type) <> 15 and cm.submit_channel = '1' and tt.media_type in ('0','2') /*保全做过变更并且是电子保单非银保通电子渠道出单*/
		 then  (SELECT tst.type_desc FROM dev_nb.t_input_type tst where  cm.input_type = tst.type_code)
		 when to_number(cm.subinput_type) <> 15 and cm.submit_channel = '1' and cm.media_type = '0' and mst.media_type = '1' and tt.media_type = '1' /*判断完保全再判断契约*/
		 then  ''
		 when tt.media_type is not null and (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = cm.apply_code) = 0 and cm.media_type <> tt.media_type
		  then ''
		 ELSE ''
		  end )as PRINT_SOURCE_NAME/*申请渠道#64327*/
	   
	   ,(case when tt.media_type is not null 
		   then (SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  tt.media_type=TMT.MEDIA_TYPE_CODE)
		   else (SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  mst.media_type=TMT.MEDIA_TYPE_CODE)
		   end
		) as MEDIA_TYPE_DESC/*保单类型#64327*/,PA.SIGN_SOURCE /*签收来源*/,
		(CASE
         WHEN CM.JOINTLY_INSURED_TYPE = '1' THEN
          (SELECT LISTAGG(APPLY_CODE, '/') WITHIN GROUP(ORDER BY APPLY_CODE) AS A1
             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
            WHERE JIP.MAIN_APPLY_CODE = CM.APPLY_CODE
            AND JIP.APPLY_CODE  in (SELECT A3.APPLY_CODE  FROM DEV_NB.T_NB_CONTRACT_MASTER A3 WHERE A3.LIABILITY_STATE  not in ('2','3','4') AND A3.JOINTLY_INSURED_TYPE = '2')
            AND JIP.MAIN_APPLY_CODE not in
				(SELECT MAS1.APPLY_CODE
				FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
				LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
					ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
				WHERE MAS1.Jointly_Insured_Type = '1'
					AND MAS1.Multi_Mainrisk_Flag = '1'
					AND PRO1.PRODUCT_CODE = '00822000'
					AND PRO1.DECISION_CODE in ('40', '50')))
         WHEN CM.JOINTLY_INSURED_TYPE = '2' THEN
          (SELECT JIP.MAIN_APPLY_CODE AS A2
             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
            WHERE JIP.APPLY_CODE = CM.APPLY_CODE
            AND JIP.MAIN_APPLY_CODE not in
				(SELECT MAS1.APPLY_CODE
				FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
				LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
					ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
				WHERE MAS1.Jointly_Insured_Type = '1'
					AND MAS1.Multi_Mainrisk_Flag = '1'
					AND PRO1.PRODUCT_CODE = '00822000'
					AND PRO1.DECISION_CODE in ('40', '50')))
         ELSE
          ''
       END) AS JOINTLY_INSURED_POLICY
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
				LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST 
				ON MST.APPLY_CODE = CM.APPLY_CODE
				 LEFT JOIN 
				 (select log_id,policy_code,media_type  from 
	                   (select    mstg.log_id,mstg.policy_code,mstg.media_type 
	                   ,row_number() over(partition by mstg.policy_code order by mstg.log_id  asc ) rn
	                   from dev_pas.t_contract_master_log mstg)
	                   where rn = 1) tt ON tt.policy_code = CM.policy_code
	            LEFT JOIN (SELECT IMG.SCAN_TIME,IMG.BUSS_CODE FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG
		         WHERE IMG.BILLCARD_CODE = 'UN004'
		         )IMSCAN ON IMSCAN.BUSS_CODE = CM.APPLY_CODE
				WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
				  AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
	 	<if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if><!-- 续保转保 --> 	 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ ) A WHERE 1 = 1 )AA WHERE 1=1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND AA.SIGN_TYPE IN (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND AA.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (AA.QT_STATUS != 7 OR AA.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND AA.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND AA.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY AA.INPUT_DATE) B) C WHERE C.RN BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>
	
	<select id="NB_queryAllReceiptforNewCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM (SELECT B.* FROM (SELECT a.* FROM (
				SELECT CM.POLICY_CODE,CM.APPLY_CODE,CM.ORGAN_CODE,
		        TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
        		'' AS SIGN_TYPE,
        		(SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
         		FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
        		INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
                     FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
           		LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
        		LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST ON MST.APPLY_CODE = CM.APPLY_CODE
         		LEFT JOIN 
		         (SELECT LOG_ID,POLICY_CODE,MEDIA_TYPE  FROM 
		                    (SELECT    MSTG.LOG_ID,MSTG.POLICY_CODE,MSTG.MEDIA_TYPE 
		                    ,ROW_NUMBER() OVER(PARTITION BY MSTG.POLICY_CODE ORDER BY MSTG.LOG_ID  ASC ) RN
		                    FROM DEV_PAS.T_CONTRACT_MASTER_LOG MSTG)
		                    WHERE RN = 1) TT ON TT.POLICY_CODE = CM.POLICY_CODE
                LEFT JOIN (SELECT IMG.SCAN_TIME,IMG.BUSS_CODE FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG
		         WHERE IMG.BILLCARD_CODE = 'UN004'
		         )IMSCAN ON IMSCAN.BUSS_CODE = CM.APPLY_CODE
      			WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33') 
      			AND  (CM.POLICY_REINSURE_FLAG !='3' OR CM.POLICY_REINSURE_FLAG IS NULL)      				
      				AND PA.ACKNOWLEDGE_DATE IS NULL
		           AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
	 	<!-- 续保转保 -->        
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if>	 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ UNION ALL 
			SELECT CM.POLICY_CODE,CM.APPLY_CODE,CM.ORGAN_CODE,
	        TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
        	(CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
        	(SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
         	FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
        		INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
                                  AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
                          (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
                          (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
                                  AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
                     FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
		        INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
		        LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST  ON MST.APPLY_CODE = CM.APPLY_CODE
		        LEFT JOIN  (SELECT LOG_ID,POLICY_CODE,MEDIA_TYPE  FROM 
		                     (SELECT    MSTG.LOG_ID,MSTG.POLICY_CODE,MSTG.MEDIA_TYPE 
		                     ,ROW_NUMBER() OVER(PARTITION BY MSTG.POLICY_CODE ORDER BY MSTG.LOG_ID  ASC ) RN
		                     FROM DEV_PAS.T_CONTRACT_MASTER_LOG MSTG)
		                     WHERE RN = 1) TT ON TT.POLICY_CODE = CM.POLICY_CODE
		         LEFT JOIN (SELECT IMG.SCAN_TIME,IMG.BUSS_CODE FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG
		         WHERE IMG.BILLCARD_CODE = 'UN004'
		         )IMSCAN ON IMSCAN.BUSS_CODE = CM.APPLY_CODE
		        WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
      			AND  (CM.POLICY_REINSURE_FLAG !='3' OR CM.POLICY_REINSURE_FLAG IS NULL)						
						   AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<!-- 电子渠道 -->
		<if test=" subinput_type != null and subinput_type !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${subinput_type} ]]></if>
	 	<!-- 续保转保 -->        
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if>	 		 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>		
		<![CDATA[ ) A WHERE 1 = 1 ) B WHERE 1=1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND B.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND B.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (B.QT_STATUS != 7 OR B.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND B.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND B.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ) C ]]>
	</select>
	
  	
	<!-- 撤保清单离线-job1 -->
	<select id="NB_queryAllReceiptforNewPagelx1" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ SELECT ROWNUM ROM, C.* FROM (SELECT ROWNUM RN, B.* FROM (SELECT A.* FROM (
			   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				      PCC.BUSINESS_PRODUCT_CODES,
				      (
		               CASE (SELECT TO_CHAR(COUNT(NCBP.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP WHERE NCBP.APPLY_CODE = CM.APPLY_CODE AND NCBP.MASTER_BUSI_ITEM_ID IS NULL)
		                WHEN '1' THEN 
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP 
		                 WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                 AND (TBP.PRODUCT_CATEGORY = '10001' 
		                     OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                 WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 ELSE
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP ,DEV_NB.T_NB_CONTRACT_BUSI_PROD NBPA
		                  WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                  AND TNCP.APPLY_CODE = NBPA.APPLY_CODE
		                  AND NBPA.PRODUCT_CODE =  TNCP.PRODUCT_CODE
		                  AND (TBP.PRODUCT_CATEGORY = '10001' 
		                  OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                  WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 END
                 	   ) AS BUSINESS_PRODUCT_CODE,/*第一主险名称*/
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE = cm.POLICY_CODE AND pp.PRINT_TYPE = '1' AND pp.PRINT_STATUS = '3') > 0 
					  	THEN
				          TO_CHAR((SELECT max(pp.BPO_PRINT_DATE)
				                    FROM DEV_NB.T_POLICY_PRINT PP
				                   WHERE pp.POLICY_CODE = cm.POLICY_CODE),
				                  'YYYY-MM-DD')
				        ELSE ''
				      END) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      NULL AS RECEIPT_SIGN_DATE,
				      NULL AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(CASE WHEN (NULL = 0 OR NULL IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) END) AS OPERATOR,*/
              		  NULL AS OPERATOR,
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND NULL IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL IS NOT NULL AND NULL != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
					  ,(SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  CM.MEDIA_TYPE=TMT.MEDIA_TYPE_CODE) AS MEDIA_TYPE_DESC
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
		         LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
		         LEFT JOIN (SELECT MAX(FF.YWGGX) AS BUSINESS_PRODUCT_CODES, FF.APPLY_CODE
										            FROM (SELECT TO_CHAR(WM_CONCAT(BB.PRODUCT_CODE_SYS) 
										                     OVER (PARTITION BY BB.APPLY_CODE )) YWGGX,
																 BB.APPLY_CODE
														FROM (SELECT TNCP.APPLY_CODE, TBP.PRODUCT_CODE_SYS,TBP.PRODUCT_NAME_SYS，TNCP.PRODUCT_CODE
																		FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT     TBP
																	 WHERE TNCP.PRODUCT_CODE = TBP.PRODUCT_CODE_SYS
																		 AND (TBP.PRODUCT_CATEGORY = '10001' OR
																				 (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS
																					WHERE SSS.SUB_APPLY_CODE = TNCP.Apply_Code	AND SSS.IS_RELATION_VALID = '1'
																					AND SSS.RELATION_TYPE = '1') IS NOT NULL) and rownum<2 )  BB) FF
									 GROUP BY FF.APPLY_CODE) PCC	ON PCC.APPLY_CODE = CM.APPLY_CODE
				 WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33') AND PA.ACKNOWLEDGE_DATE IS NULL
				 AND CM.ORGAN_CODE LIKE CONCAT(#{receiptReportOrganID},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN ('02') ]]></if>
		<if test=" policyReinsureFlag != null and policyReinsureFlag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policyReinsureFlag} ]]></if><!-- 续保转保 -->  
	 	<!--是否绩优  -->
       	<if test=" isQualityAgent != null and isQualityAgent != '' and isQualityAgent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" isQualityAgent != null and isQualityAgent != '' and isQualityAgent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- 签收方式 -->
		<if test=" signType != null and signType !='' "><![CDATA[ AND A.SIGN_TYPE in (${signType}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" qualityFlag != null and qualityFlag != '' and qualityFlag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" qualityFlag != null and qualityFlag != '' and qualityFlag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" isScan != null and isScan != '' and isScan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" isScan != null and isScan != '' and isScan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY A.INPUT_DATE) B) C WHERE C.RN BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>	
  	
  
  
	<!-- 撤保清单-离线-job3 -->
	<select id="NB_queryAllReceiptforNewPagelx3" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM ROM, C.* FROM (SELECT ROWNUM RN, B.* FROM (SELECT A.* FROM (
			   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				       PCC.BUSINESS_PRODUCT_CODES,
				      (
		               CASE (SELECT TO_CHAR(COUNT(NCBP.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP WHERE NCBP.APPLY_CODE = CM.APPLY_CODE AND NCBP.MASTER_BUSI_ITEM_ID IS NULL)
		                WHEN '1' THEN 
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP 
		                 WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                 AND (TBP.PRODUCT_CATEGORY = '10001' 
		                     OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                 WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 ELSE
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP ,DEV_NB.T_NB_CONTRACT_BUSI_PROD NBPA
		                  WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                  AND TNCP.APPLY_CODE = NBPA.APPLY_CODE
		                  AND NBPA.ORDER_ID = '1'
		                  AND NBPA.PRODUCT_CODE =  TNCP.PRODUCT_CODE
		                  AND (TBP.PRODUCT_CATEGORY = '10001' 
		                  OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                  WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 END
                 	   ) AS BUSINESS_PRODUCT_CODE,/*第一主险名称*/
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE = cm.POLICY_CODE AND pp.PRINT_TYPE = '1' AND pp.PRINT_STATUS = '3') > 0 
					  	THEN
				          TO_CHAR((SELECT max(pp.BPO_PRINT_DATE)
				                    FROM DEV_NB.T_POLICY_PRINT PP
				                   WHERE pp.POLICY_CODE = cm.POLICY_CODE),
				                  'YYYY-MM-DD')
				        ELSE ''
				      END) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      NULL AS RECEIPT_SIGN_DATE,
				      NULL AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(CASE WHEN (NULL = 0 OR NULL IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) END) AS OPERATOR,*/
              		  NULL AS OPERATOR,
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND NULL IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL IS NOT NULL AND NULL != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R, 
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
				       ,(SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  CM.MEDIA_TYPE=TMT.MEDIA_TYPE_CODE) AS MEDIA_TYPE_DESC
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
		       LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
		       LEFT JOIN (SELECT MAX(FF.YWGGX) AS BUSINESS_PRODUCT_CODES, FF.APPLY_CODE
										FROM (SELECT TO_CHAR(WM_CONCAT(BB.PRODUCT_CODE_SYS) 
										                     OVER (PARTITION BY BB.APPLY_CODE )) YWGGX,
																 BB.APPLY_CODE
														FROM (SELECT TNCP.APPLY_CODE,TBP.PRODUCT_CODE_SYS,TBP.PRODUCT_NAME_SYS， TNCP.PRODUCT_CODE
																		FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP,
																				 DEV_PDS.T_BUSINESS_PRODUCT     TBP
																	 WHERE TNCP.PRODUCT_CODE = TBP.PRODUCT_CODE_SYS 
																		 AND (TBP.PRODUCT_CATEGORY = '10001' OR
																				 (SELECT SSS.SUB_BUSI_PROD_CODE
																						 FROM DEV_NB.T_NB_CONTRACT_RELATION SSS
																						WHERE SSS.SUB_APPLY_CODE = TNCP.Apply_Code
																							AND SSS.IS_RELATION_VALID = '1'
																							AND SSS.RELATION_TYPE = '1') IS NOT NULL) and rownum<2 )  BB) FF
									 GROUP BY FF.APPLY_CODE) PCC
										ON PCC.APPLY_CODE = CM.APPLY_CODE
			WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33') 
			AND PA.ACKNOWLEDGE_DATE IS NULL
		 	AND CM.ORGAN_CODE LIKE CONCAT(#{receiptReportOrganID},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN ('02') ]]></if>
		<if test=" policyReinsureFlag != null and policyReinsureFlag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policyReinsureFlag} ]]></if><!-- 续保转保 -->  
	 	<!--是否绩优  -->
       	<if test=" isQualityAgent != null and isQualityAgent != '' and isQualityAgent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" isQualityAgent != null and isQualityAgent != '' and isQualityAgent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ UNION ALL 
		 SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				       PCC.BUSINESS_PRODUCT_CODES,
				      (
                 		CASE (SELECT TO_CHAR(COUNT(NCBP.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP WHERE NCBP.APPLY_CODE = CM.APPLY_CODE AND NCBP.MASTER_BUSI_ITEM_ID IS NULL)
                 		WHEN '1' THEN 
                 			(SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP 
                  			WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
                  			AND (TBP.PRODUCT_CATEGORY = '10001' 
                      			OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
                  					WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
                  		ELSE
                 			(SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_PRODUCT TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP ,DEV_NB.T_NB_CONTRACT_BUSI_PROD NBPA
                   			WHERE '00' || TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
                   			AND TNCP.APPLY_CODE = NBPA.APPLY_CODE
                            AND NBPA.PRODUCT_CODE = '00' || TNCP.PRODUCT_CODE
                   			AND (TBP.PRODUCT_CATEGORY = '10001' 
                                   OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
                   					   WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
                         END
                 	   ) AS BUSINESS_PRODUCT_CODE,/*第一主险名称*/
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE = cm.POLICY_CODE AND pp.PRINT_TYPE = '1' AND pp.PRINT_STATUS = '3') > 0 
					  	THEN
				          TO_CHAR((SELECT max(pp.BPO_PRINT_DATE)
				                    FROM DEV_NB.T_POLICY_PRINT PP
				                   WHERE pp.POLICY_CODE = cm.POLICY_CODE),
				                  'YYYY-MM-DD')
				        ELSE ''
				      END) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      /*TO_CHAR((SELECT MAX(PA.ACKNOWLEDGE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,*/
				      TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
				      /*TO_CHAR((SELECT MAX(PA.BRANCH_RECEIVE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS INPUT_DATE,*/
				      TO_CHAR(PA.BRANCH_RECEIVE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(SELECT (CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.UPDATE_BY) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.OPERATOR_ID) END) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) AS OPERATOR,*/
				      (case when CM.media_type = '1' then 
				           (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY)
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source = '0' then 
				                 (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY) 
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source != '0' then 
				                (select
				                    CA.Agent_Code || '-' || CA.AGENT_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                from APP___NB__DBUSER.T_UDMP_ORG_REL O
				                WHERE O.ORGAN_CODE = CA.Agent_Organ_Code)
				         else 
				         (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                    FROM APP___NB__DBUSER.T_UDMP_USER U
				                      INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                     ON U.ORGAN_CODE = O.ORGAN_CODE
				                  WHERE U.USER_ID = PA.UPDATE_BY) 
				         end) as OPERATOR,
				      /*(CASE WHEN CM.MEDIA_TYPE = '1' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NOT NULL AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, \*40588 LIUPENGIT1*\*/
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
				 	,(SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  CM.MEDIA_TYPE=TMT.MEDIA_TYPE_CODE) AS MEDIA_TYPE_DESC
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
				LEFT JOIN (SELECT MAX(FF.YWGGX) AS BUSINESS_PRODUCT_CODES, FF.APPLY_CODE
										FROM (SELECT TO_CHAR(WM_CONCAT(BB.PRODUCT_CODE_SYS) 
										                     OVER (PARTITION BY BB.APPLY_CODE )) YWGGX,
																 BB.APPLY_CODE
														FROM (SELECT TNCP.APPLY_CODE,TBP.PRODUCT_CODE_SYS,TBP.PRODUCT_NAME_SYS，TNCP.PRODUCT_CODE
																		FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP,
																				 DEV_PDS.T_BUSINESS_PRODUCT     TBP
																	 WHERE TNCP.PRODUCT_CODE = TBP.PRODUCT_CODE_SYS
			
																		 AND (TBP.PRODUCT_CATEGORY = '10001' OR
																				 (SELECT SSS.SUB_BUSI_PROD_CODE
																						 FROM DEV_NB.T_NB_CONTRACT_RELATION SSS
																						WHERE SSS.SUB_APPLY_CODE = TNCP.Apply_Code
																							AND SSS.IS_RELATION_VALID = '1'
																							AND SSS.RELATION_TYPE = '1') IS NOT NULL)) BB) FF
									 GROUP BY FF.APPLY_CODE) PCC
										ON PCC.APPLY_CODE = CM.APPLY_CODE
				WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
				  AND CM.ORGAN_CODE LIKE CONCAT(#{receiptReportOrganID},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN ('02') ]]></if>
		<if test=" policyReinsureFlag != null and policyReinsureFlag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policyReinsureFlag} ]]></if><!-- 续保转保 -->  
		<!-- 电子渠道 -->
		<if test=" receiptReportTypeCode != null and receiptReportTypeCode !='' "><![CDATA[ AND CM.SUBINPUT_TYPE = ${receiptReportTypeCode} ]]></if>
	 	<!--是否绩优  -->
       	<if test=" isQualityAgent != null and isQualityAgent != '' and isQualityAgent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" isQualityAgent != null and isQualityAgent != '' and isQualityAgent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- 签收方式 -->
		<if test=" signType != null and signType !='' "><![CDATA[ AND A.SIGN_TYPE in (${signType}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" qualityFlag != null and qualityFlag != '' and qualityFlag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" qualityFlag != null and qualityFlag != '' and qualityFlag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" isScan != null and isScan != '' and isScan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" isScan != null and isScan != '' and isScan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY A.INPUT_DATE) B) C WHERE C.RN BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>
  
  
    <!-- 撤保清单-离线-job2 -->
    <select id="NB_queryAllReceiptforNewPagelx2" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT ROWNUM ROM,B.*  FROM (SELECT ROWNUM RN, AA.* FROM (SELECT A.* FROM (
		  	   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				       PCC.BUSINESS_PRODUCT_CODES,
				      (
		               CASE (SELECT TO_CHAR(COUNT(NCBP.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP WHERE NCBP.APPLY_CODE = CM.APPLY_CODE AND NCBP.MASTER_BUSI_ITEM_ID IS NULL)
		                WHEN '1' THEN 
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP 
		                 WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                 AND (TBP.PRODUCT_CATEGORY = '10001' 
		                     OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                 WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 ELSE
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP ,DEV_NB.T_NB_CONTRACT_BUSI_PROD NBPA
		                  WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                  AND TNCP.APPLY_CODE = NBPA.APPLY_CODE
		                  AND NBPA.PRODUCT_CODE =  TNCP.PRODUCT_CODE
		                  AND (TBP.PRODUCT_CATEGORY = '10001' 
		                  OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                  WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 END
                 	   ) AS BUSINESS_PRODUCT_CODE,/*第一主险名称*/
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE = cm.POLICY_CODE AND pp.PRINT_TYPE = '1' AND pp.PRINT_STATUS = '3') > 0 
					  	THEN
				          TO_CHAR((SELECT max(pp.BPO_PRINT_DATE)
				                    FROM DEV_NB.T_POLICY_PRINT PP
				                   WHERE pp.POLICY_CODE = cm.POLICY_CODE),
				                  'YYYY-MM-DD')
				        ELSE ''
				      END) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      /*TO_CHAR((SELECT MAX(PA.ACKNOWLEDGE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,*/
				      TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
				      /*TO_CHAR((SELECT MAX(PA.BRANCH_RECEIVE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS INPUT_DATE,*/
				      TO_CHAR(PA.BRANCH_RECEIVE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(SELECT (CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.UPDATE_BY) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.OPERATOR_ID) END) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) AS OPERATOR,*/
				      (case when CM.media_type = '1' then 
				           (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY)
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source = '0' then 
				                 (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY) 
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source != '0' then 
				                (select
				                    CA.Agent_Code || '-' || CA.AGENT_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                from APP___NB__DBUSER.T_UDMP_ORG_REL O
				                WHERE O.ORGAN_CODE = CA.Agent_Organ_Code)
				         else 
				         (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                    FROM APP___NB__DBUSER.T_UDMP_USER U
				                      INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                     ON U.ORGAN_CODE = O.ORGAN_CODE
				                  WHERE U.USER_ID = PA.UPDATE_BY) 
				         end) as OPERATOR,
				      /*(CASE WHEN CM.MEDIA_TYPE = '1' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NOT NULL AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, \*40588 LIUPENGIT1*\*/
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
				 	,(SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  CM.MEDIA_TYPE=TMT.MEDIA_TYPE_CODE) AS MEDIA_TYPE_DESC
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
				
				 LEFT JOIN 
									(
									SELECT MAX(FF.YWGGX) AS BUSINESS_PRODUCT_CODES, FF.APPLY_CODE
										FROM (SELECT TO_CHAR(WM_CONCAT(BB.PRODUCT_CODE_SYS) 
										                     OVER (PARTITION BY BB.APPLY_CODE )) YWGGX,
																 BB.APPLY_CODE
														FROM (SELECT TNCP.APPLY_CODE,  TBP.PRODUCT_CODE_SYS,
																				 TBP.PRODUCT_NAME_SYS， TNCP.PRODUCT_CODE
																		FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT     TBP
																	 WHERE TNCP.PRODUCT_CODE = TBP.PRODUCT_CODE_SYS			
																		 AND (TBP.PRODUCT_CATEGORY = '10001' OR
																				 (SELECT SSS.SUB_BUSI_PROD_CODE
																						 FROM DEV_NB.T_NB_CONTRACT_RELATION SSS
																						WHERE SSS.SUB_APPLY_CODE = TNCP.Apply_Code
																							AND SSS.IS_RELATION_VALID = '1'
																							AND SSS.RELATION_TYPE = '1') IS NOT NULL)) BB) FF
									 GROUP BY FF.APPLY_CODE) PCC
										ON PCC.APPLY_CODE = CM.APPLY_CODE
				WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
				  AND CM.ORGAN_CODE LIKE CONCAT(#{receiptReportOrganID},'%') ]]>		
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN ('02') ]]></if>
		<if test=" policyReinsureFlag != null and policyReinsureFlag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policyReinsureFlag} ]]></if><!-- 续保转保 -->  
	 	<!--是否绩优  -->
       	<if test=" isQualityAgent != null and isQualityAgent != '' and isQualityAgent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" isQualityAgent != null and isQualityAgent != '' and isQualityAgent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- 签收方式 -->
		<if test=" salesChannels != null and salesChannels !='' "><![CDATA[ AND A.SIGN_TYPE in (${salesChannels}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" qualityFlag != null and qualityFlag != '' and qualityFlag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" qualityFlag != null and qualityFlag != '' and qualityFlag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" isScan != null and isScan != '' and isScan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" isScan != null and isScan != '' and isScan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY AA.INPUT_DATE) B  WHERE B.RN  BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>

	
	 <select id="NB_queryAllReceiptforNewPageq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM ROM, C.* FROM (SELECT ROWNUM RN, B.* FROM (SELECT A.* FROM (
			   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				       PCC.BUSINESS_PRODUCT_CODES,
				      (
		               CASE (SELECT TO_CHAR(COUNT(NCBP.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP WHERE NCBP.APPLY_CODE = CM.APPLY_CODE AND NCBP.MASTER_BUSI_ITEM_ID IS NULL)
		                WHEN '1' THEN 
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP 
		                 WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                 AND (TBP.PRODUCT_CATEGORY = '10001' 
		                     OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                 WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 ELSE
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP ,DEV_NB.T_NB_CONTRACT_BUSI_PROD NBPA
		                  WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                  AND TNCP.APPLY_CODE = NBPA.APPLY_CODE
		                  AND NBPA.PRODUCT_CODE =  TNCP.PRODUCT_CODE
		                  AND (TBP.PRODUCT_CATEGORY = '10001' 
		                  OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                  WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 END
                 	   ) AS BUSINESS_PRODUCT_CODE,/*第一主险名称*/
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              TO_CHAR((SELECT max(pp.PRINT_TIME) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE =  cm.POLICY_CODE)) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      NULL AS RECEIPT_SIGN_DATE,
				      NULL AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(CASE WHEN (NULL = 0 OR NULL IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) END) AS OPERATOR,*/
              		  NULL AS OPERATOR,
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND NULL IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL IS NOT NULL AND NULL != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R, 
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
				       ,(SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  CM.MEDIA_TYPE=TMT.MEDIA_TYPE_CODE) AS MEDIA_TYPE_DESC
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
		       LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
		       LEFT JOIN (SELECT MAX(FF.YWGGX) AS BUSINESS_PRODUCT_CODES, FF.APPLY_CODE
										FROM (SELECT TO_CHAR(WM_CONCAT(BB.PRODUCT_CODE_SYS) 
										                     OVER (PARTITION BY BB.APPLY_CODE ORDER BY BB.ORDER_ID)) YWGGX,
																 BB.APPLY_CODE
														FROM (SELECT TNCP.APPLY_CODE, TNCP.ORDER_ID, TBP.PRODUCT_CODE_SYS,TBP.PRODUCT_NAME_SYS， TNCP.PRODUCT_CODE
																		FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP,
																				 DEV_PDS.T_BUSINESS_PRODUCT     TBP
																	 WHERE TNCP.PRODUCT_CODE = TBP.PRODUCT_CODE_SYS 
																		 AND (TBP.PRODUCT_CATEGORY = '10001' OR
																				 (SELECT SSS.SUB_BUSI_PROD_CODE
																						 FROM DEV_NB.T_NB_CONTRACT_RELATION SSS
																						WHERE SSS.SUB_APPLY_CODE = TNCP.Apply_Code
																							AND SSS.IS_RELATION_VALID = '1'
																							AND SSS.RELATION_TYPE = '1') IS NOT NULL)) BB) FF
									 GROUP BY FF.APPLY_CODE) PCC
										ON PCC.APPLY_CODE = CM.APPLY_CODE
			WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33') 
			AND PA.ACKNOWLEDGE_DATE IS NULL
		 	AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if><!-- 续保转保 --> 	 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ UNION ALL 
		 SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				       PCC.BUSINESS_PRODUCT_CODES,
				      (
                 		CASE (SELECT TO_CHAR(COUNT(NCBP.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP WHERE NCBP.APPLY_CODE = CM.APPLY_CODE AND NCBP.MASTER_BUSI_ITEM_ID IS NULL)
                 		WHEN '1' THEN 
                 			(SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP 
                  			WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
                  			AND (TBP.PRODUCT_CATEGORY = '10001' 
                      			OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
                  					WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
                  		ELSE
                 			(SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_PRODUCT TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP ,DEV_NB.T_NB_CONTRACT_BUSI_PROD NBPA
                   			WHERE '00' || TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
                   			AND TNCP.APPLY_CODE = NBPA.APPLY_CODE
                            AND NBPA.ORDER_ID = '1'
                            AND NBPA.PRODUCT_CODE = '00' || TNCP.PRODUCT_CODE
                   			AND (TBP.PRODUCT_CATEGORY = '10001' 
                                   OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
                   					   WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
                         END
                 	   ) AS BUSINESS_PRODUCT_CODE,/*第一主险名称*/
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,
				      TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
				      /*TO_CHAR((SELECT MAX(PA.ACKNOWLEDGE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,*/
				      TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
				      /*TO_CHAR((SELECT MAX(PA.BRANCH_RECEIVE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS INPUT_DATE,*/
				      TO_CHAR(PA.BRANCH_RECEIVE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(SELECT (CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.UPDATE_BY) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.OPERATOR_ID) END) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) AS OPERATOR,*/
				      (case when CM.media_type = '1' then 
				           (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY)
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source = '0' then 
				                 (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY) 
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source != '0' then 
				                (select
				                    CA.Agent_Code || '-' || CA.AGENT_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                from APP___NB__DBUSER.T_UDMP_ORG_REL O
				                WHERE O.ORGAN_CODE = CA.Agent_Organ_Code)
				         else 
				         (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                    FROM APP___NB__DBUSER.T_UDMP_USER U
				                      INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                     ON U.ORGAN_CODE = O.ORGAN_CODE
				                  WHERE U.USER_ID = PA.UPDATE_BY) 
				         end) as OPERATOR,
				      /*(CASE WHEN CM.MEDIA_TYPE = '1' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NOT NULL AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, \*40588 LIUPENGIT1*\*/
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
				 	,(SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  CM.MEDIA_TYPE=TMT.MEDIA_TYPE_CODE) AS MEDIA_TYPE_DESC
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
				LEFT JOIN (SELECT MAX(FF.YWGGX) AS BUSINESS_PRODUCT_CODES, FF.APPLY_CODE
										FROM (SELECT TO_CHAR(WM_CONCAT(BB.PRODUCT_CODE_SYS) 
										                     OVER (PARTITION BY BB.APPLY_CODE ORDER BY BB.ORDER_ID)) YWGGX,
																 BB.APPLY_CODE
														FROM (SELECT TNCP.APPLY_CODE, TNCP.ORDER_ID,TBP.PRODUCT_CODE_SYS,TBP.PRODUCT_NAME_SYS，TNCP.PRODUCT_CODE
																		FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP,
																				 DEV_PDS.T_BUSINESS_PRODUCT     TBP
																	 WHERE TNCP.PRODUCT_CODE = TBP.PRODUCT_CODE_SYS
			
																		 AND (TBP.PRODUCT_CATEGORY = '10001' OR
																				 (SELECT SSS.SUB_BUSI_PROD_CODE
																						 FROM DEV_NB.T_NB_CONTRACT_RELATION SSS
																						WHERE SSS.SUB_APPLY_CODE = TNCP.Apply_Code
																							AND SSS.IS_RELATION_VALID = '1'
																							AND SSS.RELATION_TYPE = '1') IS NOT NULL)) BB) FF
									 GROUP BY FF.APPLY_CODE) PCC
										ON PCC.APPLY_CODE = CM.APPLY_CODE
				LEFT JOIN DEV_NB.T_POLICY_PRINT PP ON PP.POLICY_CODE = CM.POLICY_CODE
				WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
				  AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	 	<if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if><!-- 续保转保 --> 
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>		
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND A.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY A.INPUT_DATE) B) C WHERE C.RN BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>
	
	<select id="NB_queryAllReceiptforNewCountq" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT C.* FROM (SELECT B.* FROM (SELECT COUNT(1) FROM (
				SELECT TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
					   TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
					   (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
					   (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		          FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
		         INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
		                      FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				 INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
		         WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
		           AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	 	<!-- 续保转保 -->        
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if>	 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ UNION ALL 
		SELECT NULL AS RECEIPT_SIGN_DATE,
					   TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
					   (CASE WHEN CM.MEDIA_TYPE = '1' AND NULL IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL IS NOT NULL AND NULL != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
					   (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		          FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
		         INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
		                      FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
		       LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE 
				   WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')	
				   AND PA.ACKNOWLEDGE_DATE IS NULL
				   AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>		
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND A.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ) B) C ]]>
	</select>
	

	 
	<!-- 回执清单优化:2021-03-06 --> 
	<select id="NB_queryAllReceiptforNewCountq1" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(*) FROM (SELECT * FROM (SELECT * FROM (
				SELECT TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
					   TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
					   (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
					   (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		          FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
		         INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
		                      FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				 INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
		         WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
		           AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	 	<!-- 续保转保 -->        
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if>	 		 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
		<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
	  	<![CDATA[ ) A WHERE 1 = 1 ]]>		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND A.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ) B ]]>
	</select>
	
	<!-- 回执清单优化:2021-03-06 -->
	<select id="NB_queryAllReceiptforNewPageq1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM ROM,B.*  FROM (SELECT ROWNUM RN, AA.* FROM (SELECT A.* FROM (
		  	   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				       PCC.BUSINESS_PRODUCT_CODES,
				      (
		               CASE (SELECT TO_CHAR(COUNT(NCBP.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP WHERE NCBP.APPLY_CODE = CM.APPLY_CODE AND NCBP.MASTER_BUSI_ITEM_ID IS NULL)
		                WHEN '1' THEN 
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP 
		                 WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                 AND (TBP.PRODUCT_CATEGORY = '10001' 
		                     OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                 WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 ELSE
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP ,DEV_NB.T_NB_CONTRACT_BUSI_PROD NBPA
		                  WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                  AND TNCP.APPLY_CODE = NBPA.APPLY_CODE
		                  AND NBPA.PRODUCT_CODE =  TNCP.PRODUCT_CODE
		                  AND (TBP.PRODUCT_CATEGORY = '10001' 
		                  OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                  WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 END
                 	   ) AS BUSINESS_PRODUCT_CODE,/*第一主险名称*/
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              TO_CHAR((SELECT max(pp.PRINT_TIME) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE =  cm.POLICY_CODE)) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      /*TO_CHAR((SELECT MAX(PA.ACKNOWLEDGE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,*/
				      TO_CHAR(PA.ACKNOWLEDGE_DATE, 'YYYY-MM-DD') AS RECEIPT_SIGN_DATE,
				      /*TO_CHAR((SELECT MAX(PA.BRANCH_RECEIVE_DATE) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS INPUT_DATE,*/
				      TO_CHAR(PA.BRANCH_RECEIVE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(SELECT (CASE WHEN (PA.OPERATOR_ID = 0 OR PA.OPERATOR_ID IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.UPDATE_BY) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = PA.OPERATOR_ID) END) FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) AS OPERATOR,*/
				      (case when CM.media_type = '1' then 
				           (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY)
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source = '0' then 
				                 (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                FROM APP___NB__DBUSER.T_UDMP_USER U
				                  INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                 ON U.ORGAN_CODE = O.ORGAN_CODE
				              WHERE U.USER_ID = PA.UPDATE_BY) 
				          when CM.media_type in ('0','2') and PA.Sign_Source is not null and PA.Sign_Source != '0' then 
				                (select
				                    CA.Agent_Code || '-' || CA.AGENT_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                from APP___NB__DBUSER.T_UDMP_ORG_REL O
				                WHERE O.ORGAN_CODE = CA.Agent_Organ_Code)
				         else 
				         (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME
				                    FROM APP___NB__DBUSER.T_UDMP_USER U
				                      INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O
				                     ON U.ORGAN_CODE = O.ORGAN_CODE
				                  WHERE U.USER_ID = PA.UPDATE_BY) 
				         end) as OPERATOR,
				      /*(CASE WHEN CM.MEDIA_TYPE = '1' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) IS NOT NULL AND (SELECT PA.SIGN_SOURCE FROM APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA WHERE PA.POLICY_CODE = CM.POLICY_CODE) != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, \*40588 LIUPENGIT1*\*/
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND PA.SIGN_SOURCE IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND PA.SIGN_SOURCE IS NOT NULL AND PA.SIGN_SOURCE != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
				 	,(SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  CM.MEDIA_TYPE=TMT.MEDIA_TYPE_CODE) AS MEDIA_TYPE_DESC
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
				INNER JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON (PA.POLICY_CODE = CM.POLICY_CODE AND PA.ACKNOWLEDGE_DATE IS NOT NULL)
				
				 LEFT JOIN 
									(
									SELECT MAX(FF.YWGGX) AS BUSINESS_PRODUCT_CODES, FF.APPLY_CODE
										FROM (SELECT TO_CHAR(WM_CONCAT(BB.PRODUCT_CODE_SYS) 
										                     OVER (PARTITION BY BB.APPLY_CODE )) YWGGX,
																 BB.APPLY_CODE
														FROM (SELECT TNCP.APPLY_CODE, TBP.PRODUCT_CODE_SYS,
																				 TBP.PRODUCT_NAME_SYS， TNCP.PRODUCT_CODE
																		FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT     TBP
																	 WHERE TNCP.PRODUCT_CODE = TBP.PRODUCT_CODE_SYS			
																		 AND (TBP.PRODUCT_CATEGORY = '10001' OR
																				 (SELECT SSS.SUB_BUSI_PROD_CODE
																						 FROM DEV_NB.T_NB_CONTRACT_RELATION SSS
																						WHERE SSS.SUB_APPLY_CODE = TNCP.Apply_Code
																							AND SSS.IS_RELATION_VALID = '1'
																							AND SSS.RELATION_TYPE = '1') IS NOT NULL)) BB) FF
									 GROUP BY FF.APPLY_CODE) PCC
										ON PCC.APPLY_CODE = CM.APPLY_CODE
				WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
				  AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if><!-- 续保转保 -->  		
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
	  	<![CDATA[ ) A WHERE 1 = 1 ]]>		<![CDATA[ ) AA WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND AA.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND AA.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (AA.QT_STATUS != 7 OR AA.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND AA.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND AA.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY AA.INPUT_DATE) B  WHERE B.RN  BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>
	
	<!-- 回执清单优化:2021-03-06 --> 
	<select id="NB_queryAllReceiptforNewCountq2" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT C.* FROM (SELECT B.* FROM (SELECT COUNT(1) FROM (
				SELECT NULL AS RECEIPT_SIGN_DATE,
					   TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
					   (CASE WHEN CM.MEDIA_TYPE = '1' AND NULL IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL IS NOT NULL AND NULL != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
					   (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
		          FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
		         INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
		                                   AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
		                           (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
		                           (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
		                                   AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
		                      FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
		         LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE 
				   WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33')
				   AND PA.ACKNOWLEDGE_DATE IS NULL
				   AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	 	<!-- 续保转保 -->        
	    <if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if>	 		 	
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND A.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ) B) C ]]>
	</select>
	
	<!-- 回执清单优化:2021-03-06 -->
	<select id="NB_queryAllReceiptforNewPageq2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM ROM, C.* FROM (SELECT ROWNUM RN, B.* FROM (SELECT A.* FROM (
			   SELECT CM.POLICY_CODE,
				      CM.APPLY_CODE,
				      (SELECT C.SALES_CHANNEL_NAME FROM APP___NB__DBUSER.T_SALES_CHANNEL C WHERE C.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE) AS CHANNEL_TYPE,
				      PCC.BUSINESS_PRODUCT_CODES,
				      (
		               CASE (SELECT TO_CHAR(COUNT(NCBP.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP WHERE NCBP.APPLY_CODE = CM.APPLY_CODE AND NCBP.MASTER_BUSI_ITEM_ID IS NULL)
		                WHEN '1' THEN 
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP 
		                 WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                 AND (TBP.PRODUCT_CATEGORY = '10001' 
		                     OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                 WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 ELSE
		                (SELECT TO_CHAR(WM_CONCAT(TBP.PRODUCT_CODE_SYS)) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT TBP ,DEV_NB.T_NB_CONTRACT_BUSI_PROD NBPA
		                  WHERE TNCP.PRODUCT_CODE=TBP.PRODUCT_CODE_SYS AND TNCP.APPLY_CODE=CM.APPLY_CODE 
		                  AND TNCP.APPLY_CODE = NBPA.APPLY_CODE
		                  AND NBPA.PRODUCT_CODE =  TNCP.PRODUCT_CODE
		                  AND (TBP.PRODUCT_CATEGORY = '10001' 
		                  OR (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS 
		                  WHERE SSS.SUB_APPLY_CODE= CM.APPLY_CODE AND SSS.IS_RELATION_VALID='1' AND SSS.RELATION_TYPE = '1') IS NOT NULL))
		                 END
                 	   ) AS BUSINESS_PRODUCT_CODE,/*第一主险名称*/
				      CM.ORGAN_CODE,
				      (CASE WHEN LENGTH(CM.ORGAN_CODE) = 4 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN LENGTH(CM.ORGAN_CODE) = 6 OR LENGTH(CM.ORGAN_CODE) = 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 4)) ELSE '' END) AS ORGAN2_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = SUBSTR(CM.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
				      (CASE LENGTH(CM.ORGAN_CODE) WHEN 8 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = CM.ORGAN_CODE) ELSE '' END) AS ORGAN4_NAME,
				      CA.AGENT_CODE || '-' || CA.AGENT_NAME AS SALE_PERSON,
				      (SELECT L.AGENT_LEVEL_DESC FROM APP___NB__DBUSER.T_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = CA.AGENT_LEVEL) AS AGENT_LEVEL_DESC,
				      (SELECT C.CUSTOMER_NAME FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER H INNER JOIN APP___NB__DBUSER.T_CUSTOMER C ON (H.CUSTOMER_ID = C.CUSTOMER_ID) WHERE H.APPLY_CODE = CM.APPLY_CODE AND H.POLICY_CODE = CM.POLICY_CODE) AS CUSTOMER,
				      (SELECT SUM(P.TOTAL_PREM_AF) FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT P WHERE P.APPLY_CODE = CM.APPLY_CODE) AS PREM,
				      TO_CHAR(CM.APPLY_DATE, 'YYYY-MM-DD') AS APPLY_DATE,TO_CHAR(CM.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') AS INPUT_DATE,
		              TO_CHAR(CM.ISSUE_DATE, 'YYYY-MM-DD') AS SIGN_DATE,
		              TO_CHAR((SELECT max(pp.PRINT_TIME) FROM DEV_NB.T_POLICY_PRINT PP WHERE pp.POLICY_CODE =  cm.POLICY_CODE)) AS PRINT_TIME,
		              (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PRO WHERE PRO.APPLY_CODE = CM.APPLY_CODE
		              AND PRO.PROCESS_STEP = '19') > 0 THEN '是' ELSE '否' END )AS IS_UW_FLAG,
				      NULL AS RECEIPT_SIGN_DATE,
				      NULL AS BRANCH_RECEIVE_DATE,
				      TO_CHAR((SELECT MAX(IMG.SCAN_TIME) FROM APP___NB__DBUSER.T_IMAGE_SCAN IMG WHERE IMG.BILLCARD_CODE = 'UN004' AND IMG.BUSS_CODE = CM.APPLY_CODE OR IMG.BUSS_CODE = CM.POLICY_CODE), 'YYYY-MM-DD') AS SCAN_DATE,
				      /*(CASE WHEN (NULL = 0 OR NULL IS NULL) THEN (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) ELSE (SELECT U.USER_NAME || '-' || U.REAL_NAME || '-' || O.ORGAN_CODE || '-' || O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_USER U INNER JOIN APP___NB__DBUSER.T_UDMP_ORG_REL O ON U.ORGAN_CODE = O.ORGAN_CODE WHERE U.USER_ID = NULL) END) AS OPERATOR,*/
              		  NULL AS OPERATOR,
				      (CASE WHEN CM.MEDIA_TYPE = '1' AND NULL IS NULL THEN '1' WHEN CM.MEDIA_TYPE = '0' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL = 0 THEN '0' WHEN CM.MEDIA_TYPE = '2' AND NULL IS NOT NULL AND NULL != 0 THEN '1' ELSE '' END) AS SIGN_TYPE, /*40588 LIUPENGIT1*/
				      DECODE((SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE), 7, '是', NULL, '', '否') AS QUALITY_FLAG,
				      CA.SALES_ORGAN_NAME_R,
				      CA.SALES_ORGAN_NAME_D,
				      CA.SALES_ORGAN_NAME_G,CM.POLICY_REINSURE_FLAG,CM.IS_MUTUAL_INSURED,CM.IS_SELF_INSURED,
				      (SELECT B.BANK_BRANCH_NAME FROM APP___NB__DBUSER.T_BANK_BRANCH B WHERE B.BANK_BRANCH_CODE = CM.SERVICE_BANK_BRANCH) AS BANK_BRANCH_NAME,
				      (SELECT MAX(QT.QT_STATUS) FROM APP___NB__DBUSER.T_NB_QT_TASK QT WHERE QT.QA_TYPE = '5' AND CM.APPLY_CODE = QT.APPLY_CODE) AS QT_STATUS /*查询条件:质检是否通过*/
					  ,(SELECT TMT.MEDIA_TYPE_DESC FROM DEV_NB.T_MEDIA_TYPE TMT WHERE  CM.MEDIA_TYPE=TMT.MEDIA_TYPE_CODE) AS MEDIA_TYPE_DESC
				 FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER CM
				INNER JOIN (SELECT CA.APPLY_CODE, CA.POLICY_CODE, AG.SALES_ORGAN_CODE, AG.AGENT_ORGAN_CODE, AG.AGENT_CODE, AG.AGENT_NAME, AG.AGENT_LEVEL,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 1
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))))) AS SALES_ORGAN_NAME_R,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 2
				                          AND (SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE OR SO.SALES_ORGAN_CODE =
				                  (SELECT SO.PARENT_CODE FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE))) AS SALES_ORGAN_NAME_D,
				                  (SELECT SO.SALES_ORGAN_NAME FROM APP___NB__DBUSER.T_SALES_ORGAN SO WHERE SO.ORGAN_LEVEL_CODE = 3
				                          AND SO.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AS SALES_ORGAN_NAME_G
				             FROM APP___NB__DBUSER.T_NB_CONTRACT_AGENT CA INNER JOIN APP___NB__DBUSER.T_AGENT AG ON (AG.AGENT_CODE = CA.AGENT_CODE)) CA ON (CA.APPLY_CODE = CM.APPLY_CODE AND CA.POLICY_CODE = CM.POLICY_CODE)
		         LEFT JOIN APP___NB__DBUSER.T_NB_POLICY_ACKNOWLEDGEMENT PA ON PA.POLICY_CODE = CM.POLICY_CODE
		         LEFT JOIN (SELECT MAX(FF.YWGGX) AS BUSINESS_PRODUCT_CODES, FF.APPLY_CODE
										            FROM (SELECT TO_CHAR(WM_CONCAT(BB.PRODUCT_CODE_SYS) 
										                     OVER (PARTITION BY BB.APPLY_CODE)) YWGGX,
																 BB.APPLY_CODE
														FROM (SELECT TNCP.APPLY_CODE,TBP.PRODUCT_CODE_SYS,TBP.PRODUCT_NAME_SYS，TNCP.PRODUCT_CODE
																		FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP, DEV_PDS.T_BUSINESS_PRODUCT     TBP
																	 WHERE TNCP.PRODUCT_CODE = TBP.PRODUCT_CODE_SYS
																		 AND (TBP.PRODUCT_CATEGORY = '10001' OR
																				 (SELECT SSS.SUB_BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_RELATION SSS
																					WHERE SSS.SUB_APPLY_CODE = TNCP.Apply_Code	AND SSS.IS_RELATION_VALID = '1'
																					AND SSS.RELATION_TYPE = '1') IS NOT NULL)) BB) FF
									 GROUP BY FF.APPLY_CODE) PCC	ON PCC.APPLY_CODE = CM.APPLY_CODE
				 WHERE (CM.PROPOSAL_STATUS != '08' AND CM.PROPOSAL_STATUS != '12' AND CM.PROPOSAL_STATUS != '22' AND CM.PROPOSAL_STATUS != '33') AND PA.ACKNOWLEDGE_DATE IS NULL
				 AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') ]]>
		<!-- 销售渠道 -->
		<if test=" channel_type != null and channel_type !='' "><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type}) ]]></if>
		<if test=" policy_reinsure_flag != null and policy_reinsure_flag != '' "><![CDATA[AND CM.POLICY_REINSURE_FLAG = #{policy_reinsure_flag} ]]></if><!-- 续保转保 -->  
	 	<!--是否绩优  -->
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '1'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NOT NULL ]]></if>
       	<if test=" is_quality_agent != null and is_quality_agent != '' and is_quality_agent == '0'.toString() "><![CDATA[ AND CA.AGENT_LEVEL IS NULL ]]></if>
       	<include refid="NB_queryAllReceiptforNewPageQueryType"></include>
		<![CDATA[ ) A WHERE 1 = 1 ]]>
		<!-- 统计类型 -->
		<!-- <if test=" policy_type != null and policy_type != '' and policy_type == '1'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NOT NULL ]]></if> -->
	    <!-- <if test=" policy_type != null and policy_type != '' and policy_type == '0'.toString() "><![CDATA[ AND A.RECEIPT_SIGN_DATE IS NULL ]]></if> -->
		<!-- 签收方式 -->
		<if test=" sign_type != null and sign_type !='' "><![CDATA[ AND A.SIGN_TYPE in (${sign_type}) ]]></if>
		<!-- 质检是否通过 -->
		<if test=" quality_flag != null and quality_flag != '' and quality_flag == '1'.toString() "><![CDATA[ AND A.QT_STATUS = 7 ]]></if>
	  	<if test=" quality_flag != null and quality_flag != '' and quality_flag == '0'.toString() "><![CDATA[ AND (A.QT_STATUS != 7 OR A.QT_STATUS IS NULL) ]]></if>
		<!-- 回访扫描 -->
		<if test=" is_scan != null and is_scan != '' and is_scan == '1'.toString() "><![CDATA[ AND A.SCAN_DATE IS NOT NULL ]]></if>
		<if test=" is_scan != null and is_scan != '' and is_scan == '0'.toString() "><![CDATA[ AND A.SCAN_DATE IS NULL ]]></if>
		<![CDATA[ ORDER BY A.INPUT_DATE) B) C WHERE C.RN BETWEEN (#{GREATER_NUM} + 1) AND #{LESS_NUM} ]]>
	</select>
	
</mapper>