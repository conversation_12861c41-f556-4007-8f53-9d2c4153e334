<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.interfaces.model.cus.po.ServiceListPO">
	
	<!-- 查询清单字段 -->
	<select id="findAllServiceListdictionary" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from dev_pas.T_SERVICE_LIST 	
		]]>
	</select>
	<!-- 查询保全项的字段 -->
	<select id="findAllService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from dev_pas.t_service
		]]>
	</select>
	<!-- 查询申请方式的字段 -->
	<select id="findAllServiceType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from dev_pas.t_SERVICE_TYPE
		]]>
	</select>
	
	<!-- 码表转换 -->
	<select id="queryNameCodeValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT ${code} AS CODE, ${name} AS NAME FROM ${table} WHERE 1 = 1 AND ${code} = #{value}
		]]>
	</select>
	
</mapper>