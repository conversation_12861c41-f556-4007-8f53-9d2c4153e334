<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.nb.dao.impl.NbContractMasterDaoImpl">
	<!-- <sql id="NB_nbContractMasterWhereCondition"> <if test=" operator_user_code 
		!= null and operator_user_code != '' "><![CDATA[ AND A.OPERATOR_USER_CODE 
		= #{operator_user_code} ]]></if> <if test=" channel_id != null "><![CDATA[ 
		AND A.CHANNEL_ID = #{channel_id} ]]></if> <if test=" pa_user_code != null 
		and pa_user_code != '' "><![CDATA[ AND A.PA_USER_CODE = #{pa_user_code} ]]></if> 
		<if test=" next_ayer_name != null and next_ayer_name != '' "><![CDATA[ AND 
		A.NEXT_AYER_NAME = #{next_ayer_name} ]]></if> <if test=" proposal_status 
		!= null "><![CDATA[ AND A.PROPOSAL_STATUS = #{proposal_status} ]]></if> <if 
		test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE 
		= #{apply_code} ]]></if> <if test=" organ_code != null and organ_code != 
		'' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if> <if test=" channel_type 
		!= null and channel_type != '' "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} 
		]]></if> <if test=" overdue_time != null and overdue_time != '' "><![CDATA[ 
		AND A.OVERDUE_TIME = #{overdue_time} ]]></if> <if test=" sale_agent_name 
		!= null and sale_agent_name != '' "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} 
		]]></if> <if test=" insured_family != null "><![CDATA[ AND A.INSURED_FAMILY 
		= #{insured_family} ]]></if> <if test=" service_handler_name != null and 
		service_handler_name != '' "><![CDATA[ AND A.SERVICE_HANDLER_NAME = #{service_handler_name} 
		]]></if> <if test=" next_pay_bank_account != null and next_pay_bank_account 
		!= '' "><![CDATA[ AND A.NEXT_PAY_BANK_ACCOUNT = #{next_pay_bank_account} 
		]]></if> <if test=" policy_id != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} 
		]]></if> <if test=" scan_complete_time != null and scan_complete_time != 
		'' "><![CDATA[ AND A.SCAN_COMPLETE_TIME = #{scan_complete_time} ]]></if> 
		<if test=" campaign_code != null and campaign_code != '' "><![CDATA[ AND 
		A.CAMPAIGN_CODE = #{campaign_code} ]]></if> <if test=" advance_prem_indi 
		!= null "><![CDATA[ AND A.ADVANCE_PREM_INDI = #{advance_prem_indi} ]]></if> 
		<if test=" initial_pay_mode != null and initial_pay_mode != '' "><![CDATA[ 
		AND A.INITIAL_PAY_MODE = #{initial_pay_mode} ]]></if> <if test=" channel_org_code 
		!= null "><![CDATA[ AND A.CHANNEL_ORG_CODE = #{channel_org_code} ]]></if> 
		<if test=" policy_type != null and policy_type != '' "><![CDATA[ AND A.POLICY_TYPE 
		= #{policy_type} ]]></if> <if test=" expiry_date != null and expiry_date 
		!= '' "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if> <if test=" 
		submit_channel != null and submit_channel != '' "><![CDATA[ AND A.SUBMIT_CHANNEL 
		= #{submit_channel} ]]></if> <if test=" liability_state != null "><![CDATA[ 
		AND A.LIABILITY_STATE = #{liability_state} ]]></if> <if test=" policy_code 
		!= null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} 
		]]></if> <if test=" sale_agent_code != null and sale_agent_code != '' "><![CDATA[ 
		AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if> <if test=" validate_date 
		!= null and validate_date != '' "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} 
		]]></if> <if test=" e_policy_flag != null "><![CDATA[ AND A.E_POLICY_FLAG 
		= #{e_policy_flag} ]]></if> <if test=" update_timestamp != null "><![CDATA[ 
		AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if> <if test=" money_code 
		!= null and money_code != '' "><![CDATA[ AND A.MONEY_CODE = #{money_code} 
		]]></if> <if test=" service_handler_code != null and service_handler_code 
		!= '' "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if> 
		<if test=" apply_date != null and apply_date != '' "><![CDATA[ AND A.APPLY_DATE 
		= #{apply_date} ]]></if> <if test=" branch_organ_code != null and branch_organ_code 
		!= '' "><![CDATA[ AND A.BRANCH_ORGAN_CODE = #{branch_organ_code} ]]></if> 
		<if test=" insert_timestamp != null and insert_timestamp != '' "><![CDATA[ 
		AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if> <if test=" manual_uw_indi 
		!= null "><![CDATA[ AND A.MANUAL_UW_INDI = #{manual_uw_indi} ]]></if> <if 
		test=" next_pay_mode != null and next_pay_mode != '' "><![CDATA[ AND A.NEXT_PAY_MODE 
		= #{next_pay_mode} ]]></if> <if test=" submission_date != null and submission_date 
		!= '' "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if> <if 
		test=" uw_user_code != null and uw_user_code != '' "><![CDATA[ AND A.UW_USER_CODE 
		= #{uw_user_code} ]]></if> <if test=" service_handler != null and service_handler 
		!= '' "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if> <if 
		test=" e_service_flag != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} 
		]]></if> <if test=" uw_complete_time != null and uw_complete_time != '' "><![CDATA[ 
		AND A.UW_COMPLETE_TIME = #{uw_complete_time} ]]></if> <if test=" risk_indi 
		!= null "><![CDATA[ AND A.RISK_INDI = #{risk_indi} ]]></if> <if test=" service_bank_branch 
		!= null and service_bank_branch != '' "><![CDATA[ AND A.SERVICE_BANK_BRANCH 
		= #{service_bank_branch} ]]></if> <if test=" issue_user_code != null and 
		issue_user_code != '' "><![CDATA[ AND A.ISSUE_USER_CODE = #{issue_user_code} 
		]]></if> <if test=" initial_pay_bank_code != null and initial_pay_bank_code 
		!= '' "><![CDATA[ AND A.INITIAL_PAY_BANK_CODE = #{initial_pay_bank_code} 
		]]></if> <if test=" issue_date != null and issue_date != '' "><![CDATA[ AND 
		A.ISSUE_DATE = #{issue_date} ]]></if> <if test=" initial_payer_name != null 
		and initial_payer_name != '' "><![CDATA[ AND A.INITIAL_PAYER_NAME = #{initial_payer_name} 
		]]></if> <if test=" high_sa_indi != null "><![CDATA[ AND A.HIGH_SA_INDI = 
		#{high_sa_indi} ]]></if> <if test=" agent_org_id != null and agent_org_id 
		!= '' "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if> <if test=" 
		bill_checked != null "><![CDATA[ AND A.BILL_CHECKED = #{bill_checked} ]]></if> 
		<if test=" service_bank != null and service_bank != '' "><![CDATA[ AND A.SERVICE_BANK 
		= #{service_bank} ]]></if> <if test=" pa_complete_time != null and pa_complete_time 
		!= '' "><![CDATA[ AND A.PA_COMPLETE_TIME = #{pa_complete_time} ]]></if> <if 
		test=" initial_pay_bank_account != null and initial_pay_bank_account != '' 
		"><![CDATA[ AND A.INITIAL_PAY_BANK_ACCOUNT = #{initial_pay_bank_account} 
		]]></if> <if test=" birthday_pol_indi != null "><![CDATA[ AND A.BIRTHDAY_POL_INDI 
		= #{birthday_pol_indi} ]]></if> <if test=" lang_code != null and lang_code 
		!= '' "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if> <if test=" next_pay_bank_code 
		!= null and next_pay_bank_code != '' "><![CDATA[ AND A.NEXT_PAY_BANK_CODE 
		= #{next_pay_bank_code} ]]></if> <if test=" scan_user_code != null and scan_user_code 
		!= '' "><![CDATA[ AND A.SCAN_USER_CODE = #{scan_user_code} ]]></if> </sql> -->

	<!-- 按索引生成的查询条件 -->
	<sql id="NB_queryByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="NB_queryByExpiryDateCondition">
		<if test=" expiry_date  != null "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
	</sql>
	<sql id="NB_queryByOrganCodeCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>
	<sql id="NB_queryByIssueDateCondition">
		<if test=" issue_date  != null "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
	</sql>
	<sql id="NB_queryByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="NB_queryByServiceBankBranchCondition">
		<if test=" service_bank_branch != null and service_bank_branch != '' "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
	</sql>
	<sql id="NB_queryByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<!-- 核保完成时间 -->
	<sql id="NB_queryByUwPompleteTimeCondition">
		<if test=" uw_complete_time != null and uw_complete_time != '' "><![CDATA[ AND to_char(A.UW_COMPLETE_TIME,'yyyy-MM-dd HH:mm:ss') = to_char(#{uw_complete_time},'yyyy-MM-dd HH:mm:ss') ]]></if>
	</sql>
	<!-- 核保完成查询开始时间 -->
	<sql id="NB_queryByUwStartTimeCondition">
		<if test=" start_date != null and start_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME >= #{start_date} ]]></if>
	</sql>
	<!-- 核保完成查询结束时间 -->
	<sql id="NB_queryByUwEndTimeCondition">
		<if test=" end_date != null and end_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME <= #{end_date} ]]></if>
	</sql>
	<!-- 递交渠道 -->
	<sql id="NB_queryBySubmitChannelCondition">
		<if test=" submit_channel != null and submit_channel != '' "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
	</sql>
	<!-- 保单效力状态 -->
	<sql id="NB_queryByLiabilityStateCondition">
		<if test=" liability_state != null and liability_state != '' "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
	</sql>
	<!-- 保单核保决定 -->
	<sql id="NB_queryByDecisionCodeCondition">
		<if test=" decision_code != null and decision_code != '' "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
	</sql>
	<!-- 投保单状态 -->
	<sql id="NB_queryByProposalStatusCondition">
		<if test=" proposal_status != null and proposal_status != '' "><![CDATA[ AND A.PROPOSAL_STATUS = #{proposal_status} ]]></if>
	</sql>
	<!-- 签单日期 -->
	<sql id="NB_queryBySignDateCondition">
		<if test=" signDateS != null and signDateS != '' "><![CDATA[AND A.ISSUE_DATE >= #{signDateS} ]]></if>
		<if	test=" signDateE != null and signDateE != '' "><![CDATA[AND A.ISSUE_DATE <= #{signDateE} ]]></if>
	</sql>
	<!-- 投保单状态 -->
	<sql id="NB_queryByBankAgencyFlagCondition">
		<if test=" proposal_status != null and proposal_status != '' "><![CDATA[ AND A.BANK_AGENCY_FLAG = #{bank_agency_flag} ]]></if>
	</sql>
	<sql id="NB_queryByDeliverCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) = #{channel_type} ]]></if>
		<if test=" start_date != null and start_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME>= #{start_date} ]]></if>
		<if test=" end_date != null and end_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME<= #{end_date} ]]></if>
		<!-- <if test=" start_date != null and start_date != '' "><![CDATA[ AND 
			A.UW_COMPLETE_TIME>= to_date(#{start_date},'YYYY-MM-DD') ]]></if> -->
		<!-- <if test=" end_date != null and end_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME<= 
			to_date(#{end_date},'YYYY-MM-DD') ]]></if> -->

	</sql>
	<sql id="NB_queryByTransactionNo">
		<if test=" transaction_no != null and transaction_no != '' "><![CDATA[ AND A.TRANSACTION_NO = #{transaction_no} ]]></if>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="NB_queryByDeliverConditions">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')  ]]></if>
		<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) = #{channel_type} ]]></if>
		<if test=" start_date != null and start_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME>= #{start_date} ]]></if>
		<if test=" end_date != null and end_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME<= #{end_date} ]]></if>
	</sql>
	<!-- 删除投保单信息条件 -->
	<sql id="NB_deletePolicyInfo">
		<if test=" policy_id != null and policy_id != '' "><![CDATA[  POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="NB_findRelationPolicyCodeCondition">
		<if test=" customer_id != null and customer_id != '' "><![CDATA[ AND H.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" product_code != null and product_code != '' "><![CDATA[ AND P.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" apply_date != null and apply_date != '' "><![CDATA[ AND M.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" product_category2 != null and product_category2 != '' "><![CDATA[ AND B.PRODUCT_CATEGORY2 = #{product_category2} ]]></if>
		<if test=" submit_channel != null and submit_channel != '' "><![CDATA[ AND M.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
	</sql>

	<!-- 增加天利 万能相关字段 -->
	<!-- 添加操作 -->
	<insert id="NB_addNbContractMaster" useGeneratedKeys="true"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="policy_id"> SELECT DEV_NB.S_T_NB_CONTRACT_MASTER.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_NB.T_NB_CONTRACT_MASTER       (
				OPERATOR_USER_CODE, CHANNEL_ID, PA_USER_CODE, PROPOSAL_STATUS, APPLY_CODE, ORGAN_CODE, 
				CHANNEL_TYPE, OVERDUE_TIME, SALE_AGENT_NAME, UPDATE_BY, INSURED_FAMILY, SERVICE_HANDLER_NAME, 
				POLICY_ID, SCAN_COMPLETE_TIME, 
				UPDATE_TIME, POLICY_TYPE, EXPIRY_DATE, SUBMIT_CHANNEL, LIABILITY_STATE, POLICY_CODE, SALE_AGENT_CODE, 
				VALIDATE_DATE, UPDATE_TIMESTAMP, INSERT_BY, MONEY_CODE, SERVICE_HANDLER_CODE, APPLY_DATE, 
				BRANCH_ORGAN_CODE, INSERT_TIMESTAMP, MANUAL_UW_INDI, SUBMISSION_DATE, UW_USER_CODE, SERVICE_HANDLER, 
				E_SERVICE_FLAG, UW_COMPLETE_TIME, RISK_INDI, SERVICE_BANK_BRANCH, ISSUE_USER_CODE, INSERT_TIME, 
				ISSUE_DATE, HIGH_SA_INDI, AGENT_ORG_ID, BILL_CHECKED, SERVICE_BANK, PA_COMPLETE_TIME, 
				BIRTHDAY_POL_INDI, LANG_CODE, SCAN_USER_CODE,DECISION_CODE,MEDIA_TYPE,APL_PERMIT,POLICY_PWD,AGENCY_CODE,INPUT_DATE,CALL_TIME_LIST,DIALECT_INDI,WINNING_START_FLAG,
				TRANSACTION_NO,INPUT_TYPE,OLD_POLICY_CODE,APPOINT_VALIDATE,INITIAL_PREM_DATE,DRQ_FLAG,CONFIRM_WAY,POLICY_RETURN_MODE,SUBINPUT_TYPE,RELATION_POLICY_CODE,TRANSFER_FLAG,PRINT_SPECIAL_FLAG) 
			VALUES (
				#{operator_user_code, jdbcType=VARCHAR}, #{channel_id, jdbcType=VARCHAR} , #{pa_user_code, jdbcType=VARCHAR}, #{proposal_status, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} 
				, #{channel_type, jdbcType=VARCHAR} , #{overdue_time, jdbcType=DATE} , #{sale_agent_name, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{insured_family, jdbcType=NUMERIC} , #{service_handler_name, jdbcType=VARCHAR} 
				, #{policy_id, jdbcType=NUMERIC} , #{scan_complete_time, jdbcType=DATE}  
				, SYSDATE , #{policy_type, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} , #{submit_channel, jdbcType=NUMERIC} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{sale_agent_code, jdbcType=VARCHAR} 
				, #{validate_date, jdbcType=DATE}, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{money_code, jdbcType=VARCHAR} , #{service_handler_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} 
				, #{branch_organ_code, jdbcType=VARCHAR} , SYSDATE , #{manual_uw_indi, jdbcType=NUMERIC}, #{submission_date, jdbcType=DATE} , #{uw_user_code, jdbcType=VARCHAR} , #{service_handler, jdbcType=VARCHAR} 
				, #{e_service_flag, jdbcType=NUMERIC} , #{uw_complete_time, jdbcType=TIMESTAMP} , #{risk_indi, jdbcType=NUMERIC} , #{service_bank_branch, jdbcType=VARCHAR} , #{issue_user_code, jdbcType=VARCHAR} , SYSDATE 
				, #{issue_date, jdbcType=TIMESTAMP}, #{high_sa_indi, jdbcType=NUMERIC} , #{agent_org_id, jdbcType=VARCHAR} , #{bill_checked, jdbcType=NUMERIC} , #{service_bank, jdbcType=VARCHAR} , #{pa_complete_time, jdbcType=DATE}  
				, #{birthday_pol_indi, jdbcType=NUMERIC} , #{lang_code, jdbcType=VARCHAR}, #{scan_user_code, jdbcType=VARCHAR}, #{decision_code, jdbcType=VARCHAR},#{media_type, jdbcType=NUMERIC}, #{apl_permit, jdbcType=NUMERIC}, #{policy_pwd, jdbcType=VARCHAR}
				, #{agency_code, jdbcType=VARCHAR}, #{input_date, jdbcType=DATE}, #{call_time_list, jdbcType=VARCHAR}, #{dialect_indi, jdbcType=NUMERIC}, #{winning_start_flag, jdbcType=NUMERIC}, #{transaction_no, jdbcType=VARCHAR}, #{input_type, jdbcType=VARCHAR}
				, #{old_policy_code, jdbcType=VARCHAR}, #{appoint_validate, jdbcType=DATE},#{initial_prem_date, jdbcType=DATE},#{drq_flag, jdbcType=NUMERIC},#{confirm_way, jdbcType=VARCHAR}, #{policy_return_mode, jdbcType=NUMERIC},#{subinput_type, jdbcType=VARCHAR}
				,#{relation_policy_code, jdbcType=VARCHAR}, #{transfer_flag, jdbcType=NUMERIC} ,#{print_special_flag, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="NB_deleteNbContractMaster" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_CONTRACT_MASTER        WHERE POLICY_ID          = #{policy_id         } ]]>
	</delete>

	<select id="NB_findMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, A.WINNING_START_FLAG,
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE,
			A.TRANSACTION_NO,A.INPUT_TYPE 
			FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  
			and A.POLICY_CODE = #{policy_code         }]]>
		<!-- <include refid="NB_queryByPolicyCodeCondition" /> -->
		<![CDATA[ ORDER BY A.POLICY_CODE          ]]>
	</select>

	<!-- 批量修改保单的状态 肖落落 -->
	<update id="NB_updateAllPolicyStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				<if test=" proposal_status  != null "><![CDATA[ PROPOSAL_STATUS = #{proposal_status,jdbcType=VARCHAR} ,]]></if>
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_CODE          = #{policy_code         } ]]>
	</update>
	
	<!-- 修改保单的双录标记 -->
	<update id="NB_updateContractMasterDrqFlag" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				<if test=" drq_flag  != null "><![CDATA[ DRQ_FLAG = #{drq_flag,jdbcType=NUMERIC} ,]]></if>
			</trim>
		</set>
		<![CDATA[ WHERE APPLY_CODE          = #{apply_code         } ]]>
	</update>

	<!-- 修改操作,录入时 -->
	<update id="NB_updateNbContractMaster" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				CHANNEL_TYPE = #{channel_type,
				jdbcType=VARCHAR} ,
				APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
				APPLY_DATE = #{apply_date, jdbcType=DATE} ,
				SUBMISSION_DATE =
				#{submission_date, jdbcType=DATE} ,
				SALE_AGENT_NAME =
				#{sale_agent_name, jdbcType=VARCHAR} ,
				SALE_AGENT_CODE =
				#{sale_agent_code, jdbcType=VARCHAR} ,
				ORGAN_CODE = #{organ_code,
				jdbcType=VARCHAR} ,
				PA_USER_CODE = #{pa_user_code, jdbcType=VARCHAR},
				AGENT_ORG_ID = #{agent_org_id, jdbcType=VARCHAR} ,
				CHANNEL_ID   = #{channel_id, jdbcType=VARCHAR},
				SERVICE_BANK = #{service_bank,
				jdbcType=VARCHAR} ,
				SERVICE_BANK_BRANCH = #{service_bank_branch,
				jdbcType=VARCHAR} ,
				SERVICE_HANDLER = #{service_handler,
				jdbcType=VARCHAR} ,
				SERVICE_HANDLER_CODE = #{service_handler_code,
				jdbcType=VARCHAR} ,
				HIGH_SA_INDI = #{high_sa_indi, jdbcType=NUMERIC},
				BIRTHDAY_POL_INDI = #{birthday_pol_indi, jdbcType=NUMERIC} ,
				MANUAL_UW_INDI = #{manual_uw_indi, jdbcType=NUMERIC} ,
				RISK_INDI =#{risk_indi, jdbcType=NUMERIC} ,
				E_SERVICE_FLAG = #{e_service_flag,
				jdbcType=NUMERIC} ,
				UPDATE_TIME = SYSDATE ,
				POLICY_CODE =
				#{policy_code,jdbcType=VARCHAR},
				ISSUE_DATE =
				#{issue_date,jdbcType=TIMESTAMP},
				VALIDATE_DATE =
				#{validate_date,jdbcType=DATE},
				EXPIRY_DATE =
				#{expiry_date,jdbcType=DATE},
				LIABILITY_STATE =
				#{liability_state,jdbcType=NUMERIC},
				PROPOSAL_STATUS =
				#{proposal_status,jdbcType=VARCHAR},
				DECISION_CODE = #{decision_code,
				jdbcType=VARCHAR},
				UW_COMPLETE_TIME = #{uw_complete_time,
				jdbcType=TIMESTAMP},
				MEDIA_TYPE = #{media_type, jdbcType=VARCHAR},
				CALL_TIME_LIST = #{call_time_list, jdbcType=VARCHAR},
				DIALECT_INDI = #{dialect_indi, jdbcType=NUMERIC},
				WINNING_START_FLAG = #{winning_start_flag, jdbcType=NUMERIC},
				BANK_AGENCY_FLAG = #{bank_agency_flag, jdbcType=NUMERIC},
				RELATION_POLICY_CODE = #{relation_policy_code, jdbcType=VARCHAR},
				TRANSFER_FLAG = #{transfer_flag, jdbcType=NUMERIC},
				PRINT_SPECIAL_FLAG = #{print_special_flag, jdbcType=NUMERIC},
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID          = #{policy_id         } ]]>
	</update>
	<!-- 修改操作 ，添加缴费信息时 -->
	<update id="NB_updateNbContractMasterAll" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				NEXT_PAY_BANK_ACCOUNT = #{next_pay_bank_account, jdbcType=VARCHAR} ,
				NEXT_AYER_NAME = #{next_ayer_name, jdbcType=VARCHAR} ,
				POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
				INITIAL_PAY_MODE = #{initial_pay_mode, jdbcType=VARCHAR} ,
				NEXT_PAY_MODE = #{next_pay_mode, jdbcType=VARCHAR} ,
				INITIAL_PAY_BANK_CODE = #{initial_pay_bank_code, jdbcType=VARCHAR} ,
				INITIAL_PAYER_NAME = #{initial_payer_name, jdbcType=VARCHAR} ,
				INITIAL_PAY_BANK_ACCOUNT = #{initial_pay_bank_account,
				jdbcType=VARCHAR} ,
				NEXT_PAY_BANK_CODE = #{next_pay_bank_code, jdbcType=VARCHAR} ,

			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID          = #{policy_id         } ]]>
	</update>
	<!-- 修改操作,修改投保单状态 -->
	<update id="NB_updateNbContractMasterPolicyStatus"
		parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER  A      ]]>
		<set>
			<trim suffixOverrides=",">
				<if test=" proposal_status  != null and proposal_status  != '' "><![CDATA[ PROPOSAL_STATUS = #{proposal_status,jdbcType=VARCHAR} ,]]></if>
				<if test=" liability_state  != null and liability_state  != '' "><![CDATA[ LIABILITY_STATE = #{liability_state,jdbcType=NUMERIC} ,]]></if>
				<if test=" overdue_time  != null and overdue_time  != '' "><![CDATA[ OVERDUE_TIME = #{overdue_time,jdbcType=TIMESTAMP} ,]]></if>
				<if test=" initial_prem_date  != null and initial_prem_date  != '' "><![CDATA[ INITIAL_PREM_DATE = #{initial_prem_date,jdbcType=TIMESTAMP} ,]]></if>
				<if test=" relation_policy_code  != null and relation_policy_code  != '' "><![CDATA[ RELATION_POLICY_CODE = #{relation_policy_code,jdbcType=VARCHAR} ,]]></if>
			</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByApplyCodeCondition" />
	</update>
	<!-- 修改操作 -->
	<update id="NB_updateContractMaster" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				OPERATOR_USER_CODE = #{operator_user_code,
				jdbcType=VARCHAR} ,
				CHANNEL_ID = #{channel_id, jdbcType=VARCHAR} ,
				PA_USER_CODE = #{pa_user_code, jdbcType=VARCHAR} ,
				PROPOSAL_STATUS =
				#{proposal_status, jdbcType=VARCHAR} ,
				APPLY_CODE = #{apply_code,
				jdbcType=VARCHAR} ,
				ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
				CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
				OVERDUE_TIME =
				#{overdue_time, jdbcType=DATE} ,
				SALE_AGENT_NAME = #{sale_agent_name,
				jdbcType=VARCHAR} ,
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				INSURED_FAMILY = #{insured_family, jdbcType=NUMERIC} ,
				SERVICE_HANDLER_NAME = #{service_handler_name, jdbcType=VARCHAR} ,
				POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
				SCAN_COMPLETE_TIME =
				#{scan_complete_time, jdbcType=DATE} ,				
				UPDATE_TIME = SYSDATE ,
				POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
				EXPIRY_DATE =
				#{expiry_date, jdbcType=DATE} ,
				SUBMIT_CHANNEL = #{submit_channel,
				jdbcType=NUMERIC} ,
				LIABILITY_STATE = #{liability_state,
				jdbcType=NUMERIC} ,
				POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
				SALE_AGENT_CODE = #{sale_agent_code, jdbcType=VARCHAR} ,
				VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
				UPDATE_TIMESTAMP =
				#{update_timestamp, jdbcType=NUMERIC} ,
				MONEY_CODE = #{money_code,
				jdbcType=VARCHAR} ,
				SERVICE_HANDLER_CODE = #{service_handler_code,
				jdbcType=VARCHAR} ,
				APPLY_DATE = #{apply_date, jdbcType=DATE} ,
				BRANCH_ORGAN_CODE = #{branch_organ_code, jdbcType=VARCHAR} ,
				INSERT_TIMESTAMP = #{insert_timestamp, jdbcType=VARCHAR} ,
				MANUAL_UW_INDI = #{manual_uw_indi, jdbcType=NUMERIC} ,
				SUBMISSION_DATE = #{submission_date, jdbcType=DATE} ,
				UW_USER_CODE =
				#{uw_user_code, jdbcType=VARCHAR} ,
				SERVICE_HANDLER =
				#{service_handler, jdbcType=VARCHAR} ,
				E_SERVICE_FLAG =
				#{e_service_flag, jdbcType=NUMERIC} ,
				UW_COMPLETE_TIME =
				#{uw_complete_time, jdbcType=TIMESTAMP} ,
				RISK_INDI = #{risk_indi,
				jdbcType=NUMERIC} ,
				SERVICE_BANK_BRANCH = #{service_bank_branch,
				jdbcType=VARCHAR} ,
				ISSUE_USER_CODE = #{issue_user_code,
				jdbcType=VARCHAR} ,
				ISSUE_DATE = #{issue_date, jdbcType=TIMESTAMP} ,
				HIGH_SA_INDI = #{high_sa_indi, jdbcType=NUMERIC} ,
				AGENT_ORG_ID =
				#{agent_org_id, jdbcType=VARCHAR} ,
				BILL_CHECKED = #{bill_checked,
				jdbcType=NUMERIC} ,
				SERVICE_BANK = #{service_bank, jdbcType=VARCHAR},
				PA_COMPLETE_TIME = #{pa_complete_time, jdbcType=DATE} ,
				BIRTHDAY_POL_INDI = #{birthday_pol_indi, jdbcType=NUMERIC} ,
				LANG_CODE = #{lang_code, jdbcType=VARCHAR} ,
				SCAN_USER_CODE =
				#{scan_user_code, jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID          = #{policy_id         } ]]>
	</update>
	<update id="NB_updateContractAgentPolicyCode" parameterType="java.util.Map">
	
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_AGENT  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</update>
	<update id="NB_updateNbPolicyConditionPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_POLICY_CONDITION  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByPolicyIdCondition" /> -->
	</update>
	
	
	<update id="NB_updateNbContractPolicyCodeBypolicyId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER  A      ]]>
		<set>
			<trim suffixOverrides=",">
				RELATION_POLICY_CODE = #{relation_policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE APPLY_CODE = #{apply_code} ]]>
		<!-- <include refid="NB_queryByPolicyIdCondition" /> -->
	</update>
	
	
	<update id="NB_updateNbPolicyHolderPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_POLICY_HOLDER  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</update>
	<update id="NB_updateNbInsuredListPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_INSURED_LIST  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</update>
	<update id="NB_updateNbContractBenePolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_BENE  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByPolicyIdCondition" /> -->
	</update>
	<update id="NB_updateNbBenefitInsuredPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_BENEFIT_INSURED  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByPolicyIdCondition" /> -->
	</update>
	<update id="NB_updateNbPayerPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_PAYER  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByPolicyIdCondition" /> -->
	</update>
	<update id="NB_updateNbPayerAccountPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_PAYER_ACCOUNT  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByPolicyIdCondition" /> -->
	</update>
	<update id="NB_updateCustomerSurveyPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_CUSTOMER_SURVEY  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</update>
	<update id="NB_updateNbContractInvestRatePolicyCode"
		parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_INVEST_RATE  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</update>

	<update id="NB_updateNbContractProductPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_PRODUCT  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</update>


	<update id="NB_updateProposalProcessPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_PROPOSAL_PROCESS  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</update>

	<update id="NB_updateNbContractBusiProdPolicyCode"
		parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_BUSI_PROD  A      ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</update>
	<!-- 按索引查询操作 -->
	<select id="NB_findMasterByPolicyId_jicheng" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME,A.CHANNEL_ORG_CODE, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>

	<select id="NB_findQryMasterByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.OPERATOR_USER_CODE,LTRIM(RTRIM(A.DECISION_CODE)) AS DECISION_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
      LTRIM(RTRIM(A.CHANNEL_TYPE)) AS CHANNEL_TYPE , A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
      A.POLICY_ID, A.SCAN_COMPLETE_TIME,
      A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
      A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
      A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
      A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, A.WINNING_START_FLAG,
      A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, A.DIALECT_INDI,
      A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,B.SALES_CHANNEL_NAME AS TYPE_NAME,A.INPUT_DATE,A.CALL_TIME_LIST,A.APPOINT_VALIDATE ,A.BANK_AGENCY_FLAG,A.OLD_POLICY_CODE from DEV_NB.t_NB_CONTRACT_MASTER  A ,DEV_NB.t_Sales_Channel B WHERE 1 = 1 
      and A.channel_type=B.sales_channel_code  ]]>
		<include refid="NB_queryByDeliverConditions" />
		<![CDATA[ ORDER BY A.APPLY_CODE          ]]>
	</select>

	<select id="NB_findMasterByApplyCodes_jicheng" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.OPERATOR_USER_CODE,LTRIM(RTRIM(A.DECISION_CODE)) AS DECISION_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
      LTRIM(RTRIM(A.CHANNEL_TYPE)) AS CHANNEL_TYPE , A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
      A.POLICY_ID, A.SCAN_COMPLETE_TIME,A.INITIAL_PREM_DATE, 
      A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
      A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
      A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
      A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, A.RELATION_POLICY_CODE,A.TRANSFER_FLAG,
      A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, A.DIALECT_INDI,A.WINNING_START_FLAG,
      A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,B.SALES_CHANNEL_NAME AS TYPE_NAME,A.INPUT_DATE,A.CALL_TIME_LIST,A.APPOINT_VALIDATE,A.INSERT_BY,A.BANK_AGENCY_FLAG,A.OLD_POLICY_CODE,INPUT_TYPE,SUBINPUT_TYPE  from DEV_NB.T_NB_CONTRACT_MASTER  A ,DEV_NB.T_Sales_Channel B WHERE 1 = 1 
      and A.channel_type=B.sales_channel_code and rownum =1  ]]>
		<include refid="NB_queryByDeliverCondition" />
		<![CDATA[ ORDER BY A.APPLY_CODE          ]]>
	</select>
	
	<select id="NB_findMasterByTransactionNo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.OPERATOR_USER_CODE,LTRIM(RTRIM(A.DECISION_CODE)) AS DECISION_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
      LTRIM(RTRIM(A.CHANNEL_TYPE)) AS CHANNEL_TYPE , A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
      A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
      A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
      A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
      A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
      A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
      A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, A.DIALECT_INDI,A.WINNING_START_FLAG,
      A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,B.SALES_CHANNEL_NAME AS TYPE_NAME,A.INPUT_DATE,A.CALL_TIME_LIST,A.APPOINT_VALIDATE,A.INSERT_BY from DEV_NB.T_NB_CONTRACT_MASTER  A ,DEV_NB.T_Sales_Channel B WHERE 1 = 1 
      and A.channel_type=B.sales_channel_code  ]]>
		<include refid="NB_queryByTransactionNo" />
	</select>

	<select id="NB_findMasterProductByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		select tm.organ_code,
		       tm.channel_type,
		       tm.service_bank,
		       tm.SERVICE_BANK_BRANCH,
		       tm.apply_date,
		       tca.agent_code as sale_agent_code,tm.policy_id
		  from DEV_NB.T_nb_contract_master tm, DEV_NB.T_NB_CONTRACT_AGENT tca
		 where tca.policy_id = tm.policy_id
		 	   and  tm.POLICY_ID = #{policy_id}
		 ]]>
		<![CDATA[  ORDER BY tm.policy_id          ]]>
	</select>

	<select id="NB_findByExpiryDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME,
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByExpiryDateCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>

	<select id="NB_findByOrganCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByOrganCodeCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>

	<select id="NB_findByIssueDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByIssueDateCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>

	<select id="NB_findByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>

	<select id="NB_findByServiceBankBranch" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByServiceBankBranchCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>


	<!-- 按map查询操作 -->
	<select id="NB_findAllMapNbContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="NB_findAllNbContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="NB_findNbContractMasterTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="NB_queryNbContractMasterForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPERATOR_USER_CODE, B.CHANNEL_ID, B.PA_USER_CODE, B.PROPOSAL_STATUS, B.APPLY_CODE, B.ORGAN_CODE, 
			B.CHANNEL_TYPE, B.OVERDUE_TIME, B.SALE_AGENT_NAME, B.INSURED_FAMILY, B.SERVICE_HANDLER_NAME, 
			B.POLICY_ID, B.SCAN_COMPLETE_TIME, 
			B.POLICY_TYPE, B.EXPIRY_DATE, B.SUBMIT_CHANNEL, B.LIABILITY_STATE, B.POLICY_CODE, B.SALE_AGENT_CODE, 
			B.VALIDATE_DATE, B.MONEY_CODE, B.SERVICE_HANDLER_CODE, B.APPLY_DATE, 
			B.BRANCH_ORGAN_CODE, B.MANUAL_UW_INDI, B.SUBMISSION_DATE, B.UW_USER_CODE, B.SERVICE_HANDLER, 
			B.E_SERVICE_FLAG, B.UW_COMPLETE_TIME, B.RISK_INDI, B.SERVICE_BANK_BRANCH, B.ISSUE_USER_CODE, 
			B.ISSUE_DATE, B.HIGH_SA_INDI, B.AGENT_ORG_ID, B.BILL_CHECKED, B.SERVICE_BANK, B.PA_COMPLETE_TIME, 
			B.BIRTHDAY_POL_INDI, B.LANG_CODE, B.SCAN_USER_CODE,B.MEDIA_TYPE,B.APL_PERMIT,B.POLICY_PWD,B.AGENCY_CODE FROM (
					SELECT ROWNUM RN, A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME,
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_ID          ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

	<select id="NB_findConcelPolicyByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  
           			select 
                 CC.APPLY_CODE,
                 CC.ORGAN_CODE,
                 CC.PROPOSAL_STATUS,
                 CC.BANK_CODE,
                 CC.FINISH_TIME,
                 CC.BANK_ACCOUNT,
                 CC.HOLDER_NAME,
                 CC.POLICY_ID,
                 CC.FEE_AMOUNT,
                 CC.PAY_MODE,
                 CC.CONCLUSION_CODE,
                 CC.customer_name,
                 CC.PAYEE_NAME,
                   CC.Image_Scan_Status,
                   CC.customer_id 

 from (select A.APPLY_CODE,
                 aa.ORGAN_CODE,
                 aa.PROPOSAL_STATUS,
                 dd.BANK_CODE,
                 dd.FINISH_TIME,
                 dd.BANK_ACCOUNT,
                 dd.HOLDER_NAME,
                 aa.POLICY_ID,
                 dd.FEE_AMOUNT,
                 dd.PAY_MODE,
                 A.CONCLUSION_CODE,
                 A.Insert_Time,
                 c.customer_name,
                 dd.PAYEE_NAME,
                    D.Image_Scan_Status,
                    c.customer_id 
            from DEV_NB.T_pre_audit_master A
            left join (select p.APPLY_CODE,
                              p.ORGAN_CODE,
                              p.PROPOSAL_STATUS,
                              p.POLICY_ID
                         from DEV_NB.T_nb_contract_master p) aa
              on A.apply_code = aa.APPLY_CODE
            left join (    SELECT  aaa.apply_code, max(aaa.bank_code) as bank_code, max(bbb.BANK_ACCOUNT) as BANK_ACCOUNT,
                                           max(aaa.holder_name) as holder_name,
                                           max(aaa.pay_mode) as pay_mode,
                                             max(aaa.finish_time) as finish_time,
                                        max(aaa.payee_name) as payee_name,
                                            sum(aaa.fee_amount) as fee_amount
                                  FROM DEV_NB.T_nb_Prem_Arap aaa,DEV_NB.T_Prem_Arap bbb
                                  WHERE bbb.FEE_STATUS = '01' and aaa.busi_prod_code=bbb.busi_prod_code and aaa.fee_status='00'
                                  and aaa.apply_code=bbb.apply_code and bbb.APPLY_CODE =#{apply_code} 
                                  group by aaa.apply_code
                        ) dd
              on aa.apply_code = dd.apply_code
            left join DEV_NB.T_NB_POLICY_HOLDER B
              ON aa.POLICY_ID = B.POLICY_ID
            LEFT JOIN DEV_NB.T_CUSTOMER C
              ON B.CUSTOMER_ID = C.CUSTOMER_ID
              left join DEV_NB.T_image_scan D on A.Apply_Code= D.Buss_Code
            WHERE 1 = 1 AND A.APPLY_CODE =#{apply_code}
            order by A.Insert_Time desc ) CC where rownum = 1
           			]]>
	</select>


	<!-- E保通专用 涉及出单前撤保功能 查询 -->
	<select id="NB_findConcelEpolicyByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  
           		select CC.APPLY_CODE,
				       CC.ORGAN_CODE,
				       CC.PROPOSAL_STATUS,
				       CC.BANK_CODE,
				       CC.FINISH_TIME,
				       CC.BANK_ACCOUNT,
				       CC.HOLDER_NAME,
				       CC.POLICY_ID,
				       CC.FEE_AMOUNT,
				       CC.PAY_MODE,
				       CC.customer_name,
				       CC.PAYEE_NAME,
				       CC.Image_Scan_Status,
				       CC.customer_id 
			  from (select aa.APPLY_CODE,
			               aa.ORGAN_CODE,
			               aa.PROPOSAL_STATUS,
			               dd.BANK_CODE,
			               dd.FINISH_TIME,
			               dd.BANK_ACCOUNT,
			               dd.HOLDER_NAME,
			               aa.POLICY_ID,
			               dd.FEE_AMOUNT,
			               dd.PAY_MODE,
			               c.customer_name,
			               dd.PAYEE_NAME,
			               D.Image_Scan_Status,
			               c.customer_id 
			          from DEV_NB.T_NB_CONTRACT_MASTER aa
			          left join (SELECT aaa.apply_code,
                           max(aaa.bank_code) as bank_code,
                           max(bbb.BANK_ACCOUNT) as BANK_ACCOUNT,
                           max(aaa.holder_name) as holder_name,
                           max(aaa.pay_mode) as pay_mode,
                           max(aaa.finish_time) as finish_time,
                           max(aaa.payee_name) as payee_name,
                           sum(aaa.fee_amount) as fee_amount
                      FROM DEV_NB.T_NB_PREM_ARAP aaa, DEV_NB.T_Prem_Arap bbb
                     WHERE bbb.FEE_STATUS = '01'
                       and aaa.busi_prod_code = bbb.busi_prod_code
                       and aaa.fee_status = '00'
                       and aaa.apply_code = bbb.apply_code
                       and bbb.APPLY_CODE = #{apply_code}
                     group by aaa.apply_code) dd
				            on aa.apply_code = dd.apply_code
				          left join DEV_NB.T_NB_POLICY_HOLDER B
				            ON aa.POLICY_ID = B.POLICY_ID
				          LEFT JOIN DEV_NB.T_CUSTOMER C
				            ON B.CUSTOMER_ID = C.CUSTOMER_ID
				          left join DEV_NB.T_IMAGE_SCAN D
				            on aa.Apply_Code = D.Buss_Code
				         WHERE 1 = 1
				           AND aa.APPLY_CODE = #{apply_code}
				         order by aa.Insert_Time desc) CC
				 where rownum = 1
           			]]>
	</select>


	<!-- 更改通知书状态 -->
	<update id="NB_updateProposalStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				PROPOSAL_STATUS = #{proposal_status,
				jdbcType=VARCHAR} ,
				LIABILITY_STATE = #{liability_state,
				jdbcType=NUMERIC},
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				UPDATE_TIME = SYSDATE ,
				UPDATE_TIMESTAMP = SYSDATE ,
			</trim>
		</set>
		<![CDATA[ WHERE APPLY_CODE = #{apply_code} ]]>
	</update>
	<!-- 更改核保完成时间 zhuhaixin -->
	<update id="NB_update_uw_complete_time" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				decision_code=#{decision_code,jdbcType=VARCHAR},
				uw_user_code = #{uw_user_code,jdbcType=NUMERIC},
				uw_complete_time=#{uw_complete_time,jdbcType=TIMESTAMP},
				UPDATE_BY =
				#{update_by, jdbcType=NUMERIC} ,
				UPDATE_TIME = SYSDATE ,
				UPDATE_TIMESTAMP = SYSDATE ,
			</trim>
		</set>
		<![CDATA[ WHERE APPLY_CODE = #{apply_code} ]]>
	</update>
	<!-- 更改投保单主表高额件标识 -->
	<update id="NB_updateContracterMasterHighSaIndi" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				high_sa_indi = #{high_sa_indi, jdbcType=NUMERIC},
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
	</update>



	<!-- 保存保单核保决定 -->
	<update id="NB_updateMasterUWResult" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER set decision_code=#{decision_code},
		uw_user_code = #{uw_user_code},uw_complete_time=#{uw_complete_time},proposal_status=#{proposal_status},
		 UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,UPDATE_TIME = SYSDATE ,]]>
		<if test="appoint_validate != null and appoint_validate != '' "><![CDATA[   APPOINT_VALIDATE = #{appoint_validate},]]></if>
		<![CDATA[  UPDATE_TIMESTAMP = SYSDATE  WHERE POLICY_ID = #{policy_id} ]]>
	</update>
	<!-- add by xiaoluoluo -->
	<select id="NB_findMasterStatusByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME,
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE,A.MEDIA_TYPE,B.STATUS_DESC,A.APPOINT_VALIDATE,A.INPUT_TYPE,A.WINNING_START_FLAG,A.SUBINPUT_TYPE
			FROM DEV_NB.T_NB_CONTRACT_MASTER A,DEV_NB.T_PROPOSAL_STATUS B
			WHERE 1 = 1 
			AND A.PROPOSAL_STATUS=B.PROPOSAL_STATUS]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByPolicyCodeCondition" />
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE          ]]>
	</select>
	<!-- add by yangdd -->
	<select id="NB_findContactMasterByCondition" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE,B.STATUS_DESC,A.APPOINT_VALIDATE
			FROM DEV_NB.T_NB_CONTRACT_MASTER A,DEV_NB.T_PROPOSAL_STATUS B
			WHERE 1 = 1 
			AND A.PROPOSAL_STATUS=B.PROPOSAL_STATUS]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByApplyCodeCondition" />
		<include refid="NB_queryByPolicyCodeCondition" />
		<include refid="NB_queryByUwPompleteTimeCondition" />
		<include refid="NB_queryBySubmitChannelCondition" />
		<include refid="NB_queryByLiabilityStateCondition" />
		<include refid="NB_queryByDecisionCodeCondition" />
		<include refid="NB_queryByProposalStatusCondition" />
		<include refid="NB_queryByUwStartTimeCondition" />
		<include refid="NB_queryByUwEndTimeCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE          ]]>
	</select>

	<!-- 公司自主制单打印分页查询 -->
	<select id="NB_findCompanyPrintPolicyTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
	    <![CDATA[SELECT COUNT(1) FROM DEV_NB.T_NB_CONTRACT_MASTER N 
	             RIGHT JOIN DEV_NB.T_POLICY_PRINT J ON J.POLICY_CODE=N.POLICY_CODE 
                 LEFT JOIN DEV_NB.T_ISSUE_OPERATION I ON N.POLICY_ID=I.POLICY_ID
                 LEFT JOIN DEV_PAS.T_AGENT A ON A.AGENT_CODE=N.SALE_AGENT_CODE
                 LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER P ON N.POLICY_ID=P.POLICY_ID
                 LEFT JOIN DEV_NB.T_CUSTOMER C ON P.CUSTOMER_ID=C.CUSTOMER_ID WHERE 1=1 and j.print_type=0 and j.print_status='2'
                 AND I.INSERT_TIME=(SELECT max(INSERT_TIME) from DEV_NB.T_ISSUE_OPERATION where POLICY_ID=I.POLICY_ID)]]>
		<include refid="companyPrintPolicyCondition" />
	</select>

	<select id="NB_queryCompanyPrintPolicyForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
	    <![CDATA[SELECT B.RN AS ROWNUMBER,B.ORGAN_CODE,B.POLICY_CODE,B.APPLY_CODE,B.POLICY_ID,B.POLICY_TYPE,B.ISSUE_DATE,B.SALE_AGENT_CODE,B.CHANNEL_ID FROM
              (SELECT ROWNUM RN,N.ORGAN_CODE,J.POLICY_CODE,j.APPLY_CODE,N.POLICY_ID,N.POLICY_TYPE,N.ISSUE_DATE,N.SALE_AGENT_CODE,J.PRINT_ID AS CHANNEL_ID
               FROM DEV_NB.T_NB_CONTRACT_MASTER N 
               RIGHT JOIN DEV_NB.T_POLICY_PRINT J ON J.POLICY_CODE=N.POLICY_CODE            
               LEFT JOIN DEV_PAS.T_AGENT A ON A.AGENT_CODE=N.SALE_AGENT_CODE
               LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER P ON N.POLICY_ID=P.POLICY_ID
               LEFT JOIN DEV_NB.T_CUSTOMER C ON P.CUSTOMER_ID=C.CUSTOMER_ID WHERE ROWNUM <= #{LESS_NUM} and j.print_type=0 and j.print_status='2']]>
		<include refid="companyPrintPolicyCondition" />
               <![CDATA[ORDER BY N.POLICY_TYPE,N.ORGAN_CODE,C.CUSTOMER_LEVEL ) B WHERE B.RN > #{GREATER_NUM}]]>
	</select>

	<!-- 公司自主制单打印查询条件 -->
	<sql id="companyPrintPolicyCondition">
		<if test="apply_code_list != null  ">
			<![CDATA[ AND J.apply_code in ]]>
			<foreach item="apply_code" collection="apply_code_list" open="("
				separator="," close=")">
				<![CDATA[ '${apply_code}' ]]>
			</foreach>
		</if>
	</sql>

	<!-- <sql id="companyPrintPolicyCondition"> <if test=" organ_code != null 
		and organ_code != '' "><![CDATA[ AND N.ORGAN_CODE = #{organ_code} ]]></if> 
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND J.POLICY_CODE 
		= #{policy_code} ]]></if> <if test=" apply_code != null and apply_code != 
		'' "><![CDATA[ AND N.APPLY_CODE = #{apply_code} ]]></if> <if test=" sale_agent_code 
		!= null and sale_agent_code != '' "><![CDATA[ AND N.SALE_AGENT_CODE = #{sale_agent_code} 
		]]></if> <if test=" agent_star_flag != null and agent_star_flag != '' "><![CDATA[ 
		AND A.AGENT_STAR_FLAG = #{agent_star_flag} ]]></if> <if test=" customer_level 
		!= null and customer_level != '' "><![CDATA[ AND C.CUSTOMER_LEVEL = #{customer_level} 
		]]></if> <if test=" operation_time_st != null and operation_time_st != '' 
		"><![CDATA[ AND I.OPERATION_TIME>= #{operation_time_st} ]]></if> <if test=" 
		operation_time_end != null and operation_time_end != '' "><![CDATA[ AND I.OPERATION_TIME<=#{operation_time_end} 
		]]></if> </sql> -->


	<!-- 事后质检寻找符合规则的投保单 -->
	<select id="NB_findQatAsk" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select m.policy_id,
       m.insured_family,
       m.apply_code,
       m.decision_code,
       m.apply_date,
       m.policy_type,
       m.branch_organ_code,
       m.organ_code,
       m.validate_date,
       m.expiry_date,
       m.liability_state,
       m.money_code,
       m.submission_date,
       m.submit_channel,
       m.channel_type,
       m.lang_code,
       m.service_bank,
       m.service_bank_branch,
       m.service_handler,
       m.service_handler_code,
       m.service_handler_name,
       m.bill_checked,
       m.sale_agent_code,
       m.sale_agent_name,
       m.high_sa_indi,
       m.birthday_pol_indi,
       m.risk_indi,
       m.agent_org_id,
       m.manual_uw_indi,
       m.media_type,
       m.e_service_flag,
       m.channel_id,
       m.policy_code,
       m.operator_user_code,
       m.proposal_status,
       m.pa_user_code,
       m.pa_complete_time,
       m.scan_user_code,
       m.scan_complete_time,
       m.uw_user_code,
       m.uw_complete_time,
       m.issue_user_code,
       m.issue_date,
       m.overdue_time,
       m.agency_code,
       m.policy_pwd,
       m.apl_permit,
       m.event_code,
       m.insert_by,
       m.insert_time,
       m.insert_timestamp,
       m.update_by,
       m.update_time,
       m.update_timestamp,
       m.input_date,
       m.input_type,
       m.subinput_type,
       m.sale_type,
       m.call_time_list,
       m.winning_start_flag,
       m.appoint_validate,
       m.dialect_indi,
       m.transaction_no,
       m.bank_agency_flag,
       m.sale_com_code,
       m.initial_prem_date,
       m.agent_level,
       m.customer_level,
       m.qa_type,
       m.card_code
  from (select m.policy_id,
               m.insured_family,
               m.apply_code,
               m.decision_code,
               m.apply_date,
               m.policy_type,
               m.branch_organ_code,
               m.organ_code,
               m.validate_date,
               m.expiry_date,
               m.liability_state,
               m.money_code,
               m.submission_date,
               m.submit_channel,
               m.channel_type,
               m.lang_code,
               m.service_bank,
               m.service_bank_branch,
               m.service_handler,
               m.service_handler_code,
               m.service_handler_name,
               m.bill_checked,
               m.sale_agent_code,
               m.sale_agent_name,
               m.high_sa_indi,
               m.birthday_pol_indi,
               m.risk_indi,
               m.agent_org_id,
               m.manual_uw_indi,
               m.media_type,
               m.e_service_flag,
               m.channel_id,
               m.policy_code,
               m.operator_user_code,
               m.proposal_status,
               m.pa_user_code,
               m.pa_complete_time,
               m.scan_user_code,
               m.scan_complete_time,
               m.uw_user_code,
               m.uw_complete_time,
               m.issue_user_code,
               m.issue_date,
               m.overdue_time,
               m.agency_code,
               m.policy_pwd,
               m.apl_permit,
               m.event_code,
               m.insert_by,
               m.insert_time,
               m.insert_timestamp,
               m.update_by,
               m.update_time,
               m.update_timestamp,
               m.input_date,
               m.input_type,
               m.subinput_type,
               m.sale_type,
               m.call_time_list,
               m.winning_start_flag,
               m.appoint_validate,
               m.dialect_indi,
               m.transaction_no,
               m.bank_agency_flag,
               m.sale_com_code,
               m.initial_prem_date,
               m.agent_level,
               m.customer_level,
               (case
                 when prem.apply_code is not null then
                  '3'
                 when doc.apply_code is not null then
                  '2'
                 else
                  '1'
               end) as qa_type, -- 1 标准件 2 问题件 3 退费件
               ib.card_code
          from (select m.policy_id,
                       m.insured_family,
                       m.apply_code,
                       m.decision_code,
                       m.apply_date,
                       m.policy_type,
                       m.branch_organ_code,
                       m.organ_code,
                       m.validate_date,
                       m.expiry_date,
                       m.liability_state,
                       m.money_code,
                       m.submission_date,
                       m.submit_channel,
                       m.channel_type,
                       m.lang_code,
                       m.service_bank,
                       m.service_bank_branch,
                       m.service_handler,
                       m.service_handler_code,
                       m.service_handler_name,
                       m.bill_checked,
                       m.sale_agent_code,
                       m.sale_agent_name,
                       m.high_sa_indi,
                       m.birthday_pol_indi,
                       m.risk_indi,
                       m.agent_org_id,
                       m.manual_uw_indi,
                       m.media_type,
                       m.e_service_flag,
                       m.channel_id,
                       m.policy_code,
                       m.operator_user_code,
                       m.proposal_status,
                       m.pa_user_code,
                       m.pa_complete_time,
                       m.scan_user_code,
                       m.scan_complete_time,
                       m.uw_user_code,
                       m.uw_complete_time,
                       m.issue_user_code,
                       m.issue_date,
                       m.overdue_time,
                       m.agency_code,
                       m.policy_pwd,
                       m.apl_permit,
                       m.event_code,
                       m.insert_by,
                       m.insert_time,
                       m.insert_timestamp,
                       m.update_by,
                       m.update_time,
                       m.update_timestamp,
                       m.input_date,
                       m.input_type,
                       m.subinput_type,
                       m.sale_type,
                       m.call_time_list,
                       m.winning_start_flag,
                       m.appoint_validate,
                       m.dialect_indi,
                       m.transaction_no,
                       m.bank_agency_flag,
                       m.sale_com_code,
                       m.initial_prem_date,
                       agents.agent_level,
                       c.customer_level
                  from DEV_NB.T_nb_contract_master m,
                       DEV_NB.T_nb_policy_holder   h,
                       DEV_NB.T_NB_CONTRACT_AGENT     aa,
                       DEV_PAS.T_AGENT              agents,
                       DEV_NB.T_CUSTOMER           c
                 where m.policy_id = h.policy_id
                   and m.policy_id = aa.policy_id
                   and m.submit_channel !='9'
                   and agents.agent_code = aa.agent_code
                   and c.customer_id = h.customer_id
                   and m.validate_date is not null) m
          left join (select apply_code
                      from DEV_NB.T_PREM_ARAP t
                     where t.fee_type = 'P001050000' --溢缴退费
                     group by t.apply_code) prem
            on m.apply_code = prem.apply_code
          left join (select b.apply_code
                      from DEV_NB.T_issue_list b
                     where b.is_valid = '0' --问题件
                     group by b.apply_code) doc
            on m.apply_code = doc.apply_code 
            left join (select ims.buss_code,b.card_code
                         from DEV_NB.T_Image_Scan ims, DEV_NB.T_Billcard b
                         where b.card_code = ims.billcard_code
                         and b.card_code in ('UA006','UA011','UA001','UA024','UA022','EUA001')
                         and b.card_type = '0001') ib 
                     on m.apply_code = ib.buss_code 
            ) m
 where 1 = 1 
		 AND BRANCH_ORGAN_CODE like concat(#{organ_code},'%')
		 AND ISSUE_DATE >= to_date(#{start_dt1},'yyyy-mm-dd hh24:mi:ss') AND ISSUE_DATE <= to_date(#{end_dt2},'yyyy-mm-dd hh24:mi:ss')
 ]]>
 		<if test="entry_type != null and entry_type != ''"> AND SUBMIT_CHANNEL = #{entry_type} </if>
		<if test="policy_type != null and policy_type != ''"> AND POLICY_TYPE = #{policy_type} </if>
		<if test="channel_type != null and channel_type != ''"> AND CHANNEL_TYPE = #{channel_type}</if>
		<if test="bank_code != null and bank_code != ''"> AND SERVICE_BANK = #{bank_code}</if>
		<if test="bank_branch_code != null and bank_branch_code!= ''"> AND SERVICE_BANK_BRANCH = #{bank_branch_code}</if>
		<if test="qa_type != null and qa_type != ''"> AND QA_TYPE = #{qa_type}</if>
		<if test="customer_level != null and customer_level != ''"> AND customer_level = #{customer_level}</if>
		<if test="agent_level != null and agent_level != ''"> AND agent_level = #{agent_level}</if>
		<if test="card_code != null and card_code != ''"> AND card_code = #{card_code}</if>
		<if test="product_code != null and product_code != ''">
			and exists (select 1
            from DEV_NB.T_nb_contract_busi_prod pr,dev_nb.t_nb_contract_master m
           where pr.apply_code = m.apply_code
             and pr.product_code = #{product_code} )
		</if>
		and m.policy_id not in (
          <if test="plan_level != null and plan_level == 0 "> 
		          select policy_id
		          from DEV_NB.T_nb_qt_criteria_result
		         where policy_id = m.policy_id
          </if>
          <if test="plan_level != null and plan_level == 1 "> 
		          select policy_id
          from (select cr.policy_id from dev_nb.T_NB_QT_CRITERIA_RESULT cr where cr.plan_level ='0' and cr.status ='1'
                union 
                 select cr.policy_id from dev_nb.T_NB_QT_CRITERIA_RESULT cr where cr.plan_level ='1') qcr
         where qcr.policy_id = m.policy_id
          </if>
         )
         and m.policy_code is not null
	</select>

	<!-- 根据agentCode或者service_bank_branch查询applyCode -->
	<select id="NB_findMasterByServiceBankBranch" resultType="java.util.Map"
		parameterType="java.util.Map">
	    <![CDATA[select  A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE, A.DECISION_CODE,MEDIA_TYPE,APL_PERMIT,POLICY_PWD,AGENCY_CODE,A.APPOINT_VALIDATE
			from DEV_NB.T_NB_CONTRACT_MASTER A where service_bank_branch=#{service_bank_branch} 
	    or apply_code =(select apply_code from DEV_NB.T_NB_CONTRACT_AGENT where agent_code =#{service_bank_branch})
	    ]]>
              
               <![CDATA[ORDER BY N.POLICY_ID ) B WHERE B.RN > #{GREATER_NUM}]]>
	</select>

	<!-- 事后质检根据保单号查询出相关的信息 -->
	<select id="NB_findQatAskByPolicy_id" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select distinct *
  from (select m.policy_id,
               m.insured_family,
               m.apply_code,
               m.decision_code,
               m.apply_date,
               m.policy_type,
               m.branch_organ_code,
               m.organ_code,
               m.validate_date,
               m.expiry_date,
               m.liability_state,
               m.money_code,
               m.submission_date,
               m.submit_channel,
               m.channel_type,
               m.lang_code,
               m.service_bank,
               m.service_bank_branch,
               m.service_handler,
               m.service_handler_code,
               m.service_handler_name,
               m.bill_checked,
               m.sale_agent_code,
               m.sale_agent_name,
               m.high_sa_indi,
               m.birthday_pol_indi,
               m.risk_indi,
               m.agent_org_id,
               m.manual_uw_indi,
               m.media_type,
               m.e_service_flag,
               m.channel_id,
               m.policy_code,
               m.operator_user_code,
               m.proposal_status,
               m.pa_user_code,
               m.pa_complete_time,
               m.scan_user_code,
               m.scan_complete_time,
               m.uw_user_code,
               m.uw_complete_time,
               m.issue_user_code,
               m.issue_date,
               m.overdue_time,
               m.agency_code,
               m.policy_pwd,
               m.apl_permit,
               m.event_code,
               m.insert_by,
               m.insert_time,
               m.insert_timestamp,
               m.update_by,
               m.update_time,
               m.update_timestamp,
               m.input_date,
               m.input_type,
               m.subinput_type,
               m.sale_type,
               m.call_time_list,
               m.winning_start_flag,
               m.appoint_validate,
               m.dialect_indi,
               m.transaction_no,
               m.bank_agency_flag,
               m.sale_com_code,
               m.initial_prem_date,
               m.agent_level,
               m.customer_level,
               (case
                 when prem.apply_code is not null then
                  '3'
                 when doc.apply_code is not null then
                  '2'
                 else
                  '1'
               end) as qa_type, -- 1 标准件 2 问题件 3 退费件
               ib.card_code
          from (select m.policy_id,
                       m.insured_family,
                       m.apply_code,
                       m.decision_code,
                       m.apply_date,
                       m.policy_type,
                       m.branch_organ_code,
                       m.organ_code,
                       m.validate_date,
                       m.expiry_date,
                       m.liability_state,
                       m.money_code,
                       m.submission_date,
                       m.submit_channel,
                       m.channel_type,
                       m.lang_code,
                       m.service_bank,
                       m.service_bank_branch,
                       m.service_handler,
                       m.service_handler_code,
                       m.service_handler_name,
                       m.bill_checked,
                       m.sale_agent_code,
                       m.sale_agent_name,
                       m.high_sa_indi,
                       m.birthday_pol_indi,
                       m.risk_indi,
                       m.agent_org_id,
                       m.manual_uw_indi,
                       m.media_type,
                       m.e_service_flag,
                       m.channel_id,
                       m.policy_code,
                       m.operator_user_code,
                       m.proposal_status,
                       m.pa_user_code,
                       m.pa_complete_time,
                       m.scan_user_code,
                       m.scan_complete_time,
                       m.uw_user_code,
                       m.uw_complete_time,
                       m.issue_user_code,
                       m.issue_date,
                       m.overdue_time,
                       m.agency_code,
                       m.policy_pwd,
                       m.apl_permit,
                       m.event_code,
                       m.insert_by,
                       m.insert_time,
                       m.insert_timestamp,
                       m.update_by,
                       m.update_time,
                       m.update_timestamp,
                       m.input_date,
                       m.input_type,
                       m.subinput_type,
                       m.sale_type,
                       m.call_time_list,
                       m.winning_start_flag,
                       m.appoint_validate,
                       m.dialect_indi,
                       m.transaction_no,
                       m.bank_agency_flag,
                       m.sale_com_code,
                       m.initial_prem_date,
                       agents.agent_level,
                       c.customer_level
                  from DEV_NB.T_nb_contract_master m,
                       DEV_NB.T_nb_policy_holder   h,
                       DEV_NB.T_NB_CONTRACT_AGENT     aa,
                       DEV_PAS.T_AGENT              agents,
                       DEV_NB.T_CUSTOMER           c
                 where m.policy_id = h.policy_id
                   and m.policy_id = aa.policy_id
                   and agents.agent_code = aa.agent_code
                   and c.customer_id = h.customer_id
                   and m.VALIDATE_DATE is not null) m
          left join (select apply_code
                      from DEV_NB.T_PREM_ARAP t
                     where t.fee_type = 'P001050000' --溢缴退费
                     group by t.apply_code) prem
            on m.apply_code = prem.apply_code
          left join (select b.apply_code
                      from DEV_NB.T_issue_list b
                     where b.is_valid = '0' --问题件
                     group by b.apply_code) doc
            on m.apply_code = doc.apply_code 
            left join (select ims.buss_code,b.card_code
                         from DEV_NB.T_Image_Scan ims, DEV_NB.T_Billcard b
                         where b.card_code = ims.billcard_code
                         and b.card_type = '0001') ib 
                     on m.apply_code = ib.buss_code    
            ) bbc where 1=1 
     	 and policy_id=#{policy_id}  ]]>
		<if test="product_code != null and product_code != ''">
			and exists (select policy_id from DEV_NB.T_nb_contract_busi_prod pr
			where pr.apply_code = bbc.apply_code and pr.product_code = #{product_code}
			)
		</if>
	</select>



	<select id="NB_findQatAskCustomerByapply_code" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		
			select hh.customer_id,nc.apply_code
                  from DEV_NB.T_nb_policy_holder hh,DEV_NB.T_nb_contract_master nc
                 	where hh.policy_id = nc.policy_id and nc.apply_code=#{apply_code}
		
	  ]]>


	</select>

	<select id="NB_findQatAskTyperByApply_code" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		
			select (case
         when (select count(1)
                 from DEV_NB.T_cash a
                where a.apply_code = nc.apply_code
                  and a.fee_status = '01'
                  and a.fee_type = 12) > 0 then
          '3'
         when (select count(1)
                 from DEV_NB.T_document b
                where b.buss_code = nc.apply_code
                  and b.status = 5
                  and b.template_code in ('templatecode')) > 0 then
          '2'
         else
          '1'
       end) qa_type ,nc.policy_id,
       nc.insured_family,
       nc.apply_code,
       nc.decision_code,
       nc.apply_date,
       nc.policy_type,
       nc.branch_organ_code,
       nc.organ_code,
       nc.validate_date,
       nc.expiry_date,
       nc.liability_state,
       nc.money_code,
       nc.submission_date,
       nc.submit_channel,
       nc.channel_type,
       nc.lang_code,
       nc.service_bank,
       nc.service_bank_branch,
       nc.service_handler,
       nc.service_handler_code,
       nc.service_handler_name,
       nc.bill_checked,
       nc.sale_agent_code,
       nc.sale_agent_name,
       nc.high_sa_indi,
       nc.birthday_pol_indi,
       nc.risk_indi,
       nc.agent_org_id,
       nc.manual_uw_indi,
       nc.media_type,
       nc.e_service_flag,
       nc.channel_id,
       nc.policy_code,
       nc.operator_user_code,
       nc.proposal_status,
       nc.pa_user_code,
       nc.pa_complete_time,
       nc.scan_user_code,
       nc.scan_complete_time,
       nc.uw_user_code,
       nc.uw_complete_time,
       nc.issue_user_code,
       nc.issue_date,
       nc.overdue_time,
       nc.agency_code,
       nc.policy_pwd,
       nc.apl_permit,
       nc.event_code,
       nc.insert_by,
       nc.insert_time,
       nc.insert_timestamp,
       nc.update_by,
       nc.update_time,
       nc.update_timestamp,
       nc.input_date,
       nc.input_type,
       nc.subinput_type,
       nc.sale_type,
       nc.call_time_list,
       nc.winning_start_flag,
       nc.appoint_validate,
       nc.dialect_indi,
       nc.transaction_no,
       nc.bank_agency_flag,
       nc.sale_com_code,
       nc.initial_prem_date  
  	from DEV_NB.T_nb_contract_master nc  WHERE 1 = 1 and nc.apply_code=#{apply_code}
		
	  ]]>

	</select>

	<!-- customerId 查询 customer信息 -->
	<select id="NB_findCustomerInfoById" resultType="java.util.Map"
		parameterType="java.util.Map">
	    <![CDATA[  select CUSTOMER_ID, UN_CUSTOMER_CODE, MARRIAGE_DATE, EDUCATION, CUSTOMER_NAME, CUSTOMER_BIRTHDAY, CUSTOMER_GENDER, CUSTOMER_HEIGHT, CUSTOMER_WEIGHT, CUSTOMER_CERT_TYPE, CUSTOMER_CERTI_CODE, CUSTOMER_ID_CODE, CUST_CERT_STAR_DATE, CUST_CERT_END_DATE, JOB_CODE, JOB_NATURE, JOB_KIND, JOB_TITLE, MARRIAGE_STATUS, IS_PARENT, ANNUAL_INCOME, COUNTRY_CODE, RELIGION_CODE, NATION_CODE, DRIVER_LICENSE_TYPE, COMPANY_NAME, OFFEN_USE_TEL, HOUSE_TEL, FAX_TEL, OFFICE_TEL, MOBILE_TEL, EMAIL, QQ, WECHAT_NO, OTHER, CUSTOMER_LEVEL, CUSTOMER_VIP, SMOKING_FLAG, DRUNK_FLAG, BLACKLIST_FLAG, HOUSEKEEPER_FLAG, SYN_MDM_FLAG, LIVE_STATUS, RETIRED_FLAG, DEATH_DATE, HEALTH_STATUS, REMARK, CUST_PWD, INSERT_BY, INSERT_TIME, INSERT_TIMESTAMP, UPDATE_BY, UPDATE_TIME, UPDATE_TIMESTAMP, OLD_CUSTOMER_ID, CUSTOMER_RISK_LEVEL from DEV_NB.T_CUSTOMER where 1=1 and CUSTOMER_ID = #{customer_id} ]]>
	</select>
	<!-- 新单取消 修改投保单状态 xiaoll_wb -->
	<update id="NB_updateProposalStatusByApplyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				<if test=" proposal_status  != null "><![CDATA[ PROPOSAL_STATUS = #{proposal_status,jdbcType=VARCHAR} ,]]></if>
			</trim>
		</set>
		<![CDATA[ WHERE APPLY_CODE          = #{apply_code         } ]]>
	</update>

	<!-- 删除保单与代理人关系 -->
	<delete id="NB_deleteContractAgentInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_CONTRACT_AGENT WHERE POLICY_ID = #{policy_id} ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除问卷答案信息 -->
	<delete id="NB_deleteCustomerSurveyDetailInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_CUSTOMER_SURVEY_DETAIL where  survey_id = (select SURVEY_ID from DEV_NB.T_CUSTOMER_SURVEY where POLICY_ID = #{policy_id})  ]]>
	</delete>
	<!-- 删除问卷信息 -->
	<delete id="NB_deleteCustomerSurveyInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_CUSTOMER_SURVEY WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除告知信息参数表 -->
	<delete id="NB_deleteBankPolicyQuestionaireCustomerParam" parameterType="java.util.Map">
		<![CDATA[delete DEV_NB.T_questionaire_customer_param where customer_SURVEY_ID in (select customer_SURVEY_ID from DEV_NB.T_questionaire_customer where POLICY_ID = #{policy_id})  ]]>
	</delete>
	<!-- 删除告知信息表 -->
	<delete id="NB_deleteBankPolicyQuestionaireCustomer" parameterType="java.util.Map">
		<![CDATA[delete DEV_NB.T_QUESTIONAIRE_CUSTOMER WHERE POLICY_ID = #{policy_id}   ]]>
	</delete>
	<!-- 删除投保人信息 -->
	<delete id="NB_deleteNbPolicyHolderInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_POLICY_HOLDER WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除险种被保人关系 -->
	<delete id="NB_deleteNbBenefitInsuredInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_BENEFIT_INSURED WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除被保人信息 -->
	<delete id="NB_deleteNbInsuredListInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_INSURED_LIST WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除受益人信息 -->
	<delete id="NB_deleteNbContractBeneInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_CONTRACT_BENE WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除缴费账户信 -->
	<delete id="NB_deleteNbPayerAccountInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_PAYER_ACCOUNT WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除保单缴费人信息 -->
	<delete id="NB_deleteNbPayerInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_PAYER WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除投资账户信息额度 -->
	<delete id="NB_deleteNbContractInvestRateInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_CONTRACT_INVEST_RATE WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除保单责任层信息 -->
	<delete id="NB_deleteNbContractProductInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_CONTRACT_PRODUCT WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除投保单状态轨迹 -->
	<delete id="NB_deleteProposalProcessInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_PROPOSAL_PROCESS WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除投保单险种信息 -->
	<delete id="NB_deleteNbContractBusiProdInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_CONTRACT_BUSI_PROD WHERE POLICY_ID = #{policy_id}  ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 删除投保单主表信 -->
	<delete id="NB_deleteTNbContractMasterInfo" parameterType="java.util.Map">
		<![CDATA[ delete DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = #{ policy_id} ]]>
		<!-- <include refid="NB_deletePolicyInfo" /> -->
	</delete>
	<!-- 根据投保单号查询主表保单号 -->
	<select id="NB_findNbContractMasterByApplyCodeSignData" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUBMIT_CHANNEL,A.POLICY_CODE,A.UW_COMPLETE_TIME,A.issue_date,A.APPOINT_VALIDATE,A.Apply_Date,a.proposal_status,a.apply_code,A.MEDIA_TYPE,a.policy_id from DEV_NB.T_NB_CONTRACT_MASTER A 
 			WHERE 1 = 1  AND A.APPLY_CODE = #{apply_code} ]]>
		<!-- <include refid="NB_queryByApplyCodeCondition" /> -->
	</select>
	<sql id="NB_batchUpdateByApplyCodeCondition">
	
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND APPLY_CODE = #{apply_code} ]]></if>
	</sql>

	<!-- 签单修改时，更改投保单状态 -->
	<update id="NB_batchUpdateMasterInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				<if test=" proposal_status  != null "><![CDATA[ PROPOSAL_STATUS = #{proposal_status,jdbcType=VARCHAR} ,]]></if>
			</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="NB_batchUpdateByApplyCodeCondition" />
	</update>

	<!-- 签单处理时，更改投保单逾期时间 -->
	<update id="NB_updateOverdueTimeByApplyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				<if test=" overdue_time  != null "><![CDATA[ OVERDUE_TIME = #{overdue_time,jdbcType=TIMESTAMP} ,]]></if>
			</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="NB_batchUpdateByApplyCodeCondition" />
	</update>
	
	<!--by zhaoyoan_wb 根据投保单号查询投保状态信息 -->
	<select id="NB_findInsuredStatusInfoByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.POLICY_ID,A.PROPOSAL_STATUS, 
			(SELECT STATUS_DESC FROM DEV_NB.T_PROPOSAL_STATUS WHERE A.PROPOSAL_STATUS=PROPOSAL_STATUS) AS status_desc 
			FROM DEV_NB.T_NB_CONTRACT_MASTER A 
 			WHERE 1 = 1  AND A.APPLY_CODE = #{apply_code} 
 		]]>
	</select>
	
	<!--回执信息查询（电子签名）  -->
	<sql id="NB_findAllReceiptInformationByAgentCode">
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ AND TA.AGENT_CODE = #{agent_code} ]]></if>
	</sql>	
	<select id="NB_findAllReceiptInformation" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			     SELECT TCM.POLICY_CODE,TA.AGENT_CODE,TA.AGENT_NAME,TCM.BANK_AGENCY_FLAG,TC.CUSTOMER_NAME,TCM.SUBMISSION_DATE,
		         TC.MOBILE_TEL,TPA.NEXT_ACCOUNT,
		         (SELECT TBPA.UNIT_NUMBER FROM DEV_NB.T_NB_PREM_ARAP  TBPA WHERE  TBPA.APPLY_CODE=TCM.APPLY_CODE AND TBPA.DERIV_TYPE='001' AND TBPA.BUSINESS_CODE=TCM.APPLY_CODE AND ROWNUM=1)UNIT_NUMBER,
		         TSO.BRANCH_ATTR AS CHANNEL_ORG_CODE
		         FROM DEV_NB.T_NB_CONTRACT_MASTER TCM,
		              DEV_NB.T_NB_CONTRACT_AGENT     TCA,
		              DEV_PAS.T_AGENT TA,
		              DEV_NB.T_SALES_ORGAN TSO,
		              DEV_NB.T_CUSTOMER TC,
		              DEV_NB.T_NB_PAYER_ACCOUNT TPA,
		              DEV_NB.T_NB_POLICY_HOLDER TPH
		        WHERE TCM.POLICY_CODE = TCA.POLICY_CODE 
		        AND TA.AGENT_CODE=TCA.AGENT_CODE
		        AND TPH.APPLY_CODE=TCM.APPLY_CODE
		        AND TC.CUSTOMER_ID=TPH.CUSTOMER_ID
		        AND TPA.Policy_Id=TCM.Policy_Id
		        AND TA.SALES_ORGAN_CODE=TSO.SALES_ORGAN_CODE
			    AND TCM.APPLY_CODE=#{pol_no}
 		]]>
 		<include refid="NB_findAllReceiptInformationByAgentCode" />
	</select>
	
	<!-- 根据投保单号获取保单险种类别信息 -->
	<select id="NB_findNbContractBusiInfoByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  
		      SELECT (SELECT TBP.COVER_PERIOD_TYPE FROM DEV_NB.T_BUSINESS_PRODUCT TBP WHERE TCBP.BUSI_PRD_ID=TBP.BUSINESS_PRD_ID )COVER_PERIOD_TYPE,
		             (SELECT TBP.PRODUCT_CATEGORY1 FROM DEV_NB.T_BUSINESS_PRODUCT TBP WHERE TCBP.BUSI_PRD_ID=TBP.BUSINESS_PRD_ID )PRODUCT_CATEGORY1,
		             (SELECT TBP.PRIMARY_SALES_CHANNEL FROM DEV_NB.T_BUSINESS_PRODUCT TBP WHERE TCBP.BUSI_PRD_ID=TBP.BUSINESS_PRD_ID )PRIMARY_SALES_CHANNEL,
		             TCBP.MASTER_BUSI_ITEM_ID FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TCBP 
		      WHERE  TCBP.APPLY_CODE=#{apply_code}
        ]]>
	</select>
	
	
	<select id="NB_findNbContractMasterApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  
			and A.APPLY_CODE = #{apply_code}  ORDER BY A.APPLY_CODE  ]]>
	</select>
	
	<select id="NB_findChannelId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT PARENT_CODE  FROM   DEV_NB.T_SALES_ORGAN WHERE SALES_ORGAN_CODE =#{sales_organ_code}
           ]]>
	</select>

	
	<!-- 短信承保，e保录入件数查询 -->
	 <select id="NB_findByEnterCount"  parameterType="java.util.Map" resultType="int">
     <choose>
	 <when test="epolicy_flag==null">
	 SELECT COUNT(*)   FROM DEV_NB.T_NB_CONTRACT_MASTER TNCM
     WHERE TNCM.SUBMIT_CHANNEL = '2'
     AND TO_CHAR(TNCM.INSERT_TIME, 'YYYY-MM-DD') =
     TO_CHAR(SYSDATE, 'YYYY-MM-DD') AND TNCM.PROPOSAL_STATUS IN ('10','13','14')
	 </when>
	 <otherwise>
	SELECT COUNT(*)
	FROM DEV_NB.T_NB_CONTRACT_MASTER TNCM
	WHERE TNCM.SUBMIT_CHANNEL = '2'
	AND TO_CHAR(TNCM.INSERT_TIME, 'YYYY-MM-DD') =
	TO_CHAR(SYSDATE, 'YYYY-MM-DD')
	 </otherwise>
	 </choose>
     </select>
	<!-- 纠错申请任务池人工发起 -->
	<select id="NB_findNbContractMasterOne" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE,
			B.STATUS_CODE, B.ERROR_SOURCE, B.CREATE_BY, B.CREATE_DATE
			FROM DEV_NB.T_NB_CONTRACT_MASTER A 
			LEFT JOIN DEV_NB.T_ERROR_CORRECT B ON B.POLICY_ID = A.POLICY_ID
			WHERE 1 = 1  ]]>
			<![CDATA[ AND A.PROPOSAL_STATUS IN('10','13','14','26','34','35','36','37','38','39','40','41')]]>
			<include refid="NB_queryByApplyCodeCondition" />
			<include refid="NB_queryByPolicyCodeCondition" />
			<include refid="NB_queryByOrganCodeCondition" />
			<include refid="NB_queryBySignDateCondition" />
			<include refid="NB_queryByPolicyIdCondition" />
			<![CDATA[ORDER BY A.ISSUE_DATE DESC]]>
	</select>
	<!-- PROPOSAL_STATUS判断是否已签单 -->
	<select id="NB_findNbContractMasterSignCheck" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE 
			FROM DEV_NB.T_NB_CONTRACT_MASTER A WHERE 1 = 1  ]]>
			<![CDATA[ AND A.PROPOSAL_STATUS IN('10','13','14','21','26','34','35','36','37','38','39','40','41')]]>
			<include refid="NB_queryByApplyCodeCondition" />
			<include refid="NB_queryByPolicyCodeCondition" />
			<!-- <include refid="NB_queryByOrganCodeCondition" /> -->
			<!-- <include refid="NB_queryBySignDateCondition" /> -->
			<include refid="NB_queryByPolicyIdCondition" />
	</select>	
	
	<!-- 投保单号快查 -->
	<select id="NB_findNbContractMasterApplyCodeExist" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_CONTRACT_MASTER  T WHERE T.APPLY_CODE = #{apply_code} ]]>
	</select>
	<!-- 业务员保单状态查询 -->
	<select id="NB_findAgentPolicyState" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT A.APPLY_CODE, A.PROPOSAL_STATUS, B.STEP_DESC
 			 FROM T_NB_CONTRACT_MASTER A
  				LEFT JOIN T_PROPOSAL_STEP B
    			ON A.PROPOSAL_STATUS = B.STEP_CODE
 				WHERE A.SALE_AGENT_CODE = #{sale_agent_code}
		]]>
	</select>
	<!--根据银行网点查询T_BANK_BRANCH的是否银代新政标志IS_BANK_LOAN-->
	<select id="NB_queryIsBankLoanByBankBranchCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_BANK_LOAN FROM DEV_NB.T_BANK_BRANCH     A WHERE 1 = 1  
			and A.BANK_BRANCH_CODE = #{service_bank_branch} ]]>
	</select>
	<!--根据BankBranchCode查询是否存在银代新政银行网点-->
	<select id="NB_queryByBankAgencyBranchCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select distinct C.BANK_BRANCH_CODE from (
	SELECT A.BANK_BRANCH_CODE FROM DEV_NB.T_BANK_AGENCY_BRANCH A 
	where  A.BANK_BRANCH_CODE = #{bank_branch_code}
	union 
	SELECT B.BANK_BRANCH_CODE FROM DEV_NB.T_BANK_BRANCH B 
    where  B.BANK_BRANCH_CODE = #{bank_branch_code}
    ) C ]]>
	</select>
	<update id="NB_updateBankAgencyFlagByPolicyId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER        ]]>
		<set>
			<trim suffixOverrides=",">
				BANK_AGENCY_FLAG=#{bank_agency_flag,jdbcType=NUMERIC},
				UPDATE_BY =#{update_by, jdbcType=NUMERIC} ,
				UPDATE_TIME = SYSDATE ,
				UPDATE_TIMESTAMP = SYSDATE ,
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
	</update>
	<!-- 查询540,541产品累积住院费用保额 -->
		<select id="NB_getCumulativeAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
	select sum(pp.amount) as uw_user_code
  from DEV_NB.T_nb_contract_product pp
 where  exists
       (select apply_code
          from DEV_NB.T_nb_insured_list il
         where pp.apply_code = il.apply_code and  exists
               (select t.customer_id
                  from DEV_NB.T_nb_insured_list t 
                 where il.customer_id = t.customer_id and exists 
                       (select bi.insured_id
                          from DEV_NB.T_nb_benefit_insured bi
                         where  t.list_id = bi.list_id and exists
                               (select cp.busi_item_id
                                  from DEV_NB.T_nb_contract_product cp
                                 where bi.busi_item_id = cp.busi_item_id and cp.apply_code = #{apply_code}
                                   and cp.product_code in ('540000', '541000')))))
   and pp.product_code in
       ('717000', '717001', '726000', '727000', '540000', '541000')
 		]]>
	</select>
	
	
	<!-- 保单号 和 核保结论 -->
	<select id="NB_FindpolicyandDecisionCodeByNbContractMaster" resultType="java.util.Map"
	 parameterType="java.util.Map">
		<![CDATA[ 
		SELECT DECISION_CODE, POLICY_CODE
		  FROM DEV_NB.T_NB_CONTRACT_MASTER
		 	WHERE DECISION_CODE IS NOT NULL
		  	 AND APPLY_CODE = #{apply_code}
 		]]>
	</select>
	<!-- 投保单状态 和 核保结论 -->
	<select id="NB_FindDecisionCodeAndStatusByNbContractMaster" resultType="java.util.Map"
	 parameterType="java.util.Map">
		<![CDATA[ 
		SELECT PROPOSAL_STATUS,DECISION_CODE,APPLY_CODE
		  FROM DEV_NB.T_NB_CONTRACT_MASTER
		 	WHERE DECISION_CODE IS  NULL
		  	 AND APPLY_CODE = #{apply_code}
 		]]>
	</select>
	<!-- 受理状态-->
	<select id="NB_findUwStatusDetailByUwMaster" resultType="java.util.Map"
	 parameterType="java.util.Map">
		<![CDATA[ 
			SELECT UW_STATUS_DETAIL  FROM DEV_UW.T_UW_MASTER WHERE  BIZ_CODE = #{apply_code}
 		]]>
	</select>
	
	
	<!-- 通过投保单号查询 标准保费 和 当期缴费频率 -->
	<select id="NB_findStdPremfFByNbContractProduct" resultType="java.util.Map"
	 parameterType="java.util.Map">
		<![CDATA[ 
			SELECT STD_PREM_AF , PREM_FREQ 
		  		FROM DEV_NB.T_NB_CONTRACT_PRODUCT
		 			WHERE APPLY_CODE = #{apply_code}
	]]>
	</select>
	
	<!-- 查询被保人信息 -->
	<select id="NB_findLostInfoByInsuredListAndCustomer" resultType="java.util.Map"
	 parameterType="java.util.Map">
		<![CDATA[ 
			SELECT CUSTOMER_NAME,CUSTOMER_CERT_TYPE,CUSTOMER_CERTI_CODE
			  FROM DEV_NB.T_CUSTOMER
 				WHERE CUSTOMER_ID = (SELECT CUSTOMER_ID
					                     FROM DEV_NB.T_NB_INSURED_LIST
					                       WHERE APPLY_CODE = #{apply_code})
	]]>
	</select>
	<!-- 查询所属的主险ID及所属业务产品ID-->
	<select id="NB_findBusiPrdID" resultType="java.util.Map"
	 parameterType="java.util.Map">
		<![CDATA[ 
			SELECT BUSI_PRD_ID, MASTER_BUSI_ITEM_ID
				  	FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD
						WHERE APPLY_CODE = #{apply_code}
	]]>
	</select>
	<!-- 查询产品相关信息-->
	<select id="NB_findBusiPrdinfo" resultType="java.util.Map"
	 parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.STD_PREM_AF,
			       A.UNIT,
			       A.AMOUNT,
			       A.COVERAGE_PERIOD,
			       A.COVERAGE_YEAR,
			       A.CHARGE_PERIOD,
			       A.CHARGE_YEAR,
			       B.PRODUCT_STATIC_CODE,
			       C.BUSI_PRD_ID,
			       C.MASTER_BUSI_ITEM_ID,
			       C.DECISION_CODE
			  FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD C,
			       DEV_NB.T_BUSINESS_PRODUCT      B,
			       DEV_NB.T_NB_CONTRACT_PRODUCT   A
			 WHERE B.BUSINESS_PRD_ID = C.BUSI_PRD_ID
			   AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
			   AND C.APPLY_CODE = #{apply_code}
	]]>
	</select>
	<!-- 根据投保单号查询基本信息的保存字段的个数 -->
	<select id="NB_findPolicyBasiCountByApplyCodeCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ 
		       select (count1+count2+count3+count4+count5+count6+count7+count8+count9
				       +count10+count11+count12+count13+count14+count15+count16+count17
				       +count18+count19+count20+count21+count22+count23)
				 from ( select mm.count1,mm.apply_code,
               mm.count2,
               mm.count3,
               mm.count4,
               mm.count5,
               mm.count6,
               mm.count7,
               mm.count8,
               mm.count9,
               mm.count10,
               mm.count11,
               mm.count12,
               mm.count13,
               mm.count14,
               mm.count15,
               mm.count16,
               mm.count17,
               mm.count18,
               mm.count19,
               mm.count20,
               mm.count21,
               mm.count23, (case when tnpa.acknowledge_date is not null then 1 else 0 end) as count22--回执日期
				             from
				          (select tncm.policy_id,tncm.apply_code,
				          case when ta.agent_channel is not null then 1 else 0 end as count1,--销售渠道
				          case when tncm.apply_code is not null then 1 else 0 end as count2,--投保单号
				          case when tncm.apply_date is not null then 1 else 0 end as count3,--投保日期
				          case when tncm.submission_date is not null then 1 else 0 end as count4,--交单日期
				          case when tncm.submission_date is not null then 1 else 0 end as count5,--审核日期
				          case when tncm.sale_agent_code is not null then 1 else 0 end as count6,--交叉销售专员
				          case when tncm.organ_code is not null then 1 else 0 end as count7,--管理机构
				          case when tncm.pa_user_code is not null then 1 else 0 end as count8,--初审人员
				          case when tca.agent_code is not null then 1 else 0 end as count9,--业务员
				          case when ta.agent_level is not null then 1 else 0 end as count10,--星级业务员
				          case when ta.agent_mobile is not null then 1 else 0 end as count11,--业务员电话 
				          case when ta.agent_organ_code is not null then 1 else 0 end as count12,--所属机构
				          case when tca.relation_to_ph is not null then 1 else 0 end as count13,--与投保人关系           
				          case when tncm.high_sa_indi is not null then 1 else 0 end as count14,--高额件
				          case when tncm.birthday_pol_indi is not null then 1 else 0 end as count15, --生日单
				          case when tncm.manual_uw_indi is not null then 1 else 0 end as count16,--人工核保
				          case when tncm.risk_indi is not null then 1 else 0 end as count17,--风险提示是否抄写
				          case when tncm.e_service_flag is not null then 1 else 0 end as count18,--电子函件服务
				          case when tncm.dialect_indi is not null then 1 else 0 end as count19,--方言件
				          case when tncm.call_time_list is not null then 1 else 0 end as count20,--电话回访时间
				          case when tncm.media_type is not null then 1 else 0 end as count21,--保单类型
				          case when tncm.validate_date is not null then 1 else 0 end as count23--生效日期   
				     from DEV_NB.T_nb_contract_master tncm,
				          DEV_NB.T_NB_CONTRACT_AGENT tca,
				          DEV_PAS.T_AGENT ta
				     where 1=1
				     and tca.policy_id = tncm.policy_id
				     and ta.agent_code = tca.agent_code) mm
				     left join  DEV_NB.T_nb_policy_acknowledgement tnpa
				     on tnpa.policy_id = mm.policy_id
				     ) mmmm
				     where  1=1 
				     and mmmm.apply_code = #{apply_code}
		  ]]>
	</select>
	<!-- 根据投保单号查询投保人信息的保存字段的个数 -->
	<select id="NB_findPolicyHolderCountByApplyCodeCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		select count1+count2+count3/*+count4*/+count5+count6+count7+count8+count9+count10+
       		   count11+count12+count13+count14+count15+count16+count17+count18+count19+count20+
       		   count21+count22+count23+count24+count25+count26+count27 from
			  (select 
			        case when tc.customer_name is not null then 1 else 0 end as count1,--客户编码
			        case when tc.customer_birthday is not null then 1 else 0 end as count2,--姓名
			        case when tc.customer_level is not null then 1 else 0 end as count3,--客户等级
			        --客户风险等级
			        case when tc.customer_cert_type is not null then 1 else 0 end as count5,--证件类型 
			        case when tc.customer_certi_code is not null then 1 else 0 end as count6,--证件号码
			        case when tc.cust_cert_star_date is not null then 1 else 0 end as count7,--证件有效期起
			        case when tc.cust_cert_end_date is not null then 1 else 0 end as count8,--证件有效期止
			        case when tc.country_code is not null then 1 else 0 end as count9,--国籍
			        case when tc.customer_gender is not null then 1 else 0 end as count10,--性别
			        case when tc.customer_birthday is not null then 1 else 0 end as count11,--出生日期
			        case when tc.customer_birthday is not null then 1 else 0 end as count12,--投保人年龄
			        case when tc.marriage_status is not null then 1 else 0 end as count13,--婚姻状况
			        case when tc.is_parent is not null then 1 else 0 end as count14,--子女状况
			        case when tc.job_code is not null then 1 else 0 end as count15,--职业编码
			        case when tc.job_kind is not null then 1 else 0 end as count16,--职业类别
			        case when tc.job_title is not null then 1 else 0 end as count17,--职务
			        case when tc.driver_license_type is not null then 1 else 0 end as count18,--驾照类型
			        case when ta.state is not null then 1 else 0 end as count19,--省/直辖市
			        case when ta.city is not null then 1 else 0 end as count20,--市
			        case when ta.district is not null then 1 else 0 end as count21,--区/县
			        case when ta.address is not null then 1 else 0 end as count22,--地址 
			        case when ta.post_code is not null then 1 else 0 end as count23,--邮政编码
			        case when ta.mobile_tel is not null then 1 else 0 end as count24,--移动电话 
			        case when ta.fixed_tel is not null then 1 else 0 end as count25,--固定电话
			        case when tc.company_name is not null then 1 else 0 end as count26,--工作单位 
			        case when tc.email is not null then 1 else 0 end as count27--电子邮箱          
			   from DEV_NB.T_NB_POLICY_HOLDER tnph,DEV_NB.T_customer tc,DEV_NB.T_address ta 
			   where 1=1
			   and tnph.customer_id = tc.customer_id 
			   and ta.address_id = (select max(taa.address_id) from DEV_NB.T_address taa where 1=1
			   and taa.customer_id=(select tnph.customer_id from DEV_NB.T_NB_POLICY_HOLDER tnph where tnph.apply_code = #{apply_code}))
			   and tnph.apply_code = #{apply_code})
		  ]]>
	</select>
	<!-- 根据投保单号查询被保人信息的保存字段的个数 -->
	<select id="NB_findPolicyInsureCountByApplyCodeCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		select count1+count2+count3+count4+count5+count6+count7+count8+count9+count10+
       		   count11+count12+count13+count14+count15+count16+count17+count18+count19+count20+
       		   count21+count22+count23+count24+count25+count26 from
			  (select 
			        case when tc.customer_name is not null then 1 else 0 end as count1,--客户编码
			        case when tnil.relation_to_ph is not null then 1 else 0 end as count2,--与投保人关系
			        case when tc.customer_birthday is not null then 1 else 0 end as count3,--姓名
			        case when tc.customer_cert_type is not null then 1 else 0 end as count4,--证件类型 
			        case when tc.customer_certi_code is not null then 1 else 0 end as count5,--证件号码
			        case when tc.cust_cert_star_date is not null then 1 else 0 end as count6,--证件有效期起
			        case when tc.cust_cert_end_date is not null then 1 else 0 end as count7,--证件有效期止
			        case when tc.country_code is not null then 1 else 0 end as count8,--国籍
			        case when tc.customer_gender is not null then 1 else 0 end as count9,--性别
			        case when tc.customer_birthday is not null then 1 else 0 end as count10,--出生日期
			        case when tc.customer_birthday is not null then 1 else 0 end as count11,--被保人年龄
			        case when tc.marriage_status is not null then 1 else 0 end as count12,--婚姻状况
			        case when tc.is_parent is not null then 1 else 0 end as count13,--子女状况
			        case when tc.job_code is not null then 1 else 0 end as count14,--职业编码
			        case when tc.job_kind is not null then 1 else 0 end as count15,--职业类别
			        case when tc.job_title is not null then 1 else 0 end as count16,--职务
			        case when tc.driver_license_type is not null then 1 else 0 end as count17,--驾照类型
			        case when ta.state is not null then 1 else 0 end as count18,--省/直辖市
			        case when ta.city is not null then 1 else 0 end as count19,--市
			        case when ta.district is not null then 1 else 0 end as count20,--区/县
			        case when ta.address is not null then 1 else 0 end as count21,--地址 
			        case when ta.post_code is not null then 1 else 0 end as count22,--邮政编码
			        case when ta.mobile_tel is not null then 1 else 0 end as count23,--移动电话 
			        case when ta.fixed_tel is not null then 1 else 0 end as count24,--固定电话
			        case when tc.company_name is not null then 1 else 0 end as count25,--工作单位 
			        case when tc.email is not null then 1 else 0 end as count26--电子邮箱          
			   from DEV_NB.T_nb_insured_list tnil,DEV_NB.T_customer tc,DEV_NB.T_address ta 
			   where 1=1
			   and tnil.customer_id = tc.customer_id 
			   and ta.address_id = (select max(taa.address_id) from DEV_NB.T_address taa where 1=1
			   and taa.customer_id=(select max(tnil.customer_id) as customer_id from DEV_NB.T_nb_insured_list tnil where tnil.apply_code = #{apply_code}))
			   and tnil.customer_id=(select max(tnil.customer_id) as customer_id from DEV_NB.T_nb_insured_list tnil where tnil.apply_code = #{apply_code})
			   and tnil.apply_code = #{apply_code})
		  ]]>
	</select>
	<!-- 根据投保单号查询险种信息的保存字段的个数 -->
	<select id="NB_findPolicyBusiCountByApplyCodeCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		  select count(*)*8 from dev_nb.t_nb_contract_product tncp where tncp.apply_code=#{apply_code}
		  ]]>
	</select>
	<!-- 根据投保单号查询受益人信息的保存字段的个数 -->
	<select id="NB_findPolicyBeneCountByApplyCodeCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		select nvl(sum(count1+count2+count3+count4+count5+count6+count7+count8+count9+count10+
		       count11+count12+count13+count14+count15+count16+count17+count18+count19+count20+
		       count2+count22),0) from
			  (select 
			        case when tncb.busi_item_id is not null then 1 else 0 end as count1,--所属险种
			        case when tncb.insured_id is not null then 1 else 0 end as count2,--所属被保险人
			        case when tncb.designation is not null then 1 else 0 end as count3,--与被保险人关系
			        case when tc.customer_birthday is not null then 1 else 0 end as count4,--姓名
			        case when tc.customer_cert_type is not null then 1 else 0 end as count5,--证件类型 
			        case when tc.customer_certi_code is not null then 1 else 0 end as count6,--证件号码
			        case when tc.cust_cert_star_date is not null then 1 else 0 end as count7,--证件有效期起
			        case when tc.cust_cert_end_date is not null then 1 else 0 end as count8,--证件有效期止
			        case when tc.country_code is not null then 1 else 0 end as count9,--国籍
			        case when tc.customer_gender is not null then 1 else 0 end as count10,--性别
			        case when tc.customer_birthday is not null then 1 else 0 end as count11,--受益人年龄
			        case when tc.customer_birthday is not null then 1 else 0 end as count12,--出生日期
			        case when tc.job_code is not null then 1 else 0 end as count13,--职业编码
			        case when tncb.share_order is not null then 1 else 0 end as count14,--受益顺序
			        case when tncb.share_rate is not null then 1 else 0 end as count15,--受益份额
			        case when ta.mobile_tel is not null then 1 else 0 end as count16,--联系电话 
			        case when ta.fixed_tel is not null then 1 else 0 end as count17,--固定电话
			        case when ta.address is not null then 1 else 0 end as count18,--通讯地址 
			        case when ta.state is not null then 1 else 0 end as count19,--省/直辖市
			        case when ta.city is not null then 1 else 0 end as count20,--市
			        case when ta.district is not null then 1 else 0 end as count21,--区/县
			        case when ta.post_code is not null then 1 else 0 end as count22--邮政编码  
			   from DEV_NB.T_NB_CONTRACT_BENE tncb,DEV_NB.T_customer tc,DEV_NB.T_address ta
			  where 1=1
			  and tc.customer_id = tncb.customer_id
			  and ta.address_id = (select max(taa.address_id) from DEV_NB.T_address taa where 1=1
			     and taa.customer_id=(select max(tncb.customer_id) as customer_id from DEV_NB.T_NB_CONTRACT_BENE tncb where tncb.apply_code = #{apply_code}))
			  and tncb.apply_code =#{apply_code})
		  ]]>
	</select>
	<select id="NB_queryContractMasterApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		 select m.apply_code,m.apply_date
      	from DEV_NB.T_nb_contract_master m
     		where m.proposal_status = #{proposal_status}
		   ]]>
			<include refid="queryContractMasterApplyCode" />
	</select>
	<sql id="queryContractMasterApplyCode">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND m.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	
	<select id="NB_queryMastCountByStatus" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		 SELECT COUNT(1) FROM DEV_NB.T_NB_CONTRACT_MASTER M WHERE m.proposal_status = #{proposal_status}
		]]>
	</select>

	<!-- 查询是否电子签名保单 -->
	<select id="NB_findIfEsingNbContractMaster" resultType="java.lang.Integer" parameterType="java.util.Map">
	<![CDATA[
	select A.CONFIRM_WAY,A.POLICY_CODE,A.APPLY_CODE from dev_NB.t_NB_contract_master A WHERE A.APPLY_CODE=#{apply_code}
		]]>
	</select>
	<!--电子签名核保履历 -->
	 <select id="NB_findEsingNbContractMasterRecord" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
select  t.buss_code as apply_code,t.scan_user_code as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=t.scan_user_code)as operatorname, 
          t.scan_time as operatortime, '上载单证'||t.billcard_code as state,'--' as chkType from DEV_NB.T_image_scan t 
             where t.billcard_code in ('UA001', 'UA006', 'UA022', 'UA023', 'UA024', 'UA011') and t.buss_code =#{apply_code}
union 
select  t.apply_code,to_char( t.org_id)as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=t.org_id)as operatorname,
           t.send_time as operatortime, '操作员问题件下发' as state, '--' as chkType from DEV_NB.T_bpo_entry_detail t 
             where t.apply_code = #{apply_code} and t.issue_sign = '1' and t.send_date is not null and 
              t.receive_date is null and t.receive_state is null
union               
select t.apply_code,to_char( t.org_id)as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=t.org_id)as operatorname,
          t.send_time as operatortime, '操作员问题件处理完毕' as state, '--' as chkType 
             from DEV_NB.T_bpo_entry_detail t where t.apply_code = #{apply_code} and t.issue_sign = '1' 
                and t.receive_date is not null and t.receive_state is not null
union               
select a.apply_code,a.qt_user as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=a.qt_user)as operatorname,
         a.QT_TIME as operatortime ,a.qa_type as chkType,'质检不通过' as state  
           from DEV_NB.T_nb_qt_task a  where a.qt_status = '1' and a.qa_type in ('4','5')  and a.apply_code = #{apply_code}
             and exists (select * from DEV_NB.T_NB_ESQUOALITYDECISON b where  b.es_qt_result = '2' and a.task_id = b.task_id)
union 
 select a.apply_code,a.qt_user as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=a.qt_user)as operatorname,
          a.QT_TIME as operatortime,a.qa_type as chkType,'质检通过' as state 
           from DEV_NB.T_nb_qt_task a  where a.qt_status = '1' and a.qa_type in ('4','5') and a.apply_code = #{apply_code}
          and exists (select * from DEV_NB.T_NB_ESQUOALITYDECISON b where b.es_qt_result = '1' and a.task_id = b.task_id)
union 
select a.apply_code,a.qt_user as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=a.qt_user)as operatorname,
         a.QT_TIME as operatortime ,a.qa_type as chkType,decode(a.qt_status,'0','待质检','2','质检终止','4','质检逾期') as state
 from DEV_NB.T_nb_qt_task a where a.qt_status = '0' and a.qa_type in ('4','5')  and a.apply_code = #{apply_code} and a.qt_status in ('0','2','4')

union 

select a.apply_code,a.qt_user as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=a.qt_user)as operatorname,
       a.QT_TIME as operatortime ,a.qa_type as chkType,'质检通知书已下发'  as state
  from DEV_NB.T_nb_qt_task a  where a.qt_status = '1' and a.qa_type in ('4','5')  and a.apply_code = #{apply_code}
   and exists (select * from DEV_NB.T_document b where b.buss_code = a.apply_code and b.template_code = 'NBS_00014' and b.status = '2')

union 

select a.apply_code,a.qt_user as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=a.qt_user)as operatorname,
    a.QT_TIME as operatortime,a.qa_type as chkType,'质检通知书已回复' as state 
    from DEV_NB.T_nb_qt_task a  where a.qt_status = '1' and a.qa_type in ('4','5') and a.apply_code = #{apply_code}
   and exists (select * from DEV_NB.T_document b where b.buss_code = a.apply_code and b.template_code = 'NBS_00014'and b.status = '7')
   
	 ]]>
</select>
	<!-- 非电子签名履历 -->
	<select id="NB_findNotEsingNbContractMasterRecord" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
select  t.buss_code as apply_code,t.scan_user_code as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=t.scan_user_code)as operatorname, 
          t.scan_time as operatortime, '上载单证'||t.billcard_code as state,'--' as chkType from DEV_NB.T_image_scan t 
             where t.billcard_code in ('UA001', 'UA006', 'UA022', 'UA023', 'UA024', 'UA011') and t.buss_code =#{apply_code}
union 
select  t.apply_code,to_char( t.org_id)as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=t.org_id)as operatorname,
           t.send_time as operatortime, '操作员问题件下发' as state, '--' as chkType from DEV_NB.T_bpo_entry_detail t 
             where t.apply_code =#{apply_code} and t.issue_sign = '1' and t.send_date is not null and 
              t.receive_date is null and t.receive_state is null
union               
select t.apply_code,to_char( t.org_id)as operatorcode,(select B.USER_NAME from DEV_PAS.T_UDMP_USER B WHERE B.USER_ID=t.org_id)as operatorname,
          t.send_time as operatortime, '操作员问题件处理完毕' as state, '--' as chkType 
             from DEV_NB.T_bpo_entry_detail t where t.apply_code =#{apply_code} and t.issue_sign = '1' 
                and t.receive_date is not null and t.receive_state is not null
	 ]]>
	 </select>

	
	<select id="NB_findNbContractBusiInfoForEs" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT T.APPLY_CODE, T.POLICY_CODE, T.POLICY_ID
			  FROM DEV_NB.T_NB_CONTRACT_MASTER T
			 WHERE T.ISSUE_DATE > SYSDATE - 1
			   AND T.SUBMIT_CHANNEL IN (2, 5)
			   /*AND T.CONFIRM_WAY = '2'*/
			   AND NOT EXISTS
			 (SELECT 1 FROM DEV_NB.T_PROPOSAL_PROCESS T1
			         WHERE T1.PROCESS_STEP = '19'
			           AND T1.APPLY_CODE = T.APPLY_CODE)
			   	       AND 
			   ((NOT EXISTS (SELECT 1 FROM DEV_NB.T_NB_QT_TASK T2 WHERE T2.APPLY_CODE = T.APPLY_CODE AND T2.QA_TYPE IN ('4')) AND T.PROPOSAL_STATUS IN ('10', '13')) 
			         OR
			       (NOT EXISTS (SELECT 1 FROM DEV_NB.T_NB_QT_TASK T2 WHERE T2.APPLY_CODE = T.APPLY_CODE AND T2.QA_TYPE IN ('5')) AND T.PROPOSAL_STATUS IN ('14'))) ]]>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND T.ORGAN_CODE like concat(#{organ_code},'%') ]]></if>
	</select>
	
	<select id="NB_findOtherESQcMission" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT T.APPLY_CODE,
                T.POLICY_CODE,
                T.POLICY_ID,
                T1.TEMPLATE_CODE,
                T.PROPOSAL_STATUS,
                T.ISSUE_DATE
			  FROM DEV_NB.T_NB_CONTRACT_MASTER T, DEV_NB.T_Document t1
			 WHERE T.ISSUE_DATE > SYSDATE - 2
			   AND T.SUBMIT_CHANNEL IN (2, 5)
			   AND T.APPLY_CODE = T1.BUSS_CODE
			   AND T1.TEMPLATE_CODE IN ('NBS_00001', 'NBS_00002', 'NBS_00008')
			   AND T1.STATUS = '7'
			   AND T.PROPOSAL_STATUS IN ('10', '13', '14')
			   AND EXISTS (SELECT 1
			          FROM DEV_NB.T_NB_QT_TASK T2
			         WHERE T2.APPLY_CODE = T.APPLY_CODE
			           AND T2.QA_TYPE = '6')]]>
	</select>
	
	<!--  -->
	<select id="NB_queryMasterDataForDRQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT MM.APPLY_CODE,MM.POLICY_CODE,MM.PROPOSAL_STATUS,MM.APPLY_DATE,MM.ORGAN_CODE,MM.SUBMIT_CHANNEL,MM.CHANNEL_TYPE,B.AGENT_NAME,H.CUSTOMER_ID,A.AGENT_CODE,D.AUDIO_INFO_DATE,D.AUDIO_INFO_SIZE FROM DEV_NB.T_NB_CONTRACT_MASTER MM,  DEV_NB.T_NB_CONTRACT_AGENT A,
				DEV_NB.T_NB_POLICY_HOLDER H,DEV_NB.T_DRQ_CHECK_INFO D,DEV_PAS.T_AGENT  B
				WHERE MM.POLICY_ID = A.POLICY_ID
				AND D.APPLY_CODE = MM.APPLY_CODE
				AND MM.POLICY_ID = H.POLICY_ID
				AND A.AGENT_CODE = B.AGENT_CODE 
				AND MM.PROPOSAL_STATUS IN ('05', '06', '07', '09','52','22','31','32','42','43','51')
				AND MM.DRQ_FLAG = '1'
				AND ROWNUM<10
		 ]]>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND mm.ORGAN_CODE like concat(#{organ_code},'%') ]]></if>
	</select>
	
	<select id="NB_queryCheckedAccountPolicy" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select cm.apply_code,cm.apply_date, cm.proposal_status, cm.service_bank, cm.policy_code,cm.decision_code 
		from dev_nb.t_nb_contract_master cm where cm.submit_channel = 1 ]]>
		<if test="policy_code != null and policy_code != '' "><![CDATA[ AND cm.policy_code = #{policy_code}]]></if>
		<if test="service_bank != null and service_bank != '' "><![CDATA[ AND cm.service_bank = #{service_bank}]]></if>
		<if test="apply_date != null "><![CDATA[ AND cm.issue_date between #{apply_date} and #{apply_date} + 1]]></if>
		<if test="apply_code != null and apply_code != '' "><![CDATA[ AND cm.apply_code = #{apply_code}]]></if>
	</select>
	<!-- 银保通查询接口使用 -->
	<select id="NB_findPolicyMasterForBankService" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  
			AND A.CHANNEL_TYPE = '03'
			]]>
		<if test="apply_code != null and apply_code != '' "><![CDATA[ AND A.apply_code = #{apply_code}]]></if>
		<if test="policy_code != null and policy_code != '' "><![CDATA[ AND A.policy_code = #{policy_code}]]></if>
		<if test="apply_date != null "><![CDATA[ AND A.apply_date = #{apply_date}]]></if>
		<if test="service_bank != null and service_bank != ''  "><![CDATA[ AND trim(A.service_bank) = #{service_bank}]]></if>
		 <if test=" bussiness_type != null and bussiness_type == 1 "><![CDATA[ 
			   and A.PROPOSAL_STATUS in ('10','13','14') ]]></if>
		 <if test=" input_type != null and input_type != '' and input_type == 0 "><![CDATA[ 
			   and A.SUBMIT_CHANNEL in (3, 4) ]]></if>
		 <if test=" input_type != null and input_type != '' and input_type == 1 "><![CDATA[ 
			   and A.SUBMIT_CHANNEL = 1  AND A.SUBINPUT_TYPE = '5']]></if>
		<![CDATA[  ORDER BY A.APPLY_CODE ]]>
	</select>
	
	<select id="NB_findConcelPolicyByApplyCodeAndProposalStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  
           		 select  CC.RN AS ROWNUMBER,
                 CC.APPLY_CODE,
                 CC.ORGAN_CODE,
                 CC.PROPOSAL_STATUS,
                 CC.BANK_CODE,
                 CC.FINISH_TIME,
                 CC.BANK_ACCOUNT,
                 CC.HOLDER_NAME,
                 CC.POLICY_ID,
                 CC.FEE_AMOUNT,
                 CC.PAY_MODE,
                 CC.CONCLUSION_CODE,
                 CC.customer_name,
                 CC.PAYEE_NAME,
                   CC.Image_Scan_Status,
                   CC.customer_id ,
                   CC.submit_channel

 		from (		select ROWNUM RN,
 				 A.APPLY_CODE,
                 aa.ORGAN_CODE,
                 aa.PROPOSAL_STATUS,
                 dd.BANK_CODE,
                 dd.FINISH_TIME,
                 dd.BANK_ACCOUNT,
                 dd.HOLDER_NAME,
                 aa.POLICY_ID,
                 aa.submit_channel,
                 dd.FEE_AMOUNT,
                 dd.PAY_MODE,
                 A.CONCLUSION_CODE,
                 A.Insert_Time,
                 c.customer_name,
                 dd.PAYEE_NAME,
                    D.Image_Scan_Status,
                    c.customer_id 
            from DEV_NB.T_pre_audit_master A
            left join (select p.APPLY_CODE,
                              p.ORGAN_CODE,
                              p.PROPOSAL_STATUS,
                              p.POLICY_ID,
                              p.submit_channel
                         from DEV_NB.T_nb_contract_master p) aa
              on A.apply_code = aa.APPLY_CODE
            left join (    SELECT  aaa.apply_code, max(aaa.bank_code) as bank_code, max(bbb.BANK_ACCOUNT) as BANK_ACCOUNT,
                                           max(aaa.holder_name) as holder_name,
                                           max(aaa.pay_mode) as pay_mode,
                                             max(aaa.finish_time) as finish_time,
                                        max(aaa.payee_name) as payee_name,
                                            sum(aaa.fee_amount) as fee_amount
                                  FROM DEV_NB.T_nb_Prem_Arap aaa,DEV_NB.T_Prem_Arap bbb
                                  WHERE bbb.FEE_STATUS = '01' and aaa.busi_prod_code=bbb.busi_prod_code and aaa.fee_status='00'
                                  and aaa.apply_code=bbb.apply_code ]]>
                                  <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND bbb.APPLY_CODE = #{apply_code} ]]></if>
                                  <![CDATA[ group by aaa.apply_code
                        ) dd
              on aa.apply_code = dd.apply_code
            left join DEV_NB.T_NB_POLICY_HOLDER B
              ON aa.POLICY_ID = B.POLICY_ID
            LEFT JOIN DEV_NB.T_CUSTOMER C
              ON B.CUSTOMER_ID = C.CUSTOMER_ID
              left join (select d.Buss_Code,max(d.image_scan_status) as image_scan_status from DEV_NB.T_image_scan  D group by d.buss_code) D 
              on A.Apply_Code= D.Buss_Code
            WHERE 1 = 1 ]]>
            <include refid="NB_queryByApplyCodeCondition"/>
            <if test=" proposal_status != null and proposal_status != '' "><![CDATA[ AND AA.PROPOSAL_STATUS = #{proposal_status} ]]></if>
            <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND (A.Audit_Organ_Code LIKE CONCAT(#{organ_code}, '%') or AA.organ_Code LIKE CONCAT(#{organ_code}, '%'))]]></if>
            <![CDATA[  and ROWNUM <= #{LESS_NUM}
            order by A.Insert_Time desc ) CC 
           	WHERE CC.RN > #{GREATER_NUM}		]]>
	</select>
	
	<select id="NB_findConcelPolicyByApplyCodeAndProposalStatusTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[  SELECT COUNT(1) from(
           		 select 
                 CC.APPLY_CODE,
                 CC.ORGAN_CODE,
                 CC.PROPOSAL_STATUS,
                 CC.BANK_CODE,
                 CC.FINISH_TIME,
                 CC.BANK_ACCOUNT,
                 CC.HOLDER_NAME,
                 CC.POLICY_ID,
                 CC.FEE_AMOUNT,
                 CC.PAY_MODE,
                 CC.CONCLUSION_CODE,
                 CC.customer_name,
                 CC.PAYEE_NAME,
                   CC.Image_Scan_Status,
                   CC.customer_id ,
                   CC.submit_channel

 		from (		select 
 				 A.APPLY_CODE,
                 aa.ORGAN_CODE,
                 aa.PROPOSAL_STATUS,
                 dd.BANK_CODE,
                 dd.FINISH_TIME,
                 dd.BANK_ACCOUNT,
                 dd.HOLDER_NAME,
                 aa.POLICY_ID,
                 aa.submit_channel,
                 dd.FEE_AMOUNT,
                 dd.PAY_MODE,
                 A.CONCLUSION_CODE,
                 A.Insert_Time,
                 c.customer_name,
                 dd.PAYEE_NAME,
                    D.Image_Scan_Status,
                    c.customer_id 
            from DEV_NB.T_pre_audit_master A
            left join (select p.APPLY_CODE,
                              p.ORGAN_CODE,
                              p.PROPOSAL_STATUS,
                              p.POLICY_ID,
                              p.submit_channel
                         from DEV_NB.T_nb_contract_master p) aa
              on A.apply_code = aa.APPLY_CODE
            left join (    SELECT  aaa.apply_code, max(aaa.bank_code) as bank_code, max(bbb.BANK_ACCOUNT) as BANK_ACCOUNT,
                                           max(aaa.holder_name) as holder_name,
                                           max(aaa.pay_mode) as pay_mode,
                                             max(aaa.finish_time) as finish_time,
                                        max(aaa.payee_name) as payee_name,
                                            sum(aaa.fee_amount) as fee_amount
                                  FROM DEV_NB.T_nb_Prem_Arap aaa,DEV_NB.T_Prem_Arap bbb
                                  WHERE bbb.FEE_STATUS = '01' and aaa.busi_prod_code=bbb.busi_prod_code and aaa.fee_status='00'
                                  and aaa.apply_code=bbb.apply_code ]]>
                                  <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND bbb.APPLY_CODE = #{apply_code} ]]></if>
                                  <![CDATA[ group by aaa.apply_code
                        ) dd
              on aa.apply_code = dd.apply_code
            left join DEV_NB.T_NB_POLICY_HOLDER B
              ON aa.POLICY_ID = B.POLICY_ID
            LEFT JOIN DEV_NB.T_CUSTOMER C
              ON B.CUSTOMER_ID = C.CUSTOMER_ID
              left join (select d.Buss_Code,max(d.image_scan_status) as image_scan_status from DEV_NB.T_image_scan  D group by d.buss_code) D 
              on A.Apply_Code= D.Buss_Code
            WHERE 1 = 1 ]]>
            <include refid="NB_queryByApplyCodeCondition"/>
            <if test=" proposal_status != null and proposal_status != '' "><![CDATA[ AND AA.PROPOSAL_STATUS = #{proposal_status} ]]></if>
            <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND (A.Audit_Organ_Code LIKE CONCAT(#{organ_code}, '%') or AA.organ_Code LIKE CONCAT(#{organ_code}, '%')) ]]></if>
            <![CDATA[  
            order by A.Insert_Time desc ) CC )X
           		]]>
	</select>

	<!-- E保通专用 涉及出单前撤保功能 查询 -->
	<select id="NB_findConcelEpolicyByApplyCodeAndProposalStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  
           		select CC.RN AS ROWNUMBER,
           			   CC.APPLY_CODE,
				       CC.ORGAN_CODE,
				       CC.PROPOSAL_STATUS,
				       CC.BANK_CODE,
				       CC.FINISH_TIME,
				       CC.BANK_ACCOUNT,
				       CC.HOLDER_NAME,
				       CC.POLICY_ID,
				       CC.FEE_AMOUNT,
				       CC.PAY_MODE,
				       CC.customer_name,
				       CC.PAYEE_NAME,
				       CC.Image_Scan_Status,
				       CC.customer_id ,
				       CC.submit_channel
			  from (select ROWNUM RN,
			  			   aa.APPLY_CODE,
			               aa.ORGAN_CODE,
			               aa.PROPOSAL_STATUS,
			               dd.BANK_CODE,
			               dd.FINISH_TIME,
			               dd.BANK_ACCOUNT,
			               dd.HOLDER_NAME,
			               aa.POLICY_ID,
			               dd.FEE_AMOUNT,
			               dd.PAY_MODE,
			               c.customer_name,
			               dd.PAYEE_NAME,
			               D.Image_Scan_Status,
			               c.customer_id ,
			               aa.submit_channel
			          from DEV_NB.T_NB_CONTRACT_MASTER aa
			          left join (SELECT aaa.apply_code,
                           max(aaa.bank_code) as bank_code,
                           max(bbb.BANK_ACCOUNT) as BANK_ACCOUNT,
                           max(aaa.holder_name) as holder_name,
                           max(aaa.pay_mode) as pay_mode,
                           max(aaa.finish_time) as finish_time,
                           max(aaa.payee_name) as payee_name,
                           sum(aaa.fee_amount) as fee_amount
                      FROM DEV_NB.T_NB_PREM_ARAP aaa, DEV_NB.T_Prem_Arap bbb
                     WHERE bbb.FEE_STATUS = '01'
                       and aaa.busi_prod_code = bbb.busi_prod_code
                       and aaa.fee_status = '00'
                       and aaa.apply_code = bbb.apply_code]]>
                       <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND bbb.APPLY_CODE = #{apply_code} ]]></if>
                       <![CDATA[group by aaa.apply_code) dd
				            on aa.apply_code = dd.apply_code
				          left join DEV_NB.T_NB_POLICY_HOLDER B
				            ON aa.POLICY_ID = B.POLICY_ID
				          LEFT JOIN DEV_NB.T_CUSTOMER C
				            ON B.CUSTOMER_ID = C.CUSTOMER_ID
				          left join (select d.Buss_Code,max(d.image_scan_status) as image_scan_status from DEV_NB.T_image_scan  D group by d.buss_code) D
				            on aa.Apply_Code = D.Buss_Code
				         WHERE 1 = 1]]>
                         <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND aa.APPLY_CODE = #{apply_code} ]]></if>
                         <if test=" proposal_status != null and proposal_status != '' "><![CDATA[ AND AA.PROPOSAL_STATUS = #{proposal_status} ]]></if>
                         <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND AA.organ_code = #{organ_code} ]]></if>
                         <![CDATA[ and ROWNUM <= #{LESS_NUM}
                         order by aa.Insert_Time desc) CC
				 			WHERE CC.RN > #{GREATER_NUM}	
           			]]>
	</select>
	<select id="NB_findConcelEpolicyByApplyCodeAndProposalStatusTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[  SELECT COUNT(1) from (
           		select 
           			   CC.APPLY_CODE,
				       CC.ORGAN_CODE,
				       CC.PROPOSAL_STATUS,
				       CC.BANK_CODE,
				       CC.FINISH_TIME,
				       CC.BANK_ACCOUNT,
				       CC.HOLDER_NAME,
				       CC.POLICY_ID,
				       CC.FEE_AMOUNT,
				       CC.PAY_MODE,
				       CC.customer_name,
				       CC.PAYEE_NAME,
				       CC.Image_Scan_Status,
				       CC.customer_id ,
				       CC.submit_channel
			  from (select 
			  			   aa.APPLY_CODE,
			               aa.ORGAN_CODE,
			               aa.PROPOSAL_STATUS,
			               dd.BANK_CODE,
			               dd.FINISH_TIME,
			               dd.BANK_ACCOUNT,
			               dd.HOLDER_NAME,
			               aa.POLICY_ID,
			               dd.FEE_AMOUNT,
			               dd.PAY_MODE,
			               c.customer_name,
			               dd.PAYEE_NAME,
			               D.Image_Scan_Status,
			               c.customer_id ,
			               aa.submit_channel
			          from DEV_NB.T_NB_CONTRACT_MASTER aa
			          left join (SELECT aaa.apply_code,
                           max(aaa.bank_code) as bank_code,
                           max(bbb.BANK_ACCOUNT) as BANK_ACCOUNT,
                           max(aaa.holder_name) as holder_name,
                           max(aaa.pay_mode) as pay_mode,
                           max(aaa.finish_time) as finish_time,
                           max(aaa.payee_name) as payee_name,
                           sum(aaa.fee_amount) as fee_amount
                      FROM DEV_NB.T_NB_PREM_ARAP aaa, DEV_NB.T_Prem_Arap bbb
                     WHERE bbb.FEE_STATUS = '01'
                       and aaa.busi_prod_code = bbb.busi_prod_code
                       and aaa.fee_status = '00'
                       and aaa.apply_code = bbb.apply_code]]>
                       <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND bbb.APPLY_CODE = #{apply_code} ]]></if>
                       <![CDATA[group by aaa.apply_code) dd
				            on aa.apply_code = dd.apply_code
				          left join DEV_NB.T_NB_POLICY_HOLDER B
				            ON aa.POLICY_ID = B.POLICY_ID
				          LEFT JOIN DEV_NB.T_CUSTOMER C
				            ON B.CUSTOMER_ID = C.CUSTOMER_ID
				          left join (select d.Buss_Code,max(d.image_scan_status) as image_scan_status from DEV_NB.T_image_scan  D group by d.buss_code) D
				            on aa.Apply_Code = D.Buss_Code
				         WHERE 1 = 1]]>
                         <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND aa.APPLY_CODE = #{apply_code} ]]></if>
                         <if test=" proposal_status != null and proposal_status != '' "><![CDATA[ AND AA.PROPOSAL_STATUS = #{proposal_status} ]]></if>
                         <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND AA.organ_code = #{organ_code} ]]></if>
                         <![CDATA[ 
                         order by aa.Insert_Time desc) CC
				 		)X
           			]]>
	</select>
	<select id="NB_findImageScanForEPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.CARD_CODE FROM DEV_NB.T_BILLCARD A,DEV_NB.T_IMAGE_SCAN B
			 WHERE trim(A.CARD_TYPE) = #{card_type}  AND trim(A.CARD_CODE) = trim(B.BILLCARD_CODE)
		 ]]>
		<if test=" business_code != null and business_code != '' "><![CDATA[ AND B.BUSS_CODE = #{business_code} ]]></if>
	</select>

    <!-- 撤保原因查询 -->
    <select id="NB_queryReCallReason" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT  T1.APPLY_CODE,
				(SELECT T.CUSTOMER_NAME FROM DEV_NB.T_CUSTOMER T WHERE T.CUSTOMER_ID=T1.CUSTOMER_ID)HOL_NAME,
				(SELECT T.CUSTOMER_NAME FROM DEV_NB.T_CUSTOMER T WHERE T.CUSTOMER_ID=T2.CUSTOMER_ID)INS_NAME,
				T3.CAUSE_ID_LIST,(SELECT CAUSE_DESC FROM DEV_NB.T_POLICY_CANCEL_CAUSE WHERE CAUSE_ID=T3.CAUSE_ID_LIST)REASON
				FROM DEV_NB.T_NB_POLICY_HOLDER T1,DEV_NB.T_NB_INSURED_LIST T2,DEV_NB.T_POLICY_CANCEL T3
				WHERE T1.APPLY_CODE=T2.APPLY_CODE AND T3.APPLY_CODE=T2.APPLY_CODE 
				AND T1.APPLY_CODE=#{apply_code}
		 ]]>
	</select>
<!-- 查询回执方式 -->
<select id="NB_queryContractMasterAcknowledgemmMode" resultType="java.util.Map" parameterType="java.util.Map" >
<![CDATA[
 select A.POLICY_RETURN_MODE FROM DEV_NB.T_NB_CONTRACT_MASTER A where A.APPLY_CODE=#{apply_code}
  ]]>
</select>
<!-- 修改操作,修改投保单状态 -->
	<update id="NB_updatePolicyReturnModeByApplyCode"
		parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_MASTER  A      ]]>
		<set>
			<trim suffixOverrides=",">
				<if test=" policy_return_mode  != null and policy_return_mode  != '' "><![CDATA[ POLICY_RETURN_MODE = #{policy_return_mode,jdbcType=NUMERIC} ,]]></if>
			</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="NB_queryByApplyCodeCondition" />
	</update>
	
	<!-- 根据客户id及投保日期指定险种查询万能险保单 -->
	<select id="NB_findRelationPolicyCodeByCustomerId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT M.PROPOSAL_STATUS,
				      	 M.POLICY_CODE,
				      	 M.APPLY_CODE,
				      	 H.CUSTOMER_ID,
				      	 P.PRODUCT_CODE,
				      	 B.PRODUCT_CATEGORY2,
				      	 M.APPLY_DATE,
				      	 M.POLICY_ID
				  FROM	 DEV_NB.T_NB_POLICY_HOLDER H,
				      	 DEV_NB.T_NB_INSURED_LIST  I,
				      	 DEV_NB.T_NB_CONTRACT_BUSI_PROD     P,
				       	 DEV_NB.T_NB_CONTRACT_MASTER      M,
				      	 DEV_NB.T_BUSINESS_PRODUCT B
				 WHERE  I.APPLY_CODE = P.APPLY_CODE  
					   	AND H.CUSTOMER_ID = I.CUSTOMER_ID
					   	AND H.APPLY_CODE = P.APPLY_CODE
					   	AND P.POLICY_ID = M.POLICY_ID
					   	AND B.BUSINESS_PRD_ID = P.BUSI_PRD_ID ]]>
		<include refid="NB_findRelationPolicyCodeCondition" />
		<![CDATA[ ORDER BY M.POLICY_ID]]>
	</select>
	
</mapper>
