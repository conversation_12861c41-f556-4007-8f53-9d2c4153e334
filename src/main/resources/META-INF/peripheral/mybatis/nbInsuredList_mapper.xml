<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.NbInsuredListDaoImpl">
<!--
	<sql id="NB_nbInsuredListWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" stand_life  != null "><![CDATA[ AND A.STAND_LIFE = #{stand_life} ]]></if>
		<if test=" relation_to_insured_1 != null and relation_to_insured_1 != ''  "><![CDATA[ AND A.RELATION_TO_INSURED_1 = #{relation_to_insured_1} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" insert_timestamp != null and insert_timestamp != ''  "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" medical_exam_indi != null and medical_exam_indi != ''  "><![CDATA[ AND A.MEDICAL_EXAM_INDI = #{medical_exam_indi} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="NB_queryByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="NB_queryByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyIdAndOrderCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<!-- <if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id } ]]></if> -->
	</sql>	
	<sql id="NB_queryByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>		
	<sql id="NB_queryByNotEqualsApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE != #{apply_code} ]]></if>
	</sql>
	<sql id="NB_findAnnualIncomeCondition">
		<if test=" resident_type != null "><![CDATA[ AND A.RESIDENT_TYPE = #{resident_type} ]]></if>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code}]]></if>
	</sql>
	<sql id="NB_findWaiver">
		<if test="waiver_flag != null and waiver_flag == 0">
			AND BP.PRODUCT_CODE IN (SELECT T.PRODUCT_CODE_SYS FROM DEV_UW.T_BUSINESS_PRODUCT T
                  WHERE T.WAIVER_FLAG = '1' AND T.PRODUCT_CATEGORY = '10002')
		</if>
		<if test="waiver_flag != null and waiver_flag == 1">
			AND BP.PRODUCT_CODE = '00425000'
		</if>
	</sql>
<!-- 添加操作 -->
	<insert id="NB_addNbInsuredList"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id"> SELECT DEV_NB.S_t_nb_insured_list.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_NB.T_NB_INSURED_LIST          (
				ADDRESS_ID, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, RELATION_TO_PH,ORDER_ID ,
				INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, 
				POLICY_ID,APPLY_CODE,JOB_CODE,JOB_UNDERWRITE,RESIDENT_TYPE,SOCI_SECU ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{relation_to_ph, jdbcType=VARCHAR} , #{order_id, jdbcType=NUMERIC}
				, SYSDATE , #{policy_code, jdbcType=VARCHAR}, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , SYSDATE , #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC}, #{apply_code, jdbcType=VARCHAR}, #{job_code, jdbcType=VARCHAR}, #{job_underwrite, jdbcType=VARCHAR}
				, #{resident_type, jdbcType=VARCHAR},#{soci_secu, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="NB_deleteNbInsuredList" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_INSURED_LIST           WHERE LIST_ID            = #{list_id           } ]]>
	</delete>

<!-- 修改操作 -->
	<update id="NB_updateNbInsuredList" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_INSURED_LIST           ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			RELATION_TO_PH = #{relation_to_ph, jdbcType=VARCHAR} ,
			INSERT_TIMESTAMP = #{insert_timestamp, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = #{update_timestamp, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    JOB_CODE = #{job_code, jdbcType=VARCHAR}, 
		    JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR}
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID            = #{list_id           } ]]>
	</update>

<!-- 修改操作 -->
	<update id="NB_updateNbInsuredListByListId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_INSURED_LIST           ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			<if test=" relation_to_ph != null and relation_to_ph != '' "><![CDATA[ RELATION_TO_PH = #{relation_to_ph, jdbcType=VARCHAR} , ]]></if>
			RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR},
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID            = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="NB_findInsuredByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,A.RESIDENT_TYPE, A.SOCI_SECU,
			A.POLICY_ID, A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE , job_name FROM DEV_NB.T_NB_INSURED_LIST  A left join DEV_NB.T_job_code b on a.job_code = b.job_code  WHERE 1 = 1  ]]>
		<include refid="NB_queryByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE 1 = 1  ]]>
		<include refid="NB_queryByAddressIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<select id="NB_findInsuredByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE 1 = 1  ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findNbInsuredListByJobCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			 select job.job_code, job.job_name as policy_code, und.job_uw_level_code||'' as job_underwrite
			  from DEV_NB.T_JOB_code job, DEV_NB.T_job_underwrite und
			 where job.job_uw_level = und.job_uw_level_code
			   and job.job_code = #{job_code} 
		 ]]>
		<!-- <![CDATA[ ORDER BY und.job_code   desc          ]]> -->
	</select>
	
	<select id="NB_findInsuredByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.ORDER_ID,
			A.POLICY_CODE, A.LIST_ID, A.APPLY_CODE, 
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="NB_findAllMapNbInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllNbInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,A.APPLY_CODE,A.RESIDENT_TYPE,
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.UPDATE_TIME desc            ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findNbInsuredListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_INSURED_LIST           A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
<!-- 查询个数操作 -->
	<select id="NB_findNbInsuredListTotalByCustomerId" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_INSURED_LIST           A WHERE 1 = 1  ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<include refid="NB_queryByPolicyIdCondition" />
	</select>
<!-- 分页查询操作 -->
	<select id="NB_queryNbInsuredListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.CUSTOMER_ID, B.RELATION_TO_PH, 
			B.POLICY_CODE, B.LIST_ID, 
			B.POLICY_ID,B.JOB_CODE,B.JOB_UNDERWRITE FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 查询第一被保人的信息 -->	
	<select id="NB_findInsuredByPolicyIdAndOrder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE 1 = 1]]>
		<include refid="NB_queryByPolicyIdAndOrderCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<!-- ${customer_id}  po中由于没有productId字段    利用customer_id 带入参数-->
	<select id="NB_findInsuredByPolicyIdAndProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		select i.LIST_ID,
		       i.POLICY_ID,
		       i.POLICY_CODE,
		       i.APPLY_CODE,
		       b.ORDER_ID,
		       i.ADDRESS_ID,
		       i.CUSTOMER_ID,
		       b.RELATION_TO_INSURED_1,
		       i.RELATION_TO_PH,
		       i.JOB_CODE,
		       i.JOB_UNDERWRITE,
		       i.CUSTOMER_HEIGHT,
		       i.CUSTOMER_WEIGHT,
		       i.SOCI_SECU
		  from DEV_NB.T_nb_contract_busi_prod prd,
		       DEV_NB.T_nb_benefit_insured  b,
		       DEV_NB.T_nb_insured_list     i
		 where prd.busi_prd_id = ${customer_id}
		   and prd.policy_id = ${policy_id}
		   and b.busi_item_id = prd.busi_item_id
		   and i.list_id = b.insured_id
		]]>
		<![CDATA[  order by b.order_id         ]]>
	</select>
	<select id="NB_findInsuredListByBusiItemIdAndPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select lins.LIST_ID,
				       	lins.POLICY_ID,
				      	lins.POLICY_CODE,
				     	lins.APPLY_CODE,
				        bins.ORDER_ID,
				        bins.busi_item_id,
				        lins.ADDRESS_ID,
				        lins.CUSTOMER_ID,
				        lins.RELATION_TO_PH,
				        lins.JOB_CODE,
				        lins.JOB_UNDERWRITE
				  from DEV_NB.T_nb_benefit_insured bins, DEV_NB.T_nb_insured_list lins
				 where bins.insured_id = lins.list_id ]]>
				 <if test=" customer_id != null and customer_id != '' ">
				 <![CDATA[ AND bins.busi_item_id = #{customer_id} ]]>
				 </if>
				  <if test=" policy_id != null and policy_id != '' ">
				 <![CDATA[ AND lins.policy_id = #{policy_id} ]]>
				 </if>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY lins.LIST_ID            ]]>
	</select>
	<select id="NB_findInsuredByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH,A.POLICY_CODE, A.LIST_ID,
				A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE,A.INPUT_SEQUENCE,T.CUSTOMER_NAME
			FROM DEV_NB.T_NB_INSURED_LIST A INNER JOIN DEV_NB.T_CUSTOMER T  ON A.CUSTOMER_ID = T.CUSTOMER_ID
			 WHERE 1 = 1  ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
		<select id="NB_findFirstInsuredByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select d.ADDRESS_ID,d.CUSTOMER_ID,c.customer_name,d.RELATION_TO_PH,d.POLICY_CODE,d.LIST_ID,d.POLICY_ID,
                   d.APPLY_CODE,d.JOB_CODE,d.JOB_UNDERWRITE from (SELECT A.ADDRESS_ID,
                   A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, A.LIST_ID, A.POLICY_ID, A.APPLY_CODE,
                   A.JOB_CODE, A.JOB_UNDERWRITE
                 FROM DEV_NB.T_NB_INSURED_LIST A, DEV_NB.T_NB_BENEFIT_INSURED B
                 WHERE 1 = 1
                 AND B.INSURED_ID = A.LIST_ID
                 AND B.RELATION_TO_INSURED_1 = '00' ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ AND ROWNUM=1 ORDER BY A.LIST_ID)d,DEV_NB.T_customer c where  d.CUSTOMER_ID=c.CUSTOMER_ID            ]]>
	</select>	
		
	<select id="NB_queryInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,A.CUSTOMER_ID,A.POLICY_ID,C.CUSTOMER_NAME,C.CUSTOMER_CERT_TYPE,
		C.CUSTOMER_CERTI_CODE FROM DEV_NB.T_NB_INSURED_LIST A left join DEV_NB.T_CUSTOMER C 
		on A.CUSTOMER_ID=C.CUSTOMER_ID where 1=1 ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>	
	<!-- 根据customer_id 和 apply_code 查询被保人为除该保单外的所有未承保保单的被保人信息 -->
	<select id="NB_findPolicyInsuredByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST A,DEV_NB.T_nb_contract_master B WHERE 1 = 1 and A.Policy_Id = B.Policy_Id
   AND B.Policy_Code is null AND B.Proposal_Status != '00' ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<include refid="NB_queryByNotEqualsApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
		<!-- 根据customer_id 和 apply_code 查询被保人为除该保单外的所有被保人信息 -->
	<select id="NB_findPolicyInsuredApplycodeByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST A WHERE 1 = 1 
   ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<include refid="NB_queryByNotEqualsApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<!-- 修改操作 -->
	<update id="NB_updatePolicyInsuredHeightAndWeight" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_INSURED_LIST           ]]>
		<set>
		<trim suffixOverrides=",">
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE CUSTOMER_ID = #{customer_id} AND POLICY_ID =#{policy_id} ]]>
	</update>
	<!-- 更新被保人社保标识  update by gaoxyit -->
	<update id="NB_updatePolicyInsuredSociSecu" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_INSURED_LIST           ]]>
		<set>
		<trim suffixOverrides=",">
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE CUSTOMER_ID = #{customer_id} AND POLICY_ID =#{policy_id} ]]>
	</update>
	<!-- 删除操作 -->	
	<delete id="NB_deleteNbInsuredListByPolicyId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_INSURED_LIST  WHERE POLICY_ID = #{policy_id} ]]>
	</delete>	
	<sql id="findploicyInsuredTRelationToPh">
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<!-- 投保人与被保人关系 -->
	<select id="NB_findploicyInsuredTRelationToPh" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE 1=1]]>
		<include refid="findploicyInsuredTRelationToPh" />
	</select>
	<select id="NB_findInsuredByCustomerIdToBusiProdTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
	       
	 <![CDATA[ SELECT count(*) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD D,
           (SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, A.LIST_ID,
                   A.POLICY_ID, A.APPLY_CODE, A.JOB_CODE, A.JOB_UNDERWRITE
              FROM DEV_NB.T_NB_INSURED_LIST    A,
                   DEV_NB.T_NB_CONTRACT_MASTER B
             WHERE A.APPLY_CODE = B.APPLY_CODE
               AND B.LIABILITY_STATE IN ('4', '0', '2')]]>
               <!--核保决定为拒保延期的时候，更新这个字段4无效 -->
               <include refid="NB_queryByCustomerIdCondition" />
           <![CDATA[ ) F
     WHERE D.POLICY_ID = F.POLICY_ID
     AND D.MASTER_BUSI_ITEM_ID IS NULL]]>
	</select>	
	
	<select id="NB_findInsuredByCustomerIdToBusiProdForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select e.RN AS rowNumber,e.apply_code,e.product_code,e.busi_item_id 
		from 
		(
		select ROWNUM RN,f.apply_code,d.product_code,d.busi_item_id from DEV_NB.T_nb_contract_busi_prod d,
		(SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, A.LIST_ID,
      			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE 
      			from DEV_NB.T_NB_INSURED_LIST A ,DEV_NB.T_nb_contract_master B
      		   where A.Apply_Code = B.Apply_Code       
		      and B.Liability_State in ('4','0','2') ]]><!--核保决定为拒保延期的时候，更新这个字段4无效 -->
		      <include refid="NB_queryByCustomerIdCondition" />  <![CDATA[
		      
		      ) f
		      where d.policy_id = f.policy_id and ROWNUM <= #{LESS_NUM} AND D.MASTER_BUSI_ITEM_ID IS NULL
		      order by f.apply_code
		  ) e WHERE e.RN > #{GREATER_NUM}  ]]>
	</select>	
	
	<!-- 根据投保人和被保人客户号，投被关系进行重复投被关系查询 -->
	<select id="NB_findByCustomerIdTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
	<![CDATA[
		select COUNT(1)
  from dev_nb.t_nb_insured_list a,dev_nb.T_nb_contract_master B,dev_nb.t_customer c,dev_nb.t_la_ph_rela l,dev_nb.T_GENDER g
 where a.relation_to_ph = #{relation_to_ph }
   and a.apply_code in
       (select apply_code
          from dev_nb.t_nb_policy_holder t1
         where customer_id = #{customer_id})--投保人 
         and a.relation_to_ph =l.relation_code
         and c.customer_id = a.customer_id
        and B.Apply_Code = a.apply_code
        and c.customer_id = a.customer_id
        and g.gender_code = c.customer_gender
   and a.customer_id <> #{holder_customer_id}--被保人 ]]>
	</select>
	<!-- 根据投保人和被保人客户号，投被关系进行重复投被关系查询 -->
	<select id="NB_findByCustomerIdList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select D.RN,D.APPLY_CODE,D.POLICY_CODE,D.CUSTOMER_NAME,D.CUSTOMER_GENDER,D.CUSTOMER_ID,
			      D.RELATION_TO_PH,D.RELATION_NAME,D.GENDER_DESC,D.customer_age,D.proposal_status from (
			      select  ROWNUM RN,
			      a.policy_code,--保单号
			      a.apply_code,--投保单号
			      c.customer_id,
			      c.customer_name,--被保人姓名
			      to_char(c.customer_gender) as customer_gender,--被保人性别
			      g.gender_desc,--被保人性别
			      to_char(floor(months_between(sysdate,c.customer_birthday)/12)) as customer_age, --被保人年龄
			      a.relation_to_ph,--投被关系
			      l.relation_name,--投保关系
			      B.Proposal_Status--保单状态
			  from dev_nb.t_nb_insured_list a,dev_nb.T_nb_contract_master B,dev_nb.t_customer c,dev_nb.t_la_ph_rela l,dev_nb.T_GENDER g
			 where a.relation_to_ph in ('07','01','02','03','04')
			   and a.apply_code in
			       (select apply_code
			          from dev_nb.t_nb_policy_holder t1
			         where customer_id = #{customer_id})--投保人
			         and a.relation_to_ph =l.relation_code
			         and c.customer_id = a.customer_id
			        and B.Apply_Code = a.apply_code
			        and c.customer_id = a.customer_id
			        and g.gender_code = c.customer_gender
			   and a.customer_id <> #{holder_customer_id}--被保人
			   ) D
			   ]]>
	</select>
	<!--根据applyCode 查询被保人customerId和policyId -->
	<select id="NB_findPolicyInsuredByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE ,B.Organ_Code
       FROM DEV_NB.T_NB_INSURED_LIST A,DEV_NB.T_nb_contract_master B WHERE 1 = 1 AND A.POLICY_ID =B.Policy_Id 
		 AND A.APPLY_CODE = #{apply_code} ORDER BY A.LIST_ID            ]]>
	</select>
	
	<!-- 根据customer_id 查询被保人信息 -->
	<select id="NB_queryInsuredByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST   A 
			WHERE A.APPLY_CODE = #{apply_code} and A.CUSTOMER_ID=#{customer_id}
			  ]]>

	</select>
	<!-- 按索引查询操作 -->	
	<select id="NB_findAnnualIncome" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESIDENT_TYPE, A.ORGAN_CODE, A.ORGAN_NAME, 
			A.ANNUAL_INCOME FROM DEV_NB.T_NB_ANNUAL_INCOME  A WHERE 1 = 1  ]]>
		<include refid="NB_findAnnualIncomeCondition" />
		<![CDATA[ ORDER BY A.ORGAN_CODE            ]]>
	</select>
	<!-- lianghxit 查找投保人相同，投被关系相同的保单被保人 -->
	<select id="NB_findAllPolicyHolderInsuredByCustomerIDAndRelation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT C.CUSTOMER_ID,C.CUSTOMER_BIRTHDAY,D.RELATION_TO_PH 
			FROM DEV_NB.T_CUSTOMER C,( 
				 SELECT I.CUSTOMER_ID AS INSURED_ID,MAX(I.RELATION_TO_PH) AS RELATION_TO_PH
				 FROM  DEV_NB.T_NB_INSURED_LIST I 
				 WHERE I.APPLY_CODE IN (
				       SELECT H.APPLY_CODE FROM DEV_NB.T_NB_POLICY_HOLDER H WHERE H.CUSTOMER_ID = #{holder_customer_id}
				 	   )
				 AND I.RELATION_TO_PH = #{relation_to_ph}
				 GROUP BY I.CUSTOMER_ID
				)D
			WHERE C.CUSTOMER_ID = D.INSURED_ID
		]]>
	</select>
	<!-- lianghxit 查询投保人下所有保单的所有被保人，且投被关系为六种之一的被保人  -->
	<select id="NB_findAllPolicyHolderInsuredByCustomerID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT C.CUSTOMER_ID,C.CUSTOMER_BIRTHDAY,D.RELATION_TO_PH 
			FROM DEV_NB.T_CUSTOMER C,( 
				 SELECT I.CUSTOMER_ID,MAX(I.RELATION_TO_PH) AS RELATION_TO_PH
		 		 FROM 	DEV_NB.T_NB_INSURED_LIST I 
		 		 WHERE  I.APPLY_CODE IN (
			      			SELECT H.APPLY_CODE 
			      			FROM DEV_NB.T_NB_POLICY_HOLDER H 
			      			WHERE H.CUSTOMER_ID = #{holder_customer_id}
				 		)
				 AND 	I.RELATION_TO_PH IN ('01','02','03','04','25','24')
		  		 GROUP BY I.CUSTOMER_ID
		  		 )D
			 WHERE C.CUSTOMER_ID = D.CUSTOMER_ID
		]]>
	
	</select>
	
	<!-- 按map查询操作 -->
	<select id="NB_findAllMapNbInsuredListForCRS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST  A ,DEV_NB.T_NB_CONTRACT_MASTER  B WHERE ROWNUM <=  1000 AND A.POLICY_ID=B.POLICY_ID ]]>
			<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND B.APPLY_CODE = #{apply_code} ]]></if>
			<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<!-- 查询被保人 -->
	<select id="NB_queryInsuredListByName" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	 select A.APPLY_CODE,B.CUSTOMER_ID,C.CUSTOMER_NAME

 from DEV_NB.T_NB_CONTRACT_MASTER A,DEV_NB.T_NB_INSURED_LIST B,DEV_NB.T_CUSTOMER C
 
 WHERE A.POLICY_ID=B.POLICY_ID AND B.CUSTOMER_ID=C.CUSTOMER_ID AND A.APPLY_CODE=#{apply_code} and C.CUSTOMER_NAME=#{customer_name}
 ]]>
	
	</select>
	
	
	<select id="NB_querySpecialWaiverTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT count(*) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD BP, (SELECT A.APPLY_CODE, A.POLICY_ID
                    FROM DEV_NB.T_NB_POLICY_HOLDER   A,
                         DEV_NB.T_NB_CONTRACT_MASTER CM
                   WHERE A.APPLY_CODE = CM.APPLY_CODE
                     AND CM.LIABILITY_STATE IN ('4', '0', '2')]]>
                     <include refid="NB_queryByCustomerIdCondition" />
                     <include refid="NB_queryByApplyCodeCondition"/>
                     <![CDATA[) D
          WHERE BP.POLICY_ID = D.POLICY_ID]]>
            <include refid="NB_findWaiver" /> 
	</select>	
	<select id="NB_querySpecialWaiverForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   
 		SELECT E.RN AS ROWNUMBER, E.APPLY_CODE, E.PRODUCT_CODE, E.BUSI_ITEM_ID FROM (
         SELECT ROWNUM RN, BP.APPLY_CODE, BP.PRODUCT_CODE, BP.BUSI_ITEM_ID
           FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD BP,
                 (SELECT A.APPLY_CODE, A.POLICY_ID
                    FROM DEV_NB.T_NB_POLICY_HOLDER  A,
                         DEV_NB.T_NB_CONTRACT_MASTER CM
                   WHERE A.APPLY_CODE = CM.APPLY_CODE
                     AND CM.LIABILITY_STATE IN ('4', '0', '2')]]>
 					<include refid="NB_queryByCustomerIdCondition" /> 
 					<include refid="NB_queryByApplyCodeCondition"/>
                    <![CDATA[ ) D
          WHERE BP.POLICY_ID = D.POLICY_ID
            AND ROWNUM <= #{LESS_NUM}]]>
            <include refid="NB_findWaiver" /> 
          <![CDATA[ORDER BY BP.APPLY_CODE) E
  	WHERE E.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_queryInsuredListBypolicyode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
          SELECT A.POLICY_CODE, A.LIST_ID,A.CUSTOMER_ID,A.POLICY_ID,C.CUSTOMER_NAME,C.CUSTOMER_CERT_TYPE,
		C.CUSTOMER_CERTI_CODE FROM DEV_PAS.T_INSURED_LIST A left join DEV_PAS.T_CUSTOMER C 
		on A.CUSTOMER_ID=C.CUSTOMER_ID where 1=1 AND A.POLICY_CODE=#{policy_code} 
		]]>
		
	</select>
	
		<select id="NB_findInsuredApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,A.INS_SPE_PEOPLE,A.DISABILITY_FLAG,A.RURAL_POPULATION_FLAG,A.INPUT_SEQUENCE,B.CUSTOMER_NAME,A.new_resident,
			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST A ,DEV_NB.T_CUSTOMER B WHERE 1 = 1 AND A.CUSTOMER_ID=b.CUSTOMER_ID ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.INPUT_SEQUENCE            ]]>
	</select>
</mapper>