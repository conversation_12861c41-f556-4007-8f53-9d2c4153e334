<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.qry.IUwPenoticeDao">
<!--
	<sql id="penoticeWhereCondition">
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" quality_evaluation  != null "><![CDATA[ AND A.QUALITY_EVALUATION = #{quality_evaluation} ]]></if>
		<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
		<if test=" pe_reason != null and pe_reason != ''  "><![CDATA[ AND A.PE_REASON = #{pe_reason} ]]></if>
		<if test=" result_magnum_msg != null and result_magnum_msg != ''  "><![CDATA[ AND A.RESULT_MAGNUM_MSG = #{result_magnum_msg} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" result_age  != null "><![CDATA[ AND A.RESULT_AGE = #{result_age} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" role_type  != null "><![CDATA[ AND A.ROLE_TYPE = #{role_type} ]]></if>
		<if test=" result_cus_score != null and result_cus_score != ''  "><![CDATA[ AND A.RESULT_CUS_SCORE = #{result_cus_score} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" customer_birth  != null  and  customer_birth  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTH = #{customer_birth} ]]></if>
		<if test=" other_comments != null and other_comments != ''  "><![CDATA[ AND A.OTHER_COMMENTS = #{other_comments} ]]></if>
		<if test=" positive_indi  != null "><![CDATA[ AND A.POSITIVE_INDI = #{positive_indi} ]]></if>
		<if test=" result_pe_date  != null  and  result_pe_date  != ''  "><![CDATA[ AND A.RESULT_PE_DATE = #{result_pe_date} ]]></if>
		<if test=" uw_hospital_id  != null "><![CDATA[ AND A.UW_HOSPITAL_ID = #{uw_hospital_id} ]]></if>
		<if test=" result_tel != null and result_tel != ''  "><![CDATA[ AND A.RESULT_TEL = #{result_tel} ]]></if>
		<if test=" result_magnum_value != null and result_magnum_value != ''  "><![CDATA[ AND A.RESULT_MAGNUM_VALUE = #{result_magnum_value} ]]></if>
		<if test=" quality_comment != null and quality_comment != ''  "><![CDATA[ AND A.QUALITY_COMMENT = #{quality_comment} ]]></if>
		<if test=" result_comment != null and result_comment != ''  "><![CDATA[ AND A.RESULT_COMMENT = #{result_comment} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" gender_code  != null "><![CDATA[ AND A.GENDER_CODE = #{gender_code} ]]></if>
		<if test=" es_indi  != null "><![CDATA[ AND A.ES_INDI = #{es_indi} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryPenoticeByPenoticeIdCondition">
		<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
	</sql>
	<sql id="queryPenoticeByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>	
	            <sql id="queryPenoticeByDocListIdCondition">
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	</sql>	
					<sql id="UW_queryPenoticeByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	<sql id="UW_queryPenoticeByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="UW_queryPenoticeByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>
	<sql id="UW_queryPenoticeByDocListIdCondition">
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<if test=" document_no  != null "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
	</sql>
	
		<sql id="UW_queryPenoticeByPolicyCodeAndApplyCodeCondition">
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	
<!-- 查询个数操作 -->
	<select id="UW_findPenoticeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_PENOTICE A WHERE 1 = 1  
				AND APPLY_CODE=#{apply_code}
		]]>
	</select>
	
	<!-- 根据客户号查询体检人所有的体检记录 -->
	<select id="UW_findPenoticeByCusIdForDoc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT (SELECT (SELECT ST.TYPE_NAME
	                  FROM DEV_UW.T_UW_SOURCE_TYPE ST
		                 WHERE ST.UW_SOURCE_TYPE = T3.UW_SOURCE_TYPE) || '@' ||
		               (SELECT UU.USER_NAME
		                  FROM DEV_NB.T_UDMP_USER UU
		                 WHERE UU.USER_ID = T3.UW_USER_ID)
		          FROM DEV_UW.T_UW_MASTER T3
		         WHERE T3.UW_ID = T1.UW_ID
		           AND ROWNUM = 1) AS UW_SOURCE,
		       T1.PENOTICE_ID,
		       T2.BUSS_CODE AS APPLY_CODE,
		       T2.POLICY_CODE,
		       T2.DOCUMENT_NO,
		       T1.CUSTOMER_ID,
		       T1.CUSTOMER_NAME,
	           T1.GENDER_CODE,
	           T1.RESULT_AGE,
	           T1.CUSTOMER_BIRTH,
	           T1.RESULT_TEL,
		       T2.SEND_TIME,
		       T2.PRINT_TIME,
		       T1.IS_PHYSICAL,
		       T1.RESULT_PE_DATE,
		       T2.CLOSE_TIME,
		       T1.POSITIVE_INDI,
		       T2.DOC_LIST_ID,
		       (SELECT TH.HOSPITAL_CODE ||'-'||TH.HOSPITAL_NAME
		          FROM DEV_UW.T_UW_HOSPITAL UH,
		               DEV_UW.T_HOSPITAL    TH
		         WHERE UH.HOSPITAL_CODE = TH.HOSPITAL_CODE
		           AND UH.UW_HOSPITAL_ID = T1.UW_HOSPITAL_ID) AS HOSPITAL_NAME,
		        (SELECT UH.ACCOMPANYING_EXA_FLAG || '-' || UH.ACCOMPANYING_AVOID_FLAG || '-' || 
		      (SELECT TO_CHAR(WM_CONCAT(C.IMAGE_CODE)) FROM DEV_UW.T_HOSPITAL_ACCOMPANY C 
		      WHERE C.HOSPITAL_CODE = UH.HOSPITAL_CODE AND C.IMAGE_STATUS_CODE = 'A') FROM DEV_UW.T_UW_HOSPITAL UH, DEV_UW.T_HOSPITAL TH
				WHERE UH.HOSPITAL_CODE = TH.HOSPITAL_CODE  AND UH.UW_HOSPITAL_ID =T1.UW_HOSPITAL_ID) AS HOSPITAL_ACC_STR,
		    
		       T1.PE_FEE
		  FROM DEV_UW.T_PENOTICE T1, DEV_NB.T_DOCUMENT T2
		 WHERE T1.DOC_LIST_ID = T2.DOC_LIST_ID
		   AND T1.CUSTOMER_ID =
		       (SELECT T.CUSTOMER_ID
		          FROM DEV_UW.T_PENOTICE T
		         WHERE T.DOCUMENT_NO = #{document_no})
		     ORDER BY T2.SEND_TIME ]]>
	</select>
	
	
	
	<!-- 根据客户号查询体检人所有的体检记录 -->
	<sql id="UW_findPenoticeByCusIdForDoc1">
		<![CDATA[ SELECT (SELECT (SELECT ST.TYPE_NAME
	                  FROM DEV_UW.T_UW_SOURCE_TYPE ST
		                 WHERE ST.UW_SOURCE_TYPE = T3.UW_SOURCE_TYPE) || '@' ||
		               (SELECT UU.USER_NAME
		                  FROM DEV_UW.T_UDMP_USER UU
		                 WHERE UU.USER_ID = T3.UW_USER_ID)
		          FROM DEV_UW.T_UW_MASTER T3
		         WHERE T3.UW_ID = T1.UW_ID
		           AND ROWNUM = 1) AS UW_SOURCE,
		       T1.PENOTICE_ID,
		       T2.BUSS_CODE AS APPLY_CODE,
		       T2.POLICY_CODE,
		       T2.DOCUMENT_NO,
		       T1.CUSTOMER_ID,
		       T1.CUSTOMER_NAME,
	           T1.GENDER_CODE,
	           T1.RESULT_AGE,
	 	       T1.RESULT_COMPLETE,  	          
  
               T1.CUSTOMER_BIRTH,
	           T1.RESULT_TEL,
		       T2.SEND_TIME,
		       T2.PRINT_TIME,
		       T1.IS_PHYSICAL,
		       T1.RESULT_PE_DATE,
		       T2.CLOSE_TIME,
		       T1.POSITIVE_INDI,
		       T2.DOC_LIST_ID,
		       (SELECT TH.HOSPITAL_CODE ||'-'||TH.HOSPITAL_NAME
		          FROM DEV_UW.T_UW_HOSPITAL UH,
		               DEV_UW.T_HOSPITAL    TH
		         WHERE UH.HOSPITAL_CODE = TH.HOSPITAL_CODE
		           AND UH.UW_HOSPITAL_ID = T1.UW_HOSPITAL_ID) AS HOSPITAL_NAME,
				(SELECT UH.ACCOMPANYING_EXA_FLAG || '-' ||
	                              UH.ACCOMPANYING_AVOID_FLAG || '-' ||
	                              (SELECT TO_CHAR(WM_CONCAT(C.IMAGE_CODE)) || '-' ||
	                                      TO_CHAR(WM_CONCAT(C.IMAGE_STATUS_CODE)) || '-' ||
	                                      TO_CHAR(WM_CONCAT(C.ACCOMPANY_NAME))
	                                 FROM APP___UW__DBUSER.T_HOSPITAL_ACCOMPANY C
	                                WHERE C.HOSPITAL_CODE = UH.HOSPITAL_CODE)
	                         FROM APP___UW__DBUSER.T_UW_HOSPITAL UH,
	                              APP___UW__DBUSER.T_HOSPITAL    TH
	                        WHERE UH.HOSPITAL_CODE = TH.HOSPITAL_CODE
	                          AND UH.UW_HOSPITAL_ID = T1.UW_HOSPITAL_ID) AS HOSPITAL_ACC_STR,
		       T1.PE_FEE
		  FROM DEV_UW.T_PENOTICE T1, DEV_NB.T_DOCUMENT T2
		 WHERE T1.DOC_LIST_ID = T2.DOC_LIST_ID
		   AND T1.CUSTOMER_ID =
		       (SELECT T.CUSTOMER_ID
		          FROM DEV_UW.T_PENOTICE T
		         WHERE T.DOCUMENT_NO = #{document_no})
		     ORDER BY T2.SEND_TIME ]]>
	</sql>
		<select id="findApplyPolicyInfoByInsuredCustomerIdTotal1" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
    	SELECT COUNT(1)
	  	FROM
	  	(
	  	]]>
	  	<include refid="UW_findPenoticeByCusIdForDoc1" />
	  	 <![CDATA[ 
	  	) D
	  	]]>
	</select>
	
	  <select id="findApplyPolicyInfoByInsuredCustomerId1" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        select B.*，ROWNUM AS RN
          from (
         ]]>
         <include refid="UW_findPenoticeByCusIdForDoc1" />
         <![CDATA[ ) B ]]>
	</select>
	
	<select id="UW_findPenoticeByDocListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.DOCUMENT_NO,A.APPLY_CODE,A.DOC_LIST_ID, A.CUSTOMER_NAME, A.UW_ID, A.PE_REASON, A.PENOTICE_ID, A.RESULT_MAGNUM_MSG, A.CUSTOMER_ID, 
      A.RESULT_AGE, A.ROLE_TYPE,  A.RESULT_CUS_SCORE, A.POLICY_ID,  A.QUALITY_EVALUATION, A.ES_INDI,  A.UW_HOSPITAL_ID,
      A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.RESULT_TEL, A.PE_FEE,A.IS_PHYSICAL,
     A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT,A.GENDER_CODE,A.POLICY_CODE,a.is_change_flag,A.AETIOLOGY_ANEMIA, A.ACCOMPANYING_EXA_FLAG
	 FROM DEV_UW.T_PENOTICE   A WHERE 1 = 1   
     ]]>
		<include refid="UW_queryPenoticeByDocListIdCondition" />
		<include refid="UW_queryPenoticeByUwIdCondition" />  <!-- Modify by xuhp 契约、保全提交核保结论时校验体检通知书是否被修改 2015年12月10日 -->
		<include refid="UW_queryPenoticeByCustomerIdCondition" />
		<include refid="queryPenoticeByPenoticeIdCondition"/>
		<![CDATA[ ORDER BY A.PENOTICE_ID                ]]>
	</select>
</mapper>
