<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.NbBenefitInsuredDaoImpl">
<!--
	<sql id="NB_nbBenefitInsuredWhereCondition">
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" job_class  != null "><![CDATA[ AND A.JOB_CLASS = #{job_class} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" insert_timestamp  != null "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" soci_secu  != null "><![CDATA[ AND A.SOCI_SECU = #{soci_secu} ]]></if>
		
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="NB_queryByInsuredIdCondition">
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
	</sql>	
	<sql id="NB_queryByOrderIdCondition">
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="NB_queryByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>
	
<!-- 添加操作 -->
	<insert id="NB_addNbBenefitInsured"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id"> SELECT DEV_NB.S_T_NB_BENEFIT_INSURED.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_NB.T_NB_BENEFIT_INSURED       (
				 INSERT_TIME, PRODUCT_CODE, UPDATE_TIME, JOB_UNDERWRITE, INSURED_ID, RELATION_TO_INSURED_1,
				INSERT_TIMESTAMP, ORDER_ID, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, BUSI_ITEM_ID, 
				INSERT_BY, POLICY_ID, APPLY_CODE) 
			VALUES (
				  SYSDATE , #{product_code, jdbcType=VARCHAR} , SYSDATE , #{job_underwrite, jdbcType=NUMERIC} , #{insured_id, jdbcType=NUMERIC} ,#{relation_to_insured_1, jdbcType=VARCHAR}
				, SYSDATE , #{order_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , SYSDATE , #{busi_item_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}, #{apply_code, jdbcType=VARCHAR}) 
		 ]]>
	</insert>
<!-- 添加操作 -->
	<insert id="NB_addErrorCorrectBenefitInsured"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id"> SELECT DEV_NB.S_T_NB_BENEFIT_INSURED.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_NB.T_NB_BENEFIT_INSURED_CORRECT       (
				 INSERT_TIME, PRODUCT_CODE, UPDATE_TIME, JOB_UNDERWRITE, INSURED_ID, RELATION_TO_INSURED_1,
				INSERT_TIMESTAMP, ORDER_ID, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, BUSI_ITEM_ID, 
				INSERT_BY, POLICY_ID ) 
			VALUES (
				 SYSDATE , #{product_code, jdbcType=VARCHAR} , SYSDATE , #{job_underwrite, jdbcType=NUMERIC} , #{insured_id, jdbcType=NUMERIC} ,#{relation_to_insured_1, jdbcType=VARCHAR}
				, SYSDATE , #{order_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , SYSDATE , #{busi_item_id, jdbcType=NUMERIC} 
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="NB_deleteNbBenefitInsured" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_BENEFIT_INSURED        WHERE LIST_ID            = #{list_id           } ]]>
	</delete>
<!-- 删除操作 -->	
	<delete id="deleteBenefInsByPolicyIdAndInsured" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_BENEFIT_INSURED    A    WHERE 1=1 ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<include refid="NB_queryByOrderIdCondition" />
	</delete>
<!-- 删除险种纠错信息被保人关系操作 -->	
	<delete id="deleteErrorCorrectBenefInsByPolicyIdAndInsured" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_BENEFIT_INSURED_CORRECT    A    WHERE 1=1 ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<include refid="NB_queryByOrderIdCondition" />
	</delete>
<!-- 修改操作 -->
	<update id="NB_updateNbBenefitInsured" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_BENEFIT_INSURED        ]]>
		<set>
		<trim suffixOverrides=",">
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    JOB_UNDERWRITE = #{job_underwrite, jdbcType=NUMERIC} ,
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
		    INSERT_TIMESTAMP = #{insert_timestamp, jdbcType=NUMERIC} ,
		    ORDER_ID = #{order_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = #{update_timestamp, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID            = #{list_id           } ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="NB_findByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findByInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByInsuredIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<!-- 按被保人id和policyId查询操作 -->	
	<select id="findBusiProdByPolicyIdAndInsured" resultType="java.util.Map" parameterType="java.util.Map">
	<!-- DEV_NB.T_BUSINESS_PRODUCT表的外键为BUSINESS_PRD_ID，但与T_NB_CONTRACT_BUSI_PROD的BUSI_PRD_ID字段不对应，所以先用B表的PRODUCT_CODE_STD字段关联 -->
		<![CDATA[ SELECT 
			A.BUSI_PRD_ID,  A.BUSI_ITEM_ID, B.PRODUCT_NAME_SYS,C.INSURED_ID,B.BUSINESS_PRD_ID,
			A.POLICY_ID FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD A , DEV_NB.T_BUSINESS_PRODUCT B , DEV_NB.T_NB_BENEFIT_INSURED C WHERE 1 = 1  AND A.BUSI_ITEM_ID =C.BUSI_ITEM_ID AND 
			A.BUSI_PRD_ID = B.BUSINESS_PRD_ID AND C.POLICY_ID = #{policy_id} AND C.INSURED_ID = #{insured_id}]]>
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID       ]]>
		<!-- <![CDATA[ SELECT 
			A.BUSI_PRD_ID,  A.BUSI_ITEM_ID, B.PRODUCT_NAME_SYS,C.INSURED_ID,B.BUSINESS_PRD_ID,
			A.POLICY_ID FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD A , DEV_NB.T_BUSINESS_PRODUCT B , DEV_NB.T_NB_BENEFIT_INSURED C WHERE 1 = 1  AND A.BUSI_ITEM_ID =C.BUSI_ITEM_ID AND 
			A.BUSI_PRD_ID = B.BUSINESS_PRD_ID AND C.POLICY_ID = #{policy_id} AND C.INSURED_ID = #{insured_id}]]>
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID       ]]> -->
	</select>
	<!-- 按被保人id  policyId查询操作   -->	
	<select id="NB_findBusiProdByPolicyIdAndInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select pro.PRODUCT_NAME_SYS ,
      				 prd.busi_item_id,prd.busi_prd_id
				  from DEV_NB.T_nb_contract_busi_prod prd, DEV_NB.T_BUSINESS_PRODUCT pro
				 where prd.policy_id = #{policy_id}
				   and pro.BUSINESS_PRD_ID = prd.busi_prd_id ]]>
		<![CDATA[ ORDER BY prd.BUSI_ITEM_ID       ]]>
	</select>
	<select id="NB_findInsuredByBusiItemIdAndPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select pro.PRODUCT_NAME_SYS ,
      				 prd.busi_item_id,prd.busi_prd_id
				  from DEV_NB.T_nb_contract_busi_prod prd, DEV_NB.T_BUSINESS_PRODUCT pro
				 where prd.policy_id = #{policy_id}
				   and pro.BUSINESS_PRD_ID = prd.busi_prd_id ]]>
		<![CDATA[ ORDER BY prd.BUSI_ITEM_ID       ]]>
	</select>
	
	<select id="NB_findByOrderId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID  FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByOrderIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID  FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<select id="NB_findByPolicyIdAndBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, A.RELATION_TO_INSURED_1, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID  FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>	
	<select id="NB_findByPolicyIdAndInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID  FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByInsuredIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>		
	

<!-- 按map查询操作 -->
	<select id="NB_findAllMapNbBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllNbBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findNbBenefitInsuredTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryNbBenefitInsuredForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,  B.PRODUCT_CODE, B.JOB_UNDERWRITE, B.INSURED_ID, 
			B.ORDER_ID, B.POLICY_CODE, B.LIST_ID, B.BUSI_ITEM_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN,  A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_BENEFIT_INSURED        A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 根据policyId,insuredId以及busiItemId确定唯一的一条数据 -->
	<select id="NB_findByPolicyIdAndBusiItemIdAndinsuredId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_BENEFIT_INSURED  A WHERE 1 = 1 AND A.INSURED_ID = #{insured_id} AND A.POLICY_ID = #{policy_id}
			 AND A.BUSI_ITEM_ID = #{busi_item_id}]]>
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>		
	
<!-- 根据policyId以及busiItemId确定唯一的一条数据 -->
	<select id="findBusiProdByPolicyIdAndBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID,A.RELATION_TO_INSURED_1, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_BENEFIT_INSURED  A WHERE 1 = 1 AND A.POLICY_ID = #{policy_id}
			 AND A.BUSI_ITEM_ID = #{busi_item_id}]]>
		<![CDATA[ ORDER BY A.ORDER_ID            ]]>
	</select>	
<!-- 根据险种busiItemId和policyId查询险种纠错被保人关系表 -->
	<select id="findErrorCorrectInsuredByPolicyIdAndBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID,A.RELATION_TO_INSURED_1, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_BENEFIT_INSURED_CORRECT  A WHERE 1 = 1 AND A.POLICY_ID = #{policy_id}
			 AND A.BUSI_ITEM_ID = #{busi_item_id}]]>
		<![CDATA[ ORDER BY A.ORDER_ID            ]]>
	</select>	
	
<!-- 根据policyId以及busiItemId删除险种下面的数据 -->	
	<delete id="NB_deleteNbBenefitInsuredByPolicyIdAndBusiItemId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_BENEFIT_INSURED    A    WHERE 1=1 AND A.POLICY_ID = #{policy_id} AND A.BUSI_ITEM_ID = #{busi_item_id}]]>
	</delete>		
	
	<select id="NB_queryNbBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_ITEM_ID,B.PRODUCT_CODE,C.PRODUCT_NAME_SYS,
		B.VALIDATE_DATE,B.EXPIRY_DATE FROM DEV_NB.T_NB_BENEFIT_INSURED A 
		LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD B ON A.BUSI_ITEM_ID=B.BUSI_ITEM_ID
		LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT C ON B.PRODUCT_CODE=C.PRODUCT_CODE_SYS WHERE 1 = 1 
		AND A.INSURED_ID = #{insured_id}
		]]>
		<![CDATA[ ORDER BY A.POLICY_ID            ]]>
	</select>	
	<!-- 删除操作 -->	
	<delete id="NB_deleteBenefInsByPolicyId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_BENEFIT_INSURED  WHERE POLICY_ID  = #{policy_id} ]]>
	</delete>
 <!-- 承保下 -->
	<select id="PA_queryNbBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	   SELECT A.BUSI_ITEM_ID,B.BUSI_PROD_CODE,C.PRODUCT_NAME_SYS,
		B.VALIDATE_DATE,B.EXPIRY_DATE FROM DEV_NB.T_NB_BENEFIT_INSURED A 
		LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD B ON A.BUSI_ITEM_ID=B.BUSI_ITEM_ID
		LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT C ON B.BUSI_PROD_CODE=C.PRODUCT_CODE_SYS WHERE 1 = 1 
	 AND A.INSURED_ID=#{insured_id}
	]]>
	<![CDATA[ ORDER BY A.POLICY_ID  ]]>
	</select>
</mapper>