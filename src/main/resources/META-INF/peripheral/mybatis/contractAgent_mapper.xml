<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.ContractAgentDaoImpl">
<!--
	<sql id="NB_contractAgentWhereCondition">
		<if test=" agent_type  != null "><![CDATA[ AND A.AGENT_TYPE = #{agent_type} ]]></if>
		<if test=" insert_timestamp  != null "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyIdCondition">
		<if test=" policy_id != null and policy_id != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="NB_queryByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>

<!-- 添加操作 -->
	<insert id="NB_addContractAgent"  useGeneratedKeys="true"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id"> SELECT DEV_NB.S_T_NB_CONTRACT_AGENT.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO    DEV_NB.T_NB_CONTRACT_AGENT    (
				INSERT_TIME, UPDATE_TIME, RELATION_TO_PH, AGENT_TYPE, APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY,  LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, AGENT_CODE ) 
			VALUES (
				SYSDATE, SYSDATE , #{relation_to_ph, jdbcType=VARCHAR} , #{agent_type, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , SYSDATE, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} ,  #{list_id, jdbcType=NUMERIC} , SYSDATE, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{agent_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="NB_deleteContractAgent" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_CONTRACT_AGENT            WHERE LIST_ID            = #{list_id           } ]]>
	</delete>

<!-- 修改操作 -->
	<update id="NB_updateContractAgent" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_AGENT            ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			RELATION_TO_PH = #{relation_to_ph, jdbcType=VARCHAR} ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID            = #{policy_id           } ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="NB_findByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_TYPE, A.POLICY_CODE, A.LIST_ID,
			A.RELATION_TO_PH, A.AGENT_CODE FROM DEV_NB.T_NB_CONTRACT_AGENT            A WHERE 1 = 1  ]]>
		<include refid="NB_queryByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findByAgentPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.RELATION_TO_PH, A.AGENT_CODE FROM DEV_NB.T_NB_CONTRACT_AGENT            A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE            ]]>
	</select>
	<select id="findContractAgentByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_PH, A.AGENT_TYPE, A.APPLY_CODE, A.POLICY_CODE, 
			A.LIST_ID, A.AGENT_CODE, A.POLICY_ID,b.AGENT_NAME ,b.AGENT_MOBILE ,b.AGENT_ORGAN_CODE  FROM DEV_NB.T_NB_CONTRACT_AGENT A left join DEV_PAS.T_AGENT b  on A.AGENT_CODE = b.AGENT_CODE   WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A. LIST_ID        ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="NB_findAllMapContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_TYPE, A.POLICY_CODE, A.LIST_ID,
			A.RELATION_TO_PH, A.AGENT_CODE FROM DEV_NB.T_NB_CONTRACT_AGENT            A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.RELATION_TO_PH, A.AGENT_CODE FROM DEV_NB.T_NB_CONTRACT_AGENT            A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findContractAgentTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_CONTRACT_AGENT            A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
	
	<!-- 查询个数操作 -->
	<select id="NB_findContractAgentNum" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_CONTRACT_AGENT            A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryContractAgentForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.AGENT_TYPE, B.POLICY_CODE, B.LIST_ID, 
			B.RELATION_TO_PH, B.AGENT_CODE FROM (
					SELECT ROWNUM RN, A.AGENT_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.RELATION_TO_PH, A.AGENT_CODE FROM DEV_NB.T_NB_CONTRACT_AGENT            A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<select id="NB_findContractAgentByAgentPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.RELATION_TO_PH,A.POLICY_ID, A.AGENT_CODE FROM DEV_NB.T_NB_CONTRACT_AGENT            A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE            ]]>
	</select>
	<!-- 根据投保单号或保单号查询业务员姓名 -->
	<select id="NB_findPolicyAndApplyAgentName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select distinct A.policy_Code,A.APPLY_CODE,A.AGENT_CODE,B.AGENT_NAME from DEV_NB.T_NB_CONTRACT_AGENT A,DEV_PAS.T_AGENT B
      where A.AGENT_CODE = B.AGENT_CODE  ]]>
      <include refid="NB_queryByPolicyAndApplyAgentCondition" />
	</select>
	<sql id="NB_queryByPolicyAndApplyAgentCondition">
		<if test=" apply_code != null and apply_code != '' and policy_code != null and policy_code != ''"><![CDATA[and ( A.APPLY_CODE=#{apply_code} or A.policy_Code=#{policy_code}) ]]></if>
		<if test=" (apply_code == null or apply_code == '') and policy_code != null and policy_code != '' "><![CDATA[and A.policy_Code=#{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != '' and (policy_code == null or policy_code == '') "><![CDATA[and A.APPLY_CODE=#{apply_code} ]]></if>
	</sql>
	<select id="queryContractAgent" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_CODE FROM DEV_NB.T_NB_CONTRACT_AGENT A WHERE 1 = 1  AND A.APPLY_CODE = #{apply_code} ]]>
	</select>
		<!-- 删除操作 -->
	<delete id="NB_deleteContractAgentByPolicyId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_CONTRACT_AGENT   WHERE POLICY_ID = #{policy_id} ]]>
	</delete>
	<!-- 承保下 -->
	<select id="PA_findByAgentByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.AGENT_CODE FROM DEV_PAS.T_contract_agent A WHERE 1=1 AND A.POLICY_CODE=#{policy_code} ]]>
		
		<![CDATA[ ORDER BY A.POLICY_CODE      ]]>
		
	</select>
			 <select id="findContResult" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[  SELECT A.RELATION_TO_PH as appntRelationToInsured,
    (SELECT DECODE(AA.LIABILITY_STATE, '1', '0;')||   --有效    
        DECODE(CsLock.SUB_ID, '068', '1;')||              --保全挂起   
        DECODE(AA.END_CAUSE, '03', '2;')||            --退保终止
        DECODE(AA.END_CAUSE, '02', '3;')||            --理赔终止
        DECODE(CLMLock.SUB_ID, '067', '4;')||              --理赔挂起          
        DECODE(CLMLock.LOCK_SERVICE_ID, '93', '5;')||      --理赔未决
        DECODE(AA.END_CAUSE, '08', '6;')||            --自垫终止
        DECODE(AA.LIABILITY_STATE, '4', '7;')||       --失效状态
        DECODE((SELECT COUNT(1)
                FROM DEV_CAP.V_PREM_ARAP G
               WHERE G.ARAP_FLAG in('1','2')
                 AND G.FEE_STATUS = '04'
                 AND G.POLICY_CODE = AA.POLICY_CODE),'0','','8;')|| --处于银行划款状态
        DECODE(AA.END_CAUSE, '06', '10;')||           --贷款终止                         
       DECODE((SELECT ACKNOWLEDGE_DATE FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT M WHERE M.POLICY_ID=AA.POLICY_ID),'','11;')--回执未签收状态
      FROM DEV_PAS.T_CONTRACT_MASTER AA
WHERE 1 = 1
AND A.POLICY_CODE=AA.POLICY_CODE) contState,
DECODE((select DISTINCT 1 FROM
               DEV_PAS.T_BUSINESS_PRODUCT S,DEV_PAS.T_CONTRACT_BUSI_PROD M
               WHERE S.BUSINESS_PRD_ID = M.BUSI_PRD_ID
                 AND S.PRODUCT_CODE_STD IN ('994', '995', '996')
                 AND M.POLICY_CODE = A.POLICY_CODE),'1','Y','0','N','','N') as isTax
 FROM DEV_PAS.T_INSURED_LIST A
LEFT JOIN 
(
SELECT MM.Sub_Id,BB.POLICY_ID FROm DEV_PAS.T_LOCK_POLICY BB
INNER JOIN DEV_PAS.T_LOCK_SERVICE_DEF MM
ON BB.LOCK_SERVICE_ID=MM.LOCK_SERVICE_ID
WHERE BB.POLICY_CODE=#{policyCode}  AND MM.SUB_ID='068'
) CsLock ON A.POLICY_ID=CsLock.Policy_Id
LEFT JOIN 
(
SELECT MM.SUB_ID,BB.POLICY_ID,BB.LOCK_SERVICE_ID FROm DEV_PAS.T_LOCK_POLICY BB
INNER JOIN DEV_PAS.T_LOCK_SERVICE_DEF MM
ON BB.LOCK_SERVICE_ID=MM.LOCK_SERVICE_ID
WHERE BB.POLICY_CODE=#{policyCode}  AND MM.SUB_ID='067'
) ClmLock ON A.POLICY_ID=ClmLock.Policy_Id
LEFT JOIN  DEV_PAS.T_BENEFIT_INSURED TB
          ON TB.INSURED_ID = A.LIST_ID
WHERE A.POLICY_CODE=#{policyCode} AND TB.ORDER_ID='1'
 ]]>

  </select>
		 <select id="findBusiPrdId" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ 
      SELECT A.BUSI_PRD_ID AS busiprdid FROM  DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE A.POLICY_CODE=#{policyCode} AND A.BUSI_ITEM_ID = #{busi_item_id}
 ]]>

  </select>	
  
		 <select id="findBusiPrdIdValue" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ 
 SELECT
 DECODE(A.TAX_REVENUE_FLAG,'1','Y','','N') isCrs --保单下存在CRS产品，就返回Y;否则返回N
 FROM DEV_PDS.T_BUSINESS_PRODUCT A  WHERE A.BUSINESS_PRD_ID = #{busiprdid}
 ]]>

  </select>	
  
  
  		 <select id="findPolicyType" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ 
SELECT DECODE((
SELECT 1 FROM  DEV_PAS.T_CUSTOMER A,
DEV_PAS.T_CUSTOMER_TAX B,
DEV_PAS.T_POLICY_HOLDER C
WHERE A.CUSTOMER_NAME=B.CUSTOMER_TAX_NAME 
AND A.CUSTOMER_GENDER=B.CUSTOMER_TAX_GENDER
AND A.CUSTOMER_BIRTHDAY=B.CUSTOMER_TAX_BIRTHDAY
AND A.CUSTOMER_CERT_TYPE=B.CUSTOMER_TAX_CERT_TYPE
AND A.CUSTOMER_CERTI_CODE=B.CUSTOMER_TAX_CERTI_CODE
AND C.CUSTOMER_ID=A.CUSTOMER_ID
AND C.POLICY_CODE=#{policyCode}),'1','Y','','N')  policyType FROM DUAL
 ]]>

  </select>	
  
  		 <select id="findInsuredType" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ 
SELECT DECODE((SELECT DISTINCT 1 FROM  DEV_PAS.T_CUSTOMER A,
DEV_PAS.T_INSURED_LIST B,
DEV_PAS.T_BENEFIT_INSURED C,
DEV_PAS.T_CUSTOMER_TAX D
WHERE  A.CUSTOMER_ID=B.CUSTOMER_ID 
AND B.LIST_ID=C.INSURED_ID
AND A.CUSTOMER_NAME=D.CUSTOMER_TAX_NAME
AND A.CUSTOMER_GENDER=D.CUSTOMER_TAX_GENDER
AND A.CUSTOMER_BIRTHDAY=D.CUSTOMER_TAX_BIRTHDAY
AND A.CUSTOMER_CERT_TYPE=D.CUSTOMER_TAX_CERT_TYPE
AND A.CUSTOMER_CERTI_CODE=D.CUSTOMER_TAX_CERTI_CODE
AND B.POLICY_CODE=#{policyCode}
AND C.ORDER_ID='1'),'1','Y','','N') insuredType FROM DUAL

 ]]>

  </select>	
	 <!-- 根据保单号查询代理人表信息(#rm133925)  -->
	<select id="findAgentByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.AGENT_NAME, A.AGENT_START_DATE, A.AGENT_MOBILE, A.RELATION_TO_PH, A.AGENT_TYPE, 
		A.APPLY_CODE, A.POLICY_CODE, A.AGENT_END_DATE, A.LIST_ID, A.AGENT_ORGAN_CODE, 
		A.POLICY_ID, A.AGENT_CODE ,A.IS_CURRENT_AGENT , A.IS_NB_AGENT,A.ORGAN_CODE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT A WHERE A.IS_CURRENT_AGENT = 1 AND A.POLICY_CODE = #{policy_code} ]]>
	</select>

</mapper>