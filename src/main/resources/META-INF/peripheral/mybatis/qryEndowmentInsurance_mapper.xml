<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 928卓越优选专属养老保险电话中心账户查询 -->
<mapper namespace="com.nci.tunan.nb.dao.impl.phoneCenterAccountDaoImpl">


	<!-- 专属养老新契约账户信息查询 -->
	<select id="qry_queryNbContractInvestInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STEADY_RETURNS_RATE,
				       A.POSITIV_ENTERPRISING_RATE,
				       B.INITIAL_COST,
				       B.DEDUCT,
				       (B.INITIAL_COST - B.DEDUCT) BALANCE,
				       C.STEADY_RATES,
				       C.POSITIV_RATES,
				       D.STEADY_RETURN_PREM,
				       <PERSON><PERSON>POSITIVE_PREM
				  FROM (SELECT #{policy_code} AS POLICY_CODE,
				               MAX(CASE
				                     WHEN ACCOUNT_CODE in ('928000','928100') THEN
				                      ASSIGN_RATE
				                     ELSE
				                      0
				                   END) * 100 STEADY_RETURNS_RATE, /*稳健回报型投资组合-分配比例*/
				               MAX(CASE
				                     WHEN ACCOUNT_CODE in ('928001','928101') THEN
				                      ASSIGN_RATE
				                     ELSE
				                      0
				                   END) * 100 POSITIV_ENTERPRISING_RATE /*积极进型投资组合-分配比例*/
				          FROM DEV_NB.T_NB_CONTRACT_INVEST_RATE CR
				           	INNER JOIN DEV_NB.T_NB_CONTRACT_PRODUCT NCP
                   	 			ON CR.ITEM_ID = NCP.ITEM_ID
                 			LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
                    			ON CP.APPLY_CODE = NCP.APPLY_CODE
                  				AND CP.PRODUCT_CODE = NCP.PRODUCT_CODE
				         WHERE CR.POLICY_CODE = #{policy_code}
				        ]]>
			          <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND  CP.BUSI_ITEM_ID IN
                  (SELECT P1.BUSI_ITEM_ID
                   FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>   
				       <![CDATA[  
				         ) A
				  LEFT JOIN (SELECT #{policy_code} AS POLICY_CODE,
				                    SUM(CASE
				                          WHEN TRANS_CODE = '52' Or  TRANS_CODE = '53' THEN
				                           TRANS_AMOUNT
				                          ELSE
				                           0
				                        END) INITIAL_COST, /*保单账户初始保险费*/
				                    SUM(CASE
				                          WHEN TRANS_CODE = '56'  THEN
				                           TRANS_AMOUNT
				                          ELSE
				                           0
				                        END) DEDUCT /*初始扣费金额*/
				               FROM DEV_PAS.T_FUND_GROUP_TRANS FGT
				              INNER JOIN DEV_PAS.T_CONTRACT_MASTER M
				                 ON FGT.POLICY_ID = M.POLICY_ID
				                 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD  TCBP
                                 ON TCBP.POLICY_CODE = M.POLICY_CODE AND FGT.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				              WHERE 1 = 1
				                AND M.POLICY_CODE = #{policy_code}
				                ]]>
			          <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND  TCBP.BUSI_ITEM_ID IN
                  (SELECT P1.BUSI_ITEM_ID
                   FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>   
				       <![CDATA[
				                AND FGT.TRANS_CODE IN ('52', '56','53')) B
				    ON A.POLICY_CODE = B.POLICY_CODE
				  LEFT JOIN (SELECT #{policy_code} AS POLICY_CODE,
				                    MAX(CASE
				                          WHEN ACCOUNT_CODE in ('928000','928100') THEN
				                           CR.GURNT_RATE
				                          ELSE
				                           0
				                        END) * 100 STEADY_RATES, /*稳健回报型投资组合-保证利率*/
				                    MAX(CASE
				                          WHEN ACCOUNT_CODE in ('928001','928101') THEN
				                           CR.GURNT_RATE
				                          ELSE
				                           0
				                        END) * 100 POSITIV_RATES /*积极进取型投资组合-保证利率*/
				               FROM DEV_PAS.T_CONTRACT_INVEST_RATE CR
				              INNER JOIN DEV_PAS.T_CONTRACT_MASTER M
				                 ON CR.POLICY_ID = M.POLICY_ID
			                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD  TCBP
                                 ON TCBP.POLICY_CODE = M.POLICY_CODE AND CR.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				              WHERE M.POLICY_CODE = #{policy_code}
				              ]]>
			          <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND  TCBP.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>   
				       <![CDATA[) C
				    ON B.POLICY_CODE = C.POLICY_CODE
				  LEFT JOIN (SELECT #{policy_code} AS POLICY_CODE,
				                    SUM(CASE
				                          WHEN FUND_CODE in ('928000','928100') THEN
				                           TRANS_AMOUNT
				                          ELSE
				                           0
				                        END) STEADY_RETURN_PREM, /*稳健回报型进入保费*/
				                     SUM(CASE
				                          WHEN FUND_CODE in ('928001','928101') THEN
				                           TRANS_AMOUNT
				                          ELSE
				                           0
				                        END) POSITIVE_PREM /*积极进取型进入保费*/
				               FROM DEV_PAS.T_FUND_TRANS FGT
				              INNER JOIN DEV_PAS.T_CONTRACT_MASTER M
				                 ON FGT.POLICY_ID = M.POLICY_ID
				                 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD  TCBP
				                 ON TCBP.POLICY_CODE = M.POLICY_CODE AND TCBP.BUSI_ITEM_ID = FGT.BUSI_ITEM_ID
				              WHERE 1 = 1
				                AND M.POLICY_CODE = #{policy_code}
				                AND (FGT.TRANS_CODE = '11' OR FGT.TRANS_CODE='47')]]>
			          <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[
			          AND  TCBP.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>   
				       <![CDATA[      ) D
				    ON C.POLICY_CODE = D.POLICY_CODE
		 ]]>
	</select>
	<!-- 专属养老历史账户结算信息 -->
	<select id="qry_queryHisAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT * FROM (
			SELECT (CASE 
					WHEN FT.TRANS_CODE='11' then '1'
					WHEN FT.TRANS_CODE='47' then '2'
					WHEN FT.TRANS_CODE='12' then '3'
					WHEN FT.TRANS_CODE='51' then '4'
					WHEN FT.TRANS_CODE='49' then '5'
					--WHEN FT.TRANS_CODE='' then '6'
					--WHEN FT.TRANS_CODE='' then '7'
					--WHEN FT.TRANS_CODE='' then '8'
					--WHEN FT.TRANS_CODE='' then '9'
					--WHEN FT.TRANS_CODE='' then '10'
					WHEN FT.TRANS_CODE='23' then '11'
					WHEN FT.TRANS_CODE='14' then '12'
					--WHEN FT.TRANS_CODE='' then '13'
					ELSE '' 
					END 
			        ) AS MONEY_TYPE, /*金额类型*/
			       TO_CHAR(FT.DEAL_TIME, 'yyyy-mm-dd') AS SETTLEMENT_DATE, /*结算日期*/
			       FT.TRANS_AMOUNT AS Money, /*金额*/
			       FT.TRANS_INTEREST * 100 AS SETTLEMENT_RATE /*结算利率*/
			FROM DEV_PAS.T_FUND_TRANS FT
			 INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			    ON CI.LIST_ID = FT.LIST_ID
			 INNER JOIN DEV_PAS.T_CONTRACT_MASTER M
			    ON CI.POLICY_ID = M.POLICY_ID
			 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
                 ON TCBP.POLICY_CODE = M.POLICY_CODE 
                 AND TCBP.BUSI_ITEM_ID = CI.BUSI_ITEM_ID
			 WHERE M.POLICY_CODE = #{policy_code}/*保单号*/
			   AND CI.ACCOUNT_CODE = #{account_code} /*入参AccountType为1，传入928000,为2，传入928001*/	
			   AND FT.TRANS_CODE  in ('11','47','12','51','49','23','14')		   
			   AND FT.DEAL_TIME >= to_date(#{start_settlement_date},'yyyy-MM-dd')
			   AND FT.DEAL_TIME <= to_date(#{end_settlement_date},'yyyy-MM-dd')  
		 ]]>
		 <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND TCBP.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>   
		 <![CDATA[ ) A WHERE 1=1]]>
		 <if test="money_type != null and money_type != '' "><![CDATA[AND A.MONEY_TYPE = #{money_type} /*金额类型,需要将传入的金额类型转换为新核心的trans_code的码值*/]]></if>
		 
		 <![CDATA[ 
		 union
			SELECT * FROM (
			SELECT '6' AS MONEY_TYPE, /*金额类型*/
			       TO_CHAR(tfs.SETTLE_DATE, 'yyyy-mm-dd') AS SETTLEMENT_DATE, /*结算日期*/
			       tfs.interest AS Money, /*金额*/
			       tfs.interest_rate * 100 AS SETTLEMENT_RATE /*结算利率*/
			FROM  DEV_PAS.t_fund_settlement tfs
			 INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			    ON CI.LIST_ID = tfs.INVEST_ID
			 INNER JOIN DEV_PAS.T_CONTRACT_MASTER M
			    ON CI.POLICY_ID = M.POLICY_ID
			 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
                 ON TCBP.POLICY_CODE = M.POLICY_CODE 
                 AND TCBP.BUSI_ITEM_ID = CI.BUSI_ITEM_ID
			 WHERE M.POLICY_CODE = #{policy_code}/*保单号*/
			   AND CI.ACCOUNT_CODE = #{account_code} /*入参AccountType为1，传入928000,为2，传入928001*/		   
			   AND tfs.SETTLE_DATE >= to_date(#{start_settlement_date},'yyyy-MM-dd')
			   AND tfs.SETTLE_DATE <= to_date(#{end_settlement_date},'yyyy-MM-dd')  
		 ]]>
		 <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND TCBP.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>   
		 <![CDATA[ ) A WHERE 1=1]]>
		 <if test="money_type != null and money_type != '' "><![CDATA[AND A.MONEY_TYPE = #{money_type} /*6-结算利息*/]]></if>
	</select>
	<!-- 最新账号信息查询-投资分配比例等基本信息 -->
	<select id="qry_queryNewAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.POLICY_ID,
		       A.STEADY_RETURNS_RATE,
		       A.POSITIV_ENTERPRISING_RATE,
		       (CASE
		         WHEN A.POSITIV_ENTERPRISING_RATE != '0' THEN
		          A.STEADY_RETURNS_RATE / A.POSITIV_ENTERPRISING_RATE
		         WHEN A.POSITIV_ENTERPRISING_RATE = '0' THEN
		          A.STEADY_RETURNS_RATE
		       END) AS PORTFOLIO_RATIO, /*保单账户-投资组合分配比例*/
		       (b.INITIAL_COSET - b.DEDUC) as FIREST_BALANCE, /*保单账户-期初保单价值*/
		       C.STEADY_LASTBALANCE,
		       C.POSITIVE_LASTBALANCE,
		       NVL(C.STEADY_LASTBALANCE,0)+ NVL(C.POSITIVE_LASTBALANCE,0) AS LAST_BALANCE/*保单账户-最新保单价值*/
		  FROM (SELECT #{policy_id} AS POLICY_ID,
		               MAX(CASE
		                    WHEN ACCOUNT_CODE in ('928000','928100') THEN
		                      ASSIGN_RATE
		                     ELSE
		                      0
		                   END) * 100 AS STEADY_RETURNS_RATE, /*稳健回报型投资组合-分配比例*/
		               MAX(CASE
		                     WHEN ACCOUNT_CODE in ('928001','928101') THEN
		                      ASSIGN_RATE
		                     ELSE
		                      0
		                   END) * 100 AS POSITIV_ENTERPRISING_RATE /*积极进型投资组合-分配比例*/
		          FROM DEV_PAS.T_CONTRACT_INVEST_RATE CR
		         WHERE CR.POLICY_ID = #{policy_id}]]>
		  <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND CR.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)  ]]></if>         
		  <![CDATA[ ) A
		  LEFT JOIN (SELECT #{policy_id} AS POLICY_ID,
		                    SUM(CASE
		                          WHEN TRANS_CODE = '52' Or  TRANS_CODE = '53'  THEN
		                           TRANS_AMOUNT
		                          ELSE
		                           0
		                        END) INITIAL_COSET, /*保单账户初始保险费*/
		                    SUM(CASE
		                          WHEN TRANS_CODE = '56' THEN
		                           TRANS_AMOUNT
		                          ELSE
		                           0
		                        END) DEDUC /*初始扣费金额*/
		               FROM DEV_PAS.T_FUND_GROUP_TRANS FGT
		              WHERE 1 = 1
		                AND FGT.POLICY_ID = #{policy_id}
		                AND FGT.TRANS_CODE IN ('52','53','56')]]>
		  <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND FGT.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>         
		  <![CDATA[    ) B
		    ON A.POLICY_ID = B.POLICY_ID
		  LEFT JOIN (SELECT #{policy_id} AS POLICY_ID,
		                    MAX(CASE
		                          WHEN CI.ACCOUNT_CODE in ('928000','928100') THEN
		                           CI.INTEREST_CAPITAL
		                          ELSE
		                           0
		                        END) STEADY_LASTBALANCE, /*稳健回报型投资组合账户-最新保单价值*/
		                    MAX(CASE
		                          WHEN CI.ACCOUNT_CODE in ('928001','928101') THEN
		                           CI.INTEREST_CAPITAL
		                          ELSE
		                           0
		                        END) POSITIVE_LASTBALANCE /*积极进取型投资组合账户-最新保单价值*/
		               FROM DEV_PAS.T_CONTRACT_INVEST CI
		              WHERE 1 = 1
		                AND CI.POLICY_ID = #{policy_id}]]>
		  <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND CI.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>         
		  <![CDATA[    ) C
		    ON B.POLICY_ID = C.POLICY_ID
		]]>
	</select>
	<!-- 最新账号信息查询-查询本月结算利息和本月结算利率 -->
	<select id="qry_queryAccountInterestInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CI.POLICY_ID,/*保单号*/
			       CI.ACCOUNT_CODE,/*账户类型*/
			       TO_CHAR(MAX(FS.Settle_Date), 'yyyy-MM-dd') AS DEAL_TIME,/*结算日期*/
			       SUM(FS.INTEREST) AS TRANS_AMOUNT,/*结算利息*/
			       SUM(FS.INTEREST_RATE) AS TRANS_PROPORTION /*结算利率*/
			  FROM DEV_PAS.T_FUND_SETTLEMENT FS
			 INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			    ON CI.LIST_ID = FS.INVEST_ID
			 WHERE 1 = 1
			   AND CI.POLICY_ID= #{policy_id}
			 GROUP BY CI.POLICY_ID,CI.Account_Code
		]]>
	</select>
	<!-- 查询组合账号保单价值等 -->
	<select id="qry_queryAccountCostInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CI.POLICY_ID,
			       CI.ACCOUNT_CODE,
			       FT.TRANS_CODE,
			       TO_CHAR(MAX(FT.DEAL_TIME)) AS DEAL_TIME,
			       SUM(FT.TRANS_AMOUNT) AS TRANS_AMOUNT
			  FROM DEV_PAS.T_FUND_TRANS FT
			 INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			    ON FT.LIST_ID = CI.LIST_ID
			 WHERE CI.POLICY_ID = #{policy_id}
			 AND FT.TRANS_CODE IN ('11','47')
			 GROUP BY CI.POLICY_ID, CI.ACCOUNT_CODE, FT.TRANS_CODE
			 UNION 
			 SELECT CI.POLICY_ID,
			       CI.ACCOUNT_CODE,
			       FT.TRANS_CODE,
			       TO_CHAR(MAX(FT.DEAL_TIME)) AS DEAL_TIME,
			       SUM(FT.TRANS_AMOUNT) AS TRANS_AMOUNT
			  FROM DEV_PAS.T_FUND_TRANS FT
			 INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			    ON FT.LIST_ID = CI.LIST_ID
			 WHERE CI.POLICY_ID = #{policy_id}
			  AND FT.DEAL_TIME>= to_date(#{start_settlement_date},'yyyy-MM-dd')
			  AND FT.DEAL_TIME< to_date(#{end_settlement_date},'yyyy-MM-dd')
			  AND FT.TRANS_CODE NOT  IN ('11','47')
			 GROUP BY CI.POLICY_ID, CI.ACCOUNT_CODE, FT.TRANS_CODE
		]]>
		
	</select>
	<!-- 最新账号信息查询-根据保单号查询保单id -->
	<select id="qry_findPolicyIdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCM.POLICY_ID,TCM.POLICY_CODE FROM DEV_PAS.T_CONTRACT_MASTER TCM WHERE TCM.POLICY_CODE = #{policy_code}]]>
	</select>
	<!-- 专属养老投资组合转入转出记录查询 -->
	<select id="qry_queryRollInOutInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT TO_CHAR(FT.DEAL_TIME,'yyyy-MM-dd') AS SETTLEMENT_DATE, /*结算日期*/
		       (CASE
		         WHEN FT.TRANS_CODE = '11' THEN
		          '1' /*1、保单生效日,首期期交保费变更类型对应的TRANS_CODE*/
		         WHEN FT.TRANS_CODE = '12' THEN
		          '2' /*期交保费缴费日,续期期交保费变更类型对应的TRANS_CODE,*/
		         WHEN FT.TRANS_CODE = '47' OR FT.TRANS_CODE = '49'  OR FT.TRANS_CODE = '51' THEN
		          '3' /*不定期交保费缴费日,首次不定期交保费,补交保费,不定期交保费三种变更类型对应的TRANS_CODE*/
		         WHEN FT.TRANS_CODE = '23'  OR FT.TRANS_CODE = '14'THEN
		          '4' /*投资组合转换日，投资组合转换-转出，投资组合转换-转入变更类型对应的2个TRANS_CODE*/
		       END) AS SETTLEMENT_TYPE, /*结算类型*/
		       (CASE
		         WHEN TRANS_CODE = '11' OR TRANS_CODE = '47'  OR FT.TRANS_CODE = '49' or TRANS_CODE = '12' OR FT.TRANS_CODE = '51' THEN
		          TRANS_AMOUNT
		         ELSE
		          0
		       END) INPUT_PREM, /*首期期交保费、续期期交保费、首次不定期交保费,补交保费,不定期交保费*/
		       (CASE
		         WHEN TRANS_CODE = '23' THEN
		          TRANS_AMOUNT
		         ELSE
		          0
		       END) ROLL_OUT_FEE, /*投资组合转换-转出*/
		       (CASE
		         WHEN TRANS_CODE = '14' THEN
		          TRANS_AMOUNT
		         ELSE
		          0
		       END) ROLL_IN_FEE /*投资组合转换-转入*/
		  FROM DEV_PAS.T_FUND_TRANS FT
		 INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
		    ON CI.LIST_ID = FT.LIST_ID
		 INNER JOIN DEV_PAS.T_CONTRACT_MASTER M
		    ON CI.POLICY_ID = M.POLICY_ID
		  INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
		    ON TCBP.POLICY_CODE = M.POLICY_CODE 
		    AND TCBP.BUSI_ITEM_ID = CI.BUSI_ITEM_ID
		 WHERE M.POLICY_CODE = #{policy_code}
		   AND CI.ACCOUNT_CODE = #{account_code} /*入参AccountType为1，传入928000,为2，传入928001*/
		  AND FT.TRANS_CODE IN ('11','12','47', '49','23','14','51')
		]]>
		<if test="main_pol_no != null and main_pol_no != '' ">AND TCBP.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID) </if>
		<if test="trans_code != null and trans_code != '' "> AND FT.TRANS_CODE=#{trans_code}</if>
	</select>
</mapper>
