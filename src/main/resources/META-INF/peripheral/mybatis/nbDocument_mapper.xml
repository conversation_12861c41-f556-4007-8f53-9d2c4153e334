<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.RecordDocumentDaoImpl">

	<select id="NB_findAllDocument" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT D.DOC_LIST_ID,D.BUSS_CODE,D.TEMPLATE_CODE,D.STATUS,D.SEND_BY,D.SEND_TIME,D.REPLY_TIME,D.REPLY_REMARK,C.AGENT_CODE ,D.BUSS_ID
			 FROM DEV_NB.T_DOCUMENT D INNER JOIN DEV_NB.T_NB_CONTRACT_AGENT C ON D.BUSS_CODE = C.APPLY_CODE WHERE 1=1]]>
		<if test=" buss_code != null and buss_code != ''  "><![CDATA[ AND D.BUSS_CODE = #{buss_code} ]]></if>
	</select>
	
	<select id="NB_findAllDecisionReason" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT P.Apply_Code,R.REASON_ID,R.UW_ID,R.UW_PRD_ID,R.LIMIT_ID,R.CONDITION_LIST_ID,R.EXTRA_PREM_ID,R.REASON_TYPE,R.REASON_CONTENT,R.UW_USER_CODE,P.INSERT_TIME
			 FROM DEV_UW.T_UW_DECISION_REASON R INNER JOIN DEV_UW.T_UW_POLICY P ON P.UW_ID = R.UW_ID WHERE 1=1]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND P.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND P.policy_code = #{policy_code} ]]></if>
	</select>
	
	<select id="NB_queryCsMedicalResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.BUSS_SOURCE_CODE,A.DOCUMENT_NO,A.CLOSE_TIME,A.CLOSE_BY,
			B.APPLY_CODE,B.CUSTOMER_NAME,B.RESULT_PE_DATE,
			(SELECT UW_USER_ID FROM DEV_UW.T_UW_MASTER C WHERE UW_ID=B.UW_ID) AS uw_user_id
			FROM DEV_NB.T_DOCUMENT A 
			LEFT JOIN DEV_UW.T_PENOTICE B ON A.DOC_LIST_ID=B.DOC_LIST_ID
			WHERE 1=1
			AND B.POLICY_CODE=#{policy_code} 
		]]>
		<if test=" document_no != null and document_no != ''  "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<if test=" buss_code != null and buss_code != ''  "><![CDATA[ AND A.BUSS_CODE = #{buss_code} ]]></if>
		
		<if test="customer_id_code != null and customer_id_code != ''">
			<![CDATA[ AND B.CUSTOMER_ID IN (SELECT TC.CUSTOMER_ID FROM DEV_PAS.T_CUSTOMER TC
				WHERE TC.CUSTOMER_ID_CODE = #{customer_id_code} 
				)]]>
		</if>
		
		<if test="customer_certi_code != null and customer_certi_code != ''">
			<![CDATA[ AND B.CUSTOMER_ID IN (SELECT TC.CUSTOMER_ID FROM DEV_PAS.T_CUSTOMER TC
				WHERE TC.CUSTOMER_NAME = #{customer_name} AND TC.CUSTOMER_BIRTHDAY =  TO_DATE(#{customer_birthday}, 'YYYY-MM-DD')
				AND TC.CUSTOMER_GENDER = #{customer_gender} AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type} 
				AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}
				)]]>
		</if>
	</select>
	
	
	<select id="PA_findDocument" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[
		   
       SELECT D.DOC_LIST_ID,
              D.BUSS_CODE,
              D.TEMPLATE_CODE,
              D.STATUS,
              D.SEND_BY,
              D.SEND_TIME,
              D.REPLY_TIME,
              D.REPLY_REMARK,
              C.AGENT_CODE,
              D.BUSS_ID
         FROM DEV_PAS.V_DOCUMENT_ALL D
        INNER JOIN DEV_PAS.T_contract_agent C
           ON D.POLICY_CODE = C.POLICY_CODE
        WHERE 1 = 1
	]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND D.policy_code = #{policy_code} ]]></if>
	</select>
	
	
	<select id="qry_findNote" resultType="java.util.Map" parameterType="java.util.Map">
	
		<if test="  flag == 'policy_code'  ">
		<![CDATA[
			SELECT N.NOTE_BUSI_CODE,
				N.CONTENT,
				N.PROCESS_CODE,
				N.NOTE_USER_CODE,
				N.NOTE_DATE
			FROM DEV_UW.T_NOTE N
			WHERE N.NOTE_BUSI_CODE = 
			(SELECT APPLY_CODE FROM DEV_PAS.T_CONTRACT_MASTER WHERE POLICY_CODE = #{policy_code})
		]]>
		</if>
		
		<if test="  flag == 'apply_code'  ">
		<![CDATA[
			SELECT N.NOTE_BUSI_CODE,
				N.CONTENT,
				N.PROCESS_CODE,
				N.NOTE_USER_CODE,
				N.NOTE_DATE
			FROM DEV_UW.T_NOTE N
			WHERE N.NOTE_BUSI_CODE = #{apply_code}
		]]>
		</if>
	</select>	
	
</mapper>