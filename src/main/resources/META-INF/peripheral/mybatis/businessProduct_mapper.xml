<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.BusinessProductDaoImpl">

	<sql id="PA_businessProductWhereCondition">
		<if test=" primary_sales_channel != null and primary_sales_channel != ''  "><![CDATA[ AND A.PRIMARY_SALES_CHANNEL = #{primary_sales_channel} ]]></if>
		<if test=" single_joint_life != null and single_joint_life != ''  "><![CDATA[ AND A.SINGLE_JOINT_LIFE = #{single_joint_life} ]]></if>
		<if test=" cover_period_type  != null "><![CDATA[ AND A.COVER_PERIOD_TYPE = #{cover_period_type} ]]></if>
		<if test=" product_category4 != null and product_category4 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY4 = #{product_category4} ]]></if>
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
		<if test=" product_category3 != null and product_category3 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY3 = #{product_category3} ]]></if>
		<if test=" product_code_original != null and product_code_original != ''  "><![CDATA[ AND A.PRODUCT_CODE_ORIGINAL = #{product_code_original} ]]></if>
		<if test=" product_category2 != null and product_category2 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY2 = #{product_category2} ]]></if>
		<if test=" product_category1 != null and product_category1 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY1 = #{product_category1} ]]></if>
		<if test=" product_name_sys != null and product_name_sys != ''  "><![CDATA[ AND A.PRODUCT_NAME_SYS = #{product_name_sys} ]]></if>
		<if test=" product_abbr_name != null and product_abbr_name != ''  "><![CDATA[ AND A.PRODUCT_ABBR_NAME = #{product_abbr_name} ]]></if>
		<if test=" product_static_code != null and product_static_code != ''  "><![CDATA[ AND A.PRODUCT_STATIC_CODE = #{product_static_code} ]]></if>
		<if test=" insured_count_max  != null "><![CDATA[ AND A.INSURED_COUNT_MAX = #{insured_count_max} ]]></if>
		<if test=" insured_count_min  != null "><![CDATA[ AND A.INSURED_COUNT_MIN = #{insured_count_min} ]]></if>
		<if test=" product_desc != null and product_desc != ''  "><![CDATA[ AND A.PRODUCT_DESC = #{product_desc} ]]></if>
		<if test=" release_date  != null  and  release_date  != ''  "><![CDATA[ AND A.RELEASE_DATE = #{release_date} ]]></if>
		<if test=" premium_rate_layer != null and premium_rate_layer != ''  "><![CDATA[ AND A.PREMIUM_RATE_LAYER = #{premium_rate_layer} ]]></if>
		<if test=" premium_currency != null and premium_currency != ''  "><![CDATA[ AND A.PREMIUM_CURRENCY = #{premium_currency} ]]></if>
		<if test=" product_name_std != null and product_name_std != ''  "><![CDATA[ AND A.PRODUCT_NAME_STD = #{product_name_std} ]]></if>
		<if test=" product_code_std != null and product_code_std != ''  "><![CDATA[ AND A.PRODUCT_CODE_STD = #{product_code_std} ]]></if>
		<if test=" renew_option != null and renew_option != ''  "><![CDATA[ AND A.RENEW_OPTION = #{renew_option} ]]></if>
		<if test=" product_category != null and product_category != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY = #{product_category} ]]></if>
		<if test=" schedule_rate  != null "><![CDATA[ AND A.SCHEDULE_RATE = #{schedule_rate} ]]></if>
		<if test=" product_code_sys != null and product_code_sys != ''  "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code_sys} ]]></if>
		<if test=" renewal_max_age != null and renewal_max_age != ''  "><![CDATA[ AND A.RENEWAL_MAX_AGE = #{renewal_max_age} ]]></if>
		<if test=" waiver_flag != null and waiver_flag != ''  "><![CDATA[ AND A.WAIVER_FLAG = #{waiver_flag} ]]></if>
	</sql>

	<select id="PA_findBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE FROM DEV_PAS.T_BUSINESS_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_businessProductWhereCondition" />		 
	</select>
	
	<select id="PAandNB_findBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		 <if test="  type != null and type == '1'.toString() ">
		 <![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE FROM DEV_PDS.T_BUSINESS_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_businessProductWhereCondition" />	
		</if>
		
		<if test="  type != null and type == '0'.toString() ">
		 <![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE FROM DEV_PDS.T_BUSINESS_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_businessProductWhereCondition" />
		</if>	 
	</select>
	
	<select id="PA_findBusinessProductQryOfNb" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
		A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
		A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
		A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.WAIVER_CUSTOMER_ROLE FROM dev_pds.T_BUSINESS_PRODUCT A WHERE 1 = 1  ]]>
	 <include refid="PA_businessProductWhereCondition" />		 
	</select>
	<select id="UW_findBusinessProductByWaiver" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
		A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
		A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
		A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.WAIVER_CUSTOMER_ROLE FROM dev_pds.T_BUSINESS_PRODUCT A WHERE 1 = 1 ]]>
	    <if test=" waiver_flag != null and waiver_flag != ''  "><![CDATA[ AND A.waiver_flag = #{waiver_flag} ]]></if>
	  	<if test=" waiver_customer_role != null and waiver_customer_role != ''  "><![CDATA[ AND A.WAIVER_CUSTOMER_ROLE = #{waiver_customer_role} ]]></if>
		<if test=" waiver_customer_roles != null "> AND A.WAIVER_CUSTOMER_ROLE IN 
		    <foreach item="item" index="index" collection="waiver_customer_roles"  open="(" separator="," close=")">
			  #{item}
            </foreach>
	    </if>
	</select>
</mapper>
