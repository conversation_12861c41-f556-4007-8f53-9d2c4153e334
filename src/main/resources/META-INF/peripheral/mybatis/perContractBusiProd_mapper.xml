<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractBusiProd">

	<select id="PA_findContractBeneByApplyCodeAndRiskCode"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 
			       cb.bene_type as bnftype ,
			       (select c.customer_name from dev_pas.t_customer c where c.customer_id = cb.customer_id) as name,
			       (select c.customer_gender from dev_pas.t_customer c where c.customer_id = cb.customer_id) as sex,
			       (select c.customer_birthday from dev_pas.t_customer c where c.customer_id = cb.customer_id) as birthday,
			       (select c.customer_cert_type from dev_pas.t_customer c where c.customer_id = cb.customer_id) as idtype,
			       (select c.customer_certi_code from dev_pas.t_customer c where c.customer_id = cb.customer_id) as idno,
			       cb.designation  as relationtoinsured,
			       cb.share_order as occupationtype,
			       '' as customertype,
			       (select c.customer_name from dev_pas.t_insured_list il,dev_pas.t_customer c where il.list_id = cb.insured_id and il.customer_id = c.customer_id and rownum=1) as insuredname,
			       (select c.country_code from dev_pas.t_customer c where c.customer_id = cb.customer_id) as appntnativeplace,
			       (select c.cust_cert_star_date from dev_pas.t_customer c where c.customer_id = cb.customer_id) as appntideffstartdate,
			       (select c.cust_cert_end_date from dev_pas.t_customer c where c.customer_id = cb.customer_id) as appntideffenddate,
			       'iflongflag' as iflongflag,
			       cb.share_rate as bnflotnumerator,
			       (select c.job_code from dev_pas.t_customer c where c.customer_id = cb.customer_id) as occupationcode,
			       (select c.job_title from dev_pas.t_customer c where c.customer_id = cb.customer_id) as occupationcodename,
			       (select c.mobile_tel from dev_pas.t_customer c where c.customer_id = cb.customer_id) as mobile,
			       (select a.state from DEV_PAS.T_ADDRESS a where a.address_id = cb.address_id) as provincename,
			       (select a.post_code from DEV_PAS.T_ADDRESS a where a.address_id = cb.address_id) as zipcode
			  	from dev_pas.t_contract_bene cb
			 	where exists
				 	(select 1 from dev_pas.t_contract_master cm,dev_pas.t_contract_busi_prod cbp where cm.policy_id = cb.policy_id and cm.policy_code = #{policyCode}
				 	and cm.policy_id = cbp.policy_id and cbp.busi_item_id = cb.busi_item_id  and cbp.busi_prod_code= #{riskCode})
		]]>
	</select>
	
	<select id="NA_findContractBeneByApplyCodeAndRiskCode"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 
             cb.bene_type as bnftype ,
             (select c.customer_name from dev_nb.t_customer c where c.customer_id = cb.customer_id) as name,
             (select c.customer_gender from dev_nb.t_customer c where c.customer_id = cb.customer_id) as sex,
             (select c.customer_birthday from dev_nb.t_customer c where c.customer_id = cb.customer_id) as birthday,
             (select c.customer_cert_type from dev_nb.t_customer c where c.customer_id = cb.customer_id) as idtype,
             (select c.customer_certi_code from dev_nb.t_customer c where c.customer_id = cb.customer_id) as idno,
             cb.designation  as relationtoinsured,
             cb.share_order as occupationtype,
             '' as customertype,
             (select c.customer_name from dev_nb.t_nb_insured_list il,dev_nb.t_customer c where il.list_id = cb.insured_id and il.customer_id = c.customer_id and rownum=1) as insuredname,
             (select c.country_code from dev_nb.t_customer c where c.customer_id = cb.customer_id) as appntnativeplace,
             (select c.cust_cert_star_date from dev_nb.t_customer c where c.customer_id = cb.customer_id) as appntideffstartdate,
             (select c.cust_cert_end_date from dev_nb.t_customer c where c.customer_id = cb.customer_id) as appntideffenddate,
             'iflongflag' as iflongflag,
             cb.share_rate as bnflotnumerator,
             (select c.job_code from dev_nb.t_customer c where c.customer_id = cb.customer_id) as occupationcode,
             (select c.job_title from dev_nb.t_customer c where c.customer_id = cb.customer_id) as occupationcodename,
             (select c.mobile_tel from dev_nb.t_customer c where c.customer_id = cb.customer_id) as mobile,
             (select a.state from dev_nb.t_address a where a.address_id = cb.address_id) as provincename,
             (select a.post_code from dev_nb.t_address a where a.address_id = cb.address_id) as zipcode
          from dev_nb.t_nb_contract_bene cb
			   where exists
          (select 1 from dev_nb.t_nb_contract_master cm,dev_nb.t_nb_contract_busi_prod cbp where cm.policy_id = cb.policy_id and cm.apply_code =#{applyCode}
          and cm.policy_id = cbp.policy_id and cbp.busi_item_id = cb.busi_item_id  and cbp.PRODUCT_CODE=#{riskCode}
          )]]>
	</select>
	
	<select id="PA_findRiskInfoByApplyCodeAndRiskCode"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 
		        (select cp.coverage_period from dev_pas.t_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id and rownum=1) as insuyearflag,
		        (select cp.coverage_year from dev_pas.t_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id and rownum=1) as insuyear,
		        (select pa.pay_mode from dev_pas.t_payer_account pa where pa.policy_id = cbp.policy_id and rownum=1) as paymode,
		        (select cp.charge_year from dev_pas.t_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id and rownum=1) as payintv,
		        (select sum(cp.amount) from dev_pas.t_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id) as amnt,  
		        (select sum(cp.total_prem_af) from dev_pas.t_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id)  as prem
		          from dev_pas.t_contract_busi_prod cbp
		         where exists (select 1
		                  from dev_pas.t_contract_master    cm
		                 where cm.policy_id = cbp.policy_id
		                   and cm.policy_code = #{policyCode}) and cbp.busi_prod_code =  #{riskCode}
			
		]]>
	</select>
	
	<select id="NA_findRiskInfoByApplyCodeAndRiskCode"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 
			  (select cp.coverage_period from dev_nb.t_nb_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id and rownum=1) as insuyearflag,
			  (select cp.coverage_year from dev_nb.t_nb_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id and rownum=1) as insuyear,
			  (select pa.pay_mode from dev_nb.t_nb_payer_account pa where pa.policy_id = cbp.policy_id and rownum=1) as paymode,
			  (select cp.charge_year from dev_nb.t_nb_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id and rownum=1) as payintv,
			  (select sum(cp.amount) from dev_nb.t_nb_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id) as amnt,  
			  (select sum(cp.total_prem_af) from dev_nb.t_nb_contract_product cp where cp.busi_item_id = cbp.busi_item_id and cp.policy_id = cbp.policy_id)  as prem
			    from dev_nb.t_nb_contract_busi_prod cbp
			   where exists (select 1
			    from dev_nb.t_nb_contract_master    cm
			   where cm.policy_id = cbp.policy_id
			     and cm.apply_code = #{applyCode}) and cbp.product_code =  #{riskCode}
		]]>
	</select>
	
	<select id="PA_findSpecialInfoByApplyCodeAndRiskCode"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select  '',
       				uw_condition_type,
       				condition_desc
 			from dev_pas.t_policy_condition pc where exists
				(select 1 from dev_pas.t_contract_master cm,dev_pas.t_contract_busi_prod cbp where cm.policy_id = pc.policy_id and cm.policy_code = #{policyCode}
 				and cm.policy_id = cbp.policy_id and cbp.busi_item_id =  pc.busi_item_id and cbp.busi_prod_code= #{riskCode})
		]]>
	</select>
	
	<!-- 未承包特约内容 -->
	<select id="NB_findSpecialInfoByApplyCodeAndRiskCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		  select  '',
              uw_condition_type,
              condition_desc
      from dev_nb.t_nb_policy_condition pc where exists
        (select 1 from dev_nb.t_nb_contract_master cm,dev_nb.t_nb_contract_busi_prod cbp where cm.policy_id = pc.policy_id and cm.apply_code =#{applyCode}
        and cm.policy_id = cbp.policy_id and cbp.busi_item_id =  pc.busi_item_id and cbp.PRODUCT_CODE=#{riskCode})
		
		]]>
	
	</select>
	
</mapper>
