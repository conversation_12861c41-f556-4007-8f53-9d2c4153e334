<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwPenoticeResultDaoImpl">

<!-- 按索引生成的查询条件 -->
	<sql id="uw_queryByPenoticeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND TC.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" endorse_code != null and endorse_code != '' "><![CDATA[ AND TC.ENDORSE_CODE = #{endorse_code} ]]></if>
		<if test=" accept_id != null and accept_id != '' "><![CDATA[ AND TC.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" document_no != null and document_no != '' "><![CDATA[ AND TD.DOCUMENT_NO = #{document_no} ]]></if>
		<if test=" customer_id != null and customer_id != '' "><![CDATA[ AND TP.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	
	<select id="uw_findPenoticeInfo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[select TC.POLICY_ID,
       TC.ACCEPT_ID,
       TC.POLICY_CODE,
       TC.ENDORSE_CODE,
       TU.BIZ_CODE,
       TU.UW_SOURCE_TYPE,
       TU.UW_ID,
       TU.UW_USER_ID,
       TD.BUSS_SOURCE_CODE,
       TD.DOCUMENT_NO,
       TD.CLOSE_TIME,
       TD.CLOSE_BY,
       TP.APPLY_CODE,
       TP.CUSTOMER_ID,
       TP.CUSTOMER_NAME,
       TP.RESULT_PE_DATE
  from DEV_nb.T_DOCUMENT          TD, 
       DEV_UW.T_PENOTICE          TP, 
       DEV_UW.T_UW_MASTER         TU, 
       DEV_PAS.T_CS_POLICY_CHANGE TC, 
       DEV_PAS.t_cs_accept_change CA 
 where TD.DOC_LIST_ID = TP.DOC_LIST_ID
   AND TP.UW_ID = TU.UW_ID
   AND CA.accept_id = TC.accept_id
   AND TU.biz_code = CA.accept_code
   AND TU.UW_SOURCE_TYPE = '2'  ]]>
		<include refid="uw_queryByPenoticeCondition" />
	</select>
</mapper>