<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.PreAuditCauseDaoImpl">
<!--
	<sql id="NB_preAuditCauseWhereCondition">
		<if test=" insert_timestamp  != null "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" cause_code != null and cause_code != ''  "><![CDATA[ AND A.CAUSE_CODE = #{cause_code} ]]></if>
		<if test=" comments != null and comments != ''  "><![CDATA[ AND A.COMMENTS = #{comments} ]]></if>
		<if test=" audit_id  != null "><![CDATA[ AND A.AUDIT_ID = #{audit_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="NB_queryByAuditIdCondition">
		<if test=" audit_id  != null "><![CDATA[ AND A.AUDIT_ID = #{audit_id} ]]></if>
	</sql>

	<!-- 根据auditId查询所有操作 -->
	<select id="NBfindPreAuditCauseByAuditId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CAUSE_CODE FROM DEV_NB.T_PRE_AUDIT_CAUSE  A WHERE ROWNUM <=  1000  AND A.AUDIT_ID = #{audit_id} ]]>
	</select>
</mapper>
