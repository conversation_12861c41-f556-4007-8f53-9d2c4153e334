<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.IPolicyBonusDao">

<!-- 保单红利查询 ，修改原来的sql，修改红利信息查询，和返回条数不正确问题 回归曲线使用105提交-->
	<select id="findPolicyBoundsByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		
		  select tt.product_name_sys, tba.bonus_sa, tba.total_bonus_cv, tct.initial_amount, tct.product_id,pe.product_name,tba.stand_amount, 
      tba.policy_code,tba.busi_item_id,tcbp.busi_prod_code,tct.product_code,tba.valid_amount,tba.RATE_RELEASE_DATE,
      tba.ALLOCATE_DUE_DATE,tba.allocate_date,tba.terminal_bonus_rate,tba.bonus_rate,tba.BONUS_ALLOT,pe.internal_id,
      (select count(*) from dev_pas.t_bonus_allocate t where t.policy_code =#{policy_code} and to_char(t.allocate_date,'yyyy') = to_char(tba.allocate_date,'yyyy'))bonus_no
      from dev_pas.t_bonus_allocate tba,dev_pas.t_contract_busi_prod tcbp,
           dev_pas.t_contract_product tct,dev_pds.t_product_life pe,dev_pas.t_business_product tt
       where tba.policy_code = #{policy_code}
       and tcbp.busi_item_id = tba.busi_item_id
       and tcbp.busi_item_id = tct.busi_item_id
       and pe.product_id = tct.product_id
       and tcbp.busi_prod_code = tt.product_code_sys
       order by tba.allocate_date
       
       
     
 </select>
 
 <!-- 保单红利 -->
 <select id="findBountAllotByItemId" resultType="java.util.Map" parameterType="java.util.Map" >
 
 select  T2.BONUS_ALLOT, T2.ALLOCATE_DATE,T2.INSERT_TIME,T2.ALLOCATE_TYPE,
           to_char(ALLOCATE_DATE, 'yyyy') ACOUNTINGYEAR   
          from dev_pas.t_BONUS_ALLOCATE T2
        
         where T2.INSERT_TIME =(select max(T.INSERT_TIME) from dev_pas.T_BONUS_ALLOCATE T where T.ITEM_ID =#{item_id})
       and T2.ITEM_ID=#{item_id}
 </select>
 
 
 <!-- 查询最大分红日 -->
   
    <select id="findLastBonusInfo" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select rownum rn, b.*
			    from (select a.*
			            from dev_pas.T_BONUS_ALLOCATE a
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test=" allocate_type != null and allocate_type != ''  "><![CDATA[ AND A.ALLOCATE_TYPE = #{allocate_type} ]]></if>
     <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
     <if test="bonus_allot_list != null and bonus_allot_list.size()!=0">
		<![CDATA[AND A.bonus_allot IN ]]>
			<foreach collection ="bonus_allot_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
	 </if>
<!--       <if test=" special_bonus  != null "><![CDATA[ AND A.SPECIAL_BONUS = #{special_bonus} ]]></if> -->
			        <![CDATA[
			           order by a.allocate_date desc) b
			   where rownum = 1
          ]]>
    </select>
   
    <!-- 保单红利查询 -->
      
	<select id="findDividendRate" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT A.REV_BONUS_RATE, A.RATE_RELEASE_DATE
		  FROM dev_pds.T_REV_BONUS_RATE A
		 WHERE 1 = 1
		   AND A.BUSINESS_PRD_ID = #{BUSINESS_PRD_ID}
		   AND A.RATE_RELEASE_DATE BETWEEN #{START_DATE} AND #{END_DATE}
		   AND (A.CHARGE_YEAR IS NULL OR A.CHARGE_YEAR = #{CHARGE_YEAR})
		   AND A.RATE_TYPE = #{RATE_TYPE}
		   AND A.POLICY_YEAR =
		       (TO_CHAR(A.RATE_RELEASE_DATE, 'YYYY') - TO_CHAR(#{POLICY_YEAR}, 'YYYY'))
		   ORDER BY A.RATE_RELEASE_DATE
 	</select>
 	
 	<!-- 查询产品是否受权 -->
 		<select id="queryWhetherAccredit" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT  B.AUTH_MAIN_ID , B.AUTHO_ID , B.AUTHO_CODE
                   FROM    dev_pds.T_MMS_AUTH_LIST_INFO A LEFT JOIN dev_pds.T_MMS_AUTH_MAIN_INFO B 
                           ON A.AUTH_MINFO_ID = B.AUTH_MAIN_ID 
                           WHERE 1=1 AND B.AUTH_STATUS = '2' AND B.AUTH_FLAG = 'PRO' ]]>
	    <if test=" apply_date != null and apply_date != '' and apply_date != 'null' ">
	            <![CDATA[  AND ((#{apply_date} BETWEEN A.sale_start AND A.sale_end) OR (#{apply_date} > A.sale_start AND (A.sale_end is null)))]]>
	    </if>
	    <if test=" organ_code != null and organ_code != ''  ">
	    	<![CDATA[ AND (#{organ_code} LIKE '%'|| LTRIM(RTRIM(A.ORGAN_CODE, ' ') , ' ') ||'%' AND A.ORGAN_CODE is not null)]]>
	    </if>
	    <if test=" channel_code != null and channel_code != '' ">
	    	<![CDATA[ AND A.CHANNEL_CODE LIKE '%'|| LTRIM(RTRIM(#{channel_code}, ' ') , ' ') ||'%' ]]>
	    </if>
	    <if test=" bank_code != null and bank_code != ''  ">
	    	<![CDATA[ AND A.BANK_CODE LIKE '%'|| LTRIM(RTRIM(#{bank_code}, ' ') , ' ') ||'%' ]]>
	    </if>
	    <if test=" agent_code != null and agent_code != ''  ">
	    	<![CDATA[ AND A.AGENT_CODE LIKE '%'|| LTRIM(RTRIM(#{agent_code}, ' ') , ' ') ||'%' ]]>
	    </if>
	    <if test=" autho_code != null and autho_code != ''  ">
	    	<![CDATA[ AND B.AUTHO_CODE LIKE '%'|| LTRIM(RTRIM(#{autho_code}, ' ') , ' ') ||'%' ]]>
	    </if>
	    <if test=" autho_id != null ">
	    	<![CDATA[ AND B.AUTHO_ID = #{autho_id,jdbcType=NUMERIC} ]]>
	    </if>
	    <![CDATA[ ORDER BY B.AUTHO_CODE ]]>
	</select>
	<select id="findBonusRateInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.PAY_FREQ, A.END_DATE, A.POLICY_YEAR, 
			 A.START_DATE, A.REV_BONUS_RATE, A.RATE_TYPE, 
			A.CHARGE_YEAR, A.RATE_RELEASE_DATE FROM 	dev_pds.T_REV_BONUS_RATE A WHERE 1 = 1  ]]>
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
		<if test=" start_date  != null and end_date != null "><![CDATA[ AND A.RATE_RELEASE_DATE BETWEEN #{start_date} AND #{end_date}]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND (A.CHARGE_YEAR IS NULL OR A.CHARGE_YEAR = #{charge_year})]]></if>
		<if test=" rate_type  != null "><![CDATA[ AND A.RATE_TYPE = #{rate_type} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = (TO_CHAR(A.RATE_RELEASE_DATE, 'YYYY') - #{policy_year}) ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" pay_freq  != null "><![CDATA[ AND (A.PAY_FREQ IS NULL OR A.PAY_FREQ = #{pay_freq}) ]]></if>
		<if test=" is_policy_greater_insured  != null "><![CDATA[ AND A.IS_POLICY_GREATER_INSURED = #{is_policy_greater_insured} ]]></if>
		<![CDATA[ ORDER BY A.RATE_RELEASE_DATE desc ]]>
	</select>
	
</mapper>