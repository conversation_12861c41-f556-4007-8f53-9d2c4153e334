<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.IProductRenewDao">
 
<sql id="PA_queryCustomerByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
<!--保单号是否存在  -->
<select id="PA_contractmaster_cjk" resultType="java.util.Map" parameterType="java.util.Map">

select A.POLICY_CODE from dev_pas.t_contract_master A where A.POLICY_CODE=#{policy_code}
</select>
	
	
<!-- 保单号与险种是否匹配 -->	

<select id="PA_contractbusiprod_cjk" resultType="java.util.Map" parameterType="java.util.Map">

<![CDATA[
select  A.BUSI_PRD_ID, A.POLICY_CODE,A.BUSI_PROD_CODE,A.BUSI_ITEM_ID,
        A.maturity_date,A.VALIDATE_DATE,nvl(A.PAIDUP_DATE,A.MATURITY_DATE) as MATURITYDATE
   from dev_pas.T_CONTRACT_BUSI_PROD A, dev_pds.t_business_product B WHERE
       A.BUSI_PRD_ID=B.BUSINESS_PRD_ID  AND  B.RENEW_OPTION <>'0'
       and A.POLICY_CODE =#{policy_code}
       and A.BUSI_PROD_CODE =#{busi_prod_code}
      ]]>
</select>


   <!-- 查询被保人 -->
   <select id="queryBusiInsuredInfo_cjk" resultType="java.util.Map" parameterType="java.util.Map">
   
   select til.customer_id,til.list_id from DEV_PAS.t_insured_list til left join DEV_PAS.t_benefit_insured tbi 
			on til.list_id = tbi.insured_id where tbi.busi_item_id=#{busi_item_id} 
   </select>
   <!-- 查询客户 -->
   <select id="PA_findCustomerByCustomerId_cjk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="PA_queryCustomerByCustomerIdCondition" />
	</select>
	
	<!-- 查询是否过宽限期 -->
	
	<select id="PA_findPremArapByPolicyCode_cjk" resultType="java.util.Map" parameterType="java.util.Map" >
	
	SELECT 
 D.pay_end_date
 FROM 
 (select A.pay_end_date from dev_cap.V_PREM_ARAP  A WHERE A.POLICY_CODE=#{policy_code} and A.deriv_type='003'AND A.fee_status not in ('01','16','19')  

AND A.due_time=(select CASE WHEN B.MATURITY_DATE IS NOT NULL THEN B.MATURITY_DATE
 
       ELSE B.EXPIRY_DATE END AS MATURITY_DATE
       
  from dev_pas.T_CONTRACT_BUSI_PROD B
 where B.POLICY_CODE =#{policy_code}
   AND B.BUSI_PROD_CODE =#{busi_prod_code}) order by A.INSERT_TIME DESC ) D WHERE ROWNUM=1
	
	</select>
	
	<!-- 查询是否是主险 -->
	
	<select id="PA_findIfMasterRisk_cjk" resultType="java.util.Map" parameterType="java.util.Map" >
	
	select A.POLICY_CODE,A.BUSI_ITEM_ID,A.MASTER_BUSI_ITEM_ID from dev_pas.T_CONTRACT_BUSI_PROD A WHERE 

  A.POLICY_CODE =#{policy_code}
   and A.BUSI_PROD_CODE =#{busi_prod_code}
	
	</select>
	
	<!-- 查询核保决定 -->
	
	<select id="PA_findRenewDecision_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	
	select RENEW_DECISION from dev_pas.T_CONTRACT_PRODUCT A WHERE A.POLICY_CODE=#{policy_code} AND A.BUSI_ITEM_ID=

(select BUSI_ITEM_ID from dev_pas.T_CONTRACT_BUSI_PROD B where B.POLICY_CODE=#{policy_code} AND  B.BUSI_PROD_CODE=#{busi_prod_code})
	
	</select>
	
	<!-- 查询业务锁 -->
	<select id="PA_findLockPolicyByPolicyCode_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	
	select A.POLICY_CODE from  dev_pas.t_lock_policy A WHERE A.POLICY_CODE=#{policy_code}
	</select>
	<!-- 查询被保人年龄 -->
	<select id="PA_findInsureByPolicyCode_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	
	select til.customer_id,cus.customer_birthday from dev_pas.t_insured_list til, dev_pas.t_benefit_insured tbi,
 
 dev_pas.t_customer cus  where til.customer_id=cus.customer_id 
			and til.list_id = tbi.insured_id and  tbi.busi_item_id=#{busi_item_id}
	
	</select>
	<!-- 查询核保 -->
	<select id="PA_findUwBusiProd_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	
	select bf.DECISION_CODE from dev_uw.t_uw_busi_prod bf where bf.POLICY_CODE=#{policy_code} and  bf.busi_prod_code in ('00784000','00527000') and bf.decision_code in ('40','50')
	
	</select>
	
	<!-- 查询理赔 -->
	
	<select id="PA_findClmCase_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	
	 SELECT A.CLAIM_TYPE
   FROM dev_CLM.T_CLAIM_SUB_CASE A
  WHERE   
   A.CLAIM_TYPE='03'
   and  A.CASE_ID IN
        (select A.CASE_ID
           from dev_clm.T_CLAIM_CASE A
          WHERE A.INSURED_ID IN  (SELECT B.CUSTOMER_ID
                   FROM dev_clm.T_INSURED_LIST B
                  WHERE B.POLICY_CODE =#{policy_code})
         )
	
	</select>
	
	<!-- 查询理赔 -->
	
	<select id="PA_findClmCase2_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	
	 SELECT A.CLAIM_TYPE
   FROM dev_CLM.T_CLAIM_SUB_CASE A
  WHERE   
   A.CASE_ID IN
        (select A.CASE_ID
           from dev_clm.T_CLAIM_CASE A
          WHERE A.INSURED_ID IN  (SELECT B.CUSTOMER_ID
                   FROM dev_clm.T_INSURED_LIST B
                  WHERE B.POLICY_CODE =#{policy_code})
         )
	
	</select>
	<!-- 查询被保人最大续保年龄 -->
	
	<select id="PA_findInsuerListMaxAgeBYbusiprdid" resultType="java.util.Map" parameterType="java.util.Map">
	
	select a.INSURED_MAX_AGE,a.INSURED_MIN_AGE from dev_pds.t_business_product a WHERE a.business_prd_id=#{business_prd_id}
	
	</select>
</mapper>