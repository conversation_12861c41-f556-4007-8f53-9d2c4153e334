<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.PhysicalResultQueryDaoImpl">

    <!-- 查询T_PENOTICE 体检结果查询-保单号-->	
	<select id="queryPenoticeByContNoAndPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		     SELECT T1.UW_ID,
		            T1.PENOTICE_ID,
		            T1.APPLY_CODE,
		            T1.DOC_LIST_ID,
		            T1.CUSTOMER_NAME,
		            T1.RESULT_PE_DATE,
		            T1.GENDER_CODE,
		            T1.RESULT_AGE,
		            T1.RESULT_TEL,
		            T1.UW_HOSPITAL_ID,
		            T1.POSITIVE_INDI,
		            T2.UW_USER_ID
		       FROM DEV_UW.T_PENOTICE T1,DEV_UW.T_UW_MASTER  T2
		       WHERE T1.UW_ID=T2.UW_ID AND POLICY_CODE = #{policy_code}
		]]>
	</select>
	
	<!-- 查询T_PENOTICE 体检结果查询-投保单号-->	
	<select id="queryPenoticeByContNoAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		     SELECT T1.UW_ID,
		            T1.PENOTICE_ID,
		            T1.APPLY_CODE,
		            T1.DOC_LIST_ID,
		            T1.CUSTOMER_NAME,
		            T1.RESULT_PE_DATE,
		            T1.GENDER_CODE,
		            T1.RESULT_AGE,
		            T1.RESULT_TEL,
		            T1.UW_HOSPITAL_ID,
		            T1.POSITIVE_INDI,
		            T2.UW_USER_ID
		       FROM DEV_UW.T_PENOTICE T1,DEV_UW.T_UW_MASTER  T2
		       WHERE T1.UW_ID=T2.UW_ID AND APPLY_CODE = #{apply_code}
		]]>
	</select>
	
	<!-- 查询T_DOCUMENT -->
	<select id="queryDocunentByContNoAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		     SELECT BUSS_SOURCE_CODE, 
		            CLOSE_TIME, 
		            CLOSE_BY, 
		            PRINT_TIME
		       FROM DEV_NB.T_DOCUMENT
		       WHERE DOC_LIST_ID = #{doc_list_id}
		]]>
	</select>
	
	<!-- 查询T_HOSPITAL -->
	<select id="queryHospitalByUwhospitalId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		     SELECT T1.HOSPITAL_NAME, T1.HOSPITAL_CODE
		     FROM DEV_UW.T_HOSPITAL T1, DEV_UW.T_UW_HOSPITAL T2
		     WHERE T1.HOSPITAL_CODE = T2.HOSPITAL_CODE
		     AND T2.UW_HOSPITAL_ID = #{uw_hospital_id}
		]]>
	</select>
	
	<!-- 查询T_BODY_EXAM_CHARG_CONF -->
	<select id="queryCheckmoneyByContNoAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT SUM(A.STANDARD_FEE) AS STANDARD_FEE,
		           SUM(A.PROTOCOL_FEE) AS PROTOCOL_FEE 
		           FROM DEV_UW.T_BODY_EXAM_CHARG_CONF A 
		    	   WHERE 1 = 1  AND A.PEITEM_CODE IN (SELECT PEITEM_CODE FROM 
		    	   DEV_UW.T_PENOTICE_DETAIL B WHERE  1 = 1 
		           AND B.PENOTICE_ID = #{penotice_id})
		]]>
	</select>
	
	<!-- 查询 T_CONTRACT_BUSI_PROD-->
	<select id="queryRiskByContNoAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT T1.BUSI_PROD_CODE, SUM(T2.TOTAL_PREM_AF) AS FEE
		       FROM DEV_UW.T_CONTRACT_BUSI_PROD T1, DEV_UW.T_CONTRACT_PRODUCT T2
		       WHERE T1.BUSI_ITEM_ID = T2.BUSI_ITEM_ID
		]]>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND T1.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND T1.APPLY_CODE = #{apply_code} ]]></if>
		<![CDATA[ GROUP BY T1.BUSI_PROD_CODE ]]> 
		
	</select>
	
	<!-- 查询T_PHYSICAL_ITEM -->
	<select id="queryItemnameByContNoAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		     SELECT T2.PEITEM_NAME,T2.REMARK FROM DEV_UW.T_PENOTICE_DETAIL_ITEM T1,DEV_UW.T_PHYSICAL_ITEM T2  WHERE T1.PEITEM_CODE=T2.PEITEM_CODE AND T1.PENOTICE_ID=#{penotice_id}
		]]>
	</select>
	
	<!-- 查询T_PERESULT_DECLARE -->
	<select id="queryNoticeByContNoAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT DECL_ITEM,DECL_CONTENT FROM DEV_UW.T_PERESULT_DECLARE WHERE penotice_id=#{penotice_id}
		]]>
	</select>
	
	<!-- 查询T_PENOTICE_DETAIL_ITEM -->
	<select id="queryItemnameDetailByContNoAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    
		     SELECT PEITEM_CODE,
         (Select pi.peitem_name
            from dev_uw.t_physical_item pi
           where pi.peitem_code = a.PEITEM_CODE) peitem_name,
         PEITEM_DETAIL_CODE,
         (select pid.peitem_detail_name
            from dev_uw.t_physical_item_detail pid
           where pid.peitem_detail_code = a.peitem_detail_code
             and rownum = 1) peitem_detail_name,
         PE_BACK_INDI,
         PE_RESULT_VALUE,
         PE_MSG,
         UNIT,
         MIN,
         MAX
    FROM DEV_UW.T_PENOTICE_DETAIL_ITEM a
   WHERE PENOTICE_ID = #{penotice_id} 
		    
		]]>
	</select>
	
	<!-- 查询T_PHYSICAL_ITEM_detail 2018-07-20 -->
	<select id="queryItemnameDetailByContNoAndApplyCodeNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select a.peitem_detail_name
			  from dev_uw.t_physical_item_detail a
			   where a.peitem_detail_code=#{peitem_detail_code}
			 group by a.peitem_detail_name
		]]>
	</select>
	
	<!-- 查询T_CUSTOMER -->
	<select id="queryCustomberByCustomberId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		     select a.customer_id  from  DEV_pas.t_customer a where a.customer_id =#{customer_id}
		]]>
	</select>
	
</mapper>
