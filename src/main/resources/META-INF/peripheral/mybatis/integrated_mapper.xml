<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IIntegratedDao">

	<!-- 加费通知单号快查 -->
	<select id="QRY_findAddDocument" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  select count(1) from dev_nb.T_DOCUMENT A
 			where a.template_code in ('NBS_00001', 'UWS_00006')
   			and a.document_no = #{document_no}  ]]>
	</select>
	
	<!-- 催缴通知单号快查 -->
	<select id="QRY_findCallDocument" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  select count(1) from dev_nb.T_DOCUMENT A
 			where a.template_code = 'NBS_00008'
   			and a.document_no = #{document_no}  ]]>
	</select>
	
	 <!-- 交退费编号快查服务 -->
    <select id="QRY_countPremArapByBusinessCode" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
        	select COUNT(1) FROM  DEV_CAP.V_PREM_ARAP T WHERE T.UNIT_NUMBER = #{business_code, jdbcType=VARCHAR} 
        ]]>
    </select>
    
    <!-- 补费通知单号快查 -->
	<select id="QRY_findAddPremDocument" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  select count(1) from dev_nb.T_DOCUMENT A
 			where a.template_code = 'NBS_00001'
   			and a.document_no = #{document_no}  ]]>
	</select>
    
    <!-- 交退费编号快查服务 -->
    <select id="QRY_judgeSurveyNoExist" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ 
        	SELECT COUNT(1) FROM  APP___PAS__DBUSER.T_SURVEY_APPLY WHERE SURVEY_CODE=#{survey_code, jdbcType=VARCHAR} 
        ]]>
    </select>
    
	<!-- 保单号快查 -->
	<select id="QRY_findContractMasterPolicyCodeExist" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1) FROM dev_pas.T_CONTRACT_MASTER A WHERE A.POLICY_CODE=#{policy_code} ]]>
	</select>
	
	<!-- 保全受理号快查服务 -->
	<select id="QRY_findIsExistedAcceptService" resultType="java.lang.Integer" parameterType="java.util.Map">
	 <![CDATA[	select count(1) from dev_pas.T_CS_ACCEPT_CHANGE t where t.accept_code =  #{accept_code} ]]> 
 	</select>
    
    <!-- 核保通知书号快查服务 -->
	<select id="QRY_findUWDocumentQuick" resultType="java.lang.Integer" parameterType="java.util.Map">
	 <![CDATA[ select COUNT(1) FROM  DEV_NB.T_DOCUMENT A where A.DOCUMENT_NO = #{document_no}  ]]>
 	</select>
</mapper>

