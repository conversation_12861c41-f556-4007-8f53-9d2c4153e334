<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.NbContractProductDaoImpl">
<!--
	<sql id="NB_nbContractProductWhereCondition">
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" discnted_prem_af  != null "><![CDATA[ AND A.DISCNTED_PREM_AF = #{discnted_prem_af} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" master_product_code != null and master_product_code != ''  "><![CDATA[ AND A.MASTER_PRODUCT_CODE = #{master_product_code} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" renew  != null "><![CDATA[ AND A.RENEW = #{renew} ]]></if>
		<if test=" insert_timestamp  != null "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" extra_prem_af  != null "><![CDATA[ AND A.EXTRA_PREM_AF = #{extra_prem_af} ]]></if>
		<if test=" insured_category != null and insured_category != ''  "><![CDATA[ AND A.INSURED_CATEGORY = #{insured_category} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" health_service_flag != null and health_service_flag != ''  "><![CDATA[ AND A.HEALTH_SERVICE_FLAG = #{health_service_flag} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" unit  != null "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" initial_type  != null "><![CDATA[ AND A.INITIAL_TYPE = #{initial_type} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" short_end_time  != null  and  short_end_time  != ''  "><![CDATA[ AND A.SHORT_END_TIME = #{short_end_time} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" master_item_id  != null "><![CDATA[ AND A.MASTER_ITEM_ID = #{master_item_id} ]]></if>
		<if test=" decision_code  != null "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" count_way != null and count_way != ''  "><![CDATA[ AND A.COUNT_WAY = #{count_way} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" discnt_prem_af  != null "><![CDATA[ AND A.DISCNT_PREM_AF = #{discnt_prem_af} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByMasterItemIdCondition">
		<if test=" master_item_id  != null "><![CDATA[ AND A.MASTER_ITEM_ID = #{master_item_id} ]]></if>
	</sql>
	<sql id="NB_objectParameter_labelName">
		<if test=" label_name  != null "><![CDATA[ AND u.label_name = #{label_name} ]]></if>
	</sql>
	
	<sql id="NB_queryBybusinessProdIdCondition">
		<if test=" business_prod_id  != null "><![CDATA[ AND A.BUSINESS_PROD_ID = #{business_prod_id} ]]></if>
	</sql>	
	<sql id="NB_queryByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="NB_queryByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>
	<sql id="NB_queryByProductIdCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
	</sql>	
	<sql id="NB_queryByPOCondition">
		<if test=" product_code  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ 
			AND EXISTS(SELECT 1 FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD B WHERE B.APPLY_CODE = A.APPLY_CODE AND 
			B.BUSI_ITEM_ID = A.BUSI_ITEM_ID AND B.BUSI_PRD_ID = #{busi_prd_id})
		]]></if>
		<if test=" busi_prd_code  != null "><![CDATA[ 
			AND EXISTS(SELECT 1 FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD B WHERE B.APPLY_CODE = A.APPLY_CODE AND 
			B.BUSI_ITEM_ID = A.BUSI_ITEM_ID AND B.PRODUCT_CODE = #{busi_prd_code})
		]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="NB_queryByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="NB_queryByProductCodeCondition">
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
	</sql>	
	<sql id="NB_queryByapplyCodeCondition">
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="NB_queryByLiabilityStateCondition">
		<if test=" liability_state != null and liability_state != ''  "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
	</sql>  
<!-- 查询product_id 这个查询时为解决小电商解析xml时save责任组信息时精算产品ＩＤ-->
	<select id="NB_ProductID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID,A.COUNTER_WAY FROM DEV_NB.T_PRODUCT_LIFE      A WHERE INTERNAL_ID = #{internal_id} ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
<!-- 查询所有操作 -->
<!-- 添加操作 -->
	<insert id="NB_addNbContractProduct"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="item_id"> SELECT DEV_NB.S_t_nb_contract_product.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_NB.T_NB_CONTRACT_PRODUCT(
				BENEFIT_LEVEL, PRODUCT_ID, PRODUCT_CODE, PROD_PKG_PLAN_CODE, ITEM_ID, COVERAGE_PERIOD, 
				RENEWAL_EXTRA_PREM_AF, APPLY_CODE, INSERT_TIMESTAMP, UPDATE_BY, CHARGE_YEAR, EXTRA_PREM_AF, BUSI_ITEM_ID, 
				HEALTH_SERVICE_FLAG, POLICY_ID, UNIT, COVERAGE_YEAR, INITIAL_EXTRA_PREM_AF, INSERT_TIME, UPDATE_TIME, 
				AMOUNT, RENEWAL_DISCNTED_PREM_AF, TOTAL_PREM_AF, DECISION_CODE, EXPIRY_DATE, PAY_YEAR, LIABILITY_STATE, 
				CHARGE_PERIOD, PAY_PERIOD, STD_PREM_AF, COUNT_WAY, POLICY_CODE, IS_GIFT, VALIDATE_DATE, 
				BONUS_MODE_CODE, UPDATE_TIMESTAMP, INSERT_BY, PREM_FREQ, INITIAL_DISCNT_PREM_AF,INTEREST_MODE,PAIDUP_DATE,DEDUCTIBLE_FRANCHISE,PAYOUT_RATE,
				ADDITIONAL_PREM_AF,APPEND_PREM_AF, IS_WAIVED ,ANNU_PAY_TYPE, PAY_FREQ ) 
			VALUES (
				#{benefit_level, jdbcType=VARCHAR}, #{product_id, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{prod_pkg_plan_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{coverage_period, jdbcType=VARCHAR} 
				, #{renewal_extra_prem_af, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{health_service_flag, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , #{initial_extra_prem_af, jdbcType=NUMERIC} , SYSDATE , SYSDATE 
				, #{amount, jdbcType=NUMERIC} , #{renewal_discnted_prem_af, jdbcType=NUMERIC} , #{total_prem_af, jdbcType=NUMERIC} , #{decision_code, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} , #{pay_year, jdbcType=NUMERIC} , #{liability_state, jdbcType=NUMERIC} 
				, #{charge_period, jdbcType=VARCHAR} , #{pay_period, jdbcType=VARCHAR} , #{std_prem_af, jdbcType=NUMERIC} , #{count_way, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{is_gift, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} 
				, #{bonus_mode_code, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{prem_freq, jdbcType=NUMERIC} , #{initial_discnt_prem_af, jdbcType=NUMERIC},#{interest_mode, jdbcType=NUMERIC},#{paidup_date, jdbcType=DATE},#{deductible_franchise, jdbcType=NUMERIC},#{payout_rate, jdbcType=NUMERIC},
				#{additional_prem_af, jdbcType=NUMERIC},#{append_prem_af, jdbcType=NUMERIC}, #{is_waived, jdbcType=NUMERIC},  #{annu_pay_type, jdbcType=NUMERIC}, #{pay_freq, jdbcType=VARCHAR}) 
		 ]]>
	</insert>
		<insert id="NB_addNbErrorCorrectProduct"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="item_id"> SELECT DEV_NB.S_t_nb_contract_product.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_NB.T_NB_CONTRACT_PRODUCT_CORRECT(
				BENEFIT_LEVEL, PRODUCT_ID, PRODUCT_CODE, PROD_PKG_PLAN_CODE, ITEM_ID, COVERAGE_PERIOD, 
				RENEWAL_EXTRA_PREM_AF, APPLY_CODE, INSERT_TIMESTAMP, UPDATE_BY, CHARGE_YEAR, EXTRA_PREM_AF, BUSI_ITEM_ID, 
				HEALTH_SERVICE_FLAG, POLICY_ID, UNIT, COVERAGE_YEAR, INITIAL_EXTRA_PREM_AF, INSERT_TIME, UPDATE_TIME, 
				AMOUNT, RENEWAL_DISCNTED_PREM_AF, TOTAL_PREM_AF, DECISION_CODE, EXPIRY_DATE, PAY_YEAR, LIABILITY_STATE, 
				CHARGE_PERIOD, PAY_PERIOD, STD_PREM_AF, COUNT_WAY, POLICY_CODE, IS_GIFT, VALIDATE_DATE, 
				BONUS_MODE_CODE, UPDATE_TIMESTAMP, INSERT_BY, PREM_FREQ, INITIAL_DISCNT_PREM_AF,INTEREST_MODE,PAIDUP_DATE,DEDUCTIBLE_FRANCHISE,PAYOUT_RATE,
				ADDITIONAL_PREM_AF,APPEND_PREM_AF ) 
			VALUES (
				#{benefit_level, jdbcType=VARCHAR}, #{product_id, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{prod_pkg_plan_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , #{coverage_period, jdbcType=VARCHAR} 
				, #{renewal_extra_prem_af, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{health_service_flag, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , #{initial_extra_prem_af, jdbcType=NUMERIC} , SYSDATE , SYSDATE 
				, #{amount, jdbcType=NUMERIC} , #{renewal_discnted_prem_af, jdbcType=NUMERIC} , #{total_prem_af, jdbcType=NUMERIC} , #{decision_code, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} , #{pay_year, jdbcType=NUMERIC} , #{liability_state, jdbcType=NUMERIC} 
				, #{charge_period, jdbcType=VARCHAR} , #{pay_period, jdbcType=VARCHAR} , #{std_prem_af, jdbcType=NUMERIC} , #{count_way, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{is_gift, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} 
				, #{bonus_mode_code, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{prem_freq, jdbcType=NUMERIC} , #{initial_discnt_prem_af, jdbcType=NUMERIC},#{interest_mode, jdbcType=NUMERIC},#{paidup_date, jdbcType=DATE},#{deductible_franchise, jdbcType=NUMERIC},#{payout_rate, jdbcType=NUMERIC},
				#{additional_prem_af, jdbcType=NUMERIC},#{append_prem_af, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
	
<!-- 根据bust_item_id 和 productId 删除操作 -->	
	<delete id="NB_deleteNbContractProduct" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_CONTRACT_PRODUCT WHERE BUSI_ITEM_ID = #{ busi_item_id }  AND PRODUCT_ID = #{ product_id }]]>
	</delete>

<!-- 修改操作 -->
	<update id="NB_updateNbContractProductByItemId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_PRODUCT       ]]>
		<set>
		<trim suffixOverrides=",">
			BENEFIT_LEVEL = #{benefit_level, jdbcType=VARCHAR} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code, jdbcType=VARCHAR} ,
			COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR} ,
		    RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    EXTRA_PREM_AF = #{extra_prem_af, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    HEALTH_SERVICE_FLAG = #{health_service_flag, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    UNIT = #{unit, jdbcType=NUMERIC} ,
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
		    INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    AMOUNT = #{amount, jdbcType=NUMERIC} ,
		    RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af, jdbcType=NUMERIC} ,
		    TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
			PAY_PERIOD = #{pay_period, jdbcType=VARCHAR} ,
		    STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC} ,
			COUNT_WAY = #{count_way, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    IS_GIFT = #{is_gift, jdbcType=NUMERIC} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    BONUS_MODE_CODE = #{bonus_mode_code, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			PREM_FREQ = #{prem_freq, jdbcType=NUMERIC} ,
		    INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af, jdbcType=NUMERIC} ,
		    INTEREST_MODE = #{interest_mode, jdbcType=NUMERIC} ,
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		   ADDITIONAL_PREM_AF= #{additional_prem_af, jdbcType=NUMERIC},
		   APPEND_PREM_AF= #{append_prem_af, jdbcType=NUMERIC},
		   IS_WAIVED = #{is_waived, jdbcType=NUMERIC},
		   ANNU_PAY_TYPE =  #{annu_pay_type, jdbcType=NUMERIC},
		   PAY_FREQ =  #{pay_freq, jdbcType=VARCHAR},
		   UW_COMPLETE_DESC = #{uw_complete_desc, jdbcType=VARCHAR}
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>
	
	
	<!-- 修改操作   根据 -->
	<update id="NB_updateNbBYProductId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_PRODUCT       ]]>
		<set>
		<trim suffixOverrides=",">
			COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    UNIT = #{unit, jdbcType=NUMERIC} ,
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    AMOUNT = #{amount, jdbcType=NUMERIC} ,
		    STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} 
		</trim>
		</set>
		<![CDATA[ WHERE PRODUCT_ID = #{product_id} ]]>
	</update>
	

<!-- 按索引查询操作 -->	
	<select id="NB_findByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF, A.PAIDUP_DATE, A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<include refid="NB_queryByItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>
	
	
<!-- 按索引查询操作 根据busiItemId和ProductId查询责任-->	
	<select id="NB_findByBusiItemIdAndProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE, A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<include refid="NB_queryByBusiItemIdCondition" />
		<include refid="NB_queryByProductIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>	
<!-- 按索引查询操作 根据busiItemId和ProductId查询责任-->	
	<select id="NB_findByBusiAndProductIdAndPolicId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.item_id,
					       A.product_id,
					       A.product_code,
					       A.bonus_mode_code,
					       A.apply_code,
					       A.busi_item_id,
					       A.policy_code,
					       A.prod_pkg_plan_code,
					       A.policy_id,
					       A.amount,
					       A.unit,
					       A.count_way,
					       A.benefit_level,
					       A.validate_date,
					       A.expiry_date,
					       A.liability_state,
					       A.charge_period,
					       A.charge_year,
					       A.coverage_period,
					       A.coverage_year,
					       A.pay_period,
					       A.pay_year,
					       A.prem_freq,
					       A.decision_code,
					       A.std_prem_af,
					       A.extra_prem_af,
					       A.total_prem_af,
					       A.health_service_flag,
					       A.insert_by,
					       A.insert_time,
					       A.insert_timestamp,
					       A.update_by,
					       A.update_time,
					       A.update_timestamp,
					       A.initial_discnt_prem_af,
					       A.initial_extra_prem_af,
					       A.is_gift,
					       A.renewal_extra_prem_af,
					       A.renewal_discnted_prem_af,
					       A.paidup_date,
					       A.interest_mode,
					       A.deductible_franchise,
					       A.payout_rate,
					       A.additional_prem_af,
					       A.append_prem_af,
					       A.is_waived,
					       A.waiver_start,
					       A.waiver_end,
					       A.master_item_id,
					       A.master_product_code,
					       A.master_product_id,
					       A.PAY_FREQ,
					       A.ANNU_PAY_TYPE
        FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByProductIdCondition" />
		<include refid="NB_queryByapplyCodeCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>		
	
<!-- 按productId查询操作 -->	
<select id="NB_findByProductId1" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE, A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
	<include refid="NB_queryByProductIdCondition" />
	<include refid="NB_queryByBusiItemIdCondition" />
	<![CDATA[ ORDER BY A.ITEM_ID            ]]>
</select>

<!-- 按productId查询操作 -->	
<select id="NB_findByPO" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE, A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
	<include refid="NB_queryByProductIdCondition" />
	<include refid="NB_queryByapplyCodeCondition" />
	<![CDATA[ ORDER BY A.ITEM_ID            ]]>
</select>
<!-- 根据busiItemId查询责任包含码表中的责任名称 zhuhaixin-->
	<select id="NB_findContractProductAndProductName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL,A.PRODUCT_ID, A.PRODUCT_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, A.PAIDUP_DATE,
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, 
			A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, 
			A.PAY_YEAR, A.STD_PREM_AF, A.LIABILITY_STATE, A.CHARGE_PERIOD, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, 
			A.VALIDATE_DATE,A.PROD_PKG_PLAN_CODE,A.PREM_FREQ ,B.PRODUCT_NAME,A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A ,DEV_NB.T_PRODUCT_LIFE B WHERE A.PRODUCT_ID=B.PRODUCT_ID  ]]>
		<include refid="NB_queryByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]> 
	</select>
	<!-- 查询责任名称 -->
	
	<select id="NB_queryByProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.PRODUCT_NAME FROM DEV_NB.T_PRODUCT_LIFE  A  WHERE 1=1 ]]>
		<include refid="NB_queryByProductIdCondition" />
		<!-- <include refid="NB_queryBybusinessProdIdCondition" /> -->
		<![CDATA[ ORDER BY A.PRODUCT_ID            ]]> 
	</select>
	<select id="NB_findByMasterItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE, A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<include refid="NB_queryByMasterItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="NB_findAllMapNbContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE, A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllNbContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE,A.IS_WAIVED, A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByBusiItemIdCondition" />
		<include refid="NB_queryByProductIdCondition" />
		<include refid="NB_queryByapplyCodeCondition" />
		<include refid="NB_queryByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]> 
	</select>
<!-- 根据保单号查询保费 -->
	<select id="NB_findNbContractProductByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.TOTAL_PREM_AF, A.ITEM_ID,A.LIABILITY_STATE FROM DEV_NB.T_NB_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findNbContractProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryNbContractProductForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BENEFIT_LEVEL, B.PRODUCT_ID, B.PRODUCT_CODE, B.ITEM_ID, B.COVERAGE_PERIOD, 
			B.CHARGE_YEAR, B.EXTRA_PREM_AF, 
			B.BUSI_ITEM_ID, B.HEALTH_SERVICE_FLAG, B.POLICY_ID, B.UNIT, B.COVERAGE_YEAR, 
			B.AMOUNT, B.TOTAL_PREM_AF, B.DECISION_CODE, B.EXPIRY_DATE, 
			B.PAY_YEAR, B.STD_PREM_AF, B.LIABILITY_STATE, B.CHARGE_PERIOD, B.PAY_PERIOD, B.COUNT_WAY, B.POLICY_CODE, 
			B.VALIDATE_DATE,B.PROD_PKG_PLAN_CODE,B.PREM_FREQ, B.PAY_FREQ, B.ANNU_PAY_TYPE FROM (
					SELECT ROWNUM RN, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, 
			A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, 
			A.PAY_YEAR, A.STD_PREM_AF, A.LIABILITY_STATE, A.CHARGE_PERIOD, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, 
			A.VALIDATE_DATE FROM,A.PROD_PKG_PLAN_CODE,A.PREM_FREQ, A.PAY_FREQ, A.ANNU_PAY_TYPE DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ITEM_ID            ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 根据产品productId查询出所有的下拉选项值 -->	
	<select id="NB_findSelectValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT CODE,CODE_NAME FROM DEV_NB.T_PAGECFG_ELEMENT_VALUE A WHERE ELEMENT_VALUE_CODE IN
					(SELECT LIST_VALUE
  								FROM DEV_NB.T_PAGECFG_ENTRY_CATE_VALUE
 								WHERE RELATION_ID = (SELECT RELATION_ID
                       					 FROM DEV_NB.T_PAGECFG_ENTRY_CATE_RELA
                      							 WHERE PRODUCT_ID = #{product_id})
								 AND ELEMENT_ID IN
      				 (SELECT ELEMENT_ID
						          FROM DEV_NB.T_PAGECFG_ELEMENT
						         WHERE SUQARE_ID IN
						               (SELECT SQUARE_ID
						                  FROM DEV_NB.T_PAGECFG_SQUARE
						                 WHERE ENTRY_CATEGORY_ID =
						                       (SELECT BELONG_ENTRY_CATEGORY
						                          FROM DEV_NB.T_PAGECFG_ENTRY_CATE_RELA
						                         WHERE PRODUCT_ID = #{product_id})))) ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<!-- <![CDATA[ ORDER BY A.ITEM_ID         ]]>  -->
	</select>
	
	
	<!-- 根据产品productId查询出productId所对应的对象字段 -->
	<!-- <select id="NB_objectParameter" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select *
						  from DEV_NB.T_pagecfg_unit
						 where unit_code in
						       (select unit_code
						          from DEV_NB.T_pagecfg_element
						         where square_id in (
						                             SELECT SQUARE_ID
						                               FROM DEV_NB.T_PAGECFG_SQUARE
						                              WHERE ENTRY_CATEGORY_ID =
						                                    (SELECT ENTRY_CATEGORY_ID
						                                       FROM DEV_NB.T_PAGECFG_PRD_CATE_RELA
						                                      WHERE PRODUCT_ID = #{product_id}))) ]]>
		<include refid="请添加查询条件" />
		  <![CDATA[ order by  unit_code       ]]>  
	</select> -->
	<!-- 根据产品productId查询出productId所对应的对象字段 -->
	<select id="NB_objectParameter" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[     select u.unit_code,u.unit_name,u.label_name,u.input_type,u.value_type,pre.default_value_origin as list_value_origin,u.list_value,u.bo_parameter
                from  DEV_NB.T_pagecfg_prd_element pre,DEV_NB.T_PAGECFG_PRD_CATE_RELA cra,DEV_NB.T_pagecfg_unit u
                      where cra.product_id=#{product_id} and cra.relation_id=pre.relation_id and u.unit_code=pre.unit_code
      ]]>
      <include refid="NB_objectParameter_labelName" />
        <![CDATA[ order by  u.unit_code       ]]>  
   </select>
	
	
	<!-- 根据产品productId查询出elementId和listValue -->
	<select id="NB_" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT LIST_VALUE FROM DEV_NB.T_PAGECFG_PRD_ELEMENT WHERE RELATION_ID = 				 
							 (SELECT relation_id
							  FROM DEV_NB.T_PAGECFG_PRD_CATE_RELA
							 WHERE PRODUCT_ID = #{product_id})]]>
		<!-- <include refid="请添加查询条件" /> -->
		<!-- <![CDATA[ ORDER BY A.ITEM_ID         ]]>  -->
	</select>
	
	<!-- 根据产品productId初始化页面默认值 -->
	<select id="NB_findDefaultValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		SELECT DEFAULT_VALUE FROM DEV_NB.T_PAGECFG_PRD_ELEMENT
         WHERE RELATION_ID IN ( SELECT RELATION_ID FROM DEV_NB.T_PAGECFG_PRD_CATE_RELA WHERE PRODUCT_ID =#{product_id})
         AND UNIT_CODE IN (SELECT UNIT_CODE FROM DEV_NB.T_PAGECFG_UNIT WHERE BO_PARAMETER = #{bo_parameter})
		    ]]>
		<!-- <include refid="请添加查询条件" /> -->
		  <!-- <![CDATA[ order by  unit_code       ]]>   -->
	</select>
	<!-- 根据产品productId初始化页面默认值 -->
	<select id="NB_findParentDefaultValue" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		select ua.save_table as unit_code ,ua.save_column as default_value  from   DEV_NB.T_pagecfg_unit u,DEV_NB.T_pagecfg_unit_save ua where ua.unit_code=u.unit_code
 			and ua.product_id=#{product_id} and u.bo_parameter= #{bo_parameter}
		    ]]>
		<!-- <include refid="请添加查询条件" /> -->
		  <!-- <![CDATA[ order by  unit_code       ]]>   -->
	</select>
	
	<select id="NB_findDefaultValueNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		 select ${default_value} as DEFAULT_VALUE from ]]>
		 <![CDATA[ ${unit_code} ]]>
		   <![CDATA[ cap  where  cap.policy_id=#{relation_id}
		    ]]>
		<!-- <include refid="请添加查询条件" /> -->
		  <!-- <![CDATA[ order by  unit_code       ]]>   -->
	</select>
	
	<select id="NB_findParentDefaultValueNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		 select ${charge_period}  from ]]>
		 <![CDATA[ ${benefit_level} ]]>
		   <![CDATA[ cap  where  cap.policy_id=#{policy_id}  and cap.product_id=#{product_id}
		    ]]>
		<!-- <include refid="请添加查询条件" /> -->
		  <!-- <![CDATA[ order by  unit_code       ]]>   -->
	</select>
	
	<select id="NB_findParentDefaultValueOtherNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		 select ${field2}  from ]]>
		 <![CDATA[ ${field1} ]]>
		   <![CDATA[ cap  where  cap.policy_id=#{policy_id}
		    ]]>
		<!-- <include refid="请添加查询条件" /> -->
		  <!-- <![CDATA[ order by  unit_code       ]]>   -->
	</select>
	
	
	<!-- 查询责任所属配置页面 -->
	<select id="NB_findBelongEntryCategory" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT    relation_id,
					       busi_prd_id,
					       product_id,
					       entry_category_id,
					       insert_by,
					       update_by,
					       insert_time,
					       update_time,
					       insert_timestamp,
					       update_timestamp
        FROM DEV_NB.T_PAGECFG_PRD_CATE_RELA WHERE PRODUCT_ID= #{product_id} ]]>
	</select>
	
	<!-- 查询责任字段只读属性列表 -->
	<select id="NB_findReadOnly" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT CAV.READ_ONLY,UNIT_CODE,DEFAULT_VALUE,HIDDEN,DEFAULT_VALUE_ORIGIN
 			 	FROM DEV_NB.T_PAGECFG_PRD_ELEMENT CAV
 				WHERE RELATION_ID IN(SELECT RELATION_ID FROM DEV_NB.T_PAGECFG_PRD_CATE_RELA  
 				 WHERE PRODUCT_ID =  #{product_id}) ]]>
	</select>
	<select id="NB_findAllPagecfgPrdElementByBoP" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   select  pre.prd_element_id, u.input_type as relation_id,pre.unit_code,pre.default_value_origin,pre.default_value,pre.list_value,pre.read_only,pre.hidden,pre.notnul
				     from DEV_NB.T_pagecfg_prd_element   pre,
				          DEV_NB.T_PAGECFG_PRD_CATE_RELA cra,
				          DEV_NB.T_pagecfg_unit          u
				    where cra.product_id = #{prd_element_id}
				      and cra.relation_id = pre.relation_id
				      and u.unit_code = pre.unit_code
				      and u.bo_parameter='${default_value}']]>
	</select>
	<!-- 保存核保决定限额信息, 核保信息回传接口用 -->
	<update id="NB_saveUWLimit" parameterType="java.util.Map">
	<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_PRODUCT       ]]>
		<set>
		<trim suffixOverrides=",">
		COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
		COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR} ,
		CHARGE_PERIOD =#{charge_period , jdbcType=VARCHAR},
		CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		AMOUNT = #{amount, jdbcType=NUMERIC} ,
		STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC} ,
		TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
		UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		UPDATE_TIME = SYSDATE , 
		UPDATE_TIMESTAMP = SYSDATE 
		</trim>
		</set>
		<![CDATA[ WHERE policy_id =#{policy_id} AND busi_item_id =#{busi_item_id}  AND item_id= #{item_id} ]]>
	</update>
	
	
	<!-- 保存责任核保决定 -->
	<update id="NB_updateProductUWResult" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_PRODUCT SET DECISION_CODE =#{decision_code},
		UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,UPDATE_TIME = SYSDATE ,  UPDATE_TIMESTAMP = SYSDATE 
		 WHERE  ITEM_ID= #{item_id}       ]]>
	</update>
	
	
	<!-- 根据productId和labelName查询出要素所对应的动作和校验 -->
	<select id="NB_queryElementAction" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   action_id,
					       action_type,
					       element_id,
					       element_value,
					       target_element_id,
					       target_element_value,
					       target_element_list_value,
					       yes_no,
					       insert_by,
					       update_by,
					       insert_time,
					       update_time,
					       insert_timestamp,
					       update_timestamp 
       FROM DEV_NB.T_PAGECFG_ELEMENT_ACTION WHERE ELEMENT_ID = (
					SELECT ELEMENT_ID FROM DEV_NB.T_PAGECFG_ELEMENT
 						WHERE SQUARE_ID IN (SELECT SQUARE_ID FROM DEV_NB.T_PAGECFG_SQUARE
                     							 WHERE ENTRY_CATEGORY_ID =
                            				(SELECT ENTRY_CATEGORY_ID
                             				  FROM DEV_NB.T_PAGECFG_PRD_CATE_RELA
                             						 WHERE PRODUCT_ID = #{product_id}))
  				 				AND UNIT_CODE IN
      					 (SELECT UNIT_CODE FROM DEV_NB.T_PAGECFG_UNIT WHERE LABEL_NAME = #{label_name} ))  
      					  AND ELEMENT_VALUE = #{this_value}   order by  action_type]]>
	</select>
	<!-- 根据productId和labelName查询出要素所对应的动作和校验 -->
	<select id="NB_queryElementActionByElementId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[          select ea.PRD_ACTION_ID,
            ea.RELATION_ID, 
            ea.action_type,
            ea.PRD_ELEMENT_ID,
            ea. PRD_ELEMENT_VALUE,
            u.input_type||'' as  target_prd_element_id,
            u .bo_parameter  as  target_prd_element_value,
            ea.TARGET_PRD_ELEMENT_LIST_VALUE,
            ea.YES_NO
     from DEV_NB.T_Pagecfg_prd_Element_Action ea,DEV_NB.T_pagecfg_prd_element   pre, DEV_NB.T_pagecfg_unit          u  
     where ea.target_prd_element_id=${target_prd_element_id}  and u.unit_code=pre.unit_code and pre.prd_element_id=ea.prd_element_id ]]>
	</select>
	<!-- 根据productId和labelName查询出要素所对应的动作和校验 -->
	<select id="NB_queryPrdElementAction" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT prd_action_id,
				       relation_id,
				       action_type,
				       prd_element_id,
				       prd_element_value,
				       target_prd_element_id,
				       target_prd_element_value,
				       target_prd_element_list_value,
				       yes_no,
				       insert_by,
				       update_by,
				       insert_time,
				       update_time,
				       insert_timestamp,
				       update_timestamp
		FROM DEV_NB.T_PAGECFG_PRD_ELEMENT_ACTION WHERE PRD_ELEMENT_ID = (
          SELECT PRD_ELEMENT_ID FROM DEV_NB.T_PAGECFG_PRD_ELEMENT
             WHERE RELATION_ID IN (SELECT RELATION_ID
                             				  FROM DEV_NB.T_PAGECFG_PRD_CATE_RELA
                             						 WHERE PRODUCT_ID = #{product_id})
  				 				AND UNIT_CODE IN
      					 (SELECT UNIT_CODE FROM DEV_NB.T_PAGECFG_UNIT WHERE LABEL_NAME = #{label_name} ))  
      					  AND PRD_ELEMENT_VALUE = #{this_value}  order by  action_type]]>
	</select>
	
		<!-- 根据productId和labelName查询出要素所对应的动作和校验 -->
	<select id="NB_queryElementValid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  valid_id,
					       element_id,
					       comp_type,
					       comp_property_code,
					       comp_property_value,
					       insert_by,
					       update_by,
					       insert_time,
					       update_time,
					       insert_timestamp,
					       update_timestamp
		FROM DEV_NB.T_PAGECFG_ELEMENT_VALID WHERE ELEMENT_ID in (
					SELECT ELEMENT_ID FROM DEV_NB.T_PAGECFG_ELEMENT
 						WHERE SQUARE_ID IN (SELECT SQUARE_ID FROM DEV_NB.T_PAGECFG_SQUARE
                     							 WHERE ENTRY_CATEGORY_ID =
                            				(SELECT ENTRY_CATEGORY_ID
                             				  FROM DEV_NB.T_PAGECFG_PRD_CATE_RELA
                             						 WHERE PRODUCT_ID = #{product_id}))
  				 				AND UNIT_CODE IN
      					 (SELECT UNIT_CODE FROM DEV_NB.T_PAGECFG_UNIT WHERE LABEL_NAME = #{label_name} ))        ]]>
	</select>
	
	<sql id="NB_queryByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>		
<!-- 按索引POLICY_ID查询操作 -->	
	<select id="NB_findContractProductByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE,A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>	
	
	<select id="NB_findAllNbContractProductByPolicyIDAndBusiItemID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.APPLY_CODE,A.PRODUCT_ID, A.PRODUCT_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.INITIAL_DISCNT_PREM_AF,A.INITIAL_EXTRA_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF,A.RENEWAL_EXTRA_PREM_AF,
			A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, 
			A.PAY_YEAR, A.STD_PREM_AF, A.LIABILITY_STATE, A.CHARGE_PERIOD, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, p.option_type,
			A.VALIDATE_DATE,A.PROD_PKG_PLAN_CODE,A.PREM_FREQ,A.PAIDUP_DATE,A.INTEREST_MODE,  A.DEDUCTIBLE_FRANCHISE,A.PAYOUT_RATE,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,p.product_name,A.IS_WAIVED,A.waiver_start,
					       A.waiver_end,
					       A.master_item_id,
					       A.master_product_code,
					       A.master_product_id,
					       A.PAY_FREQ,
					       A.ANNU_PAY_TYPE
			 FROM DEV_NB.T_NB_CONTRACT_PRODUCT   A ,DEV_NB.T_product_life p      WHERE 1 = 1   and a.product_id=p.product_id   ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<include refid="NB_queryByProductIdCondition"/>
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>	
	<!-- 根据保单号查询保费 -->	
	<select id="findNbContractProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.STD_PREM_AF FROM DEV_NB.T_NB_CONTRACT_PRODUCT B
         WHERE 1=1 and B.POLICY_CODE=#{policy_code} ORDER BY B.ITEM_ID  ]]>
	</select>
	
	
	<!-- 按policyId查询操作保费信息 -->	
<select id="NB_findByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE,A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
	<include refid="findByPolicyId" />
	<![CDATA[ ORDER BY A.ITEM_ID            ]]>
</select>
<sql id="findByPolicyId">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	
	<!-- 修改操作   policyId -->
	<update id="NB_updateProductUWExtraPrem" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_PRODUCT       A ]]>
		<set>
		<trim suffixOverrides=",">
			INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af, jdbcType=NUMERIC},
			 RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af, jdbcType=NUMERIC},
			 INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af, jdbcType=NUMERIC},
			 RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af, jdbcType=NUMERIC},
			 TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC},
			 EXTRA_PREM_AF = #{extra_prem_af, jdbcType=NUMERIC}
		</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="NB_queryByItemIdCondition" />
        <include refid="NB_queryByPolicyIdCondition" />
	</update>
	
	<select id="NB_queryContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(B.TOTAL_PREM_AF) TOTAL_PREM_AF,SUM(B.AMOUNT) AMOUNT 
		 		  FROM DEV_NB.T_NB_CONTRACT_PRODUCT B
		          WHERE B.BUSI_ITEM_ID =#{busi_item_id} and rownum<=1  ]]>
	</select>
	
	<select id="NB_queryContractProductOne" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.PREM_FREQ,B.CHARGE_YEAR,C.BUSI_PRD_ID ,NVL(C.OLD_POL_NO,C.BUSI_ITEM_ID) AS BUSI_ITEM_ID FROM DEV_NB.T_NB_CONTRACT_PRODUCT B,DEV_NB.T_NB_CONTRACT_BUSI_PROD C WHERE C.BUSI_ITEM_ID=B.BUSI_ITEM_ID 
		          AND B.BUSI_ITEM_ID =#{busi_item_id} and rownum<=1  ]]>
	</select>
	<!-- 根据policyId 删除操作 -->	
	<delete id="NB_deleteNbContractProductByPolicyId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_CONTRACT_PRODUCT WHERE  POLICY_ID = #{ policy_id }]]>
	</delete>
	<select id="NB_queryNbContractInfoByApplyCodeAndProductCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	                   select A.apply_code,A.product_id,A.amount, A.additional_prem_af,A.append_prem_af,A.total_prem_af,A.std_prem_af,
	                          A.prem_freq
					   from DEV_NB.T_nb_contract_product A, 
     						DEV_NB.T_BUSINESS_PRODUCT B,
     						DEV_NB.T_product_life tpl
						where A.apply_code=#{apply_code}
      						  and A.product_id = tpl.product_id
      						  and tpl.business_prd_id = B.business_prd_id
      						  and B.product_code_sys = #{label_name}
         ]]>
         <include refid="NB_queryByItemIdCondition" />
	</select>
	<select id="NB_queryNbContractInfoByPolicyIdAndItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.item_id,
					       A.product_id,
					       A.product_code,
					       A.bonus_mode_code,
					       A.apply_code,
					       A.busi_item_id,
					       A.policy_code,
					       A.prod_pkg_plan_code,
					       A.policy_id,
					       A.amount,
					       A.unit,
					       A.count_way,
					       A.benefit_level,
					       A.validate_date,
					       A.expiry_date,
					       A.liability_state,
					       A.charge_period,
					       A.charge_year,
					       A.coverage_period,
					       A.coverage_year,
					       A.pay_period,
					       A.pay_year,
					       A.prem_freq,
					       A.decision_code,
					       A.std_prem_af,
					       A.extra_prem_af,
					       A.total_prem_af,
					       A.health_service_flag,
					       A.insert_by,
					       A.insert_time,
					       A.insert_timestamp,
					       A.update_by,
					       A.update_time,
					       A.update_timestamp,
					       A.initial_discnt_prem_af,
					       A.initial_extra_prem_af,
					       A.is_gift,
					       A.renewal_extra_prem_af,
					       A.renewal_discnted_prem_af,
					       A.paidup_date,
					       A.interest_mode,
					       A.deductible_franchise,
					       A.payout_rate,
					       A.additional_prem_af,
					       A.append_prem_af,
					       A.is_waived,
					       A.waiver_start,
					       A.waiver_end,
					       A.master_item_id,
					       A.master_product_code,
					       A.master_product_id,
					       A.PAY_FREQ,
					       A.ANNU_PAY_TYPE
				  from DEV_NB.T_nb_contract_product A
			 	  where 1=1
         ]]>
         <include refid="NB_queryByItemIdCondition" />
         <include refid="NB_queryByPolicyIdCondition" />
	</select>
	<!-- 按applyCode查询操作 -->	
	<select id="NB_findContractProductByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  SUM(A.TOTAL_PREM_AF) as TOTAL_PREM_AF  FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<include refid="NB_queryByapplyCodeCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>
	<!-- 查询被豁免的产品的信息 -->
	<select id="NB_findStdPremAfMainAndAttachWaived" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, A.IS_WAIVED,
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE,A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT A WHERE 1 = 1 ]]>
			<if test=" is_waived != null and is_waived != ''  "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
			<![CDATA[ AND A.BUSI_ITEM_ID IN ]]>
			<foreach collection="busiItems" item="busi_item_id"
             open="(" close=")" separator=",">
            	 #{busi_item_id}
        	</foreach>
			ORDER BY A.ITEM_ID
	</select>
	<!-- 更新193豁免险的每期豁免保费 -->
	<update id="NB_updateWaiverStdPremAf" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_PRODUCT_OTHER SET FIELD10 = #{field10} WHERE apply_code = #{apply_code} and busi_prd_id = #{busi_prd_id}]]>
	</update>
	<!-- 根据投保单号查询出该投保单下的有效的险种的总费用 -->
	<select id="NB_queryValidTotalPremAf" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  SUM(A.TOTAL_PREM_AF) as TOTAL_PREM_AF  FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<include refid="NB_queryByapplyCodeCondition" />
		<include refid="NB_queryByLiabilityStateCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>
	<!-- 根据投保单号查询出该投保单下的productCode的险种累计保额 -->
	<select id="NB_findAllNbcontractProductByApplyCodeAndBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT AMOUNT,PRODUCT_CODE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE 1 = 1  ]]>
		<include refid="NB_queryByapplyCodeCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>
	<select id="NB_qqueryContractProductByProductCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[     selecT
						 A.TOTAL_PREM_AF,
						 A.PREM_FREQ,
						 A.PRODUCT_CODE,
						 A.POLICY_CODE,
						 A.COVERAGE_PERIOD,
						 A.COVERAGE_YEAR,
						 A.CHARGE_PERIOD,
						 A.CHARGE_YEAR,
						 A.PAY_FREQ,
						 A.ANNU_PAY_TYPE
						  from DEV_NB.T_nb_contract_product A
		 					 WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyCodeCondition" />
		<include refid="NB_queryByProductCodeCondition" />
	</select>
	<!-- add 查询要素保存的值 by changwm_wb 2017/09/20 begin -->
	<select id="NB_findProductElementValueByParam" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select a.${save_colmun}
		  from DEV_NB.${save_table} a,
		       DEV_NB.T_pagecfg_unit_save   b,
		       DEV_NB.T_pagecfg_unit        c
		 where a.product_id = b.product_id
		   and b.unit_code = c.unit_code
		   and a.product_id = #{product_code}
		   and a.apply_code = #{apply_code}
		   and c.bo_parameter = #{label_name}
		]]>
	</select>
	
	<!-- add 查询要素保存的值 by changwm_wb 2017/09/20 end -->
	<!-- add 更新193或425豁免险的同级的长期附加险豁免标志  by caopy_wb 2017/11/11 end -->
	<update id="NB_updateContractProdIsWaived" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_CONTRACT_PRODUCT SET IS_WAIVED = #{is_waived} WHERE APPLY_CODE = #{apply_code} AND BUSI_ITEM_ID = #{busi_item_id}]]>
	</update>
	<!-- add 更新193或425豁免险的同级的长期附加险豁免标志  by caopy_wb 2017/11/11 end -->
	
	<!-- 查询产品及责任 -->
	<select id="NB_findContractProductAndBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			
				SELECT A.ITEM_ID,
			       A.PRODUCT_ID,
			       A.BONUS_MODE_CODE,
			       A.APPLY_CODE,
			       A.BUSI_ITEM_ID,
			       A.POLICY_CODE,
			       A.PROD_PKG_PLAN_CODE,
			       A.POLICY_ID,
			       A.AMOUNT,
			       A.UNIT,
			       A.COUNT_WAY,
			       A.BENEFIT_LEVEL,
			       A.VALIDATE_DATE,
			       A.EXPIRY_DATE,
			       A.LIABILITY_STATE,
			       A.CHARGE_PERIOD,
			       A.CHARGE_YEAR,
			       A.COVERAGE_PERIOD,
			       A.COVERAGE_YEAR,
			       A.PAY_PERIOD,
			       A.PAY_YEAR,
			       A.PREM_FREQ,
			       A.DECISION_CODE,
			       A.STD_PREM_AF,
			       A.EXTRA_PREM_AF,
			       A.TOTAL_PREM_AF,
			       A.HEALTH_SERVICE_FLAG,
			       A.INSERT_BY,
			       A.INSERT_TIME,
			       A.INSERT_TIMESTAMP,
			       A.UPDATE_BY,
			       A.UPDATE_TIME,
			       A.UPDATE_TIMESTAMP,
			       A.INITIAL_DISCNT_PREM_AF,
			       A.INITIAL_EXTRA_PREM_AF,
			       A.IS_GIFT,
			       A.RENEWAL_EXTRA_PREM_AF,
			       A.RENEWAL_DISCNTED_PREM_AF,
			       A.PAIDUP_DATE,
			       A.INTEREST_MODE,
			       A.DEDUCTIBLE_FRANCHISE,
			       A.PAYOUT_RATE,
			       A.ADDITIONAL_PREM_AF,
			       A.APPEND_PREM_AF,
			       A.IS_WAIVED,
			       A.WAIVER_START,
			       A.WAIVER_END,
			       A.MASTER_ITEM_ID,
			       A.MASTER_PRODUCT_CODE,
			       A.MASTER_PRODUCT_ID,
			       A.PAY_FREQ,
			       A.ANNU_PAY_TYPE,
			       A.PAY_AGE,
			       A.UW_COMPLETE_DESC,
			       B.PRODUCT_CODE,
			       C.PRODUCT_NAME_STD ,
			       C.PRODUCT_CATEGORY,
			       D.PRODUCT_TYPE
			  FROM DEV_NB.T_NB_CONTRACT_PRODUCT A
			  LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD B  ON A.APPLY_CODE = B.APPLY_CODE AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			  LEFT JOIN DEV_NB.T_BUSINESS_PRODUCT C ON B.PRODUCT_CODE = C.PRODUCT_CODE_SYS 
			  LEFT JOIN DEV_NB.T_DRQ_PRODUCT D ON C.PRODUCT_CODE_SYS = D.PRODUCT_CODE
			 WHERE 1 = 1
		]]>
		<include refid="NB_queryByapplyCodeCondition" />
	</select>
	<!-- 根据投保单号查询出该投保单下的productCode的险种累计保额 -->
	<select id="NB_findLabelNameAndBoParameterByInternalId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select pf.product_id,pf.product_name,us.save_table,us.save_column,pu.label_name,pu.bo_parameter from DEV_NB.T_product_life pf,DEV_NB.T_PAGECFG_unit_save us,
		DEV_NB.T_pagecfg_unit pu
 		where 1=1
 		and pf.product_id = us.product_id
 		and us.unit_code = pu.unit_code
		and pf.internal_id = #{internal_id}
		and us.save_table = #{save_table} ]]>
		<![CDATA[ ORDER BY pu.bo_parameter            ]]>
	</select>
	
	<!-- 承保下 -->
	<select id="PA_queryContractProductBypolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[   SELECT SUM(B.TOTAL_PREM_AF) TOTAL_PREM_AF,SUM(B.AMOUNT) AMOUNT 
		 		  FROM DEV_PAS.T_CONTRACT_PRODUCT B
		          WHERE B.BUSI_ITEM_ID =#{busi_item_id} and rownum<=1  ]]>
	</select>
	 <!-- 承包下 -->
	 <select id="PA_queryContractProductOneByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[ SELECT B.PREM_FREQ,B.CHARGE_YEAR,C.BUSI_PRD_ID,NVL(C.OLD_POL_NO,C.BUSI_ITEM_ID) AS BUSI_ITEM_ID FROM DEV_PAS.T_CONTRACT_PRODUCT B,
DEV_PAS.T_CONTRACT_BUSI_PROD C WHERE C.BUSI_ITEM_ID=B.BUSI_ITEM_ID 
		          AND B.BUSI_ITEM_ID =#{busi_item_id} and rownum<=1  ]]>
	 </select>
	 
	 	 
	 
	 <select id="NB_queryTotalPremByPayMode" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[  
	 		SELECT A.POLICY_ID,
		       A.BUSI_ITEM_ID,
		       A.ITEM_ID,
		       A.PREM_FREQ,
		       A.VALIDATE_DATE,
		       A.PAIDUP_DATE,
		       A.STD_PREM_AF,
		       A.IRREGULAR_PREM,
		       B.MASTER_BUSI_ITEM_ID,
		       B.RENEW,
		       B.ASSURERENEW_FLAG,
		       (SELECT T.PAY_MODE
		          FROM DEV_CAP.V_CASH_DETAIL T
		         WHERE T.POLICY_CODE = A.POLICY_CODE
				   AND T.BUSI_PROD_CODE = B.PRODUCT_CODE
		           AND T.ARAP_FLAG = 1 /*应收*/
		           AND T.BUSINESS_TYPE = 1004 /*预收*/
		           AND T.FEE_STATUS = '01'
		           AND ROWNUM = 1) AS PAY_MODE,
		       C.COVER_PERIOD_TYPE,
		       (SELECT TB.GURNT_RENEW_END
		          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TB
		         WHERE TB.POLICY_CODE = A.POLICY_CODE
		           AND TB.BUSI_PRD_ID = B.BUSI_PRD_ID
		           AND TB.MASTER_BUSI_ITEM_ID IS NULL) AS GURNT_RENEW_END
		
		  FROM DEV_NB.T_NB_CONTRACT_PRODUCT   A,
		       DEV_NB.T_NB_CONTRACT_BUSI_PROD B,
		       DEV_PDS.T_BUSINESS_PRODUCT     C
		 WHERE A.POLICY_ID = B.POLICY_ID
		     AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
		     AND B.BUSI_PRD_ID = C.BUSINESS_PRD_ID
			 AND A.POLICY_CODE = #{policy_code}

	  ]]>
	 </select>
	 
	  <!-- 工行纸质保单补充接口使用  查询所有操作 -->
	<select id="NB_findAllNbContractProductPaper" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.RENEWAL_EXTRA_PREM_AF, A.APPLY_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, A.PAY_YEAR, A.LIABILITY_STATE, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.STD_PREM_AF, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.VALIDATE_DATE, 
			A.BONUS_MODE_CODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.PAIDUP_DATE,A.IS_WAIVED, A.PAY_FREQ, A.ANNU_PAY_TYPE FROM DEV_NB.T_NB_CONTRACT_PRODUCT       A WHERE  
			#{policy_code} IN (A.POLICY_CODE,A.APPLY_CODE)
		 ORDER BY A.ITEM_ID            ]]> 
	</select>
	<!-- 关联保单信息查询接口，根据投保单号和险种编码查询对应险种所有责任组信息  -->
	<select id="NB_queryAllNbContractProductByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT G.PRODUCT_CODE /* 精算产品代码 */,
	              E.FIELD1 /**保障计划*/,
	              E.FIELD5 /** 保障区域 */,
	              E.HOSPITAL_SCOPE /* 医院范围 */
	         FROM APP___PDS__DBUSER.T_PRODUCT_LIFE A
	        INNER JOIN APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
	           ON A.BUSINESS_PRD_ID = B.BUSINESS_PRD_ID
	        INNER JOIN APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT G
	           ON G.PRODUCT_ID = A.PRODUCT_ID
	        INNER JOIN APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT_OTHER E
	           ON E.APPLY_CODE = G.APPLY_CODE
	        WHERE B.PRODUCT_CODE_SYS = #{product_code_sys}	
	        AND G.APPLY_CODE = #{apply_code}	
		]]> 
	</select>
</mapper>