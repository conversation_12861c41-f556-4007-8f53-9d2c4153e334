<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IClaimForMobileDao"> 

	<!-- 理赔列表信息查询-->
	<select id="QRY_INTEGRAL_findAllMapClaimListInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ select distinct ca.case_no,ca.case_id,
       cu.customer_name,
       ca.case_status,
       ca.insured_id,
       sub.claim_date,
       (select m.name from dev_clm.t_case_status m where m.code = ca.case_status) as case_status_name,
       ca.organ_code,
       nvl(ca.registe_organ,
           (select b.organ_code
              from dev_clm.t_udmp_org a, dev_clm.t_udmp_user b
             where a.organ_id = b.organ_id
               and b.user_id = ca.register_id)) as registe_organ,
       (select u.real_name from dev_clm.t_udmp_user u where u.user_id = ca.acceptor_id and rownum=1) as real_name,
        (select u.real_name from dev_clm.t_udmp_user u where u.user_id =
       (select re.acceptor_id from dev_clm.t_claim_case_record re where re.case_id=ca.case_id) and rownum=1)as real_name_recode,
       ca.case_flag,
       (select name from dev_clm.t_apply_type where code=ca.case_apply_type) as case_apply_type,
       ca.case_apply_type as case_apply_type_code,
       (CASE  WHEN CA.CASE_STATUS='10' OR CA.CASE_STATUS='20' THEN '1' ELSE '0' END) AS REPORT_CONFIRM  /*PL2021031959755*/
  from dev_clm.t_claim_case     ca,
       dev_clm.t_claim_accident ac,
       dev_clm.t_claim_sub_case sub,
       dev_clm.t_customer       cu
 where ca.accident_id = ac.accident_id 
   and ca.case_id = sub.case_id(+)
   and ca.insured_id = cu.customer_id(+) ]]>
	 <if test="policy_code !=null and policy_code !=''"> 
	<![CDATA[ and ca.insured_id in (
	   select il.customer_id 
	   from dev_pas.t_insured_list il 
	   where il.policy_code = #{policy_code}) ]]>
	 </if>
	<if test="case_id !=null and case_id !=''"> 
	<![CDATA[  and ca.case_id = #{case_id} ]]>
	 </if>
	 <if test="accident_id !=null and accident_id !=''"> 
	 <![CDATA[ and ca.accident_id = #{accident_id}  ]]>
	 </if>
	 <if test="case_no !=null and case_no !=''"> 
	 <![CDATA[ and ca.case_no = #{case_no}  ]]>
	  </if>
	<if test="rptr_name !=null and rptr_name !=''"> <!-- '%${rptr_name}%' -->
	 <![CDATA[ and ca.rptr_name = #{rptr_name}  ]]>
	  </if>
	<if test="organ_code !=null and organ_code !=''"> 
	  <![CDATA[ and ca.registe_organ = #{organ_code}]]>
	  </if>
	<if test="case_status !=null and case_status !=''"> 
	  <![CDATA[ and ca.case_status = #{case_status}  ]]>
	 </if>
	 <if test="accident_no !=null and accident_no !=''"> 
	<![CDATA[  and ac.accident_no = #{accident_no} ]]>
	 </if>
	 <if test="claim_start_date !=null and claim_start_date !=''"> 
	<![CDATA[  and sub.claim_date >= #{claim_start_date} ]]>
	 </if>
	 <if test="claim_end_date !=null and claim_end_date !=''"> 
	<![CDATA[  and sub.claim_date <= #{claim_end_date} ]]>
	 </if>
	 <if test="customer_name !=null and customer_name !=''"> 
	<![CDATA[   and cu.customer_name = #{customer_name} ]]>
	 </if>
	 <if test="id_no !=null and id_no !=''"> 
	<![CDATA[   and cu.customer_certi_code = #{id_no} ]]>
	 </if>
	
	<if test=" order_context != null and order_context != ''  "><![CDATA[
		${order_context}
	]]></if>
	</select>
	
	<!-- 取值赔案的理赔类型 -->
	<select id="QRY_INTEGRAL_findAllMapClaimType" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT CSC.CLAIM_TYPE FROM
	   DEV_CLM.T_CLAIM_CASE CA
	   LEFT JOIN DEV_CLM.T_CLAIM_SUB_CASE CSC ON CA.CASE_ID = CSC.CASE_ID where 1=1 AND CA.CASE_NO = #{case_no} ]]>
	</select>
	
</mapper>
