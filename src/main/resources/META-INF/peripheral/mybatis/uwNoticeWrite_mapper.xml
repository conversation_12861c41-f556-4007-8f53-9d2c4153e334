<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.uw.dao.impl.QueryNwNoticeDaoImpl">


<!-- 报文对比 -->
<!-- 电子签名审核通知书在nb库定义表中变了 -->
<select id="queryUwNotice_cjk" resultType="java.util.Map" parameterType="java.util.Map" >
<![CDATA[ 
        SELECT A.TEMPLATE_CODE,
			CASE 
       			WHEN A.TEMPLATE_CODE='UWS_00003' THEN '03-体检通知书'
                WHEN A.TEMPLATE_CODE='UWS_00004' THEN '81-修改事项和索要材料说明通知书'
                WHEN A.TEMPLATE_CODE='UWS_00001' THEN '89-核保问卷通知书'
                WHEN A.TEMPLATE_CODE='UN012' THEN '04-生调通知书'
                WHEN A.TEMPLATE_CODE='UWS_00002' THEN '84-退保附加险或主险部分退保通知书'
                WHEN A.TEMPLATE_CODE='UWS_00016' THEN '90-电子签名审核通知书'
                WHEN A.TEMPLATE_CODE='UWS_00019' THEN 'EUN021-费率变更事项通知书'
                WHEN A.TEMPLATE_CODE='UWS_00011' THEN 'EUA026-居民纳税身份变动确认声明表通知书'
                ELSE A.DOCUMENT_NAME END AS DOCUMENT_NAME ,  
               A.document_no,A.status,A.buss_code,A.BUSS_ID,
               '' AS chk_type/*27质检通知书类别*/
               ,mst.submit_channel
               ,umst.uw_source_type
               from dev_nb.t_document A 
               left join dev_nb.t_nb_contract_master mst 
               on mst.apply_code = A.buss_code
               left join dev_uw.t_uw_master umst 
               on A.BUSS_ID = UMST.UW_ID
               WHERE A.buss_source_code='002' 
    AND A.buss_code=#{apply_code} 
    UNION 
     SELECT A.TEMPLATE_CODE,
     case 
          when A.TEMPLATE_CODE='NBS_00001' THEN 'BF00-保费补交通知书'
          when A.TEMPLATE_CODE='NBS_00008' THEN '15-催缴通知书'
          ELSE A.document_name END AS document_name ,
          A.document_no,A.status,A.buss_code,A.BUSS_ID,
          '' as chk_type/*27质检通知书类别*/    
          ,B.submit_channel
          ,'1' as uw_source_type  
     from dev_nb.t_document A 
     left join dev_nb.t_nb_contract_master B 
     on A.BUSS_CODE = B.APPLY_CODE 
     WHERE A.buss_source_code='001' 
     AND A.TEMPLATE_CODE in('NBS_00001','NBS_00008') 
     and A.status != '0' 
     and B.PROPOSAL_STATUS NOT IN ('28','30','38') 
     and A.buss_code=#{apply_code}
     
      UNION 
     SELECT A.TEMPLATE_CODE,
     case when A.TEMPLATE_CODE='NBS_00014' THEN '27-电子签名质检通知书'
          when A.TEMPLATE_CODE='NBS_00018' THEN '94-保费补交质检通知书'
          when A.TEMPLATE_CODE='NBS_00019' THEN '95-催缴质检通知书' 
          when A.TEMPLATE_CODE='NBS_00017' THEN '93-出单前撤保质检通知书'
          ELSE A.document_name END AS document_name ,
          A.document_no,A.status,A.buss_code,A.BUSS_ID,
           (select 
         case when qt.qa_type='4' then '1'
           when qt.qa_type='5' then '2'
           else ''
         end
         from dev_nb.t_nb_esquoalitydecison ed 
         inner join dev_nb.t_nb_qt_task qt on ed.task_id=qt.task_id
         where ed.apply_code=a.buss_code and ed.document_no=a.document_no
         ) as  chk_type/*27质检通知书类别*/    
          ,mst.submit_channel
          ,'1' as uw_source_type
     from dev_nb.t_nb_esquoalitydecison ed 
     inner join dev_nb.t_document A 
     on ed.apply_code=A.buss_code 
     and ed.document_no=a.document_no
     left join dev_nb.t_nb_contract_master mst 
     on mst.apply_code = A.buss_code
     WHERE A.buss_source_code='001' AND 
     A.TEMPLATE_CODE in('NBS_00014','NBS_00018','NBS_00019','NBS_00017') 
     and A.buss_code=#{apply_code}
    ]]>
</select>


<select id="queryNbQtTaskDocument" resultType="java.util.Map" parameterType="java.util.Map" >
 <![CDATA[ 
       SELECT 
		TO_CHAR(A.QT_STATUS) AS QT_STATUS,
		A.APPLY_CODE,
		A.QA_TYPE,
		A.CARD_CODE
	  FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE = #{qa_type} AND A.CARD_CODE = #{card_code}  AND A.APPLY_CODE = #{apply_code}
    ]]>


</select>



<!-- 查询核保决定 -->
<select id="findAllBusiProdDecisionCodeByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">

select A.decision_code,A.UW_ID,A.MASTER_BUSI_ITEM_ID 
from dev_uw.t_uw_busi_prod A WHERE A.APPLY_CODE=#{apply_code}
and A.uw_id = #{uw_id}
order by A.MASTER_BUSI_ITEM_ID DESC 

</select>
<!-- 查询核保结论30对应具体的核保结论 -->
<select id="findAllDecisionCodeByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
select  31 AS CODES from dev_uw.t_uw_extra_prem A where uw_id =#{uw_id}
UNION 
select 33 AS CODES from dev_uw.t_uw_condition where uw_id =#{uw_id}
]]>
</select>

<!-- 查询是否为核保问卷通知书（自驾车） -->
<select id="findUWAskforinfoList" resultType="java.util.Map" parameterType="java.util.Map" >
 <![CDATA[ 
	SELECT A.DOC_LIST_ID ,D.BUSS_CODE AS APPLY_CODE FROM DEV_UW.T_ASKFORINFO A 
		INNER JOIN DEV_NB.T_DOCUMENT D ON A.DOC_LIST_ID = D.DOC_LIST_ID
		INNER JOIN DEV_UW.T_ASKFORINFO_DETAIL AD ON AD.ASKFORINFO_ID = A.ASKFORINFO_ID
	WHERE D.TEMPLATE_CODE = 'UWS_00001'
	/* #175116 【接口需求】 核保通知书问卷类信件支持线上回复-新时代对接新核心-QRY-3/3*/
		AND AD.QUESTION_CONTENT in ('投保问卷（自驾车意外险适用）','胸痛','头晕、头痛','甲状腺','呼吸道','消化性溃疡','癫痫性疾病','哮喘疾病'
   		,'糖尿病疾病','肝炎疾病','消化道疾病','高血压疾病','客户疾病史','体育运动','外出人员问卷')
		AND D.BUSS_CODE = #{apply_code}
		AND D.DOCUMENT_NO = #{document_no} 
    ]]>
</select>


	<select id="queryTodoDocument_lnts" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT * from( 
			SELECT CM.APPLY_CODE,
		       CM.POLICY_CODE,
		       CM.APPLY_DATE,
		       CUS.OLD_CUSTOMER_ID,
		       CUS.CUSTOMER_ID_CODE,
		       CUS.CUSTOMER_NAME,
		       CUS.CUSTOMER_GENDER,
		       CUS.CUSTOMER_BIRTHDAY,
		       CUS.MOBILE_TEL,
		       CUS.CUSTOMER_CERTI_CODE,
		       CUS.CUSTOMER_CERT_TYPE,
		       CM.SUBMIT_CHANNEL,
		       CM.SUBINPUT_TYPE,
		       CM.MEDIA_TYPE,
		       (SELECT T.MEDIA_TYPE FROM DEV_PAS.T_CONTRACT_MASTER T WHERE T.POLICY_CODE = CM.POLICY_CODE) MEDIA_TYPE1,
		       CM.DRQ_FLAG,
		       (SELECT TB.BANK_NAME FROM DEV_NB.T_BANK TB WHERE TB.BANK_CODE =  PA.ACCOUNT_BANK) AS SERVICE_BANK,
		       (SELECT TA.ADDRESS FROM DEV_NB.T_ADDRESS TA WHERE TA.ADDRESS_ID = PH.ADDRESS_ID) AS ADDRESS,
		       CM.ISSUE_DATE,
		       CA.AGENT_CODE,
		       CM.PROPOSAL_STATUS,
		       CM.CONFIRM_WAY,
		       PA.ACCOUNT,
		       PA.NEXT_ACCOUNT,
		       CM.POLICY_REINSURE_FLAG,
		       (SELECT COUNT(1)
		          FROM DEV_UW.T_UW_POLICY UP
		         WHERE UP.APPLY_CODE = CM.APPLY_CODE
		           AND UP.UW_SOURCE_TYPE = '1'
		           AND UP.POLICY_DECISION = '50') AS DECL_TOTAL,
		       (SELECT TO_CHAR(WM_CONCAT(DISTINCT QT.QA_TYPE))
		          FROM DEV_NB.T_NB_QT_TASK QT
		         WHERE QT.QA_TYPE IN ('4', '5')
		           AND QT.QT_STATUS = 7
		           AND QT.APPLY_CODE = CM.APPLY_CODE) AS QA_TYPES,
		           (CASE
         WHEN CM.OLD_POLICY_CODE IS NOT NULL THEN
          (SELECT CA.SERVICE_TYPE
             FROM DEV_PAS.T_RENEW_CHANGE RC
            INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
               ON RC.CHANGE_ID = AC.CHANGE_ID
              AND RC.ACCEPT_ID = AC.ACCEPT_ID
            INNER JOIN DEV_PAS.T_CS_APPLICATION CA
               ON AC.CHANGE_ID = CA.CHANGE_ID
            INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
               ON RC.NEW_BUSI_PRD_ID = BP.BUSINESS_PRD_ID
            WHERE RC.POLICY_CODE = CM.OLD_POLICY_CODE
                 
              AND AC.ACCEPT_STATUS = '18'
              AND RC.RENEW_CHANGE_TYPE = '3'
              AND AC.SERVICE_CODE = 'RR'
              AND BP.PRODUCT_CODE_SYS = '********'
              AND ROWNUM = 1)
         ELSE
          ''
       END) AS RR_INFO, /*保全转保信息*/
		     (SELECT LISTAGG(TEMPLATE_CODE, '/') WITHIN GROUP(ORDER BY TEMPLATE_CODE)TEMPLATE_CODE
                   FROM DEV_NB.T_DOCUMENT B
                  WHERE B.BUSS_CODE = CM.APPLY_CODE) AS TEMPLATE_CODE      
		  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
		  JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
		    ON CM.APPLY_CODE = CA.APPLY_CODE
		  JOIN DEV_NB.T_NB_POLICY_HOLDER PH
		    ON PH.APPLY_CODE = CM.APPLY_CODE
		  JOIN DEV_PAS.T_CUSTOMER CUS
		    ON CUS.CUSTOMER_ID = PH.CUSTOMER_ID
		  JOIN DEV_NB.T_NB_PAYER_ACCOUNT PA
		    ON PA.POLICY_ID = CM.POLICY_ID
		 WHERE 1 = 1 
		 	AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
		   	AND CA.AGENT_CODE = #{agent_code}]]>
		   	<if test = "customer_name != null and customer_name != '' "><![CDATA[AND CUS.CUSTOMER_NAME = #{customer_name}]]></if>
		   	<if test = "customer_certi_code != null and customer_certi_code != '' "><![CDATA[AND CUS.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
		    <![CDATA[AND CM.APPLY_CODE IN
		       (SELECT BUSS_CODE
		          FROM DEV_NB.T_DOCUMENT TD
		         WHERE TD.BUSS_CODE = CM.APPLY_CODE
		           AND TD.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
		           AND (TD.BUSS_SOURCE_CODE = '001' OR
		               (TD.BUSS_SOURCE_CODE = '002' AND TD.POLICY_CODE IS NULL))
		           AND ((TD.TEMPLATE_CODE = 'UWS_00016' AND TD.STATUS IN ('2', '9'))]]>
		           
		           
		            <if test="TemplateCodeSet1 != null and TemplateCodeSet1.size() > 0">
			           	OR (TD.TEMPLATE_CODE IN 
						   <foreach collection="TemplateCodeSet1" index="index" item="templateCode1" open="(" separator="," close=")">
						         <![CDATA[#{templateCode1 }]]>
						   </foreach>
						 AND TD.STATUS IN ('2', '3', '4'))
		           	 </if>
		           	
		            <![CDATA[OR (TD.TEMPLATE_CODE IN ('UWS_00006', 'UWS_00004', 'UWS_00002', 'UWS_00003', 'UN012', 'UWS_00001',
		                'NBS_00022', 'UWS_00019') AND TD.STATUS IN ('2', '3', '4', '5', '6'))]]>
		               /*159359-【接口需求】 核保消息提醒扩展通知书范围-新时代对接新核心*/
		              <![CDATA[OR ( TD.TEMPLATE_CODE IN ('UWS_00011') and  TD.STATUS IN ('2', '3', '4', '5', '6','8'))]]>
		            <if test="TemplateCodeSet != null and TemplateCodeSet.size() > 0">
		             OR (TD.TEMPLATE_CODE IN
		               <foreach collection="TemplateCodeSet" index="index" item="templateCode" open="(" separator="," close=")">
					         <![CDATA[#{templateCode}]]>
					   </foreach> 
		             <![CDATA[AND (
		             		(SELECT A.QT_STATUS
		                       FROM DEV_NB.T_NB_QT_TASK           A,
		                            DEV_NB.T_NB_ESQUOALITYDECISON ES
		                      WHERE ES.TASK_ID = A.TASK_ID
		                        AND A.APPLY_CODE = TD.BUSS_CODE
		                        AND TD.DOCUMENT_NO = ES.DOCUMENT_NO
		                        AND rownum = 1) = 9 AND TD.STATUS = '2' 
		                OR
		             		(SELECT A.QT_STATUS
		                       FROM DEV_NB.T_NB_QT_TASK           A,
		                            DEV_NB.T_NB_ESQUOALITYDECISON ES
		                      WHERE ES.TASK_ID = A.TASK_ID
		                        AND A.APPLY_CODE = TD.BUSS_CODE
		                        AND TD.DOCUMENT_NO = ES.DOCUMENT_NO
		                        AND rownum = 1) = 4 AND TD.STATUS = '9'))]]>
		           </if>
		 <![CDATA[ )
		        )
		 		ORDER BY CM.APPLY_DATE DESC)where 1= 1 and rownum <=50
		 	]]>
	</select>
	
	<select id="queryTodoDocument_lnts_total" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(CM.APPLY_CODE)
		  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
		  JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
		    ON CM.APPLY_CODE = CA.APPLY_CODE]]>
		  <if test = "customer_name != null and customer_name != ''  or  customer_certi_code != null and customer_certi_code != ''">
		  <![CDATA[JOIN DEV_NB.T_NB_POLICY_HOLDER PH
		    ON PH.APPLY_CODE = CM.APPLY_CODE
		  JOIN DEV_PAS.T_CUSTOMER CUS
		    ON CUS.CUSTOMER_ID = PH.CUSTOMER_ID]]></if>
		 <![CDATA[WHERE 1 = 1
		 	AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
		   	AND CA.AGENT_CODE = #{agent_code}]]>
		   	<if test = "customer_name != null and customer_name != '' "><![CDATA[AND CUS.CUSTOMER_NAME = #{customer_name}]]></if>
		   	<if test = "customer_certi_code != null and customer_certi_code != '' "><![CDATA[AND CUS.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
		    <![CDATA[AND CM.APPLY_CODE IN
		       (SELECT BUSS_CODE
		          FROM DEV_NB.T_DOCUMENT TD
		         WHERE TD.BUSS_CODE = CM.APPLY_CODE
		           AND TD.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
		           AND (TD.BUSS_SOURCE_CODE = '001' OR
		               (TD.BUSS_SOURCE_CODE = '002' AND TD.POLICY_CODE IS NULL))
		           AND ((TD.TEMPLATE_CODE = 'UWS_00016' AND TD.STATUS IN ('2', '9')) ]]>
		           
		           	<if test="TemplateCodeSet1 != null and TemplateCodeSet1.size() > 0">
			           	OR (TD.TEMPLATE_CODE IN 
						   <foreach collection="TemplateCodeSet1" index="index" item="templateCode1" open="(" separator="," close=")">
						         <![CDATA[#{templateCode1 }]]>
						   </foreach>
						 AND TD.STATUS IN ('2', '3', '4'))
		           	</if>
		           	
		            <![CDATA[OR (TD.TEMPLATE_CODE IN ('UWS_00006', 'UWS_00004', 'UWS_00002', 'UWS_00003', 'UN012', 'UWS_00001',
		                'NBS_00022', 'UWS_00019') AND TD.STATUS IN ('2', '3', '4', '5', '6'))]]>
		               /*159359-【接口需求】 核保消息提醒扩展通知书范围-新时代对接新核心*/
		               <![CDATA[OR ( TD.TEMPLATE_CODE IN ('UWS_00011') and  TD.STATUS IN ('2', '3', '4', '5', '6','8'))]]>
		            <if test="TemplateCodeSet != null and TemplateCodeSet.size() > 0">
		             OR (TD.TEMPLATE_CODE IN
		               <foreach collection="TemplateCodeSet" index="index" item="templateCode" open="(" separator="," close=")">
					         <![CDATA[#{templateCode}]]>
					   </foreach>
		             <![CDATA[AND (
		             		(SELECT A.QT_STATUS
		                       FROM DEV_NB.T_NB_QT_TASK           A,
		                            DEV_NB.T_NB_ESQUOALITYDECISON ES
		                      WHERE ES.TASK_ID = A.TASK_ID
		                        AND A.APPLY_CODE = TD.BUSS_CODE
		                        AND TD.DOCUMENT_NO = ES.DOCUMENT_NO
		                        AND rownum = 1) = 9 AND TD.STATUS = '2' 
		                OR
		             		(SELECT A.QT_STATUS
		                       FROM DEV_NB.T_NB_QT_TASK           A,
		                            DEV_NB.T_NB_ESQUOALITYDECISON ES
		                      WHERE ES.TASK_ID = A.TASK_ID
		                        AND A.APPLY_CODE = TD.BUSS_CODE
		                        AND TD.DOCUMENT_NO = ES.DOCUMENT_NO
		                        AND rownum = 1) = 4 AND TD.STATUS = '9'))]]>
		           </if>
		 <![CDATA[ )
		        )
		 	]]>
	</select>
<!-- 查询待回执签收保单 -->	
<select id="queryAckSign" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT RELT.* FROM (
			SELECT CM.MULTI_MAINRISK_FLAG,CM.APPLY_CODE,CM.CHANNEL_TYPE,
		       CM.POLICY_CODE,
		       CM.APPLY_DATE,
		       CUS.OLD_CUSTOMER_ID,
		       CUS.CUSTOMER_ID_CODE,
		       CUS.CUSTOMER_NAME,
		       CUS.CUSTOMER_GENDER,
		       CUS.CUSTOMER_BIRTHDAY,
		       CUS.MOBILE_TEL,
		       CUS.CUSTOMER_CERTI_CODE,
		       CUS.CUSTOMER_CERT_TYPE,
		       CM.SUBMIT_CHANNEL, 
		       CM.SUBINPUT_TYPE,
		       CM.MEDIA_TYPE,
		       TO_CHAR(CM.SELF_APPLY_FLAG) SELF_APPLY_FLAG,
		       (SELECT T.MEDIA_TYPE FROM DEV_PAS.T_CONTRACT_MASTER T WHERE T.POLICY_CODE = CM.POLICY_CODE) MEDIA_TYPE1,
		       CM.DRQ_FLAG,
		       (SELECT TB.BANK_NAME FROM DEV_NB.T_BANK TB WHERE TB.BANK_CODE =  PA.ACCOUNT_BANK) AS SERVICE_BANK,
		       	CM.SERVICE_BANK AS SERVICE_BANK_NUM,
		       (SELECT TA.ADDRESS FROM DEV_NB.T_ADDRESS TA WHERE TA.ADDRESS_ID = PH.ADDRESS_ID) AS ADDRESS,
		       CM.ISSUE_DATE,
		       /*需求 #139508-保单查询接口-待回执签收 变更----start */
		       (select TCA.AGENT_CODE
                  from DEV_PAS.T_CONTRACT_AGENT TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS AGENT_CODE,
		       (select TCA.AGENT_CODE
                  from DEV_NB.t_Nb_Contract_Agent TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS SALE_AGENT_CODE,
               (select TCA.AGENT_NAME
                  from DEV_PAS.T_CONTRACT_AGENT TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS AGENT_NAME,
               (select TCA.AGENT_NAME
                  from DEV_NB.t_Nb_Contract_Agent TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS SALE_AGENT_NAME,
               CM.Special_Account_Flag,
               (select TCA.Organ_Code
                  from DEV_PAS.T_CONTRACT_AGENT TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) as ORGAN_CODE,
               CM.Validate_Date,
               (SELECT ORG.ORGAN_ADDRESS
                  FROM APP___PAS__DBUSER.T_UDMP_ORG ORG
                  LEFT JOIN DEV_PAS.T_CONTRACT_AGENT TCA
                    ON ORG.Organ_Code = TCA.Organ_Code
                 WHERE TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS ORGAN_ADDRESS,
               /*需求 #139508-保单查询接口-待回执签收 变更----end */
		       CM.PROPOSAL_STATUS,
		       CM.CONFIRM_WAY,
		       PA.ACCOUNT,
		       PA.NEXT_ACCOUNT,
		       CM.POLICY_REINSURE_FLAG,
		       
		        /*需求 #122752-保单查询接口-待回执签收 变更----start */
				(SELECT TO_CHAR(CU.OLD_CUSTOMER_ID)
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDNO,
		       (SELECT TO_CHAR(CU.CUSTOMER_ID_CODE)
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDIDNO,
		       (SELECT CU.CUSTOMER_NAME
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDNAME,
		       (SELECT TO_CHAR(CU.CUSTOMER_CERT_TYPE)
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDIDTYPE,
		       (SELECT CU.CUSTOMER_CERTI_CODE
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDIDNUMBER,
		       (SELECT TO_CHAR(TIL.RELATION_TO_PH)
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS APPNTANDSECONDINSUREDRELATION,
				/*需求 #122752-保单查询接口-待回执签收 变更----end */
				
		       (SELECT COUNT(1)
		          FROM DEV_UW.T_UW_POLICY UP
		         WHERE UP.APPLY_CODE = CM.APPLY_CODE
		           AND UP.UW_SOURCE_TYPE = '1'
		           AND UP.POLICY_DECISION = '50') AS DECL_TOTAL,
		       (SELECT TO_CHAR(WM_CONCAT(DISTINCT QT.QA_TYPE))
		          FROM DEV_NB.T_NB_QT_TASK QT
		         WHERE QT.QA_TYPE IN ('4', '5')
		           AND QT.QT_STATUS = 7
		           AND QT.APPLY_CODE = CM.APPLY_CODE) AS QA_TYPES,
		           (CASE
         WHEN CM.OLD_POLICY_CODE IS NOT NULL THEN
          (SELECT CA.SERVICE_TYPE
             FROM DEV_PAS.T_RENEW_CHANGE RC
            INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
               ON RC.CHANGE_ID = AC.CHANGE_ID
              AND RC.ACCEPT_ID = AC.ACCEPT_ID
            INNER JOIN DEV_PAS.T_CS_APPLICATION CA
               ON AC.CHANGE_ID = CA.CHANGE_ID
            INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
               ON RC.NEW_BUSI_PRD_ID = BP.BUSINESS_PRD_ID
            WHERE RC.POLICY_CODE = CM.OLD_POLICY_CODE
                 
              AND AC.ACCEPT_STATUS = '18'
              AND RC.RENEW_CHANGE_TYPE = '3'
              AND AC.SERVICE_CODE = 'RR'
              AND (BP.PRODUCT_CODE_SYS = '********' or BP.PRODUCT_CODE_SYS = '********')
              AND ROWNUM = 1)
         ELSE
          ''
       END) AS RR_INFO /*保全转保信息*/
		           
		  FROM (SELECT TD.*
            FROM DEV_NB.T_NB_CONTRACT_MASTER TD
            LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT NPA
              ON TD.POLICY_CODE = NPA.POLICY_CODE
           WHERE 1 = 1
           and ((TD.SUBMIT_CHANNEL IN (3, 5, 9) AND
                 TD.PROPOSAL_STATUS = '13' AND TD.MEDIA_TYPE IN (0, 2)) OR
                 (TD.SUBMIT_CHANNEL IN (1) AND TD.PROPOSAL_STATUS = '14' AND
                 TD.MEDIA_TYPE IN (0) AND NPA.P_ACKNOWLEDGE_DATE IS NULL)
                 OR (TD.SUBMIT_CHANNEL IN (11,1) AND
                 TD.PROPOSAL_STATUS = '13'))
           )  CM
		  JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
		    ON CM.APPLY_CODE = CA.APPLY_CODE
		  JOIN DEV_NB.T_NB_POLICY_HOLDER PH
		    ON PH.APPLY_CODE = CM.APPLY_CODE
		  JOIN DEV_PAS.T_CUSTOMER CUS
		    ON CUS.CUSTOMER_ID = PH.CUSTOMER_ID
		  JOIN DEV_NB.T_NB_PAYER_ACCOUNT PA
		    ON PA.POLICY_ID = CM.POLICY_ID
		 WHERE 1 = 1 
		 	AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
		   	AND CA.AGENT_CODE = #{agent_code}]]>
		   	<if test = "customer_name != null and customer_name != '' "><![CDATA[AND CUS.CUSTOMER_NAME = #{customer_name}]]></if>
		   	<if test = "customer_certi_code != null and customer_certi_code != '' "><![CDATA[AND CUS.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
		    <if test = "customer_cert_type != null and customer_cert_type != '' "><![CDATA[AND CUS.CUSTOMER_CERT_TYPE = #{customer_cert_type}]]></if>
		    <![CDATA[
		 		ORDER BY CM.APPLY_DATE DESC ) RELT
		 		WHERE ROWNUM <= 50
		 	]]>
	</select>
<!-- 查询待回执签收保单(微信)-->	 
    <select id="queryAckSignWX" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		    SELECT RELT.* FROM ( 
			SELECT CM.MULTI_MAINRISK_FLAG,CM.APPLY_CODE,CM.CHANNEL_TYPE,
		       CM.POLICY_CODE,
		       CM.APPLY_DATE,
		       CUS.OLD_CUSTOMER_ID,
		       CUS.CUSTOMER_ID_CODE,
		       CUS.CUSTOMER_NAME,
		       CUS.CUSTOMER_GENDER,
		       CUS.CUSTOMER_BIRTHDAY,
		       CUS.MOBILE_TEL,
		       CUS.CUSTOMER_CERTI_CODE,
		       CUS.CUSTOMER_CERT_TYPE,
		       CM.SUBMIT_CHANNEL,
		       CM.SUBINPUT_TYPE,
		       CM.MEDIA_TYPE,
		       TO_CHAR(CM.SELF_APPLY_FLAG) SELF_APPLY_FLAG,
		       (SELECT T.MEDIA_TYPE FROM DEV_PAS.T_CONTRACT_MASTER T WHERE T.POLICY_CODE = CM.POLICY_CODE) MEDIA_TYPE1,
		       CM.DRQ_FLAG,
		       (SELECT TB.BANK_NAME FROM DEV_NB.T_BANK TB WHERE TB.BANK_CODE =  PA.ACCOUNT_BANK) AS SERVICE_BANK,
		    	CM.SERVICE_BANK AS SERVICE_BANK_NUM,
		       (SELECT TA.ADDRESS FROM DEV_NB.T_ADDRESS TA WHERE TA.ADDRESS_ID = PH.ADDRESS_ID) AS ADDRESS,
		       CM.ISSUE_DATE,
		        (select TCA.AGENT_CODE
                  from DEV_NB.t_Nb_Contract_Agent TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS AGENT_CODE,
			    (select TCA.AGENT_CODE
                  from DEV_NB.t_Nb_Contract_Agent TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS SALE_AGENT_CODE,
               (select TCA.AGENT_NAME
                  from DEV_NB.t_Nb_Contract_Agent TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS AGENT_NAME,
			    (select TCA.AGENT_NAME
                  from DEV_NB.t_Nb_Contract_Agent TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS SALE_AGENT_NAME,
               CM.Special_Account_Flag,
               (select TCA.Organ_Code
                  from DEV_NB.t_Nb_Contract_Agent TCA
                 where TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) as ORGAN_CODE,
               CM.Validate_Date,
               (SELECT ORG.ORGAN_ADDRESS
                  FROM APP___PAS__DBUSER.T_UDMP_ORG ORG
                  LEFT JOIN DEV_NB.t_Nb_Contract_Agent TCA
                    ON ORG.Organ_Code = TCA.Organ_Code
                 WHERE TCA.Policy_Code = CM.Policy_Code
                   AND TCA.Is_Current_Agent = 1
                   AND ROWNUM <= 1) AS ORGAN_ADDRESS,
		       CM.PROPOSAL_STATUS,
		       CM.CONFIRM_WAY,
		       PA.ACCOUNT,
		       PA.NEXT_ACCOUNT,
		       CM.POLICY_REINSURE_FLAG,
		       
		       /*需求 #122752-保单查询接口-待回执签收 变更----start */
				(SELECT TO_CHAR(CU.OLD_CUSTOMER_ID)
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDNO,
		       (SELECT TO_CHAR(CU.CUSTOMER_ID_CODE)
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDIDNO,
		       (SELECT CU.CUSTOMER_NAME
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDNAME,
		       (SELECT TO_CHAR(CU.CUSTOMER_CERT_TYPE)
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDIDTYPE,
		       (SELECT CU.CUSTOMER_CERTI_CODE
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS SECONDINSUREDIDNUMBER,
		       (SELECT TO_CHAR(TIL.RELATION_TO_PH)
		          FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_CUSTOMER CU
		         WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
		           AND TIL.ORDER_ID = 2
		           AND TIL.APPLY_CODE = CM.APPLY_CODE) AS APPNTANDSECONDINSUREDRELATION,
				/*需求 #122752-保单查询接口-待回执签收 变更----end */
		       
		       (SELECT COUNT(1)
		          FROM DEV_UW.T_UW_POLICY UP
		         WHERE UP.APPLY_CODE = CM.APPLY_CODE
		           AND UP.UW_SOURCE_TYPE = '1'
		           AND UP.POLICY_DECISION = '50') AS DECL_TOTAL,
		       (SELECT TO_CHAR(WM_CONCAT(DISTINCT QT.QA_TYPE))
		          FROM DEV_NB.T_NB_QT_TASK QT
		         WHERE QT.QA_TYPE IN ('4', '5')
		           AND QT.QT_STATUS = 7
		           AND QT.APPLY_CODE = CM.APPLY_CODE) AS QA_TYPES,
		           (CASE
         WHEN CM.OLD_POLICY_CODE IS NOT NULL THEN
          (SELECT CA.SERVICE_TYPE
             FROM DEV_PAS.T_RENEW_CHANGE RC
            INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
               ON RC.CHANGE_ID = AC.CHANGE_ID
              AND RC.ACCEPT_ID = AC.ACCEPT_ID
            INNER JOIN DEV_PAS.T_CS_APPLICATION CA
               ON AC.CHANGE_ID = CA.CHANGE_ID
            INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
               ON RC.NEW_BUSI_PRD_ID = BP.BUSINESS_PRD_ID
            WHERE RC.POLICY_CODE = CM.OLD_POLICY_CODE
                 
              AND AC.ACCEPT_STATUS = '18'
              AND RC.RENEW_CHANGE_TYPE = '3'
              AND AC.SERVICE_CODE = 'RR'
              AND (BP.PRODUCT_CODE_SYS = '********' or BP.PRODUCT_CODE_SYS = '********')
              AND ROWNUM = 1)
         ELSE
          ''
       END) AS RR_INFO /*保全转保信息*/     
		  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
		  JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
		    ON CM.APPLY_CODE = CA.APPLY_CODE
		  JOIN DEV_NB.T_NB_POLICY_HOLDER PH
		    ON PH.APPLY_CODE = CM.APPLY_CODE
		  JOIN DEV_PAS.T_CUSTOMER CUS
		    ON CUS.CUSTOMER_ID = PH.CUSTOMER_ID
		  JOIN DEV_NB.T_NB_PAYER_ACCOUNT PA
		    ON PA.POLICY_ID = CM.POLICY_ID
		 WHERE 1 = 1]]>
		   	<if test = "organ_code != null and organ_code != '' "><![CDATA[AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		    <if test = "agent_code != null and agent_code != '' "><![CDATA[AND CA.AGENT_CODE = #{agent_code}]]></if>
		   	<if test = "customer_name != null and customer_name != '' "><![CDATA[AND CUS.CUSTOMER_NAME = #{customer_name}]]></if>
		   	<if test = "customer_certi_code != null and customer_certi_code != '' "><![CDATA[AND CUS.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
		   	<if test = "customer_cert_type != null and customer_cert_type != '' "><![CDATA[AND CUS.CUSTOMER_CERT_TYPE = #{customer_cert_type}]]></if>
		    <![CDATA[AND CM.APPLY_CODE IN
		       (SELECT APPLY_CODE
              FROM DEV_NB.T_NB_CONTRACT_MASTER TD
              LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT NPA ON TD.POLICY_CODE=NPA.POLICY_CODE
             WHERE TD.APPLY_CODE = CM.APPLY_CODE               
                AND ((TD.SUBMIT_CHANNEL IN (4,5,9,11,1) AND TD.PROPOSAL_STATUS='13'))
		        )
		 		ORDER BY CM.ISSUE_DATE ASC ) RELT
		 		WHERE ROWNUM <= 50
		 	]]>
	</select>	
<!-- 查询待回执签收条数 -->
<select id="queryAckSignTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(CM.APPLY_CODE)
		  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
		  JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
		    ON CM.APPLY_CODE = CA.APPLY_CODE]]>
		  <if test = "customer_name != null and customer_name != ''  or  customer_certi_code != null and customer_certi_code != ''
		  or customer_cert_type != null and customer_cert_type != '' ">
		  <![CDATA[JOIN DEV_NB.T_NB_POLICY_HOLDER PH
		    ON PH.APPLY_CODE = CM.APPLY_CODE
		  JOIN DEV_PAS.T_CUSTOMER CUS
		    ON CUS.CUSTOMER_ID = PH.CUSTOMER_ID]]></if>
		 <![CDATA[WHERE 1 = 1
		 	AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
		   	AND CA.AGENT_CODE = #{agent_code}]]>
		   	<if test = "customer_name != null and customer_name != '' "><![CDATA[AND CUS.CUSTOMER_NAME = #{customer_name}]]></if>
		   	<if test = "customer_certi_code != null and customer_certi_code != '' "><![CDATA[AND CUS.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
		   	<if test = "customer_cert_type != null and customer_cert_type != '' "><![CDATA[AND CUS.CUSTOMER_CERT_TYPE = #{customer_cert_type}]]></if>
		    <![CDATA[AND CM.APPLY_CODE IN
		       (SELECT APPLY_CODE
              FROM DEV_NB.T_NB_CONTRACT_MASTER TD
              LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT NPA ON TD.POLICY_CODE=NPA.POLICY_CODE
             WHERE TD.APPLY_CODE = CM.APPLY_CODE               
                AND ((TD.SUBMIT_CHANNEL IN (3,5,9) AND TD.PROPOSAL_STATUS='13' AND 
                TD.MEDIA_TYPE IN(0,2))
                OR (TD.SUBMIT_CHANNEL IN (1) AND TD.PROPOSAL_STATUS='14' AND 
                   TD.MEDIA_TYPE IN(0) AND NPA.P_ACKNOWLEDGE_DATE IS NULL ))
		        )
		 	]]>
	</select>
    <!-- 查询待回执签收条数(微信)-->
    <select id="queryAckSignTotalWX" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(CM.APPLY_CODE)
		  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
		  JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
		    ON CM.APPLY_CODE = CA.APPLY_CODE]]>
		  <if test = "customer_name != null and customer_name != ''   or  customer_certi_code != null and customer_certi_code != ''
		    or customer_cert_type != null and customer_cert_type != ''">
		  <![CDATA[JOIN DEV_NB.T_NB_POLICY_HOLDER PH
		    ON PH.APPLY_CODE = CM.APPLY_CODE
		  JOIN DEV_PAS.T_CUSTOMER CUS
		    ON CUS.CUSTOMER_ID = PH.CUSTOMER_ID]]></if>
		 <![CDATA[WHERE 1 = 1]]>
		    <if test = "organ_code != null and organ_code != '' "><![CDATA[AND CM.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		    <if test = "agent_code != null and agent_code != '' "><![CDATA[AND CA.AGENT_CODE = #{agent_code}]]></if>
		   	<if test = "customer_name != null and customer_name != '' "><![CDATA[AND CUS.CUSTOMER_NAME = #{customer_name}]]></if>
		   	<if test = "customer_certi_code != null and customer_certi_code != '' "><![CDATA[AND CUS.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]></if>
		   	<if test = "customer_cert_type != null and customer_cert_type != '' "><![CDATA[AND CUS.CUSTOMER_CERT_TYPE = #{customer_cert_type}]]></if>
		    <![CDATA[AND CM.APPLY_CODE IN
		       (SELECT APPLY_CODE
              FROM DEV_NB.T_NB_CONTRACT_MASTER TD
              LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT NPA ON TD.POLICY_CODE=NPA.POLICY_CODE
             WHERE TD.APPLY_CODE = CM.APPLY_CODE               
                AND ((TD.SUBMIT_CHANNEL IN (4,5,9,11,1) AND TD.PROPOSAL_STATUS='13'))
		        )
		 	]]>
	</select>

	<!-- 查询被保人信息 -->
	<select id="queryInsuredListByApplyCode_lnts" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT * FROM (
			SELECT C.OLD_CUSTOMER_ID,
		       C.CUSTOMER_ID_CODE,
		       C.CUSTOMER_NAME,
		       C.CUSTOMER_CERT_TYPE,
		       C.CUSTOMER_CERTI_CODE,
		       A.RELATION_TO_PH,
               A.POLICY_CODE 
		  FROM DEV_NB.T_NB_INSURED_LIST    A,
		       DEV_PAS.T_CUSTOMER          C
		 WHERE A.ORDER_ID = '1'
		   AND A.CUSTOMER_ID = C.CUSTOMER_ID
		   AND A.APPLY_CODE = #{apply_code}
		   and rownum = 1) TIL1
		   
	/*需求 #122752-保单查询接口-待回执签收 变更----start */
		    LEFT JOIN (SELECT *
              FROM (SELECT TIL.POLICY_CODE,
                            TO_CHAR(TIL.RELATION_TO_PH)     APPNTANDSECONDINSUREDRELATION,
                            TO_CHAR(CU.OLD_CUSTOMER_ID)         SECONDINSUREDNO,
                            CU.CUSTOMER_NAME       SECONDINSUREDNAME,
                            TO_CHAR(CU.CUSTOMER_CERT_TYPE)  SECONDINSUREDIDTYPE,
                            CU.CUSTOMER_CERTI_CODE SECONDINSUREDIDNUMBER,
                            TO_CHAR(CU.CUSTOMER_ID_CODE)    SECONDINSUREDIDNO
                       FROM DEV_NB.T_NB_INSURED_LIST    TIL,
                            DEV_NB.T_CUSTOMER     CU
                      WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
                        AND TIL.ORDER_ID = '2'
                        AND TIL.APPLY_CODE =  #{apply_code})) TIL2
    ON TIL1.POLICY_CODE = TIL2.POLICY_CODE
    /*需求 #122752-保单查询接口-待回执签收 变更----end */
		]]>
	</select>
<!-- rm149387查询被保人信息列表 -->
	<select id="queryNbInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT C.OLD_CUSTOMER_ID,
       C.CUSTOMER_ID_CODE,
       C.CUSTOMER_NAME,
       C.CUSTOMER_CERT_TYPE,
       C.CUSTOMER_CERTI_CODE,
       A.RELATION_TO_PH,
       A.POLICY_CODE,       
       C.CUSTOMER_ID,
       B.BUSI_ITEM_ID,
       B.ORDER_ID
  FROM DEV_NB.T_NB_INSURED_LIST A
  LEFT JOIN DEV_PAS.T_CUSTOMER C
    ON A.CUSTOMER_ID = C.CUSTOMER_ID
  LEFT JOIN DEV_NB.T_NB_BENEFIT_INSURED B
    ON A.APPLY_CODE = B.APPLY_CODE
   AND A.LIST_ID = B.INSURED_ID
 WHERE 1 = 1
   AND A.APPLY_CODE = #{apply_code}
		]]>
	</select>
	<!-- 查询第二被保人信息 -->
	<select id="queryInsuredListByApplyCode_2nts" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT C.OLD_CUSTOMER_ID,
		       C.CUSTOMER_ID_CODE,
		       C.CUSTOMER_NAME,
		       C.CUSTOMER_CERT_TYPE,
		       C.CUSTOMER_CERTI_CODE,
		       A.RELATION_TO_PH
		  FROM DEV_NB.T_NB_INSURED_LIST    A,
		       DEV_PAS.T_CUSTOMER          C
		 WHERE A.ORDER_ID = '2'
		   AND A.CUSTOMER_ID = C.CUSTOMER_ID
		   AND A.APPLY_CODE = #{apply_code}
		]]>
	</select>
	
	<!-- 查询受益人信息 -->
	<select id="queryContractBeneByApplyCode_lnts" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ 
				SELECT C.CUSTOMER_NAME,
			       C.CUSTOMER_CERT_TYPE,
			       C.CUSTOMER_GENDER,
			       C.CUSTOMER_BIRTHDAY,
			       C.CUSTOMER_CERTI_CODE,
			       A.DESIGNATION AS RELATION_TO_PH,
			       A.BENE_TYPE
			  	FROM DEV_NB.T_NB_CONTRACT_BENE A, DEV_PAS.T_CUSTOMER C
			 	WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
			   	AND A.APPLY_CODE = #{apply_code}
			]]>
	</select>

	<!-- 查询险种信息 -->
	<select id="queryContractBusiProdByApplyCode_lnts" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ 
				SELECT A.MASTER_BUSI_ITEM_ID,
			       A.BUSI_ITEM_ID,
			       A.PRODUCT_CODE,
			       A.RENEW,
			       A. HESITATION_PERIOD_DAY,
			       B.PRODUCT_NAME_SYS,
			       B.Product_Abbr_Name,
                   B.Cover_Period_Type,
                   B.Product_Category1,
                   B.Tax_Extension_Flag
			  	FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD A, DEV_PDS.T_BUSINESS_PRODUCT B
			 	WHERE B.PRODUCT_CODE_SYS = A.PRODUCT_CODE
			   	AND A.APPLY_CODE = #{apply_code}
			   	ORDER BY A.ORDER_ID
			]]>
	</select>
	<!-- 查询保全转保信息 -->
	<select id="queryAckSignRRInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			 SELECT CA.SERVICE_TYPE AS RR_INFO
             FROM DEV_PAS.T_RENEW_CHANGE RC
            INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
               ON RC.CHANGE_ID = AC.CHANGE_ID
              AND RC.ACCEPT_ID = AC.ACCEPT_ID
            INNER JOIN DEV_PAS.T_CS_APPLICATION CA
               ON AC.CHANGE_ID = CA.CHANGE_ID
            INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
               ON RC.NEW_BUSI_PRD_ID = BP.BUSINESS_PRD_ID
            WHERE 1=1
              AND RC.POLICY_CODE = #{Old_Policy_Code}                
              AND AC.ACCEPT_STATUS = '18'
              AND RC.RENEW_CHANGE_TYPE = '3'
              AND AC.SERVICE_CODE = 'RR'
              AND (BP.PRODUCT_CODE_SYS = '********' or BP.PRODUCT_CODE_SYS = '********')
              AND ROWNUM = 1
		]]>
	</select>
	<!-- #164727【接口需求】 续保审核结论通知书-新时代对接新核心-QRY-3/4   根据入参保单号查询，按照查询规则返回通知书信息-->
	<select id="NB_queryReDecAuditingDocNoPoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.STATUS,
				       T.BUSS_CODE,
				       T.DOCUMENT_NO,
				       T.POLICY_CODE,
				       T.DOCUMENT_NAME,
				       T.TEMPLATE_CODE,
				       T.DOC_LIST_ID,
       				   A.CARD_CODE
				  FROM DEV_NB.T_DOCUMENT T
				  LEFT JOIN DEV_NB.T_DOCUMENT_TEMPLATE A
  					ON T.TEMPLATE_CODE=A.TEMPLATE_CODE
				 WHERE ROWNUM <= 1000
				   AND T.STATUS IN (2, 4, 5)
				   AND T.TEMPLATE_CODE = 'UWS_00021'
				   AND T.POLICY_CODE = #{policy_code} ]]>
		<![CDATA[ ORDER BY T.DOC_LIST_ID ]]> 
	</select>
</mapper>
