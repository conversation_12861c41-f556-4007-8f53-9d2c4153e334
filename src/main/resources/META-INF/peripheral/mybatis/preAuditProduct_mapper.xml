<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.PreAuditProductDaoImpl">
<!--
	<sql id="NB_preAuditProductWhereCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" insert_timestamp  != null "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" audit_id  != null "><![CDATA[ AND A.AUDIT_ID = #{audit_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" UNIT  != null "><![CDATA[ AND A.UNIT = #{UNIT} ]]></if>
		<if test=" pay_freq  != null "><![CDATA[ AND A.PAY_FREQ = #{pay_freq} ]]></if>
		<if test=" age  != null "><![CDATA[ AND A.AGE = #{age} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" period_prem  != null "><![CDATA[ AND A.PERIOD_PREM = #{period_prem} ]]></if>
		<if test=" job_class  != null "><![CDATA[ AND A.JOB_CLASS = #{job_class} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" gender  != null "><![CDATA[ AND A.GENDER = #{gender} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" initial_type  != null "><![CDATA[ AND A.INITIAL_TYPE = #{initial_type} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>		

	<sql id="NB_queryByAuditIdCondition">
		<if test=" audit_id  != null "><![CDATA[ AND tap.AUDIT_ID = #{audit_id} ]]></if>
		<if test="busi_prd_id !=null "><![CDATA[ AND tap.busi_prd_id = #{busi_prd_id} ]]> </if>
	</sql>		

<!-- 添加操作 -->
	<insert id="NB_addPreAuditProduct"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id"> SELECT DEV_NB.S_t_pre_audit_product.NEXTVAL FROM DUAL </selectKey>
			<![CDATA[
			INSERT INTO    DEV_NB.T_PRE_AUDIT_PRODUCT (
				BENEFIT_LEVEL, PRODUCT_ID, PRODUCT_CODE, BUSI_PRD_ID, COVERAGE_PERIOD, INSERT_TIMESTAMP, UPDATE_BY, 
				CHARGE_YEAR, AUDIT_ID, LIST_ID, PAY_FREQ, UNIT, AGE, COVERAGE_YEAR, 
				PERIOD_PREM, INSERT_TIME, UPDATE_TIME, JOB_UNDERWRITE, BUSI_PROD_PACK_CODE, AMOUNT, PAY_YEAR, 
				CHARGE_PERIOD, PAY_PERIOD, GENDER, UPDATE_TIMESTAMP, INSERT_BY, PREM_FREQ ) 
			VALUES (
				#{benefit_level, jdbcType=VARCHAR}, #{product_id, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{busi_prd_id, jdbcType=NUMERIC} , #{coverage_period, jdbcType=VARCHAR} , SYSDATE, #{update_by, jdbcType=NUMERIC} 
				, #{charge_year, jdbcType=NUMERIC} , #{audit_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{pay_freq, jdbcType=VARCHAR} , #{unit, jdbcType=NUMERIC} , #{age, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} 
				, #{period_prem, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{job_underwrite, jdbcType=NUMERIC} , #{busi_prod_pack_code, jdbcType=VARCHAR} , #{amount, jdbcType=NUMERIC} , #{pay_year, jdbcType=NUMERIC} 
				, #{charge_period, jdbcType=VARCHAR} , #{pay_period, jdbcType=VARCHAR} , #{gender, jdbcType=NUMERIC} , SYSDATE, #{insert_by, jdbcType=NUMERIC} , #{prem_freq, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="NB_deletePreAuditProduct" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_PRE_AUDIT_PRODUCT         WHERE LIST_ID            = #{list_id           } ]]>
	</delete>

<!-- 修改操作 -->
		<update id="NB_updatePreAuditProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE    DEV_NB.T_PRE_AUDIT_PRODUCT  ]]>
		<set>
		<trim suffixOverrides=",">
			BENEFIT_LEVEL = #{benefit_level, jdbcType=VARCHAR} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    BUSI_PRD_ID = #{busi_prd_id, jdbcType=NUMERIC} ,
			COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    AUDIT_ID = #{audit_id, jdbcType=NUMERIC} ,
		    PAY_FREQ = #{pay_freq, jdbcType=VARCHAR} ,
		    UNIT = #{unit, jdbcType=NUMERIC} ,
		    AGE = #{age, jdbcType=NUMERIC} ,
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
		    PERIOD_PREM = #{period_prem, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    JOB_UNDERWRITE = #{job_underwrite, jdbcType=NUMERIC} ,
			BUSI_PROD_PACK_CODE = #{busi_prod_pack_code, jdbcType=VARCHAR} ,
		    AMOUNT = #{amount, jdbcType=NUMERIC} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
			PAY_PERIOD = #{pay_period, jdbcType=VARCHAR} ,
		    GENDER = #{gender, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = SYSDATE ,
			PREM_FREQ = #{prem_freq, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="NB_findByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.BUSI_PRD_ID, A.COVERAGE_PERIOD, 
			A.CHARGE_YEAR, A.AUDIT_ID, A.LIST_ID, A.PAY_FREQ, A.UNIT, A.AGE, A.COVERAGE_YEAR, 
			A.PERIOD_PREM, A.JOB_UNDERWRITE, A.BUSI_PROD_PACK_CODE, A.AMOUNT, A.PAY_YEAR, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.GENDER, A.PREM_FREQ FROM    DEV_NB.T_PRE_AUDIT_PRODUCT  A WHERE 1 = 1  ]]>
		<include refid="NB_queryByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="NB_findAllMapPreAuditProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.BUSI_PRD_ID, A.COVERAGE_PERIOD, 
			A.CHARGE_YEAR, A.AUDIT_ID, A.LIST_ID, A.PAY_FREQ, A.UNIT, A.AGE, A.COVERAGE_YEAR, 
			A.PERIOD_PREM, A.JOB_UNDERWRITE, A.BUSI_PROD_PACK_CODE, A.AMOUNT, A.PAY_YEAR, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.GENDER, A.PREM_FREQ FROM    DEV_NB.T_PRE_AUDIT_PRODUCT  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<!-- 根据auditid查询所有 -->
	<select id="NB_findAllPreAuditProductByAuditId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select tap.BENEFIT_LEVEL,
               tap.PRODUCT_ID,
               tap.PRODUCT_CODE,
               tap.BUSI_PRD_ID,
               tap.COVERAGE_PERIOD,
               tap.CHARGE_YEAR,
               tap.AUDIT_ID,
               tap.LIST_ID,
               tap.PAY_FREQ,
               tap.UNIT,
               tap.AGE,
               tap.COVERAGE_YEAR,
               tap.PERIOD_PREM,
               tap.JOB_UNDERWRITE,
               tap.BUSI_PROD_PACK_CODE,
               tap.AMOUNT,
               tap.PAY_YEAR,
               tap.CHARGE_PERIOD,
               tap.PAY_PERIOD,
               tap.GENDER,
               to_char(TAP.PREM_FREQ)  PREM_FREQ,
               tbp.product_category,
               tbp.product_code_sys,
               tpl.option_type
          from DEV_NB.T_PRE_AUDIT_PRODUCT tap,
               DEV_NB.T_business_product  tbp,
               DEV_NB.T_product_life      tpl
         where tap.busi_prd_id = tbp.business_prd_id
           and tbp.business_prd_id = tpl.business_prd_id  ]]>
		<include refid="NB_queryByAuditIdCondition" />
		<![CDATA[ ORDER BY tap.LIST_ID            ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllPreAuditProduct" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.BUSI_PRD_ID, A.COVERAGE_PERIOD, 
			A.CHARGE_YEAR, A.AUDIT_ID, A.LIST_ID, A.PAY_FREQ, A.UNIT, A.AGE, A.COVERAGE_YEAR, 
			A.PERIOD_PREM, A.JOB_UNDERWRITE, A.BUSI_PROD_PACK_CODE, A.AMOUNT, A.PAY_YEAR, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.GENDER, A.PREM_FREQ FROM    DEV_NB.T_PRE_AUDIT_PRODUCT  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findPreAuditProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_PRE_AUDIT_PRODUCT         A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryPreAuditProductForPage" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT B.RN AS rowNumber, B.BENEFIT_LEVEL, B.PRODUCT_ID, B.PRODUCT_CODE, B.BUSI_PRD_ID, B.COVERAGE_PERIOD, 
			B.CHARGE_YEAR, B.AUDIT_ID, B.LIST_ID, B.PAY_FREQ, B.UNIT, B.AGE, B.COVERAGE_YEAR, 
			B.PERIOD_PREM, B.JOB_UNDERWRITE, B.BUSI_PROD_PACK_CODE, B.AMOUNT, B.PAY_YEAR, 
			B.CHARGE_PERIOD, B.PAY_PERIOD, B.GENDER, B.PREM_FREQ FROM (
					SELECT ROWNUM RN, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.PRODUCT_CODE, A.BUSI_PRD_ID, A.COVERAGE_PERIOD, 
			A.CHARGE_YEAR, A.AUDIT_ID, A.LIST_ID, A.PAY_FREQ, A.UNIT, A.AGE, A.COVERAGE_YEAR, 
			A.PERIOD_PREM, A.JOB_UNDERWRITE, A.BUSI_PROD_PACK_CODE, A.AMOUNT, A.PAY_YEAR, 
			A.CHARGE_PERIOD, A.PAY_PERIOD, A.GENDER, A.PREM_FREQ FROM    DEV_NB.T_PRE_AUDIT_PRODUCT  A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<delete id="NB_deletePreAuditProductByAuditId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_PRE_AUDIT_PRODUCT         WHERE AUDIT_ID            = #{audit_id} ]]>
	</delete>	
	
	<select id="queryPreAuditProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT PREM_FREQ,(SELECT SUM(PERIOD_PREM) FROM DEV_NB.T_PRE_AUDIT_PRODUCT 
					WHERE AUDIT_ID=#{audit_id}) AS PERIOD_PREM
					FROM DEV_NB.T_PRE_AUDIT_PRODUCT WHERE AUDIT_ID=#{audit_id} AND ROWNUM=1   ]]>
	</select>
	<select id="NBFindInfoByAuditId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT PAP.PRODUCT_CODE , 
						PAP.COVERAGE_YEAR ,
						PAP.PREM_FREQ,
						PAP.CHARGE_PERIOD,
						PAP.CHARGE_YEAR,
						PAP.PAY_YEAR,
						PAP.PAY_FREQ,
						PAP.AMOUNT,
						PAP.UNIT,
						PAP.PERIOD_PREM ,
						(SELECT AP.LIST_ID FROM DEV_NB.T_AGENT_PRODUCT AP WHERE AP.AGENT_CODE = #{agent_code} AND AP.PRODUCT_CODE = PAP.PRODUCT_CODE) AS SALEFLAG ,
						BP.PRODUCT_NAME_SYS
 						FROM DEV_NB.T_PRE_AUDIT_PRODUCT PAP LEFT JOIN DEV_NB.T_BUSINESS_PRODUCT BP ON PAP.PRODUCT_CODE = BP.PRODUCT_CODE_SYS
 						WHERE PAP.AUDIT_ID = #{audit_id}
		]]>
	</select>
	
	<select id="NBFindInfoByAuditId1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT PAP.PRODUCT_CODE , 
						PAP.COVERAGE_YEAR ,
						PAP.PREM_FREQ,
						PAP.CHARGE_PERIOD,
						PAP.CHARGE_YEAR,
						PAP.PAY_YEAR,
						PAP.PAY_FREQ,
						PAP.AMOUNT,
						PAP.UNIT,
						PAP.PERIOD_PREM ,
						(SELECT AP.LIST_ID FROM DEV_NB.T_AGENT_PRODUCT AP WHERE AP.PRODUCT_CODE = PAP.PRODUCT_CODE AND ROWNUM = 1) AS SALEFLAG ,
						BP.PRODUCT_NAME
 						FROM DEV_NB.T_PRE_AUDIT_PRODUCT PAP LEFT JOIN DEV_PDS.T_PRODUCT_LIFE BP ON PAP.PRODUCT_CODE = BP.INTERNAL_ID
 						WHERE PAP.AUDIT_ID = #{audit_id}
		]]>
	</select>
</mapper>
