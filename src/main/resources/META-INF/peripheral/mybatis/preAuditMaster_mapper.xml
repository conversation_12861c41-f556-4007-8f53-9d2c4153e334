<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.PreAuditMasterDaoImpl">
	<!-- <sql id="NB_preAuditMasterWhereCondition"> 
	<if test=" audit_conclusion 
		!= null "><![CDATA[ AND A.AUDIT_CONCLUSION = #{audit_conclusion} ]]>
		</if> 
		<if test=" audit_organ_code != null and audit_organ_code != '' "><![CDATA[ 
		AND A.AUDIT_ORGAN_CODE = #{audit_organ_code} ]]>
		</if> <if test=" audit_by 
		!= null "><![CDATA[ AND A.AUDIT_BY = #{audit_by} ]]>
		<
		/if> <if test=" busi_prod_pack_code 
		!= null and busi_prod_pack_code != '' "><![CDATA[ AND A.BUSI_PROD_PACK_CODE 
		= #{busi_prod_pack_code} ]]></if> 
		<if test=" high_sa_indi != null "><![CDATA[ 
		AND A.HIGH_SA_INDI = #{high_sa_indi} ]]></if> 
		<if test=" agent_sincerity_level 
		!= null "><![CDATA[ AND A.AGENT_SINCERITY_LEVEL = #{agent_sincerity_level} 
		]]></if>
		 <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND 
		A.APPLY_CODE = #{apply_code} ]]></if> 
		<if test=" bank_agent_code != null 
		and bank_agent_code != '' "><![CDATA[ AND A.BANK_AGENT_CODE = #{bank_agent_code} 
		]]></if> <if test=" insert_timestamp != null and insert_timestamp != '' "><![CDATA[ 
		AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if> <if test=" custom_level 
		!= null and custom_level != '' "><![CDATA[ AND A.CUSTOM_LEVEL = #{custom_level} 
		]]></if> <if test=" manual_uw_indi != null "><![CDATA[ AND A.MANUAL_UW_INDI 
		= #{manual_uw_indi} ]]></if> <if test=" birthday_indi != null "><![CDATA[ 
		AND A.BIRTHDAY_INDI = #{birthday_indi} ]]></if> <if test=" audit_id != null 
		"><![CDATA[ AND A.AUDIT_ID = #{audit_id} ]]></if> <if test=" manual_uw_tip 
		!= null and manual_uw_tip != '' "><![CDATA[ AND A.MANUAL_UW_TIP = #{manual_uw_tip} 
		]]></if> <if test=" update_timestamp != null and update_timestamp != '' "><![CDATA[ 
		AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if> <if test=" policy_id 
		!= null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if> <if test=" agent_code 
		!= null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} 
		]]></if> <if test=" submit_date != null and submit_date != '' "><![CDATA[ 
		AND A.SUBMIT_DATE = #{submit_date} ]]></if> </sql> -->



	<!-- 根据apply_code查询 -->
	<select id="NBFindInfoByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		
		SELECT DISTINCT 
					PAM.AUDIT_ID, 
					PAM.APPLY_CODE , 
					PAM.UPDATE_BY ,
					PAM.UPDATE_TIME,
					PAM.CONCLUSION_CODE,
					PAM.MANUAL_UW_INDI ,   
					PAP.PREM_FREQ ,
					PAM.AGENT_CODE ,
					PAM.AGENT_MOBILE , 
					PAM.AGENT_LEVEL , 
					PAM.QUALIFICATION_VALID ,
					PAM.BANK_CODE ,
					PAM.BANK_AGENT_CODE ,
					PAM.BRANCH_VALID ,
					PAC.CAUSE_CODE ,
					NCM.APPLY_DATE ,
					NCM.ORGAN_CODE ,
					C.CUSTOMER_LEVEL,
					(SELECT PAP.BUSI_PRD_ID FROM DEV_NB.T_BUSINESS_PRODUCT BP WHERE  BP.PRODUCT_CODE_SYS='********' AND PAP.BUSI_PRD_ID = BP.PRODUCT_CODE_STD) AS RELATEFLAGCHECK--对于RELATEFLAGCHECK字段，判断这个字段的值是否为NULL即可，   --查询时候查询出多条数据，但是只有PREM_FREQ这个
		FROM 
					((((DEV_NB.T_PRE_AUDIT_MASTER PAM LEFT JOIN DEV_NB.T_PRE_AUDIT_PRODUCT PAP ON PAM.AUDIT_ID = PAP.AUDIT_ID) 
    				LEFT JOIN DEV_NB.T_PRE_AUDIT_CAUSE PAC ON PAM.AUDIT_ID = PAC.AUDIT_ID ) 
    				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON PAM.APPLY_CODE = NCM.APPLY_CODE ) 
     				LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER NPH ON NPH.APPLY_CODE = PAM.APPLY_CODE )
    				LEFT JOIN DEV_NB.T_CUSTOMER C ON C.CUSTOMER_ID = NPH.CUSTOMER_ID 
      				WHERE PAM.APPLY_CODE =	#{apply_code}
		
		  ]]>
	</select>

	<select id="NBFindPremByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT SUM(PAP.PERIOD_PREM) AS TOTAL FROM DEV_NB.T_PRE_AUDIT_PRODUCT PAP ,(SELECT PAM.* FROM DEV_NB.T_PRE_AUDIT_MASTER PAM WHERE PAM.APPLY_CODE = #{apply_code}) PAM1 WHERE PAP.AUDIT_ID = PAM1.AUDIT_ID
		
		  ]]>
	</select>


	<!-- 根据apply_code查询PreAudit -->
	<select id="NBFindPreAuditByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		
			      SELECT    M.AUDIT_ID,
			      			M.APPLY_CODE,
			      			M.UPDATE_BY,
			      			M.UPDATE_TIME,
			      			M.CONCLUSION_CODE
			      FROM	DEV_NB.T_PRE_AUDIT_MASTER M
				  WHERE M.APPLY_CODE = #{apply_code}
		  ]]>
	</select>

	<!-- 根据apply_code查询 -->
	<!-- 新契约 T_PRE_AUDIT_MASTER.BANK_CODE,T_PRE_AUDIT_MASTER.BANK_AGENT_CODE,T_PRE_AUDIT_MASTER.BRANCH_VALID字段删除-->
	<select id="NBFindInfoByApplyCode1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		
                 SELECT   M.AUDIT_ID
			             ,M.APPLY_CODE
			             ,M.UPDATE_BY
			             ,M.UPDATE_TIME,
                   (select a.conclusion_desc from dev_nb.T_AUDIT_CONCLUSION a where a.conclusion_code = M.CONCLUSION_CODE) CONCLUSION_CODE_NAME
                   ,C.CUSTOMER_LEVEL
                   ,M.MANUAL_UW_INDI
                   
                   ,CON.APPLY_DATE
                        ,CON.ORGAN_CODE
                   ,M.AGENT_CODE
                   ,(SELECT A.AGENT_NAME FROM DEV_PAS.T_AGENT A WHERE A.AGENT_CODE = M.AGENT_CODE) AGENT_NAME
                   ,(SELECT A.CERTI_CODE FROM DEV_PAS.T_AGENT A WHERE A.AGENT_CODE = M.AGENT_CODE) CERTI_CODE
                   ,M.AGENT_MOBILE
                   ,M.AGENT_LEVEL
                   ,M.QUALIFICATION_VALID
                   ,C.CUSTOMER_CERT_TYPE
                   ,C.MOBILE_TEL
                   ,C.OFFEN_USE_TEL
                   ,T1.CUSTOMER_ID
                   ,T1.CUSTOMER_NAME
                   
                   ,T1.CUSTOMER_CERTI_CODE
                   ,T1.CUSTOMER_GENDER
                   ,T1.CUSTOMER_BIRTHDAY
                   ,T1.JOB_CODE
                   ,T1.JOB_UNDERWRITE
                   ,T2.CUSTOMER_GENDER  CUSTOMER_GENDER2
                   ,T2.CUSTOMER_BIRTHDAY  CUSTOMER_BIRTHDAY2
      
            FROM dev_nb.T_PRE_AUDIT_MASTER M
            LEFT JOIN dev_nb.T_NB_CONTRACT_MASTER CON ON M.APPLY_CODE = CON.APPLY_CODE
            LEFT JOIN dev_nb.T_NB_POLICY_HOLDER H ON M.APPLY_CODE = H.APPLY_CODE
            LEFT JOIN dev_nb.T_CUSTOMER C ON H.CUSTOMER_ID = C.CUSTOMER_ID
      
            LEFT JOIN (SELECT C1.CUSTOMER_ID, C1.CUSTOMER_NAME,C1.CUSTOMER_CERTI_CODE,C1.CUSTOMER_BIRTHDAY
            ,C1.CUSTOMER_CERT_TYPE,C1.CUSTOMER_GENDER,L.JOB_CODE,L.JOB_UNDERWRITE,L.APPLY_CODE
            FROM dev_nb.T_CUSTOMER C1 ,dev_nb.T_NB_INSURED_LIST L,dev_nb.T_NB_BENEFIT_INSURED BI
            WHERE C1.CUSTOMER_ID = L.CUSTOMER_ID 
            AND L.LIST_ID = BI.INSURED_ID
            AND BI.ORDER_ID = 1  
            ) T1 ON T1.APPLY_CODE = M.APPLY_CODE
            
            LEFT JOIN (SELECT C2.CUSTOMER_BIRTHDAY,C2.CUSTOMER_GENDER,L2.APPLY_CODE
            FROM dev_nb.T_CUSTOMER C2,dev_nb.T_NB_INSURED_LIST L2,dev_nb.T_NB_BENEFIT_INSURED BI2
            WHERE C2.CUSTOMER_ID = L2.CUSTOMER_ID
            AND L2.LIST_ID = BI2.INSURED_ID
            AND BI2.ORDER_ID = 2) T2 ON T2.APPLY_CODE = M.APPLY_CODE
			      
				  WHERE 1=1
		  ]]>
		  
		  <if test=" apply_code != null and apply_code != '' ">
		   <![CDATA[  AND M.APPLY_CODE = #{apply_code} ]]>
		  </if>
		 
		  <if test=" policy_code != null and policy_code != '' ">
		   <![CDATA[  AND CON.POLICY_CODE = #{policy_code} ]]>
		  </if>
		 
		
	</select>
	
	<!-- 查询首期保费 -->
	<select id="NBFindPremByAuditId1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT 
		 SUM(P.PERIOD_PREM) TOTAL
		FROM DEV_NB.T_PRE_AUDIT_MASTER M,
		 DEV_NB.T_PRE_AUDIT_PRODUCT P 
		WHERE M.AUDIT_ID = P.AUDIT_ID 
		AND M.AUDIT_ID = #{audit_id}
		  ]]>
	</select>
	<!-- 查询交费方式 -->
	<select id="NBFindPayIntvByAuditId1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT PREM_FREQ 
			FROM DEV_NB.T_PRE_AUDIT_PRODUCT
			WHERE AUDIT_ID = #{audit_id}
		  ]]>
	</select>
	
	<!-- 查询客户分级 -->
	<select id="NBFindCusLevelByAuditId1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT C.CUSTOMER_LEVEL
			  FROM DEV_NB.T_CUSTOMER C
			 WHERE C.CUSTOMER_ID IN (SELECT H.CUSTOMER_ID
			                          FROM DEV_NB.T_NB_POLICY_HOLDER H
			                         WHERE EXISTS (SELECT 1
			                                  FROM DEV_NB.T_PRE_AUDIT_MASTER M
			                                 WHERE M.APPLY_CODE = H.APPLY_CODE
			                                   AND M.AUDIT_ID = #{audit_id}))
		  ]]>
	</select>
	
	<!-- 已提供并审核客户需求分析和风险承受能力问卷(初审结论result不为空，且 list.size()>0) -->
	<select id="NBFindRiskFlag1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			    SELECT S.*
				FROM DEV_NB.T_CUSTOMER_SURVEY S 
				     ,DEV_NB.T_SURVEY_TEMPLATE T
				WHERE S.SURVEY_TEMPLATE_ID = T.SURVEY_TEMPLATE_ID 
				AND T.SURVEY_TEMPL_CODE = '1003'
		  ]]>
		  <if test=" apply_code != null and apply_code != '' ">
		   <![CDATA[  AND S.APPLY_CODE = #{apply_code} ]]>
		  </if>
		 
		  <if test=" policy_code != null and policy_code != '' ">
		   <![CDATA[  AND S.POLICY_CODE = #{policy_code} ]]>
		  </if>
	</select>
	
	<!-- 是否同时投保祥和特享款 -->
	<select id="NBFindRelateFlagCheck1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			   SELECT P.BUSI_PRD_ID ,BP.BUSINESS_PRD_ID
				FROM DEV_NB.T_PRE_AUDIT_PRODUCT P
				     ,DEV_NB.T_BUSINESS_PRODUCT BP
				WHERE P.BUSI_PRD_ID = BP.BUSINESS_PRD_ID 
				AND BP.PRODUCT_CODE_SYS = '********'
				AND P.AUDIT_ID = #{audit_id}
		  ]]>
	</select>
</mapper>
