<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.NbPolicyHolderDaoImpl">
<!--
	<sql id="NB_nbPolicyHolderWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" applicant_age  != null "><![CDATA[ AND A.APPLICANT_AGE = #{applicant_age} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="NB_queryByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="NB_queryByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="NB_queryByCustomerIdCondition">
		<if test=" customer_id != null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	<!-- 投保单号不等于 -->
	<sql id="NB_queryByNotEqualsApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE != #{apply_code} ]]></if>
	</sql>
	<sql id="NB_findPayModeByCodeCondition">
		<if test=" code != null and code != '' "><![CDATA[ AND A.CODE = #{code} ]]></if>
	</sql>
<!-- 添加操作 -->
	<insert id="NB_addNbPolicyHolder"  useGeneratedKeys="true"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT DEV_NB.S_t_nb_policy_holder.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_NB.T_NB_POLICY_HOLDER         (
				ADDRESS_ID, INSERT_TIME, CUSTOMER_ID, JOB_CODE, UPDATE_TIME, INSERT_TIMESTAMP, 
				POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, APPLY_CODE,JOB_UNDERWRITE, RESIDENT_TYPE ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, SYSDATE , #{customer_id, jdbcType=NUMERIC} , #{job_code, jdbcType=VARCHAR} , SYSDATE , SYSDATE
				, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , 
				#{apply_code, jdbcType=VARCHAR}, #{job_underwrite, jdbcType=VARCHAR}, #{resident_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="NB_deleteNbPolicyHolder" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_POLICY_HOLDER          WHERE LIST_ID            = #{list_id           } ]]>
	</delete>

<!-- 修改操作 -->
	<update id="NB_updateHolderNbPolicyHolder" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_POLICY_HOLDER          ]]>
		<set>
		<trim suffixOverrides=",">
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID            = #{policy_id           } ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="NB_findByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findHolderByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, A.APPLY_CODE,A.RESIDENT_TYPE,
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE,B.APPLY_DATE FROM DEV_NB.T_NB_POLICY_HOLDER A,
			DEV_NB.T_NB_CONTRACT_MASTER B WHERE 1 = 1 AND A.POLICY_ID=B.POLICY_ID ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByAddressIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<select id="NB_findByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<select id="NB_findByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<!-- 根据客户id查询 zhuhaixin-->
	<select id="NB_findNbPolicyHolderByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, A.APPLY_CODE,
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM  DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="NB_findAllMapNbPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllNbPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findNbPolicyHolderTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>
	
<!-- 查询某个客户下保单号个数 -->
	<select id="NB_findNbPolicyCustomerTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(A.POLICY_ID) FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByCustomerIdCondition" />
	</select>
<!-- 查询个数操作(投保人，被保人，保单主表，险种，责任五个表的联合查询，用在初审既往信息查询，投保单信息部分 zhuhaixin) -->
	<select id="NB_findNbCoreTotal" resultType="java.lang.Integer"
	parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM
					(SELECT B.APPLY_CODE,
							       B.APPLY_DATE,
							       A.CUSTOMER_ID,
							       E.CUSTOMER_ID,
							       C.PRODUCT_CODE,
							       D.AMOUNT
							  FROM DEV_NB.T_NB_POLICY_HOLDER      A,
							       DEV_NB.T_NB_CONTRACT_MASTER    B,
							       DEV_NB.T_NB_CONTRACT_BUSI_PROD C,
							       DEV_NB.T_NB_CONTRACT_PRODUCT   D,
							       DEV_NB.T_NB_INSURED_LIST       E,
							       DEV_NB.T_NB_BENEFIT_INSURED F
							 WHERE A.APPLY_CODE = B.APPLY_CODE
							   AND B.POLICY_ID = C.POLICY_ID
							   AND C.BUSI_ITEM_ID = D.BUSI_ITEM_ID
							   AND B.POLICY_ID = E.POLICY_ID
							   AND B.POLICY_ID = F.POLICY_ID
							   AND F.ORDER_ID = 1
							   AND C.MASTER_BUSI_ITEM_ID IS NULL
							   AND A.CUSTOMER_ID = ${customer_id}
							UNION
							SELECT B.APPLY_CODE,
							       B.APPLY_DATE,
							       A.CUSTOMER_ID,
							       E.CUSTOMER_ID,
							       C.PRODUCT_CODE,
							       D.AMOUNT
							  FROM DEV_NB.T_NB_POLICY_HOLDER      A,
							       DEV_NB.T_NB_CONTRACT_MASTER    B,
							       DEV_NB.T_NB_CONTRACT_BUSI_PROD C,
							       DEV_NB.T_NB_CONTRACT_PRODUCT   D,
							       DEV_NB.T_NB_INSURED_LIST       E
							 WHERE A.POLICY_ID = B.POLICY_ID
							   AND B.POLICY_ID = C.POLICY_ID
							   AND C.BUSI_ITEM_ID = D.BUSI_ITEM_ID
							   AND B.APPLY_CODE = E.APPLY_CODE
							  
							   AND C.MASTER_BUSI_ITEM_ID IS NULL
							   AND E.CUSTOMER_ID = ${customer_id})  ]]>
</select>
	<!-- 查询个数操作 -->
	<select id="NB_findNbPolicyHolderNum" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryNbPolicyHolderForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.CUSTOMER_ID, B.JOB_CODE, 
			B.POLICY_CODE, B.LIST_ID, B.POLICY_ID,B.JOB_UNDERWRITE FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID ,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 分页查询操作 (投保人，被保人，保单主表，险种，责任五个表的联合查询，用在初审既往信息查询，投保单信息部分 zhuhaixin) -->
	<select id="NB_queryNbCoreForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
									<![CDATA[  SELECT G.RN AS ROWNUMBER, G.APPLY_CODE,
														      G.APPLY_DATE,
														      G.HOLDER_CUS_ID,
														      G.INSURED_CUS_ID,
														      G.PRODUCT_CODE,
														      G.AMOUNT FROM 
							  ( SELECT ROWNUM RN , F.APPLY_CODE,
														       F.APPLY_DATE,
														       F.HOLDER_CUS_ID,
														       F.INSURED_CUS_ID,
														       F.PRODUCT_CODE,
														       F.AMOUNT FROM( SELECT B.APPLY_CODE,
							       B.APPLY_DATE,
							       A.CUSTOMER_ID HOLDER_CUS_ID,
							       E.CUSTOMER_ID INSURED_CUS_ID,
							       C.PRODUCT_CODE,
							       D.AMOUNT
							  FROM DEV_NB.T_NB_POLICY_HOLDER      A,
							       DEV_NB.T_NB_CONTRACT_MASTER    B,
							       DEV_NB.T_NB_CONTRACT_BUSI_PROD C,
							       DEV_NB.T_NB_CONTRACT_PRODUCT   D,
							       DEV_NB.T_NB_INSURED_LIST       E,
							       DEV_NB.T_NB_BENEFIT_INSURED F
							 WHERE A.APPLY_CODE = B.APPLY_CODE
							   AND B.POLICY_ID = C.POLICY_ID
							   AND C.BUSI_ITEM_ID = D.BUSI_ITEM_ID
							   AND B.POLICY_ID = E.POLICY_ID
							   AND B.POLICY_ID = F.POLICY_ID
							   AND F.ORDER_ID = 1
							   AND C.MASTER_BUSI_ITEM_ID IS NULL
							   AND A.CUSTOMER_ID = ${customer_id}
							UNION
							SELECT B.APPLY_CODE,
							       B.APPLY_DATE,
							       A.CUSTOMER_ID HOLDER_ID,
							       E.CUSTOMER_ID INSURED_ID,
							       C.PRODUCT_CODE,
							       D.AMOUNT
							  FROM DEV_NB.T_NB_POLICY_HOLDER      A,
							       DEV_NB.T_NB_CONTRACT_MASTER    B,
							       DEV_NB.T_NB_CONTRACT_BUSI_PROD C,
							       DEV_NB.T_NB_CONTRACT_PRODUCT   D,
							       DEV_NB.T_NB_INSURED_LIST       E
							 WHERE A.POLICY_ID = B.POLICY_ID
							   AND B.POLICY_ID = C.POLICY_ID
							   AND C.BUSI_ITEM_ID = D.BUSI_ITEM_ID
							   AND B.APPLY_CODE = E.APPLY_CODE
							  
							   AND C.MASTER_BUSI_ITEM_ID IS NULL
							   AND E.CUSTOMER_ID = ${customer_id} ) F WHERE ROWNUM <= #{LESS_NUM}) G
							                     WHERE G.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="NB_findAllHolderByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
		<!--根据投保单号或保单号 查询投保人姓名 -->	
	
		<select id="NB_findPolCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select distinct A.APPLY_CODE,A.policy_Code,B.CUSTOMER_NAME from DEV_NB.T_NB_POLICY_HOLDER A,DEV_NB.T_CUSTOMER B 
		  where A.CUSTOMER_ID = B.CUSTOMER_ID  ]]>
		<include refid="NB_queryByPolCustomerCondition" />
	</select>
	<sql id="NB_queryByPolCustomerCondition">
		<if test=" apply_code != null and apply_code != '' and policy_code != null and policy_code != ''"><![CDATA[and ( A.APPLY_CODE=#{apply_code} or A.policy_Code=#{policy_code}) ]]></if>
		<if test=" (apply_code == null or apply_code == '') and policy_code != null and policy_code != '' "><![CDATA[and A.policy_Code=#{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != '' and (policy_code == null or policy_code == '') "><![CDATA[and A.APPLY_CODE=#{apply_code} ]]></if>
	</sql>
		<!--根据applyCode 查询投customerId和policyId -->
	<select id="NB_findPolicyHolderByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE ,B.ORGAN_CODE,
			C.CUSTOMER_NAME,C.CUSTOMER_GENDER,C.MOBILE_TEL,C.CUSTOMER_CERT_TYPE,C.CUSTOMER_CERTI_CODE,C.CUSTOMER_BIRTHDAY
       FROM DEV_NB.T_NB_POLICY_HOLDER A,DEV_NB.T_NB_CONTRACT_MASTER B,DEV_NB.T_CUSTOMER C
        WHERE 1 = 1 AND A.POLICY_ID =B.POLICY_ID AND C.CUSTOMER_ID = A.CUSTOMER_ID  ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<select id="NB_findPolicyHolderByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ADDRESS_ID, INSERT_TIME, CUSTOMER_ID, JOB_CODE, UPDATE_TIME, INSERT_TIMESTAMP, 
						POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, APPLY_CODE,JOB_UNDERWRITE 
				FROM DEV_NB.T_NB_POLICY_HOLDER A WHERE 1 = 1 ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID  DESC          ]]>
	</select>
	<select id="NB_queryNbPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  C.ANNUAL_INCOME FROM DEV_NB.T_NB_POLICY_HOLDER H LEFT JOIN 
		DEV_NB.T_CUSTOMER C ON H.CUSTOMER_ID=C.CUSTOMER_ID WHERE H.APPLY_CODE=#{apply_code}]]>
	</select>
	<!-- 查投保人姓名赋值给首期账户姓名 -->
	<select id="NB_findHolderCustomerName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  C.CUSTOMER_NAME FROM DEV_NB.T_NB_POLICY_HOLDER H LEFT JOIN 
		          DEV_NB.T_CUSTOMER C ON H.CUSTOMER_ID=C.CUSTOMER_ID WHERE H.APPLY_CODE=#{apply_code}]]>
	</select>
	<!-- 根据customer_id 和 apply_code 查询投保人为除该保单外的所有投保人信息 -->
	<select id="NB_findPolicyHolderByCustomerIdAndNotApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, A.APPLY_CODE,
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER A,DEV_NB.T_nb_contract_master B  WHERE 1 = 1 AND A.POLICY_ID =B.Policy_Id AND B.Policy_Code is null AND B.Proposal_Status != '00'  ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<include refid="NB_queryByNotEqualsApplyCodeCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]> 
	</select>
	<!-- 根据 customerId 和 policyId 更新身高、体重 -->
	<update id="NB_updatePolicyHolderHeightAndWeight" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_POLICY_HOLDER          ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
			CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE ,
		</trim>
		</set>
		<!-- <include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByCustomerIdCondition" /> -->
		<![CDATA[ WHERE CUSTOMER_ID = #{customer_id} AND POLICY_ID =#{policy_id} ]]>
	</update>
	<!-- 根据 customerId 和 policyId 更新社保标识 -->
	<update id="NB_updatePolicyHolderSociSecu" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_NB.T_NB_POLICY_HOLDER           ]]>
		<set>
		<trim suffixOverrides=",">
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		</trim>
		</set>
		<![CDATA[ WHERE CUSTOMER_ID = #{customer_id} AND POLICY_ID =#{policy_id} ]]>
	</update>
	<select id="NB_findHolderCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
       select CUSTOMER_ID,
       UN_CUSTOMER_CODE,
       MARRIAGE_DATE,
       EDUCATION,
       CUSTOMER_NAME,
       CUSTOMER_BIRTHDAY,
       CUSTOMER_GENDER,
       CUSTOMER_HEIGHT,
       CUSTOMER_WEIGHT,
       CUSTOMER_CERT_TYPE,
       CUSTOMER_CERTI_CODE,
       CUSTOMER_ID_CODE,
       CUST_CERT_STAR_DATE,
       CUST_CERT_END_DATE,
       JOB_CODE,
       JOB_NATURE,
       JOB_KIND,
       JOB_TITLE,
       MARRIAGE_STATUS,
       IS_PARENT,
       ANNUAL_INCOME,
       COUNTRY_CODE,
       RELIGION_CODE,
       NATION_CODE,
       DRIVER_LICENSE_TYPE,
       COMPANY_NAME,
       OFFEN_USE_TEL,
       HOUSE_TEL,
       FAX_TEL,
       OFFICE_TEL,
       MOBILE_TEL,
       EMAIL,
       QQ,
       WECHAT_NO,
       OTHER,
       CUSTOMER_LEVEL,
       CUSTOMER_VIP,
       SMOKING_FLAG,
       DRUNK_FLAG,
       BLACKLIST_FLAG,
       HOUSEKEEPER_FLAG,
       SYN_MDM_FLAG,
       LIVE_STATUS,
       RETIRED_FLAG,
       DEATH_DATE,
       HEALTH_STATUS,
       REMARK,
       CUST_PWD,
       INSERT_BY,
       INSERT_TIME,
       INSERT_TIMESTAMP,
       UPDATE_BY,
       UPDATE_TIME,
       UPDATE_TIMESTAMP,
       OLD_CUSTOMER_ID,
       CUSTOMER_RISK_LEVEL
        from DEV_NB.T_customer WHERE 1 = 1
         ]]>
		<include refid="findHolderCustomer" />
		<!-- <![CDATA[ ORDER BY A.LIST_ID            ]]> -->
	</select>
	<sql id="findHolderCustomer">
		<if test=" customer_id  != null "><![CDATA[ AND CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	<select id="NB_findPayModeByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.code,A.name from DEV_NB.T_pay_mode A where 1=1  ]]>
		<include refid="NB_findPayModeByCodeCondition" />
	</select>
	<!-- 删除操作 -->	
	<delete id="NB_deleteNbPolicyHolderByPolicyId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_NB_POLICY_HOLDER   WHERE POLICY_ID = #{policy_id} ]]>
	</delete>
	<!-- 根据客户id查询保单 -->
	<select id="NB_findPolicyHolderByCustomerIdAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE,A.APPLY_CODE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<!-- 查询同一营业区业务员手机号 -->
	<select id="NB_findAgentMobile"  resultType="java.util.Map"  parameterType="java.util.Map">
	<![CDATA[
                 
SELECT TA.AGENT_CODE,
       TA.AGENT_NAME,
       TA.POSTAL_ADDRESS,
       TA.HOME_ADDRESS,
       TA.AGENT_EMAIL,
       TA.AGENT_MOBILE,
       TA.AGENT_PHONE,
       TA.BIRTHDAY,
       TA.AGENT_GENDER,
       TA.CERTI_CODE,
       TA. CERT_TYPE,
       TA.EMPLOYEE_FLAG,
       TA.AGENT_NORMAL_TYPE,
       TA.AGENT_ORGAN_CODE,
       TA.AGENT_LEVEL,
       TA.SGENT_HORNER_LEVEL,
       TA.AGENT_STATUS,
       TA.EMPLOYMENT_DATE,
       TA.DISMISSAL_DATE,
       TA.INSERT_BY,
       TA.INSERT_TIME,
       TA.INSERT_TIMESTAMP,
       TA.UPDATE_BY,
       TA.UPDATE_TIME,
       TA.UPDATE_TIMESTAMP,
       TA.SALES_ORGAN_CODE,
       TA.AGENT_CHANNEL
        FROM DEV_PAS.T_AGENT TA WHERE TA.SALES_ORGAN_CODE IN (

SELECT
CASE WHEN E.ORGAN_LEVEL_CODE= 1
     THEN(
       SELECT C.B_SOC FROM(
         SELECT A.SALES_ORGAN_CODE A_SOC,B.SALES_ORGAN_CODE B_SOC FROM (
                (SELECT PARENT_CODE,SALES_ORGAN_CODE FROM DEV_NB.T_SALES_ORGAN WHERE ORGAN_LEVEL_CODE = 2) A
                LEFT JOIN
                (SELECT SALES_ORGAN_CODE FROM DEV_NB.T_SALES_ORGAN WHERE ORGAN_LEVEL_CODE = 3) B
                ON A.PARENT_CODE = B.SALES_ORGAN_CODE 
         )) C WHERE C.A_SOC = E.PARENT_CODE)    
       WHEN E.ORGAN_LEVEL_CODE= 2
         THEN(
            SELECT SALES_ORGAN_CODE FROM DEV_NB.T_SALES_ORGAN SO WHERE ORGAN_LEVEL_CODE = 3 AND SO.SALES_ORGAN_CODE = E.PARENT_CODE
         )  
       WHEN E.ORGAN_LEVEL_CODE= 3
         THEN
         E.SALES_ORGAN_CODE  
       END AS NNN FROM
  DEV_NB.T_SALES_ORGAN E WHERE E.SALES_ORGAN_CODE IN (  SELECT SALES_ORGAN_CODE FROM DEV_PAS.T_AGENT TT WHERE TT.AGENT_CODE=#{agent_code})
       
     ) ]]>
	</select>
	
	
	
	
	<!-- 根据客户id查询 zhuhaixin-->
	<select id="NB_findAllNbPolicyHolderForCRS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, A.APPLY_CODE,
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM  DEV_NB.T_NB_POLICY_HOLDER A ,DEV_NB.T_NB_CONTRACT_MASTER  B WHERE 1 = 1 AND A.POLICY_ID=B.POLICY_ID ]]>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>
		 <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND B.APPLY_CODE = #{apply_code} ]]></if>
		 <if test=" customer_id != null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	
	<!-- 根据投保单号,客户姓名查询 -->
	
	<select id="NB_findNbPolicyHolderByApplyCodeAndName" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	
	  select A.APPLY_CODE,B.CUSTOMER_ID,C.CUSTOMER_NAME

 from DEV_NB.T_NB_CONTRACT_MASTER A,DEV_NB.T_NB_POLICY_HOLDER  B,DEV_NB.T_CUSTOMER C
 
 WHERE A.POLICY_ID=B.POLICY_ID AND B.CUSTOMER_ID=C.CUSTOMER_ID AND A.APPLY_CODE=#{apply_code} AND C.CUSTOMER_NAME=#{customer_name}
	
	]]>
	</select>
	
	<select id="NB_findExternalSystemInfoForCrs" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	
	  SELECT T2.EXTERNAL_SYSTEM_URL HOST, T1.URL URI
  FROM DEV_NB.T_UDMP_MODULE               T1,
       DEV_NB.T_UDMP_EXTERNAL_SYSTEM_INFO T2
 WHERE T1.EXTERNAL_SYSTEM_ID = T2.EXTERNAL_SYSTEM_ID
   AND T1.MODULE_ID =#{module_id}
	
	]]>
	</select>
	
	
	<select id="NB_findAllCustomersInPolicyByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT DISTINCT CUSTOMER_ID FROM 
		(SELECT T1.CUSTOMER_ID
          FROM DEV_NB.T_NB_POLICY_HOLDER T1
         WHERE T1.APPLY_CODE=#{policy_code}
        UNION ALL
        SELECT T2.CUSTOMER_ID
          FROM DEV_NB.T_NB_INSURED_LIST T2
         WHERE T2.APPLY_CODE=#{policy_code}) ]]>
	</select>
</mapper>
