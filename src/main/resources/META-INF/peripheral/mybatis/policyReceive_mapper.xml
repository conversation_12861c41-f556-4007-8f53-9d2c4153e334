<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.PolicyReceiveDaoImpl">
	
	<!-- 查询逾期通知书个数操作 -->
	<select id="queryPolicyReceive" resultType="com.nci.tunan.qry.interfaces.model.po.PolicyReceivePO" parameterType="java.util.Map">
		 select distinct due.policy_code contno,
                due.liab_id,
                due.fee_amount,
                due.pay_due_date,
                p.liab_code as liab_name,
                case
                  when due.survival_invest_flag = '1' then
                   '是'
                  else
                   '否'
                end survival_invest_flag,
                p.end_date,
                lia.liab_category,
                pro.product_code,
                (select mode_name from dev_pas.t_survival_mode 
                where mode_code = p.survival_mode) as codename,
          (select bp.product_name_sys from dev_pds.t_business_product bp
          where substr(bp.product_code_sys,3,6) = pro.product_code) as riskname,
          cd.arap_bank_account as getbankaccno,
          (select b.bank_name from dev_pas.t_bank b 
          where b.bank_code = cd.arap_bank_code) as bankname,
                    (select  t.customer_name from dev_pas.t_customer t where t.customer_id=til.customer_id) as insuredname,
                (select max(arap.finish_time)  from dev_cap.V_prem_arap arap where arap.unit_number = due.unit_number )timea,
                (select max(det.finish_time ) from  dev_cap.V_cash_detail det where det.unit_number = due.unit_number )timed,
                (select max(det.payrefno ) from  dev_cap.V_cash_detail det where det.unit_number = due.unit_number )payrefno,
          (select bp.product_abbr_name from dev_pds.t_business_product bp
          where substr(bp.product_code_sys,3,6) = pro.product_code) as abbrriskname
    from dev_pas.t_pay_due due
    left join dev_pas.t_pay_plan p
      on due.plan_id = p.plan_id
    left join dev_cap.t_liability lia
      on due.liab_id = lia.liab_id
    left join dev_pas.t_contract_product pro
      on due.item_id = pro.item_id
    left join dev_cap.V_cash_detail cd
      on due.unit_number = cd.unit_number
     left join dev_pas.t_insured_list til
      on til.policy_id=due.policy_id 
    where due.fee_status ='01'
      and due.policy_code =#{contNo}
	</select>
	
</mapper>