<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.qry.impl.NbDocumentDaoImpl">
<!--
	<sql id="NB_documentWhereCondition">
		<if test=" buss_source_code != null and buss_source_code != ''  "><![CDATA[ AND A.BUSS_SOURCE_CODE = #{buss_source_code} ]]></if>
		<if test=" clob_id  != null "><![CDATA[ AND A.CLOB_ID = #{clob_id} ]]></if>
		<if test=" scan_by  != null "><![CDATA[ AND A.SCAN_BY = #{scan_by} ]]></if>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<if test=" print_time  != null  and  print_time  != ''  "><![CDATA[ AND A.PRINT_TIME = #{print_time} ]]></if>
		<if test=" create_time  != null  and  create_time  != ''  "><![CDATA[ AND A.CREATE_TIME = #{create_time} ]]></if>
		<if test=" send_by  != null "><![CDATA[ AND A.SEND_BY = #{send_by} ]]></if>
		<if test=" send_time  != null  and  send_time  != ''  "><![CDATA[ AND A.SEND_TIME = #{send_time} ]]></if>
		<if test=" create_by  != null "><![CDATA[ AND A.CREATE_BY = #{create_by} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" is_link  != null "><![CDATA[ AND A.IS_LINK = #{is_link} ]]></if>
		<if test=" buss_id  != null "><![CDATA[ AND A.BUSS_ID = #{buss_id} ]]></if>
		<if test=" print_by  != null "><![CDATA[ AND A.PRINT_BY = #{print_by} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" overdue_time  != null  and  overdue_time  != ''  "><![CDATA[ AND A.OVERDUE_TIME = #{overdue_time} ]]></if>
		<if test=" close_by  != null "><![CDATA[ AND A.CLOSE_BY = #{close_by} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" send_obj_type != null and send_obj_type != ''  "><![CDATA[ AND A.SEND_OBJ_TYPE = #{send_obj_type} ]]></if>
		<if test=" reply_time  != null  and  reply_time  != ''  "><![CDATA[ AND A.REPLY_TIME = #{reply_time} ]]></if>
		<if test=" document_name != null and document_name != ''  "><![CDATA[ AND A.DOCUMENT_NAME = #{document_name} ]]></if>
		<if test=" reply_remark != null and reply_remark != ''  "><![CDATA[ AND A.REPLY_REMARK = #{reply_remark} ]]></if>
		<if test=" overdue_document_no != null and overdue_document_no != ''  "><![CDATA[ AND A.OVERDUE_DOCUMENT_NO = #{overdue_document_no} ]]></if>
		<if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
		<if test=" close_time  != null  and  close_time  != ''  "><![CDATA[ AND A.CLOSE_TIME = #{close_time} ]]></if>
		<if test=" send_obj_id != null and send_obj_id != ''  "><![CDATA[ AND A.SEND_OBJ_ID = #{send_obj_id} ]]></if>
		<if test=" reprint_times  != null "><![CDATA[ AND A.REPRINT_TIMES = #{reprint_times} ]]></if>
		<if test=" document_no != null and document_no != ''  "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<if test=" is_merger  != null "><![CDATA[ AND A.IS_MERGER = #{is_merger} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" scan_time  != null  and  scan_time  != ''  "><![CDATA[ AND A.SCAN_TIME = #{scan_time} ]]></if>
		<if test=" reply_conclusion  != null "><![CDATA[ AND A.REPLY_CONCLUSION = #{reply_conclusion} ]]></if>
		<if test=" buss_code != null and buss_code != ''  "><![CDATA[ AND A.BUSS_CODE = #{buss_code} ]]></if>
		<if test=" reply_by  != null "><![CDATA[ AND A.REPLY_BY = #{reply_by} ]]></if>
		<if test=" reply_days  != null "><![CDATA[ AND A.REPLY_DAYS = #{reply_days} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<!-- <sql id="NB_queryDocumentByDocumentNoCondition">
		<if test=" document_no != null and document_no != '' "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
	</sql> -->
	<sql id="NB_queryDocumentByDocListIdCondition">
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	</sql>	
	<sql id="NB_queryDocumentByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="NB_queryDocumentByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="NB_queryDocumentByApplyCodeCondition">
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="NB_queryDocumentByBussSourceCodeCondition">
		<if test=" buss_source_code  != null "><![CDATA[ AND c.BUSS_SOURCE_CODE = #{buss_source_code} ]]></if>
	</sql>
	<sql id="NB_queryDocumentByCompDocumentNo">
		<if test="compDocumentNoSql != null  ">
		<![CDATA[ AND A.DOCUMENT_NO in ]]>
		<foreach item ="compDocumentNo" collection="compDocumentNoSql" open="(" separator="," close=")">
		<![CDATA[ ${compDocumentNo} ]]>
		</foreach>
		</if>
	</sql>	
<!-- 查询逾期通知书个数操作 -->
	<select id="queryDocument" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) 
   			FROM DEV_NB.T_DOCUMENT WHERE TEMPLATE_CODE='NBS_00010' AND
  			BUSS_CODE = #{buss_code}  ]]>
	</select>
	<!-- 查询逾期通知书个数操作 -->
	<select id="queryDocumentUwS" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) 
   			FROM DEV_UW.T_DOCUMENT WHERE TEMPLATE_CODE LIKE 'UWS%' AND
  			BUSS_CODE = #{buss_code}  ]]>
	</select>
</mapper>
