<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.OrderServiceDaoImpl">

	<sql id="PA_queryOrderInfoByServiceOrderIdCondition">
		<if test=" service_order_id  != null and service_order_id != '' "><![CDATA[ AND OS.SERVICE_ORDER_ID  = #{service_order_id} ]]></if>
	</sql>
 <!-- 报文对比修改 -->
	<select id="PA_queryOrderInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT OS.SERVICE_ORDER_ID,OS.INSERT_TIME,
              OS.BIZ_CODE,
              OS.BIZ_CODE_TYPE,
              OS.ORDER_TRANS_RESULT,
              case when  (select  (select nvl(SUM(FEE_AMOUNT),0) SumFeeAmount
               from dev_cap.v_prem_arap A where A.arap_flag='1' and A.BUSINESS_CODE=OS.Biz_Code) -(select nvl(SUM(FEE_AMOUNT),0) SumFeeAmount
                from dev_cap.v_prem_arap A where A.arap_flag='2' and A.BUSINESS_CODE=OS.Biz_Code) as SumFeeAmount from dual) >0 then '1'
                when  (select  (select nvl(SUM(FEE_AMOUNT),0) SumFeeAmount
               from dev_cap.v_prem_arap A where A.arap_flag='1' and A.BUSINESS_CODE=OS.Biz_Code) -(select nvl(SUM(FEE_AMOUNT),0) SumFeeAmount
                from dev_cap.v_prem_arap A where A.arap_flag='2' and A.BUSINESS_CODE=OS.Biz_Code) as SumFeeAmount from dual) <0 then '2'
                
                ELSE '0' END AS  ARAP_FLAG ,
	   
              (SELECT PC.CHANGE_STATUS FROM DEV_PAS.T_CHANGE PC WHERE PC.CHANGE_ID = AC.CHANGE_ID) CHANGE_STATUS,
              AC.ACCEPT_STATUS,
              OS.ORDER_TRANS_RESULT_DESC,
              (SELECT PA.FEE_STATUS FROM dev_cap.v_prem_arap PA WHERE PA.BUSINESS_CODE = OS.BIZ_CODE AND PA.FEE_STATUS<>'16' AND ROWNUM=1) FEE_STATUS,
              (SELECT PA.PAY_MODE FROM DEV_CAP.T_PREM_ARAP PA WHERE PA.BUSINESS_CODE = OS.BIZ_CODE  AND ROWNUM=1) PAY_MODE
              FROM DEV_PAS.T_ORDER_SERVICE    OS , DEV_PAS.T_CS_ACCEPT_CHANGE AC
          WHERE OS.BIZ_CODE_TYPE ='10' AND OS.BIZ_CODE = AC.ACCEPT_CODE AND AC.ACCEPT_STATUS IN ('13','18')
          
			]]>		
			<include refid="PA_queryOrderInfoByServiceOrderIdCondition" />
<![CDATA[      UNION     
          
       select  OS.SERVICE_ORDER_ID,OS.INSERT_TIME,
              OS.BIZ_CODE,
              OS.BIZ_CODE_TYPE,
              OS.ORDER_TRANS_RESULT,
              '1' ARAP_FLAG ,
              '' CHANGE_STATUS,
              '' ACCEPT_STATUS,
              OS.ORDER_TRANS_RESULT_DESC,
              (SELECT FEE_STATUS FROM 
                  ( SELECT PA.FEE_STATUS  
                FROM dev_cap.v_prem_arap PA,dev_cap.t_order_service OS WHERE  PA.Unit_Number =OS.BIZ_CODE and OS.SERVICE_ORDER_ID=#{service_order_id} and PA.FEE_STATUS<>'16'
                 ORDER BY  PA.INSERT_TIME DESC)  WHERE 1=1 AND  ROWNUM=1) FEE_STATUS,
      			(SELECT PA.PAY_MODE FROM DEV_CAP.T_PREM_ARAP PA WHERE PA.BUSINESS_CODE = OS.BIZ_CODE  AND ROWNUM=1) PAY_MODE
           from dev_cap.t_order_service OS WHERE OS.BIZ_CODE_TYPE = '11' and OS.SERVICE_ORDER_ID=#{service_order_id} AND OS.ORDER_TRANS_TYPE='2'  ORDER BY INSERT_TIME DESC
	      
	]]>
	</select>
	
<!-- 报文对比查询ServMoney业务金额 -->
	<select id="querySumFeeAmount" resultType="java.util.Map" parameterType="java.util.Map">
	
	<if test="type != null and 'BQ' ==type ">
	<![CDATA[
	
	select  ( select nvl(SUM(FEE_AMOUNT),0) SumFeeAmount
     from dev_cap.v_prem_arap A where A.arap_flag='1' AND A.FEE_STATUS='00' and A.BUSINESS_CODE=#{business_code}) -(select nvl(SUM(FEE_AMOUNT),0) SumFeeAmount
     from dev_cap.v_prem_arap A where A.arap_flag='2' AND A.FEE_STATUS='00' and A.BUSINESS_CODE=#{business_code}) as SumFeeAmount from dual
     ]]>
	</if>
	
	<if test="type != null and 'XQ' ==type ">
	<![CDATA[
	 select sum(t.fee_amount) as SumFeeAmount
                    from dev_cap.v_prem_arap t
                   where t.unit_number=#{business_code}
                     and t.deriv_type = '003'
                     
	]]>
	</if>
	</select>
	
	<!-- 查询业务金额 -->
	<select id="findSumFeeAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		
 SELECT H.FEE_AMOUNT 
    FROM (SELECT E.FEE_AMOUNT,                
                 e.Precont_Id
            FROM (SELECT SUM(DECODE(A.ARAP_FLAG,
                                    2,
                                    0 - A.FEE_AMOUNT,
                                    A.FEE_AMOUNT)) FEE_AMOUNT,                        
                         null as Precont_Id
                    FROM APP___PAS__DBUSER.T_PREM_ARAP        A,
                         APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B,
                         APP___PAS__DBUSER.T_CS_APPLICATION   C
                   WHERE B.ACCEPT_CODE = A.BUSINESS_CODE
                     AND B.CHANGE_ID = C.CHANGE_ID
                     AND A.DERIV_TYPE = '004'
                     AND A.FEE_STATUS IN ('00', '03')
                     AND B.ACCEPT_CODE=#{business_code}
                   GROUP BY B.change_id
                   
                   ]]>
       
        <![CDATA[
                
                UNION
                  SELECT SUM(DECODE(A.ARAP_FLAG,
                                    2,
                                    0 - A.FEE_AMOUNT,
                                    A.FEE_AMOUNT)) FEE_AMOUNT,                        
                         D.LOG_ID as Precont_Id
                    FROM APP___PAS__DBUSER.T_CS_PREM_ARAP     A,
                       APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE   B,
                       APP___PAS__DBUSER.T_CS_APPLICATION     C,
                       APP___PAS__DBUSER.T_CS_PRECONT_PRODUCT D
                   WHERE B.ACCEPT_CODE = A.BUSINESS_CODE
                     AND B.CHANGE_ID = C.CHANGE_ID
                     AND B.ACCEPT_ID = D.ACCEPT_ID
                     AND C.CHANGE_ID = D.CHANGE_ID
                     AND B.SERVICE_CODE = 'NS'
                     AND D.PRECONT_STATUS = '0'
                     AND B.ACCEPT_STATUS = '18'
                     AND A.FEE_STATUS = '16' /** 新增长期附加险会先默认一条16的数据 **/
                     AND A.DERIV_TYPE = '004'
                     AND B.ACCEPT_CODE=#{business_code}
                   
                   ]]>
        
        <![CDATA[
                 GROUP BY D.LOG_ID) E
) H

 ]]>
	</select>
	
	<!-- 订单号快查 -->
	<select id="qry_orderNoQuery" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1) FROM dev_pas.T_ORDER_SERVICE T 
		WHERE T.SERVICE_ORDER_ID =#{service_order_id} ]]>
	</select>
</mapper>
