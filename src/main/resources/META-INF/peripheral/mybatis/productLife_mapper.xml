<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.ProductLifeDaoImpl">

	<sql id="PA_productLifeWhereCondition">
		<if test=" option_type != null and option_type != ''  "><![CDATA[ AND A.OPTION_TYPE = #{option_type} ]]></if>
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" occupation_addfee_unit  != null "><![CDATA[ AND A.OCCUPATION_ADDFEE_UNIT = #{occupation_addfee_unit} ]]></if>
		<if test=" product_name != null and product_name != ''  "><![CDATA[ AND A.PRODUCT_NAME = #{product_name} ]]></if>
		<if test=" counter_way != null and counter_way != ''  "><![CDATA[ AND A.COUNTER_WAY = #{counter_way} ]]></if>
		<if test=" premium_unit  != null "><![CDATA[ AND A.PREMIUM_UNIT = #{premium_unit} ]]></if>
		<if test=" health_addfee_unit  != null "><![CDATA[ AND A.HEALTH_ADDFEE_UNIT = #{health_addfee_unit} ]]></if>
		<if test=" hobby_addfee_unit  != null "><![CDATA[ AND A.HOBBY_ADDFEE_UNIT = #{hobby_addfee_unit} ]]></if>
		<if test=" internal_id != null and internal_id != ''  "><![CDATA[ AND A.INTERNAL_ID = #{internal_id} ]]></if>
		<if test=" cashvalue_unit  != null "><![CDATA[ AND A.CASHVALUE_UNIT = #{cashvalue_unit} ]]></if>
		<if test=" sa_unit  != null "><![CDATA[ AND A.SA_UNIT = #{sa_unit} ]]></if>
	</sql>
	
<!-- 按索引查询操作 -->	
	<select id="PA_findProductLifeByProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM DEV_PAS.T_PRODUCT_LIFE A WHERE 1 = 1  ]]>
		<include refid="PA_productLifeWhereCondition" />
	</select>
	
	<select id="findProductLifeByProductIdPAandNB" resultType="java.util.Map" parameterType="java.util.Map">
		<if test="  type != null and type == '1'.toString()  ">
		<![CDATA[ SELECT A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM DEV_PDS.T_PRODUCT_LIFE A WHERE 1 = 1  ]]>
		<include refid="PA_productLifeWhereCondition" />
		</if>
		<if test="  type != null and type == '0'.toString()  ">
		<![CDATA[ SELECT A.OPTION_TYPE, A.BUSINESS_PRD_ID, A.PRODUCT_ID, A.OCCUPATION_ADDFEE_UNIT, A.PRODUCT_NAME, A.COUNTER_WAY, A.PREMIUM_UNIT, 
			A.HEALTH_ADDFEE_UNIT, A.HOBBY_ADDFEE_UNIT, A.INTERNAL_ID, A.CASHVALUE_UNIT, A.SA_UNIT FROM DEV_PDS.T_PRODUCT_LIFE A WHERE 1 = 1  ]]>
		<include refid="PA_productLifeWhereCondition" />
		</if>
	</select>
	
	
</mapper>
