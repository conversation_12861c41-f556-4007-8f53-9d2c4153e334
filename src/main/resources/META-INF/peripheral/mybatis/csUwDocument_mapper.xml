<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.csUwDocumentListDaoImpl">

<!-- 按索引生成的查询条件 --> 
	<sql id="uw_queryByPenoticeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND TC.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" endorse_code != null and endorse_code != '' "><![CDATA[ AND TC.ENDORSE_CODE = #{endorse_code} ]]></if>
		<if test=" accept_id != null and accept_id != '' "><![CDATA[ AND TC.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" document_no != null and document_no != '' "><![CDATA[ AND TD.DOCUMENT_NO = #{document_no} ]]></if>
		<if test=" customer_id != null and customer_id != '' "><![CDATA[ AND TP.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	
	<select id="qry_csUwDocumentListQuery" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT T2.*,
	       (CASE
	         WHEN T2.EXISTS_EXTRA_PREM >= 1 AND T2.DOC_STATUS = '1' THEN
	          (SELECT (CASE
	                    WHEN COUNT(*) >= 1 THEN
	                     MAX(PAR.FEE_STATUS) || ',' || MAX(PAR.FUNDS_RTN_CODE)
	                    ELSE
	                     NULL
	                  END)
	             FROM DEV_PAS.T_PREM_ARAP PAR
	            WHERE PAR.BUSINESS_CODE = T2.ACCEPT_CODE
	              AND PAR.POLICY_CODE = T2.POLICY_CODE)
	         ELSE
	          NULL
	       END) AS FEE_STATUS1,
	       (CASE
	         WHEN T2.EXISTS_EXTRA_PREM >= 1 AND T2.DOC_STATUS = '1' THEN
	          (SELECT (CASE
	                    WHEN COUNT(*) >= 1 THEN
	                     MAX(PAR.FEE_STATUS) || ',' || MAX(PAR.FUNDS_RTN_CODE)
	                    ELSE
	                     NULL
	                  END)
	             FROM DEV_CAP.T_PREM_ARAP PAR
	            WHERE PAR.BUSINESS_CODE = T2.ACCEPT_CODE
	              AND PAR.POLICY_CODE = T2.POLICY_CODE)
	         ELSE
	          NULL
	       END) AS FEE_STATUS
	  FROM (SELECT CA.POLICY_CODE, /*保单号*/
	               AC.ACCEPT_CODE, /*保全受理号*/
	               (SELECT S.SERVICE_NAME
	                  FROM DEV_PAS.T_SERVICE S
	                 WHERE S.SERVICE_CODE = AC.SERVICE_CODE
	                   AND ROWNUM = 1) AS SERVICE_NAME, /*保全项目名称*/
	               CA.APPLY_CODE, /*投保单号*/
	               M.ORGAN_CODE AS MANDGE_COM_NAME, /**/
	               CAC.APPLY_NAME AS APP_NAME, /*申请人姓名*/
	               CAC.CUSTOMER_ID AS APP_CUST_NO /*申请人客户号*/,
	               TO_CHAR(CAC.APPLY_TIME, 'YYYY-MM-DD') AS APPLY_DATE /*申请日期*/,
	               CA.AGENT_CODE, /*代理人编码*/
	               (CASE
	                 WHEN G.AGENT_CHANNEL = '03' OR G.AGENT_CHANNEL = '08' THEN
	                  (SELECT BANK_NAME
	                     FROM DEV_PAS.T_BANK
	                    WHERE BANK_CODE = M.SERVICE_BANK
	                      AND ROWNUM = 1)
	                 ELSE
	                  ''
	               END) AS BANK /*银行及储蓄所（代理专用）*/,
	               G.AGENT_CHANNEL,
	               (SELECT A.SALES_ORGAN_NAME
	                  FROM DEV_PAS.T_SALES_ORGAN A
	                 WHERE A.SALES_ORGAN_CODE =
	                       NVL(G.GROUP_CODE, G.SALES_ORGAN_CODE)
	                   AND ROWNUM = 1) AS BUSI_DEPARTMENT, /*业务分部及业务组*/
	               (SELECT B.SALES_ORGAN_NAME
	                  FROM DEV_PAS.T_SALES_ORGAN A
	                 INNER JOIN DEV_PAS.T_SALES_ORGAN B
	                    ON A.PARENT_CODE = B.SALES_ORGAN_CODE
	                 WHERE A.SALES_ORGAN_CODE =
	                       NVL(G.GROUP_CODE, G.SALES_ORGAN_CODE)
	                   AND ROWNUM = 1) AS SALES_DEPARTMENT, /*业务分部及业务组*/
	               G.AGENT_NAME, /*代理人姓名*/
	               D.DOCUMENT_NO AS DOC_NO, /*通知书号*/
	               (SELECT UU.REAL_NAME
	                  FROM DEV_NB.T_UDMP_USER UU
	                 WHERE UU.USER_ID = D.SEND_BY) AS UW_USER_NAME,
	               TO_CHAR(D.CREATE_TIME, 'YYYY-MM-DD') AS UW_DATE_DOC,
	               (CASE
	               	WHEN UP.POLICY_DECISION = '50' AND
                      D.TEMPLATE_CODE = 'UWS_00007' AND
                      (SELECT COUNT(1)
                         FROM DEV_UW.T_UW_BUSI_PROD UBP
                        WHERE UBP.MASTER_BUSI_ITEM_ID IS NULL
                          AND UBP.UW_ID = D.BUSS_ID
                          AND UBP.APPLY_CODE = D.BUSS_CODE
                          AND UBP.DECISION_CODE = '40') > 0 THEN
                  	  '次标'
	                 WHEN UP.POLICY_DECISION = '50' AND
	                      D.TEMPLATE_CODE = 'UWS_00007' THEN
	                  '整单拒保'
	                 WHEN UP.POLICY_DECISION = '40' AND
	                      D.TEMPLATE_CODE = 'UWS_00007' THEN
	                  '整单延期'
	                 WHEN UP.POLICY_DECISION = '20' AND
	                      D.TEMPLATE_CODE = 'UWS_00007' THEN
	                  '次标'
	                 ELSE
	                  DT.TEMPLATE_NAME
	               END) AS DOC_TYPE_NAME, /*通知书类型名称*/
	               DT.CARD_CODE AS DOC_TYPE, /*通知书类型码值*/
	               D.DOCUMENT_NO AS CARD_CODE, /*条形码/单证号码*/
	               D.DOC_LIST_ID, /*通知书主键序列号*/
                   (CASE WHEN  D.Template_Code='UWS_00003' then (select TO_CHAR(WM_CONCAT(PEN.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' || T3.CUSTOMER_NAME)) from DEV_UW.T_PENOTICE PEN,DEV_PAS.T_CUSTOMER T3 where PEN.CUSTOMER_ID = T3.CUSTOMER_ID AND PEN.DOC_LIST_ID = D.DOC_LIST_ID ) 
					 WHEN  D.Template_Code='UWS_00004' then (select TO_CHAR(WM_CONCAT(PEN.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' || T3.CUSTOMER_NAME)) from dev_uw.t_askforinfo PEN,DEV_PAS.T_CUSTOMER T3 where PEN.CUSTOMER_ID = T3.CUSTOMER_ID AND PEN.DOC_LIST_ID = D.DOC_LIST_ID ) 
        	         WHEN  D.TEMPLATE_CODE='UWS_00001' THEN (SELECT TO_CHAR(WM_CONCAT(T2.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' || T3.CUSTOMER_NAME))  FROM DEV_UW.T_ASKFORINFO T1,DEV_UW.T_ASKFORINFO_DETAIL T2,DEV_PAS.T_CUSTOMER T3 WHERE T1.ASKFORINFO_ID = T2.ASKFORINFO_ID AND T2.CUSTOMER_ID = T3.CUSTOMER_ID AND T1.DOC_LIST_ID = D.DOC_LIST_ID)
                     WHEN  D.Template_Code='UN012'  then (select TO_CHAR(WM_CONCAT(PEN.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' ||T3.CUSTOMER_NAME)) from dev_uw.t_survival_investigation PEN,DEV_PAS.T_CUSTOMER T3 where PEN.CUSTOMER_ID = T3.CUSTOMER_ID AND PEN.DOC_LIST_ID = D.DOC_LIST_ID )  
                     WHEN  D.Template_Code IN('UWS_00007','UWS_00009') THEN (SELECT TO_CHAR(WM_CONCAT(PEN.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' || T3.CUSTOMER_NAME)) FROM dev_uw.t_uw_document_verify PEN,DEV_PAS.T_CUSTOMER T3 WHERE  PEN.CUSTOMER_ID = T3.CUSTOMER_ID AND  PEN.DOCUMENT_NO =  D.DOCUMENT_NO)
                     ELSE ''
                     END   
                   ) AS SEND_OBJ_TYPE_INFO,/*接收对象信息*/
	               (CASE
	                 WHEN D.STATUS = '3' OR D.STATUS = '2' OR D.STATUS = '4' OR
	                      D.STATUS = '5' THEN
	                  '0'
	                 WHEN D.STATUS = '7' THEN
	                  '1'
	                 WHEN D.STATUS = '9' THEN
	                  '2'
	                 ELSE
	                  ''
	               END) AS DOC_STATUS, /*通知书状态*/
	               TO_CHAR(D.CREATE_TIME, 'YYYY-MM-DD') AS PRINT_DATE, /*打印日期*/
	               (SELECT COUNT(1)
	                  FROM DEV_UW.T_UW_EXTRA_PREM EP
	                 WHERE EP.UW_ID = UM.UW_ID
	                   AND EP.APPLY_CODE = M.APPLY_CODE) AS EXISTS_EXTRA_PREM /*是否存在加费*/
	          FROM DEV_UW.T_CONTRACT_AGENT CA
	         INNER JOIN DEV_UW.T_CONTRACT_MASTER M
	            ON CA.POLICY_CODE = M.POLICY_CODE
	           AND CA.UW_ID = M.UW_ID
	           AND M.DERIVATION = '2'
	           AND CA.AGENT_CODE = #{agent_code}]]>
			<if test="policy_code != null and policy_code != '' "> AND CA.POLICY_CODE = #{policy_code}/*保单号*/</if>
			<![CDATA[INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
	            ON M.POLICY_CODE = PC.POLICY_CODE
	         INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
	            ON PC.ACCEPT_ID = AC.ACCEPT_ID
	           AND AC.ACCEPT_STATUS NOT IN
	               ('18', '19', '14', '15', '16', '17', '22', '23')]]>
	         <if test="accept_code != null and accept_code != '' ">AND AC.ACCEPT_CODE = #{accept_code}</if>
	         <![CDATA[INNER JOIN DEV_PAS.T_CS_APPLICATION CAC
	            ON AC.CHANGE_ID = CAC.CHANGE_ID
	           AND PC.CHANGE_ID = AC.CHANGE_ID]]>
	         <if test="app_name != null and app_name != '' ">AND CAC.APPLY_NAME  = #{app_name}</if>
	         <![CDATA[INNER JOIN DEV_UW.T_UW_MASTER UM
	            ON AC.ACCEPT_CODE = UM.BIZ_CODE
	           AND CA.UW_ID = UM.UW_ID
	           AND UM.UW_SOURCE_TYPE = '2'
	           AND UM.UW_USER_ID IS NOT NULL
	         INNER JOIN DEV_PAS.T_AGENT G
	            ON CA.AGENT_CODE = G.AGENT_CODE
	         INNER JOIN DEV_UW.T_UW_POLICY UP
	            ON UM.UW_ID = UP.UW_ID
	           AND UP.POLICY_CODE = PC.POLICY_CODE
	           AND UM.UW_SOURCE_TYPE = '2'
	         INNER JOIN DEV_NB.T_DOCUMENT D
	            ON D.POLICY_CODE = UP.POLICY_CODE
	           AND D.BUSS_SOURCE_CODE = '002'
	           AND D.BUSS_ID = UM.UW_ID
	           AND D.TEMPLATE_CODE IN ('UWS_00001',
	                                   'UWS_00004',
	                                   'UWS_00003',
	                                   'UWS_00007', 'UWS_00009')
	         INNER JOIN DEV_NB.T_DOCUMENT_TEMPLATE DT
	            ON D.TEMPLATE_CODE = DT.TEMPLATE_CODE
	         ORDER BY CA.POLICY_CODE, AC.ACCEPT_CODE) T2]]>
    			  
	</select>
	
	<!-- 保单投保人信息查询 -->
	<select id="qry_findPolicyHolderCustomersInfo" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT P.POLICY_CODE,
			   C.OLD_CUSTOMER_ID AS CUSTOMER_NO, /*客户号*/
		       C.CUSTOMER_NAME AS APPLY_NAME, /*投保人姓名*/
		       (CASE
		         WHEN C.CUSTOMER_GENDER = '9' THEN
		          2
		         WHEN C.CUSTOMER_GENDER = '1' THEN
		          0
		         WHEN C.CUSTOMER_GENDER = '2' THEN
		          1
		         ELSE
		          C.CUSTOMER_GENDER
		       END) AS APPNT_SEX, /*投保人性别*/
		       to_char(C.CUSTOMER_BIRTHDAY, 'yyyy-MM-dd') AS APP_BIRTHDAY, /*投保人生日*/
		       C.CUSTOMER_CERT_TYPE AS APP_ID_TYPE, /*投保人证件类型*/
		       C.CUSTOMER_CERTI_CODE AS APP_ID_NO, /*投保人证件号码*/
		       to_char(C.CUST_CERT_STAR_DATE, 'yyyy-MM-dd') AS APPNT_ID_EFFSTARTDATE, /*证件有效起期*/
		       to_char(C.CUST_CERT_END_DATE, 'yyyy-MM-dd') AS APPNT_ID_EFFENDDATE, /*证件有效止期*/
		       AD.MOBILE_TEL AS MOBILE /*移动电话*/
		  FROM DEV_PAS.T_POLICY_HOLDER P
		 INNER JOIN DEV_PAS.T_CUSTOMER C
		    ON P.CUSTOMER_ID = C.CUSTOMER_ID
		 INNER JOIN DEV_PAS.T_ADDRESS AD
		    ON P.ADDRESS_ID = AD.ADDRESS_ID
		 WHERE P.POLICY_CODE IN (${policy_codes})
		
	</select>
	<!-- 保单被保人信息查询 -->
	<select id="qry_findInsuredCustomersInfo" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT IL1.POLICY_CODE,
		       C.OLD_CUSTOMER_ID AS CUSTOMER_NO, /*客户号*/
		       C.CUSTOMER_NAME AS Ins_Name, /*投保人姓名*/
		       (CASE
		         WHEN C.CUSTOMER_GENDER = '9' THEN
		          2
		         WHEN C.CUSTOMER_GENDER = '1' THEN
		          0
		         WHEN C.CUSTOMER_GENDER = '2' THEN
		          1
		         ELSE
		          C.CUSTOMER_GENDER
		       END) AS Ins_Sex, /*投保人性别*/
		       to_char(C.CUSTOMER_BIRTHDAY, 'yyyy-MM-dd') AS Ins_BIRTHDAY, /*投保人生日*/
		       C.CUSTOMER_CERT_TYPE AS Ins_ID_TYPE, /*投保人证件类型*/
		       C.CUSTOMER_CERTI_CODE AS Ins_ID_NO, /*投保人证件号码*/
		       to_char(C.CUST_CERT_STAR_DATE, 'yyyy-MM-dd') AS Ins_ID_EFFSTARTDATE, /*证件有效起期*/
		       to_char(C.CUST_CERT_END_DATE, 'yyyy-MM-dd') AS Ins_ID_EFFENDDATE, /*证件有效止期*/
		       AD.MOBILE_TEL AS MOBILE, /*移动电话*/
		       CASE
		         WHEN (SELECT COUNT(1)
		                 FROM DEV_PAS.T_BENEFIT_INSURED CBI
		                WHERE IL1.INSURED_ID = CBI.INSURED_ID
		                  AND IL1.POLICY_CODE = CBI.POLICY_CODE
		                  AND CBI.ORDER_ID = '1') > 0 THEN
		          'Y'
		         ELSE
		          'N'
		       END MAIN_INSURED, /*是否主被保险人*/
		       IL1.Relation_To_Ph, /*投被保人关系码值*/
		       (SELECT LR.RELATION_NAME
		          FROM DEV_PAS.T_LA_PH_RELA LR
		         WHERE LR.RELATION_CODE = IL1.RELATION_TO_PH) AS RELATION_NAME /*投被保人关系码值描述*/
		  FROM (SELECT IL.ADDRESS_ID,
		               IL.POLICY_CODE,
		               IL.CUSTOMER_ID,
		               BI.INSURED_ID,
		               IL.RELATION_TO_PH
		          From DEV_PAS.t_Benefit_Insured BI
		         INNER JOIN DEV_PAS.t_Insured_List IL
		            ON BI.POLICY_ID = IL.POLICY_ID
		           AND BI.INSURED_ID = IL.LIST_ID
		         WHERE IL.POLICY_CODE IN
		               (${policy_codes})
		         GROUP BY IL.ADDRESS_ID,
		                  IL.POLICY_CODE,
		                  IL.CUSTOMER_ID,
		                  BI.INSURED_ID,
		                  IL.RELATION_TO_PH) IL1
		 INNER JOIN DEV_PAS.T_CUSTOMER C
		    ON IL1.CUSTOMER_ID = C.CUSTOMER_ID
		 INNER JOIN DEV_PAS.T_ADDRESS AD
		    ON IL1.ADDRESS_ID = AD.ADDRESS_ID
		
	</select>
	<!-- 次标通知书列表查询 -->
	<select id="qry_csUwSubStandardDocumentQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT UM.UW_ID,/*核保ID*/
		       to_char(UM.UW_FINISH_TIME,'yyyy-MM-dd') AS UW_DATE,/*核保日期*/
		      U.REAL_NAME AS UW_USER,/*核保用户*/
		       UM.UW_SOURCE_TYPE,/*核保来源*/
		       UP.POLICY_DECISION,/*核保决定*/
		       UM.BIZ_CODE,/*核保业务ID*/
		       D.DOCUMENT_NO AS DOC_NO,/*通知书号*/
		       D.DOCUMENT_NAME AS DOC_TYPE_NAME,/*通知书名称*/      
		       UP.POLICY_CODE,/*保单号 */
		       C.CONTENT,/*通知书内容*/
		       UM.SERVICE_CODE AS SERVICE_NAME/*保全项目*/
		  FROM DEV_UW.T_UW_MASTER UM
		 INNER JOIN
		 (SELECT PE.UW_ID, PE.DOCUMENT_NO
              FROM DEV_UW.T_PENOTICE PE
            UNION ALL
            SELECT AI.UW_ID, AI.DOCUMENT_NO
              FROM DEV_UW.T_ASKFORINFO AI
            UNION ALL
            SELECT UN.UW_ID, UN.DOCUMENT_NO
              FROM DEV_UW.T_UW_NOTICE UN) T
		    ON UM.UW_ID = T.UW_ID
		 INNER JOIN DEV_UW.T_UW_POLICY UP
		    ON UM.UW_ID = UP.UW_ID
		 INNER JOIN DEV_NB.T_DOCUMENT D
		    ON T.DOCUMENT_NO = D.DOCUMENT_NO
		 INNER JOIN DEV_NB.T_CLOB C ON D.CLOB_ID=C.CLOB_ID
		 INNER JOIN DEV_NB.T_DOCUMENT_TEMPLATE DT
		    ON D.TEMPLATE_CODE = DT.TEMPLATE_CODE
		 INNER JOIN DEV_PAS.T_UDMP_USER U ON UM.UW_USER_ID=U.USER_ID
		 WHERE 1 = 1
		  AND UP.POLICY_CODE=#{policy_code} /*保单号 */
		  AND UM.UW_SOURCE_TYPE = '2' /*保全核保来源*/
		  AND UP.POLICY_DECISION IN ('10','40','50','20')/*保单层决定为标准体,延期,拒保,次标   */
		  ]]>
		 <if test="doc_no != null and doc_no != '' ">AND D.DOCUMENT_NO=#{doc_no}/*通知书号*/</if> 
	</select>
	<!-- 查询产品下的责任组信息Type -->
	<select id="qry_queryProductLifeInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
         	SELECT BI.ORDER_ID,
		       C.CUSTOMER_NAME,
		       C.OLD_CUSTOMER_ID,
		       BP.DECISION_CODE,
		       (SELECT A.PRODUCT_NAME FROM APP___UW__DBUSER.T_PRODUCT_LIFE A WHERE A.PRODUCT_ID = P.PRODUCT_ID) AS PRODUCT_NAME
				  FROM APP___UW__DBUSER.T_UW_PRODUCT       B,
				       APP___UW__DBUSER.T_CONTRACT_PRODUCT P,
				       APP___UW__DBUSER.T_BENEFIT_INSURED  BI,
				       APP___UW__DBUSER.T_INSURED_LIST     IL,
				       APP___PAS__DBUSER.T_CUSTOMER         C,
				       APP___UW__DBUSER.T_UW_BUSI_PROD     BP
				 WHERE B.ITEM_ID = P.ITEM_ID
				   AND B.UW_ID = P.UW_ID
				   AND BI.BUSI_ITEM_ID = B.BUSI_ITEM_ID
				   AND BI.UW_ID = B.UW_ID
				   AND IL.UW_ID = B.UW_ID
				   AND IL.POLICY_CODE = BI.POLICY_CODE
				   AND IL.LIST_ID = BI.INSURED_ID
				   AND C.CUSTOMER_ID = IL.CUSTOMER_ID
				   AND BP.UW_ID = B.UW_ID
				   AND BP.POLICY_CODE = BI.POLICY_CODE
				   AND BP.BUSI_ITEM_ID = B.BUSI_ITEM_ID
				   AND B.UW_ID =  #{uw_id} ]]>
				<if test="decision_code != null and decision_code != '' ">AND B.DECISION_CODE=#{decision_code}</if>
           	    <if test="busi_prod_code != null and busi_prod_code != '' ">AND B.BUSI_PROD_CODE LIKE CONCAT(#{busi_prod_code},'%')</if>
           	    <if test="policy_code != null and policy_code != '' ">AND BI.POLICY_CODE = #{policy_code}</if>
           	    <if test="busi_item_id != null and busi_item_id != '' ">AND B.BUSI_ITEM_ID = #{busi_item_id}</if>
	</select>
	
	<!-- 根据业务员号查询需要处理的保全项 -->
	<select id="qry_queryAcceptByAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.DOCUMENT_NO FROM DEV_NB.T_DOCUMENT T 
			WHERE (T.POLICY_CODE, T.BUSS_ID) IN
	       (SELECT TCA.POLICY_CODE, UM.UW_ID
	          FROM DEV_UW.T_CONTRACT_AGENT TCA
	         INNER JOIN DEV_UW.T_UW_MASTER UM
	            ON TCA.UW_ID = UM.UW_ID
	           AND UM.UW_SOURCE_TYPE = '2'
	           AND TCA.AGENT_CODE = #{agent_code}
	           AND UM.UW_USER_ID IS NOT NULL
	           AND UM.UW_STATUS_DETAIL NOT IN ('0302', '0202')
	           AND TRUNC(UM.UW_SUBMIT_TIME) > TRUNC(SYSDATE - 100)
	         INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
	            ON CAC.ACCEPT_CODE = UM.BIZ_CODE
	           AND CAC.ACCEPT_STATUS NOT IN
	               ('18', '19', '14', '15', '16', '17', '22', '23'))
	   AND T.BUSS_SOURCE_CODE = '002'
	   AND T.TEMPLATE_CODE IN
	       ('UWS_00001', 'UWS_00004', 'UWS_00003', 'UWS_00007')  ]]>
	</select>
	
	<select id="qry_queryUwMsgByUwIdAndPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT T2.*,
	       (CASE
	         WHEN T2.EXISTS_EXTRA_PREM >= 1 AND T2.DOC_STATUS = '1' THEN
	          (SELECT (CASE
	                    WHEN COUNT(*) >= 1 THEN
	                     MAX(PAR.FEE_STATUS) || ',' || MAX(PAR.FUNDS_RTN_CODE)
	                    ELSE
	                     NULL
	                  END)
	             FROM DEV_PAS.T_PREM_ARAP PAR
	            WHERE PAR.BUSINESS_CODE = t2.ACCEPT_CODE
	              AND PAR.POLICY_CODE = T2.POLICY_CODE)
	         ELSE
	          NULL
	       END) AS FEE_STATUS1,
	       (CASE
	         WHEN T2.EXISTS_EXTRA_PREM >= 1 AND T2.DOC_STATUS = '1' THEN
	          (SELECT (CASE
	                    WHEN COUNT(*) >= 1 THEN
	                     MAX(PAR.FEE_STATUS) || ',' || MAX(PAR.FUNDS_RTN_CODE)
	                    ELSE
	                     NULL
	                  END)
	             FROM DEV_CAP.T_PREM_ARAP PAR
	            WHERE PAR.BUSINESS_CODE = t2.ACCEPT_CODE
	              AND PAR.POLICY_CODE = T2.POLICY_CODE)
	         ELSE
	          NULL
	       END) AS FEE_STATUS
	  FROM (SELECT UP.POLICY_CODE, /*保单号*/
               UP.APPLY_CODE, /*投保单号*/
               (SELECT S.SERVICE_NAME
                  FROM DEV_PAS.T_SERVICE S
                 WHERE S.SERVICE_CODE = AC.SERVICE_CODE
                   AND ROWNUM = 1) AS SERVICE_NAME, /*保全项目名称*/
               D.DOCUMENT_NO AS DOC_NO, /*通知书号*/
               M.ORGAN_CODE AS MANDGE_COM_NAME, /**/
               UM.BIZ_CODE AS ACCEPT_CODE,
               CAC.APPLY_NAME AS APP_NAME, /*申请人姓名*/
               CAC.CUSTOMER_ID AS APP_CUST_NO /*申请人客户号*/,
               TO_CHAR(CAC.APPLY_TIME, 'YYYY-MM-DD') AS APPLY_DATE /*申请日期*/,
               (SELECT UU.REAL_NAME
                  FROM DEV_NB.T_UDMP_USER UU
                 WHERE UU.USER_ID = D.SEND_BY) AS UW_USER_NAME,
               TO_CHAR(D.CREATE_TIME, 'YYYY-MM-DD') AS UW_DATE_DOC,
               (CASE
                 WHEN ]]> #{option} <![CDATA[ = '1' THEN
                  (SELECT BANK_NAME
                     FROM DEV_PAS.T_BANK
                    WHERE BANK_CODE = M.SERVICE_BANK
                      AND ROWNUM = 1)
                 ELSE
                  ''
               END) AS BANK /*银行及储蓄所（代理专用）*/,
               G.AGENT_CODE, /*业务员姓名*/
               G.AGENT_NAME, /*业务员编号*/
               (SELECT A.SALES_ORGAN_NAME
                    FROM DEV_PAS.T_SALES_ORGAN A
                   WHERE A.SALES_ORGAN_CODE =
                         NVL(G.GROUP_CODE, G.SALES_ORGAN_CODE)
	                   AND ROWNUM = 1) AS BUSI_DEPARTMENT, /*业务分部及业务组*/
               (SELECT B.SALES_ORGAN_NAME
	                  FROM DEV_PAS.T_SALES_ORGAN A
	                 INNER JOIN DEV_PAS.T_SALES_ORGAN B
	                    ON A.PARENT_CODE = B.SALES_ORGAN_CODE
	                 WHERE A.SALES_ORGAN_CODE =
	                       NVL(G.GROUP_CODE, G.SALES_ORGAN_CODE)
	                   AND ROWNUM = 1) AS SALES_DEPARTMENT, /*业务分部及业务组*/ 
               (CASE
               	 WHEN UP.POLICY_DECISION = '50' AND
                      D.TEMPLATE_CODE = 'UWS_00007' AND
                      (SELECT COUNT(1)
                         FROM DEV_UW.T_UW_BUSI_PROD UBP
                        WHERE UBP.MASTER_BUSI_ITEM_ID IS NULL
                          AND UBP.UW_ID = D.BUSS_ID
                          AND UBP.APPLY_CODE = D.BUSS_CODE
                          AND UBP.DECISION_CODE = '40') > 0 THEN
                  '次标'
                 WHEN UP.POLICY_DECISION = '50' AND
                      D.TEMPLATE_CODE = 'UWS_00007' THEN
                  '整单拒保'
                 WHEN UP.POLICY_DECISION = '40' AND
                      D.TEMPLATE_CODE = 'UWS_00007' THEN
                  '整单延期'
                 WHEN UP.POLICY_DECISION = '20' AND
                      D.TEMPLATE_CODE = 'UWS_00007' THEN
                  '次标'
                 ELSE
                  DT.TEMPLATE_NAME
               END) AS DOC_TYPE_NAME, /*通知书类型名称*/
               DT.CARD_CODE AS DOC_TYPE, /*通知书类型码值*/
               D.DOCUMENT_NO AS CARD_CODE, /*条形码/单证号码*/
               D.DOC_LIST_ID, /*通知书主键序列号*/
               (CASE WHEN  D.Template_Code='UWS_00003' then (select TO_CHAR(WM_CONCAT(PEN.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' || T3.CUSTOMER_NAME)) from DEV_UW.T_PENOTICE PEN,DEV_PAS.T_CUSTOMER T3 where PEN.CUSTOMER_ID = T3.CUSTOMER_ID AND PEN.DOC_LIST_ID = D.DOC_LIST_ID ) 
					 WHEN  D.Template_Code='UWS_00004' then (select TO_CHAR(WM_CONCAT(PEN.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' || T3.CUSTOMER_NAME)) from dev_uw.t_askforinfo PEN,DEV_PAS.T_CUSTOMER T3 where PEN.CUSTOMER_ID = T3.CUSTOMER_ID AND PEN.DOC_LIST_ID = D.DOC_LIST_ID ) 
        	         WHEN  D.TEMPLATE_CODE='UWS_00001' THEN (SELECT TO_CHAR(WM_CONCAT(T2.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' || T3.CUSTOMER_NAME))  FROM DEV_UW.T_ASKFORINFO T1,DEV_UW.T_ASKFORINFO_DETAIL T2,DEV_PAS.T_CUSTOMER T3 WHERE T1.ASKFORINFO_ID = T2.ASKFORINFO_ID AND T2.CUSTOMER_ID = T3.CUSTOMER_ID AND T1.DOC_LIST_ID = D.DOC_LIST_ID)
                     WHEN  D.Template_Code='UN012'  then (select TO_CHAR(WM_CONCAT(PEN.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' ||T3.CUSTOMER_NAME)) from dev_uw.t_survival_investigation PEN,DEV_PAS.T_CUSTOMER T3 where PEN.CUSTOMER_ID = T3.CUSTOMER_ID AND PEN.DOC_LIST_ID = D.DOC_LIST_ID )  
                     WHEN  D.Template_Code IN('UWS_00007','UWS_00009') THEN (SELECT TO_CHAR(WM_CONCAT(PEN.ROLE_TYPE || '、' || T3.OLD_CUSTOMER_ID || '、' || T3.CUSTOMER_NAME)) FROM dev_uw.t_uw_document_verify PEN,DEV_PAS.T_CUSTOMER T3 WHERE  PEN.CUSTOMER_ID = T3.CUSTOMER_ID AND  PEN.DOCUMENT_NO =  D.DOCUMENT_NO)
                     ELSE ''
                     END   
                   ) AS SEND_OBJ_TYPE_INFO,/*接收对象信息*/
               (CASE
                 WHEN D.STATUS = '3' OR D.STATUS = '2' OR D.STATUS = '4' OR
                      D.STATUS = '5' THEN
                  '0'
                 WHEN D.STATUS = '7' THEN
                  '1'
                 WHEN D.STATUS = '9' THEN
                  '2'
                 ELSE
                  ''
               END) AS DOC_STATUS, /*通知书状态*/
               TO_CHAR(D.CREATE_TIME, 'YYYY-MM-DD') AS PRINT_DATE, /*打印日期*/
               (SELECT COUNT(1)
                  FROM DEV_UW.T_UW_EXTRA_PREM EP
                 WHERE EP.UW_ID = UP.UW_ID
                   AND EP.APPLY_CODE = UP.APPLY_CODE) AS EXISTS_EXTRA_PREM /*是否存在加费*/
          FROM DEV_NB.T_DOCUMENT D
         INNER JOIN DEV_UW.T_UW_MASTER UM
            ON D.BUSS_SOURCE_CODE = '002'
           	AND UM.UW_ID = D.BUSS_ID
            AND UM.UW_SOURCE_TYPE = '2'
            AND UM.UW_USER_ID IS NOT NULL
            AND UM.UW_STATUS_DETAIL NOT IN ('0302', '0202')
            AND D.TEMPLATE_CODE IN ('UWS_00001', 'UWS_00004', 'UWS_00003', 'UWS_00007', 'UWS_00009')
         INNER JOIN DEV_UW.T_CONTRACT_AGENT TCA
         ON TCA.POLICY_CODE = D.POLICY_CODE
           AND UM.UW_ID = TCA.UW_ID
           AND TCA.AGENT_CODE = #{agent_code}
         INNER JOIN DEV_UW.T_CONTRACT_MASTER M
            ON D.POLICY_CODE = M.POLICY_CODE
           AND UM.UW_ID = M.UW_ID
           AND M.DERIVATION = '2'
         INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
            ON D.POLICY_CODE = PC.POLICY_CODE
         INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
            ON PC.ACCEPT_ID = AC.ACCEPT_ID
           AND AC.ACCEPT_CODE = UM.BIZ_CODE
             AND AC.ACCEPT_STATUS NOT IN ('18', '19', '14', '15', '16', '17', '22', '23')
         INNER JOIN DEV_PAS.T_CS_APPLICATION CAC
            ON AC.CHANGE_ID = CAC.CHANGE_ID
           and PC.CHANGE_ID = AC.CHANGE_ID
         INNER JOIN DEV_UW.T_UW_POLICY UP
            ON D.BUSS_ID = UP.UW_ID
           AND UP.POLICY_CODE = D.POLICY_CODE
         INNER JOIN DEV_NB.T_DOCUMENT_TEMPLATE DT
            ON D.TEMPLATE_CODE = DT.TEMPLATE_CODE
         INNER JOIN DEV_PAS.T_AGENT G
            ON TCA.AGENT_CODE = G.AGENT_CODE) T2 ]]>
	</select>
	
	<select id="qry_queryAgentByAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.AGENT_CHANNEL,
		        (SELECT A.SALES_ORGAN_NAME
		           FROM DEV_PAS.T_SALES_ORGAN A
		          WHERE A.SALES_ORGAN_CODE = NVL(T.GROUP_CODE, T.SALES_ORGAN_CODE)
		            AND ROWNUM = 1) AS BUSI_DEPARTMENT, /*业务分部及业务组*/
		        (SELECT B.SALES_ORGAN_NAME
		           FROM DEV_PAS.T_SALES_ORGAN A
		          INNER JOIN DEV_PAS.T_SALES_ORGAN B
		             ON A.PARENT_CODE = B.SALES_ORGAN_CODE
		          WHERE A.SALES_ORGAN_CODE = NVL(T.GROUP_CODE, T.SALES_ORGAN_CODE)
		            AND ROWNUM = 1) AS SALES_DEPARTMENT, /*业务分部及业务组*/
		        T.AGENT_NAME
		   FROM DEV_PAS.T_AGENT T
		  WHERE T.AGENT_CODE = #{agent_code} ]]>
	</select>
	
	<!-- 根据核保ID保单号查询被保人信息 -->
	<select id="QRY_queryInsuredInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TIL.CUSTOMER_ID, TIL.CUSTOMER_NAME, TO_CHAR(TBI.ORDER_ID) AS MAIN_INSURED, CBP.DECISION_CODE
 			 FROM DEV_UW.T_INSURED_LIST TIL
 			 LEFT JOIN DEV_UW.T_BENEFIT_INSURED TBI
 			   ON TIL.UW_ID = TBI.UW_ID
 			 JOIN DEV_UW.T_CONTRACT_BUSI_PROD CBP
  			  ON TIL.UW_ID = CBP.UW_ID
  			 AND TIL.POLICY_CODE = CBP.POLICY_CODE
 			WHERE 1 = 1 ]]>
		 <if test="uw_id != null and uw_id != '' ">AND  TIL.UW_ID = #{uw_id}/*核保ID*/</if> 
		 <if test="policy_code != null and policy_code != '' ">AND  TIL.POLICY_CODE = #{policy_code}/*保单号*/</if> 
	</select>
	
	<!-- 根据uw_id,policy_code查询投保人信息  -->
	<select id="qry_queryPolicyHolderInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 SELECT C.OLD_CUSTOMER_ID, C.CUSTOMER_NAME
		   FROM APP___UW__DBUSER.T_POLICY_HOLDER PH, APP___NB__DBUSER.T_CUSTOMER C
		  WHERE PH.CUSTOMER_ID = C.CUSTOMER_ID
		]]>
		<if test="uw_id != null and uw_id != '' ">AND PH.UW_ID = #{uw_id}/*核保ID*/</if> 
		<if test="policy_code != null and policy_code != '' ">AND PH.POLICY_CODE = #{policy_code}/*保单号*/</if> 
	</select>
	
</mapper>