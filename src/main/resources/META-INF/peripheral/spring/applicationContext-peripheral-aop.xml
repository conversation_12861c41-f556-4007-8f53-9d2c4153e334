<?xml version="1.0" encoding="UTF-8"?><beans xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c" xmlns:cache="http://www.springframework.org/schema/cache" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/task    http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans    http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch    http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache    http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx    http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context    http://www.springframework.org/schema/context/spring-context-3.0.xsd   http://www.springframework.org/schema/aop    http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

	<!-- 集成接口新老客户号转换Aop配置 -->
	<aop:config>
	  <!-- 105——927 删除按照身份证号查询的UCC -->
		<!-- 定义一个切入点表达式： 拦截哪些方法 -->
		<aop:pointcut id="peripheral_pt_qry"
			expression="
				   execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101000045.impl.QueryClaimListInfoUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101001334.impl.PolicyQueryInfoByAppOrPolicyCodeUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101002675.impl.QueryContBusiProdUcc.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101002691.impl.LpPremItemUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r06401001837.impl.PaDetailQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r06401001886.impl.PAPreAuditInfoQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r06601001898.impl.UwPresentPhysicalQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r06701001914.impl.ClaimQueryUwFeeInfoUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r06801001921.impl.PhysicalResultQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r06501001902.impl.ResumeQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101002683.impl.JointInsurerInfUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101002709.impl.ContInsuredInfoQueryForRIUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101002633.impl.CustomerPolicyInfoUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101002447.impl.LinFenQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r06401001871.impl.ZBBenefitInfoQueryUccImpl.*(..))
				|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101002246.impl.TelephoneCenterQueryPolicyInfoImpl.*(..))
        " />

		<!-- 切面 -->
		<aop:aspect ref="PA_outter_customer_aop">
			<!-- 前置通知： 在目标方法调用前执行 -->
			<aop:before method="beginMethod" pointcut-ref="peripheral_pt_qry" />
			<!-- 返回后通知 -->
			<aop:after-returning method="afterReturning"
				pointcut-ref="peripheral_pt_qry" arg-names="joinPoint,returning"
				returning="returning" />
		</aop:aspect>
	</aop:config>
     <aop:config>
      <!-- 105——927 按照身份证号查询的UCC通过综合查询数据源转换新老核心号码 -->
    <!-- 定义一个切入点表达式： 拦截哪些方法 -->
        <aop:pointcut id="customer_no_change_pt_qry"
            expression="execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101001336.impl.PolicyQueryInfoByIdNoUccImpl.*(..)) 
        			|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r13501900400.impl.PolicyPrintQueryUCCImpl.*(..))
        			|| execution(public * com.nci.tunan.qry.impl.peripheral.ucc.r00101000061.impl.CusContInfoQueryUccImpl.*(..))
        "/>

        <!-- 切面 -->
        <aop:aspect ref="QRY_outter_customer_aop">
            <!-- 前置通知： 在目标方法调用前执行 -->
            <aop:before method="beginMethod" pointcut-ref="customer_no_change_pt_qry" />
            <!-- 返回后通知 -->
            <aop:after-returning method="afterReturning"
                pointcut-ref="customer_no_change_pt_qry" arg-names="joinPoint,returning"
                returning="returning" />
        </aop:aspect>
    </aop:config>
    
</beans>