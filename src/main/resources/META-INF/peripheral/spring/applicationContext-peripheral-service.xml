<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">
	
	<!--体检资料查询-->
	<bean id="nbPhysicalEtionInformationService" class="com.nci.tunan.qry.impl.peripheral.service.r06501001906.impl.NbPhysicalEtionInformationServiceImpl">
		<property name="customerDao" ref="customerDao"></property>
		<property name="policyHolderDao" ref="policyHolderDao"></property>
		<property name="insuredListDao" ref="insuredListDao"></property>
		<property name="contractMasterDao" ref="contractMasterDao"></property>
	</bean> 
	<!--已承保保单查询-->
	<bean id="paGuaranteeQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06401001858.impl.PaGuaranteeQueryServiceImpl">
		<property name="policyHolderDao" ref="policyHolderDao"></property>
		<property name="contractMasterDao" ref="contractMasterDao"></property>
		<property name="paQuestionaireCustomerDao" ref="paQuestionaireCustomerDao"></property>
		<property name="uwPenoticeDao" ref="uwPenoticeDao"></property>
		<property name="contractBusiProdDao" ref="contractBusiProdDao"></property>
		<property name="contractProductDao" ref="contractProductDao"></property>
		<property name="nbDocumentDao" ref="nbDocumentDao"></property>
		<property name="insuredListDao" ref="insuredListDao"></property>
	</bean> 
	<!--影像资料查询-->
	 <bean id="videoInfoQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06401001839.impl.PaVideoInfoQueryServiceImpl">	
	 
		<property name="paContractMasterDao" ref="paContractMasterDao"></property>
	 </bean>	
	<!--影像资料下载-->
	 <bean id="videoInfoDownService" class="com.nci.tunan.qry.impl.peripheral.service.r06401001840.impl.PaVideoInfoDownServiceImpl">	
	 
		<property name="paContractMasterDao" ref="paContractMasterDao"></property>
	 </bean>

	 
	 <!-- 再保险 操作履历下  初审详细信息查询-->
	 <bean id="preAuditInfoQueryService"  
		class="com.nci.tunan.qry.impl.peripheral.service.r06401001886.impl.PAPreAuditInfoQueryServiceImpl">
		<property name="paPreAuditMasterDao" ref="paPreAuditMasterDao"/>
		<property name="paPolicyHolderDao" ref="paNBPolicyHolderDao"/>
		<property name="paPreAuditProductDao" ref="paPreAuditProductDao"/>
		<property name="paPreAuditCauseDao" ref="paPreAuditCauseDao"/>
	</bean>
	

	<!--add by yangjx 理赔核保照会下加费应收信息查询  -->
	<bean id="claimQueryUwFeeInfoService" class="com.nci.tunan.qry.impl.peripheral.service.r06701001914.impl.ClaimQueryUwFeeInfoServiceImpl">
		<property name="claimCompDao" ref="claimCompDao"></property>
	</bean>

	<!--by zhaoyoan_wb 万能险账户当前信息查询  -->
	<bean id="universalAccountInfoQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r00101000031.impl.UniversalAccountInfoQueryServiceImpl">
		<property name="paContractMasterDao" ref="paContractMasterDao"></property>
		<property name="paContractInvestDao" ref="paContractInvestDao"></property>
		<property name="paFundSettlementDao" ref="paFundSettlementDao"></property>
		<property name="paFundTransDao" ref="paFundTransDao"></property>
		<property name="paPolicyHolderDao" ref="paPolicyHolderDao"></property>
		<property name="paPremArapDao" ref="paPremArapDao"></property>
	</bean>
	
	<!-- 保单明细下险种信息（新核心） -->
	<bean id="perContractBusiProdInfoService" class="com.nci.tunan.qry.impl.peripheral.service.r06401001810.impl.PERContractBusiProdInfoServiceImpl">
		<property name="perContractBusiProdCompDao" ref="perContractBusiProdCompDao"></property>
	</bean>
	
	<!--by chenaq 操作履历下核保通知书查询 -->
	<bean id="recordDocumentQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06501001904.impl.NbRecordDocumentQueryServiceImpl">
		<property name="recordDocumentDao" ref="recordDocumentDao"></property>
	</bean>
	
	<!--by chenaq 保全核保照会下体检结果查询 -->
	<bean id="presentPhysicalQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06601001898.impl.UwPresentPhysicalQueryServiceImpl">
		<property name="uwPenoticeResultDao" ref="uwPenoticeResultDao"></property>
		<property name="acceptChangeDao" ref="PA_csAcceptChangeDao"></property>
	</bean>
	
	<!--操作履历查询  -->
	<bean id="OperatingHistoryQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06601001888.impl.OperatingHistoryQueryServiceImpl">
		<property name="autoUwDao" ref="autoUwDao"></property>
		<property name="uwMasterDao" ref="uwMasterDao"></property>
		<property name="uWPenoticeDao" ref="uWPenoticeDao"></property>
		<property name="uwPolicyReviewDao" ref="uwPolicyReviewDao"></property>
		<property name="uWRiAppPolicyDao" ref="uWRiAppPolicyDao"></property>
		<property name="uWSurvivalInvestigationDetaiDao" ref="uWSurvivalInvestigationDetaiDao"></property>
		<property name="uwNoteDao" ref="uwNoteDao"></property>
		<property name="nbDocumentDao" ref="nbDocumentDao"></property>
		<property name="uwRuleResultDao" ref="uwRuleResultDao"></property>
		<property name="nbPreAuditMasterDao" ref="nbPreAuditMasterDao"></property>
		<property name="nbProposalProcessDao" ref="nbProposalProcessDao"></property>
		<property name="pAContractMasterDao" ref="paContractMasterDao"></property>
	</bean>
	
	<!-- 保单明细查询 -->
	<bean id="iPaDetailQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06401001837.impl.PaDetailQueryServiceImpl">
	<property name="paContractMasterDao" ref="paContractMasterDao"></property>
	 <property name="contractAgentDao" ref="NB_contractAgentDAO"></property>
	 <property name="iAgentDao" ref="iAgentDao"></property>
	 <property name="insureQueryDao" ref="insureQueryDao"></property>
	 <property name="iQryBankDao" ref="iQryBankDao"></property>
	 <property name="iriskInfoDao" ref="iriskInfoDao"></property>
	 <property name="appentsDao" ref="appentsDao"></property>
	 <property name="iQuestionCall" ref="iQuestionCall"></property>
	 <property name="insuresdDao" ref="insuresdDao"></property>
	 <property name="InsuredInfosDao" ref="InsuredInfosDao"></property>
	 <property name="iquryContsDao" ref="iquryContsDao"></property>
	</bean>
	
	<!-- 保单接收查询 -->
	<bean id="policyReceiveService" class="com.nci.tunan.qry.impl.peripheral.service.r00101000871.impl.PolicyReceiveServiceImpl">
		<property name="iPolicyReceiveDao" ref="iPolicyReceiveDao"></property> 
	</bean>
		<!-- 保单红利查询 -->
	<bean id="policyBonusService" class="com.nci.tunan.qry.impl.peripheral.service.r00101000869.impl.PolicyBonusServiceImpl">
		<property name="policyBonusDao" ref="policyBonusDao"></property>
	</bean>
	
	<!-- 订单查询接口 -->
	<bean id="orderInfoQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r00101000749.impl.OrderInfoQueryServiceImpl"> 
		<property name="orderServiceDao" ref="orderServiceDaoImpl"></property>
	</bean>
	
	<!-- 保全体检结果查询 -->
	<bean id="csMedicalResultQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06801001814.impl.CsMedicalResultQueryServiceImpl">
		<property name="nbDocumentDao" ref="nbDocumentDao"></property>
	</bean>
	<!-- 核保通知书 -->
	
	<bean id="iQueryUwNoticeService" class="com.nci.tunan.qry.impl.peripheral.service.r00101000885.impl.QueryUwNoticeServiceImpl">
	
	<property name="iQueryUwNoticeDao" ref="iQueryUwNoticeDao"></property>
	<property name="paPremArapDao" ref="paPremArapDao"></property>
		<property name="uwPolicyDao" ref="UW_uwPolicyDao"></property>
	</bean>
	
	<!-- 体检结果查询 -->
	<bean id="physicalResultQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06801001921.impl.PhysicalResultQueryServiceImpl">
	 <property name="physicalResultQueryDao" ref="physicalResultQueryDao"></property>
	</bean>
	<!-- 南京医保交费信息查询 -->
	<bean id="medicalInsuranceFeeInfoQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r00101002257.impl.NJMedicalInsuranceFeeInfoQueryServiceImpl">
		<property name="servicePolicyListDao" ref="servicePolicyListDao"></property>
	</bean>
	<!-- 产品是否可续投保 -->
	<bean id="iPolicyRenewQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r00101001019.impl.PolicyRenewQueryServiceImpl">
	<property name="iProductRenewDao" ref="iProductRenewDao"></property>
	</bean>
	
	<!-- 临分查询接口 -->
	<bean id="LinFenQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r00101002447.impl.LinFenQueryServiceImpl">
		<property name="contractMasterDao" ref="paContractMasterDao"></property>
		<property name="contractAgentDao" ref="NB_contractAgentDAO"></property>
		<property name="appProductDao" ref="riAppProductDao"></property>
		<property name="policyHolderDao" ref="paPolicyHolderDao"></property>
		<property name="customerDao" ref="customerDao"></property>
		<property name="insuredInfosDao" ref="InsuredInfosDao"></property>
		<property name="nbContractMasterDao" ref="nbContractMasterDAOImpl"></property>
	</bean>
	
	<!-- 临分计算数据接口 -->
	<bean id="linfenCalcDataInfomationService" class="com.nci.tunan.qry.impl.peripheral.service.r06401002582.impl.LinfenCalcDataInfomationServiceImpl">
		<property name="nbContractMasterDao" ref="nbContractMasterDAOImpl"></property>
		<property name="paContractMasterDao" ref="paContractMasterDao"></property>
	</bean>
	
	<!-- 临分分页计算接口 -->
	<bean id="linFenPageQueryDataService" class="com.nci.tunan.qry.impl.peripheral.service.r06401002583.impl.LinFenPageQueryDataServiceImpl">
		<property name="appProductDao" ref="riAppProductDao"></property>
		<property name="ipaContractMasterDao" ref="paContractMasterDao"></property>
	</bean>
	
	<!--保单查询接口-按投保单号或保单号查询 -->
	<bean id="policyQueryInfoByAppOrPolicyCodeService" class="com.nci.tunan.qry.impl.peripheral.service.r00101001334.impl.PolicyQueryInfoByAppOrPolicyCodeServiceImpl">
	   <property name="qryContractMasterDao" ref="contractMasterDao"></property>
	   <property name="uwPolicyDao" ref="UW_uwPolicyDao"></property>
	  <property name="nbJointlyInsuredPolicyDao" ref="NB_jointlyInsuredPolicyDao"/>
	    <property name="nbContractMasterDao" ref="nbContractMasterDAOImpl"/>
	 <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
	</bean>
	
	<!--保单查询接口-按身份证号查询-->
	<bean id="policyQueryInfoByIdNoService" class="com.nci.tunan.qry.impl.peripheral.service.r00101001336.impl.PolicyQueryInfoByIdNoServiceImpl">
	   <property name="qryContractMasterDao" ref="contractMasterDao"></property>
	   <property name="nbContractMasterDao" ref="nbContractMasterDAOImpl"/>
	    <property name="nbJointlyInsuredPolicyDao" ref="NB_jointlyInsuredPolicyDao"/>
	     <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
	
	</bean>
	
	<!-- 追加保费查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101000988.impl.AddPremQueryServiceImpl" id="PA_addPremQueryService">
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"/>
	</bean>
	
	<!-- 工行纸质保单补充接口 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101000984.impl.PaperSupplementServiceImpl" id="QRY_NB_paperSupplementService">
		<property name="appProductDao" ref="riAppProductDao"/>
		<property name="nbContractMasterDao" ref="nbContractMasterDAOImpl"/>
		<property name="nbContractProductDAO" ref="NB_nbContractProductDAO"/>
	</bean>
	
	<!-- 网站E化单证防伪验证码验真接口-->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101001332.impl.WebEDocumentPseudoVerifiServiceImpl" id="QRY_webEDocumentPseudoVerifiService">
		<property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
		<property name="customerDao" ref="commonCustomerDao"/>
	</bean>
	<!-- 网站法人E化单证验真接口-->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101001380.impl.WebCorpEDocumentVerifiServiceImpl" id="QRY_webCorpEDocumentVerifiService">
		<property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
		<property name="customerDao" ref="commonCustomerDao"/>
		<property name="documentQueryDao" ref="documentQueryDao"/>
	</bean>
	
	<!-- 保费项信息接口 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101002693.impl.QueryPremiumsInfoServiceImpl" id="QRY_queryPremiumsInfoService">
		<property name="qryContractProductDao" ref="contractProductDao"/>
	</bean>
	
	<!-- 保单险种表信息接口 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101002675.impl.QueryContBusiProdService" id="QRY_queryContBusiProdService">
		<property name="qryContractBusiProdDao" ref="contractBusiProdDao"/>
		<property name="contractProductOtherDao" ref="contractProductOtherDao"/>  
	</bean>
	
	<!-- 险种责任领取项接口  -->
	<bean id="QRY_lpPremItemService" class="com.nci.tunan.qry.impl.peripheral.service.r00101002691.impl.LpPremItemServiceImpl" >
	    <property name="qryContractBusiProdDao" ref="QRY_contractBusiProdDao"/>
		<property name="contractProductDao" ref="QRY_contractProductDao"/>
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="paPayPlanDao" ref="QRY_payPlanDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="insuredListDao" ref="QRY_insuredListDao"/>
		<property name="customerDao" ref="QRY_customerDao"/>
		<property name="beneDao" ref="QRY_contractBeneDao"/>
		<property name="iprdService" ref="QRY_iprdDao"/>
	</bean>
	
	<!-- 操作履历下核保查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06501001902.impl.ResumeQueryServiceImpl" id="NB_resumeQueryService">
		<property name="nbContractMasterDao" ref="nbContractMasterDAOImpl"/>
		<property name="contractAgentDAO" ref="NB_contractAgentDAO"/>
		<property name="nbInsuredListDao" ref="NB_nbInsuredListDAO"/>
		<property name="nbBenefitInsuredDao" ref="NB_nbBenefitInsuredDAO"/>
		<property name="nbContractProductDao" ref="NB_nbContractProductDAO"/>
		<property name="iPAContractMasterDao" ref="paContractMasterDao"></property>
		</bean>

	<!-- 以下为移动接口 -->
	<!-- R06601001889 -->
    <bean class="com.nci.tunan.qry.impl.peripheral.service.r06601001889.impl.QueryUWHistoryServiceImpl" id="UW_iQueryUWHistoryService">
    	<property name="uwPolicyDao" ref="UW_uwPolicyDao"/>
    </bean>
    
    <bean class="com.nci.tunan.qry.impl.peripheral.service.r06601001890.impl.QueryBusiProdUWDecisionServiceImpl" id="UW_iQueryBusiProdUWDecisionService">
    	<property name="busiProdListDao" ref="UW_uWBusiProdListDao"/>
    </bean>
    	<!-- 操作履历下初审基础信息查询-->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06501001905.impl.ResumeFirstBaseQueryServiceImpl" id="NB_resumeFirstBaseQueryService">
		<property name="preAuditMasterDao" ref="NB_preAuditMasterDao"/>
		<property name="nbPolicyHolderDAO" ref="NB_nbPolicyHolderDAO"/>
		<property name="preAuditProductDAO" ref="NB_preAuditProductDAO"/>
		<property name="contractMasterDao" ref="nbContractMasterDAOImpl"/>
	</bean>
	<!--特约查询-->
	 <bean class="com.nci.tunan.qry.impl.peripheral.service.r06401001861.impl.QueryPolicyConditionServiceImpl" id="PA_queryPolicyConditionService">	
		<property name="policyConditionDao" ref="PA_policyConditionDao"/>
	</bean>

	<!-- 再保险 受益人查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06401001871.impl.ZBBenefitInfoQueryServiceImpl" id="PA_zbBenefitInfoQueryService">
		<property name="policyHolderDao" ref="paPolicyHolderDao"/>
		<property name="contractBeneDao" ref="PA_contractBeneDao"/>
	</bean>
<!-- 险种责任接口 -->
	<bean id="PA_busiItemDutyService" class="com.nci.tunan.qry.impl.peripheral.service.r00101002681.impl.BusiItemDutyServiceImpl" >
		<property name="contractProductDao" ref="iContractProductDao"/>
	</bean>
    <!--by zhaoyoan_wb 操作履历下再保回复查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06601001891.impl.ReinsuranceReplyQueryServiceImpl" id="UW_reinsuranceReplyQueryService">
		<property name="contractMasterDao" ref="UW_contractMasterDao"/>
		<property name="riAppPolicyDao" ref="UW_riAppPolicyDao"/>
		<property name="riAppProductDao" ref="UW_riAppProductDao"/>
	</bean>
	
	<!--操作履历下客户合并通知书查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06601001892.impl.NbCustomerMergeNoticeQueryServiceImpl" id="NB_nbCustomerMergeNoticeQueryService">
	</bean>
    <!-- 个单被保人信息接口 -->
	<bean id="PA_contInsuredInfoQueryForRIService" class="com.nci.tunan.qry.impl.peripheral.service.r00101002709.impl.ContInsuredInfoQueryForRIServiceImpl" >
	    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	    <property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	</bean>

	<!--by zhaoyoan_wb 操作履历下复议答复查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06601001893.impl.ReviewReplyQueryServiceImpl" id="UW_reviewReplyQueryService">
		<property name="uwPolicyReviewDao" ref="uwPolicyReviewDao"/>
	</bean>
	<!--by zhaoyoan_wb 操作履历下自核提示查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06601001895.impl.AutoUWPointQueryServiceImpl" id="UW_autoUWPointQueryService">
		<property name="uwRuleResultDao" ref="uwRuleResultDao"/>
	</bean>	<!-- 连带被保险人信息接口 -->
	<bean id="PA_jointInsurerInfService" class="com.nci.tunan.qry.impl.peripheral.service.r00101002683.impl.JointInsurerInfServiceImpl" >
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	
	<!--by zhaoyoan_wb 操作履历下无需体检生调原因查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06601001896.impl.NoPhysicalReasonQueryServiceImpl" id="UW_noPhysicalReasonQueryService">
		<property name="ruleResultDao" ref="QRY_ruleResultDao"/>
	</bean>

	<!-- 操作履历下问题件查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06501001903.impl.ProblemPiecesQueryServiceImpl" id="NB_problemPiecesQueryService">
		<property name="appProductDao" ref="riAppProductDao"></property>
	</bean>
	<!-- 再保险  操作履历下记事本查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06401001853.impl.NoteInfoQueryServiceImpl" id="PA_noteInfoQueryService">
	<property name="csNoteDao" ref="PA_csNoteDao"/>
	<property name="recordDocumentDao" ref="recordDocumentDao"></property>
	</bean>
	
	<!-- R06601001897 合同核保轨迹查询 -->
    <bean class="com.nci.tunan.qry.impl.peripheral.service.r06601001897.impl.ContUwTraceServiceImpl" id="UW_contUwTraceQueryService">
        <property name="uwPolicyDao" ref="UW_uwPolicyDao"/>
    </bean>
	<!--操作履历下生调结果查询（新核心）-->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06401001842.impl.SurveyResultQueryServiceImpl" id="PA_surveyResultQueryService">	
		<property name="surveyApplyDao" ref="qrySurveyApplyDao"/>
	</bean>
	
	<!--网站在线自助报案客户校验接口 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101002191.impl.WebOnlineSelfServiceReportCheckServiceImpl" id="CLM_webOnlineSelfServiceReportCheckService">
	<property name="claimCaseDao" ref="CLM_claimCaseDao"/>
	
	<property name="insuredListDao" ref="QRY_insuredListDao"></property>
	<property name="contractProductDao" ref= "QRY_contractProductDao"></property>

	</bean>
	
	<!-- 保全核保照会查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r06801001924.impl.CsUWconfirmQueryServiceImpl" id="PA_CsUWconfirmQueryService">
		<property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
		<property name="uwMasterDao" ref="uwMasterDao"></property>
		<property name="documentDao" ref="Css_documentDao"></property>
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao"></property>
		<property name="csUWconfirmQueryDao" ref="PA_csUWconfirmQueryDao"/>	
	</bean>
	
	<!-- 出险人保单查询接口Service -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101000061.impl.CusContInfoQueryServiceImpl" id="PA_cusContInfoQueryService">
		<property name="insuredListDao" ref="QRY_insuredListDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
	</bean>
	
		<!-- 核心保单验真接口service -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101001688.impl.PolicyDocumentPseudoVerifiServiceImpl" id="PA_policyDocumentPseudoVerifiService">
	   <property name="csAcceptChangeDao" ref="PA_csAcceptChangeDao"/>
		<property name="customerDao" ref="qryCustomerDao"/>
	</bean>

	<!-- 电话中心保单信息查询  -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101002246.impl.TelephoneCenterQueryPolicyInfoServiceImpl" id="PA_telephoneCenterQueryPolicyInfoService">
		<property name="contractMasterDao" ref="PA_contractMasterDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="contractProductDao" ref="QRY_contractProductDao"/>
		<property name="businessProductDao" ref="PA_businessProductDao"/>
		<property name="policyHolderDao" ref="paPolicyHolderDao"/>
		<property name="customerDao" ref="qryCustomerDao"/>
		<property name="insuredListDao" ref="QRY_insuredListDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
		<property name="policyAccountDao" ref="PA_policyAccountDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="agentDao" ref="PA_agentDao"/>
	</bean>
	
	<!-- 客户保单查询  -->
	<bean id="PA_customerPolicyInfoService" class="com.nci.tunan.qry.impl.peripheral.service.r00101002633.impl.CustomerPolicyInfoServiceImpl" >
		<property name="policyHolderDao" ref="paPolicyHolderDao"/>
		<property name="contractMasterDao" ref="paContractMasterDao"/>
		<property name="customerDao" ref="qryCustomerDao"/>
		<property name="insuredListDao" ref="QRY_insuredListDao"/>
		<property name="contractExtendDao" ref="PA_contractExtendDao"/>
		<property name="payerAccountDao" ref="payerAccountDao"/>
		<property name="contractAgentDao" ref="NB_contractAgentDAO"/>
		<property name="bankDao" ref="bankDao"/>
		<property name="agentDao" ref="PA_agentDao"/>
		<property name="businessProductDao" ref="businessProductDao"/>
		<property name="contractProductDao" ref="QRY_contractProductDao"/>
		<property name="productLifeDao" ref="PA_productLifeDao"/>
		<property name="contractBusiProdDao" ref="PA_contractBusiProdDao"/>
		<property name="benefitInsuredDao" ref="PA_benefitInsuredDao"/>
	</bean>
	
	<!-- 老客户校验service -->
	<!-- 健康无忧（尊享版）老客户校验接口-->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101002797.impl.OldCustomerVerifyServiceImpl" id="PA_oldCustomerVerifyService">
        <property name="contractProductDao" ref="QRY_contractProductDao"></property>	
      <!-- 需求变更#48814 关于调整841健康无忧（宜家版）老客户规则及最低起售金额的需求-->
        <property name="productRuleConfigInfoDao" ref="QRY_productRuleConfigInfoDao"></property>
	</bean>
	
	<!-- 理赔列表信息查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r00101000045.impl.QueryClaimListInfoServiceImpl" id="QRY_iQueryClaimListInfoService">
		<property name="iClaimForMobileDao" ref="QRY_iClaimForMobileDao"/>
	</bean>
	
	<!-- 订单号快速查询-->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.ordernoquery.impl.OrderNoQueryServiceImpl" id="PA_orderNoQueryService">
		<property name="orderServiceDao" ref="orderServiceDaoImpl"/>
	</bean>
		
	<!-- 加费通知单号快速查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.judgeaddpremdocnoexist.impl.AddPremDocNoQuickSearchServiceImpl" id="QRY_addPremDocNoQuickSearchService">
		<property name="documentDao" ref="QRY_integratedDao"/>
	</bean>
	
	<!-- 交退费编号快速查询是否存在服务  -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.judgeunitnumberexist.impl.JudgeUnitNumberExistServiceImpl" id="QRY_judgeUnitNumberExistService">
	    <property name="integratedDao" ref="QRY_integratedDao"/>
	</bean>
	
	<!-- 投保单号快速查询是否存在服务 -->
	<bean class="com.nci.tunan.qry.impl.judgeapplycodeexit.service.impl.JudgeApplyCodeExitServiceImpl" id="NB_judgeApplyCodeExitService">
	    <property name="nbContractMasterDao" ref="nbContractMasterDAOImpl"/>
	</bean>
	
	<!-- 提调号快查服务 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.judgesecuritynoexist.impl.JudgeSecurityNoExistServiceImpl" id="QRY_securityNoExistService">		
        <property name="integratedDao" ref="QRY_integratedDao"/>
	</bean>	
	
	<!-- ESB服务 判断保单号是否存在 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.judgepolicyexist.impl.JudgePolicyExistServiceImpl" id="QRY_judgePolicyExistService">
	    <property name="contractMasterDao" ref="QRY_integratedDao"/>	
	</bean>
	
	<!-- 保全受理号码快速查询是否存在服务 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.cspolicyaccept.impl.CsPolicyAcceptIsExistedServiceImpl" id="QRY_csPolicyAcceptIsExistedService">
		<property name="csAcceptChangeDao" ref="QRY_integratedDao"/>
	</bean>

				<!-- 赔案号快速查询是否存在服务 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.casenoisexistserv.impl.CasenoIsExistServServiceImpl" id="CLM_casenoIsExistServService">
		<property name="claimCaseDao" ref="CLM_claimCaseDao"/>
	</bean>
	
	    <!-- 核保通知书号快速查询接口 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.judgeprtseqexist.impl.JudgePrtSeqExistServiceImpl" id="QRY_judgePrtSeqExistService">
	 <property name="integratedDao" ref="QRY_integratedDao"/>
	</bean>
	
		  <!-- 保全批单号快查 -->
	<bean id="PA_csEndorseCodeIsExistedService" class="com.nci.tunan.qry.impl.peripheral.service.csedorsecode.impl.CsEndorseCodeIsExistedServiceImpl">
		<property name="csPolicyChangeDao" ref="PA_csPolicyChangeDao" />
	</bean>
	
	<!-- 柜面自助终端  红利信息查询(打印通知书类) -->
	<bean id="PA_IBonusInfoQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r06401003161.impl.BonusInfoQueryServiceImpl">
    	<property name="documentDao" ref="QRY_documentDao"/>
	</bean>
	
	<!-- 催缴通知书快速查询接口 -->
	<bean id="QRY_ICallDocNoQuickSearchService" class="com.nci.tunan.qry.impl.peripheral.service.calldocumentnoexist.impl.CallDocNoQuickSearchServiceImpl">
 		<property name="documentDao" ref="QRY_integratedDao"/>
	</bean>
	
	<!-- 补费通知书快速查询接口 -->
	<bean id="QRY_IDocNoQuickSearchService" class="com.nci.tunan.qry.impl.peripheral.service.documentnoexist.impl.DocNoQuickSearchServiceImpl">
 		<property name="documentDao" ref="QRY_integratedDao"/>
	</bean>
	
	<!--是否生成保单打印日期查询接口  -->
	<bean class="com.nci.tunan.qry.impl.peripheral.service.r13501003276.impl.QueryProposalPrintDateFlagServiceImpl" id="QRY_queryProposalPrintDateFlagService">
		<property name="queryProposalPrintDateFlagDao" ref="QRY_queryProposalPrintDateFlagDao"/>
	</bean>
	
	<!--中保信保单编码路由快查灾备  -->
    <bean class="com.nci.tunan.qry.impl.peripheral.service.judgePolicySequenceNo.impl.PolicySequenceNoQueryServiceImpl" id="QRY_PolicySequenceNoQueryServiceImpl">
        <property name="qryInsuredListDao" ref="insuredListDao"/>
    </bean>
       <!-- #164727-续保审核结论通知书列表查询接口 -->
    <bean id="qry_ReDecAuditingDocNoServiceImpl" class="com.nci.tunan.qry.impl.peripheral.service.ReDecAuditingDocNo.impl.ReDecAuditingDocNoServiceImpl">
    	<property name="iQueryUwNoticeDao" ref="iQueryUwNoticeDao"/>
    </bean>
    <!-- 保单生存给付计划信息查询接口 -->
    <bean id="QRY_queryPayPlanInfoService" class="com.nci.tunan.qry.impl.peripheral.service.r13501900956.impl.QueryPayPlanInfoServiceImpl">
    </bean> 
    <!-- 保单生存给付明细信息查询 -->
    <bean id="QRY_queryPayPlanDetailInfoService" class="com.nci.tunan.qry.impl.peripheral.service.r13501900957.impl.QueryPayPlanDetailInfoServiceImpl">
    </bean> 
    
    <!-- 生存金满期金保单列表查询接口Service -->
    <bean id="QRY_queryPolicyInfosOfSurvivalPaymentService" class="com.nci.tunan.qry.impl.peripheral.service.r13501901026.impl.QueryPolicyInfosOfSurvivalPaymentServiceImpl">
    </bean>
    
    <!--再保对接个险新核心接口_险种其他信息接口  -->
    <bean id="iOtherProductInforMationService" class="com.nci.tunan.qry.impl.peripheral.service.OtherProductInformation.impl.OtherProductInforMationServiceImpl">
		<property name="iotherProductInforMationDao" ref="nbPremArapDao"></property>
	</bean>
    <!-- 关联保单信息查询接口-共同参保保单查询(rm146696) -->
    <bean id="qry_RelationPolicyInfoService" class="com.nci.tunan.qry.impl.peripheral.service.r13501900916.impl.RelationPolicyInfoServiceImpl">
    	<property name="qryContractMasterDao" ref="PA_contractMasterDao"/>
    	<property name="nbContractProductDAO" ref="NB_nbContractProductDAO"/>
    </bean>
      <bean class="com.nci.tunan.qry.dao.impl.NbJointlyInsuredPolicyDaoImpl" id="NB_jointlyInsuredPolicyDao" parent="baseDao">
	</bean>	
	<!-- 保单详情信息查询接口 -->
    <bean id="qry_queryPolicyDetailsServiceImpl" class="com.nci.tunan.qry.impl.peripheral.service.r13501900868.impl.QueryPolicyDetailsServiceImpl">
        <property name="qryContractMasterDao" ref="contractMasterDao"></property>
        <property name="contractProductDao" ref="QRY_contractProductDao"/>
        <property name="nbImageScanDao" ref="NB_ImageScanDao"/>
        <property name="commonQueryDao" ref="commonQueryDao"></property>
        <property name="imageDataInfoDao" ref="NB_imageDataInfoDao"></property>
    </bean>
    <!-- 133925-保单列表查询接口 -->
    <bean id="qry_WXPolicyQueryService" class="com.nci.tunan.qry.impl.peripheral.service.r13501900869.impl.WXPolicyQueryServiceImpl">
    	<property name="wxPolicyQueryDao" ref="qry_WXPolicyQueryDao"/>
    	<property name="queryPolicyDetailsService" ref="qry_queryPolicyDetailsServiceImpl"/>
    </bean>
    
    <!-- 保费明细记录查询接口 -->
    <bean id="QRY_queryPolicyPremDetailInfoService" class="com.nci.tunan.qry.impl.peripheral.service.r13501901043.impl.QueryPolicyPremDetailInfoServiceImpl">
    </bean> 
     <!--  #192831接口需求_官微医药无忧（A04）险种保单相关信息查询接口申请（新核心） -->
    <bean id="QRY_RiskAgreementInformationServiceImpl" class="com.nci.tunan.qry.impl.peripheral.service.re192831.impl.RiskAgreementInformationServiceImpl">
    <property name="contractBusiProdDao" ref="PA_contractBusiProdDao"></property>
    <property name="uwQryDao" ref="uwQryDao"></property>
    </bean> 
</beans>