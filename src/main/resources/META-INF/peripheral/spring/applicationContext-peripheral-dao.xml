<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<bean id="paContractMasterDao" class="com.nci.tunan.qry.dao.impl.PAContractMasterDaoImpl"
		parent="baseDao" />
	<bean id="paQuestionaireCustomerDao" class="com.nci.tunan.qry.dao.impl.PaQuestionaireCustomerDaoImpl"
		parent="baseDao" />
	<bean id="uwPenoticeDao" class="com.nci.tunan.qry.dao.impl.UwPenoticeDaoImpl"
		parent="baseDao" />
	<bean id="nbDocumentDao" class="com.nci.tunan.qry.dao.impl.NbDocumentDaoImpl"
		parent="baseDao" />
	<bean id="paContractInvestDao" class="com.nci.tunan.qry.dao.impl.PAContractInvestDaoImpl"
		parent="baseDao" />
	<bean id="paFundSettlementDao" class="com.nci.tunan.qry.dao.impl.PAFundSettlementDaoImpl"
		parent="baseDao" />
	<bean id="paFundTransDao" class="com.nci.tunan.qry.dao.impl.PAFundTransDaoImpl"
		parent="baseDao" />
	<bean id="paPolicyHolderDao" class="com.nci.tunan.qry.dao.impl.PAPolicyHolderDaoImpl"
		parent="baseDao" />
	<bean id="paPremArapDao" class="com.nci.tunan.qry.dao.impl.PAPremArapDaoImpl"
		parent="baseDao" />
		
	<bean id="paNBPolicyHolderDao"
		class="com.nci.tunan.qry.dao.impl.PANBPolicyHolderDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="paPreAuditMasterDao"
		class="com.nci.tunan.qry.dao.impl.PAPreAuditMasterDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="paPreAuditProductDao"
		class="com.nci.tunan.qry.dao.impl.PAPreAuditProductDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="paPreAuditCauseDao"
		class="com.nci.tunan.qry.dao.impl.PAPreAuditCauseDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<!-- 保单明细下险种信息（新核心） -->
	<bean id="perContractBusiProdCompDao" class="com.nci.tunan.qry.dao.impl.PERContractBusiProdCompDaoImpl" scope="prototype" parent="baseDao">
	</bean>
    <!-- 操作履历下核保通知书查询-->
    <bean id="recordDocumentDao"
        class="com.nci.tunan.qry.dao.impl.NbRecordDocumentDaoImpl"
        scope="prototype" parent="baseDao">
    </bean>
    <!-- 保全核保照会下体检结果查询-->   
    <bean id="uwPenoticeResultDao"
        class="com.nci.tunan.qry.dao.impl.UwPenoticeResultDaoImpl"
        scope="prototype" parent="baseDao">
    </bean>
    
    	<bean id="autoUwDao"
		class="com.nci.tunan.qry.dao.impl.UWAutoUwDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>

	<bean id="preAuditMasterDao"
		class="com.nci.tunan.qry.dao.impl.NBPreAuditMasterDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="proposalProcessDao"
		class="com.nci.tunan.qry.dao.impl.NBProposalProcessDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="uwMasterDao"
		class="com.nci.tunan.qry.dao.impl.UwMasterDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="uwNoteDao"
		class="com.nci.tunan.qry.dao.impl.UwNoteDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	
	<bean id="uwPolicyReviewDao"
		class="com.nci.tunan.qry.dao.impl.UwPolicyReviewDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="uWRiAppPolicyDao"
		class="com.nci.tunan.qry.dao.impl.UWRiAppPolicyDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="uwRuleResultDao"
		class="com.nci.tunan.qry.dao.impl.UWRuleResultDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	
	<bean id="uWSurvivalInvestigationDetaiDao"
		class="com.nci.tunan.qry.dao.impl.UWSurvivalInvestigationDetaiDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	<bean id="nbPreAuditMasterDao"
		class="com.nci.tunan.qry.dao.impl.NBPreAuditMasterDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	<bean id="uWPenoticeDao" class="com.nci.tunan.qry.dao.impl.UwPenoticeDaoImpl"
	parent="baseDao" />
	<bean id="nbProposalProcessDao"
		class="com.nci.tunan.qry.dao.impl.NBProposalProcessDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	<!-- 保单明细查询 -->
	<bean id="NB_contractAgentDAO" class="com.nci.tunan.qry.dao.impl.ContractAgentDaoImpl"
	scope="prototype" parent="baseDao" >
	</bean>
	
	<bean id="iAgentDao" class="com.nci.tunan.qry.dao.impl.AgentDaoImpl" scope="prototype" parent="baseDao">
	</bean>
	<bean id="insureQueryDao" class="com.nci.tunan.qry.dao.impl.InsureQueryDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	<bean id="iQryBankDao" class="com.nci.tunan.qry.dao.impl.QryBankDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	<bean id="iriskInfoDao" class="com.nci.tunan.qry.dao.impl.RiskInfoDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	
	<bean id="appentsDao" class="com.nci.tunan.qry.dao.impl.AppentsDaoImpl" scope="prototype" parent="baseDao">
	
	
	</bean>
	
	<bean id="iQuestionCall" class="com.nci.tunan.qry.dao.impl.QuestionCallDaoImpl" scope="prototype" parent="baseDao">
	
	
	</bean>
	
	<bean id="insuresdDao" class="com.nci.tunan.qry.dao.impl.InsuresdDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	
	<bean id="InsuredInfosDao" class="com.nci.tunan.qry.dao.impl.InsuredInfosDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	<bean id="iquryContsDao" class="com.nci.tunan.qry.dao.impl.QuryContsDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	<!-- 订单查询 -->
	<bean id="orderServiceDaoImpl" class="com.nci.tunan.qry.dao.impl.OrderServiceDaoImpl" scope="prototype" parent="baseDao">
	</bean>
	<!-- 保单红利 -->
	
	<bean id="policyBonusDao" class="com.nci.tunan.qry.dao.impl.PolicyBonusDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	<!-- 保单领取_cjk -->
	<bean id="iPolicyReceiveDao" class="com.nci.tunan.qry.dao.impl.PolicyReceiveDaoImpl" scope="prototype" parent="baseDao" >
	
	</bean>
	<bean id="iQueryUwNoticeDao" class="com.nci.tunan.qry.dao.impl.QueryNwNoticeDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	
	<!-- 体检结果查询 -->
	<bean id="physicalResultQueryDao"
		class="com.nci.tunan.qry.dao.impl.PhysicalResultQueryDaoImpl"
		scope="prototype" parent="baseDao">
	</bean>
	<!-- 产品是否可续投保 -->
	
	<bean id="iProductRenewDao" class="com.nci.tunan.qry.dao.impl.ProductRenewDaoImpl" scope="prototype" parent="baseDao">
	
	</bean>
	
	<bean id="claimCompDao" class="com.nci.tunan.qry.dao.impl.ClaimCompDaoImpl" scope="prototype" parent="baseDao">
	</bean>
	<bean id="PA_csPolicyChangeDao" class="com.nci.tunan.qry.dao.impl.CsPolicyChangeDaoImpl" scope="prototype" parent="baseDao">
	</bean>
	<bean id="PA_csAcceptChangeDao" class="com.nci.tunan.qry.dao.impl.CsAcceptChangeDaoImpl" scope="prototype" parent="baseDao">
	</bean>
	<!-- <bean class="com.nci.tunan.qry.dao.impl.NbContractMasterDaoImpl" id="NB_nbContractMasterDAO" parent="baseDao"/> -->
	<!-- <bean class="com.nci.tunan.qry.dao.impl.ContractAgentDaoImpl" id="NB_contractAgentDAO" parent="baseDao"/> -->
	<bean class="com.nci.tunan.qry.dao.impl.NbInsuredListDaoImpl" id="NB_nbInsuredListDAO" parent="baseDao"/>
	<bean class="com.nci.tunan.qry.dao.impl.NbBenefitInsuredDaoImpl" id="NB_nbBenefitInsuredDAO" parent="baseDao"/> 	
	<bean class="com.nci.tunan.qry.dao.impl.NbContractProductDaoImpl" id="NB_nbContractProductDAO" parent="baseDao"/>
	<!-- 初审信息 -->
	<bean class="com.nci.tunan.qry.dao.impl.PreAuditMasterDaoImpl" id="NB_preAuditMasterDao" parent="baseDao"/>
	<bean class="com.nci.tunan.qry.dao.impl.NbPolicyHolderDaoImpl" id="NB_nbPolicyHolderDAO" parent="baseDao"/>
	<bean class="com.nci.tunan.qry.dao.impl.PreAuditProductDaoImpl" id="NB_preAuditProductDAO" parent="baseDao"/>
	<!-- 特约查询 -->
	<bean class="com.nci.tunan.qry.dao.impl.PolicyConditionDaoImpl" id="PA_policyConditionDao" parent="baseDao"/>

   <!-- 受益人查询 -->
  <bean class="com.nci.tunan.qry.dao.impl.ContractBeneDaoImpl" id="PA_contractBeneDao" parent="baseDao" ></bean>
 
<!-- 以下为移动接口dao -->
	<bean class="com.nci.tunan.qry.dao.impl.UwPolicyDaoImpl" id="UW_uwPolicyDao" parent="baseDao"/>
	<bean class="com.nci.tunan.qry.dao.impl.UwBusiProdDaoImpl" id="UW_uWBusiProdListDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.ContractMasterDaoImpl" id="UW_contractMasterDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.RiAppPolicyDaoImpl" id="UW_riAppPolicyDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.RiAppProductDaoImpl" id="UW_riAppProductDao" parent="baseDao"/>
	<!-- 个单被保人信息接口 -->
	<bean class="com.nci.tunan.qry.dao.impl.ContractBusiProdDaoImpl" id="PA_contractBusiProdDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.BenefitInsuredDaoImpl" id="PA_benefitInsuredDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.CsNoteDaoImpl" id="PA_csNoteDao" parent="baseDao"></bean>
    <bean class="com.nci.tunan.qry.dao.impl.ClaimCaseDaoImpl" id="CLM_claimCaseDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.InsuredListDaoImpl" id="QRY_insuredListDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.ContractProductDaoImpl" id="QRY_contractProductDao" parent="baseDao"/>
	<bean id="qryCustomerDao" class="com.nci.tunan.qry.dao.impl.CustomerDaoImpl" parent="baseDao"></bean>
    <!-- 需求变更#48814 关于调整841健康无忧（宜家版）老客户规则及最低起售金额的需求-->
    <bean class="com.nci.tunan.qry.dao.impl.ProductRuleConfigInfoDao" id="QRY_productRuleConfigInfoDao" parent="baseDao"/>
    
	 <!-- 电话中心保单信息查询  -->
	<bean class="com.nci.tunan.qry.dao.impl.QryContractMasterDaoImpl" id="PA_contractMasterDao" parent="baseDao"></bean>
	<bean class="com.nci.tunan.qry.dao.impl.QryCommonInfoQueryDaoImpl" id="PA_businessProductDao" parent="baseDao"></bean>
	<bean class="com.nci.tunan.qry.dao.impl.PaPolicyAccountDaoImpl" id="PA_policyAccountDao" parent="baseDao"></bean>
	<bean class="com.nci.tunan.qry.dao.impl.QryContractExtendDaoImpl" id="PA_contractExtendDao" parent="baseDao"></bean>
	<bean class="com.nci.tunan.qry.dao.impl.AgentDaoImpl" id="PA_agentDao" parent="baseDao"></bean>
    
    <!-- 客户保单查询  -->
    <bean class="com.nci.tunan.qry.dao.impl.ProductLifeDaoImpl" id="PA_productLifeDao" parent="baseDao"></bean>
    <bean class="com.nci.tunan.qry.dao.impl.BusinessProductDaoImpl" id="businessProductDao" parent="baseDao"></bean>
    
    <!-- 险种责任领取项接口  -->
    <bean class="com.nci.tunan.qry.dao.impl.QryContractBusiProdDaoImpl" id="QRY_contractBusiProdDao" parent="baseDao"></bean>
    <bean class="com.nci.tunan.qry.dao.impl.QryCommonQueryDaoImpl" id="QRY_payPlanDao" parent="baseDao"></bean>
    <bean class="com.nci.tunan.qry.dao.impl.CustomerDaoImpl" id="QRY_customerDao" parent="baseDao"></bean>
    <bean class="com.nci.tunan.qry.dao.impl.QryContractBeneDaoImpl" id="QRY_contractBeneDao" parent="baseDao"></bean>
    <bean class="com.nci.tunan.qry.impl.peripheral.service.r00101002691.impl.PRDServiceImpl" id="QRY_iprdDao"></bean>
    
    <bean class="com.nci.tunan.qry.dao.impl.ClaimForMobileDaoImpl" id="QRY_iClaimForMobileDao" parent="baseDao"></bean>
    
    <!-- 集成快查集合 -->
 	<bean id="QRY_integratedDao" class="com.nci.tunan.qry.dao.impl.IntegratedDaoImpl" parent="baseDao"></bean>
    <!-- 柜面自助终端  红利信息查询(打印通知书类) -->
 	<bean id="QRY_documentDao" class="com.nci.tunan.qry.dao.impl.DocumentQueryDaoImpl" parent="baseDao"></bean>
    <!--是否生成保单打印日期查询接口  -->
	<bean class="com.nci.tunan.qry.dao.impl.QueryProposalPrintDateFlagDaoImpl" id="QRY_queryProposalPrintDateFlagDao" parent="baseDao"></bean>
	<!-- 133925需求- 保单列表查询接口dao层 -->
	<bean id="qry_WXPolicyQueryDao"  class="com.nci.tunan.qry.dao.impl.WXPolicyQueryDaoImpl" parent="baseDao"/>
	
	<bean id="qry_queryPayPlanInfoDao"  class="com.nci.tunan.qry.dao.impl.QueryPayPlanInfoDaoImpl" parent="baseDao"/>
	
	<bean id="qry_queryPayPlanDetailInfoDao"  class="com.nci.tunan.qry.dao.impl.QueryPayPlanDetailInfoDaoImpl" parent="baseDao"/>
	
	<!-- 生存金满期金保单列表查询接口Dao -->
	<bean id="qry_queryPolicyInfosOfSurvivalPaymentDao"  class="com.nci.tunan.qry.dao.impl.QueryPolicyInfosOfSurvivalPaymentDaoImpl" parent="baseDao"/>
	
	 <!-- P307 个人保单信息查询 -->
    <bean class="com.nci.tunan.qry.dao.impl.ContractProductOtherDaoImpl" id="contractProductOtherDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.SecondPolicyHolderDaoImpl" id="QRY_SecondPolicyHolderDao" parent="baseDao"/>
    <bean class="com.nci.tunan.qry.dao.impl.QueryPersonalInsuranceInfoDaoImpl" id="qry_QueryPersonalInsuranceInfoDao" parent="baseDao"/>
	<bean class="com.nci.tunan.qry.dao.impl.PolicyHolderDaoImpl" id="QRY_policyHolderDao" parent="baseDao"/>
	<bean class="com.nci.tunan.qry.dao.impl.QueryPolicyFeeInfoDaoImpl" id="qry_QueryPolicyFeeInfoDao" parent="baseDao"/>
	
</beans>
