<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:jaxws="http://cxf.apache.org/jaxws" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:batch="http://www.springframework.org/schema/batch" xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
        http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-4.0.xsd">


	<!-- 体检资料查询 -->
	<jaxws:endpoint id="queryPhysicalEtionInformation" address="/iNbPhysicalEtionInformationUccqueryPhysicalEtionInformationAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06501001906.inbphysicaletioninformationucc.queryphysicaletioninformation.ws.INbPhysicalEtionInformationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="NbPhysicalEtionInformationUccWSImplqueryPhysicalEtionInformation" class="com.nci.tunan.qry.impl.peripheral.exports.r06501001906.inbphysicaletioninformationucc.queryphysicaletioninformation.ws.NbPhysicalEtionInformationUccWSImpl">
				<property name="ucc">
					<ref bean="iNbPhysicalEtionInformationUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 已承保保单查询-->
	<jaxws:endpoint id="queryGuarantee" address="/PaGuaranteeQueryUccqueryGuaranteeAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001858.ipaguaranteequeryucc.queryguarantee.ws.IPaGuaranteeQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PaGuaranteeQueryUccWSImplqueryGuarantee" class="com.nci.tunan.qry.impl.peripheral.exports.r06401001858.ipaguaranteequeryucc.queryguarantee.ws.PaGuaranteeQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PaGuaranteeQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 影像资料查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryVideoInfo" address="/videoInfoQueryUccImplqueryVideoInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001839.ipavideoinfoqueryucc.queryvideoinfo.ws.IPaVideoInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PaVideoInfoQueryUccWSImplqueryVideoInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r06401001839.ipavideoinfoqueryucc.queryvideoinfo.ws.PaVideoInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="videoInfoQueryUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="downVideoInfo" address="/videoInfoDownUccImpldownVideoInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001840.ipavideoinfodownucc.downvideoinfo.ws.IPaVideoInfoDownUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PaVideoInfoDownUccWSImpldownVideoInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r06401001840.ipavideoinfodownucc.downvideoinfo.ws.PaVideoInfoDownUccWSImpl">
				<property name="ucc">
					<ref bean="videoInfoDownUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 理赔核保照会加费应收信息查询  -->
	<jaxws:endpoint id="queryUwFeeInfo" address="/claimQueryUwFeeInfoUccqueryUwFeeInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06701001914.iclaimqueryuwfeeinfoucc.queryuwfeeinfo.ws.IClaimQueryUwFeeInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="ClaimQueryUwFeeInfoUccWSImplqueryUwFeeInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r06701001914.iclaimqueryuwfeeinfoucc.queryuwfeeinfo.ws.ClaimQueryUwFeeInfoUccWSImpl">
				<property name="ucc">
					<ref bean="claimQueryUwFeeInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 再保险 保单管理 操作履历下初审详细信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="preAuditInfoQuery" address="/preAuditInfoQueryUccpreAuditInfoQueryAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001886.ipapreauditinfoqueryucc.preauditinfoquery.ws.IPAPreAuditInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PAPreAuditInfoQueryUccWSImplpreAuditInfoQuery" class="com.nci.tunan.qry.impl.peripheral.exports.r06401001886.ipapreauditinfoqueryucc.preauditinfoquery.ws.PAPreAuditInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="preAuditInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<jaxws:endpoint id="queryUniversalAccountInfo" address="/universalAccountInfoQueryUccqueryUniversalAccountInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000031.iuniversalaccountinfoqueryucc.queryuniversalaccountinfo.ws.IUniversalAccountInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="UniversalAccountInfoQueryUccWSImplqueryUniversalAccountInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r00101000031.iuniversalaccountinfoqueryucc.queryuniversalaccountinfo.ws.UniversalAccountInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="universalAccountInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单明细下险种信息（新核心） -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryContractBusiProdInfo" address="/perContractBusiProdInfoUccqueryContractBusiProdInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001810.ipercontractbusiprodinfoucc.querycontractbusiprodinfo.ws.IPERContractBusiProdInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PERContractBusiProdInfoUccWSImplqueryContractBusiProdInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r06401001810.ipercontractbusiprodinfoucc.querycontractbusiprodinfo.ws.PERContractBusiProdInfoUccWSImpl">
				<property name="ucc">
					<ref bean="perContractBusiProdInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--by chenaq 操作履历下核保通知书查询-->	
	<jaxws:endpoint id="queryRecordDocument" address="/recordDocumentQueryUccqueryRecordDocumentAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06501001904.inbrecorddocumentqueryucc.queryrecorddocument.ws.INbRecordDocumentQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="NbRecordDocumentQueryUccWSImplqueryRecordDocument" class="com.nci.tunan.qry.impl.peripheral.exports.r06501001904.inbrecorddocumentqueryucc.queryrecorddocument.ws.NbRecordDocumentQueryUccWSImpl">
				<property name="ucc">
					<ref bean="recordDocumentQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!--by chenaq 保全核保照会下体检结果查询-->
	<jaxws:endpoint id="queryPresentPhysical" address="/presentPhysicalQueryUccqueryPresentPhysicalAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001898.iuwpresentphysicalqueryucc.querypresentphysical.ws.IUwPresentPhysicalQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="UwPresentPhysicalQueryUccWSImplqueryPresentPhysical" class="com.nci.tunan.qry.impl.peripheral.exports.r06601001898.iuwpresentphysicalqueryucc.querypresentphysical.ws.UwPresentPhysicalQueryUccWSImpl">
				<property name="ucc">
					<ref bean="presentPhysicalQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryOperatingHistory" address="/OperatingHistoryQueryUccqueryOperatingHistoryAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001888.ioperatinghistoryqueryucc.queryoperatinghistory.ws.IOperatingHistoryQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="OperatingHistoryQueryUccWSImplqueryOperatingHistory" class="com.nci.tunan.qry.impl.peripheral.exports.r06601001888.ioperatinghistoryqueryucc.queryoperatinghistory.ws.OperatingHistoryQueryUccWSImpl">
				<property name="ucc">
					<ref bean="OperatingHistoryQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 保单明细R -->
	<jaxws:endpoint id="findPadetailByPolicyId" address="/iPaDetailQueryUccfindPadetailByPolicyIdAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001837.ipadetailqueryucc.findpadetailbypolicyid.ws.IPaDetailQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PaDetailQueryUccWSImplfindPadetailByPolicyId" class="com.nci.tunan.qry.impl.peripheral.exports.r06401001837.ipadetailqueryucc.findpadetailbypolicyid.ws.PaDetailQueryUccWSImpl">
				<property name="ucc">
					<ref bean="iPaDetailQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<jaxws:endpoint id="queryPolicyReceive" address="/policyReceiveUccqueryPolicyReceiveAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000871.ipolicyreceiveucc.querypolicyreceive.ws.IPolicyReceiveUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyReceiveUccWSImplqueryPolicyReceive" class="com.nci.tunan.qry.impl.peripheral.exports.r00101000871.ipolicyreceiveucc.querypolicyreceive.ws.PolicyReceiveUccWSImpl">
				<property name="ucc">
					<ref bean="policyReceiveUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- CRM保单红利信息查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyBonus" address="/policyBonusQueryUccqueryPolicyBonusAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000869.ipolicybonusqueryucc.querypolicybonus.ws.IPolicyBonusQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyBonusQueryUccWSImplqueryPolicyBonus" class="com.nci.tunan.qry.impl.peripheral.exports.r00101000869.ipolicybonusqueryucc.querypolicybonus.ws.PolicyBonusQueryUccWSImpl">
				<property name="ucc">
					<ref bean="policyBonusQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>	
	
	<!-- 订单查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryOrderInfo" address="/orderInfoQueryUccqueryOrderInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000749.iorderinfoqueryucc.queryorderinfo.ws.IOrderInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="OrderInfoQueryUccWSImplqueryOrderInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r00101000749.iorderinfoqueryucc.queryorderinfo.ws.OrderInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="orderInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保全体检结果查询 -->
	<jaxws:endpoint id="queryCsMedicalResult" address="/ICsMedicalResultQueryUccqueryCsMedicalResultAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06801001814.icsmedicalresultqueryucc.querycsmedicalresult.ws.ICsMedicalResultQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CsMedicalResultQueryUccWSImplqueryCsMedicalResult" class="com.nci.tunan.qry.impl.peripheral.exports.r06801001814.icsmedicalresultqueryucc.querycsmedicalresult.ws.CsMedicalResultQueryUccWSImpl">
				<property name="ucc">
					<ref bean="csMedicalResultQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 核保通知书列表信息 -->
	<jaxws:endpoint id="queryUwNotice" address="/IQueryUwNoticeUccqueryUwNoticeAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000885.iqueryuwnoticeucc.queryuwnotice.ws.IQueryUwNoticeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryUwNoticeUccWSImplqueryUwNotice" class="com.nci.tunan.qry.impl.peripheral.exports.r00101000885.iqueryuwnoticeucc.queryuwnotice.ws.QueryUwNoticeUccWSImpl">
				<property name="ucc">
					<ref bean="IQueryUwNoticeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPhysicalResultList" address="/physicalResultQueryUccqueryPhysicalResultListAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06801001921.iphysicalresultqueryucc.queryphysicalresultlist.ws.IPhysicalResultQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PhysicalResultQueryUccWSImplqueryPhysicalResultList" class="com.nci.tunan.qry.impl.peripheral.exports.r06801001921.iphysicalresultqueryucc.queryphysicalresultlist.ws.PhysicalResultQueryUccWSImpl">
				<property name="ucc">
					<ref bean="physicalResultQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 南京医保交费信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="medicalInsuranceFeeInfoQuery" address="/medicalInsuranceFeeInfoQueryUccmedicalInsuranceFeeInfoQueryAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002257.injmedicalinsurancefeeinfoqueryucc.medicalinsurancefeeinfoquery.ws.INJMedicalInsuranceFeeInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="NJMedicalInsuranceFeeInfoQueryUccWSImplmedicalInsuranceFeeInfoQuery" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002257.injmedicalinsurancefeeinfoqueryucc.medicalinsurancefeeinfoquery.ws.NJMedicalInsuranceFeeInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="medicalInsuranceFeeInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 产品是否可续投保 -->
	<jaxws:endpoint id="queryPolicyRenew" address="/queryIfPolicyRenewqueryPolicyRenewAddr" 

implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101001019.ipolicyrenewqueryucc.querypolicyrenew.ws.IPolicyRenewQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyRenewQueryUccWSImplqueryPolicyRenew" 

class="com.nci.tunan.qry.impl.peripheral.exports.r00101001019.ipolicyrenewqueryucc.querypolicyrenew.ws.PolicyRenewQueryUccWSImpl">
				<property name="ucc">
					<ref bean="queryIfPolicyRenew" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 临分查询接口 -->
	<jaxws:endpoint id="queryData" address="/LinFenQueryUccqueryDataAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002447.ilinfenqueryucc.querydata.ws.ILinFenQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="LinFenQueryUccWSImplqueryData" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002447.ilinfenqueryucc.querydata.ws.LinFenQueryUccWSImpl">
				<property name="ucc">
					<ref bean="LinFenQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 临分计算数据接口 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="linfenCalcDataInfo" address="/linfenCalcDataInfomationUcclinfenCalcDataInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401002582.ilinfencalcdatainfomationucc.linfencalcdatainfo.ws.ILinfenCalcDataInfomationUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="LinfenCalcDataInfomationUccWSImpllinfenCalcDataInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r06401002582.ilinfencalcdatainfomationucc.linfencalcdatainfo.ws.LinfenCalcDataInfomationUccWSImpl">
				<property name="ucc">
					<ref bean="linfenCalcDataInfomationUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 临分分页计算接口 -->
	<jaxws:endpoint id="queryPateData" address="/linFenPageQueryDataUccqueryDataAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401002583.ilinfenpagequerydataucc.querydata.ws.ILinFenPageQueryDataUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="LinFenPageQueryDataUccWSImplqueryData" class="com.nci.tunan.qry.impl.peripheral.exports.r06401002583.ilinfenpagequerydataucc.querydata.ws.LinFenPageQueryDataUccWSImpl">
				<property name="ucc">
					<ref bean="linFenPageQueryDataUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 保单查询接口-按投保单号或保单号查询  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyInfoByAppOrPolicyCode" address="/policyQueryInfoByAppOrPolicyCodeUccqueryPolicyInfoByAppOrPolicyCodeAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.ws.IPolicyQueryInfoByAppOrPolicyCodeUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyQueryInfoByAppOrPolicyCodeUccWSImplqueryPolicyInfoByAppOrPolicyCode" class="com.nci.tunan.qry.impl.peripheral.exports.r00101001334.ipolicyqueryinfobyapporpolicycodeucc.querypolicyinfobyapporpolicycode.ws.PolicyQueryInfoByAppOrPolicyCodeUccWSImpl">
				<property name="ucc">
					<ref bean="policyQueryInfoByAppOrPolicyCodeUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单查询接口-按身份证号查询  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyInfoByIdNo" address="/policyQueryInfoByIdNoUccqueryPolicyInfoByIdNoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101001336.ipolicyqueryinfobyidnoucc.querypolicyinfobyidno.ws.IPolicyQueryInfoByIdNoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyQueryInfoByIdNoUccWSImplqueryPolicyInfoByIdNo" class="com.nci.tunan.qry.impl.peripheral.exports.r00101001336.ipolicyqueryinfobyidnoucc.querypolicyinfobyidno.ws.PolicyQueryInfoByIdNoUccWSImpl">
				<property name="ucc">
					<ref bean="policyQueryInfoByIdNoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 追加保费查询 -->
	<jaxws:endpoint address="/addPremQueryUccqueryAddPremAddr" id="PA_queryAddPrem" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000988.iaddpremqueryucc.queryaddprem.ws.IAddPremQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r00101000988.iaddpremqueryucc.queryaddprem.ws.AddPremQueryUccWSImpl" id="PA_AddPremQueryUccWSImplqueryAddPrem">
				<property name="ucc" ref="PA_addPremQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--工行纸质保单补充接口   -->
	<jaxws:endpoint address="/IPaperSupplementUccpaperSupplementAddr" id="NB_paperSupplement" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000984.ipapersupplementucc.papersupplement.ws.IPaperSupplementUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r00101000984.ipapersupplementucc.papersupplement.ws.PaperSupplementUccWSImpl" id="NB_PaperSupplementUccWSImplpaperSupplement">
				<property name="ucc" ref="QRY_NB_IPaperSupplementUcc"></property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 网站E化单证防伪验证码验真接口  -->
	<jaxws:endpoint id="judgeWebEDocumentPseudoVer" address="/QRY_WebEDocumentPseudoVerifiUccjudgeWebEDocumentPseudoVerAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101001332.iwebedocumentpseudoverifiucc.judgewebedocumentpseudover.ws.IWebEDocumentPseudoVerifiUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="WebEDocumentPseudoVerifiUccWSImpljudgeWebEDocumentPseudoVer" class="com.nci.tunan.qry.impl.peripheral.exports.r00101001332.iwebedocumentpseudoverifiucc.judgewebedocumentpseudover.ws.WebEDocumentPseudoVerifiUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_WebEDocumentPseudoVerifiUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 保费项信息接口 -->
	<jaxws:endpoint id="queryInfo" address="/queryPremiumsInfoUccqueryInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002693.iquerypremiumsinfoucc.queryinfo.ws.IQueryPremiumsInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPremiumsInfoUccWSImplqueryInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002693.iquerypremiumsinfoucc.queryinfo.ws.QueryPremiumsInfoUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_queryPremiumsInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 保单险种表信息接口 -->
	<jaxws:endpoint id="queryContBusiProd" address="/queryContBusiProdUccqueryInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002675.iquerycontbusiproducc.queryinfo.ws.IQueryContBusiProdUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryContBusiProdUccWSImplqueryInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002675.iquerycontbusiproducc.queryinfo.ws.QueryContBusiProdUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_queryContBusiProdUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 险种责任领取项接口 -->
	<jaxws:endpoint id="queryLpPremItem" address="/QRY_lpPremItemUccqueryLpPremItemAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002691.ilppremitemucc.querylppremitem.ws.ILpPremItemUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="LpPremItemUccWSImplqueryLpPremItem" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002691.ilppremitemucc.querylppremitem.ws.LpPremItemUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_lpPremItemUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 网站法人E化单证验真接口  -->
	<jaxws:endpoint id="queryWebCorpEDocumentVerifi" address="/webCorpEDocumentVerifiUccqueryWebCorpEDocumentVerifiAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101001380.iwebcorpedocumentverifiucc.querywebcorpedocumentverifi.ws.IWebCorpEDocumentVerifiUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="WebCorpEDocumentVerifiUccWSImplqueryWebCorpEDocumentVerifi" class="com.nci.tunan.qry.impl.peripheral.exports.r00101001380.iwebcorpedocumentverifiucc.querywebcorpedocumentverifi.ws.WebCorpEDocumentVerifiUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_webCorpEDocumentVerifiUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 操作履历下核保查询  -->
	<jaxws:endpoint address="/ResumeQueryUccqueryResumeAddr" id="NB_queryResume" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06501001902.iresumequeryucc.queryresume.ws.IResumeQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06501001902.iresumequeryucc.queryresume.ws.ResumeQueryUccWSImpl" id="NB_ResumeQueryUccWSImplqueryResume">
				<property name="ucc" ref="NB_ResumeQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 受益人查询 -->
	<jaxws:endpoint address="/zbBenefitInfoQueryUcczbBenefitInfoQueryAddr" id="PA_zbBenefitInfoQuery" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001871.izbbenefitinfoqueryucc.zbbenefitinfoquery.ws.IZBBenefitInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06401001871.izbbenefitinfoqueryucc.zbbenefitinfoquery.ws.ZBBenefitInfoQueryUccWSImpl" id="PA_ZBBenefitInfoQueryUccWSImplzbBenefitInfoQuery">
				<property name="ucc" ref="PA_zbBenefitInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

  <!-- 险种责任接口-->
	<jaxws:endpoint id="busiItemDuty" address="/PA_busiItemDutyUccbusiItemDutyAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002681.ibusiitemdutyucc.busiitemduty.ws.IBusiItemDutyUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="BusiItemDutyUccWSImplbusiItemDuty" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002681.ibusiitemdutyucc.busiitemduty.ws.BusiItemDutyUccWSImpl">
				<property name="ucc">
					<ref bean="PA_busiItemDutyUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 操作履历下初审基础信息查询 -->
	<jaxws:endpoint address="/resumeFirstBaseQueryUccresumeFirstBaseQueryAddr" id="NB_resumeFirstBaseQuery" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06501001905.iresumefirstbasequeryucc.resumefirstbasequery.ws.IResumeFirstBaseQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06501001905.iresumefirstbasequeryucc.resumefirstbasequery.ws.ResumeFirstBaseQueryUccWSImpl" id="NB_ResumeFirstBaseQueryUccWSImplresumeFirstBaseQuery">
				<property name="ucc" ref="NB_resumeFirstBaseQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 特约查询 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryPolicyConditionUCCqueryPolicyConditionAddr" id="PA_queryPolicyCondition" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001861.iquerypolicyconditionucc.querypolicycondition.ws.IQueryPolicyConditionUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06401001861.iquerypolicyconditionucc.querypolicycondition.ws.QueryPolicyConditionUCCWSImpl" id="PA_QueryPolicyConditionUCCWSImplqueryPolicyCondition">
				<property name="ucc" ref="PA_queryPolicyConditionUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryUWHistoryUccqueryUWHistoryAddr" id="UW_queryUWHistory" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001889.iqueryuwhistoryucc.queryuwhistory.ws.IQueryUWHistoryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06601001889.iqueryuwhistoryucc.queryuwhistory.ws.QueryUWHistoryUccWSImpl" id="UW_QueryUWHistoryUccWSImplqueryUWHistory">
				<property name="ucc" ref="UW_IQueryUWHistoryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- R06601001890  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/IQueryBusiProdUWDecisionUccqueryBusiProdUWDecisionAddr" id="UW_queryBusiProdUWDecision" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001890.iquerybusiproduwdecisionucc.querybusiproduwdecision.ws.IQueryBusiProdUWDecisionUccWS">	
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06601001890.iquerybusiproduwdecisionucc.querybusiproduwdecision.ws.QueryBusiProdUWDecisionUccWSImpl" id="UW_QueryBusiProdUWDecisionUccWSImplqueryBusiProdUWDecision">
				<property name="ucc" ref="UW_IQueryBusiProdUWDecisionUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!--by zhaoyoan_wb 操作履历下再保回复查询 -->
	<jaxws:endpoint address="/reinsuranceReplyQueryUccqueryReinsuranceReplyAddr" id="UW_queryReinsuranceReply" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001891.ireinsurancereplyqueryucc.queryreinsurancereply.ws.IReinsuranceReplyQueryUccWS">		
		                                                                                                                                                          
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06601001891.ireinsurancereplyqueryucc.queryreinsurancereply.ws.ReinsuranceReplyQueryUccWSImpl" id="UW_ReinsuranceReplyQueryUccWSImplqueryReinsuranceReply">
				<property name="ucc" ref="UW_reinsuranceReplyQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!-- 操作履历下客户合并通知书查询 -->
	<jaxws:endpoint address="/nbCustomerMergeNoticeQueryUccqueryCustomerMergeNoticeListAddr" id="NB_queryCustomerMergeNoticeList" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001892.querycustomermergenoticelist.ws.INbCustomerMergeNoticeQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06601001892.inbcustomermergenoticequeryucc.querycustomermergenoticelist.ws.NbCustomerMergeNoticeQueryUccWSImpl" id="NB_NbCustomerMergeNoticeQueryUccWSImplqueryCustomerMergeNoticeList">
				<property name="ucc" ref="NB_nbCustomerMergeNoticeQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

		<!-- 个单被保人信息接口 -->
	<jaxws:endpoint id="queryContInsuredInfo" address="/contInsuredInfoQueryForRIUccqueryContInsuredInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002709.icontinsuredinfoqueryforriucc.querycontinsuredinfo.ws.IContInsuredInfoQueryForRIUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="ContInsuredInfoQueryForRIUccWSImplqueryContInsuredInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002709.icontinsuredinfoqueryforriucc.querycontinsuredinfo.ws.ContInsuredInfoQueryForRIUccWSImpl">
				<property name="ucc">
					<ref bean="PA_contInsuredInfoQueryForRIUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--by zhaoyoan_wb 操作履历下复议答复 -->
	<jaxws:endpoint address="/reviewReplyQueryUccqueryReviewReplyQueryAddr" id="UW_queryReviewReplyQuery" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001893.ireviewreplyqueryucc.queryreviewreplyquery.ws.IReviewReplyQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06601001893.ireviewreplyqueryucc.queryreviewreplyquery.ws.ReviewReplyQueryUccWSImpl" id="UW_ReviewReplyQueryUccWSImplqueryReviewReplyQuery">
				<property name="ucc" ref="UW_reviewReplyQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--by zhaoyoan_wb 操作履历下自核提示查询 -->

	<!--by zhaoyoan_wb 操作履历下自核提示查询 -->
	<jaxws:endpoint address="/autoUWPointQueryUccqueryAutoUWPointAddr" id="UW_queryAutoUWPoint" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001895.iautouwpointqueryucc.queryautouwpoint.ws.IAutoUWPointQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06601001895.iautouwpointqueryucc.queryautouwpoint.ws.AutoUWPointQueryUccWSImpl" id="UW_AutoUWPointQueryUccWSImplqueryAutoUWPoint">
				<property name="ucc" ref="UW_autoUWPointQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
		<!-- 连带被保险人信息接口 -->
	<jaxws:endpoint id="queryJointInsurer" address="/PA_jointInsurerInfUccqueryJointInsurerAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002683.ijointinsurerinfucc.queryjointinsurer.ws.IJointInsurerInfUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="JointInsurerInfUccWSImplqueryJointInsurer" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002683.ijointinsurerinfucc.queryjointinsurer.ws.JointInsurerInfUccWSImpl">
				<property name="ucc">
					<ref bean="PA_jointInsurerInfUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	
	<!--by zhaoyoan_wb 操作履历下无需体检生调原因查询 -->
	<jaxws:endpoint address="/noPhysicalReasonQueryUccqueryNoPhysicalReasonAddr" id="UW_queryNoPhysicalReason" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001896.inophysicalreasonqueryucc.querynophysicalreason.ws.INoPhysicalReasonQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06601001896.inophysicalreasonqueryucc.querynophysicalreason.ws.NoPhysicalReasonQueryUccWSImpl" id="UW_NoPhysicalReasonQueryUccWSImplqueryNoPhysicalReason">
				<property name="ucc" ref="UW_noPhysicalReasonQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 操作履历下问题件查询 -->
	<jaxws:endpoint address="/problemPiecesQueryUccproblemPiecesQueryAddr" id="NB_problemPiecesQuery" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06501001903.iproblempiecesqueryucc.problempiecesquery.ws.IProblemPiecesQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06501001903.iproblempiecesqueryucc.problempiecesquery.ws.ProblemPiecesQueryUccWSImpl" id="NB_ProblemPiecesQueryUccWSImplproblemPiecesQuery">
				<property name="ucc" ref="NB_problemPiecesQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/ISurveyResultQueryUccquerySurveyResultAddr" id="PA_querySurveyResult" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001842.isurveyresultqueryucc.querysurveyresult.ws.ISurveyResultQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06401001842.isurveyresultqueryucc.querysurveyresult.ws.SurveyResultQueryUccWSImpl" id="PA_SurveyResultQueryUccWSImplquerySurveyResult">
				<property name="ucc" ref="PA_surveyResultQueryUccImpl">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--by zhaoyoan_wb 操作履历下记事本查询-->
	<jaxws:endpoint address="/noteInfoQueryUccnoteInfoQueryAddr" id="PA_noteInfoQuery" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401001853.inoteinfoqueryucc.noteinfoquery.ws.INoteInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06401001853.inoteinfoqueryucc.noteinfoquery.ws.NoteInfoQueryUccWSImpl" id="PA_NoteInfoQueryUccWSImplnoteInfoQuery">
				<property name="ucc" ref="PA_noteInfoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- R06601001897 合同核保轨迹查询 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/uwContUwTraceQueryUccqueryContUwTraceListAddr"
		id="UW_queryContUwTraceList"
		implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06601001897.icontuwtracequeryucc.querycontuwtracelist.ws.IContUwTraceQueryUccWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean
				class="com.nci.tunan.qry.impl.peripheral.exports.r06601001897.icontuwtracequeryucc.querycontuwtracelist.ws.ContUwTraceQueryUccWSImpl"
				id="UW_ContUwTraceQueryUccWSImplqueryContUwTraceList">
				<property name="ucc" ref="UW_uwContUwTraceQueryUcc">

				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	

	
	<!-- 网站在线自助报案客户校验接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/webOnlineSelfServiceReportCheckUcconlineSelfServiceReportAddr" id="CLM_onlineSelfServiceReport" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002191.iwebonlineselfservicereportcheckucc.onlineselfservicereport.ws.IWebOnlineSelfServiceReportCheckUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r00101002191.iwebonlineselfservicereportcheckucc.onlineselfservicereport.ws.IWebOnlineSelfServiceReportCheckUccWS.WebOnlineSelfServiceReportCheckUccWSImpl" id="CLM_WebOnlineSelfServiceReportCheckUccWSImplonlineSelfServiceReport">
				<property name="ucc" ref="CLM_webOnlineSelfServiceReportCheckUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保全核保照会查询  -->
	<jaxws:endpoint address="/csUWconfirmQueryUCCCsUWconfirmQueryUCCImplAddr" id="PA_csUWconfirmquery" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06801001924.icsuwconfirmqueryucc.csuwconfirmqueryuccimpl.ws.ICsUWconfirmQueryUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r06801001924.icsuwconfirmqueryucc.csuwconfirmqueryuccimpl.ws.CsUWconfirmQueryUCCWSImpl" id="PA_CsUWconfirmQueryUCCWSImplCsUWconfirmQueryUCCImpl">
				<property name="ucc" ref="PA_csUWconfirmQueryUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 出险人保单查询接口 -->
	<jaxws:endpoint id="queryCusContInfoList" address="/cusContInfoQueryUccqueryCusContInfoListAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000061.icuscontinfoqueryucc.querycuscontinfolist.ws.ICusContInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CusContInfoQueryUccWSImplqueryCusContInfoList" class="com.nci.tunan.qry.impl.peripheral.exports.r00101000061.icuscontinfoqueryucc.querycuscontinfolist.ws.CusContInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="PA_cusContInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 核心保单验真 -->
		<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="policyDocumentPseudoVer" address="/PA_policyDocumentPseudoVerifiUccImplpolicyDocumentPseudoVerAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101001688.ipolicydocumentpseudoverifiucc.policydocumentpseudover.ws.IPolicyDocumentPseudoVerifiUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyDocumentPseudoVerifiUccWSImplpolicyDocumentPseudoVer" class="com.nci.tunan.qry.impl.peripheral.exports.r00101001688.ipolicydocumentpseudoverifiucc.policydocumentpseudover.ws.PolicyDocumentPseudoVerifiUccWSImpl">
				<property name="ucc">
					<ref bean="PA_policyDocumentPseudoVerifiUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 电话中心保单信息查询  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/telephoneCenterQueryPolicyInfoUccqueryPolicyInfoAddr" id="PA_queryPolicyInfo" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002246.itelephonecenterquerypolicyinfo.querypolicyinfo.ws.ITelephoneCenterQueryPolicyInfoWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r00101002246.itelephonecenterquerypolicyinfo.querypolicyinfo.ws.TelephoneCenterQueryPolicyInfoWSImpl" id="PA_TelephoneCenterQueryPolicyInfoWSImplqueryPolicyInfo">
				<property name="ucc" ref="PA_telephoneCenterQueryPolicyInfoUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户保单查询-->
	<jaxws:endpoint id="queryPolicyInfo" address="/customerPolicyInfoUccImplqueryPolicyInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002633.icustomerpolicyinfoucc.querypolicyinfo.ws.ICustomerPolicyInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CustomerPolicyInfoUccWSImplqueryPolicyInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002633.icustomerpolicyinfoucc.querypolicyinfo.ws.CustomerPolicyInfoUccWSImpl">
				<property name="ucc">
					<ref bean="PA_customerPolicyInfoUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 老客户校验，从保单迁移到综合查询 -->
	<!-- 健康无忧（尊享版）老客户校验接口 -->
	<jaxws:endpoint id="verifyOldCustomer" address="/oldCustomerVerifyUCCverifyOldCustomerAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101002797.ioldcustomerverifyucc.verifyoldcustomer.ws.IOldCustomerVerifyUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="OldCustomerVerifyUCCWSImplverifyOldCustomer" class="com.nci.tunan.qry.impl.peripheral.exports.r00101002797.ioldcustomerverifyucc.verifyoldcustomer.ws.OldCustomerVerifyUCCWSImpl">
				<property name="ucc">
					<ref bean="PA_OldCustomerVerifyUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 理赔列表信息查询 -->
	<jaxws:endpoint address="/iQueryClaimListInfoUccqueryInfoAddr" id="CLM_queryClaimListInfo" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r00101000045.iqueryclaimlistinfoucc.queryclaimlistinfo.ws.IQueryClaimListInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r00101000045.iqueryclaimlistinfoucc.queryclaimlistinfo.ws.QueryClaimListInfoUccWSImpl" id="CLM_QueryClaimListInfoUccWSImplqueryClaimListInfo">
				<property name="ucc">
					<ref bean="QRY_iQueryClaimListInfoUcc"/>
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 订单查询 -->
		<jaxws:endpoint address="/zb_orderNoQueryUccqueryOrderNoAddr" id="PA_queryOrderNo" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.ordernoquery.iordernoqueryucc.queryorderno.ws.IOrderNoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.ordernoquery.iordernoqueryucc.queryorderno.ws.OrderNoQueryUccWSImpl" id="PA_OrderNoQueryUccWSImplqueryOrderNo">
				<property name="ucc" ref="PA_orderNoQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
		
		<!-- 加费通知单号快速查询 -->
	<jaxws:endpoint id="isExistAddPremDocNo" address="/zb_addPremDocNoQuickSearchUccisExistAddPremDocNoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.judgeaddpremdocnoexist.iaddpremdocnoquicksearchucc.isexistaddpremdocno.ws.IAddPremDocNoQuickSearchUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AddPremDocNoQuickSearchUccWSImplisExistAddPremDocNo" class="com.nci.tunan.qry.impl.peripheral.exports.judgeaddpremdocnoexist.iaddpremdocnoquicksearchucc.isexistaddpremdocno.ws.AddPremDocNoQuickSearchUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_addPremDocNoQuickSearchUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 交退费编号快查服务-->
	<jaxws:endpoint address="/zb_judgeUnitNumberExistUccjudgeUnitnumberExistAddr" id="QRY_judgeUnitnumberExist" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.judgeunitnumberExist.ijudgeunitnumberexistucc.judgeunitnumberexist.ws.IJudgeUnitNumberExistUccWS">		
	<jaxws:implementor>
		<!-- class对应服务发布接口的实现类  -->
		<bean class="com.nci.tunan.qry.impl.peripheral.exports.ijudgeunitnumberexistucc.judgeunitnumberexist.ws.JudgeUnitNumberExistUccWSImpl" id="QRY_JudgeUnitNumberExistUccWSImpljudgeUnitnumberExist">
			<property name="ucc" ref="QRY_judgeUnitNumberExistUcc">
				
			</property>
		</bean>
	</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 投保单号快速查询是否存在服务 -->
	<jaxws:endpoint address="/zb_judgeApplyCodeExitUccjudgeApplyCodeExitAddr" id="NB_judgeApplyCodeExit" implementorClass="com.nci.tunan.qry.interfaces.judgeapplycodeexit.exports.ijudgeapplycodeexitucc.judgeapplycodeexit.ws.IJudgeApplyCodeExitUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.judgeapplycodeexit.exports.ijudgeapplycodeexitucc.judgeapplycodeexit.ws.JudgeApplyCodeExitUccWSImpl" id="NB_JudgeApplyCodeExitUccWSImpljudgeApplyCodeExit">
				<property name="ucc" ref="NB_judgeApplyCodeExitUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!--提调号查询 implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/zb_judgeSericurityNoExistUccjudgeSericurityNoExistAddr" id="QRY_judgeSericurityNoExist" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.judgesecuritynoexist.judgesecuritynoexist.exports.ijudgesericuritynoexistucc.judgesericuritynoexist.ws.IjudgeSericurityNoExistUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.ijudgesericuritynoexistucc.judgesericuritynoexist.ws.judgeSericurityNoExistUccWSImpl" id="QRY_judgeSericurityNoExistUccWSImpljudgeSericurityNoExist">
				<property name="ucc" ref="QRY_judgeSericurityNoExistUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保单号快查 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/zb_judgePolicyExistUCCjudegPolicyExistAddr" id="PA_judegPolicyExist" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.judgepolicyexist.ijudgepolicyexistucc.judegpolicyexist.ws.IjudgePolicyExistUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.judgepolicyexist.ijudgepolicyexistucc.judegpolicyexist.ws.judgePolicyExistUccWSImpl" id="QRY_judgePolicyExistUccWSImpljudegPolicyExist">
				<property name="ucc" ref="QRY_judgePolicyExistUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保全受理号码快速查询是否存在服务 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/zb_csPolicyAcceptIsExistedUccisExistedCsPolicyAcceptAddr" id="PA_isExistedCsPolicyAccept" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.cspolicyaccept.icspolicyacceptisexisteducc.isexistedcspolicyaccept.ws.ICsPolicyAcceptIsExistedUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.cspolicyaccept.icspolicyacceptisexisteducc.isexistedcspolicyaccept.ws.CsPolicyAcceptIsExistedUccWSImpl" id="QRY_CsPolicyAcceptIsExistedUccWSImplisExistedCsPolicyAccept">
				<property name="ucc" ref="QRY_csPolicyAcceptIsExistedUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
        <!--赔案号快查服务-->
		<!-- address属性是调用webservice时需要使用的地址赔案号快速查询是否存在服务  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/zb_casenoIsExistServUCCisExistServByCasenoAddr" id="CLM_isExistServByCaseno" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.casenoisexistserv.icasenoisexistservucc.isexistservbycaseno.ws.ICasenoIsExistServUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.casenoisexistserv.icasenoisexistservucc.isexistservbycaseno.ws.CasenoIsExistServUCCWSImpl" id="CLM_CasenoIsExistServUCCWSImplisExistServByCaseno">
				<property name="ucc" ref="CLM_casenoIsExistServUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		<!-- 核保通知书号快速查询接口 -->
	<jaxws:endpoint id="judgePrtSeqExist" address="/zb_judgePrtSeqExistUccjudgePrtSeqExistAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.judgeprtseqexist.ijudgeprtseqexistucc.judgeprtseqexist.ws.IJudgePrtSeqExistUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="JudgePrtSeqExistUccWSImpljudgePrtSeqExist" class="com.nci.tunan.qry.impl.peripheral.exports.judgeprtseqexist.ijudgeprtseqexistucc.judgeprtseqexist.ws.JudgePrtSeqExistUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_judgePrtSeqExistUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		<!-- 保全批单号码快速查询是否存在服务 -->
	<jaxws:endpoint address="/zb_csEndorseCodeIsExistedUccisExistedCsEnodrseCodeAddr" id="PA_isExistedCsEnodrseCode" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.csendorsecode.icsendorsecodeisexisteducc.isexistedcsendorsecode.ws.ICsEndorseCodeIsExistedUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.csendorsecode.icsendorsecodeisexisteducc.isexistedcsendorsecode.ws.CsEndorseCodeIsExistedUccWSImpl" id="PA_CsEndorseCodeIsExistedUccWSImplisExistedCsEndorseCode">
				<property name="csEndorseCodeIsExistedUcc" ref="PA_csEndorseCodeIsExistedUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!--柜面自助终端  红利信息查询(打印通知书类)    -->
	<jaxws:endpoint id="bonusInfoQuery" address="/IBonusInfoQueryUccbonusInfoQueryAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r06401003161.ibonusinfoqueryucc.bonusinfoquery.ws.IBonusInfoQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="BonusInfoQueryUccWSImplbonusInfoQuery" class="com.nci.tunan.qry.impl.peripheral.exports.r06401003161.ibonusinfoqueryucc.bonusinfoquery.ws.BonusInfoQueryUccWSImpl">
				<property name="ucc">
					<ref bean="IBonusInfoQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!-- 催缴通知书快速查询接口 -->
	<jaxws:endpoint id="isExistCallDocNo" address="/zb_ICallDocNoQuickSearchUccisExistCallDocNoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.calldocumentnoexist.icalldocnoquicksearchucc.isexistcalldocno.ws.ICallDocNoQuickSearchUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="CallDocNoQuickSearchUccisExistCallDocNo" class="com.nci.tunan.qry.impl.peripheral.exports.calldocumentnoexist.icalldocnoquicksearchucc.isexistcalldocno.ws.CallDocNoQuickSearchUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_ICallDocNoQuickSearchUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 加费通知单号快速查询 -->
	<jaxws:endpoint id="isExistAddPremDocNo" address="/zb_addPremDocNoQuickSearchUccisExistAddPremDocNoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.judgeaddpremdocnoexist.iaddpremdocnoquicksearchucc.isexistaddpremdocno.ws.IAddPremDocNoQuickSearchUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="AddPremDocNoQuickSearchUccWSImplisExistAddPremDocNo" class="com.nci.tunan.qry.impl.peripheral.exports.judgeaddpremdocnoexist.iaddpremdocnoquicksearchucc.isexistaddpremdocno.ws.AddPremDocNoQuickSearchUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_addPremDocNoQuickSearchUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 补费通知书快速查询接口 -->
	<jaxws:endpoint id="isExistDocNo" address="/zb_IDocNoQuickSearchUccisExistDocNoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.documentnoexist.idocnoquicksearchucc.isexistdocno.ws.IDocNoQuickSearchUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="DocNoQuickSearchUccWSImplisExistDocNo" class="com.nci.tunan.qry.impl.peripheral.exports.documentnoexist.idocnoquicksearchucc.isexistdocno.ws.DocNoQuickSearchUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_IDocNoQuickSearchUcc" />
				</property>
			</bean>
		</jaxws:implementor>	
	</jaxws:endpoint>	
	
	<!--是否有保单打印日期查询接口  -->
	<jaxws:endpoint id="queryPrintDateFlag" address="/IQueryProposalPrintDateFlagUccqueryPrintDateFlagAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501003276.iqueryproposalprintdateflagucc.queryprintdateflag.ws.IQueryProposalPrintDateFlagUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryProposalPrintDateFlagUccWSImplqueryPrintDateFlag" class="com.nci.tunan.qry.impl.peripheral.exports.r13501003276.iqueryproposalprintdateflagucc.queryprintdateflag.ws.QueryProposalPrintDateFlagUccWSImpl">
				<property name="ucc">
					<ref bean="IQueryProposalPrintDateFlagUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 中保信保单编码路由快查   -->
	<jaxws:endpoint id="QRY_JudgePolicySequenceNoQuery" address="/IJudgePolicySequenceNoExistUccPolicySequenceNoQueryQryAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.judgepolicysequencenoexist.ipolicysequencenoqueryucc.policysequencenoquery.ws.IPolicySequenceNoExistQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicySequenceNoQueryUccWSImplPolicySequenceNoQryQuery" class="com.nci.tunan.qry.impl.peripheral.exports.judgepolicysequencenoexist.ipolicysequencenoqueryucc.policysequencenoquery.ws.PolicySequenceNoExistQueryUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_PolicySequenceNoQueryUCCImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>	
	<!-- 专属养老新契约账户信息查询 -->
	<jaxws:endpoint address="/phoneCenterAccountUccQueryNbContractInvestAddr" id="QRY_quyerNbContractInvestInfo" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900578.iphonecenteraccountqueryucc.querynbcontractinvestinfo.ws.IQueryNbContractInvestInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r13501900578.iphonecenteraccountqueryucc.querynbcontractinvestinfo.ws.QueryNbContractInvestInfoUccWSImpl" id="QRY_QueryNbContractInvestInfo">
				<property name="phoneCenterAccountUcc" ref="QRY_PhoneCenterAccountUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 专属养老最新账户信息查询 -->
	<jaxws:endpoint address="/phoneCenterAccountUccQueryNewAccountInfoAddr" id="QRY_quyerNewAccountUcc" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900579.iphonecenteraccountqueryucc.querynewaccount.ws.IQueryNewAccountUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r13501900579.iphonecenteraccountqueryucc.querynewaccount.ws.QueryNewAccountUccWSImpl" id="QRY_QueryNewAccountUccWSImpl">
				<property name="phoneCenterAccountUcc" ref="QRY_PhoneCenterAccountUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 专属养老历史账户结算信息查询 -->
	<jaxws:endpoint address="/phoneCenterAccountUccQueryHisAccountInfoAddr" id="QRY_queryHisAccountUcc" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900580.iphonecenteraccountqueryucc.queryhisaccount.ws.IQueryHisAccountUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r13501900580.iphonecenteraccountqueryucc.queryHisAccount.ws.QueryHisAccountUccWSImpl" id="QRY_QueryHisAccountUccWSImpl">
				<property name="phoneCenterAccountUcc" ref="QRY_PhoneCenterAccountUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 专属养老投资组合转入转出记录查询 -->
	<jaxws:endpoint address="/phoneCenterAccountUccQueryRollInOutInfoAddr" id="QRY_queryRollInOutInfoUcc" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900581.iphonecenteraccountqueryucc.queryrollinoutinfo.ws.IQueryRollInOutInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r13501900581.iphonecenteraccountqueryucc.queryRollInOutInfo.ws.QueryRollInOutInfoUccWSImpl" id="QRY_QueryRollInOutInfoUccWSImpl">
				<property name="phoneCenterAccountUcc" ref="QRY_PhoneCenterAccountUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 核保通知书查询接口 -->
	<jaxws:endpoint address="/queryUwDocumentList" id="QRY_queryUwDocumentListUcc" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.queryuwdocument.icsuwdocumentlistquyerucc.queryuwdocumentlist.ws.ICsUwDocumentListQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.queryuwdocument.icsuwdocumentlistquyerucc.queryuwdocumentlist.ws.CsUwDocumentListQueryUccWSImpl" id="QRY_queryUwDocumentListUccWSImpl">
				<property name="ucc" ref="QRY_queryUwDocumentUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 次标通知书查询接口 -->
	<jaxws:endpoint address="/queryUwSubStandardDocumentList" id="QRY_queryUwSubStandDocumentListUcc" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.querycbdocument.icsuwsubstandardmenqueryucc.querysubstandarddocument.ws.ICsUwSubStandardDocumentQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.querycbdocument.icsuwsubstandarddocumentucc.querycbdocument.ws.CsUwSubStandardDocumentQueryUccWSImpl" id="QRY_queryUwSubStandDocumentListUccWSImpl">
				<property name="ucc" ref="QRY_queryUwDocumentUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryTodoDocument" address="/QRY_queryTodoDocumentUccqueryTodoDocumentAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.ur20220301003503.iquerytododocumentucc.querytododocument.ws.IQueryTodoDocumentUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryTodoDocumentUccWSImplqueryTodoDocument" class="com.nci.tunan.qry.impl.peripheral.exports.ur20220301003503.iquerytododocumentucc.querytododocument.ws.QueryTodoDocumentUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_queryTodoDocumentUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 待回执签收查询接口 -->
	<jaxws:endpoint id="queryAckSign" address="/QRY_queryAckSignUccqueryAckSignAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.ur20220301003504.iqueryacksignucc.queryacksign.ws.IQueryAckSignUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryAckSignUccWSImplqueryAckSign" class="com.nci.tunan.qry.impl.peripheral.exports.ur20220301003504.iqueryacksignucc.queryacksign.ws.QueryAckSignUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_queryAckSignUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>	
	<!-- 关联保单信息查询接口-共同参保保单查询(rm146696)  -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryRelationPolicyInfo" address="/QRY_relationPolicyInfoUccqueryRelationPolicyInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900916.irelationpolicyinfoucc.queryrelationpolicyinfo.ws.IRelationPolicyInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="RelationPolicyInfoUccWSImplqueryRelationPolicyInfo" class="com.nci.tunan.qry.impl.peripheral.exports.r13501900916.irelationpolicyinfoucc.queryrelationpolicyinfo.ws.RelationPolicyInfoUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_relationPolicyInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>

	<!--根据投保单号 查询个险所有保单下对应的保单状态接口`-->
	<jaxws:endpoint id="queryPolicyStatus" address="/QRY_PolicyQueryStatusUccAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900950.ipolicyquerystatusucc.querypolicystatus.ws.IPolicyQueryStatusUccWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryQRYPolicyStatusUccWSImpl" class="com.nci.tunan.qry.impl.peripheral.exports.r13501900950.ipolicyquerystatusucc.querypolicystatus.ws.PolicyQueryStatusUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_queryPolicyStatusUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 保险合同打印信息查询接口 -->
	<jaxws:endpoint id="queryPolicyPrintInfo" address="/QRY_queryPolicyPrintInfoUccqueryPolicyPrintInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.querypolicyprintinfo.iquerypolicyprintinfoucc.querypolicyprintinfo.ws.IQueryPolicyPrintInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyPrintInfoUccWSImplqueryPolicyPrintInfo" class="com.nci.tunan.qry.impl.peripheral.exports.querypolicyprintinfo.iquerypolicyprintinfoucc.querypolicyprintinfo.ws.QueryPolicyPrintInfoUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_queryPolicyPrintInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<!-- 银代业务客户资料采集优化-保单详情查询   -->
	<jaxws:endpoint id="queryPolicyDetailsList" address="/QRY_queryPolicyDetailsUccImplqueryPolicyDetailsListAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900868.iquerypolicydetailsucc.querypolicydetailslist.ws.IQueryPolicyDetailsUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyDetailsUccWSImplqueryPolicyDetailsList" class="com.nci.tunan.qry.impl.peripheral.exports.r13501900868.iquerypolicydetailsucc.querypolicydetailslist.ws.QueryPolicyDetailsUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_queryPolicyDetailsUccImpl" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 133925-保单列表查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicy" address="/QRY_wxPolicyQueryUccqueryPolicyAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900869.iwxpolicyqueryucc.querypolicy.ws.IWXPolicyQueryUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="WXPolicyQueryUccWSImplqueryPolicy" class="com.nci.tunan.qry.impl.peripheral.exports.r13501900869.iwxpolicyqueryucc.querypolicy.ws.WXPolicyQueryUccWSImpl">
				<property name="ucc">
					<ref bean="QRY_wxPolicyQueryUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
</beans>