<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 体检资料查询 -->
	 <bean id="iNbPhysicalEtionInformationUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06501001906.impl.NbPhysicalEtionInformationUccImpl">
		<property name="nbPhysicalEtionInformationService" ref="nbPhysicalEtionInformationService"></property>
	</bean>	 
	<!-- 已承保保单查询 -->
	 <bean id="PaGuaranteeQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001858.impl.PaGuaranteeQueryUccImpl">
		<property name="paGuaranteeQueryService" ref="paGuaranteeQueryService"></property>
		<property name="nbPhysicalEtionInformationService" ref="nbPhysicalEtionInformationService"></property>
	</bean>	 
	
	<!--影像资料查询-->
    <bean  id="videoInfoQueryUccImpl" class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001839.impl.PaVideoInfoQueryUccImpl">	
	     <property name="videoInfoQueryService" ref="videoInfoQueryService"></property>	
	</bean>
	<!--影像资料下载-->
    <bean  id="videoInfoDownUccImpl" class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001840.impl.PaVideoInfoDownUccImpl">	
	     <property name="videoInfoDownService" ref="videoInfoDownService"></property>	
	</bean>

	
	<!-- 再保险 操作履历下 初审详细查询 -->
	<bean id="preAuditInfoQueryUcc"
		class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001886.impl.PAPreAuditInfoQueryUccImpl">
		<property name="paPreAuditInfoQueryService" ref="preAuditInfoQueryService"/>
	</bean>

	<!-- add by yangjx 理赔核保照会下加费应收信息查询 -->
	<bean id="claimQueryUwFeeInfoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06701001914.impl.ClaimQueryUwFeeInfoUccImpl">
		<property name="claimQueryUwFeeInfoService" ref="claimQueryUwFeeInfoService"></property>
	</bean>

	<!--by zhaoyoan_wb 万能险账户当前信息查询 -->
	<bean id="universalAccountInfoQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000031.impl.UniversalAccountInfoQueryUccImpl">
		<property name="universalAccountInfoQueryService" ref="universalAccountInfoQueryService"></property>
	</bean>
	
	<!-- 保单明细下险种信息（新核心） -->
	<bean id="perContractBusiProdInfoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001810.impl.PERContractBusiProdInfoUccImpl">
		<property name="perContractBusiProdInfoService" ref="perContractBusiProdInfoService"></property>
	</bean>
	
	<!--by chenaq 操作履历下核保通知书查询 -->
	<bean id="recordDocumentQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06501001904.impl.NbRecordDocumentQueryUccImpl">
	    <property name="recordDocumentQueryService" ref="recordDocumentQueryService"></property>
	</bean>
	
	<!--by chenaq 保全核保照会下体检结果查询 -->
	<bean id="presentPhysicalQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001898.impl.UwPresentPhysicalQueryUccImpl">
	    <property name="presentPhysicalQueryService" ref="presentPhysicalQueryService"></property>
	</bean>
	
	<!--操作履历查询 -->
	<bean id="OperatingHistoryQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001888.impl.OperatingHistoryQueryUccImpl">
		<property name="OperatingHistoryQueryService" ref="OperatingHistoryQueryService"></property>
	</bean>
	<!-- 保单明细查询 -->
	
	<bean id="iPaDetailQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001837.impl.PaDetailQueryUccImpl">
		<property name="iPaDetailQueryService" ref="iPaDetailQueryService"></property>
	
	</bean>
	<!--保单领取查询 -->
	<bean id="policyReceiveUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000871.impl.PolicyReceiveUccImpl">
		<property name="policyReceiveService" ref="policyReceiveService"></property>
	</bean>
	<!-- 保单红利查询 -->
     <bean id="policyBonusQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000869.impl.PolicyBonusQueryUccImpl">
      <property name="policyBonusService" ref="policyBonusService"></property> 
     </bean>
     
     <!-- 订单查询接口-->
	<bean id="orderInfoQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000749.impl.OrderInfoQueryUccImpl"> 
	 	<property name="orderInfoQueryService" ref="orderInfoQueryService"></property>
	</bean>
	
	<!-- 保全体检结果查询 -->
	<bean id="csMedicalResultQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06801001814.impl.CsMedicalResultQueryUccImpl">
	    <property name="csMedicalResultQueryService" ref="csMedicalResultQueryService"></property>
	</bean>
	<!-- 核保通知书 -->
	
	<bean id="IQueryUwNoticeUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000885.impl.QueryUwNoticeUccImpl">
	 <property name="iQueryUwNoticeService" ref="iQueryUwNoticeService"></property>
	</bean>
	
	<!-- 体检结果查询 -->
	<bean id="physicalResultQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06801001921.impl.PhysicalResultQueryUccImpl">
	   <property name="physicalResultQueryService" ref="physicalResultQueryService"></property>
	</bean>
	<!-- 南京医保交费信息查询 -->
	<bean id="medicalInsuranceFeeInfoQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002257.impl.NJMedicalInsuranceFeeInfoQueryUccImpl">
	   <property name="medicalInsuranceFeeInfoQueryService" ref="medicalInsuranceFeeInfoQueryService"></property>
	</bean>
	<!-- 产品是否可续投保-->
	<bean id="queryIfPolicyRenew" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101001019.impl.PolicyRenewQueryUccImpl">
	
	<property name="iPolicyRenewQueryService" ref="iPolicyRenewQueryService"></property>
	</bean>
	
	
	<!--临分查询接口 -->
	<bean id="LinFenQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002447.impl.LinFenQueryUccImpl">
	   <property name="service" ref="LinFenQueryService"></property>
	</bean>
	
	<!--临分计算数据接口 -->
	<bean id="linfenCalcDataInfomationUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06401002582.impl.LinfenCalcDataInfomationUccImpl">
	   <property name="linfenCalcDataInfomationService" ref="linfenCalcDataInfomationService"></property>
	</bean>
	
	<!--临分分页计算接口 -->
	<bean id="linFenPageQueryDataUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06401002583.impl.LinFenPageQueryDataUccImpl">
	   <property name="fenPageQueryDataService" ref="linFenPageQueryDataService"></property>
	</bean>
	
	<!--保单查询接口-按投保单号或保单号查询 -->
	<bean id="policyQueryInfoByAppOrPolicyCodeUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101001334.impl.PolicyQueryInfoByAppOrPolicyCodeUccImpl">
	   <property name="policyQueryInfoByAppOrPolicyCodeService" ref="policyQueryInfoByAppOrPolicyCodeService"></property>
	</bean>
	
	<!--保单查询接口-按身份证号查询-->
	<bean id="policyQueryInfoByIdNoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101001336.impl.PolicyQueryInfoByIdNoUccImpl">
	   <property name="policyQueryInfoByIdNoService" ref="policyQueryInfoByIdNoService"></property>
	   <property name="qryContractMasterDao" ref="contractMasterDao"></property>
	</bean>
	
	<!-- 追加保费查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000988.impl.AddPremQueryUccImpl" id="PA_addPremQueryUcc">
		<property name="addPremQueryService" ref="PA_addPremQueryService"/>
	</bean>
	
	<!-- 工行纸质保单补充接口 -->	
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000984.impl.PaperSupplementUccImpl" id="QRY_NB_IPaperSupplementUcc">
		 <property name="paperSupplementService" ref="QRY_NB_paperSupplementService"/> 
	</bean>
	
	<!-- 网站E化单证防伪验证码验真接口 -->	
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101001332.impl.WebEDocumentPseudoVerifiUccImpl" id="QRY_WebEDocumentPseudoVerifiUcc">
		 <property name="webEDocumentPseudoVerifiService" ref="QRY_webEDocumentPseudoVerifiService"/> 
	</bean>
	<!-- 网站法人E化单证验真接口 -->	
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101001380.impl.WebCorpEDocumentVerifiUccImpl" id="QRY_webCorpEDocumentVerifiUcc">
		 <property name="webCorpEDocumentVerifiService" ref="QRY_webCorpEDocumentVerifiService"/> 
	</bean>
	
	<!-- 保费项信息接口 -->
	<bean id="QRY_queryPremiumsInfoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002693.impl.QueryPremiumsInforUccImpl" >
	    <property name="queryPremiumsInfoService" ref="QRY_queryPremiumsInfoService"/>
	</bean>
	
	<!-- 保单险种表信息接口 -->
	<bean id="QRY_queryContBusiProdUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002675.impl.QueryContBusiProdUcc" >
	    <property name="queryContBusiProdService" ref="QRY_queryContBusiProdService"/>
	</bean>
	
	<!-- 险种责任领取项接口  -->
	<bean id="QRY_lpPremItemUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002691.impl.LpPremItemUccImpl" >
	    <property name="lpPremItemService" ref="QRY_lpPremItemService"/>
	</bean>
	<!--  操作履历下核保查询-->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06501001902.impl.ResumeQueryUccImpl" id="NB_ResumeQueryUcc">
		  <property name="resumeQueryService" ref="NB_resumeQueryService"/>
	</bean>
	<!-- 操作履历下初审基础信息查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06501001905.impl.ResumeFirstBaseQueryUccImpl" id="NB_resumeFirstBaseQueryUcc">
		<property name="resumeFirstBaseQueryService" ref="NB_resumeFirstBaseQueryService"/>
	</bean>	
	<!--特约查询-->
    <bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001861.impl.QueryPolicyConditionUCCImpl" id="PA_queryPolicyConditionUCC">	
	     <property name="queryPolicyConditionService" ref="PA_queryPolicyConditionService"/>	
	</bean>

	<!-- 再保险 受益人查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001871.impl.ZBBenefitInfoQueryUccImpl" id="PA_zbBenefitInfoQueryUcc">
	<property name="zbBenefitInfoQueryService" ref="PA_zbBenefitInfoQueryService"/>
	</bean>
    <!-- 险种责任接口 -->
	<bean id="PA_busiItemDutyUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002681.impl.BusiItemDutyUccImpl" >
	    <property name="busiItemDutyService" ref="PA_busiItemDutyService"/>
	</bean>
	<!-- 以下为移动接口 -->
	 <!-- R06601001889 -->
    <bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001889.impl.QueryUWHistoryUccImpl" id="UW_IQueryUWHistoryUcc">
    	<property name="iQueryUWHistoryService" ref="UW_iQueryUWHistoryService"/>
    </bean>
     <!-- R06601001890 -->
    <bean class="com.nci.tunan.qry.impl.peripheral.ucc.R06601001890.impl.QueryBusiProdUWDecisionUccImpl" id="UW_IQueryBusiProdUWDecisionUcc">
    	<property name="iQueryBusiProdUWDecisionService" ref="UW_iQueryBusiProdUWDecisionService"/>
    </bean>
    <!--by zhaoyoan_wb 操作履历下再保回复查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001891.impl.ReinsuranceReplyQueryUccImpl" id="UW_reinsuranceReplyQueryUcc">
	    <property name="reinsuranceReplyQueryService" ref="UW_reinsuranceReplyQueryService"/>
	</bean>
	
	<!-- 操作履历下客户合并通知书查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001892.impl.NbCustomerMergeNoticeQueryUccImpl" id="NB_nbCustomerMergeNoticeQueryUcc">
		<property name="nbCustomerMergeNoticeQueryService" ref="NB_nbCustomerMergeNoticeQueryService"/>
	</bean>	
     <!-- 个单被保人信息接口 -->
	<bean id="PA_contInsuredInfoQueryForRIUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002709.impl.ContInsuredInfoQueryForRIUccImpl" >
	    <property name="contInsuredInfoQueryForRIService" ref="PA_contInsuredInfoQueryForRIService"/>
	</bean>	
	<!--by zhaoyoan_wb 操作履历下复议答复 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001893.impl.ReviewReplyQueryUccImpl" id="UW_reviewReplyQueryUcc">
	    <property name="reviewReplyQueryService" ref="UW_reviewReplyQueryService"/>
	</bean>
	<!--by zhaoyoan_wb 操作履历下自核提示查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001895.impl.AutoUWPointQueryUccImpl" id="UW_autoUWPointQueryUcc">
	    <property name="autoUWPointQueryService" ref="UW_autoUWPointQueryService"/>
	</bean>
		<!-- 连带被保险人信息接口 -->
	<bean id="PA_jointInsurerInfUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002683.impl.JointInsurerInfUccImpl" >
	    <property name="jointInsurerInfService" ref="PA_jointInsurerInfService"/>
	</bean>
	<!--by zhaoyoan_wb 操作履历下无需体检生调原因查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001896.impl.NoPhysicalReasonQueryUccImpl" id="UW_noPhysicalReasonQueryUcc">
	    <property name="noPhysicalReasonQueryService" ref="UW_noPhysicalReasonQueryService"/>
	</bean>
	
	<!-- 操作履历下问题件查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06501001903.impl.ProblemPiecesQueryUccImpl" id="NB_problemPiecesQueryUcc">
		<property name="problemPiecesQueryService" ref="NB_problemPiecesQueryService"/>
	</bean>
	<!-- 再保险 影响查询-->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001853.impl.NoteInfoQueryUccImpl" id="PA_noteInfoQueryUcc">
		<property name="noteInfoQueryService" ref="PA_noteInfoQueryService"/>
	</bean>

	<!-- R06601001897 合同核保轨迹查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06601001897.impl.ContUwTraceUccQueryImpl" id="UW_uwContUwTraceQueryUcc">
		<property name="contUwTraceQueryService" ref="UW_contUwTraceQueryService"/>
	</bean>
	<!--操作履历下生调结果查询（新核心）-->
    <bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06401001842.impl.SurveyResultQueryUccImpl" id="PA_surveyResultQueryUccImpl">	
	     <property name="surveyResultQueryService" ref="PA_surveyResultQueryService"/>	
	</bean>
	
	<!-- 网站在线自助报案客户校验接口-->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002191.impl.WebOnlineSelfServiceReportCheckUccImpl" id="CLM_webOnlineSelfServiceReportCheckUcc">
	<property name="webOnlineSelfServiceReportCheckService" ref="CLM_webOnlineSelfServiceReportCheckService"/>
	</bean>
	
	<!-- 保全核保照会查询-->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r06801001924.impl.CsUWconfirmQueryUCCImpl" id="PA_csUWconfirmQueryUCC">
		<property name="CsUWconfirmQueryService" ref="PA_CsUWconfirmQueryService"/>
	</bean>
	
	<!-- 出险人保单查询接口 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000061.impl.CusContInfoQueryUccImpl" id="PA_cusContInfoQueryUcc">
		<property name="cusContInfoQueryService" ref="PA_cusContInfoQueryService"/>
	</bean>
	
		<!-- 核心保单验真接口 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101001688.impl.PolicyDocumentPseudoVerifiUccImpl" id="PA_policyDocumentPseudoVerifiUccImpl">
	    <property name="policyDocumentPseudoVerifiService" ref="PA_policyDocumentPseudoVerifiService"></property>
	</bean>
	
	<!-- 电话中心保单信息查询  -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002246.impl.TelephoneCenterQueryPolicyInfoImpl" id="PA_telephoneCenterQueryPolicyInfoUcc">
		<property name="telephoneCenterQueryPolicyInfoService" ref="PA_telephoneCenterQueryPolicyInfoService"/>
	</bean>

    <!-- 客户保单查询  -->
	<bean id="PA_customerPolicyInfoUccImpl" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002633.impl.CustomerPolicyInfoUccImpl" >
	    <property name="customerPolicyInfoService" ref="PA_customerPolicyInfoService"/>
	</bean>
	<!-- 增加老客户校验 -->
	<!-- 健康无忧（尊享版）老客户校验接口 -->
	 <bean id="PA_OldCustomerVerifyUCC" class="com.nci.tunan.qry.impl.peripheral.ucc.r00101002797.impl.OldCustomerVerifyUCCImpl">
	     <property name="oldCustomerVerifyService" ref="PA_oldCustomerVerifyService"></property> 
	 </bean>
	
	<!-- 理赔列表信息查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r00101000045.impl.QueryClaimListInfoUccImpl" id="QRY_iQueryClaimListInfoUcc">
		<property name="iQueryClaimListInfoService" ref="QRY_iQueryClaimListInfoService"/>
	</bean>
	<!-- 订单号快速查询-->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.ordernoquery.impl.OrderNoQueryUccImpl" id="PA_orderNoQueryUcc">
		<property name="orderNoQueryService" ref="PA_orderNoQueryService"/>
	</bean>
	<!-- 加费通知单号快速查询 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.judgeaddpremdocnoexist.impl.AddPremDocNoQuickSearchUccImpl" id="QRY_addPremDocNoQuickSearchUcc">
		<property name="addPremDocNoQuickSearchService" ref="QRY_addPremDocNoQuickSearchService"/>
	</bean>
	
	<!-- 交退费编号快速查询是否存在服务 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.judgeunitnumberexist.impl.JudgeUnitNumberExistUccImpl" id="QRY_judgeUnitNumberExistUcc">	
	     <property name="judgeUnitNumberExistService" ref="QRY_judgeUnitNumberExistService"/>	
	</bean>
	
	<!--投保单号快速查询是否存在服务 -->
	<bean class="com.nci.tunan.qry.impl.judgeapplycodeexit.ucc.impl.JudgeApplyCodeExitUccImpl" id="NB_judgeApplyCodeExitUcc">	
	     <property name="judgeApplyCodeExitService" ref="NB_judgeApplyCodeExitService"/>	
	</bean>

	
		<!-- 提调号快查服务 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.judgesecuritynoexist.impl.JudgeSecurityNoExistUccImpl" id="QRY_judgeSericurityNoExistUcc">
		<property name="securityNoExistService" ref="QRY_securityNoExistService"/>
	</bean>
	
	 <!-- ESB服务 判断保单号是否存在 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.judgepolicyexist.impl.JudgePolicyExistUccImpl" id="QRY_judgePolicyExistUCC">
	     <property name="judgePolicyExistService" ref="QRY_judgePolicyExistService"/>
	</bean>
	
	<!-- 保全受理号码快速查询是否存在服务 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.cspolicyaccept.impl.CsPolicyAcceptIsExistedUccImpl" id="QRY_csPolicyAcceptIsExistedUcc">
		<property name="csPolicyAcceptIsExistedService" ref="QRY_csPolicyAcceptIsExistedService"/>
	</bean>
	
	

		<!-- 赔案号快速查询是否存在服务 -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.casenoisexistserv.impl.CasenoIsExistServUCCImpl" id="CLM_casenoIsExistServUCC">
		<property name="casenoIsExistServService" ref="CLM_casenoIsExistServService"/>
	</bean>
	
		<!-- 核保通知书号快速查询接口 -->
	<bean id="QRY_judgePrtSeqExistUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.judgeprtseqexist.impl.JudgePrtSeqExistUccImpl">
	 <property name="judgePrtSeqExistService" ref="QRY_judgePrtSeqExistService"></property>
	</bean>
	
		<!-- 保全批单号快查 -->
	<bean id="PA_csEndorseCodeIsExistedUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.csendorsecode.impl.CsEndorseCodeIsExistedUccImpl">
		<property name="csEndorseCodeIsExistedService" ref="PA_csEndorseCodeIsExistedService"></property>
	</bean>
		 <!--柜面自助终端  红利信息查询(打印通知书类)-->
	 <bean id="IBonusInfoQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r06401003161.impl.BonusInfoQueryUccImpl">
	      <property name="bonusInfoQueryService" ref="PA_IBonusInfoQueryService"></property>
	 </bean>
	<!-- 催缴通知书快速查询接口 -->
	 <bean id="QRY_ICallDocNoQuickSearchUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.calldocumentnoexist.impl.CallDocNoQuickSearchUccImpl">
	    <property name="callDocNoQuickSearchService" ref="QRY_ICallDocNoQuickSearchService"></property>
	 </bean>
	 <!-- 补费通知书快速查询接口 -->
	 <bean id="QRY_IDocNoQuickSearchUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.documentnoexist.impl.DocNoQuickSearchUccImpl">
	    <property name="docNoQuickSearchService" ref="QRY_IDocNoQuickSearchService"></property>
	 </bean>
	 
	 <!--是否生成保单打印日期查询接口  -->
	<bean class="com.nci.tunan.qry.impl.peripheral.ucc.r13501003276.impl.QueryProposalPrintDateFlagUccImpl" id="IQueryProposalPrintDateFlagUcc">
		<property name="queryProposalPrintDateFlagService" ref="QRY_queryProposalPrintDateFlagService"/>
	</bean>
	
	<!--中保信保单编码路由快查灾备  -->
    <bean class="com.nci.tunan.qry.impl.peripheral.ucc.judgePolicySequenceNo.impl.PolicySequenceNoQueryUccImpl" id="QRY_PolicySequenceNoQueryUCCImpl">
        <property name="queryService" ref="QRY_PolicySequenceNoQueryServiceImpl"/>
    </bean>
	
	<!-- 查询待处理的通知书 -->  
	<bean id="QRY_queryTodoDocumentUcc"
		class="com.nci.tunan.qry.impl.peripheral.ucc.ur20220301003503.impl.QueryTodoDocumentUccImpl">
		<property name="iQueryUwNoticeService" ref="iQueryUwNoticeService"/>
	</bean>
    <!-- 查询待回执保单 -->
	<bean id="QRY_queryAckSignUcc"
		class="com.nci.tunan.qry.impl.peripheral.ucc.ur20220301003504.impl.QueryAckSignUccImpl">
		<property name="iQueryUwNoticeService" ref="iQueryUwNoticeService"/>
	</bean>
	 <!-- 关联保单信息查询接口-共同参保保单查询(rm146696) -->
    <bean id="QRY_relationPolicyInfoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r13501900916.impl.RelationPolicyInfoUccImpl">
	    <property name="relationPolicyInfoService" ref="qry_RelationPolicyInfoService"/>
    </bean>
 <!-- #164727-续保审核结论通知书列表查询接口 -->
    <bean id="QRY_QueryReDecAuditingDocNoUccImpl" class="com.nci.tunan.qry.impl.peripheral.ucc.ReDecAuditingDocNo.impl.QueryReDecAuditingDocNoUccImpl">
       <property name="iReDecAuditingDocNoService" ref="qry_ReDecAuditingDocNoServiceImpl"/>
    </bean>
    <!--保单生存给付计划信息查询接口-->
	<bean id="QRY_queryPayPlanInfoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r13501900956.impl.QueryPayPlanInfoUccImpl">
	</bean>
	<!--保单生存给付明细信息查询-->
	<bean id="QRY_queryPayPlanDetailInfoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r13501900957.impl.QueryPayPlanDetailInfoUccImpl">
	</bean>

	<!--生存金满期金保单列表查询接口Ucc-->
	<bean id="QRY_queryPolicyInfosOfSurvivalPaymentUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r13501901026.impl.QueryPolicyInfosOfSurvivalPaymentUccImpl">
	</bean>

	<!--查询保单状态`-->
	<bean id="QRY_queryPolicyStatusUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r13501900950.impl.PolicyQueryStatusUccImpl">
		<property name="qryContractMasterDao" ref="contractMasterDao"></property>
	</bean>
	
	<!-- 查询保险合同打印信息 -->
	<bean id="QRY_queryPolicyPrintInfoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.querypolicyprintinfo.impl.QueryPolicyPrintInfoUccImpl">
	   <property name = "contractMasterDao" ref="uwContractMasterDao"></property>
	   <property name = "policyPrintQueryDao" ref="QRY_policyPrintQueryDao"></property>
	</bean>
	
	<!--133925-保单详情查询  -->
    <bean id="QRY_queryPolicyDetailsUccImpl" class="com.nci.tunan.qry.impl.peripheral.ucc.r13501900868.impl.QueryPolicyDetailsUccImpl">
        <property name="queryPolicyDetailsService" ref="qry_queryPolicyDetailsServiceImpl"/>
        <property name="qryPolicyHolderDao" ref="QRY_policyHolderDao"/>
    	<property name="qryCustomerDao" ref="QRY_customerDao"/>
    	<property name="qryInsuredListDao" ref="QRY_insuredListDao"/>
    	<property name="contractBeneDao" ref="PA_contractBeneDao"/>
    	<property name="nbContractAgentDao" ref="NB_contractAgentDAO"/>
    </bean>
    <!-- 133925-保单列表查询接口 -->
    <bean id="QRY_wxPolicyQueryUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r13501900869.impl.WXPolicyQueryUccImpl">
       <property name="wxPolicyQueryService" ref="qry_WXPolicyQueryService"/>
    </bean>
    
    <!--保费明细记录查询接口-->
	<bean id="QRY_queryPolicyPremDetailInfoUcc" class="com.nci.tunan.qry.impl.peripheral.ucc.r13501901043.impl.QueryPolicyPremDetailInfoUccImpl">
	</bean>
	<!-- #192831接口需求_官微医药无忧（A04）险种保单相关信息查询接口申请（新核心）-->
    <bean id="qry_RiskAgreementInformationUccImpl" class="com.nci.tunan.qry.impl.peripheral.ucc.re192831.impl.RiskAgreementInformationUccImpl"  scope="prototype">
     <property name="riskAgreementInformationUccImplService" ref="QRY_RiskAgreementInformationServiceImpl"></property>
    </bean>
</beans>