<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.impl.QueryPolicyInfoNbServiceImpl"
		id="JRQD_QueryPolicyInfoNbService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.impl.QueryPolicyInfoPasServiceImpl"
		id="JRQD_QueryPolicyInfoPasService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.clm.impl.ClaimCommonQueryServiceImpl"
		id="JRQD_CLM_ClaimCommonQueryService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.clm.impl.MobileReportCheckServiceImpl"
		id="JRQD_CLM_MobileReportCheckService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.BaseDaoDepInService"
		id="JRQD_CS_BaseDaoDepInService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CLMServiceImpl"
		id="JRQD_CS_CLMService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CodeTableServiceImpl"
		id="JRQD_CS_CodeTableService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CSCalculateCashValueServiceImpl"
		id="JRQD_CS_CSCalculateCashValueService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CsEndorseCTCalculateServiceImpl"
		id="JRQD_CS_CsEndorseCTCalculateService" parent="JRQD_CS_CSItemBaseService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CsEndorseCTSaveServiceImpl"
		id="JRQD_CS_CsEndorseCTSaveService" parent="JRQD_CS_CSItemBaseService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CsEndorseCTServiceImpl"
		id="JRQD_CS_CsEndorseCTService" parent="JRQD_CS_CSItemBaseService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CsGeneralInfoServiceImpl"
		id="JRQD_CS_CsGeneralInfoService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CSItemBaseServiceImpl"
		id="JRQD_CS_CSItemBaseService" parent="JRQD_CS_BaseDaoDepInService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.CsMainStreamServiceImpl"
		id="JRQD_CS_CsMainStreamService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.cs.impl.PASImportsAdapterServiceImpl"
		id="JRQD_CS_PASImportsAdapterService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.CalcOptBonusSAServiceImpl"
		id="JRQD_PA_CalcOptBonusSAService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.CalcSAMServiceImpl"
		id="JRQD_PA_CalcSAMService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.CalculateCashValueServiceImpl"
		id="JRQD_PA_CalculateCashValueService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.CalculateIlpuDueFee"
		id="JRQD_PA_CalculateIlpuDueFee" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.CalculateSAMServiceImpl"
		id="JRQD_PA_CalculateSAMService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.CommonTool"
		id="JRQD_PA_CommonTool" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.ContinueBonusDServiceImpl"
		id="JRQD_PA_ContinueBonusDService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.FormulaServiceImpl"
		id="JRQD_PA_FormulaService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.PRDServiceImpl"
		id="JRQD_PA_PRDService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.QueryCalcParamImpl"
		id="JRQD_PA_QueryCalcParam" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.QueryCashValueListServiceImpl"
		id="JRQD_PA_QueryCashValueListService" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.service.pa.impl.UniversalSettlementServiceImpl"
		id="JRQD_PA_UniversalSettlementService" />

</beans>