<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cap.impl.CapQueryPayedPremDaoImpl"
		id="JRQD_CAP_CapQueryPayedPremDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.clm.impl.ClaimBaseDaoImpl"
		id="JRQD_CLM_ClaimBaseDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.clm.impl.ClaimCommonQueryDaoImpl"
		id="JRQD_CLM_ClaimCommonQueryDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.clm.impl.ClaimLiabDaoImpl"
		id="JRQD_CLM_ClaimLiabDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.clm.impl.ClaimSubCaseDaoImpl"
		id="JRQD_CLM_ClaimSubCaseDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.clm.impl.ClaimUwDaoImpl"
		id="JRQD_CLM_ClaimUwDao" parent="baseDao" />
	<bean class="com.nci.tunan.qry.impl.jrqd.dao.clm.impl.CodeDaoImpl"
		id="JRQD_CLM_CodeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.clm.impl.MobileReportCheckDaoImpl"
		id="JRQD_CLM_MobileReportCheckDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.clm.impl.SurveyApplyDaoImpl"
		id="JRQD_CLM_SurveyApplyDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsAcceptChangeDaoImpl"
		id="JRQD_CS_CsAcceptChangeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsApplicationDaoImpl"
		id="JRQD_CS_CsApplicationDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsBenefitInsuredDaoImpl"
		id="JRQD_CS_CsBenefitInsuredDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsBusinessProductDaoImpl"
		id="JRQD_CS_CsBusinessProductDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsCalculateCashValueDaoImpl"
		id="JRQD_CS_CsCalculateCashValueDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsContractBusiProdDaoImpl"
		id="JRQD_CS_CsContractBusiProdDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsContractExtendDaoImpl"
		id="JRQD_CS_CsContractExtendDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsContractMasterDaoImpl"
		id="JRQD_CS_CsContractMasterDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsContractProductDaoImpl"
		id="JRQD_CS_CsContractProductDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsContractProductOtherDaoImpl"
		id="JRQD_CS_CsContractProductOtherDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsCountryDaoImpl"
		id="JRQD_CS_CsCountryDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsCustomerDaoImpl"
		id="JRQD_CS_CsCustomerDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsEntryDaoImpl"
		id="JRQD_CS_CsEntryDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsFundServiceDaoImpl"
		id="JRQD_CS_CsFundServiceDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsInsuredListDaoImpl"
		id="JRQD_CS_CsInsuredListDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsPayPlanDaoImpl"
		id="JRQD_CS_CsPayPlanDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsPolicyChangeDaoImpl"
		id="JRQD_CS_CsPolicyChangeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.CsPolicyHolderDaoImpl"
		id="JRQD_CS_CsPolicyHolderDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.JobCodeDaoImpl"
		id="JRQD_CS_JobCodeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.cs.impl.SurrenderDaoImpl"
		id="JRQD_CS_SurrenderDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.DrqResionDaoImpl"
		id="JRQD_NB_IDrqResionDao" parent="baseDao">
		<property name="sqlSessionTemplate" ref="sqlSessionTemplate" />
	</bean>
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.QtResultDaoImpl"
		id="JRQD_NB_IQtResultDao" parent="baseDao">
		<property name="sqlSessionTemplate" ref="sqlSessionTemplate" />
	</bean>
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.QtStatusDaoImpl"
		id="JRQD_NB_IQtStatusDao" parent="baseDao">
		<property name="sqlSessionTemplate" ref="sqlSessionTemplate" />
	</bean>
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.NbDrqInfoSendDaoImpl"
		id="JRQD_NB_NbDrqInfoSendDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.NbQtTaskDaoImpl"
		id="JRQD_NB_NbQtTaskDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.DrqNResultDaoImpl"
		id="JRQD_NB_DrqNResultDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.NbContractMasterDaoImpl"
		id="JRQD_NB_NbContractMasterDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.CustomerDaoImpl"
		id="JRQD_NB_CustomerDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.AddressDaoImpl"
		id="JRQD_NB_AddressDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.NbPolicyHolderDaoImpl"
		id="JRQD_NB_NbPolicyHolderDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.NbInsuredListDaoImpl"
		id="JRQD_NB_NbInsuredListDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.nb.impl.NbContractBeneDaoImpl"
		id="JRQD_NB_NbContractBeneDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.AddressDaoImpl"
		id="JRQD_PA_AddressDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.BenefitInsuredDaoImpl"
		id="JRQD_PA_BenefitInsuredDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.BonusAllocateDaoImpl"
		id="JRQD_PA_BonusAllocateDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.BonusAllocationDaoimpl"
		id="JRQD_PA_BonusAllocationDaoimpl" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.BusinessProductDaoImpl"
		id="JRQD_PA_BusinessProductDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.CalculateCashValueDaoImpl"
		id="JRQD_PA_CalculateCashValueDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContinueBonusDaoImpl"
		id="JRQD_PA_ContinueBonusDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractBeneDaoImpl"
		id="JRQD_PA_ContractBeneDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractBusiProdDaoImpl"
		id="JRQD_PA_ContractBusiProdDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractExtendDaoImpl"
		id="JRQD_PA_ContractExtendDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractInvestDaoImpl"
		id="JRQD_PA_ContractInvestDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractInvestRateDaoImpl"
		id="JRQD_PA_ContractInvestRateDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractMasterDaoImpl"
		id="JRQD_PA_ContractMasterDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractMasterLogDaoImpl"
		id="JRQD_PA_ContractMasterLogDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractProductDaoImpl"
		id="JRQD_PA_ContractProductDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ContractProductOtherDaoImpl"
		id="JRQD_PA_ContractProductOtherDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.CustomerDaoImpl"
		id="JRQD_PA_CustomerDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ExtraPremDaoImpl"
		id="JRQD_PA_ExtraPremDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.FormulaDaoImpl"
		id="JRQD_PA_FormulaDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.FundSettlementDaoImpl"
		id="JRQD_PA_FundSettlementDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.FundTransDaoImpl"
		id="JRQD_PA_FundTransDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ILPUChargeDeductionDaoImpl"
		id="JRQD_PA_ILPUChargeDeductionDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.InsuredListDaoImpl"
		id="JRQD_PA_InsuredListDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.InvestUnitPriceDaoImpl"
		id="JRQD_PA_InvestUnitPriceDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.LoanPaymentAdvanceDaoImpl"
		id="JRQD_PA_LoanPaymentAdvanceDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PayDueDaoImpl"
		id="JRQD_PA_PayDueDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PayPlanDaoImpl"
		id="JRQD_PA_PayPlanDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PolicyAccountDaoImpl"
		id="JRQD_PA_PolicyAccountDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PolicyAcknowledgementDaoImpl"
		id="JRQD_PA_PolicyAcknowledgementDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PolicyChangeDaoImpl"
		id="JRQD_PA_PolicyChangeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PolicyFundChargeDaoImpl"
		id="JRQD_PA_PolicyFundChargeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PolicyHolderDaoImpl"
		id="JRQD_PA_PolicyHolderDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PremArapDaoImpl"
		id="JRQD_PA_PremArapDao" parent="baseDao" />
	<bean class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.PremDaoImpl"
		id="JRQD_PA_PremDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ProductLiabilityChangeDaoImpl"
		id="JRQD_PA_ProductLiabilityChangeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.ProductLifeDaoImpl"
		id="JRQD_PA_ProductLifeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.QueryPolicyInfoDaoImpl"
		id="JRQD_PA_QueryPolicyInfoDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.SaChangeDaoImpl"
		id="JRQD_PA_SaChangeDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.SpecialBonusDaoImpl"
		id="JRQD_PA_SpecialBonusDao" parent="baseDao" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.dao.pa.impl.SurvialAnnuityMaturityDaoImpl"
		id="JRQD_PA_SurvialAnnuityMaturityDao" parent="baseDao" />

</beans>