<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:task="http://www.springframework.org/schema/task"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:c="http://www.springframework.org/schema/c"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<bean
		class="com.nci.tunan.qry.impl.jrqd.ucc.clm.impl.CsQueryInsuranceUCCImpl"
		id="JRQD_CLM_CsQueryInsuranceUCC" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.ucc.clm.impl.QueryClaimCaseTrackUCCImpl"
		id="JRQD_CLM_QueryClaimCaseTrackUCC" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.ucc.clm.impl.QueryClaimCaseUCCImpl"
		id="JRQD_CLM_QueryClaimCaseUCC" />
	<bean
		class="com.nci.tunan.qry.impl.jrqd.ucc.impl.QueryPolicyInfoUccImpl"
		id="JRQD_QueryPolicyInfoUcc" />

</beans>