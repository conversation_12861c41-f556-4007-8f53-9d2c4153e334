<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_nbInsuredListDaoImpl">

	<!-- 查询保单被保人列表条件 -->
	<sql id="JRQD_Nb_nbInsuredListWhereCondition">
		<if test=" policy_id  != null "><![CDATA[ AND t.POLICY_ID = #{policy_id} ]]></if>
	</sql>

	<!-- 查询保单被保人列表所有数据 -->
	<select id="JRQD_NB_findAllNbInsuredList"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT t.LIST_ID,
			       t.POLICY_CODE,
			       t.APPLY_CODE,
			       t.ORDER_ID,
			       t.ADDRESS_ID,
			       t.CUSTOMER_ID,
			       t.POLICY_ID,
			       t.RELATION_TO_PH,
			       t.CUSTOMER_HEIGHT,
			       t.CUSTOMER_WEIGHT,
			       t.JOB_CODE,
			       t.JOB_UNDERWRITE,
			       t.SOCI_SECU,
			       t.SMOKING,
			       t.RESIDENT_TYPE,
			       t.IS_INSURED_UPDATE,
			       /* t.AGENT_RELATION, */
			       /* t.ANNUAL_INCOME_CEIL, */
			       /* t.INCOME_SOURCE, */
			       /* t.RISK_PROBABILITY, */
			       t.INSERT_BY,
			       t.INSERT_TIME,
			       t.INSERT_TIMESTAMP,
			       t.UPDATE_BY,
			       t.UPDATE_TIME,
			       t.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_NB_INSURED_LIST t
			 WHERE 1 = 1
		]]>
		<include refid="JRQD_Nb_nbInsuredListWhereCondition" />
	</select>

</mapper> 
