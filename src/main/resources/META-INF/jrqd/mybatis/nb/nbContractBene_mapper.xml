<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_nbContractBeneDaoImpl">

	<!-- 查询保单受益人条件 -->
	<sql id="JRQD_Nb_nbContractBeneWhereCondition">
		<if test=" policy_id  != null "><![CDATA[ AND t.POLICY_ID = #{policy_id} ]]></if>
	</sql>

	<!-- 查询保单受益人表所有数据 -->
	<select id="JRQD_NB_findAllNbContractBene"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT t.BENE_ID,
			       t.POLICY_ID,
			       t.POLICY_CODE,
			       t.APPLY_CODE,
			       t.BUSI_ITEM_ID,
			       t.PRODUCT_CODE,
			       t.INSURED_ID,
			       t.BENE_TYPE,
			       t.SHARE_ORDER,
			       t.SHARE_RATE,
			       t.DESIGNATION,
			       t.ADDRESS_ID,
			       t.JOB_CODE,
			       t.CUSTOMER_ID,
			       t.CUSTOMER_NAME,
			       t.CUSTOMER_BIRTHDAY,
			       t.CUSTOMER_GENDER,
			       t.CUSTOMER_CERT_TYPE,
			       t.CUSTOMER_CERTI_CODE,
			       t.OLD_BNF_NO/* , */
			       /* t.AGENT_RELATION */
			  FROM APP___NB__DBUSER.T_NB_CONTRACT_BENE t
			 WHERE 1 = 1
		]]>
		<include refid="JRQD_Nb_nbContractBeneWhereCondition" />
	</select>

</mapper> 
