<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_customer">

	<!-- 质检失败结果查询条件 -->
	<sql id="JRQD_NB_customerWhereCondition">
		<if test=" customer_id  != null "><![CDATA[ AND t.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>

	<!-- 查询单条质检失败结果 -->
	<select id="JRQD_NB_findCustomerByCustomerId"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select t.CUSTOMER_ID,
			       t.UN_CUSTOMER_CODE,
			       t.OLD_CUSTOMER_ID,
			       t.MARRIAGE_DATE,
			       t.EDUCATION,
			       t.CUSTOMER_NAME,
			       t.CUSTOMER_BIRTHDAY,
			       t.CUSTOMER_GENDER,
			       t.CUSTOMER_HEIGHT,
			       t.CUSTOMER_WEIGHT,
			       t.CUSTOMER_CERT_TYPE,
			       t.CUSTOMER_CERTI_CODE,
			       t.CUSTOMER_ID_CODE,
			       t.CUST_CERT_STAR_DATE,
			       t.CUST_CERT_END_DATE,
			       t.JOB_CODE,
			       t.JOB_NATURE,
			       t.JOB_KIND,
			       t.JOB_TITLE,
			       t.MARRIAGE_STATUS,
			       t.IS_PARENT,
			       t.ANNUAL_INCOME,
			       t.COUNTRY_CODE,
			       t.RELIGION_CODE,
			       t.NATION_CODE,
			       t.DRIVER_LICENSE_TYPE,
			       t.COMM_METHOD,
			       t.COMPANY_NAME,
			       t.OFFEN_USE_TEL,
			       t.HOUSE_TEL,
			       t.FAX_TEL,
			       t.OFFICE_TEL,
			       t.MOBILE_TEL,
			       t.EMAIL,
			       t.QQ,
			       t.WECHAT_NO,
			       t.OTHER,
			       t.CUSTOMER_LEVEL,
			       t.CUSTOMER_RISK_LEVEL,
			       t.CUSTOMER_VIP,
			       t.SMOKING_FLAG,
			       t.DRUNK_FLAG,
			       t.BLACKLIST_FLAG,
			       t.HOUSEKEEPER_FLAG,
			       t.SYN_MDM_FLAG,
			       t.SOCI_SECU,
			       t.LIVE_STATUS,
			       t.RETIRED_FLAG,
			       t.DEATH_DATE,
			       t.HEALTH_STATUS,
			       t.REMARK,
			       t.CUST_PWD,
			       t.RESIDENT_TYPE,
			       t.IS_SUBSCRIPTION_EMAIL,
			       t.TAX_RESIDENT_TYPE,
			       t.NON_RESIDENT_FLAG,
			       t.INCOME_SOURCE,
			       t.ANNUAL_INCOME_CEIL,
			       t.RZ_LEVEL,
			       t.SECOND_CERT_TYPE,
			       t.SECOND_CERTI_CODE,
			       t.INSERT_BY,
			       t.INSERT_TIME,
			       t.INSERT_TIMESTAMP,
			       t.UPDATE_BY,
			       t.UPDATE_TIME,
			       t.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_CUSTOMER t
			 WHERE 1 = 1
		]]>
		<include refid="JRQD_NB_customerWhereCondition" />
	</select>

</mapper>
