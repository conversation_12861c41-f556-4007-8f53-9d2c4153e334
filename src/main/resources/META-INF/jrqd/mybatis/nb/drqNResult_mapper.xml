<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_drqNResult">

	<!-- 质检失败结果查询条件 -->
	<sql id="JRQD_NB_drqNResultWhereCondition">
		<if test=" TASK_ID  != null "><![CDATA[ AND t.TASK_ID = #{TASK_ID} ]]></if>
	</sql>

	<!-- 查询单条质检失败结果 -->
	<select id="JRQD_NB_QueryDrqNResultSingleRecord"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT * FROM (
			SELECT t.DRQ_ID,
			       t.TASK_ID,
			       t.APPLY_CODE,
			       t.RESION_CODE,
			       t.RESION_DESC,
			       t.BATCH_NO,
			       t.NOTICE_STATUS,
			       t.DOCUMENT_NO,
			       t.INSERT_BY,
			       t.INSERT_TIME,
			       t.INSERT_TIMESTAMP,
			       t.UPDATE_BY,
			       t.UPDATE_TIME,
			       t.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_DRQ_N_RESULT t
			 WHERE 1 = 1
		]]>
		<include refid="JRQD_NB_drqNResultWhereCondition" />
		<![CDATA[
		     ORDER BY t.DRQ_ID DESC
		 ) WHERE ROWNUM = 1
		]]>
	</select>

</mapper>
