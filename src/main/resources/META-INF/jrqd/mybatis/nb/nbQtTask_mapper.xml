<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_nbQtTask">

	<!-- 查询双录信息发送表最新记录 -->
	<select id="JRQD_NB_QueryLatestQtTask"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	      SELECT t.TASK_ID,
	             t.BATCH_ID,
	             t.QT_CRITERIA_ID,
	             t.POLICY_ID,
	             t.QT_STATUS,
	             t.QT_RESULT,
	             t.ENTRY_TYPE,
	             t.BPO_COMP,
	             t.BANK_CODE,
	             t.BANK_BRANCH_CODE,
	             t.ORGAN_CODE,
	             t.CHANNEL_TYPE,
	             t.CARD_CODE,
	             t.QA_TYPE,
	             t.APPLY_CODE,
	             t.POLICY_CODE,
	             t.CUSTOMER_LEVEL,
	             t.AGENT_LEVEL,
	             t.CREATE_TIME,
	             t.QT_USER,
	             t.QT_TIME,
	             t.QT_TOOLTIP,
	             t.QT_COMMENTS,
	             t.QT_ELEMENT_TOTAL,
	             t.QT_ERROR_COUNT,
	             t.FIELD_ERROR_RATE,
	             t.BILL_ERROR_RATE,
	             t.QT_HOLDER_INDI,
	             t.QT_INSURED_INDI,
	             t.QT_VERIFY_INDI,
	             t.RISK_DEFECT_INDI,
	             t.BATCH_NO,
	             t.BPO_CHECK_FLAG,
	             t.DOCUMENT_NO,
	             t.DOCU_FLAG,
	             t.IS_ART,
	             t.IS_IMPORT_DQR,
	             t.QT_RESULT_SEND_FLAG,
	             t.QT_RESULT_SEND_TIMES,
	             t.IS_AVOID_CHECK,
	             t.IS_BPM_FLAG,
	             t.INSERT_BY,
	             t.INSERT_TIME,
	             t.INSERT_TIMESTAMP,
	             t.UPDATE_BY,
	             t.UPDATE_TIME,
	             t.UPDATE_TIMESTAMP
	        FROM APP___NB__DBUSER.T_NB_QT_TASK t
	       WHERE t.TASK_ID = (SELECT MAX(nqt.TASK_ID)
	                            FROM APP___NB__DBUSER.T_NB_QT_TASK nqt
	                           WHERE nqt.QA_TYPE = '6'
                                 AND nqt.APPLY_CODE = #{APPLY_CODE})
		]]>
	</select>

</mapper>
