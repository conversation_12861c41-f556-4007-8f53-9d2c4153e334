<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_nbPolicyHolderDaoImpl">

	<!-- 查询保单投保人表条件 -->
	<sql id="JRQD_Nb_nbPolicyHolderWhereCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND t.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" address_id  != null "><![CDATA[ AND t.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND t.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND t.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" soci_secu  != null "><![CDATA[ AND t.SOCI_SECU = #{soci_secu} ]]></if>
		<if test=" smoking  != null "><![CDATA[ AND t.SMOKING = #{smoking} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND t.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND t.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND t.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" job_underwrite != null and job_underwrite != ''  "><![CDATA[ AND t.JOB_UNDERWRITE = #{job_underwrite} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND t.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND t.POLICY_ID = #{policy_id} ]]></if>
		<if test=" in_customer_id  != null"><![CDATA[ or tl.customer_id = #{in_customer_id} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ t.POLICY_ID = #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" agent_relation != null and agent_relation != ''  "><![CDATA[ AND t.AGENT_RELATION = #{agent_relation} ]]></if>
	</sql>

	<!-- 按索引生成的查询条件 -->
	<sql id="JRQD_NB_queryNbPolicyHolderByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND t.POLICY_ID = #{policy_id} ]]></if>
	</sql>

	<!-- 查询保单投保人表单条数据 -->
	<select id="JRQD_NB_findNbPolicyHolder"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT t.LIST_ID,
			       t.POLICY_CODE,
			       t.APPLY_CODE,
			       t.ADDRESS_ID,
			       t.JOB_CODE,
			       t.JOB_UNDERWRITE,
			       t.CUSTOMER_ID,
			       t.POLICY_ID,
			       t.CUSTOMER_HEIGHT,
			       t.CUSTOMER_WEIGHT,
			       t.SOCI_SECU,
			       t.SMOKING,
			       t.RESIDENT_TYPE,
			       /* t.AGENT_RELATION, */
			       /* t.ANNUAL_INCOME_CEIL, */
			       /* t.INCOME_SOURCE, */
			       /* t.RISK_PROBABILITY, */
			       t.APPLICANT_SPE_PEOPLE,
			       t.INSERT_BY,
			       t.INSERT_TIME,
			       t.INSERT_TIMESTAMP,
			       t.UPDATE_BY,
			       t.UPDATE_TIME,
			       t.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER t
			 WHERE 1 = 1
		]]>
		<include refid="JRQD_Nb_nbPolicyHolderWhereCondition" />
	</select>

	<!-- 根据policyId查询投保人信息 -->
	<select id="JRQD_NB_findNbPolicyHolderByPolicyId"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT t.LIST_ID,
			       t.POLICY_CODE,
			       t.APPLY_CODE,
			       t.ADDRESS_ID,
			       t.JOB_CODE,
			       t.JOB_UNDERWRITE,
			       t.CUSTOMER_ID,
			       t.POLICY_ID,
			       t.CUSTOMER_HEIGHT,
			       t.CUSTOMER_WEIGHT,
			       t.SOCI_SECU,
			       t.SMOKING,
			       t.RESIDENT_TYPE,
			       /* t.AGENT_RELATION, */
			       /* t.ANNUAL_INCOME_CEIL, */
			       /* t.INCOME_SOURCE, */
			       /* t.RISK_PROBABILITY, */
			       t.APPLICANT_SPE_PEOPLE,
			       t.INSERT_BY,
			       t.INSERT_TIME,
			       t.INSERT_TIMESTAMP,
			       t.UPDATE_BY,
			       t.UPDATE_TIME,
			       t.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_NB_POLICY_HOLDER t
			 WHERE 1 = 1
		]]>
		<include
			refid="JRQD_NB_queryNbPolicyHolderByPolicyIdCondition" />
	</select>

</mapper> 
