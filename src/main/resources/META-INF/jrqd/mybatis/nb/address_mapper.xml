<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_address">

	<!-- 按索引生成的查询条件 -->
	<sql id="JRQD_NB_queryAddressByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND t.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND t.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>

	<!-- 按索引查询操作 -->
	<select id="JRQD_NB_findAddressByAddressId"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT t.ADDRESS_ID,
			       t.CUSTOMER_ID,
			       t.ADDRESS_TYPE,
			       t.COUNTRY_CODE,
			       t.STATE,
			       t.CITY,
			       t.DISTRIC<PERSON>,
			       t.ADDRESS,
			       t.POST_CODE,
			       t.MOBILE_TEL,
			       t.FIXED_TEL,
			       t.EMAIL,
			       t.ADDRESS_STATUS,
			       t.CONFIRMED,
			       t.FOREIGN_INDI,
			       t.VALIDITY_START_DATE,
			       t.MAIL_FAILED_TIMES,
			       t.RETURN_MAIL_DATE,
			       t.RETURN_MAIL_REASON,
			       t.FAX_TEL,
			       t.HOUSE_TEL,
			       t.OFFICE_TEL,
			       t.COMPANY_NAME,
			       t.INSERT_BY,
			       t.INSERT_TIME,
			       t.INSERT_TIMESTAMP,
			       t.UPDATE_BY,
			       t.UPDATE_TIME,
			       t.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_ADDRESS t
			 WHERE 1 = 1
		]]>
		<include refid="JRQD_NB_queryAddressByAddressIdCondition" />
	</select>

</mapper>
