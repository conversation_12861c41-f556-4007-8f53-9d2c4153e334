<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_nbDrqInfoSend">

	<!-- 查询双录信息发送表最新记录 -->
	<select id="JRQD_NB_QueryLatestDrqInfoSend"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT t.LIST_ID,
			       t.POLICY_ID,
			       t.APPLY_CODE,
			       t.IS_NCL_QT,
			       t.IS_DRQ_DRAW,
			       t.DRQ_SEND_FLAG,
			       t.SEND_TIMES,
			       t.INSERT_BY,
			       t.INSERT_TIME,
			       t.INSERT_TIMESTAMP,
			       t.UPDATE_BY,
			       t.UPDATE_TIME,
			       t.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_NB_DRQ_INFO_SEND t
			 WHERE t.LIST_ID = (SELECT MAX(LIST_ID)
			                      FROM APP___NB__DBUSER.T_NB_DRQ_INFO_SEND
			                     WHERE APPLY_CODE = #{APPLY_CODE})
		]]>
	</select>

</mapper>
