<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="JRQD_NB_nbContractMaster">

	<!-- 查询投保单主表条件 -->
	<sql id="JRQD_NB_queryNbContractMasterWhereCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND ncm.POLICY_CODE = trim(#{policy_code}) ]]></if>
		<if test=" relation_policy_code != null and relation_policy_code != ''  "><![CDATA[ AND ncm.RELATION_POLICY_CODE = #{relation_policy_code} ]]></if>
		<if test=" double_mainrisk_flag  != null "><![CDATA[ AND ncm.DOUBLE_MAINRISK_FLAG = #{double_mainrisk_flag} ]]></if>
	</sql>

	<!-- 查询投保单主表单条记录 -->
	<select
		id="JRQD_NB_findNbContractMaster"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT ncm.POLICY_ID,
			       ncm.INSURED_FAMILY,
			       ncm.APPLY_CODE,
			       ncm.DECISION_CODE,
			       ncm.APPOINT_VALIDATE,
			       ncm.APPLY_DATE,
			       ncm.APPLY_TIME,
			       ncm.POLICY_TYPE,
			       ncm.BRANCH_ORGAN_CODE,
			       ncm.ORGAN_CODE,
			       ncm.VALIDATE_DATE,
			       ncm.EXPIRY_DATE,
			       ncm.INITIAL_PREM_DATE,
			       ncm.LIABILITY_STATE,
			       ncm.MONEY_CODE,
			       ncm.SUBMISSION_DATE,
			       ncm.SUBMIT_CHANNEL,
			       ncm.CHANNEL_TYPE,
			       ncm.LANG_CODE,
			       ncm.SALE_COM_CODE,
			       ncm.SERVICE_BANK,
			       ncm.SERVICE_BANK_BRANCH,
			       ncm.SERVICE_HANDLER,
			       ncm.SERVICE_HANDLER_CODE,
			       ncm.SERVICE_HANDLER_NAME,
			       ncm.BILL_CHECKED,
			       ncm.SALE_AGENT_CODE,
			       ncm.SALE_AGENT_NAME,
			       ncm.WINNING_START_FLAG,
			       ncm.HIGH_SA_INDI,
			       ncm.BIRTHDAY_POL_INDI,
			       ncm.RISK_INDI,
			       ncm.AGENT_ORG_ID,
			       ncm.DIALECT_INDI,
			       ncm.MANUAL_UW_INDI,
			       ncm.MEDIA_TYPE,
			       ncm.E_SERVICE_FLAG,
			       ncm.CHANNEL_ID,
			       ncm.POLICY_CODE,
			       ncm.OPERATOR_USER_CODE,
			       ncm.PROPOSAL_STATUS,
			       ncm.PA_USER_CODE,
			       ncm.PA_COMPLETE_TIME,
			       ncm.SCAN_USER_CODE,
			       ncm.SCAN_COMPLETE_TIME,
			       ncm.UW_USER_CODE,
			       ncm.UW_COMPLETE_TIME,
			       ncm.ISSUE_USER_CODE,
			       ncm.ISSUE_DATE,
			       ncm.OVERDUE_TIME,
			       ncm.AGENCY_CODE,
			       ncm.POLICY_PWD,
			       ncm.APL_PERMIT,
			       ncm.EVENT_CODE,
			       ncm.INPUT_DATE,
			       ncm.INPUT_TYPE,
			       ncm.SUBINPUT_TYPE,
			       ncm.SALE_TYPE,
			       ncm.CALL_TIME_LIST,
			       ncm.BANK_AGENCY_FLAG,
			       ncm.TRANSACTION_NO,
			       ncm.OLD_POLICY_CODE,
			       ncm.CONFIRM_WAY,
			       ncm.DRQ_FLAG,
			       ncm.POLICY_RETURN_MODE,
			       ncm.RELATION_POLICY_CODE,
			       ncm.TRANSFER_FLAG,
			       ncm.PRINT_SPECIAL_FLAG,
			       ncm.POLICY_PER_ARRT_ORGAN,
			       ncm.POLICY_PER_ARRT_ORGAN_NAME,
			       ncm.EMERGENCY_CONTACTS_NAME,
			       ncm.EMERGENCY_CONTACTS_MOBILE,
			       ncm.EMER_CON_RELATION_TO_PH,
			       ncm.IDCARD_OCR_FLAG,
			       ncm.BANKCARD_OCR_FLAG,
			       ncm.IS_SELF_INSURED,
			       ncm.POLICY_FLAG,
			       ncm.DOUBLE_MAINRISK_FLAG,
			       ncm.MAGNUM_CONCLUSION,
			       ncm.IS_CALL_MAGNUM,
			       ncm.IS_REMOTE_AUTOGRAPH,
			       ncm.IS_CASE_INFO_UPLOAD,
			       ncm.GROUP_SALE_TYPE,
			       ncm.WEBINPUT_BACKTRACKING_QUREY,
			       ncm.RELATION_APPLY_CODE,
			       ncm.IS_OCR_RELATION,
			       /* ncm.REVIEW_FLAG, */
			       ncm.IS_ALONE_INSURE,
			       ncm.SERVICE_BANK_NODE,
			       ncm.NCL_PRINT,
			       ncm.MEDICAL_INSURANCE_CARD,
			       ncm.POLICY_REINSURE_FLAG,
			       ncm.REINSURED_TIMES,
			       ncm.BANK_MANAGER_NAME,
			       ncm.BANK_MANAGER_LICENSENO,
			       ncm.IS_MUTUAL_INSURED,
			       ncm.POLICY_RELATION_TYPE,
			       /* ncm.MULTI_MAINRISK_FLAG, */
			       ncm.BANKNRT_FALG,
			       ncm.THIRD_ORG_FLAG,
			       ncm.MEET_POV_STANDARD_FLAG,
			       /* ncm.BUSI_PROD_NUM, */
			       /* ncm.AML_FLAG, */
			       ncm.INSERT_BY,
			       ncm.INSERT_TIME,
			       ncm.INSERT_TIMESTAMP,
			       ncm.UPDATE_BY,
			       ncm.UPDATE_TIME,
			       ncm.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER ncm
			 WHERE 1 = 1
		]]>
		<include refid="JRQD_NB_queryNbContractMasterWhereCondition" />
		<![CDATA[
		     ORDER BY ncm.POLICY_CODE
		]]>
	</select>

	<!-- 查询投保单主表(关联投保单状态轨迹) -->
	<select
		id="JRQD_NB_findAllNbContractMasterJoinedWithProposalProcess"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT ncm.POLICY_ID,
			       ncm.INSURED_FAMILY,
			       ncm.APPLY_CODE,
			       ncm.DECISION_CODE,
			       ncm.APPOINT_VALIDATE,
			       ncm.APPLY_DATE,
			       ncm.APPLY_TIME,
			       ncm.POLICY_TYPE,
			       ncm.BRANCH_ORGAN_CODE,
			       ncm.ORGAN_CODE,
			       ncm.VALIDATE_DATE,
			       ncm.EXPIRY_DATE,
			       ncm.INITIAL_PREM_DATE,
			       ncm.LIABILITY_STATE,
			       ncm.MONEY_CODE,
			       ncm.SUBMISSION_DATE,
			       ncm.SUBMIT_CHANNEL,
			       ncm.CHANNEL_TYPE,
			       ncm.LANG_CODE,
			       ncm.SALE_COM_CODE,
			       ncm.SERVICE_BANK,
			       ncm.SERVICE_BANK_BRANCH,
			       ncm.SERVICE_HANDLER,
			       ncm.SERVICE_HANDLER_CODE,
			       ncm.SERVICE_HANDLER_NAME,
			       ncm.BILL_CHECKED,
			       ncm.SALE_AGENT_CODE,
			       ncm.SALE_AGENT_NAME,
			       ncm.WINNING_START_FLAG,
			       ncm.HIGH_SA_INDI,
			       ncm.BIRTHDAY_POL_INDI,
			       ncm.RISK_INDI,
			       ncm.AGENT_ORG_ID,
			       ncm.DIALECT_INDI,
			       ncm.MANUAL_UW_INDI,
			       ncm.MEDIA_TYPE,
			       ncm.E_SERVICE_FLAG,
			       ncm.CHANNEL_ID,
			       ncm.POLICY_CODE,
			       ncm.OPERATOR_USER_CODE,
			       ncm.PROPOSAL_STATUS,
			       ncm.PA_USER_CODE,
			       ncm.PA_COMPLETE_TIME,
			       ncm.SCAN_USER_CODE,
			       ncm.SCAN_COMPLETE_TIME,
			       ncm.UW_USER_CODE,
			       ncm.UW_COMPLETE_TIME,
			       ncm.ISSUE_USER_CODE,
			       ncm.ISSUE_DATE,
			       ncm.OVERDUE_TIME,
			       ncm.AGENCY_CODE,
			       ncm.POLICY_PWD,
			       ncm.APL_PERMIT,
			       ncm.EVENT_CODE,
			       ncm.INPUT_DATE,
			       ncm.INPUT_TYPE,
			       ncm.SUBINPUT_TYPE,
			       ncm.SALE_TYPE,
			       ncm.CALL_TIME_LIST,
			       ncm.BANK_AGENCY_FLAG,
			       ncm.TRANSACTION_NO,
			       ncm.OLD_POLICY_CODE,
			       ncm.CONFIRM_WAY,
			       ncm.DRQ_FLAG,
			       ncm.POLICY_RETURN_MODE,
			       ncm.RELATION_POLICY_CODE,
			       ncm.TRANSFER_FLAG,
			       ncm.PRINT_SPECIAL_FLAG,
			       ncm.POLICY_PER_ARRT_ORGAN,
			       ncm.POLICY_PER_ARRT_ORGAN_NAME,
			       ncm.EMERGENCY_CONTACTS_NAME,
			       ncm.EMERGENCY_CONTACTS_MOBILE,
			       ncm.EMER_CON_RELATION_TO_PH,
			       ncm.IDCARD_OCR_FLAG,
			       ncm.BANKCARD_OCR_FLAG,
			       ncm.IS_SELF_INSURED,
			       ncm.POLICY_FLAG,
			       ncm.DOUBLE_MAINRISK_FLAG,
			       ncm.MAGNUM_CONCLUSION,
			       ncm.IS_CALL_MAGNUM,
			       ncm.IS_REMOTE_AUTOGRAPH,
			       ncm.IS_CASE_INFO_UPLOAD,
			       ncm.GROUP_SALE_TYPE,
			       ncm.WEBINPUT_BACKTRACKING_QUREY,
			       ncm.RELATION_APPLY_CODE,
			       ncm.IS_OCR_RELATION,
			       /* ncm.REVIEW_FLAG, */
			       ncm.IS_ALONE_INSURE,
			       ncm.SERVICE_BANK_NODE,
			       ncm.NCL_PRINT,
			       ncm.MEDICAL_INSURANCE_CARD,
			       ncm.POLICY_REINSURE_FLAG,
			       ncm.REINSURED_TIMES,
			       ncm.BANK_MANAGER_NAME,
			       ncm.BANK_MANAGER_LICENSENO,
			       ncm.IS_MUTUAL_INSURED,
			       ncm.POLICY_RELATION_TYPE,
			       /* ncm.MULTI_MAINRISK_FLAG, */
			       ncm.BANKNRT_FALG,
			       ncm.THIRD_ORG_FLAG,
			       ncm.MEET_POV_STANDARD_FLAG,
			       /* ncm.BUSI_PROD_NUM, */
			       /* ncm.AML_FLAG, */
			       ncm.INSERT_BY,
			       ncm.INSERT_TIME,
			       ncm.INSERT_TIMESTAMP,
			       ncm.UPDATE_BY,
			       ncm.UPDATE_TIME,
			       ncm.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER ncm
			 WHERE ncm.APPLY_CODE IN
			       (SELECT DISTINCT APPLY_CODE
			          FROM APP___NB__DBUSER.T_PROPOSAL_PROCESS
			         WHERE 1 = 1
		]]>
		<if test=" batch_date != null and batch_date != ''">
			<![CDATA[
			           AND TRUNC(START_TIME) = #{batch_date}
			]]>
		</if>
		<if test=" batch_start_date != null and batch_start_date != ''">
			<![CDATA[
			           AND TRUNC(START_TIME) >= #{batch_start_date}
			]]>
		</if>
		<if test=" batch_end_date != null and batch_end_date != ''">
			<![CDATA[
			           AND TRUNC(START_TIME) <= #{batch_end_date}
			]]>
		</if>
		<if test=" proposal_status_list  != null and proposal_status_list.size() > 0 ">
			<![CDATA[
			           AND START_STATUS IN (
			]]>
			<foreach collection="proposal_status_list" item="proposal_status"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{proposal_status} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[
			           AND APPLY_CODE IN (SELECT APPLY_CODE
			                                FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER
			                               WHERE 1 = 1
		]]>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[
			           AND POLICY_CODE IN (
			]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[
			           AND APPLY_CODE IN (
			]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[
			           AND SUBMIT_CHANNEL IN (
			]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" channel_type != null and channel_type != '' ">
			<![CDATA[
			                                 AND CHANNEL_TYPE = #{channel_type}
			]]>
		</if>
		<if test=" service_bank != null and service_bank != '' ">
			<![CDATA[
			                                 AND SERVICE_BANK = #{service_bank}
			]]>
		</if>
		<![CDATA[
			                             )
			       )
			 ORDER BY ncm.POLICY_CODE
		]]>
	</select>

	<!-- 查询投保单主表(关联投保单状态轨迹) - 查询总数用 -->
	<select id="JRQD_NB_findAllNbContractMasterJoinedWithProposalProcessCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_NB_findAllNbContractMasterJoinedWithProposalProcessRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 查询投保单主表(关联投保单状态轨迹) - 总数分页用 -->
	<select id="JRQD_NB_findAllNbContractMasterJoinedWithProposalProcessTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_NB_findAllNbContractMasterJoinedWithProposalProcessRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 查询投保单主表(关联投保单状态轨迹) - 详细分页用 -->
	<select id="JRQD_NB_findAllNbContractMasterJoinedWithProposalProcessDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_NB_findAllNbContractMasterJoinedWithProposalProcessRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>

	<!-- 查询投保单主表(关联投保单状态轨迹) - 查询范围 -->
	<sql id="JRQD_NB_findAllNbContractMasterJoinedWithProposalProcessRange">
		<![CDATA[
			SELECT ncm.POLICY_ID,
			       ncm.INSURED_FAMILY,
			       ncm.APPLY_CODE,
			       ncm.DECISION_CODE,
			       ncm.APPOINT_VALIDATE,
			       ncm.APPLY_DATE,
			       ncm.APPLY_TIME,
			       ncm.POLICY_TYPE,
			       ncm.BRANCH_ORGAN_CODE,
			       ncm.ORGAN_CODE,
			       ncm.VALIDATE_DATE,
			       ncm.EXPIRY_DATE,
			       ncm.INITIAL_PREM_DATE,
			       ncm.LIABILITY_STATE,
			       ncm.MONEY_CODE,
			       ncm.SUBMISSION_DATE,
			       ncm.SUBMIT_CHANNEL,
			       ncm.CHANNEL_TYPE,
			       ncm.LANG_CODE,
			       ncm.SALE_COM_CODE,
			       ncm.SERVICE_BANK,
			       ncm.SERVICE_BANK_BRANCH,
			       ncm.SERVICE_HANDLER,
			       ncm.SERVICE_HANDLER_CODE,
			       ncm.SERVICE_HANDLER_NAME,
			       ncm.BILL_CHECKED,
			       ncm.SALE_AGENT_CODE,
			       ncm.SALE_AGENT_NAME,
			       ncm.WINNING_START_FLAG,
			       ncm.HIGH_SA_INDI,
			       ncm.BIRTHDAY_POL_INDI,
			       ncm.RISK_INDI,
			       ncm.AGENT_ORG_ID,
			       ncm.DIALECT_INDI,
			       ncm.MANUAL_UW_INDI,
			       ncm.MEDIA_TYPE,
			       ncm.E_SERVICE_FLAG,
			       ncm.CHANNEL_ID,
			       ncm.POLICY_CODE,
			       ncm.OPERATOR_USER_CODE,
			       ncm.PROPOSAL_STATUS,
			       ncm.PA_USER_CODE,
			       ncm.PA_COMPLETE_TIME,
			       ncm.SCAN_USER_CODE,
			       ncm.SCAN_COMPLETE_TIME,
			       ncm.UW_USER_CODE,
			       ncm.UW_COMPLETE_TIME,
			       ncm.ISSUE_USER_CODE,
			       ncm.ISSUE_DATE,
			       ncm.OVERDUE_TIME,
			       ncm.AGENCY_CODE,
			       ncm.POLICY_PWD,
			       ncm.APL_PERMIT,
			       ncm.EVENT_CODE,
			       ncm.INPUT_DATE,
			       ncm.INPUT_TYPE,
			       ncm.SUBINPUT_TYPE,
			       ncm.SALE_TYPE,
			       ncm.CALL_TIME_LIST,
			       ncm.BANK_AGENCY_FLAG,
			       ncm.TRANSACTION_NO,
			       ncm.OLD_POLICY_CODE,
			       ncm.CONFIRM_WAY,
			       ncm.DRQ_FLAG,
			       ncm.POLICY_RETURN_MODE,
			       ncm.RELATION_POLICY_CODE,
			       ncm.TRANSFER_FLAG,
			       ncm.PRINT_SPECIAL_FLAG,
			       ncm.POLICY_PER_ARRT_ORGAN,
			       ncm.POLICY_PER_ARRT_ORGAN_NAME,
			       ncm.EMERGENCY_CONTACTS_NAME,
			       ncm.EMERGENCY_CONTACTS_MOBILE,
			       ncm.EMER_CON_RELATION_TO_PH,
			       ncm.IDCARD_OCR_FLAG,
			       ncm.BANKCARD_OCR_FLAG,
			       ncm.IS_SELF_INSURED,
			       ncm.POLICY_FLAG,
			       ncm.DOUBLE_MAINRISK_FLAG,
			       ncm.MAGNUM_CONCLUSION,
			       ncm.IS_CALL_MAGNUM,
			       ncm.IS_REMOTE_AUTOGRAPH,
			       ncm.IS_CASE_INFO_UPLOAD,
			       ncm.GROUP_SALE_TYPE,
			       ncm.WEBINPUT_BACKTRACKING_QUREY,
			       ncm.RELATION_APPLY_CODE,
			       ncm.IS_OCR_RELATION,
			       /* ncm.REVIEW_FLAG, */
			       ncm.IS_ALONE_INSURE,
			       ncm.SERVICE_BANK_NODE,
			       ncm.NCL_PRINT,
			       ncm.MEDICAL_INSURANCE_CARD,
			       ncm.POLICY_REINSURE_FLAG,
			       ncm.REINSURED_TIMES,
			       ncm.BANK_MANAGER_NAME,
			       ncm.BANK_MANAGER_LICENSENO,
			       ncm.IS_MUTUAL_INSURED,
			       ncm.POLICY_RELATION_TYPE,
			       /* ncm.MULTI_MAINRISK_FLAG, */
			       ncm.BANKNRT_FALG,
			       ncm.THIRD_ORG_FLAG,
			       ncm.MEET_POV_STANDARD_FLAG,
			       /* ncm.BUSI_PROD_NUM, */
			       /* ncm.AML_FLAG, */
			       ncm.INSERT_BY,
			       ncm.INSERT_TIME,
			       ncm.INSERT_TIMESTAMP,
			       ncm.UPDATE_BY,
			       ncm.UPDATE_TIME,
			       ncm.UPDATE_TIMESTAMP
			  FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER ncm
			 WHERE ncm.APPLY_CODE IN
			       (SELECT DISTINCT APPLY_CODE
			          FROM APP___NB__DBUSER.T_PROPOSAL_PROCESS
			         WHERE 1 = 1
		]]>
		<if test=" batch_date != null and batch_date != ''">
			<![CDATA[
			           AND TRUNC(START_TIME) = #{batch_date}
			]]>
		</if>
		<if test=" batch_start_date != null and batch_start_date != ''">
			<![CDATA[
			           AND TRUNC(START_TIME) >= #{batch_start_date}
			]]>
		</if>
		<if test=" batch_end_date != null and batch_end_date != ''">
			<![CDATA[
			           AND TRUNC(START_TIME) <= #{batch_end_date}
			]]>
		</if>
		<if test=" proposal_status_list  != null and proposal_status_list.size() > 0 ">
			<![CDATA[
			           AND START_STATUS IN (
			]]>
			<foreach collection="proposal_status_list" item="proposal_status"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{proposal_status} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[
			           AND APPLY_CODE IN (SELECT APPLY_CODE
			                                FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER
			                               WHERE 1 = 1
		]]>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[
			           AND POLICY_CODE IN (
			]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[
			           AND APPLY_CODE IN (
			]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[
			           AND SUBMIT_CHANNEL IN (
			]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" channel_type != null and channel_type != '' ">
			<![CDATA[
			                                 AND CHANNEL_TYPE = #{channel_type}
			]]>
		</if>
		<if test=" service_bank != null and service_bank != '' ">
			<![CDATA[
			                                 AND SERVICE_BANK = #{service_bank}
			]]>
		</if>
		<![CDATA[
			                             )
			       )
			 ORDER BY ncm.POLICY_CODE
		]]>
	</sql>

</mapper>
