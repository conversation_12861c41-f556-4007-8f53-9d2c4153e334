<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="CAP_PremArap">

	<!-- 查询当年续期保险缴纳金额 -->
	<select id="JRQD_CAP_payedPremInCurrentYear" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select round(sum(t.fee_amount), 2) fee_amount
		  from APP___CAP__DBUSER.t_prem_arap t
		 where 1 = 1
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND t.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[
             and t.deriv_type = '003'
             and t.arap_flag = '1'
             and to_char(t.due_time, 'yyyy') = to_char(sysdate, 'yyyy')
             and t.finish_time is not null
		]]>
	</select>

	<!-- 查询续期保费累计缴纳金额 -->
	<select id="JRQD_CAP_payedPremTotal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select round(sum(t.fee_amount), 2) fee_amount
		  from APP___CAP__DBUSER.t_prem_arap t
		 where 1 = 1
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND t.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[
             and t.deriv_type = '003'
             and t.arap_flag = '1' 
             and t.finish_time is not null
		]]>
	</select>
	
	<!-- 查询该单保险累计加保的补费金额 -->
	<select id="JRQD_CAP_payedPremByGather" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select round(SUM(ccp1.std_prem_af-ccp2.std_prem_af),2) as fee_amount
			  from dev_pas.t_cs_policy_change cpc 
			 inner join dev_pas.t_cs_accept_change cac 
			    on cpc.accept_id = cac.accept_id 
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ and cpc.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[
			   and cac.service_code in ('PA')
			   and cac.accept_status = '18'
			 inner join dev_pas.t_cs_contract_product ccp1
			 on ccp1.policy_chg_id = cpc.policy_chg_id
			 and ccp1.operation_type = '2'
			 and ccp1.old_new = '1' 
			 inner join dev_pas.t_cs_contract_product ccp2
			 on ccp1.policy_chg_id = ccp2.policy_chg_id
			 and ccp1.item_id = ccp2.item_id
			 and ccp2.old_new = '0' 
		]]>
	</select>
	
	<!-- 查询该单保险累计减保的退费金额 -->
	<select id="JRQD_CAP_payedPremByPayment" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select round(SUM(ccp2.std_prem_af-ccp1.std_prem_af),2) as fee_amount
			  from dev_pas.t_cs_policy_change cpc 
			 inner join dev_pas.t_cs_accept_change cac 
			    on cpc.accept_id = cac.accept_id 
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ and cpc.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[
			   and cac.service_code in ('PT')
			   and cac.accept_status = '18'
			 inner join dev_pas.t_cs_contract_product ccp1
			 on ccp1.policy_chg_id = cpc.policy_chg_id
			 and ccp1.operation_type = '2'
			 and ccp1.old_new = '1'
			 inner join dev_pas.t_cs_contract_product ccp2
			 on ccp1.policy_chg_id = ccp2.policy_chg_id
			 and ccp1.item_id = ccp2.item_id
			 and ccp2.old_new = '0' 
		]]>
	</select>
	
	<!-- 查询追加保费累计金额 -->
	<select id="JRQD_CAP_payedPremEx" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select round(sum(ccp1.append_prem_af),2) as fee_amount 
		  from dev_pas.t_cs_policy_change cpc
		 inner join dev_pas.t_cs_accept_change cac
		    on cpc.accept_id = cac.accept_id 
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND cpc.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[
		   and cac.service_code ='AM' 
		   and cac.accept_status = '18'
		 inner join dev_pas.t_cs_contract_product ccp1
		 on ccp1.policy_chg_id = cpc.policy_chg_id
		 and ccp1.operation_type = '2'
		 and ccp1.old_new = '1'
		]]>
	</select>
</mapper>
