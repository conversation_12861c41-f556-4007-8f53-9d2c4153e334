<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.batch.allocation.dao.impl.BonusAllocationDaoimpl.allocate">	
 <!-- 批处理分页查询开始 -->
 
	<sql id="JRQD_queryForPageStart">
		<if test="start != null and counts != null ">
			<![CDATA[SELECT policy_id FROM (SELECT ROWNUM rn, policy_id FROM (]]>
		</if>
	</sql>
	<!-- 批处理分页查询结束 -->
	<sql id="JRQD_queryForPageEnd">
		<if test="start != null  and counts != null" >
			<![CDATA[ and rn >= #{start} AND rn <= #{start} + #{counts}]]>
		</if>
	</sql>
   
    <!-- 添加保单号 -->
    <sql id="JRQD_queryallocationPolicyByPolicyCode">
         <if test="policy_code != null and policy_code != '' and policy_code !='null'">
         <![CDATA[ AND tcm.policy_code = #{policy_code} ]]></if>
         <if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[AND tcm.organ_code in (
		select t.organ_code
			  from APP___PAS__DBUSER.t_udmp_org_rel t
			 start with t.organ_code = #{organ_code}
			connect by prior t.organ_code = t.uporgan_code)  ]]></if>	
    </sql>
	
	<!-- 修改T_contract_prouct的Bonus_sa,sa_Bonus_date操作 -->
	<update id="JRQD_updateProductBonusSA" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
		    BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} 
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID=#{item_id,jdbcType=NUMERIC} ]]>
	</update>
	

    <!--根据保单id查询保单下所有险种    -->
    <select id="JRQD_findallbusiitemsbypolicyid" resultType="java.util.Map" parameterType="java.util.Map">
       <![CDATA[
           select * from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD where policy_id=#{policy_id,jdbcType=NUMERIC}  
       ]]>
    </select>
    
    <!--根据险种id查询险种下所有责任组  -->
    <select id="JRQD_findallitemsbybusiitemsid" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
             select * from  APP___PAS__DBUSER.T_CONTRACT_PRODUCT where busi_item_id=#{busi_item_id,jdbcType=NUMERIC}
          ]]>
    </select>
    

	<!-- 生成红利通知书并插入红利通知书中间表 -->    <!--暂无表待定 -->
	<insert id="JRQD_addsendBonusNotice" parameterType="java.util.Map"> 
	   <![CDATA[
	      
	   ]]>
    </insert>

    <!-- 查询最大分红日 -->
    <select id="JRQD_queryMaxAllocateDate" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
             select Max(allocate_date) as allocate_date  from APP___PAS__DBUSER.t_bonus_allocate
          ]]>
    </select>
	<!-- 查询最大分红日 -->
    <select id="JRQD_findLastBonusInfo" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select rownum rn, b.*
			    from (select a.*
			            from APP___PAS__DBUSER.T_BONUS_ALLOCATE a
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test=" allocate_type != null and allocate_type != ''  "><![CDATA[ AND A.ALLOCATE_TYPE = #{allocate_type} ]]></if>
     <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
     <if test=" sa_change_id  != null "><![CDATA[ AND A.SA_CHANGE_ID = #{sa_change_id} ]]></if>
     <if test="bonus_allot_list != null and bonus_allot_list.size()!=0">
		<![CDATA[AND A.bonus_allot IN ]]>
			<foreach collection ="bonus_allot_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
	 </if>
			          <![CDATA[
			           order by a.allocate_due_date desc) b
			   where rownum = 1
          ]]>
    </select>
    
    <!-- 查询本年度分红信息 -->
    <select id="JRQD_findAllBonusByYear" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select max(a.allocate_date) as allocate_date from APP___PAS__DBUSER.T_BONUS_ALLOCATE a
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
     <if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND to_date(to_char(A.ALLOCATE_DATE,'yyyy'),'yyyy') =  to_date(to_char(#{allocate_date},'yyyy'),'yyyy')]]></if>
    </select>
    
    <!-- 查询出险日应分的累计红利保额 -->
	<select id="JRQD_findBonusOfContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<![CDATA[ AND A.BONUS_ALLOT in ('1','2','6') ]]>
		<if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND A.ALLOCATE_DUE_DATE <= #{allocate_date} ]]></if>
	</select>
	
	    <!--出险日之前的上次分红日 -->
    <select id="JRQD_findBonusOfLastDate" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select max(a.allocate_due_date) as allocate_due_date from APP___PAS__DBUSER.T_BONUS_ALLOCATE a
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
     <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
     <![CDATA[ AND A.BONUS_ALLOT in ('1','2','6','4','5') ]]>
	 <if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND A.ALLOCATE_DATE <= #{allocate_date} ]]></if>
     
    </select>

	<!-- 年度分红-按启动参数查询符合条件的保单列表的最大policyId和最小policyId -->
	<select id="JRQD_queryallocationPolicyCounts" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ 
				select count(1)
			  from (select product_code_sys
			          from APP___PDS__DBUSER.t_business_product tbp
			         where tbp.product_category1 = '20002') a
			  left join APP___PAS__DBUSER.t_contract_busi_prod tcbp
			    on tcbp.busi_prod_code = a.product_code_sys
			  left join APP___PAS__DBUSER.t_contract_master tcm
			    on tcbp.policy_id = tcm.policy_id
			  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP
			  	ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			 where 1 = 1
			   and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01' and exists (
	         SELECT 1 FROM  APP___PAS__DBUSER.T_PAY_PLAN TPP WHERE TPP.POLICY_CODE = TCBP.POLICY_CODE AND TPP.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID 
           AND TPP.PAY_PLAN_TYPE='4' AND (TPP.PAY_STATUS <> 4 OR EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_PAY_DUE TPD WHERE 
           TPP.PLAN_ID = TPD.PLAN_ID AND TPP.PAY_STATUS=4 AND TPD.FEE_STATUS <> '01')))))
			   AND NOT EXISTS (SELECT 'X'
                FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                  ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
               WHERE  B.SUB_ID IN ('068', '067')
               	AND B.LOCK_SERVICE_TYPE = 1
                 AND A.POLICY_CODE = TCBP.POLICY_CODE)
		]]>
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND TCBP.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]></if>
		<if test=" organ_code  != null and organ_code !='' "><![CDATA[ AND TCM.ORGAN_CODE LIKE (SELECT ORGAN_CODE || '%' FROM TEMP_PARE_BA)]]></if>
		<if test=" batch_date  != null and batch_date !='' "><![CDATA[
		AND ((TCP.LAST_BONUS_DATE IS NULL AND ADD_MONTHS(TCBP.VALIDATE_DATE, 12) <= #{batch_date, jdbcType=DATE} 
		AND ADD_MONTHS(TCBP.VALIDATE_DATE, 12) < TCP.MATURITY_DATE)
    	OR (TCP.LAST_BONUS_DATE IS NOT NULL AND  ADD_MONTHS(TCP.LAST_BONUS_DATE - 1, 12) <= #{batch_date, jdbcType=DATE}
    	AND TCP.LAST_BONUS_DATE < TCP.MATURITY_DATE))
		]]></if>
	</select>

	<!-- 年度分红-按启动参数查询符合条件的保单 -->
	<select id="JRQD_queryallocatePolicy" resultType="java.util.Map"
		parameterType="java.util.Map">
   				<![CDATA[ 
			      SELECT ROWNUM,
             TCBP.POLICY_ID,
             TCBP.POLICY_CODE,
             TCBP.BUSI_ITEM_ID,
             TCBP.BUSI_PROD_CODE,
             TCBP.BUSI_PRD_ID,
             TCBP.VALIDATE_DATE,
             TCM.VALIDATE_DATE AS POLICY_EFFECTIVE_DATE
        FROM (SELECT PRODUCT_CODE_SYS
                FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP
               WHERE TBP.PRODUCT_CATEGORY1 = '20002') A
        LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
          ON TCBP.BUSI_PROD_CODE = A.PRODUCT_CODE_SYS
        LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
          ON TCBP.POLICY_ID = TCM.POLICY_ID
        LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP
          ON TCP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
       WHERE 1 = 1
         AND (TCBP.LIABILITY_STATE = 1 OR
             (TCBP.LIABILITY_STATE = 3 AND TCBP.END_CAUSE = '01' AND
             EXISTS (SELECT 1
                        FROM APP___PAS__DBUSER.T_PAY_PLAN TPP
                       WHERE TPP.POLICY_CODE = TCBP.POLICY_CODE
                         AND TPP.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                         AND TPP.PAY_PLAN_TYPE = '4'
                         AND (TPP.PAY_STATUS <> 4 OR NOT EXISTS
                              (SELECT 1
                                 FROM APP___PAS__DBUSER.T_PAY_DUE TPD
                                WHERE TPP.PLAN_ID = TPD.PLAN_ID
                                  AND TPP.PAY_STATUS = 4
                                  AND TPD.FEE_STATUS = '01')))))
         AND NOT EXISTS
       (SELECT 'X'
                FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                  ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
               WHERE B.SUB_ID IN ('068', '067')
                 AND B.LOCK_SERVICE_TYPE = 1
                 AND A.POLICY_CODE = TCBP.POLICY_CODE)
                AND MOD(TCM.POLICY_ID, #{modnum,jdbcType=VARCHAR}) = #{start,jdbcType=VARCHAR}
		]]>
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND TCBP.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]></if>
		<if test=" organ_code  != null and organ_code !='' "><![CDATA[ AND TCM.ORGAN_CODE LIKE (SELECT ORGAN_CODE || '%' FROM TEMP_PARE_BA)]]></if>
		<if test=" batch_date  != null and batch_date !='' "><![CDATA[
		AND ((TCP.LAST_BONUS_DATE IS NULL AND ADD_MONTHS(TCBP.VALIDATE_DATE, 12) <= #{batch_date, jdbcType=DATE} 
		AND ADD_MONTHS(TCBP.VALIDATE_DATE, 12) < TCP.MATURITY_DATE)
    	OR (TCP.LAST_BONUS_DATE IS NOT NULL AND  ADD_MONTHS(TCP.LAST_BONUS_DATE - 1, 12) <= #{batch_date, jdbcType=DATE}
    	AND TCP.LAST_BONUS_DATE < TCP.MATURITY_DATE))
		]]></if>
   			
	</select>
    
    <select id="JRQD_queryPolicyDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		select tcbp.policy_id,
			       tcbp.policy_code,
			       tcbp.busi_item_id,
			       tcbp.busi_prod_code,
			       tcbp.busi_prd_id,
			       tcbp.validate_date,
			       tcm.validate_date as policy_effective_date,
			       tcbp.maturity_date
			  from (select product_code_sys
			          from APP___PDS__DBUSER.t_business_product tbp
			         where tbp.product_category1 = '20002') a
			  left join APP___PAS__DBUSER.t_contract_busi_prod tcbp
			    on tcbp.busi_prod_code = a.product_code_sys
			  left join APP___PAS__DBUSER.t_contract_master tcm
			    on tcbp.policy_id = tcm.policy_id
			 where 1 = 1
				and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01'))	   
				AND NOT EXISTS (SELECT 'X'
                FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                  ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
               WHERE  B.SUB_ID IN ('068', '067')
               AND B.LOCK_SERVICE_TYPE = 1
                 AND A.POLICY_CODE = TCBP.POLICY_CODE)
   		]]>
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[AND TCBP.POLICY_CODE = #{policy_code}]]></if>
	</select>
    
</mapper>