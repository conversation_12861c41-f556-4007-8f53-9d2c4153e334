<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IProductLiabilityChangeDao">

	<sql id="JRQD_PA_productLiabilityChangeWhereCondition">
		<if test=" liability_changed  != null "><![CDATA[ AND A.LIABILITY_CHANGED = #{liability_changed} ]]></if>
		<if test=" liability_status  != null "><![CDATA[ AND A.LIABILITY_STATUS = #{liability_status} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" change_date  != null  and  change_date  != ''  "><![CDATA[ AND A.CHANGE_DATE = #{change_date} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		
		<if test=" busi_cash_value  != null "><![CDATA[ AND A.BUSI_CASH_VALUE = #{busi_cash_value} ]]></if>
		<if test=" terminal_bonus  != null "><![CDATA[ AND A.TERMINAL_BONUS = #{terminal_bonus} ]]></if>
		<if test=" pay_due  != null "><![CDATA[ AND A.PAY_DUE = #{pay_due} ]]></if>
		<if test=" loan_capital  != null "><![CDATA[ AND A.LOAN_CAPITAL = #{loan_capital} ]]></if>
		<if test=" loan_interest  != null "><![CDATA[ AND A.LOAN_INTEREST = #{loan_interest} ]]></if>
		<if test=" apl_capital  != null "><![CDATA[ AND A.APL_CAPITAL = #{apl_capital} ]]></if>
		<if test=" apl_interest  != null "><![CDATA[ AND A.APL_INTEREST = #{apl_interest} ]]></if>
		
		<!-- gyy_wb ADD start -->
		<if test=" start_date  != null "><![CDATA[ AND A.CHANGE_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.CHANGE_DATE <= #{end_date} ]]></if>
		<if test=" liability_changed_list  != null and liability_changed_list.size()!=0">
			<![CDATA[ AND A.liability_changed IN ]]>
			<foreach collection ="liability_changed_list" 
			item="item_liability_changed" index="index" open="(" close=")" separator=",">#{item_liability_changed}</foreach>
		</if>
		<!-- gyy_wb ADD end -->
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_queryProductLiabilityChangeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryProductLiabilityChangeByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryProductLiabilityChangeByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_PA_addProductLiabilityChange"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PRODUCT_LIABILITY_CHANGE__LI.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE(
				LIABILITY_CHANGED, LIABILITY_STATUS, INSERT_TIME, END_CAUSE, UPDATE_TIME, ITEM_ID, LAPSE_CAUSE, 
				INSERT_TIMESTAMP, UPDATE_BY, CHANGE_DATE, CHANGE_ID, LIST_ID, UPDATE_TIMESTAMP, POLICY_CHG_ID, 
				BUSI_ITEM_ID, INSERT_BY, POLICY_ID,BUSI_CASH_VALUE,TERMINAL_BONUS,PAY_DUE,LOAN_CAPITAL,
       			LOAN_INTEREST,APL_CAPITAL,APL_INTEREST ) 
			VALUES (
				#{liability_changed, jdbcType=NUMERIC}, #{liability_status, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{lapse_cause, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{change_date, jdbcType=DATE} , #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}
				, #{busi_cash_value, jdbcType=NUMERIC}, #{terminal_bonus, jdbcType=NUMERIC}, #{pay_due, jdbcType=NUMERIC}
				, #{loan_capital, jdbcType=NUMERIC}, #{loan_interest, jdbcType=NUMERIC}, #{apl_capital, jdbcType=NUMERIC}
				, #{apl_interest, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteProductLiabilityChange" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_PA_updateProductLiabilityChange" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE ]]>
		<set>
		<trim suffixOverrides=",">
		    LIABILITY_CHANGED = #{liability_changed, jdbcType=NUMERIC} ,
		    LIABILITY_STATUS = #{liability_status, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_DATE = #{change_date, jdbcType=DATE} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    BUSI_CASH_VALUE = #{busi_cash_value, jdbcType=NUMERIC},
		    TERMINAL_BONUS =  #{terminal_bonus, jdbcType=NUMERIC},
		    PAY_DUE = #{pay_due, jdbcType=NUMERIC},
		    LOAN_CAPITAL = #{loan_capital, jdbcType=NUMERIC},
       		LOAN_INTEREST = #{loan_interest, jdbcType=NUMERIC},
       		APL_CAPITAL = #{apl_capital, jdbcType=NUMERIC} ,
       		APL_INTERES =  #{apl_interest, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findProductLiabilityChangeByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_CHANGED, A.LIABILITY_STATUS, A.END_CAUSE, A.ITEM_ID, A.LAPSE_CAUSE, 
			A.CHANGE_DATE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID,BUSI_CASH_VALUE,TERMINAL_BONUS,PAY_DUE,LOAN_CAPITAL,
       			LOAN_INTEREST,APL_CAPITAL,APL_INTEREST FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryProductLiabilityChangeByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="JRQD_PA_findProductLiabilityChangeByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_CHANGED, A.LIABILITY_STATUS, A.END_CAUSE, A.ITEM_ID, A.LAPSE_CAUSE, 
			A.CHANGE_DATE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID,BUSI_CASH_VALUE,TERMINAL_BONUS,PAY_DUE,LOAN_CAPITAL,
       			LOAN_INTEREST,APL_CAPITAL,APL_INTEREST FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryProductLiabilityChangeByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="JRQD_PA_findProductLiabilityChangeByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_CHANGED, A.LIABILITY_STATUS, A.END_CAUSE, A.ITEM_ID, A.LAPSE_CAUSE, 
			A.CHANGE_DATE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID,BUSI_CASH_VALUE,TERMINAL_BONUS,PAY_DUE,LOAN_CAPITAL,
       			LOAN_INTEREST,APL_CAPITAL,APL_INTEREST FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryProductLiabilityChangeByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapProductLiabilityChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_CHANGED, A.LIABILITY_STATUS, A.END_CAUSE, A.ITEM_ID, A.LAPSE_CAUSE, 
			A.CHANGE_DATE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID,BUSI_CASH_VALUE,TERMINAL_BONUS,PAY_DUE,LOAN_CAPITAL,
       			LOAN_INTEREST,APL_CAPITAL,APL_INTEREST FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllProductLiabilityChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIABILITY_CHANGED, A.LIABILITY_STATUS, A.END_CAUSE, A.ITEM_ID, A.LAPSE_CAUSE, 
			A.CHANGE_DATE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID,BUSI_CASH_VALUE,TERMINAL_BONUS,PAY_DUE,LOAN_CAPITAL,
       			LOAN_INTEREST,APL_CAPITAL,APL_INTEREST FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_productLiabilityChangeWhereCondition" />
<!-- 		guyy_wb 添加按变更时间排序 -->
		<![CDATA[ ORDER BY A.CHANGE_DATE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_PA_findProductLiabilityChangeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryProductLiabilityChangeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LIABILITY_CHANGED, B.LIABILITY_STATUS, B.END_CAUSE, B.ITEM_ID, B.LAPSE_CAUSE, 
			B.CHANGE_DATE, B.CHANGE_ID, B.LIST_ID, B.POLICY_CHG_ID, 
			B.BUSI_ITEM_ID, B.POLICY_ID,B.BUSI_CASH_VALUE,B.TERMINAL_BONUS,B.PAY_DUE,B.LOAN_CAPITAL,
       			B.LOAN_INTEREST,B.APL_CAPITAL,B.APL_INTEREST FROM (
					SELECT ROWNUM RN, A.LIABILITY_CHANGED, A.LIABILITY_STATUS, A.END_CAUSE, A.ITEM_ID, A.LAPSE_CAUSE, 
			A.CHANGE_DATE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.POLICY_ID,A.BUSI_CASH_VALUE,A.TERMINAL_BONUS,A.PAY_DUE,A.LOAN_CAPITAL,
       			A.LOAN_INTEREST,A.APL_CAPITAL,A.APL_INTEREST FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- leihong -->
	<sql id="JRQD_PA_findLastProductLiabilityCondition">
		<if test=" start_date  != null "><![CDATA[ AND A.CHANGE_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.CHANGE_DATE <= #{end_date} ]]></if>
	</sql>
	
	<!-- leihong 查询保单年度末之前最后一次责任变更后状态为有效的记录 -->
	<select id="JRQD_PA_findLastProductLiabilityChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT rownum, A.LIABILITY_CHANGED, A.LIABILITY_STATUS, A.END_CAUSE, A.ITEM_ID, A.LAPSE_CAUSE, 
				A.CHANGE_DATE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, 
				A.BUSI_ITEM_ID, A.POLICY_ID,BUSI_CASH_VALUE,TERMINAL_BONUS,PAY_DUE,LOAN_CAPITAL,
       			LOAN_INTEREST,APL_CAPITAL,APL_INTEREST 
			FROM (
				SELECT LIABILITY_CHANGED, LIABILITY_STATUS, END_CAUSE, ITEM_ID, LAPSE_CAUSE, 
					CHANGE_DATE, CHANGE_ID, LIST_ID, POLICY_CHG_ID, 
					BUSI_ITEM_ID, POLICY_ID,BUSI_CASH_VALUE,TERMINAL_BONUS,PAY_DUE,LOAN_CAPITAL,
       			LOAN_INTEREST,APL_CAPITAL,APL_INTEREST
				FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE  A
			WHERE 1 = 1 
		 ]]>		
		<include refid="JRQD_PA_productLiabilityChangeWhereCondition" />
		<include refid="JRQD_PA_findLastProductLiabilityCondition" />			
		<![CDATA[ 	ORDER BY change_date desc) A WHERE rownum = 1	]]>
	</select>
	
	<!-- guyy 查询某一时点，保单、险种或责任组效力状态 -->
	<select id="JRQD_PA_findLiabilityStatuByChangeTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.*, ROWNUM
			  FROM (SELECT T.*
			          FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE T
			         WHERE 1 = 1
			         ]]>
					<if test=" policy_id  != null "><![CDATA[ AND t.POLICY_ID = #{policy_id} ]]></if>
			        <if test=" busi_item_id  != null "><![CDATA[ AND t.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
			        <if test=" item_id  != null "><![CDATA[ AND t.ITEM_ID = #{item_id} ]]></if>
			        <if test=" policy_chg_id  != null "><![CDATA[ AND t.policy_chg_id = #{policy_chg_id} ]]></if>
			        
			         <![CDATA[ 
			           AND T.CHANGE_DATE > #{change_date}
			         order by t.CHANGE_DATE) a
			 WHERE ROWNUM < 2
		 ]]>		
	</select>
	
	<!-- 保单状态接口 -->
	<select id="JRQD_PA_queryPolicyStateInf" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT BUSI_ITEM_ID,
				(select bp.old_pol_no from dev_pas.t_contract_busi_prod bp where bp.policy_id=t.policy_id and bp.busi_item_id = T.busi_item_id and bp.policy_id=#{policy_id}) old_pol_no,
			       LIABILITY_CHANGED,
			       CASE
			       WHEN CHANGE_DATE = (SELECT MAX(CHANGE_DATE)
			                               FROM DEV_PAS.T_PRODUCT_LIABILITY_CHANGE
			                              WHERE POLICY_ID = #{policy_id}
			                                AND ROWNUM = 1) THEN 1 ELSE 0 END STATE,
			       LAPSE_CAUSE || END_CAUSE AS STATEREASON,
			       CHANGE_DATE,
			       INSERT_TIME,
			       UPDATE_TIME
			  FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE T
			  WHERE T.POLICY_ID = #{policy_id}
			  ORDER BY CHANGE_DATE
		 ]]>		
	</select>
	
	<!-- 查询保单失效日期 -->
	<select id="JRQD_PA_findPolicyLapseDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT T.CHANGE_DATE  FROM 	APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE T WHERE T.POLICY_ID=#{policy_id} 
			AND T.CHANGE_DATE >#{start_date}
			AND T.CHANGE_DATE <#{batch_date} 
			AND T.LIABILITY_STATUS ='1' 
			AND T.LIABILITY_CHANGED ='4'		
			AND 				
				(SELECT COUNT(*) FROM 	APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE M WHERE M.POLICY_ID=#{policy_id}  
				AND  M.CHANGE_DATE=	#{batch_date} 
				AND M.LIABILITY_STATUS='4' 
				AND M.LIABILITY_CHANGED='1')>0 
				AND ROWNUM=1
		 ]]>	
	</select>
	
	<!-- 查询保单最新复效日期 -->
	<select id="JRQD_PA_findPolicyReinstateDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT * FROM (
				SELECT T.* FROM DEV_PAS.T_PRODUCT_LIABILITY_CHANGE T 
				 	WHERE T.POLICY_ID = #{policy_id}
				    	AND T.BUSI_ITEM_ID = #{busi_item_id}
				    	AND T.ITEM_ID = #{item_id}
					 	AND T.LIABILITY_STATUS='4'
					 	AND T.LIABILITY_CHANGED='1'
					ORDER BY T.CHANGE_DATE DESC) 
				WHERE ROWNUM=1
		]]>	
	</select>
</mapper>
