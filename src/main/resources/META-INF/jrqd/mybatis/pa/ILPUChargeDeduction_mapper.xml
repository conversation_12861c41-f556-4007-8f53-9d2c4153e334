<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.ILPUChargeDeductionJobDaoImpl">
	<sql id="JRQD_extractChargeDeductionCondition">
		<!-- 保单管理机构 -->
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[ AND  C.ORGAN_CODE IN (
				SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T
					START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
			) ]]>
		</if>
		<!-- 保单号 -->
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>

<!-- 		<if test=" start_num  != null and start_num !='' "><![CDATA[ AND A.busi_item_id >= #{start_num} ]]></if> -->
<!-- 		<if test=" end_num  != null and end_num !='' "><![CDATA[ AND A.busi_item_id <= #{end_num} ]]></if> -->
		<if test="batch_date != null "><![CDATA[ AND to_char(C.VALIDATE_DATE,'MM-dd') = to_char(#{batch_date},'MM-dd') ]]></if>
	</sql>

	<!-- 查询需要进行费用扣除的投连万能险种个数操作 -->
	<select id="JRQD_queryChargeDeductionDueCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1) AS END_NUM
			  FROM (SELECT A.BUSI_ITEM_ID,
			               (SELECT MAX(D.LAST_CHARGE_DATE)
			                  FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE D
			                 WHERE B.ITEM_ID = D.ITEM_ID) LAST_CHARGE_DATE
			          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
			          JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST B
			            ON A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER C
			            ON A.POLICY_ID = C.POLICY_ID
			         WHERE A.LIABILITY_STATE = 1
			           AND C.LIABILITY_STATE = 1
			           AND A.DUE_LAPSE_DATE IS NULL
			           AND (B.ACCUM_UNITS >= 0 OR B.INTEREST_CAPITAL >= 0)
			           AND NOT EXISTS
			         (SELECT 'X'
			                  FROM APP___PAS__DBUSER.T_LOCK_POLICY TA
			                  LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF TB
			                    ON TA.LOCK_SERVICE_ID = TB.LOCK_SERVICE_ID
			                 WHERE TB.SUB_ID IN ('068', '067')
			                   AND TB.LOCK_SERVICE_TYPE = 1
			                   AND TA.POLICY_CODE = A.POLICY_CODE) ]]>
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[ AND  C.ORGAN_CODE IN (
				SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T
					START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
			) ]]>
		</if>
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ) T WHERE (T.LAST_CHARGE_DATE IS NULL OR T.LAST_CHARGE_DATE < #{batch_date} ) ]]>
	</select>

	<!-- 查询需要进行费用扣除的投连万能险种列表 -->
	<select id="JRQD_queryChargeDeductionDueProducts" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT * FROM (
				SELECT T.*
				  FROM (SELECT A.*,
				               (SELECT MAX(D.LAST_CHARGE_DATE)
				                  FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE D
				                 WHERE B.ITEM_ID = D.ITEM_ID) LAST_CHARGE_DATE
				          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
				          JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST B
				            ON A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
				          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER C
				            ON A.POLICY_ID = C.POLICY_ID
				         WHERE A.LIABILITY_STATE = 1
				           AND C.LIABILITY_STATE = 1
				           AND A.DUE_LAPSE_DATE IS NULL
				           AND (B.ACCUM_UNITS >= 0 OR B.INTEREST_CAPITAL >= 0)
				           AND NOT EXISTS
				         (SELECT 'X'
				                  FROM APP___PAS__DBUSER.T_LOCK_POLICY TA
				                  LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF TB
				                    ON TA.LOCK_SERVICE_ID = TB.LOCK_SERVICE_ID
				                 WHERE TB.SUB_ID IN ('068', '067')
				                   AND TB.LOCK_SERVICE_TYPE = 1
				                   AND TA.POLICY_CODE = A.POLICY_CODE) ]]>
		<if test=" charge_code_list  != null and charge_code_list.size()!=0">
			<![CDATA[ AND D.CHARGE_CODE IN ]]>
			<foreach collection ="trans_code_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[ AND  C.ORGAN_CODE IN (
				SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T
					START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
			) ]]>
		</if>
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ) T WHERE (T.LAST_CHARGE_DATE IS NULL OR T.LAST_CHARGE_DATE < #{batch_date} )]]>
		<![CDATA[ ) WHERE MOD(POLICY_ID,#{modNum,jdbcType=VARCHAR}) = #{start,jdbcType=VARCHAR}]]>
		
	</select>


	<!-- 查询需要进行费用扣除的投连万能险种列表 -->
	<select id="JRQD_queryChargeDeduction" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT * FROM (
				select t.*
				  from (SELECT a.*,
				               (select max(d.last_charge_date)
				                  from APP___PAS__DBUSER.t_policy_fund_charge d
				                 where b.item_id = d.item_id AND D.CHARGE_CODE IN (1, 2, 3)) last_charge_date
				          FROM APP___PAS__DBUSER.t_contract_busi_prod a
				          join APP___PAS__DBUSER.t_contract_invest b
				            on a.busi_item_id = b.busi_item_id
				          join APP___PAS__DBUSER.t_contract_master c
				            on a.policy_id = c.policy_id
				         WHERE a.liability_state = 1
				           AND c.liability_state = 1
				           AND A.DUE_LAPSE_DATE IS NULL
				           AND (b.accum_units >= 0 OR b.interest_capital >= 0)
				           AND B.ACCOUNT_CODE IN ('000008',
							                       '890000',
							                       '892001',
							                       '903000',
							                       '904000',
							                       '905000',
							                       '907000',
							                       '909000',
							                       '910100',
							                       '912100',
							                       '913000',
							                       '914000',
							                       '919000','925000','926000','927000')
				           AND NOT EXISTS
				         (SELECT 'X'
				                  FROM APP___PAS__DBUSER.T_LOCK_POLICY TA
				                  LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF TB
				                    ON TA.LOCK_SERVICE_ID = TB.LOCK_SERVICE_ID
				                 WHERE TB.SUB_ID IN ('068', '067')
				                   AND TB.LOCK_SERVICE_TYPE = 1
				                   AND TA.POLICY_CODE = a.POLICY_CODE) ]]>
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[ AND  C.ORGAN_CODE in (
				SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T
					START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
			) ]]>
		</if>
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ) T WHERE (T.LAST_CHARGE_DATE IS NULL OR T.LAST_CHARGE_DATE < #{batch_date} )]]>
		<![CDATA[ ) where MOD(POLICY_ID,#{modNum,jdbcType=VARCHAR}) = #{start,jdbcType=VARCHAR}]]>
		
	</select>
	
	<!-- 查询对应扣费项的上次扣费日信息 -->
	<select id="JRQD_queryLastDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		SELECT  A.*
					FROM APP___PAS__DBUSER.T_POLICY_FUND_CHARGE  A
					WHERE 1 = 1
						AND A.POLICY_ID = #{policy_id}
						AND A.BUSI_ITEM_ID = #{busi_item_id}	
		]]>			
		<if test=" charge_code  != null and charge_code !='' "><![CDATA[ AND A.CHARGE_CODE = #{charge_code} ]]></if>
		<![CDATA[ 									
					ORDER BY  A.LAST_CHARGE_DATE DESC
		]]>
	</select>

	<!-- 查询指定险种在指定时间段的责任变更履历信息 -->
	<select id="JRQD_queryLiabilityChangeHistory" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT *
  				FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE A
 			WHERE A.POLICY_ID = #{policy_id}
   				AND A.BUSI_ITEM_ID = #{busi_item_id}
   				AND A.CHANGE_DATE >= #{start}
   				AND A.CHANGE_DATE <= #{end}
   			ORDER BY A.CHANGE_DATE ASC 
		]]>
	</select>

	<!-- 查询账户信息条件 -->
	<sql id="JRQD_queryContractInvestCondition">
		<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>

	<!-- 查询账户信息 -->
	<select id="JRQD_findContractInvestList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.*
  				FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A
 			WHERE 1 = 1
 				AND (A.ACCUM_UNITS >= 0 OR A.INTEREST_CAPITAL >= 0)
		]]>
		<include refid="JRQD_queryContractInvestCondition" />
	</select>

	<sql id="JRQD_queryFundPricesCondition">
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" start != null and start != ''  "><![CDATA[ AND A.PRICING_DATE > #{start} ]]></if>
		<if test=" end != null and end != ''  "><![CDATA[ AND A.PRICING_DATE <= #{end} ]]></if>
	</sql>

	<!-- 查询自上次扣费日截止当前的投连计价日以及价格信息列表 -->
	<select id="JRQD_queryFundPricesList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		SELECT A.*,ROWNUM 
			FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A
		WHERE 1 = 1 
		]]>
		<include refid="JRQD_queryFundPricesCondition" />
		<![CDATA[ ORDER BY PRICING_DATE ASC ]]>
	</select>


	<select id="JRQD_queryPolicyAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[		
		SELECT SUM(T.AMOUNT) SUM_AMOUNT, SUM(T.TOTAL_PREM_AF) SUM_TOTAL_PREM_AF
  			FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT T
 		WHERE 1 = 1
   			AND T.POLICY_ID = #{policy_id}
   			AND T.BUSI_ITEM_ID = #{busi_item_id}
   		GROUP BY T.POLICY_ID,T.BUSI_ITEM_ID  			
		]]>
	</select>

	<!-- 根据本次复效日期 查询对应失效日期 -->
	<select id="JRQD_findIneffectDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[		
				SELECT T.*
  				FROM (SELECT ROWNUM R, T.*
				          FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE T
				         WHERE 1 = 1
				           AND T.POLICY_ID = #{policy_id}
				           AND T.BUSI_ITEM_ID = #{busi_item_id}
				           AND T.LIABILITY_CHANGED = 4
				           AND T.CHANGE_DATE < #{change_date}
				         ORDER BY T.CHANGE_DATE DESC) T
 				WHERE R = 1
		]]>
	</select>

	<!-- 查询第一次部分领取的信息 -->
	<select id="JRQD_findPartPayFistMonth" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[		
				SELECT T.*
				  FROM (SELECT ROWNUM R, T.*
				          FROM APP___PAS__DBUSER.T_FUND_TRANS T
				         WHERE T.FUND_CODE = '22'
				           AND T.POLICY_ID = #{policy_id}
				           AND T.DEAL_TIME <= #{deal_time}
				         ORDER BY T.DEAL_TIME ASC) T
				 WHERE R = 1
		]]>
	</select>
	
	<!-- 查询指定时间点的所有交易总额  (买入或卖出类型) -->
	<select id="JRQD_calcSumAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[		
				SELECT NVL(SUM(T.TRANS_AMOUNT), 0) AS SUM_AMOUNT,T.TRANS_TYPE AS TRANS_TYPE
				  FROM APP___PAS__DBUSER.T_FUND_TRANS T
				 WHERE 1 = 1
				   AND T.LIST_ID = #{list_id}
				   AND T.DEAL_TIME = #{deal_time}				   
				   GROUP BY T.TRANS_TYPE 
		]]>
	</select>
	
	
	<!-- 当前累积增加保费 -->
	<select id="JRQD_calcTotalAddAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[		
				SELECT NVL(SUM(T.TRANS_AMOUNT), 0) AS SUM_AMOUNT
				  FROM APP___PAS__DBUSER.T_FUND_TRANS T
				 WHERE 1 = 1
				   AND T.LIST_ID = #{list_id}
				   AND T.DEAL_TIME <= #{deal_time}				  
				   AND T.TRANS_TYPE in (0,2)
		]]>
	</select>
	
	<select id="JRQD_findFundTransList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM 
			 APP___PAS__DBUSER.T_FUND_TRANS A 
				WHERE  A.LIST_ID = #{list_id}   ]]>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.DEAL_TIME >#{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.DEAL_TIME <= #{end_date} ]]></if>
		<![CDATA[ ORDER BY A.DEAL_TIME  ]]>
	</select>
	<!-- 查询满足发放投连万能状态报告书 -->
	<select id="JRQD_queryILPUchargeNoticeCounts" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			     SELECT COUNT(1) AS END_NUM
			       FROM (
			             
			             SELECT DISTINCT D.*                         
			               FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
			               LEFT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT B
			                 ON A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
			               LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER D
			                 ON A.POLICY_ID = D.POLICY_ID
			              WHERE 1 = 1
			                AND (D.LIABILITY_STATE = 1 OR D.LIABILITY_STATE = 3 OR D.LIABILITY_STATE = 4)
			                AND (B.PRODUCT_CATEGORY1 = '20003' OR B.PRODUCT_CATEGORY1 = '20004')
			             
			             ) C
			      WHERE 1 = 1
		 ]]>

		<include refid="JRQD_extractChargeDeductionCondition" />
	</select>
	<!-- 查询满足发放投连万能通知书的数据 -->
	<select id="JRQD_queryILPUchargeNoticeData" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			     SELECT * FROM (
                 
                          SELECT ROWNUM AS RN,C.* FROM (
                   
                          SELECT DISTINCT D.*                         
                     FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
                     LEFT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT B
			                 ON A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
			               LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER D
			                 ON A.POLICY_ID = D.POLICY_ID
			              WHERE 1 = 1
			                AND (D.LIABILITY_STATE = 1 OR D.LIABILITY_STATE = 3 OR D.LIABILITY_STATE = 4)
			                AND A.BUSI_PROD_CODE IN ('00890000','00892000')
                   
                   )C WHERE 1=1]]>
                  	 <include refid="JRQD_extractChargeDeductionCondition" />	
                )E
                
		<![CDATA[ WHERE 1=1 ]]>
			<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(E.POLICY_ID , #{modnum}) = #{start} AND ROWNUM <= 5000 ]]></if>
		
	</select>
	
	<!-- 查询所有的账户信息 -->
	<select id="JRQD_findAllContractInvestList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.*
  				FROM APP___PAS__DBUSER.T_CONTRACT_INVEST A
 			WHERE 1 = 1
		]]>
		<include refid="JRQD_queryContractInvestCondition" />
	</select>
	
	<select id="JRQD_queryIsFirstTrans" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1)
			  FROM DEV_PAS.T_FUND_TRANS T
			 WHERE T.LIST_ID = #{list_id}
			   and T.TRANS_CODE IN ('03','41')
		 ]]>		
	</select>
	

	
	<!-- 查询扣费时点最近的一条交易记录作为上次扣费日 -->
	<select id="JRQD_findLatesedFundTransList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			        SELECT T.DEAL_TIME
			          FROM (SELECT T.DEAL_TIME
			                  FROM APP___PAS__DBUSER.T_FUND_TRANS T
			                 WHERE  T.LIST_ID = #{list_id}
			                   AND T.DEAL_TIME <= #{deal_time}
			                   AND T.TRANS_CODE = #{trans_code}
			                 ORDER BY T.DEAL_TIME DESC) T
			         WHERE ROWNUM = 1
		]]>
	</select>
	
	<!-- 888状态报告书批处理查询要执行的集合信息-->
	<select id="JRQD_PA_queryCastAndConnect" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			     SELECT * FROM (                
                          SELECT ROWNUM AS RN,C.* FROM (                   
                          SELECT DISTINCT D.*                         
                     FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
                     LEFT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT B
			                 ON A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
			               LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER D
			                 ON A.POLICY_ID = D.POLICY_ID
			              WHERE 1 = 1
			                AND D.LIABILITY_STATE = 1
			                AND A.BUSI_PROD_CODE = '00888000'
			                AND TO_CHAR(D.VALIDATE_DATE,'yyyy-mm') <= TO_CHAR(#{last_year_day},'yyyy-mm')
                            AND TO_CHAR(D.VALIDATE_DATE,'mm') = TO_CHAR(#{last_year_day}-1,'mm')
                            AND EXISTS (
		                      SELECT 1 FROM 
		                      APP___PAS__DBUSER.T_FUND_TRANS Z 
		                      WHERE Z.TRANS_CODE IN ('04','02','41','03')
		                      AND Z.POLICY_ID = D.POLICY_ID
		                      AND TO_CHAR(Z.DEAL_TIME,'yyyy-mm') = TO_CHAR(#{batch_date}-1,'yyyy-mm')
		                      )
		                      AND NOT EXISTS(
		                      SELECT 1 FROM 
		                      APP___PAS__DBUSER.T_DOCUMENT Z
		                      WHERE Z.TEMPLATE_CODE = 'PAS_00006'
		                      AND Z.POLICY_ID = D.POLICY_ID
		                      AND Z.CREATE_TIME = #{batch_date}
		                      )
                   )C WHERE 1=1]]>
				        <!-- 保单管理机构 -->
						<if test=" organ_code  != null and organ_code !='' ">
							<![CDATA[ AND  C.ORGAN_CODE IN (
								SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T
									START WITH T.ORGAN_CODE = #{organ_code}
									CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
							) ]]>
						</if>
						<!-- 保单号 -->
						<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>
                )E
                
		<![CDATA[ WHERE 1=1 ]]>
			<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(E.POLICY_ID , #{modnum}) = #{start} ]]></if>
		
	</select>
		
</mapper>