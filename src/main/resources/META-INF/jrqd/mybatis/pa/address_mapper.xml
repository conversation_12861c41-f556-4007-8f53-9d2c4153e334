<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="address">

	<sql id="JRQD_PA_addressWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" state != null and state != ''  "><![CDATA[ AND A.STATE = #{state} ]]></if>
		<if test=" customer_id  != null and customer_id != ''"><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" foreign_indi  != null "><![CDATA[ AND A.FOREIGN_INDI = #{foreign_indi} ]]></if>
		<if test=" mail_failed_times  != null "><![CDATA[ AND A.MAIL_FAILED_TIMES = #{mail_failed_times} ]]></if>
		<if test=" return_mail_reason != null and return_mail_reason != ''  "><![CDATA[ AND A.RETURN_MAIL_REASON = #{return_mail_reason} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" email != null and email != ''  "><![CDATA[ AND A.EMAIL = #{email} ]]></if>
		<if test=" district != null and district != ''  "><![CDATA[ AND A.DISTRICT = #{district} ]]></if>
		<if test=" address != null and address != ''  "><![CDATA[ AND A.ADDRESS = #{address} ]]></if>
		<if test=" country_code != null and country_code != ''  "><![CDATA[ AND A.COUNTRY_CODE = #{country_code} ]]></if>
		<if test=" address_status  != null "><![CDATA[ AND A.ADDRESS_STATUS = #{address_status} ]]></if>
		<if test=" post_code != null and post_code != ''  "><![CDATA[ AND A.POST_CODE = #{post_code} ]]></if>
		<if test=" return_mail_date  != null  and  return_mail_date  != ''  "><![CDATA[ AND A.RETURN_MAIL_DATE = #{return_mail_date} ]]></if>
		<if test=" address_type != null and address_type != ''  "><![CDATA[ AND A.ADDRESS_TYPE = #{address_type} ]]></if>
		<if test=" confirmed  != null "><![CDATA[ AND A.CONFIRMED = #{confirmed} ]]></if>
		<if test=" city != null and city != ''  "><![CDATA[ AND A.CITY = #{city} ]]></if>
		<if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND A.FIXED_TEL = #{fixed_tel} ]]></if>
		<if test=" validity_start_date  != null  and  validity_start_date  != ''  "><![CDATA[ AND A.VALIDITY_START_DATE = #{validity_start_date} ]]></if>
		<if test=" company_name  != null  and  company_name  != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_queryAddressByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryAddressByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code}]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_PA_addAddress"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="address_id">
			SELECT APP___PAS__DBUSER.S_ADDRESS.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ADDRESS(
				ADDRESS_ID, STATE, CUSTOMER_ID, FOREIGN_INDI, MAIL_FAILED_TIMES, INSERT_TIMESTAMP, RETURN_MAIL_REASON, 
				UPDATE_BY, MOBILE_TEL, EMAIL, DISTRICT, ADDRESS, INSERT_TIME, COUNTRY_CODE, 
				UPDATE_TIME, ADDRESS_STATUS, POST_CODE, RETURN_MAIL_DATE, ADDRESS_TYPE, CONFIRMED, CITY, 
				UPDATE_TIMESTAMP, FIXED_TEL, INSERT_BY, VALIDITY_START_DATE,FAX_TEL,HOUSE_TEL,OFFICE_TEL,COMPANY_NAME  ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{state, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{foreign_indi, jdbcType=NUMERIC} , #{mail_failed_times, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{return_mail_reason, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{mobile_tel, jdbcType=VARCHAR} , #{email, jdbcType=VARCHAR} , #{district, jdbcType=VARCHAR} , #{address, jdbcType=VARCHAR} , SYSDATE , #{country_code, jdbcType=VARCHAR} 
				, SYSDATE , #{address_status, jdbcType=NUMERIC} , #{post_code, jdbcType=VARCHAR} , #{return_mail_date, jdbcType=DATE} , #{address_type, jdbcType=VARCHAR} , #{confirmed, jdbcType=NUMERIC} , #{city, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{fixed_tel, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{validity_start_date, jdbcType=DATE}
				, #{fax_tel, jdbcType=VARCHAR}
				, #{house_tel, jdbcType=VARCHAR}
				, #{office_tel, jdbcType=VARCHAR}
				, #{company_name, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteAddress" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_ADDRESS WHERE ADDRESS_ID = #{address_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_PA_updateAddress" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_ADDRESS ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
			STATE = #{state, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    FOREIGN_INDI = #{foreign_indi, jdbcType=NUMERIC} ,
		    MAIL_FAILED_TIMES = #{mail_failed_times, jdbcType=NUMERIC} ,
			RETURN_MAIL_REASON = #{return_mail_reason, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			MOBILE_TEL = #{mobile_tel, jdbcType=VARCHAR} ,
			EMAIL = #{email, jdbcType=VARCHAR} ,
			DISTRICT = #{district, jdbcType=VARCHAR} ,
			ADDRESS = #{address, jdbcType=VARCHAR} ,
			COUNTRY_CODE = #{country_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ADDRESS_STATUS = #{address_status, jdbcType=NUMERIC} ,
			POST_CODE = #{post_code, jdbcType=VARCHAR} ,
		    RETURN_MAIL_DATE = #{return_mail_date, jdbcType=DATE} ,
			ADDRESS_TYPE = #{address_type, jdbcType=VARCHAR} ,
		    CONFIRMED = #{confirmed, jdbcType=NUMERIC} ,
			CITY = #{city, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			FIXED_TEL = #{fixed_tel, jdbcType=VARCHAR} ,
		    VALIDITY_START_DATE = #{validity_start_date, jdbcType=DATE} ,
		    FAX_TEL = #{fax_tel, jdbcType=VARCHAR} ,	
		    HOUSE_TEL = #{house_tel, jdbcType=VARCHAR} ,	
			OFFICE_TEL = #{office_tel, jdbcType=VARCHAR} ,
			COMPANY_NAME = #{company_name, jdbcType=VARCHAR} ,	 
		</trim>
		</set>
		<![CDATA[ WHERE ADDRESS_ID = #{address_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findAddressByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL,A.COMPANY_NAME FROM APP___PAS__DBUSER.T_ADDRESS A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryAddressByAddressIdCondition" />
	</select>
	
	<select id="JRQD_PA_findAddressByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT rownum,  A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL,A.COMPANY_NAME FROM APP___PAS__DBUSER.T_ADDRESS A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryAddressByCustomerIdCondition" />
		<![CDATA[ ORDER BY INSERT_TIME DESC ]]>
	</select>
	<!--查询申请人的信息  -->
	<select id="JRQD_PA_findAddressByCustomerIdMobileTel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MOBILE_TEL FROM APP___PAS__DBUSER.T_ADDRESS A ,APP___PAS__DBUSER.T_CS_APPLICATION T WHERE 1 = 1 and t.customer_id=a.customer_id and T.change_id=#{change_id} ]]>
   	</select>

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapAddress" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
				A.FIXED_TEL, A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL,A.COMPANY_NAME FROM APP___PAS__DBUSER.T_ADDRESS A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_addressWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllAddress" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL,A.COMPANY_NAME FROM APP___PAS__DBUSER.T_ADDRESS A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_addressWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_PA_findAddressTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_ADDRESS A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_addressWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryAddressForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.STATE, B.CUSTOMER_ID, B.FOREIGN_INDI, B.MAIL_FAILED_TIMES, B.RETURN_MAIL_REASON, 
			B.MOBILE_TEL, B.EMAIL, B.DISTRICT, B.ADDRESS, B.COUNTRY_CODE, 
			B.ADDRESS_STATUS, B.POST_CODE, B.RETURN_MAIL_DATE, B.ADDRESS_TYPE, B.CONFIRMED, B.CITY, 
			B.FIXED_TEL, B.VALIDITY_START_DATE, B.FAX_TEL,B.HOUSE_TEL,B.OFFICE_TEL,B.COMPANY_NAME FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL,A.COMPANY_NAME FROM APP___PAS__DBUSER.T_ADDRESS A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_PA_addressWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="JRQD_PA_findAddressByCustomerNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE,A.INSERT_TIME,A.INSERT_BY,A.UPDATE_TIME,A.UPDATE_BY, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL,A.COMPANY_NAME FROM APP___PAS__DBUSER.T_ADDRESS A WHERE A.CUSTOMER_ID=#{customer_id}  ]]>
		
	</select>
	<!-- 添加机构查询 -->
	<select id="JRQD_findOrgName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_CODE, A.ORGAN_NAME, A.ORGAN_GRADE FROM DEV_PAS.T_UDMP_ORG_REL A WHERE 1=1  ]]>
		<include refid="JRQD_PA_queryAddressByCustomerIdCondition" />
	</select>
	
		<!-- 添加机构查询邮编 -->
	<select id="JRQD_findOrgZipCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.ORGAN_ZIPCODE,a.ORGAN_NAME,a.ORGAN_ADDRESS from DEV_PAS.T_UDMP_ORG A where 1=1 ]]>
		<include refid="JRQD_PA_queryAddressByCustomerIdCondition" />
	</select>
	
			
	<select id="JRQD_findSalesOrgan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select m.organ_level_code,M.SALES_ORGAN_CODE,M.SALES_ORGAN_NAME ,M.ADMIN_ORGAN_CODE,
		M.Parent_Code from dev_pas.t_sales_organ M where M.SALES_ORGAN_CODE=#{sales_organ_code}
  ]]>
		
	</select>
	
	
	<select id="JRQD_findSalesByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[  select t.sales_organ_name, t.sales_organ_code,t.organ_level_code
		from APP___PAS__DBUSER.t_sales_organ t  
		start with t.sales_organ_code = #{sales_organ_code}
		connect by prior t.parent_code = t.sales_organ_code
		order by t.organ_level_code; ]]>
	</select>
		
	
		<select id="JRQD_findBankBranch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select ee.bank_branch_name from dev_pas.t_bank_branch ee where ee.bank_branch_code=#{bank_branch_code}
  ]]>
		
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findAddressByAddressIdTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID,(SELECT NAME FROM DEV_PAS.T_DISTRICT D WHERE D.CODE=A.STATE) AS STATE,A.CUSTOMER_ID,A.FOREIGN_INDI,A.MAIL_FAILED_TIMES,
				A.RETURN_MAIL_REASON,A.MOBILE_TEL,A.EMAIL,(SELECT NAME FROM DEV_PAS.T_DISTRICT D WHERE D.CODE=A.DISTRICT) AS DISTRICT,A.ADDRESS,
				A.COUNTRY_CODE,A.ADDRESS_STATUS,A.POST_CODE,A.RETURN_MAIL_DATE,A.ADDRESS_TYPE,A.CONFIRMED, 
				(SELECT NAME FROM DEV_PAS.T_DISTRICT D WHERE D.CODE=A.CITY) AS CITY,A.FIXED_TEL,A.VALIDITY_START_DATE,A.COMPANY_NAME
				  FROM APP___PAS__DBUSER.T_ADDRESS A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryAddressByAddressIdCondition" />
	</select>
	
	<select id="JRQD_findAddressIdNextVal" resultType="java.util.Map" parameterType="java.util.Map">
	 	<![CDATA[SELECT APP___PAS__DBUSER.S_ADDRESS.NEXTVAL AS ADDRESS_ID FROM DUAL]]>
	</select>
	
	
	<!-- 客户快捷键变更  添加操作 无主键自增-->
	<insert id="JRQD_PA_addCCHKAddress"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_ADDRESS(
				ADDRESS_ID, STATE, CUSTOMER_ID, FOREIGN_INDI, MAIL_FAILED_TIMES, INSERT_TIMESTAMP, RETURN_MAIL_REASON, 
				UPDATE_BY, MOBILE_TEL, EMAIL, DISTRICT, ADDRESS, INSERT_TIME, COUNTRY_CODE, 
				UPDATE_TIME, ADDRESS_STATUS, POST_CODE, RETURN_MAIL_DATE, ADDRESS_TYPE, CONFIRMED, CITY, 
				UPDATE_TIMESTAMP, FIXED_TEL, INSERT_BY, VALIDITY_START_DATE, COMPANY_NAME ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{state, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{foreign_indi, jdbcType=NUMERIC} , #{mail_failed_times, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{return_mail_reason, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{mobile_tel, jdbcType=VARCHAR} , #{email, jdbcType=VARCHAR} , #{district, jdbcType=VARCHAR} , #{address, jdbcType=VARCHAR} , SYSDATE , #{country_code, jdbcType=VARCHAR} 
				, SYSDATE , #{address_status, jdbcType=NUMERIC} , #{post_code, jdbcType=VARCHAR} , #{return_mail_date, jdbcType=DATE} , #{address_type, jdbcType=VARCHAR} , #{confirmed, jdbcType=NUMERIC} , #{city, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{fixed_tel, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{validity_start_date, jdbcType=DATE} , #{company_name, jdbcType=DATE}) 
		 ]]>
	</insert>
	
	<select id="JRQD_queryAddressById" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE,A.COMPANY_NAME FROM APP___PAS__DBUSER.T_ADDRESS A WHERE ROWNUM <=  1000  ]]>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		
		<if test=" address_id_list  != null and address_id_list.size()!=0">
		<![CDATA[ AND (A.ADDRESS_ID in ]]>
		<foreach collection ="address_id_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
		<![CDATA[ )]]>
		</if>
	</select>
	
		
	
	
	<!-- 查询移动电话一致的所有客户(投保人、被保人、受益人) P2740接口 -->
	<select id="JRQD_PA_findAllAddressCustomersByMobiel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT DISTINCT a.Customer_Id
			  FROM App___Pas__Dbuser.t_Address a
			 WHERE (EXISTS
			        (SELECT 1
			           FROM App___Pas__Dbuser.t_Policy_Holder Ph
			          WHERE a.Customer_Id = Ph.Customer_Id
			            AND Ph.Address_Id = a.Address_Id
			            AND EXISTS (SELECT 1
			                   FROM App___Pas__Dbuser.t_Contract_Master Cm
			                  WHERE Ph.Policy_Id = Cm.Policy_Id
			                    AND Cm.Liability_State = '1']]>
			<if test=" organ_code != null and organ_code !='' "><![CDATA[ and substr(cm.organ_code ,0,4) = #{organ_code} ]]></if>                   
            <![CDATA[        
			           			)
			            AND EXISTS
			          (SELECT 1
			                   FROM App___Pas__Dbuser.t_Contract_Busi_Prod Cbp
			                  WHERE Cbp.Policy_Id = Ph.Policy_Id
			                    AND Cbp.Master_Busi_Item_Id IS NULL
			                    AND EXISTS
			                  (SELECT 1
			                           FROM App___Pas__Dbuser.t_Business_Product Pro
			                          WHERE Cbp.Busi_Prd_Id = Pro.Business_Prd_Id
			                            AND Pro.Cover_Period_Type = '0'))) OR EXISTS
			        (SELECT 1
			           FROM App___Pas__Dbuser.t_Insured_List Ph
			          WHERE a.Customer_Id = Ph.Customer_Id
			            AND Ph.Address_Id = a.Address_Id
			            AND EXISTS (SELECT 1
			                   FROM App___Pas__Dbuser.t_Contract_Master Cm
			                  WHERE Ph.Policy_Id = Cm.Policy_Id
			                    AND Cm.Liability_State = '1' ]]>
			<if test=" organ_code != null and organ_code !='' "><![CDATA[ and substr(cm.organ_code ,0,4) = #{organ_code} ]]></if>                   
            <![CDATA[ 
			                    )
			            AND EXISTS
			          (SELECT 1
			                   FROM App___Pas__Dbuser.t_Contract_Busi_Prod Cbp
			                  WHERE Cbp.Policy_Id = Ph.Policy_Id
			                    AND Cbp.Master_Busi_Item_Id IS NULL
			                    AND EXISTS
			                  (SELECT 1
			                           FROM App___Pas__Dbuser.t_Business_Product Pro
			                          WHERE Cbp.Busi_Prd_Id = Pro.Business_Prd_Id
			                            AND Pro.Cover_Period_Type = '0')))) ]]>
		     <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND a.MOBILE_TEL = #{mobile_tel} ]]></if>
		     <if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND a.FIXED_TEL = #{fixed_tel} ]]></if>
		     <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND a.OFFICE_TEL = #{office_tel} ]]></if> 
		     <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND a.HOUSE_TEL = #{house_tel} ]]></if> 
		     <if test=" company_name != null and company_name != ''  "><![CDATA[ AND a.COMPANY_NAME = #{company_name} ]]></if> 
		     
		    <![CDATA[ AND A.CUSTOMER_ID not in ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
			<![CDATA[ 
				GROUP BY a.Customer_Id
		     ]]>
		   
		  
		    
	</select>
	
	<!-- 查询移动电话一致的所有客户 P2740接口 
	是否与除变更信息的客户本人以外的本分公司及下辖机构其他有效保单（有效保单不包括终止保单。终止保单、万能险缓交保单不参与校验）
	和失效保单的【投保人】的手机号码一致
	有效保单投保人规则：排除保单保险期间小于等于一年的个单和团单的投保人[新增“团单”]。
	-->
	<select id="JRQD_PA_findAllAddressReaPhones" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 SELECT DISTINCT a.Customer_Id
		   FROM App___Pas__Dbuser.t_Address a
		  WHERE EXISTS 
		    (SELECT 1
		      FROM App___Pas__Dbuser.t_Policy_Holder Ph
		     WHERE a.Customer_Id = Ph.Customer_Id
		       AND Ph.Address_Id = a.Address_Id
		       AND EXISTS (SELECT 1
		              FROM App___Pas__Dbuser.t_Contract_Master Cm,App___Pas__Dbuser.t_Contract_Product CP
		             WHERE Ph.Policy_Id = Cm.Policy_Id AND Cm.Policy_Id=CP.POLICY_ID AND CP.IS_PAUSE<>1
		               AND Cm.Liability_State = '1']]>
		               <if test=" organ_code != null and organ_code !='' "><![CDATA[ and substr(cm.organ_code ,0,4) = #{organ_code} ]]></if>  
		               <![CDATA[        
					           			)
		                AND EXISTS
		                (SELECT 1
		                  FROM App___Pas__Dbuser.t_Contract_Busi_Prod Cbp
		                 WHERE Cbp.Policy_Id = Ph.Policy_Id
		                   AND Cbp.Master_Busi_Item_Id IS NULL
		                   AND EXISTS
		                 (SELECT 1
		                          FROM App___Pas__Dbuser.t_Business_Product Pro
		                         WHERE Cbp.Busi_Prd_Id = Pro.Business_Prd_Id
		                           AND Pro.Cover_Period_Type = '0'))) ]]>
          
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND A.FIXED_TEL = #{fixed_tel} ]]></if>	
		<if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>	
		<if test=" customer_id_list  != null and customer_id_list.size()!=0">
		<![CDATA[ AND A.CUSTOMER_ID NOT IN ]]>
		<foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
			#{cus}
		</foreach>
		</if>
		<![CDATA[  GROUP BY a.customer_id ]]>
		 
	</select>
	
    <!-- 根据地址id查询地址信息 -->
	<select id="JRQD_findAddressById" resultType="java.util.Map" parameterType="java.util.Map">
	 	<![CDATA[
	 	    SELECT TD.ADDRESS from APP___PAS__DBUSER.T_ADDRESS TD
	 	    WHERE ADDRESS_ID=#{address_id}
	 	]]>
	</select>
	
	<!-- 查询移动电话一致的所有客户(投保人、被保人、受益人) R3050接口 -->
	<select id="JRQD_PA_findAllAddressCustomers" resultType="java.util.Map" parameterType="java.util.Map">
		<bind name="organ_code" value="organ_code + '%'" />  
		<![CDATA[ 
		SELECT  A.CUSTOMER_ID  
	     FROM APP___PAS__DBUSER.T_ADDRESS A ,
	          APP___PAS__DBUSER.T_POLICY_HOLDER PH,
	          APP___PAS__DBUSER.T_CONTRACT_MASTER CM
	     WHERE A.CUSTOMER_ID = PH.CUSTOMER_ID 
	     AND PH.ADDRESS_ID = A.ADDRESS_ID
	     AND PH.POLICY_CODE=CM.POLICY_CODE
	     AND CM.LIABILITY_STATE='1'
	     AND CM.organ_code LIKE #{organ_code} 
		 ]]>
		<if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND CM.POLICY_CODE <>#{policy_code} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND A.FIXED_TEL = #{fixed_tel} ]]></if>
		<if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND A.OFFICE_TEL = #{office_tel} ]]></if>	
		<if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>		
		<if test=" customer_id_list  != null and customer_id_list.size()!=0">
		<![CDATA[ AND A.CUSTOMER_ID not in ]]>
		<foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
			#{cus}
		</foreach>
		<![CDATA[  GROUP BY a.customer_id ]]>
		</if>
		
		<![CDATA[  UNION   ]]>
		 <bind name="organ_code" value="organ_code + '%'" />  
		<![CDATA[  
		    SELECT  B.CUSTOMER_ID  
		     FROM APP___PAS__DBUSER.T_ADDRESS B ,
		          APP___PAS__DBUSER.T_INSURED_LIST IL,
		          APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		     WHERE B.CUSTOMER_ID = IL.CUSTOMER_ID
		     AND IL.ADDRESS_ID = B.ADDRESS_ID 
		     AND IL.POLICY_CODE=CM.POLICY_CODE
		     AND CM.LIABILITY_STATE='1'
		     AND CM.organ_code LIKE #{organ_code} 
		 ]]>
		<if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND CM.POLICY_CODE <>#{policy_code} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND B.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND B.FIXED_TEL = #{fixed_tel} ]]></if>	
		<if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND B.HOUSE_TEL = #{house_tel} ]]></if>	
		<if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND B.OFFICE_TEL = #{office_tel} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND B.COMPANY_NAME = #{company_name} ]]></if>		
		<if test=" customer_id_list  != null and customer_id_list.size()!=0">
		<![CDATA[ AND B.CUSTOMER_ID not in ]]>
		<foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
			#{cus}
		</foreach>
		<![CDATA[  GROUP BY B.customer_id ]]>
		</if>
		
		<![CDATA[  UNION   ]]>
		 <bind name="organ_code" value="organ_code + '%'" />  
		<![CDATA[  
		     SELECT  B.CUSTOMER_ID  
         FROM APP___PAS__DBUSER.T_ADDRESS B ,
              APP___PAS__DBUSER.T_CONTRACT_BENE BE,
              APP___PAS__DBUSER.T_CONTRACT_MASTER CM
         WHERE B.CUSTOMER_ID = BE.CUSTOMER_ID
         AND B.ADDRESS_ID = BE.ADDRESS_ID
         AND BE.POLICY_CODE =CM.POLICY_CODE
         AND CM.LIABILITY_STATE='1'
         AND CM.organ_code LIKE #{organ_code} 
		 ]]>
		<if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND CM.POLICY_CODE <>#{policy_code} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND B.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND B.FIXED_TEL = #{fixed_tel} ]]></if>	
		<if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND B.OFFICE_TEL = #{office_tel} ]]></if>	
		<if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND B.HOUSE_TEL = #{house_tel} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND B.COMPANY_NAME = #{company_name} ]]></if>		
		<if test=" customer_id_list  != null and customer_id_list.size()!=0">
		<![CDATA[ AND B.CUSTOMER_ID not in ]]>
		<foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
			#{cus}
		</foreach>
		<![CDATA[  GROUP BY B.customer_id ]]>
		</if>
	</select>
	
		<!-- 查询移动电话一致的所有客户 P2740接口 
	是否与除变更信息的客户本人以外的本分公司及下辖机构其他有效保单（排除保单保险期间小于等于一年的个单）的投保人的手机号码一致 。【失效保单参与校验】
	-->
	<select id="JRQD_PA_findAllAddressReaPhonesOfEndAndWanNeng" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT DISTINCT a.Customer_Id
			  FROM App___Pas__Dbuser.t_Address a
			 WHERE (EXISTS
			        (SELECT 1
			           FROM App___Pas__Dbuser.t_Policy_Holder Ph
			          WHERE a.Customer_Id = Ph.Customer_Id
			            AND Ph.Address_Id = a.Address_Id
			            AND EXISTS (SELECT 1
			                   FROM App___Pas__Dbuser.t_Contract_Master Cm
			                  WHERE Ph.Policy_Id = Cm.Policy_Id
			                    AND Cm.Liability_State not in ('3','4')
								]]>
			<if test=" organ_code != null and organ_code !='' "><![CDATA[ and substr(cm.organ_code ,0,4) = #{organ_code} ]]></if>                   
            <![CDATA[        
			           			)
			            AND EXISTS
			          (SELECT 1
			                   FROM App___Pas__Dbuser.t_Contract_Busi_Prod Cbp
			                  WHERE Cbp.Policy_Id = Ph.Policy_Id
			                    AND Cbp.Master_Busi_Item_Id IS NULL
			                    AND EXISTS
			                  (SELECT 1
			                           FROM App___Pas__Dbuser.t_Business_Product Pro
			                          WHERE Cbp.Busi_Prd_Id = Pro.Business_Prd_Id
			                            AND Pro.Cover_Period_Type = '0'
										AND PRO.PRODUCT_CATEGORY1 <> '20003'
										))) OR EXISTS
			        (SELECT 1
			           FROM App___Pas__Dbuser.t_Policy_Holder Ph
			          WHERE a.Customer_Id = Ph.Customer_Id
			            AND Ph.Address_Id = a.Address_Id
			            AND EXISTS (SELECT 1
			                   FROM App___Pas__Dbuser.t_Contract_Master Cm
			                  WHERE Ph.Policy_Id = Cm.Policy_Id
			                    AND Cm.Liability_State = '4' ]]>
			<if test=" organ_code != null and organ_code !='' "><![CDATA[ and substr(cm.organ_code ,0,4) = #{organ_code} ]]></if>                   
            <![CDATA[ 
			                    )
			            AND EXISTS
			          (SELECT 1
			                   FROM App___Pas__Dbuser.t_Contract_Busi_Prod Cbp
			                  WHERE Cbp.Policy_Id = Ph.Policy_Id
			                    AND Cbp.Master_Busi_Item_Id IS NULL
			                    AND EXISTS
			                  (SELECT 1
			                           FROM App___Pas__Dbuser.t_Business_Product Pro
			                          WHERE Cbp.Busi_Prd_Id = Pro.Business_Prd_Id
			                            AND Pro.Cover_Period_Type = '0'))) ) ]]>
		     <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND a.MOBILE_TEL = #{mobile_tel} ]]></if>
		     <if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND a.FIXED_TEL = #{fixed_tel} ]]></if>
		     <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND a.OFFICE_TEL = #{office_tel} ]]></if> 
		     <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND a.HOUSE_TEL = #{house_tel} ]]></if> 
		     <if test=" company_name != null and company_name != ''  "><![CDATA[ AND a.COMPANY_NAME = #{company_name} ]]></if> 
		     
		    <![CDATA[ AND A.CUSTOMER_ID not in ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
			<![CDATA[ 
				GROUP BY a.Customer_Id
		     ]]>
		 
	</select>
	
	<!-- add by hhb 查询山西机构下投保人的移动电话和固定电话 -->
	<!--  36661-713 -操作保单贷款回访电话校验有误  修改为‘仅校验系统记录的最新的电话’-->
	<select id="JRQD_findPolicyHolerCallPhone" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	 
	 SELECT * FROM (
	 SELECT AD.MOBILE_TEL,AD.FIXED_TEL FROM 
     DEV_PAS.T_POLICY_HOLDER PH
     LEFT JOIN DEV_PAS.T_CUSTOMER C ON C.CUSTOMER_ID=PH.CUSTOMER_ID
     LEFT JOIN DEV_PAS.T_ADDRESS AD ON AD.CUSTOMER_ID=C.CUSTOMER_ID
     WHERE 1=1 AND AD.ADDRESS_STATUS= 1 ]]> 
     <if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND PH.POLICY_CODE= #{policy_code} ]]></if>
     <![CDATA[ ORDER BY AD.UPDATE_TIME DESC ) A WHERE ROWNUM=1]]>
	</select>
	
	<!-- add by hhb 查询山西机构下被保人的移动电话和固定电话 -->
	<!--  36661-713 -操作保单贷款回访电话校验有误  修改为‘仅校验系统记录的最新的电话’-->
	<select id="JRQD_findInsuredListCallPhone" resultType="java.util.Map" parameterType="java.util.Map">
	 	<![CDATA[	 
	 	SELECT * FROM (
	 	SELECT AD.MOBILE_TEL,AD.FIXED_TEL FROM 
     DEV_PAS.T_POLICY_HOLDER PH
     LEFT JOIN DEV_PAS.T_CUSTOMER C ON C.CUSTOMER_ID=PH.CUSTOMER_ID
     LEFT JOIN DEV_PAS.T_ADDRESS AD ON AD.CUSTOMER_ID=C.CUSTOMER_ID
     WHERE 1=1 AND AD.ADDRESS_STATUS= 1 ]]> 
     <if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND PH.POLICY_CODE= #{policy_code} ]]></if>
     <![CDATA[ ORDER BY AD.UPDATE_TIME DESC ) A WHERE ROWNUM=1]]>
     <![CDATA[ UNION 
     SELECT * FROM (
     SELECT AD.MOBILE_TEL,AD.FIXED_TEL FROM 
     DEV_PAS.T_INSURED_LIST L
     LEFT JOIN DEV_PAS.T_CUSTOMER C ON C.CUSTOMER_ID=L.CUSTOMER_ID
     LEFT JOIN DEV_PAS.T_ADDRESS AD ON AD.CUSTOMER_ID=C.CUSTOMER_ID
     WHERE 1=1 AND AD.ADDRESS_STATUS= 1 ]]>
      <if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND l.POLICY_CODE= #{policy_code} ]]></if>
      <![CDATA[ ORDER BY AD.UPDATE_TIME DESC ) B WHERE ROWNUM=1]]>
	</select>
	
	
	<!-- 查询移动电话一致的所有客户(投保人、被保人) 客户信息真实性校验接口 -->
	<select id="JRQD_PA_findAllAddressCustomersOfSamePhoneAndMoblile" resultType="java.util.Map" parameterType="java.util.Map">
		  <![CDATA[ 
		    SELECT  A.CUSTOMER_ID  
		       FROM APP___PAS__DBUSER.T_ADDRESS A ,
		            APP___PAS__DBUSER.T_POLICY_HOLDER PH,
		            APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		       WHERE A.CUSTOMER_ID = PH.CUSTOMER_ID 
		       AND PH.ADDRESS_ID = A.ADDRESS_ID
		       AND PH.POLICY_CODE=CM.POLICY_CODE
		       AND CM.LIABILITY_STATE='1'
		     ]]>
		    <if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND CM.POLICY_CODE <>#{policy_code} ]]></if>
		    <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		    <if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND A.FIXED_TEL = #{fixed_tel} ]]></if>
		    <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND A.OFFICE_TEL = #{office_tel} ]]></if> 
		    <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
		    <if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>  
		    <if test=" customer_id_list  != null and customer_id_list.size()!=0">
		    <![CDATA[ AND A.CUSTOMER_ID not in ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
		   </if> 
		     <!-- add by zhangweiwei   先定义移动保全2.0传18 -->
		     <if test=" state != null and state != '' and state =='ydbq2.0'.toString() "><![CDATA[ and (select (select COVER_PERIOD_TYPE from dev_pas.t_business_product where business_prd_id=(select busi_prd_id from dev_pas.t_contract_busi_prod where policy_code=cm.policy_code and master_busi_item_id is null)) from dual)=0 ]]></if> 
		    <![CDATA[  GROUP BY a.customer_id ]]>

		
		    <![CDATA[  
		    UNION
		        SELECT  B.CUSTOMER_ID  
		         FROM APP___PAS__DBUSER.T_ADDRESS B ,
		              APP___PAS__DBUSER.T_INSURED_LIST IL,
		              APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		         WHERE B.CUSTOMER_ID = IL.CUSTOMER_ID
		         AND IL.ADDRESS_ID = B.ADDRESS_ID 
		         AND IL.POLICY_CODE=CM.POLICY_CODE
		         AND CM.LIABILITY_STATE='1'
		     ]]>
		    <if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND CM.POLICY_CODE <>#{policy_code} ]]></if>
		    <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND B.MOBILE_TEL = #{mobile_tel} ]]></if>
		    <if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND B.FIXED_TEL = #{fixed_tel} ]]></if> 
		    <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND B.HOUSE_TEL = #{house_tel} ]]></if> 
		    <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND B.OFFICE_TEL = #{office_tel} ]]></if> 
		    <if test=" company_name != null and company_name != ''  "><![CDATA[ AND B.COMPANY_NAME = #{company_name} ]]></if> 
		    <if test=" customer_id_list  != null and customer_id_list.size()!=0">
		    <![CDATA[ AND B.CUSTOMER_ID not in ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
		     </if> 
		    <!-- add by zhangweiwei   先定义移动保全2.0传18 -->
		     <if test=" state != null and state != '' and state =='ydbq2.0'.toString() "><![CDATA[ and (select (select COVER_PERIOD_TYPE from dev_pas.t_business_product where business_prd_id=(select busi_prd_id from dev_pas.t_contract_busi_prod where policy_code=cm.policy_code and master_busi_item_id is null)) from dual)=0 ]]></if> 
		    <![CDATA[  GROUP BY B.customer_id ]]>
    
	</select>
	
	<!-- 查询移动电话一致的所有客户(投保人、被保人) 客户信息真实性校验接口 -->
	<select id="JRQD_CUS_findAllAddressForOut" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[ 
		    SELECT A.CUSTOMER_ID,
			       A.MOBILE_TEL,
			       A.FIXED_TEL,
			       (SELECT T.NAME FROM APP___PAS__DBUSER.t_district T WHERE T.CODE = A.STATE) AS STATE,
			       (SELECT T.NAME FROM APP___PAS__DBUSER.t_district T WHERE T.CODE = A.CITY) AS CITY,
			       (SELECT T.NAME FROM APP___PAS__DBUSER.t_district T WHERE T.CODE = A.DISTRICT) AS DISTRICT,
			       A.ADDRESS
			  FROM APP___PAS__DBUSER.T_ADDRESS         A,
			       APP___PAS__DBUSER.T_POLICY_HOLDER   PH,
			       APP___PAS__DBUSER.T_CONTRACT_MASTER CM
			 WHERE A.CUSTOMER_ID = PH.CUSTOMER_ID
			   AND PH.ADDRESS_ID = A.ADDRESS_ID
			   AND PH.POLICY_CODE = CM.POLICY_CODE
		--	   AND CM.LIABILITY_STATE = '1'
		     ]]>
		     <if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
		     <if test=" customer_id!= null and customer_id!= ''  "><![CDATA[ AND A.customer_id = #{customer_id} ]]></if>
		     <![CDATA[  
		    UNION
			SELECT A.CUSTOMER_ID,
			       A.MOBILE_TEL,
			       A.FIXED_TEL,
			       (SELECT T.NAME FROM APP___PAS__DBUSER.t_district T WHERE T.CODE = A.STATE) AS STATE,
			       (SELECT T.NAME FROM APP___PAS__DBUSER.t_district T WHERE T.CODE = A.CITY) AS CITY,
			       (SELECT T.NAME FROM APP___PAS__DBUSER.t_district T WHERE T.CODE = A.DISTRICT) AS DISTRICT,
			       A.ADDRESS
			  FROM APP___PAS__DBUSER.T_ADDRESS         A,
			       APP___PAS__DBUSER.T_INSURED_LIST    IL,
			       APP___PAS__DBUSER.T_CONTRACT_MASTER CM
			 WHERE A.CUSTOMER_ID = IL.CUSTOMER_ID
			   AND IL.ADDRESS_ID = A.ADDRESS_ID
			   AND IL.POLICY_CODE = CM.POLICY_CODE
		--	   AND CM.LIABILITY_STATE = '1'
		     ]]>
		     <if test=" policy_code!= null and policy_code!= ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
		     <if test=" customer_id!= null and customer_id!= ''  "><![CDATA[ AND A.customer_id = #{customer_id} ]]></if>
	</select>
	<select id="JRQD_queryType" resultType="java.util.Map" parameterType="java.util.Map">
	select COVER_PERIOD_TYPE as winning_start_flag
                          from dev_pas.t_business_product
                         where business_prd_id =
                               (select busi_prd_id
                                  from dev_pas.t_contract_busi_prod
                                 where policy_code = #{policy_code}
                                   and master_busi_item_id is null)
	</select>
	<!--个人保单信息查询P00001000307  -->
	<select id="JRQD_queryinsuredAddressById" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[ 
  select
 ((select b.name from APP___PAS__DBUSER.t_District b where b.code = a.state)||
 (select b.name from APP___PAS__DBUSER.t_District b where b.code = a.city)||
 (select b.name from APP___PAS__DBUSER.t_District b where b.code = a.district)||
  a.address) as insuredaddress

  from APP___PAS__DBUSER.t_Address  a
 where a.address_id =#{address_id}
	 
	   ]]>
	</select>
	<!-- 查询客户存在保单关联的地址ID进行抄单 -->
	<select id="JRQD_PA_findAllAssociatedPolicyAddressByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT ROWNUM,C.* FROM  ( SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL FROM APP___PAS__DBUSER.T_ADDRESS A WHERE 1 = 1 
			AND EXISTS (
 select 1 from dev_pas.t_POLICY_HOLDER B WHERE  A.CUSTOMER_ID = B.CUSTOMER_ID AND A.ADDRESS_ID = B.ADDRESS_ID
 ) ]]>
		<include refid="JRQD_PA_queryAddressByCustomerIdCondition" />
		<![CDATA[ UNION SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL FROM APP___PAS__DBUSER.T_ADDRESS A WHERE 1 = 1 
			AND EXISTS (
 select 1 from dev_pas.t_Insured_List B WHERE  A.CUSTOMER_ID = B.CUSTOMER_ID AND A.ADDRESS_ID = B.ADDRESS_ID
 ) ]]>
		<include refid="JRQD_PA_queryAddressByCustomerIdCondition" />
		<![CDATA[ UNION SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES, A.RETURN_MAIL_REASON, 
			A.MOBILE_TEL, A.EMAIL, A.DISTRICT, A.ADDRESS, A.COUNTRY_CODE, 
			A.ADDRESS_STATUS, A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, 
			A.FIXED_TEL, A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL FROM APP___PAS__DBUSER.T_ADDRESS A WHERE 1 = 1 
			AND EXISTS (
 select 1 from dev_pas.T_CONTRACT_BENE B WHERE  A.CUSTOMER_ID = B.CUSTOMER_ID AND A.ADDRESS_ID = B.ADDRESS_ID
 ) ]]>
		<include refid="JRQD_PA_queryAddressByCustomerIdCondition" />
		<![CDATA[ ) C]]>
	</select>
	
	<!-- 查询所有操作(排序) -->
  <select id="JRQD_queryAddressNewByCustId" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ SELECT A.* FROM
		(SELECT A.ADDRESS_ID, A.STATE, A.CUSTOMER_ID, A.FOREIGN_INDI, A.MAIL_FAILED_TIMES,
		A.RETURN_MAIL_REASON, A.MOBILE_TEL, A.EMAIL, A.DISTRICT, 
		A.ADDRESS, A.COUNTRY_CODE, A.ADDRESS_STATUS, 
		A.POST_CODE, A.RETURN_MAIL_DATE, A.ADDRESS_TYPE, A.CONFIRMED, A.CITY, A.FIXED_TEL, 
		A.VALIDITY_START_DATE, A.FAX_TEL,A.HOUSE_TEL,A.OFFICE_TEL,A.COMPANY_NAME,A.INSERT_TIMESTAMP 
		FROM APP___PAS__DBUSER.T_ADDRESS A WHERE 1=1]]>
    <include refid="JRQD_PA_addressWhereCondition" />
    <![CDATA[ORDER BY A.INSERT_TIMESTAMP DESC) A WHERE ROWNUM = 1 ]]>
  </select>
	
	
	<select id="JRQD_PA_findAllcounttbrEmail" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[ select count(1)
	         from (select distinct b.customer_id, c.email
	          from APP___PAS__DBUSER.t_policy_holder a,
	               APP___PAS__DBUSER.t_customer      b,
	               APP___PAS__DBUSER.t_address       c
	         where a.customer_id = b.customer_id
	           and a.address_id = c.address_id
	           and b.customer_id = c.customer_id
	           and exists (select 1
	                  from APP___PAS__DBUSER.t_contract_master ta
	                 where a.policy_id = ta.policy_id
	                 and ta.liability_state = 1)               
	         ]]>
	    <if test=" email !=null "><![CDATA[ AND c.email = #{email} ]]></if>
		<if test=" customer_id !=null "><![CDATA[ AND b.customer_id = #{customer_id} ]]></if>
		<![CDATA[)]]>
	</select>
	
	<select id="JRQD_PA_findAllcounttbrEmailjk" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[ select count(1)
	         from (select distinct b.customer_id, c.email
	          from APP___PAS__DBUSER.t_policy_holder a,
	               APP___PAS__DBUSER.t_customer      b,
	               APP___PAS__DBUSER.t_address       c
	         where a.customer_id = b.customer_id
	           and a.address_id = c.address_id
	           and b.customer_id = c.customer_id
	           and exists (select 1
	                  from APP___PAS__DBUSER.t_contract_master ta
	                 where a.policy_id = ta.policy_id
	                 and ta.liability_state = 1)               
	         ]]>
	    <if test=" email !=null "><![CDATA[ AND c.email = #{email} ]]></if>
		<if test=" customer_id !=null "><![CDATA[ AND b.customer_id != #{customer_id} ]]></if>
		<![CDATA[)]]>
	</select>
		
</mapper>
