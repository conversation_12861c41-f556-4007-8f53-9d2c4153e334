<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.ContractBusiProdDaoImpl">

	<sql id="JRQD_PA_contractBusiProdWhereCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" gurnt_start_date  != null  and  gurnt_start_date  != ''  "><![CDATA[ AND A.GURNT_START_DATE = #{gurnt_start_date} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" gurnt_period  != null "><![CDATA[ AND A.GURNT_PERIOD = #{gurnt_period} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" hesitation2acc  != null "><![CDATA[ AND A.HESITATION2ACC = #{hesitation2acc} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" settle_method  != null "><![CDATA[ AND A.SETTLE_METHOD = #{settle_method} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" flight_no != null and flight_no != ''  "><![CDATA[ AND A.FLIGHT_NO = #{flight_no} ]]></if>
		<if test=" waiver  != null "><![CDATA[ AND A.WAIVER = #{waiver} ]]></if>
		<if test=" gurnt_rate  != null "><![CDATA[ AND A.GURNT_RATE = #{gurnt_rate} ]]></if>
		<if test=" master_busi_item_id  != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" renewal_state  != null "><![CDATA[ AND A.RENEWAL_STATE = #{renewal_state} ]]></if>
		<if test=" not_renewal_state  != null "><![CDATA[ AND A.RENEWAL_STATE != #{not_renewal_state} ]]></if>
		<if test=" assurerenew_flag  != null "><![CDATA[ AND A.ASSURERENEW_FLAG = #{assurerenew_flag} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" renew  != null "><![CDATA[ AND A.RENEW = #{renew} ]]></if>
		<if test=" renew_times  != null "><![CDATA[ AND A.RENEW_TIMES = #{renew_times} ]]></if>
		<if test=" waiver_end  != null  and  waiver_end  != ''  "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" gurnt_perd_type != null and gurnt_perd_type != ''  "><![CDATA[ AND A.GURNT_PERD_TYPE = #{gurnt_perd_type} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" joint_life_flag  != null "><![CDATA[ AND A.JOINT_LIFE_FLAG = #{joint_life_flag} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" due_lapse_date  != null  and  due_lapse_date  != ''  "><![CDATA[ AND A.DUE_LAPSE_DATE = #{due_lapse_date} ]]></if>
		<if test=" waiver_start  != null  and  waiver_start  != ''  "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" prd_pkg_code != null and prd_pkg_code != ''  "><![CDATA[ AND A.PRD_PKG_CODE = #{prd_pkg_code} ]]></if>
		<if test=" gurnt_renew_end  != null  and  gurnt_renew_end  != ''  "><![CDATA[ AND A.GURNT_RENEW_END = #{gurnt_renew_end} ]]></if>
		<if test=" gurnt_renew_start  != null  and  gurnt_renew_start  != ''  "><![CDATA[ AND A.GURNT_RENEW_START = #{gurnt_renew_start} ]]></if>
		<if test=" reinsured  != null  and  reinsured  != ''  "><![CDATA[ AND A.REINSURED = #{reinsured} ]]></if>
		<if test=" can_change_flag  != null  "><![CDATA[ AND A.CAN_CHANGE_FLAG = #{can_change_flag} ]]></if>
		<if test=" can_reinsure_flag  != null  "><![CDATA[ AND A.CAN_REINSURE_FLAG = #{can_reinsure_flag} ]]></if>
		<if test=" charge_flag  != null  "><![CDATA[ AND A.CHARGE_FLAG = #{charge_flag} ]]></if>
		<if test=" cond_pre_check_flag  != null  "><![CDATA[ AND A.COND_PRE_CHECK_FLAG = #{cond_pre_check_flag} ]]></if>

		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_ID in (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test="policy_code_list  != null and policy_code_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_code in (]]>
			<foreach collection="policy_code_list" item="policy_code_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" old_pol_no != null and old_pol_no != ''  "><![CDATA[ AND A.OLD_POL_NO = #{old_pol_no} ]]></if>
		<if test=" polno != null and polno != ''  "><![CDATA[ 
			AND NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID)=#{polno} 
		]]></if>
	<!-- 退保标识， SURRENDER_FLAG NUMBER(1) surrender_flag-->
	   <if test=" surrender_flag  != null  and  surrender_flag  != ''  "><![CDATA[ AND A.SURRENDER_FLAG = #{surrender_flag} ]]></if>
		
	   <if test=" is_rpu  != null "><![CDATA[ AND A.IS_RPU = #{is_rpu} ]]></if>
	   <if test=" hesitation_period_day  != null "><![CDATA[ AND A.HESITATION_PERIOD_DAY = #{hesitation_period_day} ]]></if>
	   
	   <if test="busi_Prod_Code_list != null and busi_Prod_Code_list.size() !=0">
	   	   <![CDATA[ AND BUSI_PROD_CODE IN ]]>	
	       <foreach collection="busi_Prod_Code_list" item="item" index="index" open="(" separator="," close=")">
	           <![CDATA[ #{item} ]]>
	       </foreach>		
	   </if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_queryContractBusiProdByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryContractBusiProdByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryContractBusiProdByBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryContractBusiProdByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID= #{policy_id} ]]></if>
	</sql>
	<sql id="JRQD_PA_queryContractBusiProdByProductCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE in(#{busi_prod_code}) ]]></if>
	</sql>
	<sql id="JRQD_PA_contractBusiProdByMasterBusiProdCondition">
		<if test = " master_busi_item_id  != null"><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id}]]></if>
	</sql>
	<sql id="JRQD_PA_queryRiskAmountByBusiCode">
			<if test="busi_prod_code_list != null and busi_prod_code_list.size()!=0"><![CDATA[ AND A.BUSI_PROD_CODE IN]]>
				<foreach collection="busi_prod_code_list" item="busi_prod_code_list"
					index="index" open="(" close=")" separator=",">
					#{busi_prod_code_list}
				</foreach>
			</if>
	</sql>

<!-- 添加操作 -->
	<insert id="JRQD_PA_addContractBusiProd"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="busi_item_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_BUSI_PROD__BUSI_ITE.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD(
				IS_RPU,GURNT_START_DATE, BUSI_PRD_ID, GURNT_PERIOD, BUSI_PROD_CODE, HESITATION2ACC, APPLY_CODE, IS_WAIVED, 
				SETTLE_METHOD, UPDATE_BY, POLICY_ID, FLIGHT_NO, WAIVER, GURNT_RATE, MASTER_BUSI_ITEM_ID, 
				UPDATE_TIME, PAIDUP_DATE, EXPIRY_DATE, LIABILITY_STATE, POLICY_CODE, RERINSTATE_DATE, RENEW_DECISION, 
				VALIDATE_DATE, UPDATE_TIMESTAMP, RENEWAL_STATE, ASSURERENEW_FLAG, INSERT_BY, APL_PERMIT, APPLY_DATE, 
				INITIAL_VALIDATE_DATE, RENEW, INSERT_TIMESTAMP, RENEW_TIMES, WAIVER_END, MATURITY_DATE, GURNT_PERD_TYPE, 
				BUSI_ITEM_ID, LAPSE_DATE, JOINT_LIFE_FLAG, INSERT_TIME, END_CAUSE, ISSUE_DATE, LAPSE_CAUSE, 
				DECISION_CODE, SUSPEND_DATE, SUSPEND_CAUSE, INITIAL_PREM_DATE, DUE_LAPSE_DATE, WAIVER_START, PRD_PKG_CODE, 
				GURNT_RENEW_END, GURNT_RENEW_START ,OLD_POL_NO,SURRENDER_FLAG,hesitation_period_day,
				CAN_CHANGE_FLAG,CAN_REINSURE_FLAG,CHARGE_FLAG,REINSURED,ORDER_ID,MEET_POV_STANDARD_FLAG)
			VALUES (
				 #{is_rpu, jdbcType=NUMERIC} ,#{gurnt_start_date, jdbcType=DATE}, #{busi_prd_id, jdbcType=NUMERIC} , #{gurnt_period, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{hesitation2acc, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{is_waived, jdbcType=NUMERIC} 
				, #{settle_method, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{flight_no, jdbcType=VARCHAR} , #{waiver, jdbcType=NUMERIC} , #{gurnt_rate, jdbcType=NUMERIC} , #{master_busi_item_id, jdbcType=NUMERIC} 
				, SYSDATE , #{paidup_date, jdbcType=DATE} , #{expiry_date, jdbcType=DATE} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{renew_decision, jdbcType=NUMERIC} 
				, #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				]]>	
			 <if test=" renewal_state != null"><![CDATA[,#{renewal_state, jdbcType=NUMERIC}]]></if>
             <if test=" renewal_state == null"><![CDATA[, default]]></if>
			<![CDATA[
				, #{assurerenew_flag, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{apl_permit, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} 
				, #{initial_validate_date, jdbcType=DATE} , #{renew, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{renew_times, jdbcType=NUMERIC} , #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{gurnt_perd_type, jdbcType=VARCHAR} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} , #{joint_life_flag, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , #{lapse_cause, jdbcType=VARCHAR} 
				, #{decision_code, jdbcType=VARCHAR} , #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{initial_prem_date, jdbcType=DATE} , #{due_lapse_date, jdbcType=DATE} , #{waiver_start, jdbcType=DATE} , #{prd_pkg_code, jdbcType=VARCHAR} 
				, #{gurnt_renew_end, jdbcType=DATE} , #{gurnt_renew_start, jdbcType=DATE} ,#{old_pol_no, jdbcType=VARCHAR},#{surrender_flag, jdbcType=NUMERIC},#{hesitation_period_day, jdbcType=NUMERIC}
				, #{can_change_flag, jdbcType=NUMERIC}, #{can_reinsure_flag, jdbcType=NUMERIC}
		        , #{charge_flag, jdbcType=NUMERIC}, #{REINSURED, jdbcType=VARCHAR},#{order_id, jdbcType=VARCHAR},#{meet_pov_standard_flag,jdbcType=NUMERIC}) 
			]]>	
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteContractBusiProd" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID =#{busi_item_id } ]]>
	</delete>
	

<!-- 修改操作 -->
	<update id="JRQD_PA_updateContractBusiProd" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD ]]>
		<set>
		<trim suffixOverrides=",">
			IS_RPU = #{is_rpu, jdbcType=NUMERIC} ,
		    GURNT_START_DATE = #{gurnt_start_date, jdbcType=DATE} ,
		    BUSI_PRD_ID = #{busi_prd_id, jdbcType=NUMERIC} ,
		    GURNT_PERIOD = #{gurnt_period, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    HESITATION2ACC = #{hesitation2acc, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
		    SETTLE_METHOD = #{settle_method, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			FLIGHT_NO = #{flight_no, jdbcType=VARCHAR} ,
		    WAIVER = #{waiver, jdbcType=NUMERIC} ,
		    GURNT_RATE = #{gurnt_rate, jdbcType=NUMERIC} ,
		    MASTER_BUSI_ITEM_ID = #{master_busi_item_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
		    RENEW_DECISION = #{renew_decision, jdbcType=NUMERIC} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    RENEWAL_STATE = #{renewal_state, jdbcType=NUMERIC} ,
		    ASSURERENEW_FLAG = #{assurerenew_flag, jdbcType=NUMERIC} ,
		    APL_PERMIT = #{apl_permit, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    INITIAL_VALIDATE_DATE =  #{initial_validate_date, jdbcType=DATE} ,
		    RENEW = #{renew, jdbcType=NUMERIC} ,
		    RENEW_TIMES = #{renew_times, jdbcType=NUMERIC} ,
		    WAIVER_END = #{waiver_end, jdbcType=DATE} ,
		    MATURITY_DATE = #{maturity_date, jdbcType=DATE} ,
			GURNT_PERD_TYPE = #{gurnt_perd_type, jdbcType=VARCHAR} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
		    JOINT_LIFE_FLAG = #{joint_life_flag, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		    ISSUE_DATE = #{issue_date, jdbcType=DATE} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
		    INITIAL_PREM_DATE = #{initial_prem_date, jdbcType=DATE} ,
		    DUE_LAPSE_DATE = #{due_lapse_date, jdbcType=DATE} ,
		    WAIVER_START = #{waiver_start, jdbcType=DATE} ,
			PRD_PKG_CODE = #{prd_pkg_code, jdbcType=VARCHAR} ,
		    GURNT_RENEW_END = #{gurnt_renew_end, jdbcType=DATE} ,
		    GURNT_RENEW_START = #{gurnt_renew_start, jdbcType=DATE} ,
		    OLD_POL_NO = #{old_pol_no, jdbcType=VARCHAR} ,
		    SURRENDER_FLAG = #{surrender_flag, jdbcType=NUMERIC} ,
		    HESITATION_PERIOD_DAY = #{hesitation_period_day, jdbcType=NUMERIC},
		    NEXT_FLAG = #{next_flag, jdbcType=NUMERIC},
		    CAN_CHANGE_FLAG = #{can_change_flag, jdbcType=NUMERIC},
		    CAN_REINSURE_FLAG = #{can_reinsure_flag, jdbcType=NUMERIC},
		    CHARGE_FLAG = #{charge_flag, jdbcType=NUMERIC},
		    REINSURED = #{reinsured, jdbcType=VARCHAR} ,
		    COND_PRE_CHECK_FLAG = #{cond_pre_check_flag, jdbcType=NUMERIC},
		    ORDER_ID = #{order_id, jdbcType=VARCHAR} ,
		    MEET_POV_STANDARD_FLAG = #{meet_pov_standard_flag,jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ]]>
	</update>
	
	<update id="JRQD_PA_updateContractBusiProdEndCause" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD ]]>
			<set>
				<trim suffixOverrides=",">
					END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
				</trim>
			</set>
		<![CDATA[ WHERE BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ]]>
	</update>
<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findContractBusiProdByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
	<include refid="JRQD_PA_queryContractBusiProdByBusiItemIdCondition" />
	<include refid="JRQD_PA_contractBusiProdWhereCondition" />
	</select>
	<!-- 投连价格查询接口 -->
	<select id="JRQD_PA_findContractBusiProdByBusiItemId1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE ROWNUM = 1  and BUSI_ITEM_ID = #{busi_item_id} ]]>
		
	</select>
	
	<select id="JRQD_PA_findContractBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START, A.OLD_POL_NO,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryContractBusiProdByPolicyCodeCondition" />
	</select>
	
	<select id="JRQD_PA_findContractBusiProdByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1    ]]>
		<include refid="JRQD_PA_queryContractBusiProdByBusiProdCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>
	<!-- 按map查询操作 -->
	<select id="JRQD_PA_findHealthInsurance" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG,  
			(SELECT TCI.CONSTANTS_VALUE FROM APP___PAS__DBUSER.T_CONSTANTS_INFO TCI WHERE TCI.CONSTANTS_KEY 
			 IN ('SHORT_PRODUCT_ZD', 'SHORT_PRODUCT_I', 'SHORT_PRODUCT_PT') AND TCI.CONSTANTS_VALUE = A.BUSI_PROD_CODE) SHORT_PRODUCT
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A 
			WHERE ROWNUM <=  1000 
		 ]]>
		 <include refid="JRQD_PA_contractBusiProdWhereCondition" />
	</select>
<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  P.PRODUCT_NAME_STD BUSI_ITEM_NAME,
		P.PRODUCT_ABBR_NAME BUSI_ABBR_NAME,
       A.BUSI_ITEM_ID,
       A.BUSI_PRD_ID,
       A.BUSI_PROD_CODE,
       A.APPLY_CODE,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.MASTER_BUSI_ITEM_ID,
       A.OLD_POL_NO,
       A.VALIDATE_DATE,
       A.INITIAL_VALIDATE_DATE,
       A.INITIAL_PREM_DATE,
       A.LIABILITY_STATE,
       A.LAPSE_CAUSE,
       A.LAPSE_DATE,
       A.DUE_LAPSE_DATE,
       A.RERINSTATE_DATE,
       A.SUSPEND_CAUSE,
       A.SUSPEND_DATE,
       A.END_CAUSE,
       A.EXPIRY_DATE,
       A.DECISION_CODE,
       A.ISSUE_DATE,
       A.MATURITY_DATE,
       A.JOINT_LIFE_FLAG,
       A.RENEW,
       A.ASSURERENEW_FLAG,
       A.GURNT_RENEW_START,
       A.GURNT_RENEW_END,
       A.HESITATION2ACC,
       A.APPLY_DATE,
       A.WAIVER,
       A.IS_WAIVED,
       A.WAIVER_START,
       A.WAIVER_END,
       A.RENEW_DECISION,
       A.RENEW_TIMES,
       A.RENEWAL_STATE,
       A.APL_PERMIT,
       A.PRD_PKG_CODE,
       A.PAIDUP_DATE,
       A.GURNT_START_DATE,
       A.GURNT_PERD_TYPE,
       A.GURNT_PERIOD,
       A.GURNT_RATE,
       A.SETTLE_METHOD,
       A.SURRENDER_FLAG,
       A.FLIGHT_NO,
       A.HESITATION_PERIOD_DAY,
       A.IS_RENEWAL_SWITCH,
       A.IS_RPU,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.NEXT_FLAG,
       A.REINSURED,
       A.CAN_CHANGE_FLAG,
       '1' AS ORDER_ID,
       A.MEET_POV_STANDARD_FLAG,
       A.CAN_REINSURE_FLAG,
       A.CHARGE_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A , APP___PAS__DBUSER.T_BUSINESS_PRODUCT P
       WHERE ROWNUM <=  1000  
       AND P.BUSINESS_PRD_ID = A.BUSI_PRD_ID
       ]]>
			
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
		<include refid="JRQD_PA_queryRiskAmountByBusiCode" />
		<![CDATA[ORDER BY /* A.order_id, */ A.master_busi_item_id DESC,A.VALIDATE_DATE DESC]]>
		<!-- <![CDATA[ORDER BY A.master_busi_item_id DESC]]> -->
	</select>
	<!-- 查询保单下有效的849附加险信息 -->
	<select id="JRQD_PA_findAllAdditionContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_ITEM_ID,A.liability_state
			       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A 
			       WHERE A.BUSI_PROD_CODE = '00849000' and A.liability_state = 1 and A.POLICY_CODE = #{policy_code}]]>
       
	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllContractBusiProdForMsg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  (SELECT P.PRODUCT_NAME_STD
           FROM DEV_PAS.T_BUSINESS_PRODUCT P
          WHERE P.BUSINESS_PRD_ID = A.BUSI_PRD_ID) BUSI_ITEM_NAME,
          A.BUSI_ITEM_ID,
       A.BUSI_PRD_ID,
       A.BUSI_PROD_CODE,
       A.APPLY_CODE,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.MASTER_BUSI_ITEM_ID,
       A.OLD_POL_NO,
       A.VALIDATE_DATE,
       A.INITIAL_VALIDATE_DATE,
       A.INITIAL_PREM_DATE,
       A.LIABILITY_STATE,
       A.LAPSE_CAUSE,
       A.LAPSE_DATE,
       A.DUE_LAPSE_DATE,
       A.RERINSTATE_DATE,
       A.SUSPEND_CAUSE,
       A.SUSPEND_DATE,
       A.END_CAUSE,
       A.EXPIRY_DATE,
       A.DECISION_CODE,
       A.ISSUE_DATE,
       A.MATURITY_DATE,
       A.JOINT_LIFE_FLAG,
       A.RENEW,
       A.ASSURERENEW_FLAG,
       A.GURNT_RENEW_START,
       A.GURNT_RENEW_END,
       A.HESITATION2ACC,
       A.APPLY_DATE,
       A.WAIVER,
       A.IS_WAIVED,
       A.WAIVER_START,
       A.WAIVER_END,
       A.RENEW_DECISION,
       A.RENEW_TIMES,
       A.RENEWAL_STATE,
       A.APL_PERMIT,
       A.PRD_PKG_CODE,
       A.PAIDUP_DATE,
       A.GURNT_START_DATE,
       A.GURNT_PERD_TYPE,
       A.GURNT_PERIOD,
       A.GURNT_RATE,
       A.SETTLE_METHOD,
       A.SURRENDER_FLAG,
       A.FLIGHT_NO,
       A.HESITATION_PERIOD_DAY,
       A.IS_RENEWAL_SWITCH,
       A.IS_RPU,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.NEXT_FLAG,
       A.REINSURED,
       '1' AS ORDER_ID,
       A.MEET_POV_STANDARD_FLAG,
       A.CAN_CHANGE_FLAG,
       A.CAN_REINSURE_FLAG,
       A.CHARGE_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
			
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
		<![CDATA[ AND A.BUSI_PROD_CODE IN (SELECT TCI.CONSTANTS_VALUE FROM APP___PAS__DBUSER.T_CONSTANTS_INFO TCI 
           WHERE TCI.CONSTANTS_KEY IN ('SHORT_PRODUCT_ZD', 'SHORT_PRODUCT_I', 'SHORT_PRODUCT_PT'))
             ORDER BY A.master_busi_item_id DESC,A.VALIDATE_DATE DESC]]>
		<!-- <![CDATA[ORDER BY A.master_busi_item_id DESC]]> -->
	</select>
	
	<!-- add by dugang  查询所有操作 -->
	<select id="JRQD_PA_findAllContractBusiProdNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			(SELECT P.PRODUCT_NAME_STD FROM DEV_PAS.T_BUSINESS_PRODUCT P WHERE P.BUSINESS_PRD_ID = A.BUSI_PRD_ID) BUSI_ITEM_NAME,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000 and A.LIABILITY_STATE <> '3' ]]>
			
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
		<include refid="JRQD_PA_queryRiskAmountByBusiCode" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC,A.VALIDATE_DATE DESC]]>
		<!-- <![CDATA[ORDER BY A.master_busi_item_id DESC]]> -->
	</select>
<!-- 查询该险种的已交保费 -->
	<select id="JRQD_PA_findtotalFeeAmountForContractBusi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT NVL(SUM(CP.TOTAL_PREM_AF),0) AS IS_WAIVED FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT CP 
			WHERE CP.POLICY_ID = #{policy_id}
				AND CP.BUSI_ITEM_ID = #{busi_item_id}
			]]>
	</select>

<!-- 附加险满期降低保额续保条件:险种状态有效,险种状态终止 -->
<select id="JRQD_PA_findAllContractBusiProd_cjk" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
SELECT A.RENEW,A.LIABILITY_STATE,A.SUSPEND_CAUSE, B.COVER_PERIOD_TYPE,A.RENEW_DECISION,A.OLD_POL_NO,A.BUSI_ITEM_ID,A.BUSI_PRD_ID,A.BUSI_PROD_CODE,A.Expiry_Date AS VALIDATE_DATE,A.RENEW_TIMES,
  C.COVERAGE_PERIOD,C.COVERAGE_YEAR,A.POLICY_ID 
  
  from APP___PAS__DBUSER.T_conTRACT_BUSI_PROD A,dev_pas.T_BUSINESS_PRODUCT B,dev_pas.T_CONTRACT_PRODUCT C

  
 WHERE 1=1  and A.MASTER_BUSI_ITEM_ID IS NOT NULL AND  A.BUSI_PRD_ID=B.BUSINESS_PRD_ID  AND A.BUSI_ITEM_ID=C.BUSI_ITEM_ID
 AND A.LIABILITY_STATE in ('1','3')
  ]]>
<include refid="JRQD_PA_contractBusiProdWhereCondition" />
</select>
<!-- 查询个数操作 -->
	<select id="JRQD_PA_findContractBusiProdTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryContractBusiProdForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.IS_RPU,B.HESITATION_PERIOD_DAY, B.GURNT_START_DATE, B.BUSI_PRD_ID, B.GURNT_PERIOD, B.BUSI_PROD_CODE, B.HESITATION2ACC, B.APPLY_CODE, B.IS_WAIVED, 
			B.SETTLE_METHOD, B.POLICY_ID, B.FLIGHT_NO, B.WAIVER, B.GURNT_RATE, B.MASTER_BUSI_ITEM_ID, 
			B.PAIDUP_DATE, B.EXPIRY_DATE, B.LIABILITY_STATE, B.POLICY_CODE, B.RERINSTATE_DATE, B.RENEW_DECISION, 
			B.VALIDATE_DATE, B.RENEWAL_STATE, B.ASSURERENEW_FLAG, B.APL_PERMIT, B.APPLY_DATE, B.ORDER_ID,
			B.INITIAL_VALIDATE_DATE, B.RENEW, B.RENEW_TIMES, B.WAIVER_END, B.MATURITY_DATE, B.GURNT_PERD_TYPE, 
			B.BUSI_ITEM_ID, B.LAPSE_DATE, B.JOINT_LIFE_FLAG, B.END_CAUSE, B.ISSUE_DATE, B.LAPSE_CAUSE, 
			B.DECISION_CODE, B.SUSPEND_DATE, B.SUSPEND_CAUSE, B.INITIAL_PREM_DATE, B.DUE_LAPSE_DATE, B.WAIVER_START, B.PRD_PKG_CODE, 
			B.GURNT_RENEW_END, B.GURNT_RENEW_START FROM (
					SELECT ROWNUM RN,A.IS_RPU, A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <= #{LESS_NUM}  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

<!--  -->
	<select id="JRQD_PA_findMainBusiProdByPolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
			<![CDATA[AND A.MASTER_BUSI_ITEM_ID IS NULL]]>
		<include refid="JRQD_PA_queryContractBusiProdByPolicyIdCondition" />
		
		<![CDATA[ ORDER BY A.MATURITY_DATE DESC]]>
	</select>
	
	<!--  -->
	<select id="JRQD_PA_findAddBusiProdByPolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[  SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
			<![CDATA[AND A.MASTER_BUSI_ITEM_ID IS NOT NULL]]>
		<include refid="JRQD_PA_queryContractBusiProdByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID ]]>
	</select>
	
	<select id="JRQD_PA_findContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
	</select>
    
        <!-- 短信调用-查询险种责任终止日期 -->
    <select id="JRQD_PA_findAddBusiProdByPolicyIdforMessage"  resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
        <include refid="JRQD_PA_queryContractBusiProdByPolicyIdCondition" />
        <include refid="JRQD_PA_contractBusiProdWhereCondition" />
        <![CDATA[ ORDER BY A.BUSI_ITEM_ID ]]>
    </select>
	
	<!-- 通过主险id查询附加险 -->
	<select id="JRQD_PA_findAllContractBusiProdBuMasterBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>			
		<include refid="JRQD_PA_queryContractBusiProdByBusiItemIdCondition" />
		<include refid="JRQD_PA_contractBusiProdByMasterBusiProdCondition" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC]]>
	</select>
		
	<!-- 长期险续保通过保单号码和产品code查询险种信息 -->
	<select id="JRQD_PA_findContractBusiProdByPolicyIdAndPrdCod" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
		<include refid="JRQD_PA_queryContractBusiProdByPolicyIdCondition" />
		<include refid="JRQD_PA_queryContractBusiProdByProductCodeCondition" />
	</select>
	
	<!-- 转保单专用 -->
	<insert id="JRQD_PA_addContractBusiProdCreatePolicy"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="busi_item_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_BUSI_PROD__BUSI_ITE.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD(
				IS_RPU,GURNT_RENEW_START, GURNT_RENEW_END, GURNT_START_DATE, APL_PERMIT, GURNT_PERIOD, BUSI_PRD_ID, APPLY_DATE,  IS_WAIVED, BUSI_PROD_CODE, 
				APPLY_CODE, RENEW, HESITATION2ACC, INSERT_TIMESTAMP, RENEW_TIMES, WAIVER_END, UPDATE_BY, 
				GURNT_PERD_TYPE, BUSI_ITEM_ID, POLICY_ID, WAIVER, JOINT_LIFE_FLAG, INSERT_TIME, MASTER_BUSI_ITEM_ID, 
				END_CAUSE, ISSUE_DATE, UPDATE_TIME, LAPSE_CAUSE, PAIDUP_DATE, DECISION_CODE, EXPIRY_DATE, 
				LIABILITY_STATE, POLICY_CODE, RENEW_DECISION, VALIDATE_DATE, UPDATE_TIMESTAMP, INSERT_BY, WAIVER_START, 
				PRD_PKG_CODE , MATURITY_DATE ,GURNT_RATE,SETTLE_METHOD,INITIAL_VALIDATE_DATE,OLD_POL_NO, SURRENDER_FLAG,
				HESITATION_PERIOD_DAY,ORDER_ID,MEET_POV_STANDARD_FLAG,RENEWAL_STATE) 
			VALUES (
				 #{is_rpu, jdbcType=NUMERIC} ,#{gurnt_renew_start, jdbcType=DATE} , #{gurnt_renew_end, jdbcType=DATE}, #{gurnt_start_date, jdbcType=DATE}, #{apl_permit, jdbcType=NUMERIC} , #{gurnt_period, jdbcType=NUMERIC} , #{busi_prd_id, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} , #{is_waived, jdbcType=NUMERIC},  #{busi_prod_code, jdbcType=VARCHAR} 
				, #{apply_code, jdbcType=VARCHAR} ,  #{renew, jdbcType=NUMERIC} , #{hesitation2acc, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{renew_times, jdbcType=NUMERIC} , #{waiver_end, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} 
				, #{gurnt_perd_type, jdbcType=VARCHAR} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{waiver, jdbcType=NUMERIC} , #{joint_life_flag, jdbcType=NUMERIC} , SYSDATE , #{master_busi_item_id, jdbcType=NUMERIC} 
				, #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , SYSDATE , #{lapse_cause, jdbcType=VARCHAR} , #{paidup_date, jdbcType=DATE} , #{decision_code, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} 
				, #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{waiver_start, jdbcType=DATE} 
				, #{prd_pkg_code, jdbcType=VARCHAR}, #{maturity_date, jdbcType=DATE},#{gurnt_rate, jdbcType=NUMERIC} ,#{settle_method, jdbcType=NUMERIC} 
				, #{initial_validate_date, jdbcType=DATE} ,#{old_pol_no, jdbcType=VARCHAR},#{surrender_flag, jdbcType=NUMERIC},#{hesitation_period_day, jdbcType=NUMERIC},#{order_id, jdbcType=VARCHAR},#{meet_pov_standard_flag,jdbcType=NUMERIC},
		]]>				
                <if test=" renewal_state != null and renewal_state != ''  "><![CDATA[#{renewal_state, jdbcType=NUMERIC}]]></if>
                <if test=" renewal_state = null or renewal_state = ''  "><![CDATA[ default]]></if>
         <![CDATA[ ) 
		 ]]>
	</insert>
	
	<select id="JRQD_PA_calPrem"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT SUM(A.TOTAL_PREM_AF) PREM FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A  WHERE 1 = 1  ]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</select>
	
	<select id="JRQD_PA_calAmount"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT SUM(A.AMOUNT) AMOUNT FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A  WHERE 1 = 1  ]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</select>
	
	<!-- by zhaoyoan_wb 通过保单号和险种代码查询主险险种 -->
	<select id="JRQD_PA_queryMasterByPolicyCodeAndBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD 
			where MASTER_BUSI_ITEM_ID is null 
			and POLICY_CODE=#{policy_code} 
			and BUSI_PROD_CODE in (${busi_prod_code}) 
		]]>
	</select>
	
	<!-- 查询保单下的投连万能险账户信息 -->
	<select id="JRQD_PA_findPGDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			    SELECT TCBP.POLICY_CODE, TF.FUND_CODE, TBP.PRODUCT_NAME_SYS, TCBP.BUSI_PROD_CODE, 
				       TF.FUND_NAME, TCI.INTEREST_CAPITAL 
				  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP, 
				       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP, 
				       APP___PAS__DBUSER.T_CONTRACT_INVEST    TCI, 
				       APP___PAS__DBUSER.T_FUND               TF 
				 WHERE 1 = 1 
				   AND TCBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS 
				   AND TCBP.POLICY_ID      = TCI.POLICY_ID 
				   AND TCBP.BUSI_ITEM_ID   = TCI.BUSI_ITEM_ID 
				   AND TCI.ACCOUNT_CODE    = TF.FUND_CODE 
				   AND TCBP.POLICY_CODE    = #{policy_code} 
		]]>
	</select>

	<!-- 通过保单号查询主险主险名称+主险代码 -->
	<select id="JRQD_PA_findMainNameCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT (SELECT P.PRODUCT_NAME_SYS
			           FROM DEV_PAS.T_BUSINESS_PRODUCT P
			          WHERE P.BUSINESS_PRD_ID = A.BUSI_PRD_ID) BUSI_ITEM_NAME,
			        A.BUSI_PROD_CODE
			   FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A 
			  WHERE 1 = 1      
			    AND A.MASTER_BUSI_ITEM_ID is null
			    AND A.POLICY_CODE=#{policy_code} 
		]]>
	</select>
	
	<select id="JRQD_queryMasterByPolicyCode1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 
		]]>
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	<!-- 被保险人职业类别查询接口 -->
	<select id="JRQD_queryBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select b.job_code,
		       (select job_nature from APP___PAS__DBUSER.T_JOB_NATURE where job_nature_code = b.job_code ) as occupation_code_name,
		       (select job_kind from APP___PAS__DBUSER.T_customer where customer_id =b.customer_id) as  job_kind,
		       (select JOB_CATEGORY_NAME from APP___PAS__DBUSER.T_JOB_CATEGORY  where job_code=b.job_code) as occupation_type_name,
		       (select PRODUCT_CODE_STD  from APP___PAS__DBUSER.T_BUSINESS_PRODUCT  where BUSINESS_PRD_ID = a.busi_prd_id) as product_code_std,
		       (select PRODUCT_NAME_SYS from APP___PAS__DBUSER.T_BUSINESS_PRODUCT  where BUSINESS_PRD_ID=a.busi_prd_id) as busi_prod_name,
		       (select sum(c.AMOUNT)
		          from APP___PAS__DBUSER.T_contract_product c
		         where c.busi_item_id = a.busi_item_id) as amnt ,
		       (select sum(c.TOTAL_PREM_AF)
		          from APP___PAS__DBUSER.T_contract_product c
		         where c.busi_item_id = a.busi_item_id) as prem ,
		       (select  sum(c.EXTRA_PREM_AF)
		          from APP___PAS__DBUSER.T_contract_product c
		         where c.busi_item_id = a.busi_item_id)  as occ_add_prem
		  from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD a, APP___PAS__DBUSER.T_INSURED_LIST b
		 where a.policy_id = b.policy_id
		   and a.policy_code = #{policy_code}
		   and b.customer_id = #{customer_id}
		]]>
	</select>
	<!-- 保单险种信息查询接口  -->
	<select id="JRQD_queryPolicyCodeRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 
			A.liability_state,
			B.Coverage_Year,
			A.Old_Pol_No,
            A.Busi_Prod_Code,
            B.COVERAGE_YEAR COVERAGEYEAR, --保障年期
            C.COVERAGE_DESC COVERAGEDESC,--保障年期单位
			(SELECT PRODUCT_ABBR_NAME
			FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT
			WHERE BUSINESS_PRD_ID = A.BUSI_PRD_ID) AS PRODUCT_NAME,
			(SELECT PRODUCT_CODE_SYS
			FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT
			WHERE BUSINESS_PRD_ID = A.BUSI_PRD_ID) AS PRODUCT_CODE,
			(SELECT SUM(B.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT B WHERE B.BUSI_ITEM_ID = 
			A.BUSI_ITEM_ID) AS AMOUNT,
			(SELECT SUM(nvl(B.STD_PREM_AF,0))+SUM(nvl(B.ADDITIONAL_PREM_AF,0))+SUM(nvl(B.APPEND_PREM_AF,0))
			FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT B
			WHERE B.BUSI_ITEM_ID = A.BUSI_ITEM_ID) AS TOTAL_PREM_AF,
			A.EXPIRY_DATE,A.VALIDATE_DATE,A.ISSUE_DATE SIGNDATE,A.POLICY_ID,A.BUSI_ITEM_ID
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,APP___PAS__DBUSER.t_contract_product B,APP___PAS__DBUSER.T_COVERAGE_PERIOD C
 			WHERE A.POLICY_CODE = #{policy_code}
 			AND B.busi_item_id = A.Busi_Item_Id
 			AND C.COVERAGE_PERIOD = B.COVERAGE_PERIOD
 			
		]]>
	</select>
	<!-- 保单号获取所有可转投险种信息 -->
	<select id="JRQD_PA_queryRelaRisksByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
      select decode(du.PRODUCT_STATIC_CODE,'726','727','727','726','507','508','508','507') as PRODUCT_STATIC_CODE,
       (case du.PRODUCT_STATIC_CODE 
          when '726' then (select t.PRODUCT_ABBR_NAME from APP___PAS__DBUSER.T_BUSINESS_PRODUCT t where t.PRODUCT_STATIC_CODE = '727') 
          when '727' then (select t.PRODUCT_ABBR_NAME from APP___PAS__DBUSER.T_BUSINESS_PRODUCT t where t.PRODUCT_STATIC_CODE = '726') 
          when '507' then (select t.PRODUCT_ABBR_NAME from APP___PAS__DBUSER.T_BUSINESS_PRODUCT t where t.PRODUCT_STATIC_CODE = '508') 
          else (select t.PRODUCT_ABBR_NAME from APP___PAS__DBUSER.T_BUSINESS_PRODUCT t where t.PRODUCT_STATIC_CODE = '507')  end ) as PRODUCT_ABBR_NAME
        from APP___PAS__DBUSER.T_CONTRACT_MASTER    ma,
             APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD pr,
             APP___PAS__DBUSER.T_BUSINESS_PRODUCT   du
       where ma.policy_id = pr.policy_id
         and busi_prd_id = du.BUSINESS_PRD_ID
         and du.PRODUCT_STATIC_CODE in ('726', '727', '507', '508')
         and pr.policy_code = #{policy_code}
	]]>
	</select>
	<!-- 保单号、险种号码<list>获取附加险信息 -->
	<select id="JRQD_PA_queryNotmasterBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
      select * from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD t where t.policy_code = #{policy_code} and t.master_busi_item_id is not null and t.busi_prod_code in 
	]]>
	<foreach collection="list" item="list"
			index="index" open="(" close=")" separator=",">#{list}</foreach>
	</select>
	
		<!--yuzw-->
	<select id="JRQD_PA_CS_findAddBusiProdByPolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryContractBusiProdByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID ]]>
	</select>
	<!-- 查询关联保单险种信息 -->
	<select id="JRQD_PA_findAddBusiProdByRelevancePolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE 
			A.POLICY_CODE= (select B.RELATION_POLICY_CODE from APP___PAS__DBUSER.T_CONTRACT_MASTER b where B.POLICY_ID = #{policy_id})
			ORDER BY A.BUSI_ITEM_ID ]]>
	</select>
	<!-- 保单险种信息查询  -->
	<select id="JRQD_queryPolicyCodeRiskName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT  A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.APL_PERMIT, A.GURNT_PERIOD, A.BUSI_PRD_ID, A.APPLY_DATE,  A.BUSI_PROD_CODE, 
			A.IS_WAIVED, A.APPLY_CODE, A.RENEW, A.HESITATION2ACC, A.RENEW_TIMES, A.WAIVER_END, 
			A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.BUSI_ITEM_ID, A.POLICY_ID, A.LAPSE_DATE, A.WAIVER, 
			A.JOINT_LIFE_FLAG, A.MASTER_BUSI_ITEM_ID, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.PAIDUP_DATE, A.DECISION_CODE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.RERINSTATE_DATE, 
			A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.RENEW_DECISION, A.DUE_LAPSE_DATE, A.VALIDATE_DATE, A.RENEWAL_STATE, 
			A.WAIVER_START, A.GURNT_RENEW_END, A.PRD_PKG_CODE, A.GURNT_RENEW_START, TP.PRODUCT_NAME_STD AS RISK_NAME,TH.CUSTOMER_ID,TF.TYPE_NAME,A.GURNT_RATE,A.SETTLE_METHOD,A.OLD_POL_NO
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, APP___PAS__DBUSER.T_BUSINESS_PRODUCT TP, APP___PAS__DBUSER.T_POLICY_HOLDER TH 
            LEFT JOIN APP___PAS__DBUSER.T_FEE_TYPE TF
            ON TF.CODE=#{fee_type,jdbcType=VARCHAR}
            WHERE  A.BUSI_PRD_ID = TP.BUSINESS_PRD_ID AND A.POLICY_CODE=TH.POLICY_CODE 
            
		]]>
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
	</select>
	<!-- 当日年化受益率当前日期价格-->
	<select id="JRQD_queryAnnualYieldInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * from  
      
   (SELECT A.PRICING_DATE ,A.CAL_BID_PRICE ,A.CAL_OFF_PRICE ,A.INVEST_ACCOUNT_CODE
             
      FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A
                  
         WHERE   A.INVEST_ACCOUNT_CODE ='892001' AND 
         
         A.CONFIRM_RESULT=0 AND A.PRICING_DATE < to_date(#{pricing_date},'YYYY-MM-DD') ORDER BY A.PRICING_DATE DESC ) 
         
         where rownum <= 2				
		 ]]>
	</select>
	<!-- 当日年化受益率前一计价日价格 -->
	<select id="JRQD_queryAnnualYieldInfoPrior" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from (select tiup.BID_PRICE, tiup.PRICING_DATE
			  from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD tcbp
			 inner join APP___PAS__DBUSER.T_CONTRACT_INVEST tci
			    on tcbp.BUSI_ITEM_ID = tci.BUSI_ITEM_ID
			 inner join APP___PAS__DBUSER.T_INVEST_UNIT_PRICE tiup
			    on tci.ACCOUNT_CODE = tiup.INVEST_ACCOUNT_CODE
			 where tcbp.BUSI_PROD_CODE = #{busi_prod_code} 
			 and to_char(tiup.PRICING_DATE, 'yyyy-mm-dd') < #{pricing_date} 
			 order by tiup.PRICING_DATE desc) where rownum = 1	]]>
	</select>
	<!-- i添财投资当前日期价格 -->
	<select id="JRQD_queryRenturnRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select res.*,
			(select nvl(sum(cal_bid_price),0) from APP___PAS__DBUSER.T_INVEST_UNIT_PRICE 
			  where INVEST_ACCOUNT_CODE=res.INVEST_ACCOUNT_CODE and PRICING_DATE<#{pricing_date}) sum_bnf 
			from (
			  select distinct tiup.INVEST_ACCOUNT_CODE,tiup.cal_off_price, tiup.cal_bid_price, tiup.PRICING_DATE,
			  	tiup.ANNUALIZED_RETURN,tci.accum_units,tci.policy_id,tci.busi_item_id
			  from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD tcbp
			  inner join APP___PAS__DBUSER.T_CONTRACT_INVEST tci on tcbp.BUSI_ITEM_ID = tci.BUSI_ITEM_ID
			  inner join APP___PAS__DBUSER.T_INVEST_UNIT_PRICE tiup on tci.ACCOUNT_CODE = tiup.INVEST_ACCOUNT_CODE
			  where tcbp.POLICY_CODE = #{policy_code} and tiup.PRICING_DATE<#{pricing_date}
			  order by tiup.PRICING_DATE desc) res
			where rownum <= 2
		]]>
	</select>
	<!-- i添财投资前一计价日价格 -->
	<select id="JRQD_queryRenturnRatePrior" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from (select tiup.BID_PRICE, tiup.PRICING_DATE,tci.ACCUM_UNITS,tiup.INVEST_ACCOUNT_CODE,tcbp.BUSI_ITEM_ID ,tiup.cal_off_price
			  from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD tcbp
			 inner join APP___PAS__DBUSER.T_CONTRACT_INVEST tci
			    on tcbp.BUSI_ITEM_ID = tci.BUSI_ITEM_ID
			 inner join APP___PAS__DBUSER.T_INVEST_UNIT_PRICE tiup
			    on tci.ACCOUNT_CODE = tiup.INVEST_ACCOUNT_CODE
			 where tcbp.POLICY_CODE = #{policy_code} 
			 and tiup.PRICING_DATE <= sysdate 
			 order by tiup.PRICING_DATE desc) where rownum <= 2		]]>
	</select>
	<!--by zhaoyoan_wb 通过保单号查询保单下各险种满期给付通知书信息 -->
	<select id="JRQD_queryWebLPEdorGetNoticeBusinessInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.BUSI_PROD_CODE,
			(SELECT PRODUCT_ABBR_NAME FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT
				WHERE BUSINESS_PRD_ID = A.BUSI_PRD_ID) AS product_abbr_name,
			E.CUSTOMER_NAME,E.CUSTOMER_GENDER,B.LIAB_CODE,B.PAY_DUE_DATE,
			(SELECT FEE_AMOUNT FROM APP___PAS__DBUSER.T_PAY_DUE WHERE B.PLAN_ID=PLAN_ID AND PAY_DUE_DATE=
				(SELECT MAX(PAY_DUE_DATE) FROM APP___PAS__DBUSER.T_PAY_DUE WHERE B.PLAN_ID=PLAN_ID) AND ROWNUM=1) AS get_standard,
			INSTALMENT_AMOUNT AS get_money,
			(SELECT POST_CODE FROM APP___PAS__DBUSER.T_ADDRESS WHERE ADDRESS_ID=D.ADDRESS_ID) AS POST_CODE,
			(SELECT ADDRESS FROM APP___PAS__DBUSER.T_ADDRESS WHERE ADDRESS_ID=D.ADDRESS_ID) AS ADDRESS,
			(SELECT SUM(NVL(AMOUNT,0)) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT
				WHERE A.POLICY_CODE = POLICY_CODE AND A.BUSI_ITEM_ID = BUSI_ITEM_ID) AS amount
			FROM APP___PAS__DBUSER.T_PAY_PLAN B,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
				APP___PAS__DBUSER.T_BENEFIT_INSURED C,APP___PAS__DBUSER.T_INSURED_LIST D,APP___PAS__DBUSER.T_CUSTOMER E
			WHERE A.POLICY_CODE = #{policy_code} AND B.PAY_PLAN_TYPE = '4'
			AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID AND C.BUSI_ITEM_ID=B.BUSI_ITEM_ID 
			AND D.LIST_ID=C.INSURED_ID AND E.CUSTOMER_ID=D.CUSTOMER_ID
			AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_DOCUMENT C WHERE A.POLICY_ID=C.POLICY_ID 
				AND C.TEMPLATE_CODE='PAS_00005')
		]]>
		<!--  AND C.STATUS='2'	老核心条件的是状态为0的，对应新核心是2 -->
	</select>
	<!--by zhaoyoan_wb 通过保单号查询保单下各险种信息及被保人客户号 -->
	<select id="JRQD_queryRiskInfoAndInsuredNoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,A.POLICY_ID,A.BUSI_ITEM_ID,A.BUSI_PROD_CODE,A.BUSI_PRD_ID,A.OLD_POL_NO,
			(SELECT PRODUCT_NAME_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT 
				WHERE BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS product_name_sys,
			(SELECT PRODUCT_ABBR_NAME FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT 
				WHERE BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS product_abbr_name,
			(SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST WHERE LIST_ID=
            	(SELECT INSURED_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED WHERE 
            		POLICY_CODE=A.POLICY_CODE AND BUSI_ITEM_ID=A.BUSI_ITEM_ID /* AND ORDER_ID=1 */)) AS insured_id
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A 
			WHERE 1=1 AND A.POLICY_CODE=#{policy_code} 
		]]>
	</select>
	<!-- 查询是否是精选产品 -->				
	
	<select id="JRQD_findConBusiProdIsJXOrNot" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.APL_PERMIT, A.GURNT_PERIOD, A.BUSI_PRD_ID, A.APPLY_DATE,  A.BUSI_PROD_CODE, 
			A.IS_WAIVED, A.APPLY_CODE, A.RENEW, A.HESITATION2ACC, A.RENEW_TIMES, A.WAIVER_END, 
			A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.BUSI_ITEM_ID, A.POLICY_ID, A.LAPSE_DATE, A.WAIVER, 
			A.JOINT_LIFE_FLAG, A.MASTER_BUSI_ITEM_ID, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.PAIDUP_DATE, A.DECISION_CODE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.RERINSTATE_DATE, 
			A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.RENEW_DECISION, A.DUE_LAPSE_DATE, A.VALIDATE_DATE, A.RENEWAL_STATE, 
			A.WAIVER_START, A.GURNT_RENEW_END, A.PRD_PKG_CODE, A.GURNT_RENEW_START,A.GURNT_RATE,A.SETTLE_METHOD,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE (A.BUSI_PROD_CODE like '%9121%' or A.BUSI_PROD_CODE like '%913%') and A.BUSI_ITEM_ID = #{busi_item_id} ]]>
		
	</select>
	
	<!-- 根据险种code查询险种id -->	
	<select id="JRQD_queryContractBusiPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select t.busi_prd_id from APP___PAS__DBUSER.t_contract_busi_prod t where t.busi_prod_code = #{busi_prod_code} and rownum=1
		]]>
	</select>
	
	<!-- 查询被保险人所属的险种：根据被保险人ID查询 -->	
	<select id="JRQD_PA_findAllContractBusiProdByInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select tc.*
			  from APP___PAS__DBUSER.t_benefit_insured t
			  left join APP___PAS__DBUSER.t_contract_busi_prod tc
			    on t.busi_item_id = tc.busi_item_id
			 where t.insured_id = #{insured_id} 
			 order by  tc.master_busi_item_id desc
		]]>
	</select>
	<!-- 查询保单号下是否有附加险万能型 -->	
	<select id="JRQD_PA_queryContractYN" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 1 from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD tcbp,APP___PAS__DBUSER.T_CONTRACT_INVEST tci where tcbp.busi_item_id=tci.busi_item_id and 
			tcbp.MASTER_BUSI_ITEM_ID is not null and tci.INVEST_ACCOUNT_TYPE='2' and tcbp.policy_code=#{policy_code}	
		]]>
	</select>
	
	<!-- 移动保全保单复效查询 -->	
	<select id="JRQD_queryContReinstateInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.BUSI_PROD_CODE,A.LIABILITY_STATE,A.LAPSE_DATE,
			(SELECT PRODUCT_NAME_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT WHERE BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS product_name_sys,
			(SELECT SUM(NVL(STD_PREM_AF,0)) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS std_prem_af,
			(SELECT SUM(NVL(AMOUNT,0)) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS amount,
			(SELECT SUM(NVL(UNIT,0)) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS unit,
			(SELECT PAY_DUE_DATE FROM DEV_PAS.T_CONTRACT_EXTEND WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND ROWNUM=1) AS pay_due_date,
			(SELECT SUM(NVL(EXTRA_PREM,0)) FROM DEV_PAS.T_EXTRA_PREM WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND EXTRA_TYPE=1) AS extra_para1,
			(SELECT SUM(NVL(EXTRA_PREM,0)) FROM DEV_PAS.T_EXTRA_PREM WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND EXTRA_TYPE=2) AS extra_para2
			FROM DEV_PAS.T_CONTRACT_BUSI_PROD A
			WHERE 1=1 
			AND A.POLICY_CODE=#{policy_code} 
		]]>
	</select>
	
	<!-- 续保时特殊附加险校验 -->
	<select id="JRQD_PA_querySpeRuleCount1" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_BUSI_PROD T 
			WHERE  1 = 1 AND (T.BUSI_PROD_CODE LIKE '%717%' OR T.BUSI_PROD_CODE LIKE '%540%' OR T.BUSI_PROD_CODE LIKE '%726%' 
			OR T.BUSI_PROD_CODE LIKE '%946%')
			AND T.LIABILITY_STATE <> '1' AND T.POLICY_CODE = #{policy_code} AND T.MASTER_BUSI_ITEM_ID = #{master_busi_item_id}
		]]>
	</select>
	<select id="JRQD_PA_querySpeRuleCount2" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_BUSI_PROD T 
			WHERE 1 = 1 AND (T.BUSI_PROD_CODE LIKE '%541%' OR T.BUSI_PROD_CODE LIKE '%717%' OR T.BUSI_PROD_CODE LIKE '%726%' 
			OR T.BUSI_PROD_CODE LIKE '%946%')
			AND T.LIABILITY_STATE = '1' AND T.POLICY_CODE = #{policy_code} AND T.MASTER_BUSI_ITEM_ID = #{master_busi_item_id}
		]]>
	</select>
	
	<!-- 根据保单号查询主险 -->
	<select id="JRQD_PA_findMasterContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE  A.MASTER_BUSI_ITEM_ID is null]]>
		<if test="policy_id != null and policy_id != ''">
			<![CDATA[AND A.policy_id = #{policy_id} ]]>
		</if>
		<if test="policy_code != null and policy_code != ''">
			<![CDATA[AND A.policy_code = #{policy_code} ]]>
		</if> 
		
		<if test=" renew  != null "><![CDATA[ AND A.RENEW = #{renew} ]]></if>
	</select>
	<!-- 根据保单号查询主险 -->
	<select id="JRQD_PA_findAllMasterContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE  A.MASTER_BUSI_ITEM_ID is null]]>
		<if test="policy_id != null and policy_id != ''">
			<![CDATA[AND A.policy_id = #{policy_id} ]]>
		</if>
		<if test="policy_code != null and policy_code != ''">
			<![CDATA[AND A.policy_code = #{policy_code} ]]>
		</if> 
		
		<if test=" renew  != null "><![CDATA[ AND A.RENEW = #{renew} ]]></if>
		
	</select>
	
	
	<!-- 根据保单号查询主险 -->
	<select id="JRQD_findUniContractBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 select a.*
                  from (select *
                          from dev_pas.t_contract_busi_prod t
                         where t.policy_code = #{policy_code}) a
                  left join dev_pas.t_business_product tbp
                    on a.busi_prd_id = tbp.business_prd_id
                 where tbp.product_category1 = '20003'
		]]>
	</select>
	
	<!--by zhaoyoan_wb 根据新(老)核心险种号查询保单险种信息 -->
	<select id="JRQD_findContractBusiProdByPolNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.BUSI_ITEM_ID,A.BUSI_PRD_ID,A.BUSI_PROD_CODE,A.POLICY_ID,A.POLICY_CODE,A.HESITATION_PERIOD_DAY,
				A.OLD_POL_NO,A.LIABILITY_STATE,A.VALIDATE_DATE,A.MASTER_BUSI_ITEM_ID 
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A 
			WHERE 1=1
		]]>
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
		<![CDATA[
			AND NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID)=#{polno} 
		]]>
	</select>
	
	<!-- 附加险查询接口 -->
	<select id="JRQD_findContractBusiProdsByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select 
			(select sum(p.std_prem_af) from dev_pas.t_contract_product p where p.busi_item_id = a.busi_item_id and p.policy_code = a.policy_code) as prem,
			(select sum(p.amount) from dev_pas.t_contract_product p where p.busi_item_id = a.busi_item_id and p.policy_code = a.policy_code) as amnt,
			a.validate_date,
			a.busi_prod_code,
			(select PRODUCT_ABBR_NAME from dev_pds.t_business_product where product_code_sys = a.busi_prod_code) as riskname,
			(select ce.PAY_DUE_DATE from dev_pas.T_CONTRACT_EXTEND ce where ce.BUSI_ITEM_ID = a.BUSI_ITEM_ID and rownum=1) as paytodate,
			(select customer_id from dev_pas.t_insured_list where list_id = 
				(select insured_id from dev_pas.t_benefit_insured where a.busi_item_id = busi_item_id /* and order_id = 1 */)) insuredno
			from dev_pas.t_contract_busi_prod a
			where a.policy_code = #{policy_code}
		]]>
	</select>
	
	<!-- 附加险代码查询接口 -->
	<select id="JRQD_findContractBusiProdCodeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select  a.busi_prod_code,a.VALIDATE_DATE,a.LIABILITY_STATE,
       			    (select product_name_sys from dev_pds.t_business_product where product_code_sys = a.busi_prod_code) as riskname
  			from dev_pas.t_contract_busi_prod a
 			where a.policy_code = #{policy_code}
   			and a.master_busi_item_id is not null
		]]>
	</select>
	
	<!-- 查询险种名称报文对比 -->
	<select id="JRQD_queryPolicyCodeRroductSysName" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[
			SELECT  A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.APL_PERMIT, A.GURNT_PERIOD, A.BUSI_PRD_ID, A.APPLY_DATE,  A.BUSI_PROD_CODE, 
			A.IS_WAIVED, A.APPLY_CODE, A.RENEW, A.HESITATION2ACC, A.RENEW_TIMES, A.WAIVER_END, 
			A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.BUSI_ITEM_ID, A.POLICY_ID, A.LAPSE_DATE, A.WAIVER, 
			A.JOINT_LIFE_FLAG, A.MASTER_BUSI_ITEM_ID, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.PAIDUP_DATE, A.DECISION_CODE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.RERINSTATE_DATE, 
			A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.RENEW_DECISION, A.DUE_LAPSE_DATE, A.VALIDATE_DATE, A.RENEWAL_STATE, 
			A.WAIVER_START, A.GURNT_RENEW_END, A.PRD_PKG_CODE, A.GURNT_RENEW_START, TP.PRODUCT_NAME_SYS AS RISK_NAME,TH.CUSTOMER_ID,TF.TYPE_NAME,A.GURNT_RATE,A.SETTLE_METHOD,A.OLD_POL_NO
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, APP___PAS__DBUSER.T_BUSINESS_PRODUCT TP, APP___PAS__DBUSER.T_POLICY_HOLDER TH 
            LEFT JOIN APP___PAS__DBUSER.T_FEE_TYPE TF
            ON TF.CODE=#{fee_type,jdbcType=VARCHAR}
            WHERE  A.BUSI_PRD_ID = TP.BUSINESS_PRD_ID AND A.POLICY_CODE=TH.POLICY_CODE 
            
		]]>
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
	
	</select>
	<!-- 根据险种号查询险种ID保单Id -->
	<select id="JRQD_findContractBusiProdIdByPolNo" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[	SELECT A.BUSI_ITEM_ID,A.POLICY_ID
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A 
			WHERE 1=1 AND  A.POLICY_CODE=#{policy_code} and A.OLD_POL_NO=#{polno} ]]>
	
	</select>
	<!-- 根据险种号查询险种ID保单Id -->
	<select id="JRQD_findMainBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	SELECT CM.POLICY_CODE, CM.VALIDATE_DATE, TBP.PRODUCT_NAME_SYS, CBP.BUSI_PROD_CODE
			  FROM DEV_PAS.T_CONTRACT_MASTER    CM,
			       DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
			       DEV_PAS.T_BUSINESS_PRODUCT   TBP
			 WHERE CBP.MASTER_BUSI_ITEM_ID IS NULL
			   AND CM.POLICY_CODE = CBP.POLICY_CODE
			   AND CBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
			   AND CBP.POLICY_CODE = #{policy_code}]]>	   
	</select>
	<!-- 连带被保险人信息接口 -->
	<select id="JRQD_PA_queryJointInsurerInf" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[	
	       SELECT  T2.BUSI_ITEM_ID,T1.CUSTOMER_ID,
	       (SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST WHERE LIST_ID IN(SELECT INSURED_ID  FROM APP___PAS__DBUSER.T_BENEFIT_INSURED /* WHERE ORDER_ID='1' */)AND POLICY_ID=T1.POLICY_ID AND ROWNUM=1)MCUSTOMER_ID,
	       T2.RELATION_TO_INSURED_1,T3.CUSTOMER_NAME,T3.CUSTOMER_GENDER,
	       T3.CUSTOMER_BIRTHDAY,T2.INSERT_TIME,T2.UPDATE_TIME,T3.CUSTOMER_CERT_TYPE,T3.CUSTOMER_CERTI_CODE,T2.JOB_UNDERWRITE
	       FROM APP___PAS__DBUSER.T_INSURED_LIST T1 ,APP___PAS__DBUSER.T_BENEFIT_INSURED T2 ,APP___PAS__DBUSER.T_CUSTOMER T3
	       WHERE T1.POLICY_ID=T2.POLICY_ID AND T1.POLICY_CODE=T2.POLICY_CODE AND T1.LIST_ID=T2.INSURED_ID AND T1.CUSTOMER_ID=T3.CUSTOMER_ID 
	       AND T2.POLICY_CODE=#{policy_code} AND T2.BUSI_ITEM_ID=#{busi_item_id}
	   ]]>
	</select>
	
	<!-- 根据保单号查询t_business_product.product_category1="20003",CP.IS_PAUSE="1"时为万能险缓交保单 -->
	<select id="JRQD_findProductCategory1ByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT BP.PRODUCT_CATEGORY1,CP.IS_PAUSE
     			FROM DEV_PAS.T_BUSINESS_PRODUCT BP, DEV_PAS.T_CONTRACT_BUSI_PROD CBP,DEV_PAS.T_CONTRACT_PRODUCT CP
    			WHERE BP.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
          		AND CP.POLICY_CODE=CBP.POLICY_CODE AND CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
          		AND CBP.POLICY_CODE = #{policy_code}
		]]>
	</select>

	<!-- 查询保单主险code -->
		<select id="JRQD_PA_queryMasterBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[	
		SELECT A.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE A.MASTER_BUSI_ITEM_ID IS NULL 
	   ]]>
	   <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</select>
<!-- 根据保单号查询t_business_product中险种的CRS标识 -->
	<select id="JRQD_findIsCrs" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			    SELECT QQ.TAX_REVENUE_FLAG,CP.POLICY_CODE,QQ.PRODUCT_CODE_SYS,CP.BUSI_PROD_CODE,CP.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD  
			    CP LEFT JOIN  APP___PDS__DBUSER.T_BUSINESS_PRODUCT QQ ON 
			    CP.BUSI_PROD_CODE=QQ.PRODUCT_CODE_SYS  WHERE 
          	    CP.POLICY_CODE = #{policy_code}
		]]>
		<if test=" policy_id  != null "><![CDATA[ AND CP.POLICY_ID = #{policy_id} ]]></if>
	</select>
	
	
	<!--年金主险信息查询  -->
	<select id="JRQD_querySpcialBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map" >
	
		<![CDATA[
		     select A.busi_item_id, A.busi_prd_id, A.Busi_Prod_Code,A.APPLY_CODE,A.POLICY_CODE
			   from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
			   WHERE 1 = 1 and A.POLICY_CODE=#{policy_code}
			   and A.BUSI_PROD_CODE IN  
		]]>
	  <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}       
      </foreach>
	
	</select>
	
	<select id="JRQD_PA_findMasterContractBusiProdisLoan" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(A.POLICY_CODE) FROM DEV_PAS.T_CONTRACT_BUSI_PROD A,DEV_PAS.T_POLICY_ACCOUNT TPA WHERE A.MASTER_BUSI_ITEM_ID IS NULL AND A.BUSI_ITEM_ID = TPA.BUSI_ITEM_ID
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
		
	<select id="JRQD_findBusinessProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select distinct c.busi_item_id from dev_pas.t_contract_busi_prod c 
			where c.master_busi_item_id is null and c.policy_code = #{policy_code}
		]]>
	</select>
	
	<select id="JRQD_findValidateDateBybusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select to_date(to_char(nvl(c.validate_date,sysdate),'yyyy/mm/dd'),'yyyy/mm/dd') validate_date from dev_pas.t_contract_busi_prod c where c.busi_item_id = #{busi_item_id}
		]]>
	</select>
	
	<select id="JRQD_findcDateBybusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select paidup_date,extraction_due_date 
			from (select to_date(to_char(a.paidup_date,'yyyy/mm/dd'),'yyyy/mm/dd') paidup_date, 
			(select to_date(to_char(c.extraction_due_date,'yyyy/mm/dd'),'yyyy/mm/dd') 
			from dev_pas.t_contract_extend c 
			where c.busi_item_id = a.busi_item_id) extraction_due_date 
			from dev_pas.t_contract_busi_prod a where a.busi_item_id = #{busi_item_id})
		]]>
	</select>
	
	<select id="JRQD_findTotalPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select c.total_prem_af from dev_pas.t_contract_product c 
			where c.busi_item_id = #{busi_item_id}
			and c.product_code in ('167000','430100','431000',
			'555000','556000','564000','565000','696000','697000',
			'698000','699000','801000','802000','949000','950000',
			'954000','955000','959000','960000','961000','962000','973000')
		]]>
	</select>
	<!-- 添加操作 -->
	<insert id="JRQD_addConBusiProdMapping"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="lis_id">
			SELECT APP___PAS__DBUSER.S_CON_BUSI_PROD_MAPPING.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CON_BUSI_PROD_MAPPING(LIS_ID,
				POLICY_ID,POLICY_CODE,APPLY_CODE,BUSI_ITEM_ID_PAS,BUSI_ITEM_ID_NB,
				INSERT_BY,UPDATE_BY,INSERT_TIME,INSERT_TIMESTAMP,UPDATE_TIME,UPDATE_TIMESTAMP
				) 
			VALUES (
			     #{lis_id, jdbcType=NUMERIC},
				 #{policy_id, jdbcType=NUMERIC}, #{policy_code, jdbcType=VARCHAR},#{apply_code, jdbcType=VARCHAR}, 
				 #{busi_item_id_pas, jdbcType=NUMERIC},  #{busi_item_id_nb, jdbcType=NUMERIC},#{insert_by, jdbcType=NUMERIC},
				 #{update_by, jdbcType=NUMERIC},SYSDATE,CURRENT_TIMESTAMP,SYSDATE,CURRENT_TIMESTAMP)
				]]>	
			 	
	</insert>
	

	<update id="JRQD_PA_updateContractBusiProdLiabilityStateByPolicyId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD ]]>
	<set>
		<trim suffixOverrides=",">
			EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
			LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR}
		</trim>
	</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} ]]>
</update>
	

	
	
	<select id="JRQD_queryNSBusiProdInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CBP.*
			  FROM DEV_PAS.T_CONTRACT_MASTER CM
			 INNER JOIN DEV_PAS.T_POLICY_CHANGE PC
			    ON PC.POLICY_ID = CM.POLICY_ID
			   AND PC.SERVICE_CODE = 'NS'
			 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
			    ON CAC.ACCEPT_CODE = PC.BUSINESS_CODE
			   AND CAC.SERVICE_CODE = 'NS'
			   AND CAC.ACCEPT_STATUS = '18'
			 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC
			    ON CPC.CHANGE_ID = CAC.CHANGE_ID
			   AND CPC.ACCEPT_ID = CPC.ACCEPT_ID
			   AND CPC.POLICY_ID = CM.POLICY_ID
			 INNER JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD CCBP
			    ON CCBP.CHANGE_ID = CAC.CHANGE_ID
			   AND CCBP.POLICY_CHG_ID = CPC.POLICY_CHG_ID
			   AND CCBP.OLD_NEW = '1'
			   AND CCBP.OPERATION_TYPE = '1'
			 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
			    ON CBP.POLICY_ID = CM.POLICY_ID
			   AND CBP.BUSI_ITEM_ID = CCBP.BUSI_ITEM_ID
			 WHERE 1 = 1
			   AND CM.POLICY_CODE = #{policy_code}
			 ORDER BY CBP.INSERT_TIME DESC
		]]>
	</select>
	
	<!-- 自动生成主键 -->
	<select id="JRQD_findNextBusiItemId" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT APP___PAS__DBUSER.S_CONTRACT_BUSI_PROD__BUSI_ITE.nextval from dual ]]>
	</select>
	<!-- 添加操作 -->
	<insert id="JRQD_PA_addContractBusiProdNS"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD(
				IS_RPU,GURNT_START_DATE, BUSI_PRD_ID, GURNT_PERIOD, BUSI_PROD_CODE, HESITATION2ACC, APPLY_CODE, IS_WAIVED, 
				SETTLE_METHOD, UPDATE_BY, POLICY_ID, FLIGHT_NO, WAIVER, GURNT_RATE, MASTER_BUSI_ITEM_ID, 
				UPDATE_TIME, PAIDUP_DATE, EXPIRY_DATE, LIABILITY_STATE, POLICY_CODE, RERINSTATE_DATE, RENEW_DECISION, 
				VALIDATE_DATE, UPDATE_TIMESTAMP, RENEWAL_STATE, ASSURERENEW_FLAG, INSERT_BY, APL_PERMIT, APPLY_DATE, 
				INITIAL_VALIDATE_DATE, RENEW, INSERT_TIMESTAMP, RENEW_TIMES, WAIVER_END, MATURITY_DATE, GURNT_PERD_TYPE, 
				BUSI_ITEM_ID, LAPSE_DATE, JOINT_LIFE_FLAG, INSERT_TIME, END_CAUSE, ISSUE_DATE, LAPSE_CAUSE, 
				DECISION_CODE, SUSPEND_DATE, SUSPEND_CAUSE, INITIAL_PREM_DATE, DUE_LAPSE_DATE, WAIVER_START, PRD_PKG_CODE, 
				GURNT_RENEW_END, GURNT_RENEW_START ,OLD_POL_NO,SURRENDER_FLAG,hesitation_period_day,ORDER_ID) 
			VALUES (
				 #{is_rpu, jdbcType=NUMERIC} ,#{gurnt_start_date, jdbcType=DATE}, #{busi_prd_id, jdbcType=NUMERIC} , #{gurnt_period, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{hesitation2acc, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{is_waived, jdbcType=NUMERIC} 
				, #{settle_method, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{flight_no, jdbcType=VARCHAR} , #{waiver, jdbcType=NUMERIC} , #{gurnt_rate, jdbcType=NUMERIC} , #{master_busi_item_id, jdbcType=NUMERIC} 
				, SYSDATE , #{paidup_date, jdbcType=DATE} , #{expiry_date, jdbcType=DATE} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{renew_decision, jdbcType=NUMERIC} 
				, #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP
		]]>
			<if test=" renewal_state != null"><![CDATA[,#{renewal_state, jdbcType=NUMERIC}]]></if>
			<if test=" renewal_state == null"><![CDATA[, default]]></if>
			<![CDATA[
				, #{assurerenew_flag, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{apl_permit, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} 
				, #{initial_validate_date, jdbcType=DATE} , #{renew, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{renew_times, jdbcType=NUMERIC} , #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{gurnt_perd_type, jdbcType=VARCHAR} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} , #{joint_life_flag, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , #{lapse_cause, jdbcType=VARCHAR} 
				, #{decision_code, jdbcType=VARCHAR} , #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{initial_prem_date, jdbcType=DATE} , #{due_lapse_date, jdbcType=DATE} , #{waiver_start, jdbcType=DATE} , #{prd_pkg_code, jdbcType=VARCHAR} 
				, #{gurnt_renew_end, jdbcType=DATE} , #{gurnt_renew_start, jdbcType=DATE} ,#{old_pol_no, jdbcType=VARCHAR},#{surrender_flag, jdbcType=NUMERIC},#{hesitation_period_day, jdbcType=NUMERIC},#{order_id, jdbcType=VARCHAR}) 
			]]>
	</insert>
	<select id="JRQD_queryBusiProductIsReprise" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   SELECT TCBP.POLICY_CODE,
	       TCBP.BUSI_ITEM_ID,
	       TCBP.POLICY_ID,
	       TCBP.BUSI_PRD_ID
		   FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP, DEV_PAS.T_BUSINESS_PRODUCT TBP
		   WHERE TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
		   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
		   AND TBP.PRODUCT_CATEGORY1 IN ('20001','20002')
		   AND TBP.PRODUCT_CATEGORY2 = '30004'
		   AND TCBP.POLICY_CODE =#{policy_code}
		]]>
	</select>
	
	<select id="JRQD_queryCTPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select 
			(select d.product_abbr_name from app___pds__dbuser.t_business_product d 
			where d.product_code_sys = b.busi_prod_code) productname,
			(select (select nvl(sum(fee_amount),0) 
			from app___pas__dbuser.t_prem_arap a
			where a.arap_flag='1' 
			and a.fee_status='01' 
			and a.business_code=#{acceptCode}
			and a.busi_prod_code = b.busi_prod_code) -
			(select nvl(sum(fee_amount),0) sumfeeamount
			from app___pas__dbuser.t_prem_arap a 
			where a.arap_flag='2' 
			and a.fee_status='01' 
			and a.business_code=#{acceptCode}
			and a.busi_prod_code = b.busi_prod_code) 
			as sumfeeamount from dual) feeamount
			from app___pas__dbuser.t_contract_busi_prod b 
			where b.policy_code=#{policy_code}
		]]>
	</select>

<!-- 通过主险id查询附加险 -->
	<select id="JRQD_PA_findAllContractBusiProdBuMasterBusiProdUniversal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, '1' AS ORDER_ID, A.MEET_POV_STANDARD_FLAG,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000 AND EXISTS (SELECT '1' FROM DEV_PAS.T_BUSINESS_PRODUCT B WHERE B.BUSINESS_PRD_ID = A.BUSI_PRD_ID AND B.PRODUCT_CATEGORY1 = '20003') ]]>			
		<include refid="JRQD_PA_queryContractBusiProdByBusiItemIdCondition" />
		<include refid="JRQD_PA_contractBusiProdByMasterBusiProdCondition" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC]]>
	</select>
	
	<!-- 根据保单号查询主主险信息   redmine:61836 增加失效日期-->
	<select id="JRQD_PA_findAllPolicyRiskInformation" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
            SELECT A.POLICY_CODE,
            A.LIABILITY_STATE,
            A.POLICY_ID,
			A.VALIDATE_DATE,
			A.BUSI_PRD_ID,
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            B.PRODUCT_CODE_SYS,
            A.OLD_POL_NO,
            A.RENEW,
            A.REINSURED,
            B.PRODUCT_NAME_STD,
            B.PRODUCT_NAME_SYS,
            B.PRODUCT_ABBR_NAME,
            B.PRODUCT_CATEGORY2,
            B.COVER_PERIOD_TYPE,
            A.END_CAUSE,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,
            (SELECT SUM(Z.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS STD_PREM_AF,
            (SELECT SUM(Z.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS AMOUNT,
            (SELECT SUM(Z.UNIT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS UNIT,
            (SELECT Z.FIELD1 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER Z 
            WHERE Z.POLICY_ID=A.POLICY_ID AND Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND Z.FIELD1 IS NOT NULL AND ROWNUM=1) AS FIELD1,
            (SELECT Z.PRODUCT_CODE_SYS 
             FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z,APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE Y 
             WHERE Z.BUSINESS_PRD_ID=Y.BUSINESS_PRD_ID AND Y.SERVICE_CODE='RR' AND Z.BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS PRODUCT_CODE_SYS, 
            A.PAIDUP_DATE,
            A.VALIDATE_DATE,
            A.LAPSE_DATE,
            A.APPLY_CODE,
            A.MATURITY_DATE,
            A.EXPIRY_DATE,
            A.COND_PRE_CHECK_FLAG,
            A.BUSI_PROD_CODE MAST_BUSI_PROD_CODE,
            A.LIABILITY_STATE MASTER_LIABILITY_STATE,
            (SELECT TP.CHARGE_PERIOD
		          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TP
		         WHERE TP.BUSI_ITEM_ID = A.BUSI_ITEM_ID
		           AND TP.IS_MASTER_ITEM = 1) CHARGE_PERIOD, /*缴费年期类型*/
		     (SELECT TA.CHANGE_ID
          FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE      TA,
               APP___PAS__DBUSER.T_CS_POLICY_CHANGE      TB,
               APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD TC
         WHERE TA.CHANGE_ID = TB.CHANGE_ID
           AND TA.ACCEPT_ID = TB.ACCEPT_ID
           AND TB.CHANGE_ID = TC.CHANGE_ID
           AND TB.POLICY_CHG_ID = TC.POLICY_CHG_ID
           AND TB.POLICY_ID = A.POLICY_ID
           AND TC.BUSI_ITEM_ID = A.BUSI_ITEM_ID
           AND TC.OLD_NEW = '1'
           AND TC.OPERATION_TYPE = '2'
           AND TA.ACCEPT_STATUS = '18'
           AND TA.SERVICE_CODE = 'EN') CHANGE_ID /*判断是否做过EN保全项*/
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE A.MASTER_BUSI_ITEM_ID IS NULL
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
        AND A.POLICY_CODE=#{policy_code}
        /* ORDER BY A.ORDER_ID ASC */ /*按照核心顺序分别显示主险信息*/ ]]>
	</select>
	
	<!-- 保单号查询出效力状态为有效的附加险数据 -->           
	<select id="JRQD_PA_findAllPolicySubRiskInformation" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
     SELECT A.POLICY_CODE,
            A.BUSI_PRD_ID,
            A.LIABILITY_STATE,
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            B.PRODUCT_CODE_SYS,
            A.OLD_POL_NO,
            B.PRODUCT_NAME_STD,
            B.PRODUCT_ABBR_NAME,
            B.PRODUCT_CATEGORY2,
            B.COVER_PERIOD_TYPE,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,          
            A.VALIDATE_DATE
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE A.MASTER_BUSI_ITEM_ID IS NOT NULL
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
        AND A.LIABILITY_STATE = 1
        AND A.POLICY_CODE=#{policy_code}]]>
	</select>
	
	<!-- 根据保单号查询附加险信息 -->
	<select id="JRQD_PA_findAllPolicyNsRiskInformation" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
            SELECT A.POLICY_CODE,
            A.BUSI_PRD_ID,
            A.LIABILITY_STATE,
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            B.PRODUCT_CODE_SYS,
            A.OLD_POL_NO,
            A.RENEW,
            B.PRODUCT_NAME_STD,
            B.PRODUCT_ABBR_NAME,
            B.PRODUCT_CATEGORY2,
            B.COVER_PERIOD_TYPE,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,
            (SELECT SUM(Z.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS STD_PREM_AF,
            (SELECT SUM(Z.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS AMOUNT,
            (SELECT SUM(Z.UNIT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS UNIT,
            (SELECT Z.FIELD1 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER Z 
            WHERE Z.POLICY_ID=A.POLICY_ID AND Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND Z.FIELD1 IS NOT NULL AND ROWNUM=1) AS FIELD1,
            (SELECT Z.PRODUCT_CODE_SYS 
             FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z,APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE Y 
             WHERE Z.BUSINESS_PRD_ID=Y.BUSINESS_PRD_ID AND Y.SERVICE_CODE='RR' AND Z.BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS PRODUCT_CODE_SYS_A,            
            A.PAIDUP_DATE,
            A.VALIDATE_DATE
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE A.MASTER_BUSI_ITEM_ID IS NOT NULL
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
        AND A.POLICY_CODE=#{policy_code}]]>
	</select>
	
	<!-- 根据保单号查询非终止的附加险信息  redmine:61836 增加失效日期-->
	<select id="JRQD_PA_findAllNonterminationNsRiskInformation" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
            SELECT 
            A.POLICY_ID,
            A.POLICY_CODE,
            A.BUSI_PRD_ID,
            A.APPLY_CODE,
            A.LIABILITY_STATE,
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            B.PRODUCT_CODE_SYS,
            A.OLD_POL_NO,
            A.RENEW,
            A.COND_PRE_CHECK_FLAG,
            B.PRODUCT_NAME_STD,
            B.PRODUCT_NAME_SYS,
            B.PRODUCT_ABBR_NAME,
            B.PRODUCT_CATEGORY2,
            B.COVER_PERIOD_TYPE,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,
            (SELECT SUM(Z.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS STD_PREM_AF,
            (SELECT SUM(Z.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS AMOUNT,
            (SELECT SUM(Z.UNIT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS UNIT,
            (SELECT Z.FIELD1 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER Z 
            WHERE Z.POLICY_ID=A.POLICY_ID AND Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND Z.FIELD1 IS NOT NULL AND ROWNUM=1) AS FIELD1,
            (SELECT Z.PRODUCT_CODE_SYS 
             FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z,APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE Y 
             WHERE Z.BUSINESS_PRD_ID=Y.BUSINESS_PRD_ID AND Y.SERVICE_CODE='RR' AND Z.BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS PRODUCT_CODE_SYS_A,            
            A.PAIDUP_DATE,
            A.VALIDATE_DATE,
            A.LAPSE_DATE,
            A.MATURITY_DATE,
            A.EXPIRY_DATE,
            /** rdmine:78071 新增   附加险简称2(取值与“主险险种简称”一致)**/     
            (SELECT TP.PRODUCT_ABBR_NAME
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T, APP___PAS__DBUSER.T_BUSINESS_PRODUCT TP
         WHERE T.POLICY_CODE = A.POLICY_CODE
           AND T.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID
           AND T.BUSI_PRD_ID = TP.BUSINESS_PRD_ID) MASTER_PRODUCT_ABBR_NAME,
            (SELECT TP.PRODUCT_CODE_SYS
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T, APP___PAS__DBUSER.T_BUSINESS_PRODUCT TP
         WHERE T.POLICY_CODE = A.POLICY_CODE
           AND T.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID
           AND T.BUSI_PRD_ID = TP.BUSINESS_PRD_ID) MAST_BUSI_PROD_CODE,
            (SELECT T.LIABILITY_STATE
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T
         WHERE T.POLICY_CODE = A.POLICY_CODE
           AND T.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID) MASTER_LIABILITY_STATE,
           (SELECT TBPT.PRODUCT_CODE_SYS 
            FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBPT 
            WHERE A.BUSI_PROD_CODE = TBPT.PRODUCT_CODE_SYS 
            AND TBPT.COVER_PERIOD_TYPE = '0')PRODUCTCODESYS,
           A.REINSURED
            
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE A.MASTER_BUSI_ITEM_ID IS NOT NULL
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
        AND A.LIABILITY_STATE != 3 
        AND A.POLICY_CODE=#{policy_code}]]>
	</select>
	<!-- 根据保单号查询有效的附加险信息-->
	<select id="JRQD_PA_findAllValidNsRiskInformation" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
            SELECT 
            A.POLICY_ID,
            A.POLICY_CODE,
            A.BUSI_PRD_ID,
            A.APPLY_CODE,
            A.LIABILITY_STATE,
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            B.PRODUCT_CODE_SYS,
            A.OLD_POL_NO,
            A.RENEW,
            A.COND_PRE_CHECK_FLAG,
            B.PRODUCT_NAME_STD,
            B.PRODUCT_NAME_SYS,
            B.PRODUCT_ABBR_NAME,
            B.PRODUCT_CATEGORY2,
            B.COVER_PERIOD_TYPE,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,
            (SELECT SUM(Z.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS STD_PREM_AF,
            (SELECT SUM(Z.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS AMOUNT,
            (SELECT SUM(Z.UNIT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS UNIT,
            (SELECT Z.FIELD1 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER Z 
            WHERE Z.POLICY_ID=A.POLICY_ID AND Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND Z.FIELD1 IS NOT NULL AND ROWNUM=1) AS FIELD1,
            (SELECT Z.PRODUCT_CODE_SYS 
             FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z,APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE Y 
             WHERE Z.BUSINESS_PRD_ID=Y.BUSINESS_PRD_ID AND Y.SERVICE_CODE='RR' AND Z.BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS PRODUCT_CODE_SYS_A,            
            A.PAIDUP_DATE,
            A.VALIDATE_DATE,
            A.LAPSE_DATE,
            A.MATURITY_DATE,
            A.EXPIRY_DATE,
            /** rdmine:78071 新增   附加险简称2(取值与“主险险种简称”一致)**/     
            (SELECT TP.PRODUCT_ABBR_NAME
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T, APP___PAS__DBUSER.T_BUSINESS_PRODUCT TP
         WHERE T.POLICY_CODE = A.POLICY_CODE
           AND T.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID
           AND T.BUSI_PRD_ID = TP.BUSINESS_PRD_ID) MASTER_PRODUCT_ABBR_NAME,
            (SELECT TP.PRODUCT_CODE_SYS
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T, APP___PAS__DBUSER.T_BUSINESS_PRODUCT TP
         WHERE T.POLICY_CODE = A.POLICY_CODE
           AND T.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID
           AND T.BUSI_PRD_ID = TP.BUSINESS_PRD_ID) MAST_BUSI_PROD_CODE,
            (SELECT T.LIABILITY_STATE
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T
         WHERE T.POLICY_CODE = A.POLICY_CODE
           AND T.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID) MASTER_LIABILITY_STATE,
           (SELECT TBPT.PRODUCT_CODE_SYS 
            FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBPT 
            WHERE A.BUSI_PROD_CODE = TBPT.PRODUCT_CODE_SYS 
            AND TBPT.COVER_PERIOD_TYPE = '0')PRODUCTCODESYS,
           A.REINSURED
            
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE A.MASTER_BUSI_ITEM_ID =#{busi_item_id}
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
        AND A.LIABILITY_STATE = 1 
        AND A.POLICY_CODE=#{policy_code}]]>
	</select>
	<!-- 根据保单号查询主险下有效的附加险信息-->
	<select id="JRQD_PA_findAllMasterValidNsRiskInformation" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
            SELECT 
            A.POLICY_ID,
            A.POLICY_CODE,
            A.BUSI_PRD_ID,
            A.APPLY_CODE,
            A.LIABILITY_STATE,
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            B.PRODUCT_NAME_SYS,
            B.PRODUCT_ABBR_NAME,
            B.PRODUCT_CODE_SYS,
            A.VALIDATE_DATE,
            A.RENEW,
            A.COND_PRE_CHECK_FLAG
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE A.MASTER_BUSI_ITEM_ID =#{busi_item_id}
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
        AND A.LIABILITY_STATE = 1 
        AND A.POLICY_CODE=#{policy_code}]]>
	</select>
	<select id="JRQD_PA_findNsRiskInformation" resultType="java.util.Map" parameterType="java.util.Map">
   	<![CDATA[
   		SELECT A.POLICY_CODE,
   			   A.POLICY_ID,
   			   A.BUSI_PRD_ID,	
   			   A.VALIDATE_DATE,
   			   A.REINSURED,
		       A.BUSI_ITEM_ID,
		       A.BUSI_PROD_CODE,
		       A.MASTER_BUSI_ITEM_ID,
		       A.LIABILITY_STATE,
		       A.RENEW,/*是否可续保*/
		       CASE
		         WHEN A.MASTER_BUSI_ITEM_ID IS NOT NULL THEN
		          (SELECT B.LIABILITY_STATE
		             FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
		            WHERE B.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID)
		         ELSE
		          NULL
		       END AS MAST_LIABILITY_STATE,
		       CASE
		         WHEN A.MASTER_BUSI_ITEM_ID IS NOT NULL THEN
		          (SELECT B.BUSI_PROD_CODE
		             FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
		            WHERE B.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID)
		         ELSE
		          NULL
		       END AS MAST_BUSI_PROD_CODE,
		       (SELECT Z.PRODUCT_CODE_SYS
		          FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT         Z,
		               APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE Y
		         WHERE Z.BUSINESS_PRD_ID = Y.BUSINESS_PRD_ID
		           AND Y.SERVICE_CODE = 'RR'
		           AND Z.BUSINESS_PRD_ID = A.BUSI_PRD_ID) AS PRODUCT_CODE_SYS_A,/*可转换险种代码*/
		       (SELECT TA.CHANGE_ID
		          FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE      TA,
		               APP___PAS__DBUSER.T_CS_POLICY_CHANGE      TB,
		               APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD TC
		         WHERE TA.CHANGE_ID = TB.CHANGE_ID
		           AND TA.ACCEPT_ID = TB.ACCEPT_ID
		           AND TB.CHANGE_ID = TC.CHANGE_ID
		           AND TB.POLICY_CHG_ID = TC.POLICY_CHG_ID
		           AND TB.POLICY_ID = A.POLICY_ID
		           AND TC.BUSI_ITEM_ID = A.BUSI_ITEM_ID
		           AND TC.OLD_NEW = '1'
		           AND TC.OPERATION_TYPE = '2'			          
		           AND TA.ACCEPT_STATUS = '18'
		           AND TA.SERVICE_CODE = 'EN'
		           AND ROWNUM = 1) CHANGE_ID/*判断是否做过EN保全项*/
		  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
		       
		 WHERE A.POLICY_CODE = #{policy_code}        
            
    ]]>
	</select>
	
	
	<!-- 根据保单号查询附加险信息 -->
	<select id="JRQD_PA_findAllPolicyRisk" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
            SELECT A.POLICY_CODE,
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            B.PRODUCT_CODE_SYS,
            A.OLD_POL_NO,
            A.RENEW,
            B.PRODUCT_NAME_STD,
            B.PRODUCT_ABBR_NAME,
            B.PRODUCT_CATEGORY2,
            B.COVER_PERIOD_TYPE,
            B.TAX_REVENUE_FLAG,
            A.HESITATION_PERIOD_DAY,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,
            (SELECT SUM(Z.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS STD_PREM_AF,
            (SELECT SUM(Z.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS AMOUNT,
            (SELECT SUM(Z.UNIT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS UNIT,
            A.PAIDUP_DATE,
            A.VALIDATE_DATE
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
        AND A.POLICY_CODE=#{policy_code}]]>
  
	</select>
	
	<!-- 查询是否有年金或者满期金 -->
	<select id="JRQD_PA_findAllExpirationDateGold" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
     SELECT A.POLICY_CODE, A.PAY_PLAN_TYPE
          FROM APP___PAS__DBUSER.T_PAY_PLAN A
         WHERE A.PAY_PLAN_TYPE IN ('2','3','4','8','10','11')
           AND A.POLICY_CODE = #{policy_code}
           AND A.BUSI_ITEM_ID = #{busi_item_id}]]>
	</select>
		
	<!-- 查询主险信息 -->
	<select id="JRQD_findAllMainBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		
		<![CDATA[	
			SELECT CBP.POLICY_CODE,TBP.PRODUCT_NAME_SYS,TBP.PRODUCT_CATEGORY,tbp.product_name_std,tbp.product_abbr_name,CBP.VALIDATE_DATE,
			 tbp.product_code_sys,tbp.product_code_std,CBP.BUSI_PROD_CODE,CBP.BUSI_ITEM_ID,CBP.LIABILITY_STATE,CBP.END_CAUSE
	        FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
	             APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
	       WHERE CBP.MASTER_BUSI_ITEM_ID IS NULL
	         AND CBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
	         AND CBP.POLICY_CODE = #{policy_code}
         ]]>	 
				
	</select>
	
	<!-- 查询原贷款金额\原贷款起期\原贷款止期\原贷款起息日 -->
	<select id="JRQD_findLoansByBusiitenid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	
			 select
	          B.CAPITAL_BALANCE CAPITAL_BALANCE_OLD /*原贷款金额*/,
	          TO_CHAR(B.LOAN_START_DATE, 'YYYY-MM-DD') LOAN_START_DATE_OLD /*原贷款起期*/,
	          TO_CHAR(B.REPAY_DUE_DATE, 'YYYY-MM-DD') REPAY_DUE_DATE_OLD /*原贷款止期*/,
	          TO_CHAR(B.Interest_Start_Date, 'YYYY-MM-DD') INTEREST_START_DATE_OLD /*原贷款起息日*/
	        from APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM B
	        where b.BUSI_ITEM_ID=#{busi_item_id}
	        and b.ACCOUNT_TYPE=4
	        order by B.INTEREST_START_DATE desc
         ]]>	 
				
	</select>
	
	<select id="JRQD_PA_PolicyMainRiskREQuery" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
      SELECT distinct  A.POLICY_CODE , --保单号
            A.BUSI_ITEM_ID,-- 险种ID
            A.BUSI_PROD_CODE as RiskCode,-- 所属业务产品代码
            B.PRODUCT_CODE_SYS,-- 产品代码
            B.PRODUCT_NAME_STD as MainAllName,-- 产品名称
            B.PRODUCT_ABBR_NAME as MainName,--产品简称
            C.PAY_DUE_DATE as PayDate,-- 交费日期
            A.VALIDATE_DATE , -- 生效日期
            d.total_prem_af as PremStad, -- 标准保费
            d.amount as BasicAmont,
            d.unit as Mult, -- 分数
            a.Lapse_Date as LoseDate --失效日期
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B,
            APP___PAS__DBUSER.T_CONTRACT_EXTEND    C,
            APP___PAS__DBUSER.t_Contract_Product d
      WHERE A.POLICY_CODE = C.POLICY_CODE
        AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
        AND A.MASTER_BUSI_ITEM_ID IS NULL
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
        and a.policy_code = d.policy_code
        and c.busi_item_id = d.busi_item_id
        AND A.POLICY_CODE=#{policy_code}]]>
	</select>
	<select id="JRQD_PA_PolicyMainRiskREBuMasterBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT distinct  A.POLICY_CODE , --保单号
            A.BUSI_ITEM_ID,-- 险种ID
            A.BUSI_PROD_CODE as NsRiskCode,-- 所属业务产品代码
            B.PRODUCT_CODE_SYS,-- 产品代码
            B.PRODUCT_NAME_STD as NsRiskAllName,-- 产品名称
            B.PRODUCT_ABBR_NAME as NsRiskName,--产品简称
            C.PAY_DUE_DATE as NsPayDate,-- 交费日期
            A.VALIDATE_DATE , -- 生效日期
            d.total_prem_af as NsPremStad, -- 标准保费
            d.amount as NsBasicAmont,
            d.unit as NsMult, -- 分数
            a.Lapse_Date as NsLoseDate --失效日期
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B,
            APP___PAS__DBUSER.T_CONTRACT_EXTEND    C,
            APP___PAS__DBUSER.t_Contract_Product d
      WHERE A.POLICY_CODE = C.POLICY_CODE
        AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
        AND A.MASTER_BUSI_ITEM_ID IS NOT NULL
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
        and a.policy_code = d.policy_code
        and c.busi_item_id = d.busi_item_id
        and a.liability_state <> '3' 
        AND A.POLICY_CODE=#{policy_code}  ]]>			
		<include refid="JRQD_PA_queryContractBusiProdByBusiItemIdCondition" />
		<include refid="JRQD_PA_contractBusiProdByMasterBusiProdCondition" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC]]>
	</select>
	
	<select id="JRQD_PA_findAllMedicalProds" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
	    select cbp.policy_code,cbp.policy_id,cbp.busi_item_id,cbp.master_busi_item_id,cbp.old_pol_no,cbp.busi_prd_id,cbp.busi_prod_code 
	       from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD cbp 
	       where cbp.busi_prod_code in  (
		          '00557000',
		          '00539000',
		          '00538000',
		          '00537100',
		          '00537200',
		          '00868000',
		          '00558000'       )   
	          and cbp.policy_code = #{policy_code}   
	      ]]>
		
	</select>
	
		<!-- 根据保单号查询主险险种信息 -->
	<select id="JRQD_PA_findNextPayDateByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
	        SELECT A.VALIDATE_DATE, A.RENEW, C.COVER_PERIOD_TYPE, B.PAY_DUE_DATE,A.PAIDUP_DATE
      FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
           APP___PAS__DBUSER.T_CONTRACT_EXTEND    B,
           APP___PDS__DBUSER.T_BUSINESS_PRODUCT   C
     WHERE A.BUSI_PRD_ID = C.BUSINESS_PRD_ID
       AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
       AND A.BUSI_ITEM_ID = #{busi_item_id}
	</select>
	
	<!-- 根据保单号查询主险险种信息 -->
	<select id="JRQD_findconMain" resultType="java.util.Map" parameterType="java.util.Map">
	    select cbp.expiry_date,cbp.liability_state from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD cbp 
	    where cbp.policy_code = #{policy_code}
        and cbp.master_busi_item_id is null	
	</select>
	
	<!-- 受益人变更-根据保单号查询所有主险信息(多主险) -->
	<select id="JRQD_findAllBusiProdForQueryBeneChange" resultType="java.util.Map" parameterType="java.util.Map">
	      SELECT   CBP.POLICY_CODE,
			       TBP.PRODUCT_NAME_SYS,
			       TBP.PRODUCT_NAME_STD,
			       TBP.PRODUCT_ABBR_NAME,
			       CBP.BUSI_ITEM_ID,
			       CBP.BUSI_PROD_CODE,
			       CBP.MASTER_BUSI_ITEM_ID,
			       CBP.LIABILITY_STATE,
			       (SELECT CASE
			                 WHEN COUNT(A.PLAN_ID) > 0 THEN
			                  'Y'
			                 ELSE
			                  'N'
			               END
			          FROM APP___PAS__DBUSER.T_PAY_PLAN A
			         WHERE A.PAY_PLAN_TYPE IN ('2', '3', '4', '8', '10', '11')
			           AND A.POLICY_CODE = CBP.POLICY_CODE
			           AND A.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS IS_TYPE
		  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
		       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
		 WHERE CBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		   AND CBP.MASTER_BUSI_ITEM_ID IS NULL 
		   AND CBP.POLICY_CODE = #{policy_code}
	</select>
	
	<!-- P00001003191 受益人变更-保单列表查询 -->
	<select id="JRQD_findBusiProdListByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[ SELECT  CBP.POLICY_CODE,
					       TBP.PRODUCT_NAME_SYS,
					       TBP.PRODUCT_NAME_STD,
					       TBP.PRODUCT_ABBR_NAME,
					       CBP.BUSI_ITEM_ID,
					       CBP.BUSI_PROD_CODE,
					       CBP.MASTER_BUSI_ITEM_ID,
					       CBP.LIABILITY_STATE,
					       (SELECT CASE
					                 WHEN COUNT(A.PLAN_ID) > 0 THEN
					                  'Y'
					                 ELSE
					                  'N'
					               END
					          FROM APP___PAS__DBUSER.T_PAY_PLAN A
					         WHERE A.PAY_PLAN_TYPE IN ('2', '3', '4', '8', '10', '11')
					           AND A.POLICY_CODE = CBP.POLICY_CODE
					           AND A.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS IS_TYPE
				  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
				       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
				 WHERE CBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
				   AND CBP.POLICY_CODE = #{policy_code} ]]>
		   <if test=" master_busi_item_id  != null "><![CDATA[ AND CBP.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
	</select>
	<!-- P00001002344  附加险险种信息查询接口 -->
	<select id="JRQD_findHMTotalPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select sum(B.std_prem_af) std_prem_af
      from 
           APP___PAS__DBUSER.t_contract_product    B,
           APP___PAS__DBUSER.t_contract_busi_prod  C,
           APP___PAS__DBUSER.t_business_product    D
     where B.POLICY_ID=C.POLICY_ID
       AND B.BUSI_ITEM_ID=C.BUSI_ITEM_ID
       and C.BUSI_PRD_ID=D.BUSINESS_PRD_ID
       and D.cover_period_type = '0'
       AND C.POLICY_CODE=#{policy_code}
		]]>
	</select>
	
	<!-- 贷款清偿客户保单查询接口，查询保单是否有精选产品 -->
	<select id="JRQD_PA_findIsContainSelectedProd" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1) FROM APP___PAS__DBUSER.t_contract_busi_prod A WHERE A.POLICY_CODE=#{policy_code}
		AND A.busi_prod_code in ('00912100','00913000') ]]>
	</select>
	
	<!-- 贷款清偿客户保单查询接口，查询保单详细信息 PA_findPolicyDetailInfo -->
	<select id="JRQD_PA_findPolicyDetailInfo" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[ SELECT DISTINCT TCM.POLICY_CODE, TCP.POLICY_ID, TCP.BUSI_ITEM_ID, TCP.ITEM_ID,tcbp.busi_prd_id,
	        bp.product_name_std,bp.product_name_sys,bp.product_abbr_name,bp.product_code_sys,
	        tcbp.master_busi_item_id,tcp.amount,tcp.std_prem_af,tcp.unit,tcp.validate_date, 
	        bp.cover_period_type,bp.renew_option,tcbp.renew,tcbp.end_cause,tcbp.liability_state	
		          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
		          APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
		          APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
		          APP___PAS__DBUSER.t_Business_Product bp
		      WHERE 1 = 1
		      AND TCM.POLICY_CODE = TCP.POLICY_CODE
		      AND TCM.POLICY_CODE = TCBP.POLICY_CODE
		      AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
		      and bp.business_prd_id = tcbp.busi_prd_id
		      and tcm.policy_code = #{policy_code} ]]>
		      
	   
	</select>
	 
	<!-- 贷款清偿客户保单查询接口，查询该产品是否可操作某保全项 -->
	
	<select id="JRQD_PA_findOperableEdorType" resultType="java.util.Map" parameterType="java.util.Map">
       select s.service_name,c.service_code
		from APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE C,
		     APP___PDS__DBUSER.t_service  s
		where c.service_code = s.service_code
		  and c.service_code = #{service_code}
		  and c.business_prd_id = #{busi_prd_id}

	</select>
	
	<!-- 贷款清偿客户保单查询接口，查询该产品是否操作过满期不续保 -->
	
	<select id="JRQD_PA_findIsOperateEnFlag" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT A.OPERATION_TYPE,A.BUSI_PRD_ID,A.BUSI_ITEM_ID
                FROM dev_pas.t_cs_policy_change      t,
                     DEV_PAS.T_CS_CONTRACT_BUSI_PROD A,
                     dev_pas.t_Cs_Accept_Change      B
               WHERE 1 = 1
                 AND T.ACCEPT_ID = B.ACCEPT_ID
                 AND T.POLICY_CHG_ID = A.POLICY_CHG_ID
				 AND B.ACCEPT_STATUS = '18'
				 AND A.OLD_NEW = '1'
				 AND A.OPERATION_TYPE = '2'
				 AND t.service_code = 'EN'
                 AND A.BUSI_PRD_ID =#{busi_prd_id}
                 AND A.Policy_Code =#{policy_code}

	</select>
	
	<!-- 查询保单险种下续保险种转换待生效时转换后的险种代码 -->
	<select id="JRQD_PA_queryRenewalInsuranceConversion" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ 
      select d.product_code_sys as BUSI_PROD_CODE,d.business_prd_id as BUSI_PRD_ID from Dev_pas.t_Contract_Busi_Prod A,
                 Dev_pas.t_Contract_Product B,
                 Dev_pas.t_Renew_Change C,
                 Dev_pds.t_Business_Product D
           where A.Policy_Id = B.policy_id
             and A.Busi_Item_Id = B.Busi_Item_Id
             and B.Policy_Id = C.Policy_Id
             and b.item_id = C.Item_Id
             and a.busi_prd_id = c.old_busi_prd_id
             and c.new_busi_prd_id = d.business_prd_id
             and a.liability_state = #{liability_state}
             and c.valid_status = #{valid_status}
             and a.policy_id = #{policy_id}
         ]]>
         <if test=" renew_change_type != null and renew_change_type != ''  "><![CDATA[ AND c.renew_change_type = #{renew_change_type} ]]></if>
         <if test=" itemId != null and itemId != ''  "><![CDATA[ AND c.item_Id = #{itemId} ]]></if>
         <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	
	<!-- 移动保全2.0 年金满期金领取保单列表查询接口 -->
	<select id="JRQD_PA_PolicyAGRiskQuery" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[   SELECT DISTINCT A.ORDER_ID AS ORDERID,
	                  A.LIABILITY_STATE AS LIABILITYSTATE, /*险种状态*/
	                  A.END_CAUSE AS ENDCAUSE, /*终止原因*/
	                  A.BUSI_ITEM_ID AS BUSIITEMID,
	                  B.PRODUCT_CODE_SYS AS RISKCODE, /*产品代码*/
	                  B.PRODUCT_NAME_STD AS RISKNAME, /*产品名称*/
	                  B.PRODUCT_ABBR_NAME AS RISKSHORTNAME, /*产品简称*/
	                  (CASE WHEN 1 = (SELECT 1 FROM DEV_PAS.T_PAY_PLAN L WHERE L.BUSI_ITEM_ID = A.BUSI_ITEM_ID AND L.POLICY_CODE = A.POLICY_CODE
	                                 AND ROWNUM = 1) THEN 'Y' ELSE 'N' END) AS ISTYPE, /*是否为年金险产品*/
	                  (SELECT (CASE WHEN T.PREM_FREQ = '1' THEN '' ELSE TO_CHAR(C.PAY_DUE_DATE, 'yyyy-mm-dd') END)
	                                 FROM DEV_PAS.T_CONTRACT_PRODUCT T WHERE T.BUSI_ITEM_ID = A.BUSI_ITEM_ID AND ROWNUM = 1) AS PAYDATE, /*交费日期*/
	                  TO_CHAR(A.VALIDATE_DATE, 'yyyy-mm-dd') AS CVALIDATE /*生效日期*/
			     FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
			          APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B,
			          APP___PAS__DBUSER.T_CONTRACT_EXTEND    C
			    WHERE A.POLICY_CODE = C.POLICY_CODE
			      AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
			      AND A.MASTER_BUSI_ITEM_ID IS NULL
			      AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
			      AND A.POLICY_CODE = #{policy_code} 
			      ORDER BY A.ORDER_ID ]]>
	</select>
	
	<!-- 移动保全2.0 年金满期金领取保单列表查询接口 -->
	<select id="JRQD_PA_GetAmntInfoQuery" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
    select 
        e.liab_name as GetDutyName, -- 领取项目
        to_char(l.pay_due_date,'yyyy-mm-dd') as getdate,-- 应领日期
        l.fee_amount as GetMoney, -- 应领金额
       (select decode(h.pay_plan_type,'3','年金',h.type_desc)   from dev_pas.T_PAY_PLAN_TYPE h where h.pay_plan_type = r.PAY_PLAN_TYPE ) as GetType -- 给付分类
           from dev_pas.t_pay_due l,
       dev_pas.t_contract_product q,
       dev_pas.T_PAY_PLAN r,
       dev_pas.T_LIABILITY e
       where  1=1
        and l.policy_code=q.policy_code
        and l.plan_id = r.plan_id
        and l.busi_item_id = q.busi_item_id
        and l.liab_id = e.liab_id 
        and l.busi_item_id = #{busi_item_id}
        and l.pay_due_date <= sysdate
        and l.fee_status = '00'
        and  exists ( select 1 from dev_pas.t_prem_arap b 
       where b.policy_code = l.policy_code and b.unit_number = l.unit_number and b.payrefno is null)
       ]]>
	</select>
	
	<select id="JRQD_PA_PolicyAGAddRiskQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT distinct  
            A.BUSI_ITEM_ID as BusiItemId ,-- 险种ID
            B.PRODUCT_CODE_SYS as AdditionRiskCode,-- 产品代码
            B.PRODUCT_NAME_STD as AdditionRiskName,-- 产品名称
            B.PRODUCT_ABBR_NAME as AdditionRiskShortName,--产品简称
            (case when b.Cover_Period_Type in ('1','2','3') and a.RENEW = '1' then to_char(a.MATURITY_DATE,'yyyy-mm-dd') 
             when b.Cover_Period_Type = '0' 
              and 1=(  select 1
               from dev_pas.t_contract_product t where
                t.busi_item_id = a.busi_item_id and t.prem_freq != '1' and rownum = 1)
              then to_char(C.PAY_DUE_DATE,'yyyy-mm-dd') else '' end ) as PayDate,-- 交费日期,
            to_char(A.VALIDATE_DATE,'yyyy-mm-dd') as CValiDate  -- 生效日期
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B,
            APP___PAS__DBUSER.T_CONTRACT_EXTEND    C
      WHERE A.POLICY_CODE = C.POLICY_CODE
        AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
        AND A.MASTER_BUSI_ITEM_ID IS NOT NULL
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
        and a.liability_state <> '3' 
        AND A.POLICY_CODE=#{policy_code}  ]]>			
		<include refid="JRQD_PA_queryContractBusiProdByBusiItemIdCondition" />
		<include refid="JRQD_PA_contractBusiProdByMasterBusiProdCondition" />
		
	</select>
	
	<!-- 客户保单基本信息查询接口险种层查询sql -->
	<select id="JRQD_queryCusPlyBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT T.BUSI_ITEM_ID,T.BUSI_PROD_CODE RISKCODE,T.EXPIRY_DATE EXPIRYDATE,
			     (SELECT TBP.PRODUCT_ABBR_NAME FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP WHERE TBP.PRODUCT_CODE_SYS=T.BUSI_PROD_CODE) RISKNAME,
			     (SELECT TBPA.PRODUCT_NAME_STD FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBPA WHERE TBPA.PRODUCT_CODE_SYS=T.BUSI_PROD_CODE) ALLRISKNAME,
			     DECODE(T.MASTER_BUSI_ITEM_ID,'' ,'Y','N') SUBRISKFLAG,
				 T.LIABILITY_STATE APPFLAG,
				 T.REINSURED RENEW,  
				 (SELECT COUNT(TCP.UNIT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID) MULT,
				 (SELECT  (SELECT DECODE(TCPO.FIELD1,'1','计划一','2','计划二','3','计划三','') FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER TCPO 
			                  WHERE TCPO.ITEM_ID=TCP.ITEM_ID AND ROWNUM=1)
				  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID AND TCP.IS_MASTER_ITEM=1
				  AND ROWNUM=1) STANDBYFLAG,
				 (SELECT SUM(TCP.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID) PREM,
				 (SELECT SUM(TEP.EXTRA_PREM) FROM APP___PAS__DBUSER.T_EXTRA_PREM TEP WHERE TEP.EXTRA_TYPE=1 AND TEP.BUSI_ITEM_ID=T.BUSI_ITEM_ID) HEALTHFEE,
				 (SELECT SUM(TEP.EXTRA_PREM) FROM APP___PAS__DBUSER.T_EXTRA_PREM TEP WHERE TEP.EXTRA_TYPE=2 AND TEP.BUSI_ITEM_ID=T.BUSI_ITEM_ID) PROFESSIONALFEE,
				 TO_CHAR(T.VALIDATE_DATE,'YYYY-MM-DD') CVALIDATE,
				 (SELECT TO_CHAR(MAX(TCP.MATURITY_DATE),'YYYY-MM-DD') FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID) ENDDATE,
				 (SELECT TO_CHAR(MIN(TCE.PAY_DUE_DATE),'YYYY-MM-DD') FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE WHERE TCE.BUSI_ITEM_ID=T.BUSI_ITEM_ID) PAYTODATE,
				 (SELECT TO_CHAR(MIN(TCP.PAIDUP_DATE),'YYYY-MM-DD') FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID) PAYENDDATE,
				 CASE WHEN (SELECT COUNT(*) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID AND TCP.IS_MASTER_ITEM=1)>0
					 THEN (SELECT 
						       CASE WHEN TCP.CHARGE_YEAR=1 THEN '趸交' ELSE 
						       TCP.CHARGE_YEAR || DECODE(TCP.CHARGE_PERIOD,'0','','2','年','3','岁','') END
						 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID AND TCP.IS_MASTER_ITEM=1 AND ROWNUM=1)
				   ELSE (SELECT 
						      CASE WHEN TCP.CHARGE_YEAR=1 THEN '趸交' ELSE 
									     TCP.CHARGE_YEAR || DECODE(TCP.CHARGE_PERIOD,'0','','2','年','3','岁','') END
						 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID AND ROWNUM=1)
						 END PAYYEARS,
				   CASE WHEN (SELECT COUNT(*) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID AND TCP.IS_MASTER_ITEM=1)>0
					 THEN (SELECT (SELECT TCM.CHARGE_NAME FROM APP___PAS__DBUSER.T_CHARGE_MODE TCM WHERE TCM.CHARGE_TYPE=TCP.PREM_FREQ) 
						 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID AND TCP.IS_MASTER_ITEM=1 AND ROWNUM=1)
				   ELSE (SELECT (SELECT TCM.CHARGE_NAME FROM APP___PAS__DBUSER.T_CHARGE_MODE TCM WHERE TCM.CHARGE_TYPE=TCP.PREM_FREQ) 
						 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP WHERE T.BUSI_ITEM_ID=TCP.BUSI_ITEM_ID AND ROWNUM=1)
						 END PAYINTV
 			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD T WHERE T.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 根据险种id查询险种信息 -->
	<select id="JRQD_PA_findBusiByPolicyAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
             select 
                  tcbp.busi_prod_code,
                  tbp.product_name_sys,
                  tcbp.validate_date,
                  tbp.product_abbr_name,
                  tcbp.master_busi_item_id,
                  tcbp.liability_state
                  from APP___PAS__DBUSER.t_contract_busi_prod tcbp,APP___PAS__DBUSER.t_business_product tbp
             where tcbp.busi_prd_id = tbp.business_prd_id
                  
                   <if test="busi_item_id!=null">
                    and tcbp.busi_item_id = #{busi_item_id}
                   </if>
                   
                   <if test="policy_code!=null">
                    and tcbp.policy_code = #{policy_code}
                   </if>
                   
	</select>
	<!-- 根据险种id查询主险险种信息 -->
	<select id="JRQD_PA_findMasterBusiByPolicyAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
             select 
                  tcbp.busi_prod_code,
                  tbp.product_name_sys,
                  tcbp.validate_date,
                  tbp.product_abbr_name,
                  tcbp.BUSI_ITEM_ID,
                  tcbp.liability_state,
                  tcbp.END_CAUSE,
                  tcbp.POLICY_CODE
                  from APP___PAS__DBUSER.t_contract_busi_prod tcbp,APP___PAS__DBUSER.t_business_product tbp
             where tcbp.busi_prd_id = tbp.business_prd_id
                  and tcbp.	MASTER_BUSI_ITEM_ID is null
                   <if test="policy_code!=null">
                    and tcbp.policy_code = #{policy_code}
                   </if>
                   
	</select>
	<!-- 根据保单号查询险种、责任以及生存给付计划信息 -->
	<select id="JRQD_PA_querySurvivalBenefitPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select b.PAY_STATUS,
			        b.TOTAL_AMOUNT
			 from APP___PAS__DBUSER.T_CONTRACT_MASTER a,
			      APP___PAS__DBUSER.T_PAY_PLAN        b
			 where a.policy_code = b.policy_code
			      and b.pay_plan_type in ('3','4')
			      and a.policy_code = #{policy_code}
  		]]>
	</select>
	
	
	<!-- 根据保单号查询附加险849险种信息 -->
	<select id="JRQD_PA_query849busiCodebyPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select distinct c.policy_code,c.busi_item_id 
			from dev_pas.t_contract_busi_prod c
			left join (select b.policy_code from 
			dev_pas.t_cs_contract_busi_prod b
			where b.busi_prod_code = '00849000'
			and b.liability_state = '1'
			and b.master_busi_item_id is not null) a
			on a.policy_code = c.policy_code
			where c.master_busi_item_id is null
			and c.liability_state = '3'
			and c.end_cause = '02'
			and c.policy_code = #{policy_code}
  		]]>
  		<if test="end_date != null and end_date != ''">
			<![CDATA[and c.expiry_date >= #{end_date} ]]>
		</if>
	</select>
	 <!--#掌上新华_核心接口_新契约投保包含849产品保单处理规则优化-新核心:查询附加险信息  -->
	 <select id="JRQD_querybusiCodeInfobyPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
	     SELECT A.POLICY_CODE, A.BUSI_PROD_CODE, A.LIABILITY_STATE
  			 FROM APP___PAS__DBUSER.t_Contract_Busi_Prod A
				  WHERE A.POLICY_CODE = #{policy_code}
   					AND A.BUSI_PROD_CODE = #{busi_prod_code}
   					AND A.LIABILITY_STATE = #{liability_state}
   					AND A.MASTER_BUSI_ITEM_ID IS NOT NULL
     ]]>
	 </select>
	 
	 <!--查询单独投保险种  -->
	 <select id="JRQD_PA_queryContractBusiProdfobySubPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
	       SELECT TCBP.POLICY_CODE,
         TCBP.BUSI_PROD_CODE,
         TCBP.BUSI_ITEM_ID,
         TCBP.OLD_POL_NO,
         TCBP.LIABILITY_STATE,
         TCBP.MASTER_BUSI_ITEM_ID,
         TCBP.END_CAUSE,
         TBP.ACC_RISK_SINGLE_INSURE_FLAG
    FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
         APP___PDS__DBUSER.T_BUSINESS_PRODUCT   TBP
   WHERE 1 = 1
     AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
     AND TCBP.POLICY_ID = #{policy_id}
     ]]>
	 </select>
	 <!-- 查询主险的险种信息 -->
	 <select id="JRQD_PA_findMasterBusi" resultType="java.util.Map" parameterType="java.util.Map">
	     select tbt.cover_period_type from APP___PAS__DBUSER.t_contract_busi_prod tcbp,APP___PDS__DBUSER.T_BUSINESS_PRODUCT tbt
	     where tcbp.busi_prd_id = tbt.business_prd_id
	     and tcbp.master_busi_item_id is null
	     and tcbp.policy_code = #{policy_code}
	 </select>
	 
	 <!-- 查询是否做过续保转投 -->
	 <select id="JRQD_PA_queryRenewChangeByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,TRC.RENEW_CHANGE_TYPE,A.IS_RPU,A.HESITATION_PERIOD_DAY,
          A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
      A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
      A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
      A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
      A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
      A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
      A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
      A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG 
      FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,APP___PAS__DBUSER.T_RENEW_CHANGE TRC ,
      dev_pas.t_contract_product tcp
      WHERE TRC.VALID_STATUS='0'
        AND TRC.RENEW_CHANGE_TYPE=3
        AND A.POLICY_CODE=TRC.POLICY_CODE
        and tcp.item_id = trc.item_id
        and tcp.busi_item_id = a.busi_item_id
        AND A.BUSI_PRD_ID=TRC.OLD_BUSI_PRD_ID
        AND A.RENEW = 1
	    ]]>  
	    <include refid="JRQD_PA_contractBusiProdWhereCondition" />
	 </select>
	 
	 
	  <!-- 险种是否满足追加保费 -->
	 <select id="JRQD_queryPolicyZJBF" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ select tbp.product_name_sys,
			       tbps.service_code,
			       tbp.product_name_std,
			       tbp.PRODUCT_CODE_STD,
			       tbp.PRODUCT_CODE_SYS
			  from APP___PAS__DBUSER.t_Contract_Busi_Prod         tcbp,
			       APP___PDS__DBUSER.t_business_product tbp,
			       APP___PDS__DBUSER.t_business_product_service   tbps
			 where 1 = 1
			   and tcbp.busi_prd_id = tbp.business_prd_id
			   and tbp.business_prd_id = tbps.business_prd_id
			   and tbps.service_code = 'AM'
				 AND tcbp.liability_state <> '3'
			   and tcbp.policy_code =#{policy_code}
	    ]]> 
	 </select>
	 <!-- 贷款清偿客户保单查询接口，查询GC保全项下的银行账号信息 findBankInfo -->
	 <select id="JRQD_PA_findBankInfo" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[ SELECT DISTINCT tba.bank_account bank_account 
                    FROM dev_pas.t_pay_plan_payee tpp,
                    dev_pas.t_pay_plan tp,
                    dev_pas.t_bank_account tba
                    WHERE 1 = 1
                    AND tpp.plan_id = tp.plan_id
                    AND tpp.item_id = tp.item_id
                    AND tpp.payee_account_id = tba.account_id
                    AND tp.pay_plan_type in ('3','10','11')
                    AND tp.survival_mode = '1'
                    AND tp.survival_w_mode = '2'
                    AND tp.pay_status <> '4'
                    AND tp.item_id = #{item_id}
          ]]>
</select>

		<!-- 查询保单主险是否操作过续保险种转换且已生效 -->
	 <select id="JRQD_PA_findMainRenewalFlag" resultType="java.util.Map" parameterType="java.util.Map">
   			SELECT B.POLICY_CODE, B.BUSI_PROD_CODE,T.VALID_TIME
			  FROM APP___PAS__DBUSER.T_RENEW_CHANGE T, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
			 WHERE T.POLICY_CODE = B.POLICY_CODE
			   AND T.OLD_BUSI_PRD_ID = B.BUSI_PRD_ID
			   AND B.POLICY_CODE = #{policy_code}
			   AND B.BUSI_PROD_CODE = #{busi_prod_code}
			   AND T.VALID_TIME BETWEEN  B.VALIDATE_DATE AND B.MATURITY_DATE
			   AND T.VALID_STATUS = '0'
	 </select>
	 
	<!-- 查询险种信息  -->
	<select id="JRQD_PA_findAllPolicyRiskByPilicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	 SELECT A.POLICY_CODE,
            A.LIABILITY_STATE,
            A.END_CAUSE,
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            B.PRODUCT_CODE_SYS,
            A.OLD_POL_NO,
            A.MASTER_BUSI_ITEM_ID,
            A.RENEW,
            B.PRODUCT_NAME_STD,
            B.PRODUCT_NAME_SYS,
            B.PRODUCT_ABBR_NAME,
            B.PRODUCT_CATEGORY2,
            B.COVER_PERIOD_TYPE,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,
            (SELECT SUM(Z.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS STD_PREM_AF,
            (SELECT SUM(Z.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS AMOUNT,
            (SELECT SUM(Z.UNIT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS UNIT,
            (SELECT Z.FIELD1 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER Z 
            WHERE Z.POLICY_ID=A.POLICY_ID AND Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND Z.FIELD1 IS NOT NULL AND ROWNUM=1) AS FIELD1,
            (SELECT Z.PRODUCT_CODE_SYS 
             FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z,APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE Y 
             WHERE Z.BUSINESS_PRD_ID=Y.BUSINESS_PRD_ID AND Y.SERVICE_CODE='RR' AND Z.BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS PRODUCT_CODE_SYS, 
            A.PAIDUP_DATE,
            A.VALIDATE_DATE,
            A.LAPSE_DATE,
             A.EXPIRY_DATE,
             (SELECT TP.CHARGE_PERIOD
              FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TP
             WHERE TP.BUSI_ITEM_ID = A.BUSI_ITEM_ID
               AND TP.IS_MASTER_ITEM = 1) CHARGE_PERIOD, 
         (SELECT TA.CHANGE_ID
          FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE      TA,
               APP___PAS__DBUSER.T_CS_POLICY_CHANGE      TB,
               APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD TC
         WHERE TA.CHANGE_ID = TB.CHANGE_ID
           AND TA.ACCEPT_ID = TB.ACCEPT_ID
           AND TB.CHANGE_ID = TC.CHANGE_ID
           AND TB.POLICY_CHG_ID = TC.POLICY_CHG_ID
           AND TB.POLICY_ID = A.POLICY_ID
           AND TC.BUSI_ITEM_ID = A.BUSI_ITEM_ID
           AND TC.OLD_NEW = '1'
           AND TC.OPERATION_TYPE = '2'
           AND TA.ACCEPT_STATUS = '18'
           AND TA.SERVICE_CODE = 'EN' AND ROWNUM = 1) CHANGE_ID,
           (SELECT T.Plan_Id  FROM APP___PAS__DBUSER.T_PAY_PLAN T 
         WHERE T.PAY_PLAN_TYPE IN ('2', '3', '4', '8', '10', '11') 
         AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID and ROWNUM = 1) plan_id
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE 1=1
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
        AND A.POLICY_CODE = #{policy_code} ]]>
        <choose>
            <when test="master_busi_item_id != null ">
                <![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id}  ]]>
            </when>
            <otherwise>
            	<![CDATA[ AND A.MASTER_BUSI_ITEM_ID IS NULL ]]>
            </otherwise>
         </choose>

	</select>
	
	<select id="JRQD_findAllContractBusiProdNewShort" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM ( SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			(SELECT P.PRODUCT_NAME_STD FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT P WHERE P.BUSINESS_PRD_ID = A.BUSI_PRD_ID) BUSI_ITEM_NAME,
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG,A.NEXT_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
			 
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
		<include refid="JRQD_PA_queryRiskAmountByBusiCode" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC,A.VALIDATE_DATE DESC )WHERE ROWNUM = 1]]>
		<!-- <![CDATA[ORDER BY A.master_busi_item_id DESC]]> -->
	</select>
	
	<!--  查询保单是否收过续期保费（不包含续保费用）-->
	 <select id="JRQD_PA_findAllPolicyByPilicyCode" resultType="java.util.Map" parameterType="java.util.Map">
   			SELECT T.POLICY_CODE,T.BUSI_PROD_CODE,A.POLICY_PERIOD
         FROM DEV_PAS.T_CONTRACT_BUSI_PROD T, DEV_PAS.T_CONTRACT_EXTEND A
        WHERE A.BUSI_ITEM_ID = T.BUSI_ITEM_ID
			AND T.POLICY_CODE = #{policy_code}
            AND EXISTS (SELECT B.PRODUCT_CODE_SYS
                 FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
                WHERE T.BUSI_PROD_CODE = B.PRODUCT_CODE_SYS
                  AND B.COVER_PERIOD_TYPE = '0')
					AND A.POLICY_PERIOD > '1'
	 </select>
	 
	 <!--  查询保单险种是否满足贷款条件 -->
	 <select id="JRQD_PA_findAllPolicyByPilicyCodeDK" resultType="java.util.Map" parameterType="java.util.Map">
   			select p.product_code_sys,p.product_name_sys
			from app___pds__dbuser.t_business_product_service c
			inner join dev_pas.t_business_product p
			on c.BUSINESS_PRD_ID = p.business_prd_id
			inner join dev_pas.t_service s
			on s.service_code = c.service_code
			and s.service_code = 'LN' AND P.PRODUCT_CODE_SYS = #{busi_prod_code}
	 </select>
	 
	 <!--  查询保单险种是否为i系列-->
	 <select id="JRQD_PA_queryPolicyInfoConstants" resultType="java.util.Map" parameterType="java.util.Map">
   			SELECT T.CONSTANTS_KEY,T.CONSTANTS_VALUE FROM DEV_PAS.T_CONSTANTS_INFO T WHERE 
   			T.CONSTANTS_KEY = 'SHORT_PRODUCT_I' AND T.CONSTANTS_VALUE = #{busi_prod_code}
	 </select>
	 
	 <!--  查询保单险种是否满足续贷条件 -->
	 <select id="JRQD_PA_findAllPolicyByPilicyCodeXD" resultType="java.util.Map" parameterType="java.util.Map">
   			select p.product_code_sys,p.product_name_sys
			from app___pds__dbuser.t_business_product_service c
			inner join dev_pas.t_business_product p
			on c.BUSINESS_PRD_ID = p.business_prd_id
			inner join dev_pas.t_service s
			on s.service_code = c.service_code
			and s.service_code = 'RL' AND P.PRODUCT_CODE_SYS = #{busi_prod_code}
	 </select>
	 
	 <!--  保单贷款查询保单附加险 -->
	 <select id="JRQD_PA_findAllPolicyByFJDK" resultType="java.util.Map" parameterType="java.util.Map">
				SELECT CBP.BUSI_PROD_CODE, 
					TBP.PRODUCT_NAME_SYS, 
					TBP.PRODUCT_ABBR_NAME, 
					CBP.LIABILITY_STATE, 
					TBP.COVER_PERIOD_TYPE, 
					(SELECT B.PRODUCT_CODE_SYS 
						FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B 
						WHERE CBP.BUSI_PROD_CODE = B.PRODUCT_CODE_SYS 
						AND B.COVER_PERIOD_TYPE = '0')PRODUCTCODESYS, 
					CBP.VALIDATE_DATE 
				FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP, 
					APP___PAS__DBUSER.T_BUSINESS_PRODUCT    TBP 
				WHERE CBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID 
					AND CBP.MASTER_BUSI_ITEM_ID IS NOT NULL 
					AND CBP.POLICY_CODE = #{policy_code}
	 </select>
<!--  保单贷款查询保单主险下的附加险信息 -->
	 <select id="JRQD_PA_findAllPolicyAdditionByFJDK" resultType="java.util.Map" parameterType="java.util.Map">
				SELECT CBP.BUSI_PROD_CODE, 
					TBP.PRODUCT_NAME_SYS, 
					TBP.PRODUCT_ABBR_NAME, 
					CBP.LIABILITY_STATE, 
					TBP.COVER_PERIOD_TYPE, 
					(SELECT B.PRODUCT_CODE_SYS 
						FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B 
						WHERE CBP.BUSI_PROD_CODE = B.PRODUCT_CODE_SYS 
						AND B.COVER_PERIOD_TYPE = '0')PRODUCTCODESYS, 
					CBP.VALIDATE_DATE 
				FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP, 
					APP___PAS__DBUSER.T_BUSINESS_PRODUCT    TBP 
				WHERE CBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID 
					AND CBP.MASTER_BUSI_ITEM_ID = #{busi_item_id}
					AND CBP.POLICY_CODE = #{policy_code}
	 </select>
	<!-- 根据保单号查询主险 -->
	<select id="JRQD_PA_findMasterContractBusiProdType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,
       A.HESITATION_PERIOD_DAY,
       A.GURNT_START_DATE,
       A.BUSI_PRD_ID,
       A.GURNT_PERIOD,
       A.BUSI_PROD_CODE,
       A.HESITATION2ACC,
       A.APPLY_CODE,
       A.IS_WAIVED,
       A.SETTLE_METHOD,
       A.POLICY_ID,
       A.FLIGHT_NO,
       A.WAIVER,
       A.GURNT_RATE,
       A.MASTER_BUSI_ITEM_ID,
       A.PAIDUP_DATE,
       A.EXPIRY_DATE,
       A.LIABILITY_STATE,
       A.POLICY_CODE,
       A.RERINSTATE_DATE,
       A.RENEW_DECISION,
       A.VALIDATE_DATE,
       A.RENEWAL_STATE,
       A.ASSURERENEW_FLAG,
       A.APL_PERMIT,
       A.APPLY_DATE,
       A.INITIAL_VALIDATE_DATE,
       A.RENEW,
       A.RENEW_TIMES,
       A.WAIVER_END,
       A.MATURITY_DATE,
       A.GURNT_PERD_TYPE,
       A.BUSI_ITEM_ID,
       A.LAPSE_DATE,
       A.JOINT_LIFE_FLAG,
       A.END_CAUSE,
       A.ISSUE_DATE,
       A.LAPSE_CAUSE,
       A.DECISION_CODE,
       A.SUSPEND_DATE,
       A.SUSPEND_CAUSE,
       A.INITIAL_PREM_DATE,
       A.DUE_LAPSE_DATE,
       A.WAIVER_START,
       A.PRD_PKG_CODE,
       A.GURNT_RENEW_END,
       A.GURNT_RENEW_START,
       A.OLD_POL_NO,
       A.REINSURED,
       (SELECT TCI.CONSTANTS_KEY FROM APP___PAS__DBUSER.T_CONSTANTS_INFO TCI WHERE TCI.CONSTANTS_KEY IN
                        ('SHORT_PRODUCT_ZD',
                         'SHORT_PRODUCT_I',
                         'SHORT_PRODUCT_PT') AND TCI.CONSTANTS_VALUE = A.BUSI_PROD_CODE) AS CONSTANTS_KEY
  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
 WHERE rownum = 1
   AND A.MASTER_BUSI_ITEM_ID is null
]]>
		<if test="policy_code != null and policy_code != ''">
			<![CDATA[AND A.policy_code = #{policy_code} ]]>
		</if> 
	</select>
	 
	 <select id="JRQD_PA_findAddBusiProdByPolicyIdType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT (SELECT P.PRODUCT_NAME_STD
          FROM DEV_PAS.T_BUSINESS_PRODUCT P
         WHERE P.BUSINESS_PRD_ID = A.BUSI_PRD_ID) BUSI_ITEM_NAME,
       A.BUSI_ITEM_ID,
       A.BUSI_PRD_ID,
       A.BUSI_PROD_CODE,
       A.APPLY_CODE,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.MASTER_BUSI_ITEM_ID,
       A.OLD_POL_NO,
       A.VALIDATE_DATE,
       A.INITIAL_VALIDATE_DATE,
       A.INITIAL_PREM_DATE,
       A.LIABILITY_STATE,
       A.LAPSE_CAUSE,
       A.LAPSE_DATE,
       A.DUE_LAPSE_DATE,
       A.RERINSTATE_DATE,
       A.SUSPEND_CAUSE,
       A.SUSPEND_DATE,
       A.END_CAUSE,
       A.EXPIRY_DATE,
       A.DECISION_CODE,
       A.ISSUE_DATE,
       A.MATURITY_DATE,
       A.JOINT_LIFE_FLAG,
       A.RENEW,
       A.ASSURERENEW_FLAG,
       A.GURNT_RENEW_START,
       A.GURNT_RENEW_END,
       A.HESITATION2ACC,
       A.APPLY_DATE,
       A.WAIVER,
       A.IS_WAIVED,
       A.WAIVER_START,
       A.WAIVER_END,
       A.RENEW_DECISION,
       A.RENEW_TIMES,
       A.RENEWAL_STATE,
       A.APL_PERMIT,
       A.PRD_PKG_CODE,
       A.PAIDUP_DATE,
       A.GURNT_START_DATE,
       A.GURNT_PERD_TYPE,
       A.GURNT_PERIOD,
       A.GURNT_RATE,
       A.SETTLE_METHOD,
       A.SURRENDER_FLAG,
       A.FLIGHT_NO,
       A.HESITATION_PERIOD_DAY,
       A.IS_RENEWAL_SWITCH,
       A.IS_RPU,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.NEXT_FLAG,
       A.REINSURED,
       A.CAN_CHANGE_FLAG,
       A.CAN_REINSURE_FLAG,
       tci.constants_key,
       A.CHARGE_FLAG
  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
       APP___PAS__DBUSER.t_constants_info     tci
 WHERE ROWNUM <= 1000
   and tci.constants_key in
       ('SHORT_PRODUCT_ZD', 'SHORT_PRODUCT_I', 'SHORT_PRODUCT_PT')
   and tci.constants_value = a.busi_prod_code
   and a.MASTER_BUSI_ITEM_ID is not null ]]>
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC,A.VALIDATE_DATE DESC]]>
	</select>
	
	
	<select id="JRQD_PA_queryPolicyCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT count(1)
			  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TA,
			       APP___PAS__DBUSER.T_INSURED_LIST       TB,
			       APP___PAS__DBUSER.T_BENEFIT_INSURED    TC
			 WHERE TA.POLICY_ID = TB.POLICY_ID
			   AND TA.BUSI_ITEM_ID = TC.BUSI_ITEM_ID
			   AND TB.LIST_ID = TC.INSURED_ID
				 AND TA.LIABILITY_STATE = '1'
			   AND TA.BUSI_PROD_CODE = #{busi_prod_code}
			   AND EXISTS (SELECT TB.CUSTOMER_ID
			          FROM APP___PAS__DBUSER.T_INSURED_LIST T
			         WHERE T.POLICY_CODE = #{policy_code}
			           AND T.CUSTOMER_ID = TB.CUSTOMER_ID)           
		 ]]>
	</select>
	
	<!-- 查询主险为理赔终止的信息 -->
	<select id="JRQD_findTerminationOfClaimsMainBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		
		<![CDATA[	
			SELECT  CBP.BUSI_ITEM_ID
		      FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
		           APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
		     WHERE CBP.MASTER_BUSI_ITEM_ID IS NULL
		       AND CBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
		       AND CBP.LIABILITY_STATE = '3'
		       AND CBP.END_CAUSE = '02'
		       AND CBP.POLICY_CODE = #{policy_code}
         ]]>	 
				
	</select>
	
	<!-- 查询主险为理赔终止的保单下是否含有有效的长期附加险的险种信息  93464-->
	<select id="JRQD_findEffectiveLongTermNoMainBusiProd" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			   SELECT  COUNT(1)
                 FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
                      APP___PAS__DBUSER.T_CONTRACT_EXTEND    B,
                      APP___PDS__DBUSER.T_BUSINESS_PRODUCT   C
                WHERE A.BUSI_PRD_ID = C.BUSINESS_PRD_ID
                  AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
                  AND C.COVER_PERIOD_TYPE = '0'
                  AND A.BUSI_ITEM_ID = #{busi_item_id}		
		 ]]>
	</select>
	
	<select id="JRQD_PA_queryRRPolicyCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT count(1)
			  FROM APP___PDS__DBUSER.T_BUSINESS_PROD_CS A,
			       APP___PDS__DBUSER.T_BUSINESS_PRODUCT B,
				   APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
			 where A.BUSINESS_PRD_ID = B.BUSINESS_PRD_ID
			   AND TCBP.BUSI_PRD_ID = B.BUSINESS_PRD_ID
				 AND A.CONVERSION_BUSI_PRD_ID <> '0'
				 AND TCBP.BUSI_PROD_CODE NOT IN ('00563000','00563100','00958000','00958100')
				 AND TCBP.POLICY_CODE = #{policy_code}
		 ]]>
	</select>
	
	<select id="JRQD_PA_findPolicyByXB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT MAX(TA.VALIDATE_DATE) AS VALIDATE_DATE
				  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TA,
				       APP___PAS__DBUSER.T_INSURED_LIST       TB,
				       APP___PAS__DBUSER.T_BENEFIT_INSURED    TC
				 WHERE TA.POLICY_ID = TB.POLICY_ID
				   AND TA.BUSI_ITEM_ID = TC.BUSI_ITEM_ID
				   AND TA.LIABILITY_STATE = '1'
				   AND TA.POLICY_CODE <> #{policy_code}
				   AND TB.LIST_ID = TC.INSURED_ID
				   AND TA.BUSI_PROD_CODE in (${busiProdCodes})
				   AND EXISTS (SELECT TB.CUSTOMER_ID
				          FROM APP___PAS__DBUSER.T_INSURED_LIST T
				         WHERE T.POLICY_CODE = #{policy_code}
				           AND T.CUSTOMER_ID = TB.CUSTOMER_ID)
		 ]]>
	</select>
	
	<select id="JRQD_PA_queryBusiDataForRR" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT TA.BUSI_ITEM_ID,
				   TA.BUSI_PRD_ID,
				   TA.BUSI_PROD_CODE,
			       TA.VALIDATE_DATE,
				   TA.RENEW,
			       TA.EXPIRY_DATE,
			       (SELECT A.PRODUCT_ABBR_NAME
			          FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT A
			         WHERE A.BUSINESS_PRD_ID = TA.BUSI_PRD_ID) AS PRODUCT_ABBR_NAME,
			       
			       (SELECT B.COUNT_WAY
			          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT B
			         WHERE TA.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			           AND B.IS_MASTER_ITEM = 1) COUNT_WAY,
			       
			       (SELECT B.AMOUNT
			          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT B
			         WHERE TA.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			           AND B.IS_MASTER_ITEM = 1) AMOUNT,
			       
			       (SELECT B.UNIT
			          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT B
			         WHERE TA.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			           AND B.IS_MASTER_ITEM = 1) UNIT,
			       
			       (SELECT B.STD_PREM_AF
			          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT B
			         WHERE TA.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			           AND B.IS_MASTER_ITEM = 1) STD_PREM_AF,
			       
			       (SELECT CASE
			                 WHEN C.SOCI_SECU = 1 THEN
			                  'Y'
			                 WHEN C.SOCI_SECU = 0 THEN
			                  'N'
			                 ELSE
			                  ''
			               END
			          FROM APP___PAS__DBUSER.T_INSURED_LIST C, APP___PAS__DBUSER.T_BENEFIT_INSURED TD
			         WHERE C.LIST_ID = TD.INSURED_ID
			           AND TD.BUSI_ITEM_ID = TA.BUSI_ITEM_ID
			           AND TD.ORDER_ID = 1) SOCI_SECU,
			           
			       (SELECT TT.FIELD1
			          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT       B,
			               APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER TT
			         WHERE B.BUSI_ITEM_ID = TA.BUSI_ITEM_ID
			           AND B.ITEM_ID = TT.ITEM_ID) RNEWPLAN,
			       
			       (SELECT T.NAME FROM APP___PAS__DBUSER.T_PAY_MODE T WHERE T.CODE = TB.PAY_NEXT) PAY_MODE,
			       TB.NEXT_ACCOUNT,
			       TB.NEXT_ACCOUNT_NAME,
			        (SELECT TE.BANK_NAME
			          FROM APP___PAS__DBUSER.T_BANK TE
			         WHERE TE.BANK_CODE = TB.NEXT_ACCOUNT_BANK) NEXT_ACCOUNT_BANK,
			       
			       (SELECT TR.ORGAN_NAME
	                FROM APP___PAS__DBUSER.T_CONTRACT_MASTER T,APP___PAS__DBUSER.T_UDMP_ORG_REL TR
	               WHERE T.POLICY_ID = TA.POLICY_ID
	                 AND T.ORGAN_CODE = TR.ORGAN_CODE) ORGAN_CODE,
	                 
	               (SELECT TO_CHAR(MIN(TCE.PAY_DUE_DATE), 'YYYY-MM-DD')
			          FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
			         WHERE TA.POLICY_ID = TCE.POLICY_ID
			           AND ROWNUM = 1) PAY_DUE_DATE
			
			  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TA, 
			  	   APP___PAS__DBUSER.T_PAYER_ACCOUNT TB
			 WHERE TA.POLICY_ID = TB.POLICY_ID
			   AND TA.LIABILITY_STATE = 1
			   AND TA.POLICY_CODE =#{policy_code}
			   AND TA.BUSI_PROD_CODE =#{busi_prod_code}
		 ]]>
	</select>
	
	<select id="JRQD_PA_findNoRenewalExpirationByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT      A.POLICY_CODE,
            A.LIABILITY_STATE,
            A.POLICY_ID,
            A.VALIDATE_DATE, 
            A.BUSI_ITEM_ID,
            A.BUSI_PROD_CODE,
            A.RENEW,
            A.REINSURED,
            B.PRODUCT_NAME_STD,
            B.PRODUCT_NAME_SYS,
            B.COVER_PERIOD_TYPE,
            A.MASTER_BUSI_ITEM_ID,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,
            (SELECT SUM(Z.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS STD_PREM_AF,
            (SELECT SUM(Z.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID ) AS AMOUNT,
            (SELECT SUM(Z.UNIT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID ) AS UNIT,
            (SELECT Z.PREM_FREQ FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID ) AS PREM_FREQ,
            (SELECT Z.CHARGE_PERIOD FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID ) AS CHARGE_PERIOD,
            A.VALIDATE_DATE,
            A.EXPIRY_DATE,
            (SELECT TCP.MATURITY_DATE
			          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT TCP
			         WHERE TCP.BUSI_ITEM_ID = A.BUSI_ITEM_ID) MATURITY_DATE,
            (SELECT TA.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD TA WHERE TA.BUSI_ITEM_ID = A.MASTER_BUSI_ITEM_ID)MASTSI_PROD_CODE,
			(SELECT T.RENEW_GRACE_PERIOD FROM APP___PDS__DBUSER.T_GRACE_PERIOD T WHERE T.BUSINESS_PRD_ID = B.BUSINESS_PRD_ID)RENEW_GRACE_PERIOD,
			(SELECT TCI.CONSTANTS_VALUE FROM APP___PAS__DBUSER.T_CONSTANTS_INFO TCI WHERE TCI.CONSTANTS_KEY 
			 IN ('SHORT_PRODUCT_ZD', 'SHORT_PRODUCT_I', 'SHORT_PRODUCT_PT') AND TCI.CONSTANTS_VALUE = A.BUSI_PROD_CODE) SHORT_PRODUCT
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE 1=1
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
        AND A.POLICY_CODE= #{policy_code}
		]]>
	</select>
	
	<select id="JRQD_PA_findRenewalExpiration" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select s.service_name, p.product_code_sys, p.product_name_sys, c.*
			  from APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE C,
			       APP___PDS__DBUSER.t_service                  s,
			       APP___PDS__DBUSER.t_business_product         p
			 where c.business_prd_id = p.business_prd_id
			   and c.service_code = s.service_code
			   and c.service_code = 'EN'
			   and P.Product_Code_Sys = #{busi_prod_code}
		]]>
	</select>
	
	<select id="JRQD_PA_findPolicyByEN" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT TCAC.ACCEPT_ID,TCAC.SERVICE_CODE,TCBP.BUSI_ITEM_ID,TCBP.POLICY_CODE
						FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD TCBP,
								 DEV_PAS.T_CS_POLICY_CHANGE   TCPC,
								 DEV_PAS.T_CS_ACCEPT_CHANGE   TCAC
					 where TCBP.POLICY_CHG_ID = TCPC.POLICY_CHG_ID
					   AND TCBP.POLICY_ID = TCPC.POLICY_ID
						 AND TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
						 AND TCPC.CHANGE_ID = TCAC.CHANGE_ID
						 AND TCAC.ACCEPT_STATUS = '18'
						 AND TCBP.OPERATION_TYPE = '2'
						 AND TCAC.SERVICE_CODE = 'EN'
						 AND TCBP.BUSI_ITEM_ID = #{busi_item_id}
						 AND TCBP.POLICY_CODE = #{policy_code}
		]]>
	</select>
	
	<!-- 随信通续期缴费信息查询接口查询主险+附加险信息 -->
	<select id="JRQD_PA_findContractBusiProdInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT A.BUSI_PROD_CODE, A.BUSI_PRD_ID,
					       A.LIABILITY_STATE, 
					       A.MASTER_BUSI_ITEM_ID, 
					       A.IS_WAIVED, 
					       A.VALIDATE_DATE,
					       B.PRODUCT_NAME_SYS, 
     					   B.PRODUCT_ABBR_NAME, 
					       B.COVER_PERIOD_TYPE 
					  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, 
					       APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B 
					 WHERE 1=1 
					   AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
					   AND A.MASTER_BUSI_ITEM_ID IS NOT NULL 
					   AND A.POLICY_CODE=  #{policy_code} 
		]]>
	</select>
	
	<!-- 随信通续期缴费信息-根据policycode查询非终止主险 -->
	<select id="JRQD_PA_findUnEndMainBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		  SELECT A.POLICY_CODE, A.BUSI_PRD_ID,
                 A.BUSI_PROD_CODE, 
                 A.LIABILITY_STATE, 
                 A.MASTER_BUSI_ITEM_ID, 
                 A.IS_WAIVED, 
                 A.VALIDATE_DATE,
                 B.PRODUCT_NAME_SYS, 
                 B.PRODUCT_ABBR_NAME, 
                 B.COVER_PERIOD_TYPE 
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, 
                 APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B 
           WHERE 1=1 
             AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
             AND A.LIABILITY_STATE != 3
             AND A.MASTER_BUSI_ITEM_ID IS NULL 
			 AND A.POLICY_CODE=  #{policy_code} 
		]]>
	</select>
	
	<!-- 随信通续期缴费信息-查询长期有效附加险 -->
	<select id="JRQD_PA_findLongEffectByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		  SELECT A.POLICY_CODE, A.BUSI_PRD_ID,
                 A.BUSI_PROD_CODE, 
                 A.LIABILITY_STATE, 
                 A.MASTER_BUSI_ITEM_ID, 
                 A.IS_WAIVED, 
                 A.VALIDATE_DATE,
                 B.PRODUCT_NAME_SYS, 
                 B.PRODUCT_ABBR_NAME, 
                 B.COVER_PERIOD_TYPE 
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, 
                 APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B 
           WHERE 1=1 
             AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
             AND A.LIABILITY_STATE != 3
             AND B.COVER_PERIOD_TYPE = 0
			 AND A.POLICY_CODE=  #{policy_code} 
		]]>
	</select>
	
	<!-- 累积生息账户领取-查询所有险种信息 -->
	<select id="JRQD_findAllBusiProdForQueryAI" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		  SELECT A.POLICY_CODE, A.BUSI_PRD_ID,
                 A.BUSI_PROD_CODE, 
                 A.LIABILITY_STATE, 
                 A.MASTER_BUSI_ITEM_ID, 
                 A.IS_WAIVED, 
                 A.VALIDATE_DATE,
                 B.PRODUCT_NAME_SYS, 
                 B.PRODUCT_ABBR_NAME, 
                 B.COVER_PERIOD_TYPE 
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, 
                 APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B 
           WHERE 1=1 
             AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
			 AND A.POLICY_CODE=  #{policy_code} 
		]]>
	</select>
	
		<!-- 查询短期健康险信息 -->
	<select id="JRQD_PA_findAddBusiProdByBusiItemIdType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT (SELECT P.PRODUCT_NAME_STD
          FROM DEV_PAS.T_BUSINESS_PRODUCT P
         WHERE P.BUSINESS_PRD_ID = A.BUSI_PRD_ID) BUSI_ITEM_NAME,
       A.BUSI_ITEM_ID,
       A.BUSI_PRD_ID,
       A.BUSI_PROD_CODE,
       A.APPLY_CODE,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.MASTER_BUSI_ITEM_ID,
       A.OLD_POL_NO,
       A.VALIDATE_DATE,
       A.INITIAL_VALIDATE_DATE,
       A.INITIAL_PREM_DATE,
       A.LIABILITY_STATE,
       A.LAPSE_CAUSE,
       A.LAPSE_DATE,
       A.DUE_LAPSE_DATE,
       A.RERINSTATE_DATE,
       A.SUSPEND_CAUSE,
       A.SUSPEND_DATE,
       A.END_CAUSE,
       A.EXPIRY_DATE,
       A.DECISION_CODE,
       A.ISSUE_DATE,
       A.MATURITY_DATE,
       A.JOINT_LIFE_FLAG,
       A.RENEW,
       A.ASSURERENEW_FLAG,
       A.GURNT_RENEW_START,
       A.GURNT_RENEW_END,
       A.HESITATION2ACC,
       A.APPLY_DATE,
       A.WAIVER,
       A.IS_WAIVED,
       A.WAIVER_START,
       A.WAIVER_END,
       A.RENEW_DECISION,
       A.RENEW_TIMES,
       A.RENEWAL_STATE,
       A.APL_PERMIT,
       A.PRD_PKG_CODE,
       A.PAIDUP_DATE,
       A.GURNT_START_DATE,
       A.GURNT_PERD_TYPE,
       A.GURNT_PERIOD,
       A.GURNT_RATE,
       A.SETTLE_METHOD,
       A.SURRENDER_FLAG,
       A.FLIGHT_NO,
       A.HESITATION_PERIOD_DAY,
       A.IS_RENEWAL_SWITCH,
       A.IS_RPU,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.NEXT_FLAG,
       A.REINSURED,
       A.CAN_CHANGE_FLAG,
       A.CAN_REINSURE_FLAG,
       tci.constants_key,
       A.CHARGE_FLAG
  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
       APP___PAS__DBUSER.t_constants_info     tci
 WHERE ROWNUM <= 1000
   and tci.constants_key in
       ('SHORT_PRODUCT_ZD', 'SHORT_PRODUCT_I', 'SHORT_PRODUCT_PT')
   and tci.constants_value = a.busi_prod_code]]>
		<include refid="JRQD_PA_contractBusiProdWhereCondition" />
		<![CDATA[ORDER BY A.VALIDATE_DATE DESC]]>
	</select>
	
	<!-- 查询短期健康险信息 -->
	<select id="JRQD_PA_findPolicySurrenderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT      
			A.POLICY_CODE, 
            A.POLICY_ID,
            A.LAPSE_DATE,
            A.MATURITY_DATE,
			A.BUSI_ITEM_ID,
			A.WAIVER_START,
            A.LIABILITY_STATE,
            A.VALIDATE_DATE, 
            A.HESITATION_PERIOD_DAY,
            A.BUSI_PROD_CODE,
			B.PRODUCT_ABBR_NAME,
            B.PRODUCT_NAME_SYS,
            B.COVER_PERIOD_TYPE,
            A.MASTER_BUSI_ITEM_ID,
            (SELECT Z.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS PAY_DUE_DATE,
            (SELECT Z.EXTRACTION_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND A.POLICY_CODE = Z.POLICY_CODE AND ROWNUM=1) AS EXTRACTION_DUE_DATE,
            (SELECT SUM(Z.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS STD_PREM_AF,
            (SELECT SUM(Z.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS AMOUNT,
            (SELECT SUM(Z.TOTAL_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID) AS TOTAL_PREM_AF,
			(SELECT T.CHARGE_TYPE FROM DEV_PAS.T_CHARGE_MODE T WHERE T.CHARGE_TYPE = (SELECT Z.PREM_FREQ FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
            WHERE Z.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND ROWNUM = 1))CHARGE_NAME,
            (SELECT Z.PREM_FREQ
                FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z
               WHERE Z.BUSI_ITEM_ID = A.BUSI_ITEM_ID AND ROWNUM = 1) AS PREM_FREQ
       FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
            APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
      WHERE 1=1
        AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
		AND A.POLICY_CODE=#{policy_code} ]]>
   
	</select>
	
	<!-- 查询保单下险种信息-万能险账户领取 -->
	<select id="JRQD_PA_findPGPolicyForYdbqByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT A.POLICY_CODE,
				       A.POLICY_ID,
				       A.LIABILITY_STATE,
				       A.VALIDATE_DATE,
				       A.BUSI_PROD_CODE,
				       B.PRODUCT_ABBR_NAME,
				       B.PRODUCT_NAME_SYS,
				       B.COVER_PERIOD_TYPE,
				       A.MASTER_BUSI_ITEM_ID
				  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
				       APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B,
				       APP___PAS__DBUSER.T_CONTRACT_PRODUCT   C
				 WHERE 1 = 1
				   AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
				   AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
				   AND A.POLICY_CODE = #{policy_code} ]]>
	</select>
	
	<!-- 万能投连账户信息查询接口查询险种信息 -->
	<select id="JRQD_PA_findPolicyForGwByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			            SELECT A.POLICY_CODE,
				               A.POLICY_ID,
				               A.LIABILITY_STATE,
				               A.LAPSE_DATE,
				               A.VALIDATE_DATE,
				               A.BUSI_PROD_CODE,
				               A.RENEW,
				               A.MASTER_BUSI_ITEM_ID,
				               A.EXPIRY_DATE,
				               B.PRODUCT_ABBR_NAME,
				               B.PRODUCT_NAME_SYS,
				               B.COVER_PERIOD_TYPE,
				               C.STD_PREM_AF,
				               C.AMOUNT,
				               D.NEXT_PREM,
				               D.PAY_DUE_DATE
				          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
				               APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B,
				               APP___PAS__DBUSER.T_CONTRACT_PRODUCT   C,
				               APP___PAS__DBUSER.T_CONTRACT_EXTEND    D
				         WHERE 1 = 1
				           AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
				           AND A.POLICY_CODE = C.POLICY_CODE
				           AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
				           AND A.POLICY_CODE = D.POLICY_CODE
				           AND A.BUSI_ITEM_ID = D.BUSI_ITEM_ID
						   AND A.POLICY_CODE = #{policy_code} ]]>
	</select>
	
		<select id="JRQD_PA_findAllMedicaPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select ccbp.REINSURED,ccbp.CAN_CHANGE_FLAG,ccbp.CAN_REINSURE_FLAG,ccbp.CHARGE_FLAG,ccbp.IS_RPU,ccbp.ASSURERENEW_FLAG,ccbp.OLD_POL_NO,ccbp.HESITATION_PERIOD_DAY,ccbp.GURNT_START_DATE, ccbp.BUSI_PRD_ID, ccbp.GURNT_PERIOD, ccbp.BUSI_PROD_CODE, ccbp.IS_WAIVED, ccbp.HESITATION2ACC, 
      ccbp.APPLY_CODE,   ccbp.POLICY_ID, ccbp.WAIVER,
      ccbp.MASTER_BUSI_ITEM_ID, ccbp.PAIDUP_DATE, ccbp.EXPIRY_DATE, ccbp.LIABILITY_STATE, ccbp.POLICY_CODE, ccbp.RERINSTATE_DATE, 
      ccbp.RENEW_DECISION, ccbp.VALIDATE_DATE, ccbp.RENEWAL_STATE, ccbp.APL_PERMIT, ccbp.APPLY_DATE, 
      ccbp.RENEW, ccbp.RENEW_TIMES, ccbp.WAIVER_END, ccbp.MATURITY_DATE, ccbp.GURNT_PERD_TYPE,  
      ccbp.BUSI_ITEM_ID, ccbp.LAPSE_DATE, ccbp.JOINT_LIFE_FLAG, ccbp.END_CAUSE, ccbp.ISSUE_DATE, ccbp.LAPSE_CAUSE, 
      ccbp.DECISION_CODE,  ccbp.SUSPEND_DATE, ccbp.INITIAL_PREM_DATE, ccbp.SUSPEND_CAUSE, ccbp.DUE_LAPSE_DATE, ccbp.WAIVER_START, 
      ccbp.PRD_PKG_CODE, ccbp.GURNT_RENEW_END, ccbp.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,ccbp.SURRENDER_FLAG
  from dev_pas.t_contract_busi_prod ccbp, dev_pas.t_business_product bp
 where ccbp.busi_prod_code = bp.PRODUCT_CODE_SYS
   
   and bp.medica_flag = '1'
  ]]>
    <if test=" policy_id  != null "><![CDATA[  and ccbp.policy_id = #{policy_id}  ]]></if>
   <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND ccbp.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	
	<!-- 根据险种id查询可以转换的险种 -->
	<select id="JRQD_PA_findALLBusiProdByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT B.PRODUCT_CODE_SYS,
			       B.PRODUCT_NAME_SYS,
			       B.BUSINESS_PRD_ID,
			       A.ALLOW_CONVERSION,
			       A.CONVERSION_BUSI_PRD_ID,
			       (SELECT TBP.PRODUCT_NAME_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT TBP WHERE  TBP.PRODUCT_CODE_SYS = A.CONVERSION_BUSI_PRD_ID ) ZPRODUCT_NAME_SYS,
			 	   (SELECT TBP.PRODUCT_ABBR_NAME FROM DEV_PDS.T_BUSINESS_PRODUCT TBP WHERE  TBP.PRODUCT_CODE_SYS = A.CONVERSION_BUSI_PRD_ID )ZPRODUCT_ABBR_NAME,
			       A.RENEWAL_MAX_AGE             as renwal_max_age,
			       A.IS_SOCIAL_SECURITY_PRODUCTS as is_social_security_products
			  FROM APP___PDS__DBUSER.T_BUSINESS_PROD_CS A,
			       APP___PDS__DBUSER.T_BUSINESS_PRODUCT B,
						 DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
			 where A.BUSINESS_PRD_ID = B.BUSINESS_PRD_ID
			       AND B.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
			       AND TCBP.BUSI_PRD_ID = #{busi_prd_id}
			       AND TCBP.POLICY_CODE = #{policy_code}
   		]]>
	</select>
	
	<!-- 查询客户名下所有保单接口-查询所有主险信息 -->
	<select id="JRQD_findAllMainBusiProdForQueryAllPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	
			  SELECT CBP.POLICY_CODE,
			         TBP.PRODUCT_NAME_SYS,
			         TBP.PRODUCT_CATEGORY,
			         TBP.PRODUCT_NAME_STD,
			         TBP.PRODUCT_ABBR_NAME,
			         CBP.VALIDATE_DATE,
			         TBP.PRODUCT_CODE_SYS,
			         TBP.PRODUCT_CODE_STD,
			         CBP.BUSI_PROD_CODE,
			         CBP.LIABILITY_STATE,
               		 CBP.END_CAUSE,
			         CBP.BUSI_ITEM_ID
			    FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
			         APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
			   WHERE CBP.MASTER_BUSI_ITEM_ID IS NULL
			     AND CBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
		         AND CBP.POLICY_CODE = #{policy_code}
			   ORDER BY CBP.BUSI_ITEM_ID 
         ]]>	 
	</select>
	
	<!-- 随信通续期缴费信息-根据policyCode查询所有主险信息 -->
	<select id="JRQD_findAllMainBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	
			  SELECT CBP.POLICY_CODE,
			         CBP.BUSI_PRD_ID,
			         CBP.BUSI_ITEM_ID,
			         CBP.BUSI_PROD_CODE,
			         CBP.LIABILITY_STATE,
			         CBP.IS_WAIVED,
			         CBP.VALIDATE_DATE,
			         TBP.PRODUCT_NAME_SYS,
			         TBP.PRODUCT_ABBR_NAME,
			         TBP.COVER_PERIOD_TYPE,
			         TBP.PRODUCT_CATEGORY,
			         TBP.PRODUCT_NAME_STD,
			         TBP.PRODUCT_CODE_SYS,
			         TBP.PRODUCT_CODE_STD,
               		 CBP.END_CAUSE
			        
			    FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
			         APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
			   WHERE CBP.MASTER_BUSI_ITEM_ID IS NULL
			     AND CBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
		         AND CBP.POLICY_CODE = #{policy_code}
			   ORDER BY CBP.BUSI_ITEM_ID 
         ]]>	 
	</select>
	
	<!-- 随信通续期缴费信息-根据主险查询长期有效附加险 -->
	<select id="JRQD_PA_findLongEffectByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		  SELECT A.POLICY_CODE, A.BUSI_PRD_ID,
                 A.BUSI_PROD_CODE, 
                 A.LIABILITY_STATE, 
                 A.MASTER_BUSI_ITEM_ID, 
                 A.IS_WAIVED, 
                 A.VALIDATE_DATE,
                 B.PRODUCT_NAME_SYS, 
                 B.PRODUCT_ABBR_NAME, 
                 B.COVER_PERIOD_TYPE 
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, 
                 APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B 
           WHERE 1=1 
             AND A.BUSI_PRD_ID = B.BUSINESS_PRD_ID 
             AND A.LIABILITY_STATE != 3
             AND A.MASTER_BUSI_ITEM_ID IS NOT NULL 
             AND B.COVER_PERIOD_TYPE = 0
			 AND A.MASTER_BUSI_ITEM_ID = #{busi_item_id}
		]]>
	</select>
	
	<!-- 查询保单下险种信息 -->
	<select id="JRQD_PA_queryShortInsurance" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.POLICY_CODE,
			       A.BUSI_ITEM_ID,
			       A.LIABILITY_STATE,
			       A.LAPSE_CAUSE,
			       A.LAPSE_DATE,
			       A.END_CAUSE,
			       A.EXPIRY_DATE,
			       A.RENEW,
			       B.COVER_PERIOD_TYPE
			  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A, 
			       APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
			 WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID        
			   ]]>
			   <include refid="JRQD_PA_contractBusiProdWhereCondition" />
	</select>
	
	<select id="PA_findAllBusiProdByPolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[  SELECT A.BUSI_ITEM_ID,
               A.VALIDATE_DATE,
               (SELECT FIELD1
			     FROM DEV_PAS.T_CONTRACT_PRODUCT_OTHER T
			    WHERE T.POLICY_CODE = A.POLICY_CODE
			      AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID) AS field1,
			   
               A.BUSI_PRD_ID,]]>
              <if test=" is_pas  != null "><![CDATA[ 
              A.BUSI_PROD_CODE AS BUSI_PROD_CODE,
              (SELECT D.SOCI_SECU
		        FROM DEV_PAS.T_BENEFIT_INSURED C, DEV_PAS.T_CUSTOMER D
		       WHERE C.INSURED_ID = D.CUSTOMER_ID
		         AND C.POLICY_CODE = A.POLICY_CODE
		         AND C.BUSI_ITEM_ID = A.BUSI_ITEM_ID
		         AND C.ORDER_ID = 1) AS soci_secu,]]></if> 
			  <if test=" is_nb   != null "><![CDATA[ 
			  A.PRODUCT_CODE AS BUSI_PROD_CODE,
			  (SELECT D.SOCI_SECU
		        FROM DEV_NB.T_NB_BENEFIT_INSURED  C, DEV_PAS.T_CUSTOMER D
		       WHERE C.INSURED_ID = D.CUSTOMER_ID
		         AND C.POLICY_CODE = A.POLICY_CODE
		         AND C.BUSI_ITEM_ID = A.BUSI_ITEM_ID
		         AND C.ORDER_ID = 1) AS soci_secu,]]></if> 
              <![CDATA[ A.EXPIRY_DATE,
               A.MASTER_BUSI_ITEM_ID FROM ]]>
			<if test=" is_pas  != null "><![CDATA[ DEV_PAS.T_CONTRACT_BUSI_PROD A]]></if> 
			<if test=" is_nb  != null "><![CDATA[ DEV_NB.T_NB_CONTRACT_BUSI_PROD A]]></if> 
			<![CDATA[WHERE 1 = 1  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID= #{policy_id} ]]></if>
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID ]]>
	</select>
</mapper>
