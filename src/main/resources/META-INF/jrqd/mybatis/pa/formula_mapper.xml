<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.common.dao.IFormulaDao">
	<sql id="JRQD_PA_premArapWhereCondition">
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" product_code  != null "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" start_date  != null "><![CDATA[ AND to_char(A.finish_time,'yyyy-MM-dd') >= to_char(#{start_date},'yyyy-MM-dd') ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND to_char(A.finish_time,'yyyy-MM-dd') <= to_char(#{end_date},'yyyy-MM-dd') ]]></if>
	</sql>
	
	<!-- 计算资金贡献 -->
	<select id="JRQD_calCapitalContribution" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select sum(e.mid_amount * e.mid_prop) / 365 as capital_contribution
  from (select b.*,
               c.change_date as change_date_next,
               
               (case
                 when #{validate_date} = b.change_date then
                  c.change_date - b.change_date + 1
                 else
                  c.change_date - b.change_date
               end) as passday,
               b.amount * (case
                 when #{validate_date} = b.change_date  then
                  c.change_date - b.change_date + 1
                 else
                  c.change_date - b.change_date
               end) as mid_amount,
               
               NVL((select exp(sum(ln(d.proportion))) as ss
                   
                     from (select a.*, rownum as rn
                             from (select t.settle_date as change_date,
                                          t.balance     as amount,
                                          0             as type,
                                          1             as proportion
                                     from app___pas__dbuser.t_fund_settlement t
                                    where t.invest_id = #{invest_id}
                                      and t.settle_date < #{end_date, jdbcType=DATE}
                                   union
                                   select t.deal_time,
                                          t.trans_amount,
                                          t.trans_type,
                                          case
                                            when t.trans_type = 2 then
                                             1
                                            when t.trans_type = 1 then
                                             1 - (t.trans_amount /
                                             t.BALANCE_UNITS_BF)
                                          end
                                     from app___pas__dbuser.t_fund_trans t
                                    where t.list_id = #{invest_id}
                                      and t.trans_type in (1, 2)
                                      and t.deal_time < #{end_date, jdbcType=DATE}) a
                            order by change_date) d
                    where d.rn >= c.rn),
                   1) as mid_prop
          from (select a.*, rownum as rn
                  from (select t.settle_date as change_date,
                               t.balance     as amount,
                               0             as type,
                               1             as proportion
                          from app___pas__dbuser.t_fund_settlement t
                         where t.invest_id = #{invest_id}
                           and t.settle_date < #{end_date, jdbcType=DATE}
                        union
                        select t.deal_time,
                               t.trans_amount,
                               t.trans_type,
                               case
                                 when t.trans_type = 2 then
                                  1
                                 when t.trans_type = 1 then
                                  1 - (t.trans_amount / t.BALANCE_UNITS_BF)
                               end
                          from app___pas__dbuser.t_fund_trans t
                         where t.list_id = #{invest_id}
                           and t.trans_type in (1, 2)
                           and t.deal_time < #{end_date, jdbcType=DATE}
                        
                        ) a
                 order by change_date) b,
               (select a.*, rownum as rn
                  from (select t.settle_date as change_date,
                               t.balance     as amount,
                               0             as type,
                               1             as proportion
                          from app___pas__dbuser.t_fund_settlement t
                         where t.invest_id = #{invest_id}
                           and t.settle_date < #{end_date, jdbcType=DATE}
                        union
                        select t.deal_time,
                               t.trans_amount,
                               t.trans_type,
                               case
                                 when t.trans_type = 2 then
                                  1
                                 when t.trans_type = 1 then
                                  1 - (t.trans_amount / t.BALANCE_UNITS_BF)
                               end
                          from app___pas__dbuser.t_fund_trans t
                         where t.list_id = #{invest_id}
                           and t.trans_type in (1, 2)
                           and t.deal_time < #{end_date, jdbcType=DATE}
                        union
                        select #{end_date, jdbcType=DATE}, null, null, #{proportion}
                          from dual) a
                 order by change_date) c
         where (b.rn + 1) = c.rn) e


]]> 
	</select>
	<select id="JRQD_findCalCapConBaseInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
					SELECT to_char(gg.change_date, 'yyyy-MM-dd') AS change_date, gg.amount, gg.type
			  , gg.proportion
			  , CASE 
			    WHEN gg.type = 2
			    AND gg.rn = 1 THEN gg.amount
			    WHEN gg.type = 1 THEN (
			      SELECT ab.amount
			      FROM (
			        SELECT a.*, rownum AS rn
			        FROM (
			          SELECT t.settle_date AS change_date, t.balance AS amount, 0 AS type, 1 AS proportion
			          FROM app___pas__dbuser.t_fund_settlement t
			          WHERE t.invest_id = #{invest_id}
			            AND t.settle_date < #{end_date}
			          UNION
			          SELECT deal_time, SUM(trans_amount) AS trans_amount, trans_type, proportion
			          FROM (
			  SELECT t.deal_time,
			         t.trans_amount,
			         t.trans_type,
			         CASE
			           WHEN t.trans_type = 2 THEN
			            1
			           WHEN t.trans_type = 1 THEN
			            1 - t.trans_amount / t.BALANCE_UNITS_BF
			         END AS proportion,
			         t.BALANCE_UNITS_BF
			    from (SELECT t.deal_time,
			                 sum(t.trans_amount) as trans_amount,
			                 max(t.balance_units_bf) as BALANCE_UNITS_BF,
			                 t.trans_type
			            FROM app___pas__dbuser.t_fund_trans t
			           WHERE t.list_id = #{invest_id}
			             AND t.trans_type IN (1, 2)
			             AND t.deal_time < #{end_date}
			           group by t.deal_time, t.trans_type) t
			          )
			          GROUP BY deal_time, trans_type, proportion
			        ) a
			        ORDER BY change_date
			      ) ab
			      WHERE ab.rn + 1 = gg.rn
			    ) - gg.amount
			    WHEN gg.type = 2
			    AND gg.rn != 1 THEN gg.amount + (
			      SELECT ab.amount
			      FROM (
			        SELECT a.*, rownum AS rn
			        FROM (
			          SELECT t.settle_date AS change_date, t.balance AS amount, 0 AS type, 1 AS proportion
			          FROM app___pas__dbuser.t_fund_settlement t
			          WHERE t.invest_id = #{invest_id}
			            AND t.settle_date < #{end_date}
			          UNION
			          SELECT deal_time, SUM(trans_amount) AS trans_amount, trans_type, proportion
			          FROM (
			            SELECT t.deal_time,
			       t.trans_amount,
			       t.trans_type,
			       CASE
			         WHEN t.trans_type = 2 THEN
			          1
			         WHEN t.trans_type = 1 THEN
			          1 - t.trans_amount / t.BALANCE_UNITS_BF
			       END AS proportion
			        ,t.BALANCE_UNITS_BF
			  from (SELECT t.deal_time,
			               sum(t.trans_amount) as trans_amount,
			               max(t.balance_units_bf) as BALANCE_UNITS_BF,
			               t.trans_type
			          FROM app___pas__dbuser.t_fund_trans t
			         WHERE t.list_id = #{invest_id}
			           AND t.trans_type IN (1, 2)
			           AND t.deal_time < #{end_date}
			         group by t.deal_time, t.trans_type) t
			          )
			          GROUP BY deal_time, trans_type, proportion
			        ) a
			        ORDER BY change_date
			      ) ab
			      WHERE ab.rn + 1 = gg.rn
			    )
			    ELSE gg.amount
			  END AS final_amount
			FROM (
			  SELECT a.*, rownum AS rn
			  FROM (
			    SELECT t.settle_date AS change_date, t.balance AS amount, 0 AS type, 1 AS proportion
			    FROM app___pas__dbuser.t_fund_settlement t
			    WHERE t.invest_id = #{invest_id}
			      AND t.settle_date < #{end_date}
			    UNION
			    SELECT deal_time, SUM(trans_amount) AS trans_amount, trans_type, proportion
			    FROM (
			      SELECT t.deal_time,
			       t.trans_amount,
			       t.trans_type,
			       CASE
			         WHEN t.trans_type = 2 THEN
			          1
			         WHEN t.trans_type = 1 THEN
			          1 - t.trans_amount / t.BALANCE_UNITS_BF
			       END AS proportion
			        ,t.BALANCE_UNITS_BF
			  from (SELECT t.deal_time,
			               sum(t.trans_amount) as trans_amount,
			               max(t.balance_units_bf) as BALANCE_UNITS_BF,
			               t.trans_type
			          FROM app___pas__dbuser.t_fund_trans t
			         WHERE t.list_id = #{invest_id}
			           AND t.trans_type IN (1, 2)
			           AND t.deal_time < #{end_date}
			         group by t.deal_time, t.trans_type) t
			    )
			    GROUP BY deal_time, trans_type, proportion
			    UNION
			    SELECT #{end_date}, 0, 99
			      , 1
			    FROM dual
			  ) a
			  ORDER BY change_date, a.type DESC
         ) gg
		]]> 
	</select>
	<!-- 查询所有保单投资连结交易表操作 -->
	<select id="JRQD_findAllFundTransListByDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT 
		B.TRANS_INTEREST,
            B.MONEY_CODE,
            B.PRODUCT_ID,
          B.TRANS_UNITS,
            B.UNIT_NUMBER,
           B.FUND_CODE,
            B.ITEM_ID,
            B.TRANS_TYPE,
           B.TRANS_AMOUNT,
            B.SETTLEMENT_ID,
            B.TRANS_ID,
            B.FUND_SELL_PRICE,
            B.LIST_ID,
            B.APPLY_TRANS_CODE,
            B.POLICY_CHG_ID,
           B.BALANCE_UNITS_BF,
            B.BUSI_ITEM_ID,
            B.POLICY_ID,
            B.APPLY_ID,
            B.FUND_PURC_PRICE,
           B.TRANS_PROPORTION,
            B.DEAL_TIME,
           B.TRANS_CODE,
          B.TRANS_PRICE
		FROM (
		 SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM  APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1000
			and  A.TRANS_AMOUNT > 0]]>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>	
		<if test=" policy_id  != null "><![CDATA[ AND A.policy_id = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.busi_item_id = #{busi_item_id} ]]></if>
		<if test=" start_date  != null "><![CDATA[ AND A.deal_time >= #{start_date} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.deal_time <= #{end_date} ]]></if> 
		<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.trans_code IN ]]>
			<foreach collection ="trans_code_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
		</if>
		<![CDATA[ ORDER BY A.deal_time ) B  WHERE 1=1]]> 
		<if test=" trans_type  != null "><![CDATA[ AND  B.TRANS_TYPE = #{trans_type} ]]></if>
	</select>
<!-- 	结算万能保单结算时，日复利所用的利率：“round[(1+年结算利率)^（1/365 ）,8]^本结算期实际经过天数-1” -->
	<select id="JRQD_calInterestRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT POWER(ROUND(POWER(1+#{gurnt_interes_rate},1/#{day_of_year}),8) ,#{days_period})-1 as interest_rate FROM DUAL
		]]>
	</select>
	
	<sql id="JRQD_multiplycativeWhereCondition">
		<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.trans_code IN ]]>
			<foreach collection ="trans_code_list" 
			item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
		</if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>	
		<if test=" start_date  != null "><![CDATA[ AND A.deal_time >= #{start_date} ]]></if>
		<if test=" end_date  != null "><![CDATA[ AND A.deal_time <= #{end_date} ]]></if>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE like #{trans_code}||'%' ]]></if>
	</sql>
		<!-- 交易比例累乘积 -->
	<select id="JRQD_multiplycative" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select nvl( case
               when (select count(1) as trans_ids
                       from APP___PAS__DBUSER.t_fund_trans a
                      where (1 -
                            nvl(A.trans_proportion,
                                 A.trans_amount / A.balance_units_bf)) = 0 
         ]]>
         <include refid="JRQD_multiplycativeWhereCondition" />
         <![CDATA[ 
                        ) >= 1
                then
                0
               else
                (SELECT exp(sum(ln(1 -
                                   nvl(A.trans_proportion,
                                       A.trans_amount / A.balance_units_bf)))) cbbonus_param1
                   FROM APP___PAS__DBUSER.t_fund_trans A
                  WHERE 1 = 1 
        ]]>
        <include refid="JRQD_multiplycativeWhereCondition" />
        <![CDATA[ 
                    )
             end ,1)as all_multi_proportion
        from dual
		]]>
	</select>
	
	<!-- 根据投连账户ID查找最新的结算信息或某一时点之前最新的结算信息 -->
	<select id="JRQD_findLastSettlementByDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   SELECT ROWNUM, b.*
		   FROM (SELECT A.BATCH_DATE,
		                A.INVEST_ID,
		                A.LAST_GURNT_BALANCE,
		                A.ADJUST_FLAG,
		                A.LAST_BALANCE,
		                A.SETTLE_DATE,
		                A.GURNT_INTEREST,
		                A.ADJUST_TYPE,
		                A.INTEREST,
		                A.SETTLEMENT_ID,
		                A.ADJUST_AMOUNT,
		                A.BALANCE,
		                A.BALANCE_BA,
		                A.ACCOUNT_CODE,
		                A.LAST_SETTLE_DATE,
		                A.INTEREST_RATE,
		                A.ADJUST_DATE,
		                A.GURNT_BALANCE,
		                A.GURNT_INTEREST_RATE
		           FROM APP___PAS__DBUSER.t_fund_settlement A
		           where 1=1]]>
		          <if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
		          <if test=" end_deal_time  != null  and  end_deal_time  != ''  "><![CDATA[ AND to_date(to_char(A.SETTLE_DATE,'yyyy-MM-dd'),'yyyy-MM-dd') < to_date(to_char(#{end_deal_time},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if> 
		          <![CDATA[
		          ORDER BY a.settle_date DESC) B
		  WHERE 1 = 1
		    AND ROWNUM = 1
		]]>
		
	</select>
	
	<!-- 查询所有交易金额累计合计 -->
	<select id="JRQD_findSumTransAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT SUM(T1.trans_amount) AS sum_trans_amount
		FROM APP___PAS__DBUSER.t_fund_trans T1
		WHERE 1=1 ]]>
		<if test=" trans_type  != null "><![CDATA[ AND T1.TRANS_TYPE = #{trans_type} ]]></if>
		<if test=" trans_type_list  != null and trans_type_list.size()!=0">
			<![CDATA[ AND T1.trans_type IN ]]>
			<foreach collection ="trans_type_list" 
			item="item_list" index="index" open="(" close=")" separator=",">#{item_list}</foreach>
		</if>
		<if test=" start_Date  != null  and  start_Date  != ''  "><![CDATA[ AND to_date(to_char(T1.deal_time,'yyyy-mm-dd'),'yyyy-mm-dd') >= to_date(to_char(#{start_Date},'yyyy-mm-dd'),'yyyy-mm-dd') ]]></if>
		<if test=" end_Date  != null  and  end_Date  != ''  "><![CDATA[ AND to_date(to_char(T1.deal_time,'yyyy-mm-dd'),'yyyy-mm-dd') <= to_date(to_char(#{end_Date},'yyyy-mm-dd'),'yyyy-mm-dd') ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND T1.LIST_ID = #{list_id} ]]></if>
	</select>
	
	<!--leihong 所选险种缴费状态是否为‘正常缴费’（不包含豁免，正常缴清，减额缴清） -->
	<select id="JRQD_queryIsNormalPay" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[		
			 select count(1) capital_balance
   from (select c.item_id,
                c.policy_id,
                case
                  when c.waiver_start is null and c.waiver_end is null then
                   b.pay_due_date
                  when b.pay_due_date between c.waiver_start and c.waiver_end then
                   b.pay_due_date
                end pay_due_date
           from dev_pas.T_CONTRACT_PRODUCT c,
                (select a.policy_id, a.item_id, a.pay_due_date
                   from dev_pas.t_contract_extend a
                 where a.prem_status in ('2','3') and a.item_id=#{item_id}) b
          where c.policy_id = b.policy_id
            and c.item_id = b.item_id) t
					   
		]]>			
		<!-- <if test=" busi_item_id  != null "><![CDATA[ and a.busi_item_id = #{busi_item_id} ]]></if> -->
	</select>
	
</mapper>

