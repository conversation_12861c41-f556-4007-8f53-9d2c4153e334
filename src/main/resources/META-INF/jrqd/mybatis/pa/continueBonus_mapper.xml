<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.ContinueBonusDaoImpl">
	<sql id="JRQD_findAllCBBonusParam1WhereCondition">
			<if test=" list_id  != null "><![CDATA[ AND A.list_id = #{list_id} ]]></if>
			<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
			<if test=" item_id  != null "><![CDATA[ AND A.item_id = #{item_id} ]]></if>
			<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.trans_code IN ]]>
			<foreach collection ="trans_code_list" 
			item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
			</if>
			<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.DEAL_TIME > #{start_date} ]]></if>
			<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.DEAL_TIME <= #{end_date} ]]></if>
	</sql>
	<select id="JRQD_queryContinueBonusCounts" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) 
		FROM ( 
			SELECT DISTINCT TPB.policy_id 
			FROM APP___PAS__DBUSER.t_persistence_bonus TPB ,APP___PAS__DBUSER.t_contract_master TCM 
			WHERE TPB.policy_id = TCM.policy_id 
			AND TCM.liability_state= #{policy_liability_state}
			AND TPB.process_status = #{process_status}
			AND TPB.pay_due_date <= #{batch_date}]]>
			<if test=" policy_code  != null  and  policy_code  != ''  "><![CDATA[ AND TCM.policy_code = #{policy_code} ]]></if>
			<if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[AND tcm.organ_code in (
				select t.organ_code
					  from APP___PAS__DBUSER.t_udmp_org_rel t
					 start with t.organ_code = #{organ_code}
					connect by prior t.organ_code = t.uporgan_code)  ]]></if>	
		<![CDATA[)
		]]>
	</select>
	
	<select id="JRQD_findContinueBonusCountList"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT ROWNUM RN, B.POLICY_ID, B.BUSI_PRD_ID, B.BUSI_PROD_CODE, B.ACCOUNT_CODE, B.VALIDATE_DATE
          FROM (SELECT DISTINCT TCBP.POLICY_ID,
                                TCBP.BUSI_PRD_ID,
                                TCBP.BUSI_PROD_CODE,
                                TCI.ACCOUNT_CODE,
                                TCBP.VALIDATE_DATE
                  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
                       APP___PAS__DBUSER.T_CONTRACT_INVEST    TCI
                 WHERE TCBP.BUSI_ITEM_ID = TCI.BUSI_ITEM_ID
                   AND TCBP.LIABILITY_STATE = 1
                   AND NOT EXISTS
                 (SELECT 'X'
                          FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                          LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                            ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
                         WHERE B.SUB_ID IN ('067')
                           AND B.LOCK_SERVICE_TYPE = 1
                           AND A.POLICY_CODE = TCBP.POLICY_CODE)
		]]>
		<if test=" policy_code  != null  and  policy_code  != ''  ">
			<![CDATA[ 
				AND TCBP.POLICY_CODE = #{policy_code} 
			]]>
		</if>
		<if test=" organ_code  != null  and  organ_code  != ''  ">
			<![CDATA[
				AND TCBP.ORGAN_CODE IN (
				SELECT T.ORGAN_CODE
					  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
					 START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE)  
			]]>
		</if>
		<![CDATA[
			) B WHERE 1=1 AND MOD(B.POLICY_ID, #{modnum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR}
		]]>
	</select>
<!-- 	查询保单的持续奖金计划 -->
	<select id="JRQD_findPersistenceBonusPOList"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.BATCH_DATE, A.INVEST_ID, A.ITEM_ID, A.ACCOUNT_CODE, A.PAY_YEAR, 
			A.PROCESS_STATUS, A.FEE_AMOUNT, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.ARAP_FEE_ID,A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A 
		WHERE 1 = 1  
		AND A.process_status = #{process_status}
		AND A.pay_due_date <= #{batch_date}
		AND A.policy_id = ${policy_id}
		]]>
	</select>	
	<!-- 	查询最近一期持续奖金-->
	<select id="JRQD_findTheCurrentIssueOf"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.BATCH_DATE, A.INVEST_ID, A.ITEM_ID, A.ACCOUNT_CODE, A.PAY_YEAR, 
			A.PROCESS_STATUS, A.FEE_AMOUNT, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.ARAP_FEE_ID,A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PERSISTENCE_BONUS A 
		WHERE 1 = 1  
		AND A.POLICY_ID = ${policy_id}
		ORDER BY A.BATCH_DATE DESC
		]]>
	</select>
<!-- 	计算险种总保费 -->
	<select id="JRQD_getBusiProdTotalPremAf"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 select sum(prem_af) as total_prem_af
     	 from  APP___PAS__DBUSER.t_fund_trans A
        ]]>
        <if test=" trans_type  != null "><![CDATA[ AND A.TRANS_TYPE = #{trans_type}]]></if>
	    <if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	    <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
<!-- 	    <if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>          -->
	    <if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.trans_code IN ]]>
			<foreach collection ="trans_code_list" 
			item="index_trans_code" index="index" open="(" close=")" separator=",">#{index_trans_code}</foreach>
		</if>
	</select>
<!-- 	SELECT SUM(t1.total_prem_af) AS total_prem_af  -->
<!-- 		FROM t_contract_product T1  -->
		
<!-- 		WHERE t1.busi_item_id = #{busi_item_id} -->
<!-- 	领取比例阶乘：（1—第i次的部分领取或抵缴保费比例 ） -->
	<select id="JRQD_findAllCBBonusParam1"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select nvl( case
               when (select count(1) as trans_ids
                       from APP___PAS__DBUSER.t_fund_trans a
                      where (1 -
                            nvl(A.trans_proportion,
                                 A.trans_amount / A.balance_units_bf)) = 0
                        AND A.trans_amount > 0
		    			AND A.balance_units_bf > 0
                        ]]>
		<include refid="JRQD_findAllCBBonusParam1WhereCondition" />
		<![CDATA[
                        ) >= 1
               
                then
                0
               else
                (SELECT exp(sum(ln(1 -
                                   nvl(A.trans_proportion,
                                       A.trans_amount / A.balance_units_bf)))) cbbonus_param1
                   FROM APP___PAS__DBUSER.t_fund_trans A
                  WHERE 1 = 1
                    AND A.trans_amount > 0
                    AND A.balance_units_bf > 0
                    ]]>
        <include refid="JRQD_findAllCBBonusParam1WhereCondition" />
		<![CDATA[
                    )
             end ,1)as cbbonus_param1
        from dual
		]]>
	</select>	
<!-- 	 SELECT exp(sum(ln(1 - nvl(A.trans_proportion, -->
<!-- 		                           A.trans_amount / A.balance_units_bf)))) cbbonus_param1 -->
<!-- 		   FROM t_fund_trans A -->
<!-- 		  WHERE 1=1 -->
<!-- 		    AND A.trans_amount > 0 -->
<!-- 		    AND A.balance_units_bf > 0 -->
	
	<!-- 查询累计缴费金额（包括当期缴费金额 -->
	<select id="JRQD_findSumAllInTransAmount"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT nvl(SUM (trans_amount),0) AS trans_amount_sum FROM APP___PAS__DBUSER.t_fund_trans A where 1=1 
		]]>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.DEAL_TIME >= #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.DEAL_TIME < #{end_date} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.list_id = #{list_id} ]]></if>
		<if test=" trans_code_type != null and trans_code_type != ''  "><![CDATA[ and A.trans_code like  #{trans_code_type}||'%' ]]></if>
		<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.trans_code IN ]]>
			<foreach collection ="trans_code_list" 
			item="index_trans_code" index="index" open="(" close=")" separator=",">#{index_trans_code}</foreach>
		</if>
	</select>
	
	<!-- 查询万能结算记录 -->
	<select id="JRQD_findAllFundSettlement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BATCH_DATE, A.INVEST_ID, A.LAST_GURNT_BALANCE, A.ADJUST_FLAG, A.LAST_BALANCE, A.SETTLE_DATE, A.GURNT_INTEREST, 
			A.ADJUST_TYPE, A.INTEREST, A.SETTLEMENT_ID, A.ADJUST_AMOUNT, A.BALANCE, 
			A.BALANCE_BA, A.ACCOUNT_CODE, A.LAST_SETTLE_DATE, A.INTEREST_RATE, A.ADJUST_DATE, 
			A.GURNT_BALANCE, A.GURNT_INTEREST_RATE FROM APP___PAS__DBUSER.T_FUND_SETTLEMENT A WHERE ROWNUM <=  1000  ]]>
		<if test=" invest_id  != null "><![CDATA[ AND A.INVEST_ID = #{invest_id} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND to_date(to_char(A.SETTLE_DATE,'yyyy-MM-dd'),'yyyy-MM-dd') >= to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND to_date(t_ochar(A.SETTLE_DATE,'yyyy-MM-dd'),'yyyy-MM-dd') <= to_date(to_char(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
	</select>
	
	<!-- 查找所有持续奖金初始化任务 -->
	<select id="JRQD_findAllCommonTaskContinueBonus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select ROWNUM AS RN,b.* from( select ROWNUM RN,t.* from APP___PAS__DBUSER.t_pay_plan t where  ROWNUM <= #{LESS_NUM}]]>
		<if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[ AND organ_code = #{organ_code} ]]></if>
		<![CDATA[ and t.pay_plan_type = #{pay_plan_type}
                 and t.pay_due_date < #{batch_date} 
                 and t.policy_code = #{policy_code}  ]]>
		 <![CDATA[ ) b WHERE WHERE 1 = 1 
		        
		 ]]>
	   <if test=" start_num != null and end_num != null  "><![CDATA[ AND MOD(B.POLICY_ID , #{end_num}) = #{start_num} AND ROWNUM <= 5000 ]]></if>		
		 
	</select>
	<!-- 查找所有持续奖金初始化任务数据个数 -->
		<select id="JRQD_queryTaskCounts" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		  select count(1) from (select ROWNUM RN,t.* from APP___PAS__DBUSER.t_pay_plan t where  ROWNUM <= #{LESS_NUM}]]>
		<if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[ AND organ_code = #{organ_code} ]]></if>
		<![CDATA[ and t.pay_plan_type = #{pay_plan_type}
                 and t.pay_due_date < #{batch_date} 
                 and t.policy_code = #{policy_code}  ]]>
		 <![CDATA[ )]]>
	</select>
	
	<select id="JRQD_findTaskGroupByDueTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select sum(A.Fee_Amount) as Fee_Amount,
			       a.unit_number,
			       a.business_code,
			       a.policy_code,
			       a.finish_time,
			       a.busi_prod_code 
			 from APP___PAS__DBUSER.t_prem_arap A
			where 1=1
		 ]]>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<![CDATA[group by a.business_code, a.policy_code, a.due_time,a.unit_number, a.busi_prod_code, a.finish_time]]>
<!-- 		<if test=" common_task_status  != null  and  common_task_status  != ''  "><![CDATA[ AND common_task_status = #{common_task_status} ]]></if> -->
<!-- 		<if test=" sub_id  != null  and  sub_id  != ''  "><![CDATA[ AND sub_id = #{sub_id} ]]></if> -->
<!-- 		<if test=" policy_code  != null  and  policy_code  != ''  "><![CDATA[ AND a.policy_code = #{policy_code} ]]></if> -->
<!-- 		<if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[ AND tcm.organ_code = #{organ_code} ]]></if> -->
<!-- 		<if test=" batch_date  != null  and  batch_date  != ''  "><![CDATA[ AND to_date(to_char(A.finish_time,'yyyy-MM-dd'),'yyyy-MM-dd') <= to_date(to_char(#{batch_date},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if> -->
	</select>
	
<!-- 	查询万能账户的所交保费 -->
		<select id="JRQD_sumPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select nvl(sum(trans_amount), '0') as sum_prem
			  from APP___PAS__DBUSER.t_fund_trans t
			 where 1=1]]>
		<if test=" invest_id  != null "><![CDATA[ AND t.list_id = #{invest_id} ]]></if>
		<![CDATA[
			   and t.trans_code in ('11', '12', '13', '27', '28', '29')
		 ]]>
	</select>
	
</mapper>