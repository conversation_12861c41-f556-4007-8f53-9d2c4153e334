<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IInvestUnitPriceDao">

	<sql id="JRQD_investUnitPriceWhereCondition">
			<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" eva_times  != null "><![CDATA[ AND A.EVA_TIMES = #{eva_times} ]]></if>
		<if test=" confirm_remark != null and confirm_remark != ''  "><![CDATA[ AND A.CONFIRM_REMARK = #{confirm_remark} ]]></if>
		<if test=" confirm_time  != null  and  confirm_time  != ''  "><![CDATA[ AND A.CONFIRM_TIME = #{confirm_time} ]]></if>
		<if test=" expadn_shrink_flag  != null "><![CDATA[ AND A.EXPADN_SHRINK_FLAG = #{expadn_shrink_flag} ]]></if>
		<if test=" dif_price  != null "><![CDATA[ AND A.DIF_PRICE = #{dif_price} ]]></if>
		<if test=" reviser_result  != null "><![CDATA[ AND A.REVISER_RESULT = #{reviser_result} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" reviser_id  != null "><![CDATA[ AND A.REVISER_ID = #{reviser_id} ]]></if>
		<if test=" invest_account_id  != null "><![CDATA[ AND A.INVEST_ACCOUNT_ID = #{invest_account_id} ]]></if>
		<if test=" bid_price  != null "><![CDATA[ AND A.BID_PRICE = #{bid_price} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" fund_assets_id  != null "><![CDATA[ AND A.FUND_ASSETS_ID = #{fund_assets_id} ]]></if>
		<if test=" unit_asset_m_fee  != null "><![CDATA[ AND A.UNIT_ASSET_M_FEE = #{unit_asset_m_fee} ]]></if>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" last_price_id  != null "><![CDATA[ AND A.LAST_PRICE_ID = #{last_price_id} ]]></if>
		<if test=" reviser_time  != null  and  reviser_time  != ''  "><![CDATA[ AND A.REVISER_TIME = #{reviser_time} ]]></if>
		<if test=" reviser_remark != null and reviser_remark != ''  "><![CDATA[ AND A.REVISER_REMARK = #{reviser_remark} ]]></if>
		<if test=" cal_off_price  != null "><![CDATA[ AND A.CAL_OFF_PRICE = #{cal_off_price} ]]></if>
		<if test=" invest_units_daysum_id  != null "><![CDATA[ AND A.INVEST_UNITS_DAYSUM_ID = #{invest_units_daysum_id} ]]></if>
		<if test=" re_calc_indi  != null "><![CDATA[ AND A.RE_CALC_INDI = #{re_calc_indi} ]]></if>
		<if test=" cal_bid_price  != null "><![CDATA[ AND A.CAL_BID_PRICE = #{cal_bid_price} ]]></if>
		<if test=" annualized_return  != null "><![CDATA[ AND A.ANNUALIZED_RETURN = #{annualized_return} ]]></if>
		<if test=" off_price  != null "><![CDATA[ AND A.OFF_PRICE = #{off_price} ]]></if>
		<if test=" confirmer_id  != null "><![CDATA[ AND A.CONFIRMER_ID = #{confirmer_id} ]]></if>
		<if test=" confirm_result  != null "><![CDATA[ AND A.CONFIRM_RESULT = #{confirm_result} ]]></if>
	    <if test="invest_account_id_list != null and invest_account_id_list.size() > 0">
			<![CDATA[ AND A.INVEST_ACCOUNT_ID IN ]]>
			<foreach collection="invest_account_id_list" item="invest_account_id_list"
			index="index" open="(" close=")" separator=",">#{invest_account_id_list}</foreach>
		</if>
		
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.PRICING_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.PRICING_DATE <= #{end_date} ]]></if>
		
	</sql>
<!--start by liucmit  -->
<select id="JRQD_getInvestUnitPricePO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 		SELECT ROWNUM RN ,B.* from (
					SELECT A.MONEY_CODE,
					       A.EVA_TIMES,
					       A.CONFIRM_REMARK,
					       A.CONFIRM_TIME,
					       A.EXPADN_SHRINK_FLAG,
					       A.DIF_PRICE,
					       A.REVISER_RESULT,
					       A.LIST_ID,
					       A.REVISER_ID,
					       A.INVEST_ACCOUNT_ID,
					       A.BID_PRICE,
					       A.PRICING_DATE,
					       A.FUND_ASSETS_ID,
					       A.UNIT_ASSET_M_FEE,
					       A.INVEST_ACCOUNT_CODE,
					       A.LAST_PRICE_ID,
					       A.REVISER_TIME,
					       A.REVISER_REMARK,
					       A.CAL_OFF_PRICE,
					       A.INVEST_UNITS_DAYSUM_ID,
					       A.CAL_BID_PRICE,
					       A.OFF_PRICE,
					       A.CONFIRMER_ID,
					       A.CONFIRM_RESULT
					  FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A
					 WHERE 1 = 1
					       ]]>
		<include refid="JRQD_investUnitPriceWhereCondition" />
		<![CDATA[ order by a.PRICING_DATE desc )B where rownum = 1 ]]>
	</select>
	
	<!--start by liucmit (生产并行逻辑) -->
    <select id="JRQD_getInvestUnitPricePOTWO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 		SELECT ROWNUM RN ,B.* from (
					SELECT A.MONEY_CODE,
					       A.EVA_TIMES,
					       A.CONFIRM_REMARK,
					       A.CONFIRM_TIME,
					       A.EXPADN_SHRINK_FLAG,
					       A.DIF_PRICE,
					       A.REVISER_RESULT,
					       A.LIST_ID,
					       A.REVISER_ID,
					       A.INVEST_ACCOUNT_ID,
					       A.BID_PRICE,
					       A.PRICING_DATE,
					       A.FUND_ASSETS_ID,
					       A.UNIT_ASSET_M_FEE,
					       A.INVEST_ACCOUNT_CODE,
					       A.LAST_PRICE_ID,
					       A.REVISER_TIME,
					       A.REVISER_REMARK,
					       A.CAL_OFF_PRICE,
					       A.INVEST_UNITS_DAYSUM_ID,
					       A.CAL_BID_PRICE,
					       A.OFF_PRICE,
					       A.CONFIRMER_ID,
					       A.CONFIRM_RESULT
					  FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_B A
					 WHERE 1 = 1
					       ]]>
		<include refid="JRQD_investUnitPriceWhereCondition" />
		<![CDATA[ order by a.PRICING_DATE desc )B where rownum = 1 ]]>
	</select>
	
	<select id="JRQD_getInvestUnitPricePO_2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRICING_DATE
			 FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE A.LIST_ID = #{last_price_id} ]]>
		<!-- <include refid="JRQD_investUnitPriceWhereCondition" /> -->
	</select>
<!--start by liucmit  -->	
<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryInvestUnitPriceByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addInvestUnitPrice"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
				SELECT APP___PAS__DBUSER.s_contract_call.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_INVEST_UNIT_PRICE(
				MONEY_CODE, EVA_TIMES, CONFIRM_REMARK, CONFIRM_TIME, EXPADN_SHRINK_FLAG, DIF_PRICE, REVISER_RESULT, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, REVISER_ID, INVEST_ACCOUNT_ID, BID_PRICE, PRICING_DATE, 
				FUND_ASSETS_ID, UNIT_ASSET_M_FEE, INVEST_ACCOUNT_CODE, LAST_PRICE_ID, REVISER_TIME, REVISER_REMARK, INSERT_TIME, 
				CAL_OFF_PRICE, INVEST_UNITS_DAYSUM_ID, RE_CALC_INDI, CAL_BID_PRICE, UPDATE_TIME, ANNUALIZED_RETURN, OFF_PRICE, 
				CONFIRMER_ID, CONFIRM_RESULT, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{money_code, jdbcType=VARCHAR}, #{eva_times, jdbcType=NUMERIC} , #{confirm_remark, jdbcType=VARCHAR} , #{confirm_time, jdbcType=DATE} , #{expadn_shrink_flag, jdbcType=NUMERIC} , #{dif_price, jdbcType=NUMERIC} , #{reviser_result, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{reviser_id, jdbcType=NUMERIC} , #{invest_account_id, jdbcType=NUMERIC} , #{bid_price, jdbcType=NUMERIC} , #{pricing_date, jdbcType=DATE} 
				, #{fund_assets_id, jdbcType=NUMERIC} , #{unit_asset_m_fee, jdbcType=NUMERIC} , #{invest_account_code, jdbcType=VARCHAR} , #{last_price_id, jdbcType=NUMERIC} , #{reviser_time, jdbcType=DATE} , #{reviser_remark, jdbcType=VARCHAR} , SYSDATE 
				, #{cal_off_price, jdbcType=NUMERIC} , #{invest_units_daysum_id, jdbcType=NUMERIC} , #{re_calc_indi, jdbcType=NUMERIC} , #{cal_bid_price, jdbcType=NUMERIC} , SYSDATE , #{annualized_return, jdbcType=NUMERIC} , #{off_price, jdbcType=NUMERIC} 
				, #{confirmer_id, jdbcType=NUMERIC} , #{confirm_result, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} )
		 ]]>
	</insert>
    <!-- 添加操作 生产挡板逻辑-->
	<insert id="JRQD_addInvestUnitPriceTWO"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
				SELECT APP___PAS__DBUSER.S_INVEST_UN_PRICE_B__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_B(
				MONEY_CODE, EVA_TIMES, CONFIRM_REMARK, CONFIRM_TIME, EXPADN_SHRINK_FLAG, DIF_PRICE, REVISER_RESULT, 
				INSERT_TIMESTAMP, UPDATE_BY, LIST_ID, REVISER_ID, INVEST_ACCOUNT_ID, BID_PRICE, PRICING_DATE, 
				FUND_ASSETS_ID, UNIT_ASSET_M_FEE, INVEST_ACCOUNT_CODE, LAST_PRICE_ID, REVISER_TIME, REVISER_REMARK, INSERT_TIME, 
				CAL_OFF_PRICE, INVEST_UNITS_DAYSUM_ID, RE_CALC_INDI, CAL_BID_PRICE, UPDATE_TIME, ANNUALIZED_RETURN, OFF_PRICE, 
				CONFIRMER_ID, CONFIRM_RESULT, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{money_code, jdbcType=VARCHAR}, #{eva_times, jdbcType=NUMERIC} , #{confirm_remark, jdbcType=VARCHAR} , #{confirm_time, jdbcType=DATE} , #{expadn_shrink_flag, jdbcType=NUMERIC} , #{dif_price, jdbcType=NUMERIC} , #{reviser_result, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{reviser_id, jdbcType=NUMERIC} , #{invest_account_id, jdbcType=NUMERIC} , #{bid_price, jdbcType=NUMERIC} , #{pricing_date, jdbcType=DATE} 
				, #{fund_assets_id, jdbcType=NUMERIC} , #{unit_asset_m_fee, jdbcType=NUMERIC} , #{invest_account_code, jdbcType=VARCHAR} , #{last_price_id, jdbcType=NUMERIC} , #{reviser_time, jdbcType=DATE} , #{reviser_remark, jdbcType=VARCHAR} , SYSDATE 
				, #{cal_off_price, jdbcType=NUMERIC} , #{invest_units_daysum_id, jdbcType=NUMERIC} , #{re_calc_indi, jdbcType=NUMERIC} , #{cal_bid_price, jdbcType=NUMERIC} , SYSDATE , #{annualized_return, jdbcType=NUMERIC} , #{off_price, jdbcType=NUMERIC} 
				, #{confirmer_id, jdbcType=NUMERIC} , #{confirm_result, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} )
		 ]]>
	</insert>   
    
    <!-- 添加操作 -->
    <insert id="JRQD_addInvestUnitPriceInterface"  useGeneratedKeys="false"  parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
                SELECT APP___PAS__DBUSER.s_contract_call.nextval from dual
        </selectKey>
        <![CDATA[
            INSERT INTO APP___PAS__DBUSER.T_INVEST_UNIT_PRICE(
                LIST_ID,MONEY_CODE, EVA_TIMES,  CONFIRM_TIME,  
                INSERT_TIMESTAMP, UPDATE_BY, BID_PRICE, PRICING_DATE, 
                 UNIT_ASSET_M_FEE, INVEST_ACCOUNT_CODE, INSERT_TIME, 
                  UPDATE_TIME, OFF_PRICE, CAL_OFF_PRICE,CAL_BID_PRICE,
                 CONFIRM_RESULT, UPDATE_TIMESTAMP, INSERT_BY ) 
            VALUES (
                #{list_id, jdbcType=NUMERIC},#{money_code, jdbcType=VARCHAR}, #{eva_times, jdbcType=NUMERIC} , #{confirm_time, jdbcType=DATE} 
                , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{bid_price, jdbcType=NUMERIC} , #{pricing_date, jdbcType=DATE} 
                ,  #{unit_asset_m_fee, jdbcType=NUMERIC} , #{invest_account_code, jdbcType=VARCHAR} ,  SYSDATE 
                , SYSDATE , #{off_price, jdbcType=NUMERIC} ,#{cal_off_price, jdbcType=NUMERIC},#{cal_bid_price, jdbcType=NUMERIC} 
                ,  #{confirm_result, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} )
         ]]>
    </insert>
    
    

<!-- 删除操作 -->	
	<delete id="JRQD_deleteInvestUnitPrice" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE WHERE  LIST_ID=#{list_id} ]]>
		<![CDATA[  and  LIST_ID = #{list_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateInvestUnitPrice" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A ]]>
		<set>
		<trim suffixOverrides=",">
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    EVA_TIMES = #{eva_times, jdbcType=NUMERIC} ,
			CONFIRM_REMARK = #{confirm_remark, jdbcType=VARCHAR} ,
		    CONFIRM_TIME = #{confirm_time, jdbcType=DATE} ,
		    EXPADN_SHRINK_FLAG = #{expadn_shrink_flag, jdbcType=NUMERIC} ,
		    DIF_PRICE = #{dif_price, jdbcType=NUMERIC} ,
		    REVISER_RESULT = #{reviser_result, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    REVISER_ID = #{reviser_id, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} ,
		    BID_PRICE = #{bid_price, jdbcType=NUMERIC} ,
		    PRICING_DATE = #{pricing_date, jdbcType=DATE} ,
		    FUND_ASSETS_ID = #{fund_assets_id, jdbcType=NUMERIC} ,
		    UNIT_ASSET_M_FEE = #{unit_asset_m_fee, jdbcType=NUMERIC} ,
			INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
		    LAST_PRICE_ID = #{last_price_id, jdbcType=NUMERIC} ,
		    REVISER_TIME = #{reviser_time, jdbcType=DATE} ,
			REVISER_REMARK = #{reviser_remark, jdbcType=VARCHAR} ,
		    CAL_OFF_PRICE = #{cal_off_price, jdbcType=NUMERIC} ,
		    INVEST_UNITS_DAYSUM_ID = #{invest_units_daysum_id, jdbcType=NUMERIC} ,
		    RE_CALC_INDI = #{re_calc_indi, jdbcType=NUMERIC} ,
		    CAL_BID_PRICE = #{cal_bid_price, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ANNUALIZED_RETURN = #{annualized_return, jdbcType=NUMERIC} ,
		    OFF_PRICE = #{off_price, jdbcType=NUMERIC} ,
		    CONFIRMER_ID = #{confirmer_id, jdbcType=NUMERIC} ,
		    CONFIRM_RESULT = #{confirm_result, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  1=1 ]]>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</update>

<!-- 修改操作 (生产并行逻辑)-->
	<update id="JRQD_updateInvestUnitPriceTWO" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_B A ]]>
		<set>
		<trim suffixOverrides=",">
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    EVA_TIMES = #{eva_times, jdbcType=NUMERIC} ,
			CONFIRM_REMARK = #{confirm_remark, jdbcType=VARCHAR} ,
		    CONFIRM_TIME = #{confirm_time, jdbcType=DATE} ,
		    EXPADN_SHRINK_FLAG = #{expadn_shrink_flag, jdbcType=NUMERIC} ,
		    DIF_PRICE = #{dif_price, jdbcType=NUMERIC} ,
		    REVISER_RESULT = #{reviser_result, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    REVISER_ID = #{reviser_id, jdbcType=NUMERIC} ,
		    INVEST_ACCOUNT_ID = #{invest_account_id, jdbcType=NUMERIC} ,
		    BID_PRICE = #{bid_price, jdbcType=NUMERIC} ,
		    PRICING_DATE = #{pricing_date, jdbcType=DATE} ,
		    FUND_ASSETS_ID = #{fund_assets_id, jdbcType=NUMERIC} ,
		    UNIT_ASSET_M_FEE = #{unit_asset_m_fee, jdbcType=NUMERIC} ,
			INVEST_ACCOUNT_CODE = #{invest_account_code, jdbcType=VARCHAR} ,
		    LAST_PRICE_ID = #{last_price_id, jdbcType=NUMERIC} ,
		    REVISER_TIME = #{reviser_time, jdbcType=DATE} ,
			REVISER_REMARK = #{reviser_remark, jdbcType=VARCHAR} ,
		    CAL_OFF_PRICE = #{cal_off_price, jdbcType=NUMERIC} ,
		    INVEST_UNITS_DAYSUM_ID = #{invest_units_daysum_id, jdbcType=NUMERIC} ,
		    RE_CALC_INDI = #{re_calc_indi, jdbcType=NUMERIC} ,
		    CAL_BID_PRICE = #{cal_bid_price, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ANNUALIZED_RETURN = #{annualized_return, jdbcType=NUMERIC} ,
		    OFF_PRICE = #{off_price, jdbcType=NUMERIC} ,
		    CONFIRMER_ID = #{confirmer_id, jdbcType=NUMERIC} ,
		    CONFIRM_RESULT = #{confirm_result, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE  1=1 ]]>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</update>
<!-- 按索引查询操作 -->	
	<select id="JRQD_findInvestUnitPriceByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1 = 1 ]]>
		<include refid="JRQD_queryInvestUnitPriceByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID DESC]]>
	</select>
<!-- 	日期前最近的单位价格，保单本日 -->
	<select id="JRQD_findLastInvestUnitPriceByDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select * from (SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1 = 1 ]]>
	 <if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
	 <if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE <= TO_DATE(TO_CHAR(#{pricing_date},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
	
		<!-- <include refid="JRQD_investUnitPriceWhereCondition" /> -->
		<![CDATA[ ORDER BY A.PRICING_DATE DESC) where rownum = 1]]>
	</select>

<!-- 	条件查询单条数据,单位价格复核查询某个账户所有信息 -->
	<select id="JRQD_findInvestUnitPrice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1 = 1 ]]>
		<include refid="JRQD_investUnitPriceWhereCondition" />
		<![CDATA[ ORDER BY A.PRICING_DATE DESC]]>
	</select>
	<!-- 	条件查询单条数据,单位价格复核查询某个账户所有信息生产挡板逻辑 -->
	<select id="JRQD_findInvestUnitPriceTWO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_B A WHERE 1 = 1 ]]>
		<include refid="JRQD_investUnitPriceWhereCondition" />
		<![CDATA[ ORDER BY A.PRICING_DATE DESC]]>
	</select>
<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapInvestUnitPrice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_editFindAllInvestUnitPriceByInvestAccountCodee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_investUnitPriceWhereCondition" /> 
		 <![CDATA[ ORDER BY A.PRICING_DATE DESC]]>
	</select>
<!-- 查询所有操作 -->
	<select id="JRQD_findAllInvestUnitPrice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT,
       ROWNUM RN FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1=1 ]]>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.PRICING_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.PRICING_DATE <= #{end_date} ]]></if>
		<![CDATA[ ORDER BY A.PRICING_DATE DESC]]>
	</select>
<!-- 查询单条数据操作(小录入完成) -->
	<select id="JRQD_findAllInvestUnitPriceForEntry" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1=1 ]]>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.PRICING_DATE >= #{start_date} ]]></if>
		<!-- <if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.PRICING_DATE <= #{end_date} ]]></if> -->
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.PRICING_DATE = 
		(SELECT MAX(A.PRICING_DATE) FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE   
		A.PRICING_DATE <= #{end_date} ]]>
		      <if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		      <![CDATA[ )  ]]>
		</if>
		<![CDATA[ ORDER BY A.PRICING_DATE DESC  ]]>
	</select>
<!-- 查询个数操作 -->
	<select id="JRQD_editFindInvestUnitPriceTotalByInvestAccountCodee" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1 = 1  ]]>
		 <include refid="JRQD_investUnitPriceWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findInvestUnitPriceTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1 = 1  ]]>
		 <include refid="JRQD_investUnitPriceWhereCondition" /> 
	</select>
	
<!-- 分页查询操作 -->
	<select id="JRQD_queryInvestUnitPriceForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MONEY_CODE, B.EVA_TIMES, B.CONFIRM_REMARK, B.CONFIRM_TIME, B.EXPADN_SHRINK_FLAG, B.DIF_PRICE, B.REVISER_RESULT, 
			B.LIST_ID, B.REVISER_ID, B.INVEST_ACCOUNT_ID, B.BID_PRICE, B.PRICING_DATE, 
			B.FUND_ASSETS_ID, B.UNIT_ASSET_M_FEE, B.INVEST_ACCOUNT_CODE, B.LAST_PRICE_ID, B.REVISER_TIME, B.REVISER_REMARK, 
			B.CAL_OFF_PRICE, B.INVEST_UNITS_DAYSUM_ID, B.RE_CALC_INDI, B.CAL_BID_PRICE, B.OFF_PRICE, B.CONFIRMER_ID, 
			B.CONFIRM_RESULT FROM (
					SELECT ROWNUM RN, A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE,A.ANNUALIZED_RETURN, A.OFF_PRICE, A.CONFIRMER_ID, 
			A.CONFIRM_RESULT FROM (
			       SELECT M.MONEY_CODE, M.EVA_TIMES, M.CONFIRM_REMARK, M.CONFIRM_TIME, M.EXPADN_SHRINK_FLAG, M.DIF_PRICE, M.REVISER_RESULT, 
			M.LIST_ID, M.REVISER_ID, M.INVEST_ACCOUNT_ID, M.BID_PRICE, M.PRICING_DATE, 
			M.FUND_ASSETS_ID, M.UNIT_ASSET_M_FEE, M.INVEST_ACCOUNT_CODE, M.LAST_PRICE_ID, M.REVISER_TIME, M.REVISER_REMARK, 
			M.CAL_OFF_PRICE, M.INVEST_UNITS_DAYSUM_ID, M.RE_CALC_INDI, M.CAL_BID_PRICE,M.ANNUALIZED_RETURN, M.OFF_PRICE, M.CONFIRMER_ID, 
			M.CONFIRM_RESULT FROM 
			APP___PAS__DBUSER.T_INVEST_UNIT_PRICE M  WHERE 1 = 1 ]]>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND M.PRICING_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND M.PRICING_DATE <= #{end_date} ]]></if> 
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND M.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<![CDATA[ ORDER BY M.PRICING_DATE DESC ) A WHERE ROWNUM <= #{LESS_NUM}]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 通过invest_account_code条件查询未审核的单位投资价格信息 xuyp-->
	<select id="JRQD_checkFundFindInvestUnitPriceByInvestAccountCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT ROWNUM RN ,B.* from (
			SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1 = 1]]>
			<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
			  <if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
			  <if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>      
			 <![CDATA[       
	  order by a.PRICING_DATE desc
	)B where rownum = 1
		]]>	
	</select>
	<!-- 通过invest_account_code条件查询未审核的单位投资价格信息 xuyp 生产挡板逻辑-->
	<select id="JRQD_checkFundFindInvestUnitPriceByInvestAccountCodeTWO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT ROWNUM RN ,B.* from (
			SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_B A WHERE 1 = 1]]>
			<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
			  <if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
			  <if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[ AND INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>      
			 <![CDATA[       
	  order by a.PRICING_DATE desc
	)B where rownum = 1
		]]>	
	</select>
<!-- 通过list_id 条件查询单位价格投资数据  xuyp-->
<select id="JRQD_checkFundFindInvestUnitPriceByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1 = 1  AND LIST_ID=#{list_id}]]>	
	</select>
<!-- 通过list_id条件更新投资单位价格表中不部分信息 xuyp-->
	<update id="JRQD_checkFundUpdateInvestUnitPrice" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_UNIT_PRICE]]>
		<set>
		<trim suffixOverrides=",">
			<!--  MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,-->
		    EXPADN_SHRINK_FLAG = #{expadn_shrink_flag, jdbcType=NUMERIC} ,
		    REVISER_RESULT = #{reviser_result, jdbcType=NUMERIC} ,
		    REVISER_REMARK = #{reviser_remark, jdbcType=VARCHAR} ,
		    OFF_PRICE = #{off_price, jdbcType=NUMERIC} ,
		    BID_PRICE = #{bid_price, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  1=1 AND LIST_ID = #{list_id}]]>
	</update>
	<!-- 通过list_id条件更新投资单位价格表中不部分信息 xuyp生产挡板逻辑-->
	<update id="JRQD_checkFundUpdateInvestUnitPriceTWO" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_UNIT_PRICE_B]]>
		<set>
		<trim suffixOverrides=",">
			<!--  MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,-->
		    EXPADN_SHRINK_FLAG = #{expadn_shrink_flag, jdbcType=NUMERIC} ,
		    REVISER_RESULT = #{reviser_result, jdbcType=NUMERIC} ,
		    REVISER_REMARK = #{reviser_remark, jdbcType=VARCHAR} ,
		    OFF_PRICE = #{off_price, jdbcType=NUMERIC} ,
		    BID_PRICE = #{bid_price, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  1=1 AND LIST_ID = #{list_id}]]>
	</update>
	<!-- 保存按钮 -->
	<update id="JRQD_update" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INVEST_UNIT_PRICE ]]>
		<set>
		<trim suffixOverrides=",">
			CONFIRM_REMARK = #{confirm_remark, jdbcType=VARCHAR} ,
		    CONFIRM_RESULT = #{confirm_result, jdbcType=NUMERIC} ,
		    
		</trim>
		</set>
		<![CDATA[ WHERE  1=1 AND list_id = #{list_id}]]>
	</update>
	<!-- 查找上次计价日 -->
	<select id="JRQD_findLastPricingDay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRICING_DATE
			 FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE A.LIST_ID = #{last_price_id} ]]>
		<!-- <include refid="JRQD_investUnitPriceWhereCondition" /> -->
	</select>
	
	<!-- 对应投资账户上一次投资单位价格信息 -->
	<select id="JRQD_findLastInvestUnitPriceByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select t2.*,rownum from ( SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1 = 1 ]]>
		<include refid="JRQD_investUnitPriceWhereCondition" />
		<![CDATA[ order by update_time desc ) t2 where rownum = 1]]>
	</select>
	
	
	
	<!-- 投连单位价格查询 --> 
	<select id="JRQD_priceListQuerySql" resultType="java.util.Map" parameterType="java.util.Map">
		<if test="select_count !=null">
				<![CDATA[	SELECT 
			        TIUP.PRICING_DATE,
			        TIUP.EVA_TIMES,
					TIUP.OFF_PRICE,
			        TIUP.BID_PRICE,
					TIUP.ANNUALIZED_RETURN,
			        DECODE(TBP.PRODUCT_CODE_SYS,'********','0','1') RISK_TYPE,
			        TBP.PRODUCT_CODE_SYS,
			        TBP.PRODUCT_NAME_SYS,
					TIAI.INVEST_ACCOUNT_NAME 
					FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE TIUP,
			    APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO TIAI,
			    APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP
			WHERE 1=1 ]]>
			<if test="invest_account_code !=null"><![CDATA[AND TIUP.INVEST_ACCOUNT_CODE=#{invest_account_code} ]]></if>
			<if test="value_date_start !=null"><![CDATA[AND TIUP.PRICING_DATE>=#{value_date_start}]]></if>
			<if test="value_date_end !=null"><![CDATA[AND TIUP.PRICING_DATE<=#{value_date_end}]]></if>
			<![CDATA[AND TIUP.INVEST_ACCOUNT_CODE=TIAI.INVEST_ACCOUNT_CODE
			AND TBP.PRODUCT_CODE_SYS LIKE '%00'|| SUBSTR(TIUP.INVEST_ACCOUNT_CODE,0,3) ||'%']]>
		</if>
	
		<if test="select_count ==null">
				<![CDATA[SELECT C.* FROM ( SELECT ROWNUM RN,B.* FROM ( 
			 SELECT 
			        TIUP.PRICING_DATE,
			        TIUP.EVA_TIMES,
					TIUP.OFF_PRICE,
			        TIUP.BID_PRICE,
					TIUP.ANNUALIZED_RETURN,
			        DECODE(TBP.PRODUCT_CODE_SYS,'********','0','1') RISK_TYPE,
			        TBP.PRODUCT_CODE_SYS,
			        TBP.PRODUCT_NAME_SYS,
					TIAI.INVEST_ACCOUNT_NAME 
					FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE TIUP,
			    APP___PAS__DBUSER.T_INVEST_ACCOUNT_INFO TIAI,
			    APP___PDS__DBUSER.T_BUSINESS_PRODUCT TBP
			WHERE 1=1 ]]>
		     <if test="invest_account_code !=null"><![CDATA[AND TIUP.INVEST_ACCOUNT_CODE=#{invest_account_code} ]]></if>
		     <if test="value_date_start !=null"><![CDATA[AND TIUP.PRICING_DATE>=#{value_date_start}]]></if>
		     <if test="value_date_end !=null"><![CDATA[AND TIUP.PRICING_DATE<=#{value_date_end}]]></if>
			<![CDATA[AND TIUP.INVEST_ACCOUNT_CODE=TIAI.INVEST_ACCOUNT_CODE
			AND TBP.PRODUCT_CODE_SYS LIKE '%00'|| SUBSTR(TIUP.INVEST_ACCOUNT_CODE,0,3) ||'%']]>
			<if test="orderflag == '0'.toString() or orderflag == '1'.toString()"><![CDATA[ORDER BY TIUP.PRICING_DATE]]></if>
			<if test="orderflag =='2'.toString() "><![CDATA[ORDER BY TIUP.PRICING_DATE DESC]]></if>
			<![CDATA[)  B WHERE ROWNUM<=#{endnum}) C WHERE C.RN >#{startnum} ]]>
		</if>
	</select>
	
	<!-- 保单投资单位数及价格查询 -->
	<select id="JRQD_priceListQuerySql1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select INVEST_UNITS_DAYSUM_ID,LIST_ID,BID_PRICE, OFF_PRICE from APP___PAS__DBUSER.T_INVEST_UNIT_PRICE where 1=1 ]]>
	</select>
	
	
	<select id="JRQD_queryInvestUnitPriceByMoneyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select * from APP___PAS__DBUSER.T_INVEST_UNIT_PRICE a where 1=1 and rownum = 1 ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_queryInvestUnitPriceByPricingDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A
			WHERE A.INVEST_ACCOUNT_CODE = #{invest_account_code,jdbcType=VARCHAR} 
			AND A.PRICING_DATE=(SELECT MAX(B.PRICING_DATE) FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE B WHERE B.INVEST_ACCOUNT_CODE = #{invest_account_code,jdbcType=VARCHAR} 
			AND to_char(B.PRICING_DATE,'yyyy-MM-dd') <= to_char(#{pricing_date},'yyyy-MM-dd'))]]>
	</select>
	
	
	<!-- 投连险查最近一次的计价日 -->
		<select id="JRQD_findAllInvestUnitPriceOne" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT B.MONEY_CODE, B.EVA_TIMES, B.CONFIRM_REMARK, B.CONFIRM_TIME, B.EXPADN_SHRINK_FLAG, B.DIF_PRICE, B.REVISER_RESULT, 
			B.LIST_ID, B.REVISER_ID, B.INVEST_ACCOUNT_ID, B.BID_PRICE, B.PRICING_DATE, 
			B.FUND_ASSETS_ID, B.UNIT_ASSET_M_FEE, B.INVEST_ACCOUNT_CODE, B.LAST_PRICE_ID, B.REVISER_TIME, B.REVISER_REMARK, 
			B.CAL_OFF_PRICE, B.INVEST_UNITS_DAYSUM_ID, B.RE_CALC_INDI, B.CAL_BID_PRICE, B.ANNUALIZED_RETURN, B.OFF_PRICE, 
			B.CONFIRMER_ID, B.CONFIRM_RESULT FROM(
		 SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE   ]]>
		<if test=" invest_account_code != null and invest_account_code != ''  "><![CDATA[  A.INVEST_ACCOUNT_CODE = #{invest_account_code} ]]></if>
		<if test=" pricing_date  != null  and  pricing_date  != ''  "><![CDATA[ AND A.PRICING_DATE = #{pricing_date} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.PRICING_DATE >= #{start_date} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.PRICING_DATE <= #{end_date} ]]></if>
		<![CDATA[ ORDER BY A.PRICING_DATE DESC]]>
		)B WHERE ROWNUM = 1
		
	</select>
	<!-- 查询指定日期下一计价日的单位价格 按计价日升序排列 -->
	<select id="JRQD_cs_findNextInvestUnitPrice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT,ROWNUM RN FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1=1  ]]>
		<if test=" it_apply_date != null and  it_apply_date != ''  "><![CDATA[ AND  to_char(A.PRICING_DATE,'yyyy-MM-dd') > to_char(#{it_apply_date},'yyyy-MM-dd') ]]></if>
		<include refid="JRQD_investUnitPriceWhereCondition" /> 
		 <![CDATA[ ORDER BY A.PRICING_DATE ASC]]>
	</select>
	
	<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryInvestUnitPriceForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MONEY_CODE, B.EVA_TIMES, B.CONFIRM_REMARK, B.CONFIRM_TIME, B.EXPADN_SHRINK_FLAG, B.DIF_PRICE, B.REVISER_RESULT, 
			B.LIST_ID, B.REVISER_ID, B.INVEST_ACCOUNT_ID, B.BID_PRICE, B.PRICING_DATE, 
			B.FUND_ASSETS_ID, B.UNIT_ASSET_M_FEE, B.INVEST_ACCOUNT_CODE, B.LAST_PRICE_ID, B.REVISER_TIME, B.REVISER_REMARK, 
			B.CAL_OFF_PRICE, B.INVEST_UNITS_DAYSUM_ID, B.RE_CALC_INDI, B.CAL_BID_PRICE, B.ANNUALIZED_RETURN, B.OFF_PRICE, 
			B.CONFIRMER_ID, B.CONFIRM_RESULT FROM (
					SELECT ROWNUM RN, A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查找下一个记价日 -->
	<select id="JRQD_findNextInvestUnitPrice" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT * FROM(
		<![CDATA[SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE ROWNUM <=  1000  ]]>
		<if test="invest_account_code != null and invest_account_code != '' "> AND A.INVEST_ACCOUNT_CODE = #{invest_account_code}  </if>
		<if test=" pricing_date != null and  pricing_date != ''  "><![CDATA[ AND  A.PRICING_DATE > #{pricing_date} ]]></if>
		 
		 <![CDATA[ ORDER BY A.PRICING_DATE ASC]]>
		 ) WHERE ROWNUM=1
	</select>
	<!-- 如果没有记价日  查找最近记价日 -->
		<select id="JRQD_findNextInvestUnitPriceAgain" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT * FROM(
		<![CDATA[SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1=1  ]]>
		<if test="invest_account_code != null and invest_account_code != '' "> AND A.INVEST_ACCOUNT_CODE = #{invest_account_code}  </if>
		<if test=" pricing_date != null and  pricing_date != ''  "><![CDATA[ AND  A.PRICING_DATE <= #{pricing_date} ]]></if>
		 
		 <![CDATA[ ORDER BY A.PRICING_DATE DESC]]>
		 ) WHERE ROWNUM=1
	</select>
	<!-- 查詢資產-->
	<select id="JRQD_findInvestUnitPriceObjectOnly" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT B.ACCOUNT_VALUE_NET AS ACCOUNT_VALUE_NET
		 FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A,
		    APP___PAS__DBUSER.T_FUND_ASSETS  B
	     WHERE A.FUND_ASSETS_ID = B.LIST_ID
		  AND A.INVEST_ACCOUNT_CODE = #{invest_account_code}
          AND A.PRICING_DATE = #{pricing_date}
	</select>
	
	<!-- 查询包含申请提交日期之前最近一天的数据  
	
	别乱摸我-->
	<select id="JRQD_queryInvestUnitPriceApplyTimeBefor" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT * FROM(
		<![CDATA[SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
			A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
			A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
			A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
			A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE A.INVEST_ACCOUNT_CODE = #{invest_account_code} 
			AND A.PRICING_DATE <= #{pricing_date}
			AND ROWNUM <=  1000  ]]>
		 
		 <![CDATA[ ORDER BY A.PRICING_DATE DESC]]>
		 ) WHERE ROWNUM=1
	</select>
	
	<!-- 根据投资账户代码查询最近的计价日的卖出价 -->
	<select id="JRQD_queryBidPriceByInvestAccountCode" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT * FROM(
		<![CDATA[SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
      A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
      A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
      A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
      A.CONFIRMER_ID, A.CONFIRM_RESULT FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE A.INVEST_ACCOUNT_CODE = #{invest_account_code} 
      AND A.PRICING_DATE > #{pricing_date}
      AND ROWNUM <=  1000  ]]>		 
		 <![CDATA[ ORDER BY A.PRICING_DATE ASC]]>
		 ) WHERE ROWNUM=1
	</select>
	
	
	<!-- 保全 根据投资账户代码查询最近的计价日的卖出价 -->
	<select id="JRQD_cs_queryBidPriceByInvestAccountCode" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT * FROM(
		<![CDATA[SELECT A.MONEY_CODE, A.EVA_TIMES, A.CONFIRM_REMARK, A.CONFIRM_TIME, A.EXPADN_SHRINK_FLAG, A.DIF_PRICE, A.REVISER_RESULT, 
				      A.LIST_ID, A.REVISER_ID, A.INVEST_ACCOUNT_ID, A.BID_PRICE, A.PRICING_DATE, 
				      A.FUND_ASSETS_ID, A.UNIT_ASSET_M_FEE, A.INVEST_ACCOUNT_CODE, A.LAST_PRICE_ID, A.REVISER_TIME, A.REVISER_REMARK, 
				      A.CAL_OFF_PRICE, A.INVEST_UNITS_DAYSUM_ID, A.RE_CALC_INDI, A.CAL_BID_PRICE, A.ANNUALIZED_RETURN, A.OFF_PRICE, 
				      A.CONFIRMER_ID, A.CONFIRM_RESULT 
		      	FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A 
		      		WHERE A.INVEST_ACCOUNT_CODE = #{invest_account_code} 
		      		AND A.PRICING_DATE <= #{pricing_date}
        ]]>		 
		 <![CDATA[ ORDER BY A.PRICING_DATE DESC]]>
		 ) WHERE ROWNUM=1
	</select>
	
	
	<!-- 投连价格查询 -->
	<select id="JRQD_queryCastEvenPrice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM( 
				SELECT A.LIST_ID, A.INVEST_ACCOUNT_CODE, A.PRICING_DATE, A.CAL_BID_PRICE 
				FROM DEV_PAS.T_INVEST_UNIT_PRICE A 
				WHERE A.INVEST_ACCOUNT_CODE LIKE '892%' AND A.EVA_TIMES = '1' 
				ORDER BY PRICING_DATE DESC 
			) WHERE ROWNUM=1 
		]]>
	</select>
	
	<!-- 查询投资账户结算日期上期和下期计价日期 -->
	<select id="JRQD_queryMaxAndMinPriceDate" resultType="java.util.Map" parameterType="java.util.Map">
		<if test="maxPriceDate != null and maxPriceDate != '' "><![CDATA[ SELECT MAX(PRICING_DATE)PRICING_DATE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE WHERE INVEST_ACCOUNT_CODE =#{invest_account_code} AND  PRICING_DATE < #{maxPriceDate}]]></if>
		<if test="minPriceDate != null and minPriceDate != '' "><![CDATA[ SELECT MIN(PRICING_DATE)PRICING_DATE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE WHERE INVEST_ACCOUNT_CODE =#{invest_account_code} AND  PRICING_DATE>= #{minPriceDate}]]></if>
		<if test="pricing_date != null and pricing_date != '' "><![CDATA[ SELECT MAX(PRICING_DATE)PRICING_DATE FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE WHERE INVEST_ACCOUNT_CODE =#{invest_account_code} ]]></if>
	</select>
	<!-- 查询挡板配置-->
	<select id="JRQD_PA_queryConstantsInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select A.CONSTANTS_ID,
				        A.CONSTANTS_KEY,
				        A.CONSTANTS_VALUE,
				        A.CONSTANTS_DESC,
				        A.SUB_ID
				   FROM APP___PAS__DBUSER.T_CONSTANTS_INFO A
				  WHERE A.CONSTANTS_KEY = #{is_save_price}
        ]]>		 
	</select>
</mapper>
