<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="survialAnnuityDaoImpl">

	<!--leihong 批处理抽档条件 -->
	<sql id="JRQD_extractPayPlanCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<!-- 保单号 -->
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>
		<!-- 批处理执行日期 -->
		<if test=" batch_date  != null and pre_ext_days !=null "><![CDATA[ AND A.PAY_DUE_DATE  <= #{batch_date,jdbcType=DATE} + #{pre_ext_days} ]]></if>		
		<if test=" pay_status != null and pay_status != ''  "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
		<if test=" plan_id  != null"><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	
		<choose>		
			<!-- 生存金、理赔金转年金、满期金转年金 -->
			<when test='pay_plan_type == "3" '  >
				AND A.PAY_PLAN_TYPE in ('3','10','11')
			</when>
			<when test=' pay_plan_type == "4" '>
				AND A.PAY_PLAN_TYPE = #{pay_plan_type}
				AND EXISTS (SELECT '1' FROM DEV_PAS.T_CONTRACT_EXTEND TCE WHERE TCE.ITEM_ID = A.ITEM_ID AND TCE.PREM_STATUS = '2')
			</when>			
		</choose>
		
		<if test="policyId_list  != null and policyId_list.size()!=0">
		<![CDATA[ AND A.POLICY_ID in ]]>
			<foreach collection ="policyId_list" item="policyId_list" index="index" open="(" close=")" separator=",">#{policyId_list}</foreach>
		</if>
		
		<!-- 保单管理机构 -->
		<if test=" organ_code  != null and organ_code !='' ">
			<![CDATA[ AND  B.ORGAN_CODE in (
				SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T 
					START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
			) ]]>
		</if>
		
	</sql>
	
	<!-- leihong 批处理查询 总记录数 -->
	<select id="JRQD_extractPayPlanCounts" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(A.PLAN_ID) AS END_NUM
      FROM APP___PAS__DBUSER.T_PAY_PLAN A
     INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
        ON A.POLICY_ID = B.POLICY_ID
     INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
        ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
       WHERE (CASE
         WHEN A.GURNT_PAY_LIAB = 1011 OR A.PAY_PLAN_TYPE = 10 THEN
          1
           WHEN C.LIABILITY_STATE = '3' AND C.END_CAUSE = '01' THEN
            1
            WHEN C.LIABILITY_STATE = '4' AND C.LAPSE_DATE >= A.PAY_DUE_DATE THEN
              1
         WHEN A.PAY_PLAN_TYPE <> 10 THEN
          C.LIABILITY_STATE
       END) = 1
     	  AND NOT EXISTS
                 (SELECT 'X'
FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                          LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                            ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
                         WHERE B.SUB_ID IN ('068', '067')
               	AND B.LOCK_SERVICE_TYPE = 1
                           AND A.POLICY_CODE = B.POLICY_CODE)
        ]]>
		<include refid="JRQD_extractPayPlanCondition" />
	</select>
	
	<!--leihong  批处理查询数据 记录集-->
	<select id="JRQD_extractPayPlanList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT ROWNUM AS RN,A.* FROM (
					SELECT   
					       B.VALIDATE_DATE,B.APPLY_CODE,B.BRANCH_CODE,B.ORGAN_CODE,B.POLICY_TYPE,B.CHANNEL_TYPE,
					       B.SERVICE_BANK,B.SERVICE_BANK_BRANCH,
					       C.LIABILITY_STATE,C.RENEW_TIMES,C.BUSI_PRD_ID,C.END_CAUSE,C.LAPSE_DATE,
					       
					       D.MATURITY_DATE,D.EXPIRY_DATE,D.PREM_FREQ,D.CHARGE_YEAR,D.CHARGE_PERIOD,D.COVERAGE_PERIOD,D.COVERAGE_YEAR,       
					       D.AMOUNT,D.BONUS_SA,D.TOTAL_PREM_AF,D.INITIAL_DISCNT_PREM_AF,D.UNIT,D.PAY_YEAR ,D.PAY_PERIOD,D.IS_MASTER_ITEM,
					       D.PRODUCT_ID,
					       D.ANNU_PAY_TYPE,
					       D.PAY_FREQ, 
					       D.EXTRA_PREM_AF, 
					       D.LAST_BONUS_DATE,
					       A.PLAN_FREQ,A.ANNUITY_AMOUNT,
					       A.PLAN_ID,A.POLICY_ID,B.POLICY_CODE,A.BUSI_ITEM_ID,A.ITEM_ID,A.BUSI_PROD_CODE,A.PRODUCT_CODE,A.LIAB_ID,
					       A.LIAB_NAME,A.LIAB_CODE,A.PAY_DUE_DATE,A.PAY_NUM,A.BEGIN_DATE,A.END_DATE,A.PAY_STATUS,A.SURVIVAL_MODE,A.PAY_PLAN_TYPE,
					       A.BENE_AMOUNT,A.TOTAL_AMOUNT,A.INSTALMENT_AMOUNT,A.SURVIVAL_INVEST_FLAG,A.SURVIVAL_INVEST_RESULT,
					       A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.GUARANTEE_PERIOD_TYPE,A.ONE_TIME_FLAG,A.PAY_TYPE,A.SURVIVAL_W_MODE,
					       A.MANUAL_EXTRA_DATE,C.HESITATION_PERIOD_DAY,B.RELATION_POLICY_CODE
					  FROM APP___PAS__DBUSER.T_PAY_PLAN A
					 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
					    ON A.POLICY_ID = B.POLICY_ID
					 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
					    ON A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
					 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT D
					    ON A.ITEM_ID = D.ITEM_ID
					   WHERE (CASE
         WHEN A.GURNT_PAY_LIAB = 1011 OR A.PAY_PLAN_TYPE = 10 THEN
          1
           WHEN C.LIABILITY_STATE = '3' AND C.END_CAUSE = '01' THEN
            1
              WHEN C.LIABILITY_STATE = '4' AND C.LAPSE_DATE >= A.PAY_DUE_DATE THEN
              1
         WHEN A.PAY_PLAN_TYPE <> 10 THEN
          C.LIABILITY_STATE
       END) = 1	
        AND NOT EXISTS
                 (SELECT 'X'
				FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                 LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                 ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
                 WHERE B.SUB_ID IN ('068', '067')
               	AND B.LOCK_SERVICE_TYPE = 1
                           AND A.POLICY_CODE = B.POLICY_CODE)		
		]]>
		<include refid="JRQD_extractPayPlanCondition" />
		<if test=" survival_mode != null and survival_mode != ''  "><![CDATA[ AND A.SURVIVAL_MODE = #{survival_mode} ]]></if>
		<![CDATA[ ORDER BY A.PLAN_ID ) A WHERE 1 = 1  ]]>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.POLICY_ID , #{modnum}) = #{start} AND ROWNUM <= 5000 ]]></if>		
	</select>


	<!-- 查询被保人信息 -->
	<select id="JRQD_queryInsuredInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT C.CUSTOMER_ID,C.CUSTOMER_NAME, C.CUSTOMER_BIRTHDAY, B.INSURED_AGE, C.CUSTOMER_GENDER,C.CUSTOMER_CERT_TYPE,C.CUSTOMER_CERTI_CODE,
				   C.LIVE_STATUS,C.DEATH_DATE,A.ORDER_ID,B.RELATION_TO_PH
			   FROM APP___PAS__DBUSER.T_BENEFIT_INSURED A
				 INNER JOIN APP___PAS__DBUSER.T_INSURED_LIST B
				    ON A.INSURED_ID = B.LIST_ID
				 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
				    ON B.CUSTOMER_ID = C.CUSTOMER_ID
				 WHERE A.POLICY_ID = #{policy_id}
				   AND A.BUSI_ITEM_ID = #{busi_item_id}
		]]>		
	</select>
	
	<!-- 查询投保人信息 -->
	<select id="JRQD_queryPolicyHolderInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.COMPANY_NAME,B.CUSTOMER_ID_CODE,B.MOBILE_TEL,B.CUSTOMER_ID,B.CUSTOMER_NAME, B.CUSTOMER_BIRTHDAY, B.CUSTOMER_GENDER,B.CUSTOMER_CERT_TYPE,B.CUSTOMER_CERTI_CODE
			  FROM APP___PAS__DBUSER.T_POLICY_HOLDER A
			 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER B
			    ON A.CUSTOMER_ID = B.CUSTOMER_ID
			 WHERE A.POLICY_ID = #{policy_id}		   
		]]>		
	</select>
	
	<!-- 查询受益人信息 -->
	<select id="JRQD_queryPolicyBenes" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.CUSTOMER_ID,B.CUSTOMER_NAME, B.CUSTOMER_BIRTHDAY, B.CUSTOMER_GENDER,B.CUSTOMER_CERT_TYPE,B.CUSTOMER_CERTI_CODE,
				   A.BENE_TYPE
			  FROM APP___PAS__DBUSER.T_CONTRACT_BENE A
			 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER B
			    ON A.CUSTOMER_ID = B.CUSTOMER_ID
			 WHERE A.POLICY_ID = #{policy_id}
			   AND A.BUSI_ITEM_ID = #{busi_item_id}   
		]]>		
	</select>	
	
	<!-- 查询领取人信息 -->	
	<select id="JRQD_queryPayPlanPayeeInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT  
					A.PAYEE_ACCOUNT_ID,A.PAYEE_ADDRESS_ID,A.PAYEE_RATE,
			        B.CUSTOMER_ID,B.CUSTOMER_NAME, B.CUSTOMER_BIRTHDAY, B.CUSTOMER_GENDER,B.LIVE_STATUS,B.DEATH_DATE,
			          B.CUSTOMER_CERTI_CODE,B.CUSTOMER_ID_CODE,B.CUSTOMER_CERT_TYPE,			
			        C.BANK_CODE,C.BANK_ACCOUNT,C.ACCO_NAME,C.ACCOUNT_STATUS
							  FROM APP___PAS__DBUSER.T_PAY_PLAN_PAYEE A				
							 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER B
							    ON A.CUSTOMER_ID = B.CUSTOMER_ID
			          LEFT JOIN  APP___PAS__DBUSER.T_BANK_ACCOUNT C
			          ON A.PAYEE_ACCOUNT_ID=C.ACCOUNT_ID
							 where a.plan_id = #{plan_id}
		]]>		
	</select>
	
	<!-- 更改计划结束日期 -->
	<update id="JRQD_updatePayPlanInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PAY_PLAN ]]>
		<set>
		<trim suffixOverrides=",">
			<if test=" end_date  != null and end_date !='' "><![CDATA[ END_DATE = #{end_date, jdbcType=DATE} , ]]></if>		
			<if test=" pay_due_date  != null and pay_due_date !='' "><![CDATA[ pay_due_date = #{pay_due_date, jdbcType=DATE} , ]]></if>	     
		    <if test=" pay_status != null and pay_status != ''  "><![CDATA[ PAY_STATUS = #{pay_status, jdbcType=NUMERIC} , ]]></if>
		    <if test=" instalment_amount  != null and instalment_amount  != ''  "><![CDATA[  INSTALMENT_AMOUNT = #{instalment_amount, jdbcType=NUMERIC} , ]]></if>
		    <if test=" total_amount  != null and total_amount!=''  "><![CDATA[ TOTAL_AMOUNT = #{total_amount, jdbcType=NUMERIC} , ]]></if>
		    <if test=" pay_num  != null and pay_num != ''  "><![CDATA[ pay_num = #{pay_num, jdbcType=NUMERIC} , ]]></if>
		    <if test=" survival_invest_flag  != null"><![CDATA[  survival_invest_flag = #{survival_invest_flag, jdbcType=NUMERIC}, ]]></if>
		    <if test=" survival_invest_result  != null"><![CDATA[  SURVIVAL_INVEST_RESULT = #{survival_invest_result, jdbcType=NUMERIC}, ]]></if>
		    <if test=" manual_extra_date  != null and manual_extra_date !='' "><![CDATA[ MANUAL_EXTRA_DATE = #{manual_extra_date, jdbcType=DATE} , ]]></if>
			UPDATE_TIME = SYSDATE , 
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE   PLAN_ID = #{plan_id} ]]>
	</update>
	
	<!-- 领取年龄是否变更 -->
	<select id="JRQD_findIsPayDateChange" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(*)
			  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A, APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
			 WHERE A.ACCEPT_ID = B.ACCEPT_ID
			   AND A.SERVICE_CODE = B.SERVICE_CODE
			   AND A.SERVICE_CODE = 'LC'
			   AND A.ACCEPT_STATUS = 18
			   AND B.POLICY_ID = #{policy_id}
		 ]]>		
	</select>
	
	<!-- 领取年龄是否转增养老金 -->
	<select id="JRQD_findIsTransChange" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(*)
			  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A, APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
			 WHERE A.ACCEPT_ID = B.ACCEPT_ID
			   AND A.SERVICE_CODE = B.SERVICE_CODE
			   AND A.SERVICE_CODE = 'TA'
			   AND A.ACCEPT_STATUS = 18
			   AND B.POLICY_ID = #{policy_id}
		 ]]>		
	</select>
	
	<!-- 是否发生某个保全项 -->
	<select id="JRQD_findIsXXChange" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(POLICY_ID)
			  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A, APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
			 WHERE A.ACCEPT_ID = B.ACCEPT_ID
			   AND A.SERVICE_CODE = B.SERVICE_CODE
			   AND A.ACCEPT_STATUS = 18
			   AND A.SERVICE_CODE = #{service_code}			   
			   AND B.POLICY_ID = #{policy_id}
		 ]]>		
	</select>
	
	<!-- 查询给付责任信息 -->
	<select id="JRQD_queryPayPlanInfo" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[
		 	SELECT A.PLAN_ID,A.POLICY_ID,A.ITEM_ID,A.PAY_DUE_DATE,A.PAY_NUM,A.BEGIN_DATE,A.END_DATE,A.PAY_STATUS,A.PLAN_FREQ,A.TOTAL_AMOUNT,
		 	A.INSTALMENT_AMOUNT,A.MONEY_CODE,A.GUARANTEE_PERIOD_TYPE,A.PAY_PERIOD,A.PAY_YEAR,A.END_PERIOD,A.END_YEAR,A.SURVIVAL_MODE,A.SURVIVAL_W_MODE,A.PAY_TYPE,
		 	A.INSTALMENT_PROPORTION,A.POLICY_CODE,A.BUSI_ITEM_ID,A.BUSI_PROD_CODE,A.PRODUCT_CODE,A.SURVIVAL_INVEST_FLAG,A.SURVIVAL_INVEST_RESULT,
		 	A.LIAB_ID,A.LIAB_NAME,A.PAY_PLAN_TYPE,A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.ONE_TIME_FLAG,A.BENE_AMOUNT,A.LIAB_CODE
			  FROM APP___PAS__DBUSER.T_PAY_PLAN A
			 WHERE 1 = 1 AND ROWNUM<=1
		 ]]>
		 <if test=" pay_due_date  != null and pay_due_date !='' "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		 <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		 <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		 <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>		 
		 <if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
		 <if test=" gurnt_pay_liab  != null "><![CDATA[ AND (A.GURNT_PAY_LIAB = #{gurnt_pay_liab}  OR A.GUARANTEE_PERIOD_TYPE = 'A' OR  A.GUARANTEE_PERIOD_TYPE = 'Y' OR A.GUARANTEE_PERIOD_TYPE = 'W' )]]></if>
		 <if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
	</select>
	
	
	<!-- 查询红利分红日 -->
	<select id="JRQD_findBonusDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT MAX(T.ALLOCATE_DUE_DATE) ALLOCATE_DATE ,  NVL(SUM(T.BONUS_SA), 0) SUM_BONUS_SA
		  FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE T
		 WHERE T.BONUS_ALLOT IN (1, 2, 6)
		   AND T.ALLOCATE_TYPE = '02' 
		   AND T.ITEM_ID = #{item_id}		   
		 ]]>
		 <if test=" start_date  != null "><![CDATA[  AND T.ALLOCATE_DUE_DATE >= #{start_date} ]]></if>
		 <if test=" allocate_date  != null "><![CDATA[  AND T.ALLOCATE_DUE_DATE <= #{allocate_date} ]]></if>  
	</select>
	
	<!-- 查询退保  累计红利保额 -->
	<select id="JRQD_findBonusSaList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT T.ALLOCATE_DATE,T.BONUS_SA,T.ORIGIN_BONUS_SA
		  FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE T
		 WHERE T.BONUS_ALLOT IN (1, 2, 6)
		   AND T.ALLOCATE_TYPE = '02' 
		   AND T.ITEM_ID = #{item_id}
		   AND T.POLICY_ID=#{policy_id}		   
		 ]]>
		 <if test=" start_date  != null "><![CDATA[  AND T.ALLOCATE_DATE >= #{start_date} ]]></if>
		 <if test=" allocate_date  != null "><![CDATA[  AND T.ALLOCATE_DATE > #{allocate_date} ]]></if>  
		 <![CDATA[ ORDER BY T.ALLOCATE_DATE ]]>
	</select>
	
	<update id="JRQD_updateContractProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
			 BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID = #{item_id} ]]>
	</update>
	
	<!-- 含加费的附加险保费 （附加险种下的所有责任总保费之和） -->
	<select id="JRQD_queryAdditionPremAddFee" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT NVL(SUM(A.TOTAL_PREM_AF),0) AS  ALL_PREM_FEE
  				FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
			 WHERE A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
  				AND B.MASTER_BUSI_ITEM_ID = #{busi_item_id}
		]]>		
	</select>
	
	<select id="JRQD_findLastPrice" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[
		 SELECT *
			  FROM (
			SELECT A.PRICING_DATE,A.BID_PRICE,A.OFF_PRICE
				FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE A WHERE 1=1
				 AND A.PRICING_DATE > #{pricing_date}
				 AND A.INVEST_ACCOUNT_CODE = #{invest_account_code}
			ORDER BY  A.PRICING_DATE ASC)
		WHERE ROWNUM <=1			
		 ]]>
	</select>
	
	<!-- 已豁免保费 -->
	<select id="JRQD_queryExemptedPremium" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT NVL(SUM(A.FEE_AMOUNT),0) AS ALL_FEE_AMOUNT
  				FROM APP___PAS__DBUSER.T_PREM A, APP___PAS__DBUSER.T_CONTRACT_PRODUCT B, APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
 			WHERE A.ITEM_ID = B.ITEM_ID
   				AND B.BUSI_ITEM_ID = C.BUSI_ITEM_ID
   				AND A.DUE_TIME >= C.WAIVER_START
   				AND A.DUE_TIME <= C.WAIVER_END
   				AND A.ITEM_ID = #{item_id} 
		]]>		
	</select>
	
	<!-- 查询领取信息 -->
	<select id="JRQD_queryPayDueInfo" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[
		 SELECT * FROM (
		 	SELECT *
			  FROM APP___PAS__DBUSER.T_PAY_DUE A
			 WHERE 1 = 1 
		 ]]>		
		 <if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		 <if test=" pay_due_date  != null "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>		 
		 <if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		 <![CDATA[ ORDER BY PAY_DUE_DATE) T WHERE ROWNUM<=1	 ]]>		
	</select>
	
	
	<!-- 查询已经产生的基本保额对应的金额、红利保额对应金额 -->
	<select id="JRQD_querySurvivalAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT NVL(SUM(B.SUM_ASSURED_PAID), 0) BASIC_AMOUNT,
       			NVL(SUM(B.TOTAL_BONUS), 0) BONUS_AMOUNT
			  FROM APP___PAS__DBUSER.T_PAY_PLAN A, APP___PAS__DBUSER.T_PAY_DUE B
			 WHERE A.PLAN_ID = B.PLAN_ID
			   AND A.POLICY_ID = #{policy_id}
			   AND A.BUSI_ITEM_ID = #{busi_item_id}
			   AND A.ITEM_ID = #{item_id}
			   AND A.PAY_PLAN_TYPE = #{pay_plan_type}
			   AND TRUNC(B.INSERT_TIME) <= #{end, jdbcType=DATE}
			   AND B.FEE_STATUS != '02'
		 ]]>		
	</select>
	
	<!-- 查询续期待收费信息 -->
	<select id="JRQD_queryPremArapInfo" resultType="java.util.Map"
		parameterType="java.util.Map">		
		<![CDATA[
			SELECT *
				  FROM (SELECT  T.*
						  FROM APP___PAS__DBUSER.T_PREM_ARAP T
						 WHERE 1 = 1
						   AND T.FEE_STATUS IN ('00','04')
						   AND T.FEE_TYPE = 'G003010000'
						   AND T.POLICY_CODE = #{policy_code}
						   AND T.BUSI_PROD_CODE = #{busi_prod_code}
						   AND T.PAY_END_DATE <= #{pay_end_date}
						 ORDER BY T.DUE_TIME) T 
			WHERE ROWNUM <= 1
		]]>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="JRQD_queryPayDueCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)
					  FROM APP___PAS__DBUSER.T_PAY_DUE A
					 WHERE A.PLAN_ID =  #{plan_id}
					   AND A.FEE_STATUS != '02'
		]]>		
	</select>
	
	<!-- 部分领取金额 -->
	<select id="JRQD_findPGAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 		
		SELECT NVL(SUM(T.TRANS_AMOUNT), 0) ALL_TRANS_AMOUNT
		  FROM APP___PAS__DBUSER.T_FUND_TRANS T
		 WHERE T.POLICY_ID = #{policy_id}
		 	   AND T.BUSI_ITEM_ID = #{busi_item_id}
		   	   AND T.TRANS_CODE = #{trans_code}
		]]>		
	</select>
	
		<!-- 保全项生效时间查询-->
	<select id="JRQD_findcspolicychangeaccepttime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 		
		SELECT A.SERVICE_CODE,A.ACCEPT_TIME
			  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A, APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
			 WHERE A.ACCEPT_ID = B.ACCEPT_ID
			   AND A.SERVICE_CODE = B.SERVICE_CODE
			   AND A.ACCEPT_STATUS = '18'
			   AND A.SERVICE_CODE = #{service_code}			   
			   AND B.POLICY_ID = #{policy_id}
		]]>		
	</select>
	
	<!-- 查询红利分红日 -->
	<select id="JRQD_findCsBonusDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT MAX(T.ALLOCATE_DUE_DATE) ALLOCATE_DATE , NVL(SUM(T.BONUS_SA), 0) SUM_BONUS_SA
		  FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE T
		 WHERE T.BONUS_ALLOT IN (1, 2, 6)
		   AND T.ALLOCATE_TYPE = '02' 
		   AND T.ITEM_ID = #{item_id}
		 ]]>
		 <if test=" policy_id  != null "><![CDATA[  AND T.POLICY_ID = #{policy_id} ]]></if>
		 <if test=" start_date  != null "><![CDATA[  AND T.ALLOCATE_DUE_DATE >= #{start_date} ]]></if>
		 <if test=" end_date  != null "><![CDATA[  AND T.ALLOCATE_DUE_DATE <= #{end_date} ]]></if>
		 <if test=" allocate_date  != null "><![CDATA[  AND T.ALLOCATE_DATE <= #{allocate_date} ]]></if>  
	</select>
	
	<select id="JRQD_PA_findSurvivalInvestFlag" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		SELECT DISTINCT A.PLAN_ID,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.BUSI_ITEM_ID,
       A.BUSI_PROD_CODE,
       A.ITEM_ID,
       A.PRODUCT_CODE,
       A.LIAB_ID,
       A.LIAB_CODE,
       A.LIAB_NAME,
       A.PAY_DUE_DATE,
       A.PAY_NUM,
       A.BEGIN_DATE,
       A.END_DATE,
       A.PAY_STATUS,
       A.SURVIVAL_MODE,
       A.SURVIVAL_W_MODE,
       A.PAY_PLAN_TYPE,
       A.PLAN_FREQ,
       A.PAY_TYPE,
       A.BENE_AMOUNT,
       A.TOTAL_AMOUNT,
       A.INSTALMENT_PROPORTION,
       A.INSTALMENT_AMOUNT,
       A.ANNUITY_AMOUNT,
       A.MONEY_CODE,
       A.PAY_PERIOD,
       A.PAY_YEAR,
       A.END_PERIOD,
       A.END_YEAR,
       A.SURVIVAL_INVEST_FLAG,
       A.SURVIVAL_INVEST_RESULT,
       A.GURNT_PAY_LIAB,
       A.GURNT_PAY_PERIOD,
       A.ONE_TIME_FLAG,
       A.LAST_EXTRA_DATE,
       A.MANUAL_EXTRA_DATE,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.GUARANTEE_PERIOD_TYPE
  FROM APP___PAS__DBUSER.T_PAY_PLAN A,APP___PAS__DBUSER.T_LIAB_SURVERY_RULE D
 WHERE 1 = 1 AND A.LIAB_CODE = D.LIAB_ID AND D.STATUS = 1
	]]>
	<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</select>
	
	<!-- 已领取的次数 -->
	<select id="JRQD_PA_queryPayDueCountByDate" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)
					  FROM APP___PAS__DBUSER.T_PAY_DUE A
					 WHERE A.PLAN_ID =  #{plan_id}
		]]>	
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" pay_due_date  != null and pay_due_date !='' "><![CDATA[ AND A.PAY_DUE_DATE <= #{pay_due_date, jdbcType=DATE} ]]></if>	
		<if test=" pay_num  != null "><![CDATA[ AND A.PAY_NUM > #{pay_num} ]]></if>
	</select>
	<!-- 已领取的金额 -->
	<select id="JRQD_PA_queryPayDueAmountByDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(FEE_AMOUNT) FEE_AMOUNT
					  FROM APP___PAS__DBUSER.T_PAY_DUE A
					 WHERE A.PLAN_ID =  #{plan_id}
					   AND A.FEE_STATUS = '01'
		]]>	
		<if test=" pay_due_date  != null and pay_due_date !='' "><![CDATA[ AND A.PAY_DUE_DATE <= #{pay_due_date, jdbcType=DATE} ]]></if>	
	</select>
</mapper>