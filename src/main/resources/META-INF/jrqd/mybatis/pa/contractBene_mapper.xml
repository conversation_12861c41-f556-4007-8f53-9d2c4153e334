<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.ContractBeneDaoImpl">

	<sql id="JRQD_PA_contractBeneWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" share_order  != null "><![CDATA[ AND A.SHARE_ORDER = #{share_order} ]]></if>
		<if test=" bene_type  != null "><![CDATA[ AND A.BENE_TYPE = #{bene_type} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" designation != null and designation != ''  "><![CDATA[ AND A.DESIGNATION = #{designation} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" legal_bene  != null "><![CDATA[ AND A.LEGAL_BENE = #{legal_bene} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" share_rate  != null "><![CDATA[ AND A.SHARE_RATE = #{share_rate} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_birthday  != null "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" bene_kind != null and bene_kind != ''  "><![CDATA[ AND A.BENE_KIND = #{bene_kind} ]]></if>
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ A.POLICY_ID = #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
<!-- 		<if test=" agent_relation != null and agent_relation != ''  "><![CDATA[ AND A.AGENT_RELATION = #{agent_relation} ]]></if> -->
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_queryContractBeneByPolicyChgId">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>
	<sql id="JRQD_PA_queryContractBeneByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryContractBeneByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryContractBeneByBeneTypeCondition">
		<if test=" bene_type  != null "><![CDATA[ AND A.BENE_TYPE = #{bene_type} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryContractBeneByShareOrderCondition">
		<if test=" share_order  != null "><![CDATA[ AND A.SHARE_ORDER = #{share_order} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryContractBeneByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	

<!-- 添加操作  含主键生成策略-->
	<insert id="JRQD_PA_addContractBene_cs"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_BENE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_BENE(
				ADDRESS_ID, SHARE_ORDER, INSERT_TIME, BENE_TYPE, PRODUCT_CODE, CUSTOMER_ID, UPDATE_TIME, 
				INSURED_ID, APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, DESIGNATION, LIST_ID, 
				UPDATE_TIMESTAMP, LEGAL_BENE, INSERT_BY, BUSI_ITEM_ID, POLICY_ID, SHARE_RATE,
				CUSTOMER_NAME,CUSTOMER_BIRTHDAY,CUSTOMER_GENDER,CUSTOMER_CERT_TYPE,CUSTOMER_CERTI_CODE,COMPANY_ID,BENE_KIND ,AGENT_RELATION
				 ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{share_order, jdbcType=NUMERIC} , SYSDATE , #{bene_type, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , SYSDATE 
				, #{insured_id, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{designation, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{legal_bene, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{share_rate, jdbcType=NUMERIC}
				, #{customer_name, jdbcType=VARCHAR},#{customer_birthday,jdbcType=DATE},#{customer_gender,jdbcType=NUMERIC},#{customer_cert_type, jdbcType=VARCHAR},#{customer_certi_code, jdbcType=VARCHAR} 
				, #{company_id, jdbcType=NUMERIC } , #{bene_kind, jdbcType=VARCHAR}, #{agent_relation, jdbcType=VARCHAR}
				 ) 
		 ]]>
	</insert>
<!-- 添加操作   -->
	<insert id="JRQD_PA_addContractBene"  useGeneratedKeys="false"  parameterType="java.util.Map">	
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_CONTRACT_BENE__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CONTRACT_BENE(
				ADDRESS_ID, SHARE_ORDER, INSERT_TIME, BENE_TYPE, PRODUCT_CODE, CUSTOMER_ID, UPDATE_TIME,CUSTOMER_NAME, 
				INSURED_ID, APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, DESIGNATION, LIST_ID, 
				UPDATE_TIMESTAMP, LEGAL_BENE, INSERT_BY, BUSI_ITEM_ID, POLICY_ID, SHARE_RATE,
				CUSTOMER_BIRTHDAY,CUSTOMER_GENDER,CUSTOMER_CERT_TYPE,CUSTOMER_CERTI_CODE, BENE_KIND, COMPANY_ID, AGENT_RELATION ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{share_order, jdbcType=NUMERIC} , SYSDATE , #{bene_type, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{customer_name, jdbcType=VARCHAR}
				, #{insured_id, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{designation, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{legal_bene, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{share_rate, jdbcType=NUMERIC} 
				,#{customer_birthday,jdbcType=DATE},#{customer_gender,jdbcType=NUMERIC},#{customer_cert_type, jdbcType=VARCHAR},#{customer_certi_code, jdbcType=VARCHAR}
				,#{bene_kind, jdbcType=VARCHAR}, #{company_id, jdbcType=NUMERIC} , #{agent_relation, jdbcType=VARCHAR}
				) 
		 ]]>
	</insert>
<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteContractBene" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CONTRACT_BENE WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_PA_updateContractBene" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BENE ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    SHARE_ORDER = #{share_order, jdbcType=NUMERIC} ,
		    BENE_TYPE = #{bene_type, jdbcType=NUMERIC} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			DESIGNATION = #{designation, jdbcType=VARCHAR} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    LEGAL_BENE = #{legal_bene, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SHARE_RATE = #{share_rate, jdbcType=NUMERIC} ,
		    CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
			CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
			CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} ,
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
			BENE_KIND = #{bene_kind, jdbcType=VARCHAR} ,
			COMPANY_ID = #{company_id, jdbcType=NUMERIC} ,
			AGENT_RELATION = #{agent_relation, jdbcType=VARCHAR} , 
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

    <!-- 修改操作 -->
	<update id="JRQD_PA_updateContractBeneCertiCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BENE ]]>
		<set>
		<trim suffixOverrides=",">
		    CUSTOMER_CERTI_CODE = #{customer_certi_code} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
	
<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findContractBeneByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE, A.BENE_KIND , 
			A.COMPANY_ID, '1' AS AGENT_RELATION FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryContractBeneByListIdCondition" />
	</select>
	
	<select id="JRQD_PA_findContractBeneByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE , A.BENE_KIND , 
			A.COMPANY_ID, '1' AS AGENT_RELATION  FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryContractBeneByPolicyIdCondition" />
	</select>
	
	<select id="JRQD_PA_findContractBeneByBeneType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE , A.BENE_KIND , 
			A.COMPANY_ID, '1' AS AGENT_RELATION  FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryContractBeneByBeneTypeCondition" />
	</select>
	
	<select id="JRQD_PA_findContractBeneByShareOrder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE , A.BENE_KIND , 
			A.COMPANY_ID, '1' AS AGENT_RELATION  FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryContractBeneByShareOrderCondition" />
	</select>
	
	<select id="JRQD_PA_findContractBeneByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE , A.BENE_KIND , 
			A.COMPANY_ID , '1' AS AGENT_RELATION FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryContractBeneByAddressIdCondition" />
	</select>
	
	<select id="JRQD_PA_findContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE , A.BENE_KIND , 
			A.COMPANY_ID, '1' AS AGENT_RELATION  FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
	</select>
	
	

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE , A.BENE_KIND , 
			A.COMPANY_ID, '1' AS AGENT_RELATION  FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID,A.COMPANY_ID,
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE,A.Customer_Name,A.CUSTOMER_BIRTHDAY,
			A.CUSTOMER_GENDER,A.CUSTOMER_CERT_TYPE,A.CUSTOMER_CERTI_CODE , 
			A.BENE_KIND , A.COMPANY_ID, '1' AS AGENT_RELATION  FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_PA_findContractBeneTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryContractBeneForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.SHARE_ORDER, B.BENE_TYPE, B.PRODUCT_CODE, B.CUSTOMER_ID, 
			B.INSURED_ID, B.APPLY_CODE, B.POLICY_CODE, B.DESIGNATION, B.LIST_ID, 
			B.LEGAL_BENE, B.BUSI_ITEM_ID, B.POLICY_ID, B.SHARE_RATE, A.BENE_KIND , A.COMPANY_ID , AGENT_RELATION FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE , A.BENE_KIND , A.COMPANY_ID , '1' AS AGENT_RELATION  FROM APP___PAS__DBUSER.T_CONTRACT_BENE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!--组合查询被保人及险种信息 -->
	<select id="JRQD_PA_findContractBeneComps" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TC.CUSTOMER_NAME AS INSURED_NAME,TCBP.BUSI_PRD_ID,TCBP.BUSI_PROD_CODE,A.*
  			FROM APP___PAS__DBUSER.T_CONTRACT_BENE A,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,APP___PAS__DBUSER.T_INSURED_LIST TIL,APP___PAS__DBUSER.T_CUSTOMER  TC
 				WHERE A.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
   					AND TIL.LIST_ID = A.INSURED_ID
  					AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID	]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
	</select>
	
	<!-- 查询受益人信息 -->
	<select id="JRQD_PA_findContractBeneInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
			SELECT DISTINCT A.CUSTOMER_ID,A.CUSTOMER_NAME, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_CERTI_CODE, A.DESIGNATION,D.RELATION_NAME RELATION_TO_INSURED,
			A.SHARE_ORDER, A.SHARE_RATE, A.INSURED_ID, A.CUSTOMER_GENDER,E.GENDER_DESC, A.CUSTOMER_BIRTHDAY, F.TYPE_NAME BENE_TYPE_NAME,
			(SELECT 1 FROM APP___PAS__DBUSER.T_CONTRACT_BENE C WHERE C.POLICY_CODE IN
			(SELECT B.POLICY_CODE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B WHERE B.LIABILITY_STATE <>'4' AND B.POLICY_CODE = A.POLICY_CODE)AND ROWNUM = 1) AVAILABLESTATECODE,
			(SELECT C.CUSTOMER_ID FROM APP___PAS__DBUSER.T_CUSTOMER C WHERE C.CUSTOMER_ID =
			(SELECT B.CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST B WHERE B.LIST_ID = A.INSURED_ID AND ROWNUM =1)) InsuredNo , 
			(SELECT C.CUSTOMER_ID
                   FROM APP___PAS__DBUSER.T_CUSTOMER C
                  WHERE C.CUSTOMER_ID = (SELECT B.CUSTOMER_ID
                                           FROM APP___PAS__DBUSER.T_INSURED_LIST B
                                          WHERE B.LIST_ID = A.INSURED_ID
                                            AND ROWNUM = 1)
                    AND ROWNUM = 1) CUSTOMER_ID
			FROM APP___PAS__DBUSER.T_CONTRACT_BENE A
			LEFT OUTER JOIN APP___PAS__DBUSER.T_LA_PH_RELA D ON A.DESIGNATION=D.RELATION_CODE 
			LEFT OUTER JOIN APP___PAS__DBUSER.T_GENDER E ON A.CUSTOMER_GENDER=E.GENDER_CODE 
			LEFT OUTER JOIN APP___PAS__DBUSER.T_BENEFICIARY_TYPE F ON A.BENE_TYPE=F.BENEFICIARY_TYPE 
          	WHERE 1=1
          	]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
	</select>
	<!-- 查询受益人信息 -->
	<select id="JRQD_PA_findContractBeneInfoForBC" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT A.CUSTOMER_ID,A.CUSTOMER_NAME, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_CERTI_CODE, A.DESIGNATION,D.RELATION_NAME RELATION_TO_INSURED,
       A.SHARE_ORDER, A.SHARE_RATE, A.INSURED_ID, A.CUSTOMER_GENDER,E.GENDER_DESC, A.CUSTOMER_BIRTHDAY, F.TYPE_NAME BENE_TYPE_NAME
FROM APP___PAS__DBUSER.T_CS_CONTRACT_BENE A
LEFT OUTER JOIN APP___PAS__DBUSER.T_LA_PH_RELA D ON A.DESIGNATION=D.RELATION_CODE 
LEFT OUTER JOIN APP___PAS__DBUSER.T_GENDER E ON A.CUSTOMER_GENDER=E.GENDER_CODE 
LEFT OUTER JOIN APP___PAS__DBUSER.T_BENEFICIARY_TYPE F ON A.BENE_TYPE=F.BENEFICIARY_TYPE 
          WHERE 1=1]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
		<include refid="JRQD_PA_queryContractBeneByPolicyChgId" />
	</select>
	<!--再保险 受益人查询-->
	<select id="JRQD_findNameInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
select distinct cb.product_code , bp.product_name_sys   ,p.prem ,p.amount from    APP___PAS__DBUSER.T_contract_bene cb ,APP___PAS__DBUSER.T_contract_master m, APP___PAS__DBUSER.T_business_product bp,
 (select sum(t.total_prem_af) as prem , sum(t.amount) as amount from  ( select cp.* from APP___PAS__DBUSER.T_contract_product cp where cp.policy_code = #{policy_code} ) t ,APP___PAS__DBUSER.T_contract_bene cb where 
cb.policy_id = t.policy_id) p
where 
    cb.product_code = bp.product_code_sys  and cb.policy_id = m.policy_id and m.policy_code= #{policy_code}
	]]>
	</select>
	
	<!--再保险 受益人查询-->
	<select id="JRQD_findBeiefitInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 select distinct  cb.bene_type ,cb.designation , cb.share_order ,cb.share_rate ,c.customer_gender ,
               c.customer_cert_type,c.customer_certi_code, c.customer_name ,c1.customer_id as insuredno , c1.customer_name as name
      from APP___PAS__DBUSER.T_contract_bene cb , APP___PAS__DBUSER.T_contract_master m,APP___PAS__DBUSER.T_customer c ,APP___PAS__DBUSER.T_insured_list i ,APP___PAS__DBUSER.T_customer c1
  where cb.policy_id = m.policy_id and m.policy_code =#{policy_code} and c.customer_id = cb.customer_id and i.policy_code = #{policy_code} and i.customer_id = c1.customer_id
	 ]]>
	</select>
	
	<!-- 查询保单受益人信息接口 -->
	<select id="JRQD_PA_queryContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT C.CUSTOMER_NAME,C.CUSTOMER_CERT_TYPE,C.COUNTRY_CODE,
		C.CUSTOMER_CERTI_CODE,B.SHARE_ORDER,B.SHARE_RATE * 100 || '%' as SHARE_RATES,
		C.CUSTOMER_GENDER,C.CUSTOMER_BIRTHDAY,C.CUST_CERT_STAR_DATE,C.CUST_CERT_END_DATE 
 		FROM APP___PAS__DBUSER.T_CONTRACT_BENE B LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER C ON B.CUSTOMER_ID=C.CUSTOMER_ID WHERE 
 		B.POLICY_CODE=#{policy_code} ]]>
	</select>

	<select id="JRQD_findAllContractBeneNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT CM.POLICY_ID,
		       CM.POLICY_CODE,
		       CM.LIABILITY_STATE,
		       CM.VALIDATE_DATE,
		       CM.END_CAUSE,
		       CB.CUSTOMER_ID,
		       CM.WINNING_START_FLAG,
		       PA.ACKNOWLEDGE_DATE
		  FROM APP___PAS__DBUSER.T_CONTRACT_BENE CB
		 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		    ON CB.POLICY_ID = CM.POLICY_ID
		 INNER JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT PA
		    ON CB.POLICY_ID = PA.POLICY_ID 
		 WHERE 1=1 AND CB.BENE_TYPE=0
		 ]]>
		 <if test=" customer_id  != null "><![CDATA[ AND CB.CUSTOMER_ID = ${customer_id} ]]></if>
		 <if test=" policy_code  != null "><![CDATA[ AND CB.POLICY_CODE = ${policy_code} ]]></if>
		 <![CDATA[ ORDER BY CB.POLICY_CODE ]]> 
	</select>
	
	<!-- 查询被保人下身故受益人，只查询主险的 -->
    <select id="JRQD_findMainContractBene" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[  SELECT   TCU.CUSTOMER_CERTI_CODE,
			       TCU.CUSTOMER_GENDER,
			       TCU.CUSTOMER_BIRTHDAY,
			       TCU.CUST_CERT_STAR_DATE,
			       TCU.CUST_CERT_END_DATE,			      
			       TCU.CUSTOMER_CERT_TYPE,
			       TCU.JOB_CODE,
			       TCU.CUSTOMER_NAME,
			       TCU.CUSTOMER_ID,
			       TCU.OLD_CUSTOMER_ID,
			       TCU.COUNTRY_CODE,			       
			       TCB.SHARE_ORDER,
			       TCB.SHARE_RATE,
			       TCB.DESIGNATION,			       
			       TIL.RELATION_TO_PH,
			       TCU.MOBILE_TEL,
			       B.FIXED_TEL,
			       B.STATE,
			       B.CITY,
			       B.DISTRICT,
			       B.ADDRESS,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.STATE = O.CODE) AS STATENAME,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.CITY = O.CODE) AS CITYNAME,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.DISTRICT = O.CODE) AS DISTRICTNAME,
			       (SELECT O.COUNTRY_NAME
			          FROM APP___PAS__DBUSER.T_COUNTRY O
			         WHERE O.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			       (SELECT O.TYPE
			          FROM APP___PAS__DBUSER.T_CERTI_TYPE O
			         WHERE O.CODE = TCU.CUSTOMER_CERTI_CODE) CERT_TYPE_NAME
			
			  FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BENE TCB
				    ON TIL.LIST_ID = TCB.INSURED_ID
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
				    ON TCB.POLICY_ID = TCBP.POLICY_ID
				   AND TCB.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TCU
				    ON TCB.CUSTOMER_ID = TCU.CUSTOMER_ID
				  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS B
				    ON TCB.ADDRESS_ID = B.ADDRESS_ID
				 WHERE TCBP.MASTER_BUSI_ITEM_ID IS NULL
				 	AND TCBP.LIABILITY_STATE <> '3'
				   AND TCB.BENE_TYPE = '1' /*身故受益人*/
			   AND TIL.POLICY_CODE = #{policy_code}
			   ]]> 
    </select>
	<!-- 移动保全2.0 年金满期金领取查询 -->
	<select id="JRQD_PA_findAGAllContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT  A.CUSTOMER_ID, a.Customer_Name, a.CUSTOMER_GENDER,A.DESIGNATION,a.bene_type
			 FROM APP___PAS__DBUSER.T_CONTRACT_BENE A where 1=1   ]]>
		<include refid="JRQD_PA_contractBeneWhereCondition" />
	</select>
	<!-- 查询被保人下身故受益人，只查询主险的，并且包括任何状态的 -->
    <select id="JRQD_findMainContractBeneInfos" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[  SELECT   TCU.CUSTOMER_CERTI_CODE,
			       TCU.CUSTOMER_GENDER,
			       TCU.CUSTOMER_BIRTHDAY,
			       TCU.CUST_CERT_STAR_DATE,
			       TCU.CUST_CERT_END_DATE,			      
			       TCU.CUSTOMER_CERT_TYPE,
			       TCU.JOB_CODE,
			       TCU.CUSTOMER_NAME,
			       TCU.CUSTOMER_ID,
			       TCU.OLD_CUSTOMER_ID,
			       TCU.COUNTRY_CODE,			       
			       TCB.SHARE_ORDER,
			       TCB.SHARE_RATE,
			       TCB.DESIGNATION,			       
			       TIL.RELATION_TO_PH,
			       TCU.MOBILE_TEL,
			       B.FIXED_TEL,
			       B.STATE,
			       B.CITY,
			       B.DISTRICT,
			       B.ADDRESS,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.STATE = O.CODE) AS STATENAME,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.CITY = O.CODE) AS CITYNAME,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.DISTRICT = O.CODE) AS DISTRICTNAME,
			       (SELECT O.COUNTRY_NAME
			          FROM APP___PAS__DBUSER.T_COUNTRY O
			         WHERE O.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			       (SELECT O.TYPE
			          FROM APP___PAS__DBUSER.T_CERTI_TYPE O
			         WHERE O.CODE = TCU.CUSTOMER_CERTI_CODE) CERT_TYPE_NAME
			
			  FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BENE TCB
				    ON TIL.LIST_ID = TCB.INSURED_ID
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
				    ON TCB.POLICY_ID = TCBP.POLICY_ID
				   AND TCB.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TCU
				    ON TCB.CUSTOMER_ID = TCU.CUSTOMER_ID
				  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS B
				    ON TCB.ADDRESS_ID = B.ADDRESS_ID
				 WHERE TCBP.MASTER_BUSI_ITEM_ID IS NULL
				   AND TCB.BENE_TYPE = '1' /*身故受益人*/
			   AND TIL.POLICY_CODE = #{policy_code}
			   ]]> 
    </select>
    
    <!-- 查询保单下所有身故受益人信息 -->
    <select id="JRQD_findALLContractBeneinfo" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[  SELECT   TCU.CUSTOMER_CERTI_CODE,
			       TCU.CUSTOMER_GENDER,
			       TCU.CUSTOMER_BIRTHDAY,
			       TCU.CUST_CERT_STAR_DATE,
			       TCU.CUST_CERT_END_DATE,			      
			       TCU.CUSTOMER_CERT_TYPE,
			       TCU.JOB_CODE,
			       TCU.CUSTOMER_NAME,
			       TCU.CUSTOMER_ID,
			       TCU.OLD_CUSTOMER_ID,
			       TCU.COUNTRY_CODE,			       
			       TCB.SHARE_ORDER,
			       TCB.SHARE_RATE,
			       TCB.DESIGNATION,			       
			       TIL.RELATION_TO_PH,
			       TCU.MOBILE_TEL,
			       B.FIXED_TEL,
			       B.STATE,
			       B.CITY,
			       B.DISTRICT,
			       B.POST_CODE,
			       B.ADDRESS,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.STATE = O.CODE) AS STATENAME,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.CITY = O.CODE) AS CITYNAME,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.DISTRICT = O.CODE) AS DISTRICTNAME,
			       (SELECT O.COUNTRY_NAME
			          FROM APP___PAS__DBUSER.T_COUNTRY O
			         WHERE O.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			       (SELECT O.TYPE
			          FROM APP___PAS__DBUSER.T_CERTI_TYPE O
			         WHERE O.CODE = TCU.CUSTOMER_CERTI_CODE) CERT_TYPE_NAME
			
			  FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BENE TCB
				    ON TIL.LIST_ID = TCB.INSURED_ID
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
				    ON TCB.POLICY_ID = TCBP.POLICY_ID
				   AND TCB.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TCU
				    ON TCB.CUSTOMER_ID = TCU.CUSTOMER_ID
				  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS B
				    ON TCB.ADDRESS_ID = B.ADDRESS_ID
				 WHERE 1=1
				 	AND TCBP.LIABILITY_STATE <> '3'
				    AND TCB.BENE_TYPE = '1' /*身故受益人*/
			   		AND TIL.POLICY_CODE = #{policy_code}
			   ]]> 
    </select>
</mapper>
