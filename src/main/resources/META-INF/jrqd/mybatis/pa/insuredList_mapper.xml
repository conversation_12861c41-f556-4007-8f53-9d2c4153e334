<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.InsuredListDaoImpl">

	<sql id="JRQD_PA_insuredListWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" stand_life  != null "><![CDATA[ AND A.STAND_LIFE = #{stand_life} ]]></if>
		<if test=" smoking  != null "><![CDATA[ AND A.SMOKING = #{smoking} ]]></if>
		<if test=" soci_secu  != null "><![CDATA[ AND A.SOCI_SECU = #{soci_secu} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" insured_age  != null "><![CDATA[ AND A.INSURED_AGE = #{insured_age} ]]></if>
		<if test=" job_underwrite != null and job_underwrite != ''  "><![CDATA[ AND A.JOB_UNDERWRITE = #{job_underwrite} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ and A.list_id in (select tbi.insured_id from APP___PAS__DBUSER.T_BENEFIT_INSURED tbi where tbi.busi_item_id = #{busi_item_id} ) ]]></if>
		<if test=" order_id  != null "><![CDATA[ and A.list_id in (select tbi.insured_id from APP___PAS__DBUSER.T_BENEFIT_INSURED tbi where tbi.order_id = #{order_id} ) ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_ID in (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
<!-- 		<if test=" agent_relation != null and agent_relation != ''  "><![CDATA[ AND A.AGENT_RELATION = #{agent_relation} ]]></if> -->
	</sql>


<!-- 按索引生成的查询条件 -->
	<sql id="JRQD_PA_queryInsuredListByListIdCondition">
		<if test=" list_id  != null and list_id !='' "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
	<sql id="JRQD_PA_queryInsuredListByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="JRQD_PA_queryInsuredListByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>
	<sql id="JRQD_PA_queryInsuredListByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	<sql id="JRQD_PA_queryInsuredListByPolicyCodeCondition">
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

<!-- 添加操作 -->
	<insert id="JRQD_PA_addInsuredList"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_INSURED_LIST__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_INSURED_LIST(
				ADDRESS_ID, STAND_LIFE,  SMOKING, INSERT_TIME, CUSTOMER_ID, CUSTOMER_HEIGHT, 
				UPDATE_TIME, JOB_CODE, RELATION_TO_PH, CUSTOMER_WEIGHT, APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY, INSURED_AGE, JOB_UNDERWRITE, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, SOCI_SECU,
				ANNUAL_INCOME_CEIL,INCOME_SOURCE,RESIDENT_TYPE ,AGENT_RELATION  ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{stand_life, jdbcType=NUMERIC} ,  #{smoking, jdbcType=NUMERIC} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , #{customer_height, jdbcType=NUMERIC} 
				, SYSDATE , #{job_code, jdbcType=VARCHAR} , #{relation_to_ph, jdbcType=VARCHAR} , #{customer_weight, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{insured_age, jdbcType=NUMERIC} , #{job_underwrite, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}, #{soci_secu, jdbcType=NUMERIC}
				, #{annual_income_ceil, jdbcType=NUMERIC}, #{income_source, jdbcType=VARCHAR}, #{resident_type, jdbcType=VARCHAR} , #{agent_relation, jdbcType=VARCHAR} )  
		 ]]>
	</insert>

<!-- 删除操作 -->
	<delete id="JRQD_PA_deleteInsuredList" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_INSURED_LIST WHERE LIST_ID=#{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_PA_updateInsuredList" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_INSURED_LIST ]]>
		<set>
		<trim suffixOverrides=",">
		   ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    STAND_LIFE = #{stand_life, jdbcType=NUMERIC} ,
		    SMOKING = #{smoking, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE ,
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			RELATION_TO_PH = #{relation_to_ph, jdbcType=VARCHAR} ,
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    INSURED_AGE = #{insured_age, jdbcType=NUMERIC} ,
			JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
		    ANNUAL_INCOME_CEIL = #{annual_income_ceil, jdbcType=NUMERIC} ,
		    INCOME_SOURCE = #{income_source, jdbcType=VARCHAR} ,
		    RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR} ,
		    AGENT_RELATION = #{agent_relation, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->
	<select id="JRQD_PA_findInsuredListByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1   ]]>
		<include refid="JRQD_PA_queryInsuredListByListIdCondition" />
	</select>

	<select id="JRQD_PA_findInsuredListByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1   ]]>
		<include refid="JRQD_PA_queryInsuredListByPolicyIdCondition" />
	</select>

	<select id="JRQD_PA_findInsuredListByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1   ]]>
		<include refid="JRQD_PA_queryInsuredListByAddressIdCondition" />
	</select>


<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_insuredListWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_PA_findInsuredListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_insuredListWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryInsuredListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.STAND_LIFE, B.SMOKING, B.CUSTOMER_ID, B.CUSTOMER_HEIGHT,
			B.JOB_CODE, B.RELATION_TO_PH, B.CUSTOMER_WEIGHT, B.APPLY_CODE, B.POLICY_CODE, B.ANNUAL_INCOME_CEIL,B.INCOME_SOURCE,B.RESIDENT_TYPE,AGENT_RELATION,
			B.INSURED_AGE, B.JOB_UNDERWRITE, B.LIST_ID, B.SOCI_SECU, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<select id="JRQD_PA_findInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_insuredListWhereCondition" />
	</select>
	<!-- 为了查询红利手工配置的cusromerid -->
	<select id="JRQD_PA_findInsuredListCusromerID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryInsuredListByListIdCondition"></include>
		<!--<include refid="JRQD_PA_queryInsuredListByCustomerIdCondition" />
		<include refid="JRQD_PA_queryInsuredListByListIdCondition" />
		<include refid="JRQD_PA_insuredListWhereCondition" />-->
	</select>

	<select id="JRQD_PA_findInsuredListByIdAndPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryInsuredListByIdAndPolicy"/>
		</select>
	<sql id="JRQD_PA_queryInsuredListByIdAndPolicy">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>

	<select id="JRQD_queryCustomerMessageByPolicyCode1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID,
			A.CUSTOMER_HEIGHT,A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE,
			A.POLICY_CODE,A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU,0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,'1' AS AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE ROWNUM = 1  ]]>
		<include refid="JRQD_PA_queryInsuredListByIdAndPolicy"/>
	</select>


	<!-- R00101000014保单被保人信息查询 -->
	<!-- Modify by yangbo_wb  2016-08-02  Fix TC 缺陷ID-8726：e.RELATION_NAME 重命名为 RELATION_NAME_INSURED
		2016-08-05  Fix TC 缺陷ID-8726：T_CUSTOMER.JOB_CODE 和T_JOB_CATEGORY.JOB_CATEGORY_CODE无法关联，关联T_JOB_CODE.JOB_CODE，取值T_JOB_CODE.JOB_NAME  -->
	<select id="JRQD_PA_findAllInsuredInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		         SELECT A.CUSTOMER_ID,
			       (SELECT B.ORDER_ID
			          FROM APP___PAS__DBUSER.T_BENEFIT_INSURED B
			         WHERE A.LIST_ID = B.INSURED_ID AND B.POLICY_ID=A.POLICY_ID
			           AND ROWNUM = 1) AS ORDER_ID,
			       C.CUSTOMER_NAME,
			       C.CUSTOMER_BIRTHDAY,
			       C.CUSTOMER_GENDER,
			       C.OLD_CUSTOMER_ID,
			       (SELECT  F.GRADE_NAME FROM   APP___PAS__DBUSER.T_CUST_GRADE F   WHERE C.CUSTOMER_LEVEL = F.CUST_GRADE )GRADE_NAME,
			       C.CUSTOMER_LEVEL,
			       C.CUSTOMER_VIP,
			       C.CUSTOMER_CERT_TYPE,
			       (SELECT G.TYPE FROM  APP___PAS__DBUSER.T_CERTI_TYPE G WHERE C.CUSTOMER_CERT_TYPE = G.CODE)TYPE,
			       C.CUSTOMER_CERTI_CODE,
			       C.UN_CUSTOMER_CODE,
			       C.COUNTRY_CODE,
			       (SELECT H.COUNTRY_NAME FROM  APP___PAS__DBUSER.T_COUNTRY H  WHERE C.COUNTRY_CODE = H.COUNTRY_CODE)COUNTRY_NAME,
			       C.DRIVER_LICENSE_TYPE,
			       (SELECT  I.LICENSE_DESC FROM  APP___PAS__DBUSER.T_LICENSE_TYPE I WHERE C.DRIVER_LICENSE_TYPE = I.LICENSE_TYPE)LICENSE_DESC,
			       A.JOB_CODE,
			       (SELECT J.JOB_NAME FROM  APP___PAS__DBUSER.T_JOB_CODE J  WHERE C.JOB_CODE = J.JOB_CODE)JOB_NAME,
			       (SELECT  Q.JOB_UW_LEVEL_NAME FROM  APP___PAS__DBUSER.T_JOB_UNDERWRITE Q WHERE A.JOB_UNDERWRITE = Q.JOB_UW_LEVEL_CODE)JOB_UW_LEVEL_NAME,
			       D.MOBILE_TEL,
			       D.OFFICE_TEL,
			       C.OFFEN_USE_TEL,
			       D.FAX_TEL,
			       D.HOUSE_TEL,
			       D.FIXED_TEL,
			       D.ADDRESS,
			       D.EMAIL,
			       D.POST_CODE,
			       C.COMPANY_NAME,
			       C.MARRIAGE_STATUS,
			       (SELECT M.MARRIAGE FROM  APP___PAS__DBUSER.T_MARRIAGE M WHERE C.MARRIAGE_STATUS = M.MARRIAGE_CODE)MARRIAGE,
			       L.ORGAN_CODE,
			       L.BRANCH_CODE,
			       D.STATE,
			       (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE D.STATE = O.CODE )  AS STATENAME,
			       D.CITY,
			       (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE D.CITY = O.CODE )  AS CITYNAME,
			       D.DISTRICT,
			       (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE D.DISTRICT = O.CODE )  AS DISTRICTNAME,
			       D.ADDRESS AS STREET,
			       C.JOB_TITLE,
			       A.JOB_UNDERWRITE,
			       (SELECT B.RELATION_TO_INSURED_1 FROM  APP___PAS__DBUSER.T_BENEFIT_INSURED B WHERE A.LIST_ID = B.INSURED_ID AND B.POLICY_ID=A.POLICY_ID
                   AND ROWNUM = 1)  AS RELATION_TO_INSURED_1,
                   (SELECT  E.RELATION_NAME FROM  APP___PAS__DBUSER.T_LA_PH_RELA E WHERE E.RELATION_CODE = (SELECT B.RELATION_TO_INSURED_1 FROM  APP___PAS__DBUSER.T_BENEFIT_INSURED B WHERE A.LIST_ID = B.INSURED_ID AND B.POLICY_ID=A.POLICY_ID
                   AND ROWNUM = 1))RELATION_NAME_INSURED_1,
			       A.RELATION_TO_PH,
			       (SELECT  E.RELATION_NAME FROM  APP___PAS__DBUSER.T_LA_PH_RELA E WHERE E.RELATION_CODE =A.RELATION_TO_PH)RELATION_NAME,
			       PA.PAY_NEXT,
			       PA.NEXT_ACCOUNT_NAME,
			       PA.PAY_MODE,
			       (SELECT  TB.BANK_NAME FROM  APP___PAS__DBUSER.T_BANK TB  WHERE PA.NEXT_ACCOUNT_BANK = TB.BANK_CODE)BANK_NAME,
			       PA.NEXT_ACCOUNT,
			       C.CUST_CERT_STAR_DATE,
			       C.CUST_CERT_END_DATE,
			       A.SOCI_SECU,
			       PA.PAY_LOCATION,
			       PA.NEXT_ACCOUNT_BANK
			  FROM APP___PAS__DBUSER.T_INSURED_LIST A
			  LEFT OUTER JOIN APP___PAS__DBUSER.T_CUSTOMER C
			    ON A.CUSTOMER_ID = C.CUSTOMER_ID
			  LEFT OUTER JOIN APP___PAS__DBUSER.T_ADDRESS D
			    ON C.CUSTOMER_ID = D.CUSTOMER_ID
			   AND A.ADDRESS_ID = D.ADDRESS_ID
			  LEFT OUTER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER L
			    ON A.POLICY_ID = L.POLICY_ID
			  LEFT OUTER JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT PA
			    ON L.POLICY_ID = PA.POLICY_ID
			 WHERE 1 = 1
        ]]>
			<include refid="JRQD_PA_insuredListWhereCondition"/>
			ORDER BY ORDER_ID ASC
	</select>

	<select id="JRQD_PA_findCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			 select c.policy_id,
             CU.Customer_Id,
             CU.CUST_CERT_STAR_DATE,
             CU.CUST_CERT_END_DATE
        FROM APP___PAS__DBUSER.T_INSURED_LIST C
        left join APP___PAS__DBUSER.T_CUSTOMER CU
          on C.CUSTOMER_ID = CU.Customer_Id
       	WHERE C.POLICY_CODE = #{policy_code}
		]]>
		<if test=" oy1  != null and oy1!=''">
						${oy1}
		</if>
	</select>

	<!-- 查询被保险人的信息 -->
	<select id="JRQD_queryCustomerMessageByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
					select c.customer_name as name,
				          c.customer_gender as sex,
				          c.customer_cert_type as idType,
				          c.customer_certi_code as idNo,
				          c.cust_cert_star_date as idStartDate,
				          c.cust_cert_end_date as idEndDate,
				          (select d.address
				             from APP___PAS__DBUSER.T_address d
				            where d.address_id = i.address_id) as postaddress,
				          (select sheng.name || shi.name || xian.name
				             from dev_pas.t_district sheng
				             left join dev_pas.t_district shi
				               on shi.parent_code = sheng.code
				             left join dev_pas.t_district xian
				               on xian.parent_code = shi.code
				            where xian.code =
				                  (select d.district
				                     from APP___PAS__DBUSER.T_address d
				                    where d.address_id = i.address_id)) as ssx,
				          c.country_code as nativePlace,
				          c.customer_birthday as birthday,
				          c.job_code as occupationCode,
				          i.job_underwrite as occupationType,
				          (select d.mobile_tel
				             from APP___PAS__DBUSER.T_address d
				            where d.address_id = i.address_id) as commmethod,
				          i.relation_to_ph
				     from APP___PAS__DBUSER.T_Insured_list i
				     left join APP___PAS__DBUSER.T_customer c
				       on c.customer_id = i.customer_id
				    where i.policy_code = #{policy_code}
		]]>
	</select>

	<!-- 根据保单号和客户号查询被保人 -->
	<select id="JRQD_findCustomerTypeByPoCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select customer_id from APP___PAS__DBUSER.T_INSURED_LIST
			where policy_code=#{policy_code}
		]]>
		<if test=" customer_id  != null "><![CDATA[ AND customer_id=#{customer_id} ]]></if>
	</select>
	<!-- 根据保单号查询投保人/被保人客户id -->
	<select id="JRQD_findCustomerIdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select h.customer_id,c.customer_name,h.policy_id from APP___PAS__DBUSER.T_POLICY_HOLDER h left join APP___PAS__DBUSER.T_CUSTOMER c on h.customer_id=c.customer_id
			where h.policy_code=#{policy_code} union
			select l.customer_id,c.customer_name,l.policy_id from APP___PAS__DBUSER.T_INSURED_LIST l left join APP___PAS__DBUSER.T_CUSTOMER c on l.customer_id=c.customer_id
			where l.policy_code=#{policy_code}
		]]>
	</select>


	<select id="JRQD_findInsuredInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select distinct c.customer_id , c.customer_name,c.customer_birthday,c.customer_id_code,c.customer_cert_type, c.customer_certi_code
		  from APP___PAS__DBUSER.T_customer c ,(select i.* from APP___PAS__DBUSER.T_insured_list i where i.policy_code = #{policy_code}) t where c.customer_id = t.customer_id

		]]>
	</select>

	<!-- 根据保单号查询所有被保人id -->
	<select id="JRQD_PA_findInsuredsByPolicyCodeAndCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select
				i.job_code as occupationcode,
				  i.job_underwrite as occupationtype,
				(select bp.old_pol_no from APP___PAS__DBUSER.T_contract_busi_prod bp , APP___PAS__DBUSER.T_benefit_insured bi where bp.busi_item_id = bi.busi_item_id
				and bp.policy_id = bi.policy_id and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id and rownum=1) as polno,
				(select pro.product_name_sys from APP___PAS__DBUSER.T_contract_busi_prod bp ,APP___PAS__DBUSER.T_business_product pro, APP___PAS__DBUSER.T_benefit_insured bi where bp.busi_item_id = bi.busi_item_id
				and bp.policy_id = bi.policy_id and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id and pro.business_prd_id = bp.busi_prd_id and rownum=1) as riskcodename,
				(select sum(cp.amount) from APP___PAS__DBUSER.T_contract_product cp where exists (select 1  from APP___PAS__DBUSER.T_benefit_insured bi where cp.busi_item_id = bi.busi_item_id
				and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id) and cp.policy_id = i.policy_id ) as amnt,
				(select sum(cp.unit) from APP___PAS__DBUSER.T_contract_product cp where exists (select 1  from APP___PAS__DBUSER.T_benefit_insured bi where cp.busi_item_id = bi.busi_item_id
				and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id) and cp.policy_id = i.policy_id) as unit,
				(select sum(cp.total_prem_af) from APP___PAS__DBUSER.T_contract_product cp where exists (select 1  from APP___PAS__DBUSER.T_benefit_insured bi where cp.busi_item_id = bi.busi_item_id
				 and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id) and cp.policy_id = i.policy_id) as prem,
				(select sum(cp.initial_extra_prem_af) from APP___PAS__DBUSER.T_contract_product cp where exists (select 1  from APP___PAS__DBUSER.T_benefit_insured bi where cp.busi_item_id = bi.busi_item_id
				 and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id) and cp.policy_id = i.policy_id) as occaddprem,
				(select l.counter_way from APP___PAS__DBUSER.T_contract_busi_prod bp ,APP___PAS__DBUSER.t_product_life l where
				         bp.policy_id = i.policy_id and bp.busi_prd_id = l.business_prd_id and rownum=1) countway
				from APP___PAS__DBUSER.T_insured_list i
			where i.policy_code = #{policyCode}
		]]>
		<if test="customerId != null and customerId != '' "><![CDATA[ and i.customer_id = #{customerId}]]></if>
	</select>

	<!-- 通过policyCode 查询 -->
	<select id="JRQD_PA_findInsuredListByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryInsuredListByPolicyCodeCondition"/>
	</select>

	<!-- 通过 customId 查询 -->
	<select id="JRQD_PA_findInsuredListByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT,
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryInsuredListByCustomerIdCondition"/>
	</select>

	<!-- 根据保单信息查询该被保人所有投保保单 -->
	<select id="JRQD_PA_findCountInsuredByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   select policy_code from
		 ( SELECT distinct B.policy_code
		  FROM APP___PAS__DBUSER.T_insured_list B where B.Customer_Id in
		  (SELECT A.customer_id
		          FROM APP___PAS__DBUSER.T_insured_list A
        			 where A.policy_code = #{policy_code} ) )  ]]>
	</select>

	<!-- 根据保单id查询该被保人所有投保保单id -->
	<select id="JRQD_PA_findCountInsuredByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   select policy_id from
		 ( SELECT distinct B.policy_id
		  FROM APP___PAS__DBUSER.t_insured_list B where B.Customer_Id in
		  (SELECT A.customer_id
		          FROM APP___PAS__DBUSER.t_insured_list A
        			 where A.policy_id = #{policy_id} ) )  ]]>
	</select>

	<!--  -->
	<select id="JRQD_PA_findPolicyCodeByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">

	    <![CDATA[
		select tph.policy_code as policy_code
		  from APP___PAS__DBUSER.t_policy_holder tph,
		       APP___PAS__DBUSER.t_insured_list  til
		 where tph.policy_code = til.policy_code
	    ]]>
	    <if test="appCustomer_id != null and appCustomer_id !='' ">
	         <![CDATA[
			   and tph.customer_id = #{appCustomer_id}
	         ]]>
	    </if>
	    <if test="insCustomer_id !=null and insCustomer_id !='' ">
	        <![CDATA[
	        	and til.customer_id = #{insCustomer_id}
	         ]]>
	    </if>
	    <if test="customer_id !=null and customer_id!='' ">
	        <![CDATA[
	        	 and (#{customer_id} in
		       (select t.customer_id
		           from APP___PAS__DBUSER.t_policy_holder t
		          where t.policy_code = tph.policy_code) or
		       #{customer_id} in
		       (select t.customer_id
		           from APP___PAS__DBUSER.t_insured_list t
		          where t.policy_code = til.policy_code))
	        ]]>
	    </if>
	    <if test="fast_query !=null and fast_query!='' ">
	         <![CDATA[
	        	and (tph.customer_id = #{fast_query} or
		       til.customer_id = #{fast_query})
	        ]]>
	    </if>
		 group by tph.policy_code

	</select>

	<!-- 根据保单id查询该被保人所有投保保单id -->
	<select id="JRQD_PA_findInsuredListBySingleJointLife" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select ti.*
			  from APP___PAS__DBUSER.t_benefit_insured tb
			  left join APP___PAS__DBUSER.t_insured_list ti
			    on tb.insured_id = ti.list_id
			 where tb.busi_item_id = #{busi_item_id}
			   and tb.insured_id != #{insured_id}]]>
	</select>


	<!-- 查询保单下的第一被保险人 -->
	<select id="JRQD_PA_findAllFristInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select ti.ADDRESS_ID,
             ti.STAND_LIFE,
             ti.SMOKING,
             ti.CUSTOMER_ID,
             ti.CUSTOMER_HEIGHT,
             ti.JOB_CODE,
             ti.RELATION_TO_PH,
             ti.CUSTOMER_WEIGHT,
             ti.APPLY_CODE,
             ti.INSURED_AGE,
             ti.JOB_UNDERWRITE,
             ti.LIST_ID,
             tb.order_id,
             tb.busi_item_id,
             tb.policy_id,
             tb.policy_code,
             ad.mobile_tel,
             c.customer_name
        from APP___PAS__DBUSER.t_benefit_insured tb
        left join APP___PAS__DBUSER.t_insured_list ti
        on tb.insured_id = ti.list_id
        left join APP___PAS__DBUSER.t_Address ad
          on ad.address_id = ti.address_id
        left join APP___PAS__DBUSER.t_Customer c
        on ti.customer_id=c.customer_id
			 where ti.policy_id = #{policy_id}
			 and tb.order_id = #{order_id}]]>
	</select>
	<!-- 出险人保单查询 --><!-- 老核心查询保单层信息 -->
	<select id="JRQD_PA_queryInsuredPolicyList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select cm.policy_code,
       cm.organ_code,
       cm.BRANCH_CODE,
       cm.validate_date,
       cm.liability_state,
  (select max(pay_due_date) from dev_pas.t_contract_extend t where t.policy_code=cm.policy_code ) as finish_time
    from

APP___PAS__DBUSER.T_CONTRACT_MASTER cm where cm.policy_code in

(select  policy_code from  dev_pas.t_insured_list t1 where t1.customer_id=#{customer_id}
union
select  policy_code from  dev_pas.t_policy_holder t1 where t1.customer_id=#{customer_id} )
		 ]]>
		 <if test=" strSQLa  != null and strSQLa!=''">
						${strSQLa}
		</if>
	</select>


	<update id="JRQD_PA_updateAgeInsuredList" parameterType="java.util.Map">
		<![CDATA[  UPDATE APP___PAS__DBUSER.T_INSURED_LIST
    set insured_age = #{insured_age, jdbcType = NUMERIC},relation_to_ph = #{relation_to_ph, jdbcType=VARCHAR}
  WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- ***************************new********************************************* -->
	<select id="JRQD_findAllInsuredListNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT CM.POLICY_ID,
		       CM.POLICY_CODE,
		       CM.LIABILITY_STATE,
		       CM.VALIDATE_DATE,
		       CM.WINNING_START_FLAG,
		       CM.END_CAUSE,
		       IL.CUSTOMER_ID,
		       PA.ACKNOWLEDGE_DATE
		  FROM APP___PAS__DBUSER.T_INSURED_LIST IL
		 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		    ON IL.POLICY_ID = CM.POLICY_ID
		 INNER JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT PA
		    ON IL.POLICY_ID = PA.POLICY_ID
		 WHERE 1=1
		 ]]>
		 <if test=" customer_id  != null "><![CDATA[ AND IL.CUSTOMER_ID = ${customer_id} ]]></if>
		 <if test=" policy_code  != null "><![CDATA[ AND IL.POLICY_CODE = ${policy_code} ]]></if>
		 <![CDATA[ ORDER BY IL.POLICY_CODE ]]>
	</select>


	<!-- 被保险人职业类别查询接口 sql -->
	<select id="JRQD_PA_queryGetInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select i.job_code as occupationcode,
               i.job_underwrite  as occupationtype,
              (select j.job_uw_level from APP___PAS__DBUSER.t_Job_Code j where j.job_code = d.job_code) as occupationtype1,
              c.busi_item_id,
              c.old_pol_no,c.BUSI_PROD_CODE,
              (select pro.product_name_sys
                 from APP___PAS__DBUSER.T_business_product pro
                where pro.business_prd_id = c.busi_prd_id
                  and rownum = 1) as riskcodename,
              (select sum(cp.amount)
                    from APP___PAS__DBUSER.T_contract_product cp
                where exists (select 1
                         from APP___PAS__DBUSER.T_benefit_insured bi
                        where cp.busi_item_id = bi.busi_item_id
                          and bi.insured_id = i.list_id
                          and bi.policy_id = i.policy_id)
                  and cp.policy_id = i.policy_id and cp.busi_item_id = c.busi_item_id) as amnt,
              (select sum(cp.unit)
                 from APP___PAS__DBUSER.T_contract_product cp
                where cp.busi_item_id = c.busi_item_id  and cp.policy_id = i.policy_id ) as unit,
              (select sum(cp.total_prem_af)
                 from APP___PAS__DBUSER.T_contract_product cp
                where cp.busi_item_id = c.busi_item_id   and cp.policy_id = i.policy_id) as prem,
              (select sum(cp.STD_prem_af)
                    from APP___PAS__DBUSER.T_contract_product cp
                   where cp.busi_item_id = c.busi_item_id   and cp.policy_id = i.policy_id) as prem1,
              (select sum(cp.initial_extra_prem_af)
                 from APP___PAS__DBUSER.T_contract_product cp
                where cp.busi_item_id = c.busi_item_id   and cp.policy_id = i.policy_id) as occaddprem,
              (select l.counter_way
                 from
                      APP___PAS__DBUSER.t_product_life       l
                where c.busi_prd_id = l.business_prd_id
                  and rownum = 1) countway
         from APP___PAS__DBUSER.t_Benefit_Insured    a,
              APP___PAS__DBUSER.t_Insured_List       i,
              APP___PAS__DBUSER.t_Contract_Busi_Prod c,
              APP___PAS__DBUSER.T_CUSTOMER d
        where a.insured_id = i.list_id
          and a.busi_item_id = c.busi_item_id
          and i.policy_code =#{policyCode} and d.customer_id=i.customer_id
          and c.liability_state = '1'
		]]>
		<if test="customerId != null and customerId != '' "><![CDATA[ and i.customer_id  = #{customerId}]]></if>
	</select>

	<!-- 核保查询被保人已承保保单信息 -->
	<select id="JRQD_PA_queryUnderwrite" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,
					   TCM.ORGAN_CODE,
				       TCM.POLICY_CODE,
				       TCM.VALIDATE_DATE,
				       TCM.LIABILITY_STATE,
				       TCM.APPLY_CODE,
				       TCM.DECISION_CODE,
				       TCM.MULTI_MAINRISK_FLAG,
				       TCM.CHANNEL_TYPE,
				       A.CUSTOMER_ID,
				       0 AS ANNUAL_INCOME_CEIL,
				       TCM.SUBMIT_CHANNEL
				  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
				       APP___PAS__DBUSER.T_INSURED_LIST A
				 WHERE TCM.POLICY_ID = A.POLICY_ID  ]]>
		<if test=" customer_Id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_Id} ]]></if>
		<if test=" policy_code  != null "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ORDER BY TCM.VALIDATE_DATE DESC  ]]>
	</select>

	<!-- 根据保单号查客户id -->
	<select id="JRQD_findInsuredListByPolicyCode2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT c.list_id,c.policy_id,c.policy_code,c.apply_code,c.address_id,c.customer_id
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
        WHERE C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          AND C.POLICY_CODE=#{policy_code}
          AND ROWNUM=1]]>
	</select>

	<!-- 保单到期提醒接口：查询满足一下3种条件的保单：2、未勾选自动续保； 5、被保险人当前年龄未超过最高投保年龄 6、保险期届满前30天（含30天），保险期满后30天内（含30天）的安康保（86000670）、安心保（86000689）、宜家保（86000690）、安途保（86000691）保单。
		-->
	<select id="JRQD_findPolicyExpirationRemindEffectInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select a.agent_code, a.policy_code, c.busi_prod_code,c.EXPIRY_DATE,D.insured_max_age, b.insured_age,D.product_code_sys,
				D.Product_Abbr_Name,E.Std_Prem_Af
				  from APP___PAS__DBUSER.T_CONTRACT_AGENT     A,
				       APP___PAS__DBUSER.T_INSURED_LIST       B,
				       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C,
				       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   D,
				       APP___PAS__DBUSER.T_CONTRACT_PRODUCT E
				 WHERE 1 = 1
				   AND A.POLICY_CODE = B.POLICY_CODE
				   AND B.POLICY_CODE = C.POLICY_CODE
				   AND C.BUSI_PRD_ID = D.BUSINESS_PRD_ID
				   AND B.INSURED_AGE < D.INSURED_MAX_AGE
				   AND C.BUSI_ITEM_ID = E.BUSI_ITEM_ID
				   AND A.AGENT_CODE= #{agent_code}
				   AND B.CUSTOMER_ID = #{customer_id}
				   AND C.RENEW = '0'
				   AND (((TO_DATE(TO_CHAR(C.EXPIRY_DATE, 'YYYY-MM-DD'), 'YYYY-MM-DD') -
				       TO_DATE(to_char(sysdate, 'YYYY-MM-DD'), 'YYYY-MM-DD')) <= 30 and
				       (TO_DATE(TO_CHAR(C.EXPIRY_DATE, 'YYYY-MM-DD'), 'YYYY-MM-DD') -
				       TO_DATE(to_char(sysdate, 'YYYY-MM-DD'), 'YYYY-MM-DD')) > 0)
				        OR
				       ((TO_DATE(TO_CHAR(sysdate, 'YYYY-MM-DD'), 'YYYY-MM-DD') -
				       TO_DATE(to_char(C.EXPIRY_DATE, 'YYYY-MM-DD'), 'YYYY-MM-DD')) <= 30 and
				       (TO_DATE(TO_CHAR(C.EXPIRY_DATE, 'YYYY-MM-DD'), 'YYYY-MM-DD') -
				       TO_DATE(to_char(sysdate, 'YYYY-MM-DD'), 'YYYY-MM-DD')) > 0 and
				       C.BUSI_PROD_CODE IN ('86000670', '86000689', '86000690', '86000691'))

				       )
				   AND B.INSURED_AGE < D.INSURED_MAX_AGE
		]]>
	</select>

	<select id="JRQD_queryWeChatXbCheckFlag" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT TCM.POLICY_CODE,TCM.END_CAUSE
              FROM DEV_PAS.T_CONTRACT_MASTER TCM
             WHERE 1 = 1
               AND TCM.POLICY_CODE IN
                   (SELECT T.POLICY_CODE
                      FROM DEV_PAS.T_INSURED_LIST T
                     WHERE T.CUSTOMER_ID IN
                           (SELECT TIL.CUSTOMER_ID
                              FROM DEV_PAS.T_INSURED_LIST TIL
                             WHERE TIL.POLICY_CODE = #{policy_code}))
                AND TCM.POLICY_CODE !=#{policy_code}
	</select>
	<select id="JRQD_queryIsClaimssuspended" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT TCM.POLICY_CODE
FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                          LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                            ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
                          LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
                            ON A.POLICY_CODE = TCM.POLICY_CODE
                         WHERE B.SUB_ID ='067'
               	AND B.LOCK_SERVICE_TYPE = 1
                          AND TCM.POLICY_CODE=#{policy_code}
	</select>

	<select id="JRQD_queryIsbaoquansuspended" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT TCM.POLICY_CODE
FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                          LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                            ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
                          LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
                            ON A.POLICY_CODE = TCM.POLICY_CODE
                         WHERE B.SUB_ID IN ('068', '067')
               	AND B.LOCK_SERVICE_TYPE = 1
                          AND TCM.POLICY_CODE=#{policy_code}
	</select>

	<!-- 根据保单号和查客户信息 -->
	<select id="JRQD_findInsuredInfoByPolicyCode1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select c.list_id, c.policy_id, c.policy_code, c.apply_code, c.address_id, c.customer_id
			from dev_pas.t_insured_list c where c.list_id =
			(select b.insured_id
			from dev_pas.t_contract_busi_prod a
			left join dev_pas.t_benefit_insured b
			on a.policy_code = b.policy_code
			where a.busi_item_id = b.busi_item_id
			and a.policy_code = #{policy_code}
			and b.order_id = #{order_id}
			and rownum = 1)
		]]>
	</select>


	<!-- 查询保单下的第一被保险人 -->
	<select id="JRQD_PA_findFristInsuredInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select ti.ADDRESS_ID,
			       ti.soci_secu,
			       ti.STAND_LIFE,
			       ti.SMOKING,
			       ti.CUSTOMER_ID,
			       cus.customer_name,
			       cus.customer_gender,
			       cus.customer_birthday,
			       cus.customer_cert_type,
			       cus.customer_certi_code,
			       cus.job_code as cusjobcode,
			       ti.CUSTOMER_HEIGHT,
			       ti.JOB_CODE,
			       ti.RELATION_TO_PH,
			       ti.CUSTOMER_WEIGHT,
			       ti.APPLY_CODE,
			       ti.INSURED_AGE,
			       ti.JOB_UNDERWRITE,
			       ti.LIST_ID,
			       tb.order_id,
			       tb.busi_item_id,
			       tb.policy_id,
			       tb.policy_code
			  from APP___PAS__DBUSER.t_benefit_insured tb
			  left join APP___PAS__DBUSER.t_insured_list ti
			    on tb.insured_id = ti.list_id
			  join APP___PAS__DBUSER.T_CUSTOMER cus
			    on ti.customer_id = cus.customer_id
			  where 1=1

	 	]]>
	 <if test=" policy_code  != null "><![CDATA[ AND ti.POLICY_CODE = ${policy_code} ]]></if>
	 <if test=" policy_id  != null "><![CDATA[ AND ti.policy_id = #{policy_id} ]]></if>
	 <if test=" policy_id  != null "><![CDATA[ and tb.order_id = #{order_id}]]></if>
	 <![CDATA[ and rownum =1 ]]>

	</select>


	<!-- 查询所有的被保人信息
	//#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求-受益人变更-保单查询接口
    //modify by cuiqi_wb
    //2021-11-23 -->
	<select id="JRQD_queryInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[ SELECT A.POLICY_CODE,
            C.OLD_CUSTOMER_ID,
            C.CUSTOMER_ID,

            C.CUSTOMER_NAME,
            C.CUSTOMER_GENDER,
            C.COUNTRY_CODE,
            A.SOCI_SECU,
            C.CUSTOMER_BIRTHDAY,
            C.CUSTOMER_CERT_TYPE,
            C.CUSTOMER_CERTI_CODE,
            C.CUST_CERT_STAR_DATE,
            C.CUST_CERT_END_DATE,
            B.MOBILE_TEL,
            B.FIXED_TEL,
            A.JOB_CODE,
            (select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = A.JOB_CODE) JOB_NAME,
            C.COMPANY_NAME,
            B.EMAIL,
            B.POST_CODE,
            A.RELATION_TO_PH,
            B.STATE,
            B.CITY,
            B.DISTRICT,
            B.ADDRESS,
            (SELECT TBD.ORDER_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED TBD,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP WHERE TCBP.BUSI_ITEM_ID=TBD.BUSI_ITEM_ID AND TCBP.MASTER_BUSI_ITEM_ID IS NULL AND TBD.INSURED_ID = A.LIST_ID  AND ROWNUM = 1 ) ORDER_ID,
            (SELECT COUNT(1)  FROM APP___PAS__DBUSER.T_CONTRACT_BENE DD WHERE A.LIST_ID = DD.INSURED_ID
               AND DD.BENE_TYPE = 1) LEGAL_BENE,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.STATE = O.CODE )  AS STATENAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.CITY = O.CODE )  AS CITYNAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.DISTRICT = O.CODE )  AS DISTRICTNAME,
            (SELECT O.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY O WHERE O.COUNTRY_CODE = C.COUNTRY_CODE) COUNTRY_NAME,
            (SELECT O.TYPE FROM APP___PAS__DBUSER.T_CERTI_TYPE O WHERE O.CODE = C.CUSTOMER_CERT_TYPE) CERT_TYPE_NAME
       FROM APP___PAS__DBUSER.T_INSURED_LIST A,
            APP___PAS__DBUSER.T_CUSTOMER      C,
            APP___PAS__DBUSER.T_ADDRESS       B
      WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
        AND A.ADDRESS_ID = B.ADDRESS_ID
        AND A.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}
   ]]>

	</select>

	<!-- 根据保单号查询第一被保险人 -->
	<select id="JRQD_findFristInsuredByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT C.POLICY_CODE,A.CUSTOMER_ID,A.CUSTOMER_NAME,A.CUSTOMER_GENDER,C.RELATION_TO_PH,C.SOCI_SECU,A.CUSTOMER_CERT_TYPE,A.OLD_CUSTOMER_ID,A.CUSTOMER_CERTI_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}
		]]>
	</select>

	<!-- 根据受理号和客户好查询被保险人信息 -->
	<select id="JRQD_findAllPolicyInusredByAgentNo" resultType="java.util.Map" parameterType="java.util.Map">
	   select til.policy_code,tcu.customer_id from APP___PAS__DBUSER.t_insured_list til,APP___PAS__DBUSER.t_contract_agent t,APP___PAS__DBUSER.t_customer tcu
	   where til.policy_code = t.policy_code
	   and tcu.customer_id = til.customer_id
	   and tcu.customer_id = #{customer_id}
	   and t.agent_code = #{agentCode}
	</select>

	<!-- 根据保单号查询主险第一被保人customerid和与投保人关系 -->
	<select id="JRQD_PA_firstBusiInsuredAndPh" resultType="java.util.Map" parameterType="java.util.Map">
				SELECT TIL.CUSTOMER_ID, TIL.RELATION_TO_PH
				  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
				       APP___PAS__DBUSER.T_BENEFIT_INSURED    TBI,
				       APP___PAS__DBUSER.T_INSURED_LIST       TIL
				 WHERE TCBP.POLICY_CODE = TBI.POLICY_CODE
				   AND TCBP.BUSI_PROD_CODE = TBI.PRODUCT_CODE
				   AND TBI.INSURED_ID = TIL.LIST_ID
				   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
				   AND TBI.ORDER_ID = '1'
				   AND TCBP.POLICY_CODE = #{policy_code}
	</select>

	<!-- 客户保单基本信息查询保单被保人信息sql -->
	<select id="JRQD_queryCusPlyInsuredInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT
		        TC.CUSTOMER_NAME INSUREDNAME,
				DECODE(TC.CUSTOMER_GENDER,'1','0','2','1','2') INSUREDSEX,
				TC.CUSTOMER_CERT_TYPE INSUREDIDTYPE,
				TC.CUSTOMER_CERTI_CODE INSUREDIDCODE,
				TO_CHAR(TC.CUSTOMER_BIRTHDAY,'YYYY-MM-DD') INSUREDBIRTHDAY,
				TO_CHAR(TC.CUST_CERT_STAR_DATE,'YYYY-MM-DD') INSUREDIDEFFSTARTDATE,
				TO_CHAR(TC.CUST_CERT_END_DATE,'YYYY-MM-DD') INSUREDIDEFFENDDATE,
				(SELECT TCO.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCO WHERE TCO.COUNTRY_CODE=TC.COUNTRY_CODE) INSUREDNATIVEPLACE,
				(SELECT TJ.JOB_NAME FROM APP___PAS__DBUSER.T_JOB_CODE TJ WHERE TJ.JOB_CODE=TC.JOB_CODE) INSUREDPOSITION,
				(SELECT TJ.JOB_CODE FROM APP___PAS__DBUSER.T_JOB_CODE TJ WHERE TJ.JOB_CODE=TC.JOB_CODE) INSUREDPOSITIONCODE,
				TA.MOBILE_TEL INSUREDMOBILE,
				TA.FIXED_TEL INSUREDPHONE,
				(
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.STATE) ||
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.CITY)  ||
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.DISTRICT)  ||
				 	TA.ADDRESS
			  	) INSUREDPOSTALADDRESS,
				TA.POST_CODE INSUREDZIPCODE,
				(SELECT TLPR.RELATION_NAME FROM DEV_PAS.T_LA_PH_RELA TLPR WHERE TLPR.RELATION_CODE=TIL.RELATION_TO_PH) RELATIONTOMAININSURED,
				NVL((SELECT DECODE(TCT.TAX_RESIDENT_TYPE,'','N','Y') FROM DEV_PAS.T_CUSTOMER_TAX TCT
					   WHERE  tct.customer_tax_name =tc.customer_name
				       and tct.customer_tax_birthday = tc.customer_birthday
				       and tct.customer_tax_cert_type = tc.customer_cert_type
				       and tct.customer_tax_certi_code = tc.customer_certi_code
				       and tct.customer_tax_gender = tc.customer_gender),'N') INSUREDCRSFLAG,
				(SELECT TRT.RESIDENT_NAME FROM DEV_PAS.T_CUSTOMER_TAX TCT LEFT JOIN DEV_PAS.T_TAX_RESIDENT_TYPE TRT ON TCT.TAX_RESIDENT_TYPE = TRT.RESIDENT_CODE
					WHERE  tct.customer_tax_name =tc.customer_name
			       and tct.customer_tax_birthday = tc.customer_birthday
			       and tct.customer_tax_cert_type = tc.customer_cert_type
			       and tct.customer_tax_certi_code = tc.customer_certi_code
			       and tct.customer_tax_gender = tc.customer_gender) RESIDENTNAME
			FROM APP___PAS__DBUSER.T_INSURED_LIST TIL,APP___PAS__DBUSER.T_CUSTOMER TC,APP___PAS__DBUSER.T_ADDRESS TA
			WHERE TIL.CUSTOMER_ID=TC.CUSTOMER_ID AND TIL.ADDRESS_ID=TA.ADDRESS_ID
			AND TIL.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}
		]]>
	</select>
	<!-- 查询投被保人为同一人 -->
	<select id="JRQD_findInsuredPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
	  select til.policy_code,tcu.customer_id from APP___PAS__DBUSER.t_insured_list til,APP___PAS__DBUSER.t_contract_agent t,APP___PAS__DBUSER.t_customer tcu, APP___PAS__DBUSER.t_policy_holder tph
	   where til.policy_code = t.policy_code
	   and tcu.customer_id = til.customer_id
	   and til.customer_id = tph.customer_id
       and til.policy_code = tph.policy_code
	   and tcu.customer_id = #{customer_id}
	   and t.agent_code = #{agentCode}

	</select>

	<!-- 查询被保人信息列表信息
	//#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求-贷款清偿-保单查询接口
    //modify by cuiqi_wb
    //2021-11-26
    -->
	<select id="JRQD_PA_findInsureCustomer" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	     SELECT TCU.CUSTOMER_ID,
	       TCU.CUSTOMER_NAME,
	       TCU.CUSTOMER_GENDER,
	       TS.COUNTRY_CODE,
	       (SELECT TY.COUNTRY_NAME
	          FROM APP___PAS__DBUSER.T_COUNTRY TY
	         WHERE TY.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
	       TCU.CUSTOMER_BIRTHDAY,
	       TCU.CUSTOMER_CERT_TYPE,
	       (SELECT TE.TYPE
	          FROM APP___PAS__DBUSER.T_CERTI_TYPE TE
	         WHERE TE.CODE = TCU.CUSTOMER_CERT_TYPE) TYPE_NAME,
	       TCU.CUSTOMER_CERTI_CODE,
	       TCU.CUST_CERT_STAR_DATE,
	       TCU.CUST_CERT_END_DATE,
	       TCU.OLD_CUSTOMER_ID,
	       TS.MOBILE_TEL,
	       TS.OFFICE_TEL OFFICETEL,
	       TIL.JOB_CODE,
           (select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = TIL.JOB_CODE) JOB_NAME,
	       TS.FIXED_TEL FIXEDTEL,
	       (SELECT Z.ORDER_ID
	          FROM APP___PAS__DBUSER.T_BENEFIT_INSURED Z
	         WHERE Z.ORDER_ID = 1
	           AND Z.POLICY_CODE = TIL.POLICY_CODE
	           AND Z.INSURED_ID = TIL.LIST_ID
	           AND ROWNUM = 1) MAIN_INSURED,
	       TCU.COMPANY_NAME,
	       (SELECT TT.NAME
	          FROM APP___PAS__DBUSER.T_DISTRICT TT
	         WHERE TT.CODE = TS.STATE) STATE,
	       (SELECT TT.NAME
	          FROM APP___PAS__DBUSER.T_DISTRICT TT
	         WHERE TT.CODE = TS.CITY) CITY,
	       (SELECT TT.NAME
	          FROM APP___PAS__DBUSER.T_DISTRICT TT
	         WHERE TT.CODE = TS.DISTRICT) DISTRICT,
	       TS.ADDRESS,
	       TIL.RELATION_TO_PH,
	       (SELECT YY.ORDER_ID
	          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD ZZ,
	               APP___PAS__DBUSER.T_BENEFIT_INSURED    YY
	         WHERE ZZ.MASTER_BUSI_ITEM_ID IS NULL
	           AND ZZ.BUSI_ITEM_ID = YY.BUSI_ITEM_ID
	           AND ZZ.POLICY_CODE = TIL.POLICY_CODE
	           AND ROWNUM = 1) MAIN_INSURED,
	       (SELECT LPR.RELATION_NAME
	          FROM APP___PAS__DBUSER.T_LA_PH_RELA LPR
	         WHERE LPR.RELATION_CODE = TIL.RELATION_TO_PH) RELATION_NAME
			  FROM APP___PAS__DBUSER.T_INSURED_LIST TIL,
			       APP___PAS__DBUSER.T_CUSTOMER     TCU,
			       APP___PAS__DBUSER.T_ADDRESS      TS
			 WHERE 1 = 1
			   AND TIL.POLICY_CODE = #{policy_code}
			   AND TCU.CUSTOMER_ID = TIL.CUSTOMER_ID
			   AND TS.ADDRESS_ID = TIL.ADDRESS_ID
                                                 ]]>
	</select>
	<!-- 根据保单号查询主险第一被保人信息 -->
	<select id="JRQD_PA_firstBusiInsured" resultType="java.util.Map" parameterType="java.util.Map">

	        select til.customer_id from APP___PAS__DBUSER.t_contract_busi_prod tcbp,APP___PAS__DBUSER.t_insured_list til,APP___PAS__DBUSER.t_benefit_insured td
	        where tcbp.policy_code = til.policy_code
	        and tcbp.busi_item_id = td.busi_item_id
	        and tcbp.master_busi_item_id is null
	        and td.order_id = '1'
	        and tcbp.policy_code = #{policy_code}

	</select>
	<!--根据保单id查询主险第一被保人信息  -->
	<select id="JRQD_PA_queryFristInsured" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
select DISTINCT a.insured_id, b.customer_name
  from APP___PAS__DBUSER.t_benefit_insured A,
       APP___PAS__DBUSER.t_insured_list ti,
       APP___PAS__DBUSER.t_customer b
  WHERE
    A.POLICY_ID=#{policy_id}
    and A.insured_id = ti.list_id
    AND A.POLICY_ID=TI.POLICY_ID
    and ti.customer_id=b.customer_id
    AND A.ORDER_ID=1

	]]>
	</select>

	<!-- 根据被保人查询住院津贴 -->
	<select id="JRQD_PA_findInsureListByCustomer" resultType="java.util.Map" parameterType="java.util.Map">
			select (nvl(csunit.unit,0) + nvl(pasunit.unit,0)) *50 as unit from (
			select sum(cp.unit) unit
			  from APP___PAS__DBUSER.T_BENEFIT_INSURED    bi,
			       APP___PAS__DBUSER.t_insured_list       il,
			       APP___PAS__DBUSER.t_contract_busi_prod cbp,
			       APP___PAS__DBUSER.t_contract_product   cp
			 where il.customer_id = #{customer_id}
			   and bi.insured_id = il.list_id
			   and cbp.busi_item_id = bi.busi_item_id
			   and cbp.liability_state in ('0','1','4')
			   and cbp.busi_prod_code in ('00718001', '00225000', '00963000')
			   and cp.busi_item_id = cbp.busi_item_id) pasunit,
			(
			select nvl(newunit.unit,0)-nvl(oldunit.unit,0) as unit from (
			select sum(ccp.unit) unit
			  from APP___PAS__DBUSER.t_cs_policy_change      cpc,
			       APP___PAS__DBUSER.t_cs_benefit_insured    cbi,
			       APP___PAS__DBUSER.t_cs_insured_list cil,
			       APP___PAS__DBUSER.t_cs_contract_busi_prod ccbp,
			       APP___PAS__DBUSER.t_cs_contract_product   ccp,
             	   APP___PAS__DBUSER.t_cs_accept_change cac
			 where cpc.policy_chg_id = cbi.policy_chg_id
			   and cbi.policy_chg_id = ccbp.policy_chg_id
			   and ccbp.policy_chg_id = ccp.policy_chg_id
			   and ccp.busi_item_id = ccbp.busi_item_id
			   and cbi.busi_item_id = ccbp.busi_item_id
			   and cil.policy_chg_id = cpc.policy_chg_id
			   and cil.list_id = cbi.insured_id
               and cac.accept_id = cpc.accept_id
         	   and cac.accept_status in ('06','07','08','09','10','11','13','20','21')
			   and ccbp.busi_prod_code in ('00718001', '00225000', '00963000')
			   and cbi.old_new = '1'
			   and ccbp.old_new = '1'
			   and ccp.old_new = '1'
			   and cil.old_new = '1'
			   and cil.customer_id =#{customer_id})newunit,
			(
			select sum(ccp.unit) unit
			  from APP___PAS__DBUSER.t_cs_policy_change      cpc,
			       APP___PAS__DBUSER.t_cs_benefit_insured    cbi,
			       APP___PAS__DBUSER.t_cs_insured_list cil,
			       APP___PAS__DBUSER.t_cs_contract_busi_prod ccbp,
			       APP___PAS__DBUSER.t_cs_contract_product   ccp,
			       APP___PAS__DBUSER.t_cs_accept_change cac
			 where cpc.policy_chg_id = cbi.policy_chg_id
			   and cbi.policy_chg_id = ccbp.policy_chg_id
			   and ccbp.policy_chg_id = ccp.policy_chg_id
			   and ccp.busi_item_id = ccbp.busi_item_id
			   and cbi.busi_item_id = ccbp.busi_item_id
			   and cil.policy_chg_id = cpc.policy_chg_id
			   and cil.list_id = cbi.insured_id
			   and cac.accept_id = cpc.accept_id
         	   and cac.accept_status in ('06','07','08','09','10','11','13','20','21')
			   and ccbp.busi_prod_code in ('00718001', '00225000', '00963000')
			   and cbi.old_new = '1'
			   and ccbp.old_new = '0'
			   and ccp.old_new = '0'
			   and cil.old_new = '1'
			   and cil.customer_id = #{customer_id}) oldunit) csunit
	</select>

	<!-- 根据被保人查询住院津贴保障计划(554) -->
	<select id="JRQD_PA_findInsureListByCustomerByBZJH" resultType="java.util.Map" parameterType="java.util.Map">
											select (nvl(csfield1.field1,0) + nvl(pasfield1.field1,0)) *100 as field from (
						                      select sum(cpo.field1) field1
						                        from APP___PAS__DBUSER.T_BENEFIT_INSURED    bi,
						                             APP___PAS__DBUSER.t_insured_list       il,
						                             APP___PAS__DBUSER.t_contract_busi_prod cbp,
						                             APP___PAS__DBUSER.t_contract_product   cp,
						                             APP___PAS__DBUSER.t_contract_product_other cpo
						                       where il.customer_id = #{customer_id}
						                         and bi.insured_id = il.list_id
						                         and cbp.busi_item_id = bi.busi_item_id
						                         and cbp.liability_state in ('0','1','4')
						                         and cbp.busi_prod_code in ('00554000')
						                         and cp.item_id = cpo.item_id
						                         and cp.busi_item_id = cbp.busi_item_id) pasfield1,
						                      (
						                      select nvl(newfield1.field1,0)-nvl(oldfield1.field1,0) as field1 from (
						                       (select nvl(newfield2.field1,0) + nvl(newfield3.field1,0) as field1 from
												 (select nvl(max(cpo.field1),0) field1
						                        from APP___PAS__DBUSER.t_cs_policy_change      cpc,
						                             APP___PAS__DBUSER.t_cs_benefit_insured    cbi,
						                             APP___PAS__DBUSER.t_cs_insured_list cil,
						                             APP___PAS__DBUSER.t_cs_contract_busi_prod ccbp,
						                             APP___PAS__DBUSER.t_cs_contract_product   ccp,
						                             APP___PAS__DBUSER.t_cs_contract_product_other cpo,
						                             APP___PAS__DBUSER.t_cs_accept_change cac
						                       where cpc.policy_chg_id = cbi.policy_chg_id
						                         and cbi.policy_chg_id = ccbp.policy_chg_id
						                         and ccbp.policy_chg_id = ccp.policy_chg_id
						                         and ccp.busi_item_id = ccbp.busi_item_id
						                         and cbi.busi_item_id = ccbp.busi_item_id
						                         and ccp.item_id = cpo.item_id
						                         and ccp.policy_chg_id = cpo.policy_chg_id
						                         and cil.policy_chg_id = cpc.policy_chg_id
						                         and cil.list_id = cbi.insured_id
						                         and cac.accept_id = cpc.accept_id
						                         and cac.accept_status in ('06','07','08','09','10','11','13','20','21')
						                         and ccbp.busi_prod_code in ( '00554000')
						                         and cbi.old_new = '1'
						                         and ccbp.old_new = '1'
						                         and ccp.old_new = '1'
						                         and cil.old_new = '1'
						                         and cac.service_code not in ('XX')
						                         and cil.customer_id = #{customer_id})newfield3,
						                     (
						                     select nvl(max(tcpp.new_plan_id),0) field1
						                       from APP___PAS__DBUSER.t_cs_policy_change          cpc,
						                            APP___PAS__DBUSER.t_cs_benefit_insured        cbi,
						                            APP___PAS__DBUSER.t_cs_insured_list           cil,
						                            APP___PAS__DBUSER.t_cs_contract_busi_prod     ccbp,
						                            APP___PAS__DBUSER.t_cs_contract_product       ccp,
						                            APP___PAS__DBUSER.t_cs_contract_product_other cpo,
						                            APP___PAS__DBUSER.t_cs_accept_change          cac,
						                            dev_pas.t_cs_precont_product                  tcpp
						                      where cpc.policy_chg_id = cbi.policy_chg_id
						                        and cbi.policy_chg_id = ccbp.policy_chg_id
						                        and ccbp.policy_chg_id = ccp.policy_chg_id
						                        and ccp.busi_item_id = ccbp.busi_item_id
						                        and cbi.busi_item_id = ccbp.busi_item_id
						                        and ccp.item_id = cpo.item_id
						                        and ccp.policy_chg_id = cpo.policy_chg_id
						                        and cil.policy_chg_id = cpc.policy_chg_id
						                        and cil.list_id = cbi.insured_id
						                        and cac.accept_id = cpc.accept_id
						                        and tcpp.change_id = cac.change_id
						                        and cac.accept_status in('06', '07', '08', '09', '10', '11', '13', '20', '21')
						                        and ccbp.busi_prod_code in ('00554000')
						                        and cac.service_code = 'XX'
						                        and cbi.old_new = '1'
						                        and ccbp.old_new = '1'
						                        and ccp.old_new = '1'
						                        and cil.old_new = '1'
						                        and tcpp.old_new = '1'
						                        and cil.customer_id = #{customer_id})newfield2))newfield1,
						                      (
						                      select nvl(max(cpo.field1),0) field1
						                        from dev_pas.t_cs_policy_change      cpc,
						                             APP___PAS__DBUSER.t_cs_benefit_insured    cbi,
						                             APP___PAS__DBUSER.t_cs_insured_list cil,
						                             APP___PAS__DBUSER.t_cs_contract_busi_prod ccbp,
						                             APP___PAS__DBUSER.t_cs_contract_product   ccp,
						                             APP___PAS__DBUSER.t_cs_contract_product_other cpo,
						                             APP___PAS__DBUSER.t_cs_accept_change cac
						                       where cpc.policy_chg_id = cbi.policy_chg_id
						                         and cbi.policy_chg_id = ccbp.policy_chg_id
						                         and ccbp.policy_chg_id = ccp.policy_chg_id
						                         and ccp.busi_item_id = ccbp.busi_item_id
						                         and ccp.item_id = cpo.item_id
						                         and ccp.policy_chg_id = cpo.policy_chg_id
						                         and cbi.busi_item_id = ccbp.busi_item_id
						                         and cil.policy_chg_id = cpc.policy_chg_id
						                         and cil.list_id = cbi.insured_id
						                         and cac.accept_id = cpc.accept_id
						                         and cac.accept_status in ('06','07','08','09','10','11','13','20','21')
						                         and ccbp.busi_prod_code in ('00554000')
						                         and cbi.old_new = '1'
						                         and ccbp.old_new = '0'
						                         and ccp.old_new = '0'
						                         and cil.old_new = '1'
						                         and cil.customer_id = #{customer_id}) oldfield1) csfield1
	</select>

	<!--76630 查询第一被保人信息
	//#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求-追加保费-保单查询接口
    //modify by cuiqi_wb
    //2021-11-24
	 -->
	<select id="JRQD_queryDYInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[ SELECT A.POLICY_CODE,
            C.OLD_CUSTOMER_ID,
            C.CUSTOMER_ID,
            C.CUSTOMER_NAME,
            C.CUSTOMER_GENDER,
            C.COUNTRY_CODE,
            A.SOCI_SECU,
            C.CUSTOMER_BIRTHDAY,
            C.CUSTOMER_CERT_TYPE,
            C.CUSTOMER_CERTI_CODE,
            C.CUST_CERT_STAR_DATE,
            C.CUST_CERT_END_DATE,
            B.MOBILE_TEL,
            B.FIXED_TEL,
            A.JOB_CODE,
            (select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = A.JOB_CODE) JOB_NAME,
            C.COMPANY_NAME,
            B.EMAIL,
            B.POST_CODE,
            A.RELATION_TO_PH,
            B.STATE,
            B.CITY,
            B.DISTRICT,
            B.ADDRESS,
            (SELECT TBD.ORDER_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED TBD,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP WHERE TCBP.BUSI_ITEM_ID=TBD.BUSI_ITEM_ID AND TCBP.MASTER_BUSI_ITEM_ID IS NULL AND TBD.INSURED_ID = A.LIST_ID) ORDER_ID,
            (SELECT COUNT(1)  FROM APP___PAS__DBUSER.T_CONTRACT_BENE DD WHERE A.LIST_ID = DD.INSURED_ID
               AND DD.BENE_TYPE = 1) LEGAL_BENE,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.STATE = O.CODE )  AS STATENAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.CITY = O.CODE )  AS CITYNAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.DISTRICT = O.CODE )  AS DISTRICTNAME,
            (SELECT O.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY O WHERE O.COUNTRY_CODE = C.COUNTRY_CODE) COUNTRY_NAME,
            (SELECT O.TYPE FROM APP___PAS__DBUSER.T_CERTI_TYPE O WHERE O.CODE = C.CUSTOMER_CERT_TYPE) CERT_TYPE_NAME
       FROM APP___PAS__DBUSER.T_INSURED_LIST A,
            APP___PAS__DBUSER.T_CUSTOMER      C,
            APP___PAS__DBUSER.T_ADDRESS       B,
            APP___PAS__DBUSER.T_BENEFIT_INSURED D,
            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
      WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
        AND A.ADDRESS_ID = B.ADDRESS_ID
        AND A.LIST_ID = D.INSURED_ID
        AND D.ORDER_ID = 1
        AND E.MASTER_BUSI_ITEM_ID IS NULL
		AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
        AND E.POLICY_CODE=D.POLICY_CODE
        AND A.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}
   ]]>

	</select>

	<!--根据被保人客户号查询保额 -->
	<select id="JRQD_PA_findAmountByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT C.AMOUNT, A.POLICY_CODE, C.PRODUCT_CODE
		FROM APP___PAS__DBUSER.T_INSURED_LIST       T,
			 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
			 APP___PAS__DBUSER.T_CONTRACT_MASTER B,
			 APP___PAS__DBUSER.T_CONTRACT_PRODUCT   C
	 WHERE A.POLICY_CODE = T.POLICY_CODE
		 AND T.POLICY_CODE = C.POLICY_CODE
		 AND A.POLICY_CODE = B.POLICY_CODE
		 AND A.BUSI_PROD_CODE IN ('00946000', '00847000')
		 AND C.PRODUCT_CODE IN ('946000', '847000')
		 AND A.LIABILITY_STATE in ('0','1','4')
		 AND T.CUSTOMER_ID = #{customer_id}


	]]>
	</select>

	<!--根据被保人客户号查询保额 -->
	<select id="JRQD_PA_findAmountByBQCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select nvl(newamount.amount, 0) - nvl(oldamount.amount, 0) as amount ,busi_prod_code
                        from (select ccp.amount amount,
                                     cac.service_code,
                                     ccbp.busi_prod_code,
                                     ccbp.policy_code
                                from APP___PAS__DBUSER.t_cs_policy_change      cpc,
                                     APP___PAS__DBUSER.t_cs_benefit_insured    cbi,
                                     APP___PAS__DBUSER.t_cs_insured_list       cil,
                                     APP___PAS__DBUSER.t_cs_contract_busi_prod ccbp,
                                     APP___PAS__DBUSER.t_cs_contract_product   ccp,
                                     APP___PAS__DBUSER.t_cs_accept_change      cac
                               where cpc.policy_chg_id = cbi.policy_chg_id
                                 and cbi.policy_chg_id = ccbp.policy_chg_id
                                 and ccbp.policy_chg_id = ccp.policy_chg_id
                                 and ccp.busi_item_id = ccbp.busi_item_id
                                 and cbi.busi_item_id = ccbp.busi_item_id
                                 and cil.policy_chg_id = cpc.policy_chg_id
                                 and cil.list_id = cbi.insured_id
                                 and cac.accept_id = cpc.accept_id
                                 and cac.accept_status in
                                     ('05','06', '07', '08', '09', '10', '11', '13', '20', '21')
                                 and ccbp.busi_prod_code in ('00946000', '00847000')
                                 and cac.service_code in ('PA', 'PT')
                                 AND ccp.LIABILITY_STATE in ('0','1','4')
                                 and cbi.old_new = '1'
                                 and ccbp.old_new = '1'
                                 and ccp.old_new = '1'
                                 and cil.old_new = '1'
                                 and cil.customer_id = #{customer_id} )newamount,
                               (select ccp.amount amount, ccbp.policy_code
                                        from APP___PAS__DBUSER.t_contract_busi_prod ccbp,
                                             APP___PAS__DBUSER.t_contract_product   ccp,
                                             APP___PAS__DBUSER.t_insured_list       cil
                                       where ccp.busi_item_id = ccbp.busi_item_id
                                         and cil.policy_code = ccbp.policy_code
                                         and ccbp.busi_prod_code in ('00946000', '00847000')
                                         and cil.customer_id = #{customer_id}) oldamount
                                         where newamount.policy_code = oldamount.policy_code
                             union all select ccp.amount amount,ccbp.busi_prod_code
                   from APP___PAS__DBUSER.t_cs_policy_change      cpc,
                                     APP___PAS__DBUSER.t_cs_benefit_insured    cbi,
                                     APP___PAS__DBUSER.t_cs_insured_list       cil,
                                     APP___PAS__DBUSER.t_cs_contract_busi_prod ccbp,
                                     APP___PAS__DBUSER.t_cs_contract_product   ccp,
                                     APP___PAS__DBUSER.t_cs_accept_change      cac
                               where cpc.policy_chg_id = cbi.policy_chg_id
                                 and cbi.policy_chg_id = ccbp.policy_chg_id
                                 and ccbp.policy_chg_id = ccp.policy_chg_id
                                 and ccp.busi_item_id = ccbp.busi_item_id
                                 and cbi.busi_item_id = ccbp.busi_item_id
                                 and cil.policy_chg_id = cpc.policy_chg_id
                                 and cil.list_id = cbi.insured_id
                                 and cac.accept_id = cpc.accept_id
                                 and cac.accept_status in
                                     ('05','06', '07', '08', '09', '10', '11', '13', '20', '21')
                                 and ccbp.busi_prod_code in ('00946000', '00847000')
                                 and cac.service_code in ( 'NS')
                                 and cbi.old_new = '1'
                                 and ccbp.old_new = '1'
                                 and ccp.old_new = '1'
                                 and cil.old_new = '1'
                                 and cil.customer_id = #{customer_id}

	]]>
	</select>

	<!--根据机构查询限额信息 -->
	<select id="JRQD_PA_findInsureListByAmout" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		SELECT a.organ_code,a.hospital_limit_amount,a.product_code
				FROM APP___PAS__DBUSER.t_Hospital_Benefit_Limit a
			 where a.product_code like  '%00225000,00718001,00963000,00554000%'
				 and a.organ_code = #{ORGAN_CODE}

	]]>
	</select>


	<!--根据保单号查询第一被保险人相关信息  -->
	<select id="JRQD_PA_findAmountByPolicyFirst" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select distinct c.mobile_tel tmobiletel,b.mobile_tel tcmobiletel
		  from APP___PAS__DBUSER.t_benefit_insured A,
		       APP___PAS__DBUSER.t_insured_list ti,
		       APP___PAS__DBUSER.t_customer b ,
					 APP___PAS__DBUSER.T_ADDRESS c
		  WHERE
		    ti.policy_code = #{policy_code,jdbcType=VARCHAR}
		    and A.insured_id = ti.list_id
		    AND A.POLICY_ID=TI.POLICY_ID
				AND ti.address_id = c.address_id
		    and ti.customer_id=b.customer_id
		    AND A.ORDER_ID=1

	]]>
	</select>


	<!--根据被保人客户号查询865/851保额   by liubinit -->
	<select id="JRQD_PA_findAmountByCustomerIdForSZYB" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT C.AMOUNT, A.POLICY_CODE, C.PRODUCT_CODE
		FROM APP___PAS__DBUSER.T_INSURED_LIST       T,
			 APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
			 APP___PAS__DBUSER.T_CONTRACT_MASTER B,
			 APP___PAS__DBUSER.T_CONTRACT_PRODUCT   C
	 WHERE A.POLICY_CODE = T.POLICY_CODE
		 AND T.POLICY_CODE = C.POLICY_CODE
		 AND A.POLICY_CODE = B.POLICY_CODE
		 AND A.BUSI_PROD_CODE IN ('00865000', '00851000')
		 AND C.PRODUCT_CODE IN ('865000', '851000')
		 AND B.LIABILITY_STATE in ('0','1','4')
		 AND T.CUSTOMER_ID = #{customer_id}


	]]>
	</select>

	<!--根据客户id查询第一被保人生日  -->
	<select id="JRQD_PA_queryFristInsuredByBirthday" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		select b.customer_birthday
		from app___pas__dbuser.t_benefit_insured a,
		   app___pas__dbuser.t_insured_list    ti,
		   app___pas__dbuser.t_customer        b
		where a.insured_id = ti.list_id
		and a.policy_id = ti.policy_id
		and ti.customer_id = b.customer_id
		and a.order_id = 1
		and ti.policy_id in
		   (select li.policy_id
		      from app___pas__dbuser.t_insured_list li
		     where li.customer_id = #{customer_id})
	]]>
	</select>

	<!-- 根据policycode校验投被保人是否为同一人  -->
	<select id="JRQD_PA_ckeckHoderInsureSameByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	SELECT A.POLICY_CODE, A.CUSTOMER_ID, A.POLICY_ID
				  FROM DEV_PAS.T_POLICY_HOLDER  A,
				       DEV_PAS.T_INSURED_LIST   B
				 WHERE A.CUSTOMER_ID = B.CUSTOMER_ID
				   AND A.POLICY_CODE = B.POLICY_CODE
				   AND A.POLICY_CODE = #{policy_code}]]>
	</select>
<select id="JRQD_PA_findAllInsuredListPaOrNb" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID FROM  ]]>
		<if test=" is_pas  != null "><![CDATA[ DEV_PAS.T_INSURED_LIST A]]></if> 
		<if test=" is_nb  != null "><![CDATA[ DEV_NB.T_NB_INSURED_LIST A]]></if> 
		<![CDATA[WHERE  A.POLICY_ID = #{policy_id} ]]>
	</select>
</mapper>
