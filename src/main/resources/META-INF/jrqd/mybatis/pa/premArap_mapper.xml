<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.dao.impl.PremArapDaoImpl">
	<!-- 生存领取历史划款信息查询排序 -->
	<sql id="JRQD_queryPayDueHistoryTransferList_cjk">
		<if test="tOrder!= null "><![CDATA[ ${tOrder} ]]></if>

	</sql>

	<sql id="JRQD_PA_premArapWhereCondition">
		<if test=" task_mark  != null "><![CDATA[ AND A.TASK_MARK = #{task_mark} ]]></if>
		<if test=" policy_organ_code != null and policy_organ_code != ''  "><![CDATA[ AND A.POLICY_ORGAN_CODE = #{policy_organ_code} ]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" is_item_main  != null "><![CDATA[ AND A.IS_ITEM_MAIN = #{is_item_main} ]]></if>
		<if test=" bookkeeping_id  != null "><![CDATA[ AND A.BOOKKEEPING_ID = #{bookkeeping_id} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" payee_phone != null and payee_phone != ''  "><![CDATA[ AND A.PAYEE_PHONE = #{payee_phone} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" paid_count  != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
		<if test=" funds_rtn_code != null and funds_rtn_code != ''  "><![CDATA[ AND A.FUNDS_RTN_CODE = #{funds_rtn_code} ]]></if>
		<if test=" customer_account_flag  != null "><![CDATA[ AND A.CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} ]]></if>
		<if test=" fee_type != null and fee_type != ''  "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" seq_no  != null "><![CDATA[ AND A.SEQ_NO = #{seq_no} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" fee_status_date  != null  and  fee_status_date  != ''  "><![CDATA[ AND A.FEE_STATUS_DATE = #{fee_status_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if
			test=" red_bookkeeping_time  != null  and  red_bookkeeping_time  != ''  "><![CDATA[ AND A.RED_BOOKKEEPING_TIME = #{red_bookkeeping_time} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" is_bank_text_date  != null  and  is_bank_text_date  != ''  "><![CDATA[ AND A.IS_BANK_TEXT_DATE = #{is_bank_text_date} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" is_risk_main  != null "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
		<if test=" is_bank_account  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT = #{is_bank_account} ]]></if>
		<if test=" frozen_status != null and frozen_status != ''  "><![CDATA[ AND A.FROZEN_STATUS = #{frozen_status} ]]></if>
		<if test=" bookkeeping_flag  != null "><![CDATA[ AND A.BOOKKEEPING_FLAG = #{bookkeeping_flag} ]]></if>
		<if test=" posted != null and posted != ''  "><![CDATA[ AND A.POSTED = #{posted} ]]></if>
		<if test=" group_id  != null "><![CDATA[ AND A.GROUP_ID = #{group_id} ]]></if>
		<if test=" red_bookkeeping_by  != null "><![CDATA[ AND A.RED_BOOKKEEPING_BY = #{red_bookkeeping_by} ]]></if>
		<if test=" frozen_status_date  != null  and  frozen_status_date  != ''  "><![CDATA[ AND A.FROZEN_STATUS_DATE = #{frozen_status_date} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE='${business_code}' ]]></if>
		<if test=" cus_acc_details_id  != null "><![CDATA[ AND A.CUS_ACC_DETAILS_ID = #{cus_acc_details_id} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" product_channel != null and product_channel != ''  "><![CDATA[ AND A.PRODUCT_CHANNEL = #{product_channel} ]]></if>
		<if
			test=" is_bank_account_date  != null  and  is_bank_account_date  != ''  "><![CDATA[ AND A.IS_BANK_ACCOUNT_DATE = #{is_bank_account_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" business_type != null and business_type != ''  "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" red_belnr != null and red_belnr != ''  "><![CDATA[ AND A.RED_BELNR = #{red_belnr} ]]></if>
		<if test=" bank_text_status != null and bank_text_status != ''  "><![CDATA[ AND A.BANK_TEXT_STATUS = #{bank_text_status} ]]></if>
		<if test=" group_code != null and group_code != ''  "><![CDATA[ AND A.GROUP_CODE = #{group_code} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" cus_acc_update_by  != null "><![CDATA[ AND A.CUS_ACC_UPDATE_BY = #{cus_acc_update_by} ]]></if>
		<if test=" is_bank_text_by  != null "><![CDATA[ AND A.IS_BANK_TEXT_BY = #{is_bank_text_by} ]]></if>
		<if test=" is_bank_account_by  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT_BY = #{is_bank_account_by} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" red_bookkeeping_flag  != null "><![CDATA[ AND A.RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag} ]]></if>
		<if test=" cus_acc_fee_amount  != null "><![CDATA[ AND A.CUS_ACC_FEE_AMOUNT = #{cus_acc_fee_amount} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" is_bank_text  != null "><![CDATA[ AND A.IS_BANK_TEXT = #{is_bank_text} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" busi_apply_date  != null  and  busi_apply_date  != ''  "><![CDATA[ AND A.BUSI_APPLY_DATE = #{busi_apply_date} ]]></if>
		<if test=" group_name != null and group_name != ''  "><![CDATA[ AND A.GROUP_NAME = #{group_name} ]]></if>
		<if test=" belnr != null and belnr != ''  "><![CDATA[ AND A.BELNR = #{belnr} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" withdraw_type != null and withdraw_type != ''  "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
		<if test=" rollback_unit_number != null and rollback_unit_number != ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number} ]]></if>
		<if test=" fail_times  != null "><![CDATA[ AND A.FAIL_TIMES = #{fail_times} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" frozen_status_by  != null "><![CDATA[ AND A.FROZEN_STATUS_BY = #{frozen_status_by} ]]></if>
		<if test=" bank_user_name != null and bank_user_name != ''  "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" fee_status_by  != null "><![CDATA[ AND A.FEE_STATUS_BY = #{fee_status_by} ]]></if>
		<if test=" pay_end_date  != null  and  pay_end_date  != ''  "><![CDATA[ AND A.PAY_END_DATE = #{pay_end_date} ]]></if>
		<if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" red_bookkeeping_id  != null "><![CDATA[ AND A.RED_BOOKKEEPING_ID = #{red_bookkeeping_id} ]]></if>
		<if test=" bookkeeping_by  != null "><![CDATA[ AND A.BOOKKEEPING_BY = #{bookkeeping_by} ]]></if>
		<if test=" cus_acc_update_time  != null  and  cus_acc_update_time  != ''  "><![CDATA[ AND A.CUS_ACC_UPDATE_TIME = #{cus_acc_update_time} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" bookkeeping_time  != null  and  bookkeeping_time  != ''  "><![CDATA[ AND A.BOOKKEEPING_TIME = #{bookkeeping_time} ]]></if>
		<if test=" refeflag != null and refeflag != ''  "><![CDATA[ AND A.REFEFLAG = #{refeflag} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" pay_liab_code != null and pay_liab_code != ''  "><![CDATA[ AND A.PAY_LIAB_CODE = #{pay_liab_code} ]]></if>
		<if test=" source_table_pk != null "><![CDATA[ AND A.SOURCE_TABLE_PK = #{source_table_pk} ]]></if>
		<if test=" source_table != null and source_table != ''  "><![CDATA[ AND A.SOURCE_TABLE = #{source_table} ]]></if>
		<if test=" not_fee_status != null and not_fee_status != '' "><![CDATA[ AND A.FEE_STATUS != #{not_fee_status}]]></if>
		<!-- <if test=" finish_time_is_null != null and finish_time_is_null != 
			'' "><![CDATA[ AND A.finish_time is null ]]></if> -->
		<if test=" claim_date  != null  and  claim_date  != ''  "><![CDATA[ AND A.DUE_TIME < #{claim_date} AND A.FINISH_TIME >= #{claim_date} ]]></if>
		<if test=" pay_mode_list != null and pay_mode_list != ''  "><![CDATA[ AND A.PAY_MODE in ]]>
			<foreach collection="pay_mode_list" item="item" index="index"
				open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test="deriv_type_list  != null and deriv_type_list.size()!=0 ">
			<![CDATA[  AND (]]>
			<foreach collection="deriv_type_list" item="deriv_type_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ A.DERIV_TYPE = #{deriv_type_item} ]]>
			</foreach>
			<![CDATA[)  ]]>
		</if>
		<if test="policy_chg_id != null and policy_chg_id != '' ">
			AND A.POLICY_CHG_ID=#{policy_chg_id}
		</if>
		<if test=" start_date  != null  and  start_date  != ''  ">
		<![CDATA[ AND A.DUE_TIME >= #{start_date} ]]></if>
		<!-- 业务类型的集合 -->
		<if test="businessTypeList  != null and businessTypeList.size()!=0 ">
			<![CDATA[  AND (]]>
			<foreach collection="businessTypeList" item="businessType"
				index="index" open="" close="" separator="or">
				<![CDATA[ A.BUSINESS_TYPE = #{businessType} ]]>
			</foreach>
			<![CDATA[)  ]]>
		</if>
		<!-- 收费状态 -->
		<if test=" feeStatusList  != null and feeStatusList.size()!=0">
			<![CDATA[ AND A.FEE_STATUS IN ]]>
			<foreach collection ="feeStatusList"  item="feestatus" index="index" open="(" close=")" separator=",">#{feestatus}</foreach>
		</if>
	</sql>
	

	<!-- 按索引生成的查询条件 -->
	<sql id="JRQD_PA_queryPremArapByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
	<!-- <sql id="JRQD_PA_queryPremArapByArapFeeIdCondition"> <if test=" arap_fee_id 
		!= null "><![CDATA[ AND A.ARAP_FEE_ID = #{arap_fee_id} ]]></if> </sql> -->

	<!-- 查询条件 -->
	<sql id="JRQD_PA_queryPremArapByUnitNumber">
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
	</sql>
	
	<sql id="JRQD_PA_premArapAGCondition">
		<if test=" task_mark  != null "><![CDATA[ AND A.TASK_MARK = #{task_mark} ]]></if>
		<if test=" policy_organ_code != null and policy_organ_code != ''  "><![CDATA[ AND A.POLICY_ORGAN_CODE = #{policy_organ_code} ]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" is_item_main  != null "><![CDATA[ AND A.IS_ITEM_MAIN = #{is_item_main} ]]></if>
		<if test=" bookkeeping_id  != null "><![CDATA[ AND A.BOOKKEEPING_ID = #{bookkeeping_id} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" payee_phone != null and payee_phone != ''  "><![CDATA[ AND A.PAYEE_PHONE = #{payee_phone} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" paid_count  != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
		<if test=" funds_rtn_code != null and funds_rtn_code != ''  "><![CDATA[ AND A.FUNDS_RTN_CODE = #{funds_rtn_code} ]]></if>
		<if test=" customer_account_flag  != null "><![CDATA[ AND A.CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} ]]></if>
		<if test=" fee_type != null and fee_type != ''  "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" seq_no  != null "><![CDATA[ AND A.SEQ_NO = #{seq_no} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" fee_status_date  != null  and  fee_status_date  != ''  "><![CDATA[ AND A.FEE_STATUS_DATE = #{fee_status_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" red_bookkeeping_time  != null  and  red_bookkeeping_time  != ''  "><![CDATA[ AND A.RED_BOOKKEEPING_TIME = #{red_bookkeeping_time} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" is_bank_text_date  != null  and  is_bank_text_date  != ''  "><![CDATA[ AND A.IS_BANK_TEXT_DATE = #{is_bank_text_date} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" is_risk_main  != null "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
		<if test=" is_bank_account  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT = #{is_bank_account} ]]></if>
		<if test=" frozen_status != null and frozen_status != ''  "><![CDATA[ AND A.FROZEN_STATUS = #{frozen_status} ]]></if>
		<if test=" bookkeeping_flag  != null "><![CDATA[ AND A.BOOKKEEPING_FLAG = #{bookkeeping_flag} ]]></if>
		<if test=" posted != null and posted != ''  "><![CDATA[ AND A.POSTED = #{posted} ]]></if>
		<if test=" group_id  != null "><![CDATA[ AND A.GROUP_ID = #{group_id} ]]></if>
		<if test=" red_bookkeeping_by  != null "><![CDATA[ AND A.RED_BOOKKEEPING_BY = #{red_bookkeeping_by} ]]></if>
		<if test=" frozen_status_date  != null  and  frozen_status_date  != ''  "><![CDATA[ AND A.FROZEN_STATUS_DATE = #{frozen_status_date} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE='${business_code}' ]]></if>
		<if test=" cus_acc_details_id  != null "><![CDATA[ AND A.CUS_ACC_DETAILS_ID = #{cus_acc_details_id} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" product_channel != null and product_channel != ''  "><![CDATA[ AND A.PRODUCT_CHANNEL = #{product_channel} ]]></if>
		<if test=" is_bank_account_date  != null  and  is_bank_account_date  != ''  "><![CDATA[ AND A.IS_BANK_ACCOUNT_DATE = #{is_bank_account_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" business_type != null and business_type != ''  "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" red_belnr != null and red_belnr != ''  "><![CDATA[ AND A.RED_BELNR = #{red_belnr} ]]></if>
		<if test=" bank_text_status != null and bank_text_status != ''  "><![CDATA[ AND A.BANK_TEXT_STATUS = #{bank_text_status} ]]></if>
		<if test=" group_code != null and group_code != ''  "><![CDATA[ AND A.GROUP_CODE = #{group_code} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" cus_acc_update_by  != null "><![CDATA[ AND A.CUS_ACC_UPDATE_BY = #{cus_acc_update_by} ]]></if>
		<if test=" is_bank_text_by  != null "><![CDATA[ AND A.IS_BANK_TEXT_BY = #{is_bank_text_by} ]]></if>
		<if test=" is_bank_account_by  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT_BY = #{is_bank_account_by} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" red_bookkeeping_flag  != null "><![CDATA[ AND A.RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag} ]]></if>
		<if test=" cus_acc_fee_amount  != null "><![CDATA[ AND A.CUS_ACC_FEE_AMOUNT = #{cus_acc_fee_amount} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" is_bank_text  != null "><![CDATA[ AND A.IS_BANK_TEXT = #{is_bank_text} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" busi_apply_date  != null  and  busi_apply_date  != ''  "><![CDATA[ AND A.BUSI_APPLY_DATE = #{busi_apply_date} ]]></if>
		<if test=" group_name != null and group_name != ''  "><![CDATA[ AND A.GROUP_NAME = #{group_name} ]]></if>
		<if test=" belnr != null and belnr != ''  "><![CDATA[ AND A.BELNR = #{belnr} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" withdraw_type != null and withdraw_type != ''  "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
		
		<if test=" rollback_unit_number != null and rollback_unit_number != ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER is not null ]]></if>
		<if test=" rollback_unit_number == null || rollback_unit_number == ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER is null ]]></if>
		
		<if test=" fail_times  != null "><![CDATA[ AND A.FAIL_TIMES = #{fail_times} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" frozen_status_by  != null "><![CDATA[ AND A.FROZEN_STATUS_BY = #{frozen_status_by} ]]></if>
		<if test=" bank_user_name != null and bank_user_name != ''  "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" fee_status_by  != null "><![CDATA[ AND A.FEE_STATUS_BY = #{fee_status_by} ]]></if>
		<if test=" pay_end_date  != null  and  pay_end_date  != ''  "><![CDATA[ AND A.PAY_END_DATE = #{pay_end_date} ]]></if>
		<if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" red_bookkeeping_id  != null "><![CDATA[ AND A.RED_BOOKKEEPING_ID = #{red_bookkeeping_id} ]]></if>
		<if test=" bookkeeping_by  != null "><![CDATA[ AND A.BOOKKEEPING_BY = #{bookkeeping_by} ]]></if>
		<if test=" cus_acc_update_time  != null  and  cus_acc_update_time  != ''  "><![CDATA[ AND A.CUS_ACC_UPDATE_TIME = #{cus_acc_update_time} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" bookkeeping_time  != null  and  bookkeeping_time  != ''  "><![CDATA[ AND A.BOOKKEEPING_TIME = #{bookkeeping_time} ]]></if>
		<if test=" refeflag != null and refeflag != ''  "><![CDATA[ AND A.REFEFLAG = #{refeflag} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" pay_liab_code != null and pay_liab_code != ''  "><![CDATA[ AND A.PAY_LIAB_CODE = #{pay_liab_code} ]]></if>
		<if test=" source_table_pk != null "><![CDATA[ AND A.SOURCE_TABLE_PK = #{source_table_pk} ]]></if>
		<if test=" source_table != null and source_table != ''  "><![CDATA[ AND A.SOURCE_TABLE = #{source_table} ]]></if>
	</sql>
	
	<!-- 添加操作 -->
	<insert id="JRQD_PA_addPremArap" useGeneratedKeys="true"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_PREM_ARAP.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_PREM_ARAP(
				CHARGE_PERIOD,TASK_MARK, POLICY_ORGAN_CODE, HOLDER_NAME, IS_ITEM_MAIN, BOOKKEEPING_ID, BATCH_NO, BUSI_PROD_NAME, 
				PAYEE_PHONE, UNIT_NUMBER, PAID_COUNT, FUNDS_RTN_CODE, CUSTOMER_ACCOUNT_FLAG, FEE_TYPE, SEQ_NO, 
				BUSI_PROD_CODE, APPLY_CODE, FEE_STATUS_DATE, ORGAN_CODE, RED_BOOKKEEPING_TIME, CHANNEL_TYPE, IS_BANK_TEXT_DATE, 
				UPDATE_BY, CHARGE_YEAR, IS_RISK_MAIN, IS_BANK_ACCOUNT, FROZEN_STATUS, BOOKKEEPING_FLAG, POSTED, 
				GROUP_ID, RED_BOOKKEEPING_BY, FROZEN_STATUS_DATE, UPDATE_TIME, INSURED_NAME, CUS_ACC_DETAILS_ID, 
				INSURED_ID, POLICY_TYPE, PRODUCT_CHANNEL, IS_BANK_ACCOUNT_DATE, POLICY_CODE, PAY_MODE, OPERATOR_BY, 
				BRANCH_CODE, BUSINESS_TYPE, VALIDATE_DATE, UPDATE_TIMESTAMP, INSERT_BY, RED_BELNR, BANK_TEXT_STATUS, 
				GROUP_CODE, CERTI_TYPE, CUS_ACC_UPDATE_BY, IS_BANK_TEXT_BY, IS_BANK_ACCOUNT_BY, BUSINESS_CODE, MONEY_CODE, 
				PAYEE_NAME, BANK_ACCOUNT, RED_BOOKKEEPING_FLAG, CUS_ACC_FEE_AMOUNT, CUSTOMER_ID, FINISH_TIME, INSERT_TIMESTAMP, 
				DUE_TIME, IS_BANK_TEXT, CERTI_CODE, BUSI_APPLY_DATE, GROUP_NAME, BELNR, LIST_ID, 
				FEE_AMOUNT, SERVICE_CODE, HOLDER_ID, WITHDRAW_TYPE, ROLLBACK_UNIT_NUMBER, FAIL_TIMES, INSERT_TIME, 
				POLICY_YEAR, FROZEN_STATUS_BY, BANK_USER_NAME, FEE_STATUS, FEE_STATUS_BY, PAY_END_DATE, DERIV_TYPE, 
				RED_BOOKKEEPING_ID, BOOKKEEPING_BY, CUS_ACC_UPDATE_TIME, ARAP_FLAG, BANK_CODE, BOOKKEEPING_TIME, REFEFLAG, Audit_Date,
				AGENT_CODE, PREM_FREQ,POLICY_CHG_ID,PRODUCT_CODE,PAY_LIAB_CODE,SOURCE_TABLE,SOURCE_TABLE_PK,PRE_UNIT_NUMBER, IS_YBT_BANK_TAXT,AGENT_NAME,BUSI_ITEM_ID) 
			VALUES (
				#{charge_period, jdbcType=VARCHAR},#{task_mark, jdbcType=NUMERIC}, #{policy_organ_code, jdbcType=VARCHAR} , #{holder_name, jdbcType=VARCHAR} , #{is_item_main, jdbcType=NUMERIC} , #{bookkeeping_id, jdbcType=NUMERIC} , #{batch_no, jdbcType=VARCHAR} , #{busi_prod_name, jdbcType=VARCHAR} 
				, #{payee_phone, jdbcType=VARCHAR} , #{unit_number, jdbcType=VARCHAR} , #{paid_count, jdbcType=NUMERIC} , #{funds_rtn_code, jdbcType=VARCHAR} , #{customer_account_flag, jdbcType=NUMERIC} , #{fee_type, jdbcType=VARCHAR} , #{seq_no, jdbcType=NUMERIC} 
				, #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{fee_status_date, jdbcType=DATE} , #{organ_code, jdbcType=VARCHAR} , #{red_bookkeeping_time, jdbcType=DATE} , #{channel_type, jdbcType=VARCHAR} , #{is_bank_text_date, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{is_risk_main, jdbcType=NUMERIC} , #{is_bank_account, jdbcType=NUMERIC} , #{frozen_status, jdbcType=VARCHAR} , #{bookkeeping_flag, jdbcType=NUMERIC} , #{posted, jdbcType=VARCHAR} 
				, #{group_id, jdbcType=NUMERIC} , #{red_bookkeeping_by, jdbcType=NUMERIC} , #{frozen_status_date, jdbcType=DATE} , SYSDATE , #{insured_name, jdbcType=VARCHAR} , #{cus_acc_details_id, jdbcType=NUMERIC} 
				, #{insured_id, jdbcType=NUMERIC} , #{policy_type, jdbcType=VARCHAR} , #{product_channel, jdbcType=VARCHAR} , #{is_bank_account_date, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} , #{pay_mode, jdbcType=VARCHAR} , #{operator_by, jdbcType=NUMERIC} 
				, #{branch_code, jdbcType=VARCHAR} , #{business_type, jdbcType=VARCHAR} , #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{red_belnr, jdbcType=VARCHAR} , #{bank_text_status, jdbcType=VARCHAR} 
				, #{group_code, jdbcType=VARCHAR} , #{certi_type, jdbcType=VARCHAR} , #{cus_acc_update_by, jdbcType=NUMERIC} , #{is_bank_text_by, jdbcType=NUMERIC} , #{is_bank_account_by, jdbcType=NUMERIC} , #{business_code, jdbcType=VARCHAR} , #{money_code, jdbcType=VARCHAR} 
				, #{payee_name, jdbcType=VARCHAR} , #{bank_account, jdbcType=VARCHAR} , #{red_bookkeeping_flag, jdbcType=NUMERIC} , #{cus_acc_fee_amount, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} , #{finish_time, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{due_time, jdbcType=DATE} , #{is_bank_text, jdbcType=NUMERIC} , #{certi_code, jdbcType=VARCHAR} , #{busi_apply_date, jdbcType=DATE} , #{group_name, jdbcType=VARCHAR} , #{belnr, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} 
				, #{fee_amount, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{holder_id, jdbcType=NUMERIC} , #{withdraw_type, jdbcType=VARCHAR} , #{rollback_unit_number, jdbcType=VARCHAR} , #{fail_times, jdbcType=NUMERIC} , SYSDATE 
				, #{policy_year, jdbcType=NUMERIC} , #{frozen_status_by, jdbcType=NUMERIC} , #{bank_user_name, jdbcType=VARCHAR} , #{fee_status, jdbcType=VARCHAR} , #{fee_status_by, jdbcType=NUMERIC} , #{pay_end_date, jdbcType=DATE} , #{deriv_type, jdbcType=VARCHAR} 
				, #{red_bookkeeping_id, jdbcType=NUMERIC} , #{bookkeeping_by, jdbcType=NUMERIC} , #{cus_acc_update_time, jdbcType=DATE} , #{arap_flag, jdbcType=VARCHAR} , #{bank_code, jdbcType=VARCHAR} , #{bookkeeping_time, jdbcType=DATE} , #{refeflag, jdbcType=VARCHAR} 
				, #{audit_date, jdbcType=DATE}, #{agent_code, jdbcType=VARCHAR} , #{prem_freq, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC},#{product_code, jdbcType=VARCHAR},#{pay_liab_code, jdbcType=VARCHAR},#{source_table, jdbcType=VARCHAR},#{source_table_pk, jdbcType=NUMERIC}
				, #{pre_unit_number, jdbcType=VARCHAR},#{is_ybt_bank_taxt, jdbcType=NUMERIC},#{agent_name,jdbcType=VARCHAR}, #{busi_item_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="JRQD_PA_deletePremArap" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_PREM_ARAP WHERE LIST_ID = #{list_id} ]]>
	</delete>

	<!-- 修改操作 -->
	<update id="JRQD_PA_updatePremArap" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_ARAP ]]>
		<set>
			<trim suffixOverrides=",">
				CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR},
				TASK_MARK = #{task_mark, jdbcType=NUMERIC},
				POLICY_ORGAN_CODE = #{policy_organ_code, jdbcType=VARCHAR} ,
				HOLDER_NAME = #{holder_name, jdbcType=VARCHAR} ,
				IS_ITEM_MAIN = #{is_item_main, jdbcType=NUMERIC} ,
				BOOKKEEPING_ID = #{bookkeeping_id, jdbcType=NUMERIC} ,
				BATCH_NO = #{batch_no,jdbcType=VARCHAR} ,
				BUSI_PROD_NAME = #{busi_prod_name,jdbcType=VARCHAR} ,
				PAYEE_PHONE = #{payee_phone, jdbcType=VARCHAR} ,
				UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
				PAID_COUNT = #{paid_count, jdbcType=NUMERIC} ,
				FUNDS_RTN_CODE = #{funds_rtn_code,jdbcType=VARCHAR} ,
				CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag,jdbcType=NUMERIC} ,
				FEE_TYPE = #{fee_type, jdbcType=VARCHAR} ,
				SEQ_NO
				= #{seq_no, jdbcType=NUMERIC} ,
				BUSI_PROD_CODE = #{busi_prod_code,
				jdbcType=VARCHAR} ,
				APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
				FEE_STATUS_DATE = #{fee_status_date, jdbcType=DATE} ,
				ORGAN_CODE =
				#{organ_code, jdbcType=VARCHAR} ,
				RED_BOOKKEEPING_TIME =
				#{red_bookkeeping_time, jdbcType=DATE} ,
				CHANNEL_TYPE =
				#{channel_type, jdbcType=VARCHAR} ,
				IS_BANK_TEXT_DATE =
				#{is_bank_text_date, jdbcType=DATE} ,
				UPDATE_BY = #{update_by,
				jdbcType=NUMERIC} ,
				CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
				IS_RISK_MAIN = #{is_risk_main, jdbcType=NUMERIC} ,
				IS_BANK_ACCOUNT =
				#{is_bank_account, jdbcType=NUMERIC} ,
				FROZEN_STATUS =
				#{frozen_status, jdbcType=VARCHAR} ,
				BOOKKEEPING_FLAG =
				#{bookkeeping_flag, jdbcType=NUMERIC} ,
				POSTED = #{posted,
				jdbcType=VARCHAR} ,
				GROUP_ID = #{group_id, jdbcType=NUMERIC} ,
				RED_BOOKKEEPING_BY = #{red_bookkeeping_by, jdbcType=NUMERIC} ,
				FROZEN_STATUS_DATE = #{frozen_status_date, jdbcType=DATE} ,
				UPDATE_TIME = SYSDATE ,
				INSURED_NAME = #{insured_name,
				jdbcType=VARCHAR} ,

				CUS_ACC_DETAILS_ID = #{cus_acc_details_id,
				jdbcType=NUMERIC} ,
				INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
				POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
				PRODUCT_CHANNEL =
				#{product_channel, jdbcType=VARCHAR} ,
				IS_BANK_ACCOUNT_DATE =
				#{is_bank_account_date, jdbcType=DATE} ,
				POLICY_CODE = #{policy_code,
				jdbcType=VARCHAR} ,
				PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
				OPERATOR_BY = #{operator_by, jdbcType=NUMERIC} ,
				BRANCH_CODE =
				#{branch_code, jdbcType=VARCHAR} ,
				BUSINESS_TYPE = #{business_type,
				jdbcType=VARCHAR} ,
				VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				RED_BELNR = #{red_belnr,
				jdbcType=VARCHAR} ,
				BANK_TEXT_STATUS = #{bank_text_status,
				jdbcType=VARCHAR} ,
				GROUP_CODE = #{group_code, jdbcType=VARCHAR} ,
				CERTI_TYPE = #{certi_type, jdbcType=VARCHAR} ,
				CUS_ACC_UPDATE_BY =
				#{cus_acc_update_by, jdbcType=NUMERIC} ,
				IS_BANK_TEXT_BY =
				#{is_bank_text_by, jdbcType=NUMERIC} ,
				IS_BANK_ACCOUNT_BY =
				#{is_bank_account_by, jdbcType=NUMERIC} ,
				BUSINESS_CODE =
				#{business_code, jdbcType=VARCHAR} ,
				MONEY_CODE = #{money_code,
				jdbcType=VARCHAR} ,
				PAYEE_NAME = #{payee_name, jdbcType=VARCHAR} ,
				BANK_ACCOUNT = #{bank_account, jdbcType=VARCHAR} ,
				RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag, jdbcType=NUMERIC} ,
				CUS_ACC_FEE_AMOUNT = #{cus_acc_fee_amount, jdbcType=NUMERIC} ,
				CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
				FINISH_TIME =
				#{finish_time, jdbcType=DATE} ,
				DUE_TIME = #{due_time, jdbcType=DATE}
				,
				IS_BANK_TEXT = #{is_bank_text, jdbcType=NUMERIC} ,
				CERTI_CODE =
				#{certi_code, jdbcType=VARCHAR} ,
				BUSI_APPLY_DATE =
				#{busi_apply_date, jdbcType=DATE} ,
				GROUP_NAME = #{group_name,
				jdbcType=VARCHAR} ,
				BELNR = #{belnr, jdbcType=VARCHAR} ,
				FEE_AMOUNT =
				#{fee_amount, jdbcType=NUMERIC} ,
				SERVICE_CODE = #{service_code,
				jdbcType=VARCHAR} ,
				HOLDER_ID = #{holder_id, jdbcType=NUMERIC} ,
				WITHDRAW_TYPE = #{withdraw_type, jdbcType=VARCHAR} ,
				ROLLBACK_UNIT_NUMBER = #{rollback_unit_number, jdbcType=VARCHAR} ,
				FAIL_TIMES = #{fail_times, jdbcType=NUMERIC} ,
				POLICY_YEAR =
				#{policy_year, jdbcType=NUMERIC} ,
				FROZEN_STATUS_BY =
				#{frozen_status_by, jdbcType=NUMERIC} ,
				BANK_USER_NAME =
				#{bank_user_name, jdbcType=VARCHAR} ,
				FEE_STATUS = #{fee_status,
				jdbcType=VARCHAR} ,
				FEE_STATUS_BY = #{fee_status_by,
				jdbcType=NUMERIC} ,
				PAY_END_DATE = #{pay_end_date, jdbcType=DATE} ,
				DERIV_TYPE = #{deriv_type, jdbcType=VARCHAR} ,
				RED_BOOKKEEPING_ID =
				#{red_bookkeeping_id, jdbcType=NUMERIC} ,
				BOOKKEEPING_BY =
				#{bookkeeping_by, jdbcType=NUMERIC} ,
				CUS_ACC_UPDATE_TIME =
				#{cus_acc_update_time, jdbcType=DATE} ,
				ARAP_FLAG = #{arap_flag,
				jdbcType=VARCHAR} ,
				BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
				BOOKKEEPING_TIME = #{bookkeeping_time, jdbcType=DATE} ,
				REFEFLAG =
				#{refeflag, jdbcType=VARCHAR} ,
				AGENT_CODE = #{agent_code,
				jdbcType=VARCHAR} ,
				AGENT_NAME = #{agent_name,
				jdbcType=VARCHAR} ,
				PREM_FREQ = #{prem_freq, jdbcType=NUMERIC} ,
				POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC},
				STATISTICAL_DATE = #{statistical_date, jdbcType=DATE},
				AREA = #{area, jdbcType=VARCHAR},
				PART = #{part, jdbcType=VARCHAR},
				"GROUP" = #{group, jdbcType=VARCHAR},
				IS_YBT_BANK_TAXT = #{is_ybt_bank_taxt, jdbcType=NUMERIC},
				BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

	<!-- 修改操作 -->
	<update id="JRQD_PA_updatePremArapById" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_ARAP ]]>
		<set>
			<trim suffixOverrides=",">
				FEE_STATUS = #{fee_status,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id}]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="JRQD_PA_findPremArapByListId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.v_prem_arap_all A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryPremArapByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="JRQD_PA_findLastPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT * FROM (SELECT *
          FROM APP___PAS__DBUSER.T_PREM_ARAP T
         WHERE T.POLICY_CODE = #{policy_code}
           AND T.UNIT_NUMBER NOT IN
               (SELECT A.ROLLBACK_UNIT_NUMBER
                  FROM APP___PAS__DBUSER.T_PREM_ARAP A
                 WHERE A.POLICY_CODE = #{policy_code}
                   AND A.DUE_TIME IS NOT NULL
                   AND A.ROLLBACK_UNIT_NUMBER IS NOT NULL)
         ORDER BY T.DUE_TIME DESC)C
		 WHERE ROWNUM = 1]]>
	</select>

	<select id="JRQD_PA_findPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID, A.STATISTICAL_DATE FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- <select id="JRQD_PA_findPremArapByArapFeeId" resultType="java.util.Map" 
		parameterType="java.util.Map"> <![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, 
		A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
		A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, 
		A.FEE_TYPE, A.SEQ_NO, A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, 
		A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
		A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, 
		A.POSTED, A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, 
		A.CUS_ACC_DETAILS_ID, A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, 
		A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, A.BUSINESS_TYPE, 
		A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, A.CERTI_TYPE, 
		A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, 
		A.MONEY_CODE, A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, 
		A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, 
		A.GROUP_NAME, A.BELNR, A.LIST_ID, A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, 
		A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, A.FROZEN_STATUS_BY, 
		A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
		A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, 
		A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, A.PREM_FREQ FROM 
		APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1 = 1 ]]> <include refid="JRQD_PA_queryPremArapByArapFeeIdCondition" 
		/> <![CDATA[ ORDER BY A.LIST_ID ]]> </select> -->


	<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.v_prem_arap_all A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<include refid="JRQD_PA_premArapWhereListCondition" />
		<if test=" rollback_unit_number != null and rollback_unit_number != ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME > #{finish_time} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.policy_chg_id = #{policy_chg_id} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="JRQD_PA_findAllPremArapCS" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.PRODUCT_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID ,
			(select t.item_id from APP___PAS__DBUSER.t_contract_product t where t.product_code = a.product_code and t.policy_code = a.policy_code and t.liability_state = '1' ) as ITEM_ID
			FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<include refid="JRQD_PA_premArapWhereListCondition" />
		<if test=" rollback_unit_number != null and rollback_unit_number != ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME > #{finish_time} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.policy_chg_id = #{policy_chg_id} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	
	<!-- 查询所有操作((获取所有的应交未缴续期并排列)) -->
	<select id="JRQD_findOrderPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <=  1000  AND A.FEE_STATUS <> '01'
			 AND A.FEE_STATUS <> '14' AND A.FEE_STATUS <> '16' AND A.FEE_STATUS <> '19'  
			]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY DUE_TIME DESC ]]>
	</select>
	<!-- zhulh -->
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <=  1000  AND FEE_TYPE IN ('G003010000','G003020100','G003030100','G003040100','G003020200'
			,'G003030200','G003030300','G003040200','P003050000','G003060000','G003070000','G003080000','G003100000'
			)]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<include refid="JRQD_PA_premArapWhereListCondition" />
	</select>


	<!-- 增加集合查询条件 -->
	<sql id="JRQD_PA_premArapWhereListCondition">
		<if test=" fee_status_list  != null  and fee_status_list.size() !=0 ">
			<![CDATA[ AND A.fee_status IN ]]>
			<foreach collection="fee_status_list" item="item" index="index"
				open="(" close=")" separator=",">
				<![CDATA[ #{item} ]]>
			</foreach>
		</if>
	</sql>
	<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllPremArapByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <=  1000  AND A.FEE_STATUS = '01' AND A.ARAP_FLAG='1' ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY BUSI_PROD_CODE,DUE_TIME ]]>
	</select>
	
	<!-- 理赔结案查询需要挂起解挂UnitNumber -->
	<select id="JRQD_PA_findUnitNumberByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				SELECT  TPA.LIST_ID,
        TPA.UNIT_NUMBER,
        TPA.BUSI_ITEM_ID,
        TPA.PRE_UNIT_NUMBER,
        TPA.PAYREFNO,
        TPA.BUSINESS_CODE,
        TPA.APPLY_CODE,
        TPA.POLICY_CODE,
        TPA.BATCH_NO,
        TPA.BUSI_PROD_CODE,
        TPA.BUSI_PROD_NAME,
        TPA.PRODUCT_CODE,
        TPA.PAY_LIAB_CODE,
        TPA.SOURCE_TABLE,
        TPA.SOURCE_TABLE_PK,
        TPA.DERIV_TYPE,
        TPA.BRANCH_CODE,
        TPA.ORGAN_CODE,
        TPA.POLICY_ORGAN_CODE,
        TPA.AGENT_CODE,
        TPA.POLICY_TYPE,
        TPA.CHANNEL_TYPE,
        TPA.PRODUCT_CHANNEL,
        TPA.BUSI_APPLY_DATE,
        TPA.AUDIT_DATE,
        TPA.STATISTICAL_DATE,
        TPA.VALIDATE_DATE,
        TPA.POLICY_YEAR,
        TPA.PREM_FREQ,
        TPA.CHARGE_YEAR,
        TPA.PAID_COUNT,
        TPA.HOLDER_ID,
        TPA.HOLDER_NAME,
        TPA.INSURED_ID,
        TPA.INSURED_NAME,
        TPA.IS_RISK_MAIN,
        TPA.IS_ITEM_MAIN,
        TPA.GROUP_CODE,
        TPA.GROUP_NAME,
        TPA.FEE_TYPE,
        TPA.WITHDRAW_TYPE,
        TPA.REFEFLAG,
        TPA.DUE_TIME,
        TPA.FINISH_TIME,
        TPA.PAY_END_DATE,
        TPA.ARAP_FLAG,
        TPA.CUSTOMER_ID,
        TPA.PAYEE_NAME,
        TPA.CERTI_TYPE,
        TPA.CERTI_CODE,
        TPA.PAYEE_PHONE,
        TPA.PAYEE_EMAIL,
        TPA.BANK_CODE,
        TPA.BANK_ACCOUNT,
        TPA.BANK_USER_NAME,
        TPA.CUSTOMER_ACCOUNT_FLAG,
        TPA.IS_BANK_ACCOUNT,
        TPA.IS_BANK_ACCOUNT_BY,
        TPA.IS_BANK_ACCOUNT_DATE,
        TPA.MONEY_CODE,
        TPA.FEE_AMOUNT,
        TPA.PAY_MODE,
        TPA.ROLLBACK_UNIT_NUMBER,
        TPA.FEE_STATUS,
        TPA.FEE_STATUS_BY,
        TPA.FEE_STATUS_DATE,
        TPA.FROZEN_STATUS,
        TPA.FROZEN_STATUS_BY,
        TPA.FROZEN_STATUS_DATE,
        TPA.IS_BANK_TEXT,
        TPA.FAIL_TIMES,
        TPA.GROUP_ID,
        TPA.SEQ_NO,
        TPA.BANK_TEXT_STATUS,
        TPA.TASK_MARK,
        TPA.IS_BANK_TEXT_BY,
        TPA.IS_BANK_TEXT_DATE,
        TPA.BOOKKEEPING_FLAG,
        TPA.POSTED,
        TPA.BOOKKEEPING_ID,
        TPA.BELNR,
        TPA.CR_BELNR,
        TPA.BOOKKEEPING_BY,
        TPA.BOOKKEEPING_TIME,
        TPA.RED_BOOKKEEPING_FLAG,
        TPA.RED_BOOKKEEPING_ID,
        TPA.RED_BELNR,
        TPA.RED_BOOKKEEPING_BY,
        TPA.RED_BOOKKEEPING_TIME,
        TPA.SERVICE_CODE,
        TPA.CIP_BANK_CODE,
        TPA.CIP_DISTRICT_BANK_CODE,
        TPA.IS_SPLIT,
        TPA.IS_SEND,
        TPA.TAX_RATE,
        TPA.AGENT_NAME,
        TPA.AREA,
        TPA.PART,
        TPA.PRODUCT_ABBR_NAME,
        TPA.SEND_DATE,
        TPA.CIP_BRANCH_BANK_CODE,
        TPA.BUSINESS_TYPE,
        TPA.FUNDS_RTN_CODE,
        TPA.CUS_ACC_FEE_AMOUNT,
        TPA.CUS_ACC_DETAILS_ID,
        TPA.CUS_ACC_UPDATE_BY,
        TPA.CUS_ACC_UPDATE_TIME,
        TPA.BANK_DEAL_DATE,
        TPA.CLAIM_NATURE,
        TPA.CLAIM_TYPE,
        TPA.POLICY_BALANCE,
        TPA.POLICY_CHG_ID,
        TPA.OPERATOR_BY,
        TPA.COVERAGE_PERIOD,
        TPA.COVERAGE_YEAR,
        TPA.BACK_TEXT_TIME,
        TPA.POS_BELNR,
        TPA.IS_BACKUP_BACK,
        TPA.IS_RECOVER_DOCUMENT,
        TPA.INSERT_BY,
        TPA.UPDATE_BY,
        TPA.INSERT_TIME,
        TPA.UPDATE_TIME,
        TPA.INSERT_TIMESTAMP,
        TPA.UPDATE_TIMESTAMP
          FROM  dev_pas.t_prem_arap tpa,
              dev_pas.t_contract_master tcm 
         where  tpa.fee_status = '00'
         and tpa.business_type IN('2002','2063')
           and  tpa.policy_code = tcm.policy_code
		]]>
		<if test=" policy_id != null and policy_id != ''  "><![CDATA[  and 	tcm.policy_id = #{policy_id} ]]></if>
	</select>
	
	<!-- 核销-为产生通知书使用 -->
	<select id="JRQD_PA_findDistictUnitNumber" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT A.UNIT_NUMBER,A.PAY_MODE,A.DUE_TIME,A.FINISH_TIME FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.FEE_STATUS = '01' AND A.ARAP_FLAG = '1' 
			AND A.DERIV_TYPE = '003' AND (A.BUSINESS_TYPE = '4003' OR A.BUSINESS_TYPE = '1005')]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" due_time != null and due_time != ''  "><![CDATA[ AND A.DUE_TIME <= #{due_time} ]]></if>
	</select>

	<!-- 续保处理使用 -->
	<select id="JRQD_PA_findDistictUnitNumberByDueTime" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT A.UNIT_NUMBER,A.PAY_MODE,A.BUSI_PROD_CODE,A.FEE_STATUS FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.ARAP_FLAG = '1' 
			AND A.DERIV_TYPE = '003']]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" business_type != null and business_type != ''  "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
	</select>

	<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllPremArapByPolicyCode1" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD, A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ,A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <=  1000  AND A.FEE_STATUS = '01' AND A.ARAP_FLAG = '1' 
			AND A.DERIV_TYPE = '003'
			]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY DUE_TIME]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllPremArapByOrganCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE A.FEE_STATUS = '01' AND A.FINISH_TIME IS NOT NULL AND A.ARAP_FLAG = '1' AND A.DERIV_TYPE = '003'  AND  A.BUSINESS_TYPE = '4003']]>
		<if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[AND A.ORGAN_CODE IN (
				SELECT T.ORGAN_CODE
			  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
			 START WITH T.ORGAN_CODE = #{organ_code}
			CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE)  ]]></if>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY DUE_TIME]]>
	</select>










	<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllPremArapByUnitNumber" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <=  1000 ]]>
		<include refid="JRQD_PA_queryPremArapByUnitNumber" />
	</select>

	<!-- 查询个数操作 -->
	<select id="JRQD_PA_findPremArapTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
	</select>
	<!-- 查询个数操作 是否做过续期 -->
	<select id="JRQD_PA_findPremArapTotal1" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1 = 1 AND A.fee_type in('G003100000','G003010000','G003020100','G003020200','G003030100','G003030100','G003040100','G003040200')  ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryPremArapForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,b.CHARGE_PERIOD, B.TASK_MARK, B.POLICY_ORGAN_CODE, B.HOLDER_NAME, B.IS_ITEM_MAIN, B.BOOKKEEPING_ID, B.BATCH_NO, B.BUSI_PROD_NAME, 
			B.PAYEE_PHONE, B.UNIT_NUMBER, B.PAID_COUNT, B.FUNDS_RTN_CODE, B.CUSTOMER_ACCOUNT_FLAG, B.FEE_TYPE, B.SEQ_NO, B.BUSI_ITEM_ID,
			B.BUSI_PROD_CODE, B.APPLY_CODE, B.FEE_STATUS_DATE, B.ORGAN_CODE, B.RED_BOOKKEEPING_TIME, B.CHANNEL_TYPE, B.IS_BANK_TEXT_DATE, 
			B.CHARGE_YEAR, B.IS_RISK_MAIN, B.IS_BANK_ACCOUNT, B.FROZEN_STATUS, B.BOOKKEEPING_FLAG, B.POSTED, 
			B.GROUP_ID, B.RED_BOOKKEEPING_BY, B.FROZEN_STATUS_DATE, B.INSURED_NAME,  B.CUS_ACC_DETAILS_ID, 
			B.INSURED_ID, B.POLICY_TYPE, B.PRODUCT_CHANNEL, B.IS_BANK_ACCOUNT_DATE, B.POLICY_CODE, B.PAY_MODE, B.OPERATOR_BY, 
			B.BRANCH_CODE, B.BUSINESS_TYPE, B.VALIDATE_DATE, B.RED_BELNR, B.BANK_TEXT_STATUS, 
			B.GROUP_CODE, B.CERTI_TYPE, B.CUS_ACC_UPDATE_BY, B.IS_BANK_TEXT_BY, B.IS_BANK_ACCOUNT_BY, B.BUSINESS_CODE, B.MONEY_CODE, 
			B.PAYEE_NAME, B.BANK_ACCOUNT, B.RED_BOOKKEEPING_FLAG, B.CUS_ACC_FEE_AMOUNT, B.CUSTOMER_ID, B.FINISH_TIME, 
			B.DUE_TIME, B.IS_BANK_TEXT, B.CERTI_CODE, B.BUSI_APPLY_DATE, B.GROUP_NAME, B.BELNR, B.LIST_ID, 
			B.FEE_AMOUNT, B.SERVICE_CODE, B.HOLDER_ID, B.WITHDRAW_TYPE, B.ROLLBACK_UNIT_NUMBER, B.FAIL_TIMES, 
			B.POLICY_YEAR, B.FROZEN_STATUS_BY, B.BANK_USER_NAME, B.FEE_STATUS, B.FEE_STATUS_BY, B.PAY_END_DATE, B.DERIV_TYPE, 
			B.RED_BOOKKEEPING_ID, B.BOOKKEEPING_BY, B.CUS_ACC_UPDATE_TIME, B.ARAP_FLAG, B.BANK_CODE, B.BOOKKEEPING_TIME, B.REFEFLAG, 
			B.AGENT_CODE, B.PREM_FREQ FROM (
					SELECT ROWNUM RN, A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>


	<!-- 去重复查询unit_number -->
	<select id="JRQD_CS_findUintNumber" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT A.UNIT_NUMBER FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE  ROWNUM <=  1000  
		 ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
	</select>


	<!-- 修改操作 -->
	<update id="JRQD_CSS_updatePremArapByUnitNumber" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_ARAP ]]>
		<set>
			<trim suffixOverrides=",">
				FEE_STATUS = #{fee_status,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE  UNIT_NUMBER = #{unit_number}]]>
	</update>


	<!-- 修改操作 -->
	<update id="JRQD_CSF_updatePremArapByUnitNumber" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_ARAP ]]>
		<set>
			<trim suffixOverrides=",">
				FEE_STATUS = #{fee_status,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE  UNIT_NUMBER = #{unit_number}]]>
	</update>

	<!-- 查询所有操作 -->
	<select id="JRQD_CS_findAllPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">

		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID,A.PRODUCT_CODE FROM APP___PAS__DBUSER.T_PREM_ARAP A 
			WHERE ROWNUM <=  1000  ]]>

		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY UNIT_NUMBER ]]>
	</select>


	<!-- 查询所有操作 -->
	<select id="JRQD_CS_findAllPremArapAll" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <=  1000   ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY UNIT_NUMBER ]]>
	</select>

	<!-- 生存保险金追回 查询 -->
	<select id="JRQD_CS_findRetreivePremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.TASK_MARK,
       A.POLICY_ORGAN_CODE,
       A.HOLDER_NAME,
       A.IS_ITEM_MAIN,
       A.BOOKKEEPING_ID,
       A.BATCH_NO,
       A.BUSI_PROD_NAME,
       A.PAYEE_PHONE,
       A.UNIT_NUMBER,
       A.PAID_COUNT,
       A.FUNDS_RTN_CODE,
       A.CUSTOMER_ACCOUNT_FLAG,
       A.FEE_TYPE,
       A.SEQ_NO,
       A.BUSI_PROD_CODE,
       A.APPLY_CODE,
       A.FEE_STATUS_DATE,
       A.ORGAN_CODE,
       A.RED_BOOKKEEPING_TIME,
       A.CHANNEL_TYPE,
       A.IS_BANK_TEXT_DATE,
       A.CHARGE_YEAR,
       A.IS_RISK_MAIN,
       A.IS_BANK_ACCOUNT,
       A.FROZEN_STATUS,
       A.BOOKKEEPING_FLAG,
       A.POSTED,
       A.GROUP_ID,
       A.RED_BOOKKEEPING_BY,
       A.FROZEN_STATUS_DATE,
       A.INSURED_NAME,
       A.CUS_ACC_DETAILS_ID,
       A.INSURED_ID,
       A.POLICY_TYPE,
       A.PRODUCT_CHANNEL,
       A.IS_BANK_ACCOUNT_DATE,
       A.POLICY_CODE,
       A.PAY_MODE,
       A.OPERATOR_BY,
       A.BRANCH_CODE,
       A.BUSINESS_TYPE,
       A.VALIDATE_DATE,
       A.RED_BELNR,
       A.BANK_TEXT_STATUS,
       A.GROUP_CODE,
       A.CERTI_TYPE,
       A.CUS_ACC_UPDATE_BY,
       A.IS_BANK_TEXT_BY,
       A.IS_BANK_ACCOUNT_BY,
       A.BUSINESS_CODE,
       A.MONEY_CODE,
       A.PAYEE_NAME,
       A.BANK_ACCOUNT,
       A.RED_BOOKKEEPING_FLAG,
       A.CUS_ACC_FEE_AMOUNT,
       A.CUSTOMER_ID,
       A.FINISH_TIME,
       A.DUE_TIME,
       A.IS_BANK_TEXT,
       A.CERTI_CODE,
       A.BUSI_APPLY_DATE,
       A.GROUP_NAME,
       A.BELNR,
       A.LIST_ID,
       A.FEE_AMOUNT,
       A.SERVICE_CODE,
       A.HOLDER_ID,
       A.WITHDRAW_TYPE,
       A.ROLLBACK_UNIT_NUMBER,
       A.FAIL_TIMES,
       A.POLICY_YEAR,
       A.FROZEN_STATUS_BY,
       A.BANK_USER_NAME,
       A.FEE_STATUS,
       A.FEE_STATUS_BY,
       A.PAY_END_DATE,
       A.DERIV_TYPE,
       A.RED_BOOKKEEPING_ID,
       A.BOOKKEEPING_BY,
       A.CUS_ACC_UPDATE_TIME,
       A.ARAP_FLAG,
       A.BANK_CODE,
       A.BOOKKEEPING_TIME,
       A.REFEFLAG,
       A.AGENT_CODE,
       A.PREM_FREQ,
       A.POLICY_CHG_ID
  FROM APP___PAS__DBUSER.v_prem_arap_all A
 WHERE ROWNUM <= 1000
    and a.arap_flag='2'
   AND A.FEE_STATUS in ('01', '16', '19', '00')
			]]>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code}]]></if>
		<if test=" busi_prod_code != null and policy_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code}]]></if>
		<if test=" due_time != null "><![CDATA[ AND A.DUE_TIME >=  #{due_time}]]></if>
		<if test=" finish_time != null "><![CDATA[ AND A.FINISH_TIME >  #{finish_time}]]></if>
		<![CDATA[ ORDER BY A.DUE_TIME ASC ]]>
	</select>
	<!-- 生存保险金追回 查询 -->
	<select id="JRQD_CS_findRetreivePremArapTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PREM_ARAP A 
			WHERE ROWNUM <=  1000   AND FEE_TYPE IN ('P004620100','P004620200','P004620000','P004390100','P004390200','P004390300','T003100100','T003100200','T003110100','T003110200') AND A.FEE_STATUS in('01','16','19','00')  
			]]>
		<![CDATA[ AND POLICY_CODE = #{policy_code}]]>
		<![CDATA[ AND DUE_TIME >=  #{due_time}  ORDER BY UNIT_NUMBER ]]>
	</select>
	<!-- 生存保险金追回 查询 -->
	<select id="JRQD_CS_findRetreivePremArapPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.TASK_MARK, B.POLICY_ORGAN_CODE, B.HOLDER_NAME, B.IS_ITEM_MAIN, B.BOOKKEEPING_ID, B.BATCH_NO, B.BUSI_PROD_NAME, 
			B.PAYEE_PHONE, B.UNIT_NUMBER, B.PAID_COUNT, B.FUNDS_RTN_CODE, B.CUSTOMER_ACCOUNT_FLAG, B.FEE_TYPE, B.SEQ_NO, B.BUSI_ITEM_ID,
			B.BUSI_PROD_CODE, B.APPLY_CODE, B.FEE_STATUS_DATE, B.ORGAN_CODE, B.RED_BOOKKEEPING_TIME, B.CHANNEL_TYPE, B.IS_BANK_TEXT_DATE, 
			B.CHARGE_YEAR, B.IS_RISK_MAIN, B.IS_BANK_ACCOUNT, B.FROZEN_STATUS, B.BOOKKEEPING_FLAG, B.POSTED, 
			B.GROUP_ID, B.RED_BOOKKEEPING_BY, B.FROZEN_STATUS_DATE, B.INSURED_NAME,  B.CUS_ACC_DETAILS_ID, 
			B.INSURED_ID, B.POLICY_TYPE, B.PRODUCT_CHANNEL, B.IS_BANK_ACCOUNT_DATE, B.POLICY_CODE, B.PAY_MODE, B.OPERATOR_BY, 
			B.BRANCH_CODE, B.BUSINESS_TYPE, B.VALIDATE_DATE, B.RED_BELNR, B.BANK_TEXT_STATUS, 
			B.GROUP_CODE, B.CERTI_TYPE, B.CUS_ACC_UPDATE_BY, B.IS_BANK_TEXT_BY, B.IS_BANK_ACCOUNT_BY, B.BUSINESS_CODE, B.MONEY_CODE, 
			B.PAYEE_NAME, B.BANK_ACCOUNT, B.RED_BOOKKEEPING_FLAG, B.CUS_ACC_FEE_AMOUNT, B.CUSTOMER_ID, B.FINISH_TIME, 
			B.DUE_TIME, B.IS_BANK_TEXT, B.CERTI_CODE, B.BUSI_APPLY_DATE, B.GROUP_NAME, B.BELNR, B.LIST_ID, 
			B.FEE_AMOUNT, B.SERVICE_CODE, B.HOLDER_ID, B.WITHDRAW_TYPE, B.ROLLBACK_UNIT_NUMBER, B.FAIL_TIMES, 
			B.POLICY_YEAR, B.FROZEN_STATUS_BY, B.BANK_USER_NAME, B.FEE_STATUS, B.FEE_STATUS_BY, B.PAY_END_DATE, B.DERIV_TYPE, 
			B.RED_BOOKKEEPING_ID, B.BOOKKEEPING_BY, B.CUS_ACC_UPDATE_TIME, B.ARAP_FLAG, B.BANK_CODE, B.BOOKKEEPING_TIME, B.REFEFLAG, 
			B.AGENT_CODE, B.PREM_FREQ FROM (
					SELECT ROWNUM RN, A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.v_prem_arap_all A WHERE A.FEE_TYPE IN ('P004620100','P004620200','P004620000','P004390100','P004390200','P004390300','T003100100','T003100200','T003110100','T003110200') AND A.FEE_STATUS in('01','16','19','00')  ]]>
		<![CDATA[ AND A.POLICY_CODE = #{policy_code}]]>
		<![CDATA[ AND A.DUE_TIME >=  #{due_time}]]>
		<![CDATA[ AND ROWNUM <= #{LESS_NUM} ]]>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 修改操作 -->
	<update id="JRQD_CS_updatePremArapByUnitNumber" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_ARAP ]]>
		<set>
			<trim suffixOverrides=",">
				FEE_STATUS = #{fee_status,
				jdbcType=VARCHAR} ,
				FINISH_TIME = #{finish_time, jdbcType=DATE} ,
			</trim>
		</set>
		<![CDATA[ WHERE  UNIT_NUMBER = #{unit_number}]]>
	</update>

	<!-- 修改操作 -->
	<update id="JRQD_PA_updatePremArapByUnitNumber" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_ARAP ]]>
		<set>
			<trim suffixOverrides=",">
				FEE_STATUS = #{fee_status,
				jdbcType=VARCHAR} ,
			</trim>
		</set>
		<![CDATA[ WHERE  UNIT_NUMBER = #{unit_number} AND BUSI_PROD_CODE = #{busi_prod_code}]]>
	</update>


	<!-- 查询所有操作 -->
	<select id="JRQD_CS_findPremArapBySyncFlag" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  select (case when 0 <=
               (select (select nvl(sum(FEE_AMOUNT), 0.00) S_SUM_FEE_AMOUNT
                          from APP___PAS__DBUSER.T_PREM_ARAP
                         where
                            business_code =  #{business_code}
                           and deriv_type = '004'and fee_status='00') -
                       (select nvl(sum(FEE_AMOUNT), 0.00) F_SUM_FEE_AMOUNT
                          from APP___PAS__DBUSER.T_PREM_ARAP
                         where fee_status='00'
                           and business_code = #{business_code}
                           and deriv_type = '004' and fee_status='00') c
                  from dual) then 1 else 0 end) need_Prem_Flag,
        (select distinct business_code
           from APP___PAS__DBUSER.T_PREM_ARAP
          where business_code = #{business_code}
            and deriv_type = '004' and fee_status='00') business_code from dual    ]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllPremArapByConditions" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID,A.POLICY_CODE, A.BUSI_PROD_CODE, A.FEE_STATUS, A.DUE_TIME,A.FINISH_TIME , A.fee_amount
		            FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE A.POLICY_CODE = #{policy_code} AND A.DERIV_TYPE = '003' AND A.BUSINESS_TYPE = '4003' AND A.FEE_TYPE IN ('G003010000',
                        'G003020100',
                        'G003030100',
                        'G003040100',
                        'G003020200',
                        'G003030200',
                        'G003030300',
                        'G003040200',
                        'P003050000',
                        'G003060000',
                        'G003070000',
                        'G003080000') ]]>
		<if test=" busi_prod_code != null and busi_prod_code != '' "> 
			       <![CDATA[  AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]>
		</if>
		<if
			test="startTime != null and startTime != '' and endTime !=null and endTime != ''"> 
		           <![CDATA[  AND A.DUE_TIME >= #{startTime}]]>
		           <![CDATA[  AND A.DUE_TIME <= #{endTime}]]>
		</if>
		<if test=" modnum != null and start != null  "><![CDATA[ AND MOD(A.LIST_ID , #{modnum}) = #{start} AND ROWNUM <= 5000 ]]></if>	
	</select>

	<!--续期信息接口查询 -->
	<select id="JRQD_findPremArapByPolicyCodeDueTime" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATISTICAL_DATE,A.FINISH_TIME,a.unit_number,A.BUSI_PROD_NAME,A.BUSI_PROD_CODE,A.PAY_MODE,B.NAME PAY_MODE_NAME, A.FEE_TYPE, C.TYPE_NAME FEE_TYPE_NAME, A.DUE_TIME,A.FEE_AMOUNT,A.FEE_STATUS_DATE,A.FEE_STATUS, D.STATUS_NAME FEE_STATUS_NAME
		            FROM APP___PAS__DBUSER.T_PREM_ARAP A 
                    LEFT OUTER JOIN APP___PAS__DBUSER.T_PAY_MODE B ON A.PAY_MODE=B.CODE
                    LEFT OUTER JOIN APP___PAS__DBUSER.T_FEE_TYPE C ON A.FEE_TYPE=C.CODE
                    LEFT OUTER JOIN APP___PAS__DBUSER.T_FEE_STATUS D ON A.FEE_STATUS=D.STATUS_CODE
		            WHERE A.POLICY_CODE = #{policy_code} 
		            ]]>
		<if
			test="startDueTime != null and startDueTime != '' and endDueTime !=null and endDueTime != ''"> 
		           <![CDATA[ and to_char(A.DUE_TIME,'yyyy-MM-dd') >= #{startDueTime}]]>
		           <![CDATA[ and to_char(A.DUE_TIME,'yyyy-MM-dd') <= #{endDueTime}]]>
		</if>
	</select>

	<!-- 根据应收应付类型计算实收金额fee_amount合计 -->
	<select id="JRQD_PA_sumFeeAmountByArapFlagAndPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			select sum(nvl(fee_amount,0)) as fee_amount from APP___PAS__DBUSER.T_PREM_ARAP 
			where POLICY_CODE=#{policy_code} and ARAP_FLAG=#{arap_flag}
		]]>
	</select>

	<!-- 查询该保单当期保费缴费状态 业务来源为003 续保 -->
	<select id="JRQD_CS_findPremArapByStdPremAfStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_STATUS,A.DUE_TIME FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.DERIV_TYPE='003' AND A.POLICY_CODE=#{policy_code} AND A.BUSI_PROD_CODE=#{busi_prod_code}
          AND A.DUE_TIME=(SELECT MAX(B.DUE_TIME) FROM APP___PAS__DBUSER.T_PREM_ARAP B WHERE B.DERIV_TYPE='003' AND B.POLICY_CODE=#{policy_code} AND B.BUSI_PROD_CODE=#{busi_prod_code})]]>
	</select>


	<!-- 查询该保单此时之前的所有续期费用 业务来源为003 续保 -->
	<select id="JRQD_CS_findAllPremArapBeforeDueTime" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_STATUS FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.DERIV_TYPE='003' AND A.POLICY_CODE=#{policy_code}
			 AND A.BUSI_PROD_CODE=#{busi_prod_code} AND A.DUE_TIME<=#{due_time}]]>
	</select>

	<select id="JRQD_CS_findAllPremArapBeforeDueTimeForFM" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.DERIV_TYPE='003' AND A.FEE_STATUS = '00'
			 ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" due_time != null and due_time != ''  "><![CDATA[  AND A.DUE_TIME <= #{due_time} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" fee_type != null and fee_type != ''  "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</select>

	<!-- 查询是否存在未收费的记录条数 -->
	<select id="JRQD_CS_findPremArapForNoneFeeStatus" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} AND A.ARAP_FLAG = '1' AND A.FEE_STATUS != '01']]>
	</select>



	<!-- 续期冲正查询条件 -->
	<sql id="JRQD_PA_findPremArapListCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.policy_code=#{policy_code} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND TRIM(A.FEE_STATUS) = TRIM(#{fee_status}) ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND TRIM(A.ARAP_FLAG) = TRIM(#{arap_flag}) ]]></if>
	</sql>

	<!-- 续期冲正查询列表 -->
	<select id="JRQD_PA_findPremArapList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT *
  				FROM APP___PAS__DBUSER.T_PREM_ARAP TPA
 				WHERE TPA.POLICY_CODE =#{policy_code} AND TPA.FEE_STATUS = '01' AND TPA.BUSINESS_CODE = '4003'
 				ORDER BY TPA.FINISH_TIME DESC]]>
	</select>

	<!-- 查询欠缴保费 -->
	<select id="JRQD_CS_findPolicyFeeInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID  FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.business_type = '4003'  and A.fee_status <> '01'   and A.fee_status <> '14'
		   and A.fee_status <> '16' and A.fee_status <> '19'  and A.arap_flag = 1 ]]>
		<if test=" due_time != null and due_time != ''  "><![CDATA[ AND  A.due_time <= #{due_time} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.policy_code = #{policy_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = TRIM(#{busi_prod_code}) ]]></if>
	</select>

	<!-- 续收查询 -->
	<select id="JRQD_queryPremArapInfos" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		
		SELECT DISTINCT A.POLICY_CODE , A.PREM_FREQ , A.HOLDER_NAME , A.DUE_TIME , A.BUSI_PROD_NAME ,A.FEE_AMOUNT ,B.NAME , A.VALIDATE_DATE ,A.FEE_STATUS ,C.NEXT_PREM
           FROM( APP___PAS__DBUSER.T_PREM_ARAP A INNER JOIN APP___PAS__DBUSER.T_PAY_MODE B ON A.PAY_MODE = B.CODE ) LEFT JOIN  T_CONTRACT_EXTEND C ON  C.POLICY_CODE = A.POLICY_CODE WHERE A.POLICY_CODE =  #{policy_code}  AND A.PREM_FREQ IS NOT NULL
		]]>

		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
	</select>


	<!-- 生存领取历史划款信息查询 -->
	<!--2018/06/27 原来的查询条件：A.BUSINESS_CODE = #{unit_number}，改为A.UNIT_NUMBER = #{unit_number}  -->
	<select id="JRQD_queryPayDueHistoryTransferList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_PROD_NAME,A.BUSI_PROD_CODE,A.FEE_AMOUNT,A.BANK_ACCOUNT,A.BANK_USER_NAME,A.BANK_CODE,B.BANK_NAME,A.IS_BANK_TEXT_DATE,A.FEE_STATUS_DATE,
		A.BANK_TEXT_STATUS,A.FEE_STATUS,A.FINISH_TIME FROM APP___PAS__DBUSER.T_PREM_ARAP A LEFT JOIN T_BANK  B ON A.BANK_CODE = B.BANK_CODE WHERE 
		A.UNIT_NUMBER = #{unit_number} 
		 ]]>
		<include refid="JRQD_queryPayDueHistoryTransferList_cjk"></include>
	</select>

	<!-- 查询未实收的续期 -->
	<select id="JRQD_cs_findLastPayInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000 AND A.FEE_STATUS != '01'

			-----START--2016/07/15--sunxs_wb--修改TC5329----
			--AND A.FEE_STATUS != '02' 
			AND A.FEE_STATUS != '14' AND A.FEE_STATUS != '16' AND A.FEE_STATUS != '19'  
			-----E N D--2016/07/15--sunxs_wb--修改TC5329---- 
			
			AND A.FEE_TYPE IN('43','G003010000','G003020200','G003020100') ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY A.DUE_TIME ASC ]]>
	</select>

	<!-- 查询不同费用类型的应收应付 -->
	<select id="JRQD_find_policyId_premarap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
				select rownum rn,t.fee_type,
			       t.unit_number,
			       t.fee_status,
			       t.policy_code
			  from APP___PAS__DBUSER.t_prem_arap       t,
			       APP___PAS__DBUSER.t_contract_master tc
			 where t.policy_code = tc.policy_code
			   and tc.policy_id = #{policy_id}			
		]]>
		<if test=" fee_types != null and fee_types.size() !=0 ">
			<![CDATA[ AND t.fee_type IN ]]>
			<foreach collection="fee_types" item="fee_types" index="index"
				open="(" close=")" separator=",">#{fee_types}</foreach>
		</if>
		<![CDATA[and rownum <= 1000 ]]>
	</select>

	<!-- 查询当前日期在交费对应日后的数据,包括费用状态名称 -->
	<select id="JRQD_findPremArapListWithStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO,A.BUSI_ITEM_ID, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID, B.STATUS_NAME FROM APP___PAS__DBUSER.T_PREM_ARAP A 
			LEFT OUTER JOIN APP___PAS__DBUSER.T_FEE_STATUS B 
			ON A.FEE_STATUS=B.STATUS_CODE 
			WHERE A.DUE_TIME < SYSDATE AND ROWNUM <=  1000 ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
	</select>
	<!--续期信息接口查询总数 -->
	<select id="JRQD_findPremArapByPolicyCodeDueTimeCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ Select count(1) from (SELECT a.unit_number,A.BUSI_PROD_NAME,A.BUSI_PROD_CODE,A.PAY_MODE,B.NAME PAY_MODE_NAME, A.FEE_TYPE, C.TYPE_NAME FEE_TYPE_NAME, A.DUE_TIME,A.FEE_AMOUNT,A.FEE_STATUS_DATE,A.FEE_STATUS, D.STATUS_NAME FEE_STATUS_NAME
		            FROM APP___PAS__DBUSER.T_PREM_ARAP A 
                    LEFT OUTER JOIN APP___PAS__DBUSER.T_PAY_MODE B ON A.PAY_MODE=B.CODE
                    LEFT OUTER JOIN APP___PAS__DBUSER.T_FEE_TYPE C ON A.FEE_TYPE=C.CODE
                    LEFT OUTER JOIN APP___PAS__DBUSER.T_FEE_STATUS D ON A.FEE_STATUS=D.STATUS_CODE
		            WHERE A.POLICY_CODE = #{policy_code} 
		           ) ]]>
		<if
			test="startDueTime != null and startDueTime != '' and endDueTime !=null and endDueTime != ''"> 
		           <![CDATA[ and to_char(A.DUE_TIME,'yyyy-MM-dd') >= #{startDueTime}]]>
		           <![CDATA[ and to_char(A.DUE_TIME,'yyyy-MM-dd') <= #{endDueTime}]]>
		</if>
	</select>
	<!-- 查询续期保费信息 -->
	<select id="JRQD_CS_findPayedPrem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID,
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  AND A.FEE_STATUS IN ('01','19','16') AND (A.FEE_TYPE='G003010000' OR (A.FEE_TYPE='G004010000' AND A.SERVICE_CODE='RE') )]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 一个受理下的收付费统和 -->
	<select id="JRQD_CS_findAllFeeAmountForAccept" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT NVL(SUM(DECODE(A.ARAP_FLAG, 2, 0 - A.FEE_AMOUNT, A.FEE_AMOUNT)),0) FEE_AMOUNT
		     FROM APP___PAS__DBUSER.T_CS_PREM_ARAP A
		    WHERE A.BUSINESS_CODE = #{business_code}
		      AND A.FEE_STATUS NOT IN ('16', '02')
	</select>

	<!-- 查找保全申请提交日期之前的所有未缴费记录 -->
	<select id="JRQD_findPremArapbeforApplyDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
            SELECT A.CHARGE_PERIOD,A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BUSI_PROD_NAME, A.PAYEE_PHONE, A.UNIT_NUMBER, 
			A.PAID_COUNT, A.FEE_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.POSTED, A.INSURED_NAME, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
			A.LIST_ID, A.CERTI_TYPE, A.BUSINESS_CODE, A.MONEY_CODE, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.FINISH_TIME, A.DUE_TIME, A.CERTI_CODE, A.BUSI_APPLY_DATE, 
			A.FEE_AMOUNT, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.POLICY_YEAR, 
			A.BANK_USER_NAME, A.FEE_STATUS, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.ARAP_FLAG, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID ,A.CUSTOMER_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A 
      WHERE A.DUE_TIME <=#{busi_apply_date} and A.fee_status in ('00','03','04')
      AND A.POLICY_CODE=#{policy_code} AND A.BUSI_PROD_CODE=#{busi_prod_code}
      ORDER BY LIST_ID ]]>
	</select>

	<select id="JRQD_queryFeeCountByBusinessCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT T.FEE_AMOUNT
		FROM APP___PAS__DBUSER.T_PREM_ARAP T
		WHERE T.FEE_STATUS = '01'
		AND T.ARAP_FLAG = '1'
		AND T.BUSINESS_CODE = #{business_code}
	</select>

	<select id="JRQD_findMoreFeeAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[
		SELECT A.CHARGE_PERIOD,A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BUSI_PROD_NAME, A.PAYEE_PHONE, A.UNIT_NUMBER, 
      A.PAID_COUNT, A.FEE_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
      A.CHARGE_YEAR, A.IS_RISK_MAIN, A.POSTED, A.INSURED_NAME, A.INSURED_ID, 
      A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.POLICY_CODE, A.PAY_MODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.BUSINESS_TYPE, 
      A.LIST_ID, A.CERTI_TYPE, A.BUSINESS_CODE, A.MONEY_CODE, A.PAYEE_NAME, 
      A.BANK_ACCOUNT, A.FINISH_TIME, A.DUE_TIME, A.CERTI_CODE, A.BUSI_APPLY_DATE, 
      A.FEE_AMOUNT, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.POLICY_YEAR, 
      A.BANK_USER_NAME, A.FEE_STATUS, A.PAY_END_DATE, A.DERIV_TYPE, 
      A.ARAP_FLAG, A.BANK_CODE, A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID ,A.CUSTOMER_ID
  FROM APP___PAS__DBUSER.T_PREM_ARAP A, dev_pas.t_contract_busi_prod tcbp
 WHERE A.FEE_STATUS IN ('01', '19')
   AND A.POLICY_CODE = #{policy_code}
   AND A.ROLLBACK_UNIT_NUMBER IS NULL
    AND A.ARAP_FLAG = 1 
    AND  A.FEE_TYPE IN ('G003010000','G004510300','G004740300','G004510100','G004510200','G004740100','G004740200','G004010000','G003100000','G003020100')
    AND (A.FROZEN_STATUS <> '04' OR A.FROZEN_STATUS IS NULL)
   AND A.UNIT_NUMBER NOT IN
       (SELECT T.ROLLBACK_UNIT_NUMBER
          FROM APP___PAS__DBUSER.T_PREM_ARAP T
         WHERE T.FEE_STATUS IN ('01', '19')
           AND T.POLICY_CODE = #{policy_code}
           AND T.ROLLBACK_UNIT_NUMBER IS NOT NULL)
           AND TCBP.POLICY_CODE = A.POLICY_CODE
           AND TCBP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
       ]]>

		<if test = "waiver_end != null and waiver_start != null">
			<![CDATA[
           AND ((A.DUE_TIME < TCBP.WAIVER_START) OR (A.DUE_TIME > TCBP.WAIVER_END))
			]]>
		</if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  ">
			<![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]>
		</if>
	</select>

	<select id="JRQD_PA_sumFeeAmountByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		select case when sum(t.FEE_AMOUNT) > 0 then 1
		else 0 end as capital_balance
		from APP___PAS__DBUSER.T_PREM_ARAP t
		where t.arap_flag = '2' and t.fee_status = '00' and t.policy_code =
		#{policy_code}

	</select>

	<!-- 续期缴费提交接口 使用 -->
	
	<select id="JRQD_PA_queryPremArapPOFroRenewalPayment" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[

		 	    select unit_number ,due_time ,pay_end_date ,fee_status,sum(fee_amount) as fee_amount
            from (select t.unit_number , t.due_time ,t.pay_end_date ,t.fee_status,t.fee_amount
            from APP___PAS__DBUSER.t_prem_arap t
                   where t.policy_code =#{policy_code}
                     and t.deriv_type = '003'
                     and t.fee_status  in ('00', '03')
                     and t.fee_type in('G003100000',
                                    'G003010000',
                                    'G003020100',
                                    'G003020200',
                                    'G003030100',
                                    'G003030200',
                                    'G003040100',
                                    'G003040200')
                   order by t.due_time desc)
                   group by unit_number ,due_time ,pay_end_date ,fee_status 
		]]>
	</select>
	
	
	<select id="JRQD_PA_queryMasterBusiInfoByContNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select t.fee_status
			from dev_pas.t_prem_arap t
			where t.policy_code = #{policy_code}
			and t.deriv_type = '003'
			and t.fee_status = '01'
			and t.fee_type in ('G003100000',
			'G003010000',
			'G003020100',
			'G003020200',
			'G003030100',
			'G003030200',
			'G003040100',
			'G003040200')
			and t.busi_prod_code in
			(select t.busi_prod_code
			from dev_pas.t_contract_busi_prod a
			where a.busi_prod_code = t.busi_prod_code
			and a.master_busi_item_id is null)
		]]>
	</select>

	<!-- 期缴费提交接口使用 查询同一个 unit_number 下的金额 -->

	<select id="JRQD_PA_querySumPremArapFeeAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			select sum(a.fee_amount) sumamount from APP___PAS__DBUSER.t_prem_arap a where
			
			a.fee_type in ('G003100000','G003010000','G003020100','G003020200', 'G003030100','G003030200','G003040100', 'G003040200') 
			
			and a.unit_number=#{unit_number} and a.fee_status  in ('00', '03')
		]]>
	</select>

	<select id="JRQD_querySumFeeAmountByPolicyCodeLastDueTime"
		resultType="java.util.Map" parameterType="java.util.Map">
		SELECT SUM(T.FEE_AMOUNT)
		SUM_FEE_AMOUNT
		FROM APP___PAS__DBUSER.T_PREM_ARAP T
		WHERE T.POLICY_CODE = #{policy_code}
		AND T.DUE_TIME =
		(SELECT MAX(T.DUE_TIME) FROM APP___PAS__DBUSER.T_PREM_ARAP T
		WHERE T.POLICY_CODE = #{policy_code})
		AND T.FEE_STATUS NOT IN ('01'
		,'16','19')
		GROUP BY T.DUE_TIME
	</select>

	<select id="JRQD_PA_querySumFeeAmountByPolicyCodeBeforeDueTime"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
					SELECT SUM(C.FEE_AMOUNT) as FEE_AMOUNT
					  FROM APP___PAS__DBUSER.T_PREM_ARAP C
					 WHERE C.UNIT_NUMBER IN
					       (SELECT DISTINCT A.UNIT_NUMBER
					          FROM APP___PAS__DBUSER.T_PREM A, APP___PAS__DBUSER.T_CONTRACT_PRODUCT B
					         WHERE A.ITEM_ID = B.ITEM_ID
					           AND B.POLICY_CODE = #{policy_code})
					   AND C.POLICY_CODE = #{policy_code}
					   AND ((C.FEE_STATUS = '19') OR (C.FEE_STATUS = '01'))
					   AND (C.BUSINESS_TYPE = #{business_type} OR C.BUSINESS_TYPE = '2050')
					   AND (C.DERIV_TYPE =  #{deriv_type} OR C.DERIV_TYPE = '004')
					   AND C.ARAP_FLAG = '1'
					   AND C.BUSI_PROD_CODE = #{busi_prod_code}
					   AND C.DUE_TIME <= #{due_time}
					   AND C.FEE_TYPE IN ('G003010000','G004510300','G004740300','G004510100','G004510200','G004740100','G004740200','G004010000') 
					   ]]>
	</select>


	<select id="JRQD_PA_queryRenewInfoByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT PAY_TYPE,
       DERIV_TYPE,
       UNIT_NUMBER,
       BUSINESS_CODE,
       BUSI_PROD_CODE,
       FEE_STATUS,
       DUE_TIME,
       PAY_MODE,
       sum (FEE_AMOUNT)  FEE_AMOUNT, 
       SALECHNL,
       PAID_COUNT,
       RISKNAME,
       FINISH_TIME,
       FUNDS_RTN_CODE,
       CODENAME,
       FEE_STATUS_NAME
  FROM (SELECT (CASE
                 WHEN B.DERIV_TYPE = '003' AND
                      (B.FEE_TYPE = 'G003010000' OR B.FEE_TYPE = 'G003100000' OR
                      B.FEE_TYPE = 'G003010001' OR B.FEE_TYPE = 'G003100001') AND
                      (B.IS_WAIVED = 1 AND B.DUE_TIME >= B.WAIVER_START AND
                      B.DUE_TIME <= B.WAIVER_END) THEN
                  '豁免'
                 WHEN B.DERIV_TYPE = '003' AND
                      (B.FEE_TYPE = 'G003010000' OR B.FEE_TYPE = 'G003100000' OR
                      B.FEE_TYPE = 'G003010001' OR B.FEE_TYPE = 'G003100001' OR
                      B.FEE_TYPE = 'G003020200' OR B.FEE_TYPE = 'G003020100' OR
                      B.FEE_TYPE = 'G003030200' OR B.FEE_TYPE = 'G003030100') AND
                      NOT (B.IS_WAIVED = 1 AND B.DUE_TIME >= B.WAIVER_START AND
                       B.DUE_TIME <= B.WAIVER_END) THEN
                  '续期正常交费'
                 WHEN (B.DERIV_TYPE = '004' AND B.FEE_TYPE = 'G004010000' OR
                      B.FEE_TYPE = 'G004010001') THEN
                  '保单复效'
                 WHEN (B.DERIV_TYPE = '005' AND B.FEE_TYPE = 'G005060000' OR
                      B.FEE_TYPE = 'G005060001') THEN
                  '理赔金扣交'
               END) AS PAY_TYPE,
               B.*
          FROM (SELECT (SELECT PRODUCT_NAME_SYS
                          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT
                         WHERE PRODUCT_CODE_SYS = PAR.BUSI_PROD_CODE) RISKNAME,
                       (SELECT BANK_RET_NAME
                          FROM APP___PAS__DBUSER.T_BANK_RET_CONF
                         WHERE BANK_RET_CODE = PAR.FUNDS_RTN_CODE) CODENAME,
                       (SELECT PAY_LOCATION
                          FROM APP___PAS__DBUSER.T_PAYER_ACCOUNT
                         WHERE POLICY_ID =
                               (SELECT POLICY_ID
                                  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER
                                 WHERE POLICY_CODE = PAR.POLICY_CODE)) SALECHNL,
                       PAR.FUNDS_RTN_CODE,
                       PAR.BUSI_PROD_CODE,
                       PAR.FINISH_TIME,
                       PAR.PAID_COUNT,
                       PAR.DERIV_TYPE,
                       PAR.UNIT_NUMBER,
                       PAR.BUSINESS_CODE,
                       PAR.BUSI_PROD_CODE AS PRODUCT_NAME_SYS,
                       (CASE
                         WHEN PAR.FEE_STATUS = '16' THEN
                          '01'
                         ELSE
                          PAR.FEE_STATUS
                       END) AS FEE_STATUS,
                       PAR.DUE_TIME,
                       DECODE((SELECT CD.PAY_MODE FROM APP___CAP__DBUSER.T_CASH_DETAIL CD 
                       WHERE UNIT_NUMBER = PAR.UNIT_NUMBER AND ROWNUM = 1),'',PAR.PAY_MODE,
                       (SELECT CD.PAY_MODE FROM APP___CAP__DBUSER.T_CASH_DETAIL CD 
                       WHERE UNIT_NUMBER = PAR.UNIT_NUMBER AND ROWNUM = 1)) PAY_MODE,
                       (CASE
                         WHEN ARAP_FLAG = '1' THEN
                          PAR.FEE_AMOUNT
                         ELSE
                          PAR.FEE_AMOUNT * -1
                       END) AS FEE_AMOUNT,
                       CBP.APL_PERMIT,
                       PAR.FEE_TYPE,
                       CBP.IS_WAIVED,
                       CBP.WAIVER_START,
                       CBP.WAIVER_END,
                       FS.STATUS_NAME AS FEE_STATUS_NAME,
                       CE.PAY_DUE_DATE 
                  FROM (SELECT A.POLICY_CODE
                          FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A
                  WHERE  A.POLICY_CODE = #{policy_code}) PO
                  LEFT JOIN APP___CAP__DBUSER.T_PREM_ARAP PAR
                    ON PAR.POLICY_CODE = PO.POLICY_CODE
                  LEFT JOIN APP___PAS__DBUSER.T_FEE_STATUS FS
                    ON FS.STATUS_CODE = PAR.FEE_STATUS
                  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                    ON CBP.BUSI_PROD_CODE = PAR.BUSI_PROD_CODE
                   AND CBP.POLICY_CODE = PO.POLICY_CODE
                  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND CE
                    ON CE.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
                  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT CP
                    ON CP.BUSI_ITEM_ID = CE.BUSI_ITEM_ID
                 WHERE 1 = 1  and PAR.Fee_Status <> '02' 
                   and par.UNIT_NUMBER not in (
                   		SELECT av.ROLLBACK_UNIT_NUMBER 
                   		  FROM dev_cap.v_prem_arap av 
                   		 where av.ROLLBACK_UNIT_NUMBER is not null 
                   		   and av.ROLLBACK_UNIT_NUMBER = par.UNIT_NUMBER 
                   		   and av.ARAP_FLAG =2)
                   AND ((PAR.DERIV_TYPE = '003' AND
                       (PAR.FEE_TYPE = 'G003010000' OR
                       PAR.FEE_TYPE = 'G003100000' OR
                       PAR.FEE_TYPE = 'G003010001' OR
                       PAR.FEE_TYPE = 'G003100001' OR
                       PAR.FEE_TYPE = 'G003020200' OR
                       PAR.FEE_TYPE = 'G003020100' OR
                       PAR.FEE_TYPE = 'G003030200' OR
                       PAR.FEE_TYPE = 'G003030100')) OR
                       (PAR.DERIV_TYPE = '004' AND
                       (PAR.FEE_TYPE = 'G004010000' OR
                       PAR.FEE_TYPE = 'G004010001')) OR
                       (PAR.DERIV_TYPE = '005' AND
                       PAR.FEE_TYPE = 'G005060000' OR
                       PAR.FEE_TYPE = 'G005060001'))
                 
         ]]>
        <if test=" startpaydate != null and startpaydate != ''  ">
			<![CDATA[ AND PAR.DUE_TIME >= to_date(#{startpaydate},'yyyy-MM-dd') ]]>
		</if>
		<if test=" endpaydate != null and endpaydate != ''  ">
			<![CDATA[ AND PAR.DUE_TIME <= to_date(#{endpaydate},'yyyy-MM-dd') ]]>
		</if>
		<![CDATA[ ORDER BY PAR.BUSI_PROD_CODE,PAR.DUE_TIME
             ) B  
		     ) C GROUP BY 
		     PAY_TYPE,
		     FINISH_TIME,
		     DERIV_TYPE,
		     UNIT_NUMBER,
		     BUSINESS_CODE,
		     PRODUCT_NAME_SYS,
		     FEE_STATUS,
		     DUE_TIME,
		     PAY_MODE,
		     PAY_DUE_DATE,
		     SALECHNL,
		     PAID_COUNT,
		     BUSI_PROD_CODE,
		     RISKNAME,
		     FUNDS_RTN_CODE,
		     CODENAME,
		     FEE_STATUS_NAME
		]]>
		
		<if test=" order_context != null and order_context != ''  ">
			<![CDATA[ ${order_context} ]]>
		</if>
	</select>

	<!-- 生存保险金追回 查询 -->
	<select id="JRQD_CS_findNotRGPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT a.fee_status FROM APP___PAS__DBUSER.T_PREM_ARAP A 
			WHERE ROWNUM <=  1000 AND A.FEE_STATUS in('00')
			]]>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code}]]></if>
		<![CDATA[ ORDER BY A.DUE_TIME ASC ]]>
	</select>


	<!-- 查询出险日期前所有续期为交费的金额 -->
	<select id="JRQD_findPremArapPremium" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[
        select NVL(sum(a.fee_amount), 0) AS fee_amount
          from dev_pas.t_prem_arap a
         where a.policy_code = #{policy_code}
			]]>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME <= #{due_time} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<include refid="JRQD_PA_premArapWhereListCondition" />
	</select>

	<!-- 续期保费 -->
	<select id="JRQD_findPremArapNoFinisForXQ" resultType="java.util.Map"
		parameterType="java.util.Map">

		<!-- 专用别乱动===fanzc_wb -->
		<![CDATA[ SELECT  A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
						A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
						A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
						A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
						A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
						A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
						A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
						A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
						A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
						A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
						A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
						A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
						A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
						A.AGENT_CODE, A.PREM_FREQ 
						FROM APP___PAS__DBUSER.T_PREM_ARAP A 
					WHERE  a.DERIV_TYPE = '003' 
					and fee_status ='19' and business_type = '4003'
			]]>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code}]]></if>

	</select>


	<!-- 查询符合现金红利的list -->
	<select id="JRQD_findGRAllPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000
			and a.fee_type in ('P004620000','P004630000','P004620100','P004620200','P004630100','P004630200')  ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		
		<![CDATA[ ORDER BY a.due_time asc ]]>
	</select>

	<select id="JRQD_PA_findAllPremArap_Unit_number_list" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.PRODUCT_CODE,
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE,
			(SELECT B.BANK_NAME FROM DEV_PAS.T_BANK B WHERE B.BANK_CODE=A.BANK_CODE ) BANK_NAME,
			 A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER in (${unit_number}) ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<!-- 查询续期交费次数 -->
	<select id="JRQD_findRenewTimes" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(DISTINCT A.UNIT_NUMBER) FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE 1=1
			AND A.FEE_TYPE IN ('G003100000', 'G003010000', 'G003020100', 'G003020200', 'G003030100', 'G003030100', 'G003040100', 'G003040200') 
			AND A.FEE_STATUS = '19' 
		]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
	</select>

	<!-- 续期交费查询与校检接口 -->

	<select id="JRQD_findPremArapRenewalpayment" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			
			select DISTINCT b.pay_due_date as pay_due_date,
			   a.busi_prod_code, 
		       c.fee_status,c.unit_number,
		       c.bank_account,
		       c.frozen_status,
		      (select i.product_name_sys from APP___PAS__DBUSER.t_business_product i where i.business_prd_id = a.busi_prd_id) as risk_name ,
		      (select sum(j.fee_amount) from APP___PAS__DBUSER.t_prem_arap j where j.unit_number = c.unit_number  ) as fee_amount,
		      c.due_time
		      from APP___PAS__DBUSER.t_contract_busi_prod a,
		       APP___PAS__DBUSER.t_contract_extend    b,
		       APP___CAP__DBUSER.t_prem_arap          c
		 where a.busi_item_id = b.busi_item_id
		   and b.policy_code = c.policy_code
		   and a.master_busi_item_id is null
		   and a.busi_prod_code=c.busi_prod_code
		   and c.deriv_type = '003'
		   and a.policy_code = #{policy_code} 
		   and b.pay_due_date = c.due_time
		   and c.fee_status<>'16'
		   and c.fee_status <>'02'

		  ]]>

	</select>

	<!-- 修改操作 -->
	<update id="JRQD_PA_updatePremArapForEffect" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_PREM_ARAP ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_CHG_ID = #{policy_chg_id,
				jdbcType=NUMERIC} ,
				VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
			</trim>
		</set>
		<![CDATA[ WHERE  LIST_ID = #{list_id}]]>
	</update>

	<!-- 查询最初欠款日期 -->
	<select id="JRQD_queryDebtPremDate" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[select t.due_time from (select tpa.due_time
			  from dev_pas.t_prem_arap tpa
			 where ((tpa.fee_status = '00' and tpa.fee_type in ('G003010000','G003100000','G003020100','G003030100','G003040100','G003020200','G003030200','G003040200')) or (tpa.fee_status = '03' and tpa.fee_type = 'G003010000') or
           (tpa.fee_status = '02' and tpa.fee_type = 'G003010000') or (tpa.fee_status = '16' and tpa.Fee_Type = 'G003020100'))
			   ]]>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND tpa.due_time <= #{busi_apply_date,jdbcType=DATE} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPA.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND TPA.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<!-- <include refid="JRQD_PA_premArapWhereListCondition"/> -->
			<![CDATA[order by tpa.due_time asc]]>
			<![CDATA[) t where rownum = 1]]>
	</select>
	
	<!-- 查询欠款理赔前到最初次欠款金额 -->
	<select id="JRQD_queryDebOwePrem" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[select t.fee_amount from (select sum(tpa.fee_amount) fee_amount
      from dev_pas.t_prem_arap tpa
     where ((tpa.fee_status = '00' and tpa.fee_type in ('G003010000','G003100000','G003020100','G003030100','G003040100','G003020200','G003030200','G003040200')) or (tpa.fee_status = '03' and tpa.fee_type = 'G003010000') or
           (tpa.fee_status = '02' and tpa.fee_type = 'G003010000') or (tpa.fee_status = '16' and tpa.Fee_Type = 'G003020100'))
           ]]>
           <if test=" due_time  != null  and  due_time  != ''  "><![CDATA[and  tpa.due_time >= #{due_time,jdbcType=DATE} ]]></if>
           <if test=" busi_apply_date  != null  and  busi_apply_date  != ''  "><![CDATA[ and tpa.due_time <= #{busi_apply_date,jdbcType=DATE} ]]></if>
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPA.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND TPA.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
			<![CDATA[)t]]>
		</select>

	<!-- 查询理赔前已交的金额-->
	<select id="JRQD_queryDebPayPrem" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[select t.fee_amount from (select sum(tpa.fee_amount) fee_amount
      from dev_pas.t_prem_arap tpa
     where ((tpa.fee_status = '01') or (tpa.fee_status = '19')) and tpa.fee_type in ('G003010000','G004510300','G004740300','G004510100','G004510200','G004740100','G004740200')
           ]]>
           <if test=" due_time  != null  and  due_time  != ''  "><![CDATA[and  tpa.due_time >= #{due_time,jdbcType=DATE} ]]></if>
           <if test=" busi_apply_date  != null  and  busi_apply_date  != ''  "><![CDATA[ and tpa.due_time <= #{busi_apply_date,jdbcType=DATE} ]]></if>
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPA.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND TPA.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
			<![CDATA[)t]]>
		</select>

	<!-- 查询续期收费未成功的数据===更改收付费信息==ForPC  liudx_wb 勿动 -->
	<select id="JRQD_PA_findAllPremArapForPC" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID,A.product_code FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000
			AND A.DERIV_TYPE ='003'
			AND A.FEE_STATUS IN ('00','03','04')
		    AND BUSINESS_TYPE in('4003','1005') 
		    AND A.POLICY_CODE = #{policy_code}  ]]>
	</select>
	<!-- 查询该保单下是否有新增的长期附加险已推送收付费但缴费未成功的数据 -->
	<select id="JRQD_PA_findAllPremArapForPCofNS" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		 SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
      A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
      A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
      A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
      A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
      A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
      A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
      A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
      A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
      A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
      A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
      A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
      A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AUDIT_DATE,
      A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID 
      FROM APP___PAS__DBUSER.T_PREM_ARAP A ,
      APP___PAS__DBUSER.T_CS_POLICY_CHANGE B,
      APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C,
      APP___PAS__DBUSER.T_CS_PRECONT_PRODUCT D,
      APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD E
      
      WHERE ROWNUM <=  1000
      AND A.POLICY_CODE = B.POLICY_CODE
      AND B.ACCEPT_ID = C.ACCEPT_ID
      AND B.ACCEPT_ID = D.ACCEPT_ID
      AND C.CHANGE_ID = D.CHANGE_ID
      AND B.POLICY_CHG_ID = D.POLICY_CHG_ID
      AND B.POLICY_CHG_ID = E.POLICY_CHG_ID
      AND A.BUSI_PROD_CODE = E.BUSI_PROD_CODE
      AND C.ACCEPT_STATUS = '18'
      AND C.SERVICE_CODE = 'NS'
      AND E.OLD_NEW = '1'
      AND E.OPERATION_TYPE = '1'
      AND D.PRECONT_STATUS = '3'
      AND A.DERIV_TYPE ='004'
      AND A.FEE_STATUS IN ('00','03','04')
      AND A.POLICY_CODE = #{policy_code}
       ]]>
	</select>
	<!-- 查询保费缴费信息 -->
	<select id="JRQD_PA_queryPremPaymentInf" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT TCE.BUSI_ITEM_ID,TCE.POLICY_PERIOD,TCE.POLICY_CODE,
				(SELECT T.payrefno FROM APP___CAP__DBUSER.T_CASH_DETAIL T WHERE T.UNIT_NUMBER=TPA.UNIT_NUMBER 
				AND T.BUSI_PROD_CODE=(SELECT TB.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TB WHERE TB.BUSI_ITEM_ID=TCE.BUSI_ITEM_ID)
				AND ROWNUM=1)ARAP_BELNR,
				(SELECT T.PRODUCT_CODE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT T WHERE T.ITEM_ID=TCE.ITEM_ID AND ROWNUM=1)PRODUCT_CODE,
				TPA.FEE_AMOUNT,TPA.DUE_TIME,TPA.FEE_TYPE,TPA.FINISH_TIME,TCE.PAY_DUE_DATE,
				(SELECT T.PREM_FREQ FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT T WHERE T.ITEM_ID=TCE.ITEM_ID AND ROWNUM=1)PREM_FREQ,
				(SELECT ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE WHERE ACCEPT_CODE=TPA.BUSINESS_CODE AND ROWNUM=1)ACCEPT_CODE,
				TPA.INSERT_TIME,TPA.UPDATE_TIME,TPA.INSERT_TIMESTAMP
				FROM  APP___PAS__DBUSER.T_PREM_ARAP TPA,APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
				WHERE TPA.FEE_STATUS IN('01','19') and TPA.POLICY_CODE=TCE.POLICY_CODE AND TPA.BUSI_PROD_CODE=(SELECT TB.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TB WHERE TB.BUSI_ITEM_ID=TCE.BUSI_ITEM_ID)
				AND TCE.POLICY_CODE=#{policy_code} AND TCE.BUSI_ITEM_ID=(select a.busi_item_id
                             from dev_pas.t_contract_busi_prod a
                            where NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID)=#{busi_item_id}
                              and rownum = 1)
		 ]]>
	</select>
	
	<!-- 查询已续期抽档未缴费的信息 -->
	<select id="JRQD_findNotPayRenewExtra" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM  APP___PAS__DBUSER.T_PREM_ARAP TPA WHERE 1=1
			AND TPA.FEE_STATUS in ('00','03','20')
			AND (TPA.BUSINESS_TYPE = '4003' OR TPA.BUSINESS_TYPE = '1005') 
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPA.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ORDER BY TPA.DUE_TIME DESC]]>
    </select>
    
    <select id="JRQD_findAllrollPremArap" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
    		SELECT TPA.LIST_ID
  FROM DEV_PAS.T_PREM_ARAP TPA, DEV_PAS.T_CONTRACT_MASTER TCM
 WHERE TPA.POLICY_CODE = TCM.POLICY_CODE
    	]]>
    	<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND tcm.POLICY_id = #{policy_id} ]]></if>
    	<if test=" due_time != null and due_time != ''  "><![CDATA[ AND tpa.DUE_TIME >= #{due_time} ]]></if>
    	<if test=" frozen_status != null and frozen_status != ''  "><![CDATA[ AND tpa.frozen_status = #{frozen_status}]]></if>
    </select>
    
    <select id="JRQD_PA_findPramArapByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
    		SELECT TPA.LIST_ID,
       TPA.UNIT_NUMBER,
       TPA.BUSI_ITEM_ID, 
       TPA.CHARGE_PERIOD,
       TPA.BUSI_ITEM_ID,
       TPA.PRE_UNIT_NUMBER,
       TPA.PAYREFNO,
       TPA.BUSINESS_CODE,
       TPA.APPLY_CODE,
       TPA.POLICY_CODE,
       TPA.BATCH_NO,
       TPA.BUSI_PROD_CODE,
       TPA.BUSI_PROD_NAME,
       TPA.PRODUCT_CODE,
       TPA.PAY_LIAB_CODE,
       TPA.SOURCE_TABLE,
       TPA.SOURCE_TABLE_PK,
       TPA.DERIV_TYPE,
       TPA.BRANCH_CODE,
       TPA.ORGAN_CODE,
       TPA.POLICY_ORGAN_CODE,
       TPA.AGENT_CODE,
       TPA.POLICY_TYPE,
       TPA.CHANNEL_TYPE,
       TPA.PRODUCT_CHANNEL,
       TPA.BUSI_APPLY_DATE,
       TPA.AUDIT_DATE,
       TPA.STATISTICAL_DATE,
       TPA.VALIDATE_DATE,
       TPA.POLICY_YEAR,
       TPA.PREM_FREQ,
       TPA.CHARGE_YEAR,
       TPA.PAID_COUNT,
       TPA.HOLDER_ID,
       TPA.HOLDER_NAME,
       TPA.INSURED_ID,
       TPA.INSURED_NAME,
       TPA.IS_RISK_MAIN,
       TPA.IS_ITEM_MAIN,
       TPA.GROUP_CODE,
       TPA.GROUP_NAME,
       TPA.FEE_TYPE,
       TPA.WITHDRAW_TYPE,
       TPA.REFEFLAG,
       TPA.DUE_TIME,
       TPA.FINISH_TIME,
       TPA.PAY_END_DATE,
       TPA.ARAP_FLAG,
       TPA.CUSTOMER_ID,
       TPA.PAYEE_NAME,
       TPA.CERTI_TYPE,
       TPA.CERTI_CODE,
       TPA.PAYEE_PHONE,
       TPA.PAYEE_EMAIL,
       TPA.BANK_CODE,
       TPA.BANK_ACCOUNT,
       TPA.BANK_USER_NAME,
       TPA.CUSTOMER_ACCOUNT_FLAG,
       TPA.IS_BANK_ACCOUNT,
       TPA.IS_BANK_ACCOUNT_BY,
       TPA.IS_BANK_ACCOUNT_DATE,
       TPA.MONEY_CODE,
       TPA.FEE_AMOUNT,
       TPA.PAY_MODE,
       TPA.ROLLBACK_UNIT_NUMBER,
       TPA.FEE_STATUS,
       TPA.FEE_STATUS_BY,
       TPA.FEE_STATUS_DATE,
       TPA.FROZEN_STATUS,
       TPA.FROZEN_STATUS_BY,
       TPA.FROZEN_STATUS_DATE,
       TPA.IS_BANK_TEXT,
       TPA.FAIL_TIMES,
       TPA.GROUP_ID,
       TPA.SEQ_NO,
       TPA.BANK_TEXT_STATUS,
       TPA.TASK_MARK,
       TPA.IS_BANK_TEXT_BY,
       TPA.IS_BANK_TEXT_DATE,
       TPA.BOOKKEEPING_FLAG,
       TPA.POSTED,
       TPA.BOOKKEEPING_ID,
       TPA.BELNR,
       TPA.CR_BELNR,
       TPA.BOOKKEEPING_BY,
       TPA.BOOKKEEPING_TIME,
       TPA.RED_BOOKKEEPING_FLAG,
       TPA.RED_BOOKKEEPING_ID,
       TPA.RED_BELNR,
       TPA.RED_BOOKKEEPING_BY,
       TPA.RED_BOOKKEEPING_TIME,
       TPA.SERVICE_CODE,
       TPA.CIP_BANK_CODE,
       TPA.CIP_DISTRICT_BANK_CODE,
       TPA.IS_SPLIT,
       TPA.IS_SEND,
       TPA.TAX_RATE,
       TPA.AGENT_NAME,
       TPA.AREA,
       TPA.PART,
       TPA.PRODUCT_ABBR_NAME,
       TPA.SEND_DATE,
       TPA.CIP_BRANCH_BANK_CODE,
       TPA.BUSINESS_TYPE,
       TPA.FUNDS_RTN_CODE,
       TPA.CUS_ACC_FEE_AMOUNT,
       TPA.CUS_ACC_DETAILS_ID,
       TPA.CUS_ACC_UPDATE_BY,
       TPA.CUS_ACC_UPDATE_TIME,
       TPA.BANK_DEAL_DATE,
       TPA.CLAIM_NATURE,
       TPA.CLAIM_TYPE,
       TPA.POLICY_BALANCE,
       TPA.POLICY_CHG_ID,
       TPA.OPERATOR_BY,
       TPA.COVERAGE_PERIOD,
       TPA.COVERAGE_YEAR,
       TPA.BACK_TEXT_TIME,
       TPA.POS_BELNR,
       TPA.IS_BACKUP_BACK,
       TPA.IS_RECOVER_DOCUMENT,
       TPA.INSERT_BY,
       TPA.UPDATE_BY,
       TPA.INSERT_TIME,
       TPA.UPDATE_TIME,
       TPA.INSERT_TIMESTAMP,
       TPA.UPDATE_TIMESTAMP
  		FROM DEV_PAS.T_PREM_ARAP TPA WHERE 1 = 1 AND ROWNUM = 1
    	]]>
    	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPA.POLICY_CODE = #{policy_code} ]]></if>
    </select>
    <select id="JRQD_PA_findAllPremArapForGC" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A,APP___PAS__DBUSER.T_PAY_DUE B WHERE ROWNUM <=  1000
			AND A.UNIT_NUMBER = B.UNIT_NUMBER AND A.POLICY_CODE = B.POLICY_CODE
			AND A.FEE_STATUS IN ('00','03','04')	
			]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" plan_id != null and plan_id != ''  "><![CDATA[ AND B.PLAN_ID = #{plan_id} ]]></if>
			  
	</select>
    <!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllPremArapListByUnitNumber" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <=  1000  AND FEE_TYPE IN ('G003010000','G003020100','G003030100','G003040100','G003020200'
			,'G003030200','G003030300','G003040200','P003050000','G003060000','G003070000','G003080000','G003100000'
			)]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<include refid="JRQD_PA_premArapWhereListCondition" />
	</select>
	
    <!--年金满期金生效时专用 -->
	<select id="JRQD_PA_findAGPremArap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.* FROM APP___PAS__DBUSER.T_CS_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_premArapAGCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<!-- 查询同一个unitNumber下的应收应付表最大的宽限期止期 -->
	<select id="JRQD_PA_findMaxPayEndDateByUnitnumber" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
    		SELECT MAX(TPA.PAY_END_DATE) PAY_END_DATE FROM APP___PAS__DBUSER.T_PREM_ARAP TPA 
    		WHERE TPA.UNIT_NUMBER = #{unit_number}
    	]]>
    </select>
    
    <!-- 查最后一次有效的续期记录的pay_end_date -->
    <select id="JRQD_PA_findLastPayEndDateByCondition" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
    		SELECT T.POLICY_CODE,T.BUSI_PROD_CODE,MAX(T.PAY_END_DATE) AS PAY_END_DATE,MAX(T.DUE_TIME) AS DUE_TIME 
    		FROM DEV_PAS.T_PREM_ARAP  T WHERE 1=1 AND T.DERIV_TYPE = '003' AND T.FEE_STATUS = '19' 
				AND T.POLICY_CODE = #{policy_code} AND T.BUSI_PROD_CODE = #{busi_prod_code} AND  T.ROLLBACK_UNIT_NUMBER IS NULL
				GROUP BY T.POLICY_CODE,T.BUSI_PROD_CODE
    	]]>
    </select>
    
    <select id="JRQD_PA_queryPayModeByUnit" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[ SELECT C.PAY_MODE FROM DEV_CAP.T_CASH_DETAIL C WHERE C.UNIT_NUMBER IN (
			SELECT B.UNIT_NUMBER FROM DEV_CAP.T_PREM_ARAP B WHERE B.DERIV_TYPE = '003'
			AND B.BUSINESS_TYPE IN('4003','1005') AND B.UNIT_NUMBER = #{unit_number})
			AND ROWNUM = 1
    	]]>
    </select>
    
    
    <select id="JRQD_PA_findOrphanPolicyNoticePremArap" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
    		SELECT TPA.LIST_ID,
       TPA.UNIT_NUMBER,
       TPA.BUSI_ITEM_ID, 
       TPA.PRE_UNIT_NUMBER,
       TPA.PAYREFNO,
       TPA.BUSINESS_CODE,
       TPA.APPLY_CODE,
       TPA.POLICY_CODE,
       TPA.BATCH_NO,
       TPA.BUSI_PROD_CODE,
       TPA.BUSI_PROD_NAME,
       TPA.PRODUCT_CODE,
       TPA.PAY_LIAB_CODE,
       TPA.SOURCE_TABLE,
       TPA.SOURCE_TABLE_PK,
       TPA.DERIV_TYPE,
       TPA.BRANCH_CODE,
       TPA.ORGAN_CODE,
       TPA.POLICY_ORGAN_CODE,
       TPA.AGENT_CODE,
       TPA.POLICY_TYPE,
       TPA.CHANNEL_TYPE,
       TPA.PRODUCT_CHANNEL,
       TPA.BUSI_APPLY_DATE,
       TPA.AUDIT_DATE,
       TPA.STATISTICAL_DATE,
       TPA.VALIDATE_DATE,
       TPA.POLICY_YEAR,
       TPA.PREM_FREQ,
       TPA.CHARGE_YEAR,
       TPA.PAID_COUNT,
       TPA.HOLDER_ID,
       TPA.HOLDER_NAME,
       TPA.INSURED_ID,
       TPA.INSURED_NAME,
       TPA.IS_RISK_MAIN,
       TPA.IS_ITEM_MAIN,
       TPA.GROUP_CODE,
       TPA.GROUP_NAME,
       TPA.FEE_TYPE,
       TPA.WITHDRAW_TYPE,
       TPA.REFEFLAG,
       TPA.DUE_TIME,
       TPA.FINISH_TIME,
       TPA.PAY_END_DATE,
       TPA.ARAP_FLAG,
       TPA.CUSTOMER_ID,
       TPA.PAYEE_NAME,
       TPA.CERTI_TYPE,
       TPA.CERTI_CODE,
       TPA.PAYEE_PHONE,
       TPA.PAYEE_EMAIL,
       TPA.BANK_CODE,
       TPA.BANK_ACCOUNT,
       TPA.BANK_USER_NAME,
       TPA.CUSTOMER_ACCOUNT_FLAG,
       TPA.IS_BANK_ACCOUNT,
       TPA.IS_BANK_ACCOUNT_BY,
       TPA.IS_BANK_ACCOUNT_DATE,
       TPA.MONEY_CODE,
       TPA.FEE_AMOUNT,
       TPA.PAY_MODE,
       TPA.ROLLBACK_UNIT_NUMBER,
       TPA.FEE_STATUS,
       TPA.FEE_STATUS_BY,
       TPA.FEE_STATUS_DATE,
       TPA.FROZEN_STATUS,
       TPA.FROZEN_STATUS_BY,
       TPA.FROZEN_STATUS_DATE,
       TPA.IS_BANK_TEXT,
       TPA.FAIL_TIMES,
       TPA.GROUP_ID,
       TPA.SEQ_NO,
       TPA.BANK_TEXT_STATUS,
       TPA.TASK_MARK,
       TPA.IS_BANK_TEXT_BY,
       TPA.IS_BANK_TEXT_DATE,
       TPA.BOOKKEEPING_FLAG,
       TPA.POSTED,
       TPA.BOOKKEEPING_ID,
       TPA.BELNR,
       TPA.CR_BELNR,
       TPA.BOOKKEEPING_BY,
       TPA.BOOKKEEPING_TIME,
       TPA.RED_BOOKKEEPING_FLAG,
       TPA.RED_BOOKKEEPING_ID,
       TPA.RED_BELNR,
       TPA.RED_BOOKKEEPING_BY,
       TPA.RED_BOOKKEEPING_TIME,
       TPA.SERVICE_CODE,
       TPA.CIP_BANK_CODE,
       TPA.CIP_DISTRICT_BANK_CODE,
       TPA.IS_SPLIT,
       TPA.IS_SEND,
       TPA.TAX_RATE,
       TPA.AGENT_NAME,
       TPA.AREA,
       TPA.PRODUCT_ABBR_NAME,
       TPA.SEND_DATE,
       TPA.CIP_BRANCH_BANK_CODE,
       TPA.BUSINESS_TYPE,
       TPA.FUNDS_RTN_CODE,
       TPA.CUS_ACC_FEE_AMOUNT,
       TPA.CUS_ACC_DETAILS_ID,
       TPA.CUS_ACC_UPDATE_BY,
       TPA.CUS_ACC_UPDATE_TIME,
       TPA.BANK_DEAL_DATE,
       TPA.CLAIM_NATURE,
       TPA.CLAIM_TYPE,
       TPA.POLICY_BALANCE,
       TPA.POLICY_CHG_ID,
       TPA.OPERATOR_BY,
       TPA.COVERAGE_PERIOD,
       TPA.COVERAGE_YEAR,
       TPA.BACK_TEXT_TIME,
       TPA.POS_BELNR,
       TPA.IS_BACKUP_BACK,
       TPA.IS_RECOVER_DOCUMENT
  FROM APP___PAS__DBUSER.t_Prem_Arap TPA
 WHERE 1 = 1

   AND TPA.FEE_STATUS IN ( '00','03')
    	]]>
    	<if test=" policy_code != null and policy_code != ''  "><![CDATA[   AND TPA.POLICY_CODE = #{policy_code} ]]></if>
    </select>
    
    
    <!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllPremArapCap" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.PRODUCT_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.Audit_Date,
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___CAP__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
			<include refid="JRQD_PA_premArapWhereCondition" />
	        <![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
    <select id="JRQD_find_cs_product_info" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
    		SELECT ITEM_ID,VALIDATE_DATE  FROM (SELECT ITEM_ID,VALIDATE_DATE  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT 
    		WHERE    PRODUCT_CODE = #{product_code} AND POLICY_CODE = #{policy_code} ORDER BY VALIDATE_DATE DESC) WHERE ROWNUM =1
    	]]>
    </select>
    
    <!-- 查询续期保费 -->
	<select id="JRQD_findAllPremArapPU" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT A.PRODUCT_CODE,A.BUSI_ITEM_ID, 
       A.TASK_MARK,
       A.POLICY_ORGAN_CODE,
       A.HOLDER_NAME,
       A.IS_ITEM_MAIN,
       A.BOOKKEEPING_ID,
       A.BATCH_NO,
       A.BUSI_PROD_NAME,
       A.PAYEE_PHONE,
       A.UNIT_NUMBER,
       A.PAID_COUNT,
       A.FUNDS_RTN_CODE,
       A.CUSTOMER_ACCOUNT_FLAG,
       A.FEE_TYPE,
       A.SEQ_NO,
       A.BUSI_PROD_CODE,
       A.APPLY_CODE,
       A.FEE_STATUS_DATE,
       A.ORGAN_CODE,
       A.RED_BOOKKEEPING_TIME,
       A.CHANNEL_TYPE,
       A.IS_BANK_TEXT_DATE,
       A.CHARGE_YEAR,
       A.IS_RISK_MAIN,
       A.IS_BANK_ACCOUNT,
       A.FROZEN_STATUS,
       A.BOOKKEEPING_FLAG,
       A.POSTED,
       A.GROUP_ID,
       A.RED_BOOKKEEPING_BY,
       A.FROZEN_STATUS_DATE,
       A.INSURED_NAME,
       A.CUS_ACC_DETAILS_ID,
       A.INSURED_ID,
       A.POLICY_TYPE,
       A.PRODUCT_CHANNEL,
       A.IS_BANK_ACCOUNT_DATE,
       A.POLICY_CODE,
       A.PAY_MODE,
       A.OPERATOR_BY,
       A.BRANCH_CODE,
       A.BUSINESS_TYPE,
       A.VALIDATE_DATE,
       A.RED_BELNR,
       A.BANK_TEXT_STATUS,
       A.GROUP_CODE,
       A.CERTI_TYPE,
       A.CUS_ACC_UPDATE_BY,
       A.IS_BANK_TEXT_BY,
       A.IS_BANK_ACCOUNT_BY,
       A.BUSINESS_CODE,
       A.MONEY_CODE,
       A.PAYEE_NAME,
       A.BANK_ACCOUNT,
       A.RED_BOOKKEEPING_FLAG,
       A.CUS_ACC_FEE_AMOUNT,
       A.CUSTOMER_ID,
       A.FINISH_TIME,
       A.DUE_TIME,
       A.IS_BANK_TEXT,
       A.CERTI_CODE,
       A.BUSI_APPLY_DATE,
       A.GROUP_NAME,
       A.BELNR,
       A.LIST_ID,
       A.FEE_AMOUNT,
       A.SERVICE_CODE,
       A.HOLDER_ID,
       A.WITHDRAW_TYPE,
       A.ROLLBACK_UNIT_NUMBER,
       A.FAIL_TIMES,
       A.POLICY_YEAR,
       A.FROZEN_STATUS_BY,
       A.BANK_USER_NAME,
       A.FEE_STATUS,
       A.FEE_STATUS_BY,
       A.PAY_END_DATE,
       A.DERIV_TYPE,
       A.RED_BOOKKEEPING_ID,
       A.BOOKKEEPING_BY,
       A.CUS_ACC_UPDATE_TIME,
       A.ARAP_FLAG,
       A.BANK_CODE,
       A.BOOKKEEPING_TIME,
       A.REFEFLAG,
       A.AUDIT_DATE,
       A.AGENT_CODE,
       A.PREM_FREQ,
       A.POLICY_CHG_ID
  FROM APP___PAS__DBUSER.T_PREM_ARAP A
 WHERE A.FEE_TYPE IN ('G003100000',
                      'G003010000',
                      'G003020100',
                      'G003030100',
                      'G003040100',
                      'G003020200',
                      'G003030200',
                      'G003040200')
   AND A.FEE_STATUS IN ('01', '19')
   AND A.POLICY_CODE = #{policy_code}
   AND A.DUE_TIME < #{due_time}
   ORDER BY A.PAY_END_DATE DESC
		]]>
	</select>
	
	<!-- 上海医保产品续保校验 -->
    <select id="JRQD_PA_checkSHMedical"  resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[ SELECT CM.POLICY_ID,CM.APPLY_CODE,PAM.POLICY_FORMER_NO,PAM.POLICY_SEQUENCE_NO FROM
    		APP___PAS__DBUSER.T_CONTRACT_MASTER CM,APP___PAS__DBUSER.T_PREM_ARAP_MEDICAL PAM
			WHERE CM.APPLY_CODE=PAM.APPLY_CODE AND CM.POLICY_ID = #{policy_id } ORDER BY PAM.INSERT_TIME DESC
    	]]>
    </select>

   <!-- 查询个人交费信息 -->
   <select id="JRQD_findPersonPremArap" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[
		select tp.policy_code,tp.due_time,tp.finish_time, tp.paid_count,tcbp.expiry_date,tcbp.busi_prod_code,tt.charge_period,tp.insert_time,tp.update_time
		  from APP___PAS__DBUSER.t_prem_arap        tp,
		       APP___PAS__DBUSER.t_contract_extend  td,
					 APP___PAS__DBUSER.t_contract_busi_prod tcbp,
		       APP___PAS__DBUSER.t_contract_product tt,
					 dev_pas.t_cs_accept_change tcac,
					 dev_pas.t_cs_policy_change tcpc
		 where tp.policy_code = td.policy_code
		   and td.item_id = tt.item_id
			 and tcpc.policy_code = tp.policy_code 
			 and tcpc.accept_id = tcac.accept_id
			 and tcbp.policy_code = tt.policy_code
			 and tcbp.busi_item_id = tt.busi_item_id
			 and tcac.service_code = 'RE'
			 and tcac.accept_status = '18'
			 and tp.arap_flag = '1'
			 and tcac.accept_code = #{eventNo}
			 and tp.policy_code = #{policy_code}
		 ]]>
   
   </select>
   
   <select id="JRQD_find_cs_product_infoFroRB" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
    		SELECT A.ITEM_ID,A.DUE_TIME FROM DEV_PAS.T_PREM A WHERE A.UNIT_NUMBER=#{unit_number} AND A.PRODUCT_CODE=#{product_code}
    	]]>
    </select>
    
    <select id="JRQD_PA_findAllPremArapBusiProdCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG,A.Product_Code, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.DERIV_TYPE='003' AND A.FEE_STATUS IN ('00','03') AND A.FEE_TYPE IN ('G003010000','G003100000')
			 ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" due_time != null and due_time != ''  "><![CDATA[  AND A.DUE_TIME <= #{due_time} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" busi_prod_code_list  != null  and busi_prod_code_list.size() !=0 ">
			<![CDATA[ AND A.busi_prod_code IN ]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code" index="index"
				open="(" close=")" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
		</if>
	</select>
    
    <!-- 查询保单下当期未交费成功的续期数据 -->
	<select id="JRQD_queryPREinfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT *  FROM DEV_PAS.T_PREM_ARAP A 
			WHERE A.FEE_TYPE IN ('G003100000','G003010000','G003020200','G003030100','G003030200','G003020100') AND A.ARAP_FLAG = '1'
			AND A.FEE_STATUS IN ('00','03','20') ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<![CDATA[ ORDER BY A.DUE_TIME DESC ]]>
	</select>

	<select id="JRQD_PA_findAllPremArapBusiProdCodeByUnitNumber" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, A.BUSI_ITEM_ID, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID,A.PRODUCT_CODE FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.DERIV_TYPE='003' AND A.FEE_TYPE IN ('G003010000','G003100000') 
			 ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" due_time != null and due_time != ''  "><![CDATA[  AND A.DUE_TIME <= #{due_time} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" busi_prod_code_list  != null  and busi_prod_code_list.size() !=0 ">
			<![CDATA[ AND A.busi_prod_code IN ]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code" index="index"
				open="(" close=")" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
		</if>
	</select>

	<!-- 查询保全项费用 -->
	<select id="JRQD_PA_queryFeeInfoByBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[	select  ( select nvl(sum(fee_amount),0) fee_amount
			from dev_pas.t_prem_arap a where a.arap_flag='1' and a.business_code=#{business_code}) -(select nvl(sum(fee_amount),0) sumfeeamount 
			from dev_pas.t_prem_arap a where a.arap_flag='2' and a.business_code=#{business_code}) as sumfeeamount from dual 
    	]]>
    </select>
    
    <!--根据保单和dueTime判断保费是否回退 -->
	<select id="JRQD_PA_checkXQPremBack" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT P.*
		    FROM  APP___PAS__DBUSER.T_PREM_ARAP P WHERE 1 = 1  
			AND NOT  EXISTS ( /* 去除已进行过续期回退的数据 */
				SELECT 'X'
				FROM APP___PAS__DBUSER.T_PREM_ARAP B
				WHERE 1 = 1
				AND B.POLICY_CODE = p.POLICY_CODE
				AND B.DERIV_TYPE = '004'
				AND B.FEE_STATUS != '02'
				AND B.ROLLBACK_UNIT_NUMBER IS NOT NULL
				AND B.ROLLBACK_UNIT_NUMBER = p.UNIT_NUMBER
				)
			AND p.WITHDRAW_TYPE IN ('0030101000', '0030102000', '0030103000', '0031200000') 
      AND (P.FEE_STATUS = '01' OR P.FEE_STATUS = '19')
      AND P.POLICY_CODE= #{policy_code} AND P.DUE_TIME = #{due_time}  AND p.DERIV_TYPE = '003' ORDER BY P.INSERT_TIME DESC ]]>
	</select>
	
	<!--外围接口使用查询移动保全2.0-订单号查询受理号接口 -->
	<select id="JRQD_queryFeeArapFlagBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[	select case
            when (select (select nvl(SUM(FEE_AMOUNT), 0) SumFeeAmount
                            from dev_cap.t_prem_arap A
                           where A.arap_flag = '1'
                             and A.BUSINESS_CODE =#{business_code}) -
                         (select nvl(SUM(FEE_AMOUNT), 0) SumFeeAmount
                            from dev_cap.t_prem_arap A
                           where A.arap_flag = '2'
                             and A.BUSINESS_CODE =#{business_code}) as SumFeeAmount
                    from dual) > 0 then
             '1'
            when (select (select nvl(SUM(FEE_AMOUNT), 0) SumFeeAmount
                            from dev_cap.t_prem_arap A
                           where A.arap_flag = '1'
                             and A.BUSINESS_CODE =#{business_code}) -
                         (select nvl(SUM(FEE_AMOUNT), 0) SumFeeAmount
                            from dev_cap.t_prem_arap A
                           where A.arap_flag = '2'
                             and A.BUSINESS_CODE =#{business_code}) as SumFeeAmount
                    from dual) < 0 then
             '2'
          
            ELSE
             '0'
          END AS arap_flag
   
     from dual
  ]]>
	</select>
	<!--外围接口使用  -->
	<select id="JRQD_queryCauseOffail" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	select A.BANK_RET_CODE, A.BANK_RET_NAME from dev_pas.t_bank_ret_conf A WHERE A.BANK_RET_CODE=#{bank_ret_code}
	]]>
	</select>
	
	<!--保单是否存在续期保费处于银行制返盘划款期间  -->
	<select id="JRQD_queryRenewalHangUp" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      SELECT *
          FROM (SELECT  T.*
              FROM APP___PAS__DBUSER.T_PREM_ARAP T
             WHERE 1 = 1
               AND T.FEE_STATUS = '04'
               AND T.FEE_TYPE = 'G003010000'
               AND T.PAY_MODE='32'
               AND T.POLICY_CODE = #{policy_code}
             ORDER BY T.DUE_TIME) T 
      WHERE ROWNUM <= 1
	]]>
</select>

	<!-- 54754 关于新核心系统生成唯一收付费号码的需求  对冲时判断当前受理号是收费还是付费-->
	<select id="JRQD_findPremArapGYSumByBusinessCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      SELECT T.UNIT_NUMBER,
             NVL(SUM(CASE
                   WHEN T.ARAP_FLAG = 1 THEN
                    T.FEE_AMOUNT
                   ELSE
                    -T.FEE_AMOUNT
                 END),0) FEE_AMOUNT
        FROM APP___PAS__DBUSER.T_PREM_ARAP T
       WHERE T.BUSINESS_CODE =#{business_code}
       GROUP BY T.UNIT_NUMBER
	]]>
	</select>
	<!-- 54754 查询unitNumber下是收费还是付费  -->
		<select id="JRQD_findPremArapGYSumByUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	             SELECT T.UNIT_NUMBER,
             NVL(SUM(CASE
                   WHEN T.ARAP_FLAG = 1 THEN
                    T.FEE_AMOUNT
                   ELSE
                    -T.FEE_AMOUNT
                 END), 0) FEE_AMOUNT
        FROM APP___PAS__DBUSER.T_PREM_ARAP T
       WHERE T.UNIT_NUMBER =#{unit_number}
       GROUP BY T.UNIT_NUMBER
	]]>
	</select>
	
	<!-- 续期查询(接入渠道) -->
	<select id="JRQD_PA_queryRenewalPolicy" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[                          
         SELECT (SELECT U.ORGAN_NAME
                  FROM APP___PAS__DBUSER.T_UDMP_ORG U
                 WHERE U.ORGAN_CODE = C.ORGAN_CODE) ORGAN_CODE,
               C.holder_name,
               C.PAY_MODE,
               C.PREM_FREQ,
               C.CHARGE_YEAR,
               C.DUE_TIME,
               C.PAY_END_DATE,
               C.APPLY_CODE,
               C.CERTI_TYPE,
               C.CERTI_CODE,
               (SELECT Z.AGENT_NAME
                  FROM APP___PAS__DBUSER.T_AGENT Z
                 WHERE Z.AGENT_CODE = C.AGENT_CODE) AGENT_NAME,
               C.AGENT_CODE,
               C.BANK_ACCOUNT,
               C.BUSI_PROD_CODE,
               C.IS_RISK_MAIN,
               C.BUSI_PROD_NAME,
               C.COVERAGE_PERIOD,
               C.COVERAGE_YEAR,
               C.INSURED_NAME,
               C.POLICY_CODE,
               C.PAID_COUNT,
               C.FEE_AMOUNT,
               C.DOUBLE_MAINRISK_FLAG,
               C.STD_PREM_AF,
               C.TOTAL_PREM_AF,
               C. POLICY_PERIOD,
               C.MAIN_STD_PREM_AF
          FROM (select max(A.PAID_COUNT) AS paid_count,
                       MAX(A.ORGAN_CODE) AS organ_code,
                       MAX(A.HOLDER_NAME) AS holder_name,
                       MAX(A.PAY_MODE) AS pay_mode,
                       MAX(A.PREM_FREQ) AS prem_freq,
                       MAX(A.CHARGE_YEAR) AS charge_year,
                       MAX(A.DUE_TIME) AS due_time,
                       MAX(A.PAY_END_DATE) AS pay_end_date,
                       MAX(A.APPLY_CODE) AS apply_code,
                       MAX(A.CERTI_TYPE) AS certi_type,
                       MAX(A.CERTI_CODE) AS certi_code,
                       MAX(A.AGENT_NAME) AS agent_name,
                       MAX(A.AGENT_CODE) AS agent_code,
                       MAX(A.BANK_ACCOUNT) AS bank_account,
                       MAX(A.BUSI_PROD_CODE) AS busi_prod_code,
                       MAX(A.IS_RISK_MAIN) AS is_risk_main,
                       MAX(A.BUSI_PROD_NAME) AS busi_prod_name,
                       MAX(A.COVERAGE_PERIOD) AS coverage_period,
                       MAX(A.COVERAGE_YEAR) AS coverage_year,
                       MAX(A.INSURED_NAME) AS insured_name,
                       SUM(A.FEE_AMOUNT) AS fee_amount,
                       MAX(A.POLICY_CODE) AS POLICY_CODE,
               (SELECT TCM.DOUBLE_MAINRISK_FLAG
                  from APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
                 WHERE TCM.POLICY_CODE = A.POLICY_CODE) DOUBLE_MAINRISK_FLAG,
               (SELECT SUM(TCP.STD_PREM_AF)
                  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
                       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
                 WHERE TCBP.POLICY_CODE = A.POLICY_CODE
                   AND TCBP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                   AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID) STD_PREM_AF,
               (SELECT SUM(TCP.TOTAL_PREM_AF)
                  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
                       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
                 WHERE TCBP.POLICY_CODE = A.POLICY_CODE
                   AND TCBP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                   AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID) TOTAL_PREM_AF,
               (SELECT MAX(TCE.POLICY_PERIOD)
                  FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND    TCE,
                       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
                 WHERE TCBP.POLICY_CODE = A.POLICY_CODE
                   AND TCBP.BUSI_PROD_CODE = A.BUSI_PROD_CODE
                   AND TCBP.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID) POLICY_PERIOD,
               (SELECT SUM(TCP.STD_PREM_AF)
                  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   TCP,
                       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
                 WHERE TCBP.POLICY_CODE = A.POLICY_CODE
                   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
                   AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID) MAIN_STD_PREM_AF
                  FROM APP___PAS__DBUSER.T_PREM_ARAP A
                 WHERE A.FEE_STATUS in ('00', '03')
                   AND A.ARAP_FLAG = '1'
                   AND A.DERIV_TYPE = '003'
                   AND A.FROZEN_STATUS != '03'
                   AND A.POLICY_CODE = #{policy_code}
                 GROUP BY A.POLICY_CODE, A.BUSI_PROD_CODE) C	             
	]]>
	</select>
	
	<!-- 外围接口使用查询所有操作 -->
	<select id="JRQD_CS_findAllPremArapForOut" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID , 
			(SELECT TRC.BANK_RET_NAME
          	FROM APP___CAP__DBUSER.T_BANK_RET_CONF TRC
        	 WHERE TRC.BANK_RET_CODE = A.FUNDS_RTN_CODE) AS RETURN_FAILURE_REASON
			FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <=  1000   ]]>
		<include refid="JRQD_PA_premArapWhereCondition" />
		<![CDATA[ ORDER BY UNIT_NUMBER ]]>
	</select>
	
  	<!-- 根据保单号查询收付费信息 -->
	<select id="JRQD_PA_findAllXQPrem" resultType="java.util.Map" parameterType="java.util.Map">
	    select sum(tp.fee_amount) fee_amount from APP___PAS__DBUSER.t_prem tm,APP___PAS__DBUSER.t_prem_arap tp
	    where 1=1 
	    and tp.policy_code = #{policy_code}
	    and tm.unit_number = tp.unit_number
	    group by tp.finish_time
	</select>
	
	<!-- 查询保单是否在宽限期内 -->
	<select id="JRQD_PA_queryGracePeriod" resultType="java.util.Map" parameterType="java.util.Map">
	         <![CDATA[SELECT A.POLICY_CODE,A.DUE_TIME,A.PAY_END_DATE
        FROM APP___PAS__DBUSER.T_PREM_ARAP       A,
             APP___PAS__DBUSER.T_CONTRACT_MASTER B
       WHERE A.POLICY_CODE = B.POLICY_CODE
         AND B.LIABILITY_STATE = 1
         AND A.FEE_STATUS IN ('00', '03')
         AND (A.FROZEN_STATUS <> '03' OR A.FROZEN_STATUS IS NULL)
         AND A.ARAP_FLAG = '1'
         AND A.DERIV_TYPE = '003'
         AND A.FEE_TYPE IN ('G003010000', 'G003100000')
         AND A.PAY_END_DATE > #{pay_end_date}
         AND A.DUE_TIME < #{pay_end_date}
         AND A.POLICY_CODE = #{policy_code}
         ]]>
	</select>
	
	<!-- 保全收付费信息保单列表查询-查询所有收付费历史记录 -->
	<select id="JRQD_PA_queryPremHistory" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT DISTINCT (SELECT S.SERVICE_NAME
			  FROM DEV_PAS.T_SERVICE S
			 WHERE S.SERVICE_CODE = AC.SERVICE_CODE) SERVICE_CODE,
		   CM.POLICY_CODE,
		   PA.HOLDER_NAME,
		   PA.INSURED_NAME,
		   (SELECT B.PRODUCT_NAME_SYS
			  FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
			 WHERE B.BUSINESS_PRD_ID = BP.BUSI_PRD_ID) PRODUCT_NAME,
		   (SELECT A.STATUS_NAME
			  FROM DEV_PAS.T_LIABILITY_STATUS A
			 WHERE A.STATUS_CODE = CM.LIABILITY_STATE) STATUS_NAME,
		   CM.APPLY_DATE,
		   CM.VALIDATE_DATE,
		   CM.LAPSE_DATE,
		   CM.ORGAN_CODE,
		   (SELECT A.ORGAN_NAME
			  FROM DEV_PAS.T_UDMP_ORG A
			 WHERE A.ORGAN_CODE = CM.ORGAN_CODE) ORGAN_NAME,
		   (SELECT (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '1'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) -
				   (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '2'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) AS SUMFEEAMOUNT
			  FROM DUAL) FEE_AMOUNT,
		   PA.BANK_USER_NAME,
		   (SELECT D.BANK_NAME
			  FROM DEV_PAS.T_BANK D
			 WHERE D.BANK_CODE = PA.BANK_CODE) BANK_NAME,
		   PA.BANK_ACCOUNT,
		   AC.ACCEPT_CODE
	  FROM DEV_CAP.T_PREM_ARAP PA
	  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
		ON PA.BUSINESS_CODE = AC.ACCEPT_CODE
	  LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
		ON PC.ACCEPT_ID = AC.ACCEPT_ID
	  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
		ON CM.POLICY_ID = PC.POLICY_ID
	  LEFT JOIN DEV_PAS.T_POLICY_HOLDER PH
		ON PH.POLICY_ID = CM.POLICY_ID
	  LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD BP
		ON BP.POLICY_CHG_ID = PC.POLICY_CHG_ID
	   AND BP.MASTER_BUSI_ITEM_ID IS NULL
	   AND BP.OLD_NEW = '1'
	  LEFT JOIN DEV_PAS.T_CUSTOMER C
		ON C.CUSTOMER_ID = PH.CUSTOMER_ID
	 WHERE C.CUSTOMER_ID = #{customer_id} ]]>
  
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND CM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND CM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state} ]]></if>
	
	 <![CDATA[  
	     AND AC.SERVICE_CODE IN ('RE', 'LN', 'RL', 'RF')
	     AND NOT EXISTS (SELECT 1
			  FROM DEV_CAP.T_CASH_DETAIL CD
			 WHERE CD.BUSINESS_CODE = AC.ACCEPT_CODE)
	UNION
	SELECT (SELECT S.SERVICE_NAME
			  FROM DEV_PAS.T_SERVICE S
			 WHERE S.SERVICE_CODE = AC.SERVICE_CODE) SERVICE_CODE,
		   CM.POLICY_CODE,
		   PA.HOLDER_NAME,
		   PA.INSURED_NAME,
		   (SELECT B.PRODUCT_NAME_SYS
			  FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
			 WHERE B.BUSINESS_PRD_ID = BP.BUSI_PRD_ID) PRODUCT_NAME,
		   (SELECT A.STATUS_NAME
			  FROM DEV_PAS.T_LIABILITY_STATUS A
			 WHERE A.STATUS_CODE = CM.LIABILITY_STATE) STATUS_NAME,
		   CM.APPLY_DATE,
		   CM.VALIDATE_DATE,
		   CM.LAPSE_DATE,
		   CM.ORGAN_CODE,
		   (SELECT A.ORGAN_NAME
			  FROM DEV_PAS.T_UDMP_ORG A
			 WHERE A.ORGAN_CODE = CM.ORGAN_CODE) ORGAN_NAME,
		   (SELECT (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '1'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) -
				   (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '2'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) AS SUMFEEAMOUNT
			  FROM DUAL) FEE_AMOUNT,
		   PA.BANK_USER_NAME,
		   (SELECT D.BANK_NAME
			  FROM DEV_PAS.T_BANK D
			 WHERE D.BANK_CODE = PA.BANK_CODE) BANK_NAME,
		   PA.BANK_ACCOUNT,
		   AC.ACCEPT_CODE
	  FROM DEV_CAP.T_PREM_ARAP PA
	  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
		ON PA.BUSINESS_CODE = AC.ACCEPT_CODE
	  LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
		ON PC.ACCEPT_ID = AC.ACCEPT_ID
	  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
		ON CM.POLICY_ID = PC.POLICY_ID
	  LEFT JOIN DEV_PAS.T_BENEFIT_INSURED BI
		ON BI.POLICY_ID = CM.POLICY_ID
	   AND BI.ORDER_ID = '1'
	  LEFT JOIN DEV_PAS.T_INSURED_LIST IL
		ON IL.LIST_ID = BI.INSURED_ID
	  LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD BP
		ON BP.POLICY_CHG_ID = PC.POLICY_CHG_ID
	   AND BP.MASTER_BUSI_ITEM_ID IS NULL
	   AND BP.OLD_NEW = '1'
	  LEFT JOIN DEV_PAS.T_CUSTOMER C
		ON C.CUSTOMER_ID = IL.CUSTOMER_ID
	 WHERE C.CUSTOMER_ID = #{customer_id}
	   AND AC.SERVICE_CODE = 'AG'
	   AND NOT EXISTS (SELECT 1
			  FROM DEV_CAP.T_CASH_DETAIL CD
			 WHERE CD.BUSINESS_CODE = AC.ACCEPT_CODE)
	]]>
	 <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND CM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND CM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state} ]]></if>
	
	</select>
	
	<!-- 保全收付费信息保单列表查询-查询投保人收付费历史记录 -->
	<select id="JRQD_PA_queryHolderPremHistory" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT DISTINCT (SELECT S.SERVICE_NAME
			  FROM DEV_PAS.T_SERVICE S
			 WHERE S.SERVICE_CODE = AC.SERVICE_CODE) SERVICE_CODE,
		   CM.POLICY_CODE,
		   PA.HOLDER_NAME,
		   PA.INSURED_NAME,
		   (SELECT B.PRODUCT_NAME_SYS
			  FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
			 WHERE B.BUSINESS_PRD_ID = BP.BUSI_PRD_ID) PRODUCT_NAME,
		   (SELECT A.STATUS_NAME
			  FROM DEV_PAS.T_LIABILITY_STATUS A
			 WHERE A.STATUS_CODE = CM.LIABILITY_STATE) STATUS_NAME,
		   CM.APPLY_DATE,
		   CM.VALIDATE_DATE,
		   CM.LAPSE_DATE,
		   CM.ORGAN_CODE,
		   (SELECT A.ORGAN_NAME
			  FROM DEV_PAS.T_UDMP_ORG A
			 WHERE A.ORGAN_CODE = CM.ORGAN_CODE) ORGAN_NAME,
		   (SELECT (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '1'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) -
				   (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '2'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) AS SUMFEEAMOUNT
			  FROM DUAL) FEE_AMOUNT,
		   PA.BANK_USER_NAME,
		   (SELECT D.BANK_NAME
			  FROM DEV_PAS.T_BANK D
			 WHERE D.BANK_CODE = PA.BANK_CODE) BANK_NAME,
		   PA.BANK_ACCOUNT,
		   AC.ACCEPT_CODE
	  FROM DEV_CAP.T_PREM_ARAP PA
	  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
		ON PA.BUSINESS_CODE = AC.ACCEPT_CODE
	  LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
		ON PC.ACCEPT_ID = AC.ACCEPT_ID
	  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
		ON CM.POLICY_ID = PC.POLICY_ID
	  LEFT JOIN DEV_PAS.T_POLICY_HOLDER PH
		ON PH.POLICY_ID = CM.POLICY_ID
	  LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD BP
		ON BP.POLICY_CHG_ID = PC.POLICY_CHG_ID
	   AND BP.MASTER_BUSI_ITEM_ID IS NULL
	   AND BP.OLD_NEW = '1'
	  LEFT JOIN DEV_PAS.T_CUSTOMER C
		ON C.CUSTOMER_ID = PH.CUSTOMER_ID
	 WHERE C.CUSTOMER_ID = #{customer_id}
	   AND AC.SERVICE_CODE = #{service_code} ]]>
	   
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND CM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND CM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state} ]]></if>
	   
   <![CDATA[ AND NOT EXISTS (SELECT 1
			  FROM DEV_CAP.T_CASH_DETAIL CD
			 WHERE CD.BUSINESS_CODE = AC.ACCEPT_CODE)
	]]>
	</select>
	
	<!-- 保全收付费信息保单列表查询-查询投保人收付费历史记录 -->
	<select id="JRQD_PA_queryPoliyHolderPremHistory" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT DISTINCT (SELECT S.SERVICE_NAME
			  FROM DEV_PAS.T_SERVICE S
			 WHERE S.SERVICE_CODE = AC.SERVICE_CODE) SERVICE_CODE,
		   CM.POLICY_CODE,
		   PA.HOLDER_NAME,
		   PA.INSURED_NAME,
		   (SELECT B.PRODUCT_NAME_SYS
			  FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
			 WHERE B.BUSINESS_PRD_ID = BP.BUSI_PRD_ID) PRODUCT_NAME,
		   (SELECT A.STATUS_NAME
			  FROM DEV_PAS.T_LIABILITY_STATUS A
			 WHERE A.STATUS_CODE = CM.LIABILITY_STATE) STATUS_NAME,
		   CM.APPLY_DATE,
		   CM.VALIDATE_DATE,
		   CM.LAPSE_DATE,
		   CM.ORGAN_CODE,
		   (SELECT A.ORGAN_NAME
			  FROM DEV_PAS.T_UDMP_ORG A
			 WHERE A.ORGAN_CODE = CM.ORGAN_CODE) ORGAN_NAME,
		   (SELECT (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '1'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) -
				   (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '2'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) AS SUMFEEAMOUNT
			  FROM DUAL) FEE_AMOUNT,
		   PA.BANK_USER_NAME,
		   (SELECT D.BANK_NAME
			  FROM DEV_PAS.T_BANK D
			 WHERE D.BANK_CODE = PA.BANK_CODE) BANK_NAME,
		   PA.BANK_ACCOUNT,
		   AC.ACCEPT_CODE
	  FROM DEV_CAP.T_PREM_ARAP PA
	  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
		ON PA.BUSINESS_CODE = AC.ACCEPT_CODE
	  LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
		ON PC.ACCEPT_ID = AC.ACCEPT_ID
	  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
		ON CM.POLICY_ID = PC.POLICY_ID
	  LEFT JOIN DEV_PAS.T_POLICY_HOLDER PH
	    ON PH.POLICY_CODE = CM.POLICY_CODE
	  LEFT JOIN DEV_PAS.T_CUSTOMER C
	    ON C.CUSTOMER_ID = PH.CUSTOMER_ID
	  LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD BP
		ON BP.POLICY_CHG_ID = PC.POLICY_CHG_ID
	   AND BP.MASTER_BUSI_ITEM_ID IS NULL
	   AND BP.OLD_NEW = '1'
	 WHERE C.CUSTOMER_ID = #{customer_id}
	   AND AC.SERVICE_CODE = #{service_code} ]]>
	   
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND CM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND CM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state} ]]></if>
      
	  <![CDATA[ AND NOT EXISTS (SELECT 1
			  FROM DEV_CAP.T_CASH_DETAIL CD
			 WHERE CD.BUSINESS_CODE = AC.ACCEPT_CODE)
	]]>
	</select>
	
	<!-- 保全收付费信息保单列表查询-查询被保人收付费历史记录 -->
	<select id="JRQD_PA_queryInsuredPremHistory" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT DISTINCT (SELECT S.SERVICE_NAME
			  FROM DEV_PAS.T_SERVICE S
			 WHERE S.SERVICE_CODE = AC.SERVICE_CODE) SERVICE_CODE,
		   CM.POLICY_CODE,
		   PA.HOLDER_NAME,
		   PA.INSURED_NAME,
		   (SELECT B.PRODUCT_NAME_SYS
			  FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT B
			 WHERE B.BUSINESS_PRD_ID = BP.BUSI_PRD_ID) PRODUCT_NAME,
		   (SELECT A.STATUS_NAME
			  FROM DEV_PAS.T_LIABILITY_STATUS A
			 WHERE A.STATUS_CODE = CM.LIABILITY_STATE) STATUS_NAME,
		   CM.APPLY_DATE,
		   CM.VALIDATE_DATE,
		   CM.LAPSE_DATE,
		   CM.ORGAN_CODE,
		   (SELECT A.ORGAN_NAME
			  FROM DEV_PAS.T_UDMP_ORG A
			 WHERE A.ORGAN_CODE = CM.ORGAN_CODE) ORGAN_NAME,
		   (SELECT (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '1'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) -
				   (SELECT NVL(SUM(FEE_AMOUNT), 0) SUMFEEAMOUNT
					  FROM APP___CAP__DBUSER.T_PREM_ARAP A
					 WHERE A.ARAP_FLAG = '2'
					   AND A.BUSINESS_CODE = AC.ACCEPT_CODE) AS SUMFEEAMOUNT
			  FROM DUAL) FEE_AMOUNT,
		   PA.BANK_USER_NAME,
		   (SELECT D.BANK_NAME
			  FROM DEV_PAS.T_BANK D
			 WHERE D.BANK_CODE = PA.BANK_CODE) BANK_NAME,
		   PA.BANK_ACCOUNT,
		   AC.ACCEPT_CODE
	  FROM DEV_CAP.T_PREM_ARAP PA
	  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
		ON PA.BUSINESS_CODE = AC.ACCEPT_CODE
	  LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
		ON PC.ACCEPT_ID = AC.ACCEPT_ID
	  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
		ON CM.POLICY_ID = PC.POLICY_ID
	  LEFT JOIN DEV_PAS.T_BENEFIT_INSURED BI
		ON BI.POLICY_ID = CM.POLICY_ID
	   AND BI.ORDER_ID = '1'
	  LEFT JOIN DEV_PAS.T_INSURED_LIST IL
		ON IL.LIST_ID = BI.INSURED_ID
	  LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD BP
		ON BP.POLICY_CHG_ID = PC.POLICY_CHG_ID
	   AND BP.MASTER_BUSI_ITEM_ID IS NULL
	   AND BP.OLD_NEW = '1'
	  LEFT JOIN DEV_PAS.T_CUSTOMER C
		ON C.CUSTOMER_ID = IL.CUSTOMER_ID
	 WHERE C.CUSTOMER_ID = #{customer_id}
	   AND AC.SERVICE_CODE = #{service_code} 
	   ]]>
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND CM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND CM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state} ]]></if> 
	<![CDATA[  AND NOT EXISTS (SELECT 1
			  FROM DEV_CAP.T_CASH_DETAIL CD
			 WHERE CD.BUSINESS_CODE = AC.ACCEPT_CODE)
	]]>
	</select>
	
	<!-- 保全收付费信息保单列表查询-查询AG保全项信息 -->
	<select id="JRQD_PA_queryAGPremHistory" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 
			   AC.ACCEPT_CODE,
			   AC.ACCEPT_TIME
			FROM DEV_CAP.T_PREM_ARAP PA
			LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
			ON PA.BUSINESS_CODE = AC.ACCEPT_CODE
			LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
			ON PC.ACCEPT_ID = AC.ACCEPT_ID
			LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
			ON CM.POLICY_ID = PC.POLICY_ID
			LEFT JOIN DEV_PAS.T_POLICY_HOLDER PH
			  ON PH.POLICY_CODE = CM.POLICY_CODE
			LEFT JOIN DEV_PAS.T_CUSTOMER C
			  ON C.CUSTOMER_ID = PH.CUSTOMER_ID
			WHERE C.CUSTOMER_ID = #{customer_id}
	   		AND AC.SERVICE_CODE = #{service_code} 
			 AND NOT EXISTS (SELECT 1
			    FROM DEV_CAP.T_CASH_DETAIL CD
			   WHERE CD.BUSINESS_CODE = AC.ACCEPT_CODE)
			]]>
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND CM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND CM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state} ]]></if>		
		<![CDATA[   
			UNION
			SELECT AC.ACCEPT_CODE,
			   AC.ACCEPT_TIME
			    FROM DEV_CAP.T_PREM_ARAP PA
			    LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
			    ON PA.BUSINESS_CODE = AC.ACCEPT_CODE
			    LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
			    ON PC.ACCEPT_ID = AC.ACCEPT_ID
			    LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
			    ON CM.POLICY_ID = PC.POLICY_ID
			    LEFT JOIN DEV_PAS.T_BENEFIT_INSURED BI
			    ON BI.POLICY_ID = CM.POLICY_ID
			     AND BI.ORDER_ID = '1'
			    LEFT JOIN DEV_PAS.T_INSURED_LIST IL
			    ON IL.LIST_ID = BI.INSURED_ID
			    LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD BP
			    ON BP.POLICY_CHG_ID = PC.POLICY_CHG_ID
			     AND BP.MASTER_BUSI_ITEM_ID IS NULL
			     AND BP.OLD_NEW = '1'
			    LEFT JOIN DEV_PAS.T_CUSTOMER C
			    ON C.CUSTOMER_ID = IL.CUSTOMER_ID
			   WHERE C.CUSTOMER_ID = #{customer_id}
	   			AND AC.SERVICE_CODE = #{service_code} 
	   			]]>
   <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND CM.VALIDATE_DATE >= #{validate_date} ]]></if>	
   <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND CM.VALIDATE_DATE <= #{suspend_date} ]]></if>
   <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND CM.LIABILITY_STATE = #{liability_state} ]]></if>		
		<![CDATA[ AND NOT EXISTS (SELECT 1
			        FROM DEV_CAP.T_CASH_DETAIL CD
			       WHERE CD.BUSINESS_CODE = AC.ACCEPT_CODE)
		]]>
	</select>
	
	<!-- 查询保单主险尚未缴纳保险费 -->
	<select id="JRQD_PA_queryPAGracePeriod" resultType="java.util.Map" parameterType="java.util.Map">
	         <![CDATA[
	  SELECT A.POLICY_CODE,A.DUE_TIME,A.PAY_END_DATE,prem_freq
        FROM APP___PAS__DBUSER.T_PREM_ARAP A,
             APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
       WHERE A.POLICY_CODE = B.POLICY_CODE
			   AND A.BUSI_PROD_CODE = B.BUSI_PROD_CODE
         AND B.LIABILITY_STATE = 1
         AND A.FEE_STATUS IN ('00', '03')
         AND (A.FROZEN_STATUS <> '03' OR A.FROZEN_STATUS IS NULL)
         AND A.ARAP_FLAG = '1'
				 AND B.MASTER_BUSI_ITEM_ID IS NULL
         AND A.DERIV_TYPE = '003'
         AND A.FEE_TYPE IN ('G003100000','G003010000','G003020200','G003030100','G003030200','G003020100')
         AND A.POLICY_CODE= #{policy_code}
         ]]>
	</select>
	
	<!-- 查询保全应收应付状态 -->
	<select id="JRQD_findBQBYPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	         <![CDATA[
	  SELECT A.CHARGE_PERIOD,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <= 1000 
         ]]>
         <include refid="JRQD_PA_premArapWhereCondition" />
		<include refid="JRQD_PA_premArapWhereListCondition"  />
	</select>
	
	<!-- 查询该保单下是否有新增的长期附加险已推送收付费但缴费未成功的数据 -->
	<select id="JRQD_PA_findBQYSYFByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT AR.FEE_STATUS,AR.POLICY_CODE,AR.ARAP_FLAG
			FROM APP___PAS__DBUSER.T_CS_PREM_ARAP AR 
			WHERE AR.FEE_STATUS IN ('00','03')
			AND AR.POLICY_CODE = #{policy_code}
			AND AR.ARAP_FLAG = '1'
			UNION
			SELECT AR.FEE_STATUS,AR.POLICY_CODE,AR.ARAP_FLAG
			FROM APP___PAS__DBUSER.T_CS_PREM_ARAP AR 
			WHERE AR.FEE_STATUS IN ('00','03')
			AND AR.POLICY_CODE = #{policy_code}
			AND AR.ARAP_FLAG = '2'
       ]]>
	</select>
	
	<!-- 查询该保单当期保费缴费状态 -->
	<select id="JRQD_PA_findPremArapByFeeStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.FEE_STATUS, A.DUE_TIME,A.BUSI_PROD_CODE,A.PREM_FREQ
			  FROM APP___PAS__DBUSER.T_PREM_ARAP A
			 WHERE A.DERIV_TYPE = '003'
			   AND A.POLICY_CODE = #{policy_code}
			   AND A.DUE_TIME = 
			       (SELECT MAX(B.DUE_TIME)
			          FROM APP___PAS__DBUSER.T_PREM_ARAP B
			         WHERE B.DERIV_TYPE = '003'
			           AND B.POLICY_CODE = #{policy_code})
       ]]>
	</select>
	
	
	<!-- 查询保单是否存在应收未收 -->
	<select id="JRQD_PA_findPremArapFeeStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.CHARGE_PERIOD, A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.BOOKKEEPING_FLAG, A.POSTED, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME,  A.CUS_ACC_DETAILS_ID, 
			A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, 
			A.BRANCH_CODE, A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, 
			A.GROUP_CODE, A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.IS_BANK_ACCOUNT_BY, A.BUSINESS_CODE, A.MONEY_CODE, 
			A.PAYEE_NAME, A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, 
			A.DUE_TIME, A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.LIST_ID, 
			A.FEE_AMOUNT, A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, 
			A.POLICY_YEAR, A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, 
			A.RED_BOOKKEEPING_ID, A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, 
			A.AGENT_CODE, A.PREM_FREQ, A.POLICY_CHG_ID FROM APP___PAS__DBUSER.T_PREM_ARAP A  WHERE ROWNUM <= 1000 
			AND A.ARAP_FLAG = '1' 
			AND A.FEE_STATUS IN ('00','03')
			AND A.POLICY_CODE = #{policy_code}
			AND A.BUSI_PROD_CODE = #{busi_prod_code}
       ]]>
	</select>
	
	
	
	<!-- 查询个数操作 -->
	<select id="JRQD_findOnWaryCountForRB" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT  count(1) FROM APP___CAP__DBUSER.T_PREM_ARAP A 
				WHERE  A.FEE_STATUS='04' 
				   AND A.POLICY_CODE = #{policy_code}
	    ]]>		
	</select>
	
	<!-- 查询保单贷款是否清偿 -->
	<select id="JRQD_findLoanIsPayOff" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT CASE WHEN SUM(CAPITAL_BALANCE) > 0  THEN 1
                  WHEN SUM(CAPITAL_BALANCE) = 0 THEN 0
                  ELSE 0 END AS CAPITAL_BALANCE
      FROM (
           SELECT SUM(T.FEE_AMOUNT) AS CAPITAL_BALANCE FROM APP___PAS__DBUSER.T_PREM_ARAP T 
           WHERE EXISTS (SELECT A.POLICY_CODE FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE A.POLICY_ID= #{policy_id} AND T.POLICY_CODE = A.POLICY_CODE) AND T.FEE_TYPE = '38'
           UNION ALL
           SELECT SUM(T.CAPITAL_BALANCE) AS CAPITAL_BALANCE FROM   APP___PAS__DBUSER.T_POLICY_ACCOUNT T 
           WHERE  T.ACCOUNT_TYPE = 4 AND T.POLICY_ID = #{policy_id} )
       ]]>
	</select>

	<!-- 查询南京医保社保缴纳的费用 -->
	<select id="JRQD_findAllPremArapByNJYB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select pa.CHARGE_PERIOD,pa.TASK_MARK, pa.POLICY_ORGAN_CODE, pa.HOLDER_NAME, pa.IS_ITEM_MAIN, pa.BOOKKEEPING_ID, pa.BATCH_NO, pa.BUSI_PROD_NAME,
			pa.PAYEE_PHONE, pa.UNIT_NUMBER, pa.PAID_COUNT, pa.FUNDS_RTN_CODE, pa.CUSTOMER_ACCOUNT_FLAG, pa.FEE_TYPE, pa.SEQ_NO,
			pa.BUSI_PROD_CODE, pa.APPLY_CODE, pa.FEE_STATUS_DATE, pa.ORGAN_CODE, pa.RED_BOOKKEEPING_TIME, pa.CHANNEL_TYPE, pa.IS_BANK_TEXT_DATE,
			pa.CHARGE_YEAR, pa.IS_RISK_MAIN, pa.IS_BANK_ACCOUNT, pa.FROZEN_STATUS, pa.BOOKKEEPING_FLAG, pa.POSTED,
			pa.GROUP_ID, pa.RED_BOOKKEEPING_BY, pa.FROZEN_STATUS_DATE, pa.INSURED_NAME,  pa.CUS_ACC_DETAILS_ID,
			pa.INSURED_ID, pa.POLICY_TYPE, pa.PRODUCT_CHANNEL, pa.IS_BANK_ACCOUNT_DATE, pa.POLICY_CODE, pa.PAY_MODE, pa.OPERATOR_BY,
			pa.BRANCH_CODE, pa.BUSINESS_TYPE, pa.VALIDATE_DATE, pa.RED_BELNR, pa.BANK_TEXT_STATUS,
			pa.GROUP_CODE, pa.CERTI_TYPE, pa.CUS_ACC_UPDATE_BY, pa.IS_BANK_TEXT_BY, pa.IS_BANK_ACCOUNT_BY, pa.BUSINESS_CODE, pa.MONEY_CODE,
			pa.PAYEE_NAME, pa.BANK_ACCOUNT, pa.RED_BOOKKEEPING_FLAG, pa.CUS_ACC_FEE_AMOUNT, pa.CUSTOMER_ID, pa.FINISH_TIME,
			pa.DUE_TIME, pa.IS_BANK_TEXT, pa.CERTI_CODE, pa.BUSI_APPLY_DATE, pa.GROUP_NAME, pa.BELNR, pa.LIST_ID,
			pa.FEE_AMOUNT, pa.SERVICE_CODE, pa.HOLDER_ID, pa.WITHDRAW_TYPE, pa.ROLLBACK_UNIT_NUMBER, pa.FAIL_TIMES,
			pa.POLICY_YEAR, pa.FROZEN_STATUS_BY, pa.BANK_USER_NAME, pa.FEE_STATUS, pa.FEE_STATUS_BY, pa.PAY_END_DATE, pa.DERIV_TYPE,
			pa.RED_BOOKKEEPING_ID, pa.BOOKKEEPING_BY, pa.CUS_ACC_UPDATE_TIME, pa.ARAP_FLAG, pa.BANK_CODE, pa.BOOKKEEPING_TIME, pa.REFEFLAG,
			pa.AGENT_CODE, pa.PREM_FREQ, pa.POLICY_CHG_ID from dev_pas.t_prem_arap pa ,dev_pas.t_contract_master cm
			 where  cm.policy_code = pa.policy_code and cm.medical_insurance_card = '3' and pa.business_code = #{business_code} and pa.pay_mode = '18' and pa.fee_status = #{fee_status}
       ]]>
	</select>
	
	<!--查询保单首期或续期保费缴费信息-->
	<select id="JRQD_findPolicyG001010000OrG003010000" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select t.CHARGE_PERIOD,t.TASK_MARK, t.POLICY_ORGAN_CODE, t.HOLDER_NAME, t.IS_ITEM_MAIN, t.BOOKKEEPING_ID, t.BATCH_NO, t.BUSI_PROD_NAME, 
			t.PAYEE_PHONE, t.UNIT_NUMBER, t.PAID_COUNT, t.FUNDS_RTN_CODE, t.CUSTOMER_ACCOUNT_FLAG, t.FEE_TYPE, t.SEQ_NO, 
			t.BUSI_PROD_CODE, t.APPLY_CODE, t.FEE_STATUS_DATE, t.ORGAN_CODE, t.RED_BOOKKEEPING_TIME, t.CHANNEL_TYPE, t.IS_BANK_TEXT_DATE, 
			t.CHARGE_YEAR, t.IS_RISK_MAIN, t.IS_BANK_ACCOUNT, t.FROZEN_STATUS, t.BOOKKEEPING_FLAG, t.POSTED, 
			t.GROUP_ID, t.RED_BOOKKEEPING_BY, t.FROZEN_STATUS_DATE, t.INSURED_NAME,  t.CUS_ACC_DETAILS_ID, 
			t.INSURED_ID, t.POLICY_TYPE, t.PRODUCT_CHANNEL, t.IS_BANK_ACCOUNT_DATE, t.POLICY_CODE, t.PAY_MODE, t.OPERATOR_BY, 
			t.BRANCH_CODE, t.BUSINESS_TYPE, t.VALIDATE_DATE, t.RED_BELNR, t.BANK_TEXT_STATUS, 
			t.GROUP_CODE, t.CERTI_TYPE, t.CUS_ACC_UPDATE_BY, t.IS_BANK_TEXT_BY, t.IS_BANK_ACCOUNT_BY, t.BUSINESS_CODE, t.MONEY_CODE, 
			t.PAYEE_NAME, t.BANK_ACCOUNT, t.RED_BOOKKEEPING_FLAG, t.CUS_ACC_FEE_AMOUNT, t.CUSTOMER_ID, t.FINISH_TIME, 
			t.DUE_TIME, t.IS_BANK_TEXT, t.CERTI_CODE, t.BUSI_APPLY_DATE, t.GROUP_NAME, t.BELNR, t.LIST_ID, 
			t.FEE_AMOUNT, t.SERVICE_CODE, t.HOLDER_ID, t.WITHDRAW_TYPE, t.ROLLBACK_UNIT_NUMBER, t.FAIL_TIMES, 
			t.POLICY_YEAR, t.FROZEN_STATUS_BY, t.BANK_USER_NAME, t.FEE_STATUS, t.FEE_STATUS_BY, t.PAY_END_DATE, t.DERIV_TYPE, 
			t.RED_BOOKKEEPING_ID, t.BOOKKEEPING_BY, t.CUS_ACC_UPDATE_TIME, t.ARAP_FLAG, t.BANK_CODE, t.BOOKKEEPING_TIME, t.REFEFLAG, 
			t.AGENT_CODE, t.PREM_FREQ, t.POLICY_CHG_ID, t.STATISTICAL_DATE
		  from dev_cap.t_prem_arap t, dev_pas.t_contract_busi_prod cbp
		 where t.policy_code = #{policy_code}
		   and cbp.policy_code = t.policy_code
		   and cbp.busi_prod_code = t.busi_prod_code
		   and cbp.master_busi_item_id is null
		   and t.fee_type in ('G001010000','G003010000')
		   and t.fee_status = '01'
		   and rownum = 1
		   order by t.insert_time desc
		]]>
	</select>
	
	
	<select id="JRQD_queryPremArapDataForAM" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.BUSI_PROD_CODE, SUM(T.FEE_AMOUNT) FEE_AMOUNT, T.DUE_TIME
				  FROM APP___PAS__DBUSER.T_PREM_ARAP T
				 WHERE T.BUSINESS_CODE = #{business_code}
			]]>	 
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND T.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
			<if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND T.DERIV_TYPE = #{deriv_type} ]]></if>
			<![CDATA[	 
				 GROUP BY T.BUSI_PROD_CODE, T.DUE_TIME
			]]>
	</select>
	
	<select id="JRQD_PA_findAllRenewalUnpaid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				    SELECT A.POLICY_CODE, A.BUSI_PROD_CODE 
					  FROM DEV_PAS.T_PREM_ARAP A 
					 WHERE 1 = 1 
					   AND A.FEE_TYPE IN ('G003100000', 
					                      'G003010000', 
					                      'G003020100', 
					                      'G003020200', 
					                      'G003030100', 
					                      'G003030200', 
					                      'G003040100', 
					                      'G003040200') 
					   AND A.FEE_STATUS IN ('00', '03', '20') 
					   AND A.DERIV_TYPE = '003' 
					   AND A.ARAP_FLAG = '1' 
					   AND A.POLICY_CODE = #{policy_code}  ]]>
	</select>
	
	<select id="JRQD_PA_findAllXQPremByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.FEE_STATUS,T.BUSINESS_TYPE,T.DUE_TIME,T.FINISH_TIME FROM DEV_PAS.T_PREM_ARAP T 
      		 WHERE 1=1 
			 AND T.ARAP_FLAG = '1'
			 AND T.FEE_STATUS IN ('01','19')
			 AND T.BUSINESS_TYPE IN ('4003','1005')
			 AND T.BANK_ACCOUNT = #{bank_account}
			 AND T.POLICY_CODE = #{policy_code}
			]]>
	</select>
	
	<select id="JRQD_PA_queryLastRenewal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.LIST_ID,
			       A.DUE_TIME,
			       A.FEE_TYPE,
			       A.PAID_COUNT,
			       A.BUSINESS_TYPE,
			       A.DERIV_TYPE,
			       A.FEE_STATUS,
			       A.ARAP_FLAG
			  FROM APP___PAS__DBUSER.T_PREM_ARAP A
			 WHERE 1=1 ]]>
			    <include refid="JRQD_PA_premArapWhereCondition" />
			  <![CDATA[ AND A.DERIV_TYPE = '003'
			   AND A.BUSINESS_TYPE IN ('4003', '1005', '4002')
			 ORDER BY A.DUE_TIME DESC
			]]>
	</select>	
	
	<!--查询保单存在银行转账途中的应收应付费用(新增长期附加险首期保费) -->
	<select id="JRQD_PA_queryPremArapByFJ" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      select CPP.ACCEPT_ID,CAC.ACCEPT_CODE 
			  from dev_pas.t_cs_policy_change cpc,
			   dev_pas.t_Cs_Accept_Change CAC,
			   DEV_PAS.T_CS_PRECONT_PRODUCT CPP,
				 dev_cap.t_prem_arap  tpa 
			 where CPC.ACCEPT_ID = CAC.ACCEPT_ID
			 AND CPP.ACCEPT_ID = CPC.ACCEPT_ID
			 AND CAC.ACCEPT_CODE = TPA.BUSINESS_CODE
			 AND TPA.FEE_STATUS = '04'
			 AND CAC.service_code = 'NS' 
			 AND CAC.ACCEPT_STATUS = '18'
			 AND CPC.POLICY_CODE = #{policy_code}
	]]>
	</select>
	
	<select id="JRQD_PA_findXQPremForYdbq" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.FEE_STATUS,T.BUSINESS_TYPE,T.DUE_TIME,T.FINISH_TIME FROM DEV_PAS.T_PREM_ARAP T 
      		 WHERE 1=1 
			 AND T.ARAP_FLAG = '1'
			 AND T.FEE_STATUS IN ('01','19')
			 AND T.BUSINESS_TYPE IN ('4003','1005')
			 AND T.POLICY_CODE = #{policy_code}
			]]>
	</select>
	
	<!--查询保单存在银行转账途中的应收应付费用（续期保费、生存金、满期金和年金给付）-->
	<select id="JRQD_PA_queryPremArapByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      select T.FEE_STATUS, T.UNIT_NUMBER
				  from DEV_CAP.T_PREM_ARAP T
				 WHERE T.FEE_TYPE IN('P004390200', 'P004390300', 'G003010000','G003020100','G003020200','T003100200')
				   AND T.FEE_STATUS = '04'
				   AND T.POLICY_CODE = #{policy_code}
	]]>
	</select>
	
	<!--查询保单下所有附加险首期收费到账日期-->
	<select id="JRQD_PA_queryPremArapALLByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	      select tpa.finish_time from dev_pas.t_cs_policy_change tcpc,
              				dev_pas.t_cs_accept_change tcac,
							dev_cap.t_prem_arap tpa
							where 1=1
							and tcpc.accept_id = tcac.accept_id
							and tcac.accept_code = tpa.business_code
							and tcac.accept_status = '18'
							and tpa.fee_status = '01'
							and tcpc.policy_code = #{policy_code}
							and tpa.busi_prod_code = #{busi_prod_code}
        union
		select TPA.FINISH_TIME from dev_pas.t_cs_policy_change cpc,
						   dev_pas.t_Cs_Accept_Change CAC,
						   DEV_PAS.T_CS_PRECONT_PRODUCT CPP,
						   dev_cap.t_prem_arap  tpa 
						 where CPC.ACCEPT_ID = CAC.ACCEPT_ID
						 AND CPP.ACCEPT_ID = CPC.ACCEPT_ID
						 AND CAC.ACCEPT_CODE = TPA.BUSINESS_CODE
						 AND TPA.FEE_STATUS = '01'
						 AND CAC.ACCEPT_STATUS = '18'
						 AND CAC.service_code = 'NS' 
						 AND CPC.POLICY_CODE = #{policy_code}
						 AND TPA.BUSI_PROD_CODE = #{busi_prod_code}
	]]>
	</select>
	
	
	<select id="JRQD_PA_findPremArapDataForI17" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ select A.LIST_ID,
		       A.UNIT_NUMBER,
		       A.BUSINESS_CODE,
		       A.APPLY_CODE,
		       A.INSERT_BY,
		       A.POLICY_CODE,
		       A.BUSI_PROD_CODE,
		       A.BUSI_PROD_NAME,
		       A.PRODUCT_CODE,
		       A.PAY_LIAB_CODE,
		       A.DERIV_TYPE,
		       A.BRANCH_CODE,
		       A.ORGAN_CODE,
		       A.POLICY_ORGAN_CODE,
		       A.AGENT_CODE,
		       A.POLICY_TYPE,
		       A.CHANNEL_TYPE,
		       A.PRODUCT_CHANNEL,
		       A.STATISTICAL_DATE,
		       A.VALIDATE_DATE,
		       A.POLICY_YEAR,
		       A.PREM_FREQ,
		       A.CHARGE_YEAR,
		       A.PAID_COUNT,
		       A.HOLDER_ID,
		       A.HOLDER_NAME,
		       A.INSURED_ID,
		       A.INSURED_NAME,
		       A.IS_RISK_MAIN,
		       A.FEE_TYPE,
		       A.WITHDRAW_TYPE,
		       A.REFEFLAG,
		       A.DUE_TIME,
		       A.FINISH_TIME,
		       A.PAY_END_DATE,
		       A.ARAP_FLAG,
		       A.CUSTOMER_ID,
		       A.PAYEE_NAME,
		       A.CERTI_TYPE,
		       A.CERTI_CODE,
		       A.PAYEE_PHONE,
		       A.PAYEE_EMAIL,
		       A.BANK_CODE,
		       A.BANK_ACCOUNT,
		       A.BANK_USER_NAME,
		       A.MONEY_CODE,
		       A.FEE_AMOUNT,
		       A.FEE_AMOUNT_BEFOR_TAX,
		       A.PAY_MODE,
		       A.ROLLBACK_UNIT_NUMBER,
		       A.FEE_STATUS,
		       A.BOOKKEEPING_FLAG,
		       A.POSTED,
		       A.BOOKKEEPING_ID,
		       A.BELNR,
		       A.CR_BELNR,
		       A.RED_BOOKKEEPING_ID,
		       A.SERVICE_CODE,
		       A.CLAIM_NATURE,
		       A.CLAIM_TYPE,
		       A.TAX_RATE,
		       A.AGENT_NAME,
		       A.AREA,
		       A.PART,
		       'GROUP',
		       A.IS_SEND,
		       A.SEND_DATE,
		       A.UPDATE_BY,
		       A.INSERT_TIME,
		       A.UPDATE_TIME,
		       A.INSERT_TIMESTAMP,
		       A.UPDATE_TIMESTAMP from APP___CAP__DBUSER.T_PREM_ARAP_JS A
			WHERE A.FEE_TYPE IN ('G004390600','P004390600') 
				AND A.FEE_STATUS='16'
				AND A.SERVICE_CODE = 'AG'
				 AND NOT EXISTS
		 		 (SELECT 1
		          FROM APP___CAP__DBUSER.T_PREM_ARAP_JS TA
		         WHERE TA.ROLLBACK_UNIT_NUMBER = A.UNIT_NUMBER)	
			]]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>	
	</select>
	
	
	<select id="JRQD_PA_findPremArapByXQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.FEE_STATUS,MIN(T.DUE_TIME),T.FINISH_TIME,T.BUSINESS_TYPE
				  FROM APP___PAS__DBUSER.T_PREM_ARAP T
				 WHERE 1=1
				 AND T.BUSI_PROD_CODE = '00928000'
				 AND T.ARAP_FLAG = '1'
				 AND T.FEE_STATUS = '00'
				 AND T.DERIV_TYPE = '003'
				 AND T.BUSINESS_TYPE = '4003'
			]]>	 
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>
			<![CDATA[	 
				 GROUP BY T.FEE_STATUS, T.FINISH_TIME,T.BUSINESS_TYPE
			]]>
	</select>
	
	<select id="JRQD_PA_queryTempPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			   SELECT MAX(TCM.POLICY_ID) POLICY_ID,
			          MAX(A.HOLDER_NAME) HOLDER_NAME,
				      MAX(A.POLICY_CODE) POLICY_CODE,
				      MAX(A.ORGAN_CODE) ORGAN_CODE,
				      MAX(A.FINISH_TIME) FINISH_TIME,
				      MAX(A.PAY_MODE) PAY_MODE,
				      MAX(A.UNIT_NUMBER) UNIT_NUMBER,
				      SUM(A.FEE_AMOUNT) FEE_AMOUNT,
				      MAX(A.DUE_TIME) DUE_TIME
			  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM, 
			       APP___PAS__DBUSER.T_PREM_ARAP A,
			       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
			       APP___PAS__DBUSER.T_CONTRACT_EXTEND TCE
			 WHERE TCM.POLICY_CODE = A.POLICY_CODE
			   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
			   AND TCBP.POLICY_CODE=A.POLICY_CODE
			   AND TCBP.BUSI_PROD_CODE=A.BUSI_PROD_CODE
			   AND TCBP.BUSI_ITEM_ID=TCE.BUSI_ITEM_ID
			   AND TCE.PAY_DUE_DATE = A.DUE_TIME
			   AND TCE.PAY_DUE_DATE>A.FINISH_TIME
			   AND A.FEE_STATUS = '01'
			   AND A.ARAP_FLAG = '1'
			   AND A.BUSINESS_TYPE IN ('4003', '1005') 
			   AND A.ORGAN_CODE IN (
					SELECT T.ORGAN_CODE
					  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
					 START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE = t.UPORGAN_CODE) 
			]]>	 
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
			<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
			<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND TO_CHAR(A.FINISH_TIME,'yyyy-MM-dd') = TO_CHAR(#{finish_time},'yyyy-MM-dd')]]></if>
			<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT  = #{fee_amount} ]]></if>
			<![CDATA[	 
				 GROUP BY A.POLICY_CODE
			]]>
	</select>
</mapper>


