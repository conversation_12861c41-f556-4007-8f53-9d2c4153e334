<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.pa.batch.allocation.dao.ISpecialBonusDao">	
 <!-- 批处理分页查询开始 -->
 
	<sql id="JRQD_queryForPageStart">
		<if test="start != null and counts != null ">
			<![CDATA[SELECT policy_id FROM (SELECT ROWNUM rn, policy_id FROM (]]>
		</if>
	</sql>
	<!-- 批处理分页查询结束 -->
	<sql id="JRQD_queryForPageEnd">
		<if test="start != null  and counts != null" >
			<![CDATA[ and rn >= #{start} AND rn <= #{start} + #{counts}]]>
		</if>
	</sql>
   
    <!-- 添加保单号 -->
    <sql id="JRQD_queryallocationPolicyByPolicyCode">
         <if test="policy_code != null and policy_code != '' and policy_code !='null'">
         <![CDATA[ AND tcm.policy_code = #{policy_code} ]]></if>
         <if test=" organ_code  != null  and  organ_code  != ''  "><![CDATA[AND tcm.organ_code in (
		select t.organ_code
			  from APP___PAS__DBUSER.t_udmp_org_rel t
			 start with t.organ_code = #{organ_code}
			connect by prior t.organ_code = t.uporgan_code)  ]]></if>	
    </sql>
    
    <!-- 年度分红-按启动参数查询符合条件的保单列表的最大policyId和最小policyId -->
	<select id="JRQD_queryCounts" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		    select count(DISTINCT c.busi_item_id) as endnum
      from (select b.*
              from (select a.policy_id,
                           a.policy_code,
                           tcbp.busi_item_id,
                           tcbp.busi_prd_id,
                           tcbp.busi_prod_code
                      from (select *
                              from APP___PAS__DBUSER.t_contract_master tcm
                             where 1 = 1
                               and tcm.liability_state = '1']]>
                               <include refid="JRQD_queryallocationPolicyByPolicyCode" />	
                           <![CDATA[ ) a
                      left join APP___PAS__DBUSER.t_contract_busi_prod tcbp
                        on a.policy_id = tcbp.policy_id
                     where tcbp.liability_state = 1) b
              left join APP___PAS__DBUSER.t_business_product tbp
                on tbp.business_prd_id = b.busi_prd_id
             where tbp.product_category1 = '20002') c   
      left join APP___PAS__DBUSER.t_bonus_allocate tba
        on c.busi_item_id = tba.busi_item_id
     where 1 = 1
       and tba.allocate_type = '02'
       and tba.BONUS_ALLOT = '1'
       and extract(year from tba.allocate_date) = 
                  extract(year from #{batch_date}) 
	]]>	
	</select>

	<!-- 年度分红-按启动参数查询符合条件的保单 -->
	<select id="JRQD_query" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT * FROM (SELECT e.*, rownum rn FROM (]]><![CDATA[ 
		
		select  distinct c.policy_id,c.policy_code,c.busi_item_id,c.busi_prd_id,c.busi_prod_code,
		c.policy_effective_date
      from (select b.*
              from (select a.policy_id,
                           a.policy_code,
                           a.validate_date as policy_effective_date,
                           tcbp.busi_item_id,
                           tcbp.busi_prd_id,
                           tcbp.busi_prod_code
                      from (select *
                              from APP___PAS__DBUSER.t_contract_master tcm
                             where 1 = 1
                               and tcm.liability_state = '1']]>
                               <include refid="JRQD_queryallocationPolicyByPolicyCode" />	
                           <![CDATA[ ) a
                      left join APP___PAS__DBUSER.t_contract_busi_prod tcbp
                        on a.policy_id = tcbp.policy_id
                     where tcbp.liability_state = 1) b
              left join APP___PAS__DBUSER.t_business_product tbp
                on tbp.business_prd_id = b.busi_prd_id
             where tbp.product_category1 = '20002') c   
      left join APP___PAS__DBUSER.t_bonus_allocate tba
        on c.busi_item_id = tba.busi_item_id
     where 1 = 1
       and tba.allocate_type = '02'
       and tba.BONUS_ALLOT = '1'
       and extract(year from tba.allocate_date) = 
                  extract(year from #{batch_date}) 
       order by c.policy_id ,c.busi_item_id
		 
		 ]]>
           <![CDATA[) e]]><![CDATA[
          ) g
		 WHERE 1 = 1
   		]]>
   		<if test=" end_num != null and start_num != null  "><![CDATA[ AND MOD(g.POLICY_ID , #{end_num}) = #{start_num} AND ROWNUM <= 5000 ]]></if>
	</select>
	
	<!-- 修改T_contract_prouct的Bonus_sa,sa_Bonus_date操作 -->
	<update id="JRQD_updateProductBonusSA" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
		    BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} 
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_ID=#{item_id,jdbcType=NUMERIC} ]]>
	</update>
	

    <!--根据保单id查询保单下所有险种    -->
    <select id="JRQD_findallbusiitemsbypolicyid" resultType="java.util.Map" parameterType="java.util.Map">
       <![CDATA[
           select * from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD where policy_id=#{policy_id,jdbcType=NUMERIC}  
       ]]>
    </select>
    
    <!--根据险种id查询险种下所有责任组  -->
    <select id="JRQD_findallitemsbybusiitemsid" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
             select * from  APP___PAS__DBUSER.T_CONTRACT_PRODUCT where busi_item_id=#{busi_item_id,jdbcType=NUMERIC}
          ]]>
    </select>
    

	<!-- 生成红利通知书并插入红利通知书中间表 -->    <!--暂无表待定 -->
	<insert id="JRQD_addsendBonusNotice" parameterType="java.util.Map"> 
	   <![CDATA[
	      
	   ]]>
    </insert>

    <!-- 查询最大分红日 -->
    <select id="JRQD_queryMaxAllocateDate" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
             select Max(allocate_date) as allocate_date  from APP___PAS__DBUSER.t_bonus_allocate A where 1=1
          ]]>
       <if test=" ct_allocate_date  != null  and  ct_allocate_date  != ''  "><![CDATA[ AND A.ALLOCATE_DATE <=  #{ct_allocate_date}]]></if>
      <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test=" policy_id  != null and policy_id  != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
     <if test="bonus_allot_list != null and bonus_allot_list.size()!=0">
		<![CDATA[AND A.bonus_allot IN ]]>
			<foreach collection ="bonus_allot_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
	 </if>
    </select>
	<!-- 查询最大分红日 -->
    <select id="JRQD_findLastBonusInfo" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select rownum rn, b.*
			    from (select a.*
			            from APP___PAS__DBUSER.T_BONUS_ALLOCATE a
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
     <if test=" sa_change_id  != null "><![CDATA[ AND A.SA_CHANGE_ID = #{sa_change_id} ]]></if>
     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
			          <![CDATA[
			           order by a.allocate_date desc) b
			   where rownum = 1
          ]]>
    </select>
    
    <!-- 合计红利保额 -->
    <select id="JRQD_sumBonusSaByCondition" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select nvl(sum(a.bonus_sa),'0') as sum_bonus_sa from APP___PAS__DBUSER.T_BONUS_ALLOCATE a
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test="bonus_allot_list != null and bonus_allot_list.size()!=0">
		<![CDATA[AND A.bonus_allot IN ]]>
			<foreach collection ="bonus_allot_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
	 </if>
     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
     <if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND to_date(to_char(A.ALLOCATE_DATE,'yyyy'),'yyyy') =  to_date(to_char(#{allocate_date},'yyyy'),'yyyy')]]></if>
     <if test=" sa_change_id  != null "><![CDATA[ AND A.SA_CHANGE_ID = #{sa_change_id} ]]></if>
     <if test=" sa_change_id  == null "><![CDATA[ AND A.SA_CHANGE_ID is null ]]></if>
     <if test=" end_date  != null  and  end_date  != ''  "><![CDATA[AND to_date(to_char(A.ALLOCATE_DATE,'yyyy'),'yyyy') >  to_date(to_char(#{end_date},'yyyy'),'yyyy') ]]></if>
    </select>
    
    
     <!-- 合计前一日红利保额 -->
    <select id="JRQD_calBeforeBonusByCondition" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select nvl(sum(a.bonus_sa),'0') as sum_bonus_sa from APP___PAS__DBUSER.T_BONUS_ALLOCATE a
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test="bonus_allot_list != null and bonus_allot_list.size()!=0">
		<![CDATA[AND A.bonus_allot IN ]]>
			<foreach collection ="bonus_allot_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
	 </if>
	 <if test=" policy_id  != null and policy_id  != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
     <if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND to_date(to_char(A.ALLOCATE_DATE,'yyyy'),'yyyy') <=  to_date(to_char(#{allocate_date},'yyyy'),'yyyy')]]></if>
     <if test=" ct_allocate_date  != null  and  ct_allocate_date  != ''  "><![CDATA[ AND A.ALLOCATE_DATE <=  #{ct_allocate_date}]]></if>
     <if test=" allocate_due_date  != null  and  allocate_due_date  != ''  "><![CDATA[ AND A.ALLOCATE_DUE_DATE <=  #{allocate_due_date}]]></if>
     <if test=" sa_change_id  != null "><![CDATA[ AND A.SA_CHANGE_ID = #{sa_change_id} ]]></if>
     <if test=" sa_change_id  == null "><![CDATA[ AND A.SA_CHANGE_ID is null ]]></if>
     <if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID != #{policy_chg_id} ]]></if>
     <if test=" end_date  != null  and  end_date  != ''  "><![CDATA[AND to_date(to_char(A.ALLOCATE_DATE,'yyyy'),'yyyy') >  to_date(to_char(#{end_date},'yyyy'),'yyyy') ]]></if>
    </select>
    
     <!-- 本次分红红利保额 -->
    <select id="JRQD_calNowBonusByCondition" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select nvl(sum(A.bonus_sa),0) as sum_bonus_sa from APP___PAS__DBUSER.T_BONUS_ALLOCATE A where A.ITEM_ID = #{item_id} AND A.Bonus_Doc_Date IS  NULL
			          ]]>
     
    </select>
    
    <!-- 获取本次金分红红利金额,利息金额 -->
    <select id="JRQD_getCashBonusSaByCondition" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select A.CASH_BONUS,A.INTEREST_MARGIN,A.MORTALITY_MARGIN,A.REISSUE_INTEREST from APP___PAS__DBUSER.T_BONUS_ALLOCATE A
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test="bonus_allot_list != null and bonus_allot_list.size()!=0">
		<![CDATA[AND A.bonus_allot IN ]]>
			<foreach collection ="bonus_allot_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
	 </if>
	 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	 <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
     <if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND to_date(to_char(A.ALLOCATE_DATE,'yyyy'),'yyyy') =  to_date(to_char(#{allocate_date},'yyyy'),'yyyy')]]></if>
    </select>
      <!-- 合计现金分红红利金额,利息金额 -->
    
       <select id="JRQD_sumCashBonusSaByCondition" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[
               select A.CASH_BONUS,A.INTEREST_MARGIN,A.MORTALITY_MARGIN,A.REISSUE_INTEREST from APP___PAS__DBUSER.T_BONUS_ALLOCATE A
			           where 1=1
			          ]]>
     <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
     <if test="bonus_allot_list != null and bonus_allot_list.size()!=0">
		<![CDATA[AND A.bonus_allot IN ]]>
			<foreach collection ="bonus_allot_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
	 </if>
	 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	 <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
     <if test=" bonus_allot != null and bonus_allot != ''  "><![CDATA[ AND A.BONUS_ALLOT = #{bonus_allot} ]]></if>
     <if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND to_date(to_char(A.ALLOCATE_DATE,'yyyy'),'yyyy') <=  to_date(to_char(#{allocate_date},'yyyy'),'yyyy')]]></if>
    </select>
    
    <!-- 查询该责任组上次红利通知书发放时间 -->
      <select id="JRQD_getbonusDocDate" resultType="java.util.Map" parameterType="java.util.Map">
       <![CDATA[
		       select Bonus_Doc_Date
		  from (SELECT A.Bonus_Doc_Date as Bonus_Doc_Date
		          FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A
		         where A.ITEM_ID = #{item_id}
		           AND A.Bonus_Doc_Date IS NOT NULL
		         order by A.Bonus_Doc_Date desc)
		 where rownum = 1
       ]]>
    </select>
    
    <!-- 修改操作 -->
	<update id="JRQD_updateBonusAllocateNotice" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BONUS_ALLOCATE ]]>
		<set>
		<trim suffixOverrides=",">
		    Bonus_Doc_Date = #{bonus_doc_date, jdbcType=DATE} ,
		    BONUS_CV = #{bonus_cv, jdbcType=NUMERIC} ,
		    ORIGIN_BONUS_CV = #{origin_bonus_cv, jdbcType=NUMERIC} ,
		    TOTAL_BONUS_CV = #{total_bonus_cv, jdbcType=NUMERIC} ,
		    TERMINAL_BONUS = #{terminal_bonus, jdbcType=NUMERIC} ,
		    TERMINAL_BONUS_RATE = #{terminal_bonus_rate, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE  ALLOCATE_ID = #{allocate_id}  ]]>
	</update>
	
	
	    <!--根据保单id查询保单下所有险种    -->
    <select id="JRQD_getUpdateBonusDocDate" resultType="java.util.Map" parameterType="java.util.Map">
       <![CDATA[
           select ALLOCATE_ID,Bonus_Doc_Date,BONUS_CV,ORIGIN_BONUS_CV,TOTAL_BONUS_CV,TERMINAL_BONUS,TERMINAL_BONUS_RATE 
           from APP___PAS__DBUSER.T_BONUS_ALLOCATE A where A.ITEM_ID = #{item_id} AND A.Bonus_Doc_Date IS NUll
       ]]>
       <if test=" allocate_date  != null  and  allocate_date  != ''  "><![CDATA[ AND to_char(A.allocate_due_date,'yyyy') =  to_char(#{allocate_date},'yyyy')]]></if>
    </select>

</mapper>