<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="fundTrans">
	
	<sql id="JRQD_PA_fundTransWhereCondition">
		<if test=" trans_interest  != null "><![CDATA[ AND A.TRANS_INTEREST = #{trans_interest} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" trans_units  != null "><![CDATA[ AND A.TRANS_UNITS = #{trans_units} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" fund_code != null and fund_code != ''  "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" trans_type  != null "><![CDATA[ AND A.TRANS_TYPE = #{trans_type} ]]></if>
		<if test=" trans_amount  != null "><![CDATA[ AND A.TRANS_AMOUNT = #{trans_amount} ]]></if>
		<if test=" settlement_id  != null "><![CDATA[ AND A.SETTLEMENT_ID = #{settlement_id} ]]></if>
		<if test=" trans_id  != null "><![CDATA[ AND A.TRANS_ID = #{trans_id} ]]></if>
		<if test=" fund_sell_price  != null "><![CDATA[ AND A.FUND_SELL_PRICE = #{fund_sell_price} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" apply_trans_code != null and apply_trans_code != ''  "><![CDATA[ AND A.APPLY_TRANS_CODE = #{apply_trans_code} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" balance_units_bf  != null "><![CDATA[ AND A.BALANCE_UNITS_BF = #{balance_units_bf} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" fund_purc_price  != null "><![CDATA[ AND A.FUND_PURC_PRICE = #{fund_purc_price} ]]></if>
		<if test=" trans_proportion  != null "><![CDATA[ AND A.TRANS_PROPORTION = #{trans_proportion} ]]></if>
		<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[ AND A.DEAL_TIME = #{deal_time} ]]></if>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" trans_price  != null "><![CDATA[ AND A.TRANS_PRICE = #{trans_price} ]]></if>
		
		<!-- guyy_wb start -->
		<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.trans_code IN ]]>
			<foreach collection ="trans_code_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test=" trans_type_list  != null and trans_type_list.size()!=0">
			<![CDATA[ AND A.trans_type IN ]]>
			<foreach collection ="trans_type_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test=" not_trans_code_list  != null and not_trans_code_list.size()!=0">
			<![CDATA[ AND A.trans_code not IN ]]>
			<foreach collection ="not_trans_code_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test=" now_date != null and now_date != '' "><![CDATA[ AND A.DEAL_TIME = #{now_date} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.DEAL_TIME >= #{start_date} ]]></if>
		<!-- guyy_wb end -->
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.DEAL_TIME <= #{end_date} ]]></if>
		<if test=" chg_list  != null and chg_list.size()!=0">
			<![CDATA[ AND A.policy_chg_id IN ]]>
			<foreach collection ="chg_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test="trans_Code_List != null and trans_Code_List.size()!=0"><![CDATA[ AND A.FUND_CODE  IN ]]>
			<foreach collection="trans_Code_List" item="trans_Code_List"
				index="index" open="(" close=")" separator=",">
				#{trans_Code_List}
			</foreach>
		</if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_queryFundTransByTransIdCondition">
		<if test=" trans_id  != null "><![CDATA[ AND A.TRANS_ID = #{trans_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryFundTransByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryFundTransByFundCodeCondition">
		<if test=" fund_code != null and fund_code != '' "><![CDATA[ AND A.FUND_CODE = #{fund_code} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryFundTransByApplyIdCondition">
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
	</sql>	

	<sql id="JRQD_PA_queryFundTransByDealTimeCondition">
		<if test=" deal_time  != null "><![CDATA[ AND A.DEAL_TIME = #{deal_time} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryFundTransByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

	<sql id="JRQD_PA_queryFundTransByStreamInvestIdCondition">
		<if test=" stream_invest_id  != null "><![CDATA[ AND A.STREAM_INVEST_ID = #{stream_invest_id} ]]></if>
	</sql>	

	<sql id="JRQD_PA_fundTransDateWhereCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[ AND A.DEAL_TIME >=#{deal_time} ]]></if>
	</sql>
<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_findFundTransHisCondition">
		<if test=" trans_code != null and trans_code != '' "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" policy_id != null and policy_id != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	
	
	<sql id="JRQD_queryInvestAccountHistory">
		<if test=" policy_id != null and policy_id != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" list_id != null and list_id != '' "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
	
	<sql id="JRQD_queryInvestAccountHistoryByStartDateAndEndDate">      		
		<if test=" startDate  != null  and  startDate  != ''  "><![CDATA[ AND TFT.DEAL_TIME >=#{startDate} ]]></if>
		<if test=" endDate  != null  and  endDate  != ''  "><![CDATA[ AND TFT.DEAL_TIME <=#{endDate} ]]></if>
	</sql>		

<!-- 添加操作 -->
	<insert id="JRQD_PA_addFundTrans"  useGeneratedKeys="true" parameterType="java.util.Map"> 
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="trans_id">
			SELECT APP___PAS__DBUSER.S_FUND_TRANS__TRANS_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_TRANS(
				TRANS_INTEREST, MONEY_CODE, PRODUCT_ID, TRANS_UNITS, UNIT_NUMBER, FUND_CODE, ITEM_ID, 
				TRANS_TYPE, INSERT_TIMESTAMP, TRANS_AMOUNT, UPDATE_BY, SETTLEMENT_ID, TRANS_ID, FUND_SELL_PRICE, 
				LIST_ID, APPLY_TRANS_CODE, POLICY_CHG_ID, BALANCE_UNITS_BF, BUSI_ITEM_ID, POLICY_ID, APPLY_ID, 
				FUND_PURC_PRICE, INSERT_TIME, UPDATE_TIME, TRANS_PROPORTION, DEAL_TIME, TRANS_CODE, TRANS_PRICE, 
				UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{trans_interest, jdbcType=NUMERIC}, #{money_code, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{trans_units, jdbcType=NUMERIC} , #{unit_number, jdbcType=VARCHAR} , #{fund_code, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} 
				, #{trans_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{trans_amount, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{settlement_id, jdbcType=NUMERIC} , #{trans_id, jdbcType=NUMERIC} , #{fund_sell_price, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , #{apply_trans_code, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{balance_units_bf, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{apply_id, jdbcType=NUMERIC} 
				, #{fund_purc_price, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{trans_proportion, jdbcType=NUMERIC} , #{deal_time, jdbcType=DATE} , #{trans_code, jdbcType=VARCHAR} , #{trans_price, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteFundTrans" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE TRANS_ID = #{trans_id} ]]>
	</delete>
	<!-- 保全回退删除操作 -->	
	<delete id="JRQD_PA_deleteFundTransForRB" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID = #{policy_id} AND DEAL_TIME >=#{deal_time}  ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_PA_updateFundTrans" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_TRANS ]]>
		<set>
		<trim suffixOverrides=",">
		    TRANS_INTEREST = #{trans_interest, jdbcType=NUMERIC} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    TRANS_UNITS = #{trans_units, jdbcType=NUMERIC} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
			FUND_CODE = #{fund_code, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    TRANS_TYPE = #{trans_type, jdbcType=NUMERIC} ,
		    TRANS_AMOUNT = #{trans_amount, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SETTLEMENT_ID = #{settlement_id, jdbcType=NUMERIC} ,
		    FUND_SELL_PRICE = #{fund_sell_price, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
			APPLY_TRANS_CODE = #{apply_trans_code, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BALANCE_UNITS_BF = #{balance_units_bf, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    APPLY_ID = #{apply_id, jdbcType=NUMERIC} ,
		    FUND_PURC_PRICE = #{fund_purc_price, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    TRANS_PROPORTION = #{trans_proportion, jdbcType=NUMERIC} ,
		    DEAL_TIME = #{deal_time, jdbcType=DATE} ,
			TRANS_CODE = #{trans_code, jdbcType=VARCHAR} ,
		    TRANS_PRICE = #{trans_price, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE TRANS_ID = #{trans_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findFundTransByTransId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1   ]]>
		<include refid="JRQD_PA_queryFundTransByTransIdCondition" />
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>
	
	<select id="JRQD_PA_findFundTransByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1   ]]>
		<include refid="JRQD_PA_queryFundTransByItemIdCondition" />
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>
	
	<select id="JRQD_PA_findFundTransByFundCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1    ]]>
		<include refid="JRQD_PA_queryFundTransByFundCodeCondition" />
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>
	
	<select id="JRQD_PA_findFundTransByApplyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1   ]]>
		<include refid="JRQD_PA_queryFundTransByApplyIdCondition" />
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>
	
	
	<select id="JRQD_PA_findFundTransByDealTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryFundTransByDealTimeCondition" />
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>
	
	<select id="JRQD_PA_findFundTransByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1    ]]>
		<include refid="JRQD_PA_queryFundTransByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>
	
	
	<select id="JRQD_PA_findFundTransByStreamInvestId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1    ]]>
		<include refid="JRQD_PA_queryFundTransByStreamInvestIdCondition" />
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapFundTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1000   ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllFundTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1000   ]]>
		<include refid="JRQD_PA_fundTransWhereCondition" />
		<if test=" transCodeList != null and transCodeList.size() != 0">
			<![CDATA[ AND A.TRANS_CODE in ]]>
			<foreach collection ="transCodeList" item="transCodeList" index="index" open="(" close=")" separator=",">#{transCodeList}</foreach>
		</if>
		<![CDATA[ ORDER BY A.DEAL_TIME ]]> 
	</select>
<!-- 查询个数操作 -->
	<select id="JRQD_PA_findFundTransTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_fundTransWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryFundTransForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.RN AS rowNumber, B.TRANS_INTEREST, B.MONEY_CODE, B.PRODUCT_ID, B.TRANS_UNITS, B.UNIT_NUMBER, B.FUND_CODE, B.ITEM_ID, 
			B.TRANS_TYPE, B.TRANS_AMOUNT, B.SETTLEMENT_ID, B.TRANS_ID, B.FUND_SELL_PRICE, 
			B.LIST_ID, B.APPLY_TRANS_CODE, B.POLICY_CHG_ID, B.BALANCE_UNITS_BF, B.BUSI_ITEM_ID, B.POLICY_ID, B.APPLY_ID, 
			B.FUND_PURC_PRICE, B.TRANS_PROPORTION, B.DEAL_TIME, B.TRANS_CODE, B.TRANS_PRICE FROM (
					SELECT ROWNUM RN, A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TRANS_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 追加保费查询（至尊双利）  -->
	<select id="JRQD_queryFundTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select a.TRANS_AMOUNT,a.DEAL_TIME from APP___PAS__DBUSER.T_FUND_TRANS a where exists (select 1 from  APP___PAS__DBUSER.T_CONTRACT_MASTER b
            where a.policy_id = b.policy_id  and b.policy_code=#{policy_code})
		]]>
	</select>
	
	<!-- 根据交易类型码和保单id查询相关费用累计金额 -->
	<select id="JRQD_PA_sumAmountByTypeAndPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select nvl(sum(TRANS_AMOUNT),0) as trans_amount 
			from APP___PAS__DBUSER.T_FUND_TRANS where POLICY_ID=#{policy_id} 
		]]>
		<if test=" list_id  != null ">
			<![CDATA[ and list_id=#{list_id} ]]>
		</if>
		<if test=" trans_code  != null ">
			<![CDATA[ and TRANS_CODE IN (${trans_code}) ]]>
		</if>
		<if test=" deal_time != null ">
			<![CDATA[ and DEAL_TIME > #{deal_time} ]]>
		</if>
		<if test=" deal_timea != null">
			<![CDATA[  and DEAL_TIME <= #{deal_timea}]]>
		</if>
	</select>
	
	<!-- 根据保单id查询最早交易记录的数据 -->
	<select id="JRQD_PA_findEarliestDealByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM (SELECT * FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE A.POLICY_ID=#{policy_id} 
		]]>
		<include refid="JRQD_PA_fundTransWhereCondition" />
		<![CDATA[
			ORDER BY A.DEAL_TIME) WHERE ROWNUM=1
		]]>
	</select>
	<!-- 根据保单id查询最晚交易记录的数据 -->
	<select id="JRQD_PA_findLatestDealByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM (SELECT * FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE POLICY_ID=#{policy_id} 
		]]>
		<include refid="JRQD_PA_fundTransWhereCondition" />
		<![CDATA[
			ORDER BY DEAL_TIME DESC) WHERE ROWNUM=1
		]]>
	</select>
	
	<select id="JRQD_findFundTransHis" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select 
		            A.TRANS_UNITS,
		            A.TRANS_PRICE,
		            A.TRANS_AMOUNT,
		            A.FUND_PURC_PRICE,
		            A.FUND_SELL_PRICE
		        FROM
		            APP___PAS__DBUSER.T_FUND_TRANS A  
		        where rownum = 1]]>
				<include refid="JRQD_findFundTransHisCondition" />
		<![CDATA[ ORDER BY A.DEAL_TIME ASC ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllFundTransDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1000  ]]>
		 <if test=" ct_apply_time != null and ct_apply_time != ''  "><![CDATA[ AND A.deal_time > #{ct_apply_time} ]]></if>
			<include refid="JRQD_PA_fundTransWhereCondition" />
		<![CDATA[ ORDER BY A.DEAL_TIME ]]>  
	</select>
	
	<!-- 投连价格查询 -->
	<select id="JRQD_PA_findFundTransByTransType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM = 1  and TRANS_TYPE=#{trans_type} ]]>
		<![CDATA[ ORDER BY A.TRANS_ID ]]>
	</select>
	
	
		<!-- 外围投连账户结算历史信息查询 -->
	<select id="JRQD_queryInvestAccountHistory" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   SELECT OLD_POL_NO,BUSI_ITEM_ID,BUSI_PROD_CODE,PRODUCT_NAME_SYS,TRANS_CODE,CODE_NAME,FEE_CODE,FEE_NAME,TRANS_AMOUNT,TRANS_UNITS,
           FUND_PROCESS_STATUS,DEAL_TIME,VALUEDATE,SERVICE_CODE,SERVICE_NAME,LIST_ID,ACCOUNT_CODE,SUMMONEY,SUMCOUNT,FUND_PURC_PRICE,
           FUND_SELL_PRICE,INTEREST_CAPITAL,ACCUM_UNITS FROM  
		   (SELECT ROWNUM RN,OLD_POL_NO,BUSI_ITEM_ID,BUSI_PROD_CODE,PRODUCT_NAME_SYS,TRANS_CODE,CODE_NAME,FEE_CODE,FEE_NAME,TRANS_AMOUNT,TRANS_UNITS,
           FUND_PROCESS_STATUS,DEAL_TIME,VALUEDATE,SERVICE_CODE,SERVICE_NAME,LIST_ID,ACCOUNT_CODE,SUMMONEY,SUMCOUNT,FUND_PURC_PRICE,
           FUND_SELL_PRICE,INTEREST_CAPITAL,ACCUM_UNITS FROM
		   (SELECT  ROWNUM RN, (SELECT NVL(OLD_POL_NO,BUSI_ITEM_ID) FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID=TFT.BUSI_ITEM_ID)AS OLD_POL_NO,TFT.BUSI_ITEM_ID,
		   (SELECT TBP.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCB WHERE  TBP.BUSINESS_PRD_ID=TCB.BUSI_PRD_ID AND TCB.BUSI_ITEM_ID=TFT.BUSI_ITEM_ID)BUSI_PROD_CODE,
		   (SELECT TBP.PRODUCT_NAME_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCB WHERE  TBP.BUSINESS_PRD_ID=TCB.BUSI_PRD_ID AND TCB.BUSI_ITEM_ID=TFT.BUSI_ITEM_ID)PRODUCT_NAME_SYS,
		   TFT.TRANS_CODE,
		   (SELECT T.DESCRIPTION FROM APP___PAS__DBUSER.T_TRANSACTION_CODE T WHERE T.TRANS_CODE=TFT.TRANS_CODE)CODE_NAME,
		   CASE  WHEN    TFT.TRANS_CODE IN('02','03','04','41','35','36','37','05','30','31')THEN 'GL' ELSE '' END AS FEE_CODE,
           CASE WHEN (CASE  WHEN    TFT.TRANS_CODE IN('02','03','04','41')THEN 'GL' ELSE NULL END)='GL' THEN '管理费' ELSE '' END AS FEE_NAME,
		   TFT.TRANS_AMOUNT,
		   TFT.TRANS_UNITS,
		   CASE (SELECT TTA.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY TTA WHERE TTA.BUSI_ITEM_ID=TFT.BUSI_ITEM_ID AND TTA.TRANS_CODE=TFT.TRANS_CODE AND TTA.FUND_CODE=TFT.FUND_CODE )WHEN '1' THEN '计价完成' ELSE '未计价' END FUND_PROCESS_STATUS,
		   TFT.DEAL_TIME,
		   (SELECT MIN(TIUP.PRICING_DATE) FROM  APP___PAS__DBUSER.T_INVEST_UNIT_PRICE TIUP WHERE TIUP.INVEST_ACCOUNT_CODE=TFT.FUND_CODE AND TIUP.PRICING_DATE>=TFT.DEAL_TIME )VALUEDATE,
		   (SELECT TCAC.SERVICE_CODE FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY TFTA,APP___PAS__DBUSER.T_SERVICE TS,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC WHERE TFTA.FUND_CODE=TFT.FUND_CODE AND TFTA.BUSI_ITEM_ID=TFT.BUSI_ITEM_ID AND TFTA.TRANS_CODE=TFT.TRANS_CODE
		   AND  TCAC.ACCEPT_CODE=TFTA.ACCEPT_CODE AND TS.SERVICE_CODE=TCAC.SERVICE_CODE )SERVICE_CODE,
		   (SELECT TS.SERVICE_NAME FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY TFTA,APP___PAS__DBUSER.T_SERVICE TS,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC WHERE TFTA.FUND_CODE=TFT.FUND_CODE AND TFTA.BUSI_ITEM_ID=TFT.BUSI_ITEM_ID AND TFTA.TRANS_CODE=TFT.TRANS_CODE
		   AND  TCAC.ACCEPT_CODE=TFTA.ACCEPT_CODE AND TS.SERVICE_CODE=TCAC.SERVICE_CODE )SERVICE_NAME,
		   TFT.LIST_ID,
		   TFT.FUND_CODE AS ACCOUNT_CODE,
		   (SELECT SUM(TRANS_AMOUNT) FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE DEAL_TIME<=TFT.DEAL_TIME AND  POLICY_ID=TFT.POLICY_ID AND TRANS_CODE IN 
           ('11','27','29','12','28'))-(SELECT SUM(TRANS_AMOUNT) FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE DEAL_TIME<=TFT.DEAL_TIME AND  POLICY_ID=TFT.POLICY_ID AND TRANS_CODE IN 
           ('02','03','04','41') ) SUMMONEY,
		   (SELECT SUM(TRANS_UNITS) FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE DEAL_TIME<=TFT.DEAL_TIME AND  POLICY_ID=TFT.POLICY_ID)SUMCOUNT,
		   (SELECT T2.CAL_BID_PRICE FROM  APP___PAS__DBUSER.T_INVEST_UNIT_PRICE T2 WHERE T2.INVEST_ACCOUNT_CODE=TFT.FUND_CODE AND T2.PRICING_DATE=TFT.DEAL_TIME AND ROWNUM=1)FUND_PURC_PRICE,
		   (SELECT T2.CAL_OFF_PRICE FROM  APP___PAS__DBUSER.T_INVEST_UNIT_PRICE T2 WHERE T2.INVEST_ACCOUNT_CODE=TFT.FUND_CODE AND T2.PRICING_DATE=TFT.DEAL_TIME AND ROWNUM=1)FUND_SELL_PRICE,
		   (SELECT T2.CAL_BID_PRICE FROM  APP___PAS__DBUSER.T_INVEST_UNIT_PRICE T2 WHERE T2.INVEST_ACCOUNT_CODE=TFT.FUND_CODE AND T2.PRICING_DATE=TFT.DEAL_TIME AND ROWNUM=1)* ((SELECT TCI.ACCUM_UNITS FROM APP___PAS__DBUSER.T_CONTRACT_INVEST TCI 
           WHERE TCI.ACCOUNT_CODE=TFT.FUND_CODE AND TCI.BUSI_ITEM_ID=TFT.BUSI_ITEM_ID AND TCI.POLICY_ID=TFT.POLICY_ID)-NVL((SELECT SUM(TRANS_UNITS) FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE FUND_CODE=TFT.FUND_CODE AND POLICY_ID=TFT.POLICY_ID 
           AND DEAL_TIME >TFT.DEAL_TIME]]>
           <if test=" endDate  != null  and  endDate  != ''  "><![CDATA[ AND DEAL_TIME<=#{endDate}  ]]></if>
           <![CDATA[),0) )INTEREST_CAPITAL,
		   (SELECT TCI.ACCUM_UNITS FROM APP___PAS__DBUSER.T_CONTRACT_INVEST TCI 
           WHERE TCI.ACCOUNT_CODE=TFT.FUND_CODE AND TCI.BUSI_ITEM_ID=TFT.BUSI_ITEM_ID AND TCI.POLICY_ID=TFT.POLICY_ID)-NVL((SELECT SUM(TRANS_UNITS) FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE FUND_CODE=TFT.FUND_CODE AND POLICY_ID=TFT.POLICY_ID	
           AND DEAL_TIME >TFT.DEAL_TIME]]>
           <if test=" endDate  != null  and  endDate  != ''  "><![CDATA[ AND DEAL_TIME<=#{endDate} ]]></if>
           <![CDATA[),0) ACCUM_UNITS
		   FROM APP___PAS__DBUSER.T_FUND_TRANS TFT,APP___PAS__DBUSER.T_CONTRACT_MASTER TCM   WHERE TFT.POLICY_ID=TCM.POLICY_ID AND TCM.POLICY_CODE=#{policy_code} AND TFT.FUND_CODE=#{account_code}
		]]>		
		<include refid="JRQD_queryInvestAccountHistoryByStartDateAndEndDate" />
		<if test=" orderFlag  != null  and  orderFlag  != ''"><![CDATA[${orderFlag}]]></if>	
		<![CDATA[ ) ]]>
		<if test=" pageRowNum  != null  and  pageRowNum  != ''  and rowNumStart  != null  and  rowNumStart  != ''"><![CDATA[WHERE  ROWNUM<=#{pageRowNum})]]>
		<![CDATA[WHERE RN>=#{rowNumStart}]]></if>
	</select>
	
	<select id="JRQD_queryInvestAccountHistoryCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		  SELECT COUNT(1)
		   FROM APP___PAS__DBUSER.T_FUND_TRANS TFT,APP___PAS__DBUSER.T_CONTRACT_MASTER TCM   WHERE TFT.POLICY_ID=TCM.POLICY_ID AND TCM.POLICY_CODE=#{policy_code} AND TFT.FUND_CODE=#{account_code}
		]]>		
		<include refid="JRQD_queryInvestAccountHistoryByStartDateAndEndDate" />
	</select>
	
	<!--by zhaoyoan_wb 通过保单id和险种id查询基金(账户)名称 -->
	<select id="JRQD_PA_findFundNameByPolicyIdAndBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT (SELECT FUND_NAME FROM APP___PDS__DBUSER.T_FUND WHERE FUND_CODE=A.FUND_CODE) AS fund_name,
			(SELECT INTEREST_START_DATE FROM APP___PDS__DBUSER.T_UNIVERSAL_SETTLEMENT_RATE 
        WHERE BUSINESS_PRD_ID=(SELECT BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID) 
          AND INTEREST_RATE_TYPE='1' AND ROWNUM=1 and INTEREST_START_DATE <= b.settle_date and interest_end_date >= b.settle_date ) AS interest_start_date, 
			(SELECT INTEREST_END_DATE FROM APP___PDS__DBUSER.T_UNIVERSAL_SETTLEMENT_RATE 
        WHERE BUSINESS_PRD_ID=(SELECT BUSI_PRD_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID=A.BUSI_ITEM_ID) 
          AND INTEREST_RATE_TYPE='1' AND ROWNUM=1 and INTEREST_START_DATE <= b.settle_date and interest_end_date >= b.settle_date ) AS interest_end_date, 
			(SELECT TRANS_AMOUNT FROM APP___PAS__DBUSER.T_FUND_TRANS T WHERE A.BUSI_ITEM_ID=T.BUSI_ITEM_ID AND T.TRANS_CODE='03' AND ROWNUM=1 AND 
			        NOT EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND TRANS_CODE='03' AND T.DEAL_TIME>DEAL_TIME)) AS trans_amount
			FROM APP___PAS__DBUSER.T_FUND_TRANS A ,  APP___PAS__DBUSER.t_Fund_Settlement B
			WHERE A.list_id = B.invest_id 
			AND b.settle_date = (select max(c.settle_date)  from  dev_pas.t_fund_settlement c where c.invest_id = a.list_id  )
			AND A.POLICY_ID=#{policy_id} 
			AND A.BUSI_ITEM_ID=#{busi_item_id} 
			AND ROWNUM=1
		]]>
	</select>
	
	<!-- 查询部分领取所在保单年度的领取次数 add by zhouly_wb -->
	<select id="JRQD_PA_findTotalPartTrans" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1 ]]>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" start_date  != null  and  start_date  != '' and end_date != null and end_date != '' ">
		<![CDATA[ AND A.DEAL_TIME >= #{start_date} AND A.DEAL_TIME <= #{end_date} ]]></if>
	</select>
	
	<!-- 查询自上个结算日至申请提交日发生过的部分领取的交易记录  -->
	<select id="JRQD_PA_findFundTransPG" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE, A.UPDATE_TIME FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1000]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[ AND A.DEAL_TIME > #{deal_time} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.DEAL_TIME <= #{end_time} ]]></if>
	</select>	
	<!-- 查询自上个结算日至申请提交日发生过的部分领取的交易记录  （投连险）-->
	<select id="JRQD_PA_findTransApplyPG" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.TRANS_CODE,A.APPLY_AMOUNT,A.APPLY_UNITS,A.APPLY_TIME FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY A WHERE ROWNUM <=  1000 AND TRANS_CODE = 22]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" apply_time  != null  and  apply_time  != ''  "><![CDATA[ AND A.APPLY_TIME > #{apply_time} ]]></if>
	</select>
	
	<!-- 万能险账户价值结算查询  接口用 -->
	<select id="JRQD_queryFundTransByTransCode" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[ 
	select *
	  from (select j.*, rownum as rn
	          from (select i.flag,
	                       i.gettype,
	                       i.getmoney,
	                       i.BalanceDate,
	                       i.balancerate,
	                       i.inceptcontvalue,
	                       i.currentcontvalue,
	                       i.changedate,
	                       i.fund_code,
	                       i.adjustamount
	                  from (
	]]>
		<if test=" isQueryFundSettlement == true">
		
		<![CDATA[ 
			select 'fund_settlement' as flag,
      		 '' as GetType,
		       a.interest         as GetMoney,
		       a.Settle_Date      as BalanceDate,
		       a.Interest_Rate    as BalanceRate,
		       a.Last_Balance     as InceptContValue,
		       a.BALANCE          as CurrentContValue,
		       a.Settle_Date      as ChangeDate,
		       '' as fund_code, 
		       a.adjust_amount as AdjustAmount,
		       a.ADJUST_TYPE as AdjustType
			  from APP___PAS__DBUSER.T_FUND_SETTLEMENT a,
			       APP___PAS__DBUSER.T_CONTRACT_INVEST b
			 where a.account_code = b.account_code
			   and a.invest_id = b.list_id
			   and b.policy_id = #{policy_id}
			   
		]]>
			<if test=" isQueryFundSettlement == true">
				<if test=" balance_start_date!=null "><![CDATA[and a.SETTLE_DATE>=  #{balance_start_date}]]></if>
				<if test=" balance_end_date!=null "><![CDATA[and a.SETTLE_DATE<=  #{balance_end_date}]]></if>
			</if>
		
		</if>
		
		<if test=" isQueryFundSettlement == true and  isQueryFundTrans == true"> union </if>
		
		<if test=" isQueryFundTrans == true">
			select 'fund_trans' as flag,
	          a.trans_code as GetType,
	          a.trans_amount as GetMoney,
	          a.DEAL_TIME as BalanceDate,
	          0 as BalanceRate,
	          0 as InceptContValue,
	          (case a.Trans_Type
		             when 0 then a.Balance_Units_Bf
		             when 1 then a.Balance_Units_Bf-a.TRANS_AMOUNT
		             when 2 then a.Balance_Units_Bf+a.TRANS_AMOUNT
		             when 3 then a.BALANCE_UNITS_BF-a.Trans_Amount
		         end) CurrentContValue, 
	          a.insert_time as ChangeDate,
	          a.fund_code as fund_code,
	          null as AdjustAmount,
	          null as AdjustType
	     from APP___PAS__DBUSER.t_fund_trans      a,
	          APP___PAS__DBUSER.t_contract_master b
	    where a.policy_id = b.policy_id
	      and a.list_id = #{list_id}
	      and b.policy_code = #{policy_code}
	      and a.trans_code in 
		</if>
		<if test=" isQueryFundTrans == true "> 
			<foreach collection ="transCodeList" item="transCodeList" index="index" open="(" close=")" separator=",">#{transCodeList}</foreach>
			<if test=" balance_start_date!=null "><![CDATA[and a.DEAL_TIME>=  #{balance_start_date}]]></if>
			<if test=" balance_end_date!=null "><![CDATA[and a.DEAL_TIME<=  #{balance_end_date}]]></if>
		</if>
	
	<![CDATA[ 
	   ) i
                 order by i.BalanceDate desc) j
         where rownum <= #{endRn}) k
 where k.rn >= #{startRn}
	]]>
		
	</select>

	<!-- 投连保险账户信息查询实现 -->
	<select id="JRQD_findAllFundTransList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.TRANS_UNITS, A.TRANS_AMOUNT, A.DEAL_TIME FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1=1   ]]>
		<include refid="JRQD_PA_fundTransWhereCondition" />
		<if test=" check_time  != null  and  check_time  != ''  ">
		<![CDATA[ AND A.DEAL_TIME <= #{check_time} ]]></if> 
		<if test=" tOrder2 != null and tOrder2 != ''">
			${tOrder2}
		</if>	
		
	</select>

	
	<!-- 万能保单最新月度结算信息  -->
	<select id="JRQD_PA_findLastTime" resultType="java.util.Map" parameterType="java.util.Map" >
	
		<![CDATA[
			select  max(deal_time)  from APP___PAS__DBUSER.T_FUND_TRANS a where  1=1 
			 and a.deal_time< 
		]]>
		<if test="list_id != null ">
			<![CDATA[ and a.list_id=#{list_id} ]]>
		</if>
		<if test="deal_time !=null ">
			<![CDATA[ and a.deal_time < #{deal_time} ]]>
		</if>
	</select>
	
	<!--by zhaoyoan_wb 查询投连投资账户单位价格信息(单位卖出价、最近计价日、账户单位数) -->
	<select id="JRQD_PA_queryInvestUnitPriceInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 
			(SELECT FUND_SELL_PRICE FROM DEV_PAS.T_FUND_TRANS WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND A.FUND_CODE=FUND_CODE AND DEAL_TIME=
				(SELECT MAX(DEAL_TIME) FROM DEV_PAS.T_FUND_TRANS WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID 
					AND A.FUND_CODE=FUND_CODE 	) AND ROWNUM=1) AS lastest_fund_sell_price,
			(SELECT MAX(DEAL_TIME) FROM DEV_PAS.T_FUND_TRANS 
				WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND A.FUND_CODE=FUND_CODE 	) AS lastest_deal_time,
			(SELECT SUM(NVL(TRANS_UNITS,0)) FROM DEV_PAS.T_FUND_TRANS 
				WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND A.FUND_CODE=FUND_CODE) AS sum_trans_units
			FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1=1 
		]]>
		<include refid="JRQD_PA_fundTransWhereCondition" />
		<![CDATA[ AND ROWNUM=1 ]]>
	</select>
	<sql id="JRQD_PA_queryFundTransByApplyDateCondition">
		<if test=" apply_date != null "><![CDATA[ AND A.DEAL_TIME < #{apply_date}  ]]></if>
	</sql>
	
	<!-- 查询总金额 -->
	<select id="JRQD_PA_findSumAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(DECODE(A.TRANS_TYPE, 3, A.TRANS_AMOUNT, 2, -A.TRANS_AMOUNT, 0)) sum_amount FROM APP___PAS__DBUSER.t_fund_trans a  
				WHERE  A.LIST_ID = #{list_id}
		]]>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" transCodeList != null and transCodeList.size() != 0">
			<![CDATA[ AND A.TRANS_CODE in ]]>
			<foreach collection ="transCodeList" item="transCodeList" index="index" open="(" close=")" separator=",">#{transCodeList}</foreach>
		</if>
	</select>
	
	<!--by zhaoyoan_wb 查询某时间点之前或之后的第一笔记录 -->
	<select id="JRQD_PA_queryFirstFundTransByDealTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM(
			SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
						A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
						A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
						A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE
			FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1 = 1
		]]>
		<include refid="JRQD_PA_fundTransWhereCondition" />
		<![CDATA[ ORDER BY A.DEAL_TIME ]]>
		<if test=" order_desc != null and order_desc != ''  "><![CDATA[ DESC ]]></if>
		<![CDATA[ ) WHERE ROWNUM=1 ]]>
	</select>
	
	<!-- 查询总金额 -->
	<select id="JRQD_PA_findFundTransFeeDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			      select max(t.deal_time) as deal_time
               	  from app___pas__dbuser.t_fund_trans t
                	 where t.list_id = #{list_id}
                	   and t.deal_time < #{end_date}
		]]>
<!-- 		('02', '03', '04', '41') -->
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND t.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.trans_code IN ]]>
			<foreach collection ="trans_code_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test=" policy_id  != null "><![CDATA[ AND t.POLICY_ID = #{policy_id} ]]></if>
	</select>
	
	<!-- 保全试算接口_账户退保 中查询最近的一笔管理费 -->
	<select id="JRQD_findLastFundTransPO" resultType="java.util.Map" parameterType="java.util.Map">
	
		<![CDATA[ 
			select *
			  from (select a.trans_amount
			          from APP___PAS__DBUSER.t_fund_trans a
			         where a.list_id =  #{list_id}
			           and a.trans_code = '04'
			         order by a.deal_time desc)
			 where rownum = '1'
		]]>
	</select>
	
	<!-- 保单账户查询最早或最晚一笔交易 -->
	<select id="JRQD_PA_findLastOrFirstFundTransPO" resultType="java.util.Map" parameterType="java.util.Map">
	    <if test=" flag1 != null  and flag1 != '' "><![CDATA[ 
	         SELECT MAX(T.DEAL_TIME)AS DEAL_TIME  FROM DEV_PAS.T_FUND_TRANS T WHERE T.FUND_CODE=#{account_code} AND T.POLICY_ID=(SELECT POLICY_ID FROM DEV_PAS.T_CONTRACT_MASTER WHERE POLICY_CODE=#{policy_code})
         ]]></if>
		<if test=" flag2 != null  and flag2 != '' "><![CDATA[ 
		     SELECT MIN(T.DEAL_TIME)AS DEAL_TIME  FROM DEV_PAS.T_FUND_TRANS T WHERE T.FUND_CODE=#{account_code} AND T.POLICY_ID=(SELECT POLICY_ID FROM DEV_PAS.T_CONTRACT_MASTER WHERE POLICY_CODE=#{policy_code})
        ]]></if>
	</select>
	
	<!-- 查询追加保费交易记录 -->
	<select id="JRQD_PA_findAllAddPremFundTrans" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE A.POLICY_ID=
			(SELECT POLICY_ID FROM APP___PAS__DBUSER.T_CONTRACT_MASTER WHERE POLICY_CODE=#{policy_code})
		AND A.TRANS_CODE IN('13','29','31','37','40','34')
	</select>
	
	<select id="JRQD_PA_findAllForSettleFundTrans" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
		SELECT * FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE A.POLICY_ID=#{policy_id}
			 AND A.DEAL_TIME=(SELECT MAX(DEAL_TIME) FROM APP___PAS__DBUSER.T_FUND_TRANS WHERE POLICY_ID=#{policy_id})
	    ]]>
		<if test=" trans_code_list != null and trans_code_list.size()!=0">
			<![CDATA[ AND A.TRANS_CODE IN ]]>
			<foreach collection ="trans_code_list" item="trans_code_list" index="index" open="(" close=")" separator=",">#{trans_code_list}</foreach>
		</if>
	</select>
	
	<select id="JRQD_PA_findAllOfSettleFundTrans" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT * FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE A.POLICY_ID=#{policy_id}
        AND A.BUSI_ITEM_ID=#{busi_item_id}
		AND A.DEAL_TIME BETWEEN #{begin_time} AND #{end_time}
	</select>
	
	<!-- 查询交易表中所有的交易时间 -->
	<select id="JRQD_PA_fingAllDealTimes" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT DISTINCT DEAL_TIME FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE 1=1
		<include refid="JRQD_PA_fundTransWhereCondition" />
		<![CDATA[ ORDER BY A.DEAL_TIME ]]> 
	</select>
	
	
	
	<!-- 查询总金额 -->
	<select id="JRQD_PA_querySumAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(a.trans_amount) sum_amount ,MAX(a.deal_time) AS deal_time FROM APP___PAS__DBUSER.t_fund_trans a 
				WHERE  A.LIST_ID = #{list_id} 
				and a.DEAL_TIME > #{lastBalaDate} and a.DEAL_TIME <= #{balaDate}
		]]>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" transCodeList != null and transCodeList.size() != 0">
			<![CDATA[ AND A.TRANS_CODE in ]]>
			<foreach collection ="transCodeList" item="transCodeList" index="index" open="(" close=")" separator=",">#{transCodeList}</foreach>
		</if>
	</select>
	
	<!-- 查询单条数据 -->
	<select id="JRQD_PA_findFundTrans" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE,A.UPDATE_TIMESTAMP FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1000  ]]>
			<include refid="JRQD_PA_fundTransWhereCondition" />
		<![CDATA[ ORDER BY A.DEAL_TIME ]]>
	</select>
	
	<!-- 查询最近最次的保单管理费、风险保费、资产管理费 -->
	<select id="JRQD_findAboutFee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select t.trans_amount,t.trans_code
      from (select tft.trans_amount, tft.trans_code
              from dev_pas.t_fund_trans tft
             where tft.trans_code in ('04', '03', '02')
               and tft.policy_id = #{policy_id}
               and tft.deal_time >= #{deal_time}
             ORDER BY tft.deal_time desc) T
     where rownum <= #{LESS_NUM}
 ]]>
	</select>
	
	<!-- 查询部分领取的交易数据 针对老核心可能出现的同一次部分领取送fund_trans表两条数据 故根据交易前账户价值来进行分组-->
	<select id="JRQD_findPayRates" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT SUM(FT.TRANS_AMOUNT) TRANS_AMOUNT, FT.BALANCE_UNITS_BF, FT.TRANS_PROPORTION, FT.DEAL_TIME 
			FROM DEV_PAS.T_FUND_TRANS FT 
			WHERE FT.POLICY_ID = #{policy_id}
			AND FT.BUSI_ITEM_ID = #{busi_item_id} 
			and FT.TRANS_CODE IN (${trans_code})
			AND FT.DEAL_TIME <= #{cur_date}
			GROUP BY FT.BALANCE_UNITS_BF,FT.TRANS_PROPORTION, FT.DEAL_TIME ORDER BY FT.DEAL_TIME
 		]]>
	</select>
	
	<select id="JRQD_findfundTransCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select tft.trans_amount, tft.trans_code
              from dev_pas.t_fund_trans tft
             where tft.trans_code = '41'
               and tft.policy_id = #{policy_id}
               and tft.deal_time >= #{deal_time}
             ORDER BY tft.deal_time desc ]]>
	</select>
	
	<!-- 查询单条数据 -->
	<select id="JRQD_PA_findFundNameByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1]]>
			<include refid="JRQD_PA_fundTransWhereCondition" />
		<![CDATA[ ORDER BY A.DEAL_TIME ]]>
	</select>
	
	<select id="JRQD_PA_findPayTotalPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(A.TRANS_AMOUNT) AS TRANS_AMOUNT FROM DEV_PAS.T_FUND_TRANS A WHERE 1 = 1 AND A.TRANS_CODE = '22'
		]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[ AND A.DEAL_TIME >= #{deal_time} ]]></if>
	</select>
	
	<!-- 查询出险日之后所有账户明细数据 -->
	<select id="JRQD_PA_findAllFundTransByClaimDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.MONEY_CODE, A.PRODUCT_ID, A.TRANS_UNITS, A.UNIT_NUMBER, A.FUND_CODE, A.ITEM_ID, 
			A.TRANS_TYPE, A.TRANS_AMOUNT, A.SETTLEMENT_ID, A.TRANS_ID, A.FUND_SELL_PRICE, 
			A.LIST_ID, A.APPLY_TRANS_CODE, A.POLICY_CHG_ID, A.BALANCE_UNITS_BF, A.BUSI_ITEM_ID, A.POLICY_ID, A.APPLY_ID, 
			A.FUND_PURC_PRICE, A.TRANS_PROPORTION, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1000   ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" deal_time  != null  and  deal_time  != ''  "><![CDATA[ AND A.DEAL_TIME >= #{deal_time} ]]></if>
		<![CDATA[ ORDER BY A.DEAL_TIME ]]> 
	</select>
	
	<select id="JRQD_PA_findAllByPolicyChgId"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.TRANS_ID,
			       A.UNIT_NUMBER,
			       A.MONEY_CODE,
			       A.BUSI_ITEM_ID,
			       A.ITEM_ID,
			       A.FUND_CODE,
			       A.POLICY_ID,
			       A.LIST_ID,
			       A.PRODUCT_ID,
			       A.SETTLEMENT_ID,
			       A.TRANS_CODE,
			       A.TRANS_TYPE,
			       A.TRANS_AMOUNT,
			       A.TRANS_UNITS,
			       A.TRANS_PROPORTION,
			       A.TRANS_INTEREST,
			       A.TRANS_PRICE,
			       A.DEAL_TIME,
			       A.APPLY_ID,
			       A.APPLY_TRANS_CODE,
			       A.FUND_PURC_PRICE,
			       A.FUND_SELL_PRICE,
			       A.BALANCE_UNITS_BF,
			       A.POLICY_CHG_ID
			       FROM DEV_PAS.T_FUND_TRANS A WHERE 1 = 1
		]]>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</select>
	<select id="JRQD_PA_queryCustomerUnitByFundCode"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.FUND_CODE,
       T.FUND_NAME,
       T.BUSINESS_PRD_ID,
       T.PRODUCT_NAME_STD,
       T.BRANCH_CODE,
       T.DEAL_TIME,
       T.FUND_PURC_PRICE,
       T.FUND_SELL_PRICE,
       SUM(OLD_BUY_BALANCE) OLD_BUY_BALANCE,
       SUM(OLD_SELL_BALANCE) OLD_SELL_BALANCE,
       SUM(NbUnit) NbUnit,
       SUM(AddUnit) AddUnit,
       SUM(InUnit) InUnit,
       SUM(NbUnit + AddUnit + InUnit) OffTotal,
       SUM(HesitationBack) HesitationBack,
       SUM(AnnuPayBack) AnnuPayBack,
       SUM(SurValBack) SurValBack,
       SUM(SurrenderBack) SurrenderBack,
       SUM(ClaimBack) ClaimBack,
       SUM(MaturityBack) MaturityBack,
       SUM(OutBack) OutBack,
       SUM(HesitationBack + AnnuPayBack + SurValBack + SurrenderBack +
           ClaimBack + MaturityBack + OutBack) BackTotal, /*单位数赎回-赎回合计*/
       SUM(RiskFee) RiskFee,
       SUM(PlicyMagFee) PlicyMagFee,
       SUM(RiskFee + PlicyMagFee) FeeTotal, /*单位数扣除-扣除合计*/
       0 AdjUnit,
       SUM(BUY_Unit - SELL_UNIT) CurrentUnit /*期末数额*/
  FROM （SELECT TF.FUND_CODE,
       TF.FUND_NAME,
       TBP.BUSINESS_PRD_ID,
       TBP.PRODUCT_NAME_STD,
       TCM.BRANCH_CODE,
       TFT.DEAL_TIME,
       TFT.FUND_PURC_PRICE,
       TFT.FUND_SELL_PRICE， (SELECT NVL(SUM(T.BALANCE_UNITS_BF), 0)
                               FROM DEV_PAS.T_FUND_TRANS T
                              WHERE T.TRANS_ID = TFT.TRANS_ID
                                AND T.TRANS_TYPE = '2') OLD_BUY_BALANCE, /*前一日T_FUND_TRANS总买入单位数*/
       (SELECT NVL(SUM(T.BALANCE_UNITS_BF), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_TYPE = '1') OLD_SELL_BALANCE, /*前一日T_FUND_TRANS总卖出单位数*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('11', '27', '29')
           AND T.TRANS_TYPE = '2') NbUnit, /*单位数买入-新契约买入*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('06', '13', '31', '34', '37', '40')
           AND T.TRANS_TYPE = '2') AddUnit, /*单位数买入-追加保费*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('14')
           AND T.TRANS_TYPE = '2') InUnit, /*单位数买入-其他账户转入*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T，DEV_PAS.T_POLICY_ACKNOWLEDGEMENT TPA
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('21', '07')
           AND TPA.ACKNOWLEDGE_DATE + TCBP.HESITATION_PERIOD_DAY >=
               T.DEAL_TIME
           AND T.TRANS_TYPE = '1') HesitationBack, /*单位数赎回-犹豫期退费赎回*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('24')
           AND T.TRANS_TYPE = '2') AnnuPayBack, /*单位数赎回-年金领取赎回*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('08', '22')
           AND T.TRANS_TYPE = '2') SurValBack, /*单位数赎回-部分领取赎回*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('07', '21')
           AND T.TRANS_TYPE = '2') SurrenderBack, /*单位数赎回-退保赎回*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('25')
           AND T.TRANS_TYPE = '2') ClaimBack, /*单位数赎回-理赔赎回*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('24')
           AND T.TRANS_TYPE = '2') MaturityBack, /*单位数赎回-满期赎回*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('26')
           AND T.TRANS_TYPE = '2') OutBack, /*单位数赎回-转出赎回*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('03', '41')
           AND T.TRANS_TYPE = '2') RiskFee, /*单位数扣除-死亡风险保费扣除*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_CODE IN ('02')
           AND T.TRANS_TYPE = '2') PlicyMagFee, /*单位数扣除-保单管理费扣除*/
       0 AdjUnit, /*单位数扣除-投资单位数调整*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_TYPE = '2') BUY_Unit, /*买入总数*/
       (SELECT NVL(SUM(T.TRANS_UNITS), 0)
          FROM DEV_PAS.T_FUND_TRANS T
         WHERE T.TRANS_ID = TFT.TRANS_ID
           AND T.TRANS_TYPE = '1') SELL_UNIT /*卖出总数*/
  FROM DEV_PAS.T_CONTRACT_MASTER    TCM,
       DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
       DEV_PAS.T_CONTRACT_INVEST    TCI,
       DEV_PAS.T_FUND               TF,
       DEV_PAS.T_FUND_TRANS         TFT,
       DEV_PAS.T_BUSINESS_PRODUCT   TBP
 WHERE 1 = 1
   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
   AND TCBP.BUSI_ITEM_ID = TCI.BUSI_ITEM_ID
   AND TFT.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
   AND TCI.LIST_ID = TFT.LIST_ID
   AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
   AND TFT.FUND_CODE = TF.FUND_CODE]]>
   <if test=" fund_code  != null "><![CDATA[ AND TF.FUND_CODE IN =${fund_code}]]></if>
   <if test=" start_date  != null "><![CDATA[ AND TFT.DEAL_TIME >= #{start_date}]]></if>
   <if test=" end_date  != null "><![CDATA[ AND TFT.DEAL_TIME <= #{end_date}]]></if>
   <![CDATA[
   ) T
 GROUP BY T.FUND_CODE,
          T.FUND_NAME,
          T.BUSINESS_PRD_ID,
          T.PRODUCT_NAME_STD,
          T.BRANCH_CODE,
          T.DEAL_TIME,
          T.FUND_PURC_PRICE,
          T.FUND_SELL_PRICE
	]]>	
		
	</select>
	
	<!-- 查询多账户变动时间 -->
	<select id="JRQD_PA_findAccountChangeTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.DEAL_TIME
  FROM (SELECT TFGT.DEAL_TIME DEAL_TIME
          FROM DEV_PAS.T_FUND_GROUP_TRANS TFGT
         WHERE 1=1 ]]>
          <if test=" policy_id  != null "><![CDATA[ AND TFGT.POLICY_ID = #{policy_id} ]]></if>
          <if test=" start_date  != null "><![CDATA[ AND TFGT.DEAL_TIME >= #{start_date}]]></if>          
          <if test=" end_date  != null "><![CDATA[ AND TFGT.DEAL_TIME <= #{end_date}]]></if>
          <if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND TFGT.TRANS_CODE IN ]]>
			<foreach collection ="trans_code_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		  </if>
		
        <![CDATA[ UNION
        SELECT TFT.DEAL_TIME DEAL_TIME
          FROM DEV_PAS.T_FUND_TRANS TFT
         WHERE 1=1]]>
          <if test=" policy_id  != null "><![CDATA[ AND TFT.POLICY_ID = #{policy_id} ]]></if>
          <if test=" start_date  != null "><![CDATA[ AND TFT.DEAL_TIME >= #{start_date}]]></if>          
          <if test=" end_date  != null "><![CDATA[ AND TFT.DEAL_TIME <= #{end_date}]]></if>
          <if test=" trans_code_list  != null and trans_code_list.size()!=0">
			<![CDATA[ AND TFT.TRANS_CODE IN ]]>
			<foreach collection ="trans_code_list" 
			item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		  </if>
       <![CDATA[ UNION
        SELECT TFS.SETTLE_DATE DEAL_TIME
          FROM DEV_PAS.T_FUND_SETTLEMENT TFS, DEV_PAS.T_CONTRACT_INVEST TCI
         WHERE TFS.INVEST_ID = TCI.LIST_ID ]]> 
          <if test=" policy_id  != null "><![CDATA[ AND TCI.POLICY_ID = #{policy_id} ]]></if>
          <if test=" start_date  != null "><![CDATA[ AND TFS.SETTLE_DATE >= #{start_date}]]></if>          
          <if test=" end_date  != null "><![CDATA[ AND TFS.SETTLE_DATE <= #{end_date}]]></if>
           <![CDATA[) T ORDER BY T.DEAL_TIME  ]]>
	</select>
	
	<!-- 犹豫期内投连退保金额明细查询 -->
	<select id="JRQD_queryFundTransForIT" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[				
			SELECT * FROM (SELECT A.TRANS_CODE,
					       A.TRANS_AMOUNT,
					       A.DEAL_TIME,
					       A.POLICY_ID,
					       A.BUSI_ITEM_ID
					  FROM APP___PAS__DBUSER.T_FUND_TRANS A
					 WHERE  A.TRANS_CODE IN ('35', '36', '37', '38', '39','40', '41', '04', '02')
					 	AND A.POLICY_ID = #{policy_id}
					    AND A.BUSI_ITEM_ID = #{busi_item_id}				    
					UNION ALL
					SELECT B.TRANS_CODE,
					       B.APPLY_AMOUNT AS TRANS_AMOUNT,
					       B.APPLY_TIME   AS DEAL_TIME,
					       B.POLICY_ID,
					       B.BUSI_ITEM_ID
					  FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY B
					 WHERE  B.TRANS_CODE IN ('11', '27', '29')
					    AND B.POLICY_ID = #{policy_id}
					    AND B.BUSI_ITEM_ID = #{busi_item_id}	
				) A WHERE A.DEAL_TIME <= #{deal_time}				
		 ]]>
		 
	</select>
			
	<!-- 查询投连保单账户变动时间 -->
	<select id="JRQD_PA_findCastAndConnectChangeTime" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			SELECT T.DEAL_TIME
		  FROM (SELECT TFT.DEAL_TIME DEAL_TIME
		          FROM APP___PAS__DBUSER.T_FUND_TRANS TFT
		         WHERE 1 = 1 ]]>
		     <if test=" policy_id  != null "><![CDATA[ AND TFT.POLICY_ID = #{policy_id} ]]></if>
		     <if test=" start_date  != null "><![CDATA[ AND TFT.DEAL_TIME >= #{start_date}]]></if>          
             <if test=" end_date  != null "><![CDATA[ AND TFT.DEAL_TIME <= #{end_date}]]></if> 
             <if test=" trans_code_list  != null and trans_code_list.size()!=0">
			 <![CDATA[ AND TFT.TRANS_CODE IN ]]>
			 <foreach collection ="trans_code_list" 
			 item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		     </if>     
		     <![CDATA[  
		        UNION
		        SELECT TFTA.APPLICATION_DATE DEAL_TIME
		          FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY TFTA
		         WHERE 1 = 1 ]]>
		     <if test=" policy_id  != null "><![CDATA[ AND TFTA.POLICY_ID = #{policy_id} ]]></if>
		     <if test=" start_date  != null "><![CDATA[ AND TFTA.APPLICATION_DATE >= #{start_date}]]></if>          
             <if test=" end_date  != null "><![CDATA[ AND TFTA.APPLICATION_DATE <= #{end_date}]]></if>  
             <if test=" trans_code_list  != null and trans_code_list.size()!=0">
			 <![CDATA[ AND TFTA.TRANS_CODE IN ]]>
			 <foreach collection ="trans_code_list" 
			 item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		     </if>  		     
		        <![CDATA[   
		           ) T
		 ORDER BY T.DEAL_TIME ASC ]]>
	</select>	
	<!-- 获取投连万能账户累计的交易金额 -->
	<select id="JRQD_PA_findTransFundAmount" resultType="java.util.Map" parameterType="java.util.Map">
			<if test=" pricingdateflag == 'zero' ">
				<![CDATA[
					SELECT SUM(TFT.TRANS_AMOUNT) TRANS_AMOUNT,
					       SUM(TFT.TRANS_UNITS) TRANS_UNITS,
					       TFT.TRANS_CODE
					  FROM APP___PAS__DBUSER.T_FUND_TRANS TFT 
						   WHERE 1=1
				]]>
				 <if test=" policy_id  != null "><![CDATA[ AND TFT.POLICY_ID = #{policy_id} ]]></if>
			     <if test=" now_date  != null "><![CDATA[ AND TFT.DEAL_TIME = #{now_date}]]></if>
	             <if test=" trans_code_list  != null and trans_code_list.size()!=0">
				 <![CDATA[ AND TFT.TRANS_CODE IN ]]>
				 <foreach collection ="trans_code_list" 
				 item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
			     </if> 
			     <![CDATA[ GROUP BY TFT.TRANS_CODE ]]>
			</if>
			
			<if test=" pricingdateflag == 'one' ">
				<![CDATA[
					SELECT SUM(TFTA.APPLY_AMOUNT) TRANS_AMOUNT,
					       SUM(TFTA.APPLY_UNITS) TRANS_UNITS, 
					       TFTA.TRANS_CODE
	                       FROM APP___PAS__DBUSER.T_FUND_TRANS_APPLY TFTA 
	                       WHERE 1=1
				]]>
				 <if test=" policy_id  != null "><![CDATA[ AND TFTA.POLICY_ID = #{policy_id} ]]></if>
			     <if test=" now_date  != null "><![CDATA[ AND TFTA.APPLICATION_DATE = #{now_date}]]></if>
	             <if test=" trans_code_list  != null and trans_code_list.size()!=0">
				 <![CDATA[ AND TFTA.TRANS_CODE IN ]]>
				 <foreach collection ="trans_code_list" 
				 item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
			     </if>  
			     <![CDATA[ GROUP BY TFTA.TRANS_CODE ]]>
			</if>
			 
	</select>		
	<!-- 查询期初和期末计算账户单位和价值逻辑 -->
	<select id="JRQD_PA_findAllFundTransOrderBy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRANS_INTEREST, A.TRANS_UNITS, A.FUND_CODE,  
			A.TRANS_TYPE, A.TRANS_AMOUNT,  A.FUND_SELL_PRICE, A.APPLY_TRANS_CODE,  A.BALANCE_UNITS_BF, 
			A.FUND_PURC_PRICE, A.DEAL_TIME, A.TRANS_CODE, A.TRANS_PRICE FROM APP___PAS__DBUSER.T_FUND_TRANS A WHERE ROWNUM <=  1000   ]]>
		<include refid="JRQD_PA_fundTransWhereCondition" />
		<if test=" trans_code_list != null and trans_code_list.size() != 0">
			<![CDATA[ AND A.TRANS_CODE in ]]>
			<foreach collection ="trans_code_list" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test=" pricingdateflag == 'zero' "><![CDATA[ ORDER BY A.UPDATE_TIMESTAMP DESC ]]></if>
		<if test=" pricingdateflag == 'one' "><![CDATA[ ORDER BY A.UPDATE_TIMESTAMP ASC ]]></if>
	</select>
	<!-- 查询保全追加和领取金额 -->
	<select id="JRQD_PA_findAcceptChangeTransFundAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.FEE_AMOUNT TRANS_AMOUNT, B.SERVICE_CODE, B.ACCEPT_STATUS, B.ACCEPT_TIME
					  FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,
					       APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B,
					       APP___PAS__DBUSER.T_CONTRACT_MASTER  D,
					       APP___PAS__DBUSER.T_FUND_TRANS_APPLY E
					 WHERE D.POLICY_ID = A.POLICY_ID
					   AND A.ACCEPT_ID = B.ACCEPT_ID
					   AND B.ACCEPT_CODE=E.ACCEPT_CODE
					   AND B.ACCEPT_STATUS = 18]]>
	    <if test=" policy_id  != null "><![CDATA[ AND D.POLICY_ID = #{policy_id} ]]></if>
	    <if test=" accept_code  != null and accept_code != '' "><![CDATA[ AND B.ACCEPT_CODE = #{accept_code} ]]></if>
	    <if test=" now_date  != null "><![CDATA[ AND E.APPLY_TIME = #{now_date}]]></if>
		<if test=" service_code_list != null and service_code_list.size() != 0">
			<![CDATA[ AND B.SERVICE_CODE in ]]>
			<foreach collection ="service_code_list" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<![CDATA[ ORDER BY B.ACCEPT_TIME DESC ]]>
	</select>
	<!-- 查询扣费金额 -->
	<select id="JRQD_PA_findFundService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT E.APPLY_AMOUNT TRANS_AMOUNT,E.TRANS_CODE,B.SERVICE_CODE, B.ACCEPT_STATUS, B.ACCEPT_TIME
						FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,
						              APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B,
						              APP___PAS__DBUSER.T_CONTRACT_MASTER D,
						              APP___PAS__DBUSER.T_FUND_SERVICE E
						WHERE D.POLICY_ID=A.POLICY_ID 
						  AND A.ACCEPT_ID=B.ACCEPT_ID 
						  AND B.ACCEPT_CODE=E.ACCEPT_CODE
						  AND B.ACCEPT_STATUS=18]]>
	    <if test=" policy_id  != null "><![CDATA[ AND D.POLICY_ID = #{policy_id} ]]></if>
	    <if test=" accept_code  != null and accept_code != '' "><![CDATA[ AND B.ACCEPT_CODE = #{accept_code} ]]></if>
	    <if test=" now_date  != null "><![CDATA[ AND B.ACCEPT_TIME = #{now_date}]]></if>
		<if test=" service_code_list != null and service_code_list.size() != 0">
			<![CDATA[ AND B.SERVICE_CODE in ]]>
			<foreach collection ="service_code_list" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<if test=" trans_code_list != null and trans_code_list.size() != 0">
			<![CDATA[ AND E.TRANS_CODE in ]]>
			<foreach collection ="trans_code_list" item="item" index="index" open="(" close=")" separator=",">#{item}</foreach>
		</if>
		<![CDATA[ ORDER BY B.ACCEPT_TIME DESC ]]>
	</select>
</mapper>
