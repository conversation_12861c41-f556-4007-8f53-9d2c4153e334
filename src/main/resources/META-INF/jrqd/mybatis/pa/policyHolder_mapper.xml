<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.PolicyHolderDaoImpl">

	<sql id="JRQD_PA_policyHolderWhereCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" soci_secu  != null "><![CDATA[ AND A.SOCI_SECU = #{soci_secu} ]]></if>
		<if test=" smoking  != null "><![CDATA[ AND A.SMOKING = #{smoking} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" job_underwrite != null and job_underwrite != ''  "><![CDATA[ AND A.JOB_UNDERWRITE = #{job_underwrite} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" in_customer_id  != null"><![CDATA[ or tl.customer_id = #{in_customer_id} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ A.POLICY_ID = #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" agent_relation != null and agent_relation != ''  "><![CDATA[ AND A.AGENT_RELATION = #{agent_relation} ]]></if>
	</sql>

<sql id="JRQD_findHolderCustomerorinsuredCustomerIdByPolicyCodeAndName_cjk">
		<if test=" policy_code  != null "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" customer_name  != null "><![CDATA[ AND D.CUSTOMER_NAME = #{customer_name} ]]></if>
	</sql>	
<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_queryPolicyHolderByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryPolicyHolderByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryPolicyHolderByPolicyCodeCondition">
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="JRQD_PA_queryPolicyHolderByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	
	
<!-- 添加操作 -->
	<insert id="JRQD_PA_addPolicyHolder"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_POLICY_HOLDER__LIST_ID.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_POLICY_HOLDER(
				ADDRESS_ID, INSERT_TIME, CUSTOMER_HEIGHT, CUSTOMER_ID, JOB_CODE, UPDATE_TIME, CUSTOMER_WEIGHT, 
				APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, JOB_UNDERWRITE, LIST_ID, UPDATE_TIMESTAMP, 
				INSERT_BY, POLICY_ID, SOCI_SECU, SMOKING,ANNUAL_INCOME_CEIL,INCOME_SOURCE,RESIDENT_TYPE,AGENT_RELATION,APPLICANT_SPE_PEOPLE ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, SYSDATE , #{customer_height, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} , #{job_code, jdbcType=VARCHAR} , SYSDATE , #{customer_weight, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{job_underwrite, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{soci_secu, jdbcType=NUMERIC} , #{smoking, jdbcType=NUMERIC}, #{annual_income_ceil, jdbcType=NUMERIC}, #{income_source, jdbcType=VARCHAR}
				, #{resident_type, jdbcType=VARCHAR} , #{agent_relation, jdbcType=VARCHAR},#{applicant_spe_people, jdbcType=NUMERIC})  
		 ]]>
	</insert>
<!-- 删除操作 -->	
	<delete id="JRQD_PA_deletePolicyHolder" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_POLICY_HOLDER WHERE  LIST_ID=#{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_PA_updatePolicyHolder" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_POLICY_HOLDER ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
		    SMOKING = #{smoking, jdbcType=NUMERIC} ,
		    ANNUAL_INCOME_CEIL = #{annual_income_ceil, jdbcType=NUMERIC} ,
		    INCOME_SOURCE = #{income_source, jdbcType=VARCHAR} ,
		    RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR} ,
		    AGENT_RELATION = #{agent_relation, jdbcType=VARCHAR} ,
		    APPLICANT_SPE_PEOPLE = #{APPLICANT_SPE_PEOPLE, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findPolicyHolderByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1 ]]>
		<include refid="JRQD_PA_queryPolicyHolderByListIdCondition" />
	</select>
	
	<select id="JRQD_PA_findPolicyHolderByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryPolicyHolderByPolicyIdCondition" />
	</select>
	
	<select id="JRQD_findPolicyHolderByPolicyCode1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE ROWNUM = 1 ]]>
		<include refid="JRQD_PA_queryPolicyHolderByPolicyCodeCondition" />
	</select>
	
	<select id="JRQD_PA_findPolicyHolderByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1   ]]>
		<include refid="JRQD_PA_queryPolicyHolderByAddressIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE   ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE  ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_PA_findPolicyHolderTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryPolicyHolderForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.CUSTOMER_HEIGHT, B.CUSTOMER_ID, B.JOB_CODE, B.CUSTOMER_WEIGHT, B.APPLICANT_SPE_PEOPLE,
			B.APPLY_CODE, B.POLICY_CODE, B.JOB_UNDERWRITE, B.LIST_ID, B.SOCI_SECU, B.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLICANT_SPE_PEOPLE,
			A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 查询单条 -->
	<select id="JRQD_PA_findPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.APPLICANT_SPE_PEOPLE,
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />
	</select>
<!--  -->
<select id="JRQD_PA_findPolicyHolderCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, A.APPLICANT_SPE_PEOPLE,
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />
		  union
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 1 as APPLICANT_SPE_PEOPLE,
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,0 as ANNUAL_INCOME_CEIL,'1' as INCOME_SOURCE,'1' as RESIDENT_TYPE, '1' as AGENT_RELATION,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />  
	</select>
	<!--查询客户信息  -->	
<select id="JRQD_PA_findPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UN_CUSTOMER_CODE, A.OLD_CUSTOMER_ID, A.CUSTOMER_ID, A.CUSTOMER_NAME, 
			A.CUSTOMER_WEIGHT, A.CUSTOMER_GENDER, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_CERTI_CODE, A.CUSTOMER_LEVEL
			FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />	  
</select>
<!-- 老核心改为查询投保人地址 -->
	<select id="JRQD_findPolicyHolderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
       <![CDATA[

			 SELECT A1.CUSTOMER_NAME,
		        floor(months_between(A1.ISSUE_DATE,A1.CUSTOMER_BIRTHDAY) / 12) AS AGE,
		        A1.JOB_UNDERWRITE, 
		        A1.CUSTOMER_BIRTHDAY,
		        A1.GENDER_DESC,
		        A1.TYPE_NAME,
		        A1.CUSTOMER_VIP,
		        A1.MARRIAGE_STATUS,
		        A1.MARRIAGE,
		        A1.COUNTRY_CODE,
		        A1.COUNTRY_NAME,
		        A1.CUSTOMER_CERT_TYPE,
		        A1.TYPE,
		        A1.CUSTOMER_CERTI_CODE,
		        A1.CUSTOMER_ID,
		       A1.DRIVER_LICENSE_TYPE,
		        A1.LICENSE_DESC,
		        A1.JOB_CODE,
		       A1.JOB_UW_LEVEL,
		       A1.CATEGORY_JOB_DESC,
		        A1.KIND_JOB_DESC,
		        P.MOBILE_TEL,
		        A1.OFFICE_TEL,
		        A1.OFFEN_USE_TEL,
		        A1.FAX_TEL,
		        A1.HOUSE_TEL,
		        P.ADDRESS,
		        P.POST_CODE,
		        P.EMAIL,
		        P.STATE,
		        QSTATE.NAME            AS STATENAME,
		        P.CITY,
		        QCITY.NAME             AS CITYNAME,
		        P.DISTRICT,
		        QDISTRICT.NAME         AS DISTRICTNAME,
		        D.NEXT_ACCOUNT_BANK,
		        (select TB.BANK_BRANCH_NAME from APP___PAS__DBUSER.T_BANK_BRANCH TB where TB.BANK_CODE = D.NEXT_ACCOUNT_BANK and rownum=1)  AS BANK_NAME,
		        D.NEXT_ACCOUNT,
		        D.NEXT_ACCOUNT_NAME,
		        D.PAY_NEXT ,
		        D.PAY_LOCATION,
		        N.NAME,
		        A1.COMPANY_NAME,
		        A1.APPLY_DATE,
		        A1.CUSTOMER_GENDER,
		        A1.ORGAN_CODE,
		        TU.ORGAN_NAME,
		        TJU.JOB_UW_LEVEL_NAME,
		        TJU.JOB_UW_LEVEL_CODE,
		        A1.CUST_CERT_STAR_DATE,
		        A1.CUST_CERT_END_DATE
		   FROM (SELECT B.POLICY_ID,
		                B.JOB_CODE,
		                B.JOB_UNDERWRITE,
		                B.ADDRESS_ID,
		                A.CUSTOMER_ID,
		                A.UN_CUSTOMER_CODE,
		                A.OLD_CUSTOMER_ID,
		                A.MARRIAGE_DATE,
		                A.EDUCATION,
		                A.CUSTOMER_NAME,
		                A.CUSTOMER_BIRTHDAY,
		                A.CUSTOMER_GENDER,
		                A.CUSTOMER_HEIGHT,
		                A.CUSTOMER_WEIGHT,
		                A.CUSTOMER_CERT_TYPE,
		                A.CUSTOMER_CERTI_CODE,
		                A.CUSTOMER_ID_CODE,
		                A.CUST_CERT_STAR_DATE,
		                A.CUST_CERT_END_DATE,
		                A.JOB_NATURE,
		                A.JOB_TITLE,
		                A.MARRIAGE_STATUS,
		                A.IS_PARENT,
		                A.ANNUAL_INCOME,
		                A.COUNTRY_CODE,
		                A.RELIGION_CODE,
		                A.NATION_CODE,
		               A.DRIVER_LICENSE_TYPE,
		                A.COMPANY_NAME,
		                A.OFFEN_USE_TEL,
		                A.HOUSE_TEL,
		                A.FAX_TEL,
		                A.OFFICE_TEL,
		                A.MOBILE_TEL,
		                A.EMAIL,
		                A.QQ,
		                A.WECHAT_NO,
		                A.OTHER,
		                A.CUSTOMER_LEVEL,
		                A.CUSTOMER_RISK_LEVEL,
		                A.CUSTOMER_VIP,
		                A.SMOKING_FLAG,
		                A.DRUNK_FLAG,
		                A.BLACKLIST_FLAG,
		                A.HOUSEKEEPER_FLAG,
		                A.SYN_MDM_FLAG,
		                A.LIVE_STATUS,
		                A.RETIRED_FLAG,
		                A.DEATH_DATE,
		                A.HEALTH_STATUS,
		                A.REMARK,
		                A.CUST_PWD,
		                E.GENDER_DESC,
		                F.TYPE_NAME,
		                G.MARRIAGE,
		                H.COUNTRY_NAME,
		                J.TYPE,
		               K.LICENSE_DESC,
		                TM.APPLY_DATE,
		                TM.ORGAN_CODE,
		                TM.ISSUE_DATE,
		                L.JOB_NAME           AS CATEGORY_JOB_DESC,
		                L.JOB_UW_LEVEL,
		                M.JOB_UW_LEVEL_NAME   AS KIND_JOB_DESC
		           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TM,
		                APP___PAS__DBUSER.T_POLICY_HOLDER B,
		               APP___PAS__DBUSER.T_CUSTOMER        A
		           LEFT JOIN APP___PAS__DBUSER.T_GENDER E
		             ON E.GENDER_CODE = A.CUSTOMER_GENDER
		           LEFT JOIN APP___PAS__DBUSER.T_YES_NO F
		             ON F.YES_NO = A.CUSTOMER_VIP
		           LEFT JOIN APP___PAS__DBUSER.T_MARRIAGE G
		             ON G.MARRIAGE_CODE = A.MARRIAGE_STATUS
		           LEFT JOIN APP___PAS__DBUSER.T_COUNTRY H
		             ON H.COUNTRY_CODE = A.COUNTRY_CODE
		           LEFT JOIN APP___PAS__DBUSER.T_CERTI_TYPE J
		             ON J.CODE = A.CUSTOMER_CERT_TYPE
		           LEFT JOIN APP___PAS__DBUSER.T_LICENSE_TYPE K
		             ON K.LICENSE_TYPE = A.DRIVER_LICENSE_TYPE
		           LEFT JOIN APP___PAS__DBUSER.T_JOB_CODE L
		             ON L.JOB_CODE =A.Job_Code
		           LEFT JOIN APP___PAS__DBUSER.t_Job_Underwrite M
		             ON JOB_UW_LEVEL_CODE = A.JOB_KIND
		             WHERE A.CUSTOMER_ID = B.CUSTOMER_ID
		            AND TM.POLICY_ID = B.POLICY_ID
		           AND B.POLICY_ID=TM.POLICY_ID
		            AND B.POLICY_CODE =#{policy_code}) A1
		   LEFT JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT D
		     ON D.POLICY_ID = A1.POLICY_ID
		   LEFT JOIN APP___PAS__DBUSER.T_PAY_MODE N
		     ON N.CODE = D.PAY_NEXT
		   LEFT JOIN APP___PAS__DBUSER.T_ADDRESS P
		     ON P.ADDRESS_ID = A1.ADDRESS_ID
		   LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QSTATE
		     ON QSTATE.CODE = P.STATE
		   LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QCITY
		     ON QCITY.CODE = P.CITY
		   LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QDISTRICT
		     ON QDISTRICT.CODE = P.DISTRICT
		   LEFT JOIN APP___PAS__DBUSER.t_Udmp_Org TU
		     ON TU.ORGAN_CODE = a1.ORGAN_CODE
		   LEFT JOIN APP___PAS__DBUSER.T_JOB_UNDERWRITE TJU
		     ON TJU.JOB_UW_LEVEL_CODE = A1.JOB_UW_LEVEL
 
           ]]> 
	</select>
	
	<select id="JRQD_PA_findPolicyHolderInformationByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select tc.customer_name as appnt_name,
         tc.offen_use_tel as phone,
         tc.customer_birthday as appnt_birthday,
         th.customer_id ,
         (select address
            from APP___PAS__DBUSER.T_address ta
           where ta.address_id = th.address_id) as postal_address
    from APP___PAS__DBUSER.T_Customer tc, APP___PAS__DBUSER.T_POLICY_HOLDER th
   where tc.customer_id = th.customer_id
     and th.policy_id = #{policy_id}  ]]>
	</select>
	
	<!-- 再保险 受益人查询 -->
	<select id="JRQD_findCustomerInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select c.customer_id , c.customer_name ,c.customer_gender ,c.customer_cert_type , c.customer_certi_code from APP___PAS__DBUSER.T_customer c 
    where c.customer_id in( select ph.customer_id from APP___PAS__DBUSER.T_policy_holder ph where ph.policy_code = #{policy_code})
     ]]>
	</select>
	
	<!-- Modify by yangbo_wb  2016-08-09  FIX TC 缺陷ID-9275：响应报文字段ExitReason（出境事由），应取：T_CONTRACT_PRODUCT_OTHER.Field16。 -->
	<select id="JRQD_findPolicyHolderInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
      SELECT A1.ADDRESS_ID, A1.CUSTOMER_ID, A1.UN_CUSTOMER_CODE, A1.POLICY_ID, TCRO.FIELD16,
      A1.OLD_CUSTOMER_ID, A1.MARRIAGE_DATE, A1.EDUCATION, 
      A1.CUSTOMER_NAME, A1.CUSTOMER_BIRTHDAY, A1.CUSTOMER_GENDER, 
      A1.CUSTOMER_HEIGHT, A1.CUSTOMER_WEIGHT, A1.CUSTOMER_CERT_TYPE, 
      A1.CUSTOMER_CERTI_CODE, A1.CUSTOMER_ID_CODE, 
      A1.CUST_CERT_STAR_DATE, A1.CUST_CERT_END_DATE, 
      A1.JOB_CODE, A1.JOB_NATURE, A1.JOB_KIND, A1.JOB_TITLE, 
      A1.MARRIAGE_STATUS, A1.IS_PARENT, A1.ANNUAL_INCOME, 
      A1.COUNTRY_CODE, A1.RELIGION_CODE, A1.NATION_CODE, 
      A1.DRIVER_LICENSE_TYPE, A1.COMPANY_NAME, A1.OFFEN_USE_TEL, 
      A1.HOUSE_TEL, A1.FAX_TEL, A1.OFFICE_TEL, A1.MOBILE_TEL, 
      A1.EMAIL, A1.QQ, A1.WECHAT_NO, A1.OTHER, A1.CUSTOMER_LEVEL, 
      A1.CUSTOMER_RISK_LEVEL, A1.CUSTOMER_VIP, A1.SMOKING_FLAG, 
      A1.DRUNK_FLAG, A1.BLACKLIST_FLAG, A1.HOUSEKEEPER_FLAG, 
      A1.SYN_MDM_FLAG, A1.LIVE_STATUS, A1.RETIRED_FLAG, A1.DEATH_DATE, 
      A1.HEALTH_STATUS, A1.REMARK, A1.CUST_PWD,  
      A1.GENDER_DESC, A1.TYPE_NAME, A1.MARRIAGE, A1.COUNTRY_NAME, A1.TYPE, A1.LICENSE_DESC, 
      A1.CATEGORY_JOB_DESC, A1.KIND_JOB_DESC, 
	  D.NEXT_ACCOUNT, D.NEXT_ACCOUNT_NAME, D.NEXT_ACCOUNT_BANK, D.NEXT_ACCOUNT_ID, D.PAY_NEXT, 
      N.NAME, P.ADDRESS, P.POST_CODE, P.STATE, P.CITY, P.DISTRICT,
      QSTATE.NAME AS STATENAME, QCITY.NAME AS CITYNAME, QDISTRICT.NAME AS DISTRICTNAME ,A1.JOB_UNDERWRITE,
       ( select sheng.name||shi.name||xian.name
      from dev_pas.t_district sheng
      left join dev_pas.t_district shi
        on shi.parent_code = sheng.code
      left join dev_pas.t_district xian
        on xian.parent_code = shi.code
     where xian.code =P.district ) AS SSX
      FROM
      ( SELECT B.POLICY_ID, B.ADDRESS_ID, A.CUSTOMER_ID, A.UN_CUSTOMER_CODE, 
      A.OLD_CUSTOMER_ID, A.MARRIAGE_DATE, A.EDUCATION, 
      A.CUSTOMER_NAME, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_GENDER, 
      A.CUSTOMER_HEIGHT, A.CUSTOMER_WEIGHT, A.CUSTOMER_CERT_TYPE, 
      A.CUSTOMER_CERTI_CODE, A.CUSTOMER_ID_CODE, 
      A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
      A.JOB_CODE, A.JOB_NATURE, A.JOB_KIND, A.JOB_TITLE, 
      A.MARRIAGE_STATUS, A.IS_PARENT, A.ANNUAL_INCOME, 
      A.COUNTRY_CODE, A.RELIGION_CODE, A.NATION_CODE, 
      A.DRIVER_LICENSE_TYPE, A.COMPANY_NAME, A.OFFEN_USE_TEL, 
      A.HOUSE_TEL, A.FAX_TEL, A.OFFICE_TEL, A.MOBILE_TEL, 
      A.EMAIL, A.QQ, A.WECHAT_NO, A.OTHER, A.CUSTOMER_LEVEL, 
      A.CUSTOMER_RISK_LEVEL, A.CUSTOMER_VIP, A.SMOKING_FLAG, 
      A.DRUNK_FLAG, A.BLACKLIST_FLAG, A.HOUSEKEEPER_FLAG, 
      A.SYN_MDM_FLAG, A.LIVE_STATUS, A.RETIRED_FLAG, A.DEATH_DATE, 
      A.HEALTH_STATUS, A.REMARK, A.CUST_PWD,  
      E.GENDER_DESC, F.TYPE_NAME, G.MARRIAGE, H.COUNTRY_NAME, J.TYPE, K.LICENSE_DESC, 
      L.JOB_CATEGORY_NAME AS CATEGORY_JOB_DESC, M.JOB_DESC AS KIND_JOB_DESC ,B.JOB_UNDERWRITE
      FROM APP___PAS__DBUSER.T_POLICY_HOLDER B, APP___PAS__DBUSER.T_CUSTOMER A
      LEFT JOIN APP___PAS__DBUSER.T_GENDER E ON E.GENDER_CODE = A.CUSTOMER_GENDER 
      LEFT JOIN APP___PAS__DBUSER.T_YES_NO F ON F.YES_NO = A.CUSTOMER_VIP 
      LEFT JOIN APP___PAS__DBUSER.T_MARRIAGE G ON G.MARRIAGE_CODE = A.MARRIAGE_STATUS 
      LEFT JOIN APP___PAS__DBUSER.T_COUNTRY H ON H.COUNTRY_CODE = A.COUNTRY_CODE 
      LEFT JOIN APP___PAS__DBUSER.T_CERTI_TYPE J ON J.CODE = A.CUSTOMER_CERT_TYPE 
      LEFT JOIN APP___PAS__DBUSER.T_LICENSE_TYPE K ON K.LICENSE_TYPE = A.DRIVER_LICENSE_TYPE 
      LEFT JOIN APP___PAS__DBUSER.T_JOB_CATEGORY L ON L.JOB_CATEGORY_CODE = A.JOB_CODE 
      LEFT JOIN APP___PAS__DBUSER.T_JOB_KIND M ON M.JOB_KIND = A.JOB_KIND
      WHERE 
      A.CUSTOMER_ID = B.CUSTOMER_ID 
      AND B.POLICY_CODE =  #{policy_code} ) A1
      LEFT JOIN APP___PAS__DBUSER.T_PAYER_ACCOUNT D ON D.POLICY_ID = A1.POLICY_ID
      LEFT JOIN APP___PAS__DBUSER.T_PAY_MODE N ON N.CODE = D.PAY_NEXT
      LEFT JOIN APP___PAS__DBUSER.T_ADDRESS P ON P.ADDRESS_ID = A1.ADDRESS_ID     
      LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QSTATE ON QSTATE.CODE = P.STATE
      LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QCITY ON QCITY.CODE = P.CITY
      LEFT JOIN APP___PAS__DBUSER.T_DISTRICT QDISTRICT ON QDISTRICT.CODE = P.DISTRICT 
      LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER TCRO ON TCRO.POLICY_ID = A1.POLICY_ID]]>
	</select>
	<!--by zhaoyoan_wb 根据保单号和客户姓名查询客户id(被保人或投保人) -->
	<select id="JRQD_findInsuredOrHolderCustomerIdByPolicyCodeAndName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,A.CUSTOMER_ID,B.CUSTOMER_NAME,A.POLICY_ID
			FROM APP___PAS__DBUSER.T_INSURED_LIST A LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER B ON A.CUSTOMER_ID=B.CUSTOMER_ID
			WHERE A.POLICY_CODE=#{policy_code}
			AND B.CUSTOMER_NAME=#{customer_name} 
      		union
      		SELECT C.POLICY_CODE,C.CUSTOMER_ID,D.CUSTOMER_NAME,C.POLICY_ID
			FROM APP___PAS__DBUSER.T_POLICY_HOLDER C LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER D ON C.CUSTOMER_ID=D.CUSTOMER_ID
			WHERE C.POLICY_CODE=#{policy_code} 
			AND D.CUSTOMER_NAME=#{customer_name} 
		]]>	
	</select>
	<!--保单投保人信息AC变更成功次数  -->
	<select id="JRQD_findACSuccessCountBypolicyCodeAndcustomerId"  resultType="java.lang.Integer" parameterType="java.util.Map">
<![CDATA[
	 SELECT COUNT(1) AS ACSuccessCount
   FROM APP___PAS__DBUSER.T_POLICY_CHANGE D
  WHERE  D.POLICY_ID IN
        (SELECT A.POLICY_ID
           FROM 
                APP___PAS__DBUSER.T_POLICY_HOLDER_LOG A,
                APP___PAS__DBUSER.T_POLICY_CHANGE    B,
                APP___PAS__DBUSER.T_CS_CUSTOMER      C
          WHERE A.POLICY_CHG_ID = B.POLICY_CHG_ID
            AND A.CUSTOMER_ID = C.CUSTOMER_ID
            AND A.POLICY_CODE=#{policy_code}
            AND B.SERVICE_CODE='CC' AND C.OLD_NEW=1
            
          )
]]>	
	</select>
	<!--  -->
	<select id="JRQD_findHolderCustomerIdByPolicyCodeAndName_cjk" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[
	 SELECT C.POLICY_CODE,C.CUSTOMER_ID,D.CUSTOMER_NAME,C.POLICY_ID
      FROM APP___PAS__DBUSER.T_POLICY_HOLDER C LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER D ON C.CUSTOMER_ID=D.CUSTOMER_ID
      WHERE 1=1
      ]]>
      <include refid="JRQD_findHolderCustomerorinsuredCustomerIdByPolicyCodeAndName_cjk" />
     	
       union 
     <![CDATA[  
       select C.POLICY_CODE,C.CUSTOMER_ID,D.CUSTOMER_NAME,C.POLICY_ID
       from APP___PAS__DBUSER.T_INSURED_LIST C LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER D ON C.CUSTOMER_ID=D.CUSTOMER_ID
       WHERE 1=1
        ]]>
       <include refid="JRQD_findHolderCustomerorinsuredCustomerIdByPolicyCodeAndName_cjk" />
   
	</select>
    
   
    <!-- 根据policyId查询保单投保人与代理人信息 -->
    <select id="JRQD_findPolicyHolderAndAgentInfo" resultType="java.util.Map" parameterType="java.util.Map" >
    <![CDATA[
   select c.customer_id         as customer_id,
       c.customer_name       as customer_name,
       c.customer_gender     as customer_gender,
       c.company_name        as company_name,
       c.customer_cert_type  as customer_cert_type,
       c.customer_certi_code as customer_certi_code,
       c.customer_id_code    as customer_id_code,
       c.mobile_tel          as mobile_tel,
       c.house_tel           as house_tel,
       a.agent_organ_code    as agent_organ_code,
       a.agent_code          as agent_code,
       t.agent_name          as agent_name,
       a.last_agent_code     as last_agent_code,
       a.last_agent_name     as last_agent_name,
       a.agent_start_date    as agent_start_date,
       a.relation_to_ph      as relation_to_ph

    from APP___PAS__DBUSER.t_customer c, APP___PAS__DBUSER.t_policy_holder h, APP___PAS__DBUSER.T_CONTRACT_AGENT a,
  		APP___PAS__DBUSER.t_agent t
 where h.policy_code = a.policy_code
   and h.customer_id = c.customer_id
   and a.agent_code = t.agent_code
   and h.policy_id = #{policy_id}
   and a.is_current_agent = #{is_current_agent}
      ]]>   
    </select>
    
    <!-- 根据policyId查询被保险人和续期付款方式信息 -->
    <select id="JRQD_findPolicyInsuredAndPayAcountInfo" resultType="java.util.Map" parameterType="java.util.Map" >
    <![CDATA[
    select c.customer_id       as customer_id,
       c.customer_name     as customer_name,
       c.mobile_tel        as mobile_tel,
       c.house_tel         as house_tel,
       i.relation_to_ph    as relation_to_ph,
       a.pay_location      as pay_location,
       a.next_account_bank as next_account_bank,
       a.next_account      as next_account,
       a.next_account_id   as next_account_id,
       c.customer_birthday  as customer_birthday

  from APP___PAS__DBUSER.t_customer c, APP___PAS__DBUSER.t_insured_list i, APP___PAS__DBUSER.T_PAYER_ACCOUNT a
 where i.policy_id = a.policy_id
   and i.customer_id = c.customer_id
   and i.policy_id = #{policy_id}
   and rownum = '1'
      ]]>   
    </select>
    
    <select id="JRQD_findPolicyHolderInfoForSendMessage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 
				CU.OFFEN_USE_TEL, CU.MOBILE_TEL,CU.CUSTOMER_NAME,CU.CUSTOMER_GENDER,PH.POLICY_CODE,NVL(PA.ACCOUNT,PA.NEXT_ACCOUNT) AS NEXT_ACCOUNT
				FROM  APP___PAS__DBUSER.T_CS_POLICY_HOLDER PH
				LEFT JOIN 
				APP___PAS__DBUSER.T_CUSTOMER CU
				ON PH.CUSTOMER_ID =  CU.CUSTOMER_ID
				LEFT JOIN 
				APP___PAS__DBUSER.T_PAYER_ACCOUNT PA
				ON PH.POLICY_ID = PA.POLICY_ID
				WHERE 
				PH.POLICY_CODE = #{policy_code} AND PH.OLD_NEW = '1'
		  ]]>
	</select>
	
	
	<select id="JRQD_findAllPolicyHolderNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT CM.POLICY_ID,
		       CM.POLICY_CODE,
		       CM.LIABILITY_STATE,
		       CM.VALIDATE_DATE,
		       CM.END_CAUSE,
		       CM.WINNING_START_FLAG,
		       PH.CUSTOMER_ID,
		       PA.ACKNOWLEDGE_DATE
		  FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH
		 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		    ON PH.POLICY_ID = CM.POLICY_ID
		 INNER JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT PA
		    ON PA.POLICY_ID = PH.POLICY_ID
		 WHERE 1=1 
		 ]]>
		 <if test=" customer_id  != null "><![CDATA[ AND PH.CUSTOMER_ID = ${customer_id} ]]></if>
		 <if test=" policy_code  != null "><![CDATA[ AND PH.POLICY_CODE = ${policy_code} ]]></if>
		 <![CDATA[ ORDER BY PH.POLICY_CODE ]]> 
	</select>
	
	<select id="JRQD_PA_findCustomerHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />
	</select>
	<select id="JRQD_PA_findCustomerList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />  
	</select>
	
	<select id="JRQD_queryHolderOtherPolicyCodeByCode" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT TCBP.BUSI_ITEM_ID,
       TCBP.BUSI_PRD_ID,
       TCBP.BUSI_PROD_CODE,
       TCBP.APPLY_CODE,
       TCBP.POLICY_ID,
       TCBP.POLICY_CODE,
       TCBP.MASTER_BUSI_ITEM_ID,
       TCBP.VALIDATE_DATE
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
       DEV_PDS.T_BUSINESS_PRODUCT   TBP
 WHERE 1 = 1
   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
   AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
  
   AND TBP.PRODUCT_CATEGORY2 = '30004'
      
   AND TCM.POLICY_CODE IN
       (SELECT T.POLICY_CODE
          FROM DEV_PAS.T_POLICY_HOLDER T
         WHERE T.CUSTOMER_ID IN
               (SELECT TPH.CUSTOMER_ID
                  FROM DEV_PAS.T_POLICY_HOLDER TPH
                 WHERE TPH.POLICY_CODE = #{policy_code}))
   AND TCM.POLICY_CODE != #{policy_code}
   AND TCM.RELATION_POLICY_CODE = #{policy_code}
	</select>
	
	<!-- 查询投保人信息 -->
	<select id="JRQD_findPolicyInfoByCustomerId" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[
	select a.customer_id,a.policy_id FROM  APP___PAS__DBUSER.T_POLICY_HOLDER A  where a.customer_id=#{customer_id}
	
	]]>
	</select>
	
	<!-- 通过客户信息查询出该客户作为投保人、被保人对应的所有保单 -->
	<select id="JRQD_PA_findPolicysByCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[ 
				select ph.policy_code
			     from APP___PAS__DBUSER.T_policy_holder          ph,
			          APP___PAS__DBUSER.t_Policy_Acknowledgement ack
			    where 1 = 1
			      and ack.policy_id = ph.policy_id
			      and ack.acknowledge_date is not null
		]]>
		<if test=" customer_id_list  != null and customer_id_list.size()!=0">
		    <![CDATA[ AND PH.CUSTOMER_ID  in ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
		 </if>
		 <![CDATA[  GROUP BY  ph.policy_code ]]>
		 
    	 <![CDATA[ union ]]>
    	 <![CDATA[
   		 		select il.policy_code
			     from APP___PAS__DBUSER.T_insured_list il,
			          APP___PAS__DBUSER.t_Policy_Acknowledgement ack
			    where 1 = 1
			      and il.policy_id = ack.policy_id 
			      and ack.acknowledge_date is not null
   		 ]]>
		
		 <if test=" customer_id_list  != null and customer_id_list.size()!=0">
		    <![CDATA[ AND il.CUSTOMER_ID  in ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
	 	 </if>
	    <![CDATA[  GROUP BY  il.policy_code ]]>
	
		
	</select>
	
	<select id="JRQD_PA_findPolicyHolderByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT  A.POLICY_ID,A.POLICY_CODE,A.CUSTOMER_ID, TC.CUSTOMER_NAME,A.APPLY_CODE,A.ADDRESS_ID, 
		TL.CUSTOMER_ID AS IN_CUSTOMER_ID ,TL.ADDRESS_ID as IN_ADDRESS_ID FROM DEV_PAS.T_POLICY_HOLDER A 
 JOIN DEV_PAS.T_INSURED_LIST TL ON A.POLICY_ID=TL.POLICY_ID JOIN DEV_PAS.T_CUSTOMER TC ON A.CUSTOMER_ID=TC.CUSTOMER_ID
WHERE   ROWNUM<1000 ]]>
		<include refid="JRQD_PA_policyHolderWhereCondition" />
	</select>
	
	<!-- 客户保单基本信息查询保单投保人信息sql -->
	<select id="JRQD_queryCusPlyPolicyHolderInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 
		        TC.CUSTOMER_NAME APPNTNAME,
				DECODE(TC.CUSTOMER_GENDER,'1','0','2','1','2') APPNTSEX,
				TC.CUSTOMER_CERT_TYPE APPNTIDTYPE,
				TC.CUSTOMER_CERTI_CODE APPNTIDCODE,
				TO_CHAR(TC.CUSTOMER_BIRTHDAY,'YYYY-MM-DD') APPNTBIRTHDAY,
				TO_CHAR(TC.CUST_CERT_STAR_DATE,'YYYY-MM-DD') APPNTIDEFFSTARTDATE,
				TO_CHAR(TC.CUST_CERT_END_DATE,'YYYY-MM-DD') APPNTIDEFFENDDATE,
				(SELECT TCO.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCO WHERE TCO.COUNTRY_CODE=TC.COUNTRY_CODE) APPNTNATIVEPLACE,
				(SELECT TJ.JOB_NAME FROM APP___PAS__DBUSER.T_JOB_CODE TJ WHERE TJ.JOB_CODE=TC.JOB_CODE) APPNTPOSITION,
				(SELECT TJ.JOB_CODE FROM APP___PAS__DBUSER.T_JOB_CODE TJ WHERE TJ.JOB_CODE=TC.JOB_CODE) APPNTPOSITIONCODE,
				TA.MOBILE_TEL APPNTMOBILE,
				TA.FIXED_TEL APPNTPHONE,
				(
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.STATE) ||
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.CITY)  ||
					(SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE=TA.DISTRICT)  ||	
					 TA.ADDRESS				
				) APPNTPOSTALADDRESS,
				TA.POST_CODE APPNTZIPCODE,
				NVL((SELECT DECODE(TCT.TAX_RESIDENT_TYPE,'','N','Y') FROM DEV_PAS.T_CUSTOMER_TAX TCT 
						WHERE  tct.customer_tax_name =tc.customer_name
				       and tct.customer_tax_birthday = tc.customer_birthday
				       and tct.customer_tax_cert_type = tc.customer_cert_type
				       and tct.customer_tax_certi_code = tc.customer_certi_code
				       and tct.customer_tax_gender = tc.customer_gender),'N') APPNTCRSFLAG,
				(SELECT TRT.RESIDENT_NAME FROM DEV_PAS.T_CUSTOMER_TAX TCT LEFT JOIN DEV_PAS.T_TAX_RESIDENT_TYPE TRT ON TCT.TAX_RESIDENT_TYPE = TRT.RESIDENT_CODE
						WHERE  tct.customer_tax_name =tc.customer_name
				       and tct.customer_tax_birthday = tc.customer_birthday
				       and tct.customer_tax_cert_type = tc.customer_cert_type
				       and tct.customer_tax_certi_code = tc.customer_certi_code
				       and tct.customer_tax_gender = tc.customer_gender) RESIDENTNAME
			FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,APP___PAS__DBUSER.T_CUSTOMER TC,APP___PAS__DBUSER.T_ADDRESS TA 
			WHERE TPH.CUSTOMER_ID=TC.CUSTOMER_ID AND TPH.ADDRESS_ID=TA.ADDRESS_ID
			AND TPH.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}
		]]>
	</select>
	<!-- 查询所有的投保人信息
	//#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求-受益人变更-保单查询接口
    //modify by cuiqi_wb
    //2021-11-23 -->
	<select id="JRQD_findHolderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	   SELECT A.POLICY_CODE,
            C.OLD_CUSTOMER_ID,
            C.CUSTOMER_ID,
            (SELECT Z.ORDER_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED Z 
            WHERE Z.ORDER_ID=1 AND Z.POLICY_CODE=A.POLICY_CODE 
            AND Z.INSURED_ID=A.LIST_ID AND ROWNUM = 1) AS INSUREDORDER,
            C.CUSTOMER_NAME,
            C.CUSTOMER_GENDER,
            C.COUNTRY_CODE,
            A.SOCI_SECU,            
            C.CUSTOMER_BIRTHDAY,
            C.CUSTOMER_CERT_TYPE,
            C.CUSTOMER_CERTI_CODE,
            C.CUST_CERT_STAR_DATE,
            C.CUST_CERT_END_DATE,
            B.MOBILE_TEL,
            B.FIXED_TEL,
            A.JOB_CODE,
            (select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = A.JOB_CODE) JOB_NAME,
            C.COMPANY_NAME,
            B.EMAIL,
            B.POST_CODE,
            B.STATE,
            B.CITY,
            B.DISTRICT,
            B.ADDRESS,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.STATE = O.CODE )  AS STATENAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.CITY = O.CODE )  AS CITYNAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.DISTRICT = O.CODE )  AS DISTRICTNAME,
            (SELECT O.COUNTRY_NAME FROM  APP___PAS__DBUSER.T_COUNTRY O WHERE O.COUNTRY_CODE = C.COUNTRY_CODE) COUNTRY_NAME,
            (SELECT O.TYPE FROM  APP___PAS__DBUSER.T_CERTI_TYPE O WHERE O.CODE = C.CUSTOMER_CERT_TYPE) CERT_TYPE_NAME

       FROM APP___PAS__DBUSER.T_POLICY_HOLDER A,
            APP___PAS__DBUSER.T_CUSTOMER      C,
            APP___PAS__DBUSER.T_ADDRESS       B
      WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
        AND A.ADDRESS_ID = B.ADDRESS_ID
        AND A.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}
	
	</select>
	<!-- 根据保单号查询保单信息 
	//#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求-贷款清偿-保单查询接口
    //modify by cuiqi_wb
    //2021-11-26-->
	<select id="JRQD_PA_findAppCustomerByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
			 SELECT TCU.CUSTOMER_ID,
			                TCU.CUSTOMER_NAME,
			                TCU.CUSTOMER_GENDER,
			                TCU.COUNTRY_CODE,
			                (SELECT TY.COUNTRY_NAME
			                   FROM APP___PAS__DBUSER.T_COUNTRY TY
			                  WHERE TY.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			                TCU.CUSTOMER_BIRTHDAY,
			                TCU.CUSTOMER_CERT_TYPE,
			                (SELECT TE.TYPE
			                   FROM APP___PAS__DBUSER.T_CERTI_TYPE TE
			                  WHERE TE.CODE = TCU.CUSTOMER_CERT_TYPE) TYPE_NAME,
			                TCU.CUSTOMER_CERTI_CODE,
			                TCU.CUST_CERT_STAR_DATE,
			                TCU.CUST_CERT_END_DATE,
			                TS.MOBILE_TEL,
			                TS.OFFICE_TEL OFFICETEL,
			                TS.FIXED_TEL FIXEDTEL,
			                TCU.OLD_CUSTOMER_ID,
			       	TPH.JOB_CODE,
            		(select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = TPH.JOB_CODE) JOB_NAME,
			                TCU.COMPANY_NAME,
			                (SELECT TT.NAME
			                   FROM APP___PAS__DBUSER.T_DISTRICT TT
			                  WHERE TT.CODE = TS.STATE) STATE,
			                (SELECT TT.NAME
			                   FROM APP___PAS__DBUSER.T_DISTRICT TT
			                  WHERE TT.CODE = TS.CITY) CITY,
			                (SELECT TT.NAME
			                   FROM APP___PAS__DBUSER.T_DISTRICT TT
			                  WHERE TT.CODE = TS.DISTRICT) DISTRICT,
			                TS.ADDRESS
			           FROM APP___PAS__DBUSER.T_POLICY_HOLDER TPH,
			                APP___PAS__DBUSER.T_CUSTOMER      TCU,
			                APP___PAS__DBUSER.T_ADDRESS       TS
			          WHERE TPH.POLICY_CODE = #{policy_code}
			            AND TCU.CUSTOMER_ID = TPH.CUSTOMER_ID
			            AND TS.ADDRESS_ID = TPH.ADDRESS_ID
	     ]]>
	</select>
	<!-- 根据客户id查询保单号 -->
	<select id="JRQD_PA_findCustomerContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,
                         A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1
			 AND A.CUSTOMER_ID = ${customer_id}
	    ]]>
	</select>
	<!-- 根据客户id查询保单号 -->
	<select id="JRQD_PA_findCustomerInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select A.POLICY_CODE policy_code,A.Customer_Id customer_id
			 from APP___PAS__DBUSER.T_INSURED_LIST A 
			 where A.Customer_Id = ${customer_id}
	    ]]>
	</select>
	
	
	<select id="JRQD_PA_findPolicyHolderAge" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select TRUNC(months_between(sysdate, cus.customer_birthday) / 12) AS holderAge 
			  from dev_pas.t_policy_holder ph, dev_pas.t_customer cus 
			 where cus.customer_id = ph.customer_id
			   and ph.policy_code =#{policy_code} 
	   	]]>
	</select>
	
	<select id="JRQD_PA_findPolicyHolderByCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT  distinct  PH.POLICY_CODE,PH.CUSTOMER_ID
      FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH,
           APP___PAS__DBUSER.T_CONTRACT_MASTER CM
     WHERE 1=1 
		       AND CM.LIABILITY_STATE IN ('1','4')
					 AND PH.CUSTOMER_ID = #{customer_id}
					 AND CM.VALIDATE_DATE >= TRUNC(sysdate, 'yyyy') 
					 AND CM.VALIDATE_DATE <= add_months(trunc(sysdate, 'yyyy'), 12) - 1
		 ]]>
	</select>
	
	<select id="JRQD_PA_findPolicyHolderByPolicyIdPaOrNb" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID FROM]]>
		<if test=" is_pas  != null "><![CDATA[ DEV_PAS.T_POLICY_HOLDER A]]></if> 
		<if test=" is_nb  != null "><![CDATA[ DEV_NB.T_NB_POLICY_HOLDER A]]></if> 
		<![CDATA[ WHERE A.POLICY_ID = #{policy_id}]]>
	</select>
</mapper> 
