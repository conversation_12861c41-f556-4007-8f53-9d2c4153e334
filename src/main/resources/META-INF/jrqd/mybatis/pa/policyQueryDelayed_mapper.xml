<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.pa.impl.interfaceforchannel.dao.IPolicyQueryDelayedDao">

	<!-- 若类型为空，则根据查询日期查询当天生效的保单 -->
	<select id="JRQD_PA_findDelayedPolicyByDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, ]]>
		<if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
		<![CDATA[	    tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' ]]>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.validate_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date == null and date_flag == 1 "><![CDATA[ and tcm.validate_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date == null and date_flag == 1 "><![CDATA[ and tcm.validate_date <= #{batch_end_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date != null and date_flag == 1 "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date != null and date_flag == 1 "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date == null and date_flag == 2 "><![CDATA[ and tcm.apply_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date == null and date_flag == 2 "><![CDATA[ and tcm.apply_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" busi_prod_code_list  != null and busi_prod_code_list.size() > 0 ">
			<![CDATA[ and tcbp.busi_prod_code in (]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tcm.input_type,
							tcm.suspend_date,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>

	<!-- 若类型为空，则根据查询日期查询当天生效的保单 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedPolicyByDateCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyByDateRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 若类型为空，则根据查询日期查询当天生效的保单 - 总数分页用 -->
	<select id="findDelayedPolicyByDateTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyByDateRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 若类型为空，则根据查询日期查询当天生效的保单 - 详细分页用 -->
	<select id="findDelayedPolicyByDateDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedPolicyByDateRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>

	<!-- 若类型为空，则根据查询日期查询当天生效的保单 - 查询范围 -->
	<sql id="JRQD_PA_findDelayedPolicyByDateRange">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, ]]>
		<if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
		<![CDATA[	    tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' ]]>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.validate_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date == null and date_flag == 1 "><![CDATA[ and tcm.validate_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date == null and date_flag == 1 "><![CDATA[ and tcm.validate_date <= #{batch_end_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date != null and date_flag == 1 "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date != null and date_flag == 1 "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' and expiry_date == null and date_flag == 2 "><![CDATA[ and tcm.apply_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' and expiry_date == null and date_flag == 2 "><![CDATA[ and tcm.apply_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" busi_prod_code_list  != null and busi_prod_code_list.size() > 0 ">
			<![CDATA[ and tcbp.busi_prod_code in (]]>
			<foreach collection="busi_prod_code_list" item="busi_prod_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{busi_prod_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tcm.input_type,
							tcm.suspend_date,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</sql>

	<select id="JRQD_PA_findDelayedPolicyByHesitationDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id,
				        '17' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
				   join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id  
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  
				    and tca.is_nb_agent = '1' 
				    and tcm.LIABILITY_STATE = 1     ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpa.ACKNOWLEDGE_DATE = #{batch_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and tpa.ACKNOWLEDGE_DATE >= #{batch_start_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and tpa.ACKNOWLEDGE_DATE <= #{batch_end_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" hesitation_period_day != null and hesitation_period_day != ''"><![CDATA[ and sysdate - tpa.ACKNOWLEDGE_DATE > #{hesitation_period_day} + 1 ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
						   tcm.SUBINPUT_TYPE,
						   tcm.SUBMIT_CHANNEL,
						   tcm.input_type,
						   tcm.suspend_date,
						   tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>

	<!-- 查询过犹豫期生效的保单 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedPolicyByHesitationDateCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyByHesitationDateRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 查询过犹豫期生效的保单 - 总数分页用 -->
	<select id="JRQD_PA_findDelayedPolicyByHesitationDateTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyByHesitationDateRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 查询过犹豫期生效的保单 - 详细分页用 -->
	<select id="JRQD_PA_findDelayedPolicyByHesitationDateDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedPolicyByHesitationDateRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>

	<!-- 查询过犹豫期生效的保单 - 查询范围 -->
	<sql id="JRQD_PA_findDelayedPolicyByHesitationDateRange">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id,
				        '17' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
				   join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id  
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  
				    and tca.is_nb_agent = '1' 
				    and tcm.LIABILITY_STATE = 1     ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpa.ACKNOWLEDGE_DATE = #{batch_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and tpa.ACKNOWLEDGE_DATE >= #{batch_start_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and tpa.ACKNOWLEDGE_DATE <= #{batch_end_date} - tcbp.hesitation_period_day ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" hesitation_period_day != null and hesitation_period_day != ''"><![CDATA[ and sysdate - tpa.ACKNOWLEDGE_DATE > #{hesitation_period_day} + 1 ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
						   tcm.SUBINPUT_TYPE,
						   tcm.SUBMIT_CHANNEL,
						   tcm.input_type,
						   tcm.suspend_date,
						   tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</sql>

	<select id="JRQD_PA_findDelayedPolicyBeforeHesitationDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id,
				        '27' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
				   join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id  
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  
				    and tca.is_nb_agent = '1' 
				    and tcm.LIABILITY_STATE = 1 ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and greatest(tpa.ACKNOWLEDGE_DATE, tcm.validate_date) = #{batch_date} and #{batch_date} < tpa.ACKNOWLEDGE_DATE + tcbp.hesitation_period_day ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and greatest(tpa.ACKNOWLEDGE_DATE, tcm.validate_date) >= #{batch_start_date} and #{batch_start_date} < tpa.ACKNOWLEDGE_DATE + tcbp.hesitation_period_day ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and greatest(tpa.ACKNOWLEDGE_DATE, tcm.validate_date) <= #{batch_end_date} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
						   tcm.SUBINPUT_TYPE,
						   tcm.SUBMIT_CHANNEL,
						   tcm.input_type,
						   tcm.suspend_date,
						   tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>

	<!-- 查询犹豫期 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedPolicyBeforeHesitationDateCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyBeforeHesitationDateRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 查询犹豫期 - 总数分页用 -->
	<select id="JRQD_PA_findDelayedPolicyBeforeHesitationDateTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyBeforeHesitationDateRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 查询犹豫期 - 详细分页用 -->
	<select id="JRQD_PA_findDelayedPolicyBeforeHesitationDateDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedPolicyBeforeHesitationDateRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>

	<!-- 查询犹豫期 - 查询范围 -->
	<sql id="JRQD_PA_findDelayedPolicyBeforeHesitationDateRange">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id,
				        '27' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
				   join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id  
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  
				    and tca.is_nb_agent = '1' 
				    and tcm.LIABILITY_STATE = 1 ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and greatest(tpa.ACKNOWLEDGE_DATE, tcm.validate_date) = #{batch_date} and #{batch_date} < tpa.ACKNOWLEDGE_DATE + tcbp.hesitation_period_day ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and greatest(tpa.ACKNOWLEDGE_DATE, tcm.validate_date) >= #{batch_start_date} and #{batch_start_date} < tpa.ACKNOWLEDGE_DATE + tcbp.hesitation_period_day ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and greatest(tpa.ACKNOWLEDGE_DATE, tcm.validate_date) <= #{batch_end_date} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
						   tcm.SUBINPUT_TYPE,
						   tcm.SUBMIT_CHANNEL,
						   tcm.input_type,
						   tcm.suspend_date,
						   tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</sql>

	<!-- 若类型不为空,则根据业务类型查询保单 -->
	<select id="JRQD_PA_findDelayedPolicyByType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, ]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[			   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tcm.input_type,
				       tcm.suspend_date,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       sum(ts.surrender_amount) surrender_amount,
				       sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa
				  from dev_pas.t_policy_change tpc
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_cs_accept_change tac
		          	on tac.accept_code = tpc.business_code
		          	and tac.accept_status = '18'
		          left join dev_pas.t_cs_application tcsa
		            on tcsa.change_id = tac.change_id
		          left join dev_pas.t_surrender ts
		            on ts.accept_code = tpc.business_code
		            and ts.change_id = tcsa.change_id
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1'  ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and trunc(tpc.validate_time) = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and trunc(tpc.validate_time) >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and trunc(tpc.validate_time) <= #{batch_end_date} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[		          tcm.validate_date,
				          tcm.liability_state,
					      tcm.apply_date,
					      tcm.issue_date,
					      tcm.SUBINPUT_TYPE,
						  tcm.SUBMIT_CHANNEL,
						  tcm.input_type,
						  tcm.suspend_date,
				          tca.CHANNEL_TYPE,
					      tpa.acknowledge_date,
				          tcsa.apply_name,
					      tcm.lapse_date,
					      tcm.expiry_date,
					      tcm.service_bank_branch,
					      tcm.end_cause,
					      tcsa.customer_id,
					      tcsa.service_type,
					      tcsa.bank_channel,
					      tpac.next_account,
					      tpc.business_code,
					      tpac.pay_next  ]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedPolicyByTypeCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyByTypeRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 总数分页用 -->
	<select id="JRQD_PA_findDelayedPolicyByTypeTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyByTypeRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 若类型不为空,则根据业务类型查询保单 - 详细分页用 -->
	<select id="JRQD_PA_findDelayedPolicyByTypeDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedPolicyByTypeRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 查询范围 -->
	<sql id="JRQD_PA_findDelayedPolicyByTypeRange">
		<![CDATA[
				select tcm.policy_code,
				       tcm.apply_code,
				       tcm.policy_id, ]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[			   tcm.validate_date,
				       tcm.liability_state,
				       tcm.lapse_date,
				       tcm.expiry_date,
				       tcm.apply_date,
				       tcm.issue_date,
				       tcm.end_cause,
				       tcm.SUBINPUT_TYPE,
				       tcm.SUBMIT_CHANNEL,
				       tcm.input_type,
				       tcm.suspend_date,
				       tca.CHANNEL_TYPE,
				       tpa.acknowledge_date,
				       min(ts.hesitate_flag) hesitate_flag,
				       sum(ts.surrender_amount) surrender_amount,
				       sum(ts.fee_amount) fee_amount,
				       tcsa.apply_name,
				       tcm.service_bank_branch,
				       (select max(tcbp.hesitation_period_day)
				          from dev_pas.t_contract_busi_prod tcbp
				         where tcm.policy_code = tcbp.policy_code) hesitation_period_day,
				       tcsa.customer_id,
				       tcsa.service_type,
				       tcsa.bank_channel,
				       (SELECT A.ORGAN_CODE 
				          FROM DEV_PAS.T_CONTRACT_AGENT A 
				         WHERE A.POLICY_CODE = TCM.POLICY_CODE 
				           AND A.IS_NB_AGENT = '1') AS organ_code ,
				       tpac.next_account,
				       tpac.pay_next,
				       tpc.business_code,
				       max(tpc.validate_time) validate_time,
				       (select sum(tcp.total_prem_af)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) total_prem_af,
				       (select sum(tcp.initial_amount)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) initial_amount,
				       (select sum(tcp.bonus_sa)
				          from dev_pas.t_contract_product tcp
				         where tcm.policy_code = tcp.policy_code) bonus_sa,
				       TP.POLICY_YEAR RENEWAL_NUM,
			           sum(tp.FEE_AMOUNT)RE_FEE_AMOUNT,
			           case when tp.pay_mode=10 then ''
			           else TP.bank_account end as bank_account,
                 		TP.finish_time renewal_date
				  from dev_pas.t_policy_change tpc
				  join dev_pas.t_contract_master tcm
				    on tcm.policy_id = tpc.policy_id
				  left join dev_pas.t_cs_accept_change tac
		          	on tac.accept_code = tpc.business_code
		          	and tac.accept_status = '18'
		          left join dev_pas.t_cs_application tcsa
		            on tcsa.change_id = tac.change_id
		          left join dev_pas.t_surrender ts
		            on ts.accept_code = tpc.business_code
		            and ts.change_id = tcsa.change_id
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
    				]]>
				<if test="policy_code_list!=null and policy_code_list.size()>0">
				<![CDATA[LEFT JOIN APP___PAS__DBUSER.V_PREM_ALL TP]]>
				</if>
				<if test="policy_code_list==null or policy_code_list.size()==0">
				<![CDATA[LEFT JOIN APP___PAS__DBUSER.T_PREM TP]]> 
				</if>
				<![CDATA[
             		ON TCM.POLICY_CODE = TP.POLICY_CODE
                     AND TP.BUSINESS_CODE = tpc.BUSINESS_CODE
				  where 1 = 1  and tca.is_nb_agent = '1'  ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and trunc(tpc.validate_time) = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and trunc(tpc.validate_time) >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and trunc(tpc.validate_time) <= #{batch_end_date} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 快捷保全只有CC -->
		<![CDATA[ and tpc.service_code<>'CC' and ( 1=1 ]]>
		<if test=" service_codes  != null and service_codes.size()!=0">
			<![CDATA[ and tpc.service_code in ( ]]>
			<foreach collection="service_codes" item="service_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{service_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" no_policyCodes  != null and no_policyCodes.size()!=0">
			<![CDATA[ and tcm.policy_code not in ( ]]>
			<foreach collection="no_policyCodes" item="no_policyCode"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{no_policyCode} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>

		<if test=" liability_state_end != null and liability_state_end != ''">
			<![CDATA[ and tcm.liability_state = #{liability_state_end} ]]>
		</if>
		<if test=" liability_state_ineffect != null and liability_state_ineffect != '' and lapse_cause_list != null and lapse_cause_list != ''">
			<![CDATA[ and (tcm.liability_state = #{liability_state_ineffect} ]]>
			<![CDATA[ and tcm.LAPSE_CAUSE in ( ]]>
			<foreach collection="lapse_cause_list" item="lapse_cause"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{lapse_cause} ]]>
			</foreach>
			<![CDATA[ )) ]]>
		</if>
		<![CDATA[ ) ]]>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]></if>
		<![CDATA[group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,]]>
		<if test=" liability_state_end == null or liability_state_end == ''">
			<![CDATA[ tpc.service_code, ]]>
		</if>
<![CDATA[		          tcm.validate_date,
				          tcm.liability_state,
					      tcm.apply_date,
					      tcm.issue_date,
					      tcm.SUBINPUT_TYPE,
						  tcm.SUBMIT_CHANNEL,
						  tcm.input_type,
						  tcm.suspend_date,
				          tca.CHANNEL_TYPE,
					      tpa.acknowledge_date,
				          tcsa.apply_name,
					      tcm.lapse_date,
					      tcm.expiry_date,
					      tcm.service_bank_branch,
					      tcm.end_cause,
					      tcsa.customer_id,
					      tcsa.service_type,
					      tcsa.bank_channel,
					      tpac.next_account,
					      tpc.business_code,
					      tpac.pay_next,
					      TP.finish_time,
                TP.bank_account ,
                 TP.POLICY_YEAR,
                 tp.pay_mode  ]]>
	</sql>

	<select id="JRQD_PA_findDelayedPolicyByServiceCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select distinct tcm.policy_code,
				                tcm.apply_code,
				                tcm.policy_id,
				                #{service_code} service_code,
				                tcm.validate_date,
				                tcm.liability_state,
				                tcm.lapse_date,
				                tcm.expiry_date,
				                tcm.apply_date,
				                tcm.issue_date,
				                tcm.end_cause,
				                tcm.SUBINPUT_TYPE,
				                tcm.SUBMIT_CHANNEL,
				                tcm.input_type,
				                tcm.suspend_date,
				                tca.CHANNEL_TYPE,
				                tpa.acknowledge_date,
				                tcm.service_bank_branch,
				                tpac.next_account,
				                tpac.pay_next,
				                tpar.finish_time validate_time,
				                sum(tcp.total_prem_af) total_prem_af,
				                sum(tcp.initial_amount) initial_amount,
				                sum(tcp.bonus_sa) bonus_sa
				  from dev_pas.t_contract_master tcm
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id
				  left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  left join dev_pas.t_prem_arap tpar
				    on tpar.policy_code = tcm.policy_code
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				 where 1 = 1 and tpar.fee_type in ('G003010000', 'G003100000') and tca.is_nb_agent = '1' ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpar.finish_time = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and tpar.finish_time >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and tpar.finish_time <= #{batch_end_date} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[
				 group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,
				          tcm.validate_date,
				          tcm.liability_state,
				          tcm.apply_date,
				          tcm.issue_date,
				          tcm.SUBINPUT_TYPE,
				          tcm.SUBMIT_CHANNEL,
				          tcm.input_type,
				          tcm.suspend_date,
				          tca.CHANNEL_TYPE,
				          tpa.acknowledge_date,
				          tcm.lapse_date,
				          tcm.expiry_date,
				          tcm.service_bank_branch,
				          tcm.end_cause,
				          tpar.finish_time,
				          tpac.next_account,
				          tpac.pay_next
				 order by validate_time]]>
	</select>

	<!-- 根据业务类型查询保单 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedPolicyByServiceCodeCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyByServiceCodeRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 根据业务类型查询保单 - 总数分页用 -->
	<select id="JRQD_PA_findDelayedPolicyByServiceCodeTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyByServiceCodeRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 根据业务类型查询保单 - 详细分页用 -->
	<select id="JRQD_PA_findDelayedPolicyByServiceCodeDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedPolicyByServiceCodeRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>

	<!-- 根据业务类型查询保单 - 查询范围 -->
	<sql id="JRQD_PA_findDelayedPolicyByServiceCodeRange">
		<![CDATA[
				select distinct tcm.policy_code,
				                tcm.apply_code,
				                tcm.policy_id,
				                #{service_code} service_code,
				                tcm.validate_date,
				                tcm.liability_state,
				                tcm.lapse_date,
				                tcm.expiry_date,
				                tcm.apply_date,
				                tcm.issue_date,
				                tcm.end_cause,
				                tcm.SUBINPUT_TYPE,
				                tcm.SUBMIT_CHANNEL,
				                tcm.input_type,
				                tcm.suspend_date,
				                tca.CHANNEL_TYPE,
				                tpa.acknowledge_date,
				                tcm.service_bank_branch,
				                tpac.next_account,
				                tpac.pay_next,
				                tpar.finish_time validate_time,
				                sum(tcp.total_prem_af) total_prem_af,
				                sum(tcp.initial_amount) initial_amount,
				                sum(tcp.bonus_sa) bonus_sa,
				                tpar.POLICY_YEAR RENEWAL_NUM,
			                    (SELECT sum(tp1.FEE_AMOUNT)
			                        FROM APP___PAS__DBUSER.V_PREM_ALL TP1
			                       WHERE TP1.UNIT_NUMBER = tpar.UNIT_NUMBER) RE_FEE_AMOUNT,
			                    case when tpar.pay_mode=10 then ''
                 else tpar.bank_account end as bank_account,
			                    tpar.finish_time renewal_date
				  from dev_pas.t_contract_master tcm
				  left join dev_pas.t_contract_busi_prod tcbp
				    on tcbp.policy_id = tcm.policy_id
				  left join dev_pas.t_contract_product tcp
				    on tcp.busi_item_id = tcbp.busi_item_id
				  left join dev_pas.t_policy_acknowledgement tpa
				    on tpa.policy_id = tcm.policy_id
				  left join dev_pas.t_payer_account tpac
				    on tpac.policy_id = tcm.policy_id
				  left join dev_pas.t_prem_arap tpar
				    on tpar.policy_code = tcm.policy_code
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				 where 1 = 1 and tpar.fee_type in ('G003010000', 'G003100000') and tca.is_nb_agent = '1' ]]>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" liability_state != null and liability_state != ''"><![CDATA[ and tcm.liability_state = #{liability_state} ]]></if>		   
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tpar.finish_time = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''"><![CDATA[ and tpar.finish_time >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''"><![CDATA[ and tpar.finish_time < #{batch_end_date}+1 ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[
				 group by tcm.policy_code,
				          tcm.apply_code,
				          tcm.policy_id,
				          tcm.validate_date,
				          tcm.liability_state,
				          tcm.apply_date,
				          tcm.issue_date,
				          tcm.SUBINPUT_TYPE,
				          tcm.SUBMIT_CHANNEL,
				          tcm.input_type,
				          tcm.suspend_date,
				          tca.CHANNEL_TYPE,
				          tpa.acknowledge_date,
				          tcm.lapse_date,
				          tcm.expiry_date,
				          tcm.service_bank_branch,
				          tcm.end_cause,
				          tpar.finish_time,
				          tpac.next_account,
				          tpac.pay_next,
				          tpar.bank_account,
				          tpar.UNIT_NUMBER,
				          tpar.POLICY_YEAR,
                  		  tpar.pay_mode
				 order by validate_time]]>
	</sql>

	<!-- 查询险种、责任组信息 -->
	<select id="JRQD_PA_findContractBusiProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		         select tbp.product_code_sys,
		                tbp.product_name_sys,
		                tbp.product_category,
		                tbp.PRODUCT_CATEGORY1,
		                tbp.PRODUCT_CATEGORY2,
		                tpbc2.CATEGORY_NAME AS category_name2,
		                tcp.coverage_period,
		                tcp.coverage_year,
		                tcp.charge_period,
		                tcp.charge_year,
		                tcp.bonus_mode,
		                tcp.prem_freq,
		                tcbp.maturity_date,
		                tcbp.renew,
		                tcbp.initial_validate_date,
		                tcpo.field1,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.unit) unit,
		                sum(tcp.std_prem_af) std_prem_af,
		                sum(tcp.total_prem_af) total_prem_af,
		                tbptc.busi_prod_type3_code
		           from APP___PAS__DBUSER.t_contract_busi_prod tcbp
		           join APP___PAS__DBUSER.t_contract_product tcp
		             on tcbp.busi_item_id = tcp.busi_item_id
		           join APP___PAS__DBUSER.t_business_product tbp
		             on tcbp.busi_prd_id = tbp.business_prd_id
		           left join  APP___PAS__DBUSER.t_Contract_Product_Other  tcpo
                     on tcpo.busi_item_id = tcbp.busi_item_id
                   left join APP___PDS__DBUSER.T_PROD_BIZ_CATEGORY tpbc2
                     on tbp.PRODUCT_CATEGORY2 = tpbc2.CATEGORY_ID
                   left join APP___PDS__DBUSER.T_BUSI_PROD_TYPE_CONFIG tbptc
                     on substr(tbp.PRODUCT_CODE_STD, 0, 3) = tbptc.BUSI_PROD_CODE
		          where 1 = 1
		            and tcbp.policy_code = #{policy_code}
		          group by tbp.product_code_sys,
		                   tbp.product_name_sys,
		                   tbp.product_category,
						   tbp.PRODUCT_CATEGORY1,
		                   tbp.PRODUCT_CATEGORY2,
		                   tpbc2.CATEGORY_NAME,
		                   tcp.coverage_period,
		                   tcp.coverage_year,
		                   tcp.charge_period,
		                   tcp.charge_year,
		                   tcp.bonus_mode,
		                   tcp.prem_freq,
		                   tcbp.hesitation_period_day,
		                   tcbp.maturity_date,
		                   tcbp.renew,
		                   tcbp.initial_validate_date,
		                   tcpo.field1,
		                   tbptc.busi_prod_type3_code
 				 order by tbp.product_category]]>
	</select>

	<!-- 查询上次缴费日 -->
	<select id="JRQD_PA_findContractDueTime" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			  SELECT tpa.policy_code,
			         SUM(TPA.FEE_AMOUNT) fee_amount,
			         max(TPA.DUE_TIME) DUE_TIME
			    FROM APP___CAP__DBUSER.T_PREM_ARAP TPA
			   WHERE TPA.FEE_TYPE IN ('G001010000','G001070100',
			   						  'G003100000',
			                          'G003010000',
			                          'G003020100',
			                          'G003020200',
			                          'G003030100',
			                          'G003030200',
			                          'G003040100',
			                          'G003040200')
			     AND TPA.FEE_STATUS = '01'
			     AND TPA.POLICY_CODE = #{policy_code} ]]>
		<if test=" due_time != null and due_time != ''"><![CDATA[ and TPA.DUE_TIME <= #{due_time} ]]></if>	     
		<![CDATA[   GROUP BY tpa.policy_code]]>
	</select>
	
	<!-- 查询首期缴费金额 -->
	<select id="JRQD_PA_findPolicyInitialPrem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			  SELECT tpa.policy_code,
			         SUM(TPA.FEE_AMOUNT) fee_amount
			    FROM APP___PAS__DBUSER.T_PREM TPA
			   WHERE TPA.FEE_SCENE_CODE='NB'
			     AND TPA.POLICY_CODE = #{policy_code} ]]>
		<if test=" due_time != null and due_time != ''"><![CDATA[ and TPA.DUE_TIME <= #{due_time} ]]></if>	     
		<![CDATA[   GROUP BY tpa.policy_code]]>
	</select>
	
	<!-- 查询终了红利金额 -->
	<select id="JRQD_PA_findTerminalBonus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
           select sum(tba.terminal_bonus) terminal_bonus
             from dev_pas.t_bonus_allocate tba
            where tba.policy_code = #{policy_code} ]]>
	</select>

	<!-- 交通银行做特殊处理(手工单承保、退保、续期缴费、保全等业务场景) -->
	<select id="JRQD_PA_findBCMDelayedPolicyByDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
           	SELECT TCM.POLICY_CODE,
			       TCM.SERVICE_BANK,
			       TCM.SERVICE_BANK_BRANCH,
			       TCM.SERVICE_HANDLER_CODE,
			       (SELECT MAX(TPA1.DUE_TIME)
			          FROM DEV_CAP.T_PREM_ARAP TPA1
			         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) DUE_TIME,
			       (SELECT MAX(TPA1.UNIT_NUMBER)
			          FROM DEV_CAP.T_PREM_ARAP TPA1
			         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UNIT_NUMBER,
			       (SELECT MAX(TPA1.UPDATE_TIME)
			          FROM DEV_CAP.T_PREM_ARAP TPA1
			         WHERE TPA1.POLICY_CODE = TCM.POLICY_CODE) UPDATE_TIME,
			       TA.STATE,
			       TA.CITY,
			       TA.DISTRICT,
			       TCBP.BUSI_PROD_CODE,
			       TBP.PRODUCT_NAME_STD,
			       TCE.PAY_DUE_DATE,
			       TCBP.HESITATION_PERIOD_DAY,
			       (SELECT SUM(TCP1.STD_PREM_AF)
			          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
			         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
			           AND TCP1.PREM_FREQ <> '1'
			           AND TCP1.PRODUCT_CODE IN
			               (SELECT DISTINCT TPL.INTERNAL_ID
			                  FROM DEV_PDS.T_PRODUCT_LIFE TPL
			                  JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
			                    ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID)) STD_PREM_AF,
			       CASE
			         WHEN TCP.PREM_FREQ = '5' THEN
			          TO_CHAR(TCP.PAIDUP_DATE, 'YYYY') -
			          TO_CHAR(TCP.VALIDATE_DATE, 'YYYY') - 1
			         WHEN TCP.PREM_FREQ = '1' THEN
			          TCP.CHARGE_YEAR - 1
			       END TOTAL_PERIOD,
			       (SELECT SUM(TCP1.TOTAL_PREM_AF)
			          FROM DEV_PAS.T_CONTRACT_PRODUCT TCP1
			         WHERE TCP1.POLICY_CODE = TCM.POLICY_CODE
			           AND TCP1.PRODUCT_CODE IN
			               (SELECT DISTINCT TPL.INTERNAL_ID
			                  FROM DEV_PDS.T_PRODUCT_LIFE TPL
			                  JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP1
			                    ON TPL.BUSINESS_PRD_ID = TBP1.BUSINESS_PRD_ID)) TOTAL_PREM_AF,
			       TCE.POLICY_PERIOD, 
			      (SELECT CASE WHEN TPA.ACCOUNT_BANK = '12' THEN  TPA.ACCOUNT
                           ELSE TPA.NEXT_ACCOUNT  END         
                      FROM DEV_PAS.T_PAYER_ACCOUNT TPA
                     WHERE TPA.POLICY_ID = TCM.POLICY_ID
                       AND ROWNUM = 1) NEXT_ACCOUNT  /*交通银行取首期帐号*/
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM
			  JOIN DEV_PAS.T_POLICY_HOLDER TPH
			    ON TCM.POLICY_CODE = TPH.POLICY_CODE
			  JOIN DEV_PAS.T_CUSTOMER TC
			    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
			  JOIN DEV_PAS.T_ADDRESS TA
			    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
			  JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
			    ON TCM.POLICY_ID = TCBP.POLICY_ID
			  JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
			    ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
			  JOIN DEV_PAS.T_CONTRACT_PRODUCT TCP
			    ON TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
			  JOIN DEV_PAS.T_CONTRACT_EXTEND TCE
			    ON TCE.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			 WHERE 1 = 1
			   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
               AND TCM.POLICY_CODE = #{policy_code} ]]>
	</select>

	<!-- 查询理赔金额 -->
	<select id="JRQD_PA_findPolicyClaimPrem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 SELECT tpa.policy_code, SUM(TPA.FEE_AMOUNT) fee_amount
			   FROM APP___CAP__DBUSER.T_PREM_ARAP TPA
			  WHERE 1 = 1
			    AND TPA.DERIV_TYPE = '005'
			    AND TPA.FEE_STATUS = '01'
			    AND TPA.POLICY_CODE = #{policy_code} ]]>
		<![CDATA[  GROUP BY tpa.policy_code]]>
	</select>

	<!-- 补退费金额 -->
	<select id="JRQD_PA_findPolicyPayPrem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 SELECT tpa.policy_code, SUM(TPA.FEE_AMOUNT) fee_amount
			   FROM APP___CAP__DBUSER.T_PREM_ARAP TPA
			  WHERE 1 = 1
			    AND TPA.BUSINESS_CODE = #{business_code} ]]>
		<![CDATA[  GROUP BY tpa.policy_code]]>
	</select>
	
	<!-- 查询缴费信息 -->
	<select id="JRQD_PA_findDelayedPayInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT T.POLICY_ID,
			   T.LIABILITY_STATE,
			   T.POLICY_CODE,
			   T.input_type,
		       T.suspend_date,
		       T.PAY_TYPE,
		       SUM(T.FEE_AMOUNT) FEE_AMOUNT,
		       '22' SERVICE_CODE,
		       T.PAY_MODE,
		       T.BANK_CODE,
		       T.BANK_ACCOUNT,
		       T.FINISH_TIME,
		       T.RENEWAL_COUNT,
		       T.RENEWAL_PAY_AMOUNT,
		       T.TOTAL_PAY_COUNT,
		       T.TOTAL_PAY_AMOUNT
		  FROM (SELECT TPA.POLICY_CODE,
					   TCM.POLICY_ID,
					   TCM.LIABILITY_STATE,
					   TCM.input_type,
		               TCM.suspend_date,
		               CASE
		                 WHEN TPA.FEE_SCENE_CODE = 'NB' THEN
		                  0
		                 WHEN TPA.FEE_SCENE_CODE = 'RN' THEN
		                  1
		                 WHEN TPA.FEE_SCENE_CODE = 'CS' AND TPA.SERVICE_CODE = 'AM' THEN
		                  2
		               END PAY_TYPE,
		               TPA.FEE_AMOUNT,
		               TPA.PAY_MODE,
		               TPA.BANK_CODE,
		               TPA.BANK_ACCOUNT,
		               TPA.FINISH_TIME,
		               (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
		               (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   OR (TP1.FEE_SCENE_CODE = 'CS' AND TP1.SERVICE_CODE = 'AM'))
		                   AND TP1.FEE_STATUS IN ('01','16','19')) TOTAL_PAY_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   OR (TP1.FEE_SCENE_CODE = 'CS' AND TP1.SERVICE_CODE = 'AM'))
		                   AND TP1.FEE_STATUS IN ('01','16','19')) TOTAL_PAY_AMOUNT
		          FROM APP___PAS__DBUSER.T_PREM TPA
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
		            ON TPA.POLICY_CODE = TCM.POLICY_CODE
				  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    				 ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TPA.ARAP_FLAG = '1' AND TCA.IS_NB_AGENT = '1' AND TPA.FEE_STATUS IN ('01','16','19') ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and trunc(tpa.finish_time) >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and trunc(tpa.finish_time) <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
								  T.input_type,
								  T.suspend_date,
						          T.PAY_TYPE,
						          T.PAY_MODE,
						          T.BANK_CODE,
						          T.BANK_ACCOUNT,
						          T.FINISH_TIME,
						          T.RENEWAL_COUNT,
						          T.RENEWAL_PAY_AMOUNT,
						          T.TOTAL_PAY_COUNT,
						          T.TOTAL_PAY_AMOUNT]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedPayInfoCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPayInfoRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 总数分页用 -->
	<select id="JRQD_PA_findDelayedPayInfoTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPayInfoRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 若类型不为空,则根据业务类型查询保单 - 详细分页用 -->
	<select id="JRQD_PA_findDelayedPayInfoDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedPayInfoRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>
	
	<!-- 查询缴费信息 - 查询范围 -->
	<sql id="JRQD_PA_findDelayedPayInfoRange">
		<![CDATA[
		SELECT T.POLICY_ID,
			   T.LIABILITY_STATE,
			   T.POLICY_CODE,
			   T.input_type,
		       T.suspend_date,
		       T.PAY_TYPE,
		       SUM(T.FEE_AMOUNT) FEE_AMOUNT,
		       '22' SERVICE_CODE,
		       T.PAY_MODE,
		       T.BANK_CODE,
		       T.BANK_ACCOUNT,
		       T.FINISH_TIME,
		       T.RENEWAL_COUNT,
		       T.RENEWAL_PAY_AMOUNT,
		       T.TOTAL_PAY_COUNT,
		       T.TOTAL_PAY_AMOUNT
		  FROM (SELECT TPA.POLICY_CODE,
					   TCM.POLICY_ID,
					   TCM.LIABILITY_STATE,
					   TCM.input_type,
		               TCM.suspend_date,
		               CASE
		                 WHEN TPA.FEE_SCENE_CODE = 'NB' THEN
		                  0
		                 WHEN TPA.FEE_SCENE_CODE = 'RN' THEN
		                  1
		                 WHEN TPA.FEE_SCENE_CODE = 'CS' AND TPA.SERVICE_CODE = 'AM' THEN
		                  2
		               END PAY_TYPE,
		               TPA.FEE_AMOUNT,
		               TPA.PAY_MODE,
		               TPA.BANK_CODE,
		               TPA.BANK_ACCOUNT,
		               TPA.FINISH_TIME,
		               (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   AND TP1.FEE_STATUS IN ('01','16','19')) RENEWAL_PAY_AMOUNT,
		               (SELECT COUNT(DISTINCT TP1.UNIT_NUMBER)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   OR (TP1.FEE_SCENE_CODE = 'CS' AND TP1.SERVICE_CODE = 'AM'))
		                   AND TP1.FEE_STATUS IN ('01','16','19')) TOTAL_PAY_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND (TP1.FEE_SCENE_CODE IN ('NB', 'RN')
		                   OR (TP1.FEE_SCENE_CODE = 'CS' AND TP1.SERVICE_CODE = 'AM'))
		                   AND TP1.FEE_STATUS IN ('01','16','19')) TOTAL_PAY_AMOUNT
		          FROM APP___PAS__DBUSER.T_PREM TPA
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
		            ON TPA.POLICY_CODE = TCM.POLICY_CODE
				  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    				 ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TPA.ARAP_FLAG = '1' AND TCA.IS_NB_AGENT = '1' AND TPA.FEE_STATUS IN ('01','16','19') ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and trunc(tpa.finish_time) >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and trunc(tpa.finish_time) <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
								  T.input_type,
								  T.suspend_date,
						          T.PAY_TYPE,
						          T.PAY_MODE,
						          T.BANK_CODE,
						          T.BANK_ACCOUNT,
						          T.FINISH_TIME,
						          T.RENEWAL_COUNT,
						          T.RENEWAL_PAY_AMOUNT,
						          T.TOTAL_PAY_COUNT,
						          T.TOTAL_PAY_AMOUNT]]>
	</sql>
	
	<!-- 查询保单状态信息 -->
	<select id="JRQD_PA_findDelayedPolicyState" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT T.POLICY_ID,
			   T.LIABILITY_STATE,
			   T.POLICY_CODE,
			   T.input_type,
		       T.suspend_date,
		       T.PAY_TYPE,
		       SUM(T.FEE_AMOUNT) FEE_AMOUNT,
		       '22' SERVICE_CODE,
		       T.PAY_MODE,
		       T.BANK_CODE,
		       T.BANK_ACCOUNT,
		       T.FINISH_TIME,
		       T.RENEWAL_COUNT,
		       T.RENEWAL_PAY_AMOUNT,
		       T.TOTAL_PAY_COUNT,
		       T.TOTAL_PAY_AMOUNT
		  FROM (SELECT TPA.POLICY_CODE,
					   TCM.POLICY_ID,
					   TCM.LIABILITY_STATE,
					   TCM.input_type,
		               TCM.suspend_date,
		               CASE
		                 WHEN TPA.FEE_SCENE_CODE = 'NB' THEN
		                  0
		                 WHEN TPA.FEE_SCENE_CODE = 'RN' THEN
		                  1
		                 WHEN TPA.FEE_SCENE_CODE = 'CS' THEN
		                  2
		               END PAY_TYPE,
		               TPA.FEE_AMOUNT,
		               TPA.PAY_MODE,
		               TPA.BANK_CODE,
		               TPA.BANK_ACCOUNT,
		               TPA.FINISH_TIME,
		               (SELECT COUNT(DISTINCT TP1.FINISH_TIME)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')) RENEWAL_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')) RENEWAL_PAY_AMOUNT,
		               (SELECT COUNT(DISTINCT TP1.FINISH_TIME)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN', 'CS')) TOTAL_PAY_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN', 'CS')) TOTAL_PAY_AMOUNT
		          FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE PLC
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM--
		            ON PLC.POLICY_ID=TCM.POLICY_ID
		          JOIN APP___PAS__DBUSER.T_PREM TPA
		            ON TPA.POLICY_CODE = TCM.POLICY_CODE
				  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    				 ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TPA.ARAP_FLAG = '1' AND TCA.IS_NB_AGENT = '1' ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and trunc(PLC.CHANGE_DATE) >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and trunc(PLC.CHANGE_DATE) <= #{batch_end_date} ]]></if>
		<!--  
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		-->
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 
		<if test=" submit_channel_list  != null and submit_channel_list.size()>0">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		 -->
		<if test="policy_code_list!=null and policy_code_list.size()>0">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
								  T.input_type,
								  T.suspend_date,
						          T.PAY_TYPE,
						          T.PAY_MODE,
						          T.BANK_CODE,
						          T.BANK_ACCOUNT,
						          T.FINISH_TIME,
						          T.RENEWAL_COUNT,
						          T.RENEWAL_PAY_AMOUNT,
						          T.TOTAL_PAY_COUNT,
						          T.TOTAL_PAY_AMOUNT]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedPolicyStateCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyStateRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 总数分页用 -->
	<select id="JRQD_PA_findDelayedPolicyStateTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedPolicyStateRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 若类型不为空,则根据业务类型查询保单 - 详细分页用 -->
	<select id="JRQD_PA_findDelayedPolicyStateDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedPolicyStateRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>
	
	<!-- 查询保单状态信息 - 查询范围 -->
	<sql id="JRQD_PA_findDelayedPolicyStateRange">
		<![CDATA[
		SELECT T.POLICY_ID,
			   T.LIABILITY_STATE,
			   T.POLICY_CODE,
			   T.input_type,
		       T.suspend_date,
		       T.PAY_TYPE,
		       SUM(T.FEE_AMOUNT) FEE_AMOUNT,
		       '22' SERVICE_CODE,
		       T.PAY_MODE,
		       T.BANK_CODE,
		       T.BANK_ACCOUNT,
		       T.FINISH_TIME,
		       T.RENEWAL_COUNT,
		       T.RENEWAL_PAY_AMOUNT,
		       T.TOTAL_PAY_COUNT,
		       T.TOTAL_PAY_AMOUNT
		  FROM (SELECT TPA.POLICY_CODE,
					   TCM.POLICY_ID,
					   TCM.LIABILITY_STATE,
					   TCM.input_type,
		               TCM.suspend_date,
		               CASE
		                 WHEN TPA.FEE_SCENE_CODE = 'NB' THEN
		                  0
		                 WHEN TPA.FEE_SCENE_CODE = 'RN' THEN
		                  1
		                 WHEN TPA.FEE_SCENE_CODE = 'CS' THEN
		                  2
		               END PAY_TYPE,
		               TPA.FEE_AMOUNT,
		               TPA.PAY_MODE,
		               TPA.BANK_CODE,
		               TPA.BANK_ACCOUNT,
		               TPA.FINISH_TIME,
		               (SELECT COUNT(DISTINCT TP1.FINISH_TIME)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')) RENEWAL_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN')) RENEWAL_PAY_AMOUNT,
		               (SELECT COUNT(DISTINCT TP1.FINISH_TIME)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN', 'CS')) TOTAL_PAY_COUNT,
		               (SELECT SUM(TP1.FEE_AMOUNT)
		                  FROM APP___PAS__DBUSER.T_PREM TP1
		                 WHERE TP1.POLICY_CODE = TCM.POLICY_CODE
		                   AND TP1.FEE_SCENE_CODE IN ('NB', 'RN', 'CS')) TOTAL_PAY_AMOUNT
		          FROM APP___PAS__DBUSER.T_PRODUCT_LIABILITY_CHANGE PLC
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM--
		            ON PLC.POLICY_ID=TCM.POLICY_ID
		          JOIN APP___PAS__DBUSER.T_PREM TPA
		            ON TPA.POLICY_CODE = TCM.POLICY_CODE
				  JOIN DEV_PAS.T_CONTRACT_AGENT TCA
    				 ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TPA.ARAP_FLAG = '1' AND TCA.IS_NB_AGENT = '1' ]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and trunc(PLC.CHANGE_DATE) >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and trunc(PLC.CHANGE_DATE) <= #{batch_end_date} ]]></if>
		<!--  
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		-->
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<!-- 
		<if test=" submit_channel_list  != null and submit_channel_list.size()>0">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		 -->
		<if test="policy_code_list!=null and policy_code_list.size()>0">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
								  T.input_type,
								  T.suspend_date,
						          T.PAY_TYPE,
						          T.PAY_MODE,
						          T.BANK_CODE,
						          T.BANK_ACCOUNT,
						          T.FINISH_TIME,
						          T.RENEWAL_COUNT,
						          T.RENEWAL_PAY_AMOUNT,
						          T.TOTAL_PAY_COUNT,
						          T.TOTAL_PAY_AMOUNT]]>
	</sql>
	
	
	<!-- 查询万能险账户变动信息-->
	<select id="JRQD_PA_findDelayedFundTransChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT T.POLICY_ID,
		       T.LIABILITY_STATE,
		       T.POLICY_CODE,
		       T.input_type,
		       T.suspend_date
		  FROM (SELECT TCM.POLICY_CODE,
		               TCM.POLICY_ID,
		               TCM.LIABILITY_STATE,
		               TCM.input_type,
		               TCM.suspend_date
		          FROM APP___PAS__DBUSER.T_CONTRACT_INVEST TCI
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
		            ON TCI.POLICY_ID= TCM.POLICY_ID
		          JOIN DEV_PAS.T_CONTRACT_AGENT            TCA
		            ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TCA.IS_NB_AGENT = '1' 
		           AND EXISTS (SELECT 'x' FROM APP___PAS__DBUSER.T_FUND_TRANS  TFT 
		                                 WHERE TFT.POLICY_ID = TCI.POLICY_ID
		           ]]>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and TFT.DEAL_TIME >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and TFT.DEAL_TIME <= #{batch_end_date} ]]></if>
		           <![CDATA[ )]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		 
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size>0">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	
		<if test=" policy_code_list  != null and policy_code_list.size()>0">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
								  T.input_type,
								  T.suspend_date
						         ]]>
	</select>

	<!-- 查询万能险账户变动信息 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedFundTransChangeCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedFundTransChangeRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 查询万能险账户变动信息 - 总数分页用 -->
	<select id="JRQD_PA_findDelayedFundTransChangeTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedFundTransChangeRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 查询万能险账户变动信息 - 详细分页用 -->
	<select id="JRQD_PA_findDelayedFundTransChangeDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedFundTransChangeRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>
	
	<!-- 查询万能险账户变动信息 - 查询范围-->
	<sql id="JRQD_PA_findDelayedFundTransChangeRange">
		<![CDATA[
		SELECT T.POLICY_ID,
		       T.LIABILITY_STATE,
		       T.POLICY_CODE,
		       T.input_type,
		       T.suspend_date
		  FROM (SELECT TCM.POLICY_CODE,
		               TCM.POLICY_ID,
		               TCM.LIABILITY_STATE,
		               TCM.input_type,
		               TCM.suspend_date
		          FROM APP___PAS__DBUSER.T_CONTRACT_INVEST TCI
		          JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER TCM
		            ON TCI.POLICY_ID= TCM.POLICY_ID
		          JOIN DEV_PAS.T_CONTRACT_AGENT            TCA
		            ON TCM.POLICY_CODE = TCA.POLICY_CODE
		         WHERE 1 = 1
		           AND TCA.IS_NB_AGENT = '1' 
		           AND EXISTS (SELECT 'x' FROM APP___PAS__DBUSER.T_FUND_TRANS  TFT 
		                                 WHERE TFT.POLICY_ID = TCI.POLICY_ID
		           ]]>
		<if test=" batch_start_date != null and batch_start_date != '' "><![CDATA[ and TFT.DEAL_TIME >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != '' "><![CDATA[ and TFT.DEAL_TIME <= #{batch_end_date} ]]></if>
		           <![CDATA[ )]]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		 
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size>0">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	
		<if test=" policy_code_list  != null and policy_code_list.size()>0">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		
		<![CDATA[  	) T  GROUP BY T.POLICY_ID,
								  T.POLICY_CODE,
								  T.LIABILITY_STATE,
								  T.input_type,
								  T.suspend_date
						         ]]>
	</sql>
	
	<!-- 查询退保信息 -->
	<select id="JRQD_PA_findSurrenderInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	select tcbp.policy_code,
	       sum(ts.surrender_amount) surrender_amount,
	       min(ts.hesitate_flag) hesitate_flag
	  from dev_pas.t_surrender ts
	  join dev_pas.t_contract_busi_prod tcbp
	    on ts.busi_item_id = tcbp.busi_item_id
	 where tcbp.liability_state = '3'
	   and tcbp.end_cause = '03'
	   and tcbp.policy_code = #{policy_code}
	 group by tcbp.policy_code
	</select>	

	<!-- 查询退保信息 - 查询总数用 -->
	<select id="JRQD_PA_findSurrenderInfoCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findSurrenderInfoRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 查询退保信息 - 总数分页用 -->
	<select id="JRQD_PA_findSurrenderInfoTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findSurrenderInfoRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 查询退保信息 - 详细分页用 -->
	<select id="JRQD_PA_findSurrenderInfoDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findSurrenderInfoRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>
	
	<!-- 查询退保信息 - 查询范围 -->
	<sql id="JRQD_PA_findSurrenderInfoRange">
	select tcbp.policy_code,
	       sum(ts.surrender_amount) surrender_amount,
	       min(ts.hesitate_flag) hesitate_flag
	  from dev_pas.t_surrender ts
	  join dev_pas.t_contract_busi_prod tcbp
	    on ts.busi_item_id = tcbp.busi_item_id
	 where tcbp.liability_state = '3'
	   and tcbp.end_cause = '03'
	   and tcbp.policy_code = #{policy_code}
	 group by tcbp.policy_code
	</sql>	
	
	<!-- 查询开门红撤单 -->
	<select id="JRQD_PA_findDelayedCancellation" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, ]]>
		<if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
		<![CDATA[	    tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' and tcm.liability_state='3' and tcm.end_cause='15']]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.expiry_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tcm.input_type,
							tcm.suspend_date,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 查询总数用 -->
	<select id="JRQD_PA_findDelayedCancellationCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDelayedCancellationRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 总数分页用 -->
	<select id="JRQD_PA_findDelayedCancellationTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDelayedCancellationRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 若类型不为空,则根据业务类型查询保单 - 详细分页用 -->
	<select id="JRQD_PA_findDelayedCancellationDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDelayedCancellationRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>
	
	<!-- 查询开门红撤单 - 查询范围-->
	<sql id="JRQD_PA_findDelayedCancellationRange">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id, ]]>
		<if test=" query_type != null and query_type != ''"><![CDATA[ #{query_type} service_code, ]]></if>
		<![CDATA[	    tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' and tcm.liability_state='3' and tcm.end_cause='15']]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.expiry_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tcm.input_type,
							tcm.suspend_date,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</sql>

	<!-- 查询险种、责任组信息 -->
	<select id="JRQD_NB_findContractBusiProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			select tbp.product_code_sys,
			       tbp.product_name_sys,
			       tbp.product_category,
			       tbp.PRODUCT_CATEGORY1,
			       tbp.PRODUCT_CATEGORY2,
			       tpbc2.CATEGORY_NAME AS category_name2,
			       tcp.coverage_period,
			       tcp.coverage_year,
			       tcp.charge_period,
			       tcp.charge_year,
			       tcp.prem_freq,
			       tcbp.renew,
			       tcpo.field1,
			       sum(tcp.unit) unit,
			       sum(tcp.std_prem_af) std_prem_af,
			       sum(tcp.total_prem_af) total_prem_af,
			       tbptc.busi_prod_type3_code
			  from APP___NB__DBUSER.t_NB_contract_busi_prod tcbp
			  join APP___NB__DBUSER.t_NB_contract_product tcp
			    on tcbp.busi_item_id = tcp.busi_item_id
			  join APP___NB__DBUSER.t_business_product tbp
			    on tcbp.busi_prd_id = tbp.business_prd_id
			  left join APP___NB__DBUSER.t_NB_Contract_Product_Other tcpo
			    on tcpo.busi_item_id = tcbp.busi_item_id
			  left join APP___PDS__DBUSER.T_PROD_BIZ_CATEGORY tpbc2
			    on tbp.PRODUCT_CATEGORY2 = tpbc2.CATEGORY_ID
			  left join APP___PDS__DBUSER.T_BUSI_PROD_TYPE_CONFIG tbptc
			    on substr(tbp.PRODUCT_CODE_STD, 0, 3) = tbptc.BUSI_PROD_CODE
			 where 1 = 1
			   and tcbp.policy_code = #{policy_code}
			 group by tbp.product_code_sys,
			          tbp.product_name_sys,
			          tbp.product_category,
			          tbp.PRODUCT_CATEGORY1,
			          tbp.PRODUCT_CATEGORY2,
			          tpbc2.CATEGORY_NAME,
			          tcp.coverage_period,
			          tcp.coverage_year,
			          tcp.charge_period,
			          tcp.charge_year,
			          tcp.prem_freq,
			          tcbp.hesitation_period_day,
			          tcbp.renew,
			          tcpo.field1,
			          tbptc.busi_prod_type3_code
			 order by tbp.product_category]]>
	</select>
	
	<!-- 查询当日撤单 -->
	<select id="JRQD_PA_findDrcd" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id,
				        '28' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' and tcm.liability_state='3' and tcm.end_cause='80']]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.expiry_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tcm.input_type,
							tcm.suspend_date,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 查询总数用 -->
	<select id="JRQD_PA_findDrcdCount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) QUERY_COUNT FROM (
		]]>
		<include refid="JRQD_PA_findDrcdRange" />
		<![CDATA[
			   )
		]]>
	</select>

	<!-- 若类型不为空,则根据业务类型查询保单 - 总数分页用 -->
	<select id="JRQD_PA_findDrcdTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM (
		]]>
		<include refid="JRQD_PA_findDrcdRange" />
		<![CDATA[
			   )
		]]>
	</select>
	
	<!-- 若类型不为空,则根据业务类型查询保单 - 详细分页用 -->
	<select id="JRQD_PA_findDrcdDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
SELECT B.*
  FROM (SELECT A.*, ROWNUM AS RN
          FROM (
        ]]>
		<include refid="JRQD_PA_findDrcdRange" />
		<![CDATA[
          ) A
         WHERE ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
        ]]>
	</select>
	
	<!-- 查询当日撤单 - 查询范围 -->
	<sql id="JRQD_PA_findDrcdRange">
		<![CDATA[
				 select tcm.policy_code,
				        tcm.apply_code,
				        tcm.policy_id,
				        '28' service_code,
				        tcm.validate_date,
				        tcm.liability_state,
				        tcm.lapse_date,
				        tcm.expiry_date,
				        tcm.apply_date,
				        tcm.issue_date,
				        tcm.end_cause,
						tcm.SUBINPUT_TYPE,
						tcm.SUBMIT_CHANNEL,
						tcm.input_type,
						tcm.suspend_date,
						tca.CHANNEL_TYPE,
				        tpa.acknowledge_date,
				        tcm.service_bank_branch,
				        tpac.next_account,
				        tpac.pay_next,
				        sum(tcp.total_prem_af) total_prem_af,
		                sum(tcp.initial_amount) initial_amount,
		                sum(tcp.bonus_sa) bonus_sa
				   from dev_pas.t_contract_master tcm
				   join dev_pas.t_contract_product tcp
				     on tcp.policy_code = tcm.policy_code
				   join dev_pas.t_contract_busi_prod tcbp
				     on tcbp.busi_item_id = tcp.busi_item_id
				   join dev_pas.t_policy_acknowledgement tpa
				     on tpa.policy_id = tcm.policy_id
			       join dev_pas.t_payer_account tpac
             		 on tpac.policy_id = tcm.policy_id
				  join dev_pas.t_contract_agent tca
    				 on tcm.policy_code = tca.policy_code
				  where 1 = 1  and tca.is_nb_agent = '1' and tcm.liability_state='3' and tcm.end_cause='80']]>
		<if test=" service_bank != null and service_bank != ''"><![CDATA[ and trim(tcm.service_bank) = #{service_bank} ]]></if>
		<if test=" batch_date != null and batch_date != ''"><![CDATA[ and tcm.expiry_date = #{batch_date} ]]></if>
		<if test=" batch_start_date != null and batch_start_date != ''  "><![CDATA[ and tcm.expiry_date >= #{batch_start_date} ]]></if>
		<if test=" batch_end_date != null and batch_end_date != ''  "><![CDATA[ and tcm.expiry_date <= #{batch_end_date} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''"><![CDATA[ and tcm.submit_channel = #{submit_channel} ]]></if>
		<if test=" input_type != null and input_type != ''"><![CDATA[ and tcm.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ and tca.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" policy_code_list  != null and policy_code_list.size() > 0 ">
			<![CDATA[ and tcm.policy_code in (]]>
			<foreach collection="policy_code_list" item="policy_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" apply_code_list  != null and apply_code_list.size() > 0 ">
			<![CDATA[ and tcm.apply_code in (]]>
			<foreach collection="apply_code_list" item="apply_code"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{apply_code} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" bank_sale_channel_list  != null and bank_sale_channel_list.size() > 0 ">
			<![CDATA[ and tcm.input_type in (]]>
			<foreach collection="bank_sale_channel_list" item="bank_sale_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{bank_sale_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" submit_channel_list  != null and submit_channel_list.size() > 0 ">
			<![CDATA[ and tcm.submit_channel in (]]>
			<foreach collection="submit_channel_list" item="submit_channel"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{submit_channel} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[ group by tcm.policy_code,
				           tcm.apply_code,
				           tcm.policy_id,
				           tcm.validate_date,
				           tcm.liability_state,
				           tcm.lapse_date,
				           tcm.expiry_date,
				           tcm.apply_date,
					       tcm.issue_date,
					       tcm.end_cause,
							tcm.SUBINPUT_TYPE,
							tcm.SUBMIT_CHANNEL,
							tcm.input_type,
							tcm.suspend_date,
							tca.CHANNEL_TYPE,
					       tpa.acknowledge_date,
					       tpac.next_account,
					       tpac.pay_next,
					       tcm.service_bank_branch]]>
	</sql>
		
</mapper>
