<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="customer">

	<sql id="JRQD_PA_customerWhereCondition">
		<!-- <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME like '%${customer_name}%' ]]></if> -->
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if> 
		<if test=" customer_level != null and customer_level != ''  "><![CDATA[ AND A.CUSTOMER_LEVEL = #{customer_level} ]]></if>
		<if test=" nation_code != null and nation_code != ''  "><![CDATA[ AND A.NATION_CODE = #{nation_code} ]]></if>
		<if test=" death_date  != null  and  death_date  != ''  "><![CDATA[ AND A.DEATH_DATE = #{death_date} ]]></if>
		<if test=" job_nature != null and job_nature != ''  "><![CDATA[ AND A.JOB_NATURE = #{job_nature} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" cust_pwd != null and cust_pwd != ''  "><![CDATA[ AND A.CUST_PWD = #{cust_pwd} ]]></if>
		<if test=" wechat_no != null and wechat_no != ''  "><![CDATA[ AND A.WECHAT_NO = #{wechat_no} ]]></if>
		<if test=" offen_use_tel != null and offen_use_tel != ''  "><![CDATA[ AND A.OFFEN_USE_TEL = #{offen_use_tel} ]]></if>
		<if test=" un_customer_code != null and un_customer_code != ''  "><![CDATA[ AND A.UN_CUSTOMER_CODE = #{un_customer_code} ]]></if>
		<if test=" cust_cert_end_date  != null  and  cust_cert_end_date  != ''  "><![CDATA[ AND A.CUST_CERT_END_DATE = #{cust_cert_end_date} ]]></if>
		<if test=" qq != null and qq != ''  "><![CDATA[ AND A.QQ = #{qq} ]]></if>
		<if test=" OLD_CUSTOMER_ID != null and OLD_CUSTOMER_ID != ''  "><![CDATA[ AND A.OLD_CUSTOMER_ID = #{OLD_CUSTOMER_ID} ]]></if>
		<if test=" old_customer_id != null and old_customer_id != ''  "><![CDATA[ AND A.OLD_CUSTOMER_ID = #{old_customer_id} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND to_char(A.CUSTOMER_BIRTHDAY,'yyyy-MM-dd') = to_char(#{customer_birthday},'yyyy-MM-dd') ]]></if>
		<if test=" is_parent  != null "><![CDATA[ AND A.IS_PARENT = #{is_parent} ]]></if>
		<if test=" country_code != null and country_code != ''  "><![CDATA[ AND A.COUNTRY_CODE = #{country_code} ]]></if>
		<if test=" fax_tel != null and fax_tel != ''  "><![CDATA[ AND A.FAX_TEL = #{fax_tel} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND A.OFFICE_TEL = #{office_tel} ]]></if>
		<if test=" smoking_flag  != null "><![CDATA[ AND A.SMOKING_FLAG = #{smoking_flag} ]]></if>
		<if test=" marriage_status != null and marriage_status != ''  "><![CDATA[ AND A.MARRIAGE_STATUS = #{marriage_status} ]]></if>
		<if test=" education != null and education != ''  "><![CDATA[ AND A.EDUCATION = #{education} ]]></if>
		<if test=" customer_id_code != null and customer_id_code != ''  "><![CDATA[ AND A.CUSTOMER_ID_CODE = #{customer_id_code} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" other != null and other != ''  "><![CDATA[ AND A.OTHER = #{other} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
		<if test=" job_title != null and job_title != ''  "><![CDATA[ AND A.JOB_TITLE = #{job_title} ]]></if>
		<if test=" customer_id  != null and customer_id !=''  "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" health_status != null and health_status != ''  "><![CDATA[ AND A.HEALTH_STATUS = #{health_status} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" retired_flag  != null "><![CDATA[ AND A.RETIRED_FLAG = #{retired_flag} ]]></if>
		<if test=" drunk_flag  != null "><![CDATA[ AND A.DRUNK_FLAG = #{drunk_flag} ]]></if>
		<if test=" marriage_date  != null  and  marriage_date  != ''  "><![CDATA[ AND A.MARRIAGE_DATE = #{marriage_date} ]]></if>
		<if test=" blacklist_flag  != null "><![CDATA[ AND A.BLACKLIST_FLAG = #{blacklist_flag} ]]></if>
		<if test=" driver_license_type != null and driver_license_type != ''  "><![CDATA[ AND A.DRIVER_LICENSE_TYPE = #{driver_license_type} ]]></if>
		<if test=" housekeeper_flag  != null "><![CDATA[ AND A.HOUSEKEEPER_FLAG = #{housekeeper_flag} ]]></if>
		<if test=" email != null and email != ''  "><![CDATA[ AND A.EMAIL = #{email} ]]></if>
		<if test=" annual_income  != null "><![CDATA[ AND A.ANNUAL_INCOME = #{annual_income} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND trim(A.CUSTOMER_CERT_TYPE) = #{customer_cert_type} ]]></if>
		<if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
		<if test=" job_kind != null and job_kind != ''  "><![CDATA[ AND A.JOB_KIND = #{job_kind} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" religion_code  != null "><![CDATA[ AND A.RELIGION_CODE = #{religion_code} ]]></if>
		<if test=" cust_cert_star_date  != null  and  cust_cert_star_date  != ''  "><![CDATA[ AND A.CUST_CERT_STAR_DATE = #{cust_cert_star_date} ]]></if>
		<if test=" syn_mdm_flag  != null "><![CDATA[ AND A.SYN_MDM_FLAG = #{syn_mdm_flag} ]]></if>
		<if test=" customer_vip  != null "><![CDATA[ AND A.CUSTOMER_VIP = #{customer_vip} ]]></if>
		<if test=" live_status  != null "><![CDATA[ AND A.LIVE_STATUS = #{live_status} ]]></if>
		<if test=" comm_method != null and comm_method != ''  "><![CDATA[ AND A.COMM_METHOD = #{comm_method} ]]></if>
		<if test=" tax_resident_type != null and tax_resident_type != ''  "><![CDATA[ AND A.TAX_RESIDENT_TYPE = #{tax_resident_type} ]]></if>
		<if test=" rz_level != null and rz_level != ''  "><![CDATA[ AND A.RZ_LEVEL = #{rz_level} ]]></if>
	</sql>
	<sql id="JRQD_PA_customerInjuredWhereCondition">
<!-- 		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if> -->
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND A.customer_id = #{customer_id} ]]></if>
		<if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND W.VALIDATE_DATE >= #{validate_date} ]]></if>	
       <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND W.SUSPEND_DATE <= #{suspend_date} ]]></if>
       <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND W.LIABILITY_STATE = #{liability_state} ]]></if>
	</sql>
	
	<sql id="JRQD_PA_customerPolicyList">
 		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND A.customer_id = #{customer_id} ]]></if>
		<if test=" old_customer_id != null and old_customer_id != ''  "><![CDATA[ AND A.OLD_CUSTOMER_ID = #{old_customer_id} ]]></if>
	</sql>
	
	<sql id="JRQD_PA_customerOtherPolicyList">
 		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" old_customer_id != null and old_customer_id != ''  "><![CDATA[ AND A.OLD_CUSTOMER_ID <> #{old_customer_id} ]]></if>
	</sql>
<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_queryCustomerByCustomerIdCodeCondition">
		<if test=" customer_id_code != null and customer_id_code != '' "><![CDATA[ AND A.CUSTOMER_ID_CODE = #{customer_id_code} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryCustomerByCustomerNameCondition">
		<if test=" customer_name != null and customer_name != '' "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryCustomerByCustomerBirthdayCondition">
		<if test=" customer_birthday  != null "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryCustomerByCustomerGenderCondition">
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryCustomerByCustomerCertTypeCondition">
		<if test=" customer_cert_type != null and customer_cert_type != '' "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryCustomerByCustomerCertiCodeCondition">
		<if test=" customer_certi_code != null and customer_certi_code != '' "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryCustomerByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_PA_addCustomer"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="customer_id">
			SELECT APP___PAS__DBUSER.S_CUSTOMER.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CUSTOMER(
				CUSTOMER_NAME, CUSTOMER_LEVEL, NATION_CODE, DEATH_DATE, JOB_NATURE, REMARK, CUST_PWD, 
				WECHAT_NO, OFFEN_USE_TEL, UN_CUSTOMER_CODE, CUST_CERT_END_DATE, UPDATE_BY, QQ, OLD_CUSTOMER_ID, 
				MOBILE_TEL, CUSTOMER_BIRTHDAY, IS_PARENT, COUNTRY_CODE, FAX_TEL, UPDATE_TIME, JOB_CODE, 
				OFFICE_TEL, SMOKING_FLAG, MARRIAGE_STATUS, EDUCATION, CUSTOMER_ID_CODE, UPDATE_TIMESTAMP, INSERT_BY, 
				CUSTOMER_GENDER, OTHER, COMPANY_NAME, JOB_TITLE, CUSTOMER_ID, CUSTOMER_HEIGHT, HEALTH_STATUS, 
				CUSTOMER_CERTI_CODE, RETIRED_FLAG, INSERT_TIMESTAMP, DRUNK_FLAG, MARRIAGE_DATE, BLACKLIST_FLAG, DRIVER_LICENSE_TYPE, 
				HOUSEKEEPER_FLAG, EMAIL, ANNUAL_INCOME, CUSTOMER_CERT_TYPE, HOUSE_TEL, INSERT_TIME, JOB_KIND, 
				CUSTOMER_WEIGHT, RELIGION_CODE, CUST_CERT_STAR_DATE, SYN_MDM_FLAG, CUSTOMER_VIP, LIVE_STATUS ,COMM_METHOD,TAX_RESIDENT_TYPE,RZ_LEVEL,SECOND_CERT_TYPE,SECOND_CERTI_CODE) 
			VALUES (
				#{customer_name, jdbcType=VARCHAR}, #{customer_level, jdbcType=VARCHAR} , #{nation_code, jdbcType=VARCHAR} , #{death_date, jdbcType=DATE} , #{job_nature, jdbcType=VARCHAR} , #{remark, jdbcType=VARCHAR} , #{cust_pwd, jdbcType=VARCHAR} 
				, #{wechat_no, jdbcType=VARCHAR} , #{offen_use_tel, jdbcType=VARCHAR} , #{un_customer_code, jdbcType=VARCHAR} , #{cust_cert_end_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{qq, jdbcType=VARCHAR} , #{old_customer_id, jdbcType=VARCHAR} 
				, #{mobile_tel, jdbcType=VARCHAR} , #{customer_birthday, jdbcType=DATE} , #{is_parent, jdbcType=NUMERIC} , #{country_code, jdbcType=VARCHAR} , #{fax_tel, jdbcType=VARCHAR} , SYSDATE , #{job_code, jdbcType=VARCHAR} 
				, #{office_tel, jdbcType=VARCHAR} , #{smoking_flag, jdbcType=NUMERIC} , #{marriage_status, jdbcType=VARCHAR} , #{education, jdbcType=VARCHAR} , #{customer_id_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{customer_gender, jdbcType=NUMERIC} , #{other, jdbcType=VARCHAR} , #{company_name, jdbcType=VARCHAR} , #{job_title, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{customer_height, jdbcType=NUMERIC} , #{health_status, jdbcType=VARCHAR} 
				, #{customer_certi_code, jdbcType=VARCHAR} , #{retired_flag, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{drunk_flag, jdbcType=NUMERIC} , #{marriage_date, jdbcType=DATE} , #{blacklist_flag, jdbcType=NUMERIC} , #{driver_license_type, jdbcType=VARCHAR} 
				, #{housekeeper_flag, jdbcType=NUMERIC} , #{email, jdbcType=VARCHAR} , #{annual_income, jdbcType=NUMERIC} , #{customer_cert_type, jdbcType=VARCHAR} , #{house_tel, jdbcType=VARCHAR} , SYSDATE , #{job_kind, jdbcType=VARCHAR} 
				, #{customer_weight, jdbcType=NUMERIC} , #{religion_code, jdbcType=NUMERIC} , #{cust_cert_star_date, jdbcType=DATE} , #{syn_mdm_flag, jdbcType=NUMERIC} , #{customer_vip, jdbcType=NUMERIC} , #{live_status, jdbcType=NUMERIC} 
				,#{comm_method, jdbcType=VARCHAR},#{tax_resident_type, jdbcType=VARCHAR},#{rz_level, jdbcType=VARCHAR},#{second_cert_type, jdbcType=VARCHAR},#{second_certi_code, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteCustomer" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CUSTOMER WHERE  CUSTOMER_ID = #{customer_id}]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_PA_updateCustomer" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
			CUSTOMER_LEVEL = #{customer_level, jdbcType=VARCHAR} ,
			NATION_CODE = #{nation_code, jdbcType=VARCHAR} ,
		    DEATH_DATE = #{death_date, jdbcType=DATE} ,
			JOB_NATURE = #{job_nature, jdbcType=VARCHAR} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
			CUST_PWD = #{cust_pwd, jdbcType=VARCHAR} ,
			WECHAT_NO = #{wechat_no, jdbcType=VARCHAR} ,
			OFFEN_USE_TEL = #{offen_use_tel, jdbcType=VARCHAR} ,
			UN_CUSTOMER_CODE = #{un_customer_code, jdbcType=VARCHAR} ,
		    CUST_CERT_END_DATE = #{cust_cert_end_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			QQ = #{qq, jdbcType=VARCHAR} ,
			OLD_CUSTOMER_ID = #{old_customer_id, jdbcType=VARCHAR} ,
			MOBILE_TEL = #{mobile_tel, jdbcType=VARCHAR} ,
		    CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} ,
		    IS_PARENT = #{is_parent, jdbcType=NUMERIC} ,
			COUNTRY_CODE = #{country_code, jdbcType=VARCHAR} ,
			FAX_TEL = #{fax_tel, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			OFFICE_TEL = #{office_tel, jdbcType=VARCHAR} ,
		    SMOKING_FLAG = #{smoking_flag, jdbcType=NUMERIC} ,
			MARRIAGE_STATUS = #{marriage_status, jdbcType=VARCHAR} ,
			EDUCATION = #{education, jdbcType=VARCHAR} ,
			CUSTOMER_ID_CODE = #{customer_id_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
			OTHER = #{other, jdbcType=VARCHAR} ,
			COMPANY_NAME = #{company_name, jdbcType=VARCHAR} ,
			JOB_TITLE = #{job_title, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
			HEALTH_STATUS = #{health_status, jdbcType=VARCHAR} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
		    RETIRED_FLAG = #{retired_flag, jdbcType=NUMERIC} ,
		    DRUNK_FLAG = #{drunk_flag, jdbcType=NUMERIC} ,
		    MARRIAGE_DATE = #{marriage_date, jdbcType=DATE} ,
		    BLACKLIST_FLAG = #{blacklist_flag, jdbcType=NUMERIC} ,
			DRIVER_LICENSE_TYPE = #{driver_license_type, jdbcType=VARCHAR} ,
		    HOUSEKEEPER_FLAG = #{housekeeper_flag, jdbcType=NUMERIC} ,
			EMAIL = #{email, jdbcType=VARCHAR} ,
		    ANNUAL_INCOME = #{annual_income, jdbcType=NUMERIC} ,
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
			HOUSE_TEL = #{house_tel, jdbcType=VARCHAR} ,
			JOB_KIND = #{job_kind, jdbcType=VARCHAR} ,
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
		    RELIGION_CODE = #{religion_code, jdbcType=NUMERIC} ,
		    CUST_CERT_STAR_DATE = #{cust_cert_star_date, jdbcType=DATE} ,
		    SYN_MDM_FLAG = #{syn_mdm_flag, jdbcType=NUMERIC} ,
		    CUSTOMER_VIP = #{customer_vip, jdbcType=NUMERIC} ,
		    LIVE_STATUS = #{live_status, jdbcType=NUMERIC} ,
		    COMM_METHOD = #{comm_method, jdbcType=VARCHAR} ,
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
		    IS_SUBSCRIPTION_EMAIL = #{is_subscription_email, jdbcType=NUMERIC} ,
		    TAX_RESIDENT_TYPE = #{tax_resident_type,jdbcType=VARCHAR},
		    RZ_LEVEL = #{rz_level,jdbcType=VARCHAR},
		    SECOND_CERT_TYPE = #{second_cert_type, jdbcType=VARCHAR} ,
		    SECOND_CERTI_CODE = #{second_certi_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CUSTOMER_ID = #{customer_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findCustomerByCustomerIdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE ,A.SOCI_SECU FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryCustomerByCustomerIdCodeCondition" />
	</select>
	
	<select id="JRQD_PA_findCustomerByCustomerName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE,
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryCustomerByCustomerNameCondition" />
	</select>
	
	<select id="JRQD_PA_findCustomerByCustomerBirthday" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryCustomerByCustomerBirthdayCondition" />
	</select>
	
	<select id="JRQD_PA_findCustomerByCustomerGender" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryCustomerByCustomerGenderCondition" />
	</select>
	
	<select id="JRQD_PA_findCustomerByCustomerCertType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryCustomerByCustomerCertTypeCondition" />
	</select>
	
	<select id="JRQD_PA_findCustomerByCustomerCertiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryCustomerByCustomerCertiCodeCondition" />
	</select>

	<select id="JRQD_PA_findCustomerByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_ID,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.CUSTOMER_RISK_LEVEL,A.COMM_METHOD,A.SOCI_SECU,A.IS_SUBSCRIPTION_EMAIL,A.RESIDENT_TYPE,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryCustomerByCustomerIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  AND ROWNUM <=  1000  ]]>
			<if test="modnum != null and start != null"><![CDATA[AND MOD(A.CUSTOMER_ID , #{modnum}) = #{start}]]></if>
		<include refid="JRQD_PA_customerWhereCondition" />
	</select>
	
	<!-- 投保人/被保险人手机号码不能与本分公司三个有效客户（不含当前保单的客户）手机号码相同 PA_customerWhereCondition-->
	<select id="JRQD_PA_findCheckCutomerMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT A.CUSTOMER_CERTI_CODE,A.CUSTOMER_CERT_TYPE,A.CUSTOMER_BIRTHDAY,A.CUSTOMER_GENDER,A.CUSTOMER_NAME 
		   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,APP___PAS__DBUSER.T_POLICY_HOLDER B,APP___PAS__DBUSER.T_CUSTOMER A,APP___PAS__DBUSER.T_ADDRESS S,
		   APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,APP___PAS__DBUSER.T_BUSINESS_PRODUCT PRO  
           WHERE C.POLICY_ID=B.POLICY_ID AND A.CUSTOMER_ID=B.CUSTOMER_ID AND C.LIABILITY_STATE='1' AND S.ADDRESS_ID=B.ADDRESS_ID AND S.CUSTOMER_ID=B.CUSTOMER_ID
           AND CBP.POLICY_CODE = C.POLICY_CODE AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID AND CBP.MASTER_BUSI_ITEM_ID IS NULL AND PRO.COVER_PERIOD_TYPE = '0' 
           AND S.MOBILE_TEL = #{mobile_tel,jdbcType=VARCHAR} 
           AND A.CUSTOMER_ID NOT IN (SELECT Z.CUSTOMER_ID FROM APP___PAS__DBUSER.T_CUSTOMER Z WHERE 1=1]]><include refid="JRQD_PA_customerWhereCondition" /><![CDATA[)
           AND C.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}
           UNION
           SELECT A.CUSTOMER_CERTI_CODE,A.CUSTOMER_CERT_TYPE,A.CUSTOMER_BIRTHDAY,A.CUSTOMER_GENDER,A.CUSTOMER_NAME
           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,APP___PAS__DBUSER.T_INSURED_LIST B,APP___PAS__DBUSER.T_CUSTOMER A,APP___PAS__DBUSER.T_ADDRESS S,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,APP___PAS__DBUSER.T_BUSINESS_PRODUCT PRO
           WHERE C.POLICY_ID=B.POLICY_ID AND A.CUSTOMER_ID=B.CUSTOMER_ID AND C.LIABILITY_STATE='1' AND S.ADDRESS_ID=B.ADDRESS_ID AND S.CUSTOMER_ID=B.CUSTOMER_ID
           AND CBP.POLICY_CODE = C.POLICY_CODE AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID AND CBP.MASTER_BUSI_ITEM_ID IS NULL AND PRO.COVER_PERIOD_TYPE = '0'
           AND S.MOBILE_TEL = #{mobile_tel,jdbcType=VARCHAR}
           AND A.CUSTOMER_ID NOT IN (SELECT Z.CUSTOMER_ID FROM APP___PAS__DBUSER.T_CUSTOMER Z WHERE 1=1]]><include refid="JRQD_PA_customerWhereCondition" /><![CDATA[)
           AND C.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}) WHERE ROWNUM < 20]]>
	</select>
	<!-- 投保人/被保险人手机号码不能与本分公司三个有效客户（不含当前保单的客户）手机号码相同 -->
	<select id="JRQD_PA_findCheckCutomerHouseTe" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT A.CUSTOMER_CERTI_CODE,A.CUSTOMER_CERT_TYPE,A.CUSTOMER_BIRTHDAY,A.CUSTOMER_GENDER,A.CUSTOMER_NAME 
		   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,APP___PAS__DBUSER.T_POLICY_HOLDER B,APP___PAS__DBUSER.T_CUSTOMER A,APP___PAS__DBUSER.T_ADDRESS S,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
           WHERE C.POLICY_ID=B.POLICY_ID AND A.CUSTOMER_ID=B.CUSTOMER_ID AND C.LIABILITY_STATE='1' AND S.ADDRESS_ID=B.ADDRESS_ID AND S.CUSTOMER_ID=B.CUSTOMER_ID
           AND CBP.POLICY_CODE = C.POLICY_CODE AND CBP.MASTER_BUSI_ITEM_ID IS NULL
           AND S.HOUSE_TEL=#{house_tel,jdbcType=VARCHAR} AND A.CUSTOMER_ID != #{customer_id} AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT PRO 
           WHERE PRO.COVER_PERIOD_TYPE = '0' AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID)
           AND C.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}
           UNION
           SELECT A.CUSTOMER_CERTI_CODE,A.CUSTOMER_CERT_TYPE,A.CUSTOMER_BIRTHDAY,A.CUSTOMER_GENDER,A.CUSTOMER_NAME
           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,APP___PAS__DBUSER.T_INSURED_LIST B,APP___PAS__DBUSER.T_CUSTOMER A,APP___PAS__DBUSER.T_ADDRESS S,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
           WHERE C.POLICY_ID=B.POLICY_ID AND A.CUSTOMER_ID=B.CUSTOMER_ID AND C.LIABILITY_STATE='1' AND S.ADDRESS_ID=B.ADDRESS_ID AND S.CUSTOMER_ID=B.CUSTOMER_ID
           AND CBP.POLICY_CODE = C.POLICY_CODE AND CBP.MASTER_BUSI_ITEM_ID IS NULL
           AND S.HOUSE_TEL=#{house_tel,jdbcType=VARCHAR} AND A.CUSTOMER_ID != #{customer_id} AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT PRO 
           WHERE PRO.COVER_PERIOD_TYPE = '0' AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID)
           AND C.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}) WHERE ROWNUM < 20]]>
	</select>
	
	<!-- 投保人/被保险人手机号码不能与本分公司三个有效客户（不含当前保单的客户）手机号码相同 -->
	<select id="JRQD_PA_findCheckCutomerOfficeTel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT A.CUSTOMER_CERTI_CODE,A.CUSTOMER_CERT_TYPE,A.CUSTOMER_BIRTHDAY,A.CUSTOMER_GENDER,A.CUSTOMER_NAME 
		   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,APP___PAS__DBUSER.T_POLICY_HOLDER B,APP___PAS__DBUSER.T_CUSTOMER A,APP___PAS__DBUSER.T_ADDRESS S,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,APP___PAS__DBUSER.T_BUSINESS_PRODUCT PRO
           WHERE C.POLICY_ID=B.POLICY_ID AND A.CUSTOMER_ID=B.CUSTOMER_ID AND C.LIABILITY_STATE='1' AND S.ADDRESS_ID=B.ADDRESS_ID AND S.CUSTOMER_ID=B.CUSTOMER_ID
           AND CBP.POLICY_CODE = C.POLICY_CODE AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID AND CBP.MASTER_BUSI_ITEM_ID IS NULL AND PRO.COVER_PERIOD_TYPE = '0'
           AND S.FIXED_TEL=#{office_tel,jdbcType=VARCHAR} AND A.CUSTOMER_ID != #{customer_id}
           AND C.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}
           UNION
           SELECT A.CUSTOMER_CERTI_CODE,A.CUSTOMER_CERT_TYPE,A.CUSTOMER_BIRTHDAY,A.CUSTOMER_GENDER,A.CUSTOMER_NAME
           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,APP___PAS__DBUSER.T_INSURED_LIST B,APP___PAS__DBUSER.T_CUSTOMER A,APP___PAS__DBUSER.T_ADDRESS S,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,APP___PAS__DBUSER.T_BUSINESS_PRODUCT PRO
           WHERE C.POLICY_ID=B.POLICY_ID AND A.CUSTOMER_ID=B.CUSTOMER_ID AND C.LIABILITY_STATE='1' AND S.ADDRESS_ID=B.ADDRESS_ID AND S.CUSTOMER_ID=B.CUSTOMER_ID
           AND CBP.POLICY_CODE = C.POLICY_CODE AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID AND CBP.MASTER_BUSI_ITEM_ID IS NULL AND PRO.COVER_PERIOD_TYPE = '0'
           AND S.FIXED_TEL=#{office_tel,jdbcType=VARCHAR} AND A.CUSTOMER_ID != #{customer_id}
           AND C.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}) WHERE ROWNUM < 20]]>
	</select>
	
		<!-- 查询投保人电话信息保单层的 -->
	<select id="JRQD_PA_findPolicylayerHolderCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		      SELECT  A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			S.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			S.FIXED_TEL AS OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE,			 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, S.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
		    A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A 
		     LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER B ON A.CUSTOMER_ID=B.CUSTOMER_ID           
		     LEFT JOIN APP___PAS__DBUSER.T_ADDRESS S ON S.ADDRESS_ID=B.ADDRESS_ID
		     WHERE S.CUSTOMER_ID=B.CUSTOMER_ID 
		     AND A.CUSTOMER_ID=S.CUSTOMER_ID 
		     AND B.POLICY_CODE=#{policy_code,jdbcType=VARCHAR} AND A.CUSTOMER_ID=#{customer_id}
		]]>
	</select>
	
		<!-- 查询被保人电话信息保单层的 -->
	<select id="JRQD_PA_findPolicylayerInsuredCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT  A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			S.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			S.FIXED_TEL AS OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, S.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
		    A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A 
		     LEFT JOIN APP___PAS__DBUSER.T_INSURED_LIST B ON A.CUSTOMER_ID=B.CUSTOMER_ID           
		     LEFT JOIN APP___PAS__DBUSER.T_ADDRESS S ON S.ADDRESS_ID=B.ADDRESS_ID
		     WHERE S.CUSTOMER_ID=B.CUSTOMER_ID 
		     AND A.CUSTOMER_ID=S.CUSTOMER_ID 
		     AND B.POLICY_CODE=#{policy_code,jdbcType=VARCHAR} AND A.CUSTOMER_ID=#{customer_id}
		]]>
	</select>
<!-- 查询所有投保客户操作 -->
	<select id="JRQD_PA_findAllHolderCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A, APP___PAS__DBUSER.T_POLICY_HOLDER B WHERE A.CUSTOMER_ID = B.CUSTOMER_ID AND ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_customerWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_PA_findCustomerTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1 ]]>
		<include refid="JRQD_PA_customerWhereCondition" />
	</select>
	
<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryCustomerForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RZ_LEVEL, B.CUSTOMER_NAME, B.CUSTOMER_LEVEL, B.NATION_CODE, B.DEATH_DATE, B.JOB_NATURE, B.REMARK, B.CUST_PWD, 
			B.WECHAT_NO, B.OFFEN_USE_TEL, B.UN_CUSTOMER_CODE, B.CUST_CERT_END_DATE, B.QQ, B.OLD_CUSTOMER_ID, 
			B.MOBILE_TEL, B.CUSTOMER_BIRTHDAY, B.IS_PARENT, B.COUNTRY_CODE, B.FAX_TEL, B.JOB_CODE, 
			B.OFFICE_TEL, B.SMOKING_FLAG, B.MARRIAGE_STATUS, B.EDUCATION, B.CUSTOMER_ID_CODE, 
			B.CUSTOMER_GENDER, B.OTHER, B.COMPANY_NAME, B.JOB_TITLE, B.CUSTOMER_ID, B.CUSTOMER_HEIGHT, B.HEALTH_STATUS, 
			B.CUSTOMER_CERTI_CODE, B.RETIRED_FLAG, B.DRUNK_FLAG, B.MARRIAGE_DATE, B.BLACKLIST_FLAG, B.DRIVER_LICENSE_TYPE, 
			B.HOUSEKEEPER_FLAG, B.EMAIL, B.ANNUAL_INCOME, B.CUSTOMER_CERT_TYPE, B.HOUSE_TEL, B.JOB_KIND, 
			B.CUSTOMER_WEIGHT, B.RELIGION_CODE, B.CUST_CERT_STAR_DATE, B.SYN_MDM_FLAG, B.CUSTOMER_VIP, B.LIVE_STATUS FROM (
					SELECT ROWNUM RN, A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_PA_customerWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ORDER BY  B.CUSTOMER_BIRTHDAY DESC]]>
	</select>
<!-- 查询单条 -->
	<select id="JRQD_PA_findCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,A.RESIDENT_TYPE,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE,A.CUSTOMER_CERT_TYPE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1 and  rownum=1 and rownum < 1000  ]]>
		<!-- <include refid="JRQD_PA_queryCustomerByCustomerIdCondition" /> -->
		<include refid="JRQD_PA_queryCustomerByCustomerCertiCodeCondition" />
		<include refid="JRQD_PA_customerWhereCondition" />
	</select>
    <select id="JRQD_PA_findCustomerByTelPast" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A
			WHERE A.HOUSE_TEL = #{house_tel} or A.MOBILE_TEL = #{mobile_tel} order by A.CUSTOMER_ID ASC ]]>
			
	</select>
	
	 <select id="JRQD_PA_findCustomerByTel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A
			WHERE 1=1 ]]>
			<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
			<if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
			<![CDATA[ ORDER BY A.CUSTOMER_ID]]>
	</select>
	
	 <select id="JRQD_PA_findCustomerByMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A
			WHERE 1=1 ]]>
			<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
			<![CDATA[ ORDER BY A.CUSTOMER_ID]]>
	</select>
	
	 <select id="JRQD_PA_findCustomerByHouseTel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A
			WHERE 1=1 ]]>
			<if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
			<![CDATA[ ORDER BY A.CUSTOMER_ID]]>
	</select>
	
	
	
	
	<select id="JRQD_PA_findAllCustomerInjured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1=1]]>
		<include refid="JRQD_PA_customerInjuredWhereCondition" />
	</select>
	
	
	<!-- 根据业务员编号和生日区间查询客户列表 -->
	<select id="JRQD_PA_findAllCustomerByAgentCodeAndBirthDay" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	
		select d.policy_code,
		       (select (select product_abbr_name
		                  from APP___PAS__DBUSER.T_business_product where product_code_sys = g.busi_prod_code )
		          from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD g where g.policy_id = d.policy_id
		           and g.policy_code = d.policy_code and g.MASTER_BUSI_ITEM_ID is null) as riskname,
		       c.customer_name, c.customer_birthday, c.mobile_tel,
		       (select f.ADDRESS from APP___PAS__DBUSER.T_address f where f.address_id = d.address_id
		           and f.customer_id = d.customer_id) as address from APP___PAS__DBUSER.T_customer c, APP___PAS__DBUSER.T_INSURED_LIST d
		 where c.customer_id in
		       (select a.customer_id from APP___PAS__DBUSER.T_INSURED_LIST a,
		           (select policy_code, policy_id from APP___PAS__DBUSER.T_contract_agent where agent_code = #{agent_code}) b
		         where a.policy_id = b.policy_id and a.policy_code = b.policy_code)
		   and c.customer_id = d.customer_id 
		   and to_char(c.customer_birthday,'MMdd')<= #{birthDay_end} 
		   and to_char(c.customer_birthday,'MMdd')>= #{birthDay_start}
		union
		select d.policy_code,
		        (select (select product_abbr_name
		                  from APP___PAS__DBUSER.T_business_product where product_code_sys = g.busi_prod_code )
		          from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD g where g.policy_id = d.policy_id
		           and g.policy_code = d.policy_code and g.MASTER_BUSI_ITEM_ID is null) as riskname,
		       c.customer_name, c.customer_birthday, c.mobile_tel,
		       (select f.ADDRESS from APP___PAS__DBUSER.T_address f where f.address_id = d.address_id
		           and f.customer_id = d.customer_id) as address from APP___PAS__DBUSER.T_customer c, APP___PAS__DBUSER.T_POLICY_HOLDER d
		 where c.customer_id in
		       (select a.customer_id from APP___PAS__DBUSER.T_POLICY_HOLDER a,
		           (select policy_code, policy_id from APP___PAS__DBUSER.T_contract_agent where agent_code = #{agent_code}) b
		         where a.policy_id = b.policy_id and a.policy_code = b.policy_code)
		   and c.customer_id = d.customer_id 
		   and to_char(c.customer_birthday,'MMdd')<= #{birthDay_end} 
		   and to_char(c.customer_birthday,'MMdd')>= #{birthDay_start}
		]]>
	</select>
 
	
	<!-- 查询被保人生存状态 -->
	<select id="JRQD_queryLiveStatus" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	
			SELECT C.CUSTOMER_ID,C.LIVE_STATUS,C.DEATH_DATE 
			  FROM APP___PAS__DBUSER.T_BENEFIT_INSURED    A,
			       APP___PAS__DBUSER.T_INSURED_LIST       B,
			       APP___PAS__DBUSER.T_CUSTOMER           C
			 WHERE A.INSURED_ID = B.LIST_ID
			   AND B.CUSTOMER_ID = C.CUSTOMER_ID
			   AND A.BUSI_ITEM_ID = #{busi_item_id}
		]]>
	</select>
	
	<!-- 查询一年期投保人电话客户个数 -->
	<select id="JRQD_queryHolderCustomerIdByMobileTel" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	
			SELECT DISTINCT PH.customer_id
                 FROM  DEV_PAS.T_POLICY_HOLDER PH
                INNER JOIN DEV_PAS.T_CONTRACT_MASTER M ON PH.POLICY_ID = M.POLICY_ID 
                  AND M.LIABILITY_STATE = '1'
                AND ABS((SELECT ((MONTHS_BETWEEN(M.VALIDATE_DATE,
                           M.EXPIRY_DATE))/12) FROM DUAL)) > 1
                 INNER JOIN DEV_PAS.T_CUSTOMER A ON A.CUSTOMER_ID = PH.CUSTOMER_ID
                WHERE 1=1
	 	]]>
		<include refid="JRQD_PA_customerWhereCondition" />	
		
	</select>
 
	
	<!--leihong 更改客户风险等级 -->
	<update id="JRQD_updateCustomerRiskLevel" parameterType="java.util.Map" >
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER 
					  SET CUSTOMER_RISK_LEVEL = #{customer_risk_level, jdbcType=VARCHAR},
					  UPDATE_TIME = SYSDATE , 
 					  UPDATE_TIMESTAMP = CURRENT_TIMESTAMP 
				   WHERE CUSTOMER_ID = #{customer_id}
	    ]]>
	</update>
	<!--leihong 更改rz_level -->
	<update id="JRQD_PA_updateCustomerRZLevel" parameterType="java.util.Map" >
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER 
					  SET RZ_LEVEL = #{rz_level,jdbcType=VARCHAR},
					  UPDATE_TIME = SYSDATE , 
 					  UPDATE_TIMESTAMP = CURRENT_TIMESTAMP 
				   WHERE CUSTOMER_ID = #{customer_id}
	    ]]>
	</update>
    <!--通过保单号和保单id获取投保人信息，提供个外围系统 -->
   <select id="JRQD_queryPolicyholderBypolicyCodeAndPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	
	select cu.*, (select li.license_desc
          from APP___PAS__DBUSER.T_license_type li
         where li.license_type = cu.DRIVER_LICENSE_TYPE) as license_desc
		  from APP___PAS__DBUSER.T_POLICY_HOLDER ho
		  left join APP___PAS__DBUSER.T_customer cu on cu.customer_id =  ho.customer_id
		  left join APP___PAS__DBUSER.T_address a on cu.customer_id = a.customer_id
	 where ho.policy_code = #{policy_code} and ho.policy_id = #{policy_id}
		]]>
	</select>
   <!--通过保单号和保单id获取被保人信息，提供个外围系统 -->
   <select id="JRQD_queryInsuredListBypolicyCodeAndPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	
     select cu.*, 
          (select li.license_desc from APP___PAS__DBUSER.T_license_type li  where li.license_type = cu.DRIVER_LICENSE_TYPE) as license_desc,
          a.relation_to_insured_1, ho.relation_to_ph,a.order_id
		  from APP___PAS__DBUSER.T_INSURED_LIST ho
		  left join APP___PAS__DBUSER.T_customer cu on cu.customer_id =  ho.customer_id
		  left join APP___PAS__DBUSER.T_BENEFIT_INSURED a on a.policy_id = ho.policy_id
	 where ho.policy_code = #{policy_code} and ho.policy_id = #{policy_id}
		]]>
	</select>
  <!-- 保单号和保单id获取保单受益人信息 ,提供个外围系统-->
  <select id="JRQD_PA_findContractBeneInfoByCodeAndId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
		   select cu.*,t.bene_type,t.share_order,b.order_id,t.designation,a.address,a.post_code,t.share_rate,
		   (select m.customer_name from APP___PAS__DBUSER.T_customer m where m.customer_id = t.insured_id) as insured_name
		     from APP___PAS__DBUSER.T_CONTRACT_BENE t
		     left join APP___PAS__DBUSER.T_customer cu
		       on t.customer_id = cu.customer_id
		 left join APP___PAS__DBUSER.T_address a  on  a.address_id = t.address_id and a.customer_id = t.customer_id
         left join APP___PAS__DBUSER.T_BENEFIT_INSURED b on  b.policy_id = t.policy_id and  t.busi_item_id = b.busi_item_id and b.busi_item_id is not null 
		    where t.policy_id = #{policy_id}
		      and t.policy_code = #{policy_code}
		 ]]>
	</select>
	<select id="JRQD_queryCustById" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	select COMPANY_NAME,OFFEN_USE_TEL,MOBILE_TEL,EMAIL,FAX_TEL from APP___PAS__DBUSER.T_customer where customer_id=#{customer_id}
	]]>
	</select>
	<!-- riskfindCsutomer -->
	 <select id="JRQD_riskFindCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A
			WHERE A. CUSTOMER_ID <= #{customer_height}  AND A.CUSTOMER_ID >= #{customer_id} ]]>
	</select>
	<!-- 修改操作 -->
	<update id="JRQD_PA_updateCustomerPWD" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CUSTOMER set
			CUST_PWD = #{cust_pwd, jdbcType=VARCHAR} ,
		    SYN_MDM_FLAG = 1 
            WHERE CUSTOMER_ID = #{customer_id} ]]>
	</update>
	<!-- 通过policyCode 查询投保人 -->
	 <select id="JRQD_PA_findCustomerByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS
		  from dev_pas.T_CONTRACT_MASTER cm,
		       dev_pas.T_POLICY_HOLDER    il,
		       dev_pas.t_customer        A
		 where cm.policy_code = #{policy_code}
		   and cm.policy_id = il.policy_id
		   and il.customer_id = A.customer_id ]]>
	</select>

	<!-- 查询回访电话相同的个数 -->
	<select id="JRQD_PA_findCustomerBizCallObjectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
				  from APP___PAS__DBUSER.t_biz_call_object bco
				 where bco.boj_call_tel = #{office_tel}
			 ]]>
	</select>
	
	
	<select id="JRQD_PA_findAllCustomerForBC" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.customer_id,a.customer_name,
						a.customer_cert_type, --证件类型
						a.customer_certi_code,--证件号码
						a.cust_cert_star_date,--证件有效起期
						a.cust_cert_end_date,--证件有效止期
						a.country_code,--国籍
						a.customer_gender,-- 性别
						a.customer_birthday,--生日 
						a.job_code,--职业编码
						a.Offen_Use_Tel,
						a.House_Tel,--固定电话
						b.address_id,b.State,b.city,b.district,b.address,b.post_code,
						b.mobile_tel,--联系电话
						b.Fixed_Tel--固定电话
						from APP___PAS__DBUSER.t_Customer a ,APP___PAS__DBUSER.t_Address b 
						where a.customer_id=b.customer_id(+) and a.customer_id=#{customer_id}  ]]>
		
	</select>
	
	<!-- 查询投保人电话是否唯一 -->
	<select id="JRQD_findCustomerMobile" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
			  from dev_pas.t_customer        tc,
			       dev_pas.t_policy_holder   tph,
			       dev_pas.t_Contract_Master tcm
			 where tc.mobile_tel = #{mobile_tel}
			   and tc.customer_id != #{customer_id}
			   and tph.customer_id = tc.customer_id
			   and tcm.policy_id = tph.policy_id
			   and tcm.liability_state != '3'
			 ]]>
	</select>
	
	<!-- 投保人电话是否与其他投保人电话相同 -->
	<select id="JRQD_findOrganCodeMobile" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[select count(1)
		  from dev_pas.t_customer tc
		 where tc.mobile_tel = #{mobile_tel}
		]]>
	</select>

	<!-- 查询客户税收信息 T_ADDRESS_TAX添加字段 -->
	<!--udmp的T_CUSTOMER_TAX和T_CUSTOMER_TAX_TRACK表在总集环境上已修改表结构（总集上表是最新的，会主键覆盖到其他环境），因此修改表关联字段 
	 modify-by zhangxj2_wb 2018-8-16 -->
	<select id="JRQD_querycustomerInfoByfiveFacters" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[	 
	SELECT B.CUSTOMER_TAX_BIRTHDAY,
		       B.UPDATE_BY,
		       B.INSERT_TIME,
		       B.INSERT_TIMESTAMP,
		       B.UPDATE_TIME,
		       B.UPDATE_TIMESTAMP,
		       B.CUSTOMER_TAX_SURNAME,
		       B.CUSTOMER_TAX_ENAME,
		       B.REMARKS,
		       B.CUSTOMERTAX_ID,
		       D.LIVE_STATE_ENGLISH,
		       D.LIVE_CITY_ENGLISH,
		       D.LIVE_COUNTRY_CODE_ENGLISH,
		       D.LIVE_DISTRICT_ENGLISH,
		       D.LIVE_ADDRESS_ENGLISH,
		       D.BIRTH_STATE_CHN,
		       D.BIRTH_CITY_CHN,
		       D.BIRTH_DISTRICT_CHN,
		       D.BIRTH_ADDRESS_CHN,
		       D.BIRTH_STATE_ENGLISH,
		       D.BIRTH_CITY_ENGLISH,
		       D.LIVE_COUNTRY_CODE_CHN,
		       D.BIRTH_DISTRICT_ENGLISH,
		       D.LIVE_STATE_CHN,
		       D.LIVE_CITY_CHN,
		       D.LIVE_DISTRICT_CHN,
		       D.LIVE_ADDRESS_CHN,
		       D.BIRTH_COUNTRY_CODE_CHN,
		       D.BIRTH_COUNTRY_CODE_ENGLISH,
		       D.BIRTH_ADDRESS_ENGLISH,
		       E.OPERATION_TYPE,
		       E.TAX_RESIDENT_TYPE,
		       E.TRACK_ID,
       		   (SELECT C.CUSTOMER_ID FROM APP___PAS__DBUSER.T_CUSTOMER C WHERE C.CUSTOMER_NAME = B.CUSTOMER_TAX_NAME 
			       AND C.CUSTOMER_GENDER = B.CUSTOMER_TAX_GENDER AND C.CUSTOMER_BIRTHDAY = B.CUSTOMER_TAX_BIRTHDAY 
			       AND C.CUSTOMER_CERT_TYPE = B.CUSTOMER_TAX_CERT_TYPE AND C.CUSTOMER_CERTI_CODE = B.CUSTOMER_TAX_CERTI_CODE AND ROWNUM=1) AS CUSTOMER_ID
			  from APP___PAS__DBUSER.T_CUSTOMER_TAX B 
			  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS_TAX D
			    on B.CUSTOMERTAX_ID = D.CUSTOMERTAX_ID
			  LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER_TAX_TRACK E
			    ON B.CUSTOMER_TAX_NAME = E.CUSTOMER_TAX_NAME
			   AND B.CUSTOMER_TAX_BIRTHDAY = E.CUSTOMER_TAX_BIRTHDAY
			   AND B.CUSTOMER_TAX_GENDER = E.CUSTOMER_TAX_GENDER
			   AND B.CUSTOMER_TAX_CERT_TYPE = E.CUSTOMER_TAX_CERT_TYPE
			   AND B.CUSTOMER_TAX_CERTI_CODE = E.CUSTOMER_TAX_CERTI_CODE
			 WHERE 1 = 1 
	           and  B.CUSTOMER_TAX_GENDER= #{customer_gender} 
	           and  B.CUSTOMER_TAX_CERT_TYPE= #{customer_cert_type}
	           and  B.CUSTOMER_TAX_CERTI_CODE=#{customer_certi_code} 
	           and  B.CUSTOMER_TAX_BIRTHDAY= #{customer_birthday} 
	           and  B.CUSTOMER_TAX_NAME= #{customer_name}
	            order by E.INSERT_TIME DESC  
	           
	  ]]>    
	
	</select>
	
	<!-- 查询税收信息 -->
	
	<select id="JRQD_querycustomerInfoBycustomertaxId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[   SELECT A.TAX_COUNTRY,A.TAX_NUMBER,
         			A.CAUSE_NO,A.CAUSE_INS
     			FROM APP___PAS__DBUSER.T_TAX_INFO A 
     			WHERE A.CUSTOMERTAX_ID =#{customertax_id}
	 ]]> 
	</select>
	<!-- 查询客户类型 -->
	<select id="JRQD_querycustomerTypeBycustomertaxId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ select 1 as customerType from APP___PAS__DBUSER.t_Policy_Holder A WHERE  A.CUSTOMER_ID=#{customer_id}
  UNION select 2 as customerType from APP___PAS__DBUSER.t_Insured_List B WHERE B.CUSTOMER_ID=#{customer_id}
  UNION select 3 as customerType from APP___PAS__DBUSER.T_CONTRACT_BENE C  WHERE C.CUSTOMER_ID=#{customer_id}
	 ]]>
	
	</select>

	
	<select id="JRQD_PA_addressToSwitch" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		    SELECT T.NEW_CODE FROM DEV_PAS.T_CODE_MAPPER T WHERE T.CODETYPE=#{code_type} AND T.OLD_CODE_NAME=#{code_name} AND ROWNUM=1
		]]>
	</select>

	<!-- 查询客户信息 -->
	<select id="JRQD_PA_queryCustem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
      SELECT B.POLICY_CODE,
       B.APPLY_CODE,
       B.RELATION_TO_PH,
       B.CUSTOMER_ID,
       C.CUSTOMER_NAME,
       C.CUSTOMER_BIRTHDAY,
       C.CUSTOMER_GENDER,
       TCM.LIABILITY_STATE
  FROM DEV_PAS.T_POLICY_HOLDER A
  JOIN DEV_PAS.T_INSURED_LIST B
    ON A.POLICY_ID = B.POLICY_ID
  LEFT JOIN DEV_PAS.T_CUSTOMER C
    ON B.CUSTOMER_ID = C.CUSTOMER_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM
    ON A.POLICY_ID = TCM.POLICY_ID
 WHERE B.RELATION_TO_PH IN (07, 01, 02, 03, 04)
			                           
		]]>

		<if test=" customer_id != null and customer_id != ''  ">
			<![CDATA[ and a.customer_id = #{customer_id, jdbcType=VARCHAR}]]>
		</if>
				<if test=" insuredCustomerId != null and insuredCustomerId != ''  ">
			<![CDATA[ and b.customer_id != #{insuredCustomerId, jdbcType=VARCHAR}]]>
		</if>			
		<![CDATA[  order by b.RELATION_TO_PH desc]]>
	</select>

	<!-- 查询做过投保人变更的最早数据 -->
	<select id="JRQD_findSourcePolicyHolderName"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select t.customer_name,t.customer_id
		  from (select cus.customer_name,cus.customer_id
		          from APP___PAS__DBUSER.t_cs_policy_change pc,
		               APP___PAS__DBUSER.t_cs_policy_holder ph,
		               APP___PAS__DBUSER.t_customer         cus
		         where pc.service_code = 'AE'
		           and pc.policy_code = #{policy_code}
		           and cus.customer_id = ph.customer_id
		           and ph.old_new = '0'
		           and ph.change_id = pc.change_id
		         order by pc.insert_time desc) t
		 where ROWNUM <= 1]]>
	</select>
	
	<!-- 查询被保人信息 findInsurdByBusiItmeid -->
	<select id="JRQD_findInsurdByBusiItmeid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE A.CUSTOMER_ID =
				(SELECT B.CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST B WHERE B.LIST_ID =
				(SELECT C.INSURED_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED C WHERE 
				1=1
		 ]]>
		 <if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND C.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		 <if test=" order_id != null and order_id != ''  "><![CDATA[ AND C.ORDER_ID = #{order_id} ]]></if>
		 <![CDATA[ )) ]]> 
	 </select>
	 
	 <!-- 校验移动电话 -->
	 <select id="JRQD_PA_checkRepeatMobileTel"  resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		     SELECT CASE WHEN (SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CUSTOMER T
		         WHERE T.MOBILE_TEL = #{mobile_tel} AND T.CUSTOMER_ID <> #{customer_id} AND T.LIVE_STATUS = '1') >= 3 
		         THEN 0 ELSE 1 END FLAG FROM DUAL
		]]>
	 </select>
	
	 <!-- 校验固定电话 -->
	 <select id="JRQD_PA_checkRepeatHouOffTel"  resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
              SELECT CASE WHEN 
                  (SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CUSTOMER T WHERE T.HOUSE_TEL=#{house_tel} AND T.CUSTOMER_ID <> #{customer_id} AND T.LIVE_STATUS='1')>=5 THEN 0
                  WHEN 
                  (SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CUSTOMER T WHERE T.OFFICE_TEL=#{office_tel} AND T.CUSTOMER_ID <> #{customer_id} AND T.LIVE_STATUS='1')>=5 THEN 0
                  ELSE 1 END FLAG FROM DUAL
		]]>
	  </select>
	 
	  <!-- 业务员所在四级机构其他业务员手机号码 -->
	 <select id="JRQD_PA_checkRepeatTelForAgent"  resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
            SELECT COUNT(1) FROM DEV_PAS.T_AGENT A WHERE A.AGENT_CODE IN(
            SELECT T.ORGAN_CODE
            FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
            START WITH T.ORGAN_CODE = #{organ_code}
            CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE)
         ]]>
		     <if test=" telPhone != null and telPhone != ''  "><![CDATA[ AND A.AGENT_PHONE = #{telPhone} ]]></if> 
		     <if test=" houPhone != null and houPhone != ''  "><![CDATA[ AND A.AGENT_PHONE = #{houPhone} ]]></if> 
		     <if test=" agent_mobile != null and agent_mobile != ''  "><![CDATA[ AND A.AGENT_MOBILE = #{agent_mobile} ]]></if> 	
		
	 </select>

	 <!-- 根据customerid查询地址-->
	<select id="JRQD_queryCustomerIdByAddress" resultType="java.util.Map" parameterType="java.util.Map" >
<![CDATA[	select b.Live_Country_Code_Chn AS COUNTRY_CODE,
       b.LIVE_COUNTRY_CODE_ENGLISH AS ECOUNTRY_CODE,
       b.LIVE_STATE_CHN AS STATE_CHN,
       b.LIVE_STATE_ENGLISH AS STATE_CHN,
       b.LIVE_CITY_CHN AS CITY_CHN,
       b.Live_City_English AS City_English,
       b.Live_District_Chn AS District_Chn,
       b.Live_District_English AS District_English,
       b.LIVE_ADDRESS_CHN AS ADDRESS_CHN,
       b.Live_Address_English AS Address_English,
       b.customertax_id
  from dev_pas.t_customer_tax a
  left join dev_pas.T_ADDRESS_TAX b
    on a.customertax_id = b.customertax_id
 where a.customertax_id =#{customer_id} and ROWNUM <= 1
	  ]]>    
	
	</select>
	 <!-- 根据五要素查询客户信息 -->
	<select id="JRQD_PA_findAllCustomerCrs" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.TAX_RESIDENT_TYPE,
			       A.CUSTOMER_NAME,
			       A.CUSTOMER_LEVEL,
			       A.NATION_CODE,
			       A.DEATH_DATE,
			       A.JOB_NATURE,
			       A.REMARK,
			       A.CUST_PWD,
			       A.CUSTOMER_RISK_LEVEL,
			       A.WECHAT_NO,
			       A.OFFEN_USE_TEL,
			       A.UN_CUSTOMER_CODE,
			       A.CUST_CERT_END_DATE,
			       A.QQ,
			       A.OLD_CUSTOMER_ID,
			       A.MOBILE_TEL,
			       A.CUSTOMER_BIRTHDAY,
			       A.IS_PARENT,
			       A.COUNTRY_CODE,
			       A.FAX_TEL,
			       A.JOB_CODE,
			       A.OFFICE_TEL,
			       A.SMOKING_FLAG,
			       A.MARRIAGE_STATUS,
			       A.EDUCATION,
			       A.CUSTOMER_ID_CODE,
			       A.CUSTOMER_GENDER,
			       A.OTHER,
			       A.COMPANY_NAME,
			       A.JOB_TITLE,
			       A.CUSTOMER_ID,
			       A.CUSTOMER_HEIGHT,
			       A.HEALTH_STATUS,
			       A.CUSTOMER_CERTI_CODE,
			       A.RETIRED_FLAG,
			       A.DRUNK_FLAG,
			       A.MARRIAGE_DATE,
			       A.BLACKLIST_FLAG,
			       A.DRIVER_LICENSE_TYPE,
			       A.HOUSEKEEPER_FLAG,
			       A.EMAIL,
			       A.ANNUAL_INCOME,
			       A.CUSTOMER_CERT_TYPE,
			       A.HOUSE_TEL,
			       A.JOB_KIND,
			       A.CUSTOMER_WEIGHT,
			       A.RELIGION_CODE,
			       A.CUST_CERT_STAR_DATE,
			       A.SYN_MDM_FLAG,
			       A.CUSTOMER_VIP,
			       A.LIVE_STATUS,
			       A.RZ_LEVEL,
			       A.COMM_METHOD,
			       B.RESIDENT_NAME
			 FROM APP___PAS__DBUSER.T_CUSTOMER A
			 LEFT JOIN APP___PAS__DBUSER.T_COUNTRY C
			 ON A.COUNTRY_CODE = C.COUNTRY_CODE
			 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER_TAX T
			 ON A.CUSTOMER_NAME = T.CUSTOMER_TAX_NAME
			 AND A.CUSTOMER_BIRTHDAY = T.CUSTOMER_TAX_BIRTHDAY
			 AND A.CUSTOMER_GENDER = T.CUSTOMER_TAX_GENDER
			 AND A.CUSTOMER_CERT_TYPE = T.CUSTOMER_TAX_CERT_TYPE
			 AND A.CUSTOMER_CERTI_CODE = T.CUSTOMER_TAX_CERTI_CODE
			 INNER JOIN APP___PAS__DBUSER.T_TAX_RESIDENT_TYPE B
			 ON T.TAX_RESIDENT_TYPE = B.RESIDENT_CODE AND ROWNUM=1]]>
		<if test=" customer_name != null and customer_name != ''  ">
		<![CDATA[ AND A.CUSTOMER_NAME =#{customer_name} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  ">
		<![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null ">
		<![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  ">
		<![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  ">
		<![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</select>
	
	<!-- 根据客户号查询客户信息 -->
	<select id="JRQD_PA_findCustomerCrs" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT  D.CUSTOMER_ID,
                B.TAX_COUNTRY,
                C.COUNTRY_NAME,
                A.TAX_RESIDENT_TYPE
  FROM APP___PAS__DBUSER.T_CUSTOMER_TAX A
  LEFT JOIN APP___PAS__DBUSER.T_TAX_INFO B
    ON A.CUSTOMERTAX_ID = B.CUSTOMERTAX_ID
  LEFT JOIN APP___PAS__DBUSER.T_COUNTRY C
    ON B.TAX_COUNTRY = C.COUNTRY_CODE
  INNER JOIN APP___PAS__DBUSER.T_CUSTOMER D
    ON D.CUSTOMER_NAME = A.CUSTOMER_TAX_NAME
   AND D.CUSTOMER_BIRTHDAY = A.CUSTOMER_TAX_BIRTHDAY
   AND D.CUSTOMER_GENDER = A.CUSTOMER_TAX_GENDER
   AND D.CUSTOMER_CERT_TYPE = A.CUSTOMER_TAX_CERT_TYPE
   AND D.CUSTOMER_CERTI_CODE = A.CUSTOMER_TAX_CERTI_CODE
 WHERE 1 = 1 ]]>
		<if test=" customer_name != null and customer_name != ''  ">
		<![CDATA[ AND D.CUSTOMER_NAME =#{customer_name} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  ">
		<![CDATA[ AND D.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null ">
		<![CDATA[ AND D.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  ">
		<![CDATA[ AND D.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  ">
		<![CDATA[ AND D.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</select>

	<!-- 查询客户信息 -->
	<select id="JRQD_PA_findCustomerByCustomerCertTypeAndBank" resultType="java.util.Map"
		parameterType="java.util.Map">
	 	<![CDATA[
	 		SELECT A.CUSTOMER_NAME,
				       A.CUSTOMER_LEVEL,
				       A.NATION_CODE,
				       A.DEATH_DATE,
				       A.JOB_NATURE,
				       A.REMARK,
				       A.CUST_PWD,
				       A.CUSTOMER_RISK_LEVEL,
				       A.WECHAT_NO,
				       A.OFFEN_USE_TEL,
				       A.UN_CUSTOMER_CODE,
				       A.CUST_CERT_END_DATE,
				       A.QQ,
				       A.OLD_CUSTOMER_ID,
				       A.MOBILE_TEL,
				       A.CUSTOMER_BIRTHDAY,
				       A.IS_PARENT,
				       A.COUNTRY_CODE,
				       A.FAX_TEL,
				       A.JOB_CODE,
				       A.OFFICE_TEL,
				       A.SMOKING_FLAG,
				       A.MARRIAGE_STATUS,
				       A.EDUCATION,
				       A.CUSTOMER_ID_CODE,
				       A.CUSTOMER_GENDER,
				       A.OTHER,
				       A.COMPANY_NAME,
				       A.JOB_TITLE,
				       A.CUSTOMER_ID,
				       A.CUSTOMER_HEIGHT,
				       A.HEALTH_STATUS,
				       A.CUSTOMER_CERTI_CODE,
				       A.RETIRED_FLAG,
				       A.DRUNK_FLAG,
				       A.MARRIAGE_DATE,
				       A.BLACKLIST_FLAG,
				       A.DRIVER_LICENSE_TYPE,
				       A.HOUSEKEEPER_FLAG,
				       A.EMAIL,
				       A.ANNUAL_INCOME,
				       A.CUSTOMER_CERT_TYPE,
				       A.HOUSE_TEL,
				       A.JOB_KIND,
				       A.TAX_RESIDENT_TYPE,
				       A.CUSTOMER_WEIGHT,
				       A.RELIGION_CODE,
				       A.CUST_CERT_STAR_DATE,
				       A.SYN_MDM_FLAG,
				       A.CUSTOMER_VIP,
				       A.LIVE_STATUS,
				       A.COMM_METHOD,
				       A.RZ_LEVEL
				  FROM APP___PAS__DBUSER.T_CUSTOMER A join dev_pas.t_policy_holder b on a.CUSTOMER_ID =b.CUSTOMER_ID
				 WHERE 1 = 1
				   and rownum < 1000 
	 	]]>
    <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND b.policy_code = #{policy_code} ]]></if>	 	
		<include refid="JRQD_PA_queryCustomerByCustomerCertiCodeCondition" />
		<include refid="JRQD_PA_customerWhereCondition" />
	</select>
	
    <!--根据客户五要素查询有效保单投保人的移动电话 -->
	<select id="JRQD_queryRepeatHolderPhone" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT DISTINCT C.CUSTOMER_ID,
                   C.CUSTOMER_NAME,
                   C.CUSTOMER_GENDER,
                   C.CUSTOMER_BIRTHDAY,
                   C.CUSTOMER_CERT_TYPE,
                   C.CUSTOMER_CERTI_CODE
			  FROM APP___PAS__DBUSER.T_CUSTOMER C
			 WHERE C.MOBILE_TEL = #{mobile_tel}
			   AND EXISTS (SELECT 1
			          FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH
			         INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER M
			            ON PH.POLICY_ID = M.POLICY_ID
			         WHERE C.CUSTOMER_ID = PH.CUSTOMER_ID
			           AND M.LIABILITY_STATE = 1
			           AND (ABS(MONTHS_BETWEEN(M.VALIDATE_DATE, M.EXPIRY_DATE)) / 12) > 1)
			   AND NOT EXISTS (SELECT 1
		          FROM DEV_PAS.T_CUSTOMER TD
		         WHERE  TD.CUSTOMER_ID = C.CUSTOMER_ID 
		            AND TD.CUSTOMER_NAME = #{customer_name}
		            AND TD.CUSTOMER_GENDER = #{customer_gender}
		            AND TD.CUSTOMER_BIRTHDAY =  #{customer_birthday}
		            AND TD.CUSTOMER_CERT_TYPE = #{customer_cert_type}		           
		           ]]>
		           <choose>
		             <when test="certi_code != null and certi_code != ''">
						 AND (TD.CUSTOMER_CERTI_CODE = #{customer_certi_code} OR TD.CUSTOMER_CERTI_CODE = #{certi_code})
					 </when>
					 <otherwise>
					     AND TD.CUSTOMER_CERTI_CODE = #{customer_certi_code}
					 </otherwise>
		           </choose>	           	
		   <![CDATA[ ) ]]>
	</select>
	
    <!--固定电话号码不得与全系统5个有效客户重复 -->
	<select id="JRQD_checkCustTelRepe" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  SELECT DISTINCT C.CUSTOMER_NAME,
                C.CUSTOMER_GENDER,
                C.CUSTOMER_BIRTHDAY,
                C.CUSTOMER_CERT_TYPE,
                C.CUSTOMER_CERTI_CODE,
                C.OFFICE_TEL,
                C.OFFEN_USE_TEL
			  FROM APP___PAS__DBUSER.T_CUSTOMER C
			 WHERE C.OFFEN_USE_TEL = #{offen_use_tel}
			   AND EXISTS
			 (SELECT 1
			          FROM APP___PAS__DBUSER.T_POLICY_HOLDER PH
			         INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER M
			            ON PH.POLICY_ID = M.POLICY_ID
			         WHERE C.CUSTOMER_ID = PH.CUSTOMER_ID
			           AND M.LIABILITY_STATE = 1
			           AND (ABS(MONTHS_BETWEEN(M.VALIDATE_DATE, M.EXPIRY_DATE)) / 12) > 1)
			 ]]>
	</select>
	<!--根据五要素查询五要素相同的客户 -->
	<select id="JRQD_PA_findAllCustomerByFiveElement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_customerWhereCondition" />
	</select>
	
	
	<!-- 根据五要素查询是否采集居民税收信息 -->
	 <select id="JRQD_PA_findCrsCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT T.TAX_RESIDENT_TYPE,
                    T.CUSTOMER_TAX_NAME,
                    T.CUSTOMER_TAX_BIRTHDAY,
                    T.CUSTOMER_TAX_GENDER,
                    T.CUSTOMER_TAX_CERT_TYPE,
                    T.CUSTOMER_TAX_CERTI_CODE
      FROM APP___PAS__DBUSER.T_CUSTOMER_TAX T
     WHERE 1 = 1 ]]>
			<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND T.CUSTOMER_TAX_NAME = #{customer_name} ]]></if>
			<if test=" customer_birthday != null and customer_birthday != ''  "><![CDATA[ AND T.CUSTOMER_TAX_BIRTHDAY = #{customer_birthday} ]]></if>
			<if test=" customer_gender != null and customer_gender != ''  "><![CDATA[ AND T.CUSTOMER_TAX_GENDER = #{customer_gender} ]]></if>
			<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND T.CUSTOMER_TAX_CERT_TYPE = #{customer_cert_type} ]]></if>
			<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND T.CUSTOMER_TAX_CERTI_CODE = #{customer_certi_code} ]]></if>

	</select>
	
      <!-- 	查询快捷客户基本资料的客户信息 -->
	 <!-- <select id="JRQD_queryCCHKCustomer" resultType="java.util.Map" parameterType="java.util.Map">
	 	<![CDATA[select m.* from dev_pas.t_customer m where m.customer_id in 
				( select distinct tablehp.customer_id
				  from (select ph.customer_id
				          from dev_pas.t_Policy_Holder ph
				         where ph.policy_id in
				               (select distinct thp.policy_id
				                  from (select h.policy_id
				                          from dev_pas.t_Policy_Holder h
				                         where h.customer_id = #{customer_id}
				                        union all
				                        select p.policy_id
				                          from dev_pas.t_insured_list p
				                         where p.customer_id = #{customer_id}) thp)
				        union all
				        select pil.customer_id
				          from dev_pas.t_insured_list pil
				         where pil.policy_id in
				               (select distinct thp.policy_id
				                  from (select h.policy_id
				                          from dev_pas.t_Policy_Holder h
				                         where h.customer_id = #{customer_id}
				                        union all
				                        select p.policy_id
				                          from dev_pas.t_insured_list p
				                         where p.customer_id = #{customer_id}) thp)) tablehp ) ]]>
	 </select> -->
	 
     <!-- 	 查询快捷客户基本资料的保单层信息 -->
	 <select id="JRQD_queryCCHKPolicy" resultType="java.util.Map" parameterType="java.util.Map">
	 	<![CDATA[ select tableInfo.policy_id, 
		       tableInfo.policy_code,
		       tableInfo.customer_id,
		       tableInfo.address_id,
		       tableInfo.role_Name,
		       cm.customer_name,
		       cm.customer_cert_type,
		       cm.customer_certi_code,
		       ad.mobile_tel,ad.fixed_tel,ad.state,ad.city,ad.district,ad.address from 
		(
		select ph.policy_id, 
		       ph.policy_code,
		       ph.customer_id,
		       ph.address_id,
		       '投保人'  role_Name
		          from APP___PAS__DBUSER.t_Policy_Holder ph
		         where ph.policy_id in
		               (select distinct thp.policy_id
		                  from (select h.policy_id
		                          from APP___PAS__DBUSER.t_Policy_Holder h
		                         where h.customer_id = #{customer_id}
		                        union all
		                        select p.policy_id
		                          from APP___PAS__DBUSER.t_insured_list p
		                         where p.customer_id = #{customer_id}) thp)
		        union all
		        select pil.policy_id, 
		               pil.policy_code,
		               pil.customer_id,
		               pil.address_id,
		               '被保人' role_Name
		          from APP___PAS__DBUSER.t_insured_list pil
		         where pil.policy_id in
		               (select distinct thp.policy_id
		                  from (select h.policy_id
		                          from APP___PAS__DBUSER.t_Policy_Holder h
		                         where h.customer_id = #{customer_id}
		                        union all
		                        select p.policy_id
		                          from APP___PAS__DBUSER.t_insured_list p
		                         where p.customer_id = #{customer_id}) thp)
		                         )  tableInfo
		                        
		left join  APP___PAS__DBUSER.t_customer cm on tableInfo.customer_id = cm.customer_id
		left join APP___PAS__DBUSER.t_address ad on tableInfo.address_id = ad.address_id ]]>
	 <if test=" policy_code_list != null and policy_code_list.size() !=0 "><![CDATA[ where tableInfo.policy_code in  ]]> 
	 <foreach collection ="policy_code_list" item="policy_code" index="index" open="(" close=")" separator=",">
		<![CDATA[ #{policy_code,jdbcType=VARCHAR} ]]>
	</foreach>
	 </if>
	 </select>
	 
	 
	 
	 <!--根据五要素查询五要素相同的客户 (取最新的一条)-->
	<select id="JRQD_PA_findCustomerByFiveElement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE ROWNUM = 1  ]]>
		<include refid="JRQD_PA_customerWhereCondition" />
		<![CDATA[ ORDER BY A.UPDATE_TIMESTAMP]]>
	</select>
	 
	 <!--根据保单id查询被保人生日 -->
	<select id="JRQD_PA_findCustomerBypolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TC.CUSTOMER_ID,
       TC.UN_CUSTOMER_CODE,
       TC.OLD_CUSTOMER_ID,
       TC.MARRIAGE_DATE,
       TC.EDUCATION,
       TC.CUSTOMER_NAME,
       TC.CUSTOMER_BIRTHDAY,
       TC.CUSTOMER_GENDER,
       TC.CUSTOMER_HEIGHT,
       TC.CUSTOMER_WEIGHT,
       TC.CUSTOMER_CERT_TYPE,
       TC.CUSTOMER_CERTI_CODE,
       TC.CUSTOMER_ID_CODE,
       TC.CUST_CERT_STAR_DATE,
       TC.CUST_CERT_END_DATE
       FROM DEV_PAS.T_INSURED_LIST TIL,DEV_PAS.T_CUSTOMER TC WHERE TC.CUSTOMER_ID = TIL.CUSTOMER_ID   ]]>
	<if test=" policy_id  != null "><![CDATA[ AND TIL.POLICY_ID = #{policy_id} ]]></if>		
	</select>

	 <!--查询内蒙分公司有效投保人、被保人重复电话信息 -->
	<select id="JRQD_queryRepeatMobileNM" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select distinct taCus.customer_id,
			        taCus.customer_name,
			        taCus.customer_gender,
			        taCus.customer_birthday,
			        taCus.customer_cert_type,
			        taCus.customer_certi_code
			   from (select distinct c.customer_id,
			                         c.customer_name,
			                         c.customer_gender,
			                         c.customer_birthday,
			                         c.customer_cert_type,
			                         c.customer_certi_code
			           from dev_pas.t_customer c
			          right join dev_pas.t_policy_holder ph
			             on c.customer_id = ph.customer_id
			          right join dev_pas.t_contract_master m
			             on ph.policy_id = m.policy_id ]]>
			           <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
			           <![CDATA[and m.LIABILITY_STATE = 1 and abs((select ((months_between(m.VALIDATE_DATE, 
                           m.EXPIRY_DATE))/12) from dual)) > 1 
                           and abs((select ((months_between(c.customer_birthday,
                           sysdate))/12) from dual)) > 18
			          where c.mobile_tel = #{mobile_tel}
			          
			          union all
			          
			          select distinct c.customer_id,
			                         c.customer_name,
			                         c.customer_gender,
			                         c.customer_birthday,
			                         c.customer_cert_type,
			                         c.customer_certi_code
			           from dev_pas.t_customer c
			          right join dev_pas.t_insured_list ph
			             on c.customer_id = ph.customer_id
			          right join dev_pas.t_contract_master m
			             on ph.policy_id = m.policy_id ]]>
			           <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
			           <![CDATA[and m.LIABILITY_STATE = 1 and abs((select ((months_between(m.VALIDATE_DATE,
                           m.EXPIRY_DATE))/12) from dual)) > 1
                           and abs((select ((months_between(c.customer_birthday,
                           sysdate))/12) from dual)) > 18
			          where c.mobile_tel = #{mobile_tel}
			          
			          ) taCus
			  where taCus.Customer_Id not in
			        (select d.customer_id
			           from dev_pas.t_customer d
			          where d.customer_name = #{customer_name}
			            and d.customer_gender = #{customer_gender}
			            and d.customer_birthday = #{customer_birthday}
			            and d.customer_cert_type = #{customer_cert_type}
			            and d.customer_certi_code = #{customer_certi_code}]]>
			 <if test=" certi_code != null and certi_code != '' ">  or d.customer_certi_code = #{certi_code}]]</if>	
			<![CDATA[)]]>
	</select>

    <!--九要素校验重复电话 -->
	<select id="JRQD_check9MsgMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select taCus.customer_id,
              taCus.customer_name,
              taCus.customer_gender,
              taCus.customer_birthday,
              taCus.customer_cert_type,
              taCus.customer_certi_code
         from (select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                 from dev_pas.t_customer c
                right join dev_pas.t_address ad on c.customer_id = ad.customer_id
                right join dev_pas.t_policy_holder ph
                   on c.customer_id = ph.customer_id
                right join dev_pas.t_contract_master m
                   on ph.policy_id = m.policy_id ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%' ]]></if>
                 <if test=" organ_code == '8638' "> and m.LIABILITY_STATE in (1,4) </if>
                 <if test=" organ_code == null || organ_code == '' || organ_code != '8638' "> and m.LIABILITY_STATE = 1 </if>
                   <![CDATA[and abs((select ((months_between(m.VALIDATE_DATE,
                           m.EXPIRY_DATE))/12) from dual)) > 1
                where ad.mobile_tel = #{mobile_tel}) taCus
        where taCus.Customer_Id not in
              (select d.customer_id
                 from dev_pas.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{customer_certi_code} ]]>
                  <if test=" old_customer_name != null and old_customer_name != '' ">
                  	 union all
                  	 
                  	 select d.customer_id
                  from dev_pas.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_customer_certi_code}
                  </if>
                   <if test=" certi_code != null and certi_code != '' "> 
                   	    <![CDATA[
                   	     union all
                  select d.customer_id
                 from dev_pas.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{certi_code} ]]>
                 
                   </if>
                    <![CDATA[  )]]>
       <!-- <if test=" certi_code != null and certi_code != '' ">
         <![CDATA[  union
        
          select taCus.customer_id,
              taCus.customer_name,
              taCus.customer_gender,
              taCus.customer_birthday,
              taCus.customer_cert_type,
              taCus.customer_certi_code
         from (select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                 from dev_pas.t_customer c
                 right join dev_pas.t_address ad on c.customer_id = ad.customer_id
                right join dev_pas.t_policy_holder ph
                   on c.customer_id = ph.customer_id
                right join dev_pas.t_contract_master m
                   on ph.policy_id = m.policy_id ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <if test=" organ_code == '8638' "> and m.LIABILITY_STATE in (1,4) </if>
                 <if test=" organ_code == null || organ_code == '' || organ_code != '8638' "> and m.LIABILITY_STATE = 1 </if>
                 <![CDATA[ and abs((select ((months_between(m.VALIDATE_DATE,
                           m.EXPIRY_DATE))/12) from dual)) > 1
                where ad.mobile_tel = #{mobile_tel}) taCus
        where taCus.Customer_Id not in
              (select d.customer_id
                 from dev_pas.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{certi_code} ]]>
                  <if test=" old_customer_name != null and old_customer_name != '' ">
                  	 union all
                  	 
                  	 select d.customer_id
                  from dev_pas.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_customer_certi_code}
                  </if>
                    <![CDATA[  )]]>
      </if>  -->
	</select>
	
	<!--查询内蒙分公司有效投保人、被保人、受益人重复电话信息 -->
  <select id="JRQD_check9MsgMobileNM" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ select distinct taCus.customer_id,
              taCus.customer_name,
              taCus.customer_gender,
              taCus.customer_birthday,
              taCus.customer_cert_type,
              taCus.customer_certi_code
         from (select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                 from dev_pas.t_customer c
                right join dev_pas.t_address ad on c.customer_id = ad.customer_id
                right join dev_pas.t_policy_holder ph
                   on c.customer_id = ph.customer_id
                right join dev_pas.t_contract_master m
                   on ph.policy_id = m.policy_id ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[and m.LIABILITY_STATE = 1 and abs((select ((months_between(m.VALIDATE_DATE, 
                           m.EXPIRY_DATE))/12) from dual)) > 1
                where ad.mobile_tel = #{mobile_tel}
                
                union all
                
                select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                 from dev_pas.t_customer c
                  right join dev_pas.t_address ad on c.customer_id = ad.customer_id
                right join dev_pas.t_insured_list ph
                   on c.customer_id = ph.customer_id
                right join dev_pas.t_contract_master m
                   on ph.policy_id = m.policy_id ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[and m.LIABILITY_STATE = 1 and abs((select ((months_between(m.VALIDATE_DATE,
                           m.EXPIRY_DATE))/12) from dual)) > 1
                where ad.mobile_tel = #{mobile_tel}
                
                union all
                
                select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                 from dev_pas.t_customer c
                  right join dev_pas.t_address ad on c.customer_id = ad.customer_id
                right join dev_pas.t_contract_bene ph
                   on c.customer_id = ph.customer_id
                right join dev_pas.t_contract_master m
                   on ph.policy_id = m.policy_id ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[and m.LIABILITY_STATE = 1 and abs((select ((months_between(m.VALIDATE_DATE,
                           m.EXPIRY_DATE))/12) from dual)) > 1
                where ad.mobile_tel = #{mobile_tel}
                
                ) taCus
        where taCus.Customer_Id not in
              (select d.customer_id
                 from dev_pas.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{customer_certi_code} ]]>
                  <if test=" old_customer_name != null and old_customer_name != '' ">
                  	 union all
                  	 
                  	 select d.customer_id
                  from dev_pas.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_customer_certi_code}
                  </if>
                    <![CDATA[  )]]>
       <if test=" certi_code != null and certi_code != '' ">
         <![CDATA[  union
        
          select taCus.customer_id,
              taCus.customer_name,
              taCus.customer_gender,
              taCus.customer_birthday,
              taCus.customer_cert_type,
              taCus.customer_certi_code
         from (select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                 from dev_pas.t_customer c
                  right join dev_pas.t_address ad on c.customer_id = ad.customer_id
                right join dev_pas.t_policy_holder ph
                   on c.customer_id = ph.customer_id
                right join dev_pas.t_contract_master m
                   on ph.policy_id = m.policy_id ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[ and m.LIABILITY_STATE = 1 and abs((select ((months_between(m.VALIDATE_DATE,
                           m.EXPIRY_DATE))/12) from dual)) > 1
                where ad.mobile_tel = #{mobile_tel}
                
                union all
                
                select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                 from dev_pas.t_customer c
                  right join dev_pas.t_address ad on c.customer_id = ad.customer_id
                right join dev_pas.t_insured_list ph
                   on c.customer_id = ph.customer_id
                right join dev_pas.t_contract_master m
                   on ph.policy_id = m.policy_id ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[and m.LIABILITY_STATE = 1 and abs((select ((months_between(m.VALIDATE_DATE,
                           m.EXPIRY_DATE))/12) from dual)) > 1
                where ad.mobile_tel = #{mobile_tel}
                
                union all
                
                select distinct c.customer_id,
                               c.customer_name,
                               c.customer_gender,
                               c.customer_birthday,
                               c.customer_cert_type,
                               c.customer_certi_code
                 from dev_pas.t_customer c
                  right join dev_pas.t_address ad on c.customer_id = ad.customer_id
                right join dev_pas.t_contract_bene ph
                   on c.customer_id = ph.customer_id
                right join dev_pas.t_contract_master m
                   on ph.policy_id = m.policy_id ]]>
                 <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
                 <![CDATA[and m.LIABILITY_STATE = 1 and abs((select ((months_between(m.VALIDATE_DATE,
                           m.EXPIRY_DATE))/12) from dual)) > 1
                where ad.mobile_tel = #{mobile_tel}
                
                ) taCus
        where taCus.Customer_Id not in
              (select d.customer_id
                 from dev_pas.t_customer d
                where d.customer_name = #{customer_name}
                  and d.customer_gender = #{customer_gender}
                  and d.customer_birthday = #{customer_birthday}
                  and d.customer_cert_type = #{customer_cert_type}
                  and d.customer_certi_code = #{certi_code} ]]>
                  <if test=" old_customer_name != null and old_customer_name != '' ">
                  	 union all
                  	 
                  	 select d.customer_id
                  from dev_pas.t_customer d
                  where d.customer_name = #{old_customer_name}
                  and d.customer_gender = #{old_customer_gender}
                  and d.customer_birthday = #{old_customer_birthday}
                  and d.customer_cert_type = #{old_customer_cert_type}
                  and d.customer_certi_code = #{old_customer_certi_code}
                  </if>
                    <![CDATA[  )]]>
      </if> 
  </select>
  
  <!--查询客户是否有在途业务  add by hhb-->
  <select id="JRQD_PA_checkIsLocked" resultType="java.util.Map" parameterType="java.util.Map">
  	<![CDATA[ 
  			select c.customer_id
  	        from dev_pas.t_customer c
  	        left join dev_pas.t_policy_holder ph on ph.customer_id=c.customer_id
  	        left join dev_pas.t_lock_policy lp on lp.policy_id =ph.policy_id
  	        left join dev_pas.t_lock_service_def d on lp.lock_service_id=d.lock_service_id
  	        where  d.sub_id='068' ]]>
  	        <![CDATA[  and c.customer_id in ]]> 
  	        	<foreach collection="id_list" item="ids" index="index" open="(" close=")" separator=",">
  	        		<![CDATA[ #{ids} ]]>
  	        	</foreach>
  	        <![CDATA[
  	        union 
  	        select c.customer_id
  	        from dev_pas.t_customer c
  	        left join dev_pas.t_insured_list l on l.customer_id=c.customer_id
  	        left join dev_pas.t_lock_policy lp on lp.policy_id =l.policy_id
  	        left join dev_pas.t_lock_service_def d on lp.lock_service_id=d.lock_service_id
  	        where  d.sub_id='068'  ]]> 
  	        <![CDATA[  and c.customer_id in ]]> 
  	        	<foreach collection="id_list" item="ides" index="index" open="(" close=")" separator=",">
  	        		<![CDATA[ #{ides} ]]>
  	        	</foreach>
  </select>
  
  <!--查询给定客户最新的保单信息 add by hhb -->
  <select id="JRQD_PA_queryLastCsCustomer" resultType="java.util.Map" parameterType="java.util.Map">
   	<![CDATA[
  		SELECT A.RZ_LEVEL, A.CUSTOMER_ID,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE,
  		    A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.CUSTOMER_RISK_LEVEL,A.COMM_METHOD,A.SOCI_SECU,A.IS_SUBSCRIPTION_EMAIL,A.RESIDENT_TYPE FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1]]>
  	        <![CDATA[  AND A.CUSTOMER_ID IN ]]> 
  	        	<foreach collection="id_list" item="id_list" index="index" open="(" close=")" separator=",">
  	        		<![CDATA[ #{id_list,jdbcType=VARCHAR} ]]>
  	        	</foreach>
  	      <![CDATA[ ORDER BY A.UPDATE_TIMESTAMP DESC  ]]>
  </select>
  
	<!-- add by hourui 查询投保人的其他银行账号 -->
	<select id="JRQD_findCustomerBankAccounts" resultType="java.util.Map" parameterType="java.util.Map">
	 select DISTINCT a.CUSTOMER_ID, a.CUSTOMER_NAME, a.CUSTOMER_GENDER, a.CUSTOMER_CERT_TYPE, a.CUSTOMER_CERTI_CODE, a.CUSTOMER_BIRTHDAY    
	 from dev_pas.T_CUSTOMER a 
	 RIGHT join DEV_PAS.T_POLICY_HOLDER b on a.CUSTOMER_ID = b.CUSTOMER_ID
	 RIGHT join DEV_PAS.T_BANK_ACCOUNT c  on b.CUSTOMER_ID = c.CUSTOMER_ID 
	 RIGHT join dev_pas.t_contract_master d on b.POLICY_ID = d.POLICY_ID
	 where  d.LIABILITY_STATE = 1
	 and c.BANK_ACCOUNT = #{bankAccount}
	 and a.CUSTOMER_NAME <![CDATA[<>]]> #{customer_name}   
	 and a.CUSTOMER_GENDER <![CDATA[<>]]> #{customer_gender} 
	 and a.CUSTOMER_CERT_TYPE <![CDATA[<>]]> #{customer_cert_type} 
	 and a.CUSTOMER_CERTI_CODE <![CDATA[<>]]> #{customer_certi_code}
	 and a.CUSTOMER_BIRTHDAY <![CDATA[<>]]> #{customer_birthday}
	</select>
	
	<!-- 保单信息列表查投保人信息 -->
	<!--//#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求
	//新增职业代码和职业名称查询逻辑与综合查询保单层中的客户信息中的职业信息查询逻辑一致
    //modify by cuiqi_wb
    //2021-11-19 -->
	<select id="JRQD_PA_queryPolicyAppCustomer" resultType="java.util.Map" parameterType="java.util.Map">
  <![CDATA[ SELECT A.POLICY_CODE,
            C.OLD_CUSTOMER_ID,
            C.CUSTOMER_ID,
            C.CUSTOMER_NAME,
            C.CUSTOMER_GENDER,
            (SELECT TCY.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCY WHERE C.COUNTRY_CODE = TCY.COUNTRY_CODE ) COUNTRY_NAME, 
            C.COUNTRY_CODE,
            C.CUSTOMER_BIRTHDAY,
            (SELECT TCT.TYPE FROM APP___PAS__DBUSER.T_CERTI_TYPE TCT WHERE  C.CUSTOMER_CERT_TYPE = TCT.CODE ) CUSTOMER_CERT_TYPENAME,
            C.CUSTOMER_CERT_TYPE,
            C.CUSTOMER_CERTI_CODE,
            C.CUST_CERT_STAR_DATE,
            C.CUST_CERT_END_DATE,
            B.MOBILE_TEL,
            B.FIXED_TEL,
            A.JOB_CODE,
            (select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = A.JOB_CODE) JOB_NAME,
            A.JOB_CODE POLICY_JOB_CODE,
            (SELECT T.JOB_UW_LEVEL_NAME FROM DEV_PAS.T_JOB_UNDERWRITE T WHERE T.JOB_UW_LEVEL_CODE IN (SELECT E.JOB_UW_LEVEL FROM DEV_PAS.T_JOB_CODE E WHERE E.JOB_CODE = C.JOB_CODE))JOB_UW_LEVEL_NAME,
            C.COMPANY_NAME,
            B.STATE,
            B.CITY,
            B.DISTRICT,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.STATE = O.CODE )  AS STATENAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.CITY = O.CODE )  AS CITYNAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.DISTRICT = O.CODE )  AS DISTRICTNAME,
            B.ADDRESS,
            B.POST_CODE,
            B.EMAIL,
            A.SOCI_SECU,
            c.tax_resident_type,
            c.HOUSE_TEL
       FROM APP___PAS__DBUSER.T_POLICY_HOLDER A,
            APP___PAS__DBUSER.T_CUSTOMER      C,
            APP___PAS__DBUSER.T_ADDRESS       B
      WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
        AND A.ADDRESS_ID = B.ADDRESS_ID
        AND A.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}]]>   
	</select>
	
	<!-- 保单信息列表查被保人信息 --> 
    <!--//#93464新核心-接口需求-移动保全2.0-短期健康险新规应对需求-客户职业取值规则优化需求
	    //新增职业代码和职业名称查询逻辑与综合查询保单层中的客户信息中的职业信息查询逻辑一致
        //modify by cuiqi_wb
        //2021-11-19 -->
	<select id="JRQD_PA_queryInsuredCustomer" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[ SELECT A.POLICY_CODE,
            C.OLD_CUSTOMER_ID,
            C.CUSTOMER_ID,
            (SELECT Z.ORDER_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED Z 
            WHERE Z.ORDER_ID=1 AND Z.POLICY_CODE=A.POLICY_CODE 
            AND Z.INSURED_ID=A.LIST_ID AND ROWNUM = 1) AS INSUREDORDER,
            C.CUSTOMER_NAME,
            C.CUSTOMER_GENDER,
            (SELECT TCY.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY TCY WHERE C.COUNTRY_CODE = TCY.COUNTRY_CODE ) COUNTRY_NAME,
            C.COUNTRY_CODE,
            A.SOCI_SECU,            
            C.CUSTOMER_BIRTHDAY,
            (SELECT TCT.TYPE FROM APP___PAS__DBUSER.T_CERTI_TYPE TCT WHERE  C.CUSTOMER_CERT_TYPE = TCT.CODE ) CUSTOMER_CERT_TYPENAME,
            C.CUSTOMER_CERT_TYPE,
            C.CUSTOMER_CERTI_CODE,
            C.CUST_CERT_STAR_DATE,
            C.CUST_CERT_END_DATE,
            B.MOBILE_TEL,
            B.FIXED_TEL,
            A.JOB_CODE,
            (select tjc.job_name from dev_pas.T_JOB_CODE tjc where tjc.job_code = A.JOB_CODE) JOB_NAME,
            A.JOB_CODE POLICY_JOB_CODE,
            (SELECT T.JOB_UW_LEVEL_NAME FROM DEV_PAS.T_JOB_UNDERWRITE T WHERE T.JOB_UW_LEVEL_CODE IN (SELECT E.JOB_UW_LEVEL FROM DEV_PAS.T_JOB_CODE E WHERE E.JOB_CODE = C.JOB_CODE))JOB_UW_LEVEL_NAME,
            C.COMPANY_NAME,
            B.EMAIL,
            B.POST_CODE,
            A.RELATION_TO_PH,
            B.STATE,
            B.CITY,
            B.DISTRICT,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.STATE = O.CODE )  AS STATENAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.CITY = O.CODE )  AS CITYNAME,
            (SELECT O.NAME FROM  APP___PAS__DBUSER.T_DISTRICT O WHERE B.DISTRICT = O.CODE )  AS DISTRICTNAME,
            B.ADDRESS,
            c.tax_resident_type,
            c.HOUSE_TEL
       FROM APP___PAS__DBUSER.T_INSURED_LIST A
            LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER  C ON A.CUSTOMER_ID = C.CUSTOMER_ID
            LEFT JOIN APP___PAS__DBUSER.T_ADDRESS  B ON A.ADDRESS_ID = B.ADDRESS_ID
            WHERE A.POLICY_CODE= #{policy_code,jdbcType=VARCHAR}
   ]]> 
	</select>
	
	<!-- 传保单号并且 传姓名或者证件类型证件号任意一项 -->
	<select id="JRQD_PA_queryPolicyCodeAndIDTypeName" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CONTRACT_AGENT B,
              APP___PAS__DBUSER.T_CUSTOMER      A
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
          <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND B.AGENT_CODE = #{agent_code} ]]></if>
          <include refid="JRQD_PA_customerPolicyList" />
       UNION
      <![CDATA[ SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CONTRACT_AGENT B,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1
          AND C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
          <include refid="JRQD_PA_customerPolicyList" /> 
          <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND B.AGENT_CODE = #{agent_code} ]]></if>      
	</select>
	
	<!-- 传保单号并且 不传姓名证件类型证件号，投被保人是否同一人 -->
	<select id="JRQD_PA_queryPolicyCodeAndNotIDTypeName" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         SELECT A.CUSTOMER_NAME,
            A.CUSTOMER_GENDER,
            A.CUSTOMER_BIRTHDAY,
            A.CUSTOMER_CERT_TYPE,
            A.CUSTOMER_CERTI_CODE
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CONTRACT_AGENT B,
              APP___PAS__DBUSER.T_CUSTOMER      A
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1 
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}   ]]>
          <include refid="JRQD_PA_customerPolicyList" />
          <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND B.AGENT_CODE = #{agent_code} ]]></if> 
        <![CDATA[   
       UNION
      		SELECT A.CUSTOMER_NAME,
            A.CUSTOMER_GENDER,           
            A.CUSTOMER_BIRTHDAY,
            A.CUSTOMER_CERT_TYPE,
            A.CUSTOMER_CERTI_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CONTRACT_AGENT B,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1
          AND C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
          <include refid="JRQD_PA_customerPolicyList" />
          <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND B.AGENT_CODE = #{agent_code} ]]></if> 
	</select>
	
	<!-- 不传保单号  传姓名或者证件类型证件号任意一项-->
	<select id="JRQD_PA_queryNotPolicyAndIDTypeName" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
         SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CONTRACT_AGENT B,
              APP___PAS__DBUSER.T_CUSTOMER      A
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1   
          ]]>
          <include refid="JRQD_PA_customerPolicyList" />
          <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND B.AGENT_CODE = #{agent_code} ]]></if> 
       UNION
      <![CDATA[ SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CONTRACT_AGENT B,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1 
          AND C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          ]]>
          <include refid="JRQD_PA_customerPolicyList" /> 
          <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND B.AGENT_CODE = #{agent_code} ]]></if> 
	</select>
	
	<!-- 同步该客户下查询保单 -->
	<select id="JRQD_PA_queryAllPolicysOfTheCustomer" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         SELECT C.POLICY_CODE,D.ACKNOWLEDGE_DATE, A.OLD_CUSTOMER_ID 
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
              APP___PAS__DBUSER.T_CONTRACT_MASTER B,
              APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT D
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.POLICY_ID = B.POLICY_ID
          AND C.POLICY_ID=D.POLICY_ID]]>
          <include refid="JRQD_PA_customerPolicyList" />
       UNION
      <![CDATA[ SELECT C.POLICY_CODE,D.ACKNOWLEDGE_DATE, A.OLD_CUSTOMER_ID 
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_CONTRACT_MASTER B,
              APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT D
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.POLICY_CODE=B.POLICY_CODE
          AND C.POLICY_ID=D.POLICY_ID]]>
          <include refid="JRQD_PA_customerPolicyList" />  
	</select>
	
	<!-- 客户名下所有保单(其他客户号) -->
	<select id="JRQD_PA_queryAllPolicyOfTheCustomer" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         SELECT C.POLICY_CODE,D.ACKNOWLEDGE_DATE
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
              APP___PAS__DBUSER.T_CONTRACT_MASTER B,
              APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT D
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.POLICY_ID = B.POLICY_ID
          AND C.POLICY_ID=D.POLICY_ID]]>
          <include refid="JRQD_PA_customerOtherPolicyList" />
       UNION
      <![CDATA[ SELECT C.POLICY_CODE,D.ACKNOWLEDGE_DATE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_CONTRACT_MASTER B,
              APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT D
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.POLICY_CODE=B.POLICY_CODE
          AND C.POLICY_ID=D.POLICY_ID]]>
          <include refid="JRQD_PA_customerOtherPolicyList" />  
	</select>
	
	<!-- 心圆福职域保单或深圳医保保单列表查询-->
	<select id="JRQD_PA_queryxyfzPolicyList" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
              APP___PAS__DBUSER.T_CONTRACT_MASTER B
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.POLICY_ID = B.POLICY_ID
          AND (EXISTS (SELECT 1 FROM  APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Y
          WHERE Z.BUSINESS_PRD_ID=Y.BUSI_PRD_ID AND Y.POLICY_CODE=B.POLICY_CODE AND Z.OCCUPATION_INSUREANCE_FLAG='1')
          	   OR B.MEDICAL_INSURANCE_CARD in ('1'))
          ]]>
          <include refid="JRQD_PA_customerPolicyList" />
          <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND B.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]></if>
          <if test=" cust_cert_star_date != null  "><![CDATA[ AND B.VALIDATE_DATE > #{cust_cert_star_date} ]]></if>
          <if test=" cust_cert_end_date != null  "><![CDATA[ AND B.VALIDATE_DATE < #{cust_cert_end_date} ]]></if>
       UNION
      <![CDATA[ SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_CONTRACT_MASTER B
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.POLICY_CODE=B.POLICY_CODE
          AND (EXISTS (SELECT 1 FROM  APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD Y
          WHERE Z.BUSINESS_PRD_ID=Y.BUSI_PRD_ID AND Y.POLICY_CODE=B.POLICY_CODE AND Z.OCCUPATION_INSUREANCE_FLAG='1')
          		OR B.MEDICAL_INSURANCE_CARD in ('1'))
          ]]>
          <include refid="JRQD_PA_customerPolicyList" />  
          <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND B.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]></if>
          <if test=" cust_cert_star_date != null  "><![CDATA[ AND B.VALIDATE_DATE > #{cust_cert_star_date} ]]></if>
          <if test=" cust_cert_end_date != null  "><![CDATA[ AND B.VALIDATE_DATE < #{cust_cert_end_date} ]]></if>
	</select>
	
	<!-- 心圆福职域保单或深圳医保保单详情查询-->
	<select id="JRQD_PA_queryPolicyDetails" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         SELECT E.ACCOUNT_BANK,
       E.ACCOUNT,
       E.NEXT_ACCOUNT_BANK,
       E.NEXT_ACCOUNT,
       A.POLICY_CODE,
       A.POLICY_ID,
       A.MEDICAL_INSURANCE_CARD,
       A.CALL_TIME_LIST,
       (SELECT Y.PRODUCT_NAME_STD
          FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Y
         WHERE Y.BUSINESS_PRD_ID = B.BUSI_PRD_ID) PRODUCT_NAME_SYS,
       B.BUSI_PROD_CODE,
       (SELECT MAX(Z.PREM_FREQ)
          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z
         WHERE B.BUSI_ITEM_ID = Z.BUSI_ITEM_ID
           AND Z.POLICY_ID = B.POLICY_ID) PREM_FREQ,
       (SELECT SUM(Z.AMOUNT)
          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z
         WHERE B.BUSI_ITEM_ID = Z.BUSI_ITEM_ID
           AND Z.POLICY_ID = B.POLICY_ID) AMOUNT,
       (SELECT SUM(Z.STD_PREM_AF)
          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z
         WHERE B.BUSI_ITEM_ID = Z.BUSI_ITEM_ID
           AND Z.POLICY_ID = B.POLICY_ID) STD_PREM_AF,
        B.VALIDATE_DATE,
        B.MATURITY_DATE,
       (SELECT SUM(Z.TOTAL_PREM_AF)
          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z
         WHERE B.BUSI_ITEM_ID = Z.BUSI_ITEM_ID
           AND Z.POLICY_ID = B.POLICY_ID) TOTAL_PREM_AF,
       (SELECT MAX(Z.FINISH_TIME)
          FROM APP___PAS__DBUSER.T_PREM_ARAP Z
         WHERE Z.FEE_TYPE IN ('G003010000', 'G003100000')
           AND Z.DERIV_TYPE = '003'
           AND Z.BUSI_PROD_CODE = B.BUSI_PROD_CODE
           AND Z.POLICY_CODE = A.POLICY_CODE) FINISH_TIME,
       (SELECT MAX(Z.DUE_TIME) FROM APP___PAS__DBUSER.T_PREM Z 
           WHERE Z.POLICY_CODE = A.POLICY_CODE AND Z.BUSI_PROD_CODE = B.BUSI_PROD_CODE AND Z.FEE_SCENE_CODE='NB') DUE_TIME,
       (SELECT MAX(Z.PAY_DUE_DATE)
          FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND Z
         WHERE Z.POLICY_ID = A.POLICY_ID
           AND Z.BUSI_ITEM_ID = B.BUSI_ITEM_ID) PAY_DUE_DATE,
       (SELECT MAX(Z.CONDITION_DESC)
          FROM APP___PAS__DBUSER.T_POLICY_CONDITION Z
         WHERE Z.BUSI_ITEM_ID = B.BUSI_ITEM_ID) CONDITION_DESC,
       E.PAY_NEXT,
       E.PAY_MODE,
       E.PAY_LOCATION,
       C.CUSTOMER_NAME,
       C.CUSTOMER_CERT_TYPE,
       C.CUSTOMER_CERTI_CODE,
       C.CUSTOMER_BIRTHDAY,
       C.CUSTOMER_GENDER,
       C.CUST_CERT_STAR_DATE,
       C.CUST_CERT_END_DATE,
       C.COUNTRY_CODE,
       D.JOB_CODE,
       (SELECT Z.JOB_NAME
          FROM APP___PAS__DBUSER.T_JOB_CODE Z
         WHERE D.JOB_CODE = Z.JOB_CODE) JOB_NAME,
       (SELECT Z2.NAME
          FROM APP___PAS__DBUSER.T_ADDRESS Z1,APP___PAS__DBUSER.T_DISTRICT Z2
         WHERE D.ADDRESS_ID = Z1.ADDRESS_ID AND Z1.STATE = Z2.CODE AND Z2.DISTRICT_LEVEL = '1') STATE,
       (SELECT Z2.NAME
          FROM APP___PAS__DBUSER.T_ADDRESS Z1,APP___PAS__DBUSER.T_DISTRICT Z2
         WHERE D.ADDRESS_ID = Z1.ADDRESS_ID AND Z1.CITY = Z2.CODE AND Z2.DISTRICT_LEVEL = '2') CITY,
       (SELECT Z2.NAME
          FROM APP___PAS__DBUSER.T_ADDRESS Z1,APP___PAS__DBUSER.T_DISTRICT Z2
         WHERE D.ADDRESS_ID = Z1.ADDRESS_ID AND Z1.DISTRICT = Z2.CODE AND Z2.DISTRICT_LEVEL = '3') DISTRICT,
       (SELECT Z.ADDRESS
          FROM APP___PAS__DBUSER.T_ADDRESS Z
         WHERE D.ADDRESS_ID = Z.ADDRESS_ID) ADDRESS,
       (SELECT Z.MOBILE_TEL
          FROM APP___PAS__DBUSER.T_ADDRESS Z
         WHERE D.ADDRESS_ID = Z.ADDRESS_ID) MOBILE_TEL,
       (SELECT Z.EMAIL
          FROM APP___PAS__DBUSER.T_ADDRESS Z
         WHERE D.ADDRESS_ID = Z.ADDRESS_ID) EMAIL,
         B.MASTER_BUSI_ITEM_ID,
           (SELECT Z.CHARGE_YEAR
           FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z
          WHERE B.BUSI_ITEM_ID = Z.BUSI_ITEM_ID
            AND Z.POLICY_ID = B.POLICY_ID
            AND Z.IS_MASTER_ITEM = '1') PAYENDYEAR,
        (SELECT Z.CHARGE_PERIOD
           FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z
          WHERE B.BUSI_ITEM_ID = Z.BUSI_ITEM_ID
            AND Z.POLICY_ID = B.POLICY_ID
            AND Z.IS_MASTER_ITEM = '1') PAYENDYEARFLAG,
        (SELECT Z.UNIT 
           FROM  APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
          WHERE Z.BUSI_ITEM_ID = B.BUSI_ITEM_ID
           AND Z.POLICY_ID = B.POLICY_ID) UNIT,
        (SELECT Z.COUNT_WAY 
           FROM  APP___PAS__DBUSER.T_CONTRACT_PRODUCT Z 
          WHERE Z.BUSI_ITEM_ID = B.BUSI_ITEM_ID
           AND Z.POLICY_ID = B.POLICY_ID) COUNT_WAY,
        (SELECT Z.BANK_YL_PHONE
        	FROM  APP___PAS__DBUSER.T_MEDICAL_CARD Z
         WHERE Z.POLICY_CODE = A.POLICY_CODE) BANK_YL_PHONE,
        (SELECT Z.SIGN_AFFIRM_CODE
        	FROM  APP___PAS__DBUSER.T_MEDICAL_CARD Z
         WHERE Z.POLICY_CODE = A.POLICY_CODE) SIGN_AFFIRM_CODE,
        (SELECT Z.AGENT_CODE
        	  FROM  APP___PAS__DBUSER.T_CONTRACT_AGENT Z
           WHERE Z.POLICY_CODE = A.POLICY_CODE) AGENT_CODE
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    A,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B,
       APP___PAS__DBUSER.T_CUSTOMER           C,
       APP___PAS__DBUSER.T_POLICY_HOLDER      D,
       APP___PAS__DBUSER.T_PAYER_ACCOUNT      E
 WHERE A.POLICY_ID = B.POLICY_ID
   AND A.POLICY_ID = D.POLICY_ID
   AND C.CUSTOMER_ID = D.CUSTOMER_ID
   AND A.POLICY_ID = E.POLICY_ID
   AND (EXISTS (SELECT 1 FROM  APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z WHERE Z.BUSINESS_PRD_ID=B.BUSI_PRD_ID AND Z.OCCUPATION_INSUREANCE_FLAG='1')
    	OR A.MEDICAL_INSURANCE_CARD in ('1')
    	)
   AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}
          ]]>
        <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND C.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND C.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND C.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND C.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND C.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</select>
	
	<!--心圆福职域保单详情查询被保人信息 -->
	<select id="JRQD_PA_findInsuredListBypolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
         <![CDATA[    
        SELECT A.CUSTOMER_ID, A.CUSTOMER_NAME, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_CERTI_CODE,A.CUSTOMER_BIRTHDAY,
       			A.CUSTOMER_GENDER,A.COUNTRY_CODE,A.CUST_CERT_STAR_DATE,A.CUST_CERT_END_DATE,B.RELATION_TO_PH,C.MOBILE_TEL,
       			B.JOB_CODE,
      			(SELECT Z.JOB_NAME
          			FROM APP___PAS__DBUSER.T_JOB_CODE Z
         			WHERE B.JOB_CODE = Z.JOB_CODE) JOB_NAME,
         		(SELECT Z2.NAME
          			FROM APP___PAS__DBUSER.T_ADDRESS Z1,APP___PAS__DBUSER.T_DISTRICT Z2
         				WHERE C.ADDRESS_ID = Z1.ADDRESS_ID 
         					AND Z1.STATE = Z2.CODE 
         					AND Z2.DISTRICT_LEVEL = '1') STATE,
      			(SELECT Z2.NAME
          			FROM APP___PAS__DBUSER.T_ADDRESS Z1,APP___PAS__DBUSER.T_DISTRICT Z2
         				WHERE C.ADDRESS_ID = Z1.ADDRESS_ID 
         					AND Z1.CITY = Z2.CODE 
         					AND Z2.DISTRICT_LEVEL = '2') CITY,
      			(SELECT Z2.NAME
          				FROM APP___PAS__DBUSER.T_ADDRESS Z1,APP___PAS__DBUSER.T_DISTRICT Z2
         					WHERE C.ADDRESS_ID = Z1.ADDRESS_ID 
         						AND Z1.DISTRICT = Z2.CODE 
         						AND Z2.DISTRICT_LEVEL = '3') DISTRICT,
       			C.ADDRESS
     	FROM APP___PAS__DBUSER.T_CUSTOMER  A,
			APP___PAS__DBUSER.T_INSURED_LIST B,
			APP___PAS__DBUSER.T_ADDRESS C
		WHERE A.CUSTOMER_ID = B.CUSTOMER_ID
				AND B.ADDRESS_ID = C.ADDRESS_ID
				AND B.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}
           ]]>
	</select>

	<!--心圆福职域保单详情查询被保人告知信息 -->
	<select id="JRQD_PA_findInsuredQueByApplicyCode" resultType="java.util.Map"
	parameterType="java.util.Map">
	<![CDATA[
		SELECT Z1.SURVEY_MODULE_RESULT,
				Z2.SURVEY_CODE 
			FROM APP___PAS__DBUSER.T_QUESTIONAIRE_CUSTOMER Z1,APP___PAS__DBUSER.T_QUESTIONAIRE_INFO Z2 
		WHERE 1 = 1
			AND Z1.SURVEY_QUESTION_ID = Z2.SURVEY_QUESTION_ID
			AND Z1.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}
			AND Z1.CUSTOMER_ID = #{customer_id}
  		]]>
	</select>
	
    <!--查询客户是否存在一年期以上的保单 -->
	<select id="JRQD_queryYearUpPolicy" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[ select distinct ph.customer_id,m.policy_code
  from dev_pas.t_policy_holder ph
 inner join dev_pas.t_contract_master m
    on ph.policy_id = m.policy_id
   and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
              from dual)) > 1 and m.liability_state = '1')

 where ph.customer_id = #{customer_id}
union
select distinct  ph.customer_id,m.policy_code
  from dev_pas.t_policy_holder ph
 inner join dev_pas.t_contract_master m
    on ph.policy_id = m.policy_id
   and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
              from dual)) > 1 and m.liability_state = '3' and
       m.end_cause not in ('01', '03', '02', '06'))

 where ph.customer_id = #{customer_id}

union
select distinct ph.customer_id,m.policy_code
  from dev_pas.t_policy_holder ph
 inner join dev_pas.t_contract_master m
    on ph.policy_id = m.policy_id
   and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
              from dual)) > 1 and m.liability_state = '4' and
       m.lapse_cause not in ('1', '6'))

 where ph.customer_id = #{customer_id}
 union
 select distinct l.customer_id,m.policy_code
  from dev_pas.t_insured_list l
 inner join dev_pas.t_contract_master m
    on l.policy_id = m.policy_id
   and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
              from dual)) > 1 and m.liability_state = '1')

 where l.customer_id = #{customer_id}
union
select distinct  l.customer_id,m.policy_code
  from dev_pas.t_insured_list l
 inner join dev_pas.t_contract_master m
    on l.policy_id = m.policy_id
   and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
              from dual)) > 1 and m.liability_state = '3' and
       m.end_cause not in ('01', '03', '02', '06'))

 where l.customer_id = #{customer_id}

union
select distinct  l.customer_id,m.policy_code
  from dev_pas.t_insured_list l
 inner join dev_pas.t_contract_master m
    on l.policy_id = m.policy_id
   and (abs((select ((months_between(m.VALIDATE_DATE, m.EXPIRY_DATE)) / 12)
              from dual)) > 1 and m.liability_state = '4' and
       m.lapse_cause not in ('1', '6'))

 where l.customer_id = #{customer_id}  ]]>          
	</select>
	
	<!-- 传保单号并且 传姓名或者证件类型证件号任意一项 -->
	<select id="JRQD_PA_queryPolicyCodeAndIDTypeNamecustomerID" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         SELECT C.POLICY_CODE,
         A.customer_id
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER c,
              APP___PAS__DBUSER.T_CUSTOMER      A,
              APP___PAS__DBUSER.t_contract_master w
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
            and  c.policy_code = w.policy_code 
            and w.liability_state <> '0' ]]>
            <if test=" resident_name != null and resident_name != ''  ">
           <![CDATA[ and  exists (select 1 from dev_pas.t_contract_agent l where l.policy_code = w.policy_code and l.agent_code = #{resident_name,jdbcType=VARCHAR}  )]]></if>
          <![CDATA[  
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
          <include refid="JRQD_PA_customerWhereCondition" />
       UNION
      <![CDATA[ SELECT C.POLICY_CODE,a.customer_id
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E,
              APP___PAS__DBUSER.t_contract_master w 
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          and c.POLICY_CODE = w.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          and w.liability_state <> '0' ]]>
          <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND W.VALIDATE_DATE >= #{validate_date} ]]></if>	
          <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND W.VALIDATE_DATE <= #{suspend_date} ]]></if>
          <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND W.LIABILITY_STATE = #{liability_state} ]]></if>
           <if test=" resident_name != null and resident_name != ''  ">
           <![CDATA[ and  exists (select 1 from dev_pas.t_contract_agent l where l.policy_code = w.policy_code and l.agent_code = #{resident_name,jdbcType=VARCHAR}  )]]></if>
          <![CDATA[
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
          <include refid="JRQD_PA_customerWhereCondition" />       
	</select>
	
	<!-- 传保单号并且 不传姓名证件类型证件号，投被保人是否同一人 -->
	<select id="JRQD_PA_queryPolicyCodeAndNotIDTypeNamecustomerID" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         SELECT A.CUSTOMER_NAME,
            A.CUSTOMER_GENDER,
            A.CUSTOMER_BIRTHDAY,
            A.CUSTOMER_CERT_TYPE,
            A.CUSTOMER_CERTI_CODE,
            A.customer_id 
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
               APP___PAS__DBUSER.t_contract_master w 
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
         and  c.policy_code = w.policy_code 
          and  w.liability_state = '4'   
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
          <include refid="JRQD_PA_customerWhereCondition" />
       UNION
      <![CDATA[ SELECT A.CUSTOMER_NAME,
            A.CUSTOMER_GENDER,           
            A.CUSTOMER_BIRTHDAY,
            A.CUSTOMER_CERT_TYPE,
            A.CUSTOMER_CERTI_CODE,
            a.customer_id 
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E,
                APP___PAS__DBUSER.t_contract_master w 
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          and c.POLICY_CODE = w.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          and  w.liability_state = '4'   
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}]]>
          <include refid="JRQD_PA_customerWhereCondition" />
        
	</select>
	
	<!-- 不传保单号  传姓名或者证件类型证件号任意一项-->
	<select id="JRQD_PA_queryNotPolicyAndIDTypeNamecustomerID" resultType="java.util.Map" parameterType="java.util.Map">
	
	<if test="housekeeper_flag!=null  and housekeeper_flag =='0'.toString()">
         <![CDATA[
         SELECT DISTINCT C.POLICY_CODE,
        A.customer_id 
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
              APP___PAS__DBUSER.t_contract_master w 
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
         and  c.policy_code = w.policy_code 
         and w.liability_state <> '0' 
         
          ]]>
           <include refid="JRQD_PA_customerInjuredWhereCondition" />
     </if>    
     <if test="housekeeper_flag!=null  and housekeeper_flag =='1'.toString()">
      <![CDATA[ SELECT DISTINCT C.POLICY_CODE,a.customer_id 
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E,
              APP___PAS__DBUSER.t_contract_master w 
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          and c.POLICY_CODE = w.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          and w.liability_state <> '0' 
            
          ]]>
          <include refid="JRQD_PA_customerInjuredWhereCondition" /> 
          </if>
       <if test="housekeeper_flag!=null  and housekeeper_flag =='2'.toString()">
       <![CDATA[
       select distinct r.policy_code ,a.customer_id from dev_pas.t_contract_agent l,
  dev_pas.t_contract_master r,
  dev_pas.t_policy_holder e,
  dev_pas.t_customer a 
   where 1=1
   and r.policy_code = l.policy_code
   and r.policy_id = e.policy_id
   and e.customer_id = a.customer_id
     and r.liability_state <> '0'
   ]]>
   <if test=" resident_name != null and resident_name != ''  ">
      <![CDATA[ and  exists (select 1 from dev_pas.t_contract_agent l where l.policy_code = r.policy_code and l.agent_code = #{resident_name,jdbcType=VARCHAR}  )]]></if>
    <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
   <![CDATA[ UNION
    select distinct r.policy_code ,a.customer_id from dev_pas.t_contract_agent l,
  dev_pas.t_contract_master r,
  dev_pas.t_insured_list e,
  dev_pas.t_customer a 
   where 1=1
   and r.policy_code = l.policy_code
   and r.policy_id = e.policy_id
   and e.customer_id = a.customer_id
    ]]>
   <if test=" resident_name != null and resident_name != ''  ">
       <![CDATA[ and  exists (select 1 from dev_pas.t_contract_agent l where l.policy_code = r.policy_code and l.agent_code = #{resident_name,jdbcType=VARCHAR}  )]]></if>
    <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>  
        </if>
	</select>
	
	<!-- 通过flag判断投保人或被保人对应的保单号升序排序 -->
	<select id="JRQD_PA_queryRenewalPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
        <if test=" flag == 'zero' ">
        <![CDATA[
         SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
              APP___PAS__DBUSER.T_CONTRACT_AGENT  B
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
        AND C.POLICY_CODE = B.POLICY_CODE
        AND B.IS_CURRENT_AGENT = 1 
        ]]>
         <if test=" agent_code != null and agent_code != ''  ">
         <![CDATA[ AND B.AGENT_CODE=#{agent_code,jdbcType=VARCHAR}]]>
         </if> 
         <if test=" policy_code != null and policy_code != ''  ">
         <![CDATA[  AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]>
         </if>         
          <include refid="JRQD_PA_customerPolicyList" />
         <![CDATA[ ORDER BY C.POLICY_CODE ASC ]]>
       </if>
       
      <if test=" flag == 'one' ">
      <![CDATA[ SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E,
              APP___PAS__DBUSER.T_CONTRACT_AGENT  B
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.LIST_ID = D.INSURED_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE ]]>
          <if test=" agent_code != null and agent_code != ''  ">
         <![CDATA[ AND B.AGENT_CODE=#{agent_code,jdbcType=VARCHAR}]]>
         </if> 
          <if test=" policy_code != null and policy_code != ''  ">
          <![CDATA[  AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]>
          </if> 
          <include refid="JRQD_PA_customerPolicyList" />  
          <![CDATA[ ORDER BY C.POLICY_CODE ASC ]]>
          </if>    
	</select>
	
	<!-- 投保人或被保人对应的保单号升序排序 -->
	<select id="JRQD_PA_queryRenewalPolicyCodeAll" resultType="java.util.Map" parameterType="java.util.Map">
       SELECT Z.POLICY_CODE FROM (
        <![CDATA[
         SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
              APP___PAS__DBUSER.T_CONTRACT_AGENT  B
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
        AND C.POLICY_CODE = B.POLICY_CODE
        AND B.IS_CURRENT_AGENT = 1
        ]]>
         <if test=" agent_code != null and agent_code != ''  ">
          <![CDATA[  AND B.AGENT_CODE=#{agent_code,jdbcType=VARCHAR} ]]>
          </if>
          <if test=" policy_code != null and policy_code != ''  ">
          <![CDATA[  AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]>
          </if> 
          <include refid="JRQD_PA_customerPolicyList" />
       UNION
      <![CDATA[ SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E,
              APP___PAS__DBUSER.T_CONTRACT_AGENT  B
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.LIST_ID = D.INSURED_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE ]]>
          <if test=" agent_code != null and agent_code != ''  ">
          <![CDATA[  AND B.AGENT_CODE=#{agent_code,jdbcType=VARCHAR} ]]>
          </if>
          <if test=" policy_code != null and policy_code != ''  ">
          <![CDATA[  AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]>
          </if> 
          <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND E.VALIDATE_DATE >= #{validate_date} ]]></if>	
          <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND E.VALIDATE_DATE <= #{suspend_date} ]]></if>
          <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND E.LIABILITY_STATE = #{liability_state} ]]></if>
          <include refid="JRQD_PA_customerPolicyList" />   
          ) Z
          <![CDATA[ ORDER BY Z.POLICY_CODE ASC ]]>  
	</select>
	
	<!-- 投保人保单查询 -->
	<select id="JRQD_PA_queryPolicyHoderContNo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
                     SELECT W.POLICY_CODE,W.POLICY_ID,W.VALIDATE_DATE,W.LIABILITY_STATE,
            (SELECT Y.CAUSE_NAME FROM APP___PAS__DBUSER.T_END_CAUSE Y WHERE Y.CAUSE_CODE=W.END_CAUSE) AS END_NAME,
            (SELECT Y.CAUSE_DESC FROM APP___PAS__DBUSER.T_LAPSE_CAUSE Y WHERE Y.CAUSE_CODE=W.LAPSE_CAUSE) AS LAPSE_NAME,
            (SELECT (SELECT Z.PRODUCT_ABBR_NAME FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z WHERE Z.BUSINESS_PRD_ID=G.BUSI_PRD_ID) 
			FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD G WHERE G.MASTER_BUSI_ITEM_ID IS NULL AND  G.POLICY_CODE=W.POLICY_CODE AND ROWNUM=1) AS NAMETD,
			(SELECT Z.TAX_EXTENSION_FLAG FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD G,APP___PDS__DBUSER.T_BUSINESS_PRODUCT Z 
			WHERE Z.BUSINESS_PRD_ID=G.BUSI_PRD_ID AND Z.TAX_EXTENSION_FLAG='1' AND G.POLICY_CODE=W.POLICY_CODE AND ROWNUM=1) AS TAXEXTENSIONFLAG,
(SELECT CC.RELATION_TO_PH
         FROM APP___PAS__DBUSER.T_INSURED_LIST    CC,
              APP___PAS__DBUSER.T_BENEFIT_INSURED DD,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD EE
         WHERE 1=1
           AND CC.LIST_ID = DD.INSURED_ID
          AND DD.ORDER_ID = 1
          AND DD.POLICY_CODE = CC.POLICY_CODE
          AND EE.MASTER_BUSI_ITEM_ID IS NULL
          AND EE.BUSI_ITEM_ID=DD.BUSI_ITEM_ID
          AND EE.POLICY_CODE=DD.POLICY_CODE AND EE.POLICY_CODE=W.POLICY_CODE  AND ROWNUM=1) AS RELATION_TO_PH,
          (SELECT (SELECT ZZ.RELATION_NAME FROM DEV_PAS.T_LA_PH_RELA ZZ WHERE ZZ.RELATION_CODE=CC.RELATION_TO_PH)
         FROM APP___PAS__DBUSER.T_INSURED_LIST    CC,
              APP___PAS__DBUSER.T_BENEFIT_INSURED DD,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD EE
         WHERE 1=1
           AND CC.LIST_ID = DD.INSURED_ID
          AND DD.ORDER_ID = 1
          AND DD.POLICY_CODE = CC.POLICY_CODE
          AND EE.MASTER_BUSI_ITEM_ID IS NULL
          AND EE.BUSI_ITEM_ID=DD.BUSI_ITEM_ID
          AND EE.POLICY_CODE=DD.POLICY_CODE AND EE.POLICY_CODE=W.POLICY_CODE AND ROWNUM=1) AS RELATION_NAME
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
                   APP___PAS__DBUSER.T_CONTRACT_MASTER W 
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
         AND  C.POLICY_CODE = W.POLICY_CODE     
          ]]>
           <include refid="JRQD_PA_customerInjuredWhereCondition" />
	</select>
	
	<!-- 投被保人是否对应该保单号 -->
	<select id="JRQD_PA_queryRenewalPolicyCodeCheck" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E,
              APP___PAS__DBUSER.T_POLICY_HOLDER F,
              APP___PAS__DBUSER.T_CONTRACT_AGENT  B
        WHERE C.CUSTOMER_ID = A.CUSTOMER_ID
          AND C.LIST_ID = D.INSURED_ID
          AND C.POLICY_CODE = B.POLICY_CODE
          AND B.IS_CURRENT_AGENT = 1
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND F.POLICY_CODE=C.POLICY_CODE
          AND F.CUSTOMER_ID=C.CUSTOMER_ID
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          ]]> 
           <if test=" agent_code != null and agent_code != '' ">
         AND B.AGENT_CODE=#{agent_code,jdbcType=VARCHAR}
          </if>
          
          <if test=" policy_code != null and policy_code != '' ">
          AND C.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}
          </if>
          <include refid="JRQD_PA_customerPolicyList" />
          <if test=" validate_date  != null and validate_date != '' "><![CDATA[  AND E.VALIDATE_DATE >= #{validate_date} ]]></if>	
          <if test=" suspend_date != null and suspend_date != ''  "><![CDATA[  AND E.VALIDATE_DATE <= #{suspend_date} ]]></if>
          <if test=" liability_state != null and liability_state != ''  "><![CDATA[  AND E.LIABILITY_STATE = #{liability_state} ]]></if>
          <![CDATA[ ORDER BY C.POLICY_CODE ASC ]]>       
	</select>
	
	<!-- 通过五要素查询投被保人名下保单-->
	<select id="JRQD_PA_findPolicyCodeByCustomerMessage" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
         SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
              APP___PAS__DBUSER.T_CUSTOMER      A,
              APP___PAS__DBUSER.T_CONTRACT_MASTER B,
              APP___PAS__DBUSER.T_CONTRACT_AGENT T
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND T.POLICY_CODE = C.POLICY_CODE
          AND T.IS_CURRENT_AGENT=1
          AND B.POLICY_CODE = C.POLICY_CODE
          AND B.LIABILITY_STATE != 3
          AND T.AGENT_CODE = #{agentcode,jdbcType=VARCHAR}
          ]]>
          <include refid="JRQD_PA_customerPolicyList" />
       UNION
      <![CDATA[ SELECT C.POLICY_CODE
         FROM APP___PAS__DBUSER.T_INSURED_LIST    C,
              APP___PAS__DBUSER.T_CUSTOMER        A,
              APP___PAS__DBUSER.T_BENEFIT_INSURED D,
              APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E,
              APP___PAS__DBUSER.T_CONTRACT_AGENT T
        WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
          AND C.LIST_ID = D.INSURED_ID
          AND D.ORDER_ID = 1
          AND D.POLICY_CODE = C.POLICY_CODE
          AND T.POLICY_CODE = C.POLICY_CODE
          AND T.IS_CURRENT_AGENT=1
          AND E.MASTER_BUSI_ITEM_ID IS NULL
          AND E.BUSI_ITEM_ID=D.BUSI_ITEM_ID
          AND E.POLICY_CODE=D.POLICY_CODE
          AND E.LIABILITY_STATE != 3
          AND T.AGENT_CODE = #{agentcode,jdbcType=VARCHAR}
          ]]>
          <include refid="JRQD_PA_customerPolicyList" /> 
	</select>
	
	<!-- 通过保单号查询保单信息-->
	<select id="JRQD_PA_queryPolicyMessage" resultType="java.util.Map" parameterType="java.util.Map">
	     <if test=" flagtype != null and flagtype == '1'.toString() ">
         <![CDATA[
         SELECT Z.POLICY_CODE,
       (SELECT WM_CONCAT(B.PRODUCT_ABBR_NAME)
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
               APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
         WHERE B.BUSINESS_PRD_ID = A.BUSI_PRD_ID
           AND A.POLICY_ID = Z.POLICY_ID) RISKNAME,
       (SELECT A.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_INSURED_LIST       C,
               APP___PAS__DBUSER.T_CUSTOMER           A,
               APP___PAS__DBUSER.T_BENEFIT_INSURED    D,
               APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
         WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
           AND C.LIST_ID = D.INSURED_ID
           AND D.ORDER_ID = 1
           AND D.POLICY_CODE = C.POLICY_CODE
           AND E.MASTER_BUSI_ITEM_ID IS NULL
           AND E.BUSI_ITEM_ID = D.BUSI_ITEM_ID
           AND E.POLICY_CODE = D.POLICY_CODE
           AND E.POLICY_ID = Z.POLICY_ID) INSUREDNAME,
       (SELECT A.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
               APP___PAS__DBUSER.T_CUSTOMER      A
         WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
           AND C.POLICY_ID = Z.POLICY_ID) APPNTNAME,
       Z.VALIDATE_DATE
	 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER Z
	 WHERE Z.POLICY_CODE IN ]]>
	       <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
	        #{item}
	       </foreach>
	    <![CDATA[
	   AND EXISTS (SELECT 1
	          FROM APP___PAS__DBUSER.T_PAY_PLAN Y
	         WHERE Y.POLICY_ID = Z.POLICY_ID
	           AND Y.PAY_PLAN_TYPE = '3')
          ]]>
          </if>
          
         <if test=" flagtype != null and flagtype == '2'.toString() ">
         <![CDATA[
         SELECT Z.POLICY_CODE,
       (SELECT WM_CONCAT(B.PRODUCT_ABBR_NAME)
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
               APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
         WHERE B.BUSINESS_PRD_ID = A.BUSI_PRD_ID
           AND A.POLICY_ID = Z.POLICY_ID) RISKNAME,
       (SELECT A.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_INSURED_LIST       C,
               APP___PAS__DBUSER.T_CUSTOMER           A,
               APP___PAS__DBUSER.T_BENEFIT_INSURED    D,
               APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
         WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
           AND C.LIST_ID = D.INSURED_ID
           AND D.ORDER_ID = 1
           AND D.POLICY_CODE = C.POLICY_CODE
           AND E.MASTER_BUSI_ITEM_ID IS NULL
           AND E.BUSI_ITEM_ID = D.BUSI_ITEM_ID
           AND E.POLICY_CODE = D.POLICY_CODE
           AND E.POLICY_ID = Z.POLICY_ID) INSUREDNAME,
       (SELECT A.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
               APP___PAS__DBUSER.T_CUSTOMER      A
         WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
           AND C.POLICY_ID = Z.POLICY_ID) APPNTNAME,
       Z.VALIDATE_DATE
	 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER Z
	 WHERE Z.POLICY_CODE IN ]]>
	       <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
	        #{item}
	       </foreach>
	    <![CDATA[
	      AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE X,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE Y 
          WHERE X.POLICY_ID=Z.POLICY_ID AND X.CHANGE_ID=Y.CHANGE_ID AND Y.ACCEPT_STATUS='18')
          ]]>
         </if>
         
         <if test=" flagtype != null and flagtype == '3'.toString() ">
         <![CDATA[
         SELECT Z.POLICY_CODE,
       (SELECT WM_CONCAT(B.PRODUCT_ABBR_NAME)
          FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
               APP___PDS__DBUSER.T_BUSINESS_PRODUCT   B
         WHERE B.BUSINESS_PRD_ID = A.BUSI_PRD_ID
           AND A.POLICY_ID = Z.POLICY_ID) RISKNAME,
       (SELECT A.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_INSURED_LIST       C,
               APP___PAS__DBUSER.T_CUSTOMER           A,
               APP___PAS__DBUSER.T_BENEFIT_INSURED    D,
               APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD E
         WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
           AND C.LIST_ID = D.INSURED_ID
           AND D.ORDER_ID = 1
           AND D.POLICY_CODE = C.POLICY_CODE
           AND E.MASTER_BUSI_ITEM_ID IS NULL
           AND E.BUSI_ITEM_ID = D.BUSI_ITEM_ID
           AND E.POLICY_CODE = D.POLICY_CODE
           AND E.POLICY_ID = Z.POLICY_ID) INSUREDNAME,
       (SELECT A.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER C,
               APP___PAS__DBUSER.T_CUSTOMER      A
         WHERE A.CUSTOMER_ID = C.CUSTOMER_ID
           AND C.POLICY_ID = Z.POLICY_ID) APPNTNAME,
       Z.VALIDATE_DATE
	 FROM APP___PAS__DBUSER.T_CONTRACT_MASTER Z
	 WHERE Z.POLICY_CODE IN ]]>
	       <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
	        #{item}
	       </foreach>
	    <![CDATA[
	   AND EXISTS (SELECT 1
	          FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE Y
	         WHERE Y.POLICY_ID = Z.POLICY_ID)
          ]]>
         </if>
	</select>
	
	<!--移动保全2.0： 身故受益人变更-信息变更保存校验接口 -->
	<select id="JRQD_PA_findAllCustomerInjuredNew" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	   SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD 
		FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1=1]]>
	
	    <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>		
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	<![CDATA[ 
		ORDER BY A.INSERT_TIME DESC
	]]>
	</select> 
	
	<!--移动保全2.0： 身故受益人变更-信息变更保存校验接口 -->
	<select id="JRQD_PA_findCountry" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
		select a.country_code,a.country_name,a.brief_name,a.country_english_name,a.country_uw_level 
		from APP___PAS__DBUSER.t_country a WHERE 1=1
		
	]]>
	
	<if test=" country_code != null and country_code != '' "> and a.country_code= #{country_code}</if>
    <if test=" country_name != null and country_name != '' "> AND a.country_name = #{country_name}</if>
	</select>
	
	<select id="JRQD_findAllPolicyHolderByAgentNo" resultType="java.util.Map" parameterType="java.util.Map">
	   select tph.policy_code,tcu.customer_id from APP___PAS__DBUSER.t_policy_holder tph,APP___PAS__DBUSER.t_contract_agent t,APP___PAS__DBUSER.t_customer tcu
	   where tph.policy_code = t.policy_code
	   and tcu.customer_id = tph.customer_id
	   and tcu.customer_id = #{customer_id}
	   and t.agent_code = #{agentCode}
	</select>
	
	<!-- 根据五要素查询 所有客户：P00001003078~查询客户名下所有保单 -->
	<select id="JRQD_PA_findAllCustomerByFiveFactorsPA" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  AND ROWNUM <=  1000  ]]>
			<if test="modnum != null and start != null"><![CDATA[AND MOD(A.CUSTOMER_ID , #{modnum}) = #{start}]]></if>
		<include refid="JRQD_PA_customerPolicyList" />
	</select>
		<!--集成使用r00101003066客户信息真实性校验接口  -->
	<select id="JRQD_findAllCustomerByNameAndCustomerCertiCode" resultType="java.util.Map" parameterType="java.util.Map" >
<![CDATA[	select AD.MOBILE_TEL,CU.CUSTOMER_ID,
       CM.LIABILITY_STATE,CM.POLICY_CODE
from APP___PAS__DBUSER.t_Contract_Master CM,
     APP___PAS__DBUSER.t_Policy_Holder PH,
     APP___PAS__DBUSER.T_ADDRESS AD,
     APP___PAS__DBUSER.T_CUSTOMER CU
     
     WHERE CM.POLICY_ID=PH.POLICY_ID AND  PH.ADDRESS_ID=AD.ADDRESS_ID 
     AND AD.CUSTOMER_ID=CU.CUSTOMER_ID
     
     AND AD.MOBILE_TEL=#{mobile_tel} and CM.ORGAN_CODE LIKE '${organ_code}%'
     AND CM.LIABILITY_STATE NOT IN ('3') AND CU.CUSTOMER_NAME<>#{customer_name} 
     AND CU.CUSTOMER_CERTI_CODE<>#{customer_certi_code}
     AND trunc(months_between(SYSDATE,CU.CUSTOMER_BIRTHDAY)/12)>18  
     AND EXISTS
     (SELECT 1
           FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
          WHERE PRO.COVER_PERIOD_TYPE = '0'
            AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
            AND CBP.POLICY_CODE = CM.POLICY_CODE
            AND CBP.LIABILITY_STATE != 3)
 union
    select AD.MOBILE_TEL,CU.CUSTOMER_ID,
    CM.LIABILITY_STATE,CM.POLICY_CODE
      from APP___PAS__DBUSER.t_Contract_Master CM,
     APP___PAS__DBUSER.t_Policy_Holder PH,
     APP___PAS__DBUSER.T_ADDRESS AD,
     APP___PAS__DBUSER.T_CUSTOMER CU
     
     WHERE CM.POLICY_ID=PH.POLICY_ID AND  PH.ADDRESS_ID=AD.ADDRESS_ID 
     AND AD.CUSTOMER_ID=CU.CUSTOMER_ID
     
     AND AD.MOBILE_TEL=#{mobile_tel} and CM.ORGAN_CODE LIKE '${organ_code}%'
     AND CM.LIABILITY_STATE NOT IN ('3') AND CU.CUSTOMER_NAME=#{customer_name} 
     AND CU.CUSTOMER_CERTI_CODE<>#{customer_certi_code}
     AND trunc(months_between(SYSDATE,CU.CUSTOMER_BIRTHDAY)/12)>18 
     AND EXISTS
     (SELECT 1
           FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
          WHERE PRO.COVER_PERIOD_TYPE = '0'
            AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
            AND CBP.POLICY_CODE = CM.POLICY_CODE
            AND CBP.LIABILITY_STATE != 3)        
 union     
 
 select AD.MOBILE_TEL,CU.CUSTOMER_ID,CM.LIABILITY_STATE,CM.POLICY_CODE

from APP___PAS__DBUSER.t_Contract_Master CM,
     APP___PAS__DBUSER.t_Insured_List IL,
     APP___PAS__DBUSER.T_ADDRESS AD,
     APP___PAS__DBUSER.T_CUSTOMER CU
     
     WHERE CM.POLICY_ID=IL.POLICY_ID AND  IL.ADDRESS_ID=AD.ADDRESS_ID 
     AND AD.CUSTOMER_ID=CU.CUSTOMER_ID
     
     AND AD.MOBILE_TEL=#{mobile_tel} and CM.ORGAN_CODE LIKE '${organ_code}%'
     AND CM.LIABILITY_STATE NOT IN ('3') AND CU.CUSTOMER_NAME<>#{customer_name} 
     AND CU.CUSTOMER_CERTI_CODE<>#{customer_certi_code}
     AND trunc(months_between(SYSDATE,CU.CUSTOMER_BIRTHDAY)/12)>18 
     AND EXISTS
     (SELECT 1
           FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
          WHERE PRO.COVER_PERIOD_TYPE = '0'
            AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
            AND CBP.POLICY_CODE = CM.POLICY_CODE
            AND CBP.LIABILITY_STATE != 3)     
 union
  
  select AD.MOBILE_TEL,CU.CUSTOMER_ID,CM.LIABILITY_STATE,CM.POLICY_CODE
   from APP___PAS__DBUSER.t_Contract_Master CM,
     APP___PAS__DBUSER.t_Insured_List IL,
     APP___PAS__DBUSER.T_ADDRESS AD,
     APP___PAS__DBUSER.T_CUSTOMER CU
     
     WHERE CM.POLICY_ID=IL.POLICY_ID AND  IL.ADDRESS_ID=AD.ADDRESS_ID 
     AND AD.CUSTOMER_ID=CU.CUSTOMER_ID
     
     AND AD.MOBILE_TEL=#{mobile_tel} and CM.ORGAN_CODE LIKE '${organ_code}%'
     AND CM.LIABILITY_STATE NOT IN ('3') AND CU.CUSTOMER_NAME=#{customer_name} 
     AND CU.CUSTOMER_CERTI_CODE<>#{customer_certi_code}
     AND trunc(months_between(SYSDATE,CU.CUSTOMER_BIRTHDAY)/12)>18 
     AND EXISTS
     (SELECT 1
           FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
          WHERE PRO.COVER_PERIOD_TYPE = '0'
            AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
            AND CBP.POLICY_CODE = CM.POLICY_CODE
            AND CBP.LIABILITY_STATE != 3)     
 ]]>    
  </select>
  <!--集成使用r00101003066客户信息真实性校验接口 -->
  <select id="JRQD_findAllCustomerByNameAndCustomerCertiCodeofDifferentprovince" resultType="java.util.Map" parameterType="java.util.Map">
 <![CDATA[   select distinct AD.MOBILE_TEL,CU.CUSTOMER_ID,CM.LIABILITY_STATE,CM.POLICY_CODE

from APP___PAS__DBUSER.t_Contract_Master CM,
     APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
     APP___PAS__DBUSER.t_Policy_Holder PH,
     APP___PAS__DBUSER.T_ADDRESS AD,
     APP___PAS__DBUSER.T_CUSTOMER CU
   WHERE CM.POLICY_ID=CBP.POLICY_ID AND CBP.POLICY_ID=PH.POLICY_ID AND  PH.ADDRESS_ID=AD.ADDRESS_ID 
     AND AD.CUSTOMER_ID=CU.CUSTOMER_ID
     AND CBP.MASTER_BUSI_ITEM_ID IS NULL 
     AND AD.MOBILE_TEL=#{mobile_tel} and CM.ORGAN_CODE LIKE '${organ_code}%'
     AND (CM.LIABILITY_STATE IN ('1','4') or CM.LIABILITY_STATE=3 and CM.END_CAUSE='11' )  
     AND CU.CUSTOMER_NAME<>#{customer_name} AND CU.CUSTOMER_CERTI_CODE<>#{customer_certi_code}
  	]]>
     <if test=" age_flg != null and age_flg != ''  ">
       <![CDATA[  AND trunc(months_between(SYSDATE,CU.CUSTOMER_BIRTHDAY))>18 ]]>
      </if> 
   <![CDATA[   
    AND EXISTS (SELECT 1 from APP___PDS__DBUSER.t_business_product BP WHERE BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID AND BP.COVER_PERIOD_TYPE='0' ) 
	]]>
 <![CDATA[  
  UNION
   select distinct AD.MOBILE_TEL,CU.CUSTOMER_ID,CM.LIABILITY_STATE,CM.POLICY_CODE

from APP___PAS__DBUSER.t_Contract_Master CM,
     APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
     APP___PAS__DBUSER.t_Policy_Holder PH,
     APP___PAS__DBUSER.T_ADDRESS AD,
     APP___PAS__DBUSER.T_CUSTOMER CU
   WHERE CM.POLICY_ID=CBP.POLICY_ID AND CBP.POLICY_ID=PH.POLICY_ID AND  PH.ADDRESS_ID=AD.ADDRESS_ID 
     AND AD.CUSTOMER_ID=CU.CUSTOMER_ID
     AND CBP.MASTER_BUSI_ITEM_ID IS NULL 
     AND AD.MOBILE_TEL=#{mobile_tel} and CM.ORGAN_CODE LIKE '${organ_code}%'
     AND (CM.LIABILITY_STATE IN ('1','4') or CM.LIABILITY_STATE=3 and CM.END_CAUSE='11' )  
     AND CU.CUSTOMER_NAME=#{customer_name} AND CU.CUSTOMER_CERTI_CODE<>#{customer_certi_code}
  	]]>
     <if test=" age_flg != null and age_flg != ''  ">
       <![CDATA[  AND trunc(months_between(SYSDATE,CU.CUSTOMER_BIRTHDAY))>18 ]]>
      </if> 
   <![CDATA[   
    AND EXISTS (SELECT 1 from APP___PDS__DBUSER.t_business_product BP WHERE BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID AND BP.COVER_PERIOD_TYPE='0' ) 
	]]>
   
   <if test=" insured_list != null and insured_list != ''  ">
  <![CDATA[  UNION  
  select distinct AD.MOBILE_TEL,CU.CUSTOMER_ID,CM.LIABILITY_STATE,CM.POLICY_CODE
  from APP___PAS__DBUSER.t_Contract_Master CM,
     APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
     APP___PAS__DBUSER.T_INSURED_LIST IL,
     APP___PAS__DBUSER.T_ADDRESS AD,
     APP___PAS__DBUSER.T_CUSTOMER CU
   WHERE CM.POLICY_ID=CBP.POLICY_ID AND CBP.POLICY_ID=IL.POLICY_ID AND  IL.ADDRESS_ID=AD.ADDRESS_ID 
     AND AD.CUSTOMER_ID=CU.CUSTOMER_ID
     AND CBP.MASTER_BUSI_ITEM_ID IS NULL 
     AND AD.MOBILE_TEL=#{mobile_tel} and CM.ORGAN_CODE LIKE '${organ_code}%'
     AND (CM.LIABILITY_STATE IN ('1','4') or CM.LIABILITY_STATE=3 and CM.END_CAUSE='11' )  
    AND CU.CUSTOMER_NAME<>#{customer_name} AND CU.CUSTOMER_CERTI_CODE<>#{customer_certi_code}
   AND trunc(months_between(SYSDATE,CU.CUSTOMER_BIRTHDAY))>18 ]]>
     AND EXISTS (SELECT 1 from APP___PDS__DBUSER.t_business_product BP WHERE BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID AND BP.COVER_PERIOD_TYPE='0' ) 
  <![CDATA[  union
   
    select distinct AD.MOBILE_TEL,CU.CUSTOMER_ID,CM.LIABILITY_STATE,CM.POLICY_CODE
  from APP___PAS__DBUSER.t_Contract_Master CM,
     APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
     APP___PAS__DBUSER.T_INSURED_LIST IL,
     APP___PAS__DBUSER.T_ADDRESS AD,
     APP___PAS__DBUSER.T_CUSTOMER CU
   WHERE CM.POLICY_ID=CBP.POLICY_ID AND CBP.POLICY_ID=IL.POLICY_ID AND  IL.ADDRESS_ID=AD.ADDRESS_ID 
     AND AD.CUSTOMER_ID=CU.CUSTOMER_ID
     AND CBP.MASTER_BUSI_ITEM_ID IS NULL 
     AND AD.MOBILE_TEL=#{mobile_tel} and CM.ORGAN_CODE LIKE '${organ_code}%'
     AND (CM.LIABILITY_STATE IN ('1','4') or CM.LIABILITY_STATE=3 and CM.END_CAUSE='11' )  
    AND CU.CUSTOMER_NAME=#{customer_name} AND CU.CUSTOMER_CERTI_CODE<>#{customer_certi_code}
   AND trunc(months_between(SYSDATE,CU.CUSTOMER_BIRTHDAY))>18 
     AND EXISTS (SELECT 1 from APP___PDS__DBUSER.t_business_product BP WHERE BP.BUSINESS_PRD_ID=CBP.BUSI_PRD_ID AND BP.COVER_PERIOD_TYPE='0' ) 
    ]]>
   </if> 
  </select>
  <!-- 集成使用r00101003066客户信息真实性校验接口  -->
  <select id="JRQD_findAllCustomerByNameAndCustomerCertiCodeofbeijing" resultType="java.util.Map" parameterType="java.util.Map">
   <![CDATA[      SELECT *
       FROM (SELECT A.CUSTOMER_CERTI_CODE,
                    A.CUSTOMER_CERT_TYPE,
                    A.CUSTOMER_BIRTHDAY,
                    A.CUSTOMER_GENDER,
                    A.CUSTOMER_NAME,
                    A.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
                    APP___PAS__DBUSER.T_POLICY_HOLDER   B,
                    APP___PAS__DBUSER.T_CUSTOMER        A,
                    APP___PAS__DBUSER.T_ADDRESS         S
              WHERE C.POLICY_ID = B.POLICY_ID
                AND A.CUSTOMER_ID = B.CUSTOMER_ID
                AND C.LIABILITY_STATE != 3
                AND S.ADDRESS_ID = B.ADDRESS_ID
                AND S.CUSTOMER_ID = B.CUSTOMER_ID
                AND S.MOBILE_TEL = #{mobile_tel,
              jdbcType = VARCHAR}
                AND A.CUSTOMER_ID != #{customer_id}
                AND EXISTS
              (SELECT 1
                       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                      WHERE PRO.COVER_PERIOD_TYPE = '0'
                        AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
                        AND CBP.POLICY_CODE = C.POLICY_CODE
                        AND CBP.LIABILITY_STATE != 3)
                AND C.ORGAN_CODE LIKE '${organ_code}%'
             UNION
             SELECT A.CUSTOMER_CERTI_CODE,
                    A.CUSTOMER_CERT_TYPE,
                    A.CUSTOMER_BIRTHDAY,
                    A.CUSTOMER_GENDER,
                    A.CUSTOMER_NAME,
                    A.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
                    APP___PAS__DBUSER.T_INSURED_LIST    B,
                    APP___PAS__DBUSER.T_CUSTOMER        A,
                    APP___PAS__DBUSER.T_ADDRESS         S
              WHERE C.POLICY_ID = B.POLICY_ID
                AND A.CUSTOMER_ID = B.CUSTOMER_ID
                AND C.LIABILITY_STATE != 3
                AND S.ADDRESS_ID = B.ADDRESS_ID
                AND S.CUSTOMER_ID = B.CUSTOMER_ID
                AND S.MOBILE_TEL = #{mobile_tel,
              jdbcType = VARCHAR}
                AND A.CUSTOMER_ID != #{customer_id}
                AND EXISTS
              (SELECT 1
                       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                      WHERE PRO.COVER_PERIOD_TYPE = '0'
                        AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
                        AND CBP.POLICY_CODE = C.POLICY_CODE
                        AND CBP.LIABILITY_STATE != 3)
                AND C.ORGAN_CODE LIKE '${organ_code}%')
      WHERE ROWNUM < 20
  	]]>
  </select>
  
 	<!-- 贷款清偿客户保单查询接口:查询新核心保单的投保人的五要素与输入条件中的“姓名、性别、生日、证件类型、证件号”五要素一致的保单相关信息。  -->
	<select id="JRQD_findAllPolicysByFiveFactorsAsPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 select ph.policy_code,a.customer_id,ph.policy_id
		  from APP___PAS__DBUSER.t_policy_holder ph,
		  	   APP___PAS__DBUSER.t_customer a,
		  	   APP___PAS__DBUSER.t_Contract_Master c
			 where a.customer_id = ph.customer_id
			 and c.policy_code = ph.policy_code
		 ]]>
	<if test=" liability_state  != null "><![CDATA[ AND C.LIABILITY_STATE = #{liability_state} ]]></if>
	<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND C.VALIDATE_DATE >= #{validate_date} ]]></if>
	<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[   AND  C.VALIDATE_DATE  <= #{suspend_date} ]]></if>
	<include refid="JRQD_PA_customerPolicyList" />
	</select>
  
   	<!-- 微信续投保表单投保人信息  -->
	<select id="JRQD_PA_queryHolderRenewal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	  SELECT C.CUSTOMER_NAME,
             C.CUSTOMER_CERT_TYPE,
             C.CUSTOMER_CERTI_CODE,
             C.CUSTOMER_GENDER,
             B.MOBILE_TEL,
             B.EMAIL,
             A.JOB_CODE
        FROM APP___PAS__DBUSER.T_POLICY_HOLDER A,
             APP___PAS__DBUSER.T_ADDRESS       B,
             APP___PAS__DBUSER.T_CUSTOMER      C
       WHERE A.ADDRESS_ID = B.ADDRESS_ID
         AND A.CUSTOMER_ID = C.CUSTOMER_ID
         AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}
		 ]]>
	</select>
	
	 <!-- 微信续投保表单被保人信息  -->
	<select id="JRQD_PA_queryInsuredRenewal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
      SELECT C.CUSTOMER_NAME,
             C.CUSTOMER_CERT_TYPE,
             C.CUSTOMER_CERTI_CODE,
             C.CUSTOMER_GENDER,
             B.MOBILE_TEL,
             B.EMAIL,
             A.RELATION_TO_PH,
             A.JOB_CODE
        FROM APP___PAS__DBUSER.T_INSURED_LIST A,
             APP___PAS__DBUSER.T_ADDRESS       B,
             APP___PAS__DBUSER.T_CUSTOMER      C
       WHERE A.ADDRESS_ID = B.ADDRESS_ID
         AND A.CUSTOMER_ID = C.CUSTOMER_ID
         AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR}
		 ]]>
	</select>

	<!--P00001000852 投连退保查询接口使用  -->
	<select id="JRQD_PA_findCustomerNameByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  SELECT A.customer_name
            
            FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1 and A.customer_id=#{customer_id}
		 ]]>
	</select>

	
	
	 <!-- 微信续投保表单被保人信息  -->
	<select id="JRQD_PA_queryHodlerMobelForWX" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
     	SELECT DISTINCT TACUS.CUSTOMER_ID,
                 TACUS.CUSTOMER_NAME,
                 TACUS.CUSTOMER_GENDER,
                 TACUS.CUSTOMER_BIRTHDAY,
                 TACUS.CUSTOMER_CERT_TYPE,
                 TACUS.CUSTOMER_CERTI_CODE
   FROM (SELECT DISTINCT C.CUSTOMER_ID,
                         C.CUSTOMER_NAME,
                         C.CUSTOMER_GENDER,
                         C.CUSTOMER_BIRTHDAY,
                         C.CUSTOMER_CERT_TYPE,
                         C.CUSTOMER_CERTI_CODE
           FROM APP___PAS__DBUSER.T_CONTRACT_MASTER M
           RIGHT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT CP
             ON CP.POLICY_ID = M.POLICY_ID
           AND CP.IS_PAUSE is null
           LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER PH
             ON M.POLICY_ID = PH.POLICY_ID
           LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER C
             ON C.CUSTOMER_ID = PH.CUSTOMER_ID
           LEFT JOIN APP___PAS__DBUSER.T_ADDRESS A
             ON PH.ADDRESS_ID = A.ADDRESS_ID
             WHERE A.MOBILE_TEL =  #{mobile_tel}
              AND M.POLICY_CODE != #{policy_code}
              AND M.LIABILITY_STATE NOT IN ('3', '4')
              AND ABS((SELECT ((MONTHS_BETWEEN(M.VALIDATE_DATE, M.EXPIRY_DATE)) / 12)  FROM DUAL)) > 1
         ]]>
          <if test=" organ_code != null and organ_code != ''  "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%' ]]></if>
          <![CDATA[
         ) TACUS
         WHERE TACUS.CUSTOMER_ID NOT IN
        (SELECT A.CUSTOMER_ID
           FROM APP___PAS__DBUSER.T_CUSTOMER A
          WHERE 1 = 1
            AND A.CUSTOMER_NAME = #{customer_name}
		    AND A.CUSTOMER_GENDER = #{customer_gender}
		    AND A.CUSTOMER_BIRTHDAY = #{customer_birthday}
		    AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type}
     	]]>
		<if test=" certi_code != null and certi_code != '' ">
		<![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{certi_code} OR  A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" certi_code == null and certi_code == '' ">
		<![CDATA[ AND  A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<![CDATA[) ]]>
</select>

	<!-- 甘肃分公司投保人移动电话重复校验查询  -->
	<select id="JRQD_PA_findCheckGanSuCutomerMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.CUSTOMER_ID,S.MOBILE_TEL 
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
				APP___PAS__DBUSER.T_POLICY_HOLDER B,
				APP___PAS__DBUSER.T_CUSTOMER A,
				APP___PAS__DBUSER.T_ADDRESS S
			WHERE C.POLICY_ID = B.POLICY_ID
			AND A.CUSTOMER_ID = B.CUSTOMER_ID
			AND C.LIABILITY_STATE NOT IN ('3','4')
			AND S.ADDRESS_ID = B.ADDRESS_ID
			AND S.MOBILE_TEL = #{mobile_tel,jdbcType=VARCHAR}
			AND A.CUSTOMER_ID != #{customer_id}
			AND C.ORGAN_CODE LIKE '%8653%'
		 ]]>
	</select>
	
	<!-- 根据入参CustomerId查询客户死亡状态  -->
	<select id="JRQD_PA_findAppOrInsuredDieFlagPA" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT LIVE_STATUS
				    FROM APP___PAS__DBUSER.T_CUSTOMER
				   WHERE CUSTOMER_ID = #{customer_id} 
				     AND LIVE_STATUS=2
		 ]]>
	</select>
	
	<!-- 根据入参手机号或者固定电话查询客户信息  -->
	<select id="JRQD_PA_findAllCusByMobileOrFix" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT TC.CUSTOMER_CERTI_CODE, 
					        TC.CUSTOMER_CERT_TYPE,
					        TC.CUSTOMER_BIRTHDAY,
					        TC.CUSTOMER_GENDER,
					        TC.CUSTOMER_NAME 
					   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
					        APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
					        APP___PAS__DBUSER.T_CUSTOMER        TC,
					        APP___PAS__DBUSER.T_ADDRESS         TA
					  WHERE TCM.POLICY_ID = TPH.POLICY_ID
					    AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
					    AND TA.ADDRESS_ID = TPH.ADDRESS_ID
					    AND TA.CUSTOMER_ID = TPH.CUSTOMER_ID
					    AND (TCM.END_CAUSE NOT IN ('01', '02', '03', '06') OR TCM.END_CAUSE is null)  
					    AND (TCM.LAPSE_CAUSE NOT IN ('1', '4') OR TCM.LAPSE_CAUSE is null)   ]]>
		 <if test=" mobile_tel != null and mobile_tel != '' "> <![CDATA[ AND TA.MOBILE_TEL = #{mobile_tel} ]]></if>
		 <if test=" fax_tel != null and fax_tel != '' "> <![CDATA[ AND TA.FIXED_TEL = #{fax_tel} ]]></if>
					<![CDATA[
					UNION 
					SELECT TC.CUSTOMER_CERTI_CODE, 
					       TC.CUSTOMER_CERT_TYPE, 
					       TC.CUSTOMER_BIRTHDAY,
					       TC.CUSTOMER_GENDER,
					       TC.CUSTOMER_NAME 
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
					       APP___PAS__DBUSER.T_INSURED_LIST    TIL,
					       APP___PAS__DBUSER.T_CUSTOMER        TC,
					       APP___PAS__DBUSER.T_ADDRESS         TA
					 WHERE TCM.POLICY_ID = TIL.POLICY_ID
					   AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID
					   AND TA.ADDRESS_ID = TIL.ADDRESS_ID
					   AND TA.CUSTOMER_ID = TIL.CUSTOMER_ID
					   AND (TCM.END_CAUSE NOT IN ('01', '02', '03', '06') OR TCM.END_CAUSE is null)  
					   AND (TCM.LAPSE_CAUSE NOT IN ('1', '4') OR TCM.LAPSE_CAUSE is null)   ]]>
		 <if test=" mobile_tel != null and mobile_tel != '' "> <![CDATA[ AND TA.MOBILE_TEL = #{mobile_tel} ]]></if>
		 <if test=" fax_tel != null and fax_tel != '' "> <![CDATA[ AND TA.FIXED_TEL = #{fax_tel} ]]></if>
	</select>

	<!-- 根据入参手机号查询所有山西客户信息 -->
	<select id="JRQD_PA_findsxAllCusByMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT DISTINCT TC.CUSTOMER_CERTI_CODE,
					       TC.CUSTOMER_CERT_TYPE,
					       TC.CUSTOMER_BIRTHDAY,
					       TC.CUSTOMER_GENDER,
					       TC.CUSTOMER_NAME
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
					       APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
					       APP___PAS__DBUSER.T_CUSTOMER        TC,
					       APP___PAS__DBUSER.T_ADDRESS         TA
					 WHERE TCM.POLICY_ID = TPH.POLICY_ID
					   AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
					   AND TA.ADDRESS_ID = TPH.ADDRESS_ID
					   AND TA.CUSTOMER_ID = TPH.CUSTOMER_ID
					   AND TCM.ORGAN_CODE LIKE '8638%'
					   AND TCM.LIABILITY_STATE IN ('1', '4')
					   AND TA.MOBILE_TEL = #{mobile_tel}
					   ]]>
	</select>
	
	<!-- 根据入参手机号查询所有甘肃客户信息 -->
	<select id="JRQD_PA_findgsAllCusByMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		   SELECT DISTINCT TC.CUSTOMER_CERTI_CODE,
					       TC.CUSTOMER_NAME
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
					       APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
					       APP___PAS__DBUSER.T_CUSTOMER        TC,
					       APP___PAS__DBUSER.T_ADDRESS         TA
					 WHERE TCM.POLICY_ID = TPH.POLICY_ID
					   AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
					   AND TA.ADDRESS_ID = TPH.ADDRESS_ID
					   AND TA.CUSTOMER_ID = TPH.CUSTOMER_ID
					   AND TCM.ORGAN_CODE LIKE '8653%'
					   AND TCM.LIABILITY_STATE NOT IN ('3', '4')
					   AND TA.MOBILE_TEL = #{mobile_tel}
					   ]]>
	</select>
	
	<!-- 根据入参手机号查询所有北京客户信息 -->
	<select id="JRQD_PA_findbjAllCusByMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT DISTINCT TC.CUSTOMER_CERTI_CODE, TC.CUSTOMER_NAME 
					   FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
					        APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
					        APP___PAS__DBUSER.T_CUSTOMER        TC,
					        APP___PAS__DBUSER.T_ADDRESS         TA
					  WHERE TCM.POLICY_ID = TPH.POLICY_ID
					    AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
					    AND TA.ADDRESS_ID = TPH.ADDRESS_ID
					    AND TA.CUSTOMER_ID = TPH.CUSTOMER_ID
					    AND TCM.ORGAN_CODE LIKE '8621%'
					    AND (TCM.END_CAUSE NOT IN ('01', '02', '03', '06', '15', '80') OR TCM.END_CAUSE is null)
					    AND (TCM.LAPSE_CAUSE NOT IN ('1', '4') OR TCM.LAPSE_CAUSE is null)
					    AND TA.MOBILE_TEL = #{mobile_tel}
					    AND TCM.POLICY_CODE NOT IN
					        (SELECT CP.POLICY_CODE
					           FROM DEV_PAS.T_BUSINESS_PRODUCT   BP,
					                DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
					                DEV_PAS.T_CONTRACT_PRODUCT   CP
					          WHERE BP.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
					            AND CP.POLICY_CODE = CBP.POLICY_CODE
					            AND CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
					            AND BP.PRODUCT_CATEGORY1 = '20003'
					            AND CP.IS_PAUSE = '1'
					            AND CBP.POLICY_CODE = TCM.POLICY_CODE)
					   ]]>
	</select>
	
	<!-- 根据入参手机号查询所有内蒙客户信息 -->
	<select id="JRQD_PA_findnmAllCusByMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				    SELECT TC.CUSTOMER_CERTI_CODE, TC.CUSTOMER_NAME 
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM, 
					       APP___PAS__DBUSER.T_POLICY_HOLDER   TPH, 
					       APP___PAS__DBUSER.T_CUSTOMER        TC, 
					       APP___PAS__DBUSER.T_ADDRESS         TA 
					 WHERE TCM.POLICY_ID = TPH.POLICY_ID 
					   AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID 
					   AND TA.ADDRESS_ID = TPH.ADDRESS_ID 
					   AND TA.CUSTOMER_ID = TPH.CUSTOMER_ID 
					   AND TCM.LIABILITY_STATE NOT IN ('3', '4') 
					   AND TCM.ORGAN_CODE LIKE '8649%' 
					   AND trunc(months_between(SYSDATE,TC.CUSTOMER_BIRTHDAY+1)/12) >= 18 
					   AND TA.MOBILE_TEL = #{mobile_tel} 
					 UNION 
					 SELECT TC.CUSTOMER_CERTI_CODE, 
					       TC.CUSTOMER_NAME 
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM, 
					       APP___PAS__DBUSER.T_INSURED_LIST    TIL, 
					       APP___PAS__DBUSER.T_CUSTOMER        TC, 
					       APP___PAS__DBUSER.T_ADDRESS         TA 
					 WHERE TCM.POLICY_ID = TIL.POLICY_ID 
					   AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID 
					   AND TA.ADDRESS_ID = TIL.ADDRESS_ID 
					   AND TA.CUSTOMER_ID = TIL.CUSTOMER_ID 
					   AND TCM.LIABILITY_STATE NOT IN ('3', '4') 
					   AND TCM.ORGAN_CODE LIKE '8649%' 
					   AND trunc(months_between(SYSDATE,TC.CUSTOMER_BIRTHDAY+1)/12) >= 18 
					   AND TA.MOBILE_TEL = #{mobile_tel} 
					 UNION 
					 SELECT TC.CUSTOMER_CERTI_CODE, 
					       TC.CUSTOMER_NAME 
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM, 
					       APP___PAS__DBUSER.T_CONTRACT_BENE   TCB, 
					       APP___PAS__DBUSER.T_CUSTOMER        TC, 
					       APP___PAS__DBUSER.T_ADDRESS         TA 
					 WHERE TCM.POLICY_ID = TCB.POLICY_ID 
					   AND TC.CUSTOMER_ID = TCB.CUSTOMER_ID 
					   AND TA.ADDRESS_ID = TCB.ADDRESS_ID 
					   AND TA.CUSTOMER_ID = TCB.CUSTOMER_ID 
					   AND TCM.LIABILITY_STATE NOT IN ('3', '4')  
					   AND TCM.ORGAN_CODE LIKE '8649%' 
					   AND trunc(months_between(SYSDATE,TC.CUSTOMER_BIRTHDAY+1)/12) >= 18 
					   AND TA.MOBILE_TEL = #{mobile_tel} 
					   ]]>
	</select>
	
	<!-- 根据老客户号查询客户信息(customer+address) -->
	<select id="JRQD_PA_findCustomerDesc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				    SELECT TC.OLD_CUSTOMER_ID, 
					       TC.CUSTOMER_NAME, 
					       TG.GENDER_DESC,
					       TC.CUSTOMER_BIRTHDAY,
					       TCT.TYPE,
					       TC.CUSTOMER_CERTI_CODE,
					       TC.CUST_CERT_STAR_DATE,
					       TC.CUST_CERT_END_DATE,
					       TJC.JOB_NAME,
					       TC.JOB_CODE,
					       TJCY.JOB_CATEGORY_NAME AS JOB_UW_LEVEL_NAME,
                 		   TJC.JOB_CATEGORY AS JOB_UW_LEVEL,
					       TC.COMPANY_NAME,
					       TA.MOBILE_TEL,
					       TCY.COUNTRY_NAME,
					       TC.COUNTRY_CODE,
					       CT.TAX_RESIDENT_TYPE,
					       TA.ADDRESS,
					       (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.STATE) AS STATE_NAME,
					       TA.STATE,
					       (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.CITY) AS CITY_NAME,
					       TA.CITY,
					       (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.DISTRICT) AS DISTRICT_NAME,
					       TA.DISTRICT,
					       TA.POST_CODE
					  FROM APP___PAS__DBUSER.T_CUSTOMER         TC
					  JOIN APP___PAS__DBUSER.T_ADDRESS          TA  ON TA.CUSTOMER_ID = TC.CUSTOMER_ID
					  LEFT JOIN APP___PAS__DBUSER.T_GENDER      TG  ON TC.CUSTOMER_GENDER = TG.GENDER_CODE
					  LEFT JOIN APP___PAS__DBUSER.T_CERTI_TYPE  TCT ON TC.CUSTOMER_CERT_TYPE = TCT.CODE  
					  LEFT JOIN APP___PAS__DBUSER.T_JOB_CODE    TJC ON TC.JOB_CODE = TJC.JOB_CODE
					  LEFT JOIN APP___PAS__DBUSER.T_JOB_CATEGORY TJCY ON TJC.JOB_CATEGORY = TJCY.JOB_CATEGORY_CODE
					  LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER_TAX CT 
					         ON CT.CUSTOMER_TAX_NAME = TC.CUSTOMER_NAME
					        AND CT.CUSTOMER_TAX_GENDER = TC.CUSTOMER_GENDER
					        AND CT.CUSTOMER_TAX_BIRTHDAY = TC.CUSTOMER_BIRTHDAY
					        AND CT.CUSTOMER_TAX_CERT_TYPE = TC.CUSTOMER_CERT_TYPE
					        AND CT.CUSTOMER_TAX_CERTI_CODE = TC.CUSTOMER_CERTI_CODE
					  LEFT JOIN APP___PAS__DBUSER.T_COUNTRY     TCY ON TC.COUNTRY_CODE = TCY.COUNTRY_CODE
					  WHERE 1 = 1 
					  AND TA.UPDATE_TIME = (SELECT MAX(TADD.UPDATE_TIME) FROM APP___PAS__DBUSER.T_ADDRESS TADD WHERE TADD.CUSTOMER_ID = TA.CUSTOMER_ID)
					  AND TC.OLD_CUSTOMER_ID = #{old_customer_id} 
					  ORDER BY TA.UPDATE_TIME DESC
					   ]]>
	</select>
	
	<!-- 根据客户信息查询保单是否存在 -->
	<select id="JRQD_PA_findCustomerByCustomerDesc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT TC.CUSTOMER_ID, TCM.POLICY_CODE, TCM.POLICY_ID, TCM.ORGAN_CODE, TCM.LIABILITY_STATE, 
							(SELECT TCA.AGENT_CODE FROM APP___PAS__DBUSER.T_CONTRACT_AGENT  TCA 
											      WHERE TCM.POLICY_CODE = TCA.POLICY_CODE AND TCA.IS_CURRENT_AGENT = '1') AS AGENT__CODE 
					  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM, 
					       APP___PAS__DBUSER.T_POLICY_HOLDER   TPH, 
					       APP___PAS__DBUSER.T_CUSTOMER        TC 
					 WHERE TCM.POLICY_CODE = TPH.POLICY_CODE 
					   AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID 
					   AND TCM.POLICY_CODE = #{policy_code} 
					   AND TC.CUSTOMER_NAME = #{customer_name}  ]]>
	    <choose>
			<when test="customer_cert_type == '0'.toString() "> AND TC.CUSTOMER_CERT_TYPE in (0, 5) </when>
			<otherwise> AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type} </otherwise>
	    </choose>
		<![CDATA[  AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}  ]]>
	</select>
	
	<!-- 按照保单号查询客户生存状态 -->
	<select id="JRQD_PA_queryPolicyLiveStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   
				   SELECT TC.CUSTOMER_ID
		             FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
		                  APP___PAS__DBUSER.T_POLICY_HOLDER   TPH,
		                  APP___PAS__DBUSER.T_CUSTOMER        TC
		            WHERE TCM.POLICY_ID = TPH.POLICY_ID
		              AND TC.CUSTOMER_ID = TPH.CUSTOMER_ID
		              AND TC.LIVE_STATUS = 2
		              AND TCM.POLICY_CODE = #{policy_code}
		            UNION 
		           SELECT TC.CUSTOMER_ID
		             FROM APP___PAS__DBUSER.T_CONTRACT_MASTER TCM,
		                  APP___PAS__DBUSER.T_INSURED_LIST    TIL,
		                  APP___PAS__DBUSER.T_CUSTOMER        TC
		            WHERE TCM.POLICY_ID = TIL.POLICY_ID
		              AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID
		              AND TC.LIVE_STATUS = 2 
		              AND TCM.POLICY_CODE = #{policy_code}   ]]>
	</select>
	
	<!-- 随信通续期缴费信息查询接口-根据保单号查询投保人信息 -->
	<select id="JRQD_PA_findCustomerNsPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
	      SELECT TC.CUSTOMER_ID, 
                 TPH.POLICY_CODE, 
                 TC.OLD_CUSTOMER_ID,  
                 TC.CUSTOMER_NAME,  
                 TC.CUSTOMER_GENDER, 
                 TC.CUSTOMER_BIRTHDAY, 
                 TC.CUSTOMER_CERT_TYPE, 
                 TC.CUSTOMER_CERTI_CODE, 
                 TC.CUST_CERT_STAR_DATE, 
                 TC.CUST_CERT_END_DATE, 
                 TA.POST_CODE, 
                 TJC.JOB_NAME, 
                 TC.JOB_CODE, 
                 TJU.JOB_UW_LEVEL_NAME, 
                 TJC.JOB_UW_LEVEL, 
                 TC.COMPANY_NAME, 
                 TA.MOBILE_TEL, 
                 TA.ADDRESS, 
                 TCY.COUNTRY_NAME, 
                 TC.COUNTRY_CODE, 
                 (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.STATE) AS STATE_NAME, 
                 TA.STATE, 
                 (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.CITY) AS CITY_NAME, 
                 TA.CITY, 
                 (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.DISTRICT) AS DISTRICT_NAME, 
                 TA.DISTRICT, 
                 CT.TAX_RESIDENT_TYPE 
            FROM APP___PAS__DBUSER.T_POLICY_HOLDER    TPH  
            JOIN APP___PAS__DBUSER.T_CUSTOMER         TC  ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID 
            JOIN APP___PAS__DBUSER.T_ADDRESS          TA  ON TA.ADDRESS_ID = TPH.ADDRESS_ID 
            LEFT JOIN APP___PAS__DBUSER.T_JOB_CODE    TJC ON TC.JOB_CODE = TJC.JOB_CODE 
            LEFT JOIN APP___PAS__DBUSER.T_JOB_UNDERWRITE TJU ON TJC.JOB_UW_LEVEL = TJU.JOB_UW_LEVEL_CODE 
            LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER_TAX CT  
                   ON CT.CUSTOMER_TAX_NAME = TC.CUSTOMER_NAME 
                  AND CT.CUSTOMER_TAX_GENDER = TC.CUSTOMER_GENDER 
                  AND CT.CUSTOMER_TAX_BIRTHDAY = TC.CUSTOMER_BIRTHDAY 
                  AND CT.CUSTOMER_TAX_CERT_TYPE = TC.CUSTOMER_CERT_TYPE 
                  AND CT.CUSTOMER_TAX_CERTI_CODE = TC.CUSTOMER_CERTI_CODE 
            LEFT JOIN APP___PAS__DBUSER.T_COUNTRY     TCY ON TC.COUNTRY_CODE = TCY.COUNTRY_CODE 
            WHERE 1 = 1  
            AND TPH.POLICY_CODE = #{policy_code}  
	     ]]>
	</select>
	
	<!-- 随信通续期缴费信息查询接口-根据保单号查询所有被保人信息 -->
	<select id="JRQD_PA_findCustomerInsuredByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
	      SELECT TC.CUSTOMER_ID,
                 TIL.POLICY_CODE,
                 TC.OLD_CUSTOMER_ID, 
                 TC.CUSTOMER_NAME, 
                 TC.CUSTOMER_GENDER,
                 TC.CUSTOMER_BIRTHDAY,
                 TC.CUSTOMER_CERT_TYPE,
                 TC.CUSTOMER_CERTI_CODE,
                 TIL.RELATION_TO_PH,
                 TLPR.RELATION_NAME,
                 TBI.ORDER_ID,
                 TC.CUST_CERT_STAR_DATE,
                 TC.CUST_CERT_END_DATE,
                 TA.POST_CODE,
                 TJC.JOB_NAME,
                 TC.JOB_CODE,
                 TJU.JOB_UW_LEVEL_NAME,
                 TJC.JOB_UW_LEVEL,
                 TC.COMPANY_NAME,
                 TA.MOBILE_TEL,
                 TA.ADDRESS,
                 TCY.COUNTRY_NAME,
                 TC.COUNTRY_CODE,
                 (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.STATE) AS STATE_NAME,
                 TA.STATE,
                 (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.CITY) AS CITY_NAME,
                 TA.CITY,
                 (SELECT TD.NAME FROM APP___PAS__DBUSER.T_DISTRICT TD WHERE TD.CODE = TA.DISTRICT) AS DISTRICT_NAME,
                 TA.DISTRICT,
                 CT.TAX_RESIDENT_TYPE
            FROM APP___PAS__DBUSER.T_INSURED_LIST     TIL 
            JOIN APP___PAS__DBUSER.T_CUSTOMER         TC  ON TIL.CUSTOMER_ID = TC.CUSTOMER_ID
            JOIN APP___PAS__DBUSER.T_ADDRESS          TA  ON TA.ADDRESS_ID = TIL.ADDRESS_ID
            JOIN APP___PAS__DBUSER.T_BENEFIT_INSURED  TBI ON TIL.LIST_ID = TBI.INSURED_ID AND ROWNUM = 1
            LEFT JOIN APP___PAS__DBUSER.T_JOB_CODE    TJC ON TC.JOB_CODE = TJC.JOB_CODE
            LEFT JOIN APP___PAS__DBUSER.T_JOB_UNDERWRITE TJU ON TJC.JOB_UW_LEVEL = TJU.JOB_UW_LEVEL_CODE
            LEFT JOIN APP___PAS__DBUSER.T_LA_PH_RELA  TLPR   ON TLPR.RELATION_CODE = TIL.RELATION_TO_PH  
            LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER_TAX CT 
                   ON CT.CUSTOMER_TAX_NAME = TC.CUSTOMER_NAME
                  AND CT.CUSTOMER_TAX_GENDER = TC.CUSTOMER_GENDER
                  AND CT.CUSTOMER_TAX_BIRTHDAY = TC.CUSTOMER_BIRTHDAY
                  AND CT.CUSTOMER_TAX_CERT_TYPE = TC.CUSTOMER_CERT_TYPE
                  AND CT.CUSTOMER_TAX_CERTI_CODE = TC.CUSTOMER_CERTI_CODE
            LEFT JOIN APP___PAS__DBUSER.T_COUNTRY     TCY ON TC.COUNTRY_CODE = TCY.COUNTRY_CODE
            WHERE 1 = 1 
            AND TIL.POLICY_CODE =  #{policy_code}    
	     ]]>
	</select>
	
	<!-- 根据投被保人客户五要素查询保单信息 -->
	<select id="JRQD_PA_queryPolicyByFiveElements" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ select distinct t.policy_code,t.apply_code,t.relation_policy_code,t.busi_prod_code from (select 
	          tcm.policy_code,
	          tcm.apply_code,
	          tcm.relation_policy_code,
	          tcbp.busi_prod_code 
	     from 
	          dev_pas.t_customer tcu,
	          dev_pas.t_policy_holder tph,
	          dev_pas.t_contract_busi_prod tcbp,
	          dev_pas.t_contract_master tcm ,
            dev_pas.t_insured_list til ,
            dev_pas.t_customer b
         where 1=1
              and tcu.customer_id = tph.customer_id
              and tph.policy_code = tcbp.policy_code
              and til.policy_code = tph.policy_code
              and til.customer_id = b.customer_id
              and tcbp.policy_id = tcm.policy_id
              and tcm.liability_state = '1']]>
         <if test="holder_birthDay!=null and holder_birthDay!=''">
              <![CDATA[ and tcu.customer_birthday = to_date(#{holder_birthDay},'yyyy-MM-dd')]]>
         </if>
         <if test="holder_certi_code!=null and holder_certi_code!=null!=''">
             <![CDATA[and tcu.customer_certi_code = #{holder_certi_code}]]>
         </if> 
         <if test="holder_certit_type!=null and holder_certit_type!=''">
            <![CDATA[ and tcu.customer_cert_type = #{holder_certit_type}]]>
         </if>  
         
         <if test=" holder_certit_type_list != null">
		    <![CDATA[ and tcu.customer_cert_type in ]]>
				<foreach collection="holder_certit_type_list" item="holder"
					index="index" open="(" close=")" separator=",">#{holder}</foreach>
		 </if>
         
         <if test="holder_name!=null and holder_name!=''">
             <![CDATA[ and tcu.customer_name = #{holder_name}]]>
         </if> 
         <if test="holder_sex!=null and holder_sex!=''">
            <![CDATA[ and tcu.customer_gender = #{holder_sex}]]>
         </if> 
         <if test=" code_list != null">
		    <![CDATA[ and tcbp.busi_prod_code in ]]>
				<foreach collection="code_list" item="item"
					index="index" open="(" close=")" separator=",">#{item}</foreach>
		 </if>

	     <if test="insured_birth_day!=null and insured_birth_day!=''">
            <![CDATA[  and b.customer_birthday = to_date(#{insured_birth_day},'yyyy-MM-dd')]]>
         </if>
         <if test="insured_cert_code!=null and insured_cert_code!=null!=''">
             <![CDATA[ and b.customer_certi_code = #{insured_cert_code}]]>
         </if> 
         <if test="insured_cert_type!=null and insured_cert_type!=''">
            <![CDATA[ and b.customer_cert_type = #{insured_cert_type}]]>
         </if>
           
          <if test=" insured_cert_type_list != null">
		    <![CDATA[ and b.customer_cert_type in ]]>
				<foreach collection="insured_cert_type_list" item="insured"
					index="index" open="(" close=")" separator=",">#{insured}</foreach>
		 </if>
         
         <if test="insured_name!=null and insured_name!=''">
             <![CDATA[ and b.customer_name = #{insured_name}]]>
         </if> 
         <if test="insured_sex!=null and insured_sex!=''">
           <![CDATA[  and b.customer_gender = #{insured_sex}]]>
         </if> 
	   <![CDATA[ ) t]]>
	</select>
	<!-- 根据身份证号查询保单信息 -->
	<select id="JRQD_PA_findCustomerByIdNo"  resultType="java.util.Map" parameterType="java.util.Map">
	      select
		           tcm.policy_code,
		           tcm.validate_date,
		           tcm.liability_state,
		           tcm.policy_id,
		           (select udmp.organ_name from APP___PAS__DBUSER.t_udmp_org udmp where tcm.organ_code = udmp.organ_code) organ_name,
		           tcm.organ_code,
		           (select tl.cause_desc from APP___PAS__DBUSER.t_lapse_cause tl where tl.cause_code = tcm.lapse_cause) lapse,
                   (select tc.cause_name from APP___PAS__DBUSER.t_end_cause tc where tc.cause_code = tcm.end_cause) end_cause
		           from 
		          APP___PAS__DBUSER.t_customer tcu,
		          APP___PAS__DBUSER.t_contract_master tcm,
		          APP___PAS__DBUSER.t_contract_agent tca,
		          APP___PAS__DBUSER.t_policy_holder tph
		           where  tcm.policy_code = tph.policy_code
	               and tcm.policy_code = tca.policy_code
	               and tph.customer_id = tcu.customer_id
	               and tca.is_current_agent = '1'
	           <if test="policy_code!=null and policy_code!=''">
	               and tcm.policy_code = #{policy_code}
	           </if> 
	           <if test="customer_name!=null and customer_name!=''">
	               and tcu.customer_name = #{customer_name}
	           </if>
	           <if test="customer_certi_code!=null and customer_certi_code!=''">
	               and tcu.customer_certi_code = #{customer_certi_code}
	           </if>
	            <if test="agent_code!=null and agent_code!=''">
	               and tca.agent_code = #{agent_code}
	           </if>
	
	</select>
	
	<select id="JRQD_PA_findALLCustomerByIdNoAndNameAndPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	
	            select
		           tcm.policy_code,
		           tcm.validate_date,
		           tcm.liability_state,
		           tcm.policy_id,
		           (select tl.cause_desc from APP___PAS__DBUSER.t_lapse_cause tl where tl.cause_code = tcm.lapse_cause) lapse,
                   (select tc.cause_name from APP___PAS__DBUSER.t_end_cause tc where tc.cause_code = tcm.end_cause) end_cause,
		           (select udmp.organ_name from APP___PAS__DBUSER.t_udmp_org udmp where tcm.organ_code = udmp.organ_code) organ_name,
		           tcm.organ_code 
		           from 
		          APP___PAS__DBUSER.t_customer tcu,
		          APP___PAS__DBUSER.t_contract_master tcm,
		          APP___PAS__DBUSER.t_contract_agent tca,
		          APP___PAS__DBUSER.t_policy_holder tph
		           where  tcm.policy_code = tph.policy_code
	               and tcm.policy_code = tca.policy_code
	               and tph.customer_id = tcu.customer_id
	               and tca.is_current_agent = '1'
	           <if test="policy_code!=null and policy_code!=''">
	               and tcm.policy_code = #{policy_code}
	           </if> 
	           <if test="customer_name!=null and customer_name!=''">
	               and tcu.customer_name = #{customer_name}
	           </if>
	           <if test="customer_certi_code!=null and customer_certi_code!=''">
	               and tcu.customer_certi_code = #{customer_certi_code}
	           </if>
	           <if test="agent_code!=null and agent_code!=''">
	               and tca.agent_code = #{agent_code}
	           </if>
	           
	</select>
	
	<select id="JRQD_PA_findHolderCustomerByConNo" resultType="java.util.Map" parameterType="java.util.Map">
		select a.customer_name,a.customer_birthday,a.customer_gender,
		a.customer_certi_code,a.customer_cert_type 
		from dev_pas.t_policy_holder p
		left join dev_pas.t_contract_busi_prod c
		on c.policy_code = p.policy_code
		left join dev_pas.t_customer a
		on a.customer_id = p.customer_id
		where p.policy_code = #{policy_code}
		union
		select a.customer_name,a.customer_birthday,a.customer_gender,
		a.customer_certi_code,a.customer_cert_type
		from dev_pas.t_policy_holder p
		left join dev_pas.t_contract_busi_prod c
		on c.policy_code = p.policy_code
		left join dev_pas.t_customer a
		on a.customer_id = p.customer_id
		where p.policy_code = #{agent_code}
	</select>
	
	<select id="JRQD_PA_findInsuredCustomerByConNo" resultType="java.util.Map" parameterType="java.util.Map">
		select a.customer_name,a.customer_birthday,a.customer_gender,
		a.customer_certi_code,a.customer_cert_type 
		from dev_pas.t_insured_list p
		left join dev_pas.t_contract_busi_prod c
		on c.policy_code = p.policy_code
		left join dev_pas.t_customer a
		on a.customer_id = p.customer_id
		where p.policy_code = #{policy_code}
		union
		select a.customer_name,a.customer_birthday,a.customer_gender,
		a.customer_certi_code,a.customer_cert_type 
		from dev_pas.t_insured_list p
		left join dev_pas.t_contract_busi_prod c
		on c.policy_code = p.policy_code
		left join dev_pas.t_customer a
		on a.customer_id = p.customer_id
		where p.policy_code = #{agent_code}
	</select>
	
	<select id="JRQD_PA_findAllCustomerByNameAndCustomerCertiCodeofshanxi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      SELECT *
       FROM (SELECT A.CUSTOMER_CERTI_CODE,
                    A.CUSTOMER_CERT_TYPE,
                    A.CUSTOMER_BIRTHDAY,
                    A.CUSTOMER_GENDER,
                    A.CUSTOMER_NAME,
                    A.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
                    APP___PAS__DBUSER.T_POLICY_HOLDER   B,
                    APP___PAS__DBUSER.T_CUSTOMER        A,
                    APP___PAS__DBUSER.T_ADDRESS         S
              WHERE C.POLICY_ID = B.POLICY_ID
                AND A.CUSTOMER_ID = B.CUSTOMER_ID
                AND C.LIABILITY_STATE != 3
                AND S.ADDRESS_ID = B.ADDRESS_ID
                AND S.CUSTOMER_ID = B.CUSTOMER_ID
                AND S.MOBILE_TEL = #{mobile_tel,
              jdbcType = VARCHAR}
                AND A.CUSTOMER_ID != #{customer_id}
                AND EXISTS
              (SELECT 1
                       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                      WHERE PRO.COVER_PERIOD_TYPE = '0'
                        AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
                        AND CBP.POLICY_CODE = C.POLICY_CODE
                        AND CBP.LIABILITY_STATE != 3)
                AND C.ORGAN_CODE LIKE '${organ_code}%'
             UNION
             SELECT A.CUSTOMER_CERTI_CODE,
                    A.CUSTOMER_CERT_TYPE,
                    A.CUSTOMER_BIRTHDAY,
                    A.CUSTOMER_GENDER,
                    A.CUSTOMER_NAME,
                    A.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
                    APP___PAS__DBUSER.T_INSURED_LIST    B,
                    APP___PAS__DBUSER.T_CUSTOMER        A,
                    APP___PAS__DBUSER.T_ADDRESS         S
              WHERE C.POLICY_ID = B.POLICY_ID
                AND A.CUSTOMER_ID = B.CUSTOMER_ID
                AND C.LIABILITY_STATE != 3
                AND S.ADDRESS_ID = B.ADDRESS_ID
                AND S.CUSTOMER_ID = B.CUSTOMER_ID
                AND S.MOBILE_TEL = #{mobile_tel,
              jdbcType = VARCHAR}
                AND A.CUSTOMER_ID != #{customer_id}
                AND EXISTS
              (SELECT 1
                       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                      WHERE PRO.COVER_PERIOD_TYPE = '0'
                        AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
                        AND CBP.POLICY_CODE = C.POLICY_CODE
                        AND CBP.LIABILITY_STATE != 3)
                AND C.ORGAN_CODE LIKE '${organ_code}%')
      WHERE ROWNUM < 20]]>
	</select>
		
	<select id="JRQD_PA_findAllAddressCustomersOfSamePhoneAndMoblile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      SELECT *
       FROM (SELECT A.CUSTOMER_CERTI_CODE,
                    A.CUSTOMER_CERT_TYPE,
                    A.CUSTOMER_BIRTHDAY,
                    A.CUSTOMER_GENDER,
                    A.CUSTOMER_NAME,
                    A.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
                    APP___PAS__DBUSER.T_POLICY_HOLDER   B,
                    APP___PAS__DBUSER.T_CUSTOMER        A,
                    APP___PAS__DBUSER.T_ADDRESS         S
              WHERE C.POLICY_ID = B.POLICY_ID
                AND A.CUSTOMER_ID = B.CUSTOMER_ID
                AND S.ADDRESS_ID = B.ADDRESS_ID
                AND S.CUSTOMER_ID = B.CUSTOMER_ID]]>
		    <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND S.MOBILE_TEL = #{mobile_tel,jdbcType = VARCHAR} ]]></if>
		    <if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND S.FIXED_TEL = #{fixed_tel,jdbcType = VARCHAR} ]]></if>
		    <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND S.OFFICE_TEL = #{office_tel,jdbcType = VARCHAR} ]]></if> 
		    <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND S.HOUSE_TEL = #{house_tel,jdbcType = VARCHAR} ]]></if> 
          	<if test=" customer_id_list  != null and customer_id_list.size()!=0">
	            <![CDATA[ AND A.CUSTOMER_ID NOT IN ]]>
			    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
			      #{cus}
			    </foreach>
		    </if>
             <![CDATA[
                AND EXISTS  (SELECT 1
                       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                      WHERE PRO.COVER_PERIOD_TYPE = '0'
                        AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
                        AND CBP.POLICY_CODE = C.POLICY_CODE
                        ) ]]>
           <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND C.ORGAN_CODE LIKE '${organ_code}%']]></if>
           <![CDATA[     
             UNION
             SELECT A.CUSTOMER_CERTI_CODE,
                    A.CUSTOMER_CERT_TYPE,
                    A.CUSTOMER_BIRTHDAY,
                    A.CUSTOMER_GENDER,
                    A.CUSTOMER_NAME,
                    A.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
                    APP___PAS__DBUSER.T_INSURED_LIST    B,
                    APP___PAS__DBUSER.T_CUSTOMER        A,
                    APP___PAS__DBUSER.T_ADDRESS         S
              WHERE C.POLICY_ID = B.POLICY_ID
                AND A.CUSTOMER_ID = B.CUSTOMER_ID
                AND S.ADDRESS_ID = B.ADDRESS_ID
                AND S.CUSTOMER_ID = B.CUSTOMER_ID]]>      
            <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND S.MOBILE_TEL = #{mobile_tel,jdbcType = VARCHAR} ]]></if>
		    <if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND S.FIXED_TEL = #{fixed_tel,jdbcType = VARCHAR} ]]></if>
		    <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND S.OFFICE_TEL = #{office_tel,jdbcType = VARCHAR} ]]></if> 
		    <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND S.HOUSE_TEL = #{house_tel,jdbcType = VARCHAR} ]]></if> 
            <if test=" customer_id_list  != null and customer_id_list.size()!=0">
	            <![CDATA[ AND A.CUSTOMER_ID NOT IN ]]>
			    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
			      #{cus}
			    </foreach>
		    </if>
             <![CDATA[
              AND EXISTS (SELECT 1
                       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                      WHERE PRO.COVER_PERIOD_TYPE = '0'
                        AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
                        AND CBP.POLICY_CODE = C.POLICY_CODE
                        )]]>
           <if test=" organ_code != null and organ_code != '' "><![CDATA[  AND C.ORGAN_CODE LIKE '${organ_code}%' ]]></if>
                
          <![CDATA[ ) WHERE ROWNUM < 20]]>
	</select>
	
	<select id="JRQD_PA_findAllAddressCustomersOfSamePhone" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      SELECT *
       FROM (SELECT A.CUSTOMER_CERTI_CODE,
                    A.CUSTOMER_CERT_TYPE,
                    A.CUSTOMER_BIRTHDAY,
                    A.CUSTOMER_GENDER,
                    A.CUSTOMER_NAME,
                    A.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
                    APP___PAS__DBUSER.T_POLICY_HOLDER   B,
                    APP___PAS__DBUSER.T_CUSTOMER        A,
                    APP___PAS__DBUSER.T_ADDRESS         S
              WHERE C.POLICY_ID = B.POLICY_ID
                AND A.CUSTOMER_ID = B.CUSTOMER_ID
                AND S.ADDRESS_ID = B.ADDRESS_ID
                AND S.CUSTOMER_ID = B.CUSTOMER_ID]]>
		    <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND S.MOBILE_TEL = #{mobile_tel,jdbcType = VARCHAR} ]]></if>
		    <if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND S.FIXED_TEL = #{fixed_tel,jdbcType = VARCHAR} ]]></if>
		    <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND S.OFFICE_TEL = #{office_tel,jdbcType = VARCHAR} ]]></if> 
		    <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND S.HOUSE_TEL = #{house_tel,jdbcType = VARCHAR} ]]></if> 
            <if test=" customer_id_list  != null and customer_id_list.size()!=0">
            <![CDATA[ AND A.CUSTOMER_ID NOT IN ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
		   </if>
             <![CDATA[
                AND EXISTS  (SELECT 1
                       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                      WHERE PRO.COVER_PERIOD_TYPE = '0'
                        AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
                        AND CBP.POLICY_CODE = C.POLICY_CODE
                        ) ]]>
           <if test=" organ_code != null and organ_code != '' "><![CDATA[ AND C.ORGAN_CODE LIKE '${organ_code}%']]></if>
           <![CDATA[     
             UNION
             SELECT A.CUSTOMER_CERTI_CODE,
                    A.CUSTOMER_CERT_TYPE,
                    A.CUSTOMER_BIRTHDAY,
                    A.CUSTOMER_GENDER,
                    A.CUSTOMER_NAME,
                    A.CUSTOMER_ID
               FROM APP___PAS__DBUSER.T_CONTRACT_MASTER C,
                    APP___PAS__DBUSER.T_INSURED_LIST    B,
                    APP___PAS__DBUSER.T_CUSTOMER        A,
                    APP___PAS__DBUSER.T_ADDRESS         S
              WHERE C.POLICY_ID = B.POLICY_ID
                AND A.CUSTOMER_ID = B.CUSTOMER_ID
                AND S.ADDRESS_ID = B.ADDRESS_ID
                AND S.CUSTOMER_ID = B.CUSTOMER_ID]]>      
            <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND S.MOBILE_TEL = #{mobile_tel,jdbcType = VARCHAR} ]]></if>
		    <if test=" fixed_tel != null and fixed_tel != ''  "><![CDATA[ AND S.FIXED_TEL = #{fixed_tel,jdbcType = VARCHAR} ]]></if>
		    <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND S.OFFICE_TEL = #{office_tel,jdbcType = VARCHAR} ]]></if> 
		    <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND S.HOUSE_TEL = #{house_tel,jdbcType = VARCHAR} ]]></if> 
            <if test=" customer_id_list  != null and customer_id_list.size()!=0">
            <![CDATA[ AND A.CUSTOMER_ID NOT IN ]]>
		    <foreach collection ="customer_id_list" item="cus" index="index" open="(" close=")" separator=",">
		      #{cus}
		    </foreach>
		   </if>
             <![CDATA[
              AND EXISTS (SELECT 1
                       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT   PRO,
                            APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
                      WHERE PRO.COVER_PERIOD_TYPE = '0'
                        AND CBP.BUSI_PRD_ID = PRO.BUSINESS_PRD_ID
                        AND CBP.POLICY_CODE = C.POLICY_CODE
                        )]]>
           <if test=" organ_code != null and organ_code != '' "><![CDATA[  AND C.ORGAN_CODE LIKE '${organ_code}%' ]]></if>
                
          <![CDATA[ ) WHERE ROWNUM < 20]]>
	</select>
	
	<!-- 查询同一机构下移动电话相同的客户ID -->
	<select id="JRQD_PA_findSamePhoneCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.TAX_RESIDENT_TYPE, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1  AND ROWNUM <=  1000 AND A.MOBILE_TEL = #{mobile_tel,jdbcType = VARCHAR} ]]>
			<if test=" organ_code != null and organ_code != '' "><![CDATA[  AND A.ORGAN_CODE LIKE '${organ_code}%' ]]></if>
	</select>
	
    <!-- 根据身份类型和保单号来查询客户身份信息 -->
	<select id="JRQD_CS_queryCustomerIdCardInfo" resultType="java.util.Map" parameterType="java.util.Map">
	        <![CDATA[
	                 SELECT 
					       TCM.POLICY_CODE,
					       TC.CUSTOMER_ID,
					       TC.UN_CUSTOMER_CODE,
					       TC.OLD_CUSTOMER_ID,
					       TC.MARRIAGE_DATE,
					       TC.EDUCATION,
					       TC.CUSTOMER_NAME,
					       TC.CUSTOMER_BIRTHDAY,
					       TC.CUSTOMER_GENDER,
					       TC.CUSTOMER_HEIGHT,
					       TC.CUSTOMER_WEIGHT,
					       TC.CUSTOMER_CERT_TYPE,
					       TC.CUSTOMER_CERTI_CODE,
					       TC.CUSTOMER_ID_CODE,
					       TC.CUST_CERT_STAR_DATE,
					       TC.CUST_CERT_END_DATE,
					       TC.JOB_CODE,
					       TC.JOB_NATURE,
					       TC.JOB_KIND,
					       TC.JOB_TITLE,
					       TC.MARRIAGE_STATUS,
					       TC.IS_PARENT,
					       TC.ANNUAL_INCOME,
					       TC.COUNTRY_CODE,
					       TC.RELIGION_CODE,
					       TC.NATION_CODE,
					       TC.DRIVER_LICENSE_TYPE,
					       TC.COMM_METHOD,
					       TC.COMPANY_NAME,
					       TC.OFFEN_USE_TEL,
					       TC.HOUSE_TEL,
					       TC.FAX_TEL,
					       TC.OFFICE_TEL,
					       TC.MOBILE_TEL,
					       TC.EMAIL,
					       TC.QQ,
					       TC.WECHAT_NO,
					       TC.OTHER,
					       TC.CUSTOMER_LEVEL,
					       TC.CUSTOMER_RISK_LEVEL,
					       TC.CUSTOMER_VIP,
					       TC.SMOKING_FLAG,
					       TC.DRUNK_FLAG,
					       TC.BLACKLIST_FLAG,
					       TC.HOUSEKEEPER_FLAG,
					       TC.SYN_MDM_FLAG,
					       TC.SOCI_SECU,
					       TC.LIVE_STATUS,
					       TC.RETIRED_FLAG,
					       TC.DEATH_DATE,
					       TC.HEALTH_STATUS,
					       TC.REMARK,
					       TC.CUST_PWD,
					       TC.RESIDENT_TYPE,
					       TC.IS_SUBSCRIPTION_EMAIL,
					       TC.TAX_RESIDENT_TYPE,
					       TC.INSERT_BY,
					       TC.INSERT_TIME,
					       TC.INSERT_TIMESTAMP,
					       TC.UPDATE_BY,
					       TC.UPDATE_TIME,
					       TC.UPDATE_TIMESTAMP,
					       TC.NON_RESIDENT_FLAG,
					       TC.INCOME_SOURCE,
					       TC.ANNUAL_INCOME_CEIL,
					       TC.RZ_LEVEL,
					       TC.SECOND_CERT_TYPE,
					       TC.SECOND_CERTI_CODE
	        ]]>
			<if test="capacityType == '1'.toString() ">
			<![CDATA[  
                      FROM 
                          DEV_PAS.T_CONTRACT_MASTER TCM,
                          DEV_PAS.T_POLICY_HOLDER   TPH,
                          DEV_PAS.T_CUSTOMER        TC
                      WHERE
                           TCM.POLICY_CODE = TPH.POLICY_CODE
                           AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
                            ]]></if>
			<if test="capacityType == '2'.toString() ">
			<![CDATA[  
			          FROM 
			              DEV_PAS.T_CONTRACT_MASTER TCM,
                          DEV_PAS.T_INSURED_LIST TIL,
                          DEV_PAS.T_CUSTOMER        TC
                      WHERE 
                          TCM.POLICY_CODE = TIL.POLICY_CODE
                          AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID
                          ]]></if>
			<if test="capacityType == '3'.toString() ">
			<![CDATA[ 
			         FROM 
			               DEV_PAS.T_CONTRACT_MASTER TCM,
                           DEV_PAS.T_CONTRACT_BENE  TCB,
                           DEV_PAS.T_CUSTOMER        TC
                     WHERE 
                           TCM.POLICY_CODE = TCB.POLICY_CODE
                           AND TCB.CUSTOMER_ID = TC.CUSTOMER_ID
                             ]]></if>
            <![CDATA[ 
                           AND TCM.POLICY_CODE = #{policy_code}
                           AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type} 
                           AND TC.CUSTOMER_NAME = #{customer_name}
                           AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]>
	</select>
	
	<select id="JRQD_PA_queryCRSPolicyByCustomerID" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
		   select tcm.policy_code,
							 tpa.acknowledge_date,
							 tca.organ_code,
							 (SELECT T.ORGAN_NAME
									FROM DEV_PAS.T_UDMP_ORG_REL T
								 WHERE T.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME,
							 (SELECT TSC.SALES_CHANNEL_CODE FROM DEV_PAS.T_SALES_CHANNEL TSC WHERE TSC.SALES_CHANNEL_CODE = TCA.CHANNEL_TYPE )submit_channel,
							 tca.agent_code,
							 tca.agent_name,
							 tc.customer_id,
							 tc.customer_name,
							 tc.customer_gender,
							 tc.customer_certi_code,
							 ta.mobile_tel
					from dev_pas.t_contract_master            tcm,
							 dev_pas.t_policy_acknowledgement     tpa,
							 dev_pas.t_policy_holder              tph,
							 dev_pas.t_customer                   tc,
							 dev_pas.t_contract_agent             tca,
							 dev_pas.t_address                    ta,
							 dev_pas.t_contract_busi_prod         tcbp,
							 APP___PDS__DBUSER.t_business_product tbp
				 where 1 = 1
					 and tcm.policy_id = tpa.policy_id
					 and tcm.policy_code = tph.policy_code
					 and tcm.policy_code = tca.policy_code
					 and tph.customer_id = tc.customer_id
					 and tph.address_id = ta.address_id
					 and tcm.policy_code = tcbp.policy_code
					 and tcbp.BUSI_PRD_ID = tbp.BUSINESS_PRD_ID
					 and tcm.liability_state not in ('4')
           			 and tbp.tax_revenue_flag = '1'
           			 AND tca.IS_CURRENT_AGENT = '1'
           			 and not exists (select ta.tax_resident_type from dev_pas.t_customer_tax ta where ta.customer_tax_name = tc.customer_name
	                                                and ta.customer_tax_birthday = tc.customer_birthday
													and ta.customer_tax_gender = tc.customer_gender
													and ta.customer_tax_cert_type = tc.customer_cert_type
													and ta.customer_tax_certi_code = tc.customer_certi_code)
					 and tc.customer_id = #{customer_id}
				union
				select tcm.policy_code,
							 tpa.acknowledge_date,
							 tca.organ_code,
							 (SELECT T.ORGAN_NAME
									FROM DEV_PAS.T_UDMP_ORG_REL T
								 WHERE T.ORGAN_CODE = TCM.ORGAN_CODE) ORGAN_NAME,
							 (SELECT TSC.SALES_CHANNEL_CODE FROM DEV_PAS.T_SALES_CHANNEL TSC WHERE TSC.SALES_CHANNEL_CODE = TCA.CHANNEL_TYPE )submit_channel,
							 tca.agent_code,
							 tca.agent_name,
							 tc.customer_id,
							 tc.customer_name,
							 tc.customer_gender,
							 tc.customer_certi_code,
							 ta.mobile_tel
					from dev_pas.t_contract_master            tcm,
							 dev_pas.t_policy_acknowledgement     tpa,
							 dev_pas.t_insured_list               tph,
							 dev_pas.t_customer                   tc,
							 dev_pas.t_contract_agent             tca,
							 dev_pas.t_address                    ta,
							 dev_pas.t_contract_busi_prod         tcbp,
							 APP___PDS__DBUSER.t_business_product tbp
				 where 1 = 1
					 and tcm.policy_id = tpa.policy_id
					 and tcm.policy_code = tph.policy_code
					 and tcm.policy_code = tca.policy_code
					 and tph.customer_id = tc.customer_id
					 and tph.address_id = ta.address_id
					 and tcm.policy_code = tcbp.policy_code
					 and tcbp.BUSI_PRD_ID = tbp.BUSINESS_PRD_ID
					 and tcm.liability_state not in ('4')
           			 and tbp.tax_revenue_flag = '1'
           			 AND tca.IS_CURRENT_AGENT = '1'
           			 and not exists (select ta.tax_resident_type from dev_pas.t_customer_tax ta where ta.customer_tax_name = tc.customer_name
	                                                and ta.customer_tax_birthday = tc.customer_birthday
													and ta.customer_tax_gender = tc.customer_gender
													and ta.customer_tax_cert_type = tc.customer_cert_type
													and ta.customer_tax_certi_code = tc.customer_certi_code)
					 and tc.customer_id = #{customer_id}
		   ]]>
	</select>
	<!--投保人手机号一号一人核验规则   根据客户五要素查询有效保单投保人的移动电话 -->
	<select id="JRQD_queryOrganRepeatHolderPhone" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select distinct c.customer_id,
			                c.customer_name,
			                c.customer_gender,
			                c.customer_birthday,
			                c.customer_cert_type,
			                c.customer_certi_code
			  from DEV_PAS.T_CONTRACT_MASTER m
			  left join DEV_PAS.T_POLICY_HOLDER ph
			    on m.policy_id = ph.policy_id
			  left join DEV_PAS.T_ADDRESS ad
			    on ad.address_id = ph.address_id
			  left join DEV_PAS.T_CUSTOMER c
			    on c.customer_id = ph.customer_id
			 where 1=1
			   and (m.liability_state = '1' 
			       or (m.liability_state = '3' and m.end_cause not in ('03','02','06','15','01'))
			       or (m.liability_state = '4' and m.lapse_cause not in ('4','1','6')))
			   and ad.mobile_tel = #{mobile_tel} ]]>
			  <if test=" organ_code != null and organ_code != '' "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>
			  <![CDATA[ AND NOT EXISTS (SELECT 1
		          FROM DEV_PAS.T_CUSTOMER TD
		         WHERE  TD.CUSTOMER_ID = C.CUSTOMER_ID 
		            AND TD.CUSTOMER_NAME = #{customer_name}
		            AND TD.CUSTOMER_GENDER = #{customer_gender}
		            AND TD.CUSTOMER_BIRTHDAY =  #{customer_birthday}
		            AND TD.CUSTOMER_CERT_TYPE = #{customer_cert_type}		           
		           ]]>
		           <choose>
		             <when test="certi_code != null and certi_code != ''">
						 AND (TD.CUSTOMER_CERTI_CODE = #{customer_certi_code} OR TD.CUSTOMER_CERTI_CODE = #{certi_code})
					 </when>
					 <otherwise>
					     AND TD.CUSTOMER_CERTI_CODE = #{customer_certi_code}
					 </otherwise>
		           </choose>	           	
		   <![CDATA[ ) ]]>
	</select>

	
	<!-- 95918查询投保人邮箱是否和业务员为同一人 -->
	<select id="JRQD_PA_findAllcounttbrEmailtbr" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  
           select count(1)
  from dev_pas.t_customer a
 where exists (select 1
          from dev_pas.t_policy_holder ta
         where a.customer_id = ta.customer_id)
   and exists (select 1
          from dev_pas.t_agent t
         where t.agent_status in (1, 2)
           and (t.agent_name <> a.customer_name or
               t.cert_type <> a.customer_cert_type or
               t.certi_code <> a.customer_certi_code or
               t.agent_gender <> a.customer_gender or
               t.birthday <> a.customer_birthday)
           and t.agent_email = #{email})
   and a.customer_id = #{customer_id}
	     ]]>
	</select>

	<!-- 投保人手机号重复查询 -->
	<select id="JRQD_PA_queryAbnormalCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select ROWNUM, tcm.policy_code,tc.customer_id from dev_pas.t_contract_master tcm,
              dev_pas.t_policy_holder tph,
							dev_pas.t_customer tc,
							dev_pas.t_address t
							where 1=1
							and tcm.policy_code = tph.policy_code
							and tph.customer_id = tc.customer_id
							and tph.address_id = t.address_id
							and tcm.liability_state <> '3'
							and tc.customer_id <> #{customer_id}
							and t.mobile_tel = #{mobilephone} 
			]]>
	</select>
	
	<!-- 投保人首期缴费账户重复查询 -->
	<select id="JRQD_PA_queryAbnormalAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select ROWNUM,tcm.policy_code,tc.customer_id from dev_pas.t_contract_master tcm,
              dev_pas.t_policy_holder tph,
							dev_pas.t_customer tc,
							dev_pas.t_payer_account t
							where 1=1
							and tcm.policy_code = tph.policy_code
							and tcm.policy_id = t.policy_id
							and tph.customer_id = tc.customer_id
							and tcm.liability_state <> '3'
							and tc.customer_id <> #{customer_id}
             			    and t.account = #{account} 
			]]>
	</select>
	
	<!-- 投保人邮箱账户重复查询 -->
	<select id="JRQD_PA_queryHolderMailCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					  SELECT ROWNUM,TCM.POLICY_CODE,
					  		 TC.CUSTOMER_ID,
						     TC.CUSTOMER_NAME,
						     TC.CUSTOMER_BIRTHDAY,
						     TC.CUSTOMER_GENDER,
						     TC.CUSTOMER_CERT_TYPE,
						     TC.CUSTOMER_CERTI_CODE
					    FROM DEV_PAS.T_CONTRACT_MASTER TCM,
					         DEV_PAS.T_POLICY_HOLDER TPH,
					         DEV_PAS.T_CUSTOMER TC,
					         DEV_PAS.T_ADDRESS T
					   WHERE 1=1
					     AND TCM.POLICY_CODE = TPH.POLICY_CODE
					     AND TPH.CUSTOMER_ID = TC.CUSTOMER_ID
					     AND TPH.ADDRESS_ID = T.ADDRESS_ID
					     AND TCM.LIABILITY_STATE <> '3'
					     AND TC.CUSTOMER_ID <> #{customer_id}
					     AND T.EMAIL = #{mail} 
			]]>
	</select>
	
	<!--根据移动电话号和管理机构查询投保人的客户信息-->
	<select id="JRQD_queryOrganHolderPhone" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select distinct c.customer_id,
			                c.customer_name,
			                c.customer_gender,
			                c.customer_birthday,
			                c.customer_cert_type,
			                c.customer_certi_code
			  from DEV_PAS.T_CONTRACT_MASTER m
			  left join DEV_PAS.T_POLICY_HOLDER ph
			    on m.policy_id = ph.policy_id
			  left join DEV_PAS.T_ADDRESS ad
			    on ad.address_id = ph.address_id
			  left join DEV_PAS.T_CUSTOMER c
			    on c.customer_id = ph.customer_id
			 where 1=1
			   and (m.liability_state = '1' 
			       or (m.liability_state = '3' and m.end_cause not in ('03','02','06','15','01'))
			       or (m.liability_state = '4' and m.lapse_cause not in ('4','1','6')))
			   and ad.mobile_tel = #{mobile_tel} ]]>
			  <if test=" organ_code != null and organ_code != '' "><![CDATA[  and m.ORGAN_CODE like '${organ_code}%'  ]]></if>				           	
	</select>
	
     <!--rm:105277 投保人手机号码不能所在机构在职业务员手机号码相同 -->
	<select id="JRQD_checkAgentMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT ta.agent_code
			  FROM APP___PAS__DBUSER.t_agent ta
			 WHERE ta.agent_status in (1, 2)			 	 
			 AND ta.certi_code <> #{certi_code}
			 AND ta.agent_name <> #{agent_name}			           
		     AND ta.agent_mobile = #{agent_mobile} 
             AND ta.agent_organ_code like '${organ_code}%'				          
         	]]>
	</select>

</mapper>
