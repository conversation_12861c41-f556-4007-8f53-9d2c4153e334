<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="loanPaymentAdvance">

	<sql id="JRQD_queryLoanPaymentCondition">
		<!-- 保单管理机构 -->
		<if test=" organ_code != null and organ_code !='' ">
			<![CDATA[ AND  TCM.ORGAN_CODE in (
				SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T
					START WITH T.ORGAN_CODE = #{organ_code}
					CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE
			) ]]>
		</if>
		<!-- 保单号 -->
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<!-- 按启动参数查询符合条件的总记录数 -->
	<select id="JRQD_queryLoanPaymentCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(TCBP.BUSI_ITEM_ID) end_num
      FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
           APP___PAS__DBUSER.T_POLICY_ACCOUNT     TPA,
           APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM TPAS
      WHERE TCM.POLICY_ID = TCBP.POLICY_ID 
        AND TCBP.BUSI_ITEM_ID = TPA.BUSI_ITEM_ID 
        AND TPAS.ACCOUNT_ID=TPA.ACCOUNT_ID
        AND TPAS.REGULAR_REPAY = 0
        AND TPA.ACCOUNT_TYPE IN (4,5)
        AND TPA.INTEREST_CAPITAL > 0
        AND ((TPAS.IS_AUTO_FAILED = '1' OR TPAS.IS_AUTO_FAILED IS NULL) OR (TPAS.REPAY_DUE_DATE < #{batch_date}))
        AND (CASE
            WHEN TCBP.LIABILITY_STATE = 1 AND
                 to_char(TPAS.REPAY_DUE_DATE, 'dd') =
                 to_char(#{batch_date}, 'dd') THEN
                 (CASE
                   WHEN TPA.ACCOUNT_TYPE = 4  THEN
                    Add_Months(TPAS.REPAY_DUE_DATE,-2)
                   WHEN TPA.ACCOUNT_TYPE = 5 THEN
                    Add_Months(TPAS.REPAY_DUE_DATE, 1)
                 END)
                  WHEN TCBP.LIABILITY_STATE = 4 AND TCBP.LAPSE_CAUSE = 6 THEN
                    Add_Months(TCBP.LAPSE_DATE,22) 
                
                   WHEN TCBP.LIABILITY_STATE = 4 AND TCBP.LAPSE_CAUSE = 1 THEN
                     TCBP.LAPSE_DATE
          END)<= #{batch_date} 	
            AND NOT EXISTS
                 (SELECT 'X'
				FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                 LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                 ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
                 WHERE B.SUB_ID IN ('068', '067')
               	AND B.LOCK_SERVICE_TYPE = 1
                           AND A.POLICY_CODE = TCM.POLICY_CODE)	
		]]>
		<include refid="JRQD_queryLoanPaymentCondition" />
	</select>

	<!-- 查询满足条件的保单 -->
	<select id="JRQD_queryLoanPaymentAdvanceJob" resultType="java.util.Map"
		parameterType="java.util.Map">
		
		<![CDATA[		
		SELECT ROWNUM,A.* FROM   
        (SELECT DISTINCT TCBP.POLICY_CODE,TCBP.POLICY_ID
          FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
               APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
               APP___PAS__DBUSER.T_POLICY_ACCOUNT     TPA,
               APP___PAS__DBUSER.t_policy_account_stream tpas
          WHERE TCM.POLICY_ID = TCBP.POLICY_ID 
            AND TCBP.BUSI_ITEM_ID = TPA.BUSI_ITEM_ID 
            AND tpas.account_id=tpa.account_id
            AND tpas.regular_repay = 0
            AND tpa.account_type in (4,5)
            AND tpa.interest_capital > 0
            AND ((TPAS.IS_AUTO_FAILED = '1' OR TPAS.IS_AUTO_FAILED IS NULL) OR (TPAS.REPAY_DUE_DATE < #{batch_date}))
            AND (CASE
            WHEN TCBP.LIABILITY_STATE = 1 AND
                 to_char(TPAS.REPAY_DUE_DATE, 'dd') =
                 to_char(#{batch_date}, 'dd') THEN
                     (CASE
                       WHEN TPA.ACCOUNT_TYPE = 4 THEN
                        Add_Months(TPAS.REPAY_DUE_DATE,-2)
                       WHEN TPA.ACCOUNT_TYPE = 5 THEN
                        Add_Months(TPAS.REPAY_DUE_DATE, 1)
                     END)
                      WHEN TCBP.LIABILITY_STATE = 4 AND TCBP.LAPSE_CAUSE = 6 THEN
                        Add_Months(TCBP.LAPSE_DATE,22) 
                        WHEN TCBP.LIABILITY_STATE = 4 AND TCBP.LAPSE_CAUSE = 1 THEN
                          TCBP.LAPSE_DATE
                 END)<=#{batch_date}   
                 AND NOT EXISTS
                 (SELECT 'X'
				FROM APP___PAS__DBUSER.T_LOCK_POLICY A
                 LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B
                 ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID
                 WHERE B.SUB_ID IN ('068', '067')
               	AND B.LOCK_SERVICE_TYPE = 1
                           AND A.POLICY_CODE = TCM.POLICY_CODE)		 			       
		]]>
		<include refid="JRQD_queryLoanPaymentCondition" />
		<![CDATA[ ) a  WHERE  MOD(A.POLICY_ID,#{modNum,jdbcType=VARCHAR})= #{start,jdbcType=VARCHAR} ]]>
	</select>


	<!-- 查询下期应缴费日信息 -->
	<sql id="JRQD_findContractExtendCondition">
		<if test=" start_date  != null and start_date !='' "><![CDATA[ AND a.pay_due_date>= #{start_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND a.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>

	<select id="JRQD_findContractExtend" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM ( SELECT A.* FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND A WHERE 1 = 1 
		 ]]>
		<include refid="JRQD_findContractExtendCondition" />
		<![CDATA[ ORDER BY A.pay_due_date ASC )
			  WHERE  ROWNUM<=1
		  ]]>
	</select>

	
	<!-- 查询账户信息 -->
	<select id="JRQD_findPolicyAccount" resultType="java.util.Map"
		parameterType="java.util.Map">		
		<![CDATA[
			SELECT *
  				FROM APP___PAS__DBUSER.t_policy_account t
 			WHERE 1 = 1
   				AND t.policy_id = #{policy_id}
   				AND t.busi_item_id = #{busi_item_id}
   				AND t.account_type = #{account_type}
   				AND t.interest_capital > 0			
		]]>
	</select>
	
	<!-- 查询应领未领生存金 金额 -->
	<select id="JRQD_calcSurvivalAmount" resultType="java.util.Map"
		parameterType="java.util.Map">		
		<![CDATA[
				SELECT 	NVL(SUM(b.fee_amount), 0) AS fee_amount,
	       				NVL(SUM(b.SUM_ASSURED_PAID), 0) AS SUM_ASSURED_PAID,
	       				NVL(SUM(b.TOTAL_BONUS), 0) AS TOTAL_BONUS
				  FROM APP___PAS__DBUSER.t_pay_plan a, APP___PAS__DBUSER.t_pay_due b
				 WHERE a.plan_id = b.plan_id
				   AND a.policy_id = b.policy_id
				   AND a.busi_item_id = b.busi_item_id
				   AND a.pay_plan_type = #{pay_plan_type}
				   AND b.policy_id = #{policy_id}
				   AND b.busi_item_id = #{busi_item_id}
				   AND b.fee_status = #{fee_status}
				   AND b.pay_due_date <= #{batch_date}
		]]>
	</select>
	
	<select id="JRQD_queryPolicyAccountStream" resultType="java.util.Map"
		parameterType="java.util.Map">		
		<![CDATA[
			SELECT *
			  FROM APP___PAS__DBUSER.t_policy_account_stream a
			  inner join APP___PAS__DBUSER.t_policy_account b
			    on a.account_id = b.account_id
		]]>
		<if test=" account_type  != null and account_type !='' "><![CDATA[ and b.account_type = #{account_type} ]]></if>
		<if test=" account_type  == null or account_type =='' "><![CDATA[  and b.account_type in (4, 5) ]]></if>
		<![CDATA[			  
			   and b.interest_capital > 0
			   and a.regular_repay = 0 ]]>
		<if test=" policy_id  != null "><![CDATA[ and b.policy_id = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[  and b.busi_item_id = #{busi_item_id} ]]></if>
	</select>
	
	<select id="JRQD_pa_queryPolicyInterest" resultType="java.util.Map"
		parameterType="java.util.Map">		
		<![CDATA[
			SELECT TPAS.POLICY_ID,
          TPA.POLICY_CODE,
          TCAP.ACCEPT_CODE,
          TCAP.FEE_AMOUNT,
          TCAP.SERVICE_CODE,
          TPAS.STREAM_ID,
          TPAS.REGULAR_REPAY,
          TPA.FEE_TYPE,
          TPAS.BUSI_ITEM_ID,
          TPAS.LOAN_START_DATE,
          TPAS.INTEREST_START_DATE,
          TPA.FINISH_TIME,
          TCPAS.CAPITAL_BALANCE,
          TCPAS.LOG_ID
     FROM APP___PAS__DBUSER.T_POLICY_ACCOUNT_STREAM    TPAS,
          APP___PAS__DBUSER.T_CS_POLICY_ACCOUNT_STREAM TCPAS,
          APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE         TCAP,
          APP___PAS__DBUSER.T_CS_POLICY_CHANGE         TCPC,
          APP___PAS__DBUSER.T_PREM_ARAP                TPA
    WHERE 1 = 1
      AND TPAS.STREAM_ID = TCPAS.STREAM_ID
      AND TPAS.BUSI_ITEM_ID = TCPAS.BUSI_ITEM_ID
      AND TCAP.CHANGE_ID = TCPAS.CHANGE_ID
      AND TPA.BUSINESS_CODE = TCAP.ACCEPT_CODE
      AND TCPAS.CHANGE_ID=TCPC.CHANGE_ID
      AND TCPC.CHANGE_ID=TCAP.CHANGE_ID
      AND TCPC.POLICY_CODE=TPA.POLICY_CODE
      AND TPAS.POLICY_ID=TCPC.POLICY_ID
      AND TPAS.ACCOUNT_TYPE = '4'
      AND TPAS.REGULAR_REPAY = '0'
      AND TCPAS.OLD_NEW = '1'
      AND TCPAS.OPERATION_TYPE = '1'
      AND TCAP.SERVICE_CODE IN ('LN', 'RL')
      AND TCAP.ACCEPT_STATUS = '18'
      AND TPA.FEE_TYPE IN ('P004410000', 'G004790000')
      AND TPA.FINISH_TIME IS NOT NULL
      AND TPA.FEE_STATUS IN ('01', '19')
      AND TPAS.POLICY_ID = #{policy_id}
		]]>
	</select>
</mapper>