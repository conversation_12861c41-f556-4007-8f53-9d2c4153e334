<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="businessProduct">

	<sql id="JRQD_PA_businessProductWhereCondition">
		<if test=" primary_sales_channel != null and primary_sales_channel != ''  "><![CDATA[ AND A.PRIMARY_SALES_CHANNEL = #{primary_sales_channel} ]]></if>
		<if test=" single_joint_life != null and single_joint_life != ''  "><![CDATA[ AND A.SINGLE_JOINT_LIFE = #{single_joint_life} ]]></if>
		<if test=" cover_period_type  != null "><![CDATA[ AND A.COVER_PERIOD_TYPE = #{cover_period_type} ]]></if>
		<if test=" product_category4 != null and product_category4 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY4 = #{product_category4} ]]></if>
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
		<if test=" product_category3 != null and product_category3 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY3 = #{product_category3} ]]></if>
		<if test=" product_code_original != null and product_code_original != ''  "><![CDATA[ AND A.PRODUCT_CODE_ORIGINAL = #{product_code_original} ]]></if>
		<if test=" product_category2 != null and product_category2 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY2 = #{product_category2} ]]></if>
		<if test=" product_category1 != null and product_category1 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY1 = #{product_category1} ]]></if>
		<if test=" product_name_sys != null and product_name_sys != ''  "><![CDATA[ AND A.PRODUCT_NAME_SYS = #{product_name_sys} ]]></if>
		<if test=" product_abbr_name != null and product_abbr_name != ''  "><![CDATA[ AND A.PRODUCT_ABBR_NAME = #{product_abbr_name} ]]></if>
		<if test=" product_static_code != null and product_static_code != ''  "><![CDATA[ AND A.PRODUCT_STATIC_CODE = #{product_static_code} ]]></if>
		<if test=" insured_count_max  != null "><![CDATA[ AND A.INSURED_COUNT_MAX = #{insured_count_max} ]]></if>
		<if test=" insured_count_min  != null "><![CDATA[ AND A.INSURED_COUNT_MIN = #{insured_count_min} ]]></if>
		<if test=" product_desc != null and product_desc != ''  "><![CDATA[ AND A.PRODUCT_DESC = #{product_desc} ]]></if>
		<if test=" release_date  != null  and  release_date  != ''  "><![CDATA[ AND A.RELEASE_DATE = #{release_date} ]]></if>
		<if test=" premium_rate_layer != null and premium_rate_layer != ''  "><![CDATA[ AND A.PREMIUM_RATE_LAYER = #{premium_rate_layer} ]]></if>
		<if test=" premium_currency != null and premium_currency != ''  "><![CDATA[ AND A.PREMIUM_CURRENCY = #{premium_currency} ]]></if>
		<if test=" product_name_std != null and product_name_std != ''  "><![CDATA[ AND A.PRODUCT_NAME_STD = #{product_name_std} ]]></if>
		<if test=" product_code_std != null and product_code_std != ''  "><![CDATA[ AND A.PRODUCT_CODE_STD = #{product_code_std} ]]></if>
		<if test=" renew_option != null and renew_option != ''  "><![CDATA[ AND A.RENEW_OPTION = #{renew_option} ]]></if>
		<if test=" product_category != null and product_category != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY = #{product_category} ]]></if>
		<if test=" schedule_rate  != null "><![CDATA[ AND A.SCHEDULE_RATE = #{schedule_rate} ]]></if>
		<if test=" product_code_sys != null and product_code_sys != ''  "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code_sys} ]]></if>
		<if test=" renewal_max_age != null and renewal_max_age != ''  "><![CDATA[ AND A.RENEWAL_MAX_AGE = #{renewal_max_age} ]]></if>
		<if test=" tax_extension_flag != null and tax_extension_flag != ''  "><![CDATA[ AND A.TAX_EXTENSION_FLAG = #{tax_extension_flag} ]]></if>
		<if test=" waiver_flag != null and waiver_flag != ''  "><![CDATA[ AND A.WAIVER_FLAG = #{waiver_flag} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_queryBusinessProductByBusinessPrdIdCondition">
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_queryContractBusiProdByBusiPrdIdCondition">
		<if test=" business_prd_id != null and business_prd_id != '' "><![CDATA[ AND A.business_prd_id = #{business_prd_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_businessProductWherePoliyCode">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND C.policy_code = #{policy_code} ]]></if>
	</sql>	
<!-- 添加操作 -->
	<insert id="JRQD_PA_addBusinessProduct"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BUSINESS_PRODUCT(
				PRIMARY_SALES_CHANNEL, SINGLE_JOINT_LIFE, COVER_PERIOD_TYPE, PRODUCT_CATEGORY4, BUSINESS_PRD_ID, PRODUCT_CATEGORY3, PRODUCT_CODE_ORIGINAL, 
				PRODUCT_CATEGORY2, PRODUCT_CATEGORY1, PRODUCT_NAME_SYS, PRODUCT_ABBR_NAME, PRODUCT_STATIC_CODE, INSURED_COUNT_MAX, INSURED_COUNT_MIN, 
				PRODUCT_DESC, RELEASE_DATE, PREMIUM_RATE_LAYER, PREMIUM_CURRENCY, PRODUCT_NAME_STD, PRODUCT_CODE_STD, RENEW_OPTION, 
				PRODUCT_CATEGORY, SCHEDULE_RATE, PRODUCT_CODE_SYS,TAX_EXTENSION_FLAG ) 
			VALUES (
				#{primary_sales_channel, jdbcType=VARCHAR}, #{single_joint_life, jdbcType=VARCHAR} , #{cover_period_type, jdbcType=NUMERIC} , #{product_category4, jdbcType=VARCHAR} , #{business_prd_id, jdbcType=NUMERIC} , #{product_category3, jdbcType=VARCHAR} , #{product_code_original, jdbcType=VARCHAR} 
				, #{product_category2, jdbcType=VARCHAR} , #{product_category1, jdbcType=VARCHAR} , #{product_name_sys, jdbcType=VARCHAR} , #{product_abbr_name, jdbcType=VARCHAR} , #{product_static_code, jdbcType=VARCHAR} , #{insured_count_max, jdbcType=NUMERIC} , #{insured_count_min, jdbcType=NUMERIC} 
				, #{product_desc, jdbcType=VARCHAR} , #{release_date, jdbcType=DATE} , #{premium_rate_layer, jdbcType=VARCHAR} , #{premium_currency, jdbcType=VARCHAR} , #{product_name_std, jdbcType=VARCHAR} , #{product_code_std, jdbcType=VARCHAR} , #{renew_option, jdbcType=VARCHAR} 
				, #{product_category, jdbcType=VARCHAR} , #{schedule_rate, jdbcType=NUMERIC} , #{product_code_sys, jdbcType=VARCHAR}, #{tax_extension_flag, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteBusinessProduct" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT WHERE BUSINESS_PRD_ID = #{business_prd_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_PA_updateBusinessProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BUSINESS_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
			PRIMARY_SALES_CHANNEL = #{primary_sales_channel, jdbcType=VARCHAR} ,
			SINGLE_JOINT_LIFE = #{single_joint_life, jdbcType=VARCHAR} ,
		    COVER_PERIOD_TYPE = #{cover_period_type, jdbcType=NUMERIC} ,
			PRODUCT_CATEGORY4 = #{product_category4, jdbcType=VARCHAR} ,
		    BUSINESS_PRD_ID = #{business_prd_id, jdbcType=NUMERIC} ,
			PRODUCT_CATEGORY3 = #{product_category3, jdbcType=VARCHAR} ,
			PRODUCT_CODE_ORIGINAL = #{product_code_original, jdbcType=VARCHAR} ,
			PRODUCT_CATEGORY2 = #{product_category2, jdbcType=VARCHAR} ,
			PRODUCT_CATEGORY1 = #{product_category1, jdbcType=VARCHAR} ,
			PRODUCT_NAME_SYS = #{product_name_sys, jdbcType=VARCHAR} ,
			PRODUCT_ABBR_NAME = #{product_abbr_name, jdbcType=VARCHAR} ,
			PRODUCT_STATIC_CODE = #{product_static_code, jdbcType=VARCHAR} ,
		    INSURED_COUNT_MAX = #{insured_count_max, jdbcType=NUMERIC} ,
		    INSURED_COUNT_MIN = #{insured_count_min, jdbcType=NUMERIC} ,
			PRODUCT_DESC = #{product_desc, jdbcType=VARCHAR} ,
		    RELEASE_DATE = #{release_date, jdbcType=DATE} ,
			PREMIUM_RATE_LAYER = #{premium_rate_layer, jdbcType=VARCHAR} ,
			PREMIUM_CURRENCY = #{premium_currency, jdbcType=VARCHAR} ,
			PRODUCT_NAME_STD = #{product_name_std, jdbcType=VARCHAR} ,
			PRODUCT_CODE_STD = #{product_code_std, jdbcType=VARCHAR} ,
			RENEW_OPTION = #{renew_option, jdbcType=VARCHAR} ,
			PRODUCT_CATEGORY = #{product_category, jdbcType=VARCHAR} ,
		    SCHEDULE_RATE = #{schedule_rate, jdbcType=NUMERIC} ,
			PRODUCT_CODE_SYS = #{product_code_sys, jdbcType=VARCHAR} ,
			TAX_EXTENSION_FLAG = #{tax_extension_flag, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE 1 = 1 ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findBusinessProductByBusinessPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE_ADJUST_PRODUCT_FLAG,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.TAX_EXTENSION_FLAG FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_queryBusinessProductByBusinessPrdIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE_ADJUST_PRODUCT_FLAG,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.TAX_EXTENSION_FLAG FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE_ADJUST_PRODUCT_FLAG,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.TAX_EXTENSION_FLAG FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_businessProductWhereCondition" />
		<![CDATA[ ORDER BY A.PRODUCT_CODE_STD  ]]>
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_PA_findBusinessProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_queryBusinessProductForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.RATE_ADJUST_PRODUCT_FLAG, B.PRIMARY_SALES_CHANNEL, B.SINGLE_JOINT_LIFE, B.COVER_PERIOD_TYPE, B.PRODUCT_CATEGORY4, B.BUSINESS_PRD_ID, B.PRODUCT_CATEGORY3, B.PRODUCT_CODE_ORIGINAL, 
			B.PRODUCT_CATEGORY2, B.PRODUCT_CATEGORY1, B.PRODUCT_NAME_SYS, B.PRODUCT_ABBR_NAME, B.PRODUCT_STATIC_CODE, B.INSURED_COUNT_MAX, B.INSURED_COUNT_MIN, 
			B.PRODUCT_DESC, B.RELEASE_DATE, B.PREMIUM_RATE_LAYER, B.PREMIUM_CURRENCY, B.PRODUCT_NAME_STD, B.PRODUCT_CODE_STD, B.RENEW_OPTION, 
			B.PRODUCT_CATEGORY, B.SCHEDULE_RATE, B.PRODUCT_CODE_SYS,B.RENEWAL_MAX_AGE FROM (
					SELECT ROWNUM RN,A.RATE_ADJUST_PRODUCT_FLAG, A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.TAX_EXTENSION_FLAG FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
		<!--查询单条  -->
		
	<select id="JRQD_PA_findBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		
		 <![CDATA[ SELECT A.RATE_ADJUST_PRODUCT_FLAG,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.TAX_EXTENSION_FLAG,A.WAIVER_FLAG,A.RELA_BUSI_PRD_CODE,A.INSURED_MAX_AGE,A.MEDICA_FLAG
			FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_businessProductWhereCondition" />
		 
	</select>
	<!-- 查询所有操作，不添加条件，用于页面险种代码表初始化 -->
	<select id="JRQD_findAllBusinessProductForPageSelect" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE_ADJUST_PRODUCT_FLAG,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.TAX_EXTENSION_FLAG FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A
			where A.PRODUCT_CODE_STD IS NOT null  order by A.PRODUCT_CODE_STD 
			]]>
	</select>
	
	<!-- 查询所有精选产品用于三段贷款利息配置页面使用 -->
	<select id="JRQD_findAllJingxuanBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE_ADJUST_PRODUCT_FLAG,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.TAX_EXTENSION_FLAG FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A
			WHERE A.PRODUCT_CODE_STD IS NOT NULL AND (A.PRODUCT_CODE_STD LIKE '%9121%' OR A.PRODUCT_CODE_STD LIKE '%913%')  
      		ORDER BY A.PRODUCT_CODE_STD
			]]>
	</select>
	
	<!--by zhaoyoan_wb 根据产品id查询万能的数据条数 -->
	<select id="JRQD_PA_findUniversalTotalByBusinessPrdId" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1) from APP___PAS__DBUSER.T_BUSINESS_PRODUCT 
			where product_category in ('10001','10002') 
			and BUSINESS_PRD_ID=#{business_prd_id}
		]]>
	</select>
    
    <!-- add by zhouly_wb 查询产品ID和是否银代新政签约产品 -->	
 	<select id="JRQD_PA_findBusiProdAndSignFlag" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[    SELECT A.RATE_ADJUST_PRODUCT_FLAG,A.PRIMARY_SALES_CHANNEL,
				             A.SINGLE_JOINT_LIFE,
				             A.COVER_PERIOD_TYPE,
				             A.PRODUCT_CATEGORY4,
				             A.BUSINESS_PRD_ID,
				             A.PRODUCT_CATEGORY3,
				             A.PRODUCT_CODE_ORIGINAL,
				             A.PRODUCT_CATEGORY2,
				             A.PRODUCT_CATEGORY1,
				             A.PRODUCT_NAME_SYS,
				             A.PRODUCT_ABBR_NAME,
				             A.PRODUCT_STATIC_CODE,
				             A.INSURED_COUNT_MAX,
				             A.INSURED_COUNT_MIN,
				             A.PRODUCT_DESC,
				             A.RELEASE_DATE,
				             A.PREMIUM_RATE_LAYER,
				             A.PREMIUM_CURRENCY,
				             A.PRODUCT_NAME_STD,
				             A.PRODUCT_CODE_STD,
				             A.RENEW_OPTION,
				             A.PRODUCT_CATEGORY,
				             A.SCHEDULE_RATE,
				             A.PRODUCT_CODE_SYS,
				             A.RENEWAL_MAX_AGE,
				             A.TAX_EXTENSION_FLAG,
				             B.SIGN_FLAG
				        FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A
				        LEFT JOIN APP___PAS__DBUSER.T_BANK_AGENCY_PRODUCT B
				          ON A.PRODUCT_CODE_SYS = B.PRODUCT_CODE
				       WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_businessProductWhereCondition" />
	</select>
	<!-- 查询险种类型 -->
	<select id="JRQD_PA_findAllBusinessProductByProductId" resultType="java.util.Map" parameterType="java.util.Map">
	
	     select A.BUSINESS_PRD_ID,A.PRODUCT_CATEGORY1 from APP___PDS__DBUSER.t_business_product A WHERE A.BUSINESS_PRD_ID in
        <foreach collection="list"  item="item"
            open="(" separator="," close=")">
            #{item}
        </foreach>
	
	</select>
<!-- 查询个数操作 -->

	<select id="JRQD_PA_findBusinessProductByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 
       TBP.PRODUCT_CATEGORY1,
       TBP.TAX_REVENUE_FLAG,TBP.PRODUCT_NAME_SYS FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,DEV_PAS.T_BUSINESS_PRODUCT TBP WHERE TBP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
		]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND TCBP.BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ]]></if>
	</select>
	
	<select id="JRQD_findlongTermProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE
		  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A,
		       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   B
		 WHERE A.BUSI_PRD_ID = B.BUSINESS_PRD_ID
		   AND B.COVER_PERIOD_TYPE = '0'
		   AND A.MASTER_BUSI_ITEM_ID IS NULL
		   AND A.POLICY_CODE=#{policy_code, jdbcType=VARCHAR}
		]]>
	</select>
	
	<!--可续保转换险种清单列表集成接口  -->
	<select id="JRQD_PA_findBusinessProductByRiskCode" resultType="java.util.Map" parameterType="java.util.Map" >
<![CDATA[	 
select B.BUSI_PROD_CODE as product_code_sys,A.COUNT_WAY,C.PRODUCT_ABBR_NAME

from
     APP___PAS__DBUSER.T_CONTRACT_PRODUCT A,
     APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B,
     APP___PDS__DBUSER.t_business_product C

WHERE A.Policy_Id=B.Policy_Id AND A.BUSI_ITEM_ID=B.BUSI_ITEM_ID AND B.BUSI_PRD_ID=C.BUSINESS_PRD_ID

      AND a.is_master_item =1  and  B.POLICY_CODE=#{policy_code} AND B.BUSI_PROD_CODE=#{product_code_sys}
		]]> 
	</select>
	<!-- 查询产品渠道 -->
	<select id="JRQD_PA_findBusinessProductByPoliyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
      A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.RENEWAL_MAX_AGE,A.TAX_EXTENSION_FLAG,C.POLICY_CODE FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A,
      DEV_PAS.T_CONTRACT_BUSI_PROD B ,DEV_PAS.T_CONTRACT_MASTER C WHERE ROWNUM=1 and C.POLICY_ID=B.Policy_Id AND B.Busi_Prd_Id=A.Business_Prd_Id AND b.master_busi_item_id is null ]]>
		<include refid="JRQD_PA_businessProductWherePoliyCode" />
	</select>
	
	
	
		<!-- 查询产品配置产品-->
	<select id="JRQD_PA_busiProdTypeConfig" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select t.busi_prod_code,                                                                                                                                                                            
			       t.busi_prod_name,
			       t.busi_prod_type1_code,
			       t.busi_prod_type1_name,
			       t.busi_prod_type2_code,
			       t.busi_prod_type2_name,
			       t.busi_prod_type3_code,
			       t.busi_prod_type3_name
  			from dev_pas.t_busi_prod_type_config t
 				where t.busi_prod_code = #{product_code_std}
		 ]]>
	</select>
	
		<!--是否含身故保险责任  -->
	<select id="JRQD_PA_findDieInsuFlag" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[   SELECT LA.LIAB_ID,
					       LA.LIAB_NAME,
					       R.LIBA_CODE,
					       R.PRODUCT_ID,
					       LA.LIAB_CATEGORY
					  FROM DEV_PDS.T_LIABILITY LA, DEV_PDS.T_LIAB_PAY_RELATIVE R
					 WHERE LA.LIAB_ID = R.LIAB_ID
					   AND R.PRODUCT_ID IN
					       (SELECT A.PRODUCT_ID
					          FROM DEV_PDS.T_PRODUCT_LIFE A
					         WHERE BUSINESS_PRD_ID =
					               (SELECT P.BUSINESS_PRD_ID
					                  FROM DEV_PDS.T_BUSINESS_PRODUCT P
					                 WHERE P.PRODUCT_CODE_SYS = (#{product_code_sys})))
					   AND LA.LIAB_CATEGORY = '01'
					]]> 
	</select>
	
	<!-- 查询产品配置项  -->
	<select id="JRQD_PA_queryHealthInsurance" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[   
SELECT A.BUSINESS_PRD_ID,
       A.PRODUCT_CATEGORY,
       A.PRODUCT_CATEGORY1,
       A.SCHEDULE_RATE,
       A.RELEASE_DATE,
       A.PRODUCT_CATEGORY2,
       A.PRODUCT_CATEGORY3,
       A.PRODUCT_CATEGORY4,
       A.PRODUCT_NAME_SYS,
       A.PRODUCT_NAME_STD,
       A.PRODUCT_ABBR_NAME,
       A.PRODUCT_CODE_STD,
       A.PRODUCT_CODE_SYS,
       A.PRODUCT_STATIC_CODE,
       A.PRODUCT_DESC,
       A.PRODUCT_CODE_ORIGINAL,
       A.RENEW_OPTION,
       A.COVER_PERIOD_TYPE,
       A.SINGLE_JOINT_LIFE,
       A.INSURED_COUNT_MIN,
       A.INSURED_COUNT_MAX,
       A.PREMIUM_RATE_LAYER,
       A.PREMIUM_CURRENCY,
       A.WAIVER_FLAG,
       A.PRIMARY_SALES_CHANNEL,
       A.REINSURANCE_FLAG,
       A.INSURANCE_ACCOUNT_FLAG,
       A.PRODUCT_LIFE_RELA,
       A.PRODUCT_LIFE_COUNT,
       A.MEDICA_FLAG,
       A.RATE_ADJUST_PRODUCT_FLAG
       FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT A,
              APP___PAS__DBUSER.T_CONSTANTS_INFO B
       WHERE B.CONSTANTS_KEY IN ('SHORT_PRODUCT_I', 'SHORT_PRODUCT_PT', 'SHORT_PRODUCT_ZD')
       AND A.PRODUCT_CODE_SYS=B.CONSTANTS_VALUE
					]]> 
	  <include refid="JRQD_PA_businessProductWhereCondition" />
	</select>	

	<!-- 查询是否是短期健康险产品  -->
	<select id="JRQD_PA_findProductAnnuityPaymentPlanType" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[  
		 SELECT A.BUSINESS_PRD_ID , 
		        A.PRODUCT_CODE_SYS , 
		        B.ANNUITY_PAYMENT_PLAN_TYPE 
		   FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT A,
		        APP___PDS__DBUSER.T_BUSINESS_PROD_CS B
		  WHERE 1 = 1
		    AND A.BUSINESS_PRD_ID = B.BUSINESS_PRD_ID 
		    AND B.ANNUITY_PAYMENT_PLAN_TYPE =  1  ]]> 
	  <include refid="JRQD_PA_businessProductWhereCondition" />
	</select>	
	
</mapper>
