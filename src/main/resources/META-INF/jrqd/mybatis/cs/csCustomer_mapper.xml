<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.object.dao.ICsCustomerDao">
	<sql id="JRQD_csCustomerWhereCondition">
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
		<if test=" customer_level != null and customer_level != ''  "><![CDATA[ AND A.CUSTOMER_LEVEL = #{customer_level} ]]></if>
		<if test=" death_date  != null  and  death_date  != ''  "><![CDATA[ AND A.DEATH_DATE = #{death_date} ]]></if>
		<if test=" nation_code != null and nation_code != ''  "><![CDATA[ AND A.NATION_CODE = #{nation_code} ]]></if>
		<if test=" job_nature != null and job_nature != ''  "><![CDATA[ AND A.JOB_NATURE = #{job_nature} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" wechat_no != null and wechat_no != ''  "><![CDATA[ AND A.WECHAT_NO = #{wechat_no} ]]></if>
		<if test=" offen_use_tel != null and offen_use_tel != ''  "><![CDATA[ AND A.OFFEN_USE_TEL = #{offen_use_tel} ]]></if>
		<if test=" un_customer_code != null and un_customer_code != ''  "><![CDATA[ AND A.UN_CUSTOMER_CODE = #{un_customer_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" qq != null and qq != ''  "><![CDATA[ AND A.QQ = #{qq} ]]></if>
		<if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" is_parent  != null "><![CDATA[ AND A.IS_PARENT = #{is_parent} ]]></if>
		<if test=" cust_cert_end_date  != null  and  cust_cert_end_date  != ''  "><![CDATA[ AND A.CUST_CERT_END_DATE = #{cust_cert_end_date} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" country_code != null and country_code != ''  "><![CDATA[ AND A.COUNTRY_CODE = #{country_code} ]]></if>
		<if test=" fax_tel != null and fax_tel != ''  "><![CDATA[ AND A.FAX_TEL = #{fax_tel} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND A.OFFICE_TEL = #{office_tel} ]]></if>
		<if test=" cust_pwd != null and cust_pwd != ''  "><![CDATA[ AND A.CUST_PWD = #{cust_pwd} ]]></if>
		<if test=" smoking_flag  != null "><![CDATA[ AND A.SMOKING_FLAG = #{smoking_flag} ]]></if>
		<if test=" marriage_status != null and marriage_status != ''  "><![CDATA[ AND A.MARRIAGE_STATUS = #{marriage_status} ]]></if>
		<if test=" education != null and education != ''  "><![CDATA[ AND A.EDUCATION = #{education} ]]></if>
		<if test=" customer_id_code != null and customer_id_code != ''  "><![CDATA[ AND A.CUSTOMER_ID_CODE = #{customer_id_code} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" other != null and other != ''  "><![CDATA[ AND A.OTHER = #{other} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
		<if test=" job_title != null and job_title != ''  "><![CDATA[ AND A.JOB_TITLE = #{job_title} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" health_status != null and health_status != ''  "><![CDATA[ AND A.HEALTH_STATUS = #{health_status} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" retired_flag  != null "><![CDATA[ AND A.RETIRED_FLAG = #{retired_flag} ]]></if>
		<if test=" drunk_flag  != null "><![CDATA[ AND A.DRUNK_FLAG = #{drunk_flag} ]]></if>
		<if test=" marriage_date  != null  and  marriage_date  != ''  "><![CDATA[ AND A.MARRIAGE_DATE = #{marriage_date} ]]></if>
		<if test=" blacklist_flag  != null "><![CDATA[ AND A.BLACKLIST_FLAG = #{blacklist_flag} ]]></if>
		<if test=" driver_license_type != null and driver_license_type != ''  "><![CDATA[ AND A.DRIVER_LICENSE_TYPE = #{driver_license_type} ]]></if>
		<if test=" housekeeper_flag  != null "><![CDATA[ AND A.HOUSEKEEPER_FLAG = #{housekeeper_flag} ]]></if>
		<if test=" email != null and email != ''  "><![CDATA[ AND A.EMAIL = #{email} ]]></if>
		<if test=" annual_income  != null "><![CDATA[ AND A.ANNUAL_INCOME = #{annual_income} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
		<if test=" job_kind != null and job_kind != ''  "><![CDATA[ AND A.JOB_KIND = #{job_kind} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" religion_code  != null "><![CDATA[ AND A.RELIGION_CODE = #{religion_code} ]]></if>
		<if test=" cust_cert_star_date  != null  and  cust_cert_star_date  != ''  "><![CDATA[ AND A.CUST_CERT_STAR_DATE = #{cust_cert_star_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" syn_mdm_flag  != null "><![CDATA[ AND A.SYN_MDM_FLAG = #{syn_mdm_flag} ]]></if>
		<if test=" customer_vip  != null "><![CDATA[ AND A.CUSTOMER_VIP = #{customer_vip} ]]></if>
		<if test=" live_status  != null "><![CDATA[ AND A.LIVE_STATUS = #{live_status} ]]></if>
		<if test=" comm_method != null and comm_method != ''  "><![CDATA[ AND A.COMM_METHOD = #{comm_method} ]]></if>
	    <if test=" is_subscription_email != null and is_subscription_email != ''  "><![CDATA[ AND A.IS_SUBSCRIPTION_EMAIL = #{is_subscription_email} ]]></if>
		
	</sql>


<!-- 按索引生成的查询条件 -->	

	<sql id="JRQD_findCsCustomerById">
		<if test=" customer_id != null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" change_id != null and change_id != '' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" accept_id  != null  and accept_id != '' "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" old_new != null and old_new != '' "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
	</sql>

	<sql id="JRQD_queryCsCustomerByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

	<sql id="JRQD_queryCsCustomerByCustomerNameCondition">
		<if test=" customer_name != null and customer_name != '' "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsCustomerByCustomerBirthdayCondition">
		<if test=" customer_birthday  != null "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsCustomerByCustomerGenderCondition">
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsCustomerByCustomerCertTypeCondition">
		<if test=" customer_cert_type != null and customer_cert_type != '' "><![CDATA[ AND A.Customer_cert_type = #{customer_cert_type} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsCustomerByCustomerCertiCodeCondition">
		<if test=" customer_certi_code != null and customer_certi_code != '' "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsCustomerByCustomerIdCodeCondition">
		<if test=" customer_id_code != null and customer_id_code != '' "><![CDATA[ AND A.CUSTOMER_ID_CODE = #{customer_id_code} ]]></if>
	</sql>	
	<sql id="JRQD_findCustomerTypeByPolCode">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="JRQD_findCustomerTypeByCusName">
		<if test=" customer_name != null and customer_name != '' "><![CDATA[ AND B.CUSTOMER_NAME = #{customer_name} ]]></if>
	</sql>
<!-- 添加操作 -->
	<insert id="JRQD_addCsCustomer"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CUSTOMER.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CUSTOMER(TAX_RESIDENT_TYPE,SOCI_SECU,COMM_METHOD,
				CUSTOMER_NAME, CUSTOMER_LEVEL, DEATH_DATE, NATION_CODE, JOB_NATURE, REMARK, WECHAT_NO, 
				OFFEN_USE_TEL, UN_CUSTOMER_CODE, OLD_NEW, UPDATE_BY, QQ,  MOBILE_TEL, 
				CHANGE_ID, CUSTOMER_BIRTHDAY, CUSTOMER_CERT_TYPE, IS_PARENT, CUST_CERT_END_DATE, OPERATION_TYPE, COUNTRY_CODE, 
				FAX_TEL, UPDATE_TIME, JOB_CODE, OFFICE_TEL, CUST_PWD, SMOKING_FLAG, MARRIAGE_STATUS, 
				EDUCATION, CUSTOMER_ID_CODE, UPDATE_TIMESTAMP, INSERT_BY, CUSTOMER_GENDER, OTHER, COMPANY_NAME, 
				JOB_TITLE, CUSTOMER_ID, CUSTOMER_HEIGHT, HEALTH_STATUS, CUSTOMER_CERTI_CODE, RETIRED_FLAG, INSERT_TIMESTAMP, 
				DRUNK_FLAG, MARRIAGE_DATE, BLACKLIST_FLAG, DRIVER_LICENSE_TYPE, HOUSEKEEPER_FLAG, EMAIL, ANNUAL_INCOME, 
				ACCEPT_ID, HOUSE_TEL, INSERT_TIME, JOB_KIND, CUSTOMER_WEIGHT, RELIGION_CODE, CUST_CERT_STAR_DATE, 
				LOG_ID, SYN_MDM_FLAG, CUSTOMER_VIP, LIVE_STATUS,OLD_CUSTOMER_ID,CUSTOMER_RISK_LEVEL,IS_SUBSCRIPTION_EMAIL,SECOND_CERT_TYPE,SECOND_CERTI_CODE ) 
			VALUES (#{tax_resident_type, jdbcType=VARCHAR} ,#{soci_secu, jdbcType=NUMERIC},#{comm_method, jdbcType=VARCHAR},
				#{customer_name, jdbcType=VARCHAR}, #{customer_level, jdbcType=VARCHAR} , #{death_date, jdbcType=DATE} , #{nation_code, jdbcType=VARCHAR} , #{job_nature, jdbcType=VARCHAR} , #{remark, jdbcType=VARCHAR} , #{wechat_no, jdbcType=VARCHAR} 
				, #{offen_use_tel, jdbcType=VARCHAR} , #{un_customer_code, jdbcType=VARCHAR} , #{old_new, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{qq, jdbcType=VARCHAR} , #{mobile_tel, jdbcType=VARCHAR} 
				, #{change_id, jdbcType=NUMERIC} , #{customer_birthday, jdbcType=DATE} , #{customer_cert_type, jdbcType=VARCHAR} , #{is_parent, jdbcType=NUMERIC} , #{cust_cert_end_date, jdbcType=DATE} , #{operation_type, jdbcType=VARCHAR} , #{country_code, jdbcType=VARCHAR} 
				, #{fax_tel, jdbcType=VARCHAR} , SYSDATE , #{job_code, jdbcType=VARCHAR} , #{office_tel, jdbcType=VARCHAR} , #{cust_pwd, jdbcType=VARCHAR} , #{smoking_flag, jdbcType=NUMERIC} , #{marriage_status, jdbcType=VARCHAR} 
				, #{education, jdbcType=VARCHAR} , #{customer_id_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{customer_gender, jdbcType=NUMERIC} , #{other, jdbcType=VARCHAR} , #{company_name, jdbcType=VARCHAR} 
				, #{job_title, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{customer_height, jdbcType=NUMERIC} , #{health_status, jdbcType=VARCHAR} , #{customer_certi_code, jdbcType=VARCHAR} , #{retired_flag, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{drunk_flag, jdbcType=NUMERIC} , #{marriage_date, jdbcType=DATE} , #{blacklist_flag, jdbcType=NUMERIC} , #{driver_license_type, jdbcType=VARCHAR} , #{housekeeper_flag, jdbcType=NUMERIC} , #{email, jdbcType=VARCHAR} , #{annual_income, jdbcType=NUMERIC} 
				, #{accept_id, jdbcType=NUMERIC} , #{house_tel, jdbcType=VARCHAR} , SYSDATE , #{job_kind, jdbcType=VARCHAR} , #{customer_weight, jdbcType=NUMERIC} , #{religion_code, jdbcType=NUMERIC} , #{cust_cert_star_date, jdbcType=DATE} 
				,#{log_id,jdbcType=NUMERIC} , #{syn_mdm_flag, jdbcType=NUMERIC} , #{customer_vip, jdbcType=NUMERIC} , #{live_status, jdbcType=NUMERIC} ,#{old_customer_id, jdbcType=NUMERIC},#{customer_risk_level, jdbcType=VARCHAR}
				,#{is_subscription_email, jdbcType=NUMERIC},#{second_cert_type, jdbcType=VARCHAR},#{second_certi_code, jdbcType=VARCHAR})
		 ]]>
	</insert>
	
	<!-- 添加操作 -->
	<insert id="JRQD_addCsCustomerForCopy"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CUSTOMER(TAX_RESIDENT_TYPE,SOCI_SECU,COMM_METHOD,
				CUSTOMER_NAME, CUSTOMER_LEVEL, DEATH_DATE, NATION_CODE, JOB_NATURE, REMARK, WECHAT_NO, 
				OFFEN_USE_TEL, UN_CUSTOMER_CODE, OLD_NEW, UPDATE_BY, QQ,  MOBILE_TEL, 
				CHANGE_ID, CUSTOMER_BIRTHDAY, CUSTOMER_CERT_TYPE, IS_PARENT, CUST_CERT_END_DATE, OPERATION_TYPE, COUNTRY_CODE, 
				FAX_TEL, UPDATE_TIME, JOB_CODE, OFFICE_TEL, CUST_PWD, SMOKING_FLAG, MARRIAGE_STATUS, 
				EDUCATION, CUSTOMER_ID_CODE, UPDATE_TIMESTAMP, INSERT_BY, CUSTOMER_GENDER, OTHER, COMPANY_NAME, 
				JOB_TITLE, CUSTOMER_ID, CUSTOMER_HEIGHT, HEALTH_STATUS, CUSTOMER_CERTI_CODE, RETIRED_FLAG, INSERT_TIMESTAMP, 
				DRUNK_FLAG, MARRIAGE_DATE, BLACKLIST_FLAG, DRIVER_LICENSE_TYPE, HOUSEKEEPER_FLAG, EMAIL, ANNUAL_INCOME, 
				ACCEPT_ID, HOUSE_TEL, INSERT_TIME, JOB_KIND, CUSTOMER_WEIGHT, RELIGION_CODE, CUST_CERT_STAR_DATE, 
				LOG_ID, SYN_MDM_FLAG, CUSTOMER_VIP, LIVE_STATUS,OLD_CUSTOMER_ID,CUSTOMER_RISK_LEVEL,IS_SUBSCRIPTION_EMAIL,SECOND_CERT_TYPE,SECOND_CERTI_CODE ) 
			VALUES (#{tax_resident_type, jdbcType=VARCHAR} ,#{soci_secu, jdbcType=NUMERIC},#{comm_method, jdbcType=VARCHAR},
				#{customer_name, jdbcType=VARCHAR}, #{customer_level, jdbcType=VARCHAR} , #{death_date, jdbcType=DATE} , #{nation_code, jdbcType=VARCHAR} , #{job_nature, jdbcType=VARCHAR} , #{remark, jdbcType=VARCHAR} , #{wechat_no, jdbcType=VARCHAR} 
				, #{offen_use_tel, jdbcType=VARCHAR} , #{un_customer_code, jdbcType=VARCHAR} , #{old_new, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{qq, jdbcType=VARCHAR} , #{mobile_tel, jdbcType=VARCHAR} 
				, #{change_id, jdbcType=NUMERIC} , #{customer_birthday, jdbcType=DATE} , #{customer_cert_type, jdbcType=VARCHAR} , #{is_parent, jdbcType=NUMERIC} , #{cust_cert_end_date, jdbcType=DATE} , #{operation_type, jdbcType=VARCHAR} , #{country_code, jdbcType=VARCHAR} 
				, #{fax_tel, jdbcType=VARCHAR} , SYSDATE , #{job_code, jdbcType=VARCHAR} , #{office_tel, jdbcType=VARCHAR} , #{cust_pwd, jdbcType=VARCHAR} , #{smoking_flag, jdbcType=NUMERIC} , #{marriage_status, jdbcType=VARCHAR} 
				, #{education, jdbcType=VARCHAR} , #{customer_id_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{customer_gender, jdbcType=NUMERIC} , #{other, jdbcType=VARCHAR} , #{company_name, jdbcType=VARCHAR} 
				, #{job_title, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{customer_height, jdbcType=NUMERIC} , #{health_status, jdbcType=VARCHAR} , #{customer_certi_code, jdbcType=VARCHAR} , #{retired_flag, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{drunk_flag, jdbcType=NUMERIC} , #{marriage_date, jdbcType=DATE} , #{blacklist_flag, jdbcType=NUMERIC} , #{driver_license_type, jdbcType=VARCHAR} , #{housekeeper_flag, jdbcType=NUMERIC} , #{email, jdbcType=VARCHAR} , #{annual_income, jdbcType=NUMERIC} 
				, #{accept_id, jdbcType=NUMERIC} , #{house_tel, jdbcType=VARCHAR} , SYSDATE , #{job_kind, jdbcType=VARCHAR} , #{customer_weight, jdbcType=NUMERIC} , #{religion_code, jdbcType=NUMERIC} , #{cust_cert_star_date, jdbcType=DATE} 
				,APP___PAS__DBUSER.S_CS_CUSTOMER.NEXTVAL , #{syn_mdm_flag, jdbcType=NUMERIC} , #{customer_vip, jdbcType=NUMERIC} , #{live_status, jdbcType=NUMERIC} ,#{old_customer_id, jdbcType=NUMERIC},#{customer_risk_level, jdbcType=VARCHAR}
				,#{is_subscription_email, jdbcType=NUMERIC},#{second_cert_type, jdbcType=VARCHAR},#{second_certi_code, jdbcType=VARCHAR})
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsCustomer" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CUSTOMER WHERE  LOG_ID = #{log_id}  ]]>
	</delete>
<!-- 客户职业类别变更  liuxl  开始 -->
	<update id="JRQD_custJobCateGoryUpdate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CUSTOMER ]]>
		<set>
		<trim suffixOverrides=",">
			JOB_NATURE = #{job_nature, jdbcType=VARCHAR} ,
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
			JOB_KIND = #{job_kind, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CUSTOMER_ID = trim(#{customer_id}) AND CHANGE_ID = trim(#{change_id}) AND OLD_NEW = trim(#{old_new}) ]]>
	</update>
<!-- 客户职业类别变更  liuxl  结束 -->
<!-- 修改操作 -->
	<update id="JRQD_updateCsCustomer" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CUSTOMER ]]>
		<set>
		<trim suffixOverrides=",">
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC},
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
			CUSTOMER_LEVEL = #{customer_level, jdbcType=VARCHAR} ,
		    DEATH_DATE = #{death_date, jdbcType=DATE} ,
			NATION_CODE = #{nation_code, jdbcType=VARCHAR} ,
			JOB_NATURE = #{job_nature, jdbcType=VARCHAR} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
			WECHAT_NO = #{wechat_no, jdbcType=VARCHAR} ,
			OFFEN_USE_TEL = #{offen_use_tel, jdbcType=VARCHAR} ,
			UN_CUSTOMER_CODE = #{un_customer_code, jdbcType=VARCHAR} ,
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			QQ = #{qq, jdbcType=VARCHAR} ,
			MOBILE_TEL = #{mobile_tel, jdbcType=VARCHAR} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} ,
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
		    IS_PARENT = #{is_parent, jdbcType=NUMERIC} ,
		    CUST_CERT_END_DATE = #{cust_cert_end_date, jdbcType=DATE} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
			COUNTRY_CODE = #{country_code, jdbcType=VARCHAR} ,
			FAX_TEL = #{fax_tel, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			OFFICE_TEL = #{office_tel, jdbcType=VARCHAR} ,
			CUST_PWD = #{cust_pwd, jdbcType=VARCHAR} ,
		    SMOKING_FLAG = #{smoking_flag, jdbcType=NUMERIC} ,
			MARRIAGE_STATUS = #{marriage_status, jdbcType=VARCHAR} ,
			EDUCATION = #{education, jdbcType=VARCHAR} ,
			CUSTOMER_ID_CODE = #{customer_id_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
			OTHER = #{other, jdbcType=VARCHAR} ,
			COMPANY_NAME = #{company_name, jdbcType=VARCHAR} ,
			JOB_TITLE = #{job_title, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
			HEALTH_STATUS = #{health_status, jdbcType=VARCHAR} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
		    RETIRED_FLAG = #{retired_flag, jdbcType=NUMERIC} ,
		    DRUNK_FLAG = #{drunk_flag, jdbcType=NUMERIC} ,
		    MARRIAGE_DATE = #{marriage_date, jdbcType=DATE} ,
		    BLACKLIST_FLAG = #{blacklist_flag, jdbcType=NUMERIC} ,
			DRIVER_LICENSE_TYPE = #{driver_license_type, jdbcType=VARCHAR} ,
		    HOUSEKEEPER_FLAG = #{housekeeper_flag, jdbcType=NUMERIC} ,
			EMAIL = #{email, jdbcType=VARCHAR} ,
		    ANNUAL_INCOME = #{annual_income, jdbcType=NUMERIC} ,
		    ACCEPT_ID = #{accept_id, jdbcType=NUMERIC} ,
			HOUSE_TEL = #{house_tel, jdbcType=VARCHAR} ,
			JOB_KIND = #{job_kind, jdbcType=VARCHAR} ,
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
		    RELIGION_CODE = #{religion_code, jdbcType=NUMERIC} ,
		    CUST_CERT_STAR_DATE = #{cust_cert_star_date, jdbcType=DATE} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
		    SYN_MDM_FLAG = #{syn_mdm_flag, jdbcType=NUMERIC} ,
		    CUSTOMER_VIP = #{customer_vip, jdbcType=NUMERIC} ,
		    COMM_METHOD = #{comm_method, jdbcType=VARCHAR} ,
		    LIVE_STATUS = #{live_status, jdbcType=NUMERIC} ,
		    CUSTOMER_RISK_LEVEL = #{customer_risk_level, jdbcType=NUMERIC} ,
		    IS_SUBSCRIPTION_EMAIL = #{is_subscription_email, jdbcType=NUMERIC} ,
		    SECOND_CERT_TYPE = #{second_cert_type, jdbcType=VARCHAR} ,
		    SECOND_CERTI_CODE = #{second_certi_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id, jdbcType=NUMERIC} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsCustomerByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsCustomerByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsCustomerByCustomerName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ, A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsCustomerByCustomerNameCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<!-- liuxl: 按客户ID查询单条客户信息：开始  16524612226-->
	<select id="JRQD_findCsCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.LOG_ID, 
			A.SYN_MDM_FLAG,A.ACCEPT_ID, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,a.IS_SUBSCRIPTION_EMAIL,A.CUSTOMER_RISK_LEVEL,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE ROWNUM = 1  ]]>
		<include refid="JRQD_findCsCustomerById" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	<select id="JRQD_findCustomer1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CUSTOMER_CERT_TYPE,A.CUSTOMER_NAME,A.LIVE_STATUS,A.CUSTOMER_CERTI_CODE FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE ROWNUM = 1  ]]>
		<include refid="JRQD_findCsCustomerById" />
	</select>
	<!-- liuxl: 按客户ID查询单条客户信息：结束 -->
		<!-- xianf: 按客户五项基本信息和changeId查询客户-->
	<select id="JRQD_findOneCsCustomerByInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.LOG_ID, 
			A.SYN_MDM_FLAG,A.ACCEPT_ID, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1 
			AND A.OPERATION_TYPE !='3' ]]>
		<include refid="JRQD_csCustomerWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	
	<select id="JRQD_findCsCustomerByCustomerBirthday" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsCustomerByCustomerBirthdayCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsCustomerByCustomerGender" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method ,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsCustomerByCustomerGenderCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsCustomerByCustomerCertType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsCustomerByCustomerCertTypeCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsCustomerByCustomerCertiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsCustomerByCustomerCertiCodeCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsCustomerByCustomerIdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsCustomerByCustomerIdCodeCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ, A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<include refid="JRQD_findCsCustomerById" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<!-- 添加了三个字段（A.COUNTRY_CODE_NAME，A.MARRIAGE_STATUS_NAME，A.DRIVER_LICENSE_TYPE_NAME）的查询，
		优化CC查询页面前端字段（国籍、婚姻状态、驾照类型）转换的问题 （#105_1081） chenxuan -->
	<select id="JRQD_findAllCsCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_RISK_LEVEL,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, 
			(SELECT Y.COUNTRY_NAME FROM APP___PAS__DBUSER.T_COUNTRY Y WHERE Y.COUNTRY_CODE=A.COUNTRY_CODE) AS COUNTRY_CODE_NAME,
			(SELECT E.MARRIAGE FROM APP___PAS__DBUSER.T_MARRIAGE E WHERE E.MARRIAGE_CODE=A.MARRIAGE_STATUS) AS MARRIAGE_STATUS_NAME,
			(SELECT PE.LICENSE_DESC FROM APP___PAS__DBUSER.T_LICENSE_TYPE PE WHERE PE.LICENSE_TYPE=A.DRIVER_LICENSE_TYPE) AS DRIVER_LICENSE_TYPE_NAME,
			A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,A.OLD_CUSTOMER_ID,A.IS_SUBSCRIPTION_EMAIL,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csCustomerWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
<!-- 查询所有投保操作 -->
	<select id="JRQD_findAllCsHolderCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_RISK_LEVEL,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ,  A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,A.OLD_CUSTOMER_ID,A.IS_SUBSCRIPTION_EMAIL,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A,APP___PAS__DBUSER.T_POLICY_HOLDER B WHERE A.CUSTOMER_ID = B.CUSTOMER_ID AND ROWNUM <=  1000  ]]>
		<include refid="JRQD_csCustomerWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsCustomerMobileTel" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ select a.MOBILE_TEL from APP___PAS__DBUSER.T_POLICY_HOLDER t,APP___PAS__DBUSER.T_CUSTOMER A WHERE  A.customer_id=T.customer_id and  T.policy_code=#{policy_code} ]]> 
	</select>
<!-- 查询所有操作 -->
	<select id="JRQD_findAllCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.QQ,  A.MOBILE_TEL, 
			 A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.CUST_PWD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
      A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
      A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE
			 FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csCustomerWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findCsCustomerTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="JRQD_csCustomerWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCsCustomerForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.TAX_RESIDENT_TYPE,B.SOCI_SECU, B.CUSTOMER_NAME, B.CUSTOMER_LEVEL, B.DEATH_DATE, B.NATION_CODE, B.JOB_NATURE, B.REMARK, B.WECHAT_NO, 
			B.OFFEN_USE_TEL, B.UN_CUSTOMER_CODE, B.OLD_NEW, B.QQ, B.MOBILE_TEL, 
			B.CHANGE_ID, B.CUSTOMER_BIRTHDAY, B.CUSTOMER_CERT_TYPE, B.IS_PARENT, B.CUST_CERT_END_DATE, B.OPERATION_TYPE, B.COUNTRY_CODE, 
			B.FAX_TEL, B.JOB_CODE, B.OFFICE_TEL, B.CUST_PWD, B.SMOKING_FLAG, B.MARRIAGE_STATUS, 
			B.EDUCATION, B.CUSTOMER_ID_CODE, B.CUSTOMER_GENDER, B.OTHER, B.COMPANY_NAME, 
			B.JOB_TITLE, B.CUSTOMER_ID, B.CUSTOMER_HEIGHT, B.HEALTH_STATUS, B.CUSTOMER_CERTI_CODE, B.RETIRED_FLAG, 
			B.DRUNK_FLAG, B.MARRIAGE_DATE, B.BLACKLIST_FLAG, B.DRIVER_LICENSE_TYPE, B.HOUSEKEEPER_FLAG, B.EMAIL, B.ANNUAL_INCOME, 
			B.ACCEPT_ID, B.HOUSE_TEL, B.JOB_KIND, B.CUSTOMER_WEIGHT, B.RELIGION_CODE, B.CUST_CERT_STAR_DATE, 
			B.LOG_ID, B.SYN_MDM_FLAG, B.CUSTOMER_VIP, B.LIVE_STATUS FROM (
					SELECT ROWNUM RN,A.TAX_RESIDENT_TYPE, A.SOCI_SECU,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.DEATH_DATE, A.NATION_CODE, A.JOB_NATURE, A.REMARK, A.WECHAT_NO, 
			A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.OLD_NEW, A.QQ, A.MOBILE_TEL, 
			A.CHANGE_ID, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_CERT_TYPE, A.IS_PARENT, A.CUST_CERT_END_DATE, A.OPERATION_TYPE, A.COUNTRY_CODE, 
			A.FAX_TEL, A.JOB_CODE, A.OFFICE_TEL, A.PASS_WORD, A.SMOKING_FLAG, A.MARRIAGE_STATUS, 
			A.EDUCATION, A.CUSTOMER_ID_CODE, A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, 
			A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, 
			A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, 
			A.ACCEPT_ID, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, 
			A.LOG_ID, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,a.comm_method,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE FROM APP___PAS__DBUSER.T_CS_CUSTOMER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_csCustomerWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询用户名称在 保单下是什么角色   投保人或 被保人      -->
	<select id="JRQD_findCustomerTypeByCusNameOrPolCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 1 AS inputType, CUSTOMER_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1=1  ]]>
				<include refid="JRQD_findCustomerTypeByPolCode" />
      	<![CDATA[
      		AND CUSTOMER_ID IN (SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_CUSTOMER B WHERE 1 = 1 ]]>
      			<include refid="JRQD_findCustomerTypeByCusName" />
      	<![CDATA[ )
			UNION
			SELECT 2 AS inputType, CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1=1  ]]>
			    <include refid="JRQD_findCustomerTypeByPolCode" />
		<![CDATA[
       		AND CUSTOMER_ID IN (SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_CUSTOMER B WHERE 1 = 1 ]]>
       			<include refid="JRQD_findCustomerTypeByCusName" />
       	<![CDATA[)
		 ]]>
	</select>
	
	<select id="JRQD_findAllCustomerByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select * from APP___PAS__DBUSER.t_customer t where t.CUSTOMER_ID = #{customer_id}
		 ]]>
	</select>
	<!-- 校验移动电话不能被三名以上客户使用  panmd_wb-->
	<select id="JRQD_findCsCustomersByMTel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select a.customer_id from APP___PAS__DBUSER.T_CS_CUSTOMER A where mobile_tel =#{mobile_tel} group by a.customer_id
			]]> 
	</select>
	<!-- 查询该手机号记录数量xiayt_wb-->
	<select id="JRQD_findCustomersByMTel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select  A.customer_id from APP___PAS__DBUSER.T_customer A where A.mobile_tel=#{mobile_tel} group by a.customer_id
			]]> 
	</select>
	<!-- 查询该手机号是否为当前代理人手机号，并且代理人和投保人不为本人 -->
	<select id="JRQD_findAgentByMTel" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_AGENT CCA
					WHERE CCA.AGENT_MOBILE = #{mobile_tel} AND CCA.POLICY_ID = #{log_id} AND CCA.RELATION_TO_PH != '00'
			]]> 
	</select>
		<!-- 查询客户层交易密码连续输入错误次数lisk_wb-->
	<select id="JRQD_finCsPwdErrorTimes" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT T.customer_id,T.days_error_times,T.error_times,T.final_input_date FROM APP___PAS__DBUSER.T_CS_PWDERRORTIMES T WHERE T.customer_id = #{customer_id}
			]]> 
	</select>
	<insert id="JRQD_addCsPwdErrorTimes"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
		INSERT INTO APP___PAS__DBUSER.T_CS_PWDERRORTIMES(
			customer_id,days_error_times,error_times,final_input_date) 
		VALUES (
			#{customer_id, jdbcType=NUMERIC},#{days_error_times, jdbcType=NUMERIC},#{error_times, jdbcType=NUMERIC},#{final_input_date, jdbcType=DATE}
			)
		 ]]>
	</insert>
	<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsPwdErrorTimes" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_PWDERRORTIMES WHERE  customer_id = #{customer_id}  ]]>
	</delete>
	
	<!-- 修改操作 -->
	<update id="JRQD_updateCsPwdErrorTimes" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PWDERRORTIMES ]]>
		<set>
		<trim suffixOverrides=",">		    
			customer_id = #{customer_id, jdbcType=NUMERIC},
			days_error_times= #{days_error_times, jdbcType=NUMERIC},
			error_times = #{error_times, jdbcType=NUMERIC},
			final_input_date = #{final_input_date, jdbcType=DATE},		
		</trim>
		</set>
		<![CDATA[ WHERE customer_id = #{customer_id, jdbcType=NUMERIC} ]]>
	</update>
	<!-- 根据保单号查询投保人信息 -->
	<select id="JRQD_findpolicyHolderByPolicyCode_cjk" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[	 select A.POLICY_CODE,C.CUSTOMER_ID,C.Customer_Name,A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER B,APP___PAS__DBUSER.t_contract_master A, APP___PAS__DBUSER.T_CUSTOMER C
 
 WHERE A.POLICY_ID=B.POLICY_ID AND B.CUSTOMER_ID=C.CUSTOMER_ID AND A.POLICY_CODE=#{policy_code}
	]]>
	</select>
	<select id="JRQD_findTpolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ SELECT b.CUSTOMER_ID_CODE, b.CUSTOMER_NAME
			  FROM APP___PAS__DBUSER.T_CUSTOMER b, APP___PAS__DBUSER.T_POLICY_HOLDER a
			   WHERE 1 = 1
			   AND A.CUSTOMER_ID = B.CUSTOMER_ID ]]>
	<include refid="JRQD_findCustomerTypeByPolCode" />
	</select>
		
    <!--yangyl_wb 获得下一个customerID-->
	<select id="JRQD_getNextCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT APP___PAS__DBUSER.S_CUSTOMER.NEXTVAL AS serial_number FROM DUAL ]]>
	</select>
	<select id="JRQD_cus_queryCsApplicationByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ select tc.customer_id, tc.customer_name, tc.customer_certi_code
  from APP___PAS__DBUSER.t_customer tc, APP___PAS__DBUSER.t_cs_application a
 where a.customer_id = tc.customer_id
   and tc.customer_cert_type in ('0','5') ]]>
	<include refid="JRQD_csCustomerWhereCondition" />
	</select>
	
		<!-- fanzc_wb 通过changeId查询客户姓名 -->
	<select id="JRQD_findCsCustomerByChangeIdNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select (select cus.customer_name
          from dev_pas.t_customer cus
         where cus.customer_id = t.customer_id) as customer_name
		  from dev_pas.t_cs_policy_holder t
		 where t.change_id = #{change_id}
		   and t.old_new = '1'
    ]]>
	</select>
	
	<!-- 查询客户基本资料变更操作轨迹 -->
	<select id="JRQD_findCustomerLocus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		SELECT T.CHANGE_ID, T.POLICY_CODE, T.ACCEPT_ID
  FROM DEV_PAS.T_CS_POLICY_CHANGE T
 WHERE T.SERVICE_CODE = 'CC'
   AND T.CHANGE_ID IN
       (SELECT CHANGE_ID
          FROM DEV_PAS.T_CS_APPLICATION
         WHERE CUSTOMER_ID = #{customer_id})
 ORDER BY T.INSERT_TIME DESC
		 ]]>
	</select>
	
	
	<select id="JRQD_queryRepeatCustomerPolicy" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	SELECT Y.POLICY_CODE,
       Y.ORGAN_CODE,
       Y.MOBILE_TEL,
       Y.CUSTOMER_ID,
       Y.CUSTOMER_NAME,
       Y.ACCEPT_CODE
  FROM (SELECT ROWNUM RN,
               Z.POLICY_CODE,
               Z.ORGAN_CODE,
               Z.MOBILE_TEL,
               Z.CUSTOMER_ID,
               (SELECT T.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER T WHERE Z.CUSTOMER_ID = T.CUSTOMER_ID) CUSTOMER_NAME,
               Z.ACCEPT_CODE
          FROM (WITH MEL AS (
          						
          						SELECT A.ADDRESS_ID, 
					          a.MOBILE_TEL, 
					          A.CUSTOMER_ID
                               FROM DEV_PAS.T_ADDRESS A
                              WHERE 1 = 1
                                AND (1 <> 1 
                                		]]>
                                		 <if test=" mobile_tel != null and mobile_tel != '' "><![CDATA[OR A.MOBILE_TEL = #{mobile_tel}]]></if>
                                		
                                  <![CDATA[      
                                    )
                              union 
                                  SELECT A.ADDRESS_ID, 
					          a.FIXED_TEL, 
					          A.CUSTOMER_ID
                               FROM DEV_PAS.T_ADDRESS A
                              WHERE 1 = 1
                                AND (1 <> 1 
                                		]]>
                                		 
                                		<if test=" offen_use_tel != null and offen_use_tel != '' "><![CDATA[OR A.FIXED_TEL = #{offen_use_tel}]]></if>
                                  <![CDATA[      
                                    )  
                                    
                                    
                                    )
               
                 SELECT B.POLICY_CODE,
                        D.ORGAN_CODE,
                        A.MOBILE_TEL,
                        A.CUSTOMER_ID,
                        NULL AS ACCEPT_CODE
                   FROM MEL A
                  INNER JOIN DEV_PAS.T_POLICY_HOLDER B
                     ON A.ADDRESS_ID = B.ADDRESS_ID
                  INNER JOIN DEV_PAS.T_CONTRACT_MASTER D
                     ON B.POLICY_ID = D.POLICY_ID
                    AND D.LIABILITY_STATE = '1'
                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD E
                     ON D.POLICY_ID = E.POLICY_ID
                    AND D.POLICY_CODE = E.POLICY_CODE
                    AND E.LIABILITY_STATE = '1'
                    AND ABS((MONTHS_BETWEEN(E.VALIDATE_DATE, E.EXPIRY_DATE)) / 12) > 1
                 
                 UNION
                 
                 SELECT B.POLICY_CODE,
                        D.ORGAN_CODE,
                        A.MOBILE_TEL,
                        A.CUSTOMER_ID,
                        NULL AS ACCEPT_CODE
                   FROM MEL A
                  INNER JOIN DEV_PAS.T_INSURED_LIST B
                     ON A.ADDRESS_ID = B.ADDRESS_ID
                  INNER JOIN DEV_PAS.T_CONTRACT_MASTER D
                     ON B.POLICY_ID = D.POLICY_ID
                    AND D.LIABILITY_STATE = '1'
                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD E
                     ON D.POLICY_ID = E.POLICY_ID
                    AND D.POLICY_CODE = E.POLICY_CODE
                    AND E.LIABILITY_STATE = '1'
                    AND ABS((MONTHS_BETWEEN(E.VALIDATE_DATE, E.EXPIRY_DATE)) / 12) > 1
                 
                 UNION
                 
                 SELECT AA.POLICY_CODE,
                        AA.ORGAN_CODE,
                        AA.CALL_PHONE1 AS MOBILE_TEL,
                        AA.CUSTOMER_ID,
                         AA.ACCEPT_CODE
                   FROM (SELECT A.CALL_PHONE1 ,
                                A.CALL_PHONE2 ,
                                C.POLICY_CODE,
                                A.ACCEPT_CODE,
                                C.ORGAN_CODE,
                                CA.APPLY_NAME,
                                CA.CUSTOMER_ID,
                                ROW_NUMBER() OVER(PARTITION BY A.ACCEPT_CODE ORDER BY B.INSERT_TIME DESC) AS ID1
                           FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
                          INNER JOIN DEV_PAS.T_CS_APPLICATION CA
                             ON A.CHANGE_ID = CA.CHANGE_ID
                              ]]>
                              <if test=" begin_time != null and begin_time != '' and apply_time != null and apply_time != '' ">
                              <![CDATA[AND CA.APPLY_TIME BETWEEN #{begin_time} AND #{apply_time}]]>
                              </if>
                              <![CDATA[
                             AND A.ACCEPT_STATUS = '18'
                          INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
                             ON A.ACCEPT_ID = B.ACCEPT_ID
                         
                          INNER JOIN DEV_PAS.T_CS_CONTRACT_MASTER C
                             ON C.POLICY_CHG_ID = B.POLICY_CHG_ID
                            AND C.OLD_NEW = '1'
                            AND C.LIABILITY_STATE = '1'
                             INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD E
                    		 ON C.POLICY_ID = E.POLICY_ID
                   	 		AND C.POLICY_CODE = E.POLICY_CODE
                    		AND E.LIABILITY_STATE = '1'
                            AND ABS((MONTHS_BETWEEN(E.VALIDATE_DATE, E.EXPIRY_DATE)) / 12) > 1
                          WHERE 1 = 1 
                           AND (1 <> 1 
                                		]]>
                             <if test=" call_Phone1 != null and call_Phone1 != '' "><![CDATA[ OR A.CALL_PHONE1 = #{call_Phone1}]]></if>
                              <![CDATA[      
                                    ))AA  
                       WHERE AA.ID1 = 1
                             
                       UNION
                 
                 SELECT AA.POLICY_CODE,
                        AA.ORGAN_CODE,
                        AA.CALL_PHONE2 AS MOBILE_TEL,
                        AA.CUSTOMER_ID,
                         AA.ACCEPT_CODE
                   FROM (SELECT A.CALL_PHONE1 ,
                                A.CALL_PHONE2 ,
                                C.POLICY_CODE,
                                A.ACCEPT_CODE,
                                C.ORGAN_CODE,
                                CA.APPLY_NAME,
                                CA.CUSTOMER_ID,
                                ROW_NUMBER() OVER(PARTITION BY A.ACCEPT_CODE ORDER BY B.INSERT_TIME DESC) AS ID1
                           FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
                          INNER JOIN DEV_PAS.T_CS_APPLICATION CA
                             ON A.CHANGE_ID = CA.CHANGE_ID
                              ]]>
                              <if test=" begin_time != null and begin_time != '' and apply_time != null and apply_time != '' ">
                              <![CDATA[AND CA.APPLY_TIME BETWEEN #{begin_time} AND #{apply_time}]]>
                              </if>
                              <![CDATA[
                             AND A.ACCEPT_STATUS = '18'
                          INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
                             ON A.ACCEPT_ID = B.ACCEPT_ID
                         
                          INNER JOIN DEV_PAS.T_CS_CONTRACT_MASTER C
                             ON C.POLICY_CHG_ID = B.POLICY_CHG_ID
                            AND C.OLD_NEW = '1'
                            AND C.LIABILITY_STATE = '1'
                             INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD E
                    		 ON C.POLICY_ID = E.POLICY_ID
                   	 		AND C.POLICY_CODE = E.POLICY_CODE
                    		AND E.LIABILITY_STATE = '1'
                            AND ABS((MONTHS_BETWEEN(E.VALIDATE_DATE, E.EXPIRY_DATE)) / 12) > 1
                          WHERE 1 = 1
                           AND (1 <> 1 
                                		]]>
                             <if test=" call_Phone2 != null and call_Phone2 != '' "><![CDATA[ OR A.CALL_PHONE2 = #{call_Phone2}]]></if>
                             <![CDATA[
                             ) )AA
                  WHERE AA.ID1 = 1
                 
                  ) Z
                  WHERE ROWNUM <= #{LESS_NUM}
        ) Y
 WHERE Y.RN > #{GREATER_NUM}
	]]>
	</select>
	
	
	<!--查询移动电话、固定电话重复的保单信息总数-->
	<select id="JRQD_queryRepeatCustomerPolicyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
    <![CDATA[ select count(1) from (
       SELECT ROWNUM RN,
               Z.POLICY_CODE,
               Z.ORGAN_CODE,
               Z.MOBILE_TEL,
               Z.CUSTOMER_ID,
               (SELECT T.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER T WHERE Z.CUSTOMER_ID = T.CUSTOMER_ID) CUSTOMER_NAME,
               Z.ACCEPT_CODE
          FROM (WITH MEL AS  (
          						
          						SELECT A.ADDRESS_ID, 
					          a.MOBILE_TEL, 
					          A.CUSTOMER_ID
                               FROM DEV_PAS.T_ADDRESS A
                              WHERE 1 = 1
                                AND (1 <> 1 
                                		]]>
                                		 <if test=" mobile_tel != null and mobile_tel != '' "><![CDATA[OR A.MOBILE_TEL = #{mobile_tel}]]></if>
                                		
                                  <![CDATA[      
                                    )
                              union 
                                  SELECT A.ADDRESS_ID, 
					          a.FIXED_TEL, 
					          A.CUSTOMER_ID
                               FROM DEV_PAS.T_ADDRESS A
                              WHERE 1 = 1
                                AND (1 <> 1 
                                		]]>
                                		 
                                		<if test=" offen_use_tel != null and offen_use_tel != '' "><![CDATA[OR A.FIXED_TEL = #{offen_use_tel}]]></if>
                                  <![CDATA[      
                                    )  
                                    
                                    
                                    )
               
                 SELECT B.POLICY_CODE,
                        D.ORGAN_CODE,
                        A.MOBILE_TEL,
                        A.CUSTOMER_ID,
                         NULL AS ACCEPT_CODE
                   FROM MEL A
                  INNER JOIN DEV_PAS.T_POLICY_HOLDER B
                     ON A.ADDRESS_ID = B.ADDRESS_ID
                  INNER JOIN DEV_PAS.T_CONTRACT_MASTER D
                     ON B.POLICY_ID = D.POLICY_ID
                    AND D.LIABILITY_STATE = '1'
                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD E
                     ON D.POLICY_ID = E.POLICY_ID
                    AND D.POLICY_CODE = E.POLICY_CODE
                    AND E.LIABILITY_STATE = '1'
                    AND ABS((MONTHS_BETWEEN(E.VALIDATE_DATE, E.EXPIRY_DATE)) / 12) > 1
                 
                 UNION
                 
                 SELECT B.POLICY_CODE,
                        D.ORGAN_CODE,
                        A.MOBILE_TEL,
                        A.CUSTOMER_ID,
                         NULL AS ACCEPT_CODE
                   FROM MEL A
                  INNER JOIN DEV_PAS.T_INSURED_LIST B
                     ON A.ADDRESS_ID = B.ADDRESS_ID
                  INNER JOIN DEV_PAS.T_CONTRACT_MASTER D
                     ON B.POLICY_ID = D.POLICY_ID
                    AND D.LIABILITY_STATE = '1'
                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD E
                     ON D.POLICY_ID = E.POLICY_ID
                    AND D.POLICY_CODE = E.POLICY_CODE
                    AND E.LIABILITY_STATE = '1'
                    AND ABS((MONTHS_BETWEEN(E.VALIDATE_DATE, E.EXPIRY_DATE)) / 12) > 1
                 
                 UNION
                 
                 SELECT AA.POLICY_CODE,
                        AA.ORGAN_CODE,
                        AA.CALL_PHONE1 AS MOBILE_TEL,
                        AA.CUSTOMER_ID,
                         AA.ACCEPT_CODE
                   FROM (SELECT A.CALL_PHONE1 ,
                                A.CALL_PHONE2 ,
                                C.POLICY_CODE,
                                A.ACCEPT_CODE,
                                C.ORGAN_CODE,
                                CA.APPLY_NAME,
                                CA.CUSTOMER_ID,
                                ROW_NUMBER() OVER(PARTITION BY A.ACCEPT_CODE ORDER BY B.INSERT_TIME DESC) AS ID1
                           FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
                          INNER JOIN DEV_PAS.T_CS_APPLICATION CA
                             ON A.CHANGE_ID = CA.CHANGE_ID
                              ]]>
                              <if test=" begin_time != null and begin_time != '' and apply_time != null and apply_time != '' ">
                              <![CDATA[AND CA.APPLY_TIME BETWEEN #{begin_time} AND #{apply_time}]]>
                              </if>
                              <![CDATA[
                             AND A.ACCEPT_STATUS = '18'
                          INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
                             ON A.ACCEPT_ID = B.ACCEPT_ID
                         
                          INNER JOIN DEV_PAS.T_CS_CONTRACT_MASTER C
                             ON C.POLICY_CHG_ID = B.POLICY_CHG_ID
                            AND C.OLD_NEW = '1'
                            AND C.LIABILITY_STATE = '1'
                             INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD E
                    		 ON C.POLICY_ID = E.POLICY_ID
                   	 		AND C.POLICY_CODE = E.POLICY_CODE
                    		AND E.LIABILITY_STATE = '1'
                            AND ABS((MONTHS_BETWEEN(E.VALIDATE_DATE, E.EXPIRY_DATE)) / 12) > 1
                          WHERE 1 = 1 
                           AND (1 <> 1 
                                		]]>
                             <if test=" call_Phone1 != null and call_Phone1 != '' "><![CDATA[ OR A.CALL_PHONE1 = #{call_Phone1}]]></if>
                              <![CDATA[      
                                    )) AA 
                              WHERE AA.ID1 = 1
                       UNION
                 
                 SELECT AA.POLICY_CODE,
                        AA.ORGAN_CODE,
                        AA.CALL_PHONE2 AS MOBILE_TEL,
                        AA.CUSTOMER_ID,
                         AA.ACCEPT_CODE
                   FROM (SELECT A.CALL_PHONE1 ,
                                A.CALL_PHONE2 ,
                                C.POLICY_CODE,
                                A.ACCEPT_CODE,
                                C.ORGAN_CODE,
                                CA.APPLY_NAME,
                                CA.CUSTOMER_ID,
                                ROW_NUMBER() OVER(PARTITION BY A.ACCEPT_CODE ORDER BY B.INSERT_TIME DESC) AS ID1
                           FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
                          INNER JOIN DEV_PAS.T_CS_APPLICATION CA
                             ON A.CHANGE_ID = CA.CHANGE_ID
                              ]]>
                              <if test=" begin_time != null and begin_time != '' and apply_time != null and apply_time != '' ">
                              <![CDATA[AND CA.APPLY_TIME BETWEEN #{begin_time} AND #{apply_time}]]>
                              </if>
                              <![CDATA[
                             AND A.ACCEPT_STATUS = '18'
                          INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
                             ON A.ACCEPT_ID = B.ACCEPT_ID
                         
                          INNER JOIN DEV_PAS.T_CS_CONTRACT_MASTER C
                             ON C.POLICY_CHG_ID = B.POLICY_CHG_ID
                            AND C.OLD_NEW = '1'
                            AND C.LIABILITY_STATE = '1'
                             INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD E
                    		 ON C.POLICY_ID = E.POLICY_ID
                   	 		AND C.POLICY_CODE = E.POLICY_CODE
                    		AND E.LIABILITY_STATE = '1'
                            AND ABS((MONTHS_BETWEEN(E.VALIDATE_DATE, E.EXPIRY_DATE)) / 12) > 1
                          WHERE 1 = 1
                           AND (1 <> 1 
                                		]]>
                             <if test=" call_Phone2 != null and call_Phone2 != '' "><![CDATA[ OR A.CALL_PHONE2 = #{call_Phone2}]]></if>
                             <![CDATA[
                             ))  AA
                  WHERE AA.ID1 = 1
                 
                  ) Z
       ) catable ]]>      
  </select>
	
    <!--查询移动电话、固定电话重复的重复的业务员 -->
	<select id="JRQD_queryRepeatCustomerAgent" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ select B.AGENT_CODE,B.AGENT_NAME,B.AGENT_MOBILE,B.AGENT_ORGAN_CODE,B.SALES_ORGAN_CODE,      
       (SELECT C.SALES_ORGAN_NAME
          FROM DEV_PAS.T_SALES_ORGAN C WHERE  C.ORGAN_LEVEL_CODE='1'
          START WITH C.SALES_ORGAN_CODE=B.SALES_ORGAN_CODE 
          CONNECT BY  PRIOR  C.PARENT_CODE=  C.SALES_ORGAN_CODE ) AS AGENT_AREA,      
        (SELECT C.SALES_ORGAN_NAME
          FROM DEV_PAS.T_SALES_ORGAN C WHERE  C.ORGAN_LEVEL_CODE='2'
          START WITH C.SALES_ORGAN_CODE=B.SALES_ORGAN_CODE 
          CONNECT BY  PRIOR  C.PARENT_CODE=  C.SALES_ORGAN_CODE) AS AGENT_DEPARTMENT,

        (SELECT C.SALES_ORGAN_NAME
          FROM DEV_PAS.T_SALES_ORGAN C WHERE  C.ORGAN_LEVEL_CODE='3'
          START WITH C.SALES_ORGAN_CODE=B.SALES_ORGAN_CODE 
          CONNECT BY  PRIOR  C.PARENT_CODE=  C.SALES_ORGAN_CODE) AS AGENT_GROUP from (
      select ROWNUM RN,d.AGENT_CODE,d.AGENT_NAME,d.AGENT_MOBILE,d.AGENT_ORGAN_CODE,d.SALES_ORGAN_CODE from (
           SELECT DISTINCT ROWNUM RN,T.AGENT_CODE,T.AGENT_NAME,T.AGENT_MOBILE,T.AGENT_ORGAN_CODE,T.SALES_ORGAN_CODE
           FROM DEV_PAS.T_AGENT T WHERE  T.AGENT_MOBILE IN  (  
          SELECT DISTINCT CC.MOBILE_TEL 
          FROM DEV_PAS.T_CUSTOMER CC
         WHERE CC.MOBILE_TEL is not null and ]]>
    <if test=" mobile_tel != null and mobile_tel != '' "><![CDATA[CC.MOBILE_TEL = #{mobile_tel}]]></if>
    <if test=" mobile_tel == null || mobile_tel == '' "><![CDATA[CC.MOBILE_TEL = null]]></if>
    <if test=" call_Phone1 != null and call_Phone1 != '' "><![CDATA[ or cc.mobile_tel = #{call_Phone1}]]></if>
        <![CDATA[  AND EXISTS
         (SELECT 1 FROM DEV_PAS.T_AGENT CA
                 WHERE CC.MOBILE_TEL = CA.AGENT_MOBILE) UNION
         SELECT DISTINCT CC.MOBILE_TEL 
          FROM DEV_PAS.T_ADDRESS CC
         WHERE CC.MOBILE_TEL is not null and ]]>
    <if test=" mobile_tel != null and mobile_tel != '' "><![CDATA[CC.MOBILE_TEL = #{mobile_tel}]]></if>
    <if test=" mobile_tel == null || mobile_tel == '' "><![CDATA[CC.MOBILE_TEL = null]]></if>
    <if test=" call_Phone1 != null and call_Phone1 != '' "><![CDATA[ or cc.mobile_tel = #{call_Phone1}]]></if>
        <![CDATA[  AND EXISTS
         (SELECT 1 FROM DEV_PAS.T_AGENT CA
                WHERE CC.MOBILE_TEL = CA.AGENT_MOBILE) UNION
         SELECT A.AGENT_MOBILE FROM DEV_PAS.T_AGENT A WHERE  A.AGENT_MOBILE IS NOT NULL AND]]>
    <if test=" mobile_tel != null and mobile_tel != '' "><![CDATA[A.AGENT_MOBILE = #{mobile_tel}]]></if>
    <if test=" mobile_tel == null || mobile_tel == '' "><![CDATA[A.AGENT_MOBILE = null]]></if>
    <if test=" call_Phone1 != null and call_Phone1 != '' "><![CDATA[ OR A.AGENT_MOBILE = #{call_Phone1}]]></if>
     <![CDATA[GROUP BY A.AGENT_MOBILE HAVING COUNT(A.AGENT_MOBILE) >1          ) 
    		 AND T.AGENT_STATUS   in('1','2')  
        union 
          SELECT DISTINCT ROWNUM RN,T.AGENT_CODE,T.AGENT_NAME,T.AGENT_PHONE as AGENT_MOBILE,T.AGENT_ORGAN_CODE,T.SALES_ORGAN_CODE
           FROM DEV_PAS.T_AGENT T WHERE  T.AGENT_PHONE IN (   
          SELECT DISTINCT CC.offen_use_tel AS FIXED_TEL
          FROM DEV_PAS.T_CUSTOMER CC
         WHERE CC.offen_use_tel is not null and ]]>
    <if test=" offen_use_tel != null and offen_use_tel != '' "><![CDATA[CC.offen_use_tel = #{offen_use_tel}]]></if>
    <if test=" offen_use_tel == null || offen_use_tel == '' "><![CDATA[CC.offen_use_tel = null]]></if>
    <if test=" call_Phone2 != null and call_Phone2 != '' "><![CDATA[ or cc.offen_use_tel = #{call_Phone2}]]></if>
        <![CDATA[    AND EXISTS
         (SELECT 1 FROM DEV_PAS.T_AGENT CA
                 WHERE CC.offen_use_tel = CA.AGENT_PHONE)   
         UNION      
         SELECT DISTINCT CC.FIXED_TEL AS FIXED_TEL
         
          FROM DEV_PAS.T_ADDRESS CC
         WHERE CC.FIXED_TEL is not null and ]]>
    <if test=" offen_use_tel != null and offen_use_tel != '' "><![CDATA[CC.FIXED_TEL = #{offen_use_tel}]]></if>
    <if test=" offen_use_tel == null || offen_use_tel == '' "><![CDATA[CC.FIXED_TEL = null]]></if>
    <if test=" call_Phone2 != null and call_Phone2 != '' "><![CDATA[ or cc.FIXED_TEL = #{call_Phone2}]]></if>
        <![CDATA[     AND EXISTS
         (SELECT 1 FROM DEV_PAS.T_AGENT CA
                 WHERE CC.FIXED_TEL = CA.AGENT_PHONE) union 
                 select a.agent_phone as FIXED_TEL from dev_pas.t_agent a where  a.agent_phone is not null and]]>
      <if test=" offen_use_tel != null and offen_use_tel != '' "><![CDATA[a.agent_phone = #{offen_use_tel}]]></if>
      <if test=" offen_use_tel == null || offen_use_tel == '' "><![CDATA[a.agent_phone = null]]></if>
      <if test=" call_Phone2 != null and call_Phone2 != '' "><![CDATA[ or a.agent_phone = #{call_Phone2}]]></if>
       <![CDATA[group by a.agent_phone 
      having count(a.agent_phone) >0  
                 
                  ) AND T.AGENT_STATUS IN ('1','2') ) D where  ROWNUM <= #{LESS_NUM}
    ) B  WHERE B.RN > #{GREATER_NUM}]]>
  </select>
	<select id="JRQD_queryRepeatCustomerAgentTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
    <![CDATA[ select count(*) from (
      select B.AGENT_CODE,B.AGENT_NAME,B.AGENT_MOBILE,B.AGENT_ORGAN_CODE from (
           SELECT DISTINCT T.AGENT_CODE,T.AGENT_NAME,T.AGENT_MOBILE,T.AGENT_ORGAN_CODE
           FROM DEV_PAS.T_AGENT T WHERE  T.AGENT_MOBILE IN  (  
          SELECT DISTINCT CC.MOBILE_TEL 
          FROM DEV_PAS.T_CUSTOMER CC
         WHERE CC.MOBILE_TEL is not null and ]]>
    <if test=" mobile_tel != null and mobile_tel != '' "><![CDATA[CC.MOBILE_TEL = #{mobile_tel}]]></if>
    <if test=" mobile_tel == null || mobile_tel == '' "><![CDATA[CC.MOBILE_TEL = null]]></if>
    <if test=" call_Phone1 != null and call_Phone1 != '' "><![CDATA[ or cc.mobile_tel = #{call_Phone1}]]></if>
        <![CDATA[    AND EXISTS
         (SELECT 1 FROM DEV_PAS.T_AGENT CA
                 WHERE CC.MOBILE_TEL = CA.AGENT_MOBILE) UNION
         SELECT DISTINCT CC.MOBILE_TEL 
          FROM DEV_PAS.T_ADDRESS CC
         WHERE CC.MOBILE_TEL is not null and ]]>
    <if test=" mobile_tel != null and mobile_tel != '' "><![CDATA[CC.MOBILE_TEL = #{mobile_tel}]]></if>
    <if test=" mobile_tel == null || mobile_tel == '' "><![CDATA[CC.MOBILE_TEL = null]]></if>
    <if test=" call_Phone1 != null and call_Phone1 != '' "><![CDATA[ or cc.mobile_tel = #{call_Phone1}]]></if>
        <![CDATA[    AND EXISTS
         (SELECT 1 FROM DEV_PAS.T_AGENT CA
                WHERE CC.MOBILE_TEL = CA.AGENT_MOBILE)  UNION
         SELECT A.AGENT_MOBILE FROM DEV_PAS.T_AGENT A WHERE  A.AGENT_MOBILE IS NOT NULL AND]]>
    <if test=" mobile_tel != null and mobile_tel != '' "><![CDATA[A.AGENT_MOBILE = #{mobile_tel}]]></if>
    <if test=" mobile_tel == null || mobile_tel == '' "><![CDATA[A.AGENT_MOBILE = null]]></if>
    <if test=" call_Phone1 != null and call_Phone1 != '' "><![CDATA[ OR A.AGENT_MOBILE = #{call_Phone1}]]></if>
     <![CDATA[GROUP BY A.AGENT_MOBILE HAVING COUNT(A.AGENT_MOBILE) >1          )
      AND T.AGENT_STATUS IN ('1','2')   
        union 
          SELECT DISTINCT T.AGENT_CODE,T.AGENT_NAME,T.AGENT_PHONE as AGENT_MOBILE,T.AGENT_ORGAN_CODE
           FROM DEV_PAS.T_AGENT T WHERE  T.AGENT_PHONE IN (   
          SELECT DISTINCT CC.offen_use_tel AS FIXED_TEL
          FROM DEV_PAS.T_CUSTOMER CC
         WHERE CC.offen_use_tel is not null and ]]>
    <if test=" offen_use_tel != null and offen_use_tel != '' "><![CDATA[CC.offen_use_tel = #{offen_use_tel}]]></if>
    <if test=" offen_use_tel == null || offen_use_tel == '' "><![CDATA[CC.offen_use_tel = null]]></if>
    <if test=" call_Phone2 != null and call_Phone2 != '' "><![CDATA[ or cc.offen_use_tel = #{call_Phone2}]]></if>
        <![CDATA[     AND EXISTS
         (SELECT 1 FROM DEV_PAS.T_AGENT CA
                 WHERE CC.offen_use_tel = CA.AGENT_PHONE)   
         UNION      
         SELECT DISTINCT CC.FIXED_TEL AS FIXED_TEL
         
          FROM DEV_PAS.T_ADDRESS CC
         WHERE CC.FIXED_TEL is not null and ]]>
    <if test=" offen_use_tel != null and offen_use_tel != '' "><![CDATA[CC.FIXED_TEL = #{offen_use_tel}]]></if>
    <if test=" offen_use_tel == null || offen_use_tel == '' "><![CDATA[CC.FIXED_TEL = null]]></if>
    <if test=" call_Phone2 != null and call_Phone2 != '' "><![CDATA[ or cc.FIXED_TEL = #{call_Phone2}]]></if>
        <![CDATA[     AND EXISTS
         (SELECT 1 FROM DEV_PAS.T_AGENT CA
                 WHERE CC.FIXED_TEL = CA.AGENT_PHONE) union 
                 select a.agent_phone as FIXED_TEL from dev_pas.t_agent a where  a.agent_phone is not null and]]>
      <if test=" offen_use_tel != null and offen_use_tel != '' "><![CDATA[a.agent_phone = #{offen_use_tel}]]></if>
      <if test=" offen_use_tel == null || offen_use_tel == '' "><![CDATA[a.agent_phone = null]]></if>
      <if test=" call_Phone2 != null and call_Phone2 != '' "><![CDATA[ or a.agent_phone = #{call_Phone2}]]></if>
       <![CDATA[group by a.agent_phone 
      having count(a.agent_phone) >0     )  AND T.AGENT_STATUS IN ('1','2') ) B   ) D]]>
  </select>
  
  <select id="JRQD_queryCsCustomerForCCTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
  	<![CDATA[ SELECT COUNT(*)
          FROM (SELECT SOCI_SECU,ADDRESS_ID,CUSTOMER_ID,CUSTOMER_HEIGHT,JOB_CODE,CUSTOMER_WEIGHT, APPLY_CODE,LOG_ID,OLD_NEW,
                       POLICY_CODE,CHANGE_ID,LIST_ID,POLICY_CHG_ID,POLICY_ID,SMOKING,COMM_METHOD
                  FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A
                 WHERE ROWNUM <= 1000  ]]>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
                   <![CDATA[
                union all
                SELECT SOCI_SECU,ADDRESS_ID,CUSTOMER_ID, CUSTOMER_HEIGHT,JOB_CODE,CUSTOMER_WEIGHT,APPLY_CODE,LOG_ID,OLD_NEW,
						POLICY_CODE,CHANGE_ID,LIST_ID,POLICY_CHG_ID,POLICY_ID,SMOKING,COMM_METHOD
                  FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A
                 WHERE 1 = 1  AND ROWNUM <= 1000 ]]>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
                 <![CDATA[  )  ]]>
  </select>
  
  <!--查询基本资料变更分页查询用户信息 add by hhb -->
  <select id="JRQD_queryCsCustomerForCC" resultType="java.util.Map" parameterType="java.util.Map">
  	<![CDATA[  
		SELECT
       C.RN, C.SOCI_SECU,C.SMOKING,C.ADDRESS_ID,C.CUSTOMER_HEIGHT,C.CUSTOMER_ID,C.JOB_CODE,C.CUSTOMER_WEIGHT,
       C.APPLY_CODE,C.LOG_ID,C.OLD_NEW,C.POLICY_CODE,C.CHANGE_ID,C.LIST_ID,C.POLICY_CHG_ID,C.POLICY_ID,C.COMM_METHOD
 		 FROM (SELECT ROWNUM RN,B.SOCI_SECU,B.SMOKING,B.ADDRESS_ID,B.CUSTOMER_HEIGHT,B.CUSTOMER_ID,B.JOB_CODE,B.CUSTOMER_WEIGHT,
               B.APPLY_CODE,B.LOG_ID,B.OLD_NEW,B.POLICY_CODE,B.CHANGE_ID,B.LIST_ID,B.POLICY_CHG_ID,B.POLICY_ID,B.COMM_METHOD
          FROM (SELECT SOCI_SECU,ADDRESS_ID,CUSTOMER_ID,CUSTOMER_HEIGHT,JOB_CODE,CUSTOMER_WEIGHT, APPLY_CODE,LOG_ID,OLD_NEW,
                       POLICY_CODE,CHANGE_ID,LIST_ID,POLICY_CHG_ID,POLICY_ID,SMOKING,COMM_METHOD
                  FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A
                 WHERE ROWNUM <= 1000  ]]>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
                   <![CDATA[
                UNION ALL 
                SELECT SOCI_SECU,ADDRESS_ID,CUSTOMER_ID, CUSTOMER_HEIGHT,JOB_CODE,CUSTOMER_WEIGHT,APPLY_CODE,LOG_ID,OLD_NEW,
						POLICY_CODE,CHANGE_ID,LIST_ID,POLICY_CHG_ID,POLICY_ID,SMOKING,COMM_METHOD
                  FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A
                 WHERE 1 = 1  AND ROWNUM <= 1000 ]]>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
                 <![CDATA[  ) B
         				WHERE ROWNUM <= #{LESS_NUM}) C
							 WHERE C.RN > #{GREATER_NUM}
  				]]>
  </select> 
  <!-- 查询客户基本资料变更操作轨迹 -->
	<select id="JRQD_findCsAllCustomerOfHolderAndInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select cc.customer_id,cc.customer_name,cc.customer_birthday,cc.customer_cert_type,cc.customer_certi_code,cc.customer_gender
      from dev_pas.t_cs_policy_change cpc, dev_pas.t_cs_policy_holder cph,dev_pas.t_cs_customer cc
     where cpc.accept_id = #{accept_id}
       and cpc.policy_id = cph.policy_id
       and cpc.policy_chg_id = cph.policy_chg_id
       and cph.old_new = '1'
       and cc.customer_id = cph.customer_id
       and cc.accept_id = cpc.accept_id
       and cc.old_new = '1'
       and cpc.service_code in ('CC','CM','CT','XT','IT','AI','PG','AG','LN','RL','CS','PT','EA')
       union  
       select cc.customer_id,cc.customer_name,cc.customer_birthday,cc.customer_cert_type,cc.customer_certi_code,cc.customer_gender
      from dev_pas.t_cs_policy_change cpc, dev_pas.t_cs_insured_list cil,dev_pas.t_cs_customer cc
     where cpc.accept_id = #{accept_id}
       and cpc.policy_id = cil.policy_id
       and cpc.policy_chg_id = cil.policy_chg_id
       and cil.old_new = '1'
       and cc.customer_id = cil.customer_id
       and cc.accept_id = cpc.accept_id
       and cc.old_new = '1'
       and cpc.service_code in ('CC','CM','CT','XT','IT','AI','PG','AG','LN','RL','CS','PT','EA')
       union
       select cc.customer_id,cc.customer_name,cc.customer_birthday,cc.customer_cert_type,cc.customer_certi_code,cc.customer_gender
      from dev_pas.t_cs_policy_change cpc, dev_pas.t_insured_list cil,dev_pas.t_customer cc
     where cpc.accept_id = #{accept_id}
       and cpc.policy_id = cil.policy_id
       --and cpc.policy_chg_id = cil.policy_chg_id
      -- and cil.old_new = '1'
       and cc.customer_id = cil.customer_id
       and cpc.service_code in ('CC','CM','CT','XT','IT','AI','PG','AG','LN','RL','CS','PT','EA')
       union
       select cc.customer_id,cc.customer_name,cc.customer_birthday,cc.customer_cert_type,cc.customer_certi_code,cc.customer_gender
      from dev_pas.t_cs_policy_change cpc, dev_pas.t_policy_holder cph,dev_pas.t_customer cc
     where cpc.accept_id = #{accept_id}
       and cpc.policy_id = cph.policy_id
       --and cpc.policy_chg_id = cph.policy_chg_id
       --and cph.old_new = '1'
       and cc.customer_id = cph.customer_id
       and cpc.service_code in ('CC','CM','CT','XT','IT','AI','PG','AG','LN','RL','CS','PT','EA')
		 ]]>
	</select>
	
	<select id="JRQD_checkChangeJobCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select nvl(cil.job_code,0) job_code from dev_pas.t_cs_insured_list cil ,dev_pas.t_cs_policy_change cpc where cil.policy_chg_id = cpc.policy_chg_id
			and cpc.accept_id = #{accept_id} and cil.old_new = '0' and cil.customer_id = #{customer_id}
			union 
			select nvl(cph.job_code,0)job_code from dev_pas.t_cs_policy_holder cph ,dev_pas.t_cs_policy_change cpc where cpc.policy_chg_id = cph.policy_chg_id
			and cpc.accept_id = #{accept_id} and cph.old_new = '0' and cph.customer_id = #{customer_id}
			union
			select nvl(cc.job_code,0) from dev_pas.t_cs_customer cc where cc.accept_id = #{accept_id} and cc.customer_id = #{customer_id} and cc.old_new = '0'
		]]>
	</select>
</mapper>


