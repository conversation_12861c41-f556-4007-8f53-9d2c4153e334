<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.object.dao.ICsContractBusiProdDao">
	<sql id="JRQD_csContractBusiProdWhereCondition">
		<if test=" gurnt_start_date  != null  and  gurnt_start_date  != ''  "><![CDATA[ AND A.GURNT_START_DATE = #{gurnt_start_date} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" gurnt_period  != null "><![CDATA[ AND A.GURNT_PERIOD = #{gurnt_period} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" hesitation2acc  != null "><![CDATA[ AND A.HESITATION2ACC = #{hesitation2acc} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" waiver  != null "><![CDATA[ AND A.WAIVER = #{waiver} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" master_busi_item_id  != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" renewal_state  != null "><![CDATA[ AND A.RENEWAL_STATE = #{renewal_state} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" renew  != null "><![CDATA[ AND A.RENEW = #{renew} ]]></if>
		<if test=" renew_times  != null "><![CDATA[ AND A.RENEW_TIMES = #{renew_times} ]]></if>
		<if test=" waiver_end  != null  and  waiver_end  != ''  "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" gurnt_perd_type != null and gurnt_perd_type != ''  "><![CDATA[ AND A.GURNT_PERD_TYPE = #{gurnt_perd_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" joint_life_flag  != null "><![CDATA[ AND A.JOINT_LIFE_FLAG = #{joint_life_flag} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" due_lapse_date  != null  and  due_lapse_date  != ''  "><![CDATA[ AND A.DUE_LAPSE_DATE = #{due_lapse_date} ]]></if>
		<if test=" waiver_start  != null  and  waiver_start  != ''  "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" prd_pkg_code != null and prd_pkg_code != ''  "><![CDATA[ AND A.PRD_PKG_CODE = #{prd_pkg_code} ]]></if>
		<if test=" gurnt_renew_end  != null  and  gurnt_renew_end  != ''  "><![CDATA[ AND A.GURNT_RENEW_END = #{gurnt_renew_end} ]]></if>
		<if test=" gurnt_renew_start  != null  and  gurnt_renew_start  != ''  "><![CDATA[ AND A.GURNT_RENEW_START = #{gurnt_renew_start} ]]></if>
		<!-- 退保标识， SURRENDER_FLAG NUMBER(1) surrender_flag-->
	   <if test=" surrender_flag  != null  and  surrender_flag  != ''  "><![CDATA[ AND A.SURRENDER_FLAG = #{surrender_flag} ]]></if>
	   
	   <if test=" hesitation_period_day  != null  and  hesitation_period_day  != ''  "><![CDATA[ AND A.HESITATION_PERIOD_DAY = #{hesitation_period_day} ]]></if>
	   
	   <if test=" old_pol_no  != null  and  old_pol_no  != ''  "><![CDATA[ AND A.old_pol_no = #{old_pol_no} ]]></if>
	   <if test=" assurerenew_flag  != null  and  assurerenew_flag  != ''  "><![CDATA[ AND A.assurerenew_flag = #{assurerenew_flag} ]]></if>
	   <if test=" is_rpu  != null "><![CDATA[ AND A.IS_RPU = #{is_rpu} ]]></if>
	   <if test=" is_longterm  != null "><![CDATA[ AND B.COVER_PERIOD_TYPE = '1' ]]></if>
	</sql>

	<sql id="JRQD_queryBusinessProductByBusinessPrdIdCondition">
		<if test=" business_prd_id  != null "><![CDATA[ AND BP.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
	</sql>
	<sql id="JRQD_queryBusinessProductByUnitNameIdCondition">
		<if test=" unit_name  != null "><![CDATA[ AND U.UNIT_NAME = #{unit_name} ]]></if>
	</sql>
	<!-- zhulh  queryListById-->
	<sql id="JRQD_queryListById">
		<if test=" busi_prd_id  != null and  busi_prd_id != '' "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" product_id  != null and  product_id != '' "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
	</sql>
	
	
	<!-- liuxl: 自动生成主键 -->
	<select id="JRQD_findBusiProdNextId" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT APP___PAS__DBUSER.S_BUSI_PROD_BASIC_CFG.NEXTVAL FROM DUAL ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>
	<!-- 按索引生成的查询条件 -->
	<sql id="JRQD_queryCsContractBusiProdByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>
 
	<!-- 添加操作 liuxingle：修改险种Id的生成方式 -->
	<insert id="JRQD_addCsContractBusiProd" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CONTRACT_BUSI_PROD.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD(
				REINSURED,CAN_CHANGE_FLAG,CAN_REINSURE_FLAG,CHARGE_FLAG,
				IS_RPU,ASSURERENEW_FLAG,OLD_POL_NO,GURNT_START_DATE, BUSI_PRD_ID, GURNT_PERIOD, BUSI_PROD_CODE, IS_WAIVED, HESITATION2ACC, 
				APPLY_CODE, OLD_NEW, UPDATE_BY, CHANGE_ID, POLICY_ID, WAIVER, OPERATION_TYPE, 
				MASTER_BUSI_ITEM_ID, UPDATE_TIME, PAIDUP_DATE, EXPIRY_DATE, LIABILITY_STATE, POLICY_CODE, RERINSTATE_DATE,
				RENEW_DECISION, VALIDATE_DATE, UPDATE_TIMESTAMP, RENEWAL_STATE, INSERT_BY, APL_PERMIT, APPLY_DATE, 
				RENEW, INSERT_TIMESTAMP, RENEW_TIMES, WAIVER_END, MATURITY_DATE, GURNT_PERD_TYPE, POLICY_CHG_ID, 
				BUSI_ITEM_ID, LAPSE_DATE, JOINT_LIFE_FLAG, INSERT_TIME, END_CAUSE, ISSUE_DATE, LAPSE_CAUSE, 
				DECISION_CODE, LOG_ID, SUSPEND_DATE, INITIAL_PREM_DATE, SUSPEND_CAUSE, DUE_LAPSE_DATE, WAIVER_START, 
				PRD_PKG_CODE, GURNT_RENEW_END, GURNT_RENEW_START, INITIAL_VALIDATE_DATE,SURRENDER_FLAG,HESITATION_PERIOD_DAY,NEXT_FLAG,
				COND_PRE_CHECK_FLAG,ORDER_ID,MEET_POV_STANDARD_FLAG,APPLICANT_SPE_PEOPLE)
			VALUES (
				 #{reinsured, jdbcType=VARCHAR} ,#{can_change_flag, jdbcType=NUMERIC} ,#{can_reinsure_flag, jdbcType=NUMERIC} ,#{charge_flag, jdbcType=NUMERIC} ,
				  #{is_rpu, jdbcType=NUMERIC} ,#{assurerenew_flag, jdbcType=NUMERIC} , #{old_pol_no, jdbcType=VARCHAR} ,#{gurnt_start_date, jdbcType=DATE}, #{busi_prd_id, jdbcType=NUMERIC} , #{gurnt_period, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{is_waived, jdbcType=NUMERIC} , #{hesitation2acc, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , #{old_new, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{waiver, jdbcType=NUMERIC} , #{operation_type, jdbcType=VARCHAR} 
				, #{master_busi_item_id, jdbcType=NUMERIC} , SYSDATE , #{paidup_date, jdbcType=DATE} , #{expiry_date, jdbcType=DATE} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} 
				, #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP, nvl(#{renewal_state, jdbcType=NUMERIC},0) , #{insert_by, jdbcType=NUMERIC} , #{apl_permit, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} 
				, #{renew, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{renew_times, jdbcType=NUMERIC} , #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{gurnt_perd_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} , #{joint_life_flag, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , #{lapse_cause, jdbcType=VARCHAR} 
				, #{decision_code, jdbcType=VARCHAR} , #{log_id,jdbcType=NUMERIC} , #{suspend_date, jdbcType=DATE} , #{initial_prem_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{due_lapse_date, jdbcType=DATE} , #{waiver_start, jdbcType=DATE} 
				, #{prd_pkg_code, jdbcType=VARCHAR} , #{gurnt_renew_end, jdbcType=DATE} , #{gurnt_renew_start, jdbcType=DATE} , #{initial_validate_date, jdbcType=DATE},#{surrender_flag, jdbcType=NUMERIC},#{hesitation_period_day, jdbcType=NUMERIC} ,#{next_flag, jdbcType=NUMERIC}
				, #{cond_pre_check_flag, jdbcType=NUMERIC},#{order_id, jdbcType=VARCHAR}
				, #{meet_pov_standard_flag, jdbcType=NUMERIC}
				, #{applicant_spe_people, jdbcType=NUMERIC})
		 ]]>
	</insert>
	
	
	
	<!-- 添加操作 liuxingle：修改险种Id的生成方式 -->
	<insert id="JRQD_addCsContractBusiProdForCopy" useGeneratedKeys="false" parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD(
				REINSURED,CAN_CHANGE_FLAG,CAN_REINSURE_FLAG,CHARGE_FLAG,
				IS_RPU,ASSURERENEW_FLAG,OLD_POL_NO,GURNT_START_DATE, BUSI_PRD_ID, GURNT_PERIOD, BUSI_PROD_CODE, IS_WAIVED, HESITATION2ACC, 
				APPLY_CODE, OLD_NEW, UPDATE_BY, CHANGE_ID, POLICY_ID, WAIVER, OPERATION_TYPE, 
				MASTER_BUSI_ITEM_ID, UPDATE_TIME, PAIDUP_DATE, EXPIRY_DATE, LIABILITY_STATE, POLICY_CODE, RERINSTATE_DATE,
				RENEW_DECISION, VALIDATE_DATE, UPDATE_TIMESTAMP, RENEWAL_STATE, INSERT_BY, APL_PERMIT, APPLY_DATE, 
				RENEW, INSERT_TIMESTAMP, RENEW_TIMES, WAIVER_END, MATURITY_DATE, GURNT_PERD_TYPE, POLICY_CHG_ID, 
				BUSI_ITEM_ID, LAPSE_DATE, JOINT_LIFE_FLAG, INSERT_TIME, END_CAUSE, ISSUE_DATE, LAPSE_CAUSE, 
				DECISION_CODE, LOG_ID, SUSPEND_DATE, INITIAL_PREM_DATE, SUSPEND_CAUSE, DUE_LAPSE_DATE, WAIVER_START, 
				PRD_PKG_CODE, GURNT_RENEW_END, GURNT_RENEW_START, INITIAL_VALIDATE_DATE,SURRENDER_FLAG,HESITATION_PERIOD_DAY,COND_PRE_CHECK_FLAG,
				ORDER_ID)
			VALUES (
				  #{reinsured, jdbcType=VARCHAR} ,#{can_change_flag, jdbcType=NUMERIC} ,#{can_reinsure_flag, jdbcType=NUMERIC} ,#{charge_flag, jdbcType=NUMERIC} ,
				  #{is_rpu, jdbcType=NUMERIC} ,#{assurerenew_flag, jdbcType=NUMERIC} , #{old_pol_no, jdbcType=VARCHAR} ,#{gurnt_start_date, jdbcType=DATE}, #{busi_prd_id, jdbcType=NUMERIC} , #{gurnt_period, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{is_waived, jdbcType=NUMERIC} , #{hesitation2acc, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , #{old_new, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{waiver, jdbcType=NUMERIC} , #{operation_type, jdbcType=VARCHAR} 
				, #{master_busi_item_id, jdbcType=NUMERIC} , SYSDATE , #{paidup_date, jdbcType=DATE} , #{expiry_date, jdbcType=DATE} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} 
				, #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP, nvl(#{renewal_state, jdbcType=NUMERIC},0) , #{insert_by, jdbcType=NUMERIC} , #{apl_permit, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} 
				, #{renew, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{renew_times, jdbcType=NUMERIC} , #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{gurnt_perd_type, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} , #{joint_life_flag, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , #{lapse_cause, jdbcType=VARCHAR} 
				, #{decision_code, jdbcType=VARCHAR} , APP___PAS__DBUSER.S_CS_CONTRACT_BUSI_PROD.NEXTVAL , #{suspend_date, jdbcType=DATE} , #{initial_prem_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{due_lapse_date, jdbcType=DATE} , #{waiver_start, jdbcType=DATE} 
				, #{prd_pkg_code, jdbcType=VARCHAR} , #{gurnt_renew_end, jdbcType=DATE} , #{gurnt_renew_start, jdbcType=DATE} , #{initial_validate_date, jdbcType=DATE},#{surrender_flag, jdbcType=NUMERIC},#{hesitation_period_day, jdbcType=NUMERIC} 
				,#{cond_pre_check_flag, jdbcType=NUMERIC},#{order_id, jdbcType=VARCHAR})
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="JRQD_deleteCsContractBusiProd" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD WHERE LOG_ID = #{log_id} ]]>
	</delete>

	<!-- 条件删除操作 -->
	<delete id="JRQD_deleteCsContractBusiProdByCondition" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE 1=1 ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
	</delete>
	
	<!-- 修改操作 -->
	<update id="JRQD_updateContractBusiProdBYPolicyANDBusi" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD ]]>
		<set>
			<trim suffixOverrides=",">
				renewal_state = #{renewal_state, jdbcType=NUMERIC},
				renew_decision = #{renew_decision, jdbcType=NUMERIC},
				can_change_flag = #{can_change_flag, jdbcType=NUMERIC},
		        can_reinsure_flag = #{can_reinsure_flag, jdbcType=NUMERIC}, 
			</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} AND 
		BUSI_ITEM_ID = #{busi_item_id}  ]]>
	</update>
 
	<!-- 修改操作 -->
	<update id="JRQD_updateCsContractBusiProd" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD ]]>
		<set>
			<trim suffixOverrides=",">
			IS_RPU = #{is_rpu, jdbcType=NUMERIC} ,
			ASSURERENEW_FLAG = #{assurerenew_flag, jdbcType=NUMERIC} ,
			OLD_POL_NO = #{old_pol_no, jdbcType=VARCHAR},
			GURNT_START_DATE = #{gurnt_start_date, jdbcType=DATE} ,
		    BUSI_PRD_ID = #{busi_prd_id, jdbcType=NUMERIC} ,
		    GURNT_PERIOD = #{gurnt_period, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
		    HESITATION2ACC = #{hesitation2acc, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    WAIVER = #{waiver, jdbcType=NUMERIC} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
		    MASTER_BUSI_ITEM_ID = #{master_busi_item_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
		    RENEW_DECISION = #{renew_decision, jdbcType=NUMERIC} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    RENEWAL_STATE = #{renewal_state, jdbcType=NUMERIC} ,
		    APL_PERMIT = #{apl_permit, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    RENEW = #{renew, jdbcType=NUMERIC} ,
		    RENEW_TIMES = #{renew_times, jdbcType=NUMERIC} ,
		    WAIVER_END = #{waiver_end, jdbcType=DATE} ,
		    MATURITY_DATE = #{maturity_date, jdbcType=DATE} ,
			GURNT_PERD_TYPE = #{gurnt_perd_type, jdbcType=VARCHAR} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
		    JOINT_LIFE_FLAG = #{joint_life_flag, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		    ISSUE_DATE = #{issue_date, jdbcType=DATE} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
		    INITIAL_PREM_DATE = #{initial_prem_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
		    DUE_LAPSE_DATE = #{due_lapse_date, jdbcType=DATE} ,
		    WAIVER_START = #{waiver_start, jdbcType=DATE} ,
			PRD_PKG_CODE = #{prd_pkg_code, jdbcType=VARCHAR} ,
		    GURNT_RENEW_END = #{gurnt_renew_end, jdbcType=DATE} ,
		    GURNT_RENEW_START = #{gurnt_renew_start, jdbcType=DATE},
		    INITIAL_VALIDATE_DATE = #{initial_validate_date, jdbcType=DATE},
		    SURRENDER_FLAG = #{surrender_flag, jdbcType=NUMERIC} ,
		    HESITATION_PERIOD_DAY = #{hesitation_period_day, jdbcType=NUMERIC} ,
		    NEXT_FLAG = #{next_flag, jdbcType=NUMERIC},
		    REINSURED = #{reinsured, jdbcType=VARCHAR} ,
		    CAN_CHANGE_FLAG = #{can_change_flag, jdbcType=NUMERIC} ,
		    CAN_REINSURE_FLAG = #{can_reinsure_flag, jdbcType=NUMERIC} ,
		    CHARGE_FLAG = #{charge_flag, jdbcType=NUMERIC} ,
			COND_PRE_CHECK_FLAG = #{cond_pre_check_flag, jdbcType=NUMERIC} ,
			MEET_POV_STANDARD_FLAG = #{meet_pov_standard_flag, jdbcType=NUMERIC} ,
			APPLICANT_SPE_PEOPLE = #{applicant_spe_people, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>
	<!-- 按索引查询操作 -->
	<select id="JRQD_findCsContractBusiProdByLogId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG,A.NEXT_FLAG,
			A.COND_PRE_CHECK_FLAG,null as ORDER_ID,A.MEET_POV_STANDARD_FLAG ,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1   ]]>
		<include refid="JRQD_queryCsContractBusiProdByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
<!-- 按条件查询单条数据操作 -->
	<select id="JRQD_findCsContractBusiProdById" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG,A.NEXT_FLAG,
			A.COND_PRE_CHECK_FLAG,null as ORDER_ID,A.MEET_POV_STANDARD_FLAG,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE ROWNUM <= 1   ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	<!-- 按map查询操作 -->	
	
	<select id="JRQD_findAllMapCsContractBusiProd" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG,A.NEXT_FLAG,
			A.COND_PRE_CHECK_FLAG,null as ORDER_ID,A.MEET_POV_STANDARD_FLAG ,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	<!-- 按map查询操作  add by zhangjh1_wb 20190614 -->	
	
	<select id="JRQD_findAllMapCsContractBusiProdForTry" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.PRODUCT_CODE_SYS,
			       B.PRODUCT_NAME_STD,
			       C.STD_PREM_AF,
			       C.TOTAL_PREM_AF,
			       C.AMOUNT,
			       A.IS_WAIVED,
                   A.POLICY_CODE
			  FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A
			 inner JOIN APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT C
			    ON A.POLICY_CHG_ID = C.POLICY_CHG_ID
			   AND A.POLICY_ID = C.POLICY_ID
			   AND C.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			   AND A.OLD_NEW = C.OLD_NEW
			 inner JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT B
			    ON B.BUSINESS_PRD_ID = A.BUSI_PRD_ID
			 WHERE A.OLD_NEW = '1'
    	]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />		
	</select>
	

	<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsContractBusiProd" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG,A.NEXT_FLAG,
			A.COND_PRE_CHECK_FLAG,null as ORDER_ID,A.MEET_POV_STANDARD_FLAG ,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
		<![CDATA[ ORDER BY  A.POLICY_CODE,A.BUSI_PROD_CODE,A.BUSI_ITEM_ID ]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="JRQD_queryAcceptInfoForLongNS" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		
          SELECT DISTINCT CBP.BUSI_PROD_CODE as BUSI_PROD_CODE,BP.PRODUCT_NAME_SYS as PRODUCT_NAME_SYS ,CBP.VALIDATE_DATE as VALIDATE_DATE
             FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE      CAC,
                  APP___PAS__DBUSER.T_CS_POLICY_CHANGE      CPC,
                  APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD CCBP,
                  APP___PAS__DBUSER.T_CS_PRECONT_PRODUCT CPP,
                  APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP,
                  APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
            WHERE CAC.ACCEPT_ID = CPC.ACCEPT_ID
              AND CPC.POLICY_CHG_ID = CCBP.POLICY_CHG_ID
              AND CCBP.POLICY_CHG_ID = CPP.POLICY_CHG_ID
              AND CBP.POLICY_CODE = CPC.POLICY_CODE
              AND CCBP.BUSI_ITEM_ID = CPP.BUSI_ITEM_ID
              AND CBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
              AND CCBP.OLD_NEW = '1'
              AND CPC.SERVICE_CODE = 'NS'
              AND CAC.ACCEPT_STATUS = '18'
              AND CPP.PRECONT_STATUS = '1'
              AND CBP.LIABILITY_STATE <> '3' 
              AND BP.COVER_PERIOD_TYPE = '0'
              AND CCBP.POLICY_CODE=#{policy_code}
           		]]>
	</select>
	<!-- 查询所有主险 -->
	<select id="JRQD_findAllCsContractBusiProdMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG,
			A.COND_PRE_CHECK_FLAG,null as ORDER_ID,A.MEET_POV_STANDARD_FLAG ,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
		<![CDATA[ AND A.MASTER_BUSI_ITEM_ID IS NULL ORDER BY A.MASTER_BUSI_ITEM_ID DESC]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="JRQD_findCsContractBusiProdTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
	</select>

	<!-- 分页查询操作 -->
	<select id="JRQD_queryCsContractBusiProdForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.REINSURED,B.CAN_CHANGE_FLAG,B.CAN_REINSURE_FLAG,B.CHARGE_FLAG,B.IS_RPU,B.ASSURERENEW_FLAG,B.OLD_POL_NO,B.HESITATION_PERIOD_DAY, B.GURNT_START_DATE, B.BUSI_PRD_ID, B.GURNT_PERIOD, B.BUSI_PROD_CODE, B.IS_WAIVED, B.HESITATION2ACC, 
			B.APPLY_CODE, B.OLD_NEW, B.CHANGE_ID, B.POLICY_ID, B.WAIVER, B.OPERATION_TYPE, 
			B.MASTER_BUSI_ITEM_ID, B.PAIDUP_DATE, B.EXPIRY_DATE, B.LIABILITY_STATE, B.POLICY_CODE, B.RERINSTATE_DATE, 
			B.RENEW_DECISION, B.VALIDATE_DATE, B.RENEWAL_STATE, B.APL_PERMIT, B.APPLY_DATE, 
			B.RENEW, B.RENEW_TIMES, B.WAIVER_END, B.MATURITY_DATE, B.GURNT_PERD_TYPE, B.POLICY_CHG_ID, 
			B.BUSI_ITEM_ID, B.LAPSE_DATE, B.JOINT_LIFE_FLAG, B.END_CAUSE, B.ISSUE_DATE, B.LAPSE_CAUSE, 
			B.DECISION_CODE, B.LOG_ID, B.SUSPEND_DATE, B.INITIAL_PREM_DATE, B.SUSPEND_CAUSE, B.DUE_LAPSE_DATE, B.WAIVER_START, 
			B.PRD_PKG_CODE, B.GURNT_RENEW_END, B.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,B.COND_PRE_CHECK_FLAG FROM (
					SELECT ROWNUM RN,A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY, A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG ,
			A.COND_PRE_CHECK_FLAG,null as ORDER_ID FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 更新生效日期 -->
	<update id="JRQD_updateBusiProdValidDate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD ]]>
		<set>
			<trim suffixOverrides=",">
				VALIDATE_DATE = #{validate_date,
				jdbcType=DATE} ,
				EXPIRY_DATE = #{expiry_date, jdbcType=DATE},
				PAIDUP_DATE = #{paidup_date, jdbcType=DATE},
				MATURITY_DATE = #{maturity_date, jdbcType=DATE},
				OPERATION_TYPE = #{operation_type,jdbcType=VARCHAR}
			</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<![CDATA[ AND　CHANGE_ID = #{change_id} ]]>
		<![CDATA[ AND POLICY_CHG_ID = #{policy_chg_id} ]]>
		<![CDATA[ AND OLD_NEW = #{old_new} ]]>
		<![CDATA[ AND BUSI_ITEM_ID = #{busi_item_id} ]]>
	</update>

	<!-- 查询产品的基本信息 -->
	<select id="JRQD_queryPagecfgUnitByBusinessPrdId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT * FROM APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE PEV WHERE PEV.LIST_VALUE_CODE=(SELECT U.LIST_VALUE FROM APP___PAS__DBUSER.T_PAGECFG_UNIT U
   WHERE 1=1 ]]>
		<include refid="JRQD_queryBusinessProductByUnitNameIdCondition" />
   <![CDATA[ AND U.UNIT_CODE IN (SELECT EE.UNIT_CODE FROM APP___PAS__DBUSER.T_PAGECFG_ELEMENT EE WHERE EE.SQUARE_ID =
                 (SELECT EC.ENTRY_CATEGORY_ID FROM APP___PAS__DBUSER.T_PAGECFG_ENTRY_CATEGORY EC WHERE EC.ENTRY_CATEGORY_NAME =
                         (SELECT BP.PRODUCT_CODE_STD FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP WHERE 1 = 1 ]]>
		<include refid="JRQD_queryBusinessProductByBusinessPrdIdCondition" />
		<![CDATA[))))]]>
	</select> 
	
		<!-- 查询个数操作 -->
	<select id="JRQD_findBusiProdNextCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ select count (1) countNumber from (
					select tccbp.busi_item_id,
					       tt.service_type,
					       tt.service_code,
					       tt.policy_code,
					       tt.agent_code,
					       tt.accept_status,
					       tt.accept_time
					  from (select tca.customer_id,
					               tca.change_id,
					               tcc.accept_status,
					               tcp.policy_chg_id,
					               tcp.policy_code,
					               tcc.accept_time,
					               tca.agent_code,
					               tca.service_type,
					               tcc.service_code
					          from APP___PAS__DBUSER.T_CS_APPLICATION   tca,
					               APP___PAS__DBUSER.T_CS_POLICY_CHANGE tcp,
					               APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE tcc
					         where tca.change_id = tcp.change_id
					           and tca.change_id = tcc.change_id
					           and tca.customer_id =  #{customer_id}) tt,
					       APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD tccbp
					 where tt.policy_chg_id = tccbp.policy_chg_id
					   and tt.change_id = tccbp.change_id
					   and tccbp.old_new = 1) ]]>
	</select>
	
		<!-- 分页查询操作 -->
	<select id="JRQD_querybusiProdPO" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		   select (select nvl(m.product_name_sys, '')
                  from APP___PAS__DBUSER.t_business_product m
                 where m.business_prd_id = ro.busi_prd_id) busi_prd_name,
               ro.busi_prod_code,
               ro.busi_prd_id,
               ro.service_type,
               (select n.type_name
                  from APP___PAS__DBUSER.t_service_type n
                 where n.service_type = ro.service_type) type_name,
               ro.service_code,
               (select y.service_name
                  from APP___PAS__DBUSER.t_service y
                 where y.service_code = ro.service_code) service_name,
               ro.policy_code,
               ro.agent_code,
               ro.accept_status,
               (select q.status_desc
                  from APP___PAS__DBUSER.t_accept_status q
                 where q.accept_status = ro.accept_status) status_desc,
               ro.agent_name,
               ro.accept_code,
               ro.accept_time,
               case when ro.try_calc_no is null then '1'
                    else '0' end as try_calc_no
          from (select rownum roeno,
                       tto.busi_prd_id,
                       tto.busi_prod_code,
                       tto.service_type,
                       tto.service_code,
                       tto.policy_code,
                       tto.agent_code,
                       tto.accept_status,
                       tto.agent_name,
                       tto.accept_code,
                       tto.accept_time,
                       tto.try_calc_no
                  from (select tccbp.busi_prd_id,
                               tccbp.busi_prod_code,
                               tt.service_type,
                               tt.service_code,
                               tt.policy_code,
                               tt.agent_code,
                               tt.accept_status,
                               tt.agent_name,
                               tt.accept_code,
                               tt.accept_time,
                               tt.try_calc_no
                          from (select tca.customer_id,
                                       tca.change_id,
                                       tcc.accept_status,
                                       tcp.policy_chg_id,
                                       tcp.policy_code,
                                       tcc.accept_time,
                                       tca.agent_code ,
                                       tca.service_type,
                                       tcc.service_code,
                                       tcc.accept_code,
                                       tca.agent_name,
                                       tca.TRY_CALC_NO,
                                       tcp.policy_id
                                  from APP___PAS__DBUSER.T_CS_APPLICATION   tca,
                                       APP___PAS__DBUSER.T_CS_POLICY_CHANGE tcp,
                                       APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE tcc
                                 where tca.change_id = tcp.change_id
                                   and tca.change_id = tcc.change_id
                                   and tca.customer_id = #{customer_id}) tt,
                               APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD tccbp
                         where tt.policy_id = tccbp.policy_id
                         order by tt.accept_time desc) tto
                 where rownum <= #{endNumber}) ro
         where ro.roeno >= #{begainNumber}
 		]]>
	</select>
	
			<!-- 查询个数操作 -->
	<select id="JRQD_findBusiProdNextCounto" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ select count (1) countNumber from (
					select tccbp.busi_item_id,
					       tt.service_type,
					       tt.service_code,
					       tt.policy_code,
					       tt.agent_code,
					       tt.accept_status,
					       tt.accept_time
					  from (select tca.customer_id,
					               tca.change_id,
					               tcc.accept_status,
					               tcp.policy_chg_id,
					               tcp.policy_code,
					               tcc.accept_time,
					               tca.agent_code,
					               tca.service_type,
					               tcc.service_code
					          from APP___PAS__DBUSER.T_CS_APPLICATION   tca,
					               APP___PAS__DBUSER.T_CS_POLICY_CHANGE tcp,
					               APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE tcc
					         where tca.change_id = tcp.change_id
					           and tca.change_id = tcc.change_id
					           and tca.customer_id =  #{customer_id} 
					           and tca.service_type= #{service_type}   					             
					        ) tt,
					       APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD tccbp
					 where tt.policy_chg_id = tccbp.policy_chg_id
					   and tt.change_id = tccbp.change_id
					   and tccbp.old_new = 1) ]]>
	</select>
	
		<!-- 分页查询操作 -->
	<select id="JRQD_querybusiProdPOO" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[            select ro.busi_prd_id,
			                (select nvl(m.product_name_sys, '')
			                   from APP___PAS__DBUSER.t_business_product m
			                  where m.business_prd_id = ro.busi_prd_id) busi_prd_name,
			                ro.service_type,
			                (select n.type_name
			                   from APP___PAS__DBUSER.t_service_type n
			                  where n.service_type = ro.service_type) type_name,
			                ro.service_code,
			                (select y.service_name
			                   from APP___PAS__DBUSER.t_service y
			                  where y.service_code = ro.service_code) service_name,
			                ro.policy_code,
			                ro.agent_code,
			                ro.accept_status,
			                (select q.status_desc
			                   from APP___PAS__DBUSER.t_accept_status q
			                  where q.accept_status = ro.accept_status) status_desc,
			                ro.accept_time
			           from (select rownum roeno,
			                        tto.busi_prd_id,
			                        tto.service_type,
			                        tto.service_code,
			                        tto.policy_code,
			                        tto.agent_code,
			                        tto.accept_status,
			                        tto.accept_time
			                   from (select tccbp.busi_prd_id,
			                                tt.service_type,
			                                tt.service_code,
			                                tt.policy_code,
			                                tt.agent_code,
			                                tt.accept_status,
			                                tt.accept_time
			                           from (select tca.customer_id,
			                                        tca.change_id,
			                                        tcc.accept_status,
			                                        tcp.policy_chg_id,
			                                        tcp.policy_code,
			                                        tcc.accept_time,
			                                        tca.agent_code,
			                                        tca.service_type,
			                                        tcc.service_code
			                                   from APP___PAS__DBUSER.T_CS_APPLICATION   tca,
			                                        APP___PAS__DBUSER.T_CS_POLICY_CHANGE tcp,
			                                        APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE tcc
			                                  where tca.change_id = tcp.change_id
			                                    and tca.change_id = tcc.change_id
			                                    and tca.customer_id = #{customer_id}
			                                    and tca.service_type = #{service_type}) tt,
			                                APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD tccbp
			                          where tt.policy_chg_id = tccbp.policy_chg_id
			                            and tt.change_id = tccbp.change_id
			                            and tccbp.old_new = 1
			                          order by tt.accept_time desc) tto
			                  where rownum <= #{endNumber}) ro
			          where ro.roeno >= #{begainNumber} ]]>

	</select>
	
				<!-- 查询个数操作 -->
	<select id="JRQD_findBusiProdNextCountot" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[         select count(1) countNumber from (select t1.apply_time,
                                t1.agent_code,
                                t1.agent_name,
                                t1.service_code,
                                t1.policy_code,
                                t1.policy_chg_id,
                                tcco.busi_prd_id
                           from APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD tcco
                          inner join (select tcap.apply_time,
                                            ta.agent_code,
                                            ta.agent_name,
                                            tcap.change_id,
                                            tcas.service_code,
                                            tcpo.policy_code,
                                            tcpo.policy_chg_id
                                       from APP___PAS__DBUSER.T_AGENT ta
                                      inner join APP___PAS__DBUSER.T_CS_APPLICATION tcap
                                         on ta.agent_code = tcap.agent_code
                                      inner join APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE tcas
                                         on tcap.change_id = tcas.change_id
                                      inner join APP___PAS__DBUSER.T_CS_POLICY_CHANGE tcpo
                                         on tcpo.accept_id = tcas.accept_id
                                      where ta.agent_code = #{agent_code} ) t1
                             on t1.policy_chg_id = tcco.policy_chg_id
                          where tcco.old_new = '1')]]>
	</select>
	
		<!-- 分页查询操作 -->
	<select id="JRQD_querybusiProdPOOT" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[                    
		          select ro.apply_time,
		                 ro.agent_code,
		                 ro.agent_name,
		                 ro.service_code,
		                 (select y.service_name
		                    from APP___PAS__DBUSER.t_service y
		                   where y.service_code = ro.service_code) service_name,
		                 ro.policy_code,
		                 ro.policy_chg_id,
		                 ro.agent_code,
		                 ro.accept_status,
		                 (select q.status_desc
		                    from APP___PAS__DBUSER.t_accept_status q
		                   where q.accept_status = ro.accept_status) status_desc,
		                 ro.busi_prd_id
		            from (select rownum roeno,
		                         tto.apply_time,
		                         tto.agent_code,
		                         tto.agent_name,
		                         tto.service_code,
		                         tto.policy_code,
		                         tto.policy_chg_id,
		                         tto.agent_code,
		                         tto.accept_status,
		                         tto.busi_prd_id
		                    from (select t1.apply_time,
		                                 t1.agent_code,
		                                 t1.agent_name,
		                                 t1.service_code,
		                                 t1.policy_code,
		                                 t1.policy_chg_id,
		                                 t1.agent_code,
		                                 t1.accept_status,
		                                 tcco.busi_prd_id
		                            from APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD tcco
		                           inner join (select tcap.apply_time,
		                                             ta.agent_code,
		                                             tcap.agent_code,
		                                             ta.agent_name,
		                                             tcap.change_id,
		                                             tcas.service_code,
		                                             tcpo.policy_code,
		                                             tcas.accept_status,
		                                             tcpo.policy_chg_id
		                                        from APP___PAS__DBUSER.T_AGENT ta
		                                       inner join APP___PAS__DBUSER.T_CS_APPLICATION tcap
		                                          on ta.agent_code = tcap.agent_code
		                                       inner join APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE tcas
		                                          on tcap.change_id = tcas.change_id
		                                       inner join APP___PAS__DBUSER.T_CS_POLICY_CHANGE tcpo
		                                          on tcpo.accept_id = tcas.accept_id
		                                       where ta.agent_code = #{agent_code}) t1
		                              on t1.policy_chg_id = tcco.policy_chg_id
		                           where tcco.old_new = '1'
		                           order by t1.apply_time desc) tto
		                   where rownum <= #{endNumber}) ro
		           where ro.roeno >= #{begainNumber}
  	            ]]>

	</select>

	
			<!--解约清单接口查询 -->
	<select id="JRQD_findRescissionList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT
					C.VALIDATE_TIME RESCISSION_DATE, A.POLICY_CODE, F.CUSTOMER_NAME, F.OFFEN_USE_TEL, A.BUSI_PROD_CODE, G.PRODUCT_NAME_SYS
					FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A
					LEFT OUTER JOIN APP___PAS__DBUSER.T_CS_APPLICATION B ON A.CHANGE_ID=B.CHANGE_ID 
					LEFT OUTER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE C ON A.Change_Id=C.Change_Id AND A.Policy_Chg_Id=C.Policy_Chg_Id
					LEFT OUTER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE D ON C.Change_Id=D.Change_Id AND C.Accept_Id=D.Accept_Id
					LEFT OUTER JOIN APP___PAS__DBUSER.T_CS_POLICY_HOLDER E ON A.Policy_Chg_Id=E.Policy_Chg_Id AND E.OLD_NEW='1'
					LEFT OUTER JOIN APP___PAS__DBUSER.T_CUSTOMER F ON E.CUSTOMER_ID=F.CUSTOMER_ID
					LEFT OUTER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT G ON A.BUSI_PRD_ID=G.BUSINESS_PRD_ID
					WHERE A.OLD_NEW='1'
					AND D.SERVICE_CODE='EA' 
					AND B.AGENT_CODE= #{agentCode}
					AND to_char(C.VALIDATE_TIME,'yyyy-mm-dd') >= #{startTime}
					AND to_char(C.VALIDATE_TIME,'yyyy-mm-dd') <= #{endTime}
		            ]]>
	</select>
	
	

	
	<!-- 查询抄单保单险种列表 -->
	<select id="JRQD_findCsContractBusiProds" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				 select cp.policy_code,cp.busi_prod_code as busi_prod_code ,  cp.liability_state,
                       nvl2(cp.master_busi_item_id, '1', '0') as master_flag,
                       cp.master_busi_item_id,cp.busi_prd_id,
                       nvl2(cp.master_busi_item_id, (select busi_prod_code from APP___PAS__DBUSER.t_contract_busi_prod cbp where cp.master_busi_item_id = cbp.busi_item_id  and cp.policy_id = cbp.policy_id), null) as master_busi_prod_code,
	                 (select brp.product_category1
	                    from APP___PAS__DBUSER.t_business_product brp
	                   where brp.business_prd_id = cp.busi_prd_id) as product_category1,
	                 (select brp.product_category2
	                    from APP___PAS__DBUSER.t_business_product brp
	                   where brp.business_prd_id = cp.busi_prd_id) as product_category2,
	                 (select brp.product_category3
	                    from APP___PAS__DBUSER.t_business_product brp
	                   where brp.business_prd_id = cp.busi_prd_id) as product_category3,
	                 (select brp.product_category4
	                    from APP___PAS__DBUSER.t_business_product brp
	                   where brp.business_prd_id = cp.busi_prd_id) as product_category4,
	                 (select brp.cover_period_type
	                    from APP___PAS__DBUSER.t_business_product brp
	                   where brp.business_prd_id = cp.busi_prd_id) as cover_period_type,
	                 (select sum(total_prem_af)
	                    from APP___PAS__DBUSER.t_cs_contract_product ccp
	                   where ccp.busi_item_id = cp.busi_item_id and ccp.policy_chg_id = cp.policy_chg_id and ccp.change_id = cp.change_id and ccp.old_new = cp.old_new) as total_prem_af,
	                   (select sum(std_prem_af)
	                    from APP___PAS__DBUSER.t_cs_contract_product ccp
	                   where ccp.busi_item_id = cp.busi_item_id and ccp.policy_chg_id = cp.policy_chg_id and ccp.change_id = cp.change_id and ccp.old_new = cp.old_new) as std_prem_af,
	                 (select max(ccp.amount)
	                    from APP___PAS__DBUSER.t_cs_contract_product ccp
	                   where ccp.busi_item_id = cp.busi_item_id  and ccp.policy_chg_id = cp.policy_chg_id and ccp.change_id = cp.change_id and ccp.old_new = cp.old_new) as amount,
	                 cp.validate_date,
	                 cp.expiry_date,
	                 cp.renew_times,
	                 (select insured_age from APP___PAS__DBUSER.t_cs_insured_list cil where cil.old_new = cp.old_new and cil.change_id = cp.change_id and cil.policy_chg_id = cp.policy_chg_id and rownum = 1) as insured_age,
	                        nvl2(cp.master_busi_item_id, (select sum(amount) from APP___PAS__DBUSER.t_cs_contract_product ccp
	                           where ccp.busi_item_id = cp.busi_item_id  and ccp.policy_chg_id = cp.policy_chg_id and ccp.change_id = cp.change_id and ccp.old_new = cp.old_new), 0) as add_sumamount,
                 	cp.busi_item_id
          	from APP___PAS__DBUSER.t_cs_contract_busi_prod cp where 1=1 
		 ]]>
		 <if test="old_new != null and old_new != ''"><![CDATA[ and cp.old_new = #{old_new}   ]]></if>
		 <if test="change_id != null"><![CDATA[ and cp.change_id = #{change_id}   ]]></if>
		 <if test="policy_chg_id != null"><![CDATA[ and cp.policy_chg_id = #{policy_chg_id}   ]]></if>		
	</select>
	
	<!-- 查询保单险种列表 -->
	<select id="JRQD_findContractBusiProds" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
				select 		cp.busi_prod_code as busi_prod_code , cp.liability_state,
				            cp.master_busi_item_id,
           					nvl2(cp.master_busi_item_id, '1', '0') as master_flag,
       					    nvl2(cp.master_busi_item_id, (select busi_prod_code from APP___PAS__DBUSER.t_contract_busi_prod cbp where cp.master_busi_item_id = cbp.busi_item_id  and cp.policy_id = cbp.policy_id), null) as master_busi_prod_code,
					       brp.product_category1 as product_category1,
					       brp.product_category2 as product_category2,
					       brp.product_category3 as product_category3,
					       brp.product_category4 as product_category4,
					       brp.cover_period_type as cover_period_type,
					       ccp.total_prem_af as total_prem_af,
					       ccp.amount as amount,
					       cp.validate_date,
					       cp.expiry_date,
					       (select insured_age from APP___PAS__DBUSER.t_insured_list cil,APP___PAS__DBUSER.T_BENEFIT_INSURED bi where cil.policy_id = cp.policy_id and cil.list_id = bi.insured_id
                 				and bi.busi_item_id = cp.busi_item_id and bi.policy_id = cp.policy_id and rownum=1) as insured_age,
                 		   nvl2(cp.master_busi_item_id, ccp.amount, 0) as add_sumamount,
					       cp.busi_item_id
  				from APP___PAS__DBUSER.t_contract_busi_prod cp
  				left join APP___PAS__DBUSER.t_business_product brp ON brp.business_prd_id = cp.busi_prd_id
  				left join (select policy_id, BUSI_ITEM_ID, sum(total_prem_af) AS total_prem_af, sum(amount) AS amount
  					from APP___PAS__DBUSER.t_cs_contract_product ccp where 1=1]]> 
  					<if test="policy_id != null"><![CDATA[ and ccp.policy_id = #{policy_id}   ]]></if>
		 			<if test="busi_item_id != null"><![CDATA[ and ccp.busi_item_id = #{busi_item_id}   ]]></if>
			<![CDATA[ group by policy_id , BUSI_ITEM_ID) ccp 
					  ON ccp.policy_id = cp.policy_id
					  AND ccp.busi_item_id = cp.busi_item_id
					 where 1 = 1
		 ]]>
		 <if test="policy_id != null"><![CDATA[ and cp.policy_id = #{policy_id}   ]]></if>
		 <if test="busi_item_id != null"><![CDATA[ and cp.busi_item_id = #{busi_item_id}   ]]></if>
	</select>
	
	<!-- huangcc 查询险种缴费方式的属性 --> 
	<select id = "findBusinCoverageTypeAttr" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT T.PRD_ELEMENT_ID,T.READ_ONLY AS is_readonly, T.DEFAULT_VALUE , T.HIDDEN, T.DEFAULT_VALUE_ORIGIN
  				FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT T
 				WHERE 1 = 1
   				AND T.RELATION_ID = #{relation_id}
   				AND T.PRD_ELEMENT_ID = #{prd_element_id}
		]]>
		</select>
		
	<!-- 查询缴费方式的默认值 -->
	<select id = "findBusinPayMentAttr" resultType="java.util.Map" 
		parameterType="java.util.Map">
	<![CDATA[
			  SELECT A.PRD_ELEMENT_ID, A.PRD_ELEMENT_VALUE
   				 FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION A
   					WHERE A.PRD_ELEMENT_ID = #{prd_element_id}
    				 AND A.RELATION_ID = #{relation_id}
    				 AND A.TARGET_PRD_ELEMENT_LIST_VALUE = #{list_value_code}
    				 AND A.ACTION_TYPE = '1'
		]]>
	</select>
	
	<select id="JRQD_CS_findPayYearInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.CODE,A.CODE2
			  FROM DEV_PAS.T_PAGECFG_ELEMENT_VALUE A
			 WHERE 1 = 1
			   AND A.LIST_VALUE_CODE IN
			       (SELECT CASE WHEN G.DEFAULT_VALUE_ORIGIN = 1 THEN G.LIST_VALUE
			               ELSE NULL END AS LIST_VALUE
			          FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT G,
			               APP___PAS__DBUSER.T_PAGECFG_UNIT        U
			         WHERE G.UNIT_CODE = U.UNIT_CODE
			           AND G.RELATION_ID IN
			               (SELECT RELATION_ID
			                  FROM APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA C
			                 WHERE C.BUSI_PRD_ID =
			                       (SELECT BUSINESS_PRD_ID
			                          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
			                         WHERE TBP.BUSINESS_PRD_ID = #{busi_prd_id}))
			           AND U.LABEL_NAME = '领取年龄'
			           AND G.LIST_VALUE IS NOT NULL)
		]]>
	</select>
	<!-- zhaojy 查询险种交费方式 -->
	<select id="JRQD_findBusinCoverageType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		
		  SELECT V.CODE, V.CODE2, A.PRD_ELEMENT_ID, A.RELATION_ID
    	FROM APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE V
    		RIGHT JOIN (SELECT PRE.PRD_ELEMENT_ID, PRE.RELATION_ID,PRE.LIST_VALUE
                 FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT PRE
                WHERE EXISTS (SELECT 1
                         FROM APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA CA
                        WHERE 1=1 ]]>
                         <if test="relation_id != null"><![CDATA[ AND CA.RELATION_ID = #{relation_id} ]]></if> 
                         <if test="product_id != null"><![CDATA[ AND CA.PRODUCT_ID = #{product_id} ]]></if> 
                         <if test="busi_prd_id != null"><![CDATA[ AND CA.BUSI_PRD_ID = #{busi_prd_id} ]]></if> 
                          <![CDATA[
                          AND PRE.RELATION_ID = CA.RELATION_ID)
		           and exists (select 'X' from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT tppe where tppe.unit_code = 021 and pre.unit_code = tppe.unit_code and pre.relation_id = tppe.relation_id and tppe.list_value = PRE.LIST_VALUE) 
                  AND EXISTS
                (SELECT 1
                         FROM APP___PAS__DBUSER.T_PAGECFG_UNIT PU
                        WHERE PRE.UNIT_CODE = PU.UNIT_CODE)) A
      		ON A.LIST_VALUE = V.LIST_VALUE_CODE
  		 ORDER BY DISPLAY_ORDER ASC
		]]>
	</select>
	
	<!-- 朱丽华 查询缴费期间 024期间-->
	<select id="JRQD_findListById" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 SELECT  V.CODE, V.CODE2
  		FROM APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE V
		 WHERE EXISTS
		       (SELECT  1
		          FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT PRE
		         WHERE EXISTS (SELECT 1
		                  FROM APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA CA
		                 WHERE CA.PRODUCT_ID  = #{product_id} 
		                   AND CA.BUSI_PRD_ID = #{busi_prd_id}
		                   AND PRE.RELATION_ID = CA.RELATION_ID)
		           and exists (select 'X' from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT tppe where tppe.unit_code = 024 and pre.relation_id = tppe.relation_id and tppe.list_value = PRE.LIST_VALUE) 
		           AND EXISTS (SELECT 1
		                  FROM APP___PAS__DBUSER.T_PAGECFG_UNIT PU
		                 WHERE PRE.UNIT_CODE = PU.UNIT_CODE) and PRE.LIST_VALUE =  V.LIST_VALUE_CODE)
		 ORDER BY DISPLAY_ORDER ASC
		]]>
	</select>
	
	<!-- zhaojy 查询缴费期间 024 modify by lixf 重写！20160927 -->
	<select id="JRQD_findBusinCoveragePeriod" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 
select a.code, a.code2, a.list_value_code, c.read_only IS_READONLY
  from APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE      a,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION b,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT        c,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION bb
 where a.list_value_code = bb.target_prd_element_list_value
   and b.relation_id = #{relation_id}
   and b.prd_element_id = #{prd_element_id}
   and b.action_type = '1'
   and b.prd_element_value = #{selectedValue}
   and b.target_prd_element_id = c.prd_element_id
   and c.unit_code = 024
   and b.prd_element_id=bb.prd_element_id
   and bb.relation_id=b.relation_id and bb.action_type='4' and bb.target_prd_element_id=b.target_prd_element_id
   and bb.target_prd_element_value is null and bb.target_prd_element_list_value is not null
   and bb.prd_element_value=b.prd_element_value
union 
select a.code, a.code2, a.list_value_code, c.read_only IS_READONLY
  from APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE      a,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION b,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT        c,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION bb
 where a.list_value_code = b.target_prd_element_list_value
   and b.relation_id = #{relation_id}
   and b.prd_element_id = #{prd_element_id}
   and b.action_type = '1'
   and b.prd_element_value = #{selectedValue}
   and b.target_prd_element_id = c.prd_element_id
   and c.unit_code = 024
   and b.prd_element_id=bb.prd_element_id
   and bb.relation_id=b.relation_id and bb.action_type='4' and bb.target_prd_element_id=b.target_prd_element_id
   and bb.target_prd_element_value is not null and bb.target_prd_element_list_value is null
   and bb.prd_element_value=b.prd_element_value
   and a.code=bb.target_prd_element_value
   
union
  
select a.code, a.code2, a.list_value_code, c.read_only IS_READONLY
  from APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE      a,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION b,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT        c,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION bb
 where a.list_value_code = bb.target_prd_element_list_value
   and b.relation_id = #{relation_id}
   and b.prd_element_id = #{prd_element_id}
   and b.action_type = '1'
   and b.prd_element_value = #{selectedValue}
   and b.target_prd_element_id = c.prd_element_id
   and c.unit_code = 024
   and b.prd_element_id=bb.prd_element_id
   and bb.relation_id=b.relation_id and bb.action_type='4' and bb.target_prd_element_id=b.target_prd_element_id
   and bb.target_prd_element_list_value is not null and bb.target_prd_element_value is not null
   and bb.prd_element_value=b.prd_element_value
   and a.code=bb.target_prd_element_value   
   
union

select a.code, a.code2, a.list_value_code, c.read_only IS_READONLY
  from APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE      a,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION b,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT        c
 where a.list_value_code = b.target_prd_element_list_value
   and b.relation_id = #{relation_id}
   and b.prd_element_id = #{prd_element_id}
   and b.action_type = '1'
   and b.prd_element_value = #{selectedValue}
   and b.target_prd_element_id = c.prd_element_id
   and c.unit_code = 024
   and not exists (select 1 from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION bb
   where b.prd_element_id=bb.prd_element_id
   and bb.relation_id=b.relation_id and bb.action_type='4' and bb.target_prd_element_id=b.target_prd_element_id
   and bb.prd_element_value=b.prd_element_value 
   and (bb.target_prd_element_value is not null or bb.target_prd_element_list_value is not null))

union
			
select a.code, a.code2, a.list_value_code, 
c.read_only IS_READONLY
  from APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE      a,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION b,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT        c
 where a.list_value_code = c.list_value
   and b.relation_id = #{relation_id}
   and a.code = b.prd_element_value
   and b.prd_element_id = #{prd_element_id}
   and b.action_type = '4'
   and b.prd_element_value = #{selectedValue}
   and b.target_prd_element_id = c.prd_element_id
   and c.unit_code =024 
union  
select a.code, a.code2, a.list_value_code, c.read_only IS_READONLY 
  from APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE      a,
       APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT        c
 where a.list_value_code = c.list_value
   and c.relation_id = #{relation_id}
   and c.unit_code = 024
   and c.default_value=a.code
   and not exists (select 1 from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION b
   where b.prd_element_id=c.prd_element_id 
   and b.relation_id=c.relation_id
   )
   and exists (select 1 from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT cc where cc.prd_element_id= #{prd_element_id} 
   and cc.relation_id=c.relation_id and cc.unit_code=021 and cc.default_value =#{selectedValue})   
		]]>
		
	</select>
	<!--by zhaoyoan_wb 通过保单号和险种号查询险种代码和险种id -->
	<select id="JRQD_findByPolicyCodeBusiPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select * from APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD where 1=1
			 and BUSI_PRD_ID=#{busi_prd_id} 
			 and POLICY_CODE=#{policy_code} 
			 and rownum=1
		]]>
	</select>
	
	<select id="JRQD_findIsOrNotMasterBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select distinct a.change_id,
                a.policy_chg_id,
                a.policy_code,
                a.busi_prod_code,
                a.master_busi_item_id
  from APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD a
 where a.change_id = #{change_id}
   and a.policy_chg_id = #{policy_chg_id}
   and a.policy_code = #{policy_code}
   and a.busi_prod_code =#{busi_prod_code}
		]]>
	</select>
	<select id="JRQD_findContractBusiProdForYuanBiao" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSI_PRD_ID, A.BUSI_PROD_CODE, A.BUSI_ITEM_ID, A.POLICY_ID,  A.POLICY_CODE FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A WHERE A.POLICY_ID=#{policy_id} AND A.BUSI_ITEM_ID =#{busi_item_id} ]]>
	</select>
	
	<select id="JRQD_CS_findInsurAgeToApplyTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT MONTHS_BETWEEN(TA.APPLY_TIME,D.CUSTOMER_BIRTHDAY)/12 AS AGE
			  FROM APP___PAS__DBUSER.T_CS_APPLICATION TA
			 INNER JOIN APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A
			    ON TA.CHANGE_ID = A.CHANGE_ID
			 INNER JOIN APP___PAS__DBUSER.T_CS_BENEFIT_INSURED B
			    ON A.POLICY_CHG_ID = B.POLICY_CHG_ID
			   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			   AND A.OLD_NEW = B.OLD_NEW
			   AND B.ORDER_ID = 1
			 INNER JOIN APP___PAS__DBUSER.T_CS_INSURED_LIST C
			    ON B.INSURED_ID = C.LIST_ID
			   AND B.POLICY_CHG_ID = C.POLICY_CHG_ID
			   AND B.OLD_NEW = C.OLD_NEW
			 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER D
			    ON C.CUSTOMER_ID = D.CUSTOMER_ID
			 WHERE 1 = 1
			   AND A.POLICY_CHG_ID = #{policy_chg_id}
			   AND A.BUSI_ITEM_ID = #{busi_item_id}
			   AND A.OLD_NEW = '0'
		 ]]>
	</select>
	
<!-- 按条件查询单条数据操作 -->
	<select id="JRQD_findCsContractBusiProdForRenew" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT bs.RENEWAL_MAX_AGE ,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
      A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
      A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
      A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG ,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A ,dev_pds.T_BUSINESS_PRODUCT bs  WHERE ROWNUM <= 1000 and a.busi_prod_code = bs.product_code_sys   ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>	
<!-- 根据保单号查询险种  -->	
	<select id="JRQD_findBusiPrdId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			select a.busi_prd_id from dev_pas.t_contract_busi_prod a where 1 = 1
		]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
	</select>
	
	<!-- 查询险种名称  -->	
	<select id="JRQD_getProductName" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 SELECT A.PRODUCT_NAME_STD from APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE A.BUSINESS_PRD_ID = #{business_prd_id}
		]]>
	</select>	
	<!-- 获取保单满期日 -->
	<select id="JRQD_getMaturityDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 select max(A.maturity_date) as  maturity_date from APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A where A.policy_code=#{policy_code} AND A.master_busi_item_id is null
		]]>
	</select>
	<!-- 查询非终止状态的险种 -->
	<select id="JRQD_findAllNotEndBusiProd" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			 select policy_chg_id,old_new,liability_state from dev_pas.t_cs_contract_busi_prod t 
			  where t.policy_chg_id = #{policy_chg_id} and t.old_new = #{old_new}
			    and t.liability_state <> 3
		]]>
	</select>
	
	<!-- 查询保全险种信息 -->
	<select id="JRQD_CS_queryCsBusiProdInf" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT  T1.ENDORSE_CODE,T3.SERVICE_CODE,T1.POLICY_CODE,T2.BUSI_ITEM_ID,T4.APPLY_CODE,T4.POLICY_TYPE,
				(SELECT A.BUSI_ITEM_ID FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD A WHERE A.POLICY_ID=T1.POLICY_ID AND A.MASTER_BUSI_ITEM_ID IS  NULL AND ROWNUM=1)MASTER_BUSI_ITEM_ID,
				T2.BUSI_PROD_CODE,T4.ORGAN_CODE,T4.CHANNEL_TYPE,T5.CUSTOMER_ID AS INSURED_ID,
				(SELECT CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER WHERE CUSTOMER_ID=T5.CUSTOMER_ID )INSURED_NAME,
				(SELECT CUSTOMER_GENDER FROM DEV_PAS.T_CUSTOMER WHERE CUSTOMER_ID=T5.CUSTOMER_ID )INSURED_GENDER,
				(SELECT CUSTOMER_BIRTHDAY FROM DEV_PAS.T_CUSTOMER WHERE CUSTOMER_ID=T5.CUSTOMER_ID )INSURED_BIRTHDAY,
				T5.INSURED_AGE,T5.JOB_UNDERWRITE,T6.CUSTOMER_ID AS HOLDER_ID,
				(SELECT CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER WHERE CUSTOMER_ID=T6.CUSTOMER_ID )HOLDER_NAME,
				T2.VALIDATE_DATE,T2.ISSUE_DATE,
				(SELECT PAY_DUE_DATE FROM DEV_PAS.T_CS_CONTRACT_EXTEND A WHERE A.CHANGE_ID=T1.CHANGE_ID AND A.POLICY_CHG_ID=T1.POLICY_CHG_ID AND A.OLD_NEW=T2.OLD_NEW AND ROWNUM=1)PAY_DUE_DATE,
				T2.EXPIRY_DATE,T7.CHARGE_PERIOD,T7.CHARGE_YEAR,T7.COVERAGE_PERIOD,T7.COVERAGE_YEAR,T7.PREM_FREQ,T7.UNIT,
				T7.STD_PREM_AF,T7.AMOUNT,T4.LIABILITY_STATE,
				(SELECT T.COVER_PERIOD_TYPE FROM DEV_PAS.T_BUSINESS_PRODUCT T WHERE T.BUSINESS_PRD_ID=T2.BUSI_PRD_ID )COVER_PERIOD_TYPE,
				(SELECT ACKNOWLEDGE_DATE FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT WHERE POLICY_ID=T1.POLICY_ID )ACKNOWLEDGE_DATE,
				T2.INSERT_TIME,T2.UPDATE_TIME,T2.RENEW_TIMES,
				(SELECT A.STATUS FROM DEV_PAS.T_DOCUMENT A WHERE A.POLICY_ID=T2.POLICY_ID AND ROWNUM=1)STATUS
				FROM DEV_PAS.T_CS_POLICY_CHANGE T1,DEV_PAS.T_CS_CONTRACT_BUSI_PROD T2,DEV_PAS.T_CS_ACCEPT_CHANGE T3,
				DEV_PAS.T_CS_CONTRACT_MASTER T4,DEV_PAS.T_CS_INSURED_LIST T5,DEV_PAS.T_CS_POLICY_HOLDER T6,
				DEV_PAS.T_CS_CONTRACT_PRODUCT T7
				WHERE T1.CHANGE_ID=T2.CHANGE_ID AND T1.POLICY_CHG_ID=T2.POLICY_CHG_ID 
				AND T1.POLICY_ID=T2.POLICY_ID AND T3.ACCEPT_ID=T1.ACCEPT_ID AND T3.CHANGE_ID=T1.CHANGE_ID 
				AND T1.POLICY_ID=T4.POLICY_ID AND T4.CHANGE_ID=T1.CHANGE_ID AND T4.POLICY_CHG_ID=T1.POLICY_CHG_ID  AND  T4.OLD_NEW=T2.OLD_NEW
				AND T5.POLICY_ID=T1.POLICY_ID AND T5.CHANGE_ID=T1.CHANGE_ID AND T5.POLICY_CHG_ID=T1.POLICY_CHG_ID  AND  T5.OLD_NEW=T2.OLD_NEW
				AND T6.POLICY_ID=T1.POLICY_ID AND T6.CHANGE_ID=T1.CHANGE_ID AND T6.POLICY_CHG_ID=T1.POLICY_CHG_ID  AND  T6.OLD_NEW=T2.OLD_NEW
				AND T7.POLICY_ID=T1.POLICY_ID AND T7.CHANGE_ID=T1.CHANGE_ID AND T7.POLICY_CHG_ID=T1.POLICY_CHG_ID  AND  T7.OLD_NEW=T2.OLD_NEW
				AND T2.POLICY_CODE=#{policy_code} AND nvl(T2.old_pol_no,T2.Busi_Item_Id) = #{polno}
				AND T2.old_new =1  
		]]>
	</select>
	<select id="JRQD_cs_findProductChargeYearForFM" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.CODE,A.CODE2
			  FROM APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE A
			 WHERE 1 = 1
			   AND A.LIST_VALUE_CODE IN
			       (SELECT CASE WHEN G.DEFAULT_VALUE_ORIGIN = 1 THEN G.LIST_VALUE
			               ELSE NULL END AS LIST_VALUE
			          FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT G,
			               APP___PAS__DBUSER.T_PAGECFG_UNIT        U
			         WHERE G.UNIT_CODE = U.UNIT_CODE
			           AND G.RELATION_ID IN
			               (SELECT RELATION_ID
			                  FROM APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA C
			                 WHERE C.BUSI_PRD_ID =#{busi_prd_id})
			           AND U.LABEL_NAME = '交费方式'
			           AND G.LIST_VALUE IS NOT NULL)
		]]>
	</select>
	
	<select id="JRQD_cs_findProductPayModeForFM" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select c.relation_id,g.default_value_origin, p.code, p.code2, p.display_order, g.read_only IS_READONLY, g.default_value
		      from APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE p,
		            APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT   g,
		            APP___PAS__DBUSER.T_pagecfg_unit u,
		            APP___PAS__DBUSER.t_pagecfg_prd_cate_rela c
		      where 1=1
		        and c.relation_id = g.relation_id
		        and c.relation_id = #{relation_id}
		        and g.unit_code = u.unit_code
		        and p.list_value_code = g.list_value
		         and u.bo_parameter = 'ChargeYear'
		]]>
	</select>
	
	
	<select id="JRQD_CS_queryCsBusiProdPOsByInsuredId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		SELECT A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG  ,A.COND_PRE_CHECK_FLAG
		FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A,APP___PAS__DBUSER.T_CS_BENEFIT_INSURED B
		 WHERE A.CHANGE_ID=B.CHANGE_ID 
		 	   AND A.POLICY_CHG_ID=B.POLICY_CHG_ID
		 	   AND A.OLD_NEW=B.OLD_NEW
		 	   AND ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
		<if test=" insured_id  != null "><![CDATA[ AND B.INSURED_ID = #{insured_id} ]]></if>
		<![CDATA[ ORDER BY  A.POLICY_CODE,A.BUSI_PROD_CODE,A.BUSI_ITEM_ID ]]>
	</select>
	
	<!-- 从配置表中查询交费期间 -->
	<select id="JRQD_CS_findChargeYearById" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TPEV.CODE,
			       TPEV.CODE2,
			       A.RELATION_ID,
			       A.PRD_ELEMENT_ID,
			       A.DEFAULT_VALUE_ORIGIN,
			       TPEV.DISPLAY_ORDER,
			       A.DEFAULT_VALUE,
			       A.READ_ONLY
			  FROM APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE TPEV
			 RIGHT JOIN (SELECT TPPE.RELATION_ID,
			                    TPPE.PRD_ELEMENT_ID,
			                    TPPE.LIST_VALUE,
			                    TPPE.READ_ONLY,
			                    TPPE.DEFAULT_VALUE,
			                    TPPE.DEFAULT_VALUE_ORIGIN,TPU.BO_PARAMETER
			               FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT TPPE,
			                    APP___PAS__DBUSER.T_PAGECFG_UNIT        TPU
			              WHERE TPPE.UNIT_CODE = TPU.UNIT_CODE
			                AND TPPE.RELATION_ID IN (SELECT T.RELATION_ID
			                                           FROM APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA T
			                                          WHERE 1 = 1
			                                            AND T.BUSI_PRD_ID = #{busi_prd_id}
			                                         	AND T.PRODUCT_ID = #{product_id}
			                                         )
			                AND TPU.BO_PARAMETER = 'ChargeYear') A
			    ON TPEV.LIST_VALUE_CODE = A.LIST_VALUE
		]]>
	</select>
	
	<!-- 从配置表中查询交费方式 -->
	<select id="JRQD_CS_findInitialTypeById" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TPEV.CODE,
			       TPEV.CODE2,
			       A.RELATION_ID,
			       A.PRD_ELEMENT_ID,
			       A.DEFAULT_VALUE_ORIGIN,
			       TPEV.DISPLAY_ORDER,
			       A.DEFAULT_VALUE,
			       A.READ_ONLY
			  FROM APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE TPEV
			 RIGHT JOIN (SELECT TPPE.RELATION_ID,
			                    TPPE.PRD_ELEMENT_ID,
			                    TPPE.LIST_VALUE,
			                    TPPE.READ_ONLY,
			                    TPPE.DEFAULT_VALUE,
			                    TPPE.DEFAULT_VALUE_ORIGIN,TPU.BO_PARAMETER
			               FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT TPPE,
			                    APP___PAS__DBUSER.T_PAGECFG_UNIT        TPU
			              WHERE TPPE.UNIT_CODE = TPU.UNIT_CODE
			                AND TPPE.RELATION_ID IN (SELECT T.RELATION_ID
			                                           FROM APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA T
			                                          WHERE 1 = 1
			                                            AND T.BUSI_PRD_ID = #{busi_prd_id}
			                                         	AND T.PRODUCT_ID = #{product_id}
			                                         )
			                AND TPU.BO_PARAMETER = 'InitialType') A
			    ON TPEV.LIST_VALUE_CODE = A.LIST_VALUE
		]]>
	</select>
	
	<!-- 从配置表中查询可选的保障计划 -->
	<select id="JRQD_CS_findSafeguardPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.CODE,T.CODE_NAME,T.CODE_VALUE
			  FROM (SELECT TBP.BUSINESS_PRD_ID,
			               TBP.PRODUCT_CODE_SYS,
			               TPEV.CODE,/**/
			               TPEV.CODE2 AS CODE_NAME,
			               TPU.LABEL_NAME,
			               (SELECT T.TARGET_PRD_ELEMENT_VALUE
			                  FROM APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT_ACTION T
			                 WHERE T.PRD_ELEMENT_ID = TPPE.PRD_ELEMENT_ID
			                   AND T.RELATION_ID = TPPCR.RELATION_ID
			                   AND T.ACTION_TYPE = 4
			                   AND T.PRD_ELEMENT_VALUE = TPEV.CODE) AS CODE_VALUE
			          FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
			          LEFT JOIN APP___PAS__DBUSER.T_PRODUCT_LIFE TPL
			            ON TBP.BUSINESS_PRD_ID = TPL.BUSINESS_PRD_ID
			          LEFT JOIN APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA TPPCR
			            ON TPL.PRODUCT_ID = TPPCR.PRODUCT_ID
			          LEFT JOIN APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT TPPE
			            ON TPPCR.RELATION_ID = TPPE.RELATION_ID
			          LEFT JOIN APP___PAS__DBUSER.T_PAGECFG_UNIT TPU
			            ON TPPE.UNIT_CODE = TPU.UNIT_CODE
			          LEFT JOIN APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE TPEV
			            ON TPEV.LIST_VALUE_CODE = TPPE.LIST_VALUE) T
			 WHERE T.LABEL_NAME = '保障计划'			  
			]]>				
			<if test="busi_prd_id  != null  and  busi_prd_id  != ''">
				<![CDATA[  AND T.BUSINESS_PRD_ID = #{busi_prd_id}]]>
			</if>
			<if test="busi_prod_code  != null  and  busi_prod_code  != ''">
				<![CDATA[  AND T.PRODUCT_CODE_SYS = #{busi_prod_code}]]>
			</if>
			<if test="init_amount  != null  and  init_amount  != ''">
				<![CDATA[  AND TO_NUMBER(T.CODE_VALUE) < #{init_amount}]]>
			</if>	   
			<if test="code  != null  and  code  != ''">
				<![CDATA[  AND TRIM(T.CODE) = #{code}]]>
			</if>			
			<![CDATA[ ORDER BY T.CODE ]]>
	</select>
	
	<!-- 上海医保843产品保障计划特殊处理 -->
	<select id="JRQD_CS_find843SafeguardPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.CODE,T.CODE_NAME,T.CODE_VALUE
		        FROM (SELECT TBP.BUSINESS_PRD_ID,
		                   TBP.PRODUCT_CODE_SYS,
		                   (CASE WHEN TPEV.CODE = '5000' THEN 'A'
                        		 WHEN TPEV.CODE = '10000' THEN 'B'
                        		 ELSE 'C' END) AS CODE,
		                   (CASE WHEN TPEV.CODE = '5000' THEN '计划一'
                        		 WHEN TPEV.CODE = '10000' THEN '计划二'
                        		 ELSE '计划三' END) AS CODE_NAME,
		                   TPU.LABEL_NAME,
		                   TPEV.CODE AS CODE_VALUE
		              FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT TBP
		              LEFT JOIN APP___PAS__DBUSER.T_PRODUCT_LIFE TPL
		                ON TBP.BUSINESS_PRD_ID = TPL.BUSINESS_PRD_ID
		              LEFT JOIN APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA TPPCR
		                ON TPL.PRODUCT_ID = TPPCR.PRODUCT_ID
		              LEFT JOIN APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT TPPE
		                ON TPPCR.RELATION_ID = TPPE.RELATION_ID
		              LEFT JOIN APP___PAS__DBUSER.T_PAGECFG_UNIT TPU
		                ON TPPE.UNIT_CODE = TPU.UNIT_CODE
		              LEFT JOIN APP___PAS__DBUSER.T_PAGECFG_ELEMENT_VALUE TPEV
		                ON TPEV.LIST_VALUE_CODE = TPPE.LIST_VALUE) T
		     WHERE T.LABEL_NAME = '保额' 		  
		]]>				
		<if test="busi_prd_id  != null  and  busi_prd_id  != ''">
			<![CDATA[  AND T.BUSINESS_PRD_ID = #{busi_prd_id}]]>
		</if>
		<if test="busi_prod_code  != null  and  busi_prod_code  != ''">
			<![CDATA[  AND T.PRODUCT_CODE_SYS = #{busi_prod_code}]]>
		</if>
		<if test="init_amount  != null  and  init_amount  != ''">
			<![CDATA[  AND TO_NUMBER(T.CODE_VALUE) < #{init_amount}]]>
		</if>	   
		<if test="code  != null  and  code  != ''">
			<![CDATA[  AND TRIM(T.CODE) = #{code}]]>
		</if>			
		<![CDATA[ ORDER BY T.CODE ]]>
	</select>
	
			<!-- 查询所有险种 -->
	<select id="JRQD_findAllCsContractBusiProdAllBusi" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC, 
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE, 
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE, 
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, 
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG ,A.COND_PRE_CHECK_FLAG
  from APP___PAS__DBUSER.t_cs_contract_busi_prod A
 where A.policy_chg_id = ${policy_chg_id} and A.old_new = '1']]>
 
	</select>
	
	
	<select id="JRQD_findAllCustomerBusiProdCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		 select distinct b.busi_prod_code from (
			select  a.busi_prod_code
          from APP___PAS__DBUSER.t_cs_contract_busi_prod A
         where A.policy_chg_id = #{policy_chg_id}
           and A.old_new = '1'
           union all
            select  cbp.busi_prod_code
   from dev_pas.t_insured_list il, dev_pas.t_contract_busi_prod cbp
  where il.policy_id = cbp.policy_id
    and il.customer_id = #{customer_id}
        union all
        select  tra.busi_prod_code
          from APP___PAS__DBUSER.t_risk_amount tra
         where tra.customer_id = #{customer_id}
           and tra.policy_code is null) b
		]]>
 
	</select>
	
	<!-- IO 外围接口调用 -->
	<select id="JRQD_queryResultInfo" resultType="java.util.Map" 	parameterType="java.util.Map">
		<![CDATA[
		 	select a.busi_prod_code, /*险种代码*/
			       a.busi_prod_name, /*险种名称*/
			       sum(amount) amount, /*基本保额，总分数*/
			       sum(std_prem_af) prem, /*总保费*/
			       sum(fee_amount) fee_amount /*补退费金额*/
			
			  from (select a.busi_prod_code,
			               (select t.product_name_sys
			                  from dev_pas.t_business_product t
			                 where t.business_prd_id = a.busi_prd_id) busi_prod_name,
			               (select sum(tpa.fee_amount)
			                  from dev_pas.t_prem_arap tpa
			                 where tpa.business_code = c.accept_code
			                   and tpa.busi_prod_code = a.busi_prod_code) fee_amount,
			               decode(b.count_way, 5, b.unit, b.amount) amount,
			               b.std_prem_af
			        
			          from APP___PAS__DBUSER.t_cs_accept_change      c,
			               APP___PAS__DBUSER.t_cs_contract_busi_prod a,
			               APP___PAS__DBUSER.t_cs_contract_product   b
			         where c.change_id = a.change_id
			           and a.change_id = b.change_id
			           and a.policy_chg_id = b.policy_chg_id
			           and a.old_new = b.old_new
			           and a.old_new = 1
			           and a.change_id = #{change_id}) A
			 group by a.busi_prod_code, a.busi_prod_name
		]]>
 
	</select>

	<!-- 按条件查询单条数据操作 -->
	<select id="JRQD_findCsContractBusiProdMaster" resultType="java.util.Map"
			parameterType="java.util.Map">
		<![CDATA[SELECT A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC,
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE,
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE,
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE,
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID,
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE,
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START,
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A WHERE  A.MASTER_BUSI_ITEM_ID IS NULL  ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findInsuredPolicyCount" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[     select count(1) countNumber
      from APP___PAS__DBUSER.t_contract_busi_prod cp,
           APP___PAS__DBUSER.t_business_product   brp,
           APP___PAS__DBUSER.t_insured_list       l
     where cp.busi_prd_id = brp.business_prd_id
       and cp.policy_id = l.policy_id
       and cp.master_busi_item_id is null
       and (brp.cover_period_type = 0 or 
           (brp.cover_period_type = 1 and brp.renew_option = 2))
       and l.customer_id = #{customer_id} 
       and cp.policy_id <>#{policy_id}
       ]]>
	</select>
	<!-- 查询该豁免附加险下的主险保费 -->
	<select id="JRQD_findAllcContractBusiProdForTry" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		SELECT
			  C.STD_PREM_AF,
			  C.APPEND_PREM_AF,
			  C.TOTAL_PREM_AF,
			  C.AMOUNT,
			  A.IS_WAIVED,
			  A.POLICY_CODE,
			  NVL(C.STD_PREM_AF, 0) + NVL(C.APPEND_PREM_AF, 0)+NVL(C.EXTRA_PREM_AF, 0) AS EXEMPTPREMIUM
	     FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
			  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT C
			     ON A.POLICY_CODE = C.POLICY_CODE
			    AND C.BUSI_ITEM_ID = A.BUSI_ITEM_ID
		 WHERE A.MASTER_BUSI_ITEM_ID IS NULL
			    AND A.IS_WAIVED = '0']]>
              <include refid="JRQD_csContractBusiProdWhereCondition" />                  		
	</select>
	<!-- 查询该豁免附加险下的长期附加险保费 -->
	<select id="JRQD_findAllcContractBusiProdForFjx" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[    
              SELECT    B.PRODUCT_CODE_SYS,
				        B.PRODUCT_NAME_STD,
				        C.STD_PREM_AF,
				        C.APPEND_PREM_AF,
				        C.TOTAL_PREM_AF,
				        B.COVER_PERIOD_TYPE,
				        C.AMOUNT,
				        A.IS_WAIVED,
				        A.POLICY_CODE,
				        NVL(C.STD_PREM_AF, 0) + NVL(C.APPEND_PREM_AF, 0)+NVL(C.EXTRA_PREM_AF, 0) AS EXEMPTPREMIUMS
				   FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
				  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT C
				     ON A.POLICY_ID = C.POLICY_ID
				    AND C.BUSI_ITEM_ID = A.BUSI_ITEM_ID
				  INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT B
				     ON B.BUSINESS_PRD_ID = A.BUSI_PRD_ID
				  WHERE B.COVER_PERIOD_TYPE = '0'
				    AND B.PRODUCT_CATEGORY = '10002'
				    AND A.IS_WAIVED = '0'
				    AND A.MASTER_BUSI_ITEM_ID IS NOT NULL
                   ]]>
		<include refid="JRQD_csContractBusiProdWhereCondition" />
	</select>
	
	<select id="JRQD_findMapCsContractBusiProdIfWaived" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT A.IS_WAIVED, A.POLICY_CODE
            FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
            WHERE A.MASTER_BUSI_ITEM_ID IS NOT NULL
		]]>
           <include refid="JRQD_csContractBusiProdWhereCondition" />                  		
	</select>
	
			<select id="JRQD_findAllMedicaPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select ccbp.REINSURED,ccbp.CAN_CHANGE_FLAG,ccbp.CAN_REINSURE_FLAG,ccbp.CHARGE_FLAG,ccbp.IS_RPU,ccbp.ASSURERENEW_FLAG,ccbp.OLD_POL_NO,ccbp.HESITATION_PERIOD_DAY,ccbp.GURNT_START_DATE, ccbp.BUSI_PRD_ID, ccbp.GURNT_PERIOD, ccbp.BUSI_PROD_CODE, ccbp.IS_WAIVED, ccbp.HESITATION2ACC, 
      ccbp.APPLY_CODE, ccbp.OLD_NEW, ccbp.CHANGE_ID, ccbp.POLICY_ID, ccbp.WAIVER, ccbp.OPERATION_TYPE, 
      ccbp.MASTER_BUSI_ITEM_ID, ccbp.PAIDUP_DATE, ccbp.EXPIRY_DATE, ccbp.LIABILITY_STATE, ccbp.POLICY_CODE, ccbp.RERINSTATE_DATE, 
      ccbp.RENEW_DECISION, ccbp.VALIDATE_DATE, ccbp.RENEWAL_STATE, ccbp.APL_PERMIT, ccbp.APPLY_DATE, 
      ccbp.RENEW, ccbp.RENEW_TIMES, ccbp.WAIVER_END, ccbp.MATURITY_DATE, ccbp.GURNT_PERD_TYPE, ccbp.POLICY_CHG_ID, 
      ccbp.BUSI_ITEM_ID, ccbp.LAPSE_DATE, ccbp.JOINT_LIFE_FLAG, ccbp.END_CAUSE, ccbp.ISSUE_DATE, ccbp.LAPSE_CAUSE, 
      ccbp.DECISION_CODE, ccbp.LOG_ID, ccbp.SUSPEND_DATE, ccbp.INITIAL_PREM_DATE, ccbp.SUSPEND_CAUSE, ccbp.DUE_LAPSE_DATE, ccbp.WAIVER_START, 
      ccbp.PRD_PKG_CODE, ccbp.GURNT_RENEW_END, ccbp.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,ccbp.SURRENDER_FLAG,ccbp.COND_PRE_CHECK_FLAG
  from dev_pas.t_cs_contract_busi_prod ccbp, dev_pas.t_business_product bp
 where ccbp.busi_prod_code = bp.PRODUCT_CODE_SYS
   and ccbp.old_new = '1'
   and bp.medica_flag = '1'
   and ccbp.policy_chg_id = #{policy_chg_id}
		]]>
		</select>
		
		
	<select id="JRQD_queryCountFor928" resultType="java.lang.Integer"  parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1)
			  FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A,
			       APP___PAS__DBUSER.T_CS_POLICY_CHANGE    B
			 WHERE A.CHANGE_ID = B.CHANGE_ID
			   AND A.POLICY_CHG_ID=B.POLICY_CHG_ID
			   AND A.BUSI_PROD_CODE in ('00928000','00928100')
			   AND B.CHANGE_ID = #{change_id}
			   AND B.ACCEPT_ID = #{accept_id}
		 ]]>		
	</select>
	
	
	<select id="JRQD_queryFMDataFor928" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.CHANGE_ID,
			       A.POLICY_CHG_ID,
			       A.POLICY_ID,
			       A.POLICY_CODE,
			       A.BUSI_ITEM_ID,
			       A.BUSI_PROD_CODE,
			       A.BUSI_PRD_ID,
			       A.MASTER_BUSI_ITEM_ID,
			       A.LIABILITY_STATE,
			       A.VALIDATE_DATE,
			       SUM(A.STD_PREM_AF) STD_PREM_AF,
			       SUM(A.AMOUNT) AMOUNT,
			       A.CHARGE_PERIOD,
			       A.CHARGE_YEAR,
			       A.PREM_FREQ,
			       A.PAY_DUE_DATE,
			       A.PREM_STATUS			       
			  FROM (SELECT A.CHANGE_ID,
			               A.POLICY_CHG_ID,
			               A.POLICY_CODE,
			               A.POLICY_ID,
			               A.BUSI_ITEM_ID,
			               A.BUSI_PROD_CODE,
			               A.BUSI_PRD_ID,
			               A.MASTER_BUSI_ITEM_ID,
			               A.LIABILITY_STATE,
			               A.VALIDATE_DATE,
			               B.STD_PREM_AF,
			               B.AMOUNT,
			               B.CHARGE_PERIOD,
			               B.CHARGE_YEAR,
			               B.PREM_FREQ,
			               C.PAY_DUE_DATE,
			               C.PREM_STATUS
			          FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD A
			         INNER JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT B
			            ON A.CHANGE_ID = B.CHANGE_ID
			           AND A.POLICY_CHG_ID = B.POLICY_CHG_ID
			           AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			           AND A.OLD_NEW=B.OLD_NEW
			         INNER JOIN DEV_PAS.T_CS_CONTRACT_EXTEND C
			            ON A.CHANGE_ID = C.CHANGE_ID
			           AND A.POLICY_CHG_ID = C.POLICY_CHG_ID
			           AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
			           AND B.ITEM_ID = C.ITEM_ID
			           AND B.OLD_NEW=C.OLD_NEW
			         WHERE A.POLICY_CHG_ID = #{policy_chg_id}
			         	   AND A.POLICY_ID = #{policy_id}
			               AND A.OLD_NEW = #{old_new}) A
			 GROUP BY A.CHANGE_ID,
			          A.POLICY_CHG_ID,
			          A.POLICY_CODE,
			          A.POLICY_ID,
			          A.BUSI_ITEM_ID,
			          A.BUSI_PROD_CODE,
			          A.BUSI_PRD_ID,
			          A.MASTER_BUSI_ITEM_ID,
			          A.LIABILITY_STATE,
			          A.VALIDATE_DATE,
			          A.CHARGE_PERIOD,
			          A.CHARGE_YEAR,
			          A.PREM_FREQ,
			          A.PAY_DUE_DATE,
			          A.PREM_STATUS

		 ]]>		
	</select>
	
	
	<select id="JRQD_checkDecreaseCoverage454" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[ 
			select A.REINSURED,A.CAN_CHANGE_FLAG,A.CAN_REINSURE_FLAG,A.CHARGE_FLAG,A.IS_RPU,A.ASSURERENEW_FLAG,A.OLD_POL_NO,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.IS_WAIVED, A.HESITATION2ACC,
			A.APPLY_CODE, A.OLD_NEW, A.CHANGE_ID, A.POLICY_ID, A.WAIVER, A.OPERATION_TYPE,
			A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE,
			A.RENEW_DECISION, A.VALIDATE_DATE, A.RENEWAL_STATE, A.APL_PERMIT, A.APPLY_DATE,
			A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.POLICY_CHG_ID,
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE,
			A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START,
			A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START, INITIAL_VALIDATE_DATE,A.SURRENDER_FLAG,A.COND_PRE_CHECK_FLAG
			  from dev_pas.t_cs_contract_busi_prod a
			 where a.master_busi_item_id in (
			                                    
			                                    select cpc.busi_item_id
			                                      from dev_pas.t_cs_contract_busi_prod cpc
			                                     where cpc.busi_item_id = #{busi_item_id}
			                                       and cpc.policy_chg_id = #{policy_chg_id}
			                                       and cpc.old_new = '1'
			                                       and cpc.busi_prod_code = '00454000')
			   and a.busi_prod_code in('00878000','00882000')
			   and a.policy_chg_id =#{policy_chg_id} 
			   and a.old_new = '1'
		 ]]>		
	</select>
</mapper>
