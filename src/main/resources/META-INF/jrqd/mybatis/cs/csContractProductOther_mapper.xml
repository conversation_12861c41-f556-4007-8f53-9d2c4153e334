<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsContractProductOtherDao">

	<sql id="JRQD_csContractProductOtherWhereCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" field20 != null and field20 != ''  "><![CDATA[ AND A.FIELD20 = #{field20} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" field19 != null and field19 != ''  "><![CDATA[ AND A.FIELD19 = #{field19} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" field17 != null and field17 != ''  "><![CDATA[ AND A.FIELD17 = #{field17} ]]></if>
		<if test=" field18 != null and field18 != ''  "><![CDATA[ AND A.FIELD18 = #{field18} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" field13 != null and field13 != ''  "><![CDATA[ AND A.FIELD13 = #{field13} ]]></if>
		<if test=" field14 != null and field14 != ''  "><![CDATA[ AND A.FIELD14 = #{field14} ]]></if>
		<if test=" field15 != null and field15 != ''  "><![CDATA[ AND A.FIELD15 = #{field15} ]]></if>
		<if test=" field16 != null and field16 != ''  "><![CDATA[ AND A.FIELD16 = #{field16} ]]></if>
		<if test=" field10 != null and field10 != ''  "><![CDATA[ AND A.FIELD10 = #{field10} ]]></if>
		<if test=" field11 != null and field11 != ''  "><![CDATA[ AND A.FIELD11 = #{field11} ]]></if>
		<if test=" field12 != null and field12 != ''  "><![CDATA[ AND A.FIELD12 = #{field12} ]]></if>
		<if test=" field7 != null and field7 != ''  "><![CDATA[ AND A.FIELD7 = #{field7} ]]></if>
		<if test=" field6 != null and field6 != ''  "><![CDATA[ AND A.FIELD6 = #{field6} ]]></if>
		<if test=" field9 != null and field9 != ''  "><![CDATA[ AND A.FIELD9 = #{field9} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" field8 != null and field8 != ''  "><![CDATA[ AND A.FIELD8 = #{field8} ]]></if>
		<if test=" field3 != null and field3 != ''  "><![CDATA[ AND A.FIELD3 = #{field3} ]]></if>
		<if test=" field2 != null and field2 != ''  "><![CDATA[ AND A.FIELD2 = #{field2} ]]></if>
		<if test=" field5 != null and field5 != ''  "><![CDATA[ AND A.FIELD5 = #{field5} ]]></if>
		<if test=" field4 != null and field4 != ''  "><![CDATA[ AND A.FIELD4 = #{field4} ]]></if>
		<if test=" field1 != null and field1 != ''  "><![CDATA[ AND A.FIELD1 = #{field1} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" is_extend_rate != null and is_extend_rate != ''  "><![CDATA[ AND A.IS_EXTEND_RATE = #{is_extend_rate} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsContractProductOtherByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addCsContractProductOther"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CONTRACT_PRODUCT_OTHER.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER(
				LOG_ID,OLD_NEW,OPERATION_TYPE,PRODUCT_ID, BUSI_PRD_ID, FIELD20, ITEM_ID, 
				FIELD19, APPLY_CODE, INSERT_TIMESTAMP, FIELD17, FIELD18, UPDATE_BY,
				CHANGE_ID, POLICY_CHG_ID, BUSI_ITEM_ID, POLICY_ID,FIELD13, FIELD14, 
				FIELD15, FIELD16, FIELD10, FIELD11, INSERT_TIME,FIELD12, UPDATE_TIME, 
				FIELD7, FIELD6, FIELD9, POLICY_CODE, FIELD8,FIELD3, FIELD2, FIELD5, 
				FIELD4, FIELD1, UPDATE_TIMESTAMP, INSERT_BY,IS_EXTEND_RATE ) 
			VALUES (
				#{log_id, jdbcType=NUMERIC},#{old_new, jdbcType=VARCHAR} ,#{operation_type, jdbcType=VARCHAR} ,
				#{product_id, jdbcType=NUMERIC}, #{busi_prd_id, jdbcType=NUMERIC} , #{field20, jdbcType=VARCHAR} , 
				#{item_id, jdbcType=NUMERIC} , #{field19, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{field17, jdbcType=VARCHAR} , #{field18, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} 
				, #{field13, jdbcType=VARCHAR} , #{field14, jdbcType=VARCHAR} , #{field15, jdbcType=VARCHAR} , #{field16, jdbcType=VARCHAR} , #{field10, jdbcType=VARCHAR} , #{field11, jdbcType=VARCHAR} , SYSDATE 
				, #{field12, jdbcType=VARCHAR} , SYSDATE , #{field7, jdbcType=VARCHAR} , #{field6, jdbcType=VARCHAR} , #{field9, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{field8, jdbcType=VARCHAR} 
				, #{field3, jdbcType=VARCHAR} , #{field2, jdbcType=VARCHAR} , #{field5, jdbcType=VARCHAR} , #{field4, jdbcType=VARCHAR} , #{field1, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC},#{is_extend_rate,jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsContractProductOther" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateCsContractProductOther" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER ]]>
		<set>
		<trim suffixOverrides=",">
			LOG_ID=#{log_id, jdbcType=NUMERIC},
			OLD_NEW=#{old_new, jdbcType=VARCHAR} ,
			OPERATION_TYPE=#{operation_type, jdbcType=VARCHAR} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    BUSI_PRD_ID = #{busi_prd_id, jdbcType=NUMERIC} ,
			FIELD20 = #{field20, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			FIELD19 = #{field19, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			FIELD17 = #{field17, jdbcType=VARCHAR} ,
			FIELD18 = #{field18, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			FIELD13 = #{field13, jdbcType=VARCHAR} ,
			FIELD14 = #{field14, jdbcType=VARCHAR} ,
			FIELD15 = #{field15, jdbcType=VARCHAR} ,
			FIELD16 = #{field16, jdbcType=VARCHAR} ,
			FIELD10 = #{field10, jdbcType=VARCHAR} ,
			FIELD11 = #{field11, jdbcType=VARCHAR} ,
			FIELD12 = #{field12, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			FIELD7 = #{field7, jdbcType=VARCHAR} ,
			FIELD6 = #{field6, jdbcType=VARCHAR} ,
			FIELD9 = #{field9, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			FIELD8 = #{field8, jdbcType=VARCHAR} ,
			FIELD3 = #{field3, jdbcType=VARCHAR} ,
			FIELD2 = #{field2, jdbcType=VARCHAR} ,
			FIELD5 = #{field5, jdbcType=VARCHAR} ,
			FIELD4 = #{field4, jdbcType=VARCHAR} ,
			FIELD1 = #{field1, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    IS_EXTEND_RATE = #{is_extend_rate, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsContractProductOtherByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID,A.OLD_NEW,A.OPERATION_TYPE,A.PRODUCT_ID, A.BUSI_PRD_ID, A.FIELD20, A.ITEM_ID, A.FIELD19, A.APPLY_CODE, 
			A.FIELD17, A.FIELD18, A.CHANGE_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.FIELD13, A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, 
			A.FIELD12, A.FIELD7, A.FIELD6, A.FIELD9, A.POLICY_CODE, A.FIELD8, 
			A.FIELD3, A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.IS_EXTEND_RATE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_queryCsContractProductOtherByItemIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsContractProductOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID,A.OLD_NEW,A.OPERATION_TYPE,A.PRODUCT_ID, A.BUSI_PRD_ID, A.FIELD20, A.ITEM_ID, A.FIELD19, A.APPLY_CODE, 
			A.FIELD17, A.FIELD18, A.CHANGE_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.FIELD13, A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, 
			A.FIELD12, A.FIELD7, A.FIELD6, A.FIELD9, A.POLICY_CODE, A.FIELD8, 
			A.FIELD3, A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.IS_EXTEND_RATE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsContractProductOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID,A.OLD_NEW,A.OPERATION_TYPE,A.PRODUCT_ID, A.BUSI_PRD_ID, A.FIELD20, A.ITEM_ID, A.FIELD19, A.APPLY_CODE, 
			A.FIELD17, A.FIELD18, A.CHANGE_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.FIELD13, A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, 
			A.FIELD12, A.FIELD7, A.FIELD6, A.FIELD9, A.POLICY_CODE, A.FIELD8, 
			A.FIELD3, A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1,A.IS_EXTEND_RATE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractProductOtherWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findCsContractProductOtherTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCsContractProductOtherForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LOG_ID,B.OLD_NEW,B.OPERATION_TYPE,B.PRODUCT_ID, B.BUSI_PRD_ID, B.FIELD20, B.ITEM_ID, B.FIELD19, B.APPLY_CODE, 
			B.FIELD17, B.FIELD18, B.CHANGE_ID, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID, 
			B.FIELD13, B.FIELD14, B.FIELD15, B.FIELD16, B.FIELD10, B.FIELD11, 
			B.FIELD12, B.FIELD7, B.FIELD6, B.FIELD9, B.POLICY_CODE, B.FIELD8, 
			B.FIELD3, B.FIELD2, B.FIELD5, B.FIELD4, B.FIELD1, B.IS_EXTEND_RATE FROM (
					SELECT ROWNUM RN, A.LOG_ID,A.OLD_NEW,A.OPERATION_TYPE,A.PRODUCT_ID, A.BUSI_PRD_ID, A.FIELD20, A.ITEM_ID, A.FIELD19, A.APPLY_CODE, 
			A.FIELD17, A.FIELD18, A.CHANGE_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.FIELD13, A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, 
			A.FIELD12, A.FIELD7, A.FIELD6, A.FIELD9, A.POLICY_CODE, A.FIELD8, 
			A.FIELD3, A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.IS_EXTEND_RATE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询单条操作 -->
	<select id="JRQD_findCsContractProductOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LOG_ID,A.OLD_NEW,A.OPERATION_TYPE,A.PRODUCT_ID, A.BUSI_PRD_ID, A.FIELD20, A.ITEM_ID, A.FIELD19, A.APPLY_CODE, 
			A.FIELD17, A.FIELD18, A.CHANGE_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, 
			A.FIELD13, A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, 
			A.FIELD12, A.FIELD7, A.FIELD6, A.FIELD9, A.POLICY_CODE, A.FIELD8, 
			A.FIELD3, A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1, A.IS_EXTEND_RATE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractProductOtherWhereCondition" /> 
	</select>
	
</mapper>
