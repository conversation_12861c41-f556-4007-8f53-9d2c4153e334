<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsContractProductDao">

	<sql id="JRQD_csContractProductWhereCondition"> 
		<if test=" is_pause  != null "><![CDATA[ AND A.IS_PAUSE = #{is_srp} ]]></if>
	
		<if test=" is_pause  != null "><![CDATA[ AND A.IS_PAUSE = #{is_pause} ]]></if>
		<if test=" prod_pkg_plan_code != null and prod_pkg_plan_code != ''  "><![CDATA[ AND A.PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" norenew_reason != null and norenew_reason != ''  "><![CDATA[ AND A.NORENEW_REASON = #{norenew_reason} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" extra_prem_af  != null "><![CDATA[ AND A.EXTRA_PREM_AF = #{extra_prem_af} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" cc_sa  != null "><![CDATA[ AND A.CC_SA = #{cc_sa} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" count_way != null and count_way != ''  "><![CDATA[ AND A.COUNT_WAY = #{count_way} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" bonus_mode  != null "><![CDATA[ AND A.bonus_mode = #{bonus_mode} ]]></if>
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" bonus_sa  != null "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" health_service_flag  != null "><![CDATA[ AND A.HEALTH_SERVICE_FLAG = #{health_service_flag} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" unit  != null "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" pause_date  != null  and  pause_date  != ''  "><![CDATA[ AND A.PAUSE_DATE = #{pause_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" prem_freq != null and prem_freq != ''  "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" is_gift  != null "><![CDATA[ AND A.IS_GIFT = #{is_gift} ]]></if>
		<if test=" initial_discnt_prem_af  != null "><![CDATA[ AND A.INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af} ]]></if>
		<if test=" renewal_discnted_prem_af  != null "><![CDATA[ AND A.RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af} ]]></if>
		<if test=" initial_extra_prem_af  != null "><![CDATA[ AND A.INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af} ]]></if>
		<if test=" renewal_extra_prem_af  != null "><![CDATA[ AND A.RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af} ]]></if>
		<if test=" deductible_franchise  != null "><![CDATA[ AND A.DEDUCTIBLE_FRANCHISE = #{deductible_franchise} ]]></if>
		<if test=" payout_rate  != null "><![CDATA[ AND A.PAYOUT_RATE = #{payout_rate} ]]></if>
		<if test=" is_waived != null "><![CDATA[ AND A.IS_WAIVED= #{is_waived} ]]></if>
		<if test=" waiver_start  != null "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" waiver_end != null "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
		<if test=" additional_prem_af != null "><![CDATA[ AND A.ADDITIONAL_PREM_AF = #{additional_prem_af} ]]></if>
		<if test=" append_prem_af != null "><![CDATA[ AND A.APPEND_PREM_AF = #{append_prem_af} ]]></if>
		<if test=" paidup_date != null "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" maturity_date != null "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" initial_amount != null "><![CDATA[ AND A.INITIAL_AMOUNT = #{initial_amount} ]]></if>
		<if test=" is_master_item != null "><![CDATA[ AND A.IS_MASTER_ITEM = #{is_master_item} ]]></if>
		<if test=" annu_pay_type != null and annu_pay_type != ''  "><![CDATA[ AND A.ANNU_PAY_TYPE = #{annu_pay_type} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="JRQD_queryCsContractProductByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>
	<sql id="JRQD_queryCsContractProductByOldNewCondition">
		<if test=" old_new != null and old_new != '' "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
	</sql>
	<sql id="JRQD_queryCsContractProductByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>
	<!-- liuxingle 添加操作 -->
	<insert id="JRQD_saveCsContractProduct" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CONTRACT_PRODUCT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT(
				IS_PAUSE, IS_SRP,  PROD_PKG_PLAN_CODE, APPLY_CODE, NORENEW_REASON, ORGAN_CODE, 
				OLD_NEW, UPDATE_BY, CHARGE_YEAR, CHANGE_ID, EXTRA_PREM_AF, POLICY_ID, CC_SA, 
				OPERATION_TYPE, UPDATE_TIME, AMOUNT,  EXPIRY_DATE, PAY_YEAR, 
				LIABILITY_STATE, PAY_PERIOD, COUNT_WAY, POLICY_CODE, RERINSTATE_DATE, RENEW_DECISION, VALIDATE_DATE, 
				bonus_mode, UPDATE_TIMESTAMP, INSERT_BY, BENEFIT_LEVEL, PRODUCT_ID, BONUS_SA, PRODUCT_CODE, 
				APPLY_DATE, COVERAGE_PERIOD, ITEM_ID, INSERT_TIMESTAMP, POLICY_CHG_ID, BUSI_ITEM_ID, HEALTH_SERVICE_FLAG, 
				LAPSE_DATE, UNIT, COVERAGE_YEAR, INSERT_TIME, END_CAUSE, LAPSE_CAUSE, TOTAL_PREM_AF, 
				DECISION_CODE, PAUSE_DATE, LOG_ID, CHARGE_PERIOD, STD_PREM_AF, INTEREST_MODE, SUSPEND_DATE, SUSPEND_CAUSE, 
				PREM_FREQ ,IS_GIFT,INITIAL_DISCNT_PREM_AF,RENEWAL_DISCNTED_PREM_AF,INITIAL_EXTRA_PREM_AF,RENEWAL_EXTRA_PREM_AF,
				DEDUCTIBLE_FRANCHISE,PAYOUT_RATE,IS_WAIVED,WAIVER_START,WAIVER_END,ADDITIONAL_PREM_AF,APPEND_PREM_AF,PAIDUP_DATE,MATURITY_DATE,INITIAL_AMOUNT,IS_MASTER_ITEM,LAST_BONUS_DATE,ANNU_PAY_TYPE) 
			VALUES (
				#{is_pause, jdbcType=NUMERIC},#{is_srp, jdbcType=NUMERIC}, #{prod_pkg_plan_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{norenew_reason, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} 
				, #{old_new, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{cc_sa, jdbcType=NUMERIC} 
				, #{operation_type, jdbcType=VARCHAR} , SYSDATE , #{amount, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} , #{pay_year, jdbcType=NUMERIC} 
				, #{liability_state, jdbcType=NUMERIC} , #{pay_period, jdbcType=VARCHAR} , #{count_way, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} 
				, #{bonus_mode, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{benefit_level, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{bonus_sa, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} 
				, #{apply_date, jdbcType=DATE} , #{coverage_period, jdbcType=VARCHAR} , #{log_id,jdbcType=NUMERIC}, CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{health_service_flag, jdbcType=NUMERIC} 
				, #{lapse_date, jdbcType=DATE} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{lapse_cause, jdbcType=VARCHAR} , #{total_prem_af, jdbcType=NUMERIC} 
				, #{decision_code, jdbcType=VARCHAR} , #{pause_date, jdbcType=DATE} , nvl(#{item_id, jdbcType=NUMERIC},APP___PAS__DBUSER.S_CONTRACT_PRODUCT__ITEM_ID.NEXTVAL), #{charge_period, jdbcType=VARCHAR} , #{std_prem_af, jdbcType=NUMERIC} , #{interest_mode, jdbcType=NUMERIC} , #{suspend_date, jdbcType=DATE} 
				, #{suspend_cause, jdbcType=VARCHAR} , #{prem_freq, jdbcType=NUMERIC}, #{is_gift, jdbcType=NUMERIC}, #{initial_discnt_prem_af, jdbcType=NUMERIC}, #{renewal_discnted_prem_af, jdbcType=NUMERIC}, #{initial_extra_prem_af, jdbcType=NUMERIC}
				, #{renewal_extra_prem_af, jdbcType=NUMERIC},#{deductible_franchise, jdbcType=NUMERIC},#{payout_rate, jdbcType=NUMERIC},#{is_waived, jdbcType=NUMERIC},#{waiver_start, jdbcType=DATE},#{waiver_end, jdbcType=DATE},#{additional_prem_af, jdbcType=NUMERIC},#{append_prem_af, jdbcType=NUMERIC}
				, #{paidup_date, jdbcType=DATE},#{maturity_date, jdbcType=DATE},#{initial_amount, jdbcType=NUMERIC},#{is_master_item, jdbcType=NUMERIC},#{last_bonus_date, jdbcType=DATE}, #{annu_pay_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>
	<!-- 添加操作 -->
	<insert id="JRQD_addCsContractProduct" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CONTRACT_PRODUCT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT(
				IRREGULAR_PREM,START_PAY_AGE,START_PAY_DATE,IS_PAUSE, IS_SRP,PROD_PKG_PLAN_CODE, APPLY_CODE, NORENEW_REASON, ORGAN_CODE, 
				OLD_NEW, UPDATE_BY, CHARGE_YEAR, CHANGE_ID, EXTRA_PREM_AF, POLICY_ID, CC_SA, 
				OPERATION_TYPE, UPDATE_TIME, AMOUNT,  EXPIRY_DATE, PAY_YEAR, 
				LIABILITY_STATE, PAY_PERIOD, COUNT_WAY, POLICY_CODE, RERINSTATE_DATE, RENEW_DECISION, VALIDATE_DATE, 
				bonus_mode, UPDATE_TIMESTAMP, INSERT_BY, BENEFIT_LEVEL, PRODUCT_ID, BONUS_SA, PRODUCT_CODE, 
				APPLY_DATE, COVERAGE_PERIOD, ITEM_ID, INSERT_TIMESTAMP, POLICY_CHG_ID, BUSI_ITEM_ID, HEALTH_SERVICE_FLAG, 
				LAPSE_DATE, UNIT, COVERAGE_YEAR, INSERT_TIME, END_CAUSE, LAPSE_CAUSE, TOTAL_PREM_AF, 
				DECISION_CODE, PAUSE_DATE, LOG_ID, CHARGE_PERIOD, STD_PREM_AF, INTEREST_MODE, SUSPEND_DATE, 
				SUSPEND_CAUSE, PREM_FREQ ,PAY_FREQ ,IS_GIFT,INITIAL_DISCNT_PREM_AF,RENEWAL_DISCNTED_PREM_AF,INITIAL_EXTRA_PREM_AF,RENEWAL_EXTRA_PREM_AF,
				DEDUCTIBLE_FRANCHISE,PAYOUT_RATE,IS_WAIVED,WAIVER_START,WAIVER_END,ADDITIONAL_PREM_AF,APPEND_PREM_AF,PAIDUP_DATE,MATURITY_DATE,INITIAL_AMOUNT,IS_MASTER_ITEM,LAST_BONUS_DATE,ANNU_PAY_TYPE) 
			VALUES (
				#{irregular_prem, jdbcType=NUMERIC},#{start_pay_age, jdbcType=NUMERIC},#{start_pay_date, jdbcType=DATE}
				, #{is_pause, jdbcType=NUMERIC}, #{is_srp, jdbcType=NUMERIC}, #{prod_pkg_plan_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{norenew_reason, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} 
				, #{old_new, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{cc_sa, jdbcType=NUMERIC} 
				, #{operation_type, jdbcType=VARCHAR} , SYSDATE , #{amount, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} , #{pay_year, jdbcType=NUMERIC} 
				, #{liability_state, jdbcType=NUMERIC} , #{pay_period, jdbcType=VARCHAR} , #{count_way, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} 
				, #{bonus_mode, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{benefit_level, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{bonus_sa, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} 
				, #{apply_date, jdbcType=DATE} , #{coverage_period, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{health_service_flag, jdbcType=NUMERIC} 
				, #{lapse_date, jdbcType=DATE} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{lapse_cause, jdbcType=VARCHAR} , #{total_prem_af, jdbcType=NUMERIC} 
				, #{decision_code, jdbcType=VARCHAR} , #{pause_date, jdbcType=DATE} , #{log_id, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} , #{std_prem_af, jdbcType=NUMERIC} , #{interest_mode, jdbcType=NUMERIC} , #{suspend_date, jdbcType=DATE} 
				, #{suspend_cause, jdbcType=VARCHAR} , #{prem_freq, jdbcType=NUMERIC} ,#{pay_freq, jdbcType=VARCHAR} , #{is_gift, jdbcType=NUMERIC}, #{initial_discnt_prem_af, jdbcType=NUMERIC}, #{renewal_discnted_prem_af, jdbcType=NUMERIC}, #{initial_extra_prem_af, jdbcType=NUMERIC}, #{renewal_extra_prem_af, jdbcType=NUMERIC}
				, #{deductible_franchise, jdbcType=NUMERIC},#{payout_rate, jdbcType=NUMERIC},#{is_waived, jdbcType=NUMERIC},#{waiver_start, jdbcType=DATE},#{waiver_end, jdbcType=DATE},#{additional_prem_af, jdbcType=NUMERIC},#{append_prem_af, jdbcType=NUMERIC}
				, #{paidup_date, jdbcType=DATE},#{maturity_date, jdbcType=DATE},#{initial_amount, jdbcType=NUMERIC},#{is_master_item, jdbcType=NUMERIC},#{last_bonus_date, jdbcType=DATE}, #{annu_pay_type, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="JRQD_deleteCsContractProduct" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT WHERE LOG_ID = #{log_id} ]]>
	</delete>
	<!-- 按条件删除 -->
	<delete id="JRQD_deleteCsContractProductByPolicyChgId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE  POLICY_CHG_ID = #{policy_chg_id}  ]]>
	</delete>

	<!-- 按条件删除 -->
	<delete id="JRQD_deleteCsContractProductByCondition" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE 1=1 ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
	</delete>
	<!-- 修改操作 -->
	<update id="JRQD_updateCsContractProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT ]]>
		<set>
			<trim suffixOverrides=",">
				IRREGULAR_PREM=#{irregular_prem, jdbcType=NUMERIC},
				START_PAY_AGE=#{start_pay_age, jdbcType=NUMERIC},
				START_PAY_DATE=#{start_pay_date, jdbcType=DATE},
				
				IS_PAUSE = #{is_pause, jdbcType=NUMERIC} ,
				IS_SRP = #{is_srp,jdbcType=NUMERIC} ,
				PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code, jdbcType=VARCHAR} ,
				APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
				NORENEW_REASON = #{norenew_reason, jdbcType=VARCHAR} ,
				ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
				OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
				CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
				PAY_FREQ = #{pay_freq , jdbcType=VARCHAR},
				EXTRA_PREM_AF = #{extra_prem_af,
				jdbcType=NUMERIC} ,
				POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
				CC_SA = #{cc_sa, jdbcType=NUMERIC} ,
				OPERATION_TYPE =
				#{operation_type, jdbcType=VARCHAR} ,
				UPDATE_TIME = SYSDATE ,
				AMOUNT =
				#{amount, jdbcType=NUMERIC} ,
				EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
				PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
				LIABILITY_STATE =
				#{liability_state, jdbcType=NUMERIC} ,
				PAY_PERIOD = #{pay_period,
				jdbcType=VARCHAR} ,
				COUNT_WAY = #{count_way, jdbcType=VARCHAR} ,
				POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
				RERINSTATE_DATE =
				#{rerinstate_date, jdbcType=DATE} ,
				RENEW_DECISION =
				#{renew_decision, jdbcType=NUMERIC} ,
				VALIDATE_DATE =
				#{validate_date, jdbcType=DATE} ,
				bonus_mode =
				#{bonus_mode, jdbcType=NUMERIC} ,
				bonus_w_mode =
				#{bonus_w_mode, jdbcType=NUMERIC} ,
				UPDATE_TIMESTAMP =
				CURRENT_TIMESTAMP ,
				BENEFIT_LEVEL = #{benefit_level,
				jdbcType=VARCHAR} ,
				PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
				BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
				PRODUCT_CODE =
				#{product_code, jdbcType=VARCHAR} ,
				APPLY_DATE = #{apply_date,
				jdbcType=DATE} ,
				COVERAGE_PERIOD = #{coverage_period,
				jdbcType=VARCHAR} ,
				ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
				POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
				BUSI_ITEM_ID =
				#{busi_item_id, jdbcType=NUMERIC} ,
				HEALTH_SERVICE_FLAG =
				#{health_service_flag, jdbcType=NUMERIC} ,
				LAPSE_DATE = #{lapse_date,
				jdbcType=DATE} ,
				UNIT = #{unit, jdbcType=NUMERIC} ,
				COVERAGE_YEAR =
				#{coverage_year, jdbcType=NUMERIC} ,
				END_CAUSE = #{end_cause,
				jdbcType=VARCHAR} ,
				LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
				TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
				DECISION_CODE =
				#{decision_code, jdbcType=VARCHAR} ,
				PAUSE_DATE = #{pause_date,
				jdbcType=DATE} ,
				LOG_ID = #{log_id, jdbcType=NUMERIC} ,
				CHARGE_PERIOD
				= #{charge_period, jdbcType=VARCHAR} ,
				STD_PREM_AF = #{std_prem_af,
				jdbcType=NUMERIC} ,
				INTEREST_MODE = #{interest_mode, jdbcType=NUMERIC}
				,
				SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
				SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
				ANNU_PAY_TYPE = #{annu_pay_type, jdbcType=VARCHAR} ,
				PREM_FREQ = #{prem_freq,
				jdbcType=NUMERIC} ,
				IS_GIFT = #{is_gift, jdbcType=NUMERIC} ,
				INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af, jdbcType=NUMERIC} ,
				RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af, jdbcType=NUMERIC} ,
				INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af, jdbcType=NUMERIC} ,
				RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af, jdbcType=NUMERIC} ,
				DEDUCTIBLE_FRANCHISE =#{deductible_franchise, jdbcType=NUMERIC},
				PAYOUT_RATE =#{payout_rate, jdbcType=NUMERIC},
				IS_WAIVED =#{is_waived, jdbcType=NUMERIC} ,
				WAIVER_START =#{waiver_start, jdbcType=DATE} ,
				WAIVER_END = #{waiver_end, jdbcType=DATE},
				ADDITIONAL_PREM_AF=#{additional_prem_af, jdbcType=NUMERIC} ,
				APPEND_PREM_AF=#{append_prem_af, jdbcType=NUMERIC} ,
				PAIDUP_DATE=#{paidup_date, jdbcType=DATE} ,
				MATURITY_DATE=#{maturity_date, jdbcType=DATE},
				INITIAL_AMOUNT=#{initial_amount, jdbcType=NUMERIC},
				IS_MASTER_ITEM=#{is_master_item, jdbcType=NUMERIC},
				LAST_BONUS_DATE = #{last_bonus_date, jdbcType=DATE} 
			</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>
	
	<update id="JRQD_updateCsContractTotelPremAf" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT ]]>
		<set>
				TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC}
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

	<!-- 按索引查询操作 -->
	<select id="JRQD_findCsContractProductByLogId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.IS_PAUSE, A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.OLD_NEW, A.CHARGE_YEAR, A.CHANGE_ID, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.OPERATION_TYPE, A.AMOUNT,  A.EXPIRY_DATE, A.PAY_YEAR, 
			A.LIABILITY_STATE, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.bonus_mode, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, 
			A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, 
			A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, 
			A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.PREM_FREQ , A.IS_GIFT, A.INITIAL_DISCNT_PREM_AF, A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,
			A.DEDUCTIBLE_FRANCHISE, A.PAYOUT_RATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,A.IS_MASTER_ITEM
			FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE ROWNUM <=  1  ]]>
		<include refid="JRQD_queryCsContractProductByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

	<!-- 计算每期豁免保费 -->
	<select id="JRQD_calWaivedStdAll" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT *
  FROM (SELECT SUM(CCP.STD_PREM_AF) STD_PREM_AF
          FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE      CPC,
               APP___PAS__DBUSER.T_BUSINESS_PRODUCT      BP,
               APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT   CCP,
               APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD CCBP
         WHERE CCP.POLICY_CHG_ID = CPC.POLICY_CHG_ID
           AND CCBP.BUSI_ITEM_ID = CCP.BUSI_ITEM_ID
           AND BP.PRODUCT_CODE_SYS = CCBP.BUSI_PROD_CODE
           AND CCP.POLICY_CHG_ID = CCBP.POLICY_CHG_ID
           AND CCP.OLD_NEW = '1'
           AND CCBP.OLD_NEW = '1'
           AND BP.COVER_PERIOD_TYPE = '0'
           AND CPC.POLICY_CHG_ID = '${policy_chg_id}')
 WHERE ROWNUM < 2000 
		   ]]>
	</select>

	<!-- 按索引查询操作 -->
	<select id="JRQD_findCsContractProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.IS_PAUSE, A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.OLD_NEW, A.CHARGE_YEAR, A.CHANGE_ID, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.OPERATION_TYPE, A.AMOUNT,  A.EXPIRY_DATE, A.PAY_YEAR, A.PAY_FREQ,
			A.LIABILITY_STATE, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.bonus_mode, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, 
			A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, 
			A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, 
			A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.PREM_FREQ , A.IS_GIFT, A.INITIAL_DISCNT_PREM_AF, A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,
			A.DEDUCTIBLE_FRANCHISE, A.PAYOUT_RATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,A.IS_MASTER_ITEM
			FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsContractProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.IS_PAUSE, A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.OLD_NEW, A.CHARGE_YEAR, A.CHANGE_ID, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.OPERATION_TYPE, A.AMOUNT, A.EXPIRY_DATE, A.PAY_YEAR, 
			A.LIABILITY_STATE, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.bonus_mode, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, 
			A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, 
			A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, 
			A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.PREM_FREQ , A.IS_GIFT, A.INITIAL_DISCNT_PREM_AF, A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,
			 A.DEDUCTIBLE_FRANCHISE, A.PAYOUT_RATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,A.IS_MASTER_ITEM
			FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsContractProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
				<![CDATA[ SELECT A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.OLD_NEW, A.CHARGE_YEAR, A.CHANGE_ID, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.OPERATION_TYPE, A.AMOUNT,  A.EXPIRY_DATE, A.PAY_YEAR,  A.PAY_FREQ,
			A.LIABILITY_STATE, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.bonus_mode, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, 
			A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, 
			A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, 
			A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.PREM_FREQ , A.IS_GIFT, A.INITIAL_DISCNT_PREM_AF, A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,
			A.DEDUCTIBLE_FRANCHISE, A.PAYOUT_RATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,A.IS_MASTER_ITEM,a.charge_period 
			 FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
		<![CDATA[ ORDER BY A.PRODUCT_CODE ]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllContractProductS" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT   A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.IS_MASTER_ITEM, A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED,
			A.INTEREST_MODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF,
			A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR,A.EXPIRY_DATE,A.PAY_PERIOD,A.LIABILITY_STATE,A.POLICY_CODE,A.COUNT_WAY,A.IS_GIFT,A.RERINSTATE_DATE,
			A.ADDITIONAL_PREM_AF,A.RENEW_DECISION,A.VALIDATE_DATE,A.BONUS_MODE,A.BENEFIT_LEVEL,A.PRODUCT_ID,A.BONUS_SA,
			A.PRODUCT_CODE,A.APPLY_DATE,A.INTEREST_FLAG,A.COVERAGE_PERIOD,A.ITEM_ID,A.RENEWAL_EXTRA_PREM_AF,A.WAIVER_END,
			A.MATURITY_DATE,A.BUSI_ITEM_ID,A.HEALTH_SERVICE_FLAG,A.LAPSE_DATE,A.PAY_FREQ,A.UNIT,A.COVERAGE_YEAR,
			A.END_CAUSE,A.LAPSE_CAUSE,A.RENEWAL_DISCNTED_PREM_AF,A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD,
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ,A.INITIAL_DISCNT_PREM_AF, A.BONUS_W_MODE, A.LAST_BONUS_DATE 
			 FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
		<![CDATA[ ORDER BY A.PRODUCT_CODE ]]>
	</select>
	
	<!-- 查询是否是分红险 -->
	<select id="JRQD_findProductCategory1" resultType="java.util.Map"
		parameterType="java.util.Map">
				<![CDATA[ select case when t.product_category1 ='20002' then 1
                          else 0 end as product_category1
                          from APP___PAS__DBUSER.T_BUSINESS_PRODUCT t
                           where t.business_prd_id = (select a.busi_prd_id
                                 from APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A
                                  WHERE 1=1  
								  AND A.CHANGE_ID = #{change_id} 
								  AND A.POLICY_CHG_ID = #{policy_chg_id} 
								  AND A.OLD_NEW = #{old_new} 
								  AND A.BUSI_ITEM_ID = #{busi_item_id} )
                  ]]>
		
	</select>
	
	
<!-- 查询是否是主险 -->
<select id="JRQD_findAllCsContractProductl" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select p.product_category
         from APP___PAS__DBUSER.T_cs_contract_busi_prod A,
              APP___PAS__DBUSER.t_business_PRODUCT      p
        where A.busi_prd_id = p.business_prd_id
        ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
		</select>
	<!-- 查询个数操作 -->
	<select id="JRQD_findCsContractProductTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
	</select>
	
	<!-- 查询个数操作 -->
	<select id="JRQD_findCsContractProductChangeTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT sum(t.ACOUNT)
          FROM (SELECT COUNT(policy_chg_id) ACOUNT
                   from APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD
         where policy_chg_id = #{policy_chg_id}
           and busi_item_id = #{busi_item_id}
           and operation_type in ('1', '2')
                UNION ALL
                SELECT COUNT(policy_chg_id) ACOUNT
                  from APP___PAS__DBUSER.T_CS_contract_product
         where policy_chg_id = #{policy_chg_id}
           and busi_item_id = #{busi_item_id}
           and operation_type in ('1', '2')) t
		  ]]>
		
	</select>
	

	<!-- 分页查询操作 -->
	<select id="JRQD_queryCsContractProductForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.IRREGULAR_PREM,B.START_PAY_AGE,B.START_PAY_DATE,B.ANNU_PAY_TYPE,B.IS_PAUSE,  B.PROD_PKG_PLAN_CODE, B.APPLY_CODE, B.NORENEW_REASON, B.ORGAN_CODE, B.LAST_BONUS_DATE
			B.OLD_NEW, B.CHARGE_YEAR, B.CHANGE_ID, B.EXTRA_PREM_AF, B.POLICY_ID, B.CC_SA, 
			B.OPERATION_TYPE, B.AMOUNT,   B.EXPIRY_DATE, B.PAY_YEAR, 
			B.LIABILITY_STATE, B.PAY_PERIOD, B.COUNT_WAY, B.POLICY_CODE, B.RERINSTATE_DATE, B.RENEW_DECISION, B.VALIDATE_DATE, 
			B.bonus_mode, B.BENEFIT_LEVEL, B.PRODUCT_ID, B.BONUS_SA, B.PRODUCT_CODE, 
			B.APPLY_DATE, B.COVERAGE_PERIOD, B.ITEM_ID, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.HEALTH_SERVICE_FLAG, 
			B.LAPSE_DATE, B.UNIT, B.COVERAGE_YEAR, B.END_CAUSE, B.LAPSE_CAUSE, B.TOTAL_PREM_AF, 
			B.DECISION_CODE, B.PAUSE_DATE, B.LOG_ID, B.CHARGE_PERIOD, B.STD_PREM_AF, B.INTEREST_MODE, B.SUSPEND_DATE, 
			B.SUSPEND_CAUSE, B.PREM_FREQ , B.IS_GIFT, B.INITIAL_DISCNT_PREM_AF, B.RENEWAL_DISCNTED_PREM_AF, B.INITIAL_EXTRA_PREM_AF, B.RENEWAL_EXTRA_PREM_AF ,B.ADDITIONAL_PREM_AF,B.APPEND_PREM_AF,B.PAIDUP_DATE,B.MATURITY_DATE,B.INITIAL_AMOUNT,B.IS_MASTER_ITEM FROM (
					SELECT ROWNUM RN, A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.IS_PAUSE, A.ANNU_PAY_TYPE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE,A.LAST_BONUS_DATE 
			A.OLD_NEW, A.CHARGE_YEAR, A.CHANGE_ID, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.OPERATION_TYPE, A.AMOUNT,  A.EXPIRY_DATE, A.PAY_YEAR, 
			A.LIABILITY_STATE, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.bonus_mode, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, 
			A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, 
			A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, 
			A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.PREM_FREQ , A.IS_GIFT, A.INITIAL_DISCNT_PREM_AF, A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF, 
			A.DEDUCTIBLE_FRANCHISE, A.PAYOUT_RATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,A.IS_MASTER_ITEM
			FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 更新生效日期 -->
	<update id="JRQD_updateProductValidDate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT ]]>
		<set>
			<trim suffixOverrides=",">
				VALIDATE_DATE = #{validate_date,
				jdbcType=DATE} ,
				EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
				AMOUNT =
				#{amount, jdbcType=NUMERIC} ,
				TOTAL_PREM_AF = #{total_prem_af,
				jdbcType=NUMERIC} ,
				STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE 1=1  
		  AND CHANGE_ID = #{change_id} 
		  AND POLICY_CHG_ID = #{policy_chg_id} 
		  AND OLD_NEW = #{old_new} 
		  AND BUSI_ITEM_ID = #{busi_item_id}  
		  AND ITEM_ID = #{item_id} ]]>
	</update>
	
	
	<update id="JRQD_saveContractProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_PRODUCT ]]>
		<set>
			<trim suffixOverrides=",">
				DECISION_CODE = #{decision_code, jdbcType=DATE} ,
			</trim>
		</set>
		<![CDATA[   WHERE  ITEM_ID = #{item_id} ]]>
	</update>
	
	<!-- 查询抄单责任组信息 -->
	<select id="JRQD_findLiabilityGroups" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ 
				select ccp.product_code,
				       (select bp.product_name_std
				          from APP___PAS__DBUSER.t_business_product bp
				         where bp.business_prd_id = ccp.product_id) as product_name_std,
				        (select l.option_type from APP___PAS__DBUSER.t_product_life l where l.product_id = ccp.product_id) as option_type,
				       ccp.charge_period,
				       ccp.charge_year,
				       ccp.coverage_period,
				       ccp.COVERAGE_YEAR as  COVERAGE_YEAR ,
				       to_char(ccp.prem_freq) as prem_freq,
				       ccp.amount,
				       ccp.std_prem_af,
				       ccp.unit,
				        ccp.amount as adjust_amount,
				       (select sum(t.FEE_AMOUNT) as  lgAddPremium
							from APP___PAS__DBUSER.t_CS_PREM_ARAP t where t.BUSINESS_CODE = #{accept_code} and t.policy_code = ccp.policy_code ) as addprem,
				       ccp.validate_date,
				       ccp.expiry_date,				       
				       ccp.pay_period,
				       ccp.pay_year,
				       (case
         				when ccp.pay_freq is null then
          			   (select d.PLAN_FREQ
             				from APP___PAS__DBUSER.T_PAY_PLAN d
           					 where d.item_id = ccp.item_id and rownum=1)
           					 else
           					 ccp.pay_freq
       					end) as pay_freq,
				       ccp.annu_pay_type lggetmode,
				       ccp.unit as sum_unit
				  from APP___PAS__DBUSER.t_cs_contract_product ccp
				 where 1=1 and ccp.liability_state !='3'--投保规则不传终止责任
		  ]]>
		  
		  <if test="old_new != null and old_new != ''"><![CDATA[ and ccp.old_new = #{old_new} ]]></if>
		  <if test="change_id != null"><![CDATA[ and ccp.change_id = #{change_id} ]]></if>
		  <if test="policy_chg_id != null"><![CDATA[ and ccp.policy_chg_id = #{policy_chg_id} ]]></if>
		  <if test="busi_item_id != null"><![CDATA[ and ccp.busi_item_id = #{busi_item_id} ]]></if>
	</select>
	
	<!-- 查询保单责任组信息 -->
	<select id="JRQD_findLiabilityGroupsOfHistory" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ 
				select ccp.product_code,
				       (select bp.product_name_std
				          from APP___PAS__DBUSER.t_business_product bp
				         where bp.business_prd_id = ccp.product_id) as product_name_std,
				       (select l.option_type from APP___PAS__DBUSER.t_product_life l where l.product_id = ccp.product_id) as option_type,
				       ccp.charge_period,
				       ccp.charge_year,
				       ccp.coverage_period,
				       ccp.coverage_year,
				       to_char(ccp.prem_freq) as prem_freq,
				       ccp.amount,
				       ccp.total_prem_af,
				       ccp.unit,
				       ccp.amount as adjust_amount,				      
				       ccp.validate_date,
				       ccp.expiry_date,			      
				       ccp.pay_period,
				       ccp.pay_year,
				       ccp.pay_freq,
				       ccp.annu_pay_type lggetmode,
						ccp.unit as sum_unit
				  from APP___PAS__DBUSER.t_contract_product ccp
				 where 1=1
		  ]]>
		  
		  <if test="policy_id != null"><![CDATA[ and ccp.policy_id = #{policy_id} ]]></if>
	</select>
	
	<!-- 查询当前受理下所涉及的被保人为customerId责任组信息， -->
	<select id="JRQD_findCsAccumulateAmountInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
				<![CDATA[ SELECT A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.OLD_NEW, A.CHARGE_YEAR, A.CHANGE_ID, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.OPERATION_TYPE, A.AMOUNT,A.EXPIRY_DATE, A.PAY_YEAR, 
			A.LIABILITY_STATE, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.bonus_mode, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, 
			A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, 
			A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, 
			A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.PREM_FREQ , A.IS_GIFT, A.INITIAL_DISCNT_PREM_AF, A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,
			A.DEDUCTIBLE_FRANCHISE, A.PAYOUT_RATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,A.IS_MASTER_ITEM,
			E.BUSI_PROD_CODE AS PRODUCT_CODE
			 FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A, APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD E WHERE ROWNUM <=  1000  AND E.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND E.POLICY_CHG_ID=A.POLICY_CHG_ID  ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND E.OLD_NEW = #{old_new} ]]></if>
		<if test="policy_chg_id_list  != null and policy_chg_id_list.size()!=0 ">
			<![CDATA[  AND (]]>
			<foreach collection="policy_chg_id_list" item="policy_chg_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ A.POLICY_CHG_ID=#{policy_chg_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
			<if test="policy_liability_state != null">
				<![CDATA[ AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER D WHERE 1=1 ]]>
					<![CDATA[  AND D.POLICY_ID=A.POLICY_ID AND (]]>
						<foreach collection="policy_chg_id_list" item="policy_chg_id_item"
							index="index" open="" close="" separator="or">
							<![CDATA[ D.POLICY_CHG_ID=#{policy_chg_id_item} ]]>
						</foreach>
					<![CDATA[) ]]>
				<![CDATA[ AND D.LIABILITY_STATE=#{policy_liability_state})]]>
			</if>
			<![CDATA[ AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_cs_BENEFIT_INSURED B WHERE B.BUSI_ITEM_ID=A.BUSI_ITEM_ID ]]>
			<![CDATA[  AND (]]>
			<foreach collection="policy_chg_id_list" item="policy_chg_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ B.POLICY_CHG_ID=#{policy_chg_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
			 <![CDATA[ AND 
				    EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_INSURED_LIST C WHERE C.LIST_ID=B.INSURED_ID AND C.CUSTOMER_ID=#{customer_id}))]]>	
		</if>
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

	<!-- 查询被保人下变更过的责任组信息 -->
	<select id="JRQD_findCsAccumulateAmountInfoByUpdateOrAdd" resultType="java.util.Map"
		parameterType="java.util.Map">
				<![CDATA[ SELECT A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.OLD_NEW, A.CHARGE_YEAR, A.CHANGE_ID, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.OPERATION_TYPE, A.AMOUNT,A.EXPIRY_DATE, A.PAY_YEAR, 
			A.LIABILITY_STATE, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.bonus_mode, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, 
			A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, 
			A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, 
			A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.PREM_FREQ , A.IS_GIFT, A.INITIAL_DISCNT_PREM_AF, A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,
			A.DEDUCTIBLE_FRANCHISE, A.PAYOUT_RATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,A.IS_MASTER_ITEM,
			E.BUSI_PROD_CODE AS PRODUCT_CODE
			 FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A, APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD E WHERE ROWNUM <=  1000  AND E.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND E.POLICY_CHG_ID=A.POLICY_CHG_ID  ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND E.OLD_NEW = #{old_new} ]]></if>
		<![CDATA[ and (a.operation_type = 2 or a.operation_type = 1) ]]>
		<if test="policy_chg_id_list  != null and policy_chg_id_list.size()!=0 ">
			<![CDATA[  AND (]]>
			<foreach collection="policy_chg_id_list" item="policy_chg_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ A.POLICY_CHG_ID=#{policy_chg_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
			<if test="policy_liability_state != null">
				<![CDATA[ AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER D WHERE 1=1 ]]>
					<![CDATA[  AND D.POLICY_ID=A.POLICY_ID AND (]]>
						<foreach collection="policy_chg_id_list" item="policy_chg_id_item"
							index="index" open="" close="" separator="or">
							<![CDATA[ D.POLICY_CHG_ID=#{policy_chg_id_item} ]]>
						</foreach>
					<![CDATA[) ]]>
				<![CDATA[ AND D.LIABILITY_STATE=#{policy_liability_state})]]>
			</if>
			<![CDATA[ AND EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_cs_BENEFIT_INSURED B WHERE B.BUSI_ITEM_ID=A.BUSI_ITEM_ID ]]>
			<![CDATA[  AND (]]>
			<foreach collection="policy_chg_id_list" item="policy_chg_id_item"
				index="index" open="" close="" separator="or">
				<![CDATA[ B.POLICY_CHG_ID=#{policy_chg_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
			 <![CDATA[ AND 
				    EXISTS (SELECT 1 FROM APP___PAS__DBUSER.T_INSURED_LIST C WHERE C.LIST_ID=B.INSURED_ID AND C.CUSTOMER_ID=#{customer_id}))]]>	
		</if>
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<!-- 通过productId查询saUnit -->
	<select id="JRQD_findProductLife" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select sa_unit from APP___PAS__DBUSER.t_product_life where product_id=#{product_id}
        ]]>
		
		</select>
    
    <!-- 更新追加保费到T_CS_CONTRACT_PRODUCT表 -->
	<update id="JRQD_updateCsContractByPolicyId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT ]]>
		<set>
				APPEND_PREM_AF = #{append_prem_af, jdbcType=NUMERIC},
				TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC},
				OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR},
				AMOUNT = #{amount, jdbcType=NUMERIC},
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<if test=" policy_id  != null "><![CDATA[ AND POLICY_ID = #{policy_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND PRODUCT_CODE = #{product_code} ]]></if>
	</update>		
	<!-- 通过changeId,itemId,oldNew查询busiPrdId和amount
		 对领取形式变更录入页面进行优化，该方法为该页面专用 -->
	<select id="JRQD_findAmountBusiByChangeAndItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.IS_PAUSE,A.ANNU_PAY_TYPE,A.PROD_PKG_PLAN_CODE,A.APPLY_CODE,A.NORENEW_REASON,A.ORGAN_CODE,A.OLD_NEW,A.CHARGE_YEAR,A.CHANGE_ID,
       			A.EXTRA_PREM_AF,A.POLICY_ID,A.CC_SA,A.OPERATION_TYPE,A.AMOUNT,A.EXPIRY_DATE,A.PAY_YEAR, A.PAY_FREQ,A.LIABILITY_STATE,
       			A.PAY_PERIOD,A.COUNT_WAY,A.POLICY_CODE,A.RERINSTATE_DATE,A.RENEW_DECISION,A.VALIDATE_DATE,A.bonus_mode,A.BENEFIT_LEVEL,
       			A.PRODUCT_ID,A.BONUS_SA,A.PRODUCT_CODE,A.APPLY_DATE,A.COVERAGE_PERIOD,A.ITEM_ID,A.POLICY_CHG_ID,A.BUSI_ITEM_ID,A.HEALTH_SERVICE_FLAG,
       			A.LAPSE_DATE,A.UNIT,A.COVERAGE_YEAR,A.END_CAUSE,A.LAPSE_CAUSE,A.TOTAL_PREM_AF,A.DECISION_CODE,A.PAUSE_DATE,A.LOG_ID,
       			A.CHARGE_PERIOD,A.STD_PREM_AF,A.INTEREST_MODE,A.SUSPEND_DATE,A.SUSPEND_CAUSE,A.PREM_FREQ,A.IS_GIFT,A.INITIAL_DISCNT_PREM_AF,
       			A.RENEWAL_DISCNTED_PREM_AF,A.INITIAL_EXTRA_PREM_AF,A.RENEWAL_EXTRA_PREM_AF,A.DEDUCTIBLE_FRANCHISE,A.PAYOUT_RATE,A.IS_WAIVED,
       			A.WAIVER_START,A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,
       			A.IS_MASTER_ITEM,E.BUSI_PRD_ID
  			FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A,
      			 APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD E
 			WHERE 1 = 1
   			AND A.CHANGE_ID = E.CHANGE_ID
		   	AND A.POLICY_CHG_ID = E.POLICY_CHG_ID
		   	AND A.BUSI_ITEM_ID = E.BUSI_ITEM_ID
		   	AND A.OLD_NEW = E.OLD_NEW
		   	AND A.CHANGE_ID = #{change_id}
		   	AND A.ITEM_ID = #{item_id}
		   	AND A.OLD_NEW = #{old_new}
		   	AND A.POLICY_CHG_ID = #{policy_chg_id}
		]]>
	</select>
	<!-- 查询险种保额 -->
	<select id="JRQD_findAllSAForBusi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IRREGULAR_PREM,A.START_PAY_AGE,A.START_PAY_DATE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE,A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.OLD_NEW, A.CHARGE_YEAR, A.CHANGE_ID, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, 
			A.OPERATION_TYPE, A.AMOUNT,  A.EXPIRY_DATE, A.PAY_YEAR,  A.PAY_FREQ,
			A.LIABILITY_STATE, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.bonus_mode, A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, 
			A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, 
			A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, 
			A.DECISION_CODE, A.PAUSE_DATE, A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.PREM_FREQ , A.IS_GIFT, A.INITIAL_DISCNT_PREM_AF, A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,
			A.DEDUCTIBLE_FRANCHISE, A.PAYOUT_RATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.PAIDUP_DATE,A.MATURITY_DATE,A.INITIAL_AMOUNT,A.IS_MASTER_ITEM,a.charge_period 
			 FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractProductWhereCondition" />
	</select>
	
		<select id="JRQD_sumStdPremAfByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select  sum(tcp.std_prem_af) as std_prem_af
				  from (select *
				          from (select *
				                  from APP___PAS__DBUSER.t_cs_contract_busi_prod tcbp
				                 where tcbp.liability_state != 3
				                   and tcbp.policy_chg_id = #{policy_chg_id}
				                   and tcbp.old_new = #{old_new}) a
				          left join APP___PAS__DBUSER.t_business_product tbp
				            on a.busi_prd_id = tbp.business_prd_id
				         where tbp.cover_period_type = 0) b
				  left join APP___PAS__DBUSER.t_contract_product tcp
				    on b.busi_item_id = tcp.busi_item_id  ]]>
	</select>
	
	<select id="JRQD_sumStdPremAfByCondition_pt" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select  sum(tcp.std_prem_af) as std_prem_af
				  from (select *
				          from (select *
				                  from APP___PAS__DBUSER.t_cs_contract_busi_prod tcbp
				                 where tcbp.liability_state != 3
				                   and tcbp.policy_chg_id = #{policy_chg_id}
				                   and tcbp.old_new = #{old_new}) a
				          left join APP___PAS__DBUSER.t_business_product tbp
				            on a.busi_prd_id = tbp.business_prd_id
				         where tbp.cover_period_type = 0) b
				  left join APP___PAS__DBUSER.t_cs_contract_product tcp
				    on b.busi_item_id = tcp.busi_item_id
				    and tcp.old_new = #{old_new}
				    and tcp.policy_chg_id = #{policy_chg_id}
				    and tcp.product_id != 956 ]]>
	</select>
	
	<!-- 通过保单号和险种号查询保全责任信息 -->
	<select id="JRQD_queryCsDutyInfoForRI" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.BUSI_ITEM_ID,A.ANNU_PAY_TYPE,A.PRODUCT_CODE,A.UNIT,A.STD_PREM_AF,A.TOTAL_PREM_AF,A.AMOUNT,A.PREM_FREQ,
			A.CHARGE_YEAR,A.COVERAGE_YEAR,A.CHARGE_PERIOD,A.PAY_PERIOD,A.PAY_YEAR,A.EXPIRY_DATE,
			A.VALIDATE_DATE,A.PAY_FREQ,A.INSERT_TIME,A.UPDATE_TIME,A.OLD_NEW,
			C.ENDORSE_CODE,B.BUSI_PRD_ID,B.BUSI_PROD_CODE,D.SERVICE_CODE,D.ACCEPT_STATUS
			FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A,APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD B,
			APP___PAS__DBUSER.T_CS_POLICY_CHANGE C,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE D
			WHERE A.POLICY_CHG_ID=C.POLICY_CHG_ID AND A.BUSI_ITEM_ID=B.BUSI_ITEM_ID AND C.ACCEPT_ID=D.ACCEPT_ID
			AND C.POLICY_CODE=#{policy_code} AND NVL(B.OLD_POL_NO,B.BUSI_ITEM_ID)=#{pol_no}
		]]>
	</select>
	
	<!-- /** fanzc_wb 38509 start **/ -->
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsContractProductByAcceptCode" resultType="java.util.Map"
		parameterType="java.util.Map">
				<![CDATA[ 
					select ccp.LAST_BONUS_DATE,ccp.ANNU_PAY_TYPE,ccp.IS_PAUSE,ccp.IS_SRP, ccp.PROD_PKG_PLAN_CODE, ccp.APPLY_CODE, ccp.NORENEW_REASON, ccp.ORGAN_CODE, 
      ccp.OLD_NEW, ccp.CHARGE_YEAR, ccp.CHANGE_ID, ccp.EXTRA_PREM_AF, ccp.POLICY_ID, ccp.CC_SA, 
      ccp.OPERATION_TYPE, ccp.AMOUNT,  ccp.EXPIRY_DATE, ccp.PAY_YEAR,  ccp.PAY_FREQ,
      ccp.LIABILITY_STATE, ccp.PAY_PERIOD, ccp.COUNT_WAY, ccp.POLICY_CODE, ccp.RERINSTATE_DATE, ccp.RENEW_DECISION, ccp.VALIDATE_DATE, 
      ccp.bonus_mode, ccp.BENEFIT_LEVEL, ccp.PRODUCT_ID, ccp.BONUS_SA, ccp.PRODUCT_CODE, 
      ccp.APPLY_DATE, ccp.COVERAGE_PERIOD, ccp.ITEM_ID, ccp.POLICY_CHG_ID, ccp.BUSI_ITEM_ID, ccp.HEALTH_SERVICE_FLAG, 
      ccp.LAPSE_DATE, ccp.UNIT, ccp.COVERAGE_YEAR, ccp.END_CAUSE, ccp.LAPSE_CAUSE, ccp.TOTAL_PREM_AF, 
      ccp.DECISION_CODE, ccp.PAUSE_DATE, ccp.LOG_ID, ccp.CHARGE_PERIOD, ccp.STD_PREM_AF, ccp.INTEREST_MODE, ccp.SUSPEND_DATE, 
      ccp.SUSPEND_CAUSE, ccp.PREM_FREQ , ccp.IS_GIFT, ccp.INITIAL_DISCNT_PREM_AF, ccp.RENEWAL_DISCNTED_PREM_AF, ccp.INITIAL_EXTRA_PREM_AF, ccp.RENEWAL_EXTRA_PREM_AF,
      ccp.DEDUCTIBLE_FRANCHISE, ccp.PAYOUT_RATE, ccp.IS_WAIVED, ccp.WAIVER_START, ccp.WAIVER_END,ccp.ADDITIONAL_PREM_AF,ccp.APPEND_PREM_AF,ccp.PAIDUP_DATE,ccp.MATURITY_DATE,ccp.INITIAL_AMOUNT,ccp.IS_MASTER_ITEM from dev_pas.t_cs_accept_change cac ,dev_pas.t_cs_policy_change cpc,dev_pas.t_cs_contract_product ccp
	where cac.accept_id = cpc.accept_id and cpc.policy_chg_id = ccp.policy_chg_id and cac.accept_code = #{accept_code}
	and ccp.operation_type = '1'
			 
			 ]]>
	</select>
	<!-- /** fanzc_wb 38509 start **/ -->

	<!-- 添加操作 -->
	<insert id="JRQD_addCsContractProductNS" useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CONTRACT_PRODUCT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT(
				IS_PAUSE, IS_SRP,PROD_PKG_PLAN_CODE, APPLY_CODE, NORENEW_REASON, ORGAN_CODE, 
				OLD_NEW, UPDATE_BY, CHARGE_YEAR, CHANGE_ID, EXTRA_PREM_AF, POLICY_ID, CC_SA, 
				OPERATION_TYPE, UPDATE_TIME, AMOUNT,  EXPIRY_DATE, PAY_YEAR, 
				LIABILITY_STATE, PAY_PERIOD, COUNT_WAY, POLICY_CODE, RERINSTATE_DATE, RENEW_DECISION, VALIDATE_DATE, 
				bonus_mode, UPDATE_TIMESTAMP, INSERT_BY, BENEFIT_LEVEL, PRODUCT_ID, BONUS_SA, PRODUCT_CODE, 
				APPLY_DATE, COVERAGE_PERIOD, ITEM_ID, INSERT_TIMESTAMP, POLICY_CHG_ID, BUSI_ITEM_ID, HEALTH_SERVICE_FLAG, 
				LAPSE_DATE, UNIT, COVERAGE_YEAR, INSERT_TIME, END_CAUSE, LAPSE_CAUSE, TOTAL_PREM_AF, 
				DECISION_CODE, PAUSE_DATE, LOG_ID, CHARGE_PERIOD, STD_PREM_AF, INTEREST_MODE, SUSPEND_DATE, 
				SUSPEND_CAUSE, PREM_FREQ ,PAY_FREQ ,IS_GIFT,INITIAL_DISCNT_PREM_AF,RENEWAL_DISCNTED_PREM_AF,INITIAL_EXTRA_PREM_AF,RENEWAL_EXTRA_PREM_AF,
				DEDUCTIBLE_FRANCHISE,PAYOUT_RATE,IS_WAIVED,WAIVER_START,WAIVER_END,ADDITIONAL_PREM_AF,APPEND_PREM_AF,PAIDUP_DATE,MATURITY_DATE,INITIAL_AMOUNT,IS_MASTER_ITEM,LAST_BONUS_DATE,ANNU_PAY_TYPE) 
			VALUES (
				#{is_pause, jdbcType=NUMERIC}, #{is_srp, jdbcType=NUMERIC}, #{prod_pkg_plan_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{norenew_reason, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} 
				, #{old_new, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{cc_sa, jdbcType=NUMERIC} 
				, #{operation_type, jdbcType=VARCHAR} , SYSDATE , #{amount, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} , #{pay_year, jdbcType=NUMERIC} 
				, #{liability_state, jdbcType=NUMERIC} , #{pay_period, jdbcType=VARCHAR} , #{count_way, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} 
				, #{bonus_mode, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{benefit_level, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{bonus_sa, jdbcType=NUMERIC} , #{product_code, jdbcType=VARCHAR} 
				, #{apply_date, jdbcType=DATE} , #{coverage_period, jdbcType=VARCHAR} , nvl(#{item_id, jdbcType=NUMERIC}, APP___PAS__DBUSER.S_CONTRACT_PRODUCT__ITEM_ID.NEXTVAL), CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{health_service_flag, jdbcType=NUMERIC} 
				, #{lapse_date, jdbcType=DATE} , #{unit, jdbcType=NUMERIC} , #{coverage_year, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{lapse_cause, jdbcType=VARCHAR} , #{total_prem_af, jdbcType=NUMERIC} 
				, #{decision_code, jdbcType=VARCHAR} , #{pause_date, jdbcType=DATE} , #{log_id, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR} , #{std_prem_af, jdbcType=NUMERIC} , #{interest_mode, jdbcType=NUMERIC} , #{suspend_date, jdbcType=DATE} 
				, #{suspend_cause, jdbcType=VARCHAR} , #{prem_freq, jdbcType=NUMERIC} ,#{pay_freq, jdbcType=VARCHAR} , #{is_gift, jdbcType=NUMERIC}, #{initial_discnt_prem_af, jdbcType=NUMERIC}, #{renewal_discnted_prem_af, jdbcType=NUMERIC}, #{initial_extra_prem_af, jdbcType=NUMERIC}, #{renewal_extra_prem_af, jdbcType=NUMERIC}
				, #{deductible_franchise, jdbcType=NUMERIC},#{payout_rate, jdbcType=NUMERIC},#{is_waived, jdbcType=NUMERIC},#{waiver_start, jdbcType=DATE},#{waiver_end, jdbcType=DATE},#{additional_prem_af, jdbcType=NUMERIC},#{append_prem_af, jdbcType=NUMERIC}
				, #{paidup_date, jdbcType=DATE},#{maturity_date, jdbcType=DATE},#{initial_amount, jdbcType=NUMERIC},#{is_master_item, jdbcType=NUMERIC},#{last_bonus_date, jdbcType=DATE}, #{annu_pay_type, jdbcType=VARCHAR} ) 
		]]>
	</insert>
	
	<!-- 42584 查询该险种是否为新增附加险添加并返回 总保费 FOR CT -->
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllMapCsContractProductForNs" resultType="java.util.Map"
		parameterType="java.util.Map">
				<![CDATA[ 
					SELECT CCP.POLICY_CODE,CCP.BUSI_ITEM_ID,CCP.TOTAL_PREM_AF
                FROM DEV_PAS.T_CS_POLICY_CHANGE CPC
                LEFT JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT CCP
                  ON CPC.POLICY_CHG_ID = CCP.POLICY_CHG_ID
                   LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD CCBP
                ON CPC.POLICY_CHG_ID  = CCBP.POLICY_CHG_ID
                AND CCBP.BUSI_ITEM_ID = CCP.BUSI_ITEM_ID
                AND CCBP.OLD_NEW = '1'
                LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
                  ON CPC.ACCEPT_ID = CAC.ACCEPT_ID
                 AND CAC.CHANGE_ID = CCP.CHANGE_ID
               WHERE 1 = 1
                 AND CAC.SERVICE_CODE = 'NS'
                 AND CCP.OLD_NEW = '1'
                 AND CAC.ACCEPT_STATUS = '18'
                 AND CCP.OPERATION_TYPE = '1'
                 AND CCP.BUSI_ITEM_ID = #{busi_item_id}   
                 AND CPC.POLICY_CODE = #{policy_code}  ]]>
			 <if test=" busi_Prd_Id  != null and busi_Prd_Id != '' "><![CDATA[ AND CCBP.BUSI_PRD_ID = #{busi_Prd_Id} ]]></if>
			 
	</select>
	<select id="JRQD_queryInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
				<![CDATA[ 
				select 
distinct a.CHANNEL_TYPE channelCode,  --销售渠道
c.busi_item_id busiitemid,
c.busi_prd_id businessPrdId,  --险种ID
b.prem_freq chargeMode,      --缴费方式  （年交，月交，一次性缴清）
a.organ_code organcode,--管理机构
b.validate_date valadatedate,--生效时间
(select customer_gender from dev_pas.t_customer where customer_id=(select distinct bb.customer_id
                                   from dev_pas.t_benefit_insured aa,
                                        dev_pas.t_insured_list    bb
                                  where bb.list_id = aa.insured_id
                                    and bb.policy_id = b.policy_id and aa.order_id='1' and aa.busi_item_id = b.busi_item_id)) gender,
(select customer_birthday from dev_pas.t_customer where customer_id=(select distinct bb.customer_id
                                   from dev_pas.t_benefit_insured aa,
                                        dev_pas.t_insured_list    bb
                                  where bb.list_id = aa.insured_id
                                    and bb.policy_id = b.policy_id and aa.order_id='1' and aa.busi_item_id = b.busi_item_id)) birthday,
b.Charge_Year changeYear
from dev_pas.t_cs_contract_master a,
dev_pas.t_cs_contract_product  b,
dev_pas.t_cs_contract_busi_prod c 
where a.policy_chg_id=b.policy_chg_id
and b.policy_chg_id=c.policy_chg_id
and b.change_id=c.change_id
and b.busi_item_id=c.busi_item_id
and c.master_busi_item_id is null
and a.change_id=b.change_id
and a.change_id= #{changeid}
and b.old_new= #{oldnew}
			 
			 ]]>
	</select>
	
	<!-- 61836 成功转换后续期总保费 -->
	<select id="JRQD_PA_queryNextPremForRR" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT (SUM(CASE
		              WHEN TB.COVER_PERIOD_TYPE = 0 AND A.PAIDUP_DATE < C.PAY_DUE_DATE THEN
		              /*长期险未缴费期满*/
		               +B.STD_PREM_AF
		              WHEN TB.COVER_PERIOD_TYPE <> 0 AND A.RENEW = 1 THEN /*短期险可续保*/
		               +B.STD_PREM_AF
		              ELSE
		               0
		            END) + SUM((SELECT SUM(TR.NEW_STD_PREM_AF - TR.OLD_STD_PREM_AF)
		                          FROM APP___PAS__DBUSER.T_RENEW_CHANGE TR
		                         WHERE A.BUSI_PRD_ID = TR.OLD_BUSI_PRD_ID
		                           AND TR.VALID_STATUS = 3
		                           AND TR.CHANGE_ID = #{change_id})) /*转换后标准保费*/
		          ) SUM_STD_PREM
		
		  FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD A
		 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT B
		    ON A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
		 INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT TB
		    ON A.BUSI_PRD_ID = TB.BUSINESS_PRD_ID
		 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND C
		    ON B.POLICY_ID = C.POLICY_ID
		   AND B.BUSI_ITEM_ID = C.BUSI_ITEM_ID
		   AND B.ITEM_ID = C.ITEM_ID
		   AND B.POLICY_CODE= #{policy_code}
	 	]]>
	</select>
	<update id="JRQD_updateProductStdPremAfByAcceptCodeAndItemId" parameterType="java.util.Map">
		<![CDATA[
		update APP___PAS__DBUSER.t_cs_contract_product cp set cp.std_prem_af = #{std_prem_af},cp.amount=#{amount},cp.unit=#{unit},cp.extra_prem_af=#{unit},cp.total_prem_af =  #{total_prem_af},cp.operation_type = '2' where cp.log_id = (
			select ccp.log_id from APP___PAS__DBUSER.t_cs_contract_product ccp,APP___PAS__DBUSER.t_cs_accept_change cac,APP___PAS__DBUSER.t_cs_policy_change cpc
			where cac.accept_id = cpc.accept_id and cpc.policy_chg_id = ccp.policy_chg_id
			  and ccp.old_new = '1'
			  and ccp.item_id = #{item_id}
			  and cac.accept_code = #{accept_code})
		]]>
	</update>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_findContractProductInfoByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT DISTINCT
			B.VALIDATE_DATE,
			A.IS_WAIVED,
			A.POLICY_CODE,
			C.GUARANTEE_PERIOD_TYPE,
			C.GURNT_PAY_PERIOD,
			C.INSTALMENT_PROPORTION,
			C.PLAN_FREQ,
			C.PAY_PLAN_TYPE,
			B.BUSI_PROD_CODE,
			A.TOTAL_PREM_AF,
			A.UNIT,
			A.CHARGE_PERIOD,
			A.CHARGE_YEAR,
			A.PREM_FREQ,
			A.COVERAGE_PERIOD,
			A.COVERAGE_YEAR,
			A.AMOUNT,
			B.RENEW
			FROM DEV_PAS.T_CS_CONTRACT_PRODUCT A
			LEFT JOIN DEV_PAS.T_CS_CONTRACT_BUSI_PROD B
			ON A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			AND A.POLICY_CODE = B.POLICY_CODE
			LEFT JOIN DEV_PAS.T_PAY_PLAN C
			ON A.POLICY_CODE = C.POLICY_CODE
			AND C.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			WHERE A.OLD_NEW = '1' 
			AND B.OLD_NEW = '1'
			AND A.POLICY_CODE = #{policy_code}
			
	 	]]>
		<if test=" busi_prod_code != null and busi_prod_code != ''  ">
	       <![CDATA[ AND B.BUSI_PROD_CODE IN ]]>
	        <foreach collection="busi_prod_code" item="busi_prod_code"
		         index="index" open="(" close=")" separator=",">
		           #{busi_prod_code}
	       </foreach>
        </if>
	</select>
	
	
	<select id="JRQD_queryPremAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT SUM(NVL(TA.STD_PREM_AF, 0) + NVL(TB.EXTRA_PREM, 0)) AS STD_PREM_AF
				  FROM DEV_PAS.T_CS_CONTRACT_PRODUCT TA
				  LEFT JOIN DEV_PAS.T_CS_EXTRA_PREM TB
				    ON TA.POLICY_CHG_ID = TB.POLICY_CHG_ID
				   AND TA.ITEM_ID = TB.ITEM_ID
				   WHERE TA.POLICY_CHG_ID = #{policy_chg_id}
				   AND TA.BUSI_ITEM_ID = #{busi_item_id}
				   AND TA.OLD_NEW = #{old_new}
		   ]]>
	</select>
	
	<select id="JRQD_calcChargeYearFor928" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT (A.START_PAY_AGE - C.INSURED_AGE) as CHARGE_YEAR
			  FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A
			  LEFT JOIN APP___PAS__DBUSER.T_BENEFIT_INSURED B
			    ON A.POLICY_ID = B.POLICY_ID
			   AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
			  LEFT JOIN APP___PAS__DBUSER.T_INSURED_LIST C
			    ON B.INSURED_ID = C.LIST_ID
			 WHERE A.OLD_NEW = 1
			   AND A.POLICY_CHG_ID = #{policy_chg_id}
			   AND A.POLICY_ID = #{policy_id}
			   AND A.BUSI_ITEM_ID = #{busi_item_id}
			   AND A.ITEM_ID = #{item_id}								
		   ]]>
	</select>
	
	
	<select id="JRQD_findStdPremAfForBusiItemAndPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			        
 			SELECT SUM(B.STD_PREM_AF) AS STD_PREM_AF FROM (  
            SELECT CP.STD_PREM_AF
           FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD T,
                 DEV_PAS.T_CS_CONTRACT_PRODUCT CP
          WHERE T.MASTER_BUSI_ITEM_ID = #{busi_item_id}
            AND T.POLICY_CHG_ID = #{policy_chg_id}
            AND T.IS_WAIVED = 1
            AND T.WAIVER=0
            AND T.LIABILITY_STATE=#{liability_state}
            AND T.OLD_NEW = #{old_new}
            AND CP.POLICY_ID = T.POLICY_ID
            AND CP.BUSI_ITEM_ID = T.BUSI_ITEM_ID
            AND CP.POLICY_CHG_ID = T.POLICY_CHG_ID
         UNION
         SELECT CP.STD_PREM_AF
           FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD T,
                 DEV_PAS.T_CS_CONTRACT_PRODUCT   CP
          WHERE T.BUSI_ITEM_ID = #{busi_item_id}
            AND T.POLICY_CHG_ID = #{policy_chg_id}
            AND T.IS_WAIVED = 1
            AND T.LIABILITY_STATE =#{liability_state} 
            AND CP.OLD_NEW = #{old_new}
            AND CP.POLICY_ID = T.POLICY_ID
            AND CP.BUSI_ITEM_ID = T.BUSI_ITEM_ID
            AND CP.POLICY_CHG_ID = T.POLICY_CHG_ID) B							
		   ]]>
	</select>
	
	
</mapper>