<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.codeTable.dao.ICsBusinessProductDao">

	<sql id="JRQD_businessProductWhereCondition">
		<if test=" primary_sales_channel != null and primary_sales_channel != ''  "><![CDATA[ AND A.PRIMARY_SALES_CHANNEL = #{primary_sales_channel} ]]></if>
		<if test=" single_joint_life != null and single_joint_life != ''  "><![CDATA[ AND A.SINGLE_JOINT_LIFE = #{single_joint_life} ]]></if>
		<if test=" cover_period_type  != null "><![CDATA[ AND A.COVER_PERIOD_TYPE = #{cover_period_type} ]]></if>
		<if test=" product_category4 != null and product_category4 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY4 = #{product_category4} ]]></if>
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
		<if test=" product_category3 != null and product_category3 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY3 = #{product_category3} ]]></if>
		<if test=" product_code_original != null and product_code_original != ''  "><![CDATA[ AND A.PRODUCT_CODE_ORIGINAL = #{product_code_original} ]]></if>
		<if test=" product_category2 != null and product_category2 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY2 = #{product_category2} ]]></if>
		<if test=" product_category1 != null and product_category1 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY1 = #{product_category1} ]]></if>
		<if test=" product_name_sys != null and product_name_sys != ''  "><![CDATA[ AND A.PRODUCT_NAME_SYS = #{product_name_sys} ]]></if>
		<if test=" product_abbr_name != null and product_abbr_name != ''  "><![CDATA[ AND A.PRODUCT_ABBR_NAME = #{product_abbr_name} ]]></if>
		<if test=" product_static_code != null and product_static_code != ''  "><![CDATA[ AND A.PRODUCT_STATIC_CODE = #{product_static_code} ]]></if>
		<if test=" insured_count_max  != null "><![CDATA[ AND A.INSURED_COUNT_MAX = #{insured_count_max} ]]></if>
		<if test=" insured_count_min  != null "><![CDATA[ AND A.INSURED_COUNT_MIN = #{insured_count_min} ]]></if>
		<if test=" product_desc != null and product_desc != ''  "><![CDATA[ AND A.PRODUCT_DESC = #{product_desc} ]]></if>
		<if test=" release_date  != null  and  release_date  != ''  "><![CDATA[ AND A.RELEASE_DATE = #{release_date} ]]></if>
		<if test=" premium_rate_layer != null and premium_rate_layer != ''  "><![CDATA[ AND A.PREMIUM_RATE_LAYER = #{premium_rate_layer} ]]></if>
		<if test=" premium_currency != null and premium_currency != ''  "><![CDATA[ AND A.PREMIUM_CURRENCY = #{premium_currency} ]]></if>
		<if test=" product_name_std != null and product_name_std != ''  "><![CDATA[ AND A.PRODUCT_NAME_STD = #{product_name_std} ]]></if>
		<if test=" product_code_std != null and product_code_std != ''  "><![CDATA[ AND A.PRODUCT_CODE_STD = #{product_code_std} ]]></if>
		<if test=" renew_option != null and renew_option != ''  "><![CDATA[ AND A.RENEW_OPTION = #{renew_option} ]]></if>
		<if test=" product_category != null and product_category != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY = #{product_category} ]]></if>
		<if test=" schedule_rate  != null "><![CDATA[ AND A.SCHEDULE_RATE = #{schedule_rate} ]]></if>
		<if test=" product_code_sys != null and product_code_sys != ''  "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code_sys} ]]></if>
		<!-- add by shaocongwang -->
		<if test="product_code_sys_list  != null and product_code_sys_list.size()!=0">
			<![CDATA[ AND A.PRODUCT_CODE_SYS IN ]]>
				<foreach collection="product_code_sys_list" item="product_code_sys_list"
					index="index" open="(" close=")" separator=",">#{product_code_sys_list}</foreach>
		 </if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryBusinessProductByBusinessPrdIdCondition">
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
		<if test=" product_code_sys != null and product_code_sys != ''  "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code_sys} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addBusinessProduct"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_BUSINESS_PRODUCT(
				PRIMARY_SALES_CHANNEL, SINGLE_JOINT_LIFE, COVER_PERIOD_TYPE, PRODUCT_CATEGORY4, BUSINESS_PRD_ID, PRODUCT_CATEGORY3, PRODUCT_CODE_ORIGINAL, 
				PRODUCT_CATEGORY2, PRODUCT_CATEGORY1, PRODUCT_NAME_SYS, PRODUCT_ABBR_NAME, PRODUCT_STATIC_CODE, INSURED_COUNT_MAX, INSURED_COUNT_MIN, 
				PRODUCT_DESC, RELEASE_DATE, PREMIUM_RATE_LAYER, PREMIUM_CURRENCY, PRODUCT_NAME_STD, PRODUCT_CODE_STD, RENEW_OPTION, 
				PRODUCT_CATEGORY, SCHEDULE_RATE, PRODUCT_CODE_SYS ) 
			VALUES (
				#{primary_sales_channel, jdbcType=VARCHAR}, #{single_joint_life, jdbcType=VARCHAR} , #{cover_period_type, jdbcType=NUMERIC} , #{product_category4, jdbcType=VARCHAR} , #{business_prd_id, jdbcType=NUMERIC} , #{product_category3, jdbcType=VARCHAR} , #{product_code_original, jdbcType=VARCHAR} 
				, #{product_category2, jdbcType=VARCHAR} , #{product_category1, jdbcType=VARCHAR} , #{product_name_sys, jdbcType=VARCHAR} , #{product_abbr_name, jdbcType=VARCHAR} , #{product_static_code, jdbcType=VARCHAR} , #{insured_count_max, jdbcType=NUMERIC} , #{insured_count_min, jdbcType=NUMERIC} 
				, #{product_desc, jdbcType=VARCHAR} , #{release_date, jdbcType=DATE} , #{premium_rate_layer, jdbcType=VARCHAR} , #{premium_currency, jdbcType=VARCHAR} , #{product_name_std, jdbcType=VARCHAR} , #{product_code_std, jdbcType=VARCHAR} , #{renew_option, jdbcType=VARCHAR} 
				, #{product_category, jdbcType=VARCHAR} , #{schedule_rate, jdbcType=NUMERIC} , #{product_code_sys, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 修改操作 -->
	<update id="JRQD_updateBusinessProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_BUSINESS_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
			PRIMARY_SALES_CHANNEL = #{primary_sales_channel, jdbcType=VARCHAR} ,
			SINGLE_JOINT_LIFE = #{single_joint_life, jdbcType=VARCHAR} ,
		    COVER_PERIOD_TYPE = #{cover_period_type, jdbcType=NUMERIC} ,
			PRODUCT_CATEGORY4 = #{product_category4, jdbcType=VARCHAR} ,
		    BUSINESS_PRD_ID = #{business_prd_id, jdbcType=NUMERIC} ,
			PRODUCT_CATEGORY3 = #{product_category3, jdbcType=VARCHAR} ,
			PRODUCT_CODE_ORIGINAL = #{product_code_original, jdbcType=VARCHAR} ,
			PRODUCT_CATEGORY2 = #{product_category2, jdbcType=VARCHAR} ,
			PRODUCT_CATEGORY1 = #{product_category1, jdbcType=VARCHAR} ,
			PRODUCT_NAME_SYS = #{product_name_sys, jdbcType=VARCHAR} ,
			PRODUCT_ABBR_NAME = #{product_abbr_name, jdbcType=VARCHAR} ,
			PRODUCT_STATIC_CODE = #{product_static_code, jdbcType=VARCHAR} ,
		    INSURED_COUNT_MAX = #{insured_count_max, jdbcType=NUMERIC} ,
		    INSURED_COUNT_MIN = #{insured_count_min, jdbcType=NUMERIC} ,
			PRODUCT_DESC = #{product_desc, jdbcType=VARCHAR} ,
		    RELEASE_DATE = #{release_date, jdbcType=DATE} ,
			PREMIUM_RATE_LAYER = #{premium_rate_layer, jdbcType=VARCHAR} ,
			PREMIUM_CURRENCY = #{premium_currency, jdbcType=VARCHAR} ,
			PRODUCT_NAME_STD = #{product_name_std, jdbcType=VARCHAR} ,
			PRODUCT_CODE_STD = #{product_code_std, jdbcType=VARCHAR} ,
			RENEW_OPTION = #{renew_option, jdbcType=VARCHAR} ,
			PRODUCT_CATEGORY = #{product_category, jdbcType=VARCHAR} ,
		    SCHEDULE_RATE = #{schedule_rate, jdbcType=NUMERIC} ,
			PRODUCT_CODE_SYS = #{product_code_sys, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE BUSINESS_PRD_ID = #{business_prd_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_CS_findBusinessProductByBusinessPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RENEWAL_MAX_AGE,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS,A.WAIVER_FLAG
			 FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE ROWNUM = 1  ]]>
		<include refid="JRQD_queryBusinessProductByBusinessPrdIdCondition" />
		<![CDATA[ ORDER BY A.BUSINESS_PRD_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RENEWAL_MAX_AGE,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.BUSINESS_PRD_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_CS_findAllBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RENEWAL_MAX_AGE,A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_businessProductWhereCondition" />
		<![CDATA[ ORDER BY A.BUSINESS_PRD_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findBusinessProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryBusinessProductForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.RENEWAL_MAX_AGE, B.PRIMARY_SALES_CHANNEL, B.SINGLE_JOINT_LIFE, B.COVER_PERIOD_TYPE, B.PRODUCT_CATEGORY4, B.BUSINESS_PRD_ID, B.PRODUCT_CATEGORY3, B.PRODUCT_CODE_ORIGINAL, 
			B.PRODUCT_CATEGORY2, B.PRODUCT_CATEGORY1, B.PRODUCT_NAME_SYS, B.PRODUCT_ABBR_NAME, B.PRODUCT_STATIC_CODE, B.INSURED_COUNT_MAX, B.INSURED_COUNT_MIN, 
			B.PRODUCT_DESC, B.RELEASE_DATE, B.PREMIUM_RATE_LAYER, B.PREMIUM_CURRENCY, B.PRODUCT_NAME_STD, B.PRODUCT_CODE_STD, B.RENEW_OPTION, 
			B.PRODUCT_CATEGORY, B.SCHEDULE_RATE, B.PRODUCT_CODE_SYS FROM (
					SELECT ROWNUM RN,A.RENEWAL_MAX_AGE, A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.BUSINESS_PRD_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
