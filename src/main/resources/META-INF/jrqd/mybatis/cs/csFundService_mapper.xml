<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsFundServiceDao">
	<sql id="JRQD_fundServiceWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" target_id  != null and target_id !=''"><![CDATA[ AND A.TARGET_ID = #{target_id} ]]></if>
		<if test=" assign_unit != null and assign_unit != ''  "><![CDATA[ AND A.ASSIGN_UNIT = #{assign_unit} ]]></if>
		<if test=" product_id  != null and product_id !=''"><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" apply_time  != null  and  apply_time  != ''  "><![CDATA[ AND A.APPLY_TIME = #{apply_time} ]]></if>
		<if test=" item_id  != null and item_id !=''"><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" trans_charge_fee  != null and trans_charge_fee !=''"><![CDATA[ AND A.TRANS_CHARGE_FEE = #{trans_charge_fee} ]]></if>
		<if test=" apply_units  != null and apply_units !=''"><![CDATA[ AND A.APPLY_UNITS = #{apply_units} ]]></if>
		<if test=" sa_factor  != null and sa_factor!=''"><![CDATA[ AND A.SA_FACTOR = #{sa_factor} ]]></if>
		<if test=" switch_in_code != null and switch_in_code != ''  "><![CDATA[ AND A.SWITCH_IN_CODE = #{switch_in_code} ]]></if>
		<if test=" change_id  != null and change_id !=''"><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" apply_amount  != null and apply_amount !=''"><![CDATA[ AND A.APPLY_AMOUNT = #{apply_amount} ]]></if>
		<if test=" list_id  != null and list_id !=''"><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" terminal_interest  != null and terminal_interest!=''"><![CDATA[ AND A.TERMINAL_INTEREST = #{terminal_interest} ]]></if>
		<if test=" policy_chg_id  != null and policy_chg_id !=''"><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null and busi_item_id !=''"><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null and policy_id!=''"><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" distri_type != null and distri_type != ''  "><![CDATA[ AND A.DISTRI_TYPE = #{distri_type} ]]></if>
		<if test=" account_code != null and account_code != ''  "><![CDATA[ AND A.ACCOUNT_CODE = #{account_code} ]]></if>
		<if test=" validate_flag  != null and validate_flag !=''"><![CDATA[ AND A.VALIDATE_FLAG = #{validate_flag} ]]></if>
		<if test=" trans_code != null and trans_code != ''  "><![CDATA[ AND A.TRANS_CODE = #{trans_code} ]]></if>
		<if test=" fund_process_status != null and fund_process_status != ''"><![CDATA[ AND A.FUND_PROCESS_STATUS = #{fund_process_status} ]]></if>
		<if test=" accept_code != null and accept_code != ''"><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
	</sql>  
	
	<sql id="JRQD_addFundServiceCondition">
		<if test="trans_charge_fee  != null and trans_charge_fee != '' "><![CDATA[#{trans_charge_fee, jdbcType=NUMERIC}	,]]></if> 
		<if test="trans_charge_fee == null or trans_charge_fee == '' "><![CDATA[default,]]></if>
	   	<if test="terminal_interest != null and terminal_interest != '' "><![CDATA[#{terminal_interest, jdbcType=NUMERIC} ,]]></if> 
		<if test="terminal_interest == null or terminal_interest == '' "><![CDATA[default,]]></if>
	    <if test="apply_units  != null and apply_units != ''"><![CDATA[ #{apply_units, jdbcType=NUMERIC} ,]]></if>
		<if test="apply_units == null or apply_units == '' "><![CDATA[default,]]></if> 
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryFundServiceByListIdCondition">
		<if test=" list_id  != null and list_id != '' "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="JRQD_queryFundServiceByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addFundService"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_FUND_SERVICE__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_FUND_SERVICE(
				ACCEPT_CODE, MONEY_CODE, TARGET_ID, ASSIGN_UNIT, PRODUCT_ID, APPLY_TIME, ITEM_ID, TRANS_CHARGE_FEE, 
				TRANS_TYPE, APPLY_UNITS, INSERT_TIMESTAMP, UPDATE_BY, SA_FACTOR, SWITCH_IN_CODE, CHANGE_ID, 
				APPLY_AMOUNT, LIST_ID, TERMINAL_INTEREST, POLICY_CHG_ID, BUSI_ITEM_ID, POLICY_ID, DISTRI_TYPE, 
				INSERT_TIME, UPDATE_TIME, ACCOUNT_CODE, VALIDATE_FLAG, TRANS_CODE, UPDATE_TIMESTAMP, FUND_PROCESS_STATUS,TRANS_PROPORTION,PAY_WAY,
				INSERT_BY ) 
			VALUES (
				#{accept_code, jdbcType=VARCHAR}, #{money_code, jdbcType=VARCHAR}, #{target_id, jdbcType=NUMERIC} , #{assign_unit, jdbcType=VARCHAR} , #{product_id, jdbcType=NUMERIC} , #{apply_time, jdbcType=DATE} , #{item_id, jdbcType=NUMERIC} , #{trans_charge_fee, jdbcType=NUMERIC} 
				, #{trans_type, jdbcType=NUMERIC} , #{apply_units, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{sa_factor, jdbcType=NUMERIC} , #{switch_in_code, jdbcType=VARCHAR} , #{change_id, jdbcType=NUMERIC} 
				, #{apply_amount, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{terminal_interest, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{distri_type, jdbcType=VARCHAR} 
				, SYSDATE , SYSDATE , #{account_code, jdbcType=VARCHAR} , #{validate_flag, jdbcType=NUMERIC} , #{trans_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{fund_process_status, jdbcType=VARCHAR}, #{trans_proportion, jdbcType=NUMERIC},#{pay_way, jdbcType=VARCHAR} 
				, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>
<!-- 修改操作 -->
	<update id="JRQD_savePGMessage" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CONTRACT_INVEST ]]>
		<set>
		<trim suffixOverrides=",">
			 	INTEREST_CAPITAL = #{interest_capital,jdbcType = NUMERIC},	
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
	<!-- 修改操作 -->
	<update id="JRQD_updateUnitAfter" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT ]]>
		<set>
			<trim suffixOverrides=",">
				IS_PAUSE = #{is_pause, jdbcType=NUMERIC} ,
				PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code, jdbcType=VARCHAR} ,
				APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
				NORENEW_REASON = #{norenew_reason, jdbcType=VARCHAR} ,
				ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
				OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
				UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
				CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
				CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
				PAY_FREQ = #{pay_freq , jdbcType=VARCHAR},
				EXTRA_PREM_AF = #{extra_prem_af,
				jdbcType=NUMERIC} ,
				POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
				CC_SA = #{cc_sa, jdbcType=NUMERIC} ,
				OPERATION_TYPE =
				#{operation_type, jdbcType=VARCHAR} ,
				UPDATE_TIME = SYSDATE ,
				AMOUNT =
				#{amount, jdbcType=NUMERIC} ,
				EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
				PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
				LIABILITY_STATE =
				#{liability_state, jdbcType=NUMERIC} ,
				PAY_PERIOD = #{pay_period,
				jdbcType=VARCHAR} ,
				COUNT_WAY = #{count_way, jdbcType=VARCHAR} ,
				POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
				RERINSTATE_DATE =
				#{rerinstate_date, jdbcType=DATE} ,
				RENEW_DECISION =
				#{renew_decision, jdbcType=NUMERIC} ,
				VALIDATE_DATE =
				#{validate_date, jdbcType=DATE} ,
				UPDATE_TIMESTAMP =
				CURRENT_TIMESTAMP ,
				BENEFIT_LEVEL = #{benefit_level,
				jdbcType=VARCHAR} ,
				PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
				BONUS_SA = #{bonus_sa, jdbcType=NUMERIC} ,
				PRODUCT_CODE =
				#{product_code, jdbcType=VARCHAR} ,
				APPLY_DATE = #{apply_date,
				jdbcType=DATE} ,
				COVERAGE_PERIOD = #{coverage_period,
				jdbcType=VARCHAR} ,
				ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
				POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
				BUSI_ITEM_ID =
				#{busi_item_id, jdbcType=NUMERIC} ,
				HEALTH_SERVICE_FLAG =
				#{health_service_flag, jdbcType=NUMERIC} ,
				LAPSE_DATE = #{lapse_date,
				jdbcType=DATE} ,
				UNIT = #{unit, jdbcType=NUMERIC} ,
				COVERAGE_YEAR =
				#{coverage_year, jdbcType=NUMERIC} ,
				END_CAUSE = #{end_cause,
				jdbcType=VARCHAR} ,
				LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
				TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
				DECISION_CODE =
				#{decision_code, jdbcType=VARCHAR} ,
				PAUSE_DATE = #{pause_date,
				jdbcType=DATE} ,
				LOG_ID = #{log_id, jdbcType=NUMERIC} ,
				CHARGE_PERIOD
				= #{charge_period, jdbcType=VARCHAR} ,
				STD_PREM_AF = #{std_prem_af,
				jdbcType=NUMERIC} ,
				INTEREST_MODE = #{interest_mode, jdbcType=NUMERIC}
				,
				SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
				SUSPEND_CAUSE =
				#{suspend_cause, jdbcType=VARCHAR} ,
				PREM_FREQ = #{prem_freq,
				jdbcType=NUMERIC} ,
				IS_GIFT = #{is_gift, jdbcType=NUMERIC} ,
				INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af, jdbcType=NUMERIC} ,
				RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af, jdbcType=NUMERIC} ,
				INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af, jdbcType=NUMERIC} ,
				RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af, jdbcType=NUMERIC} ,
				DEDUCTIBLE_FRANCHISE =#{deductible_franchise, jdbcType=NUMERIC},
				PAYOUT_RATE =#{payout_rate, jdbcType=NUMERIC},
				IS_WAIVED =#{is_waived, jdbcType=NUMERIC} ,
				WAIVER_START =#{waiver_start, jdbcType=DATE} ,
				WAIVER_END = #{waiver_end, jdbcType=DATE},
				ADDITIONAL_PREM_AF=#{additional_prem_af, jdbcType=NUMERIC} ,
				APPEND_PREM_AF=#{append_prem_af, jdbcType=NUMERIC} ,
				PAIDUP_DATE=#{paidup_date, jdbcType=DATE} ,
				MATURITY_DATE=#{maturity_date, jdbcType=DATE},
				INITIAL_AMOUNT=#{initial_amount, jdbcType=NUMERIC},
				IS_MASTER_ITEM=#{is_master_item, jdbcType=NUMERIC}
			</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>
<!-- 查询基金名称 -->	
	<select id="JRQD_findFundName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      
		      SELECT  distinct B.FUND_NAME FROM APP___PAS__DBUSER.T_FUND B,APP___PAS__DBUSER.T_CONTRACT_INVEST CI
		       where B.FUND_CODE=CI.ACCOUNT_CODE AND CI.ACCOUNT_CODE= #{account_code} ]]>	
	</select>
		
	<select id="JRQD_findAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      
		     select b.accept_code from APP___PAS__DBUSER.t_cs_accept_change b  where b.accept_id = #{accept_id} ]]>	
	</select>
	<!-- 查询领取金额 -->	
	<select id="JRQD_findFeeAmountl" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      
		       select t.FEE_AMOUNT from  APP___PAS__DBUSER.T_CS_PREM_ARAP t where  t.arap_flag='2' and t.fee_type='P004300000' and t.business_code=#{business_code} ]]>	
	</select>
<!-- 删除操作 -->	
	<delete id="JRQD_deleteFundService" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE  A.LIST_ID = #{list_id}]]>	
	</delete>
<!-- 根据条件删除 -->
    <delete id="JRQD_deleteFundServiceByCondition" parameterType="java.util.Map">
          <![CDATA[DELETE FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE 1=1 ]]>
           <include refid="JRQD_fundServiceWhereCondition" />
    </delete>
<!-- 修改操作 -->
	<update id="JRQD_updateFundService" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_FUND_SERVICE ]]>
		<set>
		<trim suffixOverrides=",">
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    TARGET_ID = #{target_id, jdbcType=NUMERIC} ,
			ASSIGN_UNIT = #{assign_unit, jdbcType=VARCHAR} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
		    APPLY_TIME = #{apply_time, jdbcType=DATE} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    TRANS_CHARGE_FEE = #{trans_charge_fee, jdbcType=NUMERIC} ,
		    APPLY_UNITS = #{apply_units, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SA_FACTOR = #{sa_factor, jdbcType=NUMERIC} ,
			SWITCH_IN_CODE = #{switch_in_code, jdbcType=VARCHAR} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    APPLY_AMOUNT = #{apply_amount, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    TERMINAL_INTEREST = #{terminal_interest, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			DISTRI_TYPE = #{distri_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			ACCOUNT_CODE = #{account_code, jdbcType=VARCHAR} ,
		    VALIDATE_FLAG = #{validate_flag, jdbcType=NUMERIC} ,
			TRANS_CODE = #{trans_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			FUND_PROCESS_STATUS = #{fund_process_status, jdbcType=VARCHAR} ,
			TRANS_TYPE = #{trans_type, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findFundServiceByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, 
			A.APPLY_UNITS, A.SA_FACTOR, A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, 
			A.LIST_ID, A.TERMINAL_INTEREST, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, 
			A.ACCOUNT_CODE, A.VALIDATE_FLAG, A.TRANS_CODE, A.FUND_PROCESS_STATUS,A.TRANS_TYPE FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryFundServiceByListIdCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_CODE ]]>
	</select>
	<!-- 查询 朱丽华 -->
	<select id="JRQD_findFundService" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, 
			A.APPLY_UNITS, A.SA_FACTOR, A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, 
			A.LIST_ID, A.TERMINAL_INTEREST, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, 
			A.ACCOUNT_CODE, A.VALIDATE_FLAG, A.TRANS_CODE, A.FUND_PROCESS_STATUS,A.TRANS_TYPE FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE 1 = 1  ]]>
		<include refid="JRQD_fundServiceWhereCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_CODE ]]>
	</select>
	<!-- 查询 朱丽华 -->
	<select id="JRQD_findFundServiceByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, 
			A.APPLY_UNITS, A.SA_FACTOR, A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, 
			A.LIST_ID, A.TERMINAL_INTEREST, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, 
			A.ACCOUNT_CODE, A.VALIDATE_FLAG, A.TRANS_CODE, A.FUND_PROCESS_STATUS,A.TRANS_TYPE FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryFundServiceByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapFundService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, 
			A.APPLY_UNITS, A.SA_FACTOR, A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, 
			A.LIST_ID, A.TERMINAL_INTEREST, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, 
			A.ACCOUNT_CODE, A.VALIDATE_FLAG, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE ROWNUM <=  1000  ]]>
		 <include refid="JRQD_fundServiceWhereCondition" /> 
		<![CDATA[ ORDER BY A.ACCOUNT_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllFundService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, 
			A.APPLY_UNITS, A.SA_FACTOR, A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, 
			A.LIST_ID, A.TERMINAL_INTEREST, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, 
			A.ACCOUNT_CODE, A.VALIDATE_FLAG, A.TRANS_CODE, A.FUND_PROCESS_STATUS,A.TRANS_TYPE,A.TRANS_PROPORTION,A.PAY_WAY,A.UPDATE_TIME FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE ROWNUM <=  1000  ]]>
		 <include refid="JRQD_fundServiceWhereCondition" />
		<![CDATA[ ORDER BY A.ACCOUNT_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findFundServiceTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE 1 = 1  ]]>
		<include refid="JRQD_fundServiceWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryFundServiceForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCEPT_CODE, B.MONEY_CODE, B.TARGET_ID, B.ASSIGN_UNIT, B.PRODUCT_ID, B.APPLY_TIME, B.ITEM_ID, B.TRANS_CHARGE_FEE, 
			B.APPLY_UNITS, B.SA_FACTOR, B.SWITCH_IN_CODE, B.CHANGE_ID, B.APPLY_AMOUNT, 
			B.LIST_ID, B.TERMINAL_INTEREST, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID, B.DISTRI_TYPE, 
			B.ACCOUNT_CODE, B.VALIDATE_FLAG, B.TRANS_CODE, B.FUND_PROCESS_STATUS FROM (
					SELECT ROWNUM RN, A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, 
			A.APPLY_UNITS, A.SA_FACTOR, A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, 
			A.LIST_ID, A.TERMINAL_INTEREST, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, 
			A.ACCOUNT_CODE, A.VALIDATE_FLAG, A.TRANS_CODE, A.FUND_PROCESS_STATUS,A.TRANS_TYPE FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_fundServiceWhereCondition" /> 
		<![CDATA[ ORDER BY A.ACCOUNT_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 获取本次生效日期和下一个保单对应生效日之间部分领取次数 add by panmd_wb -->
	<select id="JRQD_getDrawTimes" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select count(1) from ( SELECT A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, 
      A.APPLY_UNITS, A.SA_FACTOR, A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, 
			A.LIST_ID, A.TERMINAL_INTEREST, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, 
			A.ACCOUNT_CODE, A.VALIDATE_FLAG, A.TRANS_CODE, A.FUND_PROCESS_STATUS FROM APP___PAS__DBUSER.T_FUND_SERVICE A 
      inner join   APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE b on a.change_id = b.change_id and b.accept_status='18'
      inner join APP___PAS__DBUSER.t_transaction_code c on a.trans_code = c.trans_code where 1=1 ]]>
	<if test="next_validate != null and next_validate != '' ">
      <![CDATA[  and a.apply_time <= #{next_validate}]]>
	</if>
	<if test="max_validate != null and max_validate != '' ">
      <![CDATA[  and a.apply_time >= #{max_validate} ]]>
	</if>
	 <![CDATA[ ) ]]>
</select>
	
	<select id="JRQD_findAllFundGroupByAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.policy_id,
       a.busi_item_id,
       a.change_id,
       a.policy_chg_id,
       a.account_code,
       a.product_id,
       sum(a.apply_amount) as apply_amount
  from APP___PAS__DBUSER.t_fund_service a WHERE 1 = 1  ]]>
		<include refid="JRQD_fundServiceWhereCondition" />
		<![CDATA[  group by a.policy_id,
          a.busi_item_id,
          a.change_id,
          a.policy_chg_id,
          a.account_code,
          a.product_id
          ORDER BY A.ACCOUNT_CODE ]]>
	</select>
	
	<select id="JRQD_findAllFundServiceAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select 
       sum(a.apply_amount) as apply_amount
  from APP___PAS__DBUSER.t_fund_service a WHERE 1 = 1  ]]>
		<include refid="JRQD_fundServiceWhereCondition" />
		<![CDATA[  and a.trans_code in ('13','31','30','32','33','34')]]>
	</select>
    
    <!-- 根据change_id删除数据 -->
    <delete id="JRQD_deleteFundServiceByChangeId" parameterType="java.util.Map">
          <![CDATA[DELETE FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE CHANGE_ID = #{change_id}]]>
    </delete>	
    
    <!-- 查询保单投连万能账户抄单表数据及保全基金交易历史表数据 -->
    <select id="JRQD_findCsFundServiceAndAccountCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, 
			A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, A.APPLY_UNITS, A.SA_FACTOR, 
			A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, A.LIST_ID, A.TERMINAL_INTEREST, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, C.ACCOUNT_CODE, A.VALIDATE_FLAG, 
			A.TRANS_CODE, A.FUND_PROCESS_STATUS,A.TRANS_TYPE,A.TRANS_PROPORTION,A.PAY_WAY,A.UPDATE_TIME 
			FROM APP___PAS__DBUSER.T_FUND_SERVICE A 
			LEFT JOIN APP___PAS__DBUSER.T_CS_CONTRACT_INVEST C 
			ON C.CHANGE_ID = A.CHANGE_ID
			AND C.POLICY_CHG_ID = A.POLICY_CHG_ID
			AND C.POLICY_ID = A.POLICY_ID
			WHERE ROWNUM <=  1000  ]]>
		 <include refid="JRQD_fundServiceWhereCondition" />
	</select>
	
	<select id="JRQD_queryFundServiceFor928" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT DISTINCT A.POLICY_PERIOD, /**累计交费期数*/
			      (SELECT A.POLICY_PERIOD - TE.POLICY_PERIOD
	                   FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND TE
	                  WHERE TE.POLICY_CHG_ID = A.POLICY_CHG_ID
	                    AND TE.POLICY_ID = A.POLICY_ID
	                    AND TE.BUSI_ITEM_ID = A.BUSI_ITEM_ID
	                    AND TE.ITEM_ID = A.ITEM_ID
	                    AND TE.OLD_NEW = '0') AS PAY_TIMES, /*补交保费期数*/
			       (SELECT SUM(T.APPLY_AMOUNT)
			          FROM APP___PAS__DBUSER.T_FUND_SERVICE T
			         WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID
			           AND T.POLICY_ID = A.POLICY_ID
			           AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			           AND T.TRANS_CODE IN ('55')) AS PREM_TOTAL, /*补交保费金额*/
			       
			       (SELECT SUM(T.APPLY_AMOUNT)
			          FROM APP___PAS__DBUSER.T_FUND_SERVICE T
			         WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID
			           AND T.POLICY_ID = A.POLICY_ID
			           AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			           AND T.TRANS_CODE IN ('54')) AS IRREGULAR_PREM, /**不定期交保费*/
			           
			        (SELECT SUM(T.APPLY_AMOUNT)
			          FROM APP___PAS__DBUSER.T_FUND_SERVICE T
			         WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID
			           AND T.POLICY_ID = A.POLICY_ID
			           AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			           AND T.TRANS_CODE IN ('56')) AS COST_FEE, /**初始费用*/
			           
			       (SELECT SUM(T.APPLY_AMOUNT)
			          FROM APP___PAS__DBUSER.T_FUND_SERVICE T
			         WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID
			           AND T.POLICY_ID = A.POLICY_ID
			           AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			           AND T.TRANS_CODE IN ('49', '51')
			           AND T.ACCOUNT_CODE in ('928000','928100')) AS ADD_PREM1, /**进入稳健回报型投资组合的保险费*/
			       
			       (SELECT SUM(T.APPLY_AMOUNT)
			          FROM APP___PAS__DBUSER.T_FUND_SERVICE T
			         WHERE T.POLICY_CHG_ID = A.POLICY_CHG_ID
			           AND T.POLICY_ID = A.POLICY_ID
			           AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			           AND T.TRANS_CODE IN ('49', '51')
			           AND T.ACCOUNT_CODE in ('928001','928101')) AS ADD_PREM2 /**进入积极进取型投资组合时保险费*/  
			            
			  FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A
			 WHERE A.POLICY_CHG_ID = #{policy_chg_id}
			   AND A.POLICY_ID = #{policy_id}
			   AND A.BUSI_ITEM_ID = #{busi_item_id}
			   AND A.OLD_NEW = 1			  
		 ]]>
	</select>
    
    
    <select id="JRQD_queryAllFundService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCEPT_CODE, A.MONEY_CODE, A.TARGET_ID, A.ASSIGN_UNIT, A.PRODUCT_ID, A.APPLY_TIME, A.ITEM_ID, A.TRANS_CHARGE_FEE, 
			A.APPLY_UNITS, A.SA_FACTOR, A.SWITCH_IN_CODE, A.CHANGE_ID, A.APPLY_AMOUNT, 
			A.LIST_ID, A.TERMINAL_INTEREST, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID, A.DISTRI_TYPE, 
			A.ACCOUNT_CODE, A.VALIDATE_FLAG, A.TRANS_CODE, A.FUND_PROCESS_STATUS,A.TRANS_TYPE,A.TRANS_PROPORTION,A.PAY_WAY,A.UPDATE_TIME FROM APP___PAS__DBUSER.T_FUND_SERVICE A WHERE ROWNUM <=  1000  ]]>
		 <include refid="JRQD_fundServiceWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
    
    
    
    <select id="JRQD_queryAmountGroupByTransCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.TRANS_CODE, SUM(A.APPLY_AMOUNT) AS APPLY_AMOUNT
				  FROM DEV_PAS.T_FUND_SERVICE A
				 WHERE A.ACCEPT_CODE = #{accept_code}
				 	 AND A.POLICY_CHG_ID = #{policy_chg_id}
					 AND A.POLICY_ID = #{policy_id}							 
				 GROUP BY A.TRANS_CODE										
		 ]]>
	 </select>
		 
</mapper>
