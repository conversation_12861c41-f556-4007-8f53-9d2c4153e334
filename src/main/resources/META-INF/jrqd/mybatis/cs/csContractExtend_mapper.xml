<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.object.dao.ICsContractExtendDao">
	<sql id="JRQD_csContractExtendWhereCondition">
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" extraction_due_date  != null  and  extraction_due_date  != ''  "><![CDATA[ AND A.EXTRACTION_DUE_DATE = #{extraction_due_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" billing_date  != null  and  billing_date  != ''  "><![CDATA[ AND A.BILLING_DATE = #{billing_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_period  != null "><![CDATA[ AND A.POLICY_PERIOD = #{policy_period} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" prem_status != null and prem_status != ''  "><![CDATA[ AND A.PREM_STATUS = #{prem_status} ]]></if>
	</sql>


	<!-- 按索引生成的查询条件 -->
	<sql id="JRQD_queryCsContractExtendByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>
	<sql id="JRQD_queryCsContractExtendByOldNewCondition">
		<if test=" old_new != null and old_new != '' "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
	</sql>
	<sql id="JRQD_queryCsContractExtendByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>

	<!-- 添加操作 -->
	<insert id="JRQD_addCsContractExtend" useGeneratedKeys="false"
		parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CONTRACT_EXTEND.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND(
				INSERT_TIME, POLICY_YEAR, OPERATION_TYPE, EXTRACTION_DUE_DATE, UPDATE_TIME, ITEM_ID, BILLING_DATE, 
				INSERT_TIMESTAMP, LOG_ID, ORGAN_CODE, OLD_NEW, POLICY_CODE, UPDATE_BY, POLICY_PERIOD, 
				CHANGE_ID, LIST_ID, PAY_DUE_DATE, UPDATE_TIMESTAMP, POLICY_CHG_ID, INSERT_BY, 
				PREM_STATUS,BUSI_ITEM_ID, POLICY_ID, NEXT_PREM) 
			VALUES (
				SYSDATE, #{policy_year, jdbcType=NUMERIC} , #{operation_type, jdbcType=VARCHAR} , #{extraction_due_date, jdbcType=DATE} , SYSDATE , #{item_id, jdbcType=NUMERIC} , #{billing_date, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{log_id,jdbcType=NUMERIC} , #{organ_code, jdbcType=VARCHAR} , #{old_new, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{policy_period, jdbcType=NUMERIC} 
				, #{change_id, jdbcType=NUMERIC} ,#{list_id,jdbcType=NUMERIC} , #{pay_due_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} ,]]>
		<if test="prem_status != null and prem_status != ''  "><![CDATA[ #{prem_status, jdbcType=NUMERIC},]]>
		</if>
		<if test="prem_status == null or prem_status == ''  "><![CDATA[ default,]]></if>
				<![CDATA[  #{busi_item_id, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC},#{next_prem, jdbcType=NUMERIC}  )]]>
	</insert>

	<!-- 删除操作 -->
	<delete id="JRQD_deleteCsContractExtend" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND WHERE  LOG_ID = #{log_id} ]]>
	</delete>

	<!-- 删除操作 -->
	<delete id="JRQD_deleteCsContractExtendbyPolicyChgId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND WHERE  POLICY_CHG_ID = #{policy_chg_id} ]]>
	</delete>

	<delete id="JRQD_deleteCsContractExtendByCondition" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE  1 = 1 ]]>
		<include refid="JRQD_csContractExtendWhereCondition" />
	</delete>
	
	<!-- 修改操作 -->
	<update id="JRQD_updateCsContractExtend" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
				OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
				EXTRACTION_DUE_DATE = #{extraction_due_date, jdbcType=DATE} ,
				UPDATE_TIME = SYSDATE ,
				ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
				BILLING_DATE = #{billing_date, jdbcType=DATE} ,
				LOG_ID = #{log_id, jdbcType=NUMERIC} ,
				ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
				OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
				POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
				POLICY_PERIOD = #{policy_period, jdbcType=NUMERIC} ,
				CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
				LIST_ID = #{list_id, jdbcType=NUMERIC} ,
				PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
				BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
				POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
				PREM_STATUS = #{prem_status, jdbcType=NUMERIC} ,
				NEXT_PREM = #{next_prem, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE  LOG_ID = #{log_id, jdbcType=NUMERIC} ]]>
	</update>

	<!-- 可更新'下期应缴费金额'字段的更新处理 add by liuhk_wb START -->
	<!-- 修改操作 -->
	<update id="JRQD_updateCsContractExtendNextPrem" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND ]]>
		<set>
			<trim suffixOverrides=",">
				POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
				OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
				EXTRACTION_DUE_DATE = #{extraction_due_date, jdbcType=DATE} ,
				UPDATE_TIME = SYSDATE ,
				ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
				BILLING_DATE = #{billing_date, jdbcType=DATE} ,
				LOG_ID = #{log_id, jdbcType=NUMERIC} ,
				ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
				OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
				POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
				POLICY_PERIOD = #{policy_period, jdbcType=NUMERIC} ,
				CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
				LIST_ID = #{list_id, jdbcType=NUMERIC} ,
				PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
				UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
				POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
				BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
				POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
				PREM_STATUS = #{prem_status, jdbcType=NUMERIC} ,
				NEXT_PREM = #{next_prem, jdbcType=NUMERIC} ,
			</trim>
		</set>
		<![CDATA[ WHERE  LOG_ID = #{log_id, jdbcType=NUMERIC} ]]>
	</update>
	<!-- 可更新'下期应缴费金额'字段的更新处理 add by liuhk_wb END -->

<!-- 修改操作 -->
	<update id="JRQD_updateCsContractExtends" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND ]]>
		<set>
			<trim suffixOverrides=",">
				OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
				OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
				PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
			</trim>
		</set>
		<![CDATA[ WHERE  LOG_ID = #{log_id, jdbcType=NUMERIC} ]]>
	</update>
	<!-- 按索引查询操作 -->
	<select id="JRQD_findCsContractExtendByPolicyChgId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.OPERATION_TYPE, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.LOG_ID, A.ORGAN_CODE, A.OLD_NEW, A.POLICY_CODE, A.POLICY_PERIOD, 
			A.CHANGE_ID, A.LIST_ID, A.PAY_DUE_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE ROWNUM <= 1 ]]>
		<include refid="JRQD_queryCsContractExtendByPolicyChgIdCondition" />
	</select>

	<select id="JRQD_findCsContractExtendByOldNew" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.OPERATION_TYPE, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.LOG_ID, A.ORGAN_CODE, A.OLD_NEW, A.POLICY_CODE, A.POLICY_PERIOD, 
			A.CHANGE_ID, A.LIST_ID, A.PAY_DUE_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsContractExtendByOldNewCondition" />
	</select>

	<select id="JRQD_findCsContractExtendByLogId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.OPERATION_TYPE, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.LOG_ID, A.ORGAN_CODE, A.OLD_NEW, A.POLICY_CODE, A.POLICY_PERIOD, 
			A.CHANGE_ID, A.LIST_ID, A.PAY_DUE_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsContractExtendByLogIdCondition" />
	</select>


	<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsContractExtend" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.OPERATION_TYPE, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.LOG_ID, A.ORGAN_CODE, A.OLD_NEW, A.POLICY_CODE, A.POLICY_PERIOD, 
			A.CHANGE_ID, A.LIST_ID, A.PAY_DUE_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

	<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsContractExtend" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.OPERATION_TYPE, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.LOG_ID, A.ORGAN_CODE, A.OLD_NEW, A.POLICY_CODE, A.POLICY_PERIOD, 
			A.CHANGE_ID, A.LIST_ID, A.PAY_DUE_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csContractExtendWhereCondition" />
		<![CDATA[ ORDER BY A.PAY_DUE_DATE ASC  ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="JRQD_findCsContractExtendTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="JRQD_queryCsContractExtendForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.POLICY_YEAR, B.OPERATION_TYPE, B.EXTRACTION_DUE_DATE, B.ITEM_ID, B.BILLING_DATE, 
			B.LOG_ID, B.ORGAN_CODE, B.OLD_NEW, B.POLICY_CODE, B.POLICY_PERIOD, 
			B.CHANGE_ID, B.LIST_ID, B.PAY_DUE_DATE, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, 
			B.POLICY_ID, B.PREM_STATUS FROM (
					SELECT ROWNUM RN, A.POLICY_YEAR, A.OPERATION_TYPE, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.LOG_ID, A.ORGAN_CODE, A.OLD_NEW, A.POLICY_CODE, A.POLICY_PERIOD, 
			A.CHANGE_ID, A.LIST_ID, A.PAY_DUE_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PREM_STATUS FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 修改下次缴费日 -->
	<update id="JRQD_updateExtendPayDueDate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND ]]>
		<set>
			<trim suffixOverrides=",">
				UPDATE_TIME = SYSDATE ,
				PAY_DUE_DATE =
				#{pay_due_date, jdbcType=DATE}
			</trim>
		</set> 
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

	<!--yuzw 查询单条数据 -->
	<select id="JRQD_findCsContractExtend" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.OPERATION_TYPE, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.LOG_ID, A.ORGAN_CODE, A.OLD_NEW, A.POLICY_CODE, A.POLICY_PERIOD, 
			A.CHANGE_ID, A.LIST_ID, A.PAY_DUE_DATE, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="JRQD_csContractExtendWhereCondition" />
	</select>
	
	<!-- 根据changeid删除操作 -->
	<delete id="JRQD_deleteCsContractExtendbyChangeId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND WHERE  CHANGE_ID = #{change_id} ]]>
	</delete>
	
	
	<select id="JRQD_queryNextPayDueDate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT MIN(A.PAY_DUE_DATE) AS PAY_DUE_DATE  
					 FROM APP___PAS__DBUSER.T_CS_CONTRACT_EXTEND A 
				WHERE  A.POLICY_CHG_ID = #{policy_chg_id}
					   AND A.POLICY_ID = #{policy_id}
					   AND A.BUSI_ITEM_ID = #{busi_item_id}
					   AND A.OLD_NEW = #{old_new} 
	     ]]>	
	</select>
	
</mapper>
