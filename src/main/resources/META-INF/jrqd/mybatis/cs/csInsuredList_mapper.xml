<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.object.dao.ICsInsuredListDao">
	<sql id="JRQD_csInsuredListWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" insured_age  != null "><![CDATA[ AND A.INSURED_AGE = #{insured_age} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" stand_life  != null "><![CDATA[ AND A.STAND_LIFE = #{stand_life} ]]></if>
	<!-- <if test=" relation_to_insured_1 != null and relation_to_insured_1 != ''  "><![CDATA[ AND D.RELATION_TO_INSURED_1 = #{relation_to_insured_1} ]]></if>  -->	
		<if test=" smoking  != null "><![CDATA[ AND A.SMOKING = #{smoking} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" JOB_UNDERWRITE != null and JOB_UNDERWRITE != ''  "><![CDATA[ AND A.JOB_UNDERWRITE = #{JOB_UNDERWRITE} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
<!-- 		<IF TEST=" BUSIITEMID != NULL AND BUSIITEMID != ''  "><![CDATA[ AND D.BUSI_ITEM_ID = #{BUSIITEMID} ]]></IF> -->
		<if test="policy_change_list != null and policy_change_list.size()!=0">
		<![CDATA[ AND A.POLICY_CHG_ID IN (]]>
		<foreach collection="policy_change_list" item="policyChaId"
			index="index" open="" close="" separator=",">#{policyChaId}</foreach>
		<![CDATA[)]]>
	    </if>
	    <!-- #108297被保人表同步增加字段 -->
	    <if test=" agent_relation != null and agent_relation != ''  "><![CDATA[ AND A.AGENT_RELATION = #{agent_relation} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsInsuredListByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsInsuredListByOldNewCondition">
		<if test=" old_new != null and old_new != '' "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsInsuredListByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addCsInsuredList"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_INSURED_LIST.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_INSURED_LIST(
				ADDRESS_ID, CUSTOMER_ID, CUSTOMER_HEIGHT, RELATION_TO_PH, APPLY_CODE, INSERT_TIMESTAMP, OLD_NEW, 
				INSURED_AGE, UPDATE_BY, CHANGE_ID, LIST_ID, POLICY_CHG_ID, POLICY_ID, STAND_LIFE, SMOKING, INSERT_TIME, OPERATION_TYPE, JOB_UNDERWRITE, JOB_CODE, UPDATE_TIME, 
				CUSTOMER_WEIGHT, LOG_ID, POLICY_CODE, UPDATE_TIMESTAMP, INSERT_BY ,SOCI_SECU,
				ANNUAL_INCOME_CEIL,INCOME_SOURCE,RESIDENT_TYPE,AGENT_RELATION) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{customer_id, jdbcType=NUMERIC} , #{customer_height, jdbcType=NUMERIC} , #{relation_to_ph, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{old_new, jdbcType=VARCHAR} 
				, #{insured_age, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{stand_life, jdbcType=NUMERIC} 
				, #{smoking, jdbcType=NUMERIC} , SYSDATE , #{operation_type, jdbcType=VARCHAR} , #{job_underwrite, jdbcType=VARCHAR} , #{job_code, jdbcType=VARCHAR} , SYSDATE 
				, #{customer_weight, jdbcType=NUMERIC} , #{log_id,jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC},#{soci_secu, jdbcType=NUMERIC}
				, #{annual_income_ceil, jdbcType=NUMERIC}, #{income_source, jdbcType=VARCHAR}, #{resident_type, jdbcType=VARCHAR} ,#{agent_relation, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsInsuredList" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_INSURED_LIST WHERE LOG_ID = #{log_id} ]]>
	</delete>
<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsInsuredListByPolicyChgId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_INSURED_LIST WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</delete>
<!-- 修改操作 -->
	<update id="JRQD_updateCsInsuredList" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_INSURED_LIST ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
			RELATION_TO_PH = #{relation_to_ph, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
		    INSURED_AGE = #{insured_age, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    STAND_LIFE = #{stand_life, jdbcType=NUMERIC} ,
		    SMOKING = #{smoking, jdbcType=NUMERIC} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
			JOB_UNDERWRITE = #{job_underwrite, jdbcType=VARCHAR} ,
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SOCI_SECU=#{soci_secu, jdbcType=NUMERIC},
		    COMM_METHOD=#{comm_method, jdbcType=VARCHAR},
		    ANNUAL_INCOME_CEIL = #{annual_income_ceil, jdbcType=NUMERIC},
			INCOME_SOURCE = #{income_source, jdbcType=VARCHAR},
			RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR},
			AGENT_RELATION = #{agent_relation, jdbcType=VARCHAR},
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsInsuredListByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT null as ANNUAL_INCOME_CEIL,null as INCOME_SOURCE,'1' as RESIDENT_TYPE,
			A.SOCI_SECU,A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.OLD_NEW, 
			A.INSURED_AGE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.STAND_LIFE, 
			A.SMOKING, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.LOG_ID, A.POLICY_CODE, null as AGENT_RELATION FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A  WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_queryCsInsuredListByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT null as ANNUAL_INCOME_CEIL,null as INCOME_SOURCE,'1' as RESIDENT_TYPE,
			A.SOCI_SECU,A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.OLD_NEW, 
			A.INSURED_AGE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.STAND_LIFE, 
			A.SMOKING, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.LOG_ID, A.POLICY_CODE, null as AGENT_RELATION FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A  WHERE ROWNUM <=  1  ]]>
		<include refid="JRQD_csInsuredListWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	<!-- 查询最新一条保全的投保年龄   -->
	<select id="JRQD_findCsInsuredList1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT H.INSURED_AGE FROM(SELECT * FROM APP___PAS__DBUSER.T_CS_INSURED_LIST  A WHERE 
		   A.customer_id=#{customer_id} AND A.OLD_NEW='1'AND A.OPERATION_TYPE='2'
		   ORDER BY A.UPDATE_TIME DESC) H WHERE ROWNUM=1]]>
	</select>
	
	<select id="JRQD_findCsInsuredListByOldNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT null as ANNUAL_INCOME_CEIL,null as INCOME_SOURCE,'1' as RESIDENT_TYPE,
			A.SOCI_SECU,A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.OLD_NEW, 
			A.INSURED_AGE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.STAND_LIFE, 
			A.SMOKING, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.LOG_ID, A.POLICY_CODE, null as AGENT_RELATION FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A  WHERE ROWNUM <=  1000   ]]>
		<include refid="JRQD_queryCsInsuredListByOldNewCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsInsuredListByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT null as ANNUAL_INCOME_CEIL,null as INCOME_SOURCE,'1' as RESIDENT_TYPE,
			A.SOCI_SECU,A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.OLD_NEW, 
			A.INSURED_AGE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.STAND_LIFE, 
			A.SMOKING, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.LOG_ID, A.POLICY_CODE, null as AGENT_RELATION FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A  WHERE ROWNUM <=  1   ]]>
		<include refid="JRQD_queryCsInsuredListByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT null as ANNUAL_INCOME_CEIL,null as INCOME_SOURCE,'1' as RESIDENT_TYPE,
			A.SOCI_SECU,A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.OLD_NEW, 
			A.INSURED_AGE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.STAND_LIFE, 
			A.SMOKING, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.LOG_ID, A.POLICY_CODE, null as AGENT_RELATION FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A  WHERE 1=1  AND ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
SELECT null as ANNUAL_INCOME_CEIL,null as INCOME_SOURCE,'1' as RESIDENT_TYPE,
			A.SOCI_SECU,A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.OLD_NEW, 
			A.INSURED_AGE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.STAND_LIFE, 
			A.SMOKING, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.LOG_ID, A.POLICY_CODE, A.RELATION_TO_PH,a.comm_method, null as AGENT_RELATION FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A WHERE 1=1  AND ROWNUM <=  1000  ]]>
		<include refid="JRQD_csInsuredListWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findCsInsuredListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="JRQD_csInsuredListWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCsInsuredListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, null as ANNUAL_INCOME_CEIL,null as INCOME_SOURCE,'1' as RESIDENT_TYPE,
			B.SOCI_SECU,B.ADDRESS_ID, B.CUSTOMER_ID, B.CUSTOMER_HEIGHT, B.RELATION_TO_PH, B.APPLY_CODE, B.OLD_NEW, 
			B.INSURED_AGE, B.CHANGE_ID, B.LIST_ID, B.POLICY_CHG_ID, B.POLICY_ID, B.STAND_LIFE, 
			B.SMOKING, B.OPERATION_TYPE, B.JOB_UNDERWRITE, B.JOB_CODE, 
			B.CUSTOMER_WEIGHT, B.LOG_ID, B.POLICY_CODE, null as AGENT_RELATION FROM (
					SELECT ROWNUM RN, null as ANNUAL_INCOME_CEIL,null as INCOME_SOURCE,'1' as RESIDENT_TYPE,
			A.SOCI_SECU,A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.OLD_NEW, 
			A.INSURED_AGE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.STAND_LIFE, 
			A.SMOKING, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.LOG_ID, A.POLICY_CODE, null as AGENT_RELATION FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A WHERE 1=1  AND ROWNUM <= #{LESS_NUM}  ]]>
		<include refid="JRQD_csInsuredListWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<update id="JRQD_updateInsuredAge" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_INSURED_LIST ]]>
		<set>
		<trim suffixOverrides=",">
		    INSURED_AGE = #{insured_age, jdbcType=NUMERIC}
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>
	<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsInsuredListByChgID" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_INSURED_LIST WHERE OLD_NEW = #{old_new, jdbcType=VARCHAR} AND CHANGE_ID = #{change_id, jdbcType=NUMERIC} ]]>
	</delete>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_findCsInsuredLists" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			select cc.un_customer_code,
			       cc.customer_name,
			       cil.soci_secu,
			       cbi.order_id,
			       cbi.relation_to_insured_1,
			       cc.customer_gender,
			       cil.insured_age customer_age,
			       cc.customer_birthday,
			       cc.customer_cert_type,
			       cc.customer_certi_code,
			       cc.customer_height,
			       cc.customer_weight,
			       (select jc.job_name from APP___PAS__DBUSER.T_job_code jc where jc.job_code = cc.job_code) job_name,
             	   (select jc.job_uw_level from APP___PAS__DBUSER.T_job_code jc where jc.job_code = cc.job_code) as occupation_type,
			       cc.job_code,
			       cil.relation_to_ph,
			       '',
			       cil.customer_id,
			       cil.list_id,
			       cc.company_name as company_name
			  from APP___PAS__DBUSER.T_cs_benefit_insured cbi
			 inner join APP___PAS__DBUSER.T_cs_insured_list cil
			    on cbi.insured_id = cil.list_id
			   and cbi.change_id = cil.change_id
			   and cbi.policy_chg_id = cil.policy_chg_id
			   and cbi.policy_id = cil.policy_id
			   and cbi.old_new = cil.old_new
			  left join APP___PAS__DBUSER.T_cs_customer cc
			    on cil.customer_id = cc.customer_id
			 where 1=1 
		 ]]>
		 <if test="busi_item_id != null"><![CDATA[ and cbi.busi_item_id = #{busi_item_id}]]></if>
		 <if test="old_new != null and old_new != ''"><![CDATA[ and cbi.old_new = #{old_new}]]></if>
		 <if test="change_id != null"><![CDATA[ and cbi.change_id = #{change_id}]]></if>
		 <if test="policy_chg_id != null"><![CDATA[ and cbi.policy_chg_id = #{policy_chg_id}]]></if>
		 <if test="old_new != null"><![CDATA[ and cc.old_new = #{old_new}]]></if>
		 <if test="accept_id != null"><![CDATA[ and cc.accept_id = #{accept_id}]]></if>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_findInsuredLists" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			select cil.soci_secu ,cc.un_customer_code,
             cc.customer_name,
             cbi.order_id,
             cbi.relation_to_insured_1,
             cc.customer_gender,
             cil.insured_age customer_age,
             cc.customer_birthday,
             cc.customer_cert_type,
             cc.customer_certi_code,
             cc.customer_height,
             cc.customer_weight,
             (select jc.job_name from APP___PAS__DBUSER.T_job_code jc where jc.job_code = cc.job_code) job_name,
             (select jc.job_uw_level from APP___PAS__DBUSER.T_job_code jc where jc.job_code = cc.job_code) as occupation_type,
             cc.job_code,
             cil.relation_to_ph,
             cil.soci_secu  as social_security,
             cil.customer_id,
             cil.list_id,
             cc.company_name as company_name
        from APP___PAS__DBUSER.T_benefit_insured cbi
       inner join APP___PAS__DBUSER.T_insured_list cil
          on cbi.insured_id = cil.list_id     
         and cbi.policy_id = cil.policy_id
        left join APP___PAS__DBUSER.T_customer cc
          on cil.customer_id = cc.customer_id
       where 1=1 
		 ]]>
		 <if test="busi_item_id != null"><![CDATA[ and cbi.busi_item_id = #{busi_item_id}]]></if>
		 <if test="policy_id != null"><![CDATA[ and cbi.policy_id = #{policy_id} and cil.policy_id = #{policy_id}]]></if>
	</select>
	
	<!-- 根据保单号查询所有被保人id -->
	<select id="JRQD_findAllCustomerIdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select list_id from APP___PAS__DBUSER.T_CS_INSURED_LIST 
			where policy_code=#{policy_code} 
			group by policy_code,list_id
		]]>	
	</select>
	<!-- 查询个数操作 -->
	<select id="JRQD_findCsPolicyAccountTotalByCustomeId" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select count(1) from (select distinct a.policy_id from  APP___PAS__DBUSER.T_POLICY_ACCOUNT a,dev_pas.t_benefit_insured b, dev_pas.t_INSURED_LIST c
            where a.policy_id=b.policy_id and a.busi_item_id=b.busi_item_id and c.list_id=b.insured_id and a.account_type='11' 
            AND c.CUSTOMER_ID =#{customer_id} and c.POLICY_ID =#{policy_id}
            union
            select distinct a.policy_id from  APP___PAS__DBUSER.T_POLICY_ACCOUNT a, dev_pas.t_POLICY_HOLDER b
                  where a.policy_id=b.policy_id and a.account_type='2'  
                  AND b.CUSTOMER_ID =#{customer_id} and b.POLICY_ID =#{policy_id}) t
            
            
            ]]>
	
	</select>
	
	
	<!--by zhaoyoan_wb 根据保单号和客户姓名查询被保人id -->
	<select id="JRQD_findCustomerIdByPolicyCodeAndName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,A.CUSTOMER_ID
			FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER B ON A.CUSTOMER_ID=B.CUSTOMER_ID
			WHERE A.POLICY_CODE=#{policy_code,jdbcType=VARCHAR} 
			AND B.CUSTOMER_NAME=#{customer_name,jdbcType=VARCHAR} 
			GROUP BY A.POLICY_CODE,A.CUSTOMER_ID
		]]>	
	</select>
	
	<!-- 根据保单号查询所有被保人id -->
	<select id="JRQD_findInsuredsByPolicyCodeAndCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select 
				i.job_code as occupationcode,
				(select j.job_name from APP___PAS__DBUSER.T_job_code j where j.job_code = i.job_code) as occupationcodename,
				(select j.job_category from APP___PAS__DBUSER.T_job_code j,APP___PAS__DBUSER.T_job_category c where j.job_code = i.job_code and j.job_category = c.job_category_code) as occupationtype,
				(select c.job_category_name from APP___PAS__DBUSER.T_job_code j,APP___PAS__DBUSER.T_job_category c where j.job_code = i.job_code and j.job_category = c.job_category_code) occupationtypename,
				(select bp.busi_prod_code from APP___PAS__DBUSER.T_contract_busi_prod bp , APP___PAS__DBUSER.T_benefit_insured bi where bp.busi_item_id = bi.busi_item_id 
				and bp.policy_id = i.policy_id and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id and rownum=1) as polno,
				(select pro.product_name_std from APP___PAS__DBUSER.T_contract_busi_prod bp ,APP___PAS__DBUSER.T_business_product pro, APP___PAS__DBUSER.T_benefit_insured bi where bp.busi_item_id = bi.busi_item_id 
				and bp.policy_id = i.policy_id and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id and pro.business_prd_id = bp.busi_prd_id and rownum=1) as riskcodename,
				(select sum(cp.amount) from APP___PAS__DBUSER.T_contract_product cp where exists (select 1  from APP___PAS__DBUSER.T_benefit_insured bi where cp.busi_item_id = bi.busi_item_id 
				 and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id) and cp.policy_id = i.policy_id ) as amnt,
				(select sum(cp.unit) from APP___PAS__DBUSER.T_contract_product cp where exists (select 1  from APP___PAS__DBUSER.T_benefit_insured bi where cp.busi_item_id = bi.busi_item_id 
				 and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id) and cp.policy_id = i.policy_id ) as unit,
				(select sum(cp.total_prem_af) from APP___PAS__DBUSER.T_contract_product cp where exists (select 1  from APP___PAS__DBUSER.T_benefit_insured bi where cp.busi_item_id = bi.busi_item_id 
				 and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id) and cp.policy_id = i.policy_id ) as prem,
				(select sum(cp.initial_extra_prem_af) from APP___PAS__DBUSER.T_contract_product cp where exists (select 1  from APP___PAS__DBUSER.T_benefit_insured bi where cp.busi_item_id = bi.busi_item_id 
				and bi.insured_id = i.list_id and  bi.policy_id = i.policy_id) and cp.policy_id = i.policy_id ) as occaddprem
			from APP___PAS__DBUSER.T_insured_list i  where policy_code=#{policyCode}
		]]>	
		<if test="customerId != null and customerId != '' "><![CDATA[ and i.customer_id = #{customerId}]]></if>
	</select>
	<!-- 根据保全变更ID删除操作 -->	
	<delete id="JRQD_deleteCsInsuredListByChangeID" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_INSURED_LIST WHERE CHANGE_ID = #{change_id, jdbcType=NUMERIC} ]]>
	</delete>
	
	
	
	<!-- 查询所有操作 -->
	<select id="JRQD_pa_findAllCsInsuredListForHI" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
             SELECT DISTINCT B.CUSTOMER_ID,B.CUSTOMER_NAME,B.CUSTOMER_BIRTHDAY,
             (SELECT D.GENDER_DESC FROM APP___PAS__DBUSER.T_GENDER D WHERE D.GENDER_CODE = B.CUSTOMER_GENDER) GENDER,
             B.CUSTOMER_GENDER,(SELECT C.TYPE
                   FROM APP___PAS__DBUSER.T_CERTI_TYPE C
                  WHERE C.CODE = B.CUSTOMER_CERT_TYPE) CSET_TYPE,B.CUSTOMER_CERT_TYPE,B.CUSTOMER_CERTI_CODE
  FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A, DEV_PAS.T_CUSTOMER B
 WHERE 1=1 
   AND A.CUSTOMER_ID = B.CUSTOMER_ID  ]]>
   
   <if test="csPolicyChgIds  != null and csPolicyChgIds.size()!=0 ">
      <![CDATA[ AND (]]>
      <foreach collection="csPolicyChgIds" item="csPolicyChgId"
        index="index" open="" close="" separator="OR">
        <![CDATA[ A.POLICY_CHG_ID = #{csPolicyChgId} ]]>
      </foreach>
      <![CDATA[) ]]>
    </if>
   
   
		<include refid="JRQD_csInsuredListWhereCondition" />
		
	</select>
	
	<select id="JRQD_queryInsuredListByPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  DISTINCT C.ORDER_ID,LC.CUSTOMER_ID,LC.RELATION_TO_PH,LC.POLICY_CODE,LC.POLICY_ID,LC.LIST_ID,LC.LOG_ID   
			FROM DEV_PAS.T_CS_INSURED_LIST LC 
			LEFT JOIN DEV_PAS.T_CS_BENEFIT_INSURED C ON LC.POLICY_ID = C.POLICY_ID AND LC.LIST_ID = C.INSURED_ID
			AND LC.CHANGE_ID = C.CHANGE_ID AND LC.POLICY_CHG_ID = C.POLICY_CHG_ID AND LC.OLD_NEW = 1 
			AND C.OLD_NEW = 1
			WHERE C.POLICY_CODE = #{policy_code} AND LC.CHANGE_ID = #{change_id} 
			AND LC.POLICY_CHG_ID = #{policy_chg_id} ]]>
	</select>
	
	<select id="JRQD_findCsInsuredByChangeIdAcceptCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT P.* FROM DEV_PAS.T_CS_POLICY_CHANGE C ,DEV_PAS.T_CS_INSURED_LIST P
				   WHERE C.CHANGE_ID = P.CHANGE_ID AND C.POLICY_CHG_ID = P.POLICY_CHG_ID ]]>
	    <if test=" accept_id  != null "><![CDATA[ AND C.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" address_id  != null "><![CDATA[ AND P.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND P.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND P.CHANGE_ID = #{change_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND P.OLD_NEW = #{old_new} ]]></if>
	</select>
	
	
	<update id="JRQD_updateInsuredSociSecu" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_INSURED_LIST ]]>
		<set>
		<trim suffixOverrides=",">		   
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,		    
			UPDATE_TIME = SYSDATE , 		  
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SOCI_SECU=#{soci_secu, jdbcType=NUMERIC},		    	  
		    OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,		   
		</trim>
		
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>
	
	<!-- 查询被保人信息 -->
	<select id="JRQD_queryInsuredInfoForEffectOfNs" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT C.CUSTOMER_ID,C.CUSTOMER_NAME, C.CUSTOMER_BIRTHDAY, C.CUSTOMER_GENDER,C.CUSTOMER_CERT_TYPE,C.CUSTOMER_CERTI_CODE,
				   C.LIVE_STATUS,C.DEATH_DATE,A.ORDER_ID,B.RELATION_TO_PH
			   FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED A
				 INNER JOIN APP___PAS__DBUSER.T_INSURED_LIST B
				    ON A.INSURED_ID = B.LIST_ID
				 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER C
				    ON B.CUSTOMER_ID = C.CUSTOMER_ID
				 WHERE A.POLICY_ID = #{policy_id}
				   AND A.BUSI_ITEM_ID = #{busi_item_id}
		]]>		
	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_acceptIdQueryCsInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT a.* 
  FROM APP___PAS__DBUSER.T_CS_INSURED_LIST A,
       APP___PAS__DBUSER.t_Cs_Policy_Change B, 
       APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C   
 WHERE 1 = 1
and A.POLICY_CHG_ID = B.POLICY_CHG_ID
and b.accept_id = c.accept_id 
and c.accept_id = #{accept_id} 
		  ]]>
		<include refid="JRQD_csInsuredListWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
	
	
</mapper>
