<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.model.dao.impl.ICsApplicationDao">

	<sql id="JRQD_csApplicationWhereCondition">
		<if test=" is_befor_scan != null and is_befor_scan != ''  "><![CDATA[ AND A.IS_BEFOR_SCAN = #{is_befor_scan} ]]></if>
		<if test=" applicant_type != null and applicant_type != ''  "><![CDATA[ AND A.APPLICANT_TYPE = #{applicant_type} ]]></if>
		<if test=" try_calc_no != null and try_calc_no != ''  "><![CDATA[ AND A.TRY_CALC_NO = #{try_calc_no} ]]></if>		
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>		
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" eslewhere_flag  != null "><![CDATA[ AND A.ESLEWHERE_FLAG = #{eslewhere_flag} ]]></if>
		<if test=" apply_name != null and apply_name != ''  "><![CDATA[ AND A.APPLY_NAME = #{apply_name} ]]></if>
		<if test=" to_uw_date  != null  and  to_uw_date  != ''  "><![CDATA[ AND A.TO_UW_DATE = #{to_uw_date} ]]></if>
		<if test=" agent_name != null and agent_name != ''  "><![CDATA[ AND A.AGENT_NAME = #{agent_name} ]]></if>
		<if test=" app_status != null and app_status != ''  "><![CDATA[ AND A.APP_STATUS = #{app_status} ]]></if>
		<if test=" nofill_flag  != null "><![CDATA[ AND A.NOFILL_FLAG = #{nofill_flag} ]]></if>
		<if test=" agent_certi_type != null and agent_certi_type != ''  "><![CDATA[ AND A.AGENT_CERTI_TYPE = #{agent_certi_type} ]]></if>
		<if test=" apply_time  != null  and  apply_time  != ''  "><![CDATA[ AND A.APPLY_TIME = #{apply_time} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" source_type != null and source_type != ''  "><![CDATA[ AND A.SOURCE_TYPE = #{source_type} ]]></if>
		<if test=" agent_level  != null "><![CDATA[ AND A.AGENT_LEVEL = #{agent_level} ]]></if>
		<if test=" service_type != null and service_type != ''  "><![CDATA[ AND A.SERVICE_TYPE = #{service_type} ]]></if>
		<if test=" agent_certi_code != null and agent_certi_code != ''  "><![CDATA[ AND A.AGENT_CERTI_CODE = #{agent_certi_code} ]]></if>
		<if test=" event_code != null and event_code != ''  "><![CDATA[ AND A.EVENT_CODE = #{event_code} ]]></if>		
		<if test=" agent_tel != null and agent_tel != ''  "><![CDATA[ AND A.AGENT_TEL = #{agent_tel} ]]></if>
		<if test=" agent_code  != null and  agent_code  != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" to_approve_date  != null  and  to_approve_date  != ''  "><![CDATA[ AND A.TO_APPROVE_DATE = #{to_approve_date} ]]></if>
		<if test="customer_id_list  != null and customer_id_list.size()!=0">
		<![CDATA[ AND A.CUSTOMER_ID IN ]]>
			<foreach collection="customer_id_list" item="customer_id_list"
				index="index" open="(" close=")" separator=",">#{customer_id_list}</foreach></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsApplicationByChangeIdCondition">
		<if test=" change_id  != null and change_id !='' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addCsApplication"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_APPLICATION(
			    IS_BEFOR_SCAN,
			    IS_INTERACTIVE_JOINT,
				APPLICANT_TYPE, TRY_CALC_NO , APPLY_CODE, ORGAN_CODE, 
				UPDATE_BY, CHANGE_ID, APPLY_NAME, TO_UW_DATE, AGENT_NAME, 
				UPDATE_TIME,  APP_STATUS, NOFILL_FLAG, 
				UPDATE_TIMESTAMP, INSERT_BY , AGENT_CERTI_TYPE , 
				APPLY_TIME, CUSTOMER_ID, FINISH_TIME , SOURCE_TYPE, INSERT_TIMESTAMP, 
				AGENT_LEVEL, SERVICE_TYPE, INSERT_TIME, AGENT_CERTI_CODE,  EVENT_CODE, 
				 AGENT_TEL,  AGENT_CODE ,
				TO_APPROVE_DATE,ESLEWHERE_FLAG,IS_AUTO_INPUT,BACK_STEP,BANK_CHANNEL,BANK_CODE,
				agent_sales_organ_code,agent_bank_branch_code,agent_organ_code,agent_cert_star_date,agent_cert_end_date ) 
			VALUES (
				#{is_befor_scan, jdbcType=NUMERIC},#{is_interactive_joint, jdbcType=NUMERIC},#{applicant_type, jdbcType=VARCHAR}, #{try_calc_no, jdbcType=VARCHAR}  ,  #{apply_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} 
				,  #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{apply_name, jdbcType=VARCHAR} , #{to_uw_date, jdbcType=DATE} , #{agent_name, jdbcType=VARCHAR} 
				, SYSDATE , #{app_status, jdbcType=VARCHAR} ,  #{nofill_flag, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC}  ,  #{agent_certi_type, jdbcType=VARCHAR}  
				, to_date(#{t_time, jdbcType=VARCHAR},'yyyy-MM-dd HH24:mi:ss') , #{customer_id, jdbcType=NUMERIC} , #{finish_time, jdbcType=DATE} ,  #{source_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP
				, #{agent_level, jdbcType=NUMERIC} ,  #{service_type, jdbcType=VARCHAR} , SYSDATE , #{agent_certi_code, jdbcType=VARCHAR} ,  #{event_code, jdbcType=VARCHAR} 
				,  #{agent_tel, jdbcType=VARCHAR} , #{agent_code, jdbcType=VARCHAR} 
				, #{to_approve_date, jdbcType=DATE} , #{eslewhere_flag, jdbcType=NUMERIC},#{is_auto_input, jdbcType=NUMERIC},#{back_step, jdbcType=VARCHAR},#{bank_channel, jdbcType=VARCHAR},#{bank_code, jdbcType=VARCHAR}
				,#{agent_sales_organ_code, jdbcType=VARCHAR},#{agent_bank_branch_code, jdbcType=VARCHAR},#{agent_organ_code, jdbcType=VARCHAR},#{agent_cert_star_date, jdbcType=DATE},#{agent_cert_end_date, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsApplication" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_APPLICATION WHERE CHANGE_ID = #{change_id} ]]>
	</delete>
	<update id="JRQD_updateCsApplicationAppStatusEffect" parameterType="java.util.Map">
		<![CDATA[ 
		UPDATE APP___PAS__DBUSER.T_CS_APPLICATION CA
				   SET CA.APP_STATUS       = '9',
				       CA.UPDATE_BY        = #{update_by, jdbcType=NUMERIC},
				       CA.UPDATE_TIME      = SYSDATE,
				       CA.UPDATE_TIMESTAMP = CURRENT_TIMESTAMP
				 WHERE CA.CHANGE_ID = #{change_id}
	  	]]>
	</update>
<!-- 修改操作 -->
	<update id="JRQD_updateCsApplication" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_APPLICATION ]]>
		<set>
		<trim suffixOverrides=",">
			APPLICANT_TYPE = #{applicant_type, jdbcType=VARCHAR} ,
			TRY_CALC_NO = #{try_calc_no, jdbcType=VARCHAR} ,			
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,			
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			APPLY_NAME = #{apply_name, jdbcType=VARCHAR} ,
		    TO_UW_DATE = #{to_uw_date, jdbcType=DATE} ,
			AGENT_NAME = #{agent_name, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			APP_STATUS = #{app_status, jdbcType=VARCHAR} ,
		    NOFILL_FLAG = #{nofill_flag, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENT_CERTI_TYPE = #{agent_certi_type, jdbcType=VARCHAR} ,
		    <if test="apply_time != null">
			APPLY_TIME = #{apply_time,jdbcType=DATE},
			</if>
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
		    <if test="source_type != null">
			SOURCE_TYPE = #{source_type, jdbcType=VARCHAR} ,
			</if>
			<if test="is_interactive_joint != null">
			IS_INTERACTIVE_JOINT = #{is_interactive_joint, jdbcType=NUMERIC} ,
			</if>
			<if test="is_befor_scan != null">
			IS_BEFOR_SCAN = #{is_befor_scan, jdbcType=NUMERIC} ,
			</if>
			<if test="bank_code != null">
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
			</if>
			<if test="bank_channel != null">
			BANK_CHANNEL = #{bank_channel, jdbcType=VARCHAR} ,
			</if>
		    AGENT_LEVEL = #{agent_level, jdbcType=NUMERIC} ,
			SERVICE_TYPE = #{service_type, jdbcType=VARCHAR} ,
			AGENT_CERTI_CODE = #{agent_certi_code, jdbcType=VARCHAR} ,
			EVENT_CODE = #{event_code, jdbcType=VARCHAR} ,		   
			AGENT_TEL = #{agent_tel, jdbcType=VARCHAR} ,
		    AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		    TO_APPROVE_DATE = #{to_approve_date, jdbcType=DATE} ,
		    ESLEWHERE_FLAG = #{eslewhere_flag, jdbcType=NUMERIC},
		    IS_AUTO_INPUT = #{is_auto_input, jdbcType=NUMERIC},
		    BACK_STEP = #{back_step, jdbcType=VARCHAR},
		    AGENT_SALES_ORGAN_CODE = #{agent_sales_organ_code, jdbcType=VARCHAR},
		    AGENT_BANK_BRANCH_CODE = #{agent_bank_branch_code, jdbcType=VARCHAR},
		    AGENT_ORGAN_CODE = #{agent_organ_code, jdbcType=VARCHAR},
		    AGENT_CERT_STAR_DATE = #{agent_cert_star_date,jdbcType=DATE},
		    AGENT_CERT_END_DATE = #{agent_cert_end_date,jdbcType=DATE},
		</trim>
		</set>
		<![CDATA[ WHERE CHANGE_ID = #{change_id} ]]>
	</update>


<!-- 修改操作 -->
	<update id="JRQD_updateCsAppliStatusById" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_APPLICATION ]]>
		<set>
		<trim suffixOverrides=",">
			APP_STATUS = #{app_status, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CHANGE_ID = #{change_id} ]]>
	</update>
<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsApplicationByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO ,  A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , A.INSERT_BY,
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE,  
			A.AGENT_LEVEL,  A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE, 
			 A.AGENT_TEL,  A.AGENT_CODE , A.AGENT_SALES_ORGAN_CODE,A.AGENT_BANK_BRANCH_CODE,A.AGENT_ORGAN_CODE,
			A.TO_APPROVE_DATE , A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP ,A.IS_BEFOR_SCAN,A.IS_INTERACTIVE_JOINT,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE FROM APP___PAS__DBUSER.T_CS_APPLICATION A WHERE ROWNUM <=  1]]>
		<include refid="JRQD_queryCsApplicationByChangeIdCondition" />
		<![CDATA[ ORDER BY A.CHANGE_ID ]]>
	</select>
	
	
		<select id="JRQD_findCsApplicationByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO ,  A.APPLY_CODE, A.ORGAN_CODE, 
      A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
      A.APP_STATUS,  A.NOFILL_FLAG, 
      A.AGENT_CERTI_TYPE , A.INSERT_BY,
      A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE,  
      A.AGENT_LEVEL,  A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE, 
       A.AGENT_TEL,  A.AGENT_CODE , A.AGENT_SALES_ORGAN_CODE,A.AGENT_BANK_BRANCH_CODE,A.AGENT_ORGAN_CODE,
      A.TO_APPROVE_DATE , A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP ,A.IS_BEFOR_SCAN,A.IS_INTERACTIVE_JOINT,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE FROM APP___PAS__DBUSER.T_CS_APPLICATION A ,APP___PAS__DBUSER.t_cs_policy_change cpa WHERE ROWNUM <=  1
      and A.change_id = cpa.change_id and cpa.policy_chg_id = ${policy_chg_id}]]>
	</select>
	
<!-- 按索引查询操作 -->	
	<select id="JRQD_findApplyIsPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.policy_id,A.policy_code,A.apply_code FROM APP___PAS__DBUSER.T_POLICY_HOLDER  A 
		          WHERE A.policy_code=#{policy_code} AND A.CUSTOMER_ID=#{customer_id}]]>
	</select>
	<!-- 因为有时分秒所以截取一下 bug 28721 和需求确认当天是可以退的 -->
		<select id="JRQD_findCsApplicationByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		select trunc(max(app.apply_time),'dd') APPLY_TIME
  from APP___PAS__DBUSER.T_CS_CONTRACT_MASTER ma,
       APP___PAS__DBUSER.T_CS_APPLICATION     app
 where app.change_id = ma.change_id
   and ma.old_new = 1
   and app.app_status = 9
   and ma.policy_code = #{accept_code}
	</select>
	<!-- 被保人存在电话变更 -->	
	<select id="JRQD_find_insuredPhoneChange_info" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select a.change_id,a.service_code,a.accept_id
			  from APP___PAS__DBUSER.t_cs_accept_change a
			 where a.accept_status = '18'
			   and a.service_code = 'CC'
			   and exists(select 'X' from APP___PAS__DBUSER.t_cs_application where change_id = a.change_id and customer_id = #{customer_id})
			   and (   (
				         (select max(nvl(house_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '0')
				         <>
				         (select max(nvl(house_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '1')
				        )
			        or
				        (
				          (select max(nvl(office_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '0')
				           <>
				          (select max(nvl(office_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '1')
				        )
				    or
				        (
				          (select max(nvl(offen_use_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '0')
				           <>
				          (select max(nvl(offen_use_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '1')
				        )
			        or
				        (
				           (select max(nvl(mobile_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '0')
				           <>
				           (select max(nvl(mobile_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '1')
				         )
			   )
		]]>
	</select> 
	
	
	<!-- 客户在某日期后是否发生过电话变更 -->	
	<select id="JRQD_CS_findCsApplicationTotal"  resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			select count(1)
			  from APP___PAS__DBUSER.t_cs_accept_change a
			 where a.accept_status = '18'
			   and a.service_code = 'CC'
			   and exists(select 'X' from APP___PAS__DBUSER.t_cs_application where change_id = a.change_id and customer_id = #{customer_id})
			   and (   (
				         (select max(nvl(house_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '0')
				         <>
				         (select max(nvl(house_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '1')
				        )
			        or
				        (
				          (select max(nvl(office_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '0')
				           <>
				          (select max(nvl(office_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '1')
				        )
				    or
				        (
				          (select max(nvl(offen_use_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '0')
				           <>
				          (select max(nvl(offen_use_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '1')
				        )
			        or
				        (
				           (select max(nvl(mobile_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '0')
				           <>
				           (select max(nvl(mobile_tel, '')) from APP___PAS__DBUSER.t_cs_customer where change_id = a.change_id and customer_id = #{customer_id} and old_new = '1')
				         )
			   )
		]]>
	</select> 
	
	<!-- 查找单条数据 -->	
	<select id="JRQD_findCsApplication" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO,   A.APPLY_CODE, A.ORGAN_CODE, 
			 A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			  A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , A.BANK_CHANNEL, A.BANK_CODE,
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE, 
			A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE,
			 A.AGENT_TEL, A.AGENT_CODE , A.TO_APPROVE_DATE , A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP,A.IS_BEFOR_SCAN,
            A.AGENT_SALES_ORGAN_CODE,
            A.AGENT_BANK_BRANCH_CODE,A.UPDATE_BY,A.INSERT_BY,
            A.AGENT_ORGAN_CODE,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE FROM APP___PAS__DBUSER.T_CS_APPLICATION A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csApplicationWhereCondition" />
		<![CDATA[ ORDER BY A.CHANGE_ID ]]>
	</select>
	<!-- 查找单条数据 -->	
		<select id="JRQD_findCsApplication1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO,   A.APPLY_CODE, A.ORGAN_CODE, 
       A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
        A.APP_STATUS,  A.NOFILL_FLAG, 
    A.AGENT_CERTI_TYPE , 
      A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE, 
      A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE,
       A.AGENT_TEL, A.AGENT_CODE , A.TO_APPROVE_DATE , A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP,  B.ACCEPT_CODE,C.POLICY_CODE ,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE
            FROM APP___PAS__DBUSER.T_CS_APPLICATION A
            left join 
           APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
           on  A.CHANGE_ID=B.CHANGE_ID
           left join APP___PAS__DBUSER.T_CS_POLICY_CHANGE C 
           on B.ACCEPT_ID=C.ACCEPT_ID
            WHERE 1=1 and   ROWNUM <=  1000   ]]>
		<include refid="JRQD_csApplicationWhereCondition" />
		<![CDATA[ ORDER BY A.CHANGE_ID ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsApplication" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO,   A.APPLY_CODE, A.ORGAN_CODE, 
			 A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			  A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE,
			 A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE, 
			  A.AGENT_TEL, A.AGENT_CODE , 
			A.TO_APPROVE_DATE ,A.IS_AUTO_INPUT,A.BACK_STEP,A.IS_BEFOR_SCAN,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE FROM APP___PAS__DBUSER.T_CS_APPLICATION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CHANGE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsApplication" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO,  A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			 A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE,
			 A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE, 
			  A.AGENT_TEL, A.AGENT_CODE, 
			A.TO_APPROVE_DATE,A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP,A.IS_BEFOR_SCAN,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE FROM APP___PAS__DBUSER.T_CS_APPLICATION A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csApplicationWhereCondition" />
		<![CDATA[ ORDER BY A.CHANGE_ID ]]> 
	</select>
	
	<select id="JRQD_findPolicyChangeById" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select b.accept_status,b.service_code,b.policy_code,b.apply_time from APP___PAS__DBUSER.T_CS_POLICY_CHANGE b where exists (select a.change_id from t_cs_application a where a.change_id=b.change_id and a.customer_id =#{customer_id} )
		  ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="JRQD_findCsApplicationTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_APPLICATION A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>
	
	
	<!-- 查询试算aPPLICATION个数操作 只是在application 单条件下查询 另外有个复核的查询 -->
	<select id="JRQD_findCsApplicationTotalForTryCalc" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM (select A.CHANGE_ID from APP___PAS__DBUSER.T_CS_APPLICATION A where A.TRY_CALC_NO IS NOT NULL and  not exists (select D.CHANGE_ID from APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE D where A.CHANGE_ID=D.CHANGE_Id) ]]>
			<include refid="JRQD_csApplicationWhereCondition" />
	<![CDATA[ UNION
				select A.CHANGE_ID from APP___PAS__DBUSER.T_CS_APPLICATION A , APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C where( A.TRY_CALC_NO IS NOT NULL AND A.CHANGE_ID=C.CHANGE_Id  AND C.ACCEPT_STATUS='01')
				]]>
			<include refid="JRQD_csApplicationWhereCondition" />
	<![CDATA[
		) ]]>
		
	</select>
	
	<!-- 查询试算任务池个数操作 复核条件查询 -->
	<select id="JRQD_findQuotationPoolMsgTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM (select A.CHANGE_ID from APP___PAS__DBUSER.T_CS_APPLICATION A where A.TRY_CALC_NO IS NOT NULL and  A.APP_STATUS='11'  and  not exists (select D.CHANGE_ID from APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE D where A.CHANGE_ID=D.CHANGE_Id) ]]>
			<if test=" insert_by != null and insert_by != ''  ">
			<![CDATA[ AND A.insert_by = #{insert_by} ]]></if>
			<include refid="JRQD_csQuotationTotalForPage1" />
	<![CDATA[ UNION
				select A.CHANGE_ID from  APP___PAS__DBUSER.T_CS_APPLICATION A ,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B,APP___PAS__DBUSER.T_CS_POLICY_CHANGE C
       where(( A.TRY_CALC_NO IS NOT NULL AND A.APP_STATUS='11' AND A.CHANGE_ID=B.CHANGE_Id  AND B.ACCEPT_STATUS in ('01','02','05','06','07') )
       and A.Change_Id=B.Change_Id and B.Change_Id=C.Change_Id and B.ACCEPT_ID=C.ACCEPT_ID)
				]]>
				<if test=" insert_by != null and insert_by != ''  ">
			<![CDATA[ AND A.insert_by = #{insert_by} ]]></if>
			<include refid="JRQD_csQuotationTotalForPage" />
	<![CDATA[
            		) 
		]]>
		
	</select>
	
	
	<!-- 查询个数操作 -->
	<select id="JRQD_findCsAcceptTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="JRQD_queryCsApplicationForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.APPLICANT_TYPE, B.TRY_CALC_NO,   B.APPLY_CODE, B.ORGAN_CODE, 
			B.CHANGE_ID, B.APPLY_NAME, B.TO_UW_DATE, B.AGENT_NAME , 
			 B.APP_STATUS,  B.NOFILL_FLAG, 
			 B.AGENT_CERTI_TYPE , 
			B.APPLY_TIME, B.CUSTOMER_ID, B.FINISH_TIME , B.SOURCE_TYPE, 
			 B.AGENT_LEVEL, B.SERVICE_TYPE, B.AGENT_CERTI_CODE, B.EVENT_CODE, 
			 B.AGENT_TEL, B.AGENT_CODE , 
			B.TO_APPROVE_DATE B.ESLEWHERE_FLAG ,B.IS_AUTO_INPUT,B.BACK_STEP FROM (
					SELECT ROWNUM RN, A.APPLICANT_TYPE, A.TRY_CALC_NO,   A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			 A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE, 
			A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE, A.EVENT_CODE, 
			 A.AGENT_TEL, A.AGENT_CODE , 
			A.TO_APPROVE_DATE, A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP,A.IS_BEFOR_SCAN,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE FROM APP___PAS__DBUSER.T_CS_APPLICATION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_csApplicationWhereCondition" />
		<![CDATA[ ORDER BY A.CHANGE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 分页查询操作 -->
	<select id="JRQD_queryCsApplicationForPageForTryCalc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		
		SELECT A.RN AS rowNumber,A.APPLICANT_TYPE, A.TRY_CALC_NO,   A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			 A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE, 
			A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE, A.EVENT_CODE, 
			A.AGENT_TEL, A.AGENT_CODE , 
			A.TO_APPROVE_DATE,A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP FROM (
		SELECT ROWNUM AS RN,T.*
			FROM (
			SELECT B.APPLICANT_TYPE, B.TRY_CALC_NO,   B.APPLY_CODE, B.ORGAN_CODE, 
			B.CHANGE_ID, B.APPLY_NAME, B.TO_UW_DATE, B.AGENT_NAME , 
			 B.APP_STATUS,  B.NOFILL_FLAG, 
			B.AGENT_CERTI_TYPE , 
			B.APPLY_TIME, B.CUSTOMER_ID, B.FINISH_TIME , B.SOURCE_TYPE, 
			 B.AGENT_LEVEL, B.SERVICE_TYPE, B.AGENT_CERTI_CODE, B.EVENT_CODE, 
			 B.AGENT_TEL, B.AGENT_CODE , 
			B.TO_APPROVE_DATE,B.ESLEWHERE_FLAG ,B.IS_AUTO_INPUT,B.BACK_STEP FROM (
					SELECT ROWNUM RN, A.APPLICANT_TYPE, A.TRY_CALC_NO,   A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			 A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE, 
			A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE, A.EVENT_CODE, 
			 A.AGENT_TEL, A.AGENT_CODE , 
			A.TO_APPROVE_DATE,A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP FROM APP___PAS__DBUSER.T_CS_APPLICATION A where 
			A.TRY_CALC_NO IS NOT NULL and  not exists (select D.CHANGE_ID from APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE D where A.CHANGE_ID=D.CHANGE_Id)
			]]>
			<include refid="JRQD_csApplicationWhereCondition" />
	<![CDATA[)  B
  UNION
  SELECT  B.APPLICANT_TYPE, B.TRY_CALC_NO,   B.APPLY_CODE, B.ORGAN_CODE, 
			B.CHANGE_ID, B.APPLY_NAME, B.TO_UW_DATE, B.AGENT_NAME , 
			 B.APP_STATUS,  B.NOFILL_FLAG, 
			B.AGENT_CERTI_TYPE , 
			B.APPLY_TIME, B.CUSTOMER_ID, B.FINISH_TIME , B.SOURCE_TYPE, 
			 B.AGENT_LEVEL, B.SERVICE_TYPE, B.AGENT_CERTI_CODE, B.EVENT_CODE, 
			 B.AGENT_TEL, B.AGENT_CODE , 
			B.TO_APPROVE_DATE,B.ESLEWHERE_FLAG ,B.IS_AUTO_INPUT,B.BACK_STEP FROM (
					SELECT ROWNUM RN, A.APPLICANT_TYPE, A.TRY_CALC_NO,   A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE, 
			A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE, A.EVENT_CODE, 
			 A.AGENT_TEL, A.AGENT_CODE , 
			A.TO_APPROVE_DATE,A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP FROM APP___PAS__DBUSER.T_CS_APPLICATION A , APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C where( A.TRY_CALC_NO IS NOT NULL AND A.CHANGE_ID=C.CHANGE_Id  AND C.ACCEPT_STATUS='01')
			]]>
			<include refid="JRQD_csApplicationWhereCondition" />
			<![CDATA[
 			)B
 			)T  WHERE ROWNUM <= #{LESS_NUM} ]]>
		<![CDATA[ ORDER BY T.CHANGE_ID ]]> 
		<![CDATA[ )  A
			WHERE A.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!-- 分页查询操作 -->
	<select id="JRQD_queryQuotationPool" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT A.RN AS rowNumber,A.TRY_CALC_NO TRY_CALC_NO, A.APPLY_CODE APPLY_CODE,
          A.CHANGE_ID CHANGE_ID,
          A.APPLY_TIME CREATE_DATE,
		  A.SERVICE_CODE SERVICE_CODE,
		A.ACCEPT_Code ACCEPT_Code,
		A.POLICY_CODE POLICY_CODE
		
		FROM (
		SELECT ROWNUM AS RN,T.*
			FROM (
			SELECT B.* FROM (
          SELECT  A.TRY_CALC_NO, 
          A.APPLY_CODE,
          A.CHANGE_ID,
          A.APPLY_TIME,
           ''as SERVICE_CODE,
           ''as ACCEPT_Code,
           ''as POLICY_CODE
      FROM APP___PAS__DBUSER.T_CS_APPLICATION A
       where 
      A.TRY_CALC_NO IS NOT NULL and  A.APP_STATUS='11' and not exists (select D.CHANGE_ID from APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE D where A.CHANGE_ID=D.CHANGE_Id)
			]]>
			<if test=" insert_by != null and insert_by != ''  ">
			<![CDATA[ AND A.insert_by = #{insert_by} ]]></if>
			<include refid="JRQD_csQuotationTotalForPage1" />
	<![CDATA[)  B
  UNION
  SELECT  B.* FROM (
          select D.TRY_CALC_NO,
            D.APPLY_CODE,
            D.CHANGE_ID,
            D.APPLY_TIME,
            LISTAGG(D.SERVICE_CODE,'/') within group(order by D.SERVICE_CODE) AS SERVICE_CODE,
            LISTAGG(D.ACCEPT_Code,'/') within group(order by D.ACCEPT_Code) AS ACCEPT_Code,
            LISTAGG(D.POLICY_CODE,'/') within group(order by D.POLICY_CODE) AS POLICY_CODE
          FROM(
           SELECT  A.TRY_CALC_NO,
            A.APPLY_CODE,
            A.CHANGE_ID,
            A.APPLY_TIME,
            B.SERVICE_CODE,
            B.ACCEPT_Code,
            LISTAGG(C.POLICY_CODE,'*') within group(order by C.POLICY_CODE) AS POLICY_CODE
      FROM APP___PAS__DBUSER.T_CS_APPLICATION A,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B,APP___PAS__DBUSER.T_CS_POLICY_CHANGE C
      where( A.TRY_CALC_NO IS NOT NULL AND A.APP_STATUS='11' AND A.CHANGE_ID=B.CHANGE_Id  AND B.ACCEPT_STATUS in ('01','02','05','06','07') )
       and A.Change_Id=B.Change_Id and B.Change_Id=C.Change_Id and B.ACCEPT_ID=C.ACCEPT_ID
        ]]>
        <if test=" insert_by != null and insert_by != ''  ">
			<![CDATA[ AND A.insert_by = #{insert_by} ]]></if>
			<include refid="JRQD_csQuotationTotalForPage" />
			<![CDATA[
       group by A.TRY_CALC_NO,
            A.APPLY_CODE,
            A.CHANGE_ID,
            A.APPLY_TIME,
            B.SERVICE_CODE,
            B.ACCEPT_Code
            )D
           
            group by D.TRY_CALC_NO,
            D.APPLY_CODE,
            D.CHANGE_ID,
            D.APPLY_TIME
 			)B
 			)T  WHERE ROWNUM <= #{LESS_NUM} ]]>
		<![CDATA[ )  A
			WHERE A.RN > #{GREATER_NUM} ]]>
			<![CDATA[ ORDER BY  A.APPLY_TIME,A.CHANGE_ID ]]> 
	</select>
	
	<sql id="JRQD_csQuotationTotalForPage">
		 <if test=" try_calc_no!= null and try_calc_no!= '' "><![CDATA[ AND A.TRY_CALC_NO=#{try_calc_no}]]></if> 
		<if test=" service_code!= null and service_code!= '' "><![CDATA[ AND B.SERVICE_CODE=#{service_code}]]></if> 
		<if test=" policy_code!= null and policy_code!= '' "><![CDATA[ AND C.POLICY_CODE=#{policy_code}]]></if> 
		<if test=" create_date!= null and create_date!= '' "><![CDATA[ AND A.APPLY_TIME >=#{create_date} AND A.APPLY_TIME<=#{create_date}+numtodsinterval(23,'hour')+numtodsinterval(59,'minute')+numtodsinterval(59,'second')]]></if>
	</sql>
	<sql id="JRQD_csQuotationTotalForPage1">
		 <if test=" try_calc_no!= null and try_calc_no!= '' "><![CDATA[ AND A.TRY_CALC_NO=#{try_calc_no}]]></if> 
		<if test=" create_date!= null and create_date!= '' "><![CDATA[ AND A.APPLY_TIME >=#{create_date} AND A.APPLY_TIME<=#{create_date}+numtodsinterval(23,'hour')+numtodsinterval(59,'minute')+numtodsinterval(59,'second')]]></if>
		<if test=" service_code!= null and service_code!= '' "><![CDATA[ AND A.TRY_CALC_NO=#{service_code}]]></if> 
		<if test=" policy_code!= null and policy_code!= '' "><![CDATA[ AND A.TRY_CALC_NO=#{policy_code}]]></if> 
	</sql>
	<sql id="JRQD_entryTaskPoolWhereConditions">
		 <if test=" change_id!= null and change_id!= '' "><![CDATA[ AND APP.CHANGE_ID=#{change_id}]]></if> 
		<if test=" apply_code!= null and apply_code!= '' "><![CDATA[ AND APP.APPLY_CODE=#{apply_code}]]></if> 
		<if test=" customer_id!= null and customer_id!= '' "><![CDATA[ AND APP.INSERT_BY=#{customer_id}]]></if> 
	</sql>
<sql id="JRQD_acceptTaskPoolWhereConditions">

		 <if test=" is_interactive_joint!= null and is_interactive_joint!= '' "><![CDATA[ AND APP.IS_INTERACTIVE_JOINT=#{is_interactive_joint}]]></if> 
		 <if test=" change_id!= null and change_id!= '' "><![CDATA[ AND APP.CHANGE_ID=#{change_id}]]></if> 
		<if test=" apply_code!= null and apply_code!= '' "><![CDATA[ AND APP.APPLY_CODE=#{apply_code}]]></if> 
		<if test=" customer_id!= null and customer_id!= '' "><![CDATA[ AND APP.INSERT_BY=#{customer_id}]]></if> 
		<if test=" accept_code!= null and accept_code!= '' "><![CDATA[ AND ACC.ACCEPT_CODE=#{accept_code}]]></if>
		<if test=" policy_code!= null and policy_code!= '' "><![CDATA[ AND PCH.POLICY_CODE=#{policy_code}]]></if>
		<if test=" accept_date != null  and  accept_date  != ''  "><![CDATA[ AND ACC.ACCEPT_TIME = to_date(#{accept_date},'yyyy-MM-DD') ]]></if>
	</sql>
	<!-- 查询受理池中个数操作 -->
	<!-- <select id="JRQD_findCsApplicationTotalForAccept" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM (
			SELECT  ROWNUM RN, PCH.POLICY_CHG_ID, APP.APPLY_CODE, ACC.ACCEPT_CODE, ACC.SERVICE_CODE AS POLICY, PCH.POLICY_CODE,CUST.CUSTOMER_NAME AS POLICY_PERSON ,CUST.CUSTOMER_ID, APP.App_Status AS TASK_STATE, TO_CHAR(ACC.ACCEPT_TIME, 'YYYY-MM-DD') AS ACCEPT_DATE 
			FROM APP___PAS__DBUSER.T_CS_APPLICATION APP, APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC, APP___PAS__DBUSER.T_CS_POLICY_CHANGE PCH, APP___PAS__DBUSER.T_POLICY_HOLDER PH, APP___PAS__DBUSER.T_CUSTOMER CUST
			WHERE  APP.CHANGE_ID = PCH.CHANGE_ID(+) AND PCH.ACCEPT_ID = ACC.ACCEPT_ID(+) AND PCH.POLICY_ID = PH.POLICY_ID(+) AND APP.CUSTOMER_ID=CUST.CUSTOMER_ID 
					AND APP.TRY_CALC_NO IS NULL
  					AND  APP.App_Status in ('03','04')  
  					]]>
		<include refid="JRQD_acceptTaskPoolWhereConditions" />
		<![CDATA[ ORDER BY ACCEPT_TIME DESC ]]> 
		<![CDATA[ ) ]]> -->
		
	 <select id="JRQD_findCsApplicationTotalForAccept" resultType="java.lang.Integer" parameterType="java.util.Map">
		<!--  <![CDATA[ SELECT COUNT(1) FROM (
			 SELECT B.RN AS ROWNUMBER, B.POLICY_CHG_ID,B.Change_Id,B.APPLY_CODE, B.ACCEPT_CODE, B.POLICY, B.POLICY_CODE,B.POLICY_PERSON, B.CUSTOMER_ID,B.TASK_STATE, B.ACCEPT_DATE
		 FROM (
			SELECT  ROWNUM RN, PCH.POLICY_CHG_ID,APP.Change_Id,APP.APPLY_CODE, ACC.ACCEPT_CODE, ACC.SERVICE_CODE AS POLICY, PCH.POLICY_CODE,(SELECT TU.CUSTOMER_NAME  FROM APP___PAS__DBUSER.T_CUSTOMER TU WHERE TU.CUSTOMER_ID=PH.CUSTOMER_ID) AS POLICY_PERSON,PH.CUSTOMER_ID, APP.App_Status AS TASK_STATE,TO_CHAR(ACC.ACCEPT_TIME, 'YYYY-MM-DD') AS ACCEPT_DATE 
			FROM APP___PAS__DBUSER.T_CS_APPLICATION APP, APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC, APP___PAS__DBUSER.T_CS_POLICY_CHANGE PCH, APP___PAS__DBUSER.T_POLICY_HOLDER PH
			WHERE  APP.CHANGE_ID=PCH.CHANGE_ID(+) AND PCH.ACCEPT_ID =ACC.ACCEPT_ID(+) AND PCH.POLICY_ID = PH.POLICY_ID(+)  
					AND APP.TRY_CALC_NO IS NULL
					]]>
		<include refid="JRQD_acceptTaskPoolWhereConditions" />
		<if test=" task_state != null and task_state != ''  "><![CDATA[ AND APP.App_Status = #{task_state} ]]></if>
		<if test=" update_by != null and update_by != ''  "><![CDATA[ AND APP.UPDATE_BY = #{update_by} ]]></if>
		<![CDATA[ ORDER BY ACCEPT_TIME DESC ]]> 
		<![CDATA[ )  B
			WHERE 1=1 ]]>
		<![CDATA[ )]]>  -->
		<![CDATA[ SELECT COUNT(1) FROM (
			 SELECT ROWNUM RN,B.Change_Id,B.APPLY_CODE,B.TASK_STATE, B.APPLY_TIME
		 FROM (
			SELECT DISTINCT APP.Change_Id,APP.APPLY_CODE,APP.APP_STATUS AS TASK_STATE,APP.APPLY_TIME,APP.BACK_STEP 
			FROM APP___PAS__DBUSER.T_CS_APPLICATION APP, APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC, APP___PAS__DBUSER.T_CS_POLICY_CHANGE PCH
			WHERE  APP.CHANGE_ID=PCH.CHANGE_ID(+) AND PCH.ACCEPT_ID =ACC.ACCEPT_ID(+)   
					AND APP.TRY_CALC_NO IS NULL
					]]>
		<include refid="JRQD_acceptTaskPoolWhereConditions" />
		<if test=" is_interactive_joint!= null and is_interactive_joint!= '' "><![CDATA[ AND APP.ORGAN_CODE = #{organ_code}]]></if>
		<if test=" is_interactive_joint== null or is_interactive_joint== '' "><![CDATA[ AND APP.ORGAN_CODE  LIKE '${organ_code}%'  ]]></if>
		<if test=" task_state != null and task_state != ''  "><![CDATA[ AND APP.App_Status = #{task_state} ]]></if>
		<if test=" update_by != null and update_by != ''  "><![CDATA[ AND APP.UPDATE_BY = #{update_by} ]]></if>
		<if test=" back_step != null and back_step != ''  "><![CDATA[ AND APP.back_step = #{back_step} ]]></if>
		<![CDATA[ ORDER BY APPLY_TIME DESC ]]> 
		<![CDATA[ )  B
			WHERE 1=1 ]]>
		<![CDATA[ )]]> 
		
		
	</select>
	
<!-- 受理任务池分页多表查询操作 -->
	<select id="JRQD_queryAcceptTaskPool" resultType="java.util.Map" parameterType="java.util.Map">
	 <!-- <![CDATA[ SELECT B.RN AS ROWNUMBER, B.POLICY_CHG_ID,B.Change_Id,B.APPLY_CODE, B.ACCEPT_CODE, B.POLICY, B.POLICY_CODE,B.POLICY_PERSON, B.CUSTOMER_ID,B.TASK_STATE, B.ACCEPT_DATE 
		 FROM (
			SELECT  ROWNUM RN, PCH.POLICY_CHG_ID,APP.Change_Id,APP.APPLY_CODE, ACC.ACCEPT_CODE, ACC.SERVICE_CODE AS POLICY, PCH.POLICY_CODE,(SELECT TU.CUSTOMER_NAME  FROM APP___PAS__DBUSER.T_CUSTOMER TU WHERE TU.CUSTOMER_ID=PH.CUSTOMER_ID) AS POLICY_PERSON,PH.CUSTOMER_ID, APP.App_Status AS TASK_STATE, TO_CHAR(ACC.ACCEPT_TIME, 'YYYY-MM-DD') AS ACCEPT_DATE 
			FROM APP___PAS__DBUSER.T_CS_APPLICATION APP, APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC, APP___PAS__DBUSER.T_CS_POLICY_CHANGE PCH, APP___PAS__DBUSER.T_POLICY_HOLDER PH
			WHERE  APP.CHANGE_ID=PCH.CHANGE_ID(+) AND PCH.ACCEPT_ID =ACC.ACCEPT_ID(+) AND PCH.POLICY_ID = PH.POLICY_ID(+)  
					AND APP.TRY_CALC_NO IS NULL
					AND ROWNUM <= #{LESS_NUM}]]>
		<include refid="JRQD_acceptTaskPoolWhereConditions" />
		<if test=" task_state != null and task_state != ''  "><![CDATA[ AND APP.App_Status = #{task_state} ]]></if>
		<if test=" update_by != null and update_by != ''  "><![CDATA[ AND APP.UPDATE_BY = #{update_by} ]]></if>
		<![CDATA[ ORDER BY ACCEPT_TIME DESC ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>  -->
		
		SELECT B.RN,B.Change_Id,B.APPLY_CODE, B.TASK_STATE,B.BACK_STEP , B.IS_BEFOR_SCAN, B.IS_INTERACTIVE_JOINT
		FROM (
				SELECT ROWNUM RN,A.Change_Id,A.APPLY_CODE, A.TASK_STATE ,A.APPLY_TIME,A.BACK_STEP , A.IS_BEFOR_SCAN, A.IS_INTERACTIVE_JOINT 
				FROM (
					SELECT APP.IS_BEFOR_SCAN, APP.Change_Id,APP.APPLY_CODE, APP.App_Status AS TASK_STATE,APP.APPLY_TIME,APP.BACK_STEP, APP.IS_INTERACTIVE_JOINT
					FROM APP___PAS__DBUSER.T_CS_APPLICATION APP, 
					APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC, 
					APP___PAS__DBUSER.T_CS_POLICY_CHANGE PCH, APP___PAS__DBUSER.T_POLICY_HOLDER PH
					WHERE  APP.CHANGE_ID = PCH.CHANGE_ID(+) 
						AND PCH.ACCEPT_ID = ACC.ACCEPT_ID(+) 
						AND PCH.POLICY_ID = PH.POLICY_ID(+)  
						AND APP.TRY_CALC_NO IS NULL
						<include refid="JRQD_acceptTaskPoolWhereConditions" />
						<if test=" is_interactive_joint!= null and is_interactive_joint!= '' "><![CDATA[ AND APP.ORGAN_CODE = #{organ_code}]]></if>
						<if test=" is_interactive_joint== null or is_interactive_joint== '' "><![CDATA[ AND APP.ORGAN_CODE  LIKE '${organ_code}%'  ]]></if>
						<if test=" task_state != null and task_state != ''  "><![CDATA[ AND APP.App_Status = #{task_state} ]]></if>
						<if test=" update_by != null and update_by != ''  "><![CDATA[ AND APP.UPDATE_BY = #{update_by} ]]></if>
						<if test=" back_step != null and back_step != '' "><![CDATA[ AND APP.back_step = #{back_step} ]]></if>
						GROUP BY APP.Change_Id,APP.APPLY_CODE,APP.App_Status,APP.APPLY_TIME,APP.BACK_STEP,APP.IS_BEFOR_SCAN, APP.IS_INTERACTIVE_JOINT
						ORDER BY APP.APPLY_TIME DESC 
				) A     <![CDATA[
				WHERE  ROWNUM <= #{LESS_NUM} 
		) B
		WHERE B.RN > #{GREATER_NUM} ]]>	
			
		
	</select>
	
	<!-- 受理任务池分页多表查询操作数据准备 -->
	<select id="JRQD_queryAcceptTaskPoolForShow" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	 	SELECT ACC.ACCEPT_CODE, 
                   (SELECT TS.SERVICE_NAME FROM APP___PAS__DBUSER.T_SERVICE TS WHERE TS.SERVICE_CODE=ACC.SERVICE_CODE) AS POLICY,
                   TO_CHAR(ACC.ACCEPT_TIME, 'YYYY-MM-DD') AS ACCEPT_DATE,
                   PCC.POLICY_CODE,
                   TPH.CUSTOMER_ID,
                   (SELECT TC.CUSTOMER_NAME FROM APP___PAS__DBUSER.T_CUSTOMER TC WHERE TC.CUSTOMER_ID=TPH.CUSTOMER_ID) AS POLICY_PERSON
         FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC 
         	LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE PCC
         	ON PCC.ACCEPT_ID=ACC.ACCEPT_ID
         	LEFT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER TPH
            ON TPH.POLICY_ID=PCC.POLICY_ID    
         WHERE 1=1
         	AND ACC.accept_status NOT in('12') ]]>
            <if test=" change_id!= null and change_id!= '' "><![CDATA[ AND ACC.CHANGE_ID=#{change_id}]]></if>
         <![CDATA[ ORDER BY ACCEPT_DATE DESC ]]> 
      </select>
	
	
	<!-- 分页多表查询操作 -->
	<!-- <select id="JRQD_queryJoinApplicationPage" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ 
			
			SELECT A.RN AS rowNumber,A.* from(
			SELECT ROWNUM AS RN,T.*
			FROM (
				SELECT B.* FROM (
					SELECT 	A.CHANGE_ID,A.APPLY_CODE,A.Apply_Time,A.CUSTOMER_ID FROM T_CS_APPLICATION A where A.TRY_CALC_NO IS NULL and  not exists (select D.CHANGE_ID from T_CS_ACCEPT_CHANGE D where A.CHANGE_ID=D.CHANGE_Id)
			]]>
			<include refid="JRQD_csApplicationWhereCondition" />
	<![CDATA[)  B
 		 UNION
  			SELECT B.* FROM (
				SELECT 	A.CHANGE_ID,A.APPLY_CODE,A.Apply_Time,A.CUSTOMER_ID FROM T_CS_APPLICATION A , T_CS_ACCEPT_CHANGE C where( A.TRY_CALC_NO IS NULL AND A.CHANGE_ID=C.CHANGE_Id  AND (C.ACCEPT_STATUS='03' or C.ACCEPT_STATUS='04'))
			]]>
			<include refid="JRQD_csApplicationWhereCondition" />
	<![CDATA[
 		)  B) T  WHERE ROWNUM <= #{LESS_NUM} ]]>
		
		<![CDATA[ ORDER BY T.CHANGE_ID ]]> 
		<![CDATA[ )  A
			WHERE A.RN > #{GREATER_NUM} ]]>
	</select> -->
	
	<!-- 多表查询操作 -->
	<select id="JRQD_queryJoinApplicationForPage" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT A.APPLY_CODE ,A.CHANGE_ID,A.CUSTOMER_ID,S.ACCEPT_CODE,S.SERVICE_CODE,S.ACCEPT_STATUS,S.INSERT_TIME,S.POLICY_CODE
FROM (SELECT C.ACCEPT_ID,C.ACCEPT_CODE,C.SERVICE_CODE,C.ACCEPT_STATUS,C.INSERT_TIME,C.CHANGE_ID,P.POLICY_CODE FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C ,APP___PAS__DBUSER.T_CS_POLICY_CHANGE P 
WHERE C.ACCEPT_ID=P.ACCEPT_ID ) S,T_CS_APPLICATION A WHERE A.CHANGE_ID =S.CHANGE_ID ]]>
		<include refid="JRQD_csApplicationWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_CODE ]]> 

	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_findCsApplicationByCustID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO,   A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			 A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE, 
			 A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE, 
			 A.AGENT_TEL, A.AGENT_CODE , 
			A.TO_APPROVE_DATE,A.ESLEWHERE_FLAG,A.BACK_STEP,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE FROM APP___PAS__DBUSER.T_CS_APPLICATION A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csApplicationWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_TIME ]]> 
	</select>
		<!--by zhaoyoan_wb 根据业务员代码,保全申请提交日期,保全号,申请方式查询历次保全
		select RB_SERVICE_CODE from T_POLICY_REVERSAL where a.change_id=change_id -->
	<select id="JRQD_findGetMobileEdor" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT A.AGENT_CODE,
			       A.SERVICE_TYPE,
			       A.APPLY_TIME,
			       B.POLICY_CODE,
			       C.ACCEPT_STATUS,
			       (SELECT T.STATUS_DESC
			          FROM APP___PAS__DBUSER.T_APP_STATUS t
			         WHERE A.APP_STATUS = T.APP_STATUS_CODE) AS apply_status_name,
			       (SELECT STATUS_DESC
			          FROM APP___PAS__DBUSER.T_ACCEPT_STATUS
			         WHERE ACCEPT_STATUS = C.ACCEPT_STATUS) AS accept_status_name,
			       (SELECT CHANGE_STATUS
			          FROM APP___PAS__DBUSER.T_CHANGE
			         WHERE A.CHANGE_ID = CHANGE_ID) AS change_status,
			       B.ENDORSE_CODE,
			       A.AGENT_CERTI_TYPE,
			       C.FEE_AMOUNT,
			       A.INSERT_BY,
			       A.ORGAN_CODE,
			       (SELECT ORGAN_NAME
			          FROM APP___PAS__DBUSER.T_UDMP_ORG
			         WHERE A.ORGAN_CODE = ORGAN_CODE) AS organ_name,
			       (SELECT REAL_NAME
			          FROM APP___PAS__DBUSER.T_UDMP_USER
			         WHERE USER_ID = C.UPDATE_BY) AS real_name,
			       C.ACCEPT_CODE,
			       B.POLICY_ID,
			       B.SERVICE_CODE,
			       (SELECT CAUSE_NAME
			          FROM APP___PAS__DBUSER.T_REVERSAL_CAUSE
			         WHERE REVERSAL_CAUSE = C.ROLLBACK_CAUSE) AS rollback_cause_name,
			       (SELECT BACK_ENTYR_DESC
			          FROM APP___PAS__DBUSER.T_CS_BACK_ENTYR_CAUSE
			         WHERE C.BACK_ENTYR_CAUSE = BACK_ENTYR_CODE) AS back_entyr_desc
			  FROM APP___PAS__DBUSER.T_CS_APPLICATION   A,
			       APP___PAS__DBUSER.T_CS_POLICY_CHANGE B,
			       APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C
			 WHERE A.CHANGE_ID = B.CHANGE_ID
			   AND A.CHANGE_ID = C.CHANGE_ID
			   AND C.ACCEPT_ID=B.ACCEPT_ID
			   AND C.ACCEPT_STATUS != '24'
			   AND C.ACCEPT_STATUS != '25'
			   AND A.APPLY_TIME >= #{edor_app_start_date}
			   AND A.APPLY_TIME <= #{edor_app_end_date}
			   AND A.SERVICE_TYPE = #{service_type}
			   AND EXISTS (SELECT 1
			          FROM APP___PAS__DBUSER.T_CONTRACT_AGENT
			         WHERE POLICY_ID = B.POLICY_ID
			           AND AGENT_CODE = #{agent_code})			
			]]>
		<if test="policy_code !=null and policy_code!=''">
			<![CDATA[
				AND B.POLICY_CODE=#{policy_code} 
			]]>
		</if>
	</select>
	
		<select id="JRQD_MOB_findAllService" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT ROWNUM,S.SERVICE_CODE,S.SERVICE_NAME FROM APP___PAS__DBUSER.T_SERVICE S
		]]>
	</select>
	
	<select id="JRQD_queryIdentifyNO" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[SELECT A.CUSTOMER_CERTI_CODE AS CERTI_NO, A.CUSTOMER_CERT_TYPE AS CERTI_TYPE, A.CUSTOMER_NAME AS CUS_NAME FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE ROWNUM <=  1000]]> 
    	<if test="customer_id != null and customer_id != ''">
    		<![CDATA[AND A.CUSTOMER_ID = #{customer_id} ]]>
    	</if>
	</select>
	
	<!-- 根据受理号查询申请信息 -->
	<select id="JRQD_queryApplicationByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.ACCEPT_CODE,B.SERVICE_CODE,A.APPLICANT_TYPE, A.TRY_CALC_NO,  A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			 A.APP_STATUS,  A.NOFILL_FLAG, 
			A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE,
			 A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE, 
			  A.AGENT_TEL, A.AGENT_CODE, 
			A.TO_APPROVE_DATE,A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT,A.BACK_STEP ,null as AGENT_CERT_STAR_DATE,null as AGENT_CERT_END_DATE
			FROM APP___PAS__DBUSER.T_CS_APPLICATION A
			LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
			ON A.change_id = B.change_id
			WHERE 1 = 1 AND  B.accept_code = #{accept_code}
		]]>
	</select>
	<!-- 判断是客户的身份 -->
	<select id="JRQD_isHoliderOrInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TO_CHAR(REPLACE(WM_CONCAT(ACOUNT), ',', '')) CUSTOMER_RULE
			    FROM (SELECT COUNT(A.LIST_ID) ACOUNT
			            FROM APP___PAS__DBUSER.T_POLICY_HOLDER A
			           WHERE A.POLICY_ID = #{policy_id}
			             AND A.CUSTOMER_ID = #{customer_id}
			          UNION ALL
			          SELECT COUNT(B.LIST_ID) ACOUNT
			            FROM APP___PAS__DBUSER.T_INSURED_LIST B
			           WHERE B.POLICY_ID = #{policy_id}
			             AND B.CUSTOMER_ID = #{customer_id})
				]]>
	</select>
	
	<select id="JRQD_queryCTFeeAmountNumber" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT sum(S.FEE_AMOUNT) as FEE_AMOUNT
			  FROM DEV_PAS.T_SURRENDER S
			 WHERE 1 = 1 AND S.ACCEPT_CODE ='${accept_code}'
		]]>
	</select>
	<!-- 保单下是否有有效的补发记录 -->
	<select id="JRQD_isHaveLRLogByPolicyCode" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			select count(1)
			  from dev_pas.t_cs_policy_change t
			 inner join dev_pas.t_cs_accept_change tcac
			    on t.accept_id = tcac.accept_id
			 where t.policy_code = #{policy_code}
			 and tcac.service_code = 'LR'
			   and tcac.accept_code = '18'
		]]>
	</select>
	
	
	<!-- 获取保单上已经完成的保全申请提交日期 -->
	<select id="JRQD_findMaxCsAppTimeByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT TCAC.SERVICE_CODE
			  FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC
			 INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC
			    ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
			 INNER JOIN APP___PAS__DBUSER.T_CS_APPLICATION TCA
			    ON TCPC.CHANGE_ID = TCA.CHANGE_ID
			 WHERE TCPC.POLICY_CODE = '${policy_code}'
			   AND TCAC.SERVICE_CODE NOT IN ('SR','RB','RG','XQ')
			   AND TCAC.ACCEPT_STATUS = '18'
			   AND TCA.APPLY_TIME > #{apply_time}
		]]>
	</select>
	
	
	<!-- 根据申请id查询申请数据 -->	
	<select id="JRQD_queryCsApplicationByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT A.IS_INTERACTIVE_JOINT FROM APP___PAS__DBUSER.T_CS_APPLICATION A WHERE 1 = 1
		<if test=" change_id  != null and change_id !='' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
	</select>
	
	<!--柜面：手动分配任务   查询受理节点 -->
	<select id="JRQD_queryCsApplyTaskList" resultType="java.util.Map" parameterType="java.util.Map">

	SELECT * FROM (
	SELECT ROWNUM RN ,T.* FROM (	
		SELECT   
		   D.APPLY_CODE,
		   D.APPLY_TIME,
		   D.CHANGE_ID,
		   D.APP_STATUS,    
	       LISTAGG(D.SERVICE_TYPE, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS SERVICE_TYPE,	  
	       LISTAGG(D.POLICY_CODE, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS POLICY_CODE,    
	       LISTAGG(D.ACCEPT_CODE, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS ACCEPT_CODE,
	       LISTAGG(D.SERVICE_CODE, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS SERVICE_CODE,
	       LISTAGG(D.ACCEPT_TIME, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS ACCEPT_TIME,  
	       LISTAGG(OPERATOR_USER, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS OPERATOR_USER,
	       LISTAGG(TO_CHAR(D.LAST_DATE,'YYYY-MM-DD hh24:mm:ss'), '/') within group(order by D.ACCEPT_CODE) AS LAST_DATE 
	       FROM (	
				SELECT
					PCH.POLICY_CODE,
					APP.APPLY_CODE,				
					APP.CHANGE_ID,
					ACC.ACCEPT_CODE,				
					(SELECT TS.TYPE_NAME
	                  FROM APP___PAS__DBUSER.T_SERVICE_TYPE TS
	                 WHERE TS.SERVICE_TYPE = APP.SERVICE_TYPE) AS SERVICE_TYPE,
					APP.APPLY_TIME,
					TO_CHAR(ACC.ACCEPT_TIME,'YYYY-MM-DD') ACCEPT_TIME,
					APP_STATUS,	
					(SELECT SSS.SERVICE_NAME FROM DEV_PAS.T_SERVICE SSS WHERE SSS.SERVICE_CODE=ACC.SERVICE_CODE) SERVICE_CODE,
					(SELECT U.USER_NAME FROM APP___PAS__DBUSER.T_UDMP_USER U WHERE U.USER_ID=APP.UPDATE_BY) OPERATOR_USER,							
					nvl(ACC.UPDATE_TIME,APP.UPDATE_TIME) LAST_DATE
				FROM APP___PAS__DBUSER.T_CS_APPLICATION APP,
					APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC,
					APP___PAS__DBUSER.T_CS_POLICY_CHANGE PCH
			
				WHERE  APP.CHANGE_ID = PCH.CHANGE_ID(+)
					AND PCH.ACCEPT_ID = ACC.ACCEPT_ID(+)
			    	AND APP.TRY_CALC_NO IS NULL 
			    	AND ACC.ACCEPT_STATUS(+) !='12'
	
	<if test=" operator_user!= null and operator_user!= '' "><![CDATA[ AND APP.UPDATE_BY = (SELECT US.USER_ID  FROM APP___PAS__DBUSER.T_UDMP_USER US WHERE US.USER_NAME =#{operator_user}  AND US.USER_DISABLE='N' )]]></if>
	<if test=" app_status!= null and app_status!= '' "><![CDATA[ AND APP_STATUS=#{app_status}]]></if>
	<if test=" apply_code!= null and apply_code!= '' "><![CDATA[ AND APP.APPLY_CODE=#{apply_code}]]></if>
    <if test=" accept_code!= null and accept_code!= '' "><![CDATA[ AND ACC.ACCEPT_CODE=#{accept_code}]]></if>
    <if test=" policy_code!= null and policy_code!= '' "><![CDATA[ AND PCH.POLICY_CODE=#{policy_code}]]></if>
    <if test=" apply_time!= null and apply_time!= '' "><![CDATA[ AND PCH.APPLY_TIME=#{apply_time}]]></if>
    
    
    <if test="applyCodeList  != null and applyCodeList.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="applyCodeList" item="apply_code_item"
				index="index" open="" close="" separator="OR">
				<![CDATA[ APP.APPLY_CODE= #{apply_code_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
   <if test="appStatusList  != null and appStatusList.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="appStatusList" item="app_status_item"
				index="index" open="" close="" separator="OR">
				<![CDATA[ APP.APP_STATUS= #{app_status_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if> 
		
	<![CDATA[ ORDER BY APP.APPLY_TIME DESC)D
		GROUP BY  
		          APPLY_TIME,
		          APPLY_CODE,
				  CHANGE_ID,
				  APP_STATUS 
	      ) T 	
	 ) B 
	 WHERE B.RN <= #{LESS_NUM} AND B.RN>#{GREATER_NUM}
	]]>
	
	</select>
	
	
	<!-- 手动分配任务查询受理节点 -->
	<select id="JRQD_queryCsApplyTaskCount" resultType="java.lang.Integer"
	parameterType="java.util.Map">
	SELECT COUNT(1) FROM (
		SELECT APPLY_CODE FROM (
				SELECT
					PCH.POLICY_CODE,
					APP.APPLY_CODE,				
					APP.CHANGE_ID,
					ACC.ACCEPT_CODE,				
					APP.SERVICE_TYPE,
					APP.APPLY_TIME,
					TO_CHAR(ACC.ACCEPT_TIME,'YYYY-MM-DD') ACCEPT_TIME,
					APP_STATUS,	
					(SELECT SSS.SERVICE_NAME FROM DEV_PAS.T_SERVICE SSS WHERE SSS.SERVICE_CODE=ACC.SERVICE_CODE) SERVICE_CODE,
					(SELECT U.USER_NAME FROM APP___PAS__DBUSER.T_UDMP_USER U WHERE U.USER_ID=ACC.UPDATE_BY) OPERATOR_USER,							
					nvl(ACC.UPDATE_TIME,APP.UPDATE_TIME) LAST_DATE
				FROM APP___PAS__DBUSER.T_CS_APPLICATION APP,
					APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC,
					APP___PAS__DBUSER.T_CS_POLICY_CHANGE PCH
			
				WHERE  APP.CHANGE_ID = PCH.CHANGE_ID(+)
					AND PCH.ACCEPT_ID = ACC.ACCEPT_ID(+)
			    	AND APP.TRY_CALC_NO IS NULL 
			    	AND ACC.ACCEPT_STATUS(+) !='12'
	
	<if test=" operator_user!= null and operator_user!= '' "><![CDATA[ AND APP.UPDATE_BY = (SELECT US.USER_ID  FROM APP___PAS__DBUSER.T_UDMP_USER US WHERE US.USER_NAME =#{operator_user} AND US.USER_DISABLE='N' )]]></if>
	<if test=" app_status!= null and app_status!= '' "><![CDATA[ AND APP_STATUS=#{app_status}]]></if>
	<if test=" apply_code!= null and apply_code!= '' "><![CDATA[ AND APP.APPLY_CODE=#{apply_code}]]></if>
	<if test=" accept_code!= null and accept_code!= '' "><![CDATA[ AND ACC.ACCEPT_CODE=#{accept_code}]]></if>
	<if test=" policy_code!= null and policy_code!= '' "><![CDATA[ AND PCH.POLICY_CODE=#{policy_code}]]></if>
	<if test=" apply_time!= null and apply_time!= '' "><![CDATA[ AND PCH.APPLY_TIME=#{apply_time}]]></if>
	
     <if test="applyCodeList  != null and applyCodeList.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="applyCodeList" item="apply_code_item"
				index="index" open="" close="" separator="OR">
				<![CDATA[ APP.APPLY_CODE= #{apply_code_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		
	 <if test="appStatusList  != null and appStatusList.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="appStatusList" item="app_status_item"
				index="index" open="" close="" separator="OR">
				<![CDATA[ APP.APP_STATUS= #{app_status_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
    
	    <![CDATA[ORDER BY APP.APPLY_TIME DESC)D
				GROUP BY  
				          APPLY_TIME,
				          APPLY_CODE,
						  CHANGE_ID,
						  APP_STATUS 
			      ) T 	
		   ]]>
	 </select>
	
	<!-- 柜面：手动分配任务查询受理节点 -->
	<select id="JRQD_queryCsEntryTaskList" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
	SELECT CHANGE_ID,
	       D.APPLY_CODE,
	       APPLY_TIME,
	       LISTAGG(ACCEPT_CODE, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS ACCEPT_CODE,
	       LISTAGG(POLICY_CODE, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS POLICY_CODE,
	       LISTAGG(SERVICE_CODE, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS SERVICE_CODE,
	       LISTAGG(SERVICE_TYPE, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS SERVICE_TYPE,	      
	       LISTAGG(ACCEPT_TIME, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS ACCEPT_TIME,
	       LISTAGG(OPERATOR_USER, '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS OPERATOR_USER,
	       LISTAGG(REVIEW_RESULT, '/') WITHIN GROUP(ORDER BY D.REVIEW_RESULT) AS TASK_NODE,  
	       LISTAGG(to_CHAR(LAST_DATE, 'yyyy-MM-dd hh24:mm:ss'), '/') WITHIN GROUP(ORDER BY D.ACCEPT_CODE) AS LAST_DATE
	  FROM (SELECT TCA.CHANGE_ID,
	               TCA.APPLY_CODE,
	               TCAC.ACCEPT_CODE,
	               TCPC.POLICY_CODE,
	               TCA.APPLY_TIME,
	               TCAC.REVIEW_RESULT,
	               TO_CHAR(TCAC.ACCEPT_TIME,'YYYY-MM-DD') ACCEPT_TIME,	               
	               
	               (SELECT TS.SERVICE_NAME
	                  FROM APP___PAS__DBUSER.T_SERVICE TS
	                 WHERE TS.SERVICE_CODE = TCPC.SERVICE_CODE) AS SERVICE_CODE,
	               
	               (SELECT TS.TYPE_NAME
	                  FROM APP___PAS__DBUSER.T_SERVICE_TYPE TS
	                 WHERE TS.SERVICE_TYPE = TCA.SERVICE_TYPE) AS SERVICE_TYPE,
	               	               
	               (SELECT U.USER_NAME
	                  FROM APP___PAS__DBUSER.T_UDMP_USER U
	                 WHERE U.USER_ID = TCAC.INSERT_BY) OPERATOR_USER,
	                 
	               nvl(TCAC.UPDATE_TIME, TCA.UPDATE_TIME) LAST_DATE
	        
	          FROM APP___PAS__DBUSER.T_CS_APPLICATION TCA
	          LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC
	            ON TCA.CHANGE_ID = TCAC.CHANGE_ID
	          LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC
	            ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID	        
	         WHERE TCAC.ACCEPT_STATUS != '12'		
		
		]]>
		<if test="applyCodeList  != null and applyCodeList.size()!=0 ">
			<![CDATA[ AND (]]>
				<foreach collection="applyCodeList" item="apply_code_item"
					index="index" open="" close="" separator="OR">
						<![CDATA[ TCA.APPLY_CODE= #{apply_code_item} ]]>
				</foreach>
			<![CDATA[) ]]>
		</if>
		<![CDATA[  
		       ) D   GROUP BY  APPLY_TIME,APPLY_CODE,CHANGE_ID
		]]>			
	   
	</select>
	
	
	<!-- 柜面手工任务分配，受理任务修改操作 -->
	<update id="JRQD_updateCsApplicationForHand" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_APPLICATION ]]>
		<set>
		<trim suffixOverrides=",">
		
		<if test="app_status != null">
			APP_STATUS = #{app_status, jdbcType=VARCHAR} ,
		</if>

		UPDATE_TIME = SYSDATE,
		UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		UPDATE_BY = #{update_by, jdbcType=NUMERIC} 
		</trim>
		</set>
		<![CDATA[ WHERE CHANGE_ID = #{change_id} ]]>
	</update>
	
	
	
	
	
	<!-- 查询数据迁移 -->
	<select id="JRQD_findAllSjqyLog" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT NVL(SUM(A.SJQY_POLICY_CNT),0) SJQY_POLICY_CNT FROM DEV_PAS.T_SJQY_LOG  A 
	 WHERE A.SJQY_ORGAN=#{organ_code}  AND A.SJQY_DATE >= #{lon_date}
	</select>
	
	
	<!-- <select id="JRQD_queryEntryTaskPoolCheckForShowNew" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT 
	 D.APPLY_CODE,
	 D.ACCEPT_CODE,
	 LISTAGG(D.POLICY_CODE, '/') within group(order by D.POLICY_CODE) AS POLICY_CODE,
	 LISTAGG(D.ORGAN_NAME, '/') within group(order by D.ORGAN_NAME) AS ORGAN_NAME,
	 LISTAGG(D.SERVICE_CODE, '/') within group(order by D.SERVICE_CODE) AS SERVICE_CODE, 
	 D.SERVICE_TYPE,
	 LISTAGG(D.AREA_NAME, '/') within group(order by D.AREA_NAME) AS AREA_NAME, 
	 LISTAGG(D.CONVEN_FLAG, '/') within group(order by D.CONVEN_FLAG) AS CONVEN_FLAG,
	 APPLY_TIME,
	 LISTAGG(D.URGENT_FLAG, '/') within group(order by D.URGENT_FLAG) AS URGENT_FLAG,
	 LISTAGG(IS_INTERACTIVE_JOINT, '/') WITHIN GROUP(ORDER BY D.IS_INTERACTIVE_JOINT) AS IS_INTERACTIVE_JOINT_CHA,
	 LISTAGG(REVIEW_RESULT, '/') WITHIN GROUP(ORDER BY D.REVIEW_RESULT) AS REVIEW_RESULT
	 
	FROM (SELECT 
	        TCA.APPLY_CODE,
	        TCAC.ACCEPT_CODE,
	        TCPC.POLICY_CODE,
	       (select A.ORGAN_NAME FROM APP___PAS__DBUSER.T_UDMP_ORG_REL A WHERE  A.ORGAN_CODE =TCPC.ORGAN_CODE )ORGAN_NAME ,
	        AREM.AREA_NAME/*片区*/,
	       (SELECT SSS.SERVICE_NAME FROM DEV_PAS.T_SERVICE SSS WHERE SSS.SERVICE_CODE=TCPC.SERVICE_CODE) SERVICE_CODE  , 
	       TCA.SERVICE_TYPE /*申请方式*/,
	        NVL(TCAC.CONVEN_FLAG,1) CONVEN_FLAG /*常规业务标识*/,
	       TCA.APPLY_TIME /*申请提交日期*/,
	       TCAC.URGENT_FLAG/*加急件标示*/,
	       DECODE(TCA.IS_INTERACTIVE_JOINT, 1, '是', '否') IS_INTERACTIVE_JOINT,
	       DECODE(NVL(TCAC.REVIEW_RESULT, 0), 2, 1, 0) REVIEW_RESULT
	  FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC
	  LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC
	    ON TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
	  LEFT JOIN APP___PAS__DBUSER.T_CS_APPLICATION TCA
	    ON TCAC.CHANGE_ID = TCA.CHANGE_ID
	  LEFT JOIN (SELECT AR.AREA_NAME, ORG.ORGAN_CODE
	               FROM APP___PAS__DBUSER.T_CS_AREA AR
	               LEFT JOIN APP___PAS__DBUSER.T_CS_AREA_ORGAN ORG
	                 ON ORG.AREA_ID = AR.AREA_ID) AREM
	    ON AREM.ORGAN_CODE = TCAC.ORGAN_CODE
	 where 1=1  
	 
	 /*and TCA.Apply_Code = 'z6120181101052113'*/
	 AND TCAC.ACCEPT_CODE = #{accept_code}
	 
	 
	 
	 ) D 
	 GROUP BY D.APPLY_CODE,D.SERVICE_TYPE,D.APPLY_TIME,D.ACCEPT_CODE
	
	</select> -->
	
	<select id="JRQD_find_contract_busi_prod_allinfo"  resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select * from dev_pas.t_contract_busi_prod bp
      where bp.policy_id in(select policy_id from dev_pas.t_policy_holder ph where ph.customer_id
        in(select customer_id from dev_pas.t_policy_holder  where policy_id=#{policy_id}))
        and exists(select 1 from dev_pas.t_contract_master cm where cm.policy_id=bp.policy_id and cm.liability_state!=3 and cm.liability_state!=4)
        and bp.policy_id !=#{policy_id}]]>
	</select>
<!-- 	根据保全的类型查出保全所属保单层还是客户层等 -->
	<select id="JRQD_queryServiceTypeByServiceCode"  resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select service_type from APP___PAS__DBUSER.T_CS_SERVICE_ORG_CFG where service_code = #{service_code}]]>
	</select>
	
	<!-- 	根据保单号查询是否是上海医保的数据 -->
	<select id="JRQD_querySHPolicyNumByPolicyCode"  resultType="Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(*) FROM APP___PAS__DBUSER.T_CONTRACT_MEDICAL TCM  WHERE 1=1 
                  AND EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B WHERE  B.POLICY_CODE=TCM.POLICY_CODE 
                  AND B.BUSI_PROD_CODE IN ('00557000', '00558000','00868000'))
                  AND TCM.POLICY_CODE=#{policy_code}
				  ]]>
	</select>
	
		<!-- 	根据保单号查询当前保单的终止原因 -->
	<select id="JRQD_queryEndCause"  resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select a.end_cause
				          from APP___PAS__DBUSER.t_contract_master a
				         where a.policy_code = #{policy_code}]]>
	</select>
	
<!-- 	需要修改  从t_cs表里面查询 状态为new的数据
		根据变更号查询当前保单的医保卡号 目前表里面还没有 暂时用上海医保平台投保流水号代替
	<select id="JRQD_selectMedicalNo"  resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[select b.policy_former_no as medical_no
  from APP___PAS__DBUSER.t_prem_arap_medical b
 where b.apply_code = (select a.apply_code
                         from APP___PAS__DBUSER.t_cs_application a
                        where a.change_id =  #{change_id})]]>
	</select> -->
	
	<!-- 查询数据迁移 -->
	<select id="JRQD_findAllSjqyOrgan" resultType="java.util.Map" parameterType="java.util.Map">
	 SELECT A.SJQY_ORGAN,A.SJQY_DATE,A.SJQY_POLICY_CNT FROM APP___PAS__DBUSER.T_SJQY_LOG  A 
	</select>
	
	<!-- 手工任务分配清单查询 -->
	<select id="JRQD_queryAssignTaskList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TA.APPLY_CODE, /*申请号*/
			       TB.ACCEPT_CODE, /*受理号*/
			       (SELECT T.SERVICE_NAME
			          FROM APP___PAS__DBUSER.T_SERVICE T
			         WHERE T.SERVICE_CODE = TB.SERVICE_CODE) SERVICE_NAME, /*保全项*/
			      
			       (SELECT LISTAGG(TC.POLICY_CODE, '/') WITHIN GROUP(ORDER BY TB.ACCEPT_CODE)
			          FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE TC
			         WHERE TC.CHANGE_ID = TB.CHANGE_ID) POLICY_CODE_STR, /*保单号*/
			      
			       TA.CUSTOMER_ID, /*申请人客户号*/
			       TB.ORGAN_CODE, /*受理机构代码*/
			       (SELECT TT.ORGAN_NAME
			          FROM APP___PAS__DBUSER.T_UDMP_ORG_REL TT
			         WHERE TT.ORGAN_CODE = TB.ORGAN_CODE) ORGAN_NAME, /*受理机构名称*/
			       (SELECT TST.TYPE_NAME
			          FROM APP___PAS__DBUSER.T_SERVICE_TYPE TST
			         WHERE TST.SERVICE_TYPE = TA.SERVICE_TYPE) SERVICE_TYPE, /*申请方式*/
			       
			       (SELECT LISTAGG(TU.USER_NAME, '/') WITHIN GROUP(ORDER BY TD.BIZ_KEY, TD.OP_TIME ASC)
			          FROM APP___PAS__DBUSER.T_BIZ_OPERATION TD,APP___PAS__DBUSER.T_UDMP_USER TU
			         WHERE TD.BIZ_KEY = TB.ACCEPT_CODE
			           AND TD.INSERT_BY = TU.USER_ID
			           AND TD.EVENT_CODE IN ('10605', '10606')) AS ASSIGN_USER_STR, /*分配用户*/
			       
			       (SELECT LISTAGG(CASE
			                 WHEN TD.EVENT_CODE = '10605' THEN
			                  'SYSADMIN'
			                 ELSE
			                  (SELECT TU.REAL_NAME
                                     FROM APP___PAS__DBUSER.T_UDMP_USER TU
                                    WHERE TU.USER_NAME = TD.REMARK
                                      AND ROWNUM = 1)
			               END,
			               '/') WITHIN GROUP(ORDER BY TD.BIZ_KEY, TD.OP_TIME ASC)
			          FROM APP___PAS__DBUSER.T_BIZ_OPERATION TD
			         WHERE TD.BIZ_KEY = TB.ACCEPT_CODE
			           AND TD.EVENT_CODE IN ('10605', '10606')) AS REVIEW_USER_STR, /*目标复核人*/
			       
			       (SELECT LISTAGG(TO_CHAR(TD.OP_TIME,'yyyy-MM-dd'), '/') WITHIN GROUP(ORDER BY TD.BIZ_KEY, TD.OP_TIME ASC)
			          FROM APP___PAS__DBUSER.T_BIZ_OPERATION TD
			         WHERE TD.BIZ_KEY = TB.ACCEPT_CODE
			           AND TD.EVENT_CODE IN ('10605', '10606')) AS ASSIGN_DATE_STR, /*任务分配时间*/
			       
			       (SELECT COUNT(1)
			          FROM APP___PAS__DBUSER.T_BIZ_OPERATION TD
			         WHERE TD.BIZ_KEY = TB.ACCEPT_CODE
			           AND TD.EVENT_CODE IN ('10605', '10606')) ASSIGN_NUM, /*任务分配次数*/
			       
			       DECODE((SELECT nvl(COUNT(1), 0)
			                FROM APP___PAS__DBUSER.T_BIZ_OPERATION TD
			               WHERE TD.BIZ_KEY = TB.ACCEPT_CODE
			                 AND TD.EVENT_CODE IN ('10605')),
			              0,
			              '否',
			              '是') IS_PUBLIC, /**是否分配回共享池*/
			       
			       (SELECT SUM(CASE
                             WHEN T.ARAP_FLAG = 1 THEN /*收费*/
                              T.FEE_AMOUNT
                             WHEN T.ARAP_FLAG = 2 THEN /*付费*/
                              -T.FEE_AMOUNT
                             ELSE
                              0
                           END) AS FEE_AMOUNT
                  FROM APP___PAS__DBUSER.T_CS_PREM_ARAP T
                 WHERE T.FEE_STATUS = '00'
                   AND T.BUSINESS_CODE = TB.ACCEPT_CODE) AS FEE_AMOUNT, /*收付费金额*/
                   
			       (SELECT TAS.STATUS_DESC
			          FROM APP___PAS__DBUSER.T_APP_STATUS TAS
			         WHERE TAS.APP_STATUS_CODE = TA.APP_STATUS) APP_STATUS, /*保全申请状态*/
			       
			       TA.APPLY_TIME, /*申请提交日*/
			       (SELECT TO_CHAR(MAX(A.OP_TIME), 'YYYY-MM-DD')
	                  FROM APP___PAS__DBUSER.T_BIZ_OPERATION A
	                 WHERE A.BIZ_KEY = TB.ACCEPT_CODE
	                   AND A.EVENT_CODE = '10302') ACCEPT_TIME, /*申请确认日,最后一次录入完成的日期*/
       			   TO_CHAR(TB.REVIEW_TIME,'YYYY-MM-DD') REVIEW_TIME, /*复核日期*/
			       
			       (SELECT T.STATUS_DESC
			          FROM APP___PAS__DBUSER.T_ACCEPT_STATUS T
			         WHERE T.ACCEPT_STATUS = TB.ACCEPT_STATUS) ACCEPT_STATUS /*保全受理状态*/
			
			  FROM APP___PAS__DBUSER.T_CS_APPLICATION TA
			 INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TB
			    ON TA.CHANGE_ID = TB.CHANGE_ID
			 INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TC
			    ON TB.ACCEPT_ID = TC.ACCEPT_ID			
			 WHERE EXISTS (SELECT 1
			          FROM APP___PAS__DBUSER.T_BIZ_OPERATION TBO
			         WHERE TBO.BIZ_KEY = TB.ACCEPT_CODE
			           AND TBO.EVENT_CODE IN ('10605', '10606')
			]]>
				<!-- 分配人 -->
				<if test=" assign_user != null and assign_user != ''">
				<![CDATA[ AND EXISTS 
					(SELECT 1 FROM APP___PAS__DBUSER.T_UDMP_USER TU 
						WHERE TBO.INSERT_BY=TU.USER_ID 
						  AND  TU.USER_NAME = #{assign_user})
				]]>
				</if>
				<!-- 目标复核人 -->
				<if test=" review_user != null and review_user != ''"><![CDATA[ AND TBO.REMARK = #{review_user}]]></if>
			<![CDATA[ ) ]]>
		<if test=" date_start != null and date_start != ''"><![CDATA[ AND TA.APPLY_TIME >=  TO_DATE(#{date_start},'yyyy-MM-dd')   ]]></if>
		<if test=" date_end != null and date_end != '' "><![CDATA[ AND TA.APPLY_TIME <= TO_DATE(#{date_end},'yyyy-MM-dd')   ]]></if>
	</select>
	
	<!-- 查询用户权限 -->
	<select id="JRQD_queryUserPermission" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[  
           SELECT rr.result_value_1 as result
		  FROM APP___PAS__DBUSER.T_UDMP_GROUP_USER           gu,
		       APP___PAS__DBUSER.T_UDMP_ROLE_GROUP           TG,
		       APP___PAS__DBUSER.T_UDMP_ROLE                 A,
		       APP___PAS__DBUSER.T_UDMP_GROUP_ROLE           gr,
		       APP___PAS__DBUSER.t_udmp_role_permission      rp,
		       APP___PAS__DBUSER.T_UDMP_PERMISSION_TYPE      t,
		       APP___PAS__DBUSER.t_udmp_permission_ratetable rr,
		       APP___PAS__DBUSER.t_Udmp_User                 tu
		 where a.role_id = gr.role_id
		   and gr.role_group_id = gu.role_group_id
		   and gu.role_group_id = tg.role_group_id
		   and rp.role_id = gr.role_id
		   and rp.permission_type_id = t.permission_type_id
		   and gu.user_id = tu.user_id
		   and a.role_type = 2 /*数据权限*/
		   and t.permission_type_name = #{type} /*权限类型*/
		   and tu.user_id=#{user_id} ]]>
	</select>
	<!-- 查询用户权限 -->
	<select id="JRQD_queryAgentCertValidity" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[  
         	 select null as agent_cert_star_date, null as agent_cert_end_date
			   from dev_pas.t_cs_application t
			  where t.agent_code = #{agent_code}
			  and rownum = 1
-- 			  and t.agent_cert_star_date is not null
-- 			  and t.agent_cert_end_date is not null
			  order by t.apply_time desc
           ]]>
	</select>
	
</mapper>
