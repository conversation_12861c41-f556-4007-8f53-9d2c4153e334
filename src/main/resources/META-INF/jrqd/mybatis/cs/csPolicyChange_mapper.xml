<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.model.dao.impl.ICsPolicyChangeDao">

	<sql id="JRQD_csPolicyChangeWhereCondition">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" change_flag != null and change_flag != ''  "><![CDATA[ AND A.CHANGE_FLAG = #{change_flag} ]]></if>
		<if test=" apply_time  != null  and  apply_time  != ''  "><![CDATA[ AND A.APPLY_TIME = #{apply_time} ]]></if>
		<if test=" hesitate_flag  != null "><![CDATA[ AND <PERSON>.HESITATE_FLAG = #{hesitate_flag} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" endorse_code != null and endorse_code != ''  "><![CDATA[ AND A.ENDORSE_CODE = #{endorse_code} ]]></if>
		<if test=" policy_lock_flag != null and policy_lock_flag != ''  "><![CDATA[ AND A.POLICY_LOCK_FLAG = #{policy_lock_flag} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" validate_time  != null  and  validate_time  != ''  "><![CDATA[ AND A.VALIDATE_TIME = #{validate_time} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" print_num  != null "><![CDATA[ AND A.PRINT_NUM = #{print_num} ]]></if>
		<if test=" policy_copy_flag != null and policy_copy_flag != ''  "><![CDATA[ AND A.POLICY_COPY_FLAG = #{policy_copy_flag} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" notify_type != null and notify_type != ''  "><![CDATA[ AND A.NOTIFY_TYPE = #{notify_type} ]]></if>
		<if test=" print_status != null and print_status != ''  "><![CDATA[ AND A.PRINT_STATUS = #{print_status} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" print_type != null and print_type != ''  "><![CDATA[ AND A.PRINT_TYPE = #{print_type} ]]></if>
		<if test=" delay_cause != null and delay_cause != ''  "><![CDATA[ AND A.DELAY_CAUSE = #{delay_cause} ]]></if>
		<if test="change_ids  != null and change_ids.size()!=0">
			<![CDATA[ AND A.CHANGE_ID IN ]]>
			<foreach collection="change_ids" item="change_ids"
				index="index" open="(" close=")" separator=",">#{change_ids}</foreach></if>
		<if test="accept_ids  != null and accept_ids.size()!=0">
			<![CDATA[ AND A.ACCEPT_ID IN (]]>
			<foreach collection="accept_ids" item="accept_id_item"
				index="index" open="" close="" separator=",">#{accept_id_item}</foreach>
			<![CDATA[)]]>
		</if>
		<if test="policyCodes!= null and policyCodes.size()!=0">
			<![CDATA[ AND A.POLICY_CODE IN (]]>
			<foreach collection="policyCodes" item="policy_codes"
				index="index" open="" close="" separator=",">#{policy_codes}</foreach>
			<![CDATA[)]]>
		</if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_ID = (select accept_id from APP___PAS__DBUSER.t_cs_accept_change acc where acc.accept_code=#{accept_code}) ]]></if>
	
		<if test=" reprint_flag != null and reprint_flag != ''  "><![CDATA[ AND A.REPRINT_FLAG = #{reprint_flag} ]]></if>
		<if test=" print_mode != null and print_mode != ''  "><![CDATA[ AND A.PRINT_MODE = #{print_mode} ]]></if>     
		<if test=" sh_flag != null and sh_flag != '' and sh_flag == '1'.toString() ">
			<![CDATA[ AND EXISTS (SELECT 'X' FROM  APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD CBP 
                           WHERE  CBP.CHANGE_ID = A.CHANGE_ID AND CBP.POLICY_CODE= A.POLICY_CODE 
                           AND  CBP.OLD_NEW ='1' AND CBP.BUSI_PROD_CODE IN ('00842000','00843000','00868000') )
			]]>
		</if>
		<if test=" sh_flag != null and sh_flag != '' and sh_flag == '2'.toString() ">
			<![CDATA[ AND NOT EXISTS (SELECT 'X' FROM  APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD CBP 
                           WHERE  CBP.CHANGE_ID = A.CHANGE_ID AND CBP.POLICY_CODE= A.POLICY_CODE 
                           AND  CBP.OLD_NEW ='1' AND CBP.BUSI_PROD_CODE IN ('00842000','00843000','00868000') )
			]]>
		</if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsPolicyChangeByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	


<!-- 添加操作 -->
	<insert id="JRQD_addCsPolicyChange"  useGeneratedKeys="false"  parameterType="java.util.Map">
			<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="policy_chg_id">
			SELECT APP___PAS__DBUSER.S_CS_POLICY_CHANGE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_POLICY_CHANGE(
				RELATED_ID, CHANGE_FLAG, ENDORSE_CODE, PRINT_STATUS, APPLY_TIME, HESITATE_FLAG, FINISH_TIME,  
				INSERT_TIMESTAMP, POLICY_LOCK_FLAG, ORGAN_CODE, VALIDATE_TIME, UPDATE_BY, CHANGE_ID, 
				FEE_AMOUNT, POLICY_COPY_FLAG, POLICY_CHG_ID, SERVICE_CODE, POLICY_ID, ACCEPT_ID, NOTIFY_TYPE, 
				INSERT_TIME, UPDATE_TIME, ORDER_ID, POLICY_CODE , PRINT_NUM,
				UPDATE_TIMESTAMP, PRINT_TYPE, INSERT_BY, DELAY_CAUSE,CARD_USED_FLAG ) 
			VALUES (
				#{related_id, jdbcType=NUMERIC},
				]]>				
				<if test=" change_flag != null and change_flag != ''  "><![CDATA[#{change_flag, jdbcType=VARCHAR} ,]]></if>
				<if test=" change_flag = null or change_flag = ''  "><![CDATA[ default,]]></if>
				<![CDATA[ #{endorse_code, jdbcType=VARCHAR} , #{print_status, jdbcType=VARCHAR} ,  #{apply_time, jdbcType=DATE} , #{hesitate_flag, jdbcType=NUMERIC} , #{finish_time, jdbcType=DATE} 
				, CURRENT_TIMESTAMP, #{policy_lock_flag, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{validate_time, jdbcType=DATE} ,  #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} 
				, #{fee_amount, jdbcType=NUMERIC} , #{policy_copy_flag, jdbcType=VARCHAR} , #{policy_chg_id, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} , #{accept_id, jdbcType=NUMERIC} , #{notify_type, jdbcType=VARCHAR} 
				, SYSDATE , SYSDATE  , #{order_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{print_num, jdbcType=NUMERIC}
				, CURRENT_TIMESTAMP, #{print_type, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{delay_cause, jdbcType=VARCHAR}, #{card_used_flag, jdbcType=NUMERIC}  ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsPolicyChange" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE 1=1 ]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
	</delete>
<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsPolicyChangeByPolicyChgId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</delete>
<!-- 修改操作 -->
	<update id="JRQD_updateCsPolicyChange" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_POLICY_CHANGE ]]>
		<set>
		<trim suffixOverrides=",">
		    RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
			CHANGE_FLAG = #{change_flag, jdbcType=VARCHAR} ,
		    APPLY_TIME = #{apply_time, jdbcType=DATE} ,
		    HESITATE_FLAG = #{hesitate_flag, jdbcType=NUMERIC} ,
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
		    PRINT_NUM = #{print_num, jdbcType=NUMERIC},
			POLICY_LOCK_FLAG = #{policy_lock_flag, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    VALIDATE_TIME = #{validate_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
			POLICY_COPY_FLAG = #{policy_copy_flag, jdbcType=VARCHAR} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    ACCEPT_ID = #{accept_id, jdbcType=NUMERIC} ,
			NOTIFY_TYPE = #{notify_type, jdbcType=VARCHAR} ,
			PRINT_STATUS = #{print_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    ENDORSE_CODE = #{endorse_code, jdbcType=VARCHAR} ,
		    ORDER_ID = #{order_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			PRINT_TYPE = #{print_type, jdbcType=VARCHAR} ,
			DELAY_CAUSE = #{delay_cause, jdbcType=VARCHAR} ,
			PRINT_MODE = #{print_mode, jdbcType=VARCHAR} ,
			REPRINT_FLAG = #{reprint_flag, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</update>

<!-- 保存保单打印，通知方式 -->

	<update id="JRQD_updatePrintandNotifyChange" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_POLICY_CHANGE ]]>
		<set>
		<trim suffixOverrides=","> 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			NOTIFY_TYPE = #{notify_type, jdbcType=VARCHAR} ,
			REPRINT_FLAG = #{reprint_flag, jdbcType=VARCHAR} ,
			PRINT_MODE = #{print_mode, jdbcType=VARCHAR} ,
			PRINT_TYPE = #{print_type, jdbcType=VARCHAR}
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</update>
<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsPolicyChangeByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, A.PRINT_STATUS, A.PRINT_NUM ,  A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME,
			 A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
			A.NOTIFY_TYPE,
			A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE, 
			A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE ROWNUM <=  1 ]]>
		<include refid="JRQD_queryCsPolicyChangeByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]>
	</select>
	
<!-- zhulh 查询 -->
<select id="JRQD_findCsPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, A.PRINT_STATUS, A.PRINT_NUM , A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME, 
			 A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
			A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
			A.NOTIFY_TYPE, 
			A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE, 
			A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE ROWNUM <=  1 ]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
	

		
	</select>
<!-- end  -->
<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, A.PRINT_STATUS, A.PRINT_NUM , A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME, 
			 A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
			A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
			A.NOTIFY_TYPE, 
			A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE, 
			A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]>
	</select>

<!-- 查询所有操作 保全专用 -->
	<select id="JRQD_findAllCsPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT 
		    A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, 
		    A.PRINT_STATUS, A.PRINT_NUM ,A.APPLY_TIME, A.HESITATE_FLAG, 
		    A.FINISH_TIME, 
		    A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
		    A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
		    A.NOTIFY_TYPE, 
		    A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE,
		    A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE,A.CARD_USED_FLAG,
		   --(select b.acknowledge_date from APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT b where b.policy_id=a.policy_id) acknowledge_date
		    B.acknowledge_date 
		    FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A 
		    LEFT JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT B on  A.POLICY_ID = B.POLICY_ID 
		    WHERE ROWNUM <=  1000  
		]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY A.INSERT_TIME desc ]]>
	</select>
	
	<!-- 查询受理不为逾期的 保全专用 -->
	<select id="JRQD_findAllCsPolicyChangeForAcceptStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT 
		    A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, 
		    A.PRINT_STATUS, A.PRINT_NUM ,A.APPLY_TIME, A.HESITATE_FLAG, 
		    A.FINISH_TIME, 
		    A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
		    A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
		    A.NOTIFY_TYPE, 
		    A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE,
		    A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE,
		    (select b.acknowledge_date from APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT b where b.policy_id=a.policy_id) acknowledge_date
		    FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A
		    left join APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B on B.CHANGE_ID=A.CHANGE_ID
		    WHERE ROWNUM <=  1000  
		    AND B.ACCEPT_STATUS !='12'
		]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE ]]>
	</select>
	<!--P00001001985 接口查出来的批单号会当成P00001001992的入参-》受理号 ，导致1992接口直接调到老核心，因此将改接口的批单号取值受理号  -->
	<select id="JRQD_findAllCsPolicyChangeSelf" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT 
		    A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, 
		    AC.ACCEPT_CODE,
		    A.PRINT_STATUS, A.PRINT_NUM ,A.APPLY_TIME, A.HESITATE_FLAG, 
		    A.FINISH_TIME, 
		    A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
		    A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
		    A.NOTIFY_TYPE, 
		    A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE,
		    A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE,
		    (select b.acknowledge_date from APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT b where b.policy_id=a.policy_id) acknowledge_date
		    FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,
             APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ac
		    WHERE ROWNUM <=  1000 
		    and a.change_id=ac.change_id
		]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE ]]>
	</select>
	
	<select id="JRQD_findAllCsPolicyChangeForCancle" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT 
		    A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, 
		    A.PRINT_STATUS, A.PRINT_NUM ,A.APPLY_TIME, A.HESITATE_FLAG, 
		    A.FINISH_TIME, 
		    A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
		    A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
		    A.NOTIFY_TYPE, 
		    A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE,
		    A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE,
		    (select b.acknowledge_date from APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT b where b.policy_id=a.policy_id) acknowledge_date
		    FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A left join APP___PAS__DBUSER.t_Cs_Application ta on a.change_id = ta.change_id 
		    WHERE ROWNUM <=  1000  and ta.app_status is not null
		]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE ]]>
	</select>
	
	
<!-- 查询所有操作 -->
	<select id="JRQD_findPolicyAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    select  t.AGENT_CODE from APP___PAS__DBUSER.t_contract_agent t where t.is_current_agent = 1 and t.policy_code = #{policy_code}
		]]>
	</select>
<!-- 查询个数操作 -->
	<select id="JRQD_findcustomer11" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select a.customer_gender,a.customer_name,a.mobile_tel from APP___PAS__DBUSER.t_Policy_Holder b ,APP___PAS__DBUSER.t_Customer a where a.customer_id=b.customer_id and b.policy_code=#{policy_code} ]]>
	</select>
	<select id="JRQD_findCsPremArap1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.fee_amount,A.arap_flag,A.bank_account,a.PAY_MODE FROM APP___PAS__DBUSER.T_CS_PREM_ARAP  A WHERE A.business_code=#{business_code} ]]>
	</select>
	
	<select id="JRQD_findCsPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.LIST_ID,A.UNIT_NUMBER,A.BUSINESS_CODE,A.APPLY_CODE,A.POLICY_CODE,A.BOOKKEEPING_FLAG,
		A.PRODUCT_CODE,A.PAY_LIAB_CODE,A.SOURCE_TABLE,A.SOURCE_TABLE_PK,A.DERIV_TYPE,
		A.BUSINESS_TYPE,A.BRANCH_CODE,A.AGENT_CODE,A.ORGAN_CODE,A.POLICY_ORGAN_CODE,
		A.POLICY_TYPE,A.CHANNEL_TYPE,A.PRODUCT_CHANNEL,A.BUSI_APPLY_DATE,A.VALIDATE_DATE,
		A.POLICY_YEAR,A.PREM_FREQ,A.CHARGE_YEAR,A.PAID_COUNT,A.REFEFLAG,A.HOLDER_ID,A.HOLDER_NAME,
		A.INSURED_ID,A.INSURED_NAME,A.IS_RISK_MAIN,A.IS_ITEM_MAIN,A.BUSI_PROD_CODE,A.BUSI_PROD_NAME,
		A.FEE_TYPE,A.WITHDRAW_TYPE,A.DUE_TIME,A.FINISH_TIME,A.PAY_END_DATE,A.ARAP_FLAG,A.CUSTOMER_ID,
		A.PAYEE_NAME,A.CERTI_TYPE,A.CERTI_CODE,A.PAYEE_PHONE,A.BANK_CODE,A.BANK_ACCOUNT,A.BANK_USER_NAME,
		A.MONEY_CODE,A.FEE_AMOUNT,A.PAY_MODE,A.FEE_STATUS,A.POSTED,A.ROLLBACK_UNIT_NUMBER,A.SERVICE_CODE,
		A.INSERT_BY,A.UPDATE_BY,A.INSERT_TIME,A.UPDATE_TIME,A.UPDATE_TIMESTAMP,A.PAYEE_CERT_STAR_DATE,A.PAYEE_CERT_END_DATE,A.PAY_STAKE_REASION
		 FROM APP___PAS__DBUSER.T_CS_PREM_ARAP A WHERE A.business_code=#{business_code} ]]>
	</select>
<!-- 查询个数操作 -->
	<select id="JRQD_findCsPolicyChangeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCsPolicyChangeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RELATED_ID, B.ENDORSE_CODE, B.PRINT_STATUS, B.PRINT_NUM , B.CHANGE_FLAG, B.APPLY_TIME, B.HESITATE_FLAG, B.FINISH_TIME, 
			 B.POLICY_LOCK_FLAG, B.ORGAN_CODE, B.VALIDATE_TIME,  
			B.CHANGE_ID, B.FEE_AMOUNT, B.POLICY_COPY_FLAG, B.POLICY_CHG_ID, B.SERVICE_CODE, B.POLICY_ID, B.ACCEPT_ID, 
			B.NOTIFY_TYPE,
			B.ORDER_ID, B.POLICY_CODE , B.PRINT_TYPE, 
			B.DELAY_CAUSE,B.REPRINT_FLAG, B.PRINT_MODE FROM (
					SELECT ROWNUM RN, A.RELATED_ID, A.ENDORSE_CODE, A.PRINT_NUM , A.PRINT_STATUS, A.CHANGE_FLAG, A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME,  
			 A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
			A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
			A.NOTIFY_TYPE,  
			A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE, 
			A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 查询所有操作排序,受理号,保全申请号 -->
	<select id="JRQD_findAllCsPolicyChangeByChangeidAndOthers" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, A.PRINT_NUM , A.PRINT_STATUS,  A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME,  
			A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
			A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
			A.NOTIFY_TYPE,
			A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE, 
			A.DELAY_CAUSE,A.INSERT_BY,B.ACCEPT_CODE,C.APPLY_CODE,A.REPRINT_FLAG, A.PRINT_MODE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B ON A.ACCEPT_ID=B.ACCEPT_ID LEFT JOIN 
			APP___PAS__DBUSER.T_CS_APPLICATION C ON A.CHANGE_ID=C.CHANGE_ID WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY C.APPLY_CODE,B.ACCEPT_CODE ]]> 
	</select>
	
	
	<!-- 不同参数下的质检总数 -->
	<select id="JRQD_querySpceMoreInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1) FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE t
				WHERE t.ORGAN_CODE = #{organ_code}
		 ]]>
		 <if test=" beginApplyTime  != null  and  beginApplyTime  != ''  "><![CDATA[ AND t.APPLY_TIME >= #{beginApplyTime} ]]></if>
		 <if test=" endApplyTime  != null  and  endApplyTime  != ''  "><![CDATA[ AND t.APPLY_TIME <= #{endApplyTime} ]]></if>
		 <if test=" service_code != null and service_code != ''  "><![CDATA[ AND t.SERVICE_CODE = #{service_code} ]]></if>
	</select>
	
	
	<!-- 查询质检抽取数据-->
	<select id="JRQD_querySpceMoreInfoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT * FROM (
					SELECT rownum as rn,t.ORGAN_CODE,t.CHANGE_ID,t.SERVICE_CODE,t.APPLY_TIME FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE t
					WHERE t.ORGAN_CODE = #{organ_code}
		 ]]>
		 <if test=" beginApplyTime  != null  and  beginApplyTime  != ''  "><![CDATA[ AND t.APPLY_TIME >= #{beginApplyTime} ]]></if>
		 <if test=" endApplyTime  != null  and  endApplyTime  != ''  "><![CDATA[ AND t.APPLY_TIME <= #{endApplyTime} ]]></if>
		 <if test=" service_code != null and service_code != ''  "><![CDATA[ AND t.SERVICE_CODE = #{service_code} ]]></if>
		 <![CDATA[  ORDER BY  dbms_random.value )
		 			WHERE rownum <= rn * #{rownum}
		  ]]>
	</select>
	
	<!-- 按客户ID查询 -->
	<select id="JRQD_findCsPolicyChangeByCustID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, A.PRINT_NUM , A.PRINT_STATUS, A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME,  
			 A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
			A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
			A.NOTIFY_TYPE, 
			A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE, 
			A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE  A.CHANGE_ID IN (SELECT B.CHANGE_ID FROM APP___PAS__DBUSER.T_CS_APPLICATION B WHERE B.CUSTOMER_ID = #{customer_id}) AND ROWNUM <=  1000  ]]>
		<![CDATA[ ORDER BY A.POLICY_CHG_ID ]]> 
	</select>
	
	<!-- 查询保单账户的账户金额-->
	<select id="JRQD_findAccountMoney_policyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select sum(t.INTEREST_CAPITAL) as accountMoney from APP___PAS__DBUSER.T_POLICY_ACCOUNT t where t.policy_id = #{policy_id} and t.account_type = #{account_type} 
		]]> 
	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsPolicyChangeOrderByValidateTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, A.PRINT_NUM , A.PRINT_STATUS,A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME,  
			 A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
			A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
			A.NOTIFY_TYPE,  
			A.ORDER_ID, A.POLICY_CODE, A.PRINT_TYPE, 
			A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY A.VALIDATE_TIME desc]]> 
	</select>
	
	<!-- 保单保全历史查询 ：通过保单号和保全批改项目查询保全批改信息-->
	<select id="JRQD_accInsureBPEdorQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT AC.ACCEPT_CODE AS EDORACCEPTNO,
		       A.SERVICE_CODE AS EDORTYPE,
		       (SELECT S.SERVICE_NAME
		          FROM APP___PAS__DBUSER.T_SERVICE S
		         WHERE A.SERVICE_CODE = S.SERVICE_CODE) AS EDORNAME,
		       AP.APPLY_TIME AS EDORAPPDATE,
		       AP.APPLY_NAME AS EDORAPPNAME,
		       AP.SERVICE_TYPE AS APPTYPE,
		       (SELECT ST.TYPE_NAME
		          FROM APP___PAS__DBUSER.T_SERVICE_TYPE ST
		         WHERE AP.SERVICE_TYPE = ST.SERVICE_TYPE) AS APPTYPENAME,
		       AC.VALIDATE_TIME AS CONFDATE,
		       (select cm.old_code
		          from APP___PAS__DBUSER.t_Code_Mapper cm
		         where cm.codetype = 'ACCEPT_STATUS'
		           and cm.new_code =AC.ACCEPT_STATUS
		           and rownum = 1)  AS EDORSTATE,
		       (SELECT AST.STATUS_DESC
		          FROM APP___PAS__DBUSER.T_ACCEPT_STATUS AST
		         WHERE AST.ACCEPT_STATUS = AC.ACCEPT_STATUS) AS EDORSTATENAME
		  FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A
		  LEFT JOIN APP___PAS__DBUSER.T_CS_APPLICATION AP
		    ON A.CHANGE_ID = AP.CHANGE_ID
		  LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC
		    ON A.ACCEPT_ID = AC.ACCEPT_ID 
				WHERE A.POLICY_CODE=#{policy_code} ]]> 
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test="orderString != null">
			${orderString}
		</if>
	</select>
	<!--by zhaoyoan_wb 追加保费查询 -->
	<select id="JRQD_queryAllAddPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 'CS' source,A.POLICY_CODE,A.VALIDATE_TIME,A.SERVICE_CODE,
			(SELECT FEE_AMOUNT FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE WHERE CHANGE_ID=A.CHANGE_ID) AS fee_amount,
			(SELECT PAY_MODE FROM APP___PAS__DBUSER.T_CS_PREM_ARAP B WHERE B.POLICY_CODE=A.POLICY_CODE AND B.BUSINESS_CODE=
			  (SELECT ACCEPT_CODE FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE WHERE CHANGE_ID=A.CHANGE_ID) AND ROWNUM=1) AS pay_mode
			FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A 
			WHERE A.SERVICE_CODE='AM' 
			AND A.POLICY_CODE=#{policy_code}
			  UNION
			SELECT 'NB' source, '' POLICY_CODE,null VALIDATE_TIME,'' SERVICE_CODE,
			NVL(SUM(B.APPEND_PREM),0) fee_amount,
			'' pay_mode 
			FROM APP___PAS__DBUSER.T_APPEND_PREM_LIST B WHERE B.POLICY_CODE=#{policy_code}
   		]]>
	</select>
	
	<!-- 保全历史查询接口 -->
	<select id="JRQD_queryCsHistoryQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		  select t.policy_code,
	       (select accept_code
	          from APP___PAS__DBUSER.T_cs_accept_change cac
	         where cac.change_id = t.change_id
	           and cac.accept_id = t.accept_id) as accept_code,
	       t.endorse_code,
	       t.service_code as service_name,
	       (select apply_time
	          from APP___PAS__DBUSER.T_cs_application ca
	         where ca.change_id = t.change_id) as apply_time,
	       t.validate_time,
	       (select cac.accept_status
          from APP___PAS__DBUSER.T_cs_accept_change cac
         where cac.change_id = t.change_id
           and cac.accept_id = t.accept_id) as status_desc,
	       (select real_name from  dev_pas.t_udmp_user a where a.user_id = (select insert_by
          from APP___PAS__DBUSER.T_cs_application ca
         where ca.change_id = t.change_id)) as agent_code
	  from APP___PAS__DBUSER.T_cs_policy_change t
	 where t.policy_code = #{policy_code}
		     		]]>
	</select>
	
	
	<!-- 保全明细查询接口 -->
	<select id="JRQD_queryCsDetailQuery" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select 
				  (select auto_sign_flag from APP___PAS__DBUSER.T_cs_service_org_cfg cfg where cfg.service_code = #{service_code}) as signreportflag,
				  '',
				  (select ma.service_result from APP___PAS__DBUSER.t_insured_list       h, APP___PAS__DBUSER.t_customer            c, APP___PAS__DBUSER.t_identity_check_main ma
 					where h.policy_code = t.policy_code and h.customer_id = c.customer_id and ma.check_name = c.customer_name and ma.check_id_code = c.customer_certi_code and rownum=1) as checkresult,
				  (select ma.check_date from APP___PAS__DBUSER.t_insured_list       h, APP___PAS__DBUSER.t_customer            c, APP___PAS__DBUSER.t_identity_check_main ma
 					where h.policy_code = t.policy_code and h.customer_id = c.customer_id and ma.check_name = c.customer_name and ma.check_id_code = c.customer_certi_code and rownum=1) as checkdate,
				  (select sign_status from APP___PAS__DBUSER.T_cs_app_doc doc where t.change_id = doc.change_id and rownum=1) as recoverstate,
				  '',
				  '',
				  (select liability_state from  APP___PAS__DBUSER.T_cs_contract_master ccm where ccm.change_id = t.change_id and ccm.policy_chg_id = t.policy_chg_id and ccm.old_new = '1' and rownum=1) as available,
				  (select liability_state from  APP___PAS__DBUSER.T_cs_contract_master ccm where ccm.change_id = t.change_id and ccm.policy_chg_id = t.policy_chg_id and ccm.old_new = '1' and rownum=1) as lost,
				  (select fee_status from APP___PAS__DBUSER.T_cs_prem_arap cra where cra.business_code = #{accept_code} and cra.policy_code = t.policy_code and cra.deriv_type = '004'  and rownum=1) as payprem,
				  decode((select 1 from APP___PAS__DBUSER.T_cs_policy_account_stream cpas where cpas.change_id = t.change_id and cpas.policy_chg_id = t.change_id and cpas.old_new = '1'),null,'0','1') as loan,
				  '',
				  nvl2((select 1 from APP___PAS__DBUSER.T_cs_accept_change cac where exists ( select 1 from APP___PAS__DBUSER.T_cs_policy_change cpc where cpc.policy_code= #{policy_code} and cac.accept_id = cpc.accept_id
				  and cac.change_id = cpc.change_id ) and cac.service_code = 'pu' and rownum=1),'1','0') as  rpu,
				  (select liability_state from  APP___PAS__DBUSER.T_cs_contract_master ccm where ccm.change_id = t.change_id and ccm.policy_chg_id = t.policy_chg_id and ccm.old_new = '1' and rownum=1) as terminate,
				  '',
				  '',
				  '',
				  '',
				  '',
				  '' as apptypeonename,
				  '',
				  '',
				  '',
				  '' as edorappname_read,
				   '' as apptype_read,
				   '',
				   (select change_flag from APP___PAS__DBUSER.T_change c where c.change_id = t.change_id) as urgenttypename,
				   'APP___PAS__DBUSER.T_cs_accept_change.urgent_cause',
				   '',
				   (select  pay_mode from APP___PAS__DBUSER.T_cs_prem_arap cra where cra.business_code = #{accept_code} and cra.policy_code = t.policy_code and cra.deriv_type = '004'  and rownum=1) as getpayform,
				   (select  (select pm.name from APP___PAS__DBUSER.T_pay_mode pm where pm.code = cra.pay_mode) from APP___PAS__DBUSER.T_cs_prem_arap cra where cra.business_code = #{accept_code} and cra.policy_code = t.policy_code and cra.deriv_type = '004'  and rownum=1) as getpayformname,
				    '',
				   (select  payee_name from APP___PAS__DBUSER.T_cs_prem_arap cra where cra.business_code = #{accept_code} and cra.policy_code = t.policy_code and cra.deriv_type = '004'  and rownum=1) as cardholder,
				   nvl((select  cra.certi_code from APP___PAS__DBUSER.T_cs_prem_arap cra where cra.business_code = #{accept_code} and cra.policy_code = t.policy_code and cra.deriv_type = '004'  and rownum=1),
				       (select  c.customer_id_code from APP___PAS__DBUSER.T_cs_prem_arap cra,APP___PAS__DBUSER.T_customer c where cra.business_code = #{accept_code} and cra.policy_code = t.policy_code and cra.deriv_type = '004' and cra.customer_id = c.customer_id  and rownum=1) 
				       ) as personid,
				   decode((select  bank_account from APP___PAS__DBUSER.T_cs_prem_arap cra where cra.business_code = #{accept_code} and cra.policy_code = t.policy_code and cra.deriv_type = '004'  and rownum=1),(select b.bank_account from APP___PAS__DBUSER.T_cs_prem_arap cra, APP___PAS__DBUSER.T_bank_account b where cra.business_code = #{accept_code} and cra.deriv_type = '004' and cra.customer_id = b.customer_id and rownum=1),'1','0') as accchktype,
				   '' as proxyusername,
				   '' as proxyuserphono,
				   '' as proxyusertype,
				   '' as proxyusernum,
				   (select   (select type from APP___PAS__DBUSER.T_certi_type ct where ca.agent_certi_type = ct.code) from APP___PAS__DBUSER.T_cs_application ca where ca.change_id = t.change_id) as proxyusertypename,
				   '' as agentcode2,
				   '' as agentstar,
				   '' as checkinfochange , 
				   decode((select 1 from APP___PAS__DBUSER.T_policy_freeze pf where pf.change_id = t.change_id and pf.policy_chg_id = t.policy_chg_id and pf.policy_code = #{policy_code} and rownum=1),null,'0','1') as frozenflag,
				  (select freeze_date from APP___PAS__DBUSER.T_policy_freeze pf where pf.change_id = t.change_id and pf.policy_chg_id = t.policy_chg_id and pf.policy_code = #{policy_code} and rownum=1) as frozenstartdate ,  
				   '' as edoracceptno,
				   t.endorse_code as edorno,
				   '' as edortype,
				   '' as displaytype,
				   '',
				   (select policy_type from  APP___PAS__DBUSER.T_cs_contract_master ccm where ccm.change_id = t.change_id and ccm.policy_chg_id = t.policy_chg_id and ccm.old_new = '1' and rownum=1) as conttype,
				   t.policy_code as contno,
				   (select cil.customer_id from  APP___PAS__DBUSER.T_cs_benefit_insured cbi , APP___PAS__DBUSER.T_cs_insured_list cil where  cbi.change_id = cil.change_id and cbi.policy_chg_id = cil.policy_chg_id
				  and cil.old_new = '1' and cbi.order_id=1 and cil.change_id = t.change_id and cil.policy_chg_id = t.policy_chg_id and rownum=1) as insuredno,
				   (select ccbp.busi_prod_code from APP___PAS__DBUSER.T_cs_contract_busi_prod ccbp where ccbp.change_id = t.change_id and ccbp.policy_chg_id = t.policy_chg_id and ccbp.old_new = '1'  and ccbp.master_busi_item_id is null) as polno,
				   (select bp.product_name_sys from APP___PAS__DBUSER.T_cs_contract_busi_prod ccbp,APP___PAS__DBUSER.T_business_product bp 
				   where ccbp.change_id = t.change_id and ccbp.policy_chg_id = t.policy_chg_id and ccbp.old_new = '1' and bp.business_prd_id = ccbp.busi_prd_id  and ccbp.master_busi_item_id is null) as riskname,
				   '' as edorappdate,
				    t.validate_time,
				    '' as preinputvalidate,
				    (select cs_accept_reason_name from  APP___PAS__DBUSER.T_cs_accept_reason car where car.cs_accept_reason_code = #{cs_accept_reason_code}) as appreason,
				    '' as appreasoncode,
				    '' as getmoney,
				    '' as makedate,
				    '' as maketime,
				    '' as modifydate,
				    '' as operator,
				    (select tas.status_desc from APP___PAS__DBUSER.T_accept_status tas where tas.accept_status = #{accept_status}) as state,
				    '' as edorstate ,
				    decode((select 1 from  APP___PAS__DBUSER.T_cs_pay_due cpd where cpd.fee_status = '01'  and cpd.change_id = t.change_id and cpd.policy_chg_id = t.policy_chg_id
				    and cpd.old_new = '1'),
				    1,decode((select 1 from  APP___PAS__DBUSER.T_cs_policy_account_stream cpas where  cpas.regular_repay = 1 and cpas.change_id = t.change_id and cpas.policy_chg_id = t.policy_chg_id and cpas.old_new = '1'),1,0,1)
				    ,1)  as  survivalandloanflag,
				    t.change_id,
				    t.policy_chg_id,
				    t.policy_id,
				    t.POLICY_CODE,
				    t.ENDORSE_CODE
			  from APP___PAS__DBUSER.T_cs_policy_change t
			 where t.policy_code = #{policy_code}
			   and t.accept_id = #{accept_id}
   		]]>
	</select>
	
	<!-- 身份验真时间和结果 -->
	<select id="JRQD_queryIdentifyDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			  select * from dual
		]]>
	</select>
	
	<!-- 保全明细的抄单险种信息 -->
	<select id="JRQD_queryCsRiskInfoOfCsDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			  select   ccb.busi_prod_code as riskcode,
				       ccb.busi_prd_id as polno,
				       (select product_name_sys from APP___PAS__DBUSER.T_business_product bp where  bp.business_prd_id = ccb.busi_prd_id) as riskshortname,
				       (select c.customer_name from APP___PAS__DBUSER.T_insured_list cil ,APP___PAS__DBUSER.T_benefit_insured cbi,APP___PAS__DBUSER.T_customer c where cbi.insured_id = cil.list_id and cbi.busi_item_id = ccb.busi_item_id and cil.customer_id = c.customer_id and cbi.busi_item_id = ccb.busi_item_id) as insuredname,
				       (select customer_name from APP___PAS__DBUSER.T_customer where customer_id=(select customer_id from APP___PAS__DBUSER.T_POLICY_HOLDER where policy_id=ccb.policy_id)) as appntname,
				       (select sum(ccp.amount) from APP___PAS__DBUSER.T_cs_contract_product ccp where ccp.change_id = ccb.change_id and ccp.policy_chg_id = ccb.policy_chg_id and  ccp.old_new = '1' and ccp.busi_item_id = ccb.busi_item_id) as amnt,
				       (select sum(ccp.unit) from APP___PAS__DBUSER.T_cs_contract_product ccp where ccp.change_id = ccb.change_id and ccp.policy_chg_id = ccb.policy_chg_id  and ccp.old_new = '1' and ccp.busi_item_id = ccb.busi_item_id) as mult,
				       (select sum(ccp.std_prem_af) from APP___PAS__DBUSER.T_cs_contract_product ccp where ccp.change_id = ccb.change_id and ccp.policy_chg_id = ccb.policy_chg_id  and ccp.old_new = '1' and ccp.busi_item_id = ccb.busi_item_id) as prem1,
				       (select sum(cep.extra_prem) from APP___PAS__DBUSER.T_cs_extra_prem cep where cep.change_id = ccb.change_id and cep.policy_chg_id = ccb.policy_chg_id and cep.busi_item_id = ccb.busi_item_id and cep.old_new = '1') as prem2,
				       (select sum(cep.extra_prem) from APP___PAS__DBUSER.T_cs_extra_prem cep where cep.change_id = ccb.change_id and cep.policy_chg_id = ccb.policy_chg_id and cep.busi_item_id = ccb.busi_item_id and cep.old_new = '1') as prem3,
				       (select max(cce.pay_due_date) from APP___PAS__DBUSER.T_cs_contract_extend cce where cce.change_id = ccb.change_id and cce.policy_chg_id = ccb.policy_chg_id and cce.busi_item_id = ccb.busi_item_id and cce.old_new = '1') as pay_due_date
			from APP___PAS__DBUSER.T_cs_contract_busi_prod ccb where ccb.change_id = #{change_id} and ccb.policy_chg_id = #{policy_chg_id} and ccb.old_new = '1'
   		]]>
	</select>
	
	<!-- 保全明细的保单险种信息 -->
	<select id="JRQD_queryRiskInfoOfCsDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			  select   ccb.busi_prod_code as riskcode,
				       ccb.busi_prd_id as polno,
				       (select product_name_sys from APP___PAS__DBUSER.T_business_product bp where  bp.business_prd_id = ccb.busi_prd_id) as riskshortname,
				       (select c.customer_name from APP___PAS__DBUSER.T_insured_list cil ,APP___PAS__DBUSER.T_benefit_insured cbi,APP___PAS__DBUSER.T_customer c where cbi.insured_id = cil.list_id and cbi.busi_item_id = ccb.busi_item_id and cil.customer_id = c.customer_id and cbi.busi_item_id = ccb.busi_item_id) as insuredname,
				       (select sum(ccp.amount) from APP___PAS__DBUSER.T_contract_product ccp where ccp.policy_id = ccb.policy_id and ccp.busi_item_id = ccb.busi_item_id) as amnt,
				       (select sum(ccp.unit) from APP___PAS__DBUSER.T_contract_product ccp where ccp.policy_id = ccb.policy_id and ccp.busi_item_id = ccb.busi_item_id) as mult,
				       (select sum(ccp.std_prem_af) from APP___PAS__DBUSER.T_contract_product ccp where ccp.policy_id = ccb.policy_id and ccp.busi_item_id = ccb.busi_item_id) as prem1,
				       (select sum(cep.extra_prem) from APP___PAS__DBUSER.T_extra_prem cep where cep.policy_id = ccb.policy_id and cep.busi_item_id = ccb.busi_item_id) as prem2,
				       (select sum(cep.extra_prem) from APP___PAS__DBUSER.T_extra_prem cep where cep.policy_id = ccb.policy_id and cep.busi_item_id = ccb.busi_item_id) as prem3,
				       (select max(cce.pay_due_date) from APP___PAS__DBUSER.T_contract_extend cce where cce.policy_id = ccb.policy_id and cce.busi_item_id = ccb.busi_item_id) as pay_due_date
			from APP___PAS__DBUSER.T_contract_busi_prod ccb where ccb.policy_id= #{policy_id} 
   		]]>
	</select>
	
	<!-- 保全明细的是否存在第三方止付和保单贷款信息 -->
	<select id="JRQD_isExistsServcieCS_CP_LN_RF" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select * from APP___PAS__DBUSER.T_cs_accept_change t where  exists (select 1
          	from APP___PAS__DBUSER.T_cs_policy_change cpc where cpc.policy_id = #{policy_id} and t.change_id = cpc.change_id and t.accept_id = cpc.accept_id)
   		]]>
   		<if test="service_codes  != null and service_codes.size() != 0">
			<![CDATA[ and t.service_code in ]]>
			<foreach collection="service_codes" item="service_code" index="index" open="(" close=")" separator=",">
				#{service_code}
			</foreach>
		</if>
	</select>
	
		<!-- 查询最新的第三方止付信息  18为生效  CS，-->
	<select id="JRQD_findAllCsPolicyChangeWhereCSOrderByValidateTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, A.PRINT_NUM , A.PRINT_STATUS, A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME, 
       A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,
      A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
      A.NOTIFY_TYPE,
      A.ORDER_ID, A.POLICY_CODE,A.PRINT_TYPE, 
      A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B where
       A.ACCEPT_ID=B.ACCEPT_ID AND A.POLICY_CODE=#{policy_code} AND A.SERVICE_CODE='CS' AND B.ACCEPT_STATUS='18' ORDER BY B.VALIDATE_TIME  DESC  ]]>
	</select>
	
	<!--
	<select id="JRQD_findAllQueryInfoByCRM" resultType="java.util.Map" parameterType="java.util.Map">
	
		<![CDATA[
					
				SELECT R.POLICY_CODE CONTNO,B.ACCEPT_CODE EDORACCEPTNO,
		(SELECT O.CUSTOMER_NAME FROM APP___PAS__DBUSER.T_POLICY_HOLDER G,APP___PAS__DBUSER.T_CUSTOMER O 
		WHERE G.CUSTOMER_ID=O.CUSTOMER_ID AND G.POLICY_CODE=R.POLICY_CODE AND ROWNUM=1) APPNTNAME,
		(SELECT (SELECT O.PRODUCT_CODE_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT O WHERE O.BUSINESS_PRD_ID=G.BUSI_PRD_ID) 
		FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD G WHERE 
		G.MASTER_BUSI_ITEM_ID IS NULL AND G.POLICY_CODE=R.POLICY_CODE AND ROWNUM=1) RISKCODE,
		(SELECT (SELECT O.PRODUCT_NAME_SYS FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT O WHERE O.BUSINESS_PRD_ID=G.BUSI_PRD_ID) 
		FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD G WHERE 
		G.MASTER_BUSI_ITEM_ID IS NULL AND G.POLICY_CODE=R.POLICY_CODE AND ROWNUM=1) RISKNAME,
		(SELECT SUM(G.AMOUNT) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT G WHERE G.BUSI_ITEM_ID=(SELECT O.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD O 
		WHERE O.POLICY_CODE=R.POLICY_CODE AND O.MASTER_BUSI_ITEM_ID IS NULL)) AMNT,
		(SELECT SUM(G.STD_PREM_AF) FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT G WHERE G.BUSI_ITEM_ID=(SELECT O.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD O 
		WHERE O.POLICY_CODE=R.POLICY_CODE AND O.MASTER_BUSI_ITEM_ID IS NULL)) PREM,
		(SELECT G.PAY_DUE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND G WHERE G.POLICY_CODE=R.POLICY_CODE AND 
		G.BUSI_ITEM_ID=(SELECT O.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD O 
		WHERE O.POLICY_CODE=R.POLICY_CODE AND O.MASTER_BUSI_ITEM_ID IS NULL) AND ROWNUM=1) PAYTODATE,
		(SELECT (CASE WHEN G.PREM_FREQ = 1 THEN '一次交清' ELSE TO_CHAR(G.CHARGE_YEAR) END) AS CHARGE_YEAR FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT G WHERE G.BUSI_ITEM_ID=(SELECT O.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD O 
		WHERE O.POLICY_CODE=R.POLICY_CODE AND O.MASTER_BUSI_ITEM_ID IS NULL) AND ROWNUM=1) PAYYEARS,
		(SELECT G.PREM_FREQ FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT G WHERE G.BUSI_ITEM_ID=(SELECT O.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD O 
    	WHERE O.POLICY_CODE=R.POLICY_CODE AND O.MASTER_BUSI_ITEM_ID IS NULL) AND ROWNUM=1) PREM_FREQ,
		(SELECT G.VALIDATE_DATE FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT G WHERE G.BUSI_ITEM_ID=(SELECT O.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD O 
		WHERE O.POLICY_CODE=R.POLICY_CODE AND O.MASTER_BUSI_ITEM_ID IS NULL) AND ROWNUM=1) VALIDATEDATE,
		(SELECT G.POLICY_PERIOD FROM APP___PAS__DBUSER.T_CONTRACT_EXTEND G WHERE G.POLICY_CODE=R.POLICY_CODE AND 
		G.BUSI_ITEM_ID=(SELECT O.BUSI_ITEM_ID FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD O 
		WHERE O.POLICY_CODE=R.POLICY_CODE AND O.MASTER_BUSI_ITEM_ID IS NULL) AND ROWNUM=1) PAYTIMES,
		B.SERVICE_CODE EDORTYPE,
		(SELECT G.SERVICE_NAME FROM APP___PAS__DBUSER.T_SERVICE G WHERE G.SERVICE_CODE=B.SERVICE_CODE AND ROWNUM=1) EDORNAME,
		A.APPLY_TIME EDORAPPDATE,A.VALIDATE_TIME EDORVALIDATE,B.ACCEPT_STATUS EDORSTATE,
		(SELECT G.REAL_NAME FROM APP___PAS__DBUSER.T_UDMP_USER G WHERE G.USER_ID=B.INSERT_OPERATOR_ID AND ROWNUM=1) OPERATO,
		B.INSERT_OPERATOR_ID
		FROM APP___PAS__DBUSER.T_CONTRACT_MASTER R,APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
		WHERE R.POLICY_ID=A.POLICY_ID AND A.ACCEPT_ID=B.ACCEPT_ID AND B.ACCEPT_STATUS='18' 
				  ]]>
		<if test="policy_code !=null">
			<![CDATA[ AND R.POLICY_CODE = #{policy_code} ]]>
   			
		</if>
	
	</select>
	-->
	
	<select id="JRQD_findAllQueryInfoByCRM" resultType="java.util.Map" parameterType="java.util.Map">
	
		<![CDATA[
			SELECT B.SERVICE_CODE EDORTYPE,
       (SELECT G.SERVICE_NAME
          FROM APP___PAS__DBUSER.T_SERVICE G
         WHERE G.SERVICE_CODE = B.SERVICE_CODE
           AND ROWNUM = 1) EDORNAME,
       A.APPLY_TIME EDORAPPDATE,
       A.VALIDATE_TIME EDORVALIDATE,
       B.ACCEPT_STATUS EDORSTATE,
       (SELECT G.REAL_NAME
          FROM APP___PAS__DBUSER.T_UDMP_USER G
         WHERE G.USER_ID = B.INSERT_OPERATOR_ID
           AND ROWNUM = 1) OPERATO,
       R.POLICY_CODE CONTNO,
       B.ACCEPT_CODE EDORACCEPTNO,
       (SELECT O.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_POLICY_HOLDER G,
               APP___PAS__DBUSER.T_CUSTOMER      O
         WHERE G.CUSTOMER_ID = O.CUSTOMER_ID
           AND G.POLICY_CODE = R.POLICY_CODE
           AND ROWNUM = 1) APPNTNAME
  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER  R,
       APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,
       APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
 WHERE R.POLICY_ID = A.POLICY_ID
   AND A.ACCEPT_ID = B.ACCEPT_ID
   AND B.ACCEPT_STATUS = '18'
				  ]]>
		<if test="policy_code !=null">
			<![CDATA[ AND R.POLICY_CODE = #{policy_code} ]]>
   			
		</if>
	
	</select>
	
	<!-- 多险种节点查询保全信息查询接口 -->
	<select id="JRQD_findAllQueryInfoByCRMRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 select R.POLICY_CODE,
        G.Busi_Prd_Id,
        G.Busi_Item_Id,
        tcp.amount,
        tcp.std_prem_af PREM,
        (SELECT O.PRODUCT_NAME_SYS
           FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT O
          WHERE O.BUSINESS_PRD_ID = G.BUSI_PRD_ID) riskname,
        tcp.VALIDATE_DATE,
        (CASE
          WHEN tcp.PREM_FREQ = 1 THEN
           '一次交清'
          ELSE
           TO_CHAR(tcp.CHARGE_YEAR)
        END) AS CHARGE_YEAR,
        tcp.PREM_FREQ,
        tce.POLICY_PERIOD,
        tce.PAY_DUE_DATE PAYTODATE,
        G.BUSI_PROD_CODE RISKCODE,
        tce.POLICY_YEAR PAYYEARS,
        tce.POLICY_PERIOD PAYTIMES
   from APP___PAS__DBUSER.T_CONTRACT_MASTER    R,
        APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD G,
        APP___PAS__DBUSER.T_CONTRACT_EXTEND    tce,
        APP___PAS__DBUSER.T_CONTRACT_PRODUCT   tcp,
        APP___PAS__DBUSER.T_CS_POLICY_CHANGE   A,
        APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE   B
  WHERE 1 = 1
    and R.Policy_Code = G.Policy_Code
    and R.POLICY_ID = A.POLICY_ID
    and tce.policy_id = R.Policy_Id
    and tce.busi_item_id = G.Busi_Item_Id
    AND A.ACCEPT_ID = B.ACCEPT_ID
    and tcp.policy_id = R.Policy_Id
    and tcp.busi_item_id = G.BUSI_ITEM_ID
    and G.Master_Busi_Item_Id is null
				  ]]>
		<if test="policy_code !=null">
			<![CDATA[ AND R.POLICY_CODE = #{policy_code} ]]>
   			
		</if>
	    <if test="accept_code !=null">
			<![CDATA[ AND B.ACCEPT_CODE = #{accept_code} ]]>
		</if>
	</select>
	
	
	<!-- 保全挂起 -->
	<select id="JRQD_findCsPolicyChange_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[
		 SELECT A.POLICY_CODE,B.SUB_ID
  FROM APP___PAS__DBUSER.t_lock_policy A, APP___PAS__DBUSER.t_lock_service_def B
  
 WHERE A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID AND A.POLICY_CODE=#{policy_code}
   ]]>
	
	</select>
	
	<!-- 保全收付费查询信息 -->
	<select id="JRQD_findAllCsPayMessage" resultType="java.util.Map" parameterType="java.util.Map">
		
		<![CDATA[ 
		select G.EdorType,
		G.EdorTypeName,
		G.GetConfirmDate,
		G.PayMode,
		G.PayModeName,
		G.BankCode,
		G.BankName,
		G.BankAccNo,
		G.AccName,
		G.SendDate,
		G.BankSuccFlag,
		G.CodeName,
		G.liab_validate_time,
		(CASE WHEN g.GetMoney < 0 THEN '2' ELSE '1' END) Arap_Flag,
		(CASE WHEN g.GetMoney < 0 THEN G.GetMoney * -1 ELSE G.GetMoney END) GetMoney from         
      (   
      select MAX(F.EdorType) EdorType,                    
             MAX(F.EdorTypeName) EdorTypeName,            
             MAX(F.GetConfirmDate) GetConfirmDate,        
             MAX(F.PayMode) PayMode,                      
             MAX(F.PayModeName ) PayModeName,             
             MAX(F.BankCode ) BankCode,                   
             MAX(F.BankName) BankName,                    
             MAX(F.BankAccNo) BankAccNo,                  
             MAX(F.AccName) AccName,                      
             MAX(F.SendDate) SendDate,                    
             MAX(F.BankSuccFlag) BankSuccFlag,            
             MAX(F.CodeName) CodeName,  
             MAX(F.Arap_Flag) Arap_Flag,                  
             MAX(F.liab_validate_time) liab_validate_time,
             sum(case
                   when f.arap_flag = '1' then
                    GetMoney
                   else
                    GetMoney * -1
                 end) GetMoney
        from (select A.Service_Code as EdorType,
                     c.liab_validate_time,
                     (case (select count(1)
                          from APP___PAS__DBUSER.t_Code_Mapper cm
                         where cm.codetype = 'SERVICE_CODE'
                           and cm.new_code = A.Service_Code)
                       when 1 then
                        (select case cm.old_code_name
                                  when '解除合同' then
                                   '终止合同'
                                  else
                                   cm.old_code_name
                                end
                           from APP___PAS__DBUSER.t_Code_Mapper cm
                          where cm.codetype = 'SERVICE_CODE'
                            and cm.new_code = A.Service_Code)
                       else
                        (select cm.new_code_name
                           from APP___PAS__DBUSER.t_Code_Mapper cm
                          where cm.codetype = 'SERVICE_CODE'
                            and cm.new_code = A.Service_Code
                            and rownum = 1)
                     end) as EdorTypeName,
                -     B.Fee_Amount as GetMoney,
                     A.Validate_Time as GetConfirmDate,
                     B.Pay_Mode as PayMode,
                     (select T_PAY_MODE.name
                        from APP___PAS__DBUSER.T_PAY_MODE
                       where T_PAY_MODE.code = B.Pay_Mode) as PayModeName,
                     B.BANK_CODE as BankCode,
                     (SELECT Bank_name
                        FROM APP___PAS__DBUSER.T_BANK
                       WHERE BANK_CODE = B.BANK_CODE) as BankName,
                     B.BANK_ACCOUNT as BankAccNo,
                     B.BANK_USER_NAME as AccName,
                     B.FINISH_TIME as SendDate,
                     B.FEE_STATUS as BankSuccFlag,
                     (select STATUS_NAME
                        from APP___PAS__DBUSER.T_FEE_STATUS
                       where STATUS_CODE = B.FEE_STATUS) as CodeName,
                     B.Arap_Flag,
                     b.unit_number,
                     b.business_code
                from APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,
                     APP___PAS__DBUSER.T_PREM_ARAP        B,
                     APP___PAS__DBUSER.T_CS_accept_change C
               WHERE A.CHANGE_ID = C.CHANGE_ID
                 and a.accept_id = c.accept_id
                 AND B.BUSINESS_CODE = C.Accept_Code
                 and b.policy_code = a.policy_code
                 and B.Deriv_Type = '004'
                 and b.fee_status <> '16' 
                 and c.accept_status ='18'       
		]]>
		<if test="policy_code !=null">
			<![CDATA[ and A.POLICY_CODE = #{policy_code} ]]>
		</if>
		<if test="start_date !=null">
			<![CDATA[ and A.VALIDATE_TIME > #{start_date} ]]>
		</if>
		<if test="end_date !=null">
			<![CDATA[ and A.VALIDATE_TIME < = #{end_date} ]]>
		</if>
		<!-- @invaild RM82937 新增保全申请创建日期和保全受理号字段需求-新核心 start -->
		<if test="accept_code !=null">
			<![CDATA[ and C.ACCEPT_CODE = #{accept_code} ]]>
		</if>
		<!-- @invaild RM82937 新增保全申请创建日期和保全受理号字段需求-新核心 end -->
		<![CDATA[ 
		   ) f group by f.unit_number, f.business_code) G
		]]>
		<if test="orderString != null">
			<![CDATA[  ${orderString} ]]>
		</if>
	
	</select>
	
	<select id="JRQD_find_reser_change_info" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		        select t.service_code from APP___pas__DBUSER.t_cs_policy_change t
                where t.policy_id = #{policy_id}
                  and t.service_code in ('EN', 'ER', 'RR', 'DC', 'XX')
                  and exists(select  1 from APP___pas__DBUSER.t_cs_accept_change tca
                             where  t.accept_id = tca.accept_id and tca.accept_status = 18)
                  and exists(select 1 from APP___pas__DBUSER.t_cs_contract_busi_prod tcbup
                              where  tcbup.policy_id = t.policy_id and tcbup.policy_chg_id=t.policy_chg_id 
                              and tcbup.busi_item_id =#{busi_item_id} and tcbup.operation_type='2')
 		]]>
	</select>
	
	<!-- 保全回退-->
	<select id="JRQD_findAllCsRBPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[
		 select distinct policy_code,policy_id
  	from APP___PAS__DBUSER.T_cs_application a, APP___PAS__DBUSER.T_cs_policy_change b, APP___PAS__DBUSER.T_cs_accept_change c
 	where a.change_id = b.change_id
   	and b.accept_id = c.accept_id
   ]]>
   <if test="policy_code !=null"> <![CDATA[ and b.policy_code = #{policy_code} ]]></if>
   <if test="accept_code !=null"> <![CDATA[ and c.accept_code = #{accept_code} ]]></if>
   <if test="customer_ids  != null and customer_ids.size() != 0">
			<![CDATA[ and a.customer_id in ]]>
			<foreach collection="customer_ids" item="customer_ids" index="index" open="(" close=")" separator=",">
				#{customer_ids}
			</foreach>
	</if>
  <![CDATA[ and c.accept_status = '18' order by policy_id ]]>
	</select>
	<!-- 整单退保查询-->
	<select id="JRQD_queryCsPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT TPL.POLICY_CHG_ID,TPL.POLICY_CODE,TPL.UNLOSE_DATE,PC.CHANGE_ID,PC.POLICY_ID
             FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC,APP___PAS__DBUSER.T_POLICY_LOSE TPL
             WHERE  PC.POLICY_CHG_ID = TPL.POLICY_CHG_ID 
             AND PC.CHANGE_ID=TPL.CHANGE_ID 
             AND PC.POLICY_CODE = #{policy_code}
             AND ROWNUM <= 1
             ORDER BY TPL.POLICY_CHG_ID DESC
	   ]]>
	</select>
	
	<!--  -->
	<select id="JRQD_findAllCsPolicyChange_cjk" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[
	select p.policy_id, p.policy_code,t.change_id,p.policy_chg_id,p.accept_id,
	     (select t.customer_id
          from APP___PAS__DBUSER.T_POLICY_HOLDER t
         where t.policy_code = #{policy_code}) as customer_id
	  from APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE t, APP___PAS__DBUSER.T_CS_POLICY_CHANGE p
	 where 1 = 1
	   and t.CHANGE_ID = p.CHANGE_ID
	   and t.ACCEPT_ID = p.ACCEPT_ID
	   and  p.policy_code = #{policy_code}
	   and p.service_code = #{service_code}
	   and t.accept_status = '18'
	   ]]>
	</select>
	
	
	<update id="JRQD_updateLockServiceId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_LOCK_POLICY ]]>
		<set>
		<trim suffixOverrides=","> 
			LOCK_SERVICE_ID = #{lock_service_id , jdbcType=NUMERIC}
		</trim>
		</set>
		<![CDATA[ WHERE business_code =  (select b.apply_code
       from APP___PAS__DBUSER.t_cs_accept_change a, APP___PAS__DBUSER.t_cs_application b
      where a.change_id = b.change_id
        and a.accept_code = #{accept_code}) ]]>
	</update>
	
	<select id="JRQD_findAllCsPolicyChangeByUnitnumber" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
		SELECT C.ACCEPT_CODE, C.ACCEPT_ID, P.POLICY_CHG_ID
		  FROM APP___PAS__DBUSER.T_PREM_ARAP        A,
		       APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C,
		       APP___PAS__DBUSER.T_CS_POLICY_CHANGE P
		 WHERE 1 = 1
		   AND C.ACCEPT_CODE = A.BUSINESS_CODE
		   AND C.ACCEPT_ID = P.ACCEPT_ID
		   AND A.UNIT_NUMBER = '${unit_number}'	
	]]>
	</select>
	
	<!-- 不同参数下的质检总数 -->
	<select id="JRQD_findAllCsPolicyChangeForAM" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		
		 SELECT AC.ACCEPT_CODE,PC.POLICY_CODE,RR.RB_ACCEPT_CODE,PC.SERVICE_CODE,PA.FINISH_TIME
		   FROM APP___PAS__DBUSER.T_REVERSAL_RELATION RR,
		        APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE  AC,
		        APP___PAS__DBUSER.T_CS_POLICY_CHANGE  PC,
		        APP___PAS__DBUSER.T_PREM_ARAP PA
		  WHERE AC.ACCEPT_CODE = RR.RB_ACCEPT_CODE
		    AND RR.POLICY_CODE = #{policy_code}
		    AND ROWNUM = 1
		    AND PC.ACCEPT_ID = AC.ACCEPT_ID
		    AND PC.SERVICE_CODE = 'AM'
		    AND PA.BUSINESS_CODE = AC.ACCEPT_CODE
		    ORDER BY RR.INSERT_TIME DESC
		 ]]>
	</select>
	
	<!-- 按客户ID查询 -->
	<select id="JRQD_findCsPolicyChangeByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.RELATED_ID, A.ENDORSE_CODE, A.PRINT_NUM , A.PRINT_STATUS, A.CHANGE_FLAG, A.APPLY_TIME, A.HESITATE_FLAG, A.FINISH_TIME,  
			 A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
			A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
			A.NOTIFY_TYPE,  
			A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE, 
			A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE from APP___PAS__DBUSER.t_cs_policy_change A where A.change_id =
		 (select t.change_id from APP___PAS__DBUSER.T_CS_APPLICATION t where t.apply_code= #{service_code})]]> 
	</select>
	
	<!-- 保全批改信息接口 -->
	<select id="JRQD_queryCsEdorItemInfoByPolicyCodeAndPolNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.ACCEPT_CODE,A.ENDORSE_CODE,B.SERVICE_CODE,A.POLICY_CODE,
			(SELECT CUSTOMER_ID FROM APP___PAS__DBUSER.T_INSURED_LIST IL WHERE A.POLICY_ID=POLICY_ID AND LIST_ID=
			  (SELECT INSURED_ID FROM APP___PAS__DBUSER.T_BENEFIT_INSURED WHERE POLICY_ID=IL.POLICY_ID 
			    AND C.BUSI_ITEM_ID=BUSI_ITEM_ID AND ORDER_ID='1')) insured_no,
			NVL(C.OLD_POL_NO,C.BUSI_ITEM_ID) pol_no,A.VALIDATE_TIME,B.ACCEPT_STATUS,
			D.APPLY_CODE other_no,D.APPLY_TIME,B.INSERT_TIME make_date,B.UPDATE_TIME modify_date
			FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B,
			APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD C,APP___PAS__DBUSER.T_CS_APPLICATION D
			WHERE 1=1
			AND A.ACCEPT_ID=B.ACCEPT_ID AND A.POLICY_ID=C.POLICY_ID AND A.CHANGE_ID=D.CHANGE_ID
			AND A.CHANGE_ID=C.CHANGE_ID 
			AND C.POLICY_CODE=#{policy_code}
			AND C.BUSI_ITEM_ID=#{busi_item_id} 
			AND C.old_new = '1'
		]]> 
	</select>
	<!--by zhaoyoan_wb 保全保费信息接口 -->
	<select id="JRQD_queryCsPremItemInfoForRI" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,A.POLICY_ID,B.ENDORSE_CODE,C.SERVICE_CODE,B.INSERT_TIME,B.UPDATE_TIME,C.ACCEPT_CODE,C.ACCEPT_STATUS,
			NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID) AS polno,A.OLD_NEW,D.VALIDATE_DATE,D.EXPIRY_DATE,A.PAIDUP_DATE,E.PREM_FREQ,E.PRODUCT_CODE,
			(SELECT EM_VALUE FROM APP___PAS__DBUSER.T_EXTRA_PREM EP WHERE EP.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND ROWNUM=1) AS em_value
			FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT E,APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A,
			APP___PAS__DBUSER.T_CS_CONTRACT_MASTER D,APP___PAS__DBUSER.T_CS_POLICY_CHANGE B,APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C
			WHERE A.POLICY_CODE=#{policy_code} AND NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID)=#{pol_no} 
			AND E.POLICY_CHG_ID=B.POLICY_CHG_ID AND E.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND E.OLD_NEW=A.OLD_NEW
			AND D.POLICY_CHG_ID=B.POLICY_CHG_ID AND D.POLICY_ID=A.POLICY_ID AND D.OLD_NEW=A.OLD_NEW
			AND A.POLICY_CHG_ID=B.POLICY_CHG_ID AND B.ACCEPT_ID=C.ACCEPT_ID
   		]]>
	</select>
	<!-- 查询受理下保单险种是否为新增附加险添加  rm 49043 add by dugang -->
	<select id="JRQD_queryAcceptForNS" resultType="java.util.Map" parameterType="java.util.Map">	
		<![CDATA[
		SELECT CCBP.BUSI_PROD_CODE,
		       CPC.apply_time
            FROM DEV_PAS.T_CS_ACCEPT_CHANGE      CAC,
                 DEV_PAS.T_CS_POLICY_CHANGE      CPC,
                 DEV_PAS.T_CS_CONTRACT_BUSI_PROD CCBP
           WHERE CAC.ACCEPT_ID = CPC.ACCEPT_ID
             AND CPC.POLICY_CHG_ID = CCBP.POLICY_CHG_ID
             AND CCBP.OLD_NEW = '1'
             AND CPC.SERVICE_CODE = 'NS'
             AND CAC.ACCEPT_STATUS = '18'
             AND CPC.POLICY_CODE=#{policy_code}
             AND CCBP.OPERATION_TYPE = '1'
             AND CCBP.BUSI_ITEM_ID = #{busi_item_id}
   		]]>
	</select>
	
	<select id="JRQD_queryRSCsPolicyChangePO" resultType="java.util.Map" parameterType="java.util.Map">
	
		<![CDATA[
		select a.change_id , b.policy_chg_id ,a.validate_time
		  from dev_pas.t_cs_accept_change a, dev_pas.t_cs_policy_change b
		 where a.change_id = b.change_id
		   and a.accept_id = b.accept_id
		   and b.policy_code =#{policy_code}
		   and a.accept_status = '18'
		   and a.service_code = 'RS'
		   order by b.insert_timestamp 
   		]]>
   	</select>
	
	<select id="JRQD_queryPolicyByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,
			       A.POLICY_ID,
			       (SELECT CA.AGENT_CODE
			          FROM DEV_PAS.T_CONTRACT_AGENT CA
			         WHERE CA.POLICY_ID = A.POLICY_ID
			           AND CA.IS_CURRENT_AGENT = '1') AGENT_CODE,
			       (SELECT CA.AGENT_NAME
			          FROM DEV_PAS.T_CONTRACT_AGENT CA
			         WHERE CA.POLICY_ID = A.POLICY_ID
			           AND CA.IS_CURRENT_AGENT = '1') AGENT_NAME
			  FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A
			 WHERE 1 = 1
			   and a.change_id = '${change_id}'
   			   and a.accept_id= '${accept_id}'
		]]>	
	</select>
	
	<select id="JRQD_queryAcceptChangeByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.CHANGE_ID, AC.ACCEPT_CODE, PC.POLICY_CODE, C.CUSTOMER_NAME
		  FROM DEV_PAS.T_CS_APPLICATION A
		 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
		    ON AC.CHANGE_ID = A.CHANGE_ID
		 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
		    ON A.CHANGE_ID = PC.CHANGE_ID
		    AND AC.ACCEPT_ID = PC.ACCEPT_ID
		 INNER JOIN DEV_PAS.T_POLICY_HOLDER H
		    ON H.POLICY_CODE = PC.POLICY_CODE
		 INNER JOIN DEV_PAS.T_CUSTOMER C
		    ON C.CUSTOMER_ID = H.CUSTOMER_ID
		 WHERE A.CHANGE_ID = '${change_id}'
		]]>	
	</select>
	<select id="JRQD_queryHistoryBQWork" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select a.change_id , b.policy_chg_id ,a.validate_time
		  from dev_pas.t_cs_accept_change a, dev_pas.t_cs_policy_change b
		 where a.change_id = b.change_id
		   and a.accept_id = b.accept_id
		   and b.policy_code =#{policy_code}
		   and a.accept_status = '18'
		]]>	
	</select>
	
	<select id="JRQD_queryStateInsuranceByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			  SELECT 
		      PC.POLICY_ID,
		      CBP.POLICY_CODE,
		      BP.PRODUCT_ABBR_NAME,
		      A.POST_CODE,
		      A.ADDRESS,
		      C.CUSTOMER_NAME,
		      C.CUSTOMER_GENDER,
		      CM.VALIDATE_DATE,
		      (SELECT TO_CHAR(CM.VALIDATE_DATE,'yyyy-mm-dd') FROM DUAL) STARTDATE,
		      (SELECT TO_CHAR(TO_DATE(#{currDate}, 'yyyy-mm-dd'),'yyyy-mm-dd') FROM DUAL) ENDDATE,
		      (CASE WHEN PA.ACKNOWLEDGE_DATE IS NULL THEN '未承保' WHEN CM.LIABILITY_STATE='3' THEN '终止' ELSE '承保' END) CONTSTATE,
		      PC.APPLY_TIME,
		      PC.FEE_AMOUNT,
		      (SELECT MIN(FT.DEAL_TIME) FROM APP___PAS__DBUSER.T_FUND_TRANS FT WHERE CI.LIST_ID = FT.LIST_ID) MINDEALDATE
		      FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC
		      LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
		      ON PC.POLICY_ID = CBP.POLICY_ID
		      LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM
		      ON CM.POLICY_ID = PC.POLICY_ID
		      LEFT JOIN DEV_PAS.T_CONTRACT_INVEST CI
		      ON CI.POLICY_ID = PC.POLICY_ID
		      AND CI.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
		      LEFT JOIN APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT PA
		      ON PA.POLICY_ID = PC.POLICY_ID
		      LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC
		      ON PC.ACCEPT_ID = CAC.ACCEPT_ID
		      RIGHT JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP
		      ON BP.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
		      RIGHT JOIN APP___PAS__DBUSER.T_POLICY_HOLDER PH
		      ON PH.POLICY_ID = PC.POLICY_ID
		      RIGHT JOIN APP___PAS__DBUSER.T_CUSTOMER C
		      ON C.CUSTOMER_ID = PH.CUSTOMER_ID
		      RIGHT JOIN APP___PAS__DBUSER.T_ADDRESS A
		      ON A.ADDRESS_ID = PH.ADDRESS_ID
		      WHERE CBP.POLICY_CODE = #{policy_code} AND PC.SERVICE_CODE='PG' AND CAC.ACCEPT_STATUS = '18'
		]]>	
	</select>
	<select id="JRQD_queryStateInsuranceByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			  SELECT
		      (SELECT SUM(PRAR.FEE_AMOUNT)
		      FROM APP___CAP__DBUSER.T_PREM_ARAP PRAR
		      WHERE PRAR.FEE_STATUS = '00'
		      AND PRAR.BUSINESS_CODE = CAC.ACCEPT_CODE
		      AND PRAR.FINISH_TIME <= TO_DATE(#{startdate}, 'YYYY-MM-DD')) PayMoney,
		      (SELECT SUM(FT1.TRANS_UNITS)
		      FROM APP___PAS__DBUSER.T_FUND_TRANS FT1
		      WHERE CI.LIST_ID = FT1.LIST_ID
		      AND FT1.DEAL_TIME <= TO_DATE(#{ContValueDate}, 'YYYY-MM-DD')) BeginCount,
		      (SELECT IUP2.CAL_BID_PRICE
		      FROM (SELECT IUP.CAL_BID_PRICE, IUP.INVEST_ACCOUNT_ID,IUP.PRICING_DATE
		      FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE IUP
		      ORDER BY IUP.PRICING_DATE DESC) IUP2
		      WHERE IUP2.INVEST_ACCOUNT_ID = CI.LIST_ID
		      AND ROWNUM = 1
		      AND IUP2.PRICING_DATE <= TO_DATE(#{ContValueDate}, 'YYYY-MM-DD')) BeginUnitPrice,
		      (SELECT IUP2.PRICING_DATE
		      FROM (SELECT IUP.PRICING_DATE
		      FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE IUP
		      ORDER BY IUP.PRICING_DATE DESC) IUP2
		      WHERE IUP2.PRICING_DATE > CAC.VALIDATE_TIME
		      AND ROWNUM = 1) ShouldValueDate,
		      (SELECT SUM(FT.TRANS_UNITS)
		      FROM APP___PAS__DBUSER.T_FUND_TRANS FT
		      WHERE FT.POLICY_CHG_ID = PC.POLICY_CHG_ID) UnitCount,
		      (SELECT IUP2.CAL_BID_PRICE
		      FROM (SELECT IUP.CAL_BID_PRICE, IUP.PRICING_DATE
		      FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE IUP
		      ORDER BY IUP.PRICING_DATE DESC) IUP2
		      WHERE IUP2.PRICING_DATE > CAC.VALIDATE_TIME
		      AND ROWNUM = 1) UnitPrice,
		      (SELECT SUM(PRAR.FEE_AMOUNT)
		      FROM APP___CAP__DBUSER.T_PREM_ARAP PRAR
		      WHERE PRAR.FEE_STATUS = '00'
		      AND PRAR.BUSINESS_CODE = CAC.ACCEPT_CODE
		      AND PRAR.FINISH_TIME BETWEEN TO_DATE(#{startdate}, 'YYYY-MM-DD') AND
		      TO_DATE(#{enddate}, 'YYYY-MM-DD')) PayMoney2,
		      (SELECT SUM(FT1.TRANS_UNITS)
		      FROM APP___PAS__DBUSER.T_FUND_TRANS FT1
		      WHERE CI.LIST_ID = FT1.LIST_ID
		      AND FT1.DEAL_TIME BETWEEN TO_DATE(#{startdate}, 'YYYY-MM-DD') AND
		      TO_DATE(#{enddate}, 'YYYY-MM-DD')
		      AND FT1.TRANS_CODE = '02') Emoney1,
		      (SELECT SUM(FT1.TRANS_UNITS)
		      FROM APP___PAS__DBUSER.T_FUND_TRANS FT1
		      WHERE CI.LIST_ID = FT1.LIST_ID
		      AND FT1.DEAL_TIME BETWEEN TO_DATE(#{startdate}, 'YYYY-MM-DD') AND
		      TO_DATE(#{enddate}, 'YYYY-MM-DD')
		      AND FT1.TRANS_CODE IN ('03', '41')) Emoney2,
		      CI.ACCUM_UNITS LastCount,
		      (SELECT IUP2.CAL_BID_PRICE
		      FROM (SELECT IUP.CAL_BID_PRICE, IUP.PRICING_DATE
		      FROM APP___PAS__DBUSER.T_INVEST_UNIT_PRICE IUP
		      ORDER BY IUP.PRICING_DATE DESC) IUP2
		      WHERE IUP2.PRICING_DATE <= TO_DATE(#{enddate}, 'YYYY-MM-DD')
		      AND ROWNUM = 1) LastUnitPrice,
		      O.ORGAN_ADDRESS CorAddress,
		      O.ORGAN_ZIPCODE ComZipCode
		      FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC
		      LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CBP
		      ON PC.POLICY_ID = CBP.POLICY_ID
		      LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC
		      ON PC.ACCEPT_ID = CAC.ACCEPT_ID
		      RIGHT JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST CI
		      ON CI.POLICY_ID = PC.POLICY_ID
		      AND CI.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
		      RIGHT JOIN APP___PAS__DBUSER.T_CONTRACT_AGENT CA
		      ON CA.POLICY_CODE = PC.POLICY_CODE
		      AND CA.IS_CURRENT_AGENT = '1'
		      RIGHT JOIN APP___PAS__DBUSER.T_AGENT AG
		      ON AG.AGENT_CODE = CA.AGENT_CODE
		      RIGHT JOIN APP___PAS__DBUSER.T_UDMP_ORG O
		      ON O.ORGAN_CODE = AG.AGENT_ORGAN_CODE
		      WHERE PC.SERVICE_CODE = 'PG'
		      AND CAC.ACCEPT_STATUS = '18'
		      AND PC.POLICY_ID = #{policyid}
			
		]]>	
	</select>
	
	<!-- 查询此次受理下的补退费合计 -->
	<select id="JRQD_PA_sumFeeAmountByChangeAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT NVL(SUM(CASE WHEN TCPA.ARAP_FLAG = '1' THEN TCPA.FEE_AMOUNT ELSE TCPA.FEE_AMOUNT * -1 END ) ,0)AS FEE_AMOUNT
			  FROM APP___PAS__DBUSER.T_CS_APPLICATION TCA INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC ON TCA.CHANGE_ID = TCAC.CHANGE_ID 
			       INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC ON TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
			       INNER JOIN APP___PAS__DBUSER.T_CS_PREM_ARAP TCPA ON TCAC.ACCEPT_CODE = TCPA.BUSINESS_CODE 
			WHERE TCA.CHANGE_ID = #{change_id} AND TCAC.ACCEPT_ID = #{accept_id} AND TCPC.POLICY_CODE = TCPA.POLICY_CODE
			 AND TCPA.FEE_STATUS <> '16' AND TCPA.FEE_STATUS<>'02'
		]]>
	</select>
	
	<!-- 1.查询保单做过的保全项  13843缺陷 -->
	<select id="JRQD_CS_findAllCsPolicyChangeForXQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.SERVICE_CODE,AP.INSERT_BY RELATED_ID ,B.ACCEPT_ID,B.POLICY_CODE,AP.APPLY_TIME
			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A 
			INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE B ON A.ACCEPT_ID=B.ACCEPT_ID 
			INNER JOIN APP___PAS__DBUSER.T_CS_APPLICATION AP ON A.CHANGE_ID=AP.CHANGE_ID 
			WHERE A.ACCEPT_STATUS=#{accept_status} AND B.POLICY_CODE=#{policy_code}
			AND AP.apply_time >#{apply_time}
		]]>
	</select>
	<!-- 2.查询做过的退保类保全，被退保的险种 -->
	<select id="JRQD_CS_findAllCsBusiProdForXQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT A.MASTER_BUSI_ITEM_ID,b.POLICY_CHG_ID,A.BUSI_PROD_CODE FROM APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD A 
			 INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE  B 
			 ON A.POLICY_CHG_ID=B.POLICY_CHG_ID
			 INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC
			 ON AC.ACCEPT_ID=B.ACCEPT_ID
			 AND A.CHANGE_ID=AC.CHANGE_ID
			 WHERE AC.ACCEPT_ID=#{accept_id}
			 AND A.OPERATION_TYPE='2'
		]]>
	</select>
	<!-- 3.查询被退保的险种是否是通过附加险做的，且此险种收取过续期保费 -->
	<select id="JRQD_CS_findAllCsBusiProdByNSForXQ" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			 SELECT COUNT(1) 
			    FROM APP___PAS__DBUSER.T_CS_PRECONT_PRODUCT T
			   INNER JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND A
			      ON T.POLICY_ID = A.POLICY_ID
			     AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID
			    INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC
			    ON T.ACCEPT_ID=AC.ACCEPT_ID 
			   WHERE T.PRECONT_STATUS = '1'
			     AND A.POLICY_PERIOD >1
			     AND AC.ACCEPT_STATUS='18'
			     AND AC.SERVICE_CODE='NS'
			     AND T.POLICY_ID = #{policy_id}
			    
		]]>
		<if test="busi_item_ids  != null and busi_item_ids.size() != 0">
			<![CDATA[ AND T.BUSI_ITEM_ID IN ]]>
			<foreach collection="busi_item_ids" item="busi_item_id" index="index" open="(" close=")" separator=",">
				#{busi_item_id}
			</foreach>
		</if>
	</select>
	
	<!-- /*4.查询保单是否做过新增长期附加险，并只收取了首期保费*/ -->
	<select id="JRQD_CS_findAllBusiProdByNSForXQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CP.BUSI_PROD_CODE
          FROM APP___PAS__DBUSER.T_CS_PRECONT_PRODUCT T
         INNER JOIN APP___PAS__DBUSER.T_CONTRACT_EXTEND A
            ON T.POLICY_ID = A.POLICY_ID
           AND T.BUSI_ITEM_ID = A.BUSI_ITEM_ID
          INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC
          ON T.ACCEPT_ID=AC.ACCEPT_ID 
         INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD CP
         ON CP.POLICY_ID=T.POLICY_ID AND T.BUSI_ITEM_ID=CP.BUSI_ITEM_ID
         WHERE T.PRECONT_STATUS = '1'
           AND A.POLICY_PERIOD =1
           AND AC.ACCEPT_STATUS='18'
           AND AC.SERVICE_CODE='NS'
		   AND A.POLICY_CODE = #{policy_code}	    
		]]>
	</select>
	
	
	<select id="JRQD_CS_queryCsPolicyAndCusInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
					SELECT CH.POLICY_CODE,
		       (SELECT CU1.CUSTOMER_ID
		          FROM APP___PAS__DBUSER.T_CUSTOMER CU1
		         WHERE CU1.CUSTOMER_ID = HOL.CUSTOMER_ID) AS HOLCUSID,
		       HOL.APPLY_CODE,
		       (SELECT CU2.CUSTOMER_NAME
		          FROM APP___PAS__DBUSER.T_CUSTOMER CU2
		         WHERE CU2.CUSTOMER_ID = HOL.CUSTOMER_ID) AS HOLNAME,
		       (SELECT CU3.CUSTOMER_ID
		          FROM APP___PAS__DBUSER.T_CUSTOMER CU3,APP___PAS__DBUSER.T_BENEFIT_INSURED TB
		         WHERE CU3.CUSTOMER_ID = LI.CUSTOMER_ID
		               AND TB.LIST_ID = LI.CUSTOMER_ID
		               AND TB.ORDER_ID = '1'
		         ) AS INSURCUSID
		  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ACC,
		       APP___PAS__DBUSER.T_CS_POLICY_CHANGE CH,
		       APP___PAS__DBUSER.T_POLICY_HOLDER    HOL,
		       APP___PAS__DBUSER.T_INSURED_LIST     LI
		 WHERE 1 = 1
		   AND ACC.ACCEPT_ID = CH.ACCEPT_ID
		   AND CH.POLICY_CODE = HOL.POLICY_CODE
		   AND CH.POLICY_CODE = LI.POLICY_CODE 
		   AND ACC.ACCEPT_CODE =  #{accept_code,jdbcType=VARCHAR}   
		]]>
	</select>

	<!-- 通过批单号和保单号查询受理号 -->
	<select id="JRQD_queryEndorseCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.ENDORSE_CODE, B.ACCEPT_CODE
        FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A, APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
       WHERE B.CHANGE_ID = A.CHANGE_ID 
       AND A.POLICY_CODE= #{policy_code}
       AND A.ENDORSE_CODE=#{endorse_code}
		]]>
	</select>
	
	<!-- RM49848-保全回退历史记录查询 -->
	<select id="JRQD_queryPolicyChangeHistoryForRB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select pc.policy_chg_id,pc.validate_time,
			ac.accept_code,ac.service_code 
			from app___pas__dbuser.t_cs_policy_change pc
			left join app___pas__dbuser.t_cs_accept_change ac
			on pc.change_id = ac.change_id
			where ac.service_code != '00'
			and ac.service_code != 'RB'
			and ac.service_code != 'XQ'
			and ac.service_code != 'CLMBACK'
			and ac.service_code != 'RZ'
			and ac.service_code != 'SP'
			and ac.service_code != 'RG'
			and ac.service_code != 'XQ'
			and pc.policy_id = #{policy_id} 
		]]>
	</select>
	<!-- RM59062 受理号查受理信息详情（P00001003184) -->
	<select id="JRQD_findPolicyPremHistoryForOutter" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT B.ACCEPT_CODE,
       B.ACCEPT_ID,
       TO_CHAR(WM_CONCAT(C.POLICY_CODE)) POLICY_CODE,
       B.ORGAN_CODE ACCEPTMANGCOM,
       (SELECT UO.ORGAN_NAME
          FROM APP___PAS__DBUSER.T_UDMP_ORG UO
         WHERE UO.ORGAN_CODE = B.ORGAN_CODE) AS ACCEPTMANGNAME,
       B.FEE_AMOUNT AS FEE_AMOUNT,
       A.CUSTOMER_ID,
       A.APPLY_NAME,
       TO_CHAR(A.APPLY_TIME, 'YYYY-MM-DD') APPLY_TIME,
       B.SERVICE_CODE,
       (SELECT T.SERVICE_NAME
          FROM APP___PAS__DBUSER.T_SERVICE T
         WHERE T.SERVICE_CODE = B.SERVICE_CODE) AS SERVICE_NAME,
       TO_CHAR(E.CUSTOMER_BIRTHDAY, 'yyyy-MM-dd') AS CUSTOMER_BIRTHDAY,
       DECODE(E.CUSTOMER_GENDER, '1', '0', '2', '1') AS CUSTOMER_GENDER,
       
       (SELECT CM.OLD_CODE
          FROM APP___PAS__DBUSER.T_CODE_MAPPER CM
         WHERE CM.CODETYPE = 'CERTI_TYPE'
           AND CM.FROM_MODLE = 'PAS'
           AND CM.NEW_CODE = E.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE,
       E.CUSTOMER_CERTI_CODE,
       TO_CHAR(E.CUST_CERT_STAR_DATE, 'yyyy-MM-dd') AS CUST_CERT_STAR_DATE,
       TO_CHAR(E.CUST_CERT_END_DATE, 'yyyy-MM-dd') AS CUST_CERT_END_DATE,
       E.JOB_CODE,
       (SELECT JC.JOB_NAME
          FROM APP___PAS__DBUSER.T_JOB_CODE JC
         WHERE JC.JOB_CODE = E.JOB_CODE) AS JOB_NAME,
       E.COMPANY_NAME,
       E.COUNTRY_CODE,
       E.MOBILE_TEL,
       E.OFFEN_USE_TEL,
       (SELECT SUM(DECODE(TPA.ARAP_FLAG,
                          2,
                          0 - TPA.FEE_AMOUNT,
                          TPA.FEE_AMOUNT))
          FROM APP___PAS__DBUSER.T_PREM_ARAP TPA
         WHERE TPA.BUSINESS_CODE = B.ACCEPT_CODE
         GROUP BY TPA.BUSINESS_CODE) AS FEE_AMOUNT_INT
  FROM APP___PAS__DBUSER.T_CS_APPLICATION   A,
       APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B,
       APP___PAS__DBUSER.T_CS_POLICY_CHANGE C,
       APP___PAS__DBUSER.T_CUSTOMER         E
 WHERE A.CHANGE_ID = B.CHANGE_ID
   AND B.ACCEPT_ID = C.ACCEPT_ID
   AND B.CHANGE_ID = C.CHANGE_ID
   AND E.CUSTOMER_ID = A.CUSTOMER_ID
   AND B.ACCEPT_ID IN (SELECT CPC.ACCEPT_ID
                         FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,
                              APP___PAS__DBUSER.T_CONTRACT_AGENT   Z
                        WHERE CPC.POLICY_ID = Z.POLICY_ID
                          AND CPC.POLICY_CODE = Z.POLICY_CODE
                          AND CPC.POLICY_CODE = #{policy_code}
                          AND Z.AGENT_CODE = #{agent_code})
   AND A.CUSTOMER_ID IS NOT NULL
   AND B.ACCEPT_STATUS != '19'
AND EXISTS(
    SELECT 1 FROM APP___PAS__DBUSER.T_PREM_ARAP TPA WHERE TPA.BUSINESS_CODE = B.ACCEPT_CODE AND TPA.FEE_STATUS  IN ('00', '03')
)
 GROUP BY B.ACCEPT_CODE,
          B.ORGAN_CODE,
          B.FEE_AMOUNT,
          A.APPLY_TIME,
          B.ACCEPT_ID,
          A.CUSTOMER_ID,
          E.CUSTOMER_NAME,
          E.CUSTOMER_BIRTHDAY,
          E.CUSTOMER_GENDER,
          E.CUSTOMER_CERT_TYPE,
          E.CUSTOMER_CERTI_CODE,
          E.CUST_CERT_STAR_DATE,
          E.CUST_CERT_END_DATE,
          E.JOB_CODE,
          E.COMPANY_NAME,
          E.COUNTRY_CODE,
          B.SERVICE_CODE,
          E.MOBILE_TEL,
          E.OFFEN_USE_TEL,
          A.APPLY_NAME
	
	
	
	]]>
	
	</select>
	<select id="JRQD_findAllCsPolicyChangeByUnitnumberForNS" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
		SELECT C.ACCEPT_CODE, C.ACCEPT_ID, P.POLICY_CHG_ID
      FROM APP___PAS__DBUSER.T_cs_PREM_ARAP        A,
           APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE C,
           APP___PAS__DBUSER.T_CS_POLICY_CHANGE P,
           APP___PAS__DBUSER.t_cs_precont_product  t
     WHERE 1 = 1
       AND C.ACCEPT_CODE = A.BUSINESS_CODE
       AND C.ACCEPT_ID = P.ACCEPT_ID
       and c.accept_id = t.accept_id
       and P.Policy_Chg_Id = t.policy_chg_id
       and a.service_code = 'NS'
       AND A.UNIT_NUMBER = '${unit_number}' 
	]]>
	</select>
	
	<!-- 保全回退使用:需求分析任务 #47237-->
	 <select id="JRQD_findAllCsRBPolicyChangeByAcceptCodeAndpolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	 	<![CDATA[
	 	
	 	 select A.POLICY_CHG_ID, B.ACCEPT_CODE, A.POLICY_CODE, A.POLICY_ID,B.SERVICE_CODE,B.ACCEPT_ID
   from APP___PAS__DBUSER.t_Cs_Policy_Change A,
        APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
 
  WHERE A.ACCEPT_ID = B.ACCEPT_ID
    AND B.ACCEPT_CODE =#{accept_code}
    AND A.POLICY_CODE =#{policy_code}
	 		]]>
	 </select>
	 
	 <!-- 保全回退使用:需求分析任务 #47237-->
	 <select id="JRQD_findAllCsRBPolicyChangeByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
	 	<![CDATA[
	 	
	 	 select A.POLICY_CHG_ID, B.ACCEPT_CODE, A.POLICY_CODE, A.POLICY_ID,B.SERVICE_CODE,B.ACCEPT_ID
   from APP___PAS__DBUSER.t_Cs_Policy_Change A,
        APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
 
  WHERE A.ACCEPT_ID = B.ACCEPT_ID
    AND B.ACCEPT_CODE =#{accept_code}
	 		]]>
	 </select>
	 <!-- add by shaocongwang 20200811 根据保续保险种转换转投到的目标投保单号查询原受理号 -->
	 <select  id="JRQD_getOriginAcceptCodeByDestinateApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
	 		<![CDATA[
	 			SELECT CAC.ACCEPT_CODE
				  FROM DEV_PAS.T_CONTRACT_MASTER  CM,
				       DEV_PAS.T_RENEW_CHANGE     RC,
				       DEV_PAS.T_CS_POLICY_CHANGE CPC,
				       DEV_PAS.T_CS_ACCEPT_CHANGE CAC
				 WHERE CM.FORMER_ID = RC.POLICY_ID
				   AND CPC.POLICY_CHG_ID = RC.POLICY_CHG_ID
				   AND CAC.ACCEPT_ID = CPC.ACCEPT_ID
				   AND CAC.ACCEPT_STATUS = '18'
				   AND RC.VALID_STATUS = '1'
				   AND RC.RENEW_CHANGE_TYPE = '3'
				   AND CM.POLICY_CODE = #{policy_code}
	 		]]>
	 </select>
	  <select  id="JRQD_findAllPolicyLongBusiForAE" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	  SELECT B.POLICY_CHG_ID
	  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE      A,
	       APP___PAS__DBUSER.T_CS_POLICY_CHANGE      B,
	       APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD C
	 WHERE A.ACCEPT_ID = B.ACCEPT_ID
	   AND B.POLICY_ID = C.POLICY_ID
	   AND B.POLICY_CHG_ID = C.POLICY_CHG_ID
	    ]]>
	   <if test=" old_new != null and old_new != ''  "><![CDATA[ AND trim(C.OLD_NEW) = #{old_new} ]]></if>
	   <if test=" old_new == null or old_new == ''  "><![CDATA[ AND trim(C.OLD_NEW) = '1' ]]></if>
	    <![CDATA[
	   AND C.LIABILITY_STATE NOT IN('3','4')
	   AND (ABS((SELECT ((MONTHS_BETWEEN(C.VALIDATE_DATE, C.EXPIRY_DATE)) / 12)
	                      FROM DUAL)) > 1 )
	   AND  A.ACCEPT_ID =#{accept_id}  
	     ]]>
	  </select>
	  
	  
	  <select id="JRQD_findConstantsInfo" resultType="java.util.Map" parameterType="java.util.Map">
		 select l.constants_value  from dev_pas.t_constants_info l  where l.constants_key = 'LOAN_GO_ONLINE_FRONTGE'
	</select>
	  
	  
	<select id="JRQD_findContractInvestPG" resultType="java.util.Map" parameterType="java.util.Map">
		 select a.settle_due_date from dev_pas.t_contract_invest a where a.policy_id = #{policy_id}
	</select>
	 <select  id="JRQD_findAllPolicyLongBusiForUX" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
	  SELECT B.POLICY_CHG_ID,B.policy_code
	  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE      A,
	       APP___PAS__DBUSER.T_CS_POLICY_CHANGE      B,
	       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
	 WHERE A.ACCEPT_ID = B.ACCEPT_ID
	   AND B.POLICY_ID = C.POLICY_ID
	    ]]>
	    <![CDATA[
	   AND (ABS((SELECT ((MONTHS_BETWEEN(C.VALIDATE_DATE, C.EXPIRY_DATE)) / 12)
	                      FROM DUAL)) > 1 )
	   AND  A.ACCEPT_ID =#{accept_id}  
	     ]]>
	  </select>

	<select  id="JRQD_findCsPolicyChangeByIsBQ" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select cpc.RELATED_ID, cpc.CHANGE_FLAG, cpc.ENDORSE_CODE, 
		    cpc.PRINT_STATUS, cpc.PRINT_NUM ,cpc.APPLY_TIME, cpc.HESITATE_FLAG, 
		    cpc.FINISH_TIME, 
		    cpc.POLICY_LOCK_FLAG, cpc.ORGAN_CODE, cpc.VALIDATE_TIME,  
		    cpc.CHANGE_ID, cpc.FEE_AMOUNT, cpc.POLICY_COPY_FLAG, cpc.POLICY_CHG_ID, cpc.SERVICE_CODE, cpc.POLICY_ID, cpc.ACCEPT_ID, 
		    cpc.NOTIFY_TYPE, 
		    cpc.ORDER_ID, cpc.POLICY_CODE , cpc.PRINT_TYPE,
		    cpc.DELAY_CAUSE,cpc.REPRINT_FLAG, cpc.PRINT_MODE,cpc.CARD_USED_FLAG
		     from dev_pas.t_cs_policy_change cpc,dev_pas.t_cs_accept_change cac where cac.accept_id = cpc.accept_id
and cpc.policy_code = #{policy_code} and cac.accept_status in ('06','07', '08', '09', '10', '11', '13')
	     ]]>
	</select>
	<!-- 查询是否存在可操作性保全项的险种 -->
	<select id="JRQD_findPolicyNorYCs" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[  
		SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD       T,
                     APP___PDS__DBUSER.T_BUSINESS_PRODUCT_SERVICE C
               WHERE   C.BUSINESS_PRD_ID = T.BUSI_PRD_ID
		]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND C.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T.POLICY_CODE = #{policy_code} ]]></if>
	</select>

<!-- 查询所有非CC已生效保全项 -->
	<select id="JRQD_findAllCsPolicyChangeOrderByFinishTimeDesc" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT 
		    A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, 
		    A.PRINT_STATUS, A.PRINT_NUM ,A.APPLY_TIME, A.HESITATE_FLAG, 
		    A.FINISH_TIME, 
		    A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
		    A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
		    A.NOTIFY_TYPE, 
		    A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE,
		    A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE,A.CARD_USED_FLAG
		    FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE A 
		    WHERE ROWNUM <=  1000 AND A.SERVICE_CODE <> 'CC' AND A.FINISH_TIME IS NOT NULL 
		]]>
		<include refid="JRQD_csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY A.FINISH_TIME desc ]]>
	</select>
</mapper>
