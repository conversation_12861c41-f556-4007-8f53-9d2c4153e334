<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.codeTable.dao.ICsCountryDao">
	<sql id="JRQD_countryWhereCondition">
		<if test=" country_english_name != null and country_english_name != ''  "><![CDATA[ AND A.COUNTRY_ENGLISH_NAME = #{country_english_name} ]]></if>
		<if test=" brief_name != null and brief_name != ''  "><![CDATA[ AND A.BRIEF_NAME = #{brief_name} ]]></if>
		<if test=" country_code != null and country_code != ''  "><![CDATA[ AND A.COUNTRY_CODE = #{country_code} ]]></if>
		<if test=" country_name != null and country_name != ''  "><![CDATA[ AND <PERSON><PERSON>COUNTRY_NAME = #{country_name} ]]></if>
		<if test=" country_uw_level  != null "><![CDATA[ AND A.COUNTRY_UW_LEVEL = #{country_uw_level} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCountryByCountryCodeCondition">
		<if test=" country_code != null and country_code != '' "><![CDATA[ AND A.COUNTRY_CODE = #{country_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addCountry"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_COUNTRY(
				COUNTRY_ENGLISH_NAME, BRIEF_NAME, COUNTRY_CODE, COUNTRY_NAME, COUNTRY_UW_LEVEL ) 
			VALUES (
				#{country_english_name, jdbcType=VARCHAR}, #{brief_name, jdbcType=VARCHAR} , #{country_code, jdbcType=VARCHAR} , #{country_name, jdbcType=VARCHAR} , #{country_uw_level, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCountry" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_COUNTRY WHERE COUNTRY_CODE = #{country_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateCountry" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_COUNTRY ]]>
		<set>
		<trim suffixOverrides=",">
			COUNTRY_ENGLISH_NAME = #{country_english_name, jdbcType=VARCHAR} ,
			BRIEF_NAME = #{brief_name, jdbcType=VARCHAR} ,
			COUNTRY_NAME = #{country_name, jdbcType=VARCHAR} ,
		    COUNTRY_UW_LEVEL = #{country_uw_level, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE COUNTRY_CODE = #{country_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_CS_findCountryByCountryCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COUNTRY_ENGLISH_NAME, A.BRIEF_NAME, A.COUNTRY_CODE, A.COUNTRY_NAME, A.COUNTRY_UW_LEVEL FROM APP___PAS__DBUSER.T_COUNTRY A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCountryByCountryCodeCondition" />
		<![CDATA[ ORDER BY A.COUNTRY_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCountry" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COUNTRY_ENGLISH_NAME, A.BRIEF_NAME, A.COUNTRY_CODE, A.COUNTRY_NAME, A.COUNTRY_UW_LEVEL FROM APP___PAS__DBUSER.T_COUNTRY A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_countryWhereCondition" />
		<![CDATA[ ORDER BY A.COUNTRY_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCountry" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COUNTRY_ENGLISH_NAME, A.BRIEF_NAME, A.COUNTRY_CODE, A.COUNTRY_NAME, A.COUNTRY_UW_LEVEL FROM APP___PAS__DBUSER.T_COUNTRY A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_countryWhereCondition" /> 
		<![CDATA[ ORDER BY A.COUNTRY_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findCountryTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_COUNTRY A WHERE 1 = 1  ]]>
		<include refid="JRQD_countryWhereCondition" /> 
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCountryForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.COUNTRY_ENGLISH_NAME, B.BRIEF_NAME, B.COUNTRY_CODE, B.COUNTRY_NAME, B.COUNTRY_UW_LEVEL FROM (
					SELECT ROWNUM RN, A.COUNTRY_ENGLISH_NAME, A.BRIEF_NAME, A.COUNTRY_CODE, A.COUNTRY_NAME, A.COUNTRY_UW_LEVEL FROM APP___PAS__DBUSER.T_COUNTRY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_countryWhereCondition" /> 
		<![CDATA[ ORDER BY A.COUNTRY_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 不显示拒保类国家 -->
	<select id="JRQD_showCountryInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.COUNTRY_ENGLISH_NAME, A.BRIEF_NAME, A.COUNTRY_CODE, A.COUNTRY_NAME, A.COUNTRY_UW_LEVEL FROM APP___PAS__DBUSER.T_COUNTRY A WHERE ROWNUM <=  1000  ]]>
		
		<![CDATA[ ORDER BY A.COUNTRY_CODE ]]>
	</select>
	
</mapper>
