<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.object.dao.ICsBenefitInsuredDao">

	<sql id="JRQD_csBenefitInsuredWhereCondition">
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" JOB_UNDERWRITE  != null "><![CDATA[ AND A.JOB_UNDERWRITE = #{JOB_UNDERWRITE} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" relation_to_insured_1 != null and relation_to_insured_1 != ''  "><![CDATA[ AND A.RELATION_TO_INSURED_1 = #{relationToInsured1} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsBenefitInsuredByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addCsBenefitInsured"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_BENEFIT_INSURED.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_BENEFIT_INSURED(
				INSERT_TIME, OPERATION_TYPE, PRODUCT_CODE, UPDATE_TIME, JOB_UNDERWRITE, INSURED_ID, 
				INSERT_TIMESTAMP, LOG_ID, ORDER_ID, OLD_NEW, POLICY_CODE, UPDATE_BY, CHANGE_ID, 
				LIST_ID, UPDATE_TIMESTAMP, POLICY_CHG_ID, INSERT_BY, BUSI_ITEM_ID, POLICY_ID ,RELATION_TO_INSURED_1) 
			VALUES (
				SYSDATE, #{operation_type, jdbcType=VARCHAR} , #{product_code, jdbcType=VARCHAR} , SYSDATE , #{JOB_UNDERWRITE, jdbcType=NUMERIC} , #{insured_id, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{log_id,jdbcType=NUMERIC} , #{order_id, jdbcType=NUMERIC} , #{old_new, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} 
				, #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}, #{relation_to_insured_1, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsBenefitInsured" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateCsBenefitInsured" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_BENEFIT_INSURED ]]>
		<set>
		<trim suffixOverrides=",">
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    JOB_UNDERWRITE = #{JOB_UNDERWRITE, jdbcType=NUMERIC} ,
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
		    ORDER_ID = #{order_id, jdbcType=NUMERIC} ,
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    RELATION_TO_INSURED_1=#{relation_to_insured_1, jdbcType=VARCHAR}
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsBenefitInsuredByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATION_TYPE, A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID,A.RELATION_TO_INSURED_1, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED A WHERE ROWNUM <=  1  ]]>
		<include refid="JRQD_queryCsBenefitInsuredByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATION_TYPE, A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID,A.RELATION_TO_INSURED_1, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csBenefitInsuredWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATION_TYPE, A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID,A.RELATION_TO_INSURED_1, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATION_TYPE, A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID,A.RELATION_TO_INSURED_1, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED A WHERE ROWNUM <=  1000  ]]>
		 <include refid="JRQD_csBenefitInsuredWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsBenefitInsuredByUpdateOrAdd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATION_TYPE, A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID,A.RELATION_TO_INSURED_1, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED A WHERE ROWNUM <=  1000  ]]>
		 <include refid="JRQD_csBenefitInsuredWhereCondition" />
		<![CDATA[ 
		and (a.operation_type = '2' or a.operation_type = '1' or a.operation_type = '0')
		ORDER BY A.LOG_ID ]]> 
	</select>
	
<!-- 查询个数操作 -->
	<select id="JRQD_findCsBenefitInsuredTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCsBenefitInsuredForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPERATION_TYPE, B.PRODUCT_CODE, B.JOB_UNDERWRITE, B.INSURED_ID, 
			B.LOG_ID, B.ORDER_ID, B.OLD_NEW, B.POLICY_CODE, B.CHANGE_ID,B.RELATION_TO_INSURED_1, 
			B.LIST_ID, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.OPERATION_TYPE, A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID,A.RELATION_TO_INSURED_1, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM APP___PAS__DBUSER.T_CS_BENEFIT_INSURED A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
