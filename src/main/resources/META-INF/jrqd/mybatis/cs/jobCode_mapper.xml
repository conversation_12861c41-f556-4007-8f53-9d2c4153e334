<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.IJobCodeDao">
	<sql id="JRQD_jobCodeWhereCondition">
		<if test=" job_uw_level != null and job_uw_level != ''  "><![CDATA[ AND A.JOB_UW_LEVEL = #{job_uw_level} ]]></if>
		<if test=" job_name != null and job_name != ''  "><![CDATA[ AND A.JOB_NAME = #{job_name} ]]></if>
		<if test=" job_category != null and job_category != ''  "><![CDATA[ AND A.JOB_CATEGORY = #{job_category} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryJobCodeByJobCodeCondition">
		<if test=" job_code != null and job_code != '' "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addJobCode"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_JOB_CODE(
				JOB_UW_LEVEL, JOB_NAME, JOB_CATEGORY, JOB_CODE ) 
			VALUES (
				#{job_uw_level, jdbcType=VARCHAR}, #{job_name, jdbcType=VARCHAR} , #{job_category, jdbcType=VARCHAR} , #{job_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteJobCode" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_JOB_CODE WHERE JOB_CODE = #{job_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateJobCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_JOB_CODE ]]>
		<set>
		<trim suffixOverrides=",">
			JOB_UW_LEVEL = #{job_uw_level, jdbcType=VARCHAR} ,
			JOB_NAME = #{job_name, jdbcType=VARCHAR} ,
			JOB_CATEGORY = #{job_category, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE JOB_CODE = #{job_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findJobCodeByJobCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.JOB_UW_LEVEL, A.JOB_NAME, A.JOB_CATEGORY, A.JOB_CODE FROM APP___PAS__DBUSER.T_JOB_CODE A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryJobCodeByJobCodeCondition" />
		<![CDATA[ ORDER BY A.JOB_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapJobCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.JOB_UW_LEVEL, A.JOB_NAME, A.JOB_CATEGORY, A.JOB_CODE FROM APP___PAS__DBUSER.T_JOB_CODE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_jobCodeWhereCondition" />
		<![CDATA[ ORDER BY A.JOB_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllJobCodeCS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.JOB_UW_LEVEL, A.JOB_NAME, A.JOB_CATEGORY, A.JOB_CODE,
		          (SELECT JCM.SH_JOB_CODE || '-' || A.JOB_NAME FROM APP___PAS__DBUSER.T_SH_JOB_MAPPING JCM              
                   WHERE JCM.JOB_CODE =A.JOB_CODE AND JCM.RETURN_MARK='1') SH_JOB_CODE_NAME 
		          FROM APP___PAS__DBUSER.T_JOB_CODE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_jobCodeWhereCondition" />
		<![CDATA[ ORDER BY A.JOB_CODE ]]>
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findJobCodeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_JOB_CODE A WHERE 1 = 1  ]]>
		<include refid="JRQD_jobCodeWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryJobCodeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.JOB_UW_LEVEL, B.JOB_NAME, B.JOB_CATEGORY, B.JOB_CODE FROM (
					SELECT ROWNUM RN, A.JOB_UW_LEVEL, A.JOB_NAME, A.JOB_CATEGORY, A.JOB_CODE FROM APP___PAS__DBUSER.T_JOB_CODE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_jobCodeWhereCondition" />
		<![CDATA[ ORDER BY A.JOB_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
     <!--根据职业编码查询类别 -->
	<select id="JRQD_queryJobCateByJobCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT C.JOB_CODE,C.JOB_NAME, D.JOB_CATEGORY_NAME 
		          FROM APP___PAS__DBUSER.T_JOB_CODE C ,APP___PAS__DBUSER.T_JOB_CATEGORY D
                  WHERE C.JOB_CATEGORY=D.JOB_CATEGORY_CODE AND C.JOB_CODE = #{job_code} ]]>
	</select>
	<select id="JRQD_queryOrgRelName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG_REL WHERE ORGAN_CODE= #{organ_code} ]]>
	</select>
	<!--根据职业编码查询最新的职业编码库 -->
	<select id="JRQD_findNewJobCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT J.JOB_CODE FROM DEV_PAS.T_JOB_CODE J WHERE 1=1 AND J.JOB_STATUS != '0' AND J.JOB_CODE= #{job_code} ]]>
	</select>
</mapper>
