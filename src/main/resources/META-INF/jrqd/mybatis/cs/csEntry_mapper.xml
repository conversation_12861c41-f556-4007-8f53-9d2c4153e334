<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="csEntry">
	<!-- 查询是否是高风险 -->
	<select id="JRQD_checkCustomerRiskLevel" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			SELECT count(1)
			  FROM APP___PAS__DBUSER.t_cs_policy_change a, APP___PAS__DBUSER.t_cs_policy_holder b, APP___PAS__DBUSER.t_customer c
			 where 
			   A.CHANGE_ID = B.CHANGE_ID
         	   and A.Policy_Chg_Id = B.Policy_Chg_Id 
			   AND a.policy_id = b.policy_id
			   and b.customer_id = c.customer_id
			   and a.change_id = #{change_id}
			   and b.old_new = '0'
			   and c.customer_risk_level = 'A'
			   and a.accept_id = #{accept_id}
			   and a.service_code = #{service_code}
        ]]>		
	</select>
	
	<!-- 查询保单表   被保人信息 -->	
	<select id="JRQD_pa_queryBenefitListInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 select 
				c.customer_id,
				a.policy_id,
		 		a.busi_item_id,		 		
		 		c.customer_name,
		        c.customer_birthday,
		        c.customer_gender,
		        c.housekeeper_flag,
		        c.smoking_flag,
		        c.retired_flag,
		        c.live_status,
		        c.death_date,
		        c.job_code,	
       			a.order_id,
       			b.insured_age       				      
		  from APP___PAS__DBUSER.t_benefit_insured a,
		       APP___PAS__DBUSER.t_insured_list    b,
		       APP___PAS__DBUSER.t_customer        c
		 where a.insured_id = b.list_id
		   and b.customer_id = c.customer_id
        ]]>	
        <if test=" policy_id  != null  and  policy_id  != ''  "><![CDATA[ AND a.policy_id = #{policy_id} ]]></if>
        <if test="policy_code  != null  and  policy_code  != ''  "><![CDATA[ AND b.policy_code = #{policy_code} ]]></if>	
        <if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ and a.busi_item_id = #{busi_item_id} ]]></if>
        <if test=" order_id != null and order_id != ''  "><![CDATA[ and a.order_id = #{order_id}	 ]]></if>
        <![CDATA[
         order by a.order_id
        ]]>
	</select>
	
	<!-- 查询保全表中被保人信息 -->
	<select id="JRQD_cs_queryBenefitListInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				select a.customer_id,
				       c.customer_name,
				       c.customer_birthday,
				       c.customer_gender,
				       c.housekeeper_flag,
				       c.smoking_flag,
				       c.retired_flag,
				       c.live_status,
				       c.death_date,
				       a.job_code,				       
				       a.insured_age,
				       b.order_id,
				       a.soci_secu
				  from APP___PAS__DBUSER.t_cs_insured_list a
				 left join APP___PAS__DBUSER.t_cs_benefit_insured b
				    on a.policy_chg_id = b.policy_chg_id
				   and a.list_id = b.insured_id
				   and a.old_new = b.old_new
				  left join APP___PAS__DBUSER.t_cs_customer c
				    on a.customer_id = c.customer_id
				   and b.change_id = c.change_id
				   and a.old_new = c.old_new
				   ]]>
				   <if test=" accept_id  != null  and  accept_id  != ''  "><![CDATA[ AND c.accept_id = #{accept_id} ]]></if>
				   <![CDATA[ 
				 where  a.old_new = #{old_new}
				   ]]>
		   <if test=" policy_id  != null  and  policy_id  != ''  "><![CDATA[ AND a.policy_id = #{policy_id} ]]></if>
		   <if test=" change_id  != null  and  change_id  != ''  "><![CDATA[ AND a.change_id = #{change_id} ]]></if>
		   <if test=" policy_chg_id  != null  and  policy_chg_id  != ''  "><![CDATA[ AND a.policy_chg_id = #{policy_chg_id} ]]></if>
		   <if test=" order_id  != null  and  order_id  != ''  "><![CDATA[ AND b.order_id = #{order_id} ]]></if>
		   <if test=" busi_item_id  != null  and  busi_item_id  != ''  "><![CDATA[ AND b.busi_item_id = #{busi_item_id} ]]></if>
	</select>
	
	
	<!-- 查询回访电话使用次数 -->
	<select id="JRQD_findCallPhoneUseTime" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
select count(1)
  from APP___PAS__DBUSER.t_customer t
 where t.mobile_tel in (
	 select tbco.mobile_tel
	   from APP___PAS__DBUSER.t_biz_call        tbc,
	        APP___PAS__DBUSER.t_biz_call_object tbco,
	        APP___PAS__DBUSER.t_cs_application  a
	  where tbc.biz_call_id = tbco.biz_call_id
	    and biz_code = a.apply_code
	    and a.change_id = #{change_id})
        ]]>		
	</select>
	
	
	<!--  应领未领生存金【已经推送收付费，未产生实付】   -->		
	<select id="JRQD_find_policy_state_flag" resultType="java.lang.Integer" parameterType="java.util.Map">
		 <![CDATA[ 
			 SELECT COUNT(1) FROM (
				SELECT 1
	          FROM APP___PAS__DBUSER.T_PAY_DUE   A,
	               APP___PAS__DBUSER.T_PREM_ARAP B,
	               APP___PAS__DBUSER.T_PAY_PLAN  C
	         WHERE A.POLICY_CODE = B.POLICY_CODE
	           AND A.PLAN_ID = C.PLAN_ID
	           AND A.UNIT_NUMBER = B.UNIT_NUMBER
	           AND C.PAY_PLAN_TYPE = '3'
	           AND B.FEE_STATUS = '00'
			   AND A.POLICY_CODE = #{policy_code}
			 GROUP BY A.POLICY_CODE
			 )
		]]>
	</select>
	
	<!-- 是否发生某个保全项 -->
	<select id="JRQD_findIsEndorseChange" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(POLICY_ID)
			  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A, APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
			 WHERE A.ACCEPT_ID = B.ACCEPT_ID
			   AND A.SERVICE_CODE = B.SERVICE_CODE
			   AND A.ACCEPT_STATUS = 18		   			   
		 ]]>		
		 <if test=" policy_id  != null  and  policy_id  != ''  "><![CDATA[ AND B.POLICY_ID = #{policy_id} ]]></if>
		 <if test=" policy_code  != null  and  policy_code  != ''  "><![CDATA[ AND B.policy_code = #{policy_code} ]]></if>
		 <if test=" service_code  != null  and  service_code  != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		 <if test=" accept_time  != null  and  accept_time  != ''  "><![CDATA[ AND A.ACCEPT_TIME > #{accept_time} ]]></if>
		 <if test=" accept_id  != null  and  accept_id  != ''  ">
			 <![CDATA[ AND EXISTS (SELECT POLICY_ID
					          FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE T
					         WHERE  T.POLICY_ID = B.POLICY_ID
					          AND T.ACCEPT_ID=#{accept_id})			 		
			 ]]>
		 </if>		 
	</select>
	
	<!-- 保全  查询计算保费需要参数 -->
	<select id="JRQD_queryCalcParams" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT A.CHANGE_ID,
                A.POLICY_CHG_ID,
                A.APPLY_CODE,
                A.POLICY_ID,
                A.POLICY_CODE,
                A.BUSI_ITEM_ID,
                A.ITEM_ID,
                A.PRODUCT_CODE,
                A.PRODUCT_ID,
                A.STD_PREM_AF,
                A.TOTAL_PREM_AF,
                A.AMOUNT,
                A.INITIAL_AMOUNT,
                A.UNIT,
                A.VALIDATE_DATE AS PRODUCT_VALIDATE_DATE,
                A.APPLY_DATE AS POLICY_APPLY_DATE,
                A.MATURITY_DATE,
                A.EXPIRY_DATE,
                A.PAY_PERIOD,
                A.PAY_YEAR,
                A.PAY_FREQ,
                A.COVERAGE_PERIOD,
                A.COVERAGE_YEAR,
                A.BENEFIT_LEVEL,
                A.CHARGE_PERIOD,
                A.CHARGE_YEAR,
                A.PAYOUT_RATE,
                A.PREM_FREQ,
                A.DEDUCTIBLE_FRANCHISE,
                A.OLD_NEW,
                A.ANNU_PAY_TYPE,
                
                O.FIELD1,
                O.FIELD6,
                O.FIELD8,
                O.FIELD15,
                
                B.VALIDATE_DATE       BUSI_VALIDATE_DATE,
                B.RENEW_TIMES,
                B.MASTER_BUSI_ITEM_ID,
                B.WAIVER,
                B.BUSI_PROD_CODE,
                B.MATURITY_DATE  AS BUSI_PROD_MATURITY_DATE,
                C.VALIDATE_DATE POLICY_VALIDATE_DATE,
                C.FORMER_ID,
                C.ORGAN_CODE,
                D.APPLY_TIME,
                
                H.CUSTOMER_ID HOLDER_CUST_ID,
                E.SINGLE_JOINT_LIFE,
                E.BUSINESS_PRD_ID,
                B.ISSUE_DATE,
                B.NEXT_FLAG
                

      FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT A
     LEFT JOIN APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT_OTHER O
        ON A.POLICY_CHG_ID = O.POLICY_CHG_ID
       AND A.ITEM_ID = O.ITEM_ID
       AND A.OLD_NEW = O.OLD_NEW
     INNER JOIN APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD B
        ON A.POLICY_CHG_ID = B.POLICY_CHG_ID
       AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
       AND A.OLD_NEW = B.OLD_NEW
     INNER JOIN APP___PAS__DBUSER.T_CS_CONTRACT_MASTER C
        ON A.POLICY_CHG_ID = C.POLICY_CHG_ID
       AND A.POLICY_ID = C.POLICY_ID
       AND A.OLD_NEW = C.OLD_NEW 
     INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_HOLDER H
        ON A.POLICY_CHG_ID = H.POLICY_CHG_ID
       AND A.POLICY_ID = H.POLICY_ID
       AND A.OLD_NEW = H.OLD_NEW
     INNER JOIN APP___PAS__DBUSER.T_CS_APPLICATION D
        ON A.CHANGE_ID = D.CHANGE_ID
     INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT E
        ON B.BUSI_PRD_ID = E.BUSINESS_PRD_ID
     WHERE 1 = 1					
        ]]>		
        
       <if test=" change_id  != null  and  change_id  != ''  "><![CDATA[ AND a.change_id = #{change_id} ]]></if>
	   <if test=" policy_chg_id  != null  and  policy_chg_id  != ''  "><![CDATA[ AND a.policy_chg_id = #{policy_chg_id} ]]></if>
	   <if test=" policy_id  != null  and  policy_id  != ''  "><![CDATA[ AND a.policy_id = #{policy_id} ]]></if>
	    <if test="policy_code  != null  and  policy_code  != ''  "><![CDATA[ AND a.policy_code = #{policy_code} ]]></if>
	   <if test=" busi_item_id  != null  and  busi_item_id  != ''  "><![CDATA[ AND a.busi_item_id = #{busi_item_id} ]]></if>
	   <if test=" item_id  != null  and  item_id  != ''  "><![CDATA[ AND a.item_id = #{item_id} ]]></if>
       <if test=" old_new  != null  and  old_new  != ''  "><![CDATA[ AND a.old_new = #{old_new} ]]></if>
	</select>
	
	<!--保全计算保费参数  每期豁免保费：本保单下此险种的主险及该主险下其他长期附加险的每期保费的和[不含豁免险]  -->
	<select id="JRQD_calcSumPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select  nvl(sum(a.std_prem_af),0) as exempt_premium
			  from APP___PAS__DBUSER.t_cs_contract_product   a,
			       APP___PAS__DBUSER.t_cs_contract_busi_prod b,
			       APP___PAS__DBUSER.t_business_product      c
			 where a.change_id = b.change_id
			   and a.policy_chg_id = b.policy_chg_id
			   and a.busi_item_id = b.busi_item_id
			   and b.busi_prd_id = c.business_prd_id
			   and a.old_new = b.old_new
			   and c.cover_period_type = 0 /*长期险*/			   
			   and a.policy_chg_id = #{policy_chg_id}
			   and a.old_new = #{old_new}
			   and a.policy_id = #{policy_id}
			   and a.is_Waived = #{is_Waived}			   
		 ]]>
		 <if test=" busi_item_id  != null  and  busi_item_id  != ''  "><![CDATA[ AND a.busi_item_id != #{busi_item_id} ]]></if>
		 
		 		
	</select>
	
	<!--保单计算保费参数  每期豁免保费：本保单下此险种的主险及该主险下其他长期附加险的每期保费的和[不含豁免险]  -->
	<select id="JRQD_calcSumPremForPa" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT NVL(SUM(A.STD_PREM_AF), 0) AS EXEMPT_PREMIUM
	          FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT   A,
	               APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B,
	               APP___PAS__DBUSER.T_BUSINESS_PRODUCT      C
	         WHERE 
	            A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
	           AND B.BUSI_PRD_ID = C.BUSINESS_PRD_ID
	           AND C.COVER_PERIOD_TYPE = 0 /*长期险*/
	           AND A.POLICY_ID = #{policy_id}
	           AND A.IS_WAIVED = #{is_Waived}	
		 ]]>
		 <if test=" waiver  != null  and  waiver  != ''  "><![CDATA[  and b.waiver =#{waiver}	 ]]></if>	
		 <if test=" busi_item_id  != null  and  busi_item_id  != ''  "><![CDATA[ AND a.busi_item_id != #{busi_item_id} ]]></if>
		 		
	</select>
	
	
	<!-- 保全计算保费参数  附加险对应主险保额 -->
	<select id="JRQD_calcSumMainAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select sum(a.amount) as main_sa
			  from APP___PAS__DBUSER.t_cs_contract_product a
			 where a.policy_chg_id = #{policy_chg_id}
			   and a.busi_item_id = #{busi_item_id}
			   and a.old_new = #{old_new}
		 ]]>		
	</select>
	
	<!-- 保全计算保费参数  附加险对应主险标准保费 -->
	<select id="JRQD_calcMainPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select a.std_prem_af as main_prem
			  from APP___PAS__DBUSER.t_cs_contract_product a
			 where a.policy_chg_id = #{policy_chg_id}
			   and a.busi_item_id = #{busi_item_id}
			   and a.old_new = #{old_new}
		 ]]>		
	</select>
	
	<select id="JRQD_PA_findPTPrintInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T1.POLICY_CHG_ID,
			       (CASE
			         WHEN A.PRODUCT_CATEGORY = '10001' THEN
			          '基本险'
			         ELSE
			          '附加险'
			       END) AS BUSI_NAME,
			       MAX(TCBP.WAIVER) AS WAIVER,
			       T1.BUSI_ITEM_ID,
			       TCBP.BUSI_PROD_CODE,
			       SUM(case
			             when t1.count_way = 5 then
			              (T1.Unit - T2.Unit)
			             else
			              (T1.AMOUNT - T2.AMOUNT)
			           end) AS AMOUNT_OLD,
			       MAX(case
			             when t1.count_way = 5 then
			              (T2.Unit)
			             else
			              (T2.AMOUNT)
			           end) AS AMOUNT_NEW,
			       SUM(CASE
			             WHEN TCBP.WAIVER = 0 THEN
			              0
			             WHEN TCBP.WAIVER = 1 THEN
			              T2.AMOUNT
			             ELSE
			              0
			           END) AS WAIVER_PREM_AF,
			       SUM(T2.STD_PREM_AF) AS STD_PREM_AF,
			       SUM(TS.EXTRA_SURRENDER)  AS EXTRA_PREM,/**加费应退*/
			       MAX(A.PRODUCT_ABBR_NAME) AS PRODUCT_ABBR_NAME,
			       SUM(TS.FEE_AMOUNT) AS FEE_AMOUNT
			  FROM APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT T1
			 INNER JOIN APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT T2
			    ON T1.ITEM_ID = T2.ITEM_ID
			   AND T1.POLICY_CHG_ID = T2.POLICY_CHG_ID
			  LEFT OUTER JOIN APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD TCBP
			    ON T1.POLICY_CHG_ID = TCBP.POLICY_CHG_ID
			   AND T2.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
			   AND TCBP.OLD_NEW = '0'
			
			  LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT A
			    ON TCBP.BUSI_PRD_ID = A.BUSINESS_PRD_ID
			 INNER JOIN APP___PAS__DBUSER.T_SURRENDER TS
			    ON T1.POLICY_CHG_ID = TS.POLICY_CHG_ID
			   AND TCBP.BUSI_ITEM_ID = TS.BUSI_ITEM_ID
			   AND T2.ITEM_ID = TS.ITEM_ID
			 WHERE 1 = 1
			   AND T1.CHANGE_ID = T2.CHANGE_ID
			   AND T1.OLD_NEW = '0'
			   AND T2.OLD_NEW = '1'
			   AND T2.OPERATION_TYPE = '2'
			   AND T1.POLICY_CHG_ID = #{policy_chg_id}
			 GROUP BY T1.POLICY_CHG_ID,
			          T1.BUSI_ITEM_ID,
			          TCBP.BUSI_PROD_CODE,
			          A.PRODUCT_CATEGORY
			  
		]]>
	</select>
	<select id="JRQD_PA_retAmountUnitSavePlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		    select tccp.old_new,tccp.product_id,tccp.operation_type,
	        tpl.counter_way,tco.field1
	    from APP___PAS__DBUSER.t_cs_contract_busi_prod tcbp left join dev_pds.t_business_product tbp on tcbp.busi_prd_id = tbp.business_prd_id 
	            inner join APP___PAS__DBUSER.t_cs_contract_product tccp on tccp.policy_chg_id = tcbp.policy_chg_id and tccp.busi_item_id = tcbp.busi_item_id
	           inner join dev_pds.t_product_life tpl  on tpl.product_id = tccp.product_id 
	           left join APP___PAS__DBUSER.t_cs_contract_product_other tco on tco.policy_chg_id = tccp.policy_chg_id and tccp.item_id = tco.item_id and tccp.old_new = tco.old_new
	           where tcbp.policy_chg_id = #{policy_chg_id} and tcbp.old_new = '1' and tcbp.busi_item_id =#{busi_item_id}
			]]>
	</select>
	
	
	<select id="JRQD_PA_calcFeeAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
     		select SUM(CASE
		             WHEN t.arap_flag = 1 THEN /*收费*/
		              t.fee_amount
		             WHEN t.arap_flag = 2 THEN /*付费*/
		              -t.fee_amount
		             ELSE
		              0
		           END) AS fee_amount
		  from APP___PAS__DBUSER.t_cs_prem_arap t
		 	where t.fee_status = '00'
   				 and t.business_code = #{accept_code}
		]]>
	</select>
	
	<!-- 保全  查询计算保费需要参数 -->
	<select id="JRQD_queryCalcParamsForPA" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT 
                A.APPLY_CODE,
                A.POLICY_ID,
                A.POLICY_CODE,
                A.BUSI_ITEM_ID,
                A.ITEM_ID,
                A.PRODUCT_CODE,
                A.PRODUCT_ID,
                A.STD_PREM_AF,
                A.TOTAL_PREM_AF,
                A.AMOUNT,
                A.INITIAL_AMOUNT,
                A.UNIT,
                A.VALIDATE_DATE        AS PRODUCT_VALIDATE_DATE,
                A.APPLY_DATE           AS POLICY_APPLY_DATE,
                A.MATURITY_DATE,
                A.EXPIRY_DATE,
                A.PAY_PERIOD,
                A.PAY_YEAR,
                A.PAY_FREQ,
                A.COVERAGE_PERIOD,
                A.COVERAGE_YEAR,
                A.BENEFIT_LEVEL,
                A.CHARGE_PERIOD,
                A.CHARGE_YEAR,
                A.PAYOUT_RATE,
                A.PREM_FREQ,
                A.DEDUCTIBLE_FRANCHISE,
              
                A.ANNU_PAY_TYPE,
                
                O.FIELD1,
                O.FIELD6,
                O.FIELD8,
                O.FIELD15,
                
                B.VALIDATE_DATE       BUSI_VALIDATE_DATE,
                B.RENEW_TIMES,
                B.MASTER_BUSI_ITEM_ID,
                B.WAIVER,
                B.BUSI_PROD_CODE,
                
                C.VALIDATE_DATE POLICY_VALIDATE_DATE,
                C.ORGAN_CODE,
                C.FORMER_ID,
                H.CUSTOMER_ID       HOLDER_CUST_ID,
                E.SINGLE_JOINT_LIFE

  FROM APP___PAS__DBUSER.T_CONTRACT_PRODUCT A
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER O
    ON A.ITEM_ID = O.ITEM_ID
 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B
    ON  A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER C
    ON A.POLICY_ID = C.POLICY_ID
 INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER H
    ON A.POLICY_ID = H.POLICY_ID
 INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT E
    ON B.BUSI_PRD_ID = E.BUSINESS_PRD_ID
 WHERE 1 = 1				
        ]]>		
        
    
	   <if test=" busi_item_id  != null  and  busi_item_id  != ''  "><![CDATA[ AND a.busi_item_id = #{busi_item_id} ]]></if>
	   <if test=" item_id  != null  and  item_id  != ''  "><![CDATA[ AND a.item_id = #{item_id} ]]></if>
     
	</select>
	
	
	<!-- 查询客户作为投保人或被保人的保单信息 -->
	<select id="JRQD_queryPolicyInfoByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select distinct t.policy_code, t.policy_id, t.customer_id
		  from (select a.policy_id, a.policy_code, a.customer_id, a.address_id
		          from APP___PAS__DBUSER.t_policy_holder a
		         where a.customer_id = #{customer_id}
		        union all
		        select b.policy_id, b.policy_code, b.customer_id, b.address_id
		          from APP___PAS__DBUSER.t_insured_list b
		         where b.customer_id = #{customer_id}) t
		]]>
	</select>
	
	
	<select id="JRQD_queryIsNotCloseAllForAI" resultType="java.lang.Integer" parameterType="java.util.Map">
		SELECT  COUNT(1)
			  FROM (SELECT TA.POLICY_ID
			          FROM APP___PAS__DBUSER.T_POLICY_HOLDER TA, APP___PAS__DBUSER.T_POLICY_ACCOUNT TB
			         WHERE TA.POLICY_ID = TB.POLICY_ID
			            AND TB.ACCOUNT_TYPE  =  '2'
                 		AND TB.INTEREST_CAPITAL > 0
			            AND TA.CUSTOMER_ID = #{customer_id}
			        UNION
			        SELECT TA.POLICY_ID
			          FROM APP___PAS__DBUSER.T_INSURED_LIST TA, APP___PAS__DBUSER.T_POLICY_ACCOUNT TB
			         WHERE TA.POLICY_ID = TB.POLICY_ID
			           AND TB.ACCOUNT_TYPE = '11'
			           AND TB.Policy_Account_Status != '2'
			           AND TA.CUSTOMER_ID = #{customer_id}) A
			 WHERE NOT EXISTS (SELECT 1
			          FROM APP___PAS__DBUSER.T_LOCK_POLICY T
			         WHERE T.POLICY_ID = A.POLICY_ID
			           AND T.LOCK_SERVICE_ID = 77)
	</select>
	
	<select id="JRQD_queryConstantsInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TO_CHAR(LISTAGG(T.CONSTANTS_VALUE, ',') WITHIN GROUP(ORDER BY T.CONSTANTS_KEY))AS CONSTANTS_VALUE
			  FROM APP___PAS__DBUSER.T_CONSTANTS_INFO T
			 WHERE T.CONSTANTS_KEY IN (${constants_key})
		   ]]>	
	</select>
	
	
	<select id="JRQD_queryAddMessageForUC" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT '公司' || A.YEAR || '年' ||
			        DECODE(A.SEASON, 1, '一', 2, '二', 3, '三', '四') || '季度的综合偿付能力充足率为' ||
			        A.SOLVENCY_RATIO * 100 || '%' || CASE
			          WHEN A.YEAR = B.YEAR AND A.SEASON = B.SEASON THEN
			           '、风险综合评级为' || B.RISK_LEVEL || '类，偿付能力充足率达到监管要求。'
			          ELSE
			           ',' || B.YEAR || '年' ||
			           DECODE(B.SEASON, 1, '一', 2, '二', 3, '三', '四') || '季度风险综合评级为' ||
			           B.RISK_LEVEL || '类，偿付能力充足率达到监管要求。'
			        END AS message_info
			   FROM (SELECT M.YEAR, M.SEASON, M.SOLVENCY_RATIO
			           FROM (SELECT T.YEAR, T.SEASON, T.SOLVENCY_RATIO
			                   FROM DEV_PAS.T_C_ROSS_INFO T
			                  WHERE T.SOLVENCY_RATIO IS NOT NULL
			                    AND T.YEAR =
			                        (SELECT MAX(TC.YEAR)
			                           FROM APP___PAS__DBUSER.T_C_ROSS_INFO TC
			                          WHERE TC.SOLVENCY_RATIO IS NOT NULL)
			                  ORDER BY T.SEASON DESC) M
			          WHERE ROWNUM = 1) A,
			        (SELECT M.YEAR, M.SEASON, M.RISK_LEVEL
			           FROM (SELECT T.YEAR, T.SEASON, T.RISK_LEVEL
			                   FROM DEV_PAS.T_C_ROSS_INFO T
			                  WHERE T.RISK_LEVEL IS NOT NULL
			                    AND T.YEAR = (SELECT MAX(TC.YEAR)
			                                    FROM APP___PAS__DBUSER.T_C_ROSS_INFO TC
			                                   WHERE TC.RISK_LEVEL IS NOT NULL)
			                  ORDER BY T.SEASON DESC) M
			          WHERE ROWNUM = 1) B
							
		   ]]>	
	</select>
	
</mapper>
