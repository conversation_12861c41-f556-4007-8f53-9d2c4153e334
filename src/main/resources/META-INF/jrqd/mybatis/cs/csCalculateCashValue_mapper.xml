<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.cs.dao.ICsCalculateCashValueDao">

	<!-- 条件查询分红信息条件 -->
	<sql id="JRQD_bonusAllocateWhereCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.ALLOCATE_DATE <= #{end_date} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.ALLOCATE_DATE > #{start_date} ]]></if>
	</sql>
	
	<!-- 条件查询分红信息 -->
	<select id="JRQD_findAllBonusAllocate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MONEY_CODE, A.ALLOCATE_ID, A.BONUS_ALLOT, A.BUSI_ITEM_ID, 
			A.BONUS_SA, A.ITEM_ID,  
			A.POLICY_CHG_ID, A.POLICY_ID, A.DIVIDEND_CHOICE, A.ORIGIN_BONUS_SA, 
		    A.ALLOCATE_DATE, A.ALLOCATE_TYPE FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_bonusAllocateWhereCondition" />
	</select>

	<!-- 计算满足条件的基本保额合计 -->
	
	<sql id="JRQD_sumAmountWhereCondition">
		<if test=" is_pause  != null "><![CDATA[ AND A.IS_PAUSE = #{is_pause} ]]></if>
		<!-- <if test=" discnted_prem_af  != null "><![CDATA[ AND A.DISCNTED_PREM_AF = #{discnted_prem_af} ]]></if>  -->
		<if test=" master_product_code != null and master_product_code != ''  "><![CDATA[ AND A.MASTER_PRODUCT_CODE = #{master_product_code} ]]></if>
		<if test=" prod_pkg_plan_code != null and prod_pkg_plan_code != ''  "><![CDATA[ AND A.PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" norenew_reason != null and norenew_reason != ''  "><![CDATA[ AND A.NORENEW_REASON = #{norenew_reason} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" extra_prem_af  != null "><![CDATA[ AND A.EXTRA_PREM_AF = #{extra_prem_af} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" cc_sa  != null "><![CDATA[ AND A.CC_SA = #{cc_sa} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" master_item_id  != null "><![CDATA[ AND A.MASTER_ITEM_ID = #{master_item_id} ]]></if>
		<if test=" master_product_id  != null "><![CDATA[ AND A.MASTER_PRODUCT_ID = #{master_product_id} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" count_way != null and count_way != ''  "><![CDATA[ AND A.COUNT_WAY = #{count_way} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" bonus_mode  != null "><![CDATA[ AND A.BONUS_MODE = #{bonus_mode} ]]></if>
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" bonus_sa  != null "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" health_service_flag  != null "><![CDATA[ AND A.HEALTH_SERVICE_FLAG = #{health_service_flag} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" unit  != null "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" pause_date  != null  and  pause_date  != ''  "><![CDATA[ AND A.PAUSE_DATE = #{pause_date} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" spreads_mode  != null "><![CDATA[ AND A.SPREADS_MODE = #{spreads_mode} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" discnt_prem_af  != null "><![CDATA[ AND A.DISCNT_PREM_AF = #{discnt_prem_af} ]]></if>
		<if test=" prem_freq != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
	</sql>
	<select id="JRQD_sumAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT nvl(sum(A.amount),0)  AS sum_amount FROM APP___PAS__DBUSER.T_contract_product  A WHERE 1=1]]>
		<include refid="JRQD_sumAmountWhereCondition" />
	</select>
	
		<!-- 	条件查询给付金额合计 -->
	<sql id="JRQD_findSumPayDueFeeAmountWhereCondition">
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" interest_amount  != null "><![CDATA[ AND A.INTEREST_AMOUNT = #{interest_amount} ]]></if>
		<if test=" sum_assured_paid  != null "><![CDATA[ AND A.SUM_ASSURED_PAID = #{sum_assured_paid} ]]></if>
		<if test=" retained_amount  != null "><![CDATA[ AND A.RETAINED_AMOUNT = #{retained_amount} ]]></if>
		<if test=" total_bonus  != null "><![CDATA[ AND A.TOTAL_BONUS = #{total_bonus} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" period  != null "><![CDATA[ AND A.PERIOD = #{period} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" pay_amount  != null "><![CDATA[ AND A.PAY_AMOUNT = #{pay_amount} ]]></if>
		<if test=" pay_id  != null "><![CDATA[ AND A.PAY_ID = #{pay_id} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND to_date(to_char(A.PAY_DUE_DATE,'yyyy-MM-dd'),'yyyy-MM-dd') >= to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND to_date(to_char(A.PAY_DUE_DATE,'yyyy-MM-dd'),'yyyy-MM-dd') < to_date(to_char(#{end_date},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
	</sql>
	<select id="JRQD_findSumPayDueFeeAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select sum(fee_amount) as sum_fee_amount from APP___PAS__DBUSER.T_PAY_DUE A WHERE 1 = 1  ]]>
		<include refid="JRQD_findSumPayDueFeeAmountWhereCondition" />
	</select>
	
	<!-- 含加费的附加险保费 （附加险种下的所有责任总保费之和） -->
	<select id="JRQD_csQueryAdditionPremAddFee" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT SUM(a.total_prem_af) as  all_prem_fee
  				FROM APP___PAS__DBUSER.T_contract_product a, APP___PAS__DBUSER.T_contract_busi_prod b
			 WHERE a.busi_item_id = b.busi_item_id
  				AND b.master_busi_item_id = #{busi_item_id}
		]]>		
	</select>
	
	<!-- 已豁免保费 -->
	<select id="JRQD_csQueryExemptedPremium" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT SUM(a.fee_amount) as all_fee_amount
  				FROM APP___PAS__DBUSER.T_prem a, APP___PAS__DBUSER.T_contract_product b, APP___PAS__DBUSER.T_contract_busi_prod c
 			WHERE a.item_id = b.item_id
   				AND b.busi_item_id = c.busi_item_id
   				AND a.due_time >= c.waiver_start
   				AND a.due_time <= c.waiver_end
   				AND a.item_id = #{item_id} 
		]]>		
	</select>
	
	<!-- 查询应领未领生存金 金额 -->
	<select id="JRQD_csCalcSurvivalAmount" resultType="java.util.Map"
		parameterType="java.util.Map">		
		<![CDATA[
				SELECT NVL(SUM(b.fee_amount),0) sur_amount
				  FROM APP___PAS__DBUSER.T_pay_plan a, APP___PAS__DBUSER.T_pay_due b
				 WHERE a.plan_id = b.plan_id
				   AND a.policy_id = b.policy_id
				   AND a.item_id = b.item_id
				   AND a.pay_plan_type = #{pay_plan_type}
				   AND b.policy_id = #{policy_id}
				   AND b.item_id = #{item_id}
				   AND b.fee_status = #{fee_status}
		]]>
	</select>
	
	<sql id="JRQD_queryPolicyAmountWhereCondition">
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND t.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>
	
	<!-- 查询保单或者险种的欠缴保费 注：43代表续期(X)，G003010000代表续期（X）T003010000(自垫收费)
	，businesstype=4003代表续期。feeStatus不等于一代表未收付，arap_flag为一代表应收 -->
	<select id="JRQD_queryPolicyAmount" resultType="java.util.Map"
		parameterType="java.util.Map">	
		<![CDATA[
				select nvl(sum(t.FEE_AMOUNT),0) as fee_amount from APP___PAS__DBUSER.T_PREM_ARAP  t 
			where  t.business_type = '4003'  and t.fee_status <> '01' and t.fee_status <> '14' and t.fee_status <> '16'
			and t.fee_status <> '19' and t.arap_flag = 1 and t.due_time < #{due_time}
			and t.policy_code = #{policy_code} and t.business_code = #{policy_code} and t.fee_type ! ='T003010000'
		]]>
		<include refid="JRQD_queryPolicyAmountWhereCondition" />
	</select>
	
	
	
	<!-- 查询本保单年度开始至现在加保数据 PA为加保 状态18为确认生效 -->
	<select id="JRQD_findCsAcceptChangeByServiceCodeAndValidate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  AC.CHANGE_ID FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC WHERE AC.ACCEPT_ID IN(
		SELECT PC.ACCEPT_ID FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC WHERE PC.SERVICE_CODE='PA' AND PC.POLICY_CODE=#{policy_code})
		AND AC.ACCEPT_STATUS='18' AND AC.VALIDATE_TIME<#{end_date} AND AC.VALIDATE_TIME>#{start_date} ORDER BY AC.VALIDATE_TIME
			]]>
	</select>
	
		<!-- 查询红利分红日 -->
	<select id="JRQD_ct_findBonusDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT *
			  FROM (SELECT t.allocate_date
			          FROM APP___PAS__DBUSER.T_bonus_allocate t
			         WHERE 	t.allocate_type = '02'
			         	AND t.bonus_allot = '1'
			         	AND t.policy_id = #{policy_id}
			           AND t.item_id = #{item_id}
			         ORDER BY allocate_date desc)
			 WHERE ROWNUM <= 1
		 ]]>		
	</select>
	
	<!-- 查询该险种本年度缴费次数  43为续期保费(错！)-->
	<select id="JRQD_cs_findAllPremArapThisYear" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_PREM_ARAP A WHERE A.POLICY_CODE=#{policy_code} AND A.BUSINESS_TYPE=#{business_type} 
		AND A.BUSI_PROD_CODE=#{busi_prod_code} AND A.FEE_STATUS in ('01','19') AND A.DUE_TIME<#{end_date ,jdbcType=DATE} AND A.DUE_TIME>=#{start_date,jdbcType=DATE}]]>
	</select>
	
	<!-- 查询失效时最远一次的续期缴费记录，该记录的缴费时间既是宽限期开始前一天 -->
	<select id="JRQD_CS_findPremArapBeforeDueTimeWhereSuspend" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_STATUS,A.DUE_TIME FROM APP___PAS__DBUSER.T_PREM_ARAP A 
						WHERE A.DERIV_TYPE = '003'
						 AND A.FEE_TYPE IN ('G003010000','T003010000') 
						 AND A.POLICY_CODE = #{policy_code}
						 AND A.BUSI_PROD_CODE = #{busi_prod_code} 
						 AND A.DUE_TIME <= #{due_time} 
						 AND A.FEE_STATUS <> '01'			 
						 AND A.FEE_STATUS <> '14'
						 AND A.FEE_STATUS <> '16' 
						 AND A.FEE_STATUS <> '19'
						 ORDER BY A.DUE_TIME ASC
		]]>
			 
	</select>
	
		<!-- 查询所有已发生的领取记录-->
	<select id="JRQD_CS_findPayDueBeforeDueTimeWhereGeted" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT  A.POLICY_ID, A.ITEM_ID,  A.FEE_AMOUNT, A.FEE_STATUS, A.PAY_DUE_DATE
		FROM APP___PAS__DBUSER.T_PAY_DUE A WHERE 1=1 ]]>
		 <if test=" policy_id != null and policy_id != ''  "><![CDATA[AND A.POLICY_ID=#{policy_id} ]]></if>
		 <if test=" item_id != null and item_id != ''  "><![CDATA[AND A.ITEM_ID=#{item_id}]]></if>
		 <if test=" pay_due_date != null and pay_due_date != ''  "><![CDATA[AND A.PAY_DUE_DATE<=#{pay_due_date} ]]></if>
		<![CDATA[ AND  A.FEE_STATUS <> '02']]>
	</select>
	
</mapper>