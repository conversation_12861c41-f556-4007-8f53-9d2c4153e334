<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsPayPlanDao">

	<sql id="JRQD_csPayPlanWhereCondition">
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" instalment_proportion  != null "><![CDATA[ AND A.INSTALMENT_PROPORTION = #{instalment_proportion} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" end_year  != null "><![CDATA[ AND A.END_YEAR = #{end_year} ]]></if>
		<if test=" pay_num  != null "><![CDATA[ AND A.PAY_NUM = #{pay_num} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" survival_mode  != null "><![CDATA[ AND A.SURVIVAL_MODE = #{survival_mode} ]]></if>
		<if test=" plan_freq != null and plan_freq != ''  "><![CDATA[ AND A.PLAN_FREQ = #{plan_freq} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" survival_invest_flag  != null "><![CDATA[ AND A.SURVIVAL_INVEST_FLAG = #{survival_invest_flag} ]]></if>
		<if test=" total_amount  != null "><![CDATA[ AND A.TOTAL_AMOUNT = #{total_amount} ]]></if>
		<if test=" survival_invest_result  != null "><![CDATA[ AND A.SURVIVAL_INVEST_RESULT = #{survival_invest_result} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" end_period != null and end_period != ''  "><![CDATA[ AND A.END_PERIOD = #{end_period} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" pension_amount  != null "><![CDATA[ AND A.PENSION_AMOUNT = #{pension_amount} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" totoal_amount  != null "><![CDATA[ AND A.TOTOAL_AMOUNT = #{totoal_amount} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" begin_date  != null  and  begin_date  != ''  "><![CDATA[ AND A.BEGIN_DATE = #{begin_date} ]]></if>
		<if test=" pay_status  != null "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
		<if test=" survival_w_mode  != null "><![CDATA[ AND A.SURVIVAL_W_MODE = #{survival_w_mode} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" pay_type != null and pay_type != ''  "><![CDATA[ AND A.PAY_TYPE = #{pay_type} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
	
		<if test=" gurnt_pay_liab  != null  and  gurnt_pay_liab  != ''  "><![CDATA[ AND A.GURNT_PAY_LIAB = #{gurnt_pay_liab} ]]></if>
		<if test=" gurnt_pay_period  != null  and  gurnt_pay_period  != ''  "><![CDATA[ AND A.GURNT_PAY_PERIOD = #{gurnt_pay_period} ]]></if>
	    <if test=" liab_code  != null  and  liab_code  != ''  "><![CDATA[ AND A.LIAB_CODE = #{liab_code} ]]></if>
		<if test=" pre_instalment_amount  != null  "><![CDATA[ AND A.PRE_INSTALMENT_AMOUNT = #{pre_instalment_amount} ]]></if>
		<if test=" one_time_flag  != null  and  one_time_flag  != ''  "><![CDATA[ AND A.ONE_TIME_FLAG = #{one_time_flag} ]]></if>
		<!--保证领取期间类型  -->
		<if test=" guarantee_period_type  != null  and  guarantee_period_type  != ''  "><![CDATA[ AND A.GUARANTEE_PERIOD_TYPE = #{guarantee_period_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsPayPlanByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsPayPlanByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsPayPlanByPayStatusCondition">
		<if test=" pay_status  != null "><![CDATA[ AND A.PAY_STATUS = #{pay_status} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsPayPlanByPayPlanTypeCondition">
		<if test=" pay_plan_type != null and pay_plan_type != '' "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsPayPlanByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addCsPayPlan"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_PAY_PLAN.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_PAY_PLAN(
				ONE_TIME_FLAG,END_DATE, INSTALMENT_AMOUNT, BUSI_PROD_CODE, INSTALMENT_PROPORTION, OLD_NEW, 
				END_YEAR, UPDATE_BY, PAY_NUM, CHANGE_ID, POLICY_ID, SURVIVAL_MODE, 
				PLAN_FREQ, OPERATION_TYPE, LIAB_ID, UPDATE_TIME, PAY_PLAN_TYPE, PAY_YEAR,
				PAY_PERIOD, POLICY_CODE, SURVIVAL_INVEST_FLAG, TOTAL_AMOUNT, UPDATE_TIMESTAMP, INSERT_BY, SURVIVAL_INVEST_RESULT, 
				MONEY_CODE, END_PERIOD, PRODUCT_CODE, PENSION_AMOUNT, 
				ITEM_ID, TOTOAL_AMOUNT, INSERT_TIMESTAMP, POLICY_CHG_ID, BUSI_ITEM_ID, 
				PLAN_ID, INSERT_TIME, BEGIN_DATE, 
				PAY_STATUS, SURVIVAL_W_MODE, LOG_ID, PAY_TYPE, PAY_DUE_DATE, 
				GURNT_PAY_LIAB,GURNT_PAY_PERIOD,annuity_amount,LIAB_NAME,LIAB_CODE,PRE_INSTALMENT_AMOUNT,
				TOTAL_PREM_AF, AMOUNT, BONUS_SA, TERMINAL_BONUS,
				GUARANTEE_PERIOD_TYPE) 
			VALUES (
				#{one_time_flag, jdbcType=NUMERIC}, #{end_date, jdbcType=DATE} , #{instalment_amount, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{instalment_proportion, jdbcType=NUMERIC} , #{old_new, jdbcType=VARCHAR} 
				, #{end_year, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{pay_num, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{survival_mode, jdbcType=NUMERIC} 
				, #{plan_freq, jdbcType=VARCHAR} , #{operation_type, jdbcType=VARCHAR} , #{liab_id, jdbcType=NUMERIC} , SYSDATE , #{pay_plan_type, jdbcType=VARCHAR} , #{pay_year, jdbcType=NUMERIC}
				, #{pay_period, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{survival_invest_flag, jdbcType=NUMERIC} , #{total_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{survival_invest_result, jdbcType=NUMERIC} 
				, #{money_code, jdbcType=VARCHAR} , #{end_period, jdbcType=VARCHAR} , #{product_code, jdbcType=VARCHAR} , #{pension_amount, jdbcType=NUMERIC} 
				, #{item_id, jdbcType=NUMERIC} , #{totoal_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} 
				, #{plan_id, jdbcType=NUMERIC} , SYSDATE , #{begin_date, jdbcType=DATE} 
				, #{pay_status, jdbcType=NUMERIC} , #{survival_w_mode, jdbcType=NUMERIC} , #{log_id, jdbcType=NUMERIC} , #{pay_type, jdbcType=VARCHAR} , #{pay_due_date, jdbcType=DATE} 
				, #{gurnt_pay_liab, jdbcType=VARCHAR}, #{gurnt_pay_period, jdbcType=NUMERIC}, #{annuity_amount, jdbcType=NUMERIC} , #{liab_name, jdbcType=VARCHAR}, #{liab_code, jdbcType=VARCHAR}, #{pre_instalment_amount, jdbcType=NUMERIC}
				, #{total_prem_af, jdbcType=NUMERIC}, #{amount, jdbcType=NUMERIC}, #{bonus_sa, jdbcType=NUMERIC}, #{terminal_bonus, jdbcType=NUMERIC}
				, #{guarantee_period_type, jdbcType=VARCHAR}   ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsPayPlan" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_PAY_PLAN WHERE   LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateCsPayPlan" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PAY_PLAN ]]>
		<set>
		<trim suffixOverrides=",">
			ONE_TIME_FLAG = #{one_time_flag, jdbcType=NUMERIC} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
		    INSTALMENT_AMOUNT = #{instalment_amount, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    INSTALMENT_PROPORTION = #{instalment_proportion, jdbcType=NUMERIC} ,
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
		    END_YEAR = #{end_year, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PAY_NUM = #{pay_num, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    SURVIVAL_MODE = #{survival_mode, jdbcType=NUMERIC} ,
			PLAN_FREQ = #{plan_freq, jdbcType=VARCHAR} ,
			operation_type = #{operation_type, jdbcType=VARCHAR} ,
		    LIAB_ID = #{liab_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			PAY_PLAN_TYPE = #{pay_plan_type, jdbcType=VARCHAR} ,
		    PAY_YEAR = #{pay_year, jdbcType=NUMERIC} ,
			PAY_PERIOD = #{pay_period, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SURVIVAL_INVEST_FLAG = #{survival_invest_flag, jdbcType=NUMERIC} ,
		    TOTAL_AMOUNT = #{total_amount, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    SURVIVAL_INVEST_RESULT = #{survival_invest_result, jdbcType=NUMERIC} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
			END_PERIOD = #{end_period, jdbcType=VARCHAR} ,
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
		    PENSION_AMOUNT = #{pension_amount, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    totoal_amount = #{totoal_amount, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
		    BEGIN_DATE = #{begin_date, jdbcType=DATE} ,
		    PAY_STATUS = #{pay_status, jdbcType=NUMERIC} ,
		    SURVIVAL_W_MODE = #{survival_w_mode, jdbcType=NUMERIC} ,
		    LOG_ID = #{log_id, jdbcType=NUMERIC} ,
			PAY_TYPE = #{pay_type, jdbcType=VARCHAR} ,
		    PAY_DUE_DATE = #{pay_due_date, jdbcType=DATE} ,
		    GURNT_PAY_LIAB=#{gurnt_pay_liab, jdbcType=VARCHAR},
		    GURNT_PAY_PERIOD=#{gurnt_pay_period, jdbcType=NUMERIC},
		    ANNUITY_AMOUNT=#{annuity_amount, jdbcType=NUMERIC},
		    PRE_INSTALMENT_AMOUNT=#{pre_instalment_amount, jdbcType=NUMERIC},
		    TOTAL_PREM_AF=#{total_prem_af, jdbcType=NUMERIC},
		    AMOUNT=#{amount, jdbcType=NUMERIC},
		    BONUS_SA=#{bonus_sa, jdbcType=NUMERIC},
		    TERMINAL_BONUS=#{terminal_bonus, jdbcType=NUMERIC},
		    GUARANTEE_PERIOD_TYPE = #{guarantee_period_type,jdbcType=VARCHAR}
		</trim>
		</set>
		<![CDATA[ WHERE   LOG_ID = #{log_id} ]]>
	</update>
	
<!-- 查询责任组层的CsPayPlanPO，并且根据pay_due_date进行排序 -->
	<select id="JRQD_FindAllCsPayPlanOrderDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR,
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID,A.PAY_TYPE, A.PAY_DUE_DATE, 
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
		 FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="JRQD_csPayPlanWhereCondition"/>
		<![CDATA[ ORDER BY A.PAY_DUE_DATE DESC]]>
	</select>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsPayPlanByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR,
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID,A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.LIAB_CODE,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
		 FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>
	
	<select id="JRQD_findCsPayPlanByOne" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GUARANTEE_PERIOD_TYPE,a.GURNT_PAY_PERIOD
     FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE 1 = 1 ]]>
     <include refid="JRQD_csPayPlanWhereCondition" />
     <![CDATA[ order by  A.INSERT_TIMESTAMP  DESC ]]>
	</select>
	
	<select id="JRQD_findCsPayPlanByLiabIdAndOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
			FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>
	
	<select id="JRQD_findCsPayPlanByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
			FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsPayPlanByItemIdCondition" />
	</select>
	
	<select id="JRQD_findCsPayPlanByPayStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR,
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
			 FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsPayPlanByPayStatusCondition" />
	</select>
	
	<select id="JRQD_findCsPayPlanByPayPlanType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR,
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT, 
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
		 FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsPayPlanByPayPlanTypeCondition" />
	</select>
	
	<select id="JRQD_findCsPayPlanByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT, 
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
			 FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryCsPayPlanByPolicyIdCondition" />
	</select>
	
<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR,
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT, 
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
			 FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.LIAB_NAME,A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE,A.ANNUITY_AMOUNT,
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.LIAB_CODE,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
			 FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllGCCsPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.LIAB_NAME,A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE,A.ANNUITY_AMOUNT,
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
			FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE ROWNUM <=  1000  
			and a.survival_mode in ('1','2','3','4')]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>
	
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllPayPlanl" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.END_YEAR, 
			A.PAY_NUM, A.POLICY_ID, A.SURVIVAL_MODE, A.PLAN_FREQ, A.LIAB_ID, 
			A.PAY_YEAR, A.PAY_PLAN_TYPE, A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, 
			A.TOTAL_AMOUNT, A.MONEY_CODE, A.SURVIVAL_INVEST_RESULT, 
			A.END_PERIOD, A.PRODUCT_CODE, A.ITEM_ID,
			A.BUSI_ITEM_ID, A.PLAN_ID,
			A.BEGIN_DATE, A.PAY_STATUS, A.SURVIVAL_W_MODE, A.PAY_TYPE, 
			A.PAY_DUE_DATE,A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
		    FROM APP___PAS__DBUSER.T_PAY_PLAN A WHERE ROWNUM <=  1000
			AND  A.PAY_PLAN_TYPE <> 12  ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>
	<!-- 查询所有操作 -->
		<select id="JRQD_findAllCTCsPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT A.ONE_TIME_FLAG, A.LIAB_NAME,A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
				A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
				A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
				A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
				A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
				A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
				A.PLAN_ID, A.BEGIN_DATE,A.ANNUITY_AMOUNT,
				A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
				A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
				A.GUARANTEE_PERIOD_TYPE
				FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE ROWNUM <=  1000  
				and a.pay_plan_type in ('8','3','10')]]>
			<include refid="JRQD_csPayPlanWhereCondition" />
		</select>
<!-- 查询个数操作 -->
	<select id="JRQD_findCsPayPlanTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE 1 = 1  ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCsPayPlanForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ONE_TIME_FLAG, B.END_DATE, B.INSTALMENT_AMOUNT, B.BUSI_PROD_CODE, B.INSTALMENT_PROPORTION, B.OLD_NEW, 
			B.END_YEAR, B.PAY_NUM, B.CHANGE_ID, B.POLICY_ID, B.SURVIVAL_MODE, 
			B.PLAN_FREQ, B.OPERATION_TYPE, B.LIAB_ID, B.PAY_PLAN_TYPE, B.PAY_YEAR, 
			B.PAY_PERIOD, B.POLICY_CODE, B.SURVIVAL_INVEST_FLAG, B.TOTAL_AMOUNT, B.SURVIVAL_INVEST_RESULT, 
			B.MONEY_CODE, B.END_PERIOD, B.PRODUCT_CODE, B.PENSION_AMOUNT,
			B.ITEM_ID, B.TOTOAL_AMOUNT, B.POLICY_CHG_ID, B.BUSI_ITEM_ID, 
			B.PLAN_ID, B.BEGIN_DATE, 
			B.PAY_STATUS, B.SURVIVAL_W_MODE, B.LOG_ID, B.PAY_TYPE, B.PAY_DUE_DATE, B.PRE_INSTALMENT_AMOUNT,
			B.GURNT_PAY_LIAB,B.GURNT_PAY_PERIOD,
			B.TOTAL_PREM_AF, B.AMOUNT, B.BONUS_SA, B.TERMINAL_BONUS,
			B.GUARANTEE_PERIOD_TYPE
			FROM (
					SELECT ROWNUM RN, A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR,
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,
			A.TOTAL_PREM_AF, A.AMOUNT, A.BONUS_SA, A.TERMINAL_BONUS,
			A.GUARANTEE_PERIOD_TYPE
			FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- by zhaoyoan 通过LIAB_ID(保障责任ID)从T_LIABILITY查询对应给付责任名称 -->
	<select id="JRQD_queryGetDutyName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select LIAB_NAME from APP___PAS__DBUSER.T_LIABILITY LIAB_ID=#{liab_id}
		]]>
	</select>
	<!-- by zhaoyoan 通过保单号查询所有险种及该险种其它信息 -->
	<select id="JRQD_findBusiProdInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select  a.busi_item_id,
			sum(nvl(a.TOTAL_AMOUNT,0)) as total_amount,
			b.BUSI_PROD_CODE,
			(select PRODUCT_NAME_SYS from APP___PAS__DBUSER.T_BUSINESS_PRODUCT 
				where BUSINESS_PRD_ID=b.BUSI_PRD_ID) as product_name_sys 
			from APP___PAS__DBUSER.T_PAY_PLAN a ,APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD b 
			where 1=1 
			and b.busi_item_id=a.busi_item_id 
			and a.pay_plan_type in ('2','4') 
			and a.policy_code=#{policy_code} 
			group by a.busi_item_id,b.BUSI_PROD_CODE,b.BUSI_PRD_ID
		]]>
	</select>
	
		<!-- by lianghxit 保单质押第三方止付   查询首次领取日期 -->
	<select id="JRQD_getFirstGetDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT(MIN(BEGIN_DATE)) AS BEGIN_DATE FROM APP___PAS__DBUSER.T_PAY_PLAN WHERE POLICY_CODE = #{policy_code} 
			
		]]>
			<if test=" liab_id  != null "><![CDATA[ AND LIAB_ID = #{liab_id} ]]></if>
	</select>
	
	<!--2018-06-14 只有养老年金责任的可以申请变更1106，由于领取方式变更和领取年龄变更用同一sql，故修改sql语句  -->
	<select id="JRQD_findReceiveModeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT  B.OLD_POL_NO,A.*,
			    (SELECT PAY_NAME FROM APP___PAS__DBUSER.T_PAY_TYPE WHERE PAY_TYPE=A.PLAN_FREQ)PAY_NAME,
                (SELECT T3.CUSTOMER_BIRTHDAY FROM DEV_PAS.T_INSURED_LIST T1,DEV_PAS.T_BENEFIT_INSURED T2,DEV_PAS.T_CUSTOMER T3  WHERE  T1.LIST_ID=T2.INSURED_ID AND T2.ORDER_ID='1' AND T3.CUSTOMER_ID=T1.CUSTOMER_ID AND T1.POLICY_CODE = #{policy_code} AND ROWNUM=1)CUSTOMER_BIRTHDAY
			FROM APP___PAS__DBUSER.T_PAY_PLAN A,APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B,
            APP___PAS__DBUSER.T_CONTRACT_PRODUCT C
			WHERE B.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND B.BUSI_ITEM_ID=C.BUSI_ITEM_ID 
            AND C.ITEM_ID=A.ITEM_ID AND A.POLICY_CODE=B.POLICY_CODE AND A.POLICY_CODE = #{policy_code}
		]]>
		<if test=" liab_id  != null "><![CDATA[ AND LIAB_ID = #{liab_id} ]]></if>
	</select>
	
	<!--根据丹凤讲解来修改领取年龄初始化接口-->
	<select id="JRQD_findReceiveModeByPolicyCode2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT B.VALIDATE_DATE,
				       B.APPLY_CODE,
				       B.BRANCH_CODE,
				       B.ORGAN_CODE,
				       B.POLICY_TYPE,
				       B.CHANNEL_TYPE,
				       B.SERVICE_BANK,
				       B.SERVICE_BANK_BRANCH,
				       C.LIABILITY_STATE,
				       C.RENEW_TIMES,
				       C.BUSI_PRD_ID,
				       C.HESITATION_PERIOD_DAY,
				       D.MATURITY_DATE,
				       D.EXPIRY_DATE,
				       D.PREM_FREQ,
				       D.CHARGE_YEAR,
				       D.CHARGE_PERIOD,
				       D.COVERAGE_PERIOD,
				       D.COVERAGE_YEAR,
				       D.AMOUNT,
				       D.BONUS_SA,
				       D.TOTAL_PREM_AF,
				       D.INITIAL_DISCNT_PREM_AF,
				       D.UNIT,
				       D.PAY_YEAR,
				       D.PAY_PERIOD,
				       D.IS_MASTER_ITEM,
				       D.PRODUCT_ID,
				       D.PAY_FREQ,
				       D.EXTRA_PREM_AF,
				       A.PLAN_FREQ,
				       A.PLAN_ID,
				       A.POLICY_ID,
				       B.POLICY_CODE,
				       A.BUSI_ITEM_ID,
				       A.ITEM_ID,
				       A.BUSI_PROD_CODE,
				       A.PRODUCT_CODE,
				       A.LIAB_ID,
				       A.LIAB_NAME,
				       A.LIAB_CODE,
				       A.PAY_DUE_DATE,
				       A.PAY_NUM,
				       A.BEGIN_DATE,
				       A.END_DATE,
				       A.PAY_STATUS,
				       A.SURVIVAL_MODE,
				       A.PAY_PLAN_TYPE,
				       A.BENE_AMOUNT,
				       A.TOTAL_AMOUNT,
				       A.INSTALMENT_AMOUNT,
				       A.SURVIVAL_INVEST_FLAG,
				       A.SURVIVAL_INVEST_RESULT,
				       A.GURNT_PAY_LIAB,
				       A.GURNT_PAY_PERIOD,
				       A.ONE_TIME_FLAG,
				       A.PAY_TYPE,
				       A.SURVIVAL_W_MODE,
				       D.ANNU_PAY_TYPE,
				       (SELECT T3.CUSTOMER_BIRTHDAY
				          FROM DEV_PAS.T_INSURED_LIST    T1,
				               DEV_PAS.T_BENEFIT_INSURED T2,
				               DEV_PAS.T_CUSTOMER        T3
				         WHERE T1.LIST_ID = T2.INSURED_ID
				           AND T2.ORDER_ID = '1'
				           AND T3.CUSTOMER_ID = T1.CUSTOMER_ID
				           AND T1.POLICY_CODE = #{policy_code}
				           AND ROWNUM = 1) CUSTOMER_BIRTHDAY,
				       (SELECT PAY_NAME FROM APP___PAS__DBUSER.T_PAY_TYPE WHERE PAY_TYPE=A.PLAN_FREQ)PAY_NAME,
				       C.OLD_POL_NO
				  FROM APP___PAS__DBUSER.T_PAY_PLAN A
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER B
				    ON A.POLICY_ID = B.POLICY_ID
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
				    ON (A.BUSI_ITEM_ID = C.BUSI_ITEM_ID AND A.POLICY_ID = B.POLICY_ID)
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_PRODUCT D
				    ON (A.ITEM_ID = D.ITEM_ID AND A.POLICY_ID = B.POLICY_ID)
				 WHERE A.POLICY_CODE = #{policy_code}
				   AND A.LIAB_ID = '1106'
		]]>
	</select>
	
	<!-- by lvby 查询生存金领取项    -->
	<select id="JRQD_findLiabIdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TL.LIAB_ID, TL.LIAB_NAME FROM APP___PAS__DBUSER.T_PAY_PLAN TP, APP___PAS__DBUSER.T_LIABILITY TL WHERE TP.LIAB_ID = TL.LIAB_ID AND TP.POLICY_CODE = #{policy_code} ORDER BY TP.LIAB_ID
		]]>
	</select>
	
	<select id="JRQD_findCsPayPlanAndfeeTypeByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 SELECT B.TYPE_DESC,
        C.LIAB_NAME,
        A.END_DATE,
        A.INSTALMENT_AMOUNT,
        A.BUSI_PROD_CODE,
        A.INSTALMENT_PROPORTION,
        A.OLD_NEW,
        A.END_YEAR,
        A.PAY_NUM,
        A.CHANGE_ID,
        A.POLICY_ID,
        A.SURVIVAL_MODE,
        A.PLAN_FREQ,
        A.OPERATION_TYPE,
        A.LIAB_ID,
        A.PAY_PLAN_TYPE,
        A.PAY_YEAR,
        A.PAY_PERIOD,
        A.POLICY_CODE,
        A.SURVIVAL_INVEST_FLAG,
        A.TOTAL_AMOUNT,
        A.SURVIVAL_INVEST_RESULT,
        A.MONEY_CODE,
        A.END_PERIOD,
        A.PRODUCT_CODE,
        A.PENSION_AMOUNT,
        A.ITEM_ID,
        A.TOTOAL_AMOUNT,
        A.POLICY_CHG_ID,
        A.BUSI_ITEM_ID,
        A.PLAN_ID,
        A.BEGIN_DATE,
        A.PAY_STATUS,
        A.SURVIVAL_W_MODE,
        A.LOG_ID,
        A.PAY_TYPE,
        A.PAY_DUE_DATE,
        A.PRE_INSTALMENT_AMOUNT,
        A.ONE_TIME_FLAG
   FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A
   LEFT JOIN APP___PAS__DBUSER.T_PAY_PLAN_TYPE B
     ON A.PAY_PLAN_TYPE = B.PAY_PLAN_TYPE
   LEFT JOIN APP___PAS__DBUSER.T_LIABILITY C
     ON A.LIAB_ID = C.LIAB_ID
     WHERE 1=1 
		]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>
	
<!-- 查询只有生存金 满期金的信息 -->
	<select id="JRQD_findSurMat" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.LIAB_CODE,A.GUARANTEE_PERIOD_TYPE,
			CASE
            WHEN (A.PAY_PLAN_TYPE = '3') THEN
            '年金'
            WHEN (A.PAY_PLAN_TYPE != '3') THEN
            P.TYPE_DESC  
            END AS PAY_PLAN_NAME
			FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A 
			LEFT JOIN APP___PAS__DBUSER.T_PAY_PLAN_TYPE P
   			ON A.PAY_PLAN_TYPE = P.PAY_PLAN_TYPE
			WHERE A.PAY_PLAN_TYPE IN('3','4','10','8') AND ROWNUM <=  1000  ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>
	
	<!-- 查询发生实际领取的给付计划 -->
	<select id="JRQD_findPaidPayPlan" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ONE_TIME_FLAG, A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE, 
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.GUARANTEE_PERIOD_TYPE  FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A 
			WHERE A.PAY_NUM > 0
		]]>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</select>
	<!-- 查询险种可变更的领取方式 -->
	<select id="JRQD_findAllBusiProdGMmethod" resultType="java.util.Map" parameterType="java.util.Map">
			  select tpev.code2,tpev.code as PLAN_FREQ
			   from APP___PAS__DBUSER.T_PAGECFG_PRD_ELEMENT   tppe,
			        APP___PAS__DBUSER.T_PAGECFG_PRD_CATE_RELA tppcr,
			        APP___PAS__DBUSER.t_product_life          tpl,
			        APP___PAS__DBUSER.t_pagecfg_unit          tpu,
			        APP___PAS__DBUSER.t_pagecfg_element_value tpev
			 where tpl.product_id = tppcr.product_id 
			    and tppcr.relation_id = tppe.relation_id
			    and tppe.unit_code = tpu.unit_code
			    and tppe.list_value = tpev.list_value_code
			    and (tppe.unit_code = '139' 
                or tppe.unit_code = '061')
			    and tpl.internal_id =#{product_code}
	</select>
	<!-- 查询所有生存金年金类型 for CM-->
	<select id="JRQD_findCsPayPlanForCM" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT A.ONE_TIME_FLAG, A.LIAB_NAME,A.LIAB_CODE,A.END_DATE, A.INSTALMENT_AMOUNT, A.BUSI_PROD_CODE, A.INSTALMENT_PROPORTION, A.OLD_NEW, 
			A.END_YEAR, A.PAY_NUM, A.CHANGE_ID, A.POLICY_ID, A.SURVIVAL_MODE, 
			A.PLAN_FREQ, A.OPERATION_TYPE, A.LIAB_ID, A.PAY_PLAN_TYPE, A.PAY_YEAR, 
			A.PAY_PERIOD, A.POLICY_CODE, A.SURVIVAL_INVEST_FLAG, A.TOTAL_AMOUNT, A.SURVIVAL_INVEST_RESULT, 
			A.MONEY_CODE, A.END_PERIOD, A.PRODUCT_CODE, A.PENSION_AMOUNT,
			A.ITEM_ID, A.TOTOAL_AMOUNT, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, 
			A.PLAN_ID, A.BEGIN_DATE,A.ANNUITY_AMOUNT,
			A.PAY_STATUS, A.SURVIVAL_W_MODE, A.LOG_ID, A.PAY_TYPE, A.PAY_DUE_DATE, A.PRE_INSTALMENT_AMOUNT,
			A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.GUARANTEE_PERIOD_TYPE 
		FROM APP___PAS__DBUSER.T_CS_PAY_PLAN A WHERE A.PAY_PLAN_TYPE IN('3','4','12')  ]]>
		<include refid="JRQD_csPayPlanWhereCondition" />
	</select>
	
	<!-- 修改操作标准保费 -->
	<update id="JRQD_updateCsPayPlanPreInAm" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PAY_PLAN A
   SET A.PRE_INSTALMENT_AMOUNT = #{pre_instalment_amount}
 WHERE 1 = 1
   AND A.OLD_NEW = #{old_new}
   AND A.CHANGE_ID = #{change_id}
   AND A.POLICY_CHG_ID = #{policy_chg_id}
   AND A.PLAN_ID = #{plan_id}
   ]]>
	</update>
	
	<!--add by zhangweiwei 2019-03-05 18:00    更新定期给付金额 -->
		<update id="JRQD_updateCsPayPlanInAm" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_PAY_PLAN A
   SET A.INSTALMENT_AMOUNT = #{instalment_amount}
 WHERE 1 = 1
   AND A.OLD_NEW = #{old_new}
   AND A.CHANGE_ID = #{change_id}
   AND A.POLICY_CHG_ID = #{policy_chg_id}
   AND A.PLAN_ID = #{plan_id}
   ]]>
	</update>
</mapper>
