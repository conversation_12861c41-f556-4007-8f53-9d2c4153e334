<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ICsContractMasterDao">

	<sql id="JRQD_csContractMasterWhereCondition">
		<if test=" policy_pwd != null and policy_pwd != ''  "><![CDATA[ AND A.POLICY_PWD = #{policy_pwd} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND <PERSON>.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND trim(A.OLD_NEW) = trim(#{old_new}) ]]></if>
		<if test=" sale_agent_name != null and sale_agent_name != ''  "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} ]]></if>
		<if test=" insured_family  != null "><![CDATA[ AND A.INSURED_FAMILY = #{insured_family} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" derivation != null and derivation != ''  "><![CDATA[ AND A.DERIVATION = #{derivation} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" basic_remark != null and basic_remark != ''  "><![CDATA[ AND A.BASIC_REMARK = #{basic_remark} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pwd_invalid_flag  != null "><![CDATA[ AND A.PWD_INVALID_FLAG = #{pwd_invalid_flag} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''  "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" double_mainrisk_flag  != null "><![CDATA[ AND A.DOUBLE_MAINRISK_FLAG = #{double_mainrisk_flag} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" sale_agent_code != null and sale_agent_code != ''  "><![CDATA[ AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENCY_CODE = #{agency_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" service_handler_code != null and service_handler_code != ''  "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" service_handler != null and service_handler != ''  "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if>
		<if test=" e_service_flag  != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" dc_indi  != null "><![CDATA[ AND A.DC_INDI = #{dc_indi} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" agent_org_id != null and agent_org_id != ''  "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<if test=" lang_code != null and lang_code != ''  "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
		<if test=" sale_com_code != null "><![CDATA[ AND A.SALE_COM_CODE = #{sale_com_code} ]]></if>
		<if test=" initial_prem_date != null "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" rerinstate_date  != null "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" statistic_channel  != null "><![CDATA[ AND A.STATISTIC_CHANNEL = #{statistic_channel} ]]></if>
		<if test=" trust_busi_flag  != null "><![CDATA[ AND A.TRUST_BUSI_FLAG = #{trust_busi_flag} ]]></if>
		<!-- 保单号 -->
		<if test=" list_policy_code != null and list_policy_code.size()!=0">
			<![CDATA[ AND A.POLICY_CODE IN ]]>
			<foreach collection ="list_policy_code" item="list_policy_code" index="index" open="(" close=")" separator=",">#{list_policy_code}</foreach>
		</if>
	<if test=" input_type  != null "><![CDATA[ AND A.INPUT_TYPE = #{input_type} ]]></if>
	<if test=" subinput_type  != null "><![CDATA[ AND A.SUBINPUT_TYPE = #{subinput_type} ]]></if>
	<if test=" group_sale_type != null and group_sale_type != '' "><![CDATA[ AND A.group_sale_type = #{group_sale_type} ]]></if>	
	<!-- 多主险标识 -->
	<if test=" multi_mainrisk_flag != null and multi_mainrisk_flag != '' "><![CDATA[ AND A.multi_mainrisk_flag = #{MULTI_MAINRISK_FLAG} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsContractMasterByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>
	
	<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsContractMasterByChangeIdCondition">
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND trim(A.OLD_NEW) = trim(#{old_new}) ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>		

<!-- 添加操作 -->
	<insert id="JRQD_addCsContractMaster"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_CONTRACT_MASTER.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CONTRACT_MASTER(
				POLICY_RELATION_TYPE,STATISTIC_CHANNEL,POLICY_REINSURE_FLAG,REINSURED_TIMES,
				POLICY_PWD, MEDIA_TYPE, APPLY_CODE, ORGAN_CODE, CHANNEL_TYPE, OLD_NEW, SALE_AGENT_NAME, 
				INSURED_FAMILY, UPDATE_BY, CHANGE_ID, POLICY_ID, DERIVATION, OPERATION_TYPE, 
				UPDATE_TIME,  BASIC_REMARK, POLICY_TYPE, EXPIRY_DATE, PWD_INVALID_FLAG, SUBMIT_CHANNEL, 
				LIABILITY_STATE, POLICY_CODE, SALE_AGENT_CODE, BRANCH_CODE, UPDATE_TIMESTAMP, INSERT_BY, AGENCY_CODE, 
				MONEY_CODE, APL_PERMIT, SERVICE_HANDLER_CODE, APPLY_DATE, INITIAL_VALIDATE_DATE, INSERT_TIMESTAMP, SUBMISSION_DATE, 
				SERVICE_HANDLER, E_SERVICE_FLAG, POLICY_CHG_ID, SERVICE_BANK_BRANCH, DC_INDI, INSERT_TIME, END_CAUSE, 
				ISSUE_DATE, LAPSE_CAUSE, DECISION_CODE, AGENT_ORG_ID, VALIDATE_DATE, LOG_ID, SERVICE_BANK, 
				LANG_CODE, FORMER_ID,LAPSE_DATE,SALE_COM_CODE,INITIAL_PREM_DATE,RERINSTATE_DATE,BANK_AGENCY_FLAG,RELATION_POLICY_CODE,INPUT_TYPE,
				IS_SELF_INSURED,DOUBLE_MAINRISK_FLAG,SUBINPUT_TYPE,POLICY_PRD_FLAG ,WINNING_START_FLAG,group_sale_type,APPLY_TIME,IS_ALONE_INSURE,
				MEDICAL_INSURANCE_CARD,CALL_TIME_LIST,TRUST_BUSI_FLAG,MULTI_MAINRISK_FLAG)
			VALUES (#{policy_relation_type,jdbcType=NUMERIC},#{statistic_channel, jdbcType=VARCHAR},
				#{policy_reinsure_flag, jdbcType=VARCHAR} ,#{reinsured_times, jdbcType=NUMERIC} ,
				#{policy_pwd, jdbcType=VARCHAR}, #{media_type, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , #{channel_type, jdbcType=VARCHAR} , #{old_new, jdbcType=VARCHAR} , #{sale_agent_name, jdbcType=VARCHAR} 
				, #{insured_family, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{derivation, jdbcType=VARCHAR} , #{operation_type, jdbcType=VARCHAR} 
				, SYSDATE ,  #{basic_remark, jdbcType=VARCHAR} , #{policy_type, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} , #{pwd_invalid_flag, jdbcType=NUMERIC} , #{submit_channel, jdbcType=NUMERIC} 
				, #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{sale_agent_code, jdbcType=VARCHAR} , #{branch_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{agency_code, jdbcType=VARCHAR} 
				, #{money_code, jdbcType=VARCHAR} , #{apl_permit, jdbcType=NUMERIC} , #{service_handler_code, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{initial_validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{submission_date, jdbcType=DATE} 
				, #{service_handler, jdbcType=VARCHAR} , #{e_service_flag, jdbcType=NUMERIC} , #{policy_chg_id, jdbcType=NUMERIC} , #{service_bank_branch, jdbcType=VARCHAR} , #{dc_indi, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} 
				, #{issue_date, jdbcType=DATE} , #{lapse_cause, jdbcType=VARCHAR} , #{decision_code, jdbcType=VARCHAR} , #{agent_org_id, jdbcType=VARCHAR} , #{validate_date, jdbcType=DATE} , #{log_id,jdbcType=NUMERIC} , #{service_bank, jdbcType=VARCHAR} 
				, #{lang_code, jdbcType=VARCHAR} , #{former_id, jdbcType=NUMERIC},#{lapse_date, jdbcType=DATE},#{sale_com_code, jdbcType=VARCHAR},#{initial_prem_date, jdbcType=DATE},#{rerinstate_date, jdbcType=DATE},#{bank_agency_flag, jdbcType=NUMERIC},#{relation_policy_code, jdbcType=VARCHAR},#{input_type, jdbcType=VARCHAR}
				,#{is_self_insured, jdbcType=NUMERIC}, #{double_mainrisk_flag, jdbcType=NUMERIC},#{subinput_type, jdbcType=VARCHAR}, #{policy_prd_flag, jdbcType=NUMERIC},#{winning_start_flag, jdbcType=NUMERIC} ,#{group_sale_type, jdbcType=NUMERIC},#{apply_time, jdbcType=VARCHAR},#{is_alone_insure,jdbcType=NUMERIC},#{medical_insurance_card, jdbcType=NUMERIC}, #{call_time_list, jdbcType=VARCHAR}
			,#{trust_busi_flag,jdbcType=NUMERIC},#{multi_mainrisk_flag, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

	<select id="JRQD_findByAgentCodeAndPolicyCode" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
			 select count(1) from APP___PAS__DBUSER.T_contract_agent a where 1 = 1
   		]]>
   		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
   		<if test=" list_policy_code != null and list_policy_code.size()!=0">
				<![CDATA[ AND A.POLICY_CODE IN ]]>
				<foreach collection ="list_policy_code" item="list_policy_code" index="index" open="(" close=")" separator=",">#{list_policy_code}</foreach>
		</if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agency_code} ]]></if>
	</select>
<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsContractMaster" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER WHERE LOG_ID = #{log_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateCsContractMaster" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
			POLICY_RELATION_TYPE=#{policy_relation_type,jdbcType=NUMERIC},
			STATISTIC_CHANNEL = #{statistic_channel, jdbcType=VARCHAR},
			POLICY_PWD = #{policy_pwd, jdbcType=VARCHAR} ,
		    MEDIA_TYPE = #{media_type, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
			SALE_AGENT_NAME = #{sale_agent_name, jdbcType=VARCHAR} ,
		    INSURED_FAMILY = #{insured_family, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			DERIVATION = #{derivation, jdbcType=VARCHAR} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			BASIC_REMARK = #{basic_remark, jdbcType=VARCHAR} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    PWD_INVALID_FLAG = #{pwd_invalid_flag, jdbcType=NUMERIC} ,
			SUBMIT_CHANNEL = #{submit_channel, jdbcType=NUMERIC} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			SALE_AGENT_CODE = #{sale_agent_code, jdbcType=VARCHAR} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			AGENCY_CODE = #{agency_code, jdbcType=VARCHAR} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
		    APL_PERMIT = #{apl_permit, jdbcType=NUMERIC} ,
			SERVICE_HANDLER_CODE = #{service_handler_code, jdbcType=VARCHAR} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    INITIAL_VALIDATE_DATE = #{initial_validate_date, jdbcType=DATE} ,
		    SUBMISSION_DATE = #{submission_date, jdbcType=DATE} ,
			SERVICE_HANDLER = #{service_handler, jdbcType=VARCHAR} ,
		    E_SERVICE_FLAG = #{e_service_flag, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
			SERVICE_BANK_BRANCH = #{service_bank_branch, jdbcType=VARCHAR} ,
		    DC_INDI = #{dc_indi, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		    ISSUE_DATE = #{issue_date, jdbcType=DATE} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
			AGENT_ORG_ID = #{agent_org_id, jdbcType=VARCHAR} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
			SERVICE_BANK = #{service_bank, jdbcType=VARCHAR} ,
			LANG_CODE = #{lang_code, jdbcType=VARCHAR} ,
		    FORMER_ID = #{former_id, jdbcType=NUMERIC} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
		    SALE_COM_CODE = #{sale_com_code, jdbcType=VARCHAR},
		    INITIAL_PREM_DATE = #{initial_prem_date, jdbcType=DATE},
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE},
		    RELATION_POLICY_CODE = #{relation_policy_code, jdbcType=VARCHAR},
		    INPUT_TYPE=#{input_type, jdbcType=VARCHAR},
		    SUBINPUT_TYPE=#{subinput_type, jdbcType=VARCHAR},
		    group_sale_type=#{group_sale_type, jdbcType=VARCHAR},
		    apply_time = #{apply_time, jdbcType=VARCHAR},
			is_alone_insure = #{is_alone_insure,jdbcType=NUMERIC},
			POLICY_REINSURE_FLAG = #{policy_reinsure_flag,jdbcType=VARCHAR},
			REINSURED_TIMES = #{reinsured_times,jdbcType=NUMERIC},
			TRUST_BUSI_FLAG = #{trust_busi_flag,jdbcType=NUMERIC},
			MULTI_MAINRISK_FLAG=#{multi_mainrisk_flag,jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>
	
	<!-- 修改操作 -->
	<update id="JRQD_updateCsContractMasterByPolicyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
			BASIC_REMARK = #{basic_remark, jdbcType=VARCHAR} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR}  
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID = #{policy_id} AND trim(OLD_NEW) = trim(#{old_new}) AND LOG_ID = #{log_id} AND CHANGE_ID = #{change_id} AND POLICY_CHG_ID = #{policy_chg_id}]]>
	</update>
	<!-- 给表中加标示用于区分减额交清，险种转换的字符 -->
	<update id="JRQD_updateCsContractMasterMark" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
			  SPECIAL_CUS_FLAG  =2 
		</trim>
		</set>
		<![CDATA[ WHERE  trim(OLD_NEW) = trim(#{old_new})]]>
		<if test=" log_id != null and log_id != ''  "><![CDATA[  AND LOG_ID = #{log_id} ]]></if>
		<if test=" policy_id != null and policy_id != ''  "><![CDATA[  AND  POLICY_ID = #{policy_id} ]]></if>
		 <![CDATA[AND CHANGE_ID = #{change_id} AND POLICY_CHG_ID = #{policy_chg_id}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsContractMasterByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_RELATION_TYPE,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,A.STATISTIC_CHANNEL, A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.OLD_NEW, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.CHANGE_ID, A.POLICY_ID, A.DERIVATION, A.OPERATION_TYPE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.BRANCH_CODE, A.AGENCY_CODE, A.WINNING_START_FLAG,
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, 
			A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.VALIDATE_DATE, A.LOG_ID, A.SERVICE_BANK, 
			A.LANG_CODE, A.FORMER_ID,A.LAPSE_DATE,A.SALE_COM_CODE,A.INITIAL_PREM_DATE,A.RERINSTATE_DATE,A.INPUT_TYPE,A.SUBINPUT_TYPE, null as TAX_EXTENSION_SOURCE,null as POLICY_PRD_FLAG,a.group_sale_type
			,A.APPLY_TIME,a.is_alone_insure,A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST,A.TRUST_BUSI_FLAG,A.TRUST_BUSI_FLAG,null as MULTI_MAINRISK_FLAG
			FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER A WHERE ROWNUM <=  1  ]]>
		<include refid="JRQD_queryCsContractMasterByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
<!-- *********************************** -->
<!-- 按索引查询操作  朱丽华-->	
	<select id="JRQD_findCsContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_RELATION_TYPE,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.OLD_NEW, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.CHANGE_ID, A.POLICY_ID, A.DERIVATION, A.OPERATION_TYPE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.BRANCH_CODE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, 
			A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.VALIDATE_DATE, A.LOG_ID, A.SERVICE_BANK, A.DOUBLE_MAINRISK_FLAG,
			A.LANG_CODE, A.FORMER_ID,A.LAPSE_DATE,A.SALE_COM_CODE,A.INITIAL_PREM_DATE,A.RERINSTATE_DATE,A.BANK_AGENCY_FLAG, 
			A.RELATION_POLICY_CODE,A.INPUT_TYPE,A.SUBINPUT_TYPE,null as TAX_EXTENSION_SOURCE,null as POLICY_PRD_FLAG,A.WINNING_START_FLAG,a.group_sale_type
			,A.APPLY_TIME,a.is_alone_insure,A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST,A.TRUST_BUSI_FLAG,null as MULTI_MAINRISK_FLAG
			  FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER A WHERE ROWNUM <=  1  ]]>
		<include refid="JRQD_csContractMasterWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
<!-- *********************************** -->

<!-- 按索引查询信托标识查询字段新增信托标识:trust_busi_flag操作	
	<select id="JRQD_findCsContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_RELATION_TYPE,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.OLD_NEW, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.CHANGE_ID, A.POLICY_ID, A.DERIVATION, A.OPERATION_TYPE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.BRANCH_CODE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, 
			A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.VALIDATE_DATE, A.LOG_ID, A.SERVICE_BANK, A.DOUBLE_MAINRISK_FLAG,
			A.LANG_CODE, A.FORMER_ID,A.LAPSE_DATE,A.SALE_COM_CODE,A.INITIAL_PREM_DATE,A.RERINSTATE_DATE,A.BANK_AGENCY_FLAG, 
			A.RELATION_POLICY_CODE,A.INPUT_TYPE,A.SUBINPUT_TYPE,null as TAX_EXTENSION_SOURCE,null as POLICY_PRD_FLAG,A.WINNING_START_FLAG,a.group_sale_type
			,A.APPLY_TIME,a.is_alone_insure,A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST,A.TRUST_BUSI_FLAG
			  FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER A WHERE ROWNUM <=  1  ]]>
		<include refid="JRQD_csContractMasterWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select> -->
<!-- *********************************** -->
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_RELATION_TYPE,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.OLD_NEW, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.CHANGE_ID, A.POLICY_ID, A.DERIVATION, A.OPERATION_TYPE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.BRANCH_CODE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, 
			A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.VALIDATE_DATE, A.LOG_ID, A.SERVICE_BANK, 
			A.LANG_CODE, A.FORMER_ID,A.LAPSE_DATE,A.SALE_COM_CODE,A.INITIAL_PREM_DATE,A.RERINSTATE_DATE,A.INPUT_TYPE,
			A.SUBINPUT_TYPE,null as TAX_EXTENSION_SOURCE,null as POLICY_PRD_FLAG,WINNING_START_FLAG,a.group_sale_type,A.APPLY_TIME,a.is_alone_insure,
			A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST,null as MULTI_MAINRISK_FLAG FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_RELATION_TYPE,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.OLD_NEW, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.CHANGE_ID, A.POLICY_ID, A.DERIVATION, A.OPERATION_TYPE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.BRANCH_CODE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.WINNING_START_FLAG,
			A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.VALIDATE_DATE, A.LOG_ID, A.SERVICE_BANK, 
			A.LANG_CODE, A.FORMER_ID,A.LAPSE_DATE,A.SALE_COM_CODE,A.INITIAL_PREM_DATE,A.RERINSTATE_DATE,A.RELATION_POLICY_CODE,A.TRUST_BUSI_FLAG,
			A.INPUT_TYPE,A.is_self_insured,A.SUBINPUT_TYPE,null as TAX_EXTENSION_SOURCE,null as POLICY_PRD_FLAG,A.INSERT_BY,a.group_sale_type ,A.APPLY_TIME,
			a.is_alone_insure,A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST,null as MULTI_MAINRISK_FLAG FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER A WHERE ROWNUM <=  1000  ]]>
		 <include refid="JRQD_csContractMasterWhereCondition" /> 
		 <![CDATA[ ORDER BY A.POLICY_CODE ]]>
	</select>
 

<!-- 查询所有操作 -->
	<select id="JRQD_findAllpolicyContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.POLICY_RELATION_TYPE,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE,A.APPLY_CODE,A.ORGAN_CODE,A.CHANNEL_TYPE,A.SALE_AGENT_NAME,A.WINNING_START_FLAG,
			 A.INSURED_FAMILY, A.DERIVATION,A.BASIC_REMARK,A.POLICY_TYPE,A.EXPIRY_DATE,A.PWD_INVALID_FLAG,A.SUBMIT_CHANNEL,A.LIABILITY_STATE, A.POLICY_CODE,
			 A.SALE_AGENT_CODE,A.BRANCH_CODE,A.AGENCY_CODE,A.MONEY_CODE ,A.SERVICE_HANDLER_CODE,A.APPLY_DATE,A.INITIAL_VALIDATE_DATE,A.SUBMISSION_DATE,A.SERVICE_HANDLER,
			 A.E_SERVICE_FLAG,A.SERVICE_BANK_BRANCH,A.DC_INDI,A.END_CAUSE,A.ISSUE_DATE,A.LAPSE_CAUSE,A.DECISION_CODE,A.AGENT_ORG_ID,A.VALIDATE_DATE,A.DOUBLE_MAINRISK_FLAG,
             A.SERVICE_BANK,A.LANG_CODE,A.FORMER_ID,A.LAPSE_DATE, A.SALE_COM_CODE, A.INITIAL_PREM_DATE,A.RERINSTATE_DATE,A.INPUT_TYPE,A.SUBINPUT_TYPE,A.TRUST_BUSI_FLAG ,null as MULTI_MAINRISK_FLAG,
		     null as TAX_EXTENSION_SOURCE,null as POLICY_PRD_FLAG,a.group_sale_type ,A.APPLY_TIME,a.is_alone_insure,A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE ROWNUM <=  1000  ]]>
		 <include refid="JRQD_csContractMasterWhereCondition" /> 
		 <![CDATA[ ORDER BY A.POLICY_CODE ]]>
	</select>
<!-- 查询个数操作 -->
	<select id="JRQD_findCsContractMasterTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCsContractMasterForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.POLICY_RELATION_TYPE,B.POLICY_PWD, B.MEDIA_TYPE, B.APPLY_CODE, B.ORGAN_CODE, B.CHANNEL_TYPE, B.OLD_NEW, B.SALE_AGENT_NAME, 
			B.INSURED_FAMILY, B.CHANGE_ID, B.POLICY_ID, B.DERIVATION, B.OPERATION_TYPE, 
			B.BASIC_REMARK, B.POLICY_TYPE, B.EXPIRY_DATE, B.PWD_INVALID_FLAG, B.SUBMIT_CHANNEL, 
			B.LIABILITY_STATE, B.POLICY_CODE, B.SALE_AGENT_CODE, B.BRANCH_CODE, B.AGENCY_CODE, 
			B.MONEY_CODE, B.APL_PERMIT, B.SERVICE_HANDLER_CODE, B.APPLY_DATE, B.INITIAL_VALIDATE_DATE, B.SUBMISSION_DATE, 
			B.SERVICE_HANDLER, B.E_SERVICE_FLAG, B.POLICY_CHG_ID, B.SERVICE_BANK_BRANCH, B.DC_INDI, B.END_CAUSE, 
			B.ISSUE_DATE, B.LAPSE_CAUSE, B.DECISION_CODE, B.AGENT_ORG_ID, B.VALIDATE_DATE, B.LOG_ID, B.SERVICE_BANK, 
			B.LANG_CODE, B.FORMER_ID,B.LAPSE_DATE,B.SALE_COM_CODE,B.INITIAL_PREM_DATE,B.RERINSTATE_DATE,b.is_alone_insure FROM (
					SELECT ROWNUM RN, A.POLICY_RELATION_TYPE,A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.OLD_NEW, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.CHANGE_ID, A.POLICY_ID, A.DERIVATION, A.OPERATION_TYPE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.BRANCH_CODE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, 
			A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.VALIDATE_DATE, A.LOG_ID, A.SERVICE_BANK, null as TAX_EXTENSION_SOURCE,null as POLICY_PRD_FLAG,null as MULTI_MAINRISK_FLAG,
			A.LANG_CODE, A.FORMER_ID,A.LAPSE_DATE,A.SALE_COM_CODE,A.INITIAL_PREM_DATE,A.RERINSTATE_DATE,A.INPUT_TYPE,A.SUBINPUT_TYPE,a.is_alone_insure,A.MEDICAL_INSURANCE_CARD,A.CALL_TIME_LIST FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsContractMasterByChgID" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER WHERE OLD_NEW = #{old_new} AND CHANGE_ID = #{change_id}]]>
	</delete>
	
	<delete id="JRQD_deleteCsContractMasterByPoChgID" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER WHERE OLD_NEW = #{old_new} AND POLICY_CHG_ID = #{policy_chg_id}]]>
	</delete>
	
	<!-- 修改生效日期和失效日期-->
	<update id="JRQD_updateContractMasterValidDate" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CONTRACT_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE},
		    OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR}  
		</trim>
		</set>
		<![CDATA[ WHERE  1=1 
			AND CHANGE_ID = #{change_id} 
			AND POLICY_CHG_ID = #{policy_chg_id}]]>
			AND OLD_NEW = #{old_new}
	</update>
	
	<!-- 根据保全变更id和保单变更id值列表查询抄单保单主表 -->
	<select id="JRQD_findCsContractMasterByChgIDAndPChgIds" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select a.policy_id,
			       a.apply_code,
			       to_char(a.policy_chg_id) policy_chg_id,
			       a.policy_type,
			       to_char(a.media_type) as media_type,
			       a.organ_code,
			       a.branch_code,
			       to_char(a.submit_channel) as submit_channel,
			       a.policy_pwd,
			       to_char(a.liability_state) as liability_state,
			       to_char(a.apply_date, 'yyyy-mm-dd') as apply_date,
			       to_char(a.validate_date, 'yyyy-mm-dd') as  validate_date,
			       (select pay_mode
			          from APP___PAS__DBUSER.t_cs_payer_account t
			         where t.change_id = a.change_id
			           and t.policy_chg_id = a.policy_chg_id
			           and t.old_new = '1'
			           and t.policy_id = a.policy_id
			           and rownum = 1) as pay_mode,
			       (select pay_next
			          from APP___PAS__DBUSER.t_cs_payer_account t
			         where t.change_id = a.change_id
			           and t.policy_chg_id = a.policy_chg_id
			           and t.old_new = '1'
			           and t.policy_id = a.policy_id
			           and rownum = 1) as pay_next,
			       (select account_name
			          from APP___PAS__DBUSER.t_cs_payer_account t
			         where t.change_id = a.change_id
			           and t.policy_chg_id = a.policy_chg_id
			           and t.old_new = '1'
			           and t.policy_id = a.policy_id
			           and rownum = 1) as account_name,
			       (select next_account_name
			          from APP___PAS__DBUSER.t_cs_payer_account t
			         where t.change_id = a.change_id
			           and t.policy_chg_id = a.policy_chg_id
			           and t.old_new = '1'
			           and t.policy_id = a.policy_id
			           and rownum = 1) as next_account_name
			  from APP___PAS__DBUSER.t_cs_contract_master a
			 where 1 = 1
		 ]]>
		 <if test=" change_id  != null "><![CDATA[ and a.change_id = #{change_id} ]]></if>
		 <if test=" policy_chg_id  != null "><![CDATA[and a.policy_chg_id = #{policy_chg_id} ]]></if>
		 <if test="policy_chg_ids  != null and policy_chg_ids.size()!=0">
			<![CDATA[ and a.policy_chg_id in ]]>
			<foreach collection="policy_chg_ids" item="policy_chg_id_item"
				index="index" open="(" close=")" separator=",">#{policy_chg_id_item}</foreach>
		</if>
		 <if test=" old_new  != null "><![CDATA[ and a.old_new = #{old_new} ]]></if>
	</select>
	
	<!-- 根据投保人客户id查询 保单主表 -->
	<select id="JRQD_findContractMasterByCustomerIds" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select cm.policy_id,
			       cm.apply_code,
			       '' policy_chg_id,
			       cm.policy_type,
			       to_char(cm.media_type) as media_type,
			       cm.organ_code,
			       cm.branch_code,
			       to_char(cm.submit_channel) as submit_channel,
			       cm.policy_pwd,
			       to_char(cm.liability_state) as liability_state,
			       to_char(cm.apply_date, 'yyyy-mm-dd') as apply_date,
			       to_char(cm.validate_date, 'yyyy-mm-dd') as validate_date,
			       (select pay_mode
			          from APP___PAS__DBUSER.T_PAYER_ACCOUNT t
			         where t.policy_id = cm.policy_id
			           and rownum = 1) as pay_mode,
			       (select pay_next
			          from APP___PAS__DBUSER.T_PAYER_ACCOUNT t
			         where t.policy_id = cm.policy_id
			           and rownum = 1) as pay_next,
			       (select account_name
			          from APP___PAS__DBUSER.T_PAYER_ACCOUNT t
			         where t.policy_id = cm.policy_id
			           and rownum = 1) as account_name,
			       (select next_account_name
			          from APP___PAS__DBUSER.T_PAYER_ACCOUNT t
			         where t.policy_id = cm.policy_id
			           and rownum = 1) as next_account_name
			  from APP___PAS__DBUSER.T_CONTRACT_MASTER cm
			 where 1=1 
		]]>        
		
		 <if test=" customer_id  != null and  policy_id  != null ">
			 <![CDATA[ 
				 and exists (select 1 from APP___PAS__DBUSER.T_POLICY_HOLDER ph where cm.policy_id = ph.policy_id 
				 and ph.customer_id = #{customer_id}  and ph.policy_id <> #{policy_id} ) 
			 ]]>
		 </if>
		 <if test=" policy_ids  != null"><![CDATA[ and to_char(cm.policy_id) in #{policy_ids} ]]></if>
	</select>
	
	<!--by zhaoyoan_wb 通过保单号查询对应保单id -->
	<select id="JRQD_findPolicyIdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select policy_id from APP___PAS__DBUSER.t_cs_contract_master 
			where POLICY_CODE=#{policy_code} 
			and rownum=1
		]]>
	</select>
	<!--by zhaoyoan_wb 根据保单号查询网销的数据条数 -->
	<select id="JRQD_findWebSaleTotalByPolicyCode" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1) from APP___PAS__DBUSER.t_cs_contract_master 
			where CHANNEL_TYPE='8' 
			and POLICY_CODE=#{policy_code}
		]]>
	</select>
	<!-- 根据保单变更ID删除操作 -->	
	<delete id="JRQD_deleteCsContractMasterByChangeID" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER WHERE CHANGE_ID = #{change_id}]]>
	</delete>
	<!-- 根据保单变更ID删除操作 -->	
	<delete id="JRQD_deleteCsContractMasterByPolicyChgId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_CONTRACT_MASTER WHERE POLICY_CHG_ID = #{policy_chg_id}]]>
	</delete>
	<select id="JRQD_findCsContractMasterss" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[ SELECT  A.POLICY_RELATION_TYPE,A.POLICY_ID,
	   A.STATISTIC_CHANNEL,null as TAX_EXTENSION_SOURCE,null as POLICY_PRD_FLAG,
	   A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES,
       A.POLICY_PWD,
       A.MEDIA_TYPE,
       A.APPLY_CODE,
       A.ORGAN_CODE,
       A.CHANNEL_TYPE,      
       A.SALE_AGENT_NAME,
       A.INSURED_FAMILY,
       A.DERIVATION,
       A.BASIC_REMARK,
       A.POLICY_TYPE,
       A.EXPIRY_DATE,
       A.PWD_INVALID_FLAG,
       A.SUBMIT_CHANNEL,
       A.LIABILITY_STATE,
       A.POLICY_CODE,
       A.SALE_AGENT_CODE,
       A.BRANCH_CODE,
       A.AGENCY_CODE,
       A.MONEY_CODE,
       A.SERVICE_HANDLER_CODE,
       A.APPLY_DATE,
       A.INITIAL_VALIDATE_DATE,
       A.SUBMISSION_DATE,
       A.SERVICE_HANDLER,
       A.E_SERVICE_FLAG,
       A.SERVICE_BANK_BRANCH,
       A.DC_INDI,
       A.END_CAUSE,
       A.ISSUE_DATE,
       A.LAPSE_CAUSE,
       A.DECISION_CODE,
       A.AGENT_ORG_ID,
       A.VALIDATE_DATE,
       A.SERVICE_BANK,
       A.LANG_CODE,
       A.FORMER_ID,
       A.LAPSE_DATE,
       A.SALE_COM_CODE,
       A.INITIAL_PREM_DATE,
       A.RERINSTATE_DATE,
       A.INPUT_TYPE,
       A.SUBINPUT_TYPE,
       A.WINNING_START_FLAG,
       a.is_alone_insure,
       A.MEDICAL_INSURANCE_CARD,
       null as MULTI_MAINRISK_FLAG,
       A.CALL_TIME_LIST
  FROM dev_pas.T_CONTRACT_MASTER A
   WHERE 1=1  ]]>
<include refid="JRQD_csContractMasterWhereCondition" />
</select>

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findisValidPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select count(1)
  from dev_pas.t_contract_master a
 where a.liability_state = '1'
   and (exists (select 1
          from dev_pas.t_insured_list b
         where a.policy_id = b.policy_id
           and b.customer_id =  #{customer_id})
           or exists (select 1
          from dev_pas.t_policy_holder c
         where a.policy_id = c.policy_id
           and c.customer_id =  #{customer_id}))  ]]>
		
	</select>

	<!-- 根据客户id查询保单冻结信息 -->
	<select id="JRQD_PA_queryContractMasterByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select distinct m.policy_id,/**保单id*/
	       m.policy_code,/**保单号号*/
	      (select tc.customer_name from dev_pas.t_customer tc where tc.customer_id=h.customer_id) as h_name,/**投保人*/
	      (select tc.customer_name from dev_pas.t_customer tc where tc.customer_id=l.customer_id) as l_name,/**被保人*/
	       t.validate_date,/**生效日期*/
	       t.apply_date,/**保险起期*/
	       t.expiry_date,/**保险止期*/
	       m.liability_state,/**保单状态*/
	       p.product_code_sys,/**险种代码*/
	       p.product_name_std,/**险种名称*/
	       (select tc.customer_name from dev_pas.t_customer tc where  tc.customer_id=b.customer_id) as b_name,/**受益人*/
	       b.share_rate/**受益基本保额*/
	  from dev_pas.t_customer         c,
	       dev_pas.t_policy_holder    h,
	       dev_pas.t_contract_master  m,
	       dev_pas.t_insured_list     l,
	       dev_pas.t_contract_bene    b,
	       dev_pas.t_business_product p,
	       dev_pas.t_contract_busi_prod t
	 where c.customer_id = h.customer_id
	   and h.policy_id = m.policy_id
	   and h.policy_id = l.policy_id
	   and h.policy_id = b.policy_id
	   and b.busi_item_id =t.busi_item_id
	   and t.busi_prd_id=p.business_prd_id
	   and (( m.liability_state=3 and exists (select d.policy_id from dev_pas.t_pay_due d where d.fee_status='00' 
	   and d.policy_id=m.policy_id)) or m.liability_state=1)
	   and b.share_order=1
	   and c.customer_id = h.customer_id
	   and c.customer_id=#{customer_id} ]]>
		
	</select>
	<!-- 根据客户id查询保单解冻信息 -->
	<select id="JRQD_PA_queryPolicyFreezeByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select distinct m.policy_id, /**保单id*/
                m.policy_code, /**保单号号*/
                (select tc.customer_name
                   from dev_pas.t_customer tc
                  where tc.customer_id = h.customer_id) as h_name, /**投保人*/
                (select tc.customer_name
                   from dev_pas.t_customer tc
                  where tc.customer_id = l.customer_id) as l_name, /**被保人*/
                t.validate_date, /**生效日期*/
                t.apply_date, /**保险起期*/
                t.expiry_date, /**保险止期*/
                m.liability_state, /**保单状态*/
                f.freeze_date, /**冻结起期*/
                f.freeze_end_date /**冻结止起*/

		  from dev_pas.t_cs_policy_change   a,
		       dev_pas.t_contract_master    m,
		       dev_pas.t_policy_freeze      f,
		       dev_pas.t_contract_busi_prod t,
		       dev_pas.t_policy_holder      h,
		       dev_pas.t_insured_list       l
		 where a.policy_id = m.policy_id
		   and m.policy_id = f.policy_id
		   and f.policy_id = t.policy_id
		   and f.policy_id = h.policy_id
		   and f.policy_id = l.policy_id
           and a.accept_id = #{accept_id} ]]>
		<if test=" freeze_id  != null "><![CDATA[ AND f.freeze_id = #{freeze_id} ]]></if> 
	</select>
	<!-- 根据受理变更id查询失效保单数量 -->
	<select  id="JRQD_findInvalidPolicyByAcceptid" resultType="java.lang.Integer" parameterType="java.util.Map">
	 		<![CDATA[
	 			SELECT COUNT(1)
				  FROM DEV_PAS.T_CS_POLICY_CHANGE P,
				       DEV_PAS.T_CS_CONTRACT_MASTER C
				 WHERE P.POLICY_CHG_ID = C.POLICY_CHG_ID
				   AND P.ACCEPT_ID = #{accept_id}
				   AND C.OLD_NEW = '1'
				   AND C.LIABILITY_STATE = '4'
	 		]]>
	</select>

	<select id="JRQD_queryContractMasterByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select cpc.policy_code,

			   (select cus.customer_name
				from dev_pas.t_cs_policy_holder cph, dev_pas.t_customer cus
				where cus.customer_id = cph.customer_id
				  and cph.old_new = '0'
				  and cph.policy_chg_id = cpc.policy_chg_id) h_name,

			   (select cus.customer_name
				FROm dev_pas.t_customer cus
				where cus.customer_id = il.customer_id) l_name,

			   ccbp.validate_date validate_date,
			   ccbp.validate_date apply_date,
			   ccbp.expiry_date expiry_date,

			   (select cm.liability_state
				from dev_pas.t_contract_master cm
				where cm.policy_id = cpc.policy_id) liability_state,

			   ccbp.busi_prod_code product_code_sys,

			   (select bp.product_name_std
				from dev_pas.t_business_product bp
				where bp.business_prd_id = ccbp.busi_prd_id) product_name_std,

			   (select to_char(wm_concat(cb.customer_name))
				FROM dev_pas.t_contract_bene cb
				where cb.busi_item_id = ccbp.busi_item_id) b_name,

			   (select sum(cp.amount)
				FROM dev_pas.t_contract_product cp
				where cp.busi_item_id = ccbp.busi_item_id) share_rate

		from dev_pas.t_cs_policy_change   cpc,
			 dev_pas.t_contract_busi_prod ccbp,
			 dev_pas.t_insured_list       il
		where cpc.accept_id = #{accept_id}
		  and ccbp.policy_id = cpc.policy_id
		  and il.policy_id = ccbp.policy_id

		]]>

	</select>
	
	<select id="JRQD_find_contract_master_by_accept_code" resultType="java.lang.Integer"
		parameterType="java.util.Map">
	<![CDATA[
		
	select count(1)
 		 from dev_pas.t_cs_accept_change ac
 		 left join dev_pas.t_Cs_Policy_Change pc
  		  on ac.accept_id = pc.accept_id

 		where ac.accept_code = #{accept_code}
  		 and exists (select 1
             from dev_pas.t_contract_master m
             where m.policy_code = pc.policy_code
        	 and m.medical_insurance_card is not null
        )
		
	]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllEmailChangeContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		select distinct policy_code
  from (select b.policy_code
          from dev_pas.t_cs_accept_change a,
               dev_pas.t_cs_policy_change b,
               dev_pas.t_cs_policy_holder c,
               dev_pas.t_cs_policy_holder d,
               dev_pas.t_cs_address       e,
               dev_pas.t_cs_address       f,
               dev_pas.t_contract_master  tcm,
               dev_pas.t_contract_agent   tca
         WHERE a.accept_id = b.accept_id
           and b.policy_chg_id = c.policy_chg_id
           and b.policy_chg_id = d.policy_chg_id
           and c.policy_id = d.policy_id
           and c.address_id = e.address_id
           and d.address_id = f.address_id
           and c.old_new = '1'
           and d.old_new = '0'
           and e.old_new = '1'
           and f.old_new = '0'
           and e.email is not null
           and e.email <> f.email
           and e.accept_id = f.accept_id
           and e.accept_id = a.accept_id
           and b.policy_code = tcm.policy_code
           and tcm.service_bank_branch like '01%'
           and tcm.bank_agency_flag = '1'
           and tca.channel_type = '03'
           and tca.policy_code = b.policy_code
           and tca.is_nb_agent = '1'
           and a.accept_code = #{accept_code}
        union all
        select b.policy_code
          from dev_pas.t_cs_accept_change a,
               dev_pas.t_cs_policy_change b,
               dev_pas.t_cs_policy_holder c,
               dev_pas.t_cs_policy_holder d,
               dev_pas.t_cs_customer      e,
               dev_pas.t_cs_customer      f,
               dev_pas.t_contract_master  tcm,
               dev_pas.t_contract_agent   tca
         WHERE a.accept_id = b.accept_id
           and b.policy_chg_id = c.policy_chg_id
           and d.customer_id = e.customer_id
           and c.customer_id = f.customer_id
           and c.policy_chg_id = d.policy_chg_id
           and d.old_new = '1'
           and c.old_new = '0'
           and e.old_new = '1'
           and f.old_new = '0'
           and e.email <> f.email
           and e.email is not null
           and e.accept_id = f.accept_id
           and e.accept_id = a.accept_id
           and b.policy_code = tcm.policy_code
           and tcm.service_bank_branch like '01%'
           and tcm.bank_agency_flag = '1'
           and tca.channel_type = '03'
           and tca.policy_code = b.policy_code
           and tca.is_nb_agent = '1'
           and a.accept_code = #{accept_code})  	
		  ]]>
		
	</select>
<!-- 查询多主多附标识入参 -->
<sql id="JRQD_MultiMainriskFlagRC">
	<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
	<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
</sql>
<!-- 查询单条数据-保单主表(多主多附标识) -->
	<select id="JRQD_queryMultiMainriskFlagD" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select 	null as multi_mainrisk_flag,a.policy_id,a.policy_code,a.relation_policy_code,a.insured_family,a.apply_code,a.decision_code,
       				a.apply_date,a.policy_type,a.branch_code,a.organ_code,a.validate_date,a.initial_prem_date,
       				a.liability_state,a.lapse_cause,a.lapse_date,a.rerinstate_date,a.suspend_cause,a.suspend_date,
       				a.end_cause,a.expiry_date,a.money_code,a.submission_date,a.submit_channel,a.channel_type,
       				a.sale_com_code,a.lang_code,a.service_bank,a.service_bank_branch,a.service_handler,
       				a.service_handler_code,a.sale_agent_code,a.sale_agent_name,a.winning_start_flag,a.agent_org_id,
       				a.media_type,a.e_service_flag,a.issue_date,a.agency_code,a.policy_pwd,a.pwd_invalid_flag,
       				a.initial_validate_date,a.derivation,a.former_id,a.basic_remark,a.dc_indi,a.interest_mode,
       				a.input_date,a.input_type,a.subinput_type,a.statistic_channel,a.sale_type,a.special_cus_flag,
       				a.bank_agency_flag,a.Is_Self_Insured,a.insert_by,a.insert_time,a.insert_timestamp,a.update_by,
       				a.update_time,a.update_timestamp,a.policy_manage_organ,a.emergency_contacts_name,a.emergency_contacts_mobile,
       				a.emer_con_relation_to_ph,a.idcard_ocr_flag,a.bankcard_ocr_flag,a.double_mainrisk_flag,
       				null as tax_extension_source,null as policy_prd_flag,a.group_sale_type,a.apply_time,a.is_alone_insure,
       				a.lapse_loan_suspend_date,a.medical_insurance_card,a.call_time_list,a.policy_reinsure_flag
  				from dev_pas.t_contract_master a
 			   where 1 = 1
   				 and rownum = 1 ]]>
   		<include refid="JRQD_MultiMainriskFlagRC" />
	</select>
<!-- 查询单条数据-CS保单主表(多主多附标识) -->
	<select id="JRQD_queryMultiMainriskFlagDCS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select 	null as multi_mainrisk_flag,a.policy_id,a.policy_code,a.relation_policy_code,a.insured_family,a.apply_code,a.decision_code,
       				a.apply_date,a.policy_type,a.branch_code,a.organ_code,a.validate_date,a.initial_prem_date,
       				a.liability_state,a.lapse_cause,a.lapse_date,a.rerinstate_date,a.suspend_cause,a.suspend_date,
       				a.end_cause,a.expiry_date,a.money_code,a.submission_date,a.submit_channel,a.channel_type,
       				a.sale_com_code,a.lang_code,a.service_bank,a.service_bank_branch,a.service_handler,
       				a.service_handler_code,a.sale_agent_code,a.sale_agent_name,a.winning_start_flag,a.agent_org_id,
       				a.media_type,a.e_service_flag,a.issue_date,a.agency_code,a.policy_pwd,a.pwd_invalid_flag,
       				a.initial_validate_date,a.derivation,a.former_id,a.basic_remark,a.dc_indi,a.interest_mode,
       				a.input_date,a.input_type,a.subinput_type,a.statistic_channel,a.sale_type,a.special_cus_flag,
       				a.bank_agency_flag,a.Is_Self_Insured,a.insert_by,a.insert_time,a.insert_timestamp,a.update_by,
       				a.update_time,a.update_timestamp,a.policy_manage_organ,a.emergency_contacts_name,a.emergency_contacts_mobile,
       				a.emer_con_relation_to_ph,a.idcard_ocr_flag,a.bankcard_ocr_flag,a.double_mainrisk_flag,
       				null as tax_extension_source,null as policy_prd_flag,a.group_sale_type,a.apply_time,a.is_alone_insure,
       				a.lapse_loan_suspend_date,a.medical_insurance_card,a.call_time_list,a.policy_reinsure_flag
  				from dev_pas.t_cs_contract_master a
 			   where 1 = 1
   				 and rownum = 1
   				 and a.old_new = '1' ]]>
   		<include refid="JRQD_MultiMainriskFlagRC" />
	</select>
</mapper>
