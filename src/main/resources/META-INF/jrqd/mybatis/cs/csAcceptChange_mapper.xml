<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.object.dao.ICsAcceptChangeDao">

    <sql id="JRQD_csAcceptChangeWhereCondition">
        <if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
        <if test=" cancel_time  != null  and  cancel_time  != ''  ">
            <![CDATA[ AND A.CANCEL_TIME = #{cancel_time} ]]></if>
        <if test=" cancel_cause != null and cancel_cause != ''  ">
            <![CDATA[ AND A.CANCEL_CAUSE = #{cancel_cause} ]]></if>
        <if test=" rollback_cause != null and rollback_cause != ''  ">
            <![CDATA[ AND <PERSON><PERSON>K_CAUSE = #{rollback_cause} ]]></if>
        <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
        <if test=" review_time  != null  and  review_time  != ''  ">
            <![CDATA[ AND A.REVIEW_TIME = #{review_time} ]]></if>
        <if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
        <if test=" rollback_note != null and rollback_note != ''  ">
            <![CDATA[ AND A.ROLLBACK_NOTE = #{rollback_note} ]]></if>
        <if test=" review_property != null and review_property != ''  ">
            <![CDATA[ AND A.REVIEW_PROPERTY = #{review_property} ]]></if>
        <if test=" review_view != null and review_view != ''  "><![CDATA[ AND A.REVIEW_VIEW = #{review_view} ]]></if>
        <if test=" cancel_note != null and cancel_note != ''  "><![CDATA[ AND A.CANCEL_NOTE = #{cancel_note} ]]></if>
        <if test=" urgent_detail != null and urgent_detail != ''  ">
            <![CDATA[ AND A.URGENT_DETAIL = #{urgent_detail} ]]></if>
        <if test=" uncon_type != null and uncon_type != ''  "><![CDATA[ AND A.UNCON_TYPE = #{uncon_type} ]]></if>
        <if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
        <if test=" cs_accept_reason != null and cs_accept_reason != ''  ">
            <![CDATA[ AND A.CS_ACCEPT_REASON = #{cs_accept_reason} ]]></if>
        <if test=" cancel_id  != null "><![CDATA[ AND A.CANCEL_ID = #{cancel_id} ]]></if>
        <if test=" rollback_app_id  != null "><![CDATA[ AND A.ROLLBACK_APP_ID = #{rollback_app_id} ]]></if>
        <if test=" uncon_cause != null and uncon_cause != ''  "><![CDATA[ AND A.UNCON_CAUSE = #{uncon_cause} ]]></if>
        <if test=" rollback_app_time  != null  and  rollback_app_time  != ''  ">
            <![CDATA[ AND A.ROLLBACK_APP_TIME = #{rollback_app_time} ]]></if>
        <if test=" change_flag != null and change_flag != ''  "><![CDATA[ AND A.CHANGE_FLAG = #{change_flag} ]]></if>
        <if test=" finish_time  != null  and  finish_time  != ''  ">
            <![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
        <if test=" hesitate_flag  != null "><![CDATA[ AND A.HESITATE_FLAG = #{hesitate_flag} ]]></if>
        <if test=" rollback_review_view != null and rollback_review_view != ''  ">
            <![CDATA[ AND A.ROLLBACK_REVIEW_VIEW = #{rollback_review_view} ]]></if>
        <if test=" insert_operator_id  != null "><![CDATA[ AND A.INSERT_OPERATOR_ID = #{insert_operator_id} ]]></if>
        <if test=" review_id  != null "><![CDATA[ AND A.REVIEW_ID = #{review_id} ]]></if>
        <if test=" validate_time  != null  and  validate_time  != ''  ">
            <![CDATA[ AND A.VALIDATE_TIME = #{validate_time} ]]></if>
        <if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
        <if test=" pre_flag  != null "><![CDATA[ AND A.PRE_FLAG = #{pre_flag} ]]></if>
        <if test=" service_code != null and service_code != ''  ">
            <![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
        <if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
        <if test=" conven_flag  != null "><![CDATA[ AND A.CONVEN_FLAG = #{conven_flag} ]]></if>
        <if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
        <if test=" accept_time  != null  and  accept_time  != ''  ">
            <![CDATA[ AND A.ACCEPT_TIME = #{accept_time} ]]></if>
        <if test=" pre_validate_date  != null  and  pre_validate_date  != ''  ">
            <![CDATA[ AND A.PRE_VALIDATE_DATE = #{pre_validate_date} ]]></if>
        <if test=" accept_status != null and accept_status != ''  ">
            <![CDATA[ AND A.ACCEPT_STATUS = #{accept_status} ]]></if>
        <if test=" back_entyr_cause != null and back_entyr_cause != ''  ">
            <![CDATA[ AND A.BACK_ENTYR_CAUSE = #{back_entyr_cause} ]]></if>
        <if test=" urgent_cause != null and urgent_cause != ''  ">
            <![CDATA[ AND A.URGENT_CAUSE = #{urgent_cause} ]]></if>
        <if test=" delay_cause != null and delay_cause != ''  "><![CDATA[ AND A.DELAY_CAUSE = #{delay_cause} ]]></if>
        <if test=" urgent_flag  != null "><![CDATA[ AND A.URGENT_FLAG = #{urgent_flag} ]]></if>
        <if test=" rollback_review_result != null and rollback_review_result != ''  ">
            <![CDATA[ AND A.ROLLBACK_REVIEW_RESULT = #{rollback_review_result} ]]></if>
        <if test=" review_accept_rate != null "><![CDATA[ AND A.REVIEW_ACCEPT_RATE = #{review_accept_rate} ]]></if>
        <if test=" review_input_rate  != null "><![CDATA[ AND A.REVIEW_INPUT_RATE = #{review_input_rate} ]]></if>
        <if test=" review_rate != null "><![CDATA[ AND A.REVIEW_RATE = #{review_rate} ]]></if>
        <if test=" bill_send_type != null "><![CDATA[ AND A.BILL_SEND_TYPE = #{bill_send_type} ]]></if>
        <if test=" is_renew_payment != null and is_renew_payment != ''  ">
            <![CDATA[ AND A.is_renew_payment = #{is_renew_payment} ]]></if>
        <if test=" is_repay_loan != null and is_repay_loan != ''  ">
            <![CDATA[ AND A.is_repay_loan = #{is_repay_loan} ]]></if>
        <if test=" is_cus_base_chg != null and is_cus_base_chg != ''  ">
            <![CDATA[ AND A.IS_CUS_BASE_CHG = #{is_cus_base_chg} ]]></if>

        <if test=" call_phone1 != null and call_phone1 != ''  "><![CDATA[ AND A.CALL_PHONE1 = #{call_phone1} ]]></if>
        <if test=" call_phone2 != null and call_phone2 != ''  "><![CDATA[ AND A.CALL_PHONE2 = #{call_phone2} ]]></if>
        <if test=" not_online_policy != null and not_online_policy != ''  ">
            <![CDATA[ AND A.NOT_ONLINE_POLICY = #{not_online_policy} ]]></if>
        <if test=" abnormal_pay_flag  != null "><![CDATA[ AND A.ABNORMAL_PAY_FLAG = #{abnormal_pay_flag} ]]></if>
        <if test=" tracbackflag  != null "><![CDATA[ AND A.TRACBACKFLAG = #{tracbackflag} ]]></if>
    </sql>
    <!-- 查询所有操作(紧急件) -->
    <sql id="JRQD_csAcceptChangeWhereConditionForUrgency">
        <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
        <if test=" begin1  != null  and  begin1  != ''  "><![CDATA[ AND A.ACCEPT_TIME >= #{begin1} ]]></if>
        <if test=" begin2  != null  and  begin2  != ''  "><![CDATA[ AND A.ACCEPT_TIME >= #{begin2} ]]></if>
        <if test=" end1  != null  and  end1  != ''  "><![CDATA[ AND A.ACCEPT_TIME <= #{end1} ]]></if>
        <if test=" end2  != null  and  end2  != ''  "><![CDATA[ AND A.ACCEPT_TIME <= #{end2} ]]></if>
        <if test=" review_property  != null  and  review_property  != ''  ">
            <![CDATA[ AND A.REVIEW_PROPERTY = #{review_property} ]]></if>


    </sql>

    <sql id="JRQD_PA_serviceCodeListCondition">
        <if test=" service_code_list  != null  and service_code_list.size() !=0 ">
            <![CDATA[ AND CAC.SERVICE_CODE IN ]]>
            <foreach collection="service_code_list" item="item" index="index" open="(" close=")" separator=",">
                <![CDATA[ #{item} ]]>
            </foreach>
        </if>
    </sql>
    <sql id="JRQD_PA_policyCodeListCondition">
        <if test=" policy_code_list  != null  and policy_code_list.size() !=0 ">
            <![CDATA[ AND CPC.POLICY_CODE IN ]]>
            <foreach collection="policy_code_list" item="item" index="index" open="(" close=")" separator=",">
                <![CDATA[ #{item} ]]>
            </foreach>
        </if>
    </sql>
    <!-- 查询受理查询条件 -->
    <sql id="JRQD_csAcceptCodenewQuery">

        <if test=" apply_time != null and apply_time != ''  "><![CDATA[ AND CA.APPLY_TIME = #{apply_time} ]]></if>
    </sql>

    <sql id="JRQD_findTotalWhereCondition">
        <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like #{organ_code} ]]></if>
        <if test=" review_result != null and review_result == '1'  ">
            <![CDATA[
		and a.accept_status in ('13','18') 		
		]]>
        </if>
        <if test=" review_result != null and review_result == '2'  ">
            <![CDATA[
		and a.accept_status in ('21') 
		]]>
        </if>
        <if test=" review_result != null and review_result == '3'  ">
            <![CDATA[
		and a.accept_status in ('14') 
		]]>
        </if>
    </sql>
    <!-- 按索引生成的查询条件 -->
    <sql id="JRQD_queryCsAcceptChangeByAcceptIdCondition">
        <if test=" accept_id  != null and accept_id != '' "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
        <if test=" change_id  != null and change_id !='' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
    </sql>

    <!-- 单条数据的查询条件 -->
    <sql id="JRQD_queryCsAcceptChangeBySingle">
        <if test=" accept_code  != null "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
        <if test=" accept_id  != null and accept_id !='' "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
        <if test=" change_id  != null and change_id != '' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
        <if test=" service_code_list  != null and service_code_list != '' ">
            <![CDATA[ AND A.SERVICE_CODE IN (${service_code_list}) ]]></if>
        <if test=" accept_status != null and accept_status != ''  ">
            <![CDATA[ AND A.ACCEPT_STATUS = #{accept_status} ]]></if>
    </sql>

    <sql id="JRQD_queryCsAcceptChangeByChangeId">
        <if test=" change_id  != null and change_id != '' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
    </sql>

    <!-- add by zkm -->
    <sql id="JRQD_csEndorseManualCancelTrail">
        <if test="policy_code != null and policy_code != ''"><![CDATA[ and pc.policy_code = #{policy_code} ]]></if>
        <if test="apply_code != null and apply_code != ''"><![CDATA[ and a.apply_code = #{apply_code} ]]></if>
        <if test="accept_code != null and accept_code != ''"><![CDATA[ and ac.accept_code = #{accept_code} ]]></if>
        <if test="user_name != null and user_name != ''"><![CDATA[ and uu.user_name = #{user_name} ]]></if>
        <if test="certi_type != null and certi_type != ''"><![CDATA[ and uu.id_type = #{certi_type} ]]></if>
        <if test="certi_code != null and certi_code != ''"><![CDATA[ and uu.id_card = #{certi_code} ]]></if>
        <if test="insert_time != null and insert_time != ''">
            <![CDATA[and ac.cancel_time between to_date(#{insert_time} || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') and to_date(#{insert_time} || ' 23:59:59', 'yyyy-mm-dd hh24:mi:ss')]]></if>
    </sql>

    <!-- 添加操作 -->
    <insert id="JRQD_addCsAcceptChange" useGeneratedKeys="false" parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="accept_id">
            SELECT APP___PAS__DBUSER.S_CS_ACCEPT_CHANGE.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE( 
			INSERT_OPERATOR_ID,	RELATED_ID, UNCON_CAUSE, CHANGE_FLAG, CANCEL_TIME, ACCEPT_TIME, HESITATE_FLAG, 
				FINISH_TIME, CANCEL_CAUSE, REVIEW_ID, INSERT_TIMESTAMP, ORGAN_CODE, REVIEW_TIME, VALIDATE_TIME, 
				UPDATE_BY,CHANGE_ID, PRE_FLAG, FEE_AMOUNT, SERVICE_CODE, ACCEPT_ID, 
				INSERT_TIME, REVIEW_PROPERTY, UPDATE_TIME, CONVEN_FLAG, CANCEL_NOTE, REVIEW_VIEW, ACCEPT_CODE, 
				REVIEW_RESULT, URGENT_DETAIL, PRE_VALIDATE_DATE, ORDER_ID, UNCON_TYPE, ACCEPT_STATUS, 
				CANCEL_ID, BACK_ENTYR_CAUSE, UPDATE_TIMESTAMP, URGENT_CAUSE, INSERT_BY, URGENT_FLAG, DELAY_CAUSE, 
				REVIEW_ACCEPT_RATE, REVIEW_INPUT_RATE, REVIEW_RATE,BILL_SEND_TYPE,BALANCE_FLAG,IS_RENEW_PAYMENT,
				IS_REPAY_LOAN,IS_ELEC_SIGN,LIAB_VALIDATE_TIME,
				SIGN_TYPE,FACE_FLAG,SKY_SIGN,IDENTITY_FLAG,IS_IDENTITY_CHECK,ELEC_SIGN_FLAG,MAN_CHECK_INFO,LOBBY_MANAGER_RESULT,LOBBY_MANAGER_NAME
				,FACE_RECOGNITION_RESULT,AUTHENTICATION_RESULT,ABNORMAL_PAY_FLAG,POLICY_LOST_FLAG,POLICY_REISSUE_FLAG) 
			VALUES ( #{insert_operator_id, jdbcType=NUMERIC}, 
				#{related_id, jdbcType=NUMERIC}, #{uncon_cause, jdbcType=VARCHAR} , ]]>
        <if test=" change_flag != null and change_flag != ''  "><![CDATA[#{change_flag, jdbcType=VARCHAR} ,]]></if>
        <if test=" change_flag = null or change_flag = ''  "><![CDATA[ default,]]></if>
        <![CDATA[  #{cancel_time, jdbcType=DATE} , #{accept_time, jdbcType=DATE} , #{hesitate_flag, jdbcType=NUMERIC}
				, #{finish_time, jdbcType=DATE} , #{cancel_cause, jdbcType=VARCHAR} , #{review_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{review_time, jdbcType=DATE} , #{validate_time, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC}  ,#{change_id, jdbcType=NUMERIC} , #{pre_flag, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{accept_id, jdbcType=NUMERIC} 
				, SYSDATE , #{review_property, jdbcType=VARCHAR} , SYSDATE , #{conven_flag, jdbcType=NUMERIC} , #{cancel_note, jdbcType=VARCHAR} , #{review_view, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} 
				, #{review_result, jdbcType=VARCHAR} , #{urgent_detail, jdbcType=VARCHAR} , #{pre_validate_date, jdbcType=DATE} , #{order_id, jdbcType=NUMERIC} , #{uncon_type, jdbcType=VARCHAR} , #{accept_status, jdbcType=VARCHAR} 
				, #{cancel_id, jdbcType=NUMERIC} , #{back_entyr_cause, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{urgent_cause, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{urgent_flag, jdbcType=NUMERIC} , #{delay_cause, jdbcType=VARCHAR} 
				, #{review_accept_rate, jdbcType=NUMERIC}, #{review_input_rate, jdbcType=NUMERIC}, #{review_rate, jdbcType=NUMERIC},#{bill_send_type, jdbcType=VARCHAR},#{balance_flag, jdbcType=NUMERIC},#{is_renew_payment,jdbcType=NUMERIC}
				,#{is_repay_loan,jdbcType=NUMERIC},#{is_elec_sign,jdbcType=NUMERIC}, #{liab_validate_time, jdbcType=DATE}
				,#{sign_type, jdbcType=VARCHAR},#{face_flag, jdbcType=VARCHAR},#{sky_sign, jdbcType=VARCHAR},#{identity_flag, jdbcType=VARCHAR},#{is_identity_check, jdbcType=VARCHAR}, 
				#{elec_sign_flag,jdbcType=VARCHAR},#{man_check_info,jdbcType=VARCHAR},#{lobby_manager_result,jdbcType=VARCHAR},#{lobby_manager_name,jdbcType=VARCHAR}
				,#{face_recognition_result,jdbcType=VARCHAR},#{authentication_result,jdbcType=VARCHAR},#{abnormal_pay_flag,jdbcType=NUMERIC},#{policy_lost_flag,jdbcType=NUMERIC},#{policy_reissue_flag,jdbcType=NUMERIC}
				) 
		 ]]>
    </insert>

    <!-- 添加操作 -->
    <insert id="JRQD_addCsCancel" useGeneratedKeys="false" parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
            SELECT APP___PAS__DBUSER.S_CS_CANCEL__LIST_ID.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_CANCEL(
				INSERT_TIME, UPDATE_TIME, CANCEL_NOTE, STATUS, ACCEPT_CODE, CANCEL_CAUSE, APPLY_CODE, 
				INSERT_TIMESTAMP, UPDATE_BY, CANCEL_DATE, CANCEL_ID, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				SYSDATE, SYSDATE , #{cancel_note, jdbcType=VARCHAR} , #{status, jdbcType=NUMERIC} , #{accept_code, jdbcType=VARCHAR} , #{cancel_cause, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{cancel_date, jdbcType=DATE} , #{cancel_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
    </insert>

    <!-- 按索引查询操作 -->
    <select id="JRQD_findCsCancelByListId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.CANCEL_NOTE, A.STATUS, A.ACCEPT_CODE, A.CANCEL_CAUSE, A.APPLY_CODE,
			A.CANCEL_DATE, A.CANCEL_ID, A.LIST_ID FROM APP___PAS__DBUSER.T_CS_CANCEL A WHERE 1 = 1  ]]>
        <if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
        <![CDATA[ ORDER BY A.LIST_ID ]]>
    </select>

    <!-- 修改操作 -->
    <update id="JRQD_updateCsCancel" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_CANCEL ]]>
        <set>
            <trim suffixOverrides=",">
                UPDATE_TIME = SYSDATE ,
                CANCEL_NOTE = #{cancel_note, jdbcType=VARCHAR} ,
                STATUS = #{status, jdbcType=NUMERIC} ,
                CANCEL_CAUSE = #{cancel_cause, jdbcType=VARCHAR} ,
                UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
                CANCEL_DATE = #{cancel_date, jdbcType=DATE} ,
                CANCEL_ID = #{cancel_id, jdbcType=NUMERIC} ,
                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
            </trim>
        </set>
        <![CDATA[ WHERE ACCEPT_CODE = #{accept_code} ]]>
    </update>

    <!-- 修改操作 -->
    <update id="JRQD_updateIsRenewPaymentByAcceptId" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                IS_RENEW_PAYMENT = #{is_renew_payment, jdbcType=NUMERIC} ,
                UPDATE_TIME = SYSDATE ,
                UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
            </trim>
        </set>
        <![CDATA[ WHERE ACCEPT_ID = #{accept_id} ]]>
    </update>

    <!-- 删除操作 -->
    <delete id="JRQD_deleteCsAcceptChange" parameterType="java.util.Map">
        <![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE 1=1 ]]>
        <if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
        <if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
    </delete>
    <!-- 删除操作 -->
    <delete id="JRQD_deleteCsAcceptChangeByChangeId" parameterType="java.util.Map">
		<![CDATA[
        DELETE
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
        WHERE A.CHANGE_ID = #{change_id}
        ]]>
	</delete>

    <!-- 删除操作 -->
    <delete id="JRQD_deleteCsAcceptChangeByAcceptId" parameterType="java.util.Map">
		<![CDATA[
        DELETE
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
        WHERE A.ACCEPT_ID = #{accept_id}
        ]]>
	</delete>
    <!-- 修改操作 -->
    <update id="JRQD_updateCsAcceptChange" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
                CANCEL_TIME = #{cancel_time, jdbcType=DATE} ,
                CANCEL_CAUSE = #{cancel_cause, jdbcType=VARCHAR} ,
                ROLLBACK_CAUSE = #{rollback_cause, jdbcType=VARCHAR} ,
                ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
                <if test="r_time != null ">
                    REVIEW_TIME = to_date(#{r_time, jdbcType=VARCHAR},'YYYY-MM-DD HH24:MI:SS') ,
                </if>
                <if test="review_time != null and r_time == null">
                    REVIEW_TIME = #{review_time,jdbcType=DATE},
                </if>
                UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
                CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
                ROLLBACK_NOTE = #{rollback_note, jdbcType=VARCHAR} ,
                REVIEW_PROPERTY = #{review_property, jdbcType=VARCHAR} ,
                UPDATE_TIME = SYSDATE ,
                REVIEW_VIEW = #{review_view, jdbcType=VARCHAR} ,
                CANCEL_NOTE = #{cancel_note, jdbcType=VARCHAR} ,
                REVIEW_RESULT = #{review_result, jdbcType=VARCHAR} ,
                URGENT_DETAIL = #{urgent_detail, jdbcType=VARCHAR} ,
                UNCON_TYPE = #{uncon_type, jdbcType=VARCHAR} ,
                ORDER_ID = #{order_id, jdbcType=NUMERIC} ,
                CS_ACCEPT_REASON = #{cs_accept_reason, jdbcType=VARCHAR} ,
                CANCEL_ID = #{cancel_id, jdbcType=NUMERIC} ,
                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
                ROLLBACK_APP_ID = #{rollback_app_id, jdbcType=NUMERIC} ,
                UNCON_CAUSE = #{uncon_cause, jdbcType=VARCHAR} ,
                ROLLBACK_APP_TIME = #{rollback_app_time, jdbcType=DATE} ,

                <if test="change_flag != null ">
                    CHANGE_FLAG = #{change_flag, jdbcType=VARCHAR} ,
                </if>

                FINISH_TIME = #{finish_time, jdbcType=DATE} ,
                HESITATE_FLAG = #{hesitate_flag, jdbcType=NUMERIC} ,
                ROLLBACK_REVIEW_VIEW = #{rollback_review_view, jdbcType=VARCHAR} ,
                INSERT_OPERATOR_ID = #{insert_operator_id, jdbcType=NUMERIC} ,
                REVIEW_ID = #{review_id, jdbcType=NUMERIC} ,
                VALIDATE_TIME = #{validate_time, jdbcType=DATE} ,
                FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
                PRE_FLAG = #{pre_flag, jdbcType=NUMERIC} ,
                SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
                CONVEN_FLAG = #{conven_flag, jdbcType=NUMERIC} ,
                ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
                ACCEPT_TIME = #{accept_time, jdbcType=DATE} ,
                PRE_VALIDATE_DATE = #{pre_validate_date, jdbcType=DATE} ,
                ACCEPT_STATUS = #{accept_status, jdbcType=VARCHAR} ,
                BACK_ENTYR_CAUSE = #{back_entyr_cause, jdbcType=VARCHAR} ,
                URGENT_CAUSE = #{urgent_cause, jdbcType=VARCHAR} ,
                DELAY_CAUSE = #{delay_cause, jdbcType=VARCHAR} ,
                URGENT_FLAG = #{urgent_flag, jdbcType=NUMERIC} ,
                ROLLBACK_REVIEW_RESULT = #{rollback_review_result, jdbcType=VARCHAR} ,
                REVIEW_ACCEPT_RATE = #{review_accept_rate, jdbcType=NUMERIC} ,
                REVIEW_INPUT_RATE = #{review_input_rate, jdbcType=NUMERIC} ,
                REVIEW_RATE = #{review_rate, jdbcType=NUMERIC} ,
                IS_SIGN_FLAG = #{is_sign_flag, jdbcType=NUMERIC} ,
                SIGN_NO_LIST = #{sign_no_list, jdbcType=VARCHAR} ,
                IS_SAVED = #{is_saved, jdbcType=NUMERIC} ,
                IS_ADD_DELAY = #{is_add_delay, jdbcType=NUMERIC},
                IS_CUS_BASE_CHG = #{is_cus_base_chg, jdbcType=NUMERIC},
                CALL_PHONE1 = #{call_phone1, jdbcType=VARCHAR} ,
                CALL_PHONE2 = #{call_phone2, jdbcType=VARCHAR} ,
                NOT_ONLINE_POLICY = #{not_online_policy, jdbcType=VARCHAR} ,
                TRACBACKFLAG = #{tracbackflag, jdbcType=NUMERIC} ,
                REVIEW_END_CAUSE = #{review_end_cause, jdbcType=VARCHAR} ,
                APP_TYPE = #{app_type, jdbcType=VARCHAR} ,
                <if test="bill_send_type != null">
                    BILL_SEND_TYPE = #{bill_send_type, jdbcType=VARCHAR},
                </if>
                <if test="gxsm_read_time != null">
                    GXSM_READ_TIME = #{gxsm_read_time,jdbcType=TIMESTAMP},
                </if>
                <if test="liab_validate_time != null">
                    LIAB_VALIDATE_TIME = #{liab_validate_time, jdbcType=DATE},
                </if>
                <if test="is_repay_loan != null">
                    IS_REPAY_LOAN = #{is_repay_loan,jdbcType=NUMERIC},
                </if>
                <if test="is_renew_payment != null and is_renew_payment != ''">
                    IS_RENEW_PAYMENT = #{is_renew_payment,jdbcType=NUMERIC},
                </if>
                <if test="cust_id_upper_flag != null and cust_id_upper_flag != ''">
                    CUST_ID_UPPER_FLAG = #{cust_id_upper_flag,jdbcType=NUMERIC},
                </if>
                <!--仅在移动保全2.0使用-->
                <if test="sign_type != null and sign_type != ''">
                    SIGN_TYPE = #{sign_type,jdbcType=NUMERIC},
                </if>
                <if test="face_flag != null and face_flag != ''">
                    FACE_FLAG = #{face_flag,jdbcType=NUMERIC},
                </if>
                <if test="sky_sign != null and sky_sign != ''">
                    SKY_SIGN = #{sky_sign,jdbcType=NUMERIC},
                </if>
                <if test="identity_flag != null and identity_flag != ''">
                    IDENTITY_FLAG = #{identity_flag,jdbcType=NUMERIC},
                </if>
                <if test="is_identity_check != null and is_identity_check != ''">
                    IS_IDENTITY_CHECK = #{is_identity_check,jdbcType=NUMERIC},
                </if>
                <!--仅在移动保全2.0使用-->
                <!--仅远程柜面使用 -->
                <if test="elec_sign_flag != null and elec_sign_flag != ''">
                    ELEC_SIGN_FLAG = #{elec_sign_flag,jdbcType=VARCHAR},
                </if>
                <if test="man_check_info != null and man_check_info != ''">
                    MAN_CHECK_INFO = #{man_check_info,jdbcType=VARCHAR},
                </if>
                <!--柜面自助使用  -->
                <if test="lobby_manager_result != null and lobby_manager_result != ''">
                    LOBBY_MANAGER_RESULT = #{lobby_manager_result,jdbcType=VARCHAR},
                </if>
                <if test="lobby_manager_name != null and lobby_manager_name != ''">
                    LOBBY_MANAGER_NAME = #{lobby_manager_name,jdbcType=VARCHAR},
                </if>
                <!--柜面自助使用 人脸识别结果 -->
                <if test="face_recognition_result != null and face_recognition_result != ''">
                    FACE_RECOGNITION_RESULT = #{face_recognition_result,jdbcType=VARCHAR},
                </if>
                <!--柜面自助使用 身份验真结果 -->
                <if test="authentication_result != null and authentication_result != ''">
                    AUTHENTICATION_RESULT = #{authentication_result,jdbcType=VARCHAR},
                </if>
                <!-- 受理业务员代码 -->
                <if test=" accept_agent_code != null and accept_agent_code != ''  ">
                    ACCEPT_AGENT_CODE = #{accept_agent_code, jdbcType=VARCHAR},
                </if>
            </trim>
        </set>
        <![CDATA[ WHERE ACCEPT_ID = #{accept_id} ]]>
    </update>

    <!--仅远程柜面使用 -->
    <update id="JRQD_updateCsAcceptChangeElecSignConfFlag" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                ELEC_SIGN_CONF_FLAG = #{elec_sign_conf_flag,jdbcType=VARCHAR},
            </trim>
        </set>
        <![CDATA[ WHERE accept_id = #{accept_id} ]]>
    </update>

    <!--仅掌上新华保全标识回传接口使用 -->
    <update id="JRQD_updateCsAcceptChangeZSXHFlagReturn" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                YCFX = #{ycfx,jdbcType=VARCHAR},
                BANK_CARD_FLAG = #{bank_card_flag,jdbcType=VARCHAR},
                GZ_READ_TIME = #{gz_read_time,jdbcType=TIMESTAMP},
                SQ_READ_TIME = #{sq_read_time,jdbcType=TIMESTAMP},
                GXSM_READ_TIME = #{gxsm_read_time,jdbcType=TIMESTAMP},
                FACE_CONFIRM_FLAG = #{face_confirm_flag,jdbcType=VARCHAR},
            </trim>
        </set>
        <![CDATA[ WHERE accept_id = #{accept_id} ]]>
    </update>

    <update id="JRQD_updateCsAcceptChangeIsSaved" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                IS_SAVED = #{is_saved, jdbcType=NUMERIC} ,
            </trim>
        </set>
        <![CDATA[ WHERE accept_id = #{accept_id} ]]>
    </update>
    <!-- 更新复核属性 -->
    <update id="JRQD_updateReviewProperty" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                REVIEW_PROPERTY = #{review_property, jdbcType=VARCHAR} ,
            </trim>
        </set>
        <![CDATA[ WHERE accept_id = #{accept_id} ]]>
    </update>
    <!-- 修改操作 -->
    <update id="JRQD_updateCsAcceptChangeOne" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
                CANCEL_TIME = #{cancel_time, jdbcType=DATE} ,
                CANCEL_CAUSE = #{cancel_cause, jdbcType=VARCHAR} ,
                ROLLBACK_CAUSE = #{rollback_cause, jdbcType=VARCHAR} ,
                ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
                <if test="r_time != null">
                    REVIEW_TIME = to_date(#{r_time, jdbcType=VARCHAR},'YYYY-MM-DD HH24:MI:SS') ,
                </if>
                <if test="review_time == null">
                    REVIEW_TIME = #{review_time,jdbcType=DATE},
                </if>
                UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
                CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
                ROLLBACK_NOTE = #{rollback_note, jdbcType=VARCHAR} ,
                REVIEW_PROPERTY = #{review_property, jdbcType=VARCHAR} ,
                UPDATE_TIME = SYSDATE ,
                REVIEW_VIEW = #{review_view, jdbcType=VARCHAR} ,
                CANCEL_NOTE = #{cancel_note, jdbcType=VARCHAR} ,
                REVIEW_RESULT = #{review_result, jdbcType=VARCHAR} ,
                URGENT_DETAIL = #{urgent_detail, jdbcType=VARCHAR} ,
                UNCON_TYPE = #{uncon_type, jdbcType=VARCHAR} ,
                ORDER_ID = #{order_id, jdbcType=NUMERIC} ,
                CS_ACCEPT_REASON = #{cs_accept_reason, jdbcType=VARCHAR} ,
                CANCEL_ID = #{cancel_id, jdbcType=NUMERIC} ,
                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
                ROLLBACK_APP_ID = #{rollback_app_id, jdbcType=NUMERIC} ,
                UNCON_CAUSE = #{uncon_cause, jdbcType=VARCHAR} ,
                ROLLBACK_APP_TIME = #{rollback_app_time, jdbcType=DATE} ,
                CHANGE_FLAG = #{change_flag, jdbcType=VARCHAR} ,
                FINISH_TIME = #{finish_time, jdbcType=DATE} ,
                HESITATE_FLAG = #{hesitate_flag, jdbcType=NUMERIC} ,
                ROLLBACK_REVIEW_VIEW = #{rollback_review_view, jdbcType=VARCHAR} ,
                INSERT_OPERATOR_ID = #{insert_operator_id, jdbcType=NUMERIC} ,
                REVIEW_ID = #{review_id, jdbcType=NUMERIC} ,
                VALIDATE_TIME = #{validate_time, jdbcType=DATE} ,
                FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
                PRE_FLAG = #{pre_flag, jdbcType=NUMERIC} ,
                SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
                CONVEN_FLAG = #{conven_flag, jdbcType=NUMERIC} ,
                ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
                ACCEPT_TIME = #{accept_time, jdbcType=DATE} ,
                PRE_VALIDATE_DATE = #{pre_validate_date, jdbcType=DATE} ,
                ACCEPT_STATUS = #{accept_status, jdbcType=VARCHAR} ,
                BACK_ENTYR_CAUSE = #{back_entyr_cause, jdbcType=VARCHAR} ,
                URGENT_CAUSE = #{urgent_cause, jdbcType=VARCHAR} ,
                DELAY_CAUSE = #{delay_cause, jdbcType=VARCHAR} ,
                URGENT_FLAG = #{urgent_flag, jdbcType=NUMERIC} ,
                ROLLBACK_REVIEW_RESULT = #{rollback_review_result, jdbcType=VARCHAR} ,
                REVIEW_ACCEPT_RATE = #{review_accept_rate, jdbcType=NUMERIC} ,
                REVIEW_INPUT_RATE = #{review_input_rate, jdbcType=NUMERIC} ,
                REVIEW_RATE = #{review_rate, jdbcType=NUMERIC} ,
                CALL_PHONE1 = #{call_phone1, jdbcType=VARCHAR} ,
                CALL_PHONE2 = #{call_phone2, jdbcType=VARCHAR} ,
                POLICY_LOST_FLAG = #{policy_lost_flag, jdbcType=NUMERIC} ,
                POLICY_REISSUE_FLAG = #{policy_reissue_flag, jdbcType=NUMERIC} ,
                <if test="is_sign_flag != null">
                    IS_SIGN_FLAG = #{is_sign_flag, jdbcType=NUMERIC} ,
                </if>
                SIGN_NO_LIST = #{sign_no_list, jdbcType=VARCHAR} ,
                IS_SAVED = #{is_saved, jdbcType=NUMERIC} ,
                bALANCE_FLAG = #{balance_flag, jdbcType=NUMERIC} ,
                IS_ADD_DELAY = #{is_add_delay, jdbcType=NUMERIC},
                <if test="bill_send_type != null">
                    BILL_SEND_TYPE = #{bill_send_type, jdbcType=VARCHAR},
                </if>
                <if test="is_renew_payment != null and is_renew_payment != ''">
                    IS_RENEW_PAYMENT = #{is_renew_payment,jdbcType=NUMERIC}
                </if>
                <if test="lobby_manager_result != null and lobby_manager_result != ''">
                    LOBBY_MANAGER_RESULT = #{lobby_manager_result,jdbcType=VARCHAR},
                </if>
                <if test="lobby_manager_name != null and lobby_manager_name != ''">
                    LOBBY_MANAGER_NAME = #{lobby_manager_name,jdbcType=VARCHAR},
                </if>
                <!--柜面自助使用 人脸识别结果 -->
                <if test="face_recognition_result != null and face_recognition_result != ''">
                    FACE_RECOGNITION_RESULT = #{face_recognition_result,jdbcType=VARCHAR},
                </if>
                <!--柜面自助使用 身份验真结果 -->
                <if test="authentication_result != null and authentication_result != ''">
                    AUTHENTICATION_RESULT = #{authentication_result,jdbcType=VARCHAR},
                </if>

            </trim>
        </set>
        <![CDATA[ WHERE ACCEPT_ID = #{accept_id} ]]>
    </update>

    <!-- 按索引查询操作 -->
    <select id="JRQD_findCsAcceptChangeByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,A.RELATED_ID,A.UNCON_CAUSE,A.CHANGE_FLAG,A.CANCEL_TIME,A.ACCEPT_TIME,
						A.HESITATE_FLAG,A.FINISH_TIME,A.CANCEL_CAUSE,A.REVIEW_ID,A.ORGAN_CODE,A.REVIEW_TIME,A.VALIDATE_TIME,A.CHANGE_ID,A.PRE_FLAG,A.FEE_AMOUNT,A.SERVICE_CODE,
						A.ACCEPT_ID,A.INSERT_OPERATOR_ID,A.REVIEW_PROPERTY,A.CONVEN_FLAG,A.CANCEL_NOTE,A.REVIEW_VIEW,A.ACCEPT_CODE,A.IS_SAVED,A.REVIEW_RESULT,A.URGENT_DETAIL,
						A.PRE_VALIDATE_DATE,A.ORDER_ID,A.UNCON_TYPE,A.ACCEPT_STATUS,A.CANCEL_ID,A.BACK_ENTYR_CAUSE,A.URGENT_CAUSE,A.URGENT_FLAG,A.DELAY_CAUSE,A.INSERT_BY,A.CALL_PHONE1,A.CALL_PHONE2,
						A.INSERT_TIME,A.REVIEW_ACCEPT_RATE,A.REVIEW_INPUT_RATE,A.REVIEW_RATE,A.IS_SIGN_FLAG,A.SIGN_NO_LIST,A.BILL_SEND_TYPE,B.APPLY_TIME,B.SERVICE_TYPE,
						B.APPLY_NAME,C.REISSUE_CAUSE,A.BALANCE_FLAG,A.CUST_ID_UPPER_FLAG,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0') FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0') IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
						A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG
						  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
						 INNER JOIN APP___PAS__DBUSER.T_CS_APPLICATION B
						    ON A.CHANGE_ID = B.CHANGE_ID
						  LEFT JOIN APP___PAS__DBUSER.T_POLICY_REISSUE C
						    ON A.ACCEPT_CODE = C.ACCEPT_CODE
						 WHERE 1 = 1 
		]]>
        <include refid="JRQD_queryCsAcceptChangeByAcceptIdCondition"/>
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>
    <!-- 按索引查询操作 -->
    <select id="JRQD_findcsUrgenTFlags" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.ACCEPT_CODE,A.URGENT_FLAG FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A  WHERE ROWNUM <=  1000 ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
    </select>

    <!-- 按索引查询操作 -->
    <!--  <select id="JRQD_findCsAcceptChangeByAcceptId2" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT T.INFORM_TYPE,A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG,
            A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME,
            A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,
            A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE,
            A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS ,
            A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME FROM T_CS_ACCEPT_CHANGE A,T_POLICY_REISSUE T  WHERE 1 = 1 AND T.CHANGE_ID=A.CHANGE_ID]]>
        <include refid="JRQD_queryCsAcceptChangeByAcceptIdCondition" />
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>
-->
    <!-- 按map查询操作 -->
    <select id="JRQD_findAllMapCsAcceptChange" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE,A.BILL_SEND_TYPE
			,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
        <!-- <include refid="JRQD_请添加查询条件" /> -->
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>

    <!-- 查询所有操作 -->
    <select id="JRQD_findAllCsAcceptChange" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_ELEC_SIGN,A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, A.IS_SAVED,A.IS_RENEW_PAYMENT,
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , A.BALANCE_FLAG,
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME,
			A.IS_SIGN_FLAG,A.SIGN_NO_LIST,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE, A.BILL_SEND_TYPE, A.IS_CUS_BASE_CHG, 
			A.LIAB_VALIDATE_TIME,CALL_PHONE1,CALL_PHONE2,
			0 IS_VERIFY_TRUTH,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK IS_IDENTITY_CHECK,
			A.ELEC_SIGN_FLAG,A.MAN_CHECK_INFO,A.ELEC_SIGN_CONF_FLAG,A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,
			A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG,
			A.YCFX,A.BANK_CARD_FLAG,A.GZ_READ_TIME,A.SQ_READ_TIME,GXSM_READ_TIME,A.FACE_CONFIRM_FLAG,null as TRACBACKFLAG
			 FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>

    <!-- 查询所有操作 按生效时间降序排列 -->
    <select id="JRQD_findAllCsAcceptChangeOrdDesc" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, A.IS_SAVED,A.IS_RENEW_PAYMENT,
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , A.BALANCE_FLAG,
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME,
			A.IS_SIGN_FLAG,A.SIGN_NO_LIST,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE, A.BILL_SEND_TYPE, A.IS_CUS_BASE_CHG, 
			A.LIAB_VALIDATE_TIME,CALL_PHONE1,CALL_PHONE2,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ ORDER BY A.VALIDATE_TIME ]]>
    </select>


    <select id="JRQD_findCsApplications" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT A.apply_code, A.change_id
        FROM APP___PAS__DBUSER.T_CS_APPLICATION A
        WHERE A.change_id = #{change_id}
        ]]>
	</select>
    <select id="JRQD_findcsPolicyAndApplication" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ select b.apply_code,a.accept_code,b.apply_time from APP___PAS__DBUSER.T_CS_APPLICATION b,
		APP___PAS__DBUSER.t_Cs_Accept_Change a where a.change_id=b.change_id ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
    </select>

    <!-- lianghxit 客户层受理查询 -->
    <select id="JRQD_findAllCsAcceptChange2" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE,A.BILL_SEND_TYPE
			,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
			WHERE ROWNUM <=  1000  AND A.SERVICE_CODE IN ('CM','CC','HI','IO','FK','CD')]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>

    <!-- 查询个数操作 -->
    <select id="JRQD_findCsAcceptChangeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
        <![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE 1 = 1 AND A.REVIEW_RESULT = #{review_result}
		AND A.Accept_Time between to_date(#{system_day} || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') and to_date(#{system_day} || ' 23:59:59', 'yyyy-mm-dd hh24:mi:ss')]]>
        <include refid="JRQD_findTotalWhereCondition"/>
    </select>

    <select id="JRQD_findCsAcceptChangeCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        <![CDATA[ SELECT COUNT(8) FROM DEV_PAS.T_CS_ACCEPT_CHANGE A WHERE 1 = 1]]>
        <if test=" change_id  != null and change_id !='' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
    </select>

    <!-- 分页查询操作 -->
    <select id="JRQD_queryCsAcceptChangeForPage" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT B.RN AS rowNumber, B.RELATED_ID, B.UNCON_CAUSE, B.CHANGE_FLAG, B.CANCEL_TIME, B.ACCEPT_TIME, B.HESITATE_FLAG,
			B.FINISH_TIME, B.CANCEL_CAUSE, B.REVIEW_ID, B.ORGAN_CODE, B.REVIEW_TIME, B.VALIDATE_TIME, 
			B.CHANGE_ID, B.PRE_FLAG, B.FEE_AMOUNT, B.SERVICE_CODE, B.ACCEPT_ID,  B.INSERT_OPERATOR_ID,
			B.REVIEW_PROPERTY, B.CONVEN_FLAG, B.CANCEL_NOTE, B.REVIEW_VIEW, B.ACCEPT_CODE, 
			B.REVIEW_RESULT, B.URGENT_DETAIL, B.PRE_VALIDATE_DATE, B.ORDER_ID, B.UNCON_TYPE, B.ACCEPT_STATUS , 
			B.CANCEL_ID, B.BACK_ENTYR_CAUSE, B.URGENT_CAUSE, B.URGENT_FLAG, B.DELAY_CAUSE,B.REVIEW_ACCEPT_RATE, B.REVIEW_INPUT_RATE, B.REVIEW_RATE, B.IS_RENEW_PAYMENT,B.IS_REPAY_LOAN FROM (
					SELECT ROWNUM RN, A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE,A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN
			,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <= #{LESS_NUM} ]]>
        <!-- <include refid="JRQD_请添加查询条件" /> -->
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
        <![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
    </select>

    <!-- 查询单条数据操作 -->
    <select id="JRQD_findCsAcceptChange" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[  SELECT A.SQ_READ_TIME,A.GZ_READ_TIME,A.GXSM_READ_TIME,A.CHOOSE_FLAG,A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,
			A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
			A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG,A.CALL_PHONE1,A.CALL_PHONE2, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE,
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE ,A.INSERT_BY,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE,
			A.UPDATE_TIME,A.UPDATE_BY,A.is_saved,A.is_sign_flag,A.sign_no_list, A.SERVICE_CODE,A.IS_CUS_BASE_CHG,
			(select ump.user_name from dev_pas.t_udmp_user ump where a.review_id = ump.user_id) ApproOperatorName,
			(select ump.real_name from dev_pas.t_udmp_user ump where a.INSERT_OPERATOR_ID = ump.user_id) OperatorName,
			A.BALANCE_FLAG,A.CUST_ID_UPPER_FLAG,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG,A.CALL_PHONE1,
			null as TRACBACKFLAG
			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A 
			WHERE 1=1 
			and ROWNUM =1 ]]>
        <include refid="JRQD_queryCsAcceptChangeBySingle"/>
    </select>

    <!--没事儿别乱用这个sql，返回的fee_amount字段不是原表的数据。
     查询本次受理下收付费金额，如果最终没有收付费返回0，如果最终收费返回0，如果最终付费返回最终付费金额绝对值。用于判断是否上收业务。 -->
    <select id="JRQD_findCsAcceptChangeOverrideFeeAmount" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[  select (case
         when b.fee_amount >= 0 then
          0
         when b.fee_amount < 0 then
          -b.fee_amount
          when b.fee_amount is null then
          0
       end) fee_amount,
       B.IS_ELEC_SIGN,
       B.IS_RENEW_PAYMENT,
       B.IS_REPAY_LOAN,
       B.IS_ADD_DELAY,
       B.ROLLBACK_CAUSE,
       B.ROLLBACK_NOTE,
       B.ROLLBACK_APP_TIME,
       B.ROLLBACK_APP_ID,
       B.RELATED_ID,
       B.UNCON_CAUSE,
       B.CHANGE_FLAG,
       B.CANCEL_TIME,
       B.ACCEPT_TIME,
       B.HESITATE_FLAG,
       B.CALL_PHONE1,
       B.CALL_PHONE2,
       B.FINISH_TIME,
       B.CANCEL_CAUSE,
       B.REVIEW_ID,
       B.ORGAN_CODE,
       B.REVIEW_TIME,
       B.VALIDATE_TIME,
       B.CHANGE_ID,
       B.PRE_FLAG,
       B.ACCEPT_ID,
       B.INSERT_OPERATOR_ID,
       B.REVIEW_PROPERTY,
       B.CONVEN_FLAG,
       B.CANCEL_NOTE,
       B.REVIEW_VIEW,
       B.ACCEPT_CODE,
       B.REVIEW_RESULT,
       B.URGENT_DETAIL,
       B.PRE_VALIDATE_DATE,
       B.ORDER_ID,
       B.UNCON_TYPE,
       B.ACCEPT_STATUS,
       B.CANCEL_ID,
       B.BACK_ENTYR_CAUSE,
       B.URGENT_CAUSE,
       B.URGENT_FLAG,
       B.DELAY_CAUSE,
       B.INSERT_BY,
       B.REVIEW_ACCEPT_RATE,
       B.REVIEW_INPUT_RATE,
       B.REVIEW_RATE,
       B.UPDATE_TIME,
       B.UPDATE_BY,
       B.is_saved,
       B.is_sign_flag,
       B.sign_no_list,
       B.IS_CUS_BASE_CHG,
       b.ApproOperatorName,
       b.OperatorName,
       b.BALANCE_FLAG,
       b.CUST_ID_UPPER_FLAG,
       b.SIGN_TYPE,
       b.FACE_FLAG,
       b.SKY_SIGN,
       b.IDENTITY_FLAG,
       b.IS_IDENTITY_CHECK,
       b.LOBBY_MANAGER_RESULT,
       b.LOBBY_MANAGER_NAME,
       b.FACE_RECOGNITION_RESULT,
       b.AUTHENTICATION_RESULT,
       b.service_code
  from (SELECT (select sum((case
                             when t.arap_flag = 1 then
                              t.fee_amount
                             else
                              -t.fee_amount
                           end))
                  from dev_pas.t_cs_prem_arap t
                 where t.business_code = a.accept_code) fee_amount,
               A.IS_ELEC_SIGN,
               A.IS_RENEW_PAYMENT,
               A.IS_REPAY_LOAN,
               A.IS_ADD_DELAY,
               A.ROLLBACK_CAUSE,
               A.ROLLBACK_NOTE,
               A.ROLLBACK_APP_TIME,
               A.ROLLBACK_APP_ID,
               A.RELATED_ID,
               A.UNCON_CAUSE,
               A.CHANGE_FLAG,
               A.CANCEL_TIME,
               A.ACCEPT_TIME,
               A.HESITATE_FLAG,
               A.CALL_PHONE1,
               A.CALL_PHONE2,
               A.FINISH_TIME,
               A.CANCEL_CAUSE,
               A.REVIEW_ID,
               A.ORGAN_CODE,
               A.REVIEW_TIME,
               A.VALIDATE_TIME,
               A.CHANGE_ID,
               A.PRE_FLAG,
               A.ACCEPT_ID,
               A.INSERT_OPERATOR_ID,
               A.REVIEW_PROPERTY,
               A.CONVEN_FLAG,
               A.CANCEL_NOTE,
               A.REVIEW_VIEW,
               A.ACCEPT_CODE,
               A.REVIEW_RESULT,
               A.URGENT_DETAIL,
               A.PRE_VALIDATE_DATE,
               A.ORDER_ID,
               A.UNCON_TYPE,
               A.ACCEPT_STATUS,
               A.CANCEL_ID,
               A.BACK_ENTYR_CAUSE,
               A.URGENT_CAUSE,
               A.URGENT_FLAG,
               A.DELAY_CAUSE,
               A.INSERT_BY,
               A.REVIEW_ACCEPT_RATE,
               A.REVIEW_INPUT_RATE,
               A.REVIEW_RATE,
               A.UPDATE_TIME,
               A.UPDATE_BY,
               A.is_saved,
               A.is_sign_flag,
               A.sign_no_list,
               A.SERVICE_CODE,
               A.IS_CUS_BASE_CHG,
               (select ump.user_name
                  from dev_pas.t_udmp_user ump
                 where a.review_id = ump.user_id) ApproOperatorName,
               (select ump.real_name
                  from dev_pas.t_udmp_user ump
                 where a.INSERT_OPERATOR_ID = ump.user_id) OperatorName,
               A.BALANCE_FLAG,
               A.CUST_ID_UPPER_FLAG,
               A.SIGN_TYPE,
               A.FACE_FLAG,
               A.SKY_SIGN,
               A.IDENTITY_FLAG,
               A.IS_IDENTITY_CHECK,
               A.LOBBY_MANAGER_RESULT,
               A.LOBBY_MANAGER_NAME,
               A.FACE_RECOGNITION_RESULT,
               A.AUTHENTICATION_RESULT
          FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
         WHERE 1 = 1 ]]>
        <include refid="JRQD_queryCsAcceptChangeBySingle"/>
        <![CDATA[) B]]>
    </select>
    <!-- 查询是否是批量退保已实付数据 -->
    <select id="JRQD_findCsAcceptChangeBS" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
        SELECT AC.IS_RENEW_PAYMENT,
               AC.IS_ADD_DELAY,
               AC.ROLLBACK_CAUSE,
               AC.ROLLBACK_NOTE,
               AC.ROLLBACK_APP_TIME,
               AC.ROLLBACK_APP_ID,
               AC.RELATED_ID,
               AC.UNCON_CAUSE,
               AC.CHANGE_FLAG,
               AC.CANCEL_TIME,
               AC.ACCEPT_TIME,
               AC.HESITATE_FLAG,
               AC.FINISH_TIME,
               AC.CANCEL_CAUSE,
               AC.REVIEW_ID,
               AC.ORGAN_CODE,
               AC.REVIEW_TIME,
               AC.VALIDATE_TIME,
               AC.CHANGE_ID,
               AC.PRE_FLAG,
               AC.FEE_AMOUNT,
               AC.SERVICE_CODE,
               AC.ACCEPT_ID,
               AC.INSERT_OPERATOR_ID,
               AC.REVIEW_PROPERTY,
               AC.CONVEN_FLAG,
               AC.CANCEL_NOTE,
               AC.REVIEW_VIEW,
               AC.ACCEPT_CODE,
               AC.REVIEW_RESULT,
               AC.URGENT_DETAIL,
               AC.PRE_VALIDATE_DATE,
               AC.ORDER_ID,
               AC.UNCON_TYPE,
               AC.ACCEPT_STATUS,
               AC.CANCEL_ID,
               AC.BACK_ENTYR_CAUSE,
               AC.URGENT_CAUSE,
               AC.URGENT_FLAG,
               AC.DELAY_CAUSE,
               AC.INSERT_BY,
               AC.REVIEW_ACCEPT_RATE,
               AC.REVIEW_INPUT_RATE,
               AC.REVIEW_RATE,
               AC.UPDATE_TIME,
               AC.UPDATE_BY,
               AC.is_saved,
               AC.is_sign_flag,
               AC.sign_no_list,
               null as NOT_ONLINE_POLICY,
               AC.ABNORMAL_PAY_FLAG,
               AC.POLICY_LOST_FLAG,
               AC.POLICY_REISSUE_FLAG
        from dev_pas.t_cs_accept_change ac,
             dev_pas.t_batch_surrender_policy bsp
        where ac.change_id = bsp.change_id
          and bsp.REAL_PAY_FLAG = '1'
          and ac.accept_code = #{accept_code}
        ]]>
	</select>

    <!-- 查询单条数据操作 -->
    <select id="JRQD_findCsAcceptChange2" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
        SELECT T.uw_id, A.accept_id
        FROM APP___PAS__DBUSER.T_UW_MASTER T,
             APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
        WHERE A.ACCEPT_CODE = T.BIZ_CODE
          AND A.ACCEPT_CODE = #{accept_code}
        order by t.uw_id desc
        ]]>
	</select>

    <!-- 查询所有操作 -->
    <select id="JRQD_findCsAcceptChangeByCustID" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
						A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, 
			A.REVIEW_INPUT_RATE, A.REVIEW_RATE,CALL_PHONE1,CALL_PHONE2,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG
			 FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>
    <!-- 扫描任务查询 -->
    <sql id="JRQD_csAcceptChangeWhereCondition_scan">
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND B.APPLY_CODE = #{apply_code} ]]></if>
        <if test="accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code}]]></if>
        <if test="accept_time_start != null and accept_time_start != ''  ">
            <![CDATA[ AND A.ACCEPT_TIME>=#{accept_time_start}]]></if>
        <if test="accept_time_end != null and accept_time_end != ''  ">
            <![CDATA[ AND A.ACCEPT_TIME<=#{accept_time_end}]]></if>
        <if test="accept_status != null and accept_status != '' ">
            <![CDATA[ AND A.ACCEPT_STATUS=#{accept_status} ]]></if>
        <if test="accept_status_list  != null and accept_status_list.size()!=0 ">
            <![CDATA[ AND (]]>
            <foreach collection="accept_status_list" item="accept_status_item"
                     index="index" open="" close="" separator="or">
                <![CDATA[ A.ACCEPT_STATUS=#{accept_status_item} ]]>
            </foreach>
            <![CDATA[) ]]>
        </if>
        <if test="(insert_by_name != null and insert_by_name != '') or (accept_by_name != null and accept_by_name != '') ">
            <![CDATA[ AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_UDMP_USER C WHERE C.USER_ID=A.INSERT_BY ]]>
            <if test="insert_by_name != null and insert_by_name != '' ">
                <![CDATA[ AND C.REAL_NAME LIKE '%${insert_by_name}%'  ]]>
            </if>
            <if test="accept_by_name != null and accept_by_name != '' ">
                <![CDATA[ AND C.REAL_NAME LIKE '%${accept_by_name}%'  ]]>
            </if>
            <![CDATA[ ) ]]>
        </if>
        <if test="policy_code != null and policy_code != ''  ">
            <![CDATA[ AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE D WHERE D.ACCEPT_ID=A.ACCEPT_ID AND D.POLICY_CODE = #{policy_code}) ]]>
        </if>
    </sql>
    <!-- 扫描任务查询 -->
    <select id="JRQD_queryCsAcceptChangeForPage__scan" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT E.RN AS rowNumber, E.CHANGE_ID, E.APPLY_CODE, E.ACCEPT_ID,  E.ACCEPT_CODE, E.SERVICE_CODE, E.ACCEPT_STATUS, E.APPLY_TIME,
			E.INSERT_BY, E.ACCEPT_TIME FROM 
			(SELECT ROWNUM RN, A.CHANGE_ID, B.APPLY_CODE,B.APPLY_TIME, A.ACCEPT_ID,  A.ACCEPT_CODE, A.SERVICE_CODE, A.ACCEPT_STATUS, 
				A.INSERT_BY, A.ACCEPT_TIME FROM  APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A,APP___PAS__DBUSER.T_CS_APPLICATION B ,
				APP___PAS__DBUSER.T_CS_POLICY_CHANGE C,
               APP___PAS__DBUSER.T_CONTRACT_MASTER  D
				WHERE B.CHANGE_ID=A.CHANGE_ID 
				AND C.CHANGE_ID = A.CHANGE_ID
           		AND D.POLICY_CODE = C.POLICY_CODE]]>
        <if test="apply_time != null and apply_time != ''  ">
            <![CDATA[ AND to_char(b.apply_time,'yyyy-MM-DD') =to_char(#{apply_time},'yyyy-MM-DD') ]]>
        </if>
        <if test="sign_status != null and sign_status != ''  ">
            <![CDATA[ AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_CS_APP_DOC F WHERE F.CHANGE_ID=A.CHANGE_ID AND F.SIGN_STATUS=#{sign_status}) ]]>
        </if>
        <if test="organ_code != null and organ_code != ''  ">
            <![CDATA[AND  (EXISTS (SELECT 1
                  FROM (SELECT T.ORGAN_CODE
                  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
                 START WITH T.ORGAN_CODE = #{organ_code}
                CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE) TT WHERE TT.ORGAN_CODE=A.ORGAN_CODE)
           OR 
              EXISTS (SELECT 1 
                  FROM (SELECT T.ORGAN_CODE
                  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
                 START WITH T.ORGAN_CODE = #{organ_code}
                CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE) TT WHERE TT.ORGAN_CODE=D.ORGAN_CODE)
           ) ]]>
        </if>
        <![CDATA[ AND ROWNUM <= #{LESS_NUM} ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition_scan"/>
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
        <![CDATA[ )  E
			WHERE E.RN > #{GREATER_NUM} ]]>
    </select>
    <!-- 扫描任务查询 -->
    <select id="JRQD_queryCsAcceptChangeTotal_scan" resultType="java.lang.Integer" parameterType="java.util.Map">
        <![CDATA[SELECT COUNT(1) FROM  APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A,APP___PAS__DBUSER.T_CS_APPLICATION B,
				APP___PAS__DBUSER.T_CS_POLICY_CHANGE C,
               APP___PAS__DBUSER.T_CONTRACT_MASTER  D 
				WHERE B.CHANGE_ID=A.CHANGE_ID
				AND C.CHANGE_ID = A.CHANGE_ID
           AND D.POLICY_CODE = C.POLICY_CODE  ]]>
        <if test="apply_time != null and apply_time != ''  ">
            <![CDATA[ AND to_char(b.apply_time,'yyyy-MM-DD') =to_char(#{apply_time},'yyyy-MM-DD') ]]>
        </if>
        <if test="sign_status != null and sign_status != ''  ">
            <![CDATA[ AND EXISTS(SELECT 1 FROM APP___PAS__DBUSER.T_CS_APP_DOC F WHERE F.CHANGE_ID=A.CHANGE_ID AND F.SIGN_STATUS=#{sign_status}) ]]>
        </if>
        <if test="organ_code != null and organ_code != ''  ">
            <![CDATA[AND  (EXISTS (SELECT 1
                  FROM (SELECT T.ORGAN_CODE
                  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
                 START WITH T.ORGAN_CODE = #{organ_code}
                CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE) TT WHERE TT.ORGAN_CODE=A.ORGAN_CODE)
           OR 
              EXISTS (SELECT 1 
                  FROM (SELECT T.ORGAN_CODE
                  FROM APP___PAS__DBUSER.T_UDMP_ORG_REL T
                 START WITH T.ORGAN_CODE = #{organ_code}
                CONNECT BY PRIOR T.ORGAN_CODE = T.UPORGAN_CODE) TT WHERE TT.ORGAN_CODE=D.ORGAN_CODE)
           ) ]]>
        </if>
        <include refid="JRQD_csAcceptChangeWhereCondition_scan"/>
    </select>


    <!-- qiaoqc_wb	检查当日是否做过该保全项 -->
    <select id="JRQD_findAcceptCheckInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        select t.accept_id
        from APP___PAS__DBUSER.t_cs_accept_change t
        where t.service_code = #{service_code}
          and t.change_id = #{change_id}
          and to_char(t.insert_Time, 'yyyy-MM-dd') || '00:00:00' < to_char(sysdate, 'yyyy-MM-dd hh24:mm:ss')
          and to_char(t.insert_Time, 'yyyy-MM-dd') || '23:59:59' > to_char(sysdate, 'yyyy-MM-dd hh24:mm:ss')
        ]]>
	</select>

    <!-- 查询所有操作 -->
    <select id="JRQD_findReissuInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME,
			A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE,CALL_PHONE1,CALL_PHONE2
			,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG
			 FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ AND A.VALIDATE_TIME IS　NOT NULL AND EXISTS( SELECT 1 FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE B WHERE B.ACCEPT_ID = A.ACCEPT_ID AND B.POLICY_ID = #{policy_id}) ]]>
        <![CDATA[ ORDER BY A.VALIDATE_TIME DESC ]]>
    </select>
    <!-- 查询补发信息，按申请确认日排序 -->
    <select id="JRQD_findPolicyReissuInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME,
			A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE,CALL_PHONE1,CALL_PHONE2
			,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG
			 FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ AND A.VALIDATE_TIME IS　NOT NULL AND EXISTS( SELECT 1 FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE B WHERE B.ACCEPT_ID = A.ACCEPT_ID AND B.POLICY_ID = #{policy_id}) ]]>
        <![CDATA[ ORDER BY A.ACCEPT_TIME ASC ]]>
    </select>
    <!-- 查询申请信息下是否存在受理信息，且受理信息必须为录入完成状态  -->
    <select id="JRQD_findCsAcceptChangeInfoByChangeId" resultType="java.lang.Integer" parameterType="java.util.Map">
        <!-- <![CDATA[
            SELECT COUNT(1) FROM DUAL WHERE 1 = 1
               AND (SELECT COUNT(1) FROM T_CS_ACCEPT_CHANGE A WHERE 1=1  ]]>
                    <include refid="JRQD_queryCsAcceptChangeByChangeId" />
               <![CDATA[
               ) > 0
               AND (SELECT COUNT(1) FROM T_CS_ACCEPT_CHANGE A WHERE 1=1  ]]>
                    <include refid="JRQD_queryCsAcceptChangeByChangeId" />
               <![CDATA[
               ) = (SELECT COUNT(1) FROM T_CS_ACCEPT_CHANGE A WHERE 1=1  ]]>
                 <include refid="JRQD_queryCsAcceptChangeByChangeId" />
                  <![CDATA[  AND A.ACCEPT_STATUS = '07')

              ]]> -->
        <![CDATA[
          	SELECT COUNT(1) FROM DUAL WHERE 1 = 1
          	AND (SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE 1=1
          	]]>
        <include refid="JRQD_queryCsAcceptChangeByChangeId"/>
        <![CDATA[  AND A.ACCEPT_STATUS in ('07','21') ) >0
          	]]>
    </select>

    <!-- 查询申请信息下所有保全受理号的状态是否存在不处于“录入完成”、“撤销”、“终止”状态，新增“复核修改”  -->
    <select id="JRQD_findCsAcceptChangeInfoByChangeIdNewAddition" resultType="java.lang.Integer"
            parameterType="java.util.Map">
        <![CDATA[
          	SELECT COUNT(1) FROM DUAL WHERE 1 = 1
          	AND (SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE 1=1
          	]]>
        <include refid="JRQD_queryCsAcceptChangeByChangeId"/>
        <![CDATA[  AND A.ACCEPT_STATUS not in ('07','21','12','22') ) >0
          	]]>
    </select>

    <!--  add by panmd_wb 查询受理号下最大保全项生效日期 -->
    <select id="JRQD_findMaxValiDateCsAcceptChange" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[	select * from (select t.ROLLBACK_CAUSE,t.ROLLBACK_NOTE,t.ROLLBACK_APP_TIME,t.ROLLBACK_APP_ID,
						  T.RELATED_ID, T.UNCON_CAUSE, T.CHANGE_FLAG, T.CANCEL_TIME, T.ACCEPT_TIME, T.HESITATE_FLAG, 
					      T.FINISH_TIME, T.CANCEL_CAUSE, T.REVIEW_ID, T.ORGAN_CODE, T.REVIEW_TIME, T.VALIDATE_TIME, 
					      T.CHANGE_ID, T.PRE_FLAG, T.FEE_AMOUNT, T.SERVICE_CODE, T.ACCEPT_ID,  T.INSERT_OPERATOR_ID,
					      T.REVIEW_PROPERTY, T.CONVEN_FLAG, T.CANCEL_NOTE, T.REVIEW_VIEW, T.ACCEPT_CODE, T.IS_SAVED,
					      T.REVIEW_RESULT, T.URGENT_DETAIL, T.PRE_VALIDATE_DATE, T.ORDER_ID, T.UNCON_TYPE, T.ACCEPT_STATUS , 
					      T.CANCEL_ID, T.BACK_ENTYR_CAUSE, T.URGENT_CAUSE, T.URGENT_FLAG, T.DELAY_CAUSE,T.INSERT_BY,T.INSERT_TIME,
					      T.IS_SIGN_FLAG,T.SIGN_NO_LIST,T.REVIEW_ACCEPT_RATE, T.REVIEW_INPUT_RATE, T.REVIEW_RATE,null as NOT_ONLINE_POLICY,T.ABNORMAL_PAY_FLAG,T.POLICY_LOST_FLAG,T.POLICY_REISSUE_FLAG,
						  row_number() over(order by VALIDATE_TIME desc) rn
                from APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE t where T.ACCEPT_STATUS = '18' ]]>
        <include refid="JRQD_queryCsAcceptChangeByAcceptIdsCondition"/>
        <![CDATA[ order by VALIDATE_TIME desc) r  where r.rn <= 1 ]]>
    </select>

    <sql id="JRQD_queryCsAcceptChangeByAcceptIdsCondition">
        <if test="accept_ids  != null and accept_ids.size()!=0">
            <![CDATA[ AND T.ACCEPT_ID IN (]]>
            <foreach collection="accept_ids" item="accept_id_item"
                     index="index" open="" close="" separator=",">#{accept_id_item}
            </foreach>
            <![CDATA[)]]>
        </if>
    </sql>
    <!--  add by panmd_wb 返回受理是更新受理状态——受理中 未使用-->
    <select id="JRQD_updateCsAcceptStatus" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
        update APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE t
        set t.accept_status='04' ]]>
        <![CDATA[ where t.accept_id = #{accept_id}
        ]]>
 	</select>
    <!-- 更新受理状态为生效 -->
    <select id="JRQD_updateAcceptStatusEffect" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
        update APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE t
        set t.accept_status='18' ]]>
        <![CDATA[ where t.accept_id = #{accept_id}
        ]]>
 	</select>
    <!--  add by qiaoqc_wb 核保查询保全的既往保单 -->
    <select id="JRQD_find_uw_history_csaccept" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT 
        	A.SERVICE_CODE,
			A.POLICY_CODE,
			A.ACCEPT_TIME,
			A.ACCEPT_CODE,
			A.ACCEPT_STATUS,
			A.OPERATION_TYPE,
			A.MASTER_BUSI_ITEM_ID,
			A.BUSI_PROD_CODE,
			A.UW_ID,
			A.UW_DECISION,
			A.UW_USER_ID,
			A.DECISION_CODE,
			(SELECT ADD_MONTHS(SYSDATE,-36) FROM DUAL) THRYEAR,
			LISTAGG(TRIM(A.SURVEY_MODULE_RESULT),',') 
			WITHIN GROUP( ORDER BY A.SURVEY_MODULE_RESULT DESC) SURVEY_MODULE_RESULT
			FROM  (SELECT DISTINCT ACC.SERVICE_CODE,
                  ACC.POLICY_CODE,
                  ACC.ACCEPT_TIME,
                  ACC.ACCEPT_CODE,
                  ACC.ACCEPT_STATUS,
                  BUS.OPERATION_TYPE,
                  BUS.MASTER_BUSI_ITEM_ID,
                  BUS.BUSI_PROD_CODE,
                  UM.UW_ID,
                  UM.UW_DECISION,
                  UM.UW_USER_ID,
                  T.DECISION_CODE,
			      CQC.SURVEY_MODULE_RESULT
    FROM (SELECT B.SERVICE_CODE,
                 C.POLICY_CODE,
                 C.POLICY_CHG_ID,
                 B.ACCEPT_TIME,
                 B.ACCEPT_CODE,
                 B.ACCEPT_STATUS,
				 B.ACCEPT_ID
            FROM APP___PAS__DBUSER.T_CS_APPLICATION A
           INNER JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
              ON A.CHANGE_ID = B.CHANGE_ID
           INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE C
              ON B.ACCEPT_ID = C.ACCEPT_ID
             AND A.CHANGE_ID = C.CHANGE_ID
             AND C.SERVICE_CODE IN ('CC',
                                    'AE',
                                    'BC',
                                    'NS',
                                    'DA',
                                    'PA',
                                    'RE',
                                    'CA',
                                    'XX',
                                    'HI',
                                    'RA',
                                    'AM')
           ]]>
        <if test="queryFlag == '' ">
            <![CDATA[
             INNER JOIN APP___PAS__DBUSER.T_INSURED_LIST CIL
              ON C.POLICY_CODE = CIL.POLICY_CODE
              AND C.POLICY_ID = CIL.POLICY_ID 
              AND CIL.CUSTOMER_ID = #{customer_id}
            ]]>
        </if>
        <if test="queryFlag == 1 ">
            <![CDATA[
             INNER JOIN APP___PAS__DBUSER.T_POLICY_HOLDER PH
              ON C.POLICY_CODE = PH.POLICY_CODE
              AND C.POLICY_ID = PH.POLICY_ID 
              AND PH.CUSTOMER_ID = #{customer_id}
            ]]>
        </if>
        <![CDATA[                         
          ) ACC
   	  INNER JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER UCC
      ON ACC.POLICY_CODE = UCC.POLICY_CODE
   	  INNER JOIN APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD BUS
      ON UCC.POLICY_ID = BUS.POLICY_ID
      AND BUS.OLD_NEW='1' AND BUS.POLICY_CHG_ID=ACC.POLICY_CHG_ID
          ]]>
        <if test="queryFlag == 1">
            <![CDATA[ 
           
           AND 
			   EXISTS (
			   
			   SELECT 1 FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD CC WHERE 
			   CC.POLICY_ID=BUS.POLICY_ID
			   AND CC.POLICY_CHG_ID=BUS.POLICY_CHG_ID
			   AND CC.BUSI_PROD_CODE IN ('00425000', '00952000','00861000')
			   )
           
           ]]>
        </if>
        <![CDATA[  
        LEFT JOIN APP___PAS__DBUSER.T_UW_MASTER UM
		ON UM.BIZ_CODE = ACC.ACCEPT_CODE
		LEFT JOIN APP___PAS__DBUSER.T_UW_BUSI_PROD T
		ON T.POLICY_CODE = ACC.POLICY_CODE
		AND T.UW_ID = UM.UW_ID
		AND T.BUSI_ITEM_ID = BUS.BUSI_ITEM_ID
		LEFT JOIN APP___PAS__DBUSER.T_CS_QUESTIONAIRE_CUSTOMER CQC
		ON CQC.ACCEPT_ID = ACC.ACCEPT_ID
		AND CQC.CUSTOMER_ID = #{customer_id}
		LEFT JOIN APP___PAS__DBUSER.T_QUESTIONAIRE_INFO Q
		ON CQC.SURVEY_QUESTION_ID = Q.SURVEY_QUESTION_ID 
		AND Q.SURVEY_VERSION IN ('19','20','52','65','25') ) A
		GROUP BY A.SERVICE_CODE,
		A.POLICY_CODE,
		A.ACCEPT_TIME,
		A.ACCEPT_CODE,
		A.ACCEPT_STATUS,
		A.OPERATION_TYPE,
		A.MASTER_BUSI_ITEM_ID,
		A.BUSI_PROD_CODE,
		A.UW_ID,
		A.UW_DECISION,
		A.UW_USER_ID,
		A.DECISION_CODE ]]>
    </select>

    <!-- 根据受理号和受理机构查询受理信息 -->
    <select id="JRQD_findCsAcceptChangeByOrgan" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ select distinct t.accept_code, t.accept_id
		    from APP___PAS__DBUSER.t_cs_accept_change t
		    left join APP___PAS__DBUSER.t_prem_arap tpa
		      on t.accept_code = tpa.business_code
		    left join APP___PAS__DBUSER.t_cs_application tca
		    on tca.change_id = t.change_id
		   where t.accept_status = '13'
		     and tpa.fee_status = '01']]>
        <if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND t.ACCEPT_CODE = #{accept_code} ]]></if>
        <![CDATA[ and mod(t.accept_id,#{modNum}) = #{start}]]>
        <![CDATA[ ORDER BY t.ACCEPT_ID ]]>
    </select>
    <!-- 获取可以撤销的保全变更信息 -->
    <select id="JRQD_findAllCancelCsAcceptChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        select *
        from APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE t
        where t.ACCEPT_CODE = #{accept_code}
          and t.ACCEPT_STATUS in ('03', '04', '05', '06', '07', '08', '09', '10', '01', '21', '13')
        ]]>
	</select>

    <!-- 通过受理号查询保全信息 -->
    <select id="JRQD_findAccEdorInfoByBussNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT CP.POLICY_CODE                          AS CONTNO,
               A.SERVICE_CODE,
               A.ACCEPT_STATUS,
               (SELECT AP.APPLY_CODE
                FROM APP___PAS__DBUSER.T_CS_APPLICATION AP
                WHERE AP.CHANGE_ID = A.CHANGE_ID)      AS BUSSNOTYPE,
               (SELECT BP.PRODUCT_ABBR_NAME
                FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP,
                     APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TBP
                WHERE BP.BUSINESS_PRD_ID = TBP.BUSI_PRD_ID
                  AND TBP.POLICY_CODE = CP.POLICY_CODE
                  AND TBP.MASTER_BUSI_ITEM_ID IS NULL) AS PRODUCTNAME,
               (SELECT AP.APPLY_TIME
                FROM APP___PAS__DBUSER.T_CS_APPLICATION AP
                WHERE AP.CHANGE_ID = A.CHANGE_ID)      AS EDORAPPDATE,
               (SELECT CHANGE_STATUS
                FROM APP___PAS__DBUSER.T_CHANGE
                WHERE CHANGE_ID = A.CHANGE_ID)         AS EDORFLAG,
               null                     AS NOTONLINEPOLICY
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A,
             APP___PAS__DBUSER.T_CS_POLICY_CHANGE CP
        WHERE A.ACCEPT_CODE = #{accept_code}
          AND A.CHANGE_ID = CP.CHANGE_ID
          AND A.ACCEPT_ID = CP.ACCEPT_ID
        ]]>
	</select>

    <!-- yuzw添加操作 -->
    <insert id="JRQD_addCsAcceptChangeyuzw" useGeneratedKeys="false" parameterType="java.util.Map">
        <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="accept_id">
            SELECT APP___PAS__DBUSER.S_CS_ACCEPT_CHANGE.NEXTVAL FROM DUAL
        </selectKey>
        <![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE(
				RELATED_ID, UNCON_CAUSE, CHANGE_FLAG, CANCEL_TIME, ACCEPT_TIME, HESITATE_FLAG, 
				FINISH_TIME, CANCEL_CAUSE, REVIEW_ID, INSERT_TIMESTAMP, ORGAN_CODE, REVIEW_TIME, VALIDATE_TIME, 
				UPDATE_BY, CHANGE_ID, PRE_FLAG, FEE_AMOUNT, SERVICE_CODE, ACCEPT_ID, 
				INSERT_TIME, REVIEW_PROPERTY, UPDATE_TIME, CONVEN_FLAG, CANCEL_NOTE, REVIEW_VIEW, ACCEPT_CODE, 
				REVIEW_RESULT, URGENT_DETAIL, PRE_VALIDATE_DATE, ORDER_ID, UNCON_TYPE, ACCEPT_STATUS, 
				CANCEL_ID, BACK_ENTYR_CAUSE, UPDATE_TIMESTAMP, URGENT_CAUSE, INSERT_BY, URGENT_FLAG, DELAY_CAUSE ,REVIEW_ACCEPT_RATE, REVIEW_INPUT_RATE, REVIEW_RATE,BILL_SEND_TYPE,LOBBY_MANAGER_RESULT,LOBBY_MANAGER_NAME
				,FACE_RECOGNITION_RESULT,AUTHENTICATION_RESULT) 
			VALUES (
				#{related_id, jdbcType=NUMERIC}, #{uncon_cause, jdbcType=VARCHAR} ,#{change_flag, jdbcType=VARCHAR},#{cancel_time, jdbcType=DATE} , #{accept_time, jdbcType=DATE} , #{hesitate_flag, jdbcType=NUMERIC} 
				, #{finish_time, jdbcType=DATE} , #{cancel_cause, jdbcType=VARCHAR} , #{review_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{review_time, jdbcType=DATE} , #{validate_time, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{pre_flag, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{accept_id, jdbcType=NUMERIC} 
				, SYSDATE , #{review_property, jdbcType=VARCHAR} , SYSDATE , #{conven_flag, jdbcType=NUMERIC} , #{cancel_note, jdbcType=VARCHAR} , #{review_view, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} 
				, #{review_result, jdbcType=VARCHAR} , #{urgent_detail, jdbcType=VARCHAR} , #{pre_validate_date, jdbcType=DATE} , #{order_id, jdbcType=NUMERIC} , #{uncon_type, jdbcType=VARCHAR} , #{accept_status, jdbcType=VARCHAR} 
				, #{cancel_id, jdbcType=NUMERIC} , #{back_entyr_cause, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{urgent_cause, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{urgent_flag, jdbcType=NUMERIC} , #{delay_cause, jdbcType=VARCHAR}
				,#{review_accept_rate, jdbcType=NUMERIC}, #{review_input_rate, jdbcType=NUMERIC}, #{review_rate, jdbcType=NUMERIC},#{bill_send_type, jdbcType=VARCHAR},#{lobby_manager_result, jdbcType=VARCHAR},#{lobby_manager_name, jdbcType=VARCHAR}
				,#{face_recognition_result, jdbcType=VARCHAR},#{authentication_result, jdbcType=VARCHAR} ) 
		 ]]>
    </insert>

    <!-- 犹豫期标识 -->
    <select id="JRQD_findPolicyHesitateFlag" resultType="java.util.Map"
            parameterType="java.util.Map">
		<![CDATA[
        select case
                   when count(t.policy_id) > 0 then
                       1
                   when count(t.policy_id) = 0 then
                       0
                   else
                       0 end as capital_balance
        from APP___PAS__DBUSER.T_POLICY_ACKNOWLEDGEMENT t
        where to_date(#{apply_time}, 'yyyy-mm-dd') >= t.acknowledge_date
          and to_date(#{apply_time}, 'yyyy-mm-dd') < t.acknowledge_date +
                                                     (select case
                                                                 when count(b.cfg_id) > 0 and
                                                                      max(b.free_look_perid) is not null then
                                                                     max(b.free_look_perid)
                                                                 when count(b.cfg_id) < 1 then 10
                                                                 else 10
                                                                 end as FREE_LOOK_PERID
                                                      from APP___PAS__DBUSER.T_FREE_LOOK_PERIOD_CFG b
                                                      where exists(
                                                                    select c.busi_item_id, c.busi_prd_id
                                                                    from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD c
                                                                    where c.master_busi_item_id is null
                                                                      and c.policy_id = #{policy_id}
                                                                      and b.business_prod_id = c.busi_prd_id))
          and t.policy_id = #{policy_id}
        ]]>
</select>
    <!-- 指定时间段内犹豫期退保条数统计 -->
    <select id="JRQD_findPolicyHesitateCounts" resultType="java.util.Map"
            parameterType="java.util.Map">
		<![CDATA[
        select case
                   when count(cg.policy_id) = 0 then
                       0
                   when count(cg.policy_id) > 0 then
                       count(cg.policy_id)
                   end policy_count
        from APP___PAS__DBUSER.T_CS_POLICY_CHANGE cg
        where cg.hesitate_flag = '1'
          and cg.service_code = 'CT'
          and cg.accept_id in
              (select c.accept_id
               from APP___PAS__DBUSER.t_cs_accept_change c
               where c.accept_status = '18'
                 and to_char(c.validate_time, 'yyyy-MM-dd') < #{end_date}
                 and to_char(c.validate_time, 'yyyy-MM-dd') > #{start_date})
        ]]>
</select>
    <!-- 查询挂起的保单受理号 -->
    <select id="JRQD_findAcceptCodebyPolicyCode" resultType="java.util.Map"
            parameterType="java.util.Map">
		<![CDATA[
        select accept_code
        from APP___PAS__DBUSER.t_cs_accept_change y,
             APP___PAS__DBUSER.t_cs_policy_change z
        where y.accept_id = z.accept_id
          and z.policy_code = #{policy_code}
          AND y.accept_status in ('04', '05', '06', '08', '09', '10', '11')
        ]]>
	</select>

    <select id="JRQD_findIsExistedAcceptService" resultType="java.lang.Integer" parameterType="java.util.Map">
	 <![CDATA[
        select count(1)
        from APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE t
        where t.accept_code = #{accept_code}
        ]]>
 	</select>

    <!-- 保全批改信息查询 -->
    <!--2018-06-27 为了 与新核心页面查询保持一致，sql语句添加accept_status not in ('01','02','24','25') 的条件-->
    <!-- <select id="JRQD_queryCsPolicyChangeList" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
         select AC.ACCEPT_CODE as EdorAcceptNo,AP.INSERT_TIMESTAMP,
            (select PRODUCT_ABBR_NAME from APP___PAS__DBUSER.T_BUSINESS_PRODUCT
              WHERE BUSINESS_PRD_ID = (select BUSI_PRD_ID from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD
              WHERE POLICY_ID=PC.POLICY_ID AND MASTER_BUSI_ITEM_ID IS NULL)) as RiskName,
            (select BUSI_PROD_CODE from APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD
              WHERE POLICY_ID=PC.POLICY_ID AND MASTER_BUSI_ITEM_ID IS NULL) as RiskCode,
            PC.SERVICE_CODE as EdorType,
            (SELECT SERVICE_NAME FROM APP___PAS__DBUSER.T_SERVICE WHERE SERVICE_CODE=PC.SERVICE_CODE) as EdorTypeName,
            PC.APPLY_TIME as EdorAppDate,
            (select TCA.APPLY_NAME
               from APP___PAS__DBUSER.T_CS_APPLICATION TCA
              WHERE TCA.CHANGE_ID = PC.CHANGE_ID) as EdorAppName,
            (select TCA.Service_Type
               from APP___PAS__DBUSER.T_CS_APPLICATION TCA
              WHERE TCA.CHANGE_ID = PC.CHANGE_ID) as AppType,
            AC.VALIDATE_TIME as EdorValiDate,
            AC.ACCEPT_STATUS as EdorState,
            AC.FINISH_TIME as ConfDate	,
            AC.NOT_ONLINE_POLICY as notOnLinePolicy,
            AC.ABNORMAL_PAY_FLAG as abnormalPayFlag,
            AC.POLICY_LOST_FLAG as policyLostFlag,
            AC.POLICY_REISSUE_FLAG as policyReissueFlag
            from APP___PAS__DBUSER.T_CS_APPLICATION   AP,
            APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC,
            APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC
       WHERE AP.CHANGE_ID = PC.CHANGE_ID
       and AC.ACCEPT_ID = PC.ACCEPT_ID(+)
       and PC.POLICY_CODE = #{policy_code}
       and AC.accept_status not in ('01','02','24','25')
      ]]>
      <if test=" service_code !=null and service_code!='' ">
              <![CDATA[
                  and PC.SERVICE_CODE = #{service_code}
              ]]>
      </if>
      <if test=" accept_start_time !=null and accept_start_time !='' ">
              <![CDATA[
                   and AC.ACCEPT_TIME >= #{accept_start_time}
              ]]>
      </if>
      <if test=" accept_end_time !=null and accept_end_time !='' ">
              <![CDATA[
                   and AC.ACCEPT_TIME <= #{accept_end_time}
              ]]>
      </if>
      <if test=" order_context !=null and order_context !='' ">
              <![CDATA[
                   ${order_context}
              ]]>
      </if>
       ORDER BY AP.INSERT_TIMESTAMP
    </select>
    -->

    <!-- 保全批改信息查询 -->
    <!--2018-06-27 为了 与新核心页面查询保持一致，sql语句添加accept_status not in ('01','02','24','25') 的条件-->
    <select id="JRQD_queryCsPolicyChangeList" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
	       select AC.ACCEPT_CODE as EdorAcceptNo,AP.INSERT_TIMESTAMP,
           (select PRODUCT_ABBR_NAME from APP___PAS__DBUSER.T_BUSINESS_PRODUCT 
             WHERE BUSINESS_PRD_ID = G.BUSI_PRD_ID) as RiskName,
             G.BUSI_PROD_CODE RiskCode,
           PC.SERVICE_CODE as EdorType,
           (SELECT SERVICE_NAME FROM APP___PAS__DBUSER.T_SERVICE WHERE SERVICE_CODE=PC.SERVICE_CODE) as EdorTypeName,
           PC.APPLY_TIME as EdorAppDate,
           (select TCA.APPLY_NAME
              from APP___PAS__DBUSER.T_CS_APPLICATION TCA
             WHERE TCA.CHANGE_ID = PC.CHANGE_ID) as EdorAppName,
           (select TCA.Service_Type
              from APP___PAS__DBUSER.T_CS_APPLICATION TCA
             WHERE TCA.CHANGE_ID = PC.CHANGE_ID) as AppType,
           AC.VALIDATE_TIME as EdorValiDate,
           AC.ACCEPT_STATUS as EdorState,
           AC.FINISH_TIME as ConfDate ,
           null as notOnLinePolicy,
           AC.ABNORMAL_PAY_FLAG as abnormalPayFlag,
           AC.POLICY_LOST_FLAG as policyLostFlag,
           AC.POLICY_REISSUE_FLAG as policyReissueFlag
         from APP___PAS__DBUSER.T_CS_APPLICATION   AP,
           APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC,
           APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE AC,
           APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD G          
      WHERE AP.CHANGE_ID = PC.CHANGE_ID
      and G.Policy_Id =PC.Policy_Id
      and AC.ACCEPT_ID = PC.ACCEPT_ID(+)
	    and PC.POLICY_CODE = #{policy_code}
	    and AC.accept_status not in ('01','02','24','25')
	   ]]>
        <if test=" service_code !=null and service_code!='' ">
            <![CDATA[
	   			and PC.SERVICE_CODE = #{service_code}
	   		]]>
        </if>
        <if test=" accept_start_time !=null and accept_start_time !='' ">
            <![CDATA[
	   			 and AC.ACCEPT_TIME >= #{accept_start_time}	   			 
	   		]]>
        </if>
        <if test=" accept_end_time !=null and accept_end_time !='' ">
            <![CDATA[
	   			 and AC.ACCEPT_TIME <= #{accept_end_time}
	   		]]>
        </if>
        <if test=" order_context !=null and order_context !='' ">
            <![CDATA[
	   			 ${order_context}
	   		]]>
        </if>
        ORDER BY AP.INSERT_TIMESTAMP
    </select>


    <!-- 查询挂起的保单受理号 -->
    <select id="JRQD_findNoImageCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        select count(*)
        from (select ROWNUM rn, T1.ACCEPT_ID, T1.Accept_Code, T1.Change_Id, T1.Accept_Status
              from (select TAC.ACCEPT_ID,
                           TAC.Accept_Code,
                           TAC.Change_Id,
                           TAC.Accept_Status
                    from APP___PAS__DBUSER.t_cs_accept_change TAC
                    where TAC.Accept_Status in ('06', '07', '08', '09', '10', '11')
                      AND not exists(select TI.accept_code, TI.change_id
                                     from APP___PAS__DBUSER.T_IMAGE TI
                                     where TAC.Accept_Code = TI.Accept_Code
                                       AND TAC.Change_Id = TI.Change_Id)
                    union
                    select TAC.ACCEPT_ID,
                           TAC.Accept_Code,
                           TAC.Change_Id,
                           TAC.Accept_Status
                    from APP___PAS__DBUSER.t_cs_accept_change TAC
                    where TAC.Accept_Status between '05' and '06'
                      AND sysdate - TAC.accept_time >= 5) T1)
        ]]>
	</select>

    <!-- 查询挂起的保单受理号 -->
    <select id="JRQD_findNoImageDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        select A.ACCEPT_ID, A.Accept_Code, A.Change_Id, A.Accept_Status, rownum rn
        from (select TAC.ACCEPT_ID,
                     TAC.Accept_Code,
                     TAC.Change_Id,
                     TAC.Accept_Status
              from APP___PAS__DBUSER.t_cs_accept_change TAC
              where TAC.Accept_Status in ('06', '07', '08', '09', '10', '11')
                AND not exists(select TI.accept_code, TI.change_id
                               from APP___PAS__DBUSER.T_IMAGE TI
                               where TAC.Accept_Code = TI.Accept_Code
                                 AND TAC.Change_Id = TI.Change_Id)
                and mod(tac.change_id, #{end_num}) = #{start_num}

              union
              select TAC.ACCEPT_ID,
                     TAC.Accept_Code,
                     TAC.Change_Id,
                     TAC.Accept_Status
              from APP___PAS__DBUSER.t_cs_accept_change TAC
              where TAC.Accept_Status between '05' and '06'
                AND sysdate - TAC.accept_time >= 5
                and mod(tac.change_id, #{end_num}) = #{start_num}) A
        ]]>
	</select>

    <!-- 查询保单是否做过某个保全项 -->
    <select id="JRQD_findProject" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        select count(*)
        from APP___PAS__DBUSER.t_policy_change t
        where t.policy_id =
              (select ta.policy_id
               from APP___PAS__DBUSER.t_contract_master ta
               where ta.policy_code = #{policy_code})
          and t.service_code = #{service_code}
        ]]>
	</select>

    <!-- 查询保单是否豁免 -->
    <select id="JRQD_isWaived" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        select count(*)
        from dual
        where exists(select *
                     from APP___PAS__DBUSER.T_contract_product t
                     where t.policy_code = '887182409366'
                       and t.is_waived = 1)
        ]]>
	</select>

    <!-- 根据条件查询手工撤销申请轨迹  add by zkm-->
    <select id="JRQD_queryCsEndorseManualCancelTrail" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
		select a.apply_code, ac.accept_code, pc.policy_code, ac.service_code, 
		ac.cancel_id, ac.cancel_cause, ac.cancel_time, ac.cancel_note, uu.user_name
		from APP___PAS__DBUSER.t_cs_policy_change pc
		left join APP___PAS__DBUSER.t_cs_accept_change ac
		on pc.change_id = ac.change_id
		left join APP___PAS__DBUSER.t_cs_application a
		on ac.change_id = a.change_id
		left join APP___PAS__DBUSER.t_udmp_user uu
    	on ac.cancel_id = uu.user_id where 1=1
		and ac.accept_status = '12'
		]]>
        <include refid="JRQD_csEndorseManualCancelTrail"/>
        <![CDATA[
		order by a.apply_code desc
		]]>
    </select>
    <!-- 根据受理号查询受理信息 -->
    <select id="JRQD_findCsAcceptChangeByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT  A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
			A.ACCEPT_CODE, A.ACCEPT_ID,A.CANCEL_ID,  A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, 
			A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE,   A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW,
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,
			A.INSERT_TIME,A.IS_SAVED,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE
			,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0') FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0') IDENTITY_FLAG,A.IS_IDENTITY_CHECK ,
			 B.APPLY_TIME,A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG
			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A,APP___PAS__DBUSER.T_CS_APPLICATION B  WHERE 1=1 and A.CHANGE_ID = B.CHANGE_ID ]]>
        <if test=" accept_code !=null and accept_code !='' ">
            <![CDATA[
	   			and A.ACCEPT_CODE = #{accept_code}
	   		]]>
        </if>
        <if test="accept_id !=null and accept_id !='' ">
            <![CDATA[
		   		and A.ACCEPT_ID = #{accept_id}
	   		]]>
        </if>
    </select>
    <!-- 查询未完成的保单受理号 -->
    <select id="JRQD_findCsAcceptChangeNotFinish" resultType="java.util.Map"
            parameterType="java.util.Map">
		<![CDATA[
        select accept_code
        from APP___PAS__DBUSER.t_cs_accept_change y,
             APP___PAS__DBUSER.t_cs_policy_change z
        where y.accept_id = z.accept_id
          and z.policy_id = #{policy_id}
          AND y.accept_status in ('03', '04', '05', '06', '07', '08', '09', '10', '11', '13')
          AND y.service_code = 'LN'
        ]]>
	</select>

    <!-- 查询保单是否做过某些保全项 -->
    <select id="JRQD_findSomeProject" resultType="java.lang.Integer" parameterType="java.util.Map">
        <![CDATA[
		    select count(*)
		 from APP___PAS__DBUSER.t_policy_change t
		 where t.policy_id =
         (select ta.policy_id
          from APP___PAS__DBUSER.t_contract_master ta
         where ta.policy_code = #{policy_code})
		]]>
        <if test=" service_code_list  != null and service_code_list.size()!=0">
            <![CDATA[ AND t.service_code IN ]]>
            <foreach collection="service_code_list"
                     item="item" index="index" open="(" close=")" separator=",">#{item}
            </foreach>
        </if>
    </select>
    <!-- 查询所有操作 -->
    <select id="JRQD_findMouthEfficiency" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        select aa.apply_time, ab.review_time, aa.change_id, ab.organ_code, ab.accept_time
        from dev_pas.t_cs_application aa,
             (select a.review_time, a.change_id, a.update_time, a.organ_code, a.accept_time
              from dev_pas.t_cs_accept_change a
              where a.review_result is not null) ab
        where aa.change_id = ab.change_id
          AND ab.review_time >= to_date(#{system_date} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')
          and ab.review_time < to_date(#{system_date1} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')
          and ab.organ_code = #{organ_code}
          and ROWNUM <= 1000
        ]]>
	</select>

    <!-- 查询办结效率 -->
    <select id="JRQD_findDayCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        select count(*)
        from dev_pas.t_cs_application aa,
             (select a.review_time, a.change_id, a.update_time, a.organ_code, a.accept_time
              from dev_pas.t_cs_accept_change a
              where a.review_result is not null) ab
        where aa.change_id = ab.change_id
          AND ab.review_time >= to_date(#{system_date} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')
          and ab.review_time < to_date(#{system_date1} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')
          and ab.organ_code = #{organ_code}
        ]]>
	</select>
    <!-- 查询办结效率 -->
    <select id="JRQD_findDayCount1" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        select count(*)
        from (select distinct KK.organ_code,
                              kk.change_id,
                              KK.review_time,
                              KK.apply_time,
                              (case
                                   when (to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') between KK.apply_time and KK.review_time)
                                       then (KK.review_time -
                                             to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss')) +
                                            (to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') -
                                             KK.apply_time)
                                   else KK.review_time - KK.apply_time end) tdeal
              from (select ab.organ_code, ab.review_time, aa.apply_time, ab.change_id
                    from dev_pas.t_cs_application aa,
                         (select a.review_time, a.change_id, a.update_time, a.organ_code, a.accept_time
                          from dev_pas.t_cs_accept_change a
                          where a.review_result is not null
                            AND a.review_time >= to_date(#{system_date} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')
                            and a.review_time < to_date(#{system_date1} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')) ab
                    where aa.change_id = ab.change_id
                      and ab.organ_code = #{organ_code}) KK,
                   dev_pas.t_Udmp_Workdays AA) JJ
        where 1 = 1
          AND JJ.TDEAL < 1
          and 0 <= JJ.TDEAL
        ]]>
              
	</select>
    <!-- 查询办结效率 -->
    <select id="JRQD_findDayCount2" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        select count(*)
        from (select distinct KK.organ_code,
                              kk.change_id,
                              KK.review_time,
                              KK.apply_time,
                              (case
                                   when (to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') between KK.apply_time and KK.review_time)
                                       then (KK.review_time -
                                             to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss')) +
                                            (to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') -
                                             KK.apply_time)
                                   else KK.review_time - KK.apply_time end) tdeal
              from (select ab.organ_code, ab.review_time, aa.apply_time, ab.change_id
                    from dev_pas.t_cs_application aa,
                         (select a.review_time, a.change_id, a.update_time, a.organ_code, a.accept_time
                          from dev_pas.t_cs_accept_change a
                          where a.review_result is not null
                            AND a.review_time >= to_date(#{system_date} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')
                            and a.review_time < to_date(#{system_date1} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')) ab
                    where aa.change_id = ab.change_id
                      and ab.organ_code = #{organ_code}) KK,
                   dev_pas.t_Udmp_Workdays AA) JJ
        where 1 = 1
          AND JJ.TDEAL < 2
          and 1 <= JJ.TDEAL
        ]]>
              
	</select>

    <!-- 平均时率 -->
    <select id="JRQD_findAverage" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        select sum(TDEAL)
        from (select distinct KK.organ_code,
                              kk.change_id,
                              KK.review_time,
                              KK.apply_time,
                              (case
                                   when (to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') between KK.apply_time and KK.review_time)
                                       then (KK.review_time -
                                             to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss')) +
                                            (to_date(AA.Especial_Day || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') -
                                             KK.apply_time)
                                   else KK.review_time - KK.apply_time end) tdeal
              from (select ab.organ_code, ab.review_time, aa.apply_time, ab.change_id
                    from dev_pas.t_cs_application aa,
                         (select a.review_time, a.change_id, a.update_time, a.organ_code, a.accept_time
                          from dev_pas.t_cs_accept_change a
                          where a.review_result is not null
                            AND a.review_time >= to_date(#{system_date} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')
                            and a.review_time < to_date(#{system_date1} || ' 00:00:00', 'yyyy-mm hh24:mi:ss')) ab
                    where aa.change_id = ab.change_id
                      and ab.organ_code = #{organ_code}) KK,
                   dev_pas.t_Udmp_Workdays AA) JJ
        where 1 = 1
        ]]>
              
	</select>

    <!-- 查询保单贷款信息 -->
    <select id="JRQD_findCsAcceptChangeInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT CHANGE_ID, ACCEPT_ID, CUSTOMER_ID
        FROM (SELECT T.CHANGE_ID, T.ACCEPT_ID, T3.CUSTOMER_ID
              FROM DEV_PAS.T_CS_ACCEPT_CHANGE T,
                   DEV_PAS.T_CS_POLICY_CHANGE T2,
                   DEV_PAS.T_CS_APPLICATION T3
              WHERE T.SERVICE_CODE = 'LN'
                AND T.ACCEPT_STATUS = '18'
                AND T.ACCEPT_ID = T2.ACCEPT_ID
                AND T.CHANGE_ID = T2.CHANGE_ID
                AND T2.POLICY_CODE = #{policy_code}
                AND T3.CHANGE_ID = T.CHANGE_ID
              ORDER BY T.ACCEPT_TIME DESC) A
        WHERE ROWNUM = 1
        ]]>
	</select>
    <!-- 查询所有可返回受理的受理信息 -->
    <select id="JRQD_findCsAcceptChangeForAcceptBack" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE
			,A.SIGN_TYPE,DECODE(A.FACE_FLAG,'Y','1','N','0','0',FACE_FLAG) FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG
			 FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE 1=1 ]]>
        <![CDATA[
		   		and A.ACCEPT_STATUS IN ('05','06','07')
	   		]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>

    <!-- 查询单条受理信息 -->
    <select id="JRQD_PA_findCsAcceptChangeInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, 
			A.REVIEW_INPUT_RATE, A.REVIEW_RATE , A.BALANCE_FLAG,A.IS_SIGN_FLAG,A.IS_SAVED,A.CALL_PHONE1,A.CALL_PHONE2
			,A.SIGN_TYPE,A.FACE_FLAG,A.SKY_SIGN,DECODE(A.IDENTITY_FLAG,'Y','1','N','0',IDENTITY_FLAG) IDENTITY_FLAG,A.IS_IDENTITY_CHECK,
			A.ELEC_SIGN_FLAG,A.MAN_CHECK_INFO,A.LOBBY_MANAGER_RESULT,A.LOBBY_MANAGER_NAME,A.FACE_RECOGNITION_RESULT,A.AUTHENTICATION_RESULT,
			null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG
			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE 1=1 ]]>
        <if test="accept_id != null and accept_id != '' ">
            <![CDATA[ AND A.ACCEPT_ID = #{accept_id}  ]]>
        </if>
    </select>

    <!-- 信息是否可申请-->
    <select id="JRQD_PA_CsAcceptChangeIsApp" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
			SELECT * FROM   APP___PAS__DBUSER.t_cs_accept_change C LEFT JOIN (
				select A.ACCEPT_ID, A.SERVICE_CODE, NVL(A.FEE_AMOUNT,0),
					case 
					  WHEN A.service_code in ('CM' ,'GC', 'IT','GM','HI','PL','PF','LR' ,'RG', 'CP','CD','CF','CW')
					    OR (A.SERVICE_CODE IN ('CT', 'PG','XT','AG','LN','RL','PT','AI') 
					       AND (ABS(NVL(A.FEE_AMOUNT, 0)) >=0 AND ABS(NVL(A.FEE_AMOUNT, 0)) <30000 )) 
					    THEN 1 --中台复核
					  WHEN A.SERVICE_CODE in ('AE','PU'  ) 
					     OR (A.SERVICE_CODE IN ('CT','PG','XT','AG','LN','RL','PT', 'AI'
					     ) AND (ABS(NVL(A.FEE_AMOUNT, 0)) >=30000 AND ABS(NVL(A.FEE_AMOUNT, 0)) <999999990000 )) 
					    THEN 2 --集中复核
					  END RC
					from APP___PAS__DBUSER.t_cs_accept_change A
				) B ON B.ACCEPT_ID = C.ACCEPT_ID
				WHERE 1=1 AND B.RC=1
 			]]>
        <if test="accept_id != null and accept_id != '' ">
            <![CDATA[ AND C.ACCEPT_ID = #{accept_id}  ]]>
        </if>
        <!-- <if test="fee_amount != null and fee_amount != '' ">
                <![CDATA[ AND B.fee_amount = #{accept_id}  ]]>
        </if>
        <if test="accept_id != null and accept_id != '' ">
                <![CDATA[ AND C.ACCEPT_ID = #{accept_id}  ]]>
        </if> -->
    </select>

    <!-- 保全复核还是集中复核 1保全复核2集中复核-->
    <select id="JRQD_findCsFocueOrReview" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
			SELECT A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
			A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, 
			A.REVIEW_INPUT_RATE, A.REVIEW_RATE,
			null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG,
			CASE 
			  WHEN A.SERVICE_CODE IN ('AE','PU') THEN '2'
			  WHEN (
			        A.SERVICE_CODE IN ('CT','PG','XT','AG','LN','RL','PT', 'AI') 
			        AND (ABS(NVL(A.FEE_AMOUNT, 0)) >=30000 AND ABS(NVL(A.FEE_AMOUNT, 0)) <999999990000)
			       ) THEN '2'
			     ELSE '1'      
			   END FOCUS_REVIRE_FLAG
			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A 
			WHERE 1=1
 		]]>
        <if test="accept_id != null and accept_id != '' ">
            <![CDATA[ AND A.ACCEPT_ID = #{accept_id}  ]]>
        </if>
    </select>

    <!-- 保单是否处于贷款或者续贷中-->
    <select id="JRQD_findPolicyIsRLOrLN" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT A.IS_RENEW_PAYMENT,
               A.IS_REPAY_LOAN,
               A.ROLLBACK_CAUSE,
               A.ROLLBACK_NOTE,
               A.ROLLBACK_APP_TIME,
               A.ROLLBACK_APP_ID,
               A.ACCEPT_ID,
               A.RELATED_ID,
               A.UNCON_CAUSE,
               A.CHANGE_FLAG,
               A.CANCEL_TIME,
               A.ACCEPT_TIME,
               A.HESITATE_FLAG,
               A.FINISH_TIME,
               A.CANCEL_CAUSE,
               A.REVIEW_ID,
               A.ORGAN_CODE,
               A.REVIEW_TIME,
               A.VALIDATE_TIME,
               A.CHANGE_ID,
               A.PRE_FLAG,
               A.FEE_AMOUNT,
               A.SERVICE_CODE,
               A.INSERT_OPERATOR_ID,
               A.REVIEW_PROPERTY,
               A.CONVEN_FLAG,
               A.CANCEL_NOTE,
               A.REVIEW_VIEW,
               A.ACCEPT_CODE,
               A.REVIEW_RESULT,
               A.URGENT_DETAIL,
               A.PRE_VALIDATE_DATE,
               A.ORDER_ID,
               A.UNCON_TYPE,
               A.ACCEPT_STATUS,
               A.CANCEL_ID,
               A.BACK_ENTYR_CAUSE,
               A.URGENT_CAUSE,
               A.URGENT_FLAG,
               A.DELAY_CAUSE,
               A.REVIEW_ACCEPT_RATE,
               A.REVIEW_INPUT_RATE,
               A.REVIEW_RATE,
               null as NOT_ONLINE_POLICY,
               A.ABNORMAL_PAY_FLAG,
               A.POLICY_LOST_FLAG,
               A.POLICY_REISSUE_FLAG
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
        WHERE 1 = 1
          AND A.ACCEPT_STATUS <> '18'
          AND A.ACCEPT_STATUS <> '12'
          AND A.ACCEPT_ID IN (SELECT B.ACCEPT_ID
                              FROM APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
                              WHERE B.POLICY_CODE = #{policy_code}
                                AND B.SERVICE_CODE IN ('LN', 'RL'))
        ]]>
	</select>
    <!-- 保全受理号查询 -->
    <select id="JRQD_queryAcceptCodeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
        select A.POLICY_CODE, B.ACCEPT_CODE
        from APP___PAS__DBUSER.t_cs_policy_change A,
             APP___PAS__DBUSER.t_cs_accept_change B,
             APP___PAS__DBUSER.T_CS_APPLICATION C
        WHERE A.CHANGE_ID = B.CHANGE_ID
          AND B.CHANGE_ID = C.CHANGE_ID
          AND C.TRY_CALC_NO IS NULL
          AND A.Accept_Id = B.Accept_Id
          AND B.ACCEPT_STATUS NOT IN ('12', '19')
          AND A.Policy_Code = #{policy_code}
        ]]>
	</select>
    <!-- 交易密码是否重置 -->
    <select id="JRQD_queryPaPassWordIfReset" resultType="java.util.Map" parameterType="java.util.Map">
        select cac.service_code,
               to_char(cac.validate_time, 'yyyy-MM-dd') as ChangeDate,
               to_char(cac.validate_time, 'hh24-mi-SS') as ChangeTime
        from dev_pas.t_cs_accept_change cac
        where cac.service_code = 'FK'
          and cac.accept_status = '18'
          and exists(select ccp.customer_id
                     from dev_pas.t_cs_customer_password ccp
                     where ccp.customer_id = ${CUSTOMER_ID}

                       and ccp.set_cause = '2'
                       and ccp.change_id = cac.change_id)
        order by cac.validate_time desc

    </select>

    <!-- 查询付款人 -->
    <select id="JRQD_findCsPolicyChangeByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
        SELECT B.ACCOUNT_NAME, B.ACCOUNT, B.ACCOUNT_BANK
        from dev_pas.t_cs_policy_change A,
             dev_pas.t_payer_account B
        WHERE A.POLICY_ID = B.POLICY_ID
          AND A.ACCEPT_ID = #{accept_id}
        ]]>
	</select>

    <!-- 保全受理查询(新) -->
    <select id="JRQD_queryAcceptCodeNew" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT CA.APPLY_CODE,CAC.ACCEPT_CODE,CPC.POLICY_CODE,CAC.ACCEPT_STATUS,CPC.SERVICE_CODE,CA.APPLY_TIME,CAC.VALIDATE_TIME,CA.UPDATE_BY
   FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC, 
        APP___PAS__DBUSER.T_CS_APPLICATION CA,
        APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC
   WHERE CA.CHANGE_ID = CAC.CHANGE_ID
    AND CPC.ACCEPT_ID = CAC.ACCEPT_ID
    ]]>
        <include refid="JRQD_csAcceptCodenewQuery"/>
        <include refid="JRQD_PA_serviceCodeListCondition"/>
        <include refid="JRQD_PA_policyCodeListCondition"/>
    </select>


    <!-- 查询 是否做过保全回退 AM专用 -->
    <select id="JRQD_findAllCsAcceptChangeForAM" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, A.IS_SAVED,
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , A.BALANCE_FLAG,
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME,A.IS_SIGN_FLAG,A.SIGN_NO_LIST,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE, A.BILL_SEND_TYPE,
			null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG
			FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>

    <!-- 查询是否是批量退保已实付数据 -->
    <!-- <select id="JRQD_findCsAcceptChangeBS" resultType="java.util.Map" parameterType="java.util.Map">
            <![CDATA[
            SELECT AC.IS_ADD_DELAY,AC.ROLLBACK_CAUSE,AC.ROLLBACK_NOTE,AC.ROLLBACK_APP_TIME,AC.ROLLBACK_APP_ID,
            AC.RELATED_ID, AC.UNCON_CAUSE, AC.CHANGE_FLAG, AC.CANCEL_TIME, AC.ACCEPT_TIME, AC.HESITATE_FLAG,
            AC.FINISH_TIME, AC.CANCEL_CAUSE, AC.REVIEW_ID, AC.ORGAN_CODE, AC.REVIEW_TIME, AC.VALIDATE_TIME,
            AC.CHANGE_ID, AC.PRE_FLAG, AC.FEE_AMOUNT, AC.SERVICE_CODE, AC.ACCEPT_ID,  AC.INSERT_OPERATOR_ID,
            AC.REVIEW_PROPERTY, AC.CONVEN_FLAG, AC.CANCEL_NOTE, AC.REVIEW_VIEW, AC.ACCEPT_CODE,
            AC.REVIEW_RESULT, AC.URGENT_DETAIL, AC.PRE_VALIDATE_DATE, AC.ORDER_ID, AC.UNCON_TYPE, AC.ACCEPT_STATUS ,
            AC.CANCEL_ID, AC.BACK_ENTYR_CAUSE, AC.URGENT_CAUSE, AC.URGENT_FLAG, AC.DELAY_CAUSE ,AC.INSERT_BY,AC.REVIEW_ACCEPT_RATE, AC.REVIEW_INPUT_RATE, AC.REVIEW_RATE,
            AC.UPDATE_TIME,AC.UPDATE_BY,AC.is_saved,AC.is_sign_flag,AC.sign_no_list
              from dev_pas.t_cs_accept_change ac, dev_pas.t_batch_surrender_policy bsp
             where ac.change_id = bsp.change_id
                 and bsp.REAL_PAY_FLAG = '1'
               and ac.accept_code = #{accept_code}
            ]]>
    </select> -->

    <select id="JRQD_findAllCsAcceptChangeByYBT" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT AC.IS_RENEW_PAYMENT,
               AC.IS_ADD_DELAY,
               AC.ROLLBACK_CAUSE,
               AC.ROLLBACK_NOTE,
               AC.ROLLBACK_APP_TIME,
               AC.ROLLBACK_APP_ID,
               AC.RELATED_ID,
               AC.UNCON_CAUSE,
               AC.CHANGE_FLAG,
               AC.CANCEL_TIME,
               AC.ACCEPT_TIME,
               AC.HESITATE_FLAG,
               AC.FINISH_TIME,
               AC.CANCEL_CAUSE,
               AC.REVIEW_ID,
               AC.ORGAN_CODE,
               AC.REVIEW_TIME,
               AC.VALIDATE_TIME,
               AC.CHANGE_ID,
               AC.PRE_FLAG,
               AC.FEE_AMOUNT,
               AC.SERVICE_CODE,
               AC.ACCEPT_ID,
               AC.INSERT_OPERATOR_ID,
               AC.REVIEW_PROPERTY,
               AC.CONVEN_FLAG,
               AC.CANCEL_NOTE,
               AC.REVIEW_VIEW,
               AC.ACCEPT_CODE,
               AC.IS_SAVED,
               AC.REVIEW_RESULT,
               AC.URGENT_DETAIL,
               AC.PRE_VALIDATE_DATE,
               AC.ORDER_ID,
               AC.UNCON_TYPE,
               AC.ACCEPT_STATUS,
               AC.CANCEL_ID,
               AC.BACK_ENTYR_CAUSE,
               AC.URGENT_CAUSE,
               AC.URGENT_FLAG,
               AC.DELAY_CAUSE,
               AC.INSERT_BY,
               AC.INSERT_TIME,
               AC.REVIEW_ACCEPT_RATE,
               AC.REVIEW_INPUT_RATE,
               AC.REVIEW_RATE,
               AC.IS_SIGN_FLAG,
               AC.SIGN_NO_LIST,
               AC.BILL_SEND_TYPE,
               A.APPLY_TIME,
               A.SERVICE_TYPE,
               A.APPLY_NAME,
               AC.BALANCE_FLAG,
               PC.POLICY_CODE,
               null as NOT_ONLINE_POLICY,
               AC.ABNORMAL_PAY_FLAG,
               AC.POLICY_LOST_FLAG,
               AC.POLICY_REISSUE_FLAG
        FROM DEV_PAS.T_CS_APPLICATION A
                 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
                            ON AC.CHANGE_ID = A.CHANGE_ID
                 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
                            ON PC.ACCEPT_ID = AC.ACCEPT_ID
        WHERE 1 = 1
          AND A.APP_STATUS IN ('0', '1', '2', '3')
          AND A.BANK_CODE = '${bank_code}'
          AND AC.ACCEPT_STATUS IN ('03', '04', '05', '06', '07')
          AND A.APPLY_TIME >= TO_DATE(TO_CHAR(#{apply_time}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
          AND A.APPLY_TIME < TO_DATE(TO_CHAR(#{apply_date_add}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
          AND A.SERVICE_TYPE = '10'
          AND A.TRY_CALC_NO IS NULL
        ]]>
	</select>
    <!-- 查询所有操作(紧急件) -->
    <select id="JRQD_findAllCsAcceptChangeForUrgency" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.IS_RENEW_PAYMENT,A.IS_REPAY_LOAN,A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, A.IS_SAVED,A.IS_RENEW_PAYMENT,
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , A.BALANCE_FLAG,
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME,A.IS_SIGN_FLAG,A.SIGN_NO_LIST,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE, A.BILL_SEND_TYPE, A.LIAB_VALIDATE_TIME,null as NOT_ONLINE_POLICY,A.ABNORMAL_PAY_FLAG,A.POLICY_LOST_FLAG,A.POLICY_REISSUE_FLAG FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
        <include refid="JRQD_csAcceptChangeWhereConditionForUrgency"/>
        <![CDATA[ AND A.REVIEW_RESULT = '1' AND A.URGENT_FLAG = '1']]>
        <![CDATA[ ORDER BY A.ACCEPT_ID ]]>
    </select>

    <!-- 查询待录入的个数操作 -->
    <select id="JRQD_findCssWarningConfigTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
        <![CDATA[
			SELECT COUNT(A.ACCEPT_ID) FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A WHERE 1 = 1 AND A.ACCEPT_STATUS = '05' AND A.ORGAN_CODE = #{organ_code} 
		]]>
        <include refid="JRQD_findTotalWhereCondition"/>
    </select>


    <select id="JRQD_findAccountInfoForCheck" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT *
        FROM (SELECT MAX(E.SETTLE_DATE) AS BALANCE_DATE,
                     C.BUSI_PRD_ID,
                     D.ACCOUNT_CODE
              FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
                       LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
                                 ON A.ACCEPT_ID = B.ACCEPT_ID
                       LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD C
                                 ON C.POLICY_ID = B.POLICY_ID
                       INNER JOIN APP___PAS__DBUSER.T_CONTRACT_INVEST D
                                  ON C.BUSI_ITEM_ID = D.BUSI_ITEM_ID
                       LEFT JOIN APP___PAS__DBUSER.T_FUND_SETTLEMENT E
                                 ON D.LIST_ID = E.INVEST_ID
              WHERE 1 = 1
                AND A.ACCEPT_ID = #{accept_id}
              GROUP BY BUSI_PRD_ID, D.ACCOUNT_CODE) T
        WHERE 1 = 1
          AND BALANCE_DATE IS NOT NULL
        ]]>
	</select>

    <!-- 查询银保通保全状态    -->
    <select id="JRQD_findYbtAcceptStatus" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ select ac.validate_time, ac.accept_status, ac.accept_code,a.apply_time,ac.service_code
			from dev_pas.t_cs_application a inner join dev_pas.t_cs_accept_change ac on ac.change_id = a.change_id
			inner join dev_pas.t_cs_policy_change pc on pc.change_id = a.change_id where 1=1 and rownum=1 ]]>

        <if test="bank_code != null and bank_code != ''"> <![CDATA[ AND a.bank_code = #{bank_code} ]]> </if>
        <if test="bank_channel != null and bank_channel != ''"> <![CDATA[ AND a.bank_channel = #{bank_channel} ]]> </if>
        <if test="policy_code != null and policy_code != ''"> <![CDATA[ AND pc.policy_code = #{policy_code} ]]> </if>
        <if test="service_code != null and service_code != ''">
            <![CDATA[ AND ac.service_code = #{service_code} ]]> </if>
    </select>

    <!-- 根据受理ID查之前是否做过LR包含同一天    -->
    <select id="JRQD_PA_findIsHappenedLR" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
        SELECT (CASE WHEN COUNT(TPR.POLICY_REISSUE_ID) > 0 THEN '1' ELSE '0' END) AS IS_LR_POLICY
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC
                 LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC
                           ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
                 LEFT JOIN APP___PAS__DBUSER.T_POLICY_REISSUE TPR ON TCPC.POLICY_ID = TPR.POLICY_ID
                 LEFT JOIN APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC1 ON TCAC1.ACCEPT_CODE = TPR.ACCEPT_CODE
        WHERE TCAC.ACCEPT_ID = #{accept_id}
          AND TCAC.ACCEPT_TIME >= TCAC1.ACCEPT_TIME
          AND TCAC.ACCEPT_ID <> TCAC1.ACCEPT_ID
          AND TCAC.INSERT_TIME > TCAC1.INSERT_TIME
        ]]>
	</select>

    <!-- 复核页面查询客户信息列表 分页查询 -->
    <select id="JRQD_queryCsCustomersForPageTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        SELECT COUNT(1)
        FROM (SELECT TCPC.POLICY_CODE
                   , '投保人'                                                AS CUS_ROLE_FOR_POLICY
                   , NVL(TCC.CUSTOMER_NAME, TC.CUSTOMER_NAME)             AS CUSTOMER_NAME
                   , NVL(TCC.CUSTOMER_GENDER, TC.CUSTOMER_GENDER)         AS CUSTOMER_GENDER
                   , NVL(TCC.CUSTOMER_BIRTHDAY, TC.CUSTOMER_BIRTHDAY)     AS CUSTOMER_BIRTHDAY
                   , NVL(TCC.CUSTOMER_CERT_TYPE, TC.CUSTOMER_CERT_TYPE)   AS CUSTOMER_CERT_TYPE
                   , NVL(TCC.CUSTOMER_CERTI_CODE, TC.CUSTOMER_CERTI_CODE) AS CUSTOMER_CERTI_CODE
                   , NVL(TCC.CUSTOMER_RISK_LEVEL, TC.CUSTOMER_RISK_LEVEL) AS CUSTOMER_RISK_LEVEL
                   , '00'                                                 AS RELATION_TO_PH
                   , (SELECT STATUS_NAME
                      from DEV_PAS.T_LIABILITY_STATUS T
                      WHERE T.Status_Code = CM.LIABILITY_STATE)           as STATUS_NAME
                   , CM.LAPSE_CAUSE
                   , CM.END_CAUSE
              FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC
                       INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
                       LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM ON CM.POLICY_CODE = TCPC.POLICY_CODE
                       INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_HOLDER TCPH
                                  ON TCPH.POLICY_CHG_ID = TCPC.POLICY_CHG_ID AND TCPH.OLD_NEW = '0'
                       LEFT JOIN APP___PAS__DBUSER.T_CS_CUSTOMER TCC
                                 ON TCAC.ACCEPT_ID = TCC.ACCEPT_ID AND TCPH.OLD_NEW = TCC.OLD_NEW
                                     AND TCC.CHANGE_ID = TCPC.CHANGE_ID
                                     AND TCPH.CUSTOMER_ID = TCC.CUSTOMER_ID
                       LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TC ON TCPH.CUSTOMER_ID = TC.CUSTOMER_ID
              WHERE TCAC.ACCEPT_ID = #{accept_id}
              UNION ALL
              SELECT CPC.POLICY_CODE,
                     '被保人'                                       AS CUS_ROLE_FOR_POLICY,
                     TC.CUSTOMER_NAME                            AS CUSTOMER_NAME,
                     TC.CUSTOMER_GENDER                          AS CUSTOMER_GENDER,
                     TC.CUSTOMER_BIRTHDAY                        AS CUSTOMER_BIRTHDAY,
                     TC.CUSTOMER_CERT_TYPE                       AS CUSTOMER_CERT_TYPE,
                     TC.CUSTOMER_CERTI_CODE                      AS CUSTOMER_CERTI_CODE,
                     TC.CUSTOMER_RISK_LEVEL                      AS CUSTOMER_RISK_LEVEL,
                     CIL.RELATION_TO_PH,
                     (SELECT STATUS_NAME
                      from DEV_PAS.T_LIABILITY_STATUS T
                      WHERE T.Status_Code = CMA.LIABILITY_STATE) as STATUS_NAME,
                     CMA.LAPSE_CAUSE,
                     CMA.END_CAUSE
              FROM DEV_PAS.T_CS_ACCEPT_CHANGE CAC,
                   DEV_PAS.T_CS_POLICY_CHANGE CPC,
                   DEV_PAS.T_CONTRACT_MASTER CMA,
                   DEV_PAS.T_CS_INSURED_LIST CIL,
                   DEV_PAS.T_CUSTOMER TC,
                   DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
                   DEV_PAS.T_BENEFIT_INSURED BI
              WHERE CAC.ACCEPT_ID = CPC.ACCEPT_ID
                AND CPC.POLICY_CODE = CMA.POLICY_CODE
                AND CIL.POLICY_CHG_ID = CPC.POLICY_CHG_ID
                AND CIL.CUSTOMER_ID = TC.CUSTOMER_ID
                AND CBP.POLICY_ID = CPC.POLICY_ID
                AND CBP.POLICY_ID = BI.POLICY_ID
                AND CBP.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
                AND BI.ORDER_ID = '1'
                AND CBP.MASTER_BUSI_ITEM_ID IS NULL
                AND CAC.ACCEPT_ID = #{accept_id}
                AND CIL.OLD_NEW = '0') M
        ORDER BY M.POLICY_CODE
        ]]>
	</select>
    <!-- 复核页面查询客户信息列表 分页查询 -->
    <select id="JRQD_queryCsCustomersForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT N.RN AS ROWNUMBER,
               N.POLICY_CODE,
               N.CUS_ROLE_FOR_POLICY,
               N.CUSTOMER_NAME,
               N.CUSTOMER_GENDER,
               N.CUSTOMER_BIRTHDAY,
               N.CUSTOMER_CERT_TYPE,
               N.CUSTOMER_CERTI_CODE,
               N.CUSTOMER_RISK_LEVEL,
               N.RELATION_TO_PH,
               N.CUSTOMER_ID,
               N.STATUS_NAME,
               N.LAPSE_CAUSE,
               N.END_CAUSE
        FROM (SELECT ROWNUM RN,
                     M.POLICY_CODE,
                     M.CUS_ROLE_FOR_POLICY,
                     M.CUSTOMER_NAME,
                     M.CUSTOMER_GENDER,
                     M.CUSTOMER_BIRTHDAY,
                     M.CUSTOMER_CERT_TYPE,
                     M.CUSTOMER_CERTI_CODE,
                     M.CUSTOMER_RISK_LEVEL,
                     M.RELATION_TO_PH,
                     M.CUSTOMER_ID,
                     M.STATUS_NAME,
                     M.LAPSE_CAUSE,
                     M.END_CAUSE
              FROM (SELECT TCPC.POLICY_CODE
                         , '投保人'                                                AS CUS_ROLE_FOR_POLICY
                         , NVL(TCC.CUSTOMER_NAME, TC.CUSTOMER_NAME)             AS CUSTOMER_NAME
                         , NVL(TCC.CUSTOMER_ID, TC.CUSTOMER_ID)                 AS CUSTOMER_ID
                         , NVL(TCC.CUSTOMER_GENDER, TC.CUSTOMER_GENDER)         AS CUSTOMER_GENDER
                         , NVL(TCC.CUSTOMER_BIRTHDAY, TC.CUSTOMER_BIRTHDAY)     AS CUSTOMER_BIRTHDAY
                         , NVL(TCC.CUSTOMER_CERT_TYPE, TC.CUSTOMER_CERT_TYPE)   AS CUSTOMER_CERT_TYPE
                         , NVL(TCC.CUSTOMER_CERTI_CODE, TC.CUSTOMER_CERTI_CODE) AS CUSTOMER_CERTI_CODE
                         , NVL(TCC.CUSTOMER_RISK_LEVEL, TC.CUSTOMER_RISK_LEVEL) AS CUSTOMER_RISK_LEVEL
                         , '00'                                                 AS RELATION_TO_PH
                         , (SELECT STATUS_NAME
                            from DEV_PAS.T_LIABILITY_STATUS T
                            WHERE T.Status_Code = CM.LIABILITY_STATE)           as STATUS_NAME
                         , CM.LAPSE_CAUSE
                         , CM.END_CAUSE
                    FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE TCAC
                             INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE TCPC ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
                             LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER CM ON CM.POLICY_CODE = TCPC.POLICY_CODE
                             INNER JOIN APP___PAS__DBUSER.T_CS_POLICY_HOLDER TCPH
                                        ON TCPH.POLICY_CHG_ID = TCPC.POLICY_CHG_ID AND TCPH.OLD_NEW = '0'
                             LEFT JOIN APP___PAS__DBUSER.T_CS_CUSTOMER TCC
                                       ON TCAC.ACCEPT_ID = TCC.ACCEPT_ID AND TCPH.OLD_NEW = TCC.OLD_NEW
                                           AND TCC.CHANGE_ID = TCPC.CHANGE_ID
                                           AND TCPH.CUSTOMER_ID = TCC.CUSTOMER_ID
                             LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER TC ON TCPH.CUSTOMER_ID = TC.CUSTOMER_ID
                    WHERE TCAC.ACCEPT_ID = #{accept_id}
                    UNION ALL
                    SELECT CPC.POLICY_CODE,
                           '被保人'                                       AS CUS_ROLE_FOR_POLICY,
                           TC.CUSTOMER_NAME                            AS CUSTOMER_NAME,
                           TC.CUSTOMER_ID                              AS CUSTOMER_ID,
                           TC.CUSTOMER_GENDER                          AS CUSTOMER_GENDER,
                           TC.CUSTOMER_BIRTHDAY                        AS CUSTOMER_BIRTHDAY,
                           TC.CUSTOMER_CERT_TYPE                       AS CUSTOMER_CERT_TYPE,
                           TC.CUSTOMER_CERTI_CODE                      AS CUSTOMER_CERTI_CODE,
                           TC.CUSTOMER_RISK_LEVEL                      AS CUSTOMER_RISK_LEVEL,
                           CIL.RELATION_TO_PH,
                           (SELECT STATUS_NAME
                            from DEV_PAS.T_LIABILITY_STATUS T
                            WHERE T.Status_Code = CMA.LIABILITY_STATE) as STATUS_NAME,
                           CMA.LAPSE_CAUSE,
                           CMA.END_CAUSE
                    FROM DEV_PAS.T_CS_ACCEPT_CHANGE CAC,
                         DEV_PAS.T_CS_POLICY_CHANGE CPC,
                         DEV_PAS.T_CONTRACT_MASTER CMA,
                         DEV_PAS.T_CS_INSURED_LIST CIL,
                         DEV_PAS.T_CUSTOMER TC,
                         DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
                         DEV_PAS.T_BENEFIT_INSURED BI
                    WHERE CAC.ACCEPT_ID = CPC.ACCEPT_ID
                      AND CPC.POLICY_CODE = CMA.POLICY_CODE
                      AND CIL.POLICY_CHG_ID = CPC.POLICY_CHG_ID
                      AND CIL.CUSTOMER_ID = TC.CUSTOMER_ID
                      AND CBP.POLICY_ID = CPC.POLICY_ID
                      AND CBP.POLICY_ID = BI.POLICY_ID
                      AND CBP.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
                      AND BI.ORDER_ID = '1'
                      AND CBP.MASTER_BUSI_ITEM_ID IS NULL
                      AND CAC.ACCEPT_ID = #{accept_id}
                      AND CIL.OLD_NEW = '0') M
              WHERE ROWNUM <= #{LESS_NUM}]]>
		<![CDATA[ORDER BY M.POLICY_CODE ]]> 
		<![CDATA[ ) N
        WHERE N.RN > #{GREATER_NUM}
        ]]>
	</select>

    <!-- 录入页面初始化保单信息 -->
    <select id="JRQD_findAllMapLoadPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT B.POLICY_CODE,
               B.POLICY_ID,
               (SELECT CUS.CUSTOMER_NAME
                FROM APP___PAS__DBUSER.T_CUSTOMER CUS
                WHERE CUS.CUSTOMER_ID = D.CUSTOMER_ID)       POLICY_HOLDER/*投保人*/,
               C.PRODUCT_CODE,
               C.AMOUNT,
               C.STD_PREM_AF,
               CN.STD_PREM_AF                                STD_PREM_AF_OLD,
               DECODE(CN.STD_PREM_AF, C.STD_PREM_AF, 0, 1)   prem_flag/*保费变化标示*/,
               (SELECT LS.STATUS_NAME
                FROM APP___PAS__DBUSER.T_LIABILITY_STATUS LS
                WHERE LS.STATUS_CODE = MST.LIABILITY_STATE)  LIABILITY_STATE/*保单状态*/,
               (SELECT BSP.PRODUCT_CODE_SYS
                FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT BSP
                WHERE BSP.BUSINESS_PRD_ID = BUP.BUSI_PRD_ID) PRODUCT_CODE_SYS,
               (SELECT BSP.PRODUCT_NAME_SYS
                FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT BSP
                WHERE BSP.BUSINESS_PRD_ID = BUP.BUSI_PRD_ID) PRODUCT_NAME_SYS,
               BUP.BUSI_ITEM_ID,
               A.CHANGE_ID,
               bup.busi_prd_id,
               c.item_id,
               b.policy_chg_id,
               A.ACCEPT_ID,
               (SELECT LISTAGG(CUS.CUSTOMER_NAME, ',') WITHIN
        GROUP ( ORDER BY INS.CUSTOMER_ID)
        FROM APP___PAS__DBUSER.T_INSURED_LIST INS, APP___PAS__DBUSER.T_CUSTOMER CUS
        WHERE INS.CUSTOMER_ID = CUS.CUSTOMER_ID
          AND INS.POLICY_ID = B.POLICY_ID
            ) INSURED
            , /*被保人 多被保人,分割*/
            null as NOT_ONLINE_POLICY
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A
            JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
        ON A.ACCEPT_ID = B.ACCEPT_ID
            JOIN APP___PAS__DBUSER.T_CS_CONTRACT_MASTER MST
            ON B.POLICY_CHG_ID = MST.POLICY_CHG_ID
            AND MST.OLD_NEW = #{old_new}
            JOIN APP___PAS__DBUSER.T_CS_CONTRACT_BUSI_PROD BUP
            ON BUP.POLICY_CHG_ID=B.POLICY_CHG_ID
            AND BUP.POLICY_ID=MST.POLICY_ID
            AND BUP.OLD_NEW= #{old_new}
            JOIN APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT C
            ON C.POLICY_CHG_ID = B.POLICY_CHG_ID
            AND C.POLICY_ID = MST.POLICY_ID
            AND BUP.BUSI_ITEM_ID= C.BUSI_ITEM_ID
            AND C.OLD_NEW = #{old_new}
            JOIN APP___PAS__DBUSER.T_CS_CONTRACT_PRODUCT CN
            ON CN.POLICY_CHG_ID = B.POLICY_CHG_ID
            AND CN.POLICY_ID = MST.POLICY_ID
            AND BUP.BUSI_ITEM_ID= C.BUSI_ITEM_ID
            AND CN.ITEM_ID= C.ITEM_ID
            AND CN.OLD_NEW = '0'
            JOIN APP___PAS__DBUSER.T_CS_POLICY_HOLDER D
            ON B.POLICY_CHG_ID = D.POLICY_CHG_ID
            AND C.POLICY_ID = D.POLICY_ID
            AND D.OLD_NEW = #{old_new}
        WHERE A.ACCEPT_ID = #{accept_id}
        ]]>
		
	</select>

    <!-- 判断保单/險種是否在犹豫期 -->
    <select id="JRQD_find_isInHesitate_cs" resultType="java.util.Map"
            parameterType="java.util.Map">
        <!-- 犹豫期配置表T_FREE_LOOK_PERIOD_CFG已作废，去险种层犹豫期天数 -->
        <![CDATA[
      WITH POLICY AS
     (SELECT A.POLICY_ID,A.CHANNEL_TYPE,
             NVL(B.ACKNOWLEDGE_DATE, A.VALIDATE_DATE) ACKNOWLEDGE_DATE,A.VALIDATE_DATE
        FROM DEV_PAS.T_CONTRACT_MASTER A
        LEFT JOIN DEV_pAS.T_POLICY_ACKNOWLEDGEMENT  B
        ON A.POLICY_ID=B.POLICY_ID
        WHERE A.POLICY_ID=#{policy_id}
         )
    SELECT C.POLICY_ID,D.ACKNOWLEDGE_DATE,CASE WHEN D.ACKNOWLEDGE_DATE+NVL(C.HESITATION_PERIOD_DAY,10) >#{system_date} AND  
      D.ACKNOWLEDGE_DATE<=#{system_date} THEN '1'ELSE'0'END IS_INHESITATE,
      D.CHANNEL_TYPE
      FROM DEV_PAS.T_CONTRACT_BUSI_PROD C, POLICY D
     WHERE 1=1
       AND C.POLICY_ID = D.POLICY_ID]]>
        <if test=" busi_item_id  != null "><![CDATA[ AND C.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
        <if test=" busi_item_id  == null "><![CDATA[ AND C.MASTER_BUSI_ITEM_ID IS NULL ]]></if>

    </select>
    <!-- 查询保全验真信息 -->
    <select id="JRQD_cs_findAllCheckMain" resultType="java.util.Map" parameterType="java.util.Map">
        <!-- 犹豫期配置表T_FREE_LOOK_PERIOD_CFG已作废，去险种层犹豫期天数 -->
        <![CDATA[
      
	      SELECT A.BIZ_CODE,
         A.BIZ_CODE_TYPE,
         B.CHECK_NAME AS CUSTOMER_NAME,
         B.CHECK_ID_CODE AS CUSTOMER_ID_CODE,
         SERVICE_ORDER_ID,
         (select l.flag_desc from dev_pas.T_CS_IDENTITY_CHECK_FLAG l where l.flag_code = B.CHECK_TYPE )CHECK_TYPE,
          B.CHECK_TYPE CHECK_CODE ,
         B.IDENTITY_CHECK_ID,
         B.IDENTITY_DETAIL_ID,
         C.PHOTO_COMPARE_ID,
        (case when B.CHECK_TYPE = '2' then '注销' else
        (select LS.STATUS_NAME
           from dev_pas.T_LIVE_STATUS LS
          WHERE LS.STATUS_CODE = E.PERSON_LIVE_STATUS) end
         ) LIVE_STATUS, /*人口状态*/
         (case when B.CHECK_TYPE = '2' then '死亡' else E.LOGOUT_CAUSE end ) PERSON_LOGOUT_CAUSE,/*原因标识*/
         TO_CHAR(B.CHECK_DATE,'YYYY-MM-DD hh24:mi:ss') CHECK_DATE,/*验真时间*/
         (case when B.CHECK_TYPE = '2' then '不符合' else  DECODE(E.LOGOUT_STATE,'1','一致','0','不一致','') end ) LIVE_STATUS_CHECK_RESULT,/*人口状态认证结果*/
         (case when B.CHECK_TYPE = '2' then '不一致' else E.LOGOUT_CAUSE end ) AS PLC_CHECK_RESULT,/*原因标识认证结果（现在均为空值，页面无需显示）*/
         NVL(C.NAME_CHECK_RESULT,DECODE(E.LOGOUT_STATE,'1','一致','0','一致','一致')) CUSTOMER_NAME_CHECK_RESULT,/*姓名认证结果*/
         NVL(C.ID_CHECK_RESULT,DECODE(E.LOGOUT_STATE,'1','一致','0','一致','一致')) CUSTOMER_ID_CODE_CHECK_RESULT,/*身份证号码认证结果*/
         (case when B.CHECK_TYPE = '1' then  nvl(C.SIMILARITY,0)*100||'%' else '' end )SIMILARITY,/*相似度==80%*/
         (case when B.CHECK_TYPE = '1' then '同一人' else C.JUDGE_RESULT end ) JUDGE_RESULT, /*判断结果*/
         
         (case when B.CHECK_TYPE in('1','2','3') then '通过' else '' end ) faceResults, /*人脸识别结果*/
         (case when B.CHECK_TYPE in('1','2','3') then '通过' else '' end ) checkResults /*身份验真结果*/
         
         
        FROM DEV_PAS.T_ORDER_SERVICE A
       INNER JOIN DEV_PAS.T_IDENTITY_CHECK_MAIN B
          ON A.SERVICE_ORDER_ID || '' = B.BUSINESS_CODE
        LEFT JOIN DEV_PAS.T_PHOTO_COMPARE_CHECK C
          ON C.PHOTO_COMPARE_ID = B.IDENTITY_DETAIL_ID
        LEFT JOIN DEV_PAS.T_LOGOUT_PERSONNEL_CHECK E
          ON E.LOGOUT_ID=B.IDENTITY_DETAIL_ID
       WHERE 1 = 1
      		 AND A.BIZ_CODE = #{accept_code}
      ]]>


    </select>


    <!-- 查询保全验真信远程柜面 -->
    <select id="JRQD_cs_findAllCheckMainDT" resultType="java.util.Map" parameterType="java.util.Map">
  
    <![CDATA[
        SELECT B.CHECK_NAME,
               B.CUST_IDENTITY_TYPE,
               (SELECT D.TYPE_NAME FROM DEV_PAS.T_CHECK_IDENTITY_TYPE D WHERE D.TYPE_CODE = B.CHECK_TYPE) CHECK_TYPE,
               B.CHECK_TYPE                                                                               CHECK_CODE,
               B.IDENTITY_CHECK_ID,
               B.IDENTITY_DETAIL_ID,
               C.PHOTO_COMPARE_ID,
               B.CHECK_NAME    AS                                                                         CUSTOMER_NAME,
               B.CHECK_ID_CODE AS                                                                         CUSTOMER_ID_CODE,
               (select LS.STATUS_NAME
                from dev_pas.T_LIVE_STATUS LS
                WHERE LS.STATUS_CODE = E.PERSON_LIVE_STATUS)                                              LIVE_STATUS, /*人口状态*/
               E.LOGOUT_CAUSE                                                                             PERSON_LOGOUT_CAUSE,/*原因标识*/
               TO_CHAR(B.CHECK_DATE, 'yyyy-MM-dd HH24:mi:ss')                                             CHECK_DATE,/*验真时间*/
               DECODE(E.LOGOUT_STATE, '1', '一致', '0', '不一致', '')                                          LIVE_STATUS_CHECK_RESULT,/*人口状态认证结果*/
               E.LOGOUT_CAUSE  AS                                                                         PLC_CHECK_RESULT,/*原因标识认证结果（现在均为空值，页面无需显示）*/
               NVL(C.NAME_CHECK_RESULT,
                   DECODE(E.LOGOUT_STATE, '1', '一致', '0', '不一致', ''))                                     CUSTOMER_NAME_CHECK_RESULT,/*（远程柜面目前为空）姓名认证结果*/
               NVL(C.ID_CHECK_RESULT,
                   DECODE(E.LOGOUT_STATE, '1', '一致', '0', '不一致', ''))                                     CUSTOMER_ID_CODE_CHECK_RESULT,/*(远程柜面目前为空)身份证号码认证结果*/
               case
                   when C.similarity is not null then C.SIMILARITY * 100 || '%'
                   ELSE '' END as                                                                         SIMILARITY,/*相似度==80%*/
               C.JUDGE_RESULT/*判断结果*/
        FROM DEV_PAS.T_IDENTITY_CHECK_MAIN B

                 LEFT JOIN DEV_PAS.T_PHOTO_COMPARE_CHECK C
                           ON C.PHOTO_COMPARE_ID = B.IDENTITY_DETAIL_ID
                 LEFT JOIN DEV_PAS.T_LOGOUT_PERSONNEL_CHECK E
                           ON E.LOGOUT_ID = B.IDENTITY_DETAIL_ID
        WHERE 1 = 1
          AND B.Business_Code = #{accept_code}
        ]]>
       
       
  </select>


    <!-- 通过订单号查询受理信息 -->
    <select id="JRQD_findPreservationByOrderId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT B.ACCEPT_CODE,
               B.ACCEPT_STATUS,
               B.VALIDATE_TIME,
               B.REVIEW_VIEW,
               A.ORDER_TRANS_RESULT,
               A.ORDER_TRANS_RESULT_DESC,
               A.ORDER_STATUS,
               A.BIZ_CODE,
               B.ACCEPT_TIME,
               B.CANCEL_CAUSE,
               B.UPDATE_TIMESTAMP,
               B.CANCEL_NOTE
        FROM APP___PAS__DBUSER.T_ORDER_SERVICE A,
             APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE B
        WHERE A.BIZ_CODE = B.ACCEPT_CODE
          AND A.SERVICE_ORDER_ID = #{order_id, jdbcType=VARCHAR}
          AND ROWNUM = 1
        ]]>
		
  </select>

    <!-- 通过订单号查询受理信息 -->
    <select id="JRQD_queryOrdernumberByOrderId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT A.ORDER_TRANS_RESULT,
               A.ORDER_TRANS_RESULT_DESC,
               A.ORDER_STATUS,
               A.SERVICE_ORDER_ID,
               A.BIZ_CODE
        FROM APP___PAS__DBUSER.T_ORDER_SERVICE A
        WHERE A.SERVICE_ORDER_ID = #{order_id, jdbcType=VARCHAR}
          AND ROWNUM = 1
        ]]>
		
  </select>

    <!-- 通过受理号查询收付费信息调整信息 -->
    <select id="JRQD_queryPaymentAdjustmentByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT Z.REVIEW_RESULT, Z.UPDATE_TIME, Z.UNIT_NUMBER
        FROM (SELECT CPA.REVIEW_RESULT, CPA.UPDATE_TIME, CPA.UNIT_NUMBER
              FROM APP___PAS__DBUSER.T_CS_PAYMENT_ADJUST CPA
              WHERE UNIT_NUMBER IN (SELECT UNIT_NUMBER
                                    FROM APP___PAS__DBUSER.T_PREM_ARAP
                                    WHERE BUSINESS_CODE = #{accept_code})
              ORDER BY UPDATE_TIME DESC) Z
        WHERE ROWNUM = 1
        ]]>
		
  </select>

    <select id="JRQD_queryLoanCount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        select ac.accept_code
        from app___pas__dbuser.t_cs_policy_change pc
                 left join app___pas__dbuser.t_cs_accept_change ac
                           on pc.change_id = ac.change_id
                 left join app___pas__dbuser.t_policy_holder ph
                           on ph.policy_id = pc.policy_id
        where ac.service_code in ('LN', 'RL')
          and ac.accept_status = '18'
          and pc.service_code = ac.service_code
          and pc.apply_time > to_date(to_char(
                                              #{start_time,jdbcType=DATE}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
          and pc.apply_time <= to_date(to_char(
                                               #{end_time,jdbcType=DATE}, 'yyyy-MM-dd'), 'yyyy-MM-dd')
          and ph.customer_id = #{customer_id}
        ]]>
	</select>
    <!-- 根据受理号查询受理详细信息 -->
    <select id="JRQD_findCsAcceptChangePOByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
 	       <![CDATA[
        SELECT T.service_code, TN.CUSTOMER_ID, TG.POLICY_CODE, TN.ORGAN_CODE, T.Fee_Amount
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE T,
             APP___PAS__DBUSER.T_CS_APPLICATION TN,
             APP___PAS__DBUSER.T_CS_POLICY_CHANGE TG,
             APP___PAS__DBUSER.t_contract_agent tt
        WHERE T.ACCEPT_CODE = #{accept_code}
          AND T.CHANGE_ID = TN.CHANGE_ID
          AND TG.ACCEPT_ID = T.ACCEPT_ID
          and TG.policy_code = tt.policy_code
          and tt.agent_code = #{agent_code}
        ]]>
 	</select>

    <!-- 查询保全短信验证方式 -->
    <select id="JRQD_cs_findMsgCheck" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
        select distinct a.*
        from (select c.customer_name,
                     c.message_check_flag,
                     c.message_check_mobile,
                     c.message_check_date
              from dev_pas.t_order_service a
                       inner join dev_pas.t_identity_check_main b
                                  on a.service_order_id || '' = b.business_code
                       left join dev_pas.t_photo_compare_check c
                                 on c.photo_compare_id = b.identity_detail_id
              where 1 = 1
                and a.biz_code = #{accept_code}
                and c.message_check_flag is not null
              order by c.update_time desc) a
        ]]>
  	</select>

    <!-- 根据accept_id 修改受理状态操作-->
    <update id="JRQD_updateAcceptStatusByAcceptId" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                ACCEPT_STATUS = #{accept_status, jdbcType=VARCHAR} ,
                UPDATE_TIME = SYSDATE,
                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
                UPDATE_BY = #{update_by, jdbcType=NUMERIC}
            </trim>
        </set>
        <![CDATA[ WHERE ACCEPT_ID=#{accept_id} ]]>
    </update>

    <!-- 修改操作 -->
    <update id="JRQD_updateCsAcceptCancleNote" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                CANCEL_NOTE = #{cancel_note, jdbcType=VARCHAR} ,
                CANCEL_CAUSE = #{cancel_cause, jdbcType=VARCHAR} ,
                CANCEL_TIME = #{cancel_date, jdbcType=DATE} ,
                CANCEL_ID = #{cancel_id, jdbcType=NUMERIC} ,
                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
                UPDATE_TIME = SYSDATE ,
                UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
            </trim>
        </set>
        <![CDATA[ WHERE ACCEPT_ID=#{accept_id} ]]>
    </update>

    <update id="JRQD_updateCsAcceptChangeFeeAmount" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
                UPDATE_TIME = SYSDATE ,
                UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
            </trim>
        </set>
        <![CDATA[ WHERE ACCEPT_CODE=#{accept_code} ]]>
    </update>

    <update id="JRQD_updatecsAcceptchangeFeeAmount2" parameterType="java.util.Map">
		<![CDATA[
        update dev_pas.t_cs_accept_change t
        set t.fee_amount =
                (select abs(sum((case
                                     when t.arap_flag = 1 then
                                         t.fee_amount
                                     else
                                         -t.fee_amount
                    end)))
                 from dev_pas.t_cs_prem_arap t
                 where t.business_code in (#{accept_code}))
        where t.accept_code = #{accept_code}
        ]]>
	</update>

    <update id="JRQD_updateCsAcceptChangeFlag" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE ]]>
        <set>
            <trim suffixOverrides=",">
                <if test="conven_flag != null and conven_flag != ''">
                    CONVEN_FLAG = #{conven_flag, jdbcType=NUMERIC} ,
                </if>
                <if test="sign_type != null and sign_type != ''">
                    SIGN_TYPE = #{sign_type,jdbcType=VARCHAR},
                </if>
                <if test="face_flag != null and face_flag != ''">
                    FACE_FLAG = #{face_flag,jdbcType=VARCHAR},
                </if>
                <if test="sky_sign != null and sky_sign != ''">
                    SKY_SIGN = #{sky_sign,jdbcType=VARCHAR},
                </if>
                <if test="identity_flag != null and identity_flag != ''">
                    IDENTITY_FLAG = #{identity_flag,jdbcType=VARCHAR},
                </if>
                <if test="is_identity_check != null and is_identity_check != ''">
                    IS_IDENTITY_CHECK = #{is_identity_check,jdbcType=VARCHAR},
                </if>
                <!-- 受理机构 -->
                <if test=" organ_code != null and organ_code != ''  ">
                    ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
                </if>
                <!-- 人脸识别结果 -->
                <if test="face_recognition_result != null and face_recognition_result != ''">
                    FACE_RECOGNITION_RESULT = #{face_recognition_result,jdbcType=VARCHAR},
                </if>
                <!-- 身份验真结果  -->
                <if test="authentication_result != null and authentication_result != ''">
                    AUTHENTICATION_RESULT = #{authentication_result,jdbcType=VARCHAR},
                </if>
                <!-- 受理业务员代码 -->
                <if test=" accept_agent_code != null and accept_agent_code != ''  ">
                    ACCEPT_AGENT_CODE = #{accept_agent_code, jdbcType=VARCHAR},
                </if>
                <!--回访电话1 -->
                <if test=" call_phone1 != null and call_phone1 != ''  ">
                    CALL_PHONE1 = #{call_phone1, jdbcType=VARCHAR},
                </if>
                <!-- 客户是否同意标识 -->
                <if test="choose_flag != null and choose_flag != ''">
                    CHOOSE_FLAG=#{choose_flag, jdbcType=CHAR},
                </if>
                <!-- 客户信息使用授权声明 -->
                <if test="author_time != null and author_time != ''">
                    AUTHOR_TIME=#{author_time, jdbcType=DATE},
                </if>
                <!-- 客户授权声明阅读时间 -->
                <if test="sq_read_time != null and sq_read_time != ''">
                    SQ_READ_TIME=#{sq_read_time, jdbcType=TIMESTAMP},
                </if>
                <!-- 客户人脸确认标识和时间 -->
                <if test="face_confirm_flag != null and face_confirm_flag != ''">
                    FACE_CONFIRM_FLAG = #{face_confirm_flag,jdbcType=VARCHAR},
                </if>
                <!-- 大堂经理验证结果 -->
                <if test="lobby_manager_result != null and lobby_manager_result != ''">
                    LOBBY_MANAGER_RESULT = #{lobby_manager_result,jdbcType=VARCHAR},
                </if>
                <!-- 大堂经理的用户名 -->
                <if test="lobby_manager_name != null and lobby_manager_name != ''">
                    LOBBY_MANAGER_NAME = #{lobby_manager_name,jdbcType=VARCHAR},
                </if>
                <!-- 生存金和贷款本息和同时结算 -->
                <if test="balance_flag != null and balance_flag != ''">
                    BALANCE_FLAG = #{balance_flag, jdbcType=NUMERIC} ,
                </if>

                UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
                UPDATE_TIME = SYSDATE ,
                UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
            </trim>
        </set>
        <![CDATA[ WHERE ACCEPT_ID=#{accept_id, jdbcType=NUMERIC} ]]>
    </update>

    <!-- 查询贷款信息 -->
    <select id="JRQD_queryLNInfo" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
        select ac.accept_id, ac.change_id, pc.policy_code
        from dev_pas.t_cs_accept_change ac
                 left join dev_pas.t_cs_policy_change pc
                           on pc.change_id = ac.change_id
        where ac.service_code = 'LN'
          and ac.accept_code = #{accept_code}
        ]]>
    </select>

    <!--监护人信息远程柜面使用 -->
    <select id="JRQD_findGuardianInfoRemoteCounter" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
        select MAX(B.IDENTITY_DETAIL_ID) as IDENTITY_DETAIL_ID
        from DEV_PAS.T_IDENTITY_CHECK_MAIN B
        where b.business_code = #{accept_code}
        ]]>
</select>
    <select id="JRQD_findAllMapCsAcceptChangeForOutGreyChek" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
        SELECT (SELECT TC.CUSTOMER_NAME
                FROM APP___PAS__DBUSER.T_CUSTOMER TC
                WHERE TC.CUSTOMER_ID = PH.CUSTOMER_ID) AS CUSTOMER_NAME
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
             APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,
             APP___PAS__DBUSER.T_POLICY_HOLDER PH
        WHERE 1 = 1
          AND CAC.ACCEPT_ID = CPC.ACCEPT_ID
          AND CPC.POLICY_CODE = PH.POLICY_CODE
          AND CAC.ACCEPT_CODE = #{accept_code}
        UNION
        SELECT (SELECT TC.CUSTOMER_NAME
                FROM APP___PAS__DBUSER.T_CUSTOMER TC
                WHERE TC.CUSTOMER_ID = PH.CUSTOMER_ID) AS CUSTOMER_NAME

        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
             APP___PAS__DBUSER.T_CS_POLICY_CHANGE CPC,
             APP___PAS__DBUSER.T_INSURED_LIST PH
        WHERE 1 = 1
          AND CAC.ACCEPT_ID = CPC.ACCEPT_ID
          AND CPC.POLICY_CODE = PH.POLICY_CODE
          AND CAC.ACCEPT_CODE = #{accept_code}
        ]]>

</select>
    <select id="JRQD_findAllMapCsAcceptChangeMedicFlag" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
        SELECT D.MEDICA_FLAG, D.PRODUCT_CODE_SYS
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A,
             APP___PAS__DBUSER.T_CS_POLICY_CHANGE B,
             APP___PAS__DBUSER.T_PREM_ARAP C,
             APP___PAS__DBUSER.T_BUSINESS_PRODUCT D
        WHERE 1 = 1
          AND A.ACCEPT_ID = B.ACCEPT_ID
          AND A.ACCEPT_CODE = C.BUSINESS_CODE
          AND B.POLICY_CODE = C.POLICY_CODE
          AND C.BUSI_PROD_CODE = D.PRODUCT_CODE_SYS
          AND A.SERVICE_CODE IN ('CT', 'EA', 'XT')
          AND A.ACCEPT_CODE = #{accept_code}
        ]]>

</select>

    <select id="JRQD_cs_findAllCsAcceptChangeUpload" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT A.SERVICE_CODE, A.ACCEPT_TIME
        FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A,
             APP___PAS__DBUSER.T_CS_POLICY_CHANGE B
        WHERE 1 = 1
          AND A.ACCEPT_ID = B.ACCEPT_ID
          AND B.POLICY_CODE = #{policy_code}
        ]]>
    </select>

    <select id="JRQD_cs_findRRBeforeBusiValidate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        select c.validate_date valitime
        from dev_pas.t_contract_busi_prod c
        where c.busi_prod_code in ('00958100', '00563100', '00563000', '00958000')
          and c.policy_code = #{policy_code}
        ]]>
    </select>

    <select id="JRQD_findFrozenInit" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        select cac.accept_status, cac.insert_by, ca.service_type
        from dev_pas.t_cs_accept_change cac,
             dev_pas.t_cs_application ca
        where ca.change_id = cac.change_id
          and cac.accept_code = #{accept_code}
        ]]>
    </select>
    <select id="JRQD_findCsAcceptChangeByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
			select cac.accept_code
			  from dev_pas.t_cs_accept_change cac, dev_pas.t_cs_policy_change cpc
			 where cpc.policy_chg_id = #{policy_chg_id}
			   and cpc.accept_id = cac.accept_id
		]]>
        <!-- 根据change_id,查询申请下操作快捷基本资料变更的的受理信息 -->
    </select>
    <select id="JRQD_findIsCusBaseChgAcceptChangeByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
			SELECT *
				  FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE A LEFT JOIN APP___PAS__DBUSER.T_CS_POLICY_CHANGE PC ON A.ACCEPT_ID = PC.ACCEPT_ID
				 WHERE 1 = 1 
				   AND A.IS_CUS_BASE_CHG = '1'
				   AND A.ACCEPT_STATUS = '18'
		]]>
        <if test="policy_code != null and policy_code != ''"><![CDATA[ and pc.policy_code = #{policy_code} ]]></if>
        <include refid="JRQD_csAcceptChangeWhereCondition"/>
        <![CDATA[ ORDER BY A.ACCEPT_ID DESC]]>
    </select>

    <select id="JRQD_findNSChgAcceptChangeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT AC.ACCEPT_ID,
               AC.ACCEPT_CODE,
               AC.CHANGE_ID,
               PC.POLICY_CHG_ID,
               PC.POLICY_ID,
               A.SERVICE_TYPE
        FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
                 LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
                           ON AC.ACCEPT_ID = PC.ACCEPT_ID
                               AND AC.CHANGE_ID = PC.CHANGE_ID
                 LEFT JOIN DEV_PAS.T_CS_APPLICATION A
                           ON A.CHANGE_ID = PC.CHANGE_ID
        WHERE AC.SERVICE_CODE = 'NS'
          AND AC.ACCEPT_STATUS = '06'
          AND PC.POLICY_CODE = #{policy_code}
        ]]>
    </select>


    <!--       <select id="JRQD_findJSChangeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
            <![CDATA[
                SELECT AC.ACCEPT_ID,AC.ACCEPT_CODE,AC.CHANGE_ID,
                PC.POLICY_CHG_ID,PC.POLICY_ID,A.SERVICE_TYPE,
                   PC.POLICY_CODE
                FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
                LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE PC
                ON AC.ACCEPT_ID = PC.ACCEPT_ID
                AND AC.CHANGE_ID = PC.CHANGE_ID
                LEFT JOIN DEV_PAS.T_CS_APPLICATION A
                ON A.CHANGE_ID = PC.CHANGE_ID
                WHERE AC.SERVICE_CODE = 'NS'
                AND AC.ACCEPT_STATUS = '06'
                AND PC.POLICY_CODE = #{policy_code}
            ]]>
        </select> -->


    <select id="JRQD_findBusiProdbyContNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        select cbp.busi_prod_code
        from dev_pas.t_cs_accept_change ac
                 left join dev_pas.t_cs_policy_change pc
                           on ac.accept_id = pc.accept_id
                 left join dev_pas.t_cs_contract_busi_prod cbp
                           on cbp.policy_chg_id = pc.policy_chg_id
        where ac.service_code in ('CT', 'XT')
          and ac.accept_status = '18'
          and cbp.liability_state = '3'
          and pc.policy_code = #{policy_code}
        ]]>
    </select>

</mapper>
