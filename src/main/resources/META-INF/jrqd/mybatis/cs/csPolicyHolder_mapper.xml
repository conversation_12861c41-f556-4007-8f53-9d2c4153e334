<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.object.dao.ICsPolicyHolderDao">

	<sql id="JRQD_csPolicyHolderWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" operation_type != null and operation_type != ''  "><![CDATA[ AND A.OPERATION_TYPE = #{operation_type} ]]></if>
		<if test=" JOB_UNDERWRITE != null and JOB_UNDERWRITE != ''  "><![CDATA[ AND A.JOB_UNDERWRITE = #{JOB_UNDERWRITE} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test="policy_change_list != null and policy_change_list.size()!=0">
		<![CDATA[ AND A.POLICY_CHG_ID IN (]]>
		<foreach collection="policy_change_list" item="policyChaId"
			index="index" open="" close="" separator=",">#{policyChaId}</foreach>
		<![CDATA[)]]>
	    </if>
	    <!-- #108297投保人表同步增加字段 -->
<!-- 	    <if test=" agent_relation != null and agent_relation != ''  "><![CDATA[ AND A.AGENT_RELATION = #{agent_relation} ]]></if> -->
	     <!-- #107460重点帮扶县费率浮动增加字段 -->
	    <if test=" applicant_spe_people != null and applicant_spe_people != ''  "><![CDATA[ AND A.APPLICANT_SPE_PEOPLE = #{applicant_spe_people} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCsPolicyHolderByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsPolicyHolderByOldNewCondition">
		<if test=" old_new != null and old_new != '' "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
	</sql>	
	<sql id="JRQD_queryCsPolicyHolderByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addCsPolicyHolder"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="log_id">
			SELECT APP___PAS__DBUSER.S_CS_POLICY_HOLDER.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_CS_POLICY_HOLDER( ANNUAL_INCOME_CEIL,INCOME_SOURCE,RESIDENT_TYPE,SOCI_SECU,SMOKING,
				ADDRESS_ID, INSERT_TIME, OPERATION_TYPE, JOB_UNDERWRITE, CUSTOMER_HEIGHT, CUSTOMER_ID, UPDATE_TIME, 
				JOB_CODE, CUSTOMER_WEIGHT, APPLY_CODE, INSERT_TIMESTAMP, LOG_ID, OLD_NEW, POLICY_CODE, 
				UPDATE_BY, CHANGE_ID, LIST_ID, UPDATE_TIMESTAMP, POLICY_CHG_ID, INSERT_BY, POLICY_ID, AGENT_RELATION,APPLICANT_SPE_PEOPLE ) 
			VALUES (#{annual_income_ceil, jdbcType=NUMERIC}, #{income_source, jdbcType=VARCHAR}, #{resident_type, jdbcType=VARCHAR}, #{soci_secu, jdbcType=NUMERIC},#{smoking, jdbcType=NUMERIC},
				#{address_id, jdbcType=NUMERIC}, SYSDATE , #{operation_type, jdbcType=VARCHAR} , #{JOB_UNDERWRITE, jdbcType=VARCHAR} , #{customer_height, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} , SYSDATE 
				, #{job_code, jdbcType=VARCHAR} , #{customer_weight, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{log_id,jdbcType=NUMERIC} , #{old_new, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_chg_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}
				,#{agent_relation, jdbcType=VARCHAR}
				,#{applicant_spe_people, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsPolicyHolderByPolicyChgId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE POLICY_CHG_ID = #{policy_chg_id} ]]>
	</delete>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCsPolicyHolder" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE 1=1 ]]>
		<include refid="JRQD_csPolicyHolderWhereCondition" />
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateCsPolicyHolder" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_CS_POLICY_HOLDER ]]>
		<set>
		<trim suffixOverrides=",">
		    ANNUAL_INCOME_CEIL = #{annual_income_ceil, jdbcType=NUMERIC},
			INCOME_SOURCE = #{income_source, jdbcType=VARCHAR},
			RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR},
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC},
		    SMOKING = #{smoking, jdbcType=NUMERIC},
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
			OPERATION_TYPE = #{operation_type, jdbcType=VARCHAR} ,
			JOB_UNDERWRITE = #{JOB_UNDERWRITE, jdbcType=VARCHAR} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			OLD_NEW = #{old_new, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    COMM_METHOD=#{comm_method, jdbcType=VARCHAR},
		    AGENT_RELATION = #{agent_relation, jdbcType=VARCHAR},
		    APPLICANT_SPE_PEOPLE = #{applicant_spe_people, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LOG_ID = #{log_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findCsPolicyHolderByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,
			A.SOCI_SECU,A.SMOKING,A.ADDRESS_ID, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, 
			A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, '1' AS AGENT_RELATION,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_queryCsPolicyHolderByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsPolicyHolderByOldNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,
			A.SOCI_SECU,A.SMOKING,A.ADDRESS_ID, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, 
			A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, '1' AS AGENT_RELATION,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_queryCsPolicyHolderByOldNewCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	
	<select id="JRQD_findCsPolicyHolderByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,
			A.SOCI_SECU,A.SMOKING,A.ADDRESS_ID, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, 
			A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, '1' AS AGENT_RELATION ,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE ROWNUM <=  1  ]]>
		<include refid="JRQD_queryCsPolicyHolderByLogIdCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	<!-- 查询一个的 朱丽华 -->
	<select id="JRQD_findCsPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,
			A.SOCI_SECU,A.SMOKING,A.ADDRESS_ID, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, 
			A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, '1' AS AGENT_RELATION , A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csPolicyHolderWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCsPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,
			A.SOCI_SECU,A.SMOKING,A.ADDRESS_ID, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, 
			A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, '1' AS AGENT_RELATION,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCsPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,
			A.SOCI_SECU,A.SMOKING,A.ADDRESS_ID, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, 
			A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID,a.comm_method, '1' AS AGENT_RELATION,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_csPolicyHolderWhereCondition" /> 
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
<!-- 查询个数操作 -->
	<select id="JRQD_findCsPolicyHolderTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<include refid="JRQD_csPolicyHolderWhereCondition"/>
	</select>


<!-- 分页查询操作 -->
	<select id="JRQD_queryCsPolicyHolderForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.ANNUAL_INCOME_CEIL,B.INCOME_SOURCE,B.RESIDENT_TYPE,
			B.SOCI_SECU,B.SMOKING,B.RN AS rowNumber, B.ADDRESS_ID, B.OPERATION_TYPE, B.JOB_UNDERWRITE, B.CUSTOMER_HEIGHT, B.CUSTOMER_ID, 
			B.JOB_CODE, B.CUSTOMER_WEIGHT, B.APPLY_CODE, B.LOG_ID, B.OLD_NEW, B.POLICY_CODE, 
			B.CHANGE_ID, B.LIST_ID, B.POLICY_CHG_ID, B.POLICY_ID, B.AGENT_RELATION,B.APPLICANT_SPE_PEOPLE FROM (
					SELECT ROWNUM RN, 0 AS ANNUAL_INCOME_CEIL,'1' AS INCOME_SOURCE,'1' AS RESIDENT_TYPE,
			A.SOCI_SECU,A.SMOKING,A.ADDRESS_ID, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, 
			A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, '1' AS AGENT_RELATION,A.APPLICANT_SPE_PEOPLE FROM APP___PAS__DBUSER.T_CS_POLICY_HOLDER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
		<![CDATA[ )  B 
			WHERE B.RN > #{GREATER_NUM} ]]> 
	</select>
	<!-- modify by lixf 查询效率过低且部分字段查询出错 20160901  modify by wangsy 投保人年龄需要的不是投保人申请时的年龄，而是其投保时的年龄-->
	<!-- 查询抄单投保人客户信息 -->
	<select id="JRQD_findCsPolicyHolderCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		select distinct c.customer_id ,
				c.un_customer_code,
				c.customer_name,
				to_char(c.customer_gender) customer_gender,
				floor(months_between(
				]]>
				<if test="policy_chg_id  != null and old_new  != null and old_new  != ''">
				<![CDATA[ (select apply_date from APP___PAS__DBUSER.t_cs_contract_master where policy_chg_id=#{policy_chg_id} and old_new = #{old_new})]]></if>
				<if test="policy_chg_id  == null"><![CDATA[ #{apply_time} ]]></if>
				, c.customer_birthday) / 12) customer_age,
				c.customer_birthday,
				c.customer_cert_type,
				c.customer_certi_code,
				c.customer_height,
				c.customer_weight,
				c.job_code,
				(select jc.job_name from APP___PAS__DBUSER.T_job_code jc where jc.job_code = c.job_code) job_name,
				(select jc.job_uw_level from APP___PAS__DBUSER.T_job_code jc where jc.job_code = c.job_code) as occupation_type,
				c.annual_income,
				<if test="steins_flag  == null or steins_flag  == ''"><![CDATA[
				(select max(count(1)) from APP___PAS__DBUSER.T_cs_address t1,APP___PAS__DBUSER.T_cs_address t2
				where t1.customer_id=c.customer_id
				and t1.change_id=c.change_id and t1.address_status='1'
				and t1.country_code=t2.country_code
				and t1.city=t2.city and t1.district=t2.district
				and t1.address=t2.address 
				and t1.customer_id<>t2.customer_id and t2.address_status='1'
				group by t2.customer_id,t2.country_code,t2.city,t2.district,t2.address
				) address_count,     
				(select count(distinct cc.customer_id) from APP___PAS__DBUSER.T_CS_CUSTOMER cc where cc.offen_use_tel = c.offen_use_tel and cc.customer_id <> c.customer_id) mobile_count,]]>
				</if>
				decode(cca.agent_mobile,null,'false',c.offen_use_tel,'true','false') as mobile_same,
				c.company_name as company_name
        		from APP___PAS__DBUSER.T_CS_POLICY_HOLDER h ,APP___PAS__DBUSER.T_CS_CUSTOMER c,APP___PAS__DBUSER.T_cs_contract_agent cca
        		where h.customer_id = c.customer_id 
		        and h.old_new = c.old_new 
		        and h.change_id = c.change_id
		        and cca.policy_chg_id(+)=h.policy_chg_id
		        and cca.policy_code(+)=h.policy_code
		        and cca.policy_id(+)=h.policy_id
		        and cca.change_id(+)=h.change_id 
   				and ROWNUM = 1
		 <if test="change_id  != null"><![CDATA[ and h.change_id = #{change_id} ]]></if>
		 <if test="policy_chg_id  != null"><![CDATA[ and h.policy_chg_id = #{policy_chg_id} ]]></if>
		  <if test="accept_id != null"><![CDATA[ and c.accept_id = #{accept_id} ]]></if>
		 <if test="old_new  != null and old_new  != ''"><![CDATA[ and h.old_new = #{old_new} ]]></if>
		 
	</select>
	<!-- modify by lixf 查询效率过低且部分字段查询出错 20160901-->
	<!-- 查询保单投保人客户信息 -->
	<select id="JRQD_findPolicyHolderCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		select  distinct 
				c.customer_id ,
				c.un_customer_code,
				c.customer_name,
				to_char(c.customer_gender) customer_gender,
				
				floor(months_between( (select apply_date from APP___PAS__DBUSER.t_contract_master where policy_id=#{policy_id})
				, c.customer_birthday) / 12) customer_age,
				
				c.customer_birthday,
				c.customer_cert_type,
				c.customer_certi_code,
				c.customer_height,
				c.customer_weight,
		        c.job_code,
		        
		        (select jc.job_name from APP___PAS__DBUSER.T_job_code jc where jc.job_code = c.job_code) job_name,
		        (select jc.job_uw_level from APP___PAS__DBUSER.T_job_code jc where jc.job_code = c.job_code) as occupation_type,
		        
		        c.annual_income
		        /*,	
		        (select max(count(1)) from APP___PAS__DBUSER.T_address t1,APP___PAS__DBUSER.T_address t2
			        where t1.customer_id=c.customer_id
			        and t1.address_status='1'
			        and t1.country_code=t2.country_code
			        and t1.city=t2.city and t1.district=t2.district
			        and t1.address=t2.address 
			        and t1.customer_id<>t2.customer_id and t2.address_status='1'
			        group by t2.customer_id,t2.country_code,t2.city,t2.district,t2.address
		        )*/ address_count   
		        
		        from (select *
	                  from APP___PAS__DBUSER.T_POLICY_HOLDER
	                 where policy_id = #{policy_id}) h ,
		        	 APP___PAS__DBUSER.T_CUSTOMER c		        	 
		        where h.customer_id = c.customer_id 
		 ]]>
		 
		
	</select>
	
	<!--by zhaoyoan_wb 通过保单号查询对应投保人姓名 (customer_id有用)-->
	<select id="JRQD_findApplyNameByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select a.customer_id,
			(select customer_name from APP___PAS__DBUSER.T_CS_CUSTOMER 
				where customer_id=a.customer_id and rownum=1) as customer_name 
			from APP___PAS__DBUSER.T_CS_POLICY_HOLDER a where policy_code=#{policy_code} 
			and rownum=1
		]]>
	</select>
	<!--by xiayt_wb 判断投保人和被保人是否为同一人-->
	<select id="JRQD_CS_findHoderInsureList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   SELECT  A.POLICY_CODE,A.CUSTOMER_ID,A.POLICY_ID FROM DEV_PAS.T_POLICY_HOLDER A ,DEV_PAS.T_INSURED_LIST B WHERE
		   A.CUSTOMER_ID=B.CUSTOMER_ID AND A.POLICY_CODE=B.POLICY_CODE  AND A.POLICY_CODE=#{policy_code}
		]]>
	</select>
	
	<select id="JRQD_findPolicyIdByCustomerNameAndPolicyId_cjk" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	select B.CUSTOMER_ID,A.POLICY_ID,B.CUSTOMER_NAME from  APP___PAS__DBUSER.T_POLICY_HOLDER A,APP___PAS__DBUSER.t_customer B,APP___PAS__DBUSER.t_contract_master C
 
 WHERE A.POLICY_ID=C.POLICY_ID AND B.CUSTOMER_ID=A.CUSTOMER_ID
  ]]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>
	<if test=" name != null and name != ''  "><![CDATA[ AND B.CUSTOMER_NAME = #{name} ]]></if>
	</select>
	
    <!--查询需要同步的投保人变更手机号码 -->
	<select id="JRQD_queryChangeHolderMobile" resultType="java.util.Map"	parameterType="java.util.Map">
		<![CDATA[ 
		select  *  from (
	        
		          select        c.accept_id,
		                        c.accept_code,
		                        c.service_code,
		                        c.validate_time,
		                        pc.policy_chg_id,
		                        pc.policy_code,
		                        (SELECT PH.CUSTOMER_ID
		                           FROM DEV_pAS.t_Cs_Policy_Holder PH
		                          WHERE PH.POLICY_CHG_ID = PC.POLICY_CHG_ID
		                            AND PH.OLD_NEW = '1') as holder_customer_id, /*投保人客户号*/
		                        
		                        (select cus.customer_name
		                           from dev_pas.t_customer cus
		                           LEFT JOIN DEV_pAS.t_Cs_Policy_Holder PH
		                             ON cus.customer_id = ph.customer_id
		                            AND PH.OLD_NEW = '1'
		                            WHERE PH.POLICY_CHG_ID = PC.POLICY_CHG_ID) holder_name, /*投保人姓名*/
		                        
		                        max((select ca.mobile_tel
		                              from dev_pas.t_cs_address ca
		                              LEFT JOIN DEV_pAS.t_Cs_Policy_Holder PH
		                                ON ph.address_id = ca.address_id
		                               and ca.old_new = '0'
		                               and ca.operation_type = '0'
		                               and ph.old_new = '0'
		                               WHERE PH.POLICY_CHG_ID = PC.POLICY_CHG_ID and ca.accept_id = c.accept_id)) as before_mobile, /*变更前电话*/
		                        max((CASE
		                              WHEN C.SERVICE_CODE = 'AE' THEN
		                               (select ca.mobile_tel
		                                  from dev_pas.t_address ca
		                                  LEFT JOIN DEV_pAS.t_Cs_Policy_Holder PH
		                                  on ph.address_id = ca.address_id
		                                  and ph.old_new = '1'
		                                  where ph.policy_chg_id = pc.policy_chg_id
		                                   )
		                              ELSE
		                               (select ca.mobile_tel
		                                  from dev_pas.t_cs_address ca
		                                  LEFT JOIN DEV_pAS.t_Cs_Policy_Holder PH
		                                    ON ph.address_id = ca.address_id
		                                   and ca.old_new = '1'
		                                   and ca.operation_type = '2'
		                                   and PH.OLD_NEW = '1'
		                                   WHERE PH.POLICY_CHG_ID = pc.policy_chg_id  and ca.accept_id = c.accept_id)
		                            END)) as after_mobile /*变更后电话*/
		         from dev_pas.t_cs_accept_change c
		         LEFT join dev_pas.t_cs_application capp
		           on c.change_id = capp.change_id
		         LEFT join dev_pas.t_cs_policy_change pc
		           on c.accept_id = pc.accept_id
		        where c.service_code in ('CC', 'AE')
		          and c.accept_status = '18'
	              and c.validate_time between #{start_date} and #{end_date}
	              group by c.accept_id,
	                         c.accept_code,
	                         c.service_code,
	                         c.validate_time,
	                         pc.policy_chg_id,
	                         pc.policy_code
	               )  where before_mobile  != after_mobile	  ]]>
	</select>
	
	<select id="JRQD_findCsPolicyHolderByChangeIdAcceptCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT P.* FROM DEV_PAS.T_CS_POLICY_CHANGE C ,DEV_PAS.T_CS_POLICY_Holder P
				   WHERE C.CHANGE_ID = P.CHANGE_ID AND C.POLICY_CHG_ID = P.POLICY_CHG_ID ]]>
	    <if test=" accept_id  != null "><![CDATA[ AND C.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" address_id  != null "><![CDATA[ AND P.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND P.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND P.CHANGE_ID = #{change_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND P.OLD_NEW = #{old_new} ]]></if>
	</select>
	<!-- 查询个数操作 -->
	<select id="JRQD_findCsPolicyAccountTotalByCustomeIdHolder" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select count(1) from  APP___PAS__DBUSER.T_POLICY_ACCOUNT a, dev_pas.t_POLICY_HOLDER b
                  where a.policy_id=b.policy_id and a.account_type='2'  
                  AND b.CUSTOMER_ID =#{customer_id} and b.POLICY_ID =#{policy_id} ]]>
		
	</select>
	
</mapper>
