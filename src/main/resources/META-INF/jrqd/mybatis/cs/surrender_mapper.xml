<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.dao.ISurrenderDao">

	<sql id="JRQD_PA_surrenderWhereCondition">
		<if test=" surrender_type != null and surrender_type != ''  "><![CDATA[ AND A.SURRENDER_TYPE = #{surrender_type} ]]></if>
		<if test=" surrender_cause != null and surrender_cause != ''  "><![CDATA[ AND A.SURRENDER_CAUSE = #{surrender_cause} ]]></if>
		<if test=" surrender_amount  != null "><![CDATA[ AND A.SURRENDER_AMOUNT = #{surrender_amount} ]]></if>
		<if test=" other_adj_amount  != null "><![CDATA[ AND A.OTHER_ADJ_AMOUNT = #{other_adj_amount} ]]></if>
		<if test=" adjust_bonus_amount  != null "><![CDATA[ AND A.ADJUST_BONUS_AMOUNT = #{adjust_bonus_amount} ]]></if>
		<if test=" adjust_end_bonus  != null "><![CDATA[ AND A.ADJUST_END_BONUS = #{adjust_end_bonus} ]]></if>
		<if test=" adjust_fee_amount  != null "><![CDATA[ AND A.ADJUST_FEE_AMOUNT = #{adjust_fee_amount} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" hesitate_flag  != null "><![CDATA[ AND A.HESITATE_FLAG = #{hesitate_flag} ]]></if>
		<if test=" survival_adj_amount  != null "><![CDATA[ AND A.SURVIVAL_ADJ_AMOUNT = #{survival_adj_amount} ]]></if>
		<if test=" agent_holder_relation != null and agent_holder_relation != ''  "><![CDATA[ AND A.AGENT_HOLDER_RELATION = #{agent_holder_relation} ]]></if>
		<if test=" cause_remark != null and cause_remark != ''  "><![CDATA[ AND A.CAUSE_REMARK = #{cause_remark} ]]></if>
		<if test=" health_service_fee  != null "><![CDATA[ AND A.HEALTH_SERVICE_FEE = #{health_service_fee} ]]></if>
		<if test=" adjust_stand_amount  != null "><![CDATA[ AND A.ADJUST_STAND_AMOUNT = #{adjust_stand_amount} ]]></if>
		<if test=" apl_capital  != null "><![CDATA[ AND A.APL_CAPITAL = #{apl_capital} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" accumulate_capital  != null "><![CDATA[ AND A.ACCUMULATE_CAPITAL = #{accumulate_capital} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" margin_interest  != null "><![CDATA[ AND A.MARGIN_INTEREST = #{margin_interest} ]]></if>
		<if test=" loan_interest  != null "><![CDATA[ AND A.LOAN_INTEREST = #{loan_interest} ]]></if>
		<if test=" loan_capital  != null "><![CDATA[ AND A.LOAN_CAPITAL = #{loan_capital} ]]></if>
		<if test=" complaint_num != null and complaint_num != ''  "><![CDATA[ AND A.COMPLAINT_NUM = #{complaint_num} ]]></if>
		<if test=" stand_amount  != null "><![CDATA[ AND A.STAND_AMOUNT = #{stand_amount} ]]></if>
		<if test=" stand_fee_amount  != null "><![CDATA[ AND A.STAND_FEE_AMOUNT = #{stand_fee_amount} ]]></if>
		<if test=" accumulate_interest  != null "><![CDATA[ AND A.ACCUMULATE_INTEREST = #{accumulate_interest} ]]></if>
		<if test=" apl_interest  != null "><![CDATA[ AND A.APL_INTEREST = #{apl_interest} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" cost_fee  != null "><![CDATA[ AND A.COST_FEE = #{cost_fee} ]]></if>
		<if test=" invest_cash_value  != null "><![CDATA[ AND A.INVEST_CASH_VALUE = #{invest_cash_value} ]]></if>
		<if test=" is_depart  != null "><![CDATA[ AND A.IS_DEPART = #{is_depart} ]]></if>
		<if test=" bonus_amount  != null "><![CDATA[ AND A.BONUS_AMOUNT = #{bonus_amount} ]]></if>
		<if test=" market_cost  != null "><![CDATA[ AND A.MARKET_COST = #{market_cost} ]]></if>
		<if test=" margin_capital  != null "><![CDATA[ AND A.MARGIN_CAPITAL = #{margin_capital} ]]></if>
		<if test=" delay_cause != null and delay_cause != ''  "><![CDATA[ AND A.DELAY_CAUSE = #{delay_cause} ]]></if>
		<if test=" end_bonus  != null "><![CDATA[ AND A.END_BONUS = #{end_bonus} ]]></if>
		<if test=" extra_surrender  != null "><![CDATA[ AND A.EXTRA_SURRENDER = #{extra_surrender} ]]></if>
		<if test=" end_interest  != null "><![CDATA[ AND A.END_INTEREST = #{end_interest} ]]></if>
		<if test=" surrender_cause_name != null and surrender_cause_name != ''  "><![CDATA[ AND A.SURRENDER_CAUSE_NAME = #{surrender_cause_name} ]]></if>
		<if test=" death_date  != null  and  death_date  != ''  "><![CDATA[ AND A.DEATH_DATE = #{death_date} ]]></if>
		<if test=" death_cause  != null  and  death_cause  != ''  "><![CDATA[ AND A.DEATH_CAUSE = #{death_cause} ]]></if>
		<if test=" adjust_flag  != null  and  adjust_flag  != ''  "><![CDATA[ AND A.ADJUST_FLAG = #{adjust_flag} ]]></if>
		<if test=" special_flag  != null  and  special_flag  != ''  "><![CDATA[ AND A.SPECIAL_FLAG = #{special_flag} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_PA_querySurrenderByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_querySurrenderByChangeIdCondition">
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
	</sql>	
	<sql id="JRQD_PA_querySurrenderByPolicyChgIdCondition">
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_PA_addSurrender"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SURRENDER.NEXTVAL FROM DUAL
		</selectKey>
		
		
		<![CDATA[
			INSERT INTO APP___PAS__DBUSER.T_SURRENDER(
				SPECIAL_FLAG,SURRENDER_TYPE, SURRENDER_CAUSE, SURRENDER_AMOUNT, OTHER_ADJ_AMOUNT, ADJUST_BONUS_AMOUNT, ADJUST_END_BONUS, ADJUST_FEE_AMOUNT, 
				ITEM_ID, HESITATE_FLAG, SURVIVAL_ADJ_AMOUNT, AGENT_HOLDER_RELATION, CAUSE_REMARK, HEALTH_SERVICE_FEE, INSERT_TIMESTAMP, 
				ADJUST_STAND_AMOUNT, UPDATE_BY, APL_CAPITAL, CHANGE_ID, FEE_AMOUNT, LIST_ID, ACCUMULATE_CAPITAL, 
				POLICY_CHG_ID, BUSI_ITEM_ID, MARGIN_INTEREST, LOAN_INTEREST, LOAN_CAPITAL, COMPLAINT_NUM, INSERT_TIME, 
				STAND_AMOUNT, UPDATE_TIME, STAND_FEE_AMOUNT, ACCUMULATE_INTEREST, APL_INTEREST, ACCEPT_CODE, COST_FEE, 
				INVEST_CASH_VALUE, IS_DEPART, BONUS_AMOUNT, UPDATE_TIMESTAMP, INSERT_BY, MARKET_COST, MARGIN_CAPITAL, 
				DELAY_CAUSE, END_BONUS,EXTRA_SURRENDER, END_INTEREST,ADJUST_INVEST_CASH_VALUE, DEATH_DATE, DEATH_CAUSE,
				CASH_BONUS_ACCOUNT_CASH,CASH_BONUS_ACCOUNT_INTEREST,CASH_BONUS,REISSUED_INTEREST,CT_CASH_FEE,POLICY_AMOUNT,POLICY_FEE,RISK_FEE,INVEST_REISSUED_INTEREST,
				ADJUST_FLAG,ADJUST_LOAN_INTEREST,ADJUST_LOAN_CAPITAL,SURVEY_TIME,INVALID_TIME                 
				) 
			VALUES (
				 #{special_flag, jdbcType=NUMERIC} ,#{surrender_type, jdbcType=VARCHAR}, #{surrender_cause, jdbcType=VARCHAR} , #{surrender_amount, jdbcType=NUMERIC} , #{other_adj_amount, jdbcType=NUMERIC} , #{adjust_bonus_amount, jdbcType=NUMERIC} , #{adjust_end_bonus, jdbcType=NUMERIC} , #{adjust_fee_amount, jdbcType=NUMERIC} 
				, #{item_id, jdbcType=NUMERIC} , #{hesitate_flag, jdbcType=NUMERIC} , #{survival_adj_amount, jdbcType=NUMERIC} , #{agent_holder_relation, jdbcType=VARCHAR} , #{cause_remark, jdbcType=VARCHAR} , #{health_service_fee, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{adjust_stand_amount, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{apl_capital, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , #{accumulate_capital, jdbcType=NUMERIC} 
				, #{policy_chg_id, jdbcType=NUMERIC} , #{busi_item_id, jdbcType=NUMERIC} , #{margin_interest, jdbcType=NUMERIC} , #{loan_interest, jdbcType=NUMERIC} , #{loan_capital, jdbcType=NUMERIC} , #{complaint_num, jdbcType=VARCHAR} , SYSDATE 
				, #{stand_amount, jdbcType=NUMERIC} , SYSDATE , #{stand_fee_amount, jdbcType=NUMERIC} , #{accumulate_interest, jdbcType=NUMERIC} , #{apl_interest, jdbcType=NUMERIC} , #{accept_code, jdbcType=VARCHAR} , #{cost_fee, jdbcType=NUMERIC} 
				, #{invest_cash_value, jdbcType=NUMERIC} , #{is_depart, jdbcType=NUMERIC} , #{bonus_amount, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{market_cost, jdbcType=NUMERIC} , #{margin_capital, jdbcType=NUMERIC} 
				, #{delay_cause, jdbcType=VARCHAR} , #{end_bonus, jdbcType=NUMERIC} ,#{extra_surrender, jdbcType=NUMERIC},#{end_interest, jdbcType=NUMERIC}, #{adjust_invest_cash_value, jdbcType=NUMERIC}, #{death_date, jdbcType=DATE}, #{death_cause, jdbcType=NUMERIC}
				, #{cash_bonus_account_cash, jdbcType=NUMERIC} ,#{cash_bonus_account_interest, jdbcType=NUMERIC} ,#{cash_bonus, jdbcType=NUMERIC} ,#{reissued_interest, jdbcType=NUMERIC},#{ct_cash_fee, jdbcType=NUMERIC} ,#{policy_amount, jdbcType=NUMERIC} ,#{policy_fee, jdbcType=NUMERIC} ,#{risk_fee, jdbcType=NUMERIC}
				,#{invest_reissued_interest, jdbcType=NUMERIC},#{adjust_flag, jdbcType=NUMERIC},#{adjust_loan_interest, jdbcType=NUMERIC},#{adjust_loan_interest, jdbcType=NUMERIC},#{survey_time, jdbcType=DATE},#{invalid_time, jdbcType=TIMESTAMP}
				) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteSurrender" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SURRENDER WHERE LIST_ID = #{list_id} ]]>
	</delete>
	<!-- 删除操作 -->	
	<delete id="JRQD_PA_deleteSurrenderByAcceptId" parameterType="java.util.Map">
		<![CDATA[DELETE FROM APP___PAS__DBUSER.T_SURRENDER t
                         where t.accept_code = #{accept_code}]]>
	</delete>
	

<!-- 修改操作 -->
	<update id="JRQD_PA_updateSurrender" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SURRENDER ]]>
		<set>
		<trim suffixOverrides=",">
			SURRENDER_TYPE = #{surrender_type, jdbcType=VARCHAR} ,
			SURRENDER_CAUSE = #{surrender_cause, jdbcType=VARCHAR} ,
		    SURRENDER_AMOUNT = #{surrender_amount, jdbcType=NUMERIC} ,
		    OTHER_ADJ_AMOUNT = #{other_adj_amount, jdbcType=NUMERIC} ,
		    ADJUST_BONUS_AMOUNT = #{adjust_bonus_amount, jdbcType=NUMERIC} ,
		    ADJUST_END_BONUS = #{adjust_end_bonus, jdbcType=NUMERIC} ,
		    ADJUST_FEE_AMOUNT = #{adjust_fee_amount, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    HESITATE_FLAG = #{hesitate_flag, jdbcType=NUMERIC} ,
		    SURVIVAL_ADJ_AMOUNT = #{survival_adj_amount, jdbcType=NUMERIC} ,
			AGENT_HOLDER_RELATION = #{agent_holder_relation, jdbcType=VARCHAR} ,
			CAUSE_REMARK = #{cause_remark, jdbcType=VARCHAR} ,
		    HEALTH_SERVICE_FEE = #{health_service_fee, jdbcType=NUMERIC} ,
		    ADJUST_STAND_AMOUNT = #{adjust_stand_amount, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    APL_CAPITAL = #{apl_capital, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    ACCUMULATE_CAPITAL = #{accumulate_capital, jdbcType=NUMERIC} ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    MARGIN_INTEREST = #{margin_interest, jdbcType=NUMERIC} ,
		    LOAN_INTEREST = #{loan_interest, jdbcType=NUMERIC} ,
		    LOAN_CAPITAL = #{loan_capital, jdbcType=NUMERIC} ,
			COMPLAINT_NUM = #{complaint_num, jdbcType=VARCHAR} ,
		    STAND_AMOUNT = #{stand_amount, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    STAND_FEE_AMOUNT = #{stand_fee_amount, jdbcType=NUMERIC} ,
		    ACCUMULATE_INTEREST = #{accumulate_interest, jdbcType=NUMERIC} ,
		    APL_INTEREST = #{apl_interest, jdbcType=NUMERIC} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
		    COST_FEE = #{cost_fee, jdbcType=NUMERIC} ,
		    INVEST_CASH_VALUE = #{invest_cash_value, jdbcType=NUMERIC} ,
		    IS_DEPART = #{is_depart, jdbcType=NUMERIC} ,
		    BONUS_AMOUNT = #{bonus_amount, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    MARKET_COST = #{market_cost, jdbcType=NUMERIC} ,
		    MARGIN_CAPITAL = #{margin_capital, jdbcType=NUMERIC} ,
			DELAY_CAUSE = #{delay_cause, jdbcType=VARCHAR} ,
		    END_BONUS = #{end_bonus, jdbcType=NUMERIC} ,
		    EXTRA_SURRENDER = #{extra_surrender, jdbcType=NUMERIC},
		    END_INTEREST=#{end_interest, jdbcType=NUMERIC},
		    ADJUST_INVEST_CASH_VALUE=#{adjust_invest_cash_value, jdbcType=NUMERIC},
		    DEATH_DATE=#{death_date, jdbcType=DATE}, 
		    DEATH_CAUSE=#{death_cause, jdbcType=NUMERIC},
		    CASH_BONUS_ACCOUNT_CASH=#{cash_bonus_account_cash, jdbcType=NUMERIC},
		    CASH_BONUS_ACCOUNT_INTEREST=#{cash_bonus_account_interest, jdbcType=NUMERIC},
		    CASH_BONUS=#{cash_bonus, jdbcType=NUMERIC},
		    REISSUED_INTEREST=#{reissued_interest, jdbcType=NUMERIC},
		    CT_CASH_FEE=#{ct_cash_fee, jdbcType=NUMERIC},
		    POLICY_AMOUNT=#{policy_amount, jdbcType=NUMERIC},
		    POLICY_FEE=#{policy_fee, jdbcType=NUMERIC},
		    RISK_FEE =#{risk_fee, jdbcType=NUMERIC},
		    <if test="survey_time != null and survey_time != ''">
				SURVEY_TIME=#{survey_time, jdbcType=DATE},			
			</if>
			<if test="invalid_time != null and invalid_time != ''">
				INVAILD_TIME=#{invalid_time, jdbcType=TIMESTAMP}	
			</if>
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findSurrender" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.INVALID_TIME,A.SPECIAL_FLAG,A.SURRENDER_TYPE, A.SURRENDER_CAUSE, A.SURRENDER_AMOUNT, A.OTHER_ADJ_AMOUNT, A.ADJUST_BONUS_AMOUNT, A.ADJUST_END_BONUS, A.ADJUST_FEE_AMOUNT, 
			A.ITEM_ID, A.HESITATE_FLAG, A.SURVIVAL_ADJ_AMOUNT, A.AGENT_HOLDER_RELATION, A.CAUSE_REMARK, A.HEALTH_SERVICE_FEE, 
			A.ADJUST_STAND_AMOUNT, A.APL_CAPITAL, A.CHANGE_ID, A.FEE_AMOUNT, A.LIST_ID, A.ACCUMULATE_CAPITAL, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.MARGIN_INTEREST, A.LOAN_INTEREST, A.LOAN_CAPITAL, A.COMPLAINT_NUM, 
			A.STAND_AMOUNT, A.STAND_FEE_AMOUNT, A.ACCUMULATE_INTEREST, A.APL_INTEREST, A.ACCEPT_CODE, A.COST_FEE, 
			A.INVEST_CASH_VALUE, A.IS_DEPART, A.BONUS_AMOUNT, A.MARKET_COST, A.MARGIN_CAPITAL, 
			A.DELAY_CAUSE, A.END_BONUS, A.EXTRA_SURRENDER ,A.END_INTEREST,A.ADJUST_INVEST_CASH_VALUE,A.DEATH_DATE,A.DEATH_CAUSE,
			0 as CASH_BONUS_ACCOUNT_CASH,0 as CASH_BONUS_ACCOUNT_INTEREST,0 as CASH_BONUS,0 as REISSUED_INTEREST,0 as CT_CASH_FEE, 0 as POLICY_AMOUNT,0 as POLICY_FEE,0 as RISK_FEE,A.INVEST_REISSUED_INTEREST
			  FROM APP___PAS__DBUSER.T_SURRENDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_surrenderWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="JRQD_PA_findSurrenderByChangeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SPECIAL_FLAG,A.SURRENDER_TYPE, A.SURRENDER_CAUSE, A.SURRENDER_AMOUNT, A.OTHER_ADJ_AMOUNT, A.ADJUST_BONUS_AMOUNT, A.ADJUST_END_BONUS, A.ADJUST_FEE_AMOUNT, 
			A.ITEM_ID, A.HESITATE_FLAG, A.SURVIVAL_ADJ_AMOUNT, A.AGENT_HOLDER_RELATION, A.CAUSE_REMARK, A.HEALTH_SERVICE_FEE, 
			A.ADJUST_STAND_AMOUNT, A.APL_CAPITAL, A.CHANGE_ID, A.FEE_AMOUNT, A.LIST_ID, A.ACCUMULATE_CAPITAL, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.MARGIN_INTEREST, A.LOAN_INTEREST, A.LOAN_CAPITAL, A.COMPLAINT_NUM, 
			A.STAND_AMOUNT, A.STAND_FEE_AMOUNT, A.ACCUMULATE_INTEREST, A.APL_INTEREST, A.ACCEPT_CODE, A.COST_FEE, 
			A.INVEST_CASH_VALUE, A.IS_DEPART, A.BONUS_AMOUNT, A.MARKET_COST, A.MARGIN_CAPITAL, 
			A.DELAY_CAUSE, A.END_BONUS, A.EXTRA_SURRENDER ,A.END_INTEREST,A.ADJUST_INVEST_CASH_VALUE,A.DEATH_DATE,A.DEATH_CAUSE,
			0 as CASH_BONUS_ACCOUNT_CASH,0 as CASH_BONUS_ACCOUNT_INTEREST,0 as CASH_BONUS,0 as REISSUED_INTEREST,0 as CT_CASH_FEE, 0 as POLICY_AMOUNT,0 as POLICY_FEE,0 as RISK_FEE,
			A.ADJUST_FLAG,A.ADJUST_LOAN_INTEREST,A.ADJUST_LOAN_CAPITAL
			 FROM APP___PAS__DBUSER.T_SURRENDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_querySurrenderByChangeIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="JRQD_PA_findSurrenderByPolicyChgId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SPECIAL_FLAG,A.SURRENDER_TYPE, A.SURRENDER_CAUSE, A.SURRENDER_AMOUNT, A.OTHER_ADJ_AMOUNT, A.ADJUST_BONUS_AMOUNT, A.ADJUST_END_BONUS, A.ADJUST_FEE_AMOUNT, 
			A.ITEM_ID, A.HESITATE_FLAG, A.SURVIVAL_ADJ_AMOUNT, A.AGENT_HOLDER_RELATION, A.CAUSE_REMARK, A.HEALTH_SERVICE_FEE, 
			A.ADJUST_STAND_AMOUNT, A.APL_CAPITAL, A.CHANGE_ID, A.FEE_AMOUNT, A.LIST_ID, A.ACCUMULATE_CAPITAL, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.MARGIN_INTEREST, A.LOAN_INTEREST, A.LOAN_CAPITAL, A.COMPLAINT_NUM, 
			A.STAND_AMOUNT, A.STAND_FEE_AMOUNT, A.ACCUMULATE_INTEREST, A.APL_INTEREST, A.ACCEPT_CODE, A.COST_FEE, 
			A.INVEST_CASH_VALUE, A.IS_DEPART, A.BONUS_AMOUNT, A.MARKET_COST, A.MARGIN_CAPITAL, 
			A.DELAY_CAUSE, A.END_BONUS, A.EXTRA_SURRENDER ,A.END_INTEREST,A.ADJUST_INVEST_CASH_VALUE,A.DEATH_DATE,A.DEATH_CAUSE, 
			0 as CASH_BONUS_ACCOUNT_CASH,0 as CASH_BONUS_ACCOUNT_INTEREST,0 as CASH_BONUS,0 as REISSUED_INTEREST,0 as CT_CASH_FEE, 0 as POLICY_AMOUNT,0 as POLICY_FEE,0 as RISK_FEE,
			A.ADJUST_FLAG,A.ADJUST_LOAN_INTEREST,A.ADJUST_LOAN_CAPITAL
			FROM APP___PAS__DBUSER.T_SURRENDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_querySurrenderByPolicyChgIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_PA_findAllMapSurrender" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SPECIAL_FLAG,A.SURRENDER_TYPE, A.SURRENDER_CAUSE, A.SURRENDER_AMOUNT, A.OTHER_ADJ_AMOUNT, A.ADJUST_BONUS_AMOUNT, A.ADJUST_END_BONUS, A.ADJUST_FEE_AMOUNT, 
			A.ITEM_ID, A.HESITATE_FLAG, A.SURVIVAL_ADJ_AMOUNT, A.AGENT_HOLDER_RELATION, A.CAUSE_REMARK, A.HEALTH_SERVICE_FEE, 
			A.ADJUST_STAND_AMOUNT, A.APL_CAPITAL, A.CHANGE_ID, A.FEE_AMOUNT, A.LIST_ID, A.ACCUMULATE_CAPITAL, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.MARGIN_INTEREST, A.LOAN_INTEREST, A.LOAN_CAPITAL, A.COMPLAINT_NUM, 
			A.STAND_AMOUNT, A.STAND_FEE_AMOUNT, A.ACCUMULATE_INTEREST, A.APL_INTEREST, A.ACCEPT_CODE, A.COST_FEE, 
			A.INVEST_CASH_VALUE, A.IS_DEPART, A.BONUS_AMOUNT, A.MARKET_COST, A.MARGIN_CAPITAL, 
			A.DELAY_CAUSE, A.END_BONUS, A.EXTRA_SURRENDER ,A.END_INTEREST,A.ADJUST_INVEST_CASH_VALUE,A.DEATH_DATE,A.DEATH_CAUSE ,
			0 as CASH_BONUS_ACCOUNT_CASH,0 as CASH_BONUS_ACCOUNT_INTEREST,0 as CASH_BONUS,0 as REISSUED_INTEREST,0 as CT_CASH_FEE, 0 as POLICY_AMOUNT,0 as POLICY_FEE,0 as RISK_FEE,
			A.ADJUST_FLAG,A.ADJUST_LOAN_INTEREST,A.ADJUST_LOAN_CAPITAL
			FROM APP___PAS__DBUSER.T_SURRENDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_surrenderWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_PA_findAllSurrender" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.INVALID_TIME,A.SPECIAL_FLAG,A.SURRENDER_TYPE, A.SURRENDER_CAUSE, A.SURRENDER_AMOUNT, A.OTHER_ADJ_AMOUNT, A.ADJUST_BONUS_AMOUNT, A.ADJUST_END_BONUS, A.ADJUST_FEE_AMOUNT, 
			A.ITEM_ID, A.HESITATE_FLAG, A.SURVIVAL_ADJ_AMOUNT, A.AGENT_HOLDER_RELATION, A.CAUSE_REMARK, A.HEALTH_SERVICE_FEE, 
			A.ADJUST_STAND_AMOUNT, A.APL_CAPITAL, A.CHANGE_ID, A.FEE_AMOUNT, A.LIST_ID, A.ACCUMULATE_CAPITAL, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.MARGIN_INTEREST, A.LOAN_INTEREST, A.LOAN_CAPITAL, A.COMPLAINT_NUM, 
			A.STAND_AMOUNT, A.STAND_FEE_AMOUNT, A.ACCUMULATE_INTEREST, A.APL_INTEREST, A.ACCEPT_CODE, A.COST_FEE, 
			A.INVEST_CASH_VALUE, A.IS_DEPART, A.BONUS_AMOUNT, A.MARKET_COST, A.MARGIN_CAPITAL, 
			A.DELAY_CAUSE, A.END_BONUS, A.EXTRA_SURRENDER ,A.END_INTEREST,A.ADJUST_INVEST_CASH_VALUE,A.DEATH_DATE,A.DEATH_CAUSE,
			0 as CASH_BONUS_ACCOUNT_CASH,0 as CASH_BONUS_ACCOUNT_INTEREST,0 as CASH_BONUS,0 as REISSUED_INTEREST,0 as CT_CASH_FEE, 0 as POLICY_AMOUNT,0 as POLICY_FEE,0 as RISK_FEE,
			A.ADJUST_FLAG,A.ADJUST_LOAN_INTEREST,A.ADJUST_LOAN_CAPITAL
			 FROM APP___PAS__DBUSER.T_SURRENDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_PA_surrenderWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_PA_findSurrenderTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SURRENDER A WHERE 1 = 1  ]]>
		<include refid="JRQD_PA_surrenderWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_PA_querySurrenderForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.SPECIAL_FLAG, B.SURRENDER_TYPE, B.SURRENDER_CAUSE, B.SURRENDER_AMOUNT, B.OTHER_ADJ_AMOUNT, B.ADJUST_BONUS_AMOUNT, B.ADJUST_END_BONUS, B.ADJUST_FEE_AMOUNT, 
			B.ITEM_ID, B.HESITATE_FLAG, B.SURVIVAL_ADJ_AMOUNT, B.AGENT_HOLDER_RELATION, B.CAUSE_REMARK, B.HEALTH_SERVICE_FEE, 
			B.ADJUST_STAND_AMOUNT, B.APL_CAPITAL, B.CHANGE_ID, B.FEE_AMOUNT, B.LIST_ID, B.ACCUMULATE_CAPITAL, 
			B.POLICY_CHG_ID, B.BUSI_ITEM_ID, B.MARGIN_INTEREST, B.LOAN_INTEREST, B.LOAN_CAPITAL, B.COMPLAINT_NUM, 
			B.STAND_AMOUNT, B.STAND_FEE_AMOUNT, B.ACCUMULATE_INTEREST, B.APL_INTEREST, B.ACCEPT_CODE, B.COST_FEE, 
			B.INVEST_CASH_VALUE, B.IS_DEPART, B.BONUS_AMOUNT, B.MARKET_COST, B.MARGIN_CAPITAL, B.ADJUST_FLAG,B.ADJUST_LOAN_INTEREST,B.ADJUST_LOAN_CAPITAL
			B.DELAY_CAUSE, B.END_BONUS, B.EXTRA_SURRENDER,0 as CASH_BONUS_ACCOUNT_CASH,0 as CASH_BONUS_ACCOUNT_INTEREST,0 as CASH_BONUS,0 as REISSUED_INTEREST,0 as CT_CASH_FEE, 0 as POLICY_AMOUNT,0 as POLICY_FEE,0 as RISK_FEE FROM (
					SELECT ROWNUM RN, A.SPECIAL_FLAG,A.SURRENDER_TYPE, A.SURRENDER_CAUSE, A.SURRENDER_AMOUNT, A.OTHER_ADJ_AMOUNT, A.ADJUST_BONUS_AMOUNT, A.ADJUST_END_BONUS, A.ADJUST_FEE_AMOUNT, 
			A.ITEM_ID, A.HESITATE_FLAG, A.SURVIVAL_ADJ_AMOUNT, A.AGENT_HOLDER_RELATION, A.CAUSE_REMARK, A.HEALTH_SERVICE_FEE, 
			A.ADJUST_STAND_AMOUNT, A.APL_CAPITAL, A.CHANGE_ID, A.FEE_AMOUNT, A.LIST_ID, A.ACCUMULATE_CAPITAL, 
			A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.MARGIN_INTEREST, A.LOAN_INTEREST, A.LOAN_CAPITAL, A.COMPLAINT_NUM, 
			A.STAND_AMOUNT, A.STAND_FEE_AMOUNT, A.ACCUMULATE_INTEREST, A.APL_INTEREST, A.ACCEPT_CODE, A.COST_FEE, 
			A.INVEST_CASH_VALUE, A.IS_DEPART, A.BONUS_AMOUNT, A.MARKET_COST, A.MARGIN_CAPITAL, 
			A.DELAY_CAUSE, A.END_BONUS, A.EXTRA_SURRENDER ,A.END_INTEREST,A.ADJUST_INVEST_CASH_VALUE,A.DEATH_DATE,A.DEATH_CAUSE,
			0 as CASH_BONUS_ACCOUNT_CASH,0 as CASH_BONUS_ACCOUNT_INTEREST,0 as CASH_BONUS,0 as REISSUED_INTEREST,0 as CT_CASH_FEE, 0 as POLICY_AMOUNT,0 as POLICY_FEE,0 as RISK_FEE,
			A.ADJUST_FLAG,A.ADJUST_LOAN_INTEREST,A.ADJUST_LOAN_CAPITAL
			FROM APP___PAS__DBUSER.T_SURRENDER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_PA_surrenderWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="JRQD_PA_findSurrenderCause" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.SURRENDER_CAUSE,A.SURRENDER_CAUSE_NAME FROM APP___PAS__DBUSER.T_SURRENDER_CAUSE  A WHERE SURRENDER_CAUSE=#{surrender_cause}]]>
	</select>
	<!-- 查询退保为收费的数据 -->
	<select id="JRQD_PA_findSpcialSurrender" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ 
 select 
       A.BUSI_ITEM_ID,
       A.LOAN_CAPITAL,
       A.LOAN_INTEREST,
       A.SURRENDER_AMOUNT,
       A.STAND_AMOUNT,
       B.BUSI_PROD_CODE,
       C.PRODUCT_NAME_SYS,
       B.BUSI_PRD_ID,
       B.Policy_Id,
       A.LIST_ID
  from APP___PAS__DBUSER.t_surrender A,
       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B,
       APP___PAS__DBUSER.T_BUSINESS_PRODUCT C
 where A.accept_code =#{accept_code}
    and A.surrender_amount < 0
   AND A.BUSI_ITEM_ID=B.BUSI_ITEM_ID
   AND B.BUSI_PRD_ID=C.BUSINESS_PRD_ID	
	 ]]>
	</select>
	<!--需求分析任务 #65761查询退保数据  -->
	<update id="JRQD_PA_updaetRefundAmountSurrendersByLlstId" parameterType="java.util.Map">
	<![CDATA[ UPDATE APP___PAS__DBUSER.T_SURRENDER A ]]>
		<set>
		<trim suffixOverrides=",">
		A.ADJUST_FLAG = #{adjust_flag, jdbcType=NUMERIC},
		 <if test="ADJUST_LOAN_CAPITAL !='' ">
	    A.ADJUST_LOAN_CAPITAL = #{adjust_loan_capital,jdbcType=NUMERIC},
		</if>
		<if test="ADJUST_LOAN_INTEREST !='' ">
	    A.ADJUST_LOAN_INTEREST = #{adjust_loan_interest,jdbcType=NUMERIC},
		</if>
		</trim>
		</set>
		<![CDATA[ WHERE A.LIST_ID = #{list_id} ]]>
	</update>
	
	<select id="JRQD_PA_querySpecialForCT" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT count(1)
			  FROM APP___PAS__DBUSER.T_SURRENDER A
			 WHERE  A.SPECIAL_FLAG =1
			    AND A.POLICY_CHG_ID = #{policy_chg_id}
   	 ]]>
	</select>
	
</mapper>
