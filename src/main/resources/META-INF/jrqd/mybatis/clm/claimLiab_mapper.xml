<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimLiabPO">

	<sql id="JRQD_claimLiabWhereCondition">
		<if test=" advance_pay  != null "><![CDATA[ AND A.ADVANCE_PAY = #{advance_pay} ]]></if>
		<if test=" claim_type != null and claim_type != ''  "><![CDATA[ AND A.CLAIM_TYPE = #{claim_type} ]]></if>
		<if test=" actual_pay  != null "><![CDATA[ AND A.ACTUAL_PAY = #{actual_pay} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" clob_id != null "><![CDATA[ AND A.CLOB_ID = #{clob_id} ]]></if>
		<if test=" claim_liab_id  != null "><![CDATA[ AND A.CLAIM_LIAB_ID = #{claim_liab_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" is_common  != null "><![CDATA[ AND A.IS_COMMON = #{is_common} ]]></if>
		<if test=" liab_start_date  != null  and  liab_start_date  != ''  "><![CDATA[ AND A.LIAB_START_DATE = #{liab_start_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" sub_case_id  != null "><![CDATA[ AND A.SUB_CASE_ID = #{sub_case_id} ]]></if>
		<if test=" amount_basic_pay  != null "><![CDATA[ AND A.AMOUNT_BASIC_PAY = #{amount_basic_pay} ]]></if>
		<if test=" waive_item  != null "><![CDATA[ AND A.WAIVE_ITEM = #{waive_item} ]]></if>
		<if test=" waive_reason != null and waive_reason != ''  "><![CDATA[ AND A.WAIVE_REASON = #{waive_reason} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" liab_adjust_reason != null and liab_adjust_reason != ''  "><![CDATA[ AND A.LIAB_ADJUST_REASON = #{liab_adjust_reason} ]]></if>
		<if test=" makeup_basic_pay  != null "><![CDATA[ AND A.MAKEUP_BASIC_PAY = #{makeup_basic_pay} ]]></if>
		<if test=" resist_flag  != null "><![CDATA[ AND A.RESIST_FLAG = #{resist_flag} ]]></if>
		<if test=" liability_status  != null "><![CDATA[ AND A.LIABILITY_STATUS = #{liability_status} ]]></if>
		<if test=" waive_start  != null  and  waive_start  != ''  "><![CDATA[ AND A.WAIVE_START = #{waive_start} ]]></if>
		<if test=" adjust_pay  != null "><![CDATA[ AND A.ADJUST_PAY = #{adjust_pay} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" bonus_basic_pay  != null "><![CDATA[ AND A.BONUS_BASIC_PAY = #{bonus_basic_pay} ]]></if>
		<if test=" liab_name != null and liab_name != ''  "><![CDATA[ AND A.LIAB_NAME = #{liab_name} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" adjust_remark != null and adjust_remark != ''  "><![CDATA[ AND A.ADJUST_REMARK = #{adjust_remark} ]]></if>
		<if test=" liab_conclusion  != null "><![CDATA[ AND A.LIAB_CONCLUSION = #{liab_conclusion} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" waive_amt  != null "><![CDATA[ AND A.WAIVE_AMT = #{waive_amt} ]]></if>
		<if test=" waive_end  != null  and  waive_end  != ''  "><![CDATA[ AND A.WAIVE_END = #{waive_end} ]]></if>
		<if test=" calc_pay  != null "><![CDATA[ AND A.CALC_PAY = #{calc_pay} ]]></if>
		<if test=" basic_pay  != null "><![CDATA[ AND A.BASIC_PAY = #{basic_pay} ]]></if>
		<if test=" liab_end_date  != null  and  liab_end_date  != ''  "><![CDATA[ AND A.LIAB_END_DATE = #{liab_end_date} ]]></if>
		<if test=" advance_date  != null  and  advance_date  != ''  "><![CDATA[ AND A.ADVANCE_DATE = #{advance_date} ]]></if>
		<if test=" clm_after_state  != null  and  clm_after_state  != ''  "><![CDATA[ AND A.CLM_AFTER_STATE = #{clm_after_state} ]]></if>
		<if test=" is_instalment  != null"><![CDATA[ AND A.IS_INSTALMENT = #{is_instalment} ]]></if>
		<if test=" is_must_sign  != null"><![CDATA[ AND A.IS_MUST_SIGN = #{is_must_sign} ]]></if>
		<if test=" rela_master_busi_end_flag  != null  and  rela_master_busi_end_flag  != ''  "><![CDATA[ AND A.RELA_MASTER_BUSI_END_FLAG = #{rela_master_busi_end_flag} ]]></if>
		<if test=" rela_master_busi_stop_pay_flag  != null  and  rela_master_busi_stop_pay_flag  != ''  "><![CDATA[ AND A.RELA_MASTER_BUSI_STOP_PAY_FLAG = #{rela_master_busi_stop_pay_flag} ]]></if>
		<if test=" rela_mas_busi_amou_bonus_flag  != null  and rela_mas_busi_amou_bonus_flag != ''  "><![CDATA[ AND A.RELA_MAS_BUSI_AMOU_BONUS_FLAG = #{rela_mas_busi_amou_bonus_flag} ]]></if>
		<if test=" rela_mas_busi_redu_amount_flag  != null  and rela_mas_busi_redu_amount_flag != ''  "><![CDATA[ AND A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG = #{rela_mas_busi_redu_amount_flag} ]]></if>
		<if test=" rela_mas_busi_cash_bonus_flag  != null  and rela_mas_busi_cash_bonus_flag != ''  "><![CDATA[ AND A.RELA_MAS_BUSI_CASH_BONUS_FLAG = #{rela_mas_busi_cash_bonus_flag} ]]></if>
		<if test=" surplus_effect_amount  != null"><![CDATA[ AND A.SURPLUS_EFFECT_AMOUNT = #{surplus_effect_amount} ]]></if>
		<if test=" reject_code != null and reject_code != ''  "><![CDATA[ AND A.REJECT_CODE = #{reject_code} ]]></if>
		<if test=" reject_proof != null and reject_proof != ''  "><![CDATA[ AND A.REJECT_PROOF = #{reject_proof} ]]></if>
		<if test=" special_remark != null and special_remark != ''  "><![CDATA[ AND A.SPECIAL_REMARK = #{special_remark} ]]></if>
		<if test=" before_surplus_effect_amount != null "><![CDATA[ AND A.BEFORE_SURPLUS_EFFECT_AMOUNT = #{before_surplus_effect_amount} ]]></if>
		<if test=" liab_code  != null  and liab_code != ''  "><![CDATA[ AND A.LIAB_CODE = #{liab_code} ]]></if>
		<if test=" liab_risk_label  != null"><![CDATA[ AND A.LIAB_RISK_LABEL = #{liab_risk_label} ]]></if>
		<if test=" is_subsidy  != null"><![CDATA[ AND A.IS_SUBSIDY = #{is_subsidy} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryClaimLiabByClaimLiabIdCondition">
		<if test=" claim_liab_id  != null "><![CDATA[ AND A.CLAIM_LIAB_ID = #{claim_liab_id} ]]></if>
		<if test=" claim_liab_id  == null "><![CDATA[ AND A.CLAIM_LIAB_ID = null ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addClaimLiab"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="claim_liab_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_LIAB__CLAIM_LIAB_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_LIAB(
				ADVANCE_PAY, CLAIM_TYPE, ACTUAL_PAY, PRODUCT_ID, CLOB_ID, CLAIM_LIAB_ID, ITEM_ID, 
				IS_COMMON, LIAB_START_DATE, BUSI_PROD_CODE, IS_WAIVED, INSERT_TIMESTAMP, UPDATE_BY, SUB_CASE_ID, 
				AMOUNT_BASIC_PAY, WAIVE_ITEM, WAIVE_REASON, BUSI_ITEM_ID, POLICY_ID, LIAB_ADJUST_REASON, MAKEUP_BASIC_PAY, 
				RESIST_FLAG, LIABILITY_STATUS, WAIVE_START, INSERT_TIME, ADJUST_PAY, LIAB_ID, UPDATE_TIME, 
				BONUS_BASIC_PAY, LIAB_NAME, CASE_ID, ADJUST_REMARK, LIAB_CONCLUSION, POLICY_CODE, WAIVE_AMT, 
				WAIVE_END, UPDATE_TIMESTAMP, INSERT_BY, CALC_PAY, BASIC_PAY, LIAB_END_DATE, ADVANCE_DATE, CLM_AFTER_STATE,IS_SUBSIDY,IS_INSTALMENT,IS_MUST_SIGN,
				RELA_MASTER_BUSI_END_FLAG,RELA_MASTER_BUSI_STOP_PAY_FLAG,RELA_MAS_BUSI_AMOU_BONUS_FLAG,RELA_MAS_BUSI_REDU_AMOUNT_FLAG,RELA_MAS_BUSI_CASH_BONUS_FLAG,
				SURPLUS_EFFECT_AMOUNT,INSTALMENT_TOTAL_PAY,ADVANCE_INPUT_OPERATOR,REJECT_CODE,REJECT_PROOF,SPECIAL_REMARK,ACCU_BONUS_AMOUNT,BEFORE_SURPLUS_EFFECT_AMOUNT,LIAB_CODE,OTHER_REASON,LIAB_RISK_LABEL) 
			VALUES (
				#{advance_pay, jdbcType=NUMERIC}, #{claim_type, jdbcType=VARCHAR} , #{actual_pay, jdbcType=NUMERIC} , #{product_id, jdbcType=NUMERIC} , #{clob_id, jdbcType=NUMERIC} , #{claim_liab_id, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} 
				, #{is_common, jdbcType=NUMERIC} , #{liab_start_date, jdbcType=DATE} , #{busi_prod_code, jdbcType=VARCHAR} , #{is_waived, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{sub_case_id, jdbcType=NUMERIC} 
				, #{amount_basic_pay, jdbcType=NUMERIC} , #{waive_item, jdbcType=NUMERIC} , #{waive_reason, jdbcType=VARCHAR} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{liab_adjust_reason, jdbcType=VARCHAR} , #{makeup_basic_pay, jdbcType=NUMERIC} 
				, #{resist_flag, jdbcType=NUMERIC} , #{liability_status, jdbcType=NUMERIC} , #{waive_start, jdbcType=DATE} , SYSDATE , #{adjust_pay, jdbcType=NUMERIC} , #{liab_id, jdbcType=NUMERIC} , SYSDATE 
				, #{bonus_basic_pay, jdbcType=NUMERIC} , #{liab_name, jdbcType=VARCHAR} , #{case_id, jdbcType=NUMERIC} , #{adjust_remark, jdbcType=VARCHAR} , #{liab_conclusion, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{waive_amt, jdbcType=NUMERIC} 
				, #{waive_end, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{calc_pay, jdbcType=NUMERIC} , #{basic_pay, jdbcType=NUMERIC} , #{liab_end_date, jdbcType=DATE} , #{advance_date, jdbcType=DATE} , #{clm_after_state, jdbcType=VARCHAR}
				, #{is_subsidy, jdbcType=NUMERIC},#{is_instalment, jdbcType=NUMERIC},#{is_must_sign, jdbcType=NUMERIC},#{rela_master_busi_end_flag, jdbcType=VARCHAR},#{rela_master_busi_stop_pay_flag, jdbcType=VARCHAR},#{rela_mas_busi_amou_bonus_flag, jdbcType=VARCHAR}
				, #{rela_mas_busi_redu_amount_flag, jdbcType=VARCHAR},#{rela_mas_busi_cash_bonus_flag, jdbcType=VARCHAR},#{surplus_effect_amount, jdbcType=NUMERIC},#{instalment_total_pay, jdbcType=NUMERIC}, #{advance_input_operator, jdbcType=NUMERIC}
				, #{reject_code, jdbcType=VARCHAR}, #{reject_proof, jdbcType=VARCHAR}, #{special_remark, jdbcType=VARCHAR},#{accu_bonus_amount,jdbcType=NUMERIC},#{before_surplus_effect_amount,jdbcType=NUMERIC}, #{liab_code, jdbcType=VARCHAR}, #{other_reason, jdbcType=VARCHAR},#{liab_risk_label,jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteClaimLiab" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_LIAB WHERE CLAIM_LIAB_ID = #{claim_liab_id} ]]>
	</delete>
	<delete id="JRQD_deleteClaimLiabByCaseId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_LIAB WHERE CASE_ID = #{case_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateClaimLiab" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_LIAB ]]>
		<set>
		<trim suffixOverrides=",">
		    ADVANCE_PAY = #{advance_pay, jdbcType=NUMERIC} ,
			CLAIM_TYPE = #{claim_type, jdbcType=VARCHAR} ,
		    ACTUAL_PAY = #{actual_pay, jdbcType=NUMERIC} ,
		    PRODUCT_ID = #{product_id, jdbcType=NUMERIC} ,
			CLOB_ID = #{clob_id, jdbcType=NUMERIC} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		    IS_COMMON = #{is_common, jdbcType=NUMERIC} ,
		    LIAB_START_DATE = #{liab_start_date, jdbcType=DATE} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    SUB_CASE_ID = #{sub_case_id, jdbcType=NUMERIC} ,
		    AMOUNT_BASIC_PAY = #{amount_basic_pay, jdbcType=NUMERIC} ,
		    WAIVE_ITEM = #{waive_item, jdbcType=NUMERIC} ,
			WAIVE_REASON = #{waive_reason, jdbcType=VARCHAR} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			LIAB_ADJUST_REASON = #{liab_adjust_reason, jdbcType=VARCHAR} ,
		    MAKEUP_BASIC_PAY = #{makeup_basic_pay, jdbcType=NUMERIC} ,
		    RESIST_FLAG = #{resist_flag, jdbcType=NUMERIC} ,
		    LIABILITY_STATUS = #{liability_status, jdbcType=NUMERIC} ,
		    WAIVE_START = #{waive_start, jdbcType=DATE} ,
		    ADJUST_PAY = #{adjust_pay, jdbcType=NUMERIC} ,
		    LIAB_ID = #{liab_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    BONUS_BASIC_PAY = #{bonus_basic_pay, jdbcType=NUMERIC} ,
			LIAB_NAME = #{liab_name, jdbcType=VARCHAR} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			ADJUST_REMARK = #{adjust_remark, jdbcType=VARCHAR} ,
		    LIAB_CONCLUSION = #{liab_conclusion, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    WAIVE_AMT = #{waive_amt, jdbcType=NUMERIC} ,
		    WAIVE_END = #{waive_end, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CALC_PAY = #{calc_pay, jdbcType=NUMERIC} ,
		    BASIC_PAY = #{basic_pay, jdbcType=NUMERIC} ,
		    LIAB_END_DATE = #{liab_end_date, jdbcType=DATE} ,
		    ADVANCE_DATE = #{advance_date, jdbcType=DATE} ,
		    CLM_AFTER_STATE = #{clm_after_state, jdbcType=VARCHAR} ,
		    IS_SUBSIDY = #{is_subsidy, jdbcType=NUMERIC} ,
		    IS_INSTALMENT = #{is_instalment, jdbcType=NUMERIC} ,
		    IS_MUST_SIGN = #{is_must_sign, jdbcType=NUMERIC} ,
		    RELA_MASTER_BUSI_END_FLAG = #{rela_master_busi_end_flag, jdbcType=VARCHAR},
		    RELA_MASTER_BUSI_STOP_PAY_FLAG=#{rela_master_busi_stop_pay_flag, jdbcType=VARCHAR},
		    RELA_MAS_BUSI_AMOU_BONUS_FLAG = #{rela_mas_busi_amou_bonus_flag, jdbcType=VARCHAR},
			RELA_MAS_BUSI_REDU_AMOUNT_FLAG = #{rela_mas_busi_redu_amount_flag, jdbcType=VARCHAR},
			RELA_MAS_BUSI_CASH_BONUS_FLAG = #{rela_mas_busi_cash_bonus_flag, jdbcType=VARCHAR},
			INSTALMENT_TOTAL_PAY = #{instalment_total_pay, jdbcType=NUMERIC},
			ADVANCE_INPUT_OPERATOR = #{advance_input_operator, jdbcType=NUMERIC},
			SURPLUS_EFFECT_AMOUNT = #{surplus_effect_amount,jdbcType=NUMERIC},
			REJECT_CODE = #{reject_code,jdbcType=VARCHAR},
			REJECT_PROOF = #{reject_proof,jdbcType=VARCHAR},
			SPECIAL_REMARK = #{special_remark,jdbcType=VARCHAR},
			ACCU_BONUS_AMOUNT = #{accu_bonus_amount,jdbcType=NUMERIC},
			BEFORE_SURPLUS_EFFECT_AMOUNT = #{before_surplus_effect_amount,jdbcType=NUMERIC},
			LIAB_CODE = #{liab_code,jdbcType=VARCHAR},
			OTHER_REASON = #{other_reason,jdbcType=VARCHAR},
			LIAB_RISK_LABEL = #{liab_risk_label,jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE CLAIM_LIAB_ID = #{claim_liab_id} ]]>
	</update>
	
	<!-- 修改操作 -->
	<update id="JRQD_updateClaimCase" parameterType="java.util.Map">
		<![CDATA[ 
     
    UPDATE APP___CLM__DBUSER.T_CLAIM_CASE TCC
       SET TCC.ACTUAL_PAY =
           ((select nvl(sum(tcb.actual_pay), '0')
              FROM APP___CLM__DBUSER.T_CLAIM_BUSI_PROD tcb
             where tcb.case_id = #{case_id} ) +
           
            (select nvl(sum(tca.fee_amount), '0')
              FROM APP___CLM__DBUSER.T_CLAIM_ADJUST_BUSI tca
             where tca.case_id = #{case_id}
                  
               and tca.adjust_type in
                   (1, 2, 8, 9, 10, 13) )
                  
                   -   ( select nvl(sum(tca.fee_amount), '0')
                      FROM APP___CLM__DBUSER.T_CLAIM_ADJUST_BUSI tca
                     where tca.case_id = #{case_id}
                       and tca.adjust_type not in (1, 2, 8, 9, 10, 13))    ) 
                    
                     WHERE TCC.CASE_ID = #{case_id}
		
		 ]]>
		 
	 
	</update>
	
    <update id="JRQD_updatePartClaimLiab" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_LIAB ]]>
		<set>
		<trim suffixOverrides=",">
		    WAIVE_START = #{waive_start, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
		    WAIVE_AMT = #{waive_amt, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    WAIVE_END = #{waive_end, jdbcType=DATE} ,
			WAIVE_REASON = #{waive_reason, jdbcType=VARCHAR} ,
			LIAB_CONCLUSION = #{liab_conclusion, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE CLAIM_LIAB_ID = #{claim_liab_id} ]]>
	</update>
<!-- 按索引查询操作 -->	
	<select id="JRQD_findClaimLiabByClaimLiabId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.INSTALMENT_TOTAL_PAY,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryClaimLiabByClaimLiabIdCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]>
	</select>
<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.INSTALMENT_TOTAL_PAY,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, A.INSERT_TIME,
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN, 
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.INSTALMENT_TOTAL_PAY,A.ADVANCE_INPUT_OPERATOR,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
<!-- 查询所有操作 -->
	<select id="JRQD_findAdvancePay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT SUM(A.ADVANCE_PAY) AS ADVANCE_PAY 
        FROM APP___CLM__DBUSER.T_CLAIM_LIAB A
       WHERE ROWNUM <= 1000 ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
	</select>
<!-- 查询一般失能累计给付金额 -->
	<select id="JRQD_findClaimLiabGeneral" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(A.ACTUAL_PAY) ACTUAL_PAY
		FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.POLICY_CODE=#{policy_code} AND CLAIM_TYPE='06' ]]>
	</select>
	
	<!-- 查询重度失能累计给付金额 -->
	<select id="JRQD_findClaimLiabSerious" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(A.ACTUAL_PAY) ACTUAL_PAY
		FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.POLICY_CODE=#{policy_code} AND CLAIM_TYPE='07' ]]>
	</select>
<!-- 查询个数操作 -->
	<select id="JRQD_findClaimLiabTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE 1 = 1  ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryClaimLiabForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.LIAB_CODE,B.ADVANCE_PAY, B.CLAIM_TYPE, B.ACTUAL_PAY, B.PRODUCT_ID, B.CLOB_ID, B.CLAIM_LIAB_ID, B.ITEM_ID, 
			B.IS_COMMON, B.LIAB_START_DATE, B.BUSI_PROD_CODE, B.IS_WAIVED, B.SUB_CASE_ID, 
			B.AMOUNT_BASIC_PAY, B.WAIVE_ITEM, B.WAIVE_REASON, B.BUSI_ITEM_ID, B.POLICY_ID, B.LIAB_ADJUST_REASON, B.MAKEUP_BASIC_PAY, 
			B.RESIST_FLAG, B.LIABILITY_STATUS, B.WAIVE_START, B.ADJUST_PAY, B.LIAB_ID, 
			B.BONUS_BASIC_PAY, B.LIAB_NAME, B.CASE_ID, B.ADJUST_REMARK, B.LIAB_CONCLUSION, B.POLICY_CODE, B.WAIVE_AMT, 
			B.WAIVE_END, B.CALC_PAY, B.BASIC_PAY, B.LIAB_END_DATE, B.ADVANCE_DATE, B.CLM_AFTER_STATE, B.IS_SUBSIDY,B.IS_INSTALMENT,B.IS_MUST_SIGN,
			B.RELA_MASTER_BUSI_END_FLAG,B.RELA_MASTER_BUSI_STOP_PAY_FLAG,B.RELA_MAS_BUSI_AMOU_BONUS_FLAG,B.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,B.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			B.SURPLUS_EFFECT_AMOUNT,B.INSTALMENT_TOTAL_PAY,B.LIAB_RISK_LABEL FROM (
					SELECT ROWNUM RN, A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.INSTALMENT_TOTAL_PAY,A.OTHER_REASON,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- zhangjy 查询理赔给付责任理算信息 -->
    <select id="JRQD_findClaimLiaByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.CLAIM_LIAB_ID, A.CASE_ID, A.SUB_CASE_ID, A.POLICY_ID, A.POLICY_CODE, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE, A.ITEM_ID, A.PRODUCT_ID, A.LIAB_ID, A.CLAIM_TYPE, A.BASIC_PAY, A.CALC_PAY, A.ADJUST_PAY, A.ACTUAL_PAY, A.ADVANCE_PAY, A.ADVANCE_DATE, A.IS_WAIVED, A.WAIVE_ITEM, A.WAIVE_START, A.WAIVE_END, A.WAIVE_REASON, A.WAIVE_AMT, A.LIAB_NAME, A.LIAB_START_DATE, A.LIAB_END_DATE, A.LIAB_CONCLUSION, A.REJECT_CODE,a.other_reason, A.IS_COMMON, A.RESIST_FLAG, A.LIAB_ADJUST_REASON, A.ADJUST_REMARK, A.CLOB_ID, A.INSERT_BY, A.INSERT_TIME, A.INSERT_TIMESTAMP, A.UPDATE_BY, A.UPDATE_TIME, A.UPDATE_TIMESTAMP, A.AMOUNT_BASIC_PAY, A.BONUS_BASIC_PAY, A.MAKEUP_BASIC_PAY, A.LIABILITY_STATUS, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT, A.IS_MUST_SIGN,A.INSTALMENT_TOTAL_PAY,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.CASE_ID = #{case_id} ]]>
    </select>
    <select id="JRQD_findClaimLiabByConditions" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.INSTALMENT_TOTAL_PAY,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.OTHER_REASON,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE ROWNUM <=  1000  ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
    </select>
    <!-- 查询单条数据 -->
    <select id="JRQD_findClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.INSTALMENT_TOTAL_PAY,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- liulei_wb 查询所有责任关联产品表 -->
	<select id="JRQD_findAllClaimLiabAndBusiness" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN, 
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,A.LIAB_RISK_LABEL,A.REJECT_CODE,B.PRODUCT_CATEGORY2,B.PRODUCT_CATEGORY3,B.PRODUCT_NAME_SYS FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_BUSINESS_PRODUCT B WHERE ROWNUM <=  1000 AND A.BUSI_PROD_CODE = B.PRODUCT_CODE_SYS]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<if test=" product_category2  != null  and  product_category2  != ''  "><![CDATA[ AND B.PRODUCT_CATEGORY2 = #{product_category2} ]]></if>
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- liulei_wb 查询所有责任的日期 -->
	<select id="JRQD_findAllClaimLiabDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT A.LIAB_START_DATE FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE 1=1]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.LIAB_START_DATE ASC]]> 
	</select>
	<!-- liulei_wb 查询所有责任的理算金额之和 -->
	<select id="JRQD_findAllClaimLiabCalcPay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT SUM(A.CALC_PAY) CALC_PAY FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE 1=1]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.LIAB_START_DATE DESC]]> 
	</select>
	<!-- 根据liabId集合查询claimLiab -->
	<select id="JRQD_findAllInteractionClaimLiab" resultType="java.util.Map" parameterType="java.util.Map"> 
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY,B.Case_No, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, C.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.OTHER_REASON,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,B.SERIOUS_DISEASE,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A,APP___CLM__DBUSER.T_CLAIM_CASE B,APP___CLM__DBUSER.T_CLAIM_SUB_LIAB C WHERE A.CASE_ID=B.CASE_ID AND C.CASE_ID=B.CASE_ID AND C.CLAIM_LIAB_ID=A.CLAIM_LIAB_ID
			AND A.LIAB_CONCLUSION != '5']]>
		<![CDATA[ AND C.LIAB_ID IN (${interaction_liab_id})]]>
		<![CDATA[ AND B.AUDIT_DECISION != '3']]>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND B.CASE_ID != #{case_id} ]]></if>
		<!-- <foreach collection="interaction_liab_id" item="list" index="index"
				open="(" close=")" separator=",">#{list}</foreach> -->
		<if test=" case_status != null and case_status != '' "><![CDATA[ AND B.CASE_STATUS = #{case_status}]]></if>
		<if test=" claim_date != null"><![CDATA[ AND A.LIAB_START_DATE<=#{claim_date} AND A.LIAB_END_DATE>#{claim_date}]]></if>
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- LIULEI START-->
	<select id="JRQD_findLiabByLiabIdAndInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.LIAB_CODE,A.CLAIM_LIAB_ID, A.CASE_ID, A.SUB_CASE_ID, A.POLICY_ID, A.POLICY_CODE, A.BUSI_ITEM_ID, A.BUSI_PROD_CODE, A.ITEM_ID, A.PRODUCT_ID, A.LIAB_ID, A.CLAIM_TYPE, A.BASIC_PAY, A.CALC_PAY, A.ADJUST_PAY, A.ACTUAL_PAY, A.ADVANCE_PAY, A.ADVANCE_DATE, A.IS_WAIVED, A.WAIVE_ITEM, A.WAIVE_START, A.WAIVE_END, A.WAIVE_REASON, A.WAIVE_AMT, A.LIAB_NAME, A.LIAB_START_DATE, A.LIAB_END_DATE, A.LIAB_CONCLUSION, A.IS_COMMON, A.RESIST_FLAG, A.LIAB_ADJUST_REASON, A.ADJUST_REMARK, A.CLOB_ID, A.INSERT_BY, A.INSERT_TIME, A.INSERT_TIMESTAMP, A.UPDATE_BY, A.UPDATE_TIME, A.UPDATE_TIMESTAMP, A.AMOUNT_BASIC_PAY, A.BONUS_BASIC_PAY, A.MAKEUP_BASIC_PAY, A.LIABILITY_STATUS, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT, A.IS_MUST_SIGN,
		A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,A.LIAB_RISK_LABEL
				  FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_CASE B
				 WHERE A.CASE_ID = B.CASE_ID
				   AND B.INSURED_ID = #{insured_id}
				   AND A.LIAB_ID = #{liab_id}
				   AND B.CASE_STATUS = 80]]>
	</select>
	<!-- END -->
	<!-- liulei_wb 根据案件状态查询理赔给付责任表 -->
	<select id="JRQD_findAllClaimLiabByCaseStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.CASE_NO, A.LIAB_CONCLUSION, A.LIAB_ID,A.ACTUAL_PAY,A.LIABILITY_STATUS,A.CASE_ID,A.CLAIM_LIAB_ID,A.SURPLUS_EFFECT_AMOUNT
					  FROM APP___CLM__DBUSER.T_CLAIM_LIAB     A,
					       APP___CLM__DBUSER.T_CLAIM_CASE     B
					 WHERE A.CASE_ID = B.CASE_ID
					   AND B.CASE_STATUS = #{case_status}]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- liulei_wb 根据案件状态查询理赔给付责任表(排除掉拒付的) -->
	<select id="JRQD_findAllClaimLiabByCaseStatusRefusal" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.CASE_NO, A.LIAB_CONCLUSION, A.LIABILITY_STATUS,A.CASE_ID,A.CLAIM_LIAB_ID,A.SURPLUS_EFFECT_AMOUNT,A.LIAB_RISK_LABEL
					  FROM APP___CLM__DBUSER.T_CLAIM_LIAB     A,
					       APP___CLM__DBUSER.T_CLAIM_CASE     B
					 WHERE A.CASE_ID = B.CASE_ID
					   AND B.CASE_STATUS = #{case_status}
					   AND A.LIAB_CONCLUSION != '5']]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- add by zhaoyq 根据责任组ID查询既往赔付的金额 -->
	<select id="JRQD_findSumAmountByProductID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT nvl(sum(li.actual_pay),0) as actual_pay FROM APP___CLM__DBUSER.T_CLAIM_LIAB li, APP___CLM__DBUSER.T_CLAIM_CASE ca where li.case_id = ca.case_id and li.liab_conclusion!=5]]>
		<if test=" product_id  != null "><![CDATA[ AND li.product_id = #{product_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND li.policy_id = #{policy_id} ]]></if>
		<if test=" busi_prod_code  != null and busi_prod_code != '' "><![CDATA[ AND li.busi_prod_code = #{busi_prod_code} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND li.liab_id = #{liab_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND ca.case_id <> #{case_id} ]]></if>
		<if test=" case_status!=null "><![CDATA[and ca.case_status=#{case_status}]]></if>
	</select>
	<!-- 赔付通知书 -->
	<select id="JRQD_findDistinctBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT distinct busi_prod_code FROM APP___CLM__DBUSER.T_CLAIM_LIAB where policy_code=#{policy_code} and case_id=#{case_id} ]]>
	</select>
	<select id="JRQD_findAllAlwaysClaimPre" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, C.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,B.CASE_NO ,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A,APP___CLM__DBUSER.T_CLAIM_CASE B,APP___CLM__DBUSER.T_CLAIM_SUB_LIAB C WHERE A.CLAIM_LIAB_ID=C.CLAIM_LIAB_ID AND A.CASE_ID=B.CASE_ID AND C.CASE_ID=B.CASE_ID AND B.CASE_STATUS=#{case_status} ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- 保全 保单下给付各种保险金 -->
	<select id="JRQD_findClaimLiabByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.POLICY_CODE,A.PRODUCT_ID,A.BUSI_ITEM_ID,A.LIAB_ID,A.LIAB_NAME,A.LIAB_CONCLUSION, A.BUSI_PROD_CODE FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE 1 = 1 AND A.LIAB_CONCLUSION !=5 ]]>
		 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]>
	</select>
	<!-- add by zhaoyq -->
	<select id="JRQD_findClaimLiabMessByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT a.case_id,b.case_no,c.claim_date, a.liab_name,b.serious_disease
                    FROM APP___CLM__DBUSER.t_claim_liab a, APP___CLM__DBUSER.t_claim_case b, APP___CLM__DBUSER.t_claim_sub_case c
                   WHERE a.case_id = b.case_id
                     and b.case_id = c.case_id
                     and b.SERIOUS_DISEASE not like 'qz%'
                     and b.case_status = 80
                     and c.claim_type = 03
                     and a.policy_code = #{policy_code}
                     and a.liab_id = 3001
                     order by c.claim_date
         ]]>
	</select>
	<!-- add by zhaoyq -->
	<select id="JRQD_findClaimLiabMessByPolicyCodeNew" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID,B.CASE_NO,C.CLAIM_DATE, A.LIAB_NAME,B.SERIOUS_DISEASE
                    FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_CASE B, APP___CLM__DBUSER.T_CLAIM_SUB_CASE C
                   WHERE A.CASE_ID = B.CASE_ID
                     AND B.CASE_ID = C.CASE_ID
                     AND B.SERIOUS_DISEASE NOT LIKE 'QZ%'
                     AND B.CASE_STATUS = 80
                     AND C.CLAIM_TYPE = 03
                     AND A.POLICY_CODE = #{policy_code}
                     AND A.LIAB_ID IN (${liab_id_list})
                     ORDER BY C.CLAIM_DATE
         ]]>
	</select>
	
	<select id="JRQD_findLiabCodeByClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select re.liab_id, re.product_id, re.liba_code
			  from APP___CLM__DBUSER.t_liab_pay_relative re
			 where rownum < 1000
			  and re.product_id = #{product_id}
			  and re.liab_id = #{liab_id}
		]]>
	</select>
	<!-- 查询所有已赔付的责任 -->
	<select id="JRQD_findAllPayClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,B.CASE_NO,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A,APP___CLM__DBUSER.T_CLAIM_CASE B WHERE ROWNUM <=  1000 AND A.LIAB_CONCLUSION!=5
      		AND (SELECT COUNT(*) FROM APP___CLM__DBUSER.T_CLAIM_CASE C WHERE C.RELATED_NO =B.CASE_NO )=0 AND A.CASE_ID=B.CASE_ID  ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- 查询所有操作 -->
	<select id="JRQD_findAllCalcClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN, 
			A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
			A.SURPLUS_EFFECT_AMOUNT,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON,B.CLAIM_DATE,A.LIAB_RISK_LABEL FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_SUB_CASE B WHERE ROWNUM <=  1000 AND A.SUB_CASE_ID=B.SUB_CASE_ID  ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- 查询所有已赔付的责任 -->
	<select id="JRQD_findClaimLiaByCaseIdAndByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.BUSI_PROD_CODE,a.busi_item_id,a.actual_pay,a.policy_code  FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.POLICY_ID=#{policy_id} AND A.CASE_ID=#{case_id}]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="JRQD_findFirstClaimDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT CLAIM_DATE FROM (SELECT SC.CLAIM_DATE FROM APP___CLM__DBUSER.T_CLAIM_LIAB CL,APP___CLM__DBUSER.T_CLAIM_CASE CC,APP___CLM__DBUSER.T_CLAIM_SUB_CASE SC WHERE CL.CASE_ID=CC.CASE_ID AND CL.SUB_CASE_ID = SC.SUB_CASE_ID AND CC.CASE_STATUS=80  AND (SELECT COUNT(*) FROM APP___CLM__DBUSER.T_CLAIM_CASE C WHERE C.RELATED_NO =CC.CASE_NO )=0 AND CL.ITEM_ID=#{item_id} AND CL.LIAB_ID=#{liab_id} ORDER BY SC.CLAIM_DATE) WHERE ROWNUM=1]]>
	</select>
	
	<!-- 547产品规则用 -->
	<select id="JRQD_findClaimLiaNotCaseId" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[SELECT DISTINCT B.CASE_ID
                   FROM APP___CLM__DBUSER.T_CLAIM_LIAB A,APP___CLM__DBUSER.T_CLAIM_CASE B
                  WHERE A.CASE_ID = B.CASE_ID
                    AND A.POLICY_CODE = #{policy_code}
                    AND A.BUSI_PROD_CODE = #{busi_prod_code}
                    AND A.LIAB_ID = #{liab_id}
                    AND A.CASE_ID != #{case_id}
                    AND B.CASE_STATUS = 80
                  ORDER BY B.CASE_ID
           ]]>
	</select>
	<select id="JRQD_findLiabByClaimDate" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[SELECT DISTINCT C.CLAIM_DATE
                    FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_CASE B, APP___CLM__DBUSER.T_CLAIM_SUB_CASE C
                   WHERE A.CASE_ID = B.CASE_ID
                     AND B.CASE_ID = C.CASE_ID
                     AND A.POLICY_CODE = #{policy_code}
                     AND A.BUSI_PROD_CODE = #{busi_prod_code}
                     AND A.LIAB_ID = #{liab_id}
                     AND A.CASE_ID != #{case_id}
                     AND B.CASE_STATUS = 80
                     AND C.CLAIM_DATE > #{s_date}
                     AND C.CLAIM_DATE < #{e_date}
           ]]>
	</select>
	<!-- 本责任既往赔付的金额 -->
	<select id="JRQD_findLiabSumActualPay" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[SELECT SUM(A.ACTUAL_PAY) ACTUAL_PAY
			          FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_CASE B
			      WHERE (SELECT COUNT(*) FROM APP___CLM__DBUSER.T_CLAIM_CASE C WHERE C.RELATED_NO=B.CASE_NO)=0 
			      AND B.CASE_STATUS=80
			      AND A.LIAB_CONCLUSION !=5
			      AND A.CASE_ID = B.CASE_ID AND A.ITEM_ID=#{item_id} AND A.LIAB_ID IN (${liab_id_list})
           ]]>
	</select>
	<!-- 本责任既往赔付的金额 -->
	<select id="JRQD_findSumActualPayByInsured" resultType="java.util.Map" parameterType="java.util.Map">
	     <![CDATA[SELECT SUM(A.ACTUAL_PAY) ACTUAL_PAY
					  FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_CASE B
					 WHERE (SELECT COUNT(*)
					          FROM APP___CLM__DBUSER.T_CLAIM_CASE C
					         WHERE C.RELATED_NO = B.CASE_NO) = 0
					   AND A.CASE_ID = B.CASE_ID
					   AND B.INSURED_ID=#{insured_id}
           ]]>
           <include refid="JRQD_claimLiabWhereCondition" />
	</select>
	<!-- 汇总赔案层赔付金额 -->
	<select id="JRQD_findClaimCaseSubPay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(A.CALC_PAY) CALC_PAY,SUM(case when  A.LIAB_CONCLUSION != 5 then nvl(A.ADJUST_PAY,0) else 0 end ) ADJUST_PAY, SUM(A.ACTUAL_PAY) ACTUAL_PAY,
			sum(case when  A.LIAB_CONCLUSION = 5 then nvl(A.ADJUST_PAY,0) else 0 end ) REJECT_PAY
			from APP___CLM__DBUSER.T_CLAIM_LIAB a WHERE 1=1 
			AND A.CASE_ID = #{case_id}
		]]>
	</select>
	<!-- add by zhaoyq  打印用-->
	<select id="JRQD_findSumCalcAndAdvancePay" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[SELECT SUM(A.CALC_PAY) AS CALC_PAY, SUM(A.ADVANCE_PAY) AS ADVANCE_PAY, SUM(ADJUST_PAY) AS ADJUST_PAY
	               FROM APP___CLM__DBUSER.T_CLAIM_LIAB A 
	              WHERE A.CASE_ID = #{case_id} AND A.POLICY_CODE = #{policy_code}]]>
	</select>
	<!-- add by fanjj  打印用（赔付依据与说明）-->
	<select id="JRQD_findSumCalcAndAdvancePayOne" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[SELECT SUM(A.CALC_PAY) AS CALC_PAY, SUM(A.ADVANCE_PAY) AS ADVANCE_PAY, SUM(ACTUAL_PAY) AS ACTUAL_PAY
	               FROM APP___CLM__DBUSER.T_CLAIM_LIAB A 
	              WHERE A.CASE_ID = #{case_id} AND A.POLICY_CODE = #{policy_code}]]>
	</select>
	
	<!-- 查询赔案的预付录入人 -->
	<select id="JRQD_findAdvanceOperatorByCase" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.ADVANCE_INPUT_OPERATOR FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.CASE_ID = #{case_id} AND A.ADVANCE_PAY > 0  AND A.ADVANCE_INPUT_OPERATOR IS NOT NULL  ]]>
	</select>
	
	<!-- caoyy -->
	<select id="JRQD_findClaimLiabByCaseIdDistict" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.POLICY_CODE FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE ROWNUM <=  1000  AND A.CASE_ID = #{case_id}]]>
	</select>
	<!-- end -->
	
	
	<select id="JRQD_findClaimLiabByLiabId" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
	    SELECT TCL.POLICY_ID,
       TCL.BUSI_ITEM_ID,
       TCL.ITEM_ID,
       TCL.PRODUCT_ID,
       TCL.LIAB_ID,
       TCL.LIAB_START_DATE,
       TCL.CALC_PAY
  FROM APP___CLM__DBUSER.T_CLAIM_LIAB TCL
 WHERE TCL.CASE_ID = #{case_id}
   AND TCL.LIAB_ID = #{liab_id}
   AND TCL.POLICY_ID = #{policy_id}
   AND TCL.BUSI_ITEM_ID = #{busi_item_id}
 GROUP BY TCL.POLICY_ID,
          TCL.BUSI_ITEM_ID,
          TCL.ITEM_ID,
          TCL.PRODUCT_ID,
          TCL.LIAB_ID,
          TCL.LIAB_START_DATE,
          TCL.CALC_PAY
	    ]]>
	</select>
	<select id="JRQD_queryClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
	    SELECT 
        to_char(wm_concat(DISTINCT B.Claim_Type)) CLAIM_TYPE,
        B.POLICY_CODE POLICY_CODE,
         to_char(wm_concat(DISTINCT A.REFUSAL_BENE_NAME)) REFUSAL_BENE_NAME,
        to_char(wm_concat(DISTINCT B.LIAB_CONCLUSION)) LIAB_CONCLUSIONSTR,
        to_char(wm_concat(B.REJECT_CODE)) REJECT_CODE,
        to_char(wm_concat(B.REJECT_PROOF)) REJECT_PROOF,
        to_char(wm_concat(B.OTHER_REASON)) OTHER_REASON
   FROM DEV_CLM.T_CLAIM_POLICY A
   left join DEV_CLM.T_CLAIM_LIAB B 
     ON A.Policy_Id = B.Policy_Id
     and a.case_id = b.case_id
  where A.Case_Id = #{case_id}
  and B.Liab_Conclusion = 5 group by B.Policy_Code
	    ]]>
	</select>
	<select id="JRQD_queryPayNames" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
 SELECT BENE.BENE_NAME
   FROM DEV_CLM.T_CLAIM_BENE BENE, DEV_CLM.T_CLAIM_PAY PAY
  WHERE BENE.BENE_ID = PAY.BENE_ID
    AND PAY.CASE_ID = #{case_id}
    AND PAY.PAY_AMOUNT != 0
    AND PAY.POLICY_CODE = #{policy_code}

	    ]]>
	</select>
	<select id="JRQD_queryPolicyName" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ 
	    SELECT B.CUSTOMER_NAME
  FROM DEV_CLM.T_CUSTOMER B
 WHERE B.CUSTOMER_ID IN (SELECT INS.CUSTOMER_ID
                           FROM DEV_CLM.T_CONTRACT_BENE INS
                          WHERE INS.CASE_ID =  #{case_id}
                          AND INS.POLICY_CODE = #{policy_code}
                            AND INS.CUR_FLAG = '1')
	    ]]>
	</select>
	<!-- 查询责任信息按照出险日期排序 -->
	<select id="JRQD_findAllClaimLiabAboutClaimDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A1.CLAIM_DATE,A2.LIAB_NAME,A2.LIAB_CATEGORY,A3.CASE_STATUS,A3.SERIOUS_DISEASE,A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
      					 A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID,A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
      					 A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID,A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
      					 A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN, 
      					 A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
      					 A.SURPLUS_EFFECT_AMOUNT,A.INSTALMENT_TOTAL_PAY,A.ADVANCE_INPUT_OPERATOR,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.OTHER_REASON,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.LIAB_RISK_LABEL
  				  FROM APP___CLM__DBUSER.T_CLAIM_LIAB     A,
       				   APP___CLM__DBUSER.T_CLAIM_SUB_CASE A1,
       				   APP___CLM__DBUSER.T_LIABILITY A2,
       				   APP___CLM__DBUSER.T_CLAIM_CASE A3
 				 WHERE ROWNUM <=  1000 AND A.CASE_ID=A3.CASE_ID
 				   AND A.LIAB_ID=A2.LIAB_ID
 				   AND A3.CASE_STATUS = '80'
 				   AND A.SUB_CASE_ID = A1.SUB_CASE_ID]]>
		<include refid="JRQD_claimLiabWhereCondition" />
		<![CDATA[  ORDER BY A1.CLAIM_DATE,A.CLAIM_LIAB_ID]]> 
	</select>
	<!-- 查询赔付明细ID -->
	<select id="JRQD_findAllClaimLiabByClaimLiabIdList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_LIAB_ID,A.CLOB_ID FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.CLAIM_LIAB_ID IN (#{claim_liab_id_list}) ORDER BY A.CLAIM_LIAB_ID]]>
	</select>
		<!-- 查询相关保单是否挂起 -->
	<select id="JRQD_findAllClaimLiabLock" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.CLAIM_DATE,A.LIAB_CODE,A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLOB_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
      A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
      A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
      A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
      A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, A.INSERT_TIME,
      A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN, 
      A.RELA_MASTER_BUSI_END_FLAG,A.RELA_MASTER_BUSI_STOP_PAY_FLAG,A.RELA_MAS_BUSI_AMOU_BONUS_FLAG,A.RELA_MAS_BUSI_REDU_AMOUNT_FLAG,A.RELA_MAS_BUSI_CASH_BONUS_FLAG,
      A.SURPLUS_EFFECT_AMOUNT,A.INSTALMENT_TOTAL_PAY,A.ADVANCE_INPUT_OPERATOR,A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK,A.ACCU_BONUS_AMOUNT,A.BEFORE_SURPLUS_EFFECT_AMOUNT,A.OTHER_REASON ,A.LIAB_RISK_LABEL
       	FROM APP___CLM__DBUSER.T_CLAIM_LIAB     A,
       	APP___CLM__DBUSER.T_CLAIM_SUB_CASE B
 	   	WHERE A.CASE_ID = B.CASE_ID
   		AND A.CLAIM_TYPE = B.CLAIM_TYPE]]>
		<include refid="JRQD_claimLiabWhereCondition" />
	</select>
	<!-- 根据理赔给付责任理算ID查询赔付过程 -->
	<select id="JRQD_findClaimClobByClaimLiabId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TO_CHAR(B.CONTENT) as txt_content
				   FROM APP___CLM__DBUSER.t_claim_sub_liab               A,
				        APP___CLM__DBUSER.t_clob                 B,
				        APP___CLM__DBUSER.T_CLAIM_LIAB c
				  where A.CLOB_ID = B.CLOB_ID
				    and c.claim_liab_id = a.claim_liab_id
				    AND c.claim_liab_id = #{claim_liab_id}]]>
	</select>
	<select id="JRQD_findAllClaimLiabByCaseID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.BUSI_ITEM_ID,A.POLICY_ID, A.POLICY_CODE, A.BUSI_PROD_CODE 
					FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE ROWNUM <=  1000     ]]>
		<include refid="JRQD_claimLiabWhereCondition" />
	</select>
	<!-- 根据赔案ID和理赔类型，查询此赔案下此理赔类型赔付的钱 -->
	<select id="JRQD_findClaimLiabPayByClaimType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(A.ACTUAL_PAY) ACTUAL_PAY
		FROM APP___CLM__DBUSER.T_CLAIM_LIAB A WHERE A.CASE_ID = #{case_id} AND CLAIM_TYPE= #{claim_type}]]>
	</select>
	<!-- 根据赔案ID和医疗，查询保单号和责任起期和责任止期 -->
	<select id="JRQD_queryPolicyCodeLiabDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT C.BUSI_PROD_CODE, C.POLICY_CODE,C.LIAB_START_DATE,C.LIAB_END_DATE
		FROM APP___CLM__DBUSER.T_CLAIM_LIAB C WHERE   CLAIM_TYPE= '08'  AND C.CASE_ID = #{case_id} AND to_date(to_char(C.LIAB_START_DATE, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= to_date(#{claim_date}, 'YYYY-MM-DD') and to_date(to_char(C.LIAB_END_DATE, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= to_date(#{claim_date}, 'YYYY-MM-DD')]]>
	</select>
	<!-- 根据赔案ID和医疗，查询保单号和责任起期和责任止期 -->
	<select id="JRQD_findAllAdjustPayAndActualPayByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT SUM((SELECT NVL(SUM(A.PAY_AMOUNT), 0)
               FROM DEV_CLM.T_CLAIM_ADJUST_BUSI A
              WHERE A.ADJUST_TYPE IN
                    ('1', '2', '8', '9', '10', '13', '19', '20', '22')
                AND A.CASE_ID = #{case_id}) +
            (SELECT NVL(SUM(A.ACTUAL_PAY), 0)
               FROM DEV_CLM.T_CLAIM_LIAB A
              WHERE A.LIAB_CONCLUSION != 5
                AND A.CASE_ID = #{case_id})) SUM_PAY_AMOUNT
   				FROM DUAL
		]]>
	</select>
	<!-- 根据赔案ID和医疗，查询保单号和责任起期和责任止期 -->
	<select id="JRQD_findClaimLiabByAccidentId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT a.liab_conclusion,a.case_id,a.policy_code,a.busi_item_id,a.item_id,a.actual_pay,b.serious_disease
		  FROM dev_clm.t_claim_liab a
		 inner join dev_clm.t_claim_case b
		    on a.case_id = b.case_id
		 inner join dev_clm.t_claim_accident c
		    on b.accident_id=c.accident_id
		    WHERE c.accident_id =  #{accident_id} and b.case_status='80'
		]]>
		<include refid="JRQD_claimLiabWhereCondition" />
	</select>
	<select id="JRQD_findClaimLiabTotalBySeriousDisease" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT count(1)
					  FROM APP___CLM__DBUSER.T_CLAIM_LIAB     A,
					       APP___CLM__DBUSER.T_CLAIM_CASE     B
					 WHERE A.CASE_ID = B.CASE_ID
					   AND A.LIAB_CONCLUSION<>'5'
					   AND B.CASE_STATUS = #{case_status}
					   AND B.SERIOUS_DISEASE = #{serious_disease}
					   AND A.ITEM_ID=#{item_id}]]>
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<!-- 解挂时如果存在未结案案件不做解挂处理 -->
	<select id="JRQD_findClaimLiabAlwaysclaim" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT DISTINCT A.CASE_ID, B.POLICY_CODE, A.CASE_STATUS
		  FROM DEV_CLM.T_CLAIM_CASE A, DEV_CLM.T_CLAIM_LIAB B
		 WHERE A.CASE_ID = B.CASE_ID
		   AND A.CASE_STATUS NOT IN ('80', '90', '99')
		   AND A.CASE_ID != #{case_id}
		   AND B.POLICY_CODE = #{policy_code}
		 ]]> 
	</select>
	
</mapper>
