<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.CodePO">
<!--
	<sql id="JRQD_codeWhereCondition">
		<if test=" code_column != null and code_column != ''  "><![CDATA[ AND A.CODE_COLUMN = #{code_column} ]]></if>
		<if test=" table_name != null and table_name != ''  "><![CDATA[ AND A.TABLE_NAME = #{table_name} ]]></if>
		<if test=" system_id != null and system_id != ''  "><![CDATA[ AND A.SYSTEM_ID = #{system_id} ]]></if>
		<if test=" where_clause != null and where_clause != ''  "><![CDATA[ AND <PERSON><PERSON>WHERE_CLAUSE = #{where_clause} ]]></if>
		<if test=" tag_style != null and tag_style != ''  "><![CDATA[ AND A.TAG_STYLE = #{tag_style} ]]></if>
		<if test=" sql_content != null and sql_content != ''  "><![CDATA[ AND A.SQL_CONTENT = #{sql_content} ]]></if>
		<if test=" multi_lang != null and multi_lang != ''  "><![CDATA[ AND A.MULTI_LANG = #{multi_lang} ]]></if>
		<if test=" remote_table_url != null and remote_table_url != ''  "><![CDATA[ AND A.REMOTE_TABLE_URL = #{remote_table_url} ]]></if>
		<if test=" cache_refresh_second  != null "><![CDATA[ AND A.CACHE_REFRESH_SECOND = #{cache_refresh_second} ]]></if>
		<if test=" desc_column_style != null and desc_column_style != ''  "><![CDATA[ AND A.DESC_COLUMN_STYLE = #{desc_column_style} ]]></if>
		<if test=" order_column != null and order_column != ''  "><![CDATA[ AND A.ORDER_COLUMN = #{order_column} ]]></if>
		<if test=" id_column_type != null and id_column_type != ''  "><![CDATA[ AND A.ID_COLUMN_TYPE = #{id_column_type} ]]></if>
		<if test=" extend_search_column != null and extend_search_column != ''  "><![CDATA[ AND A.EXTEND_SEARCH_COLUMN = #{extend_search_column} ]]></if>
		<if test=" table_desc != null and table_desc != ''  "><![CDATA[ AND A.TABLE_DESC = #{table_desc} ]]></if>
		<if test=" need_cache != null and need_cache != ''  "><![CDATA[ AND A.NEED_CACHE = #{need_cache} ]]></if>
		<if test=" not_exist_msg_id != null and not_exist_msg_id != ''  "><![CDATA[ AND A.NOT_EXIST_MSG_ID = #{not_exist_msg_id} ]]></if>
		<if test=" default_value != null and default_value != ''  "><![CDATA[ AND A.DEFAULT_VALUE = #{default_value} ]]></if>
		<if test=" id_column != null and id_column != ''  "><![CDATA[ AND A.ID_COLUMN = #{id_column} ]]></if>
		<if test=" desc_column != null and desc_column != ''  "><![CDATA[ AND A.DESC_COLUMN = #{desc_column} ]]></if>
		<if test=" need_conv != null and need_conv != ''  "><![CDATA[ AND A.NEED_CONV = #{need_conv} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryCodeByTableNameCondition">
		<if test=" table_name != null and table_name != '' "><![CDATA[ AND A.TABLE_NAME = #{table_name} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addCode"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_UDMP_CODE(
				CODE_COLUMN, TABLE_NAME, SYSTEM_ID, WHERE_CLAUSE, TAG_STYLE, SQL_CONTENT, MULTI_LANG, 
				REMOTE_TABLE_URL, CACHE_REFRESH_SECOND, DESC_COLUMN_STYLE, ORDER_COLUMN, ID_COLUMN_TYPE, EXTEND_SEARCH_COLUMN, TABLE_DESC, 
				NEED_CACHE, NOT_EXIST_MSG_ID, DEFAULT_VALUE, ID_COLUMN, DESC_COLUMN, NEED_CONV ) 
			VALUES (
				#{code_column, jdbcType=VARCHAR}, #{table_name, jdbcType=VARCHAR} , #{system_id, jdbcType=VARCHAR} , #{where_clause, jdbcType=VARCHAR} , #{tag_style, jdbcType=VARCHAR} , #{sql_content, jdbcType=VARCHAR} , #{multi_lang, jdbcType=VARCHAR} 
				, #{remote_table_url, jdbcType=VARCHAR} , #{cache_refresh_second, jdbcType=NUMERIC} , #{desc_column_style, jdbcType=VARCHAR} , #{order_column, jdbcType=VARCHAR} , #{id_column_type, jdbcType=VARCHAR} , #{extend_search_column, jdbcType=VARCHAR} , #{table_desc, jdbcType=VARCHAR} 
				, #{need_cache, jdbcType=VARCHAR} , #{not_exist_msg_id, jdbcType=VARCHAR} , #{default_value, jdbcType=VARCHAR} , #{id_column, jdbcType=VARCHAR} , #{desc_column, jdbcType=VARCHAR} , #{need_conv, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteCode" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_UDMP_CODE WHERE TABLE_NAME = #{table_name} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_UDMP_CODE ]]>
		<set>
		<trim suffixOverrides=",">
			CODE_COLUMN = #{code_column, jdbcType=VARCHAR} ,
			SYSTEM_ID = #{system_id, jdbcType=VARCHAR} ,
			WHERE_CLAUSE = #{where_clause, jdbcType=VARCHAR} ,
			TAG_STYLE = #{tag_style, jdbcType=VARCHAR} ,
			SQL_CONTENT = #{sql_content, jdbcType=VARCHAR} ,
			MULTI_LANG = #{multi_lang, jdbcType=VARCHAR} ,
			REMOTE_TABLE_URL = #{remote_table_url, jdbcType=VARCHAR} ,
		    CACHE_REFRESH_SECOND = #{cache_refresh_second, jdbcType=NUMERIC} ,
			DESC_COLUMN_STYLE = #{desc_column_style, jdbcType=VARCHAR} ,
			ORDER_COLUMN = #{order_column, jdbcType=VARCHAR} ,
			ID_COLUMN_TYPE = #{id_column_type, jdbcType=VARCHAR} ,
			EXTEND_SEARCH_COLUMN = #{extend_search_column, jdbcType=VARCHAR} ,
			TABLE_DESC = #{table_desc, jdbcType=VARCHAR} ,
			NEED_CACHE = #{need_cache, jdbcType=VARCHAR} ,
			NOT_EXIST_MSG_ID = #{not_exist_msg_id, jdbcType=VARCHAR} ,
			DEFAULT_VALUE = #{default_value, jdbcType=VARCHAR} ,
			ID_COLUMN = #{id_column, jdbcType=VARCHAR} ,
			DESC_COLUMN = #{desc_column, jdbcType=VARCHAR} ,
			NEED_CONV = #{need_conv, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE TABLE_NAME = #{table_name} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findCodeByTableName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CODE_COLUMN, A.TABLE_NAME, A.SYSTEM_ID, A.WHERE_CLAUSE, A.TAG_STYLE, A.SQL_CONTENT, A.MULTI_LANG, 
			A.REMOTE_TABLE_URL, A.CACHE_REFRESH_SECOND, A.DESC_COLUMN_STYLE, A.ORDER_COLUMN, A.ID_COLUMN_TYPE, A.EXTEND_SEARCH_COLUMN, A.TABLE_DESC, 
			A.NEED_CACHE, A.NOT_EXIST_MSG_ID, A.DEFAULT_VALUE, A.ID_COLUMN, A.DESC_COLUMN, A.NEED_CONV FROM APP___CLM__DBUSER.T_UDMP_CODE A WHERE 1 = 1 
			AND A.TABLE_NAME = #{table_name}    ORDER BY A.TABLE_NAME ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CODE_COLUMN, A.TABLE_NAME, A.SYSTEM_ID, A.WHERE_CLAUSE, A.TAG_STYLE, A.SQL_CONTENT, A.MULTI_LANG, 
			A.REMOTE_TABLE_URL, A.CACHE_REFRESH_SECOND, A.DESC_COLUMN_STYLE, A.ORDER_COLUMN, A.ID_COLUMN_TYPE, A.EXTEND_SEARCH_COLUMN, A.TABLE_DESC, 
			A.NEED_CACHE, A.NOT_EXIST_MSG_ID, A.DEFAULT_VALUE, A.ID_COLUMN, A.DESC_COLUMN, A.NEED_CONV FROM APP___CLM__DBUSER.T_UDMP_CODE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TABLE_NAME ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CODE_COLUMN, A.TABLE_NAME, A.SYSTEM_ID, A.WHERE_CLAUSE, A.TAG_STYLE, A.SQL_CONTENT, A.MULTI_LANG, 
			A.REMOTE_TABLE_URL, A.CACHE_REFRESH_SECOND, A.DESC_COLUMN_STYLE, A.ORDER_COLUMN, A.ID_COLUMN_TYPE, A.EXTEND_SEARCH_COLUMN, A.TABLE_DESC, 
			A.NEED_CACHE, A.NOT_EXIST_MSG_ID, A.DEFAULT_VALUE, A.ID_COLUMN, A.DESC_COLUMN, A.NEED_CONV FROM APP___CLM__DBUSER.T_UDMP_CODE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TABLE_NAME ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findCodeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_UDMP_CODE A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryCodeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CODE_COLUMN, B.TABLE_NAME, B.SYSTEM_ID, B.WHERE_CLAUSE, B.TAG_STYLE, B.SQL_CONTENT, B.MULTI_LANG, 
			B.REMOTE_TABLE_URL, B.CACHE_REFRESH_SECOND, B.DESC_COLUMN_STYLE, B.ORDER_COLUMN, B.ID_COLUMN_TYPE, B.EXTEND_SEARCH_COLUMN, B.TABLE_DESC, 
			B.NEED_CACHE, B.NOT_EXIST_MSG_ID, B.DEFAULT_VALUE, B.ID_COLUMN, B.DESC_COLUMN, B.NEED_CONV FROM (
					SELECT ROWNUM RN, A.CODE_COLUMN, A.TABLE_NAME, A.SYSTEM_ID, A.WHERE_CLAUSE, A.TAG_STYLE, A.SQL_CONTENT, A.MULTI_LANG, 
			A.REMOTE_TABLE_URL, A.CACHE_REFRESH_SECOND, A.DESC_COLUMN_STYLE, A.ORDER_COLUMN, A.ID_COLUMN_TYPE, A.EXTEND_SEARCH_COLUMN, A.TABLE_DESC, 
			A.NEED_CACHE, A.NOT_EXIST_MSG_ID, A.DEFAULT_VALUE, A.ID_COLUMN, A.DESC_COLUMN, A.NEED_CONV FROM APP___CLM__DBUSER.T_UDMP_CODE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TABLE_NAME ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
