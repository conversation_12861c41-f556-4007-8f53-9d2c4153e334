<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO">

	<sql id="JRQD_claimSubCaseWhereCondition">
		<if test=" claim_type != null and claim_type != ''  "><![CDATA[ AND A.CLAIM_TYPE = #{claim_type} ]]></if>
		<if test=" acc_reason  != null "><![CDATA[ AND A.ACC_REASON = #{acc_reason} ]]></if>
		<if test=" claim_date  != null  and  claim_date  != ''  "><![CDATA[ AND A.CLAIM_DATE = #{claim_date} ]]></if>
		<if test=" sub_case_id  != null "><![CDATA[ AND A.SUB_CASE_ID = #{sub_case_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryClaimSubCaseBySubCaseIdCondition">
		<if test=" sub_case_id  != null "><![CDATA[ AND A.SUB_CASE_ID = #{sub_case_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addClaimSubCase"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="sub_case_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_SUB_CASE__SUB_CASE_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_SUB_CASE(
				INSERT_TIMESTAMP, CLAIM_TYPE, ACC_REASON, UPDATE_BY, CLAIM_DATE, INSERT_TIME, SUB_CASE_ID, 
				UPDATE_TIME, UPDATE_TIMESTAMP, INSERT_BY, CASE_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{claim_type, jdbcType=VARCHAR} , #{acc_reason, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{claim_date, jdbcType=DATE} , SYSDATE , #{sub_case_id, jdbcType=NUMERIC} 
				, SYSDATE , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{case_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteClaimSubCase" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE WHERE SUB_CASE_ID = #{sub_case_id} ]]>
	</delete>
	<delete id="JRQD_deleteClaimSubCaseByCaseId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE WHERE CASE_ID = #{case_id}  ]]>
	</delete>
	<delete id="JRQD_deleteClaimSubCaseByConditions" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE WHERE CASE_ID = #{case_id} and acc_reason = #{acc_reason} and claim_type = #{claim_type} ]]>
	</delete>
<!-- 修改操作 -->
	<update id="JRQD_updateClaimSubCase" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_SUB_CASE ]]>
		<set>
		<trim suffixOverrides=",">
		<if test=" claim_type != null and claim_type != ''  ">
			CLAIM_TYPE = #{claim_type, jdbcType=VARCHAR} ,
		</if>
		    ACC_REASON = #{acc_reason, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CLAIM_DATE = #{claim_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE SUB_CASE_ID = #{sub_case_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findClaimSubCaseBySubCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A  WHERE 1 = 1 AND SUB_CASE_ID = #{sub_case_id} ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<include refid="JRQD_queryClaimSubCaseBySubCaseIdCondition" />
		<![CDATA[ ORDER BY A.SUB_CASE_ID ]]>
	</select>
<!-- 按赔案id查询 -->	
	<select id="JRQD_findByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A  WHERE 1 = 1  ]]>
		<if test="case_id != null ">
			<![CDATA[ AND A.CASE_ID = #{case_id} ]]>
		</if>
		<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapClaimSubCase" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SUB_CASE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllClaimSubCase" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_claimSubCaseWhereCondition" />
		<![CDATA[ ORDER BY A.SUB_CASE_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findClaimSubCaseTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryClaimSubCaseForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CLAIM_TYPE, B.ACC_REASON, B.CLAIM_DATE, B.SUB_CASE_ID, 
			B.CASE_ID FROM (
					SELECT ROWNUM RN, A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SUB_CASE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- add by zhangjy_wb start -->
	<select id="JRQD_findClaimSubCaseByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE 1 = 1  AND A.CASE_ID = #{case_id}]]>
			<if test="claimType !=null and claimType != ''"><![CDATA[  AND A.CLAIM_TYPE IN (${claimType}) ]]></if>
			<if test=" claim_type != null and claim_type != ''  "><![CDATA[ AND A.CLAIM_TYPE = #{claim_type} ]]></if>
			<![CDATA[ ORDER BY A.CLAIM_DATE ]]> 
	</select>	
	<select id="JRQD_findClaimSubCaseByCaseId1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
		TRUNC(months_between(A.CLAIM_DATE, r.customer_birthday)/12) AS age,
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A,dev_clm.t_customer r,dev_clm.t_claim_case t WHERE 1 = 1 
			and A.CASE_ID=T.CASE_ID
         	AND R.CUSTOMER_ID=T.INSURED_ID
			 AND A.CASE_ID = #{case_id}]]>
			<if test="claimType !=null and claimType != ''"><![CDATA[  AND A.CLAIM_TYPE IN (${claimType}) ]]></if>
			<if test=" claim_type != null and claim_type != ''  "><![CDATA[ AND A.CLAIM_TYPE = #{claim_type} ]]></if>
			<![CDATA[ ORDER BY A.CLAIM_DATE ]]> 
	</select>
	<select id="JRQD_findDistinctClaimTypeByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.CLAIM_TYPE, A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE 1 = 1  AND A.CASE_ID = #{case_id}]]>
	</select>
	<!-- add by zhangjy_wb end -->	
	<!-- add by zhaoyq -->
	<select id="JRQD_findAllClaimSubCaseEnd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE A.CASE_ID = #{case_id}  ORDER BY A.INSERT_TIME DESC ]]>
	</select>
	<!-- add by xinghj -->
	<select id="JRQD_findClaimSubCaseByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE A.CASE_ID = #{case_id} AND A.CLAIM_TYPE=#{claim_type} ORDER BY a.claim_date desc,A.INSERT_TIME DESC ]]>
	</select>
	<!-- caoyy -->
	<select id="JRQD_findAllClaimSubCaseByCaseIdAndClaimType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE A.CASE_ID = #{case_id} AND A.CLAIM_TYPE=#{claim_type} ORDER BY A.CLAIM_DATE DESC ]]>
	</select>
	<!-- add by liulei_wb -->
	<select id="JRQD_findRedoSubCaseByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE A.CASE_ID IN (SELECT CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_CASE B WHERE B.INSURED_ID = (SELECT INSURED_ID FROM APP___CLM__DBUSER.T_CLAIM_CASE C WHERE C.CASE_ID=#{case_id}))  ORDER BY A.INSERT_TIME DESC ]]>
	</select>
	<!-- 查询赔案的出险日期 -->
	<select id="JRQD_findClaimDatesByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT(A.CLAIM_DATE)
		                    FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE A.CASE_ID = #{case_id}]]>
			<![CDATA[ ORDER BY A.CLAIM_DATE ]]> 
	</select>
	<select id="JRQD_findClaimSubCaseDistinct" parameterType="java.util.Map" resultType="java.util.Map">
	  <![CDATA[
	  SELECT DISTINCT A.CLAIM_DATE,A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE A.CASE_ID=#{case_id}
	  ]]>
	</select>
	<select id="JRQD_findMaxClaimDateByCaseId" parameterType="java.util.Map" resultType="java.util.Map">
	   <![CDATA[select max(claim_date) from dev_clm.t_claim_sub_case a where a.case_id=#{case_id} ]]> 
	</select>
	
	<select id="JRQD_findClaimSubCaseTypeByCaseId" parameterType="java.util.Map" resultType="java.util.Map">	
	   <![CDATA[ SELECT 
	        (SELECT B.NAME FROM DEV_CLM.T_CLAIM_TYPE B WHERE B.CODE=A.CLAIM_TYPE ) AS CLAIM_NAME,
	        (SELECT B.NAME FROM APP___CLM__DBUSER.T_CLAIM_NATURE B WHERE B.CODE=A.ACC_REASON) AS ACC_NAME,
	        A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A  WHERE A.CASE_ID = #{case_id, jdbcType=NUMERIC} ]]>
	</select>
	
	<!-- 问题件调整-调整确认 -->
	<select id="JRQD_findClaimSubCaseByMinClaimDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID, rownum rw FROM APP___CLM__DBUSER.T_CLAIM_SUB_CASE A WHERE 1 = 1  AND A.CASE_ID = #{case_id} order by a.claim_date]]>
	</select>
</mapper>
