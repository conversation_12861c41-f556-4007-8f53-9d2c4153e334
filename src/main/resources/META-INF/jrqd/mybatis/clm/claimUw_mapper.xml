<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimUwPO">

	<sql id="JRQD_claimUwWhereCondition">
		<if test=" cancel_reason != null and cancel_reason != ''  "><![CDATA[ AND A.CANCEL_REASON = #{cancel_reason} ]]></if>
		<if test=" claim_uw_type  != null "><![CDATA[ AND A.CLAIM_UW_TYPE = #{claim_uw_type} ]]></if>
		<if test=" uw_cancel_flag  != null "><![CDATA[ AND A.UW_CANCEL_FLAG = #{uw_cancel_flag} ]]></if>
		<if test=" not_inform_situation != null and not_inform_situation != ''  "><![CDATA[ AND A.NOT_INFORM_SITUATION = #{not_inform_situation} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" claim_uw_reason != null and claim_uw_reason != ''  "><![CDATA[ AND A.CLAIM_UW_REASON = #{claim_uw_reason} ]]></if>
		<if test=" apply_by  != null "><![CDATA[ AND A.APPLY_BY = #{apply_by} ]]></if>
		<if test=" uw_status != null and uw_status != ''  "><![CDATA[ AND A.UW_STATUS = #{uw_status} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" repair_back_way != null and repair_back_way != ''  "><![CDATA[ AND A.REPAIR_BACK_WAY = #{repair_back_way} ]]></if>
		<if test=" open_account_bank != null and open_account_bank != ''  "><![CDATA[ AND A.OPEN_ACCOUNT_BANK = #{open_account_bank} ]]></if>
		<if test=" uw_conclusion  != null "><![CDATA[ AND A.UW_CONCLUSION = #{uw_conclusion} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" not_effect_reason != null and not_effect_reason != ''  "><![CDATA[ AND A.NOT_EFFECT_REASON = #{not_effect_reason} ]]></if>
		<if test=" clm_uw_id  != null "><![CDATA[ AND A.CLM_UW_ID = #{clm_uw_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" bank_no != null and bank_no != ''  "><![CDATA[ AND A.BANK_NO = #{bank_no} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" uw_policy_id != null and uw_policy_id != ''  "><![CDATA[ AND A.UW_POLICY_ID = #{uw_policy_id} ]]></if>
		<if test=" uw_times != null and uw_times != ''  "><![CDATA[ AND A.UW_TIMES = #{uw_times} ]]></if>
		<if test=" uw_conclusion_time != null and uw_conclusion_time != ''  "><![CDATA[ AND A.UW_CONCLUSION_TIME = #{uw_conclusion_time} ]]></if>
		<if test=" uw_nucleus_time != null and uw_nucleus_time != ''  "><![CDATA[ AND A.UW_NUCLEUS_TIME = #{uw_nucleus_time} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryClaimUwByClmUwIdCondition">
		<if test=" clm_uw_id  != null "><![CDATA[ AND A.CLM_UW_ID = #{clm_uw_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addClaimUw"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="clm_uw_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_UW__CLM_UW_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_UW(
				CANCEL_REASON, CLAIM_UW_TYPE, UW_CANCEL_FLAG, NOT_INFORM_SITUATION, REMARK, APPLY_DATE, CLAIM_UW_REASON, 
				APPLY_BY, UW_STATUS, CASE_NO, INSERT_TIMESTAMP, REPAIR_BACK_WAY, OPEN_ACCOUNT_BANK, UPDATE_BY, 
				UW_CONCLUSION, POLICY_ID, NOT_EFFECT_REASON, INSERT_TIME, CLM_UW_ID, UPDATE_TIME, CASE_ID, 
				BANK_NO, POLICY_CODE, UW_POLICY_ID, UPDATE_TIMESTAMP, INSERT_BY, UW_TIMES, UNIT_NUMBER, UW_BATCH_NO ) 
			VALUES (
				#{cancel_reason, jdbcType=VARCHAR}, #{claim_uw_type, jdbcType=NUMERIC} , #{uw_cancel_flag, jdbcType=NUMERIC} , #{not_inform_situation, jdbcType=VARCHAR} , #{remark, jdbcType=VARCHAR} , #{apply_date, jdbcType=DATE} , #{claim_uw_reason, jdbcType=VARCHAR} 
				, #{apply_by, jdbcType=NUMERIC} , #{uw_status, jdbcType=VARCHAR} , #{case_no, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{repair_back_way, jdbcType=VARCHAR} , #{open_account_bank, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{uw_conclusion, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{not_effect_reason, jdbcType=VARCHAR} , SYSDATE , #{clm_uw_id, jdbcType=NUMERIC} , SYSDATE , #{case_id, jdbcType=NUMERIC} 
				, #{bank_no, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{uw_policy_id, jdbcType=NUMERIC}, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{uw_times, jdbcType=NUMERIC}, #{unit_number, jdbcType=VARCHAR}, #{uw_batch_no, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteClaimUw" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_UW WHERE CLM_UW_ID = #{clm_uw_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateClaimUw" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_UW ]]>
		<set>
		<trim suffixOverrides=",">
			CANCEL_REASON = #{cancel_reason, jdbcType=VARCHAR} ,
		    CLAIM_UW_TYPE = #{claim_uw_type, jdbcType=NUMERIC} ,
		    UW_CANCEL_FLAG = #{uw_cancel_flag, jdbcType=NUMERIC} ,
			NOT_INFORM_SITUATION = #{not_inform_situation, jdbcType=VARCHAR} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
			CLAIM_UW_REASON = #{claim_uw_reason, jdbcType=VARCHAR} ,
		    APPLY_BY = #{apply_by, jdbcType=NUMERIC} ,
			UW_STATUS = #{uw_status, jdbcType=VARCHAR} ,
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
			REPAIR_BACK_WAY = #{repair_back_way, jdbcType=VARCHAR} ,
			OPEN_ACCOUNT_BANK = #{open_account_bank, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UW_CONCLUSION = #{uw_conclusion, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			NOT_EFFECT_REASON = #{not_effect_reason, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			BANK_NO = #{bank_no, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			UW_POLICY_ID = #{uw_policy_id, jdbcType=NUMERIC} ,
			UW_CONCLUSION_TIME = #{uw_conclusion_time, jdbcType=TIMESTAMP} ,
			UW_NUCLEUS_TIME = #{uw_nucleus_time, jdbcType=TIMESTAMP} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE CLM_UW_ID = #{clm_uw_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findClaimUwByClmUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UNIT_NUMBER, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryClaimUwByClmUwIdCondition" />
		<![CDATA[ ORDER BY A.CLM_UW_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapClaimUw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UNIT_NUMBER, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CLM_UW_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllClaimUw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_POLICY_ID, A.UW_TIMES, A.UNIT_NUMBER, A.UW_BATCH_NO, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.CLM_UW_ID desc]]> 
	</select>
<!-- 通过发起二核时间查询所有操作 -->
	<select id="JRQD_findAllClaimUwByApplyDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_POLICY_ID, A.UW_TIMES, A.UNIT_NUMBER, A.UW_BATCH_NO, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE ROWNUM <=  1000 AND A.UW_STATUS != '2'  ]]>
		<include refid="JRQD_claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_DATE desc]]> 
	</select>
	
<!-- 查询保单的最新二核处理结论时间 -->
	<select id="JRQD_findClaimUwByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_POLICY_ID, A.UW_TIMES, A.UNIT_NUMBER, A.UW_BATCH_NO, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.UW_CONCLUSION_TIME DESC]]>  
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findClaimUwTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryClaimUwForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CANCEL_REASON, B.CLAIM_UW_TYPE, B.UW_CANCEL_FLAG, B.NOT_INFORM_SITUATION, B.REMARK, B.APPLY_DATE, B.CLAIM_UW_REASON, 
			B.APPLY_BY, B.UW_STATUS, B.CASE_NO, B.REPAIR_BACK_WAY, B.OPEN_ACCOUNT_BANK, 
			B.UW_CONCLUSION, B.POLICY_ID, B.NOT_EFFECT_REASON, B.CLM_UW_ID, B.CASE_ID, 
			B.BANK_NO, B.POLICY_CODE, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM (
					SELECT ROWNUM RN, A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CLM_UW_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询单条操作 -->
	<select id="JRQD_findClaimUw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_POLICY_ID, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.CLM_UW_ID  DESC]]> 
	</select>
	<!-- 查询单条操作 sunjl_wb -->
	<select id="JRQD_findOneClaimUw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.CLM_UW_ID, A.CASE_ID, A.CASE_NO, A.CLAIM_UW_TYPE, A.UW_STATUS, A.CLAIM_UW_REASON, A.APPLY_DATE, A.APPLY_BY, A.REMARK, A.INSERT_BY, A.UPDATE_BY, A.INSERT_TIME, A.UPDATE_TIME, A.INSERT_TIMESTAMP, A.UPDATE_TIMESTAMP, A.POLICY_ID, A.POLICY_CODE, A.NOT_INFORM_SITUATION, A.UW_CONCLUSION, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, A.BANK_NO, A.NOT_EFFECT_REASON, A.UW_CANCEL_FLAG, A.CANCEL_REASON, A.UW_POLICY_ID, A.UW_TIMES, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE 1 = 1 ]]>
		<include refid="JRQD_claimUwWhereCondition" />
	</select>
	<!-- add by zhaoyq  start-->
	<select id="JRQD_findAllClaimUwOrderInsertTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.INSERT_TIME, A.UW_POLICY_ID, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.INSERT_TIME ]]> 
	</select>
	<!-- 查询核保结论已完成的保单 -->
	<select id="JRQD_findPolicysUwConclusion" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE,
                         A.CLAIM_UW_REASON, A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK,
                         A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CASE_ID, A.BANK_NO,
                         A.POLICY_CODE, A.INSERT_TIME , A.CLM_UW_ID, P.UW_POLICY_ID, P.UW_USER_ID, P.UW_FINISH_TIME, P.UW_ID, P.UW_STATUS as PUW_STATUS
                    FROM APP___CLM__DBUSER.T_CLAIM_UW A LEFT JOIN APP___CLM__DBUSER.T_UW_POLICY P
                      ON A.UW_POLICY_ID = P.UW_POLICY_ID
                   WHERE A.UW_POLICY_ID IS NOT NULL
                     AND A.POLICY_ID = P.POLICY_ID 
        ]]>
		<include refid="JRQD_claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.INSERT_TIME ]]> 
	</select>
	<!-- 根据条件查询保单  二核处理结论为空的 -->
	<select id="JRQD_findClaimUwByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE  A.UW_CONCLUSION IS NULL AND UW_STATUS = 0]]>
		<include refid="JRQD_claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.CLM_UW_ID ]]> 
	</select>
	<!-- 根据赔案ID和保单ID查询数据，并按插入日期倒序排序 -->
	<select id="JRQD_findAllClaimUwByInserTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE  A.POLICY_ID = #{policy_id}  AND A.CASE_ID = #{case_id} ORDER BY A.INSERT_TIME DESC ]]>
	</select>
	<select id="JRQD_findClaimUwPIdNotNull" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  update APP___CLM__DBUSER.T_CLAIM_UW a set a.uw_policy_id = #{uw_policy_id} where a.uw_status = 0 and a.policy_code=#{policy_code} and a.case_no=#{case_no} and a.uw_policy_id is null ]]>
	</select>
	<select id="JRQD_findClaimUwByUwPolicyID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_POLICY_ID FROM APP___CLM__DBUSER.T_CLAIM_UW A 
			WHERE A.UW_CONCLUSION IS NOT NULL and  A.POLICY_ID = #{policy_id} AND A.CASE_ID = #{case_id}]]>
			<if test=" uw_policy_id != null and uw_policy_id != ''  "><![CDATA[ AND A.UW_POLICY_ID = #{uw_policy_id} ]]></if>
	</select>
	<!-- add by zhaoyq end -->
	<!-- add by zhaojy_wb  -->	
	<select id="JRQD_findAllClaimUwByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID,  
			A.BANK_NO, A.POLICY_CODE, A.UPDATE_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A WHERE 1 = 1 ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" claim_uw_type  != null "><![CDATA[ AND A.claim_uw_type = #{claim_uw_type} ]]></if>
		<![CDATA[ ORDER BY A.CLM_UW_ID ]]>
	</select>
	<select id="JRQD_findClaimUwTimes" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT max(uw_times) as uw_times FROM APP___CLM__DBUSER.t_claim_uw where case_id = #{case_id} ]]>
	</select>
	<select id="JRQD_updateClaimUwByClaimUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  UPDATE APP___CLM__DBUSER.T_CLAIM_UW A SET A.UW_STATUS = #{uw_status},A.UW_NUCLEUS_TIME = #{uw_nucleus_time, jdbcType=TIMESTAMP}  WHERE A.CLM_UW_ID = #{clm_uw_id} ]]>
	</select>
	<select id="JRQD_updateClaimUwUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  UPDATE APP___CLM__DBUSER.T_CLAIM_UW A SET A.Unit_Number = #{unit_number} WHERE A.CLM_UW_ID = #{clm_uw_id} ]]>
	</select>
	
	<select id="JRQD_updateClaimUwByStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  UPDATE APP___CLM__DBUSER.T_CLAIM_UW A SET A.UW_CANCEL_FLAG = #{uw_cancel_flag}, A.CANCEL_REASON = #{cancel_reason}, 
		             A.UW_STATUS = #{uw_status} WHERE A.CLM_UW_ID = #{clm_uw_id} AND A.UW_STATUS ='4']]>
	</select>
	
	<!-- add by zhaoyq 服务方接口 根据入参查询二核核保最新数据 -->
	<select id="JRQD_queryNewUwConclusion" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		SELECT A.POLICY_CODE,
               A.UW_CONCLUSION,
               (SELECT TYPE_NAME FROM APP___CLM__DBUSER.T_YES_NO WHERE YES_NO = A.UW_CONCLUSION) UW_CONCLUSIONNAME,
               A.UPDATE_TIME,
               C.BUSI_PROD_CODE,
               C.DECISION_CODE,
               (SELECT DECISION_DESC FROM APP___CLM__DBUSER.T_PRODUCT_DECISION WHERE DECISION_CODE = C.DECISION_CODE) DECISION_DESC
          FROM APP___CLM__DBUSER.T_CLAIM_UW         A,
               APP___CLM__DBUSER.T_UW_POLICY        B,
               APP___CLM__DBUSER.T_UW_BUSI_PROD     C
         WHERE A.UW_POLICY_ID = B.UW_POLICY_ID
           AND B.UW_ID = C.UW_ID
           AND B.UW_POLICY_ID = C.UW_POLICY_ID
           AND A.UW_STATUS = 1
           AND B.POLICY_CODE = #{policy_code}
           AND C.BUSI_PROD_CODE = #{busi_prod_code}
         ORDER BY A.UPDATE_TIME DESC    
		]]>
	</select>
	<select id="JRQD_findUwDateStrByTask" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[  
	    SELECT A.CASE_NO, A.APPLY_DATE, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME
          FROM APP___CLM__DBUSER.T_CLAIM_UW A
         WHERE A.CASE_NO = #{case_no}
           AND A.APPLY_DATE = #{apply_date}
           AND A.UW_STATUS=1
           AND ROWNUM = 1]]>
	</select>
	
	<!-- add by zhaoyq 服务方接口 根据入参查询二核核保最新数据 -->
	<select id="JRQD_findClaimUwRepellentAndDelayByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			select A.POLICY_DECISION ,B.DECISION_CODE,B.DECISION_DESC,A.POLICY_CODE 
			from APP___CLM__DBUSER.T_UW_POLICY A,APP___CLM__DBUSER.T_POLICY_DECISION B
			  WHERE 1=1
			  AND A.POLICY_DECISION = B.DECISION_CODE
			  AND B.DECISION_DESC in ('拒保','延期')
			  AND A.POLICY_CODE=#{policy_code} 
		]]>
	</select>
	<!-- 撤销二核 -->
	<select id="JRQD_updateUwStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			UPDATE APP___CLM__DBUSER.T_CLAIM_UW A
			   SET A.UW_STATUS = 2 ,A.UPDATE_TIME = SYSDATE 
			 WHERE A.UW_BATCH_NO IN (SELECT B.UW_BATCH_NO
			                           FROM APP___CLM__DBUSER.T_CLAIM_UW B
			                          WHERE B.UW_STATUS = 0
			                            AND B.POLICY_CODE = #{policy_code} 
			                            AND B.CASE_NO = A.CASE_NO)
			   AND A.CASE_NO = #{case_no} 
		]]>
	</select>
	<select id="JRQD_findAllClaimUwOnNumber" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_POLICY_ID, A.UW_TIMES, A.UNIT_NUMBER, A.UW_BATCH_NO, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME FROM APP___CLM__DBUSER.T_CLAIM_UW A 
			WHERE A.UNIT_NUMBER IS NOT NULL AND A.CASE_ID=#{case_id} AND  A.UW_STATUS=#{uw_status} ]]>
			<if test=" apply_by  != null "><![CDATA[ AND A.APPLY_BY = #{apply_by} ]]></if>
		<![CDATA[ ORDER BY A.CLM_UW_ID desc]]> 
	</select>
	<!-- 打印续保类通知书查询相关保单   借用claim_uw_reason判断，1不续保通知书  2续保核保通知书 -->
	<select id="JRQD_findUwPolicysByType" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT DISTINCT A.CASE_NO,
	                A.CASE_ID,
                    A.POLICY_ID,
                    A.POLICY_CODE,
                    A.CLAIM_UW_TYPE,
                    A.UW_POLICY_ID,
                    D.UW_FINISH_TIME,
                    D.UW_USER_ID
               FROM DEV_CLM.T_CLAIM_UW     A,
                    DEV_CLM.T_UW_POLICY    B,
                    DEV_CLM.T_UW_BUSI_PROD C,
                    DEV_CLM.T_UW_MASTER D
              WHERE A.CASE_NO = D.BIZ_CODE
                AND A.UW_POLICY_ID = B.UW_POLICY_ID
                AND B.UW_POLICY_ID = C.UW_POLICY_ID
                AND (B.KEEP_OLD_DECISION = 0 OR B.KEEP_OLD_DECISION IS NULL)
                AND A.CLAIM_UW_TYPE = #{claim_uw_type}
                AND A.CASE_ID = #{case_id}
                AND A.UW_STATUS = 1]]> 
            <if test=" claim_uw_reason == 1  "><![CDATA[ AND C.DECISION_CODE ='60' ]]></if>
            <if test=" claim_uw_reason == 2  "><![CDATA[ AND C.DECISION_CODE in ('31','33','30') ]]></if>
	</select>
	
	<!-- 审核-调查/协谈/二核/合议-处理查看二核-查询“待二核”的二核信息 -->
	<select id="JRQD_queryClaimUwByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
				A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
				A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
				A.BANK_NO, A.POLICY_CODE, A.UNIT_NUMBER, A.UW_CONCLUSION_TIME, A.UW_NUCLEUS_TIME 
			   FROM dev_clm.t_claim_uw a
			   where  A.UW_STATUS = '0'
			     AND A.CASE_ID = #{case_id}
			    AND A.UW_POLICY_ID IS NULL]]> 
	</select>
	<!-- 查詢核保結論 -->
	<select id="JRQD_findAllClaimUwConlusionByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT
       UU.POLICY_CODE,
       UU.UW_CONCLUSION,
       uu.UW_CONCLUSION_TIME,
       uu.UW_STATUS,
       UM.UW_FINISH_TIME,
       UBP.BUSI_PROD_CODE,
       um.UW_DECISION,
       ubp.UPDATE_TIME,
       ubp.DECISION_CODE
        
  FROM APP___CLM__DBUSER.T_CLAIM_UW     UU,
       APP___CLM__DBUSER.T_UW_MASTER    UM,
       APP___CLM__DBUSER.T_UW_BUSI_PROD UBP  
  WHERE
     UU.CASE_NO= UM.BIZ_CODE
     AND  UM.UW_ID = UBP.UW_ID ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND uu.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ORDER BY UW_FINISH_TIME DESC ]]>
	</select>
	<!-- 查询二核标识，0为否 -->
	<select id="JRQD_findClaimUwStatus" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)   FROM DEV_CLM.T_CLAIM_UW A,
			                   DEV_CLM.T_CLAIM_CASE cc
			                   WHERE A.CASE_NO = CC.CASE_NO]]>
		<include refid="JRQD_claimUwWhereCondition" />	
	</select>
	<!-- 查询当前赔案最后一次的二核状态 -->
	<select id="JRQD_findClaimUwStatusByCaseNO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT a.UW_STATUS   FROM DEV_CLM.T_CLAIM_UW A,
			                   DEV_CLM.T_CLAIM_CASE cc
			                   WHERE A.CASE_NO = CC.CASE_NO 
                         ]]>
		<include refid="JRQD_claimUwWhereCondition" />	
		<![CDATA[ ORDER BY a.UPDATE_TIMESTAMP DESC ]]>
	</select>
	<!-- 更新二核状态（二核回退） -->
	<select id="JRQD_updateClaimUwStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			UPDATE APP___CLM__DBUSER.T_CLAIM_UW A
			   SET A.UW_STATUS = '3',
			     A.UW_CONCLUSION = '1'
			 WHERE (a.UW_CONCLUSION =0 OR a.UW_CONCLUSION IS null) and a.UW_STATUS !='1'
			   AND A.CASE_ID = #{case_id} 
		]]>
	</select>
	
	<select id="findBusiItemIdByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			SELECT DISTINCT C.BUSI_ITEM_ID
	          FROM APP___CLM__DBUSER.T_CLAIM_UW         A,
	               APP___CLM__DBUSER.T_UW_POLICY        B,
	               APP___CLM__DBUSER.T_UW_BUSI_PROD     C
	         WHERE A.UW_POLICY_ID = B.UW_POLICY_ID
	           AND B.UW_ID = C.UW_ID
	           AND B.UW_POLICY_ID = C.UW_POLICY_ID          
	           AND A.POLICY_CODE = #{policy_code}
	           AND A.CASE_ID = #{case_id}
	     ]]>
	</select>
</mapper>
