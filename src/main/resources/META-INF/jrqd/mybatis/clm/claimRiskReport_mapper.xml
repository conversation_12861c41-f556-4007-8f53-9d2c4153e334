<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.common.CommonQuery">

	<sql id="JRQD_claimRiskReportWhereCondition">
		<if test=" itme_name != null and itme_name != ''  "><![CDATA[ AND A.ITME_NAME = #{itme_name} ]]></if>
		<if test=" user_id  != null "><![CDATA[ AND A.USER_ID = #{user_id} ]]></if>
		<if test=" module_name != null and module_name != ''  "><![CDATA[ AND A.MODULE_NAME = #{module_name} ]]></if>
		<if test=" item_type != null and item_type != ''  "><![CDATA[ AND A.ITEM_TYPE = #{item_type} ]]></if>
		<if test=" module_type != null and module_type != ''  "><![CDATA[ AND A.MODULE_TYPE = #{module_type} ]]></if>
		<if test=" report_id  != null "><![CDATA[ AND A.REPORT_ID = #{report_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_queryClaimRiskReportByUserIdCondition">
		<if test=" user_id  != null "><![CDATA[ AND A.USER_ID = #{user_id} ]]></if>
	</sql>	
	<sql id="JRQD_queryClaimRiskReportByItemTypeCondition">
		<if test=" item_type != null and item_type != '' "><![CDATA[ AND A.ITEM_TYPE = #{item_type} ]]></if>
	</sql>	
	<sql id="JRQD_queryClaimRiskReportByReportIdCondition">
		<if test=" report_id  != null "><![CDATA[ AND A.REPORT_ID = #{report_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addClaimRiskReport"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="report_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_RISK_REPORT__REPORT_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_RISK_REPORT(
				INSERT_TIMESTAMP, ITME_NAME, USER_ID, UPDATE_BY, MODULE_NAME, INSERT_TIME, ITEM_TYPE, 
				UPDATE_TIME, UPDATE_TIMESTAMP, INSERT_BY, MODULE_TYPE, REPORT_ID ) 
			VALUES (
				CURRENT_TIMESTAMP, #{itme_name, jdbcType=VARCHAR} , #{user_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{module_name, jdbcType=VARCHAR} , SYSDATE , #{item_type, jdbcType=VARCHAR} 
				, SYSDATE , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{module_type, jdbcType=VARCHAR} , #{report_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteClaimRiskReport" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_RISK_REPORT WHERE REPORT_ID = #{report_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateClaimRiskReport" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_RISK_REPORT ]]>
		<set>
		<trim suffixOverrides=",">
			ITME_NAME = #{itme_name, jdbcType=VARCHAR} ,
		    USER_ID = #{user_id, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			MODULE_NAME = #{module_name, jdbcType=VARCHAR} ,
			ITEM_TYPE = #{item_type, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			MODULE_TYPE = #{module_type, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE REPORT_ID = #{report_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_findClaimRiskReportByUserId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITME_NAME, A.USER_ID, A.MODULE_NAME, A.ITEM_TYPE, 
			A.MODULE_TYPE, A.REPORT_ID FROM APP___CLM__DBUSER.T_CLAIM_RISK_REPORT A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryClaimRiskReportByUserIdCondition" />
		<![CDATA[ ORDER BY A.REPORT_ID ]]>
	</select>
	
	<select id="JRQD_findClaimRiskReportByItemType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITME_NAME, A.USER_ID, A.MODULE_NAME, A.ITEM_TYPE, 
			A.MODULE_TYPE, A.REPORT_ID FROM APP___CLM__DBUSER.T_CLAIM_RISK_REPORT A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryClaimRiskReportByItemTypeCondition" />
		<![CDATA[ ORDER BY A.REPORT_ID ]]>
	</select>
	
	<select id="JRQD_findClaimRiskReportByReportId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITME_NAME, A.USER_ID, A.MODULE_NAME, A.ITEM_TYPE, 
			A.MODULE_TYPE, A.REPORT_ID FROM APP___CLM__DBUSER.T_CLAIM_RISK_REPORT A WHERE 1 = 1  ]]>
		<include refid="JRQD_queryClaimRiskReportByReportIdCondition" />
		<![CDATA[ ORDER BY A.REPORT_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapClaimRiskReport" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITME_NAME, A.USER_ID, A.MODULE_NAME, A.ITEM_TYPE, 
			A.MODULE_TYPE, A.REPORT_ID FROM APP___CLM__DBUSER.T_CLAIM_RISK_REPORT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.REPORT_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_findAllClaimRiskReport" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITME_NAME, A.USER_ID, A.MODULE_NAME, A.ITEM_TYPE, 
			A.MODULE_TYPE, A.REPORT_ID FROM APP___CLM__DBUSER.T_CLAIM_RISK_REPORT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.REPORT_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findClaimRiskReportTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_RISK_REPORT A WHERE 1 = 1  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_queryClaimRiskReportForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ITME_NAME, B.USER_ID, B.MODULE_NAME, B.ITEM_TYPE, 
			B.MODULE_TYPE, B.REPORT_ID FROM (
					SELECT ROWNUM RN, A.ITME_NAME, A.USER_ID, A.MODULE_NAME, A.ITEM_TYPE, 
			A.MODULE_TYPE, A.REPORT_ID FROM APP___CLM__DBUSER.T_CLAIM_RISK_REPORT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.REPORT_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 报案信息查询接口 -->
	<select id="JRQD_findReportInformationcheck"  resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ select a.case_no clm_no,
				       a.insured_id customer_no,
				       d.customer_name,
				       d.customer_certi_code id_no,
				       d.customer_cert_type id_type,
				       d.customer_gender customer_sex,
				       c.acc_date,
				       b.claim_type reason_code,
				       c.acc_province,
				       c.acc_city,
				       c.acc_distreact,
				       c.acc_street,
				       c.acc_desc acc_desc,
				       (select t.name from APP___CLM__DBUSER.T_DISTRICT t where t.code=c.ACC_PROVINCE)||(select t.name from APP___CLM__DBUSER.T_DISTRICT t where t.code=c.ACC_CITY)||(select t.name from APP___CLM__DBUSER.T_DISTRICT t where t.code=c.ACC_DISTREACT)||c.ACC_STREET accident_site,
				       e.code accident_reason,
				       a.organ_code manage_com,
				       a.insert_time rpt_date
				  from APP___CLM__DBUSER.T_CLAIM_CASE a, APP___CLM__DBUSER.T_CLAIM_SUB_CASE b, APP___CLM__DBUSER.T_CLAIM_ACCIDENT c, APP___CLM__DBUSER.t_customer d, APP___CLM__DBUSER.T_CLAIM_NATURE e
				 where a.case_id = b.case_id
				   and a.accident_id = c.accident_id
				   and a.insured_id = d.customer_id 
				   and c.acc_reason = e.code
				   and a.case_status<'30'
				   and a.COMFORT_FLAG=1
				   and a.Comfort_Status=0		
				   and rownum <= 1000 ]]>
		  
		 	<if test="cla_state != null and cla_state  != ''"> 
		 		<![CDATA[ and a.case_status = #{cla_state} ]]>
		 	</if>
		 	<if test="start_date != null and start_date != ''"> 
		 		<![CDATA[ and a.rptr_time > #{start_date} ]]>
		 	</if>
		 	<if test="end_date != null and end_date != ''"> 
		 		<![CDATA[ and a.rptr_time <=  #{end_date}]]>
		 	</if>
		 	<if test="acc_province != null and acc_province != ''"> 
		 		<![CDATA[ and c.acc_province = #{acc_province} ]]>
		 	</if>
		  	<if test="acc_city != null and acc_city != ''"> 
		 		<![CDATA[ and c.acc_city = #{acc_city} ]]>
		 	</if>
		 	<if test="acc_distreact != null and acc_distreact != ''"> 
		 		<![CDATA[ and c.acc_distreact = #{acc_distreact} ]]>
		 	</if>
		 	<if test="acc_street != null and acc_street != ''"> 
		 		<![CDATA[ and c.acc_street like '%${acc_street}%' ]]>
		 	</if>
			<![CDATA[  ORDER BY A.ACCEPT_TIME DESC ]]>	  
	</select>
	
	<!-- 报案信息查询接口(签收监控) -->
	<select id="JRQD_findClaimCaseStatusTrack"  resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[ SELECT DISTINCT  CC.CASE_NO,
					                CC.INSURED_ID,
					                TCS.CUSTOMER_NAME,
					                CSC.CLAIM_DATE,
					                CC.CASE_STATUS,
					                CC.ORGAN_CODE,
					                CSC.ACC_REASON,
					                CSC.CLAIM_TYPE
					 FROM APP___CLM__DBUSER.T_CLAIM_CASE     CC,
					       APP___CLM__DBUSER.T_CUSTOMER       TCS,
					       APP___CLM__DBUSER.T_CLAIM_SUB_CASE CSC,
					       APP___CLM__DBUSER.T_CLAIM_POLICY   CP
					 WHERE rownum < 1000
					   AND CC.INSURED_ID = TCS.CUSTOMER_ID
					   AND CC.CASE_ID = CSC.CASE_ID
					   AND CC.CASE_ID = CP.CASE_ID(+)  ]]>
					   
			<if test="case_no != null and case_no  != ''"> 
		 		<![CDATA[ AND CC.CASE_NO = #{case_no} ]]>
		 	</if>
		 	<if test="customer_name != null and customer_name != ''">
		 		<![CDATA[ AND TCS.customer_name = #{customer_name} ]]>
		 	</if>
		 	<if test="case_status != null and case_status != ''"> 
		 		<![CDATA[ AND CC.CASE_STATUS =  #{case_status}]]>
		 	</if>
		 	<if test="signer_id != null and signer_id != ''"> 
		 		<![CDATA[ AND CC.SIGNER_ID = #{signer_id} ]]>
		 	</if>
		 	<if test="insured_id != null and insured_id != ''"> 
		 		<![CDATA[ AND CC.INSURED_ID = #{insured_id} ]]>
		 	</if>
		 	<if test="organ_code != null and organ_code != ''"> 
		 		<![CDATA[ AND CC.ORGAN_CODE = #{organ_code} ]]>
		 	</if>
		 	<if test="start_date != null"> 
		 		<![CDATA[ AND CC.SIGN_TIME > #{start_date} ]]>
		 	</if>
		 	<if test="end_date != null"> 
		 		<![CDATA[ AND CC.SIGN_TIME <=  #{end_date} ]]>
		 	</if>		  		 
		 	<if test="policy_code != null and policy_code != ''">
		 		<![CDATA[ AND CP.POLICY_CODE = #{policy_code} ]]>
		 	</if>
		 	<if test="customer_name != null and customer_name != ''">
		 		<![CDATA[ AND TCS.CUSTOMER_NAME = #{customer_name} ]]>
		 	</if> 
		 	 <![CDATA[ AND CC.case_status in('10','20','21','30')  ]]>
	</select>
	
	<!-- 保全-理赔服务接口 -->
	<select id="JRQD_findPasClmServiceTrack" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)
				  FROM APP___CLM__DBUSER.T_CLAIM_LIAB A, APP___CLM__DBUSER.T_CLAIM_CASE B
				 WHERE A.CASE_ID = B.CASE_ID
				   AND B.CASE_STATUS = 80
				   AND A.LIAB_CONCLUSION != 5
				   AND B.ACTUAL_PAY > 0
				   AND A.POLICY_CODE = #{policy_code}]]>
		<if test="start_date != null"> 
		 	<![CDATA[ AND B.END_CASE_TIME >=  #{start_date}]]>
		</if>
		<if test="liab_start_date != null"> 
		 	<![CDATA[ AND TRUNC(A.LIAB_START_DATE) >=  #{liab_start_date}]]>
		</if>
		<if test="liab_end_date != null"> 
		 	<![CDATA[ AND TRUNC(A.LIAB_END_DATE) <=  #{liab_end_date}]]>
		</if>
		<if test="busi_prod_code != null and busi_prod_code != '' "> 
		 	<![CDATA[ AND A.BUSI_PROD_CODE =  #{busi_prod_code}]]>
		</if>
	</select>	
</mapper>
