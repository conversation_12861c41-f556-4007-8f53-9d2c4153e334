<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.SurveyApplyPO">

	<sql id="JRQD_surveyApplyWhereConditionClm">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" survey_type  != null "><![CDATA[ AND A.SURVEY_TYPE = #{survey_type} ]]></if>
		<if test=" apply_section  != null "><![CDATA[ AND A.APPLY_SECTION = #{apply_section} ]]></if>
		<if test=" internal_resualt != null and internal_resualt != ''  "><![CDATA[ AND A.INTERNAL_RESUALT = #{internal_resualt} ]]></if>
		<if test=" survey_rule_id  != null "><![CDATA[ AND A.SURVEY_RULE_ID = #{survey_rule_id} ]]></if>
		<if test=" survey_doc_id  != null "><![CDATA[ AND A.SURVEY_DOC_ID = #{survey_doc_id} ]]></if>
		<if test=" survey_per  != null "><![CDATA[ AND A.SURVEY_PER = #{survey_per} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" survey_status  != null "><![CDATA[ AND A.SURVEY_STATUS = #{survey_status} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" survey_mode != null and survey_mode != ''  "><![CDATA[ AND A.SURVEY_MODE = #{survey_mode} ]]></if>
		<if test=" repeal_reason != null and repeal_reason != ''  "><![CDATA[ AND A.REPEAL_REASON = #{repeal_reason} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" survey_desc != null and survey_desc != ''  "><![CDATA[ AND A.SURVEY_DESC = #{survey_desc} ]]></if>
		<if test=" apply_org != null and apply_org != ''  "><![CDATA[ AND A.APPLY_ORG = #{apply_org} ]]></if>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" survey_advice != null and survey_advice != ''  "><![CDATA[ AND A.SURVEY_ADVICE = #{survey_advice} ]]></if>
		<if test=" survey_reason != null and survey_reason != ''  "><![CDATA[ AND A.SURVEY_REASON = #{survey_reason} ]]></if>
		<if test=" cs_apply_code != null and cs_apply_code != ''  "><![CDATA[ AND A.CS_APPLY_CODE = #{cs_apply_code} ]]></if>
		<if test=" survey_org != null and survey_org != ''  "><![CDATA[ AND A.SURVEY_ORG = #{survey_org} ]]></if>
		<if test=" cs_background != null and cs_background != ''  "><![CDATA[ AND A.CS_BACKGROUND = #{cs_background} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id, jdbcType=NUMERIC} ]]></if>
		<if test=" cs_accept_code != null and cs_accept_code != ''  "><![CDATA[ AND A.CS_ACCEPT_CODE = #{cs_accept_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" survey_by_level  != null "><![CDATA[ AND A.SURVEY_BY_LEVEL = #{survey_by_level} ]]></if>
		<if test=" survey_code != null and survey_code != ''  "><![CDATA[ AND A.SURVEY_CODE = #{survey_code} ]]></if>
		<if test=" apply_per  != null "><![CDATA[ AND A.APPLY_PER = #{apply_per} ]]></if>
		<if test=" cs_item != null and cs_item != ''  "><![CDATA[ AND A.CS_ITEM = #{cs_item} ]]></if>
	</sql>

    <sql id="JRQD_surveyApplyWhereConditionByCaseIdClm">
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="JRQD_querySurveyApplyByApplyIdConditionClm">
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="JRQD_addSurveyApplyLocalClm"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="apply_id">
			SELECT APP___CLM__DBUSER.S_SURVEY_APPLY.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_SURVEY_APPLY(
				RELATED_ID, SURVEY_TYPE, APPLY_SECTION, INTERNAL_RESUALT, SURVEY_RULE_ID, SURVEY_DOC_ID, SURVEY_PER, 
				REMARK, SURVEY_STATUS, APPLY_DATE, SURVEY_MODE, REPEAL_REASON, CASE_NO, APPLY_CODE, 
				SURVEY_DESC, INSERT_TIMESTAMP, UPDATE_BY, APPLY_ORG, APPLY_ID, BIZ_TYPE, SURVEY_ADVICE, 
				SURVEY_REASON, INSERT_TIME, CS_APPLY_CODE, SURVEY_ORG, UPDATE_TIME, CS_BACKGROUND, CASE_ID, 
				CS_ACCEPT_CODE, POLICY_CODE, SURVEY_BY_LEVEL, SURVEY_CODE, APPLY_PER, UPDATE_TIMESTAMP, INSERT_BY, 
				CS_ITEM ) 
			VALUES (
				#{related_id, jdbcType=NUMERIC}, #{survey_type, jdbcType=NUMERIC} , #{apply_section, jdbcType=NUMERIC} , #{internal_resualt, jdbcType=VARCHAR} , #{survey_rule_id, jdbcType=NUMERIC} , #{survey_doc_id, jdbcType=NUMERIC} , #{survey_per, jdbcType=NUMERIC} 
				, #{remark, jdbcType=VARCHAR} , #{survey_status, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} , #{survey_mode, jdbcType=VARCHAR} , #{repeal_reason, jdbcType=VARCHAR} , #{case_no, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} 
				, #{survey_desc, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{apply_org, jdbcType=VARCHAR} , #{apply_id, jdbcType=NUMERIC} , #{biz_type, jdbcType=NUMERIC} , #{survey_advice, jdbcType=VARCHAR} 
				, #{survey_reason, jdbcType=VARCHAR} , SYSDATE , #{cs_apply_code, jdbcType=VARCHAR} , #{survey_org, jdbcType=VARCHAR} , SYSDATE , #{cs_background, jdbcType=VARCHAR} , #{case_id, jdbcType=NUMERIC} 
				, #{cs_accept_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{survey_by_level, jdbcType=NUMERIC} , #{survey_code, jdbcType=VARCHAR} , #{apply_per, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{cs_item, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="JRQD_deleteSurveyApplyClm" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_SURVEY_APPLY WHERE APPLY_ID = #{apply_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="JRQD_updateSurveyApplyClm" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_SURVEY_APPLY ]]>
		<set>
		<trim suffixOverrides=",">
		    RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
		    SURVEY_TYPE = #{survey_type, jdbcType=NUMERIC} ,
		    APPLY_SECTION = #{apply_section, jdbcType=NUMERIC} ,
			INTERNAL_RESUALT = #{internal_resualt, jdbcType=VARCHAR} ,
		    SURVEY_RULE_ID = #{survey_rule_id, jdbcType=NUMERIC} ,
		    SURVEY_DOC_ID = #{survey_doc_id, jdbcType=NUMERIC} ,
		    SURVEY_PER = #{survey_per, jdbcType=NUMERIC} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
		    SURVEY_STATUS = #{survey_status, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
			SURVEY_MODE = #{survey_mode, jdbcType=VARCHAR} ,
			REPEAL_REASON = #{repeal_reason, jdbcType=VARCHAR} ,
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			SURVEY_DESC = #{survey_desc, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			APPLY_ORG = #{apply_org, jdbcType=VARCHAR} ,
		    BIZ_TYPE = #{biz_type, jdbcType=NUMERIC} ,
			SURVEY_ADVICE = #{survey_advice, jdbcType=VARCHAR} ,
			SURVEY_REASON = #{survey_reason, jdbcType=VARCHAR} ,
			CS_APPLY_CODE = #{cs_apply_code, jdbcType=VARCHAR} ,
			SURVEY_ORG = #{survey_org, jdbcType=VARCHAR} ,
			LOCIAL_FLAG_CODE = #{locial_flag_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			CS_BACKGROUND = #{cs_background, jdbcType=VARCHAR} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			CS_ACCEPT_CODE = #{cs_accept_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SURVEY_BY_LEVEL = #{survey_by_level, jdbcType=NUMERIC} ,
			SURVEY_CODE = #{survey_code, jdbcType=VARCHAR} ,
		    APPLY_PER = #{apply_per, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CS_ITEM = #{cs_item, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE APPLY_ID = #{apply_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="JRQD_claimFindSurveyApplyByApplyIdClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1  ]]>
		<include refid="JRQD_querySurveyApplyByApplyIdConditionClm" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	<select id="JRQD_findSurveyApplyByConditionsClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1  ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>	
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" survey_status  != null "><![CDATA[ AND A.SURVEY_STATUS = #{survey_status} ]]></if>
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="JRQD_findAllMapSurveyApplyClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="JRQD_请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="JRQD_claimFindAllSurveyApplyClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM,insert_timestamp,update_timestamp FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_surveyApplyWhereConditionClm" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
	</select>
	<!-- 查询是否有未完成的调查 -->
	<select id="JRQD_claimFindNotFinishSurveyClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT
			A.CASE_NO
			FROM APP___CLM__DBUSER.T_SURVEY_APPLY A,APP___CLM__DBUSER.T_SURVEY_CONCLUSION B WHERE A.apply_id = B.apply_id AND A.BIZ_TYPE=1 AND B.FINISH_DATE IS NULL ]]>
		<if test=" case_no != null and case_no != '' ">
		    <![CDATA[AND A.CASE_NO = #{case_no }]]>
		</if>
		
	</select>

<!-- 查询个数操作 -->
	<select id="JRQD_findSurveyApplyTotalClm" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1]]>
		<include refid="JRQD_surveyApplyWhereConditionClm" />
	</select>

<!-- 分页查询操作 -->
	<select id="JRQD_querySurveyApplyForPageClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RELATED_ID, B.SURVEY_TYPE, B.APPLY_SECTION, B.INTERNAL_RESUALT, B.SURVEY_RULE_ID, B.SURVEY_DOC_ID, B.SURVEY_PER, 
			B.REMARK, B.SURVEY_STATUS, B.APPLY_DATE, B.SURVEY_MODE, B.REPEAL_REASON, B.CASE_NO, B.APPLY_CODE, 
			B.SURVEY_DESC, B.APPLY_ORG, B.APPLY_ID, B.BIZ_TYPE, B.SURVEY_ADVICE, 
			B.SURVEY_REASON, B.CS_APPLY_CODE, B.SURVEY_ORG, B.CS_BACKGROUND, B.CASE_ID, 
			B.CS_ACCEPT_CODE, B.POLICY_CODE, B.SURVEY_BY_LEVEL, B.SURVEY_CODE, B.APPLY_PER, 
			B.CS_ITEM FROM (
					SELECT ROWNUM RN, A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="JRQD_surveyApplyWhereConditionClm" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- and by caoyy_wb 风险评估报告页面 -->
	<select id="JRQD_querySurveyApplyByCaseIdClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<include refid="JRQD_surveyApplyWhereConditionClm" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
	</select>
	<!-- 查询单条数据 -->
	<select id="JRQD_findSurveyApplyClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_ID,
       A.SURVEY_RULE_ID,
       A.SURVEY_CODE,
       A.BIZ_TYPE,
       A.CASE_ID,
       A.CASE_NO,
       A.POLICY_CODE,
       A.APPLY_CODE,
       A.CS_APPLY_CODE,
       A.CS_ACCEPT_CODE,
       A.CS_ITEM,
       A.RELATED_ID,
       A.SURVEY_DOC_ID,
       A.APPLY_SECTION,
       A.SURVEY_STATUS,
       A.SURVEY_DESC,
       A.SURVEY_TYPE,
       A.SURVEY_REASON,
       A.SURVEY_ORG,
       A.APPLY_ORG,
       A.APPLY_PER,
       A.APPLY_DATE,
       A.SURVEY_PER,
       A.SURVEY_BY_LEVEL,
       A.REPEAL_REASON,
       A.REMARK,
       A.INTERNAL_RESUALT,
       A.SURVEY_MODE,
       A.SURVEY_ADVICE,
       A.CS_BACKGROUND,
       A.INSERT_TIME,
       A.INSERT_BY,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIMESTAMP,
       A.UPDATE_TIME,
       A.LOCIAL_FLAG_CODE
        FROM APP___CLM__DBUSER.T_SURVEY_APPLY  A WHERE 1 = 1]]>
		<include refid="JRQD_surveyApplyWhereConditionClm" />
	</select>
	<!-- add by zhaoyq 自动分期给付支付批处理-->
	<select id="JRQD_findSurveyApplyByRelatedIdClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE A.RELATED_ID = #{related_id}  ]]>
	</select>
	<!-- add by zhangjy_wb 查询调查任务-->
	<select id="JRQD_queryAllSurveyApplyClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1 AND A.BIZ_TYPE = 1 AND A.SURVEY_STATUS NOT IN (2,3)]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>	
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	<!-- 发起调查信息查询-->
	<select id="JRQD_queryClaimResearchClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			 
			  SELECT A.CASE_NO,
			  		 A.SURVEY_CODE,
			         A.SURVEY_RULE_ID,
			         A.APPLY_ORG,
			         A.SURVEY_REASON,
			         A.SURVEY_ORG,
			         A.SURVEY_STATUS,
			         A.SURVEY_DESC,
			         A.SURVEY_TYPE,
			         A.LOCIAL_FLAG_CODE,
			         (select B.CUSTOMER_ID
			            from APP___CLM__DBUSER.T_SURVEY_OBJECT B
			           where B.APPLY_ID = A.Apply_Id) CUSTOMER_ID,
			         (select C.NAME
			            from APP___CLM__DBUSER.T_SURVEY_REASON C
			           WHERE C.CODE = A.SURVEY_REASON) AS SURVEY_REASON_NAME,
			         (select D.ORGAN_NAME
			            from APP___CLM__DBUSER.T_UDMP_ORG D
			           where D.ORGAN_CODE = A.Survey_Org) ORGAN_NAME,
			         (select to_char(wm_concat(E.SURVEY_ITEM))
                 		 from APP___CLM__DBUSER.T_SURVEY_ITEM E
                 	where E.APPLY_ID = A.APPLY_ID) SURVEY_ITEM,
			         (select F.CUSTOMER_VIP
			            from APP___CLM__DBUSER.T_CUSTOMER F
			           where F.Customer_Id =
			                 (select B.CUSTOMER_ID
			                    from APP___CLM__DBUSER.T_SURVEY_OBJECT B
			                   where B.APPLY_ID = A.Apply_Id)) CUSTOMER_VIP,
			         (select G.NAME
			            from APP___CLM__DBUSER.T_SURVEY_STATUS G
			           where G.CODE = A.SURVEY_STATUS) AS SURVEY_STATUS_NAME,
			         (select H.NAME
			            from APP___CLM__DBUSER.T_SURVEY_TYPE H
			           where H.CODE = A.SURVEY_TYPE) AS SURVEY_TYPE_NAME
			    FROM APP___CLM__DBUSER.T_SURVEY_APPLY A 
			    where  a.case_no = #{case_no}
		]]>
					<if test=" orderString != null ">
						${orderString}
					</if>
	</select>
	
	<!-- add by yangjx 通过caseID查询调查任务-->
	<select id="JRQD_queryAllSurveyApplyByCaseIdClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1 ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>	
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	
	<!-- add by liutao_wb 通过caseID查询调查任务  《签收阶段》-->
	<select id="JRQD_findsurveyApplyByCaseIdClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
		
     SELECT APPLY_ID,
       SURVEY_RULE_ID,
       SURVEY_CODE,
       BIZ_TYPE,
       CASE_ID,
       CASE_NO,
       POLICY_CODE,
       APPLY_CODE,
       CS_APPLY_CODE,
       CS_ACCEPT_CODE,
       CS_ITEM,
       RELATED_ID,
       SURVEY_DOC_ID,
       APPLY_SECTION,
       SURVEY_STATUS,
       SURVEY_DESC,
       SURVEY_TYPE,
       SURVEY_REASON,
       SURVEY_ORG,
       APPLY_ORG,
       APPLY_PER,
       APPLY_DATE,
       SURVEY_PER,
       SURVEY_BY_LEVEL,
       REPEAL_REASON,
       REMARK,
       INTERNAL_RESUALT,
       SURVEY_MODE,
       SURVEY_ADVICE,
       CS_BACKGROUND,
       INSERT_TIME,
       INSERT_BY,
       INSERT_TIMESTAMP,
       UPDATE_BY,
       UPDATE_TIMESTAMP,
       UPDATE_TIME,
       LOCIAL_FLAG_CODE,
       (select AFC_SURVEY_DECI
          from APP___CLM__DBUSER.T_SURVEY_CONCLUSION
         WHERE APPLY_ID = TSA.APPLY_ID) AFC_SURVEY_DECI,
        (select PRIORITY_CLAIM 
           from APP___CLM__DBUSER.T_SURVEY_CONCLUSION
         WHERE APPLY_ID = TSA.APPLY_ID) PRIORITY_CLAIM
        FROM APP___CLM__DBUSER.T_SURVEY_APPLY TSA
        WHERE TSA.CASE_ID = #{case_id}
		
		ORDER BY TSA.APPLY_ID 
		 ]]>
		 
	</select>
	<!-- add by xinghj_wb 通过caseNO查询   审核  （调查/协谈/二核/合议）页面 发起/查看调查数据-->
	<select id="JRQD_queryAllSurveyApplyByCaseNO"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.APPLY_ID,
				   A.SURVEY_CODE,
      			   A.APPLY_SECTION, 
       			   A.SURVEY_TYPE,
			       A.SURVEY_REASON,
			       A.SURVEY_ORG,
			       A.SURVEY_STATUS,
			       A.APPLY_DATE,
			       B.FINISH_DATE,
			       B.AFC_SURVEY_DECI,
			       B.POSITIVE_FLAG,
			       B.PRIORITY_CLAIM
           FROM APP___CLM__DBUSER.T_SURVEY_APPLY A,
	            APP___CLM__DBUSER.T_SURVEY_CONCLUSION B
                WHERE 1 = 1
	 		    AND A.APPLY_SECTION IN ('1','2','3')
	    		AND A.APPLY_ID = B.APPLY_ID(+)
		]]>
		<include refid="JRQD_surveyApplyWhereConditionClm" />
		<![CDATA[ ORDER BY A.INSERT_TIME ]]>
	</select>
	
	<!-- add by huangjh_wb 前置调查发送接口查询已申请的前置调查任务-->
	<select id="JRQD_findClaimBFSurveyApply"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
					SELECT 
		        SA.SURVEY_CODE,
		        SA.POLICY_CODE,
		        SA.SURVEY_ORG,
		        SA.APPLY_ORG,
		        SO.CUSTOMER_ID,
		        SO.CUS_NAME,
		        SO.CERTI_NO,
		        SB.APPLY_NUM,
		        SB.ACCU_AMOUNT,
		        TO_CHAR((SELECT WMSYS.WM_CONCAT(IT.VALUE) FROM APP___CLM__DBUSER.T_SURVEY_ITEM TSI,APP___CLM__DBUSER.T_SURVEY_ITEM_TYPE IT WHERE TSI.APPLY_ID = SA.APPLY_ID AND TSI.SURVEY_ITEM = IT.CODE ) ) SURVEY_ITEM,
		        SC.EXTRACT_RULE    
           FROM APP___CLM__DBUSER.T_SURVEY_APPLY SA,
              APP___CLM__DBUSER.T_CLAIM_SURVEY_TASK SB,
              APP___CLM__DBUSER.T_CLAIM_SURVEY_BATCH SC,
              APP___CLM__DBUSER.T_SURVEY_OBJECT SO,
              APP___CLM__DBUSER.T_UDMP_USER D
                WHERE SA.SURVEY_RULE_ID = SB.LIST_ID
                AND SB.BATCH_ID = SC.BATCH_ID
                AND SA.APPLY_PER = D.USER_ID(+)
                AND SA.APPLY_ID = SO.APPLY_ID
                AND SA.BIZ_TYPE = '5'
          AND SA.SURVEY_STATUS = '1'
		]]>
		<![CDATA[ ORDER BY SA.APPLY_ID ]]>
	</select>

	<!-- add by huangjh_wb 根据apply_id查询调查项目名称，并将结果拼接成字符串-->
	<select id="JRQD_findItemListByApplyId"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT 
		        SA.APPLY_ID,
		        TO_CHAR((SELECT WMSYS.WM_CONCAT(IT.VALUE) FROM APP___CLM__DBUSER.T_SURVEY_ITEM TSI,APP___CLM__DBUSER.T_SURVEY_ITEM_TYPE IT WHERE TSI.APPLY_ID = SA.APPLY_ID AND TSI.SURVEY_ITEM = IT.CODE ) ) SURVEY_ITEM
           FROM APP___CLM__DBUSER.T_SURVEY_APPLY SA
                WHERE SA.APPLY_ID = #{apply_id}
		]]>
		<![CDATA[ ORDER BY SA.APPLY_ID ]]>
	</select>
	
	
	<!-- add by huangjh_wb 前置调查发送返回接口查询需要更新状态的调查数据将surveyIdStr放入survey_mode字段中-->
	<select id="JRQD_findBFSurveyBySurveyIdStr"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.APPLY_ID,
		       A.SURVEY_RULE_ID,
		       A.SURVEY_CODE,
		       A.BIZ_TYPE,
		       A.CASE_ID,
		       A.CASE_NO,
		       A.POLICY_CODE,
		       A.APPLY_CODE,
		       A.CS_APPLY_CODE,
		       A.CS_ACCEPT_CODE,
		       A.CS_ITEM,
		       A.RELATED_ID,
		       A.SURVEY_DOC_ID,
		       A.APPLY_SECTION,
		       A.SURVEY_STATUS,
		       A.SURVEY_DESC,
		       A.SURVEY_TYPE,
		       A.SURVEY_REASON,
		       A.SURVEY_ORG,
		       A.APPLY_ORG,
		       A.APPLY_PER,
		       A.APPLY_DATE,
		       A.SURVEY_PER,
		       A.SURVEY_BY_LEVEL,
		       A.REPEAL_REASON,
		       A.REMARK,
		       A.INTERNAL_RESUALT,
		       A.SURVEY_MODE,
		       A.SURVEY_ADVICE,
		       A.CS_BACKGROUND,
		       A.INSERT_TIME,
		       A.INSERT_BY,
		       A.INSERT_TIMESTAMP,
		       A.UPDATE_BY,
		       A.UPDATE_TIMESTAMP,
		       A.UPDATE_TIME,
		       A.LOCIAL_FLAG_CODE
        FROM APP___CLM__DBUSER.T_SURVEY_APPLY  A WHERE 1 = 1 AND
        A.APPLY_ID IN (${survey_mode})]]>
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	
	
	<!-- add by huangjh_wb 复勘调查发送接口查询需要发起调查的赔案信息-->
	<select id="JRQD_findClaimRecheckSurveyApply"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    SELECT A.Case_No,
                 A.SURVEY_CODE,
                 F.SURVEY_REASON,
                 A.SURVEY_ORG,
                 A.APPLY_ORG
        FROM APP___CLM__DBUSER.T_SURVEY_APPLY  A,
          APP___CLM__DBUSER.T_CLAIM_SURVEY_TASK B,
          APP___CLM__DBUSER.T_CLAIM_SURVEY_BATCH F
        WHERE   A.SURVEY_RULE_ID = B.LIST_ID 
            AND B.BATCH_ID = F.BATCH_ID(+)
            AND A.BIZ_TYPE = '4'
          AND A.SURVEY_STATUS = '1']]>
		<![CDATA[ORDER BY A.APPLY_ID]]>
	</select>
	
	<!-- add by huangjh_wb 复勘调查发送接口查询详细调查信息-->
	<select id="JRQD_findClaimRecheckSurveyByCaseNo"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
          SELECT A.APPLY_ID,
          		 A.SURVEY_CODE,
                 F.BATCH_ID,
                 A.APPLY_SECTION,
                 F.SURVEY_REASON,
                 D.SURVEY_ITEM,
                 A.SURVEY_TYPE,
                 A.SURVEY_DESC,
                 A.SURVEY_ORG,
                 A.APPLY_PER,
                 A.APPLY_DATE,
                 A.APPLY_ORG,
                 A.SURVEY_STATUS,
                 A.LOCIAL_FLAG_CODE,
                 C.CONCLUSION_ID
        FROM APP___CLM__DBUSER.T_SURVEY_APPLY  A,
         	APP___CLM__DBUSER.T_CLAIM_SURVEY_TASK B,
         	APP___CLM__DBUSER.T_SURVEY_CONCLUSION C,
         	APP___CLM__DBUSER.T_SURVEY_ITEM D,
         	APP___CLM__DBUSER.T_UDMP_USER E,
         	APP___CLM__DBUSER.T_CLAIM_SURVEY_BATCH F
        WHERE 	A.SURVEY_RULE_ID = B.LIST_ID 
        		AND A.APPLY_ID = C.APPLY_ID(+)
        		AND A.APPLY_ID = D.APPLY_ID(+)
        		AND A.APPLY_PER = E.USER_ID(+)
        		AND B.BATCH_ID = F.BATCH_ID(+)
        		AND A.BIZ_TYPE = '4'
   				AND A.SURVEY_STATUS = '1'
   				AND A.CASE_NO = ${case_no}]]>
		<![CDATA[ORDER BY A.APPLY_ID]]>
	</select>
	<!-- 根据赔案号和调查批次查询调查申请流水号 -->
	<select id="JRQD_findSurveyApplyByCaseNoBatchId"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.APPLY_ID,
		       A.SURVEY_RULE_ID,
		       A.SURVEY_CODE,
		       A.BIZ_TYPE,
		       A.CASE_ID,
		       A.CASE_NO,
		       A.POLICY_CODE,
		       A.APPLY_CODE,
		       A.CS_APPLY_CODE,
		       A.CS_ACCEPT_CODE,
		       A.CS_ITEM,
		       A.RELATED_ID,
		       A.SURVEY_DOC_ID,
		       A.APPLY_SECTION,
		       A.SURVEY_STATUS,
		       A.SURVEY_DESC,
		       A.SURVEY_TYPE,
		       A.SURVEY_REASON,
		       A.SURVEY_ORG,
		       A.APPLY_ORG,
		       A.APPLY_PER,
		       A.APPLY_DATE,
		       A.SURVEY_PER,
		       A.SURVEY_BY_LEVEL,
		       A.REPEAL_REASON,
		       A.REMARK,
		       A.INTERNAL_RESUALT,
		       A.SURVEY_MODE,
		       A.SURVEY_ADVICE,
		       A.CS_BACKGROUND,
		       A.INSERT_TIME,
		       A.INSERT_BY,
		       A.INSERT_TIMESTAMP,
		       A.UPDATE_BY,
		       A.UPDATE_TIMESTAMP,
		       A.UPDATE_TIME,
		       A.LOCIAL_FLAG_CODE
  FROM APP___CLM__DBUSER.T_CLAIM_SURVEY_TASK B,
       APP___CLM__DBUSER.T_SURVEY_APPLY A
 WHERE B.BATCH_ID = #{batch_id}
 AND B.BIZ_NO = #{case_no}
 AND B.LIST_ID = A.SURVEY_RULE_ID(+)]]>
 <if test=" survey_code != null and survey_code != ''  "><![CDATA[ AND A.SURVEY_CODE = #{survey_code} ]]></if>
	</select>
	
	<!-- 产生审核报告 查询调查详细信息 -->
	<select id="JRQD_findSurveyCondition"  resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
	         SELECT A.SURVEY_DESC, B.SURVEY_CONCLUSION, B.REMARK
               FROM APP___CLM__DBUSER.t_Survey_Apply A, APP___CLM__DBUSER.T_SURVEY_CONCLUSION B
              WHERE A.APPLY_ID = B.APPLY_ID
                AND A.CASE_ID = #{case_id}
	    ]]>
	</select>
	
	
</mapper>
