<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.uw.dao.IReportDao">

	<!-- 按索引生成的查询条件 -->
	<sql id="queryUwLevelByLevelCodeCondition">
		<if test=" level_code != null and level_code != '' "><![CDATA[ AND A.LEVEL_CODE = #{level_code} ]]></if>
	</sql>

	<sql id="qualityCheckCondition">
		<if test=" organ_code != null and organ_code !=''"><![CDATA[ AND po.organ_code like #{organ_code,jdbcType=VARCHAR}||'%' ]]></if>
		<if test=" uw_source_type != null and uw_source_type !=''"><![CDATA[ AND po.uw_source_type = #{uw_source_type,jdbcType=VARCHAR} ]]></if>
		<if test=" uw_level_code  != null and uw_level_code!= '' "><![CDATA[ AND po.uw_level_code =#{uw_level_code,jdbcType=VARCHAR}]]></if>
		<!-- <if test=" uw_finish_time != null and uw_finish_time != '' "><![CDATA[ 
			AND po.uw_finish_time <= to_date(#{uw_finish_time},'YYYY-MM-DD HH24:MI:SS') 
			AND po.uw_finish_time >= to_date(#{uw_finish_time},'YYYY-MM-DD HH24:MI:SS')-31]]></if> -->
		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') >= #{uw_time_start} ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') <= #{uw_time_end} ]]></if>
		<!-- 核保用户查询字段修改 -->
		<if test=" user_id  != null and user_id != '' "><![CDATA[ AND po.uw_user_id =#{user_id,jdbcType=NUMERIC}]]></if>
		<!-- 核保人员等级查询条件添加 -->
		<if test=" uw_user_level_code  != null and uw_user_level_code != '' ">
		<![CDATA[ AND po.uw_user_id in (select distinct b.user_id 
                                       from t_udmp_group_role a left join t_udmp_group_user b on a.role_group_id=b.role_group_id
                                       left join t_udmp_role d on a.role_id=d.role_id
                                       where d.role_type = 2 and d.role_id in 
                                       (SELECT o2.role_id
                                        FROM T_UDMP_PERMISSION_INFO o1 
                                        left join t_udmp_role_permission o2 ON o1.PERMISSION_ID = o2.PERMISSION_ID 
                                        where o1.permission_type_id=(SELECT A.PERMISSION_TYPE_ID FROM t_udmp_permission_type A WHERE  A.PERMISSION_TYPE_NAME = 'UW_AUTH')
                                        and o1.perm_level_workflow in (#{uw_user_level_code})))]]></if>
	</sql>


	<!-- 查询质检清单个数操作 -->
	<select id="UW_queryUwList4QualityCheckTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<!-- <![CDATA[SELECT COUNT(1) from dev_uw.T_UW_qt_task ta,dev_uw.T_UW_policy po , dev_uw.T_UW_master m
	             where ta.uw_id = po.uw_id and ta.uw_id = m.uw_id ]]>
		<include refid="qualityCheckCondition" /> -->
		<![CDATA[select count(*) from (
select ROWNUM as RN,
     C.uw_user_level_code,
       C.organ_code,
       C.organ_name,
       C.uw_source_type,
       C.biz_code,
       C.uw_user_id,
       C.user_name,
       C.uw_user_real_name,
       C.uw_level_code,
       C.qt_user_code,
       C.qt_real_name,
       C.qt_comment,
       to_char(C.uw_finish_time,'YYYY-MM-DD') as uw_finish_time,
       c.qt_type,
       c.qt_result,
       c.qt_class
       from
       (select 
       uo.organ_code,
       uo.organ_name,
       (select st.type_name from dev_uw.T_UW_source_type st where st.uw_source_type =  um.uw_source_type) as uw_source_type,
       um.biz_code,
       um.uw_user_id,
       v_user_permission.user_name as user_name,
       v_user_permission.permission_name as uw_user_level_code,
        v_user_permission.real_name as uw_user_real_name,
       (select ul.level_desc from dev_uw.T_UW_level ul where ul.level_code=up.uw_level_code) as uw_level_code,
       (select u.user_name from DEV_PAS.T_UDMP_USER u where u.user_id = qt.qt_user_code) as qt_user_code,
       (select u.real_name from DEV_PAS.T_UDMP_USER u where u.user_id = qt.qt_user_code) as qt_real_name,
       qt.qt_comment,
       um.uw_finish_time,
       v_user_permission.perm_level_workflow,
       qt.qt_type,
       qt.qt_result,
       qt.qt_class
  from dev_uw.t_contract_master cm,
  		dev_uw.t_uw_policy up, 
       DEV_PAS.T_UDMP_ORG uo,
       dev_uw.T_UW_master um,
       DEV_PAS.T_UDMP_USER uu,
       dev_uw.T_UW_qt_task qt,
       (select uu.user_id,
               uu.real_name,
               uu.user_name,
               gr.role_id,
               rp.permission_id,
               pi.permission_name,
               pi.perm_level_workflow
          from DEV_PAS.T_UDMP_USER            uu,
              dev_uw.t_udmp_group_user      gu,
              dev_uw.t_udmp_group_role      gr,
              dev_uw.T_UDMP_ROLE_PERMISSION rp,
              dev_uw.T_UDMP_PERMISSION_INFO pi,
              dev_uw.t_udmp_role            tr,
              dev_uw.t_udmp_permission_type tup
         where uu.user_id = gu.user_id
           and gu.role_group_id = gr.role_group_id
           and rp.role_id = gr.role_id
           and rp.permission_id = pi.permission_id
           and tr.role_id = gr.role_id
           and tr.role_type = 2
           and tup.permission_type_name = 'UW_AUTH') v_user_permission
 where cm.organ_code = uo.organ_code
  and um.uw_id = cm.uw_id
  and um.uw_user_id = uu.user_id
  and uu.user_id = v_user_permission.user_id 
  and qt.uw_id = um.uw_id
  and um.uw_id = up.uw_id
  ]]>
  
  <if test=" organ_code != null and organ_code !=''"><![CDATA[ AND uo.organ_code like #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test=" uw_source_type != null and uw_source_type !=''"><![CDATA[ AND um.uw_source_type in (${uw_source_type}) ]]></if>
		<if test=" uw_level_code  != null and uw_level_code!= '' "><![CDATA[ AND um.uw_level_code =#{uw_level_code,jdbcType=VARCHAR}]]></if>
		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') >= #{uw_time_start} ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') <= #{uw_time_end} ]]></if>
		<if test=" user_id  != null and user_id != '' "><![CDATA[ AND um.uw_user_id =#{user_id,jdbcType=NUMERIC}]]></if>
		<if test=" uw_user_level_code  != null and uw_user_level_code != '' "><![CDATA[ AND  perm_level_workflow in (${uw_user_level_code})]]></if>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ AND v_user_permission.user_name like #{user_name,jdbcType=VARCHAR}||'%' ]]></if>
		<if test=" qt_type  != null and qt_type != '' "><![CDATA[ AND qt.qt_type = #{qt_type,jdbcType=VARCHAR} ]]></if>
		<![CDATA[ order by  uo.organ_code desc,um.uw_source_type desc, v_user_permission.perm_level_workflow desc )  C  )  ]]>
  
	</select>

	<!-- 分页查询质检清单数据 -->
	<select id="UW_queryUwList4QualityCheck" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select * from(
		select ROWNUM as RN,
	   C.uw_user_level_code,
       C.organ_code,
       C.organ_name,
       C.uw_source_type,
       C.biz_code,
       C.uw_user_id,
       C.user_name,
       C.uw_user_real_name,
       C.uw_level_code,
       C.qt_user_code,
       C.qt_real_name,
       C.qt_comment,
       to_char(C.uw_finish_time,'YYYY-MM-DD') as uw_finish_time,
       c.qt_type,
       c.qt_result,
       c.qt_cancel_flag,
       c.qt_class
       from
       (select 
       uo.organ_code,
       uo.organ_name,
       (select st.type_name from dev_uw.T_UW_source_type st where st.uw_source_type =  um.uw_source_type) as uw_source_type,
       um.biz_code,
       um.uw_user_id,
       v_user_permission.user_name as user_name,
       v_user_permission.permission_name as uw_user_level_code,
        v_user_permission.real_name as uw_user_real_name,
       (select ul.level_desc from dev_uw.T_UW_level ul where ul.level_code=up.uw_level_code) as uw_level_code,
       (select u.user_name from DEV_PAS.T_UDMP_USER u where u.user_id = qt.qt_user_code) as qt_user_code,
       (select u.real_name from DEV_PAS.T_UDMP_USER u where u.user_id = qt.qt_user_code) as qt_real_name,
       qt.qt_comment,
       um.uw_finish_time,
       v_user_permission.perm_level_workflow,
       qt.qt_type,
       qt.qt_result,
       qt.qt_cancel_flag,
       qt.qt_class
  from dev_uw.t_contract_master cm, 
  	   dev_uw.t_uw_policy up,
       DEV_PAS.T_UDMP_ORG uo,
       dev_uw.T_UW_master um,
       DEV_PAS.T_UDMP_USER uu,
       dev_uw.T_UW_qt_task qt,
       (select uu.user_id,
               uu.real_name,
               uu.user_name,
               gr.role_id,
               rp.permission_id,
               pi.permission_name,
               pi.perm_level_workflow
          from DEV_PAS.T_UDMP_USER            uu,
              dev_uw.t_udmp_group_user      gu,
              dev_uw.t_udmp_group_role      gr,
              dev_uw.T_UDMP_ROLE_PERMISSION rp,
              dev_uw.T_UDMP_PERMISSION_INFO pi,
              dev_uw.t_udmp_role            tr,
              dev_uw.t_udmp_permission_type tup
         where uu.user_id = gu.user_id
           and gu.role_group_id = gr.role_group_id
           and rp.role_id = gr.role_id
           and rp.permission_id = pi.permission_id
           and tr.role_id = gr.role_id
           and tr.role_type = 2
           and tup.permission_type_name = 'UW_AUTH') v_user_permission
 where cm.organ_code = uo.organ_code
  and um.uw_id = cm.uw_id
  and um.uw_user_id = uu.user_id
  and uu.user_id = v_user_permission.user_id
  and qt.uw_id = um.uw_id
   and um.uw_id = up.uw_id
   ]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[ AND uo.organ_code like #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test=" uw_source_type != null and uw_source_type !=''"><![CDATA[ AND um.uw_source_type in (${uw_source_type}) ]]></if>
		<if test=" uw_level_code  != null and uw_level_code!= '' "><![CDATA[ AND um.uw_level_code =#{uw_level_code,jdbcType=VARCHAR}]]></if>
		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') >= #{uw_time_start} ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') <= #{uw_time_end} ]]></if>
		<if test=" user_id  != null and user_id != '' "><![CDATA[ AND um.uw_user_id =#{user_id,jdbcType=NUMERIC}]]></if>
		<if test=" uw_user_level_code  != null and uw_user_level_code != '' "><![CDATA[ AND  perm_level_workflow in (${uw_user_level_code})]]></if>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ AND v_user_permission.user_name like #{user_name,jdbcType=VARCHAR}||'%' ]]></if>
		<if test=" qt_type  != null and qt_type != '' "><![CDATA[ AND qt.qt_type = #{qt_type,jdbcType=VARCHAR} ]]></if>
		<![CDATA[ order by  uo.organ_code desc,um.uw_source_type desc, v_user_permission.perm_level_workflow desc )  C WHERE ROWNUM   <= #{LESS_NUM} ) f where f.rn > #{GREATER_NUM} ]]>
	</select>

	<!-- 导出质检清单数据 -->
	<select id="UW_exportUwList4QualityCheck" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select * from (
select ROWNUM as RN,
     C.uw_user_level_code,
       C.organ_code,
       C.organ_name,
       C.uw_source_type,
       C.biz_code,
       C.uw_user_id,
       C.user_name,
       C.uw_user_real_name,
       C.uw_level_code,
       C.qt_user_code,
       C.qt_real_name,
       C.qt_comment,
       to_char(C.uw_finish_time,'YYYY-MM-DD') as uw_finish_time,
       c.qt_type,
       c.qt_result,
       C.qt_cancel_flag,
       c.qt_class
       from
       (select 
       uo.organ_code,
       uo.organ_name,
       (select st.type_name from dev_uw.T_UW_source_type st where st.uw_source_type =  um.uw_source_type) as uw_source_type,
       um.biz_code,
       um.uw_user_id,
       v_user_permission.user_name as user_name,
       v_user_permission.permission_name as uw_user_level_code,
        v_user_permission.real_name as uw_user_real_name,
       (select ul.level_desc from dev_uw.T_UW_level ul where ul.level_code=up.uw_level_code) as uw_level_code,
       (select u.user_name from DEV_PAS.T_UDMP_USER u where u.user_id = qt.qt_user_code) as qt_user_code,
       (select u.real_name from DEV_PAS.T_UDMP_USER u where u.user_id = qt.qt_user_code) as qt_real_name,
       qt.qt_comment,
       um.uw_finish_time,
       v_user_permission.perm_level_workflow,
       qt.qt_type,
       qt.qt_result,
       qt.qt_cancel_flag,
       qt.qt_class
  from dev_uw.t_contract_master cm,
  		dev_uw.t_uw_policy up, 
       DEV_PAS.T_UDMP_ORG uo,
       dev_uw.T_UW_master um,
       DEV_PAS.T_UDMP_USER uu,
       dev_uw.T_UW_qt_task qt,
       (select uu.user_id,
               uu.real_name,
               uu.user_name,
               gr.role_id,
               rp.permission_id,
               pi.permission_name,
               pi.perm_level_workflow
          from DEV_PAS.T_UDMP_USER            uu,
              dev_uw.t_udmp_group_user      gu,
              dev_uw.t_udmp_group_role      gr,
              dev_uw.T_UDMP_ROLE_PERMISSION rp,
              dev_uw.T_UDMP_PERMISSION_INFO pi,
              dev_uw.t_udmp_role            tr,
              dev_uw.t_udmp_permission_type tup
         where uu.user_id = gu.user_id
           and gu.role_group_id = gr.role_group_id
           and rp.role_id = gr.role_id
           and rp.permission_id = pi.permission_id
           and tr.role_id = gr.role_id
           and tr.role_type = 2
           and tup.permission_type_name = 'UW_AUTH') v_user_permission
 where cm.organ_code = uo.organ_code
  and um.uw_id = cm.uw_id
  and um.uw_user_id = uu.user_id
  and uu.user_id = v_user_permission.user_id
   and um.uw_id = up.uw_id
  ]]>
		
				<if test=" organ_code != null and organ_code !=''"><![CDATA[ AND uo.organ_code like #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test=" uw_source_type != null and uw_source_type !=''"><![CDATA[ AND um.uw_source_type in (${uw_source_type}) ]]></if>
		<if test=" uw_level_code  != null and uw_level_code!= '' "><![CDATA[ AND um.uw_level_code =#{uw_level_code,jdbcType=VARCHAR}]]></if>
		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') >= #{uw_time_start} ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') <= #{uw_time_end} ]]></if>
		<if test=" user_id  != null and user_id != '' "><![CDATA[ AND um.uw_user_id =#{user_id,jdbcType=NUMERIC}]]></if>
		<if test=" uw_user_level_code  != null and uw_user_level_code != '' "><![CDATA[ AND  perm_level_workflow in (${uw_user_level_code})]]></if>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ AND v_user_permission.user_name like #{user_name,jdbcType=VARCHAR}||'%' ]]></if>
		<if test=" qt_type  != null and qt_type != '' "><![CDATA[ AND qt.qt_type = #{qt_type,jdbcType=VARCHAR} ]]></if>
		
    <![CDATA[ and qt.uw_id = um.uw_id order by  uo.organ_code desc,um.uw_source_type desc, v_user_permission.perm_level_workflow desc)  C   )]]>
		
	</select>


	<!-- 分页查询保全核保（保单层）清单数据 -->
	<select id="UW_queryCsList4Policy" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
	select * from(
select D.*,ROWNUM as RN  from(
select 

to_char(um.biz_code) accept_Code ,
(select a.service_name from dev_uw.t_service a where a.service_code = um.service_code) cs_Service ,
to_char(um.biz_date,'yyyy-MM-dd') biz_date ,
to_char(um.uw_submit_time,'yyyy-MM-dd') uw_submit_time,
to_char(um.uw_finish_time,'yyyy-MM-dd') uw_finish_time,
to_char((select status_detail_name from dev_uw.t_uw_status_detail where status_detail_code = um.uw_status_detail)) uw_status,
to_char(up.policy_code) policy_code ,
(select a.real_name from DEV_PAS.T_UDMP_USER a where a.user_id = um.uw_user_id) real_name,
(select a.user_name from DEV_PAS.T_UDMP_USER a where a.user_id = um.uw_user_id) user_name,
to_char((select decision_desc from dev_uw.t_policy_decision where  decision_code =  up.policy_decision)) uw_decision,
(SELECT DOCUMENT_NO FROM DEV_UW.T_PENOTICE WHERE DOC_LIST_ID = (SELECT MIN(DOC_LIST_ID) FROM DEV_UW.T_PENOTICE
    WHERE UW_ID = UP.UW_ID)) DOCUMENT_NO1,
(SELECT DOCUMENT_NO FROM DEV_UW.T_PENOTICE WHERE DOC_LIST_ID = (SELECT MAX(DOC_LIST_ID) FROM DEV_UW.T_PENOTICE
    WHERE UW_ID = UP.UW_ID)) DOCUMENT_NO2,
(SELECT DOCUMENT_NO FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE RREPORT_ID = (SELECT MIN(RREPORT_ID) FROM DEV_UW.T_SURVIVAL_INVESTIGATION
    WHERE UW_ID = UP.UW_ID)) RREPORT_CODE1,
(SELECT DOCUMENT_NO FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE RREPORT_ID = (SELECT MAX(RREPORT_ID) FROM DEV_UW.T_SURVIVAL_INVESTIGATION
    WHERE UW_ID = UP.UW_ID)) RREPORT_CODE2,
nvl((select case when POSITIVE_INDI = '0' then '阴性' 
                   when POSITIVE_INDI = '1' then '阳性' 
                   else '未知' end from dev_uw.T_PENOTICE  where up.uw_id =uw_id and rownum = 1),'') positive_indi,
(SELECT DOCUMENT_NO FROM DEV_UW.T_UW_NOTICE WHERE LIST_ID = (SELECT MAX(LIST_ID) FROM DEV_UW.T_UW_NOTICE T WHERE T.UW_ID = UM.UW_ID)) AS UW_NOTICE,
         case when um.uw_status_detail = '0302' then '是' else '否' end exceed,
         CASE WHEN up.UW_ID>= 1000000000000 THEN 
        (SELECT TO_CHAR(WM_CONCAT((SELECT PRODUCT_NAME_SYS
                                   FROM DEV_PDS.T_BUSINESS_PRODUCT
                                  WHERE PRODUCT_CODE_SYS = BUSI_PROD_CODE)))
          FROM DEV_UW.T_UW_BUSI_PROD
         WHERE UW_ID = up.UW_ID
           AND EXISTS (SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT R WHERE R.PRODUCT_CODE_SYS = BUSI_PROD_CODE AND R.PRODUCT_CATEGORY = '10001' AND ROWNUM = 1))
           ELSE 
 (select to_char(wm_concat((select product_name_sys from dev_pds.t_business_product where product_code_sys = busi_prod_code))) from dev_uw.t_uw_busi_prod  where uw_id = up.uw_id and master_busi_item_id is null) 
 END product_name_sys1, TO_CHAR(UP.MULTI_MAINRISK_FLAG) AS MULTI_MAINRISK_FLAG

 FROM DEV_UW.T_UW_MASTER UM, DEV_UW.T_UW_POLICY UP, DEV_UW.T_UW_AUTO UA
     WHERE UM.UW_ID = UP.UW_ID AND UM.UW_ID = UA.UW_ID AND UP.UW_ID = UA.UW_ID
       AND UM.UW_SOURCE_TYPE = '2' AND UA.RULE_RUN_STATUS = 'V02' AND UP.POLICY_DECISION IS NOT NULL
 
		 ]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
		<if test=" cs_service != null and cs_service !='' "><![CDATA[ AND um.service_code in (${cs_service}) ]]></if>
		<if test=" uw_decision != null and uw_decision !='' "><![CDATA[ AND up.policy_decision in (${uw_decision}) ]]></if>
		
		<if test=" cs_accept_time_start  != null  and  cs_accept_time_start  != '' "> <![CDATA[AND (um.biz_date BETWEEN trunc(to_date(#{cs_accept_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{cs_accept_time_end}, 'YYYY-MM-DD'))  )]]></if>
		<if test=" uw_finish_time_start  != null  and  uw_finish_time_start  != '' "> <![CDATA[AND (um.uw_finish_time BETWEEN trunc(to_date(#{uw_finish_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{uw_finish_time_end}, 'YYYY-MM-DD'))  )]]></if>
		<if test=" uw_submit_time_start  != null  and  uw_submit_time_start  != '' "> <![CDATA[AND (um.uw_submit_time BETWEEN trunc(to_date(#{uw_submit_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{uw_submit_time_end}, 'YYYY-MM-DD')) )]]></if>
		
		<![CDATA[ order by cs_Service ,uw_finish_time,BIZ_DATE,uw_submit_time]]>
		<![CDATA[) D where rownum <= #{LESS_NUM}  ]]>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ AND D.user_name like #{user_name,jdbcType=VARCHAR}||'%' ]]></if>
		<![CDATA[ ) f where f.rn > #{GREATER_NUM} ]]>
	</select>


	<!-- 导出保全核保（保单层）清单数据 -->
	<select id="UW_queryCsList4PolicyList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
	select * from(
select D.*,ROWNUM as RN  from(
select 
to_char(um.biz_code) accept_Code ,
(select a.service_name from dev_uw.t_service a where a.service_code = um.service_code) cs_Service ,
to_char(um.biz_date,'yyyy-MM-dd') biz_date ,
to_char(um.uw_submit_time,'yyyy-MM-dd') uw_submit_time,
to_char(um.uw_finish_time,'yyyy-MM-dd') uw_finish_time,
to_char((select status_detail_name from dev_uw.t_uw_status_detail where status_detail_code = um.uw_status_detail)) uw_status,
to_char(up.policy_code) policy_code ,
(select a.real_name from DEV_PAS.T_UDMP_USER a where a.user_id = um.uw_user_id) real_name,
(select a.user_name from DEV_PAS.T_UDMP_USER a where a.user_id = um.uw_user_id) user_name,
to_char((select decision_desc from dev_uw.t_policy_decision where  decision_code =  up.policy_decision)) uw_decision,
(SELECT DOCUMENT_NO FROM DEV_UW.T_PENOTICE WHERE DOC_LIST_ID = (SELECT MIN(DOC_LIST_ID) FROM DEV_UW.T_PENOTICE
    WHERE UW_ID = UP.UW_ID)) DOCUMENT_NO1,
(SELECT DOCUMENT_NO FROM DEV_UW.T_PENOTICE WHERE DOC_LIST_ID = (SELECT MAX(DOC_LIST_ID) FROM DEV_UW.T_PENOTICE
    WHERE UW_ID = UP.UW_ID)) DOCUMENT_NO2,
(SELECT DOCUMENT_NO FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE RREPORT_ID = (SELECT MIN(RREPORT_ID) FROM DEV_UW.T_SURVIVAL_INVESTIGATION
    WHERE UW_ID = UP.UW_ID)) RREPORT_CODE1,
(SELECT DOCUMENT_NO FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE RREPORT_ID = (SELECT MAX(RREPORT_ID) FROM DEV_UW.T_SURVIVAL_INVESTIGATION
    WHERE UW_ID = UP.UW_ID)) RREPORT_CODE2,
nvl((select case when POSITIVE_INDI = '0' then '阴性' 
                   when POSITIVE_INDI = '1' then '阳性' 
                   else '未知' end from dev_uw.T_PENOTICE  where up.uw_id =uw_id and rownum = 1),'') positive_indi,

(SELECT DOCUMENT_NO FROM DEV_UW.T_UW_NOTICE WHERE LIST_ID = (SELECT MAX(LIST_ID) FROM DEV_UW.T_UW_NOTICE T WHERE T.UW_ID = UM.UW_ID)) AS UW_NOTICE,
         case when um.uw_status_detail = '0302' then '是' else '否' end exceed,
         CASE WHEN up.UW_ID>= 1000000000000 THEN 
        (SELECT TO_CHAR(WM_CONCAT((SELECT PRODUCT_NAME_SYS
                                   FROM DEV_PDS.T_BUSINESS_PRODUCT
                                  WHERE PRODUCT_CODE_SYS = BUSI_PROD_CODE)))
          FROM DEV_UW.T_UW_BUSI_PROD
         WHERE UW_ID = up.UW_ID
           AND EXISTS (SELECT 1 FROM DEV_PDS.T_BUSINESS_PRODUCT R WHERE R.PRODUCT_CODE_SYS = BUSI_PROD_CODE AND R.PRODUCT_CATEGORY = '10001' AND ROWNUM = 1))
           ELSE 
 (select to_char(wm_concat((select product_name_sys from dev_pds.t_business_product where product_code_sys = busi_prod_code))) from dev_uw.t_uw_busi_prod  where uw_id = up.uw_id and master_busi_item_id is null)
 END product_name_sys1, TO_CHAR(UP.MULTI_MAINRISK_FLAG) AS MULTI_MAINRISK_FLAG

 from DEV_UW.T_UW_MASTER UM, DEV_UW.T_UW_POLICY UP, DEV_UW.T_UW_AUTO UA
     WHERE UM.UW_ID = UP.UW_ID AND UM.UW_ID = UA.UW_ID AND UP.UW_ID = UA.UW_ID
       AND UM.UW_SOURCE_TYPE = '2' AND UA.RULE_RUN_STATUS = 'V02' AND UP.POLICY_DECISION IS NOT NULL
 
		 ]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
		<if test=" cs_service != null and cs_service !='' "><![CDATA[ AND um.service_code in (${cs_service}) ]]></if>
		<if test=" uw_decision != null and uw_decision !='' "><![CDATA[ AND up.policy_decision in (${uw_decision}) ]]></if>

		<if test=" cs_accept_time_start  != null  and  cs_accept_time_start  != '' "> <![CDATA[AND (um.biz_date BETWEEN trunc(to_date(#{cs_accept_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{cs_accept_time_end}, 'YYYY-MM-DD'))  )]]></if>
		<if test=" uw_finish_time_start  != null  and  uw_finish_time_start  != '' "> <![CDATA[AND (um.uw_finish_time BETWEEN trunc(to_date(#{uw_finish_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{uw_finish_time_end}, 'YYYY-MM-DD'))  )]]></if>
		<if test=" uw_submit_time_start  != null  and  uw_submit_time_start  != '' "> <![CDATA[AND (um.uw_submit_time BETWEEN trunc(to_date(#{uw_submit_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{uw_submit_time_end}, 'YYYY-MM-DD')) )]]></if>
		
		<![CDATA[ order by cs_Service ,uw_finish_time,BIZ_DATE,uw_submit_time]]>
		<![CDATA[) D   ]]>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ where D.user_name like #{user_name,jdbcType=VARCHAR}||'%' ]]></if>
		<![CDATA[ )  ]]>
	</select>



	<!-- 查询保全核保（保单层）清单个数操作 -->
	<select id="UW_queryCsList4PolicyTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(*) FROM(
		SELECT D.*,ROWNUM AS RN  FROM(
		SELECT 
		(SELECT A.SERVICE_NAME FROM DEV_UW.T_SERVICE A WHERE A.SERVICE_CODE = UM.SERVICE_CODE) CS_SERVICE ,
		TO_CHAR(UM.BIZ_DATE,'yyyy-MM-dd') BIZ_DATE ,
		TO_CHAR(UM.UW_SUBMIT_TIME,'yyyy-MM-dd') UW_SUBMIT_TIME,
		TO_CHAR(UM.UW_FINISH_TIME,'yyyy-MM-dd') UW_FINISH_TIME,
		(SELECT A.USER_NAME FROM DEV_PAS.T_UDMP_USER A WHERE A.USER_ID = UM.UW_USER_ID) USER_NAME,
		TO_CHAR((SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION WHERE DECISION_CODE = UP.POLICY_DECISION)) UW_DECISION 
 		FROM DEV_UW.T_UW_MASTER UM, DEV_UW.T_UW_POLICY UP, DEV_UW.T_UW_AUTO UA
     		WHERE UM.UW_ID = UP.UW_ID AND UM.UW_ID = UA.UW_ID AND UP.UW_ID = UA.UW_ID
       		AND UM.UW_SOURCE_TYPE = '2' AND UA.RULE_RUN_STATUS = 'V02' AND UP.POLICY_DECISION IS NOT NULL
		 ]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
		<if test=" cs_service != null and cs_service !='' "><![CDATA[ AND um.service_code in (${cs_service}) ]]></if>
		<if test=" uw_decision != null and uw_decision !='' "><![CDATA[ AND up.policy_decision in (${uw_decision}) ]]></if>

		<if test=" cs_accept_time_start  != null  and  cs_accept_time_start  != '' "> <![CDATA[AND (um.biz_date BETWEEN trunc(to_date(#{cs_accept_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{cs_accept_time_end}, 'YYYY-MM-DD'))  )]]></if>
		<if test=" uw_finish_time_start  != null  and  uw_finish_time_start  != '' "> <![CDATA[AND (um.uw_finish_time BETWEEN trunc(to_date(#{uw_finish_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{uw_finish_time_end}, 'YYYY-MM-DD'))  )]]></if>
		<if test=" uw_submit_time_start  != null  and  uw_submit_time_start  != '' "> <![CDATA[AND (um.uw_submit_time BETWEEN trunc(to_date(#{uw_submit_time_start}, 'YYYY-MM-DD')) AND trunc(to_date(#{uw_submit_time_end}, 'YYYY-MM-DD')) )]]></if>
		
		<![CDATA[ ORDER BY CS_SERVICE ,UW_FINISH_TIME,BIZ_DATE,UW_SUBMIT_TIME]]>
		<![CDATA[) D   ]]>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ where D.user_name like #{user_name,jdbcType=VARCHAR}||'%' ]]></if>
		<![CDATA[ )  ]]>
	</select>

	<!-- 分页查询保全核保（责任组）清单数据 -->

	<select id="queryCSList4ResponGrouplist" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT * FROM (SELECT ROWNUM AS RN, D.*
          FROM (
                SELECT TO_CHAR(UM.BIZ_CODE) ACCEPT_CODE,
                (SELECT A.SERVICE_NAME
                   FROM DEV_UW.T_SERVICE A
                  WHERE A.SERVICE_CODE = UM.SERVICE_CODE) CS_SERVICE,
                
                TO_CHAR(UM.BIZ_DATE, 'YYYY-MM-DD') BIZ_DATE,
                TO_CHAR(UM.UW_SUBMIT_TIME, 'YYYY-MM-DD') UW_SUBMIT_TIME,
                TO_CHAR(UM.UW_FINISH_TIME, 'YYYY-MM-DD') UW_FINISH_TIME,
                TO_CHAR((SELECT STATUS_DETAIL_NAME
                          FROM DEV_UW.T_UW_STATUS_DETAIL
                         WHERE STATUS_DETAIL_CODE = UM.UW_STATUS_DETAIL)) UW_STATUS,
                TO_CHAR(UP.POLICY_CODE) POLICY_CODE,
                (SELECT A.REAL_NAME
                   FROM DEV_PAS.T_UDMP_USER A
                  WHERE A.USER_ID = UM.UW_USER_ID) REAL_NAME,
                (SELECT A.USER_NAME
                   FROM DEV_PAS.T_UDMP_USER A
                  WHERE A.USER_ID = UM.UW_USER_ID) USER_NAME,
                TO_CHAR((SELECT DECISION_DESC
                          FROM DEV_UW.T_POLICY_DECISION
                         WHERE DECISION_CODE = UP.POLICY_DECISION)) UW_DECISION,               
                  (SELECT PRODUCT_NAME_SYS
                                            FROM DEV_PDS.T_BUSINESS_PRODUCT
                                           WHERE PRODUCT_CODE_SYS =
                                                 UBP.BUSI_PROD_CODE) AS BUSI_ITEM_ID,
                TO_CHAR((SELECT DECISION_DESC
                          FROM DEV_UW.T_PRODUCT_DECISION
                         WHERE DECISION_CODE = UBP.DECISION_CODE)) BUSI_DECISION_CODE,
                TO_CHAR((SELECT DECISION_DESC
                          FROM DEV_UW.T_PRODUCT_DECISION
                         WHERE DECISION_CODE = UPP.DECISION_CODE)) DECISION_CODE,
                TO_CHAR((SELECT PRODUCT_NAME
                          FROM DEV_PDS.T_PRODUCT_LIFE
                         WHERE INTERNAL_ID = UPP.PRODUCT_CODE)) ITEM_ID,
                TO_CHAR((SELECT A.EM_VALUE
                          FROM DEV_UW.T_UW_EXTRA_PREM A
                         WHERE UPP.UW_PRD_ID = A.UW_PRD_ID
                           AND ROWNUM = 1)) EM_VALUE
		  FROM DEV_UW.T_UW_MASTER    UM,
		       DEV_UW.T_UW_POLICY    UP,
		       DEV_UW.T_UW_BUSI_PROD UBP,
		       DEV_UW.T_UW_PRODUCT   UPP,
		       DEV_UW.T_UW_AUTO      UA
		 WHERE UM.UW_ID = UP.UW_ID
		   AND UM.UW_ID = UBP.UW_ID
		   AND UM.UW_ID = UPP.UW_ID
		   AND UM.UW_ID = UA.UW_ID
		   AND UBP.UW_ID = UPP.UW_ID
		   AND UA.UW_ID = UBP.UW_ID
		   AND UBP.UW_BUSI_ID = UPP.UW_BUSI_ID
		   AND UBP.BUSI_ITEM_ID = UPP.BUSI_ITEM_ID
		   AND UA.UW_ID = UPP.UW_ID
		   AND UA.UW_ID = UP.UW_ID
		   AND UA.RULE_RUN_STATUS = 'V02'
		   AND UM.UW_SOURCE_TYPE = '2'
		   AND UP.POLICY_DECISION IS NOT NULL
		   AND ((LENGTH(UM.UW_ID) < 13 AND (DECISION_INDI != 1 OR DECISION_INDI IS NULL))
		   OR (LENGTH(UM.UW_ID) >= 13 AND 1 = 1))]]>
            <if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
            <if test=" cs_service != null and cs_service !='' "><![CDATA[ AND UM.SERVICE_CODE IN (${cs_service}) ]]></if>

            <if test=" cs_accept_time_start  != null  and  cs_accept_time_start  != '' "><![CDATA[ AND TO_CHAR(UM.BIZ_DATE,'YYYY-MM-DD') >= #{cs_accept_time_start}  ]]></if>
            <if test=" cs_accept_time_end  != null  and  cs_accept_time_end  != ''  "><![CDATA[ AND TO_CHAR(UM.BIZ_DATE,'YYYY-MM-DD') <= #{cs_accept_time_end}  ]]></if>

            <if test=" uw_finish_time_start  != null  and  uw_finish_time_start  != '' "><![CDATA[ AND TO_CHAR(UM.UW_FINISH_TIME,'YYYY-MM-DD') >= #{uw_finish_time_start}  ]]></if>
            <if test=" uw_finish_time_end  != null  and  uw_finish_time_end  != ''  "><![CDATA[ AND TO_CHAR(UM.UW_FINISH_TIME,'YYYY-MM-DD') <= #{uw_finish_time_end}  ]]></if>

            <if test=" uw_submit_time_start  != null  and  uw_submit_time_start  != '' "><![CDATA[ AND TO_CHAR(UM.UW_SUBMIT_TIME,'YYYY-MM-DD') >= #{uw_submit_time_start}  ]]></if>
            <if test=" uw_submit_time_end  != null  and  uw_submit_time_end  != ''  "><![CDATA[ AND TO_CHAR(UM.UW_SUBMIT_TIME,'YYYY-MM-DD') <= #{uw_submit_time_end}  ]]></if>
            <![CDATA[ ORDER BY CS_SERVICE, UW_FINISH_TIME, BIZ_DATE, UW_SUBMIT_TIME  ]]>
                ) D
        <![CDATA[ WHERE ROWNUM <= #{LESS_NUM}) F ]]>
 WHERE F.RN > #{GREATER_NUM}

	</select>


	<!-- 查询保全核保（责任组层）清单个数操作 -->
	<select id="queryCSList4ResponGrouplist_count" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[select count(*) from (
       SELECT TO_CHAR(UM.BIZ_CODE) ACCEPT_CODE
                
		  FROM DEV_UW.T_UW_MASTER    UM,
		       DEV_UW.T_UW_POLICY    UP,
		       DEV_UW.T_UW_BUSI_PROD UBP,
		       DEV_UW.T_UW_PRODUCT   UPP,
		       DEV_UW.T_UW_AUTO      UA
		 WHERE UM.UW_ID = UP.UW_ID
		   AND UM.UW_ID = UBP.UW_ID
		   AND UM.UW_ID = UPP.UW_ID
		   AND UM.UW_ID = UA.UW_ID
		   AND UBP.UW_ID = UPP.UW_ID
		   AND UA.UW_ID = UBP.UW_ID
		   AND UBP.UW_BUSI_ID = UPP.UW_BUSI_ID
		   AND UBP.BUSI_ITEM_ID = UPP.BUSI_ITEM_ID
		   AND UA.UW_ID = UPP.UW_ID
		   AND UA.UW_ID = UP.UW_ID
		   AND UA.RULE_RUN_STATUS = 'V02'
		   AND UM.UW_SOURCE_TYPE = '2'
		   AND UP.POLICY_DECISION IS NOT NULL
		   AND ((LENGTH(UM.UW_ID) < 13 AND (DECISION_INDI != 1 OR DECISION_INDI IS NULL))
		   OR (LENGTH(UM.UW_ID) >= 13 AND 1 = 1))]]>
  
  			<if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
            <if test=" cs_service != null and cs_service !='' "><![CDATA[ AND UM.SERVICE_CODE IN (${cs_service}) ]]></if>

            <if test=" cs_accept_time_start  != null  and  cs_accept_time_start  != '' "><![CDATA[ AND TO_CHAR(UM.BIZ_DATE,'YYYY-MM-DD') >= #{cs_accept_time_start}  ]]></if>
            <if test=" cs_accept_time_end  != null  and  cs_accept_time_end  != ''  "><![CDATA[ AND TO_CHAR(UM.BIZ_DATE,'YYYY-MM-DD') <= #{cs_accept_time_end}  ]]></if>

            <if test=" uw_finish_time_start  != null  and  uw_finish_time_start  != '' "><![CDATA[ AND TO_CHAR(UM.UW_FINISH_TIME,'YYYY-MM-DD') >= #{uw_finish_time_start}  ]]></if>
            <if test=" uw_finish_time_end  != null  and  uw_finish_time_end  != ''  "><![CDATA[ AND TO_CHAR(UM.UW_FINISH_TIME,'YYYY-MM-DD') <= #{uw_finish_time_end}  ]]></if>

            <if test=" uw_submit_time_start  != null  and  uw_submit_time_start  != '' "><![CDATA[ AND TO_CHAR(UM.UW_SUBMIT_TIME,'YYYY-MM-DD') >= #{uw_submit_time_start}  ]]></if>
            <if test=" uw_submit_time_end  != null  and  uw_submit_time_end  != ''  "><![CDATA[ AND TO_CHAR(UM.UW_SUBMIT_TIME,'YYYY-MM-DD') <= #{uw_submit_time_end}  ]]></if>
		
		<![CDATA[  )  ]]>
	</select>



	<!-- 导出保全核保（责任组）清单数据 -->
	<select id="UW_queryCsList4PolicyGroup_List" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT TO_CHAR(UM.BIZ_CODE) ACCEPT_CODE,
                (SELECT A.SERVICE_NAME
                   FROM DEV_UW.T_SERVICE A
                  WHERE A.SERVICE_CODE = UM.SERVICE_CODE) CS_SERVICE,
                
                TO_CHAR(UM.BIZ_DATE, 'YYYY-MM-DD') BIZ_DATE,
                TO_CHAR(UM.UW_SUBMIT_TIME, 'YYYY-MM-DD') UW_SUBMIT_TIME,
                TO_CHAR(UM.UW_FINISH_TIME, 'YYYY-MM-DD') UW_FINISH_TIME,
                TO_CHAR((SELECT STATUS_DETAIL_NAME
                          FROM DEV_UW.T_UW_STATUS_DETAIL
                         WHERE STATUS_DETAIL_CODE = UM.UW_STATUS_DETAIL)) UW_STATUS,
                TO_CHAR(UP.POLICY_CODE) POLICY_CODE,
                (SELECT A.REAL_NAME
                   FROM DEV_PAS.T_UDMP_USER A
                  WHERE A.USER_ID = UM.UW_USER_ID) REAL_NAME,
                (SELECT A.USER_NAME
                   FROM DEV_PAS.T_UDMP_USER A
                  WHERE A.USER_ID = UM.UW_USER_ID) USER_NAME,
                TO_CHAR((SELECT DECISION_DESC
                          FROM DEV_UW.T_POLICY_DECISION
                         WHERE DECISION_CODE = UP.POLICY_DECISION)) UW_DECISION,               
                  (SELECT PRODUCT_NAME_SYS
                                            FROM DEV_PDS.T_BUSINESS_PRODUCT
                                           WHERE PRODUCT_CODE_SYS =
                                                 UBP.BUSI_PROD_CODE) AS BUSI_ITEM_ID,
                TO_CHAR((SELECT DECISION_DESC
                          FROM DEV_UW.T_PRODUCT_DECISION
                         WHERE DECISION_CODE = UBP.DECISION_CODE)) BUSI_DECISION_CODE,
                TO_CHAR((SELECT DECISION_DESC
                          FROM DEV_UW.T_PRODUCT_DECISION
                         WHERE DECISION_CODE = UPP.DECISION_CODE)) DECISION_CODE,
                TO_CHAR((SELECT PRODUCT_NAME
                          FROM DEV_PDS.T_PRODUCT_LIFE
                         WHERE INTERNAL_ID = UPP.PRODUCT_CODE)) ITEM_ID,
                TO_CHAR((SELECT A.EM_VALUE
                          FROM DEV_UW.T_UW_EXTRA_PREM A
                         WHERE UPP.UW_PRD_ID = A.UW_PRD_ID
                           AND ROWNUM = 1)) EM_VALUE
		  FROM DEV_UW.T_UW_MASTER    UM,
		       DEV_UW.T_UW_POLICY    UP,
		       DEV_UW.T_UW_BUSI_PROD UBP,
		       DEV_UW.T_UW_PRODUCT   UPP,
		       DEV_UW.T_UW_AUTO      UA
		 WHERE UM.UW_ID = UP.UW_ID
		   AND UM.UW_ID = UBP.UW_ID
		   AND UM.UW_ID = UPP.UW_ID
		   AND UM.UW_ID = UA.UW_ID
		   AND UBP.UW_ID = UPP.UW_ID
		   AND UA.UW_ID = UBP.UW_ID
		   AND UBP.UW_BUSI_ID = UPP.UW_BUSI_ID
		   AND UBP.BUSI_ITEM_ID = UPP.BUSI_ITEM_ID
		   AND UA.UW_ID = UPP.UW_ID
		   AND UA.UW_ID = UP.UW_ID
		   AND UA.RULE_RUN_STATUS = 'V02'
		   AND UM.UW_SOURCE_TYPE = '2'
		   AND UP.POLICY_DECISION IS NOT NULL
		   AND ((LENGTH(UM.UW_ID) < 13 AND (DECISION_INDI != 1 OR DECISION_INDI IS NULL))
		   OR (LENGTH(UM.UW_ID) >= 13 AND 1 = 1))
		 ]]>
		 <if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
		<if test=" cs_service != null and cs_service !='' "><![CDATA[ AND um.service_code in (${cs_service}) ]]></if>
		
		<if test=" cs_accept_time_start  != null  and  cs_accept_time_start  != '' "><![CDATA[ AND to_char(um.biz_date,'YYYY-MM-DD') >= #{cs_accept_time_start}  ]]></if>
		<if test=" cs_accept_time_end  != null  and  cs_accept_time_end  != ''  "><![CDATA[ AND to_char(um.biz_date,'YYYY-MM-DD') <= #{cs_accept_time_end}  ]]></if>
		
		<if test=" uw_finish_time_start  != null  and  uw_finish_time_start  != '' "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') >= #{uw_finish_time_start}  ]]></if>
		<if test=" uw_finish_time_end  != null  and  uw_finish_time_end  != ''  "><![CDATA[ AND to_char(um.uw_finish_time,'YYYY-MM-DD') <= #{uw_finish_time_end}  ]]></if>
		
		<if test=" uw_submit_time_start  != null  and  uw_submit_time_start  != '' "><![CDATA[ AND to_char(um.uw_submit_time,'YYYY-MM-DD') >= #{uw_submit_time_start}  ]]></if>
		<if test=" uw_submit_time_end  != null  and  uw_submit_time_end  != ''  "><![CDATA[ AND to_char(um.uw_submit_time,'YYYY-MM-DD') <= #{uw_submit_time_end}  ]]></if>
		<![CDATA[ORDER BY CS_SERVICE, UW_FINISH_TIME, BIZ_DATE, UW_SUBMIT_TIME]]>
	</select>


	<!-- 查询所有核保来源操作 -->
	<select id="UW_findAllUwSourceType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.TYPE_NAME, A.UW_SOURCE_TYPE FROM dev_uw.T_UW_SOURCE_TYPE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_SOURCE_TYPE ]]>
	</select>

	<!-- 查询所有核保等级操作 -->
	<select id="UW_findAllUwLevel" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.LEVEL_CODE, A.LEVEL_DESC FROM dev_uw.T_UW_LEVEL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LEVEL_CODE ]]>
	</select>


<!-- 根据username查询orgcode -->
	<select id="find_UserOrgCode" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM DEV_PAS.T_UDMP_USER A WHERE 1 = 1 AND A.USER_DISABLE = 'N']]>
		<if test="user_name != null and user_name !=''"><![CDATA[AND A.USER_NAME = #{user_name}  ]]></if>
	</select>


	<!-- 查询所有销售渠道操作 -->
	<select id="UW_findAllChannelType" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT A.SALES_CHANNEL_NAME TYPE_NAME, A.SALES_CHANNEL_CODE INDIVIDUAL_GROUP FROM dev_uw.t_sales_channel A WHERE 1=1 ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" sales_channel_code != null and sales_channel_code != '' "><![CDATA[ AND A.SALES_CHANNEL_CODE in (${sales_channel_code}) ]]></if>
		<![CDATA[ ORDER BY INDIVIDUAL_GROUP ]]>
	</select>

	<!-- 查询所有核保决定操作 -->
	<select id="UW_findAllPolicyDecision" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.DECISION_DESC, A.DECISION_CODE FROM dev_uw.T_POLICY_DECISION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DECISION_CODE ]]>
	</select>

	<!-- 查询所有保全项目操作 -->
	<select id="UW_findAllCSService" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT A.SERVICE_CODE,A.SERVICE_NAME FROM dev_uw.T_SERVICE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.SERVICE_CODE ]]>
	</select>

	<!-- 查询所有核保状态操作 -->
	<select id="UW_findAllUWStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT A.UW_STATUS,A.STATUS_DETAIL_NAME,A.STATUS_DETAIL_CODE FROM dev_uw.T_UW_STATUS_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.STATUS_DETAIL_CODE ]]>
	</select>

	<!-- 查询续保核保清单数据 -->
	<select id="UW_findAllInfomaction" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select *
  from (SELECT ROWNUM AS RN,
               POLICY_CODE,
               UW_USER_NAME,
               UW_USER_REAL_NAME,
               UW_DECISION,
               UW_SUBMIT_TIME,
               UW_FINISH_TIME,
               UW_DOCUMENT,
               UW_STATUS,
               IS_OVERDUE,
               BUSI_ITEM_ID,
               BUSI_DECISION_CODE,
               ITEM_ID,
               DECISION_CODE,
               INPUT_DATE,
               AMOUNT,
               UNIT,
               FGS_ORGAN_CODE,
               FGS_ORGAN_NAME,
               ZHGS_ORGAN_CODE,
               ZHGS_ORGAN_NAME,
               SALES_ORGAN_CODE,
               SALES_ORGAN_NAME,
               AGENT_CODE,
               AGENT_NAME,
               CASE_NO
          FROM (
          SELECT distinct Y.UW_ID,Y.POLICY_CODE,
       (SELECT U.USER_NAME
          FROM DEV_PAS.T_UDMP_USER U
         WHERE U.USER_ID = M.UW_USER_ID) UW_USER_NAME,
       (SELECT U.REAL_NAME
          FROM DEV_PAS.T_UDMP_USER U
         WHERE U.USER_ID = M.UW_USER_ID) UW_USER_REAL_NAME,
       (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION
         WHERE Y.POLICY_DECISION = DECISION_CODE) AS UW_DECISION,
       TO_CHAR(M.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') UW_SUBMIT_TIME,
       TO_CHAR(M.UW_FINISH_TIME, 'YYYY-MM-DD HH24:MI:SS') UW_FINISH_TIME,
       (SELECT TO_CHAR(WM_CONCAT(DOCUMENT_NAME)) FROM DEV_NB.T_DOCUMENT WHERE BUSS_ID = M.UW_ID AND BUSS_SOURCE_CODE = '002') AS UW_DOCUMENT,
       (SELECT STATUS_DETAIL_NAME
          FROM DEV_UW.T_UW_STATUS_DETAIL
         WHERE M.UW_STATUS_DETAIL = STATUS_DETAIL_CODE) UW_STATUS, M.UW_STATUS_DETAIL,
       (SELECT (CASE
                 WHEN STATUS_DETAIL_CODE = '0302' THEN
                  '是'
                 ELSE
                  '否'
               END)
          FROM DEV_UW.T_UW_STATUS_DETAIL
         WHERE M.UW_STATUS_DETAIL = STATUS_DETAIL_CODE) IS_OVERDUE,
       (SELECT PRODUCT_NAME_STD
          FROM DEV_PDS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) BUSI_ITEM_ID,
       (SELECT DECISION_DESC
          FROM DEV_UW.T_PRODUCT_DECISION
         WHERE B.DECISION_CODE = DECISION_CODE) AS BUSI_DECISION_CODE,
       (SELECT PRODUCT_NAME
          FROM DEV_PDS.T_PRODUCT_LIFE
         WHERE PRODUCT_ID = (SELECT PRODUCT_ID
                               FROM DEV_UW.T_CONTRACT_PRODUCT
                              WHERE UW_ID = P.UW_ID
                                AND ITEM_ID = P.ITEM_ID)) ITEM_ID,
       (SELECT DECISION_DESC FROM DEV_UW.T_PRODUCT_DECISION
         WHERE P.DECISION_CODE = DECISION_CODE) AS DECISION_CODE,
         MAST.INPUT_DATE,
         CASE
            WHEN M.CASE_PA = 1 THEN
             (SELECT TO_CHAR(WM_CONCAT(UOI.BUSS_CODE))
                FROM DEV_UW.T_UW_OTHER_INFO UOI
               WHERE UOI.UW_ID = M.UW_ID
                 AND UOI.POLICY_CODE = Y.POLICY_CODE)
            ELSE
             M.BIZ_CODE
          END CASE_NO,
          P.AMOUNT,
          P.UNIT,
          SUBSTR(Y.ORGAN_CODE, 1, 4) AS FGS_ORGAN_CODE,
          (SELECT ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG
            WHERE ORGAN_CODE =
                  SUBSTR(Y.ORGAN_CODE, 1, 4)) AS FGS_ORGAN_NAME,
          SUBSTR(Y.ORGAN_CODE, 1, 6) AS ZHGS_ORGAN_CODE,
          (SELECT ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG
            WHERE ORGAN_CODE =
                  SUBSTR(Y.ORGAN_CODE, 1, 6)) AS ZHGS_ORGAN_NAME,
          Y.ORGAN_CODE AS SALES_ORGAN_CODE,
          (SELECT ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG
            WHERE ORGAN_CODE = Y.ORGAN_CODE) AS SALES_ORGAN_NAME,
          TCA.AGENT_CODE,
          TCA.AGENT_NAME
  FROM DEV_UW.T_UW_MASTER    M,
       DEV_UW.T_UW_POLICY    Y,
       DEV_UW.T_UW_BUSI_PROD B,
       DEV_UW.T_UW_PRODUCT   P,
       DEV_UW.T_CONTRACT_MASTER MAST,
       DEV_UW.T_CONTRACT_AGENT  TCA
 WHERE M.UW_ID = Y.UW_ID
   AND Y.UW_ID = B.UW_ID
   AND Y.UW_ID = P.UW_ID
   AND B.BUSI_ITEM_ID = P.BUSI_ITEM_ID
   AND MAST.UW_ID = Y.UW_ID
   AND TCA.UW_ID = M.UW_ID
   AND M.UW_SOURCE_TYPE = '3'
		
		]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND Y.ORGAN_CODE like #{organ_code}||'%' ]]></if>
		<if test=" uw_user_name != null and uw_user_name !=''"><![CDATA[AND exists(
     		SELECT 1 FROM T_UDMP_USER WHERE M.Uw_User_Id = user_id and USER_NAME like (#{uw_user_name})||'%') ]]></if>
     	<if test=" uw_status != null and uw_status !=''"><![CDATA[AND M.UW_STATUS_DETAIL in (${uw_status})]]></if>
       <if test=" submission_time_start != null and submission_time_start !=''"><![CDATA[ AND MAST.ISSUE_DATE>=  to_date(#{submission_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')    ]]></if> 
	   <if test=" submission_time_end != null and submission_time_end !=''"><![CDATA[ AND MAST.ISSUE_DATE<= to_date(#{submission_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')]]></if>
	   <if test=" uw_finish_time_start != null and uw_finish_time_start !=''"><![CDATA[ AND M.UW_FINISH_TIME >= to_date(#{uw_finish_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')]]></if>
   	 <if test=" uw_finish_time_end != null and uw_finish_time_end !=''"><![CDATA[ AND M.UW_FINISH_TIME <= to_date(#{uw_finish_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')]]></if>
		<if test=" uw_submit_time_start != null and uw_submit_time_start !=''"><![CDATA[ and exists 
   		(select 1 from DEV_UW.T_CONTRACT_MASTER where uw_id = Y.Uw_Id and  SUBMISSION_DATE>=  to_date(#{uw_submit_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')   ) ]]></if> 
	 <if test=" uw_submit_time_end != null and uw_submit_time_end !=''"><![CDATA[ AND exists(
	    SELECT 1 FROM DEV_UW.T_CONTRACT_MASTER WHERE UW_ID = Y.UW_ID AND SUBMISSION_DATE <= to_date(#{uw_submit_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')
	   ) ]]></if> 
     
      <if test=" entry_time_start != null and entry_time_start !=''"><![CDATA[ and exists 
   		(select 1 from DEV_UW.T_CONTRACT_MASTER where uw_id = Y.Uw_Id and  INPUT_DATE>= to_date(#{entry_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')  ) ]]></if> 
	 <if test=" entry_time_end != null and entry_time_end !=''"><![CDATA[ AND exists(
	    SELECT 1 FROM DEV_UW.T_CONTRACT_MASTER WHERE UW_ID = Y.UW_ID AND INPUT_DATE <= to_date(#{entry_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')
	   ) ]]></if> 	
	
	
		<![CDATA[ORDER BY UW_STATUS_DETAIL ,UW_FINISH_TIME DESC,UW_SUBMIT_TIME DESC,INPUT_DATE DESC )where rownum <= #{LESS_NUM} ) a where a.rn > #{GREATER_NUM}]]>
	</select>

	<!-- 续保核保清单Excel导出 -->
	<select id="UW_findAllInfomaction_Outexcel" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT ROWNUM AS RN,
               POLICY_CODE,
               UW_USER_NAME,
               UW_USER_REAL_NAME,
               UW_DECISION,
               UW_SUBMIT_TIME,
               UW_FINISH_TIME,
               UW_DOCUMENT,
               UW_STATUS,
               IS_OVERDUE,
               BUSI_ITEM_ID,
               BUSI_DECISION_CODE,
               ITEM_ID,
               DECISION_CODE,
               INPUT_DATE,
               AMOUNT,
               UNIT,
               FGS_ORGAN_CODE,
               FGS_ORGAN_NAME,
               ZHGS_ORGAN_CODE,
               ZHGS_ORGAN_NAME,
               SALES_ORGAN_CODE,
               SALES_ORGAN_NAME,
               AGENT_CODE,
               AGENT_NAME,
               CASE_NO
          FROM (
          SELECT distinct Y.UW_ID,Y.POLICY_CODE,
       (SELECT U.USER_NAME
          FROM DEV_PAS.T_UDMP_USER U
         WHERE U.USER_ID = M.UW_USER_ID) UW_USER_NAME,
       (SELECT U.REAL_NAME
          FROM DEV_PAS.T_UDMP_USER U
         WHERE U.USER_ID = M.UW_USER_ID) UW_USER_REAL_NAME,
       (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION
         WHERE Y.POLICY_DECISION = DECISION_CODE) AS UW_DECISION,
       TO_CHAR(M.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') UW_SUBMIT_TIME,
       TO_CHAR(M.UW_FINISH_TIME, 'YYYY-MM-DD HH24:MI:SS') UW_FINISH_TIME,
       (SELECT TO_CHAR(WM_CONCAT(DOCUMENT_NAME)) FROM DEV_NB.T_DOCUMENT WHERE BUSS_ID = M.UW_ID AND BUSS_SOURCE_CODE = '002') AS UW_DOCUMENT,
       (SELECT STATUS_DETAIL_NAME
          FROM DEV_UW.T_UW_STATUS_DETAIL
         WHERE M.UW_STATUS_DETAIL = STATUS_DETAIL_CODE) UW_STATUS, M.UW_STATUS_DETAIL,
       (SELECT (CASE
                 WHEN STATUS_DETAIL_CODE = '0302' THEN
                  '是'
                 ELSE
                  '否'
               END)
          FROM DEV_UW.T_UW_STATUS_DETAIL
         WHERE M.UW_STATUS_DETAIL = STATUS_DETAIL_CODE) IS_OVERDUE,
       (SELECT PRODUCT_NAME_STD
          FROM DEV_PDS.T_BUSINESS_PRODUCT
         WHERE PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) BUSI_ITEM_ID,
       (SELECT DECISION_DESC
          FROM DEV_UW.T_PRODUCT_DECISION
         WHERE B.DECISION_CODE = DECISION_CODE) AS BUSI_DECISION_CODE,
       (SELECT PRODUCT_NAME
          FROM DEV_PDS.T_PRODUCT_LIFE
         WHERE PRODUCT_ID = (SELECT PRODUCT_ID
                               FROM DEV_UW.T_CONTRACT_PRODUCT
                              WHERE UW_ID = P.UW_ID
                                AND ITEM_ID = P.ITEM_ID)) ITEM_ID,
       (SELECT DECISION_DESC FROM DEV_UW.T_PRODUCT_DECISION
          WHERE P.DECISION_CODE = DECISION_CODE) AS DECISION_CODE,
          MAST.INPUT_DATE,
          CASE
            WHEN M.CASE_PA = 1 THEN
             (SELECT TO_CHAR(WM_CONCAT(UOI.BUSS_CODE))
                FROM DEV_UW.T_UW_OTHER_INFO UOI
               WHERE UOI.UW_ID = M.UW_ID
                 AND UOI.POLICY_CODE = Y.POLICY_CODE)
            ELSE
             M.BIZ_CODE
          END CASE_NO,
          P.AMOUNT,
          P.UNIT,
          SUBSTR(Y.ORGAN_CODE, 1, 4) AS FGS_ORGAN_CODE,
          (SELECT ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG
            WHERE ORGAN_CODE =
                  SUBSTR(Y.ORGAN_CODE, 1, 4)) AS FGS_ORGAN_NAME,
          SUBSTR(Y.ORGAN_CODE, 1, 6) AS ZHGS_ORGAN_CODE,
          (SELECT ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG
            WHERE ORGAN_CODE =
                  SUBSTR(Y.ORGAN_CODE, 1, 6)) AS ZHGS_ORGAN_NAME,
          Y.ORGAN_CODE AS SALES_ORGAN_CODE,
          (SELECT ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG
            WHERE ORGAN_CODE = Y.ORGAN_CODE) AS SALES_ORGAN_NAME,
          TCA.AGENT_CODE,
          TCA.AGENT_NAME
  FROM DEV_UW.T_UW_MASTER    M,
       DEV_UW.T_UW_POLICY    Y,
       DEV_UW.T_UW_BUSI_PROD B,
       DEV_UW.T_UW_PRODUCT   P,
       DEV_UW.T_CONTRACT_MASTER MAST,
       DEV_UW.T_CONTRACT_AGENT  TCA
 WHERE M.UW_ID = Y.UW_ID
   AND Y.UW_ID = B.UW_ID
   AND Y.UW_ID = P.UW_ID
   AND B.BUSI_ITEM_ID = P.BUSI_ITEM_ID
   AND MAST.UW_ID = Y.UW_ID
   AND TCA.UW_ID = M.UW_ID
   AND M.UW_SOURCE_TYPE = '3'
		
		]]>
	<if test=" organ_code != null and organ_code !=''"><![CDATA[AND Y.ORGAN_CODE like #{organ_code}||'%' ]]></if>
		<if test=" uw_user_name != null and uw_user_name !=''"><![CDATA[AND exists(
     		SELECT 1 FROM T_UDMP_USER WHERE M.Uw_User_Id = user_id and USER_NAME like (#{uw_user_name})||'%') ]]></if>
     	<if test=" uw_status != null and uw_status !=''"><![CDATA[AND M.UW_STATUS_DETAIL in (${uw_status})]]></if>
       <if test=" submission_time_start != null and submission_time_start !=''"><![CDATA[ AND MAST.ISSUE_DATE>=  to_date(#{submission_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')    ]]></if> 
	   <if test=" submission_time_end != null and submission_time_end !=''"><![CDATA[ AND MAST.ISSUE_DATE<= to_date(#{submission_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')]]></if>
	   <if test=" uw_finish_time_start != null and uw_finish_time_start !=''"><![CDATA[ AND M.UW_FINISH_TIME >= to_date(#{uw_finish_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')]]></if>
   	 <if test=" uw_finish_time_end != null and uw_finish_time_end !=''"><![CDATA[ AND M.UW_FINISH_TIME <= to_date(#{uw_finish_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')]]></if>
		<if test=" uw_submit_time_start != null and uw_submit_time_start !=''"><![CDATA[ and exists 
   		(select 1 from DEV_UW.T_CONTRACT_MASTER where uw_id = Y.Uw_Id and  SUBMISSION_DATE>=  to_date(#{uw_submit_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')   ) ]]></if> 
	 <if test=" uw_submit_time_end != null and uw_submit_time_end !=''"><![CDATA[ AND exists(
	    SELECT 1 FROM DEV_UW.T_CONTRACT_MASTER WHERE UW_ID = Y.UW_ID AND SUBMISSION_DATE <= to_date(#{uw_submit_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')
	   ) ]]></if> 
     
      <if test=" entry_time_start != null and entry_time_start !=''"><![CDATA[ and exists 
   		(select 1 from DEV_UW.T_CONTRACT_MASTER where uw_id = Y.Uw_Id and  INPUT_DATE>= to_date(#{entry_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')  ) ]]></if> 
	 <if test=" entry_time_end != null and entry_time_end !=''"><![CDATA[ AND exists(
	    SELECT 1 FROM DEV_UW.T_CONTRACT_MASTER WHERE UW_ID = Y.UW_ID AND INPUT_DATE <= to_date(#{entry_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')
	   ) ]]></if> 
	   
	   
	   
	<![CDATA[ ORDER BY UW_STATUS_DETAIL ,UW_FINISH_TIME DESC,UW_SUBMIT_TIME DESC,INPUT_DATE DESC ) ]]>
	</select>
	<!-- 查询续保核保清单数据的条数 -->
	<select id="UW_findAllInfomaction_count" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[  
			select count(1) Countrecord
          from ( SELECT distinct Y.UW_ID,Y.POLICY_CODE,
       Y.POLICY_DECISION AS UW_DECISION,
       TO_CHAR(M.INSERT_TIME, 'YYYY-MM-DD HH24:MI:SS') UW_SUBMIT_TIME,
       TO_CHAR(M.UW_FINISH_TIME, 'YYYY-MM-DD HH24:MI:SS') UW_FINISH_TIME,
       P.DECISION_CODE AS DECISION_CODE,
       MAST.INPUT_DATE,
       CASE
         WHEN M.CASE_PA = 1 THEN
          (SELECT TO_CHAR(WM_CONCAT(UOI.BUSS_CODE))
             FROM DEV_UW.T_UW_OTHER_INFO UOI
            WHERE UOI.UW_ID = M.UW_ID
              AND UOI.POLICY_CODE = Y.POLICY_CODE)
         ELSE
          M.BIZ_CODE
       END CASE_NO
	  FROM DEV_UW.T_UW_MASTER    M,
	       DEV_UW.T_UW_POLICY    Y,
	       DEV_UW.T_UW_BUSI_PROD B,
	       DEV_UW.T_UW_PRODUCT   P,
	       DEV_UW.T_CONTRACT_MASTER MAST,
	       DEV_UW.T_CONTRACT_AGENT  TCA
	 WHERE M.UW_ID = Y.UW_ID
	   AND Y.UW_ID = B.UW_ID
	   AND Y.UW_ID = P.UW_ID
	   AND B.BUSI_ITEM_ID = P.BUSI_ITEM_ID
	   AND MAST.UW_ID = Y.UW_ID
	   AND TCA.UW_ID = M.UW_ID
	   AND M.UW_SOURCE_TYPE = '3'
		]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND Y.ORGAN_CODE like #{organ_code}||'%' ]]></if>
		<if test=" uw_user_name != null and uw_user_name !=''"><![CDATA[AND exists(
     		SELECT 1 FROM T_UDMP_USER WHERE M.Uw_User_Id = user_id and USER_NAME like (#{uw_user_name})||'%') ]]></if>
     	<if test=" uw_status != null and uw_status !=''"><![CDATA[AND M.UW_STATUS_DETAIL in (${uw_status})]]></if>
       <if test=" submission_time_start != null and submission_time_start !=''"><![CDATA[ AND MAST.ISSUE_DATE>=  to_date(#{submission_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')    ]]></if> 
	   <if test=" submission_time_end != null and submission_time_end !=''"><![CDATA[ AND MAST.ISSUE_DATE<= to_date(#{submission_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')]]></if>
	   <if test=" uw_finish_time_start != null and uw_finish_time_start !=''"><![CDATA[ AND M.UW_FINISH_TIME >= to_date(#{uw_finish_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')]]></if>
   	 <if test=" uw_finish_time_end != null and uw_finish_time_end !=''"><![CDATA[ AND M.UW_FINISH_TIME <= to_date(#{uw_finish_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')]]></if>
		<if test=" uw_submit_time_start != null and uw_submit_time_start !=''"><![CDATA[ and exists 
   		(select 1 from DEV_UW.T_CONTRACT_MASTER where uw_id = Y.Uw_Id and  SUBMISSION_DATE>=  to_date(#{uw_submit_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')   ) ]]></if> 
	 <if test=" uw_submit_time_end != null and uw_submit_time_end !=''"><![CDATA[ AND exists(
	    SELECT 1 FROM DEV_UW.T_CONTRACT_MASTER WHERE UW_ID = Y.UW_ID AND SUBMISSION_DATE <= to_date(#{uw_submit_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')
	   ) ]]></if> 
     
      <if test=" entry_time_start != null and entry_time_start !=''"><![CDATA[ and exists 
   		(select 1 from DEV_UW.T_CONTRACT_MASTER where uw_id = Y.Uw_Id and  INPUT_DATE>= to_date(#{entry_time_start}||' 00:00:00','yyyy-mm-dd HH24:MI:SS')  ) ]]></if> 
	 <if test=" entry_time_end != null and entry_time_end !=''"><![CDATA[ AND exists(
	    SELECT 1 FROM DEV_UW.T_CONTRACT_MASTER WHERE UW_ID = Y.UW_ID AND INPUT_DATE <= to_date(#{entry_time_end}||' 23:59:59','yyyy-mm-dd HH24:MI:SS')
	   ) ]]></if> 
		<![CDATA[ ) ]]>
	</select>
	<!-- 查询阳性清单数据 -->
	<select id="UW_findAllInfomactionSun" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT * FROM (
			SELECT ROWNUM as rn,a.* FROM (
			       SELECT distinct TO_CHAR(T_CONTRACT_MASTER.APPLY_CODE) APPLY_CODE,
                 (SELECT T_SALES_CHANNEL.SALES_CHANNEL_NAME FROM DEV_UW.T_SALES_CHANNEL
                  WHERE T_SALES_CHANNEL.SALES_CHANNEL_CODE = T_CONTRACT_MASTER.CHANNEL_TYPE) CHANNEL_TYPE,
                 TO_CHAR((SELECT POLICY_CODE
          FROM DEV_NB.T_NB_CONTRACT_MASTER
         WHERE APPLY_CODE = T_CONTRACT_MASTER.APPLY_CODE)) POLICY_CODE,
                 T_UDMP_ORG.ORGAN_CODE ORGAN_CODE,
                 T_UDMP_ORG.ORGAN_NAME ORGAN_NAME,
                 TO_CHAR(T_CONTRACT_AGENT.AGENT_CODE) AGENT_CODE,
                 (SELECT AGENT_NAME FROM DEV_PAS.T_AGENT WHERE T_AGENT.AGENT_CODE = T_CONTRACT_AGENT.AGENT_CODE) AGENT_NAME,
                 T_UDMP_USER.USER_NAME UW_USER_CODE,
                 T_UDMP_USER.REAL_NAME USER_REAL_NAME,
                 TO_CHAR(CASE WHEN A.SEND_TIME IS NOT NULL THEN
                             A.SEND_TIME
                            ELSE
                             A.CREATE_TIME
                          END,
                          'YYYY-MM-DD HH24:MI:SS') INSERT_TIME,
                 (SELECT REASON_DESC
                    FROM DEV_UW.T_EXAMINE_REASON
                   WHERE T_SURVIVAL_INVESTIGATION.EXAMINE_REASON =
                         T_EXAMINE_REASON.REASON_CODE) EXAMINE_REASON,
                 (SELECT ROLE_TYPE_NAME
                         FROM DEV_UW.T_UW_ROLE_TYPE
                        WHERE T_SURVIVAL_INVESTIGATION.ROLE_TYPE =
                              T_UW_ROLE_TYPE.ROLE_TYPE_CODE) ROLE_TYPE,
                 T_SURVIVAL_INVESTIGATION.CUSTOMER_NAME CUSTOMER_NAME,
                 
                 T_SURVIVAL_INVESTIGATION.DOCUMENT_NO DOCUMENT_NO,
                 
                 TO_CHAR(DEV_UW.T_SURVEY_CONCLUSION.ORG_OPR) SURVEY_CODE,
                 TO_CHAR(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE,
                         'YYYY-MM-DD') EXAMINE_DESTRUCTION_TIME_END,
                 T_SURVEY_CONCLUSION.ORG_OPR SURVEY_NAME,
          		 DEV_UW.T_CONTRACT_MASTER.SUBMISSION_DATE,
                 A.CREATE_TIME
            FROM DEV_UW.T_CONTRACT_MASTER,
                 DEV_UW.T_SURVEY_APPLY,
                 DEV_UW.T_SURVIVAL_INVESTIGATION,
                 DEV_PAS.T_UDMP_USER,
                 DEV_UW.T_CONTRACT_AGENT,
                 DEV_UW.T_UDMP_ORG,
                 DEV_UW.T_SURVEY_CONCLUSION,
                 DEV_NB.T_DOCUMENT A
             WHERE 1 = 1
             	   AND T_CONTRACT_MASTER.UW_ID = T_SURVIVAL_INVESTIGATION.UW_ID
                   AND T_CONTRACT_MASTER.APPLY_CODE = T_SURVIVAL_INVESTIGATION.APPLY_CODE
                   AND T_SURVEY_APPLY.SURVEY_DOC_ID = T_SURVIVAL_INVESTIGATION.DOC_LIST_ID
                   AND T_SURVIVAL_INVESTIGATION.INSERT_BY = T_UDMP_USER.USER_ID
                   AND T_CONTRACT_AGENT.UW_ID = T_CONTRACT_MASTER.UW_ID
                   AND T_CONTRACT_AGENT.APPLY_CODE = T_CONTRACT_MASTER.APPLY_CODE
                   AND T_CONTRACT_MASTER.ORGAN_CODE = T_UDMP_ORG.ORGAN_CODE
                   AND T_SURVEY_APPLY.APPLY_ID = T_SURVEY_CONCLUSION.APPLY_ID
                   AND T_SURVIVAL_INVESTIGATION.DOC_LIST_ID = A.DOC_LIST_ID
                   AND T_SURVEY_CONCLUSION.POSITIVE_FLAG = '1'
							]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND T_UDMP_ORG.ORGAN_CODE like #{organ_code}||'%' ]]></if>
<!-- 		<if test=" organ_name != null and organ_name !=''"><![CDATA[AND T_UDMP_ORG.ORGAN_NAME like #{organ_name}||'%' ]]></if> -->
		<!-- <if test=" cs_accept_time_start != null and cs_accept_time_start !=''"><![CDATA[ AND T_CONTRACT_MASTER.SUBMISSION_DATE >= to_date(#{cs_accept_time_start},'YYYY-MM-DD')]]></if>
		<if test=" cs_accept_time_end != null and cs_accept_time_end !=''"><![CDATA[ AND T_CONTRACT_MASTER.SUBMISSION_DATE <= to_date(#{cs_accept_time_end},'YYYY-MM-DD')]]></if> -->
		<if test=" examine_time_start != null and examine_time_start !=''"><![CDATA[ AND A.SEND_TIME >= to_date(#{examine_time_start},'YYYY-MM-DD') ]]></if>
		<if test=" examine_time_end != null and examine_time_end !=''"><![CDATA[ AND A.SEND_TIME <= to_date(#{examine_time_end},'YYYY-MM-DD') ]]></if>
		<if test=" uw_user_code != null and uw_user_code !=''"><![CDATA[AND  t_udmp_user.user_name like #{uw_user_code} || '%']]></if>
		<if test="examine_destruction_time_start != null and examine_destruction_time_start != '' ">
			<![CDATA[ AND TRUNC(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE)>= TO_DATE(#{examine_destruction_time_start},'YYYY-MM-DD')]]>
		</if>
		<if test="examine_destruction_time_end != null and examine_destruction_time_end != '' ">
			<![CDATA[ AND TRUNC(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE)<= TO_DATE(#{examine_destruction_time_end},'YYYY-MM-DD')]]>
		</if>
		<![CDATA[ 
			order by organ_code, insert_time ,create_time ,submission_date
		) a where rownum <= #{LESS_NUM} ) C where C.rn > #{GREATER_NUM}  ]]>

	</select>

	<!-- 阳性清单Excel导出 -->
	<select id="UW_findAllInfomactionSun_Outexcel" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT distinct TO_CHAR(T_CONTRACT_MASTER.APPLY_CODE) APPLY_CODE,
                 (SELECT T_SALES_CHANNEL.SALES_CHANNEL_NAME FROM DEV_UW.T_SALES_CHANNEL
                  WHERE T_SALES_CHANNEL.SALES_CHANNEL_CODE = T_CONTRACT_MASTER.CHANNEL_TYPE) CHANNEL_TYPE,
                 TO_CHAR((SELECT POLICY_CODE
          FROM DEV_NB.T_NB_CONTRACT_MASTER
         WHERE APPLY_CODE = T_CONTRACT_MASTER.APPLY_CODE)) POLICY_CODE,
                 T_UDMP_ORG.ORGAN_CODE ORGAN_CODE,
                 T_UDMP_ORG.ORGAN_NAME ORGAN_NAME,
                 TO_CHAR(T_CONTRACT_AGENT.AGENT_CODE) AGENT_CODE,
                 (SELECT AGENT_NAME FROM DEV_PAS.T_AGENT WHERE T_AGENT.AGENT_CODE = T_CONTRACT_AGENT.AGENT_CODE) AGENT_NAME,
                 T_UDMP_USER.USER_NAME UW_USER_CODE,
                 T_UDMP_USER.REAL_NAME USER_REAL_NAME,
                 TO_CHAR(CASE WHEN A.SEND_TIME IS NOT NULL THEN
                             A.SEND_TIME
                            ELSE
                             A.CREATE_TIME
                          END,
                          'YYYY-MM-DD HH24:MI:SS') INSERT_TIME,
                 (SELECT REASON_DESC
                    FROM DEV_UW.T_EXAMINE_REASON
                   WHERE T_SURVIVAL_INVESTIGATION.EXAMINE_REASON =
                         T_EXAMINE_REASON.REASON_CODE) EXAMINE_REASON,
                 (SELECT ROLE_TYPE_NAME
                       FROM DEV_UW.T_UW_ROLE_TYPE
                      WHERE T_SURVIVAL_INVESTIGATION.ROLE_TYPE =
                            T_UW_ROLE_TYPE.ROLE_TYPE_CODE) ROLE_TYPE,
                 T_SURVIVAL_INVESTIGATION.CUSTOMER_NAME CUSTOMER_NAME,
                 T_SURVIVAL_INVESTIGATION.DOCUMENT_NO DOCUMENT_NO,
                 TO_CHAR(DEV_UW.T_SURVEY_CONCLUSION.ORG_OPR) SURVEY_CODE,
                 TO_CHAR(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE,
                         'YYYY-MM-DD') EXAMINE_DESTRUCTION_TIME_END,
                 T_SURVEY_CONCLUSION.ORG_OPR SURVEY_NAME,
          		 DEV_UW.T_CONTRACT_MASTER.SUBMISSION_DATE,
                 A.CREATE_TIME
            FROM DEV_UW.T_CONTRACT_MASTER,
                 DEV_UW.T_SURVEY_APPLY,
                 DEV_UW.T_SURVIVAL_INVESTIGATION,
                 DEV_PAS.T_UDMP_USER,
                 DEV_UW.T_CONTRACT_AGENT,
                 DEV_UW.T_UDMP_ORG,
                 DEV_UW.T_SURVEY_CONCLUSION,
                 DEV_NB.T_DOCUMENT A 
           WHERE 1 = 1
           	     AND T_CONTRACT_MASTER.UW_ID = T_SURVIVAL_INVESTIGATION.UW_ID
                 AND T_CONTRACT_MASTER.APPLY_CODE = T_SURVIVAL_INVESTIGATION.APPLY_CODE
                 AND T_SURVEY_APPLY.SURVEY_DOC_ID = T_SURVIVAL_INVESTIGATION.DOC_LIST_ID
                 AND T_SURVIVAL_INVESTIGATION.INSERT_BY = T_UDMP_USER.USER_ID
                 AND T_CONTRACT_AGENT.UW_ID = T_CONTRACT_MASTER.UW_ID
                 AND T_CONTRACT_AGENT.APPLY_CODE = T_CONTRACT_MASTER.APPLY_CODE
                 AND T_CONTRACT_MASTER.ORGAN_CODE = T_UDMP_ORG.ORGAN_CODE
                 AND T_SURVEY_APPLY.APPLY_ID = T_SURVEY_CONCLUSION.APPLY_ID
                 AND T_SURVIVAL_INVESTIGATION.DOC_LIST_ID = A.DOC_LIST_ID
                 AND T_SURVEY_CONCLUSION.POSITIVE_FLAG = '1'
							]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND T_UDMP_ORG.ORGAN_CODE like #{organ_code}||'%' ]]></if>
<!-- 		<if test=" organ_name != null and organ_name !=''"><![CDATA[AND T_UDMP_ORG.ORGAN_NAME like #{organ_name}||'%' ]]></if> -->
		<!-- <if test=" cs_accept_time_start != null and cs_accept_time_start !=''"><![CDATA[ AND T_CONTRACT_MASTER.SUBMISSION_DATE >= to_date(#{cs_accept_time_start},'YYYY-MM-DD')]]></if>
		<if test=" cs_accept_time_end != null and cs_accept_time_end !=''"><![CDATA[ AND T_CONTRACT_MASTER.SUBMISSION_DATE <= to_date(#{cs_accept_time_end},'YYYY-MM-DD')]]></if> -->
		<if test=" examine_time_start != null and examine_time_start !=''"><![CDATA[ AND A.SEND_TIME >= to_date(#{examine_time_start},'YYYY-MM-DD') ]]></if>
		<if test=" examine_time_end != null and examine_time_end !=''"><![CDATA[ AND A.SEND_TIME <= to_date(#{examine_time_end},'YYYY-MM-DD') ]]></if>
		<if test=" uw_user_code != null and uw_user_code !=''"><![CDATA[AND  t_udmp_user.user_name like #{uw_user_code} || '%']]></if>
		<if test="examine_destruction_time_start != null and examine_destruction_time_start != '' ">
			<![CDATA[ AND TRUNC(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE)>= TO_DATE(#{examine_destruction_time_start},'YYYY-MM-DD')]]>
		</if>
		<if test="examine_destruction_time_end != null and examine_destruction_time_end != '' ">
			<![CDATA[ AND TRUNC(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE)<= TO_DATE(#{examine_destruction_time_end},'YYYY-MM-DD')]]>
		</if>
		order by organ_code, insert_time,create_time,submission_date


	</select>


	<!-- 查询阳性清单数据个数 -->
	<select id="UW_findAllInfomactionSun_count" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[  
			  SELECT count(1) Countrecord FROM (
			   SELECT distinct TO_CHAR(T_CONTRACT_MASTER.APPLY_CODE) APPLY_CODE,
                 (SELECT T_SALES_CHANNEL.SALES_CHANNEL_NAME FROM DEV_UW.T_SALES_CHANNEL
                  WHERE T_SALES_CHANNEL.SALES_CHANNEL_CODE = T_CONTRACT_MASTER.CHANNEL_TYPE) CHANNEL_TYPE,
                 T_UDMP_ORG.ORGAN_CODE ORGAN_CODE,
                 T_UDMP_ORG.ORGAN_NAME ORGAN_NAME,
                 TO_CHAR(T_CONTRACT_AGENT.AGENT_CODE) AGENT_CODE,
                 (SELECT AGENT_NAME FROM DEV_PAS.T_AGENT WHERE T_AGENT.AGENT_CODE = T_CONTRACT_AGENT.AGENT_CODE) AGENT_NAME,
                 T_UDMP_USER.USER_NAME UW_USER_CODE,
                 T_UDMP_USER.REAL_NAME USER_REAL_NAME,
                 (SELECT REASON_DESC
                    FROM DEV_UW.T_EXAMINE_REASON
                   WHERE T_SURVIVAL_INVESTIGATION.EXAMINE_REASON =
                         T_EXAMINE_REASON.REASON_CODE) EXAMINE_REASON,
                 (SELECT ROLE_TYPE_NAME
                         FROM DEV_UW.T_UW_ROLE_TYPE
                        WHERE T_SURVIVAL_INVESTIGATION.ROLE_TYPE =
                              T_UW_ROLE_TYPE.ROLE_TYPE_CODE) ROLE_TYPE,
                 T_SURVIVAL_INVESTIGATION.CUSTOMER_NAME CUSTOMER_NAME,
                 
                 T_SURVIVAL_INVESTIGATION.DOCUMENT_NO DOCUMENT_NO,
                 
                 TO_CHAR(DEV_UW.T_SURVEY_CONCLUSION.ORG_OPR) SURVEY_CODE,
                 TO_CHAR(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE,
                         'YYYY-MM-DD') EXAMINE_DESTRUCTION_TIME_END,
                 DEV_UW.T_CONTRACT_MASTER.SUBMISSION_DATE,
                 A.CREATE_TIME
          
            FROM DEV_UW.T_CONTRACT_MASTER,
                 DEV_UW.T_SURVEY_APPLY,
                 DEV_UW.T_SURVIVAL_INVESTIGATION,
                 DEV_PAS.T_UDMP_USER,
                 DEV_UW.T_CONTRACT_AGENT,
                 DEV_UW.T_UDMP_ORG,
                 DEV_UW.T_SURVEY_CONCLUSION,
                 DEV_NB.T_DOCUMENT A 
             WHERE 1 = 1
	               AND T_CONTRACT_MASTER.UW_ID = T_SURVIVAL_INVESTIGATION.UW_ID
                   AND T_CONTRACT_MASTER.APPLY_CODE = T_SURVIVAL_INVESTIGATION.APPLY_CODE
                   AND T_SURVEY_APPLY.SURVEY_DOC_ID = T_SURVIVAL_INVESTIGATION.DOC_LIST_ID
                   AND T_SURVIVAL_INVESTIGATION.INSERT_BY = T_UDMP_USER.USER_ID
                   AND T_CONTRACT_AGENT.UW_ID = T_CONTRACT_MASTER.UW_ID
                   AND T_CONTRACT_AGENT.APPLY_CODE = T_CONTRACT_MASTER.APPLY_CODE
                   AND T_CONTRACT_MASTER.ORGAN_CODE = T_UDMP_ORG.ORGAN_CODE
                   AND T_SURVEY_APPLY.APPLY_ID = T_SURVEY_CONCLUSION.APPLY_ID
                   AND T_SURVIVAL_INVESTIGATION.DOC_LIST_ID = A.DOC_LIST_ID
                   AND T_SURVEY_CONCLUSION.POSITIVE_FLAG = '1' ]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND T_UDMP_ORG.ORGAN_CODE like #{organ_code}||'%' ]]></if>
<!-- 		<if test=" organ_name != null and organ_name !=''"><![CDATA[AND T_UDMP_ORG.ORGAN_NAME like #{organ_name}||'%' ]]></if> -->
		<!-- <if test=" cs_accept_time_start != null and cs_accept_time_start !=''"><![CDATA[ AND T_CONTRACT_MASTER.SUBMISSION_DATE >= to_date(#{cs_accept_time_start},'YYYY-MM-DD HH24:MI:SS')]]></if>
		<if test=" cs_accept_time_end != null and cs_accept_time_end !=''"><![CDATA[ AND T_CONTRACT_MASTER.SUBMISSION_DATE <= to_date(#{cs_accept_time_end},'YYYY-MM-DD HH24:MI:SS')]]></if> -->
		<if test=" examine_time_start != null and examine_time_start !=''"><![CDATA[ AND A.SEND_TIME >= to_date(#{examine_time_start},'YYYY-MM-DD HH24:MI:SS') ]]></if>
		<if test=" examine_time_end != null and examine_time_end !=''"><![CDATA[ AND A.SEND_TIME <= to_date(#{examine_time_end},'YYYY-MM-DD HH24:MI:SS') ]]></if>
		<if test=" uw_user_code != null and uw_user_code !=''"><![CDATA[AND  t_udmp_user.user_name like #{uw_user_code} || '%']]></if>
		<if test="examine_destruction_time_start != null and examine_destruction_time_start != '' ">
			<![CDATA[ AND TRUNC(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE)>= TO_DATE(#{examine_destruction_time_start},'YYYY-MM-DD')]]>
		</if>
		<if test="examine_destruction_time_end != null and examine_destruction_time_end != '' ">
			<![CDATA[ AND TRUNC(DEV_UW.T_SURVEY_CONCLUSION.FINISH_DATE)<= TO_DATE(#{examine_destruction_time_end},'YYYY-MM-DD')]]>
		</if>
		)

	</select>
	<!-- 查询新契核保清单(保单) -->
	<select id="nbuwList_by_policy" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT * FROM ( SELECT ROWNUM RN,ORGAN_CODE,ORGAN_NAME,FGS_ORGAN_CODE,FGS_ORGAN_NAME,ZHGS_ORGAN_CODE,ZHGS_ORGAN_NAME,SALES_ORGAN_CODE,SALES_ORGAN_NAME,CHANNEL_TYPE,APPLY_CODE,POLICY_CODE,UW_LEVEL_CODE,UW_USER_ID,REAL_NAME,UW_USER_LEVEL_CODE,UW_OVER_INDI,
  OVER_INDI_LEVEL,UP_UW_USER_NAME,UP_UW_REAL_NAME,UW_DECISION,ENTRY_TIME,UW_FINISH_TIME,OPERATION_TIME,DOCUMENT_NO1,RREPORT_CODE1,UW_DOCUMENT,UW_STATUS,
  EXCEED,UNDO,PRODUCT_NAME_SYS1, MULTI_MAINRISK_FLAG
   FROM (
SELECT (SELECT ORGAN_NAME
          FROM DEV_UW.T_UDMP_ORG
         WHERE ORGAN_CODE = Y.ORGAN_CODE) ORGAN_NAME,
         Y.ORGAN_CODE,
         SUBSTR(Y.ORGAN_CODE,1,4) AS FGS_ORGAN_CODE,
        (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(Y.ORGAN_CODE,1,4)) AS FGS_ORGAN_NAME,
        SUBSTR(Y.ORGAN_CODE,1,6) AS ZHGS_ORGAN_CODE,
        (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(Y.ORGAN_CODE,1,6)) AS ZHGS_ORGAN_NAME,
        Y.ORGAN_CODE AS SALES_ORGAN_CODE,
        (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = Y.ORGAN_CODE) AS SALES_ORGAN_NAME,
       (SELECT SALES_CHANNEL_NAME FROM DEV_UW.T_SALES_CHANNEL WHERE SALES_CHANNEL_CODE = N.CHANNEL_TYPE) CHANNEL_TYPE,
       Y.APPLY_CODE,
       (SELECT POLICY_CODE
          FROM DEV_NB.T_NB_CONTRACT_MASTER
         WHERE POLICY_ID = Y.POLICY_ID) POLICY_CODE,
       (SELECT LEVEL_DESC
          FROM DEV_UW.T_UW_LEVEL
         WHERE LEVEL_CODE = Y.UW_LEVEL_CODE) UW_LEVEL_CODE,
       (SELECT USER_NAME
          FROM DEV_PAS.T_UDMP_USER
         WHERE USER_ID = M.UW_USER_ID) UW_USER_ID,
       (SELECT REAL_NAME
          FROM DEV_PAS.T_UDMP_USER
         WHERE USER_ID = M.UW_USER_ID) REAL_NAME,
       (SELECT O.PERMISSION_NAME
          FROM DEV_UW.T_UDMP_PERMISSION_INFO O
         WHERE O.PERMISSION_ID =
               (SELECT U3.PERMISSION_ID
                  FROM DEV_UW.T_UDMP_GROUP_USER      U1,
                       DEV_UW.T_UDMP_GROUP_ROLE      U2,
                       DEV_UW.T_UDMP_ROLE_PERMISSION U3
                 WHERE U1.ROLE_GROUP_ID = U2.ROLE_GROUP_ID
                   AND U2.ROLE_ID = U3.ROLE_ID
                   AND U1.USER_ID = M.UW_USER_ID AND ROWNUM = 1)) UW_USER_LEVEL_CODE, 
       (SELECT TYPE_NAME FROM DEV_UW.T_YES_NO WHERE YES_NO = M.UW_OVER_INDI) UW_OVER_INDI,
       '' OVER_INDI_LEVEL,
       CASE
         WHEN M.UW_OVER_INDI = '1' THEN
          (SELECT USER_NAME
             FROM DEV_UW.T_UDMP_USER
            WHERE USER_ID = M.UW_USER_ID)
         ELSE
          ''
       END UP_UW_USER_NAME,
       CASE
         WHEN M.UW_OVER_INDI = '1' THEN
          (SELECT REAL_NAME
             FROM DEV_UW.T_UDMP_USER
            WHERE USER_ID = M.UW_USER_ID)
         ELSE
          ''
       END UP_UW_REAL_NAME,
       (SELECT DECISION_DESC
          FROM DEV_UW.T_POLICY_DECISION
         WHERE DECISION_CODE = Y.POLICY_DECISION) UW_DECISION,
       (SELECT TO_CHAR(INPUT_DATE, 'YYYY-MM-DD')
          FROM DEV_NB.T_NB_CONTRACT_MASTER
         WHERE POLICY_ID = Y.POLICY_ID) ENTRY_TIME,
       TO_CHAR(M.UW_FINISH_TIME, 'YYYY-MM-DD') UW_FINISH_TIME,
       (SELECT TO_CHAR(ISSUE_DATE, 'YYYY-MM-DD')
          FROM DEV_NB.T_NB_CONTRACT_MASTER
         WHERE POLICY_ID = Y.POLICY_ID) OPERATION_TIME,
       (SELECT TO_CHAR(WM_CONCAT(DOCUMENT_NO || '-' ||
                                 (SELECT TYPE_NAME
                                    FROM DEV_UW.T_YES_NO
                                   WHERE POSITIVE_INDI = YES_NO)))
          FROM DEV_UW.T_PENOTICE
         WHERE UW_ID = Y.UW_ID) AS DOCUMENT_NO1,
       
       (SELECT TO_CHAR(WM_CONCAT(DOCUMENT_NO))
          FROM DEV_UW.T_SURVIVAL_INVESTIGATION
         WHERE UW_ID = Y.UW_ID) RREPORT_CODE1,
       (SELECT DOCUMENT_NO
          FROM DEV_UW.T_UW_NOTICE
         WHERE UW_ID = Y.UW_ID
           AND REPLY_INDI = '1'
           AND ROWNUM = 1) UW_DOCUMENT,
       (SELECT STATUS_DETAIL_NAME
          FROM DEV_UW.T_UW_STATUS_DETAIL
         WHERE STATUS_DETAIL_CODE = M.UW_STATUS_DETAIL) UW_STATUS,
       CASE
         WHEN M.UW_STATUS_DETAIL = '0302' THEN
          '是'
         ELSE
          '否'
       END EXCEED,
       CASE
         WHEN (SELECT 1
                 FROM DEV_NB.T_NB_CONTRACT_MASTER
                WHERE POLICY_ID = Y.POLICY_ID
                  AND PROPOSAL_STATUS = 12) = 1 THEN
          '是'
         ELSE
          '否'
       END UNDO,
       
       (SELECT TO_CHAR(WM_CONCAT((SELECT PRODUCT_NAME_SYS
                                   FROM DEV_PDS.T_BUSINESS_PRODUCT
                                  WHERE PRODUCT_CODE_SYS = BUSI_PROD_CODE)))
          FROM DEV_UW.T_UW_BUSI_PROD
         WHERE UW_ID = Y.UW_ID
           AND MASTER_BUSI_ITEM_ID IS NULL) PRODUCT_NAME_SYS1, TO_CHAR(Y.MULTI_MAINRISK_FLAG) AS MULTI_MAINRISK_FLAG

  FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_POLICY Y, DEV_UW.T_UW_AUTO X, DEV_NB.T_NB_CONTRACT_MASTER N
 WHERE M.UW_ID = Y.UW_ID AND M.UW_ID = X.UW_ID AND M.BIZ_CODE = N.APPLY_CODE AND Y.APPLY_CODE = N.APPLY_CODE
   AND M.UW_SOURCE_TYPE = '1'
   AND (X.RULE_RUN_STATUS = 'V02' OR 
                        (X.RULE_RUN_STATUS = 'V01' AND M.REVISION_FLAG = '1'))
   <if test=" channel_type != null and channel_type !=''"><![CDATA[AND N.channel_type in (${channel_type})]]></if>
   <if test=" organ_code != null and organ_code !=''"><![CDATA[ AND Y.organ_code like #{organ_code}||'%' ]]></if>
   <if test=" uw_user_level_code != null and uw_user_level_code !=''"><![CDATA[AND exists(
     	SELECT 1 FROM  DEV_UW.T_UDMP_PERMISSION_INFO O WHERE O.PERMISSION_ID =
               (SELECT U3.PERMISSION_ID
                  FROM DEV_UW.T_UDMP_GROUP_USER      U1,
                       DEV_UW.T_UDMP_GROUP_ROLE      U2,
                       DEV_UW.T_UDMP_ROLE_PERMISSION U3
                 WHERE U1.ROLE_GROUP_ID = U2.ROLE_GROUP_ID
                   AND U2.ROLE_ID = U3.ROLE_ID
                   AND U1.USER_ID = M.UW_USER_ID) and perm_level_workflow in ( ${uw_user_level_code}) )]]></if>
   <if test=" uw_level_code  != null and uw_level_code!= '' "><![CDATA[ AND Y.uw_level_code in (${uw_level_code})]]></if>
   <if test=" uw_decision  != null and uw_decision != '' "><![CDATA[ AND Y.POLICY_DECISION in (${uw_decision})]]></if>                
   <if test=" user_name  != null and user_name != '' "><![CDATA[ AND exists(
     	SELECT 1 FROM DEV_PAS.T_UDMP_USER WHERE USER_ID = M.UW_USER_ID and user_name like #{user_name,jdbcType=VARCHAR}||'%')]]></if> 
   <if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND M.UW_FINISH_TIME >= to_date(#{uw_time_start}, 'yyyy-mm-dd') ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND M.UW_FINISH_TIME <= to_date(#{uw_time_end} , 'yyyy-mm-dd')]]></if>
   <if test=" submission_time_start  != null  and  submission_time_start  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND ISSUE_DATE >= to_date(#{submission_time_start} , 'yyyy-mm-dd') ) ]]></if> 
   <if test=" submission_time_end  != null  and  submission_time_end  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND ISSUE_DATE <= to_date(#{submission_time_end}, 'yyyy-mm-dd') )]]></if>               
   <if test=" entry_time_start  != null  and  entry_time_start  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND INPUT_DATE >= to_date(#{entry_time_start} , 'yyyy-mm-dd') ) ]]></if> 
   <if test=" entry_time_end  != null  and  entry_time_end  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND INPUT_DATE <= to_date(#{entry_time_end}, 'yyyy-mm-dd') )]]></if>  	
 <![CDATA[ 
		ORDER BY ORGAN_CODE ,CHANNEL_TYPE,UW_FINISH_TIME,ENTRY_TIME,OPERATION_TIME 	
 )where  ROWNUM <= ${LESS_NUM}   )tp WHERE tp.RN > ${GREATER_NUM} 
 	
 ]]>
 		
 			

	</select>

	<!-- 导出新契核保清单(保单) -->
	<select id="nbuwList_by_policy_export" resultType="java.util.Map"
		parameterType="java.util.Map">
SELECT (SELECT ORGAN_NAME
          FROM DEV_UW.T_UDMP_ORG
         WHERE ORGAN_CODE = Y.ORGAN_CODE) ORGAN_NAME,
         Y.ORGAN_CODE,
        SUBSTR(Y.ORGAN_CODE,1,4) AS FGS_ORGAN_CODE,
        (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(Y.ORGAN_CODE,1,4)) AS FGS_ORGAN_NAME,
        SUBSTR(Y.ORGAN_CODE,1,6) AS ZHGS_ORGAN_CODE,
        (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(Y.ORGAN_CODE,1,6)) AS ZHGS_ORGAN_NAME,
        Y.ORGAN_CODE AS SALES_ORGAN_CODE,
        (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = Y.ORGAN_CODE) AS SALES_ORGAN_NAME,
       (SELECT SALES_CHANNEL_NAME FROM DEV_UW.T_SALES_CHANNEL WHERE SALES_CHANNEL_CODE = N.CHANNEL_TYPE) CHANNEL_TYPE,
       Y.APPLY_CODE,
       (SELECT POLICY_CODE
          FROM DEV_NB.T_NB_CONTRACT_MASTER
         WHERE POLICY_ID = Y.POLICY_ID) POLICY_CODE,
       (SELECT LEVEL_DESC
          FROM DEV_UW.T_UW_LEVEL
         WHERE LEVEL_CODE = Y.UW_LEVEL_CODE) UW_LEVEL_CODE,
       (SELECT USER_NAME
          FROM DEV_PAS.T_UDMP_USER
         WHERE USER_ID = M.UW_USER_ID) UW_USER_ID,
       (SELECT REAL_NAME
          FROM DEV_PAS.T_UDMP_USER
         WHERE USER_ID = M.UW_USER_ID) REAL_NAME,
       (SELECT O.PERMISSION_NAME
          FROM DEV_UW.T_UDMP_PERMISSION_INFO O
         WHERE O.PERMISSION_ID =
               (SELECT U3.PERMISSION_ID
                  FROM DEV_UW.T_UDMP_GROUP_USER      U1,
                       DEV_UW.T_UDMP_GROUP_ROLE      U2,
                       DEV_UW.T_UDMP_ROLE_PERMISSION U3
                 WHERE U1.ROLE_GROUP_ID = U2.ROLE_GROUP_ID
                   AND U2.ROLE_ID = U3.ROLE_ID
                   AND U1.USER_ID = M.UW_USER_ID AND ROWNUM = 1)) UW_USER_LEVEL_CODE,
       (SELECT TYPE_NAME FROM DEV_UW.T_YES_NO WHERE YES_NO = M.UW_OVER_INDI) UW_OVER_INDI,
       '' OVER_INDI_LEVEL,
       CASE
         WHEN M.UW_OVER_INDI = '1' THEN
          (SELECT USER_NAME
             FROM DEV_UW.T_UDMP_USER
            WHERE USER_ID = M.UW_USER_ID)
         ELSE
          ''
       END UP_UW_USER_NAME,
       CASE
         WHEN M.UW_OVER_INDI = '1' THEN
          (SELECT REAL_NAME
             FROM DEV_UW.T_UDMP_USER
            WHERE USER_ID = M.UW_USER_ID)
         ELSE
          ''
       END UP_UW_REAL_NAME,
       (SELECT DECISION_DESC
          FROM DEV_UW.T_POLICY_DECISION
         WHERE DECISION_CODE = Y.POLICY_DECISION) UW_DECISION,
       (SELECT TO_CHAR(INPUT_DATE, 'YYYY-MM-DD')
          FROM DEV_NB.T_NB_CONTRACT_MASTER
         WHERE POLICY_ID = Y.POLICY_ID) ENTRY_TIME,
       TO_CHAR(M.UW_FINISH_TIME, 'YYYY-MM-DD') UW_FINISH_TIME,
       (SELECT TO_CHAR(ISSUE_DATE, 'YYYY-MM-DD')
          FROM DEV_NB.T_NB_CONTRACT_MASTER
         WHERE POLICY_ID = Y.POLICY_ID) OPERATION_TIME,
       (SELECT TO_CHAR(WM_CONCAT(DOCUMENT_NO || '-' ||
                                 (SELECT TYPE_NAME
                                    FROM DEV_UW.T_YES_NO
                                   WHERE POSITIVE_INDI = YES_NO)))
          FROM DEV_UW.T_PENOTICE
         WHERE UW_ID = Y.UW_ID) DOCUMENT_NO1,
        (SELECT TO_CHAR(WM_CONCAT(DOCUMENT_NO))
          FROM DEV_UW.T_SURVIVAL_INVESTIGATION
         WHERE UW_ID = Y.UW_ID) RREPORT_CODE1,
       (SELECT DOCUMENT_NO
          FROM DEV_UW.T_UW_NOTICE
         WHERE UW_ID = Y.UW_ID
           AND REPLY_INDI = '1'
           AND ROWNUM = 1) UW_DOCUMENT,
       (SELECT STATUS_DETAIL_NAME
          FROM DEV_UW.T_UW_STATUS_DETAIL
         WHERE STATUS_DETAIL_CODE = M.UW_STATUS_DETAIL) UW_STATUS,
       CASE
         WHEN M.UW_STATUS_DETAIL = '0302' THEN
          '是'
         ELSE
          '否'
       END EXCEED,
       CASE
         WHEN (SELECT 1
                 FROM DEV_NB.T_NB_CONTRACT_MASTER
                WHERE POLICY_ID = Y.POLICY_ID
                  AND PROPOSAL_STATUS = 12) = 1 THEN
          '是'
         ELSE
          '否'
       END UNDO,
       (SELECT TO_CHAR(WM_CONCAT((SELECT PRODUCT_NAME_SYS
                                   FROM DEV_PDS.T_BUSINESS_PRODUCT
                                  WHERE PRODUCT_CODE_SYS = BUSI_PROD_CODE)))
          FROM DEV_UW.T_UW_BUSI_PROD
         WHERE UW_ID = Y.UW_ID
           AND MASTER_BUSI_ITEM_ID IS NULL)  PRODUCT_NAME_SYS1, TO_CHAR(Y.MULTI_MAINRISK_FLAG) AS MULTI_MAINRISK_FLAG

  FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_POLICY Y, DEV_UW.T_UW_AUTO X, DEV_NB.T_NB_CONTRACT_MASTER N
 WHERE M.UW_ID = Y.UW_ID AND M.UW_ID = X.UW_ID AND M.BIZ_CODE = N.APPLY_CODE AND Y.APPLY_CODE = N.APPLY_CODE
   AND M.UW_SOURCE_TYPE = '1'
   AND (X.RULE_RUN_STATUS = 'V02' OR 
                        (X.RULE_RUN_STATUS = 'V01' AND M.REVISION_FLAG = '1'))
   <if test=" channel_type != null and channel_type !=''"><![CDATA[AND N.channel_type in (${channel_type})]]></if>
   <if test=" organ_code != null and organ_code !=''"><![CDATA[ AND Y.organ_code like #{organ_code}||'%' ]]></if>
   <if test=" uw_user_level_code != null and uw_user_level_code !=''"><![CDATA[AND exists(
     	SELECT 1 FROM  DEV_UW.T_UDMP_PERMISSION_INFO O WHERE O.PERMISSION_ID =
               (SELECT U3.PERMISSION_ID
                  FROM DEV_UW.T_UDMP_GROUP_USER      U1,
                       DEV_UW.T_UDMP_GROUP_ROLE      U2,
                       DEV_UW.T_UDMP_ROLE_PERMISSION U3
                 WHERE U1.ROLE_GROUP_ID = U2.ROLE_GROUP_ID
                   AND U2.ROLE_ID = U3.ROLE_ID
                   AND U1.USER_ID = M.UW_USER_ID) and perm_level_workflow in ( ${uw_user_level_code}) )]]></if>
   <if test=" uw_level_code  != null and uw_level_code!= '' "><![CDATA[ AND Y.uw_level_code in (${uw_level_code})]]></if>
   <if test=" uw_decision  != null and uw_decision != '' "><![CDATA[ AND Y.POLICY_DECISION in (${uw_decision})]]></if>                
   <if test=" user_name  != null and user_name != '' "><![CDATA[ AND exists(
     	SELECT 1 FROM DEV_PAS.T_UDMP_USER WHERE USER_ID = M.UW_USER_ID and user_name like #{user_name,jdbcType=VARCHAR}||'%')]]></if> 
   <if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND M.UW_FINISH_TIME >= to_date(#{uw_time_start}, 'yyyy-mm-dd') ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND M.UW_FINISH_TIME <= to_date(#{uw_time_end} , 'yyyy-mm-dd')]]></if>
   <if test=" submission_time_start  != null  and  submission_time_start  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND ISSUE_DATE >= to_date(#{submission_time_start} , 'yyyy-mm-dd') ) ]]></if> 
   <if test=" submission_time_end  != null  and  submission_time_end  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND ISSUE_DATE <= to_date(#{submission_time_end}, 'yyyy-mm-dd') )]]></if>               
   <if test=" entry_time_start  != null  and  entry_time_start  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND INPUT_DATE >= to_date(#{entry_time_start} , 'yyyy-mm-dd') ) ]]></if> 
   <if test=" entry_time_end  != null  and  entry_time_end  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND INPUT_DATE <= to_date(#{entry_time_end}, 'yyyy-mm-dd') )]]></if>
	ORDER BY ORGAN_CODE ,CHANNEL_TYPE,UW_FINISH_TIME,ENTRY_TIME,OPERATION_TIME
	</select>

	<select id="nbuwList_by_policy_count" resultType="java.lang.Integer"
		parameterType="java.util.Map">

		select count(*)
		from( SELECT 
       Y.APPLY_CODE

  FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_POLICY Y, DEV_UW.T_UW_AUTO X, DEV_NB.T_NB_CONTRACT_MASTER N
 WHERE M.UW_ID = Y.UW_ID AND M.UW_ID = X.UW_ID AND M.BIZ_CODE = N.APPLY_CODE AND Y.APPLY_CODE = N.APPLY_CODE
   AND M.UW_SOURCE_TYPE = '1'
   AND (X.RULE_RUN_STATUS = 'V02' OR 
                        (X.RULE_RUN_STATUS = 'V01' AND M.REVISION_FLAG = '1'))
   <if test=" channel_type != null and channel_type !=''"><![CDATA[AND N.channel_type in (${channel_type})]]></if>
   <if test=" organ_code != null and organ_code !=''"><![CDATA[ AND Y.organ_code like #{organ_code}||'%' ]]></if>
   <if test=" uw_user_level_code != null and uw_user_level_code !=''"><![CDATA[AND exists(
     	SELECT 1 FROM  DEV_UW.T_UDMP_PERMISSION_INFO O WHERE O.PERMISSION_ID IN
               (SELECT U3.PERMISSION_ID
                  FROM DEV_UW.T_UDMP_GROUP_USER      U1,
                       DEV_UW.T_UDMP_GROUP_ROLE      U2,
                       DEV_UW.T_UDMP_ROLE_PERMISSION U3
                 WHERE U1.ROLE_GROUP_ID = U2.ROLE_GROUP_ID
                   AND U2.ROLE_ID = U3.ROLE_ID
                   AND U1.USER_ID = M.UW_USER_ID) and perm_level_workflow in ( ${uw_user_level_code}) )]]></if>
   <if test=" uw_level_code  != null and uw_level_code!= '' "><![CDATA[ AND Y.uw_level_code in (${uw_level_code})]]></if>
   <if test=" uw_decision  != null and uw_decision != '' "><![CDATA[ AND Y.POLICY_DECISION in (${uw_decision})]]></if>                
   <if test=" user_name  != null and user_name != '' "><![CDATA[ AND exists(
     	SELECT 1 FROM DEV_PAS.T_UDMP_USER WHERE USER_ID = M.UW_USER_ID and user_name like #{user_name,jdbcType=VARCHAR}||'%')]]></if> 
   <if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND M.UW_FINISH_TIME >= to_date(#{uw_time_start}, 'yyyy-mm-dd') ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND M.UW_FINISH_TIME <= to_date(#{uw_time_end} , 'yyyy-mm-dd')]]></if>
   <if test=" submission_time_start  != null  and  submission_time_start  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND ISSUE_DATE >= to_date(#{submission_time_start} , 'yyyy-mm-dd') ) ]]></if> 
   <if test=" submission_time_end  != null  and  submission_time_end  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND ISSUE_DATE <= to_date(#{submission_time_end}, 'yyyy-mm-dd') )]]></if>               
   <if test=" entry_time_start  != null  and  entry_time_start  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND INPUT_DATE >= to_date(#{entry_time_start} , 'yyyy-mm-dd') ) ]]></if> 
   <if test=" entry_time_end  != null  and  entry_time_end  != ''  "><![CDATA[ AND exists(
                   SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER WHERE POLICY_ID = Y.POLICY_ID AND INPUT_DATE <= to_date(#{entry_time_end}, 'yyyy-mm-dd') )]]></if>
		)

	</select>

	<!-- 查询新契核保清单(责任组) -->
	<select id="nbuwlist_by_product" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT B.*
		FROM (SELECT ROWNUM RN, T1.*
		FROM (SELECT DISTINCT 
                CM.ORGAN_CODE,
                UO.ORGAN_NAME ORGAN_NAME,
                SUBSTR(CM.ORGAN_CODE,1,4) AS FGS_ORGAN_CODE,
                (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,4)) AS FGS_ORGAN_NAME,
                SUBSTR(CM.ORGAN_CODE,1,6) AS ZHGS_ORGAN_CODE,
                (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,6)) AS ZHGS_ORGAN_NAME,
                CM.ORGAN_CODE AS SALES_ORGAN_CODE,
                (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = CM.ORGAN_CODE) AS SALES_ORGAN_NAME,
                TSC.SALES_CHANNEL_NAME CHANNEL_TYPE,
                CM.APPLY_CODE APPLY_CODE,
                UP.POLICY_CODE POLICY_CODE,
                TO_CHAR(UM.UW_FINISH_TIME, 'YYYY-MM-DD') UW_FINISH_TIME,
                TO_CHAR(IO.OPERATION_TIME, 'YYYY-MM-DD') SUBMISSION_DATE,
                TO_CHAR(UM.UW_SUBMIT_TIME, 'YYYY-MM-DD') UW_SUBMIT_TIME,
                PD1.DECISION_DESC UW_DECISION,
                TO_CHAR(UU.USER_ID) UW_USER_ID,
                UU.USER_NAME USER_NAME,
                UU.REAL_NAME,
                BP.PRODUCT_NAME_SYS BUSI_ITEM_ID,
                PD3.DECISION_DESC BUSI_DECISION_CODE,
                PLF.PRODUCT_NAME ITEM_ID,
                UPT.AMOUNT AMOUNT,
                PD2.DECISION_DESC DECISION_CODE,
                TO_CHAR(UEP.EM_VALUE) EM_VALUE
		  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
		  INNER JOIN DEV_UW.T_UW_MASTER UM
		    ON CM.APPLY_CODE = UM.BIZ_CODE
		    AND UM.UW_SOURCE_TYPE = '1'
		  INNER JOIN DEV_UW.T_UW_AUTO UA
		  ON UA.UW_ID = UM.UW_ID
		  AND (UA.RULE_RUN_STATUS = 'V02' OR (UA.RULE_RUN_STATUS = 'V01' AND UM.REVISION_FLAG = '1'))
		 INNER JOIN DEV_UW.T_UW_POLICY UP
		    ON UM.UW_ID = UP.UW_ID
		  LEFT JOIN DEV_NB.T_ISSUE_OPERATION IO
		    ON CM.POLICY_ID = IO.POLICY_ID
		  LEFT JOIN DEV_PAS.T_UDMP_USER UU
		    ON UM.UW_USER_ID = UU.USER_ID
		  LEFT JOIN DEV_PAS.T_UDMP_ORG UO
		    ON CM.ORGAN_CODE = UO.ORGAN_CODE
		  LEFT JOIN DEV_UW.T_UW_PRODUCT UPT
		    ON UM.UW_ID = UPT.UW_ID
		  LEFT JOIN DEV_UW.T_UW_EXTRA_PREM UEP
		    ON UM.UW_ID = UEP.UW_ID
		  LEFT JOIN DEV_UW.T_UW_BUSI_PROD UBP
		    ON UM.UW_ID = UBP.UW_ID
		  LEFT JOIN DEV_UW.T_POLICY_DECISION PD1
		    ON PD1.DECISION_CODE = UP.POLICY_DECISION
		  LEFT JOIN DEV_UW.T_PRODUCT_DECISION PD2
		    ON PD2.DECISION_CODE = UPT.DECISION_CODE
		  LEFT JOIN DEV_UW.T_PRODUCT_DECISION PD3
		    ON PD3.DECISION_CODE = UBP.DECISION_CODE
		  LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		    ON BP.PRODUCT_CODE_SYS = UBP.BUSI_PROD_CODE
		  LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PLF
		    ON PLF.INTERNAL_ID = UPT.PRODUCT_CODE
		  LEFT JOIN DEV_UW.T_SALES_CHANNEL TSC
		    ON TSC.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE
		  LEFT JOIN (SELECT DISTINCT UU.USER_NAME,
                             PI.PERM_LEVEL_WORKFLOW,
                             UU.USER_ID
               FROM DEV_PAS.T_UDMP_USER           UU,
                    DEV_UW.T_UDMP_GROUP_USER      GU,
                    DEV_UW.T_UDMP_GROUP_ROLE      GR,
                    DEV_UW.T_UDMP_ROLE            UR,
                    DEV_UW.T_UDMP_ROLE_PERMISSION RP,
                    DEV_UW.T_UDMP_PERMISSION_INFO PI,
                    DEV_UW.T_UDMP_ROLE            TR,
                    DEV_UW.T_UDMP_PERMISSION_TYPE TUP
              WHERE UU.USER_ID = GU.USER_ID
                AND GU.ROLE_GROUP_ID = GR.ROLE_GROUP_ID
                AND GR.ROLE_ID = UR.ROLE_ID
                AND UR.ROLE_ID = RP.ROLE_ID
                AND RP.PERMISSION_ID = PI.PERMISSION_ID
                AND TR.ROLE_ID = GR.ROLE_ID
                AND TR.ROLE_TYPE = 2
                AND TUP.PERMISSION_TYPE_NAME = 'UW_AUTH') TP1
    	ON TP1.USER_ID = UM.UW_USER_ID
 		WHERE 1 = 1

		<if test=" channel_type != null and channel_type !=''"><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type})]]></if>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND (CM.ORGAN_CODE LIKE ${organ_code}) ]]></if>
		<if test=" uw_decision  != null and uw_decision != '' "><![CDATA[ AND UP.POLICY_DECISION IN (${uw_decision})]]></if>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ AND TP1.USER_NAME LIKE #{user_name,jdbcType=VARCHAR}||'%' ]]></if>

		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND UM.UW_FINISH_TIME >= to_date(#{fm_uw_time_start}, 'yyyy-mm-dd hh24:mi:ss') ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND UM.UW_FINISH_TIME <= to_date(#{fm_uw_time_end} , 'yyyy-mm-dd hh24:mi:ss')]]></if>

		<if
			test=" submission_time_start  != null  and  submission_time_start  != ''  "><![CDATA[ AND IO.OPERATION_TIME >= to_date(#{fm_submission_time_start} , 'yyyy-mm-dd hh24:mi:ss')]]></if>
		<if test=" submission_time_end  != null  and  submission_time_end  != ''  "><![CDATA[ AND IO.OPERATION_TIME <= to_date(#{fm_submission_time_end}, 'yyyy-mm-dd hh24:mi:ss') ]]></if>
			ORDER BY ORGAN_CODE,CHANNEL_TYPE,UW_FINISH_TIME,UW_SUBMIT_TIME,SUBMISSION_DATE
		) t1
        <![CDATA[ where ROWNUM <= #{LESS_NUM}]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 导出新契核保清单(责任组) -->
	<select id="nbuwlist_by_product_export" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT DISTINCT 
                CM.ORGAN_CODE,
                UO.ORGAN_NAME ORGAN_NAME,
                SUBSTR(CM.ORGAN_CODE,1,4) AS FGS_ORGAN_CODE,
                (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,4)) AS FGS_ORGAN_NAME,
                SUBSTR(CM.ORGAN_CODE,1,6) AS ZHGS_ORGAN_CODE,
                (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(CM.ORGAN_CODE,1,6)) AS ZHGS_ORGAN_NAME,
                CM.ORGAN_CODE AS SALES_ORGAN_CODE,
                (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = CM.ORGAN_CODE) AS SALES_ORGAN_NAME,
                TSC.SALES_CHANNEL_NAME CHANNEL_TYPE,
                CM.APPLY_CODE APPLY_CODE,
                UP.POLICY_CODE POLICY_CODE,
                TO_CHAR(UM.UW_FINISH_TIME, 'YYYY-MM-DD') UW_FINISH_TIME,
                TO_CHAR(IO.OPERATION_TIME, 'YYYY-MM-DD') SUBMISSION_DATE,
                TO_CHAR(UM.UW_SUBMIT_TIME, 'YYYY-MM-DD') UW_SUBMIT_TIME,
                PD1.DECISION_DESC UW_DECISION,
                TO_CHAR(UU.USER_ID) UW_USER_ID,
                UU.USER_NAME USER_NAME,
                UU.REAL_NAME,
                BP.PRODUCT_NAME_SYS BUSI_ITEM_ID,
                PD3.DECISION_DESC BUSI_DECISION_CODE,
                PLF.PRODUCT_NAME ITEM_ID,
                UPT.AMOUNT AMOUNT,
                PD2.DECISION_DESC DECISION_CODE,
                TO_CHAR(UEP.EM_VALUE) EM_VALUE
		  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
		  INNER JOIN DEV_UW.T_UW_MASTER UM
		    ON CM.APPLY_CODE = UM.BIZ_CODE
		    AND UM.UW_SOURCE_TYPE = '1'
		  INNER JOIN DEV_UW.T_UW_AUTO UA
		  ON UA.UW_ID = UM.UW_ID
		  AND (UA.RULE_RUN_STATUS = 'V02' OR (UA.RULE_RUN_STATUS = 'V01' AND UM.REVISION_FLAG = '1'))
		 INNER JOIN DEV_UW.T_UW_POLICY UP
		    ON UM.UW_ID = UP.UW_ID
		  LEFT JOIN DEV_NB.T_ISSUE_OPERATION IO
		    ON CM.POLICY_ID = IO.POLICY_ID
		  LEFT JOIN DEV_PAS.T_UDMP_USER UU
		    ON UM.UW_USER_ID = UU.USER_ID
		  LEFT JOIN DEV_PAS.T_UDMP_ORG UO
		    ON CM.ORGAN_CODE = UO.ORGAN_CODE
		  LEFT JOIN DEV_UW.T_UW_PRODUCT UPT
		    ON UM.UW_ID = UPT.UW_ID
		  LEFT JOIN DEV_UW.T_UW_EXTRA_PREM UEP
		    ON UM.UW_ID = UEP.UW_ID
		  LEFT JOIN DEV_UW.T_UW_BUSI_PROD UBP
		    ON UM.UW_ID = UBP.UW_ID
		  LEFT JOIN DEV_UW.T_POLICY_DECISION PD1
		    ON PD1.DECISION_CODE = UP.POLICY_DECISION
		  LEFT JOIN DEV_UW.T_PRODUCT_DECISION PD2
		    ON PD2.DECISION_CODE = UPT.DECISION_CODE
		  LEFT JOIN DEV_UW.T_PRODUCT_DECISION PD3
		    ON PD3.DECISION_CODE = UBP.DECISION_CODE
		  LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		    ON BP.PRODUCT_CODE_SYS = UBP.BUSI_PROD_CODE
		  LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PLF
		    ON PLF.INTERNAL_ID = UPT.PRODUCT_CODE
		  LEFT JOIN DEV_UW.T_SALES_CHANNEL TSC
		    ON TSC.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE
		  LEFT JOIN (SELECT DISTINCT UU.USER_NAME,
                             PI.PERM_LEVEL_WORKFLOW,
                             UU.USER_ID
               FROM DEV_PAS.T_UDMP_USER           UU,
                    DEV_UW.T_UDMP_GROUP_USER      GU,
                    DEV_UW.T_UDMP_GROUP_ROLE      GR,
                    DEV_UW.T_UDMP_ROLE            UR,
                    DEV_UW.T_UDMP_ROLE_PERMISSION RP,
                    DEV_UW.T_UDMP_PERMISSION_INFO PI,
                    DEV_UW.T_UDMP_ROLE            TR,
                    DEV_UW.T_UDMP_PERMISSION_TYPE TUP
              WHERE UU.USER_ID = GU.USER_ID
                AND GU.ROLE_GROUP_ID = GR.ROLE_GROUP_ID
                AND GR.ROLE_ID = UR.ROLE_ID
                AND UR.ROLE_ID = RP.ROLE_ID
                AND RP.PERMISSION_ID = PI.PERMISSION_ID
                AND TR.ROLE_ID = GR.ROLE_ID
                AND TR.ROLE_TYPE = 2
                AND TUP.PERMISSION_TYPE_NAME = 'UW_AUTH') TP1
    	ON TP1.USER_ID = UM.UW_USER_ID
 		WHERE 1 = 1

		<if test=" channel_type != null and channel_type !=''"><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type})]]></if>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[ AND (CM.ORGAN_CODE LIKE ${organ_code}) ]]></if>
		<if test=" uw_decision  != null and uw_decision != '' "><![CDATA[ AND UP.POLICY_DECISION IN (${uw_decision})]]></if>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ AND TP1.USER_NAME LIKE #{user_name,jdbcType=VARCHAR}||'%' ]]></if>

		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND UM.UW_FINISH_TIME >= to_date(#{fm_uw_time_start}, 'yyyy-mm-dd hh24:mi:ss') ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND UM.UW_FINISH_TIME <= to_date(#{fm_uw_time_end} , 'yyyy-mm-dd hh24:mi:ss')]]></if>
		<if
			test=" submission_time_start  != null  and  submission_time_start  != ''  "><![CDATA[ AND IO.OPERATION_TIME >= to_date(#{fm_submission_time_start} , 'yyyy-mm-dd hh24:mi:ss')]]></if>
		<if test=" submission_time_end  != null  and  submission_time_end  != ''  "><![CDATA[ AND IO.OPERATION_TIME <= to_date(#{fm_submission_time_end}, 'yyyy-mm-dd hh24:mi:ss') ]]></if>
		ORDER BY ORGAN_CODE,CHANNEL_TYPE,UW_FINISH_TIME,UW_SUBMIT_TIME,SUBMISSION_DATE
	</select>

	<select id="nbuwlist_by_product_count" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		SELECT COUNT(*)
		FROM ( SELECT DISTINCT CM.ORGAN_CODE,
                UO.ORGAN_NAME ORGAN_NAME,
                TSC.SALES_CHANNEL_NAME CHANNEL_TYPE,
                CM.APPLY_CODE APPLY_CODE,
                UP.POLICY_CODE POLICY_CODE,
                TO_CHAR(UM.UW_FINISH_TIME, 'YYYY-MM-DD') UW_FINISH_TIME,
                TO_CHAR(IO.OPERATION_TIME, 'YYYY-MM-DD') SUBMISSION_DATE,
                TO_CHAR(UM.UW_SUBMIT_TIME, 'YYYY-MM-DD') UW_SUBMIT_TIME,
                PD1.DECISION_DESC UW_DECISION,
                TO_CHAR(UU.USER_ID) UW_USER_ID,
                UU.USER_NAME USER_NAME,
                UU.REAL_NAME,
                BP.PRODUCT_NAME_SYS BUSI_ITEM_ID,
                PD3.DECISION_DESC BUSI_DECISION_CODE,
                PLF.PRODUCT_NAME ITEM_ID,
                UPT.AMOUNT AMOUNT,
                PD2.DECISION_DESC DECISION_CODE,
                TO_CHAR(UEP.EM_VALUE) EM_VALUE
		  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
		  INNER JOIN DEV_UW.T_UW_MASTER UM
		    ON CM.APPLY_CODE = UM.BIZ_CODE
		    AND UM.UW_SOURCE_TYPE = '1'
		  INNER JOIN DEV_UW.T_UW_AUTO UA
		  ON UA.UW_ID = UM.UW_ID
		  AND (UA.RULE_RUN_STATUS = 'V02' OR (UA.RULE_RUN_STATUS = 'V01' AND UM.REVISION_FLAG = '1'))
		 INNER JOIN DEV_UW.T_UW_POLICY UP
		    ON UM.UW_ID = UP.UW_ID
		  LEFT JOIN DEV_NB.T_ISSUE_OPERATION IO
		    ON CM.POLICY_ID = IO.POLICY_ID
		  LEFT JOIN DEV_PAS.T_UDMP_USER UU
		    ON UM.UW_USER_ID = UU.USER_ID
		  LEFT JOIN DEV_PAS.T_UDMP_ORG UO
		    ON CM.ORGAN_CODE = UO.ORGAN_CODE
		  LEFT JOIN DEV_UW.T_UW_PRODUCT UPT
		    ON UM.UW_ID = UPT.UW_ID
		  LEFT JOIN DEV_UW.T_UW_EXTRA_PREM UEP
		    ON UM.UW_ID = UEP.UW_ID
		  LEFT JOIN DEV_UW.T_UW_BUSI_PROD UBP
		    ON UM.UW_ID = UBP.UW_ID
		  LEFT JOIN DEV_UW.T_POLICY_DECISION PD1
		    ON PD1.DECISION_CODE = UP.POLICY_DECISION
		  LEFT JOIN DEV_UW.T_PRODUCT_DECISION PD2
		    ON PD2.DECISION_CODE = UPT.DECISION_CODE
		  LEFT JOIN DEV_UW.T_PRODUCT_DECISION PD3
		    ON PD3.DECISION_CODE = UBP.DECISION_CODE
		  LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		    ON BP.PRODUCT_CODE_SYS = UBP.BUSI_PROD_CODE
		  LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PLF
		    ON PLF.INTERNAL_ID = UPT.PRODUCT_CODE
		  LEFT JOIN DEV_UW.T_SALES_CHANNEL TSC
		    ON TSC.SALES_CHANNEL_CODE = CM.CHANNEL_TYPE
		  LEFT JOIN (SELECT DISTINCT UU.USER_NAME,
                             PI.PERM_LEVEL_WORKFLOW,
                             UU.USER_ID
               FROM DEV_PAS.T_UDMP_USER           UU,
                    DEV_UW.T_UDMP_GROUP_USER      GU,
                    DEV_UW.T_UDMP_GROUP_ROLE      GR,
                    DEV_UW.T_UDMP_ROLE            UR,
                    DEV_UW.T_UDMP_ROLE_PERMISSION RP,
                    DEV_UW.T_UDMP_PERMISSION_INFO PI,
                    DEV_UW.T_UDMP_ROLE            TR,
                    DEV_UW.T_UDMP_PERMISSION_TYPE TUP
              WHERE UU.USER_ID = GU.USER_ID
                AND GU.ROLE_GROUP_ID = GR.ROLE_GROUP_ID
                AND GR.ROLE_ID = UR.ROLE_ID
                AND UR.ROLE_ID = RP.ROLE_ID
                AND RP.PERMISSION_ID = PI.PERMISSION_ID
                AND TR.ROLE_ID = GR.ROLE_ID
                AND TR.ROLE_TYPE = 2
                AND TUP.PERMISSION_TYPE_NAME = 'UW_AUTH') TP1
    	ON TP1.USER_ID = UM.UW_USER_ID
 		WHERE 1 = 1

		<if test=" channel_type != null and channel_type !=''"><![CDATA[ AND CM.CHANNEL_TYPE IN (${channel_type})]]></if>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[ AND (CM.ORGAN_CODE LIKE ${organ_code}) ]]></if>
		<if test=" uw_decision  != null and uw_decision != '' "><![CDATA[ AND UP.POLICY_DECISION IN (${uw_decision})]]></if>
		<if test=" user_name  != null and user_name != '' "><![CDATA[ AND TP1.USER_NAME LIKE #{user_name,jdbcType=VARCHAR}||'%' ]]></if>

		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND UM.UW_FINISH_TIME >= to_date(#{fm_uw_time_start}, 'yyyy-mm-dd hh24:mi:ss') ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND UM.UW_FINISH_TIME <= to_date(#{fm_uw_time_end} , 'yyyy-mm-dd hh24:mi:ss')]]></if>

		<if test=" submission_time_start  != null  and  submission_time_start  != ''  "><![CDATA[ AND IO.OPERATION_TIME >= to_date(#{fm_submission_time_start} , 'yyyy-mm-dd hh24:mi:ss')]]></if>
		<if test=" submission_time_end  != null  and  submission_time_end  != ''  "><![CDATA[ AND IO.OPERATION_TIME <= to_date(#{fm_submission_time_end}, 'yyyy-mm-dd hh24:mi:ss') ]]></if>
		)
	</select>
	
	<!-- 出险及赔付查询功能 -->
	<select id="queryClaimAndPayment" resultType="java.util.Map" parameterType="java.util.Map">
 
 	<![CDATA[SELECT * FROM (SELECT ROWNUM AS RN,
               POLICY_DECISION,
               UW_USER_ID,
               USER_NAME,
               POLICY_CODE,
               ORGAN_CODE,
               INSURED_ID,
               RPTR_TIME,
               CASE_STATUS,
               CASE_NO,
               CLAIM_DATE,
               ACC_REASON,
               ROLE_TYPE,
               VALIDATE_DATE,
               AMOUNT,
               CHANNEL_TYPE,
               ORGAN_NAME,
               P_POSITIVE_INDI,
               SI_POSITIVE_INDI,
               IS_P,
               IS_SI,
               BUSI_PROD_CODE,
               PRODUCT_NAME_STD, --险种名称
               BUSI_FLAG, --险种标识
               ACC_DATE, --事故日期
               ACC_DESC, --事故描述
               CASE_FLAG_NAME, --案件类型
               CLAIM_NAME, --理赔类型
               LIAB_NAME, --保项名称
               GENDER_DESC, -- 性别
               AUDIT_DECISION, --审核结论
               APPROVE_TIME, --审批通过日期
               LIAB_CONCLUSION, --保项结论 
               AGE,
               REJECT_REASON, --拒付原因
               POSITIVE_FLAG, --调查阳性标识
               ACC_NAME1, -- 出险结果1
               ACC_NAME2, --出险结果2
               SECOND_ORGAN_CODE,
               THIRD_ORGAN_CODE,
               FOURTH_ORGAN_CODE,
               AGENT_Code,
               AGENT_ORGAN_CODE --服务员区部代码
          FROM (SELECT DISTINCT (SELECT A.DECISION_DESC
                                   FROM DEV_UW.T_POLICY_DECISION A
                                  WHERE A.DECISION_CODE = UP.POLICY_DECISION) POLICY_DECISION,
                                UP.UW_USER_ID,
                                (SELECT B.USER_NAME
                                   FROM DEV_PAS.T_UDMP_USER B
                                  WHERE B.USER_ID = UP.UW_USER_ID) USER_NAME,
                                UP.POLICY_CODE POLICY_CODE,
                                UP.ORGAN_CODE,
                                CLMUWUW.INSURED_ID,
                                TO_CHAR(CLMUWUW.RPTR_TIME, 'yyyy-mm-dd') RPTR_TIME,
                                CLMUWUW.CASE_STATUS,
                                CLMUWUW.CASE_NO,
                                TO_CHAR(CLMUWUW.CLAIM_DATE, 'yyyy-mm-dd') CLAIM_DATE,
                                CLMUWUW.ACC_REASON,
                                CLMUWUW.ROLE_TYPE,
                                TO_CHAR(TM.VALIDATE_DATE, 'yyyy-mm-dd') VALIDATE_DATE,
                                A.AMOUNT, --保额
                                A.BUSI_PROD_CODE, --险种编码
                                (SELECT BP.PRODUCT_NAME_STD
                                   FROM DEV_UW.T_BUSINESS_PRODUCT BP
                                  WHERE BP.PRODUCT_CODE_SYS = A.BUSI_PROD_CODE) AS PRODUCT_NAME_STD, --险种名称
                                (CASE
                                  WHEN ((SELECT BP.COVER_PERIOD_TYPE
                                           FROM DEV_PDS.T_BUSINESS_PRODUCT BP
                                          WHERE BP.PRODUCT_CODE_SYS =
                                                A.BUSI_PROD_CODE) = '0') THEN
                                   '长险'
                                  ELSE
                                   '短险'
                                END) AS BUSI_FLAG, --险种标识
                                (SELECT SC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL SC
                                  WHERE SC.SALES_CHANNEL_CODE =
                                        TM.CHANNEL_TYPE) CHANNEL_TYPE,
                                (SELECT A.ORGAN_NAME
                                   FROM DEV_PAS.T_UDMP_ORG A
                                  WHERE A.ORGAN_CODE = UP.ORGAN_CODE) ORGAN_NAME,
                                NVL((SELECT CASE
                                             WHEN POSITIVE_INDI = '0' THEN
                                              '阴性'
                                             WHEN POSITIVE_INDI = '1' THEN
                                              '阳性'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_PENOTICE
                                     WHERE UP.UW_ID = UW_ID),
                                    '') P_POSITIVE_INDI,
                                NVL((SELECT CASE
                                             WHEN POSITIVE_INDI = '0' THEN
                                              '阴性'
                                             WHEN POSITIVE_INDI = '1' THEN
                                              '阳性'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_SURVIVAL_INVESTIGATION
                                     WHERE UP.UW_ID = UW_ID),
                                    '') SI_POSITIVE_INDI,
                                NVL((SELECT CASE
                                             WHEN POLICY_CODE IS NULL THEN
                                              '否'
                                             WHEN POLICY_CODE IS NOT NULL THEN
                                              '是'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_PENOTICE
                                     WHERE UP.UW_ID = UW_ID),
                                    '否') IS_P,
                                NVL((SELECT CASE
                                             WHEN POLICY_CODE IS NULL THEN
                                              '否'
                                             WHEN POLICY_CODE IS NOT NULL THEN
                                              '是'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_SURVIVAL_INVESTIGATION
                                     WHERE UP.UW_ID = UW_ID),
                                    '否') IS_SI,
                                TO_CHAR(CLMUWUW.ACC_DATE, 'yyyy-mm-dd') ACC_DATE, --事故日期
                                CLMUWUW.ACC_DESC, --事故描述
                                CLMUWUW.CASE_FLAG_NAME, --案件类型
                                CLMUWUW.CLAIM_NAME, --理赔类型
                                CLMUWUW.LIAB_NAME, --保项名称
                                CLMUWUW.GENDER_DESC, -- 性别
                                CLMUWUW.AUDIT_DECISION, --审核结论
                                TO_CHAR(CLMUWUW.APPROVE_TIME, 'yyyy-mm-dd') APPROVE_TIME, --审批通过日期
                                CLMUWUW.LIAB_CONCLUSION, --保项结论
                                TO_CHAR(TRUNC(MONTHS_BETWEEN(CLMUWUW.CLAIM_DATE,
                                                     CUSTOMER_BIRTHDAY) / 12)) AS AGE,
                                CLMUWUW.REJECT_REASON, --拒付原因
                                (SELECT SC.POSITIVE_FLAG
                                   FROM DEV_CLM.T_SURVEY_CONCLUSION SC,
                                        DEV_CLM.T_SURVEY_APPLY      TSA
                                  WHERE SC.APPLY_ID = TSA.APPLY_ID
                                    AND TSA.CASE_ID = CLMUWUW.CASE_ID
                                    AND ROWNUM = 1) AS POSITIVE_FLAG, --调查阳性标识
                                (SELECT A1.NAME
                                   FROM DEV_CLM.T_ACCIDENT1 A1
                                  WHERE A1.CODE =
                                        (SELECT R.ACC_RESULT1
                                           FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT R
                                          WHERE R.CASE_ID = CLMUWUW.CASE_ID
                                            AND ROWNUM = 1)
                                    AND ROWNUM = 1) AS ACC_NAME1, -- 出险结果1
                                (SELECT A2.NAME
                                   FROM DEV_CLM.T_ACCIDENT2 A2
                                  WHERE A2.CODE =
                                        (SELECT R.ACC_RESULT2
                                           FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT R
                                          WHERE R.CASE_ID = CLMUWUW.CASE_ID
                                            AND ROWNUM = 1)
                                    AND ROWNUM = 1) AS ACC_NAME2, --出险结果2
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 3 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 4))
                                END AS SECOND_ORGAN_CODE,
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 5 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 6))
                                END AS THIRD_ORGAN_CODE,
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 7 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 8))
                                END AS FOURTH_ORGAN_CODE,
                                (SELECT TCAGENT.AGENT_CODE
                                   FROM DEV_CLM.T_CONTRACT_AGENT TCAGENT
                                  WHERE TCAGENT.CASE_ID = CLMUWUW.CASE_ID
                                    AND TCAGENT.POLICY_CODE = TM.POLICY_CODE
                                    AND TCAGENT.CUR_FLAG = '1'
                                    AND ROWNUM = 1) AGENT_Code, --服务员代码
                                (SELECT TCAGENT.AGENT_ORGAN_CODE
                                   FROM DEV_CLM.T_CONTRACT_AGENT TCAGENT
                                  WHERE TCAGENT.CASE_ID = CLMUWUW.CASE_ID
                                    AND TCAGENT.POLICY_CODE = TM.POLICY_CODE
                                    AND TCAGENT.CUR_FLAG = '1'
                                    AND ROWNUM = 1) AGENT_ORGAN_CODE --服务员区部代码
                  FROM DEV_UW.T_UW_POLICY UP,
                       DEV_PAS.T_CONTRACT_MASTER TM,
                       DEV_UW.T_UW_PRODUCT A,
                       (SELECT *
                          FROM DEV_UW.T_UW_MASTER UM,
                               (SELECT CLM.*
                                  FROM (SELECT (SELECT C.CUSTOMER_NAME
                                                  FROM DEV_CLM.T_CUSTOMER C
                                                 WHERE C.CUSTOMER_ID =
                                                       CC.INSURED_ID) INSURED_ID,
                                               
                                               (SELECT CASE
                                                         WHEN COUNT(POLICY_CODE) IS NULL THEN
                                                          '投保人'
                                                         WHEN COUNT(POLICY_CODE) IS NOT NULL THEN
                                                          '被保人'
                                                       END
                                                  FROM DEV_CLM.T_INSURED_LIST,
                                                       DEV_CLM.T_CLAIM_CASE
                                                 WHERE DEV_CLM.T_CLAIM_CASE.INSURED_ID =
                                                       CUSTOMER_ID) ROLE_TYPE,
                                               CC.INSERT_TIME RPTR_TIME,
                                               (SELECT D.NAME
                                                  FROM DEV_CLM.T_CASE_STATUS D
                                                 WHERE D.CODE = CC.CASE_STATUS) CASE_STATUS,
                                               CC.CASE_NO,
                                               CSC.CLAIM_DATE,
                                               (SELECT E.NAME
                                                  FROM DEV_CLM.T_CLAIM_NATURE E
                                                 WHERE E.CODE = CSC.ACC_REASON) ACC_REASON,
                                               CA.ACC_DATE,
                                               CA.ACC_DESC,
                                               (SELECT CL.NAME
                                                  FROM DEV_CLM.T_CASE_LEVEL CL
                                                 WHERE CC.CASE_FLAG = CL.CODE
                                                   AND ROWNUM = 1) AS CASE_FLAG_NAME,
                                               (SELECT R.NAME
                                                  FROM APP___CLM__DBUSER.T_CLAIM_TYPE R
                                                 WHERE R.CODE = CL.CLAIM_TYPE) AS CLAIM_NAME,
                                               CL.LIAB_NAME,
                                               (SELECT CUSTOMER_BIRTHDAY
                                                  FROM DEV_CLM.T_CUSTOMER TC
                                                 WHERE TC.CUSTOMER_ID =
                                                       CC.INSURED_ID) AS CUSTOMER_BIRTHDAY,
                                               (SELECT TG.GENDER_DESC
                                                  FROM DEV_CLM.T_GENDER TG
                                                 WHERE TG.GENDER_CODE =
                                                       (SELECT CUSTOMER_GENDER
                                                          FROM DEV_CLM.T_CUSTOMER TC
                                                         WHERE TC.CUSTOMER_ID =
                                                               CC.INSURED_ID)) AS GENDER_DESC,
                                               
                                               (CASE
                                                 WHEN CC.EASY_AUDIT_DECISION = 1 THEN
                                                  '通过'
                                                 ELSE
                                                  (SELECT CAD.NAME
                                                     FROM DEV_CLM.T_CLAIM_AUDIT_DECISION CAD
                                                    WHERE CAD.CODE =
                                                          CC.AUDIT_DECISION
                                                      AND ROWNUM = 1)
                                               END) AS AUDIT_DECISION,
                                               (CASE
                                                 WHEN CC.CASE_STATUS = '80' AND
                                                      CC.APPROVER_ID IS NULL AND
                                                      CC.AUDITOR_ID IS NOT NULL THEN
                                                  CC.AUDIT_TIME
                                                 ELSE
                                                  CC.APPROVE_TIME
                                               END) AS APPROVE_TIME,
                                               (SELECT CLD.NAME
                                                  FROM DEV_CLM.T_CLAIM_LIAB_DECISION CLD
                                                 WHERE CL.LIAB_CONCLUSION =
                                                       CLD.CODE
                                                   AND ROWNUM = 1) AS LIAB_CONCLUSION,
                                               (SELECT TCAR.NAME
                                                  FROM DEV_CLM.T_CLAIM_AUDIT_REJECT TCAR
                                                 WHERE TCAR.CODE =
                                                       SUBSTR(CL.REJECT_CODE,
                                                              0, 2)) REJECT_REASON,
                                               CC.CASE_ID
                                          FROM DEV_CLM.T_CLAIM_CASE     CC,
                                               DEV_CLM.T_CLAIM_SUB_CASE CSC,
                                               DEV_CLM.T_CLAIM_ACCIDENT CA,
                                               DEV_CLM.T_CLAIM_LIAB     CL
                                         WHERE CC.CASE_ID = CSC.CASE_ID
                                           AND CC.ACCIDENT_ID = CA.ACCIDENT_ID
                                           AND CC.CASE_ID = CL.CASE_ID) CLM) CLMUW
                         WHERE UM.BIZ_CODE = CLMUW.CASE_NO) CLMUWUW
                
                 WHERE CLMUWUW.UW_ID = UP.UW_ID
                   AND UP.POLICY_CODE = TM.POLICY_CODE
                   AND UP.UW_ID = A.UW_ID]]>
                   
        <if test=" channel_type != null and channel_type !=''"><![CDATA[ AND TM.CHANNEL_TYPE in (${channel_type})]]></if>
        <if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
        <if test=" validate_date_start != null and validate_date_start !=''"><![CDATA[ AND TM.VALIDATE_DATE >= to_date(#{validate_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" validate_date_end != null and validate_date_end !=''"><![CDATA[ AND TM.VALIDATE_DATE <= to_date(#{validate_date_end},'YYYY-MM-DD') ]]></if>
    	<if test=" claim_date_start != null and claim_date_start !=''"><![CDATA[ AND CLMUWUW.CLAIM_DATE >= to_date(#{claim_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" claim_date_end != null and claim_date_end !=''"><![CDATA[ AND CLMUWUW.CLAIM_DATE <= to_date(#{claim_date_end},'YYYY-MM-DD') ]]></if>
        <![CDATA[) WHERE ROWNUM <= #{LESS_NUM}) A WHERE A.RN > #{GREATER_NUM} ]]>
	
	</select>
	<!-- 出险及赔付清单 查询个数 -->
	<select id="queryClaimAndPaymentTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(*) FROM (SELECT DISTINCT (SELECT A.DECISION_DESC
                                   FROM DEV_UW.T_POLICY_DECISION A
                                  WHERE A.DECISION_CODE = UP.POLICY_DECISION) POLICY_DECISION,
                                UP.UW_USER_ID,
                                (SELECT B.USER_NAME
                                   FROM DEV_PAS.T_UDMP_USER B
                                  WHERE B.USER_ID = UP.UW_USER_ID) USER_NAME,
                                UP.POLICY_CODE POLICY_CODE,
                                UP.ORGAN_CODE,
                                CLMUWUW.INSURED_ID,
                                TO_CHAR(CLMUWUW.RPTR_TIME, 'yyyy-mm-dd') RPTR_TIME,
                                CLMUWUW.CASE_STATUS,
                                CLMUWUW.CASE_NO,
                                TO_CHAR(CLMUWUW.CLAIM_DATE, 'yyyy-mm-dd') CLAIM_DATE,
                                CLMUWUW.ACC_REASON,
                                CLMUWUW.ROLE_TYPE,
                                TO_CHAR(TM.VALIDATE_DATE, 'yyyy-mm-dd') VALIDATE_DATE,
                                A.AMOUNT, --保额
                                A.BUSI_PROD_CODE, --险种编码
                                (SELECT BP.PRODUCT_NAME_STD
                                   FROM DEV_UW.T_BUSINESS_PRODUCT BP
                                  WHERE BP.PRODUCT_CODE_SYS = A.BUSI_PROD_CODE) AS PRODUCT_NAME_STD, --险种名称
                                (CASE
                                  WHEN ((SELECT BP.COVER_PERIOD_TYPE
                                           FROM DEV_PDS.T_BUSINESS_PRODUCT BP
                                          WHERE BP.PRODUCT_CODE_SYS =
                                                A.BUSI_PROD_CODE) = '0') THEN
                                   '长险'
                                  ELSE
                                   '短险'
                                END) AS BUSI_FLAG, --险种标识
                                (SELECT SC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL SC
                                  WHERE SC.SALES_CHANNEL_CODE =
                                        TM.CHANNEL_TYPE) CHANNEL_TYPE,
                                (SELECT A.ORGAN_NAME
                                   FROM DEV_PAS.T_UDMP_ORG A
                                  WHERE A.ORGAN_CODE = UP.ORGAN_CODE) ORGAN_NAME,
                                NVL((SELECT CASE
                                             WHEN POSITIVE_INDI = '0' THEN
                                              '阴性'
                                             WHEN POSITIVE_INDI = '1' THEN
                                              '阳性'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_PENOTICE
                                     WHERE UP.UW_ID = UW_ID),
                                    '') P_POSITIVE_INDI,
                                NVL((SELECT CASE
                                             WHEN POSITIVE_INDI = '0' THEN
                                              '阴性'
                                             WHEN POSITIVE_INDI = '1' THEN
                                              '阳性'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_SURVIVAL_INVESTIGATION
                                     WHERE UP.UW_ID = UW_ID),
                                    '') SI_POSITIVE_INDI,
                                NVL((SELECT CASE
                                             WHEN POLICY_CODE IS NULL THEN
                                              '否'
                                             WHEN POLICY_CODE IS NOT NULL THEN
                                              '是'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_PENOTICE
                                     WHERE UP.UW_ID = UW_ID),
                                    '否') IS_P,
                                NVL((SELECT CASE
                                             WHEN POLICY_CODE IS NULL THEN
                                              '否'
                                             WHEN POLICY_CODE IS NOT NULL THEN
                                              '是'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_SURVIVAL_INVESTIGATION
                                     WHERE UP.UW_ID = UW_ID),
                                    '否') IS_SI,
                                TO_CHAR(CLMUWUW.ACC_DATE, 'yyyy-mm-dd') ACC_DATE, --事故日期
                                CLMUWUW.ACC_DESC, --事故描述
                                CLMUWUW.CASE_FLAG_NAME, --案件类型
                                CLMUWUW.CLAIM_NAME, --理赔类型
                                CLMUWUW.LIAB_NAME, --保项名称
                                CLMUWUW.GENDER_DESC, -- 性别
                                CLMUWUW.AUDIT_DECISION, --审核结论
                                TO_CHAR(CLMUWUW.APPROVE_TIME, 'yyyy-mm-dd') APPROVE_TIME, --审批通过日期
                                CLMUWUW.LIAB_CONCLUSION, --保项结论
                                TO_CHAR(TRUNC(MONTHS_BETWEEN(CLMUWUW.CLAIM_DATE,
                                                     CUSTOMER_BIRTHDAY) / 12)) AS AGE,
                                CLMUWUW.REJECT_REASON, --拒付原因
                                (SELECT A1.NAME
                                   FROM DEV_CLM.T_ACCIDENT1 A1
                                  WHERE A1.CODE =
                                        (SELECT R.ACC_RESULT1
                                           FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT R
                                          WHERE R.CASE_ID = CLMUWUW.CASE_ID
                                            AND ROWNUM = 1)
                                    AND ROWNUM = 1) AS ACC_NAME1, -- 出险结果1
                                (SELECT A2.NAME
                                   FROM DEV_CLM.T_ACCIDENT2 A2
                                  WHERE A2.CODE =
                                        (SELECT R.ACC_RESULT2
                                           FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT R
                                          WHERE R.CASE_ID = CLMUWUW.CASE_ID
                                            AND ROWNUM = 1)
                                    AND ROWNUM = 1) AS ACC_NAME2, --出险结果2
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 3 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 4))
                                END AS SECOND_ORGAN_CODE,
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 5 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 6))
                                END AS THIRD_ORGAN_CODE,
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 7 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 8))
                                END AS FOURTH_ORGAN_CODE,
                                (SELECT TCAGENT.AGENT_CODE
                                   FROM DEV_CLM.T_CONTRACT_AGENT TCAGENT
                                  WHERE TCAGENT.CASE_ID = CLMUWUW.CASE_ID
                                    AND TCAGENT.POLICY_CODE = TM.POLICY_CODE
                                    AND TCAGENT.CUR_FLAG = '1'
                                    AND ROWNUM = 1) AGENT_Code, --服务员代码
                                (SELECT TCAGENT.AGENT_ORGAN_CODE
                                   FROM DEV_CLM.T_CONTRACT_AGENT TCAGENT
                                  WHERE TCAGENT.CASE_ID = CLMUWUW.CASE_ID
                                    AND TCAGENT.POLICY_CODE = TM.POLICY_CODE
                                    AND TCAGENT.CUR_FLAG = '1'
                                    AND ROWNUM = 1) AGENT_ORGAN_CODE --服务员区部代码
                  FROM DEV_UW.T_UW_POLICY UP,
                       DEV_PAS.T_CONTRACT_MASTER TM,
                       DEV_UW.T_UW_PRODUCT A,
                       (SELECT *
                          FROM DEV_UW.T_UW_MASTER UM,
                               (SELECT CLM.*
                                  FROM (SELECT (SELECT C.CUSTOMER_NAME
                                                  FROM DEV_CLM.T_CUSTOMER C
                                                 WHERE C.CUSTOMER_ID =
                                                       CC.INSURED_ID) INSURED_ID,
                                               
                                               (SELECT CASE
                                                         WHEN COUNT(POLICY_CODE) IS NULL THEN
                                                          '投保人'
                                                         WHEN COUNT(POLICY_CODE) IS NOT NULL THEN
                                                          '被保人'
                                                       END
                                                  FROM DEV_CLM.T_INSURED_LIST,
                                                       DEV_CLM.T_CLAIM_CASE
                                                 WHERE DEV_CLM.T_CLAIM_CASE.INSURED_ID =
                                                       CUSTOMER_ID) ROLE_TYPE,
                                               CC.INSERT_TIME AS RPTR_TIME,
                                               (SELECT D.NAME
                                                  FROM DEV_CLM.T_CASE_STATUS D
                                                 WHERE D.CODE = CC.CASE_STATUS) CASE_STATUS,
                                               CC.CASE_NO,
                                               CSC.CLAIM_DATE,
                                               (SELECT E.NAME
                                                  FROM DEV_CLM.T_CLAIM_NATURE E
                                                 WHERE E.CODE = CSC.ACC_REASON) ACC_REASON,
                                               CA.ACC_DATE,
                                               CA.ACC_DESC,
                                               (SELECT CL.NAME
                                                  FROM DEV_CLM.T_CASE_LEVEL CL
                                                 WHERE CC.CASE_FLAG = CL.CODE
                                                   AND ROWNUM = 1) AS CASE_FLAG_NAME,
                                               (SELECT R.NAME
                                                  FROM APP___CLM__DBUSER.T_CLAIM_TYPE R
                                                 WHERE R.CODE = CL.CLAIM_TYPE) AS CLAIM_NAME,
                                               CL.LIAB_NAME,
                                               (SELECT CUSTOMER_BIRTHDAY
                                                  FROM DEV_CLM.T_CUSTOMER TC
                                                 WHERE TC.CUSTOMER_ID =
                                                       CC.INSURED_ID) AS CUSTOMER_BIRTHDAY,
                                               (SELECT TG.GENDER_DESC
                                                  FROM DEV_CLM.T_GENDER TG
                                                 WHERE TG.GENDER_CODE =
                                                       (SELECT CUSTOMER_GENDER
                                                          FROM DEV_CLM.T_CUSTOMER TC
                                                         WHERE TC.CUSTOMER_ID =
                                                               CC.INSURED_ID)) AS GENDER_DESC,
                                               
                                               (CASE
                                                 WHEN CC.EASY_AUDIT_DECISION = 1 THEN
                                                  '通过'
                                                 ELSE
                                                  (SELECT CAD.NAME
                                                     FROM DEV_CLM.T_CLAIM_AUDIT_DECISION CAD
                                                    WHERE CAD.CODE =
                                                          CC.AUDIT_DECISION
                                                      AND ROWNUM = 1)
                                               END) AS AUDIT_DECISION,
                                               (CASE
                                                 WHEN CC.CASE_STATUS = '80' AND
                                                      CC.APPROVER_ID IS NULL AND
                                                      CC.AUDITOR_ID IS NOT NULL THEN
                                                  CC.AUDIT_TIME
                                                 ELSE
                                                  CC.APPROVE_TIME
                                               END) AS APPROVE_TIME,
                                               (SELECT CLD.NAME
                                                  FROM DEV_CLM.T_CLAIM_LIAB_DECISION CLD
                                                 WHERE CL.LIAB_CONCLUSION =
                                                       CLD.CODE
                                                   AND ROWNUM = 1) AS LIAB_CONCLUSION,
                                               (SELECT TCAR.NAME
                                                  FROM DEV_CLM.T_CLAIM_AUDIT_REJECT TCAR
                                                 WHERE TCAR.CODE =
                                                       SUBSTR(CL.REJECT_CODE,
                                                              0, 2)) REJECT_REASON,
                                               CC.CASE_ID
                                          FROM DEV_CLM.T_CLAIM_CASE     CC,
                                               DEV_CLM.T_CLAIM_SUB_CASE CSC,
                                               DEV_CLM.T_CLAIM_ACCIDENT CA,
                                               DEV_CLM.T_CLAIM_LIAB     CL
                                         WHERE CC.CASE_ID = CSC.CASE_ID
                                           AND CC.ACCIDENT_ID = CA.ACCIDENT_ID
                                           AND CC.CASE_ID = CL.CASE_ID) CLM) CLMUW
                         WHERE UM.BIZ_CODE = CLMUW.CASE_NO) CLMUWUW
                
                 WHERE CLMUWUW.UW_ID = UP.UW_ID
                   AND UP.POLICY_CODE = TM.POLICY_CODE
                   AND UP.UW_ID = A.UW_ID]]>
                   
	   	<if test=" channel_type != null and channel_type !=''"><![CDATA[ AND TM.CHANNEL_TYPE in (${channel_type})]]></if>
        <if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
        <if test=" validate_date_start != null and validate_date_start !=''"><![CDATA[ AND TM.VALIDATE_DATE >= to_date(#{validate_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" validate_date_end != null and validate_date_end !=''"><![CDATA[ AND TM.VALIDATE_DATE <= to_date(#{validate_date_end},'YYYY-MM-DD') ]]></if>
    	<if test=" claim_date_start != null and claim_date_start !=''"><![CDATA[ AND CLMUWUW.CLAIM_DATE >= to_date(#{claim_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" claim_date_end != null and claim_date_end !=''"><![CDATA[ AND CLMUWUW.CLAIM_DATE <= to_date(#{claim_date_end},'YYYY-MM-DD') ]]></if>
		)
	</select>
	<!-- 出险及赔付清单导出 -->
	<select id="exportClmAndPayment" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
		SELECT DISTINCT (SELECT A.DECISION_DESC
                                   FROM DEV_UW.T_POLICY_DECISION A
                                  WHERE A.DECISION_CODE = UP.POLICY_DECISION) POLICY_DECISION,
                                UP.UW_USER_ID,
                                (SELECT B.USER_NAME
                                   FROM DEV_PAS.T_UDMP_USER B
                                  WHERE B.USER_ID = UP.UW_USER_ID) USER_NAME,
                                UP.POLICY_CODE POLICY_CODE,
                                UP.ORGAN_CODE,
                                CLMUWUW.INSURED_ID,
                                TO_CHAR(CLMUWUW.RPTR_TIME, 'yyyy-mm-dd') RPTR_TIME,
                                CLMUWUW.CASE_STATUS,
                                CLMUWUW.CASE_NO,
                                TO_CHAR(CLMUWUW.CLAIM_DATE, 'yyyy-mm-dd') CLAIM_DATE,
                                CLMUWUW.ACC_REASON,
                                CLMUWUW.ROLE_TYPE,
                                TO_CHAR(TM.VALIDATE_DATE, 'yyyy-mm-dd') VALIDATE_DATE,
                                A.AMOUNT, --保额
                                A.BUSI_PROD_CODE, --险种编码
                                (SELECT BP.PRODUCT_NAME_STD
                                   FROM DEV_UW.T_BUSINESS_PRODUCT BP
                                  WHERE BP.PRODUCT_CODE_SYS = A.BUSI_PROD_CODE) AS PRODUCT_NAME_STD, --险种名称
                                (CASE
                                  WHEN ((SELECT BP.COVER_PERIOD_TYPE
                                           FROM DEV_PDS.T_BUSINESS_PRODUCT BP
                                          WHERE BP.PRODUCT_CODE_SYS =
                                                A.BUSI_PROD_CODE) = '0') THEN
                                   '长险'
                                  ELSE
                                   '短险'
                                END) AS BUSI_FLAG, --险种标识
                                (SELECT SC.SALES_CHANNEL_NAME
                                   FROM DEV_PAS.T_SALES_CHANNEL SC
                                  WHERE SC.SALES_CHANNEL_CODE =
                                        TM.CHANNEL_TYPE) CHANNEL_TYPE,
                                (SELECT A.ORGAN_NAME
                                   FROM DEV_PAS.T_UDMP_ORG A
                                  WHERE A.ORGAN_CODE = UP.ORGAN_CODE) ORGAN_NAME,
                                NVL((SELECT CASE
                                             WHEN POSITIVE_INDI = '0' THEN
                                              '阴性'
                                             WHEN POSITIVE_INDI = '1' THEN
                                              '阳性'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_PENOTICE
                                     WHERE UP.UW_ID = UW_ID),
                                    '') P_POSITIVE_INDI,
                                NVL((SELECT CASE
                                             WHEN POSITIVE_INDI = '0' THEN
                                              '阴性'
                                             WHEN POSITIVE_INDI = '1' THEN
                                              '阳性'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_SURVIVAL_INVESTIGATION
                                     WHERE UP.UW_ID = UW_ID),
                                    '') SI_POSITIVE_INDI,
                                NVL((SELECT CASE
                                             WHEN POLICY_CODE IS NULL THEN
                                              '否'
                                             WHEN POLICY_CODE IS NOT NULL THEN
                                              '是'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_PENOTICE
                                     WHERE UP.UW_ID = UW_ID),
                                    '否') IS_P,
                                NVL((SELECT CASE
                                             WHEN POLICY_CODE IS NULL THEN
                                              '否'
                                             WHEN POLICY_CODE IS NOT NULL THEN
                                              '是'
                                             ELSE
                                              '未知'
                                           END
                                      FROM DEV_UW.T_SURVIVAL_INVESTIGATION
                                     WHERE UP.UW_ID = UW_ID),
                                    '否') IS_SI,
                                TO_CHAR(CLMUWUW.ACC_DATE, 'yyyy-mm-dd') ACC_DATE, --事故日期
                                CLMUWUW.ACC_DESC, --事故描述
                                CLMUWUW.CASE_FLAG_NAME, --案件类型
                                CLMUWUW.CLAIM_NAME, --理赔类型
                                CLMUWUW.LIAB_NAME, --保项名称
                                CLMUWUW.GENDER_DESC, -- 性别
                                CLMUWUW.AUDIT_DECISION, --审核结论
                                TO_CHAR(CLMUWUW.APPROVE_TIME, 'yyyy-mm-dd') APPROVE_TIME, --审批通过日期
                                CLMUWUW.LIAB_CONCLUSION, --保项结论
                                TO_CHAR(TRUNC(MONTHS_BETWEEN(CLMUWUW.CLAIM_DATE,
                                                     CUSTOMER_BIRTHDAY) / 12)) AS AGE,
                                CLMUWUW.REJECT_REASON, --拒付原因
                                (SELECT A1.NAME
                                   FROM DEV_CLM.T_ACCIDENT1 A1
                                  WHERE A1.CODE =
                                        (SELECT R.ACC_RESULT1
                                           FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT R
                                          WHERE R.CASE_ID = CLMUWUW.CASE_ID
                                            AND ROWNUM = 1)
                                    AND ROWNUM = 1) AS ACC_NAME1, -- 出险结果1
                                (SELECT A2.NAME
                                   FROM DEV_CLM.T_ACCIDENT2 A2
                                  WHERE A2.CODE =
                                        (SELECT R.ACC_RESULT2
                                           FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT R
                                          WHERE R.CASE_ID = CLMUWUW.CASE_ID
                                            AND ROWNUM = 1)
                                    AND ROWNUM = 1) AS ACC_NAME2, --出险结果2
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 3 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 4))
                                END AS SECOND_ORGAN_CODE,
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 5 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 6))
                                END AS THIRD_ORGAN_CODE,
                                CASE
                                  WHEN LENGTH(UP.ORGAN_CODE) > 7 THEN
                                   (SELECT UO.ORGAN_NAME
                                      FROM DEV_PAS.T_UDMP_ORG UO
                                     WHERE UO.ORGAN_CODE =
                                           SUBSTR(UP.ORGAN_CODE, 1, 8))
                                END AS FOURTH_ORGAN_CODE,
                                (SELECT TCAGENT.AGENT_CODE
                                   FROM DEV_CLM.T_CONTRACT_AGENT TCAGENT
                                  WHERE TCAGENT.CASE_ID = CLMUWUW.CASE_ID
                                    AND TCAGENT.POLICY_CODE = TM.POLICY_CODE
                                    AND TCAGENT.CUR_FLAG = '1'
                                    AND ROWNUM = 1) AGENT_Code, --服务员代码
                                (SELECT TCAGENT.AGENT_ORGAN_CODE
                                   FROM DEV_CLM.T_CONTRACT_AGENT TCAGENT
                                  WHERE TCAGENT.CASE_ID = CLMUWUW.CASE_ID
                                    AND TCAGENT.POLICY_CODE = TM.POLICY_CODE
                                    AND TCAGENT.CUR_FLAG = '1'
                                    AND ROWNUM = 1) AGENT_ORGAN_CODE --服务员区部代码
                  FROM DEV_UW.T_UW_POLICY UP,
                       DEV_PAS.T_CONTRACT_MASTER TM,
                       DEV_UW.T_UW_PRODUCT A,
                       (SELECT *
                          FROM DEV_UW.T_UW_MASTER UM,
                               (SELECT CLM.*
                                  FROM (SELECT (SELECT C.CUSTOMER_NAME
                                                  FROM DEV_CLM.T_CUSTOMER C
                                                 WHERE C.CUSTOMER_ID =
                                                       CC.INSURED_ID) INSURED_ID,
                                               
                                               (SELECT CASE
                                                         WHEN COUNT(POLICY_CODE) IS NULL THEN
                                                          '投保人'
                                                         WHEN COUNT(POLICY_CODE) IS NOT NULL THEN
                                                          '被保人'
                                                       END
                                                  FROM DEV_CLM.T_INSURED_LIST,
                                                       DEV_CLM.T_CLAIM_CASE
                                                 WHERE DEV_CLM.T_CLAIM_CASE.INSURED_ID =
                                                       CUSTOMER_ID) ROLE_TYPE,
                                               CC.INSERT_TIME AS RPTR_TIME,
                                               (SELECT D.NAME
                                                  FROM DEV_CLM.T_CASE_STATUS D
                                                 WHERE D.CODE = CC.CASE_STATUS) CASE_STATUS,
                                               CC.CASE_NO,
                                               CSC.CLAIM_DATE,
                                               (SELECT E.NAME
                                                  FROM DEV_CLM.T_CLAIM_NATURE E
                                                 WHERE E.CODE = CSC.ACC_REASON) ACC_REASON,
                                               CA.ACC_DATE,
                                               CA.ACC_DESC,
                                               (SELECT CL.NAME
                                                  FROM DEV_CLM.T_CASE_LEVEL CL
                                                 WHERE CC.CASE_FLAG = CL.CODE
                                                   AND ROWNUM = 1) AS CASE_FLAG_NAME,
                                               (SELECT R.NAME
                                                  FROM APP___CLM__DBUSER.T_CLAIM_TYPE R
                                                 WHERE R.CODE = CL.CLAIM_TYPE) AS CLAIM_NAME,
                                               CL.LIAB_NAME,
                                               (SELECT CUSTOMER_BIRTHDAY
                                                  FROM DEV_CLM.T_CUSTOMER TC
                                                 WHERE TC.CUSTOMER_ID =
                                                       CC.INSURED_ID) AS CUSTOMER_BIRTHDAY,
                                               (SELECT TG.GENDER_DESC
                                                  FROM DEV_CLM.T_GENDER TG
                                                 WHERE TG.GENDER_CODE =
                                                       (SELECT CUSTOMER_GENDER
                                                          FROM DEV_CLM.T_CUSTOMER TC
                                                         WHERE TC.CUSTOMER_ID =
                                                               CC.INSURED_ID)) AS GENDER_DESC,
                                               
                                               (CASE
                                                 WHEN CC.EASY_AUDIT_DECISION = 1 THEN
                                                  '通过'
                                                 ELSE
                                                  (SELECT CAD.NAME
                                                     FROM DEV_CLM.T_CLAIM_AUDIT_DECISION CAD
                                                    WHERE CAD.CODE =
                                                          CC.AUDIT_DECISION
                                                      AND ROWNUM = 1)
                                               END) AS AUDIT_DECISION,
                                               (CASE
                                                 WHEN CC.CASE_STATUS = '80' AND
                                                      CC.APPROVER_ID IS NULL AND
                                                      CC.AUDITOR_ID IS NOT NULL THEN
                                                  CC.AUDIT_TIME
                                                 ELSE
                                                  CC.APPROVE_TIME
                                               END) AS APPROVE_TIME,
                                               (SELECT CLD.NAME
                                                  FROM DEV_CLM.T_CLAIM_LIAB_DECISION CLD
                                                 WHERE CL.LIAB_CONCLUSION =
                                                       CLD.CODE
                                                   AND ROWNUM = 1) AS LIAB_CONCLUSION,
                                               (SELECT TCAR.NAME
                                                  FROM DEV_CLM.T_CLAIM_AUDIT_REJECT TCAR
                                                 WHERE TCAR.CODE =
                                                       SUBSTR(CL.REJECT_CODE,
                                                              0, 2)) REJECT_REASON,
                                               CC.CASE_ID
                                          FROM DEV_CLM.T_CLAIM_CASE     CC,
                                               DEV_CLM.T_CLAIM_SUB_CASE CSC,
                                               DEV_CLM.T_CLAIM_ACCIDENT CA,
                                               DEV_CLM.T_CLAIM_LIAB     CL
                                         WHERE CC.CASE_ID = CSC.CASE_ID
                                           AND CC.ACCIDENT_ID = CA.ACCIDENT_ID
                                           AND CC.CASE_ID = CL.CASE_ID) CLM) CLMUW
                         WHERE UM.BIZ_CODE = CLMUW.CASE_NO) CLMUWUW
                
                 WHERE CLMUWUW.UW_ID = UP.UW_ID
                   AND UP.POLICY_CODE = TM.POLICY_CODE
                   AND UP.UW_ID = A.UW_ID]]>
                   
	   	<if test=" channel_type != null and channel_type !=''"><![CDATA[ AND TM.CHANNEL_TYPE in (${channel_type})]]></if>
        <if test=" organ_code != null and organ_code !=''"><![CDATA[AND UP.ORGAN_CODE like #{organ_code}||'%' ]]></if>
        <if test=" validate_date_start != null and validate_date_start !=''"><![CDATA[ AND TM.VALIDATE_DATE >= to_date(#{validate_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" validate_date_end != null and validate_date_end !=''"><![CDATA[ AND TM.VALIDATE_DATE <= to_date(#{validate_date_end},'YYYY-MM-DD') ]]></if>
    	<if test=" claim_date_start != null and claim_date_start !=''"><![CDATA[ AND CLMUWUW.CLAIM_DATE >= to_date(#{claim_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" claim_date_end != null and claim_date_end !=''"><![CDATA[ AND CLMUWUW.CLAIM_DATE <= to_date(#{claim_date_end},'YYYY-MM-DD') ]]></if>
	
	</select>
	
	<select id="queryUwCsAutomatic" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	SELECT *
  	FROM (SELECT RN, BIZ_CODE, POLICY_CODE, SERVICE_CODE, DECLI_DATE, APPLY_TIME
          FROM (SELECT ROWNUM AS RN,
                       M.BIZ_CODE,
                       Y.POLICY_CODE,
                       M.SERVICE_CODE,
                       TO_CHAR(Y.UPDATE_TIME, 'YYYY-MM-DD') DECLI_DATE,
                       TO_CHAR(M.BIZ_DATE, 'YYYY-MM-DD') APPLY_TIME
                  FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_POLICY Y
                 WHERE M.UW_ID = Y.UW_ID
                   AND M.UW_SOURCE_TYPE = '2'
                   AND M.SERVICE_CODE in (${service_code})
                   AND M.UW_CANCEL_CAUSE in ('RE','HI')]]>
                   
        <if test=" biz_date_start != null and biz_date_start !=''"><![CDATA[ AND M.BIZ_DATE >= to_date(#{biz_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" biz_date_end != null and biz_date_end !=''"><![CDATA[ AND M.BIZ_DATE <= to_date(#{biz_date_end},'YYYY-MM-DD') ]]></if>
       	<if test=" organ_code != null and organ_code !=''"><![CDATA[AND Y.ORGAN_CODE like #{organ_code}||'%' ]]></if>                
        <![CDATA[) WHERE ROWNUM <= #{LESS_NUM} ) A WHERE A.RN > #{GREATER_NUM}]]>
	</select>
	<select id="queryUwCsAutomaticTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		SELECT COUNT(*)
	  FROM (SELECT 
	               M.BIZ_CODE,
	               Y.POLICY_CODE,
	               TO_CHAR(Y.UPDATE_TIME, 'YYYY-MM-DD') DECLI_DATE,
	               TO_CHAR(M.BIZ_DATE, 'YYYY-MM-DD') APPLY_TIME
	          FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_POLICY Y
	         WHERE M.UW_ID = Y.UW_ID
	           AND M.UW_SOURCE_TYPE = '2'
	           AND M.SERVICE_CODE in (${service_code})
               AND M.UW_CANCEL_CAUSE in ('RE','HI')
	    <if test=" biz_date_start != null and biz_date_start !=''"><![CDATA[ AND M.BIZ_DATE >= to_date(#{biz_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" biz_date_end != null and biz_date_end !=''"><![CDATA[ AND M.BIZ_DATE <= to_date(#{biz_date_end},'YYYY-MM-DD') ]]></if>
       	<if test=" organ_code != null and organ_code !=''"><![CDATA[AND Y.ORGAN_CODE like #{organ_code}||'%' ]]></if>)
	</select>
	
	<select id="exportUwCsAutomatic" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	SELECT RN, BIZ_CODE, POLICY_CODE, SERVICE_CODE, DECLI_DATE, APPLY_TIME
          FROM (SELECT ROWNUM AS RN,
                       M.BIZ_CODE,
                       Y.POLICY_CODE,
                       M.SERVICE_CODE,
                       TO_CHAR(Y.UPDATE_TIME, 'YYYY-MM-DD') DECLI_DATE,
                       TO_CHAR(M.BIZ_DATE, 'YYYY-MM-DD') APPLY_TIME
                  FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_POLICY Y
                 WHERE M.UW_ID = Y.UW_ID
                   AND M.UW_SOURCE_TYPE = '2'
                   AND M.SERVICE_CODE in (${service_code})
                   AND M.UW_CANCEL_CAUSE in ('RE','HI')]]>
                   
        <if test=" biz_date_start != null and biz_date_start !=''"><![CDATA[ AND M.BIZ_DATE >= to_date(#{biz_date_start},'YYYY-MM-DD') ]]></if>
		<if test=" biz_date_end != null and biz_date_end !=''"><![CDATA[ AND M.BIZ_DATE <= to_date(#{biz_date_end},'YYYY-MM-DD') ]]></if>
       	<if test=" organ_code != null and organ_code !=''"><![CDATA[AND Y.ORGAN_CODE like #{organ_code}||'%' ]]></if>                
        <![CDATA[)]]>
	</select>
	
	<select id="queryUwHospitalMessage" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[SELECT * FROM (SELECT ROWNUM AS RN,A.* FROM (SELECT  ORGAN_CODE,ORGAN_NAME,HOSPITAL_CODE,HOSPITAL_NAME,HOSPITAL_LEVEL,HOSPITAL_STATUS,PHYSICAL_NAME,PROTOCOL_FEE,
         PEITEM_TYPE FROM(SELECT A2.ORGAN_CODE,(SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = A2.ORGAN_CODE) ORGAN_NAME,
          A2.HOSPITAL_CODE, A2.HOSPITAL_NAME, (SELECT HL.NAME FROM DEV_UW.T_HOSPITAL_LEVEL HL WHERE HL.CODE = A2.HOSPITAL_LEVEL) AS HOSPITAL_LEVEL,
          (SELECT HS.NAME FROM DEV_UW.T_HOSPITAL_STATUS HS WHERE HS.CODE = A1.HOSPITAL_STATUS) AS HOSPITAL_STATUS,]]>
               <![CDATA[CASE
                 WHEN A3.PEITEM_TYPE = '1' THEN
                  (SELECT A4.PEITEM_NAME
                     FROM DEV_UW.T_PHYSICAL_ITEM A4
                    WHERE A3.PEITEM_CODE = A4.PEITEM_CODE)
                 ELSE
                  (SELECT A5.PESET_NAME
                     FROM DEV_UW.T_PHYSICAL_SET A5
                    WHERE A5.PESET_ID = A3.PESET_ID)
               END PHYSICAL_NAME, ]]>
              <![CDATA[ A3.PROTOCOL_FEE,
               A3.PEITEM_TYPE ]]>
        
          <![CDATA[FROM DEV_UW.T_UW_HOSPITAL A1
          JOIN DEV_UW.T_HOSPITAL A2
            ON A1.HOSPITAL_CODE = A2.HOSPITAL_CODE
          LEFT JOIN DEV_UW.T_BODY_EXAM_CHARG_CONF A3
            ON A3.UW_HOSPITAL_ID = A1.UW_HOSPITAL_ID]]>
            ) B
         <![CDATA[WHERE ROWNUM <=  #{LESS_NUM} ]]>
         	<if test=" organ_code != null and organ_code !=''"><![CDATA[AND B.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         	<if test=" hospital_code != null and hospital_code !=''"><![CDATA[AND B.HOSPITAL_CODE LIKE #{hospital_code}||'%' ]]></if>
         	ORDER BY B.ORGAN_CODE,B.HOSPITAL_CODE
           <![CDATA[ ) A ) D WHERE D.RN > #{GREATER_NUM}]]>
	</select>
	
	<select id="queryUwHospitalMessageTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(*) FROM (SELECT RN, ORGAN_CODE, ORGAN_NAME, HOSPITAL_CODE, HOSPITAL_NAME, HOSPITAL_LEVEL, HOSPITAL_STATUS,
               PHYSICAL_NAME, PROTOCOL_FEE, PEITEM_TYPE FROM (SELECT ROWNUM AS RN, A2.ORGAN_CODE,
               (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = A2.ORGAN_CODE) ORGAN_NAME,
               A2.HOSPITAL_CODE, A2.HOSPITAL_NAME,
               (SELECT HL.NAME FROM DEV_UW.T_HOSPITAL_LEVEL HL WHERE HL.CODE = A2.HOSPITAL_LEVEL) AS HOSPITAL_LEVEL,
               (SELECT HS.NAME FROM DEV_UW.T_HOSPITAL_STATUS HS WHERE HS.CODE = A1.HOSPITAL_STATUS) AS HOSPITAL_STATUS,]]>
              <![CDATA[ CASE
                 WHEN A3.PEITEM_TYPE = '1' THEN
                  (SELECT A4.PEITEM_NAME
                     FROM DEV_UW.T_PHYSICAL_ITEM A4
                    WHERE A3.PEITEM_CODE = A4.PEITEM_CODE)
                 ELSE
                  (SELECT A5.PESET_NAME
                     FROM DEV_UW.T_PHYSICAL_SET A5
                    WHERE A5.PESET_ID = A3.PESET_ID)
               END PHYSICAL_NAME,
               A3.PROTOCOL_FEE,
               A3.PEITEM_TYPE]]>
        
         <![CDATA[ FROM DEV_UW.T_UW_HOSPITAL A1
          JOIN DEV_UW.T_HOSPITAL A2
            ON A1.HOSPITAL_CODE = A2.HOSPITAL_CODE
          LEFT JOIN DEV_UW.T_BODY_EXAM_CHARG_CONF A3
            ON A3.UW_HOSPITAL_ID = A1.UW_HOSPITAL_ID]]>
            ) B
         WHERE 1 = 1
           <if test=" organ_code != null and organ_code !=''"><![CDATA[AND B.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
           <if test=" hospital_code != null and hospital_code !=''"><![CDATA[AND B.HOSPITAL_CODE LIKE #{hospital_code}||'%' ]]></if>
        <![CDATA[ ) A]]>
	</select>
	
	<select id="exportUwHospitalMessage" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[SELECT ROWNUM AS RN,A.* FROM (SELECT ORGAN_CODE,ORGAN_NAME,HOSPITAL_CODE,HOSPITAL_NAME,HOSPITAL_LEVEL,HOSPITAL_STATUS,PHYSICAL_NAME,PROTOCOL_FEE,
         PEITEM_TYPE FROM(SELECT A2.ORGAN_CODE,(SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = A2.ORGAN_CODE) ORGAN_NAME,
          A2.HOSPITAL_CODE, A2.HOSPITAL_NAME, (SELECT HL.NAME FROM DEV_UW.T_HOSPITAL_LEVEL HL WHERE HL.CODE = A2.HOSPITAL_LEVEL) AS HOSPITAL_LEVEL,
          (SELECT HS.NAME FROM DEV_UW.T_HOSPITAL_STATUS HS WHERE HS.CODE = A1.HOSPITAL_STATUS) AS HOSPITAL_STATUS,]]>
               <![CDATA[CASE
                 WHEN A3.PEITEM_TYPE = '1' THEN
                  (SELECT A4.PEITEM_NAME
                     FROM DEV_UW.T_PHYSICAL_ITEM A4
                    WHERE A3.PEITEM_CODE = A4.PEITEM_CODE)
                 ELSE
                  (SELECT A5.PESET_NAME
                     FROM DEV_UW.T_PHYSICAL_SET A5
                    WHERE A5.PESET_ID = A3.PESET_ID)
               END PHYSICAL_NAME, ]]>
              <![CDATA[ A3.PROTOCOL_FEE,
               A3.PEITEM_TYPE ]]>
        
          <![CDATA[FROM DEV_UW.T_UW_HOSPITAL A1
          JOIN DEV_UW.T_HOSPITAL A2
            ON A1.HOSPITAL_CODE = A2.HOSPITAL_CODE
          LEFT JOIN DEV_UW.T_BODY_EXAM_CHARG_CONF A3
            ON A3.UW_HOSPITAL_ID = A1.UW_HOSPITAL_ID]]>
            ) B
         <![CDATA[WHERE 1 = 1]]>
         	<if test=" organ_code != null and organ_code !=''"><![CDATA[AND B.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         	<if test=" hospital_code != null and hospital_code !=''"><![CDATA[AND B.HOSPITAL_CODE LIKE #{hospital_code}||'%' ]]></if>
         	ORDER BY B.ORGAN_CODE,B.HOSPITAL_CODE
           <![CDATA[ ) A]]>
	</select>
	
	<select id="queryPhysicalMessage" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT  * FROM (SELECT  ROWNUM AS RN,B.* FROM   (SELECT DISTINCT TP.PENOTICE_ID,
        TP.DOCUMENT_NO, TD.ORGAN_CODE, TP.APPLY_CODE, TP.RESULT_AGE,]]>
            <![CDATA[TH.HOSPITAL_CODE,
              (SELECT H.HOSPITAL_NAME FROM DEV_UW.T_HOSPITAL H WHERE H.HOSPITAL_CODE = TH.HOSPITAL_CODE ) HOSPITAL_NAME,
              TO_CHAR(TD.SEND_TIME,'YYYY-MM-DD') AS SEND_TIME, TD.SEND_TIME AS SEND_TIME_T, TP.POLICY_CODE,
              TO_CHAR(TP.RESULT_PE_DATE,'YYYY-MM-DD') AS RESULT_PE_DATE, TO_CHAR(TD.CLOSE_TIME,'YYYY-MM-DD') AS CLOSE_TIME, 
              (SELECT TO_CHAR(WM_CONCAT(PI.PEITEM_NAME)) FROM DEV_UW.T_PENOTICE_DETAIL PD JOIN DEV_UW.T_PHYSICAL_ITEM PI
			ON PD.PEITEM_CODE = PI.PEITEM_CODE WHERE PD.PENOTICE_ID = TP.PENOTICE_ID) AS PHYSICAL_ITEM, PP.UW_SOURCE_TYPE,
              TP.POSITIVE_INDI,TP.PE_FEE,TP.IS_PHYSICAL,PP.POLICY_DECISION,TO_CHAR(PM.ISSUE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS ISSUE_DATE ]]>
         <![CDATA[ FROM DEV_UW.T_PENOTICE TP
            JOIN DEV_NB.T_DOCUMENT TD
              ON TD.DOC_LIST_ID = TP.DOC_LIST_ID
             AND TD.BUSS_SOURCE_CODE = '002'
             AND TD.TEMPLATE_CODE = 'UWS_00003'
            JOIN DEV_UW.T_UW_POLICY PP
              ON TP.UW_ID = PP.UW_ID
              AND PP.APPLY_CODE = TP.APPLY_CODE]]>
            <if test=" uw_source_type != null and uw_source_type !='' "><![CDATA[AND PP.UW_SOURCE_TYPE =#{uw_source_type} ]]></if>
            <![CDATA[JOIN DEV_NB.T_NB_CONTRACT_MASTER PM
              ON TP.APPLY_CODE = PM.APPLY_CODE 
          LEFT JOIN DEV_UW.T_UW_HOSPITAL TH
          		ON  TH.UW_HOSPITAL_ID = TP.UW_HOSPITAL_ID) B WHERE ROWNUM <= #{LESS_NUM} ]]>
          <if test=" organ_code != null and organ_code !=''"><![CDATA[AND B.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
          <if test=" send_time_start  != null  and  send_time_start  != ''  "><![CDATA[ AND B.SEND_TIME_T >= TO_DATE(#{send_time_start} ||' 00:00:00','yyyy-MM-dd HH24:MI:SS') ]]></if>
		  <if test=" send_time_end  != null  and  send_time_end  != ''  "><![CDATA[ AND B.SEND_TIME_T <= TO_DATE(#{send_time_end} ||' 23:59:59','yyyy-MM-dd HH24:MI:SS') ]]></if>
          <if test=" hospital_code != null and hospital_code !=''"><![CDATA[AND B.HOSPITAL_CODE LIKE '%${hospital_code}%' ]]></if>
          <if test=" hospital_name != null and hospital_name !=''"><![CDATA[AND B.HOSPITAL_NAME LIKE '%${hospital_name}%' ]]></if>
          <![CDATA[ORDER BY B.ORGAN_CODE,B.HOSPITAL_CODE,B.SEND_TIME ) F WHERE F.RN > #{GREATER_NUM}]]>
	</select>
	
	<select id="queryPhysicalMessageTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT count(*) FROM (SELECT  ROWNUM AS RN,B.* FROM   (SELECT DISTINCT TP.PENOTICE_ID,
        TP.DOCUMENT_NO, TD.ORGAN_CODE, TP.APPLY_CODE,TP.RESULT_AGE,]]>
            <![CDATA[ TH.HOSPITAL_CODE,
           TD.SEND_TIME AS SEND_TIME_T, TO_CHAR(TP.RESULT_PE_DATE,'YYYY-MM-DD') AS RESULT_PE_DATE, 
              TO_CHAR(TD.CLOSE_TIME,'YYYY-MM-DD') AS CLOSE_TIME,
              TP.POSITIVE_INDI ]]>
         <![CDATA[FROM DEV_UW.T_PENOTICE TP
           JOIN DEV_NB.T_DOCUMENT TD
               ON TD.DOC_LIST_ID = TP.DOC_LIST_ID
              AND TD.BUSS_SOURCE_CODE = '002'
              AND TD.TEMPLATE_CODE = 'UWS_00003'
             JOIN DEV_UW.T_UW_POLICY PP
               ON TP.UW_ID = PP.UW_ID
               AND PP.APPLY_CODE = TP.APPLY_CODE]]>
            <if test=" uw_source_type != null and uw_source_type !='' "><![CDATA[AND PP.UW_SOURCE_TYPE =#{uw_source_type} ]]></if>
            <![CDATA[JOIN DEV_NB.T_NB_CONTRACT_MASTER PM
               ON TP.APPLY_CODE = PM.APPLY_CODE 
          LEFT JOIN DEV_UW.T_UW_HOSPITAL TH
          ON  TH.UW_HOSPITAL_ID = TP.UW_HOSPITAL_ID) B WHERE 1 = 1 ]]>
          <if test=" organ_code != null and organ_code !=''"><![CDATA[AND B.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
          <if test=" send_time_start  != null  and  send_time_start  != ''  "><![CDATA[ AND B.SEND_TIME_T >= TO_DATE(#{send_time_start} ||' 00:00:00','yyyy-MM-dd HH24:MI:SS') ]]></if>
		  <if test=" send_time_end  != null  and  send_time_end  != ''  "><![CDATA[ AND B.SEND_TIME_T <= TO_DATE(#{send_time_end} ||' 23:59:59','yyyy-MM-dd HH24:MI:SS') ]]></if>
          <if test=" hospital_code != null and hospital_code !=''"><![CDATA[AND B.HOSPITAL_CODE LIKE '%${hospital_code}%' ]]></if>
          <if test=" hospital_name != null and hospital_name !=''"><![CDATA[AND B.HOSPITAL_NAME LIKE '%${hospital_name}%' ]]></if>
          <![CDATA[) F]]>
	</select>
	
	<select id="exportPhysicalMessage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT  ROWNUM AS RN,B.* FROM   (SELECT DISTINCT TP.PENOTICE_ID,
        TP.DOCUMENT_NO, TD.ORGAN_CODE, TP.APPLY_CODE,  TP.RESULT_AGE,
        (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE TD.ORGAN_CODE = ORGAN_CODE) AS ORGAN_NAME,]]>
            <![CDATA[ TH.HOSPITAL_CODE,
              (SELECT H.HOSPITAL_NAME FROM DEV_UW.T_HOSPITAL H WHERE H.HOSPITAL_CODE = TH.HOSPITAL_CODE ) HOSPITAL_NAME,
              TO_CHAR(TD.SEND_TIME,'YYYY-MM-DD') AS SEND_TIME, TD.SEND_TIME AS SEND_TIME_T, TP.POLICY_CODE,
              TO_CHAR(TP.RESULT_PE_DATE,'YYYY-MM-DD') AS RESULT_PE_DATE, TO_CHAR(TD.CLOSE_TIME,'YYYY-MM-DD') AS CLOSE_TIME, 
              (SELECT TO_CHAR(WM_CONCAT(PI.PEITEM_NAME)) FROM DEV_UW.T_PENOTICE_DETAIL PD JOIN DEV_UW.T_PHYSICAL_ITEM PI
			ON PD.PEITEM_CODE = PI.PEITEM_CODE WHERE PD.PENOTICE_ID = TP.PENOTICE_ID) AS PHYSICAL_ITEM,
              TP.POSITIVE_INDI,TP.PE_FEE,TP.IS_PHYSICAL, PP.UW_SOURCE_TYPE,PP.POLICY_DECISION,TO_CHAR(PM.ISSUE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS ISSUE_DATE]]>
         <![CDATA[FROM DEV_UW.T_PENOTICE TP
           JOIN DEV_NB.T_DOCUMENT TD
               ON TD.DOC_LIST_ID = TP.DOC_LIST_ID
              AND TD.BUSS_SOURCE_CODE = '002'
              AND TD.TEMPLATE_CODE = 'UWS_00003'
             JOIN DEV_UW.T_UW_POLICY PP
               ON TP.UW_ID = PP.UW_ID
               AND PP.APPLY_CODE = TP.APPLY_CODE]]>
            <if test=" uw_source_type != null and uw_source_type !='' "><![CDATA[AND PP.UW_SOURCE_TYPE =#{uw_source_type} ]]></if>
            <![CDATA[JOIN DEV_NB.T_NB_CONTRACT_MASTER PM
               ON TP.APPLY_CODE = PM.APPLY_CODE 
          LEFT JOIN DEV_UW.T_UW_HOSPITAL TH
          ON  TH.UW_HOSPITAL_ID = TP.UW_HOSPITAL_ID) B WHERE 1 = 1 ]]>
          <if test=" organ_code != null and organ_code !=''"><![CDATA[AND B.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
          <if test=" send_time_start  != null  and  send_time_start  != ''  "><![CDATA[ AND B.SEND_TIME >= #{send_time_start} ]]></if>
		  <if test=" send_time_end  != null  and  send_time_end  != ''  "><![CDATA[ AND B.SEND_TIME <= #{send_time_end} ]]></if>
          <if test=" hospital_code != null and hospital_code !=''"><![CDATA[AND B.HOSPITAL_CODE LIKE '%${hospital_code}%' ]]></if>
          <if test=" hospital_name != null and hospital_name !=''"><![CDATA[AND B.HOSPITAL_NAME LIKE '%${hospital_name}%' ]]></if>
          <![CDATA[ORDER BY B.ORGAN_CODE,B.HOSPITAL_CODE,B.SEND_TIME ]]>
	</select>
		<!--Modify zhanghui #82124: 福建分公司新契约非标核保结论业务的表单自助提数功能  begin-->
	<select id="querySpecialFocus" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT *
  FROM (SELECT ROWNUM AS RN, K.*
          FROM (SELECT DISTINCT F.UW_ID,
                                F.BIZ_CODE,
                                F.ORGAN_NAME,
                                F.INSURED_NAME,
                                F.CUSTOMER_CERTI_CODE,
                                F.ISSUE_DATE,
                                F.UWRESULT,/* 上报原因*/
                                DECODE(F.UWRESULT,
                                       '条件承保',
                                       (SELECT LISTAGG(RC.REASON_CONTENT, ';') WITHIN GROUP(ORDER BY RC.UW_ID) AS PRO
                                          FROM DEV_UW.T_UW_DECISION_REASON RC
                                         WHERE RC.UW_ID = F.UW_ID),
                                       F.REASON_CONTENT) REASON_CONTENT,/*核保依据*/
                                F.RISK_TYPE,
                                F.AGENT_NAME,
                                F.LICENSE_NO,
                                F.AGENT_CODE
                  FROM (SELECT M.UW_ID,
                               M.BIZ_CODE,
                               (SELECT SUBSTR(ORG.ORGAN_NAME, 0, 2)
                                  FROM DEV_NB.T_UDMP_ORG ORG
                                 WHERE ORG.ORGAN_CODE =
                                       SUBSTR(P.ORGAN_CODE, 0, 6)) ORGAN_NAME, /* 受理机构*/
                               C.CUSTOMER_NAME INSURED_NAME, /* 被保险人姓名*/
                               C.CUSTOMER_CERTI_CODE, /*身份证号*/ /*CM.ISSUE_DATE 承保时间,*/
                               TO_CHAR(M.UW_FINISH_TIME, 'YYYY-MM-DD') AS ISSUE_DATE, /* 核保日期*/
                               R.REASON_CONTENT, /*核保依据*/
                               (CASE
                                 WHEN BP.PRODUCT_CATEGORY2 = '30001' OR
                                      BP.PRODUCT_CATEGORY2 = '30004'
                                       THEN
                                  '寿险'
                                 WHEN BP.PRODUCT_CATEGORY2 = '30003'  THEN
                                  '健康险'
                                 WHEN BP.PRODUCT_CATEGORY2 = '30002'  THEN
                                  '意外险'
                               END) RISK_TYPE, /* 险种类型*/                             
                               (CASE WHEN P.POLICY_DECISION ='20' THEN '条件承保'
                               WHEN P.POLICY_DECISION ='40' THEN '延期承保'
                               WHEN P.POLICY_DECISION ='50' THEN '拒保'
                               END)UWRESULT, /* 上报原因*/
                               (SELECT TA.AGENT_NAME
                                  FROM DEV_NB.T_AGENT TA
                                 WHERE TA.AGENT_CODE = CA.AGENT_CODE) AGENT_NAME, /* 业务员姓名*/
                               (SELECT MAX(D.LICENSE_NO)
                                  FROM (SELECT D.LIST_ID,
                                               D.AGENT_CODE,
                                               D.LICENSE_NO,
                                               D.LICENSE_TYPE,
                                               D.LICENSE_START_DATE,
                                               D.LICENSE_END_DATE,
                                               D.STATE
                                          FROM (SELECT A.AGENT_CODE,
                                                       A.LICENSE_TYPE,
                                                       MAX(A.LICENSE_END_DATE) DATEE
                                                  FROM DEV_NB.T_AGENT_LICENSE A
                                                 GROUP BY A.LICENSE_TYPE,
                                                          A.AGENT_CODE) A
                                          LEFT JOIN DEV_NB.T_AGENT_LICENSE D
                                            ON A.LICENSE_TYPE = D.LICENSE_TYPE
                                           AND A.LICENSE_TYPE = '1'
                                           AND A.AGENT_CODE = D.AGENT_CODE
                                           AND A.DATEE = D.LICENSE_END_DATE) D
                                 WHERE CA.AGENT_CODE = D.AGENT_CODE) LICENSE_NO, /* 代理资格证号*/
                               CA.AGENT_CODE /*业务员编码*/
                          FROM DEV_UW.T_UW_MASTER M
                          LEFT JOIN DEV_UW.T_UW_POLICY P
                            ON P.UW_ID = M.UW_ID
                          LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CM
                            ON CM.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_NB.T_NB_INSURED_LIST L
                            ON L.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_NB.T_CUSTOMER C
                            ON C.CUSTOMER_ID = L.CUSTOMER_ID
                          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
                            ON CA.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_UW.T_UW_BUSI_PROD UBP
                            ON UBP.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_UW.T_UW_PRODUCT UPT
                            ON UPT.UW_ID = M.UW_ID
                           AND UPT.UW_BUSI_ID = UBP.UW_BUSI_ID
                          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                            ON BP.PRODUCT_CODE_SYS = UPT.BUSI_PROD_CODE                         
                          LEFT JOIN DEV_UW.T_UW_DECISION_REASON R
                            ON R.UW_ID = M.UW_ID
                           AND R.UW_PRD_ID = UPT.UW_PRD_ID
                         WHERE 1 = 1  
                         AND (L.ORDER_ID = 1 or L.ORDER_ID is null)
                         AND BP.PRODUCT_CATEGORY = '10001' ]]>
          <if test=" organ_code != null and organ_code !=''"><![CDATA[AND P.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
          <if test=" send_time_start  != null  and  send_time_start  != ''  "><![CDATA[ AND TRUNC(M.UW_FINISH_TIME) >= to_date(#{send_time_start},'yyyy-mm-dd') ]]></if>
		  <if test=" send_time_end  != null  and  send_time_end  != ''  "><![CDATA[ AND TRUNC(M.UW_FINISH_TIME) <= to_date(#{send_time_end},'yyyy-mm-dd') ]]></if>
          <if test=" uwresult != null and uwresult !='' "><![CDATA[AND P.POLICY_DECISION = #{uwresult} ]]></if>
          <if test=" uwresult == null or uwresult =='' "><![CDATA[  AND P.POLICY_DECISION IN ('20', '40', '50') ]]></if>          
          <![CDATA[ORDER BY M.UW_FINISH_TIME ) F )K) TOTD
		 WHERE 1=1
		 AND TOTD.RN > #{GREATER_NUM} AND TOTD.RN<= #{LESS_NUM}]]>
	</select>
	
	<select id="querySpecialFocusTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1) FROM (SELECT ROWNUM AS RN, K.*
          FROM (SELECT DISTINCT F.UW_ID
                  FROM (SELECT M.UW_ID
                          FROM DEV_UW.T_UW_MASTER M
                          LEFT JOIN DEV_UW.T_UW_POLICY P
                            ON P.UW_ID = M.UW_ID
                          LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CM
                            ON CM.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_NB.T_NB_INSURED_LIST L
                            ON L.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_NB.T_CUSTOMER C
                            ON C.CUSTOMER_ID = L.CUSTOMER_ID
                          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
                            ON CA.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_UW.T_UW_BUSI_PROD UBP
                            ON UBP.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_UW.T_UW_PRODUCT UPT
                            ON UPT.UW_ID = M.UW_ID
                           AND UPT.UW_BUSI_ID = UBP.UW_BUSI_ID
                          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                            ON BP.PRODUCT_CODE_SYS = UPT.BUSI_PROD_CODE                         
                          LEFT JOIN DEV_UW.T_UW_DECISION_REASON R
                            ON R.UW_ID = M.UW_ID
                           AND R.UW_PRD_ID = UPT.UW_PRD_ID
                         WHERE 1 = 1  
                         AND (L.ORDER_ID = 1 or L.ORDER_ID is null)
                         AND BP.PRODUCT_CATEGORY = '10001' ]]>
          <if test=" organ_code != null and organ_code !=''"><![CDATA[AND P.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
          <if test=" send_time_start  != null  and  send_time_start  != ''  "><![CDATA[ AND TRUNC(M.UW_FINISH_TIME) >= to_date(#{send_time_start},'yyyy-mm-dd') ]]></if>
		  <if test=" send_time_end  != null  and  send_time_end  != ''  "><![CDATA[ AND TRUNC(M.UW_FINISH_TIME) <= to_date(#{send_time_end},'yyyy-mm-dd') ]]></if>
          <if test=" uwresult != null and uwresult !='' "><![CDATA[ AND P.POLICY_DECISION = #{uwresult} ]]></if>
          <if test=" uwresult == null or uwresult =='' "><![CDATA[ AND P.POLICY_DECISION IN ('20', '40', '50') ]]></if>
          <![CDATA[ORDER BY M.UW_FINISH_TIME ) F ) K) ]]>
	</select>
	
	<select id="exportSpecialFocus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT ROWNUM AS RN, K.*
          FROM (SELECT DISTINCT F.UW_ID,
                                F.BIZ_CODE,
                                F.ORGAN_NAME,
                                F.INSURED_NAME,
                                F.CUSTOMER_CERTI_CODE,
                                F.ISSUE_DATE,
                                F.UWRESULT,/* 上报原因*/
                                DECODE(F.UWRESULT,
                                       '条件承保',
                                       (SELECT LISTAGG(RC.REASON_CONTENT, ';') WITHIN GROUP(ORDER BY RC.UW_ID) AS PRO
                                          FROM DEV_UW.T_UW_DECISION_REASON RC
                                         WHERE RC.UW_ID = F.UW_ID),
                                       F.REASON_CONTENT) REASON_CONTENT,/*核保依据*/
                                F.RISK_TYPE,
                                F.AGENT_NAME,
                                F.LICENSE_NO,
                                F.AGENT_CODE
                  FROM (SELECT M.UW_ID,
                               M.BIZ_CODE,
                               (SELECT SUBSTR(ORG.ORGAN_NAME, 0, 2)
                                  FROM DEV_NB.T_UDMP_ORG ORG
                                 WHERE ORG.ORGAN_CODE =
                                       SUBSTR(P.ORGAN_CODE, 0, 6)) ORGAN_NAME, /* 受理机构*/
                               C.CUSTOMER_NAME INSURED_NAME, /* 被保险人姓名*/
                               C.CUSTOMER_CERTI_CODE, /*身份证号*/ /*CM.ISSUE_DATE 承保时间,*/
                               TO_CHAR(M.UW_FINISH_TIME, 'YYYY-MM-DD') AS ISSUE_DATE, /* 核保日期*/
                               R.REASON_CONTENT, /*核保依据*/
                               (CASE
                                 WHEN BP.PRODUCT_CATEGORY2 = '30001' OR
                                      BP.PRODUCT_CATEGORY2 = '30004'
                                       THEN
                                  '寿险'
                                 WHEN BP.PRODUCT_CATEGORY2 = '30003'  THEN
                                  '健康险'
                                 WHEN BP.PRODUCT_CATEGORY2 = '30002'  THEN
                                  '意外险'
                               END) RISK_TYPE, /* 险种类型*/                             
                               (CASE WHEN P.POLICY_DECISION ='20' THEN '条件承保'
                               WHEN P.POLICY_DECISION ='40' THEN '延期承保'
                               WHEN P.POLICY_DECISION ='50' THEN '拒保'
                               END)UWRESULT, /* 上报原因*/
                               (SELECT TA.AGENT_NAME
                                  FROM DEV_NB.T_AGENT TA
                                 WHERE TA.AGENT_CODE = CA.AGENT_CODE) AGENT_NAME, /* 业务员姓名*/
                               (SELECT MAX(D.LICENSE_NO)
                                  FROM (SELECT D.LIST_ID,
                                               D.AGENT_CODE,
                                               D.LICENSE_NO,
                                               D.LICENSE_TYPE,
                                               D.LICENSE_START_DATE,
                                               D.LICENSE_END_DATE,
                                               D.STATE
                                          FROM (SELECT A.AGENT_CODE,
                                                       A.LICENSE_TYPE,
                                                       MAX(A.LICENSE_END_DATE) DATEE
                                                  FROM DEV_NB.T_AGENT_LICENSE A
                                                 GROUP BY A.LICENSE_TYPE,
                                                          A.AGENT_CODE) A
                                          LEFT JOIN DEV_NB.T_AGENT_LICENSE D
                                            ON A.LICENSE_TYPE = D.LICENSE_TYPE
                                           AND A.LICENSE_TYPE = '1'
                                           AND A.AGENT_CODE = D.AGENT_CODE
                                           AND A.DATEE = D.LICENSE_END_DATE) D
                                 WHERE CA.AGENT_CODE = D.AGENT_CODE) LICENSE_NO, /* 代理资格证号*/
                               CA.AGENT_CODE /*业务员编码*/
                          FROM DEV_UW.T_UW_MASTER M
                          LEFT JOIN DEV_UW.T_UW_POLICY P
                            ON P.UW_ID = M.UW_ID
                          LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CM
                            ON CM.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_NB.T_NB_INSURED_LIST L
                            ON L.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_NB.T_CUSTOMER C
                            ON C.CUSTOMER_ID = L.CUSTOMER_ID
                          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
                            ON CA.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_UW.T_UW_BUSI_PROD UBP
                            ON UBP.APPLY_CODE = P.APPLY_CODE
                          LEFT JOIN DEV_UW.T_UW_PRODUCT UPT
                            ON UPT.UW_ID = M.UW_ID
                           AND UPT.UW_BUSI_ID = UBP.UW_BUSI_ID
                          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                            ON BP.PRODUCT_CODE_SYS = UPT.BUSI_PROD_CODE                         
                          LEFT JOIN DEV_UW.T_UW_DECISION_REASON R
                            ON R.UW_ID = M.UW_ID
                           AND R.UW_PRD_ID = UPT.UW_PRD_ID
                         WHERE 1 = 1 
                         AND (L.ORDER_ID = 1 or L.ORDER_ID is null)
                         AND BP.PRODUCT_CATEGORY = '10001' ]]>
          <if test=" organ_code != null and organ_code !=''"><![CDATA[AND P.ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
          <if test=" send_time_start  != null  and  send_time_start  != ''  "><![CDATA[ AND TRUNC(M.UW_FINISH_TIME) >= to_date(#{send_time_start},'yyyy-mm-dd') ]]></if>
		  <if test=" send_time_end  != null  and  send_time_end  != ''  "><![CDATA[ AND TRUNC(M.UW_FINISH_TIME) <= to_date(#{send_time_end},'yyyy-mm-dd') ]]></if>
          <if test=" uwresult != null and uwresult !='' "><![CDATA[AND P.POLICY_DECISION = #{uwresult} ]]></if>
          <if test=" uwresult == null or uwresult =='' "><![CDATA[ AND P.POLICY_DECISION IN ('20', '40', '50') ]]></if>          
          <![CDATA[ORDER BY M.UW_FINISH_TIME )F) K ]]>
	</select>
	<!--Modify zhanghui #82124: 福建分公司新契约非标核保结论业务的表单自助提数功能  end-->
	<!--Modify liupengit1  36712 绩优业务员清单新增赔案号 2019年3月26日  Start CASE_NO_STR-->
	<select id="UW_queryUwList4AgentExcellentList1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  * FROM (SELECT  ROWNUM AS RN,B.* FROM   (SELECT T.CONTINUE_MONTHS,
	           SUBSTR(T.AGENT_ORGAN_CODE,0,4) FGS_ORGAN_CODE,
	           (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(T.AGENT_ORGAN_CODE,0,4)) FGS_ORGAN_NAME,
	           SUBSTR(T.AGENT_ORGAN_CODE,0,6) ZHGS_ORGAN_CODE,
	           (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(T.AGENT_ORGAN_CODE,0,6)) ZHGS_ORGAN_NAME,
	           T.AGENT_ORGAN_CODE SALES_ORGAN_CODE,
	           (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = T.AGENT_ORGAN_CODE)
	           AS SALES_ORGAN_NAME,
	           T.AGENT_CODE,
	           T.AGENT_NAME,
	           T.IS_CLAIM_ILLNESS,
	           T.IS_CLAIM_ILLNESS_UNIT,
	           T.IS_CLAIM_ACCIDENT,
	           T.IS_CLAIM_ACCIDENT_UNIT,
	           T.IS_EXCELLENT,
	           T.CONTINUE_RATE,
	           T.INSERT_TIME,
	           T.CASE_NO_STR
	      FROM DEV_UW.T_AGENT_EXCELLENT T 
	      WHERE (T.CONTINUE_MONTHS IS NOT NULL OR T.CONTINUE_RATE IS NOT NULL) ]]>
         <if test=" organ_code != null and organ_code !=''"><![CDATA[AND T.AGENT_ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         <if test=" agent_channel != null and agent_channel !=''"><![CDATA[ AND RTRIM(T.AGENT_CHANNEL)= #{agent_channel}   ]]></if>
         <![CDATA[ ) B WHERE ROWNUM <= #{LESS_NUM} ) F WHERE F.RN > #{GREATER_NUM}]]>
	</select>
	<select id="UW_queryUwList4AgentExcellentListCount1" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(*) FROM  (SELECT T.CONTINUE_MONTHS,
		       T.AGENT_ORGAN_CODE FGS_ORGAN_CODE,
		       T.AGENT_CODE,
		       T.AGENT_NAME,
		       T.IS_CLAIM_ILLNESS,
		       T.IS_CLAIM_ILLNESS_UNIT,
		       T.IS_CLAIM_ACCIDENT,
		       T.IS_CLAIM_ACCIDENT_UNIT,
		       T.IS_EXCELLENT,
		       T.CONTINUE_RATE,
	           T.CASE_NO_STR
	      FROM DEV_UW.T_AGENT_EXCELLENT T 
	      WHERE (T.CONTINUE_MONTHS IS NOT NULL OR T.CONTINUE_RATE IS NOT NULL) ]]>
         <if test=" organ_code != null and organ_code !=''"><![CDATA[AND T.AGENT_ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         <if test=" agent_channel != null and agent_channel !=''"><![CDATA[AND  RTRIM(T.AGENT_CHANNEL)= #{agent_channel}  ]]></if>
         <![CDATA[) B ]]>
	</select>
	
	<select id="UW_exportAgentExcellentList1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN,B.* FROM ( SELECT T.CONTINUE_MONTHS,
           substr(T.AGENT_ORGAN_CODE,0,4) FGS_ORGAN_CODE,
           (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = substr(T.AGENT_ORGAN_CODE,0,4)) FGS_ORGAN_NAME,
           substr(T.AGENT_ORGAN_CODE,0,6) ZHGS_ORGAN_CODE,
           (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = substr(T.AGENT_ORGAN_CODE,0,6)) ZHGS_ORGAN_NAME,
           T.AGENT_ORGAN_CODE SALES_ORGAN_CODE,
           (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = T.AGENT_ORGAN_CODE)
           AS SALES_ORGAN_NAME,
           T.AGENT_CODE,
           T.AGENT_NAME,
           T.IS_CLAIM_ILLNESS,
           T.IS_CLAIM_ILLNESS_UNIT,
           T.IS_CLAIM_ACCIDENT,
           T.IS_CLAIM_ACCIDENT_UNIT,
           T.IS_EXCELLENT,
           T.CONTINUE_RATE,
           T.INSERT_TIME,          
	       T.CASE_NO_STR
      FROM DEV_UW.T_AGENT_EXCELLENT T
	  WHERE (T.CONTINUE_MONTHS IS NOT NULL OR T.CONTINUE_RATE IS NOT NULL) ]]> 
         <if test=" organ_code != null and organ_code !=''"><![CDATA[AND T.AGENT_ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         <if test=" agent_channel != null and agent_channel !=''"><![CDATA[AND  RTRIM(T.AGENT_CHANNEL)= #{agent_channel}   ]]></if>
         <![CDATA[ ) B]]>         
	</select>
	<!--Modify liupengit1  36712 绩优业务员清单新增赔案号 2019年3月26日 End CASE_NO_STR --> 
	<!-- #70152 绩优业务员清单模板调整 -->
	<select id="UW_queryUwList4AgentExcellentList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT ROWNUM RN, A.* FROM (
				SELECT (SELECT L.AGENT_LEVEL_NAME FROM DEV_UW.T_UW_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = E.AGENT_LEVEL) AGENT_LEVEL,
                       E.AGENT_CODE,
                       E.AGENT_NAME,
                       (SELECT O.ORGAN_NAME FROM DEV_NB.T_UDMP_ORG O WHERE O.ORGAN_CODE = E.AGENT_ORGAN_CODE) ORGAN_NAME,
                       SUBSTR(E.AGENT_ORGAN_CODE,1,4) AS FGS_ORGAN_CODE,
                       (SELECT ORGAN_NAME FROM DEV_NB.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(E.AGENT_ORGAN_CODE,1,4)) AS FGS_ORGAN_NAME,
                       SUBSTR(E.AGENT_ORGAN_CODE,1,6) AS ZHGS_ORGAN_CODE,
                       (SELECT ORGAN_NAME FROM DEV_NB.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(E.AGENT_ORGAN_CODE,1,6)) AS ZHGS_ORGAN_NAME,
                       E.AGENT_ORGAN_CODE AS SALES_ORGAN_CODE,
                       (SELECT ORGAN_NAME FROM DEV_NB.T_UDMP_ORG WHERE ORGAN_CODE = E.AGENT_ORGAN_CODE) AS SALES_ORGAN_NAME,
                       (SELECT T.AGENT_TYPE_NAME FROM DEV_UW.T_UW_AGENT_TYPE T WHERE T.AGENT_TYPE_CODE = E.AGENT_TYPE) AGENT_TYPE,
                       E.CONTINUE_MONTHS,
                       (SELECT L.AGENT_LEVEL_NAME FROM DEV_UW.T_UW_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = (CASE WHEN E.INIT_LEVEL IS NULL OR E.INIT_LEVEL = 0 THEN E.INIT_LEVEL_TS WHEN E.INIT_LEVEL_TS IS NULL OR E.INIT_LEVEL_TS = 0 THEN E.INIT_LEVEL WHEN NVL(E.INIT_LEVEL, 0) > NVL(E.INIT_LEVEL_TS, 0) THEN E.INIT_LEVEL_TS ELSE E.INIT_LEVEL END)) INIT_LEVEL,
                       TRUNC(E.CONTINUE_RATE*100,0) CONTINUE_RATE,
                       E.IS_CLAIM_ILLNESS,
                       E.IS_CLAIM_ILLNESS_UNIT,
                       E.CASE_NO_STR,
                       E.AGENT_CHANNEL,
                       (SELECT SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL WHERE SALES_CHANNEL_CODE = E.AGENT_CHANNEL) AS AGENT_CHANNEL_NAME
                  FROM DEV_UW.T_AGENT_EXCELLENT E WHERE 1 = 1 AND (E.BATCH_FLAG = 1 OR E.BATCH_FLAG = 2 OR E.BATCH_FLAG = 3) ]]>
         <if test=" organ_code != null and organ_code !=''"><![CDATA[AND E.AGENT_ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         <if test=" agent_channel != null and agent_channel !=''"><![CDATA[ AND RTRIM(E.AGENT_CHANNEL)= #{agent_channel}   ]]></if>
         <![CDATA[ ORDER BY E.AGENT_CHANNEL,E.AGENT_ORGAN_CODE) A) A WHERE A.RN BETWEEN #{GREATER_NUM} AND #{LESS_NUM} ]]>
	</select>
	<select id="UW_queryUwList4AgentExcellentListCount" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_AGENT_EXCELLENT E WHERE 1 = 1 AND (E.BATCH_FLAG = 1 OR E.BATCH_FLAG = 2 OR E.BATCH_FLAG = 3) ]]>
         <if test=" organ_code != null and organ_code !=''"><![CDATA[AND E.AGENT_ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         <if test=" agent_channel != null and agent_channel !=''"><![CDATA[ AND RTRIM(E.AGENT_CHANNEL)= #{agent_channel}   ]]></if>
	</select>
	<select id="UW_exportAgentExcellentList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ROWNUM RN, A.* FROM (SELECT (
				SELECT L.AGENT_LEVEL_NAME FROM DEV_UW.T_UW_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = E.AGENT_LEVEL) AGENT_LEVEL,
                       E.AGENT_CODE,
                       E.AGENT_NAME,
                       E.AGENT_ORGAN_CODE ORGAN_CODE,
                      (SELECT O.ORGAN_NAME FROM DEV_NB.T_UDMP_ORG O WHERE O.ORGAN_CODE = E.AGENT_ORGAN_CODE) ORGAN_NAME,
                       SUBSTR(E.AGENT_ORGAN_CODE,1,4) AS FGS_ORGAN_CODE,
                       (SELECT ORGAN_NAME FROM DEV_NB.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(E.AGENT_ORGAN_CODE,1,4)) AS FGS_ORGAN_NAME,
                       SUBSTR(E.AGENT_ORGAN_CODE,1,6) AS ZHGS_ORGAN_CODE,
                       (SELECT ORGAN_NAME FROM DEV_NB.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(E.AGENT_ORGAN_CODE,1,6)) AS ZHGS_ORGAN_NAME,
                       E.AGENT_ORGAN_CODE AS SALES_ORGAN_CODE,
                       (SELECT ORGAN_NAME FROM DEV_NB.T_UDMP_ORG WHERE ORGAN_CODE = E.AGENT_ORGAN_CODE) AS SALES_ORGAN_NAME,
                       (SELECT T.AGENT_TYPE_NAME FROM DEV_UW.T_UW_AGENT_TYPE T WHERE T.AGENT_TYPE_CODE = E.AGENT_TYPE) AGENT_TYPE,
                       E.CONTINUE_MONTHS,
                       (SELECT L.AGENT_LEVEL_NAME FROM DEV_UW.T_UW_AGENT_LEVEL L WHERE L.AGENT_LEVEL_CODE = (CASE WHEN E.INIT_LEVEL IS NULL OR E.INIT_LEVEL = 0 THEN E.INIT_LEVEL_TS WHEN E.INIT_LEVEL_TS IS NULL OR E.INIT_LEVEL_TS = 0 THEN E.INIT_LEVEL WHEN NVL(E.INIT_LEVEL, 0) > NVL(E.INIT_LEVEL_TS, 0) THEN E.INIT_LEVEL_TS ELSE E.INIT_LEVEL END)) INIT_LEVEL,
                       TRUNC(E.CONTINUE_RATE*100, 0) CONTINUE_RATE,
                       E.IS_CLAIM_ILLNESS,
                       E.IS_CLAIM_ILLNESS_UNIT,
                       E.CASE_NO_STR,
                       E.AGENT_CHANNEL,
                       (SELECT SALES_CHANNEL_NAME FROM DEV_PAS.T_SALES_CHANNEL WHERE SALES_CHANNEL_CODE = E.AGENT_CHANNEL) AS AGENT_CHANNEL_NAME
                  FROM DEV_UW.T_AGENT_EXCELLENT E WHERE 1 = 1 AND (E.BATCH_FLAG = 1 OR E.BATCH_FLAG = 2 OR E.BATCH_FLAG = 3) ]]>
         <if test=" organ_code != null and organ_code !=''"><![CDATA[AND E.AGENT_ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         <if test=" agent_channel != null and agent_channel !=''"><![CDATA[ AND RTRIM(E.AGENT_CHANNEL)= #{agent_channel}   ]]></if>
         <![CDATA[ ORDER BY E.AGENT_CHANNEL,E.AGENT_ORGAN_CODE) A]]>
	</select>
	<!-- #70152 绩优业务员清单模板调整 -->
	
	<!-- 暂时没用 -->
	<select id="UW_queryWorkLoadMonitor" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select m.* from (select (select substr(uor.organ_name, 5, 2) as organ_name
                from DEV_PAS.T_UDMP_ORG_REL uor
               where uor.organ_code = #{organCode}) as organ_name, --机构名称
             
             (SELECT COUNT(*)
             	FROM DEV_NB.T_BPO_ENTRY_DETAIL A  
    		 		WHERE A.SEND_STATE = '1'
    		 		AND A.Organ_Code like #{organ_code_item}||'%') 
    		 		AND A.SEND_DATE = TO_DATE(#{toDay}, 'yyyy-mm-dd')
                 ) AS SENDNUM, --新契约上传件数
             (SELECT COUNT(*)
                FROM DEV_NB.T_BPO_ENTRY_DETAIL A
               WHERE A.RECEIVE_STATE = '2'
                 AND A.Organ_Code like #{organCode}||'%'
                 AND A.RECEIVE_DATE = TO_DATE(#{toDay}, 'yyyy-mm-dd')) AS RETNUM, --新契约回传件数
             (select count(*)
		          from (SELECT distinct a.biz_code
		                  FROM DEV_UW.T_UW_MASTER A, DEV_UW.T_UW_POLICY B
		                 WHERE A.UW_ID = B.UW_ID
		                   and a.uw_source_type = '1']]>
		                   <foreach collection = "organ_code_set" item="organ_code_item" index="0" open="[" close="]" separator=",">
		                   		<![CDATA[ and b.organ_code like  #{organ_code_item}||'%'   ]]>
		                   </foreach>
		                  <![CDATA[ and b.uw_finish_time = TO_DATE('2018-06-15', 'yyyy-mm-dd')
		                   AND A.UW_STATUS_DETAIL IN ('0401', '0121')
		                    OR A.UW_OVER_INDI = '1'
		                    OR A.Uw_Esca_Indi = '1')) as nbreturned --新契约已回复件数
        from dual) m]]>
	</select>
	<!--查询单条语句  -->
	<select id="UW_queryWorkLoadMonitorOne" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select m.* from (select (select uor.organ_name
                from DEV_PAS.T_UDMP_ORG_REL uor
               where uor.organ_code = #{organcode}) as organname, --机构名称
             (SELECT COUNT(*)
                FROM DEV_NB.T_BPO_ENTRY_DETAIL A
               WHERE A.SEND_STATE = '1'
                 AND A.Organ_Code like #{organcode}||'%'
                 AND to_char(A.SEND_DATE,'yyyy-mm-dd') = #{today}
              ) AS sendnum, --新契约上传件数
             (SELECT COUNT(*)
                FROM DEV_NB.T_BPO_ENTRY_DETAIL A
               WHERE A.RECEIVE_STATE = '2'
                 AND A.Organ_Code like #{organcode}||'%'
                 AND to_char(A.RECEIVE_DATE,'yyyy-mm-dd') = #{today}
             ) AS retnum, --新契约回传件数
             (SELECT COUNT(*)
		          FROM dev_bpm.wftask t
		         WHERE 1 = 1
		           AND t.textattribute8 = '1' --新契约 1 保全 2 理赔 4 续保 3
		           AND t.isgroup = 'T' --未人工核保，在共享池
		           AND t.state = 'ASSIGNED' --运行中状态
		           AND t.title = 'UW020201'
		           AND t.textattribute6 like  #{organcode}||'%' --机构
		           AND to_char(t.createddate,'yyyy-mm-dd') = #{today} 
		        ) as nbnotunderwriting, --新契约未人工核保
		       (SELECT COUNT(*)
		          FROM dev_bpm.wftask t
		         WHERE 1 = 1
		           AND t.textattribute8 = '1' --新契约 1 保全 2 理赔 4 续保 3
		           AND t.state = 'ASSIGNED' --运行中状态
		           AND t.title = 'UW020201'
		           AND t.protectedTextAttribute4 = '1' --1收付费已到账，0未到账
		           AND t.textattribute6 like  #{organcode}||'%' --机构
		           AND to_char(t.createddate,'yyyy-mm-dd') = #{today}
		        ) as nbaccount, --新契约到账件数
		       (select count(*)
		          from (SELECT distinct a.biz_code
		                  FROM DEV_UW.T_UW_MASTER A, DEV_UW.T_UW_POLICY B
		                 WHERE A.UW_ID = B.UW_ID
		                   and a.uw_source_type = '1'
		                   and b.organ_code like  #{organcode}||'%'
		                   and to_char(a.uw_finish_time,'yyyy-mm-dd') = #{today}
		                   AND (A.UW_STATUS_DETAIL IN ('0401', '0121')
		                    OR A.UW_OVER_INDI = '1'
		                    OR A.Uw_Esca_Indi = '1'))
		       ) as nbreturned, --新契约已回复件数
		       (SELECT COUNT(*)
		          FROM dev_bpm.wftask t
		         WHERE 1 = 1
		           AND t.textattribute8 = '2' --新契约 1 保全 2 理赔 4 续保 3
		           AND t.isgroup = 'T' --未人工核保，在共享池
		           AND t.state = 'ASSIGNED' --运行中状态
		           AND t.title = 'UW020201'
		           AND t.textattribute6 like  #{organcode}||'%' --机构
		           AND to_char(t.createddate,'yyyy-mm-dd') = #{today}
		        ) as pasnotunderwriting, --保全未人工核保件数
		       (select count(*)
		          from (SELECT distinct a.biz_code
		                  FROM DEV_UW.T_UW_MASTER A, DEV_UW.T_UW_POLICY B
		                 WHERE A.UW_ID = B.UW_ID
		                 and b.organ_code like  #{organcode}||'%'
		                 and to_char(a.uw_finish_time, 'yyyy-mm-dd') = #{today}
		                 and a.uw_source_type = '2'
		                 AND A.UW_STATUS_DETAIL IN ('0401'))
		       ) as pasreturned, --保全已回复件数
		       (SELECT COUNT(*)
		          FROM dev_bpm.wftask t
		         WHERE 1 = 1
		           AND t.textattribute8 = '4' --新契约 1 保全 2 理赔 4 续保 3
		           AND t.isgroup = 'T' --未人工核保，在共享池
		           AND t.state = 'ASSIGNED' --运行中状态
		           AND t.title = 'UW020201'
		           AND t.textattribute6 like  #{organcode}||'%' --机构
		           AND to_char(t.createddate, 'yyyy-mm-dd') = #{today}
		        ) as clmwaitunderwriting, --二核待人工核保件数
		       (select count(*)
		          from (SELECT distinct a.biz_code
		                  FROM DEV_UW.T_UW_MASTER A, DEV_UW.T_UW_POLICY B
		                 WHERE A.UW_ID = B.UW_ID
		                   and b.organ_code like  #{organcode}||'%'
		                   and to_char(a.uw_finish_time, 'yyyy-mm-dd') = #{today}
		                   and a.uw_source_type = '2'
		                   AND A.UW_STATUS_DETAIL IN ('0401'))
		       ) as clmreturned, --二核已回复件数
		       (SELECT COUNT(*)
		          FROM dev_bpm.wftask t
		         WHERE 1 = 1
		           AND t.textattribute8 = '3' --新契约 1 保全 2 理赔 4 续保 3
		           AND t.isgroup = 'T' --未人工核保，在共享池
		           AND t.state = 'ASSIGNED' --运行中状态
		           AND t.title = 'UW020201'
		           AND t.textattribute6 like  #{organcode}||'%' --机构
		           AND to_char(t.createddate, 'yyyy-mm-dd') = #{today}
		        ) as xubaowaitunderwriting, --续保待人工核保件数
		       (select count(*)
		          from (SELECT distinct a.biz_code
		                  FROM DEV_UW.T_UW_MASTER A, DEV_UW.T_UW_POLICY B
		                 WHERE A.UW_ID = B.UW_ID
		                   and b.organ_code like  #{organcode}||'%'
		                   and to_char(a.uw_finish_time, 'yyyy-mm-dd') = #{today}
		                   and a.uw_source_type = '3'
		                   AND A.UW_STATUS_DETAIL IN ('0401'))
		        ) as xubaoreturned --续保已回复件数
        from dual) m]]>
	</select>
	
	<select id="queryUwTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT M.*
        FROM (SELECT (SELECT COUNT(1) FROM DEV_NB.T_BPO_ENTRY_DETAIL A WHERE A.SEND_STATE = '1' AND A.ORGAN_CODE LIKE #{organcode}||'%'
                 AND TO_CHAR(A.SEND_DATE, 'YYYY-MM-DD') = #{today} 
              ) AS SENDNUM, --新契约上传件数
             (SELECT COUNT(1) FROM DEV_NB.T_BPO_ENTRY_DETAIL A WHERE A.RECEIVE_STATE = '2' AND A.ORGAN_CODE LIKE #{organcode}||'%'
                 AND TO_CHAR(A.RECEIVE_DATE, 'YYYY-MM-DD') = #{today} 
              ) AS RETNUM, --新契约回传件数
             (SELECT COUNT(1) FROM DEV_NB.T_BPO_ENTRY_DETAIL A WHERE A.RECEIVE_STATE = '1' AND A.ORGAN_CODE LIKE #{organcode}||'%'
                 AND TO_CHAR(A.RECEIVE_DATE, 'YYYY-MM-DD') <= #{today} 
              ) AS NOTRETURN, --新契约未回传件数
             (SELECT COUNT(1) FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM WHERE M.UW_ID = CM.UW_ID AND CM.ORGAN_CODE LIKE #{organcode}||'%'
                 AND M.UW_SOURCE_TYPE = 1 AND M.UW_STATUS_DETAIL = '0001' AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS NBNOTUNDERWRITING, --新契约未人工核保
             (SELECT COUNT(1) FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM, (SELECT A1.APPLY_CODE, A1.FEE_STATUS FROM APP___CAP__DBUSER.T_PREM_ARAP A1 WHERE A1.FEE_STATUS = 01 GROUP BY A1.APPLY_CODE, A1.FEE_STATUS) A1
               WHERE M.UW_ID = CM.UW_ID AND A1.APPLY_CODE = M.BIZ_CODE AND CM.ORGAN_CODE LIKE #{organcode}||'%'
                 AND M.UW_SOURCE_TYPE = 1 AND M.UW_STATUS_DETAIL = '0001' AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS NBACCOUNT, --新契约到账件数
             (SELECT COUNT(1) C FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM, （SELECT W.UW_ID, COUNT(W.UW_ID) FROM DEV_UW.T_UW_WORKLOAD W WHERE W.UW_STATUS_DETAIL IN ('0114','0118','0121','0123') GROUP BY W.UW_ID）W
               WHERE M.UW_ID = CM.UW_ID AND M.UW_ID = W.UW_ID AND CM.ORGAN_CODE LIKE #{organcode}||'%' AND M.UW_STATUS_DETAIL IN ('0114', '0118', '0121', '0123')
                 AND M.UW_SOURCE_TYPE = 1 AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS NBRETURNED, --新契约已回复件数
             (SELECT COUNT(1) FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM WHERE M.UW_ID = CM.UW_ID AND CM.ORGAN_CODE LIKE #{organcode}||'%'
                 AND M.UW_SOURCE_TYPE = 2 AND M.UW_STATUS_DETAIL = '0001' AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS PASNOTUNDERWRITING, --保全未人工核保件数
             (SELECT COUNT(1) C FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM, （SELECT W.UW_ID, COUNT(W.UW_ID) FROM DEV_UW.T_UW_WORKLOAD W WHERE W.UW_STATUS_DETAIL IN ('0114','0118','0121','0123') GROUP BY W.UW_ID）W
               WHERE M.UW_ID = CM.UW_ID AND M.UW_ID = W.UW_ID AND CM.ORGAN_CODE LIKE #{organcode}||'%' AND M.UW_STATUS_DETAIL IN ('0114', '0118', '0121', '0123')
                 AND M.UW_SOURCE_TYPE = 2 AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS PASRETURNED, --保全已回复件数
             (SELECT COUNT(1) FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM WHERE M.UW_ID = CM.UW_ID AND CM.ORGAN_CODE LIKE #{organcode}||'%'
                 AND M.UW_SOURCE_TYPE = 4 AND M.UW_STATUS_DETAIL = '0001' AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS CLMWAITUNDERWRITING, --理赔未人工核保件数
             (SELECT COUNT(1) C FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM, （SELECT W.UW_ID, COUNT(W.UW_ID) FROM DEV_UW.T_UW_WORKLOAD W WHERE W.UW_STATUS_DETAIL IN ('0114','0118','0121','0123') GROUP BY W.UW_ID）W
               WHERE M.UW_ID = CM.UW_ID AND M.UW_ID = W.UW_ID AND CM.ORGAN_CODE LIKE #{organcode}||'%' AND M.UW_STATUS_DETAIL IN ('0114', '0118', '0121', '0123')
                 AND M.UW_SOURCE_TYPE = 4 AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS CLMRETURNED, --二核已回复件数        
             (SELECT COUNT(1) FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM WHERE M.UW_ID = CM.UW_ID AND CM.ORGAN_CODE LIKE #{organcode}||'%'
                 AND M.UW_SOURCE_TYPE = 3 AND M.UW_STATUS_DETAIL = '0001' AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS XUBAOWAITUNDERWRITING, --续保未人工核保件数
             (SELECT COUNT(1) C FROM DEV_UW.T_UW_MASTER M, DEV_UW.T_CONTRACT_MASTER CM, （SELECT W.UW_ID, COUNT(W.UW_ID) FROM DEV_UW.T_UW_WORKLOAD W WHERE W.UW_STATUS_DETAIL IN ('0114','0118','0121','0123') GROUP BY W.UW_ID）W
               WHERE M.UW_ID = CM.UW_ID AND M.UW_ID = W.UW_ID AND CM.ORGAN_CODE LIKE #{organcode}||'%' AND M.UW_STATUS_DETAIL IN ('0114', '0118', '0121', '0123')
                 AND M.UW_SOURCE_TYPE = 3 AND TO_CHAR(M.UW_SUBMIT_TIME, 'YYYY-MM-DD') <= #{today} 
              ) AS XUBAORETURNED, --续保已回复件数                
             ''
        FROM DUAL) M
		]]>
	</select>
	
	<!--  根据机构代码 查询机构信息 -->
	<select id="queryUdmpOrgByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT ORGAN_NAME,ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = #{organ_code} AND ROWNUM = 1
	</select>
	
	<!-- 显示险种层核保结论 -->
	<select id="queryUwProductDecisionConclusion" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT A.DECISION_CODE,A.DECISION_DESC FROM DEV_UW.T_PRODUCT_DECISION A WHERE A.DECISION_CODE !='60' AND A.DECISION_CODE !='90'
	</select>
	
	<!-- 保单层核保结论 -->
	<select id="queryUwPolicyDecisionConclusion" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT A.DECISION_CODE,A.DECISION_DESC FROM DEV_UW.T_POLICY_DECISION A where A.Decision_Code != '90' and A.Decision_Code !='60'
	</select>
	
	<!-- 核保清单查询 -->
	<select id="queryUwPolicyList" resultType="com.nci.tunan.qry.interfaces.model.vo.NewUWListPolicyVO" parameterType="java.util.Map">
		<![CDATA[SELECT * FROM (SELECT ROWNUM AS RN, BPOS.* FROM (SELECT SUBSTR(A.ORGAN_CODE, 0, 4) fgsOrganCode,/*分公司代码*/
                        (SELECT ORGAN_NAME
                           FROM DEV_PAS.T_UDMP_ORG
                          WHERE ORGAN_CODE = SUBSTR(A.ORGAN_CODE, 0, 4)) fgsOrganName,/*分公司名称 */
                        SUBSTR(A.ORGAN_CODE, 0, 6) zhgsOrganCode,/*支公司代码*/
                        (SELECT ORGAN_NAME
                           FROM DEV_PAS.T_UDMP_ORG
                          WHERE ORGAN_CODE = SUBSTR(A.ORGAN_CODE, 0, 6)) zhgsOrganName,/*支公司名称*/
                        A.ORGAN_CODE salesOrganCode,/*销售部代码*/
                        (SELECT ORGAN_NAME
                           FROM DEV_PAS.T_UDMP_ORG
                          WHERE ORGAN_CODE = A.ORGAN_CODE) AS salesOrganName,/*销售部名称*/
                        to_char(A.APPLY_CODE) applyCode,/*投保单号*/
                        (SELECT count(A1.FEE_STATUS) FROM APP___CAP__DBUSER.T_PREM_ARAP A1
							WHERE A1.FEE_STATUS = 01 and a1.apply_code = a.apply_code
                             and a1.busi_prod_code = B.BUSI_PROD_CODE) feeStatus ,/*是否到账*/
                        A.APPLY_DATE applyDate,/*投保日期*/
                        to_char(B.BUSI_PROD_CODE) busiProdCode,/*险种代码*/
                        
                        (SELECT PRODUCT_NAME_SYS
                           FROM DEV_PDS.T_BUSINESS_PRODUCT F
                          WHERE F.PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) AS busiProdName,/*险种名称*/

                        to_char(B.AMOUNT) amount,/*险种保额*/
                        to_char(B.TOTAL_PREM_AF) totalPremAf,/*险种保费  总保费*/
                        to_char(B.CHARGE_YEAR) chargeYear,/*缴费期间*/

                        (select tpd.decision_desc
                 from dev_uw.T_PRODUCT_DECISION tpd
                          where B.DECISION_CODE = tpd.decision_code
                            and tpd.decision_code != '60'
                            and tpd.decision_code != '90') as decisionCode,/*险种层核保结*/
                        (SELECT tpds.DECISION_DESC
                           FROM dev_uw.T_POLICY_DECISION tpds
                          where tpds.Decision_Code = D.POLICY_DECISION
                            and tpds.Decision_Code != '90'
                            and tpds.Decision_Code != '60') as policyDecision,/*保单层核保结论*/
                        case
                          when C.UW_OVER_INDI is not null and
                               C.UW_ESCA_INDI is not null then
                           to_char(C.UW_OVER_INDI) || ',' ||
                           to_char(C.UW_ESCA_INDI)
                          else
                           to_char(C.UW_OVER_INDI) || ',' || to_char(C.UW_ESCA_INDI)
                        end as uwIndi,/*核保流向 */

                        (select TO_CHAR(WM_CONCAT(DR.REASON_CONTENT))
                           from DEV_UW.T_UW_DECISION_REASON Dr
                          where DR.UW_PRD_ID = B.UW_PRD_ID
                            AND DR.REASON_TYPE in ('5', '6')
                           ) as decisionDesc,/*延期拒保原因*/
                     (SELECT TO_CHAR(WM_CONCAT(DR.CONDITION_DESC))
                           FROM DEV_UW.t_uw_condition DR
                          WHERE DR.UW_PRD_ID = B.UW_PRD_ID and DR.UW_ID = B.UW_ID
                           ) AS specialReason,/*特约内容*/

                        (SELECT DISTINCT TEP.EM_VALUE
                           FROM DEV_UW.T_UW_EXTRA_PREM TEP
                          WHERE TEP.UW_ID = B.UW_ID
                            AND TEP.EXTRA_TYPE = '1' and TEP.UW_PRD_ID = B.UW_PRD_ID) AS emValue,/*加费评点*/

                      (SELECT TUT.UW_COMMENTS
                           FROM DEV_UW.T_UW_TRACE TUT
                          WHERE TUT.Trace_Id = temps.Trace_Id) AS authoritySuper,/*评点超权限上报原因*/

                        (SELECT TUT.UW_COMMENTS
                           FROM DEV_UW.T_UW_TRACE TUT
                          WHERE TUT.Trace_Id = tempd.Trace_Id) AS difficultReporting,/*疑难案例上报原因*/
             
                       (select uu.user_name from dev_uw.t_uw_master um,
                        dev_pas.t_udmp_user uu where um.uw_user_id = uu.user_id and um.uw_id = B.UW_ID) uwUserId,/*核保员代码*/
                        
                        (SELECT TUT.UW_COMMENTS
                           FROM DEV_UW.T_UW_TRACE TUT
                          WHERE TUT.Trace_Id = tempf.Trace_Id) AS uwDecision /*原因说明   核保决定*/
                            
                   FROM DEV_NB.T_NB_CONTRACT_MASTER A,
                        DEV_UW.T_UW_PRODUCT         B,
                        DEV_UW.T_UW_MASTER          C,
                        DEV_UW.T_UW_POLICY          D,
                        DEV_UW.t_uw_auto uwa,
                       (SELECT max(tut.trace_id) trace_id, TUT.UW_ID
                           FROM DEV_UW.T_UW_TRACE TUT where tut.uw_event_code = '10'
                          group by TUT.UW_ID) temps,
                         (SELECT max(tut.trace_id) trace_id, TUT.UW_ID
                           FROM DEV_UW.T_UW_TRACE TUT where tut.uw_event_code = '11'
                          group by TUT.UW_ID) tempd,
                           (SELECT max(tut.trace_id) trace_id, TUT.UW_ID
                           FROM DEV_UW.T_UW_TRACE TUT where tut.uw_event_code = '13'
                          group by TUT.UW_ID) tempf 
                  WHERE C.UW_SOURCE_TYPE = '1' /*核保来源是新契约*/
                    AND A.APPLY_CODE = C.BIZ_CODE /*契约保单表和核保保单表关联*/
                    AND B.UW_ID = C.UW_ID /*责任组和核保信息关联*/
                    AND C.UW_ID = D.UW_ID
                     AND C.UW_ID = uwa.UW_ID
                    AND C.BIZ_CODE = uwa.APPLY_CODE
                    AND ((uwa.RULE_RUN_STATUS = 'V01' AND c.REVISION_FLAG = 1) OR uwa.RULE_RUN_STATUS = 'V02') /*uwa.RULE_RUN_STATUS = 'V02'*/
                    and b.uw_id = temps.UW_ID(+)
                    and b.uw_id = tempd.UW_ID(+)
                    and b.uw_id = tempf.UW_ID(+)
                    AND A.ORGAN_CODE LIKE #{organ_code} || '%' /*机构代码*/
          			AND A.APPLY_DATE BETWEEN TO_DATE(#{start_date}, 'yyyy-mm-dd') AND
              		TO_DATE(#{end_date}, 'yyyy-mm-dd') 
       ]]>
        <if test=" decision_code != null and decision_code !='' and decision_code !=0"><![CDATA[AND B.DECISION_CODE =#{decision_code}]]></if>
        
        <if test=" decision_code != null and decision_code !='' and decision_code ==0"><![CDATA[AND B.DECISION_CODE in ('10','30','31','32','33','40','50','70')]]></if>
     
       <if test=" policy_decision != null and policy_decision !=''"><![CDATA[AND D.POLICY_DECISION =#{policy_decision}]]></if>
     
       <if test=" uw_over_indi != null and uw_over_indi !=''"><![CDATA[AND C.UW_OVER_INDI =#{uw_over_indi}]]></if>
   
        <if test=" uw_esca_indi != null and uw_esca_indi !=''"><![CDATA[AND C.UW_ESCA_INDI =#{uw_esca_indi}]]></if>
        order by A.APPLY_CODE desc
         <![CDATA[) BPOS WHERE ROWNUM <= #{LESS_NUM} ]]>
 		 <![CDATA[) F WHERE F.RN > #{GREATER_NUM}]]>
	</select>
	
	<!-- 核保清单查询数量 -->
	<select id="queryUwPolicyListQuantity" resultType="java.lang.Integer" parameterType="java.util.Map">
		SELECT count(*)
		FROM DEV_NB.T_NB_CONTRACT_MASTER A, /*契约保单表*/
              DEV_UW.T_UW_PRODUCT B,/*责任层信息表*/
              DEV_UW.T_UW_MASTER C,/*核保主信息表*/
              DEV_UW.T_UW_POLICY D, /*核保保单主表*/
        	  DEV_UW.t_uw_auto uwa
        WHERE C.UW_SOURCE_TYPE = '1' /*核保来源是新契约*/
          AND A.APPLY_CODE = C.BIZ_CODE /*契约保单表和核保保单表关联*/
          AND B.UW_ID = C.UW_ID /*责任组和核保信息关联*/
          AND C.UW_ID = D.UW_ID
          AND C.UW_ID = uwa.UW_ID
          AND C.BIZ_CODE = uwa.APPLY_CODE
          AND ((uwa.RULE_RUN_STATUS = 'V01' AND c.REVISION_FLAG = 1) OR uwa.RULE_RUN_STATUS = 'V02') /*uwa.RULE_RUN_STATUS = 'V02'*/
          AND A.ORGAN_CODE LIKE #{organ_code} || '%' /*机构代码*/
          AND A.APPLY_DATE BETWEEN TO_DATE(#{start_date}, 'yyyy-mm-dd') AND
              TO_DATE(#{end_date}, 'yyyy-mm-dd')
       <if test=" decision_code != null and decision_code !='' and decision_code !=0"><![CDATA[AND B.DECISION_CODE =#{decision_code}]]></if>
        
        <if test=" decision_code != null and decision_code !='' and decision_code ==0"><![CDATA[AND B.DECISION_CODE in ('10','30','31','32','33','40','50','70')]]></if>
     
       <if test=" policy_decision != null and policy_decision !=''"><![CDATA[AND D.POLICY_DECISION =#{policy_decision}]]></if>
     
       <if test=" uw_over_indi != null and uw_over_indi !=''"><![CDATA[AND C.UW_OVER_INDI =#{uw_over_indi}]]></if>
   
        <if test=" uw_esca_indi != null and uw_esca_indi !=''"><![CDATA[AND C.UW_ESCA_INDI =#{uw_esca_indi}]]></if>
	</select>
	
	<!-- 核保清单查询 -->
	<select id="queryUwPolicyListExport" resultType="com.nci.tunan.qry.interfaces.model.vo.NewUWListPolicyVO" parameterType="java.util.Map">
	SELECT * FROM (SELECT ROWNUM AS RN, BPOS.* FROM (SELECT SUBSTR(A.ORGAN_CODE, 0, 4) fgsOrganCode,/*分公司代码*/
                        (SELECT ORGAN_NAME
                           FROM DEV_PAS.T_UDMP_ORG
                          WHERE ORGAN_CODE = SUBSTR(A.ORGAN_CODE, 0, 4)) fgsOrganName,/*分公司名称*/
                        SUBSTR(A.ORGAN_CODE, 0, 6) zhgsOrganCode,/*支公司代码*/
                        (SELECT ORGAN_NAME
                           FROM DEV_PAS.T_UDMP_ORG
                          WHERE ORGAN_CODE = SUBSTR(A.ORGAN_CODE, 0, 6)) zhgsOrganName,/*支公司名称*/
                        A.ORGAN_CODE salesOrganCode,/*销售部代码*/
                        (SELECT ORGAN_NAME
                           FROM DEV_PAS.T_UDMP_ORG
                          WHERE ORGAN_CODE = A.ORGAN_CODE) AS salesOrganName,/*销售部名称*/
                        to_char(A.APPLY_CODE) applyCode,/*投保单号*/
						(SELECT count(A1.FEE_STATUS) FROM APP___CAP__DBUSER.T_PREM_ARAP A1
							WHERE A1.FEE_STATUS = 01 and a1.apply_code = a.apply_code
                             and a1.busi_prod_code = B.BUSI_PROD_CODE) feeStatus ,/*是否到账*/
                        A.APPLY_DATE applyDate,/*投保日期*/
                        to_char(B.BUSI_PROD_CODE) busiProdCode,/*险种代码*/
                        
                        (SELECT PRODUCT_NAME_SYS
                           FROM DEV_PDS.T_BUSINESS_PRODUCT F
                          WHERE F.PRODUCT_CODE_SYS = B.BUSI_PROD_CODE) AS busiProdName,/*险种名称*/

                        to_char(B.AMOUNT) amount,/*险种保额*/
                        to_char(B.TOTAL_PREM_AF) totalPremAf,/*险种保费  总保费*/
                        to_char(B.CHARGE_YEAR) chargeYear,/*缴费期间*/

                        (select tpd.decision_desc
                 from dev_uw.T_PRODUCT_DECISION tpd
                          where B.DECISION_CODE = tpd.decision_code
                            and tpd.decision_code != '60'
                            and tpd.decision_code != '90') as decisionCode,/*险种层核保结*/
                        (SELECT tpds.DECISION_DESC
                           FROM dev_uw.T_POLICY_DECISION tpds
                          where tpds.Decision_Code = D.POLICY_DECISION
                            and tpds.Decision_Code != '90'
                            and tpds.Decision_Code != '60') as policyDecision,/*保单层核保结论*/
                            
                         case
                          when C.UW_OVER_INDI is not null and
                               C.UW_ESCA_INDI is not null then
                           to_char(C.UW_OVER_INDI) || ',' ||
                           to_char(C.UW_ESCA_INDI)
                          else
                           to_char(C.UW_OVER_INDI) || ',' || to_char(C.UW_ESCA_INDI)
                        end as uwIndi,/*核保流向 */

                        (select TO_CHAR(WM_CONCAT(DR.REASON_CONTENT))
                           from DEV_UW.T_UW_DECISION_REASON Dr
                          where DR.UW_PRD_ID = B.UW_PRD_ID
                            AND DR.REASON_TYPE in ('5', '6')
                           ) as decisionDesc,/*延期拒保原因*/
                       (SELECT TO_CHAR(WM_CONCAT(DR.CONDITION_DESC))
                           FROM DEV_UW.t_uw_condition DR
                          WHERE DR.UW_PRD_ID = B.UW_PRD_ID and DR.UW_ID = B.UW_ID
                           ) AS specialReason,/*特约内容*/

                        (SELECT DISTINCT TEP.EM_VALUE
                           FROM DEV_UW.T_UW_EXTRA_PREM TEP
                          WHERE TEP.UW_ID = B.UW_ID
                            AND TEP.EXTRA_TYPE = '1' and TEP.UW_PRD_ID = B.UW_PRD_ID) AS emValue,/*加费评点*/

                      (SELECT TUT.UW_COMMENTS
                           FROM DEV_UW.T_UW_TRACE TUT
                          WHERE TUT.Trace_Id = temps.Trace_Id) AS authoritySuper,/*评点超权限上报原因*/

                        (SELECT TUT.UW_COMMENTS
                           FROM DEV_UW.T_UW_TRACE TUT
                          WHERE TUT.Trace_Id = tempd.Trace_Id) AS difficultReporting,/*疑难案例上报原因*/
                          
                         (select uu.user_name from dev_uw.t_uw_master um,
                        dev_pas.t_udmp_user uu where um.uw_user_id = uu.user_id and um.uw_id = B.UW_ID) uwUserId,/*核保员代码*/
                        
                        (SELECT TUT.UW_COMMENTS
                           FROM DEV_UW.T_UW_TRACE TUT
                          WHERE TUT.Trace_Id = tempf.Trace_Id) AS uwDecision /*原因说明   核保决定*/
                            
                   FROM DEV_NB.T_NB_CONTRACT_MASTER A,
                        DEV_UW.T_UW_PRODUCT         B,
                        DEV_UW.T_UW_MASTER          C,
                        DEV_UW.T_UW_POLICY          D,
                        DEV_UW.t_uw_auto uwa,
                       (SELECT max(tut.trace_id) trace_id, TUT.UW_ID
                           FROM DEV_UW.T_UW_TRACE TUT where tut.uw_event_code = '10'
                          group by TUT.UW_ID) temps,
                         (SELECT max(tut.trace_id) trace_id, TUT.UW_ID
                           FROM DEV_UW.T_UW_TRACE TUT where tut.uw_event_code = '11'
                          group by TUT.UW_ID) tempd,
                           (SELECT max(tut.trace_id) trace_id, TUT.UW_ID
                           FROM DEV_UW.T_UW_TRACE TUT where tut.uw_event_code = '13'
                          group by TUT.UW_ID) tempf 
                  WHERE C.UW_SOURCE_TYPE = '1' /*核保来源是新契约*/
                    AND A.APPLY_CODE = C.BIZ_CODE /*契约保单表和核保保单表关联*/
                    AND B.UW_ID = C.UW_ID /*责任组和核保信息关联*/
                    AND C.UW_ID = D.UW_ID
                    AND C.UW_ID = uwa.UW_ID
                    AND C.BIZ_CODE = uwa.APPLY_CODE
                    AND ((uwa.RULE_RUN_STATUS = 'V01' AND c.REVISION_FLAG = 1) OR uwa.RULE_RUN_STATUS = 'V02') /*uwa.RULE_RUN_STATUS = 'V02'*/
                    and b.uw_id = temps.UW_ID(+)
                    and b.uw_id = tempd.UW_ID(+)
                    and b.uw_id = tempf.UW_ID(+)
                    AND A.ORGAN_CODE LIKE #{organ_code} || '%' /*机构代码*/
          			AND A.APPLY_DATE BETWEEN TO_DATE(#{start_date}, 'yyyy-mm-dd') AND
              		TO_DATE(#{end_date}, 'yyyy-mm-dd') 
       <if test=" decision_code != null and decision_code !='' and decision_code !=0"><![CDATA[AND B.DECISION_CODE =#{decision_code}]]></if>
        
        <if test=" decision_code != null and decision_code !='' and decision_code ==0"><![CDATA[AND B.DECISION_CODE in ('10','30','31','32','33','40','50','70')]]></if>
     
       <if test=" policy_decision != null and policy_decision !=''"><![CDATA[AND D.POLICY_DECISION =#{policy_decision}]]></if>
     
       <if test=" uw_over_indi != null and uw_over_indi !=''"><![CDATA[AND C.UW_OVER_INDI =#{uw_over_indi}]]></if>
   
        <if test=" uw_esca_indi != null and uw_esca_indi !=''"><![CDATA[AND C.UW_ESCA_INDI =#{uw_esca_indi}]]></if>
        order by A.APPLY_CODE desc
     )BPOS)
	</select>
	
	<!-- 核保通知书追踪建设 查询数量 -->
	<select id="queryUwDocumentTrackingList" resultType="java.lang.Integer" parameterType="java.util.Map">
		select count(*) from DEV_NB.T_DOCUMENT TD,dev_uw.t_uw_master m WHERE TD.BUSS_SOURCE_CODE = '002'  and TD.buss_id = m.uw_id 
		and m.uw_source_type in ('1','2')
		and TD.status not in ('0','1','8','9','a')
		and TD.BUSS_SOURCE_CODE !='001'
		 <if test=" policy_code != null and policy_code !=''"><![CDATA[AND ((TD.POLICY_CODE = #{policy_code}) OR (TD.BUSS_CODE = #{policy_code} AND TD.POLICY_CODE IS NULL))]]></if>
		 <if test=" organ_code != null and organ_code !=''"><![CDATA[AND TD.ORGAN_CODE like #{organ_code}||'%']]></if>
		 <if test=" template_code != null and template_code !=''"><![CDATA[AND TD.TEMPLATE_CODE = #{template_code}]]></if>
		 <if test=" start_date != null and start_date !=''"><![CDATA[AND  trunc(TD.SEND_TIME) BETWEEN TO_DATE(#{start_date},'yyyy-mm-dd') AND TO_DATE(#{end_date},'yyyy-mm-dd')]]></if>
		 <if test=" status != null and status !='' and status == 2"><![CDATA[AND TD.STATUS ='2']]></if>
		 <if test=" status != null and status !='' and status == 4"><![CDATA[AND TD.STATUS in('3','4')]]></if>
		 <if test=" status != null and status !='' and status == 7"><![CDATA[AND TD.STATUS in('5','6','7')]]></if>
	</select>
	
	<!-- 核保通知书追踪建设查询分页-->
	<select id="queryUwDocumentTrackingListPage" resultType="com.nci.tunan.qry.interfaces.model.vo.UWDocumentVO" parameterType="java.util.Map">
		<![CDATA[SELECT  * FROM (SELECT  ROWNUM AS RN,BPOS.* FROM   (select to_char(TN.Agent_Code) agentCode,(select a.agent_name from DEV_NB.T_AGENT a where a.agent_code = TN.agent_code) AS agentName,
		case
		when TD.policy_code is not null then to_char(TD.policy_code)
		else to_char(TD.buss_Code) end bussCode,/*投保单号*/
		to_char(TD.status) status,/*通知书状态*/
		to_char(TD.organ_Code) organCode,/*机构代码*/
		(SELECT t.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG t WHERE t.ORGAN_CODE = TD.ORGAN_CODE) as organName,/*机构名称*/
		to_char(TD.TEMPLATE_CODE) templateCode,/*通知书类型*/
		to_char(TD.SEND_TIME,'yyyy-mm-dd') sendTime,/*发放日期*/
		/*TO_CHAR((SELECT B.PRINT_TIME FROM DEV_NB.T_DOCUMENT_PRINT B WHERE B.PRINT_ID = 
		(SELECT MAX(A.PRINT_ID) PRINT_ID FROM DEV_NB.T_DOCUMENT_PRINT A WHERE A.DOCUMENT_NO = TD.DOCUMENT_NO)) , 'yyyy-mm-dd') PRINTTIME,打印日期*/
		
		
		case 
    when (SELECT count(1) from DEV_NB.T_DOCUMENT_PRINT B WHERE B.PRINT_ID = 
    (SELECT MAX(A.PRINT_ID) PRINT_ID FROM DEV_NB.T_DOCUMENT_PRINT A WHERE A.DOCUMENT_NO = TD.DOCUMENT_NO))=0  then
    TD.PRINT_TIME
    else
     (SELECT B.PRINT_TIME FROM DEV_NB.T_DOCUMENT_PRINT B WHERE B.PRINT_ID = 
    (SELECT MAX(A.PRINT_ID) PRINT_ID FROM DEV_NB.T_DOCUMENT_PRINT A WHERE A.DOCUMENT_NO = TD.DOCUMENT_NO))
    end  PRINTTIME,
		
		to_char(TD.SCAN_TIME,'yyyy-mm-dd') scanTime,/*回扫日期*/
		to_char(m.uw_source_type) sourceType, /*业务来源 1 新契约 2 保全*/
		case m.uw_source_type 
        when '1' then
        	(select a.proposal_status from dev_nb.t_nb_contract_master a where a.apply_code =TD.Buss_Code)
        else
        	(select a.accept_status from dev_pas.t_cs_accept_change a where a.accept_code = m.biz_code)
        end acceptOrUwStatus, /*投保、保全状态*/
		to_char(TD.DOC_LIST_ID) docListId /*通知书主键*/
		from DEV_NB.T_DOCUMENT TD, 
		DEV_NB.T_NB_CONTRACT_AGENT TN,
		dev_uw.t_uw_master m 
		where 
		TD.Buss_Code = TN.apply_code 
		and TD.buss_id = m.uw_id
		and m.uw_source_type in ('1','2')
		and TD.BUSS_SOURCE_CODE !='001'
		and TD.status not in ('0','1','8','9','a')
	 	 ]]> 
	 	<if test=" policy_code != null and policy_code !=''"><![CDATA[AND ((TD.POLICY_CODE = #{policy_code}) OR (TD.BUSS_CODE = #{policy_code} AND TD.POLICY_CODE IS NULL))]]></if>
		 <if test=" organ_code != null and organ_code !=''"><![CDATA[AND TD.ORGAN_CODE like #{organ_code}||'%']]></if>
		 <if test=" template_code != null and template_code !=''"><![CDATA[AND TD.TEMPLATE_CODE = #{template_code}]]></if>
		 <if test=" start_date != null and start_date !=''"><![CDATA[AND  trunc(TD.SEND_TIME) BETWEEN TO_DATE(#{start_date},'yyyy-mm-dd') AND TO_DATE(#{end_date},'yyyy-mm-dd')]]></if>
		  <if test=" status != null and status !='' and status == 2"><![CDATA[AND TD.STATUS ='2']]></if>
		  <if test=" status != null and status !='' and status == 4"><![CDATA[AND TD.STATUS in('3','4')]]></if>
		 <if test=" status != null and status !='' and status == 7"><![CDATA[AND TD.STATUS in('5','6','7')]]></if>
		 order by TD.BUSS_ID desc
		  <![CDATA[) BPOS WHERE ROWNUM <= #{LESS_NUM} ]]>
 		 <![CDATA[) F WHERE F.RN > #{GREATER_NUM}]]>
	</select>
	
	<!-- 核保通知书追踪建设查询导出使用-->
	<select id="queryUwDocumentTrackingListExport" resultType="com.nci.tunan.qry.interfaces.model.vo.UWDocumentVO" parameterType="java.util.Map">
		SELECT  * FROM (SELECT  ROWNUM AS RN,BPOS.* FROM   (select to_char(TN.Agent_Code) agentCode,(select a.agent_name from DEV_NB.T_AGENT a where a.agent_code = TN.agent_code) AS agentName,
		case
		when TD.policy_code is not null then to_char(TD.policy_code)
		else to_char(TD.buss_Code) end bussCode,/*投保单号*/
		to_char(TD.status) status,/*通知书状态*/
		to_char(TD.organ_Code) organCode,/*机构代码*/
		(SELECT t.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG t WHERE t.ORGAN_CODE = TD.ORGAN_CODE) as organName,/*机构名称*/
		to_char(TD.TEMPLATE_CODE) templateCode,/*通知书类型*/
		to_char(TD.SEND_TIME,'yyyy-mm-dd') sendTime,/*发放日期*/
		
		case 
    when (SELECT count(1) from DEV_NB.T_DOCUMENT_PRINT B WHERE B.PRINT_ID = 
    (SELECT MAX(A.PRINT_ID) PRINT_ID FROM DEV_NB.T_DOCUMENT_PRINT A WHERE A.DOCUMENT_NO = TD.DOCUMENT_NO))=0  then
    TD.PRINT_TIME
    else
     (SELECT B.PRINT_TIME FROM DEV_NB.T_DOCUMENT_PRINT B WHERE B.PRINT_ID = 
    (SELECT MAX(A.PRINT_ID) PRINT_ID FROM DEV_NB.T_DOCUMENT_PRINT A WHERE A.DOCUMENT_NO = TD.DOCUMENT_NO))
    end  PRINTTIME,
		
		
		
		
		to_char(TD.SCAN_TIME,'yyyy-mm-dd') scanTime,/*回扫日期*/
		to_char(m.uw_source_type) sourceType, /*业务来源 1 新契约 2 保全*/
		case m.uw_source_type 
        when '1' then
        	(select a.proposal_status from dev_nb.t_nb_contract_master a where a.apply_code =TD.Buss_Code)
        else
        	(select a.accept_status from dev_pas.t_cs_accept_change a where a.accept_code = m.biz_code)
        end acceptOrUwStatus /*投保、保全状态*/
		from DEV_NB.T_DOCUMENT TD, 
		DEV_NB.T_NB_CONTRACT_AGENT TN,
		dev_uw.t_uw_master m 
		where 
		TD.Buss_Code = TN.apply_code 
		and TD.buss_id = m.uw_id
		and m.uw_source_type in ('1','2')
		and TD.BUSS_SOURCE_CODE !='001'
		and TD.status not in ('0','1','8','9','a')
	 	<if test=" policy_code != null and policy_code !=''"><![CDATA[AND ((TD.POLICY_CODE = #{policy_code}) OR (TD.BUSS_CODE = #{policy_code} AND TD.POLICY_CODE IS NULL))]]></if>
		 <if test=" organ_code != null and organ_code !=''"><![CDATA[AND TD.ORGAN_CODE like #{organ_code}||'%']]></if>
		 <if test=" template_code != null and template_code !=''"><![CDATA[AND TD.TEMPLATE_CODE = #{template_code}]]></if>
		 <if test=" start_date != null and start_date !=''"><![CDATA[AND  trunc(TD.SEND_TIME) BETWEEN TO_DATE(#{start_date},'yyyy-mm-dd') AND TO_DATE(#{end_date},'yyyy-mm-dd')]]></if>
		  <if test=" status != null and status !='' and status == 2"><![CDATA[AND TD.STATUS ='2']]></if>
		  <if test=" status != null and status !='' and status == 4"><![CDATA[AND TD.STATUS in('3','4')]]></if>
		<if test=" status != null and status !='' and status == 7"><![CDATA[AND TD.STATUS in('5','6','7')]]></if>
		 order by TD.BUSS_ID desc
		 ) BPOS )
	</select>
	<!-- 通知书主键查询 -->
	<select id="findUWDocumentByDocListId" resultType="com.nci.tunan.qry.interfaces.model.vo.UWDocumentVO" parameterType="java.util.Map">
		<![CDATA[ select to_char(a.document_name) documentName,to_char(b.biz_code) bussCode from dev_nb.t_document a,dev_uw.t_uw_master b where a.buss_id = b.uw_id and a.doc_list_id = #{doc_list_id}]]> 
	</select>
	
	<!-- 核保工作量查询清单 -->
	<select id="uwWorkloadList_count" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(*)
	  	FROM (SELECT DISTINCT (SELECT A.ORGAN_NAME
	                           FROM DEV_UW.T_UDMP_ORG A
	                          WHERE A.ORGAN_CODE = SUBSTR(U.ORGAN_CODE, 0, 4)) AS ORGAN_NAME,
	                        U.REAL_NAME,
	                        U.USER_NAME,
	                        U.USER_ID,
	                        D.PERM_LEVEL_WORKFLOW
	          FROM DEV_PAS.T_UDMP_USER            U,
	               DEV_UW.T_UDMP_GROUP_USER      GU,
	               DEV_UW.T_UDMP_ROLE_GROUP      RG,
	               DEV_UW.T_UDMP_GROUP_ROLE      GR,
	               DEV_UW.T_UDMP_ROLE_PERMISSION B,
	               DEV_UW.T_UDMP_PERMISSION_INFO D
	         WHERE U.USER_ID = GU.USER_ID
	           AND GU.ROLE_GROUP_ID = RG.ROLE_GROUP_ID
	           AND RG.ROLE_GROUP_ID = GR.ROLE_GROUP_ID
	           AND GR.ROLE_ID = B.ROLE_ID
	           AND B.PERMISSION_TYPE_ID = D.PERMISSION_TYPE_ID
	           AND B.PERMISSION_ID = D.PERMISSION_ID
	           AND U.USER_DISABLE = 'N'
	           ]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[AND U.ORGAN_CODE like #{organ_code}||'%']]></if>
		)
	</select>

	<!-- 核保工作量查询清单 -->
	<select id="uwWorkloadList_map" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.* FROM (SELECT ROWNUM RN, T1.* FROM (SELECT (SELECT A.ORGAN_NAME
          FROM DEV_UW.T_UDMP_ORG A
         WHERE A.ORGAN_CODE = SUBSTR(A1.ORGAN_CODE, 0, 4)) AS ORGAN_NAME,
       A1.REAL_NAME,
       A1.USER_NAME,
       A1.USER_ID,
       (SELECT COUNT(UM.UW_ID)
          FROM DEV_UW.T_UW_WORKLOAD UW1, DEV_UW.T_UW_MASTER UM
         WHERE UW1.UW_ID = UM.UW_ID
           AND UM.UW_USER_ID = A1.USER_ID
           AND UM.UW_SOURCE_TYPE = '1'
           AND UW1.UW_STATUS_DETAIL IN ('0110', '0401')
           AND UW1.NOW_UW_STATUS_DETAIL = '0101']]>
           
        	<if test=" examineTimeStart != null and examineTimeStart != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') >= #{examineTimeStart} ]]></if>
			<if test=" examineTimeEnd != null and examineTimeEnd  != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') <= #{examineTimeEnd} ]]></if>
           
          <![CDATA[ ) AS TOTAL1,
       (SELECT COUNT(UM.UW_ID)
          FROM DEV_UW.T_UW_WORKLOAD UW1, DEV_UW.T_UW_MASTER UM
         WHERE UW1.UW_ID = UM.UW_ID
           AND UM.UW_USER_ID = A1.USER_ID
           AND UM.UW_SOURCE_TYPE = '1'
           AND UW1.POLICY_DECISION IS NULL
           AND UW1.NOW_UW_STATUS_DETAIL = '0114']]>
           <if test=" examineTimeStart != null and examineTimeStart != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') >= #{examineTimeStart} ]]></if>
			<if test=" examineTimeEnd != null and examineTimeEnd  != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') <= #{examineTimeEnd} ]]></if>
           <![CDATA[) AS TOTAL2,
       	(SELECT COUNT(DISTINCT UW_BUSI_ID)
                          FROM (SELECT BP.UW_BUSI_ID, UM.UW_USER_ID
                                  FROM DEV_UW.T_UW_WORKLOAD  UW1,
                                       DEV_UW.T_UW_MASTER    UM,
                                       DEV_UW.T_UW_BUSI_PROD BP
                                 WHERE UW1.UW_ID = UM.UW_ID
                                   AND UM.UW_ID = BP.UW_ID
                                      --AND UM.UW_USER_ID = A1.USER_ID
                                   AND UM.UW_USER_ID IS NOT NULL
                                   AND UM.UW_SOURCE_TYPE = '1'
                                   AND UW1.UW_STATUS_DETAIL IN ('0114', '0401')
                                   AND UW1.NOW_UW_STATUS_DETAIL = '0101']]>
            <if test=" examineTimeStart != null and examineTimeStart != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') >= #{examineTimeStart} ]]></if>
			<if test=" examineTimeEnd != null and examineTimeEnd  != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') <= #{examineTimeEnd} ]]></if>
                               <![CDATA[ UNION ALL
                                SELECT BP.UW_BUSI_ID, UM.UW_USER_ID
                                  FROM DEV_UW.T_UW_WORKLOAD  UW1,
                                       DEV_UW.T_UW_MASTER    UM,
                                       DEV_UW.T_UW_BUSI_PROD BP
                                 WHERE UW1.UW_ID = UM.UW_ID
                                   AND UM.UW_ID = BP.UW_ID
                                   AND UM.UW_USER_ID IS NOT NULL
                                      --AND UM.UW_USER_ID = A1.USER_ID
                                   AND UM.UW_SOURCE_TYPE = '1'
                                   AND UW1.POLICY_DECISION IS NULL
                                   AND UW1.NOW_UW_STATUS_DETAIL = '0114']]>
             <if test=" examineTimeStart != null and examineTimeStart != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') >= #{examineTimeStart} ]]></if>
			<if test=" examineTimeEnd != null and examineTimeEnd  != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') <= #{examineTimeEnd} ]]></if>
                                   ) A  WHERE A.UW_USER_ID = A1.USER_ID
			
           <![CDATA[) AS TOTAL3
  FROM (SELECT DISTINCT U.USER_ID, U.USER_NAME, U.REAL_NAME, U.ORGAN_CODE
          FROM DEV_PAS.T_UDMP_USER            U,
               DEV_UW.T_UDMP_GROUP_USER      GU,
               DEV_UW.T_UDMP_ROLE_GROUP      RG,
               DEV_UW.T_UDMP_GROUP_ROLE      GR,
               DEV_UW.T_UDMP_ROLE_PERMISSION B,
               DEV_UW.T_UDMP_PERMISSION_INFO D
         WHERE U.USER_ID = GU.USER_ID
           AND GU.ROLE_GROUP_ID = RG.ROLE_GROUP_ID
           AND RG.ROLE_GROUP_ID = GR.ROLE_GROUP_ID
           AND GR.ROLE_ID = B.ROLE_ID
           AND B.PERMISSION_TYPE_ID = D.PERMISSION_TYPE_ID
           AND B.PERMISSION_ID = D.PERMISSION_ID
           AND U.USER_DISABLE = 'N']]>
           <if test=" organ_code != null and organ_code !=''"><![CDATA[AND U.ORGAN_CODE like #{organ_code}||'%']]></if>
           ) A1
           	ORDER BY A1.ORGAN_CODE,A1.USER_NAME
           ) t1
            <![CDATA[ where ROWNUM <= #{LESS_NUM}]]> 
	        <![CDATA[ )  B WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 新单工作量统计清单导出 -->
	<select id="uwWorkloadList_export" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT ROWNUM RN, T1.* FROM (SELECT (SELECT A.ORGAN_NAME
          FROM DEV_UW.T_UDMP_ORG A
         WHERE A.ORGAN_CODE = SUBSTR(A1.ORGAN_CODE, 0, 4)) AS ORGAN_NAME,
       A1.REAL_NAME,
       A1.USER_NAME,
       A1.USER_ID,
       (SELECT COUNT(UM.UW_ID)
          FROM DEV_UW.T_UW_WORKLOAD UW1, DEV_UW.T_UW_MASTER UM
         WHERE UW1.UW_ID = UM.UW_ID
           AND UM.UW_USER_ID = A1.USER_ID
           AND UM.UW_SOURCE_TYPE = '1'
           AND UW1.UW_STATUS_DETAIL IN ('0110', '0401')
           AND UW1.NOW_UW_STATUS_DETAIL = '0101']]>
           
        	<if test=" examineTimeStart != null and examineTimeStart != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') >= #{examineTimeStart} ]]></if>
			<if test=" examineTimeEnd != null and examineTimeEnd  != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') <= #{examineTimeEnd} ]]></if>
           
          <![CDATA[ ) AS TOTAL1,
       (SELECT COUNT(UM.UW_ID)
          FROM DEV_UW.T_UW_WORKLOAD UW1, DEV_UW.T_UW_MASTER UM
         WHERE UW1.UW_ID = UM.UW_ID
           AND UM.UW_USER_ID = A1.USER_ID
           AND UM.UW_SOURCE_TYPE = '1'
           AND UW1.POLICY_DECISION IS NULL
           AND UW1.NOW_UW_STATUS_DETAIL = '0114']]>
           <if test=" examineTimeStart != null and examineTimeStart != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') >= #{examineTimeStart} ]]></if>
			<if test=" examineTimeEnd != null and examineTimeEnd  != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') <= #{examineTimeEnd} ]]></if>
           <![CDATA[) AS TOTAL2,
       	(SELECT COUNT(DISTINCT UW_BUSI_ID)
                          FROM (SELECT BP.UW_BUSI_ID, UM.UW_USER_ID
                                  FROM DEV_UW.T_UW_WORKLOAD  UW1,
                                       DEV_UW.T_UW_MASTER    UM,
                                       DEV_UW.T_UW_BUSI_PROD BP
                                 WHERE UW1.UW_ID = UM.UW_ID
                                   AND UM.UW_ID = BP.UW_ID
                                      --AND UM.UW_USER_ID = A1.USER_ID
                                   AND UM.UW_USER_ID IS NOT NULL
                                   AND UM.UW_SOURCE_TYPE = '1'
                                   AND UW1.UW_STATUS_DETAIL IN ('0114', '0401')
                                   AND UW1.NOW_UW_STATUS_DETAIL = '0101']]>
            <if test=" examineTimeStart != null and examineTimeStart != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') >= #{examineTimeStart} ]]></if>
			<if test=" examineTimeEnd != null and examineTimeEnd  != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') <= #{examineTimeEnd} ]]></if>
                               <![CDATA[ UNION ALL
                                SELECT BP.UW_BUSI_ID, UM.UW_USER_ID
                                  FROM DEV_UW.T_UW_WORKLOAD  UW1,
                                       DEV_UW.T_UW_MASTER    UM,
                                       DEV_UW.T_UW_BUSI_PROD BP
                                 WHERE UW1.UW_ID = UM.UW_ID
                                   AND UM.UW_ID = BP.UW_ID
                                   AND UM.UW_USER_ID IS NOT NULL
                                      --AND UM.UW_USER_ID = A1.USER_ID
                                   AND UM.UW_SOURCE_TYPE = '1'
                                   AND UW1.POLICY_DECISION IS NULL
                                   AND UW1.NOW_UW_STATUS_DETAIL = '0114']]>
             <if test=" examineTimeStart != null and examineTimeStart != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') >= #{examineTimeStart} ]]></if>
			<if test=" examineTimeEnd != null and examineTimeEnd  != ''  "><![CDATA[ AND to_char(UW1.INSERT_TIME,'YYYY-MM-DD HH24') <= #{examineTimeEnd} ]]></if>
                                   ) A  WHERE A.UW_USER_ID = A1.USER_ID
			
           <![CDATA[) AS TOTAL3
  FROM (SELECT DISTINCT U.USER_ID, U.USER_NAME, U.REAL_NAME, U.ORGAN_CODE
          FROM DEV_PAS.T_UDMP_USER            U,
               DEV_UW.T_UDMP_GROUP_USER      GU,
               DEV_UW.T_UDMP_ROLE_GROUP      RG,
               DEV_UW.T_UDMP_GROUP_ROLE      GR,
               DEV_UW.T_UDMP_ROLE_PERMISSION B,
               DEV_UW.T_UDMP_PERMISSION_INFO D
         WHERE U.USER_ID = GU.USER_ID
           AND GU.ROLE_GROUP_ID = RG.ROLE_GROUP_ID
           AND RG.ROLE_GROUP_ID = GR.ROLE_GROUP_ID
           AND GR.ROLE_ID = B.ROLE_ID
           AND B.PERMISSION_TYPE_ID = D.PERMISSION_TYPE_ID
           AND B.PERMISSION_ID = D.PERMISSION_ID
           AND U.USER_DISABLE = 'N']]>
           <if test=" organ_code != null and organ_code !=''"><![CDATA[AND U.ORGAN_CODE like #{organ_code}||'%']]></if>
           ) A1
           	ORDER BY A1.ORGAN_CODE,A1.USER_NAME
           ) t1
	</select>
<!-- 查询保全强制人工核保清单数据条数 -->
	<select id="UW_findForceDetialInfomaction_count" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM 
		APP___PAS__DBUSER.t_contract_master cm,
		APP___PAS__DBUSER.T_UW_FORCE_DETAIL B 
		where cm.POLICY_CODE=B.POLICY_CODE
      ]]>
 	<if test=" organ_code != null and organ_code !=''"><![CDATA[ AND cm.organ_code like #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test=" startDate  != null  and  startDate  != ''  "><![CDATA[ AND TRUNC(B.MODIFY_DATE) >=  to_date(#{startDate},'YYYY-MM-DD')]]></if>
		<if test=" endDate  != null  and  endDate  != ''  "><![CDATA[ AND TRUNC(B.MODIFY_DATE) <=  to_date(#{endDate},'YYYY-MM-DD') ]]></if>
	</select>
	
		<!-- 查询保全强制人工核保清单数据 -->
	<select id="UW_findForceDetialInfomaction" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[ SELECT B.FORCE_STATE, B.POLICY_CODE,B.INPUT_DATE, B.MODIFY_DATE  FROM 
		APP___PAS__DBUSER.t_contract_master cm,
		APP___PAS__DBUSER.T_UW_FORCE_DETAIL B 
		where cm.POLICY_CODE=B.POLICY_CODE
        ]]>
 		<if test=" organ_code != null and organ_code !=''"><![CDATA[ AND cm.organ_code like #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test=" startDate  != null  and  startDate  != ''  "><![CDATA[ AND TRUNC(B.MODIFY_DATE) >=  to_date(#{startDate},'YYYY-MM-DD')]]></if>
		<if test=" endDate  != null  and  endDate  != ''  "><![CDATA[ AND TRUNC(B.MODIFY_DATE) <=  to_date(#{endDate},'YYYY-MM-DD') ]]></if>
	</select>
	
		<!-- 导出保全强制人工核保数据 -->
	<select id="UW_exportUwListforceDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ select ROWNUM as RN,B.FORCE_STATE,B.POLICY_CODE,B.INPUT_DATE, B.MODIFY_DATE FROM 
          APP___PAS__DBUSER.T_CONTRACT_MASTER cm,
          APP___PAS__DBUSER.T_UW_FORCE_DETAIL B
          where cm.POLICY_CODE=B.POLICY_CODE]]>
		<if test=" organ_code != null and organ_code !=''"><![CDATA[ AND cm.organ_code like #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test=" startDate  != null  and  startDate  != ''  "><![CDATA[ AND TRUNC(B.MODIFY_DATE) >=  to_date(#{startDate},'YYYY-MM-DD')]]></if>
		<if test=" endDate  != null  and  endDate  != ''  "><![CDATA[ AND TRUNC(B.MODIFY_DATE) <=  to_date(#{endDate},'YYYY-MM-DD') ]]></if>
	</select>	
	
	<!-- 理赔核保清单查询 -->
	<select id="queryClmUwList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (select ROWNUM AS RN, t.*
          from (SELECT UP.ORGAN_CODE,
			       UM.BIZ_CODE,
			       UP.POLICY_CODE,
			       UM.BIZ_DATE,
			       UM.UW_USER_ID,
			       UBP.BUSI_PROD_CODE,
			       TBP.PRODUCT_NAME_SYS,
			       UBP.DECISION_CODE,
			       UU.USER_NAME,
			       SUBSTR(UP.ORGAN_CODE,1,4) AS FGS_ORGAN_CODE,
                   (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(UP.ORGAN_CODE,1,4)) AS FGS_ORGAN_NAME,
                   SUBSTR(UP.ORGAN_CODE,1,6) AS ZHGS_ORGAN_CODE,
                   (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(UP.ORGAN_CODE,1,6)) AS ZHGS_ORGAN_NAME,
                   UP.ORGAN_CODE AS SALES_ORGAN_CODE,
                   (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = UP.ORGAN_CODE) AS SALES_ORGAN_NAME,
                   (SELECT DECISION_DESC FROM DEV_PAS.T_PRODUCT_DECISION WHERE DECISION_CODE = UBP.DECISION_CODE) AS DECISION_DESC 
			  FROM DEV_UW.T_UW_MASTER         UM,
			       DEV_UW.T_UW_POLICY         UP,
			       DEV_UW.T_UW_BUSI_PROD      UBP,
			       DEV_PDS.T_BUSINESS_PRODUCT TBP,
			       DEV_PAS.T_UDMP_USER UU
			   WHERE UM.UW_ID = UP.UW_ID
		         AND UM.UW_USER_ID = UU.USER_ID
		         AND UM.UW_SOURCE_TYPE = 4
		         AND UP.UW_SOURCE_TYPE = 4
		         AND UM.UW_ID = UBP.UW_ID
		         AND UP.UW_ID = UBP.UW_ID
		         AND UP.APPLY_CODE = UBP.APPLY_CODE
		         AND UBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS ]]>
		<if test="organ_code !=null and organ_code != ''"> <![CDATA[ AND UP.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test="uw_user_id !=null"><![CDATA[ AND UM.UW_USER_ID = #{uw_user_id}]]></if>
		<if test=" uwDecisionList != null">
			<![CDATA[ AND UP.POLICY_DECISION IN ]]>
			<foreach collection="uwDecisionList" index="index" item="uwDecision" open="(" separator="," close=")">
                #{uwDecision}       
   	 		</foreach>
		</if>
		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND TRUNC(UM.UW_FINISH_TIME) >= TRUNC(#{uw_time_start}) ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND TRUNC(UM.UW_FINISH_TIME) <= TRUNC(#{uw_time_end})]]></if>
		<if test=" user_name  != null  and  user_name  != ''  "><![CDATA[ AND UU.USER_NAME LIKE #{user_name,jdbcType=VARCHAR}||'%']]></if>
	  <![CDATA[  ORDER BY UM.BIZ_DATE) T WHERE ROWNUM <= #{LESS_NUM}) A WHERE A.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 理赔核保清单查询 查询个数 -->
	<select id="queryClmUwListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(*) FROM ( SELECT UP.ORGAN_CODE,
			       UM.BIZ_CODE,
			       UP.POLICY_CODE,
			       UM.BIZ_DATE,
			       UM.UW_USER_ID,
			       UBP.BUSI_PROD_CODE,
			       TBP.PRODUCT_NAME_SYS,
			       UBP.DECISION_CODE,
			       UU.USER_NAME
			  FROM DEV_UW.T_UW_MASTER         UM,
			       DEV_UW.T_UW_POLICY         UP,
			       DEV_UW.T_UW_BUSI_PROD      UBP,
			       DEV_PDS.T_BUSINESS_PRODUCT TBP,
			       DEV_PAS.T_UDMP_USER UU
			   WHERE UM.UW_ID = UP.UW_ID
		         AND UM.UW_USER_ID = UU.USER_ID
		         AND UM.UW_SOURCE_TYPE = 4
		         AND UP.UW_SOURCE_TYPE = 4
		         AND UM.UW_ID = UBP.UW_ID
		         AND UP.UW_ID = UBP.UW_ID
		         AND UP.APPLY_CODE = UBP.APPLY_CODE
		         AND UBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS]]>
		<if test="organ_code !=null and organ_code != ''"> <![CDATA[ AND UP.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test="uw_user_id !=null"><![CDATA[ AND UM.UW_USER_ID = #{uw_user_id}]]></if>
		<if test=" uwDecisionList != null">
			<![CDATA[ AND UP.POLICY_DECISION IN ]]>
			<foreach collection="uwDecisionList" index="index" item="uwDecision" open="(" separator="," close=")">
                #{uwDecision}       
   	 		</foreach>
		</if>
		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND TRUNC(UM.UW_FINISH_TIME) >= TRUNC(#{uw_time_start}) ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND TRUNC(UM.UW_FINISH_TIME) <= TRUNC(#{uw_time_end})]]></if>
		<if test=" user_name  != null  and  user_name  != ''  "><![CDATA[ AND UU.USER_NAME LIKE #{user_name,jdbcType=VARCHAR}||'%']]></if>
		<![CDATA[ ) ]]>
	</select>
	<!-- exportClmUwList 理赔核保清单导出 -->
	<select id="exportClmUwList"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT UP.ORGAN_CODE,
			       UM.BIZ_CODE,
			       UP.POLICY_CODE,
			       UM.BIZ_DATE,
			       UM.UW_USER_ID,
			       UBP.BUSI_PROD_CODE,
			       TBP.PRODUCT_NAME_SYS,
			       UBP.DECISION_CODE, 
			       UU.USER_NAME,
			       SUBSTR(UP.ORGAN_CODE,1,4) AS FGS_ORGAN_CODE,
                   (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(UP.ORGAN_CODE,1,4)) AS FGS_ORGAN_NAME,
                   SUBSTR(UP.ORGAN_CODE,1,6) AS ZHGS_ORGAN_CODE,
                   (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = SUBSTR(UP.ORGAN_CODE,1,6)) AS ZHGS_ORGAN_NAME,
                   UP.ORGAN_CODE AS SALES_ORGAN_CODE,
                   (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG WHERE ORGAN_CODE = UP.ORGAN_CODE) AS SALES_ORGAN_NAME,
                   (SELECT DECISION_DESC FROM DEV_PAS.T_PRODUCT_DECISION WHERE DECISION_CODE = UBP.DECISION_CODE) AS DECISION_DESC 
			  FROM DEV_UW.T_UW_MASTER         UM,
			       DEV_UW.T_UW_POLICY         UP,
			       DEV_UW.T_UW_BUSI_PROD      UBP,
			       DEV_PDS.T_BUSINESS_PRODUCT TBP,
			       DEV_PAS.T_UDMP_USER UU
			   WHERE UM.UW_ID = UP.UW_ID
		         AND UM.UW_USER_ID = UU.USER_ID
		         AND UM.UW_SOURCE_TYPE = 4
		         AND UP.UW_SOURCE_TYPE = 4
		         AND UM.UW_ID = UBP.UW_ID
		         AND UP.UW_ID = UBP.UW_ID
		         AND UP.APPLY_CODE = UBP.APPLY_CODE
		         AND UBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS]]>
		<if test="organ_code !=null and organ_code != ''"> <![CDATA[ AND UP.ORGAN_CODE LIKE #{organ_code,jdbcType=VARCHAR}||'%']]></if>
		<if test="uw_user_id !=null"><![CDATA[ AND UM.UW_USER_ID = #{uw_user_id}]]></if>
		<if test=" uwDecisionList != null">
			<![CDATA[ AND UP.POLICY_DECISION IN ]]>
			<foreach collection="uwDecisionList" index="index" item="uwDecision" open="(" separator="," close=")">
                #{uwDecision}       
   	 		</foreach>
		</if>
		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND TRUNC(UM.UW_FINISH_TIME) >= TRUNC(#{uw_time_start}) ]]></if>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND TRUNC(UM.UW_FINISH_TIME) <= TRUNC(#{uw_time_end})]]></if>
		<if test=" user_name  != null  and  user_name  != ''  "><![CDATA[ AND UU.USER_NAME LIKE #{user_name,jdbcType=VARCHAR}||'%']]></if>
	  <![CDATA[  ORDER BY UM.BIZ_DATE ]]>
	</select>
	
	
	<!-- 查询个数操作 -->
	<select id="NB_findYbtClmnostandInfoBackupTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(*) FROM 
			   DEV_NB.T_YBT_CLMNOSTAND_INFO_BACKUP A,
		       DEV_NB.T_NB_CONTRACT_MASTER         B,
		       DEV_NB.T_NB_CONTRACT_PRODUCT        D,
		       DEV_PDS.T_BUSINESS_PRODUCT          BP
		 WHERE A.APPLY_CODE = B.APPLY_CODE
		   AND A.APPLY_CODE = D.APPLY_CODE
		   AND BP.PRODUCT_CODE_SYS = '00' || D.PRODUCT_CODE
		   AND BP.PRODUCT_CATEGORY = '10001'
		   AND B.ORGAN_CODE LIKE CONCAT(#{organ_code}, '%')]]>
		   <include refid="queryIssueDateForYbtCom"/>
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryYbtClmnostandInfoBackupForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT * FROM (SELECT ROWNUM AS RN, NEWLIST.*
          FROM (SELECT A.APPLY_CODE,
                       B.POLICY_CODE,
                       B.ORGAN_CODE,
                       (SELECT C.ORGAN_NAME
                          FROM DEV_NB.T_UDMP_ORG C
                         WHERE C.ORGAN_CODE = B.ORGAN_CODE) AS ORGAN_NAME,
                       D.TOTAL_PREM_AF,
                       BP.PRODUCT_NAME_SYS,
                       (CASE
                         WHEN D.CHARGE_PERIOD = 0 THEN
                          '无关'
                         WHEN D.CHARGE_PERIOD = 1 THEN
                          '趸交'
                         WHEN D.CHARGE_PERIOD = 2 THEN
                          D.CHARGE_YEAR || '年'
                         WHEN D.CHARGE_PERIOD = 3 THEN
                          '交至' || D.CHARGE_YEAR || '岁'
                         WHEN D.CHARGE_PERIOD = 4 THEN
                          '终身交费'
                         WHEN D.CHARGE_PERIOD = 5 THEN
                          '不定期交'
                         WHEN D.CHARGE_PERIOD = 6 THEN
                          D.CHARGE_YEAR || '月'
                         WHEN D.CHARGE_PERIOD = 7 THEN
                          D.CHARGE_YEAR || '天'
                         ELSE
                          TO_CHAR(D.CHARGE_YEAR)
                       END) AS CHARGE_PERIOD,
                       (SELECT UU.USER_NAME
                          FROM DEV_PAS.T_YBT_CLMNOSTAND_INFO CI,
                               DEV_PAS.T_UDMP_USER           UU
                         WHERE UU.USER_ID = CI.USER_ID
                           AND CI.SINCE_ID = A.SINCE_ID) AS USER_NAME
                  FROM DEV_NB.T_YBT_CLMNOSTAND_INFO_BACKUP A,
                       DEV_NB.T_NB_CONTRACT_MASTER         B,
                       DEV_NB.T_NB_CONTRACT_PRODUCT        D,
                       DEV_PDS.T_BUSINESS_PRODUCT          BP
                 WHERE A.APPLY_CODE = B.APPLY_CODE
                   AND A.APPLY_CODE = D.APPLY_CODE
                   AND BP.PRODUCT_CODE_SYS = '00' || D.PRODUCT_CODE
                   AND BP.PRODUCT_CATEGORY = '10001'
                   AND B.ORGAN_CODE LIKE CONCAT(#{organ_code}, '%')]]>
                   <include refid="queryIssueDateForYbtCom"/><![CDATA[) NEWLIST
         WHERE ROWNUM <= #{LESS_NUM}) LS
 		WHERE LS.RN > #{GREATER_NUM}]]>
	</select>
	
	
	<select id="NB_exportYbtClmnostandInfoBackup" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.APPLY_CODE,
                       B.POLICY_CODE,
                       B.ORGAN_CODE,
                       (SELECT C.ORGAN_NAME
                          FROM DEV_NB.T_UDMP_ORG C
                         WHERE C.ORGAN_CODE = B.ORGAN_CODE) AS ORGAN_NAME,
                       D.TOTAL_PREM_AF,
                       BP.PRODUCT_NAME_SYS,
                       (CASE
                         WHEN D.CHARGE_PERIOD = 0 THEN
                          '无关'
                         WHEN D.CHARGE_PERIOD = 1 THEN
                          '趸交'
                         WHEN D.CHARGE_PERIOD = 2 THEN
                          D.CHARGE_YEAR || '年'
                         WHEN D.CHARGE_PERIOD = 3 THEN
                          '交至' || D.CHARGE_YEAR || '岁'
                         WHEN D.CHARGE_PERIOD = 4 THEN
                          '终身交费'
                         WHEN D.CHARGE_PERIOD = 5 THEN
                          '不定期交'
                         WHEN D.CHARGE_PERIOD = 6 THEN
                          D.CHARGE_YEAR || '月'
                         WHEN D.CHARGE_PERIOD = 7 THEN
                          D.CHARGE_YEAR || '天'
                         ELSE
                          TO_CHAR(D.CHARGE_YEAR)
                       END) AS CHARGE_PERIOD,
                       (SELECT UU.USER_NAME
                          FROM DEV_PAS.T_YBT_CLMNOSTAND_INFO CI,
                               DEV_PAS.T_UDMP_USER           UU
                         WHERE UU.USER_ID = CI.USER_ID
                           AND CI.SINCE_ID = A.SINCE_ID) AS USER_NAME
                  FROM DEV_NB.T_YBT_CLMNOSTAND_INFO_BACKUP A,
                       DEV_NB.T_NB_CONTRACT_MASTER         B,
                       DEV_NB.T_NB_CONTRACT_PRODUCT        D,
                       DEV_PDS.T_BUSINESS_PRODUCT          BP
                 WHERE A.APPLY_CODE = B.APPLY_CODE
                   AND A.APPLY_CODE = D.APPLY_CODE
                   AND BP.PRODUCT_CODE_SYS = '00' || D.PRODUCT_CODE
                   AND BP.PRODUCT_CATEGORY = '10001'
                   AND B.ORGAN_CODE LIKE CONCAT(#{organ_code}, '%')]]>
                   <include refid="queryIssueDateForYbtCom"/>
	</select>
	
	<sql id="queryIssueDateForYbtCom">
		<if test=" examine_time_start != null and examine_time_start != '' "><![CDATA[ AND TRUNC(B.ISSUE_DATE) >=  to_date(#{examine_time_start},'YYYY-MM-DD')]]></if>
		<if test=" examine_time_end != null and examine_time_end != '' "><![CDATA[ AND TRUNC(B.ISSUE_DATE) <=  to_date(#{examine_time_end},'YYYY-MM-DD') ]]></if>
	</sql>
	
	
	<select id="UW_queryCreateTimeForAgentExcellent"  resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT MAX(E.UPDATE_TIME) AS CREATE_TIME
		  FROM DEV_UW.T_AGENT_EXCELLENT E
		 WHERE 1 = 1
		   AND (E.BATCH_FLAG = 1 OR E.BATCH_FLAG = 2 OR E.BATCH_FLAG = 3)]]>
		 <if test=" organ_code != null and organ_code !=''"><![CDATA[AND E.AGENT_ORGAN_CODE LIKE #{organ_code}||'%' ]]></if>
         <if test=" agent_channel != null and agent_channel !=''"><![CDATA[ AND RTRIM(E.AGENT_CHANNEL)= #{agent_channel}   ]]></if>
	</select>
</mapper>