<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:batch="http://www.springframework.org/schema/batch"
	xmlns:cache="http://www.springframework.org/schema/cache" xmlns:c="http://www.springframework.org/schema/c"
	xmlns:jaxws="http://cxf.apache.org/jaxws"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
	http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<!-- 保单特约信息查询 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/queryConditionInfoUCCqueryConditionInfoAddr" id="QRY_queryConditionInfo" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.condition.iqueryconditioninfoucc.queryconditioninfo.ws.IQueryConditionInfoUCCWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.condition.iqueryconditioninfoucc.queryconditioninfo.ws.QueryConditionInfoUCCWSImpl" id="QRY_QueryConditionInfoUCCWSImplqueryConditionInfo">
				<property name="queryConditiondUCC" ref="QRY_queryConditiondUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 投保单手机号查询 -->
	<jaxws:endpoint address="/queryAppntAndInsuredPhoneInfoAddr" id="QRY_queryAppntAndInsuredPhoneInfo" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.queryappntandinsuredphone.iqueryappntandinsuredphoneucc.queryappneandinsuredphone.ws.IQueryAppntAndInsuredPhoneUccWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.queryappntandinsuredphone.iqueryappntandinsuredphoneucc.queryappneandinsuredphone.ws.QueryAppntAndInsuredPhoneUccWSImpl" id="QRY_QueryAppntAndInsuredPhoneUCCWSImplqueryAppntAndInsuredPhone">
				<property name="queryAppntAndInsuredPhoneUCC" ref="QRY_queryAppntAndInsuredPhoneUCC">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 客户可申请纸质保单查询 -->
	<!-- address属性是调用webservice时需要使用的地址 -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint address="/policyPrintQueryAddr" id="QRY_policyPrintQuery" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.r13501900400.ipolicyprintqueryucc.querypolicyinfo.ws.IPolicyPrintQueryUccWS">
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类 -->
			<bean class="com.nci.tunan.qry.impl.peripheral.exports.r13501900400.ipolicyprintqueryucc.querypolicyinfo.ws.PolicyPrintQueryUccWSImpl" id="QRY_policyPrintQueryUccWSImplqueryPolicyInfo">
				<property name="policyPrintQueryUcc" ref="QRY_policyPrintQueryUcc">
					
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
		
	<jaxws:endpoint id="queryPolicyTotalPrem" address="/qry_QueryPolicyPremUccqueryPolicyTotalPremAddr" implementorClass="com.nci.tunan.qry.interfaces.nb.exports.iquerypolicypremucc.querypolicytotalprem.ws.IQueryPolicyPremUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyPremUccWSImplqueryPolicyTotalPrem" class="com.nci.tunan.qry.impl.nb.exports.iquerypolicypremucc.querypolicytotalprem.ws.QueryPolicyPremUccWSImpl">
				<property name="ucc">
					<ref bean="qry_QueryPolicyPremUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 非实时承保保单查询接口 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryNBPolicyInfo" address="/qry_QueryNBPolicyInfoUccqueryNBPolicyInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.querynbpolicyinfo.iquerynbpolicyinfoucc.querynbpolicyinfo.ws.IQueryNBPolicyInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryNBPolicyInfoUccWSImplqueryNBPolicyInfo" class="com.nci.tunan.qry.impl.peripheral.exports.querynbpolicyinfo.iquerynbpolicyinfoucc.querynbpolicyinfo.ws.QueryNBPolicyInfoUccWSImpl">
				<property name="ucc">
					<ref bean="qry_QueryNBPolicyInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- 查询保单/投保单信息 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyDetailInfo" address="/qry_QueryPolicyDetailInfoUCCqueryPolicyDetailInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.queryPolicyDetailInfo.iquerypolicydetailinfoucc.querypolicydetailinfo.ws.IQueryPolicyDetailInfoUCCWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyDetailInfoUCCWSImplqueryPolicyDetailInfo" class="com.nci.tunan.qry.impl.peripheral.exports.queryPolicyDetailInfo.iquerypolicydetailinfoucc.querypolicydetailinfo.ws.QueryPolicyDetailInfoUCCWSImpl">
				<property name="ucc">
					<ref bean="qry_QueryPolicyDetailInfoUCC" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 查询保单缴费信息 -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPolicyFeeInfo" address="/qry_QueryPolicyFeeInfoUccqueryPolicyDetailInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.queryPolicyFeeInfo.iquerypolicyfeeinfoucc.querypolicydetailinfo.ws.IQueryPolicyFeeInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPolicyFeeInfoUccWSImplqueryPolicyDetailInfo" class="com.nci.tunan.qry.impl.peripheral.exports.queryPolicyFeeInfo.iquerypolicyfeeinfoucc.querypolicydetailinfo.ws.QueryPolicyFeeInfoUccWSImpl">
				<property name="ucc">
					<ref bean="qry_QueryPolicyFeeInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="policyQueryDelayedInfo" address="/qry_PolicyQueryDelayedInfoUccpolicyQueryDelayedInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.policyQueryDelayedInfo.ipolicyquerydelayedinfoucc.policyquerydelayedinfo.ws.IPolicyQueryDelayedInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="PolicyQueryDelayedInfoUccWSImplpolicyQueryDelayedInfo" class="com.nci.tunan.qry.impl.peripheral.exports.policyQueryDelayedInfo.ipolicyquerydelayedinfoucc.policyquerydelayedinfo.ws.PolicyQueryDelayedInfoUccWSImpl">
				<property name="ucc">
					<ref bean="qry_PolicyQueryDelayedInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryPersonalInsuranceInfo" address="/qry_QueryPersonalInsuranceInfoUccqueryPersonalInsuranceInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.queryPersonalInsuranceInfo.iquerypersonalinsuranceinfoucc.querypersonalinsuranceinfo.ws.IQueryPersonalInsuranceInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryPersonalInsuranceInfoUccWSImplqueryPersonalInsuranceInfo" class="com.nci.tunan.qry.impl.peripheral.exports.queryPersonalInsuranceInfo.iquerypersonalinsuranceinfoucc.querypersonalinsuranceinfo.ws.QueryPersonalInsuranceInfoUccWSImpl">
				<property name="ucc">
					<ref bean="qry_QueryPersonalInsuranceInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 根据保单号查询该保单下的所有已实际收付的费用信息   -->
	<!-- address属性是调用webservice时需要使用的地址  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryCapCashDetailInfo" address="/qry_QueryCapCashDetailInfoUccqueryCapCashDetailInfoAddr" implementorClass="com.nci.tunan.qry.interfaces.peripheral.exports.queryCapCashDetailInfo.iqueryCapCashDetailInfoucc.queryCapCashDetailInfo.ws.IQueryCapCashDetailInfoUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryCapCashDetailInfoUccWSImplqueryCapCashDetailInfo" class="com.nci.tunan.qry.impl.peripheral.exports.queryCapCashDetailInfo.iqueryCapCashDetailInfoucc.queryCapCashDetailInfo.ws.QueryCapCashDetailInfoUccWSImpl">
				<property name="ucc">
					<ref bean="qry_QueryCapCashDetailInfoUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
	
	<!-- 续保审核结论通知书保单查询接口  -->
	<!-- implementorClass 是定义服务的接口类 -->
	<jaxws:endpoint id="queryRenewalExamineDocument" address="/qry_queryRenewalExamineDocumentUccqueryRenewalExamineDocumentAddr" implementorClass="com.nci.tunan.qry.interfaces.nb.exports.iqueryrenewalexaminedocumentucc.queryrenewalexaminedocument.ws.IQueryRenewalExamineDocumentUccWS">		
		<jaxws:implementor>
			<!-- class对应服务发布接口的实现类  -->
			<bean id="QueryRenewalExamineDocumentUccWSImplqueryRenewalExamineDocument" class="com.nci.tunan.qry.impl.nb.exports.iqueryrenewalexaminedocumentucc.queryrenewalexaminedocument.ws.QueryRenewalExamineDocumentUccWSImpl">
				<property name="ucc">
					<ref bean="qry_queryRenewalExamineDocumentUcc" />
				</property>
			</bean>
		</jaxws:implementor>
	</jaxws:endpoint>
</beans>