<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="CLM_ClaimOverDuePayPO">

<!-- 查询超期赔案清单 -->
	<select id="queryOverdueApproveList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
		SELECT  C.RN AS rowNumber,
				 C.CASE_NO,
				 C.CASE_ID,
		         C.INSURED_ID,
		         C.SIGN_ORGAN,
		         C.SIGN_TIME,
		         C.ACCEPT_TIME,
		         C.AUDIT_TIME,
		         C.APPROVE_TIME,
		         C.AUDIT_DECISION,
		         C.ACTUAL_PAY,
		         C.AUDITOR_ID,
		         C.AUDITOR_NAME,
		         C.AUDITOR_ORGAN_CODE,
		         C.APPROVER_ID,
		         C.APPROVER_NAME,
		         C.APPROVER_ORGAN_CODE,
		         C.<PERSON>_REASON,
		         C.OVER_DAYS,
		         C.OVER_DUE_MONEY,
		         C.CLAIM_TYPE_STR,
		         C.PAY_MODE,
		         C.INSURED_NAME,
		         C.PAY_TYPE,
		         C.PAYEE_NAME,
		         C.FINISH_TIME,
		         C.BANK_CODE,
		         C.BANK_ACCOUNT, 
		         C.RELATED_NO FROM (
		SELECT  ROWNUM RN,
				 B.CASE_NO,
				 B.CASE_ID,
		         B.INSURED_ID,
		         B.SIGN_ORGAN,
		         B.SIGN_TIME,
		         B.ACCEPT_TIME,
		         B.AUDIT_TIME,
		         B.APPROVE_TIME,
		         B.AUDIT_DECISION,
		         B.ACTUAL_PAY,
		         B.AUDITOR_ID,
		         B.AUDITOR_NAME,
		         B.AUDITOR_ORGAN_CODE,
		         B.APPROVER_ID,
		         B.APPROVER_NAME,
		         B.APPROVER_ORGAN_CODE,
		         B.OVER_REASON,
		         B.OVER_DAYS,
		         B.OVER_DUE_MONEY,
		         B.CLAIM_TYPE_STR,
		         B.PAY_MODE,
		         B.INSURED_NAME,
		         B.PAY_TYPE,
		         B.PAYEE_NAME,
		         B.FINISH_TIME,
		         B.BANK_CODE,
		         B.BANK_ACCOUNT, 
		         B.RELATED_NO FROM (
			 SELECT *       
   FROM (SELECT ROW_NUMBER() OVER(PARTITION BY cc.CASE_NO ORDER BY cc.FINISH_TIME ASC,cc.BANK_ACCOUNT ASC) rn,       
         cc.*       
         FROM
(SELECT DISTINCT A.CASE_NO,
           A.CASE_ID,
           A.INSURED_ID,
           A.ORGAN_CODE SIGN_ORGAN,
           A.SIGN_TIME,
           A.ACCEPT_TIME,
           A.AUDIT_TIME,
           A.APPROVE_TIME,
           A.AUDIT_DECISION,
           A.ACTUAL_PAY, 
           A.AUDITOR_ID,
           (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.AUDITOR_ID=U.USER_ID)AUDITOR_NAME,
           (SELECT U.ORGAN_CODE FROM DEV_PAS.T_UDMP_USER U WHERE A.AUDITOR_ID=U.USER_ID)AUDITOR_ORGAN_CODE,
           A.APPROVER_ID,
           (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.APPROVER_ID=U.USER_ID)APPROVER_NAME,
           (SELECT U.ORGAN_CODE FROM DEV_PAS.T_UDMP_USER U WHERE A.APPROVER_ID=U.USER_ID)APPROVER_ORGAN_CODE,
           A.OVER_REASON,
           A.OVER_DAYS,
           A.OVER_DUE_MONEY,
           (select listagg(B.claim_type,',') within group (order by B.claim_type) FROM DEV_CLM.T_CLAIM_LIAB  B WHERE B.CASE_ID=A.CASE_ID) CLAIM_TYPE_STR,
           (SELECT U.CUSTOMER_NAME FROM APP___PAS__DBUSER.T_CUSTOMER U WHERE E.CUSTOMER_ID=U.CUSTOMER_ID)INSURED_NAME,
           (SELECT M.NAME FROM DEV_CLM.T_PAY_MODE M WHERE M.CODE = D.PAY_MODE) PAY_TYPE,
           D.PAY_MODE,
           D.PAYEE_NAME,
           D.FINISH_TIME,
           D.BANK_CODE,
           D.BANK_ACCOUNT,
           A.RELATED_NO,
           A.IS_OVER_COMP
           FROM 
           dev_clm.T_CLAIM_CASE  A,
           dev_clm.T_CLAIM_LIAB  B,
           dev_clm.T_PREM_ARAP   D
           ,dev_clm.T_INSURED_LIST E
           WHERE 1=1
           AND B.CASE_ID = A.CASE_ID
           AND A.CASE_ID = E.CASE_ID
           AND A.CASE_NO = D.BUSINESS_CODE
           AND B.POLICY_CODE =D.POLICY_CODE
           AND B.POLICY_CODE=E.POLICY_CODE
           AND A.CASE_APPLY_TYPE = 1
           AND A.IS_OVER_COMP = 1
           ]]>
           	<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like '${organ_code}%' ]]></if>
			<if test=" over_reason != null and over_reason != ''  "><![CDATA[ AND A.OVER_REASON = #{over_reason, jdbcType=VARCHAR} ]]></if>
			<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND TRUNC(A.APPROVE_TIME)>=TO_DATE(#{start_date},'yyyy-MM-dd')]]></if>
			<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND TRUNC(A.APPROVE_TIME)<=TO_DATE(#{end_date},'yyyy-MM-dd') ]]></if>
           <![CDATA[
         order by A.CASE_NO DESC,D.FINISH_TIME ASC,D.BANK_ACCOUNT ASC)cc
         )   WHERE rn = 1 )  B ) C
			WHERE C.RN <= #{LESS_NUM} AND C.RN > #{GREATER_NUM} ]]>
	</select>
	<select id="queryOverdueApproveListTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >
			<![CDATA[ select count(1) from (
			SELECT *       
   FROM (SELECT ROW_NUMBER() OVER(PARTITION BY cc.CASE_NO ORDER BY cc.FINISH_TIME ASC,cc.BANK_ACCOUNT ASC) rn,       
         cc.*       
         FROM
(SELECT DISTINCT A.CASE_NO,
           A.CASE_ID,
           A.INSURED_ID,
           A.ORGAN_CODE SIGN_ORGAN,
           A.SIGN_TIME,
           A.ACCEPT_TIME,
           A.AUDIT_TIME,
           A.APPROVE_TIME,
           A.AUDIT_DECISION,
           A.ACTUAL_PAY, 
           A.AUDITOR_ID,
           (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.AUDITOR_ID=U.USER_ID)AUDITOR_NAME,
           (SELECT U.ORGAN_CODE FROM DEV_PAS.T_UDMP_USER U WHERE A.AUDITOR_ID=U.USER_ID)AUDITOR_ORGAN_CODE,
           A.APPROVER_ID,
           (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.APPROVER_ID=U.USER_ID)APPROVER_NAME,
           (SELECT U.ORGAN_CODE FROM DEV_PAS.T_UDMP_USER U WHERE A.APPROVER_ID=U.USER_ID)APPROVER_ORGAN_CODE,
           A.OVER_REASON,
           A.OVER_DAYS,
           A.OVER_DUE_MONEY,
           (select listagg(B.claim_type,',') within group (order by B.claim_type) FROM DEV_CLM.T_CLAIM_LIAB  B WHERE B.CASE_ID=A.CASE_ID) CLAIM_TYPE_STR,
           (SELECT U.CUSTOMER_NAME FROM APP___PAS__DBUSER.T_CUSTOMER U WHERE E.CUSTOMER_ID=U.CUSTOMER_ID)INSURED_NAME,
           (SELECT M.NAME FROM dev_clm.T_PAY_MODE M WHERE M.CODE = D.PAY_MODE) PAY_TYPE,
           D.PAY_MODE,
           D.PAYEE_NAME,
           D.FINISH_TIME,
           D.BANK_CODE,
           D.BANK_ACCOUNT,
           A.RELATED_NO,
           A.IS_OVER_COMP
           FROM 
           dev_clm.T_CLAIM_CASE  A,
           dev_clm.T_CLAIM_LIAB  B,
           dev_clm.T_PREM_ARAP   D
           ,dev_clm.T_INSURED_LIST E
           WHERE 1=1
           AND B.CASE_ID = A.CASE_ID
           AND A.CASE_ID = E.CASE_ID
           AND A.CASE_NO = D.BUSINESS_CODE
           AND B.POLICY_CODE =D.POLICY_CODE
           AND B.POLICY_CODE=E.POLICY_CODE
           AND A.CASE_APPLY_TYPE = 1
           AND A.IS_OVER_COMP = 1
           ]]>
           	<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like '${organ_code}%' ]]></if>
			<if test=" over_reason != null and over_reason != ''  "><![CDATA[ AND A.OVER_REASON = #{over_reason, jdbcType=VARCHAR} ]]></if>
			<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND TRUNC(A.APPROVE_TIME)>=TO_DATE(#{start_date},'yyyy-MM-dd')]]></if>
			<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND TRUNC(A.APPROVE_TIME)<=TO_DATE(#{end_date},'yyyy-MM-dd') ]]></if>
           <![CDATA[
         order by A.CASE_NO DESC,D.FINISH_TIME ASC,D.BANK_ACCOUNT ASC)cc
         )   WHERE rn = 1 )B]]>
	</select>
	<!-- 超期支付赔案清单 -->
	<select id="queryOverduePayList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
		SELECT D.RN AS rowNumber,D.CASE_NO,D.CASE_ID,D.INSURED_ID,D.SIGN_ORGAN,D.SIGN_ORGAN_NAME,D.ACCEPT_TIME,
             D.APPROVE_TIME,D.AUDIT_DECISION,D.ACTUAL_PAY,D.AUDITOR_ID,D.AUDITOR_NAME,D.APPROVER_ID,
             D.APPROVER_NAME,D.REGISTER_ID,D.REGISTER_NAME,D.OVER_REASON,D.OVER_DAYS,D.OVER_DUE_MONEY,
             D.IS_BPO,D.CLAIM_TYPE_STR,D.PAY_MODE,D.CUSTOMER_ID,D.INSURED_NAME,D.PAY_TYPE,
             D.PAYEE_NAME,D.FINISH_TIME,D.BANK_CODE,D.BANK_ACCOUNT ,D.FEE_STATUS,D.UNIT_NUMBER,
             D.BANK_TEXT_STATUS,D.FUNDS_RTN_CODE,D.LAST_FINISH_TIME,D.OVER_PAY_REASON,D.OVER_PAY_MONEY,D.OVER_PAY_FLAG,D.ACQUIST_WAY
             FROM (
		SELECT 
		ROWNUM RN,C.CASE_NO,C.CASE_ID,C.INSURED_ID,C.SIGN_ORGAN,C.SIGN_ORGAN_NAME,C.ACCEPT_TIME,
             C.APPROVE_TIME,C.AUDIT_DECISION,C.ACTUAL_PAY,C.AUDITOR_ID,C.AUDITOR_NAME,C.APPROVER_ID,
             C.APPROVER_NAME,C.REGISTER_ID,C.REGISTER_NAME,C.OVER_REASON,C.OVER_DAYS,C.OVER_DUE_MONEY,
             C.IS_BPO,C.CLAIM_TYPE_STR,C.PAY_MODE,C.CUSTOMER_ID,C.INSURED_NAME,C.PAY_TYPE,
             C.PAYEE_NAME,C.FINISH_TIME,C.BANK_CODE,C.BANK_ACCOUNT ,C.FEE_STATUS,C.UNIT_NUMBER,
             C.BANK_TEXT_STATUS,C.FUNDS_RTN_CODE,C.LAST_FINISH_TIME,C.OVER_PAY_REASON,C.OVER_PAY_MONEY,C.OVER_PAY_FLAG,C.ACQUIST_WAY
             FROM (
		SELECT 
         B.CASE_NO, B.CASE_ID,B.INSURED_ID,B.SIGN_ORGAN,B.SIGN_ORGAN_NAME,B.ACCEPT_TIME,
             B.APPROVE_TIME,B.AUDIT_DECISION,B.AUDITOR_ID,
             B.AUDITOR_NAME,B.APPROVER_ID,B.APPROVER_NAME,B.REGISTER_ID,
             B.REGISTER_NAME, B.OVER_REASON,B.OVER_DAYS, B.OVER_DUE_MONEY,
             B.IS_BPO,B.CLAIM_TYPE_STR, B.PAY_MODE,B.CUSTOMER_ID,
             B.INSURED_NAME, B.PAY_TYPE,B.PAYEE_NAME,B.FINISH_TIME,
             B.BANK_CODE, B.BANK_ACCOUNT ,B.FEE_STATUS, B.UNIT_NUMBER,B.BANK_TEXT_STATUS,
             B.FUNDS_RTN_CODE,B.OVER_PAY_REASON,B.OVER_PAY_FLAG,SUM(B.OVER_PAY_MONEY) AS OVER_PAY_MONEY,
             B.ACTUAL_PAY,B.ACQUIST_WAY,
              (select max(B.FINISH_TIME) as LAST_FINISH_TIME from DEV_CLM.T_PREM_ARAP  PA          where 1=1  
                AND B.CASE_NO = PA.BUSINESS_CODE 
                            group by  PA.BUSINESS_CODE ) LAST_FINISH_TIME
             FROM (
       SELECT DISTINCT  A.CASE_NO,
       					 A.CASE_ID,
                         A.INSURED_ID,
                         A.ORGAN_CODE SIGN_ORGAN,
                         (SELECT F.ORGAN_NAME FROM DEV_CLM.T_UDMP_ORG F WHERE A.ORGAN_CODE=F.ORGAN_CODE)SIGN_ORGAN_NAME,
                         A.ACCEPT_TIME,
                         A.APPROVE_TIME,
                         A.AUDIT_DECISION,
                         A.ACTUAL_PAY + (SELECT case when SUM(COP.OVER_PAY_MONEY) is null then 0 else SUM(COP.OVER_PAY_MONEY) end 
                          FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY COP
                         WHERE COP.CASE_ID = A.CASE_ID) +
                         (case when (select cc.over_due_money from dev_clm.T_claim_case cc where cc.case_no=A.related_no) is null 
                         then 0 else (select cc.over_due_money from dev_clm.T_claim_case cc where cc.case_no=A.related_no) end ) AS ACTUAL_PAY,
                         A.AUDITOR_ID,
                         (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.AUDITOR_ID=U.USER_ID)AUDITOR_NAME,
                         A.APPROVER_ID,
                         (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.APPROVER_ID=U.USER_ID)APPROVER_NAME,
                         A.REGISTER_ID,
                         (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.REGISTER_ID=U.USER_ID)REGISTER_NAME,
                         A.OVER_REASON,
                         A.OVER_DAYS,
                         A.OVER_DUE_MONEY,
                         A.IS_BPO,
                         A.ACQUIST_WAY,
                         (select listagg(B.claim_type,',') within group (order by B.claim_type) FROM DEV_CLM.T_CLAIM_LIAB  B WHERE B.CASE_ID=A.CASE_ID) CLAIM_TYPE_STR,
                         D.PAY_MODE,
                         D.CUSTOMER_ID,
                         (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE D.CUSTOMER_ID=U.USER_ID)INSURED_NAME,
                         (SELECT M.NAME FROM DEV_CLM.T_PAY_MODE M WHERE M.CODE = D.PAY_MODE) PAY_TYPE,
                         D.PAYEE_NAME,
                         D.FINISH_TIME,
                         D.BANK_CODE,
                         D.BANK_ACCOUNT,
                         D.FEE_STATUS,
                         D.UNIT_NUMBER,
                         D.BANK_TEXT_STATUS,
                         D.FUNDS_RTN_CODE,
			             COP.OVER_PAY_REASON,
			             COP.OVER_PAY_MONEY,
			             COP.OVER_PAY_FLAG
           FROM DEV_CLM.T_CLAIM_CASE  A,
                DEV_CLM.T_CLAIM_LIAB  B,
                DEV_CLM.T_CLAIM_PAY CP,
                DEV_CLM.T_INSURED_LIST E,
                DEV_CLM.T_PREM_ARAP   D
                LEFT JOIN DEV_CLM.T_CLAIM_OVERCOMP_PAY COP ON COP.ARAP_LIST_ID = D.LIST_ID
          WHERE 1=1
            AND B.CASE_ID = A.CASE_ID
            AND A.CASE_ID = E.CASE_ID
            AND A.CASE_NO = D.BUSINESS_CODE
            AND CP.UNIT_NUMBER = D.UNIT_NUMBER
            AND B.POLICY_CODE = D.POLICY_CODE
            AND B.POLICY_CODE=E.POLICY_CODE
            AND A.CASE_APPLY_TYPE = 1
            AND D.ARAP_FLAG = 2
            AND D.FEE_TYPE != 'P005110000'
            AND floor(D.FINISH_TIME - A.APPROVE_TIME)>9
             ]]>
        <if test=" over_pay_flag == null or over_pay_flag == '' ">
		<if test=" over_pay_reason != null and over_pay_reason != '' ">
		<![CDATA[
       AND D.LIST_ID IN (SELECT OVERPAY.ARAP_LIST_ID FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE 1=1 AND OVERPAY.OVER_PAY_FLAG = 1  AND COP.OVER_PAY_REASON = #{over_pay_reason} )]]>
       </if>
       </if>
       <if test=" over_pay_flag != null and over_pay_flag != '' and over_pay_flag == '1'.toString() "><![CDATA[
       AND D.LIST_ID IN (SELECT OVERPAY.ARAP_LIST_ID FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE 1=1 AND OVERPAY.OVER_PAY_FLAG = 1 ]]>
		<if test=" over_pay_reason != null and over_pay_reason != ''  "><![CDATA[ AND COP.OVER_PAY_REASON = #{over_pay_reason} ]]></if>
		<![CDATA[) ]]>
       </if>
       <if test=" over_pay_flag != null and over_pay_flag != '' and over_pay_flag == '2'.toString() "><![CDATA[
       AND D.LIST_ID NOT IN (SELECT OVERPAY.ARAP_LIST_ID FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE 1=1 AND OVERPAY.OVER_PAY_FLAG = 1 ]]>
		<![CDATA[) ]]>
       </if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like '${organ_code}%' ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND D.PAY_MODE =#{pay_mode, jdbcType=VARCHAR}]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND TRUNC(A.APPROVE_TIME)>=TO_DATE(#{start_date},'yyyy-MM-dd')]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND TRUNC(A.APPROVE_TIME)<=TO_DATE(#{end_date},'yyyy-MM-dd') ]]></if>
		<![CDATA[
		ORDER BY A.APPROVE_TIME DESC )  B 
		GROUP BY B.CASE_NO, B.CASE_ID,B.INSURED_ID,B.SIGN_ORGAN,B.SIGN_ORGAN_NAME,B.ACCEPT_TIME,
             B.APPROVE_TIME,B.AUDIT_DECISION,B.AUDITOR_ID,
             B.AUDITOR_NAME,B.APPROVER_ID,B.APPROVER_NAME,B.REGISTER_ID,
             B.REGISTER_NAME, B.OVER_REASON,B.OVER_DAYS,B.OVER_DUE_MONEY,
             B.IS_BPO,B.ACQUIST_WAY,B.CLAIM_TYPE_STR, B.PAY_MODE,B.CUSTOMER_ID,
             B.INSURED_NAME, B.PAY_TYPE,B.PAYEE_NAME,B.FINISH_TIME,
             B.BANK_CODE, B.BANK_ACCOUNT ,B.FEE_STATUS, B.UNIT_NUMBER,B.BANK_TEXT_STATUS,
             B.FUNDS_RTN_CODE,B.OVER_PAY_REASON,B.OVER_PAY_FLAG,B.ACTUAL_PAY
		) C ) D
			WHERE D.RN <= #{LESS_NUM} AND D.RN > #{GREATER_NUM} ]]>
	</select>
	<select id="queryOverduePayListTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >
			<![CDATA[ select count(1) from (
			SELECT 
         B.CASE_NO, B.CASE_ID,B.INSURED_ID,B.SIGN_ORGAN,B.SIGN_ORGAN_NAME,B.ACCEPT_TIME,
             B.APPROVE_TIME,B.AUDIT_DECISION,B.AUDITOR_ID,
             B.AUDITOR_NAME,B.APPROVER_ID,B.APPROVER_NAME,B.REGISTER_ID,
             B.REGISTER_NAME, B.OVER_REASON,B.OVER_DAYS, B.OVER_DUE_MONEY,
             B.IS_BPO,B.CLAIM_TYPE_STR, B.PAY_MODE,B.CUSTOMER_ID,
             B.INSURED_NAME, B.PAY_TYPE,B.PAYEE_NAME,B.FINISH_TIME,
             B.BANK_CODE, B.BANK_ACCOUNT ,B.FEE_STATUS, B.UNIT_NUMBER,B.BANK_TEXT_STATUS,
             B.FUNDS_RTN_CODE,B.OVER_PAY_REASON,B.OVER_PAY_FLAG,SUM(B.OVER_PAY_MONEY) AS OVER_PAY_MONEY,
             B.ACTUAL_PAY,B.ACQUIST_WAY,
              (select max(B.FINISH_TIME) as LAST_FINISH_TIME from DEV_CLM.T_PREM_ARAP  PA          where 1=1  
                AND B.CASE_NO = PA.BUSINESS_CODE 
                            group by  PA.BUSINESS_CODE ) LAST_FINISH_TIME
             FROM 
			(SELECT DISTINCT A.CASE_NO,
       					 A.CASE_ID,
                         A.INSURED_ID,
                         A.ORGAN_CODE SIGN_ORGAN,
                         (SELECT F.ORGAN_NAME FROM DEV_CLM.T_UDMP_ORG F WHERE A.ORGAN_CODE=F.ORGAN_CODE)SIGN_ORGAN_NAME,
                         A.ACCEPT_TIME,
                         A.APPROVE_TIME,
                         A.AUDIT_DECISION,
                         A.ACTUAL_PAY + (SELECT case when SUM(COP.OVER_PAY_MONEY) is null then 0 else SUM(COP.OVER_PAY_MONEY) end 
                          FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY COP
                         WHERE COP.CASE_ID = A.CASE_ID) + 
                         (case when (select cc.over_due_money from dev_clm.T_claim_case cc where cc.case_no=A.related_no) is null 
                         then 0 else (select cc.over_due_money from dev_clm.T_claim_case cc where cc.case_no=A.related_no) end ) AS ACTUAL_PAY,
                         A.AUDITOR_ID,
                         (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.AUDITOR_ID=U.USER_ID)AUDITOR_NAME,
                         A.APPROVER_ID,
                         (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.APPROVER_ID=U.USER_ID)APPROVER_NAME,
                         A.REGISTER_ID,
                         (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE A.REGISTER_ID=U.USER_ID)REGISTER_NAME,
                         A.OVER_REASON,
                         A.OVER_DAYS,
                         A.OVER_DUE_MONEY,
                         A.IS_BPO,
                         A.ACQUIST_WAY,
                         (select listagg(B.claim_type,',') within group (order by B.claim_type) FROM DEV_CLM.T_CLAIM_LIAB  B WHERE B.CASE_ID=A.CASE_ID) CLAIM_TYPE_STR,
                         D.PAY_MODE,
                         D.CUSTOMER_ID,
                         (SELECT U.REAL_NAME FROM DEV_PAS.T_UDMP_USER U WHERE D.CUSTOMER_ID=U.USER_ID)INSURED_NAME,
                         (SELECT M.NAME FROM DEV_CLM.T_PAY_MODE M WHERE M.CODE = D.PAY_MODE) PAY_TYPE,
                         D.PAYEE_NAME,
                         D.FINISH_TIME,
                         D.BANK_CODE,
                         D.BANK_ACCOUNT,
                         D.FEE_STATUS,
                         D.UNIT_NUMBER,
                         D.BANK_TEXT_STATUS,
                         D.FUNDS_RTN_CODE,
			             COP.OVER_PAY_REASON,
			             COP.OVER_PAY_MONEY,
			             COP.OVER_PAY_FLAG
           FROM DEV_CLM.T_CLAIM_CASE  A,
                DEV_CLM.T_CLAIM_LIAB  B,
                DEV_CLM.T_CLAIM_PAY CP,
                DEV_CLM.T_INSURED_LIST E,
                DEV_CLM.T_PREM_ARAP   D
                LEFT JOIN DEV_CLM.T_CLAIM_OVERCOMP_PAY COP ON COP.ARAP_LIST_ID = D.LIST_ID
          WHERE 1=1
            AND B.CASE_ID = A.CASE_ID
            AND A.CASE_ID = E.CASE_ID
            AND A.CASE_NO = D.BUSINESS_CODE
            AND B.POLICY_CODE = D.POLICY_CODE
            AND CP.UNIT_NUMBER = D.UNIT_NUMBER
            AND B.POLICY_CODE=E.POLICY_CODE
            AND A.CASE_APPLY_TYPE = 1
            AND D.ARAP_FLAG = 2
            AND D.FEE_TYPE != 'P005110000'
            AND floor(D.FINISH_TIME - A.APPROVE_TIME)>9
            ]]>
       <if test=" over_pay_flag == null or over_pay_flag == '' ">
		<if test=" over_pay_reason != null and over_pay_reason != '' ">
		<![CDATA[
       AND D.LIST_ID IN (SELECT OVERPAY.ARAP_LIST_ID FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE 1=1  AND OVERPAY.OVER_PAY_FLAG = 1 AND COP.OVER_PAY_REASON = #{over_pay_reason} )]]>
       </if>
       </if>
       <if test=" over_pay_flag != null and over_pay_flag != '' and over_pay_flag == '1'.toString() "><![CDATA[
       AND D.LIST_ID IN (SELECT OVERPAY.ARAP_LIST_ID FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE 1=1  AND OVERPAY.OVER_PAY_FLAG = 1 ]]>
			<if test=" over_pay_reason != null and over_pay_reason != ''  "><![CDATA[ AND COP.OVER_PAY_REASON = #{over_pay_reason} ]]></if>
			<![CDATA[) ]]>
       </if>
      <if test=" over_pay_flag != null and over_pay_flag != '' and over_pay_flag == '2'.toString() "><![CDATA[
       AND D.LIST_ID NOT IN (SELECT OVERPAY.ARAP_LIST_ID FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE 1=1 AND OVERPAY.OVER_PAY_FLAG = 1 ]]>
		<![CDATA[) ]]>
       </if>
			<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like '${organ_code}%' ]]></if>
			<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND D.PAY_MODE =#{pay_mode, jdbcType=VARCHAR}]]></if>
			<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND TRUNC(A.APPROVE_TIME)>=TO_DATE(#{start_date},'yyyy-MM-dd')]]></if>
			<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND TRUNC(A.APPROVE_TIME)<=TO_DATE(#{end_date},'yyyy-MM-dd') ]]></if>
	        <![CDATA[  ORDER BY A.APPROVE_TIME DESC )B
	        GROUP BY B.CASE_NO, B.CASE_ID,B.INSURED_ID,B.SIGN_ORGAN,B.SIGN_ORGAN_NAME,B.ACCEPT_TIME,
             B.APPROVE_TIME,B.AUDIT_DECISION,B.AUDITOR_ID,
             B.AUDITOR_NAME,B.APPROVER_ID,B.APPROVER_NAME,B.REGISTER_ID,
             B.REGISTER_NAME, B.OVER_REASON,B.OVER_DAYS, B.OVER_DUE_MONEY,
             B.IS_BPO,B.ACQUIST_WAY,B.CLAIM_TYPE_STR, B.PAY_MODE,B.CUSTOMER_ID,
             B.INSURED_NAME, B.PAY_TYPE,B.PAYEE_NAME,B.FINISH_TIME,
             B.BANK_CODE, B.BANK_ACCOUNT ,B.FEE_STATUS, B.UNIT_NUMBER,B.BANK_TEXT_STATUS,
             B.FUNDS_RTN_CODE,B.OVER_PAY_REASON,B.OVER_PAY_FLAG,B.ACTUAL_PAY ) C
	        ]]>        
	</select>
	
	<!-- 根据返回信息代码查询支付失败原因 -->
	<select id="findPayeeFailByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BANK_RET_NAME FROM DEV_PAS.T_BANK_RET_CONF A WHERE A.BANK_RET_CODE =#{bank_ret_code}
		]]>
	</select>
	
		<!-- 根据UnitNumber查询支付失败原因 -->
	<select id="findPayeeFailByUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.RETURN_MSG,T.RETURN_CODE,T.STATUS FROM DEV_CAP.T_FMS_INTERFACE T  WHERE T.UNIT_NUMBER = #{unit_number}
		]]>
	</select>
	
	<!-- 查询单证数据 -->
	<select id="findAllClaimChecklist" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUP_CANCEL_REASON, A.SUP_FALG, A.CHECKLIST_ID, A.CASE_ID, 
			A.SUP_SCAN_REASON, A.CHECKLIST_CODE, A.LACK_REASON, A.IS_MANUAL, A.IS_CHECKED, A.DOC_SUBM_MODE, 
			A.CHECKLIST_OPTION, A.CHECKLIST_STATUS, A.LIST_UNIT FROM DEV_CLM.T_CLAIM_CHECKLIST A WHERE ROWNUM <=  1000  ]]>
		 <if test=" sup_falg  != null "><![CDATA[ AND A.SUP_FALG = #{sup_falg} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<![CDATA[ ORDER BY A.CHECKLIST_ID ]]> 
	</select>
	
		<!-- 查询首次补充单证问题件关闭日期(首次补充单证回销日期) -->
	<select id="findClaimChecklistDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT min(a.update_time) first_date FROM DEV_CLM.T_CLAIM_CHECKLIST a  WHERE a.sup_falg ='1'    ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</select>
	
	<!-- 查询单条理赔回退申请数据 -->	
	<select id="findClaimBackApplyByApplyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BACK_DESC, A.CONCLUSION_AF, A.TERMINATE_FLAG, A.BACK_REMARK, A.BACK_REASON, A.APPLY_DATE, 
			A.APPLY_BY, A.CASE_ID, A.ORGAN_CODE, A.CASE_CONCLUSION, A.CASE_PAY, 
			A.BACK_STATUS, A.APPLY_ID ,A.UPDATE_TIME FROM DEV_CLM.T_CLAIM_BACK_APPLY A WHERE 1 = 1  ]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	<!-- 查询赔案是否有超期支付 -->
	<select id="findClaimOverDuePayByCaseId" resultType="java.lang.Integer" parameterType="java.util.Map">
	<![CDATA[ 
	SELECT COUNT(1) FROM(
	SELECT OVERPAY.ARAP_LIST_ID FROM DEV_CLM.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE 1=1 AND OVERPAY.OVER_PAY_FLAG = 1
       AND OVERPAY.CASE_ID = #{case_id} ) A]]>
	</select>
</mapper>