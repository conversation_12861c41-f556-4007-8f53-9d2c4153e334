<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.clm">
	<!-- 理赔综合查询-理赔查询 start  -->
      <sql id="queryClmInfoMain">
        SELECT ROWNUM RN, B.*
          FROM (SELECT CC.CASE_ID,
          CASE WHEN CC.REALTIME_PAY IS NULL THEN 0 ELSE CC.REALTIME_PAY END AS REALTIME_PAY,
           CA.ACCIDENT_NO,
           /*TC.CLAIM_TYPE,*/
           CC.ORGAN_CODE,
           TO_CHAR(CC.END_CASE_TIME, 'yyyy-MM-dd') END_CASE_TIME,
          
           CC.CASE_NO,
           CC.CASE_STATUS,
           (CASE (SELECT COUNT(1)
                FROM DEV_CLM.t_claim_uw uw
               WHERE uw.CASE_NO = CC.CASE_NO)
             WHEN 0 THEN
              '否'
             ELSE
              '是'
           END) UW_STATUS,
           (CASE (SELECT COUNT(1)
                FROM DEV_CLM.t_survey_apply SA
               WHERE SA.CASE_NO = CC.CASE_NO)
             WHEN 0 THEN
              '否'
             ELSE
              '是'
           END) CHAKAN,
          /*  (SELECT WMSYS.WM_CONCAT(SC.CLAIM_TYPE || ',') CLMTYPE
              FROM DEV_CLM.T_CLAIM_SUB_CASE SC
             WHERE SC.CASE_ID = CC.CASE_ID), */
           CA.INSURED_ID,
           CM.CUSTOMER_NAME,
           CM.CUSTOMER_CERTI_CODE,
           CC.GREEN_FLAG,
           CC.RPTR_TIME,
           CC.REGISTE_TIME,
           (SELECT nvl(SUM(PA.FEE_AMOUNT)*-1,0) FROM DEV_CAP.V_PREM_ARAP  PA WHERE PA.BUSINESS_CODE=CC.CASE_NO
            AND PA.ARAP_FLAG=1 AND PA.PAY_MODE!='99'  AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14') AS ACTUAL_PAY1,
            (SELECT nvl(SUM(PA.FEE_AMOUNT),0)  FROM DEV_CAP.V_PREM_ARAP  PA WHERE PA.BUSINESS_CODE=CC.CASE_NO
            AND PA.ARAP_FLAG=2 AND PA.PAY_MODE!='99'  AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14') AS ACTUAL_PAY2,
            (SELECT count(*) FROM DEV_CAP.V_PREM_ARAP  PA WHERE PA.BUSINESS_CODE=CC.CASE_NO  AND PA.ARAP_FLAG=2  AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14' AND PA.FEE_AMOUNT>0) AS PAY_COUNT,
            (SELECT COUNT(*) FROM  DEV_CAP.V_PREM_ARAP PA WHERE PA.BUSINESS_CODE=CC.CASE_NO AND PA.FEE_STATUS!='01' AND PA.ARAP_FLAG=2  AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14' AND PA.FEE_AMOUNT>0) AS UN_PAY_COUNT ,
           NVL(NVL(NVL(NVL(NVL(NVL(NVL(END_CASE_TIME,APPROVE_TIME),AUDIT_TIME),REGISTE_CONF_TIME),SIGN_TIME),DOOR_SIGN_TIME),ACCEPT_TIME),RPTR_TIME) AS STATUS_TIME,
           CC.AUDIT_DECISION
      FROM DEV_CLM.T_CLAIM_CASE CC
      left join DEV_CLM.T_CLAIM_ACCIDENT CA
        on CC.ACCIDENT_ID = CA.ACCIDENT_ID
        LEFT JOIN (SELECT CP.POLICY_CODE,CP.CASE_ID FROM DEV_CLM.T_CLAIM_POLICY CP UNION SELECT CCM.POLICY_CODE,CCM.CASE_ID FROM DEV_CLM.T_CONTRACT_MASTER CCM) BPP
                        ON BPP.CASE_ID = CC.CASE_ID
      /*left join DEV_CLM.T_CLAIM_SUB_CASE TC*/
       /* on CC.CASE_ID = TC.CASE_ID*/
      left join DEV_CLM.T_CUSTOMER CM
        ON CC.INSURED_ID = CM.CUSTOMER_ID
     WHERE 1 = 1 AND CC.CASE_STATUS != '99'
       </sql>

	<select id="queryClmInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
        <!-- <![CDATA[ 
        SELECT C.*,
          (CASE WHEN C.PAY_COUNT<=0 THEN null ELSE (C.ACTUAL_PAY2+C.ACTUAL_PAY1) END) AS ACTUAL_PAY,
          (CASE WHEN C.PAY_COUNT<=0 THEN null WHEN C.UN_PAY_COUNT>0 AND C.PAY_COUNT>0 THEN '未支付' WHEN C.UN_PAY_COUNT<=0 AND C.PAY_COUNT>0 THEN '已支付'  END) AS FEE_STATUS
  FROM ( 
   ]]> -->
   		<![CDATA[
   			 SELECT C.*, C.actual_pay,
   			 (CASE WHEN C.PAY_COUNT<=0 THEN null WHEN C.UN_PAY_COUNT>0 AND C.PAY_COUNT>0 THEN '未支付' WHEN C.UN_PAY_COUNT<=0 AND C.PAY_COUNT>0 THEN '已支付'  END) AS FEE_STATUS
   			 from( SELECT ROWNUM RN, B.*
          FROM (SELECT distinct( CC.CASE_ID),
           CA.ACCIDENT_NO,
           TC.CLAIM_TYPE,
           CC.ORGAN_CODE,
           TO_CHAR(CC.END_CASE_TIME, 'yyyy-MM-dd') END_CASE_TIME,
          
           CC.CASE_NO,
           CC.CASE_STATUS,
           (CASE
                WHEN CC.REALTIME_PAY IS NULL THEN
                 0
                ELSE
                 CC.REALTIME_PAY
              END) REALTIME_PAY,
           (CASE (SELECT COUNT(1)
                FROM DEV_CLM.t_claim_uw uw
               WHERE uw.CASE_NO = CC.CASE_NO)
             WHEN 0 THEN
              '否'
             ELSE
              '是'
           END) UW_STATUS,
           (CASE (SELECT COUNT(1)
                FROM DEV_CLM.t_survey_apply SA
               WHERE SA.CASE_NO = CC.CASE_NO)
             WHEN 0 THEN
              '否'
             ELSE
              '是'
           END) CHAKAN,
          /*  (SELECT WMSYS.WM_CONCAT(SC.CLAIM_TYPE || ',') CLMTYPE
              FROM DEV_CLM.T_CLAIM_SUB_CASE SC
             WHERE SC.CASE_ID = CC.CASE_ID), */
           CA.INSURED_ID,
           SUBSTR(CM.CUSTOMER_NAME, 1, 1) || DECODE(LENGTH(CM.CUSTOMER_NAME),'3','****','2','****','4','****')
             || SUBSTR(CM.CUSTOMER_NAME,length(CM.CUSTOMER_NAME),1)  CUSTOMER_NAME,
           CM.CUSTOMER_CERTI_CODE,
           CC.GREEN_FLAG,
           CC.RPTR_TIME,
           CC.REGISTE_TIME,
          (CC.actual_pay+nvl(CC.ADVANCE_PAY,0)) AS actual_pay,            
            (SELECT count(*) FROM DEV_CAP.V_PREM_ARAP  PA WHERE PA.BUSINESS_CODE=CC.CASE_NO  AND PA.ARAP_FLAG=2  AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14' AND PA.FEE_AMOUNT>0) AS PAY_COUNT,
            (SELECT COUNT(*) FROM  DEV_CAP.V_PREM_ARAP PA WHERE PA.BUSINESS_CODE=CC.CASE_NO AND PA.FEE_STATUS!='01' AND PA.ARAP_FLAG=2  AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14' AND PA.FEE_AMOUNT>0) AS UN_PAY_COUNT ,
           to_char(NVL(NVL(NVL(NVL(NVL(NVL(NVL(END_CASE_TIME,
                                                            APPROVE_TIME),
                                                        AUDIT_TIME),
                                                    REGISTE_CONF_TIME),
                                                SIGN_TIME),
                                            DOOR_SIGN_TIME),
                                        ACCEPT_TIME),
                                    RPTR_TIME),'yyyy-MM-dd') AS STATUS_TIME,
           CC.AUDIT_DECISION
      FROM DEV_CLM.T_CLAIM_CASE CC
      left join DEV_CLM.T_CLAIM_ACCIDENT CA
        on CC.ACCIDENT_ID = CA.ACCIDENT_ID
        LEFT JOIN (SELECT CP.POLICY_CODE,CP.CASE_ID FROM DEV_CLM.T_CLAIM_POLICY CP UNION SELECT CCM.POLICY_CODE,CCM.CASE_ID FROM DEV_CLM.T_CONTRACT_MASTER CCM) BPP
                        ON BPP.CASE_ID = CC.CASE_ID
      left join DEV_CLM.T_CLAIM_SUB_CASE TC
        on CC.CASE_ID = TC.CASE_ID
      left join DEV_CLM.T_CUSTOMER CM
        ON CC.INSURED_ID = CM.CUSTOMER_ID
     WHERE 1 = 1 AND CC.CASE_STATUS != '99'
   		 ]]>
       
        <if test=" caseID != null and caseID != ''  "><![CDATA[AND BPP.POLICY_CODE = #{caseID} ]]></if>
        <if test=" case_id != null and case_id != ''  "><![CDATA[AND BPP.CASE_ID = #{case_id} ]]></if>
         <![CDATA[ ) B WHERE ROWNUM <= #{LESS_NUM}  ]]> 
        <!-- <![CDATA[ and ROWNUM <= #{LESS_NUM}  ]]>  -->
        <![CDATA[ )  C   WHERE C.RN > #{GREATER_NUM} ]]>
	</select>
	<select id="queryClmInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
        <![CDATA[ SELECT COUNT(CC.CASE_NO)
        FROM DEV_CLM.T_CLAIM_CASE CC
      left join DEV_CLM.T_CLAIM_ACCIDENT CA
        on CC.ACCIDENT_ID = CA.ACCIDENT_ID
        LEFT JOIN (SELECT CP.POLICY_CODE,CP.CASE_ID FROM DEV_CLM.T_CLAIM_POLICY CP UNION SELECT CCM.POLICY_CODE,CCM.CASE_ID FROM DEV_CLM.T_CONTRACT_MASTER CCM) BPP
                        ON BPP.CASE_ID = CC.CASE_ID
      /*left join DEV_CLM.T_CLAIM_SUB_CASE TC*/
       /* on CC.CASE_ID = TC.CASE_ID*/
      left join DEV_CLM.T_CUSTOMER CM
        ON CC.INSURED_ID = CM.CUSTOMER_ID
     WHERE 1 = 1 AND CC.CASE_STATUS != '99'
		 ]]>
        <if test=" caseID != null and caseID != ''  "><![CDATA[AND BPP.POLICY_CODE = #{caseID} ]]></if>
        <if test=" case_id != null and case_id != ''  "><![CDATA[AND BPP.CASE_ID = #{case_id} ]]></if>
	</select>
	
	<!-- 理赔综合查询-理赔查询 end -->

	<!-- 案件基本信息-申请人列表 start -->
	<select id="queryApplicant" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
       		 SELECT A.LIST_ID,
       		        A.CLMT_NAME,
			        A.CLMT_CERTI_NO,
			        A.CLMT_MP,
			        A.CLMT_PROVINCE || A.CLMT_CITY || A.CLMT_DISTREACT || A.CLMT_STREET ADDRESS,
			        A.CLMT_INSUR_RELATION, 
			        A.CLMT_PROVINCE,       
			        A.CLMT_CITY,          
			        A.CLMT_DISTREACT，     
			        A.CLMT_STREET,         
			        A.CLMT_MAIL           
			   FROM DEV_CLM.T_CLAIM_APPLICANT A
  			   WHERE 1 = 1
		 ]]>
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND A.CASE_ID = #{caseID} ]]></if>
	</select>
	<!-- 案件基本信息-申请人列表 end -->
    <!-- 案件基本信息-申请人详细信息 start -->
    <select id="applicantInfoData" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
             SELECT A.CLMT_NAME,
                    A.CLMT_CERTI_NO,
                    A.CLMT_MP,
                    A.CLMT_PROVINCE || A.CLMT_CITY || A.CLMT_DISTREACT || A.CLMT_STREET ADDRESS,
                    A.CLMT_INSUR_RELATION, 
                    A.CLMT_PROVINCE,       
                    A.CLMT_CITY,          
                    A.CLMT_DISTREACT,     
                    A.CLMT_STREET,         
                    A.CLMT_MAIL,
                    TCC.CASE_APPLY_TYPE  ,
                    TCC.APPLY_DATE,
                    TCC.ACCEPT_TIME,
                    TCC.ORGAN_CODE,
                    (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = TCC.SIGNER_ID) signerCode,
                    TCC.SIGNER_ID,
                    (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = TCC.REGISTER_ID) registerCode,
                    TCC.REGISTER_ID,
                    TCC.GREEN_FLAG        
               FROM DEV_CLM.T_CLAIM_APPLICANT A
               left join DEV_CLM.T_CLAIM_CASE TCC          
                     ON A.CASE_ID = TCC.CASE_ID
               WHERE 1 = 1
         ]]>
        <if test=" applicant_list_id != null and applicant_list_id != ''  "><![CDATA[AND A.LIST_ID = #{applicant_list_id} ]]></if>
    </select>
    <!-- 案件基本信息-申请人详细信息 end --> 
	<!-- 案件基本信息-理赔类型信息列表 start -->
	<select id="querySubCase" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
			SELECT SC.CLAIM_TYPE, 
				   SC.ACC_REASON,
				   SC.CLAIM_DATE, 
				   SC.SUB_CASE_ID, 
			       SC.CASE_ID ,
					TO_CHAR(SC.CLAIM_DATE,'yyyy')-TO_CHAR(C.CUSTOMER_BIRTHDAY,'yyyy') AGE
				  FROM DEV_CLM.T_CLAIM_SUB_CASE SC 
		      LEFT JOIN DEV_CLM.T_CLAIM_CASE CC
		            ON  SC.CASE_ID=CC.CASE_ID
		      LEFT JOIN DEV_CLM.T_CUSTOMER C
             ON CC.INSURED_ID=C.CUSTOMER_ID
			WHERE 1 = 1
		 ]]>
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND SC.CASE_ID = #{caseID} ]]></if>
	</select>
	<!-- 案件基本信息-理赔类型信息列表 end -->

	<!-- 案件基本信息-出险结果信息列表 start -->
	<select id="queryAccidentResult" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
			SELECT DISTINCT AR.ACC_RESULT1,
                   AR.ACC_RESULT2,
                   (SELECT T1.NAME FROM DEV_CLM.T_ACCIDENT1 T1 WHERE T1.CODE = AR.ACC_RESULT1) R1,
                   (SELECT T1.NAME  FROM DEV_CLM.T_ACCIDENT2 T1 WHERE T1.CODE = AR.ACC_RESULT2 AND T1.RELA_CODE=AR.ACC_RESULT1) R2,
                    UU.USER_NAME  UPDATE_NAME   
			FROM   DEV_CLM.T_CLAIM_ACCIDENT_RESULT AR
            LEFT JOIN  DEV_PAS.T_UDMP_USER UU ON AR.UPDATE_BY=UU.USER_ID
			WHERE  1 = 1
		 ]]>
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND AR.CASE_ID = #{caseID} ]]></if>
	</select>
	<!-- 案件基本信息-出险结果信息列表 end -->

	<!-- 案件基本信息-报案信息等 start -->
	<select id="queryApplicantDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
         SELECT 
                        TCA.CLMT_NAME, TCA.CLMT_CERTI_NO,TCA.CLMT_MP,TCA.CLMT_PROVINCE , TCA.CLMT_STREET ,
                        TCA.CLMT_INSUR_RELATION, TCA.CLMT_CITY, TCA.CLMT_DISTREACT ,          TCA.CLMT_MAIL,
                        AA.RPTR_RELATION, AA.RPTR_NAME, AA.RPTR_MP, AA.RPTR_ZIP,AA.RPTR_ADDR,AA.RPTR_EMAIL, AA.REPORT_MODE,AA.CASE_APPLY_TYPE,
                        AA.RPTR_TIME,AA.ORGAN_CODE,AA.ACCIDENT_ID,
                        AA.TRUSTEE_TYPE,AA.TRUSTEE_CODE,AA.TRUSTEE_NAME,AA.TRUSTEE_MP,AA.TRUSTEE_TEL,AA.TRUSTEE_CERTI_TYPE,
                        AA.TRUSTEE_CERTI_CODE, AA.DOOR_SIGN_TIME,AA.APPLY_DATE,AA.ACCEPTOR_ID,AA.ACCEPT_TIME,
                        TC.CUSTOMER_NAME,TC.CUSTOMER_ID,TC.CUSTOMER_ID_CODE,TC.CUSTOMER_GENDER,TC.CUSTOMER_BIRTHDAY,
                        CA.INSURED_ID,CA.ACC_PROVINCE,CA.ACC_CITY,CA.ACC_DISTREACT,CA.ACC_STREET,CA.ACC_REASON,
                        CA.ACC_DATE,AA.SERIOUS_DISEASE,AA.ACCIDENT_DETAIL,AA.CURE_HOSPITAL,AA.CURE_STATUS,AA.DOCTOR_NAME,AA.MED_DEPT,
                        CA.ACC_DESC,AA.ACCEPT_DECISION, AA.CASE_FLAG, AA.ADVANCE_FLAG,AA.GREEN_FLAG,AA.SIGNER_ID,IL.SOCI_SECU,
                        AA.REGISTER_ID,/*立案人*/AA.INSURANCE_PAID_FLAG,
                        (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.SIGNER_ID) signerCode,
				        (SELECT UU.REAL_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.SIGNER_ID) signerName,
				        (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.REGISTER_ID) registerCode,
				        (SELECT UU.REAL_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.REGISTER_ID) registerName,
				        (SELECT DISEASE_TYPE FROM DEV_PAS.T_LA_TYPE LT WHERE LT.CODE=AA.SERIOUS_DISEASE) AS DISEASE_TYPE/*1：轻症病种，2：中症病种，3：重症病种*/
        FROM            DEV_CLM.T_CLAIM_CASE AA
             left JOIN DEV_CLM.T_CLAIM_ACCIDENT CA
                     ON AA.ACCIDENT_ID = CA.ACCIDENT_ID
             left join DEV_CLM.T_CLAIM_APPLICANT TCA         
                     ON AA.CASE_ID = TCA.CASE_ID
              LEFT JOIN DEV_CLM.T_CUSTOMER TC
                     ON CA.INSURED_ID = TC.CUSTOMER_ID
              LEFT JOIN DEV_CLM.T_INSURED_LIST IL
				    ON IL.CASE_ID = AA.CASE_ID
				   AND IL.CUR_FLAG = 0
				   AND IL.CUSTOMER_ID = TC.CUSTOMER_ID
	    WHERE  1=1
		 ]]>
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND AA.CASE_ID = #{caseID} ]]></if>
	</select>
	<!-- 案件基本信息-报案信息等 end -->

	<!-- 立案结论 -->
	<select id="recordConclusion" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
       	  SELECT TC.ACCEPT_DECISION,
       	  		 TC.REJECT_REASON,
       	         TC.REGISTER_ID,
       	         TC.REGISTE_TIME
 		  FROM   DEV_CLM.T_CLAIM_CASE TC 
 		  WHERE  1=1
		 ]]>
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND TC.CASE_ID = #{caseID} ]]></if>
	</select>
	<!-- 立案结论 end -->
	<!-- 立案结论 end -->

	<!-- 理赔二核相关业务查询操作start -->
	<!-- 二核保信息查询 -->
	<select id="queryOtherOperInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[   
                SELECT DISTINCT C.*,
			           NVL(HOLDER_CUSTOMER_CLM, HOLDER_CUSTOMER_PAS) AS CUSTOMER_NAME,/*投保人客户姓名*/
			           NVL(HOLDER_CUSTOMER_ID_CLM, HOLDER_CUSTOMER_ID_PAS) AS CUSTOMER_ID/*投保人客户号*/
			      FROM (SELECT  CU.CLM_UW_ID,/*理赔二核ID*/
                   				UP.UW_ID,/*核保ID*/
	                            CU.CASE_NO,/*赔案号*/
	                            CU.POLICY_CODE, /*保单号*/
	                            (SELECT C.CUSTOMER_ID
	                               FROM DEV_CLM.T_POLICY_HOLDER PH
	                              INNER JOIN DEV_PAS.T_CUSTOMER C
	                                 ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	                              WHERE PH.CASE_ID = CU.CASE_ID
	                                AND PH.POLICY_CODE = CU.POLICY_CODE
	                                AND PH.CUR_FLAG = 1
	                                AND ROWNUM = 1) AS HOLDER_CUSTOMER_ID_CLM,/*理赔投保人抄单表客户号*/
	                            (SELECT C.CUSTOMER_ID
	                               FROM DEV_PAS.T_POLICY_HOLDER PH
	                              INNER JOIN DEV_PAS.T_CUSTOMER C
	                                 ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	                              WHERE PH.POLICY_CODE = CU.POLICY_CODE) AS HOLDER_CUSTOMER_ID_PAS,/*保单库投保客户号*/
	                            (SELECT C.CUSTOMER_NAME
	                               FROM DEV_CLM.T_POLICY_HOLDER PH
	                              INNER JOIN DEV_PAS.T_CUSTOMER C
	                                 ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	                              WHERE PH.CASE_ID = CU.CASE_ID
	                                AND PH.POLICY_CODE = CU.POLICY_CODE
	                                AND PH.CUR_FLAG = 1
	                                AND ROWNUM = 1) AS HOLDER_CUSTOMER_CLM,/*理赔投保人抄单表客户姓名*/
	                            (SELECT C.CUSTOMER_NAME
	                               FROM DEV_PAS.T_POLICY_HOLDER PH
	                              INNER JOIN DEV_PAS.T_CUSTOMER C
	                                 ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	                              WHERE PH.POLICY_CODE = CU.POLICY_CODE) AS HOLDER_CUSTOMER_PAS,/*保单库投保人客户姓名*/      
	                            (SELECT LISTAGG(C.CUSTOMER_NAME, ',') WITHIN GROUP(ORDER BY CA.CASE_ID) INSURED_NAMES
	                               FROM DEV_PAS.T_CUSTOMER C
	                               WHERE CA.CASE_ID = CU.CASE_ID
	                               AND CA.INSURED_ID = C.CUSTOMER_ID) AS INSURED_NAME,        
	                            CU.UW_STATUS,/*二核状态*/  
	                            CA.CASE_FLAG,                         
	                            T.REAL_NAME,
	                            CU.APPLY_DATE,/*提起二核日期*/
	                            CU.CLAIM_UW_TYPE,/*二核类型*/
	                            CU.REMARK,/*备注*/
	                            CU.NOT_INFORM_SITUATION,/*未告知情况/出险人风险情况*/
	                            TO_CHAR(UP.UW_FINISH_TIME, 'YYYY-MM-DD') AS FINISH_TIME/*核保完成日期*/
	              FROM DEV_CLM.T_CLAIM_UW CU
	              LEFT JOIN DEV_CLM.T_UW_POLICY UP ON CU.UW_POLICY_ID = UP.UW_POLICY_ID
	              LEFT JOIN DEV_PAS.T_UDMP_USER T
	               ON T.USER_ID = UP.UW_USER_ID
	               LEFT JOIN DEV_CLM.T_CLAIM_CASE CA ON CU.CASE_ID = CA.CASE_ID
	           WHERE 1=1 ]]>
              <if test=" caseID != null"><![CDATA[AND CU.CASE_ID = #{caseID}/*理赔赔案ID*/]]></if>
             <![CDATA[  
              )C  ORDER BY UW_ID ]]>

    </select>
	<!-- 二核未告知详情以及备注查询 -->
	<select id="queryOtherOperMarkInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
        	SELECT DISTINCT 
        			CU.CASE_NO,       
	                CU.REMARK,
	                CU.NOT_INFORM_SITUATION
			  FROM 
			  		DEV_CLM.T_CLAIM_UW CU
			  WHERE 
			  		1=1
			    ]]>
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND CU.CASE_ID=#{caseID} ]]></if>
	</select>
	<!--二核业务-核保回退信息  -->
	<select id="queryUwBackDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[ 
                SELECT UBC.CAUSE_DESC, UBD.REASON
				FROM DEV_CLM.T_UW_BACK_DETAIL UBD
				LEFT JOIN DEV_UW.T_UW_BACK_CAUSE UBC
				ON UBD.CASUE_CODE = UBC.CAUSE_CODE
				 WHERE 1 = 1
             ]]>
        <if test=" uw_id != null"><![CDATA[AND UBD.UW_ID=#{uw_id} ]]></if>         
    </select>
    <!--二核业务-核保回退信息  -->
	<select id="queryClmUwBackDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
                SELECT UBC.CAUSE_DESC, UBD.REASON
				FROM DEV_CLM.T_UW_BACK_DETAIL UBD
				LEFT JOIN DEV_CLM.T_UW_BACK_CAUSE UBC
				ON UBD.CASUE_CODE = UBC.CAUSE_CODE
				 WHERE 1 = 1
             ]]>
        <if test=" uw_id != null"><![CDATA[AND UBD.UW_ID=#{uw_id} ]]></if>         
    </select>
	<!-- 二核业务 保单核保结论 -->
	<select id="queryOtherOperBDContent" resultType="java.util.Map"
		parameterType="java.util.Map">
      	   <![CDATA[ 
           SELECT  
                 CU.CLM_UW_ID,/*理赔二核保ID*/
                CU.CASE_ID,/*理赔赔案ID*/
               CU.UW_STATUS,/*二核状态*/
               UP.UW_ID,/*理赔核保ID*/
               CU.CASE_NO,/*赔案号*/
               CU.REMARK,/*备注*/
               CU.NOT_INFORM_SITUATION，/*未告知情况/出险人风险情况*/
               /*CU.UW_CONCLUSION,二核处理结论*/
               PD.DECISION_DESC AS UW_CONCLUSION,/*核保结论*/
               T.REAL_NAME,/*核保人*/
               CU.UW_CONCLUSION_TIME AS UW_FINISH_TIME,/*核保完成日期*/     
               (

                 SELECT max(T.UW_COMMENTS)
                    FROM DEV_uw.T_UW_TRACE T
                    left join dev_uw.t_uw_master ma
                      on t.uw_id = ma.uw_id
                    left join dev_uw.t_uw_policy po
                      on t.uw_id = po.uw_id
                   WHERE T.UW_EVENT_CODE = '13'
                     and t.insert_time=(select max(ut.insert_time) from dev_uw.t_uw_trace ut where ut.UW_EVENT_CODE = '13' and ut.uw_id=t.uw_id)
                     and ma.biz_code = cu.case_no
                     and po.policy_code = UP.POLICY_CODE

        ) AS HTHBYJ/*合同核保意见*/
              FROM DEV_CLM.T_CLAIM_UW CU
              LEFT JOIN DEV_CLM.T_UW_POLICY UP
                  ON CU.UW_POLICY_ID = UP.UW_POLICY_ID           
             LEFT JOIN DEV_PAS.T_UDMP_USER T
                ON UP.UW_USER_ID = T.USER_ID
             LEFT JOIN DEV_CLM.T_POLICY_DECISION PD ON UP.POLICY_DECISION=PD.DECISION_CODE
             WHERE 1 = 1             
           ]]>
        <!-- 添加查询条件 -->
        <if test=" clm_uw_id != null"><![CDATA[AND CU.CLM_UW_ID = #{clm_uw_id}]]></if>
        <if test=" uw_id != null"><![CDATA[AND UP.UW_ID = #{uw_id}]]></if>
	</select>

	<!--二核业务 险种核保结论 -->
	<select id="queryOtherOperContent" resultType="java.util.Map"
		parameterType="java.util.Map">
         <![CDATA[ 
         SELECT CU.POLICY_CODE, /*保单号*/
               BP.UW_ID,
               BP.BUSI_ITEM_ID, /*险种ID*/
               BP.UW_BUSI_ID,/*险种核保ID*/
               BP.BUSI_PROD_CODE, /*险种代码*/
               TP.PRODUCT_NAME_SYS, /*险种名称*/
               CBP.LIABILITY_STATE, /*险种状态*/
               CP.AMOUNT, /*保额*/
               CP.TOTAL_PREM_AF AS PREMAF, /*年保费*/
               (SELECT C.CUSTOMER_NAME
	          	FROM DEV_PAS.T_CUSTOMER C
	         	WHERE C.CUSTOMER_ID = BP.INSURED_1) AS REAL_NAME, /*被保人*/
               BP.DECISION_CODE, /*核保决定*/
               UPP.EXTRA_PREM_AF, /*二核加费*/
               TO_CHAR(CU.UW_NUCLEUS_TIME, 'yyyy-MM-dd') AS UW_NUCLEUS_TIME /*二核加费核销日*/
          FROM DEV_CLM.T_CLAIM_UW CU /*理赔二核表*/
          INNER JOIN DEV_CLM.T_UW_POLICY UP ON CU.UW_POLICY_ID=UP.UW_POLICY_ID
          INNER JOIN DEV_CLM.T_UW_BUSI_PROD BP /**核保险种信息表*/
            ON CU.POLICY_CODE = BP.POLICY_CODE
            AND BP.UW_ID=UP.UW_ID AND BP.UW_POLICY_ID=BP.UW_POLICY_ID
          LEFT JOIN (SELECT CP.BUSI_ITEM_ID,
                            CP.POLICY_CODE,
                            SUM(CP.AMOUNT) AS AMOUNT,
                            SUM(CP.TOTAL_PREM_AF) AS TOTAL_PREM_AF
                       FROM DEV_PAS.T_CONTRACT_PRODUCT CP
                       WHERE  EXISTS
                        (
                            SELECT * FROM DEV_CLM.T_CLAIM_UW  CU WHERE CU.POLICY_CODE=CP.POLICY_CODE 
          ]]>
           <if test=" clm_uw_id != null"><![CDATA[AND CU.CLM_UW_ID = #{clm_uw_id}]]></if>
          <![CDATA[ 
                        )
                      GROUP BY CP.POLICY_CODE, CP.BUSI_ITEM_ID                      
              ) CP
            ON CU.POLICY_CODE = CP.POLICY_CODE
           AND CP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT TP
            ON BP.BUSI_PROD_CODE = TP.PRODUCT_CODE_SYS
          LEFT JOIN (
          SELECT CBP.POLICY_CODE,CBP.BUSI_PROD_CODE,CBP.LIABILITY_STATE FROM 
          DEV_CLM.T_CONTRACT_BUSI_PROD CBP WHERE CBP.CUR_FLAG=1
           ]]>
             <if test=" case_id != null"><![CDATA[AND CBP.CASE_ID = #{case_id}]]></if>
          <![CDATA[
          UNION 
          SELECT CBP.POLICY_CODE,CBP.BUSI_PROD_CODE,CBP.LIABILITY_STATE 
           FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP WHERE NOT 
          EXISTS
            (
            SELECT CBP1.POLICY_CODE,CBP1.LIABILITY_STATE FROM 
                   DEV_CLM.T_CONTRACT_BUSI_PROD CBP1 WHERE
              CBP1.CUR_FLAG=1 AND CBP1.POLICY_CODE=CBP.POLICY_CODE AND CBP1.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE
            ]]>
             <if test=" case_id != null"><![CDATA[AND CBP1.CASE_ID = #{case_id}]]></if>
          <![CDATA[
            )
            AND 
            EXISTS
            (
                SELECT * FROM DEV_CLM.T_CLAIM_UW  CU WHERE CU.POLICY_CODE=CBP.POLICY_CODE 
           ]]>
           <if test=" clm_uw_id != null"><![CDATA[AND CU.CLM_UW_ID = #{clm_uw_id}]]></if>
          <![CDATA[
            )
          ) CBP
            ON CBP.POLICY_CODE = BP.POLICY_CODE AND CBP.BUSI_PROD_CODE=BP.BUSI_PROD_CODE
         LEFT JOIN (SELECT UPP.POLICY_CODE,UPP.UW_ID,SUM(UPP.EXTRA_PREM_AF) AS EXTRA_PREM_AF FROM 
         DEV_CLM.T_UW_PRODUCT UPP GROUP BY  UPP.POLICY_CODE,UPP.UW_ID) UPP
            ON BP.POLICY_CODE = UPP.POLICY_CODE AND BP.UW_ID=UPP.UW_ID             
         WHERE 1 = 1
       ]]>
      <if test=" clm_uw_id != null"><![CDATA[AND CU.CLM_UW_ID = #{clm_uw_id}]]></if> 
	</select>
<!--险种核保加费信息 -->
    <select id="queryExtraInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[ 
                SELECT UEP.EXTRA_TYPE, /*加费类型*/
				       UEP.INTO_EFFECT_TYPE, /*生效类别*/
				       UEP.START_DATE, /*开始日期*/
				       UEP.END_DATE, /*终止日期*/
				       UEP.EXTRA_PERIOD, /*加费期数*/
				       UEP.EM_VALUE, /*EM值*/
				       UEP.EXTRA_PERC, /*加费比例*/
				       UEP.EXTRA_PARA, /*加费金额(单位保额)*/
				       UEP.EXTRA_PREM, /*额外保险费*/
				       UDR.REASON_CONTENT /*加费原因*/
				  FROM DEV_CLM.T_UW_EXTRA_PREM UEP
				  LEFT JOIN DEV_UW.T_UW_DECISION_REASON UDR
				    ON UEP.LIST_ID = UDR.EXTRA_PREM_ID
             ]]>
        <if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[AND UEP.busi_item_id=#{busi_item_id} ]]></if>
          <![CDATA[        
          			ORDER BY UEP.EXTRA_TYPE
          ]]>
    </select>
    
    <!--险种核保特约信息 -->
    <select id="querySpecialInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[ 
                SELECT UC.UW_CONDITION_TYPE, /*特别约定代码*/
				       UC.BUSI_ITEM_ID,
				       UC.ITEM_ID, /*险种责任组信息*/
				       PTL.PRODUCT_NAME, /*险种责任组名称*/
				       BP.PRODUCT_NAME_SYS, /*险种名称*/
				       UC.CONDITION_DESC,/*特约信息描述*/
				       UC.INSURED_CUSTOMER_ID, /*被保人*/
				       C.CUSTOMER_NAME, /*被保人姓名*/
				       UDR.REASON_CONTENT /*特约原因*/
				  FROM DEV_CLM.T_UW_CONDITION UC
				  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
				    ON UC.ITEM_ID = CP.ITEM_ID
				  LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PTL
				    ON PTL.PRODUCT_ID = CP.PRODUCT_ID
				  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
				    ON CBP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
				  LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
				    ON BP.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
				  LEFT JOIN DEV_PAS.T_CUSTOMER C
				    ON UC.INSURED_CUSTOMER_ID = C.CUSTOMER_ID
				  LEFT JOIN DEV_UW.T_UW_DECISION_REASON UDR
				    ON UDR.CONDITION_LIST_ID = UC.LIST_ID
				 WHERE 1 = 1
      
             ]]>
        <if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[AND UC.busi_item_id=#{busi_item_id} ]]></if>         
    </select>

	<!-- 理赔调查相关的业务查询操作start -->
	<!--调查申请信息 -->
	<select id="queryOperBranchInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	           <![CDATA[ 
				  SELECT TS.CASE_NO,
                         TS.SURVEY_CODE,                
                         T.CUSTOMER_NAME,
                         TC.INSURED_ID,  
                         TS.APPLY_SECTION,
                         TS.SURVEY_REASON,
                         TS.INTERNAL_RESUALT,
                         TS.APPLY_DATE,
                         TS.SURVEY_DESC,
                         TS.SURVEY_ADVICE
	               FROM     DEV_CLM.T_CLAIM_CASE TC, 
	                   		DEV_CLM.T_SURVEY_APPLY TS ,
	                   		DEV_CLM.T_CUSTOMER T
                   WHERE TC.CASE_ID = TS.CASE_ID
                   		 AND T.CUSTOMER_ID  = TC.INSURED_ID
       
				]]>
		<!-- 添加查询条件 -->
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND TC.CASE_ID=#{caseID} ]]></if>
	</select>

	<!-- 调查机构结论信息查询 -->
	<select id="queryOpernPanleInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	         <![CDATA[ 
				   SELECT    SC.CONCLUSION_ID,
					         SA.SURVEY_ORG,
					         SC.SURVEY_CONCLUSION,
					         SC.REMARK,
					         SC.POSITIVE_FLAG
	               FROM 	 DEV_CLM.T_SURVEY_CONCLUSION SC,
		           	    	 DEV_CLM.T_SURVEY_APPLY SA
		           WHERE 	SA.APPLY_ID = SC.APPLY_ID
		        ]]>
		<!-- 添加查询条件 -->
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND SA.CASE_ID=#{caseID} ]]></if>
	</select>

	<!-- 调查过程详细信息查询 -->
	<select id="queryDetailSearchInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
   
	         <![CDATA[ 
			      SELECT SC.COURSE_ID,
			             SC.SURVEY_DATE,
			             SC.SURVEY_MODE,
			             SC.SURVEY_PLACE,
			             SC.SURVEYED_PERSON CUSTOMER_NAME,
			             SC.SURVEY_ORG,
			             SC.SURVEY_BY_ID1,
			             SC.UPDATE_BY,
			             SC.SURVEY_BY_ID2,
			             SC.REMARK,
			             SC.SURVEY_COURSE,
                         SC.SURVEYED_PERSON
			      FROM     DEV_CLM.T_SURVEY_COURSE SC,
			          	   DEV_CLM.T_SURVEY_APPLY SA
				  WHERE     SC.APPLY_ID = SA.APPLY_ID
				   
			  ]]>
		<!-- 添加查询条件 -->
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND SA.CASE_ID=#{caseID} ]]></if>
	</select>

	<!-- 调查信息详细费用查询 -->
	<select id="querySalyInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	             <![CDATA[ 
				  SELECT SFF.FEE_ID,
				         SFF.FEE_TYPE,
				         SFF.SURVEY_FEE,
				         SFF.FEE_DATE,
				         SFF.PAYEE_NAME
				  FROM   DEV_CLM.T_SURVEY_FEE SFF,
				  	     DEV_CLM.T_SURVEY_APPLY SA
				  WHERE SFF.APPLY_ID = SA.APPLY_ID      
		           ]]>
		<!-- 添加查询条件 -->
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND SA.CASE_ID=#{caseID} ]]></if>
	</select>
	<!-- 理赔调查相关的业务查询操作end -->
	
	<!--案件轨迹查询start  -->
	<select id="historyWork" resultType="java.util.Map"
		parameterType="java.util.Map">
            <![CDATA[ 
            SELECT * FROM(
			SELECT 
			      (SELECT 
			      BCI.VALUE
			      FROM
			      DEV_BPM.T_BPM_CODE_INFO BCI
			      WHERE BCI.CODE=SW.WORKITEM_STATUS
			      AND CODE_TYPE='STATUS')  AS WORKITEM_STATUS,
			      (SELECT 
			      BCI.VALUE
			      FROM
			      DEV_BPM.T_BPM_CODE_INFO BCI
			      WHERE BCI.CODE=SP.PROCESS_STATUS
			      AND CODE_TYPE='STATUS')  AS PROCESS_STATUS,
			      SP.BUSINESS_TYPE,
			      SP.BUSINESS_CODE1,
			      SP.BUSINESS_CODE2,
			      (
			      SELECT 
			          BIW.WORKITEM_NAME
			      FROM
			       DEV_BPM.T_BPM_INVENTORY_WORKITEM BIW WHERE BIW.WORKITEM_ID=SW.WORKITEM_CODE
			      ) AS WORKITEM_NAME1,
			      SW.WORKITEM_NAME,
			      SW.FINISH_TIME,
			      SW.USER_CODE ,
			      BIP.PROCESS_NAME,
                  UU.REAL_NAME 
			FROM 
			      DEV_BPM.T_BPM_STREAM_WORKITEM SW
			INNER JOIN   DEV_BPM.T_BPM_STREAM_PROCESS SP ON SW.PROCESS_ID=SP.PROCESS_ID
			INNER JOIN　DEV_BPM.T_BPM_INVENTORY_PROCESS BIP ON　BIP.PROCESS_CODE=SP.PROCESS_CODE
			INNER JOIN  DEV_CLM.T_CLAIM_CASE  CC ON CC.CASE_NO=SP.BUSINESS_CODE1
            INNER JOIN DEV_PAS.T_UDMP_USER UU ON  UU.USER_NAME=SW.USER_CODE
			WHERE 1=1
			AND SW.PROCESS_CODE LIKE '%CLM%'
			 ]]>
		<!-- 添加查询条件 -->
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND CC.CASE_ID=#{caseID} ]]></if>
		 <![CDATA[ 
			UNION
			SELECT 
			     
			      (SELECT 
			      BCI.VALUE
			      FROM
			      DEV_BPM.T_BPM_CODE_INFO BCI
			      WHERE BCI.CODE=SW.WORKITEM_STATUS
			      AND CODE_TYPE='STATUS')  AS WORKITEM_STATUS,
			      (SELECT 
			      BCI.VALUE
			      FROM
			      DEV_BPM.T_BPM_CODE_INFO BCI
			      WHERE BCI.CODE=SP.PROCESS_STATUS
			      AND CODE_TYPE='STATUS')  AS PROCESS_STATUS,
			      SP.BUSINESS_TYPE,
			      SP.BUSINESS_CODE1,
			      SP.BUSINESS_CODE2,
			      (
			      SELECT 
			          BIW.WORKITEM_NAME
			      FROM
			       DEV_BPM.T_BPM_INVENTORY_WORKITEM BIW WHERE BIW.WORKITEM_ID=SW.WORKITEM_CODE
			      ) AS WORKITEM_NAME1,
			      SW.WORKITEM_NAME,
			      SW.FINISH_TIME,
			      SW.USER_CODE ,
			      BIP.PROCESS_NAME,
                  UU.REAL_NAME 
			FROM 
			      DEV_BPM.T_BPM_HISTORY_WORKITEM SW
			INNER JOIN   DEV_BPM.T_BPM_HISTORY_PROCESS SP ON SW.PROCESS_ID=SP.PROCESS_ID
			INNER JOIN　DEV_BPM.T_BPM_INVENTORY_PROCESS BIP ON　BIP.PROCESS_CODE=SP.PROCESS_CODE
			INNER JOIN  DEV_CLM.T_CLAIM_CASE  CC ON CC.CASE_NO=SP.BUSINESS_CODE1
             INNER JOIN DEV_PAS.T_UDMP_USER UU ON  UU.USER_NAME=SW.USER_CODE
			WHERE 1=1
			AND SW.PROCESS_CODE LIKE '%CLM%'
           ]]>
		<!-- 添加查询条件 -->
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND CC.CASE_ID=#{caseID} ]]></if> 
		) ORDER BY FINISH_TIME ASC
	</select>
	<!-- 案件轨迹查询 END -->
</mapper>
