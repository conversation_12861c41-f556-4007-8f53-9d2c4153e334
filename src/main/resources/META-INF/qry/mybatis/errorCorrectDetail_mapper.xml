<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.ErrorCorrectDetailDaoImpl">
	<sql id="NB_queryByCorrectIdCondition">
		<if test=" correct_id  != null "><![CDATA[ AND A.CORRECT_ID = #{correct_id} ]]></if>
	</sql>	
	
	<!-- 分页查询纠错信息列表 -->
	<select id="NB_findErrorCorrectDetailTotalByCorrectId" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_ERROR_CORRECT_DETAIL A WHERE 1=1   ]]>
	    <include refid="NB_queryByCorrectIdCondition" />
	</select>
	<select id="NB_queryErrorCorrectDetailForPageByCorrectId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS ROWNUMBER, B.PRIMARY_KEY_VALUE, B.TABLE_NAME,B.OLD_VALUE, B.PRIMARY_KEY_NAME, 
			B.NEW_VALUE, B.COL_E_NAME, B.COL_C_NAME, B.CORRECT_ID, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.PRIMARY_KEY_VALUE, A.TABLE_NAME,A.OLD_VALUE, A.PRIMARY_KEY_NAME, 
			A.NEW_VALUE, A.COL_E_NAME, A.COL_C_NAME, A.CORRECT_ID, A.LIST_ID FROM DEV_NB.T_ERROR_CORRECT_DETAIL A  
			WHERE 
			ROWNUM <= #{LESS_NUM} ]]>
	    <include refid="NB_queryByCorrectIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>	
	</select>
	
	
</mapper>
