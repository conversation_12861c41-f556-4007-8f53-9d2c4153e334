<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QueryProposalPrintDateFlagDaoImpl">
    <select id="queryProposalPrintDate" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
         SELECT (CASE
		         WHEN T.POLICY_CODE <> T.RELATIOIN_POLICY_CODE AND
		              T.DOUBLE_MAINRISK_FLAG = '1' THEN
		          (SELECT MAX(PRINT_TIME)
		             FROM (SELECT DECODE(PPA.PRINT_TYPE,'1',PPA.BPO_PRINT_DATE,PPA.PRINT_TIME) AS PRINT_TIME, PPA.POLICY_CODE, PPA.PRINT_TYPE
		                     FROM DEV_PAS.T_POLICY_PRINT PPA
		                     LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC ON PPA.POLICY_CHG_ID = CPC.POLICY_CHG_ID
		                     WHERE NOT EXISTS 
		                     (SELECT 1 FROM DEV_PAS.T_CS_ACCEPT_CHANGE WHERE ACCEPT_ID = CPC.ACCEPT_ID AND SERVICE_CODE IN ('LR','RN','PT','PU') AND ACCEPT_STATUS = '19')
		                   UNION
		                   SELECT DECODE(PPN.PRINT_TYPE,'1',PPN.BPO_PRINT_DATE,PPN.PRINT_TIME) AS PRINT_TIME, PPN.POLICY_CODE, PPN.PRINT_TYPE
                           FROM DEV_NB.T_POLICY_PRINT PPN) A
		            WHERE POLICY_CODE = T.RELATIOIN_POLICY_CODE
		              AND PRINT_TIME IS NOT NULL
		              AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1'))
		         ELSE
		          (SELECT MAX(PRINT_TIME)
		             FROM (SELECT DECODE(PPA.PRINT_TYPE,'1',PPA.BPO_PRINT_DATE,PPA.PRINT_TIME) AS PRINT_TIME, PPA.POLICY_CODE, PPA.PRINT_TYPE
		                     FROM DEV_PAS.T_POLICY_PRINT PPA
		                     LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC ON PPA.POLICY_CHG_ID = CPC.POLICY_CHG_ID
		                     WHERE NOT EXISTS 
		                     (SELECT 1 FROM DEV_PAS.T_CS_ACCEPT_CHANGE WHERE ACCEPT_ID = CPC.ACCEPT_ID AND SERVICE_CODE IN ('LR','RN','PT','PU') AND ACCEPT_STATUS = '19')
		                   UNION
		                   SELECT DECODE(PPN.PRINT_TYPE,'1',PPN.BPO_PRINT_DATE,PPN.PRINT_TIME) AS PRINT_TIME, PPN.POLICY_CODE, PPN.PRINT_TYPE
                           FROM DEV_NB.T_POLICY_PRINT PPN) A
		            WHERE POLICY_CODE = T.POLICY_CODE
		              AND PRINT_TIME IS NOT NULL
		              AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1'))
		       END) AS PRINT_TIME /*打印日期*/
		  FROM (SELECT DISTINCT CM.POLICY_ID,
		                        CM.POLICY_CODE,
		                        CM.APPLY_CODE,
		                        (CASE
		                          WHEN (CM.DOUBLE_MAINRISK_FLAG = '1' AND
		                               CM.RELATION_POLICY_CODE IS NULL) THEN
		                           (SELECT APPLY_CODE
		                              FROM DEV_PAS.T_CONTRACT_MASTER NCM
		                             WHERE NCM.RELATION_POLICY_CODE = CM.POLICY_CODE)
		                          ELSE
		                           CM.APPLY_CODE
		                        END) AS RELATIOIN_APPLY_CODE, /*双主险关联投保单号*/
		                        (CASE
		                          WHEN (CM.DOUBLE_MAINRISK_FLAG = '1' AND
		                               CM.RELATION_POLICY_CODE IS NULL) THEN
		                           (SELECT POLICY_CODE
		                              FROM DEV_PAS.T_CONTRACT_MASTER NCM
		                             WHERE NCM.RELATION_POLICY_CODE = CM.POLICY_CODE)
		                          ELSE
		                           CM.POLICY_CODE
		                        END) AS RELATIOIN_POLICY_CODE, /*双主险关联保单号*/
		                        CM.DOUBLE_MAINRISK_FLAG /*双主险标识*/
		          FROM DEV_PAS.T_CONTRACT_MASTER CM
		         WHERE 1 = 1 ]]>
		       <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
		       <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
		       <![CDATA[    AND ROWNUM = 1) T
        ]]>
    </select>
        <select id="queryPaperApplySource" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
         SELECT T.*
		  FROM (
			SELECT DISTINCT 
			  CM.POLICY_ID,
			         CM.POLICY_CODE,
			         CM.APPLY_CODE,
			           TNCM.Drq_Flag,
			           (case  
			       when (select count(1) from dev_nb.t_policy_print_detail pd where pd.apply_code = TNCM.apply_code) > 0 /*电子保单申请过纸质保单*/
			       then (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD LEFT JOIN DEV_NB.T_POLICY_PRINT_SOURCE PPS ON PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE WHERE PPD.APPLY_CODE = TNCM.APPLY_CODE AND ROWNUM <= 1)
			       when to_number(TNCM.subinput_type) <> 15 and TNCM.submit_channel = '1' and tt.media_type in ('0','2') /*保全做过变更并且是电子保单非银保通电子渠道出单*/
			       then  (SELECT tst.type_desc FROM dev_nb.t_input_type tst where  TNCM.input_type = tst.type_code)
			       when to_number(TNCM.subinput_type) <> 15 and TNCM.submit_channel = '1' and TNCM.media_type = '0' and cm.media_type = '1' and tt.media_type = '1' /*判断完保全再判断契约*/
			       then  ''
			       when tt.media_type is not null and (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = TNCM.apply_code) = 0 and TNCM.media_type <> tt.media_type
			        then ''
			       ELSE ''
			        end )as PAPER_APPLY_SOURCE
			        FROM DEV_PAS.T_CONTRACT_MASTER CM
			         LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TNCM 
			      ON TNCM.POLICY_CODE = CM.POLICY_CODE
			      LEFT JOIN (SELECT LOG_ID,POLICY_CODE,MEDIA_TYPE  FROM 
			                    (SELECT MSTG.LOG_ID,MSTG.POLICY_CODE,MSTG.MEDIA_TYPE 
			                    ,row_number() over(partition by MSTG.POLICY_CODE ORDER BY MSTG.LOG_ID  ASC ) RN
			                    FROM DEV_PAS.T_CONTRACT_MASTER_LOG MSTG)
			                    WHERE RN = 1) TT ON TT.POLICY_CODE = TNCM.POLICY_CODE
			                    WHERE 1 = 1 
		        AND CM.POLICY_CODE = #{policy_code} ]]>
		       <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
		       <![CDATA[    AND ROWNUM = 1) T
        ]]>
    </select>
    
    
</mapper>
