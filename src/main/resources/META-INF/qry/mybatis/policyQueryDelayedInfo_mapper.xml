<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.IPolicyQueryDelayedInfoDao">
<select id="qry_queryPolicyInfoByType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT TCPC.POLICY_CODE,
		TCM.APPLY_CODE,
        #{query_type} as query_type,
        TCAC.VALIDATE_TIME,
		TCM.SERVICE_BANK,
		TCM.SUBMIT_CHANNEL,
		tca.CHANNEL_TYPE,
       (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCPC.POLICY_CODE = TPH.POLICY_CODE) AS holder_name
  FROM DEV_PAS.T_CS_POLICY_CHANGE TCPC
  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
    ON TCAC.ACCEPT_ID = TCPC.ACCEPT_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM
    ON TCM.POLICY_ID = TCPC.POLICY_ID
  JOIN dev_pas.t_contract_agent tca
    ON tcm.policy_code = tca.policy_code
 WHERE TCAC.ACCEPT_STATUS = '18' and tca.is_nb_agent = '1' ]]>
	<if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
	<if test="apply_code_list!=null and apply_code_list.size()>0">
      <![CDATA[ AND TCM.APPLY_CODE IN ]]>
        <foreach collection="apply_code_list" item="apply" index="index" open="(" close=")" separator=",">#{apply}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="submit"
        index="submit" open="(" close=")" separator=",">#{submit}</foreach>
    </if>
    <if test="channel_type_list!=null and channel_type_list.size()>0">
    <![CDATA[ AND tca.CHANNEL_TYPE IN ]]>
      <foreach collection="channel_type_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="liability_state != null and liability_state!= ''"> <![CDATA[ AND TCM.LIABILITY_STATE = #{liability_state} ]]> </if>
    <if test="hesitate_flag != null and hesitate_flag!=''"> <![CDATA[ AND TCPC.HESITATE_FLAG = #{hesitate_flag} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TCAC.FINISH_TIME >= #{start_date} ]]> </if>
    <if test="end_date != null"> <![CDATA[ AND TCAC.FINISH_TIME < #{end_date} + 1 ]]> </if>
    <if test='service_codes!=null and service_codes.size()>0 ' >
        <![CDATA[AND TCAC.SERVICE_CODE IN ]]>
        <foreach collection="service_codes" item="service_code"
          index="service_code" open="(" close=")" separator=",">#{service_code}</foreach>
    </if>
	</select>
	<select id="qry_queryPolicyInfoByEnd" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT TCM.POLICY_CODE,
		TCM.APPLY_CODE,
        #{query_type} as query_type,
        TCM.EXPIRY_DATE as VALIDATE_TIME,
		TCM.SERVICE_BANK,
		TCM.SUBMIT_CHANNEL,
		tca.CHANNEL_TYPE,
       (SELECT TCU.CUSTOMER_NAME
          FROM APP___PAS__DBUSER.T_CUSTOMER      TCU,
               APP___PAS__DBUSER.T_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCM.POLICY_CODE = TPH.POLICY_CODE) AS holder_name
  FROM DEV_PAS.T_CONTRACT_MASTER TCM
  JOIN dev_pas.t_contract_agent tca
    ON tcm.policy_code = tca.policy_code
    where tca.is_nb_agent = '1']]>
	<if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
	<if test="apply_code_list!=null and apply_code_list.size()>0">
      <![CDATA[ AND TCM.APPLY_CODE IN ]]>
        <foreach collection="apply_code_list" item="apply" index="index" open="(" close=")" separator=",">#{apply}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="submit"
        index="submit" open="(" close=")" separator=",">#{submit}</foreach>
    </if>
    <if test="channel_type_list!=null and channel_type_list.size()>0">
    <![CDATA[ AND tca.CHANNEL_TYPE IN ]]>
      <foreach collection="channel_type_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="liability_state != null and liability_state!= ''"> <![CDATA[ AND TCM.LIABILITY_STATE = #{liability_state} ]]> </if>
    <if test="end_cause != null and end_cause!= ''"> <![CDATA[ AND TCM.END_CAUSE = #{end_cause} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TCM.EXPIRY_DATE >= #{start_date} ]]> </if>
    <if test="end_date != null"> <![CDATA[ AND TCM.EXPIRY_DATE < #{end_date} + 1 ]]> </if>
    </select>
    
    <select id="qry_queryPolicyInfoByproposal" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT TCM.POLICY_CODE,
		TCM.APPLY_CODE,
        #{query_type} as query_type,
        TCM.INPUT_DATE as VALIDATE_TIME,
		TCM.SERVICE_BANK,
		TCM.SUBMIT_CHANNEL,
		TCM.CHANNEL_TYPE,
		TCM.PROPOSAL_STATUS,
       (SELECT TCU.CUSTOMER_NAME
          FROM DEV_NB.T_NB_CUSTOMER      TCU,
               DEV_NB.T_NB_POLICY_HOLDER TPH
         WHERE TPH.CUSTOMER_ID = TCU.CUSTOMER_ID
           and TCM.APPLY_CODE = TPH.APPLY_CODE) AS holder_name
  FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE 1=1]]>
	<if test="policy_code_list!=null and policy_code_list.size()>0">
      <![CDATA[ AND TCM.POLICY_CODE IN ]]>
        <foreach collection="policy_code_list" item="codes" index="index" open="(" close=")" separator=",">#{codes}</foreach>
    </if>
	<if test="apply_code_list!=null and apply_code_list.size()>0">
      <![CDATA[ AND TCM.APPLY_CODE IN ]]>
        <foreach collection="apply_code_list" item="apply" index="index" open="(" close=")" separator=",">#{apply}</foreach>
    </if>
    <if test="submit_channel_list!=null and submit_channel_list.size()>0">
    <![CDATA[ AND TCM.SUBMIT_CHANNEL IN ]]>
      <foreach collection="submit_channel_list" item="submit"
        index="submit" open="(" close=")" separator=",">#{submit}</foreach>
    </if>
    <if test="channel_type_list!=null and channel_type_list.size()>0">
    <![CDATA[ AND TCM.CHANNEL_TYPE IN ]]>
      <foreach collection="channel_type_list" item="channel"
        index="channel" open="(" close=")" separator=",">#{channel}</foreach>
    </if>
    <if test="service_bank != null and service_bank!=''"> <![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]> </if>
    <if test="start_date != null"> <![CDATA[ AND TCM.INPUT_DATE >= #{start_date} ]]> </if>
    <if test="end_date != null"> <![CDATA[ AND TCM.INPUT_DATE < #{end_date} + 1 ]]> </if>
	<if test='proposal_status_list!=null and proposal_status_list.size()>0 ' >
        <![CDATA[AND TCM.PROPOSAL_STATUS IN ]]>
        <foreach collection="proposal_status_list" item="proposal_status"
          index="proposal_status" open="(" close=")" separator=",">#{proposal_status}</foreach>
    </if>
    </select>
</mapper>