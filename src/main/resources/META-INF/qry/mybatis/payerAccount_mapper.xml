<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="payerAccount">

	<sql id="PA_payerAccountWhereCondition">
		<if test=" next_account_id  != null "><![CDATA[ AND A.NEXT_ACCOUNT_ID = #{next_account_id} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" account_name != null and account_name != ''  "><![CDATA[ AND A.ACCOUNT_NAME = #{account_name} ]]></if>
		<if test=" account_bank != null and account_bank != ''  "><![CDATA[ AND A.ACCOUNT_BANK = #{account_bank} ]]></if>
		<if test=" payer_id  != null "><![CDATA[ AND A.PAYER_ID = #{payer_id} ]]></if>
		<if test=" next_account_bank != null and next_account_bank != ''  "><![CDATA[ AND A.NEXT_ACCOUNT_BANK = #{next_account_bank} ]]></if>
		<if test=" account != null and account != ''  "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
		<if test=" pay_next != null and pay_next != ''  "><![CDATA[ AND A.PAY_NEXT = #{pay_next} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" next_account_name != null and next_account_name != ''  "><![CDATA[ AND A.NEXT_ACCOUNT_NAME = #{next_account_name} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" next_account != null and next_account != ''  "><![CDATA[ AND A.NEXT_ACCOUNT = #{next_account} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPayerAccountByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayerAccountByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayerAccountByPayerIdCondition">
		<if test=" payer_id  != null "><![CDATA[ AND A.PAYER_ID = #{payer_id} ]]></if>
	</sql>	
	<sql id="PA_queryPayerAccountByNextAccountIdCondition">
		<if test=" next_account_id  != null "><![CDATA[ AND A.NEXT_ACCOUNT_ID = #{next_account_id} ]]></if>
	</sql>	
    <sql id="PA_queryPayerAccountByByPolicyIdCondition">
         <if test="policy_id!=null"><![CDATA[ AND A.policy_id = #{policy_id} ]]></if>
    </sql>


<!-- 按索引查询操作 -->	
	<select id="PA_findPayerAccountByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountByListIdCondition" />
	</select>
	
	<select id="PA_findPayerAccountByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountByAccountIdCondition" />
	</select>
	
	<select id="PA_findPayerAccountByPayerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountByPayerIdCondition" />
	</select>
	
	<select id="PA_findPayerAccountByNextAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPayerAccountByNextAccountIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPayerAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM T_PAYER_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPayerAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM T_PAYER_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPayerAccountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPayerAccountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.NEXT_ACCOUNT_ID, B.ACCOUNT_ID, B.ACCOUNT_NAME, B.ACCOUNT_BANK, B.PAYER_ID, B.NEXT_ACCOUNT_BANK, 
			B.ACCOUNT, B.PAY_NEXT, B.PAY_MODE, B.LIST_ID, 
			B.NEXT_ACCOUNT_NAME, B.POLICY_ID, B.NEXT_ACCOUNT FROM (
					SELECT ROWNUM RN, A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM T_PAYER_ACCOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_payerAccountWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="PA_findPayerAccountByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, 
			A.NEXT_ACCOUNT_NAME,  A.NEXT_ACCOUNT FROM T_PAYER_ACCOUNT A WHERE 1=1]]>		
			<include refid="PA_queryPayerAccountByByPolicyIdCondition" />
	</select>
	
	<select id="PA_findPayLocationPayerAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, A.PAY_LOCATION,
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM DEV_PAS.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_payerAccountWhereCondition" />
	</select>
	
	<select id="PAandNB_findPayLocationPayerAccount" resultType="java.util.Map" parameterType="java.util.Map">
	    <if test=" type != null and type == '1'.toString() ">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, A.PAY_LOCATION,
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM DEV_PAS.T_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_payerAccountWhereCondition" />
		</if>
		
		<if test=" type != null and type == '0'.toString() ">
		<![CDATA[ SELECT A.NEXT_ACCOUNT_ID, A.ACCOUNT_ID, A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, 
			A.ACCOUNT, A.PAY_NEXT, A.PAY_MODE, A.LIST_ID, A.PAY_LOCATION,
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT FROM DEV_NB.T_NB_PAYER_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_payerAccountWhereCondition" />
		</if>
	</select>
</mapper>
