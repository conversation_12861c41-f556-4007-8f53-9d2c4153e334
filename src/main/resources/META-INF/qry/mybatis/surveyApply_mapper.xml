<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.SurveyApplyDaoImpl">
<!--
	<sql id="surveyApplyWhereCondition">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" survey_type  != null "><![CDATA[ AND A.SURVEY_TYPE = #{survey_type} ]]></if>
		<if test=" apply_section  != null "><![CDATA[ AND A.APPLY_SECTION = #{apply_section} ]]></if>
		<if test=" internal_resualt != null and internal_resualt != ''  "><![CDATA[ AND A.INTERNAL_RESUALT = #{internal_resualt} ]]></if>
		<if test=" survey_rule_id  != null "><![CDATA[ AND A.SURVEY_RULE_ID = #{survey_rule_id} ]]></if>
		<if test=" survey_doc_id  != null "><![CDATA[ AND A.SURVEY_DOC_ID = #{survey_doc_id} ]]></if>
		<if test=" survey_per  != null "><![CDATA[ AND A.SURVEY_PER = #{survey_per} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" survey_status  != null "><![CDATA[ AND A.SURVEY_STATUS = #{survey_status} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" survey_mode != null and survey_mode != ''  "><![CDATA[ AND A.SURVEY_MODE = #{survey_mode} ]]></if>
		<if test=" repeal_reason != null and repeal_reason != ''  "><![CDATA[ AND A.REPEAL_REASON = #{repeal_reason} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" survey_desc != null and survey_desc != ''  "><![CDATA[ AND A.SURVEY_DESC = #{survey_desc} ]]></if>
		<if test=" apply_org != null and apply_org != ''  "><![CDATA[ AND A.APPLY_ORG = #{apply_org} ]]></if>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" survey_advice != null and survey_advice != ''  "><![CDATA[ AND A.SURVEY_ADVICE = #{survey_advice} ]]></if>
		<if test=" survey_reason != null and survey_reason != ''  "><![CDATA[ AND A.SURVEY_REASON = #{survey_reason} ]]></if>
		<if test=" cs_apply_code != null and cs_apply_code != ''  "><![CDATA[ AND A.CS_APPLY_CODE = #{cs_apply_code} ]]></if>
		<if test=" survey_org != null and survey_org != ''  "><![CDATA[ AND A.SURVEY_ORG = #{survey_org} ]]></if>
		<if test=" locial_flag_code != null and locial_flag_code != ''  "><![CDATA[ AND A.LOCIAL_FLAG_CODE = #{locial_flag_code} ]]></if>
		<if test=" cs_background != null and cs_background != ''  "><![CDATA[ AND A.CS_BACKGROUND = #{cs_background} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" cs_accept_code != null and cs_accept_code != ''  "><![CDATA[ AND A.CS_ACCEPT_CODE = #{cs_accept_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" survey_by_level  != null "><![CDATA[ AND A.SURVEY_BY_LEVEL = #{survey_by_level} ]]></if>
		<if test=" survey_code != null and survey_code != ''  "><![CDATA[ AND A.SURVEY_CODE = #{survey_code} ]]></if>
		<if test=" apply_per  != null "><![CDATA[ AND A.APPLY_PER = #{apply_per} ]]></if>
		<if test=" cs_item != null and cs_item != ''  "><![CDATA[ AND A.CS_ITEM = #{cs_item} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="querySurveyApplyByApplyIdCondition">
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
	</sql>	
	<sql id="querySurveyApplyByApplyCode">
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="querySurveyApplyBySurveyDocId">
		<if test=" survey_doc_id  != null "><![CDATA[ AND A.SURVEY_DOC_ID = #{survey_doc_id} ]]></if>
	</sql>
<!-- 添加操作 -->
	<insert id="addSurveyApply"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE"
			keyProperty="apply_id">
			SELECT DEV_UW.S_SURVEY_APPLY.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_SURVEY_APPLY(
				RELATED_ID, SURVEY_TYPE, APPLY_SECTION, INTERNAL_RESUALT, SURVEY_RULE_ID, SURVEY_DOC_ID, SURVEY_PER, 
				REMARK, SURVEY_STATUS, APPLY_DATE, SURVEY_MODE, REPEAL_REASON, CASE_NO, APPLY_CODE, 
				SURVEY_DESC, INSERT_TIMESTAMP, UPDATE_BY, APPLY_ORG, APPLY_ID, BIZ_TYPE, SURVEY_ADVICE, 
				SURVEY_REASON, INSERT_TIME, CS_APPLY_CODE, SURVEY_ORG, UPDATE_TIME, LOCIAL_FLAG_CODE, CS_BACKGROUND, 
				CASE_ID, CS_ACCEPT_CODE, POLICY_CODE, SURVEY_BY_LEVEL, SURVEY_CODE, APPLY_PER, UPDATE_TIMESTAMP, 
				INSERT_BY, CS_ITEM ) 
			VALUES (
				#{related_id, jdbcType=NUMERIC}, #{survey_type, jdbcType=NUMERIC} , #{apply_section, jdbcType=NUMERIC} , #{internal_resualt, jdbcType=VARCHAR} , #{survey_rule_id, jdbcType=NUMERIC} , #{survey_doc_id, jdbcType=NUMERIC} , #{survey_per, jdbcType=NUMERIC} 
				, #{remark, jdbcType=VARCHAR} , #{survey_status, jdbcType=NUMERIC} , SYSDATE , #{survey_mode, jdbcType=VARCHAR} , #{repeal_reason, jdbcType=VARCHAR} , #{case_no, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} 
				, #{survey_desc, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{apply_org, jdbcType=VARCHAR} , #{apply_id, jdbcType=NUMERIC} , #{biz_type, jdbcType=NUMERIC} , #{survey_advice, jdbcType=VARCHAR} 
				, #{survey_reason, jdbcType=VARCHAR} , SYSDATE , #{cs_apply_code, jdbcType=VARCHAR} , #{survey_org, jdbcType=VARCHAR} , SYSDATE , #{locial_flag_code, jdbcType=VARCHAR} , #{cs_background, jdbcType=VARCHAR} 
				, #{case_id, jdbcType=NUMERIC} , #{cs_accept_code, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{survey_by_level, jdbcType=NUMERIC} , #{survey_code, jdbcType=VARCHAR} , #{apply_per, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{cs_item, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSurveyApply" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_SURVEY_APPLY WHERE APPLY_ID = #{apply_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSurveyApply" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_SURVEY_APPLY ]]>
		<set>
		<trim suffixOverrides=",">
		    RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
		    SURVEY_TYPE = #{survey_type, jdbcType=NUMERIC} ,
		    APPLY_SECTION = #{apply_section, jdbcType=NUMERIC} ,
			INTERNAL_RESUALT = #{internal_resualt, jdbcType=VARCHAR} ,
		    SURVEY_RULE_ID = #{survey_rule_id, jdbcType=NUMERIC} ,
		    SURVEY_DOC_ID = #{survey_doc_id, jdbcType=NUMERIC} ,
		    SURVEY_PER = #{survey_per, jdbcType=NUMERIC} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
		    SURVEY_STATUS = #{survey_status, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
			SURVEY_MODE = #{survey_mode, jdbcType=VARCHAR} ,
			REPEAL_REASON = #{repeal_reason, jdbcType=VARCHAR} ,
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			SURVEY_DESC = #{survey_desc, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			APPLY_ORG = #{apply_org, jdbcType=VARCHAR} ,
		    BIZ_TYPE = #{biz_type, jdbcType=NUMERIC} ,
			SURVEY_ADVICE = #{survey_advice, jdbcType=VARCHAR} ,
			SURVEY_REASON = #{survey_reason, jdbcType=VARCHAR} ,
			CS_APPLY_CODE = #{cs_apply_code, jdbcType=VARCHAR} ,
			SURVEY_ORG = #{survey_org, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			LOCIAL_FLAG_CODE = #{locial_flag_code, jdbcType=VARCHAR} ,
			CS_BACKGROUND = #{cs_background, jdbcType=VARCHAR} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			CS_ACCEPT_CODE = #{cs_accept_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SURVEY_BY_LEVEL = #{survey_by_level, jdbcType=NUMERIC} ,
			SURVEY_CODE = #{survey_code, jdbcType=VARCHAR} ,
		    APPLY_PER = #{apply_per, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CS_ITEM = #{cs_item, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE APPLY_ID = #{apply_id} ]]>
	</update>



	<!-- 修改操作，根据applycode修改 -->
	<update id="updateSurveyApplyByApplyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_SURVEY_APPLY ]]>
		<set>
		<trim suffixOverrides=",">
		    SURVEY_STATUS = #{survey_status, jdbcType=NUMERIC},
		     APPLY_DATE = #{apply_date, jdbcType=DATE},
		     REMARK = #{remark,jdbcType=VARCHAR},
		     SURVEY_PER = #{survey_per,jdbcType=NUMERIC}
		</trim>
		</set>
		<![CDATA[ WHERE APPLY_CODE = #{apply_code, jdbcType=VARCHAR}  AND SURVEY_DOC_ID = #{survey_doc_id}]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="UW_findSurveyApplyByApplyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE,
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.LOCIAL_FLAG_CODE, A.CS_BACKGROUND, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM DEV_UW.T_SURVEY_APPLY A WHERE 1 = 1  ]]>
		<include refid="querySurveyApplyByApplyCode" />
		<include refid="querySurveyApplyBySurveyDocId" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSurveyApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.LOCIAL_FLAG_CODE, A.CS_BACKGROUND, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM DEV_UW.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APPLY_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSurveyApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE,
			(SELECT REAL_NAME FROM DEV_PAS.T_UDMP_USER WHERE A.INSERT_BY = USER_ID) INSERT_NAME,
			A.INSERT_BY as INSERT_CODE,
			(SELECT REASON_DESC FROM DEV_UW.T_EXAMINE_REASON WHERE A.SURVEY_REASON = REASON_CODE) SURVEY_REASON,
			 A.CS_APPLY_CODE, A.SURVEY_ORG, A.LOCIAL_FLAG_CODE, A.CS_BACKGROUND, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM DEV_UW.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" survey_status  != null "><![CDATA[ AND A.SURVEY_STATUS = #{survey_status} ]]></if>
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findSurveyApplyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_SURVEY_APPLY A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="querySurveyApplyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RELATED_ID, B.SURVEY_TYPE, B.APPLY_SECTION, B.INTERNAL_RESUALT, B.SURVEY_RULE_ID, B.SURVEY_DOC_ID, B.SURVEY_PER, 
			B.REMARK, B.SURVEY_STATUS, B.APPLY_DATE, B.SURVEY_MODE, B.REPEAL_REASON, B.CASE_NO, B.APPLY_CODE, 
			B.SURVEY_DESC, B.APPLY_ORG, B.APPLY_ID, B.BIZ_TYPE, B.SURVEY_ADVICE, 
			B.SURVEY_REASON, B.CS_APPLY_CODE, B.SURVEY_ORG, B.LOCIAL_FLAG_CODE, B.CS_BACKGROUND, 
			B.CASE_ID, B.CS_ACCEPT_CODE, B.POLICY_CODE, B.SURVEY_BY_LEVEL, B.SURVEY_CODE, B.APPLY_PER, 
			B.CS_ITEM FROM (
					SELECT ROWNUM RN, A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.LOCIAL_FLAG_CODE, A.CS_BACKGROUND, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM DEV_UW.T_SURVEY_APPLY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 按条件查询操作 -->	
	<select id="findSurveyApplyByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.LOCIAL_FLAG_CODE, A.CS_BACKGROUND, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM DEV_UW.T_SURVEY_APPLY A WHERE 1 = 1  AND A.BIZ_TYPE = '3']]>
		<include refid="querySurveyApplyByApplyCode" />
		<include refid="querySurveyApplyBySurveyDocId" />
	</select>
	
	<select id="UW_querySurveyMessageNew" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT (SELECT TO_CHAR(WM_CONCAT((SELECT EXAMINE_NAME
                                   FROM DEV_UW.T_EXAMINE_CODE
                                  WHERE E2.EXAMINE_CODE = EXAMINE_CODE)))
          FROM DEV_UW.T_SURVIVAL_INVESTIGATION_DETAI E2
         WHERE E1.RREPORT_ID = E2.RREPORT_ID) AS EXAMINE_CODE,
	       E1.CUSTOMER_NAME,
	       E3.REMARK,
	       E3.SURVEY_STATUS,
	       E3.SURVEY_DOC_ID,
	       E3.APPLY_CODE,
	       E3.APPLY_ID
	  FROM DEV_UW.T_SURVIVAL_INVESTIGATION E1,
	       DEV_UW.T_SURVEY_APPLY E3
	  WHERE E1.DOC_LIST_ID = E3.SURVEY_DOC_ID
	   	AND E3.SURVEY_DOC_ID = #{survey_doc_id}
	</select>
	
	<!-- 修改生调通知书状态  2016-12-16-->
	<update id="updateSurvivalStatusByDocListIdAndUwId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_SURVIVAL_INVESTIGATION ]]>
		<set>
		<trim suffixOverrides=",">
			STATUS = #{status, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE DOC_LIST_ID = #{doc_list_id} 
						AND UW_ID = #{uw_id} ]]>
	</update>
	
	<!-- 操作履历下生调结果查询-->
	<select id="uw_querySurveyApply2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT  
						M.APPLY_CODE,
       					APP.SURVEY_PER,
				        APP.APPLY_PER,
				        APP.APPLY_DATE,
				        APP.SURVEY_STATUS,
				        APP.APPLY_ID,
					    APP.SURVEY_REASON
				 FROM 	DEV_UW.T_SURVEY_APPLY APP,
				 		DEV_UW.T_CONTRACT_MASTER M
				 WHERE  APP.POLICY_CODE = M.POLICY_CODE 		
					     
                  ]]>
		<if test=" apply_code  != null "><![CDATA[ AND M.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	
	<!-- 操作履历下生调结果查询,查询回复人-->
	<select id="uw_querySurveyApply_orgOpr1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT C.ORG_OPR,
						A.APPLY_ID
				 FROM   DEV_UW.T_SURVEY_APPLY A,
					    DEV_UW.T_SURVEY_CONCLUSION C
				 WHERE  A.APPLY_ID = C.APPLY_ID]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>	
	</select>
	
	<!-- 查询回复日期 -->
	<select id="uw_queryReplydate2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT APP.APPLY_ID ,
                     MAX(CON.FINISH_DATE) MAXFINISHDATE
                FROM DEV_UW.T_SURVEY_APPLY APP,
                     DEV_UW.T_SURVEY_CONCLUSION CON
                WHERE APP.APPLY_ID = CON.APPLY_ID
                  ]]>
		<if test=" apply_id  != null "><![CDATA[ AND APP.APPLY_ID = #{apply_id} ]]></if>
		<![CDATA[GROUP BY APP.APPLY_ID   ]]>
	</select>
	
	<!-- 操作履历下生调结果查询SurveyConclusion-->
	<select id="uw_querySurveyApplyCon0" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.APPLY_PER
				       ,A.APPLY_DATE
				       ,CON.FINISHDATE
				       ,FEE.SURVEYFEE
				       ,C.REMARK
				       ,C.SURVEY_CONCLUSION
				       ,C.POSITIVE_FLAG
				FROM 	DEV_UW.T_SURVEY_APPLY A ,
						DEV_UW.T_SURVEY_CONCLUSION C,
					  (SELECT A.APPLY_ID , MAX(C.FINISH_DATE) FINISHDATE
					  FROM DEV_UW.T_SURVEY_APPLY A ,
					       DEV_UW.T_SURVEY_CONCLUSION C
					  WHERE A.APPLY_ID = C.APPLY_ID
					  GROUP BY A.APPLY_ID) CON ,
					  (SELECT A.APPLY_ID,SUM(F.SURVEY_FEE) SURVEYFEE
					  FROM DEV_UW.T_SURVEY_APPLY A
					  ,DEV_UW.T_SURVEY_FEE F
					  WHERE A.APPLY_ID = F.APPLY_ID
					  GROUP BY A.APPLY_ID) FEE 
				WHERE A.APPLY_ID = C.APPLY_ID
				AND   A.APPLY_ID = CON.APPLY_ID
				AND   A.APPLY_ID = FEE.APPLY_ID]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>	
	</select>
	
	<!-- 操作履历下生调结果查询SurveyConclusion-->
	<select id="uw_querySurveyApplyCon1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.APPLY_PER
				       ,A.APPLY_DATE
				       ,C.REMARK
				       ,C.SURVEY_CONCLUSION
				       ,C.POSITIVE_FLAG
				FROM 	DEV_UW.T_SURVEY_APPLY A ,
						DEV_UW.T_SURVEY_CONCLUSION C
				WHERE A.APPLY_ID = C.APPLY_ID
				]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>	
	</select>
	
	<!-- 查询生调费用 -->
	<select id="uw_queryRReportFee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_ID,SUM(F.SURVEY_FEE) SURVEYFEE
					  FROM DEV_UW.T_SURVEY_APPLY A
					  ,DEV_UW.T_SURVEY_FEE F
					  WHERE A.APPLY_ID = F.APPLY_ID
                  ]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<![CDATA[GROUP BY A.APPLY_ID   ]]>
	</select>
	<!-- 操作履历下生调结果查询SurveyFee-->
	<select id="uw_querySurveyApplyFee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT ITEM.SURVEY_ITEM 
				       ,ITYPE.VALUE
				       ,ITEM.REMARK
				FROM DEV_UW.T_SURVEY_ITEM ITEM,
				     DEV_UW.T_SURVEY_ITEM_TYPE ITYPE,
				     DEV_UW.T_SURVEY_APPLY A
				WHERE A.APPLY_ID = ITEM.APPLY_ID
				AND   ITEM.SURVEY_ITEM = ITYPE.CODE]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>	
	</select>	
	<!-- 操作履历下生调结果查询SurveyFee-->
	<select id="pa_querySurveyApplyFee" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT ITEM.SURVEY_ITEM 
				       ,ITYPE.VALUE
				       ,ITEM.REMARK
				FROM DEV_PAS.T_SURVEY_ITEM ITEM,
				     DEV_PAS.T_SURVEY_ITEM_TYPE ITYPE,
				     DEV_PAS.T_SURVEY_APPLY A
				WHERE A.APPLY_ID = ITEM.APPLY_ID
				AND   ITEM.SURVEY_ITEM = ITYPE.CODE]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>	
	</select>
	<!-- 操作履历下生调结果查询SurveyConclusion-->
	<select id="pa_querySurveyApplyCon1" resultType="java.util.Map" parameterType="java.util.Map">
		
		<![CDATA[   select A.SURVEY_PER,A.APPLY_PER,A.APPLY_DATE,A.SURVEY_STATUS,A.APPLY_ID,A.SURVEY_REASON,CON.FINISHDATE,FEE.SURVEYFEE,B.REMARK
               ,B.SURVEY_CONCLUSION
               ,B.POSITIVE_FLAG
        
        from  DEV_UW.T_SURVEY_APPLY A
        left join  DEV_UW.T_CONTRACT_MASTER M on  A.APPLY_CODE = M.APPLY_CODE
        LEFT JOIN DEV_UW.T_SURVEY_CONCLUSION B  ON A.APPLY_ID=B.APPLY_ID
        LEFT JOIN (SELECT A.APPLY_ID , MAX(C.FINISH_DATE) FINISHDATE
            FROM DEV_UW.T_SURVEY_APPLY A ,
                 DEV_UW.T_SURVEY_CONCLUSION C
            WHERE A.APPLY_ID = C.APPLY_ID
            GROUP BY A.APPLY_ID) CON
         on  CON.APPLY_ID=A.APPLY_ID
         left join  (SELECT A.APPLY_ID,SUM(F.SURVEY_FEE) SURVEYFEE
            FROM DEV_UW.T_SURVEY_APPLY A
            ,DEV_UW.T_SURVEY_FEE F
            WHERE A.APPLY_ID = F.APPLY_ID
            GROUP BY A.APPLY_ID) FEE on A.APPLY_ID = FEE.APPLY_ID where M.APPLY_CODE=#{apply_code} ]]>
	</select>
	<select id="pa_querySurveyApplyCon1Policy" resultType="java.util.Map" parameterType="java.util.Map">
		
		<![CDATA[   select A.SURVEY_PER,A.APPLY_PER,A.APPLY_DATE,A.SURVEY_STATUS,A.APPLY_ID,A.SURVEY_REASON,CON.FINISHDATE,FEE.SURVEYFEE,B.REMARK
               ,B.SURVEY_CONCLUSION
               ,B.POSITIVE_FLAG
        
        from  DEV_PAS.T_SURVEY_APPLY A
        left join  DEV_PAS.T_CONTRACT_MASTER M on A.POLICY_CODE = M.POLICY_CODE
        LEFT JOIN DEV_PAS.T_SURVEY_CONCLUSION B  ON A.APPLY_ID=B.APPLY_ID
        LEFT JOIN (SELECT A.APPLY_ID , MAX(C.FINISH_DATE) FINISHDATE
            FROM DEV_PAS.T_SURVEY_APPLY A ,
                 DEV_PAS.T_SURVEY_CONCLUSION C
            WHERE A.APPLY_ID = C.APPLY_ID
            GROUP BY A.APPLY_ID) CON
         on  CON.APPLY_ID=A.APPLY_ID
         left join  (SELECT A.APPLY_ID,SUM(F.SURVEY_FEE) SURVEYFEE
            FROM DEV_PAS.T_SURVEY_APPLY A
            ,DEV_PAS.T_SURVEY_FEE F
            WHERE A.APPLY_ID = F.APPLY_ID
            GROUP BY A.APPLY_ID) FEE on A.APPLY_ID = FEE.APPLY_ID where M.POLICY_CODE=#{policy_code} ]]>
	</select>
	<!-- 操作履历下生调结果查询,查询回复人-->
	<select id="pa_querySurveyApply_orgOpr1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT C.ORG_OPR,
						A.APPLY_ID
				 FROM   DEV_PAS.T_SURVEY_APPLY A,
					    DEV_PAS.T_SURVEY_CONCLUSION C
				 WHERE  A.APPLY_ID = C.APPLY_ID]]>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>	
	</select>
	
	<select id="pa_querySurveyApplyFeeByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select b.EXAMINE_CODE,
               d.EXAMINE_NAME,
               a.EXAMINE_OTHER
          from DEV_UW.t_survival_investigation       a,
               DEV_UW.T_SURVIVAL_INVESTIGATION_DETAI b,
               DEV_UW.T_EXAMINE_CODE                 d
         where a.rreport_id = b.rreport_id
         and b.examine_code = d.examine_code]]>
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>	
	</select>
	

	<!-- 理赔管理-质检复勘结果明细清单使用通过赔案号查询调查申请 -->
	<select id="findSurveyApplyByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.LOCIAL_FLAG_CODE, A.CS_BACKGROUND, 
			A.CASE_ID, A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM APP___CLM__DBUSER.T_SURVEY_APPLY A WHERE 1 = 1 ]]>
		<![CDATA[ AND A.CASE_NO = #{case_no} ]]>
	</select>
	
</mapper>

