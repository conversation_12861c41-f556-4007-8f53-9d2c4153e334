<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.NbPremArapDaoImpl">

	<!-- 按索引生成的查询条件 -->
	<sql id="NB_findProductNameByInternalIdCondition">
		<if test=" internal_id != null and internal_id != '' "><![CDATA[ AND A.INTERNAL_ID = #{internal_id} ]]></if>
	</sql>
	<sql id="NB_queryPremArapByNotFeeStatusCondition">
		<if test=" fee_status != null and fee_status != '' "><![CDATA[ AND A.FEE_STATUS != #{fee_status} ]]></if>
	</sql>
	<sql id="NB_queryPremArapByApplyCodeCondition">
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>

	<select id="NB_findProductNameByInternalId" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT A.INTERNAL_ID,A.PRODUCT_NAME FROM DEV_NB.T_PRODUCT_LIFE A WHERE
		1=1
		<include refid="NB_findProductNameByInternalIdCondition" />
	</select>

	<select id="NB_findAllPremArapByNotFeeStatus" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADD_PREM, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BATCH_NO, A.BUSI_PROD_NAME, A.PAYEE_PHONE, 
      A.UNIT_NUMBER, A.PAID_COUNT, A.FEE_TYPE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, 
      A.CHARGE_YEAR,  A.IS_RISK_MAIN, A.POSTED, A.CUST_CERTI_TYPE, 
      A.INSURED_NAME, A.AMOUNT, A.INSURED_ID, A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.POLICY_CODE, A.PAY_MODE, 
      A.BRANCH_CODE, A.PRE_RECEIVE_TYPE, A.VALIDATE_DATE, A.ARAP_FEE_ID, A.CERTI_TYPE, 
      A.MONEY_CODE, A.BUSINESS_CODE,  A.PAYEE_NAME, A.BANK_ACCOUNT,  A.PRODUCT_CODE, 
      A.ITEM_ID, A.FINISH_TIME, A.DUE_TIME, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.FEE_AMOUNT, 
      A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER,  A.POLICY_YEAR, A.BANK_USER_NAME, 
      A.FEE_STATUS, A.HIGH_SA_INDI, A.SYNC_FLAG,  A.PAY_END_DATE, A.DERIV_TYPE,A.WINNING_START_FLAG,
      A.CUST_CERTI_CODE, A.CHARGE_PERIOD, A.ARAP_FLAG, A.BANK_CODE, A.REFEFLAG, A.AGENT_CODE, A.PREM_FREQ,A.INSERT_TIME,A.ANNUAL_INCOME FROM DEV_NB.T_NB_PREM_ARAP A
      WHERE 1=1 ]]>
		<include refid="NB_queryPremArapByNotFeeStatusCondition" />
		<include refid="NB_queryPremArapByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.ARAP_FEE_ID      ]]>
	</select>
	
		<select id="NB_findotherProductInforMation" resultType="java.util.Map" parameterType="java.util.Map">
		
        SELECT A.PRODUCT_CODE ,B.FIELD1,B.FIELD2,B.FIELD3,B.FIELD4,B.FIELD5,B.FIELD6
        ,B.FIELD7,B.FIELD8,B.FIELD9,B.FIELD10,B.FIELD11,B.FIELD12,B.FIELD13,B.FIELD14
        ,B.FIELD15,B.FIELD16,B.FIELD17,B.FIELD18,B.FIELD19,B.FIELD20
        <if test=" apply_code  != null and  apply_code !=''  " >  
        <![CDATA[
         FROM  APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT A,APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT_OTHER B WHERE
         A.PRODUCT_ID = B.PRODUCT_ID  AND A.APPLY_CODE = B.APPLY_CODE
         AND B.APPLY_CODE = #{apply_code}
         AND B.BUSI_ITEM_ID = #{busi_item_id}
		 ]]>
		 </if> 
		<if test=" policy_code  != null and  policy_code !=''  " >
        <![CDATA[
         FROM  APP___PAS__DBUSER.T_CONTRACT_PRODUCT A,APP___PAS__DBUSER.T_CONTRACT_PRODUCT_OTHER B WHERE 
         A.PRODUCT_ID = B.PRODUCT_ID   AND A.POLICY_CODE = B.POLICY_CODE
         AND B.POLICY_CODE = #{policy_code}
         AND B.BUSI_ITEM_ID = #{busi_item_id}
		 ]]>
		 </if> 
	</select>
	
</mapper>
