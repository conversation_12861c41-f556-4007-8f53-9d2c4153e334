<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.ClaimElecBillListDaoImpl">
	<!-- 查询个数操作 -->
	<select id="findClaimElecBillListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)
					  FROM DEV_CLM.T_CLAIM_CASE A
					 INNER JOIN DEV_CLM.T_CLAIM_BILL B
					    ON A.CASE_ID = B.CASE_ID
					 INNER JOIN DEV_CLM.T_CLAIM_ELEC_BILL_TASK C
					    ON B.CASE_ID=C.CASE_ID
					 INNER JOIN DEV_CLM.T_CLAIM_ELEC_BILL_LOG D
					    ON C.LIST_ID=D.MAIN_LIST_ID WHERE 1=1]]>
	    <if test=" treat_start != null"><![CDATA[ AND D.INTERFACE_DATE>=#{treat_start} ]]></if>
	    <if test=" treat_end != null"><![CDATA[ AND D.INTERFACE_DATE<=#{treat_end} ]]></if>
		<if test=" organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE LIKE #{organ_code} || '%' ]]></if>
	</select>

	<!-- 分页查询操作 -->
	<select id="queryClaimElecBillListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.* FROM (
					SELECT ROWNUM RN, (SELECT MAX(CC.CASE_NO)
											FROM DEV_CLM.T_CLAIM_CASE CC WHERE CC.CASE_STATUS<>'99'
											START WITH CC.CASE_NO = A.CASE_NO
											CONNECT BY PRIOR CC.CASE_NO = CC.RELATED_NO) CASE_NO,
					       B.ELEC_BILL_NUM,
					       B.ELEC_BILL_ID,
					       B.CHECK_CODE,
					       B.ELEC_BILL_DATE,
					       B.TREAT_TYPE,
					       (SELECT CURE_TYPE.NAME FROM DEV_CLM.T_CURE_TYPE CURE_TYPE WHERE CURE_TYPE.CODE=B.TREAT_TYPE) TREAT_TYPE_STR,
					       B.SUM_AMOUNT,
					       D.INTERFACE_NAME,
					       D.INTERFACE_DATE,
					       D.INTERFACE_RESULT,
					       (SELECT IR.RESULT_DESC FROM DEV_CLM.T_INTERFACE_RESULT IR WHERE IR.RESULT_CODE=D.INTERFACE_RESULT) INTERFACE_RESULT_STR,
					       B.ENTRY_AMOUNT,
					       A.END_CASE_TIME,
					       (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID=A.SIGNER_ID) SIGNER_NAME,
					       B.ELEC_BILL_CHECK,
					       A.ORGAN_CODE
					  FROM DEV_CLM.T_CLAIM_CASE A
					 INNER JOIN DEV_CLM.T_CLAIM_BILL B
					    ON A.CASE_ID = B.CASE_ID
					 INNER JOIN DEV_CLM.T_CLAIM_ELEC_BILL_TASK C
					    ON B.CASE_ID=C.CASE_ID
					 INNER JOIN DEV_CLM.T_CLAIM_ELEC_BILL_LOG D
					    ON C.LIST_ID=D.MAIN_LIST_ID WHERE ROWNUM <= #{LESS_NUM}]]> 
        <if test=" treat_start != null"><![CDATA[ AND D.INTERFACE_DATE>=#{treat_start} ]]></if>
	    <if test=" treat_end != null"><![CDATA[ AND D.INTERFACE_DATE<=#{treat_end} ]]></if>
	    <if test=" organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE LIKE #{organ_code} || '%' ]]></if>
	    <![CDATA[ORDER BY A.END_CASE_TIME) B
			WHERE B.RN > #{GREATER_NUM}]]>
	</select>
	
	<!-- 查询广西电票个数操作 -->
	<select id="findCbitElecBillListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)
					  FROM DEV_CLM.T_CLAIM_CASE A
					 INNER JOIN DEV_CLM.T_CLAIM_BILL B
					    ON A.CASE_ID = B.CASE_ID
					 INNER JOIN DEV_CLM.T_CLAIM_CBIT_ELEC_BILL_TASK C
					    ON B.CASE_ID=C.CASE_ID AND B.BILL_ID = C.BILL_ID
					 INNER JOIN DEV_CLM.T_CLAIM_CBIT_ELEC_BILL_LOG D
					    ON C.LIST_ID=D.MAIN_LIST_ID WHERE 1=1]]>
	    <if test=" treat_start != null"><![CDATA[ AND D.INTERFACE_DATE>=#{treat_start} ]]></if>
	    <if test=" treat_end != null"><![CDATA[ AND D.INTERFACE_DATE<=#{treat_end} ]]></if>
	    <if test=" organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE LIKE #{organ_code} || '%' ]]></if>
	</select>

	<!-- 分页查询广西电票操作 -->
	<select id="queryCbitElecBillListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.* FROM (
					SELECT ROWNUM RN, (SELECT MAX(CC.CASE_NO)
											FROM DEV_CLM.T_CLAIM_CASE CC WHERE CC.CASE_STATUS<>'99'
											START WITH CC.CASE_NO = A.CASE_NO
											CONNECT BY PRIOR CC.CASE_NO = CC.RELATED_NO) CASE_NO,
					       B.ELEC_BILL_NUM,
					       B.ELEC_BILL_ID,
					       B.CHECK_CODE,
					       B.ELEC_BILL_DATE,
					       B.TREAT_TYPE,
					       (SELECT CURE_TYPE.NAME FROM DEV_CLM.T_CURE_TYPE CURE_TYPE WHERE CURE_TYPE.CODE=B.TREAT_TYPE) TREAT_TYPE_STR,
					       B.SUM_AMOUNT,
					       D.INTERFACE_NAME,
					       D.INTERFACE_DATE,
					       D.INTERFACE_RESULT,
					       (CASE WHEN D.INTERFACE_RESULT ='S0000' THEN '成功' ELSE '失败' END) AS INTERFACE_RESULT_STR,
					       B.ENTRY_AMOUNT,
					       A.END_CASE_TIME,
					       (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID=A.SIGNER_ID) SIGNER_NAME,
					       B.ELEC_BILL_CHECK,
					       A.ORGAN_CODE
					  FROM DEV_CLM.T_CLAIM_CASE A
					 INNER JOIN DEV_CLM.T_CLAIM_BILL B
					    ON A.CASE_ID = B.CASE_ID
					 INNER JOIN DEV_CLM.T_CLAIM_CBIT_ELEC_BILL_TASK C
					    ON B.CASE_ID=C.CASE_ID AND B.BILL_ID = C.BILL_ID
					 INNER JOIN DEV_CLM.T_CLAIM_CBIT_ELEC_BILL_LOG D
					    ON C.LIST_ID=D.MAIN_LIST_ID WHERE ROWNUM <= #{LESS_NUM}]]> 
        <if test=" treat_start != null"><![CDATA[ AND D.INTERFACE_DATE>=#{treat_start} ]]></if>
	    <if test=" treat_end != null"><![CDATA[ AND D.INTERFACE_DATE<=#{treat_end} ]]></if>
	    <if test=" organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE LIKE #{organ_code} || '%' ]]></if>
	    <![CDATA[ORDER BY A.END_CASE_TIME) B
			WHERE B.RN > #{GREATER_NUM}]]>
	</select>
	<!-- 查询云南电票个数操作 -->
	<select id="findYbitElecBillListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
	<![CDATA[ SELECT COUNT(1)
            FROM DEV_CLM.T_CLAIM_CASE A
           INNER JOIN DEV_CLM.T_CLAIM_BILL B
              ON A.CASE_ID = B.CASE_ID AND B.SERVCOM='06'
           INNER JOIN DEV_CLM.T_CLAIM_MEDI_ELEC_BILL_TASK C
              ON B.CASE_ID=C.CASE_ID 
           INNER JOIN DEV_CLM.T_CLAIM_MEDI_ELEC_BILL_LOG D
              ON C.LIST_ID=D.MAIN_LIST_ID WHERE 1=1]]>
	    <if test=" treat_start != null"><![CDATA[ AND D.INTERFACE_DATE>=#{treat_start} ]]></if>
	    <if test=" treat_end != null"><![CDATA[ AND D.INTERFACE_DATE<=#{treat_end} ]]></if>
	    <if test=" organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE LIKE #{organ_code} || '%' ]]></if>
	</select>
	<!-- 分页查询云南电票操作 -->
	<select id="queryYbitElecBillListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.* FROM (
         SELECT ROWNUM RN, (SELECT MAX(CC.CASE_NO)
                      FROM DEV_CLM.T_CLAIM_CASE CC WHERE CC.CASE_STATUS<>'99'
                      START WITH CC.CASE_NO = A.CASE_NO 
                      CONNECT BY PRIOR CC.CASE_NO = CC.RELATED_NO) CASE_NO,
                 B.ELEC_BILL_NUM,
                 B.ELEC_BILL_ID,
                 B.CHECK_CODE,
                 B.ELEC_BILL_DATE,
                 B.TREAT_TYPE,
                 (SELECT CURE_TYPE.NAME FROM DEV_CLM.T_CURE_TYPE CURE_TYPE WHERE CURE_TYPE.CODE=B.TREAT_TYPE) TREAT_TYPE_STR,
                 B.SUM_AMOUNT,
                 D.INTERFACE_NAME,
                 D.INTERFACE_DATE,
                 D.INTERFACE_RESULT,
                 (SELECT IR.NAME FROM DEV_CLM.T_ELECBILL_INTER_RESULT IR WHERE IR.CODE=D.INTERFACE_RESULT) INTERFACE_RESULT_STR,
                 B.ENTRY_AMOUNT,
                 A.END_CASE_TIME,
                 (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID=A.SIGNER_ID) SIGNER_NAME,
                 B.ELEC_BILL_CHECK,
                 A.ORGAN_CODE
            FROM DEV_CLM.T_CLAIM_CASE A
           INNER JOIN DEV_CLM.T_CLAIM_BILL B
              ON A.CASE_ID = B.CASE_ID AND B.SERVCOM='06'
           INNER JOIN DEV_CLM.T_CLAIM_MEDI_ELEC_BILL_TASK C
              ON B.CASE_ID=C.CASE_ID
           INNER JOIN DEV_CLM.T_CLAIM_MEDI_ELEC_BILL_LOG D
              ON C.LIST_ID=D.MAIN_LIST_ID WHERE ROWNUM <= #{LESS_NUM}]]> 
        <if test=" treat_start != null"><![CDATA[ AND D.INTERFACE_DATE>=#{treat_start} ]]></if>
	    <if test=" treat_end != null"><![CDATA[ AND D.INTERFACE_DATE<=#{treat_end} ]]></if>
	    <if test=" organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE LIKE #{organ_code} || '%' ]]></if>
	    <![CDATA[ORDER BY A.END_CASE_TIME) B
			WHERE B.RN > #{GREATER_NUM}]]>
	</select>
</mapper>
