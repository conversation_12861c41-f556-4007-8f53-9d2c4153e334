<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IDocumentQueryDao">
	
	<!--柜面自助终端- 【新】 红利信息查询(打印通知书类)接口查询通知书信息  -->
	<select id="QRY_PAS_CLM_queryClobContent" resultType="java.util.Map" parameterType="java.util.Map">
	    <choose>
	    	<when test="flag == '保单' ">
	    			<![CDATA[select a.clob_id,a.content from dev_pas.t_clob a where a.clob_id=#{clob_id}]]>
	    	</when>
	    	<when test="flag == '理赔' ">
	    			<![CDATA[select a.clob_id,a.content from dev_clm.t_clob a where a.clob_id=#{clob_id}]]>
	    	</when>
	    	<otherwise>
			</otherwise>
	    </choose>
		
	</select>
	
	<!-- 柜面自助终端- 【新】 红利信息查询(打印通知书类) -->
	<select id="QRY_PAS_CLM_findAllBonusDocumentInfo" resultType="java.util.Map" parameterType="java.util.Map">
		
		<choose>
	    	<when test="flag == '保单' ">
	    		<![CDATA[
					select a.buss_code,a.template_code,a.create_time,a.clob_id
		          	from DEV_PAS.V_DOCUMENT_ALL a 
		          	where 1=1 
				]]>
				<if test=" template_code  != null and template_code  != '' "><![CDATA[ and a.template_code = #{template_code}]]></if> 
				<if test=" policy_code  != null and policy_code  != '' "><![CDATA[and a.policy_code = #{policy_code}]]></if> 
				<if test=" buss_code  != null and buss_code  != '' "><![CDATA[ and a.buss_code = #{buss_code}]]></if> 
			    <if test=" year  != null and year  != '' "><![CDATA[and to_char(a.create_time,'YYYY') = #{year}]]></if> 
	    		
	    	</when>
	    	
	    	<when test="flag == '理赔' ">
	    		<![CDATA[
					select a.buss_code,a.template_code,a.create_time,a.clob_id
		          	from dev_clm.t_document a 
		          	where 1=1 
		          	and a.buss_code = #{policy_code}
				]]>
				<if test=" template_code  != null and template_code  != '' "><![CDATA[ and a.template_code = #{template_code}]]></if> 
			    <if test=" year  != null and year  != '' "><![CDATA[and to_char(a.create_time,'YYYY') = #{year}]]></if> 
	    	</when>
	    	
	    	<otherwise>
			</otherwise>
	    </choose>
	    
	</select>
	
	
</mapper>