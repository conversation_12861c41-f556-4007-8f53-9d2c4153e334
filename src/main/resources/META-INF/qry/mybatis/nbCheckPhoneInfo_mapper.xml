<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.impl.nb.dao.impl.CheckPhoneDaoImpl">

	<sql id="NB_nbCheckPhone">
		<if test=" organ_code != null and organ_code != ''  "> AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%') </if>
		<if test=" submit_channel  != null and submit_channel != ''  "><![CDATA[ AND A.SUBMIT_CHANNEL IN (${submit_channel}) ]]></if>
		<if test=" channel_type  != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE IN (${channel_type}) ]]></if>
	</sql>
	<sql id="NB_nbCheckPhoneSelectCondition">
		<if test=" organ_code != null and submit_channel == null and channel_type == null "><![CDATA[ CC.ORGAN_CODE AS ORGAN_CODE,CC.ORGAN_NAME AS ORGAN_NAME,'00' AS CHANNEL_TYPE,'0' AS SUBMIT_CHANNEL ]]></if>
		<if test=" organ_code != null and submit_channel == null and channel_type != null "><![CDATA[ CC.ORGAN_CODE AS ORGAN_CODE,CC.ORGAN_NAME AS ORGAN_NAME,CC.CHANNEL_TYPE AS CHANNEL_TYPE,'0' AS SUBMIT_CHANNEL]]></if>
		<if test=" organ_code != null and submit_channel != null and channel_type == null "><![CDATA[ CC.ORGAN_CODE AS ORGAN_CODE,CC.ORGAN_NAME AS ORGAN_NAME,'00' AS CHANNEL_TYPE,CC.SUBMIT_CHANNEL AS SUBMIT_CHANNEL]]></if>
	</sql>
	
	<sql id="NB_nbCheckPhoneGroupCondition">
		<if test=" organ_code != null and submit_channel == null and channel_type==null "><![CDATA[ GROUP BY CC.ORGAN_CODE,CC.ORGAN_NAME ]]></if>
		<if test=" organ_code != null and submit_channel == null and channel_type!=null "><![CDATA[ GROUP BY CC.ORGAN_CODE,CC.ORGAN_NAME,CC.CHANNEL_TYPE]]></if>
		<if test=" organ_code != null and submit_channel != null and channel_type==null "><![CDATA[ GROUP BY CC.ORGAN_CODE,CC.ORGAN_NAME,CC.SUBMIT_CHANNEL]]></if>
	</sql>
	
	
	<sql id="NB_DateCheck">
		<if test=" statistical_methods == 1"><![CDATA[ AND A.APPLY_DATE BETWEEN TO_DATE(#{start_date}, 'yyyy-MM-dd hh24:mi:ss') and  TO_DATE(#{end_date}, 'yyyy-MM-dd hh24:mi:ss')/*投保日期*/ ]]></if>
		<if test=" statistical_methods == 2"><![CDATA[ AND A.ISSUE_DATE BETWEEN TO_DATE(#{start_date}, 'yyyy-MM-dd hh24:mi:ss') and  TO_DATE(#{end_date}, 'yyyy-MM-dd hh24:mi:ss')/*签单日期*/  ]]></if>
		<if test=" statistical_methods == 3"><![CDATA[ AND A.VALIDATE_DATE BETWEEN TO_DATE(#{start_date}, 'yyyy-MM-dd hh24:mi:ss') and  TO_DATE(#{end_date}, 'yyyy-MM-dd hh24:mi:ss')/*生效日期*/  ]]></if>
	</sql>
	
	<sql id="NB_DateCheck1">
		<if test=" statistical_methods == 1"><![CDATA[ AND CMS.APPLY_DATE BETWEEN BB.START_DATE AND BB.END_DATE ]]></if>
		<if test=" statistical_methods == 2"><![CDATA[ AND CMS.ISSUE_DATE BETWEEN BB.START_DATE AND BB.END_DATE ]]></if>
		<if test=" statistical_methods == 3"><![CDATA[ AND CMS.VALIDATE_DATE BETWEEN BB.START_DATE AND BB.END_DATE ]]></if>
	</sql>
	
	<sql id="NB_DateCheck2">
		<if test=" statistical_methods == 1"><![CDATA[ AND MS.APPLY_DATE BETWEEN BB.START_DATE AND BB.END_DATE ]]></if>
		<if test=" statistical_methods == 2"><![CDATA[ AND MS.ISSUE_DATE BETWEEN BB.START_DATE AND BB.END_DATE ]]></if>
		<if test=" statistical_methods == 3"><![CDATA[ AND MS.VALIDATE_DATE BETWEEN BB.START_DATE AND BB.END_DATE ]]></if>
	</sql>
	
	<!-- 查询个数操作 -->
	<select id="NB_queryCheckPhoneInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM 
		(SELECT A.ORGAN_CODE,
               A.CHANNEL_TYPE,
               A.SUBMIT_CHANNEL,
               TO_DATE(#{start_date}, 'yyyy-MM-dd hh24:mi:ss') as START_DATE, /*开始日期*/
               TO_DATE(#{end_date}, 'yyyy-MM-dd hh24:mi:ss') as END_DATE, /*结束日期*/
               '1' AS FIND_TYPE
          FROM DEV_NB.T_NB_CONTRACT_MASTER A
          LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY NCC
            ON A.APPLY_CODE = NCC.APPLY_CODE
          LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCCR
            ON NCC.CHECK_ID = NCCR.CHECK_ID
         WHERE 1 = 1
           AND NCCR.IDENTITY_CHECK = '7']]>
    <include refid="NB_nbCheckPhone" />      
    <include refid="NB_DateCheck" />
         <![CDATA[ GROUP BY A.ORGAN_CODE, A.CHANNEL_TYPE, A.SUBMIT_CHANNEL) BB ]]>
	</select>
	<!-- 查询个数操作 销售渠道、投保平台为空时-->
	<select id="NB_queryCheckPhoneInfoTotalByOrg" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM 
		(SELECT SUBSTR(A.ORGAN_CODE, 0, ${organ_code_lenth}) as ORGAN_CODE,
               A.CHANNEL_TYPE,
               A.SUBMIT_CHANNEL,
               TO_DATE(#{start_date}, 'yyyy-MM-dd hh24:mi:ss') as START_DATE, /*开始日期*/
               TO_DATE(#{end_date}, 'yyyy-MM-dd hh24:mi:ss') as END_DATE, /*结束日期*/
               '1' AS FIND_TYPE
          FROM DEV_NB.T_NB_CONTRACT_MASTER A
          LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY NCC
            ON A.APPLY_CODE = NCC.APPLY_CODE
          LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCCR
            ON NCC.CHECK_ID = NCCR.CHECK_ID
         WHERE 1 = 1
           AND NCCR.IDENTITY_CHECK = '7']]>
    <include refid="NB_nbCheckPhone" />      
    <include refid="NB_DateCheck" />
         <![CDATA[ GROUP BY SUBSTR(A.ORGAN_CODE,0,${organ_code_lenth}), A.CHANNEL_TYPE, A.SUBMIT_CHANNEL) BB ]]>
	</select>
		<!-- 分页查询操作 -->
	<select id="NB_queryCheckPhoneInfoForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
		SELECT 
		(SELECT ORGAN_NAME
          FROM APP___NB__DBUSER.T_UDMP_ORG
         WHERE ORGAN_CODE = BB.ORGAN_CODE) AS ORGAN_NAME, /*机构名称*/
       BB.ORGAN_CODE, /*机构编码*/
       BB.CHANNEL_TYPE, /*销售渠道*/
       TO_CHAR(BB.SUBMIT_CHANNEL) AS SUBMIT_CHANNEL, /*投保平台*/
       TO_CHAR((SELECT COUNT(DISTINCT PHA.CUSTOMER_ID)
           FROM DEV_NB.T_NB_CONTRACT_MASTER MS
           LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PHA
             ON PHA.APPLY_CODE = MS.APPLY_CODE
          WHERE 1 = 1
            AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
            AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
            AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[) +
       (SELECT COUNT(DISTINCT ILA.CUSTOMER_ID)
           FROM DEV_NB.T_NB_CONTRACT_MASTER MS
           LEFT JOIN DEV_NB.T_NB_INSURED_LIST ILA
             ON ILA.APPLY_CODE = MS.APPLY_CODE
          WHERE 1 = 1
            AND ILA.RELATION_TO_PH != '00'
            AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
            AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
            AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[) +
       (SELECT COUNT(DISTINCT CBA.CUSTOMER_ID)
           FROM DEV_NB.T_NB_CONTRACT_MASTER MS
           LEFT JOIN DEV_NB.T_NB_CONTRACT_BENE CBA
             ON CBA.APPLY_CODE = MS.APPLY_CODE
           LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PHO 
             ON CBA.APPLY_CODE = PHO.APPLY_CODE
          WHERE 1 = 1
          	AND CBA.CUSTOMER_ID <> PHO.CUSTOMER_ID
            AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
            AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
            AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL
            AND CBA.BENE_TYPE = '1']]>
   <include refid="NB_DateCheck2" />
   <![CDATA[)) AS APPLY_CUSTOMER_COUNT, /*投保客户量*/
       
       (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE))
          FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
          LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
            ON CMS.APPLY_CODE = CCV.APPLY_CODE
          LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
            ON CCV.CHECK_ID = NCVR.CHECK_ID
         WHERE 1 = 1
           AND NCVR.IDENTITY_CHECK = '7'
           AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
           AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
           AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_CHECK_COUNT, /*全量客户核验总量*/
       
       (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE))
  FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND NCVR.STATUS = '5'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_FAIL_COUNT, /*全量客户调优信平台失败量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND NCVR.STATUS = '1'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_PASS_COUNT, /*全量客户核验通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND NCVR.STATUS = '0'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_NOPASS_COUNT, /*全量客户核验不通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND NCVR.STATUS = '4'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_NOTFOUND_COUNT, /*全量客户核验未查得量*/

 (SELECT TO_CHAR(COUNT(DISTINCT PHA.CUSTOMER_ID)) FROM DEV_NB.T_NB_CONTRACT_MASTER MS
  LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PHA
    ON PHA.APPLY_CODE = MS.APPLY_CODE
 WHERE 1 = 1
   AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[ ) AS PH_CUSTOMER_COUNT, /*投保人客户量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_CHECK_COUNT, /*投保人核验总量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND NCVR.STATUS = '5'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_FAIL_COUNT, /*投保人调优信平台失败量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND NCVR.STATUS = '1'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_PASS_COUNT, /*投保人核验通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND NCVR.STATUS = '0'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_NOPASS_COUNT, /*投保人核验不通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND NCVR.STATUS = '4'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_NOTFOUND_COUNT, /*投保人核验未查得量*/

/*被保人开始-------------被保人开始-------------被保人开始-------------被保人开始*/

 (SELECT TO_CHAR(COUNT(DISTINCT LIA.CUSTOMER_ID)) FROM DEV_NB.T_NB_CONTRACT_MASTER MS
  LEFT JOIN DEV_NB.T_NB_INSURED_LIST LIA
    ON LIA.APPLY_CODE = MS.APPLY_CODE
 WHERE 1 = 1
   AND LIA.RELATION_TO_PH != '00'
   AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[ ) AS LI_CUSTOMER_COUNT, /*被保人客户量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS  LI_CUSTOMER_CHECK_COUNT, /*被保人核验总量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND NCVR.STATUS = '5'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS LI_CUSTOMER_FAIL_COUNT, /*被保人调优信平台失败量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND NCVR.STATUS = '1'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS LI_CUSTOMER_PASS_COUNT, /*被保人核验通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND NCVR.STATUS = '0'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS LI_CUSTOMER_NOPASS_COUNT, /*被保人核验不通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND NCVR.STATUS = '4'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS LI_CUSTOMER_NOTFOUND_COUNT, /*被保人核验未查得量*/

/*被保人结束-------------被保人结束-------------被保人结束-------------被保人结束*/

 (SELECT TO_CHAR(COUNT(DISTINCT BEA.CUSTOMER_ID)) FROM DEV_NB.T_NB_CONTRACT_MASTER MS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_BENE BEA
    ON BEA.APPLY_CODE = MS.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PHO 
	ON BEA.APPLY_CODE = PHO.APPLY_CODE 
 WHERE 1 = 1
   AND BEA.CUSTOMER_ID <> PHO.CUSTOMER_ID
   AND BEA.BENE_TYPE = '1'
   AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[ ) AS BE_CUSTOMER_COUNT, /*受益人客户量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS BE_CUSTOMER_CHECK_COUNT, /*受益人核验总量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND NCVR.STATUS = '5'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS BE_CUSTOMER_FAIL_COUNT, /*受益人调优信平台失败量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND NCVR.STATUS = '1'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS BE_CUSTOMER_PASS_COUNT, /*受益人核验通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND NCVR.STATUS = '0'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS BE_CUSTOMER_NOPASS_COUNT, /*受益人核验不通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND NCVR.STATUS = '4'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BE_CUSTOMER_NOTFOUND_COUNT,	 /*受益人核验未查得量*/
/*---120854-核心系统手机号核验回补需求-新契约---start*/
 (SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECK_SUMREPLEN,	/*全量客户回补总量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.RETURN_CODE = 'DY_99' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECKFAIL_SUMREPLEN,		/*全量客户回补失败量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.RETURN_CODE = 'HY_00' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECKADOPT_SUMREPLEN,		/*全量客户回补通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.RETURN_CODE = 'HY_01' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECKNO_SUMREPLEN,		/*全量客户回补不通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.RETURN_CODE = 'HY_02' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECKNOTFOUND_SUMREPLEN,		/*全量客户回补未查得量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_CHECK_SUMREPLEN,		/*投保人回补总量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.RETURN_CODE = 'DY_99' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_FAIL_SUMREPLEN,		/*投保人回补失败量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.RETURN_CODE = 'HY_00' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_ADOPT_SUMREPLEN,		/*投保人回补通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.RETURN_CODE = 'HY_01' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_NOT_SUMREPLEN,		/*投保人回补不通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.RETURN_CODE = 'HY_02' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_CHECKNOTFOUND_SUMREPLEN,		/*投保人回补未查得量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_CHECK_SUMREPLEN,		/*被保险人回补总量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.RETURN_CODE = 'DY_99' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_FAIL_SUMREPLEN,		/*被保险人回补失败量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.RETURN_CODE = 'HY_00' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_ADOPT_SUMREPLEN,		/*被保险人回补通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.RETURN_CODE = 'HY_01' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_NOT_SUMREPLEN,		/*被保险人回补不通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.RETURN_CODE = 'HY_02' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_CHECKNOTFOUND_SUMREPLEN,		/*被保险人回补未查得量*/
   
(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_CHECK_SUMREPLEN,		/*受益人回补总量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.RETURN_CODE = 'DY_99' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_FAIL_SUMREPLEN,		/*受益人回补失败量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.RETURN_CODE = 'HY_00' 
   AND CHT.DERIV_TYPE = 'CB' 
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_ADOPT_SUMREPLEN,		/*受益人回补通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.RETURN_CODE = 'HY_01' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_NOT_SUMREPLEN,		/*受益人回补不通过量*/

 (SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.RETURN_CODE = 'HY_02'
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_CHECKNOTFOUND_SUMREPLEN		/*受益人回补未查得量*/
/*---120854-核心系统手机号核验回补需求-新契约---end*/
 FROM
 (SELECT       
 			   SUBSTR(A.ORGAN_CODE,0,${organ_code_lenth}) as ORGAN_CODE,
               A.CHANNEL_TYPE,
               A.SUBMIT_CHANNEL,
               TO_DATE(#{start_date}, 'yyyy-MM-dd hh24:mi:ss') as START_DATE, /*开始日期*/
               TO_DATE(#{end_date}, 'yyyy-MM-dd hh24:mi:ss') as END_DATE, /*结束日期*/
               '1' AS FIND_TYPE
          FROM DEV_NB.T_NB_CONTRACT_MASTER A
          LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY NCC
            ON A.APPLY_CODE = NCC.APPLY_CODE
          LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCCR
            ON NCC.CHECK_ID = NCCR.CHECK_ID
         WHERE 1 = 1
         AND NCCR.IDENTITY_CHECK = '7']]>
    <include refid="NB_DateCheck" />      
    <include refid="NB_nbCheckPhone" />
         <![CDATA[GROUP BY SUBSTR(A.ORGAN_CODE,0,${organ_code_lenth}), A.CHANNEL_TYPE, A.SUBMIT_CHANNEL) BB ]]>
	</select>
		
	
	<!-- 分页查询操作 销售渠道、投保平台为空时-->
	<select id="NB_queryCheckPhoneInfoForPageByOrg" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT ]]>
		<include refid="NB_nbCheckPhoneSelectCondition" />
		<![CDATA[,
                TO_CHAR(SUM(CC.APPLY_CUSTOMER_COUNT)) AS APPLY_CUSTOMER_COUNT,
                TO_CHAR(SUM(CC.FULL_CUSTOMER_CHECK_COUNT)) AS FULL_CUSTOMER_CHECK_COUNT,
                TO_CHAR(SUM(CC.FULL_CUSTOMER_FAIL_COUNT)) AS FULL_CUSTOMER_FAIL_COUNT,
                TO_CHAR(SUM(CC.FULL_CUSTOMER_PASS_COUNT)) AS FULL_CUSTOMER_PASS_COUNT,
                TO_CHAR(SUM(CC.FULL_CUSTOMER_NOPASS_COUNT)) AS FULL_CUSTOMER_NOPASS_COUNT,
                TO_CHAR(SUM(CC.FULL_CUSTOMER_NOTFOUND_COUNT)) AS FULL_CUSTOMER_NOTFOUND_COUNT,
                TO_CHAR(SUM(CC.PH_CUSTOMER_COUNT)) AS PH_CUSTOMER_COUNT,
                TO_CHAR(SUM(CC.PH_CUSTOMER_CHECK_COUNT)) AS PH_CUSTOMER_CHECK_COUNT,
                TO_CHAR(SUM(CC.PH_CUSTOMER_FAIL_COUNT)) AS PH_CUSTOMER_FAIL_COUNT,
                TO_CHAR(SUM(CC.PH_CUSTOMER_PASS_COUNT)) AS PH_CUSTOMER_PASS_COUNT,
                TO_CHAR(SUM(CC.PH_CUSTOMER_NOPASS_COUNT)) AS PH_CUSTOMER_NOPASS_COUNT,
                TO_CHAR(SUM(CC.PH_CUSTOMER_NOTFOUND_COUNT)) AS PH_CUSTOMER_NOTFOUND_COUNT,
                TO_CHAR(SUM(CC.LI_CUSTOMER_COUNT)) AS LI_CUSTOMER_COUNT,
                TO_CHAR(SUM(CC.LI_CUSTOMER_CHECK_COUNT)) AS LI_CUSTOMER_CHECK_COUNT,
                TO_CHAR(SUM(CC.LI_CUSTOMER_FAIL_COUNT)) AS LI_CUSTOMER_FAIL_COUNT,
                TO_CHAR(SUM(CC.LI_CUSTOMER_PASS_COUNT)) AS LI_CUSTOMER_PASS_COUNT,
                TO_CHAR(SUM(CC.LI_CUSTOMER_NOPASS_COUNT)) AS LI_CUSTOMER_NOPASS_COUNT,
                TO_CHAR(SUM(CC.LI_CUSTOMER_NOTFOUND_COUNT)) AS LI_CUSTOMER_NOTFOUND_COUNT,
                TO_CHAR(SUM(CC.BE_CUSTOMER_COUNT)) AS BE_CUSTOMER_COUNT,
                TO_CHAR(SUM(CC.BE_CUSTOMER_CHECK_COUNT)) AS BE_CUSTOMER_CHECK_COUNT,
                TO_CHAR(SUM(CC.BE_CUSTOMER_FAIL_COUNT)) AS BE_CUSTOMER_FAIL_COUNT,
                TO_CHAR(SUM(CC.BE_CUSTOMER_PASS_COUNT)) AS BE_CUSTOMER_PASS_COUNT,
                TO_CHAR(SUM(CC.BE_CUSTOMER_NOPASS_COUNT)) AS BE_CUSTOMER_NOPASS_COUNT,
                TO_CHAR(SUM(CC.BE_CUSTOMER_NOTFOUND_COUNT)) AS BE_CUSTOMER_NOTFOUND_COUNT,
                TO_CHAR(SUM(CC.CUS_CHECK_SUMREPLEN)) AS CUS_CHECK_SUMREPLEN,
                TO_CHAR(SUM(CC.CUS_CHECKFAIL_SUMREPLEN)) AS CUS_CHECKFAIL_SUMREPLEN,
                TO_CHAR(SUM(CC.CUS_CHECKADOPT_SUMREPLEN)) AS CUS_CHECKADOPT_SUMREPLEN,
                TO_CHAR(SUM(CC.CUS_CHECKNO_SUMREPLEN)) AS CUS_CHECKNO_SUMREPLEN,
                TO_CHAR(SUM(CC.CUS_CHECKNOTFOUND_SUMREPLEN)) AS CUS_CHECKNOTFOUND_SUMREPLEN,
                TO_CHAR(SUM(CC.HOL_CHECK_SUMREPLEN)) AS HOL_CHECK_SUMREPLEN,
                TO_CHAR(SUM(CC.HOL_FAIL_SUMREPLEN)) AS HOL_FAIL_SUMREPLEN,
                TO_CHAR(SUM(CC.HOL_ADOPT_SUMREPLEN)) AS HOL_ADOPT_SUMREPLEN,
                TO_CHAR(SUM(CC.HOL_NOT_SUMREPLEN)) AS HOL_NOT_SUMREPLEN,
                TO_CHAR(SUM(CC.HOL_CHECKNOTFOUND_SUMREPLEN)) AS HOL_CHECKNOTFOUND_SUMREPLEN,
                TO_CHAR(SUM(CC.INS_CHECK_SUMREPLEN)) AS INS_CHECK_SUMREPLEN,
                TO_CHAR(SUM(CC.INS_FAIL_SUMREPLEN)) AS INS_FAIL_SUMREPLEN,
                TO_CHAR(SUM(CC.INS_ADOPT_SUMREPLEN)) AS INS_ADOPT_SUMREPLEN,
                TO_CHAR(SUM(CC.INS_NOT_SUMREPLEN)) AS INS_NOT_SUMREPLEN,
                TO_CHAR(SUM(CC.INS_CHECKNOTFOUND_SUMREPLEN)) AS INS_CHECKNOTFOUND_SUMREPLEN,
                TO_CHAR(SUM(CC.BENE_CHECK_SUMREPLEN)) AS BENE_CHECK_SUMREPLEN,
                TO_CHAR(SUM(CC.BENE_FAIL_SUMREPLEN)) AS BENE_FAIL_SUMREPLEN,
                TO_CHAR(SUM(CC.BENE_ADOPT_SUMREPLEN)) AS BENE_ADOPT_SUMREPLEN,
                TO_CHAR(SUM(CC.BENE_NOT_SUMREPLEN)) AS BENE_NOT_SUMREPLEN,
                TO_CHAR(SUM(CC.BENE_CHECKNOTFOUND_SUMREPLEN)) AS BENE_CHECKNOTFOUND_SUMREPLEN
		FROM 
		(SELECT 
		(SELECT ORGAN_NAME
          FROM APP___NB__DBUSER.T_UDMP_ORG
         WHERE ORGAN_CODE = BB.ORGAN_CODE) AS ORGAN_NAME, /*机构名称*/
       BB.ORGAN_CODE, /*机构编码*/
       BB.CHANNEL_TYPE, /*销售渠道*/
       TO_CHAR(BB.SUBMIT_CHANNEL) AS SUBMIT_CHANNEL, /*投保平台*/
       TO_CHAR((SELECT COUNT(DISTINCT PHA.CUSTOMER_ID)
           FROM DEV_NB.T_NB_CONTRACT_MASTER MS
           LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PHA
             ON PHA.APPLY_CODE = MS.APPLY_CODE
          WHERE 1 = 1
            AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
            AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
            AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[) +
       (SELECT COUNT(DISTINCT ILA.CUSTOMER_ID)
           FROM DEV_NB.T_NB_CONTRACT_MASTER MS
           LEFT JOIN DEV_NB.T_NB_INSURED_LIST ILA
             ON ILA.APPLY_CODE = MS.APPLY_CODE
          WHERE 1 = 1
            AND ILA.RELATION_TO_PH != '00'
            AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
            AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
            AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[) +
       (SELECT COUNT(DISTINCT CBA.CUSTOMER_ID)
           FROM DEV_NB.T_NB_CONTRACT_MASTER MS
           LEFT JOIN DEV_NB.T_NB_CONTRACT_BENE CBA
             ON CBA.APPLY_CODE = MS.APPLY_CODE
           LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PHO 
             ON CBA.APPLY_CODE = PHO.APPLY_CODE
          WHERE 1 = 1
          	AND CBA.CUSTOMER_ID <> PHO.CUSTOMER_ID
            AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
            AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
            AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL
            AND CBA.BENE_TYPE = '1']]>
   <include refid="NB_DateCheck2" />
   <![CDATA[)) AS APPLY_CUSTOMER_COUNT, /*投保客户量*/
       
       (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE))
          FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
          LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
            ON CMS.APPLY_CODE = CCV.APPLY_CODE
          LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
            ON CCV.CHECK_ID = NCVR.CHECK_ID
         WHERE 1 = 1
           AND NCVR.IDENTITY_CHECK = '7'
           AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
           AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
           AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_CHECK_COUNT, /*全量客户核验总量*/
       
       (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE))
  FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND NCVR.STATUS = '5'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_FAIL_COUNT, /*全量客户调优信平台失败量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND NCVR.STATUS = '1'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_PASS_COUNT, /*全量客户核验通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND NCVR.STATUS = '0'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_NOPASS_COUNT, /*全量客户核验不通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND NCVR.STATUS = '4'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS FULL_CUSTOMER_NOTFOUND_COUNT, /*全量客户核验未查得量*/

 (SELECT TO_CHAR(COUNT(DISTINCT PHA.CUSTOMER_ID)) FROM DEV_NB.T_NB_CONTRACT_MASTER MS
  LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PHA
    ON PHA.APPLY_CODE = MS.APPLY_CODE
 WHERE 1 = 1
   AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[ ) AS PH_CUSTOMER_COUNT, /*投保人客户量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_CHECK_COUNT, /*投保人核验总量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND NCVR.STATUS = '5'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_FAIL_COUNT, /*投保人调优信平台失败量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND NCVR.STATUS = '1'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_PASS_COUNT, /*投保人核验通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND NCVR.STATUS = '0'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_NOPASS_COUNT, /*投保人核验不通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '01'
   AND NCVR.STATUS = '4'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS PH_CUSTOMER_NOTFOUND_COUNT, /*投保人核验未查得量*/

/*被保人开始-------------被保人开始-------------被保人开始-------------被保人开始*/

 (SELECT TO_CHAR(COUNT(DISTINCT LIA.CUSTOMER_ID)) FROM DEV_NB.T_NB_CONTRACT_MASTER MS
  LEFT JOIN DEV_NB.T_NB_INSURED_LIST LIA
    ON LIA.APPLY_CODE = MS.APPLY_CODE
 WHERE 1 = 1
   AND LIA.RELATION_TO_PH != '00'
   AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[ ) AS LI_CUSTOMER_COUNT, /*被保人客户量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS  LI_CUSTOMER_CHECK_COUNT, /*被保人核验总量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND NCVR.STATUS = '5'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS LI_CUSTOMER_FAIL_COUNT, /*被保人调优信平台失败量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND NCVR.STATUS = '1'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS LI_CUSTOMER_PASS_COUNT, /*被保人核验通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND NCVR.STATUS = '0'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS LI_CUSTOMER_NOPASS_COUNT, /*被保人核验不通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE in ('02', '04')
   AND NCVR.STATUS = '4'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS LI_CUSTOMER_NOTFOUND_COUNT, /*被保人核验未查得量*/

/*被保人结束-------------被保人结束-------------被保人结束-------------被保人结束*/

 (SELECT TO_CHAR(COUNT(DISTINCT BEA.CUSTOMER_ID)) FROM DEV_NB.T_NB_CONTRACT_MASTER MS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_BENE BEA
    ON BEA.APPLY_CODE = MS.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PHO 
	ON BEA.APPLY_CODE = PHO.APPLY_CODE 
 WHERE 1 = 1
   AND BEA.CUSTOMER_ID <> PHO.CUSTOMER_ID
   AND BEA.BENE_TYPE = '1'
   AND SUBSTR(MS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND MS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND MS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck2" />
   <![CDATA[ ) AS BE_CUSTOMER_COUNT, /*受益人客户量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS BE_CUSTOMER_CHECK_COUNT, /*受益人核验总量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND NCVR.STATUS = '5'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS BE_CUSTOMER_FAIL_COUNT, /*受益人调优信平台失败量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND NCVR.STATUS = '1'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS BE_CUSTOMER_PASS_COUNT, /*受益人核验通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND NCVR.STATUS = '0'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[ ) AS BE_CUSTOMER_NOPASS_COUNT, /*受益人核验不通过量*/

 (SELECT TO_CHAR(COUNT(CCV.APPLY_CODE)) FROM DEV_NB.T_NB_CONTRACT_MASTER CMS
  LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY CCV
    ON CMS.APPLY_CODE = CCV.APPLY_CODE
  LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCVR
    ON CCV.CHECK_ID = NCVR.CHECK_ID
 WHERE 1 = 1
   AND NCVR.IDENTITY_CHECK = '7'
   AND CCV.IDENTIFY_ROLE = '03'
   AND NCVR.STATUS = '4'
   AND SUBSTR(CMS.ORGAN_CODE,0,${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BE_CUSTOMER_NOTFOUND_COUNT,	/*受益人核验未查得量*/
   
/*---120854-核心系统手机号核验回补需求-新契约---start*/
 (SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECK_SUMREPLEN,	/*全量客户回补总量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.RETURN_CODE = 'DY_99' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECKFAIL_SUMREPLEN,		/*全量客户回补失败量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.RETURN_CODE = 'HY_00' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECKADOPT_SUMREPLEN,		/*全量客户回补通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.RETURN_CODE = 'HY_01' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECKNO_SUMREPLEN,		/*全量客户回补不通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.RETURN_CODE = 'HY_02' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS CUS_CHECKNOTFOUND_SUMREPLEN,		/*全量客户回补未查得量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_CHECK_SUMREPLEN,		/*投保人回补总量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.RETURN_CODE = 'DY_99' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_FAIL_SUMREPLEN,		/*投保人回补失败量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.RETURN_CODE = 'HY_00' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_ADOPT_SUMREPLEN,		/*投保人回补通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.RETURN_CODE = 'HY_01' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_NOT_SUMREPLEN,		/*投保人回补不通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '01'
   AND CHT.RETURN_CODE = 'HY_02' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS HOL_CHECKNOTFOUND_SUMREPLEN,		/*投保人回补未查得量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_CHECK_SUMREPLEN,		/*被保险人回补总量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.RETURN_CODE = 'DY_99' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_FAIL_SUMREPLEN,		/*被保险人回补失败量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.RETURN_CODE = 'HY_00' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_ADOPT_SUMREPLEN,		/*被保险人回补通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.RETURN_CODE = 'HY_01' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_NOT_SUMREPLEN,		/*被保险人回补不通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE in ('02', '04')
   AND CHT.RETURN_CODE = 'HY_02' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS INS_CHECKNOTFOUND_SUMREPLEN,		/*被保险人回补未查得量*/
   
(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_CHECK_SUMREPLEN,		/*受益人回补总量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.RETURN_CODE = 'DY_99' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_FAIL_SUMREPLEN,		/*受益人回补失败量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.RETURN_CODE = 'HY_00' 
   AND CHT.DERIV_TYPE = 'CB' 
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_ADOPT_SUMREPLEN,		/*受益人回补通过量*/

(SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.RETURN_CODE = 'HY_01' 
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth} )= BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_NOT_SUMREPLEN,		/*受益人回补不通过量*/

 (SELECT TO_CHAR(COUNT(CHT.POLICY_CODE))
  FROM DEV_PAS.T_CHECK_PHONE_TASK CHT
  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER CMS
    ON CMS.POLICY_CODE = CHT.POLICY_CODE
 WHERE 1 = 1
   AND CHT.CUSTOMER_TYPE = '03'
   AND CHT.RETURN_CODE = 'HY_02'
   AND CHT.DERIV_TYPE = 'CB'
   AND SUBSTR(CMS.ORGAN_CODE, 0, ${organ_code_lenth}) = BB.ORGAN_CODE
   AND CMS.CHANNEL_TYPE = BB.CHANNEL_TYPE
   AND CMS.SUBMIT_CHANNEL = BB.SUBMIT_CHANNEL]]>
   <include refid="NB_DateCheck1" />
   <![CDATA[) AS BENE_CHECKNOTFOUND_SUMREPLEN		/*受益人回补未查得量*/
/*---120854-核心系统手机号核验回补需求-新契约---end*/
   
   
 FROM
 (SELECT       
 			   SUBSTR(A.ORGAN_CODE,0,${organ_code_lenth}) as ORGAN_CODE,
               A.CHANNEL_TYPE,
               A.SUBMIT_CHANNEL,
               TO_DATE(#{start_date}, 'yyyy-MM-dd hh24:mi:ss') as START_DATE, /*开始日期*/
               TO_DATE(#{end_date}, 'yyyy-MM-dd hh24:mi:ss') as END_DATE, /*结束日期*/
               '1' AS FIND_TYPE
          FROM DEV_NB.T_NB_CONTRACT_MASTER A
          LEFT JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY NCC
            ON A.APPLY_CODE = NCC.APPLY_CODE
          LEFT JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT NCCR
            ON NCC.CHECK_ID = NCCR.CHECK_ID
         WHERE 1 = 1
         AND NCCR.IDENTITY_CHECK = '7']]>
    <include refid="NB_DateCheck" />      
    <include refid="NB_nbCheckPhone" />
         <![CDATA[GROUP BY SUBSTR(A.ORGAN_CODE,0,${organ_code_lenth}), A.CHANNEL_TYPE, A.SUBMIT_CHANNEL) BB ) CC ]]> 
    <include refid="NB_nbCheckPhoneGroupCondition" />
	</select>
</mapper>