<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.ContractMasterDaoImpl">
<!--
	<sql id="contractMasterWhereCondition">
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" policy_pwd != null and policy_pwd != ''  "><![CDATA[ AND A.POLICY_PWD = #{policy_pwd} ]]></if>
		<if test=" service_handler_code != null and service_handler_code != ''  "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_agent_name != null and sale_agent_name != ''  "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" insured_family  != null "><![CDATA[ AND A.INSURED_FAMILY = #{insured_family} ]]></if>
		<if test=" e_service_flag  != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} ]]></if>
		<if test=" service_handler_name != null and service_handler_name != ''  "><![CDATA[ AND A.SERVICE_HANDLER_NAME = #{service_handler_name} ]]></if>
		<if test=" service_handler != null and service_handler != ''  "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" dc_indi  != null "><![CDATA[ AND A.DC_INDI = #{dc_indi} ]]></if>
		<if test=" derivation != null and derivation != ''  "><![CDATA[ AND A.DERIVATION = #{derivation} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" basic_remark != null and basic_remark != ''  "><![CDATA[ AND A.BASIC_REMARK = #{basic_remark} ]]></if>
		<if test=" agent_org_id != null and agent_org_id != ''  "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pwd_invalid_flag  != null "><![CDATA[ AND A.PWD_INVALID_FLAG = #{pwd_invalid_flag} ]]></if>
		<if test=" validdate_date  != null  and  validdate_date  != ''  "><![CDATA[ AND A.VALIDDATE_DATE = #{validdate_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''  "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<if test=" sale_agent_code != null and sale_agent_code != ''  "><![CDATA[ AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if>
		<if test=" uw_list_id  != null "><![CDATA[ AND A.UW_LIST_ID = #{uw_list_id} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" lang_code != null and lang_code != ''  "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENCY_CODE = #{agency_code} ]]></if>
	</sql>
-->
<sql id="PA_contractMasterWhereCondition">
		<if test=" policy_pwd != null and policy_pwd != ''  "><![CDATA[ AND A.POLICY_PWD = #{policy_pwd} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_agent_name != null and sale_agent_name != ''  "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} ]]></if>
		<if test=" insured_family  != null "><![CDATA[ AND A.INSURED_FAMILY = #{insured_family} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" derivation != null and derivation != ''  "><![CDATA[ AND A.DERIVATION = #{derivation} ]]></if>
		<if test=" subinput_type != null and subinput_type != ''  "><![CDATA[ AND A.SUBINPUT_TYPE = #{subinput_type} ]]></if>
		<if test=" basic_remark != null and basic_remark != ''  "><![CDATA[ AND A.BASIC_REMARK = #{basic_remark} ]]></if>
		<if test=" sale_com_code != null and sale_com_code != ''  "><![CDATA[ AND A.SALE_COM_CODE = #{sale_com_code} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pwd_invalid_flag  != null "><![CDATA[ AND A.PWD_INVALID_FLAG = #{pwd_invalid_flag} ]]></if>
		<if test=" submit_channel  != null "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" sale_agent_code != null and sale_agent_code != ''  "><![CDATA[ AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENCY_CODE = #{agency_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" service_handler_code != null and service_handler_code != ''  "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" service_handler != null and service_handler != ''  "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if>
		<if test=" e_service_flag  != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" dc_indi  != null "><![CDATA[ AND A.DC_INDI = #{dc_indi} ]]></if>
		<if test=" sale_type != null and sale_type != ''  "><![CDATA[ AND A.SALE_TYPE = #{sale_type} ]]></if>
		<if test=" input_type != null and input_type != ''  "><![CDATA[ AND A.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" statistic_channel != null and statistic_channel != ''  "><![CDATA[ AND A.STATISTIC_CHANNEL = #{statistic_channel} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" agent_org_id != null and agent_org_id != ''  "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if>
		<if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" lang_code != null and lang_code != ''  "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
		<if test=" relation_policy_code != null and relation_policy_code != ''  "><![CDATA[ AND A.RELATION_POLICY_CODE = #{relation_policy_code} ]]></if>
		<if test=" policy_flag != null and policy_flag != '' "><![CDATA[ AND A.POLICY_FLAG = #{policy_flag} ]]></if>
		<if test=" double_mainrisk_flag  != null "><![CDATA[ AND A.DOUBLE_MAINRISK_FLAG = #{double_mainrisk_flag} ]]></if>
	</sql>
<!-- 按索引生成的查询条件 -->	
	<sql id="queryContractMasterByUwListIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>
     <sql id="queryContractMasterByApplyCodeCondition">
		<if test="apply_code != null"><![CDATA[ and A.APPLY_CODE = #{apply_code} and rownum=1 ]]></if>
	</sql>	
	<sql id="queryContractMasterByPolicyCodeCondition">
		<if test="policy_code != null"><![CDATA[ and A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="queryContractMasterByUwIdOnlyOneCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id}  and rownum=1  ]]></if>
	</sql>
	
	<sql id="queryContractMasterByPolicyIdCondition">
	  <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<!-- add by xuhp 保全下发核保结论 根据uwid,applycode查询保单信息 2015年8月21日 start-->
	<sql id="queryUwPolicyByPolicyCodeCondition">
		<if test="policy_code != null"><![CDATA[ and A.Policy_Code = B.Policy_Code and A.Policy_Code = C.Policy_Code AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<!-- add by xuhp 保全下发核保结论 根据uwid,applycode查询保单信息 2015年8月21日 end-->
	
	<!-- add by xuhp 保全下发核保结论 下发条件承保核保结论时需要支持保全和契约 2015年9月16日 start-->
	<sql id="queryUwPolicyByMasterPolicyCodeCondition">
		<if test="policy_code != null"><![CDATA[ and A.Policy_Code = B.Policy_Code and A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<!-- 按索引查询操作 -->	
	<select id="findContractMasterByUwListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUSPEND_CAUSE,A.SUSPEND_DATE,A.INTEREST_MODE,A.MONEY_CODE, A.UW_ID, A.APL_PERMIT, A.POLICY_PWD, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
      A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.SUBMISSION_DATE, 
      A.INSURED_FAMILY, A.E_SERVICE_FLAG, A.SERVICE_HANDLER_NAME, A.SERVICE_HANDLER, A.POLICY_ID, A.SERVICE_BANK_BRANCH, 
      A.DC_INDI, A.DERIVATION, A.END_CAUSE, A.ISSUE_DATE,
      A.LAPSE_CAUSE, A.BASIC_REMARK, A.AGENT_ORG_ID, A.DECISION_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, 
      A.VALIDATE_DATE, A.LIABILITY_STATE, A.SUBMIT_CHANNEL, A.POLICY_CODE, A.SERVICE_BANK, A.UW_LIST_ID, 
      A.BRANCH_CODE, A.LANG_CODE, A.FORMER_ID, A.AGENCY_CODE, B.AGENT_CODE AS SALE_AGENT_CODE 
       FROM DEV_UW.T_CONTRACT_MASTER A, DEV_UW.T_CONTRACT_AGENT B WHERE 1 = 1 AND ROWNUM = 1 AND A.UW_ID = B.UW_ID and a.policy_id = b.policy_id ]]>
		<include refid="queryContractMasterByUwListIdCondition" />
		<include refid="queryContractMasterByPolicyCodeCondition" />
		<include refid="queryUwPolicyByMasterPolicyCodeCondition" /> <!-- add by xuhp 保全下发核保结论 下发条件承保核保结论时需要支持保全和契约 2015年9月16日 -->
		<include refid="queryContractMasterByPolicyIdCondition" /><!-- add by xuhp 保全下发核保通知书时使用policyid进行查询 -->
		<include refid="queryContractMasterByApplyCodeCondition" /> 
		<![CDATA[ ORDER BY A.UW_LIST_ID ]]>
	</select>
	
		<select id="PA_findContractMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
				<![CDATA[SELECT A.POLICY_RELATION_TYPE,
			(SELECT TPRT.POLICY_RELATION_TYPE FROM DEV_PAS.T_POLICY_RELATION_TYPE TPRT WHERE TPRT.POLICY_RELATION_TYPE = A.POLICY_RELATION_TYPE)POLICY_RELATION_NAME,
			A.POLICY_FLAG, A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, A.IS_MUTUAL_INSURED,
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.RELATION_POLICY_CODE,
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, A.MULTI_MAINRISK_FLAG,
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.MEET_POV_STANDARD_FLAG, A.TRUST_BUSI_FLAG ,
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE,A.is_self_insured, A.insert_time,]]>
			<if test=" agent_code != null and agent_code != '' "><![CDATA[
				(select ag.agent_name from APP___PAS__DBUSER.T_agent ag where ag.agent_code = #{agent_code} and rownum = 1) as agent_name,]]></if>
			<![CDATA[
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID,A.DOUBLE_MAINRISK_FLAG,
			A.BANKNRT_FALG ,A.SPECIAL_ACCOUNT_FLAG,A.IS_CHANNEL_SELF_INSURED,A.IS_CHANNEL_MUTUAL_INSURED ,
			A.NOTIFICATION_RECEIVE_METHOD,A.TAX_EXTENSION_SUM_PREM,A.JOINTLY_INSURED_TYPE 
			,A.POLICY_MANAGE_ORGAN,A.GROUP_SALE_TYPE,A.LAPSE_LOAN_SUSPEND_DATE,A.POLICY_REINSURE_FLAG,A.REINSURED_TIMES ,A.SELF_APPLY_FLAG,A.PER_FIN_PVT_BANK_CODE,A.POLICY_LOCK_FLAG
			FROM APP___PAS__DBUSER.T_CONTRACT_MASTER A WHERE 1 = 1    ]]>
		<include refid="queryContractMasterByPolicyCodeCondition" />
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ 
			AND exists (select 1 from APP___PAS__DBUSER.T_CONTRACT_AGENT m where m.POLICY_CODE = #{policy_code} and m.AGENT_CODE = #{agent_code})
		 ]]></if>
		 <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		 <if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND trim(A.SERVICE_BANK) = #{service_bank} ]]></if>
		 <if test=" kd_product != null and kd_product != '' and kd_product == '0'.toString() ">		
		    <![CDATA[AND NOT EXISTS
					 (SELECT 1
						FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
					   WHERE CBP.POLICY_CODE = A.POLICY_CODE
						 AND CBP.BUSI_PROD_CODE IN 
							 (SELECT CI.CONSTANTS_VALUE
								FROM DEV_PAS.T_CONSTANTS_INFO CI
							   WHERE CI.CONSTANTS_KEY = 'KD_PRODUCT'))]]>
		 </if>
	</select>
	
	<select id="NB_findNBContractMasterByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, A.WINNING_START_FLAG,
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE,
			A.TRANSACTION_NO,A.INPUT_TYPE,A.GROUP_SALE_TYPE,A.THIRD_ORG_FLAG,A.NOTIFICATION_RECEIVE_METHOD,A.APPOINT_APPLY_FLAG,A.JOINTLY_INSURED_TYPE
			FROM APP___NB__DBUSER.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  
			and A.POLICY_CODE = #{policy_code} ORDER BY A.POLICY_CODE 
		]]>
	</select>
	
	
	<select id="querySaleAgentInfoByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.Apply_Code,A.channel_type,A.Organ_Code,
			(SELECT AGENT_CODE FROM DEV_UW.T_CONTRACT_AGENT 
				WHERE APPLY_CODE=A.APPLY_CODE AND UW_ID=A.UW_ID AND ROWNUM=1) AS agent_code 
			FROM DEV_UW.T_CONTRACT_MASTER A 
			WHERE A.APPLY_CODE=#{apply_code} 
			AND ROWNUM=1
		]]>
	</select>
   <!-- 根据保单号查询险种结论信息 -->
   <select id="querySaleAgentInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.Apply_Code,A.channel_type,A.Organ_Code,
			(SELECT AGENT_CODE FROM DEV_UW.T_CONTRACT_AGENT 
				WHERE APPLY_CODE=A.APPLY_CODE AND UW_ID=A.UW_ID AND ROWNUM=1) AS agent_code 
			FROM DEV_UW.T_CONTRACT_MASTER A 
			WHERE A.POLICY_CODE=#{apply_code} 
			AND ROWNUM=1
		]]>
	</select>
	
	<!-- 核保复核特约前置特约保单信息查询 -->
	<select id="queryConditionContractBusiProds" resultType="java.util.Map"	parameterType="java.util.Map">
		<![CDATA[
		SELECT A.*,ROWNUM FROM (
			SELECT 
			UW_ID,POLICY_CODE,APPLY_CODE,VALIDATE_DATE,ORGAN_CODE,
			(LISTAGG(T.BUSI_PROD_CODE,',') WITHIN  GROUP(ORDER BY UW_ID,POLICY_CODE,APPLY_CODE,VALIDATE_DATE)) AS BUSI_PROD_CODE
			 FROM (
			      SELECT TCM.POLICY_CODE,/*保单号*/
			             TCM.APPLY_CODE,/*投保单号*/
			             B.BUSI_PROD_CODE,/*险种代码*/
			             TCM.ORGAN_CODE,/*管理机构*/
			             TCM.VALIDATE_DATE,/*保单生效日期*/
			             NVL((SELECT MAX(UB.UW_ID)
			                FROM DEV_UW.T_UW_PRODUCT UB
			               WHERE UB.APPLY_CODE = B.APPLY_CODE
			                 AND UB.BUSI_PROD_CODE = B.BUSI_PROD_CODE
			                 AND UB.DECISION_CODE = '33'
			                 AND UB.UW_PRD_ID < 1000000000000),
			             (SELECT MAX(UB.UW_ID)
			                FROM DEV_UW.T_UW_PRODUCT UB
			               WHERE UB.APPLY_CODE = B.APPLY_CODE
			                 AND UB.BUSI_PROD_CODE = B.BUSI_PROD_CODE
			                 AND UB.DECISION_CODE = '33'
			                 AND UB.UW_PRD_ID > 1000000000000)) AS UW_ID/*核保ID*/
			        FROM (
			           SELECT  APPLY_CODE,POLICY_CODE,BUSI_PROD_CODE,MATURITY_DATE,LIABILITY_STATE,REINSURED,COND_PRE_CHECK_FLAG
			           FROM DEV_PAS.T_CONTRACT_BUSI_PROD B1
			           WHERE 
			           B1.MATURITY_DATE>=to_date(#{startValidDate},'yyyy-MM-dd') AND B1.MATURITY_DATE<=to_date(#{endValidDate},'yyyy-MM-dd') AND B1.LIABILITY_STATE='1'
			           AND B1.BUSI_PROD_CODE IN (${busiProdScope})/*险种代码*/
			           AND B1.REINSURED IS NULL/*已重投/已转保标识*/
			           AND B1.COND_PRE_CHECK_FLAG IS NULL /*特约复核前置标识*/
			        ) B
			       INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
			          ON TCM.POLICY_CODE = B.POLICY_CODE
			       WHERE 1 = 1
			       	AND EXISTS
		            (
		            SELECT UB.UW_ID FROM DEV_UW.T_UW_PRODUCT UB WHERE UB.APPLY_CODE=TCM.APPLY_CODE
		            AND UB.BUSI_PROD_CODE=B.BUSI_PROD_CODE
		            AND UB.DECISION_CODE = '33'
		            )
			         AND (TCM.LIABILITY_STATE = '1' OR TCM.LIABILITY_STATE = '4') /*保单效力状态*/
			        AND (TCM.REINSURED_TIMES IS  NULL OR TCM.REINSURED_TIMES < 1)/*重投或转投次数*/
			        ) T WHERE   UW_ID IS NOT NULL 
			        AND NOT EXISTS (SELECT P.POLICY_CODE
			          FROM DEV_PAS.T_LOCK_POLICY P
			          INNER JOIN DEV_PAS.T_LOCK_SERVICE_DEF SD ON P.LOCK_SERVICE_ID=SD.LOCK_SERVICE_ID
			         WHERE 
			            P.POLICY_CODE =T.POLICY_CODE
			           AND SD.LOCK_SERVICE_NAME = 'RR_续保险种转换'
			        /*T_LOCK_SERVICE_DEF表中RR_续保险种转换对应的LOCK_SERVICE_ID*/
			        )
			      GROUP  BY UW_ID,POLICY_CODE,APPLY_CODE,VALIDATE_DATE,ORGAN_CODE
			   ) A WHERE  MOD(A.UW_ID, #{modnum})= #{start}
		 ]]>
	</select>	
	<!--  -->
	<select id="queryUwConditonFTPParaValues" resultType="java.util.Map"	parameterType="java.util.Map">
		<![CDATA[
		SELECT 
		UD.PARA_VALUE,
		UD.PARA_NAME
		  FROM DEV_UW.T_UDMP_PARA_DEF UD
		 WHERE UD.PARA_NAME LIKE '%UWContractBusiProdBatch%'

		]]>
	</select>
	<!-- 查询单条 -->
	<select id="PA_findContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, A.VALIDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE,A.DOUBLE_MAINRISK_FLAG, 
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.BANK_AGENCY_FLAG,A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID, A.RELATION_POLICY_CODE,
			A.IS_SELF_INSURED, A.MULTI_MAINRISK_FLAG FROM dev_pas.T_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_contractMasterWhereCondition" />
		<if test=" query_start_date!= null and query_start_date!='' "><![CDATA[AND A.APPLY_DATE >=#{query_start_date}]]></if>
		<if test=" query_end_date !=null and query_end_date !='' "><![CDATA[ AND A.APPLY_DATE <= #{query_end_date}]]></if>
		
	</select>  
	
	
	<!-- 查询续保审核保单信息 -->
	<select id="UW_queryContractMasterByRenewalExamine" resultType="java.util.Map" parameterType="java.util.Map">
		 <![CDATA[
		 	SELECT
		 		DOC.BUSS_CODE AS APPLY_CODE,DOC.POLICY_CODE,AGE.AGENT_CODE,
				(SELECT T.LIABILITY_STATE FROM DEV_PAS.T_CONTRACT_MASTER T WHERE T.POLICY_CODE = DOC.POLICY_CODE) AS LIABILITY_STATE,
				(SELECT T.APPLY_DATE FROM DEV_PAS.T_CONTRACT_MASTER T WHERE T.POLICY_CODE = DOC.POLICY_CODE) AS APPLY_DATE
			FROM DEV_UW.T_CONTRACT_AGENT AGE
				INNER JOIN DEV_NB.T_DOCUMENT DOC ON DOC.BUSS_ID = AGE.UW_ID AND DOC.POLICY_CODE = AGE.POLICY_CODE
				INNER JOIN DEV_UW.T_POLICY_HOLDER PH ON DOC.BUSS_ID = PH.UW_ID AND DOC.POLICY_CODE = PH.POLICY_CODE
				INNER JOIN DEV_PAS.T_CUSTOMER CUS ON CUS.CUSTOMER_ID = PH.CUSTOMER_ID
      		WHERE AGE.AGENT_CODE = #{agent_code}
				AND DOC.TEMPLATE_CODE = 'UWS_00021' AND DOC.STATUS IN ('2','4','5')
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND DOC.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND CUS.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND CUS.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<![CDATA[ ORDER BY DOC.SEND_TIME ]]>
	</select>
	
</mapper>
