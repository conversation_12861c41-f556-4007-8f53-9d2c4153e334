<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IContractProductDao">

	<sql id="contractProductWhereCondition">
		<if test=" is_pause  != null "><![CDATA[ AND A.IS_PAUSE = #{is_pause} ]]></if>
		<if test=" master_product_code != null and master_product_code != ''  "><![CDATA[ AND A.MASTER_PRODUCT_CODE = #{master_product_code} ]]></if>
		<if test=" prod_pkg_plan_code != null and prod_pkg_plan_code != ''  "><![CDATA[ AND A.PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" norenew_reason != null and norenew_reason != ''  "><![CDATA[ AND A.NORENEW_REASON = #{norenew_reason} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" extra_prem_af  != null "><![CDATA[ AND A.EXTRA_PREM_AF = #{extra_prem_af} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" cc_sa  != null "><![CDATA[ AND A.CC_SA = #{cc_sa} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" master_item_id  != null "><![CDATA[ AND A.MASTER_ITEM_ID = #{master_item_id} ]]></if>
		<if test=" master_product_id  != null "><![CDATA[ AND A.MASTER_PRODUCT_ID = #{master_product_id} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" count_way != null and count_way != ''  "><![CDATA[ AND A.COUNT_WAY = #{count_way} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" bonus_mode_code  != null "><![CDATA[ AND A.BONUS_MODE_CODE = #{bonus_mode_code} ]]></if>
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" bonus_sa  != null "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" health_service_flag  != null "><![CDATA[ AND A.HEALTH_SERVICE_FLAG = #{health_service_flag} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" unit  != null "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" pause_date  != null  and  pause_date  != ''  "><![CDATA[ AND A.PAUSE_DATE = #{pause_date} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" prem_freq != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>

		<if
			test=" initial_discnt_prem_af != null and  initial_discnt_prem_af  != '' "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if
			test=" renewal_discnted_prem_af != null  and  renewal_discnted_prem_af  != '' "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if
			test=" initial_extra_prem_af != null and  initial_extra_prem_af  != '' "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if
			test=" renewal_extra_prem_af != null and  renewal_extra_prem_af  != '' "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" is_gift != null and is_gift != '' "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" pay_freq != null and pay_freq != '' "><![CDATA[ AND A.PAY_FREQ = #{pay_freq} ]]></if>
	</sql>

	<!-- 按索引生成 的查询条件 -->    
	<sql id="queryContractProductByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>
	<sql id="queryContractProductByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="PA_queryContractProductByPolicyIdCodeCondition">
		<if test=" policy_id != null and policy_id != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="PA_queryContractProductByProductCodeCondition">
		<if test=" product_code != null and product_code != '' "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
	</sql>
	<sql id="PA_queryContractProductByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>

	<!-- 按索引查询操作 -->
	<!-- 按照险种查询信息 -->
	<select id="findContractProductByBusiItemId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_FREQ,A.IS_PAUSE,A.MASTER_PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.MASTER_ITEM_ID, A.MASTER_PRODUCT_ID, A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.COUNT_WAY, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, 
			A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, 
			A.CHARGE_PERIOD, A.INTEREST_MODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.PREM_FREQ,
			A.INITIAL_DISCNT_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,A.IS_GIFT , A.PAIDUP_DATE FROM T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductByBusiItemIdCondition" />
	</select>
	<!-- 按照险种查询信息 -->
	<select id="findContractProductByItemId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_FREQ,A.IS_PAUSE, A.MASTER_PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.MASTER_ITEM_ID, A.MASTER_PRODUCT_ID, A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.COUNT_WAY, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, 
			A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, 
			A.CHARGE_PERIOD, A.INTEREST_MODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.PREM_FREQ ,
			A.INITIAL_DISCNT_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,A.IS_GIFT , A.PAIDUP_DATE, A.APPEND_PREM_AF FROM T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="queryContractProductByItemIdCondition" />
	</select>

	<select id="findContractProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.IS_MASTER_ITEM, A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF,
			A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE,
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, A.CC_SA, A.INITIAL_EXTRA_PREM_AF,
			A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD,
			A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, A.IS_GIFT, A.RERINSTATE_DATE,
			A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL,
			A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG,
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE,
			A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR,
			A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE,
			A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, A.SUSPEND_DATE, A.SUSPEND_CAUSE,
			A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,
			A.BONUS_W_MODE, A.LAST_BONUS_DATE, A.ANNU_PAY_TYPE
			FROM DEV_PAS.T_CONTRACT_PRODUCT A WHERE 1 = 1
		]]>
		<include refid="queryContractProductByPolicyCodeCondition" />
	</select>

	<select id="findContractProductByProductCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_FREQ,A.IS_PAUSE,  A.MASTER_PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.MASTER_ITEM_ID, A.MASTER_PRODUCT_ID, A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.COUNT_WAY, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, 
			A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, 
			A.CHARGE_PERIOD, A.INTEREST_MODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.PREM_FREQ ,
			A.INITIAL_DISCNT_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,A.IS_GIFT , A.PAIDUP_DATE FROM T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductByProductCodeCondition" />
	</select>

	<select id="findContractProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_FREQ,A.IS_PAUSE, A.MASTER_PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.MASTER_ITEM_ID, A.MASTER_PRODUCT_ID, A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.COUNT_WAY, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, 
			A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, 
			A.CHARGE_PERIOD, A.INTEREST_MODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.PREM_FREQ,
			A.INITIAL_DISCNT_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,A.IS_GIFT, A.PAIDUP_DATE  FROM T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="contractProductWhereCondition" />
	</select>
	<!-- 按map查询操作 -->
	<select id="findAllMapContractProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_FREQ,A.IS_PAUSE, A.MASTER_PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.MASTER_ITEM_ID, A.MASTER_PRODUCT_ID, A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.COUNT_WAY, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, 
			A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, 
			A.CHARGE_PERIOD, A.INTEREST_MODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.PREM_FREQ,
			A.INITIAL_DISCNT_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,A.IS_GIFT, A.PAIDUP_DATE  FROM T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
      A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.INITIAL_EXTRA_PREM_AF, 
      A.AMOUNT, A.CASE_ID, A.EXPIRY_DATE, A.PAY_YEAR, A.PAY_PERIOD, 
      A.LIABILITY_STATE, A.COPY_DATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
      A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
      A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
      A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, 
      A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
      A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.PREM_FREQ, 
      A.INITIAL_DISCNT_PREM_AF, A.CUR_FLAG, A.PAIDUP_DATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END, A.INITIAL_AMOUNT,A.DEDUCTIBLE_FRANCHISE,A.PAYOUT_RATE,A.IS_MASTER_ITEM,A.BONUS_W_MODE FROM DEV_CLM.T_CONTRACT_PRODUCT                       A WHERE ROWNUM <=  1000  ]]>
		<include refid="contractProductWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
	<select id="CLM_findAllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.INITIAL_EXTRA_PREM_AF, 
			A.AMOUNT, A.CASE_ID, A.EXPIRY_DATE, A.PAY_YEAR, A.PAY_PERIOD, 
			A.LIABILITY_STATE, A.COPY_DATE, A.COUNT_WAY, A.POLICY_CODE, A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, 
			A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, A.BENEFIT_LEVEL, A.PRODUCT_ID, 
			A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, 
			A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, 
			A.END_CAUSE, A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
			A.LOG_ID, A.CHARGE_PERIOD, A.STD_PREM_AF, A.INTEREST_MODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.PREM_FREQ, 
			A.INITIAL_DISCNT_PREM_AF, A.CUR_FLAG, A.PAIDUP_DATE, A.IS_WAIVED, A.WAIVER_START, A.WAIVER_END, A.INITIAL_AMOUNT,A.DEDUCTIBLE_FRANCHISE,A.PAYOUT_RATE,A.IS_MASTER_ITEM FROM dev_clm.T_CONTRACT_PRODUCT                       A WHERE ROWNUM <=  1000  ]]>
		<include refid="contractProductWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
    <select id="PAS_findAllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE FROM DEV_PAS.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000 ]]>
		<include refid="contractProductWhereCondition" />
		<![CDATA[ORDER BY A.VALIDATE_DATE]]>
	</select>
    
    <select id="PAandNB_findAllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<if test="  type != null and type == '1'.toString() ">          
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.ANNU_PAY_TYPE, A.IS_PAUSE,A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF ,A.BONUS_W_MODE,A.LAST_BONUS_DATE FROM DEV_PAS.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000 ]]>
		<include refid="contractProductWhereCondition" />
		<![CDATA[ORDER BY A.VALIDATE_DATE]]>
		</if>
		
		<if test="  type != null and type == '0'.toString() ">
		<![CDATA[   SELECT TO_NUMBER(PL.OPTION_TYPE) AS IS_MASTER_ITEM,A.ANNU_PAY_TYPE,A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID,
			A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT,A.ADDITIONAL_PREM_AF,A.VALIDATE_DATE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID,A.PRODUCT_CODE,
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END,A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG,A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR,
			A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE,A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF FROM DEV_NB.T_NB_CONTRACT_PRODUCT A
			INNER JOIN DEV_PDS.T_PRODUCT_LIFE PL ON A.PRODUCT_ID=PL.PRODUCT_ID WHERE ROWNUM <=  1000 ]]>
		    <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<![CDATA[ORDER BY A.VALIDATE_DATE]]>
		</if>
	</select>
	
	<!-- 查询个数操作 -->
	<select id="findContractProductTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="queryContractProductForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.PAY_FREQ, B.IS_PAUSE, B.MASTER_PRODUCT_CODE, B.PROD_PKG_PLAN_CODE, B.APPLY_CODE, B.NORENEW_REASON, B.ORGAN_CODE, 
			B.CHARGE_YEAR, B.EXTRA_PREM_AF, B.POLICY_ID, B.CC_SA, B.AMOUNT, 
			B.MASTER_ITEM_ID, B.MASTER_PRODUCT_ID, B.PAY_YEAR, B.EXPIRY_DATE, B.PAY_PERIOD, B.LIABILITY_STATE, B.POLICY_CODE, 
			B.COUNT_WAY, B.RERINSTATE_DATE, B.RENEW_DECISION, B.VALIDATE_DATE, B.BONUS_MODE_CODE, 
			B.BENEFIT_LEVEL, B.PRODUCT_ID, B.BONUS_SA, B.PRODUCT_CODE, B.APPLY_DATE, B.COVERAGE_PERIOD, B.ITEM_ID, 
			B.MATURITY_DATE, B.BUSI_ITEM_ID, B.HEALTH_SERVICE_FLAG, B.LAPSE_DATE, B.UNIT, B.COVERAGE_YEAR, 
			B.END_CAUSE, B.LAPSE_CAUSE, B.TOTAL_PREM_AF, B.DECISION_CODE, B.PAUSE_DATE, B.STD_PREM_AF, 
			B.CHARGE_PERIOD, B.INTEREST_MODE, B.SUSPEND_DATE, B.SUSPEND_CAUSE, B.PREM_FREQ ,
			B.INITIAL_DISCNT_PREM_AF,B.RENEWAL_DISCNTED_PREM_AF, B.INITIAL_EXTRA_PREM_AF, B.RENEWAL_EXTRA_PREM_AF,B.IS_GIFT, B.PAIDUP_DATE FROM (
					SELECT ROWNUM RN, A.PAY_FREQ,A.IS_PAUSE, A.MASTER_PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.MASTER_ITEM_ID, A.MASTER_PRODUCT_ID, A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.COUNT_WAY, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.MATURITY_DATE, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, 
			A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, 
			A.CHARGE_PERIOD, A.INTEREST_MODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.PREM_FREQ,
			A.INITIAL_DISCNT_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,A.IS_GIFT, A.PAIDUP_DATE FROM T_CONTRACT_PRODUCT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

	<select id="PA_findContractProductByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PAY_FREQ,A.IS_PAUSE, A.MASTER_PRODUCT_CODE, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.MASTER_ITEM_ID, A.MASTER_PRODUCT_ID, A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.COUNT_WAY, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.UNIT, A.COVERAGE_YEAR, 
			A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.INTEREST_MODE, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF, A.INITIAL_EXTRA_PREM_AF, A.RENEWAL_EXTRA_PREM_AF,A.IS_GIFT, A.PAIDUP_DATE
			FROM T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductByPolicyIdCodeCondition" />
	</select>



	<!-- 尊享人生险种可选责任信息 -->
	<select id="querySelectMessage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			
 select b.busi_prod_code,
       (select product_name_sys
          from T_BUSINESS_PRODUCT
         where business_prd_id =b.busi_prd_id) as product_name,
(select PRODUCT_NAME from T_PRODUCT_LIFE where product_id=a.product_id)  as duty_type,(select PAY_DUE_DATE from T_PAY_PLAN where policy_id=a.policy_id) as contract_get_date,
      a.amount as amount,
       a.TOTAL_PREM_AF as total_prem_af,
    (select BONUS_SA from T_BONUS_ALLOCATE where bonus_allot='1' and policy_id=a.policy_id) as bonus_amnt,
       a.bonus_sa as sum_bonus_amnt,
      ( select BONUS_SA from T_BONUS_ALLOCATE where bonus_allot='3' and policy_id=a.policy_id) as terminal_bonus_amnt
  from T_CONTRACT_PRODUCT a,t_contract_busi_prod b
 where b.busi_item_id = a.busi_item_id and a.product_id in
       (select product_id from T_PRODUCT_LIFE where OPTION_TYPE = '1') 
       and a.policy_code=#{policy_code} and b.busi_prod_code=#{busi_prod_code}
		]]>
	</select>
	<!-- 第k个保单年度末累计可选责任的基本保额 -->
	<select id="calcAddItemSumAmount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT NVL(SUM(T.AMOUNT), 0) SUM_AMOUNT
  					FROM T_CONTRACT_PRODUCT T
				  WHERE T.MASTER_ITEM_ID = #{master_item_id}  ]]>
	</select>
	<!--已承保保单查询-->
	<select id="querySumContractProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(AMOUNT) AMOUNT, SUM(UNIT) UNIT
           FROM DEV_PAS.T_CONTRACT_PRODUCT
           
          WHERE 1=1  
          ]]>
          
          <if test="policy_code!=null and policy_code!=''">
             AND POLICY_CODE = #{policy_code}
          </if>
	</select>
	
	
	<!--保费项信息接口  -->
	<select id="qry_queryPremiumsInfo" resultType="java.util.Map" parameterType="java.util.Map">
	
		<![CDATA[
			select a.old_pol_no,
		       a.busi_item_id ,
		       b.prem_freq ,
		       b.product_code,
		       b.std_prem_af,
		       a.busi_prod_code,
		       (select a.validate_date
		          from dev_pas.t_contract_master cm
		         where cm.policy_code = a.policy_code) paystartdate,
		       (case b.prem_freq
		         when 1 then
		          a.expiry_date
		         else
		          b.paidup_date
		       end) payenddate,
		       b.insert_time makedate,
		       b.update_time modifydate,
		       b.product_code,
		       b.prem_freq payintv,
		       ( select c.em_value  from dev_uw.T_UW_EXTRA_PREM  c where c.item_id=b.item_id and rownum =1) riskscore
		  from dev_pas.t_contract_busi_prod a, dev_pas.t_contract_product b
		 where a.busi_item_id = b.busi_item_id
		   and nvl(a.old_pol_no,a.busi_item_id)=#{pol_no}
		   and a.policy_code = #{policy_code}
		
		]]>
	</select>
	
	<!--保费项信息接口:按投保单号查  -->
	<select id="qry_queryPremiumsInfoNB" resultType="java.util.Map" parameterType="java.util.Map">
	
		<![CDATA[
			select a.old_pol_no ,
			   b.prem_freq ,
		       a.busi_item_id,
		       b.product_code,
		       b.std_prem_af,
		       (select a.validate_date
		          from dev_nb.t_nb_contract_master cm
		         where cm.apply_code = a.apply_code) paystartdate,
		       (case b.prem_freq
		         when 1 then
		          a.expiry_date
		         else
		          b.paidup_date
		       end) payenddate,
		       b.insert_time makedate,
		       b.update_time modifydate,
		       b.prem_freq payintv,
		       (select c.em_value
		          from dev_uw.T_UW_EXTRA_PREM c
		         where c.item_id = b.item_id
		           and rownum = 1) riskscore
		  from dev_nb.t_nb_contract_busi_prod a, dev_nb.t_nb_contract_product b
		 where a.busi_item_id = b.busi_item_id
		   and nvl(a.old_pol_no,a.busi_item_id)=#{pol_no}
		   and a.apply_code = #{apply_code} 
		]]>
	</select>
	<!-- 查询出险原因 -->
	<select id="findAllContractProductPO" resultType="java.util.Map" parameterType="java.util.Map">
	select r.relativity1,r.relativity2,r.relativity3,r.relativity4,r.relativity5,r.relativity6,r.relativity7,r.relativity8,r.relativity9,r.relativity10,
    r.relativity11,r.relativity12,p.relv1_value,p.relv2_value,p.relv3_value,p.relv4_value,p.relv5_value,p.relv6_value,p.relv7_value,
    p.relv8_value,p.relv9_value,p.relv10_value,p.relv11_value,p.relv12_value   from dev_pds.t_liab_pay_relative r ,dev_pds.t_liab_pay_param p where r.product_id = p.product_id and r.liab_id = p.liab_id 
    and r.liba_code = p.liba_code and r.product_id = #{product_id}
	</select>
	
	<!-- 按索引查询操作 -->	
   <!-- 按照险种查询信息 -->
    <select id="QRY_INTEGRAL_findContractProductByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.IS_PAUSE, A.IS_SRP, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF,A.BONUS_W_MODE,A.LAST_BONUS_DATE,A.ANNU_PAY_TYPE FROM DEV_PAS.T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractProductByBusiItemIdCondition" />
	</select>
	
	<select id="findContractProductByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT ITEM_ID,PRODUCT_ID,PRODUCT_CODE,BONUS_MODE_CODE,
			APPLY_CODE,BUSI_ITEM_ID,POLICY_CODE,PROD_PKG_PLAN_CODE,
			POLICY_ID,AMOUNT,UNIT,COUNT_WAY,BENEFIT_LEVEL,VALIDATE_DATE,
			EXPIRY_DATE,LIABILITY_STATE,CHARGE_PERIOD,CHARGE_YEAR,PREM_FREQ,
			COVERAGE_PERIOD,COVERAGE_YEAR,PAY_PERIOD,PAY_YEAR,PAY_FREQ,
			DECISION_CODE,UW_COMPLETE_DESC,STD_PREM_AF,INITIAL_DISCNT_PREM_AF,
			RENEWAL_DISCNTED_PREM_AF,EXTRA_PREM_AF,INITIAL_EXTRA_PREM_AF
			,RENEWAL_EXTRA_PREM_AF,TOTAL_PREM_AF,IS_GIFT,HEALTH_SERVICE_FLAG,
			INTEREST_MODE,PAIDUP_DATE,ANNU_PAY_TYPE,MASTER_ITEM_ID,WAIVER_START,
			MASTER_PRODUCT_CODE,MASTER_PRODUCT_ID,DEDUCTIBLE_FRANCHISE,
			PAYOUT_RATE,ADDITIONAL_PREM_AF,APPEND_PREM_AF,IS_WAIVED,WAIVER_END,
			INSERT_BY,INSERT_TIME,INSERT_TIMESTAMP,UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP
			FROM DEV_NB.T_NB_CONTRACT_PRODUCT A WHERE A.APPLY_CODE = #{apply_code} ]]>
	</select>
	
	<select id="findLiabPayRelativeByProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT LIST_ID,PRODUCT_ID,LIAB_ID,LIBA_CODE,RELATIVITY1,
			RELATIVITY2,RELATIVITY3,RELATIVITY4,RELATIVITY5,RELATIVITY6,
			RELATIVITY7,RELATIVITY8,RELATIVITY9,RELATIVITY10,RELATIVITY11,
			RELATIVITY12,LIAB_EXTEND_TYPE,LIAB_EXTEND_LENGTH,UPDATE_BY,UPDATE_TIME,
			UPDATE_TIMESTAMP,INSERT_BY,INSERT_TIME,INSERT_TIMESTAMP
			FROM DEV_PDS.T_LIAB_PAY_RELATIVE C WHERE C.PRODUCT_ID = #{product_id} ]]>
	</select>
		
		<select id="findCSContractProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT LOG_ID,OLD_NEW,OPERATION_TYPE,CHANGE_ID,POLICY_CHG_ID,ITEM_ID,PRODUCT_ID,
			PRODUCT_CODE,IS_MASTER_ITEM,APPLY_CODE,BUSI_ITEM_ID,POLICY_CODE,PROD_PKG_PLAN_CODE,
			POLICY_ID,AMOUNT,UNIT,COUNT_WAY,BENEFIT_LEVEL,VALIDATE_DATE,EXPIRY_DATE,MATURITY_DATE,
			LIABILITY_STATE,CHARGE_PERIOD,CHARGE_YEAR,PREM_FREQ,COVERAGE_PERIOD,COVERAGE_YEAR,
			PAY_PERIOD,PAY_YEAR,PAY_FREQ,DECISION_CODE,STD_PREM_AF,ADDITIONAL_PREM_AF,APPEND_PREM_AF,
			EXTRA_PREM_AF,TOTAL_PREM_AF,HEALTH_SERVICE_FLAG,NORENEW_REASON,ORGAN_CODE,INTEREST_MODE,
			APPLY_DATE,LAPSE_DATE,END_CAUSE,LAPSE_CAUSE,RERINSTATE_DATE,BONUS_SA,CC_SA,RENEW_DECISION,
			IS_PAUSE,PAUSE_DATE,IS_GIFT,INITIAL_DISCNT_PREM_AF,RENEWAL_DISCNTED_PREM_AF,
			INITIAL_EXTRA_PREM_AF,RENEWAL_EXTRA_PREM_AF,DEDUCTIBLE_FRANCHISE,PAYOUT_RATE,IS_WAIVED,WAIVER_START,
			WAIVER_END,SUSPEND_CAUSE,SUSPEND_DATE,PAIDUP_DATE,INITIAL_AMOUNT,BONUS_MODE,
			BONUS_W_MODE,LAST_BONUS_DATE,IS_SRP,ANNU_PAY_TYPE,INSERT_BY,INSERT_TIME,INSERT_TIMESTAMP,
			UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP
			FROM DEV_PAS.T_CS_CONTRACT_PRODUCT A WHERE 1 = 1 ]]>
		<include refid="queryContractProductByPolicyCodeCondition" />
	</select>
	
		<select id="findCSContractProductByPolicyCodeandOldNew" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	 SELECT DISTINCT T.PRODUCT_ID,T.PRODUCT_CODE,T.UNIT,B.POLICY_CODE
      FROM DEV_PAS.T_CS_POLICY_CHANGE A
      LEFT JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL B
        ON A.CHANGE_ID = B.CHANGE_ID
       AND B.POLICY_CHG_ID = A.POLICY_CHG_ID
      LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE C
        ON C.ACCEPT_ID = A.ACCEPT_ID
       AND C.CHANGE_ID = B.CHANGE_ID
      LEFT JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT T
       ON T.BUSI_ITEM_ID=B.BUSI_ITEM_ID
     WHERE C.SERVICE_CODE IN('NS','RE','PA')
       AND C.ACCEPT_STATUS = '08'
      AND A.POLICY_CODE =#{policy_code}

       AND B.OLD_NEW = '1'
	]]>
	
	</select>
	<!-- 查询保单是否有有效的单子 -->
	<select id="findContractProductbyCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		
	 select tcm.liability_state libstate from dev_pas.t_contract_master tcm ,dev_pas.t_policy_holder tph,dev_pas.t_customer tcu
	 where tcm.policy_code = tph.policy_code
	 and tcu.customer_id = tph.customer_id
	 and tcu.customer_name = #{customer_name}
	 and tcu.customer_birthday = TO_DATE(#{customer_birthday},'yyyy-MM-dd')
	 and tcu.customer_cert_type = #{customer_cert_type}
	 and tcu.customer_certi_code = #{customer_certi_code}
	 and tcu.customer_gender = #{customer_gender}
	 union all
	 select tcm.liability_state libstate from dev_pas.t_contract_master tcm ,dev_pas.t_insured_list til,dev_pas.t_customer tcu
	 where tcm.policy_code = til.policy_code
	 and tcu.customer_id = til.customer_id
	 and tcu.customer_name = #{customer_name}
	 and tcu.customer_birthday = TO_DATE(#{customer_birthday},'yyyy-MM-dd') 
	 and tcu.customer_cert_type = #{customer_cert_type}
	 and tcu.customer_certi_code = #{customer_certi_code} 
	 and tcu.customer_gender = #{customer_gender}
		]]>
	</select>
	
	
	<!-- 查询保单是否有有效的单子 -->
	<select id="findNBPolicybyCustomerInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	select ncm.proposal_status, ncm.policy_code, ncm.apply_code
	  from dev_nb.t_nb_contract_master ncm
	 where exists
	 (select 1
	          from dev_nb.t_nb_policy_holder h
	         where exists (select 1
	                  from dev_nb.t_customer c
	                 where c.customer_name = #{customer_name}
	                   and c.customer_birthday =
	                       TO_DATE(#{customer_birthday}, 'yyyy-MM-dd')
	                   and c.customer_gender = #{customer_gender}
	                   and c.customer_cert_type = #{customer_cert_type}
	                   and c.customer_certi_code = #{customer_certi_code}
	                   and h.customer_id = c.customer_id)
	           and h.apply_code = ncm.apply_code)
	
	union all
	
	select ncm.proposal_status, ncm.policy_code, ncm.apply_code
	  from dev_nb.t_nb_contract_master ncm
	 where exists
	 (select 1
	          from dev_nb.t_nb_insured_list h
	         where exists (select 1
	                  from dev_nb.t_customer c
	                 where c.customer_name = #{customer_name}
	                   and c.customer_birthday =
	                       TO_DATE(#{customer_birthday}, 'yyyy-MM-dd')
	                   and c.customer_gender = #{customer_gender}
	                   and c.customer_cert_type = #{customer_cert_type}
	                   and c.customer_certi_code = #{customer_certi_code}
	                   and h.customer_id = c.customer_id)
	           and h.apply_code = ncm.apply_code)
		 
		]]>
	</select>
	<!-- 需求变更#48814 关于调整841健康无忧（宜家版）老客户规则及最低起售金额的需求-->
	<select id="PDS_findProductRuleConfig" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[           
        SELECT b.*
          FROM dev_pds.T_PRD_RULE_CONFIG_INFO B
         WHERE 1 = 1
           AND B.RULE_TYPE = #{rule_type}
           AND (B.PRODUCT_CODE = #{product_code} OR B.PRODUCT_CODE = NULL)
           AND B.RULE_EFFECTIVE_DATE <= #{rule_effective_date}
           AND B.RULE_END_DATE >= #{rule_end_date}
           AND B.VALID_FALG = '1'
         ORDER BY B.ORGAN_CODE, B.CHANNEL_CODE,B.UPDATE_TIMESTAMP DESC
                
        ]]>
    </select>
    
    <select id="LpPrem_findContractProductByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID,A.PRODUCT_ID,A.PRODUCT_CODE,
			A.APPLY_CODE,A.BUSI_ITEM_ID,A.POLICY_CODE,A.PROD_PKG_PLAN_CODE,
			A.POLICY_ID,A.AMOUNT,UNIT,A.COUNT_WAY,A.BENEFIT_LEVEL,A.VALIDATE_DATE,
			A.EXPIRY_DATE,A.LIABILITY_STATE,A.CHARGE_PERIOD,A.CHARGE_YEAR,A.PREM_FREQ,
			A.COVERAGE_PERIOD,A.COVERAGE_YEAR,A.PAY_PERIOD,A.PAY_YEAR,A.PAY_FREQ,
			A.DECISION_CODE,A.STD_PREM_AF,A.INITIAL_DISCNT_PREM_AF,
			A.RENEWAL_DISCNTED_PREM_AF,A.EXTRA_PREM_AF,A.INITIAL_EXTRA_PREM_AF
			,A.RENEWAL_EXTRA_PREM_AF,A.TOTAL_PREM_AF,A.IS_GIFT,A.HEALTH_SERVICE_FLAG,
			A.INTEREST_MODE,A.PAIDUP_DATE,A.WAIVER_START,A.DEDUCTIBLE_FRANCHISE,
			A.PAYOUT_RATE,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.IS_WAIVED,A.WAIVER_END,
			A.INSERT_BY,A.INSERT_TIME,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP
			FROM DEV_NB.T_NB_CONTRACT_PRODUCT A
      INNER JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD B ON A.POLICY_ID=B.POLICY_ID AND A.BUSI_ITEM_ID=B.BUSI_ITEM_ID
       WHERE 1=1 
				]]>
			 <if test=" apply_code != null and apply_code != '' ">
				AND A.APPLY_CODE = #{apply_code}
			</if>
			<if test=" policy_code != null and policy_code != '' ">
				AND A.POLICY_CODE = #{policy_code}
			</if>
			<if test=" pol_no != null and pol_no != '' ">
				AND (B.OLD_POL_NO=#{pol_no} OR B.Busi_Item_Id=#{pol_no})
			</if>
		   <![CDATA[
			UNION 
			SELECT A.ITEM_ID,A.PRODUCT_ID,A.PRODUCT_CODE,
			A.APPLY_CODE,A.BUSI_ITEM_ID,A.POLICY_CODE,A.PROD_PKG_PLAN_CODE,
			A.POLICY_ID,A.AMOUNT,UNIT,A.COUNT_WAY,A.BENEFIT_LEVEL,A.VALIDATE_DATE,
			A.EXPIRY_DATE,A.LIABILITY_STATE,A.CHARGE_PERIOD,A.CHARGE_YEAR,A.PREM_FREQ,
			A.COVERAGE_PERIOD,A.COVERAGE_YEAR,A.PAY_PERIOD,A.PAY_YEAR,A.PAY_FREQ,
			A.DECISION_CODE,A.STD_PREM_AF,A.INITIAL_DISCNT_PREM_AF,
			A.RENEWAL_DISCNTED_PREM_AF,A.EXTRA_PREM_AF,A.INITIAL_EXTRA_PREM_AF
			,A.RENEWAL_EXTRA_PREM_AF,A.TOTAL_PREM_AF,A.IS_GIFT,A.HEALTH_SERVICE_FLAG,
			A.INTEREST_MODE,A.PAIDUP_DATE,A.WAIVER_START,A.DEDUCTIBLE_FRANCHISE,
			A.PAYOUT_RATE,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.IS_WAIVED,A.WAIVER_END,
			A.INSERT_BY,A.INSERT_TIME,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP
			FROM DEV_PAS.T_CONTRACT_PRODUCT A 
			INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD B ON A.POLICY_ID=B.POLICY_ID AND A.BUSI_ITEM_ID=B.BUSI_ITEM_ID
      		 WHERE 1=1 
			]]>
			 <if test=" apply_code != null and apply_code != '' ">
				AND A.APPLY_CODE = #{apply_code}
			</if>
			<if test=" policy_code != null and policy_code != '' ">
				AND A.POLICY_CODE = #{policy_code}
			</if>
			<if test=" pol_no != null and pol_no != '' ">
				AND (B.OLD_POL_NO=#{pol_no} OR B.Busi_Item_Id=#{pol_no})
			</if>
		   <![CDATA[
			UNION 
			SELECT A.ITEM_ID,A.PRODUCT_ID,A.PRODUCT_CODE,
		      A.APPLY_CODE,A.BUSI_ITEM_ID,A.POLICY_CODE,A.PROD_PKG_PLAN_CODE,
		      A.POLICY_ID,A.AMOUNT,A.UNIT,A.COUNT_WAY,A.BENEFIT_LEVEL,A.VALIDATE_DATE,
		      A.EXPIRY_DATE,A.LIABILITY_STATE,A.CHARGE_PERIOD,A.CHARGE_YEAR,A.PREM_FREQ,
		      A.COVERAGE_PERIOD,A.COVERAGE_YEAR,A.PAY_PERIOD,A.PAY_YEAR,A.PAY_FREQ,
		      A.DECISION_CODE,A.STD_PREM_AF,A.INITIAL_DISCNT_PREM_AF,
		      A.RENEWAL_DISCNTED_PREM_AF,A.EXTRA_PREM_AF,A.INITIAL_EXTRA_PREM_AF
		      ,A.RENEWAL_EXTRA_PREM_AF,A.TOTAL_PREM_AF,A.IS_GIFT,A.HEALTH_SERVICE_FLAG,
		      A.INTEREST_MODE,A.PAIDUP_DATE,A.WAIVER_START, A.DEDUCTIBLE_FRANCHISE,
		      A.PAYOUT_RATE,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF,A.IS_WAIVED,A.WAIVER_END,
		      A.INSERT_BY,A.INSERT_TIME,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP
		      FROM DEV_PAS.T_CONTRACT_MASTER M 
	          INNER JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT A
	          ON M.POLICY_ID=A.POLICY_ID
	          INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC ON A.CHANGE_ID=AC.CHANGE_ID 
	          INNER JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL B ON AC.CHANGE_ID=B.CHANGE_ID AND A.POLICY_ID=B.POLICY_ID AND B.Busi_Item_Id=A.Busi_Item_Id
	          WHERE 1=1 
		      ]]>
			 <if test=" apply_code != null and apply_code != '' ">
				AND M.APPLY_CODE = #{apply_code}
			</if>
			<if test=" policy_code != null and policy_code != '' ">
				AND M.POLICY_CODE = #{policy_code}
			</if>
			<if test=" pol_no != null and pol_no != '' ">
				AND (B.OLD_POL_NO=#{pol_no} OR B.Busi_Item_Id=#{pol_no})
			</if>
			AND (AC.ACCEPT_STATUS='08' OR  AC.ACCEPT_STATUS='09' OR  AC.ACCEPT_STATUS='18')
	          AND AC.SERVICE_CODE='NS' AND A.OLD_NEW='1' AND A.OPERATION_TYPE='1' AND B.OPERATION_TYPE='1'
	</select>
	
	<!-- 根据保单号查询银保通保单险种保费信息 -->
	<select id="PA_queryBaofei" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	       SELECT 
	       TCP.CHARGE_YEAR*TCP.TOTAL_PREM_AF CONTRACTPREMIUM,
	       TCP.CHARGE_PERIOD
	       FROM DEV_PAS.T_CONTRACT_MASTER TCMM,
	            DEV_PAS.T_CONTRACT_PRODUCT TCP
	       WHERE 1=1
	       AND TCMM.POLICY_CODE = TCP.POLICY_CODE
	       AND TCMM.SUBMIT_CHANNEL = '1'
	       AND (TCMM.END_CAUSE != '80' OR TCMM.END_CAUSE IS NULL)
	       AND TCMM.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 根据保单号查询豁免险种交费年期 -->
	<select id="PA_queryMasterChargeYear" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT B.CHARGE_YEAR
	            FROM DEV_PAS.T_CONTRACT_MASTER A, DEV_PAS.T_CONTRACT_PRODUCT B
	           WHERE A.POLICY_CODE = B.POLICY_CODE
	             /*AND B.IS_MASTER_ITEM IS NULL*/
	             AND (A.END_CAUSE != '80' OR A.END_CAUSE IS NULL)
	             AND B.IS_WAIVED = '1'
	             AND A.POLICY_CODE = #{policy_code}
	             AND ROWNUM = 1
		]]>
	</select>
</mapper>
