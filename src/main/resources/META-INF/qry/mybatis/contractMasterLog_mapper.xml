<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractMasterLog">
<!--
	<sql id="PA_contractMasterLogWhereCondition">
		<if test=" policy_pwd != null and policy_pwd != ''  "><![CDATA[ AND A.POLICY_PWD = #{policy_pwd} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_agent_name != null and sale_agent_name != ''  "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} ]]></if>
		<if test=" insured_family  != null "><![CDATA[ AND A.INSURED_FAMILY = #{insured_family} ]]></if>
		<if test=" service_handler_name != null and service_handler_name != ''  "><![CDATA[ AND A.SERVICE_HANDLER_NAME = #{service_handler_name} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" derivation != null and derivation != ''  "><![CDATA[ AND A.DERIVATION = #{derivation} ]]></if>
		<if test=" channel_org_code != null and channel_org_code != ''  "><![CDATA[ AND A.CHANNEL_ORG_CODE = #{channel_org_code} ]]></if>
		<if test=" basic_remark != null and basic_remark != ''  "><![CDATA[ AND A.BASIC_REMARK = #{basic_remark} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pwd_invalid_flag  != null "><![CDATA[ AND A.PWD_INVALID_FLAG = #{pwd_invalid_flag} ]]></if>
		<if test=" submit_channel != null and submit_channel != ''  "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" sale_agent_code != null and sale_agent_code != ''  "><![CDATA[ AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENCY_CODE = #{agency_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" service_handler_code != null and service_handler_code != ''  "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" service_handler != null and service_handler != ''  "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if>
		<if test=" e_service_flag  != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" dc_indi  != null "><![CDATA[ AND A.DC_INDI = #{dc_indi} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" agent_org_id != null and agent_org_id != ''  "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<if test=" lang_code != null and lang_code != ''  "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if>
		<if test=" log_type != null and log_type != ''  "><![CDATA[ AND A.LOG_TYPE = #{log_type} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractMasterLogByLogIdCondition">
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractMasterLogByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractMasterLogByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	

<!-- 按索引查询操作 -->	
	<select id="PA_findContractMasterLogByLogId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.INSURED_FAMILY, 
			A.SERVICE_HANDLER_NAME, A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, A.BASIC_REMARK, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.BRANCH_CODE, A.AGENCY_CODE, A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, 
			A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, 
			A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.LOG_ID, A.VALIDATE_DATE, A.SERVICE_BANK, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID FROM DEV_PAS.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterLogByLogIdCondition" />
	</select>
	
	<select id="PA_findContractMasterLogByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.INSURED_FAMILY, 
			A.SERVICE_HANDLER_NAME, A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, A.BASIC_REMARK, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.BRANCH_CODE, A.AGENCY_CODE, A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, 
			A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, 
			A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.LOG_ID, A.VALIDATE_DATE, A.SERVICE_BANK, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID FROM DEV_PAS.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterLogByPolicyIdCondition" />
	</select>
	
	<select id="PA_findContractMasterLogByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.INSURED_FAMILY, 
			A.SERVICE_HANDLER_NAME, A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, A.BASIC_REMARK, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.BRANCH_CODE, A.AGENCY_CODE, A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, 
			A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, 
			A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.LOG_ID, A.VALIDATE_DATE, A.SERVICE_BANK, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID FROM DEV_PAS.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterLogByPolicyCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractMasterLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.INSURED_FAMILY, 
			A.SERVICE_HANDLER_NAME, A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, A.BASIC_REMARK, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.BRANCH_CODE, A.AGENCY_CODE, A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, 
			A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, 
			A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.LOG_ID, A.VALIDATE_DATE, A.SERVICE_BANK, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID FROM DEV_PAS.T_CONTRACT_MASTER_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractMasterLog" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.INSURED_FAMILY, 
			A.SERVICE_HANDLER_NAME, A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, A.BASIC_REMARK, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.BRANCH_CODE, A.AGENCY_CODE, A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, 
			A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, 
			A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.LOG_ID, A.VALIDATE_DATE, A.SERVICE_BANK, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID FROM DEV_PAS.T_CONTRACT_MASTER_LOG A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractMasterLogTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_MASTER_LOG A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractMasterLogForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.POLICY_PWD, B.MEDIA_TYPE, B.APPLY_CODE, B.ORGAN_CODE, B.CHANNEL_TYPE, B.SALE_AGENT_NAME, B.INSURED_FAMILY, 
			B.SERVICE_HANDLER_NAME, B.POLICY_ID, B.DERIVATION, B.CHANNEL_ORG_CODE, B.BASIC_REMARK, 
			B.POLICY_TYPE, B.EXPIRY_DATE, B.PWD_INVALID_FLAG, B.SUBMIT_CHANNEL, B.LIABILITY_STATE, B.POLICY_CODE, B.SALE_AGENT_CODE, 
			B.BRANCH_CODE, B.AGENCY_CODE, B.MONEY_CODE, B.APL_PERMIT, B.SERVICE_HANDLER_CODE, 
			B.APPLY_DATE, B.INITIAL_VALIDATE_DATE, B.SUBMISSION_DATE, B.SERVICE_HANDLER, B.E_SERVICE_FLAG, B.POLICY_CHG_ID, 
			B.SERVICE_BANK_BRANCH, B.DC_INDI, B.END_CAUSE, B.ISSUE_DATE, B.LAPSE_CAUSE, B.DECISION_CODE, 
			B.AGENT_ORG_ID, B.LOG_ID, B.VALIDATE_DATE, B.SERVICE_BANK, B.LANG_CODE, B.LOG_TYPE, B.FORMER_ID FROM (
					SELECT ROWNUM RN, A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.INSURED_FAMILY, 
			A.SERVICE_HANDLER_NAME, A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, A.BASIC_REMARK, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.BRANCH_CODE, A.AGENCY_CODE, A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, 
			A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, 
			A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.LOG_ID, A.VALIDATE_DATE, A.SERVICE_BANK, A.LANG_CODE, A.LOG_TYPE, A.FORMER_ID FROM DEV_PAS.T_CONTRACT_MASTER_LOG A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<sql id="PA_findLiabilityStateChangeCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
	</sql>	
	<select id="PA_findLiabilityStateChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT MAX(A.INSERT_TIME) INSERT_TIME
			         FROM DEV_PAS.T_CONTRACT_MASTER_LOG A
			              WHERE 1 = 1]]>
		<include refid="PA_findLiabilityStateChangeCondition" />
	</select>
</mapper>
