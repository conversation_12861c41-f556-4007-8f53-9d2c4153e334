<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UWRuleResultDaoImpl">
<!--
	<sql id="ruleResultWhereCondition">
		<if test=" rule_set_name != null and rule_set_name != ''  "><![CDATA[ AND A.RULE_SET_NAME = #{rule_set_name} ]]></if>
		<if test=" rule_status != null and rule_status != ''  "><![CDATA[ AND A.RULE_STATUS = #{rule_status} ]]></if>
		<if test=" rule_type != null and rule_type != ''  "><![CDATA[ AND A.RULE_TYPE = #{rule_type} ]]></if>
		<if test=" uw_rule_id != null and uw_rule_id != ''  "><![CDATA[ AND <PERSON>.UW_RULE_ID = #{uw_rule_id} ]]></if>
		<if test=" rr_id  != null "><![CDATA[ AND A.RR_ID = #{rr_id} ]]></if>
		<if test=" rule_insert_time  != null  and  rule_insert_time  != ''  "><![CDATA[ AND A.RULE_INSERT_TIME = #{rule_insert_time} ]]></if>
		<if test=" rule_param != null and rule_param != ''  "><![CDATA[ AND A.RULE_PARAM = #{rule_param} ]]></if>
		<if test=" rule_modi_time  != null  and  rule_modi_time  != ''  "><![CDATA[ AND A.RULE_MODI_TIME = #{rule_modi_time} ]]></if>
		<if test=" rule_mag != null and rule_mag != ''  "><![CDATA[ AND A.RULE_MAG = #{rule_mag} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" rule_modi_user  != null "><![CDATA[ AND A.RULE_MODI_USER = #{rule_modi_user} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" auto_id  != null "><![CDATA[ AND A.AUTO_ID = #{auto_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" notes != null and notes != ''  "><![CDATA[ AND A.NOTES = #{notes} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryRuleResultByRrIdCondition">
		<if test=" rr_id  != null "><![CDATA[ AND A.RR_ID = #{rr_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addRuleResult"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="rr_id"> SELECT S_T_RULE_RESULT.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_RULE_RESULT(
				RULE_SET_NAME, RULE_STATUS, RULE_TYPE, INSERT_TIME, UW_RULE_ID, RR_ID, RULE_INSERT_TIME, 
				UPDATE_TIME, RULE_PARAM, RULE_MODI_TIME, RULE_MAG, APPLY_CODE, INSERT_TIMESTAMP, RULE_MODI_USER, 
				POLICY_CODE, AUTO_ID, UPDATE_BY, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, NOTES ) 
			VALUES (
				#{rule_set_name, jdbcType=VARCHAR}, #{rule_status, jdbcType=VARCHAR} , #{rule_type, jdbcType=VARCHAR} , SYSDATE , #{uw_rule_id, jdbcType=VARCHAR} , #{rr_id, jdbcType=NUMERIC} , SYSDATE 
				, SYSDATE , #{rule_param, jdbcType=VARCHAR} , #{rule_modi_time, jdbcType=DATE} , #{rule_mag, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{rule_modi_user, jdbcType=NUMERIC} 
				, #{policy_code, jdbcType=VARCHAR} , #{auto_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{notes, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRuleResult" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_RULE_RESULT WHERE RR_ID = #{rr_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateRuleResult" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_RULE_RESULT ]]>
		<set>
		<trim suffixOverrides=",">
			RULE_STATUS = #{rule_status, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    RULE_MODI_TIME = SYSDATE ,
		    RULE_MODI_USER = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			NOTES = #{notes, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE RR_ID = #{rr_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findRuleResultByRrId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RULE_SET_NAME, A.RULE_STATUS, A.RULE_TYPE, A.UW_RULE_ID, A.RR_ID, A.RULE_INSERT_TIME, 
			A.RULE_PARAM, A.RULE_MODI_TIME, A.RULE_MAG, A.APPLY_CODE, A.RULE_MODI_USER, 
			A.POLICY_CODE, A.AUTO_ID, A.POLICY_ID, A.NOTES FROM DEV_UW.T_RULE_RESULT A WHERE 1 = 1  ]]>
		<include refid="queryRuleResultByRrIdCondition" />
		<![CDATA[ ORDER BY A.RR_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapRuleResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RULE_SET_NAME, A.RULE_STATUS, A.RULE_TYPE, A.UW_RULE_ID, A.RR_ID, A.RULE_INSERT_TIME, 
			A.RULE_PARAM, A.RULE_MODI_TIME, A.RULE_MAG, A.APPLY_CODE, A.RULE_MODI_USER, 
			A.POLICY_CODE, A.AUTO_ID, A.POLICY_ID, A.NOTES FROM DEV_UW.T_RULE_RESULT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RR_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllRuleResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RULE_SET_NAME, A.RULE_STATUS, A.RULE_TYPE, A.UW_RULE_ID, A.RR_ID, A.RULE_INSERT_TIME, 
			A.RULE_PARAM, A.RULE_MODI_TIME, A.RULE_MAG, A.APPLY_CODE, A.RULE_MODI_USER, 
			A.POLICY_CODE, A.AUTO_ID, A.POLICY_ID, A.NOTES FROM DEV_UW.T_RULE_RESULT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RR_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="UW_findRuleResultTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_RULE_RESULT A WHERE 1 = 1 AND A.RULE_STATUS='2' ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="queryRuleResultForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RULE_SET_NAME, B.RULE_STATUS, B.RULE_TYPE, B.UW_RULE_ID, B.RR_ID, B.RULE_INSERT_TIME, 
			B.RULE_PARAM, B.RULE_MODI_TIME, B.RULE_MAG, B.APPLY_CODE, B.RULE_MODI_USER, 
			B.POLICY_CODE, B.AUTO_ID, B.POLICY_ID, B.NOTES FROM (
					SELECT ROWNUM RN, A.RULE_SET_NAME, A.RULE_STATUS, A.RULE_TYPE, A.UW_RULE_ID, A.RR_ID, A.RULE_INSERT_TIME, 
			A.RULE_PARAM, A.RULE_MODI_TIME, A.RULE_MAG, A.APPLY_CODE, A.RULE_MODI_USER, 
			A.POLICY_CODE, A.AUTO_ID, A.POLICY_ID, A.NOTES FROM DEV_UW.T_RULE_RESULT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RR_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 删除操作 -->	
	<delete id="delRuleResultByAutoId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_RULE_RESULT WHERE AUTO_ID = #{auto_id} ]]>
	</delete>
	<!--by zhaoyoan_wb 根据投保单号查询所有操作 -->
	<select id="UW_findAllRuleResultByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.APPLY_CODE,A.POLICY_CODE,A.RULE_MAG,A.INSERT_TIME,A.UPDATE_TIME FROM DEV_UW.T_RULE_RESULT A 
			WHERE ROWNUM <=  1000  
		]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ 
			ORDER BY A.RR_ID ]]> 
	</select>
</mapper>
