<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IContractProductOtherDao">

	<sql id="contractProductOtherWhereCondition">
		<if test=" is_extend_rate  != null "><![CDATA[ AND A.IS_EXTEND_RATE = #{is_extend_rate} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" field20 != null and field20 != ''  "><![CDATA[ AND A.FIELD20 = #{field20} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" field19 != null and field19 != ''  "><![CDATA[ AND A.FIELD19 = #{field19} ]]></if>
		<if test=" field17 != null and field17 != ''  "><![CDATA[ AND A.FIELD17 = #{field17} ]]></if>
		<if test=" field18 != null and field18 != ''  "><![CDATA[ AND A.FIELD18 = #{field18} ]]></if>
		<if test=" busi_item_id != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" field13 != null and field13 != ''  "><![CDATA[ AND A.FIELD13 = #{field13} ]]></if>
		<if test=" field14 != null and field14 != ''  "><![CDATA[ AND A.FIELD14 = #{field14} ]]></if>
		<if test=" field15 != null and field15 != ''  "><![CDATA[ AND A.FIELD15 = #{field15} ]]></if>
		<if test=" field16 != null and field16 != ''  "><![CDATA[ AND A.FIELD16 = #{field16} ]]></if>
		<if test=" field10 != null and field10 != ''  "><![CDATA[ AND A.FIELD10 = #{field10} ]]></if>
		<if test=" field11 != null and field11 != ''  "><![CDATA[ AND A.FIELD11 = #{field11} ]]></if>
		<if test=" field12 != null and field12 != ''  "><![CDATA[ AND A.FIELD12 = #{field12} ]]></if>
		<if test=" field7 != null and field7 != ''  "><![CDATA[ AND A.FIELD7 = #{field7} ]]></if>
		<if test=" field6 != null and field6 != ''  "><![CDATA[ AND A.FIELD6 = #{field6} ]]></if>
		<if test=" field9 != null and field9 != ''  "><![CDATA[ AND A.FIELD9 = #{field9} ]]></if>
		<if test=" field8 != null and field8 != ''  "><![CDATA[ AND A.FIELD8 = #{field8} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" field3 != null and field3 != ''  "><![CDATA[ AND A.FIELD3 = #{field3} ]]></if>
		<if test=" field2 != null and field2 != ''  "><![CDATA[ AND A.FIELD2 = #{field2} ]]></if>
		<if test=" field5 != null and field5 != ''  "><![CDATA[ AND A.FIELD5 = #{field5} ]]></if>
		<if test=" field4 != null and field4 != ''  "><![CDATA[ AND A.FIELD4 = #{field4} ]]></if>
		<if test=" field1 != null and field1 != ''  "><![CDATA[ AND A.FIELD1 = #{field1} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator="OR">
				<![CDATA[ A.POLICY_ID = #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
	</sql>

<!-- 按索引生成的查询条件 -->	  
	<sql id="queryContractProductOtherByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	


<!-- 按索引查询操作 -->	
	<select id="findContractProductOtherByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
			A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
			A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
			A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, 
			A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1 FROM dev_pas.T_CONTRACT_PRODUCT_OTHER A WHERE 1 = 1 
			AND A.ITEM_ID = #{item_id}  ]]>
		<include refid="contractProductOtherWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	
	<select id="findContractProductOtherByItemIdQryOfNb" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT '' as IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
	      A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
	      A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
	      A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, 
	      A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1 FROM  dev_nb.t_nb_CONTRACT_PRODUCT_OTHER A WHERE 1 = 1 
	      AND A.ITEM_ID = #{item_id}  ]]>
		<include refid="contractProductOtherWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findAllContractProductOther" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
			A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
			A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
			A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, 
			A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1 FROM dev_pas.T_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <=  1000  ]]>
		<include refid="contractProductOtherWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]> 
	</select>
	
	<select id="findAllContractProductOtherQryOfNb" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT '' as IS_EXTEND_RATE, A.PRODUCT_ID, A.BUSI_PRD_ID, A.ITEM_ID, A.FIELD20, A.APPLY_CODE, A.FIELD19, 
	      A.FIELD17, A.FIELD18, A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, 
	      A.FIELD14, A.FIELD15, A.FIELD16, A.FIELD10, A.FIELD11, A.FIELD12, 
	      A.FIELD7, A.FIELD6, A.FIELD9, A.FIELD8, A.POLICY_CODE, A.FIELD3, 
	      A.FIELD2, A.FIELD5, A.FIELD4, A.FIELD1 FROM dev_nb.t_nb_CONTRACT_PRODUCT_OTHER A WHERE ROWNUM <=  1000   ]]>
		<include refid="contractProductOtherWhereCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID ]]> 
	</select>

	
</mapper>
