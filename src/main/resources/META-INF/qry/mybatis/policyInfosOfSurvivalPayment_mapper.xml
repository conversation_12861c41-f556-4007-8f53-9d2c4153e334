<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QueryPolicyInfosOfSurvivalPaymentDaoImpl">

	<!-- 查询生存金满期金保单信息 -->
	<select id="PA_findPolicyInfos" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[
			SELECT TCM.POLICY_CODE,
				   TCM.LIABILITY_STATE,
				   TCM.LAPSE_CAUSE,
				   TCM.END_CAUSE,
				   TCM.MULTI_MAINRISK_FLAG,
				   TCM.VALIDATE_DATE
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM
			 INNER JOIN DEV_PAS.T_POLICY_HOLDER TPH
			    ON TCM.POLICY_ID = TPH.POLICY_ID
			 INNER JOIN DEV_PAS.T_CUSTOMER TC
			    ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
			 WHERE 1 = 1
			   AND TC.CUSTOMER_NAME = #{customer_name}
			   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
			   AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}
			   AND EXISTS
			 (SELECT 1
			          FROM DEV_PAS.T_PAY_PLAN TPP
			         WHERE TPP.POLICY_ID = TCM.POLICY_ID
			           AND TPP.PAY_PLAN_TYPE IN ('3', '4', '8', '10', '11'))
			
			UNION
			
			SELECT TCM.POLICY_CODE,
				   TCM.LIABILITY_STATE,
				   TCM.LAPSE_CAUSE,
				   TCM.END_CAUSE,
				   TCM.MULTI_MAINRISK_FLAG,
				   TCM.VALIDATE_DATE
			  FROM DEV_PAS.T_CONTRACT_MASTER TCM
			 INNER JOIN DEV_PAS.T_INSURED_LIST TIL
			    ON TCM.POLICY_ID = TIL.POLICY_ID
			 INNER JOIN DEV_PAS.T_CUSTOMER TC
			    ON TIL.CUSTOMER_ID = TC.CUSTOMER_ID
			 WHERE 1 = 1
			   AND TC.CUSTOMER_NAME = #{customer_name}
			   AND TC.CUSTOMER_CERT_TYPE = #{customer_cert_type}
			   AND TC.CUSTOMER_CERTI_CODE = #{customer_certi_code}
			   AND EXISTS
			 (SELECT 1
			          FROM DEV_PAS.T_PAY_PLAN TPP
			         WHERE TPP.POLICY_ID = TCM.POLICY_ID
			           AND TPP.PAY_PLAN_TYPE IN ('3', '4', '8', '10', '11'))
		]]>
	</select>

    <!-- 查询投保人信息 -->
	<select id="PA_findPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
		 SELECT TC.CUSTOMER_NAME,
			    TC.CUSTOMER_GENDER,
			    TC.CUSTOMER_BIRTHDAY,
			    TC.CUSTOMER_CERT_TYPE,
			    TC.CUSTOMER_CERTI_CODE
		   FROM DEV_PAS.T_POLICY_HOLDER TPH
	      INNER JOIN DEV_PAS.T_CUSTOMER TC
		     ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID
		  WHERE TPH.POLICY_CODE = #{policy_code}
	     ]]>
	</select>
	
	<!-- 查询险种信息 -->
	<select id="PA_findContractBusiProds" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
		 SELECT TCBP.BUSI_ITEM_ID,
		        TCBP.MASTER_BUSI_ITEM_ID,
		        TCBP.BUSI_PROD_CODE,
		        TBP.PRODUCT_NAME_SYS,
		        TBP.PRODUCT_ABBR_NAME,
		        TCBP.LIABILITY_STATE,
		        TCBP.LAPSE_CAUSE,
		        TCBP.END_CAUSE
		   FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
		  INNER JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
		     ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		  WHERE TCBP.POLICY_CODE = #{policy_code}
	     ]]>
	</select>
	
	<!-- 查询被保人信息 -->
	<select id="PA_findInsuredInfos" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
		 SELECT TC.CUSTOMER_NAME,
		        TC.CUSTOMER_GENDER,
		        TC.CUSTOMER_BIRTHDAY,
		        TC.CUSTOMER_CERT_TYPE,
		        TC.CUSTOMER_CERTI_CODE,
		        TBI.ORDER_ID
		   FROM DEV_PAS.T_INSURED_LIST TIL
		  INNER JOIN DEV_PAS.T_BENEFIT_INSURED TBI
		     ON TIL.LIST_ID = TBI.INSURED_ID
		    AND TIL.POLICY_CODE = TBI.POLICY_CODE
		  INNER JOIN DEV_PAS.T_CUSTOMER TC
		     ON TIL.CUSTOMER_ID = TC.CUSTOMER_ID
		  WHERE 1 = 1
		    AND TBI.POLICY_CODE = #{policy_code}
		    AND TBI.BUSI_ITEM_ID = #{busi_item_id}
	     ]]>
	</select>
	
	<!-- 查询生存给付计划信息  -->
	<select id="PA_findPayPlanInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT
			       TPD.PLAN_ID,
			       (SELECT LIA.LIAB_NAME FROM DEV_PAS.T_LIABILITY LIA WHERE LIA.LIAB_ID = TPD.LIAB_ID) LIAB_NAME,
			       TPP.BEGIN_DATE,
			       TPP.END_DATE,
			       (SELECT 1
	                  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
	                 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
	                    ON A.CHANGE_ID = B.CHANGE_ID
	                   AND A.ACCEPT_ID = B.ACCEPT_ID
	                 INNER JOIN DEV_PAS.T_CS_PAY_DUE NEW_
	                    ON B.POLICY_CHG_ID = NEW_.POLICY_CHG_ID
	                   AND B.POLICY_ID = NEW_.POLICY_ID
	                   AND NEW_.FEE_STATUS = '01'
	                   AND B.POLICY_CODE = #{policy_code}
	                   AND NEW_.BUSI_ITEM_ID = #{busi_item_id}
	                   AND A.SERVICE_CODE = 'CM'
	                   AND A.ACCEPT_STATUS = 18
	                   AND ROWNUM = 1) CM_FLAG
			  FROM DEV_PAS.T_PAY_DUE TPD
			  LEFT JOIN DEV_PAS.T_PAY_PLAN TPP
			    ON TPD.PLAN_ID = TPP.PLAN_ID
			   AND TPD.POLICY_CODE = TPP.POLICY_CODE
			   AND TPD.BUSI_ITEM_ID = TPP.BUSI_ITEM_ID
			 WHERE TPD.POLICY_CODE = #{policy_code}
			   AND TPD.BUSI_ITEM_ID = #{busi_item_id}
			   AND TPP.PAY_PLAN_TYPE IN ('3','4','8','10','11')
			   AND TPD.FEE_STATUS <> '02'
		]]>
	</select>
	
	<!-- 查询可领取金额  -->
	<select id="PA_findEligibleAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(TAB.FEE_AMOUNT) TOTAL_AMOUNT
			  FROM (SELECT SUM(NVL(NEW_DUE.FEE_AMOUNT, 0) - NVL(OLD_DUE.FEE_AMOUNT, 0)) FEE_AMOUNT
			          FROM (SELECT NEW_.PAY_NUM,
			                       NEW_.FEE_AMOUNT,
			                       A.ACCEPT_CODE,
			                       B.POLICY_CODE,
			                       NEW_.BUSI_ITEM_ID,
			                       NEW_.BUSI_PROD_CODE
			                  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
			                 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
			                    ON A.CHANGE_ID = B.CHANGE_ID
			                   AND A.ACCEPT_ID = B.ACCEPT_ID
			                 INNER JOIN DEV_PAS.T_CS_PAY_DUE NEW_
			                    ON B.POLICY_CHG_ID = NEW_.POLICY_CHG_ID
			                   AND B.POLICY_ID = NEW_.POLICY_ID
			                   AND NEW_.OLD_NEW = '1'
			                   AND NEW_.FEE_STATUS = '01'
			                   AND NEW_.SURVIVAL_MODE = 1
			                   AND NEW_.PLAN_ID = #{plan_id}
			                   AND B.POLICY_CODE = #{policy_code}
			                   AND NEW_.BUSI_ITEM_ID = #{busi_item_id}
			                   AND A.SERVICE_CODE = 'CM'
			                   AND A.ACCEPT_STATUS = 18) NEW_DUE
			          LEFT JOIN (SELECT OLD_.PAY_NUM,
			                            OLD_.FEE_AMOUNT,
			                            A.ACCEPT_CODE,
			                            B.POLICY_CODE,
			                            OLD_.BUSI_ITEM_ID,
			                            OLD_.BUSI_PROD_CODE
			                       FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
			                      INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
			                         ON A.CHANGE_ID = B.CHANGE_ID
			                        AND A.ACCEPT_ID = B.ACCEPT_ID
			                      INNER JOIN DEV_PAS.T_CS_PAY_DUE OLD_
			                         ON B.POLICY_CHG_ID = OLD_.POLICY_CHG_ID
			                        AND B.POLICY_ID = OLD_.POLICY_ID
			                        AND OLD_.OLD_NEW = '0'
			                        AND OLD_.FEE_STATUS = '01'
			                        AND OLD_.SURVIVAL_MODE = 1
			                        AND OLD_.PLAN_ID = #{plan_id}
			                        AND B.POLICY_CODE = #{policy_code}
			                        AND OLD_.BUSI_ITEM_ID = #{busi_item_id}
			                        AND A.SERVICE_CODE = 'CM'
			                        AND A.ACCEPT_STATUS = 18) OLD_DUE
			            ON OLD_DUE.PAY_NUM = NEW_DUE.PAY_NUM
			         WHERE EXISTS (SELECT 1
			                  FROM DEV_CAP.T_PREM_ARAP TPA
			                 WHERE TPA.POLICY_CODE = NEW_DUE.POLICY_CODE
			                   AND TPA.BUSI_PROD_CODE = NEW_DUE.BUSI_PROD_CODE ]]>
			      <if test=" multi_mainrisk_flag  != null   and  multi_mainrisk_flag  != '' and multi_mainrisk_flag == '1'.toString() ">
			         <![CDATA[ AND TPA.BUSI_ITEM_ID = NEW_DUE.BUSI_ITEM_ID ]]>
			      </if> 
			        <![CDATA[  AND TPA.BUSINESS_CODE = NEW_DUE.ACCEPT_CODE
			                   AND TPA.FEE_TYPE IN ('P004390200', 'G004530400')
			                   AND TPA.FEE_STATUS IN ('00', '03','04'))
			                   
			       UNION ALL
			       
			        SELECT TPD.FEE_AMOUNT
			          FROM DEV_PAS.T_PAY_DUE TPD
			         WHERE TPD.POLICY_CODE = #{policy_code}
			           AND TPD.BUSI_ITEM_ID = #{busi_item_id}
			           AND TPD.PLAN_ID = #{plan_id}
			           AND TPD.FEE_STATUS = '01'
			           AND EXISTS (SELECT 1
					                 FROM DEV_CAP.T_PREM_ARAP TPA
					                WHERE TPD.UNIT_NUMBER = TPA.UNIT_NUMBER
					                  AND TPD.POLICY_CODE = TPA.POLICY_CODE
					                  AND TPD.BUSI_PROD_CODE = TPA.BUSI_PROD_CODE ]]>
				<if test=" multi_mainrisk_flag  != null   and  multi_mainrisk_flag  != '' and multi_mainrisk_flag == '1'.toString() ">
			         <![CDATA[ AND TPD.BUSI_ITEM_ID = TPA.BUSI_ITEM_ID ]]>
			    </if>
					        <![CDATA[ AND TPA.FEE_STATUS IN ('00','03','04'))
			      
			        UNION ALL
			        
			        SELECT TPD.FEE_AMOUNT
			          FROM DEV_PAS.T_PAY_DUE TPD
			         WHERE TPD.POLICY_CODE = #{policy_code}
			           AND TPD.BUSI_ITEM_ID = #{busi_item_id}
			           AND TPD.PLAN_ID = #{plan_id}
			           AND TPD.FEE_STATUS = '00') TAB
		]]>
	</select>
	
	<!-- 查询已领取金额  -->
	<select id="PA_findReceivedAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT SUM(TAA.FEE_AMOUNT) TOTAL_AMOUNT
			  FROM ( ]]>
		        <choose>
			      <when test="cm_flag != null and cm_flag != ''">
		   <![CDATA[SELECT TPD.FEE_AMOUNT
			          FROM DEV_PAS.T_PAY_DUE TPD
			         WHERE TPD.POLICY_CODE = #{policy_code}
			           AND TPD.BUSI_ITEM_ID = #{busi_item_id}
			           AND TPD.PLAN_ID = #{plan_id}
			           AND TPD.SURVIVAL_MODE = 01
			           AND TPD.FEE_STATUS = '01'
			        
			        UNION ALL
			        
			        SELECT SUM(NVL(NEW_DUE.FEE_AMOUNT, 0) - NVL(OLD_DUE.FEE_AMOUNT, 0))*(-1) FEE_AMOUNT
			          FROM (SELECT NEW_.PAY_NUM,
			                       NEW_.FEE_AMOUNT,
			                       A.ACCEPT_CODE,
			                       B.POLICY_CODE,
			                       NEW_.BUSI_ITEM_ID,
			                       NEW_.BUSI_PROD_CODE
			                  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
			                 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
			                    ON A.CHANGE_ID = B.CHANGE_ID
			                   AND A.ACCEPT_ID = B.ACCEPT_ID
			                 INNER JOIN DEV_PAS.T_CS_PAY_DUE NEW_
			                    ON B.POLICY_CHG_ID = NEW_.POLICY_CHG_ID
			                   AND B.POLICY_ID = NEW_.POLICY_ID
			                   AND NEW_.OLD_NEW = '1'
			                   AND NEW_.FEE_STATUS = '01'
			                   AND NEW_.SURVIVAL_MODE = 1
			                   AND NEW_.PLAN_ID = #{plan_id}
			                   AND B.POLICY_CODE = #{policy_code}
			                   AND NEW_.BUSI_ITEM_ID = #{busi_item_id}
			                   AND A.SERVICE_CODE = 'CM'
			                   AND A.ACCEPT_STATUS = 18) NEW_DUE
			          LEFT JOIN (SELECT OLD_.PAY_NUM,
			                            OLD_.FEE_AMOUNT,
			                            A.ACCEPT_CODE,
			                            B.POLICY_CODE,
			                            OLD_.BUSI_ITEM_ID,
			                            OLD_.BUSI_PROD_CODE
			                       FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
			                      INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE B
			                         ON A.CHANGE_ID = B.CHANGE_ID
			                        AND A.ACCEPT_ID = B.ACCEPT_ID
			                      INNER JOIN DEV_PAS.T_CS_PAY_DUE OLD_
			                         ON B.POLICY_CHG_ID = OLD_.POLICY_CHG_ID
			                        AND B.POLICY_ID = OLD_.POLICY_ID
			                        AND OLD_.OLD_NEW = '0'
			                        AND OLD_.FEE_STATUS = '01'
			                        AND OLD_.SURVIVAL_MODE = 1
			                        AND OLD_.PLAN_ID = #{plan_id}
			                        AND B.POLICY_CODE = #{policy_code}
			                        AND OLD_.BUSI_ITEM_ID = #{busi_item_id}
			                        AND A.SERVICE_CODE = 'CM'
			                        AND A.ACCEPT_STATUS = 18) OLD_DUE
			            ON OLD_DUE.PAY_NUM = NEW_DUE.PAY_NUM
			         WHERE EXISTS (SELECT 1
			                  FROM DEV_CAP.T_PREM_ARAP TPA
			                 WHERE TPA.POLICY_CODE = NEW_DUE.POLICY_CODE
			                   AND TPA.BUSI_PROD_CODE = NEW_DUE.BUSI_PROD_CODE ]]>
			      <if test=" multi_mainrisk_flag  != null   and  multi_mainrisk_flag  != '' and multi_mainrisk_flag == '1'.toString() ">
			         <![CDATA[ AND TPA.BUSI_ITEM_ID = NEW_DUE.BUSI_ITEM_ID ]]>
			      </if>       
			         <![CDATA[ AND TPA.BUSINESS_CODE = NEW_DUE.ACCEPT_CODE
			                   AND TPA.FEE_TYPE IN ('P004390200', 'G004530400')
			                   AND TPA.FEE_STATUS IN ('00', '03','04'))]]>
			      </when>
			      <otherwise>
	      <![CDATA[ SELECT TPD.FEE_AMOUNT
			          FROM DEV_PAS.T_PAY_DUE TPD
			         WHERE TPD.POLICY_CODE = #{policy_code}
			           AND TPD.BUSI_ITEM_ID = #{busi_item_id}
			           AND TPD.PLAN_ID = #{plan_id}
			           AND TPD.SURVIVAL_MODE = 1
			           AND TPD.FEE_STATUS = '01'
			           AND EXISTS (SELECT 1
							         FROM DEV_CAP.V_PREM_ARAP TPA
							        WHERE TPD.UNIT_NUMBER = TPA.UNIT_NUMBER
							          AND TPD.POLICY_CODE = TPA.POLICY_CODE
							          AND TPD.BUSI_PROD_CODE = TPA.BUSI_PROD_CODE ]]>
						    <![CDATA[ AND TPA.FEE_STATUS = '01') ]]>
			      </otherwise>
			   </choose>          
		<![CDATA[	        
			        UNION ALL
	          
			        SELECT TPD.FEE_AMOUNT
			          FROM DEV_PAS.T_PAY_DUE TPD
			         WHERE TPD.POLICY_CODE = #{policy_code}
			           AND TPD.BUSI_ITEM_ID = #{busi_item_id}
			           AND TPD.PLAN_ID = #{plan_id}
			           AND TPD.SURVIVAL_MODE IN (2, 3, 4)
			           AND TPD.FEE_STATUS = '01') TAA
		]]>
	</select>
	
</mapper>