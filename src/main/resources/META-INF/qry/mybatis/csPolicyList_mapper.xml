<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.CSPolicyListDaoImpl">
	
	<!-- 保全核保做出核保决定根据核保id查询保单信息列表  xuhp 2015年8月18日-->
	<select id="findAcceptPolicyListInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select y.uw_id,(select biz_code from DEV_UW.T_UW_MASTER where uw_source_type = '2' and uw_id = y.uw_id) accept_code,y.policy_code,(select customer_name from DEV_UW.T_POLICY_HOLDER where uw_id = y.uw_id  and policy_code = y.policy_code) holder_name,
			(select st.customer_name from DEV_UW.T_INSURED_LIST st,DEV_UW.T_BENEFIT_INSURED bs where st.uw_id = bs.uw_id and st.policy_id = bs.policy_id and st.list_id = bs.insured_id and bs.order_id = '1' and st.uw_id = y.uw_id  and st.policy_code = y.policy_code and rownum = 1) insured_name,
			nvl((select decision_desc from DEV_UW.T_POLICY_DECISION where decision_code = y.policy_decision),'待核保') uw_decision,
			(select status_name from DEV_UW.T_UW_STATUS where uw_status = y.uw_status) uw_status,
			(select user_name from DEV_PAS.T_UDMP_USER where user_id = y.update_by) update_by,
			(select '1' from DEV_UW.T_NOTE where note_busi_code = (select biz_code from DEV_UW.T_UW_MASTER where uw_source_type = '2' and uw_id = y.uw_id) and rownum = 1) note_flag,
			(case when (select '1' from DEV_UW.T_PENOTICE where penotice_id =(select max(penotice_id) penotice_id from DEV_UW.T_PENOTICE where uw_id = y.uw_id) and uw_id = y.uw_id and positive_indi = '1') = 1 then '0'
             when (select '1' from DEV_UW.T_PENOTICE where penotice_id =(select max(penotice_id) penotice_id from DEV_UW.T_PENOTICE where uw_id = y.uw_id) and uw_id = y.uw_id and positive_indi = '0') = 1 then '1' end) positive_indi,
            (select user_name from DEV_PAS.T_UDMP_USER where user_id =(select previous_uw_user from DEV_UW.T_UW_MASTER where uw_id = y.uw_id)) previous_uw_user,
            (select uw_esca_indi from DEV_UW.T_UW_MASTER  where uw_source_type = '2' and uw_id = y.uw_id) uw_esca_indi,
            (select uw_comments from DEV_UW.T_UW_TRACE where uw_id = y.UW_ID and uw_event_code = '11' and trace_id = (select max(trace_id) from DEV_UW.T_UW_TRACE where uw_id = y.UW_ID and uw_event_code = '11')
             and exists (select 1 from DEV_UW.T_UW_MASTER where uw_source_type = '2' and uw_id = y.UW_ID and uw_esca_indi =1)) uw_comments,
            (select uw_comments from DEV_UW.T_UW_TRACE where uw_id = y.UW_ID and uw_event_code = '12' and trace_id = (select max(trace_id) from DEV_UW.T_UW_TRACE where uw_id = y.UW_ID and uw_event_code = '12')
             and exists (select 1 from DEV_UW.T_UW_MASTER where uw_source_type = '2' and uw_id = y.UW_ID and uw_esca_indi =2)) uw_lower_comments   
 			from DEV_UW.T_UW_POLICY y where  uw_id = ${uw_id} ]]>
		<![CDATA[order by y.policy_code]]>
	</select>
	
	<!-- 保全核保做出核保决定根据核保id查询保单信息列表  xuhp 2015年8月18日-->
	<select id="findAcceptPolicyBizCodeInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<!-- <![CDATA[ select (select biz_code from DEV_UW.T_UW_MASTER where uw_id = y.uw_id) accept_code from DEV_UW.T_UW_POLICY y where  uw_id = ${uw_id} ]]>
		<![CDATA[order by y.policy_code]]> -->
		<![CDATA[ select biz_code accept_code from DEV_UW.T_UW_MASTER where uw_id = ${uw_id} ]]>
	</select>

	<!-- 保全核保做出核保决定根据核保id查询保全项目 2015年9月22日-->
	<!-- 可操作的保全项目：
		保单复效	保全提交核保的全部保单 RE
		增补告知	保全提交核保的全部保单 HI
		客户重要资料变更	全部保单   CM
		职业变更	保全提交核保的全部保单 IO 
		万能险基本保额增加 该保全项目保全只推万能险种 CA
		保障计划约定变更 全部保单  XX
		
		新增附加险	保全申请新增的险种或责任组 NS   只有新增的附件险可以操作，原先的不能操作
		加保	保全申请加保的险种或责任组 PA       暂时无法判断，先搁异
		新增附加特约  DA 只有新增的附件险可以操作，原先的不能操作
		
		不可操作的保全项目：
		投保人变更	保全提交核保的任务申请不通过、通过  AE
		受益人变更	保全提交核保的任务申请不通过、通过 BC
		-->
	<select id="findAcceptServiceCodeInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select case when service_code in ('RE','HI','CM','IO','CA','XX') then '0' 
		                      when service_code ='NS' then '1'
		                      when service_code ='PA' then '2'
		                      when service_code in ('AE','BC') then '3'
		                      when service_code ='DA' then '4'
		                      else '5' end service_code,(select DECISION_INDI from DEV_UW.T_UW_PRODUCT where uw_prd_id = ${uw_prd_id}) decision_indi from DEV_UW.T_UW_MASTER where uw_id = ${uw_id} ]]>
	</select>
	
	<!-- 根据uwID查询理赔保单信息-->
	<select id="findAllClmListInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select y.uw_id,
       (select biz_code
          from DEV_UW.T_UW_MASTER
         where uw_source_type = '4'
           and uw_id = y.uw_id) claim_code,
       y.policy_code,
        y.apply_code,
       (select customer_name
          from DEV_UW.T_POLICY_HOLDER
         where uw_id = y.uw_id
           and policy_code = y.policy_code) holder_name,
       (select customer_name
          from DEV_UW.T_INSURED_LIST
         where uw_id = y.uw_id
           and policy_code = y.policy_code and rownum=1) insured_name,
       nvl((select decision_desc
             from DEV_UW.T_POLICY_DECISION
            where decision_code = y.policy_decision),
           '待核保') uw_decision,
       (select status_name from DEV_UW.T_UW_STATUS where uw_status = y.uw_status) uw_status,
       (select real_name from DEV_PAS.T_UDMP_USER where user_id = y.update_by) update_by,
       (select user_name from DEV_PAS.T_UDMP_USER where user_id =(select previous_uw_user from DEV_UW.T_UW_MASTER where uw_id = y.uw_id)) previous_uw_user,
       (select uw_esca_indi from DEV_UW.T_UW_MASTER  where uw_source_type = '4' and uw_id = y.uw_id) uw_esca_indi,
       (select uw_comments from DEV_UW.T_UW_TRACE where uw_id = y.UW_ID and uw_event_code = '11' and trace_id = (select max(trace_id) from DEV_UW.T_UW_TRACE where uw_id = y.UW_ID and uw_event_code = '11')
         and exists (select 1 from DEV_UW.T_UW_MASTER where uw_source_type = '4' and uw_id = y.UW_ID and uw_esca_indi =1)) uw_comments,
       (select uw_comments from DEV_UW.T_UW_TRACE where uw_id = y.UW_ID and uw_event_code = '12' and trace_id = (select max(trace_id) from DEV_UW.T_UW_TRACE where uw_id = y.UW_ID and uw_event_code = '12')
         and exists (select 1 from DEV_UW.T_UW_MASTER where uw_source_type = '4' and uw_id = y.UW_ID and uw_esca_indi =2)) uw_lower_comments,
       NOT_INFORM_SITUATION inform_situation,
       CASE_NAME clm_mark
 		 from DEV_UW.T_UW_POLICY y
 		 left join DEV_UW.T_UW_OTHER_INFO uoi on
 		 	uoi.uw_id = y.uw_id
 		 	and uoi.policy_code = y.policy_code
 		 	and uoi.UW_SOURCE_TYPE = '4' 		 
 		  where  y.uw_id = ${uw_id} ]]>
		<![CDATA[order by y.policy_code]]>
	</select>
	
	<!--Modify by xuhp 保全核保查询机构编码、机构名称、代理人信息等字段  2015年12月9日-->
	<select id="findAcceptPolicyOrganInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.apply_code,a.uw_source_type,a.uw_id,
  			     a.organ_code,
       			 (select organ_name from DEV_PAS.T_UDMP_ORG_REL where organ_code = A.ORGAN_CODE) organ_name,
       			 (select agent_name||' '||agent_code from DEV_UW.T_CONTRACT_AGENT where UW_ID = A.UW_ID and rownum = 1) agent_name_code,
       			 (select service_code from DEV_UW.T_UW_MASTER where uw_id = a.uw_id) service_code,a.policy_code,a.apply_code,to_char(a.policy_id) policy_id,a.uw_status,
                 (select biz_code from DEV_UW.T_UW_MASTER where uw_id = a.uw_id) biz_code,
              	 (select to_char(uw_decision) from DEV_UW.T_UW_MASTER where uw_id = a.uw_id) uw_decision,a.policy_decision,
              	 (select Service_Bank from DEV_UW.T_CONTRACT_MASTER where uw_id = a.uw_id and apply_code = a.apply_code) service_bank,uw_finish_time
  				 from DEV_UW.T_UW_POLICY a where uw_id = ${uw_id} and rownum ='1']]>
	</select>
	
	<!--Modify by xuhp 再保管理 触发再保规则时查询险种编码和被保险人信息 2016年3月3日 -->
	<select id="findBusiprdidAndInsuredListInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select to_char(busi_prd_id) busi_prd_id, case when p.policy_code is null then (select to_char(wm_concat(customer_id)) from DEV_UW.T_INSURED_LIST where list_id =(select insured_id from DEV_UW.T_BENEFIT_INSURED where uw_id = p.uw_id and busi_item_id = p.busi_item_id))
				 else  (select to_char(wm_concat(customer_id)) from DEV_UW.T_INSURED_LIST where list_id =(select insured_id from DEV_UW.T_BENEFIT_INSURED where uw_id = p.uw_id and busi_item_id = p.busi_item_id and policy_code = p.policy_code) and uw_id = p.uw_id) end customer_id,p.apply_code,to_char(p.policy_id) policy_id,
					    p.busi_prod_code,d.item_id,d.product_code,d.initial_type,d.expiry_date,d.charge_year,d.charge_period,d.benefit_level,d.amount,d.validate_date,d.unit,d.std_prem_af
			  	 from DEV_UW.T_CONTRACT_BUSI_PROD p,DEV_UW.T_CONTRACT_PRODUCT d  where p.uw_id = d.uw_id and p.busi_item_id = d.busi_item_id 
   				 and p.uw_id = ${uw_id} and p.busi_item_id = ${busi_item_id} ]]>
   		<include refid="queryZBBusiAndProductList" />		 
	</select>
	
	<!-- Modify by xuhp 再保核保提交核保结论规则校验 2016年3月29日 -->
	<select id="findEMValueAndLimitAmountInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select (select max(em_value) from DEV_UW.T_UW_EXTRA_PREM where uw_prd_id = ${uw_prd_id}) em_value,(select limit_amount from DEV_UW.T_UW_LIMIT  where uw_prd_id = ${uw_prd_id}) limit_amount from DUAL
   		]]>
	</select>
	
	<!--Modify by xuhp 累计风险保额重新计算，查询险种和责任组信息  2016年4月12日 -->
	<select id="findBusiAndProductListInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select
		(select decision_code from DEV_UW.T_UW_PRODUCT where uw_id =d.uw_id and busi_item_id =d.busi_item_id and item_id = d.item_id) uw_decision, 
		(select customer_id from DEV_UW.T_POLICY_HOLDER where uw_id = p.uw_id and apply_code = p.apply_code) holder_customer_id,
		(select st.customer_id from DEV_UW.T_INSURED_LIST st,DEV_UW.T_BENEFIT_INSURED bs where st.uw_id = bs.uw_id and st.policy_id = bs.policy_id and st.list_id = bs.insured_id and bs.order_id = '1' and st.uw_id = p.uw_id  and st.policy_id = p.policy_id and rownum = 1) insured_customer_id,
        (select insured_age from DEV_UW.T_INSURED_LIST st,DEV_UW.T_BENEFIT_INSURED bs where st.uw_id = bs.uw_id and st.policy_id = bs.policy_id and st.list_id = bs.insured_id and bs.order_id = '1' and st.uw_id = p.uw_id  and st.policy_id = p.policy_id and rownum = 1) insured_age,
	   p.apply_code,
       p.policy_code,
       to_char(p.policy_id) policy_id,
       to_char(p.busi_item_id) busi_item_id,
       to_char(p.busi_prod_code) busi_prod_code,
       d.item_id,
       d.product_code,
       d.initial_type,
       pd.charge_year,
       pd.charge_period,
       pd.coverage_period,
       pd.coverage_year,
       pd.benefit_level,
       (select amount from DEV_UW.T_UW_LIMIT where UW_ID = p.uw_id and busi_item_id =p.busi_item_id and item_id = d.item_id) amount,
       d.validate_date,
       pd.unit,
       (select std_prem from DEV_UW.T_UW_LIMIT where UW_ID = p.uw_id and busi_item_id =p.busi_item_id and item_id = d.item_id) std_prem_af,
       d.liability_state,
       pd.total_prem_af,pd.uw_id
  from DEV_UW.T_CONTRACT_BUSI_PROD p, DEV_UW.T_CONTRACT_PRODUCT d,DEV_UW.T_UW_PRODUCT pd
 where p.uw_id = d.uw_id
   and d.uw_id = pd.uw_id
   and p.busi_item_id = d.busi_item_id 
   and p.busi_item_id = pd.busi_item_id
   and d.item_id = pd.item_id
   and p.apply_code = d.apply_code
   and d.apply_code = pd.apply_code]]>
   <include refid="queryBusiAndProductListByUwID" />
   <include refid="queryBusiAndProductListByPolicyCodew" />
   <include refid="queryBusiAndProductListByApplyCode" />
   <include refid="queryBusiAndProductListByBusiItemId" />
   </select>
	
	<sql id="queryBusiAndProductListByUwID">
		<if test=" uw_id  != null "><![CDATA[ AND p.UW_ID = #{uw_id} ]]></if>
	</sql>	

	<sql id="queryBusiAndProductListByPolicyCodew">
		<if test=" policy_code  != null "><![CDATA[ AND p.policy_code = #{policy_code} ]]></if>
	</sql>
	
	<sql id="queryZBBusiAndProductList">
		<if test=" policy_code  != null "><![CDATA[ and p.policy_code = d.policy_code AND p.policy_code = #{policy_code} ]]></if>
	</sql>
	
	<sql id="queryBusiAndProductListByApplyCode">
		<if test=" apply_code  != null "><![CDATA[ AND p.apply_code = #{apply_code} ]]></if>
	</sql>
	
	<sql id="queryUWCorrectionByOrganCode">
		<if test=" organ_code  != null "><![CDATA[ AND p.organ_code like #{organ_code}||'%' ]]></if>
	</sql>
	
	<sql id="queryUWCorrectionByUwUserId">
		<if test=" uw_user_id  != null "><![CDATA[ AND m.uw_user_id = #{uw_user_id} ]]></if>
	</sql>
	
	<sql id="queryUWCorrectionByUwLevelCode">
		<if test=" uw_level_code  != null "><![CDATA[ AND p.uw_level_code = #{uw_level_code} ]]></if>
	</sql>
	
	<sql id="queryUWCorrectionByPolicyDecision">
		<if test=" policy_decision  != null "><![CDATA[ AND p.policy_decision = #{policy_decision} ]]></if>
	</sql>
	
	<sql id="queryUWCorrectionByStartAndEndTime">
		<if test=" start_time  != null and end_time != null"><![CDATA[ and m.insert_time between to_date(#{start_time},'yyyy-mm-dd') and to_date(#{end_time},'yyyy-mm-dd') ]]></if>
	</sql>
	
	<sql id="queryUWCorrectionByPhysicalFlag">
		<if test=" physical_flag  != null "><![CDATA[ and exists (select 1 from DEV_UW.T_PENOTICE where uw_id = p.uw_id) ]]></if>
	</sql>
	
	<sql id="queryUWCorrectionBySurveyFlag">
		<if test=" survey_flag  != null "><![CDATA[ and exists (select 1 from DEV_UW.T_SURVEY_APPLY where apply_code = p.apply_code) ]]></if>
	</sql>
	
	<sql id="queryUWCorrectionByAgentCode">
		<if test=" agent_name_code  != null "><![CDATA[ and exists (select 1 from DEV_UW.T_CONTRACT_AGENT where uw_id = p.uw_id and agent_code = #{agent_name_code})]]></if>
	</sql>
	
	<sql id="queryBusiAndProductListByBusiItemId">
		<if test=" busi_item_id  != null "><![CDATA[ AND p.busi_item_id = #{busi_item_id} ]]></if>
	</sql>
	
	<!-- Modify by xuhp 保全核保删除加费原因时查询责任层、险种层、保单层核保决定是否全部提交核保结论 2016年4月21日 -->
	<select id="findProductBusiPolicyDecisionInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select (select '1' from DEV_UW.T_UW_PRODUCT where uw_id = #{uw_id} and policy_code = #{policy_code} and uw_prd_id =#{uw_prd_id} and decision_code is not null) product_decision, 
	(select '1' from DEV_UW.T_UW_BUSI_PROD where uw_id = #{uw_id} and policy_code = #{policy_code} and uw_busi_id = #{uw_busi_id} and decision_code is not null) busi_decision,
	(select '1' from DEV_UW.T_UW_POLICY where  uw_id = #{uw_id}  and policy_code = #{policy_code} and policy_decision is not null) policy_decision from DUAL ]]>
	</select>
	
	<sql id="querySubRiskElementByCoverageYear">
		<if test=" coverage_year  != null "><![CDATA[ 
		,(select code2 from DEV_UW.T_PAGECFG_ELEMENT_VALUE where  list_value_code = (select g.list_value from DEV_NB.T_PAGECFG_PRD_CATE_RELA c inner join DEV_NB.T_PAGECFG_PRD_ELEMENT g on c.relation_id = g.relation_id where c.product_id = #{product_id} and g.unit_code in('044','006')) and code = ''||#{coverage_year}||'') coverage_year_name
		]]></if>
	</sql>
	
	<sql id="querySubRiskElementByChargeYear">
		<if test=" charge_year  != null "><![CDATA[ 
		,(select code2 from DEV_UW.T_PAGECFG_ELEMENT_VALUE where list_value_code = (select g.list_value from DEV_NB.T_PAGECFG_PRD_CATE_RELA c inner join DEV_NB.T_PAGECFG_PRD_ELEMENT g on c.relation_id = g.relation_id where c.product_id = #{product_id} and g.unit_code = '024') and code = ''||#{charge_year}||'') charge_year_name
		]]></if>
	</sql>
	
	<!-- Modify by xuhp 新契约核保、保全核保下发限额核保结论时查询该责任组的交费期间标志、交费期间、保险期间标志、保险期间的下拉选择值是同主险还是自定义 2016年6月2日 -->
	<select id="findSubRiskElementInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select (select to_char(e.default_value_origin) from DEV_UW.T_PAGECFG_PRD_ELEMENT e,DEV_UW.T_PAGECFG_PRD_CATE_RELA  r where e.relation_id = r.relation_id
 					     and unit_code in (select unit_code from DEV_UW.T_PAGECFG_UNIT where trim(unit_name) = '交费期间标志') and r.product_id = #{product_id}) charge_period,
   						(select e.default_value_origin from DEV_UW.T_PAGECFG_PRD_ELEMENT e,DEV_UW.T_PAGECFG_PRD_CATE_RELA  r where e.relation_id = r.relation_id
 						 and unit_code in (select unit_code from DEV_UW.T_PAGECFG_UNIT where trim(unit_name) = '交费期间') and r.product_id = #{product_id} and rownum = 1) charge_year,
 						(select to_char(e.default_value_origin) from DEV_UW.T_PAGECFG_PRD_ELEMENT e,DEV_UW.T_PAGECFG_PRD_CATE_RELA  r where e.relation_id = r.relation_id
 						 and unit_code in (select unit_code from DEV_UW.T_PAGECFG_UNIT where trim(unit_name) = '保险期间标志') and r.product_id = #{product_id}) coverage_period,
 						(select e.default_value_origin from DEV_UW.T_PAGECFG_PRD_ELEMENT e,DEV_UW.T_PAGECFG_PRD_CATE_RELA  r where e.relation_id = r.relation_id
 						 and unit_code in (select unit_code from DEV_UW.T_PAGECFG_UNIT where trim(unit_name) = '保险期间') and r.product_id = #{product_id}  and rownum = 1) coverage_year,
 						 (select cp.product_id from DEV_UW.T_CONTRACT_BUSI_PROD cb,DEV_UW.T_CONTRACT_PRODUCT cp where cb.uw_id = cp.uw_id and cb.busi_item_id = cp.busi_item_id and cb.master_busi_item_id is null and cb.uw_id = #{uw_id} and rownum=1) product_id ]]>
		 <include refid="querySubRiskElementByCoverageYear" />
		 <include refid="querySubRiskElementByChargeYear" /> 						  						  
		<![CDATA[ from DUAL ]]>
	</select>
	
	<select id="findSubRiskCoverageYearActionInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT distinct to_char(target_prd_element_list_value) coverage_year_action
  				 FROM DEV_UW.T_PAGECFG_PRD_ELEMENT_ACTION WHERE PRD_ELEMENT_ID in
                 (SELECT PRD_ELEMENT_ID FROM DEV_UW.T_PAGECFG_PRD_ELEMENT WHERE RELATION_ID IN (SELECT RELATION_ID
                   FROM DEV_UW.T_PAGECFG_PRD_CATE_RELA WHERE PRODUCT_ID = #{product_id})
           		   AND UNIT_CODE IN (SELECT UNIT_CODE FROM DEV_UW.T_PAGECFG_UNIT WHERE LABEL_NAME = '保险期间标志')) ]]>
	</select>
	
	<!-- Modify by xuhp 当保险期间标志为配置动作响应时单独查询此动作对应的名称 2016年8月31日 -->
	<select id="findSubRiskCoverageYearNameInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select code2 coverage_year_name from DEV_UW.T_PAGECFG_ELEMENT_VALUE where list_value_code in (SELECT distinct to_char(target_prd_element_list_value) coverage_year_action
  				 FROM DEV_UW.T_PAGECFG_PRD_ELEMENT_ACTION WHERE PRD_ELEMENT_ID in
                 (SELECT PRD_ELEMENT_ID FROM DEV_UW.T_PAGECFG_PRD_ELEMENT WHERE RELATION_ID IN (SELECT RELATION_ID
                   FROM DEV_UW.T_PAGECFG_PRD_CATE_RELA WHERE PRODUCT_ID = #{product_id})
           		   AND UNIT_CODE IN (SELECT UNIT_CODE FROM DEV_UW.T_PAGECFG_UNIT WHERE LABEL_NAME = '保险期间标志'))) and code = ''||#{coverage_year}||'' and rownum=1 ]]>
	</select>
	
	<!-- Modify by xuhp 查询新契约核保、保全核保、理赔核保需要产生的核保结论通知书批次号 2016年6月6日 -->
	<select id="findUWNoticeBatchNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select to_char(DEV_UW.S_t_uw_notice.nextval) uw_notice_bn from DUAL ]]>
	</select>
	
	<select id="findNoticeSequences" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select DEV_UW.S_t_uw_notice.nextval uw_id from DUAL ]]>
	</select>
	
	<!-- Modify by xuhp 续保核保下发不自动续保通知书查询险种信息 2016年7月4日15:23:53 -->
	<select id="findReBusiProductInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select d.policy_code,p.expiry_date,(select PRODUCT_NAME_SYS from DEV_UW.T_BUSINESS_PRODUCT where BUSINESS_PRD_ID = p.busi_prd_id) organ_name,
               (select distinct l.customer_id from DEV_UW.T_INSURED_LIST l,DEV_UW.T_BENEFIT_INSURED b where l.uw_id = b.uw_id and l.policy_code = b.policy_code
				and b.order_id = '1' and l.uw_id = d.uw_id and l.policy_code =d.policy_code and rownum = 1) insured_customer_id
 			 from DEV_UW.T_UW_BUSI_PROD d,DEV_UW.T_CONTRACT_BUSI_PROD p where d.uw_id = p.uw_id and d.policy_code= p.policy_code
			 and d.busi_item_id = p.busi_item_id and d.uw_id = #{uw_id} and d.policy_code = #{policy_code} and d.renew_decision = '0' ]]>
	</select>
	
	<!-- Modify by xuhp 查询险种id 2016年7月23日 -->
	<select id="findContractBusiProdInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select to_char(busi_prd_id) busi_prd_id from DEV_UW.T_CONTRACT_BUSI_PROD  where uw_id = #{uw_id} ]]>
	</select>
	
	<!-- Modify by xuhp 查询满足条件的核保订正任务 2016年8月2日 -->
	<select id="findUWCorrectionInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select a.apply_code, a.organ_code, a.level_desc, a.policy_decision,
       				a.physical_flag, a.survey_flag, a.uw_finish_time, a.user_name, a.uw_id
  		 from (select p.apply_code, p.organ_code, ul.level_desc, pd.decision_desc policy_decision,
               case when pe.PENOTICE_ID is not null then  '是' else '否' end physical_flag,
               case when sa.APPLY_ID is not null then  '是' else '否' end survey_flag,
               m.uw_finish_time, uu.user_name, m.uw_id
          from DEV_UW.T_UW_MASTER       m,
               DEV_UW.T_UW_POLICY       p,
               DEV_PAS.T_UDMP_USER       uu,
               DEV_UW.T_UW_LEVEL        ul,
               DEV_UW.T_POLICY_DECISION pd,
               DEV_UW.T_PENOTICE        pe,
               DEV_UW.T_SURVEY_APPLY    sa
         where m.uw_id = p.uw_id
           and m.biz_code = p.apply_code
           and m.uw_source_type = p.uw_source_type
           and p.uw_source_type = '1'
           and m.uw_status = '04'
           and uu.user_id(+) = m.uw_user_id
           and ul.level_code(+) = p.uw_level_code
           and pd.decision_code(+) = p.policy_decision
           and pe.uw_id(+) = p.uw_id
           and sa.apply_code(+) = p.apply_code
           ]]>
         <include refid="queryBusiAndProductListByApplyCode" /> <!-- 投保单号 -->
		 <include refid="queryUWCorrectionByOrganCode" />  <!-- 所属机构 -->
		 <include refid="queryUWCorrectionByUwUserId" />  <!-- 核保员 -->
		 <include refid="queryUWCorrectionByUwLevelCode" />  <!-- 保单核保等级 -->
		 <include refid="queryUWCorrectionByPolicyDecision" />  <!-- 核保结论 -->
		 <include refid="queryUWCorrectionByStartAndEndTime" />  <!-- 起止日期 -->
		 <include refid="queryUWCorrectionByPhysicalFlag" />  <!-- 是否体检 -->
		 <include refid="queryUWCorrectionBySurveyFlag" />  <!-- 是否生调 -->
		 <include refid="queryUWCorrectionByAgentCode" />  <!-- 业务员号码 -->
          <![CDATA[  ) a group by a.apply_code, a.organ_code, a.level_desc, a.policy_decision,
          a.physical_flag, a.survey_flag, a.uw_finish_time, a.user_name, a.uw_id ]]>
	</select>
	
	<!-- Modify by xuhp 理赔二核核销后回调核保，返回二核核销结论，查询赔案下保单信息 2016年8月5日 -->
	<select id="findClmCancelPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select 
 				(select apply_code from DEV_UW.T_UW_POLICY where uw_id = m.uw_id) apply_code,
  				(select policy_code from DEV_UW.T_UW_POLICY where uw_id = m.uw_id) policy_code,
    			(select policy_decision from DEV_UW.T_UW_POLICY where uw_id = m.uw_id) policy_decision,m.uw_id
 				from DEV_UW.T_UW_MASTER m where m.biz_code = #{biz_code} ]]>
	</select>
	
	<!-- Modify by xuhp 核保完成后需根据不同的核保决定进行风险累计的计算 2016年8月9日 -->
	<select id="findRiskAmountProductInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select p.apply_code,p.policy_code,p.decision_code policy_decision,to_char(p.busi_item_id) busi_item_id,p.busi_prod_code,p.uw_prd_id,
				  nvl((select '1' from DEV_UW.T_UW_LIMIT where uw_prd_id = p.uw_prd_id),'0') survey_flag
 				  from DEV_UW.T_UW_PRODUCT p where uw_id = #{uw_id} ]]>
	</select>
	
	<!-- Modify by xuhp 分配用户下角色组时查询用户信息 2016年8月12日 -->
	<select id="findUserInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select user_name from DEV_PAS.T_UDMP_USER where user_id = #{uw_user_id} ]]>
	</select>
	
    <!-- Modify by xuhp 保全核保、理赔核保初始化下发核保结论页面时查询新契约下发的特约及限额结论的责任组id 2016年9月8日 -->
	<select id="findNbDecisionPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select (select uw_prd_id from DEV_UW.t_uw_product where uw_busi_id = d.uw_busi_id) uw_prd_id,y.uw_id
		  from DEV_UW.t_uw_busi_prod d, DEV_UW.t_uw_policy y where y.uw_id = d.uw_id and y.uw_source_type = '1'
		  and y.apply_code = #{apply_code} and d.busi_prod_code = #{busi_prod_code} and rownum=1
		 ]]>
	</select>

	<!-- Modify by xuhp 保全核保加费时新增附加险开始日期显示为下期生效日，增补告知开始日期显示为追溯生效，其他保全项目显示为保全生效日 2016年9月19日 -->
	<select id="findExtraPremOtherDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select o.field1,o.field2 from DEV_UW.t_contract_product_other o where  uw_id = #{uw_id} 
		         and o.busi_item_id = #{busi_item_id} and o.item_id = #{item_id} and rownum=1 
		 ]]>
	</select>
			
	<!-- Modify by xuhp 续保核保下发核保结论页面需显示赔案信息，查询续保保单赔案号 2016年10月9日 -->
	<select id="findRePolicyClmInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select endorse_code field7 from DEV_UW.t_uw_policy  where  uw_id = #{uw_id} and rownum=1 ]]>
	</select>
	
	<!-- Modify by xuhp 新契约人工核保初始化个人池任务时在后台使用sql统一查询出个人池中保单的所属机构、核保等级、核保状态、销售渠道、业务员等字段的中文含义，不在页面加载得迭代查询 2016年12月1日 -->
	<select id="findNbPersonalPoolTaskInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select uw_id,(select ORGAN_NAME from DEV_PAS.T_UDMP_ORG where  ORGAN_CODE = y.organ_code) organ_name, 
				 (select LEVEL_DESC from DEV_UW.T_UW_LEVEL where LEVEL_CODE= y.uw_level_code) level_desc,
				 (select (select status_detail_name from DEV_UW.T_UW_STATUS_DETAIL where status_detail_code=uw_status_detail) from DEV_UW.T_UW_MASTER where uw_id = y.UW_ID) uw_status,
				 (select SALES_CHANNEL_NAME from DEV_UW.T_SALES_CHANNEL where SALES_CHANNEL_CODE=(select channel_type from DEV_UW.t_contract_master where uw_id = y.UW_ID )) field1,
				 (select agent_name from DEV_UW.t_contract_agent where uw_id = y.UW_ID and rownum=1) field2
 			from DEV_UW.t_uw_policy y where uw_id in (${field3})]]>
	</select>
	
	<!-- Modify by xuhp 查询Magnum告知的客户号和险种责任组信息 2016年12月12日 -->
	<select id="findMagnumOtherInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select (select customer_id from DEV_UW.t_customer_survey_uw_copy where apply_code=#{apply_code} and rownum=1) holder_customer_id,
                  (select d.product_name_sys from DEV_UW.t_business_product d where d.product_code_sys = #{busi_item_id}) field1
                from dual ]]>
	</select>
	
	<!-- Modify by xuhp 获取超权限上报校验参数 em值、是否为拒保核保结论、是否为延期核保结论 2016年12月19日 -->
	<select id="findSuperPowerParameterInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select (select max(em_value) from DEV_UW.T_UW_EXTRA_PREM where uw_id = #{uw_id}) em_value,
				 (select '1' from DEV_UW.t_uw_product where uw_id = #{uw_id} and decision_code = '40' and rownum =1) field1,
				 (select '1' from DEV_UW.t_uw_product where uw_id = #{uw_id} and decision_code = '50' and rownum =1) field2
 				 from dual
   		]]>
	</select>
	<!-- Modify by gubo_wb 查询客户的新契约或理赔的既往拒保延期承保史和既往二核延期拒保结论 2016-12-29 -->
	<select id="findCusConOrHistory" resultType="java.util.Map" parameterType="java.util.Map">
  	 <![CDATA[SELECT DISTINCT to_char(C.BUSI_PRD_ID) as BUSI_PRD_ID
  	  from DEV_UW.T_UW_PRODUCT T, DEV_UW.T_UW_POLICY Y,DEV_UW.T_INSURED_LIST I,DEV_UW.T_CONTRACT_BUSI_PROD C
      	WHERE  Y.UW_ID = T.UW_ID
        AND T.UW_ID = I.UW_ID
        AND T.UW_ID = C.UW_ID 
        AND Y.UW_SOURCE_TYPE = #{field1}]]>
        <if test=" busi_prod_code  != null "><![CDATA[ AND T.BUSI_PROD_CODE NOT IN (${busi_prod_code})]]></if>
        AND I.CUSTOMER_ID = #{customer_id}
        AND T.DECISION_CODE IN ('40', '50')
  	
	</select>
	
	<select id="findStatuMsgByUWStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.UW_STATUS AS FIELD1,A.STATUS_NAME AS FIELD2 FROM DEV_UW.T_UW_STATUS A
		WHERE 1=1 AND A.UW_STATUS in (${field1})]]>
	</select>
	
	<!-- Modify by xuhp 保全核保新增附加险推算保单层核保结论时，只需要根据新增的附加险推算结论，原险种不参与推算 TC:18907 2017年3月1日 -->
	<select id="findNSProductDecitionCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T.DECISION_CODE PRODUCT_DECISION,T.PRODUCT_CODE FROM DEV_UW.T_UW_PRODUCT T,DEV_UW.T_UW_MASTER M WHERE  
 				M.UW_ID = T.UW_ID AND M.SERVICE_CODE = 'NS' AND T.DECISION_INDI = '0' AND M.UW_ID = #{uw_id} ]]>
	</select>
</mapper>
