<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="CLM_CliamRiskEventBroadPO">
	
	<select id="queryCliamRiskEventBroadListForPageTotalForCertiCode"  resultType="java.lang.Integer" parameterType="java.util.Map" >
		<![CDATA[ SELECT COUNT(1)  FROM (
		SELECT ROWNUM rowNumber,
       (CASE WHEN  OC.CUSTOMER_NAME IS NOT NULL THEN OC.CUSTOMER_NAME
          ELSE OC.DST_NAME END)CUSTOMER_NAME,
       (CASE WHEN OC.TYPE IS NOT NULL THEN OC.TYPE 
          ELSE OC.DST_CERT_TYPE END )Customer_Cert_Type,
       (CASE WHEN  OC.CUSTOMER_CERTI_CODE IS NOT NULL THEN OC.CUSTOMER_CERTI_CODE
          ELSE OC.DST_CERTI_CODE END)CUSTOMER_CERTI_CODE,
       OC.Scene_Name,
       OC.Label_Name,
       OC.Appkey,
       OC.Policy_Code,
       cm.branch_code as second_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = cm.branch_code) as second_organ_name,
       substr(cm.Organ_Code, 0, 6) as third_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = substr(cm.Organ_Code, 0, 6)) as third_organ_name,
       cm.Organ_Code as fouth_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = cm.Organ_Code) as fouth_organ_name,
       OC.Receive_Time,                                                                                                   
        (SELECT distinct MAX(A.SEND_TIME) FROM  DEV_CLM.T_CLAIM_RISKCASE_EMAIL_LOG A  WHERE A.POLICY_CODE = OC.POLICY_CODE AND to_date(to_char(a.SEND_TIME,'yyyy-MM-dd'),'yyyy-MM-dd') = to_date(to_char(OC.RECEIVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')  )  as send_time
  FROM (select distinct cc.*,az.TYPE
          from (select OC.DST_NAME, OC.DST_CERT_TYPE, OC.DST_CERTI_CODE,CT.TYPE
                  FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER OC,
                       DEV_PAS.T_CONTRACT_MASTER     CM,
                       DEV_CLM.t_Certi_Type CT
                 WHERE 1 = 1
                   and OC.Policy_Code = CM.policy_code
                   AND CT.CODE = OC.Customer_Cert_Type
                   and OC.CUSTOMER_ID in
                       (SELECT OC1.CUSTOMER_ID
                          FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER OC1,
                               dev_pas.T_CONTRACT_MASTER     CM1
                         WHERE 1 = 1
                           and OC1.POLICY_CODE = CM1.POLICY_CODE]]>
        <if test=" ser_Certi_No != null and ser_Certi_No != ''  "><![CDATA[ AND OC1.CUSTOMER_CERTI_CODE =(#{ser_Certi_No}) ]]></if>
		<if test=" start_Receive_Time  != null "><![CDATA[ AND TRUNC(OC1.Receive_Time)>=to_date(to_char(#{start_Receive_Time},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>
		<if test=" end_Receive_Time  != null "><![CDATA[ AND TRUNC(OC1.Receive_Time)<=to_date(to_char(#{end_Receive_Time},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
          <![CDATA[                 )) az
         INNER JOIN DEV_CLM.T_IHI_CLMBRO_CUSTOMER cc
            ON cc.DST_NAME = az.DST_NAME
           AND cc.DST_CERT_TYPE = az.DST_CERT_TYPE
           AND cc.DST_CERTI_CODE = az.DST_CERTI_CODE) OC
        LEFT JOIN 
       DEV_PAS.T_CONTRACT_MASTER CM
       ON OC.POLICY_CODE = CM.POLICY_CODE)
		]]>
	</select>
	<select id="queryCliamRiskEventBroadListForPageForCertiCode"  resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[ SELECT ROWNUM rowNumber,
       (CASE WHEN  OC.CUSTOMER_NAME IS NOT NULL THEN OC.CUSTOMER_NAME
          ELSE OC.DST_NAME END)CUSTOMER_NAME,
       (CASE WHEN OC.TYPE IS NOT NULL THEN OC.TYPE 
          ELSE OC.DST_CERT_TYPE END )Customer_Cert_Type,
       (CASE WHEN  OC.CUSTOMER_CERTI_CODE IS NOT NULL THEN OC.CUSTOMER_CERTI_CODE
          ELSE OC.DST_CERTI_CODE END)CUSTOMER_CERTI_CODE,
       OC.Scene_Name,
       OC.Label_Name,
       OC.Appkey,
       OC.Policy_Code,
       cm.branch_code as second_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = cm.branch_code) as second_organ_name,
       substr(cm.Organ_Code, 0, 6) as third_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = substr(cm.Organ_Code, 0, 6)) as third_organ_name,
       cm.Organ_Code as fouth_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = cm.Organ_Code) as fouth_organ_name,
       OC.Receive_Time,                                                                                                   
        (SELECT distinct MAX(A.SEND_TIME) FROM  DEV_CLM.T_CLAIM_RISKCASE_EMAIL_LOG A  WHERE A.POLICY_CODE = OC.POLICY_CODE AND to_date(to_char(a.SEND_TIME,'yyyy-MM-dd'),'yyyy-MM-dd') = to_date(to_char(OC.RECEIVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')  )  as send_time
  FROM (select distinct cc.*,az.TYPE
          from (select OC.DST_NAME, OC.DST_CERT_TYPE, OC.DST_CERTI_CODE,CT.TYPE
                  FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER OC,
                       DEV_PAS.T_CONTRACT_MASTER     CM,
                       DEV_CLM.t_Certi_Type CT
                 WHERE 1 = 1
                   and OC.Policy_Code = CM.policy_code
                   and CT.CODE = OC.Customer_Cert_Type
                   and OC.CUSTOMER_ID in
                       (SELECT OC1.CUSTOMER_ID
                          FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER OC1,
                               dev_pas.T_CONTRACT_MASTER     CM1
                         WHERE 1 = 1
                           and OC1.POLICY_CODE = CM1.POLICY_CODE ]]>
        <if test=" ser_Certi_No != null and ser_Certi_No != ''  "><![CDATA[ AND OC1.CUSTOMER_CERTI_CODE =(#{ser_Certi_No}) ]]></if>
		<if test=" start_Receive_Time  != null "><![CDATA[ AND TRUNC(OC1.Receive_Time)>=to_date(to_char(#{start_Receive_Time},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>
		<if test=" end_Receive_Time  != null "><![CDATA[ AND TRUNC(OC1.Receive_Time)<=to_date(to_char(#{end_Receive_Time},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>                   
                           	
		<![CDATA[   )) az
         INNER JOIN DEV_CLM.T_IHI_CLMBRO_CUSTOMER cc
            ON cc.DST_NAME = az.DST_NAME
           AND cc.DST_CERT_TYPE = az.DST_CERT_TYPE
           AND cc.DST_CERTI_CODE = az.DST_CERTI_CODE) OC
        LEFT JOIN 
        DEV_PAS.T_CONTRACT_MASTER CM
        ON OC.POLICY_CODE = CM.POLICY_CODE
        AND ROWNUM <= #{LESS_NUM} AND ROWNUM> #{GREATER_NUM}
		ORDER BY OC.CUSTOMER_NAME,OC.CUSTOMER_CERTI_CODE,OC.Scene_Name
		]]>
	</select>
	
	
	<select id="queryCliamRiskEventBroadListForPageTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >
		<![CDATA[ SELECT COUNT(1)FROM 
		 (SELECT ROWNUM rowNumber,
       (CASE WHEN  OC.CUSTOMER_NAME IS NOT NULL THEN OC.CUSTOMER_NAME
          ELSE OC.DST_NAME END)CUSTOMER_NAME,
       (CASE WHEN OC.TYPE IS NOT NULL THEN OC.TYPE 
          ELSE OC.DST_CERT_TYPE END )Customer_Cert_Type,
       (CASE WHEN  OC.CUSTOMER_CERTI_CODE IS NOT NULL THEN OC.CUSTOMER_CERTI_CODE
          ELSE OC.DST_CERTI_CODE END)CUSTOMER_CERTI_CODE,
       OC.Scene_Name,
       OC.Label_Name,
       OC.Appkey,
       OC.Policy_Code,
       cm.branch_code as second_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = cm.branch_code) as second_organ_name,
       substr(cm.Organ_Code, 0, 6) as third_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = substr(cm.Organ_Code, 0, 6)) as third_organ_name,
       cm.Organ_Code as fouth_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = cm.Organ_Code) as fouth_organ_name,
       OC.Receive_Time,                                                                                                   
        (SELECT distinct MAX(A.SEND_TIME) FROM  DEV_CLM.T_CLAIM_RISKCASE_EMAIL_LOG A  WHERE A.POLICY_CODE = OC.POLICY_CODE AND to_date(to_char(a.SEND_TIME,'yyyy-MM-dd'),'yyyy-MM-dd') = to_date(to_char(OC.RECEIVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')  )  as send_time
  FROM (select distinct cc.*,az.TYPE
          from (select OC.DST_NAME, OC.DST_CERT_TYPE, OC.DST_CERTI_CODE,CT.TYPE
                  FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER OC,
                       DEV_PAS.T_CONTRACT_MASTER     CM,
                       DEV_CLM.t_Certi_Type CT
                 WHERE 1 = 1
                   and OC.Policy_Code = CM.policy_code
                   and CT.CODE = OC.Customer_Cert_Type
                   and OC.CUSTOMER_ID in
                       (SELECT OC1.CUSTOMER_ID
                          FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER OC1,
                               dev_pas.T_CONTRACT_MASTER     CM1
                         WHERE 1 = 1
                           and OC1.POLICY_CODE = CM1.POLICY_CODE ]]>
        <if test=" ser_Certi_No != null and ser_Certi_No != ''  "><![CDATA[ AND OC1.CUSTOMER_CERTI_CODE =(#{ser_Certi_No} ]]></if>
		<if test=" start_Receive_Time  != null "><![CDATA[ AND TRUNC(OC1.Receive_Time)>=to_date(to_char(#{start_Receive_Time},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>
		<if test=" end_Receive_Time  != null "><![CDATA[ AND TRUNC(OC1.Receive_Time)<=to_date(to_char(#{end_Receive_Time},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
		<if test=" ser_Second_Organ  != null "><![CDATA[ AND TRUNC(CM1.ORGAN_CODE)like(#{ser_Second_Organ}||'%')]]></if>
		<if test=" ser_Third_Organ  != null "><![CDATA[ AND TRUNC(CM1.ORGAN_CODE)like(#{ser_Third_Organ}||'%' )]]></if>
                           
         <![CDATA[  )) az
         INNER JOIN DEV_CLM.T_IHI_CLMBRO_CUSTOMER cc
            ON cc.DST_NAME = az.DST_NAME
           AND cc.DST_CERT_TYPE = az.DST_CERT_TYPE
           AND cc.DST_CERTI_CODE = az.DST_CERTI_CODE) OC
        LEFT JOIN 
       DEV_PAS.T_CONTRACT_MASTER CM
       ON OC.POLICY_CODE = CM.POLICY_CODE)
		]]>
	</select>
	<select id="queryCliamRiskEventBroadListForPage"  resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[SELECT ROWNUM rowNumber,
       (CASE WHEN  OC.CUSTOMER_NAME IS NOT NULL THEN OC.CUSTOMER_NAME
          ELSE OC.DST_NAME END)CUSTOMER_NAME,
       (CASE WHEN OC.TYPE IS NOT NULL THEN OC.TYPE 
          ELSE OC.DST_CERT_TYPE END )Customer_Cert_Type,
       (CASE WHEN  OC.CUSTOMER_CERTI_CODE IS NOT NULL THEN OC.CUSTOMER_CERTI_CODE
          ELSE OC.DST_CERTI_CODE END)CUSTOMER_CERTI_CODE,
       OC.Scene_Name,
       OC.Label_Name,
       OC.Appkey,
       OC.Policy_Code,
       cm.branch_code as second_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = cm.branch_code) as second_organ_name,
       substr(cm.Organ_Code, 0, 6) as third_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = substr(cm.Organ_Code, 0, 6)) as third_organ_name,
       cm.Organ_Code as fouth_organ,
       (SELECT ORG.Organ_Name
          FROM DEV_CLM.T_UDMP_ORG ORG
         WHERE ORG.Organ_Code = cm.Organ_Code) as fouth_organ_name,
       OC.Receive_Time,                                                                                                   
        (SELECT distinct MAX(A.SEND_TIME) FROM  DEV_CLM.T_CLAIM_RISKCASE_EMAIL_LOG A  WHERE A.POLICY_CODE = OC.POLICY_CODE AND to_date(to_char(a.SEND_TIME,'yyyy-MM-dd'),'yyyy-MM-dd') = to_date(to_char(OC.RECEIVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')  )  as send_time
  FROM (select distinct cc.*,az.TYPE
          from (select OC.DST_NAME, OC.DST_CERT_TYPE, OC.DST_CERTI_CODE,CT.TYPE
                  FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER OC,
                       DEV_PAS.T_CONTRACT_MASTER     CM,
                       DEV_CLM.t_Certi_Type CT
                 WHERE 1 = 1
                   and OC.Policy_Code = CM.policy_code
                   and CT.CODE = OC.Customer_Cert_Type
                   and OC.CUSTOMER_ID in
                       (SELECT OC1.CUSTOMER_ID
                          FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER OC1,
                               dev_pas.T_CONTRACT_MASTER     CM1
                         WHERE 1 = 1
                           and OC1.POLICY_CODE = CM1.POLICY_CODE ]]>
          <if test=" ser_Certi_No != null and ser_Certi_No != ''  "><![CDATA[ AND OC1.CUSTOMER_CERTI_CODE =(#{ser_Certi_No} ]]></if>
		<if test=" start_Receive_Time  != null "><![CDATA[ AND TRUNC(OC1.Receive_Time)>=to_date(to_char(#{start_Receive_Time},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>
		<if test=" end_Receive_Time  != null "><![CDATA[ AND TRUNC(OC1.Receive_Time)<=to_date(to_char(#{end_Receive_Time},'yyyy-MM-dd'),'yyyy-MM-dd') ]]></if>
		<if test=" ser_Second_Organ  != null "><![CDATA[ AND TRUNC(CM1.ORGAN_CODE)like(#{ser_Second_Organ}||'%')]]></if>
		<if test=" ser_Third_Organ  != null "><![CDATA[ AND TRUNC(CM1.ORGAN_CODE)like(#{ser_Third_Organ}||'%' )]]></if>              
                  		
		<![CDATA[   )) az
         INNER JOIN DEV_CLM.T_IHI_CLMBRO_CUSTOMER cc
            ON cc.DST_NAME = az.DST_NAME
           AND cc.DST_CERT_TYPE = az.DST_CERT_TYPE
           AND cc.DST_CERTI_CODE = az.DST_CERTI_CODE) OC
        LEFT JOIN 
        DEV_PAS.T_CONTRACT_MASTER CM
        ON OC.POLICY_CODE = CM.POLICY_CODE
        AND ROWNUM <= #{LESS_NUM} AND ROWNUM> #{GREATER_NUM}
		ORDER BY OC.CUSTOMER_NAME,OC.CUSTOMER_CERTI_CODE,OC.Scene_Name
		]]>
	</select>
</mapper>