<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.ContractProductDaoImpl">
<!-- 按索引生成的查询条件 -->	
	<sql id="queryContractProductByUwListIdCondition">
		<if test=" uw_list_id  != null "><![CDATA[ AND A.UW_LIST_ID = #{uw_list_id} ]]></if>
	</sql>	
	
	<sql id="queryContractProductByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	
	
	<sql id="queryContractProductByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>
	
	<sql id="queryContractProductByPolicyCodeCondition">
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	
    <sql id="queryContractProductByApplyCodeCondition">
	<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	
	<!-- add by xuhp 保全核保下发核保结论 下发条件承保核保结论时需要支持契约和保全 2015年9月17日 -->
	<sql id="queryContractProductByCSPolicyCodeCondition">
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ and A.Policy_Code = C.Policy_Code AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	
     <sql id="queryContractProductByUwBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	 </sql>	
	
<!-- 按索引查询操作 -->	
	<select id="findContractProductByUwListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE,  A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, C.AMOUNT,A.AMOUNT con_amount, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, 
			A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.UW_ID, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, 
			A.ITEM_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.INITIAL_TYPE, A.UNIT, 
			A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.IS_WAIVED,
			C.TOTAL_PREM_AF,A.TOTAL_PREM_AF con_total_prem_af,  A.DECISION_CODE, A.PAUSE_DATE, 
			A.CHARGE_PERIOD, A.STD_PREM_AF, A.SPREADS_MODE, A.UW_LIST_ID,B.PRODUCT_NAME AS ORGAN_CODE, C.BUSI_ITEM_ID AS INSERT_BY,
			A.IS_GIFT,A.RENEWAL_DISCNTED_EXTRA_PREM_AF,A.INITIAL_DISCNTED_EXTRA_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF,A.INITIAL_DISCNT_PREM_AF  
			FROM DEV_UW.T_CONTRACT_PRODUCT A,DEV_PDS.T_PRODUCT_LIFE B, DEV_UW.T_UW_PRODUCT C 
			WHERE 1 = 1 AND A.PRODUCT_ID = B.PRODUCT_ID AND A.UW_ID  = C.UW_ID AND A.ITEM_ID = C.ITEM_ID  and A.BUSI_ITEM_ID = C.BUSI_ITEM_ID]]>
		<include refid="queryContractProductByItemIdCondition" />
				
		<!-- add by xuhp 保全核保下发核保结论功能开发 查询险种责任名称时需要使用uwid查询，不能只根据itemid 2015年9月14日 -->
		<include refid="queryContractProductByCSPolicyCodeCondition" />
		<include refid="queryContractProductByUwIdCondition" />
		<include refid="queryContractProductByUwBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.UW_LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.DISCNTED_PREM_AF, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, 
			A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.UW_ID, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, 
			A.ITEM_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.INITIAL_TYPE, A.UNIT, 
			A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
			A.CHARGE_PERIOD, A.STD_PREM_AF, A.SPREADS_MODE, A.UW_LIST_ID FROM DEV_UW.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.DISCNTED_PREM_AF, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, 
			A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.UW_ID, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, 
			A.ITEM_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.INITIAL_TYPE, A.UNIT, 
			A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
			A.CHARGE_PERIOD, A.STD_PREM_AF, A.SPREADS_MODE, A.UW_LIST_ID FROM DEV_UW.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findContractProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_CONTRACT_PRODUCT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryContractProductForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.IS_PAUSE, B.DISCNTED_PREM_AF, B.PROD_PKG_PLAN_CODE, B.APPLY_CODE, B.NORENEW_REASON, B.ORGAN_CODE, 
			B.CHARGE_YEAR, B.EXTRA_PREM_AF, B.POLICY_ID, B.CC_SA, B.AMOUNT, 
			B.PAY_YEAR, B.EXPIRY_DATE, B.PAY_PERIOD, B.LIABILITY_STATE, B.COUNT_WAY, 
			B.POLICY_CODE, B.RERINSTATE_DATE, B.RENEW_DECISION, B.VALIDATE_DATE, B.BONUS_MODE_CODE, 
			B.BENEFIT_LEVEL, B.UW_ID, B.PRODUCT_ID, B.BONUS_SA, B.PRODUCT_CODE, B.APPLY_DATE, B.COVERAGE_PERIOD, 
			B.ITEM_ID, B.BUSI_ITEM_ID, B.HEALTH_SERVICE_FLAG, B.LAPSE_DATE, B.INITIAL_TYPE, B.UNIT, 
			B.COVERAGE_YEAR, B.END_CAUSE, B.LAPSE_CAUSE, B.TOTAL_PREM_AF, B.DECISION_CODE, B.PAUSE_DATE, 
			B.CHARGE_PERIOD, B.STD_PREM_AF, B.SPREADS_MODE, B.UW_LIST_ID FROM (
					SELECT ROWNUM RN, A.IS_PAUSE, A.DISCNTED_PREM_AF, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, 
			A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.UW_ID, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, 
			A.ITEM_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.INITIAL_TYPE, A.UNIT, 
			A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
			A.CHARGE_PERIOD, A.STD_PREM_AF, A.SPREADS_MODE, A.UW_LIST_ID FROM DEV_UW.T_CONTRACT_PRODUCT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询交费期间的控件类型yinzj_wb -->	
	<!-- SELECT pu.UNIT_CODE, pu.INPUT_TYPE, pu.VALUE_TYPE
		  FROM DEV_UW.T_PAGECFG_PRD_ELEMENT   ppe,
		       DEV_UW.T_PAGECFG_PRD_CATE_RELA ppcr,
		       DEV_UW.T_PAGECFG_UNIT          pu
		 WHERE pu.UNIT_CODE = ppe.UNIT_CODE
		   and ppe.RELATION_ID = ppcr.ENTRY_CATEGORY_ID
		   AND ppe.UNIT_CODE = pu.UNIT_CODE
		   and pu.LABEL_NAME = '交费期间'
		   and ppcr.PRODUCT_ID = ${product_id} 	 -->
	<select id="findChargeInputTypeByProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT UNIT_CODE, INPUT_TYPE,VALUE_TYPE FROM DEV_UW.T_PAGECFG_UNIT
 					WHERE UNIT_CODE = (SELECT UNIT_CODE FROM DEV_UW.T_PAGECFG_PRD_ELEMENT
         				WHERE RELATION_ID IN (SELECT RELATION_ID FROM DEV_UW.T_PAGECFG_PRD_CATE_RELA
                        	WHERE PRODUCT_ID = ${product_id})
           						AND UNIT_CODE IN (SELECT UNIT_CODE FROM DEV_UW.T_PAGECFG_UNIT
                              		WHERE LABEL_NAME = '交费期间'))  ]]>
		<include refid="queryContractProductByUwIdCondition" />
		<!-- <![CDATA[ ORDER BY A.ITEM_ID           ]]> -->
	</select>
	
	<!-- 查询保险期间的控件类型yinzj_wb -->	
	<select id="findCoverageInputTypeByProductId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT UNIT_CODE, INPUT_TYPE,VALUE_TYPE FROM DEV_UW.T_PAGECFG_UNIT
 					WHERE UNIT_CODE = (SELECT UNIT_CODE FROM DEV_UW.T_PAGECFG_PRD_ELEMENT
         				WHERE RELATION_ID IN (SELECT ENTRY_CATEGORY_ID FROM DEV_UW.T_PAGECFG_PRD_CATE_RELA
                        	WHERE PRODUCT_ID = ${product_id})
           						AND UNIT_CODE IN (SELECT UNIT_CODE FROM DEV_UW.T_PAGECFG_UNIT
                              		WHERE LABEL_NAME = '保险期间'))  ]]>
		<include refid="queryContractProductByUwIdCondition" />
		<!-- <![CDATA[ ORDER BY A.ITEM_ID           ]]> -->
	</select>
	<!-- 根据uwId和UwListId查询责任信息yinzj_wb -->
	<select id="findContractProductByUwIdAndUwListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.DISCNTED_PREM_AF, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, 
			A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.UW_ID, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, 
			A.ITEM_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.INITIAL_TYPE, A.UNIT, 
			A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
			A.CHARGE_PERIOD, A.STD_PREM_AF, A.SPREADS_MODE, A.UW_LIST_ID FROM DEV_UW.T_CONTRACT_PRODUCT A WHERE A.UW_ID = ${uw_id} AND A.Item_Id = ${item_id}  ]]>
		 <include refid="queryContractProductByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID           ]]>
	</select>
	
		<!-- 根据uwId和UwListId查询责任信息yinzj_wb -->
	<select id="findContractProductInfoByUwListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_PAUSE, A.DISCNTED_PREM_AF, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, 
			A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.UW_ID, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, 
			A.ITEM_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.INITIAL_TYPE, A.UNIT, 
			A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
			A.CHARGE_PERIOD, A.STD_PREM_AF, A.SPREADS_MODE, A.UW_LIST_ID FROM DEV_UW.T_CONTRACT_PRODUCT A WHERE 1=1 AND A.ITEM_ID = ${item_id}  ]]>
		<![CDATA[ ORDER BY A.ITEM_ID           ]]>
	</select>
	
	<!-- 保全核保下发核保结论 根据uwid,policycode,itemid查询出责任组信息 2015年9月17日 -->
	<select id="findContractProductInfoByUwidAndPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[ SELECT A.IS_PAUSE, A.DISCNTED_PREM_AF, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, 
			A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.UW_ID, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, 
			A.ITEM_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.INITIAL_TYPE, A.UNIT, 
			A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
			A.CHARGE_PERIOD, A.STD_PREM_AF, A.SPREADS_MODE, A.UW_LIST_ID FROM DEV_UW.T_CONTRACT_PRODUCT A WHERE 1=1 AND A.ITEM_ID = ${item_id}  ]]>
			<include refid="queryContractProductByUwIdCondition" />
			<include refid="queryContractProductByPolicyCodeCondition" />
			<include refid="queryContractProductByUwBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID           ]]>
	</select>
	
	
	<!-- add by xuhp 保全核保下发核保决定 维持原核保结论 查询抄单的保单层的核保结论 2015年9月23日 -->		
	<select id="findContractProductInfoByUP" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[ SELECT UW_LIST_ID,DECISION_CODE,COUNT_WAY FROM DEV_UW.T_CONTRACT_PRODUCT A WHERE 1=1 AND rownum = 1]]>
		<include refid="queryContractProductByUwIdCondition" />
		<include refid="queryContractProductByPolicyCodeCondition" />
	</select>
	<!-- 删除操作 -->	
	<delete id="delContractProductByUwId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_CONTRACT_PRODUCT WHERE UW_ID = #{uw_id} ]]>
	</delete>
	
	<select id="UW_ContractProductsByUwids" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[ SELECT A.IS_PAUSE, A.DISCNTED_PREM_AF, A.PROD_PKG_PLAN_CODE, A.APPLY_CODE, A.NORENEW_REASON, A.ORGAN_CODE, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.CC_SA, A.AMOUNT, 
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.COUNT_WAY, 
			A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, A.BONUS_MODE_CODE, 
			A.BENEFIT_LEVEL, A.UW_ID, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.COVERAGE_PERIOD, 
			A.ITEM_ID, A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.INITIAL_TYPE, A.UNIT, 
			A.COVERAGE_YEAR, A.END_CAUSE, A.LAPSE_CAUSE, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, 
			A.CHARGE_PERIOD, A.STD_PREM_AF, A.SPREADS_MODE, A.UW_LIST_ID FROM DEV_UW.T_CONTRACT_PRODUCT A,DEV_UW.T_UW_POLICY P WHERE 1=1 AND A.UW_ID=P.UW_ID AND P.UW_SOURCE_TYPE='1'  ]]>
       <if test=" uw_id != null "><![CDATA[ and A.UW_ID = #{uw_id} ]]></if>
       <if test=" product_id != null "><![CDATA[ and A.ITEM_ID = #{product_id} ]]></if>
       <if test=" policy_id != null "><![CDATA[ and A.POLICY_ID = #{policy_id} ]]></if>
		<![CDATA[ ORDER BY A.ITEM_ID           ]]>
	</select>
	
	<!-- by zhaoyoan_wb 通过投保单号和产品代码查询产品id 2016年4月21日 -->		
	<select id="findProductIdByApplyProductCode" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
	 	SELECT A.PRODUCT_ID FROM DEV_UW.T_CONTRACT_PRODUCT A
	 	WHERE A.PRODUCT_CODE=#{product_code} AND A.APPLY_CODE=#{apply_code} 
	 	AND ROWNUM=1
	 ]]>
	</select>
	<!-- 险种责任接口 -->
	<!--险种号入参默认是新核心险种号，出参默认是老核心险种号  -->
	<select id="PA_queryBusiItemDuty" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		   SELECT   C.BUSI_ITEM_ID,C.PRODUCT_CODE,C.POLICY_CODE,C.UNIT,C.STD_PREM_AF,C.TOTAL_PREM_AF,
	       C.AMOUNT,C.PREM_FREQ,C.COVERAGE_YEAR,C.CHARGE_PERIOD,C.CHARGE_YEAR,C.PAY_PERIOD,
	       C.PAY_YEAR,C.EXPIRY_DATE,C.VALIDATE_DATE,C.PAY_FREQ,C.INSERT_TIME,C.UPDATE_TIME,B.max_benefit_period
	       FROM DEV_PAS.T_CONTRACT_PRODUCT  C
	       INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD A  ON C.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND C.APPLY_CODE=A.APPLY_CODE
	       left join dev_PAS.t_contract_product_other B on  C.APPLY_CODE=B.APPLY_CODE and C.BUSI_ITEM_ID=B.BUSI_ITEM_ID
	       WHERE 1=1 ]]>
	       <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND C.POLICY_CODE= #{policy_code} ]]></if>
	       <if test=" polno != null and polno != '' ">
	       		<![CDATA[ AND (to_char(A.BUSI_ITEM_ID)=#{polno} OR  A.OLD_POL_NO=#{polno})]]>
  		   </if>
	</select>
	
	<!--投保单号查询  -->
	<!--险种号入参默认是新核心险种号，出参默认是老核心险种号  -->
	<select id="NB_queryBusiItemDuty" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		     SELECT C.BUSI_ITEM_ID, C.PRODUCT_CODE, C.POLICY_CODE, C.UNIT, C.STD_PREM_AF, C.TOTAL_PREM_AF,
		     C.AMOUNT, C.PREM_FREQ, C.COVERAGE_YEAR, C.CHARGE_PERIOD, C.CHARGE_YEAR, C.PAY_PERIOD, C.PAY_YEAR,
		     C.EXPIRY_DATE, C.VALIDATE_DATE, C.PAY_FREQ, C.INSERT_TIME, C.UPDATE_TIME,B.max_benefit_period
		     FROM DEV_NB.T_NB_CONTRACT_PRODUCT C 
		    INNER JOIN  DEV_NB.T_NB_CONTRACT_BUSI_PROD A ON C.BUSI_ITEM_ID=A.BUSI_ITEM_ID AND C.APPLY_CODE=A.APPLY_CODE
		    left join dev_NB.t_nb_contract_product_other B on  C.APPLY_CODE=B.APPLY_CODE  and C.BUSI_ITEM_ID=B.BUSI_ITEM_ID
		     WHERE 1=1 ]]>
	       <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND C.APPLY_CODE = #{policy_code}]]></if>
	       <if test=" polno != null and polno != '' ">
	       		<![CDATA[ AND (to_char(A.BUSI_ITEM_ID)=#{polno} OR  A.OLD_POL_NO=#{polno})]]>
  		   </if>
	</select>
	
	
</mapper>
