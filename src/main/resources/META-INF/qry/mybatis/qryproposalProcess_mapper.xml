<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.NBProposalProcessDaoImpl">
<!--
	<sql id="NB_proposalProcessWhereCondition">
		<if test=" start_status != null and start_status != ''  "><![CDATA[ AND A.START_STATUS = #{start_status} ]]></if>
		<if test=" end_status != null and end_status != ''  "><![CDATA[ AND A.END_STATUS = #{end_status} ]]></if>
		<if test=" operator_id  != null "><![CDATA[ AND A.OPERATOR_ID = #{operator_id} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" operate_id  != null "><![CDATA[ AND A.OPERATE_ID = #{operate_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" process_step  != null "><![CDATA[ AND A.PROCESS_STEP = #{process_step} ]]></if>
		<if test=" active_process  != null "><![CDATA[ AND A.ACTIVE_PROCESS = #{active_process} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" notes != null and notes != ''  "><![CDATA[ AND A.NOTES = #{notes} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByOperateIdCondition">
		<if test=" operate_id  != null "><![CDATA[ AND A.OPERATE_ID = #{operate_id} ]]></if>
	</sql>
	<sql id="NB_queryByEndTimeCondition">
		<if test=" end_time  != null "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
	</sql>	
	<sql id="NB_queryByProcessStepCondition">
		<if test=" process_step  != null "><![CDATA[ AND A.PROCESS_STEP = #{process_step} ]]></if>
	</sql>	
	<sql id="NB_queryByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="NB_queryByFormerIdCondition">
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
	</sql>	
	<sql id="NB_queryByApplyCodeAndEndStatusCondition">
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" end_status  != null "><![CDATA[ AND A.END_STATUS = #{end_status} ]]></if>
	</sql>		
	<sql id="NB_queryByApplyCodeCondition">
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="NB_queryByNumTypeCondition">
		<if test=" num_type != null and num_type != '' "><![CDATA[ AND A.NumType = #{num_type} ]]></if>
	</sql>	

<!-- 删除操作 -->	
	<delete id="NB_deleteProposalProcess" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_PROPOSAL_PROCESS          WHERE OPERATE_ID         = #{operate_id        } ]]>
	</delete>

<!-- 修改操作 -->
	<update id="NB_updateProposalProcess" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_PROPOSAL_PROCESS          ]]>
		<set>
		<trim suffixOverrides=",">
			START_STATUS = #{start_status, jdbcType=VARCHAR} ,
			END_STATUS = #{end_status, jdbcType=VARCHAR} ,
		    OPERATOR_ID = #{operator_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    START_TIME = #{start_time, jdbcType=DATE} ,
		    OPERATE_ID = #{operate_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PROCESS_STEP = #{process_step, jdbcType=NUMERIC} ,
		    ACTIVE_PROCESS = #{active_process, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = #{update_timestamp, jdbcType=NUMERIC} ,
		    END_TIME = #{end_time, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			NOTES = #{notes, jdbcType=VARCHAR} ,
		    FORMER_ID = #{former_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE OPERATE_ID         = #{operate_id        } ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="NB_findByOperateId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_STATUS, A.END_STATUS, A.OPERATOR_ID, A.START_TIME, 
			A.OPERATE_ID, A.POLICY_CODE, A.PROCESS_STEP, A.ACTIVE_PROCESS, A.END_TIME, 
			A.POLICY_ID, A.NOTES, A.FORMER_ID FROM T_PROPOSAL_PROCESS          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByOperateIdCondition" />
		<![CDATA[ ORDER BY A.OPERATE_ID         ]]>
	</select>
	
	<select id="NB_findByEndTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_STATUS, A.END_STATUS, A.OPERATOR_ID, A.START_TIME, 
			A.OPERATE_ID, A.POLICY_CODE, A.PROCESS_STEP, A.ACTIVE_PROCESS, A.END_TIME, 
			A.POLICY_ID, A.NOTES, A.FORMER_ID FROM T_PROPOSAL_PROCESS          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByEndTimeCondition" />
		<![CDATA[ ORDER BY A.OPERATE_ID         ]]>
	</select>
	
	<select id="NB_findByProcessStep" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_STATUS, A.END_STATUS, A.OPERATOR_ID, A.START_TIME, 
			A.OPERATE_ID, A.POLICY_CODE, A.PROCESS_STEP, A.ACTIVE_PROCESS, A.END_TIME, 
			A.POLICY_ID, A.NOTES, A.FORMER_ID FROM T_PROPOSAL_PROCESS          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByProcessStepCondition" />
		<![CDATA[ ORDER BY A.OPERATE_ID         ]]>
	</select>
	
	<select id="NB_findByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_STATUS, A.END_STATUS, A.OPERATOR_ID, A.START_TIME, 
			A.OPERATE_ID, A.POLICY_CODE, A.PROCESS_STEP, A.ACTIVE_PROCESS, A.END_TIME, 
			A.POLICY_ID, A.NOTES, A.FORMER_ID FROM T_PROPOSAL_PROCESS          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.OPERATE_ID         ]]>
	</select>
	<select id="NB_findProposalProcessByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from (SELECT A.OPERATE_ID,A.START_TIME,
			  A.END_TIME,
			tuu.USER_NAME as OPERATE_NAME
       ,(select r.step_desc from DEV_NB.t_proposal_step r where r.step_code=A.PROCESS_STEP) as end_status,
       a.INSERT_TIMESTAMP                     
			FROM DEV_NB.T_PROPOSAL_PROCESS A left join DEV_NB.t_udmp_user tuu on tuu.user_id = a.OPERATOR_ID 
            left join DEV_NB.t_proposal_status tps on tps.proposal_status = A.end_status
            left join DEV_NB.t_proposal_status ps on ps.proposal_status = A.start_status
            left join DEV_NB.t_proposal_status p on p.proposal_status = A.PROCESS_STEP 
			WHERE A.APPLY_CODE = #{apply_code,jdbcType=VARCHAR}
		union
		select 1111 AS OPERATE_ID,a.insert_timestamp as START_TIME,a.update_timestamp as END_TIME,
		(select tuu.user_name from DEV_uw.t_udmp_user tuu where tuu.user_id =a.OPERATE_USER_CODE) as OPERATE_NAME,
		a.operate_item_desc as end_status,a.INSERT_TIMESTAMP 
		from dev_uw.t_uw_workload a where a.apply_code=#{apply_code,jdbcType=VARCHAR}) 
		order by INSERT_TIMESTAMP,OPERATE_ID]]>
	</select>
	
	<select id="NB_findProposalProcessByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
	select * from (SELECT A.OPERATE_ID, A.START_TIME, 
			 A.END_TIME,
			tuu.USER_NAME as OPERATE_NAME
      ,(select r.step_desc from DEV_NB.t_proposal_step r where r.step_code=A.PROCESS_STEP) as end_status,
       a.INSERT_TIMESTAMP
			FROM DEV_NB.T_PROPOSAL_PROCESS A left join DEV_NB.t_udmp_user tuu on tuu.user_id = a.operate_id 
            left join DEV_NB.t_proposal_status tps on tps.proposal_status = A.end_status
            left join DEV_NB.t_proposal_status ps on ps.proposal_status = A.start_status
            left join DEV_NB.t_proposal_status p on p.proposal_status = A.PROCESS_STEP
			WHERE A.APPLY_CODE = (SELECT CM.APPLY_CODE FROM DEV_PAS.T_CONTRACT_MASTER CM 
      WHERE CM.POLICY_CODE = #{policy_code,jdbcType=VARCHAR})
		union
		select 1111 AS OPERATE_ID,a.insert_timestamp as START_TIME,a.update_timestamp as END_TIME,
		(select tuu.user_name from DEV_uw.t_udmp_user tuu where tuu.user_id =a.OPERATE_USER_CODE) as OPERATE_NAME,
		a.operate_item_desc as end_status,a.INSERT_TIMESTAMP 
		from dev_uw.t_uw_workload a where a.apply_code=(SELECT CM.APPLY_CODE FROM DEV_PAS.T_CONTRACT_MASTER CM 
		      WHERE CM.POLICY_CODE = #{policy_code,jdbcType=VARCHAR})) order by INSERT_TIMESTAMP,OPERATE_ID]]>
	</select>
	
	<select id="NB_findByFormerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_STATUS, A.END_STATUS, A.OPERATOR_ID, A.START_TIME, 
			A.OPERATE_ID, A.POLICY_CODE, A.PROCESS_STEP, A.ACTIVE_PROCESS, A.END_TIME, 
			A.POLICY_ID, A.NOTES, A.FORMER_ID FROM T_PROPOSAL_PROCESS          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByFormerIdCondition" />
		<![CDATA[ ORDER BY A.OPERATE_ID         ]]>
	</select>
	<select id="NB_findEndTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT END_TIME FROM T_PROPOSAL_PROCESS WHERE PROCESS_STEP='7' AND END_STATUS='07' AND POLICY_ID=#{policy_id, jdbcType=NUMERIC}  ]]>
<!-- 		<include refid="NB_queryByOperateIdCondition" />-->		
	</select>
	
		<select id="NB_findProposalProcessByApplyCodeAndEndStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_STATUS, A.END_STATUS, A.OPERATOR_ID, A.START_TIME, 
			A.OPERATE_ID, A.POLICY_CODE, A.PROCESS_STEP, A.ACTIVE_PROCESS, A.END_TIME, 
			A.POLICY_ID, A.NOTES, A.FORMER_ID FROM T_PROPOSAL_PROCESS          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByApplyCodeAndEndStatusCondition" />
		<![CDATA[ ORDER BY A.OPERATE_ID         ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="NB_findAllMapProposalProcess" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.START_STATUS, A.END_STATUS, A.OPERATOR_ID, A.START_TIME, 
			A.OPERATE_ID, A.POLICY_CODE, A.PROCESS_STEP, A.ACTIVE_PROCESS, A.END_TIME, 
			A.POLICY_ID, A.NOTES, A.FORMER_ID FROM T_PROPOSAL_PROCESS          A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.OPERATE_ID         ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllProposalProcessByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.END_STATUS, A.START_TIME, A.END_TIME, A.UPDATE_BY FROM dev_nb.T_PROPOSAL_PROCESS          A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findProposalProcessTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_PROPOSAL_PROCESS          A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryProposalProcessForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.START_STATUS, B.END_STATUS, B.OPERATOR_ID, B.START_TIME, 
			B.OPERATE_ID, B.POLICY_CODE, B.PROCESS_STEP, B.ACTIVE_PROCESS, B.END_TIME, 
			B.POLICY_ID, B.NOTES, B.FORMER_ID FROM (
					SELECT ROWNUM RN, A.START_STATUS, A.END_STATUS, A.OPERATOR_ID, A.START_TIME, 
			A.OPERATE_ID, A.POLICY_CODE, A.PROCESS_STEP, A.ACTIVE_PROCESS, A.END_TIME, 
			A.POLICY_ID, A.NOTES, A.FORMER_ID FROM T_PROPOSAL_PROCESS          A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.OPERATE_ID         ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
