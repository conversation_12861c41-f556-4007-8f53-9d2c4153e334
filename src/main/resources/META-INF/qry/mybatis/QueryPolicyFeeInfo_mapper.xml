<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.IQueryPolicyFeeInfoDao">

	<select id="findPolicyFeeInfoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT ROWNUM AS RN,X.* FROM (
SELECT TCM.APPLY_CODE,
       TCM.POLICY_CODE,
       TCM.SERVICE_BANK,
       TP.PAY_MODE,
       TP.BANK_CODE,
       TP.BANK_ACCOUNT,
       TP.PAID_COUNT,
       CASE
         WHEN TP.FEE_SCENE_CODE = 'NB' THEN
          '01'
         WHEN TP.FEE_SCENE_CODE = 'RN' THEN
          '02'
         WHEN TP.FEE_SCENE_CODE = 'CS' AND TP.SERVICE_CODE = 'AM' THEN
          '03'
         ELSE
          '04'
       END BUSI_TYPE,
       SUM(TP.FEE_AMOUNT) AS AMOUNT,
       TP.FINISH_TIME
  FROM ]]>
  <if test=" policy_code  == null  || policy_code == ''  "><![CDATA[  DEV_PAS.T_PREM          TP,]]></if>
  <if test=" policy_code  != null  and policy_code != ''  "><![CDATA[  DEV_PAS.V_PREM_ALL           TP, ]]></if>
   <![CDATA[ 
       DEV_PAS.T_CONTRACT_MASTER    TCM,
       DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
       DEV_PAS.T_CONTRACT_AGENT TCA
 WHERE TP.POLICY_CODE = TCBP.POLICY_CODE
   AND TP.BUSI_PROD_CODE = TCBP.BUSI_PROD_CODE
   AND TCM.POLICY_CODE = TCBP.POLICY_CODE
   AND TCM.APPLY_DATE = TCBP.APPLY_DATE
   AND TCM.POLICY_CODE = TCA.POLICY_CODE 
   AND TCA.IS_NB_AGENT = '1'
   AND TCM.LIABILITY_STATE IN ('1','2','4') -- 有效保单，包含失效、中止
   AND TCA.CHANNEL_TYPE = '03' -- 银贷渠道
   AND (TP.FEE_SCENE_CODE IN ('NB', 'RN') OR
       (TP.FEE_SCENE_CODE = 'CS' AND TP.SERVICE_CODE IN ('AM', 'RE', 'SR'))) --首期、续期、追加保费、复效、特殊复效
   AND TP.FINISH_TIME IS NOT NULL]]>
   <if test=" policy_code  != null  and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
   <if test=" apply_code  != null  and apply_code != ''  "><![CDATA[ AND TCM.APPLY_CODE = #{apply_code} ]]></if>
   <if test=" service_bank  != null and service_bank != ''   "><![CDATA[ AND TCM.SERVICE_BANK = #{service_bank} ]]></if>
   <if test=" bank_code  != null and bank_code != ''   "><![CDATA[ AND TP.BANK_CODE = #{bank_code} ]]></if>
   <if test=" enter_start_date  != null  and enter_start_date != ''  "><![CDATA[ AND TRUNC(TP.FINISH_TIME) >= TO_DATE(#{enter_start_date},'yyyy-MM-dd') ]]></if>
   <if test=" enter_end_date  != null  and enter_end_date != ''  "><![CDATA[ AND TRUNC(TP.FINISH_TIME) <= TO_DATE(#{enter_end_date},'yyyy-MM-dd') ]]></if>
   <![CDATA[
 GROUP BY TCM.APPLY_CODE,
          TCM.POLICY_CODE,
          TCM.SERVICE_BANK,
          TP.PAY_MODE,
          TP.BANK_CODE,
          TP.BANK_ACCOUNT,
          TP.PAID_COUNT,
          TP.FEE_SCENE_CODE,
          TP.SERVICE_CODE,
          TP.FINISH_TIME) X]]>
	</select>
</mapper>