<?xml version="1.0" encoding="UTF-8"?>
<!--********提交  -->
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IPolicyFinancialInfoDao">
	<!-- 根据保单ID查询保单财务基本信息 -->
	<select id="QRY_policyFinancialInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
SELECT PA.PAY_NEXT,
       CASE
         WHEN CM.SERVICE_BANK = 'B8' AND CM.SUBMIT_CHANNEL = 1 AND
              CM.CHANNEL_TYPE = '03' THEN
          L.LAW_MAN_NAME
         ELSE
          B.BANK_NAME
       END AS BANK_NAME,
       CASE
         WHEN CM.SERVICE_BANK = 'B8' AND CM.SUBMIT_CHANNEL = 1 AND
              CM.CHANNEL_TYPE = '03' THEN
          ''
         ELSE
          PA.NEXT_ACCOUNT_BANK
       END AS NEXT_ACCOUNT_BANK,
   PA.NEXT_ACCOUNT_NAME,
   PA.NEXT_ACCOUNT,
   (SELECT SUM(CASE
                     WHEN TO_CHAR(CE.PAY_DUE_DATE, 'yyyy-MM-dd') = '9999-12-31' THEN
                      nvl(PA.FEE_AMOUNT,0)
                     ELSE
                      CP.STD_PREM_AF
                   END)
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         INNER JOIN DEV_PAS.T_CONTRACT_EXTEND CE
            ON CP.POLICY_ID = CE.POLICY_ID
          LEFT JOIN (SELECT *
                      FROM DEV_CAP.V_PREM_ARAP PA
                     WHERE PA.BUSINESS_CODE = #{policy_code}
                       AND PA.FEE_STATUS != '01'
                       AND PA.FEE_STATUS != '02'
                       AND PA.FEE_STATUS != 16) PA
            ON CP.POLICY_CODE = PA.BUSINESS_CODE
           AND CP.PRODUCT_CODE = PA.PRODUCT_CODE
         WHERE CP.POLICY_ID = CM.POLICY_ID
           AND CE.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
           AND CE.ITEM_ID = CP.ITEM_ID) AS NEXT_PREM,
     (
      SELECT nvl(SUM(EXTRA_PREM),0) FROM (
       (SELECT 
       (CASE WHEN  CE.PAY_DUE_DATE>=EP.START_DATE AND CE.PAY_DUE_DATE<=EP.END_DATE THEN EP.EXTRA_PREM
        ELSE EP.EXTRA_PREM
        END) AS EXTRA_PREM
      FROM DEV_PAS.T_EXTRA_PREM EP INNER JOIN  DEV_PAS.T_CONTRACT_EXTEND CE ON EP.POLICY_ID=CE.POLICY_ID
      AND  EP.BUSI_ITEM_ID=CE.BUSI_ITEM_ID AND EP.ITEM_ID=CE.ITEM_ID 
     WHERE CE.POLICY_CODE=#{policy_code} AND ROWNUM=1) T)) AS EXTRA_PREM,
   (SELECT  CE.PAY_DUE_DATE
     FROM 
     (SELECT CE.* FROM DEV_PAS.T_CONTRACT_EXTEND CE 
     INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP ON CE.POLICY_CODE=BP.POLICY_CODE
     AND CE.BUSI_ITEM_ID=BP.BUSI_ITEM_ID WHERE BP.LIABILITY_STATE!=3 ORDER BY CE.PAY_DUE_DATE) CE
       WHERE ROWNUM=1 AND CE.POLICY_ID=CM.POLICY_ID) AS PAY_DUE_DATE ,
   (SELECT SUM(CP.TOTAL_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT CP WHERE CP.POLICY_ID=CM.POLICY_ID) AS TOTAL_PREM_AF,
   PA.POLICY_ID,
   CM.POLICY_CODE
   FROM DEV_PAS.T_PAYER_ACCOUNT PA
   INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
   ON PA.POLICY_ID=CM.POLICY_ID
    LEFT JOIN DEV_PAS.T_BANK B
    ON B.BANK_CODE = PA.NEXT_ACCOUNT_BANK
  LEFT JOIN DEV_PAS.T_LAW_MAN L
    ON PA.LAW_MAN = L.LAW_MAN
   WHERE 1=1 
   ]]>
   <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE= #{policy_code} ]]></if>
   <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
	</select>

	<!-- 根据保单号码查询保单财务责任组续期信息 -->
	<select id="QRY_findItemRenewalInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
SELECT CP.PRODUCT_ID,
       CB.BUSI_PRD_ID,
       CE.PAY_DUE_DATE,
       (TRUNC(MONTHS_BETWEEN(SYSDATE, CP.VALIDATE_DATE) / 12)) + 1 AS POLICY_YEAR, --保单年度
       CE.POLICY_PERIOD, --缴费次数
       CE.PREM_STATUS, --缴费状态 T_PREM_STATUS
       BP.PRODUCT_NAME_SYS,
       PL.PRODUCT_NAME
FROM 
DEV_PAS.T_CONTRACT_EXTEND CE
INNER JOIN
DEV_PAS.T_CONTRACT_PRODUCT CP 
ON CE.ITEM_ID = CP.ITEM_ID
INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CB
ON CP.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
ON CB.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
INNER JOIN DEV_PDS.T_PRODUCT_LIFE PL
ON CP.PRODUCT_ID = PL.PRODUCT_ID
WHERE 1=1 
 ]]>
<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND CP.POLICY_CODE = #{policy_code}]]></if>
<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CP.APPLY_CODE = #{apply_code} ]]></if>
	</select>
<!-- 根据保单号码查询保单续期明细记录信息 -->
    <select id="QRY_findRenewalDetailInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
          SELECT A.PRODUCT_NAME_SYS,
		       A.PRODUCT_NAME,
		       A.DUE_TIME,
		       SUM(A.FEE_AMOUNT) AS FEE_AMOUNT,
		       A.FAMILY_POLICY_RATE_FLAG,
		       A.FEE_STATUS,
		       A.PAY_MODE,
		       A.PAY_TYPE,
		       A.SERVICE_CODE,
		       A.UNIT_NUMBER,
		       A.ORDER_ID,
		       A.SPECIAL_POLICY_FLAG,
		       (CASE
                 WHEN A.FAMILY_POLICY_RATE_FLAG = '否' THEN
                  NULL                 
                 ELSE
                  A.DISCOUNT_RATE
               END) AS DISCOUNT_RATE,/*984险种折扣率*/
         	   A.CUSTOMER_NAME/*984险种被保人姓名*/
		  FROM (SELECT PR.BUSI_PROD_CODE AS PRODUCT_NAME_SYS /*险种代码*/,
		               PRL.PRODUCT_NAME, /*责任组名称*/
		               PR.DUE_TIME, /*保费应交日期*/
		               (CASE
		                 WHEN PR.ARAP_FLAG = '1' THEN 
		                  PR.FEE_AMOUNT
		                 ELSE
		                  PR.FEE_AMOUNT * -1
		               END) AS FEE_AMOUNT, /*保费金额*/
		                (CASE
                 WHEN PR.FAMILY_POLICY_RATE_FLAG = '1' THEN
                  '是'  
                 WHEN PR.FAMILY_POLICY_RATE_FLAG = '0' THEN
                  '否'  
				 WHEN PR.FAMILY_POLICY_RATE_FLAG is null THEN
                  '否'  
                 ELSE
                  ''
               END) AS FAMILY_POLICY_RATE_FLAG,	              
         (CASE WHEN '00984000' IN (SELECT PR.BUSI_PROD_CODE FROM DEV_PAS.V_PREM_ALL PR
          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
            ON CP.ITEM_ID = PR.ITEM_ID
         WHERE 1 = 1
           AND PR.FEE_STATUS != '02'
           AND PR.POLICY_CODE =#{policy_code}
           AND NOT EXISTS
         (SELECT 1
                  FROM DEV_PAS.V_PREM_ARAP_ALL Z
                 WHERE Z.UNIT_NUMBER = PR.UNIT_NUMBER
                   AND ((Z.FEE_TYPE = 'P004990000' AND
                       Z.WITHDRAW_TYPE = '0049900000') or
                       ((Z.FEE_TYPE = 'P004300000' OR
                       Z.FEE_TYPE = 'G004270000') AND
                       Z.WITHDRAW_TYPE = '0040900000'))))  THEN '1' 
              WHEN '00A04000' IN (SELECT PR.BUSI_PROD_CODE FROM DEV_PAS.V_PREM_ALL PR
          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
            ON CP.ITEM_ID = PR.ITEM_ID
         WHERE 1 = 1
           AND PR.FEE_STATUS != '02'
           AND PR.POLICY_CODE =#{policy_code}
           AND NOT EXISTS
         (SELECT 1
                  FROM DEV_PAS.V_PREM_ARAP_ALL Z
                 WHERE Z.UNIT_NUMBER = PR.UNIT_NUMBER
                   AND ((Z.FEE_TYPE = 'P004990000' AND
                       Z.WITHDRAW_TYPE = '0049900000') or
                       ((Z.FEE_TYPE = 'P004300000' OR
                       Z.FEE_TYPE = 'G004270000') AND
                       Z.WITHDRAW_TYPE = '0040900000'))))  THEN '1' 
                       ELSE '0' END) AS SPECIAL_POLICY_FLAG,
                       
		               (CASE 
	                    WHEN PR.FEE_STATUS = '16' THEN
	                      '01'
	                    ELSE
	                      PR.FEE_STATUS
	                    END)AS FEE_STATUS, /*续期交费状态*/
		               PR.PAY_MODE, /*交费形式*/
		               PR.FEE_SCENE_CODE AS PAY_TYPE, /*交费类型*/
		               PR.SERVICE_CODE, /*保全项代码*/
		               PR.UNIT_NUMBER, /*应收应付ID*/
		               (CASE
		                 WHEN PR.FEE_SCENE_CODE = 'CS' AND PR.SERVICE_CODE = 'XQ'
		                   THEN '2'
		                 ELSE
		                   '1'
		                END) AS ORDER_ID,
                    
                       PR.DISCOUNT_RATE,/*984险种折扣率*/
                       (CASE
                 		WHEN PR.BUSI_PROD_CODE in( '00984000','00A04000') THEN
                  			(SELECT TC.CUSTOMER_NAME
                     		FROM DEV_PAS.T_BENEFIT_INSURED NBI                
                    		 LEFT JOIN DEV_PAS.T_INSURED_LIST NIL
                       		ON NBI.POLICY_CODE = NIL.POLICY_CODE
                      		AND NBI.INSURED_ID = NIL.LIST_ID
                     		LEFT JOIN DEV_PAS.T_CUSTOMER TC
                       		ON NIL.CUSTOMER_ID = TC.CUSTOMER_ID
                    		WHERE CP.POLICY_CODE = NBI.POLICY_CODE
                      		AND CP.BUSI_ITEM_ID = NBI.BUSI_ITEM_ID)
                 		ELSE
                  			''
               			END) CUSTOMER_NAME/*984险种被保人姓名*/
		          FROM DEV_PAS.V_PREM_ALL PR
		          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
		            ON CP.ITEM_ID = PR.ITEM_ID
		          LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PRL
		            ON PRL.INTERNAL_ID = nvl(PR.PRODUCT_CODE,CP.PRODUCT_CODE)
		               
		         WHERE 1 = 1
		           AND PR.FEE_STATUS != '02'
		           AND PR.POLICY_CODE = #{policy_code}
		           AND NOT EXISTS (
                       SELECT 1 FROM DEV_PAS.V_PREM_ARAP_ALL Z WHERE Z.UNIT_NUMBER=PR.UNIT_NUMBER 
                       AND ((Z.FEE_TYPE='P004990000' AND Z.WITHDRAW_TYPE='0049900000') or (( Z.FEE_TYPE='P004300000' OR Z.FEE_TYPE='G004270000') AND Z.WITHDRAW_TYPE='0040900000')))
                       ) A
		 GROUP BY A.PRODUCT_NAME_SYS,
		          A.PRODUCT_NAME,
		          A.DUE_TIME,
		          A.FAMILY_POLICY_RATE_FLAG,
		          A.FEE_STATUS,
		          A.PAY_MODE,
		          A.PAY_TYPE,
		          A.SERVICE_CODE,
		          A.UNIT_NUMBER,
		          A.ORDER_ID,
		          A.SPECIAL_POLICY_FLAG,
		          A.DISCOUNT_RATE,
         		  A.CUSTOMER_NAME
		 ORDER BY A.DUE_TIME, A.ORDER_ID, A.UNIT_NUMBER
    ]]>
    </select>
</mapper>
