<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.HospitalPeresultRefvalueDaoImpl">
	<!-- 
	<sql id="UW_hospitalPeresultRefvalueWhereCondition">
		<if test=" unit != null and unit != '' "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" peitem_sub_id != null "><![CDATA[ AND A.PEITEM_SUB_ID = #{peitem_sub_id} ]]></if>
		<if test=" hperref_id != null "><![CDATA[ AND A.HPERREF_ID = #{hperref_id} ]]></if>
		<if test=" remark != null and remark != '' "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" max != null "><![CDATA[ AND A.MAX = #{max} ]]></if>
		<if test=" content != null and content != '' "><![CDATA[ AND A.CONTENT = #{content} ]]></if>
		<if test=" peitem_id != null "><![CDATA[ AND A.PEITEM_ID = #{peitem_id} ]]></if>
		<if test=" min != null "><![CDATA[ AND A.MIN = #{min} ]]></if>
		<if test=" UW_HOSPITAL_ID != null "><![CDATA[ AND A.UW_HOSPITAL_ID = #{UW_HOSPITAL_ID} ]]></if>
		<if test=" hospital_code != null "><![CDATA[ AND A.HOSPITAL_CODE = #{hospital_code} ]]></if> 
		<if test=" peitem_detail_code  != null "><![CDATA[AND A.PEITEM_DETAIL_CODE = #{peitem_detail_code} ]]></if>
	</sql>
 	-->
	<!-- 按索引生成的查询条件 -->
	<sql id="UW_queryHospitalPeresultRefvalueByHperrefIdCondition">
		<if test=" hperref_id  != null "><![CDATA[ AND A.HPERREF_ID = #{hperref_id} ]]></if>
	</sql>
	<sql id="UW_queryHospitalPeresultRefvalueByHospitalIdCondition">
		<if test=" uw_hospital_id  != null "><![CDATA[ AND A.UW_HOSPITAL_ID = #{uw_hospital_id} ]]></if>
	</sql>

	<!-- 按索引查询操作 -->
	<select id="UW_findHospitalPeresultRefvalueByHperrefId"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT, A.PEITEM_SUB_ID, A.HPERREF_ID, A.REMARK, A.MAX, 
			A.CONTENT, A.PEITEM_ID, A.MIN, A.UW_HOSPITAL_ID FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE         A WHERE 1 = 1  ]]>
		<include refid="UW_queryHospitalPeresultRefvalueByHperrefIdCondition" />
		<![CDATA[ ORDER BY A.HPERREF_ID              ]]>
	</select>

	<select id="UW_findHospitalPeresultRefvalueByHospitalId"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT, A.PEITEM_SUB_ID, A.HPERREF_ID, A.REMARK, A.MAX, 
			A.CONTENT, A.PEITEM_ID, A.MIN, A.UW_HOSPITAL_ID FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE         A WHERE 1 = 1  ]]>
		<include refid="UW_queryHospitalPeresultRefvalueByHospitalIdCondition" />
		<![CDATA[ ORDER BY A.HPERREF_ID              ]]>
	</select>


	<!-- 按map查询操作 -->
	<select id="UW_findAllMapHospitalPeresultRefvalue" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT, A.PEITEM_SUB_ID, A.HPERREF_ID, A.REMARK, A.MAX, 
			A.CONTENT, A.PEITEM_ID, A.MIN, A.UW_HOSPITAL_ID FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE         A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.HPERREF_ID              ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="UW_findAllHospitalPeresultRefvalue" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UNIT, A.PEITEM_SUB_ID, A.HPERREF_ID, A.REMARK, A.MAX, 
			A.CONTENT, A.PEITEM_ID, A.MIN, A.UW_HOSPITAL_ID FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE         A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.HPERREF_ID              ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="UW_findHospitalPeresultRefvalueTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE         A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="UW_queryHospitalPeresultRefvalueForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UW_HOSPITAL_ID, B.UNIT, B.PEITEM_CODE, B.HPERREF_ID, B.REMARK, 
			B.MAX, B.CONTENT, B.MIN, B.PEITEM_DETAIL_CODE FROM (
					SELECT ROWNUM RN, A.UW_HOSPITAL_ID, A.UNIT, A.PEITEM_CODE, A.HPERREF_ID, A.REMARK, 
			A.MAX, A.CONTENT, A.MIN, A.PEITEM_DETAIL_CODE FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.HPERREF_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	

	<!-- 多表查询体检结果信息 -->
	<select id="UW_queryHosPeRsValByHospitalId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT 
				   A.UNIT,
				   A.PEITEM_SUB_ID,
			       C.PEITEM_DETAIL_NAME,
			       A.HPERREF_ID,
			       A.REMARK,
			       A.MAX,
			       A.CONTENT,
			       B.PEITEM_NAME,
			       A.MIN,
			       A.UW_HOSPITAL_ID
			  FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE A,
			       DEV_UW.T_PHYSICAL_ITEM              B,
			       DEV_UW.T_PHYSICAL_ITEM_DETAIL       C
			 WHERE 1 = 1
			   AND A.PEITEM_ID = B.PEITEM_ID
			   AND A.PEITEM_SUB_ID = C.PEITEM_DETAIL_ID 
			   AND A.HPERREF_ID !=  #{peitem_id} ]]>
			   <include refid="UW_queryHospitalPeresultRefvalueByHospitalIdCondition" />
			<![CDATA[
			 ORDER BY A.HPERREF_ID
			]]>
	</select>
	
	<!-- 分页查询医院体检结果信息 -->
	<select id="UW_queryHosPeRsValByHospitalIdForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UNIT, B.Peitem_Detail_Code,B.PEITEM_DETAIL_NAME, B.HPERREF_ID, B.REMARK, B.MAX, 
				B.CONTENT, B.PEITEM_NAME, B.MIN, B.UW_HOSPITAL_ID,B.PEITEM_CODE FROM (
			    SELECT ROWNUM AS RN,
			    	   A.UNIT,
			    	   A.Peitem_Detail_Code,
				       C.PEITEM_DETAIL_NAME,
				       A.HPERREF_ID,
				       A.REMARK,
				       A.MAX,
				       A.CONTENT,
				       D.PEITEM_NAME,
				       A.MIN,
				       A.UW_HOSPITAL_ID,
				       A.PEITEM_CODE
				  FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE A,
				       DEV_UW.T_PHYSICAL_ITEM              D,
				       DEV_UW.T_PHYSICAL_ITEM_DETAIL       C
				  WHERE 
				       A.Peitem_Code = D.Peitem_Code(+) AND 
		 		       A.Peitem_Detail_Code = C.Peitem_Detail_Code(+) ]]>
		 		      <include refid="UW_queryHospitalPeresultRefvalueByHospitalIdCondition" />
				      <![CDATA[  AND ROWNUM <= #{LESS_NUM} ]]>
				<!-- <include refid="请添加查询条件" /> -->
				<![CDATA[ ORDER BY A.HPERREF_ID              ]]> 
				<![CDATA[ )  B
					WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询体检医院所有的体检结果个数操作 -->
	<select id="UW_findHosPeRsValByHospitalIdTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE         A WHERE 1 = 1  ]]>
		  <include refid="UW_queryHospitalPeresultRefvalueByHospitalIdCondition" /> 
	</select>
	
	 <!-- 根据HperrefId查询体检医院对应的一条体检结果信息 -->
	<select id="UW_findHosPeRsValByHperrefId"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT   A.UNIT,
					       A.PEITEM_DETAIL_CODE,
					       A.HPERREF_ID,
					       A.REMARK,
					       A.MAX,
					       A.CONTENT,
					       A.PEITEM_CODE,
					       A.MIN,
					       A.UW_HOSPITAL_ID,
					       D.PEITEM_NAME,
					       C.PEITEM_DETAIL_NAME
					  FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE A,
					       DEV_UW.T_PHYSICAL_ITEM              D,
					       DEV_UW.T_PHYSICAL_ITEM_DETAIL       C
					 WHERE 1 = 1 
					   AND A.PEITEM_CODE = D.PEITEM_CODE(+)
					   AND A.PEITEM_DETAIL_CODE = C.PEITEM_DETAIL_CODE(+)]]>
					<include refid="UW_queryHospitalPeresultRefvalueByHperrefIdCondition" />
					<![CDATA[  ORDER BY A.HPERREF_ID]]>
	</select>
	
	<!-- 校验得出相同的体检项而不同的体检项目内容 -->
	<select id="UW_findAllPhysicalItemDetail" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[   select peitem_detail_name,peitem_detail_code 
							   from DEV_UW.T_PHYSICAL_ITEM_DETAIL Where 1 = 1 ]]>
							<if test=" peitem_code  != null "><![CDATA[ AND PEITEM_CODE = #{peitem_code} ]]></if> 
							   <![CDATA[ and peitem_detail_code not in
							        (select peitem_detail_code
							           from DEV_UW.T_BODY_EXAM_ITEM_REFERENCE
							          where 1=1 ]]>
									<if test=" peitem_code  != null "><![CDATA[AND PEITEM_CODE = #{peitem_code} ]]></if> 
							        <if test=" UW_HOSPITAL_ID !=null"><![CDATA[ AND UW_HOSPITAL_ID = #{UW_HOSPITAL_ID} ]]></if>
			<!-- <include refid="请添加查询条件" />    -->
			<![CDATA[  )   ]]> 
	</select>
	
	
	
	<select id="UW_findAllPhysicalItemDetailAdd" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[   select peitem_detail_name,peitem_detail_code 
							   from DEV_UW.T_PHYSICAL_ITEM_DETAIL Where 1 = 1 ]]>
							<if test=" peitem_code  != null "><![CDATA[ AND PEITEM_CODE = #{peitem_code} ]]></if> 
			<!-- <include refid="请添加查询条件" />    -->
	</select>
	
	
		<!-- 根据体检医院 体检项目 查询体检内容的详细信息 -->
	<select id="UW_findHospitalMessage"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.HPERREF_ID,A.UW_HOSPITAL_ID,A.PEITEM_CODE,A.PEITEM_DETAIL_CODE,A.UNIT,A.MAX,A.MIN,A.REMARK,A.CONTENT 
		                FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE A  WHERE 1=1 ]]>
		  	<if test=" peitem_detail_code  != null "><![CDATA[AND A.PEITEM_DETAIL_CODE = #{peitem_detail_code} ]]></if>
		<![CDATA[ ORDER BY A.HPERREF_ID              ]]>
	</select>
	
	<select id="UW_queryMessageByHospitalCode" resultType="java.util.Map" parameterType="java.util.Map">
		 SELECT
               A.UNIT,
               T.UW_HOSPITAL_ID,
               T.HOSPITAL_CODE,
               A.PEITEM_DETAIL_CODE,
               C.PEITEM_DETAIL_NAME,
               A.HPERREF_ID,
               A.REMARK,
               A.MAX,
               A.CONTENT,
               D.PEITEM_NAME,
               A.MIN,
               A.PEITEM_CODE
          FROM DEV_UW.T_BODY_EXAM_ITEM_REFERENCE A,
               DEV_UW.T_PHYSICAL_ITEM              D,
               DEV_UW.T_PHYSICAL_ITEM_DETAIL       C,
               DEV_UW.T_UW_HOSPITAL T
          WHERE 
               A.PEITEM_CODE = D.PEITEM_CODE(+) AND 
                A.PEITEM_DETAIL_CODE = C.PEITEM_DETAIL_CODE(+)
                AND A.UW_HOSPITAL_ID = T.UW_HOSPITAL_ID
    <if test=" HOSPITAL_CODE != null "><![CDATA[ AND T.HOSPITAL_CODE = #{HOSPITAL_CODE} ]]></if> 
	</select>
	
	<!-- 查询体检健康告知，根据PENOTICE_ID -->
	<select id="UW_findPeresultDeclaresByPenoticeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.DECL_CONTENT, A.PENOTICE_ID,A.DECL_ITEM , A.LIST_ID 
		 FROM  DEV_UW.T_PERESULT_DECLARE A WHERE 1=1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID               ]]> 
	</select>
	
    <!-- 根据体检医院code查询t_hospital中医院是否存在 use by youyuan    -->
<!-- modify by zhouzx 2015-10-10 因为数据库外键关联变了,所以将c.UW_HOSPITAL_ID改成c.HOSPITAL_CODE -->
    <select id="UW_findHospitalByHospitalCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT   A.HOSPITAL_NAME, A.FULL_ADDRESS, 
        A.PROVINCIAL, A.HOSPITAL_TYPE, A.HOSPITAL_LEVEL, A.BUS_LINE, A.ORGAN_CODE, 
        A.COUNTY, A.HOSPITAL_CLASS, A.POST_CODE, A.SELF_DRIVE_LINE, 
        A.CITY, A.HOSPITAL_CODE FROM DEV_UW.T_HOSPITAL     A WHERE 1 = 1  ]]>
        <if test=" uw_hospital_id != null and uw_hospital_id != ''  "><![CDATA[ AND A.HOSPITAL_CODE = (SELECT C.HOSPITAL_CODE FROM DEV_UW.T_UW_HOSPITAL C WHERE C.UW_HOSPITAL_ID=#{uw_hospital_id}) ]]></if>
        <![CDATA[ ORDER BY A.HOSPITAL_CODE             ]]>
    </select>
</mapper>
