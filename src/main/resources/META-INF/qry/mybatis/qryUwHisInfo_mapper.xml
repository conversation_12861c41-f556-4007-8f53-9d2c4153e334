<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
 
<mapper namespace="com.nci.tunan.qry.dao.impl.QryUwQryDaoImpl">
	<sql id="uwqueryHisCusInfoCondition">
		<if test="query_busi_code==1">
		   <![CDATA[
                 inner join (select * from DEV_PAS.T_CS_CONTRACT_BUSI_PROD )tc
           on tc.change_id = c.change_id 
           and tc.policy_chg_id=c.policy_chg_id
           and tc.policy_code=C.Policy_Code
             and tc.old_new = '1' --新的
             and exists (select   1 from DEV_PAS.T_CS_CONTRACT_BUSI_PROD tcbp where  tcbp.busi_prod_code in ('00425000', '00952000')
           and tcbp.change_id = c.change_id
           and tcbp.policy_chg_id = c.policy_chg_id
           and tcbp.policy_code = C.Policy_Code
          and tcbp.old_new = '1' )
           INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
          ON tc.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
          AND BP.PRODUCT_CATEGORY = '10001'
           ]]>
		</if>
		<if test="query_busi_code==null or query_busi_code!=1">
		   <![CDATA[
                
            inner join  DEV_PAS.T_CS_CONTRACT_BUSI_PROD tc
           on tc.change_id = c.change_id 
           and tc.policy_chg_id=c.policy_chg_id
           and tc.policy_code=C.Policy_Code
             and tc.old_new = '1' --新的
             
           ]]>
		</if>
	</sql>
	<!--分页查询 查询既往保全信息 -->
	<select id="UW_queryHisCusInfoForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		
		 <![CDATA[
        select * from (
        select B.*,ROWNUM AS RN
          from (
         ]]>
         <!--投保人豁免的  -->
          <if test="query_busi_code==1">
         	<include refid="UW_queryHuoMianHisCusInfoForPageSql" />
         </if>
         <!--正常查询的  -->
         <if test="query_busi_code==null or query_busi_code!=1">
         	<include refid="UW_queryHisCusInfoForPageSql" />
         </if>	
         <![CDATA[ ) B WHERE ROWNUM<=#{LESS_NUM}) C
            WHERE C.RN > #{GREATER_NUM}]]>
	</select>
	<!-- 核保资料既往保全查询个数操作 -->
	<select id="UW_queryHisCusInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		 SELECT COUNT(1)
      FROM (
		]]>
		<!--投保人豁免的  -->
          <if test="query_busi_code==1">
         	<include refid="UW_queryHuoMianHisCusInfoForPageSql" />
         </if>
         <!--正常查询的  -->
         <if test="query_busi_code==null or query_busi_code!=1">
         	<include refid="UW_queryHisCusInfoForPageSql" />
         </if>
		<![CDATA[)]]>
	</select>
	<!--查询投保人豁免的既往保全  -->
	<sql id="UW_queryHuoMianHisCusInfoForPageSql">
		<![CDATA[
            SELECT 
            B.*,
           (CASE WHEN (NOT_STANDARDRISKCOUNT1+NOT_STANDARDRISKCOUNT2 + NOT_STANDARDRISKCOUNT3)>0 THEN '20'
            ELSE  '10'  
             END
          ) AS POLICY_DECISION,
          	(CASE WHEN AGE >= 18 AND BMI > 0 THEN
                1 + IS_EXCEPTION_NOTIFY_FLAG
               ELSE
                0 + IS_EXCEPTION_NOTIFY_FLAG
             END) IS_EXCEPTION_NOTIFY_FLAG2
            FROM (
    			SELECT DISTINCT B.SERVICE_CODE,			    
    			       B.ACCEPT_TIME,
    			       B.ACCEPT_CODE,
    			       B.ACCEPT_ID,
    			       B.ACCEPT_STATUS,
    			       UCC.UW_USER_ID,
    			       UCC.UW_DECISION,
    			       UCC.UW_STATUS,
    			      (CASE
                      WHEN B.SERVICE_CODE IN (SELECT CC.CONFIG_CODE FROM DEV_PAS.T_CS_PARA_CFG CC WHERE CC.CONFIG_TYPE='CSPAST_FLAG1') THEN              
                       (
                        SELECT LISTAGG(PRODUCT_NAME_SYS, '/') WITHIN GROUP(ORDER BY TC.BUSI_PROD_CODE)
                          FROM (SELECT TC.CHANGE_ID,TC.BUSI_PROD_CODE,TC.MASTER_BUSI_ITEM_ID FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD TC
                           WHERE TC.OLD_NEW = '1'
                           AND TC.MASTER_BUSI_ITEM_ID IS NULL 
                          GROUP BY TC.CHANGE_ID,TC.BUSI_PROD_CODE,TC.MASTER_BUSI_ITEM_ID) TC
                         INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                            ON BP.PRODUCT_CODE_SYS = TC.BUSI_PROD_CODE
                         WHERE TC.CHANGE_ID = C.CHANGE_ID)
                      WHEN B.SERVICE_CODE IN (SELECT CC.CONFIG_CODE FROM DEV_PAS.T_CS_PARA_CFG CC WHERE CC.CONFIG_TYPE='CSPAST_FLAG2') THEN              
                       (SELECT LISTAGG(PRODUCT_NAME_SYS, '/') WITHIN GROUP(ORDER BY TC.BUSI_PROD_CODE)
                          FROM  (SELECT TC.CHANGE_ID,TC.BUSI_PROD_CODE FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD TC 
                          WHERE TC.OLD_NEW = '1' AND TC.OPERATION_TYPE!='0'
                          GROUP BY TC.CHANGE_ID,TC.BUSI_PROD_CODE) TC
                         INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                            ON TC.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
                         WHERE TC.CHANGE_ID = C.CHANGE_ID)
                    END) AS PRODUCT_NAME,
    			        (SELECT  
                    nvl(count(*) ,0)
                    from 
                    DEV_UW.T_UW_POLICY UP, DEV_UW.T_UW_MASTER U, DEV_UW.T_UW_BUSI_PROD UBP
                    WHERE UP.UW_ID = U.UW_ID AND UP.UW_ID=U.UW_ID AND UP.POLICY_ID=UBP.POLICY_ID
                    AND (UP.POLICY_DECISION = '20'
                     OR UP.POLICY_DECISION = '40' 
                    OR  UP.POLICY_DECISION = '50'
                     )
                      AND U.UW_SOURCE_TYPE='2' AND UP.POLICY_CODE=C.POLICY_CODE
                      AND U.BIZ_CODE=B.ACCEPT_CODE AND U.SERVICE_CODE='NS' AND UBP.BUSI_PROD_CODE in(${busi_prod_code})
                      ) AS Not_StandardRiskCount1,
                       (SELECT  
                    nvl(COUNT(*),0)
                    FROM 
                    DEV_NB.T_DOCUMENT D ,DEV_UW.T_UW_MASTER U 
                     WHERE D.BUSS_ID= U.UW_ID AND  U.UW_SOURCE_TYPE='2'
                     AND U.BIZ_CODE=B.ACCEPT_CODE
                      AND D.POLICY_CODE = C.POLICY_CODE AND D.TEMPLATE_CODE='UWS_00007'
                      ) AS Not_StandardRiskCount2,
                   (SELECT NVL(COUNT(*), 0)
                      FROM DEV_UW.T_UW_POLICY TUP,
                           DEV_UW.T_UW_MASTER U
                     WHERE TUP.UW_ID = U.UW_ID
                       AND U.UW_SOURCE_TYPE = '2'
                       AND U.BIZ_CODE = B.ACCEPT_CODE
                       AND TUP.NON_STANDARDS = '1') AS NOT_STANDARDRISKCOUNT3,
    			      (SELECT COUNT(0)
    				   FROM DEV_PAS.T_CS_QUESTIONAIRE_CUSTOMER DC
    				  INNER JOIN DEV_PAS.T_QUESTIONAIRE_INFO QI
    				     ON DC.SURVEY_QUESTION_ID = QI.SURVEY_QUESTION_ID
    				  WHERE 1 = 1
    				    AND DC.ACCEPT_ID = C.ACCEPT_ID
    				    AND DC.POLICY_CODE = C.POLICY_CODE
    				    AND DC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
    				    AND ((QI.SURVEY_VERSION = '19' AND
                              QI.SURVEY_CODE NOT IN
                              ('000',
                                 '001A',
                                 '001B',
                                 '002A',
                                 '012A',
                                 '014')) OR
                              (QI.SURVEY_VERSION = '20' AND
                              QI.SURVEY_CODE NOT IN
                              ('000',
                                 '001',
                                 '002',
                                 '003',
                                 '013A',
                                 '015',
                                 '021',
                                 '022',
                                 '023',
                                 '024',
                                 '025')) OR (QI.SURVEY_VERSION = '65' AND
                              QI.SURVEY_CODE NOT IN
                              ('000', '015')) OR
                              (QI.SURVEY_VERSION = '25' AND
                              QI.SURVEY_CODE IN
                              ('004',
                                 '005',
                                 '006',
                                 '007',
                                 '008',
                                 '009',
                                 '010',
                                 '011',
                                 '012',
                                 '012B',
                                 '013',
                                 '015',
                                 '016',
                                 '017',
                                 '018',
                                 '024')) OR (QI.SURVEY_VERSION = '52' AND
                              QI.SURVEY_CODE NOT IN
                              ('000', '023')))
    				    AND SUBSTR2(DC.SURVEY_MODULE_RESULT, 0, 1) = '是') AS IS_EXCEPTION_NOTIFY_FLAG,
    				(SELECT COUNT(1)
	                 FROM DEV_UW.T_UW_TRACE T1, DEV_UW.T_UW_MASTER U
	                WHERE U.BIZ_CODE = B.ACCEPT_CODE AND T1.UW_ID = U.UW_ID
	                  AND T1.UW_EVENT_CODE = '13') AS TOTAL_1,
	                  (SELECT COUNT(1)
	                 FROM DEV_UW.T_UW_AUTO T2, DEV_UW.T_RULE_RESULT T3, DEV_UW.T_UW_MASTER U
	                WHERE T2.AUTO_ID = T3.AUTO_ID AND T2.UW_ID = U.UW_ID
	                  AND U.BIZ_CODE = B.ACCEPT_CODE) AS TOTAL_2, 
	                (SELECT FLOOR(MONTHS_BETWEEN(D.APPLY_TIME, TC.CUSTOMER_BIRTHDAY) / 12)
                         FROM DEV_PAS.T_CUSTOMER TC WHERE TC.CUSTOMER_ID = CU.CUSTOMER_ID) AS AGE,
                    (SELECT SUM(CASE WHEN (CASE
                                          WHEN TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                           0,
                                                           INSTR(DC.SURVEY_MODULE_RESULT,
                                                                 ',',
                                                                 1) - 1)) = 0 THEN
                                           22
                                          ELSE
                                           (CASE
                                             WHEN INSTR(DC.SURVEY_MODULE_RESULT,
                                                        ',',
                                                        2) = 0 THEN
                                              SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           1) + 1)
                                             ELSE
                                              SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           1) + 1,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           2) - 1)
                                           END) /
                                           ((TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                         0,
                                                         INSTR(DC.SURVEY_MODULE_RESULT,
                                                               ',',
                                                               1) - 1)) / 100) *
                                           (TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                         0,
                                                         INSTR(DC.SURVEY_MODULE_RESULT,
                                                               ',',
                                                               1) - 1)) / 100))
                                        END) < 15
                                   
                                    THEN
                                    1
                                 
                                   WHEN
                                   
                                    (CASE
                                      WHEN TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                       0,
                                                       INSTR(DC.SURVEY_MODULE_RESULT,
                                                             ',',
                                                             1) - 1)) = 0 THEN
                                       22
                                      ELSE
                                       (CASE
                                         WHEN INSTR(DC.SURVEY_MODULE_RESULT,
                                                    ',',
                                                    2) = 0 THEN
                                          SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                 INSTR(DC.SURVEY_MODULE_RESULT,
                                                       ',',
                                                       1) + 1)
                                         ELSE
                                          SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                 INSTR(DC.SURVEY_MODULE_RESULT,
                                                       ',',
                                                       1) + 1,
                                                 INSTR(DC.SURVEY_MODULE_RESULT,
                                                       ',',
                                                       2) - 1)
                                       END) /
                                       ((TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                     0,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           1) - 1)) / 100) *
                                       (TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                     0,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           1) - 1)) / 100))
                                    END) > 31
                                   
                                    THEN
                                    1
                                   ELSE
                                    0
                                 END)
                        FROM DEV_PAS.T_CS_QUESTIONAIRE_CUSTOMER DC,
                             DEV_PAS.T_QUESTIONAIRE_INFO        I
                       WHERE 1 = 1
                         AND DC.ACCEPT_ID = C.ACCEPT_ID
                         AND DC.POLICY_CODE = C.POLICY_CODE
                         AND DC.SURVEY_QUESTION_ID =
                             I.SURVEY_QUESTION_ID
                         AND (I.SURVEY_VERSION IN
                             ('65',
                               '20',
                               '19',
                               '25',
                               '52',
                               '22',
                               '32',
                               '42') AND
                             I.SURVEY_CODE = '000' OR
                             I.SURVEY_VERSION = '102' AND
                             I.SURVEY_CODE = '030')
                         AND DC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
                         AND DC.CUSTOMER_ID = CU.CUSTOMER_ID
                         AND DC.SURVEY_MODULE_RESULT IS NOT NULL) AS BMI   
    			  FROM DEV_PAS.T_POLICY_HOLDER CU
    			 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE C
    			    ON CU.POLICY_CODE = C.POLICY_CODE
    			 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE B
    			    ON C.CHANGE_ID = B.CHANGE_ID
    			 INNER JOIN DEV_PAS.T_CS_APPLICATION D 
    			 	ON B.CHANGE_ID = D.CHANGE_ID
    			 INNER JOIN (SELECT * FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD) TC
    			    ON TC.CHANGE_ID = C.CHANGE_ID
    			   AND TC.POLICY_CHG_ID = C.POLICY_CHG_ID
    			   AND TC.POLICY_CODE = C.POLICY_CODE
    			   AND TC.OLD_NEW = '1' /*新的*/
    			   AND EXISTS (SELECT 1
    			          FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD TCBP
    			         WHERE TCBP.BUSI_PROD_CODE IN (${busi_prod_code})
    			           AND TCBP.CHANGE_ID = C.CHANGE_ID
    			           AND TCBP.POLICY_CHG_ID = C.POLICY_CHG_ID
    			           AND TCBP.POLICY_CODE = C.POLICY_CODE
    			           AND TCBP.OLD_NEW = '1')
    			 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
    			    ON TC.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
    			   AND BP.PRODUCT_CATEGORY = '10001'
    			  LEFT JOIN DEV_UW.T_UW_MASTER UCC
    			    ON B.ACCEPT_CODE = UCC.BIZ_CODE
    			 JOIN (SELECT DISTINCT CONFIG_CODE FROM DEV_PAS.T_CS_PARA_CFG CC WHERE CC.CONFIG_TYPE LIKE 'CSPAST_FLAG%') CC
                	ON CC.CONFIG_CODE = B.SERVICE_CODE			    
    			 WHERE CU.CUSTOMER_ID = #{customer_id} ]]>
    			 
		    	<if test="uwAutoFlag != null and uwAutoFlag != '' and uwAutoFlag == 1">
		        	<![CDATA[AND EXISTS(
		        		select * from DEV_UW.T_UW_AUTO T2 WHERE UCC.UW_ID = T2.UW_ID
						AND T2.RULE_RUN_STATUS = 'V02'
		        	)]]>
		        </if>
		        <if test="uwAutoFlag != null and uwAutoFlag != '' and uwAutoFlag == 0">
		        	<![CDATA[AND NOT EXISTS(
		        		select * from DEV_UW.T_UW_AUTO T2 WHERE UCC.UW_ID = T2.UW_ID
						AND T2.RULE_RUN_STATUS = 'V02'
		        	)]]>
		        </if>
    			 
    			<![CDATA[ ) B WHERE 1 = 1]]>
    		
    		<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
    			<![CDATA[ AND (NOT_STANDARDRISKCOUNT1+NOT_STANDARDRISKCOUNT2 + NOT_STANDARDRISKCOUNT3)>0]]>
    		</if>
    		<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
    			<![CDATA[
    				AND (NOT_STANDARDRISKCOUNT1+NOT_STANDARDRISKCOUNT2 + NOT_STANDARDRISKCOUNT3)=0
                ]]>
    		</if>
    		<if test="appntoldimpartInfo != '' and appntoldimpartInfo == 0">
				<![CDATA[AND (IS_EXCEPTION_NOTIFY_FLAG + (CASE WHEN AGE >= 18 AND BMI > 0 THEN
				1
				ELSE
				0
				END)) = 0]]>
	      </if>
	      <if test="appntoldimpartInfo != '' and appntoldimpartInfo == 1">
				<![CDATA[AND (IS_EXCEPTION_NOTIFY_FLAG + (CASE WHEN AGE >= 18 AND BMI > 0 THEN
				1
				ELSE
				0
				END)) > 0]]>
	      </if>
               <![CDATA[ 
            ORDER BY  ACCEPT_TIME DESC
        ]]>
	</sql>
	
	<!--查询风险累计的既往保全  -->
	<sql id="UW_queryHisCusInfoForPageSql">
		<![CDATA[
        SELECT T.*,
         (CASE WHEN (NOT_STANDARDRISKCOUNT1+NOT_STANDARDRISKCOUNT2 + NOT_STANDARDRISKCOUNT3)>0 THEN '20'
            ELSE  '10'  
             END
          ) AS POLICY_DECISION,
          (CASE WHEN AGE >= 18 AND BMI > 0 THEN
                1 + IS_EXCEPTION_NOTIFY_FLAG
               ELSE
                0 + IS_EXCEPTION_NOTIFY_FLAG
             END) IS_EXCEPTION_NOTIFY_FLAG2
         FROM (
		SELECT DISTINCT B.SERVICE_CODE,		    
		       B.ACCEPT_TIME,
		       B.ACCEPT_CODE,
		       B.ACCEPT_ID,
               D.APPLY_TIME,
		       B.ACCEPT_STATUS,
		       UCC.UW_USER_ID,
		       UCC.UW_DECISION,
		       UCC.UW_STATUS,
		      (CASE
                  WHEN B.SERVICE_CODE IN (SELECT CC.CONFIG_CODE FROM DEV_PAS.T_CS_PARA_CFG CC WHERE CC.CONFIG_TYPE='CSPAST_FLAG1') THEN              
                   (
                    SELECT LISTAGG(PRODUCT_NAME_SYS, '/') WITHIN GROUP(ORDER BY TC.BUSI_PROD_CODE)
                      FROM (SELECT TC.CHANGE_ID,TC.BUSI_PROD_CODE,TC.MASTER_BUSI_ITEM_ID FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD TC
                       WHERE TC.OLD_NEW = '1'
                       AND TC.MASTER_BUSI_ITEM_ID IS NULL 
                      GROUP BY TC.CHANGE_ID,TC.BUSI_PROD_CODE,TC.MASTER_BUSI_ITEM_ID) TC
                     INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                        ON BP.PRODUCT_CODE_SYS = TC.BUSI_PROD_CODE
                     WHERE TC.CHANGE_ID = C.CHANGE_ID)
                  WHEN B.SERVICE_CODE IN (SELECT CC.CONFIG_CODE FROM DEV_PAS.T_CS_PARA_CFG CC WHERE CC.CONFIG_TYPE='CSPAST_FLAG2') THEN              
                   (SELECT LISTAGG(PRODUCT_NAME_SYS, '/') WITHIN GROUP(ORDER BY TC.BUSI_PROD_CODE)
                      FROM  (SELECT TC.CHANGE_ID,TC.BUSI_PROD_CODE FROM DEV_PAS.T_CS_CONTRACT_BUSI_PROD TC 
                      WHERE TC.OLD_NEW = '1' AND TC.OPERATION_TYPE!='0'
                      GROUP BY TC.CHANGE_ID,TC.BUSI_PROD_CODE) TC
                     INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                        ON TC.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
                     WHERE TC.CHANGE_ID = C.CHANGE_ID)
                END) AS PRODUCT_NAME,
		       
		       (SELECT  
                nvl(count(*) ,0)
                from 
                DEV_UW.T_UW_POLICY UP, DEV_UW.T_UW_MASTER U
                WHERE UP.UW_ID = U.UW_ID
                AND (UP.POLICY_DECISION = '20'
                 OR UP.POLICY_DECISION = '40' 
                OR  UP.POLICY_DECISION = '50'
                 )
                  AND U.UW_SOURCE_TYPE='2' AND UP.POLICY_CODE=C.POLICY_CODE
                  AND U.BIZ_CODE=B.ACCEPT_CODE
                  ) AS Not_StandardRiskCount1,
                   
                   (SELECT  
                nvl(COUNT(*),0)
                FROM 
                DEV_NB.T_DOCUMENT D ,DEV_UW.T_UW_MASTER U 
                 WHERE D.BUSS_ID= U.UW_ID AND  U.UW_SOURCE_TYPE='2'
                 AND U.BIZ_CODE=B.ACCEPT_CODE
                  AND D.POLICY_CODE=C.POLICY_CODE AND D.TEMPLATE_CODE='UWS_00007'
                  ) AS Not_StandardRiskCount2,
                  
                (SELECT NVL(COUNT(*), 0)
                 FROM DEV_UW.T_UW_POLICY TUP,
                      DEV_UW.T_UW_MASTER U
                WHERE TUP.UW_ID = U.UW_ID
                  AND U.UW_SOURCE_TYPE = '2'
                  AND U.BIZ_CODE = B.ACCEPT_CODE
                  AND TUP.NON_STANDARDS = '1') AS NOT_STANDARDRISKCOUNT3,
                  
                  (SELECT  NVL(COUNT(*), 0)  FROM 
      APP___UW__DBUSER.T_INSURED_LIST T,
      APP___UW__DBUSER.T_UW_POLICY A ,
      APP___UW__DBUSER.T_UW_MASTER UM
     WHERE  T.APPLY_CODE = A.APPLY_CODE 
     AND T.POLICY_CODE = A.POLICY_CODE 
     AND T.UW_ID = UM.UW_ID
     AND A.UW_ID = UM.UW_ID
     AND A.IS_DISPLAY IS NULL
     AND A.UW_SOURCE_TYPE = '2'
     AND UM.BIZ_CODE = B.ACCEPT_CODE
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1) AS NOT_STANDARDRISKCOUNT4,
      (SELECT NVL(COUNT(*), 0)
                  FROM DEV_UW.T_INSURED_LIST    T1,
                       DEV_NB.T_DOCUMENT           T2,
                       DEV_UW.T_UW_DOCUMENT_VERIFY T3,
                       APP___UW__DBUSER.T_UW_MASTER T4
                 WHERE 1 = 1
                   AND T1.CUSTOMER_ID = T3.CUSTOMER_ID
                   AND T2.BUSS_CODE = T1.APPLY_CODE
                   AND T2.DOCUMENT_NO = T3.DOCUMENT_NO
                   AND T2.BUSS_ID = T3.UW_ID
                   AND T1.UW_ID = T4.UW_ID
                   AND T3.UW_ID = T4.UW_ID
                   AND T4.BIZ_CODE = B.ACCEPT_CODE
                   AND T2.BUSS_SOURCE_CODE = '002'
                   AND T2.TEMPLATE_CODE = 'UWS_00007')AS NOT_STANDARDRISKCOUNT5,
      
		           (SELECT COUNT(0)
				   FROM DEV_PAS.T_CS_QUESTIONAIRE_CUSTOMER DC
				  INNER JOIN DEV_PAS.T_QUESTIONAIRE_INFO QI
				     ON DC.SURVEY_QUESTION_ID = QI.SURVEY_QUESTION_ID
				  WHERE 1 = 1
				    AND DC.ACCEPT_ID = C.ACCEPT_ID
				    AND DC.POLICY_CODE = C.POLICY_CODE
				    AND DC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
				    AND DC.CUSTOMER_ID = CU.CUSTOMER_ID
				    AND ((QI.SURVEY_VERSION = '19' AND
                          QI.SURVEY_CODE NOT IN
                          ('000',
                             '001A',
                             '001B',
                             '002A',
                             '012A',
                             '014')) OR
                          (QI.SURVEY_VERSION = '20' AND
                          QI.SURVEY_CODE NOT IN
                          ('000',
                             '001',
                             '002',
                             '003',
                             '013A',
                             '015',
                             '021',
                             '022',
                             '023',
                             '024',
                             '025')) OR (QI.SURVEY_VERSION = '65' AND
                          QI.SURVEY_CODE NOT IN
                          ('000', '015')) OR
                          (QI.SURVEY_VERSION = '25' AND
                          QI.SURVEY_CODE IN
                          ('004',
                             '005',
                             '006',
                             '007',
                             '008',
                             '009',
                             '010',
                             '011',
                             '012',
                             '012B',
                             '013',
                             '015',
                             '016',
                             '017',
                             '018',
                             '024')) OR (QI.SURVEY_VERSION = '52' AND
                          QI.SURVEY_CODE NOT IN
                          ('000', '023')))
				    AND SUBSTR2(DC.SURVEY_MODULE_RESULT, 0, 1) = '是') AS IS_EXCEPTION_NOTIFY_FLAG,
				  (SELECT SURVEY_MODULE_RESULT
	                   FROM DEV_PAS.T_CS_QUESTIONAIRE_CUSTOMER CQC
	                  WHERE C.Accept_Id = CQC.Accept_Id
	                    AND CQC.SURVEY_QUESTION_ID in (833, 564, 1093)]]> 
	                    <if test="query_busi_code==1 or query_busi_code==2">
	                    	<![CDATA[AND CQC.QUESTIONAIRE_OBJECT = '1' ]]> 
	                    </if>
	                    <if test="query_busi_code==null or (query_busi_code!=1 and query_busi_code!=2)">
						    <![CDATA[AND CQC.QUESTIONAIRE_OBJECT = '2' ]]>
						</if>
	                    <![CDATA[and rownum = 1) AS SURVEY_MODULE_RESULT, /*年收入*/
	                 (SELECT TNYI.INCOME FROM DEV_PAS.T_NATURAL_YEAR_INCOME TNYI
                      WHERE TNYI.CUSTOMER_ID = CU.CUSTOMER_ID AND TNYI.ACCEPT_ID = C.ACCEPT_ID
                        AND ROWNUM = 1) AS INCOME, /*页面录入的年收入*/
	                 (SELECT COUNT(1)
	                 FROM DEV_UW.T_UW_TRACE T1, DEV_UW.T_UW_MASTER U
	                WHERE U.BIZ_CODE = B.ACCEPT_CODE AND T1.UW_ID = U.UW_ID
	                  AND T1.UW_EVENT_CODE = '13') AS TOTAL_1,
	                  (SELECT COUNT(1)
	                 FROM DEV_UW.T_UW_AUTO T2, DEV_UW.T_RULE_RESULT T3, DEV_UW.T_UW_MASTER U
	                WHERE T2.AUTO_ID = T3.AUTO_ID AND T2.UW_ID = U.UW_ID
	                  AND U.BIZ_CODE = B.ACCEPT_CODE) AS TOTAL_2,
	                 (SELECT FLOOR(MONTHS_BETWEEN(D.APPLY_TIME, TC.CUSTOMER_BIRTHDAY) / 12)
                         FROM DEV_PAS.T_CUSTOMER TC WHERE TC.CUSTOMER_ID = CU.CUSTOMER_ID) AS AGE,
                     (SELECT SUM(CASE WHEN (CASE
                                          WHEN TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                           0,
                                                           INSTR(DC.SURVEY_MODULE_RESULT,
                                                                 ',',
                                                                 1) - 1)) = 0 THEN
                                           22
                                          ELSE
                                           (CASE
                                             WHEN INSTR(DC.SURVEY_MODULE_RESULT,
                                                        ',',
                                                        2) = 0 THEN
                                              SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           1) + 1)
                                             ELSE
                                              SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           1) + 1,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           2) - 1)
                                           END) /
                                           ((TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                         0,
                                                         INSTR(DC.SURVEY_MODULE_RESULT,
                                                               ',',
                                                               1) - 1)) / 100) *
                                           (TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                         0,
                                                         INSTR(DC.SURVEY_MODULE_RESULT,
                                                               ',',
                                                               1) - 1)) / 100))
                                        END) < 15
                                   
                                    THEN
                                    1
                                 
                                   WHEN
                                   
                                    (CASE
                                      WHEN TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                       0,
                                                       INSTR(DC.SURVEY_MODULE_RESULT,
                                                             ',',
                                                             1) - 1)) = 0 THEN
                                       22
                                      ELSE
                                       (CASE
                                         WHEN INSTR(DC.SURVEY_MODULE_RESULT,
                                                    ',',
                                                    2) = 0 THEN
                                          SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                 INSTR(DC.SURVEY_MODULE_RESULT,
                                                       ',',
                                                       1) + 1)
                                         ELSE
                                          SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                 INSTR(DC.SURVEY_MODULE_RESULT,
                                                       ',',
                                                       1) + 1,
                                                 INSTR(DC.SURVEY_MODULE_RESULT,
                                                       ',',
                                                       2) - 1)
                                       END) /
                                       ((TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                     0,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           1) - 1)) / 100) *
                                       (TRIM(SUBSTR(DC.SURVEY_MODULE_RESULT,
                                                     0,
                                                     INSTR(DC.SURVEY_MODULE_RESULT,
                                                           ',',
                                                           1) - 1)) / 100))
                                    END) > 31
                                   
                                    THEN
                                    1
                                   ELSE
                                    0
                                 END)
                        FROM DEV_PAS.T_CS_QUESTIONAIRE_CUSTOMER DC,
                             DEV_PAS.T_QUESTIONAIRE_INFO        I
                       WHERE 1 = 1
                         AND DC.ACCEPT_ID = C.ACCEPT_ID
                         AND DC.POLICY_CODE = C.POLICY_CODE
                         AND DC.SURVEY_QUESTION_ID =
                             I.SURVEY_QUESTION_ID
                         AND (I.SURVEY_VERSION IN
                             ('65',
                               '20',
                               '19',
                               '25',
                               '52',
                               '22',
                               '32',
                               '42') AND
                             I.SURVEY_CODE = '000' OR
                             I.SURVEY_VERSION = '102' AND
                             I.SURVEY_CODE = '030')
                         AND DC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
                         AND DC.CUSTOMER_ID = CU.CUSTOMER_ID
                         AND DC.SURVEY_MODULE_RESULT IS NOT NULL) AS BMI
		         ]]> 
				
		<!--判断是投保人还是被保人  -->
        <if test="query_busi_code==1 or query_busi_code==2">
		   <![CDATA[
         	 FROM DEV_PAS.T_POLICY_HOLDER cu
           ]]> 
         </if>
          <if test="query_busi_code==null or (query_busi_code!=1 and query_busi_code!=2)">
		   <![CDATA[
               FROM DEV_PAS.T_INSURED_LIST cu
           ]]> 
           </if> 
        <![CDATA[ 
        INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE C
            ON CU.POLICY_CODE = C.POLICY_CODE
        INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE B
            ON C.CHANGE_ID = B.CHANGE_ID  AND C.ACCEPT_ID=B.ACCEPT_ID   
        INNER JOIN DEV_PAS.T_CS_APPLICATION D ON B.CHANGE_ID=D.CHANGE_ID    
        LEFT JOIN DEV_UW.T_UW_MASTER UCC
            ON B.ACCEPT_CODE = UCC.BIZ_CODE        
		WHERE  CU.CUSTOMER_ID = #{customer_id}
        AND EXISTS (SELECT * FROM DEV_PAS.T_CS_PARA_CFG CC WHERE CC.CONFIG_TYPE LIKE 'CSPAST_FLAG%' AND CC.CONFIG_CODE=B.SERVICE_CODE)
        ]]>
        <if test="uwAutoFlag != null and uwAutoFlag != '' and uwAutoFlag == 1">
        	<![CDATA[AND EXISTS(
        		select * from DEV_UW.T_UW_AUTO T2 WHERE UCC.UW_ID = T2.UW_ID
				AND T2.RULE_RUN_STATUS = 'V02'
        	)]]>
        </if>
        <if test="uwAutoFlag != null and uwAutoFlag != '' and uwAutoFlag == 0">
        	<![CDATA[AND NOT EXISTS(
        		select * from DEV_UW.T_UW_AUTO T2 WHERE UCC.UW_ID = T2.UW_ID
				AND T2.RULE_RUN_STATUS = 'V02'
        	)]]>
        </if>
        
        <![CDATA[) T  WHERE 1 = 1]]>
             
        <if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
            <![CDATA[ AND  (NOT_STANDARDRISKCOUNT1+NOT_STANDARDRISKCOUNT2 + NOT_STANDARDRISKCOUNT3 + NOT_STANDARDRISKCOUNT4+ NOT_STANDARDRISKCOUNT5  )>0]]>
        </if>
        <if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
            <![CDATA[
            	AND (NOT_STANDARDRISKCOUNT1+NOT_STANDARDRISKCOUNT2 + NOT_STANDARDRISKCOUNT3 + NOT_STANDARDRISKCOUNT4+ NOT_STANDARDRISKCOUNT5)=0
            ]]>
        </if>
        <if test="appntoldimpartInfo != '' and appntoldimpartInfo == 0">
				<![CDATA[AND (IS_EXCEPTION_NOTIFY_FLAG + (CASE WHEN AGE >= 18 AND BMI > 0 THEN
				1
				ELSE
				0
				END)) = 0]]>
	      </if>
	      <if test="appntoldimpartInfo != '' and appntoldimpartInfo == 1">
				<![CDATA[AND (IS_EXCEPTION_NOTIFY_FLAG + (CASE WHEN AGE >= 18 AND BMI > 0 THEN
				1
				ELSE
				0
				END)) > 0]]>
	      </if>
         <![CDATA[ 
        ORDER BY  ACCEPT_TIME DESC
        ]]>
	</sql>
	
	<!-- 核保资料既往通知书查询个数操作 -->
	<select id="UW_queryHisDocInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		 SELECT COUNT(1)
      FROM (
		]]>
	<include refid="UW_queryHisDocInfo" /> 
		<![CDATA[)]]>
	</select>
	<!-- 核保资料既往通知书分页查询操作 -->
	<select id="UW_queryHisDocInfoForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
	 <![CDATA[
        select * from (
        select B.*,ROWNUM AS RN
          from (
         ]]>
		<include refid="UW_queryHisDocInfo" />
         <![CDATA[ ) B WHERE ROWNUM<=#{LESS_NUM}) C
            WHERE C.RN > #{GREATER_NUM}]]>
	</select>

<sql id="UW_queryHisDocInfo">
		 <![CDATA[
	 SELECT A.CLOB_ID,
	 A.BUSS_CODE AS APPLY_CODE,
	 A.SCAN_BY,
	 A.DOC_LIST_ID,
	 A.PRINT_TIME,
	 A.CREATE_TIME,
	 A.SEND_BY,A.SEND_TIME,
       A.CREATE_BY,
       A.STATUS,A.IS_LINK,
       A.PRINT_BY,
       A.BUSS_ID,A.ORGAN_CODE,
       A.OVERDUE_TIME,
       A.BUSS_SOURCE_CODE,
       A.CLOSE_BY,A.POLICY_ID,A.SEND_OBJ_TYPE,A.REPLY_TIME,A.REPLY_REMARK,
       A.TEMPLATE_CODE,/*通知书类型*/
       A.CLOSE_TIME,/*回销日期*/
       A.SEND_OBJ_ID,A.REPRINT_TIMES,
       A.DOCUMENT_NO,/*通知书号*/
       A.IS_MERGER,
       nvl(A.POLICY_CODE,CM.POLICY_CODE) AS POLICY_CODE,/*保单号*/
       A.POLICY_CODE AS PARA_POLICY_CODE,/*查看通知书保单参数*/
       A.SCAN_TIME,A.REPLY_CONCLUSION,
       A.BUSS_CODE,A.REPLY_BY,A.REPLY_DAYS,
       B.TEMPLATE_NAME,
       A.OVERDUE_DOCUMENT_NO,
       A.SUPPLEMENT_FLAG,B.CARD_CODE,
       C.CUSTOMER_NAME AS SEND_OBJ_NAME, /*发放对象姓名*/
       TP.PENOTICE_ID,
       TP.POSITIVE_INDI,/*阳性标识*/
       CASE
         WHEN CM.POLICY_CODE IS NULL THEN
          (SELECT T.APPLY_DATE
             FROM DEV_NB.T_NB_CONTRACT_MASTER T
            WHERE T.APPLY_CODE = A.BUSS_CODE
              AND ROWNUM = 1)
         ELSE
          CM.ISSUE_DATE
       END TTT_DATE
        FROM DEV_NB.T_DOCUMENT A
       INNER JOIN  DEV_NB.T_DOCUMENT_TEMPLATE B ON A.TEMPLATE_CODE = B.TEMPLATE_CODE
       LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM ON CM.APPLY_CODE=A.BUSS_CODE
       LEFT JOIN DEV_PAS.T_CUSTOMER C  ON A.SEND_OBJ_ID = C.CUSTOMER_ID
       LEFT JOIN DEV_UW.T_PENOTICE TP ON A.DOC_LIST_ID = TP.DOC_LIST_ID
       WHERE 1=1
        AND A.BUSS_CODE IN (
         ]]>
		<if test="query_busi_code!= null and query_busi_code==1">
	                     <![CDATA[SELECT DISTINCT C.APPLY_CODE FROM DEV_NB.T_NB_POLICY_HOLDER C,DEV_NB.T_NB_CONTRACT_BUSI_PROD E
	            WHERE C.APPLY_CODE = E.APPLY_CODE AND C.POLICY_ID = E.POLICY_ID
	            AND E.PRODUCT_CODE IN 
	                ( SELECT BP.PRODUCT_CODE_SYS 
	                    FROM DEV_PDS.T_BUSINESS_PRODUCT BP
                        WHERE 1 = 1
                         AND BP.WAIVER_CUSTOMER_ROLE='01'
                         AND BP.WAIVER_FLAG='1')
	            AND C.CUSTOMER_ID = #{send_obj_id}
	            
	            UNION
	         
	               SELECT DISTINCT D.APPLY_CODE FROM DEV_UW.T_POLICY_HOLDER D,DEV_UW.T_UW_BUSI_PROD F
	            WHERE D.APPLY_CODE = F.APPLY_CODE AND D.UW_ID = F.UW_ID
	            AND F.BUSI_PROD_CODE IN (
	                  SELECT BP.PRODUCT_CODE_SYS 
	                    FROM DEV_PDS.T_BUSINESS_PRODUCT BP
                        WHERE 1 = 1
                         AND BP.WAIVER_CUSTOMER_ROLE='01'
                         AND BP.WAIVER_FLAG='1') 
	            AND D.CUSTOMER_ID = #{send_obj_id}]]>
	</if>
	<if test="query_busi_code == null or query_busi_code != 1">
	       <![CDATA[SELECT DISTINCT APPLY_CODE FROM DEV_NB.T_NB_INSURED_LIST C WHERE C.CUSTOMER_ID = #{send_obj_id}
	             UNION
	    SELECT DISTINCT APPLY_CODE FROM DEV_UW.T_INSURED_LIST D WHERE D.CUSTOMER_ID = #{send_obj_id}]]>
	</if>
    <![CDATA[)AND A.BUSS_SOURCE_CODE = '002' ORDER BY TTT_DATE, A.BUSS_CODE, A.SEND_TIME ]]>
</sql>

	<select id="UW_querySurveyMessage" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT (SELECT TO_CHAR(WM_CONCAT((SELECT EXAMINE_NAME
                                   FROM DEV_UW.T_EXAMINE_CODE
                                  WHERE E2.EXAMINE_CODE = EXAMINE_CODE)))
          FROM DEV_UW.T_SURVIVAL_INVESTIGATION_DETAI E2
         WHERE E1.RREPORT_ID = E2.RREPORT_ID) AS EXAMINE_CODE,
	       E1.CUSTOMER_NAME,
	       E3.REMARK,
	       E3.SURVEY_STATUS,
	       E3.SURVEY_DOC_ID,
	       E3.APPLY_CODE,
	       E3.APPLY_ID
	  FROM DEV_UW.T_SURVIVAL_INVESTIGATION E1,
	       DEV_UW.T_SURVEY_APPLY E3
	  WHERE E1.DOC_LIST_ID = E3.SURVEY_DOC_ID
	   	AND E3.SURVEY_DOC_ID = #{survey_doc_id}
	</select>
	
	<select id="UW_queryOrganCodeByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT T.ORGAN_CODE FROM DEV_PAS.T_CS_POLICY_CHANGE T WHERE T.ACCEPT_ID = #{accept_id}
	</select>
	
	<select id="UW_queryPenoticeByDocListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.QUALITY_EVALUATION, A.PENOTICE_ID, A.PE_REASON, A.RESULT_MAGNUM_MSG, 
			A.CUSTOMER_ID, A.RESULT_AGE, A.APPLY_CODE, A.ROLE_TYPE, A.RESULT_CUS_SCORE, 
			A.POLICY_ID, A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.UW_HOSPITAL_ID, 
			A.RESULT_TEL, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.POLICY_CODE, A.GENDER_CODE, 
			A.ES_INDI FROM APP___UW__DBUSER.T_PENOTICE A WHERE ROWNUM <=  1000  ]]>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<![CDATA[ ORDER BY A.PENOTICE_ID ]]> 
	</select>
	
	<select id="findAccumulatedPremiumPayableByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT T3.TOTAL_PREM_AF AS STD_PREM_AF,
			       0 AS EXTRA_PREM_AF,
			       T3.PREM_FREQ,
			       T3.APPLY_CODE,
			       T3.CHARGE_YEAR,
			       '1' AS UNDERWRITING
			  FROM DEV_NB.T_NB_INSURED_LIST     T1,
			       DEV_NB.T_NB_CONTRACT_MASTER  T2,
			       DEV_NB.T_NB_CONTRACT_PRODUCT T3
			 WHERE T1.APPLY_CODE = T2.APPLY_CODE
			   AND T1.APPLY_CODE = T3.APPLY_CODE
			   AND T1.ORDER_ID = 2
			   AND T3.PRODUCT_CODE = '462000'
			   AND T2.PROPOSAL_STATUS NOT IN
			       ('10', '13', '14', '12', '08', '28', '30', '38')
			   AND T2.LIABILITY_STATE IN ('1', '0')
			   AND T3.LIABILITY_STATE IN ('1', '0')
			   AND (T2.DECISION_CODE IS NULL OR T2.DECISION_CODE NOT IN ('40', '50'))
			   AND (T3.DECISION_CODE IS NULL OR
			       T3.DECISION_CODE NOT IN ('40', '50', '70'))
			   AND T1.CUSTOMER_ID = #{customer_id }
			
			UNION ALL
			
			SELECT T3.STD_PREM_AF,
			       T3.EXTRA_PREM_AF,
			       T3.PREM_FREQ,
			       T3.APPLY_CODE,
			       T3.CHARGE_YEAR,
			       '2' AS UNDERWRITING
			  FROM DEV_PAS.T_INSURED_LIST     T1,
			       DEV_PAS.T_CONTRACT_MASTER  T2,
			       DEV_PAS.T_CONTRACT_PRODUCT T3,
			       DEV_PAS.T_BENEFIT_INSURED  T4
			 WHERE T1.POLICY_CODE = T2.POLICY_CODE
			   AND T1.POLICY_CODE = T3.POLICY_CODE
			   AND T1.LIST_ID = T4.INSURED_ID
			   AND T4.ORDER_ID = 2
			   AND T3.PRODUCT_CODE = '462000'
			   AND T2.LIABILITY_STATE IN ('1', '4')
			   AND T1.CUSTOMER_ID = #{customer_id } ]]> 
	</select>
	
	<select id="findAllUwBusiProd_UW" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(*) FROM dev_nb.T_NB_INSURED_LIST T1, dev_nb.T_NB_CONTRACT_BUSI_PROD T2
	     WHERE T1.APPLY_CODE = T2.APPLY_CODE
	       AND T1.CUSTOMER_ID = #{customer_id}
	       AND T1.ORDER_ID = 2 ]]>
	    <if test=" product_code != null and product_code != ''  "><![CDATA[ AND T2.PRODUCT_CODE = #{product_code} ]]></if>
	</select>
</mapper>