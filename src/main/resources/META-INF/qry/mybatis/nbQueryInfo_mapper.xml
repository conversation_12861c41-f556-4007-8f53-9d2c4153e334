<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.impl.task.dao.impl.CommonQueryDaoImpl">

	<sql id="queryCdsAdvanceDetailPageOrganCode">
	  	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TPA.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
	</sql>
	<sql id="queryCdsAdvanceDetailPageStartDate">
		<if test = " start_date != null "><![CDATA[ AND TPA.BUSI_APPLY_DATE >= #{start_date} ]]></if>
	</sql>
	<sql id="queryCdsAdvanceDetailPageEndDate">
		<if test = " end_date != null "><![CDATA[ AND TPA.BUSI_APPLY_DATE <= #{end_date} ]]></if>
	</sql>
	<sql id="queryCdsAdvanceDetailPageChannelType">
		<if test = " channel_type != null and channel_type != ''"><![CDATA[ AND TA.AGENT_CHANNEL = #{channel_type} ]]></if>
	</sql>
	
	<sql id="queryCdsPolicyBaseInfoPageOrganCode">
	  	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TNCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
	</sql>
	<sql id="queryCdsPolicyBaseInfoPageStartDate">
		<if test = " start_date != null "><![CDATA[ AND TNCM.ISSUE_DATE >= #{start_date} ]]></if>
	</sql>
	<sql id="queryCdsPolicyBaseInfoPageEndDate">
		<if test = " end_date != null "><![CDATA[ AND TNCM.ISSUE_DATE <= #{end_date} ]]></if>
	</sql>
	<sql id="queryCdsPolicyBaseInfoPageChannelType">
		<if test = " channel_type != null and channel_type != ''"><![CDATA[ AND TNCM.CHANNEL_TYPE = #{channel_type} ]]></if>
	</sql>
	<sql id="NB_queryElecAutographCondition">
		<if test="choose_way != null and choose_way != ''">
	      	<if test="choose_way == '01'">
	      		<if test="start_date != null"><![CDATA[AND B.APPLY_DATE >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND B.APPLY_DATE <= #{end_date} ]]></if>
	      	</if>
	      	<if test="choose_way == '02'">
	      		<if test="start_date != null"><![CDATA[AND B.ISSUE_DATE >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND B.ISSUE_DATE <= #{end_date} ]]></if>
	      	</if>
	      	<if test="choose_way == '03'">
	      		<if test="start_date != null"><![CDATA[AND B.CREATE_TIME >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND B.CREATE_TIME <= #{end_date} ]]></if>
	      	</if>
	    </if>
	    <if test="buss_code != null and buss_code != ''"><![CDATA[AND #{buss_code} IN (B.APPLY_CODE,B.POLICY_CODE) ]]></if>
	    <if test="qt_status_buffer != null and qt_status_buffer != ''">
	    	<![CDATA[AND B.QT_STATUS IN (${qt_status_buffer}) ]]>
	    </if>
	    <if test="card_code != null and card_code != ''"><![CDATA[AND B.CARD_CODE = #{card_code} ]]></if>
	    <if test="qa_type_buffer != null and qa_type_buffer != ''  "><![CDATA[ AND B.QA_TYPE IN (${qa_type_buffer})]]>
			
		</if>
		<if test="channel_type != null and channel_type !='' " ><![CDATA[ AND B.CHANNEL_TYPE IN (${channel_type})]]></if>
		<if test="submit_channel != null"><![CDATA[ AND B.SUBMIT_CHANNEL = #{submit_channel}]]></if>
		<if test="subinput_type != null and subinput_type !='' "><![CDATA[ AND B.SUBINPUT_TYPE = #{subinput_type}]]></if>
	    <if test="agent_level != null and agent_level != ''">
	    	<if test="agent_level == 'A1' or agent_level == 'A2' or agent_level == 'A3' "><![CDATA[AND B.AGENT_LEVEL = #{agent_level} ]]></if></if>
	    	<if test="agent_level == 'AA'"><![CDATA[AND B.AGENT_LEVEL IS NULL]]></if>
	    <if test="organ_code != null and organ_code != ''"><![CDATA[ AND B.ORGAN_CODE like CONCAT(#{organ_code},'%') ]]></if>
	    <if test="prescription_status != null and prescription_status != ''">
	        <if test="prescription_status == '01'"><![CDATA[AND FLOOR(NVL(B.QA_AUDIT_TIME, SYSDATE) - B.QA_APPLY_TIME) <= 1 ]]></if>
	        <if test="prescription_status == '02'"><![CDATA[AND FLOOR(NVL(B.QA_AUDIT_TIME, SYSDATE) - B.QA_APPLY_TIME) > 1 AND FLOOR(NVL(B.QA_AUDIT_TIME, SYSDATE) - B.QA_APPLY_TIME) <= 3]]></if>
	        <if test="prescription_status == '03'"><![CDATA[AND FLOOR(NVL(B.QA_AUDIT_TIME, SYSDATE) - B.QA_APPLY_TIME) > 3 ]]></if>
	    </if>
	     <!--是否绩优  -->
        <if test = "is_quality_agent!=null and is_quality_agent!=''" >
        	<if test="is_quality_agent ==0"><![CDATA[ AND B.AGENT_LEVEL IS NULL ]]></if>
        	<if test="is_quality_agent ==1"><![CDATA[ AND B.AGENT_LEVEL IS NOT NULL ]]></if>
       	</if>
	</sql>
	
	<select id="QRY_queryNBDocumentForList" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT DT.TEMPLATE_NAME AS DOCUMENT_NAME,D.CREATE_TIME,D.CREATE_BY,UU.USER_NAME AS CREATE_USER,D.SEND_TIME,D.SCAN_TIME,D.REPLY_TIME,D.CLOSE_TIME,D.STATUS FROM DEV_NB.T_DOCUMENT D
			LEFT JOIN DEV_NB.T_UDMP_USER UU ON D.CREATE_BY=UU.USER_ID
			LEFT JOIN  DEV_NB.T_DOCUMENT_TEMPLATE DT ON D.TEMPLATE_CODE=DT.TEMPLATE_CODE 
			 WHERE 1=1  AND D.TEMPLATE_CODE!='UWS_00007' ]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND D.BUSS_CODE = #{apply_code} ]]></if>
	</select>
    <!-- 105_8 增加 relation_id-->
	<sql id="SQL_QueryNBProcessForList">
        <![CDATA[SELECT * FROM ( SELECT OPERATE_ID,case when (PROCESS_STEP='142' or PROCESS_STEP='143' or  PROCESS_STEP='259' or PROCESS_STEP='260')
                       then  
                      (SELECT STEP_DESC
                         FROM DEV_NB.T_PROPOSAL_STEP
                        WHERE STEP_CODE = P.PROCESS_STEP)||'('|| (SELECT STEP_NAME
                         FROM DEV_NB.T_PROPOSAL_STEP
                        WHERE STEP_CODE = P.PROCESS_STEP) || ')' else
                         (SELECT STEP_DESC
                         FROM DEV_NB.T_PROPOSAL_STEP
                        WHERE STEP_CODE = P.PROCESS_STEP) end 
                           AS STEP_DESC,
        P.OPERATOR_ID, (SELECT USER_NAME FROM DEV_NB.T_UDMP_USER WHERE USER_ID =  P.OPERATOR_ID) AS USER_NAME,P.QT_USER, 
       P.START_TIME,P.END_STATUS,(CASE WHEN (P.NOTES IS NULL OR P.NOTES='')  AND  P.PROCESS_STEP='154'  THEN '投保单影像' WHEN p.process_step = '18' THEN 
		'自核不通过' 
       WHEN P.PROCESS_STEP IN ('236','237','238','239','240','241','242','243','244','245','246','247','248','249','255') THEN '客户身份验真' 
       WHEN P.PROCESS_STEP='261' 
         THEN (SELECT TO_CHAR(WMSYS.WM_CONCAT(UR.RULE_MAG)) AS RULE_MAG FROM DEV_UW.T_UW_MASTER M INNER JOIN  DEV_UW.T_UW_AUTO UA ON M.UW_ID=UA.UW_ID INNER JOIN  DEV_UW.T_RULE_RESULT UR ON UA.AUTO_ID=UR.AUTO_ID
 		WHERE M.BIZ_CODE= #{apply_code} AND  UA.RULE_RUN_STATUS='V03')
 		WHEN P.PROCESS_STEP IN ('212','213') AND P.NOTES ='银保通双录批处理' THEN '未上传录音录像资料（系统自动质检）'
       ELSE P.NOTES END) AS NOTES,
       P.INSERT_TIME,P.APPLY_CODE,P.RELATION_ID,P.PROCESS_STEP,P.START_STATUS
       FROM DEV_NB.T_PROPOSAL_PROCESS P
              LEFT JOIN DEV_UW.T_UW_MASTER UM
                           ON UM.UW_ID = P.BUSS_ID 
                           WHERE 1=1 
                           AND (UM.UW_SOURCE_TYPE = '1' OR UM.UW_SOURCE_TYPE IS NULL)
                           ]]>
       UNION
       SELECT 1111,(SELECT STATUS_DETAIL_NAME FROM DEV_UW.T_UW_STATUS_DETAIL WHERE STATUS_DETAIL_CODE = U.UW_STATUS_DETAIL) AS STEP_DESC ,
       U.OPERATE_USER_CODE,(SELECT USER_NAME FROM DEV_PAS.T_UDMP_USER WHERE USER_ID =  U.OPERATE_USER_CODE) AS USER_NAME, '' as QT_USER,
       U.INSERT_TIME,'',U.OPERATE_ITEM_DESC,U.INSERT_TIME,U.APPLY_CODE,0 AS relation_id,'' AS PROCESS_STEP,'' AS START_STATUS FROM 
        DEV_UW.T_UW_MASTER UM
                          LEFT JOIN DEV_UW.T_UW_WORKLOAD U
                          ON U.UW_ID=UM.UW_ID AND UM.UW_SOURCE_TYPE=1) ZU 
       WHERE 1=1
       <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND ZU.APPLY_CODE = #{apply_code} ]]></if>
       <![CDATA[ORDER BY ZU.INSERT_TIME,ZU.OPERATE_ID]]>
    </sql>
    
    <sql id="SQL_QueryNBProcessForLists">
        <![CDATA[SELECT * FROM ( SELECT OPERATE_ID,(SELECT STEP_DESC FROM DEV_NB.T_PROPOSAL_STEP WHERE STEP_CODE = P.PROCESS_STEP) AS STEP_DESC,
       P.OPERATOR_ID, (SELECT USER_NAME FROM DEV_NB.T_UDMP_USER WHERE USER_ID =  P.OPERATOR_ID) AS USER_NAME,
       P.START_TIME,P.END_STATUS,(CASE WHEN (P.NOTES IS NULL OR P.NOTES='')  AND  P.PROCESS_STEP='154'  THEN '投保单影像' ELSE P.NOTES END) AS NOTES,P.INSERT_TIME,P.APPLY_CODE,P.relation_id FROM DEV_NB.T_PROPOSAL_PROCESS P]]>
       UNION
       SELECT 1111,(SELECT STATUS_DETAIL_NAME FROM DEV_UW.T_UW_STATUS_DETAIL WHERE STATUS_DETAIL_CODE = U.UW_STATUS_DETAIL) AS STEP_DESC ,
       U.OPERATE_USER_CODE,(SELECT USER_NAME FROM DEV_UW.T_UDMP_USER WHERE USER_ID =  U.OPERATE_USER_CODE) AS USER_NAME,
       U.INSERT_TIME,'',U.OPERATE_ITEM_DESC,U.INSERT_TIME,U.APPLY_CODE,0 AS relation_id FROM 
              DEV_UW.T_UW_MASTER UM
                          LEFT JOIN DEV_UW.T_UW_WORKLOAD U
                          ON U.UW_ID=UM.UW_ID AND UM.UW_SOURCE_TYPE=1 ) ZU 
       WHERE 1=1
       <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND ZU.APPLY_CODE = #{apply_code} ]]></if>
       <![CDATA[ORDER BY ZU.START_TIME,ZU.OPERATE_ID]]>
    </sql>
	<select id="QRY_queryNBProcessForList" resultType="java.util.Map" parameterType="java.util.Map">
	 	<include refid="SQL_QueryNBProcessForList" />
	</select>
	<select id="NB_queryNBProcessTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
	  <![CDATA[
		SELECT COUNT(1) FROM (
		]]>
				<include refid="SQL_QueryNBProcessForList" />
		 <![CDATA[
			) B 
		 ]]>
	</select>
	<select id="NB_queryNBProcessPageInfo" resultType="java.util.Map" parameterType="java.util.Map">
	 	 <![CDATA[
		SELECT * FROM (
			SELECT B.*,ROWNUM RN FROM (
		]]>
				<include refid="SQL_QueryNBProcessForList" />
		 <![CDATA[
			) B 
		WHERE ROWNUM <= #{LESS_NUM}) C WHERE C.RN > #{GREATER_NUM}
		 ]]>
	</select>
	<select id="QRY_queryNBuwForList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT d.decision_desc AS NOTES
			  FROM DEV_UW.T_UW_POLICY P
			 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER M
			    ON M.POLICY_ID = P.POLICY_ID
			 inner join dev_pas.t_policy_decision d
			    on d.decision_code = p.policy_decision
			 WHERE 1 = 1
		 ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND M.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND M.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	
	<select id="qry_findNBPolicyInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT CM.APPLY_CODE,PACM.POLICY_CODE,CM.SUBMIT_CHANNEL,CM.LIABILITY_STATE,PACM.END_CAUSE,CM.PROPOSAL_STATUS
		 FROM DEV_NB.T_NB_CONTRACT_MASTER CM 
        LEFT JOIN DEV_PAS.T_CONTRACT_MASTER PACM ON CM.APPLY_CODE=PACM.APPLY_CODE
        WHERE 1=1
		]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!-- 根据投保单号查询  -->
	<select id="QRY_findNBApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT CM.APPLY_CODE,PACM.POLICY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER CM 
        LEFT JOIN DEV_PAS.T_CONTRACT_MASTER PACM ON CM.APPLY_CODE=PACM.APPLY_CODE
        WHERE 1=1
		]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
		<![CDATA[ ORDER BY cm.validate_date DESC
		]]>
	</select>
	
	
	<select id="QRY_queryNBinsForList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT T.QT_COMMENTS AS NOTESSSS FROM DEV_NB.T_NB_QT_TASK T INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER M ON T.POLICY_ID=M.POLICY_ID
WHERE 1=1]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND M.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND M.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	
		<!-- 新契约查询保单外包打印轨迹信息 -->
	<select id="NB_findAllBpoPrintInfoByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[  
		      SELECT
			   B.POST_DATE 
			  ,B.POST_ID 
			  ,B.SIGN_TIME 
			  ,E.ORG_NAME AS MAKE_NAME 
			  ,F.ORG_NAME  --外包商名称
			   FROM DEV_NB.T_POLICY_PRINT A
			  LEFT JOIN DEV_NB.T_CONTRACT_SIGN B ON A.APPLY_CODE = B.APPLY_CODE
			  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER C ON A.APPLY_CODE = C.APPLY_CODE
			  LEFT JOIN DEV_NB.T_SITE_ORGAN_MAPPING D  ON C.ORGAN_CODE = D.ORGAN_CODE
			  LEFT JOIN DEV_NB.T_BPO_ORG E ON D.SITE_CODE = E.ORG_ID
			  LEFT JOIN DEV_NB.T_BPO_ORG F ON E.PARENT_ORG_ID = F.ORG_ID
			  WHERE A.PRINT_TYPE = 2 AND A.APPLY_CODE = #{apply_code}
	]]>
	</select>
	
	
	<select id="NB_findMasterByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.OPERATOR_USER_CODE,LTRIM(RTRIM(A.DECISION_CODE)) AS DECISION_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
      LTRIM(RTRIM(A.CHANNEL_TYPE)) AS CHANNEL_TYPE , A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
      A.POLICY_ID, A.SCAN_COMPLETE_TIME,
      A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
      A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
      A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
      A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, A.WINNING_START_FLAG,
      A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, A.DIALECT_INDI,
      A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,B.SALES_CHANNEL_NAME AS TYPE_NAME,A.INPUT_DATE,A.CALL_TIME_LIST,A.APPOINT_VALIDATE ,A.BANK_AGENCY_FLAG from DEV_NB.t_NB_CONTRACT_MASTER  A ,DEV_NB.t_Sales_Channel B WHERE 1 = 1 
      and A.channel_type=B.sales_channel_code  ]]>
		<include refid="NB_queryByDeliverConditions" />
		<![CDATA[ ORDER BY A.APPLY_CODE          ]]>
	</select>
	
	<sql id="NB_queryByDeliverConditions">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')  ]]></if>
		<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) = #{channel_type} ]]></if>
		<if test=" start_date != null and start_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME>= #{start_date} ]]></if>
		<if test=" end_date != null and end_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME<= #{end_date} ]]></if>
	</sql>
	
	<select id="NB_findOrgRelByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ORGAN_NAME, A.ORGAN_CODE, A.UPORGAN_LVL_CODE, A.UPORGAN_CODE, A.UPORGAN_ID, A.ORGAN_GRADE, A.ORGAN_ID FROM DEV_PAS.T_UDMP_ORG_REL A WHERE 1 = 1  ]]>
		<include refid="NB_queryOrgRelByOrganCodeCondition" />
		<![CDATA[ ORDER BY A.ORGAN_ID ]]>
	</select>
	
	<sql id="NB_queryOrgRelByOrganCodeCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>
	
	<!-- 临分计算数据接口 -->
	<select id="NB_findLinfenCalcDataInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT TCBP.BUSI_ITEM_ID,TCBP.POLICY_ID,TCBP.PRODUCT_CODE,TCBP.VALIDATE_DATE,TIL.CUSTOMER_ID,
			TCM.ISSUE_DATE,TCM.LIABILITY_STATE,TCM.CHANNEL_TYPE,
			(SELECT SUM(T.AMOUNT) FROM DEV_NB.T_NB_CONTRACT_PRODUCT T WHERE T.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID)OLD_AMOUNT,
			(SELECT SUM(T.AMOUNT) FROM DEV_NB.T_NB_CONTRACT_PRODUCT T WHERE T.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID)NEW_AMOUNT,
			TCM.POLICY_CODE,TCM.ORGAN_CODE,TCM.INSERT_TIME
			FROM DEV_NB.T_NB_INSURED_LIST TIL,DEV_NB.T_NB_BENEFIT_INSURED TBI,DEV_NB.T_NB_CONTRACT_BUSI_PROD TCBP,DEV_NB.T_NB_CONTRACT_MASTER TCM
			WHERE TIL.LIST_ID=TBI.INSURED_ID AND TCM.POLICY_ID=TCBP.POLICY_ID
			AND TCBP.BUSI_ITEM_ID=TBI.BUSI_ITEM_ID  AND TIL.CUSTOMER_ID=#{customer_id} AND TCBP.BUSI_ITEM_ID=#{busi_item_id} AND TCM.APPLY_CODE=#{apply_code}
		 ]]>
	</select>
	
	<!-- 根据保单号查询是否为银保通保单 -->
	<select id="findQryNbMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHANNEL_TYPE,A.POLICY_ID,A.INPUT_TYPE,A.SUBMIT_CHANNEL,A.SUBINPUT_TYPE,A.POLICY_CODE,A.APPLY_CODE,
       					 A.POLICY_REINSURE_FLAG,A.OLD_POLICY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER A 
       			  WHERE #{policy_code} IN (A.POLICY_CODE,A.APPLY_CODE)
        ]]>
	</select>
	<!-- 查询申请方式 -->
	<select id="getServiceTypeByOldPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT CA.SERVICE_TYPE FROM DEV_PAS.T_CS_POLICY_CHANGE  CPC 
 						INNER JOIN DEV_PAS.T_CS_APPLICATION CA ON CPC.CHANGE_ID=CA.CHANGE_ID
 						INNER JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL BP
              ON CPC.CHANGE_ID = BP.CHANGE_ID
             AND CPC.POLICY_CHG_ID = BP.POLICY_CHG_ID
 				  WHERE CPC.POLICY_CODE=#{policy_code} AND CPC.SERVICE_CODE='RR' 
        ]]>
	</select>
	<!-- 统计846险种信息个数 -->
	<select id="NB_findContractBusiProdByPolicyCode" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[  SELECT COUNT(BP.POLICY_CODE) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD BP
 					 WHERE BP.POLICY_CODE = #{policy_code}/*保单号*/
 					 AND BP.PRODUCT_CODE IN ('00846000','00846200') 
        ]]>
	</select>
	
	<select id="NB_queryCdsAdvanceDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1)
		  FROM DEV_NB.T_PREM_ARAP TPA
		 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER TNCM ON (TNCM.APPLY_CODE = TPA.APPLY_CODE)
		 INNER JOIN DEV_NB.T_NB_CONTRACT_AGENT TNCA ON (TNCA.APPLY_CODE = TNCM.APPLY_CODE)
		 INNER JOIN DEV_NB.T_AGENT TA ON (TA.AGENT_CODE = TNCA.AGENT_CODE)
		 INNER JOIN DEV_NB.T_UDMP_ORG TUO ON (TUO.ORGAN_CODE = TPA.ORGAN_CODE)
		 WHERE 1 = 1 AND (TPA.FEE_STATUS = '00' OR TPA.FEE_STATUS = '01') AND TPA.BUSINESS_TYPE = '1004' AND TPA.ARAP_FLAG = '1']]>
	    <include refid="queryCdsAdvanceDetailPageOrganCode"/>
	    <include refid="queryCdsAdvanceDetailPageStartDate"/>
	    <include refid="queryCdsAdvanceDetailPageEndDate"/>
	    <include refid="queryCdsAdvanceDetailPageChannelType"/>
	</select>
	
	<select id="NB_queryCdsAdvanceDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.RN,B.SALES_CHANNEL_NAME, B.ORDER_ID,
             B.ORGAN_CODE,
             B.ORGAN_NAME,
             B.COOPERATION_CODE AGENCY_ORGAN_CODE,
             B.COOPERATION_NAME AGENCY_ORGAN_NAME,
             B.AGENT_NAME,
             B.AGENT_CODE,
             B.AGENT_NO,
             B.POLICY_CODE,
             B.BUSI_PROD_CODE,
             B.BUSI_PROD_NAME,
             B.CHARGE_YEAR,
             B.BUSI_APPLY_DATE SUBMIT_DATE,
             B.FEE_AMOUNT
    FROM (SELECT ROWNUM RN,
               (SELECT TSC.SALES_CHANNEL_NAME FROM DEV_NB.T_SALES_CHANNEL TSC WHERE TSC.SALES_CHANNEL_CODE = TA.AGENT_CHANNEL AND ROWNUM = 1) SALES_CHANNEL_NAME,
                CBP.ORDER_ID,
               TPA.ORGAN_CODE,
               TUO.ORGAN_NAME,
               TNCA.COOPERATION_CODE,
               TNCA.COOPERATION_NAME,
               TA.AGENT_NAME,
               TPA.AGENT_CODE,
               TA.AGENT_NO,
               TNCM.POLICY_CODE,
               TPA.BUSI_PROD_CODE,
               TPA.BUSI_PROD_NAME,
               DECODE(TPA.PREM_FREQ, '1', '一次交清', '2', TPA.CHARGE_YEAR || '月', '5', TPA.CHARGE_YEAR || '年') CHARGE_YEAR,
               TPA.BUSI_APPLY_DATE,
               TPA.FEE_AMOUNT
          FROM DEV_NB.T_PREM_ARAP TPA
         INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER TNCM ON (TNCM.APPLY_CODE = TPA.APPLY_CODE)
         INNER JOIN DEV_NB.T_NB_CONTRACT_AGENT TNCA ON (TNCA.APPLY_CODE = TNCM.APPLY_CODE)
         INNER JOIN DEV_NB.T_AGENT TA ON (TA.AGENT_CODE = TNCA.AGENT_CODE)
         INNER JOIN DEV_NB.T_UDMP_ORG TUO ON (TUO.ORGAN_CODE = TPA.ORGAN_CODE)
         INNER JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD CBP ON  CBP.APPLY_CODE= TPA.APPLY_CODE AND TPA.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
         WHERE 1 = 1 AND (TPA.FEE_STATUS = '00' OR TPA.FEE_STATUS = '01') AND TPA.BUSINESS_TYPE = '1004' AND TPA.ARAP_FLAG = '1' AND ROWNUM <= #{LESS_NUM}]]>
	    <include refid="queryCdsAdvanceDetailPageOrganCode"/>
	    <include refid="queryCdsAdvanceDetailPageStartDate"/>
	    <include refid="queryCdsAdvanceDetailPageEndDate"/>
	    <include refid="queryCdsAdvanceDetailPageChannelType"/>
	    <![CDATA[  order by CBP.APPLY_CODE, CBP.ORDER_ID]]>	   
	   <![CDATA[ ) B  WHERE B.RN >= #{GREATER_NUM} ]]>
	</select>
	
	<select id="NB_queryCdsAdvanceDetailAll" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT ROWNUM RN,
		       (SELECT TSC.SALES_CHANNEL_NAME FROM DEV_NB.T_SALES_CHANNEL TSC WHERE TSC.SALES_CHANNEL_CODE = TA.AGENT_CHANNEL AND ROWNUM = 1) SALES_CHANNEL_NAME,
		       CBP.ORDER_ID,
		       TPA.ORGAN_CODE,
		       TUO.ORGAN_NAME,
		       TNCA.COOPERATION_CODE AGENCY_ORGAN_CODE,
		       TNCA.COOPERATION_NAME AGENCY_ORGAN_NAME,
		       TA.AGENT_NAME,
		       TPA.AGENT_CODE,
		       TA.AGENT_NO,
		       TNCM.POLICY_CODE,
		       TPA.BUSI_PROD_CODE,
		       TPA.BUSI_PROD_NAME,
		       DECODE(TPA.PREM_FREQ, '1', '一次交清', '2', TPA.CHARGE_YEAR || '月', '5', TPA.CHARGE_YEAR || '年') CHARGE_YEAR,
		       TPA.BUSI_APPLY_DATE SUBMIT_DATE,
		       TPA.FEE_AMOUNT
		  FROM DEV_NB.T_PREM_ARAP TPA
		 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER TNCM ON (TNCM.APPLY_CODE = TPA.APPLY_CODE)
		 INNER JOIN DEV_NB.T_NB_CONTRACT_AGENT TNCA ON (TNCA.APPLY_CODE = TNCM.APPLY_CODE)
		 INNER JOIN DEV_NB.T_AGENT TA ON (TA.AGENT_CODE = TNCA.AGENT_CODE)
		 INNER JOIN DEV_NB.T_UDMP_ORG TUO ON (TUO.ORGAN_CODE = TPA.ORGAN_CODE)
		 INNER JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD CBP ON  CBP.APPLY_CODE= TPA.APPLY_CODE  AND TPA.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
		 WHERE 1 = 1 AND (TPA.FEE_STATUS = '00' OR TPA.FEE_STATUS = '01') AND TPA.BUSINESS_TYPE = '1004' AND TPA.ARAP_FLAG = '1']]>
	    <include refid="queryCdsAdvanceDetailPageOrganCode"/>
	    <include refid="queryCdsAdvanceDetailPageStartDate"/>
	    <include refid="queryCdsAdvanceDetailPageEndDate"/>
	    <include refid="queryCdsAdvanceDetailPageChannelType"/>
	    <![CDATA[  order by CBP.APPLY_CODE, CBP.ORDER_ID]]>	   
	</select>
	
	
	<select id="NB_queryCdsPolicyBaseinfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1)
			  FROM DEV_NB.T_NB_CONTRACT_MASTER TNCM
		          LEFT JOIN DEV_NB.T_SALES_CHANNEL TSC
		            ON (TSC.SALES_CHANNEL_CODE = TNCM.CHANNEL_TYPE)
		          LEFT JOIN DEV_NB.T_UDMP_ORG TUO
		            ON (TUO.ORGAN_CODE = TNCM.ORGAN_CODE)
		          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TNCA
		            ON (TNCA.APPLY_CODE = TNCM.APPLY_CODE)
		          LEFT JOIN DEV_NB.T_AGENT TA
		            ON (TA.AGENT_CODE = TNCA.AGENT_CODE)
		          LEFT JOIN DEV_NB.T_NCL_COM_MAPPING TNCMP
		            ON (TNCMP.OTHER_GROUP = TA.MANAGE_COM_OUTER)
		          LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP
		            ON (TNCP.APPLY_CODE = TNCM.APPLY_CODE)
		          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
		            ON (TBP.PRODUCT_CODE_SYS = TNCP.PRODUCT_CODE)
		          LEFT JOIN DEV_NB.T_NB_CONTRACT_PRODUCT TNCPT
		            ON (TNCPT.BUSI_ITEM_ID = TNCP.BUSI_ITEM_ID)
		          LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT TNPA
		            ON (TNPA.POLICY_ID = TNCM.POLICY_ID)
		          LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER TNPH
		            ON (TNPH.APPLY_CODE = TNCM.APPLY_CODE)
		          LEFT JOIN DEV_PAS.T_CUSTOMER TC
		            ON (TC.CUSTOMER_ID = TNPH.CUSTOMER_ID)
		          LEFT JOIN DEV_NB.T_ADDRESS TAD
		            ON (TAD.ADDRESS_ID = TNPH.ADDRESS_ID)
		          LEFT JOIN DEV_NB.T_GENDER TG
		            ON (TG.GENDER_CODE = TC.CUSTOMER_GENDER)
		          LEFT JOIN DEV_NB.T_CERTI_TYPE TCT
		            ON (TCT.CODE = TC.CUSTOMER_CERT_TYPE)
		          LEFT JOIN DEV_NB.T_JOB_CODE TJC
		            ON (TJC.JOB_CODE = TC.JOB_CODE) 
		            WHERE 1 = 1]]>
	    <include refid="queryCdsPolicyBaseInfoPageOrganCode"/>
	    <include refid="queryCdsPolicyBaseInfoPageStartDate"/>
	    <include refid="queryCdsPolicyBaseInfoPageEndDate"/>
	    <include refid="queryCdsPolicyBaseInfoPageChannelType"/>
	</select>
	
	<select id="NB_queryCdsPolicyBaseinfo" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[SELECT B.SALES_CHANNEL_NAME,
	                   B.ORDER_ID,
				       B.ORGAN_CODE,
				       B.ORGAN_NAME,
				       B.COOPERATION_NAME AGENCY_ORGAN_NAME,
               		   B.COOPERATION_CODE AGENCY_ORGAN_CODE,
				       B.AGENT_NAME,
				       B.AGENT_CODE,
				       B.AGENT_NO,
				       B.CURR_AGENT_NAME,
                       B.CURR_AGENT_CODE,
                       B.CURR_AGENT_NO,
				       B.POLICY_CODE,
				       B.PRODUCT_CODE busi_prod_code,
				       B.PRODUCT_NAME_SYS busi_prod_name,
				       B.CHARGE_YEAR,
				       B.TOTAL_PREM_AF,
				       B.ISSUE_DATE,
				       B.BRANCH_RECEIVE_DATE,
				       B.CUSTOMER_NAME,
				       B.AGE,
				       B.GENDER_DESC,
				       B.TYPE customer_cert_type,
				       B.CUSTOMER_CERTI_CODE,
				       B.ADDRESS,
				       B.JOB_NAME,
				       B.COMPANY_NAME,
				       B.FIXED_TEL,
				       B.MOBILE_TEL,
				       B.EMAIL 
				 FROM (SELECT ROWNUM RN,TSC.SALES_CHANNEL_NAME,
				               TNCP.ORDER_ID,
				               TNCM.ORGAN_CODE,
				               TUO.ORGAN_NAME,
				               TAGE.COOPERATION_CODE,
                       		   TAGE.COOPERATION_NAME,
				               TA.AGENT_NAME,
				               TNCA.AGENT_CODE,
				               TA.AGENT_NO,
				               (SELECT A.AGENT_NAME
                                   FROM DEV_PAS.T_CONTRACT_AGENT CA
                                   INNER JOIN DEV_PAS.T_AGENT A
                                   ON CA.AGENT_CODE = A.AGENT_CODE
                                 WHERE 1 = 1
                                    AND CA.IS_CURRENT_AGENT = '1'
                                    AND CA.POLICY_CODE=TNCM.POLICY_CODE 
                                    AND ROWNUM = 1) as CURR_AGENT_NAME,
                               (SELECT CA.AGENT_CODE
                                 FROM DEV_PAS.T_CONTRACT_AGENT CA
                                 WHERE 1 = 1
                                    AND CA.IS_CURRENT_AGENT = '1'
                                    AND CA.POLICY_CODE=TNCM.POLICY_CODE 
                                    AND ROWNUM = 1) as CURR_AGENT_CODE,
                               (SELECT A.AGENT_NO 
                                   FROM DEV_PAS.T_CONTRACT_AGENT CA
                                   INNER JOIN DEV_PAS.T_AGENT A
                                   ON CA.AGENT_CODE = A.AGENT_CODE
                                  WHERE 1 = 1
                                    AND CA.IS_CURRENT_AGENT = '1'
                                    AND CA.POLICY_CODE=TNCM.POLICY_CODE
                                    AND ROWNUM = 1 ) as CURR_AGENT_NO,
				               TNCM.POLICY_CODE,
				               TNCP.PRODUCT_CODE,
				               TBP.PRODUCT_NAME_SYS,
				               DECODE(TNCPT.PREM_FREQ,'1','一次交清','2',TNCPT.CHARGE_YEAR || '月','5',TNCPT.CHARGE_YEAR || '年') CHARGE_YEAR,
				               TNCPT.TOTAL_PREM_AF,
				               TNCM.ISSUE_DATE,
				               TNPA.BRANCH_RECEIVE_DATE,
				               TC.CUSTOMER_NAME,
				               FLOOR((SYSDATE - TC.CUSTOMER_BIRTHDAY) / 365) AGE,
				               TG.GENDER_DESC,
				               TCT.TYPE,
				               TC.CUSTOMER_CERTI_CODE,
				               TAD.ADDRESS,
				               TJC.JOB_NAME,
				               TC.COMPANY_NAME,
				               TAD.FIXED_TEL,
				               TAD.MOBILE_TEL,
				               TAD.EMAIL
				          FROM DEV_NB.T_NB_CONTRACT_MASTER TNCM
				          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TAGE ON TAGE.APPLY_CODE = TNCM.APPLY_CODE
				          LEFT JOIN DEV_NB.T_SALES_CHANNEL TSC
				            ON (TSC.SALES_CHANNEL_CODE = TNCM.CHANNEL_TYPE)
				          LEFT JOIN DEV_NB.T_UDMP_ORG TUO
				            ON (TUO.ORGAN_CODE = TNCM.ORGAN_CODE)
				          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TNCA
				            ON (TNCA.APPLY_CODE = TNCM.APPLY_CODE)
				          LEFT JOIN DEV_NB.T_AGENT TA
				            ON (TA.AGENT_CODE = TNCA.AGENT_CODE)
				          LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP
				            ON (TNCP.APPLY_CODE = TNCM.APPLY_CODE)
				          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
				            ON (TBP.PRODUCT_CODE_SYS = TNCP.PRODUCT_CODE)
				          LEFT JOIN DEV_NB.T_NB_CONTRACT_PRODUCT TNCPT
				            ON (TNCPT.BUSI_ITEM_ID = TNCP.BUSI_ITEM_ID)
				          LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT TNPA
				            ON (TNPA.POLICY_ID = TNCM.POLICY_ID)
				          LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER TNPH
				            ON (TNPH.APPLY_CODE = TNCM.APPLY_CODE)
				          LEFT JOIN DEV_PAS.T_CUSTOMER TC
				            ON (TC.CUSTOMER_ID = TNPH.CUSTOMER_ID)
				          LEFT JOIN DEV_NB.T_ADDRESS TAD
				            ON (TAD.ADDRESS_ID = TNPH.ADDRESS_ID)
				          LEFT JOIN DEV_NB.T_GENDER TG
				            ON (TG.GENDER_CODE = TC.CUSTOMER_GENDER)
				          LEFT JOIN DEV_NB.T_CERTI_TYPE TCT
				            ON (TCT.CODE = TC.CUSTOMER_CERT_TYPE)
				          LEFT JOIN DEV_NB.T_JOB_CODE TJC
				            ON (TJC.JOB_CODE = TC.JOB_CODE) WHERE 1 = 1 AND ROWNUM <= #{LESS_NUM}]]>
	    <include refid="queryCdsPolicyBaseInfoPageOrganCode"/>
	    <include refid="queryCdsPolicyBaseInfoPageStartDate"/>
	    <include refid="queryCdsPolicyBaseInfoPageEndDate"/>
	    <include refid="queryCdsPolicyBaseInfoPageChannelType"/>
	    <![CDATA[  ORDER BY  TNCP.APPLY_CODE,TNCP.ORDER_ID ]]>
	   <![CDATA[ ) B  WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="NB_queryCdsPolicyBaseinfoAll" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[SELECT TSC.SALES_CHANNEL_NAME,
               TNCM.ORGAN_CODE,
               TUO.ORGAN_NAME,
               TAGE.COOPERATION_NAME AGENCY_ORGAN_NAME,
               TAGE.COOPERATION_CODE AGENCY_ORGAN_CODE,
               TA.AGENT_NAME,
               TNCA.AGENT_CODE,
               TA.AGENT_NO,
               (SELECT A.AGENT_NAME
                    FROM DEV_PAS.T_CONTRACT_AGENT CA
                    INNER JOIN DEV_PAS.T_AGENT A
                        ON CA.AGENT_CODE = A.AGENT_CODE
                    WHERE 1 = 1
                        AND CA.IS_CURRENT_AGENT = '1'
                        AND CA.POLICY_CODE=TNCM.POLICY_CODE 
                        AND ROWNUM = 1) as CURR_AGENT_NAME,
                (SELECT CA.AGENT_CODE
                     FROM DEV_PAS.T_CONTRACT_AGENT CA
                     WHERE 1 = 1
                         AND CA.IS_CURRENT_AGENT = '1'
                         AND CA.POLICY_CODE=TNCM.POLICY_CODE 
                         AND ROWNUM = 1) as CURR_AGENT_CODE,
                (SELECT A.AGENT_NO 
                     FROM DEV_PAS.T_CONTRACT_AGENT CA
                     INNER JOIN DEV_PAS.T_AGENT A
                     ON CA.AGENT_CODE = A.AGENT_CODE
                     WHERE 1 = 1
                         AND CA.IS_CURRENT_AGENT = '1'
                         AND CA.POLICY_CODE=TNCM.POLICY_CODE
                         AND ROWNUM = 1 ) as CURR_AGENT_NO,
               TNCM.POLICY_CODE,
               TNCP.PRODUCT_CODE busi_prod_code,
               TBP.PRODUCT_NAME_SYS busi_prod_name,
               DECODE(TNCPT.PREM_FREQ,'1','一次交清','2',TNCPT.CHARGE_YEAR || '月','5',TNCPT.CHARGE_YEAR || '年') CHARGE_YEAR,
               TNCPT.TOTAL_PREM_AF,
               TNCM.ISSUE_DATE,
               TNPA.BRANCH_RECEIVE_DATE,
               TC.CUSTOMER_NAME,
               FLOOR((SYSDATE - TC.CUSTOMER_BIRTHDAY) / 365) AGE,
               TG.GENDER_DESC,
               TCT.TYPE customer_cert_type,
               TC.CUSTOMER_CERTI_CODE,
               TAD.ADDRESS,
               TJC.JOB_NAME,
               TC.COMPANY_NAME,
               TAD.FIXED_TEL,
               TAD.MOBILE_TEL,
               TAD.EMAIL
          FROM DEV_NB.T_NB_CONTRACT_MASTER TNCM
          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TAGE ON TAGE.APPLY_CODE = TNCM.APPLY_CODE
          LEFT JOIN DEV_NB.T_SALES_CHANNEL TSC
            ON (TSC.SALES_CHANNEL_CODE = TNCM.CHANNEL_TYPE)
          LEFT JOIN DEV_NB.T_UDMP_ORG TUO
            ON (TUO.ORGAN_CODE = TNCM.ORGAN_CODE)
          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TNCA
            ON (TNCA.APPLY_CODE = TNCM.APPLY_CODE)
          LEFT JOIN DEV_NB.T_AGENT TA
            ON (TA.AGENT_CODE = TNCA.AGENT_CODE)
          LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD TNCP
            ON (TNCP.APPLY_CODE = TNCM.APPLY_CODE)
          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
            ON (TBP.PRODUCT_CODE_SYS = TNCP.PRODUCT_CODE)
          LEFT JOIN DEV_NB.T_NB_CONTRACT_PRODUCT TNCPT
            ON (TNCPT.BUSI_ITEM_ID = TNCP.BUSI_ITEM_ID)
          LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT TNPA
            ON (TNPA.POLICY_ID = TNCM.POLICY_ID)
          LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER TNPH
            ON (TNPH.APPLY_CODE = TNCM.APPLY_CODE)
          LEFT JOIN DEV_PAS.T_CUSTOMER TC
            ON (TC.CUSTOMER_ID = TNPH.CUSTOMER_ID)
          LEFT JOIN DEV_NB.T_ADDRESS TAD
            ON (TAD.ADDRESS_ID = TNPH.ADDRESS_ID)
          LEFT JOIN DEV_NB.T_GENDER TG
            ON (TG.GENDER_CODE = TC.CUSTOMER_GENDER)
          LEFT JOIN DEV_NB.T_CERTI_TYPE TCT
            ON (TCT.CODE = TC.CUSTOMER_CERT_TYPE)
          LEFT JOIN DEV_NB.T_JOB_CODE TJC
            ON (TJC.JOB_CODE = TC.JOB_CODE) WHERE 1 = 1]]>
	    <include refid="queryCdsPolicyBaseInfoPageOrganCode"/>
	    <include refid="queryCdsPolicyBaseInfoPageStartDate"/>
	    <include refid="queryCdsPolicyBaseInfoPageEndDate"/>
	    <include refid="queryCdsPolicyBaseInfoPageChannelType"/>
	    ORDER BY TNCP.APPLY_CODE , TNCP.ORDER_ID 	 
	</select>
  
	<select id="queryElecGraphTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(1) FROM (SELECT B.RN,B.ORGAN_CODE,
       B.ORGAN_NAME,
       B.APPLY_CODE,
       B.POLICY_CODE,
       B.QT_STATUS,
       B.CREATE_TIME,
       B.AGENT_CODE,
       B.AGENT_NAME,
       B.AGENT_LEVEL,
       B.AGENT_LEVEL_DESC,
       B.SUBMIT_CHANNEL,
       B.SUBINPUT_TYPE,
       B.APPLY_DATE,
       B.ISSUE_DATE,
       B.QA_TYPE,
       B.CARD_CODE,
       B.QT_USER_NAME,
       B.TYPE_NAME,
       B.STATUS_NAME,
       B.CHANNEL_NAME,
       B.CHANNEL_TYPE
  FROM (SELECT ROWNUM RN,
               TCM.ORGAN_CODE,
               TUO.ORGAN_NAME,
               TQT.APPLY_CODE,
               TCM.POLICY_CODE,
               TQT.QT_STATUS,
               TQT.CREATE_TIME,
               TCA.AGENT_CODE,
               TA.AGENT_NAME,
               TA.AGENT_LEVEL,
               TAL.AGENT_LEVEL_DESC,
               TCM.SUBMIT_CHANNEL,
               TCM.SUBINPUT_TYPE,
               TCM.APPLY_DATE,
               TCM.ISSUE_DATE,
               TQT.QA_TYPE,
               TQT.CARD_CODE,
               (CASE WHEN TQT.IS_AVOID_CHECK =1 THEN '系统操作' 
                       ELSE TUU.REAL_NAME || '-' || TUU.USER_NAME
                       END)  QT_USER_NAME,
                   TNQT.TYPE_NAME,
                   TQS.STATUS_NAME,
                   TSC.SALES_CHANNEL_NAME AS CHANNEL_NAME,
                   TCM.CHANNEL_TYPE
          FROM DEV_NB.T_NB_QT_TASK TQT
          LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM
            ON (TCM.APPLY_CODE = TQT.APPLY_CODE)
          LEFT JOIN DEV_NB.T_UDMP_ORG TUO
            ON (TUO.ORGAN_CODE = TCM.ORGAN_CODE)
          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA
            ON (TCA.APPLY_CODE = TQT.APPLY_CODE)
          LEFT JOIN DEV_NB.T_AGENT TA
            ON (TA.AGENT_CODE = TCA.AGENT_CODE)
          LEFT JOIN DEV_NB.T_AGENT_LEVEL TAL
            ON (TAL.AGENT_LEVEL_CODE = TA.AGENT_LEVEL)
          LEFT JOIN DEV_NB.T_UDMP_USER TUU
            ON (TUU.USER_ID = TQT.QT_USER)
          LEFT JOIN DEV_NB.T_NB_QT_TYPE TNQT
            ON (TNQT.TYPE_CODE = TQT.QA_TYPE)
          LEFT JOIN DEV_NB.T_QT_STATUS TQS
            ON (TQS.STATUS_CODE = TQT.QT_STATUS)
          LEFT JOIN DEV_NB.T_SALES_CHANNEL TSC ON TSC.SALES_CHANNEL_CODE = TCM.CHANNEL_TYPE
         WHERE 1 = 1
           AND TQT.QA_TYPE IN ('4', '5', '7')) B WHERE  1 = 1 ]]>
		<include refid="NB_queryElecAutographCondition"/>
		<![CDATA[ ) ]]>
	</select>
	
	<select id="queryElecGraphList" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[SELECT C.RN,C.* FROM (SELECT ROWNUM RN,B.ORGAN_CODE,
       B.ORGAN_NAME,
       B.APPLY_CODE,
       B.POLICY_CODE,
       B.QT_STATUS,
       B.CREATE_TIME,
       B.QT_TIME,
       B.NEW_QT_TIME,
       B.AGENT_CODE,
       B.AGENT_NAME,
       B.AGENT_LEVEL,
       B.AGENT_LEVEL_DESC,
       B.SUBMIT_CHANNEL,
       B.SUBINPUT_TYPE,
       B.APPLY_DATE,
       B.ISSUE_DATE,
       FLOOR(NVL(NVL(B.QA_AUDIT_TIME,B.AVOID_CHECK_TIME),NVL(B.AVOID_CHECK_TIME, SYSDATE)) - 
       TO_DATE(TO_CHAR(B.CREATE_TIME, 'YYYY-MM-DD'), 'YYYY-MM-DD') - 
       (SELECT COUNT(1) FROM DEV_NB.T_UDMP_WORKDAYS TUW WHERE 
	   TUW.ESPECIAL_DAY BETWEEN TO_CHAR(B.CREATE_TIME,'yyyyMMdd') AND 
   	   TO_CHAR(NVL(NVL(B.QA_AUDIT_TIME,B.AVOID_CHECK_TIME),NVL(B.AVOID_CHECK_TIME, SYSDATE)),'yyyyMMdd'))) PRESCRIPTION_DAYS,
       FLOOR(NVL(NVL(B.QA_AUDIT_TIME,B.AVOID_CHECK_TIME),NVL(B.AVOID_CHECK_TIME, SYSDATE)) - B.QA_APPLY_TIME - (SELECT COUNT(1) FROM DEV_NB.T_UDMP_WORKDAYS TUW WHERE 
   	   TUW.ESPECIAL_DAY BETWEEN TO_CHAR(B.QA_APPLY_TIME,'yyyyMMdd') AND 
       TO_CHAR(NVL(NVL(B.QA_AUDIT_TIME,B.AVOID_CHECK_TIME),NVL(B.AVOID_CHECK_TIME, SYSDATE)),'yyyyMMdd'))) PRE_PRESCRIPTION_DAYS,
       B.QA_TYPE,
       B.CARD_CODE,
       B.QT_USER_NAME,
       B.QA_AUDIT_TIME,
       B.QA_PASS_TIME,
       B.QA_AUDIT_NOPASS_REASON, 
       B.TYPE_NAME,
       B.STATUS_NAME,
       B.CHANNEL_NAME,
       B.SALES_ORGAN_NAME,
       B.CHANNEL_TYPE,
       B.IS_REMOTE_AUTOGRAPH,
       B.JOINTLY_INSURED_POLICY,
       B.TASK_ID
  FROM (SELECT TCM.ORGAN_CODE,
               TUO.ORGAN_NAME,
               TQT.APPLY_CODE,
               TCM.POLICY_CODE,
               TQT.QT_STATUS,
               TQT.CREATE_TIME,
               (SELECT TNE.INSERT_TIME
                  FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE
                 WHERE TNE.ES_QUOALI_ID =
                       (SELECT MAX(TNE1.ES_QUOALI_ID)
                          FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE1
                         WHERE TNE1.TASK_ID = TQT.TASK_ID)) NEW_QT_TIME,
               TQT.QT_TIME,
               TCA.AGENT_CODE,
               TA.AGENT_NAME,
               TA.AGENT_LEVEL,
               TAL.AGENT_LEVEL_DESC,
               TCM.SUBMIT_CHANNEL,
               TCM.SUBINPUT_TYPE,
               TCM.APPLY_DATE,
               TCM.ISSUE_DATE,
               TQT.QA_TYPE,
               TQT.CARD_CODE,
               (CASE WHEN TQT.IS_AVOID_CHECK =1 THEN '系统操作' 
                       ELSE 
				(SELECT LISTAGG(ESQ2.REAL_NAME || '-' || ESQ2.USER_NAME,';')WITHIN GROUP(ORDER BY ESQ2.REAL_NAME) 
           		  FROM (SELECT distinct ESQ.TASK_ID,UU.REAL_NAME,UU.USER_NAME FROM DEV_NB.T_NB_ESQUOALITYDECISON ESQ INNER JOIN DEV_NB.T_UDMP_USER UU
                	ON ESQ.INSERT_BY = UU.USER_ID) ESQ2
           		 WHERE ESQ2.TASK_ID = TQT.TASK_ID)
                       END)  AS QT_USER_NAME,
               (SELECT TO_DATE(TO_CHAR(TNE.INSERT_TIME, 'YYYY-MM-DD'), 'YYYY-MM-DD')
                  FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE
                 WHERE TNE.ES_QUOALI_ID =
                       (SELECT MIN(TNE1.ES_QUOALI_ID)
                          FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE1
                         WHERE TNE1.TASK_ID = TQT.TASK_ID)) QA_AUDIT_TIME,
               (SELECT TO_DATE(TO_CHAR(TNE2.INSERT_TIME, 'YYYY-MM-DD'), 'YYYY-MM-DD')
                  FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE2
                 WHERE TNE2.ES_QUOALI_ID =
                       (SELECT MAX(TNE3.ES_QUOALI_ID)
                          FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE3
                         WHERE TNE3.TASK_ID = TQT.TASK_ID
                           AND TNE3.ES_QT_RESULT = '1')) QA_PASS_TIME,
               (SELECT TNE4.REMARK
                  FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE4
                 WHERE TNE4.ES_QUOALI_ID =
                       (SELECT MAX(TNE5.ES_QUOALI_ID)
                          FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE5
                         WHERE TNE5.TASK_ID = TQT.TASK_ID
                           AND TNE5.ES_QT_RESULT = '0')) QA_AUDIT_NOPASS_REASON,
               (SELECT TO_DATE(TO_CHAR(MAX(TPP.START_TIME), 'YYYY-MM-DD'), 'YYYY-MM-DD')
                  FROM DEV_NB.T_PROPOSAL_PROCESS TPP
                 WHERE TPP.APPLY_CODE = TQT.APPLY_CODE
                   AND TPP.PROCESS_STEP = '169') QA_APPLY_TIME,
                   (SELECT NQT.QT_TIME FROM DEV_NB.T_NB_QT_TASK NQT WHERE NQT.TASK_ID = TQT.TASK_ID AND NQT.IS_AVOID_CHECK = '1'
                	AND NQT.QT_STATUS = '7') AS AVOID_CHECK_TIME,
                   TNQT.TYPE_NAME,
                   (SELECT (CASE WHEN  TQS.STATUS_CODE = 9
                   THEN '不通过' ELSE TQS.STATUS_NAME END )  FROM  DEV_NB.T_QT_STATUS TQS
             		WHERE TQS.STATUS_CODE = TQT.QT_STATUS) AS STATUS_NAME,
                   TSC.SALES_CHANNEL_NAME AS CHANNEL_NAME,
                   TCM.CHANNEL_TYPE,
                   SO3.SALES_ORGAN_NAME||'-'||SO2.SALES_ORGAN_NAME||'-'||SO1.SALES_ORGAN_NAME AS SALES_ORGAN_NAME,
                   (CASE WHEN TQT.QA_TYPE ='4' THEN 
                     CASE WHEN TCM.IS_REMOTE_AUTOGRAPH ='1' THEN '是' ELSE '否'END  
                     ELSE '' END )AS IS_REMOTE_AUTOGRAPH,
                     (CASE
                         WHEN TCM.JOINTLY_INSURED_TYPE = '1' THEN
                          (SELECT LISTAGG(APPLY_CODE, '/') WITHIN GROUP(ORDER BY APPLY_CODE) AS A1
                             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
                            WHERE JIP.MAIN_APPLY_CODE = TCM.APPLY_CODE
                            AND JIP.APPLY_CODE  in (SELECT A3.APPLY_CODE  FROM DEV_NB.T_NB_CONTRACT_MASTER A3 WHERE A3.LIABILITY_STATE  not in ('2','3','4') AND A3.JOINTLY_INSURED_TYPE = '2')
                            AND JIP.MAIN_APPLY_CODE not in
								(SELECT MAS1.APPLY_CODE
									FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
								LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
									ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
								WHERE MAS1.Jointly_Insured_Type = '1'
									AND MAS1.Multi_Mainrisk_Flag = '1'
									AND PRO1.PRODUCT_CODE = '00822000'
									AND PRO1.DECISION_CODE in ('40', '50')))
                         WHEN TCM.JOINTLY_INSURED_TYPE = '2' THEN
                          (SELECT JIP.MAIN_APPLY_CODE AS A2
                             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
                            WHERE JIP.APPLY_CODE = TCM.APPLY_CODE
                            AND JIP.MAIN_APPLY_CODE not in
								(SELECT MAS1.APPLY_CODE
									FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
								LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
									ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
								WHERE MAS1.Jointly_Insured_Type = '1'
									AND MAS1.Multi_Mainrisk_Flag = '1'
									AND PRO1.PRODUCT_CODE = '00822000'
									AND PRO1.DECISION_CODE in ('40', '50')))
                         ELSE
                          ''
                       END) AS JOINTLY_INSURED_POLICY,
                       TQT.TASK_ID
          FROM DEV_NB.T_NB_QT_TASK TQT
          LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM
            ON (TCM.APPLY_CODE = TQT.APPLY_CODE)
          LEFT JOIN DEV_NB.T_UDMP_ORG TUO
            ON (TUO.ORGAN_CODE = TCM.ORGAN_CODE)
          LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER TPH
            ON (TPH.APPLY_CODE = TQT.APPLY_CODE)
          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA
            ON (TCA.APPLY_CODE = TQT.APPLY_CODE)
          LEFT JOIN DEV_PAS.T_CUSTOMER TC
            ON (TC.CUSTOMER_ID = TPH.CUSTOMER_ID)
          LEFT JOIN DEV_NB.T_AGENT TA
            ON (TA.AGENT_CODE = TCA.AGENT_CODE)
          LEFT JOIN DEV_NB.T_AGENT_LEVEL TAL
            ON (TAL.AGENT_LEVEL_CODE = TA.AGENT_LEVEL)
          LEFT JOIN DEV_NB.T_NB_QT_TYPE TNQT
            ON (TNQT.TYPE_CODE = TQT.QA_TYPE)
          LEFT JOIN DEV_NB.T_SALES_CHANNEL TSC ON TSC.SALES_CHANNEL_CODE = TCM.CHANNEL_TYPE
          LEFT JOIN DEV_NB.T_SALES_ORGAN SO1 ON SO1.SALES_ORGAN_CODE=TA.SALES_ORGAN_CODE AND SO1.ORGAN_LEVEL_CODE=3
          LEFT JOIN DEV_NB.T_SALES_ORGAN SO2 ON SO1.PARENT_CODE = SO2.SALES_ORGAN_CODE AND SO2.ORGAN_LEVEL_CODE=2
          LEFT JOIN DEV_NB.T_SALES_ORGAN SO3 ON SO2.PARENT_CODE = SO3.SALES_ORGAN_CODE AND SO3.ORGAN_LEVEL_CODE=1
          
         WHERE 1 = 1
           AND TQT.QA_TYPE IN ('4', '5', '7') 
           ) B  WHERE ROWNUM <= #{LESS_NUM} ]]>
		      <include refid="NB_queryElecAutographCondition"/>
		      <![CDATA[ ) C  WHERE C.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="queryAllElecGraph" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[SELECT C.RN,C.* FROM (SELECT ROWNUM RN,B.ORGAN_CODE, 
       B.ORGAN_NAME,
       B.APPLY_CODE,
       B.POLICY_CODE,
       B.QT_STATUS,
       B.CREATE_TIME,
       B.NEW_QT_TIME,
       B.QT_TIME,
       B.AGENT_CODE,
       B.AGENT_NAME,
       B.AGENT_LEVEL,
       B.AGENT_LEVEL_DESC,
       B.SUBMIT_CHANNEL,
       B.SUBINPUT_TYPE,
       B.APPLY_DATE,
       B.ISSUE_DATE,
       FLOOR(NVL(NVL(B.QA_AUDIT_TIME,B.AVOID_CHECK_TIME),NVL(B.AVOID_CHECK_TIME, SYSDATE)) - 
       TO_DATE(TO_CHAR(B.CREATE_TIME, 'YYYY-MM-DD'), 'YYYY-MM-DD') - 
       (SELECT COUNT(1) FROM DEV_NB.T_UDMP_WORKDAYS TUW WHERE 
	   TUW.ESPECIAL_DAY BETWEEN TO_CHAR(B.CREATE_TIME,'yyyyMMdd') AND 
   	   TO_CHAR(NVL(NVL(B.QA_AUDIT_TIME,B.AVOID_CHECK_TIME),NVL(B.AVOID_CHECK_TIME, SYSDATE)),'yyyyMMdd'))) PRESCRIPTION_DAYS,
       FLOOR(NVL(NVL(B.QA_AUDIT_TIME,B.AVOID_CHECK_TIME),NVL(B.AVOID_CHECK_TIME, SYSDATE)) - B.QA_APPLY_TIME - (SELECT COUNT(1) FROM DEV_NB.T_UDMP_WORKDAYS TUW WHERE 
   	   TUW.ESPECIAL_DAY BETWEEN TO_CHAR(B.QA_APPLY_TIME,'yyyyMMdd') AND 
       TO_CHAR(NVL(NVL(B.QA_AUDIT_TIME,B.AVOID_CHECK_TIME),NVL(B.AVOID_CHECK_TIME, SYSDATE)),'yyyyMMdd'))) PRE_PRESCRIPTION_DAYS,
       B.QA_TYPE,
       B.CARD_CODE,
       B.QT_USER_NAME,
       B.QA_AUDIT_TIME,
       B.QA_PASS_TIME,
       B.QA_AUDIT_NOPASS_REASON, 
       B.TYPE_NAME,
       B.STATUS_NAME,
       B.CHANNEL_NAME,
       B.SALES_ORGAN_NAME,
       B.CHANNEL_TYPE,
       B.IS_REMOTE_AUTOGRAPH,
       B.JOINTLY_INSURED_POLICY,
       B.TASK_ID
  FROM (SELECT TCM.ORGAN_CODE,
               TUO.ORGAN_NAME,
               TQT.APPLY_CODE,
               TCM.POLICY_CODE,
               TQT.QT_STATUS,
               TQT.CREATE_TIME,
               (SELECT TNE.INSERT_TIME
                  FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE
                 WHERE TNE.ES_QUOALI_ID =
                       (SELECT MAX(TNE1.ES_QUOALI_ID)
                          FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE1
                         WHERE TNE1.TASK_ID = TQT.TASK_ID)) NEW_QT_TIME,
               TQT.QT_TIME,
               TCA.AGENT_CODE,
               TA.AGENT_NAME,
               TA.AGENT_LEVEL,
               TAL.AGENT_LEVEL_DESC,
               TCM.SUBMIT_CHANNEL,
               TCM.SUBINPUT_TYPE,
               TCM.APPLY_DATE,
               TCM.ISSUE_DATE,
               TQT.QA_TYPE,
               TQT.CARD_CODE,
               (CASE WHEN TQT.IS_AVOID_CHECK =1 THEN '系统操作' 
                       ELSE 
				(SELECT LISTAGG(ESQ2.REAL_NAME || '-' || ESQ2.USER_NAME,';')WITHIN GROUP(ORDER BY ESQ2.REAL_NAME) 
           		  FROM (SELECT distinct ESQ.TASK_ID,UU.REAL_NAME,UU.USER_NAME FROM DEV_NB.T_NB_ESQUOALITYDECISON ESQ INNER JOIN DEV_NB.T_UDMP_USER UU
                	ON ESQ.INSERT_BY = UU.USER_ID) ESQ2
           		 WHERE ESQ2.TASK_ID = TQT.TASK_ID)
                       END)  QT_USER_NAME,
               (SELECT TO_DATE(TO_CHAR(TNE.INSERT_TIME, 'YYYY-MM-DD'), 'YYYY-MM-DD')
                  FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE
                 WHERE TNE.ES_QUOALI_ID =
                       (SELECT MIN(TNE1.ES_QUOALI_ID)
                          FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE1
                         WHERE TNE1.TASK_ID = TQT.TASK_ID)) QA_AUDIT_TIME,
               (SELECT TO_DATE(TO_CHAR(TNE2.INSERT_TIME, 'YYYY-MM-DD'), 'YYYY-MM-DD')
                  FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE2
                 WHERE TNE2.ES_QUOALI_ID =
                       (SELECT MAX(TNE3.ES_QUOALI_ID)
                          FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE3
                         WHERE TNE3.TASK_ID = TQT.TASK_ID
                           AND TNE3.ES_QT_RESULT = '1')) QA_PASS_TIME,
               (SELECT NQT.QT_TIME FROM DEV_NB.T_NB_QT_TASK NQT WHERE NQT.TASK_ID = TQT.TASK_ID AND NQT.IS_AVOID_CHECK = '1'
                	AND NQT.QT_STATUS = '7') AS AVOID_CHECK_TIME,
               (SELECT TNE4.REMARK
                  FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE4
                 WHERE TNE4.ES_QUOALI_ID =
                       (SELECT MAX(TNE5.ES_QUOALI_ID)
                          FROM DEV_NB.T_NB_ESQUOALITYDECISON TNE5
                         WHERE TNE5.TASK_ID = TQT.TASK_ID
                           AND TNE5.ES_QT_RESULT = '0')) QA_AUDIT_NOPASS_REASON,
               (SELECT TO_DATE(TO_CHAR(MAX(TPP.START_TIME), 'YYYY-MM-DD'), 'YYYY-MM-DD')
                  FROM DEV_NB.T_PROPOSAL_PROCESS TPP
                 WHERE TPP.APPLY_CODE = TQT.APPLY_CODE
                   AND TPP.PROCESS_STEP = '169') QA_APPLY_TIME,
                   TNQT.TYPE_NAME,
                   (SELECT (CASE WHEN  TQS.STATUS_CODE = 9
                   THEN '不通过' ELSE TQS.STATUS_NAME END )  FROM  DEV_NB.T_QT_STATUS TQS
             WHERE TQS.STATUS_CODE = TQT.QT_STATUS) AS STATUS_NAME,
                   TSC.SALES_CHANNEL_NAME AS CHANNEL_NAME,
                   TCM.CHANNEL_TYPE,
                   SO3.SALES_ORGAN_NAME||'-'||SO2.SALES_ORGAN_NAME||'-'||SO1.SALES_ORGAN_NAME AS SALES_ORGAN_NAME,
                   (CASE WHEN TQT.QA_TYPE ='4' THEN 
                     CASE WHEN TCM.IS_REMOTE_AUTOGRAPH ='1' THEN '是' ELSE '否'END  
                     ELSE '' END )AS IS_REMOTE_AUTOGRAPH,
                     (CASE
                         WHEN TCM.JOINTLY_INSURED_TYPE = '1' THEN
                          (SELECT LISTAGG(APPLY_CODE, '/') WITHIN GROUP(ORDER BY APPLY_CODE) AS A1
                             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
                            WHERE JIP.MAIN_APPLY_CODE = TCM.APPLY_CODE
                            AND JIP.APPLY_CODE  in (SELECT A3.APPLY_CODE  FROM DEV_NB.T_NB_CONTRACT_MASTER A3 WHERE A3.LIABILITY_STATE  not in ('2','3','4') AND A3.JOINTLY_INSURED_TYPE = '2')
                            AND JIP.MAIN_APPLY_CODE not in
								(SELECT MAS1.APPLY_CODE
									FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
								LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
									ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
								WHERE MAS1.Jointly_Insured_Type = '1'
									AND MAS1.Multi_Mainrisk_Flag = '1'
									AND PRO1.PRODUCT_CODE = '00822000'
									AND PRO1.DECISION_CODE in ('40', '50')))
                         WHEN TCM.JOINTLY_INSURED_TYPE = '2' THEN
                          (SELECT JIP.MAIN_APPLY_CODE AS A2
                             FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY JIP
                            WHERE JIP.APPLY_CODE = TCM.APPLY_CODE
                            AND JIP.MAIN_APPLY_CODE not in
								(SELECT MAS1.APPLY_CODE
									FROM DEV_NB.T_NB_CONTRACT_MASTER MAS1
								LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD PRO1
									ON MAS1.APPLY_CODE = PRO1.APPLY_CODE
								WHERE MAS1.Jointly_Insured_Type = '1'
									AND MAS1.Multi_Mainrisk_Flag = '1'
									AND PRO1.PRODUCT_CODE = '00822000'
									AND PRO1.DECISION_CODE in ('40', '50')))
                         ELSE
                          ''
                       END) AS JOINTLY_INSURED_POLICY,
                       TQT.TASK_ID
          FROM DEV_NB.T_NB_QT_TASK TQT
          LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM
            ON (TCM.APPLY_CODE = TQT.APPLY_CODE)
          LEFT JOIN DEV_NB.T_UDMP_ORG TUO
            ON (TUO.ORGAN_CODE = TCM.ORGAN_CODE)          
          LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
            ON (TCA.APPLY_CODE = TQT.APPLY_CODE)
          LEFT JOIN DEV_NB.T_AGENT TA
            ON (TA.AGENT_CODE = TCA.AGENT_CODE)
          LEFT JOIN DEV_NB.T_AGENT_LEVEL TAL
            ON (TAL.AGENT_LEVEL_CODE = TA.AGENT_LEVEL)
          LEFT JOIN DEV_NB.T_NB_QT_TYPE TNQT
            ON (TNQT.TYPE_CODE = TQT.QA_TYPE)
          LEFT JOIN DEV_NB.T_SALES_CHANNEL TSC ON TSC.SALES_CHANNEL_CODE = TCM.CHANNEL_TYPE
          LEFT JOIN DEV_NB.T_SALES_ORGAN SO1 ON SO1.SALES_ORGAN_CODE=TA.SALES_ORGAN_CODE AND SO1.ORGAN_LEVEL_CODE=3
          LEFT JOIN DEV_NB.T_SALES_ORGAN SO2 ON SO1.PARENT_CODE = SO2.SALES_ORGAN_CODE AND SO2.ORGAN_LEVEL_CODE=2
          LEFT JOIN DEV_NB.T_SALES_ORGAN SO3 ON SO2.PARENT_CODE = SO3.SALES_ORGAN_CODE AND SO3.ORGAN_LEVEL_CODE=1
         WHERE 1 = 1
           AND TQT.QA_TYPE IN ('4', '5', '7') 
           ) B  WHERE  1 = 1 ]]>
		      <include refid="NB_queryElecAutographCondition"/>
		      <![CDATA[ ) C]]>
	</select>
	
	<!-- 查询电子签名质检不通过原因 -->
	<select id="NB_queryEleclDecison" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT A.REMARK AS QA_AUDIT_NOPASS_REASON,A.INSERT_TIMESTAMP AS QA_AUDIT_TIME FROM DEV_NB.T_NB_ESQUOALITYDECISON A
		WHERE A.ES_QT_RESULT = '0' AND A.APPLY_CODE = #{apply_code} AND A.TASK_ID = #{task_id}
		ORDER BY A.INSERT_TIMESTAMP DESC 
	</select>
	
	<!-- 销售渠道多选 -->
  <select id="NB_querySalesChannel" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
      SELECT SALES_CHANNEL_CODE,SALES_CHANNEL_NAME,IS_VALID FROM DEV_NB.T_SALES_CHANNEL where 1=1]]>
      <include refid="SalesChannelSqlWhere"/>
    <![CDATA[
      ORDER BY SALES_CHANNEL_CODE
    ]]>
  </select>
  <!-- 验真服务多选 -->
  <select id="NB_queryIdentCheck" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
      SELECT A.TYPE_CODE,A.TYPE_NAME FROM DEV_NB.T_NB_CHECK_IDENTITY_TYPE A WHERE 
		A.TYPE_CODE IN('3','5')]]>
  </select>  
  <sql id="SalesChannelSqlWhere">
    <if test=" SALES_CHANNEL_CODE != null and SALES_CHANNEL_CODE != '' "><![CDATA[ AND SALES_CHANNEL_CODE = #{sales_channel_code} ]]></if>
    <if test=" SALES_CHANNEL_NAME != null and SALES_CHANNEL_NAME != '' "><![CDATA[ AND SALES_CHANNEL_NAME = #{sales_channel_name} ]]></if>
    <if test=" IS_VALID != null and IS_VALID != '' "><![CDATA[ AND IS_VALID = #{is_valid} ]]></if>
  </sql>
  <!-- 通知书类型多选（暂时没什么条件） -->
  <select id="NB_findDocumentTemplate" resultType="java.util.Map" parameterType="java.util.Map">
  	<![CDATA[SELECT a.template_code,a.template_name FROM  DEV_NB.T_DOCUMENT_TEMPLATE a WHERE TEMPLATE_SOURCE='001' or  TEMPLATE_SOURCE='002']]>
  </select>
	
	<!-- 绩优业务统计清单TOTAL  -->
	<select id="NB_queryAgentLevelBusinessInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
			<![CDATA[
				SELECT COUNT(1) FROM (SELECT ROWNUM RN,B.* FROM
				(SELECT ORGAN_CODE,CHANNEL_TYPE,TO_CHAR(SUM(PREAUDI_SUP_COUNT)) PREAUDI_SUP_COUNT
				,TO_CHAR(SUM(PREAUDI_COUNT)) PREAUDI_COUNT,TO_CHAR(SUM(INPUT_SUP_COUNT)) INPUT_SUP_COUNT
				,TO_CHAR(SUM(INPUT_COUNT)) INPUT_COUNT,TO_CHAR(SUM(DOUBLERECORD_SUP)) DOUBLERECORD_SUP
				,TO_CHAR(SUM(DOUBLERECORD_COUNT)) DOUBLERECORD_COUNT,TO_CHAR(SUM(QTTASK_SUP_COUNT)) QTTASK_SUP_COUNT
				,TO_CHAR(SUM(QTTASK_COUNT)) QTTASK_COUNT,TO_CHAR(SUM(PRINT_SUP_COUNT)) PRINT_SUP_COUNT
				,TO_CHAR(SUM(PRINT_COUNT)) PRINT_COUNT,TO_CHAR(SUM(AGETN_COUNT)) AGENT_COUNT,TO_CHAR(SUM(AGENT_SUP_COUNT)) AGENT_SUP_COUNT FROM (
				SELECT T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,COUNT(1) PREAUDI_SUP_COUNT
				,0 AS PREAUDI_COUNT,0 AS INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP
				,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT
				,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_PRE_AUDIT_MASTER T LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1'  AND T.AGENT_LEVEL IS NOT NULL 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL
				UNION 
				SELECT  T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,COUNT(1) PREAUDI_COUNT
				,0 AS INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT
				,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT
				, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM DEV_NB.T_PRE_AUDIT_MASTER T 
				LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1' 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT A.ORGAN_CODE ORGAN_CODE,A.CHANNEL_TYPE CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,COUNT(1) INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM DEV_NB.T_NB_CONTRACT_MASTER A 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA 
				ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') AND A.SUBMIT_CHANNEL IN('3','4') 
				AND TA.AGENT_LEVEL IS NOT NULL 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE
				UNION
				SELECT A.ORGAN_CODE ORGAN_CODE,A.CHANNEL_TYPE CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,COUNT(1) INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_NB_CONTRACT_MASTER A LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') 
				AND A.SUBMIT_CHANNEL IN('3','4') 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,COUNT(1) AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' 
				AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[ 
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,COUNT(1) AS DOUBLERECORD_COUNT
				,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT
				, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE 
				FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,COUNT(1) AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7') 
				AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE 
				WHERE TA.AGENT_LEVEL IS NOT NULL 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,COUNT(1) AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7')
				 AND A.QT_STATUS!=0
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[ 
				 ) QK 
				 LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				 ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				 ]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				 GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,COUNT(1) AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT
				 FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 				 
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				 
				 ) PD LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				 ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				 LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				  ]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				 GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,COUNT(1) AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 
				 ]]>
					<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) PD LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT AGENT_ORGAN_CODE ORGAN_CODE,AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT,COUNT(1) AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_AGENT A WHERE A.AGENT_LEVEL IS NOT NULL AND A.AGENT_STATUS='1' 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.AGENT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY A.AGENT_ORGAN_CODE ,A.AGENT_CHANNEL
				UNION
				SELECT ORGAN_CODE,AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT,0 AGETN_COUNT,COUNT(1) AGENT_SUP_COUNT FROM (
				SELECT DISTINCT ORGAN_CODE,AGENT_CODE,AGENT_CHANNEL FROM (
				SELECT T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CODE,TA.AGENT_CHANNEL FROM DEV_NB.T_PRE_AUDIT_MASTER T 
				LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1'  AND T.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL,TA.AGENT_CODE
				UNION
				SELECT A.ORGAN_CODE,TCA.AGENT_CODE,A.CHANNEL_TYPE FROM DEV_NB.T_NB_CONTRACT_MASTER A 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA 
				ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') AND A.SUBMIT_CHANNEL IN('3','4') 
				AND TA.AGENT_LEVEL IS NOT NULL
				 ]]>
					<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE 
				FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' AND A.QT_STATUS!=0 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT A.APPLY_CODE
				,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7') AND A.QT_STATUS!=0 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE 
				FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) PD 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				)) GROUP BY ORGAN_CODE,AGENT_CODE,AGENT_CHANNEL
				) GROUP BY ORGAN_CODE,CHANNEL_TYPE ) B  WHERE 1=1 ) C 
			]]>

</select>
	<!-- 绩优业务统计清单查询  -->
	<select id="queryAgentLevelBusinessInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				SELECT C.RN,C.* FROM (SELECT ROWNUM RN,B.* FROM
				(SELECT ORGAN_CODE,CHANNEL_TYPE,TO_CHAR(SUM(PREAUDI_SUP_COUNT)) PREAUDI_SUP_COUNT
				,TO_CHAR(SUM(PREAUDI_COUNT)) PREAUDI_COUNT,TO_CHAR(SUM(INPUT_SUP_COUNT)) INPUT_SUP_COUNT
				,TO_CHAR(SUM(INPUT_COUNT)) INPUT_COUNT,TO_CHAR(SUM(DOUBLERECORD_SUP)) DOUBLERECORD_SUP
				,TO_CHAR(SUM(DOUBLERECORD_COUNT)) DOUBLERECORD_COUNT,TO_CHAR(SUM(QTTASK_SUP_COUNT)) QTTASK_SUP_COUNT
				,TO_CHAR(SUM(QTTASK_COUNT)) QTTASK_COUNT,TO_CHAR(SUM(PRINT_SUP_COUNT)) PRINT_SUP_COUNT
				,TO_CHAR(SUM(PRINT_COUNT)) PRINT_COUNT,TO_CHAR(SUM(AGETN_COUNT)) AGENT_COUNT,TO_CHAR(SUM(AGENT_SUP_COUNT)) AGENT_SUP_COUNT FROM (
				SELECT T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,COUNT(1) PREAUDI_SUP_COUNT
				,0 AS PREAUDI_COUNT,0 AS INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP
				,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT
				,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_PRE_AUDIT_MASTER T LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1'  AND T.AGENT_LEVEL IS NOT NULL 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL
				UNION 
				SELECT  T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,COUNT(1) PREAUDI_COUNT
				,0 AS INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT
				,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT
				, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM DEV_NB.T_PRE_AUDIT_MASTER T 
				LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1' 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT A.ORGAN_CODE ORGAN_CODE,A.CHANNEL_TYPE CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,COUNT(1) INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM DEV_NB.T_NB_CONTRACT_MASTER A 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA 
				ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') AND A.SUBMIT_CHANNEL IN('3','4') 
				AND TA.AGENT_LEVEL IS NOT NULL 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE
				UNION
				SELECT A.ORGAN_CODE ORGAN_CODE,A.CHANNEL_TYPE CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,COUNT(1) INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_NB_CONTRACT_MASTER A LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') 
				AND A.SUBMIT_CHANNEL IN('3','4') 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,COUNT(1) AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' 
				AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[ 
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,COUNT(1) AS DOUBLERECORD_COUNT
				,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT
				, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE 
				FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,COUNT(1) AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7') 
				AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE 
				WHERE TA.AGENT_LEVEL IS NOT NULL 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,COUNT(1) AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7')
				 AND A.QT_STATUS!=0
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[ 
				 ) QK 
				 LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				 ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				 ]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				 GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,COUNT(1) AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT
				 FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 				 
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				 
				 ) PD LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				 ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				 LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				  ]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				 GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,COUNT(1) AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 
				 ]]>
					<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) PD LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT AGENT_ORGAN_CODE ORGAN_CODE,AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT,COUNT(1) AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_AGENT A WHERE A.AGENT_LEVEL IS NOT NULL AND A.AGENT_STATUS='1' 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.AGENT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY A.AGENT_ORGAN_CODE ,A.AGENT_CHANNEL
				UNION
				SELECT ORGAN_CODE,AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT,0 AGETN_COUNT,COUNT(1) AGENT_SUP_COUNT FROM (
				SELECT DISTINCT ORGAN_CODE,AGENT_CODE,AGENT_CHANNEL FROM (
				SELECT T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CODE,TA.AGENT_CHANNEL FROM DEV_NB.T_PRE_AUDIT_MASTER T 
				LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1'  AND T.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL,TA.AGENT_CODE
				UNION
				SELECT A.ORGAN_CODE,TCA.AGENT_CODE,A.CHANNEL_TYPE FROM DEV_NB.T_NB_CONTRACT_MASTER A 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA 
				ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') AND A.SUBMIT_CHANNEL IN('3','4') 
				AND TA.AGENT_LEVEL IS NOT NULL
				 ]]>
					<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE 
				FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' AND A.QT_STATUS!=0 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT A.APPLY_CODE
				,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7') AND A.QT_STATUS!=0 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE 
				FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) PD 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				)) GROUP BY ORGAN_CODE,AGENT_CODE,AGENT_CHANNEL
				) GROUP BY ORGAN_CODE,CHANNEL_TYPE ) B  WHERE  ROWNUM <=#{LESS_NUM}) C WHERE C.RN>=#{GREATER_NUM}
			
			]]>
		     
	</select>
	<!-- 绩优业务统计清单导出  -->
	<select id="NB_getAgentLevelBusinessList" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[
				SELECT ROWNUM RN,B.* FROM
				(SELECT ORGAN_CODE,CHANNEL_TYPE,TO_CHAR(SUM(PREAUDI_SUP_COUNT)) PREAUDI_SUP_COUNT
				,TO_CHAR(SUM(PREAUDI_COUNT)) PREAUDI_COUNT,TO_CHAR(SUM(INPUT_SUP_COUNT)) INPUT_SUP_COUNT
				,TO_CHAR(SUM(INPUT_COUNT)) INPUT_COUNT,TO_CHAR(SUM(DOUBLERECORD_SUP)) DOUBLERECORD_SUP
				,TO_CHAR(SUM(DOUBLERECORD_COUNT)) DOUBLERECORD_COUNT,TO_CHAR(SUM(QTTASK_SUP_COUNT)) QTTASK_SUP_COUNT
				,TO_CHAR(SUM(QTTASK_COUNT)) QTTASK_COUNT,TO_CHAR(SUM(PRINT_SUP_COUNT)) PRINT_SUP_COUNT
				,TO_CHAR(SUM(PRINT_COUNT)) PRINT_COUNT,TO_CHAR(SUM(AGETN_COUNT)) AGENT_COUNT,TO_CHAR(SUM(AGENT_SUP_COUNT)) AGENT_SUP_COUNT FROM (
				SELECT T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,COUNT(1) PREAUDI_SUP_COUNT
				,0 AS PREAUDI_COUNT,0 AS INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP
				,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT
				,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_PRE_AUDIT_MASTER T LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1'  AND T.AGENT_LEVEL IS NOT NULL 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL
				UNION 
				SELECT  T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,COUNT(1) PREAUDI_COUNT
				,0 AS INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT
				,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT
				, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM DEV_NB.T_PRE_AUDIT_MASTER T 
				LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1' 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT A.ORGAN_CODE ORGAN_CODE,A.CHANNEL_TYPE CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,COUNT(1) INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM DEV_NB.T_NB_CONTRACT_MASTER A 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA 
				ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') AND A.SUBMIT_CHANNEL IN('3','4') 
				AND TA.AGENT_LEVEL IS NOT NULL 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE
				UNION
				SELECT A.ORGAN_CODE ORGAN_CODE,A.CHANNEL_TYPE CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,COUNT(1) INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_NB_CONTRACT_MASTER A LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') 
				AND A.SUBMIT_CHANNEL IN('3','4') 
				]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[ 
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,COUNT(1) AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' 
				AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[ 
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,COUNT(1) AS DOUBLERECORD_COUNT
				,0 AS QTTASK_SUP_COUNT,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT
				, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE 
				FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,COUNT(1) AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7') 
				AND A.QT_STATUS!=0 
				]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE 
				WHERE TA.AGENT_LEVEL IS NOT NULL 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,COUNT(1) AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7')
				 AND A.QT_STATUS!=0
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[ 
				 ) QK 
				 LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				 ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				 ]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				 GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,COUNT(1) AS PRINT_SUP_COUNT,0 AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT
				 FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 				 
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				 
				 ) PD LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				 ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				 LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				  ]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				 GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT TCM.ORGAN_CODE ORGAN_CODE,TA.AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,COUNT(1) AS PRINT_COUNT, 0 AS AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 
				 ]]>
					<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) PD LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE 1=1 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL
				UNION
				SELECT AGENT_ORGAN_CODE ORGAN_CODE,AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT,COUNT(1) AGETN_COUNT,0 AS AGENT_SUP_COUNT 
				FROM DEV_NB.T_AGENT A WHERE A.AGENT_LEVEL IS NOT NULL AND A.AGENT_STATUS='1' 
				]]> 
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.AGENT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				<![CDATA[
				GROUP BY A.AGENT_ORGAN_CODE ,A.AGENT_CHANNEL
				UNION
				SELECT ORGAN_CODE,AGENT_CHANNEL CHANNEL_TYPE,0 PREAUDI_SUP_COUNT,0 AS PREAUDI_COUNT
				,0 INPUT_SUP_COUNT,0 AS INPUT_COUNT,0 AS DOUBLERECORD_SUP,0 AS DOUBLERECORD_COUNT,0 AS QTTASK_SUP_COUNT
				,0 AS QTTASK_COUNT,0 AS PRINT_SUP_COUNT,0 AS PRINT_COUNT,0 AGETN_COUNT,COUNT(1) AGENT_SUP_COUNT FROM (
				SELECT DISTINCT ORGAN_CODE,AGENT_CODE,AGENT_CHANNEL FROM (
				SELECT T.AUDIT_ORGAN_CODE ORGAN_CODE,TA.AGENT_CODE,TA.AGENT_CHANNEL FROM DEV_NB.T_PRE_AUDIT_MASTER T 
				LEFT JOIN DEV_NB.T_AGENT TA ON T.AGENT_CODE=TA.AGENT_CODE  
				WHERE T.APPLY_CODE NOT IN (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TCM WHERE TCM.SUBMIT_CHANNEL IN('3','4') ) 
				AND  T.CONCLUSION_CODE='1'  AND T.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.INPUT_DATE)  >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.INPUT_DATE) <=  to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND T.AUDIT_ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY T.AUDIT_ORGAN_CODE,TA.AGENT_CHANNEL,TA.AGENT_CODE
				UNION
				SELECT A.ORGAN_CODE,TCA.AGENT_CODE,A.CHANNEL_TYPE FROM DEV_NB.T_NB_CONTRACT_MASTER A 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON A.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA 
				ON TCA.AGENT_CODE= TA.AGENT_CODE WHERE A.PROPOSAL_STATUS IN('03','04') AND A.SUBMIT_CHANNEL IN('3','4') 
				AND TA.AGENT_LEVEL IS NOT NULL
				 ]]>
					<if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 	<if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.INSERT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY A.CHANNEL_TYPE,A.ORGAN_CODE,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT A.APPLY_CODE,A.ORGAN_CODE 
				FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE='6' AND A.QT_STATUS!=0 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.APPLY_CODE=QK.APPLY_CODE 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT A.APPLY_CODE
				,A.ORGAN_CODE FROM DEV_NB.T_NB_QT_TASK A WHERE A.QA_TYPE IN('4','5','7') AND A.QT_STATUS!=0 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(A.QT_TIME) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(A.QT_TIME) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) QK LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM 
				ON TCM.APPLY_CODE=QK.APPLY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA ON  TCM.APPLY_CODE=TCA.APPLY_CODE 
				LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				UNION
				SELECT TCM.ORGAN_CODE,TCA.AGENT_CODE,TA.AGENT_CHANNEL FROM (SELECT DISTINCT T.POLICY_CODE,T.SEND_DATE 
				FROM DEV_NB.T_PRINT_DETAIL T WHERE T.TRACK_NODE = '1' 
				 ]]> 
				 <if test="start_date != null and start_date != ''"><![CDATA[AND trunc(T.SEND_DATE) >= to_date(#{start_date},'yyyy-mm-dd') ]]></if>
				 <if test="end_date != null and end_date != ''"><![CDATA[AND trunc(T.SEND_DATE) <= to_date(#{end_date},'yyyy-mm-dd') ]]></if>
				 <![CDATA[
				) PD 
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM ON TCM.POLICY_CODE=PD.POLICY_CODE LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT TCA 
				ON  TCM.APPLY_CODE=TCA.APPLY_CODE LEFT JOIN DEV_NB.T_AGENT TA ON TCA.AGENT_CODE=TA.AGENT_CODE WHERE TA.AGENT_LEVEL IS NOT NULL 
				 ]]>
				 	<if test = " organ_code != null and organ_code != ''"><![CDATA[ AND TCM.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
				 	<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(TA.AGENT_CHANNEL)) in (${channel_type}) ]]></if>
				 <![CDATA[
				GROUP BY TCM.ORGAN_CODE,TA.AGENT_CHANNEL,TCA.AGENT_CODE
				)) GROUP BY ORGAN_CODE,AGENT_CODE,AGENT_CHANNEL
				) GROUP BY ORGAN_CODE,CHANNEL_TYPE ) B  WHERE 1=1 
			
			]]>
		     
	</select>
	<!-- 银保通身份证明资料上传清单 -->
	<select id="NB_findCertiDataUploadTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(1) FROM (
			SELECT DISTINCT MAS.APPLY_CODE,MAS.POLICY_CODE,MAS.ISSUE_DATE,
		        (SELECT MAX(IMG.UPLOAD_TIME) FROM DEV_NB.T_IMAGE_SCAN IMG WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.IMAGE_SOURCE = '004')AS UPLOAD_TIME
			FROM DEV_NB.T_IMAGE_SCAN SC
		        LEFT JOIN DEV_NB.T_NB_QT_TASK A ON SC.BUSS_CODE = A.APPLY_CODE
		        INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER MAS ON SC.BUSS_CODE = MAS.APPLY_CODE
			WHERE MAS.SUBMIT_CHANNEL = '1' AND SC.IMAGE_SOURCE = '004' AND A.QA_TYPE = 'A'
		]]>
		<if test =" organ_code != null and organ_code != ''"><![CDATA[ AND MAS.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
	    <if test =" is_avoid_check != null "><![CDATA[AND A.IS_AVOID_CHECK = #{is_avoid_check} ]]></if>
	    <if test =" service_bank != null and service_bank != '' "><![CDATA[ AND MAS.SERVICE_BANK IN (${service_bank}) ]]></if>
		<if test =" choose_way != null and choose_way != ''">
	      	<if test="choose_way == '01'">
	      		<if test="start_date != null"><![CDATA[AND MAS.ISSUE_DATE >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND MAS.ISSUE_DATE <= #{end_date} ]]></if>
	      	</if>
	    </if>
	    <![CDATA[ )A WHERE 1=1 ]]>
	    <if test =" choose_way != null and choose_way != ''">
	      	<if test="choose_way == '02'">
	      		<if test="start_date != null"><![CDATA[AND A.UPLOAD_TIME >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND A.UPLOAD_TIME <= #{end_date} ]]></if>
	      	</if>
	    </if>
	    
	</select>
	
	<!-- 银保通身份证明资料上传清单 -->
	<select id="NB_findCertiDataUploadForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT B.* FROM (
			SELECT ROWNUM RN,A.* FROM (
			SELECT DISTINCT MAS.APPLY_CODE,MAS.POLICY_CODE,MAS.ISSUE_DATE,MAS.SUBMIT_CHANNEL,MAS.SERVICE_BANK,BANK.BANK_NAME,
		        MAS.SUBINPUT_TYPE,SUB.TYPE_DESC AS SUBINPUT_NAME,
		        (SELECT SUM(TCP.TOTAL_PREM_AF) FROM DEV_NB.T_NB_CONTRACT_PRODUCT TCP WHERE TCP.APPLY_CODE = MAS.APPLY_CODE )AS TOTAL_PREM_AF,/*总保费*/
		        (CASE WHEN BE.BENE_TYPE =1 THEN '是' ELSE '否' END )AS BENE_TYPE,
		        INS.RELATION_TO_PH,LA.RELATION_NAME AS INS_RELATION,/** 投被保险人关系 **/
		        BE.DESIGNATION,BELA.RELATION_NAME AS BENE_RELATION,/** 被保险人与受益人关系 **/
		        A.QT_TIME,A.QT_STATUS,A.IS_AVOID_CHECK,
		        (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = MAS.BRANCH_ORGAN_CODE) AS ORGAN2_NAME,
		        (CASE  WHEN LENGTH(MAS.ORGAN_CODE) > 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE =
		        SUBSTR(MAS.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
		        (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = MAS.ORGAN_CODE) AS ORGAN4_NAME,
		        (SELECT MAX(IMG.UPLOAD_TIME) FROM DEV_NB.T_IMAGE_SCAN IMG WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.IMAGE_SOURCE = '004')AS UPLOAD_TIME,STN.STATUS_NAME,
		        (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_IMAGE_SCAN IMG WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.IMAGE_SOURCE = '004' AND IMG.BILLCARD_CODE = 'EUA049') > 0
		        	THEN '是' ELSE '否' END 
		        )AS CERTI_TYPE,/** 身份证件类 **/
				(SELECT OCR.OCR_SCAN_NAME FROM DEV_NB.T_IMAGE_SCAN IMG,DEV_NB.T_OCR_SCAN_REMARK OCR WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.OCR_SCAN_REMARK = OCR.OCR_SCAN_CODE
		        	AND IMG.IMAGE_SOURCE = '004' AND IMG.BILLCARD_CODE = 'EUA049' AND ROWNUM = 1
		        )AS CERTI_OCR,/** 身份证件OCR **/
		        (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_IMAGE_SCAN IMG WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.IMAGE_SOURCE = '004' AND IMG.BILLCARD_CODE = 'TB006') > 0
		        	THEN '是' ELSE '否' END 
		        )AS RELATION_TYPE,/** 关系证明类 **/
				(SELECT OCR.OCR_SCAN_NAME FROM DEV_NB.T_IMAGE_SCAN IMG,DEV_NB.T_OCR_SCAN_REMARK OCR WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.OCR_SCAN_REMARK = OCR.OCR_SCAN_CODE
		        	AND IMG.IMAGE_SOURCE = '004' AND IMG.BILLCARD_CODE = 'TB006' AND ROWNUM = 1
		        )AS RELATION_OCR/** 关系证明OCR **/
		        
			FROM DEV_NB.T_IMAGE_SCAN SC
		        LEFT JOIN DEV_NB.T_NB_QT_TASK A ON SC.BUSS_CODE = A.APPLY_CODE
		        LEFT JOIN DEV_NB.T_QT_STATUS STN ON STN.STATUS_CODE = A.QT_STATUS
		        INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER MAS ON SC.BUSS_CODE = MAS.APPLY_CODE
		        LEFT JOIN DEV_NB.T_BANK BANK ON BANK.BANK_CODE = MAS.SERVICE_BANK
		        LEFT JOIN DEV_NB.T_SUBINPUT_TYPE SUB ON SUB.TYPE_CODE = MAS.SUBINPUT_TYPE
		        INNER JOIN DEV_NB.T_NB_INSURED_LIST INS ON INS.POLICY_ID = MAS.POLICY_ID
		        INNER JOIN DEV_NB.T_LA_PH_RELA LA ON LA.RELATION_CODE = INS.RELATION_TO_PH
		        LEFT JOIN DEV_NB.T_NB_CONTRACT_BENE BE ON BE.POLICY_ID = MAS.POLICY_ID AND BE.BENE_TYPE='1'
		        LEFT JOIN DEV_NB.T_LA_PH_RELA BELA ON BELA.RELATION_CODE = BE.DESIGNATION
			WHERE MAS.SUBMIT_CHANNEL = '1' AND SC.IMAGE_SOURCE = '004' AND A.QA_TYPE = 'A'
		]]>
		<if test =" organ_code != null and organ_code != ''"><![CDATA[ AND MAS.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
	    <if test =" is_avoid_check != null "><![CDATA[AND A.IS_AVOID_CHECK = #{is_avoid_check} ]]></if>
	    <if test =" service_bank != null and service_bank != '' "><![CDATA[ AND MAS.SERVICE_BANK IN (${service_bank}) ]]></if>
		<if test =" choose_way != null and choose_way != ''">
	      	<if test="choose_way == '01'">
	      		<if test="start_date != null"><![CDATA[AND MAS.ISSUE_DATE >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND MAS.ISSUE_DATE <= #{end_date} ]]></if>
	      	</if>
	    </if>
	    <![CDATA[ )A WHERE ROWNUM <= #{LESS_NUM} ]]>
	    <if test =" choose_way != null and choose_way != ''">
	      	<if test="choose_way == '02'">
	      		<if test="start_date != null"><![CDATA[AND A.UPLOAD_TIME >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND A.UPLOAD_TIME <= #{end_date} ]]></if>
	      	</if>
	    </if>
	    <![CDATA[ )B WHERE B.RN >= #{GREATER_NUM} ]]>
	</select>
	
	<!-- 银保通身份证明资料上传清单 -->
	<select id="NB_findCertiDataUploadList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT * FROM (
			SELECT DISTINCT MAS.APPLY_CODE,MAS.POLICY_CODE,MAS.ISSUE_DATE,MAS.SUBMIT_CHANNEL,MAS.SERVICE_BANK,BANK.BANK_NAME,
		        MAS.SUBINPUT_TYPE,SUB.TYPE_DESC AS SUBINPUT_NAME,
		        (SELECT SUM(TCP.TOTAL_PREM_AF) FROM DEV_NB.T_NB_CONTRACT_PRODUCT TCP WHERE TCP.APPLY_CODE = MAS.APPLY_CODE )AS TOTAL_PREM_AF,/*总保费*/
		        (CASE WHEN BE.BENE_TYPE =1 THEN '是' ELSE '否' END )AS BENE_TYPE,
		        INS.RELATION_TO_PH,LA.RELATION_NAME AS INS_RELATION,/** 投被保险人关系 **/
		        BE.DESIGNATION,BELA.RELATION_NAME AS BENE_RELATION,/** 被保险人与受益人关系 **/
		        A.QT_TIME,A.QT_STATUS,A.IS_AVOID_CHECK,
		        (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = MAS.BRANCH_ORGAN_CODE) AS ORGAN2_NAME,
		        (CASE  WHEN LENGTH(MAS.ORGAN_CODE) > 6 THEN (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE =
		        SUBSTR(MAS.ORGAN_CODE, 0, 6)) ELSE '' END) AS ORGAN3_NAME,
		        (SELECT O.ORGAN_NAME FROM APP___NB__DBUSER.T_UDMP_ORG O WHERE O.ORGAN_CODE = MAS.ORGAN_CODE) AS ORGAN4_NAME,
		        (SELECT MAX(IMG.UPLOAD_TIME) FROM DEV_NB.T_IMAGE_SCAN IMG WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.IMAGE_SOURCE = '004')AS UPLOAD_TIME,STN.STATUS_NAME,
		        (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_IMAGE_SCAN IMG WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.IMAGE_SOURCE = '004' AND IMG.BILLCARD_CODE = 'EUA049') > 0
		        	THEN '是' ELSE '否' END 
		        )AS CERTI_TYPE,/** 身份证件类 **/
				(SELECT OCR.OCR_SCAN_NAME FROM DEV_NB.T_IMAGE_SCAN IMG,DEV_NB.T_OCR_SCAN_REMARK OCR WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.OCR_SCAN_REMARK = OCR.OCR_SCAN_CODE
		        	AND IMG.IMAGE_SOURCE = '004' AND IMG.BILLCARD_CODE = 'EUA049' AND ROWNUM = 1
		        )AS CERTI_OCR,/** 身份证件OCR **/
		        (CASE WHEN (SELECT COUNT(1) FROM DEV_NB.T_IMAGE_SCAN IMG WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.IMAGE_SOURCE = '004' AND IMG.BILLCARD_CODE = 'TB006') > 0
		        	THEN '是' ELSE '否' END 
		        )AS RELATION_TYPE,/** 关系证明类 **/
				(SELECT OCR.OCR_SCAN_NAME FROM DEV_NB.T_IMAGE_SCAN IMG,DEV_NB.T_OCR_SCAN_REMARK OCR WHERE 
		        	IMG.BUSS_CODE = SC.BUSS_CODE AND IMG.OCR_SCAN_REMARK = OCR.OCR_SCAN_CODE
		        	AND IMG.IMAGE_SOURCE = '004' AND IMG.BILLCARD_CODE = 'TB006' AND ROWNUM = 1
		        )AS RELATION_OCR/** 关系证明OCR **/
		        
			FROM DEV_NB.T_IMAGE_SCAN SC
		        LEFT JOIN DEV_NB.T_NB_QT_TASK A ON SC.BUSS_CODE = A.APPLY_CODE
		        LEFT JOIN DEV_NB.T_QT_STATUS STN ON STN.STATUS_CODE = A.QT_STATUS
		        INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER MAS ON SC.BUSS_CODE = MAS.APPLY_CODE
		        LEFT JOIN DEV_NB.T_BANK BANK ON BANK.BANK_CODE = MAS.SERVICE_BANK
		        LEFT JOIN DEV_NB.T_SUBINPUT_TYPE SUB ON SUB.TYPE_CODE = MAS.SUBINPUT_TYPE
		        INNER JOIN DEV_NB.T_NB_INSURED_LIST INS ON INS.POLICY_ID = MAS.POLICY_ID
		        INNER JOIN DEV_NB.T_LA_PH_RELA LA ON LA.RELATION_CODE = INS.RELATION_TO_PH
		        LEFT JOIN DEV_NB.T_NB_CONTRACT_BENE BE ON BE.POLICY_ID = MAS.POLICY_ID AND BE.BENE_TYPE='1'
		        LEFT JOIN DEV_NB.T_LA_PH_RELA BELA ON BELA.RELATION_CODE = BE.DESIGNATION
			WHERE MAS.SUBMIT_CHANNEL = '1' AND SC.IMAGE_SOURCE = '004' AND A.QA_TYPE = 'A'
		]]>
		<if test =" organ_code != null and organ_code != ''"><![CDATA[ AND MAS.ORGAN_CODE like CONCAT(#{organ_code},'%')]]></if>
	    <if test =" is_avoid_check != null "><![CDATA[AND A.IS_AVOID_CHECK = #{is_avoid_check} ]]></if>
	    <if test =" service_bank != null and service_bank != '' "><![CDATA[ AND MAS.SERVICE_BANK IN (${service_bank}) ]]></if>
		<if test =" choose_way != null and choose_way != ''">
	      	<if test="choose_way == '01'">
	      		<if test="start_date != null"><![CDATA[AND MAS.ISSUE_DATE >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND MAS.ISSUE_DATE <= #{end_date} ]]></if>
	      	</if>
	    </if>
	    <![CDATA[ )A WHERE 1=1 ]]>
	    <if test =" choose_way != null and choose_way != ''">
	      	<if test="choose_way == '02'">
	      		<if test="start_date != null"><![CDATA[AND A.UPLOAD_TIME >= #{start_date} ]]></if>
	      		<if test="end_date != null"><![CDATA[AND A.UPLOAD_TIME <= #{end_date} ]]></if>
	      	</if>
	    </if>
	</select>
		<select id="NB_findDocumentNo" resultType="java.util.Map" parameterType="java.util.Map">
			select A.TEMPLATE_CODE, A.DOCUMENT_NO from dev_clm.t_document A, DEV_CLM.T_PRINT_CLM_DOCUMENT B where A.DOC_LIST_ID = B.DOC_LIST_ID 
				<if test =" cipher_text != null and cipher_text != ''"><![CDATA[ AND B.ENCRYPT_AFTER_SECURITY=#{cipher_text} OR B.ENCRYPT_AFTER_QR=#{cipher_text} and A.DOC_LIST_ID = B.DOC_LIST_ID ]]></if>
				
	</select>
	<!--投保资料是否上载至核心 #rm118936 -->
	<select id="NB_queryBillcardCodeByApplyCode" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[
		 SELECT A.SCAN_USER_TYPE, A.IMAGE_SCAN_ID,  A.IMAGE_SOURCE, A.UPLOAD_TIME,  A.DELETE_COMMENTS,
	     A.IMAGE_SCAN_STATUS, A.SCAN_OPERATION_TYPE, A.SCAN_TIME, 
	     A.billcard_no, A.PAGES, A.SCAN_USER_CODE ,A.billcard_code FROM APP___NB__DBUSER.T_IMAGE_SCAN                
	     A WHERE 1 = 1 and  A.BUSS_CODE=#{buss_code} and A.billcard_code in
		]]>
		  <foreach collection="list" index="index" item="billCardCode" open="(" separator="," close=")">
	                #{billCardCode}       
	    </foreach>
	</select>
	<!-- 查询所有销售渠道操作 20250123 -->
	<select id="NB_findAllChannelTypeTwo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT A.SALES_CHANNEL_NAME TYPE_NAME, A.SALES_CHANNEL_CODE INDIVIDUAL_GROUP FROM DEV_NB.T_SALES_CHANNEL A WHERE 1=1 ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" sales_channel_code != null and sales_channel_code != '' "><![CDATA[ AND A.SALES_CHANNEL_CODE in (${sales_channel_code}) ]]></if>
		<![CDATA[ ORDER BY INDIVIDUAL_GROUP ]]>
	</select>
	<!-- 查询投保单状态码表 20250123 -->
	<select id="NB_findAllproposalStatus_doubleControl" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.PROPOSAL_STATUS AS START_STATUS ,A.STATUS_DESC AS PROCESS_STEP FROM DEV_NB.T_PROPOSAL_STATUS A  ]]>
	</select>
</mapper>