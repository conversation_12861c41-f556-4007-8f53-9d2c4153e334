<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryClmQueryDaoImpl">
	
	<!-- 匹配理算结果 -->
	<!-- 匹配理算结果 保项计算信息 保项赔付结论 -->
	
	<!-- 查询所有操作 -->
	<select id="pa_findAllBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, <PERSON><PERSON>PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS, A.WAIVER_FLAG FROM dev_pds.T_BUSINESS_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<if test=" product_code_sys != null and product_code_sys != ''  "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code_sys} ]]></if>
		<if test=" primary_sales_channel != null and primary_sales_channel != ''  "><![CDATA[AND A.PRIMARY_SALES_CHANNEL IN (${primary_sales_channel})]]> </if>
		<![CDATA[ ORDER BY A.BUSINESS_PRD_ID ]]> 
	</select>
	
	
	
	<!-- 理赔综合查询-理赔查询 end -->
</mapper>
