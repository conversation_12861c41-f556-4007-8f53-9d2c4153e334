<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="commonLoanInfo">
<!-- 查询所有操作 -->
	<select id="findAllCsApplication" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO, A.CANCEL_CAUSE , A.APPLY_CODE, A.<PERSON>GAN_CODE, 
			A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			A.CANCEL_NOTE, A.APP_STATUS, A.CANCEL_ID, A.NOFILL_FLAG, 
			A.BILL_SEND_TYPE, A.AGENT_CERTI_TYPE , 
			A<PERSON>APPLY_TIME, <PERSON><PERSON>TOMER_ID, A<PERSON>FINISH_TIME , <PERSON><PERSON>_TYPE,
			 A.AGENT_LEVEL, A<PERSON>SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE, 
			  A.AGENT_TEL, A.AGENT_CODE, 
			A.TO_APPROVE_DATE,A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT FROM DEV_PAS.T_CS_APPLICATION A WHERE ROWNUM <=  1000  ]]>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<![CDATA[ ORDER BY A.CHANGE_ID ]]> 
	</select>
	
	<select id="findAllCsContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.OLD_NEW, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.CHANGE_ID, A.POLICY_ID, A.DERIVATION, A.OPERATION_TYPE, 
			A.CHANNEL_ORG_CODE, A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.BRANCH_CODE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.POLICY_CHG_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, 
			A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.VALIDATE_DATE, A.LOG_ID, A.SERVICE_BANK, 
			A.LANG_CODE, A.FORMER_ID,A.LAPSE_DATE,A.SALE_COM_CODE,A.INITIAL_PREM_DATE,A.RERINSTATE_DATE FROM DEV_PAS.T_CS_CONTRACT_MASTER A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		 <if test=" old_new != null and old_new != ''  "><![CDATA[ AND trim(A.OLD_NEW) = trim(#{old_new}) ]]></if>
		 <if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		 <![CDATA[ ORDER BY A.POLICY_CODE ]]>
	</select>
	
<select id="PA_QryfindAllContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		 <if test=" old_new != null and old_new != ''  "><![CDATA[ AND trim(A.OLD_NEW) = trim(#{old_new}) ]]></if>
		 <if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		 <if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<![CDATA[ORDER BY A.master_busi_item_id DESC,A.VALIDATE_DATE DESC]]>
	</select>

<select id="findAllCsBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATION_TYPE, A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.SOCI_SECU, A.INSURED_ID, 
			A.LOG_ID, A.ORDER_ID, A.OLD_NEW, A.POLICY_CODE, A.CHANGE_ID,A.RELATION_TO_INSURED_1, 
			A.LIST_ID, A.POLICY_CHG_ID, A.BUSI_ITEM_ID, A.POLICY_ID FROM DEV_PAS.T_CS_BENEFIT_INSURED A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>

<select id="PA_findAllBenefitInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATION_TO_INSURED_1, A.PRODUCT_CODE, A.INSURED_ID,  
			A.ORDER_ID, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM DEV_PAS.T_BENEFIT_INSURED A WHERE  ROWNUM <=  1000  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	
	<select id="findAllCsInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.OLD_NEW, 
			A.INSURED_AGE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, A.STAND_LIFE, 
			A.SMOKING, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.LOG_ID, A.POLICY_CODE FROM DEV_PAS.T_CS_INSURED_LIST A WHERE 1=1  AND ROWNUM <=  1000  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
	
		<select id="findAllCsPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.OPERATION_TYPE, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, 
			A.JOB_CODE, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.LOG_ID, A.OLD_NEW, A.POLICY_CODE, 
			A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID FROM DEV_PAS.T_CS_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
			<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
			<if test=" old_new != null and old_new != ''  "><![CDATA[ AND A.OLD_NEW = #{old_new} ]]></if>
			<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
	
	<select id="LoanOperationRecord" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	select H.change_id,H.accept_id, H.apply_code,H.accept_code,H.service_name, H.apply_time,
       H.validate_time,H.app_status,H.accept_status,H.try_calc_no,h.accept_time,h.fee_amount,h.arrive_time from 
       (select rownum RN , a.change_id,ac.accept_id, a.apply_code,ac.accept_code,
                   ac.validate_time,
                (select s.service_name from DEV_PAS.t_service s where s.service_code = ac.service_code and service_code  in ('LN', 'RL')) service_name ,
                a.apply_time,
                (select s.status_desc from DEV_PAS.t_app_status s where a.app_status = s.app_status_code) app_status,
                (select tas.status_desc from DEV_PAS.t_accept_status tas where tas.accept_status = ac.accept_status) accept_status,
                a.try_calc_no,
                ac.accept_time,
                (select sum(t.fee_amount)
                 from DEV_PAS.v_prem_arap_all t
                where t.policy_code =#{policy_code}
                  and t.business_code = ac.accept_code) fee_amount,
                a.finish_time,
                (select pa.finish_time from  DEV_CAP.v_prem_arap pa where pa.business_code=ac.accept_code and pa.fee_status='01'  and rownum=1 )
                 as arrive_time
           from DEV_PAS.T_CS_APPLICATION a inner join DEV_PAS.T_CS_ACCEPT_CHANGE ac 
         on a.change_id = ac.change_id
         where  ac.accept_status=18
         AND (AC.SERVICE_CODE='LN' or AC.SERVICE_CODE='RL')
       and exists ( select 1 from DEV_PAS.T_CS_POLICY_CHANGE t where t.change_id = a.change_id  and t.policy_code = #{policy_code}  ) order by a.apply_time desc ) H
	]]>
	</select>
</mapper>
