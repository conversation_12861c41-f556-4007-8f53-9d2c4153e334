<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryPolicyMarkingInfoDaoImpl">


    <!-- 保单睡眠保单信息查询   -->
	<select id="findAllPolicyMarkingInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	
	   <![CDATA[ SELECT PMI.POLICY_CODE,/*保单号*/
                        PMI.SLEEP_TYPE,/*睡眠类型*/
                        PMI.DEAL_STATUS,/*处理状态*/
                        PMI.DEAL_TIME,/*处理时间*/
                        PMI.EFFECT_NOTICE_INFO,/*有效通知情况*/
                        (SELECT MDS.STATUS_NAME  FROM DEV_PAS.T_MARK_DEAL_STATUS MDS WHERE MDS.DEAL_STATUS = PMI.DEAL_STATUS) AS STATUS_NAME,/*处理状态描述*/
                        (SELECT ST.TYPE_NAME FROM DEV_PAS.T_SLEEP_TYPE ST WHERE ST.SLEEP_TYPE = PMI.SLEEP_TYPE) AS TYPE_NAME/*睡眠类型描述*/
                   FROM DEV_PAS.T_POLICY_MARKING_INFO PMI
                  WHERE 1 = 1
                    AND PMI.POLICY_CODE = #{policy_code}
                  ORDER BY PMI.DEAL_TIME DESC  ]]>
	
	</select>

</mapper>