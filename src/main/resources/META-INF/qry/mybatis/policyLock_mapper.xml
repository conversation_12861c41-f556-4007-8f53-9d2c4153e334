<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.ILockPolicyQueryDao">
<!--查询保单业务锁信息  -->
<select id="QRY_queryLockPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[ SELECT LP.OPERATION_ID,
       LP.POLICY_ID,
       LP.POLICY_CODE,
       LP.LOCK_SERVICE_ID,
       LP.INSERT_TIME,
       SD.LOCK_SERVICE_NAME,
       SD.LOCK_SERVICE_TYPE,
       SD.SUB_ID,
       LP.EXCEPT_GROUP_1,
       LP.EXCEPT_GROUP_2
  FROM DEV_PAS.T_LOCK_POLICY LP
 INNER JOIN DEV_PAS.T_LOCK_SERVICE_DEF SD
    ON SD.LOCK_SERVICE_ID = LP.LOCK_SERVICE_ID
 WHERE 1 = 1]]>
   <if test=" policy_code  != null "><![CDATA[ AND LP.POLICY_CODE = #{policy_code} ]]></if>
   <![CDATA[UNION
    SELECT 
    LP.OPERATION_ID,
    LP.POLICY_ID,
       LP.POLICY_CODE,
       LP.LOCK_SERVICE_ID,
       LP.INSERT_TIME,
       SD.LOCK_SERVICE_NAME,
       SD.LOCK_SERVICE_TYPE,
       SD.SUB_ID,
       LP.EXCEPT_GROUP_1,
       LP.EXCEPT_GROUP_2
  FROM DEV_PAS.T_LOCK_POLICY LP
 INNER JOIN DEV_PAS.T_LOCK_SERVICE_DEF SD
    ON SD.LOCK_SERVICE_ID = LP.LOCK_SERVICE_ID
 WHERE 1 = 1 AND LP.POLICY_ID = (SELECT CM.POLICY_ID FROM DEV_PAS.T_CONTRACT_MASTER CM WHERE 1=1 ]]>
 <if test=" policy_code  != null "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
  <![CDATA[)]]>
</select>

	<!-- 保单冻结信息查询 -->
	<select id="QRY_queryPolicyFreezeInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_ID, A.POLICY_CODE, A.FREEZE_DATE
			  FROM DEV_PAS.T_POLICY_FREEZE A, DEV_PAS.T_CS_ACCEPT_CHANGE B
			 WHERE A.ACCEPT_CODE = B.ACCEPT_CODE
			   AND B.ACCEPT_STATUS = '18'
			   AND A.UNFREEZE_DATE IS NULL
		]]>
		<if test="policy_id != null and policy_id != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test="policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test="freeze_date != null and freeze_date != '' "><![CDATA[ AND A.FREEZE_DATE <= #{freeze_date} ]]></if>
	</select>
</mapper>