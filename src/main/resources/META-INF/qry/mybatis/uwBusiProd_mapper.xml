<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.UwBusiProdDaoImpl">
<!--
	<sql id="uwBusiProdWhereCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" master_busi_item_id  != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		<if test=" uw_busi_id  != null "><![CDATA[ AND A.UW_BUSI_ID = #{uw_busi_id} ]]></if>
		<if test=" insured_1  != null "><![CDATA[ AND A.INSURED_1 = #{insured_1} ]]></if>
		<if test=" insured_2  != null "><![CDATA[ AND A.INSURED_2 = #{insured_2} ]]></if>
		<if test=" uw_status != null and uw_status != ''  "><![CDATA[ AND A.UW_STATUS = #{uw_status} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryUwBusiProdByUwBusiIdCondition">
		<if test=" uw_busi_id  != null "><![CDATA[ AND A.UW_BUSI_ID = #{uw_busi_id} ]]></if>
	</sql>	
	<sql id="queryUwBusiProdByUwBusiIdCondition2">
		<if test=" uw_busi_id  != null "><![CDATA[ AND M.UW_BUSI_ID = #{uw_busi_id} ]]></if>
	</sql>
	<sql id="queryUwBusiProdByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>	
	<sql id="queryUwBusiProdByUwId">
		<if test=" uw_id  != null "><![CDATA[ AND M.UW_ID = #{uw_id} ]]></if>
	</sql>	
	<!-- add by xuhp 保全下发核保结论 根据uwid,applycode查询保单下险种信息 2015年8月21日 start-->
	<sql id="queryUwBusiProdByApplyCodeCondition">
		<if test="policy_code != null"><![CDATA[ AND M.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	
	<sql id="queryUwBusiProdByPolicyCodeCondition">
	  <if test="policy_code != null"><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	
	<sql id="queryUwBusiProdByCSApplyCodeCondition">
		<if test="policy_code != null"><![CDATA[ and M.Policy_Code = N.Policy_Code AND M.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<!-- add by xuhp 保全下发核保结论 根据uwid,applycode查询保单下险种信息 2015年8月21日 end-->
	<sql id="queryUwBusiProdByUwBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>
	
	<!-- Modify by xuhp 提交核保结论时校验是否满足再保结论  2016年3月29日-->
	<sql id="queryBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND M.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>
	
	<sql id="queryBusiProdCodeByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ and m.apply_code = n.apply_code and m.apply_code = #{apply_code} ]]></if>
	</sql>
<!-- 添加操作 -->
	<insert id="addUwBusiProd"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="uw_busi_id"> SELECT DEV_UW.S_T_UW_BUSI_PROD.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_UW_BUSI_PROD(
				UW_ID, INSERT_TIME, MASTER_BUSI_ITEM_ID, UW_BUSI_ID, UPDATE_TIME, INSURED_1, INSURED_2, 
				UW_STATUS, DECISION_CODE, BUSI_PROD_CODE, APPLY_CODE, ORGAN_CODE, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY, UPDATE_TIMESTAMP, BUSI_ITEM_ID, INSERT_BY, POLICY_ID,UW_POLICY_ID,uw_status_detail) 
			VALUES (
				#{uw_id, jdbcType=NUMERIC}, SYSDATE , #{master_busi_item_id, jdbcType=NUMERIC} , #{uw_busi_id, jdbcType=NUMERIC} , SYSDATE , #{insured_1, jdbcType=NUMERIC} , #{insured_2, jdbcType=NUMERIC} 
				, #{uw_status, jdbcType=VARCHAR} , #{decision_code, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{busi_item_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}
				, #{uw_policy_id, jdbcType=NUMERIC}, #{uw_status_detail, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteUwBusiProd" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_UW_BUSI_PROD WHERE UW_BUSI_ID = #{uw_busi_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateUwBusiProd" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_BUSI_PROD                       ]]>
		<set>
		<trim suffixOverrides=",">
		<if test=" master_busi_item_id  != null "><![CDATA[ MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ,]]></if>
			<!-- <if test=" decision_code != null and decision_code != ''  "><![CDATA[DECISION_CODE = #{decision_code, jdbcType=NUMERIC} ,]]></if> --> 
			DECISION_CODE = #{decision_code, jdbcType=NUMERIC} ,
		    <if test=" uw_status != null and uw_status != ''  "><![CDATA[UW_STATUS = #{uw_status} ,]]></if>
		    <if test=" uw_status_detail != null and uw_status_detail != ''  "><![CDATA[uw_status_detail = #{uw_status_detail} ,]]></if>
		    <!-- Modify by xuhp 续保核保提交险种层核保结论后更新续保标识 2016年2月3日 START -->
		    <if test=" renew_decision != null and renew_decision != ''"><![CDATA[renew_decision = #{renew_decision} ,]]></if>
		    <!-- Modify by xuhp 续保核保提交险种层核保结论后更新续保标识 2016年2月3日 END -->
		    <if test=" update_by  != null "> 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIME = SYSDATE ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    </if>
		</trim>
		</set>
		<![CDATA[ where 1=1 ]]>
		<if test=" uw_id != null ">AND  UW_ID = #{uw_id} </if>
		<if test=" uw_busi_id != null ">AND UW_BUSI_ID = #{uw_busi_id} </if>
	</update>


   <!-- 更新T_UW_STATUS表中UW_STATUS -->
	<update id="UW_findUwMasterPOInfoss" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_MASTER                       ]]>
		<set>
		<trim suffixOverrides=",">
		<if test=" uw_status != null "><![CDATA[ UW_STATUS ='03' ]]></if>
		</trim>
		</set>
		<![CDATA[ WHERE UW_ID = #{uw_id}]]>
	</update>
	
	
	<!-- 更新再保回复以后T_UW_STATUS表中UW_STATUS -->
	<update id="UW_findAppPolicyUwMasterss" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_MASTER                       ]]>
		<set>
		<trim suffixOverrides=",">
		<if test=" uw_status != null "><![CDATA[ UW_STATUS ='08' ]]></if>
		</trim>
		</set>
		<![CDATA[ WHERE UW_ID = #{uw_id}]]>
	</update>
	<!--点击核保决定的时候查看 T_UW_MASTER表中的状态值 -->
   <select id="UW_queryUwMasterStatusByUwid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PREVIOUS_UW_TIME, A.PREVIOUS_UW_USER, A.UW_SUBMIT_TIME, A.UW_LEVEL_CODE, A.UW_STATUS, A.BIZ_DATE, 
			A.UW_FINISH_TIME, A.RI_TEL_FALG, A.UW_OVER_INDI, A.SERVICE_CODE, A.UW_AMEND_INDI, 
			A.APPLY_LEVEL_CODE, A.UW_CANCEL_CAUSE, A.UW_DECISION, A.UW_ESCA_INDI, A.RI_FALG, 
			A.UW_ESCA_USER, A.UW_REVIEW_INDI, A.BIZ_CODE, A.UW_SOURCE_TYPE, A.UW_USER_ID FROM DEV_UW.T_UW_MASTER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<![CDATA[ ORDER BY A.UW_ID ]]> 
	</select>
<!-- 按索引查询操作 -->	
	<select id="findUwBusiProdByUwBusiId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.UW_ID,
       A.MASTER_BUSI_ITEM_ID,
       A.UW_BUSI_ID,
       A.INSURED_1,
       A.INSURED_2,
       A.UW_STATUS,
       A.DECISION_CODE,
       A.BUSI_PROD_CODE,
       A.POLICY_CODE,
       A.BUSI_ITEM_ID,
       A.UW_POLICY_ID,
       A.POLICY_ID,
       T.PRODUCT_NAME_SYS AS APPLY_CODE,
       A.nb_DECISION_CODE,
       A.ORGAN_CODE,
       A.customer_name,A.VALIDATE_DATE,A.EXPIRY_DATE,
       T.PRODUCT_CATEGORY as PRODUCT_CATEGORY,
       to_char(A.renew_decision) renew_decision
  FROM DEV_UW.T_BUSINESS_PRODUCT T,
       (SELECT M.UW_ID,
               M.POLICY_ID,
               M.UW_BUSI_ID,
               M.INSURED_1,
               M.INSURED_2,
               M.UW_STATUS,
               M.DECISION_CODE,
               M.BUSI_PROD_CODE,
               M.POLICY_CODE,
               M.BUSI_ITEM_ID,
               M.UW_POLICY_ID,
               M.ORGAN_CODE,
               N.BUSI_PRD_ID,
               N.MASTER_BUSI_ITEM_ID,N.VALIDATE_DATE,N.EXPIRY_DATE,
               (select decision_code from DEV_UW.T_CONTRACT_BUSI_PROD where UW_ID = M.UW_ID and POLICY_CODE = M.POLICY_CODE and Busi_Item_Id = M.Busi_Item_Id and rownum=1) nb_DECISION_CODE, 
               /*(select decision_code from DEV_UW.T_CONTRACT_BUSI_PROD cb, DEV_UW.T_UW_POLICY up where cb.UW_ID != M.UW_ID and up.uw_source_type = '1' and up.uw_status = '04' and up.policy_id = cb.policy_id and cb.policy_id = M.POLICY_id and cb.Busi_Item_Id = M.Busi_Item_Id) nb_DECISION_CODE,*/
               case when M.POLICY_CODE is null then (select customer_name from DEV_UW.T_INSURED_LIST where UW_ID = M.UW_ID and rownum = 1) 
                                               else (select customer_name from DEV_UW.T_INSURED_LIST where UW_ID = M.UW_ID and policy_code = M.POLICY_CODE and rownum = 1) end customer_name,
               M.renew_decision                                               
          FROM DEV_UW.T_UW_BUSI_PROD M,DEV_UW.T_CONTRACT_BUSI_PROD N where
             m.uw_id = n.uw_id and M.BUSI_ITEM_ID = N.Busi_Item_Id AND N.LIABILITY_STATE <> '3']]>
		<include refid="queryUwBusiProdByUwId" />
		<include refid="queryUwBusiProdByCSApplyCodeCondition" /> <!-- 保全核保查询时按uwid,applicycode查询险种信息  xuhp 2015年8月21日 -->
		<include refid="queryUwBusiProdByUwBusiIdCondition2" />  <!-- 保全核保、新契约核保查询限额险种信息 xuhp 2016年1月6日 -->
		<include refid="queryBusiProdCodeCondition" />  <!-- Modify by xuhp 提交核保结论时校验是否满足再保结论  2016年3月29日 -->
		<include refid="queryBusiProdCodeByApplyCodeCondition" />
		) A WHERE T.BUSINESS_PRD_ID = A.BUSI_PRD_ID
		<![CDATA[ ORDER BY T.PRODUCT_CATEGORY ]]>
	</select>
	 
<!-- 按map查询操作 -->
	<select id="findAllMapUwBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.MASTER_BUSI_ITEM_ID, A.UW_BUSI_ID, A.INSURED_1, A.INSURED_2, 
			A.UW_STATUS, A.DECISION_CODE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, 
			A.BUSI_ITEM_ID, A.POLICY_ID FROM DEV_UW.T_UW_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_BUSI_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllUwBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.MASTER_BUSI_ITEM_ID, A.UW_BUSI_ID, A.INSURED_1, A.INSURED_2, 
			A.UW_STATUS, A.DECISION_CODE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, 
			A.BUSI_ITEM_ID, A.POLICY_ID FROM DEV_UW.T_UW_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" busi_item_ids  != null "><![CDATA[ AND A.BUSI_ITEM_ID in (${busi_item_ids}) ]]></if>
	</select>

<!-- 查询个数操作 -->
	<select id="findUwBusiProdTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_UW_BUSI_PROD A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryUwBusiProdForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UW_ID, B.MASTER_BUSI_ITEM_ID, B.UW_BUSI_ID, B.INSURED_1, B.INSURED_2, 
			B.UW_STATUS, B.DECISION_CODE, B.BUSI_PROD_CODE, B.APPLY_CODE, B.ORGAN_CODE, B.POLICY_CODE, 
			B.BUSI_ITEM_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.UW_ID, A.MASTER_BUSI_ITEM_ID, A.UW_BUSI_ID, A.INSURED_1, A.INSURED_2, 
			A.UW_STATUS, A.DECISION_CODE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, 
			A.BUSI_ITEM_ID, A.POLICY_ID FROM DEV_UW.T_UW_BUSI_PROD A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_BUSI_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 因UwBusiProdPO中没有BUSI_PRD_ID，PRODUCT_NAME_SYS 故用POLICY_ID，POLICY_CODE 代替 -->
	<select id="findUwBusiProdByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.UW_ID,
       A.MASTER_BUSI_ITEM_ID,
       A.UW_BUSI_ID,
       A.DECISION_CODE,
       A.APPLY_CODE,
       A.ORGAN_CODE,
       A.BUSI_ITEM_ID,
       B.BUSI_PRD_ID         POLICY_ID,
       C.PRODUCT_NAME_SYS    POLICY_CODE
  FROM (SELECT M.BUSI_ITEM_ID, M.BUSI_PRD_ID,M.UW_ID
          FROM DEV_UW.T_CONTRACT_BUSI_PROD M
          LEFT JOIN DEV_UW.T_BENEFIT_INSURED N
            ON M.BUSI_ITEM_ID = N.BUSI_ITEM_ID
         WHERE M.UW_ID = N.UW_ID]]>
           <if test=" busi_item_id  != null "><![CDATA[ AND M.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
           <if test=" uw_id  != null "><![CDATA[ AND M.UW_ID = #{uw_id} ]]></if>
           <include refid="queryUwBusiProdByCSApplyCodeCondition" />
         <![CDATA[   ) B,
         DEV_UW.T_UW_BUSI_PROD A,
       DEV_UW.T_BUSINESS_PRODUCT C
 WHERE A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
   and A.UW_ID = B.UW_ID
   AND B.BUSI_PRD_ID = C.BUSINESS_PRD_ID  ]]>
	</select>
	
	<!-- add by xuhp 保全核保下发核保决定组装通知书报文 根据uwid/policycode/busiitemid查询该保单下的所有的险种 2015年9月21日 -->
	<select id="findUwBusiProdByCSBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.UW_ID,
       A.MASTER_BUSI_ITEM_ID,
       A.UW_BUSI_ID,
       A.DECISION_CODE,
       A.APPLY_CODE,
       A.ORGAN_CODE,
       A.BUSI_ITEM_ID,
       B.BUSI_PRD_ID         POLICY_ID,
       C.PRODUCT_NAME_SYS    POLICY_CODE
  FROM (SELECT M.BUSI_ITEM_ID, M.BUSI_PRD_ID,M.UW_ID,M.Policy_Code
          FROM DEV_UW.T_CONTRACT_BUSI_PROD M
          LEFT JOIN DEV_UW.T_BENEFIT_INSURED N
            ON M.BUSI_ITEM_ID = N.BUSI_ITEM_ID
         WHERE M.UW_ID = N.UW_ID]]>
           <if test=" busi_item_id  != null "><![CDATA[ AND M.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
           <if test=" uw_id  != null "><![CDATA[ AND M.UW_ID = #{uw_id} ]]></if>
           <include refid="queryUwBusiProdByCSApplyCodeCondition" />
         <![CDATA[   ) B,
         DEV_UW.T_UW_BUSI_PROD A,DEV_UW.T_BUSINESS_PRODUCT C WHERE A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
   		 AND B.BUSI_PRD_ID = C.BUSINESS_PRD_ID AND A.UW_ID = B.UW_ID AND A.POLICY_CODE = B.POLICY_CODE ]]>
	</select>

	<!--查询再保信息 -->
	<select id="findRiAppProductinfosss" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select t.*, cp.TOTAL_PREM_AF  std_prem, cp.EXPIRY_DATE, cp.VALIDATE_DATE
  			from (SELECT DISTINCT T.BUSI_ITEM_ID, T.ITEM_ID, T.APPLY_CODE,
                         T.POLICY_CODE, T.CUSTOMER_ID, t.customer_name,
                         T.EXTRA_POINT, T.RI_APP_REASON, T.AMOUNT,
                         T.UNIT, T.CHARGE_PERIOD, T.CHARGE_YEAR,
                         T.BUSI_PROD_CODE, T.INITIAL_TYPE, T.REPLY_ADVICE,
                         T.REPLY_ADVICE_AMOUNT, T.REPLY_ADVICE_EXTRA_POINT, Y.REPLY_ADVICE_COMMENT,
                         Y.REPLY_ADD_INFO_COMMENT, Y.REPLY_TIME, Y.REPLY_USER,
                         Y.IS_REPLY, Y.UW_ID
           FROM DEV_UW.T_RI_APP_POLICY Y, DEV_UW.T_RI_APP_PRODUCT T
          WHERE T.RI_ID = Y.RI_ID ]]> 
          <if test=" uw_id  != null "><![CDATA[ AND Y.UW_ID = #{uw_id} ]]></if>
         <![CDATA[ ) t
		  left join DEV_UW.T_UW_PRODUCT up
		    on t.item_id = up.item_id
		   and t.apply_code = up.apply_code
		   and t.busi_item_id = up.busi_item_id
		   AND t.UW_ID = up.UW_ID
		  left join DEV_UW.T_CONTRACT_PRODUCT cp
		    on t.item_id = cp.item_id
		   and t.apply_code = cp.apply_code
		   and t.busi_item_id = cp.busi_item_id 
			AND t.UW_ID = cp.UW_ID 
		 ORDER BY t.BUSI_ITEM_ID ]]>
		
	</select>	
	
	<!--查询回复意见 -->
	<select id="UW_findUwRiAppPolicyPOInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT Y.UW_ID,Y.RI_ID,Y.APPLY_CODE,Y.POLICY_ID,Y.RI_NUM,Y.RI_APP_COMMENT,Y.IS_REPLY,Y.APPLY_RI_USER,Y.APPLY_RI_TIME,Y.POLICY_CODE, Y.REPLY_ADVICE_COMMENT,Y.REPLY_ADD_INFO_COMMENT,Y.REPLY_TIME,Y.REPLY_USER,y.update_time REACTION_TIME FROM 
		   DEV_UW.T_RI_APP_POLICY Y WHERE y.ri_num = (select max(ri_num) from  DEV_UW.T_RI_APP_POLICY where UW_ID = Y.UW_ID) and is_reply = 1 ]]>
		<if test=" uw_id  != null "><![CDATA[ AND Y.UW_ID = #{uw_id} ]]></if>
	</select>
		
	<!-- 查询所有操作 -->
	<select id="findAllUwBusiProdByUwid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.MASTER_BUSI_ITEM_ID, A.UW_BUSI_ID, A.DECISION_CODE, A.BUSI_PROD_CODE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, A.BUSI_ITEM_ID, 
			A.POLICY_ID,(select busi_item_id from DEV_UW.T_CONTRACT_BUSI_PROD where UW_LIST_ID = A.BUSI_ITEM_ID) CONT_BUSI_ITEM_ID  FROM DEV_UW.T_UW_BUSI_PROD  A WHERE ROWNUM <=  1000  ]]>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<![CDATA[ ORDER BY A.UW_BUSI_ID              ]]> 
	</select>
	
	<!-- add by xuhp 保全核保下发核保决定 维持原核保结论 查询抄单的保单层的核保结论 2015年9月23日 -->
	<select id="UW_findUwBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DECISION_CODE,A.UW_ID,A.POLICY_CODE,A.UW_BUSI_ID,to_char(A.renew_decision) renew_decision FROM DEV_UW.T_UW_BUSI_PROD A where 1=1 ]]>
		<include refid="queryUwBusiProdByUwIdCondition" />
		<include refid="queryUwBusiProdByPolicyCodeCondition" />
		<include refid="queryUwBusiProdByUwBusiItemIdCondition" />
	</select>
	<!-- 删除操作 -->	
	<delete id="delUwBusiProdByUwId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_UW_BUSI_PROD WHERE UW_ID = #{uw_id} ]]>
	</delete>
</mapper>
