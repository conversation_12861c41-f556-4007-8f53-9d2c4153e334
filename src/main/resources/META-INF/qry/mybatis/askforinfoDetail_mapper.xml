<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.AskforinfoDetailDaoImpl">
<!--
	<sql id="UW_askforinfoDetailWhereCondition">
		<if test=" question_content != null and question_content != ''  "><![CDATA[ AND A.QUESTION_CONTENT = #{question_content} ]]></if>
		<if test=" question_type  != null "><![CDATA[ AND A.QUESTION_TYPE = #{question_type} ]]></if>
		<if test=" detail_id  != null "><![CDATA[ AND A.DETAIL_ID = #{detail_id} ]]></if>
		<if test=" askforinfo_id  != null "><![CDATA[ AND A.ASKFORINFO_ID = #{askforinfo_id} ]]></if>
		<if test=" ask_question_code  != null "><![CDATA[ AND A.QUESTION_CODE = #{ask_question_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="UW_queryAskforinfoDetailByDetailIdCondition">
		<if test=" detail_id  != null "><![CDATA[ AND A.DETAIL_ID = #{detail_id} ]]></if>
	</sql>	
	<sql id="UW_queryAskforinfoDetailByAskforinfoIdCondition">
		<if test=" askforinfo_id  != null "><![CDATA[ AND A.ASKFORINFO_ID = #{askforinfo_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="UW_addAskforinfoDetail"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="detail_id">SELECT DEV_UW.S_T_ASKFORINFO_DETAIL.NEXTVAL FROM DUAL</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_ASKFORINFO_DETAIL                 (
				INSERT_TIMESTAMP, QUESTION_CONTENT, UPDATE_BY, INSERT_TIME, QUESTION_TYPE, DETAIL_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY, ASKFORINFO_ID, ASK_QUESTION_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{question_content, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{question_type, jdbcType=NUMERIC} , #{detail_id, jdbcType=NUMERIC}, CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , DEV_UW.S_T_ASKFORINFO.CURRVAL , #{ask_question_code, jdbcType=NUMERIC}  ) 
		 ]]>
	</insert>
	
	<!-- 修改时批量添加数据 -->
	<insert id="UW_addAskforinfoDetailforUpdate"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="detail_id">SELECT DEV_UW.S_T_ASKFORINFO_DETAIL.NEXTVAL FROM DUAL</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_ASKFORINFO_DETAIL                 (
				INSERT_TIMESTAMP, QUESTION_CONTENT, UPDATE_BY, INSERT_TIME, QUESTION_TYPE, DETAIL_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY, ASKFORINFO_ID, ASK_QUESTION_CODE ) 
			VALUES (
				CURRENT_TIMESTAMP, #{question_content, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{question_type, jdbcType=NUMERIC} , #{detail_id, jdbcType=NUMERIC}, CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{askforinfo_id, jdbcType=NUMERIC} , #{ask_question_code, jdbcType=NUMERIC}  ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="UW_deleteAskforinfoDetail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_ASKFORINFO_DETAIL                  WHERE DETAIL_ID               = #{detail_id              } ]]>
	</delete>

<!-- 修改操作 -->
	<update id="UW_updateAskforinfoDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_ASKFORINFO_DETAIL                  ]]>
		<set>
		<trim suffixOverrides=",">
			QUESTION_CONTENT = #{question_content, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    QUESTION_TYPE = #{question_type, jdbcType=NUMERIC} ,
		    DETAIL_ID = #{detail_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    ASKFORINFO_ID = #{askforinfo_id, jdbcType=NUMERIC} ,
		    ASK_QUESTION_CODE = #{ask_question_code, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE DETAIL_ID               = #{detail_id              } ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="UW_findAskforinfoDetailByDetailId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_CONTENT, A.QUESTION_TYPE, A.DETAIL_ID, 
			A.ASKFORINFO_ID, A.ASK_QUESTION_CODE FROM DEV_UW.T_ASKFORINFO_DETAIL                  A WHERE 1 = 1  ]]>
		<include refid="UW_queryAskforinfoDetailByDetailIdCondition" />
		<![CDATA[ ORDER BY A.DETAIL_ID               ]]>
	</select>
	
	<select id="UW_findAskforinfoDetailByAskforinfoId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_CONTENT, A.QUESTION_TYPE, A.DETAIL_ID, A.ASKFORINFO_ID, A.ASK_QUESTION_CODE, CUSTOMER_ID, 
		ROLE_TYPE, (SELECT C.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER C WHERE C.CUSTOMER_ID = A.CUSTOMER_ID) AS CUSTOMER_NAME
		FROM DEV_UW.T_ASKFORINFO_DETAIL A WHERE 1 = 1  ]]>
		<include refid="UW_queryAskforinfoDetailByAskforinfoIdCondition" />
		<![CDATA[ ORDER BY A.DETAIL_ID               ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="UW_findAllMapAskforinfoDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_CONTENT, A.QUESTION_TYPE, A.DETAIL_ID, 
			A.ASKFORINFO_ID, A.ASK_QUESTION_CODE FROM DEV_UW.T_ASKFORINFO_DETAIL                  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DETAIL_ID               ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="UW_findAllAskforinfoDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_CONTENT, A.QUESTION_TYPE, A.DETAIL_ID, 
			A.ASKFORINFO_ID, A.ASK_QUESTION_CODE FROM DEV_UW.T_ASKFORINFO_DETAIL                  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DETAIL_ID               ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="UW_findAskforinfoDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_ASKFORINFO_DETAIL                  A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="UW_queryAskforinfoDetailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.QUESTION_CONTENT, B.QUESTION_TYPE, B.DETAIL_ID, 
			B.ASKFORINFO_ID, B.ASK_QUESTION_CODE FROM (
					SELECT ROWNUM RN, A.QUESTION_CONTENT, A.QUESTION_TYPE, A.DETAIL_ID, 
			A.ASKFORINFO_ID, A.ASK_QUESTION_CODE FROM DEV_UW.T_ASKFORINFO_DETAIL                  A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DETAIL_ID               ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 删除所要资料/问卷通知书项目信息 -->
	<delete id="UW_deleteAskforinfoDetailByDocumentNO" parameterType="java.util.Map">
		<![CDATA[ DELETE DEV_UW.T_ASKFORINFO_DETAIL A WHERE A.ASKFORINFO_ID IN
       		( SELECT ASKFORINFO_ID FROM DEV_UW.T_ASKFORINFO WHERE DOC_LIST_ID = #{doc_list_id} )]]>
	</delete>
	
	
	<!-- 删除纳税身份变动确认声明通知书项目信息 -->
	<delete id="UW_deleteResidentInfoDetailByDocumentNO" parameterType="java.util.Map">
		<![CDATA[ DELETE DEV_UW.T_RESIDENTINFO_DETAIL A WHERE A.RESIDENTINFO_ID IN
       		( SELECT RESIDENTINFO_ID FROM DEV_UW.T_RESIDENTINFO WHERE DOC_LIST_ID = #{doc_list_id} )]]>
	</delete>
	
	
	
	<!-- 增加数据 -->
	<insert id="UW_addResidentforinfoDetail"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="detail_id">SELECT DEV_UW.S_T_ASKFORINFO_DETAIL.NEXTVAL FROM DUAL</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_RESIDENTINFO_DETAIL                 (
				INSERT_TIMESTAMP, UPDATE_BY, INSERT_TIME, DETAIL_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, INSERT_BY,RESIDENTINFO_ID, RES_QUESTION_CODE,RES_REASON,CUSTOMER_ROLE_TYPE) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , SYSDATE , #{detail_id, jdbcType=NUMERIC}, CURRENT_TIMESTAMP
				, SYSDATE , #{insert_by, jdbcType=NUMERIC} , #{residentinfo_id, jdbcType=NUMERIC} , #{res_question_code, jdbcType=NUMERIC}
				, #{res_reason, jdbcType=VARCHAR},#{customer_role_type, jdbcType=NUMERIC}
				 ) 
		 ]]>
	</insert>
	
	<!-- 通过核保Id查询问卷信息 -->
	<select id="UW_queryAskforinfoDetailByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DD.QUESTION_CONTENT, AD.UW_ID, DD.ASKFORINFO_ID, DD.DETAIL_ID FROM DEV_UW.T_ASKFORINFO AD INNER JOIN DEV_UW.T_ASKFORINFO_DETAIL DD ON DD.ASKFORINFO_ID = AD.ASKFORINFO_ID WHERE 1=1 ]]>
		<if test=" uw_id != null "><![CDATA[ AND AD.UW_ID = #{uw_id } ]]></if>
		<if test=" question_content != null and question_content != '' "><![CDATA[ AND DD.QUESTION_CONTENT = #{question_content } ]]></if>
	</select>
	
</mapper>
