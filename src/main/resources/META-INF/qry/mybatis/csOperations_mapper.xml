<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.ICsAreaOrganDao">

	<sql id="csFindTotalWhereCondition">
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.textAttribute6 like #{organ_code} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.textAttribute7 = #{service_code} ]]></if>
		<if test=" created_date_start != null and created_date_start != ''"><![CDATA[ AND a.createdDate >=  to_date(#{created_date_start},'yyyy-MM-dd HH24:mi:ss')]]></if>
		<if test=" created_date_end != null and created_date_end != '' "><![CDATA[ AND a.createdDate <= to_date(#{created_date_end},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" state != null and state != ''  "><![CDATA[ AND a.state = 'ASSIGNED' ]]></if>
		<if test=" pre_flag == 0 "><![CDATA[ AND (b.pre_flag is null or b.pre_flag ='0') ]]></if>
		<if test=" pre_flag == 1 "><![CDATA[ AND b.pre_flag = '1' ]]></if>
		<if test=" effect_date_start != null and effect_date_start != ''"><![CDATA[ AND b.PRE_VALIDATE_DATE >=  to_date(#{effect_date_start},'yyyy-MM-dd HH24:mi:ss')]]></if>
		<if test=" effect_date_end != null and effect_date_end != '' "><![CDATA[ AND b.PRE_VALIDATE_DATE < to_date(#{effect_date_end},'yyyy-MM-dd HH24:mi:ss') ]]></if>
		<if test=" edor_typein != null and edor_typein != ''  "><![CDATA[ AND a.textAttribute7 in ( ${edor_typein} )]]></if>
		<if test=" fee_amount != null and fee_amount != ''  "><![CDATA[ AND a.numberAttribute5 >= #{fee_amount_start} and a.numberAttribute5 <= #{fee_amount_end}]]></if>
		<if test=" delay_days != null and delay_days != ''  "><![CDATA[ AND trunc(sysdate-a.dateAttribute2,1) > #{delay_days_start} and trunc(sysdate-a.dateAttribute2,1) <= #{delay_days_end}]]></if>
	</sql>
	
	<sql id="userCodeCondition">
	    <if test=" user_code != null and user_code != ''  "><![CDATA[ AND  a.protectedTextAttribute18 = #{user_code} ]]></if>
	</sql>
	
	<sql id="userCodeCondition1">
	    <if test=" user_code != null and user_code != ''  "><![CDATA[ AND  (select user_name from dev_pas.T_UDMP_USER c where c.user_id =b.review_id) = #{user_code} ]]></if>
	</sql>
 

	<!--时段维度查询-->
	<select id="findCsAreaOrganTimeDimension" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT C.AREA_CODE,C.AREA_NAME,A.ORGAN_NAME,A.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG A INNER JOIN APP___PAS__DBUSER.T_CS_AREA_ORGAN B ON A.ORGAN_CODE=B.ORGAN_CODE 
		INNER JOIN APP___PAS__DBUSER.T_CS_AREA C ON B.AREA_ID=C.AREA_ID 
		GROUP BY B.AREA_ID,C.AREA_NAME,A.ORGAN_NAME,A.ORGAN_CODE,C.AREA_CODE ORDER BY C.AREA_NAME,A.ORGAN_CODE]]>
	</select>
	<!--权限维度-->
	<select id="findAuthorityDimension" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT r.ROLE_ID,r.ROLE_NAME,(select rr.result_value_1
				  from APP___PAS__DBUSER.t_udmp_permission_ratetable rr
				 where rr.permission_id = rp.permission_id
				   and rr.column_value = 'edorTypeIn') EDOR_TYPEIN, /*可操作保全项*/
			   (select rr.result_value_1
				  from APP___PAS__DBUSER.t_udmp_permission_ratetable rr
				 where rr.permission_id = rp.permission_id
				   and rr.column_value = 'delayDays') DELAY_DAYS, /*延期天数范围*/
			   (select rr.result_value_1
				  from APP___PAS__DBUSER.t_udmp_permission_ratetable rr
				 where rr.permission_id = rp.permission_id
				   and rr.column_value = 'feeAmount') FEE_AMOUNT /*金额范围*/
		  FROM APP___PAS__DBUSER.T_UDMP_ROLE            r,
			   APP___PAS__DBUSER.t_udmp_role_permission rp,
			   APP___PAS__DBUSER.T_UDMP_PERMISSION_TYPE pt

		 where r.role_id = rp.role_id
		   and rp.permission_type_id = pt.permission_type_id
		   and r.role_type = 2 /*数据权限*/
		   and pt.permission_type_name = 'CUS_REVIEW_DATA_AUTH' /*复核权限*/ 
		   and role_name in ('BQ01','BQ02','BQ03','BQ04','BQ05','BQ06','BQ07','BQ08','BQ09','BQ10','BQ11','BQ12')
           order by role_name
	]]>
	</select>



<!-- 查询今日总量 -->
	<select id="findTodayTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ select count(*)
  from (select b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.state = 'ASSIGNED'
           and a.Isgroup = 'T'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)]]>
        <include refid="csFindTotalWhereCondition" />  
       <![CDATA[ union
        
       select b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.state = 'ASSIGNED'
           and a.Isgroup = 'F'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)  ]]>
           <include refid="csFindTotalWhereCondition" />                        
        <![CDATA[union
       	select b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010202'
           and a.state = 'ASSIGNED'
           and a.textAttribute8='21'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)  ]]>
           <include refid="csFindTotalWhereCondition" />  
                   <![CDATA[union
       	select b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.endDate >= TRUNC(sysdate)
           and b.review_result = '1'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image) ]]>
           <include refid="csFindTotalWhereCondition" />
                   <![CDATA[union
       	select b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.endDate >= TRUNC(sysdate)
           and b.review_result = '3'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image) ]]>
           <include refid="csFindTotalWhereCondition" />
          <![CDATA[ )]]>
	</select>
	
	<!-- 查询共享池数量 -->
		<select id="findGXPoolTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		select count(*) from (
          select  b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
           where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.state = 'ASSIGNED'
           and a.Isgroup = 'T'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)
		 ]]>
		 <include refid="csFindTotalWhereCondition" />
		 <![CDATA[ group by b.accept_code)]]>
 	</select>
 	
 	<!-- 查询个人池数量 -->
 	<select id="findGRPoolTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		select count(*) from (
          select  b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
          where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.state = 'ASSIGNED'
           and a.Isgroup = 'F'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)                        
		 ]]>
		 <include refid="csFindTotalWhereCondition" />
		 <include refid="userCodeCondition" />
		 <![CDATA[ group by b.accept_code)]]>
 	</select>
 	
 	<!-- 查询复核修改数量 -->
 	<select id="findFXTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		select count(*) from (
          select  b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
          where a.textAttribute2 = b.accept_code
           and a.title = 'CS010202'
           and a.state = 'ASSIGNED'
           and a.textAttribute8='21'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)                        
		 ]]>
		 <include refid="csFindTotalWhereCondition" />
		 <include refid="userCodeCondition1" />
		 <![CDATA[ group by b.accept_code)]]>
 	</select>
 	
 	<!-- 查询复核通过数量 -->
 	<select id="findFTTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		select count(*) from (
          select  b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
           where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.endDate >= TRUNC(sysdate)
           and b.review_result = '1'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)                        
		 ]]>
		 <include refid="csFindTotalWhereCondition" />
		 <include refid="userCodeCondition1" />
		 <![CDATA[ group by b.accept_code)]]>
 	</select>
 	
 	<!-- 查询复核终止数量 -->
 	<select id="findFZTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		select count(*) from (
          select  b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
           where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.endDate >= TRUNC(sysdate)
           and b.review_result = '3'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)                       
		 ]]>
		 <include refid="csFindTotalWhereCondition" />
		 <include refid="userCodeCondition1" />
		 <![CDATA[ group by b.accept_code)]]>
 	</select>
	
	   <!-- 查询未办结数量 -->
	 	<select id="findWBTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
        select COUNT(*)
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.state = 'ASSIGNED'
           and a.createdDate>=TRUNC(sysdate-30) 
           and a.createdDate<=to_date(concat((select to_char(sysdate,'yyyy-mm-dd') from dual), '16:00:00'),'yyyy-MM-dd HH24:mi:ss')
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)                        
		 ]]>
		 <include refid="csFindTotalWhereCondition" />
 	</select>
 	
 	    <!-- 查询应办结数量 -->
 	 	<select id="findYBTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
       select count(*)
    from (select b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.state = 'ASSIGNED'
           and a.isgroup = 'T'
           and a.createdDate>=TRUNC(sysdate-30) 
           and a.createdDate<=to_date(concat((select to_char(sysdate,'yyyy-mm-dd') from dual), '16:00:00'),'yyyy-MM-dd HH24:mi:ss') 
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)]]>
            <include refid="csFindTotalWhereCondition" />
        <![CDATA[
        union
        
        select b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and a.isgroup = 'F'
           and a.assigneddate >= to_date(concat((select to_char(sysdate-1,'yyyy-mm-dd') from dual), '16:00:00'),'yyyy-MM-dd HH24:mi:ss')
           and a.assigneddate <= to_date(concat((select to_char(sysdate,'yyyy-mm-dd') from dual), '16:00:00'),'yyyy-MM-dd HH24:mi:ss')
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)                       
		 ]]>
		 <include refid="csFindTotalWhereCondition" />
		 <![CDATA[ )]]>
 	</select>
 	
 	<!-- 查询时段数量 -->
 	<select id="findSDTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
 	 <![CDATA[
 	select count(*) from(
select  b.accept_code
          from dev_bpm.WFTASK a, dev_pas.t_cs_accept_change b
         where a.textAttribute2 = b.accept_code
           and a.title = 'CS010214'
           and( b.collection_flag ='1' or a.textAttribute7 in('RB','EA','PU','AE'))
           and a.textAttribute2 in (select accept_code from dev_pas.t_image)]]>
  <include refid="csFindTotalWhereCondition" />
           <![CDATA[ group by b.accept_code)]]>
 	</select>
 	
 	<!-- 查询所有保全项 -->
	<select id="findAllCsServiceInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[            select a.service_code,b.service_name
                                    from dev_pas.T_SERVICE_REVIEW_CFG a,dev_pas.t_service b
                                   where a.status = '1'
                                   and  a.service_code = b.service_code
                                   and rownum < 1000 ]]>
	</select>
	
	<!-- 查询区域下所有用户 -->
	<select id="findAllCsAreaUser" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT C.AREA_CODE,C.AREA_NAME,A.USER_NAME,A.REAL_NAME FROM APP___PAS__DBUSER.T_UDMP_USER A INNER JOIN APP___PAS__DBUSER.T_CS_AREA_PERSON B ON A.USER_ID=B.USER_ID 
                   INNER JOIN APP___PAS__DBUSER.T_CS_AREA C ON B.AREA_ID=C.AREA_ID 
                   GROUP BY B.AREA_ID,C.AREA_NAME,A.USER_NAME,A.REAL_NAME,C.AREA_CODE ORDER BY C.AREA_NAME,A.USER_NAME]]>
	</select>
 	
</mapper>
