<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IQueryConditionInfoDao">
	<!-- 特约信息查询 -->
	<select id="qry_queryConditionInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT
		       UBP.BUSI_PROD_CODE,/* 险种编码*/
		       BP.PRODUCT_NAME_SYS AS PRODUCT_NAME,/* 险种名称*/
		       LISTAGG(UC.CONDITION_DESC,'^') WITHIN GROUP(ORDER BY
		       UBP.BUSI_PROD_CODE ASC) AS  CONDITION_DESC /* 特约信息*/ 
	    FROM DEV_PAS.T_CONTRACT_MASTER M 
		    INNER JOIN DEV_UW.T_UW_CONDITION UC ON  M.APPLY_CODE=UC.APPLY_CODE
		    LEFT JOIN DEV_UW.T_UW_BUSI_PROD UBP
		         ON UC.UW_ID = UBP.UW_ID AND UC.APPLY_CODE=UBP.APPLY_CODE AND UC.BUSI_PROD_CODE = UBP.BUSI_PROD_CODE
		    LEFT JOIN DEV_UW.T_UW_MASTER UM ON UC.UW_ID=UM.UW_ID
		    LEFT JOIN DEV_UW.T_PRODUCT_DECISION TPD ON UBP.DECISION_CODE = TPD.DECISION_CODE
		    LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON UBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
	    WHERE TPD.DECISION_CODE = '33'/* 核保决定为特约信息*/
	   		  AND M.POLICY_CODE = #{policy_code} 
	    GROUP BY  UBP.BUSI_PROD_CODE, BP.PRODUCT_NAME_SYS
	    ]]>
	</select>
	<!-- 投被保人手机号查询 -->
	<select id="qry_queryAppntAndInsuredPhone" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT APPNT_PHONE, INSURED_PHONE, SECOND_INSURED_PHONE
		  FROM (SELECT AD.MOBILE_TEL AS APPNT_PHONE/*投保人手机号*/, PH.APPLY_CODE/*投保单号*/
		          FROM DEV_PAS.T_POLICY_HOLDER PH
		         INNER JOIN DEV_PAS.T_ADDRESS AD
		            ON PH.ADDRESS_ID = AD.ADDRESS_ID
		         WHERE PH.APPLY_CODE = #{apply_code}) A
		 LEFT JOIN (SELECT AD.MOBILE_TEL AS INSURED_PHONE, PH.APPLY_CODE
		               FROM DEV_PAS.T_BENEFIT_INSURED bi
                   right join DEV_PAS.T_INSURED_LIST PH
                   on bi.INSURED_ID =  PH.LIST_ID
                    INNER JOIN DEV_PAS.T_ADDRESS AD
		            ON PH.ADDRESS_ID = AD.ADDRESS_ID
		            inner join DEV_PAS.T_CUSTOMER CU
                 	on CU.CUSTOMER_ID= PH.CUSTOMER_ID
		              WHERE PH.APPLY_CODE = #{apply_code} 
		           and bi.ORDER_ID = 1]]>
		           <if test=" customer_name != null and customer_name != '' ">
        			<![CDATA[ AND CU.CUSTOMER_NAME =  #{customer_name }]]>
       			   </if> 
     				  <if test=" customer_birthday != null and customer_birthday != '' ">
        			<![CDATA[ AND CU.CUSTOMER_BIRTHDAY =  #{customer_birthday }]]>
        		</if> 
        		 <if test=" customer_gender != null and customer_gender != '' ">
        			<![CDATA[ AND CU.CUSTOMER_GENDER =  #{customer_gender }]]>
        		</if> 
        		 <if test=" customer_cert_type != null and customer_cert_type != '' ">
        			<![CDATA[ AND CU.CUSTOMER_CERT_TYPE =  #{customer_cert_type }]]>
        		</if> 
        		 <if test=" customer_certi_code != null and customer_certi_code != '' ">
        			<![CDATA[ AND CU.CUSTOMER_CERTI_CODE =  #{customer_certi_code }]]>
        		</if> 
        		<![CDATA[
		              ) B
		    ON A.APPLY_CODE = B.APPLY_CODE
		    
		  /*需求 #122752-投被保人手机号查询接口 新增----start */
		  LEFT JOIN (SELECT AD.MOBILE_TEL AS SECOND_INSURED_PHONE, PH.APPLY_CODE
		               FROM DEV_PAS.T_BENEFIT_INSURED bi
		              right join DEV_PAS.T_INSURED_LIST PH
		                 on bi.INSURED_ID = PH.LIST_ID
		              INNER JOIN DEV_PAS.T_ADDRESS AD
		                 ON PH.ADDRESS_ID = AD.ADDRESS_ID
		              WHERE PH.APPLY_CODE = #{apply_code}
		                and bi.ORDER_ID = 2
		                and rownum = 1) C
		    ON B.APPLY_CODE = C.APPLY_CODE
		    /*需求 #122752-投被保人手机号查询接口 新增----end */
		    
		UNION
		SELECT APPNT_PHONE, INSURED_PHONE, SECOND_INSURED_PHONE
		  FROM (SELECT AD.MOBILE_TEL AS APPNT_PHONE/*被保人手机号*/, PH.APPLY_CODE/*投保单号*/
		          FROM DEV_NB.T_NB_POLICY_HOLDER PH
		         INNER JOIN DEV_NB.T_ADDRESS AD
		            ON PH.ADDRESS_ID = AD.ADDRESS_ID
		         WHERE PH.APPLY_CODE = #{apply_code}) A
		 left JOIN (SELECT AD.MOBILE_TEL AS INSURED_PHONE, PH.APPLY_CODE
		               FROM DEV_NB.T_NB_BENEFIT_INSURED  nbi
                     right join DEV_NB.T_NB_INSURED_LIST PH
                     on nbi.INSURED_ID =  PH.LIST_ID
                      inner join DEV_NB.T_CUSTOMER CU
                     on CU.CUSTOMER_ID= PH.CUSTOMER_ID
                     INNER JOIN DEV_NB.T_ADDRESS AD
                     ON PH.ADDRESS_ID = AD.ADDRESS_ID
                  WHERE PH.APPLY_CODE = #{apply_code}
                  and nbi.ORDER_ID = 1]]>
                   <if test=" customer_name != null and customer_name != '' ">
        			<![CDATA[ AND CU.CUSTOMER_NAME =  #{customer_name }]]>
       			   </if> 
     				  <if test=" customer_birthday != null and customer_birthday != '' ">
        			<![CDATA[ AND CU.CUSTOMER_BIRTHDAY =  #{customer_birthday }]]>
        		</if> 
        		 <if test=" customer_gender != null and customer_gender != '' ">
        			<![CDATA[ AND CU.CUSTOMER_GENDER =  #{customer_gender }]]>
        		</if> 
        		 <if test=" customer_cert_type != null and customer_cert_type != '' ">
        			<![CDATA[ AND CU.CUSTOMER_CERT_TYPE =  #{customer_cert_type }]]>
        		</if> 
        		 <if test=" customer_certi_code != null and customer_certi_code != '' ">
        			<![CDATA[ AND CU.CUSTOMER_CERTI_CODE =  #{customer_certi_code }]]>
        		</if> 
        			<![CDATA[ 
              ) B
		    ON A.APPLY_CODE = B.APPLY_CODE
		    		    
		    /*需求 #122752-投被保人手机号查询接口 新增----start */
		  LEFT JOIN (SELECT AD.MOBILE_TEL AS SECOND_INSURED_PHONE, PH.APPLY_CODE
		               FROM DEV_NB.T_NB_BENEFIT_INSURED nbi
		              right join DEV_NB.T_NB_INSURED_LIST PH
		                 on nbi.INSURED_ID = PH.LIST_ID
		              INNER JOIN DEV_NB.T_ADDRESS AD
		                 ON PH.ADDRESS_ID = AD.ADDRESS_ID
		              WHERE PH.APPLY_CODE = #{apply_code}
		                and nbi.ORDER_ID = 2
		                and rownum = 1) C
		    ON B.APPLY_CODE = C.APPLY_CODE
		    /*需求 #122752-投被保人手机号查询接口 新增----end */
		    
		 WHERE NOT EXISTS (SELECT *
		          FROM DEV_PAS.T_CONTRACT_MASTER M
		         WHERE M.APPLY_CODE = #{apply_code})
		 ]]>
	</select>
	<!-- 查询客户作为投保人且通过银保通电子渠道投保的有效保单信息-> 客户可申请纸质保单查询 -->
	<select id="qry_queryPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT PH.POLICY_CODE,/*保单号*/
			   TCM.CHANNEL_TYPE/*销售渠道*/ 
		  FROM DEV_PAS.T_POLICY_HOLDER PH
		  	   LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM ON PH.POLICY_CODE = TCM.POLICY_CODE
		  WHERE ((TCM.SUBMIT_CHANNEL = '1' /*银保通电子渠道*/
       AND TCM.SUBINPUT_TYPE!='15') /*非银行柜面*/  or (tcm.submit_channel='11' and tcm.media_type=1)
       OR (TCM.SUBMIT_CHANNEL = '24')
       OR (TCM.SUBMIT_CHANNEL = '26')
       OR (TCM.SUBMIT_CHANNEL = '27')) 
		   AND TCM.LIABILITY_STATE = '1'/*保单状态为‘有效’*/
		     AND PH.CUSTOMER_ID = 
		     (SELECT C.CUSTOMER_ID FROM DEV_PAS.T_CUSTOMER C 
		     		 WHERE C.CUSTOMER_ID = #{old_customer_id})/*老核心客户号获取新核心客户号*/
		]]>
	</select>
	<!-- 获取保单打印信息 -->
	<select id="qry_queryPolicyPrint" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT (SELECT LS.STATUS_NAME
	          FROM DEV_PAS.T_LIABILITY_STATUS LS
	         WHERE LS.STATUS_CODE = TCM.LIABILITY_STATE) AS LIABILITY_STATE /*保单状态*/,
	       TCM.POLICY_CODE, /*保单号*/
	       (CASE TCM.LIABILITY_STATE
	         WHEN 3 THEN
	          TCM.END_CAUSE
	         WHEN 4 THEN
	          TCM.LAPSE_CAUSE
	       END) AS STATUS_MESSAGE, /*保单状态原因*/
	       TO_CHAR(TCM.VALIDATE_DATE, 'yyyy-MM-dd') AS VALIDATE_DATE, /*生效日期*/
	       TO_CHAR(TCM.APPLY_DATE, 'yyyy-MM-dd') AS APPLY_DATE, /*投保日期*/
	       CP.CUSTOMER_NAME AS HOLDER_NAME, /*投保人姓名*/
	       CI.CUSTOMER_NAME AS INSURED_NAME, /*被保人信息*/
	       BP.PRODUCT_NAME_SYS, /*主险名称*/
	       TO_CHAR((SELECT SUM(PP.PRINT_TIMES)
	                 FROM DEV_NB.T_POLICY_PRINT PP
	                WHERE PP.PRINT_TYPE != '2'
	                  AND PP.PRINT_STATUS = '3'
	                  AND PP.POLICY_CODE = TCM.POLICY_CODE
	                GROUP BY PP.PRINT_TYPE)) AS PRINT_TIMES, /*纸质保单打印次数*/
	       TO_CHAR(A.P_ACKNOWLEDGE_DATE,'yyyy-MM-dd') AS P_ACKNOWLEDGE_DATE,/*网银纸质保单回执日期*/ 
	       (SELECT PP.PRINT_TYPE FROM DEV_NB.T_POLICY_PRINT PP
		        		 WHERE PP.POLICY_CODE = TCM.POLICY_CODE AND ROWNUM = 1) AS PRINT_TYPE, /*打印类型*/ 
		        (SELECT PP.REISSUE_FLAG FROM DEV_NB.T_POLICY_PRINT PP
		        		 WHERE PP.POLICY_CODE = TCM.POLICY_CODE AND ROWNUM = 1)	AS REISSUE_FLAG, /*保全补发标记*/	 
	       to_char(TCM.SUBMIT_CHANNEL) channel_identification /*渠道标识*/,
		         		TCM.Input_Type input_type,/*保单录入方案*/
       BP.PRODUCT_NAME_SYS risk_name,/*险种名称*/
       BP.PRODUCT_NAME_STD risk_abbr_name, /*险种简称*/
       CBP.BUSI_PROD_CODE risk_code,/*险种代码*/
       '主险'  main_risk_flag   /*主险标识*/
	  FROM DEV_PAS.T_CONTRACT_MASTER TCM
	 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
	    ON CBP.POLICY_CODE = TCM.POLICY_CODE
	 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
	    ON CBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
	 INNER JOIN (SELECT C.CUSTOMER_NAME, C.CUSTOMER_ID, PH.POLICY_CODE
	               FROM DEV_PAS.T_CUSTOMER C
	              INNER JOIN DEV_PAS.T_POLICY_HOLDER PH
	                 ON C.CUSTOMER_ID = PH.CUSTOMER_ID
	              WHERE PH.POLICY_CODE = #{policy_code}) CP
	    ON TCM.POLICY_CODE = CP.POLICY_CODE
	 INNER JOIN (SELECT C.CUSTOMER_NAME,
	                    C.CUSTOMER_ID,
	                    IL.POLICY_CODE,
	                    BI.PRODUCT_CODE
	               FROM DEV_PAS.T_CUSTOMER C
	               LEFT JOIN DEV_PAS.T_INSURED_LIST IL
	                 ON C.CUSTOMER_ID = IL.CUSTOMER_ID
	               LEFT JOIN DEV_PAS.T_BENEFIT_INSURED BI
	                 ON IL.LIST_ID = BI.INSURED_ID
	              WHERE BI.ORDER_ID = 1
	                AND IL.POLICY_CODE = #{policy_code}) CI
	    ON TCM.POLICY_CODE = CI.POLICY_CODE
	   AND BP.PRODUCT_CODE_SYS = CI.PRODUCT_CODE
	  LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT A
		    ON A.POLICY_CODE = TCM.POLICY_CODE
		   LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT AP
		    ON AP.POLICY_ID = TCM.POLICY_ID 
	 WHERE CBP.MASTER_BUSI_ITEM_ID IS NULL /*主险标识*/
	   AND TCM.POLICY_CODE = #{policy_code} /*保单号*/
		]]>
	</select>

<!-- 获取保单打印信息附加险列表 -->
	<select id="qry_queryPolicyPrintRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT  TCM.POLICY_CODE, /*保单号*/
       BP.PRODUCT_NAME_SYS risk_name,/*险种名称*/
       BP.PRODUCT_NAME_STD risk_abbr_name, /*险种简称*/
       CBP.BUSI_PROD_CODE risk_code,/*险种代码*/
       '附加险'  main_risk_flag   /*主险标识*/
		 FROM DEV_PAS.T_CONTRACT_MASTER TCM
		 		INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP ON CBP.POLICY_CODE = TCM.POLICY_CODE
		 		INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON CBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
		 		WHERE 1=1 and CBP.MASTER_BUSI_ITEM_ID IS not NULL /*主险标识*/ 
		  AND TCM.POLICY_CODE = #{policy_code}/*保单号*/ ]]>
	</select>	
	
		<select id="qry_queryInputSequence" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT PH.APPLY_CODE,PH.input_sequence from DEV_NB.T_NB_INSURED_LIST PH  WHERE PH.APPLY_CODE = #{apply_code}
                   and ph.ORDER_ID = 1]]>
    </select>
 
 	<!-- 查询保全打印的所有结果 -->
	<select id="CS_findAllCsPolicyPrintInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT (CASE
             WHEN T.POLICY_CODE <> T.RELATIOIN_POLICY_CODE AND
                  T.DOUBLE_MAINRISK_FLAG = '1' THEN
              (SELECT MIN(PRINT_TIME)
                 FROM (SELECT DECODE(PPA.PRINT_TYPE,'1',PPA.BPO_PRINT_DATE,PPA.PRINT_TIME) AS PRINT_TIME, PPA.POLICY_CODE, PPA.PRINT_TYPE
                         FROM DEV_PAS.T_POLICY_PRINT PPA
                      ) A
                WHERE POLICY_CODE = T.RELATIOIN_POLICY_CODE
                  AND PRINT_TIME IS NOT NULL
                  AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1'))
             ELSE
              (SELECT MIN(PRINT_TIME)
                 FROM (SELECT DECODE(PPA.PRINT_TYPE,'1',PPA.BPO_PRINT_DATE,PPA.PRINT_TIME) AS PRINT_TIME, PPA.POLICY_CODE, PPA.PRINT_TYPE
                         FROM DEV_PAS.T_POLICY_PRINT PPA
                      ) A
                WHERE POLICY_CODE = T.POLICY_CODE
                  AND PRINT_TIME IS NOT NULL
                  AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1'))
           END) AS print_time /*打印日期*/
      FROM (SELECT DISTINCT CM.POLICY_ID,
                            CM.POLICY_CODE,
                            CM.APPLY_CODE,
                            (CASE
                              WHEN (CM.DOUBLE_MAINRISK_FLAG = '1' AND
                                   CM.RELATION_POLICY_CODE IS NULL) THEN
                               (SELECT APPLY_CODE
                                  FROM DEV_PAS.T_CONTRACT_MASTER NCM
                                 WHERE NCM.RELATION_POLICY_CODE = CM.POLICY_CODE)
                              ELSE
                               CM.APPLY_CODE
                            END) AS RELATIOIN_APPLY_CODE, /*双主险关联投保单号*/
                            (CASE
                              WHEN (CM.DOUBLE_MAINRISK_FLAG = '1' AND
                                   CM.RELATION_POLICY_CODE IS NULL) THEN
                               (SELECT POLICY_CODE
                                  FROM DEV_PAS.T_CONTRACT_MASTER NCM
                                 WHERE NCM.RELATION_POLICY_CODE = CM.POLICY_CODE)
                              ELSE
                               CM.POLICY_CODE
                            END) AS RELATIOIN_POLICY_CODE, /*双主险关联保单号*/
                            CM.DOUBLE_MAINRISK_FLAG /*双主险标识*/
              FROM DEV_PAS.T_CONTRACT_MASTER CM
             WHERE 1 = 1 
         AND CM.POLICY_CODE = #{policy_code} 
               AND ROWNUM = 1) T ]]>
	</select>
	
	<select id="findCsPaperPolicyPrint" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
              select CAC.accept_id
               FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
                    APP___PAS__DBUSER.T_CS_POLICY_CHANGE C,
                    APP___PAS__DBUSER.T_POLICY_REISSUE T
              WHERE cac.change_id = c.change_id
                and cac.accept_id = c.accept_id
                and t.policy_chg_id = c.policy_chg_id
                and c.change_id = t.change_id
                and t.accept_code = cac.accept_code
                and cac.service_code = 'LR'
                and cac.Accept_Status = '18'
                and t.print_type in ('0','2')
                and c.policy_code =#{policy_code}
                
                union all
                
               select CAC.accept_id
               FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
                    APP___PAS__DBUSER.T_CS_POLICY_CHANGE C
              WHERE cac.change_id = c.change_id
                and cac.accept_id = c.accept_id
                and cac.service_code in ('PU','PT')
                and cac.Accept_Status = '18'
                and c.reprint_flag = '1'
                and c.print_type in ('0','2')
                and c.policy_code =#{policy_code}
                                
                union all
                
               select CAC.accept_id
               FROM APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC,
                    APP___PAS__DBUSER.T_CS_POLICY_CHANGE C
              WHERE cac.change_id = c.change_id
                and cac.accept_id = c.accept_id
                and cac.service_code = 'RN'
                and cac.Accept_Status = '18'
                and c.policy_code =#{policy_code}
		]]>
	</select>
    
    <!-- 保单打印黑章信息查询 -->
	<select id="qry_queryBlackInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT EP.INSERT_TIME,
		       EP.LIST_ID,/*流水号*/
		       EP.VERIFY_CODE,/*验证码*/
		       EP.ANTI_FORGERY_CODE, /*防伪码*/
		       EP.ACCEPT_CODE,/*受理号*/
		       EP.ENDORSE_TYPE,/*批单类型*/
		       EP.ENDORSE_CODE,/*保单号*/
		       EP.STATUS,/*状态*/
		       EP.PRINT_DATE/*打印日期*/
	    FROM  DEV_PAS.T_ENDOR_PRINT EP
	    WHERE EP.ENDORSE_CODE = #{policy_code}
	    AND EP.STATUS = 1
	    AND ROWNUM = 1
	    ORDER BY EP.PRINT_DATE DESC
	    ]]>
	</select>
	<!-- 保单打印契约红章信息查询 -->
	<select id="qry_queryRedInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT TOS.STAMPNO
          FROM DEV_NB.T_OUTSOURCE_STAMP TOS
          WHERE TOS.POLICY_CODE = #{policy_code} 
          AND ROWNUM = 1
          ORDER BY TOS.UPDATE_TIME DESC    
	    ]]>
	</select>
	<!-- 保单打印保单保全红章信息查询 -->
	<select id="qry_queryCsRedInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT TCBP.RED_CHAPTER_NUMBER
          FROM DEV_PAS.T_CS_BPO_PRINT_RETURN TCBP
          WHERE TCBP.POLICY_CODE = #{policy_code}
          AND ROWNUM = 1
          ORDER BY TCBP.PRINT_TIME DESC    
	    ]]>
	</select>
	
	<!-- 获取保单打印信息 -->
	<select id="qry_queryPrintInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[			
		SELECT * FROM 
		(SELECT    'NB' as SYS,
		           TPP.PRINT_ID,
		           TPP.POLICY_CODE,
		           TPP.REISSUE_FLAG,
		           TPP.APPLY_CODE,
		           TPP.POLICY_CHG_ID,
		           TPP.PRINT_TYPE,
		           TPP.PRINT_STATUS,
		           TPP.PRINT_TIME,
		           TPP.PRINT_TIMES,
		           TPP.BPO_PRINT_DATE,
		           TPP.CONTENT_CLOB_ID,
		           TPP.ERROR_CODE,
		           TPP.ERROR_CONTENT,
		           TPP.BPO_PRINT_COM,
		           TPP.PRINT_ORG,
		           TPP.PLATFORM_FLAG,
		           TPP.INSERT_TIME,
		           TPP.INSERT_BY,
		           TPP.INSERT_TIMESTAMP,
		           TPP.UPDATE_BY,
		           TPP.UPDATE_TIME,
		           TPP.UPDATE_TIMESTAMP
		      FROM DEV_NB.T_POLICY_PRINT TPP  
		      WHERE TPP.POLICY_CODE =#{policy_code}/*保单号*/  
		           AND TPP.PRINT_STATUS ='3'
		           AND TPP.PRINT_TYPE != '2'
		 union	           
		SELECT     'PA' as SYS,
		           TPP.PRINT_ID,
		           TPP.POLICY_CODE,
		           TPP.REISSUE_FLAG,
		           TPP.APPLY_CODE,
		           TPP.POLICY_CHG_ID,
		           TPP.PRINT_TYPE,
		           TPP.PRINT_STATUS,
		           TPP.PRINT_TIME,
		           TPP.PRINT_TIMES,
		           TPP.BPO_PRINT_DATE,
		           TPP.CONTENT_CLOB_ID,
		           TPP.ERROR_CODE,
		           TPP.ERROR_CONTENT,
		           TPP.BPO_PRINT_COM,
		           TPP.PRINT_ORG,
		           TPP.PLATFORM_FLAG,
		           TPP.INSERT_TIME,
		           TPP.INSERT_BY,
		           TPP.INSERT_TIMESTAMP,
		           TPP.UPDATE_BY,
		           TPP.UPDATE_TIME,
		           TPP.UPDATE_TIMESTAMP
		      FROM DEV_PAS.T_POLICY_PRINT TPP 
		      WHERE TPP.POLICY_CODE = #{policy_code}/*保单号*/ 
		           AND TPP.PRINT_STATUS ='3'
		           AND TPP.PRINT_TYPE != '2') T
		  ORDER BY T.PRINT_TIME
		]]>
	</select>
	
	<select id="qry_queryPaPrintInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[		
		SELECT TPP.PRINT_ID,
		       TPP.POLICY_CODE,
		       TPP.REISSUE_FLAG,
		       TPP.APPLY_CODE,
		       TPP.POLICY_CHG_ID,
		       TPP.PRINT_TYPE,
		       TPP.PRINT_STATUS,
		       TPP.PRINT_TIME,
		       TPP.PRINT_TIMES,
		       TPP.BPO_PRINT_DATE,
		       TPP.CONTENT_CLOB_ID,
		       TPP.ERROR_CODE,
		       TPP.ERROR_CONTENT,
		       TPP.BPO_PRINT_COM,
		       TPP.PRINT_ORG,
		       TPP.PLATFORM_FLAG,
		       TPP.INSERT_TIME,
		       TPP.INSERT_BY,
		       TPP.INSERT_TIMESTAMP,
		       TPP.UPDATE_BY,
		       TPP.UPDATE_TIME,
		       TPP.UPDATE_TIMESTAMP
		  FROM DEV_PAS.T_POLICY_PRINT TPP	
		  WHERE TPP.POLICY_CODE = #{policy_code}/*保单号*/ 
		       AND TPP.PRINT_STATUS ='3'
		       ORDER BY TPP.PRINT_TIME DESC 
		]]>
	</select>
	
	<!-- 查询客户作为投保人的有效保单信息(掌上新华)-> 客户可申请纸质保单查询 -->
	<select id="qry_queryZSXHPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT PH.POLICY_CODE,/*保单号*/
			   TCM.CHANNEL_TYPE/*销售渠道*/ 
		  FROM DEV_PAS.T_POLICY_HOLDER PH
		  	   LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM ON PH.POLICY_CODE = TCM.POLICY_CODE
		  WHERE ((TCM.SUBMIT_CHANNEL = '1') /*银保通电子渠道*/
	       OR (TCM.CHANNEL_TYPE = '14')) /*互联网销售渠道*/
		   AND TCM.LIABILITY_STATE = '1'/*保单状态为‘有效’*/
		     AND PH.CUSTOMER_ID = 
		     (SELECT C.CUSTOMER_ID FROM DEV_PAS.T_CUSTOMER C 
		     		 WHERE C.CUSTOMER_ID = #{old_customer_id}
		     		)/*老核心客户号获取新核心客户号*/
		]]>
	</select>
</mapper>
