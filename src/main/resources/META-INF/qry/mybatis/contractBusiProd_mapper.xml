<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.impl.ContractBusiProdDaoImpl"> 
  
	<sql id="PA_contractBusiProdWhereCondition"> 
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" gurnt_start_date  != null  and  gurnt_start_date  != ''  "><![CDATA[ AND A.GURNT_START_DATE = #{gurnt_start_date} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" gurnt_period  != null "><![CDATA[ AND A.GURNT_PERIOD = #{gurnt_period} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" hesitation2acc  != null "><![CDATA[ AND A.HESITATION2ACC = #{hesitation2acc} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" settle_method  != null "><![CDATA[ AND A.SETTLE_METHOD = #{settle_method} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" flight_no != null and flight_no != ''  "><![CDATA[ AND A.FLIGHT_NO = #{flight_no} ]]></if>
		<if test=" waiver  != null "><![CDATA[ AND A.WAIVER = #{waiver} ]]></if>
		<if test=" gurnt_rate  != null "><![CDATA[ AND A.GURNT_RATE = #{gurnt_rate} ]]></if>
		<if test=" master_busi_item_id  != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" renewal_state  != null "><![CDATA[ AND A.RENEWAL_STATE = #{renewal_state} ]]></if>
		<if test=" assurerenew_flag  != null "><![CDATA[ AND A.ASSURERENEW_FLAG = #{assurerenew_flag} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" renew  != null "><![CDATA[ AND A.RENEW = #{renew} ]]></if>
		<if test=" renew_times  != null "><![CDATA[ AND A.RENEW_TIMES = #{renew_times} ]]></if>
		<if test=" waiver_end  != null  and  waiver_end  != ''  "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" gurnt_perd_type != null and gurnt_perd_type != ''  "><![CDATA[ AND A.GURNT_PERD_TYPE = #{gurnt_perd_type} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" joint_life_flag  != null "><![CDATA[ AND A.JOINT_LIFE_FLAG = #{joint_life_flag} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" due_lapse_date  != null  and  due_lapse_date  != ''  "><![CDATA[ AND A.DUE_LAPSE_DATE = #{due_lapse_date} ]]></if>
		<if test=" waiver_start  != null  and  waiver_start  != ''  "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" prd_pkg_code != null and prd_pkg_code != ''  "><![CDATA[ AND A.PRD_PKG_CODE = #{prd_pkg_code} ]]></if>
		<if test=" gurnt_renew_end  != null  and  gurnt_renew_end  != ''  "><![CDATA[ AND A.GURNT_RENEW_END = #{gurnt_renew_end} ]]></if>
		<if test=" gurnt_renew_start  != null  and  gurnt_renew_start  != ''  "><![CDATA[ AND A.GURNT_RENEW_START = #{gurnt_renew_start} ]]></if>
		<if test="policy_id_list  != null and policy_id_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_ID in (]]>
			<foreach collection="policy_id_list" item="policy_id_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_id_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test="policy_code_list  != null and policy_code_list.size()!=0 ">
			<![CDATA[ AND A.POLICY_code in (]]>
			<foreach collection="policy_code_list" item="policy_code_item"
				index="index" open="" close="" separator=",">
				<![CDATA[ #{policy_code_item} ]]>
			</foreach>
			<![CDATA[) ]]>
		</if>
		<if test=" old_pol_no != null and old_pol_no != ''  "><![CDATA[ AND A.OLD_POL_NO = #{OLD_POL_NO} ]]></if>
		<if test=" polno != null and polno != ''  "><![CDATA[ 
			AND NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID)=#{polno} 
		]]></if>
	<!-- 退保标识， SURRENDER_FLAG NUMBER(1) surrender_flag-->    
	   <if test=" surrender_flag  != null  and  surrender_flag  != ''  "><![CDATA[ AND A.SURRENDER_FLAG = #{surrender_flag} ]]></if>
		
	   <if test=" is_rpu  != null "><![CDATA[ AND A.IS_RPU = #{is_rpu} ]]></if>
	   <if test=" hesitation_period_day  != null "><![CDATA[ AND A.HESITATION_PERIOD_DAY = #{hesitation_period_day} ]]></if>
	</sql>
	<sql id="CLM_contractBusiProdWhereCondition">
		<if test=" gurnt_start_date  != null  and  gurnt_start_date  != ''  "><![CDATA[ AND A.GURNT_START_DATE = #{gurnt_start_date} ]]></if>
		<if test=" busi_prd_id  != null "><![CDATA[ AND A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
		<if test=" gurnt_period  != null "><![CDATA[ AND A.GURNT_PERIOD = #{gurnt_period} ]]></if>
		<if test=" settle_method  != null "><![CDATA[ AND A.SETTLE_METHOD = #{settle_method} ]]></if>
		<if test=" gurnt_rate  != null "><![CDATA[ AND A.GURNT_RATE = #{gurnt_rate} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" hesitation2acc  != null "><![CDATA[ AND A.HESITATION2ACC = #{hesitation2acc} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" waiver  != null "><![CDATA[ AND A.WAIVER = #{waiver} ]]></if>
		<if test=" master_busi_item_id  != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" copy_date  != null  and  copy_date  != ''  "><![CDATA[ AND A.COPY_DATE = #{copy_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" renew  != null "><![CDATA[ AND A.RENEW = #{renew} ]]></if>
		<if test=" renew_times  != null "><![CDATA[ AND A.RENEW_TIMES = #{renew_times} ]]></if>
		<if test=" waiver_end  != null  and  waiver_end  != ''  "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" gurnt_perd_type != null and gurnt_perd_type != ''  "><![CDATA[ AND A.GURNT_PERD_TYPE = #{gurnt_perd_type} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" joint_life_flag  != null "><![CDATA[ AND A.JOINT_LIFE_FLAG = #{joint_life_flag} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" due_lapse_date  != null  and  due_lapse_date  != ''  "><![CDATA[ AND A.DUE_LAPSE_DATE = #{due_lapse_date} ]]></if>
		<if test=" waiver_start  != null  and  waiver_start  != ''  "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" prd_pkg_code != null and prd_pkg_code != ''  "><![CDATA[ AND A.PRD_PKG_CODE = #{prd_pkg_code} ]]></if>
		<if test=" gurnt_renew_end  != null  and  gurnt_renew_end  != ''  "><![CDATA[ AND A.GURNT_RENEW_END = #{gurnt_renew_end} ]]></if>
		<if test=" cur_flag  != null "><![CDATA[ AND A.CUR_FLAG = #{cur_flag} ]]></if>
		<if test=" gurnt_renew_start  != null  and  gurnt_renew_start  != ''  "><![CDATA[ AND A.GURNT_RENEW_START = #{gurnt_renew_start} ]]></if>
		<if test=" old_pol_no  != null  and  old_pol_no  != ''  "><![CDATA[ AND A.OLD_POL_NO = #{old_pol_no} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" next_flag  != null "><![CDATA[ AND A.NEXT_FLAG = #{next_flag} ]]></if>
	</sql>  

<!-- 按索引生成的查询条件   -->	
	<sql id="PA_queryContractBusiProdByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractBusiProdByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_queryContractBusiProdByBusiProdCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != '' "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="PA_queryContractBusiProdByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID= #{policy_id} ]]></if>
	</sql>
	<sql id="PA_queryContractBusiProdByProductCodeCondition">
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE in(#{busi_prod_code}) ]]></if>
	</sql>
	<sql id="PA_contractBusiProdByMasterBusiProdCondition">
		<if test = " master_busi_item_id  != null"><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id}]]></if>
	</sql>


<!-- 添加操作 -->
	<insert id="PA_addContractBusiProd"  useGeneratedKeys="false"  parameterType="java.util.Map">
	    <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="busi_item_id">
			SELECT DEV_PAS.S_CONTRACT_BUSI_PROD__BUSI_ITE.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CONTRACT_BUSI_PROD(
				IS_RPU,GURNT_START_DATE, BUSI_PRD_ID, GURNT_PERIOD, BUSI_PROD_CODE, HESITATION2ACC, APPLY_CODE, IS_WAIVED, 
				SETTLE_METHOD, UPDATE_BY, POLICY_ID, FLIGHT_NO, WAIVER, GURNT_RATE, MASTER_BUSI_ITEM_ID, 
				UPDATE_TIME, PAIDUP_DATE, EXPIRY_DATE, LIABILITY_STATE, POLICY_CODE, RERINSTATE_DATE, RENEW_DECISION, 
				VALIDATE_DATE, UPDATE_TIMESTAMP, RENEWAL_STATE, ASSURERENEW_FLAG, INSERT_BY, APL_PERMIT, APPLY_DATE, 
				INITIAL_VALIDATE_DATE, RENEW, INSERT_TIMESTAMP, RENEW_TIMES, WAIVER_END, MATURITY_DATE, GURNT_PERD_TYPE, 
				BUSI_ITEM_ID, LAPSE_DATE, JOINT_LIFE_FLAG, INSERT_TIME, END_CAUSE, ISSUE_DATE, LAPSE_CAUSE, 
				DECISION_CODE, SUSPEND_DATE, SUSPEND_CAUSE, INITIAL_PREM_DATE, DUE_LAPSE_DATE, WAIVER_START, PRD_PKG_CODE, 
				GURNT_RENEW_END, GURNT_RENEW_START ,OLD_POL_NO,SURRENDER_FLAG,hesitation_period_day) 
			VALUES (
				 #{is_rpu, jdbcType=NUMERIC} ,#{gurnt_start_date, jdbcType=DATE}, #{busi_prd_id, jdbcType=NUMERIC} , #{gurnt_period, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{hesitation2acc, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{is_waived, jdbcType=NUMERIC} 
				, #{settle_method, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{flight_no, jdbcType=VARCHAR} , #{waiver, jdbcType=NUMERIC} , #{gurnt_rate, jdbcType=NUMERIC} , #{master_busi_item_id, jdbcType=NUMERIC} 
				, SYSDATE , #{paidup_date, jdbcType=DATE} , #{expiry_date, jdbcType=DATE} , #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{rerinstate_date, jdbcType=DATE} , #{renew_decision, jdbcType=NUMERIC} 
				, #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				]]>	
			 <if test=" renewal_state != null"><![CDATA[,#{renewal_state, jdbcType=NUMERIC}]]></if>
             <if test=" renewal_state == null"><![CDATA[, default]]></if>
			<![CDATA[
				, #{assurerenew_flag, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{apl_permit, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} 
				, #{initial_validate_date, jdbcType=DATE} , #{renew, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{renew_times, jdbcType=NUMERIC} , #{waiver_end, jdbcType=DATE} , #{maturity_date, jdbcType=DATE} , #{gurnt_perd_type, jdbcType=VARCHAR} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{lapse_date, jdbcType=DATE} , #{joint_life_flag, jdbcType=NUMERIC} , SYSDATE , #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , #{lapse_cause, jdbcType=VARCHAR} 
				, #{decision_code, jdbcType=VARCHAR} , #{suspend_date, jdbcType=DATE} , #{suspend_cause, jdbcType=VARCHAR} , #{initial_prem_date, jdbcType=DATE} , #{due_lapse_date, jdbcType=DATE} , #{waiver_start, jdbcType=DATE} , #{prd_pkg_code, jdbcType=VARCHAR} 
				, #{gurnt_renew_end, jdbcType=DATE} , #{gurnt_renew_start, jdbcType=DATE} ,#{old_pol_no, jdbcType=VARCHAR},#{surrender_flag, jdbcType=NUMERIC},#{hesitation_period_day, jdbcType=NUMERIC}) 
			]]>	
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteContractBusiProd" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_PAS.T_CONTRACT_BUSI_PROD WHERE BUSI_ITEM_ID =#{busi_item_id } ]]>
	</delete>
	

<!-- 修改操作 -->
	<update id="PA_updateContractBusiProd" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CONTRACT_BUSI_PROD ]]>
		<set>
		<trim suffixOverrides=",">
			IS_RPU = #{is_rpu, jdbcType=NUMERIC} ,
		    GURNT_START_DATE = #{gurnt_start_date, jdbcType=DATE} ,
		    BUSI_PRD_ID = #{busi_prd_id, jdbcType=NUMERIC} ,
		    GURNT_PERIOD = #{gurnt_period, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    HESITATION2ACC = #{hesitation2acc, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    IS_WAIVED = #{is_waived, jdbcType=NUMERIC} ,
		    SETTLE_METHOD = #{settle_method, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			FLIGHT_NO = #{flight_no, jdbcType=VARCHAR} ,
		    WAIVER = #{waiver, jdbcType=NUMERIC} ,
		    GURNT_RATE = #{gurnt_rate, jdbcType=NUMERIC} ,
		    MASTER_BUSI_ITEM_ID = #{master_busi_item_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PAIDUP_DATE = #{paidup_date, jdbcType=DATE} ,
		    EXPIRY_DATE = #{expiry_date, jdbcType=DATE} ,
		    LIABILITY_STATE = #{liability_state, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    RERINSTATE_DATE = #{rerinstate_date, jdbcType=DATE} ,
		    RENEW_DECISION = #{renew_decision, jdbcType=NUMERIC} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    RENEWAL_STATE = #{renewal_state, jdbcType=NUMERIC} ,
		    ASSURERENEW_FLAG = #{assurerenew_flag, jdbcType=NUMERIC} ,
		    APL_PERMIT = #{apl_permit, jdbcType=NUMERIC} ,
		    APPLY_DATE = #{apply_date, jdbcType=DATE} ,
		    INITIAL_VALIDATE_DATE =  #{initial_validate_date, jdbcType=DATE} ,
		    RENEW = #{renew, jdbcType=NUMERIC} ,
		    RENEW_TIMES = #{renew_times, jdbcType=NUMERIC} ,
		    WAIVER_END = #{waiver_end, jdbcType=DATE} ,
		    MATURITY_DATE = #{maturity_date, jdbcType=DATE} ,
			GURNT_PERD_TYPE = #{gurnt_perd_type, jdbcType=VARCHAR} ,
		    LAPSE_DATE = #{lapse_date, jdbcType=DATE} ,
		    JOINT_LIFE_FLAG = #{joint_life_flag, jdbcType=NUMERIC} ,
			END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
		    ISSUE_DATE = #{issue_date, jdbcType=DATE} ,
			LAPSE_CAUSE = #{lapse_cause, jdbcType=VARCHAR} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
		    SUSPEND_DATE = #{suspend_date, jdbcType=DATE} ,
			SUSPEND_CAUSE = #{suspend_cause, jdbcType=VARCHAR} ,
		    INITIAL_PREM_DATE = #{initial_prem_date, jdbcType=DATE} ,
		    DUE_LAPSE_DATE = #{due_lapse_date, jdbcType=DATE} ,
		    WAIVER_START = #{waiver_start, jdbcType=DATE} ,
			PRD_PKG_CODE = #{prd_pkg_code, jdbcType=VARCHAR} ,
		    GURNT_RENEW_END = #{gurnt_renew_end, jdbcType=DATE} ,
		    GURNT_RENEW_START = #{gurnt_renew_start, jdbcType=DATE} ,
		    OLD_POL_NO = #{old_pol_no, jdbcType=VARCHAR} ,
		    SURRENDER_FLAG = #{surrender_flag, jdbcType=NUMERIC} ,
		    HESITATION_PERIOD_DAY = #{hesitation_period_day, jdbcType=NUMERIC},
		   
		</trim>
		</set>
		<![CDATA[ WHERE BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ]]>
	</update>
	
	<update id="PA_updateContractBusiProdEndCause" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CONTRACT_BUSI_PROD ]]>
			<set>
				<trim suffixOverrides=",">
					END_CAUSE = #{end_cause, jdbcType=VARCHAR} ,
				</trim>
			</set>
		<![CDATA[ WHERE BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ]]>
	</update>
<!-- 按索引查询操作 -->	
	<select id="PA_findContractBusiProdByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
	<include refid="PA_queryContractBusiProdByBusiItemIdCondition" />
	</select>
	<!-- 投连价格查询接口 -->
	<select id="PA_findContractBusiProdByBusiItemId1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM = 1  and BUSI_ITEM_ID = #{busi_item_id} ]]>
		
	</select>
	
	<select id="PA_findContractBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START, A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractBusiProdByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findContractBusiProdByBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1    ]]>
		<include refid="PA_queryContractBusiProdByBusiProdCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
			
		<include refid="PA_contractBusiProdWhereCondition" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC,A.VALIDATE_DATE DESC]]>
		<!-- <![CDATA[ORDER BY A.master_busi_item_id DESC]]> -->
	</select>
<!-- 查询所有操作 -->
	<select id="CLM_findAllContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, 
			A.POLICY_ID, A.WAIVER, A.MASTER_BUSI_ITEM_ID, A.PAIDUP_DATE, A.CASE_ID, 
			A.EXPIRY_DATE, A.LIABILITY_STATE, A.COPY_DATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.APL_PERMIT, A.APPLY_DATE, A.RENEW, 
			A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, 
			A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, A.LOG_ID, A.SUSPEND_DATE, 
			A.SUSPEND_CAUSE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, A.GURNT_RENEW_END, A.CUR_FLAG, A.GURNT_RENEW_START, A.INITIAL_PREM_DATE, A.IS_WAIVED, A.GURNT_RATE,A.SETTLE_METHOD,A.OLD_POL_NO,A.INITIAL_VALIDATE_DATE,A.NEXT_FLAG FROM APP___CLM__DBUSER.T_CONTRACT_BUSI_PROD                       A WHERE ROWNUM <=  1000  ]]>
		<include refid="CLM_contractBusiProdWhereCondition" />
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
	
	<!-- 查询所有保单和契约操作 -->
	<select id="PA_findAllContractBusiProdPAandNB" resultType="java.util.Map" parameterType="java.util.Map">
	    <if test="  type != null and type == '1'.toString() ">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
			
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
				<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND (A.BUSI_ITEM_ID = #{busi_item_id} OR A.MASTER_BUSI_ITEM_ID = #{busi_item_id})]]></if>
		<![CDATA[ORDER BY A.master_busi_item_id DESC,A.VALIDATE_DATE DESC]]>
		</if>
		
		<if test="  type != null and type == '0'.toString() ">
		<![CDATA[ SELECT A.HESITATION_PERIOD_DAY,A.BUSI_PRD_ID,A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
      A.POLICY_ID,A.WAIVER,A.MASTER_BUSI_ITEM_ID, 
      A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE,
      A.VALIDATE_DATE,A.ASSURERENEW_FLAG, A.APL_PERMIT,
       A.RENEW,A.WAIVER_END,
      A.BUSI_ITEM_ID,A.JOINT_LIFE_FLAG,A.ISSUE_DATE, 
      A.DECISION_CODE,A.INITIAL_PREM_DATE,A.WAIVER_START, 
			A.OLD_POL_NO FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000]]>
			
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{policy_code} ]]></if>
		<![CDATA[ORDER BY A.master_busi_item_id DESC,A.VALIDATE_DATE DESC]]>
		</if>
		
	</select>
	
	<!-- 通过投保单号查询契约库 -->
	<select id="NB_findAllContractApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[ SELECT A.BUSI_ITEM_ID,
       A.BUSI_PRD_ID,
       A.PRODUCT_CODE,
       A.APPLY_CODE,
       A.POLICY_CODE,
       A.MASTER_BUSI_ITEM_ID,
       A.POLICY_ID,
       A.VALIDATE_DATE,
       A.EXPIRY_DATE,
       A.INITIAL_PREM_DATE,
       A.DECISION_CODE,
       A.LIABILITY_STATE,
       A.WAIVER,
       A.OLD_POL_NO
  FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD A
 WHERE ROWNUM <= 1000 
 AND A.APPLY_CODE = #{apply_code}]]>
 <if test=" polno != null and polno != ''  "><![CDATA[ 
			AND NVL(A.OLD_POL_NO,A.BUSI_ITEM_ID)=#{polno} 
		]]></if>
	</select>
<!-- 查询该险种的已交保费 -->
	<select id="PA_findtotalFeeAmountForContractBusi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT NVL(SUM(CP.TOTAL_PREM_AF),0) AS IS_WAIVED FROM DEV_PAS.T_CONTRACT_PRODUCT CP 
			WHERE CP.POLICY_ID = #{policy_id}
				AND CP.BUSI_ITEM_ID = #{busi_item_id}
			]]>
	</select>

<!-- 附加险满期降低保额续保条件:险种状态有效,险种状态终止 -->
<select id="PA_findAllContractBusiProd_cjk" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
SELECT A.RENEW,A.LIABILITY_STATE,A.SUSPEND_CAUSE, B.COVER_PERIOD_TYPE,A.RENEW_DECISION,A.OLD_POL_NO,A.BUSI_ITEM_ID,A.BUSI_PRD_ID,A.BUSI_PROD_CODE,A.Expiry_Date AS VALIDATE_DATE,A.RENEW_TIMES,
  C.COVERAGE_PERIOD,C.COVERAGE_YEAR,A.POLICY_ID 
  
  from DEV_PAS.T_conTRACT_BUSI_PROD A,dev_pas.T_BUSINESS_PRODUCT B,dev_pas.T_CONTRACT_PRODUCT C

  
 WHERE 1=1  and A.MASTER_BUSI_ITEM_ID IS NOT NULL AND  A.BUSI_PRD_ID=B.BUSINESS_PRD_ID  AND A.BUSI_ITEM_ID=C.BUSI_ITEM_ID
 AND A.LIABILITY_STATE in ('1','3')
  ]]>
<include refid="PA_contractBusiProdWhereCondition" />
</select>
<!-- 查询个数操作 -->
	<select id="PA_findContractBusiProdTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
		<include refid="PA_contractBusiProdWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractBusiProdForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.IS_RPU,B.HESITATION_PERIOD_DAY, B.GURNT_START_DATE, B.BUSI_PRD_ID, B.GURNT_PERIOD, B.BUSI_PROD_CODE, B.HESITATION2ACC, B.APPLY_CODE, B.IS_WAIVED, 
			B.SETTLE_METHOD, B.POLICY_ID, B.FLIGHT_NO, B.WAIVER, B.GURNT_RATE, B.MASTER_BUSI_ITEM_ID, 
			B.PAIDUP_DATE, B.EXPIRY_DATE, B.LIABILITY_STATE, B.POLICY_CODE, B.RERINSTATE_DATE, B.RENEW_DECISION, 
			B.VALIDATE_DATE, B.RENEWAL_STATE, B.ASSURERENEW_FLAG, B.APL_PERMIT, B.APPLY_DATE, 
			B.INITIAL_VALIDATE_DATE, B.RENEW, B.RENEW_TIMES, B.WAIVER_END, B.MATURITY_DATE, B.GURNT_PERD_TYPE, 
			B.BUSI_ITEM_ID, B.LAPSE_DATE, B.JOINT_LIFE_FLAG, B.END_CAUSE, B.ISSUE_DATE, B.LAPSE_CAUSE, 
			B.DECISION_CODE, B.SUSPEND_DATE, B.SUSPEND_CAUSE, B.INITIAL_PREM_DATE, B.DUE_LAPSE_DATE, B.WAIVER_START, B.PRD_PKG_CODE, 
			B.GURNT_RENEW_END, B.GURNT_RENEW_START FROM (
					SELECT ROWNUM RN,A.IS_RPU, A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <= #{LESS_NUM}  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

<!--  -->
	<select id="PA_findMainBusiProdByPolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
			<![CDATA[AND A.MASTER_BUSI_ITEM_ID IS NULL]]>
		<include refid="PA_queryContractBusiProdByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.MATURITY_DATE DESC]]>
	</select>
	
	<select id="PAandNB_findMainBusiProdByPolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<if test="  type != null and type == '1'.toString() "> 
	<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
			<![CDATA[AND A.MASTER_BUSI_ITEM_ID IS NULL]]>
		<include refid="PA_queryContractBusiProdByPolicyIdCondition" />
				<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID= #{busi_item_id} ]]></if>
		<![CDATA[ ORDER BY A.MATURITY_DATE DESC]]>
		</if>
		
		<if test="  type != null and type == '0'.toString() ">
		<![CDATA[ SELECT A.HESITATION_PERIOD_DAY, A.BUSI_PRD_ID,A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.POLICY_ID,A.WAIVER, A.MASTER_BUSI_ITEM_ID,A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE,
			A.VALIDATE_DATE,A.ASSURERENEW_FLAG, A.APL_PERMIT,A.RENEW,A.WAIVER_END,A.BUSI_ITEM_ID,A.JOINT_LIFE_FLAG,A.ISSUE_DATE, 
			A.DECISION_CODE,A.INITIAL_PREM_DATE,A.WAIVER_START,
			A.OLD_POL_NO FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD A WHERE 1 = 1]]>
			<![CDATA[AND A.MASTER_BUSI_ITEM_ID IS NULL AND A.POLICY_CODE IS NULL]]>
		<include refid="PA_queryContractBusiProdByPolicyIdCondition" />
				<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID= #{busi_item_id} ]]></if>
		</if>
	</select>
	
	<!--  -->
	<select id="PA_findAddBusiProdByPolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[  SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
			<![CDATA[AND A.MASTER_BUSI_ITEM_ID IS NOT NULL]]>
		<include refid="PA_queryContractBusiProdByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID ]]>
	</select>
	
	<select id="PA_findContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
		<include refid="PA_contractBusiProdWhereCondition" />
	</select>
    
        <!-- 短信调用-查询险种责任终止日期 -->
    <select id="PA_findAddBusiProdByPolicyIdforMessage"  resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  ]]>
        <include refid="PA_queryContractBusiProdByPolicyIdCondition" />
        <![CDATA[ ORDER BY A.BUSI_ITEM_ID ]]>
    </select>
	
	<!-- 通过主险id查询附加险 -->
	<select id="PA_findAllContractBusiProdBuMasterBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>			
		<include refid="PA_queryContractBusiProdByBusiItemIdCondition" />
		<include refid="PA_contractBusiProdByMasterBusiProdCondition" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC]]>
	</select>
	
	<!-- 通过主险id查询附加险 -->
	<select id="QRY_INTEGRAL_findAllContractBusiProdBuMasterBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>			
		<include refid="PA_queryContractBusiProdByBusiItemIdCondition" />
		<include refid="PA_contractBusiProdByMasterBusiProdCondition" />
		<![CDATA[ORDER BY A.master_busi_item_id DESC]]>
	</select>
		
	<!-- 长期险续保通过保单号码和产品code查询险种信息 -->
	<select id="PA_findContractBusiProdByPolicyIdAndPrdCod" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>
		<include refid="PA_queryContractBusiProdByPolicyIdCondition" />
		<include refid="PA_queryContractBusiProdByProductCodeCondition" />
	</select>
	
	<!-- 转保单专用 -->
	<insert id="PA_addContractBusiProdCreatePolicy"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="busi_item_id">
			SELECT DEV_PAS.S_CONTRACT_BUSI_PROD__BUSI_ITE.nextval from dual
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CONTRACT_BUSI_PROD(
				IS_RPU,GURNT_RENEW_START, GURNT_RENEW_END, GURNT_START_DATE, APL_PERMIT, GURNT_PERIOD, BUSI_PRD_ID, APPLY_DATE,  IS_WAIVED, BUSI_PROD_CODE, 
				APPLY_CODE, RENEW, HESITATION2ACC, INSERT_TIMESTAMP, RENEW_TIMES, WAIVER_END, UPDATE_BY, 
				GURNT_PERD_TYPE, BUSI_ITEM_ID, POLICY_ID, WAIVER, JOINT_LIFE_FLAG, INSERT_TIME, MASTER_BUSI_ITEM_ID, 
				END_CAUSE, ISSUE_DATE, UPDATE_TIME, LAPSE_CAUSE, PAIDUP_DATE, DECISION_CODE, EXPIRY_DATE, 
				LIABILITY_STATE, POLICY_CODE, RENEW_DECISION, VALIDATE_DATE, UPDATE_TIMESTAMP, INSERT_BY, WAIVER_START, 
				PRD_PKG_CODE , MATURITY_DATE ,GURNT_RATE,SETTLE_METHOD,INITIAL_VALIDATE_DATE,OLD_POL_NO, SURRENDER_FLAG,
				HESITATION_PERIOD_DAY, RENEWAL_STATE) 
			VALUES (
				 #{is_rpu, jdbcType=NUMERIC} ,#{gurnt_renew_start, jdbcType=DATE} , #{gurnt_renew_end, jdbcType=DATE}, #{gurnt_start_date, jdbcType=DATE}, #{apl_permit, jdbcType=NUMERIC} , #{gurnt_period, jdbcType=NUMERIC} , #{busi_prd_id, jdbcType=NUMERIC} , #{apply_date, jdbcType=DATE} , #{is_waived, jdbcType=NUMERIC},  #{busi_prod_code, jdbcType=VARCHAR} 
				, #{apply_code, jdbcType=VARCHAR} ,  #{renew, jdbcType=NUMERIC} , #{hesitation2acc, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{renew_times, jdbcType=NUMERIC} , #{waiver_end, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} 
				, #{gurnt_perd_type, jdbcType=VARCHAR} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{waiver, jdbcType=NUMERIC} , #{joint_life_flag, jdbcType=NUMERIC} , SYSDATE , #{master_busi_item_id, jdbcType=NUMERIC} 
				, #{end_cause, jdbcType=VARCHAR} , #{issue_date, jdbcType=DATE} , SYSDATE , #{lapse_cause, jdbcType=VARCHAR} , #{paidup_date, jdbcType=DATE} , #{decision_code, jdbcType=VARCHAR} , #{expiry_date, jdbcType=DATE} 
				, #{liability_state, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{renew_decision, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{waiver_start, jdbcType=DATE} 
				, #{prd_pkg_code, jdbcType=VARCHAR}, #{maturity_date, jdbcType=DATE},#{gurnt_rate, jdbcType=NUMERIC} ,#{settle_method, jdbcType=NUMERIC} 
				, #{initial_validate_date, jdbcType=DATE} ,#{old_pol_no, jdbcType=VARCHAR},#{surrender_flag, jdbcType=NUMERIC},#{hesitation_period_day, jdbcType=NUMERIC},
		]]>				
                <if test=" renewal_state != null and renewal_state != ''  "><![CDATA[#{renewal_state, jdbcType=NUMERIC}]]></if>
                <if test=" renewal_state = null or renewal_state = ''  "><![CDATA[ default]]></if>
         <![CDATA[ ) 
		 ]]>
	</insert>
	
	<select id="PA_calPrem"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT SUM(A.TOTAL_PREM_AF) PREM FROM DEV_PAS.T_CONTRACT_PRODUCT A  WHERE 1 = 1  ]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</select>
	
	<select id="PA_calAmount"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT SUM(A.AMOUNT) AMOUNT FROM DEV_PAS.T_CONTRACT_PRODUCT A  WHERE 1 = 1  ]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</select>
	
	<!-- by zhaoyoan_wb 通过保单号和险种代码查询主险险种 -->
	<select id="PA_queryMasterByPolicyCodeAndBusiProdCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from DEV_PAS.T_CONTRACT_BUSI_PROD 
			where MASTER_BUSI_ITEM_ID is null 
			and POLICY_CODE=#{policy_code} 
			and BUSI_PROD_CODE in (${busi_prod_code}) 
		]]>
	</select>
	
	<select id="queryMasterByPolicyCode1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 
		]]>
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	<!-- 被保险人职业类别查询接口 -->
	<select id="queryBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select b.job_code,
		       (select job_nature from DEV_PAS.T_JOB_NATURE where job_nature_code = b.job_code ) as occupation_code_name,
		       (select job_kind from DEV_PAS.T_customer where customer_id =b.customer_id) as  job_kind,
		       (select JOB_CATEGORY_NAME from DEV_PAS.T_JOB_CATEGORY  where job_code=b.job_code) as occupation_type_name,
		       (select PRODUCT_CODE_STD  from DEV_PAS.T_BUSINESS_PRODUCT  where BUSINESS_PRD_ID = a.busi_prd_id) as product_code_std,
		       (select PRODUCT_NAME_SYS from DEV_PAS.T_BUSINESS_PRODUCT  where BUSINESS_PRD_ID=a.busi_prd_id) as busi_prod_name,
		       (select sum(c.AMOUNT)
		          from DEV_PAS.T_contract_product c
		         where c.busi_item_id = a.busi_item_id) as amnt ,
		       (select sum(c.TOTAL_PREM_AF)
		          from DEV_PAS.T_contract_product c
		         where c.busi_item_id = a.busi_item_id) as prem ,
		       (select  sum(c.EXTRA_PREM_AF)
		          from DEV_PAS.T_contract_product c
		         where c.busi_item_id = a.busi_item_id)  as occ_add_prem
		  from DEV_PAS.T_CONTRACT_BUSI_PROD a, DEV_PAS.T_INSURED_LIST b
		 where a.policy_id = b.policy_id
		   and a.policy_code = #{policy_code}
		   and b.customer_id = #{customer_id}
		]]>
	</select>
	<!-- 保单险种信息查询接口  -->
	<select id="queryPolicyCodeRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT (SELECT PRODUCT_ABBR_NAME
          			FROM DEV_PAS.T_BUSINESS_PRODUCT
         			WHERE BUSINESS_PRD_ID = A.BUSI_PRD_ID) AS PRODUCT_NAME,
         			
         			(SELECT PRODUCT_CODE_SYS
                	FROM DEV_PAS.T_BUSINESS_PRODUCT
             	 	WHERE BUSINESS_PRD_ID = A.BUSI_PRD_ID) AS PRODUCT_CODE,
         
       				(SELECT SUM(B.AMOUNT) FROM DEV_PAS.T_CONTRACT_PRODUCT B WHERE B.BUSI_ITEM_ID = 
					A.BUSI_ITEM_ID) AS AMOUNT,

       				(SELECT SUM(B.TOTAL_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT B WHERE 
					B.BUSI_ITEM_ID = A.BUSI_ITEM_ID) AS TOTAL_PREM_AF,
       				NVL(A.MATURITY_DATE,A.EXPIRY_DATE)AS MATURITY_DATE,
       				A.VALIDATE_DATE,
       				A.POLICY_ID,
       				A.BUSI_ITEM_ID
  			FROM DEV_PAS.T_CONTRACT_BUSI_PROD A
 			WHERE A.POLICY_CODE = #{policy_code}
		]]>
	</select>
	<!-- 保单号获取所有可转投险种信息 -->
	<select id="PA_queryRelaRisksByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
      select decode(du.PRODUCT_STATIC_CODE,'726','727','727','726','507','508','508','507') as PRODUCT_STATIC_CODE,
       (case du.PRODUCT_STATIC_CODE 
          when '726' then (select t.PRODUCT_ABBR_NAME from DEV_PAS.T_BUSINESS_PRODUCT t where t.PRODUCT_STATIC_CODE = '727') 
          when '727' then (select t.PRODUCT_ABBR_NAME from DEV_PAS.T_BUSINESS_PRODUCT t where t.PRODUCT_STATIC_CODE = '726') 
          when '507' then (select t.PRODUCT_ABBR_NAME from DEV_PAS.T_BUSINESS_PRODUCT t where t.PRODUCT_STATIC_CODE = '508') 
          else (select t.PRODUCT_ABBR_NAME from DEV_PAS.T_BUSINESS_PRODUCT t where t.PRODUCT_STATIC_CODE = '507')  end ) as PRODUCT_ABBR_NAME
        from DEV_PAS.T_CONTRACT_MASTER    ma,
             DEV_PAS.T_CONTRACT_BUSI_PROD pr,
             DEV_PAS.T_BUSINESS_PRODUCT   du
       where ma.policy_id = pr.policy_id
         and busi_prd_id = du.BUSINESS_PRD_ID
         and du.PRODUCT_STATIC_CODE in ('726', '727', '507', '508')
         and pr.policy_code = #{policy_code}
	]]>
	</select>
	<!-- 保单号、险种号码<list>获取附加险信息 -->
	<select id="PA_queryNotmasterBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
      select * from DEV_PAS.T_CONTRACT_BUSI_PROD t where t.policy_code = #{policy_code} and t.master_busi_item_id is not null and t.busi_prod_code in 
	]]>
	<foreach collection="list" item="list"
			index="index" open="(" close=")" separator=",">#{list}</foreach>
	</select>
	
		<!--yuzw-->
	<select id="QRY_INTEGRAL_findAddBusiProdByPolicyId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A 
			WHERE 1 = 1 AND A.MASTER_BUSI_ITEM_ID IS NULL ]]>
		<include refid="PA_queryContractBusiProdByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID ]]>
	</select>
	
	<!-- 保单险种信息查询  -->
	<select id="queryPolicyCodeRiskName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT  A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.APL_PERMIT, A.GURNT_PERIOD, A.BUSI_PRD_ID, A.APPLY_DATE,  A.BUSI_PROD_CODE, 
			A.IS_WAIVED, A.APPLY_CODE, A.RENEW, A.HESITATION2ACC, A.RENEW_TIMES, A.WAIVER_END, 
			A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.BUSI_ITEM_ID, A.POLICY_ID, A.LAPSE_DATE, A.WAIVER, 
			A.JOINT_LIFE_FLAG, A.MASTER_BUSI_ITEM_ID, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.PAIDUP_DATE, A.DECISION_CODE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.RERINSTATE_DATE, 
			A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.RENEW_DECISION, A.DUE_LAPSE_DATE, A.VALIDATE_DATE, A.RENEWAL_STATE, 
			A.WAIVER_START, A.GURNT_RENEW_END, A.PRD_PKG_CODE, A.GURNT_RENEW_START, TP.PRODUCT_NAME_STD AS RISK_NAME,TH.CUSTOMER_ID,TF.TYPE_NAME,A.GURNT_RATE,A.SETTLE_METHOD,A.OLD_POL_NO
            FROM DEV_PAS.T_CONTRACT_BUSI_PROD A, DEV_PAS.T_BUSINESS_PRODUCT TP, DEV_PAS.T_POLICY_HOLDER TH 
            LEFT JOIN DEV_PAS.T_FEE_TYPE TF
            ON TF.CODE=#{fee_type,jdbcType=VARCHAR}
            WHERE  A.BUSI_PRD_ID = TP.BUSINESS_PRD_ID AND A.POLICY_CODE=TH.POLICY_CODE 
            
		]]>
		<include refid="PA_contractBusiProdWhereCondition" />
	</select>
	<!-- 当日年化受益率当前日期价格-->
	<select id="queryAnnualYieldInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT TIUP.BID_PRICE, TIUP.PRICING_DATE
			         FROM DEV_PAS.T_INVEST_UNIT_PRICE TIUP,
			              DEV_PAS.T_CONTRACT_INVEST   TCI
			        WHERE TCI.ACCOUNT_CODE = TIUP.INVEST_ACCOUNT_CODE            
 			         AND TCI.ACCOUNT_CODE = #{account_type}
					AND TIUP.PRICING_DATE < #{pricing_date}
			          AND TCI.BUSI_ITEM_ID IN
			              (SELECT BUSI_ITEM_ID
 			                FROM DEV_PAS.T_CONTRACT_BUSI_PROD
 			               WHERE BUSI_PROD_CODE = #{busi_prod_code})
			        ORDER BY TIUP.PRICING_DATE DESC						
		 ]]>
	</select>
	<!-- 当日年化受益率前一计价日价格 -->
	<select id="queryAnnualYieldInfoPrior" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from (select tiup.BID_PRICE, tiup.PRICING_DATE
			  from DEV_PAS.T_CONTRACT_BUSI_PROD tcbp
			 inner join DEV_PAS.T_CONTRACT_INVEST tci
			    on tcbp.BUSI_ITEM_ID = tci.BUSI_ITEM_ID
			 inner join DEV_PAS.T_INVEST_UNIT_PRICE tiup
			    on tci.ACCOUNT_CODE = tiup.INVEST_ACCOUNT_CODE
			 where tcbp.BUSI_PROD_CODE = #{busi_prod_code} 
			 and to_char(tiup.PRICING_DATE, 'yyyy-mm-dd') < #{pricing_date} 
			 order by tiup.PRICING_DATE desc) where rownum = 1	]]>
	</select>
	<!-- i添财投资当前日期价格 -->
	<select id="queryRenturnRate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select res.*,
			(select nvl(sum(cal_bid_price),0) from DEV_PAS.T_INVEST_UNIT_PRICE 
			  where INVEST_ACCOUNT_CODE=res.INVEST_ACCOUNT_CODE and PRICING_DATE<#{pricing_date}) sum_bnf 
			from (
			  select distinct tiup.INVEST_ACCOUNT_CODE,tiup.cal_off_price, tiup.cal_bid_price, tiup.PRICING_DATE,
			  	tiup.ANNUALIZED_RETURN,tci.accum_units,tci.policy_id,tci.busi_item_id
			  from DEV_PAS.T_CONTRACT_BUSI_PROD tcbp
			  inner join DEV_PAS.T_CONTRACT_INVEST tci on tcbp.BUSI_ITEM_ID = tci.BUSI_ITEM_ID
			  inner join DEV_PAS.T_INVEST_UNIT_PRICE tiup on tci.ACCOUNT_CODE = tiup.INVEST_ACCOUNT_CODE
			  where tcbp.POLICY_CODE = #{policy_code} and tiup.PRICING_DATE<#{pricing_date}
			  order by tiup.PRICING_DATE desc) res
			where rownum <= 2
		]]>
	</select>
	<!-- i添财投资前一计价日价格 -->
	<select id="queryRenturnRatePrior" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select * from (select tiup.BID_PRICE, tiup.PRICING_DATE,tci.ACCUM_UNITS
			  from DEV_PAS.T_CONTRACT_BUSI_PROD tcbp
			 inner join DEV_PAS.T_CONTRACT_INVEST tci
			    on tcbp.BUSI_ITEM_ID = tci.BUSI_ITEM_ID
			 inner join DEV_PAS.T_INVEST_UNIT_PRICE tiup
			    on tci.ACCOUNT_CODE = tiup.INVEST_ACCOUNT_CODE
			 where tcbp.POLICY_CODE = #{policy_code} 
			 and tiup.PRICING_DATE < sysdate 
			 order by tiup.PRICING_DATE desc) where rownum = 1	]]>
	</select>
	<!--by zhaoyoan_wb 通过保单号查询保单下各险种满期给付通知书信息 -->
	<select id="queryWebLPEdorGetNoticeBusinessInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.BUSI_PROD_CODE,
			(SELECT PRODUCT_ABBR_NAME FROM DEV_PAS.T_BUSINESS_PRODUCT
				WHERE BUSINESS_PRD_ID = A.BUSI_PRD_ID) AS product_abbr_name,
			E.CUSTOMER_NAME,E.CUSTOMER_GENDER,B.LIAB_CODE,B.PAY_DUE_DATE,
			(SELECT FEE_AMOUNT FROM DEV_PAS.T_PAY_DUE WHERE B.PLAN_ID=PLAN_ID AND PAY_DUE_DATE=
				(SELECT MAX(PAY_DUE_DATE) FROM DEV_PAS.T_PAY_DUE WHERE B.PLAN_ID=PLAN_ID) AND ROWNUM=1) AS get_standard,
			B.TOTAL_AMOUNT AS get_money,
			(SELECT POST_CODE FROM DEV_PAS.T_ADDRESS WHERE ADDRESS_ID=D.ADDRESS_ID) AS POST_CODE,
			(SELECT ADDRESS FROM DEV_PAS.T_ADDRESS WHERE ADDRESS_ID=D.ADDRESS_ID) AS ADDRESS,
			(SELECT SUM(NVL(AMOUNT,0)) FROM DEV_PAS.T_CONTRACT_PRODUCT
				WHERE A.POLICY_CODE = POLICY_CODE AND A.BUSI_ITEM_ID = BUSI_ITEM_ID) AS amount
			FROM DEV_PAS.T_PAY_PLAN B,DEV_PAS.T_CONTRACT_BUSI_PROD A,
				DEV_PAS.T_BENEFIT_INSURED C,DEV_PAS.T_INSURED_LIST D,DEV_PAS.T_CUSTOMER E
			WHERE A.POLICY_CODE = #{policy_code} AND B.LIAB_ID='1201'
			AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID AND C.BUSI_ITEM_ID=B.BUSI_ITEM_ID 
			AND D.LIST_ID=C.INSURED_ID AND E.CUSTOMER_ID=D.CUSTOMER_ID
			AND EXISTS(SELECT 1 FROM DEV_PAS.V_DOCUMENT_ALL C WHERE A.POLICY_ID=C.POLICY_ID 
				AND (C.TEMPLATE_CODE='PAS_00005' OR C.TEMPLATE_CODE='PAS_00004'))
		]]>
		<!--  AND C.STATUS='2'	老核心条件的是状态为0的，对应新核心是2 -->
	</select>
	<!--by zhaoyoan_wb 通过保单号查询保单下各险种信息及被保人客户号 -->
	<select id="queryRiskInfoAndInsuredNoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,A.POLICY_ID,A.BUSI_ITEM_ID,A.BUSI_PROD_CODE,A.BUSI_PRD_ID,A.OLD_POL_NO,
			(SELECT PRODUCT_NAME_SYS FROM DEV_PAS.T_BUSINESS_PRODUCT 
				WHERE BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS product_name_sys,
			(SELECT PRODUCT_ABBR_NAME FROM DEV_PAS.T_BUSINESS_PRODUCT 
				WHERE BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS product_abbr_name,
			(SELECT CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST WHERE LIST_ID=
            	(SELECT INSURED_ID FROM DEV_PAS.T_BENEFIT_INSURED WHERE 
            		POLICY_CODE=A.POLICY_CODE AND BUSI_ITEM_ID=A.BUSI_ITEM_ID AND ORDER_ID=1)) AS insured_id
			FROM DEV_PAS.T_CONTRACT_BUSI_PROD A 
			WHERE 1=1 AND A.POLICY_CODE=#{policy_code} 
		]]>
	</select>
	<!-- 查询是否是精选产品 -->				
	
	<select id="findConBusiProdIsJXOrNot" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.APL_PERMIT, A.GURNT_PERIOD, A.BUSI_PRD_ID, A.APPLY_DATE,  A.BUSI_PROD_CODE, 
			A.IS_WAIVED, A.APPLY_CODE, A.RENEW, A.HESITATION2ACC, A.RENEW_TIMES, A.WAIVER_END, 
			A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.BUSI_ITEM_ID, A.POLICY_ID, A.LAPSE_DATE, A.WAIVER, 
			A.JOINT_LIFE_FLAG, A.MASTER_BUSI_ITEM_ID, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.PAIDUP_DATE, A.DECISION_CODE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.RERINSTATE_DATE, 
			A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.RENEW_DECISION, A.DUE_LAPSE_DATE, A.VALIDATE_DATE, A.RENEWAL_STATE, 
			A.WAIVER_START, A.GURNT_RENEW_END, A.PRD_PKG_CODE, A.GURNT_RENEW_START,A.GURNT_RATE,A.SETTLE_METHOD,A.OLD_POL_NO,A.SURRENDER_FLAG FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE (A.BUSI_PROD_CODE like '%9121%' or A.BUSI_PROD_CODE like '%913%') and A.BUSI_ITEM_ID = #{busi_item_id} ]]>
		
	</select>
	
	<!-- 根据险种code查询险种id -->	
	<select id="queryContractBusiPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select t.busi_prd_id from DEV_PAS.t_contract_busi_prod t where t.busi_prod_code = #{busi_prod_code} and rownum=1
		]]>
	</select>
	
	<!-- 查询被保险人所属的险种：根据被保险人ID查询 -->	
	<select id="PA_findAllContractBusiProdByInsuredId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select tc.*
			  from DEV_PAS.t_benefit_insured t
			  left join DEV_PAS.t_contract_busi_prod tc
			    on t.busi_item_id = tc.busi_item_id
			 where t.insured_id = #{insured_id} 
			 order by  tc.master_busi_item_id desc
		]]>
	</select>
	<!-- 查询保单号下是否有附加险万能型 -->	
	<select id="PA_queryContractYN" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			select 1 from DEV_PAS.T_CONTRACT_BUSI_PROD tcbp,DEV_PAS.T_CONTRACT_INVEST tci where tcbp.busi_item_id=tci.busi_item_id and 
			tcbp.MASTER_BUSI_ITEM_ID is not null and tci.INVEST_ACCOUNT_TYPE='2' and tcbp.policy_code=#{policy_code}	
		]]>
	</select>
	
	<!-- 移动保全保单复效查询 -->	
	<select id="queryContReinstateInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.BUSI_PROD_CODE,A.LIABILITY_STATE,A.LAPSE_DATE,
			(SELECT PRODUCT_NAME_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT WHERE BUSINESS_PRD_ID=A.BUSI_PRD_ID) AS product_name_sys,
			(SELECT SUM(NVL(STD_PREM_AF,0)) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS std_prem_af,
			(SELECT SUM(NVL(AMOUNT,0)) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS amount,
			(SELECT SUM(NVL(UNIT,0)) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS unit,
			(SELECT PAY_DUE_DATE FROM DEV_PAS.T_CONTRACT_EXTEND WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND ROWNUM=1) AS pay_due_date,
			(SELECT SUM(NVL(EXTRA_PREM,0)) FROM DEV_PAS.T_EXTRA_PREM WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND EXTRA_TYPE=1) AS extra_para1,
			(SELECT SUM(NVL(EXTRA_PREM,0)) FROM DEV_PAS.T_EXTRA_PREM WHERE A.BUSI_ITEM_ID=BUSI_ITEM_ID AND EXTRA_TYPE=2) AS extra_para2
			FROM DEV_PAS.T_CONTRACT_BUSI_PROD A
			WHERE 1=1 
			AND A.POLICY_CODE=#{policy_code} 
		]]>
	</select>
	
	<!-- 续保时特殊附加险校验 -->
	<select id="PA_querySpeRuleCount1" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_BUSI_PROD T 
			WHERE  1 = 1 AND (T.BUSI_PROD_CODE LIKE '%717%' OR T.BUSI_PROD_CODE LIKE '%540%' OR T.BUSI_PROD_CODE LIKE '%726%')
			AND T.LIABILITY_STATE <> '1' AND T.POLICY_CODE = #{policy_code} AND T.MASTER_BUSI_ITEM_ID = #{master_busi_item_id}
		]]>
	</select>
	<select id="PA_querySpeRuleCount2" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_BUSI_PROD T 
			WHERE 1 = 1 AND (T.BUSI_PROD_CODE LIKE '%541%' OR T.BUSI_PROD_CODE LIKE '%717%' OR T.BUSI_PROD_CODE LIKE '%726%')
			AND T.LIABILITY_STATE = '1' AND T.POLICY_CODE = #{policy_code} AND T.MASTER_BUSI_ITEM_ID = #{master_busi_item_id}
		]]>
	</select>
	
	<!-- 根据保单号查询主险 -->
	<select id="PA_findMasterContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START ,A.OLD_POL_NO FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1  AND A.MASTER_BUSI_ITEM_ID is null]]>
		<if test="policy_id != null and policy_id != ''">
			<![CDATA[AND A.policy_id = #{policy_id} ]]>
		</if>
	</select>
	
	
	<!-- 根据保单号查询主险 -->
	<select id="findUniContractBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		 select a.*
                  from (select *
                          from dev_pas.t_contract_busi_prod t
                         where t.policy_code = #{policy_code}) a
                  left join dev_pas.t_business_product tbp
                    on a.busi_prd_id = tbp.business_prd_id
                 where tbp.product_category1 = '20003'
		]]>
	</select>
	
	<!--by zhaoyoan_wb 根据新(老)核心险种号查询保单险种信息 -->
	<select id="findContractBusiProdByPolNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.BUSI_ITEM_ID,A.BUSI_PRD_ID,A.BUSI_PROD_CODE,A.POLICY_ID,A.POLICY_CODE,A.HESITATION_PERIOD_DAY,
				A.OLD_POL_NO,A.LIABILITY_STATE,A.VALIDATE_DATE,A.MASTER_BUSI_ITEM_ID 
			FROM DEV_PAS.T_CONTRACT_BUSI_PROD A 
			WHERE 1=1
		]]>
		<include refid="PA_contractBusiProdWhereCondition" />
		<![CDATA[
			and nvl(A.OLD_POL_NO,A.BUSI_ITEM_ID) = #{polno}
		]]>
	</select>
	
	<!-- 附加险查询接口 -->
	<select id="findContractBusiProdsByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select 
			(select sum(p.std_prem_af) from dev_pas.t_contract_product p where p.busi_item_id = a.busi_item_id and p.policy_code = a.policy_code) as prem,
			(select sum(p.amount) from dev_pas.t_contract_product p where p.busi_item_id = a.busi_item_id and p.policy_code = a.policy_code) as amnt,
			a.validate_date,
			a.busi_prod_code,
			(select product_name_sys from dev_pds.t_business_product where product_code_sys = a.busi_prod_code) as riskname,
			(select ce.PAY_DUE_DATE from dev_pas.T_CONTRACT_EXTEND ce where ce.BUSI_ITEM_ID = a.BUSI_ITEM_ID and rownum=1) as paytodate,
			(select customer_id from dev_pas.t_insured_list where list_id = 
				(select insured_id from dev_pas.t_benefit_insured where a.busi_item_id = busi_item_id and order_id = 1)) insuredno
			from dev_pas.t_contract_busi_prod a
			where a.policy_code = #{policy_code}
		]]>
	</select>
	
	<!-- 附加险代码查询接口 -->
	<select id="findContractBusiProdCodeByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select  a.busi_prod_code,
       			    (select product_name_sys from dev_pds.t_business_product where product_code_sys = a.busi_prod_code) as riskname
  			from dev_pas.t_contract_busi_prod a
 			where a.policy_code = #{policy_code}
   			and a.master_busi_item_id is not null
		]]>
	</select>
	
	<!-- 查询险种名称报文对比 -->
	<select id="queryPolicyCodeRroductSysName" resultType="java.util.Map" parameterType="java.util.Map">
	
	<![CDATA[
			SELECT  A.IS_RPU,A.HESITATION_PERIOD_DAY,A.GURNT_START_DATE, A.APL_PERMIT, A.GURNT_PERIOD, A.BUSI_PRD_ID, A.APPLY_DATE,  A.BUSI_PROD_CODE, 
			A.IS_WAIVED, A.APPLY_CODE, A.RENEW, A.HESITATION2ACC, A.RENEW_TIMES, A.WAIVER_END, 
			A.MATURITY_DATE, A.GURNT_PERD_TYPE, A.BUSI_ITEM_ID, A.POLICY_ID, A.LAPSE_DATE, A.WAIVER, 
			A.JOINT_LIFE_FLAG, A.MASTER_BUSI_ITEM_ID, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.PAIDUP_DATE, A.DECISION_CODE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.SUSPEND_DATE, A.RERINSTATE_DATE, 
			A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.RENEW_DECISION, A.DUE_LAPSE_DATE, A.VALIDATE_DATE, A.RENEWAL_STATE, 
			A.WAIVER_START, A.GURNT_RENEW_END, A.PRD_PKG_CODE, A.GURNT_RENEW_START, TP.PRODUCT_NAME_SYS AS RISK_NAME,TH.CUSTOMER_ID,TF.TYPE_NAME,A.GURNT_RATE,A.SETTLE_METHOD,A.OLD_POL_NO
            FROM DEV_PAS.T_CONTRACT_BUSI_PROD A, DEV_PAS.T_BUSINESS_PRODUCT TP, DEV_PAS.T_POLICY_HOLDER TH 
            LEFT JOIN DEV_PAS.T_FEE_TYPE TF
            ON TF.CODE=#{fee_type,jdbcType=VARCHAR}
            WHERE  A.BUSI_PRD_ID = TP.BUSINESS_PRD_ID AND A.POLICY_CODE=TH.POLICY_CODE 
            
		]]>
		<include refid="PA_contractBusiProdWhereCondition" />
	
	</select>
	<!-- 根据险种号查询险种ID保单Id -->
	<select id="findContractBusiProdIdByPolNo" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[	SELECT A.BUSI_ITEM_ID,A.POLICY_ID
			FROM DEV_PAS.T_CONTRACT_BUSI_PROD A 
			WHERE 1=1 AND  A.POLICY_CODE=#{policy_code} and A.OLD_POL_NO=#{polno} ]]>
	
	</select>
	<!-- 根据险种号查询险种ID保单Id -->
	<select id="findMainBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	SELECT CM.POLICY_CODE, CM.VALIDATE_DATE, TBP.PRODUCT_NAME_SYS, CBP.BUSI_PROD_CODE
			  FROM DEV_PAS.T_CONTRACT_MASTER    CM,
			       DEV_PAS.T_CONTRACT_BUSI_PROD CBP,
			       DEV_PAS.T_BUSINESS_PRODUCT   TBP
			 WHERE CBP.MASTER_BUSI_ITEM_ID IS NULL
			   AND CM.POLICY_CODE = CBP.POLICY_CODE
			   AND CBP.BUSI_PROD_CODE = TBP.PRODUCT_CODE_SYS
			   AND CBP.POLICY_CODE = #{policy_code}]]>	   
	</select>
	<!-- 连带被保险人信息接口 -->
	<select id="PA_queryJointInsurerInf" resultType="java.util.Map" parameterType="java.util.Map">
	   <![CDATA[	
	       SELECT  T2.BUSI_ITEM_ID,T1.CUSTOMER_ID,
	       (SELECT CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST WHERE LIST_ID IN(SELECT INSURED_ID  FROM DEV_PAS.T_BENEFIT_INSURED WHERE ORDER_ID='1' 
	       	AND T2.BUSI_ITEM_ID = BUSI_ITEM_ID)AND POLICY_ID=T1.POLICY_ID AND ROWNUM=1)MCUSTOMER_ID,
	       T2.RELATION_TO_INSURED_1,T3.CUSTOMER_NAME,T3.CUSTOMER_GENDER,
	       T3.CUSTOMER_BIRTHDAY,T2.INSERT_TIME,T2.UPDATE_TIME,T3.CUSTOMER_CERT_TYPE,T3.CUSTOMER_CERTI_CODE,T1.JOB_UNDERWRITE
	       FROM DEV_PAS.T_INSURED_LIST T1 ,DEV_PAS.T_BENEFIT_INSURED T2 ,DEV_PAS.T_CUSTOMER T3
	       WHERE T1.POLICY_ID=T2.POLICY_ID AND T1.POLICY_CODE=T2.POLICY_CODE AND T1.LIST_ID=T2.INSURED_ID AND T1.CUSTOMER_ID=T3.CUSTOMER_ID 
	        ]]>
	       <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T2.POLICY_CODE=#{policy_code} ]]></if>
	       <if test=" polno != null and polno != ''  ">
			  <![CDATA[	AND T2.BUSI_ITEM_ID=nvl((select a.busi_item_id from dev_pas.t_contract_busi_prod a where a.old_pol_no=#{polno}),#{polno})  ]]>
			</if>
	</select>
	
	
	<!-- 连带被保险人信息接口：按投保单号查询 -->
	<select id="NB_queryJointInsurerInf" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[	
		    SELECT T2.BUSI_ITEM_ID,
	        T1.CUSTOMER_ID,
	        (SELECT CUSTOMER_ID
	           FROM DEV_NB.T_NB_INSURED_LIST
	          WHERE LIST_ID IN (SELECT INSURED_ID
	                              FROM DEV_NB.T_NB_BENEFIT_INSURED
	                             WHERE ORDER_ID = '1' AND T2.BUSI_ITEM_ID = BUSI_ITEM_ID)
	            AND APPLY_CODE = T1.APPLY_CODE
	            AND ROWNUM = 1) MCUSTOMER_ID,
	        T2.RELATION_TO_INSURED_1,
	        T3.CUSTOMER_NAME,
	        T3.CUSTOMER_GENDER,
	        T3.CUSTOMER_BIRTHDAY,
	        T2.INSERT_TIME,
	        T2.UPDATE_TIME,
	        T3.CUSTOMER_CERT_TYPE,
	        T3.CUSTOMER_CERTI_CODE,
	        T1.JOB_UNDERWRITE
	   FROM DEV_NB.T_NB_INSURED_LIST    T1,
	        DEV_NB.T_NB_BENEFIT_INSURED T2,
	        DEV_NB.T_CUSTOMER         T3
	  WHERE T1.POLICY_ID = T2.POLICY_ID
	    AND T1.APPLY_CODE = T2.APPLY_CODE
	    AND T1.LIST_ID = T2.INSURED_ID
	    AND T1.CUSTOMER_ID = T3.CUSTOMER_ID
	    ]]>
	   <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND T2.APPLY_CODE=#{policy_code} ]]></if>
       <if test=" polno != null and polno != ''  ">
			<![CDATA[ AND T2.BUSI_ITEM_ID=nvl((select a.busi_item_id from dev_pas.t_contract_busi_prod a where a.old_pol_no=#{polno}),#{polno}) ]]>
	   </if>
	</select>
	
	
	<!-- 保单险种表信息接口 使用 -->
	<select id="QRY_queryContBusiProdByinsuredNo" resultType="java.util.Map" parameterType="java.util.Map">
	
	<if test="step != null and  step == 'one' ">
		<![CDATA[
			select a.apply_code 
			  from dev_nb.t_nb_benefit_insured a,
			       dev_nb.t_nb_insured_list    b,
			       dev_nb.t_customer           c
			 where a.insured_id = b.list_id
			   and b.customer_id = c.customer_id
			   and c.customer_id = #{customer_id}
			union
			select  a.apply_code
			  from dev_pas.t_contract_master a,
			       dev_pas.t_benefit_insured b,
			       dev_pas.t_insured_list    c,
			       dev_pas.t_customer        d
			 where a.policy_id = b.policy_id
			   and b.insured_id = c.list_id
			   and c.customer_id = d.customer_id
			   and d.customer_id = #{customer_id}
			  
		]]>
	</if>
	<!-- 一下sql去掉   and e.is_master_item = 1  当时和产品功能组确认可以有非基本可选责任的保单 -->
	<if test="step != null and  step == 'two' ">
		<![CDATA[
			
			   select a.policy_code,
		       a.busi_item_id,
		       a.old_pol_no,
		       a.apply_code prtNo,
		       case a.master_busi_item_id
		         when null then
		          ''
		         else
		          (select nvl(i.old_pol_no, to_char(i.busi_item_id))
		             from dev_pas.t_contract_busi_prod i
		            where i.busi_item_id = a.master_busi_item_id)
		       end mainPolNo,
		       a.busi_prod_code riskCode,
		       b.organ_code manageCom,
		       b.channel_type saleChnl,
		       h.customer_id insuredNo,
		       d.customer_name insuredName,
		       d.customer_gender insuredSex,
		       d.customer_birthday insuredBirthday,
		       h.insured_age insuredAppAge,
		       (select count(1)
		          from dev_pas.t_benefit_insured bi
		         where bi.policy_id = b.policy_id) insuredPeoples,
		       c.job_underwrite  occupationType,
		       (select ph.customer_id
		          from dev_pas.t_policy_holder ph
		         where ph.policy_id = b.policy_id
		           and rownum = 1) appntNo,
		       (select tc.customer_name
		          from dev_pas.t_customer tc
		         where tc.customer_id = (select ph.customer_id
		          from dev_pas.t_policy_holder ph
		         where ph.policy_id = b.policy_id
		           and rownum = 1) ) appntName,
		       a.validate_date cValiDate,
		       a.issue_date signDate,
		       (select ce.pay_due_date
		          from dev_pas.t_contract_extend ce
		         where ce.item_id = e.item_id )paytoDate ，
		       a.expiry_date endDate,
		       e.charge_year payEndYear,
		       e.charge_period payEndYearFlag,
		       e.COVERAGE_PERIOD insuYearFlag,
		       e.Coverage_Year insuYear,
		       
		       (SELECT a.old_code FROM  
		       	dev_Pas.t_Code_Mapper a 
		       	where a.codetype = 'PREM_FREQ' 
		       	and new_code = e.prem_freq)payIntv,
		       	
		       e.charge_year payYears,
		       e.unit  mult,
		       e.std_prem_af prem,
		       e.total_prem_af sumPrem,
		       e.amount amnt,
		       a.renew rnewFlag,
		       a.liability_state appFlag,
		       a.insert_time makeDate,
		       (select bp.Product_Code_Std from  dev_pds.t_business_product bp where bp.business_prd_id = a.busi_prd_id) origRiskCode,
		       (select bp.Cover_Period_Type from  dev_pds.t_business_product bp where bp.business_prd_id = a.busi_prd_id) riskPeriod,
		       (select npa.Acknowledge_Date from dev_nb.t_Nb_Policy_Acknowledgement npa where npa.policy_id = b.policy_id  ) CustomGetPolDate,
		       a.renew_times renewCount,
		       e.Coverage_Period,
		       e.Coverage_Year,
		       e.SUSPEND_DATE,
		       e.VALIDATE_DATE
		  from dev_pas.t_contract_busi_prod a,
		       dev_pas.t_contract_master    b,
		       dev_pas.t_benefit_insured    c,
		       dev_pas.t_customer           d,
		       dev_pas.t_contract_product   e,
		       dev_pas.t_insured_list h
		 where a.apply_code = b.apply_code
		   and a.busi_item_id = c.busi_item_id
		   and c.insured_id = h.list_id
		   and d.customer_id = h.customer_id
		   and b.policy_id = e.policy_id
		   and a.busi_item_id = e.busi_item_id
		   
		
		   
		   and a.apply_code = #{apply_code}
		   and d.customer_id = #{customer_id}
		]]>
		
		
	</if>
	
	<if test="step != null and  step == 'three' ">
		<![CDATA[
			select a.policy_code,
			   b.apply_date applyDate,
		       a.busi_item_id,
		       a.apply_code prtNo,
		       a.master_busi_item_id mainPolNo,
		       (select tp.product_code_sys from dev_pds.t_business_product tp where a.busi_prd_id = tp.business_prd_id) riskCode,
		       b.organ_code manageCom,
		       b.channel_type saleChnl,
		       h.customer_id insuredNo,
		       d.customer_name insuredName,
		       d.customer_gender insuredSex,
		       d.customer_birthday insuredBirthday,
		       TRUNC (MONTHS_BETWEEN (b.APPLY_DATE,d.CUSTOMER_BIRTHDAY)/12) AS insuredAppAge,
		       (select count(1)
		          from dev_pas.t_benefit_insured bi
		         where bi.policy_id = b.policy_id) insuredPeoples,
		       c.job_underwrite  occupationType,
		       (select ph.customer_id
		          from dev_nb.t_nb_policy_holder ph
		         where ph.policy_id = b.policy_id
		           and rownum = 1) appntNo,
		       (select tc.customer_name
		          from dev_pas.t_customer tc
		         where tc.customer_id = (select ph.customer_id
		          from dev_nb.t_nb_policy_holder ph
		         where ph.policy_id = b.policy_id
		           and rownum = 1) ) appntName,
		       a.validate_date cValiDate,
		       a.issue_date signDate,
		       (select ce.pay_due_date
		          from dev_pas.t_contract_extend ce
		         where ce.item_id = e.item_id )paytoDate ，
		       a.expiry_date endDate,
		       e.charge_year payEndYear,
		       e.charge_period payEndYearFlag,
		       e.COVERAGE_PERIOD insuYearFlag,
		       e.Coverage_Year insuYear,
		       e.prem_freq payIntv,
		       e.charge_year payYears,
		       e.unit  mult,
		       e.std_prem_af prem,
		       e.total_prem_af sumPrem,
		       e.amount amnt,
		       a.renew rnewFlag,
		       a.liability_state appFlag,
		       a.insert_time makeDate,
		       (select bp.Product_Code_Std from  dev_pds.t_business_product bp where bp.business_prd_id = a.busi_prd_id) origRiskCode,
		       (select bp.Cover_Period_Type from  dev_pds.t_business_product bp where bp.business_prd_id = a.busi_prd_id) riskPeriod,
		       (select npa.Acknowledge_Date from dev_nb.t_Nb_Policy_Acknowledgement npa where npa.policy_id = b.policy_id  ) CustomGetPolDate,
		       '' renewCount,
		       e.Coverage_Period,
		       e.Coverage_Year,
		       e.VALIDATE_DATE,
		       e.EXPIRY_DATE
		  from dev_nb.t_nb_contract_busi_prod a,
		       dev_nb.t_nb_contract_master    b,
		       dev_nb.t_nb_benefit_insured    c,
		       dev_nb.t_customer           d,
		       dev_nb.t_nb_contract_product   e,
		       dev_nb.t_nb_insured_list h
		 where a.apply_code = b.apply_code
		   and a.busi_item_id = c.busi_item_id
		   and c.insured_id = h.list_id
		   and d.customer_id = h.customer_id
		   and b.policy_id = e.policy_id
		   and a.busi_item_id = e.busi_item_id
		   and a.apply_code = #{apply_code}
		   and d.customer_id = #{customer_id}
			
		]]>
	</if>
		
	<if test="step != null and  step == 'four' ">
	
		<![CDATA[
		
		select distinct b.busi_item_id, b.log_id ,b.update_time
		  from dev_pas.t_cs_policy_change a
		  left join DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL b
		    on a.change_id = b.change_id
		   and b.policy_chg_id = a.policy_chg_id
		  left join dev_pas.t_cs_accept_change c
		    on c.accept_id = a.accept_id
		   and c.change_id = b.change_id
		 where c.service_code = 'NS'
		   
		   and a.policy_code = #{policy_code}
		   and b.old_new = '1'
		    and b.operation_type='1'
		     and not exists (
		   	  select * from dev_pas.t_contract_busi_prod cb where cb.policy_code=b.policy_code
		   	  and cb.busi_item_id=b.busi_item_id
		   )
		]]>
	
	</if>
	
	
	<if test="step != null and  step == 'five' ">
	
		<![CDATA[
		
			 select a.policy_code,
		       a.busi_item_id,
		       a.old_pol_no,
		       a.apply_code prtNo,
		       case a.master_busi_item_id
		         when null then
		          ''
		         else
		          (select nvl(i.old_pol_no, to_char(i.busi_item_id))
		             from dev_pas.t_contract_busi_prod i
		            where i.busi_item_id = a.master_busi_item_id)
		       end mainPolNo,
		       a.busi_prod_code riskCode,
		       b.organ_code manageCom,
		       b.channel_type saleChnl,
		       h.customer_id insuredNo,
		       d.customer_name insuredName,
		       d.customer_gender insuredSex,
		       d.customer_birthday insuredBirthday,
		       h.insured_age insuredAppAge,
		       (select count(1)
		          from dev_pas.t_benefit_insured bi
		         where bi.policy_id = b.policy_id) insuredPeoples,
		       c.job_underwrite  occupationType,
		       (select ph.customer_id
		          from dev_pas.t_policy_holder ph
		         where ph.policy_id = b.policy_id
		           and rownum = 1) appntNo,
		       (select tc.customer_name
		          from dev_pas.t_customer tc
		         where tc.customer_id = (select ph.customer_id
		          from dev_pas.t_policy_holder ph
		         where ph.policy_id = b.policy_id
		           and rownum = 1) ) appntName,
		       a.validate_date cValiDate,
		       a.issue_date signDate,
		       (select ce.pay_due_date
		          from dev_pas.t_contract_extend ce
		         where ce.item_id = e.item_id )paytoDate ，
		       a.expiry_date endDate,
		       e.charge_year payEndYear,
		       e.charge_period payEndYearFlag,
		       e.COVERAGE_PERIOD insuYearFlag,
		       e.Coverage_Year insuYear,
		       e.prem_freq payIntv,
		       e.charge_year payYears,
		       e.unit  mult,
		       e.std_prem_af prem,
		       e.total_prem_af sumPrem,
		       e.amount amnt,
		       a.renew rnewFlag,
		       a.liability_state appFlag,
		       a.insert_time makeDate,
		       (select bp.Product_Code_Std from  dev_pds.t_business_product bp where bp.business_prd_id = a.busi_prd_id) origRiskCode,
		       (select bp.Cover_Period_Type from  dev_pds.t_business_product bp where bp.business_prd_id = a.busi_prd_id) riskPeriod,
		       (select npa.Acknowledge_Date from dev_nb.t_Nb_Policy_Acknowledgement npa where npa.policy_id = b.policy_id  ) CustomGetPolDate,
		       a.renew_times renewCount,
		       e.Coverage_Period,
		       e.Coverage_Year,
		       e.SUSPEND_DATE,
		       e.VALIDATE_DATE
		  from DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL a,
		       dev_pas.t_contract_master    b,
		       DEV_PAS.T_CS_BENEFIT_INSURED    c,
		       dev_pas.t_customer           d,
		       DEV_PAS.T_CS_CONTRACT_PRODUCT   e,
		       dev_pas.t_insured_list h
		 where a.apply_code = b.apply_code
		   and a.busi_item_id = c.busi_item_id
		   and c.insured_id = h.list_id
		   and d.customer_id = h.customer_id
		   and b.policy_id = e.policy_id
		   and a.busi_item_id = e.busi_item_id
		   and a.log_id  = #{log_id}
       	   and c.old_new = 1
           and e.old_new = 1
		   
		]]>
	
	</if>
		
	</select>
	<!-- 已承保保单查询接口 -->
	<select id="queryContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 
               TCBP.BUSI_ITEM_ID,
               TCBP.ISSUE_DATE,
               TCBP.BUSI_PROD_CODE,
               TCBP.LIABILITY_STATE,
               TBP.PRODUCT_NAME_SYS,
               TUBP.DECISION_CODE,
               TRAP.EXTRA_POINT
           FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
           LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT TBP
            ON TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
           LEFT JOIN DEV_UW.T_UW_BUSI_PROD TUBP
            ON TUBP.UW_BUSI_ID =TCBP.BUSI_ITEM_ID
           LEFT JOIN DEV_UW.T_RI_APP_PRODUCT TRAP
            ON TCBP.POLICY_ID = TRAP.POLICY_ID
		   WHERE TCBP.APPLY_CODE = #{apply_code}
		]]>
	</select>
	<!-- 险种责任领取项  -->
	<select id="QRY_queryLpPremItem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TPP.POLICY_CODE,
			       TPP.BUSI_ITEM_ID,
			       TPP.ITEM_ID,
			       TPP.LIAB_ID,
			       TPP.LIAB_CODE,
			       TPP.PRODUCT_CODE,
			       TIL.CUSTOMER_ID,
			       TPP.BEGIN_DATE,
			       TPP.PAY_STATUS,
			       TPP.TOTAL_AMOUNT,
			       TPP.INSERT_TIME,
			       TPP.UPDATE_TIME
			  FROM DEV_PAS.T_PAY_PLAN TPP
			 INNER JOIN DEV_PAS.T_INSURED_LIST TIL
			    ON TPP.POLICY_CODE = TIL.POLICY_CODE
			 INNER JOIN DEV_PAS.T_BENEFIT_INSURED TBI
			    ON TIL.POLICY_CODE = TBI.POLICY_CODE
			   AND TIL.LIST_ID = TBI.INSURED_ID
			   AND TPP.BUSI_ITEM_ID = TBI.BUSI_ITEM_ID
			 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD B
			    ON TBI.POLICY_CODE=B.POLICY_CODE AND TBI.BUSI_ITEM_ID=B.BUSI_ITEM_ID  AND  TPP.BUSI_ITEM_ID=B.BUSI_ITEM_ID
			 WHERE TPP.PAY_STATUS <> '4'
		]]>
		 <if test="policy_code!=null and policy_code!=''">
				AND TPP.POLICY_CODE=#{policy_code}
		</if>
		 <if test="apply_code!=null and apply_code!=''">
				AND B.APPLY_CODE=#{apply_code}
		</if>
		<if test=" pol_no != null and pol_no != '' ">
		AND (B.BUSI_ITEM_ID=#{pol_no} OR B.OLD_POL_NO=#{pol_no}) 
		</if>
	</select>

	<select id="PA_queryNBConBusiProdByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT BUSI_ITEM_ID,BUSI_PRD_ID,PRODUCT_CODE,APPLY_CODE,POLICY_CODE,
			MASTER_BUSI_ITEM_ID,POLICY_ID,VALIDATE_DATE,EXPIRY_DATE,
			INITIAL_PREM_DATE,DECISION_CODE,LIABILITY_STATE,WAIVER,ISSUE_DATE,
			JOINT_LIFE_FLAG,RENEW,ASSURERENEW_FLAG,APL_PERMIT,HESITATION_PERIOD_DAY,
			HESITATION2ACC,IS_WAIVED,WAIVER_START,WAIVER_END,OLD_POL_NO,INSERT_BY,
			INSERT_TIME,INSERT_TIMESTAMP,UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP
			FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD A WHERE 1=1
		]]>
	<include refid="PA_queryContractBusiProdByBusiItemIdCondition" />
	</select>
	
	<!-- 查询核保决定  -->
	<select id="QRY_queryDecisionCodeByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[			 
 			   SELECT M.DECISION_CODE FROM (
                SELECT A.* FROM DEV_UW.T_UW_PRODUCT A
 				INNER JOIN DEV_UW.T_UW_MASTER M ON A.Uw_Id=M.UW_ID
 			    WHERE A.BUSI_PROD_CODE=#{busi_prod_code} and A.APPLY_CODE=#{apply_code} ORDER BY A.UPDATE_TIME DESC) M WHERE ROWNUM = 1
		]]>
	</select>
	<select id="PAandNB_findMainBusiProdByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT A.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 AND A.MASTER_BUSI_ITEM_ID IS NULL]]>
			<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE= #{policy_code} ]]></if>
			<if test=" acc_date != null "><![CDATA[ AND a.EXPIRY_DATE > #{acc_date} ]]></if>
	</select>
	
		<!-- 通过投保单号查询契约库 -->
	<select id="NB_findAllContractDecsion" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[  select b.MULTI_MAINRISK_FLAG AS 多主险标识,a.DECISION_CODE AS 核保结论, a.PRODUCT_CODE AS 所属业务代码，a.apply_code AS 投保单号 from dev_nb.T_NB_CONTRACT_BUSI_PROD a
			 INNER JOIN dev_nb.T_NB_CONTRACT_MASTER b ON a.APPLY_CODE = b.APPLY_CODE
			  WHERE a.PRODUCT_CODE = '00822000' 
			 AND a.DECISION_CODE IN ('40','50')
			 AND b.MULTI_MAINRISK_FLAG = '1' 
			  AND A.APPLY_CODE = #{apply_code}]]>
	</select>
	
	
	<!-- 查询险种信息 -->
	<select id="NB_findContractBusiProdByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT 
				A.BUSI_ITEM_ID,A.BUSI_PRD_ID,A.PRODUCT_CODE,A.APPLY_CODE,A.POLICY_CODE,A.MASTER_BUSI_ITEM_ID,
				A.POLICY_ID,A.VALIDATE_DATE,A.EXPIRY_DATE,A.INITIAL_PREM_DATE,A.DECISION_CODE,A.LIABILITY_STATE,
				BP.PRODUCT_CODE_SYS,BP.PRODUCT_NAME_STD
  			FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD A 
  				INNER JOIN DEV_NB.T_BUSINESS_PRODUCT BP ON A.PRODUCT_CODE = BP.PRODUCT_CODE_SYS
 		WHERE A.APPLY_CODE = #{apply_code}]]>
	</select>
	
	<!-- 查询险种信息 -->
	<select id="QRY_queryPolicyPremDetailInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
					SELECT AA.POLICY_CODE, /*保单号*/
					       AA.BUSI_ITEM_ID, /*险种ID*/
					       AA.MASTER_BUSI_ITEM_ID, /*所属主险ID*/
					       AA.BUSI_PROD_CODE, /*险种代码*/
					       AA.PRODUCT_NAME_SYS, /*险种全称*/
					       AA.PRODUCT_ABBR_NAME, /*险种简称*/
					       AA.PRODUCT_CODE, /*责任组编码*/
					       AA.PRODUCT_NAME, /*责任组名称*/
					       AA.DUE_TIME, /*应交日期*/
					       SUM(FEE_AMOUNT) FEE_AMOUNT, /*保费金额*/
					       AA.ARAP_FLAG, /*收付标识*/
					       AA.FEE_SCENE_CODE, /*缴费场景编码*/
					       AA.FEE_SCENE_NAME, /*缴费场景名称*/
					       AA.SERVICE_CODE, /*保全项代码*/
					       AA.SERVICE_NAME, /*保全项目名称*/
					       AA.PAY_MODE, /*交费形式*/
					       AA.PAY_MODE_NAME, /*交费形式名称*/
					       AA.BUSINESS_TYPE, /*业务类型代码*/
					       AA.BUSINESS_TYPE_NAME, /*业务类型名称*/
					       AA.CAP_PAY_MODE, /*交费形式代码*/
					       AA.CAP_PAY_MODE_NAME, /*收付方式名称*/
					       AA.ACTUAL_BANK_ACCOUNT, /*划款账号*/
					       AA.FINISH_TIME, /*划款日期*/
					       AA.UNIT_NUMBER, /*应收应付流水标识*/
					       AA.FEE_STATUS /*续期交费状态代码*/
					  FROM (
					         SELECT TCBP.POLICY_CODE, /*保单号*/
					                TCBP.BUSI_ITEM_ID, /*险种ID*/
					                TCBP.MASTER_BUSI_ITEM_ID, /*所属主险ID*/
					                TCBP.BUSI_PROD_CODE, /*险种代码*/
					                (SELECT PRODUCT_NAME_SYS
					                   FROM DEV_PAS.T_BUSINESS_PRODUCT A
					                  WHERE A.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID) AS PRODUCT_NAME_SYS, /*险种全称*/
					                (SELECT PRODUCT_ABBR_NAME
					                   FROM DEV_PAS.T_BUSINESS_PRODUCT A
					                  WHERE A.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID) AS PRODUCT_ABBR_NAME, /*险种简称*/
					                VPA.PRODUCT_CODE, /*责任组编码*/
					                (SELECT PRL.PRODUCT_NAME
					                   FROM DEV_PDS.T_PRODUCT_LIFE PRL
					                  WHERE PRL.INTERNAL_ID =
					                        NVL(VPA.PRODUCT_CODE, TCP.PRODUCT_CODE)) AS PRODUCT_NAME, /*责任组名称*/
					                VPA.DUE_TIME, /*应交日期*/
					                VPA.FEE_AMOUNT, /*保费金额*/
					                VPA.ARAP_FLAG, /*收付标识*/
					                VPA.FEE_SCENE_CODE, /*缴费场景编码*/
					                (SELECT TFSC.FEE_SCENE_NAME
					                   FROM DEV_PAS.T_FEE_SCENE_CODE TFSC
					                  WHERE TFSC.FEE_SCENE_CODE = VPA.FEE_SCENE_CODE) FEE_SCENE_NAME, /*缴费场景名称*/
					                VPA.SERVICE_CODE, /*保全项代码*/
					                (SELECT TS.SERVICE_NAME
					                   FROM DEV_PAS.T_SERVICE TS
					                  WHERE TS.SERVICE_CODE = VPA.SERVICE_CODE) SERVICE_NAME, /*保全项目名称*/
					                VPA.PAY_MODE, /*交费形式*/
					                (SELECT TPM.NAME
					                   FROM DEV_PAS.T_PAY_MODE TPM
					                  WHERE TPM.CODE = VPA.PAY_MODE) PAY_MODE_NAME, /*交费形式名称*/
					                B.BUSINESS_TYPE, /*业务类型代码*/
					                (SELECT TBTD.BUSINESS_TYPE_NAME
					                   FROM DEV_PAS.T_BUSINESS_TYPE_DEF TBTD
					                  WHERE TBTD.BUSINESS_TYPE = B.BUSINESS_TYPE) BUSINESS_TYPE_NAME, /*业务类型名称*/
					                B.CAP_PAY_MODE, /*交费形式代码*/
					                B.CAP_PAY_MODE_NAME, /*收付方式名称*/
					                B.ACTUAL_BANK_ACCOUNT, /*划款账号*/
					                B.FINISH_TIME, /*划款日期*/
					                VPA.UNIT_NUMBER, /*应收应付流水标识*/
					                VPA.FEE_STATUS /*续期交费状态代码*/
					          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
					                DEV_PAS.T_CONTRACT_PRODUCT TCP,
					                DEV_PAS.V_PREM_ALL VPA,
					                (SELECT DISTINCT VCD.BUSI_PROD_CODE,
					                                 VCD.PAY_MODE CAP_PAY_MODE,
					                                 (SELECT TPM.NAME
					                                    FROM DEV_PAS.T_PAY_MODE TPM
					                                   WHERE TPM.CODE = VCD.PAY_MODE) CAP_PAY_MODE_NAME,
					                                 VCD.ACTUAL_BANK_ACCOUNT, /*划款账号*/
					                                 VCD.FINISH_TIME, /*划款日期*/
					                                 (CASE
					                                   WHEN TPA.BUSINESS_TYPE = '1004' THEN
					                                    NVL((SELECT DISTINCT A.UNIT_NUMBER
					                                          FROM DEV_CAP.T_PREM_ARAP A
					                                         WHERE A.POLICY_CODE = TPA.POLICY_CODE
					                                           AND A.BUSI_PROD_CODE =
					                                               TPA.BUSI_PROD_CODE
					                                           AND A.BUSINESS_TYPE = '1005'
					                                           AND ROWNUM = 1),
					                                        TPA.UNIT_NUMBER)
					                                   ELSE
					                                    TPA.UNIT_NUMBER
					                                 END) UNIT_NUMBER,
					                                 TPA.BUSINESS_TYPE
					                   FROM DEV_CAP.V_CASH_DETAIL VCD, DEV_CAP.T_PREM_ARAP TPA
					                  WHERE 1 = 1
					                    AND VCD.UNIT_NUMBER = TPA.UNIT_NUMBER
					                    AND VCD.BUSI_PROD_CODE = TPA.BUSI_PROD_CODE
					                    AND TPA.FEE_STATUS <> '02'
					                    AND VCD.POLICY_CODE = #{policy_code} ) B
					         WHERE 1 = 1
					           AND TCBP.POLICY_CODE = TCP.POLICY_CODE
					           AND TCBP.BUSI_ITEM_ID = TCP.BUSI_ITEM_ID
					           AND TCP.POLICY_CODE = VPA.POLICY_CODE
					           AND TCP.ITEM_ID = VPA.ITEM_ID
					           AND VPA.UNIT_NUMBER = B.UNIT_NUMBER
					           AND VPA.BUSI_PROD_CODE = B.BUSI_PROD_CODE
					           AND VPA.FEE_STATUS <> '02'
					           AND TCBP.POLICY_CODE = #{policy_code}]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND TCP.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" start_date != null and start_date != '' "><![CDATA[ AND VPA.DUE_TIME >= #{start_date} ]]></if>
		<if test=" end_date != null and end_date != '' "><![CDATA[ AND VPA.DUE_TIME <#{end_date} ]]></if>  
		<![CDATA[ 
					           AND NOT EXISTS
					         (SELECT 1
					                  FROM DEV_PAS.V_PREM_ARAP_ALL Z
					                 WHERE Z.UNIT_NUMBER = VPA.UNIT_NUMBER
					                   AND ((Z.FEE_TYPE = 'P004990000' AND
					                       Z.WITHDRAW_TYPE = '0049900000') OR
					                       ((Z.FEE_TYPE = 'P004300000' OR
					                       Z.FEE_TYPE = 'G004270000') AND
					                       Z.WITHDRAW_TYPE = '0040900000')))) AA
					
					 GROUP BY AA.POLICY_CODE, /*保单号*/
					          AA.BUSI_ITEM_ID, /*险种ID*/
					          AA.MASTER_BUSI_ITEM_ID, /*所属主险ID*/
					          AA.BUSI_PROD_CODE, /*险种代码*/
					          AA.PRODUCT_NAME_SYS, /*险种全称*/
					          AA.PRODUCT_ABBR_NAME, /*险种简称*/
					          AA.PRODUCT_CODE, /*责任组编码*/
					          AA.PRODUCT_NAME, /*责任组名称*/
					          AA.DUE_TIME, /*应交日期*/
					          AA.ARAP_FLAG, /*收付标识*/
					          AA.FEE_SCENE_CODE, /*缴费场景编码*/
					          AA.FEE_SCENE_NAME, /*缴费场景名称*/
					          AA.SERVICE_CODE, /*保全项代码*/
					          AA.SERVICE_NAME, /*保全项目名称*/
					          AA.PAY_MODE, /*交费形式*/
					          AA.PAY_MODE_NAME, /*交费形式名称*/
					          AA.BUSINESS_TYPE, /*业务类型代码*/
					          AA.BUSINESS_TYPE_NAME, /*业务类型名称*/
					          AA.CAP_PAY_MODE, /*交费形式代码*/
					          AA.CAP_PAY_MODE_NAME, /*收付方式名称*/
					          AA.ACTUAL_BANK_ACCOUNT, /*划款账号*/
					          AA.FINISH_TIME, /*划款日期*/
					          AA.UNIT_NUMBER, /*应收应付流水标识*/
					          AA.FEE_STATUS /*续期交费状态代码*/
					 ORDER BY AA.FINISH_TIME    ]]>
	</select>
			
	<select id="findContractBusiProdByPolicyCaseId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.BUSI_PRD_ID,A.BUSI_PROD_CODE, A.APPLY_CODE, A.IS_WAIVED, A.POLICY_ID, A.WAIVER,
      A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE,
      A.VALIDATE_DATE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.RENEW, A.MATURITY_DATE,
      A.BUSI_ITEM_ID, A.ISSUE_DATE, A.DECISION_CODE, A.GURNT_RENEW_END, A.GURNT_RENEW_START FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE 1 = 1 ]]>			
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE= #{policy_code} ]]></if>
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID ]]>
	</select>
	
	<select id="findCustomerIdByPolicyCaseBusiId"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.BUSI_ITEM_ID, A.INSURED_ID, A.CUSTOMER_ID
		  FROM (SELECT  A.BUSI_ITEM_ID, B.INSURED_ID,C.CUSTOMER_ID
		          FROM DEV_PAS.T_CONTRACT_BUSI_PROD A
		         INNER JOIN DEV_PAS.T_BENEFIT_INSURED B
		            ON A.BUSI_ITEM_ID = B.BUSI_ITEM_ID
		         INNER JOIN DEV_PAS.T_INSURED_LIST C
		            ON C.LIST_ID = B.INSURED_ID
		         WHERE 1=1 ]]>
		 <if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE= #{policy_code} ]]></if>
		 <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		 <![CDATA[ ) A
		 GROUP BY A.BUSI_ITEM_ID, A.INSURED_ID, A.CUSTOMER_ID ]]>	
	</select>
	<!-- 	技术需求任务 #192831: 接口需求_官微医药无忧（A04）险种保单相关信息查询接口申请（新核心 -->
	<select id="qry_queryNBConBusiProdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			 SELECT A.CUSTOMER_ID, C.BUSI_PROD_CODE,
 C.BUSI_ITEM_ID,C.POLICY_CODE,
			C.MASTER_BUSI_ITEM_ID,C.VALIDATE_DATE,C.EXPIRY_DATE,
			C.RENEW,C.APPLY_CODE
   FROM DEV_PAS.T_INSURED_LIST       A,
        DEV_PAS.T_BENEFIT_INSURED    B,
        DEV_PAS.T_CONTRACT_BUSI_PROD C
  WHERE A.LIST_ID = B.INSURED_ID
    AND C.BUSI_ITEM_ID = B.BUSI_ITEM_ID 
		]]>
	<include refid="PA_queryCondetionByApplyCode" />
	</select>
	<!-- 	技术需求任务 #192831: 接口需求_官微医药无忧（A04）险种保单相关信息查询接口申请（新核心 -->
	<select id="qry_findAllUwContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			   SELECT A.CUSTOMER_ID, B.PRODUCT_CODE, B.BUSI_ITEM_ID ,A.UW_ID FROM 
 DEV_UW.T_INSURED_LIST A,
  DEV_UW.T_BENEFIT_INSURED B,
  DEV_UW.T_CONTRACT_BUSI_PROD C
  WHERE A.LIST_ID = B.INSURED_ID AND A.UW_ID = B.UW_ID AND B.UW_ID=C.UW_ID AND B.BUSI_ITEM_ID=C.BUSI_ITEM_ID
		]]>
	<include refid="PA_queryCondetionByApplyCodeSQL" />
	</select>
	<!-- 获取险种核保信息 特约信息 -->
     <select id="QryuwConditionQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
  
 SELECT (SELECT TPL.PRODUCT_NAME
           FROM DEV_PDS.T_PRODUCT_LIFE TPL
          WHERE TO_CHAR(TPL.PRODUCT_ID) = NNN.PRODUCT_ID) PRODUCT_CODE,
        NNN.CONDITION_DESC,
        NB.VALIDATE_DATE,
        NNN.UW_CONDITION_TYPE,
        NNN.REASON_CONTENT,
        /*192831接口需求_官微医药无忧（A04）险种保单相关信息查询接口申请（新核心）*/
        NNN.BUSI_ITEM_ID AS BUSI_PRD_ID,
        NNN.UW_SOURCE_TYPE
   FROM (SELECT   NVL(
                (SELECT CC.PRODUCT_ID 
                 FROM DEV_UW.T_CONTRACT_PRODUCT CC 
                WHERE CC.UW_ID = N.UW_ID AND CC.ITEM_ID=NN.ITEM_ID AND CC.APPLY_CODE=N.APPLY_CODE),(SELECT PP.PRODUCT_ID
                 FROM DEV_PAS.T_CONTRACT_PRODUCT PP
                WHERE PP.ITEM_ID = NN.ITEM_ID)) PRODUCT_ID,
                NN.POLICY_ID,
                N.CONDITION_DESC,
                N.UW_CONDITION_TYPE,
                TUDR.REASON_CONTENT,
                N.UW_PRD_ID,
                N.UW_ID,
                N.POLICY_CODE,
                N.APPLY_CODE,
                N.BUSI_ITEM_ID,
                N.UW_BUSI_ID,
                 XT.UW_SOURCE_TYPE
           FROM DEV_UW.T_UW_CONDITION N
           INNER JOIN DEV_UW.T_UW_PRODUCT NN
             ON N.UW_ID = NN.UW_ID
            AND N.ITEM_ID = NN.ITEM_ID
            AND N.BUSI_ITEM_ID = NN.BUSI_ITEM_ID
           LEFT JOIN (SELECT UW_BUSI_ID,UW_ID,REASON_CONTENT from DEV_UW.T_UW_DECISION_REASON WHERE REASON_TYPE ='4') TUDR  
             ON N.UW_ID = TUDR.UW_ID 
               AND N.UW_BUSI_ID = TUDR.UW_BUSI_ID
                 LEFT JOIN DEV_UW.T_UW_POLICY XT
         ON XT.UW_ID= N.UW_ID
         ) NNN
  INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER NB
     ON NB.APPLY_CODE = NNN.APPLY_CODE
  WHERE 1 = 1
        ]]>
        <include refid="uwConditionQryConditions" />
    </select>
	<sql id="uwConditionQryConditions">
	  <![CDATA[ AND NNN.UW_ID= #{uw_id} ]]>
	<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[AND NNN.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>	
	
	<sql id="PA_queryCondetionByApplyCode">
	<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND C.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_queryCondetionByApplyCodeSQL">
	<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND a.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
</mapper>