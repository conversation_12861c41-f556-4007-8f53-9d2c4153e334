<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.impl.task.dao.impl.TaskDaoImpl">


  
<!-- 按保单号条件查询个数操作 -->
<select id="qryByPolicyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
<![CDATA[
SELECT 
  COUNT(1)
FROM 
(]]>
 <include refid="queryTaskByPolicyMain" />
<![CDATA[
)  TASK  
]]>

</select>

<!-- 按客户信息条件分页查询操作 -->
<select id="queryTaskByPolicyForPage" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
SELECT 
    B.POLICY_CODE,
    B.TASK_PROCESS_CODE,
    B.TASK_PROCESS_ID,
    B.BUSINESS_TYPE_DESC,
    B.CUSTOMER_NAME,
    B.ACCEPT_STATUS,
    B.OPERATOR_NAME,
    B.UPDATE_TIME ,
    B.ACCIDENT_NO
FROM(
SELECT 
    ROWNUM RN,
    POLICY_CODE,
    TASK_PROCESS_ID,
    TASK_PROCESS_CODE,
    BUSINESS_TYPE_DESC,
    CUSTOMER_NAME,
    ACCEPT_STATUS,
    OPERATOR_NAME,
    UPDATE_TIME ,
    ACCIDENT_NO 
FROM 
(]]>
<include refid="queryTaskByPolicyMain" />


<![CDATA[
   )  TASK ) B WHERE 1=1 AND B.RN > ${GREATER_NUM} AND B.RN<=${LESS_NUM}  
]]>
</select>

<sql id="queryTaskByPolicyMain">
<![CDATA[
    SELECT C.POLICY_CODE,
        0 AS TASK_PROCESS_ID,
        AC.ACCEPT_CODE AS TASK_PROCESS_CODE ,
        '保全' AS BUSINESS_TYPE_DESC,
        '02' AS BUSINESS_TYPE_CODE,
        AA.APPLY_NAME AS CUSTOMER_NAME ,
        AC.ACCEPT_STATUS AS ACCEPT_STATUS ,
        CH.UPDATE_BY AS OPERATOR_NAME,
        AC.UPDATE_TIME ,
        '' AS ACCIDENT_NO
    FROM
        DEV_PAS.T_CS_POLICY_CHANGE C
    INNER JOIN DEV_PAS.T_CS_APPLICATION AA 
    ON C.CHANGE_ID=AA.CHANGE_ID
    INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC 
    ON AC.ACCEPT_ID=C.ACCEPT_ID
    INNER JOIN (
        SELECT C.CHANGE_ID,C.CHANGE_STATUS,C.UPDATE_TIME,TO_CHAR(C.UPDATE_BY) AS UPDATE_BY FROM DEV_PAS.T_CHANGE C) CH 
    ON CH.CHANGE_ID=C.CHANGE_ID
    WHERE 1=1
]]>
<include refid="csQryCSPolicyCondition" />
<![CDATA[
    union 
    SELECT  CM.POLICY_CODE,
        CC.CASE_ID AS TASK_PROCESS_ID,
        CC.CASE_NO AS TASK_PROCESS_CODE,
        '理赔'  AS BUSINESS_TYPE_DESC,
        '03' AS BUSINESS_TYPE_CODE,
        C.CUSTOMER_NAME,
        CC.CASE_STATUS AS ACCEPT_STATUS,
        TO_CHAR(NVL(CC.ACCEPTOR_ID,CC.UPDATE_BY)) AS OPERATOR_NAME ,
        CC.UPDATE_TIME ,
        TCA.ACCIDENT_NO AS ACCIDENT_NO
    FROM 
        DEV_CLM.T_CLAIM_CASE CC
    INNER JOIN 
        (select * from DEV_CLM.T_CONTRACT_MASTER WHERE CUR_FLAG=1) CM
    ON CC.Case_Id=CM.Case_Id
    INNER JOIN 
        DEV_CLM.T_CUSTOMER C
    ON CC.INSURED_ID=C.CUSTOMER_ID
    INNER JOIN DEV_CLM.T_CLAIM_ACCIDENT TCA 

ON TCA.ACCIDENT_ID = CC.ACCIDENT_ID 
    
    WHERE 1=1
]]>
<include refid="csQryClmPolicyCondition" />
</sql>
<!-- 查询条件 -->
<sql id="csQryCSPolicyCondition">
    <if test="policy_code != null">
    <![CDATA[ AND C.POLICY_CODE=#{policy_code}]]>
    </if>
</sql> 

<sql id="csQryClmPolicyCondition">
    <if test="policy_code != null">
    <![CDATA[ AND CM.POLICY_CODE=#{policy_code}]]>
    </if>
</sql> 



<!-- 按客户信息条件查询个数操作 -->
<select id="qryByCustomerTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
<![CDATA[
SELECT 
  COUNT(1)
FROM 
(]]>
 <include refid="queryTaskByCustomerMain" />
<![CDATA[
)  TASK  
]]>

</select>

<!-- 按客户信息条件分页查询操作 -->
<select id="queryTaskByCustomerForPage" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
SELECT


B.POLICY_CODE,B.TASK_PROCESS_CODE,B.TASK_PROCESS_ID,B.BUSINESS_TYPE_DESC,B.CUSTOMER_NAME,B.ACCEPT_STATUS,B.OPERATOR_NAME,B.UPDATE_TIME

 FROM(
SELECT 
    ROWNUM RN,
    POLICY_CODE,
    TASK_PROCESS_ID,
    TASK_PROCESS_CODE,
    BUSINESS_TYPE_DESC,
    BUSINESS_TYPE_CODE,
    CUSTOMER_NAME,
    ACCEPT_STATUS,
    OPERATOR_NAME,
    UPDATE_TIME
FROM 
(]]>
<include refid="queryTaskByCustomerMain" />
<![CDATA[
   )  TASK ) B WHERE 1=1 AND B.RN > ${GREATER_NUM} AND B.RN<=${LESS_NUM}  
]]>
</select>

<sql id="queryTaskByCustomerMain">
<![CDATA[
SELECT  M.POLICY_CODE,
       0 AS TASK_PROCESS_ID,
        M.APPLY_CODE AS TASK_PROCESS_CODE,
        '新契约' AS  BUSINESS_TYPE_DESC,
        '01' AS BUSINESS_TYPE_CODE,
        P.CUSTOMER_NAME, 
        M.PROPOSAL_STATUS AS ACCEPT_STATUS,
        ( SELECT U.USER_NAME FROM DEV_PAS.T_UDMP_USER U  WHERE U.USER_ID = M.UPDATE_BY) AS OPERATOR_NAME, 
        M.UPDATE_TIME 
    FROM  
        DEV_NB.T_NB_CONTRACT_MASTER M 
    INNER JOIN ( 
    SELECT PH.POLICY_ID,C.CUSTOMER_NAME FROM DEV_NB.T_NB_POLICY_HOLDER PH,DEV_NB.T_CUSTOMER C WHERE PH.CUSTOMER_ID=C.CUSTOMER_ID 
]]>
    <include refid="csQryNbCustomerCondition" />
   <![CDATA[
   )P
    ON 
    M.POLICY_ID=P.POLICY_ID
    UNION ALL 
    SELECT C.POLICY_CODE,
        0 AS TASK_PROCESS_ID,
        AC.ACCEPT_CODE AS TASK_PROCESS_CODE ,
        '保全' AS BUSINESS_TYPE_DESC,
        '02' AS BUSINESS_TYPE_CODE,
        AA.APPLY_NAME AS CUSTOMER_NAME,
        AC.ACCEPT_STATUS AS ACCEPT_STATUS ,
        TO_CHAR(C.UPDATE_BY) AS OPERATOR_NAME,
        AC.UPDATE_TIME 
    FROM 
    DEV_PAS.T_CS_POLICY_CHANGE C
    INNER JOIN DEV_PAS.T_CS_APPLICATION AA 
    ON 
    C.CHANGE_ID=AA.CHANGE_ID
    INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC 
    ON 
    AC.ACCEPT_ID=C.ACCEPT_ID
    INNER JOIN DEV_PAS.T_CUSTOMER CU 
    ON 
    CU.CUSTOMER_ID=AA.CUSTOMER_ID
    INNER JOIN 
    DEV_PAS.T_CHANGE TC 
    ON TC.CHANGE_ID =C.CHANGE_ID 
     WHERE 1=1
     ]]>
    <include refid="csQryCSCustomerCondition" />
     <![CDATA[
     UNION ALL
     SELECT NULL,
         CC.CASE_ID AS TASK_PROCESS_ID,
         CC.CASE_NO AS TASK_PROCESS_CODE,
         '理赔'  AS BUSINESS_TYPE_DESC,
         '03' AS BUSINESS_TYPE_CODE,
         C.CUSTOMER_NAME,
         CC.CASE_STATUS AS ACCEPT_STATUS,
         U.REAL_NAME AS OPERATOR_NAME ,
         CC.UPDATE_TIME 
     FROM 
    DEV_CLM.T_CLAIM_CASE CC
     INNER JOIN 
    DEV_CLM.T_CUSTOMER  C 
     ON 
    CC.INSURED_ID=C.CUSTOMER_ID
     INNER JOIN DEV_PAS.T_UDMP_USER U
     ON
    U.USER_ID=CC.UPDATE_BY
     WHERE 1=1 
     ]]>
     <include refid="csQryClmCustomerCondition" />
</sql>
<!-- 查询条件 -->
<sql id="csQryNbCustomerCondition">
    <if test="customer_name != null and customer_name != '' ">
    <![CDATA[ AND C.CUSTOMER_NAME = '${customer_name}']]>
    </if>
    <if test="customer_cert_type != null and customer_cert_type != '' ">
    <![CDATA[ AND C.CUSTOMER_CERT_TYPE=#{customer_cert_type}]]>
    </if>
    <if test="customer_certi_code != null and customer_certi_code != '' ">
    <![CDATA[ AND C.CUSTOMER_CERTI_CODE=#{customer_certi_code}]]>
    </if>
</sql> 
<sql id="csQryCSCustomerCondition">
    <if test="customer_name != null and customer_name != '' ">
    <![CDATA[ AND AA.APPLY_NAME = '${customer_name}']]>
    </if>
    <if test="customer_cert_type != null and customer_cert_type != ''">
    <![CDATA[ AND CU.CUSTOMER_CERT_TYPE=#{customer_cert_type}]]>
    </if>
    <if test="customer_certi_code != null and customer_certi_code != '' ">
    <![CDATA[ AND CU.CUSTOMER_CERTI_CODE=#{customer_certi_code}]]>
    </if>
</sql> 

<sql id="csQryClmCustomerCondition">
    <if test="customer_name != null and customer_name != '' ">
    <![CDATA[ AND  C.CUSTOMER_NAME = '${customer_name}']]>
    </if>
    <if test="customer_cert_type != null and customer_cert_type != ''">
    <![CDATA[ AND C.CUSTOMER_CERT_TYPE=#{customer_cert_type}]]>
    </if>
    <if test="customer_certi_code != null and customer_certi_code != '' ">
    <![CDATA[ AND C.CUSTOMER_CERTI_CODE=#{customer_certi_code}]]>
    </if>
</sql> 








   
<!-- 新契约查询个数操作 -->
<select id="nbQryTaskProTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
<![CDATA[ 
SELECT  COUNT(1) FROM  
(
]]> 
<include refid="nbQryTaskMain" />
<include refid="nbQryTaskProCondition" />
<![CDATA[ 
) B

]]> 

</select>
<!-- 新契约分页查询操作 -->
<select id="nbQryTaskProForPage" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[

SELECT B.RN AS rowNumber,
B.TASK_PROCESS_ID,
B.TASK_PROCESS_CODE,
B.BUSINESS_TYPE_DESC,
B.BUSINESS_TYPE_CODE,
B.CUSTOMER_NAME,
B.ACCEPT_STATUS,
B.OPERATOR_NAME ,
B.UPDATE_TIME 

FROM 

(
]]>
<include refid="nbQryTaskMain" />
<include refid="nbQryTaskProCondition" />
    <![CDATA[ )  B WHERE  ROWNUM <= ${LESS_NUM}  AND B.RN > ${GREATER_NUM} ]]>
</select>

<sql id="nbQryTaskMain">
<![CDATA[
    SELECT 
       ROWNUM RN, 
       0 AS TASK_PROCESS_ID,
       M.APPLY_CODE AS TASK_PROCESS_CODE,
       '新契约' AS BUSINESS_TYPE_DESC,
       '01' AS BUSINESS_TYPE_CODE,
       P.CUSTOMER_NAME,
       M.PROPOSAL_STATUS AS ACCEPT_STATUS,
       TO_CHAR(M.UPDATE_BY) AS OPERATOR_NAME,
       M.UPDATE_TIME
  FROM DEV_NB.T_NB_CONTRACT_MASTER M
 LEFT JOIN (SELECT PH.POLICY_ID, C.CUSTOMER_NAME
               FROM DEV_NB.T_NB_POLICY_HOLDER PH, DEV_NB.T_CUSTOMER C
              WHERE PH.CUSTOMER_ID = C.CUSTOMER_ID) P
    ON M.POLICY_ID = P.POLICY_ID
    WHERE 1=1
]]>
</sql>

<!-- 查询条件 -->
<sql id="nbQryTaskProCondition">
    <if test="business_code != null and business_code!=''">
    <![CDATA[ AND M.APPLY_CODE=#{business_code} ]]>
    </if>
</sql> 




  
<!-- 保全按受理号查询个数操作 -->
<select id="csQryTaskProTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
<![CDATA[ 
SELECT  COUNT(1) FROM  
(
]]> 
<include refid="csQryTaskMain" />
<include refid="csQryTaskProCondition" />
<![CDATA[ 
) B
]]> 

</select>
<!-- 保全按受理号查分页查询操作 -->
<select id="csQryTaskProForPage" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[

SELECT 
    B.RN AS rowNumber,
    B.POLICY_CODE,
    B.TASK_PROCESS_ID,
    B.TASK_PROCESS_CODE,
    B.BUSINESS_TYPE_DESC,
    B.BUSINESS_TYPE_CODE,
    B.CUSTOMER_NAME,
    B.ACCEPT_STATUS,
    B.OPERATOR_NAME ,
    B.UPDATE_TIME 
FROM 

(
]]>
<include refid="csQryTaskMain" />
<![CDATA[
 AND  ROWNUM <= ${LESS_NUM}  

]]>

<include refid="csQryTaskProCondition" />
    <![CDATA[ )  B WHERE 1=1 AND B.RN > ${GREATER_NUM} ]]>
</select>


<!-- 保全按受理号查分页查询主体查询语句 -->
<sql id="csQryTaskMain">
<![CDATA[
SELECT 
    ROWNUM AS　RN,
    C.POLICY_CODE,
    0 AS TASK_PROCESS_ID,
    AC.ACCEPT_CODE AS TASK_PROCESS_CODE ,
    '保全' AS BUSINESS_TYPE_DESC,
    '02' AS BUSINESS_TYPE_CODE,
    AA.APPLY_NAME AS CUSTOMER_NAME ,
    AC.ACCEPT_STATUS AS ACCEPT_STATUS ,
    TO_CHAR(C.UPDATE_BY) AS OPERATOR_NAME, 
    AC.UPDATE_TIME 
FROM
    DEV_PAS.T_CS_POLICY_CHANGE C
INNER JOIN DEV_PAS.T_CS_APPLICATION AA 
ON C.CHANGE_ID=AA.CHANGE_ID
INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC 
ON AC.ACCEPT_ID=C.ACCEPT_ID
WHERE 1=1
]]>
</sql>

<!-- 查询条件 -->
<sql id="csQryTaskProCondition">
    <if test="business_code != null and business_code!=''">
    <![CDATA[ AND AC.ACCEPT_CODE=#{business_code} ]]>
    </if>
</sql> 
   
   
   
<!-- 理赔按赔案号查询个数操作 -->
<select id="clmQryTaskProTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
<![CDATA[ 
SELECT  COUNT(1) FROM  
(
]]> 
<include refid="clmQryTaskMain" />
<include refid="clmQryTaskProCondition" />
<![CDATA[ 
) B

]]> 

</select>
<!-- 理赔按赔案号页查询操作 -->
<select id="clmQryTaskProForPage" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[

SELECT 
    B.RN AS rowNumber,
    B.TASK_PROCESS_ID,
    B.TASK_PROCESS_CODE,
    B.BUSINESS_TYPE_DESC,
    B.BUSINESS_TYPE_CODE,
    B.CUSTOMER_NAME,
    B.ACCEPT_STATUS,
    B.OPERATOR_NAME ,
    B.UPDATE_TIME ,
    B.ACCIDENT_NO

FROM 

(
]]>
<include refid="clmQryTaskMain" />
<![CDATA[
 AND  ROWNUM <= ${LESS_NUM}  

]]>

<include refid="clmQryTaskProCondition" />
    <![CDATA[ )  B WHERE 1=1 AND B.RN > ${GREATER_NUM} ]]>
</select>

<!--理赔按赔案号查询语句主体-->
<sql id="clmQryTaskMain">
<![CDATA[
SELECT 
    ROWNUM AS RN,
    CC.CASE_ID AS TASK_PROCESS_ID,
    CC.CASE_NO AS TASK_PROCESS_CODE,
    '理赔'  AS BUSINESS_TYPE_DESC,
    '03' AS BUSINESS_TYPE_CODE,
    C.CUSTOMER_NAME,
    CC.CASE_STATUS AS ACCEPT_STATUS,
    TO_CHAR(CC.UPDATE_BY) AS OPERATOR_NAME ,
    CC.UPDATE_TIME ,
    (SELECT TCA.ACCIDENT_NO FROM DEV_CLM.T_CLAIM_ACCIDENT TCA WHERE TCA.ACCIDENT_ID = CC.ACCIDENT_ID) AS ACCIDENT_NO
FROM 
    DEV_CLM.T_CLAIM_CASE CC
LEFT JOIN 
    DEV_CLM.T_CUSTOMER C
ON CC.INSURED_ID=C.CUSTOMER_ID
WHERE 1=1
]]>
</sql>

<!-- 查询条件 -->
<sql id="clmQryTaskProCondition">
    <if test="business_code != null and business_code!=''">
    <![CDATA[ AND CC.CASE_NO=#{business_code} ]]>
    </if>
</sql> 
</mapper>
