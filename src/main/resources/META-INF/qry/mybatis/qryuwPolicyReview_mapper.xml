<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwPolicyReviewDaoImpl">
<!--
	<sql id="uwPolicyReviewWhereCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" plus_note != null and plus_note != ''  "><![CDATA[ AND A.PLUS_NOTE = #{plus_note} ]]></if>
		<if test=" review_app_user  != null "><![CDATA[ AND A.REVIEW_APP_USER = #{review_app_user} ]]></if>
		<if test=" plus_sign  != null "><![CDATA[ AND <PERSON>.PLUS_SIGN = #{plus_sign} ]]></if>
		<if test=" hr_comment  != null "><![CDATA[ AND A.HR_COMMENT = #{hr_comment} ]]></if>
		<if test=" review_advice != null and review_advice != ''  "><![CDATA[ AND A.REVIEW_ADVICE = #{review_advice} ]]></if>
		<if test=" review_id  != null "><![CDATA[ AND A.REVIEW_ID = #{review_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" hr_is_apss != null and hr_is_apss != ''  "><![CDATA[ AND A.HR_IS_APSS = #{hr_is_apss} ]]></if>
		<if test=" review_reply_user  != null "><![CDATA[ AND A.REVIEW_REPLY_USER = #{review_reply_user} ]]></if>
		<if test=" policy_cate != null and policy_cate != ''  "><![CDATA[ AND A.POLICY_CATE = #{policy_cate} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" review_app_date  != null  and  review_app_date  != ''  "><![CDATA[ AND A.REVIEW_APP_DATE = #{review_app_date} ]]></if>
		<if test=" advice_result != null and advice_result != ''  "><![CDATA[ AND A.ADVICE_RESULT = #{advice_result} ]]></if>
		<if test=" review_status  != null "><![CDATA[ AND A.REVIEW_STATUS = #{review_status} ]]></if>
		<if test=" hr_reply_time  != null  and  hr_reply_time  != ''  "><![CDATA[ AND A.HR_REPLY_TIME = #{hr_reply_time} ]]></if>
		<if test=" review_reply_time  != null  and  review_reply_time  != ''  "><![CDATA[ AND A.REVIEW_REPLY_TIME = #{review_reply_time} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" review_reason != null and review_reason != ''  "><![CDATA[ AND A.REVIEW_REASON = #{review_reason} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryUwPolicyReviewByReviewIdCondition">
		<if test=" review_id  != null "><![CDATA[ AND A.REVIEW_ID = #{review_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addUwPolicyReview"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO DEV_UW.T_UW_POLICY_REVIEW(
				UW_ID, PLUS_NOTE, REVIEW_APP_USER, PLUS_SIGN, HR_COMMENT, REVIEW_ADVICE, REVIEW_ID, 
				APPLY_CODE, ORGAN_CODE, INSERT_TIMESTAMP, HR_IS_APSS, REVIEW_REPLY_USER, UPDATE_BY, POLICY_CATE, 
				POLICY_ID, REVIEW_APP_DATE, ADVICE_RESULT, INSERT_TIME, REVIEW_STATUS, HR_REPLY_TIME, REVIEW_REPLY_TIME, 
				UPDATE_TIME, POLICY_CODE, REVIEW_REASON, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{uw_id, jdbcType=NUMERIC}, #{plus_note, jdbcType=VARCHAR} , #{review_app_user, jdbcType=NUMERIC} , #{plus_sign, jdbcType=NUMERIC} , #{hr_comment, jdbcType=NUMERIC} , #{review_advice, jdbcType=VARCHAR} , #{review_id, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{hr_is_apss, jdbcType=VARCHAR} , #{review_reply_user, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{policy_cate, jdbcType=VARCHAR} 
				, #{policy_id, jdbcType=NUMERIC} , #{review_app_date, jdbcType=DATE} , #{advice_result, jdbcType=VARCHAR} , SYSDATE , #{review_status, jdbcType=NUMERIC} , #{hr_reply_time, jdbcType=DATE} , #{review_reply_time, jdbcType=DATE} 
				, SYSDATE , #{policy_code, jdbcType=VARCHAR} , #{review_reason, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteUwPolicyReview" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_UW_POLICY_REVIEW WHERE REVIEW_ID = #{review_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateUwPolicyReview" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_POLICY_REVIEW ]]>
		<set>
		<trim suffixOverrides=",">
		    UW_ID = #{uw_id, jdbcType=NUMERIC} ,
			PLUS_NOTE = #{plus_note, jdbcType=VARCHAR} ,
		    REVIEW_APP_USER = #{review_app_user, jdbcType=NUMERIC} ,
		    PLUS_SIGN = #{plus_sign, jdbcType=NUMERIC} ,
		    HR_COMMENT = #{hr_comment, jdbcType=NUMERIC} ,
			REVIEW_ADVICE = #{review_advice, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
			HR_IS_APSS = #{hr_is_apss, jdbcType=VARCHAR} ,
		    REVIEW_REPLY_USER = #{review_reply_user, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			POLICY_CATE = #{policy_cate, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    REVIEW_APP_DATE = #{review_app_date, jdbcType=DATE} ,
			ADVICE_RESULT = #{advice_result, jdbcType=VARCHAR} ,
		    REVIEW_STATUS = #{review_status, jdbcType=NUMERIC} ,
		    HR_REPLY_TIME = #{hr_reply_time, jdbcType=DATE} ,
		    REVIEW_REPLY_TIME = #{review_reply_time, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			REVIEW_REASON = #{review_reason, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE REVIEW_ID = #{review_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findUwPolicyReviewByReviewId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PLUS_NOTE, A.REVIEW_APP_USER, A.PLUS_SIGN, A.HR_COMMENT, A.REVIEW_ADVICE, A.REVIEW_ID, 
			A.APPLY_CODE, A.ORGAN_CODE, A.HR_IS_APSS, A.REVIEW_REPLY_USER, A.POLICY_CATE, 
			A.POLICY_ID, A.REVIEW_APP_DATE, A.ADVICE_RESULT, A.REVIEW_STATUS, A.HR_REPLY_TIME, A.REVIEW_REPLY_TIME, 
			A.POLICY_CODE, A.REVIEW_REASON FROM DEV_UW.T_UW_POLICY_REVIEW A WHERE 1 = 1  ]]>
		<include refid="queryUwPolicyReviewByReviewIdCondition" />
		<![CDATA[ ORDER BY A.REVIEW_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapUwPolicyReview" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PLUS_NOTE, A.REVIEW_APP_USER, A.PLUS_SIGN, A.HR_COMMENT, A.REVIEW_ADVICE, A.REVIEW_ID, 
			A.APPLY_CODE, A.ORGAN_CODE, A.HR_IS_APSS, A.REVIEW_REPLY_USER, A.POLICY_CATE, 
			A.POLICY_ID, A.REVIEW_APP_DATE, A.ADVICE_RESULT, A.REVIEW_STATUS, A.HR_REPLY_TIME, A.REVIEW_REPLY_TIME, 
			A.POLICY_CODE, A.REVIEW_REASON FROM DEV_UW.T_UW_POLICY_REVIEW A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.REVIEW_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllUwPolicyReview" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PLUS_NOTE, A.REVIEW_APP_USER, A.PLUS_SIGN, A.HR_COMMENT, A.REVIEW_ADVICE, A.REVIEW_ID, 
			A.APPLY_CODE, A.ORGAN_CODE, A.HR_IS_APSS, A.REVIEW_REPLY_USER, A.POLICY_CATE, 
			A.POLICY_ID, A.REVIEW_APP_DATE, A.ADVICE_RESULT, A.REVIEW_STATUS, A.HR_REPLY_TIME, A.REVIEW_REPLY_TIME, 
			A.POLICY_CODE, A.REVIEW_REASON FROM DEV_UW.T_UW_POLICY_REVIEW A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.REVIEW_ID ]]> 
	</select>
<!-- 查询单个操作 -->
	<select id="uw_findUwPolicyReviewByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PLUS_NOTE, A.REVIEW_APP_USER, A.PLUS_SIGN, A.HR_COMMENT, A.REVIEW_ADVICE, A.REVIEW_ID, 
			A.APPLY_CODE, A.ORGAN_CODE, A.HR_IS_APSS, A.REVIEW_REPLY_USER, A.POLICY_CATE, 
			A.POLICY_ID, A.REVIEW_APP_DATE, A.ADVICE_RESULT, A.REVIEW_STATUS, A.HR_REPLY_TIME, A.REVIEW_REPLY_TIME, 
			A.POLICY_CODE, A.REVIEW_REASON,(select user_name from t_udmp_user where user_id=A.REVIEW_APP_USER) as user_name FROM DEV_UW.T_UW_POLICY_REVIEW A where 1=1 ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<![CDATA[ ORDER BY A.REVIEW_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findUwPolicyReviewTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_UW_POLICY_REVIEW A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="queryUwPolicyReviewForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UW_ID, B.PLUS_NOTE, B.REVIEW_APP_USER, B.PLUS_SIGN, B.HR_COMMENT, B.REVIEW_ADVICE, B.REVIEW_ID, 
			B.APPLY_CODE, B.ORGAN_CODE, B.HR_IS_APSS, B.REVIEW_REPLY_USER, B.POLICY_CATE, 
			B.POLICY_ID, B.REVIEW_APP_DATE, B.ADVICE_RESULT, B.REVIEW_STATUS, B.HR_REPLY_TIME, B.REVIEW_REPLY_TIME, 
			B.POLICY_CODE, B.REVIEW_REASON FROM (
					SELECT ROWNUM RN, A.UW_ID, A.PLUS_NOTE, A.REVIEW_APP_USER, A.PLUS_SIGN, A.HR_COMMENT, A.REVIEW_ADVICE, A.REVIEW_ID, 
			A.APPLY_CODE, A.ORGAN_CODE, A.HR_IS_APSS, A.REVIEW_REPLY_USER, A.POLICY_CATE, 
			A.POLICY_ID, A.REVIEW_APP_DATE, A.ADVICE_RESULT, A.REVIEW_STATUS, A.HR_REPLY_TIME, A.REVIEW_REPLY_TIME, 
			A.POLICY_CODE, A.REVIEW_REASON FROM DEV_UW.T_UW_POLICY_REVIEW A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.REVIEW_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!--by zhaoyoan_wb 根据投保单号查询所有操作 -->
	<select id="UW_findAllUwPolicyReviewByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.APPLY_CODE,A.POLICY_CODE,A.ADVICE_RESULT,A.REVIEW_APP_USER,A.INSERT_TIME,
			A.UPDATE_TIME,A.REVIEW_REPLY_TIME,A.REVIEW_ADVICE,A.REVIEW_REPLY_USER 
			FROM DEV_UW.T_UW_POLICY_REVIEW A
			WHERE ROWNUM <=  1000 
		]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ 
			ORDER BY A.REVIEW_ID 
		]]> 
	</select>
</mapper>
