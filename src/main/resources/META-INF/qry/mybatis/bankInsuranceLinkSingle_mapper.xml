<?xml version="1.0" encoding="UTF-8"?> 
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cip.dao.impl">
 
<!-- 生效保单查询根据生效日期查询 -->
	<select id="CIP_queryInsuredTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map"> 
		<![CDATA[  
		select count(1)
  			from (select distinct m.policy_code as bill_no,
            	'1' as policy_type,
                        m.service_bank as bank_code,
                        m.apply_code as bill_num,
                        tbp.product_category as main_pro_flg,
                        ( select tbb.bank_branch_name from dev_pas.t_bank_branch tbb where tbb.bank_branch_code=m.service_bank  and tbb.BANK_BRANCH_TYPE = '00' AND LENGTH(tbb.BANK_BRANCH_CODE) = 2) as bankname,
                        tbp.product_name_sys as insur_code,
                        cp.amount as coverage,
                        cp.std_prem_af as premium,
                        bk.bank_branch_name as branchname,
                        bp.master_busi_item_id,
                        (select tuo.organ_name
                        from dev_pas.t_udmp_org tuo
                     where tuo.organ_code = m.organ_code) as organnameman,
                        (select tuo.organ_name
                           from dev_pas.t_udmp_org tuo
                          where tuo.organ_code = m.organ_code) as organname,
                        cp.charge_year as chargeyear,
                        cp.amount as amount,
                        (select c.customer_name
                           from dev_pas.t_customer c
                          where c.customer_id = il.customer_id) customername,
                        m.sale_agent_code as agentcode,
                        (select ta.agent_name
                           from dev_pas.t_agent ta
                          where ta.agent_code = m.sale_agent_code) as agentname
          from dev_pas.t_contract_master m 
          left join dev_pas.t_contract_busi_prod bp
            on m.apply_code = bp.apply_code
          left join dev_pas.t_contract_product cp
            on bp.apply_code = cp.apply_code and cp.busi_item_id=bp.busi_item_id
          left join dev_pas.t_bank_branch bk
            on m.service_bank_branch = bk.bank_branch_code
          left join dev_pas.t_insured_list il
            on il.apply_code = m.apply_code
          left join dev_pds.t_business_product  tbp 
          	on tbp.product_code_sys = bp.busi_prod_code  and tbp.product_name_sys is not null
         where 1=1    
         	and m.submit_channel = '1' and m.liability_state = '1'
		   ]]>
		<if test=" start_date  != null and  start_date  !='' "><![CDATA[ AND m.validate_date = to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>
		<if test=" bank_code  != null and bank_code  !='' "><![CDATA[ AND service_bank  = #{bank_code}]]></if>
		<if test=" orgcode  != null and orgcode  !='' "><![CDATA[ AND m.organ_code like #{orgcode}||'%']]></if>
		<![CDATA[ ) ]]>
	</select>
	<!-- 分页查询 -->
	<select id="CIP_queryInsuredForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT * FROM 
                     (SELECT ROWNUM RN,N.* FROM
                     (select
distinct m.policy_code as bill_no,
         '1' as policy_type,
         m.service_bank as bank_code,
         m.apply_code as bill_num,
         m.insert_timestamp as apply_date,
         tbp.product_category as main_pro_flg,
         ( select tbb.bank_branch_name from dev_pas.t_bank_branch tbb where tbb.bank_branch_code=m.service_bank  and tbb.BANK_BRANCH_TYPE = '00' AND LENGTH(tbb.BANK_BRANCH_CODE) = 2) as bankname,
          tbp.product_name_sys as insur_code,
         cp.amount as coverage,
         cp.std_prem_af as premium,
         bk.bank_branch_name as branchname,
         bp.master_busi_item_id,
         (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organname,
           
           (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organnameman,
           
         cp.charge_year as chargeyear,
         cp.amount as amount,
         (select c.customer_name
            from dev_pas.t_customer c
           where c.customer_id = il.customer_id) customername,
         m.sale_agent_code as agentcode,
         (select ta.agent_name
            from dev_pas.t_agent ta
           where ta.agent_code = m.sale_agent_code) as agentname

  from dev_pas.t_contract_master m

  left join dev_pas.t_contract_busi_prod bp
    on m.apply_code = bp.apply_code
  left join dev_pas.t_contract_product cp
    on bp.apply_code = cp.apply_code  and cp.busi_item_id=bp.busi_item_id
  left join dev_pas.t_bank_branch bk
    on m.service_bank_branch = bk.bank_branch_code
  left join dev_pas.t_insured_list il
    on il.apply_code = m.apply_code 
  left join dev_pds.t_business_product  tbp 
    on tbp.product_code_sys = bp.busi_prod_code and tbp.product_name_sys is not null
    where 1=1    
    	and m.submit_channel = '1' 
    	and m.liability_state = '1'
    	
      ]]>
		<if test=" start_date  != null and  start_date  !='' "><![CDATA[ AND m.validate_date = to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>
		<if test=" bank_code  != null and bank_code  !='' "><![CDATA[ AND service_bank = #{bank_code}]]></if>
	    <if test=" orgcode  != null and orgcode  !='' "><![CDATA[ AND  m.organ_code  like #{orgcode}||'%']]></if>
	    <![CDATA[  order by apply_date desc ) N)
                WHERE RN > (#{pageno}-1)*#{pagesize} 
                  AND RN <= #{pageno}*#{pagesize} ]]>
	</select>
 <!-- 生效保单查询 -->
 
 
 
 <!-- 犹豫期内退保 根据保全申请日期查询 -->
	<select id="CIP_queryHpTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map"> 
		<![CDATA[  
		
		select count(1)
  from (
        
        select distinct m.policy_code as bill_no,
                         '2' as policy_type,
                         m.service_bank as bank_code,
                         m.apply_code as bill_num,
                          tbp.product_category as main_pro_flg,
                         ( select tbb.bank_branch_name from dev_pas.t_bank_branch tbb where tbb.bank_branch_code=m.service_bank  and tbb.BANK_BRANCH_TYPE = '00' AND LENGTH(tbb.BANK_BRANCH_CODE) = 2) as bankname,
                         tbp.product_name_sys as insur_code,
                         cp.amount as coverage,
                         c.surrender_amount as premium,
                         bk.bank_branch_name as branchname,
                         bp.master_busi_item_id,
                         (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organnameman,
                         
                         (select tuo.organ_name
                            from dev_pas.t_udmp_org tuo
                           where tuo.organ_code = m.organ_code) as organname,
                         cp.charge_year as chargeyear,
                         cp.amount as amount,
                         (select c.customer_name
                            from dev_pas.t_customer c
                           where c.customer_id = il.customer_id) customername,
                         m.sale_agent_code as agentcode,
                         (select ta.agent_name
                            from dev_pas.t_agent ta
                           where ta.agent_code = m.sale_agent_code) as agentname
        
          from dev_pas.t_contract_master m
          left join dev_pas.t_contract_busi_prod bp
            on m.apply_code = bp.apply_code
          left join dev_pas.t_surrender c
            on bp.busi_item_id = c.busi_item_id
          left join dev_pas.t_contract_product cp
            on bp.apply_code = cp.apply_code and cp.busi_item_id=bp.busi_item_id
          left join dev_pas.t_bank_branch bk
            on m.service_bank_branch = bk.bank_branch_code
          left join dev_pas.t_insured_list il
            on il.apply_code = m.apply_code
          left join dev_pds.t_business_product   tbp 
          	on tbp.product_code_sys = bp.busi_prod_code and tbp.product_name_sys is not null
         left join dev_pas.t_cs_application tca
            on tca.change_id = c.change_id
         where 1=1  
          	and c.hesitate_flag = '1'
			and m.submit_channel = '1'
			and m.liability_state='3'
   			and m.end_cause='03'
		   ]]>
		   
		<if test=" start_date  != null and  start_date  !='' "><![CDATA[ AND tca.apply_time = to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>  
		<if test=" bank_code  != null and bank_code  !='' "><![CDATA[ AND service_bank  = #{bank_code}]]></if>
	<if test=" orgcode  != null and orgcode  !='' "><![CDATA[ AND m.organ_code  like #{orgcode}||'%']]></if>
	<![CDATA[ ) ]]>
	</select>
	<!-- 分页查询 -->
	<select id="CIP_queryHpForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT * FROM 
                     (SELECT ROWNUM RN,N.* FROM
                     (select
distinct m.policy_code as bill_no,
         '2' as policy_type,
         m.service_bank as bank_code,
         m.apply_code as bill_num,
         m.insert_timestamp as apply_date,
          tbp.product_category as main_pro_flg,
         ( select tbb.bank_branch_name from dev_pas.t_bank_branch tbb where tbb.bank_branch_code=m.service_bank  and tbb.BANK_BRANCH_TYPE = '00' AND LENGTH(tbb.BANK_BRANCH_CODE) = 2) as bankname,
         tbp.product_name_sys as insur_code,
         cp.amount as coverage,
         c.surrender_amount as premium,
         bk.bank_branch_name as branchname,
         bp.master_busi_item_id,
         (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organnameman,
         
         (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organname,
         cp.charge_year as chargeyear,
         cp.amount as amount,
         (select c.customer_name
            from dev_pas.t_customer c
           where c.customer_id = il.customer_id) customername,
         m.sale_agent_code as agentcode,
         (select ta.agent_name
            from dev_pas.t_agent ta
           where ta.agent_code = m.sale_agent_code) as agentname
  	from dev_pas.t_contract_master m
   		left join dev_pas.t_contract_busi_prod bp
      		on m.apply_code = bp.apply_code
    	left join dev_pas.t_surrender c
      		on bp.busi_item_id = c.busi_item_id
   		left join dev_pas.t_contract_product cp
      		on bp.apply_code = cp.apply_code and cp.busi_item_id=bp.busi_item_id
    	left join dev_pas.t_bank_branch bk
      		on m.service_bank_branch = bk.bank_branch_code
    	left join dev_pas.t_insured_list il
      		on il.apply_code = m.apply_code
    	left join dev_pds.t_business_product  tbp 
    		on tbp.product_code_sys = bp.busi_prod_code and tbp.product_name_sys is not null
    	left join dev_pas.t_cs_application tca
            on tca.change_id = c.change_id	
         where 1=1  
     		and c.hesitate_flag = '1'
     		and m.submit_channel = '1'
     		and m.liability_state='3'
   			and m.end_cause='03'
      ]]>
		<if test=" start_date  != null and  start_date  !='' "><![CDATA[ AND tca.apply_time = to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>  
		<if test=" bank_code  != null and bank_code  !='' "><![CDATA[ AND service_bank = #{bank_code}]]></if>
	    <if test=" orgcode  != null and orgcode  !='' "><![CDATA[ AND m.organ_code like #{orgcode}||'%']]></if>
	    <![CDATA[  order by apply_date desc   ) N)
                WHERE RN > (#{pageno}-1)*#{pagesize} 
                  AND RN <= #{pageno}*#{pagesize} ]]>
	</select>
 <!-- 犹退-->
 
 
 
 <!-- 出单情况查询承保日期为当天或某一天 -->
	<select id="CIP_queryInsuredHpTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map"> 
		<![CDATA[  
		select  count(1)
  from (select distinct m.policy_code as bill_no,
        '0' as policy_type,
         m.service_bank as bank_code,
         m.issue_date as apply_date,
         m.validate_date,
         m.apply_code as bill_num,
          tbp.product_category as main_pro_flg,
         ( select tbb.bank_branch_name from dev_pas.t_bank_branch tbb where tbb.bank_branch_code=m.service_bank  and tbb.BANK_BRANCH_TYPE = '00' AND LENGTH(tbb.BANK_BRANCH_CODE) = 2) as bankname,
         tbp.product_name_sys as insur_code,
         cp.amount as coverage,
         cp.std_prem_af as premium,
         bk.bank_branch_name as branchname,
         bp.master_busi_item_id,
         (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organnameman,
         
         (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organname,
         cp.charge_year as chargeyear,
         cp.amount as amount,
         (select c.customer_name
            from dev_pas.t_customer c
           where c.customer_id = il.customer_id) customername,
         m.sale_agent_code as agentcode,
         (select ta.agent_name
            from dev_pas.t_agent ta
           where ta.agent_code = m.sale_agent_code) as agentname 
           
           from dev_pas.t_contract_master m
    left join dev_pas.t_contract_busi_prod bp
      on m.apply_code = bp.apply_code
    left join dev_pas.t_surrender c
      on bp.busi_item_id = c.busi_item_id
    left join dev_pas.t_contract_product cp
      on bp.apply_code = cp.apply_code and cp.busi_item_id=bp.busi_item_id
    left join dev_pas.t_bank_branch bk
      on m.service_bank_branch = bk.bank_branch_code
    left join dev_pas.t_insured_list il
      on il.apply_code = m.apply_code
         left join dev_pds.t_business_product tbp 
          	on tbp.product_code_sys = bp.busi_prod_code and tbp.product_name_sys is not null
         where 1=1  and m.submit_channel = '1' and m.liability_state = '1'  ]]>
         <if test=" start_date  != null and  start_date  !='' "><![CDATA[ AND m.issue_date = to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>
         <if test=" bank_code  != null and bank_code  !='' "><![CDATA[ AND service_bank  = #{bank_code}]]></if>
         <if test=" orgcode  != null and orgcode  !='' "><![CDATA[ AND m.organ_code  like #{orgcode}||'%']]></if>
	 <![CDATA[)]]>
	
	</select>
	<!-- 分页查询 -->
	<select id="CIP_queryInsuredHpForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[  SELECT * FROM 
                     (SELECT ROWNUM RN,N.* FROM
                     (select
        distinct m.policy_code as bill_no,
        '0' as policy_type,
         m.service_bank as bank_code,
         m.issue_date as apply_date,
         m.validate_date,
         m.apply_code as bill_num,
         m.insert_timestamp as apply_date1,
          tbp.product_category as main_pro_flg,
        ( select tbb.bank_branch_name from dev_pas.t_bank_branch tbb where tbb.bank_branch_code=m.service_bank  and tbb.BANK_BRANCH_TYPE = '00' AND LENGTH(tbb.BANK_BRANCH_CODE) = 2) as bankname,
         tbp.product_name_sys as insur_code,
         cp.amount as coverage,
         cp.std_prem_af as premium,
         bk.bank_branch_name as branchname,
         bp.master_busi_item_id,
         (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organnameman,
         
         (select tuo.organ_name
            from dev_pas.t_udmp_org tuo
           where tuo.organ_code = m.organ_code) as organname,
         cp.charge_year as chargeyear,
         cp.amount as amount,
         (select c.customer_name
            from dev_pas.t_customer c
           where c.customer_id = il.customer_id) customername,
         m.sale_agent_code as agentcode,
         (select ta.agent_name
            from dev_pas.t_agent ta
           where ta.agent_code = m.sale_agent_code) as agentname

  from dev_pas.t_contract_master m
        
          left join dev_pas.t_contract_busi_prod bp
            on m.apply_code = bp.apply_code
          left join dev_pas.t_contract_product cp
            on bp.apply_code = cp.apply_code and cp.busi_item_id=bp.busi_item_id
          left join dev_pas.t_bank_branch bk
            on m.service_bank_branch = bk.bank_branch_code
          left join dev_pas.t_insured_list il
            on il.apply_code = m.apply_code
        
         left join dev_pds.t_business_product tbp 
          	on tbp.product_code_sys = bp.busi_prod_code and tbp.product_name_sys is not null
         
         where 1=1   and m.submit_channel = '1' and m.liability_state = '1']]>
         <if test=" start_date  != null and  start_date  !='' "><![CDATA[ AND m.issue_date = to_date(to_char(#{start_date},'yyyy-MM-dd'),'yyyy-MM-dd')]]></if>
         <if test=" bank_code  != null and bank_code  !='' "><![CDATA[ AND service_bank  = #{bank_code}]]></if>
         <if test=" orgcode  != null and orgcode  !='' "><![CDATA[ AND m.organ_code like #{orgcode}||'%']]></if>
	    <![CDATA[  order by apply_date1 desc  ) N)
                WHERE RN > (#{pageno}-1)*#{pagesize} 
                  AND RN <= #{pageno}*#{pagesize} ]]>
	</select>
 <!-- 出单情况查询 -->
 
	
</mapper>