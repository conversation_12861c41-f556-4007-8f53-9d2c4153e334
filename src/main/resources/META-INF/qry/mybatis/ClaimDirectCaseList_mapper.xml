<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="CLM_ClaimDirectCaseListPO">
<!-- 查询理赔直连问题件选项 -->
	<select  id="findClaimDirectOptionList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ 
			SELECT  A.CODE,A.NAME AS PROBLEMOPTION FROM DEV_CLM.T_MEMO_ITEM A ]]>
			<if test=" directProblem  != null "><![CDATA[ WHERE A.CODE LIKE CONCAT(#{directProblem},'%') ]]></if> 
	</select>
<!-- 分页查询理赔直连案件清单 -->	
	<select  id="queryClaimDirectCaseListForPage" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[ 
	SELECT M.* FROM(
         SELECT L.*,ROWNUM RN FROM(
		SELECT X.CASE_NO ,
			   X.ORGAN_CODE,
			   X.ORGAN_NAME,
			   X.HOSPITAL_NAME,
			   Z.TREAT_TYPE,
			   Z.BILLCOUNT,
       	  	   X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       LISTAGG(AM.MEMO_OPTION,',')WITHIN GROUP(ORDER BY AM.MEMO_OPTION)MEMO_OPTION,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.CHECK_END_TIME,
		       X.ACTUAL_PAY FROM
		(SELECT DISTINCT A.CASE_NO,
				A.CASE_ID,A.ORGAN_CODE,B.ORGAN_NAME,C.HOSPITAL_NAME,D.DIRECT_APPLY_TIME,D.DIRECT_BACK_TIME,
		        (CASE WHEN D.CHECK_RESULT = 0 THEN '无效'
		              WHEN D.CHECK_RESULT = 1 THEN '有效' END)CHECK_RESULT,
		        (CASE WHEN D.DIRECT_APPLY_FLAG = 0 THEN '成功'
		              WHEN D.DIRECT_APPLY_FLAG = 1 THEN '失败' END)DIRECT_APPLY_FLAG,
		        D.DIRECT_FAIL_REASON,
		        G.DIRECT_CONN_BUS,
		        Y.CHECK_END_TIME,
		        A.ACTUAL_PAY,
		        (SELECT UU.USER_NAME ||'-'|| UU.REAL_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = Y.OPERATOR_CODE)OPERATOR_CODE
		        FROM 
		         DEV_CLM.T_CLAIM_CASE A,
		         DEV_CLM.T_UDMP_ORG_REL B,
		         DEV_CLM.T_HOSPITAL C ,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_APPLY D,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_AUTH G ,
		         DEV_CLM.T_CLAIM_DIRECT_CHECK_CASE Y ]]>
		        <if test=" memo_type  != null and memo_type !='' "><![CDATA[ ,DEV_CLM.T_CLAIM_MEMO EM ]]></if>
		       
		         WHERE A.ORGAN_CODE = B.ORGAN_CODE
		               AND A.CURE_HOSPITAL = C.HOSPITAL_CODE
		        	   AND A.CASE_ID  = D.CASE_ID
		               AND G.CASE_ID = D.CASE_ID
		               AND Y.CASE_ID = D.CASE_ID
		               AND A.CASE_STATUS != '99'
		          <if test=" memo_type  != null and memo_type !='' ">
		          		<![CDATA[ AND EM.CONN_APPLY_ID = D.LIST_ID AND EM.MEMO_TYPE = #{memo_type}]]></if>
		          <if test=" memo_option  != null and memo_option !='' ">
		          		<![CDATA[ AND EM.MEMO_OPTION = #{memo_option}]]></if>
		          <if test=" organ_code  != null and organ_code !='' ">
		          		<![CDATA[ AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		          <if test=" directConnBus  != null and directConnBus !='' ">
		          		<![CDATA[ AND G.DIRECT_CONN_BUS = #{directConnBus}]]></if>
		          <if test=" startTime  != null and startTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME) >=TRUNC( #{startTime})]]></if>
		          <if test=" endTime  != null and endTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME) <=TRUNC( #{endTime})]]></if>
	<![CDATA[    ORDER BY A.CASE_ID)X
		LEFT JOIN
		         (SELECT A.CASE_ID,
		         (CASE WHEN A.TREAT_TYPE = '0' THEN '门诊' 
		               WHEN A.TREAT_TYPE = '1' THEN '住院' END)TREAT_TYPE,
		         COUNT(BILL_NO)BILLCOUNT FROM DEV_CLM.T_CLAIM_BILL A GROUP BY A.TREAT_TYPE ,A.CASE_ID ORDER BY A.CASE_ID)Z
		ON X.CASE_ID = Z.CASE_ID      
		LEFT JOIN 
		          (SELECT DISTINCT ME.CASE_ID,MI.NAME AS MEMO_OPTION
		           FROM DEV_CLM.T_MEMO_ITEM MI ,DEV_CLM.T_CLAIM_MEMO ME
		           WHERE MI.CODE = MEMO_OPTION AND MI.CODE LIKE '12%'
		          )AM
		ON X.CASE_ID = AM.CASE_ID
		GROUP BY  X.CASE_NO ,X.ORGAN_CODE,X.ORGAN_NAME,X.HOSPITAL_NAME,Z.TREAT_TYPE,Z.BILLCOUNT,
		       X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.CHECK_END_TIME,
		       X.ACTUAL_PAY ]]>
	<![CDATA[ ) l WHERE ROWNUM <= #{LESS_NUM}) M WHERE RN > #{GREATER_NUM}]]>
	</select>
	
	<select id="queryClaimDirectCaseListForPageTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[SELECT COUNT(1) from(
					SELECT X.CASE_NO ,
			   X.ORGAN_CODE,
			   X.ORGAN_NAME,
			   X.HOSPITAL_NAME,
			   Z.TREAT_TYPE,
			   Z.BILLCOUNT,
       	  	   X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       LISTAGG(AM.MEMO_OPTION,',')WITHIN GROUP(ORDER BY AM.MEMO_OPTION)MEMO_OPTION,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.CHECK_END_TIME,
		       X.ACTUAL_PAY FROM
		(SELECT DISTINCT A.CASE_NO,
				A.CASE_ID,A.ORGAN_CODE,B.ORGAN_NAME,C.HOSPITAL_NAME,D.DIRECT_APPLY_TIME,D.DIRECT_BACK_TIME,
		        (CASE WHEN D.CHECK_RESULT = 0 THEN '无效'
		              WHEN D.CHECK_RESULT = 1 THEN '有效' END)CHECK_RESULT,
		        (CASE WHEN D.DIRECT_APPLY_FLAG = 0 THEN '成功'
		              WHEN D.DIRECT_APPLY_FLAG = 1 THEN '失败' END)DIRECT_APPLY_FLAG,
		        D.DIRECT_FAIL_REASON,
		        (CASE WHEN G.DIRECT_CONN_BUS = 1 THEN '保医通'
                	  WHEN G.DIRECT_CONN_BUS = 2 THEN '保信健康宝'
                	  WHEN G.DIRECT_CONN_BUS = 3 THEN '易联众' END)DIRECT_CONN_BUS,
		        Y.CHECK_END_TIME,
		        A.ACTUAL_PAY,
		        (SELECT UU.USER_NAME ||'-'|| UU.REAL_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = Y.OPERATOR_CODE)OPERATOR_CODE
		        FROM 
		         DEV_CLM.T_CLAIM_CASE A,
		         DEV_CLM.T_UDMP_ORG_REL B,
		         DEV_CLM.T_HOSPITAL C ,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_APPLY D,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_AUTH G ,
		         DEV_CLM.T_CLAIM_DIRECT_CHECK_CASE Y ]]>
		       <if test=" memo_type  != null and memo_type !='' "><![CDATA[ ,DEV_CLM.T_CLAIM_MEMO EM ]]></if>
		       
		         WHERE A.ORGAN_CODE = B.ORGAN_CODE
		               AND A.CURE_HOSPITAL = C.HOSPITAL_CODE
		        	   AND A.CASE_ID  = D.CASE_ID
		               AND G.CASE_ID = D.CASE_ID
		               AND Y.CASE_ID = D.CASE_ID
		               AND A.CASE_STATUS != '99'
		          <if test=" memo_type  != null and memo_type !='' ">
		          		<![CDATA[ AND EM.CONN_APPLY_ID = D.LIST_ID AND EM.MEMO_TYPE = #{memo_type}]]></if>
		          <if test=" memo_option  != null and memo_option !='' ">
		          		<![CDATA[ AND EM.MEMO_OPTION = #{memo_option}]]></if>
		          <if test=" organ_code  != null and organ_code !='' ">
		          		<![CDATA[ AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		          <if test=" directConnBus  != null and directConnBus !='' ">
		          		<![CDATA[ AND G.DIRECT_CONN_BUS = #{directConnBus}]]></if>
		          <if test=" startTime  != null and startTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME) >= TRUNC(#{startTime})]]></if>
		          <if test=" endTime  != null and endTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME) <= TRUNC(#{endTime})]]></if>
	<![CDATA[    ORDER BY A.CASE_ID)X
		LEFT JOIN
		         (SELECT A.CASE_ID,
		         (CASE WHEN A.TREAT_TYPE = '0' THEN '门诊' 
		               WHEN A.TREAT_TYPE = '1' THEN '住院' END)TREAT_TYPE,
		         COUNT(BILL_NO)BILLCOUNT FROM DEV_CLM.T_CLAIM_BILL A GROUP BY A.TREAT_TYPE ,A.CASE_ID ORDER BY A.CASE_ID)Z
		ON X.CASE_ID = Z.CASE_ID      
		LEFT JOIN 
		          (SELECT DISTINCT ME.CASE_ID,MI.NAME AS MEMO_OPTION
		           FROM DEV_CLM.T_MEMO_ITEM MI ,DEV_CLM.T_CLAIM_MEMO ME
		           WHERE MI.CODE = MEMO_OPTION AND MI.CODE LIKE '12%'
		          )AM
		ON X.CASE_ID = AM.CASE_ID
		GROUP BY  X.CASE_NO ,X.ORGAN_CODE,X.ORGAN_NAME,X.HOSPITAL_NAME,Z.TREAT_TYPE,Z.BILLCOUNT,
		       X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.CHECK_END_TIME,
		       X.ACTUAL_PAY ]]>
		       <![CDATA[ )]]>
	</select>
	
	<!-- Execl分页查询理赔直连案件清单 -->
		<select id="queryClaimDirectCaseListForPageExcel" parameterType="java.util.Map" resultType="java.util.Map">
			<![CDATA[ 
	SELECT M.* FROM(
         SELECT L.*,ROWNUM RN FROM(
		SELECT X.CASE_NO ,
			   X.ORGAN_CODE,
			   X.ORGAN_NAME,
			   X.HOSPITAL_NAME,
			   Z.TREAT_TYPE,
			   Z.BILLCOUNT,
       	  	   X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       LISTAGG(AM.MEMO_OPTION,',')WITHIN GROUP(ORDER BY AM.MEMO_OPTION)MEMO_OPTION,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.CHECK_END_TIME,
		       X.ACTUAL_PAY FROM
		(SELECT DISTINCT A.CASE_NO,
				A.CASE_ID,A.ORGAN_CODE,B.ORGAN_NAME,C.HOSPITAL_NAME,D.DIRECT_APPLY_TIME,D.DIRECT_BACK_TIME,
		        (CASE WHEN D.CHECK_RESULT = 0 THEN '无效'
		              WHEN D.CHECK_RESULT = 1 THEN '有效' END)CHECK_RESULT,
		        (CASE WHEN D.DIRECT_APPLY_FLAG = 0 THEN '成功'
		              WHEN D.DIRECT_APPLY_FLAG = 1 THEN '失败' END)DIRECT_APPLY_FLAG,
		        D.DIRECT_FAIL_REASON,
		        (CASE WHEN G.DIRECT_CONN_BUS = 1 THEN '保医通'
                      WHEN G.DIRECT_CONN_BUS = 2 THEN '保信健康宝'
                      WHEN G.DIRECT_CONN_BUS = 3 THEN '易联众'
                      WHEN G.DIRECT_CONN_BUS = 4 THEN '中银保信苏州' END)DIRECT_CONN_BUS,
		        Y.CHECK_END_TIME,
		        A.ACTUAL_PAY,
		        (SELECT UU.USER_NAME ||'-'|| UU.REAL_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = Y.OPERATOR_CODE)OPERATOR_CODE
		        FROM 
		         DEV_CLM.T_CLAIM_CASE A,
		         DEV_CLM.T_UDMP_ORG_REL B,
		         DEV_CLM.T_HOSPITAL C ,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_APPLY D,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_AUTH G ,
		         DEV_CLM.T_CLAIM_DIRECT_CHECK_CASE Y ]]>
		        <if test=" memo_type  != null and memo_type !='' "><![CDATA[ ,DEV_CLM.T_CLAIM_MEMO EM ]]></if>
		       
		         WHERE A.ORGAN_CODE = B.ORGAN_CODE
		               AND A.CURE_HOSPITAL = C.HOSPITAL_CODE
		        	   AND A.CASE_ID  = D.CASE_ID
		               AND G.CASE_ID = D.CASE_ID
		               AND Y.CASE_ID = D.CASE_ID
		               AND A.CASE_STATUS != '99'
		          <if test=" memo_type  != null and memo_type !='' ">
		          		<![CDATA[ AND EM.CONN_APPLY_ID = D.LIST_ID AND EM.MEMO_TYPE = #{memo_type}]]></if>
		          <if test=" memo_option  != null and memo_option !='' ">
		          		<![CDATA[ AND EM.MEMO_OPTION = #{memo_option}]]></if>
		          <if test=" organ_code  != null and organ_code !='' ">
		          		<![CDATA[ AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		          <if test=" directConnBus  != null and directConnBus !='' ">
		          		<![CDATA[ AND G.DIRECT_CONN_BUS = #{directConnBus}]]></if>
		          <if test=" startTime  != null and startTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME) >=to_date(#{startTime},'yyyy-mm-dd')]]></if>
		          <if test=" endTime  != null and endTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME) <=to_date(#{endTime},'yyyy-mm-dd')]]></if>
	<![CDATA[    ORDER BY A.CASE_ID)X
		LEFT JOIN
		         (SELECT A.CASE_ID,
		         (CASE WHEN A.TREAT_TYPE = '0' THEN '门诊' 
		               WHEN A.TREAT_TYPE = '1' THEN '住院' END)TREAT_TYPE,
		         COUNT(BILL_NO)BILLCOUNT FROM DEV_CLM.T_CLAIM_BILL A GROUP BY A.TREAT_TYPE ,A.CASE_ID ORDER BY A.CASE_ID)Z
		ON X.CASE_ID = Z.CASE_ID      
		LEFT JOIN 
		          (SELECT DISTINCT ME.CASE_ID,MI.NAME AS MEMO_OPTION
		           FROM DEV_CLM.T_MEMO_ITEM MI ,DEV_CLM.T_CLAIM_MEMO ME
		           WHERE MI.CODE = MEMO_OPTION AND MI.CODE LIKE '12%'
		          )AM
		ON X.CASE_ID = AM.CASE_ID
		GROUP BY  X.CASE_NO ,X.ORGAN_CODE,X.ORGAN_NAME,X.HOSPITAL_NAME,Z.TREAT_TYPE,Z.BILLCOUNT,
		       X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.CHECK_END_TIME,
		       X.ACTUAL_PAY ]]>
	<![CDATA[ ) l WHERE ROWNUM <= #{LESS_NUM}) M WHERE RN > #{GREATER_NUM}]]>
	</select>
	<select id="queryClaimDirectCaseListForPageTotalExcel"  resultType="java.lang.Integer" parameterType="java.util.Map" >
				<![CDATA[SELECT COUNT(1) from(
					SELECT X.CASE_NO ,
			   X.ORGAN_CODE,
			   X.ORGAN_NAME,
			   X.HOSPITAL_NAME,
			   Z.TREAT_TYPE,
			   Z.BILLCOUNT,
       	  	   X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       LISTAGG(AM.MEMO_OPTION,',')WITHIN GROUP(ORDER BY AM.MEMO_OPTION)MEMO_OPTION,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.CHECK_END_TIME,
		       X.ACTUAL_PAY FROM
		(SELECT DISTINCT A.CASE_NO,
				A.CASE_ID,A.ORGAN_CODE,B.ORGAN_NAME,C.HOSPITAL_NAME,D.DIRECT_APPLY_TIME,D.DIRECT_BACK_TIME,
		        (CASE WHEN D.CHECK_RESULT = 0 THEN '无效'
		              WHEN D.CHECK_RESULT = 1 THEN '有效' END)CHECK_RESULT,
		        (CASE WHEN D.DIRECT_APPLY_FLAG = 0 THEN '成功'
		              WHEN D.DIRECT_APPLY_FLAG = 1 THEN '失败' END)DIRECT_APPLY_FLAG,
		        D.DIRECT_FAIL_REASON,
		        (CASE WHEN G.DIRECT_CONN_BUS = 1 THEN '保医通'
                	  WHEN G.DIRECT_CONN_BUS = 2 THEN '保信健康宝'
                	  WHEN G.DIRECT_CONN_BUS = 3 THEN '易联众'
                	  WHEN G.DIRECT_CONN_BUS = 4 THEN '中银保信苏州' END)DIRECT_CONN_BUS,
		        Y.CHECK_END_TIME,
		        A.ACTUAL_PAY,
		        (SELECT UU.USER_NAME ||'-'|| UU.REAL_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = Y.OPERATOR_CODE)OPERATOR_CODE
		        FROM 
		         DEV_CLM.T_CLAIM_CASE A,
		         DEV_CLM.T_UDMP_ORG_REL B,
		         DEV_CLM.T_HOSPITAL C ,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_APPLY D,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_AUTH G ,
		         DEV_CLM.T_CLAIM_DIRECT_CHECK_CASE Y ]]>
		       <if test=" memo_type  != null and memo_type !='' "><![CDATA[ ,DEV_CLM.T_CLAIM_MEMO EM ]]></if>
		       
		         WHERE A.ORGAN_CODE = B.ORGAN_CODE
		               AND A.CURE_HOSPITAL = C.HOSPITAL_CODE
		        	   AND A.CASE_ID  = D.CASE_ID
		               AND G.CASE_ID = D.CASE_ID
		               AND Y.CASE_ID = D.CASE_ID
		               AND A.CASE_STATUS != '99'
		          <if test=" memo_type  != null and memo_type !='' ">
		          		<![CDATA[ AND EM.CONN_APPLY_ID = D.LIST_ID AND EM.MEMO_TYPE = #{memo_type}]]></if>
		          <if test=" memo_option  != null and memo_option !='' ">
		          		<![CDATA[ AND EM.MEMO_OPTION = #{memo_option}]]></if>
		          <if test=" organ_code  != null and organ_code !='' ">
		          		<![CDATA[ AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		          <if test=" directConnBus  != null and directConnBus !='' ">
		          		<![CDATA[ AND G.DIRECT_CONN_BUS = #{directConnBus}]]></if>
		          <if test=" startTime  != null and startTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME)  >=to_date(#{startTime},'yyyy-mm-dd')]]></if>
		          <if test=" endTime  != null and endTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME)  <=to_date(#{endTime},'yyyy-mm-dd')]]></if>
	<![CDATA[    ORDER BY A.CASE_ID)X
		LEFT JOIN
		         (SELECT A.CASE_ID,
		         (CASE WHEN A.TREAT_TYPE = '0' THEN '门诊' 
		               WHEN A.TREAT_TYPE = '1' THEN '住院' END)TREAT_TYPE,
		         COUNT(BILL_NO)BILLCOUNT FROM DEV_CLM.T_CLAIM_BILL A GROUP BY A.TREAT_TYPE ,A.CASE_ID ORDER BY A.CASE_ID)Z
		ON X.CASE_ID = Z.CASE_ID      
		LEFT JOIN 
		          (SELECT DISTINCT ME.CASE_ID,MI.NAME AS MEMO_OPTION
		           FROM DEV_CLM.T_MEMO_ITEM MI ,DEV_CLM.T_CLAIM_MEMO ME
		           WHERE MI.CODE = MEMO_OPTION AND MI.CODE LIKE '12%'
		          )AM
		ON X.CASE_ID = AM.CASE_ID
		GROUP BY  X.CASE_NO ,X.ORGAN_CODE,X.ORGAN_NAME,X.HOSPITAL_NAME,Z.TREAT_TYPE,Z.BILLCOUNT,
		       X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.CHECK_END_TIME,
		       X.ACTUAL_PAY ]]>
		       <![CDATA[ )]]>
	</select>
</mapper>