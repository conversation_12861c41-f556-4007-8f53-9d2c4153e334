<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwProductDaoImpl">
<!--
	<sql id="uwProductWhereCondition">
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" discnted_prem_af  != null "><![CDATA[ AND A.DISCNTED_PREM_AF = #{discnted_prem_af} ]]></if>
		<if test=" auto_renewal_indi  != null "><![CDATA[ AND <PERSON>.AUTO_RENEWAL_INDI = #{auto_renewal_indi} ]]></if>
		<if test=" uw_busi_id  != null "><![CDATA[ AND A.UW_BUSI_ID = #{uw_busi_id} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" uw_prd_id  != null "><![CDATA[ AND A.UW_PRD_ID = #{uw_prd_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" postponed_months  != null "><![CDATA[ AND A.POSTPONED_MONTHS = #{postponed_months} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" internal_id != null and internal_id != ''  "><![CDATA[ AND A.INTERNAL_ID = #{internal_id} ]]></if>
		<if test=" extra_prem_af  != null "><![CDATA[ AND A.EXTRA_PREM_AF = #{extra_prem_af} ]]></if>
		<if test=" pre_doc_uwdecision != null and pre_doc_uwdecision != ''  "><![CDATA[ AND A.PRE_DOC_UWDECISION = #{pre_doc_uwdecision} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" unit  != null "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" uw_decision_status  != null "><![CDATA[ AND A.UW_DECISION_STATUS = #{uw_decision_status} ]]></if>
		<if test=" delete_indi  != null "><![CDATA[ AND A.DELETE_INDI = #{delete_indi} ]]></if>
		<if test=" quality_indi  != null "><![CDATA[ AND A.QUALITY_INDI = #{quality_indi} ]]></if>
		<if test=" decision_remark != null and decision_remark != ''  "><![CDATA[ AND A.DECISION_REMARK = #{decision_remark} ]]></if>
		<if test=" decision_indi  != null "><![CDATA[ AND A.DECISION_INDI = #{decision_indi} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" discnt_prem_af  != null "><![CDATA[ AND A.DISCNT_PREM_AF = #{discnt_prem_af} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
<sql id="queryProUwProductByBusiProdCodeAndUwId">
        <if test=" uw_id  != null "><![CDATA[ AND a.UW_ID = #{uw_id} ]]></if>
        <if test=" apply_code  != null "><![CDATA[ AND a.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" busi_prod_code  != null "><![CDATA[ AND a.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
	</sql>	
	<sql id="queryUwProductByUwIDS">
		<if test=" uw_id  != null "><![CDATA[ AND a.UW_ID = #{uw_id} ]]></if>
	</sql>	

	<sql id="queryUwProductByUwPrdIdCondition">
		<if test=" uw_prd_id  != null "><![CDATA[ AND A.UW_PRD_ID = #{uw_prd_id} ]]></if>
	</sql>	

	<sql id="queryUwProductByUwPolicyCodeCondition">
	<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	
	<sql id="queryUwProductByCSPolicyCodeCondition">
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ and A.Policy_Code  = B.Policy_Code AND A.Policy_Code = #{policy_code} ]]></if>
	</sql>
	
		<sql id="queryUwProductByCSPolicyCode2Condition">
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND A.Policy_Code = #{policy_code} ]]></if>
	</sql>
	
	<sql id="queryUwRiAppProductByBusiItemIDss">
		<if test=" busi_item_id != null "><![CDATA[AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>
	
	<sql id="queryUwRiAppProductByItemIDss">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>
	

<!-- 添加操作 -->
	<insert id="addUwProduct"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="uw_prd_id"> SELECT DEV_UW.S_T_UW_PRODUCT.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_UW_PRODUCT(
				PRODUCT_CODE,BENEFIT_LEVEL, UW_ID, DISCNTED_PREM_AF, AUTO_RENEWAL_INDI, UW_BUSI_ID, COVERAGE_PERIOD, ITEM_ID, 
				UW_PRD_ID, BUSI_PROD_CODE, APPLY_CODE, ORGAN_CODE, INSERT_TIMESTAMP, POSTPONED_MONTHS, UPDATE_BY, 
				CHARGE_YEAR, EXTRA_PREM_AF, PRE_DOC_UWDECISION, BUSI_ITEM_ID, POLICY_ID, UNIT, 
				COVERAGE_YEAR, UW_DECISION_STATUS, DELETE_INDI, QUALITY_INDI,  INSERT_TIME, DECISION_INDI, 
				UPDATE_TIME, AMOUNT, TOTAL_PREM_AF, DECISION_CODE, CHARGE_PERIOD, STD_PREM_AF, POLICY_CODE, 
				DISCNT_PREM_AF, UPDATE_TIMESTAMP, INSERT_BY) 
			VALUES (
				#{product_code, jdbcType=VARCHAR} ,#{benefit_level, jdbcType=VARCHAR}, #{uw_id, jdbcType=NUMERIC} , #{discnted_prem_af, jdbcType=NUMERIC} , #{auto_renewal_indi, jdbcType=NUMERIC} , #{uw_busi_id, jdbcType=NUMERIC} , #{coverage_period, jdbcType=VARCHAR} , #{item_id, jdbcType=NUMERIC} 
				, #{uw_prd_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{organ_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{postponed_months, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} 
				, #{charge_year, jdbcType=NUMERIC} , #{extra_prem_af, jdbcType=NUMERIC} , #{pre_doc_uwdecision, jdbcType=VARCHAR} , #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{unit, jdbcType=NUMERIC} 
				, #{coverage_year, jdbcType=NUMERIC} , #{uw_decision_status, jdbcType=NUMERIC} , #{delete_indi, jdbcType=NUMERIC} , #{quality_indi, jdbcType=NUMERIC} ,SYSDATE , #{decision_indi, jdbcType=NUMERIC} 
				, SYSDATE , #{amount, jdbcType=NUMERIC} , #{total_prem_af, jdbcType=NUMERIC} , #{decision_code, jdbcType=VARCHAR} , #{charge_period, jdbcType=VARCHAR} , #{std_prem_af, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} 
				, #{discnt_prem_af, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteUwProduct" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_UW_PRODUCT WHERE UW_PRD_ID = #{uw_prd_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateUwProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_PRODUCT ]]>
		<set>
		<trim suffixOverrides=",">
			PRODUCT_CODE = #{product_code, jdbcType=VARCHAR} ,
			BENEFIT_LEVEL = #{benefit_level, jdbcType=VARCHAR} ,
		    UW_ID = #{uw_id, jdbcType=NUMERIC} ,
		    DISCNTED_PREM_AF = #{discnted_prem_af, jdbcType=NUMERIC} ,
		    AUTO_RENEWAL_INDI = #{auto_renewal_indi, jdbcType=NUMERIC} ,
		    UW_BUSI_ID = #{uw_busi_id, jdbcType=NUMERIC} ,
			COVERAGE_PERIOD = #{coverage_period, jdbcType=VARCHAR} ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    POSTPONED_MONTHS = #{postponed_months, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    EXTRA_PREM_AF = #{extra_prem_af, jdbcType=NUMERIC} ,
			PRE_DOC_UWDECISION = #{pre_doc_uwdecision, jdbcType=VARCHAR} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    UNIT = #{unit, jdbcType=NUMERIC} ,
		    COVERAGE_YEAR = #{coverage_year, jdbcType=NUMERIC} ,
		    UW_DECISION_STATUS = #{uw_decision_status, jdbcType=NUMERIC} ,
		    DELETE_INDI = #{delete_indi, jdbcType=NUMERIC} ,
		    QUALITY_INDI = #{quality_indi, jdbcType=NUMERIC} ,
			DECISION_REMARK = #{decision_remark, jdbcType=VARCHAR} ,
		    DECISION_INDI = #{decision_indi, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    AMOUNT = #{amount, jdbcType=NUMERIC} ,
		    TOTAL_PREM_AF = #{total_prem_af, jdbcType=NUMERIC} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
			CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR} ,
		    STD_PREM_AF = #{std_prem_af, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    DISCNT_PREM_AF = #{discnt_prem_af, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE UW_PRD_ID = #{uw_prd_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findUwProductByUwPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE,A.BENEFIT_LEVEL, A.UW_ID, A.DISCNTED_PREM_AF, A.AUTO_RENEWAL_INDI, A.UW_BUSI_ID, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.UW_PRD_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POSTPONED_MONTHS, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.PRE_DOC_UWDECISION, A.BUSI_ITEM_ID, A.POLICY_ID, A.UNIT, 
			A.COVERAGE_YEAR, A.UW_DECISION_STATUS, A.DELETE_INDI, A.QUALITY_INDI,A.DECISION_INDI, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, 
			A.DISCNT_PREM_AF FROM DEV_UW.T_UW_PRODUCT A WHERE 1 = 1  ]]>
		<include refid="queryUwProductByUwPrdIdCondition" />
		<![CDATA[ ORDER BY A.UW_PRD_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapUwProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE,A.BENEFIT_LEVEL, A.UW_ID, A.DISCNTED_PREM_AF, A.AUTO_RENEWAL_INDI, A.UW_BUSI_ID, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.UW_PRD_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POSTPONED_MONTHS, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.PRE_DOC_UWDECISION, A.BUSI_ITEM_ID, A.POLICY_ID, A.UNIT, 
			A.COVERAGE_YEAR, A.UW_DECISION_STATUS, A.DELETE_INDI, A.QUALITY_INDI, A.DECISION_REMARK, A.DECISION_INDI, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, 
			A.DISCNT_PREM_AF FROM DEV_UW.T_UW_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_PRD_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllUwProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE,A.BENEFIT_LEVEL, A.UW_ID, A.DISCNTED_PREM_AF, A.AUTO_RENEWAL_INDI, A.UW_BUSI_ID, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.UW_PRD_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POSTPONED_MONTHS, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.PRE_DOC_UWDECISION, A.BUSI_ITEM_ID, A.POLICY_ID, A.UNIT, 
			A.COVERAGE_YEAR, A.UW_DECISION_STATUS, A.DELETE_INDI, A.QUALITY_INDI, A.DECISION_REMARK, A.DECISION_INDI, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, 
			A.DISCNT_PREM_AF FROM DEV_UW.T_UW_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_PRD_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findUwProductTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_UW_PRODUCT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryUwProductForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PRODUCT_CODE,B.BENEFIT_LEVEL, B.UW_ID, B.DISCNTED_PREM_AF, B.AUTO_RENEWAL_INDI, B.UW_BUSI_ID, B.COVERAGE_PERIOD, B.ITEM_ID, 
			B.UW_PRD_ID, B.BUSI_PROD_CODE, B.APPLY_CODE, B.ORGAN_CODE, B.POSTPONED_MONTHS, 
			B.CHARGE_YEAR, B.EXTRA_PREM_AF, B.PRE_DOC_UWDECISION, B.BUSI_ITEM_ID, B.POLICY_ID, B.UNIT, 
			B.COVERAGE_YEAR, B.UW_DECISION_STATUS, B.DELETE_INDI, B.QUALITY_INDI, B.DECISION_REMARK, B.DECISION_INDI, 
			B.AMOUNT, B.TOTAL_PREM_AF, B.DECISION_CODE, B.CHARGE_PERIOD, B.STD_PREM_AF, B.POLICY_CODE, 
			B.DISCNT_PREM_AF FROM (
					SELECT ROWNUM RN, A.PRODUCT_CODE,A.BENEFIT_LEVEL, A.UW_ID, A.DISCNTED_PREM_AF, A.AUTO_RENEWAL_INDI, A.UW_BUSI_ID, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.UW_PRD_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POSTPONED_MONTHS, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.PRE_DOC_UWDECISION, A.BUSI_ITEM_ID, A.POLICY_ID, A.UNIT, 
			A.COVERAGE_YEAR, A.UW_DECISION_STATUS, A.DELETE_INDI, A.QUALITY_INDI, A.DECISION_REMARK, A.DECISION_INDI, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, 
			A.DISCNT_PREM_AF FROM DEV_UW.T_UW_PRODUCT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_PRD_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findUwProductByUwBusiId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.UW_ID, A.DISCNTED_PREM_AF, A.AUTO_RENEWAL_INDI, A.UW_BUSI_ID, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.UW_PRD_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POSTPONED_MONTHS, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.PRE_DOC_UWDECISION, A.BUSI_ITEM_ID, A.POLICY_ID, A.UNIT, 
			A.COVERAGE_YEAR, A.UW_DECISION_STATUS, A.DELETE_INDI, A.QUALITY_INDI, A.DECISION_INDI, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, 
			A.DISCNT_PREM_AF,A.Item_Id as CONT_ITEM_ID,A.PRODUCT_CODE,
			(select decision_code from DEV_UW.T_CONTRACT_PRODUCT where policy_code = A.POLICY_CODE and uw_id = A.Uw_Id and  item_id = A.ITEM_ID and rownum = 1) nb_pro_decision_code, 
			/*(select decision_code from DEV_UW.T_CONTRACT_PRODUCT cp, DEV_UW.T_UW_POLICY up where cp.apply_code = A.APPLY_CODE   and cp.uw_id != A.Uw_Id and cp.item_id = A.ITEM_ID and up.policy_id = cp.policy_id and up.uw_source_type = '1' and up.uw_status = '04' and rownum = 1) nb_pro_decision_code,*/
			(select customer_name from DEV_UW.T_INSURED_LIST where UW_ID = A.UW_ID and policy_code = A.POLICY_CODE and rownum =1) customer_name,
			(select customer_id from DEV_UW.T_INSURED_LIST where UW_ID = A.UW_ID and policy_code = A.POLICY_CODE and rownum =1) customer_id,
			 (select  max(em_value) from DEV_UW.T_UW_EXTRA_PREM p where p.uw_id =A.UW_ID and extra_type = '4' and add_arith='1' and  uw_prd_id =A.uw_prd_id and busi_prod_code = A.busi_prod_code) em_value  
			 ,(select tpl.OPTION_TYPE from DEV_PDS.T_PRODUCT_LIFE tpl where tpl.internal_id=A.Product_Code) option_type
             ,(select tpl.PRODUCT_ID from DEV_PDS.T_PRODUCT_LIFE tpl where tpl.internal_id=A.Product_Code) PRODUCT_ID
			 FROM DEV_UW.T_UW_PRODUCT	A WHERE 1 = 1  AND UW_BUSI_ID = #{uw_busi_id}]]>
		    <include refid="queryUwProductByUwIDS" />
			<include refid="queryUwProductByUwPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.UW_PRD_ID               ]]>
	</select>
	
	<update id="updateUwProductByUwPrdId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_PRODUCT                    ]]>
		<set>
		<trim suffixOverrides=",">
			UW_DECISION_STATUS = #{uw_decision_status, jdbcType=NUMERIC} ,
			DECISION_CODE = #{decision_code, jdbcType=VARCHAR} ,
			<if test=" update_by  != null "> 
			UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE ,
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,</if>
			<if test=" amount  != null "> AMOUNT = ${amount} , </if>
			<if test=" std_prem_af  != null "> STD_PREM_AF = ${std_prem_af} , </if>
			<if test=" benefit_level  != null and  benefit_level  != ''  and  benefit_level  != 'null' "> benefit_level = LTRIM(RTRIM(#{benefit_level}, ' ') , ' ') , </if>
			<if test=" unit  != null "> UNIT = ${unit} , </if>
			<if test=" coverage_period  != null and  coverage_period  != ''  and  coverage_period  != 'null' "> COVERAGE_PERIOD = LTRIM(RTRIM(#{coverage_period}, ' ') , ' ') , </if>
			<if test=" coverage_year  != null "> COVERAGE_YEAR = ${coverage_year} , </if>
			<if test=" charge_period  != null and  charge_period  != ''  and  charge_period  != 'null' "> CHARGE_PERIOD = LTRIM(RTRIM(#{charge_period}, ' ') , ' ') , </if>
			<if test=" charge_year  != null "> CHARGE_YEAR = ${charge_year} , </if>
			<!-- 保全做出核保决定功能开发 保存延期时间和是否优质体字段的修改 2015年8月27日 start -->
			<if test="postponed_months != null "> postponed_months = ${postponed_months} , </if>
			<if test="quality_indi  != null "> quality_indi = ${quality_indi} , </if>
			<!-- 保全做出核保决定功能开发 保存延期时间和是否优质体字段的修改 2015年8月27日 end -->
		</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<if test=" uw_id != null ">AND UW_ID = #{uw_id} </if>
		<if test=" uw_prd_id != null ">AND uw_prd_id = #{uw_prd_id } </if>
	</update>
	
	<select id="findUwProductInfoByUwPrdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT B.PRODUCT_ID,B.INITIAL_TYPE,B.COUNT_WAY,B.COVERAGE_PERIOD,B.COVERAGE_YEAR,B.CHARGE_PERIOD,B.CHARGE_YEAR,B.AMOUNT,B.UNIT,
      B.BENEFIT_LEVEL,B.TOTAL_PREM_AF,A.UW_ID,A.ORGAN_CODE,A.UW_BUSI_ID,A.UW_PRD_ID,A.POLICY_ID,A.BUSI_ITEM_ID,A.ITEM_ID,A.POLICY_CODE,A.APPLY_CODE,A.BUSI_PROD_CODE,
      B.VALIDATE_DATE,B.APPLY_DATE,B.STD_PREM_AF,b.expiry_date,(select paidup_date from DEV_UW.T_CONTRACT_BUSI_PROD where uw_id =B.UW_ID and busi_item_id = B.busi_item_id and rownum = 1) paidup_date
			FROM DEV_UW.T_UW_PRODUCT A,DEV_UW.T_CONTRACT_PRODUCT B WHERE A.UW_PRD_ID=${uw_prd_id} AND A.ITEM_ID=B.ITEM_ID AND A.UW_ID=B.UW_ID]]>
		 <include refid="queryUwProductByCSPolicyCodeCondition" />  <!--add by xuhp 保全核保下发核保结论 下发条件承保核保结论需要支持契约和保全 2015年9月18日 --> 
		<![CDATA[ ORDER BY A.UW_PRD_ID               ]]> 
	</select>
	
		<select id="findUwProductByUwIDS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[    select b.BUSI_PROD_CODE, c.PRODUCT_NAME_SYS, a.UW_ID, d.APPLY_CODE ,a.policy_code
				     from DEV_UW.T_UW_POLICY a, DEV_UW.T_UW_BUSI_PROD b, DEV_UW.T_BUSINESS_PRODUCT c,DEV_UW.T_CONTRACT_MASTER d
				    where a.policy_decision = '50'
				      and b.decision_code = '50'
				      and a.uw_id = b.uw_id
				      and a.apply_code = b.apply_code
				      and a.uw_id = d.uw_id
				      and a.policy_id = d.policy_id 
				      and b.busi_prod_code = c.PRODUCT_CODE_SYS]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND d.APPLY_CODE = #{apply_code} ]]></if>
		<include refid="queryUwProductByUwIDS" /><!--  group by a.BUSI_PROD_CODE,b.PRODUCT_NAME_SYS,a.UW_ID,a.APPLY_CODE -->
		
	</select>
	
	<select id="findProUwProductByBusiProdCodeAndUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select b.product_id,
				       (select product_name
				          from DEV_UW.T_PRODUCT_LIFE
				         where product_id = b.product_id) product_name,
				       (select apply_date
				          from DEV_UW.T_CONTRACT_MASTER m
				         where m.uw_id = a.uw_id
				           and m.apply_code = a.apply_code) apply_date
				  from DEV_UW.T_UW_PRODUCT a, DEV_UW.T_CONTRACT_PRODUCT b
				 where a.uw_id = b.uw_id
				   and a.apply_code = b.apply_code
				   and a.busi_item_id = b.busi_item_id
				   and a.item_id = b.item_id]]>
       <include refid="queryProUwProductByBusiProdCodeAndUwId" />
	</select>
	
	<!-- 保全核保做出核保决定功能 查询保单下的责任是否全部下发核保决定 2015年9月6日 -->
	<select id="findUwProductAllDecision" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select DECISION_CODE,DECISION_INDI,UW_PRD_ID,(select decision_code from DEV_UW.T_CONTRACT_PRODUCT where  uw_id = a.uw_id and policy_code = a.policy_code and busi_item_id = a.busi_item_id and item_id = a.item_id) cdecision_code from DEV_UW.T_UW_PRODUCT A where uw_id = #{uw_id}]]>
		<include refid="queryUwProductByCSPolicyCode2Condition" />
	</select>
	
	<!-- 查询核保决定 -->
	<select id="findUwProductDecision" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select to_char(DECISION_CODE) DECISION_CODE from DEV_UW.T_UW_PRODUCT A where uw_id = #{uw_id} ]]>
		<if test=" uw_prd_id  != null "><![CDATA[ AND a.UW_PRD_ID = #{uw_prd_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND a.POLICY_ID = #{policy_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND a.item_id = #{item_id} ]]></if>
	</select>
	
	<!-- add by xuhp 保全核保下发核保决定 维持原核保结论 查询抄单的保单层的核保结论 2015年9月23日 -->
	<select id="findUwProductByUWidAndPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select UW_PRD_ID,UW_ID,Policy_Code,uw_busi_id,POLICY_ID,APPLY_CODE,
		A.BUSI_ITEM_ID,A.ITEM_ID,A.BUSI_PROD_CODE,A.PRODUCT_CODE from DEV_UW.T_UW_PRODUCT A where 1=1]]>
		<include refid="queryUwProductByUwIDS" />
		<include refid="queryUwProductByCSPolicyCode2Condition" />
		<include refid="queryUwRiAppProductByBusiItemIDss" />
		<include refid="queryUwRiAppProductByItemIDss" />
		<include refid="queryUwProductByUwPrdIdCondition" />
	</select>
	
	<select id="findUwRiAppProductByBusiItemIDssss" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select RIPRD_ID,APPLY_CODE,POLICY_ID,POLICY_CODE,BUSI_ITEM_ID,ITEM_ID,BUSI_PROD_CODE,PRODUCT_CODE,CUSTOMER_ID,
		RI_ID,RI_APP_REASON,REPLY_ADVICE_EXTRA_POINT,REPLY_ADVICE_AMOUNT,REPLY_ADVICE,INITIAL_TYPE,EXTRA_POINT,EXPIRY_DATE,CHARGE_YEAR,
		CHARGE_PERIOD,BENEFIT_LEVEL,AMOUNT,VALIDATE_DATE,UNIT,STD_PREM from DEV_UW.T_RI_APP_PRODUCT A where 1=1]]>
		<include refid="queryUwRiAppProductByBusiItemIDss" />
	</select>
	
<!-- 删除操作 -->	
	<delete id="delUwProductByUwId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_UW_PRODUCT WHERE UW_ID = #{uw_id} ]]>
	</delete>
	
	<delete id="deluwRiAppProductPoByBusiItemId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_RI_APP_PRODUCT WHERE BUSI_ITEM_ID = #{busi_item_id} ]]>
	</delete>
	<insert id="saveuwRiAppProductPOs"  useGeneratedKeys="true"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="riPrd_id">
			SELECT  DEV_UW.S_t_ri_app_product.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_RI_APP_PRODUCT(
				RIPRD_ID, APPLY_CODE, POLICY_ID, POLICY_CODE, BUSI_ITEM_ID, ITEM_ID, BUSI_PROD_CODE, 
				PRODUCT_CODE, CUSTOMER_ID, RI_ID, RI_APP_REASON, REPLY_ADVICE_EXTRA_POINT, REPLY_ADVICE_AMOUNT, REPLY_ADVICE, 
				INITIAL_TYPE, EXTRA_POINT, EXPIRY_DATE, CHARGE_YEAR, CHARGE_PERIOD, BENEFIT_LEVEL, AMOUNT, 
				VALIDATE_DATE, UNIT, STD_PREM,INSERT_TIMESTAMP,INSERT_TIME,INSERT_BY,UPDATE_TIMESTAMP,UPDATE_TIME,UPDATE_BY) 
			VALUES (
				#{riPrd_id, jdbcType=NUMERIC}, #{apply_code, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{busi_item_id, jdbcType=NUMERIC} , #{item_id, jdbcType=NUMERIC} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{product_code, jdbcType=VARCHAR} , #{customer_id, jdbcType=NUMERIC} , #{ri_id, jdbcType=NUMERIC} , #{RI_APP_REASON, jdbcType=NUMERIC} , #{reply_advice_extra_point, jdbcType=NUMERIC} , #{reply_advice_amount, jdbcType=NUMERIC} 
				, #{reply_advice, jdbcType=VARCHAR} , #{initial_type, jdbcType=NUMERIC} , #{extra_point, jdbcType=NUMERIC} , #{expiry_date, jdbcType=DATE} , #{charge_year, jdbcType=NUMERIC} , #{charge_period, jdbcType=VARCHAR}
				, #{benefit_level, jdbcType=VARCHAR} , #{amount, jdbcType=NUMERIC} , #{validate_date, jdbcType=DATE} , #{unit, jdbcType=NUMERIC} , #{std_prem, jdbcType=NUMERIC} ,CURRENT_TIMESTAMP,SYSDATE,#{insert_by, jdbcType=NUMERIC},
				CURRENT_TIMESTAMP,SYSDATE,#{update_by, jdbcType=NUMERIC}) 
		 ]]>
	</insert>
	
	<insert id="saveuwRiAppPolicyPOss"  useGeneratedKeys="true"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="ri_id">
			SELECT  DEV_UW.S_T_RI_APP_POLICY.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_RI_APP_POLICY(
				RI_ID, UW_ID, APPLY_CODE, POLICY_ID, POLICY_CODE, RI_NUM, RI_APP_COMMENT, 
				REPLY_USER, REPLY_TIME, REPLY_ADVICE_COMMENT, REPLY_ADD_INFO_COMMENT, IS_REPLY, 
				APPLY_RI_USER, APPLY_RI_TIME,INSERT_TIMESTAMP,INSERT_TIME,INSERT_BY,UPDATE_TIMESTAMP,UPDATE_TIME,UPDATE_BY) 
			VALUES (
				#{ri_id, jdbcType=NUMERIC}, #{uw_id, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{ri_num, jdbcType=NUMERIC} , #{ri_app_comment, jdbcType=VARCHAR} 
				, #{reply_user, jdbcType=VARCHAR} , #{reply_time, jdbcType=DATE} , #{reply_advice_comment, jdbcType=VARCHAR} , #{reply_add_info_comment, jdbcType=VARCHAR}  , #{is_reply, jdbcType=NUMERIC} 
				, #{apply_ri_user, jdbcType=NUMERIC} , #{apply_ri_time, jdbcType=DATE},CURRENT_TIMESTAMP,SYSDATE,#{insert_by, jdbcType=NUMERIC},
				CURRENT_TIMESTAMP,SYSDATE,#{update_by, jdbcType=NUMERIC}) 
		 ]]>
	</insert>
	
	<!-- R06601001890   -->	
	<select id="findUwBusiProdForESB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 
					A.APPLY_CODE,
					A.BUSI_ITEM_ID,
					A.UPDATE_BY,
					A.UPDATE_TIME,
					A.DECISION_CODE,
					(SELECT B.DECISION_DESC
					          FROM DEV_UW.t_Product_Decision B
					         WHERE A.DECISION_CODE = B.DECISION_CODE) AS DECISION_DESC,
					(SELECT SUM(C.AMOUNT) FROM DEV_UW.T_UW_LIMIT C 
					     WHERE C.APPLY_CODE = A.APPLY_CODE AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
					     AND A.UW_ID = C.UW_ID AND A.UW_BUSI_ID = C.UW_BUSI_ID) AS AMOUNT
					FROM DEV_UW.T_UW_BUSI_PROD A WHERE 1=1
		]]>
		<if test=" APPLY_CODE  != null "><![CDATA[ AND A.APPLY_CODE = #{APPLY_CODE} ]]></if>
		<if test=" polno  != null and polno != '' "><![CDATA[ AND A.BUSI_ITEM_ID = (select ncm.busi_item_id  from DEV_NB.t_Nb_Contract_Busi_Prod ncm where nvl(ncm.old_pol_no,ncm.busi_item_id)=#{polno}) ]]></if>
		<![CDATA[ ORDER BY A.APPLY_CODE ]]>
	</select>
	<!-- 根据保单号查询险种核保结论信息   -->
		<select id="findBusiProdForESB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 
					A.APPLY_CODE,
					A.BUSI_ITEM_ID,
					A.UPDATE_BY,
					A.UPDATE_TIME,
					A.DECISION_CODE,
					(SELECT B.DECISION_DESC
					          FROM DEV_UW.T_POLICY_DECISION B
					         WHERE A.DECISION_CODE = B.DECISION_CODE) AS DECISION_DESC,
					(SELECT SUM(C.AMOUNT) FROM DEV_UW.T_UW_LIMIT C 
					     WHERE C.APPLY_CODE = A.APPLY_CODE AND A.BUSI_ITEM_ID = C.BUSI_ITEM_ID
					     AND A.UW_ID = C.UW_ID AND A.UW_BUSI_ID = C.UW_BUSI_ID) AS AMOUNT
					FROM DEV_UW.T_UW_BUSI_PROD A WHERE 1=1
					
		]]>
		<if test=" APPLY_CODE  != null "><![CDATA[ AND A.POLICY_CODE = #{APPLY_CODE} ]]></if>
		<if test=" polno  != null and polno != '' "><![CDATA[and A.BUSI_ITEM_ID = (select ncm.busi_item_id  from DEV_NB.t_Nb_Contract_Busi_Prod ncm where nvl(ncm.old_pol_no,ncm.busi_item_id)=#{polno}) ]]></if>
		<![CDATA[ ORDER BY A.APPLY_CODE ]]>
	</select>
	
	
	
	<sql id="findUwBusiProdForESBCondition">
		<if test=" APPLY_CODE  != null "><![CDATA[ AND A.APPLY_CODE = #{APPLY_CODE} ]]></if>
		<if test=" BUSI_ITEM_ID  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{BUSI_ITEM_ID} ]]></if>
	</sql>
	
	<update id="UW_rollbackUwProduct" parameterType="java.util.Map">
		<![CDATA[ UPDATE  DEV_UW.T_UW_PRODUCT set]]>
			(AMOUNT,UNIT,STD_PREM_AF,DISCNTED_PREM_AF,EXTRA_PREM_AF,TOTAL_PREM_AF)=
			(select t.AMOUNT,t.UNIT,t.STD_PREM_AF,t.DISCNTED_PREM_AF,t.EXTRA_PREM_AF,t.TOTAL_PREM_AF from DEV_UW.T_CONTRACT_PRODUCT t where 1=1
		<if test=" uw_id != null "><![CDATA[ and t.UW_ID = #{uw_id} ]]></if>
		<if test=" apply_code != null "><![CDATA[ and t.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_id != null "><![CDATA[ and t.POLICY_ID = #{policy_id} ]]></if>
		<if test=" item_id  != null"><![CDATA[ AND t.ITEM_ID = #{item_id} ]]></if>
		<if test=" uw_prd_id  != null and uw_id != null"><![CDATA[ AND t.busi_item_id = (select busi_item_id from DEV_UW.T_UW_PRODUCT where UW_PRD_ID = #{uw_prd_id}  and UW_ID = #{uw_id} )]]></if>
		<![CDATA[ 	and rownum = 1)
		WHERE 1=1 ]]>
		<if test=" uw_id != null "><![CDATA[ and UW_ID = #{uw_id} ]]></if>
		<if test=" apply_code != null "><![CDATA[ and APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_id != null "><![CDATA[ and POLICY_ID = #{policy_id} ]]></if>
		<if test=" item_id  != null"><![CDATA[ AND ITEM_ID = #{item_id} ]]></if>
		<if test=" uw_prd_id  != null"><![CDATA[ AND UW_PRD_ID = #{uw_prd_id} ]]></if>
		<if test=" uw_busi_id  != null and uw_prd_id == null"><![CDATA[ AND UW_BUSI_ID = #{uw_busi_id} ]]></if>
	</update>
	
	<update id="UW_updateTotalAmount_extra" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_PRODUCT t1
			   set (TOTAL_PREM_AF, EXTRA_PREM_AF) =
			       (select t1.TOTAL_PREM_AF + total_extra_prem, total_extra_prem
			          from (select nvl(sum(A.extra_prem), 0) total_extra_prem
			                  from DEV_UW.T_UW_EXTRA_PREM A
			                 where A.UW_ID = #{uw_id}
			                 and A.UW_PRD_ID = #{uw_prd_id}) t2)
			 WHERE t1.UW_ID = #{uw_id} ]]>
		<if test=" item_id != null "><![CDATA[ and t1.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_id != null "><![CDATA[ and t1.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND t1.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" uw_prd_id  != null  and busi_item_id == null"><![CDATA[ AND t1.busi_item_id = (select busi_item_id from DEV_UW.T_UW_PRODUCT where UW_PRD_ID = #{uw_prd_id}  and UW_ID = #{uw_id} )]]></if>
	</update>
	
	<update id="UW_updateTotalAmount_limit" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_PRODUCT t1
			   set TOTAL_PREM_AF=#{total_prem_af, jdbcType=NUMERIC} ]]>
			   <if test=" amount != null "><![CDATA[ , AMOUNT=#{amount, jdbcType=NUMERIC} ]]></if>
			   <if test=" unit != null "><![CDATA[ , UNIT=#{unit, jdbcType=NUMERIC} ]]></if>
			<![CDATA[  WHERE t1.UW_ID = #{uw_id} ]]> 
		<if test=" policy_id != null "><![CDATA[ and t1.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND t1.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" item_id  != null"><![CDATA[ AND t1.ITEM_ID = #{item_id} ]]></if>
		<if test=" uw_prd_id  != null  and busi_item_id == null"><![CDATA[ AND t1.busi_item_id = (select busi_item_id from DEV_UW.T_UW_PRODUCT where UW_PRD_ID = #{uw_prd_id}  and UW_ID = #{uw_id} )]]></if>
	</update>
	
	<select id="find193ProdByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select t1.*
			  from DEV_UW.t_uw_product t1, DEV_UW.t_business_product t2
			 where t1.busi_prod_code = t2.product_code_sys
			   and t1.uw_id = #{uw_id}
			   and t2.cover_period_type = 0
			   and (exists (select 1 from DEV_UW.t_uw_extra_prem t
			                 where t1.busi_prod_code = t2.product_code_sys
			                   and t2.cover_period_type = 0
			                   and t.uw_id = t1.uw_id) or
			        (exists (select 1 from DEV_UW.t_uw_busi_prod t4
			                         where t4.busi_prod_code in
			                               (select t.busi_prod_code
			                                  from DEV_UW.t_uw_extra_prem t
			                                 where t.uw_id = t1.uw_id)
			                           and t4.master_busi_item_id is null)))
			   and exists (select 1 from DEV_UW.t_uw_product t3
			         where t3.busi_prod_code = '00193000'
			           and t3.uw_id = t1.uw_id)]]>
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND t1.policy_code = #{policy_code} ]]></if>
	</select>
	
	<update id="UW_updateTotalAmount_AddfeeDecu" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_PRODUCT t1
			   set AMOUNT = #{amount}  , EXTRA_PREM_AF = #{extra_prem_af}
			 WHERE t1.UW_ID = #{uw_id} ]]>
		<if test=" item_id != null "><![CDATA[ and t1.ITEM_ID = #{item_id} ]]></if>
		<if test=" policy_id != null "><![CDATA[ and t1.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND t1.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" uw_prd_id  != null  and busi_item_id == null"><![CDATA[ AND t1.busi_item_id = (select busi_item_id from DEV_UW.T_UW_PRODUCT where UW_PRD_ID = #{uw_prd_id}  and UW_ID = #{uw_id} )]]></if>
	</update>
</mapper>
