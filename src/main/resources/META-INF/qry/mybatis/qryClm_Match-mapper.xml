<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.impl.task.dao.impl.ClmQueryMatchDaoImpl"> 
    <!--  匹配理算结果 -->
    <!-- 匹配理算结果 保项计算信息 保项赔付结论 -->
    <select id="queryPAJSInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
             SELECT 
                 CL.POLICY_CODE,
                 CL.ITEM_ID,
                 CL.LIAB_ID,
                 CL.LIAB_START_DATE,
                 CL.LIAB_END_DATE,
        <if test=" busi_prd_id  != null  and busi_prd_id != ''"> (SELECT GP.GRACE_PERIOD_DAY FROM DEV_PDS.T_GRACE_PERIOD GP 
                WHERE GP.BUSINESS_PRD_ID = #{busiprdid}) GRACE_PERIOD_DAY,</if>
        <if test=" busi_prd_id == null  or busi_prd_id = ''">'60' as GRACE_PERIOD_DAY,</if>
      CP.BONUS_SA BONUS_SA,
       BA.BONUS_SA BONUS_SA1,
                 '终了红利' ZLHL,
                 CL.CALC_PAY,
                 CL.ADVANCE_PAY ,
                 CL.ADJUST_PAY,
                 CC.ACCEPT_DECISION AS LIAB_CONCLUSION,
                 CL.IS_COMMON,
                 CL.RESIST_FLAG,
                 CL.LIAB_ADJUST_REASON,
                 CL.ADJUST_REMARK,
                 CL.LIAB_RISK_LABEL,
                 CL.OTHER_REASON 
            FROM DEV_CLM.T_CLAIM_LIAB CL
            INNER JOIN DEV_CLM.T_CLAIM_CASE CC ON CL.CASE_ID=CC.CASE_ID
            LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP 
ON CP.POLICY_CODE = CL.POLICY_CODE  AND CP.BUSI_ITEM_ID=CL.BUSI_ITEM_ID
LEFT  JOIN  DEV_PAS.T_BONUS_ALLOCATE BA 
ON BA.POLICY_CODE = CL.POLICY_CODE  AND CP.ITEM_ID=BA.ITEM_ID
                 WHERE CL.CASE_ID=#{caseNo} 
    </select>

    <!-- 匹配理算结果 保单计算信息 -->
    <select id="queryPAList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <!-- SELECT DISTINCT BPP.POLICY_CODE,
                        CSC.CLAIM_TYPE,
                        BPP.VALIDATE_DATE,
                         (select T_CONTRACT_EXTEND.Pay_Due_Date from    dev_clm.T_CONTRACT_EXTEND  where  policy_id=cbp.policy_id
       and cur_flag='0'
        and rownum=1) DUE_DATE,
                        TP.PRODUCT_NAME_STD,
                        BPP.EXPIRY_DATE,
                        BPP.BUSI_PRD_ID,
                        BPP.BUSI_PROD_CODE,
                        BPP.BUSI_ITEM_ID,
                        CBP.CALC_PAY,
                        RA.RISK_AMOUNT,
                        CSC.CLAIM_DATE
          FROM DEV_CLM.T_CONTRACT_BUSI_PROD BPP
          LEFT JOIN DEV_CLM.T_CLAIM_SUB_CASE CSC
            ON BPP.CASE_ID = CSC.CASE_ID
        
          LEFT JOIN DEV_CLM.T_CLAIM_BUSI_PROD CBP
            ON CBP.CASE_ID = BPP.CASE_ID
        
          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT TP
            ON BPP.BUSI_PROD_CODE = TP.PRODUCT_CODE_SYS
        
          LEFT JOIN DEV_PAS.T_RISK_AMOUNT RA
            ON RA.POLICY_ID = BPP.POLICY_ID
         WHERE BPP.CASE_ID = #{caseNo}-->
         SELECT B.* FROM (
         select distinct bp.policy_code,
       sc.claim_type,
       bp.valid_date VALIDATE_DATE,
       bp.expire_date, 
       (select T_CONTRACT_EXTEND.Pay_Due_Date from    DEV_CLM.T_CONTRACT_EXTEND  where  policy_id=bp.policy_id
       and cur_flag='0'
        and rownum=1) due_date,
       bp.busi_prod_code,
       p.product_name_std,
       sum(case when cl.liab_conclusion != 5 then nvl(cl.adjust_pay,0) else 0 end) actual_pay,
       bp.busi_item_id,
       sum(cl.calc_pay) calc_pay,
       '' as cash_value,
       ''
     from DEV_CLM.T_CLAIM_SUB_CASE sc, DEV_CLM.T_CLAIM_BUSI_PROD bp, DEV_PDS.T_BUSINESS_PRODUCT p, DEV_CLM.T_CLAIM_LIAB cl
    where sc.case_id = bp.case_id
   and cl.busi_prod_code = bp.busi_prod_code
   and cl.busi_item_id = bp.busi_item_id
   and sc.sub_case_id = cl.sub_case_id
   and bp.busi_prod_code = p.product_code_sys
   and sc.case_id = #{caseNo}
   GROUP BY  bp.policy_code,
   bp.policy_id,
       sc.claim_type,
       bp.valid_date,
       bp.expire_date,
       bp.busi_prod_code,
       p.product_name_std, 
       bp.busi_item_id )B WHERE B.CALC_PAY IS NOT NULL
    </select>
    <!-- 匹配理算结果 赔案计算信息 -->
    <select id="queryPAJSList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
               SELECT A.CALC_PAY,
                     A.ADVANCE_PAY,
                     A.BALANCE_PAY,
                     A.ACTUAL_PAY,
                     A.REJECT_PAY
                FROM dev_clm.T_CLAIM_CASE A,dev_clm.T_CLAIM_ACCIDENT B 
                WHERE 1=1 AND A.ACCIDENT_ID = B.ACCIDENT_ID AND A.CALC_PAY IS NOT NULL
                AND A.CASE_ID =#{caseNo}
            ]]>
    </select>
    <!-- 理赔类型计算信息 -->
    <!-- 理赔类型为08医疗时，才有账单金额，为医疗类型时，核赔赔付金额需要计算：
    等 （帐单金额-社保给付-第三方给付）>没有津贴的实际支付金额时，核赔赔付金额为没有津贴的实际支付金额+支付的津贴,否则为（帐单金额-社保给付-第三方给付）+支付的津贴。
     -->
    <select id="queryClaimLiabForMatchResultByType" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT C.*, C.ACTUAL_PAY AS ACTUAL_PAY_NEW/*和理赔确认修改核赔赔付金额 */
  FROM (SELECT B.*,
               NVL((SELECT SUM(A.ACTUAL_PAY)
                     FROM DEV_CLM.T_CLAIM_LIAB A
                    WHERE A.CASE_ID = B.CASE_ID
                      AND A.IS_SUBSIDY = 1),
                   0) JINACTUAL_PAY,
               NVL((SELECT SUM(A.ACTUAL_PAY)
                     FROM DEV_CLM.T_CLAIM_LIAB A
                    WHERE A.CASE_ID = B.CASE_ID
                      AND A.IS_SUBSIDY != 1),
                   0) NOJINACTUAL_PAY,
               NVL((SELECT SUM(D.PAID_AMOUNT)
                     FROM DEV_CLM.T_CLAIM_BILL_PAID D
                    WHERE D.CASE_ID = B.CASE_ID
                      AND D.OTHER_TYPE = '1'),
                   0) PAID_AMOUNT,
               NVL((SELECT SUM(D.PAID_AMOUNT)
                     FROM DEV_CLM.T_CLAIM_BILL_PAID D
                    WHERE D.CASE_ID = B.CASE_ID
                      AND D.OTHER_TYPE = '2'),
                   0) SUM_AMOUNT,
               (CASE B.CLAIM_TYPE
                 WHEN '08' THEN
                  (SELECT SUM(SUM_AMOUNT)
                     FROM DEV_CLM.T_CLAIM_BILL CB
                    WHERE CB.CASE_ID = B.CASE_ID)
                 ELSE
                  NULL
               END) AS BILL_AMOUNT
          FROM (SELECT A.CASE_ID,
                       A.CLAIM_TYPE,
                       SUM(A.CALC_PAY) CALC_PAY,
                       SUM(A.ACTUAL_PAY) ACTUAL_PAY,
                       SUM(A.BASIC_PAY) BASIC_PAY
                  FROM DEV_CLM.T_CLAIM_LIAB A, DEV_CLM.T_CLAIM_SUB_CASE B
                 WHERE 1 = 1
                   AND A.SUB_CASE_ID = B.SUB_CASE_ID
                   AND A.CLAIM_TYPE = B.CLAIM_TYPE
                   AND A.CASE_ID = #{caseNo}
                 GROUP BY A.CASE_ID, A.CLAIM_TYPE) B) C
  
        ]]>
    </select>
     <!--  -->
    <!-- 立案 审核 审批结论 -->
     <select id="queryLPJLInfoList" resultType="java.util.Map"
        parameterType="java.util.Map">
            <![CDATA[ 
           select * from (
             SELECT distinct AA.LIST_ID,
               AA.AUDIT_DECISION AS DECISION, /*审核结论*/
              AA.Is_Common AS OVER_COMP_FLAG, /*超期补偿标识*/
               CC.CASE_FLAG,/*案件标识*/
               CC.IS_LAWSUITS,/*是否诉讼案件(T_YES_NO)*/
               CC.MATERIAL_FREE_FLAG,/*免材料标识*/
               CC.MEDICAL_CONNECTION_FLAG,/*医疗直连标识*/
               CC.REPEAT_NUMBER_FLAG,/*重复账单号标识*/
               CC.REPEAT_NUMBER_TYPE,/*重复账单类型*/
               CC.REPEAT_NUMBER_REASON,/*重复账单其他原因*/
               CASE WHEN CC.REALTIME_PAY IS NULL THEN 0 ELSE CC.REALTIME_PAY END AS REALTIME_PAY,
               AA.Audit_Reject_Reason AS Reject_Reason ,/*不通过原因*/
                                    (SELECT B.USER_NAME
                FROM DEV_PAS.T_UDMP_USER B
               WHERE AA.AUDITOR_ID = B.USER_ID) AS APPROVER_NAME, /*审核人*/         
               (SELECT B.REAL_NAME
                FROM DEV_PAS.T_UDMP_USER B
               WHERE AA.AUDITOR_ID = B.USER_ID) AS APPROVER_REAL_NAME, /*审核真实名称*/         
                  AA.AUDIT_TIME, /*审核时间*/
                  1 AS LIST_TYPE,
                  AA.CASE_ID ,
                  AA.OVER_COMP_FLAG AS AU_OVER_COMP_FLAG ,/*审核阶段的超期补偿标识*/
                  AA.IS_OVER_COMP AS AU_IS_OVER_COMP,/*审核阶段的超期标识*/
                  AA.OVER_REASON AS AU_OVER_REASON,/*审核阶段的超期原因*/
       		      AA.OVER_DAYS AS AU_OVER_DAYS,/*审核阶段的超期天数*/
       		      AA.OVER_DUE_MONEY AS AU_OVER_DUE_MONEY,/*审核阶段的超期核定补偿总金额*/
                  CC.OVER_COMP_FLAG  AS APP_OVER_COMP_FLAG,/* 审批阶段的超期补偿标识*/
                  CC.IS_OVER_COMP AS APP_IS_OVER_COMP,/*审批阶段的超期标识*/
                  CC.OVER_REASON AS APP_OVER_REASON,/*审批阶段的超期原因*/
       		      CC.OVER_DAYS AS APP_OVER_DAYS,/*审批阶段的超期天数*/
       		      CC.OVER_DUE_MONEY AS APP_OVER_DUE_MONEY /*审批阶段的超期核定补偿总金额*/       		   
               FROM 
                 DEV_CLM.T_CLAIM_AUDIT_APPROVE AA
               INNER JOIN   DEV_CLM.T_CLAIM_CASE CC
                ON CC.CASE_ID = AA.CASE_ID
                WHERE CC.CASE_ID= #{caseNo} AND AA.AUDIT_REMARK IS NOT NULL
                union 
                select
                   AA.LIST_ID,
                   AA.DECISION,
                   AA.OVER_COMP_FLAG,
                   AA.CASE_FLAG,
                   AA.IS_LAWSUITS,
                   AA.MATERIAL_FREE_FLAG,
                   AA.MEDICAL_CONNECTION_FLAG,
                   AA.REPEAT_NUMBER_FLAG,
                   AA.REPEAT_NUMBER_TYPE,
                   AA.REPEAT_NUMBER_REASON,
                   AA.REALTIME_PAY,
                   AA.Reject_Reason,
                   AA.APPROVER_NAME,
                   AA.APPROVER_REAL_NAME,
                   AA.AUDIT_TIME,
                   AA.LIST_TYPE,
                   AA.CASE_ID,
                   AA.OVER_COMP_FLAG AS AU_OVER_COMP_FLAG ,  /*审核阶段的超期补偿标识*/
                   AA.AU_IS_OVER_COMP, /*审核阶段的超期标识*/
       			   AA.AU_OVER_REASON,
      			   AA.AU_OVER_DAYS,
       			   AA.AU_OVER_DUE_MONEY,
       			   AA.APP_OVER_COMP_FLAG,/* 审批阶段的超期补偿标识*/
       			   AA.APP_IS_OVER_COMP,/*审批阶段的超期标识*/
      			   AA.APP_OVER_REASON,
      			   AA.APP_OVER_DAYS,
       			   AA.APP_OVER_DUE_MONEY
                 from (
               SELECT  
                 distinct AA.Audit_Remark,AA.Update_Time,AA.LIST_ID,
               AA.APPROVE_DECISION AS DECISION, /*审核结论*/
               AA.Is_Common AS OVER_COMP_FLAG, /*超期补偿标识*/
               CC.CASE_FLAG,/*案件标识*/
               CC.IS_LAWSUITS,/*是否诉讼案件(T_YES_NO)*/
               CC.MATERIAL_FREE_FLAG,/*免材料标识*/
               CC.MEDICAL_CONNECTION_FLAG,/*医疗直连标识*/
               CC.REPEAT_NUMBER_FLAG,/*重复账单号标识*/
               CC.REPEAT_NUMBER_TYPE,/*重复账单类型*/
               CC.REPEAT_NUMBER_REASON,/*重复账单其他原因*/
               CASE
                 WHEN CC.REALTIME_PAY IS NULL THEN
                  0
                 ELSE
                  CC.REALTIME_PAY
               END AS REALTIME_PAY,
               AA.OVER_COMP_FLAG AS AU_OVER_COMP_FLAG , /*审核阶段的超期补偿标识*/
               AA.IS_OVER_COMP AS AU_IS_OVER_COMP,/*审核阶段的超期标识*/
               AA.OVER_REASON AS AU_OVER_REASON,/*审核阶段的超期原因*/
       		   AA.OVER_DAYS AS AU_OVER_DAYS,/*审核阶段的超期天数*/
       		   AA.OVER_DUE_MONEY AS AU_OVER_DUE_MONEY,/*审核阶段的超期核定补偿总金额*/
               CC.OVER_COMP_FLAG  AS APP_OVER_COMP_FLAG,/* 审批阶段的超期补偿标识*/
               CC.IS_OVER_COMP AS APP_IS_OVER_COMP,/*审批阶段的超期标识*/
               CC.OVER_REASON AS APP_OVER_REASON,/*审批阶段的超期原因*/
       		   CC.OVER_DAYS AS APP_OVER_DAYS,/*审批阶段的超期天数*/
       		   CC.OVER_DUE_MONEY AS APP_OVER_DUE_MONEY, /*审批阶段的超期核定补偿总金额*/   
               AA.APPROVE_REJECT_REASON  AS Reject_Reason,/*不通过原因*/  
               (SELECT C.USER_NAME
                              FROM DEV_PAS.T_UDMP_USER C
                             WHERE AA.APPROVER_ID = C.USER_ID) AS APPROVER_NAME, /*审批人*/     
                (SELECT C.REAL_NAME
                              FROM DEV_PAS.T_UDMP_USER C
                             WHERE AA.APPROVER_ID = C.USER_ID) AS APPROVER_REAL_NAME, /*审批真实名*/      
                  AA.APPROVE_TIME AS AUDIT_TIME, /*审批时间*/ 
                 (SELECT MAX(List_Id) FROM DEV_CLM.T_CLAIM_AUDIT_APPROVE T 
                 WHERE T.CASE_ID= #{caseNo} AND T.LIST_ID< AA.List_Id)
                  AS LAST_LIST_ID,/*上次审批结论数据*/
                 2 AS LIST_TYPE,
                 AA.Approve_Remark,
                 AA.CASE_ID
               FROM 
                 DEV_CLM.T_CLAIM_AUDIT_APPROVE AA
               INNER JOIN   DEV_CLM.T_CLAIM_CASE CC
                ON CC.CASE_ID = AA.CASE_ID
                WHERE CC.CASE_ID= #{caseNo} AND(AA.AUDIT_REMARK IS NOT NULL) 
                ) AA  
                LEFT JOIN  DEV_CLM.T_CLAIM_AUDIT_APPROVE T ON AA.CASE_ID=T.CASE_ID AND AA. LAST_LIST_ID=T.List_Id
                WHERE 1=1 
                  and ((AA.Approve_Remark != T.Approve_Remark  and aa.LAST_LIST_ID is not null) or aa.LAST_LIST_ID is null)
                AND AA.Audit_Remark is not null
                ) a order by list_type,a.AUDIT_TIME DESC
                
            ]]>
    </select>
    <!--查询此赔案的欺诈案件信息  -->
    <select id="queryCaseCheatList" resultType="java.util.Map"
        parameterType="java.util.Map">
            <![CDATA[ 
             SELECT A.COMMITTING_NATURE,A.INSPECTION_REGISTER_FLAG,A.INSPECTION_REGISTER_TIME,A.CHEAT_DISTINGUISH_CHANNEL,A.OTHER_CHEAT_OPTION,A.CHEAT_IMPLEMENTATION_PERSONNEL,A.OTHER_IMPLEMENTATION_PERSONNEL FROM DEV_CLM.T_CLAIM_CASE_CHEAT A WHERE A.CASE_NO = #{caseNo}
            ]]>
    </select>
    <!--查询此赔案对应的所有回退赔案  -->
     <select id="queryCaseIdList" resultType="java.util.Map"
        parameterType="java.util.Map">
            <![CDATA[ 
        SELECT A.RELATED_NO,A.IS_RISK,A.RISK_LABEL,A.RISK_OTHER_REASON,A.SALESMAN_SELF_INSURANCE,A.CASE_ID,A.Case_No,A.Accept_Decision,A.ADVANCE_FLAG,A.CASE_FLAG,A.MATERIAL_FREE_FLAG,A.MEDICAL_CONNECTION_FLAG,A.REPEAT_NUMBER_FLAG,A.REPEAT_NUMBER_TYPE,A.REPEAT_NUMBER_REASON,A.CHANNEL_CODE,A.REALTIME_PAY,A.SIGNATURE_TRACE_FLAG
              ,A.FACE_RECOGNITION_FLAG,A.IS_SMALL_CASE,A.ACQUIST_WAY,A.AUTO_CASE_TYPE
              FROM DEV_CLM.T_CLAIM_CASE A
             START WITH CASE_NO = #{caseNo}
            CONNECT BY PRIOR RELATED_NO = CASE_NO
            ORDER BY A.Audit_Time DESC
            ]]>
    </select>
    
      <!--查询此赔案对应的所有回退赔案对应的赔付结论和审核结论  -->
     <select id="queryCaseIdListConclusion" resultType="java.util.Map" parameterType="java.util.Map">
            <![CDATA[ 
        SELECT B.LIAB_CONCLUSION,C.AUDIT_DECISION
          FROM DEV_CLM.T_CLAIM_LIAB B,
               DEV_CLM.T_CLAIM_CASE C 
         WHERE B.CASE_ID = C.CASE_ID
           AND C.CASE_ID IN (SELECT A.CASE_ID
                               FROM DEV_CLM.T_CLAIM_CASE A
                              START WITH CASE_NO = #{caseNo}
                         CONNECT BY PRIOR RELATED_NO = CASE_NO)
		
            ]]>
    </select>
      <!--查询此赔案对应的拒付通知书寄送日期  -->
     <select id="queryRejectPayList" resultType="java.util.Map" parameterType="java.util.Map">
            <![CDATA[ 
        SELECT B.*
          FROM DEV_CLM.T_CLAIM_CASE A, DEV_CLM.T_CLAIM_SEND_NOTICE B
         WHERE A.CASE_ID = B.CASE_ID
           AND A.CASE_NO = #{caseNo}
      ORDER BY B.INSERT_TIME DESC
            ]]>
    </select>
    
    
    
    <select id="queryLPJLInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
            <![CDATA[ 
            SELECT (SELECT NAME
		          FROM DEV_CLM.T_CLAIM_ACCEPT_DECISION
		         WHERE CODE = CC.ACCEPT_DECISION) ACCEPT_DECISION /*立案结论*/,
		       (SELECT B.USER_NAME
		          FROM DEV_PAS.T_UDMP_USER B
		         WHERE AA.AUDITOR_ID = B.USER_ID) AS AUDITOR_NAME, /*审核人*/
		       (SELECT B.REAL_NAME
		          FROM DEV_PAS.T_UDMP_USER B
		         WHERE AA.AUDITOR_ID = B.USER_ID) AS AUDITOR_REAL_NAME, /*审核真实名*/
		       (SELECT C.USER_NAME
		          FROM DEV_PAS.T_UDMP_USER C
		         WHERE AA.APPROVER_ID = C.USER_ID) AS APPROVER_NAME, /*审批人*/
		       (SELECT C.REAL_NAME
		          FROM DEV_PAS.T_UDMP_USER C
		         WHERE AA.APPROVER_ID = C.USER_ID) AS APPROVER_REAL_NAME, /*审批真实名*/
		       (SELECT A.REJECT_PROOF
		          FROM DEV_CLM.T_CLAIM_LIAB A
		         WHERE CC.CASE_ID = A.CASE_ID
		           AND ROWNUM = 1) AS REJECT_PROOF， /*拒收依据*/ 
		       CC.CASE_FLAG, /*案件标识*/
		       CC.CHANNEL_CODE,/*申请渠道*/
		       CC.MATERIAL_FREE_FLAG, /*免材料标识*/
               CC.MEDICAL_CONNECTION_FLAG, /*医疗直连标识*/
               CC.REPEAT_NUMBER_FLAG, /*重复账单号标识*/
               CC.REPEAT_NUMBER_TYPE ,/*重复账单类型*/
               CC.REPEAT_NUMBER_REASON ,/*重复账单其他原因*/
		       CC.ADVANCE_FLAG, /*预付标识*/
		       AA.AUDIT_DECISION, /*审核结论*/
		       CC.IS_COMMON, /*是否常规给付代码*/
		       AA.AUDIT_REJECT_REASON, /*审核拒付/审核不通过原因（拒付或者不通过共同使用该字段）*/
		       CC.OTHER_REASON, /*审核其他原因*/
		       AA.AUDIT_REMARK, /*审核意见*/
		       AA.APPROVE_DECISION, /*审批结论*/
		       AA.APPROVE_REJECT_REASON, /*审批不通过原因*/
		       AA.Is_Common AS OVER_COMP_FLAG, /*超期补偿标识*/
		       AA.APPROVE_REMARK, /*审批意见*/
		       AA.AUDITOR_ID, /*审核人ID*/
		       AA.AUDIT_TIME, /*审核时间*/
		       AA.APPROVER_ID, /*审批人ID*/
		       AA.APPROVE_TIME, /*审批时间*/
               AA.Audit_No,/*审核次序号*/
		       CC.SPECIAL_REMARK_CODE, /*特殊备注代码*/
		       CC.OTHER_REASON, /*审核其他原因*/
		       CC.SPECIFIC_CAUSE, /*具体理由*/
		       AA.LIST_ID,
		       CC.REJECT_REASON,
		       CC.EASY_AUDIT_DECISION,
		       AA.OVER_COMP_FLAG AS AU_OVER_COMP_FLAG ,/*审核阶段的超期补偿标识*/
		       AA.IS_OVER_COMP AS AU_IS_OVER_COMP,/*审核阶段的超期标识*/
       		   AA.OVER_REASON AS AU_OVER_REASON,/*审核阶段的超期原因*/
       		   AA.OVER_DAYS AS AU_OVER_DAYS,/*审核阶段的超期天数*/
       		   AA.OVER_DUE_MONEY AS AU_OVER_DUE_MONEY,/*审核阶段的超期核定补偿总金额*/
       		   CC.OVER_COMP_FLAG  AS APP_OVER_COMP_FLAG,/* 审批阶段的超期补偿标识*/
       		   CC.IS_OVER_COMP AS APP_IS_OVER_COMP,/*审批阶段的超期标识*/
       		   CC.OVER_REASON AS APP_OVER_REASON,/*审批阶段的超期原因*/
       		   CC.OVER_DAYS AS APP_OVER_DAYS,/*审批阶段的超期天数*/
       		   CC.OVER_DUE_MONEY AS APP_OVER_DUE_MONEY, /*审批阶段的超期核定补偿总金额*/
       		  CC.Job_Prompt/*作业提示*/
            FROM 
                DEV_CLM.T_CLAIM_CASE CC
                LEFT JOIN DEV_CLM.T_CLAIM_AUDIT_APPROVE AA
  				ON CC.CASE_ID = AA.CASE_ID
            WHERE CC.CASE_ID=#{caseNo}
            ]]>
        <if test=" list_id  != null "><![CDATA[ AND AA.LIST_ID = #{list_id} ]]></if>
        <![CDATA[
           	ORDER BY AA.AUDIT_TIME DESC, AA.APPROVE_TIME DESC
            ]]>
    </select>
     <!--     赔付通知书 受益人和领款人信息 m-->
    <select id="queryBeneFayeeInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT DISTINCT CP.CASE_ID,
              CP.PAYEE_ID,
              CP.BENE_ID,
              CP.BENE_RATE,
              CP.PAY_DENO,
              CP.PAY_MOLE,
              CP.PAY_AMOUNT /*领款金额 受益金额*/,
              CB.BENE_NAME /*受益人姓名*/,
              CC.PAYEE_NAME /*领款人姓名*/,
              CC.ACCOUNT_NO /*银行帐号*/,
              (SELECT PM.NAME
                  FROM DEV_CLM.T_PAY_MODE PM
                 WHERE PM.CODE = CC.PAY_MODE) PAY_MODE /*领款方式*/
   FROM DEV_CLM.T_CLAIM_PAY  CP 
   LEFT JOIN DEV_CLM.T_CLAIM_BENE CB ON  CP.CASE_ID = CB.CASE_ID AND CP.BENE_ID = CB.BENE_ID 
   LEFT JOIN DEV_CLM.T_CLAIM_PAYEE CC ON CP.CASE_ID = CC.CASE_ID AND CP.PAYEE_ID = CC.PAYEE_ID  
   WHERE 1=1  
    AND CP.CASE_ID=#{case_id}     
         ]]>        
        <if test=" case_id  != null "><![CDATA[ AND CP.CASE_ID=#{case_id}]]></if>
      
    </select>
    <!--     赔付通知书 医疗类信息 -->
    <select id="queryMedicalInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT DISTINCT 
                 LI.POLICY_CODE, /*保单号*/
                LI.BUSI_PROD_CODE,
                TBP.PRODUCT_NAME_SYS, /*险种名称*/
                LI.BUSI_ITEM_ID,
                LI.LIAB_ID,
                LI.LIAB_NAME, /*保险责任名称*/
                LI.CLM_AFTER_STATE,
                LI.SURPLUS_EFFECT_AMOUNT, /*剩余有效保额*/
                (SELECT SUM(BI.SUM_AMOUNT)
                   FROM DEV_CLM.T_CLAIM_BILL BI
                  WHERE LI.CASE_ID = BI.CASE_ID) SUM_AMOUNT, /*账单金额*/
                (SELECT SUM(BI.TREAT_END - BI.TREAT_START)
                   FROM DEV_CLM.T_CLAIM_BILL BI
                  WHERE LI.CASE_ID = BI.CASE_ID
                    AND TREAT_TYPE = 1) DAY_TOTAL, /*实际住院天数*/
                nvl(LI.ADVANCE_PAY, 0.00), /*预付金额*/
                LI.ACTUAL_PAY, /*实际赔付金额*/
                LI.CLOB_ID,
                /*TO_CHAR(TC.CONTENT) AS CONTENTES, 备注信息*/
                LI.LIAB_CONCLUSION,
                LI.LIABILITY_STATUS,  
               /* P.LIABILITY_STATE LIABILITY_STATE, *//*合同状态*/
                NVL(FD.OVER_COMP_PAY,0.00), /*超期补偿金额*/
                (SELECT (NVL(FD.RETURNS_PAY_PREM,0.00) - NVL(FD.DEDUCT_PREM,0.00) +
                        NVL(FD.CLAIM_RETURNS,0.00) + NVL(FD.CLAIM_SURRENDER,0.00) -
                       NVL( FD.DEDUCT_LOAN_PRINC,0.00) - NVL(FD.DEDUCT_LOAN_INTEREST,0.00) -
                        NVL(FD.DEDUCT_ANNUITY_SURVIVAL_FUND,0.00) +
                        NVL(FD.RETURNS_UNEXPIRED_PREM,0.00) - NVL(FD.ADVANCE_PAY_AMOUNT,0.00) +
                       NVL( FD.CASH_DIVIDEND,0.00) + NVL(FD.CASH_DIVIDEND_INTEREST,0.00) +
                        NVL(FD.FINISH_INTEREST,0.00) +NVL( FD.MARGIN_BACK,0.00) +
                        NVL(FD.DEDUCT_RESK_PREM,0.00) - NVL(FD.DEDUCT_POLICY_MANA_FEE,0.00) +
                        NVL(FD.NO_COLLAR_BOND,0.00) - NVL(FD.DEDUCT_FULL_GOLD,0.00) -
                        NVL(FD.DEDUCT_HEAP_LIVE_ACCOUNT,0.00) +
                        NVL(FD.ENDD_ADJUST_INTEREST,0.00) +
                        NVL(FD.RETURNS_POLICY_MANA_FEE,0.00) + NVL(FD.RETURNS_RESK_PREM,0.00) +
                        NVL(FD.HEAP_LIVE_ACCOUNT_RETURNS,0.00)) AS SUM_MONEY
                   FROM DEV_CLM.T_CLAIM_FEE_DETAIL FD
                  WHERE FD.CASE_ID = LI.CASE_ID
                    AND FD.POLICY_ID = LI.POLICY_ID
                    AND ROWNUM = 1) CONTRACT_FEE /*合同结算金额*/
                  FROM DEV_CLM.T_CLAIM_LIAB LI
                 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
                    ON TBP.PRODUCT_CODE_SYS = LI.BUSI_PROD_CODE
                 INNER JOIN DEV_CLM.T_CLOB TC
                    ON TC.CLOB_ID = LI.CLOB_ID
                 INNER JOIN  DEV_CLM.T_LIABILITY_STATUS A
                     ON A.STATUS_CODE = LI.LIABILITY_STATUS
                  LEFT JOIN DEV_CLM.T_CLAIM_FEE_DETAIL FD
                    ON FD.POLICY_CODE = LI.POLICY_CODE
                   AND FD.CASE_ID = LI.CASE_ID
                   AND FD.BUSI_PROD_CODE=LI.BUSI_PROD_CODE
                  LEFT JOIN DEV_CLM.T_CLAIM_PRODUCT P
                    ON P.POLICY_CODE = FD.POLICY_CODE AND P.BUSI_PROD_CODE=FD.BUSI_PROD_CODE
                   AND P.CASE_ID = FD.CASE_ID
                 WHERE LI.CLAIM_TYPE = '08'
                   AND LI.LIAB_CONCLUSION != 5
                   AND LI.CASE_ID = #{case_id}
        ]]>
       <if test=" case_id != null and case_id !='' "><![CDATA[  AND LI.CASE_ID = #{case_id} ]]></if>  
       
     </select>
     <!--     赔付通知书 非医疗类信息 -->
    <select id="queryNonMedicalInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT DISTINCT LI.POLICY_CODE, /*保单号*/
                LI.ACTUAL_PAY, /*实际赔付金额*/
                NVL(LI.ACCU_BONUS_AMOUNT,0.00), /*年度红利*/
                LI.SURPLUS_EFFECT_AMOUNT, /*剩余有效保额*/
                LI.CLOB_ID,
                /*TO_CHAR(TC.CONTENT) AS CONTENTS, 备注信息*/
                LI.CASE_ID,
                TBP.PRODUCT_NAME_SYS, /*险种名称*/
                PRO.AMOUNT, /*保险金额*/
                NVL(FD.OVER_COMP_PAY,0.00), /*超期补偿金额*/
                NVL(FD.FINISH_INTEREST,0.00), /*终了红利*/
                (SELECT (NVL(FD.RETURNS_PAY_PREM, 0.00) -
                        NVL(FD.DEDUCT_PREM, 0.00) +
                        NVL(FD.CLAIM_RETURNS, 0.00) +
                        NVL(FD.CLAIM_SURRENDER, 0.00) -
                        NVL(FD.DEDUCT_LOAN_PRINC, 0.00) -
                        NVL(FD.DEDUCT_LOAN_INTEREST, 0.00) -
                        NVL(FD.DEDUCT_ANNUITY_SURVIVAL_FUND, 0.00) +
                        NVL(FD.RETURNS_UNEXPIRED_PREM, 0.00) -
                        NVL(FD.ADVANCE_PAY_AMOUNT, 0.00) +
                        NVL(FD.CASH_DIVIDEND, 0.00) +
                        NVL(FD.CASH_DIVIDEND_INTEREST, 0.00) +
                        NVL(FD.FINISH_INTEREST, 0.00) +
                        NVL(FD.MARGIN_BACK, 0.00) +
                        NVL(FD.DEDUCT_RESK_PREM, 0.00) -
                        NVL(FD.DEDUCT_POLICY_MANA_FEE, 0.00) +
                        NVL(FD.NO_COLLAR_BOND, 0.00) -
                        NVL(FD.DEDUCT_FULL_GOLD, 0.00) -
                        NVL(FD.DEDUCT_HEAP_LIVE_ACCOUNT, 0.00) +
                        NVL(FD.ENDD_ADJUST_INTEREST, 0.00) +
                        NVL(FD.RETURNS_POLICY_MANA_FEE, 0.00) +
                        NVL(FD.RETURNS_RESK_PREM, 0.00) +
                        NVL(FD.HEAP_LIVE_ACCOUNT_RETURNS, 0.00)) AS SUM_MONEY
                   FROM DEV_CLM.T_CLAIM_FEE_DETAIL FD
                  WHERE FD.CASE_ID = LI.CASE_ID
                    AND FD.POLICY_ID = LI.POLICY_ID
                    AND ROWNUM = 1) CONTRACT_FEE, /*合同结算金额*/
                    LI.LIAB_CONCLUSION,
                LI.CLAIM_TYPE
         FROM DEV_CLM.T_CLAIM_LIAB LI
        INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
           ON TBP.PRODUCT_CODE_SYS = LI.BUSI_PROD_CODE
        INNER JOIN DEV_CLM.T_CLOB TC
           ON TC.CLOB_ID = LI.CLOB_ID
         LEFT JOIN DEV_CLM.T_CONTRACT_PRODUCT PRO
           ON PRO.ITEM_ID = LI.ITEM_ID
          AND PRO.CASE_ID = LI.CASE_ID
         LEFT JOIN DEV_CLM.T_CLAIM_FEE_DETAIL FD
           ON FD.POLICY_CODE = LI.POLICY_CODE
          AND FD.CASE_ID = LI.CASE_ID
          WHERE PRO.CUR_FLAG = 1
          AND LI.CLAIM_TYPE !='08'
          AND LI.LIAB_CONCLUSION !=5
          AND LI.CASE_ID = #{case_id}
     ]]>
       <if test=" case_id != null and case_id !='' "><![CDATA[  AND LI.CASE_ID = #{case_id} ]]></if>  
      </select>  
      <!--获取备注信息  -->
      <select id="queryMedicalContet" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
	       	SELECT TC.CONTENT
			  FROM DEV_CLM.T_CLOB TC
			 WHERE 1=1 
	     ]]>
       <if test=" clob_id != null and clob_id !='' "><![CDATA[  AND TC.CLOB_ID = #{clob_id} ]]></if>  
      </select>  
<!-- daizheng1 保项计算信息不分页_NEW add -->
 <!--   (select cp.amount
                               from dev_clm.T_CONTRACT_PRODUCT cp
                              where cp.item_id = cl.item_id
                                and cp.policy_id = cl.policy_id
                                and cp.busi_item_id= cl.busi_item_id
                                and cp.case_id = cl.case_id
                                and cp.cur_flag = 1) amount, -->
    <select id="queryClaimLiabCalculate_new" parameterType="java.util.Map"
        resultType="java.util.Map">
    <![CDATA[  select distinct cl.claim_liab_id,
                            cl.policy_code,
                            cl.busi_prod_code,
                            cl.busi_item_id,
                            cl.Liab_Name,
                            cl.liab_start_date,
                            cl.liab_end_date,
                            cl.calc_pay,
                            cl.adjust_pay,
                            cl.advance_pay,
                            cl.liab_conclusion,
                             (case when nvl(cl.BEFORE_SURPLUS_EFFECT_AMOUNT,0.0)!=0.0 then cl.BEFORE_SURPLUS_EFFECT_AMOUNT
                   when nvl(cl.BEFORE_SURPLUS_EFFECT_AMOUNT,0.0)=0.0 then cp.amount end) as amount,
                            cl.ACCU_BONUS_AMOUNT AS bonus_sa,
                              (select cab.fee_amount
                               from dev_clm.t_claim_adjust_busi cab
                              where cab.policy_id = cl.policy_id
                                and cab.busi_item_id= cl.busi_item_id
                                and cab.case_id = cl.case_id
                                and cab.adjust_type = 9) pay_amount,
                            t.master_busi_item_id
                       from dev_clm.T_CLAIM_LIAB cl
                       LEFT JOIN dev_clm.T_CONTRACT_BUSI_PROD t ON t.case_id = cl.case_id AND t.cur_flag = 1 AND cl.busi_item_id=t.busi_item_id
                       left join dev_clm.t_contract_product cp on t.case_id=cp.case_id and cp.cur_flag=1 and cl.item_id=cp.item_id
                      where cl.case_id = #{case_id}
                      
                        order by   cl.liab_start_date desc, cl.calc_pay
                      ]]>
    </select>
    <!-- 按索引查询操作 -->
    <select id="findClaimLiabByClaimLiabId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
            A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
            A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON,AT.NAME AS LIAB_ADJUST_REASON_NAME, A.MAKEUP_BASIC_PAY, 
            A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
            A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION,CD.NAME AS LIAB_CONCLUSION_NAME, A.POLICY_CODE, A.WAIVE_AMT, 
            A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
            A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK
            ,A.OTHER_REASON/*31544需求变更增加*/
            ,A.LIAB_RISK_LABEL
             FROM dev_clm.T_CLAIM_LIAB A 
            LEFT JOIN   DEV_CLM.T_CLAIM_LIAB_DECISION CD ON CD.CODE=A.LIAB_CONCLUSION
            LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_TYPE AT ON AT.CODE=A.LIAB_ADJUST_REASON
            WHERE 1 = 1  ]]>
        <if test=" claim_liab_id  != null "><![CDATA[ AND A.CLAIM_LIAB_ID = #{claim_liab_id} ]]></if>
        <![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]>
    </select>
    <!-- add by zhaoyq 根据责任组ID查询既往赔付的金额 -->
    <select id="findSumAmountByProductID2" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT nvl(sum(li.actual_pay),0) as amount FROM dev_clm.T_CLAIM_LIAB li, dev_clm.T_CLAIM_CASE ca where li.case_id = ca.case_id and li.liab_conclusion!=5 and ca.case_status='80' and li.product_id= #{product_id} and li.policy_id=#{policy_id} and li.liab_id = #{liab_id}]]>
    </select>
    <select id="findGraceperiod" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ select a.list_id,a.business_prd_id,a.period_type,a.charge_mode,a.urge_flag,a.renew_grace_period,a.grace_period_day from DEV_PDS.T_GRACE_PERIOD a WHERE 1 = 1  ]]>
        <if test=" business_prd_id  != null "><![CDATA[ AND A.business_prd_id = #{business_prd_id} ]]></if>
        <![CDATA[ ORDER BY A.list_id ]]>
    </select>
    <!-- 查询所有操作 -->
    <select id="findAllBusinessProduct" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
            A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
            A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
            A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS, A.WAIVER_FLAG FROM dev_pds.T_BUSINESS_PRODUCT A WHERE ROWNUM <=  1000  ]]>
        <if test=" product_code_sys != null and product_code_sys != ''  "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code_sys} ]]></if>
        <if test=" primary_sales_channel != null and primary_sales_channel != ''  "><![CDATA[AND A.PRIMARY_SALES_CHANNEL IN (${primary_sales_channel})]]> </if>
        <![CDATA[ ORDER BY A.BUSINESS_PRD_ID ]]> 
    </select>
    
    <select id="findAllBusinessProductAtbq" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT b.business_prd_id, b.product_name_sys 
                  FROM dev_pds.t_business_prod_cs a,dev_pds.t_business_product b 
                  where a.business_prd_id = b.business_prd_id          
                  and a.allow_apl = 1  ORDER BY A.BUSINESS_PRD_ID
        ]]>
    </select>
    
    <!-- 理赔综合查询-理赔查询 end -->
    
    
    <!-- 按赔案号查询回退案件 -->
    <select id="findClaimLiabByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, A.CLAIM_LIAB_ID, A.ITEM_ID, 
            A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
            A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
            A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
            A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
            A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN,
            A.REJECT_CODE,A.REJECT_PROOF,A.SPECIAL_REMARK
            ,A.OTHER_REASON
             FROM APP___CLM__DBUSER.T_CLAIM_LIAB A
            WHERE 1 = 1  
            AND A.LIAB_CONCLUSION='2' 
            AND A.CASE_ID =#{caseId}
         ]]>
    </select>
    
    <!-- 根据理赔给付责任理算ID查询赔付过程 -->
	<select id="findClaimClobByClaimLiabId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.CONTENT
				   FROM APP___CLM__DBUSER.t_claim_sub_liab               A,
				        APP___CLM__DBUSER.t_clob                 B,
				        APP___CLM__DBUSER.T_CLAIM_LIAB c
				  where A.CLOB_ID = B.CLOB_ID
				    and c.claim_liab_id = a.claim_liab_id
				    AND c.claim_liab_id = #{claim_liab_id}]]>
	</select>
</mapper>
