<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="contractExtend">

	<sql id="PA_contractExtendWhereCondition">
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" extraction_due_date  != null  and  extraction_due_date  != ''  "><![CDATA[ AND A.EXTRACTION_DUE_DATE = #{extraction_due_date} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" billing_date  != null  and  billing_date  != ''  "><![CDATA[ AND A.BILLING_DATE = #{billing_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" renew_decision_status  != null "><![CDATA[ AND A.RENEW_DECISION_STATUS = #{renew_decision_status} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_period  != null "><![CDATA[ AND A.POLICY_PERIOD = #{policy_period} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" prem_status != null and prem_status != ''  "><![CDATA[ AND A.PREM_STATUS = #{prem_status} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractExtendByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractExtendByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractExtendByPremStatusCondition">
		<if test=" prem_status != null and prem_status != '' "><![CDATA[ AND A.PREM_STATUS = #{prem_status} ]]></if>
	</sql>	
	<sql id="PA_queryContractExtendByExtractionDueDateCondition">
		<if test=" extraction_due_date  != null "><![CDATA[ AND A.EXTRACTION_DUE_DATE = #{extraction_due_date} ]]></if>
	</sql>	
	<sql id="PA_queryContractExtendByBusiItemIdCondition">
		<if test=" busi_item_id != null and busi_item_id != '' "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>
	<sql id="PA_findContractExtendByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>
	

<!-- 按索引查询操作 -->	
	<select id="PA_findContractExtendByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendByListIdCondition" />
	</select>
	
	<select id="PA_findContractExtendByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendByPolicyIdCondition" />
		<![CDATA[
			ORDER BY A.PAY_DUE_DATE DESC
		]]>
	</select>
	
	<select id="PA_findContractExtendByPremStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendByPremStatusCondition" />
	</select>
	
	<select id="PA_findContractExtendByExtractionDueDate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractExtendByExtractionDueDateCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractExtend" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractExtendWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractExtend" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractExtendWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractExtendTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<include refid="PA_contractExtendWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractExtendForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.POLICY_YEAR, B.EXTRACTION_DUE_DATE, B.ITEM_ID, B.BILLING_DATE, 
			B.ORGAN_CODE, B.RENEW_DECISION_STATUS, B.POLICY_CODE, B.POLICY_PERIOD, B.LIST_ID, B.PAY_DUE_DATE, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.PREM_STATUS, A.NEXT_PREM FROM (
					SELECT ROWNUM RN, A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_contractExtendWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 查询单条 -->
	<select id="PA_findContractExtend" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1  ]]>
		<!-- <include refid="PA_queryContractExtendByPolicyIdCondition" />
		<include refid="PA_queryContractExtendByBusiItemIdCondition" />
		<include refid="PA_findContractExtendByItemIdCondition" />
		<include refid="PA_queryContractExtendByListIdCondition" />-->
		<include refid="PA_contractExtendWhereCondition" />
	</select>
	
<!--  -->
	<select id="PA_findContractExtendByBusiItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.POLICY_YEAR, A.POLICY_PERIOD,A.BUSI_ITEM_ID, A.POLICY_ID,TC.PAY_DUE_DATE
              FROM DEV_PAS.T_CONTRACT_EXTEND A,(SELECT MAX(TCE.PAY_DUE_DATE) PAY_DUE_DATE,TCE.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_EXTEND TCE GROUP BY TCE.BUSI_ITEM_ID) TC
                WHERE TC.BUSI_ITEM_ID = A.BUSI_ITEM_ID]]>
		<include refid="PA_queryContractExtendByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

	<select id="PA_findContractExtendByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1 ]]>
		<include refid="PA_findContractExtendByItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="QRY_INTEGRAL_findContractExtendByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, 
			A.ORGAN_CODE, A.RENEW_DECISION_STATUS, A.POLICY_CODE, A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PREM_STATUS, A.NEXT_PREM FROM DEV_PAS.T_CONTRACT_EXTEND A WHERE 1 = 1 ]]>
		<include refid="PA_findContractExtendByItemIdCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

</mapper>
