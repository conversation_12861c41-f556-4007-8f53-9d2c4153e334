<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.ImageDataInfoDaoImpl">

	<sql id="NB_imageDataInfoWhereCondition">
		<if test=" real_name_register_result != null and real_name_register_result != ''  "><![CDATA[ AND A.REAL_NAME_REGISTER_RESULT = #{real_name_register_result} ]]></if>
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND <PERSON>.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" data_save_no != null and data_save_no != ''  "><![CDATA[ AND A.DATA_SAVE_NO = #{data_save_no} ]]></if>
		<if test=" data_info_id  != null "><![CDATA[ AND A.DATA_INFO_ID = #{data_info_id} ]]></if>
		<if test=" unregister_result != null and unregister_result != ''  "><![CDATA[ AND A.UNREGISTER_RESULT = #{unregister_result} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" cust_cert_star_date  != null  and  cust_cert_star_date  != ''  "><![CDATA[ AND A.CUST_CERT_STAR_DATE = #{cust_cert_star_date} ]]></if>
		<if test=" cust_cert_end_date  != null  and  cust_cert_end_date  != ''  "><![CDATA[ AND A.CUST_CERT_END_DATE = #{cust_cert_end_date} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND trunc(A.CUSTOMER_BIRTHDAY) = trunc(#{customer_birthday}) ]]></if>
		<if test=" photo_result != null and photo_result != ''  "><![CDATA[ AND A.PHOTO_RESULT = #{photo_result} ]]></if>
		<if test=" category_tab_code != null and category_tab_code != ''  "><![CDATA[ AND A.CATEGORY_TAB_CODE = #{category_tab_code} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" certi_code_result != null and certi_code_result != ''  "><![CDATA[ AND A.CERTI_CODE_RESULT = #{certi_code_result} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryImageDataInfoByDataInfoIdCondition">
		<if test=" data_info_id  != null "><![CDATA[ AND A.DATA_INFO_ID = #{data_info_id} ]]></if>
	</sql>	
	<sql id="NB_queryImageDataInfoByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="NB_queryImageDataInfoByDataSaveNoCondition">
		<if test=" data_save_no != null and data_save_no != '' "><![CDATA[ AND A.DATA_SAVE_NO = #{data_save_no} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="NB_addImageDataInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="data_info_id">
			SELECT APP___NB__DBUSER.S_IMAGE_DATA_INFO__DATA_INFO_I.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___NB__DBUSER.T_IMAGE_DATA_INFO(
				REAL_NAME_REGISTER_RESULT, CUSTOMER_CERT_TYPE, CUSTOMER_NAME, INSERT_TIME, DATA_SAVE_NO, UPDATE_TIME, DATA_INFO_ID, 
				UNREGISTER_RESULT, APPLY_CODE, CUSTOMER_CERTI_CODE, INSERT_TIMESTAMP, CUST_CERT_STAR_DATE, CUST_CERT_END_DATE, UPDATE_BY, 
				CUSTOMER_BIRTHDAY, UPDATE_TIMESTAMP, PHOTO_RESULT, INSERT_BY, CATEGORY_TAB_CODE, CUSTOMER_GENDER, CERTI_CODE_RESULT ) 
			VALUES (
				#{real_name_register_result, jdbcType=VARCHAR}, #{customer_cert_type, jdbcType=VARCHAR} , #{customer_name, jdbcType=VARCHAR} , SYSDATE , #{data_save_no, jdbcType=VARCHAR} , SYSDATE , #{data_info_id, jdbcType=NUMERIC} 
				, #{unregister_result, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{customer_certi_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{cust_cert_star_date, jdbcType=DATE} , #{cust_cert_end_date, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} 
				, #{customer_birthday, jdbcType=DATE} , CURRENT_TIMESTAMP, #{photo_result, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{category_tab_code, jdbcType=VARCHAR} , #{customer_gender, jdbcType=NUMERIC} , #{certi_code_result, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="NB_deleteImageDataInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___NB__DBUSER.T_IMAGE_DATA_INFO WHERE APPLY_CODE = #{apply_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="NB_updateImageDataInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___NB__DBUSER.T_IMAGE_DATA_INFO ]]>
		<set>
		<trim suffixOverrides=",">
			REAL_NAME_REGISTER_RESULT = #{real_name_register_result, jdbcType=VARCHAR} ,
			CUSTOMER_CERT_TYPE = #{customer_cert_type, jdbcType=VARCHAR} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
			DATA_SAVE_NO = #{data_save_no, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    DATA_INFO_ID = #{data_info_id, jdbcType=NUMERIC} ,
			UNREGISTER_RESULT = #{unregister_result, jdbcType=VARCHAR} ,
			CUSTOMER_CERTI_CODE = #{customer_certi_code, jdbcType=VARCHAR} ,
		    CUST_CERT_STAR_DATE = #{cust_cert_star_date, jdbcType=DATE} ,
		    CUST_CERT_END_DATE = #{cust_cert_end_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CUSTOMER_BIRTHDAY = #{customer_birthday, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			PHOTO_RESULT = #{photo_result, jdbcType=VARCHAR} ,
			CATEGORY_TAB_CODE = #{category_tab_code, jdbcType=VARCHAR} ,
		    CUSTOMER_GENDER = #{customer_gender, jdbcType=NUMERIC} ,
			CERTI_CODE_RESULT = #{certi_code_result, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE APPLY_CODE = #{apply_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="NB_findImageDataInfoByDataInfoId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REAL_NAME_REGISTER_RESULT, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.DATA_SAVE_NO, A.DATA_INFO_ID, 
			A.UNREGISTER_RESULT, A.APPLY_CODE, A.CUSTOMER_CERTI_CODE, A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
			A.CUSTOMER_BIRTHDAY, A.PHOTO_RESULT, A.CATEGORY_TAB_CODE, A.CUSTOMER_GENDER, A.CERTI_CODE_RESULT FROM APP___NB__DBUSER.T_IMAGE_DATA_INFO A WHERE 1 = 1  ]]>
		<include refid="NB_queryImageDataInfoByDataInfoIdCondition" />
		<![CDATA[ ORDER BY A.APPLY_CODE ]]>
	</select>
	
	<select id="NB_findImageDataInfoByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REAL_NAME_REGISTER_RESULT, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.DATA_SAVE_NO, A.DATA_INFO_ID, 
			A.UNREGISTER_RESULT, A.APPLY_CODE, A.CUSTOMER_CERTI_CODE, A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
			A.CUSTOMER_BIRTHDAY, A.PHOTO_RESULT, A.CATEGORY_TAB_CODE, A.CUSTOMER_GENDER, A.CERTI_CODE_RESULT FROM APP___NB__DBUSER.T_IMAGE_DATA_INFO A WHERE 1 = 1  ]]>
		<include refid="NB_queryImageDataInfoByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.APPLY_CODE ]]>
	</select>
	
	<select id="NB_findImageDataInfoByDataSaveNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REAL_NAME_REGISTER_RESULT, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.DATA_SAVE_NO, A.DATA_INFO_ID, 
			A.UNREGISTER_RESULT, A.APPLY_CODE, A.CUSTOMER_CERTI_CODE, A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
			A.CUSTOMER_BIRTHDAY, A.PHOTO_RESULT, A.CATEGORY_TAB_CODE, A.CUSTOMER_GENDER, A.CERTI_CODE_RESULT FROM APP___NB__DBUSER.T_IMAGE_DATA_INFO A WHERE 1 = 1  ]]>
		<include refid="NB_queryImageDataInfoByDataSaveNoCondition" />
		<![CDATA[ ORDER BY A.APPLY_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="NB_findAllMapImageDataInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REAL_NAME_REGISTER_RESULT, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.DATA_SAVE_NO, A.DATA_INFO_ID, 
			A.UNREGISTER_RESULT, A.APPLY_CODE, A.CUSTOMER_CERTI_CODE, A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
			A.CUSTOMER_BIRTHDAY, A.PHOTO_RESULT, A.CATEGORY_TAB_CODE, A.CUSTOMER_GENDER, A.CERTI_CODE_RESULT FROM APP___NB__DBUSER.T_IMAGE_DATA_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APPLY_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllImageDataInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REAL_NAME_REGISTER_RESULT, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.DATA_SAVE_NO, A.DATA_INFO_ID, 
			A.UNREGISTER_RESULT, A.APPLY_CODE, A.CUSTOMER_CERTI_CODE, A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
			A.CUSTOMER_BIRTHDAY, A.PHOTO_RESULT, A.CATEGORY_TAB_CODE, A.CUSTOMER_GENDER, A.CERTI_CODE_RESULT FROM APP___NB__DBUSER.T_IMAGE_DATA_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_imageDataInfoWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findImageDataInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___NB__DBUSER.T_IMAGE_DATA_INFO A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryImageDataInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.REAL_NAME_REGISTER_RESULT, B.CUSTOMER_CERT_TYPE, B.CUSTOMER_NAME, B.DATA_SAVE_NO, B.DATA_INFO_ID, 
			B.UNREGISTER_RESULT, B.APPLY_CODE, B.CUSTOMER_CERTI_CODE, B.CUST_CERT_STAR_DATE, B.CUST_CERT_END_DATE, 
			B.CUSTOMER_BIRTHDAY, B.PHOTO_RESULT, B.CATEGORY_TAB_CODE, B.CUSTOMER_GENDER, B.CERTI_CODE_RESULT FROM (
					SELECT ROWNUM RN, A.REAL_NAME_REGISTER_RESULT, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_NAME, A.DATA_SAVE_NO, A.DATA_INFO_ID, 
			A.UNREGISTER_RESULT, A.APPLY_CODE, A.CUSTOMER_CERTI_CODE, A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
			A.CUSTOMER_BIRTHDAY, A.PHOTO_RESULT, A.CATEGORY_TAB_CODE, A.CUSTOMER_GENDER, A.CERTI_CODE_RESULT FROM APP___NB__DBUSER.T_IMAGE_DATA_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.APPLY_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
