<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IQueryNBPolicyInfoDao">
	<!-- 承保保单查询 -->
	<select id="qry_queryNBPolicyInfos" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT ROWNUM,X.* FROM 
		(SELECT DISTINCT TCM.POLICY_CODE,
                   TCM.APPLY_DATE,
                   TCM.VALIDATE_DATE,
                   TCM.EXPIRY_DATE,
                   TPA.ACCOUNT,
                   TCM.APPLY_DATE AS VALIDATE_TIME,
                   (SELECT TCE.PAY_DUE_DATE
                      FROM DEV_PAS.T_CONTRACT_EXTEND TCE
                     WHERE TCBP.POLICY_CODE = TCE.POLICY_CODE
                       AND TCBP.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID
                       AND TCE.PAY_DUE_DATE =
                           (SELECT MAX(T.PAY_DUE_DATE)
                              FROM DEV_PAS.T_CONTRACT_EXTEND T
                             WHERE TCBP.POLICY_CODE = T.POLICY_CODE
                               AND TCBP.BUSI_ITEM_ID = T.BUSI_ITEM_ID)
                       AND ROWNUM = 1) AS PAY_DUE_DATE,
                   (SELECT TCE.NEXT_PREM
                      FROM DEV_PAS.T_CONTRACT_EXTEND TCE
                     WHERE TCBP.POLICY_CODE = TCE.POLICY_CODE
                       AND TCBP.BUSI_ITEM_ID = TCE.BUSI_ITEM_ID
                       AND TCE.PAY_DUE_DATE =
                           (SELECT MAX(T.PAY_DUE_DATE)
                              FROM DEV_PAS.T_CONTRACT_EXTEND T
                             WHERE TCBP.POLICY_CODE = T.POLICY_CODE
                               AND TCBP.BUSI_ITEM_ID = T.BUSI_ITEM_ID)
                       AND ROWNUM = 1) AS NEXT_PREM,
                   TIO.OPERATION_TIME,
                   (SELECT SUM(TP.FEE_AMOUNT)
                      FROM DEV_PAS.T_PREM TP
                     WHERE TP.POLICY_CODE = TCM.POLICY_CODE
                       AND TP.FEE_SCENE_CODE = 'NB') AS FEE_AMOUNT,
                       (SELECT MAX(TP.FINISH_TIME)
                      FROM DEV_PAS.T_PREM TP
                     WHERE TP.POLICY_CODE = TCM.POLICY_CODE
                       AND TP.FEE_SCENE_CODE = 'NB') AS FINISH_TIME,
                       ( SELECT T.CHARGE_PERIOD FROM DEV_PAS.T_CONTRACT_PRODUCT T WHERE T.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID AND T.POLICY_CODE = TCBP.POLICY_CODE AND TCBP.MASTER_BUSI_ITEM_ID IS NULL AND ROWNUM = 1) AS CHARGE_PERIOD
     FROM DEV_PAS.T_CONTRACT_MASTER TCM
     JOIN DEV_PAS.T_POLICY_CHANGE TPC
       ON TPC.POLICY_ID = TCM.POLICY_ID
     JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
       ON TCBP.POLICY_CODE = TCM.POLICY_CODE
      AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
     JOIN DEV_PAS.T_PAYER_ACCOUNT TPA
       ON TPA.POLICY_ID = TCM.POLICY_ID
     LEFT JOIN DEV_NB.T_ISSUE_OPERATION TIO
       ON TCM.APPLY_CODE = TIO.APPLY_CODE
    WHERE 1 = 1  AND TPC.SERVICE_CODE = '00'
    AND NOT EXISTS (SELECT 1 FROM DEV_PAS.T_CONTRACT_MASTER T WHERE T.POLICY_CODE = TCM.POLICY_CODE AND T.LIABILITY_STATE = '3' AND T.END_CAUSE = '80' AND TCM.APPLY_DATE=T.EXPIRY_DATE)
]]>
      <if test=" policy_code  != null  and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
      <if test=" start_date  != null  and start_date != ''  "><![CDATA[ AND TCM.APPLY_DATE >= TO_DATE(#{start_date},'yyyy-MM-dd') ]]></if>
      <if test=" end_date  != null  and end_date != ''  "><![CDATA[ AND TCM.APPLY_DATE <= TO_DATE(#{end_date},'yyyy-MM-dd') ]]></if>
      <if test=" service_bank  != null and service_bank != ''   "><![CDATA[ AND SERVICE_BANK = #{service_bank} ]]></if>
      <![CDATA[)X]]>
	</select>
</mapper>