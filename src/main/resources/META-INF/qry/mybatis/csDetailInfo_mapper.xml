<?xml version="1.0" encoding="UTF-8"?>
<!--20180924提交  -->
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.impl.task.dao.ICSBusinessQueryDAO">
	<select id="QRY_findDetailInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT AP.CHANGE_ID,AP.APPLY_CODE,AP.NOFILL_FLAG,AP.APPLY_TIME,S.TYPE_NAME AS SERVICE_TYPE,AP.AGENT_NAME,AP.AGENT_CERTI_TYPE,AP.AGENT_CERTI_CODE,AP.AGENT_TEL,
AC.ORGAN_CODE,AC.PRE_FLAG,AC.PRE_VALIDATE_DATE,AC.SERVICE_CODE,(select s.service_name from dev_pas.t_service s where s.service_code=ac.service_code) service_name,AC.ACCEPT_STATUS,AC.VALIDATE_TIME,AP.CUSTOMER_ID,AC.IS_ELEC_SIGN FROM DEV_PAS.T_CHANGE C
INNER JOIN DEV_PAS.T_CS_APPLICATION AP ON AP.CHANGE_ID=C.CHANGE_ID
INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC ON AC.CHANGE_ID=C.CHANGE_ID
INNER JOIN DEV_PAS.T_SERVICE_TYPE S ON S.SERVICE_TYPE=AP.SERVICE_TYPE
WHERE 1=1 ]]>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[AND AC.ACCEPT_CODE= #{accept_code} ]]></if>
        <if test=" change_id != null "><![CDATA[AND AC.CHANGE_ID= #{change_id} ]]></if>
	</select>
	<!-- 通过申请号查询受理号 -->
	<select id="QRY_getAcceptIdByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT AC.ACCEPT_ID, AC.CHANGE_ID, AC.SERVICE_CODE, AC.ACCEPT_CODE,AC.CALL_PHONE1,AC.CALL_PHONE2
			  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
			 WHERE 1 = 1 ]]>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[AND AC.ACCEPT_CODE= #{accept_code} ]]></if>
		<if test=" change_id != null and change_id != ''  "><![CDATA[AND AC.CHANGE_ID= #{change_id} ]]></if>
	</select>
	<!--保全受理明细  -->
<select id="QRY_findApplyDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[SELECT AP.CHANGE_ID,
					       AP.APPLY_CODE,
					       AC.ACCEPT_CODE,
					       AC.ACCEPT_ID,
					       AP.APP_STATUS, --保全申请状态
					       AP.NOFILL_FLAG, --是否免填单
					       AP.APPLY_TIME, --保全申请时间
					       S.TYPE_NAME AS SERVICE_TYPE, --保全申请方式
					       AP.AGENT_NAME, --代办人姓名
					       AP.AGENT_CERTI_TYPE, --代办人证件类型
					       AP.AGENT_CERTI_CODE, --代办人证件号码
					       AP.AGENT_TEL, --代办人联系电话
					       AC.ORGAN_CODE, --操作人员机构号
					       AC.PRE_FLAG, --是否预收理
					       AC.PRE_VALIDATE_DATE, --预定生效日期
					       AC.SERVICE_CODE, --保全项代码
					       (SELECT S.SERVICE_NAME
					          FROM DEV_PAS.T_SERVICE S
					         WHERE S.SERVICE_CODE = AC.SERVICE_CODE) SERVICE_NAME,
					       AC.ACCEPT_STATUS, --受理状态
					       AC.VALIDATE_TIME, --保单生效日
					       AP.CUSTOMER_ID, --申请人客户号
					       AC.IS_ELEC_SIGN, --是否电子签名
					       AP.APPLICANT_TYPE, --保全申请人类型
					       AP.APPLY_NAME, --保全申请人姓名
					       AP.AGENT_CODE, --业务员代码
					       AP.AGENT_LEVEL, --绩优等级
					       AC.URGENT_FLAG, --是否紧急件
					       AC.URGENT_CAUSE, --紧急原因
					       AC.URGENT_DETAIL, --具体紧急原因
					       AC.CONVEN_FLAG, --是否常规业务
					       AC.UNCON_TYPE, --非常规业务类别
					       AC.UNCON_CAUSE, --非常规业务原因
					       CM.POLICY_CODE,
					       AP.ESLEWHERE_FLAG, --保全是否异地办理
					       AC.FACE_FLAG,/*人脸识别标识*/
		             	   AC.SKY_SIGN,/*空中签名*/
				           AC.IDENTITY_FLAG,/*简项标识*/
				           AC.IS_IDENTITY_CHECK,/*是否需要验真*/
					       (SELECT S.STATUS_NAME
					          FROM DEV_PAS.T_LIABILITY_STATUS S
					         WHERE S.STATUS_CODE = CM.LIABILITY_STATE) LIABILITY_STATE, --保单状态
					       NVL((SELECT C.CUSTOMER_NAME
					             FROM DEV_PAS.T_CS_POLICY_HOLDER H, DEV_PAS.T_CUSTOMER C
					            WHERE H.CHANGE_ID = AP.CHANGE_ID
					              AND H.POLICY_ID = CM.POLICY_ID
					              AND H.POLICY_CHG_ID = CPC.POLICY_CHG_ID
					              AND H.OLD_NEW = '1'
					              AND H.CUSTOMER_ID = C.CUSTOMER_ID),
					           (SELECT C1.CUSTOMER_NAME
					              FROM DEV_PAS.T_POLICY_HOLDER H1, DEV_PAS.T_CUSTOMER C1
					             WHERE H1.POLICY_ID = CM.POLICY_ID
					               AND C1.CUSTOMER_ID = H1.CUSTOMER_ID)) AS CUSTOMER_NAME --投保人姓名 
					  FROM DEV_PAS.T_CHANGE C
					 INNER JOIN DEV_PAS.T_CS_APPLICATION AP
					    ON AP.CHANGE_ID = C.CHANGE_ID
					 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
					    ON AC.CHANGE_ID = C.CHANGE_ID
					 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC
					    ON CPC.CHANGE_ID = C.CHANGE_ID
					 INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
					    ON CM.POLICY_ID = CPC.POLICY_ID
					 INNER JOIN DEV_PAS.T_SERVICE_TYPE S
					    ON S.SERVICE_TYPE = AP.SERVICE_TYPE
					 WHERE 1 = 1]]>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[AND AC.ACCEPT_CODE= #{accept_code} ]]></if>
        <if test=" change_id != null "><![CDATA[AND AC.CHANGE_ID= #{change_id} ]]></if>
        <if test=" apply_code != null "><![CDATA[AND AP.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!--通过申请号查询受理号  -->
	<select id="QRY_findAcceptByApply" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[
			SELECT AC.ACCEPT_CODE
			   FROM DEV_PAS.T_CS_APPLICATION AP
			  INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC
			     ON AC.CHANGE_ID = AP.CHANGE_ID
			  WHERE 1 = 1]]>
        <if test=" apply_code != null and apply_code !=''"><![CDATA[AND AP.APPLY_CODE= #{apply_code} ]]></if>
	</select>
	<!--保全申请明细  补退费信息  查询保全表-->
	<select id="queryCSPremArapInfoList" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[
			SELECT T.POLICY_CODE,
			       (SELECT S.SERVICE_NAME
			          FROM DEV_PAS.T_SERVICE S
			         WHERE S.SERVICE_CODE = CAC.SERVICE_CODE) SERVICE_NAME,
			       T.FEE_AMOUNT,
			       (SELECT A.NAME
			          FROM DEV_PAS.T_AR_AP A
			         WHERE A.AR_AP = T.ARAP_FLAG) ARAP_FLAG,
			       (SELECT M.NAME
			          FROM DEV_PAS.T_PAY_MODE M
			         WHERE M.CODE = T.PAY_MODE) PAY_MODE,
			       T.BANK_ACCOUNT,
			       T.BANK_USER_NAME,
			       (SELECT SAFE_ACCOUNT_TYPE
			          FROM DEV_PAS.T_BANK_ACCOUNT ACC
			         WHERE ACC.BANK_ACCOUNT = T.BANK_ACCOUNT
			           AND ACC.BANK_CODE = T.BANK_CODE
			           AND ACC.ACCO_NAME = T.BANK_USER_NAME
			           AND ROWNUM <= 1) SAFE_ACCOUNT_TYPE,
			       (SELECT FS.STATUS_NAME
			          FROM DEV_PAS.T_FEE_STATUS FS
			         WHERE FS.STATUS_CODE = T.FEE_STATUS) STATUS_NAME,
			       NULL AS STATUS_REASON,
			       T.BANK_CODE,
			       CASE
			         WHEN T.BANK_ACCOUNT || T.BANK_USER_NAME =
			              D.ACCOUNT || D.ACCOUNT_NAME THEN
			          '1'
			         ELSE
			          '0'
			       END AS IS_DOW_ACCOUNT,
			       T.Pay_To_Third_Person
			  FROM DEV_PAS.T_CS_PREM_ARAP T
			  LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
			    ON (CAC.ACCEPT_CODE = T.BUSINESS_CODE),
			 DEV_PAS.T_CS_POLICY_CHANGE C,
			 DEV_PAS.T_PAYER_ACCOUNT D
			 WHERE T.DERIV_TYPE = '004'
			   AND CAC.CHANGE_ID = C.CHANGE_ID
			   AND CAC.ACCEPT_ID = C.ACCEPT_ID
			   AND C.POLICY_ID = D.POLICY_ID
			   AND T.POLICY_CODE = C.POLICY_CODE
		 ]]>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[AND T.BUSINESS_CODE= #{accept_code} ]]></if>
        <if test=" change_id != null "><![CDATA[AND CAC.CHANGE_ID= #{change_id} ]]></if>
	</select>
	<!--保全申请明细  补退费信息 查询保单表  -->
	<select id="queryPAPremArapInfoList" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[
			SELECT T.POLICY_CODE,
			        (SELECT S.SERVICE_NAME
			           FROM DEV_PAS.T_SERVICE S
			          WHERE S.SERVICE_CODE = CAC.SERVICE_CODE) SERVICE_NAME,
			        T.FEE_AMOUNT,
			        (SELECT A.NAME
			           FROM DEV_PAS.T_AR_AP A
			          WHERE A.AR_AP = T.ARAP_FLAG) ARAP_FLAG,
			        (SELECT M.NAME
			           FROM DEV_PAS.T_PAY_MODE M
			          WHERE M.CODE = T.PAY_MODE) PAY_MODE,
			        T.BANK_ACCOUNT,
			        T.BANK_USER_NAME,
			        (SELECT SAFE_ACCOUNT_TYPE
			           FROM DEV_PAS.T_BANK_ACCOUNT ACC
			          WHERE ACC.BANK_ACCOUNT = T.BANK_ACCOUNT
			            AND ACC.BANK_CODE = T.BANK_CODE
			            AND ACC.ACCO_NAME = T.BANK_USER_NAME
			            AND ROWNUM <= 1) SAFE_ACCOUNT_TYPE,
			        (SELECT FS.STATUS_NAME
			           FROM DEV_PAS.T_FEE_STATUS FS
			          WHERE FS.STATUS_CODE = T.FEE_STATUS) STATUS_NAME,
			        (SELECT TBRC.BANK_RET_NAME
			           FROM DEV_PAS.T_BANK_RET_CONF TBRC
			          WHERE TBRC.BANK_RET_CODE = T.FUNDS_RTN_CODE) AS STATUS_REASON,
			        T.BANK_CODE,
			        CASE
			          WHEN T.BANK_ACCOUNT || T.BANK_USER_NAME =
			               D.ACCOUNT || D.ACCOUNT_NAME THEN
			           '1'
			          ELSE
			           '0'
			        END AS IS_DOW_ACCOUNT,
			        CPA.PAY_TO_THIRD_PERSON
			   FROM DEV_PAS.V_PREM_ARAP_ALL T
			   LEFT JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
			     ON CAC.ACCEPT_CODE = T.BUSINESS_CODE
		 	  INNER JOIN DEV_PAS.T_CS_PREM_ARAP CPA
			    ON CPA.BUSINESS_CODE = CAC.ACCEPT_CODE
			   AND T.POLICY_CODE = CPA.POLICY_CODE
			   AND T.BUSI_PROD_CODE = CPA.BUSI_PROD_CODE
			   AND T.FEE_TYPE = CPA.FEE_TYPE,
			  DEV_PAS.T_CS_POLICY_CHANGE C,
			  DEV_PAS.T_PAYER_ACCOUNT D
			  WHERE T.DERIV_TYPE = '004'
			    AND CAC.CHANGE_ID = C.CHANGE_ID
			    AND CAC.ACCEPT_ID = C.ACCEPT_ID
			    AND C.POLICY_ID = D.POLICY_ID
			    AND T.POLICY_CODE = C.POLICY_CODE
		 ]]>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[AND T.BUSINESS_CODE= #{accept_code} ]]></if>
        <if test=" change_id != null "><![CDATA[AND CAC.CHANGE_ID= #{change_id} ]]></if>
	</select>

	<select id="QRY_findPolicyHistoryInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT CM.CHANGE_ID,
       CM.POLICY_CODE, --保单号 
       CM.APPLY_CODE, --投保单号
       CM.OLD_NEW, --变更前后标志
       CM.DERIVATION, --保单险种来源
       CM.FORMER_ID, --转换保单的原保单ID
       CM.CHANNEL_TYPE, --保单销售渠道
       CM.INITIAL_VALIDATE_DATE,--续保保单的原始生效日期
       CM.VALIDATE_DATE, --生效日期
       CM.LAPSE_DATE, --预约失效日期
       CM.BRANCH_CODE, --保单所属分公司ID
       CM.ORGAN_CODE, --保单直属机构ID
       CM.MONEY_CODE, --投保币种代码
       CM.ISSUE_DATE, --保单承保日
       CM.SUSPEND_DATE, --责任终止日期
       CM.APPLY_DATE,--投保日期
       CM.LAPSE_CAUSE, --保单失效原因
       CM.APL_PERMIT, --是否允许自垫
       CM.END_CAUSE, --保单终止原因
       MM.CUSTOMER_ID,
       MM.CUSTOMER_NAME,--投保人姓名
       MM.CUST_CERT_STAR_DATE,--投保人证件有效期起期
       MM.CUST_CERT_END_DATE--投保人证件有效期止期
       --保单所属总公司ID
--保单逾期未付的选择
--服务代理人
--是否保单保费假日
--保单挂失状态
--投保人姓名
--保单补发次数
--保单冻结状态
--保单质押第三方状态
  FROM DEV_PAS.T_CS_CONTRACT_MASTER CM
 LEFT JOIN (SELECT PH.OLD_NEW,
                    PH.POLICY_CHG_ID,
                    PH.POLICY_CODE,
                    PH.CHANGE_ID,
                    C.CUSTOMER_ID,
                    C.CUSTOMER_NAME,
                    C.CUST_CERT_END_DATE,
                    C.CUST_CERT_STAR_DATE
               FROM DEV_PAS.T_CS_POLICY_HOLDER PH
              INNER JOIN DEV_PAS.T_CUSTOMER C
                 ON C.CUSTOMER_ID = PH.CUSTOMER_ID) MM
    ON CM.OLD_NEW = MM.OLD_NEW
    AND CM.POLICY_CODE = MM.POLICY_CODE
    AND CM.CHANGE_ID = MM.CHANGE_ID
 WHERE CM.CHANGE_ID = #{change_id} 
 	AND CM.POLICY_CODE = #{policy_code}
		
		]]>
	</select>

	<select id="QRY_findProductInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1)
		FROM DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL CBP
 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
    ON CBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
 INNER JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT CP
    ON CP.CHANGE_ID = CBP.CHANGE_ID
 INNER JOIN DEV_PAS.T_CS_CONTRACT_EXTEND CE
    ON CE.CHANGE_ID = CBP.CHANGE_ID
 WHERE CBP.POLICY_CODE = #{policy_code}
   AND CBP.CHANGE_ID = #{change_id}
   AND CBP.OLD_NEW = '1'
   AND CP.OLD_NEW = '1'
   AND CE.OLD_NEW = '1'
	]]>
	</select>

	<select id="QRY_findProductInfoForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT B.*,B.RN FROM (SELECT ROWNUM RN,
		CBP.CHANGE_ID,
		CBP.POLICY_CODE,
       CBP.BUSI_ITEM_ID,
       CBP.LIABILITY_STATE,--险种状态
       CP.PRODUCT_CODE,---责任组名称
       BP.PRODUCT_NAME_SYS,--险种名称
       BP.PRODUCT_CATEGORY ,--主附险标识
       CE.PAY_DUE_DATE, --下期应交日期
       CE.PREM_STATUS --交费状态
  FROM DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL CBP
 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
    ON CBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
 INNER JOIN DEV_PAS.T_CS_CONTRACT_PRODUCT CP
    ON CP.CHANGE_ID = CBP.CHANGE_ID
 INNER JOIN DEV_PAS.T_CS_CONTRACT_EXTEND CE
    ON CE.CHANGE_ID = CBP.CHANGE_ID
 WHERE CBP.POLICY_CODE = #{policy_code}
   AND CBP.CHANGE_ID = #{change_id}
   AND CBP.OLD_NEW = '1'
   AND CP.OLD_NEW = '1'
   AND CE.OLD_NEW = '1'
		AND ROWNUM <= #{LESS_NUM}
) B WHERE 1=1 AND B.RN > #{GREATER_NUM}]]> 
	</select>
	
	<select id="QRY_findProductHistroyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT P.OLD_NEW,P.ITEM_ID,P.PRODUCT_CODE,P.AMOUNT,P.UNIT,P.APPLY_DATE,P.EXPIRY_DATE,P.LIABILITY_STATE,
P.END_CAUSE,P.PREM_FREQ,P.CHARGE_PERIOD,P.COVERAGE_YEAR,P.BENEFIT_LEVEL,P.CHARGE_YEAR,P.COVERAGE_PERIOD,
P.STD_PREM_AF,P.INITIAL_DISCNT_PREM_AF,P.EXTRA_PREM_AF,P.RENEWAL_DISCNTED_PREM_AF,P.LAPSE_CAUSE,P.VALIDATE_DATE,P.INSERT_BY,P.UPDATE_BY,P.INSERT_TIME,P.UPDATE_TIME,
B.BUSI_PROD_CODE,B.RENEW_DECISION,B.WAIVER_START,B.WAIVER_END,B.WAIVER,B.RENEW,
C.ISSUE_DATE,C.CHANGE_ID,P.PAIDUP_DATE
FROM DEV_PAS.T_CS_CONTRACT_PRODUCT P 
INNER JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL B ON B.OLD_NEW=P.OLD_NEW
INNER JOIN DEV_PAS.T_CS_CONTRACT_MASTER C ON C.OLD_NEW=P.OLD_NEW
WHERE P.PRODUCT_CODE=${product_code} AND P.CHANGE_ID=${change_id} AND B.CHANGE_ID=${change_id} AND C.CHANGE_ID=${change_id}
AND B.BUSI_ITEM_ID=P.BUSI_ITEM_ID AND C.POLICY_CODE=B.POLICY_CODE
		]]>
	</select>
<!-- 保全申请验证信息列表 -->
	<select id="QRY_findIdentityCheckList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT ICM.CHECK_NAME, /*姓名*/
			       ICM.CHECK_ID_CODE, /*身份证号码*/
			       CIT.TYPE_NAME AS CHECK_TYPE, /*验证类型*/
			       ICM.CHECK_DATE, /*验真时间*/
			       (CASE
			         WHEN ICM.CHECK_TYPE = '3' THEN
			          CK.NAME_CHECK_RESULT
			         WHEN ICM.CHECK_TYPE = '2' AND LPC.LOGOUT_STATE = '1' THEN
			          '一致'
			         WHEN ICM.CHECK_TYPE = '2' AND LPC.LOGOUT_STATE = '0' THEN
			          '不一致'
			         ELSE
			          ''
			       END) AS NAME_CHECK_RESULT, /*姓名验真结果*/
			       (CASE
			         WHEN ICM.CHECK_TYPE = '3' THEN
			          CK.ID_CHECK_RESULT
			         WHEN ICM.CHECK_TYPE = '2' AND LPC.LOGOUT_STATE = '1' THEN
			          '一致'
			         WHEN ICM.CHECK_TYPE = '2' AND LPC.LOGOUT_STATE = '0' THEN
			          '不一致'
			         ELSE
			          ''
			       END) AS ID_CHECK_RESULT, /*身份证号码验真结果*/
			       CK.JUDGE_RESULT, /*判断结果*/
			       CK.SIMILARITY, /*相似度*/
			       LPC.LOGOUT_STATE, /*人口状态*/
			       LPC.LOGOUT_REASON, /*原因标识*/
			       (CASE
			         WHEN LPC.LOGOUT_STATE = '1' THEN
			          '一致'
			         WHEN LPC.LOGOUT_STATE = '0' THEN
			          '不一致'
			         ELSE
			          ''
			       END) LOGOUT_STATE_CHECK_RESULT, /*人口状态验证结果*/
			       (CASE
			         WHEN LPC.LOGOUT_REASON IS NULL THEN
			          '不一致'
			         ELSE
			          '一致'
			       END) AS LOGOUT_REASON_CHECK_RESULT /*原因标识验证结果 */
			  FROM DEV_PAS.T_CS_APPLICATION CA
			 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
			    ON CA.CHANGE_ID = CAC.CHANGE_ID
			 INNER JOIN DEV_PAS.T_ORDER_SERVICE OS
			    ON CAC.ACCEPT_CODE = OS.BIZ_CODE
			 INNER JOIN DEV_PAS.T_IDENTITY_CHECK_MAIN ICM
			    ON ICM.BUSINESS_CODE = OS.SERVICE_ORDER_ID
			  LEFT JOIN DEV_PAS.T_PHOTO_COMPARE_CHECK CK
			    ON ICM.IDENTITY_DETAIL_ID = CK.PHOTO_COMPARE_ID
			  LEFT JOIN DEV_PAS.T_LOGOUT_PERSONNEL_CHECK LPC
			    ON LPC.LOGOUT_ID = ICM.IDENTITY_DETAIL_ID
			  LEFT JOIN dev_pas.T_CHECK_IDENTITY_TYPE CIT
           		ON  ICM.CHECK_TYPE=CIT.TYPE_CODE
			 WHERE 1=1
		]]>
		<if test=" apply_code != null and apply_code != ''"><![CDATA[AND CA.APPLY_CODE = #{apply_code,jdbcType=VARCHAR} ]]></if>
	</select>
</mapper>