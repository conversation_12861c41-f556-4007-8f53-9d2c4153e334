<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.dao.IQueryPersonalInsuranceInfoDao">

	<!-- 仅入参银行账号时使用-查询根据银行卡号保单号投保单号信息 -->
	<select id="findPolicyOrApplyCodes"  resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	SELECT distinct VPA.POLICY_CODE, VPA.APPLY_CODE
				  FROM DEV_CAP.V_PREM_ARAP VPA
				 WHERE VPA.BANK_ACCOUNT = #{bank_account}
				   and not exists (SELECT 1
				                 FROM DEV_NB.T_NB_CONTRACT_MASTER T
				                WHERE T.RELATION_APPLY_CODE = VPA.APPLY_CODE
				                union
				                SELECT 1
				                 FROM DEV_PAS.T_CONTRACT_MASTER T
				                WHERE T.RELATION_POLICY_CODE = VPA.POLICY_CODE)]]>
	</select>
	
	<!-- 查询个人保险信息契约保单表保单信息 -->
	<select id="findPersonalInsurancePolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[SELECT B.POLICY_CODE,
       B.APPLY_CODE,
       B.LIABILITY_STATE,
       B.VALIDATE_DATE,
       CASE
         WHEN B.ACCT_NO IS NULL THEN
          (SELECT T.NEXT_ACCOUNT
             FROM DEV_NB.T_NB_PAYER_ACCOUNT T
            WHERE T.APPLY_CODE = B.APPLY_CODE)
         ELSE
          B.ACCT_NO
       END AS ACCT_NO,
       B.CHANNEL_TYPE,
       B.EXPIRY_DATE,
       B.DOUB_MAIN_FLAG,
       B.PROPOSAL_STATUS
  FROM (SELECT A.POLICY_CODE,
       A.APPLY_CODE,
       TNCM.LIABILITY_STATE,
       TNCM.VALIDATE_DATE,
       (SELECT TP.BANK_ACCOUNT
          FROM DEV_CAP.V_PREM_ARAP TP
         WHERE TP.POLICY_CODE = A.POLICY_CODE
           AND TP.DERIV_TYPE IN ('001', '003')
           AND TP.DUE_TIME =
               (SELECT MAX(Z.DUE_TIME)
                  FROM DEV_CAP.V_PREM_ARAP Z
                 WHERE Z.POLICY_CODE = A.POLICY_CODE
                   AND Z.DERIV_TYPE IN ('001', '003'))
           AND ROWNUM = 1) AS acct_no,
       TNCM.CHANNEL_TYPE,
       TNCM.EXPIRY_DATE,
       CASE
         WHEN TNCM.RELATION_APPLY_CODE IS NOT NULL THEN
          1
         WHEN (SELECT T.APPLY_CODE
                 FROM DEV_NB.T_NB_CONTRACT_MASTER T
                WHERE T.RELATION_APPLY_CODE = A.APPLY_CODE) IS NOT NULL THEN
          1
         ELSE
          0
       END AS doub_main_flag,
       TNCM.proposal_status
  FROM (SELECT DISTINCT CASE
                          WHEN (SELECT T.APPLY_CODE
                                  FROM DEV_NB.T_NB_CONTRACT_MASTER T
                                 WHERE T.RELATION_APPLY_CODE = TCM.APPLY_CODE) IS NOT NULL THEN
                           (SELECT T.POLICY_CODE
                              FROM DEV_NB.T_NB_CONTRACT_MASTER T
                             WHERE T.RELATION_APPLY_CODE = TCM.APPLY_CODE)
                          ELSE
                           TCM.POLICY_CODE
                        END AS policy_code,
                        CASE
                          WHEN (SELECT T.APPLY_CODE
                                  FROM DEV_NB.T_NB_CONTRACT_MASTER T
                                 WHERE T.RELATION_APPLY_CODE = TCM.APPLY_CODE) IS NOT NULL THEN
                           (SELECT T.APPLY_CODE
                              FROM DEV_NB.T_NB_CONTRACT_MASTER T
                             WHERE T.RELATION_APPLY_CODE = TCM.APPLY_CODE)
                          ELSE
                           TCM.APPLY_CODE
                        END AS apply_code
          FROM DEV_NB.T_NB_CONTRACT_MASTER TCM, DEV_CAP.V_PREM_ARAP VPA]]>
          <choose>
          	<when test=" (apply_code  != null  and apply_code != '') and (policy_code  == null  or policy_code == '')">
          	<![CDATA[WHERE TCM.APPLY_CODE = VPA.APPLY_CODE
           AND VPA.DERIV_TYPE IN ('001', '003')
           AND VPA.DUE_TIME =
               (SELECT MAX(A.DUE_TIME)
                  FROM DEV_CAP.V_PREM_ARAP A
                 WHERE A.APPLY_CODE = TCM.APPLY_CODE
                   AND A.DERIV_TYPE IN ('001', '003'))]]>
          	</when>
          	<otherwise>
          	<![CDATA[WHERE TCM.POLICY_CODE = VPA.POLICY_CODE
           AND VPA.DERIV_TYPE IN ('001', '003')
           AND VPA.DUE_TIME =
               (SELECT MAX(A.DUE_TIME)
                  FROM DEV_CAP.V_PREM_ARAP A
                 WHERE A.POLICY_CODE = TCM.POLICY_CODE
                   AND A.DERIV_TYPE IN ('001', '003'))]]>
          	</otherwise>
          </choose>
           <![CDATA[AND (TCM.SERVICE_BANK = '64' OR
               (TCM.ORGAN_CODE in (
        SELECT T.ORGAN_CODE FROM APP___PAS__DBUSER.T_UDMP_ORG_REL  T 
          START WITH T.ORGAN_CODE = '8628'
          CONNECT BY PRIOR T.ORGAN_CODE=T.UPORGAN_CODE  )  AND
               VPA.BANK_CODE IN ('8600110', '2800110', '64')))]]>
            <if test=" policy_code  != null  and policy_code != '' "><![CDATA[ AND VPA.POLICY_CODE = #{policy_code} ]]></if>
            <if test=" apply_code  != null  and apply_code != '' "><![CDATA[ AND VPA.APPLY_CODE = #{apply_code} ]]></if>
            <if test=" bank_account  != null and bank_account != ''   "><![CDATA[ AND VPA.BANK_ACCOUNT = #{bank_account} ]]></if>
            <![CDATA[) A,
       DEV_NB.T_NB_CONTRACT_MASTER TNCM
 WHERE A.APPLY_CODE = TNCM.APPLY_CODE) B]]>
	</select>
		<!-- 查询个人保险信息保单层保单信息 -->
	<select id="findPAPersonalInsurancePolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[SELECT TCM.POLICY_CODE,
       TCM.APPLY_CODE,
       TCM.LIABILITY_STATE,
       TCM.VALIDATE_DATE,
       TCM.EXPIRY_DATE,
       CASE
         WHEN TCM.RELATION_POLICY_CODE IS NOT NULL THEN
          1
         WHEN (SELECT T.POLICY_CODE
                 FROM DEV_PAS.T_CONTRACT_MASTER T
                WHERE T.RELATION_POLICY_CODE = TCM.POLICY_CODE) IS NOT NULL THEN
          1
         ELSE
          0
       END AS doub_main_flag
  FROM DEV_PAS.T_CONTRACT_MASTER TCM WHERE TCM.POLICY_CODE = #{policy_code} ]]>
	</select>
	<!-- 查询险种信息 -->
	<select id="findPolicyRiskInfo"  resultType="java.util.Map" parameterType="java.util.Map">
	<if test=" policy_code != null and policy_code != ''  ">
	<![CDATA[SELECT CASE
         WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN
          1
         ELSE
          0
       END is_main_flag,
       CASE
         WHEN (SELECT T.RELATION_POLICY_CODE
           FROM DEV_PAS.T_CONTRACT_MASTER T
          WHERE T.POLICY_CODE = TCBP.POLICY_CODE) IS NOT NULL THEN
          1
         ELSE
          0
       END is_nj,
       TCBP.BUSI_ITEM_ID,
       TCBP.BUSI_PROD_CODE,
       (SELECT T.PRODUCT_NAME_STD
          FROM DEV_PDS.T_BUSINESS_PRODUCT T
         WHERE T.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
           AND ROWNUM = 1) AS busi_prod_name,
       TCBP.LIABILITY_STATE,
       TCBP.MATURITY_DATE
  FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
 WHERE (TCBP.POLICY_CODE = #{policy_code} OR
       TCBP.POLICY_CODE =
       (SELECT A.RELATION_POLICY_CODE
           FROM DEV_PAS.T_CONTRACT_MASTER A
          WHERE A.POLICY_CODE = #{policy_code}) or
       TCBP.POLICY_CODE =
       (SELECT B.POLICY_CODE
           FROM DEV_PAS.T_CONTRACT_MASTER B
          WHERE B.RELATION_POLICY_CODE = #{policy_code}))]]></if>
    <if test=" apply_code != null and apply_code != ''  ">      
	<![CDATA[SELECT CASE
         WHEN TCBP.MASTER_BUSI_ITEM_ID IS NULL THEN
          1
         ELSE
          0
       END is_main_flag,
       TCBP.BUSI_ITEM_ID,
       TCBP.PRODUCT_CODE AS busi_prod_code,
       (SELECT T.PRODUCT_NAME_STD
          FROM DEV_PDS.T_BUSINESS_PRODUCT T
         WHERE T.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID
           AND ROWNUM = 1) AS busi_prod_name,
       TCBP.LIABILITY_STATE,
       NULL AS MATURITY_DATE
  FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD TCBP
 WHERE (TCBP.APPLY_CODE = #{apply_code} OR
       TCBP.APPLY_CODE =
       (SELECT A.RELATION_APPLY_CODE
           FROM DEV_NB.T_NB_CONTRACT_MASTER A
          WHERE A.APPLY_CODE = #{apply_code}) or
       TCBP.APPLY_CODE =
       (SELECT B.APPLY_CODE
           FROM DEV_NB.T_NB_CONTRACT_MASTER B
          WHERE B.RELATION_APPLY_CODE = #{apply_code}))]]></if>
	</select>
	
	<!-- 查询责任组信息 -->
	<select id="findPolicyProductInfo"  resultType="java.util.Map" parameterType="java.util.Map">
	<if test=" policy_code != null and policy_code != ''  ">
	<![CDATA[	
	SELECT TCP.PRODUCT_CODE,
       TCP.CHARGE_PERIOD,
       TCP.CHARGE_YEAR,
       TCP.PREM_FREQ,
       TCP.LIABILITY_STATE,
       TCP.COVERAGE_PERIOD,
       TCP.COVERAGE_YEAR,
       tcp.STD_PREM_AF,
       (SELECT SUM(TP.FEE_AMOUNT)
          FROM DEV_PAS.V_PREM_ALL TP
         WHERE TP.POLICY_CODE = TCP.POLICY_CODE
           AND TP.PRODUCT_CODE = TCP.PRODUCT_CODE
           AND TP.FEE_STATUS IN ('01','16','19')
           AND TP.FEE_SCENE_CODE = 'NB') as init_prem
  FROM DEV_PAS.T_CONTRACT_PRODUCT TCP
 WHERE TCP.BUSI_ITEM_ID = #{busi_item_id}]]></if>
   <if test=" apply_code != null and apply_code != ''  ">      
	<![CDATA[
	SELECT TCP.PRODUCT_CODE,
       TCP.CHARGE_PERIOD,
       TCP.CHARGE_YEAR,
       TCP.PREM_FREQ,
       TCP.LIABILITY_STATE,
       TCP.COVERAGE_PERIOD,
       TCP.COVERAGE_YEAR,
       tcp.STD_PREM_AF,
       (SELECT SUM(TP.FEE_AMOUNT)
          FROM DEV_PAS.V_PREM_ALL TP,
          DEV_PAS.T_CONTRACT_MASTER TCM
         WHERE TP.POLICY_CODE = TCM.POLICY_CODE
           AND TCM.APPLY_CODE = TCP.APPLY_CODE
           AND TP.PRODUCT_CODE = TCP.PRODUCT_CODE
           AND TP.FEE_STATUS IN ('01','16', '19')
           AND TP.FEE_SCENE_CODE = 'NB')as init_prem
  FROM DEV_NB.T_NB_CONTRACT_PRODUCT TCP
 WHERE TCP.BUSI_ITEM_ID = #{busi_item_id}]]></if>
	</select>
</mapper>