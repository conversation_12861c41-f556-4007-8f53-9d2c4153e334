<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.CustomerDetailInfoDaoImpl">

    <!--wumingli 查询客户个人保单角色信息 -->
    <sql id="SQL_queryCustomerPolicyInfos">
         <![CDATA[
            SELECT 
                POLICY_ID,
                POLICYROLE,
                POLICY_CODE,
                CP.ADDRESS_ID,
                AD.ADDRESS,
                AD.STATE,/*省*/
                AD.CITY,/*市 */
                AD.District/*区县 */
            FROM 
            ( ]]>
            <include refid="sqlAllPolicyRole" />
           <![CDATA[
            )  CP
            INNER JOIN DEV_PAS.T_CUSTOMER C
            ON 
               CP.CUSTOMER_ID=C.CUSTOMER_ID
            INNER JOIN DEV_PAS.T_ADDRESS AD
            ON 
                CP.ADDRESS_ID=AD.ADDRESS_ID
            WHERE C.CUSTOMER_ID=#{customer_id}
        ]]>
    </sql>
    <select id="queryCustomerPolicyInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
       <include refid="sqlAllPolicyRole" />
    </select>
    <!-- 查询客户角色带分页 Start  -->
    <select id="queryCustomerPolicyInfosTotal"  resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT COUNT(1) FROM (
        ]]>
       <include refid="SQL_queryCustomerPolicyInfos" />
       <![CDATA[
            ) B 
         ]]>
    </select>
    <select id="queryCustomerPolicyInfosPageInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
        SELECT * FROM (
            SELECT B.*,ROWNUM RN FROM (
        ]]>
       <include refid="SQL_queryCustomerPolicyInfos" />
       <![CDATA[
            ) B 
        WHERE ROWNUM <= #{LESS_NUM}) C WHERE C.RN > #{GREATER_NUM}
         ]]>
    </select>
    <!-- 查询客户角色带分页END  -->
    <sql id="SQL_queryCustomerHistoryInfos" >
        <![CDATA[
        SELECT 
            CA.APPLY_TIME,
            '' AS OPERATOR_DEPT_NAME,
            '' AS OPERATOR_NAME,
            CC.CUSTOMER_NAME,
            CAC.VALIDATE_TIME,
            CC.CUSTOMER_CERTI_CODE,
            CC.CUSTOMER_CERT_TYPE,
            CC.CUSTOMER_BIRTHDAY,
            CC.CUSTOMER_GENDER,
            CC.NATION_CODE,
            CC.RELIGION_CODE,
            CC.MARRIAGE_STATUS
        FROM
            DEV_PAS.T_CS_CUSTOMER CC
        INNER JOIN DEV_PAS.T_CS_APPLICATION CA
        ON 
            CC.CHANGE_ID=CA.CHANGE_ID
        INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
        ON 
            CC.ACCEPT_ID=CAC.ACCEPT_ID
        WHERE CC.CUSTOMER_ID=#{customer_id}
        ]]>
    </sql>
    <select id="queryCustomerHistoryInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
       <include refid="SQL_queryCustomerHistoryInfos" />
    </select>
    <!-- 查询客户历史信息带分页开始 -->
    <select id="queryCustomerHistoryInfosCount" resultType="java.lang.Integer" 
        parameterType="java.util.Map">
      <![CDATA[
        SELECT COUNT(1) FROM (
        ]]>
       <include refid="SQL_queryCustomerHistoryInfos" />
      <![CDATA[
            ) B 
         ]]>
    </select>
    <select id="queryCustomerHistoryPageInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
        SELECT * FROM (
            SELECT B.*,ROWNUM RN FROM (
        ]]>
       <include refid="SQL_queryCustomerHistoryInfos" />
        <![CDATA[
            ) B 
        WHERE ROWNUM <= #{LESS_NUM}) C WHERE C.RN > #{GREATER_NUM}
         ]]>
    </select>
    <!-- 查询客户历史信息带分页结束 -->
    <select id="queryCustomerCreatorHistoryInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            SELECT 
                '' AS OPERATOR_NAME,
                '' AS OPERATOR_DEPT_NAME,
                C.INSERT_TIME
            FROM 
            DEV_PAS.T_CUSTOMER C
            INNER JOIN DEV_PAS.T_UDMP_USER U
            ON C.INSERT_BY=U.USER_ID
            INNER JOIN DEV_PAS.T_UDMP_DEPT D
            ON U.DEPT_ID=D.DEPT_ID
            WHERE C.CUSTOMER_ID=#{customer_id}
      ]]>
    </select>
    <sql id="SQL_queryCustomerAddressHistoryInfos">
        <![CDATA[
           SELECT 
                CA.APPLY_TIME,--开始日期 
                --部门
                --用户
                C.CUSTOMER_NAME,--姓名
                CAC.VALIDATE_TIME,--更新时间
                CC.ADDRESS_ID,--地址编号
                CC.ADDRESS_STATUS,--是否有效,
                CC.ADDRESS_TYPE--地址类型
                --地址标记空着
                --详细信息空着
            FROM
                DEV_PAS.V_CS_ADDRESS_ALL CC
            INNER JOIN DEV_PAS.T_CS_APPLICATION CA
            ON 
                CC.CHANGE_ID=CA.CHANGE_ID
            INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
            ON 
                CC.ACCEPT_ID=CAC.ACCEPT_ID
            INNER JOIN　DEV_PAS.T_CUSTOMER C 
            ON C.CUSTOMER_ID=CC.CUSTOMER_ID
            --INNER JOIN T_UDMP_USER U
            --ON
            --    CC.UPDATE_BY=U.USER_ID
            --INNER JOIN T_UDMP_DEPT D
            --ON
            --    U.DEPT_ID=D.DEPT_ID
            WHERE C.CUSTOMER_ID=#{customer_id}
      ]]>
    </sql>
    <select id="queryCustomerAddressHistoryInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
       <include refid="SQL_queryCustomerAddressHistoryInfos" />
    </select>
    <!-- 查询客户联系信息历史信息带分页开始 -->
    <select id="queryCustomerAddressHistoryInfosCount" resultType="java.lang.Integer" 
        parameterType="java.util.Map">
      <![CDATA[
        SELECT COUNT(1) FROM (
        ]]>
       <include refid="SQL_queryCustomerAddressHistoryInfos" />
      <![CDATA[
            ) B 
         ]]>
    </select>
    <select id="queryCustomerAddressHistoryPageInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
        SELECT * FROM (
            SELECT B.*,ROWNUM RN FROM (
        ]]>
       <include refid="SQL_queryCustomerAddressHistoryInfos" />
        <![CDATA[
            ) B 
        WHERE ROWNUM <= #{LESS_NUM}) C WHERE C.RN > #{GREATER_NUM}
         ]]>
    </select>
    <!-- 查询客户联系信息历史信息带分页结束 -->
    <select id="queryCustomerBankAccountHistoryInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            SELECT 
                CA.APPLY_TIME,--开始日期 
                --部门
                --用户
                C.CUSTOMER_NAME,--姓名
                CAC.Validate_Time,--更新时间
                BA.Account_Id,--账户记录号
                BA.Bank_Account,--银行帐号
                BA.Acco_Name,--账户所有人全名
                BA.Account_Status,--账户状态,
                BA.BANK_CODE,--银行代码,
                BA.Bank_Account_City,--开户城市
                BA.CONFIRMED--账户审批状态不确定显示内容
            FROM
                DEV_PAS.T_CS_BANK_ACCOUNT BA
            INNER JOIN DEV_PAS.T_CS_APPLICATION CA
            ON 
                BA.CHANGE_ID=CA.CHANGE_ID
            INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
            ON 
                BA.ACCEPT_ID=CAC.ACCEPT_ID
            INNER JOIN　DEV_PAS.T_CUSTOMER C 
            ON C.CUSTOMER_ID=BA.CUSTOMER_ID
            --INNER JOIN DEV_PAS.T_UDMP_USER U
            --ON
            --    CC.UPDATE_BY=U.USER_ID
            --INNER JOIN DEV_PAS.T_UDMP_DEPT D
            --ON
            --    U.DEPT_ID=D.DEPT_ID
           WHERE C.CUSTOMER_ID=#{customer_id}
      ]]>
    </select>
    
    <!-- 查询客户密码轨迹 -->
    <select id="queryCustomerPwdModifyHistory" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
      SELECT TCCP.CUSTOMER_ID,
       TCCP.SET_TIME,
       TCCP.UPDATE_BY,
       TC.CUSTOMER_NAME,
       TCCP.SET_CAUSE
       FROM DEV_PAS.T_CS_CUSTOMER_PASSWORD TCCP
       INNER JOIN DEV_PAS.T_CUSTOMER TC
       ON TCCP.CUSTOMER_ID = TC.CUSTOMER_ID 
       WHERE 1=1 AND TCCP.CUSTOMER_ID=#{customer_id}
      ]]>
    </select>
    
    <sql id="SQL_queryCustomerCSInfos">
        <![CDATA[
        SELECT T.*,
		(CASE when T.standardCount>0 THEN '是' else '否' end) AS STANDARD_SIGN --是否是非标史
		FROM (
            SELECT 
              DISTINCT CAC.ACCEPT_CODE AS ACCEPT_CODE,
                (SELECT LISTAGG(CPC.POLICY_CODE,',') WITHIN GROUP( ORDER BY CPC.POLICY_CODE) 
                 FROM   DEV_PAS.T_CS_POLICY_CHANGE CPC
                 WHERE CPC.ACCEPT_ID=CAC.ACCEPT_ID) AS POLICY_CODE,/*#104_14311 */
                (
                SELECT COUNT(*) FROM DEV_UW.T_UW_POLICY UP
                INNER JOIN  DEV_UW.T_UW_MASTER UM ON UP.UW_ID=UM.UW_ID
                 WHERE (UP.POLICY_DECISION='20' OR UP.POLICY_DECISION='40' OR UP.POLICY_DECISION='50')
                  AND UM.UW_SUBMIT_TIME<CA.INSERT_TIME AND UM.Biz_Code=CAC.Accept_Code
                 AND (UM.UW_CANCEL_CAUSE IS NULL OR UM.UW_CANCEL_CAUSE='')
                 ) AS standardCount,    
                CA.APPLY_CODE,--保全申请号
                CAC.ACCEPT_ID,--保全受理ID
                CAC.SERVICE_CODE,--保全项目
                CA.APPLY_TIME,--保全申请日期
                CAC.ACCEPT_STATUS,--保全状态
                CAC.CHANGE_ID
                 FROM 
              DEV_PAS.T_CS_APPLICATION CA           
            INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
            ON
                CAC.CHANGE_ID=CA.CHANGE_ID
            WHERE 1=1
            AND CA.CUSTOMER_ID=#{customer_id}  ]]>
             <include refid="queryCustomerCSCondition" />
          <![CDATA[
           ) T 
         ]]>
       
    </sql>
    <select id="queryCustomerCSInfos" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <include refid="SQL_queryCustomerCSInfos" />
    </select>
    <select id="queryCustomerCSInfosTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT COUNT(1) FROM (
        ]]>
        <include refid="SQL_queryCustomerCSInfos" />
         <![CDATA[
            ) B 
         ]]>
    </select>
    <select id="queryCustomerCSInfosForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
        SELECT * FROM (
            SELECT B.*,ROWNUM RN FROM (
        ]]>
        <include refid="SQL_queryCustomerCSInfos" />
         <![CDATA[
            ) B 
        WHERE ROWNUM <= #{LESS_NUM}) C WHERE C.RN > #{GREATER_NUM}
         ]]>
    </select>
    <sql id="queryCustomerCSCondition">
        <if test=" service_code != null and service_code != ''  "><![CDATA[ AND CAC.SERVICE_CODE=#{service_code} ]]></if>
        <if test=" start_apply_time != null and start_apply_time != ''  "><![CDATA[ AND CA.APPLY_TIME>=#{start_apply_time} ]]></if>
        <if test=" end_apply_time != null and end_apply_time != ''  "><![CDATA[ AND CA.APPLY_TIME<=#{end_apply_time} ]]></if>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[
              AND EXISTS(SELECT * FROM DEV_PAS.T_CS_POLICY_CHANGE CPC WHERE  CPC.POLICY_CODE=#{policy_code}
            AND CPC.ACCEPT_ID=CAC.ACCEPT_ID AND CPC.CHANGE_ID=CAC.CHANGE_ID) 
        ]]></if>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CA.APPLY_CODE=#{apply_code} ]]></if>
        <if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND CAC.ACCEPT_CODE=#{accept_code} ]]></if>
    </sql>
	<sql id="QRY_queryClmHistorySql">
		 <![CDATA[
		SELECT ROWNUM RN, B.*
          FROM (SELECT CC.CASE_NO,/*赔案号*/
                       (SELECT LISTAGG(CL.POLICY_CODE, ',') WITHIN GROUP(ORDER BY CL.POLICY_CODE)
                          FROM (SELECT CL.CASE_ID, CL.POLICY_CODE
                                  FROM DEV_CLM.T_CLAIM_LIAB CL
                                 GROUP BY CL.CASE_ID, CL.POLICY_CODE) CL
                         WHERE CL.case_ID = CC.CASE_ID) AS POLICY_CODE,/*保单号*/
                       CC.CASE_ID, /*赔案ID*/
                       CC.CASE_STATUS,/*案件状态*/
                       C.CUSTOMER_NAME AS TRUSTEE_CODE,/*出险人姓名*/
                       (SELECT MIN(CSC.CLAIM_DATE)
                          FROM DEV_CLM.T_CLAIM_SUB_CASE CSC
                         WHERE CSC.CASE_ID = CC.CASE_ID) AS ACCEPT_TIME,/*出险日期*/
                       CC.AUDIT_DECISION AS LIAB_CONCLUSION,  /*赔付结论*/
                       CC.ACTUAL_PAY/*赔付金额*/
         FROM 　DEV_CLM.T_CLAIM_CASE CC
        INNER JOIN DEV_CLM.T_CUSTOMER C
           ON C.CUSTOMER_ID = CC.INSURED_ID
        WHERE 1 = 1
          AND C.CUSTOMER_ID = #{customer_id}
        ]]>
        <if test=" policy_code != null and policy_code != ''  ">
          <![CDATA[  
          AND EXISTS
           (
           SELECT * FROM DEV_CLM.T_CLAIM_LIAB CL WHERE CL.POLICY_CODE =#{policy_code} AND CL.CASE_ID=CC.CASE_ID
           )
           ]]>
        </if>
        <if test=" case_no != null and case_no != ''  "><![CDATA[ AND CC.CASE_NO =#{case_no} ]]></if>
        <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND C.CUSTOMER_NAME =#{customer_name} ]]></if>
        <if test=" claim_date_st != null and claim_date_st != ''  ">
        	<![CDATA[ AND  EXISTS
        	(
        	SELECT * FROM DEV_CLM.T_CLAIM_SUB_CASE CSC WHERE CSC.CLAIM_DATE >=#{claim_date_st} AND CSC.CASE_ID=CC.CASE_ID
        	)]]>
        </if>
        <if test=" claim_date_end != null and claim_date_end != ''  ">
        <![CDATA[ AND  EXISTS
        	(
        	SELECT * FROM DEV_CLM.T_CLAIM_SUB_CASE CSC WHERE CSC.CLAIM_DATE <=#{claim_date_end} AND CSC.CASE_ID=CC.CASE_ID
        	)]]>
        </if>
        <![CDATA[
        ORDER BY CASE_NO) B
		  ]]>
	</sql>
    <select id="QRY_queryClmHistoryForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
          <![CDATA[
                SELECT B.RN,
               B.POLICY_CODE,
               B.CASE_NO,
               B.CASE_ID,
               B.CASE_STATUS,
               B.TRUSTEE_CODE,
               B.ACCEPT_TIME,
               B.LIAB_CONCLUSION,
               B.ACTUAL_PAY
          FROM (
           ]]>
          <include refid="QRY_queryClmHistorySql" />
         <![CDATA[
          WHERE ROWNUM<= #{LESS_NUM}) B
         WHERE B.RN > #{GREATER_NUM}
		 ]]>
    </select>


    <select id="QRY_queryClmHistoryTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
            SELECT
            COUNT(1)
              FROM (
         ]]>
       <include refid="QRY_queryClmHistorySql" />
       <![CDATA[
           ) clmHis
        ]]>
    </select>

    <select id="QRY_customerLOANInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[
        SELECT count(1)
          FROM 
         (
         ]]>
           <include refid="QRY_customerLOANInfo_SQL" />
           <![CDATA[
           ) loan
        ]]>
    </select>

   <sql id="QRY_customerLOANInfo_SQL">
   		 <![CDATA[
   		    SELECT ROWNUM RN,  
              C.* FROM (SELECT
                       CB.POLICY_CODE,
                       CP.POLICY_ROLE AS APPLY_CODE,/*保单角色*/ 
                       PA.LOAN_START_DATE,/*贷款起始日期*/
                   PA.REPAY_DUE_DATE,/*贷款应偿日*/
                   PA.Act_Repay_Date,/*实际还款日*/
                   PA.Regular_Repay,/*是否清偿*/
                    BP.PRODUCT_NAME_SYS AS CHANNEL_TYPE, /*险种名称*/
                   sum(PA.CAPITAL_BALANCE) AS CAPITAL_BALANCE,
                            
		          max((
					select (SELECT (CASE
					                 WHEN PAS.INTEREST_START_DATE IS NOT NULL THEN
					                  PAS.INTEREST_START_DATE
					                 ELSE
					                  (CASE
					                    WHEN PC.SERVICE_CODE = 'LN' AND
					                         TO_DATE(CI.CONSTANTS_VALUE, 'YYYY-MM-DD') <
					                         PC.VALIDATE_TIME THEN
					                     (SELECT TRUNC(L.FINISH_TIME)
					                        FROM DEV_PAS.T_PREM_ARAP L
					                       WHERE L.BUSINESS_CODE = ACT.ACCEPT_CODE
					                         AND ROWNUM = '1')
					                    ELSE
					                     PC.APPLY_TIME
					                  END)
					               END)
					          FROM DEV_PAS.T_CONSTANTS_INFO CI
					         WHERE CI.CONSTANTS_KEY = 'LOAN_GO_ONLINE_FRONTGE') AS INTEREST_START_DATE
				    from dev_pas.t_cs_policy_change         pc,
				         dev_pas.t_Cs_Policy_Account_Stream pas,
				         dev_pas.t_cs_accept_change         act
				   where act.accept_id = pc.accept_id
   					 and pc.policy_chg_id = pas.policy_chg_id
				     and pas.operation_type = '1'
				     and pas.old_new = '1'
				     and pas.stream_id = PA.stream_id
				     and pc.policy_id = PA.policy_id
		           ) )as INTEREST_START_DATE,/*贷款起息日＃60429 修改*/
                   
                  max((
        select 
       (case when 
        PA.Regular_Repay = '0' then '1' 
        
         when  PA.Regular_Repay = '1' and  to_char(cspc.validate_time,'yyyy-MM-dd')<(SELECT CO.CONSTANTS_VALUE FROM DEV_PAS.T_CONSTANTS_INFO CO 
                  WHERE CO.CONSTANTS_KEY='LOAN_GO_ONLINE_FRONTGE' )
            then '0'
         when  PA.Regular_Repay = '1' and  to_char(cspc.validate_time,'yyyy-MM-dd')>=(SELECT CO.CONSTANTS_VALUE FROM DEV_PAS.T_CONSTANTS_INFO CO 
                  WHERE CO.CONSTANTS_KEY='LOAN_GO_ONLINE_FRONTGE' )
            then '1'     
            
             end 
          )
         from dev_pas.t_cs_policy_change cspc ,
              dev_pas.t_cs_policy_account_stream cspas  
        where  cspc.policy_chg_id = cspas.policy_chg_id 
        and cspc.policy_id = PA.Policy_Id 
        and cspas.stream_id = PA.stream_id
        and cspas.old_new = '1' 
        and cspas.operation_type <> '0'
        and cspas.busi_item_id = PA.busi_item_id
        and cspas.account_type = '4' 
        and cspc.service_code in ('RL','LN')
         and rownum = '1'
      ) ) AS REPAY_FLAG/*已清偿标识*/    
                  FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM PA
                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CB ON CB.POLICY_ID=PA.POLICY_ID AND CB.BUSI_ITEM_ID=PA.BUSI_ITEM_ID
                   INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                            ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
                 INNER JOIN 
                 (
                  SELECT Policy_Id, CUSTOMER_ID,LISTAGG(T.POLICYROLE,',') WITHIN GROUP( ORDER BY T.Policy_CODE,T.POLICY_ID) AS POLICY_ROLE
                 FROM (
                 ]]>
                 <include refid="sqlAllPolicyRole" />
                 <![CDATA[ ) T GROUP BY T.POLICY_ID,T.CUSTOMER_ID,T.POLICY_CODE ) CP
        ON CP.POLICY_ID = CB.POLICY_ID
        WHERE 1=1
          AND CP.CUSTOMER_ID = #{customer_id}
          GROUP BY CB.Policy_Code,CP.POLICY_ROLE,BP.PRODUCT_NAME_SYS,PA.Loan_Start_Date,PA.Repay_Due_Date,PA.Act_Repay_Date,PA.REGULAR_REPAY
           ORDER BY PA.Loan_Start_Date DESC, CB.POLICY_CODE) C 
        ]]>
   </sql>

    <select id="QRY_customerLOANInfoForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        
         SELECT B.RN,
               B.POLICY_CODE,
               B.APPLY_CODE,/*保单角色*/
               B.CHANNEL_TYPE ,/*--险种名称*/
               '' AS DERIVATION ,/*--责任组名称*/
               B.CAPITAL_BALANCE,
               B.LOAN_START_DATE,
		       B.REPAY_DUE_DATE,
		       B.Act_Repay_Date,
		       B.INTEREST_START_DATE,/*贷款起息日*/
		       B.Regular_Repay,
		       B.REPAY_FLAG
          FROM (
           ]]>
           <include refid="QRY_customerLOANInfo_SQL" />
          <![CDATA[
          WHERE ROWNUM <= #{LESS_NUM}) B
         WHERE B.RN > #{GREATER_NUM}
          ]]>
    </select>

    <select id="QRY_customerBONUSInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[
             SELECT 
                count(8)
              FROM DEV_PAS.T_BONUS_ALLOCATE BA
             INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
                ON BA.POLICY_ID = CM.POLICY_ID
             INNER JOIN (
             ]]>
                <include refid="sqlAllPolicyRole" />
               <![CDATA[
             ) CP
                ON CP.POLICY_ID = CM.POLICY_ID
             WHERE CP.CUSTOMER_ID =  #{customer_id}
             
         ]]>
    </select>

    <select id="QRY_customerBONUSInfoForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
         SELECT B.RN,
       B.POLICY_CODE,
       B.APPLY_CODE,
       B.CHANNEL_TYPE,
       B.DERIVATION,
       B.INPUT_DATE
  FROM (SELECT ROWNUM RN,
               CM.POLICY_CODE,
               CP.POLICYROLE AS APPLY_CODE,
               (SELECT BP.PRODUCT_NAME_SYS
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD CB
                 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                    ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
                 INNER JOIN　DEV_PAS.T_CONTRACT_PRODUCT CP ON　CP.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
                 WHERE CP.ITEM_ID = BA.ITEM_ID) AS CHANNEL_TYPE,
               (SELECT PL.PRODUCT_NAME
                  FROM DEV_PDS.T_PRODUCT_LIFE PL
                 INNER JOIN　 DEV_PAS.T_CONTRACT_PRODUCT　CP
                    ON CP.PRODUCT_ID = PL.PRODUCT_ID
                 WHERE CP.ITEM_ID = BA.PRODUCT_ID) AS DERIVATION,
               
               BA.ALLOCATE_DATE AS INPUT_DATE
        
          FROM DEV_PAS.T_BONUS_ALLOCATE BA
         INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
            ON BA.POLICY_ID = CM.POLICY_ID
         INNER JOIN (
         ]]>
                <include refid="sqlAllPolicyRole" />
               <![CDATA[
         ) CP
            ON CP.POLICY_ID = CM.POLICY_ID
         WHERE ROWNUM <= #{LESS_NUM}
           AND CP.CUSTOMER_ID = #{customer_id}) B
 WHERE B.RN > #{GREATER_NUM}
         ]]>
    </select>

    <select id="QRY_customerPAYAddInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[
         SELECT count(8)
  FROM DEV_PAS.T_POLICY_ACCOUNT PA
 INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
    ON PA.POLICY_ID = CM.POLICY_ID
 INNER JOIN (]]>
                <include refid="sqlAllPolicyRole" />
               <![CDATA[) CP
    ON PA.POLICY_ID = CP.POLICY_ID
 WHERE CP.CUSTOMER_ID = #{customer_id}
         
          ]]>
    </select>
    <select id="QRY_customerPAYAddInfoForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
         SELECT B.RN, B.POLICY_CODE, B.POLICY_PWD, B.APPLY_CODE, b.INTEREST_CAPITAL
  FROM (SELECT ROWNUM RN,
               CM.POLICY_CODE, --保单号吗
               (SELECT BP.PRODUCT_NAME_SYS
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD CB
                 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                    ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
                 WHERE CB.BUSI_ITEM_ID = PA.BUSI_ITEM_ID) AS POLICY_PWD, --险种名称
               (SELECT PL.PRODUCT_NAME
                  FROM DEV_PDS.T_PRODUCT_LIFE PL
                 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
                    ON PL.PRODUCT_ID = CP.PRODUCT_ID
                 WHERE CP.ITEM_ID = PA.ITEM_ID) AS APPLY_CODE, --责任组名称
               PA.INTEREST_CAPITAL --金额
          FROM DEV_PAS.T_POLICY_ACCOUNT PA
         INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
            ON PA.POLICY_ID = CM.POLICY_ID
         INNER JOIN (]]>
                <include refid="sqlAllPolicyRole" />
               <![CDATA[) CP
            ON PA.POLICY_ID = CP.POLICY_ID
         WHERE CP.CUSTOMER_ID = #{customer_id}
           AND ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
         
          ]]>
    </select>

    <select id="QRY_customerPAYSuvInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[
         SELECT count(8)
  FROM DEV_PAS.T_PAY_PLAN PP
 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CB
    ON PP.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
 INNER JOIN DEV_PAS.T_PAY_PLAN_PAYEE PPP
    ON PPP.PLAN_ID = PP.PLAN_ID
 WHERE PPP.CUSTOMER_ID = #{customer_id}
          ]]>
    </select>
    <select id="QRY_customerPAYSuvInfoForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
        SELECT B.RN,
       B.POLICY_CODE,
       (
           case 
             when HODER_COUNT>0 and INSURED_COUNT>0 and BENE_COUNT>0 then '投保人,被保险人,受益人'
             when HODER_COUNT>0 and INSURED_COUNT>0 and BENE_COUNT<=0  then '投保人,被保险人'
             when HODER_COUNT>0 and BENE_COUNT>0 and INSURED_COUNT<=0 then '投保人,受益人'
             when INSURED_COUNT>0 and BENE_COUNT>0 and HODER_COUNT<=0 then '被保人,受益人'
             when HODER_COUNT>0 and INSURED_COUNT=0 and BENE_COUNT=0 then '投保人'
             when HODER_COUNT=0 and INSURED_COUNT>0 and BENE_COUNT=0 then '被保险人'
             when HODER_COUNT=0 and INSURED_COUNT=0 and BENE_COUNT>0 then '受益人'
            end
       ) as derivation, /*保单角色*/
       B.POLICY_PWD,
       B.APPLY_CODE,
       B.CHANNEL_TYPE,
       B.LIAB_ID,
       B.BEGIN_DATE,
       B.END_DATE,
       B.PAY_STATUS,
       B.PLAN_FREQ,
       B.INSTALMENT_AMOUNT,
       B.TOTAL_AMOUNT,
       B.PAY_YEAR
  FROM (
    select B.*,ROWNUM as RN from (
    SELECT    PP.POLICY_CODE, /*保单号*/
               (SELECT BP.PRODUCT_NAME_SYS
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD CB
                 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                    ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
                 WHERE CB.BUSI_ITEM_ID = PP.BUSI_ITEM_ID) AS POLICY_PWD, /*险种名称*/
               (SELECT PL.PRODUCT_NAME
                  FROM DEV_PDS.T_PRODUCT_LIFE PL
                 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
                    ON PL.PRODUCT_ID = CP.PRODUCT_ID
                 WHERE CP.ITEM_ID = PP.ITEM_ID) AS APPLY_CODE, /*责任组名称*/
               (SELECT LIAB_NAME
                  FROM DEV_PAS.T_LIABILITY L
                 WHERE L.LIAB_ID = PP.LIAB_ID) AS CHANNEL_TYPE, /*责任名称*/
               PP.LIAB_ID, /*责任ID */
               PP.BEGIN_DATE, /*给付开始日期*/
               PP.END_DATE, /*给付结束日期*/
               PP.PAY_STATUS, /*给付状态*/
               PP.PLAN_FREQ, /*给付频率*/
                (case when PP.PAY_STATUS='2' then PP.INSTALMENT_AMOUNT else null end) AS INSTALMENT_AMOUNT,/*下期给付金额*/
               ( SELECT SUM(PD.FEE_AMOUNT) FROM DEV_PAS.T_PAY_DUE PD WHERE 
                (PD.FEE_STATUS='01' OR PD.FEE_STATUS='16') AND PD.PLAN_ID=PP.PLAN_ID ) AS TOTAL_AMOUNT ,/*已给付总金额*/
               (SELECT nvl(COUNT(*),0)  FROM DEV_PAS.T_PAY_DUE PD 
               WHERE PD.PLAN_ID=PP.PLAN_ID AND 
               (PD.FEE_STATUS='01' OR PD.FEE_STATUS='16')
               ) AS PAY_YEAR,  /*已领期数 */
               (
               SELECT 
               nvl(COUNT(*),0) FROM DEV_PAS.T_POLICY_HOLDER PH  WHERE PH.POLICY_CODE=PP.POLICY_CODE AND PH.CUSTOMER_ID=#{customer_id})  AS HODER_COUNT, /*投保人角色*/
               (
               SELECT 
                nvl(COUNT(*),0) FROM DEV_PAS.T_INSURED_LIST IL  WHERE IL.POLICY_CODE=PP.POLICY_CODE AND IL.CUSTOMER_ID=#{customer_id})  AS INSURED_COUNT,/*被保人角色*/
               (
               SELECT 
                COUNT(*) FROM DEV_PAS.T_CONTRACT_BENE CB  WHERE CB.POLICY_CODE=PP.POLICY_CODE AND CB.CUSTOMER_ID=#{customer_id})  AS BENE_COUNT /*受益人角色*/
        FROM DEV_PAS.T_PAY_PLAN PP
        INNER JOIN DEV_PAS.T_PAY_PLAN_PAYEE PPP
        ON PPP.PLAN_ID = PP.PLAN_ID
        WHERE PPP.CUSTOMER_ID = #{customer_id} order by pp.policy_code asc
        ) B where 1=1 
        AND ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
          ]]>
    </select>

 <select id="QRY_customerPAYDetailInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[
         SELECT COUNT(8)
        FROM DEV_PAS.T_PAY_DUE PD
         INNER JOIN dev_pas.t_pay_plan t
             ON T.PLAN_ID = PD.PLAN_ID
         INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CB
            ON PD.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
         INNER JOIN DEV_PAS.T_PAY_PLAN_PAYEE　PPP ON　PPP.PLAN_ID = T.PLAN_ID
         LEFT JOIN DEV_PAS.T_SURVIVAL_MODE SM
         ON SM.MODE_CODE = PD.SURVIVAL_MODE
         WHERE PPP.CUSTOMER_ID = #{customer_id} AND  (PD.FEE_STATUS='01' OR PD.FEE_STATUS='16' OR PD.FEE_STATUS='00')
          ]]>
    </select>
   <select id="QRY_customerPAYDetailInfoForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
         SELECT B.RN,
       B.POLICY_CODE,
       B.POLICY_PWD,
       B.APPLY_CODE,
       B.CHANNEL_TYPE,
       B.SURVIVAL_INVEST_FLAG,
       B.FEE_AMOUNT,
       B.FEE_STATUS,
       B.PAY_DUE_DATE,
       B.CONFIRM_DATE,
       B.INITIAL_PREM_DATE,
       B.SURVIVAL_MODE,
       B.REAL_PAY_DATE,
       B.MODE_NAME
  FROM (SELECT ROWNUM RN,B.* FROM(
            SELECT 
               PD.POLICY_CODE,
               (SELECT BP.PRODUCT_NAME_SYS
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD CB
                 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                    ON CB.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
                 WHERE CB.BUSI_ITEM_ID = PD.BUSI_ITEM_ID) AS POLICY_PWD,
               (SELECT PL.PRODUCT_NAME
                  FROM DEV_PDS.T_PRODUCT_LIFE PL
                 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
                    ON PL.PRODUCT_ID = CP.PRODUCT_ID
                 WHERE CP.ITEM_ID = PD.ITEM_ID) AS APPLY_CODE,
               (SELECT LIAB_NAME
                  FROM DEV_PAS.T_LIABILITY L
                 WHERE L.LIAB_ID = PD.LIAB_ID) AS CHANNEL_TYPE,
                (select SURVIVAL_INVEST_FLAG
                    FROM DEV_PAS.T_PAY_PLAN TAP 
                    WHERE TAP.PLAN_ID = PD.PLAN_ID) AS SURVIVAL_INVEST_FLAG,
               PD.FEE_AMOUNT,
               PD.PAY_DUE_DATE,
                ( SELECT PAR.FEE_STATUS FROM DEV_CAP.V_PREM_ARAP PAR WHERE PAR.UNIT_NUMBER=PD.UNIT_NUMBER AND ROWNUM=1 ) AS FEE_STATUS,
               ( SELECT  PAR.FINISH_TIME FROM DEV_CAP.V_PREM_ARAP PAR WHERE PAR.UNIT_NUMBER=PD.UNIT_NUMBER AND ROWNUM=1 ) AS REAL_PAY_DATE,
               ( SELECT  PAR.STATISTICAL_DATE FROM DEV_CAP.V_PREM_ARAP PAR WHERE PAR.UNIT_NUMBER=PD.UNIT_NUMBER AND ROWNUM=1 ) AS CONFIRM_DATE,
               CB.INITIAL_PREM_DATE,
               PD.SURVIVAL_MODE,
               SM.MODE_NAME
          FROM DEV_PAS.T_PAY_DUE PD
         INNER JOIN dev_pas.t_pay_plan t
             ON T.PLAN_ID = PD.PLAN_ID
         INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CB
            ON PD.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
         INNER JOIN DEV_PAS.T_PAY_PLAN_PAYEE　PPP ON　PPP.PLAN_ID = T.PLAN_ID
         LEFT JOIN DEV_PAS.T_SURVIVAL_MODE SM
         ON SM.MODE_CODE = PD.SURVIVAL_MODE
         WHERE PPP.CUSTOMER_ID = #{customer_id} AND (PD.FEE_STATUS='01' OR PD.FEE_STATUS='16' OR PD.FEE_STATUS='00')
         ORDER BY PD.PAY_DUE_DATE DESC,PD.POLICY_CODE
         ) B WHERE 1=1
           AND ROWNUM <= #{LESS_NUM}) B
 WHERE B.RN > #{GREATER_NUM}
         
          ]]>
    </select>
    <!--wumingli 所有保单所有角色-->
    <sql id="sqlAllPolicyRole">
         <![CDATA[
        SELECT 
            PH.Policy_Id,/*保单ID*/
            '投保人' AS POLICYROLE,/*客户角色*/
            PH.POLICY_CODE,/*保单号*/
            PH.CUSTOMER_ID,/*客户记录编号*/
            PH.ADDRESS_ID/*保单地址编号*/
        FROM 
        DEV_PAS.T_POLICY_HOLDER PH 
         ]]>
         <if test=" customer_id != null and customer_id != ''  "><![CDATA[ WHERE CUSTOMER_ID=#{customer_id} ]]></if>
        <![CDATA[
        UNION
        SELECT 
           IL.Policy_Id,/*保单ID */
           '被保险人' AS POLICYROLE,  /*客户角色*/
           IL.POLICY_CODE, /*保单号*/
           IL.CUSTOMER_ID,/*客户编号*/
           IL.ADDRESS_ID/*地址编号*/
        FROM 
        DEV_PAS.T_INSURED_LIST IL 
        ]]>
        <if test=" customer_id != null and customer_id != ''  "><![CDATA[ WHERE CUSTOMER_ID=#{customer_id} ]]></if>
        <![CDATA[
        UNION 
        SELECT 
           CB.Policy_Id,/*保单ID*/
           '受益人' AS POLICYROLE,  /*受益人角色*/
           CB.POLICY_CODE, /*保单号*/
           CB.CUSTOMER_ID,/*客户编号*/
           CB.ADDRESS_ID/*地址编号*/
        FROM 
        DEV_PAS.T_CONTRACT_BENE CB 
         ]]>
         <if test=" customer_id != null and customer_id != ''  "><![CDATA[WHERE CUSTOMER_ID=#{customer_id} ]]></if>
    </sql>
    <!-- <select id="PA_QryPhBonus" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
         SELECT  ph.policy_id,ph.policy_code,ph.apply_code,ph.customer_id,
    ba.allocate_type,
    ba.bonus_allot,
    ba.bonus_sa,
    ba.ALLOCATE_DATE,
    to_char(ba.ALLOCATE_DATE,'yyyy') year,
    '投保人' role, BP.product_code_sys, PTL.Product_Id
    FROM DEV_PAS.T_POLICY_HOLDER PH left join dev_pas.t_bonus_allocate ba on ba.policy_id = ph.policy_id  
    left join DEV_PAS.T_CONTRACT_PRODUCT cp on cp.policy_code=ph.policy_code
    left join DEV_PAS.T_CONTRACT_BUSI_PROD CBP  ON CBP.BUSI_ITEM_ID=CP.BUSI_ITEM_ID
     LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON CBP.BUSI_PROD_CODE=BP.PRODUCT_CODE_SYS
       LEFT JOIN  DEV_PDS.T_PRODUCT_LIFE PTL ON　CP.PRODUCT_ID=PTL.PRODUCT_ID
    where 1=1 and ph.customer_id=#{customer_id}
          ]]>
    </select> -->
    <select id="PA_QryILBonus" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
        SELECT il.policy_id,il.policy_code,il.apply_code,il.customer_id,il.relation_to_ph,
ba.allocate_type,ba.bonus_allot,ba.bonus_sa,ba.ALLOCATE_DATE,to_char(ba.ALLOCATE_DATE,'yyyy') year,
'被保人' role, BP.product_code_sys,PTL.Product_Id 
FROM  DEV_PAS.T_INSURED_LIST IL 
left join dev_pas.t_bonus_allocate ba on ba.policy_id = il.policy_id 
left join DEV_PAS.T_CONTRACT_PRODUCT cp on cp.policy_code=il.policy_code
left join DEV_PAS.T_CONTRACT_BUSI_PROD CBP  ON CBP.BUSI_ITEM_ID=CP.BUSI_ITEM_ID
LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON CBP.BUSI_PROD_CODE=BP.PRODUCT_CODE_SYS
LEFT JOIN  DEV_PDS.T_PRODUCT_LIFE PTL ON　CP.PRODUCT_ID=PTL.PRODUCT_ID
where 1=1 and il.customer_id= #{customer_id} ]]>
    </select>
    <sql id="sqlQryCustomerBonus">
         SELECT A.*
		  FROM (SELECT TO_CHAR(TO_CHAR(NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE),'YYYY') - 1) AS YEAR,
		               BP.PRODUCT_CODE_SYS,
		               PTL.PRODUCT_ID,
		               BA.POLICY_ID,
		               BA.POLICY_CODE,
		               BA.ALLOCATE_TYPE, /**红利类别*/
		               BA.BONUS_ALLOT, /**红利类型*/
		               BA.BONUS_RATE, /**分红率*/
		               BA.TERMINAL_BONUS_RATE AS FINISH_BONUS_RATE, /**终了红利率*/
		               (BA.Valid_Amount-BA.STAND_AMOUNT) AS SUM_BONUS_SA, /**累计红利保额*/
		               BA.BONUS_SA, /**红利保额*/
		               BA.CASH_BONUS, /**现金红利*/
		               BA.REISSUE_INTEREST AS CASH_INTEREST, /*现金红利利息*/
		               NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE) AS ALLOCATE_DATE, /*应分配日期*/
		               BA.ALLOCATE_DATE AS REALLO_DATE, /*实际分配日期*/
		               (SELECT TO_CHAR(POLICYROLE)
		                  FROM (SELECT P.POLICY_ID,
		                               P.POLICY_CODE,
		                               (WMSYS.WM_CONCAT(P.ROLE)) AS POLICYROLE
		                          FROM (SELECT PH1.POLICY_ID,
		                                       PH1.POLICY_CODE,
		                                       PH1.CUSTOMER_ID,
		                                       '投保人' AS ROLE
		                                  FROM DEV_PAS.T_POLICY_HOLDER PH1
		                                 WHERE PH1.CUSTOMER_ID = #{customer_id}
		                                UNION
		                                SELECT IL.POLICY_ID,
		                                       IL.POLICY_CODE,
		                                       IL.CUSTOMER_ID,
		                                       '被保险人' AS ROLE
		                                  FROM DEV_PAS.T_INSURED_LIST IL
		                                 WHERE IL.CUSTOMER_ID = #{customer_id}) P
		                         GROUP BY P.POLICY_ID, P.POLICY_CODE) PO
		                 WHERE PO.POLICY_ID = BA.POLICY_ID
		                   AND PO.POLICY_CODE = BA.POLICY_CODE) AS POLICYROLE
		          FROM DEV_PAS.T_BONUS_ALLOCATE BA
		          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
		            ON CP.POLICY_ID = BA.POLICY_ID
		           AND CP.POLICY_CODE = BA.POLICY_CODE
		          LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		            ON CBP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
		           AND CBP.POLICY_ID = CP.POLICY_ID
		           AND CBP.POLICY_CODE = CP.POLICY_CODE
		          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		            ON CBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
		          LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PTL ON　CP.PRODUCT_ID = PTL.PRODUCT_ID
		         WHERE BA.BONUS_ALLOT != 4
		           AND BA.POLICY_ID IN
		               (SELECT PH1.POLICY_ID
		                  FROM DEV_PAS.T_POLICY_HOLDER PH1
		                 WHERE PH1.CUSTOMER_ID = #{customer_id}
		                UNION
		                SELECT IL.POLICY_ID
		                  FROM DEV_PAS.T_INSURED_LIST IL
		                 WHERE IL.CUSTOMER_ID = #{customer_id})
		        UNION ALL
		        SELECT TO_CHAR(TO_CHAR(NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE),'YYYY') - 1) AS YEAR,
		               BP.PRODUCT_CODE_SYS,
		               PTL.PRODUCT_ID,
		               BA.POLICY_ID,
		               BA.POLICY_CODE,
		               BA.ALLOCATE_TYPE, /**红利类别*/
		               BA.BONUS_ALLOT, /**红利类型*/
		               BA.BONUS_RATE, /**分红率*/
		               BA.TERMINAL_BONUS_RATE AS FINISH_BONUS_RATE, /**终了红利率*/
		               (BA.Valid_Amount-BA.STAND_AMOUNT) AS SUM_BONUS_SA, /**累计红利保额*/
		               BA.BONUS_SA, /**红利保额*/
		               BA.CASH_BONUS, /**现金红利*/
		               BA.REISSUE_INTEREST AS CASH_INTEREST, /*现金红利利息*/
		               NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE) AS ALLOCATE_DATE, /*应分配日期*/
		               BA.ALLOCATE_DATE AS REALLO_DATE, /*实际分配日期*/
		               (SELECT TO_CHAR(POLICYROLE)
		                  FROM (SELECT P.POLICY_ID,
		                               P.POLICY_CODE,
		                               (WMSYS.WM_CONCAT(P.ROLE)) AS POLICYROLE
		                          FROM (SELECT PH1.POLICY_ID,
		                                       PH1.POLICY_CODE,
		                                       PH1.CUSTOMER_ID,
		                                       '投保人' AS ROLE
		                                  FROM DEV_PAS.T_POLICY_HOLDER PH1
		                                 WHERE PH1.CUSTOMER_ID = #{customer_id}
		                                UNION
		                                SELECT IL.POLICY_ID,
		                                       IL.POLICY_CODE,
		                                       IL.CUSTOMER_ID,
		                                       '被保险人' AS ROLE
		                                  FROM DEV_PAS.T_INSURED_LIST IL
		                                 WHERE IL.CUSTOMER_ID = #{customer_id}) P
		                         GROUP BY P.POLICY_ID, P.POLICY_CODE) PO
		                 WHERE PO.POLICY_ID = BA.POLICY_ID
		                   AND PO.POLICY_CODE = BA.POLICY_CODE) AS POLICYROLE
		          FROM DEV_PAS.T_BONUS_ALLOCATE BA
		          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
		            ON CP.POLICY_ID = BA.POLICY_ID
		           AND CP.POLICY_CODE = BA.POLICY_CODE
		           AND BA.ITEM_ID = CP.ITEM_ID
		          LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		            ON CBP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
		           AND CBP.POLICY_ID = CP.POLICY_ID
		           AND CBP.POLICY_CODE = CP.POLICY_CODE
		          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		            ON CBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
		          LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PTL ON　CP.PRODUCT_ID = PTL.PRODUCT_ID
		         WHERE BA.BONUS_ALLOT = 4
		         AND  (BA.VALID_FLAG IS NULL OR BA.VALID_FLAG = 1)/*非回退的红利信息*/
		         AND BA.POLICY_ID IN
		               (SELECT PH1.POLICY_ID
		                  FROM DEV_PAS.T_POLICY_HOLDER PH1
		                 WHERE PH1.CUSTOMER_ID = #{customer_id}
		                UNION
		                SELECT IL.POLICY_ID
		                  FROM DEV_PAS.T_INSURED_LIST IL
		                 WHERE IL.CUSTOMER_ID = #{customer_id})) A
		 ORDER BY A.POLICY_ID, A.POLICY_CODE, TO_CHAR(A.ALLOCATE_DATE, 'yyyy')
    </sql>
    <select id="PA_QryPhBonus" resultType="java.util.Map"
        parameterType="java.util.Map">
        <include refid="sqlQryCustomerBonus" />
    </select> 
    <select id="findQryPhBonusTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
          <![CDATA[
        SELECT COUNT(1) FROM (
        ]]>
                <include refid="sqlQryCustomerBonus" />
         <![CDATA[
            ) B 
         ]]>
    </select>
    <select id="findQryPhBonusInfoPage" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[
        SELECT * FROM (
            SELECT B.*,ROWNUM RN FROM (
        ]]>
                <include refid="sqlQryCustomerBonus" />
         <![CDATA[
            ) B 
        WHERE ROWNUM <= #{LESS_NUM}) C WHERE C.RN > #{GREATER_NUM}
         ]]>
    </select>
    <!-- 分页查询客户信息的核保前置风险信息数据总条数 -->
    <select id="QRY_findBeforeUwRiskInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
    	<![CDATA[
		    SELECT COUNT(0)
		      FROM (]]>
		      <include refid="QRY_findBeforeUwRiskInfoSql" />
		<![CDATA[
		 )]]>
    </select>
    <!-- 分页查询客户信息的核保前置风险信息-->
    <select id="QRY_findBeforeUwRiskInfo" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
	    	SELECT B.* FROM(
	           SELECT A.*,ROWNUM RN FROM( 
	        ]]>
	         <include refid="QRY_findBeforeUwRiskInfoSql" />   
	       <![CDATA[
			 ) A where rownum <= #{LESS_NUM} 
	     ) B where RN > #{GREATER_NUM}]]>
    </select>
    <!-- 查询客户信息的核保前置风险信息-->
    <sql id="QRY_findBeforeUwRiskInfoSql">
    	<![CDATA[            
			SELECT PI.CUSTOMER_NAME,
			       PI.CUSTOMER_ID_CODE AS CUSTOMER_CERTI_CODE,
			       PI.RISK_INFO,
			       U.USER_NAME AS INPUT_OPERATOR_CODE,
			       PI.IMAGE_BUSS_CODE /** 影像业务号*/
			  FROM DEV_UW.T_UW_PRE_INFO PI
			  LEFT JOIN DEV_PAS.T_UDMP_USER U
        		ON U.USER_ID=PI.ENTRY_USER
			 WHERE PI.CUSTOMER_NAME = #{customer_name}
			   AND PI.CUSTOMER_ID_CODE = #{customer_certi_code}
			   AND PI.IS_VALID = 1
    	]]>
    </sql>
    <!-- 查询  新契约信息 被保险人详细信息 -->
	<select id="NB_queryInsuredDetailInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
            SELECT C.CUSTOMER_ID,
                   IL.RELATION_TO_PH,
                   IL.APPLY_CODE,
                   C.CUSTOMER_NAME,
                   C.CUSTOMER_CERT_TYPE,
                   C.CUSTOMER_CERTI_CODE,
                   C.CUSTOMER_GENDER,
                   C.CUSTOMER_BIRTHDAY,
                   A.COUNTRY_CODE,
                   C.CUST_CERT_STAR_DATE,
                   C.CUST_CERT_END_DATE,
                   C.TAX_RESIDENT_TYPE,
                   C.MARRIAGE_STATUS,
                   C.IS_PARENT,
                   IL.JOB_CODE,
                   C.JOB_KIND,
                   JT.TITLE_DESC AS JOB_TITLE,
                   C.DRIVER_LICENSE_TYPE,
                   A.ADDRESS,
                   A.POST_CODE,
                   C.OFFEN_USE_TEL AS OFFICE_TEL,
                   A.MOBILE_TEL,
                   C.COMPANY_NAME,
                   A.STATE,
                   A.CITY,
                   A.DISTRICT,
                   C.EMAIL,
                   C.CUSTOMER_RISK_LEVEL,
                  JU.JOB_UW_LEVEL_NAME  AS JOB_UNDER_WRITE,
             /*TRUNC (MONTHS_BETWEEN (NCM.APPLY_DATE,C.CUSTOMER_BIRTHDAY)/12) */
             (CASE WHEN NCM.VALIDATE_DATE IS NOT NULL THEN 
				       TRUNC(MONTHS_BETWEEN(NCM.VALIDATE_DATE, C.CUSTOMER_BIRTHDAY) / 12) 
				       ELSE 
				       TRUNC(MONTHS_BETWEEN(NCM.APPLY_DATE, C.CUSTOMER_BIRTHDAY) / 12) 
				       END) AS INSURED_AGE, /*被保人年龄*/ 
             	    C.SECOND_CERT_TYPE,/* #48816第二证件类型*/ 
             	    C.SECOND_CERTI_CODE, /* #48816第二证件号码*/
             	   IL.ANNUAL_INCOME_CEIL,
	               IL.SOCI_SECU,
	               IL.RESIDENT_TYPE,
	               IL.INCOME_SOURCE,
	               IL.INS_SPE_PEOPLE,IL.RURAL_POPULATION_FLAG,IL.DISABILITY_FLAG,IL.DISABILITY_NO,IL.AGENT_RELATION,
	               NCM.APPLY_DATE,NCM.SUBMIT_CHANNEL,IL.NEW_RESIDENT,NCM.organ_code
              FROM DEV_NB.T_NB_INSURED_LIST IL 
              LEFT JOIN]]> 
                <if test=" his_list_id != null">
                   DEV_PAS.T_CUSTOMER_B C 
	            </if>
	            <if test=" his_list_id == null">
	               DEV_NB.T_CUSTOMER C 
	            </if>
              <![CDATA[
                ON IL.CUSTOMER_ID = C.CUSTOMER_ID
              LEFT JOIN DEV_NB.T_ADDRESS A
                ON A.ADDRESS_ID = IL.ADDRESS_ID
              LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM
                ON NCM.APPLY_CODE=IL.APPLY_CODE
              LEFT JOIN DEV_NB.T_JOB_UNDERWRITE JU
                ON JU.JOB_UW_LEVEL_CODE=IL.JOB_UNDERWRITE
              LEFT JOIN DEV_NB.T_JOB_TITLE JT
                ON C.JOB_TITLE=JT.TITLE_CODE                             
             WHERE 1=1
		]]> 
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND IL.CUSTOMER_ID = #{customer_id,jdbcType=NUMERIC} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND IL.APPLY_CODE = #{apply_code,jdbcType=VARCHAR} ]]></if>
		<if test=" his_list_id != null and his_list_id != ''  "><![CDATA[ AND C.LIST_ID = #{his_list_id,jdbcType=NUMERIC} ]]></if>
	</select>	
		<select id="QRY_findAllConstantsInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONSTANTS_DESC, A.CONSTANTS_VALUE, A.CONSTANTS_KEY, A.CONSTANTS_ID, A.SUB_ID 
		FROM DEV_NB.T_CONSTANTS_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="QRY_queryConstantsInfoByConstantsKey" />
		<![CDATA[ ORDER BY A.CONSTANTS_ID ]]> 
	</select>
	
	<sql id="QRY_queryConstantsInfoByConstantsKey">
		<if test=' constants_key  != null and constants_key != ""  and constants_key.contains("%")  '><![CDATA[ AND A.CONSTANTS_KEY LIKE (${constants_key}) ]]></if>
		<if test=' constants_key  != null and constants_key != ""  and !constants_key.contains("%")  '><![CDATA[ AND A.CONSTANTS_KEY = #{constants_key} ]]></if>
	</sql>
	<!-- 查询  新契约信息 受益人详细信息 -->
	<select id="NB_queryBeneDetailInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT (CASE
				         WHEN NCBP.WAIVER = 1 THEN
				          (SELECT T.CUSTOMER_NAME
				             FROM DEV_NB.T_NB_POLICY_HOLDER PH, DEV_NB.T_CUSTOMER T
				            WHERE PH.LIST_ID = NCB.INSURED_ID
				              AND PH.CUSTOMER_ID = T.CUSTOMER_ID)
				         WHEN NCBP.WAIVER = 0 THEN
				          (SELECT T.CUSTOMER_NAME
				             FROM DEV_NB.T_NB_INSURED_LIST IL, DEV_NB.T_CUSTOMER T
				            WHERE IL.LIST_ID = NCB.INSURED_ID
				              AND IL.CUSTOMER_ID = T.CUSTOMER_ID)
				       END) AS INSURED_NAME, /**所属被保险人 */
				       BP.PRODUCT_NAME_SYS, /*所属险种*/
				       NCB.DESIGNATION,
				       NCB.CUSTOMER_NAME,
				       NCB.CUSTOMER_GENDER,
				       NCB.CUSTOMER_BIRTHDAY,
				       NCB.CUSTOMER_CERT_TYPE,
				       NCB.CUSTOMER_CERTI_CODE,
				       NCB.JOB_CODE,
				       HIS.CUST_CERT_STAR_DATE,
				       HIS.CUST_CERT_END_DATE,
				       /*TRUNC(MONTHS_BETWEEN(NCM.APPLY_DATE, NCB.CUSTOMER_BIRTHDAY) / 12) AS BENE_AGE,*//*受益人年龄*/
				       
				       (CASE WHEN NCM.VALIDATE_DATE IS NOT NULL THEN 
				       TRUNC(MONTHS_BETWEEN(NCM.VALIDATE_DATE, NCB.CUSTOMER_BIRTHDAY) / 12) 
				       ELSE 
				       TRUNC(MONTHS_BETWEEN(NCM.APPLY_DATE, NCB.CUSTOMER_BIRTHDAY) / 12) 
				       END)AS BENE_AGE,/*受益人年龄*/
				       
				       A.COUNTRY_CODE,
				       NCB.SHARE_ORDER, /*收益顺序*/
				       NCB.SHARE_RATE, /*收益份额*/
				       A.STATE,
				       A.CITY,
				       A.DISTRICT,
				       A.ADDRESS, 
				       A.MOBILE_TEL,
				       A.FIXED_TEL AS OFFICE_TEL,
				       A.POST_CODE,
				       C.CUSTOMER_LEVEL,
				       C.CUSTOMER_RISK_LEVEL,
				       NCB.AGENT_RELATION,
				       A.Email, /*电子邮箱*/
		               HIS.TAX_RESIDENT_TYPE ,/* 受益人税收居民身份 */
		               NCM.APPLY_DATE
				  FROM DEV_NB.T_NB_CONTRACT_BENE NCB
				  LEFT JOIN DEV_NB.T_CUSTOMER C
				    ON C.CUSTOMER_ID = NCB.CUSTOMER_ID
				 INNER JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP
				    ON NCBP.BUSI_ITEM_ID = NCB.BUSI_ITEM_ID
				 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
				    ON BP.BUSINESS_PRD_ID = NCBP.BUSI_PRD_ID
				  LEFT JOIN DEV_NB.T_ADDRESS A
				    ON NCB.ADDRESS_ID = A.ADDRESS_ID
				LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM
     				ON NCM.APPLY_CODE= NCB.APPLY_CODE
     			LEFT JOIN DEV_NB.T_NB_CONTRACT_CUSTOMER_HIS HIS
             		ON 	 NCB.BENE_ID = HIS.ROLE_ID	AND  HIS.ROLE_TYPE = '03'			    
				 WHERE 1=1                              
		]]> 
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND NCB.CUSTOMER_ID = #{customer_id,jdbcType=NUMERIC} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND NCB.APPLY_CODE = #{apply_code,jdbcType=VARCHAR} ]]></if>
	</select>
	
	
		<!-- 查询客户风险事件广播（理赔）信息-->
	<select id="customerRiskBroadcast" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLMBRO_ID, A.SCENE_NAME, A.LABEL_NAME, A.DST_NAME, A.DST_CERT_TYPE, 
      A.DST_CERTI_CODE, A.APPKEY, A.POLICY_CODE,A.CUSTOMER_NAME,A.CUSTOMER_CERT_TYPE,
      A.CUSTOMER_CERTI_CODE,A.CUSTOMER_ID,A.INFO_SOURCE,A.RECEIVE_TIME
       FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER A 
       INNER JOIN (SELECT DST_NAME,DST_CERT_TYPE,DST_CERTI_CODE FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER 
       WHERE CUSTOMER_ID = #{customer_id} ) B
       ON B.DST_NAME=A.DST_NAME AND B.DST_CERT_TYPE=A.DST_CERT_TYPE AND B.DST_CERTI_CODE=A.DST_CERTI_CODE
       WHERE ROWNUM <=  1000
			]]>
		<![CDATA[ ORDER BY A.SCENE_NAME,LABEL_NAME ]]> 
	</select>
	<!-- 查询  新契约信息 保单层被保险人详细信息(#137417-取消投保环节被保险人邮箱采集需求-综合查询) -->
	<select id="NB_queryInsuredContractCustomerHisInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
            SELECT a.customer_id, /*被保人客户id*/
			       a.relation_to_ph, /* 与投保人关系 */
			       a.agent_relation, /** 与业务员关联关系 */
			       b.customer_name, /** 客户名称*/
			       b.customer_cert_type, /** 证件类型 */
			       b.customer_certi_code, /** 证件号码 */
			       b.second_cert_type, /** 第二证件类型 */
			       b.second_certi_code, /** 第二证件号码 */
			       b.customer_gender, /** 被保险人性别 */
			       b.customer_birthday, /** 出生日期 */
			       e.country_code, /** 国籍编码 */
			       b.cust_cert_star_date, /** 证件有效起期 */
			       b.cust_cert_end_date, /** 证件有效止期 */
			       /*trunc(months_between(g.apply_date, b.customer_birthday) / 12) */
			       (CASE WHEN G.VALIDATE_DATE IS NOT NULL THEN 
				       TRUNC(MONTHS_BETWEEN(G.VALIDATE_DATE, B.CUSTOMER_BIRTHDAY) / 12) 
				       ELSE 
				       TRUNC(MONTHS_BETWEEN(G.APPLY_DATE, B.CUSTOMER_BIRTHDAY) / 12) 
				       END) as insured_age, /*被保人年龄*/ 
			       b.tax_resident_type, /* 税收居民身份 */
			       b.marriage_status, /** 婚姻状况 */
			       b.is_parent, /** 子女状况 */
			       a.job_code, /** 职业代码 */
			       a.job_underwrite, /** 职业类别 */
			       b.job_title, /** 职务 */
			       h.driver_license_type, /** 驾照类型 */
			       e.state, /** 省/直辖市 */
			       e.city, /** 市 */
			       e.district, /** 县 */
			       e.address, /** 详细地址 */
			       e.post_code, /** 邮政编码 */
			       e.mobile_tel, /** 移动电话 */
			       e.fixed_tel office_tel, /* 联系电话 */
			       e.company_name, /** 工作单位 */
			       e.email, /** 邮箱 */
			       h.customer_risk_level, /** 客户风险等级 */
			       a.annual_income_ceil, /** 年收入 */
			       a.income_source, /** 收入来源 */
			       a.soci_secu, /** 社保 */
			       a.ins_spe_people, /* 特殊人员类型 1脱贫户、2边缘户、3-乡村人口、4-残疾人 */
			       a.disability_no, /* 残疾人证号码 */
			       a.apply_code, /*投保单号*/
			       g.organ_code, /*机构编码*/
			       a.rural_population_flag, /**  乡村人口标识 */
			       a.disability_flag, /** 残疾人标识 */
			       a.resident_type, /*居民类型*/
			       a.educational_background,/*是否本科以上*/
             	   a.non_smoker,/*是否非吸烟者*/
             	   a.new_resident,/*新市民*/
			       G.Submit_Channel,
			       G.Apply_Date
			  FROM DEV_NB.t_Nb_Insured_List A
			 INNER JOIN DEV_NB.T_NB_CONTRACT_CUSTOMER_HIS B
			    ON A.APPLY_CODE = B.APPLY_CODE
			 INNER JOIN DEV_NB.T_ADDRESS E
			    ON A.ADDRESS_ID = E.ADDRESS_ID
			  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER G
			    ON G.POLICY_ID = A.POLICY_ID
			  LEFT JOIN DEV_NB.T_CUSTOMER H
			    ON H.CUSTOMER_ID = B.CUSTOMER_ID
			 WHERE A.LIST_ID = B.ROLE_ID
			   AND B.ROLE_TYPE IN ('02', '04')
			   AND A.Apply_Code = #{apply_code,jdbcType=VARCHAR}
			   and a.customer_id = #{customer_id,jdbcType=NUMERIC}
			 ORDER BY A.LIST_ID
		]]> 
	</select>
</mapper>
