<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.pa.dao.IBankAccountDao">

	<sql id="PA_bankAccountWhereCondition">
		<if test=" account_status != null and account_status != ''  "><![CDATA[ AND A.ACCOUNT_STATUS = #{account_status} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
		<if test=" issue_bank_name != null and issue_bank_name != ''  "><![CDATA[ AND <PERSON>.ISSUE_BANK_NAME = #{issue_bank_name} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" account_type  != null "><![CDATA[ AND A.ACCOUNT_TYPE = #{account_type} ]]></if>
		<if test=" verified_flag  != null "><![CDATA[ AND A.VERIFIED_FLAG = #{verified_flag} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" verified_pass_flag  != null "><![CDATA[ AND A.VERIFIED_PASS_FLAG = #{verified_pass_flag} ]]></if>
		<if test=" certi_end_date  != null  and  certi_end_date  != ''  "><![CDATA[ AND A.CERTI_END_DATE = #{certi_end_date} ]]></if>
		<if test=" certi_start_date  != null  and  certi_start_date  != ''  "><![CDATA[ AND A.CERTI_START_DATE = #{certi_start_date} ]]></if>
		<if test=" cause_desc != null and cause_desc != ''  "><![CDATA[ AND A.CAUSE_DESC = #{cause_desc} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" confirmed  != null "><![CDATA[ AND A.CONFIRMED = #{confirmed} ]]></if>
		<if test=" bank_account_city != null and bank_account_city != ''  "><![CDATA[ AND A.BANK_ACCOUNT_CITY = #{bank_account_city} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" acco_name != null and acco_name != ''  "><![CDATA[ AND A.ACCO_NAME = #{acco_name} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryBankAccountByAccountIdCondition">
		<if test=" account_id  != null "><![CDATA[ AND A.ACCOUNT_ID = #{account_id} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByBankCodeCondition">
		<if test=" bank_code != null and bank_code != '' "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByBankAccountCondition">
		<if test=" bank_account != null and bank_account != '' "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByAccountTypeCondition">
		<if test=" account_type != null and account_type != '' "><![CDATA[ AND A.ACCOUNT_TYPE = #{account_type} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByAccoNameCondition">
		<if test=" acco_name != null and acco_name != '' "><![CDATA[ AND A.ACCO_NAME = #{acco_name} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByCertiCodeCondition">
		<if test=" certi_code != null and certi_code != '' "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="PA_queryBankAccountByAccountStatusCondition">
		<if test=" account_status != null and account_status != '' "><![CDATA[ AND A.ACCOUNT_STATUS = #{account_status} ]]></if>
	</sql>	

<!-- 按索引查询操作 -->	
	<select id="PA_findBankAccountByAccountId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME,nvl(A.SAFE_ACCOUNT_TYPE,0) AS SAFE_ACCOUNT_TYPE FROM DEV_PAS.T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByAccountIdCondition" />
	</select>
	
	<select id="PA_findBankAccountByBankCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByBankCodeCondition" />
	</select>
	
	<select id="PA_findBankAccountByBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByBankAccountCondition" />
	</select>
	
	<select id="PA_findBankAccountByAccountType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByAccountTypeCondition" />
	</select>
	
	<select id="PA_findBankAccountByAccoName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByAccoNameCondition" />
	</select>
	
	<select id="PA_findBankAccountByCertiCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByCertiCodeCondition" />
	</select>
	
	<select id="PA_findBankAccountByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByCustomerIdCondition" />
	</select>
	
	<select id="PA_findBankAccountByAccountStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_queryBankAccountByAccountStatusCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_bankAccountWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME,nvl(A.SAFE_ACCOUNT_TYPE,0) AS SAFE_ACCOUNT_TYPE FROM DEV_PAS.T_BANK_ACCOUNT A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_bankAccountWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findBankAccountTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_bankAccountWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryBankAccountForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ACCOUNT_STATUS, B.BANK_ACCOUNT, B.ACCOUNT_ID, B.ISSUE_BANK_NAME, B.CUSTOMER_ID, B.ACCOUNT_TYPE, B.VERIFIED_FLAG, 
			B.CERTI_CODE, B.VERIFIED_PASS_FLAG, B.CERTI_END_DATE, B.CERTI_START_DATE, 
			B.CAUSE_DESC, B.SUSPEND_CAUSE, B.BANK_CODE, B.BRANCH_CODE, B.CONFIRMED, B.BANK_ACCOUNT_CITY, 
			B.CERTI_TYPE, B.ACCO_NAME FROM (
					SELECT ROWNUM RN, A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_bankAccountWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 	查找满足条件的对象 -->
	<select id="PA_findBankAccount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_STATUS, A.BANK_ACCOUNT, A.ACCOUNT_ID, A.ISSUE_BANK_NAME, A.CUSTOMER_ID, A.ACCOUNT_TYPE, A.VERIFIED_FLAG, 
			A.CERTI_CODE, A.VERIFIED_PASS_FLAG, A.CERTI_END_DATE, A.CERTI_START_DATE, 
			A.CAUSE_DESC, A.SUSPEND_CAUSE, A.BANK_CODE, A.BRANCH_CODE, A.CONFIRMED, A.BANK_ACCOUNT_CITY, 
			A.CERTI_TYPE, A.ACCO_NAME FROM T_BANK_ACCOUNT A WHERE 1 = 1  ]]>
		<include refid="PA_bankAccountWhereCondition" />
	</select>
</mapper>
