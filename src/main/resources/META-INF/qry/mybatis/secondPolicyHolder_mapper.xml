<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.SecondPolicyHolderDaoImpl">

	<sql id="PA_secondPolicyHolderWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" resident_type != null and resident_type != ''  "><![CDATA[ AND A.RESIDENT_TYPE = #{resident_type} ]]></if>
		<if test=" relation_to_insured_1 != null and relation_to_insured_1 != ''  "><![CDATA[ AND A.RELATION_TO_INSURED_1 = #{relation_to_insured_1} ]]></if>
		<if test=" smoking  != null "><![CDATA[ AND A.SMOKING = #{smoking} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" comm_method != null and comm_method != ''  "><![CDATA[ AND A.COMM_METHOD = #{comm_method} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" soci_secu  != null "><![CDATA[ AND A.SOCI_SECU = #{soci_secu} ]]></if>
		<if test=" income_source != null and income_source != ''  "><![CDATA[ AND A.INCOME_SOURCE = #{income_source} ]]></if>
		<if test=" annual_income_ceil  != null "><![CDATA[ AND A.ANNUAL_INCOME_CEIL = #{annual_income_ceil} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_querySecondPolicyHolderByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_querySecondPolicyHolderByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	
	<sql id="PA_querySecondPolicyHolderByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="PA_querySecondPolicyHolderByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="PA_querySecondPolicyHolderByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="PA_querySecondPolicyHolderByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="PA_addSecondPolicyHolder"  useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___PAS__DBUSER.S_SE_PO_HOLDER_LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO T_SECOND_POLICY_HOLDER(
				ADDRESS_ID, RESIDENT_TYPE, RELATION_TO_INSURED_1, SMOKING, INSERT_TIME, CUSTOMER_ID, CUSTOMER_HEIGHT, 
				COMM_METHOD, UPDATE_TIME, JOB_CODE, CUSTOMER_WEIGHT, SOCI_SECU, INCOME_SOURCE, ANNUAL_INCOME_CEIL, 
				APPLY_CODE, INSERT_TIMESTAMP, POLICY_CODE, UPDATE_BY, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, 
				POLICY_ID ) 
			VALUES (
				#{address_id, jdbcType=NUMERIC}, #{resident_type, jdbcType=VARCHAR} , #{relation_to_insured_1, jdbcType=VARCHAR} , #{smoking, jdbcType=NUMERIC} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , #{customer_height, jdbcType=NUMERIC} 
				, #{comm_method, jdbcType=VARCHAR} , SYSDATE , #{job_code, jdbcType=VARCHAR} , #{customer_weight, jdbcType=NUMERIC} , #{soci_secu, jdbcType=NUMERIC} , #{income_source, jdbcType=VARCHAR} , #{annual_income_ceil, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} 
				, #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="PA_deleteSecondPolicyHolder" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER WHERE 1 = 1 ]]>
		<include refid="PA_secondPolicyHolderWhereCondition" />
	</delete>

<!-- 修改操作 -->
	<update id="PA_updateSecondPolicyHolder" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER ]]>
		<set>
		<trim suffixOverrides=",">
		    ADDRESS_ID = #{address_id, jdbcType=NUMERIC} ,
			RESIDENT_TYPE = #{resident_type, jdbcType=VARCHAR} ,
			RELATION_TO_INSURED_1 = #{relation_to_insured_1, jdbcType=VARCHAR} ,
		    SMOKING = #{smoking, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    CUSTOMER_HEIGHT = #{customer_height, jdbcType=NUMERIC} ,
			COMM_METHOD = #{comm_method, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			JOB_CODE = #{job_code, jdbcType=VARCHAR} ,
		    CUSTOMER_WEIGHT = #{customer_weight, jdbcType=NUMERIC} ,
		    SOCI_SECU = #{soci_secu, jdbcType=NUMERIC} ,
			INCOME_SOURCE = #{income_source, jdbcType=VARCHAR} ,
		    ANNUAL_INCOME_CEIL = #{annual_income_ceil, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    LIST_ID = #{list_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="PA_findSecondPolicyHolderByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_querySecondPolicyHolderByListIdCondition" />
	</select>
	
	<select id="PA_findSecondPolicyHolderByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_querySecondPolicyHolderByAddressIdCondition" />
	</select>
	
	<select id="PA_findSecondPolicyHolderByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_querySecondPolicyHolderByApplyCodeCondition" />
	</select>
	
	<select id="PA_findSecondPolicyHolderByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_querySecondPolicyHolderByCustomerIdCondition" />
	</select>
	
	<select id="PA_findSecondPolicyHolderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_querySecondPolicyHolderByPolicyCodeCondition" />
	</select>
	
	<select id="PA_findSecondPolicyHolderByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_querySecondPolicyHolderByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapSecondPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_secondPolicyHolderWhereCondition" />
	</select>

<!-- 查询单条 -->
	<select id="PA_findSecondPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_secondPolicyHolderWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllSecondPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_secondPolicyHolderWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findSecondPolicyHolderTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_secondPolicyHolderWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_querySecondPolicyHolderForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.RESIDENT_TYPE, B.RELATION_TO_INSURED_1, B.SMOKING, B.CUSTOMER_ID, B.CUSTOMER_HEIGHT, 
			B.COMM_METHOD, B.JOB_CODE, B.CUSTOMER_WEIGHT, B.SOCI_SECU, B.INCOME_SOURCE, B.ANNUAL_INCOME_CEIL, 
			B.APPLY_CODE, B.POLICY_CODE, B.LIST_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.RESIDENT_TYPE, A.RELATION_TO_INSURED_1, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.COMM_METHOD, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.SOCI_SECU, A.INCOME_SOURCE, A.ANNUAL_INCOME_CEIL, 
			A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM APP___PAS__DBUSER.T_SECOND_POLICY_HOLDER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_secondPolicyHolderWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
