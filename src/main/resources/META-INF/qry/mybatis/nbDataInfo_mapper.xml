<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
 
<mapper namespace="com.nci.tunan.qry.dao.impl.QryCommonQueryDaoImpl">

	<!-- 根据投保单号查询投保单基本信息 -->
	<select id="QRY_queryNbContractMasterInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
  SELECT DISTINCT  NCM.POLICY_ID,
        (SELECT SC.SALES_CHANNEL_NAME FROM DEV_NB.T_SALES_CHANNEL SC where SC.SALES_CHANNEL_CODE=NCM.CHANNEL_TYPE) AS CHANNEL_TYPE, --销售渠道
        (CASE WHEN ( NCM.DOUBLE_MAINRISK_FLAG ='1' AND NCM.RELATION_POLICY_CODE IS NULL) THEN 
         (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER T WHERE T.RELATION_POLICY_CODE= NCM.POLICY_CODE)
         ELSE
           NCM.APPLY_CODE
       END 
       ) AS APPLY_CODE,
       NCM.APPLY_DATE, /*投保日期*/
        /*#173020综合查询增加渠道销售方式、是否为互联网保单标识*/
        b.type_desc AS SALE_TYPE,
       to_char(xx.INTERNET_COOP_BUSI) AS INTERNET_COOPERATION_BUSINESS,
       NCM.SUBMIT_CHANNEL,
       NCM.SUBMISSION_DATE, /*交单日期*/
       NCM.PA_COMPLETE_TIME,/*审核日期*/
      (SELECT AG.AGENT_NAME FROM DEV_NB.T_AGENT AG WHERE AG.AGENT_CODE=NCM.SALE_AGENT_CODE) AS SALE_AGENT_CODE, /*交叉销售人员代码*/
       NCM.SALE_AGENT_NAME, /*交叉销售人员姓名*/
       NCM.ORGAN_CODE, /*保单管理机构*/
       (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE=NCM.ORGAN_CODE) AS ORGAN_CODE_NAME , /*保单管理机构*/
       (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID=NCM.PA_USER_CODE) AS PA_USER_CODE , /*初审人员*/
       /*营业部*/
       NCM.CHANNEL_ID, /*营业组*/
       NCM.SERVICE_BANK AS BANK_CODE,/*服务银行代码*/
       NCM.SERVICE_BANK_BRANCH AS BANK_BRANCH_CODE,/*银行网点代码*/
       (SELECT BB.BANK_NAME
          FROM DEV_NB.T_BANK BB
         WHERE BB.BANK_CODE = NCM.SERVICE_BANK) AS SERVICE_BANK, /*服务银行*/
       (SELECT BB.BANK_BRANCH_NAME
          FROM DEV_NB.T_BANK_BRANCH BB
         WHERE BB.BANK_BRANCH_CODE = NCM.SERVICE_BANK_BRANCH) AS SERVICE_BANK_BRANCH, /*银行网点*/
       NCM.SERVICE_HANDLER, /**网点经办人 */
       NCM.SERVICE_HANDLER_CODE,
       NCM.SERVICE_HANDLER_NAME,
       NCM.HIGH_SA_INDI, /*是否高额件*/
       NCM.BIRTHDAY_POL_INDI, /*是否生日单*/
       NCM.MANUAL_UW_INDI, /*是否人工核保*/
       NCM.E_SERVICE_FLAG, /*是否电子函件服务*/
       CASE
         WHEN ERV.MEDIA_TYPE IS NOT NULL  THEN
           ERV.MEDIA_TYPE
         ELSE
          NCM.MEDIA_TYPE
       END MEDIA_TYPE, /*电子保单/纸质保单*/
       NCM.CALL_TIME_LIST, /*电话回访时间段*/
       NCM.DIALECT_INDI, /*是否方言件*/
       NCM.POLICY_TYPE, /*保单类型*/
       NCM.VALIDATE_DATE, /*生效日期*/
       NCM.PROPOSAL_STATUS, /*投保单状态*/
       TA.ACKNOWLEDGE_DATE,/*回执日期*/
       TA.P_ACKNOWLEDGE_DATE,
       NCM.CONFIRM_WAY,/*移动平台确认方式*/
       CA.AGENT_CODE,
       A.AGENT_NAME, /*业务人员*/
       A.AGENT_LEVEL, /*星级业务员 1星、2星等 */
       A.AGENT_MOBILE, /*业务员电话*/
       A.AGENT_ORGAN_CODE, /*所属机构*/
       (SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE= A.AGENT_ORGAN_CODE) AS AGENT_ORGAN_CODE_NAME ,
       CA.RELATION_TO_PH, /*与投保人关系*/
       NCM.DRQ_FLAG,/*双录标识*/
       NCM.BANK_MANAGER_LICENSENO,/*客户经理执业证编码 */
       NCM.BRANCH_ORGAN_CODE,
       GST.GROUP_TYPE_DESC AS GROUP_SALE_TYPE,/*销售方式(团队)*/
       DECODE(NCM.SUBMIT_CHANNEL,'5',CA.COOPERATION_CODE,'24',CA.COOPERATION_CODE,'25',CA.COOPERATION_CODE,'26',CA.COOPERATION_CODE,TCM.OTHER_MANAGE_COM) OTHER_MANAGE_COM,/*52031需求修改 中介机构代码--160232慧择重新修改*/
       DECODE(NCM.SUBMIT_CHANNEL,'5',CA.COOPERATION_NAME,'24',CA.COOPERATION_NAME,'25',CA.COOPERATION_NAME,'26',CA.COOPERATION_NAME,TCM.OTHER_MANAGE_NAME) OTHER_MANAGE_NAME,/*52031需求修改 中介机构名称--160232慧择重新修改*/
       MP.PROTOCOL_ID,/*#48816协议号*/
       (SELECT AR.RELATION_NAME FROM DEV_PAS.T_AGENT_RELATIONSHIP AR 
		WHERE AR.RELATION_CODE=CA.RELATION_TO_ALL) AS RELATION_TO_ALL,/*投保人、被保险人或受益人与业务员关系*/
  		(CASE WHEN NCM.IS_MUTUAL_INSURED='1' THEN '是' WHEN NCM.IS_MUTUAL_INSURED='0' THEN '否' ELSE '' END) AS IS_MUTUAL_INSURED,/*是否互保件*/
 		(CASE WHEN NCM.IS_SELF_INSURED='1' THEN '是' WHEN NCM.IS_SELF_INSURED='0' THEN '否' ELSE '' END) AS IS_SELF_INSURED, /*是否自保件*/
 		NCM.PERSONAL_INFO_PROTECT,NCM.SPECIAL_ACCOUNT_FLAG,TA.P_BRANCH_RECEIVE_DATE,
 		(case  
	     when (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = ncm.apply_code) > 0 /*电子保单申请过纸质保单*/
	     then (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD LEFT JOIN DEV_NB.T_POLICY_PRINT_SOURCE PPS ON PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE WHERE PPD.APPLY_CODE = ncm.APPLY_CODE AND ROWNUM <= 1)
	     when to_number(ncm.subinput_type) <> 15 and ncm.submit_channel = '1' and tt.media_type in ('0','2') /*保全做过变更并且是电子保单非银保通电子渠道出单*/
	     then  (SELECT tst.type_desc FROM dev_nb.t_input_type tst where  ncm.input_type = tst.type_code)
	     when to_number(ncm.subinput_type) <> 15 and ncm.submit_channel = '1' and ncm.media_type = '0' and mst.media_type = '1' and tt.media_type = '1' /*判断完保全再判断契约*/
	     then  ''
	     when tt.media_type is not null and (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = ncm.apply_code) = 0 and ncm.media_type <> tt.media_type
	      then ''
	     ELSE ''
	      end )as APPLY_SOURCE_NAME,NCM.NOTIFICATION_RECEIVE_METHOD,
	      NCM.banknrt_Falg
  FROM DEV_NB.T_NB_CONTRACT_MASTER NCM
   /*#173020综合查询增加渠道销售方式、是否为互联网保单标识*/ 
  LEFT JOIN DEV_NB.T_SALE_TYPE b on b.type_code=NCM.sale_type
  LEFT JOIN DEV_NB.T_NB_POLICY_MASTER_EXTEND xx on xx.apply_code=NCM.apply_code
  LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
    ON CA.APPLY_CODE = NCM.APPLY_CODE
  LEFT JOIN DEV_PAS.T_AGENT A
    ON A.AGENT_CODE = CA.AGENT_CODE
  LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT TA
    ON TA.POLICY_CODE = NCM.POLICY_CODE
  LEFT JOIN DEV_NB.T_GROUP_SALE_TYPE GST
    ON NCM.GROUP_SALE_TYPE = GST.GROUP_TYPE_CODE
  /*48816 start*/
  LEFT JOIN DEV_NB.T_NCL_COM_MAPPING TCM
    ON A.MANAGE_COM_OUTER = TCM.OTHER_MANAGE_COM
  LEFT JOIN DEV_NB.T_AGENT_MEDIATION_PROTOCOL MP
    ON TCM.NCL_MANAGE_COM=MP.MANAGE_COM
    AND MP.SIGN_DATE IS NOT NULL
  LEFT JOIN DEV_NB.T_NB_ELECTRON_RETURN_VISIT ERV ON NCM.APPLY_CODE=ERV.APPLY_CODE
  /*48816 end*/
  LEFT JOIN APP___PAS__DBUSER.T_CONTRACT_MASTER MST ON MST.APPLY_CODE = nCM.APPLY_CODE
  LEFT JOIN (SELECT LOG_ID,POLICY_CODE,MEDIA_TYPE  FROM 
                    (SELECT MSTG.LOG_ID,MSTG.POLICY_CODE,MSTG.MEDIA_TYPE 
                    ,row_number() over(partition by MSTG.POLICY_CODE ORDER BY MSTG.LOG_ID  ASC ) RN
                    FROM DEV_PAS.T_CONTRACT_MASTER_LOG MSTG)
                    WHERE RN = 1) TT ON TT.POLICY_CODE = NCM.POLICY_CODE
   WHERE 1=1   AND ROWNUM=1 
   ]]>
   <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND NCM.APPLY_CODE = #{apply_code} ]]></if>
	</select>
<!-- 根据投保单号查询投保人信息 -->
	<select id="QRY_queryNbPolicyHolderInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT PH.APPLY_CODE,
       C.CUSTOMER_ID, /*客户编码*/
       C.CUSTOMER_NAME, /*姓名*/
       C.CUSTOMER_BIRTHDAY AS CUSTOMER_BIRTHDAY, /*客户出生日期*/
       C.CUSTOMER_GENDER AS CUSTOMER_GENDER, /*客户性别*/
        (SELECT G.GENDER_DESC FROM DEV_NB.T_GENDER G WHERE G.GENDER_CODE=C.CUSTOMER_GENDER) AS CUSTOMER_GENDER_DESC, /*客户性别*/
       (SELECT CT.TYPE FROM DEV_NB.T_CERTI_TYPE CT WHERE CT.CODE=C.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE, /*客户证件类型*/
       C.CUSTOMER_CERTI_CODE, /*客户证件号码*/
       C.CUST_CERT_STAR_DATE, /*证件有效期-起*/
       C.CUST_CERT_END_DATE, /*证件有效期-至*/
      (SELECT CL.CUSTOMER_LEVEL_DESC FROM DEV_NB.T_CUSTOMER_LEVEL CL WHERE CL.CUSTOMER_LEVEL_CODE=C.CUSTOMER_LEVEL) AS CUSTOMER_LEVEL, /*客户等级*/
       C.CUSTOMER_RISK_LEVEL, /*客户风险等级*/
       (SELECT CO.COUNTRY_NAME FROM DEV_NB.T_COUNTRY CO WHERE CO.COUNTRY_CODE=C.COUNTRY_CODE) AS COUNTRY_CODE , /*国籍*/
       (SELECT M.MARRIAGE FROM DEV_NB.T_MARRIAGE M WHERE M.MARRIAGE_CODE=C.MARRIAGE_STATUS) AS MARRIAGE_STATUS, /*婚姻状况*/
       C.IS_PARENT, /*子女状况*/
       PH.JOB_CODE, /*职业代码*/
       C.JOB_KIND, /*职业所属行业性质职业类别*/
       C.JOB_TITLE, /*职务*/
       C.JOB_NATURE, /*行业*/
       (SELECT CT.LICENSE_DESC FROM DEV_NB.T_LICENSE_TYPE CT WHERE CT.LICENSE_TYPE=C.DRIVER_LICENSE_TYPE) AS DRIVER_LICENSE_TYPE, /*驾照类型*/
       C.COMPANY_NAME, /*工作单位*/
       A.OFFICE_TEL,
       A.MOBILE_TEL,
       (SELECT DT.NAME FROM DEV_NB.T_DISTRICT DT WHERE DT.CODE=A.STATE) AS STATE, /*省直辖市*/
       (SELECT DT.NAME FROM DEV_NB.T_DISTRICT DT WHERE DT.CODE=A.CITY) AS CITY, /*市*/
       (SELECT DT.NAME FROM DEV_NB.T_DISTRICT DT WHERE DT.CODE=A.DISTRICT) AS DISTRICT, /*区县*/
       A.ADDRESS, /*地址*/
       A.POST_CODE,/*邮编*/
       A.Email,
       A.ADDRESS_TYPE,
       C.SECOND_CERT_TYPE,/*第二证件类型*/
       C.SECOND_CERTI_CODE,/*第二证件号码*/
       PH.ANNUAL_INCOME_CEIL,PH.SOCI_SECU,PH.RESIDENT_TYPE,PH.INCOME_SOURCE,
       PH.APPLICANT_SPE_PEOPLE,PH.AGENT_RELATION,
       TI.RETIREMENT_AGE,/*退休年龄*/
       TI.INSURED_TYPE,/*参保人身份类型*/
       TI.BUS_SRE_DEPT_CODE,/*所在单位的统一社会信用代码*/
       TI.TAX_DISTRICT,/*税收地点*/
       TI.TAX_NUMBER, /*纳税人识别号*/
       PH.NEW_RESIDENT
  FROM DEV_NB.T_NB_POLICY_HOLDER PH
 LEFT JOIN (SELECT CUSTOMER_ID,
                            CUSTOMER_NAME,
                            CUSTOMER_BIRTHDAY,
                            CUSTOMER_GENDER,
                            CUSTOMER_CERT_TYPE,
                            CUSTOMER_CERTI_CODE,
                            CUST_CERT_STAR_DATE,
                            CUST_CERT_END_DATE,
                            CUSTOMER_LEVEL,
                            MARRIAGE_STATUS,
                            IS_PARENT,
                            JOB_KIND,
                            JOB_TITLE,
                            JOB_NATURE,
                            DRIVER_LICENSE_TYPE,
                            COMPANY_NAME,
                            COUNTRY_CODE,
                            CUSTOMER_RISK_LEVEL,
                            SECOND_CERT_TYPE,/* 第二证件类型*/
                            SECOND_CERTI_CODE/* 第二证件号码*/
                       FROM (SELECT *
                               FROM DEV_PAS.T_CUSTOMER_B B
                              WHERE B.INSERT_TIME >= (SELECT MAX(UPDATE_TIME)
                                       FROM  DEV_NB.T_NB_POLICY_HOLDER PH
                                              WHERE PH.APPLY_CODE = #{apply_code}
                                 )
                          AND  EXISTS
                           (
                            SELECT * FROM DEV_NB.T_NB_POLICY_HOLDER PH WHERE PH.APPLY_CODE= #{apply_code}
                            AND PH.CUSTOMER_ID=B.CUSTOMER_ID
                           )
                              ORDER BY B.INSERT_TIME,B.LIST_ID) T
                      WHERE ROWNUM = 1
                     UNION
                     SELECT CUSTOMER_ID,
                            CUSTOMER_NAME,
                            CUSTOMER_BIRTHDAY,
                            CUSTOMER_GENDER,
                            CUSTOMER_CERT_TYPE,
                            CUSTOMER_CERTI_CODE,
                            CUST_CERT_STAR_DATE,
                            CUST_CERT_END_DATE,
                            CUSTOMER_LEVEL,
                            MARRIAGE_STATUS,
                            IS_PARENT,
                            JOB_KIND,
                            JOB_TITLE,
                            JOB_NATURE,
                            DRIVER_LICENSE_TYPE,
                            COMPANY_NAME,
                            COUNTRY_CODE,
                            CUSTOMER_RISK_LEVEL,
                            SECOND_CERT_TYPE,/* 第二证件类型*/
                            SECOND_CERTI_CODE/* 第二证件号码*/
                       FROM DEV_NB.T_CUSTOMER CU
                      WHERE NOT EXISTS (SELECT *
                               FROM DEV_PAS.T_CUSTOMER_B
                              WHERE CUSTOMER_ID = CU.CUSTOMER_ID 
                                AND INSERT_TIME >=
                                (SELECT MAX(UPDATE_TIME)
                                       FROM  DEV_NB.T_NB_POLICY_HOLDER PH
                                              WHERE PH.APPLY_CODE = #{apply_code}
                                 )
                              )
                        AND EXISTS
                      (SELECT CUSTOMER_ID
                               FROM DEV_NB.T_NB_POLICY_HOLDER PH
                              WHERE PH.APPLY_CODE = #{apply_code}
                                AND PH.CUSTOMER_ID = CU.CUSTOMER_ID)) C
            ON C.CUSTOMER_ID = PH.CUSTOMER_ID
          LEFT JOIN (SELECT * FROM DEV_NB.T_ADDRESS WHERE ADDRESS_STATUS = '1') A
            ON A.ADDRESS_ID = PH.ADDRESS_ID
          LEFT JOIN DEV_NB.T_NB_CUSTOMER_TAX_INFO TI 
          	ON TI.APPLY_CODE=PH.APPLY_CODE
         WHERE 1 = 1
           AND PH.APPLY_CODE = #{apply_code} 

     ]]>
	</select>
	<!-- 根据投保单号查询被保险人信息 -->
	<select id="QRY_queryNbInsuredListInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT IL.CUSTOMER_ID,
		IL.APPLY_CODE,
		C.HIS_LIST_ID,
       C.CUSTOMER_NAME,
       C.CUSTOMER_BIRTHDAY,
       C.CUSTOMER_GENDER,
       (SELECT CT.TYPE FROM DEV_NB.T_CERTI_TYPE CT WHERE CT.CODE=C.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE, /*客户证件类型*/
       C.CUSTOMER_CERTI_CODE,
       (SELECT PR.RELATION_NAME FROM DEV_NB.T_LA_PH_RELA PR WHERE PR.RELATION_CODE=IL.RELATION_TO_PH) AS RELATION_TO_PH,
       C.SECOND_CERT_TYPE,/*第二证件类型*/
       C.SECOND_CERTI_CODE,/*第二证件号码*/
       IL.AGENT_RELATION,IL.NEW_RESIDENT
  FROM DEV_NB.T_NB_INSURED_LIST IL
 INNER JOIN (
         SELECT LIST_ID AS HIS_LIST_ID,CUSTOMER_ID,
                            CUSTOMER_NAME,
                            CUSTOMER_BIRTHDAY,
                            CUSTOMER_GENDER,
                            CUSTOMER_CERT_TYPE,
                            CUSTOMER_CERTI_CODE,
                            CUST_CERT_STAR_DATE,
                            CUST_CERT_END_DATE,
                            CUSTOMER_LEVEL,
                            MARRIAGE_STATUS,
                            IS_PARENT,
                            JOB_KIND,
                            JOB_TITLE,
                            JOB_NATURE,
                            DRIVER_LICENSE_TYPE,
                            COMPANY_NAME,
                            SECOND_CERT_TYPE,/* 第二证件类型*/
                            SECOND_CERTI_CODE/* 第二证件号码*/
                       FROM (SELECT *
                               FROM DEV_PAS.T_CUSTOMER_B B
                              WHERE B.INSERT_TIME >=
                                (
                                   SELECT Max(UPDATE_TIME)
                                FROM DEV_NB.T_NB_INSURED_LIST IL
                               WHERE IL.APPLY_CODE = #{apply_code}
                                )
                                AND  EXISTS
                               (
                                SELECT * FROM DEV_NB.T_NB_INSURED_LIST IL WHERE IL.APPLY_CODE=#{apply_code}
                                AND IL.CUSTOMER_ID=B.CUSTOMER_ID
                               )
                              ORDER BY B.INSERT_TIME,B.LIST_ID) T
                              ]]>
                              <if test=" haveInsureds == null or haveInsureds == ''  "><![CDATA[ WHERE ROWNUM = 1  ]]></if>
                     /* WHERE ROWNUM = 1  rm151078-984险种多被保人改造*/
                     <![CDATA[
                     UNION
                     SELECT null AS HIS_LIST_ID, CUSTOMER_ID,
                            CUSTOMER_NAME,
                            CUSTOMER_BIRTHDAY,
                            CUSTOMER_GENDER,
                            CUSTOMER_CERT_TYPE,
                            CUSTOMER_CERTI_CODE,
                            CUST_CERT_STAR_DATE,
                            CUST_CERT_END_DATE,
                            CUSTOMER_LEVEL,
                            MARRIAGE_STATUS,
                            IS_PARENT,
                            JOB_KIND,
                            JOB_TITLE,
                            JOB_NATURE,
                            DRIVER_LICENSE_TYPE,
                            COMPANY_NAME,
                            SECOND_CERT_TYPE,/* 第二证件类型*/
                            SECOND_CERTI_CODE/* 第二证件号码*/
                       FROM DEV_NB.T_CUSTOMER CU
                      WHERE NOT EXISTS (SELECT *
                               FROM DEV_PAS.T_CUSTOMER_B
                              WHERE CUSTOMER_ID = CU.CUSTOMER_ID AND 
                               INSERT_TIME >=
                               (
                                SELECT Max(UPDATE_TIME)
                                FROM DEV_NB.T_NB_INSURED_LIST IL
                               WHERE IL.APPLY_CODE = #{apply_code}
                               )
                            )
                        AND EXISTS
                      (SELECT CUSTOMER_ID
                               FROM DEV_NB.T_NB_INSURED_LIST IL
                              WHERE IL.APPLY_CODE = #{apply_code}
                                AND IL.CUSTOMER_ID = CU.CUSTOMER_ID)
     ) C
    ON C.CUSTOMER_ID = IL.CUSTOMER_ID
		WHERE 1=1 ]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND IL.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!-- 根据投保单号查询受益人信息 -->
	<select id="QRY_queryNbContractBeneInfo_old" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		 SELECT NCB.APPLY_CODE,
        NCB.SHARE_ORDER, --收益顺序
        NCB.SHARE_RATE, --收益份额
        NCB.DESIGNATION, --与被保险人关系
        C.CUSTOMER_NAME,
        (SELECT CT.TYPE FROM DEV_NB.T_CERTI_TYPE CT WHERE CT.CODE=C.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE, /*客户证件类型*/
        C.CUSTOMER_CERTI_CODE,
        T.CUSTOMER_NAME AS INSURED_NAME,
        NCBP.BUSI_PRD_ID,
        BP.PRODUCT_NAME_SYS,
        NCB.AGENT_RELATION
   FROM DEV_NB.T_NB_CONTRACT_BENE NCB
  INNER JOIN DEV_NB.T_NB_INSURED_LIST IL
     ON IL.LIST_ID = NCB.INSURED_ID
  INNER JOIN DEV_NB.T_CUSTOMER C
     ON NCB.CUSTOMER_ID = C.CUSTOMER_ID
  INNER JOIN DEV_NB.T_CUSTOMER T
     ON T.CUSTOMER_ID = IL.CUSTOMER_ID
  INNER JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP
     ON NCBP.BUSI_ITEM_ID = NCB.BUSI_ITEM_ID
  INNER JOIN DEV_PAS.T_BUSINESS_PRODUCT BP
     ON BP.BUSINESS_PRD_ID = NCBP.BUSI_PRD_ID
		WHERE 1=1
		]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND NCB.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!--1425缺陷所要求的暂时修改查询语句  -->
	<select id="QRY_queryNbContractBeneInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT NCB.APPLY_CODE,
			       NCB.SHARE_ORDER, /*收益顺序*/
			       NCB.SHARE_RATE, /*收益份额*/
			       NCB.DESIGNATION, /*与被保险人关系*/
	               NCB.CUSTOMER_ID,/*客户ID*/
	               /*C.HIS_LIST_ID,历史投保时客户记录ID*/
			       NCB.CUSTOMER_NAME,
			       (SELECT CT.TYPE
	                  FROM DEV_NB.T_CERTI_TYPE CT
	                 WHERE CT.CODE = NCB.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE,
			       NCB.CUSTOMER_CERTI_CODE,
			       (CASE
	               WHEN NCBP.Waiver = 1 AND NOT (NCBP.Product_Code='00862000' OR NCBP.Product_Code='00953000') THEN
	                 (SELECT t.customer_name FROM dev_nb.t_nb_policy_holder ph,DEV_NB.T_CUSTOMER T WHERE ph.list_id = NCB.INSURED_ID AND ph.customer_id = t.customer_id)
	               WHEN NCBP.Waiver = 0 THEN   
	                  (SELECT t.customer_name FROM DEV_NB.T_NB_INSURED_LIST IL,DEV_NB.T_CUSTOMER T WHERE IL.LIST_ID = NCB.INSURED_ID AND il.customer_id = t.customer_id) 
	               WHEN  NCBP.Waiver = 1 AND (NCBP.Product_Code='00862000' OR NCBP.Product_Code='00953000') THEN   
	                  (SELECT t.customer_name FROM DEV_NB.T_NB_INSURED_LIST IL,DEV_NB.T_CUSTOMER T WHERE IL.LIST_ID = NCB.INSURED_ID AND il.customer_id = t.customer_id)  
	                     END) AS INSURED_NAME,/**所属被保险人 */
			       NCBP.BUSI_PRD_ID,
			       BP.PRODUCT_NAME_SYS,
	           	   NCB.AGENT_RELATION 		  
	           	   FROM DEV_NB.T_NB_CONTRACT_BENE NCB
			 INNER JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD NCBP
			    ON NCBP.BUSI_ITEM_ID = NCB.BUSI_ITEM_ID
			 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
			    ON BP.BUSINESS_PRD_ID = NCBP.BUSI_PRD_ID
			 WHERE 1 = 1 AND NCB.Bene_Type='1'
		]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND NCB.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!-- 根据投保单号查询投保单缴费信息 -->
	<select id="QRY_queryNbPayerAccountInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT NPA.PAY_MODE, --首期缴费形式
       NPA.ACCOUNT_ID, --转账信息
             (CASE
               WHEN (NCM.SERVICE_BANK = 'B8' AND NCM.SUBMIT_CHANNEL = 1 AND
                    NCM.CHANNEL_TYPE = '03') THEN
                NL.LAW_MAN_NAME
               ELSE
                NB1.BANK_NAME
             END) AS ACCOUNT_BANK, --首期转账开户行
       NPA.ACCOUNT_NAME, --首期账户姓名
       NPA.ACCOUNT, --首期账号
       --是否与首期账户信息一致
       NPA.PAY_NEXT, --续期交费形式
       NPA.NEXT_ACCOUNT_ID, --转账信息
             (CASE
               WHEN (NCM.SERVICE_BANK = 'B8' AND NCM.SUBMIT_CHANNEL = 1 AND
                    NCM.CHANNEL_TYPE = '03') THEN
                NL.LAW_MAN_NAME
               ELSE
                NB2.BANK_NAME
             END) AS NEXT_ACCOUNT_BANK, --续期转账开户行
             NPA.NEXT_ACCOUNT_NAME, --续期账户姓名
             NPA.NEXT_ACCOUNT, --续期账号
             (CASE WHEN NPA.PRIOR_PAY_FLAG = '1' THEN '微信支付' ELSE '' END) AS PRIOR_PAY_FLAG_STR
        FROM DEV_NB.T_NB_CONTRACT_MASTER NCM
       INNER JOIN DEV_NB.T_NB_PAYER_ACCOUNT NPA
          ON NCM.POLICY_ID = NPA.POLICY_ID
        LEFT JOIN DEV_NB.T_LAW_MAN NL
          ON NPA.LAW_MAN = NL.LAW_MAN
       INNER JOIN DEV_NB.T_BANK NB1
          ON NPA.ACCOUNT_BANK = NB1.BANK_CODE
       LEFT JOIN DEV_NB.T_BANK NB2
          ON NPA.NEXT_ACCOUNT_BANK = NB2.BANK_CODE
		WHERE 1=1
		]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND NCM.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!-- 根据投保单号查询投保单险种信息 -->
	<select id="QRY_queryContractBusiProdInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT 
			CP.ITEM_ID,/*责任组ID*/
			NP.BUSI_PRD_ID,
			   NP.APPLY_CODE,
			   NP.BUSI_ITEM_ID,
               BP.PRODUCT_NAME_SYS, /*险种名称*/
		       BP.PRODUCT_NAME_STD,
		       (case when NP.RENEW=1 or NP.RENEW=2 then 1
               when NP.RENEW=0 then 0 end) AS RENEW, /*自动续保标识*/
		       CP.PRODUCT_CODE,
		       CP.PRODUCT_ID,
		       PLF.PRODUCT_NAME, /*责任组名称*/
		       CP.COVERAGE_PERIOD, /*保险期间标志*/
		       CP.COVERAGE_YEAR, /*保险期间*/
		       CP.CHARGE_YEAR, /*缴费年期*/
		       CP.CHARGE_PERIOD, /*交费期间类型*/
		       CP.PREM_FREQ,/*对应页面交费方式*/
		       CP.AMOUNT NBAMOUNT, /*保额*/
		       CP.TOTAL_PREM_AF, /*保费*/
		       NPA.PAY_MODE, /*交费方式*/
		       NM.DOUBLE_MAINRISK_FLAG, /*双主险标识*/
		       NM.RELATION_POLICY_CODE, /*万能险关联保单号*/
		       NM.MULTI_MAINRISK_FLAG,/*多主多附标识*/
		       DECODE(CPO.FIELD8,
		              'Y',
		              '保证领取某确定年限',
		              'W',
		              '保证保费领取',
		              'F',
		              '固定期限领取',
		              'V',
		              '保证返还账户价值终身领取',
		              'A',
		              '保证领取至某确定年龄',
		              '') AS GET_TYPE, /*领取类型*/
  		       NM.POLICY_REINSURE_FLAG,/*续保转保标识*/
  		       CP.UNIT,
		       DECODE(CPO.FIELD5,
		              '1',
		              '中国大陆',
		              '2',
		              '中国大陆及港澳台',
		              '4',
		              '全球',
		              '5',
		              '全球除美国',
		              '') AS SUP_AREA, /* 保障区域 */
		       DECODE(CPO.HOSPITAL_SCOPE,
		              '1',
		              '包含昂贵医院',
		              '0',
		              '不包含昂贵医院',
		              '') AS HOSPITAL_SCOPE_NAME,/* 医院范围 */
		       CPO.INSURED_ADDRESS_IS_OFFEN_LIVE, /*被保险人录入的联系地址是否为常住地/固定居住地址*/
		       CPO.ANNUITY_START_PAY_DAY,
		       TC.CUSTOMER_NAME/*被保人姓名*/ 
		       ,NP.ORDER_ID /*险种录入顺序*/
		       ,BP.PRODUCT_CATEGORY1/* 设计类型 */	
		       ,NM.POLICY_ID
		       ,NP.MASTER_BUSI_ITEM_ID /*所属主险id*/
		  FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD NP
		  INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER NM 
		 	ON NP.POLICY_ID=NM.POLICY_ID
		 INNER JOIN DEV_NB.T_NB_CONTRACT_PRODUCT CP
		    ON CP.BUSI_ITEM_ID = NP.BUSI_ITEM_ID
		 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		    ON BP.BUSINESS_PRD_ID = NP.BUSI_PRD_ID
		 INNER JOIN DEV_PDS.T_PRODUCT_LIFE PLF
		    ON PLF.PRODUCT_ID = CP.PRODUCT_ID
		  LEFT JOIN DEV_NB.T_NB_PAYER_ACCOUNT NPA
		    ON NPA.POLICY_ID = NP.POLICY_ID
		  LEFT JOIN DEV_NB.T_NB_CONTRACT_PRODUCT_OTHER CPO
		    ON CPO.APPLY_CODE = NP.APPLY_CODE AND CP.BUSI_ITEM_ID=CPO.BUSI_ITEM_ID	
		    
		    LEFT JOIN DEV_NB.T_NB_BENEFIT_INSURED NBI
          ON NP.APPLY_CODE = NBI.APPLY_CODE
          AND NP.BUSI_ITEM_ID = NBI.BUSI_ITEM_ID
          LEFT JOIN DEV_NB.T_NB_INSURED_LIST NIL
          ON NBI.APPLY_CODE = NIL.APPLY_CODE
          AND NBI.INSURED_ID = NIL.LIST_ID
          LEFT JOIN DEV_NB.T_CUSTOMER TC
          ON NIL.CUSTOMER_ID = TC.CUSTOMER_ID
		    	   
		 WHERE 1 = 1
        ]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND NP.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND NP.POLICY_CODE = #{policy_code} ]]></if>
		<![CDATA[ ORDER BY NP.ORDER_ID ]]>
	</select>
		<select id="NB_findAllConstantsInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONSTANTS_DESC, A.CONSTANTS_VALUE, A.CONSTANTS_KEY, A.CONSTANTS_ID, A.SUB_ID 
		FROM DEV_NB.T_CONSTANTS_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryConstantsInfoByConstantsKey" />
		<![CDATA[ ORDER BY A.CONSTANTS_ID ]]> 
	</select>
	
	<sql id="NB_queryConstantsInfoByConstantsKey">
		<if test=' constants_key  != null and constants_key != ""  and constants_key.contains("%")  '><![CDATA[ AND A.CONSTANTS_KEY LIKE (${constants_key}) ]]></if>
		<if test=' constants_key  != null and constants_key != ""  and !constants_key.contains("%")  '><![CDATA[ AND A.CONSTANTS_KEY = #{constants_key} ]]></if>
	</sql>
	
	<select id="QRY_queryNbContractMasterInfoForApplySource" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT * FROM (
			SELECT A.ORGAN_CODE,A.APPLY_CODE, A.POLICY_CODE,A.APPLY_SOURCE FROM DEV_NB.T_POLICY_PRINT_DETAIL A WHERE 1 = 1  ]]>
			AND A.APPLY_CODE = #{apply_code}
		<![CDATA[ ORDER BY A.APPLY_DATE DESC ) AA WHERE ROWNUM = 1 ]]>
	</select>
</mapper>
