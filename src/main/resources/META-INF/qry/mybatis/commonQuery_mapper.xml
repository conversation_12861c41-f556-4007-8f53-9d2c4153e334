<?xml version="1.0" encoding="UTF-8"?>
<!--20180924提交  -->
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
 
<mapper namespace="com.nci.tunan.qry.impl.task.dao.impl.CommonQueryDaoImpl">
  
	<!-- 查询条件     -->
	<sql id="PA_findHolderPolicyInfosCondition">
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TCM.APPLY_CODE = #{apply_code} ]]></if>
		<if
			test=" service_agent != null and service_agent != '' and service_bank_branch != null and service_bank_branch != ''">
			<![CDATA[    AND   (TCA.AGENT_NAME= #{service_agent} OR TCM.SERVICE_BANK_BRANCH = #{service_bank_branch})]]>
		</if>
		<if test=" customer_id  != null "><![CDATA[ AND CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" account != null and account != ''  "><![CDATA[ AND TPA.ACCOUNT = #{account} ]]></if>
	</sql>
	<sql id="PA_findBusiItemInfosCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	
	<!-- 根据客户姓名查询客户列表信息 -->
	<select id="QRY_findCustomerInfos" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		  SELECT DISTINCT 
  CC.CUSTOMER_ID,
  CC.CUSTOMER_NAME,
  CC.CUSTOMER_BIRTHDAY,
  CC.CUSTOMER_GENDER,
  CC.CUSTOMER_CERT_TYPE,
  CC.CUSTOMER_CERTI_CODE    
FROM 
(
SELECT
CU.CUSTOMER_ID,  
CU.CUSTOMER_NAME,
CU.CUSTOMER_BIRTHDAY,
CU.CUSTOMER_GENDER,
CU.CUSTOMER_CERT_TYPE,
CU.CUSTOMER_CERTI_CODE
FROM DEV_PAS.T_CUSTOMER CU 
INNER JOIN 
DEV_PAS.T_POLICY_HOLDER PH
 ON PH.CUSTOMER_ID = CU.CUSTOMER_ID
WHERE 1=1]]>
		<if test=" customer_name  != null  and customer_name != '' "><![CDATA[and CU.CUSTOMER_NAME = #{customer_name} ]]></if>
<![CDATA[UNION
SELECT 
CU.CUSTOMER_ID,
CU.CUSTOMER_NAME,
CU.CUSTOMER_BIRTHDAY,
CU.CUSTOMER_GENDER,
CU.CUSTOMER_CERT_TYPE,
CU.CUSTOMER_CERTI_CODE    
FROM DEV_PAS.T_CUSTOMER CU 
INNER JOIN 
DEV_PAS.T_POLICY_HOLDER PH
 ON PH.CUSTOMER_ID = CU.CUSTOMER_ID
 INNER JOIN 
  DEV_PAS.T_INSURED_LIST IL
  ON IL.CUSTOMER_ID = CU.CUSTOMER_ID  
WHERE 1=1 ]]>
		<if test=" customer_name  != null  and customer_name != '' "><![CDATA[and CU.CUSTOMER_NAME = #{customer_name} ]]></if>
<![CDATA[
) CC
]]>
	</select>
	<!-- 根据保单号查询保单信息 -->
	<select id="QRY_findPolicyInfoByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT T.*,
		 (CASE 
		     WHEN  T.POLICY_CODE<>T.RELATIOIN_POLICY_CODE AND T.DOUBLE_MAINRISK_FLAG='1' THEN 
		         (SELECT min(PRINT_TIME) FROM 
		            (select ppa.print_time,ppa.policy_code,ppa.print_type from DEV_PAS.T_POLICY_PRINT  ppa
		            union 
		            select ppn.print_time,ppn.policy_code,ppn.print_type  from DEV_NB.T_POLICY_PRINT  ppn) a
		            WHERE POLICY_CODE=T.RELATIOIN_POLICY_CODE AND PRINT_TIME is not null
		                     AND (PRINT_TYPE='2'))
		     else (SELECT min(PRINT_TIME) FROM 
		        (select ppa.print_time,ppa.policy_code,ppa.print_type from DEV_PAS.T_POLICY_PRINT  ppa
		        union 
		        select ppn.print_time,ppn.policy_code,ppn.print_type  from DEV_NB.T_POLICY_PRINT  ppn) a
		        WHERE POLICY_CODE=T.POLICY_CODE AND PRINT_TIME is not null
		                 AND (PRINT_TYPE='2'))
		      END 
		       ) AS E_POLICY_SEND_TIME,/*电子保单发送日期*/
		      (CASE 
		       WHEN  T.POLICY_CODE<>T.RELATIOIN_POLICY_CODE AND T.DOUBLE_MAINRISK_FLAG='1' THEN 
		        (SELECT min(PRINT_TIME)
		                   FROM (select DECODE(PPA.PRINT_TYPE,'1',PPA.BPO_PRINT_DATE,PPA.PRINT_TIME) AS print_time,
		                                ppa.policy_code,
		                                ppa.print_type
		                           from DEV_PAS.T_POLICY_PRINT ppa
		                     where NOT EXISTS (SELECT 1
                            FROM DEV_PAS.T_CS_POLICY_CHANGE A , 
                            DEV_PAS.T_CS_ACCEPT_CHANGE B
                            WHERE  A.POLICY_CHG_ID=PPA.POLICY_CHG_ID
                             AND A.ACCEPT_ID =B.ACCEPT_ID 
                             AND B.ACCEPT_STATUS='19'
                             AND B.SERVICE_CODE IN ('LR','PT','RN','PU'))/*打印日期*/
		                         union
		                         select DECODE(PPN.PRINT_TYPE,'1',PPN.BPO_PRINT_DATE,PPN.PRINT_TIME) AS print_time,
		                                ppn.policy_code,
		                                ppn.print_type
		                           from DEV_NB.T_POLICY_PRINT ppn) a
		                  WHERE POLICY_CODE = T.RELATIOIN_POLICY_CODE
		                    AND PRINT_TIME is not null
		                    AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1')) 
		      else 
		         (SELECT min(PRINT_TIME)
		                   FROM (select DECODE(PPA.PRINT_TYPE,'1',PPA.BPO_PRINT_DATE,PPA.PRINT_TIME) AS print_time,
		                                ppa.policy_code,
		                                ppa.print_type
		                           from DEV_PAS.T_POLICY_PRINT ppa
		                    where NOT EXISTS (SELECT 1
                            FROM DEV_PAS.T_CS_POLICY_CHANGE A , 
                            DEV_PAS.T_CS_ACCEPT_CHANGE B
                            WHERE  A.POLICY_CHG_ID=PPA.POLICY_CHG_ID
                             AND A.ACCEPT_ID =B.ACCEPT_ID 
                             AND B.ACCEPT_STATUS='19'
                             AND B.SERVICE_CODE IN ('LR','PT','RN','PU'))
		                         union
		                         select DECODE(PPN.PRINT_TYPE,'1',PPN.BPO_PRINT_DATE,PPN.PRINT_TIME) AS print_time,
		                                ppn.policy_code,
		                                ppn.print_type
		                           from DEV_NB.T_POLICY_PRINT ppn) a
		                  WHERE POLICY_CODE = T.POLICY_CODE
		                    AND PRINT_TIME is not null
		                    AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1')) 
		    
		      END 
		       ) AS PRINT_TIME /*打印日期*/
		 FROM (
	SELECT DISTINCT 
	CM.POLICY_ID,
	 GST.GROUP_TYPE_DESC AS GROUP_SALE_TYPE,
	       CM.POLICY_CODE,
	       CM.APPLY_CODE,
	         TNCM.Drq_Flag,
	       (CASE WHEN ( CM.DOUBLE_MAINRISK_FLAG ='1' AND CM.RELATION_POLICY_CODE IS NULL AND TNCM.RELATION_POLICY_CODE IS NULL) THEN 
	         (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER NCM WHERE NCM.RELATION_POLICY_CODE = #{policy_code}
	         	AND NCM.DOUBLE_MAINRISK_FLAG = '1'
	         )
	         ELSE
	           CM.APPLY_CODE
	       END 
	       ) AS RELATIOIN_APPLY_CODE,
	         (CASE WHEN ( CM.DOUBLE_MAINRISK_FLAG ='1' AND CM.RELATION_POLICY_CODE IS NULL AND TNCM.RELATION_POLICY_CODE IS NULL) THEN 
	         (SELECT POLICY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER NCM WHERE NCM.RELATION_POLICY_CODE = #{policy_code}
	         	AND NCM.DOUBLE_MAINRISK_FLAG = '1'
	         )
	         ELSE
	           CM.POLICY_CODE
	       END 
	       ) AS RELATIOIN_POLICY_CODE,
	       CM.DOUBLE_MAINRISK_FLAG,
	       CM.MEDIA_TYPE,
	       TNCM.BRANCH_ORGAN_CODE,
           TNCM.BANK_MANAGER_LICENSENO,
	       CM.POLICY_TYPE,
	       CM.INPUT_TYPE,
	       '' AS CHANNEL_TYPE,
	       CM.APPLY_DATE,
	       /*#173020综合查询增加渠道销售方式、是否为互联网保单标识*/
       	   b.type_desc AS SALE_TYPE,
       	   to_char(xx.INTERNET_COOP_BUSI) AS INTERNET_COOPERATION_BUSINESS,
	       CM.ISSUE_DATE,
	       CM.VALIDATE_DATE,
	       CM.EXPIRY_DATE,
	       CM.LIABILITY_STATE,
	       CM.END_CAUSE,
	       CM.LAPSE_CAUSE,
	       CM.LAPSE_DATE,
	       CM.SUBMISSION_DATE,
	       CM.SUBINPUT_TYPE,
	       CM.ORGAN_CODE, /*单管理机构地址*/
	       CM.IS_MUTUAL_INSURED,
	       CM.AGENT_ORG_ID,
	       CM.IS_SELF_INSURED,
	       CM.SPECIAL_ACCOUNT_FLAG,/*专户投保标识*/
	       CM.TRUST_BUSI_FLAG,/* 信托业务标识  */
	       (case 
	         when CM.NOTIFICATION_RECEIVE_METHOD = '1' then  '短信'
	          when CM.NOTIFICATION_RECEIVE_METHOD = '2' then '邮寄'
	         when CM.NOTIFICATION_RECEIVE_METHOD = '3' then  '自助下载'
	         else  '' 
	       end) as NOTIFICATION_RECEIVE_METHOD,/*新型产品通知接收方式*/
	       (SELECT b.bank_name from dev_pas.t_bank b WHERE b.bank_code = trim(cm.service_bank)) AS SERVICE_BANK, /*代理银行*/
	       (SELECT bb.bank_branch_name from dev_pas.T_BANK_BRANCH bb WHERE bb.bank_branch_code = CM.SERVICE_BANK_BRANCH) AS SERVICE_BANK_BRANCH, /*银行网点*/
	       (SELECT CBP.PAIDUP_DATE
	          FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
	         WHERE ROWNUM = 1
	           AND CBP.POLICY_ID = CM.POLICY_ID
	           AND CBP.MASTER_BUSI_ITEM_ID IS NULL
	           AND (CBP.ORDER_ID = 1 OR CBP.ORDER_ID IS NULL)) AS PAIDUP_DATE,
	       (SELECT CE.POLICY_YEAR
	          FROM DEV_PAS.T_CONTRACT_EXTEND CE
	         WHERE CE.POLICY_ID = CM.POLICY_ID
	           AND CE.BUSI_ITEM_ID=MASTERINFO.BUSI_ITEM_ID AND ROWNUM=1) AS PERM_YEAR,
	       (SELECT CE.PAY_DUE_DATE
	      	  FROM DEV_PAS.T_CONTRACT_EXTEND CE
	         WHERE CE.POLICY_ID = CM.POLICY_ID
	          AND CE.BUSI_ITEM_ID=MASTERINFO.BUSI_ITEM_ID AND ROWNUM=1) AS PAY_DUE_DATE,   /*续期缴费日期*/	
	       (TRUNC(MONTHS_BETWEEN(SYSDATE, CM.VALIDATE_DATE) / 12)) + 1 AS POLICY_YEAR,
	       CM.BRANCH_CODE,
	       A.SIGN_SOURCE,
	         NVL(AP.ACKNOWLEDGE_DATE,A.ACKNOWLEDGE_DATE) AS ACKNOWLEDGE_DATE,
	      (SELECT MAX(SIGN_TIME) FROM DEV_NB.T_CONTRACT_SIGN NCS WHERE NCS.POLICY_CODE=CM.POLICY_CODE 
	) AS BRANCH_RECEIVE_DATE,
	       A.E_ACKNOWLEDGE_DATE,
	   /* #51437修改网银纸质保单回单时间取值方式*/
       (
	 	CASE 
	 	WHEN (SELECT COUNT(1)
                                  FROM  DEV_NB.T_NB_CONTRACT_MASTER MSTP                                    
                                 WHERE MSTP.POLICY_CODE = A.POLICY_CODE
                                 AND MSTP.SUBMIT_CHANNEL =1
                                 AND MSTP.MEDIA_TYPE=2
                                   AND MSTP.PROPOSAL_STATUS = '14'
                                   ) > 0 THEN
                           A.P_ACKNOWLEDGE_DATE
	 	WHEN (SELECT COUNT(1) FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER MST ON PPD.POLICY_CODE = MST.POLICY_CODE 
		     WHERE PPD.POLICY_CODE = A.POLICY_CODE AND MST.PROPOSAL_STATUS = '14') > 0
 	 	THEN A.P_ACKNOWLEDGE_DATE
	 	WHEN (SELECT COUNT(1) FROM DEV_NB.T_PROPOSAL_PROCESS PPR LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER MT ON PPR.APPLY_CODE = MT.APPLY_CODE 
			 WHERE PPR.APPLY_CODE = CM.APPLY_CODE AND PPR.PROCESS_STEP = '251' AND MT.PROPOSAL_STATUS = '14') > 0
	 	THEN A.P_ACKNOWLEDGE_DATE
	 	ELSE NULL
	 END
	   ) AS P_ACKNOWLEDGE_DATE,/*97756若申请过纸质保单的，并且已操作过回执的，则显示‘纸质保单签收日期’*/
	       NVL( AP.BRANCH_RECEIVE_DATE ,A.BRANCH_RECEIVE_DATE) AS ACKNOWLEDGE_OPERATION_DATE,
	      /*AP.BRANCH_RECEIVE_DATE AS ACKNOWLEDGE_OPERATION_DATE,回执回销时间*/
	
	       A.UPDATE_TIME,
	       TNCM.CALL_TIME_LIST AS REVIEW_TIME_PERIOD,
	       AA.EMAIL,
	       MASTERINFO.PRODUCT_NAME_SYS, /*AS '主险名称'*/
	       MASTERINFO.CHARGE_NAME, /* AS '缴费方式'*/
	       MASTERINFO.CHARGE_DESC,  /* 缴费期间*/
	       MASTERINFO.PREM, /*保费*/
	       MASTERINFO.AMOUNT, /*保额*/
	       TNCM.SUBMIT_CHANNEL,
	       CM.POLICY_FLAG,/*保单标识*/
	      (CASE
	         WHEN ((SELECT SYSDATE FROM DUAL) -
	              NVL(AP.ACKNOWLEDGE_DATE,A.ACKNOWLEDGE_DATE)) - MASTERINFO.HESITATION_PERIOD_DAY <= 0 THEN
	          1
	         ELSE
	         0
	       END) HESITATION_PERIOD_DAY,/*是否犹豫期 */
	       M.IS_SUBSCRIPTION_EMAIL AS IS_SUBSCRIPTION_EMAIL /*电子函件服务*/,
	        (SELECT PC.POLICY_CODE
	      FROM  DEV_PAS.T_CS_ACCEPT_CHANGE AC
	    INNER JOIN  DEV_PAS.T_CS_POLICY_CHANGE PC
	        ON AC.ACCEPT_ID = PC.ACCEPT_ID
	        AND PC.CHANGE_ID = AC.CHANGE_ID
	     INNER JOIN DEV_PAS.T_UW_MASTER UM ON AC.ACCEPT_CODE=UM.BIZ_CODE
	      WHERE AC.SERVICE_CODE = 'HI' 
	         AND UM.UW_CANCEL_CAUSE= 'HI'
	         AND PC.POLICY_CODE = CM.POLICY_CODE AND ROWNUM=1) AS cs_addinform_overdueend_flag, /*保全增补告知逾期终止标识*/
	      (SELECT PC.POLICY_CODE
	      FROM  DEV_PAS.T_CS_ACCEPT_CHANGE AC
	    INNER JOIN  DEV_PAS.T_CS_POLICY_CHANGE PC
	        ON AC.ACCEPT_ID = PC.ACCEPT_ID
	        AND PC.CHANGE_ID = AC.CHANGE_ID
	     INNER JOIN DEV_PAS.T_UW_MASTER UM ON AC.ACCEPT_CODE=UM.BIZ_CODE
	      WHERE AC.SERVICE_CODE = 'RE' 
	         AND UM.UW_CANCEL_CAUSE= 'RE'
	         AND PC.POLICY_CODE = CM.POLICY_CODE AND ROWNUM=1) AS cs_reeffect_overdueend_flag, /*保全复效逾期终止标识*/
	    NVL((SELECT CARD_DESC FROM DEV_PAS.T_MEDICAL_INSURANCE_CARD TIC WHERE  CARD_CODE=CM.MEDICAL_INSURANCE_CARD),'') AS MEDICAL_INSURANCE_CARD,/*医保标识，目前只有深圳医保，上海医保之前设计此表未增加*/
		(SELECT POLICY_CODE FROM DEV_PAS.T_CONTRACT_MASTER MM WHERE MM.POLICY_ID=CM.FORMER_ID) AS from_policy_code, /*原保单号/续保保单 */
	     (
	     CASE WHEN CM.SUBMIT_CHANNEL='14' AND CM.FORMER_ID IS NULL THEN
	     (
	     	SELECT POLICY_CODE FROM DEV_PAS.T_CONTRACT_MASTER MM WHERE MM.FORMER_ID=CM.POLICY_ID
	     )ELSE '' END 
     	) AS NEXT_POLICY_CODE,/*续保保单号*/
     	CM.POLICY_REINSURE_FLAG,/*保单续投/转保标识,码表T_POLICY_REINSURE_TYPE*/
     	/*(SELECT DISTINCT (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_SOURCE PPS WHERE PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE )  
 FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD  WHERE  PPD.APPLY_CODE = TNCM.APPLY_CODE) PAPER_APPLY_SOURCE, 纸质保单申请渠道*/
  (case  
	     when (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = TNCM.apply_code) > 0 /*电子保单申请过纸质保单*/
	     then (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD LEFT JOIN DEV_NB.T_POLICY_PRINT_SOURCE PPS ON PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE WHERE PPD.APPLY_CODE = TNCM.APPLY_CODE AND ROWNUM <= 1)
	     when to_number(TNCM.subinput_type) <> 15 and TNCM.submit_channel = '1' and tt.media_type in ('0','2') /*保全做过变更并且是电子保单非银保通电子渠道出单*/
	     then  (SELECT tst.type_desc FROM dev_nb.t_input_type tst where  TNCM.input_type = tst.type_code)
	     when to_number(TNCM.subinput_type) <> 15 and TNCM.submit_channel = '1' and TNCM.media_type = '0' and cm.media_type = '1' and tt.media_type = '1' /*判断完保全再判断契约*/
	     then  ''
	     when tt.media_type is not null and (select count(*) from dev_nb.t_policy_print_detail pd where pd.apply_code = TNCM.apply_code) = 0 and TNCM.media_type <> tt.media_type
	      then ''
	     ELSE ''
	      end )as PAPER_APPLY_SOURCE,
     CM.LAPSE_LOAN_SUSPEND_DATE /*续期失效保单贷款中止日期*/
	  FROM DEV_PAS.T_CONTRACT_MASTER CM
	   /*#173020综合查询增加渠道销售方式、是否为互联网保单标识*/ 
   	  LEFT JOIN DEV_NB.T_SALE_TYPE b on b.type_code=CM.sale_type
   	  LEFT JOIN DEV_NB.T_NB_POLICY_MASTER_EXTEND xx on xx.apply_code=CM.apply_code
	  LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT A
	    ON A.POLICY_CODE = CM.POLICY_CODE
	   LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT AP
	    ON AP.POLICY_ID = CM.POLICY_ID 
	  LEFT JOIN (SELECT AD.EMAIL, NPH.POLICY_CODE
	               FROM DEV_PAS.T_POLICY_HOLDER NPH
	              INNER JOIN DEV_PAS.T_ADDRESS AD
	                 ON NPH.ADDRESS_ID = AD.ADDRESS_ID WHERE 1=1  ]]>   
	                 <if test=" apply_code != null and apply_code != ''  ">
	                  AND NPH.APPLY_CODE=#{apply_code}
	                  </if>
	            <![CDATA[ ) AA  
	    ON CM.POLICY_CODE = AA.POLICY_CODE
		LEFT JOIN DEV_PAS.T_POLICY_HOLDER PH 
	    ON PH.POLICY_CODE = CM.POLICY_CODE
	    LEFT JOIN DEV_PAS.T_CUSTOMER M 
	    ON M.CUSTOMER_ID = PH.CUSTOMER_ID
		
	  LEFT JOIN
	
	 (
	  
	  SELECT TB.PRODUCT_NAME_SYS /*AS '主险名称'*/,
	          TCMD.CHARGE_NAME /* AS '缴费方式'*/,
	          (case 
	          when TEMP.CHARGE_PERIOD = '1' THEN
	            '一次交清'
	             when TEMP.CHARGE_PERIOD = '2' THEN
	             TEMP.CHARGE_YEAR||'年'
	             when TEMP.CHARGE_PERIOD = '3' THEN
	            '交至'||TEMP.CHARGE_YEAR||'岁'
	            when TEMP.CHARGE_PERIOD = '4' THEN
	            '终身交费'
	            when TEMP.CHARGE_PERIOD = '5' THEN
	            '不定期交'
	            when TEMP.CHARGE_PERIOD = '6' THEN
	            TEMP.CHARGE_YEAR||'月'
	            when TEMP.CHARGE_PERIOD = '7' THEN
	            TEMP.CHARGE_YEAR||'天'
	         ELSE
	        ''
	       end) as CHARGE_DESC,/*缴费期间*/
	          TEMP.PREM, /*保费*/
	          TEMP.AMOUNT, /*保额*/
	          TEMP.POLICY_CODE,
	          TEMP.BUSI_ITEM_ID,
	          TEMP.HESITATION_PERIOD_DAY
	    FROM (SELECT SUM(T.AMOUNT) AMOUNT,
	                  SUM(T.STD_PREM_AF) PREM,
	                  MAX(T.CHARGE_YEAR) AS CHARGE_YEAR,
	                  MAX(T.CHARGE_PERIOD) AS CHARGE_PERIOD,
	                  MAX(T.PREM_FREQ) AS FREQ,
	                  MAX(TCBP.BUSI_PROD_CODE) AS BUSI_CODE,
	                  TCBP.POLICY_CODE AS POLICY_CODE,
	                  TCBP.BUSI_ITEM_ID,
	                  TCBP.HESITATION_PERIOD_DAY
	             FROM DEV_PAS.T_CONTRACT_PRODUCT T
	             LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
	               ON T.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
	            WHERE
	            1=1 
	            
	           ]]>   
	        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCBP.POLICY_CODE = #{policy_code} ]]></if>
	        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TCBP.APPLY_CODE = #{apply_code} ]]></if>
	         
	     <![CDATA[   
	    AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
	    GROUP BY TCBP.POLICY_CODE,TCBP.BUSI_ITEM_ID,TCBP.HESITATION_PERIOD_DAY) TEMP             
	    LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT TB
	      ON TB.PRODUCT_CODE_SYS = TEMP.BUSI_CODE
	    LEFT JOIN DEV_PAS.T_CHARGE_MODE TCMD
	      ON TEMP.FREQ = TCMD.CHARGE_TYPE
	  ) MASTERINFO
	    ON MASTERINFO.POLICY_CODE = CM.POLICY_CODE 
	    LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER TNCM 
	    ON TNCM.POLICY_CODE = CM.POLICY_CODE
	    LEFT JOIN DEV_NB.T_GROUP_SALE_TYPE GST
   		ON TNCM.GROUP_SALE_TYPE = GST.GROUP_TYPE_CODE
   		LEFT JOIN (SELECT LOG_ID,POLICY_CODE,MEDIA_TYPE  FROM 
                    (SELECT MSTG.LOG_ID,MSTG.POLICY_CODE,MSTG.MEDIA_TYPE 
                    ,row_number() over(partition by MSTG.POLICY_CODE ORDER BY MSTG.LOG_ID  ASC ) RN
                    FROM DEV_PAS.T_CONTRACT_MASTER_LOG MSTG)
                    WHERE RN = 1) TT ON TT.POLICY_CODE = TNCM.POLICY_CODE
	 WHERE 1 = 1 
			]]>
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
	        AND ROWNUM=1
	 )T
	</select>
    <!--查询双主险保单信息  -->
    <select id="QRY_findRelationPolicyInfoByCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
			SELECT DISTINCT 
			       CM.POLICY_ID,
			       CM.POLICY_CODE,
			       CM.APPLY_CODE,
			        (
			       CASE WHEN (CM.DOUBLE_MAINRISK_FLAG ='1' AND CM.RELATION_POLICY_CODE IS NULL) THEN
			          (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER NCM WHERE NCM.RELATION_POLICY_CODE= #{policy_code}
			          	AND NCM.DOUBLE_MAINRISK_FLAG = '1'
			          )
                     ELSE
                      CM.APPLY_CODE
			        END 
			       ) AS RELATIOIN_APPLY_CODE,
			       CM.DOUBLE_MAINRISK_FLAG,
      			   CM.WEBINPUT_BACKTRACKING_QUREY/*互联网可回溯资料查询按钮是否可以点击*/			       
			  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
			   WHERE 1=1 
			           ]]>   
			        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
			        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
			         
			     <![CDATA[   
			        AND ROWNUM=1
			        
			        ]]>
    </select>
    
    <!-- 根据保单号查询保单信息 --> 
    <select id="QRY_findPolicyInfoByCodeA" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT 
       DISTINCT CM.POLICY_ID,
        CM.IS_MUTUAL_INSURED ,
       GST.GROUP_TYPE_DESC AS GROUP_SALE_TYPE,
       CM.POLICY_CODE,
       CM.APPLY_CODE,
        CM.APPLY_CODE AS RELATIOIN_APPLY_CODE,
       CM.POLICY_CODE AS RELATIOIN_POLICY_CODE,
       CM.MEDIA_TYPE,
       NCM.Drq_Flag,
       NCM.BRANCH_ORGAN_CODE,
       NCM.BANK_MANAGER_LICENSENO,
       CM.POLICY_TYPE, 
       CM.ORGAN_CODE,
       '' AS CHANNEL_TYPE,
       CM.APPLY_DATE,
        /*#173020综合查询增加渠道销售方式、是否为互联网保单标识*/
        b.type_desc AS SALE_TYPE,
       to_char(xx.INTERNET_COOP_BUSI) AS INTERNET_COOPERATION_BUSINESS,
       CM.ISSUE_DATE,
       CM.INPUT_TYPE,
       CM.VALIDATE_DATE,
       CM.EXPIRY_DATE,
       CM.LIABILITY_STATE,
       CM.END_CAUSE,
       CM.LAPSE_CAUSE,
       CM.LAPSE_DATE,
       CM.SUBMISSION_DATE,
       CM.SUBINPUT_TYPE,
       CM.SPECIAL_ACCOUNT_FLAG,/*专户投保标识*/
       CM.ORGAN_CODE, /*保单管理机构地址*/
       CM.IS_MUTUAL_INSURED,
       CM.IS_SELF_INSURED,
       CM.AGENT_ORG_ID,
       CM.TRUST_BUSI_FLAG,/* 信托业务标识  */
	    (case 
	       when CM.NOTIFICATION_RECEIVE_METHOD = '1' then  '短信'
	        when CM.NOTIFICATION_RECEIVE_METHOD = '2' then '邮寄'
	       when CM.NOTIFICATION_RECEIVE_METHOD = '3' then  '自助下载'
	       else  '' 
	     end) as NOTIFICATION_RECEIVE_METHOD,/*新型产品通知接收方式*/
       (SELECT b.bank_name from dev_pas.t_bank b WHERE b.bank_code = trim(cm.service_bank)) AS SERVICE_BANK, /*代理银行*/
       (SELECT bb.bank_branch_name from dev_pas.T_BANK_BRANCH bb WHERE bb.bank_branch_code = CM.SERVICE_BANK_BRANCH) AS SERVICE_BANK_BRANCH, /*银行网点*/
       (SELECT CBP.PAIDUP_DATE
          FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
         WHERE ROWNUM = 1
           AND CBP.POLICY_ID = CM.POLICY_ID
           AND CBP.MASTER_BUSI_ITEM_ID IS NULL) AS PAIDUP_DATE,
       (SELECT CE.POLICY_YEAR
          FROM DEV_PAS.T_CONTRACT_EXTEND CE
         WHERE CE.POLICY_ID = CM.POLICY_ID
           AND CE.BUSI_ITEM_ID=MASTERINFO.BUSI_ITEM_ID AND ROWNUM=1) AS PERM_YEAR,
       (TRUNC(MONTHS_BETWEEN(SYSDATE, CM.VALIDATE_DATE) / 12)) + 1 AS POLICY_YEAR,
 		CM.BRANCH_CODE, 
 		A.SIGN_SOURCE,
         NVL(AP.ACKNOWLEDGE_DATE ,A.ACKNOWLEDGE_DATE ) AS ACKNOWLEDGE_DATE,
      AP.BRANCH_RECEIVE_DATE,
      A.E_ACKNOWLEDGE_DATE,
      A.P_ACKNOWLEDGE_DATE,/*网银纸质保单回单时间*//* #51437 修改网银纸质保单回执时间取值方式*/
         nvl(AP.BRANCH_RECEIVE_DATE ,A.BRANCH_RECEIVE_DATE ) AS ACKNOWLEDGE_OPERATION_DATE,
       A.UPDATE_TIME,
         '' AS REVIEW_TIME_PERIOD,
         (SELECT min(PRINT_TIME) FROM 
        (select DECODE(PPA.PRINT_TYPE,'1',PPA.BPO_PRINT_DATE,PPA.PRINT_TIME) AS print_time,ppa.policy_code,ppa.print_type from DEV_PAS.T_POLICY_PRINT  ppa
        union 
        select DECODE(PPN.PRINT_TYPE,'1',PPN.BPO_PRINT_DATE,PPN.PRINT_TIME) AS print_time,ppn.policy_code,ppn.print_type  from DEV_NB.T_POLICY_PRINT  ppn) a
        WHERE POLICY_CODE=CM.POLICY_CODE AND PRINT_TIME is not null
                 AND (PRINT_TYPE='0' OR PRINT_TYPE='1')
       ) AS PRINT_TIME,/*打印日期*/
        (SELECT min(PRINT_TIME) FROM 
        (select ppa.print_time,ppa.policy_code,ppa.print_type from DEV_PAS.T_POLICY_PRINT  ppa
        union 
        select ppn.print_time,ppn.policy_code,ppn.print_type  from DEV_NB.T_POLICY_PRINT  ppn) a
        WHERE POLICY_CODE=CM.POLICY_CODE AND PRINT_TIME is not null
                 AND (PRINT_TYPE='2')
       ) AS E_POLICY_SEND_TIME,/*电子保单发送日期*/
       AA.EMAIL,
       MASTERINFO.PRODUCT_NAME_SYS, /*AS '主险名称'*/
       MASTERINFO.CHARGE_NAME, /* AS '缴费方式'*/
       MASTERINFO.CHARGE_DESC, /* -缴费期间*/
       MASTERINFO.PREM, /* 保费'*/
       MASTERINFO.AMOUNT, /*保额'*/
      (CASE
         WHEN ((SELECT SYSDATE FROM DUAL) -
              nvl(AP.ACKNOWLEDGE_DATE,A.ACKNOWLEDGE_DATE)) - MASTERINFO.HESITATION_PERIOD_DAY <= 0 THEN
          1
         ELSE
         0
       END) HESITATION_PERIOD_DAY, /*是否犹豫期 */
       
       TNCM.SUBMIT_CHANNEL ,
        CM.POLICY_FLAG,/*保单标识*/
        (SELECT PC.POLICY_CODE
      FROM  DEV_PAS.T_CS_ACCEPT_CHANGE AC
    INNER JOIN  DEV_PAS.T_CS_POLICY_CHANGE PC
        ON AC.ACCEPT_ID = PC.ACCEPT_ID
        AND PC.CHANGE_ID = AC.CHANGE_ID
     INNER JOIN DEV_PAS.T_UW_MASTER UM ON AC.ACCEPT_CODE=UM.BIZ_CODE
      WHERE AC.SERVICE_CODE = 'HI' 
         AND UM.UW_CANCEL_CAUSE= 'HI'
         AND PC.POLICY_CODE = CM.POLICY_CODE AND ROWNUM=1) AS cs_addinform_overdueend_flag, /*保全增补告知逾期终止标识*/
      (SELECT PC.POLICY_CODE
      FROM  DEV_PAS.T_CS_ACCEPT_CHANGE AC
    INNER JOIN  DEV_PAS.T_CS_POLICY_CHANGE PC
        ON AC.ACCEPT_ID = PC.ACCEPT_ID
        AND PC.CHANGE_ID = AC.CHANGE_ID
     INNER JOIN DEV_PAS.T_UW_MASTER UM ON AC.ACCEPT_CODE=UM.BIZ_CODE
      WHERE AC.SERVICE_CODE = 'RE' 
         AND UM.UW_CANCEL_CAUSE= 'RE'
         AND PC.POLICY_CODE = CM.POLICY_CODE AND ROWNUM=1) AS cs_reeffect_overdueend_flag, /*保全复效逾期终止标识*/
     (SELECT CARD_DESC FROM DEV_PAS.T_MEDICAL_INSURANCE_CARD TIC WHERE  CARD_CODE=CM.MEDICAL_INSURANCE_CARD) AS MEDICAL_INSURANCE_CARD,/*医保标识，目前只有深圳医保，上海医保之前设计此表未增加*/
     (SELECT POLICY_CODE FROM DEV_PAS.T_CONTRACT_MASTER MM WHERE MM.POLICY_ID=CM.FORMER_ID ) AS FROM_POLICY_CODE,/*原保单号*/
     '' AS NEXT_POLICY_CODE,/*网银保单默认为空续保保单号*/
     CM.POLICY_REINSURE_FLAG,/*保单续投/转保标识,码表T_POLICY_REINSURE_TYPE*/
      (SELECT DISTINCT (SELECT PPS.PRINT_SOURCE_NAME FROM DEV_NB.T_POLICY_PRINT_SOURCE PPS WHERE PPS.PRINT_SOURCE_CODE = PPD.APPLY_SOURCE )  
 FROM DEV_NB.T_POLICY_PRINT_DETAIL PPD  WHERE  PPD.APPLY_CODE = NCM.APPLY_CODE) PAPER_APPLY_SOURCE, /*纸质保单申请渠道*/
      CM.LAPSE_LOAN_SUSPEND_DATE /*续期失效保单贷款中止日期*/
  FROM DEV_PAS.T_CONTRACT_MASTER CM
 /*#173020综合查询增加渠道销售方式、是否为互联网保单标识*/
 LEFT JOIN DEV_NB.T_SALE_TYPE b on b.type_code=CM.sale_type
 LEFT JOIN DEV_NB.T_NB_POLICY_MASTER_EXTEND xx on xx.apply_code=CM.apply_code
 LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT A
    ON A.POLICY_CODE = CM.POLICY_CODE
   LEFT JOIN DEV_PAS.T_POLICY_ACKNOWLEDGEMENT AP
    ON AP.POLICY_ID = CM.POLICY_ID
  LEFT JOIN DEV_NB.T_POLICY_PRINT TPP
    ON CM.POLICY_CODE = TPP.POLICY_CODE AND TPP.PRINT_TIME IS NOT NULL 
  LEFT JOIN (SELECT CU.EMAIL, NPH.POLICY_CODE
               FROM DEV_PAS.T_POLICY_HOLDER NPH
              INNER JOIN DEV_PAS.T_ADDRESS CU
                 ON NPH.ADDRESS_ID = CU.ADDRESS_ID WHERE 1=1]]>   
                 <if test=" apply_code != null and apply_code != ''  ">
                  AND NPH.APPLY_CODE=#{apply_code}
                  </if>
                  <![CDATA[  
                 ) AA
    ON CM.POLICY_CODE = AA.POLICY_CODE
  LEFT JOIN
 (
  
  SELECT TB.PRODUCT_NAME_SYS /*AS '主险名称'*/,
          TCMD.CHARGE_NAME /* AS '缴费方式'*/,
          TP.CHARGE_DESC, --缴费期间
          TEMP.PREM, --保费
          TEMP.AMOUNT, --保额
          TEMP.POLICY_CODE,
          TEMP.BUSI_ITEM_ID,
           TEMP.HESITATION_PERIOD_DAY
    FROM (SELECT SUM(T.AMOUNT) AMOUNT,
                  SUM(T.TOTAL_PREM_AF) PREM,
                  MAX(T.CHARGE_YEAR) AS CYEAR,
                  MAX(T.PREM_FREQ) AS FREQ,
                  MAX(TCBP.BUSI_PROD_CODE) AS BUSI_CODE,
                  TCBP.POLICY_CODE AS POLICY_CODE,
                  TCBP.BUSI_ITEM_ID,
                   TCBP.HESITATION_PERIOD_DAY
             FROM DEV_PAS.T_CONTRACT_PRODUCT T
             LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
               ON T.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
            WHERE
            1=1 
            
           ]]>   
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCBP.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TCBP.APPLY_CODE = #{apply_code} ]]></if>
         
     <![CDATA[   
    AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
    GROUP BY TCBP.POLICY_CODE,TCBP.BUSI_ITEM_ID,TCBP.HESITATION_PERIOD_DAY) TEMP             
    LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT TB
      ON TB.PRODUCT_CODE_SYS = TEMP.BUSI_CODE
    LEFT JOIN DEV_PAS.T_CHARGE_MODE TCMD
      ON TEMP.FREQ = TCMD.CHARGE_TYPE
    LEFT JOIN DEV_PAS.T_CHARGE_PERIOD TP
      ON TEMP.CYEAR = TP.CHARGE_PERIOD
  ) MASTERINFO
    ON MASTERINFO.POLICY_CODE = CM.POLICY_CODE 
     LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TNCM
    ON TNCM.POLICY_CODE = CM.POLICY_CODE
    LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM
    ON NCM.POLICY_CODE = CM.POLICY_CODE
     LEFT JOIN DEV_NB.T_GROUP_SALE_TYPE GST
    ON TNCM.GROUP_SALE_TYPE = GST.GROUP_TYPE_CODE
 WHERE 1 = 1
        ]]>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
        AND ROWNUM=1
    </select>
    
	<!-- 根据投保单号查询保单信息 -->
	<select id="QRY_findPolicyInfoByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
SELECT DISTINCT AG.AGENT_NAME,
       AG.AGENT_ORGAN_CODE,
       CA.AGENT_CODE,
       CM.POLICY_ID,
       CM.POLICY_CODE,
       CM.INPUT_TYPE,
       CM.APPLY_CODE,
       CM.MEDIA_TYPE,
       CM.POLICY_TYPE,
       CM.ORGAN_CODE,
       CM.CHANNEL_TYPE,
       (SELECT ORG.ORGAN_ADDRESS
          FROM DEV_PAS.T_UDMP_ORG ORG
         WHERE ORG.ORGAN_CODE = CM.ORGAN_CODE) AS ORGAN_ADDRESS,
       CM.SALE_AGENT_NAME,
       CM.APPLY_DATE,
       CM.ISSUE_DATE,
       CM.VALIDATE_DATE,
       CM.EXPIRY_DATE,
       (TRUNC(MONTHS_BETWEEN(SYSDATE, CM.VALIDATE_DATE) / 12)) + 1 AS POLICY_YEAR,
       CM.LIABILITY_STATE,
       CM.SUBMISSION_DATE,
       A.ACKNOWLEDGE_DATE,
        (SELECT MAX(SIGN_TIME) FROM DEV_NB.T_CONTRACT_SIGN NCS WHERE NCS.POLICY_CODE=CM.POLICY_CODE 
) AS BRANCH_RECEIVE_DATE,
       A.E_ACKNOWLEDGE_DATE,
       A.UPDATE_TIME,
       C.CALL_RESULT_SAVE_DATE,
        C.REVIEW_TIME_PERIOD,
       TPP.PRINT_TIME,
       AA.EMAIL,
       CM.SUBMIT_CHANNEL
  FROM DEV_NB.T_NB_CONTRACT_MASTER CM
  LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
    ON CA.POLICY_CODE = CM.POLICY_CODE
  LEFT JOIN DEV_PAS.T_AGENT AG
    ON AG.AGENT_CODE = CA.AGENT_CODE
  LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT A
    ON A.POLICY_CODE = CM.POLICY_CODE
  LEFT JOIN DEV_NB.T_CONTRACT_CALL C
    ON C.POLICY_CODE = CM.POLICY_CODE
  LEFT JOIN DEV_NB.T_POLICY_PRINT TPP
    ON CM.POLICY_CODE = TPP.POLICY_CODE
   AND TPP.PRINT_TIME IS NOT NULL
  --LEFT JOIN DEV_NB.T_CONTRACT_SIGN TCS
  --  ON CM.POLICY_CODE = TCS.POLICY_CODE /*取了SIGN_TIME会数据重复，查询页面看没有用到这个字段先除掉，表也不需要连接了*/
  LEFT JOIN (SELECT CU.EMAIL, NPH.POLICY_CODE
               FROM DEV_NB.T_NB_POLICY_HOLDER NPH
              INNER JOIN DEV_NB.T_CUSTOMER CU
                 ON NPH.CUSTOMER_ID = CU.CUSTOMER_ID) AA
    ON CM.POLICY_CODE = AA.POLICY_CODE
 WHERE 1 = 1
		]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!-- 根据保单号查询孤儿单信息 -->
	<select id="QRY_findOrphanPolicyInfoByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT CA.AGENT_START_DATE,CA.AGENT_END_DATE,A.AGENT_STATUS,A.AGENT_CHANNEL
FROM DEV_PAS.T_contract_agent CA 
INNER JOIN DEV_PAS.T_AGENT A
ON CA.AGENT_CODE = A.AGENT_CODE 
WHERE 1=1 AND CA.IS_CURRENT_AGENT = '1'
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CA.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND CA.APPLY_CODE = #{apply_code} ]]></if>
        and rownum=1
	</select>
	<!-- 根据保单号查询电话回访信息 -->
	<select id="QRY_findCallInfoByCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		        SELECT A.* FROM (SELECT CC.POLICY_CODE,
		               CC.CALL_RESULT_DATE_SYS, /*回访结果时间*/
		               CC.CALL_RESULT_SYS, /*回访结果*/
		               CC.CALL_FINISH /*是否完成回访*/
		          FROM DEV_NB.T_CONTRACT_CALL CC
		         WHERE 1 = 1 
        ]]>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CC.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" apply_code != null and apply_code != '' "><![CDATA[ AND CC.APPLY_CODE = #{apply_code} ]]></if>
        <![CDATA[ 
        ORDER BY CC.LIST_ID DESC, CC.CALL_RESULT_SAVE_DATE DESC ) A 
 		WHERE ROWNUM = 1 
        ]]>
	</select>
	<!-- 根据保单号查询投保人信息列表 -->
	<select id="QRY_findHoderCustomerInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
        SELECT
    		C.CUSTOMER_ID,
            C.CUSTOMER_NAME,
            'policyHolder' as CUSTOMER_ROLE,
            C.CUSTOMER_GENDER,
            C.CUSTOMER_CERT_TYPE,
            C.CUSTOMER_CERTI_CODE,
            C.CUSTOMER_BIRTHDAY,
            C.CUSTOMER_VIP,
            C.BLACKLIST_FLAG,
        	TRUNC (MONTHS_BETWEEN(CM.VALIDATE_DATE,C.CUSTOMER_BIRTHDAY)/12) AS CUSTOMER_AGE,
            C.CUSTOMER_LEVEL, 
            AD.EMAIL,
            AD.STATE,
	        AD.CITY,
	        AD.DISTRICT,
            AD.ADDRESS,
            DS.NAME || DC.NAME || DD.NAME || AD.ADDRESS AS ADDRESS_FULL,
       		(CASE 
                 WHEN AD.MOBILE_TEL IS NOT NULL AND AD.FIXED_TEL IS NOT NULL THEN AD.MOBILE_TEL || ' / ' || AD.FIXED_TEL 
                 WHEN AD.MOBILE_TEL IS NOT NULL AND  AD.FIXED_TEL IS  NULL THEN AD.MOBILE_TEL 
                 WHEN AD.MOBILE_TEL IS  NULL AND  AD.FIXED_TEL IS NOT NULL THEN AD.FIXED_TEL 
                 WHEN AD.MOBILE_TEL IS  NULL  AND  AD.FIXED_TEL IS  NULL THEN ''
             END) AS MOBILE_TEL 
             ,PH.POLICY_CODE
        FROM
           DEV_PAS.T_POLICY_HOLDER　PH
        INNER JOIN　 DEV_PAS.T_CUSTOMER C
        ON 
           PH.CUSTOMER_ID=C.CUSTOMER_ID
        INNER JOIN  DEV_PAS.T_CONTRACT_MASTER CM
        ON CM.POLICY_ID=PH.POLICY_ID
        LEFT JOIN DEV_PAS.T_ADDRESS AD
    	ON PH.ADDRESS_ID = AD.ADDRESS_ID
	    LEFT JOIN DEV_PAS.T_DISTRICT DS
	    ON DS.CODE = AD.STATE
		LEFT JOIN DEV_PAS.T_DISTRICT DC
		  ON DC.CODE = AD.CITY
		LEFT JOIN DEV_PAS.T_DISTRICT DD
		  ON DD.CODE = AD.DISTRICT
        WHERE 1=1]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
        <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND C.CUSTOMER_ID = #{customer_id} ]]></if>
        
	</select>
	
	<!-- 根据保单号查询投保人信息 -->
	<select id="findHoderInfoByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT PH.POLICY_CODE,
       PH.APPLY_CODE,
       C.CUSTOMER_ID,
       C.CUSTOMER_NAME,
       C.CUSTOMER_CERT_TYPE,
       C.CUSTOMER_CERTI_CODE,
       TJC.JOB_CODE  AS JOB_CODE,
       TRUNC (Months_between (CM.Apply_Date,c.customer_birthday)/12)+1 AS apply_age,--投保年龄
       C.Customer_Level,--客户等级
       C.Email
  FROM DEV_PAS.T_POLICY_HOLDER PH
 INNER JOIN DEV_PAS.T_CUSTOMER C
    ON PH.CUSTOMER_ID = C.CUSTOMER_ID
 LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
    ON cm.policy_code = ph.policy_code
  LEFT JOIN DEV_PAS.T_JOB_CODE TJC
    ON TJC.JOB_CODE = C.JOB_CODE
    WHERE 1=1
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND PH.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND PH.APPLY_CODE = #{apply_code} ]]></if>
        AND ROWNUM=1
	</select>
	<!-- 根据保单号查询被保险人信息 -->
	<select id="QRY_findInsuredCustomerInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
       SELECT
           TRUNC (MONTHS_BETWEEN(CM.VALIDATE_DATE,C.CUSTOMER_BIRTHDAY)/12) AS CUSTOMER_AGE,
           C.CUSTOMER_ID,
           C.CUSTOMER_NAME,
           (SELECT CASE
     	    WHEN TBI.ORDER_ID = '2' THEN
     	     'secondinsured'
     	    ELSE
    	      'insured'
   	 	   END FROM DEV_PAS.T_BENEFIT_INSURED TBI where 1=1 AND TBI.INSURED_ID = IL.LIST_ID AND TBI.POLICY_CODE = IL.policy_code) AS CUSTOMER_ROLE,  
           (CASE WHEN 
              (SELECT CBP.LIABILITY_STATE FROM DEV_PAS.T_BENEFIT_INSURED CB,DEV_PAS.T_CONTRACT_BUSI_PROD CBP WHERE CB.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID AND CB.INSURED_ID = IL.LIST_ID AND CBP.LIABILITY_STATE != 3 AND ROWNUM = 1)  IS NULL
            THEN '终止'
              ELSE '承保' 
            END) 
              CUSTOMERSTATE,
           (CASE
         	WHEN NBIL.ORDER_ID = '2' THEN
          		'secondinsured'
       	  	ELSE
         		'insured'
      	   	END) AS CUSTOMER_ROLE, /* #121014 修改区分展示 第一被保人、第二被保人 */
           C.CUSTOMER_GENDER,
           C.CUSTOMER_CERT_TYPE,
           C.CUSTOMER_CERTI_CODE,
           C.CUSTOMER_BIRTHDAY,
           C.CUSTOMER_LEVEL, 
           C.CUSTOMER_VIP,
           C.BLACKLIST_FLAG,
           AD.EMAIL,
           to_char(IL.Soci_Secu) as Soci_Secu,
           AD.STATE,
	       AD.CITY,
	       AD.DISTRICT,
           AD.ADDRESS,
           DS.NAME || DC.NAME || DD.NAME || AD.ADDRESS AS ADDRESS_FULL,
           IL.RELATION_TO_PH,/*bug4372增加*/
       	    (CASE 
               WHEN AD.MOBILE_TEL IS NOT NULL AND AD.FIXED_TEL IS NOT NULL THEN AD.MOBILE_TEL || ' / ' || AD.FIXED_TEL 
               WHEN AD.MOBILE_TEL IS NOT NULL AND  AD.FIXED_TEL IS  NULL THEN AD.MOBILE_TEL 
               WHEN AD.MOBILE_TEL IS  NULL AND  AD.FIXED_TEL IS NOT NULL THEN AD.FIXED_TEL 
               WHEN AD.MOBILE_TEL IS  NULL  AND  AD.FIXED_TEL IS  NULL THEN ''
            END) AS MOBILE_TEL /*移动电话*/
       FROM DEV_PAS.T_INSURED_LIST IL 
       INNER JOIN DEV_NB.T_NB_INSURED_LIST NBIL
    	ON IL.APPLY_CODE = NBIL.APPLY_CODE
 		AND IL.CUSTOMER_ID = NBIL.CUSTOMER_ID
       INNER JOIN DEV_PAS.T_CUSTOMER C 
       ON IL.CUSTOMER_ID = C.CUSTOMER_ID
       INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
       ON IL.POLICY_CODE = CM.POLICY_CODE
       LEFT JOIN DEV_PAS.T_ADDRESS AD
    	ON IL.ADDRESS_ID = AD.ADDRESS_ID
      LEFT JOIN DEV_PAS.T_DISTRICT DS
	    ON DS.CODE = AD.STATE
	  LEFT JOIN DEV_PAS.T_DISTRICT DC
	    ON DC.CODE = AD.CITY
	  LEFT JOIN DEV_PAS.T_DISTRICT DD
	    ON DD.CODE = AD.DISTRICT
        WHERE 1=1]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND IL.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND IL.APPLY_CODE = #{apply_code} ]]></if>
        <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND C.CUSTOMER_ID = #{customer_id} ]]></if>
	</select>
	<!-- 根据保单号查询被保险人信息 -->
	<select id="findInsuredInfoByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
      SELECT IL.RELATION_TO_PH,
       C.CUSTOMER_NAME,
       C.CUSTOMER_CERT_TYPE,
       C.CUSTOMER_CERTI_CODE,
       TJC.JOB_CODE AS JOB_CODE,
       C.Customer_Gender,
       C.Customer_Birthday,
       C.Customer_Level,
       C.Email,
       TO_CHAR(C.SOCI_SECU) AS SOCI_SECU,
       C.Customer_Vip,
       TRUNC (Months_between (CM.Apply_Date,c.customer_birthday)/12)+1 AS apply_age
  FROM DEV_PAS.T_INSURED_LIST IL
 INNER JOIN DEV_PAS.T_CUSTOMER C
    ON IL.CUSTOMER_ID = C.CUSTOMER_ID
 LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
    ON cm.policy_code = IL.policy_code
 LEFT JOIN DEV_PAS.T_JOB_CODE TJC
    ON TJC.JOB_CODE = C.JOB_CODE
 WHERE 1 = 1
        ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND IL.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND IL.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	
	
	    
    
    <!-- 根据保单号查询受益人信息（针对受益人变更（信托）） -->
      <select id="QRY_findBeneForTrustCustomerInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            SELECT DISTINCT 
                    CB.POLICY_CODE,
                    CB.COMPANY_ID,/* 信托公司id */
                    CB.CUSTOMER_ID,
                    CB.SHARE_RATE,
                    CB.SHARE_ORDER,
                    CB.DESIGNATION,
                    NVL(C.CUSTOMER_NAME, CB.CUSTOMER_NAME) AS CUSTOMER_NAME,
                    NVL(C.CUSTOMER_BIRTHDAY, CB.CUSTOMER_BIRTHDAY) AS CUSTOMER_BIRTHDAY,
                    NVL(C.CUSTOMER_GENDER, CB.CUSTOMER_GENDER) AS CUSTOMER_GENDER,
                    NVL(C.CUSTOMER_CERT_TYPE, CB.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE,
                    NVL(C.CUSTOMER_CERTI_CODE, CB.CUSTOMER_CERTI_CODE) AS CUSTOMER_CERTI_CODE,
                    CB.INSURED_ID,
                    CBP.BUSI_PRD_ID,
                    CBP.BUSI_PROD_CODE,
                    CBP.VALIDATE_DATE,
                    BP.PRODUCT_NAME_SYS,
                    'benefit' AS CUSTOMER_ROLE,
                    (SELECT CU.CUSTOMER_NAME
                       FROM DEV_PAS.T_BENEFIT_INSURED BI
                      INNER JOIN DEV_PAS.T_INSURED_LIST IL
                         ON BI.POLICY_CODE = IL.POLICY_CODE
                        AND BI.INSURED_ID = IL.LIST_ID
                      INNER JOIN DEV_PAS.T_CUSTOMER CU
                         ON IL.CUSTOMER_ID = CU.CUSTOMER_ID
                      WHERE BI.POLICY_CODE = CBP.POLICY_CODE
                        AND BI.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
                        AND BI.ORDER_ID = 1
                        AND ROWNUM = 1) AS INC_CUSTOMER_NAME,
                    (Select ba.BANK_ACCOUNT
                       from dev_pas.t_bank_account ba
                      where ba.customer_id = CB.Customer_Id
                        and rownum = 1) BANK_ACCOUNT,
                    (Select ba.acco_name
                       from dev_pas.t_bank_account ba
                      where ba.customer_id = CB.Customer_Id
                        and rownum = 1) ACCO_NAME,
                    (Select db.bank_name
                       from dev_pas.t_bank_account ba
                      inner JOIN DEV_PAS.T_BANK DB
                         ON DB.BANK_CODE = ba.BANK_CODE
                      where ba.customer_id = CB.Customer_Id
                        and rownum = 1) BANK_NAME,
                    (select to_char(WM_CONCAT(b.bene_name))
                     from dev_pas.T_TRUST_COMPANY_bene b
                      where 1 = 1
                      and b.company_id = TC.COMPANY_ID) BENE_NAME, /*信托公司受益人*/   
                    CB.BENE_TYPE,
                    C.CUSTOMER_LEVEL,
                    AD.ADDRESS,
                    AD.STATE,
                    AD.CITY,
                    AD.DISTRICT,
                    DS.NAME || DC.NAME || DD.NAME || AD.ADDRESS AS ADDRESS_FULL,
                    AD.MOBILE_TEL,
                    TC.COMPANY_ORAN_CODE, 		    /*统一社会组织代码*/
                    TC.BUSI_LICENCE_CODE,  			/*营业执照号码*/
                    TC.TAX_CODE           		    /*税务登记号码*/
		      FROM DEV_PAS.T_CONTRACT_BENE CB
		      LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		        ON CBP.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
		      LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		        ON BP.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
		      LEFT JOIN DEV_PAS.T_CUSTOMER C
		        ON C.CUSTOMER_ID = CB.CUSTOMER_ID
		      LEFT JOIN dev_pas.T_TRUST_COMPANY TC
		        ON TC.COMPANY_ID = CB.COMPANY_ID  
		      LEFT JOIN DEV_PAS.T_ADDRESS AD
		        ON CB.ADDRESS_ID = AD.ADDRESS_ID
		      LEFT JOIN DEV_PAS.T_DISTRICT DS
		        ON DS.CODE = AD.STATE
		      LEFT JOIN DEV_PAS.T_DISTRICT DC
		        ON DC.CODE = AD.CITY
		      LEFT JOIN DEV_PAS.T_DISTRICT DD
		        ON DD.CODE = AD.DISTRICT
		     WHERE 1 = 1
		     ]]>
		        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CB.POLICY_CODE= #{policy_code} ]]></if>
		        <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND CB.CUSTOMER_ID = #{customer_id} ]]></if>
      </select>
    
    
	
	
	
	
	<!-- 根据保单号查询受益人信息 -->
	<select id="QRY_findBeneCustomerInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[
         SELECT DISTINCT CB.POLICY_CODE,
           CB.CUSTOMER_ID,
           CB.SHARE_RATE,
           CB.SHARE_ORDER,
           CB.DESIGNATION,
           CB.COMPANY_ID,/* 信托公司id */
           TC.COMPANY_NAME,
           TC.COMPANY_ORAN_CODE,
           NVL(C.CUSTOMER_NAME,CB.CUSTOMER_NAME) AS CUSTOMER_NAME,
           NVL(C.CUSTOMER_BIRTHDAY,CB.CUSTOMER_BIRTHDAY) AS CUSTOMER_BIRTHDAY,
           NVL(C.CUSTOMER_GENDER,CB.CUSTOMER_GENDER) AS CUSTOMER_GENDER,
           NVL(C.CUSTOMER_CERT_TYPE,CB.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE,
           NVL(C.CUSTOMER_CERTI_CODE,CB.CUSTOMER_CERTI_CODE) AS CUSTOMER_CERTI_CODE,
           CB.INSURED_ID,
           CBP.BUSI_PRD_ID,
           CBP.BUSI_PROD_CODE,
           CBP.VALIDATE_DATE,
           BP.PRODUCT_NAME_SYS,
           'benefit' AS CUSTOMER_ROLE,
           (Select ba.BANK_ACCOUNT from dev_pas.t_bank_account ba  where ba.customer_id=CB.Customer_Id and rownum=1 ) BANK_ACCOUNT,
          (Select ba.acco_name from dev_pas.t_bank_account ba  where ba.customer_id=CB.Customer_Id and rownum=1 ) ACCO_NAME,
          (Select db.bank_name from dev_pas.t_bank_account ba inner JOIN DEV_PAS.T_BANK DB ON DB.BANK_CODE = ba.BANK_CODE where ba.customer_id=CB.Customer_Id and rownum=1 ) BANK_NAME,
          CB.BENE_TYPE,
          C.CUSTOMER_LEVEL,
          AD.ADDRESS,
          AD.STATE,
	      AD.CITY,
	      AD.DISTRICT,
	      DS.NAME || DC.NAME || DD.NAME || AD.ADDRESS AS ADDRESS_FULL,
       	  AD.MOBILE_TEL,
       	  CASE 
       	   WHEN CBP.WAIVER = 1 AND CBP.BUSI_PROD_CODE IN (select tbp.product_code_sys from dev_pas.t_business_product tbp where tbp.waiver_customer_role='01') THEN 
            (SELECT CM.CUSTOMER_NAME
               FROM DEV_PAS.T_POLICY_HOLDER H, 
                    DEV_PAS.T_CUSTOMER CM
              WHERE H.CUSTOMER_ID = CM.CUSTOMER_ID
                AND H.POLICY_ID = CBP.POLICY_ID
                AND ROWNUM = 1 )
           WHEN CBP.WAIVER = 1 THEN
            (SELECT TC.CUSTOMER_NAME
               FROM DEV_PAS.T_INSURED_LIST    TIL,
                    DEV_PAS.T_BENEFIT_INSURED TBI,
                    DEV_PAS.T_CUSTOMER        TC
              WHERE TIL.POLICY_CODE = TBI.POLICY_CODE
                AND TIL.LIST_ID = TBI.INSURED_ID
                AND TC.CUSTOMER_ID = TIL.CUSTOMER_ID
                AND TBI.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
                AND TBI.ORDER_ID = 1)
           ELSE
            (SELECT CU.CUSTOMER_NAME
               FROM DEV_PAS.T_INSURED_LIST IL, DEV_PAS.T_CUSTOMER CU
              WHERE CB.INSURED_ID = IL.LIST_ID
                AND IL.CUSTOMER_ID = CU.CUSTOMER_ID
                AND IL.POLICY_CODE = CB.POLICY_CODE
                AND ROWNUM = 1)
         END AS INC_CUSTOMER_NAME
      FROM
      DEV_PAS.T_CONTRACT_BENE CB
      LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
        ON CBP.BUSI_ITEM_ID = CB.BUSI_ITEM_ID
     LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
        ON BP.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
       LEFT JOIN DEV_PAS.T_CUSTOMER C
        ON C.CUSTOMER_ID = CB.CUSTOMER_ID
     LEFT JOIN DEV_PAS.T_ADDRESS AD
    	ON CB.ADDRESS_ID = AD.ADDRESS_ID
    LEFT JOIN DEV_PAS.T_DISTRICT DS
	    ON DS.CODE = AD.STATE
	LEFT JOIN DEV_PAS.T_DISTRICT DC
	    ON DC.CODE = AD.CITY
	LEFT JOIN DEV_PAS.T_DISTRICT DD
	    ON DD.CODE = AD.DISTRICT
	LEFT JOIN DEV_PAS.T_TRUST_COMPANY TC
        ON  TC.COMPANY_ID = CB.COMPANY_ID    
	    
     WHERE 1 = 1
        ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CB.POLICY_CODE= #{policy_code} ]]></if>
        <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND CB.CUSTOMER_ID = #{customer_id} ]]></if>
	</select>
	<!-- 查询受益人信息 -->
	<select id="findBeneInfoByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
       SELECT CB.SHARE_RATE, C.CUSTOMER_NAME
  FROM DEV_PAS.T_CONTRACT_BENE CB
 INNER JOIN DEV_PAS.T_CUSTOMER C
    ON CB.CUSTOMER_ID = C.CUSTOMER_ID
        WHERE 1=1]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CB.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CB.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!-- 获取客户作为投保人的保单信息 -->
	<select id="PA_findHolderPolicyInfos" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,TCM.POLICY_CODE,TCM.APPLY_CODE,TCM.LIABILITY_STATE,TPH.CUSTOMER_ID,
       			TCA.AGENT_NAME,TCM.SERVICE_BANK_BRANCH,TPA.ACCOUNT
       			  FROM DEV_PAS.T_CONTRACT_MASTER TCM,DEV_PAS.T_POLICY_HOLDER TPH,DEV_PAS.T_PAYER_ACCOUNT TPA , DEV_PAS.T_contract_agent TCA
       			  	WHERE 1 = 1  
       			  		AND TCA.POLICY_ID = TCM.POLICY_ID
						AND TPH.POLICY_ID = TCM.POLICY_ID
                  		AND TPA.POLICY_ID = TCM.POLICY_ID]]>
		<include refid="PA_findHolderPolicyInfosCondition" />
		<![CDATA[ ORDER BY TCM.POLICY_ID ]]>
	</select>

	<!-- 获取客户作为被保人的保单信息 -->
	<select id="PA_findInsuredPolicyInfos" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,TCM.POLICY_CODE,TCM.APPLY_CODE,TCM.LIABILITY_STATE,TIL.CUSTOMER_ID,
       				TCA.AGENT_NAME,TCM.SERVICE_BANK_BRANCH,TPA.ACCOUNT
       			   FROM DEV_PAS.T_CONTRACT_MASTER TCM,DEV_PAS.T_INSURED_LIST TIL,DEV_PAS.T_PAYER_ACCOUNT TPA , DEV_PAS.T_contract_agent TCA
       			  	WHERE 1 = 1  
       			  		AND TCA.POLICY_ID = TCM.POLICY_ID
						AND TIL.POLICY_ID = TCM.POLICY_ID
                  		AND TPA.POLICY_ID = TCM.POLICY_ID]]>
		<include refid="PA_findHolderPolicyInfosCondition" />
		<![CDATA[ ORDER BY TCM.POLICY_ID ]]>
	</select>

	<!-- 获取保单信息 -->
	<select id="PA_findAllCommonQueryComp" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_ID,TCM.POLICY_CODE,TCM.APPLY_CODE,TCM.LIABILITY_STATE,
       			TCA.AGENT_NAME,TCM.SERVICE_BANK_BRANCH,TPA.ACCOUNT
       			  FROM DEV_PAS.T_CONTRACT_MASTER TCM,DEV_PAS.T_PAYER_ACCOUNT TPA , DEV_PAS.T_contract_agent TCA
       			  	WHERE 1 = 1  
       			  		AND TCA.POLICY_ID = TCM.POLICY_ID
                  		AND TPA.POLICY_ID = TCM.POLICY_ID 
                  		]]>
		<include refid="PA_findHolderPolicyInfosCondition" />
		<![CDATA[ ORDER BY TCM.POLICY_ID ]]>
	</select>
   <!--  根据客户编号查询保单信息SQL -->
    <sql id="SQL_findPolicyInfoByCustomerId">
       <![CDATA[
        SELECT 
           (CASE WHEN ( MINFO.DOUBLE_MAINRISK_FLAG ='1' AND MINFO.RELATION_POLICY_CODE IS NULL AND NCM.RELATION_POLICY_CODE IS NULL) THEN 
             (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TNCM WHERE TNCM.RELATION_POLICY_CODE= MINFO.POLICY_CODE
             	AND TNCM.DOUBLE_MAINRISK_FLAG = '1'
             )
                 ELSE
                   MINFO.APPLY_CODE1
               END 
               ) AS APPLY_CODE,
            MINFO.*,
            (SELECT MIN(EX.PAY_DUE_DATE) FROM DEV_PAS.T_CONTRACT_EXTEND EX 
                    WHERE MINFO.BUSI_ITEM_ID=EX.BUSI_ITEM_ID AND MINFO.POLICY_ID=EX.POLICY_ID               
            ) AS PAY_DUE_DATE,
            NBP.BUSI_PROD_CODE,
            CASE
				WHEN (NBP.RISK_SCORE <
                (SELECT T.RISK_SCORE_MIN_SECONDARY
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') or
                NBP.RISK_SCORE <
                (SELECT TC.RISK_SCORE_MAX
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG TC
                  WHERE 1 = 1
                    AND TC.IS_VALID = '1')) THEN
            CONCAT('低|', NBP.RISK_SCORE)
            WHEN (NBP.RISK_SCORE >=
                (SELECT T.RISK_SCORE_MIN_SECONDARY
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') or
                NBP.RISK_SCORE >=
                (SELECT T.RISK_SCORE_MAX
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') ) and
                    (NBP.RISK_SCORE <
                (SELECT T.RISK_SCORE_MIN_HIGH
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') or
                NBP.RISK_SCORE <
                (SELECT T.RISK_SCORE_MAX_SECONDARY
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') )
                    THEN
            CONCAT('中|', NBP.RISK_SCORE)
           WHEN NBP.RISK_SCORE >=
                (SELECT T.RISK_SCORE_MIN_HIGH
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') or
                NBP.RISK_SCORE >=
                (SELECT T.RISK_SCORE_MAX_SECONDARY
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') THEN
            CONCAT('高|', NBP.RISK_SCORE)
            else ''
              end RISK_SCORE,
            BP.PRODUCT_NAME_SYS ,
            (SELECT 
            (
            CASE WHEN COUNT(*)>0  THEN '投保人,' ELSE '' END
            ) FROM DEV_PAS.T_POLICY_HOLDER PH 
            WHERE PH.CUSTOMER_ID=#{customer_id} AND PH.POLICY_ID=MINFO.POLICY_ID) AS HOLDER,
            (SELECT CASE WHEN COUNT(*)>0  THEN '被保险人,' ELSE '' END FROM DEV_PAS.T_INSURED_LIST NIL 
            WHERE NIL.CUSTOMER_ID=#{customer_id} AND NIL.POLICY_ID=MINFO.POLICY_ID) AS INSURED,
            (SELECT CASE WHEN COUNT(*)>0  THEN '受益人' ELSE '' END FROM DEV_PAS.T_CONTRACT_BENE CB 
            WHERE CB.CUSTOMER_ID=#{customer_id} AND CB.POLICY_ID=MINFO.POLICY_ID) AS BENE,
            CA.AGENT_CODE
        FROM(
            SELECT
            AC.POLICY_ID,
            CM.DOUBLE_MAINRISK_FLAG,
            CM.APPLY_CODE AS APPLY_CODE1,
            CM.RELATION_POLICY_CODE,
            CM.POLICY_CODE,
            CM.POLICY_TYPE,
            CM.LIABILITY_STATE,
            CM.ORGAN_CODE ,
            CM.VALIDATE_DATE,
            CM.EXPIRY_DATE,
            CM.Subinput_Type,
            (SELECT 
            NBP.BUSI_ITEM_ID
             FROM
            DEV_PAS.T_CONTRACT_BUSI_PROD NBP
            WHERE NBP.POLICY_ID=CM.POLICY_ID AND NBP.MASTER_BUSI_ITEM_ID IS NULL AND ROWNUM=1) AS BUSI_ITEM_ID,
            (SELECT TO_CHAR(WMSYS.WM_CONCAT(DISTINCT CU.CUSTOMER_NAME)) FROM DEV_PAS.T_POLICY_HOLDER NPH 
            INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NPH.CUSTOMER_ID
            WHERE NPH.POLICY_ID=CM.POLICY_ID ) AS HOLDER_NAME,
            (SELECT 
            TO_CHAR(WMSYS.WM_CONCAT(DISTINCT CU.CUSTOMER_NAME))
            FROM 
            DEV_PAS.T_BENEFIT_INSURED NBI
            INNER JOIN DEV_PAS.T_INSURED_LIST NIL 
            ON NBI.INSURED_ID=NIL.LIST_ID
            INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NIL.CUSTOMER_ID
             WHERE NIL.POLICY_ID=CM.POLICY_ID
            ) AS INSURED_NAME,
            (SELECT 
            TO_CHAR(WMSYS.WM_CONCAT(DISTINCT CU.CUSTOMER_NAME))
            FROM 
            DEV_PAS.T_CONTRACT_BENE NCB
            INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NCB.CUSTOMER_ID
             WHERE NCB.POLICY_ID=CM.POLICY_ID 
            ) AS BENE_NAME,
            PA.PAY_NEXT,
            CM.SUBMIT_CHANNEL --50694功能增加
            FROM 
            (
            SELECT 
            NPH.APPLY_CODE,NPH.POLICY_ID
            FROM 
            DEV_PAS.T_POLICY_HOLDER NPH 
            ]]>
            <if test=" holder_name != null and holder_name != ''  ">
            <![CDATA[
                INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NPH.CUSTOMER_ID WHERE CU.CUSTOMER_NAME=#{holder_name}
            ]]>
            </if>
            <if test=" holder_name == null or holder_name == ''  ">
             <![CDATA[
                WHERE 1=1
               ]]>
            </if>
            <![CDATA[
            AND NPH.CUSTOMER_ID=#{customer_id}
            UNION 
            SELECT 
            NIL.APPLY_CODE,NIL.POLICY_ID
            FROM 
            DEV_PAS.T_BENEFIT_INSURED NBI
            INNER JOIN DEV_PAS.T_INSURED_LIST NIL 
            ON NBI.INSURED_ID=NIL.LIST_ID 
            ]]>
            <if test=" insured_name != null and insured_name != ''  ">
                <![CDATA[
                INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NIL.CUSTOMER_ID WHERE CU.CUSTOMER_NAME=#{insured_name}
                 ]]>
            </if>
            <if test=" insured_name == null or insured_name == ''  ">
             <![CDATA[
                WHERE 1=1
               ]]>
            </if>
            <![CDATA[
            AND NIL.CUSTOMER_ID=#{customer_id}) AC
            INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM ON 
            CM.POLICY_ID=AC.POLICY_ID
            LEFT JOIN DEV_PAS.T_PAYER_ACCOUNT PA ON 
            PA.POLICY_ID=CM.POLICY_ID
            ) MINFO
        INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD NBP ON MINFO.POLICY_ID=NBP.POLICY_ID AND MINFO.BUSI_ITEM_ID=NBP.BUSI_ITEM_ID
        INNER JOIN  DEV_PDS.T_BUSINESS_PRODUCT BP ON NBP.BUSI_PROD_CODE=BP.PRODUCT_CODE_SYS
        LEFT JOIN DEV_PAS.T_contract_agent CA ON MINFO.POLICY_ID=CA.POLICY_ID AND CA.IS_CURRENT_AGENT = '1'
        LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON MINFO.POLICY_CODE = NCM.POLICY_CODE
        WHERE 1=1
        ]]>
        <if test=" agent_code != null and agent_code != ''  "><![CDATA[AND CA.AGENT_CODE = #{agent_code}]]></if>
        <if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[AND MINFO.ORGAN_CODE = #{service_bank_branch}]]></if>
        <if test=" pay_next != null and pay_next != ''  "><![CDATA[AND MINFO.PAY_NEXT = #{pay_next}]]></if>
        <if test=" liability_state != null">
        <![CDATA[AND MINFO.LIABILITY_STATE = #{liability_state}]]>
        </if>
        <if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[AND NBP.BUSI_PROD_CODE =#{busi_prod_code}]]></if>
        <if test=" validate_date != null and validate_date != ''  "><![CDATA[AND MINFO.VALIDATE_DATE =#{validate_date}]]></if>
    </sql>
   
	<!-- 根据客户编号查询保单信息的总数 -->
	<select id="QRY_findPolicyInfoByCustomerIdTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM 
        (
        ]]>
        <include refid="SQL_findPolicyInfoByCustomerId" />
        <![CDATA[
         ) B
		]]>
	</select>
	<!-- 根据客户编号查询保单相关信息 -->
	<select id="QRY_findPolicyInfoByCustomerId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
        SELECT B.RN ,
          B.APPLY_CODE,
          B.POLICY_CODE,
          TRIM(',' from CONCAT(CONCAT(HOLDER,INSURED),BENE)) AS POLICY_ROLE,
          B.LIABILITY_STATE,
          B.PRODUCT_NAME_SYS,
          B.VALIDATE_DATE,
          B.EXPIRY_DATE,
          B.PAY_DUE_DATE,
          B.HOLDER_NAME,
          B.INSURED_NAME,
          B.BENE_NAME,
          B.Subinput_Type,
          B.ORGAN_CODE,
          B.AGENT_CODE，
          B.RISK_SCORE
        FROM
        (
            SELECT ROWNUM AS RN,C.* FROM (
            ]]>
            <include refid="SQL_findPolicyInfoByCustomerId" />
        <![CDATA[
            AND (MINFO.liABILITY_STATE = 0 OR MINFO.liABILITY_STATE =1 OR MINFO.liABILITY_STATE = 3 OR MINFO.liABILITY_STATE = 4)
            ORDER BY MINFO.VALIDATE_DATE  ASC) C
        )B WHERE B.RN > #{GREATER_NUM} AND B.RN <= #{LESS_NUM}
        
        ]]>
	</select>
    
    
    <!-- 根据客户编号查询所有 保单相关信息 -->
    <select id="QRY_findAllPolicyInfoByCustomerId" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT B.RN ,
          B.APPLY_CODE,
          B.POLICY_CODE,
          TRIM(',' from CONCAT(CONCAT(HOLDER,INSURED),BENE)) AS POLICY_ROLE,
          B.LIABILITY_STATE,
          B.PRODUCT_NAME_SYS,
          B.VALIDATE_DATE,
          B.EXPIRY_DATE,
          B.PAY_DUE_DATE,
          B.HOLDER_NAME,
          B.INSURED_NAME,
          B.BENE_NAME,
          B.ORGAN_CODE,
          B.AGENT_CODE,
          B.POLICY_TYPE,
          (SELECT
                  T.PREM_FREQ AS PREM_FREQ
             FROM DEV_PAS.T_CONTRACT_PRODUCT T
             LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
               ON T.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
            WHERE T.POLICY_CODE=B.POLICY_CODE AND T.POLICY_CODE=TCBP.POLICY_CODE AND TCBP.MASTER_BUSI_ITEM_ID is null AND ROWNUM=1) AS PREM_FREQ,
          (SELECT
                  TCBP.PAIDUP_DATE
             FROM DEV_PAS.T_CONTRACT_PRODUCT T
             LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
               ON T.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
            WHERE T.POLICY_CODE=B.POLICY_CODE AND T.POLICY_CODE=TCBP.POLICY_CODE AND TCBP.MASTER_BUSI_ITEM_ID is null AND ROWNUM=1) AS PAIDUP_DATE 
        FROM
        (
            SELECT ROWNUM AS RN,C.* FROM (
            ]]>
            <include refid="SQL_findPolicyInfoByCustomerId" />
        <![CDATA[
             ORDER BY MINFO.VALIDATE_DATE  DESC) C
        )B 
        ]]>
    </select>
    
	<!-- 根据代理人编号查询保单信息的总数 -->
	<select id="QRY_findPolicyInfoByagentCodeTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT  COUNT(1) FROM
(
         
SELECT  ROWNUM RN,
CM.APPLY_CODE,
CM.POLICY_CODE ,
CM.LIABILITY_STATE,
CBP.BUSI_PRD_ID,
CM.ORGAN_CODE,
CM.VALIDATE_DATE,
CM.EXPIRY_DATE,
C.CUSTOMER_NAME  AS INSURED_NAME,
CU.CUSTOMER_NAME AS HOLDER_NAME,
CM.SUBINPUT_TYPE,
CA.AGENT_CODE,
CA.AGENT_NAME
FROM DEV_PAS.T_CONTRACT_MASTER CM 
INNER JOIN 
DEV_PAS.T_contract_agent CA 
ON
CM.POLICY_CODE = CA.POLICY_CODE
INNER JOIN DEV_PAS.T_PAYER_ACCOUNT PA
ON CM.POLICY_ID = PA.POLICY_ID
INNER JOIN DEV_PAS.T_INSURED_LIST IL
ON CM.POLICY_ID = IL.POLICY_ID
INNER JOIN DEV_PAS.T_CUSTOMER C
ON C.CUSTOMER_ID = IL.CUSTOMER_ID
INNER JOIN DEV_PAS.T_POLICY_HOLDER PH
ON CM.POLICY_ID = PH.POLICY_ID
INNER JOIN DEV_PAS.T_CUSTOMER CU 
ON PH.CUSTOMER_ID = CU.CUSTOMER_ID
INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
ON CM.POLICY_CODE = CBP.POLICY_CODE
INNER JOIN DEV_PAS.T_contract_agent CA
ON CM.POLICY_CODE = CA.POLICY_CODE
WHERE 1=1]]>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[AND CA.AGENT_CODE = #{agent_code}]]></if>
		<if test=" pay_next != null and pay_next != ''  "><![CDATA[AND PA.PAY_NEXT = #{pay_next}]]></if>
		<if test=" liability_state != null and liability_state != ''  "><![CDATA[AND CM.LIABILITY_STATE = #{liability_state}]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[AND C.CUSTOMER_NAME =#{insured_name}]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[AND CU.CUSTOMER_NAME =#{holder_name}]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[AND CBP.BUSI_PROD_CODE =#{busi_prod_code}]]></if>
		<if test=" validate_date != null and validate_date != ''  "><![CDATA[AND CM.VALIDATE_DATE =#{validate_date}]]></if>
        <![CDATA[ ) B]]>
	</select>
	<!-- 根据代理人编号查询保单相关信息 -->
	<select id="QRY_findPolicyInfoByagentCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT 
B.APPLY_CODE,
B.POLICY_CODE ,
B.LIABILITY_STATE,
B.BUSI_PRD_ID,
B.ORGAN_CODE,
B.VALIDATE_DATE,
B.EXPIRY_DATE,
B.INSURED_NAME,
B.HOLDER_NAME,
B.SUBINPUT_TYPE,
B.AGENT_CODE,
B.AGENT_NAME,
B.CUSTOMER_VIP
 FROM (        
SELECT  ROWNUM RN,
(CASE WHEN ( CM.DOUBLE_MAINRISK_FLAG ='1' AND CM.RELATION_POLICY_CODE IS NULL AND NCM.RELATION_POLICY_CODE IS NULL) THEN 
  (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER NCM WHERE NCM.RELATION_POLICY_CODE= CM.POLICY_CODE
  	AND NCM.DOUBLE_MAINRISK_FLAG = '1'
  )
  	ELSE
    CM.APPLY_CODE
	END 
) AS AS APPLY_CODE,
CM.POLICY_CODE ,
CM.LIABILITY_STATE,
CBP.BUSI_PRD_ID,
CM.ORGAN_CODE,
CM.VALIDATE_DATE,
CM.EXPIRY_DATE,
C.CUSTOMER_NAME  AS INSURED_NAME,
CU.CUSTOMER_NAME AS HOLDER_NAME,
CM.SUBINPUT_TYPE,
CA.AGENT_CODE,
CA.AGENT_NAME,
CU.CUSTOMER_VIP
FROM DEV_PAS.T_CONTRACT_MASTER CM 
INNER JOIN 
DEV_PAS.T_contract_agent CA 
ON
CM.POLICY_CODE = CA.POLICY_CODE
INNER JOIN DEV_PAS.T_PAYER_ACCOUNT PA
ON CM.POLICY_ID = PA.POLICY_ID
INNER JOIN DEV_PAS.T_INSURED_LIST IL
ON CM.POLICY_ID = IL.POLICY_ID
INNER JOIN DEV_PAS.T_CUSTOMER C
ON C.CUSTOMER_ID = IL.CUSTOMER_ID
INNER JOIN DEV_PAS.T_POLICY_HOLDER PH
ON CM.POLICY_ID = PH.POLICY_ID
INNER JOIN DEV_PAS.T_CUSTOMER CU 
ON PH.CUSTOMER_ID = CU.CUSTOMER_ID
INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
ON CM.POLICY_CODE = CBP.POLICY_CODE
INNER JOIN DEV_PAS.T_contract_agent CA
ON CM.POLICY_CODE = CA.POLICY_CODE
LEFT JOIN DEV_NB.T_CONTRACT_MASTER NCM
ON CM.POLICY_CODE = NCM.POLICY_CODE
WHERE 1=1]]>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[AND CA.AGENT_CODE = #{agent_code}]]></if>
		<if test=" pay_next != null and pay_next != ''  "><![CDATA[AND PA.PAY_NEXT = #{pay_next}]]></if>
		<if test=" liability_state != null and liability_state != ''  "><![CDATA[AND CM.LIABILITY_STATE = #{liability_state}]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[AND C.CUSTOMER_NAME =#{insured_name}]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[AND CU.CUSTOMER_NAME =#{holder_name}]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[AND CBP.BUSI_PROD_CODE =#{busi_prod_code}]]></if>
		<if test=" validate_date != null and validate_date != ''  "><![CDATA[AND CM.VALIDATE_DATE =#{validate_date}]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[AND CM.SERVICE_BANK_BRANCH =#{service_bank_branch}]]></if>
        
 <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
<![CDATA[ORDER BY B.VALIDATE_DATE  DESC]]>
	</select>

	<!--查询险种责任组列表信息 -->
    <select id="PA_findBusiItemInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
   SELECT 
   B.*,nvl2(BENEFIT_LEVEL1,'计划' || BENEFIT_LEVEL1,'') AS BENEFIT_LEVEL,
   	(SELECT (CASE
                 WHEN B.PAY_DUE_DATE >= EP.START_DATE AND
                      B.PAY_DUE_DATE <= EP.END_DATE THEN
                  EP.EXTRA_PREM
                 ELSE
                  EP.EXTRA_PREM
               END) AS EXTRA_PREM
          FROM DEV_PAS.T_EXTRA_PREM EP
         WHERE EP.INSERT_TIMESTAMP = B.PREM_INSERT_TIME AND EP.POLICY_ID = B.POLICY_ID
                   AND  B.BUSI_ITEM_ID = EP.BUSI_ITEM_ID
                   AND  B.ITEM_ID =  EP.ITEM_ID
                   AND EP.EXTRA_TYPE = 1) AS EXTRA_PREM,
       (SELECT (CASE
                 WHEN B.PAY_DUE_DATE >= EP.START_DATE AND
                      B.PAY_DUE_DATE <= EP.END_DATE THEN
                  EP.EXTRA_PREM
                 ELSE
                  EP.EXTRA_PREM
               END) AS EXTRA_PREM
          FROM DEV_PAS.T_EXTRA_PREM EP
         WHERE EP.INSERT_TIMESTAMP = B.PREM_INSERT_TIME1
         AND EP.POLICY_ID = B.POLICY_ID
                   AND  B.BUSI_ITEM_ID = EP.BUSI_ITEM_ID
                   AND  B.ITEM_ID =  EP.ITEM_ID
                   AND EP.EXTRA_TYPE = 2
         ) AS EXTRA_PREM_JOB
    FROM(     
		   SELECT 
		   CP.ITEM_ID,
		   CP.PRODUCT_CODE,
		   CP.POLICY_ID,
		   CP.POLICY_CODE,
		   CP.APPLY_CODE,
		   CP.COVERAGE_YEAR,
		   CP.AMOUNT,
		   CP.UNIT,
		   CP.PREM_FREQ, /*交费方式  #45810 新核心业务系统青岛上线优化需求——清单、查询、承保功能优化（1）*/
		   (case 
	          when CP.CHARGE_PERIOD = '1' THEN
	            '一次交清'
	             when CP.CHARGE_PERIOD = '2' THEN
	            CP.CHARGE_YEAR||'年'
	             when CP.CHARGE_PERIOD = '3' THEN
	            '交至'||CP.CHARGE_YEAR||'岁'
	            when CP.CHARGE_PERIOD = '4' THEN
	            '终身交费'
	            when CP.CHARGE_PERIOD = '5' THEN
	            '--'
	            when CP.CHARGE_PERIOD = '6' THEN
	            CP.CHARGE_YEAR||'月'
	            when CP.CHARGE_PERIOD = '7' THEN
	            CP.CHARGE_YEAR||'天'
	            when CP.CHARGE_PERIOD = '8' THEN
            CP.CHARGE_YEAR||'年'
            when CP.CHARGE_PERIOD = '9' THEN
            TCP.CHARGE_DESC
	         ELSE
	        ''
	       end) as charge_desc,/*缴费期间*/
		   CP.STD_PREM_AF,
		   CP.LIABILITY_STATE,
		   CBP.BUSI_ITEM_ID,
		   CBP.BUSI_PROD_CODE,             /*险种代码m*/
		   CBP.MASTER_BUSI_ITEM_ID,       /*所属的主险险种IDm*/ 
		   CBP.Validate_Date,
           CBP.Maturity_Date, 
           CASE
		WHEN (CBP.RISK_SCORE <
                (SELECT T.RISK_SCORE_MIN_SECONDARY
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') or
                CBP.RISK_SCORE <
                (SELECT TC.RISK_SCORE_MAX
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG TC
                  WHERE 1 = 1
                    AND TC.IS_VALID = '1')) THEN
            CONCAT('低|', CBP.RISK_SCORE)
            WHEN (CBP.RISK_SCORE >=
                (SELECT T.RISK_SCORE_MIN_SECONDARY
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') or
                CBP.RISK_SCORE >=
                (SELECT T.RISK_SCORE_MAX
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') ) and
                    (CBP.RISK_SCORE <
                (SELECT T.RISK_SCORE_MIN_HIGH
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') or
                CBP.RISK_SCORE <
                (SELECT T.RISK_SCORE_MAX_SECONDARY
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') )
                    THEN
            CONCAT('中|', CBP.RISK_SCORE)
           WHEN CBP.RISK_SCORE >=
                (SELECT T.RISK_SCORE_MIN_HIGH
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') or
                CBP.RISK_SCORE >=
                (SELECT T.RISK_SCORE_MAX_SECONDARY
                   FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
                  WHERE 1 = 1
                    AND T.IS_VALID = '1') THEN
            CONCAT('高|', CBP.RISK_SCORE)
            else ''
              end RISK_SCORE,
		   BP.PRODUCT_NAME_SYS,
		   BP.COVER_PERIOD_TYPE,/*保障期间类型*/
		   CE.PAY_DUE_DATE,
		   (SELECT MAX(Validate_Date) FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP1 WHERE CBP1.POLICY_CODE=CBP.POLICY_CODE AND CBP1.BUSI_ITEM_ID=CBP.MASTER_BUSI_ITEM_ID) AS M_VALIDATE_DATE,/*主险生效日期*/
		   (SELECT TS.STATUS_NAME FROM DEV_PAS.T_PREM_STATUS TS WHERE TS.STATUS_CODE = CE.PREM_STATUS)  PREM_STATUS_NAME,
		   PTL.PRODUCT_NAME,
		   CP.WAIVER_START,
		   CP.WAIVER_END,
		    (case when CPO.FIELD1 ='1' then '一' when  CPO.FIELD1 ='2' then '二' when  CPO.FIELD1 ='3' then '三' when  CPO.FIELD1 ='4' then '四' when  CPO.FIELD1 ='5' then '五'
		    when  CPO.FIELD1 ='1' then '六' else '' end) AS BENEFIT_LEVEL1,
		   (CASE WHEN CBP.SUSPEND_CAUSE='01' THEN '1' else '0' END)  SUSPEND,
			(CASE WHEN CBP.SUSPEND_CAUSE='10' THEN '1' else '0' END)  PREENDFLAG,
		   (SELECT MAX(EP.INSERT_TIMESTAMP)  FROM DEV_PAS.T_EXTRA_PREM EP
                  WHERE EP.POLICY_ID = CE.POLICY_ID
                    AND EP.BUSI_ITEM_ID = CE.BUSI_ITEM_ID
                    AND EP.ITEM_ID = CE.ITEM_ID
                    AND EP.EXTRA_TYPE=1 ) AS PREM_INSERT_TIME ,/*健康加费*/
		   (SELECT MAX(EP.INSERT_TIMESTAMP)  FROM DEV_PAS.T_EXTRA_PREM EP
                  WHERE EP.POLICY_ID = CE.POLICY_ID
                    AND EP.BUSI_ITEM_ID = CE.BUSI_ITEM_ID
                    AND EP.ITEM_ID = CE.ITEM_ID
                    AND EP.EXTRA_TYPE=2) AS PREM_INSERT_TIME1,/*职业加费*/
		    (SELECT EP.EXTRA_TYPE  FROM  DEV_PAS.T_EXTRA_PREM EP WHERE EP.POLICY_ID=CE.POLICY_ID AND EP.BUSI_ITEM_ID=CE.BUSI_ITEM_ID 
		   AND EP.ITEM_ID=CE.ITEM_ID AND ROWNUM=1) AS EXTRA_TYPE,  /*加费类型m*/
		   CP.DEDUCTIBLE_FRANCHISE, /*免赔额m*/
		   CP.PAYOUT_RATE,           /*赔付比例m*/
		   (SELECT COUNT(*) FROM ( SELECT CM.POLICY_ID,CM.POLICY_CODE AS RELATION_POLICY_CODE,CM.RELATION_POLICY_CODE AS POLICY_CODE FROM DEV_PAS.T_CONTRACT_MASTER CM 
		    WHERE CM.RELATION_POLICY_CODE=#{policy_code} AND CM.POLICY_CODE IS NOT NULL
		    UNION
		    SELECT CM.POLICY_ID,CM.RELATION_POLICY_CODE,CM.POLICY_CODE FROM DEV_PAS.T_CONTRACT_MASTER CM 
		    WHERE CM.POLICY_CODE=#{policy_code} and CM.RELATION_POLICY_CODE IS NOT NULL ) B ) AS RELATION_POLICY_CODE_COUNT,  /*关联保单数量*/
	    (SELECT COUNT(1)
                FROM DEV_PAS.T_CONTRACT_MASTER   CM,
                     DEV_PAS.T_CONTRACT_RELATION TCR
               WHERE CM.POLICY_CODE = TCR.MASTER_POLICY_CODE
                 AND (TCR.MASTER_BUSI_ITEM_ID = CBP.BUSI_ITEM_ID OR
                     TCR.SUB_BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) ) AS RELATION_POLICY_CODE_COUNT1, /*关联保单数量*/
		    (CASE WHEN CBP.REINSURED='3' then '已转保' else '' end) AS REINSURED_FLAG,/*转保标识*/
			(CASE WHEN CBP.REINSURED='1' THEN '已续保' 
			  WHEN CBP.REINSURED='2' OR  CBP.REINSURED='3' 
			  THEN '已转保' ELSE '' END) AS REINSURED_TYPE/*已续保/已转保标识*/
		   FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		   LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP ON CBP.BUSI_PRD_ID=BP.BUSINESS_PRD_ID
		   LEFT JOIN　DEV_PAS.T_CONTRACT_PRODUCT CP  
		   ON CBP.BUSI_ITEM_ID=CP.BUSI_ITEM_ID
		    and  CBP.POLICY_CODE=CP.POLICY_CODE
		    LEFT JOIN DEV_PAS.T_CHARGE_PERIOD TCP
      		ON CP.CHARGE_PERIOD = TCP.CHARGE_PERIOD
		   LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT_OTHER CPO 
		   ON CP.APPLY_CODE=CPO.APPLY_CODE
		   AND CP.BUSI_ITEM_ID = CPO.BUSI_ITEM_ID AND CP.ITEM_ID=CPO.ITEM_ID/*#104_11265缺陷增加关联查询条件*/
		   LEFT JOIN DEV_PAS.T_CONTRACT_EXTEND CE ON　CE.ITEM_ID=CP.ITEM_ID 
		   and   CE.POLICY_CODE=CP.POLICY_CODE
		   LEFT JOIN  DEV_PDS.T_PRODUCT_LIFE PTL ON　CP.PRODUCT_ID=PTL.PRODUCT_ID
		   WHERE 1=1 
		   
   ]]>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[AND CBP.POLICY_CODE=#{policy_code}]]></if>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[AND CBP.APPLY_CODE=#{apply_code}]]></if>
    ORDER BY CBP.ORDER_ID,CBP.MASTER_BUSI_ITEM_ID DESC,CBP.BUSI_ITEM_ID DESC   /*按照主险ID,险种id排序m*/
       ) B 
    </select>
     <!-- 查询关联保单信息  -->
    <select id="PA_findRelationPolicyInfos" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
    SELECT B.*
  FROM (SELECT 
          (SELECT BP.PRODUCT_NAME_SYS
                  FROM DEV_PDS.T_BUSINESS_PRODUCT BP
                  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CC
                    ON CC.Busi_Prod_Code = BP.Product_Code_Sys
                 WHERE CC.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID1) PRODUCT_NAME_SYS1,
               TCBP.POLICY_ID,
               TCBP.POLICY_CODE,
               (CASE
                 WHEN (TCBP.DOUBLE_MAINRISK_FLAG = '1' AND
                      TCBP.RELATION_POLICY_CODE IS NULL AND
                      NCM.RELATION_POLICY_CODE IS NULL) THEN
                  (SELECT APPLY_CODE
                     FROM DEV_NB.T_NB_CONTRACT_MASTER TM
                    WHERE TM.RELATION_POLICY_CODE = TCBP.POLICY_CODE
                      AND TM.DOUBLE_MAINRISK_FLAG = '1')
                 ELSE
                  TCBP.APPLY_CODE
               END) AS APPLY_CODE,
               TCBP.Apply_Date, /*投保日期 */
               TCBP.Validate_Date, /*保单生效日期 */
               TCBP.Liability_State, /*保单效力状态 */
               TCBP.BUSI_PROD_CODE, /*险种代码m*/
               BP.PRODUCT_NAME_SYS, /*险种名称 */
               NVL(TCBP.DOUBLE_MAINRISK_FLAG, 0) AS DOUBLE_MAINRISK_FLAG, /*双主险标识*/
               TCBP.POLICY_RELATION_TYPE  AS RELATION_TYPE /*关联类型 */
          FROM (SELECT CM.POLICY_CODE,
                       CM.POLICY_ID,
                       CM.APPLY_CODE,
                       CM.Apply_Date,
                       CM.VALIDATE_DATE,
                       CM.LIABILITY_STATE,
                       CM.RELATION_POLICY_CODE,
                       CBP.BUSI_ITEM_ID,
                       TEMP.BUSI_ITEM_ID1,
                       CBP.BUSI_PROD_CODE,
                       TEMP.POLICY_RELATION_TYPE,
                       CM.DOUBLE_MAINRISK_FLAG
                  FROM (SELECT CM.POLICY_CODE,
                               TCBP.BUSI_ITEM_ID,
                               (SELECT A.BUSI_ITEM_ID
                                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD A
                                 WHERE A.POLICY_CODE = #{policy_code}) BUSI_ITEM_ID1,
                                 
                               (SELECT policy_RELATION_NAME
                      FROM DEV_PAS.T_policy_RELATION_type
                          WHERE policy_RELATION_TYPE =  TO_CHAR(CM.POLICY_RELATION_TYPE)) AS POLICY_RELATION_TYPE
                          
                          FROM DEV_PAS.T_CONTRACT_MASTER    CM,
                               DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
                         WHERE CM.POLICY_CODE = TCBP.POLICY_CODE
                           AND CM.RELATION_POLICY_CODE = #{policy_code}
                           AND CM.POLICY_CODE IS NOT NULL
                        UNION
                        SELECT CM.RELATION_POLICY_CODE POLICY_CODE,
                               TCBP.BUSI_ITEM_ID,
                               A.Busi_Item_Id  BUSI_ITEM_ID1,
                               (SELECT policy_RELATION_NAME
                                  FROM DEV_PAS.T_policy_RELATION_type
                                 WHERE policy_RELATION_TYPE =
                                       TO_CHAR(CM.POLICY_RELATION_TYPE)) AS POLICY_RELATION_TYPE
                          FROM DEV_PAS.T_CONTRACT_MASTER    CM,
                               DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
															  DEV_PAS.T_CONTRACT_BUSI_PROD A
                         WHERE CM.RELATION_POLICY_CODE = TCBP.POLICY_CODE
												   AND A.POLICY_CODE = CM.POLICY_CODE
                           AND CM.POLICY_CODE = #{policy_code}
                           and CM.RELATION_POLICY_CODE IS NOT NULL
                        UNION 
                        SELECT TCR.SUB_POLICY_CODE  POLICY_CODE,
                               TCR.SUB_BUSI_ITEM_ID BUSI_ITEM_ID,
                               TCR.MASTER_BUSI_ITEM_ID BUSI_ITEM_ID1,
                               (SELECT contract_RELATION_NAME
                      FROM DEV_PAS.T_CONTRACT_RELATION_type
                          WHERE contract_RELATION_TYPE =  TCR.RELATION_TYPE)     AS POLICY_RELATION_TYPE
                          FROM DEV_PAS.T_CONTRACT_MASTER    CM,
                               DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                               DEV_PAS.T_CONTRACT_RELATION  TCR
                         WHERE (CM.POLICY_CODE = TCR.MASTER_POLICY_CODE OR
                               CM.POLICY_CODE = TCR.SUB_POLICY_CODE)
                           AND CM.POLICY_CODE = TCBP.POLICY_CODE
                           AND (TCBP.BUSI_ITEM_ID = TCR.MASTER_BUSI_ITEM_ID OR
                               TCBP.BUSI_ITEM_ID = TCR.SUB_BUSI_ITEM_ID)
                           AND CM.POLICY_CODE = #{policy_code}
	                        ]]>
	                        	<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ and TCR.MASTER_BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	                       	<![CDATA[
	                       	union
                        SELECT TCR.MASTER_POLICY_CODE  POLICY_CODE,
                               TCR.MASTER_BUSI_ITEM_ID BUSI_ITEM_ID,
                                TCR.SUB_BUSI_ITEM_ID    BUSI_ITEM_ID1,
                               (SELECT contract_RELATION_NAME
                      FROM DEV_PAS.T_CONTRACT_RELATION_type
                          WHERE contract_RELATION_TYPE =  TCR.RELATION_TYPE)       AS POLICY_RELATION_TYPE
                          FROM DEV_PAS.T_CONTRACT_MASTER    CM,
                               DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                               DEV_PAS.T_CONTRACT_RELATION  TCR
                         WHERE (CM.POLICY_CODE = TCR.MASTER_POLICY_CODE OR
                               CM.POLICY_CODE = TCR.SUB_POLICY_CODE)
                           AND CM.POLICY_CODE = TCBP.POLICY_CODE
                           AND (TCBP.BUSI_ITEM_ID = TCR.MASTER_BUSI_ITEM_ID OR
                               TCBP.BUSI_ITEM_ID = TCR.SUB_BUSI_ITEM_ID)
                           AND CM.POLICY_CODE = #{policy_code}
                        ]]>
                        	<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[  and TCR.SUB_BUSI_ITEM_ID = #{busi_item_id} ]]></if>
                        <![CDATA[
                        ) TEMP
                 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
                    ON TEMP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
                  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
                    ON CM.POLICY_CODE = CBP.POLICY_CODE) TCBP
          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
            ON TCBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
          LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM
            ON TCBP.POLICY_CODE = NCM.POLICY_CODE
         WHERE 1 = 1
         ORDER BY TCBP.Apply_Date, TCBP.BUSI_PROD_CODE /*按照主险ID,险种id排序m*/
        ) B
 ]]>
    </select>
    <!--新契约履历自核不通过原因  -->
    <select id="UW_queryNotAutoProcess" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
          SELECT UM.UW_SUBMIT_TIME, /*核保日期*/ RR.RULE_MAG /*核保意见*/
			  FROM DEV_UW.T_UW_MASTER UM
			  LEFT JOIN DEV_UW.T_UW_AUTO UA
			    ON UM.UW_ID = UA.UW_ID
			  LEFT JOIN DEV_UW.T_RULE_RESULT RR
			    ON UA.AUTO_ID = RR.AUTO_ID
			 WHERE UM.BIZ_CODE = #{apply_code}
          ]]>
    </select>
    
	<select id="QRY_queryCSProductInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(8) FROM DEV_PAS.T_POLICY_ACCOUNT PA
INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM ON CM.POLICY_ID=PA.POLICY_ID
INNER JOIN (SELECT TB.PRODUCT_NAME_SYS AS BUSI_PROD_CODE,TP.BUSI_ITEM_ID FROM DEV_PDS.T_BUSINESS_PRODUCT TB,DEV_PAS.T_CONTRACT_BUSI_PROD TP WHERE TP.BUSI_PROD_CODE=TB.PRODUCT_CODE_SYS) CBP ON CBP.BUSI_ITEM_ID=PA.BUSI_ITEM_ID
LEFT JOIN (SELECT TL.PRODUCT_NAME AS PRODUCT_CODE,TC.ITEM_ID FROM DEV_PAS.T_CONTRACT_PRODUCT TC,DEV_PDS.T_PRODUCT_LIFE TL WHERE TL.PRODUCT_ID=TC.PRODUCT_ID) CP ON CP.ITEM_ID = PA.ITEM_ID
INNER JOIN (SELECT PAS.ACCOUNT_ID,MAX(PAS.INSERT_TIME) AS INSERT_TIME FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM PAS GROUP BY PAS.ACCOUNT_ID) T ON T.ACCOUNT_ID=PA.ACCOUNT_ID
WHERE 1=1 ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND CM.POLICY_ID = #{policy_id} ]]></if>
	</select>

	<select id="QRY_queryCSProductInfoForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT B.RN,B.POLICY_CODE,B.ACCOUNT_ID,B.BUSI_PROD_CODE,B.PRODUCT_CODE,B.CAPITAL_BALANCE,B.INSERT_TIME FROM
(SELECT ROWNUM RN,CM.POLICY_CODE,PA.ACCOUNT_ID,CBP.BUSI_PROD_CODE,CP.PRODUCT_CODE,T.INSERT_TIME,PA.CAPITAL_BALANCE FROM DEV_PAS.T_POLICY_ACCOUNT PA
INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM ON CM.POLICY_ID=PA.POLICY_ID
INNER JOIN (SELECT TB.PRODUCT_NAME_SYS AS BUSI_PROD_CODE,TP.BUSI_ITEM_ID FROM DEV_PDS.T_BUSINESS_PRODUCT TB,DEV_PAS.T_CONTRACT_BUSI_PROD TP WHERE TP.BUSI_PROD_CODE=TB.PRODUCT_CODE_SYS) CBP ON CBP.BUSI_ITEM_ID=PA.BUSI_ITEM_ID
LEFT JOIN (SELECT TL.PRODUCT_NAME AS PRODUCT_CODE,TC.ITEM_ID FROM DEV_PAS.T_CONTRACT_PRODUCT TC,DEV_PDS.T_PRODUCT_LIFE TL WHERE TL.PRODUCT_ID=TC.PRODUCT_ID) CP ON CP.ITEM_ID = PA.ITEM_ID
INNER JOIN (SELECT PAS.ACCOUNT_ID,MAX(PAS.INSERT_TIME) AS INSERT_TIME FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM PAS GROUP BY PAS.ACCOUNT_ID) T ON T.ACCOUNT_ID=PA.ACCOUNT_ID
WHERE ROWNUM <= #{LESS_NUM} ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND CM.POLICY_ID = #{policy_id} ]]></if>
	<![CDATA[) B WHERE B.RN > #{GREATER_NUM}]]>
	</select>
     <select id="PA_findPolicyAccountInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
         SELECT  
           S.STREAM_ID,
            SUM(S.CAPITAL_BALANCE) AS CAPITAL_BALANCE,
            SUM(round(S.INTEREST_CAPITAL,2))  AS INTEREST_CAPITAL,/*贷款本金利息和*/
            MIN(S.LOAN_START_DATE) AS INSERT_TIME,
            MAX(S.REPAY_DUE_DATE) AS REPAY_DUE_DATE,
            SUM(round(S.INTEREST_CAPITAL-S.CAPITAL_BALANCE,2) ) AS INTEREST_BALANCE,/*贷款利息*/
            S.ACT_REPAY_DATE,
           S.INTEREST_START_DATE,/*贷款起息日*/
       		S.REGULAR_REPAY      
          FROM
               (SELECT
                CAPITAL_BALANCE, LOAN_START_DATE,REPAY_DUE_DATE,POLICY_ID,
                ACCOUNT_ID,ACCOUNT_TYPE,
                (
                CASE 
                    WHEN REGULAR_REPAY=1 THEN CAPITAL_BALANCE+INTEREST_SUM
                    WHEN REGULAR_REPAY=0 THEN CAPITAL_BALANCE+INTEREST_BALANCE
                END
                ) AS INTEREST_CAPITAL,STREAM_ID,ACT_REPAY_DATE,
               REGULAR_REPAY,
               s.INTEREST_START_DATE/*贷款起息日*/
                FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM S WHERE S.ACCOUNT_TYPE='4'/*保单贷款账户*/  )  S,
               DEV_PAS.T_CONTRACT_MASTER          M
         WHERE
           S.POLICY_ID = M.POLICY_ID
         ]]>
          <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND M.POLICY_CODE = #{policy_code} ]]></if>
          <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND M.APPLY_CODE = #{apply_code} ]]></if>   
        <![CDATA[
            GROUP BY S.STREAM_ID, S.ACT_REPAY_DATE,S.INTEREST_START_DATE, S.REGULAR_REPAY
           ORDER BY  S.REGULAR_REPAY,MIN(S.LOAN_START_DATE) desc
       ]]>
        
    </select>
	<!-- <select id="QRY_queryCSInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT COUNT(8)
FROM DEV_PAS.T_CS_CONTRACT_MASTER CM
INNER JOIN (SELECT C.CHANGE_ID,SC.STATUS_DESC AS CHANGE_STATUS 
FROM DEV_PAS.T_CHANGE C,DEV_PAS.T_CHANGE_STATUS SC WHERE C.CHANGE_STATUS=SC.CHANGE_STATUS) CC ON CM.CHANGE_ID=CC.CHANGE_ID
INNER JOIN (SELECT AC.CHANGE_ID,AC.ACCEPT_ID,AC.ACCEPT_CODE,S.SERVICE_NAME AS SERVICE_CODE,A.STATUS_DESC AS ACCEPT_STATUS,AC.ACCEPT_TIME,AC.INSERT_OPERATOR_ID 
FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC,DEV_PAS.T_SERVICE S,DEV_PAS.T_ACCEPT_STATUS A WHERE AC.SERVICE_CODE=S.SERVICE_CODE AND A.ACCEPT_STATUS=AC.ACCEPT_STATUS) AA ON AA.CHANGE_ID=CM.CHANGE_ID
WHERE 1=1 AND CM.OLD_NEW=1 ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
	</select> -->
	<!-- 查询保全清单个数  -->
     <select id="QRY_queryCSInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[
              SELECT COUNT(1)
		    FROM (]]>
			<include refid="query_policyCsInfo" />
		  <![CDATA[)  ]]>
    </select>

<!-- 查询所有保全清单  -->
  <select id="QRY_queryCSInfoForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
            SELECT 
      H.*
  FROM (SELECT ROWNUM RN,C.*
  	FROM( ]]>
  	<include refid="query_policyCsInfo" />
  	 <![CDATA[)C WHERE  ROWNUM <=#{LESS_NUM}  ) H  WHERE  H.RN > #{GREATER_NUM} ]]> 
    </select>
   <!--查询保全记录信息  -->
  <sql id="query_policyCsInfo">
  	<![CDATA[
     SELECT T.*,
     (
          CASE WHEN FEE_AMOUNT1>=FEE_AMOUNT2 then FEE_AMOUNT1-FEE_AMOUNT2
          WHEN FEE_AMOUNT1<FEE_AMOUNT2 THEN FEE_AMOUNT2-FEE_AMOUNT1 END) AS FEE_AMOUNT,
     (
          CASE WHEN FEE_AMOUNT1>FEE_AMOUNT2 THEN '1'
          WHEN FEE_AMOUNT1<FEE_AMOUNT2 THEN '2' 
          ELSE ''
          END) AS ARAP_FLAG /*收付标识*/
           FROM(
                SELECT DISTINCT
                  (SELECT listagg(AAA.POLICY_CODE,',') WITHIN GROUP (ORDER BY  AAA.Accept_Id)
               FROM DEV_PAS.T_CS_POLICY_CHANGE AAA WHERE AAA.Accept_Id=CAC.Accept_Id
               ]]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND AAA.POLICY_CODE = #{policy_code} ]]></if>
                 <![CDATA[ 
               ) AS POLICY_CODE,
                (SELECT LISTAGG(CASE TCM.LIABILITY_STATE 
                                WHEN 3 THEN
                                   EC.CAUSE_NAME
                                WHEN 4 THEN
                                   LC.CAUSE_DESC                                  
                                ELSE
                                   LS.STATUS_NAME
                                END
                        , ',')  WITHIN GROUP(ORDER BY CPC.POLICY_CODE)
                            FROM DEV_PAS.T_CONTRACT_MASTER TCM 
                            LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC ON CPC.POLICY_ID = TCM.POLICY_ID 
                            LEFT JOIN DEV_PAS.T_LIABILITY_STATUS LS ON TCM.LIABILITY_STATE = LS.STATUS_CODE
                            LEFT JOIN DEV_PAS.T_LAPSE_CAUSE LC ON TCM.LAPSE_CAUSE = LC.CAUSE_CODE        
                            LEFT JOIN DEV_PAS.T_END_CAUSE EC ON TCM.END_CAUSE = EC.CAUSE_CODE             
                            WHERE CPC.ACCEPT_ID = CAC.ACCEPT_ID
                            ]]>
                            <if test=" policy_code != null and policy_code != ''  "><![CDATA[AND CPC.POLICY_CODE = #{policy_code} ]]></if>
                        	<![CDATA[ 
                            ) AS LIABILITY_STATE,
                 (SELECT listagg(CPC.ORGAN_CODE,',') WITHIN GROUP (ORDER BY  CPC.ACCEPT_ID)
                   FROM DEV_PAS.T_CS_POLICY_CHANGE CPC WHERE CPC.ACCEPT_ID = CAC.ACCEPT_ID
                   ]]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND CPC.POLICY_CODE = #{policy_code} ]]></if>
	                 <![CDATA[ 
	               ) AS POLICY_ORGAN_CODE,/*保单管理机构*/
                   CAC.ACCEPT_ID,
                   CAC.ACCEPT_CODE, /*受理号*/
                   CAC.SERVICE_CODE, /*保全项目*/
                   NVL((SELECT MAX(TBO.OP_TIME) FROM DEV_PAS.V_BIZ_OPERATION_ALL TBO WHERE TBO.EVENT_CODE='10401' AND TBO.BIZ_KEY=CAC.ACCEPT_CODE),CAC.ACCEPT_TIME) AS ACCEPT_TIME, /*受理日期*/
                   CAC.REVIEW_TIME,/*保全复核时间*/
                   CAC.ACCEPT_STATUS, /*受理状态*/
                   CAC.ORGAN_CODE, /*受理机构 */
                   CAC.INSERT_BY as INSERT_OPERATOR_ID, /*用户名*/
                   CAC.VALIDATE_TIME,/*生效日期*/
                   CAC.PRE_VALIDATE_DATE,/*指定生效日期*/
                   CAC.PRE_FLAG,/*预受理标识*/
                   S.SERVICE_NAME,
                   CA.CHANGE_ID,
                   CA.APPLY_TIME, /*申请日期*/
                   CA.APPLY_CODE,/*保全申请号*/
                   CA.APP_STATUS,/*申请状态*/
                  (SELECT NVL(SUM(PA.FEE_AMOUNT),0)
                     FROM DEV_PAS.V_PREM_ARAP_ALL PA
                       WHERE  PA.Business_Code=CAC.ACCEPT_CODE AND PA.ARAP_FLAG=1
                        AND PA.FEE_STATUS !='16'
                        AND PA.FEE_STATUS !='02'
                    ]]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND PA.POLICY_CODE = #{policy_code} ]]></if>
                 <![CDATA[
                       ) AS FEE_AMOUNT1/*收费金额*/,
                   (SELECT 	NVL(SUM(PA.FEE_AMOUNT),0)
                         FROM DEV_PAS.V_PREM_ARAP_ALL PA
                       WHERE  PA.Business_Code=CAC.ACCEPT_CODE AND PA.ARAP_FLAG=2
                       AND PA.FEE_STATUS !='16'
                       AND PA.FEE_STATUS !='02'
                         ]]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[AND PA.POLICY_CODE = #{policy_code} ]]></if>
                       <![CDATA[
                       ) AS FEE_AMOUNT2,/*付费金额*/
                       UU.REAL_NAME,/*用户姓名*/
                       UU.PHONE, /*手机号*/
                       UU.TEL_PHONE AS OFFICE_TEL,/*办公电话*/
                       UU.EMAIL /*邮箱*/
              FROM DEV_PAS.T_CS_ACCEPT_CHANGE CAC
             INNER JOIN DEV_PAS.T_CS_APPLICATION CA
                ON CA.CHANGE_ID = CAC.CHANGE_ID
             LEFT JOIN DEV_PAS.T_UDMP_USER UU
           	    ON UU.USER_ID = CAC.INSERT_BY
             INNER JOIN DEV_PAS.T_SERVICE S
                ON S.SERVICE_CODE = CAC.SERVICE_CODE
             WHERE 1 = 1
             AND CAC.ACCEPT_STATUS  NOT IN  ('01','02','24','25')
         ]]>
	<if test=" policy_code != null and policy_code != ''  ">
        <![CDATA[AND EXISTS
                         (SELECT *
                                  FROM DEV_PAS.T_CS_POLICY_CHANGE CPC
                                 WHERE CPC.POLICY_CODE = #{policy_code}
                                   AND CPC.ACCEPT_ID = CAC.ACCEPT_ID)]]></if>
	<if test=" accept_code != null and accept_code != ''  "><![CDATA[AND CAC.ACCEPT_CODE = #{accept_code} ]]></if>
         <![CDATA[  ORDER BY CA.APPLY_TIME DESC, CAC.ACCEPT_CODE DESC   ) T]]>
  </sql>
  
	<!-- 投连万能账户基本信息查询个数操作 -->
	<select id="findAccountTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[   SELECT COUNT(1)
    	FROM DEV_PAS.T_CONTRACT_INVEST AP
    LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TC
    	ON AP.POLICY_ID = TC.POLICY_ID
    WHERE 1 =1 AND (AP.INVEST_ACCOUNT_TYPE=1 OR AP.INVEST_ACCOUNT_TYPE=2) and ROWNUM <= 10000
		]]>
		<!-- 查询条件-->
		<if test="policy_code != null and policy_code !='' "><![CDATA[ AND TC.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	<!-- 投连万能账户信息分页查询操作 -->
	 <select id="findAccountPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[  
            SELECT  B.* FROM (
            SELECT ROWNUM RN,
            AP.LIST_ID AS ACCOUNT_ID ,
            TC.POLICY_CODE,
            AP.INVEST_ACCOUNT_TYPE,
            BP.BUSI_PROD_CODE,
          (select tt.product_abbr_name
       from dev_pds.t_business_product tt
        left join dev_pas.t_contract_busi_prod dd
         on tt.business_prd_id = dd.busi_prd_id
         where dd.busi_item_id = AP.busi_item_id)as busi_prod_name,
        AP.POLICY_ID, 
        AP.ITEM_ID product_code,
       (
        SELECT DEAL_TIME FROM    ( SELECT 
            FS.SETTLE_DATE AS DEAL_TIME,FS.INVEST_ID
                FROM DEV_PAS.T_FUND_SETTLEMENT FS
            INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI  
            ON FS.INVEST_ID=CI.LIST_ID
             INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM 
            ON  CI.POLICY_ID=CM.POLICY_ID
            WHERE CM.POLICY_CODE= #{policy_code} order by  FS.SETTLE_DATE DESC ) B WHERE ROWNUM = 1) count_date,
       AP.CREATE_DATE  account_date,
       (              
           SELECT pricing_date FROM  (
                  select ipd.pricing_date
             from dev_pas.T_INVEST_unit_PRICE ipd
             INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI  
            ON ipd.invest_account_code=CI.Account_Code
             INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM 
            ON  CI.POLICY_ID=CM.POLICY_ID
            WHERE CM.POLICY_CODE= #{policy_code}
            order by ipd.pricing_date desc
           ) S WHERE ROWNUM = 1
           
       ) as pricing_date,
       (
            CASE AP.INVEST_ACCOUNT_TYPE 
            WHEN 1 THEN 
                (SELECT round(BID_PRICE * AP.ACCUM_UNITS,2) FROM(
                SELECT BID_PRICE, ROWNUM AS RN
                             FROM (SELECT TUP.BID_PRICE, ROWNUM AS RN
                             FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
                            INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                               ON TUP.INVEST_ACCOUNT_CODE = CI.ACCOUNT_CODE
                            WHERE 1 = 1
                ]]>
                 <if test="policy_id  != null and policy_id !='' "><![CDATA[ AND  CI.POLICY_ID=#{policy_id} ]]></if>
                 <if test="(policy_id  == null or policy_id =='') and (policy_code!=null and policy_code!='') "><![CDATA[ AND CI.POLICY_ID=(SELECT POLICY_ID FROM DEV_PAS.T_CONTRACT_MASTER CM WHERE CM.POLICY_CODE=#{policy_code}) ]]></if>
               <![CDATA[ ORDER BY TUP.PRICING_DATE DESC) C) D
                    WHERE D.RN = 1)
            WHEN 2 THEN 
                AP.INTEREST_CAPITAL 
            END
       )    account_value,
       AP.ACCUM_UNITS  unit_number,
       (
        CASE AP.INVEST_ACCOUNT_TYPE 
        WHEN 1 THEN 
            (SELECT CAL_BID_PRICE FROM 
                (SELECT CAL_BID_PRICE,ROWNUM AS RN FROM
                    (SELECT  TUP.CAL_BID_PRICE,
                        ROWNUM AS RN FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI ON TUP.INVEST_ACCOUNT_CODE=CI.ACCOUNT_CODE 
                        INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM ON CM.POLICY_ID=CI.POLICY_ID
                        WHERE CM.POLICY_CODE=#{policy_code}
                    ORDER BY TUP.PRICING_DATE DESC) 
                 C) D WHERE D.RN=1)
        WHEN 2 THEN 
            null
        END
       )  unit_price，
       (SELECT PL.PRODUCT_NAME 
       FROM DEV_PDS.T_PRODUCT_LIFE PL 
       LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP ON CP.PRODUCT_ID=PL.PRODUCT_ID
       WHERE CP.ITEM_ID= AP.ITEM_ID) product_name
            
    FROM DEV_PAS.T_CONTRACT_INVEST AP
    INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD  BP ON BP.BUSI_ITEM_ID=AP.BUSI_ITEM_ID AND BP.POLICY_ID=AP.POLICY_ID
    LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TC
    ON AP.POLICY_ID = TC.POLICY_ID AND BP.POLICY_ID=TC.POLICY_ID
    WHERE 1 =1 AND (AP.INVEST_ACCOUNT_TYPE=1 OR AP.INVEST_ACCOUNT_TYPE=2)
          ]]>
         <if test="policy_code  != null and policy_code !='' "><![CDATA[ AND TC.POLICY_CODE = #{policy_code} ]]></if> 
       <![CDATA[ 
        AND BP.BUSI_PROD_CODE!='********'
        AND BP.BUSI_PROD_CODE!='********'
        AND BP.BUSI_PROD_CODE!='00Z01000'
        AND BP.BUSI_PROD_CODE not in ('********','********','********')
        AND ROWNUM <= #{LESS_NUM} )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
    </select>
	<!-- 万能账户轨迹查询个数操作 -->
	<select id="findAccountPathTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
	        <![CDATA[ 
	        SELECT COUNT(*)
	        FROM(
			        SELECT 
			        '42' AS TRANS_CODE,
			        B.SETTLE_DATE AS DEAL_Time,
			        B.LAST_GURNT_BALANCE AS TRANS_AMOUNT,
			        B.END_INTEREST_RATE AS TRANS_PROPORTION,
			        B.BALANCE AS TRANS_INTEREST,
			        null AS UNIT_NUMBER,
			        null AS UNIT_PRICE
			         FROM (
			          SELECT   CI.*,
		              FS.*,
		              ROWNUM  as RN
			            FROM DEV_PAS.T_FUND_SETTLEMENT FS
			          INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			            ON CI.LIST_ID=FS.INVEST_ID
			          WHERE 1=1
	          ]]>
			 <if test=" account_id  != null "><![CDATA[ AND  CI.LIST_ID = #{account_id} ]]></if> 
	        <![CDATA[ )  B
	        UNION All
	         SELECT 
			           B.TRANS_CODE AS TRANS_CODE,
				        B.DEAL_TIME AS DEAL_TIME,
				        B.TRANS_AMOUNT AS TRANS_AMOUNT,
				        B.TRANS_INTEREST AS TRANS_PROPORTION,
				        B.BALANCE AS TRANS_INTEREST,
				        B.TRANS_UNITS AS UNIT_NUMBER,
				        B.TRANS_PRICE AS UNIT_PRICE
				         FROM (
				          SELECT   CI.*,
			              TC.TRANS_CODE,
		                TC.DESCRIPTION,
		                FT.DEAL_TIME,
		                FT.TRANS_AMOUNT,
		                FT.TRANS_INTEREST,
		                FT.TRANS_UNITS,
		                FT.TRANS_PRICE,
		                FT.BALANCE_UNITS_BF,
		                (
		                    case FT.Trans_Type
		                      when 0 then FT.Balance_Units_Bf
		                      when 1 then FT.Balance_Units_Bf-FT.TRANS_AMOUNT
		                      when 2 then FT.Balance_Units_Bf+FT.TRANS_AMOUNT
		                      when 3 then FT.BALANCE_UNITS_BF-FT.Trans_Amount
		                    end
		                            
		                ) as  BALANCE,
			              ROWNUM  AS RN
				       FROM DEV_PAS.T_FUND_TRANS FT
				       INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
		                ON CI.LIST_ID=FT.LIST_ID
		               INNER JOIN DEV_PAS.T_TRANSACTION_CODE TC
		                ON FT.TRANS_CODE= TC.TRANS_CODE
				          WHERE 1=1 
	          ]]>
			 <if test=" account_id  != null  "><![CDATA[ AND  CI.LIST_ID = #{account_id} ]]></if>
			 <![CDATA[ ) B) C  WHERE 1=1 ]]>
	</select>
	<!-- 投连万能账户轨迹分页查询操作 -->
	<select id="findAccountPathPage" resultType="java.util.Map"
		parameterType="java.util.Map">
	        <![CDATA[ 
            SELECT F.* FROM(
	        SELECT E.* FROM(
	        SELECT D.*,ROWNUM as RN FROM(
	        SELECT C.*
	        FROM(
			        SELECT 
			        '42' AS TRANS_CODE,
			        B.SETTLE_DATE AS DEAL_Time,
			        B.INTEREST AS TRANS_AMOUNT,
			        B.INTEREST_RATE AS TRANS_PROPORTION,
			        B.BALANCE AS TRANS_INTEREST,
			        null AS UNIT_NUMBER,
			        null AS UNIT_PRICE,
			        B.Settlement_Id AS TRANS_ID,
			        2 as TRANS_TYPE,
			        B.INVEST_ACCOUNT_TYPE
			         FROM (
			          SELECT   CI.*,
		              FS.*,
		              ROWNUM  as RN
			            FROM DEV_PAS.T_FUND_SETTLEMENT FS
			          INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
			            ON CI.LIST_ID=FS.INVEST_ID
			          WHERE 1=1
	          ]]>
			 <if test=" account_id  != null"><![CDATA[ AND  CI.LIST_ID = #{account_id} ]]></if> 
	        <![CDATA[ )  B
	        UNION ALL
	         SELECT 
			           B.TRANS_CODE AS TRANS_CODE,
				        B.DEAL_TIME AS DEAL_TIME,
				        B.TRANS_AMOUNT AS TRANS_AMOUNT,
				        B.TRANS_INTEREST AS TRANS_PROPORTION,
				        B.BALANCE AS TRANS_INTEREST,
				        B.TRANS_UNITS AS UNIT_NUMBER,
				        B.TRANS_PRICE AS UNIT_PRICE,
                        B.TRANS_ID AS TRANS_ID,
                        B.TRANS_TYPE,
                        B.INVEST_ACCOUNT_TYPE
				         FROM (
				          SELECT   CI.*,
			              TC.TRANS_CODE,
		                TC.DESCRIPTION,
		                FT.DEAL_TIME,
		                FT.TRANS_AMOUNT,
		               (
	                        CASE
	                           WHEN TP.PRODUCT_CATEGORY1 = '20004' and FT.TRANS_INTEREST=0 THEN
	                            NULL
	                           ELSE
	                            FT.TRANS_INTEREST
	                       END                                     
                         ) AS TRANS_INTEREST,
		                FT.TRANS_UNITS,
		                FT.TRANS_PRICE,
		                FT.BALANCE_UNITS_BF,
	                    (
                           CASE
			                 WHEN TP.PRODUCT_CATEGORY1 = '20004' THEN
			                  NULL
			                 WHEN FT.TRANS_CODE = '43' AND FT.TRANS_TYPE = '0' THEN
			                  FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
			                  WHEN FT.TRANS_CODE = '17' AND FT.TRANS_TYPE = '0'
			                   THEN FT.BALANCE_UNITS_BF + FT.TRANS_AMOUNT
			                 ELSE
			                  (CASE FT.TRANS_TYPE
			                    WHEN 0 THEN
			                     FT.BALANCE_UNITS_BF
			                    WHEN 1 THEN
			                     FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
			                    WHEN 2 THEN
			                     FT.BALANCE_UNITS_BF + FT.TRANS_AMOUNT
			                    WHEN 3 THEN
			                     FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
			                  END)
			               END                                     
                         ) AS  BALANCE,
			              ROWNUM  AS RN,
                          FT.TRANS_ID AS TRANS_ID,
                          FT.TRANS_TYPE
				       FROM DEV_PAS.T_FUND_TRANS FT
				       Left JOIN DEV_PAS.T_CONTRACT_INVEST CI
		                ON CI.LIST_ID=FT.LIST_ID
		               Left JOIN DEV_PAS.T_TRANSACTION_CODE TC
		                ON FT.TRANS_CODE= TC.TRANS_CODE
                       Left JOIN Dev_pas.t_Fund_Settlement FS
                      	ON CI.LIST_ID = fs.Invest_Id
                        and FT.DEAL_TIME = FS.SETTLE_DATE
                        INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD TCB
			            ON TCB.BUSI_ITEM_ID = FT.BUSI_ITEM_ID
			           INNER JOIN APP___PAS__DBUSER.T_BUSINESS_PRODUCT TP
			            ON TCB.BUSI_PRD_ID = TP.BUSINESS_PRD_ID
				          WHERE 1=1 
	          ]]>
			 <if test=" account_id  != null "><![CDATA[ AND  CI.LIST_ID = #{account_id} ]]></if>
			 <![CDATA[ ) B) C ORDER BY C.DEAL_TIME DESC,C.TRANS_ID DESC) D ) E WHERE 1=1  and RN <= #{LESS_NUM})F
	        WHERE F.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<!--保单红利信息查询个数 -->
	<select id="find_policyBonusTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[   
		  SELECT COUNT(1) from (
        ]]>
       	 <include refid="find_policyBonus" />
        <![CDATA[   
		  )
        ]]>
	</select>
	<!-- 保单红利信息分页查询 -->
	<select id="find_policyBonusPage" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
        SELECT  B.* FROM (
        	  SELECT D.*, rownum as RN  FROM 
                (SELECT C.*
                 FROM ( 
        ]]>
        	<include refid="find_policyBonus" />
        <![CDATA[ ) C 
         ORDER BY NVL(C.ALLOCATE_DATE, C.REALLO_DATE) DESC,ITEM_ID,C.ALLOCATE_ID)  D
           WHERE ROWNUM <= #{LESS_NUM}  )B
        WHERE   B.RN > #{GREATER_NUM} ]]>
	</select>
	<sql id="find_policyBonus">
		<![CDATA[ 
			SELECT BA.TERMINAL_BONUS_RATE AS FINISH_BONUS_RATE, /*终了分红率*/
                         BA.POLICY_ID,
                         BA.ALLOCATE_ID,
                         BP.BUSI_PRD_ID         AS BUSI_ITEM_ID, /*险种代码对应产品表中的ID*/
                         CP.PRODUCT_ID          AS ITEM_ID,
                         /*责任组代码对应产品表中的ID*/
                         TO_CHAR(NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE),'YYYY') - 1 AS POLICY_YEAR, /*会计年度*/
                         BA.BONUS_ALLOT,
                         CP.CHARGE_YEAR,
                         BA.BONUS_RATE, /*分红率*/
                         BA.BONUS_SA, /*红利保额*/
                         BA.CASH_BONUS, /*现金红利*/
                         BA.REISSUE_INTEREST AS CASH_INTEREST, /*现金红利利息*/
                         NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE) AS ALLOCATE_DATE,
                          (BA.Valid_Amount-BA.STAND_AMOUNT) as SUM_BONUS_SA,
                         C.CUSTOMER_BIRTHDAY,
                         BA.ALLOCATE_DATE AS REALLO_DATE /*实际分配日期*/
                    FROM DEV_PAS.T_BONUS_ALLOCATE BA
                   INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
                      ON BA.POLICY_CODE = CM.POLICY_CODE
                   INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
                      ON CP.ITEM_ID = BA.ITEM_ID
                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
                      ON CP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
                   INNER JOIN DEV_PAS.T_INSURED_LIST IL
                      ON CM.POLICY_CODE = IL.POLICY_CODE
                   INNER JOIN DEV_PAS.T_CUSTOMER C
                      ON C.CUSTOMER_ID = IL.CUSTOMER_ID
                   WHERE 1 = 1
                 	AND BA.BONUS_ALLOT != 4
		]]>
		<if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND   BA.policy_code = #{policy_code} ]]></if> 
         <![CDATA[  
         	UNION ALL
         	SELECT null AS FINISH_BONUS_RATE, /*终了分红率*/
                         BA.POLICY_ID,
                         BA.ALLOCATE_ID,
                         BP.BUSI_PRD_ID         AS BUSI_ITEM_ID, /*险种代码对应产品表中的ID*/
                         CP.PRODUCT_ID          AS ITEM_ID,
                         /*责任组代码对应产品表中的ID*/
                         TO_CHAR(NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE),'YYYY') - 1 AS POLICY_YEAR, /*会计年度*/
                         BA.BONUS_ALLOT,
                         CP.CHARGE_YEAR,
                         null as BONUS_RATE, /*分红率*/
                         BA.BONUS_SA, /*红利保额*/
                         BA.CASH_BONUS, /*现金红利*/
                         BA.REISSUE_INTEREST AS CASH_INTEREST, /*现金红利利息*/
                         NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE) AS ALLOCATE_DATE,
                         (BA.Valid_Amount-BA.STAND_AMOUNT) as SUM_BONUS_SA,
                         C.CUSTOMER_BIRTHDAY,
                         BA.ALLOCATE_DATE AS REALLO_DATE /*实际分配日期*/
                      FROM DEV_PAS.T_BONUS_ALLOCATE BA
                   INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
                      ON BA.POLICY_CODE = CM.POLICY_CODE
                   INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
                      ON CP.ITEM_ID = BA.ITEM_ID
                   INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
                      ON CP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
                   INNER JOIN DEV_PAS.T_INSURED_LIST IL
                      ON CM.POLICY_CODE = IL.POLICY_CODE
                   INNER JOIN DEV_PAS.T_CUSTOMER C
                      ON C.CUSTOMER_ID = IL.CUSTOMER_ID                  
                   WHERE 1 = 1 AND   BA.BONUS_ALLOT = 4  /*现金红利*/
                   AND  (BA.VALID_FLAG IS NULL OR BA.VALID_FLAG = 1)/*非回退的红利信息*/
         ]]>
		 <if test=" policy_code  != null and policy_code !='' "><![CDATA[ AND   BA.policy_code = #{policy_code} ]]></if> 
	</sql>
		<!--红利详细信息查询 -->
	<select id="findBonusDetailInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[   
		        SELECT BA.ALLOCATE_ID,
       BP.BUSI_PROD_CODE, --险种代码
       TBP.PRODUCT_NAME_SYS,
       CP.PRODUCT_CODE, --责任组代码
       TPL.PRODUCT_NAME,
       nvl(BA.STAND_AMOUNT,CP.AMOUNT) AS AMOUNT,--基本保额
       TO_CHAR(NVL(BA.ALLOCATE_DUE_DATE, BA.ALLOCATE_DATE),'YYYY') - 1 AS POLICY_YEAR,--会计年度 
       BA.BONUS_ALLOT, --分红类别
      (CASE WHEN BA.BONUS_ALLOT=4 THEN NULL ELSE  BA.BONUS_RATE END ) AS BONUS_RATE , --分红率
       (CASE WHEN BA.BONUS_ALLOT=4 THEN NULL ELSE  BA.TERMINAL_BONUS_RATE END ) AS FINISH_BONUS_RATE, --终了分红率 bug#3017修改
       (BA.Valid_Amount-BA.STAND_AMOUNT) as SUM_BONUS_SA, --累计红利保额
       BA.BONUS_SA, --红利保额
       BA.CASH_BONUS, --现金红利
       BA.REISSUE_INTEREST AS CASH_INTEREST, --现金红利利息
      nvl(BA.ALLOCATE_DUE_DATE,BA.ALLOCATE_DATE) AS ALLOCATE_DATE,--应分配日期
       BA.BONUS_DOC_DATE AS REALLO_DATE--实际分配日期
       --BA.ALLOCATE_DATE AS ALLOCATE_DATE --应分配日期
  FROM DEV_PAS.T_BONUS_ALLOCATE BA
 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
    ON CP.ITEM_ID = BA.ITEM_ID
 INNER JOIN DEV_PDS.T_PRODUCT_LIFE TPL
    ON CP.PRODUCT_ID = TPL.PRODUCT_ID
 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
    ON CP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
    ON TBP.BUSINESS_PRD_ID = BP.BUSI_PRD_ID
          WHERE 1 = 1
       ]]>
		<!-- 查询条件-->
		<if test="allocate_id  != null and allocate_id !='' "><![CDATA[ AND  BA.ALLOCATE_ID = #{allocate_id} ]]></if> 
	</select>
	<!-- 生存给付计划 -->
     <sql id="findPayPlanInfoSql">
        <![CDATA[ 
         FROM DEV_PAS.T_PAY_PLAN TPP
          LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCMP
            ON TPP.POLICY_ID = TCMP.POLICY_ID
          LEFT JOIN DEV_PAS.T_PAY_PLAN_PAYEE TPPP
            ON TPP.PLAN_ID = TPPP.PLAN_ID
          LEFT JOIN DEV_PAS.T_BANK_ACCOUNT TBA
            ON TPPP.PAYEE_ACCOUNT_ID = TBA.ACCOUNT_ID
          LEFT JOIN DEV_PAS.T_BANK B ON TBA.BANK_CODE=B.BANK_CODE
          LEFT JOIN DEV_PAS.T_SURVIVAL_MODE SM
            ON TPP.SURVIVAL_MODE = SM.MODE_CODE
          LEFT JOIN DEV_PAS.T_SURVIVAL_W_MODE SWM
            ON TPP.SURVIVAL_W_MODE = SWM.MODE_CODE
         LEFT JOIN DEV_PAS.T_LIABILITY L
            ON L.LIAB_ID = TPP.LIAB_ID
          LEFT JOIN 
          (SELECT SUM( CASE
					         WHEN PD.FEE_STATUS = '00' AND PD.DEDUCTION_FLAG = 1 AND
					              TA.DEDU_AMOUNT > 0 THEN
					          TA.DEDU_AMOUNT
					         WHEN PD.FEE_STATUS = '01' THEN
					          (CASE
					            WHEN PD.SURVIVAL_TYPE IN ('1', '3', '5') THEN
					             PD.FEE_AMOUNT
					            ELSE
					             0/*追回的不计入*/
					          END)
					         ELSE
					          0
				       END) AS TOTAL_AMOUNT,
                    SUM(CASE
			             WHEN PD.SURVIVAL_TYPE IN (1, 3, 5) THEN
			              1
			             ELSE
			              0 /*追回的不计入*/
			           END) AS PAY_YEAR,
                    PD.PLAN_ID
               FROM DEV_PAS.T_PAY_DUE PD
               LEFT JOIN DEV_PAS.T_SURVIVAL_DEDUCTION_TASK TA
                 ON PD.PAY_ID = TA.PAY_ID
                 AND TA.DEDU_RESULT = 1
              WHERE (PD.FEE_STATUS = '01' OR PD.FEE_STATUS = '16' or
                    (PD.FEE_STATUS = '00' AND TA.DEDU_AMOUNT > 0)) AND PD.POLICY_CODE = #{POLICY_CODE}
            GROUP BY PD.PLAN_ID
          ) D ON D.PLAN_ID=TPP.PLAN_ID
         WHERE 1 = 1
 AND (TPP.PAY_PLAN_TYPE='3' OR TPP.PAY_PLAN_TYPE='4' or TPP.PAY_PLAN_TYPE='10' OR TPP.PAY_PLAN_TYPE='8')
          ]]>
        <!-- <include refid="请添加查询条件" /> -->
        <if test=" POLICY_CODE  != null"><![CDATA[ AND TPP.POLICY_CODE = #{POLICY_CODE} ]]></if>
    </sql>
	<!-- 查询个数操作 -->
	 <select id="findPayPlanInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[    SELECT 
            COUNT(1)
            ]]>
            <include refid="findPayPlanInfoSql" />
    </select>


<!-- 分页查询操作 -->
    <select id="findPayPlanInfoPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT B.RN,
       B.POLICY_CODE,
       B.BUSI_ITEM_ID,
       B.BUSI_PROD_CODE,
       B.BUSI_PROD_NAME,
       B.PRODUCT_CODE,
       B.LIAB_ID,
       B.LIAB_NAME,
       B.BEGIN_DATE,
       B.END_DATE,
       B.PAY_STATUS,
       B.PLAN_FREQ,
       B.INSTALMENT_AMOUNT,
       B.TOTAL_AMOUNT,
       B.PAY_NUM,
       B.PAY_YEAR,
       B.PLAN_ID,
       B.RECEIVE_FORMALLY, /*领取形式*/
       B.ISSUE_BANK_NAME, /*开户银行*/
       B.ACCO_NAME, /*户名*/
       B.BANK_ACCOUNT /*银行账户*/,
       
       (CASE
         WHEN B.BUSI_PROD_CODE in ('********','********') THEN
          (select A.START_PAY_AGE
             from DEV_PAS.T_CONTRACT_PRODUCT A
            WHERE A.ITEM_ID = B.ITEM_ID)
         ELSE
          FLOOR(MONTHS_BETWEEN(B.BEGIN_DATE, B.CUSTOMER_BIRTHDAY) / 12)
       END) AS CUSTOMER_BIRTDAY /*领取年龄*/
       
  FROM (
        
        SELECT ROWNUM RN,
                TCMP.POLICY_CODE,
                TPP.ITEM_ID,
                TPP.BUSI_ITEM_ID,
                TPP.BUSI_PROD_CODE,
                (select tt.PRODUCT_NAME_SYS
                     from dev_pds.t_business_product tt
                     left join dev_pas.t_contract_busi_prod dd
                     on tt.business_prd_id = dd.busi_prd_id
                     where dd.busi_item_id = TPP.busi_item_id)as busi_prod_name,
                (SELECT PL.PRODUCT_NAME
                   FROM DEV_PDS.T_PRODUCT_LIFE PL
                  WHERE PL.INTERNAL_ID = TPP.Product_Code) PRODUCT_CODE,
                TPP.LIAB_ID,
                L.LIAB_NAME,
                TPP.BEGIN_DATE,
                TPP.END_DATE,
                TPP.PAY_STATUS,
                TPP.PLAN_FREQ,
                (case when TPP.PAY_STATUS='2' then TPP.INSTALMENT_AMOUNT else null end) AS INSTALMENT_AMOUNT,/*下期给付金额*/
                D.TOTAL_AMOUNT,
                TPP.PAY_NUM,
                D.PAY_YEAR,
                SM.MODE_NAME ||'-'||SWM.MODE_NAME AS RECEIVE_FORMALLY, /*领取形式*/
                nvl(TBA.ISSUE_BANK_NAME,B.BANK_NAME) AS ISSUE_BANK_NAME, /*开户银行*/
                TBA.ACCO_NAME, /*户名*/
                TBA.BANK_ACCOUNT, /*银行账户*/
                TPP.PLAN_ID,
                (SELECT A.CUSTOMER_BIRTHDAY
                   FROM 
                        DEV_PAS.T_CUSTOMER     A /**客户表*/
                  WHERE A.CUSTOMER_ID = TPPP.CUSTOMER_ID) AS CUSTOMER_BIRTHDAY
         ]]> 
        <include refid="findPayPlanInfoSql" />
        <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
    </select>
   <sql id="findPayDueInfoSql">
         <![CDATA[ 
          SELECT DISTINCT
               PD.POLICY_CODE,
               PD.PAY_ID,
               PD.PLAN_ID,
               PD.SURVIVAL_INVEST_FLAG, /*生调标识*/
               CASE  WHEN PD.SURVIVAL_TYPE in ('1','3','5') THEN 
                 PD.FEE_AMOUNT
                 ELSE PD.FEE_AMOUNT * -1
                   END AS FEE_AMOUNT, /*金额*/
               PD.FEE_STATUS,
               PD.PAY_DUE_DATE AS REAL_PAY_DATE, /*应付日期*/
               nvl((select min(t.pay_due_date)
                              from dev_pas.t_pay_due t
                             where t.plan_id = pd.plan_id
                               and t.pay_due_date > pd.pay_due_date
                               ),
                            pp.pay_due_date) as pay_due_date, /*本次领至日期*/ 
               PD.LIAB_ID,
               SM.MODE_NAME ||'-'||SWM.MODE_NAME AS SURVIVAL_MODE, /*生存领取形式*/
               CBP.BUSI_PROD_CODE,
               BP.PRODUCT_NAME_SYS,
               CP.PRODUCT_CODE,
               PRL.PRODUCT_NAME,
               PLA.LIAB_NAME,
               SM.MODE_NAME,
               PAR.STATISTICAL_DATE AS CONFIRM_DATE,/*业务核销时间*/
               PAR.FINISH_TIME,/*收付费到账时间*/
               CD.PAYREFNO,
               CD.UNIT_NUMBER,
               PD.insert_time,
               (case when CBP.Liability_State = '3' and CBP.End_Cause = '03' and PD.Fee_Status = '00'
                        then 1
                     else 0 end) as obtainWaitMon,
                     SDT.DEDU_AMOUNT,
               CASE WHEN SDT.ACCEPT_CODE IS NOT NULL
               THEN SDT.ACCEPT_CODE || '(' || ASS.STATUS_DESC  || ')' 
               ELSE NULL
               END AS DEDU_ACCEPT_CODE
               
          FROM DEV_PAS.T_PAY_DUE PD
        LEFT JOIN DEV_PAS.T_PAY_PLAN PP
            ON pd.plan_id = pp.plan_id
         INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
            ON CBP.BUSI_ITEM_ID = PD.BUSI_ITEM_ID
         INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
            ON BP.PRODUCT_CODE_SYS = CBP.BUSI_PROD_CODE
         INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
            ON CP.ITEM_ID = PD.ITEM_ID
         INNER JOIN DEV_PDS.T_PRODUCT_LIFE PRL
            ON PRL.PRODUCT_ID = CP.PRODUCT_ID
         LEFT JOIN DEV_PAS.T_LIABILITY PLA
            ON PLA.LIAB_ID = PD.LIAB_ID
         LEFT JOIN DEV_PAS.T_SURVIVAL_MODE SM
            ON SM.MODE_CODE = PD.SURVIVAL_MODE
         LEFT JOIN DEV_PAS.T_SURVIVAL_W_MODE SWM
             ON PP.SURVIVAL_W_MODE = SWM.MODE_CODE
         LEFT JOIN DEV_CAP.V_PREM_ARAP PAR
            ON PAR.UNIT_NUMBER = PD.UNIT_NUMBER
           /* AND (PAR.Unit_Number is not null OR PD.FEE_STATUS = '00')*/
         LEFT JOIN DEV_CAP.V_CASH_DETAIL CD
            ON PAR.UNIT_NUMBER=CD.UNIT_NUMBER
            
          LEFT JOIN DEV_PAS.T_SURVIVAL_DEDUCTION_TASK SDT
         ON SDT.PAY_ID = PD.PAY_ID
         AND SDT.DEDU_RESULT = '1'
         LEFT JOIN DEV_PAS.T_ACCEPT_STATUS ASS
         ON SDT.ACCEPT_STATUS = ASS.ACCEPT_STATUS          
         WHERE  PD.FEE_STATUS != '02'
         
         AND (PAR.Unit_Number is not null OR PD.FEE_STATUS = '00')
          ]]>
        <if test=" POLICY_CODE  != null"><![CDATA[ AND PD.POLICY_CODE = #{POLICY_CODE} ]]></if>
        <if test=" BUSI_ITEM_ID  != null"><![CDATA[ AND PD.BUSI_ITEM_ID = #{BUSI_ITEM_ID} ]]></if>
        <if test=" PLAN_ID  != null"><![CDATA[ AND PD.PLAN_ID = #{PLAN_ID} ]]></if>
    </sql>
    <sql id="findCMPayDueInfoSql">
		select * from (
		with ta as
		(select B.POLICY_CODE, /*保单号*/
		new_.pay_id,
		new_.plan_id,
		new_.survival_invest_flag,
		new_.fee_amount - old_.fee_amount as fee_amount,
		new_.fee_status,
		new_.pay_due_date as REAL_PAY_DATE, /*应付日期*/
		NVL((select min(tcp.pay_due_date)
		from DEV_PAS.T_CS_PAY_DUE tcp
		where b.policy_chg_id = tcp.policy_chg_id
		and b.policy_id = tcp.policy_id
		and tcp.old_new = '1'
		and tcp.fee_status = '01'
		and tcp.plan_id = new_.plan_id
		AND tcp.PAY_DUE_DATE > new_.PAY_DUE_DATE),
		tpl.PAY_DUE_DATE) AS PAY_DUE_DATE, /*本次领至日期*/
		new_.LIAB_ID,

		new_.busi_prod_code,
		(select tt.PRODUCT_NAME_SYS
		from DEV_PDS.T_BUSINESS_PRODUCT tt
		where tt.product_code_sys = new_.busi_prod_code) as PRODUCT_NAME_SYS,
		new_.product_code,
		(select tp.PRODUCT_NAME
		from DEV_PDS.T_PRODUCT_LIFE tp
		where tp.internal_id = new_.product_code) as PRODUCT_NAME,
		(select pla.liab_name
		from DEV_PDS.T_LIABILITY PLA
		where PLA.LIAB_ID = new_.LIAB_ID) as liab_name,
		(select sm.mode_name
		from DEV_PAS.T_SURVIVAL_MODE SM
		WHERE SM.MODE_CODE = tpl.SURVIVAL_MODE) as mode_name,
		a.accept_code,
        new_.insert_time,
		(select 0 from dual) as obtainWaitMon
		from dev_pas.t_cs_accept_change a
		INNER JOIN dev_pas.t_cs_policy_change b
		ON a.change_id = b.change_id
		and a.accept_id = b.accept_id
		inner join DEV_PAS.T_CS_PAY_DUE new_
		on b.policy_chg_id = new_.policy_chg_id
		and b.policy_id = new_.policy_id
		and new_.old_new = '1'
		and new_.fee_status = '01'
		<if test=" PLAN_ID  != null"><![CDATA[ AND new_.PLAN_ID = #{PLAN_ID} ]]></if>
		inner join DEV_PAS.T_CS_PAY_DUE old_
		on b.policy_chg_id = old_.policy_chg_id
		and b.policy_id = old_.policy_id
		and old_.old_new = '0'
		and (old_.fee_status = '01' or(old_.fee_status='00' and old_.deduction_flag=1 ) )
		and new_.pay_num = old_.pay_num
		<if test=" PLAN_ID  != null"><![CDATA[ AND old_.PLAN_ID = #{PLAN_ID} ]]></if>
		inner join DEV_PAS.t_Cs_Pay_Plan tpl
		on b.policy_chg_id = tpl.policy_chg_id
		and b.policy_id = tpl.policy_id
		and tpl.old_new = '1'
		and new_.plan_id = tpl.plan_id
		where 
		<if test=" POLICY_CODE  != null"><![CDATA[ b.POLICY_CODE = #{POLICY_CODE} ]]></if>
		AND a.SERVICE_CODE = 'CM'
		and a.accept_status = 18),
		tb as
		(select PAR.STATISTICAL_DATE AS CONFIRM_DATE, /*业务核销时间*/
		PAR.FINISH_TIME, /*收付费到账时间*/
		CD.PAYREFNO,
		CD.UNIT_NUMBER,
		(case
		when par.pay_mode = 10 then
		'支取-自行领取'
		else
		'支取-银行转账'
		end) as SURVIVAL_MODE, /*本次领取形式*/
		par.business_code
		from DEV_CAP.T_PREM_ARAP PAR
		LEFT JOIN DEV_CAP.T_CASH_DETAIL CD
		ON PAR.UNIT_NUMBER = CD.UNIT_NUMBER
		where PAR.DERIV_TYPE = '004'
		AND PAR.SERVICE_CODE = 'CM'
		AND par.FEE_TYPE IN ('P004390200', 'G004530400')
		and par.fee_status in ('01', '19', '16','00')
		<if test=" POLICY_CODE  != null"><![CDATA[ AND par.POLICY_CODE = #{POLICY_CODE} ]]></if>
		 group by par.finish_time,
          CD.PAYREFNO,
          CD.UNIT_NUMBER,
          par.pay_mode,
          par.business_code,
          PAR.STATISTICAL_DATE)
		select POLICY_CODE,
		PAY_ID,
		PLAN_ID,
		SURVIVAL_INVEST_FLAG, /*生调标识*/
		FEE_AMOUNT, /*金额*/
		FEE_STATUS,
		REAL_PAY_DATE, /*应付日期*/
		PAY_DUE_DATE, /*本次领至日期*/
		LIAB_ID,
		SURVIVAL_MODE, /*生存领取形式*/
		BUSI_PROD_CODE,
		PRODUCT_NAME_SYS,
		PRODUCT_CODE,
		PRODUCT_NAME,
		LIAB_NAME,
		MODE_NAME,
		CONFIRM_DATE, /*业务核销时间*/
		FINISH_TIME, /*收付费到账时间*/
		PAYREFNO,
		UNIT_NUMBER,
		insert_time,
		obtainWaitMon,
        null as dedu_amount,
        null as dedu_accept_code
		from ta, tb
		where ta.accept_code = tb.business_code
		)
	</sql>
	<sql id="findCMOldPayDueInfoSql">
		SELECT DISTINCT PD.POLICY_CODE,
                PD.PAY_ID,
                PD.PLAN_ID,
                PD.SURVIVAL_INVEST_FLAG, /*生调标识*/
                CASE
                  WHEN PD.SURVIVAL_TYPE in ('1', '3', '5') THEN
                   PD.FEE_AMOUNT
                  ELSE
                   PD.FEE_AMOUNT * -1
                END AS FEE_AMOUNT, /*金额*/
                PD.FEE_STATUS,
                PD.PAY_DUE_DATE AS REAL_PAY_DATE, /*应付日期*/
                nvl((select min(t.pay_due_date)
                      from dev_pas.t_pay_due t
                     where t.plan_id = pd.plan_id
                       and t.pay_due_date > pd.pay_due_date
                       and t.fee_status = '01'),
                    pp.pay_due_date) as pay_due_date, /*本次领至日期*/
                PD.LIAB_ID,
                SM.MODE_NAME || '-' || SWM.MODE_NAME AS SURVIVAL_MODE, /*生存领取形式*/
                pd.BUSI_PROD_CODE,
                BP.PRODUCT_NAME_SYS,
                CP.PRODUCT_CODE,
                PRL.PRODUCT_NAME,
                PLA.LIAB_NAME,
                SM.MODE_NAME,
                PAR.STATISTICAL_DATE AS CONFIRM_DATE, /*业务核销时间*/
                PAR.FINISH_TIME, /*收付费到账时间*/
                CD.PAYREFNO,
                CD.UNIT_NUMBER,
                PD.insert_time,
                (select 0 from dual) as obtainWaitMon,
                SDT.DEDU_AMOUNT,
               CASE WHEN SDT.ACCEPT_CODE IS NOT NULL
               THEN SDT.ACCEPT_CODE || '(' || ASS.STATUS_DESC  || ')' 
               ELSE NULL
               END AS DEDU_ACCEPT_CODE
			  FROM DEV_PAS.T_PAY_DUE PD
			  LEFT JOIN DEV_PAS.T_PAY_PLAN PP
			    ON pd.plan_id = pp.plan_id
			 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
			    ON BP.PRODUCT_CODE_SYS = pd.BUSI_PROD_CODE
			 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
			    ON CP.ITEM_ID = PD.ITEM_ID
			 INNER JOIN DEV_PDS.T_PRODUCT_LIFE PRL
			    ON PRL.PRODUCT_ID = CP.PRODUCT_ID
			  LEFT JOIN DEV_PAS.T_LIABILITY PLA
			    ON PLA.LIAB_ID = PD.LIAB_ID
			  LEFT JOIN DEV_PAS.T_SURVIVAL_MODE SM
			    ON SM.MODE_CODE = PD.SURVIVAL_MODE
			  LEFT JOIN DEV_PAS.T_SURVIVAL_W_MODE SWM
			    ON PP.SURVIVAL_W_MODE = SWM.MODE_CODE
			  LEFT JOIN DEV_CAP.V_PREM_ARAP PAR
			    ON PAR.UNIT_NUMBER = PD.UNIT_NUMBER
			  LEFT JOIN DEV_CAP.V_CASH_DETAIL CD
			    ON PAR.UNIT_NUMBER = CD.UNIT_NUMBER
			    LEFT JOIN DEV_PAS.T_SURVIVAL_DEDUCTION_TASK SDT
        	    ON SDT.PAY_ID = PD.PAY_ID
                AND SDT.DEDU_RESULT = '1'
              LEFT JOIN DEV_PAS.T_ACCEPT_STATUS ASS
                ON SDT.ACCEPT_STATUS = ASS.ACCEPT_STATUS
			 WHERE pd.policy_code = #{POLICY_CODE}
			AND PD.BUSI_ITEM_ID = #{BUSI_ITEM_ID}
			AND PD.PLAN_ID = #{PLAN_ID}
			   and pd.fee_status = '02'
			   and exists (select 1
			          from dev_pas.t_cs_accept_change ta,
			               dev_pas.t_cs_policy_change tb,
			               dev_pas.t_cs_pay_due       tc
			         where tb.accept_id = ta.accept_id
			           and tb.policy_chg_id = tc.policy_chg_id
			           and pd.plan_id = tc.plan_id
			           and pd.pay_id = tc.pay_id
			           and ta.service_code = 'CM'
			           AND TA.ACCEPT_STATUS = '18'
			           and tc.old_new = 0
			           and ( tc.fee_status = '01' or (tc.fee_status='00' and tc.deduction_flag=1))
			           and exists (select 1
                  from dev_pas.t_cs_pay_due tpd
                 where tpd.policy_chg_id = tc.policy_chg_id
                   and tpd.old_new = 1
                   and tpd.plan_id = tc.plan_id
                   and tpd.pay_num = tc.pay_num
                   and tpd.fee_amount != tc.fee_amount)  )
	</sql>
    
    	<!-- 生存给付明细 -->
    	<!-- 查询个数操作 -->
	<select id="findPayDueInfoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[    SELECT
        COUNT(1) FROM ( ]]>
        	<include refid="findPayDueInfoSql" />
        	union
            <include refid="findCMPayDueInfoSql" />
            union
            <include refid="findCMOldPayDueInfoSql" />
		<![CDATA[ )  C where c.fee_amount != 0]]>
   </select>

	 <!-- 分页查询操作 -->
      <select id="findPayDueInfoPage" resultType="java.util.Map"
        parameterType="java.util.Map">
            SELECT 
                   C.RN,
                  C.POLICY_CODE,
                  C.PLAN_ID,
                  C.BUSI_PROD_CODE,
                  C.PRODUCT_CODE,
                  C.LIAB_ID,
                  C.LIAB_NAME,
                  C.FEE_AMOUNT,
                  C.FEE_STATUS,
                  C.REAL_PAY_DATE,/*应付日期*/
                  C.SURVIVAL_INVEST_FLAG,
                  C.UNIT_NUMBER,
                  C.PAYREFNO,/*实付号码*/
                  C.PRODUCT_NAME_SYS,
                   C.PRODUCT_NAME,
                   C.MODE_NAME,
                   C.FINISH_TIME,
                   C.PAY_DUE_DATE,
                   C.CONFIRM_DATE,
                   C.SURVIVAL_MODE,
                   c.insert_time,
                   C.obtainWaitMon,
                   C.DEDU_AMOUNT,
                   C.DEDU_ACCEPT_CODE
                  FROM
                  (SELECT ROWNUM RN, T.* from ( SELECT * 
                    FROM (
            <include refid="findPayDueInfoSql" />
            union
            <include refid="findCMPayDueInfoSql" />
            union
            <include refid="findCMOldPayDueInfoSql" />
                <![CDATA[ 
                
               	)  C  ORDER BY REAL_PAY_DATE DESC,insert_time asc ) T  ]]>
                
            <![CDATA[ WHERE ROWNUM <= #{LESS_NUM} ]]> 
            
            <![CDATA[ 
             )  C
            WHERE C.RN > #{GREATER_NUM} and c.fee_amount != 0 ]]>
      </select>
	<!-- 查询个数操作 -->
	<select id="findPayDueInfoDetailTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[    SELECT 
COUNT(1)
FROM DEV_CAP.V_PREM_ARAP PA
WHERE 1=1
]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" POLICY_CODE  != null"><![CDATA[ AND PA.POLICY_CODE = #{POLICY_CODE} ]]></if>
		<if test=" BUSI_PROD_CODE  != null"><![CDATA[ AND PA.BUSI_PROD_CODE = #{BUSI_PROD_CODE} ]]></if>
		<if test=" UNIT_NUMBER  != null"><![CDATA[ AND PA.UNIT_NUMBER = #{UNIT_NUMBER} ]]></if>
	</select>
	<!-- 分页查询操作 -->
	<select id="findPayDueInfoDetailPage" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ SELECT
         distinct
           C.VALIDATE_DATE, /*划款日期*/
           C.FEE_STATUS, /*划款结果*/
           C.FUNDS_RTN_CODE, /*划款不成功原因*/
           C.BANK_CODE, /*开户银行*/
           C.BANK_ACCOUNT, /*银行账号*/
           C.BANK_USER_NAME, /*户名*/
           C.RECEIVE_FORMALLY,
           C.DOCUMENT_NO
      FROM (SELECT ROWNUM RN,
                   B.VALIDATE_DATE,
                   B.FEE_STATUS,
                   B.FUNDS_RTN_CODE,
                   B.BANK_CODE,
                   B.BANK_ACCOUNT,
                   B.BANK_USER_NAME,
                   B.RECEIVE_FORMALLY,
                   B.DOCUMENT_NO
              FROM (SELECT NVL(CD.FINISH_TIME, PA.VALIDATE_DATE) VALIDATE_DATE,
                    SM.MODE_NAME ||'-'||SWM.MODE_NAME AS RECEIVE_FORMALLY,
                       (FS.STATUS_NAME) FEE_STATUS,
                       (BR.BANK_RET_NAME) FUNDS_RTN_CODE,
                       (TB.BANK_NAME) BANK_CODE,
                       CD.ACTUAL_BANK_ACCOUNT AS BANK_ACCOUNT,
                       CD.BANK_USER_NAME,
                       T.DOCUMENT_NO
                  FROM
                  DEV_CAP.V_Prem_Arap PA
                  LEFT JOIN DEV_CAP.V_Cash_Detail CD 
                  	ON PA.UNIT_NUMBER=CD.UNIT_NUMBER
                  	AND CD.PAY_MODE <> '99' 
                  LEFT JOIN DEV_PAS.T_FEE_STATUS FS
                    ON PA.FEE_STATUS = FS.STATUS_CODE
                  LEFT JOIN DEV_PAS.T_BANK_RET_CONF BR
                    ON BR.BANK_RET_CODE = PA.FUNDS_RTN_CODE
                  LEFT JOIN DEV_PAS.T_BANK TB
                    ON TB.BANK_CODE = CD.ACTUAL_BANK_CODE
                 LEFT JOIN DEV_PAS.V_DOCUMENT_ALL T
                    ON T.POLICY_CODE = PA.POLICY_CODE AND T.CREATE_TIME=NVL(CD.FINISH_TIME, PA.VALIDATE_DATE)
                   AND T.TEMPLATE_CODE = 'PAS_00004'
                   LEFT JOIN DEV_PAS.T_PAY_PLAN TPP
                      ON tpp.policy_code = pa.policy_code
                      AND tpp.busi_prod_code = pa.busi_prod_code
                 LEFT JOIN DEV_PAS.T_SURVIVAL_MODE SM
                      ON TPP.SURVIVAL_MODE = SM.MODE_CODE
                    LEFT JOIN DEV_PAS.T_SURVIVAL_W_MODE SWM
                      ON TPP.SURVIVAL_W_MODE = SWM.MODE_CODE
                     WHERE 1 = 1
          ]]>
        <!-- <include refid="请添加查询条件" /> -->
        <if test=" POLICY_CODE  != null"><![CDATA[ AND PA.POLICY_CODE = #{POLICY_CODE} ]]></if>
        <if test=" BUSI_PROD_CODE  != null"><![CDATA[ AND PA.BUSI_PROD_CODE = #{BUSI_PROD_CODE} ]]></if> 
        <if test=" UNIT_NUMBER  != null"><![CDATA[ AND PA.UNIT_NUMBER = #{UNIT_NUMBER} ]]></if>
        <if test=" PLAN_ID  != null"><![CDATA[ AND TPP.PLAN_ID = #{PLAN_ID} ]]></if>
        <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B) C
        WHERE C.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 密码轨迹总数查询 -->
	<select id="find_passwordPathTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[  SELECT COUNT(1)
        FROM  DEV_PAS.T_POLICY_HOLDER TT
LEFT JOIN DEV_PAS.T_INSURED_LIST DD
ON TT.POLICY_CODE = DD.POLICY_CODE
LEFT JOIN DEV_PAS.T_CS_CUSTOMER_PASSWORD DT
ON DD.LIST_ID = DT.LIST_ID
WHERE 1=1
		]]>
		<!-- 查询条件-->
		<if test=" policy_code != null and policy_code  !='' "><![CDATA[ AND TT.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	<!-- 密码轨迹分页查询 -->
	<select id="find_passwordPathPage" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[  
        	SELECT  B.* FROM (
        	SELECT ROWNUM RN,
      	TT.POLICY_CODE POLICY_CODE,
       TT.CUSTOMER_ID  CUSTOMER_ID，
       CC.CUSTOMER_NAME CUSTOMER_NAME,
       DD.CUSTOMER_ID BY_ID，
       (SELECT CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER W WHERE DD.CUSTOMER_ID = W.CUSTOMER_ID)AS BY_NAME,
       DT.SET_TIME SET_TIME，
       DT.UPDATE_TIME SET_DATE，
       DT.INSERT_BY INSERT_BY
FROM  DEV_PAS.T_POLICY_HOLDER TT
LEFT JOIN DEV_PAS.T_INSURED_LIST DD
ON TT.POLICY_CODE = DD.POLICY_CODE
LEFT JOIN DEV_PAS.T_CUSTOMER CC
ON TT.CUSTOMER_ID = CC.CUSTOMER_ID
LEFT JOIN DEV_PAS.T_CS_CUSTOMER_PASSWORD DT
ON TT.CUSTOMER_ID = DT.CUSTOMER_ID
WHERE 1=1]]>
		 <if test=" policy_code  != null and policy_code  !='' "><![CDATA[ AND TT.POLICY_CODE = #{policy_code} ]]></if> 
        <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

	<!-- 累计生息账户总数 -->
	<select id="findLiveAccountTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[  
        SELECT COUNT(1)
 	  FROM DEV_PAS.T_POLICY_ACCOUNT PT
 INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
    ON PT.POLICY_ID = CM.POLICY_ID
 WHERE (PT.ACCOUNT_TYPE = '11' OR PT.ACCOUNT_TYPE = '2')
		]]>
		<!-- 查询条件-->
		<if test=" policy_code != null and policy_code  !='' "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
	</select>
	<!-- 累计生息账户分页查询 -->
	<select id="findLiveAccountPage" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[  
        SELECT 
       B.ACCOUNT_ID,
       B.POLICY_ID, 
       B.ACCOUNT_TYPE,
       B.POLICY_ACCOUNT_STATUS, 
       B.ACCOUNT_DATE,
       B.INTEREST_CAPITAL, 
       B.BUSI_ITEM_ID,
       B.ITEM_ID,
       B.BALANCE_DATE,
       B.INTEREST_BALANCE,
       B.INTEREST_RATE,
       B.POLICY_CODE,
       B.BUSI_PROD_CODE,
       B.PRODUCT_CODE,
       B.CLOSE_REASON,
       B.DEATH_DATE
FROM(SELECT rownum RN,
     PT.ACCOUNT_ID, --账户ID
       PT.POLICY_ID, --保单ID
       PT.ACCOUNT_TYPE, --账户类型
       PT.POLICY_ACCOUNT_STATUS, --账户状态
       PT.CREATE_DATE  ACCOUNT_DATE, --账户创建日期
       PT.INTEREST_CAPITAL, --账户本息和
       PT.BUSI_ITEM_ID,
       PT.ITEM_ID,
       CBP.BUSI_PROD_CODE,
       CP.PRODUCT_ID AS PRODUCT_CODE,
       PT.BALANCE_DATE,
       PT.INTEREST_BALANCE,
       (SELECT A.FIXED_INTER_RATE_VAL FROM DEV_PDS.T_PRODUCT_INTEREST_RATE A 
       WHERE A.INTEREST_RATE_TYPE='22' and pt.balance_date>=a.effective_date and  pt.balance_date<=a.end_date
   		)AS INTEREST_RATE,
       CM.POLICY_CODE, --保单号
       (SELECT CR.CLOSE_DESC FROM DEV_PAS.T_CLOSE_REASON CR WHERE CR.CLOSE_CODE = PT.CLOSE_REASON) AS CLOSE_REASON,/*注销原因 */
       TO_CHAR(PT.DEATH_DATE,'yyyy-MM-dd') AS DEATH_DATE /*死亡日期 */
  FROM DEV_PAS.T_POLICY_ACCOUNT PT
 INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
    ON PT.POLICY_ID = CM.POLICY_ID
INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP ON CM.POLICY_ID=CBP.POLICY_ID AND
  PT.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
  INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP ON CP.Item_Id=PT.ITEM_ID
 WHERE (PT.ACCOUNT_TYPE = '11' OR PT.ACCOUNT_TYPE = '2')]]> 
		 <if test=" policy_code  != null and policy_code  !='' "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if> 
        <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	 <!-- 累计生息账户变动轨迹总数 -->
    <select id="findLiveAccountPathTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[  
        SELECT COUNT(1)
        FROM DEV_PAS.V_POL_ACC_TRANS_LIST_ALL PT
         WHERE 1=1
        ]]>
        <!-- 查询条件-->
        <if test=" account_id  != null and account_id  !='' "><![CDATA[ AND PT.ACCOUNT_ID = #{account_id} ]]></if> 
    </select>
    <!-- 累计生息账户变动轨迹查询 -->
    <select id="findLiveAccountPathPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[  
        SELECT * FROM(
        SELECT 
        ROWNUM AS RN,
        C.*
        FROM(
        SELECT
               PT.ACCOUNT_ID, --账户ID
               PT.TRANS_CODE, --变更类型
               PT.TRANS_TIME, --变更日期
               PT.TRANS_AMOUNT, --变更金额
               PT.INTEREST_RATE, --结算利率,
               PT.TRANS_AMOUNT AS INTEREST_BALANCE,--利息
               PT.ACCOUNT_BALANCE AS INTEREST_CAPITAL --账户余额
          FROM DEV_PAS.V_POL_ACC_TRANS_LIST_ALL PT
         WHERE 1=1]]> 
                 <if test=" account_id  != null and account_id  !='' "><![CDATA[ AND PT.ACCOUNT_ID = #{account_id} ]]></if> 
                 <if test=" death_date  != null and death_date  !='' "><![CDATA[ AND TO_CHAR(PT.TRANS_TIME,'yyyy-MM-dd') <= #{death_date} ]]></if> 
                <![CDATA[
            ORDER BY PT.TRANS_TIME DESC,PT.INSERT_TIME DESC) C WHERE ROWNUM <= #{LESS_NUM} ]]> 
                <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
    </select>
    
	
	<!-- 自垫信息总数 -->
	<select id="findSinceMatTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[  
       SELECT 
       COUNT(1)
        from  dev_pas.T_POLICY_ACCOUNT_STREAM pp
        INNER join dev_pas.V_POL_ACC_TRANS_LIST_ALL cc
        on pp.stream_id = cc.stream_id 
        INNER join dev_pas.T_CONTRACT_MASTER dd
        on pp.policy_id = dd.policy_id and pp.stream_pol_chg_id=cc.policy_chg_id
        where 1 =1      AND PP.ACCOUNT_TYPE='5'
		]]>
		<!-- 查询条件-->
		<if test=" policy_code != null and policy_code  !='' "><![CDATA[ AND dd.POLICY_CODE = #{policy_code}  ]]></if>
	</select>
	<!--自垫信息分页查询 -->
	<select id="findSinceMatPage" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[  
        	SELECT  B.* FROM (
		        	SELECT ROWNUM RN,
		          	  cc.trans_amount as capital_balance,
		  bp.busi_prod_code  ,
		  (select tt.product_abbr_name
		       from dev_pds.t_business_product tt
		        left join dev_pas.t_contract_busi_prod dd
		         on tt.business_prd_id = dd.busi_prd_id
		         where dd.busi_item_id = pp.busi_item_id)as busi_prod_name,
		   (select t.pay_due_date
		  from dev_pas.t_contract_extend t
		 where t.busi_item_id = pp.busi_item_id )as pay_due_date,
		  pp.regular_repay,
		 cc.trans_time,
		 cc.trans_code ,
		 dd.policy_code
		from  dev_pas.T_POLICY_ACCOUNT_STREAM pp
		INNER join dev_pas.V_POL_ACC_TRANS_LIST_ALL cc
		on pp.stream_id = cc.stream_id 
		INNER join dev_pas.T_CONTRACT_MASTER dd
		on pp.policy_id = dd.policy_id and pp.stream_pol_chg_id=cc.policy_chg_id
        inner join dev_pas.t_contract_busi_prod bp on pp.busi_item_id=bp.busi_item_id and pp.policy_id=bp.policy_id
		where 1 =1		AND PP.ACCOUNT_TYPE='5'
      	]]>
		 <if test=" policy_code  != null and policy_code  !='' "><![CDATA[   AND dd.POLICY_CODE = #{policy_code} ]]></if> 
        <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
    
    
    <!-- 总自垫期数及保费 -->
    <select id="queryCountBalancePeriodsInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[  
	   SELECT SUM(TL.TRANS_AMOUNT) AS COUNT_BALANCE, COUNT(*) AS COUNT_PERIODS
	  FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM T
	   INNER JOIN DEV_PAS.V_POL_ACC_TRANS_LIST_ALL TL 
	  ON T.STREAM_ID=TL.STREAM_ID AND T.STREAM_POL_CHG_ID=TL.POLICY_CHG_ID
      AND T.ACCOUNT_TYPE='5'  
	  WHERE T.POLICY_ID = #{policy_id}
        ]]>
    </select>
	<!-- 根据账号查询客户信息列表个数操作 -->
	<select id="PA_findCustomersByAccountTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[     SELECT COUNT(1)
           FROM  
              DEV_PAS.T_PAYER_ACCOUNT PA
              INNER JOIN DEV_PAS.T_PAYER TP
              ON PA.PAYER_ID = TP.LIST_ID
              INNER JOIN DEV_PAS.T_CUSTOMER CU
              ON  TP.CUSTOMER_ID = CU.CUSTOMER_ID
			WHERE 1=1
]]>
		<if test=" account  != null"><![CDATA[ AND  PA.ACCOUNT  = #{account} ]]></if>
	</select>
	<!-- 根据账号查询客户信息列表分页查询操作 -->
	<select id="PA_findCustomersByAccount" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ SELECT 
			B.RN,
			B.CUSTOMER_ID,
            B.CUSTOMER_NAME,
            B.CUSTOMER_BIRTHDAY,
            B.CUSTOMER_GENDER,
            B.CUSTOMER_CERT_TYPE,
            B.CUSTOMER_CERTI_CODE
			FROM (       
			  SELECT ROWNUM RN,
              CU.CUSTOMER_ID,
              CU.CUSTOMER_NAME,
              CU.CUSTOMER_BIRTHDAY,
              CU.CUSTOMER_GENDER,
              CU.CUSTOMER_CERT_TYPE,
              CU.CUSTOMER_CERTI_CODE
           FROM  
              DEV_PAS.T_PAYER_ACCOUNT PA
              INNER JOIN DEV_PAS.T_PAYER TP
              ON PA.PAYER_ID = TP.LIST_ID
              INNER JOIN DEV_PAS.T_CUSTOMER CU
              ON  TP.CUSTOMER_ID = CU.CUSTOMER_ID
			WHERE 1=1
          ]]>
		<if test=" account  != null"><![CDATA[ AND  PA.ACCOUNT  = #{account} ]]></if>
        <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="findAllCustomerSurveyUwCopy" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_ID, A.APPLY_CODE, A.POLICY_CODE, 
			A.UW_LIST_ID, A.AGENT_CODE, A.POLICY_ID, A.SURVEY_ID, 
			A.SURVEY_OBJECT FROM DEV_UW.T_CUSTOMER_SURVEY_UW_COPY A WHERE ROWNUM <=  1000  ]]>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<!-- Add BY xuhp 保全核保添加核保结论 选中某个保单查询查询告知信息时使用uwid和customerid联合查询 2015年9月14日 -->
		<if test=" customer_id != null "><![CDATA[ AND A.Customer_Id = #{customer_id} ]]></if>
		<![CDATA[ ORDER BY A.UW_LIST_ID ]]>
	</select>
    
    
    
    <!--保单补发打印-->
    <select id="getReissuedPrintTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[   
           SELECT 
             COUNT(1)
      FROM DEV_PAS.T_POLICY_REISSUE TPR --保单补发表    POLICY_ID 
      INNER JOIN
      DEV_PAS.T_CS_POLICY_CHANGE TCPC
        ON TPR.POLICY_ID = TCPC.POLICY_ID --保单变更管理表   POLICY_ID
        AND TCPC.CHANGE_ID=TPR.CHANGE_ID
      INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
        ON TCPC.CHANGE_ID = TCAC.CHANGE_ID
            WHERE 1 = 1      
            
       ]]>
        <!-- 查询条件-->
        <if test=" policy_id  != null and policy_id !='' "><![CDATA[ AND   TPR.policy_id = #{policy_id} ]]></if> 
    </select>
    <!-- 保单补发打印分页查询 -->
    <select id="getReissuedPrintPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT  
        B.RN,
        B.ACCEPT_CODE,
        B.ENDORSE_CODE,
        B.FEE_AMOUNT,
        B.UPDATE_TIME,
        B.PRINT_TIME,
        B.UPDATE_BY,
        B.REVIEW_TIME,
        B.ACCEPT_TIME,
        B.VALIDATE_TIME 
        FROM (
           SELECT 
             ROWNUM RN, 
           TPR.ACCEPT_CODE , --保全受理号1      
           TCPC.ENDORSE_CODE, --批单号2 
           TCPC.FEE_AMOUNT, --补退费金额 3
           TCPC.UPDATE_TIME, --修改日期
           TCPC.UPDATE_TIMESTAMP,--修改时间戳
           (CASE WHEN TCAC.ACCEPT_ID > 1000000000000
	       THEN
	         (SELECT MAX(P.PRINT_TIME) FROM DEV_PAS.T_POLICY_PRINT P 
	               WHERE P.POLICY_CODE = TCPC.POLICY_CODE AND P.PRINT_TYPE != '2' AND P.PRINT_ID>1000000000000)
	       ELSE
	         TCAC.UPDATE_TIME -- 打印日期   
	       END)AS PRINT_TIME, --打印日期   
           TCPC.UPDATE_BY, --修改时间     
           TCAC.REVIEW_TIME, --复核时间 4
           TCAC.ACCEPT_TIME,  --受理时间
           TCAC.VALIDATE_TIME --生效日期 5
      FROM DEV_PAS.T_POLICY_REISSUE TPR --保单补发表    POLICY_ID 
      INNER JOIN
      DEV_PAS.T_CS_POLICY_CHANGE TCPC
        ON TPR.POLICY_ID = TCPC.POLICY_ID --保单变更管理表   POLICY_ID
        AND TCPC.CHANGE_ID=TPR.CHANGE_ID
      INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE TCAC
        ON TCPC.CHANGE_ID = TCAC.CHANGE_ID
        AND TCPC.ACCEPT_ID = TCAC.ACCEPT_ID
            WHERE 1 = 1
          ]]>
         <if test=" policy_id  != null and policy_id !='' "><![CDATA[ AND   TPR.policy_id = #{policy_id} ]]></if> 
         <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
    </select>
    <!--保单补发打印 end-->
    <select id="PA_LoanInsuranceInformation" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
      
        SELECT 
          S.POLICY_ID,
           S.CAPITAL_BALANCE,
            round((
            CASE 
                WHEN S.REGULAR_REPAY=1 THEN S.CAPITAL_BALANCE+S.INTEREST_SUM
                WHEN S.REGULAR_REPAY=0 THEN  S.CAPITAL_BALANCE+S.INTEREST_BALANCE
            END
            ),2) AS INTEREST_CAPITAL,
           round(S.INTEREST_SUM,2) AS INTEREST,/*贷款利息*/
           S.LOAN_START_DATE,
           S.REPAY_DUE_DATE,
           M.POLICY_CODE,
           S.BUSI_ITEM_ID,
           S.STREAM_ID,
           CP.BUSI_PRD_ID,
           S.ACT_REPAY_DATE,
           S.REGULAR_REPAY,
           S.IS_AUTO_RENEW,/*是否自动续贷清偿*/
           (SELECT BANK_NAME  FROM DEV_PAS.T_BANK A
           WHERE A.BANK_CODE = BA.BANK_CODE) AS ACCOUNT_BANK, /*开户银行*/
           BA.BANK_ACCOUNT,/*银行帐号*/
           BA.ACCO_NAME AS BANK_USER_NAME,/*户名*/
           (select  (case when pc.service_code = 'RL' then NVL(S.INTEREST_START_DATE,PC.APPLY_TIME) else S.INTEREST_START_DATE end   ) from dev_pas.t_cs_policy_change pc,
           dev_pas.t_Cs_Policy_Account_Stream pas 
            where
             pc.policy_chg_id = pas.policy_chg_id
             and pas.operation_type = '1'
             and pas.stream_id = s.stream_id
             and pc.policy_id = s.policy_id
           )as INTEREST_START_DATE,/*贷款起息日＃60429 修改*/
            (
        select 
       (case when 
        S.Regular_Repay = '0' then '1' 
        
         when  S.Regular_Repay = '1' and  to_char(cspc.validate_time,'yyyy-MM-dd')<(SELECT CO.CONSTANTS_VALUE FROM DEV_PAS.T_CONSTANTS_INFO CO 
                  WHERE CO.CONSTANTS_KEY='LOAN_GO_ONLINE_FRONTGE' )
            then '0'
         when  S.Regular_Repay = '1' and  to_char(cspc.validate_time,'yyyy-MM-dd')>=(SELECT CO.CONSTANTS_VALUE FROM DEV_PAS.T_CONSTANTS_INFO CO 
                  WHERE CO.CONSTANTS_KEY='LOAN_GO_ONLINE_FRONTGE' )
            then '1'     
            
             end 
          )
         from dev_pas.t_cs_policy_change cspc ,
              dev_pas.t_cs_policy_account_stream cspas  
        where  cspc.policy_chg_id = cspas.policy_chg_id 
        and cspc.policy_id = M.Policy_Id 
        and cspas.stream_id = s.stream_id
        and cspas.old_new = '1' 
        and cspas.operation_type <> '0'
        and cspas.busi_item_id = s.busi_item_id
        and cspas.account_type = '4' 
        and cspc.service_code in ('RL','LN')
        and rownum = '1' 
      )AS REPAY_FLAG /*已清偿标识*/ 
      FROM 
           DEV_PAS.T_POLICY_ACCOUNT_STREAM S 
           JOIN
           DEV_PAS.T_CONTRACT_MASTER M ON S.POLICY_ID = M.POLICY_ID
           JOIN
           DEV_PAS.T_CONTRACT_BUSI_PROD CP ON S.Busi_Item_Id=CP.Busi_Item_Id
           LEFT JOIN
           DEV_PAS.T_BANK_ACCOUNT BA ON S.AUTO_ACCOUNT_ID=BA.ACCOUNT_ID
         WHERE 
           S.ACCOUNT_TYPE = '4'
   ]]>
    <if test=" policy_code  != null and policy_code  !='' "><![CDATA[ AND M.POLICY_CODE =  #{policy_code} ]]></if> 
    ORDER BY S.LOAN_START_DATE DESC,S.BUSI_ITEM_ID,S.ACT_REPAY_DATE
    </select>
<select id="PA_qryfindAllContractBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.GURNT_START_DATE, A.BUSI_PRD_ID, A.GURNT_PERIOD, A.BUSI_PROD_CODE, A.HESITATION2ACC, A.APPLY_CODE, A.IS_WAIVED, 
			A.SETTLE_METHOD, A.POLICY_ID, A.FLIGHT_NO, A.WAIVER, A.GURNT_RATE, A.MASTER_BUSI_ITEM_ID, 
			A.PAIDUP_DATE, A.EXPIRY_DATE, A.LIABILITY_STATE, A.POLICY_CODE, A.RERINSTATE_DATE, A.RENEW_DECISION, 
			A.VALIDATE_DATE, A.RENEWAL_STATE, A.ASSURERENEW_FLAG, A.APL_PERMIT, A.APPLY_DATE, 
			A.INITIAL_VALIDATE_DATE, A.RENEW, A.RENEW_TIMES, A.WAIVER_END, A.MATURITY_DATE, A.GURNT_PERD_TYPE, 
			A.BUSI_ITEM_ID, A.LAPSE_DATE, A.JOINT_LIFE_FLAG, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, 
			A.DECISION_CODE, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.INITIAL_PREM_DATE, A.DUE_LAPSE_DATE, A.WAIVER_START, A.PRD_PKG_CODE, 
			A.GURNT_RENEW_END, A.GURNT_RENEW_START FROM DEV_PAS.T_CONTRACT_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID= #{policy_id} ]]></if>
		<![CDATA[ORDER BY A.MASTER_BUSI_ITEM_ID DESC,A.VALIDATE_DATE DESC]]>
	</select>
	
	<select id="findAllLoanPolicyCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REMARK, A.ACCEPT_NO, A.LOAN_FLAG, A.MAX_LOAN_RATIO, A.ORGAN_CODE, 
			A.CFG_ID, A.LOAN_STATUS, A.POLICY_ID,A.POLICY_CODE FROM DEV_PAS.T_LOAN_POLICY_CFG A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" loan_status != null and loan_status != ''  "><![CDATA[ AND A.LOAN_STATUS = #{loan_status} ]]></if>
		<if test=" busi_item_id  != null and  busi_item_id !='' "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" loan_flag  != null "><![CDATA[ AND A.LOAN_FLAG = #{loan_flag} ]]></if>
		<![CDATA[ ORDER BY A.CFG_ID ]]> 
	</select>
	
	<select id="findAllLoanPolicyRateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TIME_PERIDO_CODE, A.CFG_ID, A.LOAN_RATE, 
			A.RATE_CFG_ID,A.UPDATE_TIME FROM DEV_PAS.T_LOAN_POLICY_RATE_CFG A WHERE ROWNUM <=  1000  ]]>
		<if test=" time_perido_code  != null and time_perido_code != ''  "><![CDATA[ AND A.TIME_PERIDO_CODE = #{time_perido_code} ]]></if>
		<if test=" cfg_id  != null and cfg_id != '' "><![CDATA[ AND A.CFG_ID = #{cfg_id} ]]></if>
		<if test=" loan_rate  != null and loan_rate != ''  "><![CDATA[ AND A.LOAN_RATE = #{loan_rate} ]]></if>
		<if test=" rate_cfg_id  != null and rate_cfg_id != '' "><![CDATA[ AND A.RATE_CFG_ID = #{rate_cfg_id} ]]></if>
		<![CDATA[ ORDER BY A.RATE_CFG_ID ]]> 
	</select>
	<select id="PA_findAllContractProduct" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF FROM DEV_PAS.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</select>
	<select id="findAllLoanBusiCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_PROD_ID, A.MIN_PAID_PREM, A.EXPIRED_DATE, A.LOAN_FLAG, A.MAX_LOAN_RATIO, 
			A.ORGAN_CODE, trim(A.CHANNEL_TYPE) as CHANNEL_TYPE, A.MAX_PAID_PREM, A.CHARGE_TYPE, A.CFG_ID, 
			A.VALID_DATE, A.PERIOD_TYPE ,A.IS_CAREFULLY_CHOSEN FROM DEV_PAS.T_LOAN_BUSI_CFG A WHERE ROWNUM <=  1000  ]]>
		<if test=" business_prod_id  != null "><![CDATA[ AND A.BUSINESS_PROD_ID = #{business_prod_id} ]]></if>
		<if test=" charge_type != null and charge_type != ''  "><![CDATA[ AND A.charge_type = #{charge_type} ]]></if>
		<if test=" period_type != null and period_type != ''  "><![CDATA[ AND A.PERIOD_TYPE = #{period_type} ]]></if>
		<if test=" loan_flag  != null "><![CDATA[ AND A.LOAN_FLAG = #{loan_flag} ]]></if>
		<![CDATA[ AND A.EXPIRED_DATE >= TRUNC(SYSDATE)]]>
		<![CDATA[ ORDER BY A.CFG_ID ]]> 
	</select>
	<select id="findAllLoanBusiRateCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.TIME_PERIDO_CODE, A.LOAN_RATE
			  FROM DEV_PAS.T_POLICY_ACCOUNT_STREAM_RATE A
			 WHERE A.STREAM_ID = #{stream_id}
		]]> 
	</select>
	
	
	
	<select id="findAllFreeLookPeriodCfgSelectByValidate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_PROD_ID, A.POLICY_HOLDER_GENDER, A.VISIT_FLAG, A.FREE_LOOK_PERID, A.CFG_VALIDATE_TIME, 
			A.ORGAN_CODE, A.CHANNEL_TYPE, A.INSURED_GENDER, A.INCLUDE_NONWORKDAYS_FLAG, A.INSURED_AGE, 
			A.CFG_ID, A.POLICY_HOLDER_AGE, A.BANK_NEWDEAL_FLAG FROM DEV_PAS.T_FREE_LOOK_PERIOD_CFG A WHERE ROWNUM <=  1000
			  AND A.CFG_VALIDATE_TIME<=#{cfg_validate_time,jdbcType=DATE} and A.INSURED_AGE<=#{insured_age, jdbcType=NUMERIC} and A.POLICY_HOLDER_AGE<=#{policy_holder_age, jdbcType=NUMERIC}]]>
		<include refid="freeLookPeriodCfgWhereConditionExceptValidate" />
		<![CDATA[ ORDER BY A.CFG_VALIDATE_TIME DESC]]> 
	</select>
	
	<sql id="freeLookPeriodCfgWhereConditionExceptValidate">
		<if test=" business_prod_id  != null "><![CDATA[ AND A.BUSINESS_PROD_ID = #{business_prod_id, jdbcType=NUMERIC} ]]></if>
		<if test=" policy_holder_gender  != null "><![CDATA[ AND A.POLICY_HOLDER_GENDER = #{policy_holder_gender, jdbcType=NUMERIC} ]]></if>
		<if test=" visit_flag  != null "><![CDATA[ AND A.VISIT_FLAG = #{visit_flag, jdbcType=NUMERIC} ]]></if>
		<if test=" free_look_perid  != null "><![CDATA[ AND A.FREE_LOOK_PERID = #{free_look_perid, jdbcType=NUMERIC} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ]]></if>
		<if test=" insured_gender != null and insured_gender != ''  "><![CDATA[ AND A.INSURED_GENDER = #{insured_gender, jdbcType=NUMERIC} ]]></if>
		<if test=" include_nonworkdays_flag  != null "><![CDATA[ AND A.INCLUDE_NONWORKDAYS_FLAG = #{include_nonworkdays_flag, jdbcType=NUMERIC} ]]></if>
		<if test=" cfg_id  != null "><![CDATA[ AND A.CFG_ID = #{cfg_id, jdbcType=NUMERIC} ]]></if>
		<if test=" bank_newdeal_flag  != null "><![CDATA[ AND A.BANK_NEWDEAL_FLAG = #{bank_newdeal_flag, jdbcType=NUMERIC} ]]></if>
	</sql>
	
	<select id="findAllVacationDays" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_UDMP_WORKDAYS A
		 WHERE A.ORGAN_ID=#{organ_id}  AND A.ESPECIAL_DAY>=#{start_time}  
		 AND  A.ESPECIAL_DAY<=#{end_time}]]>
	</select>
	
	<!-- 查找单条数据 -->	
	<select id="findCsApplication" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLICANT_TYPE, A.TRY_CALC_NO,  A.APPLY_CODE, A.ORGAN_CODE, 
			 A.CHANGE_ID, A.APPLY_NAME, A.TO_UW_DATE, A.AGENT_NAME , 
			  A.APP_STATUS,  A.NOFILL_FLAG, A.AGENT_CERTI_TYPE , 
			A.APPLY_TIME, A.CUSTOMER_ID, A.FINISH_TIME , A.SOURCE_TYPE, 
			A.AGENT_LEVEL, A.SERVICE_TYPE, A.AGENT_CERTI_CODE,  A.EVENT_CODE,
			 A.AGENT_TEL, A.AGENT_CODE , A.TO_APPROVE_DATE , A.ESLEWHERE_FLAG ,A.IS_AUTO_INPUT FROM dev_pas.T_CS_APPLICATION A WHERE ROWNUM <=  1000  ]]>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<![CDATA[ ORDER BY A.CHANGE_ID ]]>
	</select>
	<select id="findAllCsAcceptChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, A.IS_SAVED,
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.INSERT_BY,A.INSERT_TIME,A.IS_SIGN_FLAG,A.SIGN_NO_LIST,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE FROM DEV_PAS.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<![CDATA[ ORDER BY A.ACCEPT_ID ]]> 
	</select>
	<!-- 保全受理号信息 start -->
	<select id="queryAllCsInfoQueryAccpet" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			SELECT
				 (SELECT CA.APPLY_CODE FROM DEV_PAS.T_CS_APPLICATION CA WHERE CA.CHANGE_ID = PC.CHANGE_ID) APPLY_CODE,   
				AC.ACCEPT_CODE,AC.ACCEPT_ID,AC.CHANGE_ID,
                (SELECT S.SERVICE_NAME FROM DEV_PAS.T_SERVICE S WHERE S.SERVICE_CODE = AC.SERVICE_CODE) SERVICE_NAME,
                (SELECT TAS.STATUS_DESC FROM DEV_PAS.T_ACCEPT_STATUS TAS WHERE TAS.ACCEPT_STATUS = AC.ACCEPT_STATUS) ACCEPT_STATUS,
                M.POLICY_CODE,
               (SELECT S.STATUS_NAME FROM DEV_PAS.T_LIABILITY_STATUS S WHERE S.STATUS_CODE = M.LIABILITY_STATE) LIABILITYSTATE,
               M.VALIDATE_DATE,
               NVL((SELECT C.CUSTOMER_NAME FROM DEV_PAS.T_CS_POLICY_HOLDER H,DEV_PAS.T_CUSTOMER C WHERE H.CHANGE_ID = PC.CHANGE_ID AND H.POLICY_ID = PC.POLICY_ID AND H.POLICY_CHG_ID = PC.POLICY_CHG_ID AND H.OLD_NEW = '1'
               AND H.CUSTOMER_ID = C.CUSTOMER_ID),(SELECT C1.CUSTOMER_NAME FROM DEV_PAS.T_POLICY_HOLDER H1,DEV_PAS.T_CUSTOMER C1 WHERE H1.POLICY_ID = PC.POLICY_ID AND C1.CUSTOMER_ID = H1.CUSTOMER_ID)) AS CUSTOMER_NAME 
           FROM  DEV_PAS.T_CS_ACCEPT_CHANGE AC ,DEV_PAS.T_CS_POLICY_CHANGE PC ,DEV_PAS.T_CS_CONTRACT_MASTER M  WHERE AC.CHANGE_ID = PC.CHANGE_ID AND AC.ACCEPT_ID = PC.ACCEPT_ID 
           AND PC.POLICY_CODE = M.POLICY_CODE AND PC.POLICY_CHG_ID=M.POLICY_CHG_ID AND PC.CHANGE_ID = M.CHANGE_ID   AND M.OLD_NEW='1'
		]]>
		<if test=" change_id  != null "><![CDATA[ AND  AC.CHANGE_ID = #{change_id} ]]></if>
	</select>
	<!-- 保全受理号信息 end -->
	<!-- 查询不退费信息 start -->
	<select id="queryAllCsInfoQueryBackMoney" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			SELECT T.POLICY_CODE,(SELECT S.SERVICE_NAME FROM DEV_PAS.T_SERVICE S WHERE S.SERVICE_CODE = CAC.SERVICE_CODE) SERVICE_NAME,
				   T.FEE_AMOUNT,(SELECT A.NAME FROM DEV_PAS.T_AR_AP A WHERE A.AR_AP = T.ARAP_FLAG) ARAP_FLAG,
				    (SELECT M.NAME FROM DEV_PAS.T_PAY_MODE M WHERE M.CODE = T.PAY_MODE) PAY_MODE,
					T.BANK_ACCOUNT,
					T.BANK_USER_NAME,
					(SELECT FS.STATUS_NAME FROM DEV_PAS.T_FEE_STATUS FS WHERE FS.STATUS_CODE = T.FEE_STATUS) STATUS_NAME
		  	FROM DEV_PAS.v_prem_arap_all T LEFT JOIN  DEV_PAS.T_CS_ACCEPT_CHANGE CAC
		   		ON (CAC.ACCEPT_CODE = T.BUSINESS_CODE)
		   WHERE  T.DERIV_TYPE = '004' 
		]]>
			<if test=" change_id  != null "><![CDATA[ AND CAC.CHANGE_ID = #{change_id} ]]></if>
	</select>
	<!-- 查询不退费信息 end -->
	<!--根据客户号查询客户信息 -->
	<select id="findCustomerInfoById" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[   
		       SELECT CU.CUSTOMER_ID,CU.CUSTOMER_NAME,
CU.CUSTOMER_GENDER,CU.CUSTOMER_BIRTHDAY,
CU.CUSTOMER_CERT_TYPE,CU.CUSTOMER_CERTI_CODE
 FROM DEV_PAS.T_CUSTOMER CU 
 WHERE CU.CUSTOMER_ID = #{customer_id} 	
        	
       ]]>
       </select>
       <!--根据代理人号查询代理人信息 -->
       <select id="findAgentByAgentCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMPLOYEE_FLAG, A.AGENT_MOBILE, A.POSTAL_ADDRESS, A.CERTI_CODE, A.AGENT_GENDER, 
			A.AGENT_LEVEL, A.AGENT_ORGAN_CODE, A.AGENT_STATUS, A.SALES_ORGAN_CODE, A.AGENT_NORMAL_TYPE, A.DISMISSAL_DATE, A.BIRTHDAY, 
			A.SGENT_HORNER_LEVEL, A.AGENT_NAME, A.AGENT_PHONE, A.AGENT_EMAIL, A.AGENT_CHANNEL, 
			A.CERT_TYPE, A.HOME_ADDRESS, A.AGENT_CODE, A.EMPLOYMENT_DATE,SO.BRANCH_ATTR,
			(SELECT TAA.CODE || '-' || TAA.NAME  FROM DEV_PAS.T_AGENT_ATTRIBUTE TAA WHERE TAA.CODE = A.AGENT_ATTRIBUTE) AS AGENT_ATTRIBUTE
			FROM DEV_PAS.T_AGENT A 
            LEFT JOIN DEV_NB.T_SALES_ORGAN SO ON SO.SALES_ORGAN_CODE=A.SALES_ORGAN_CODE
            WHERE 1 = 1  ]]>
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</select>
	           <!--根据代理人号查询代理人证件号 -->
    <select id="COMM_findLicByAgentCodeCard" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.AGENT_CODE,A.LICENSE_NO,A.LICENSE_TYPE FROM DEV_NB.T_AGENT_LICENSE A WHERE 1 = 1  ]]>
        <if test=" agent_code != null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
    </select>
     <select id="QRY_findPolicySimpleInfoByCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT
        CM.POLICY_ID,
       CM.POLICY_CODE,
      CM.APPLY_CODE,
        CM.SUBMIT_CHANNEL,
       CM.LIABILITY_STATE,
       CM.END_CAUSE,
          (CASE WHEN ( CM.DOUBLE_MAINRISK_FLAG ='1' AND CM.RELATION_POLICY_CODE IS NULL AND NCM.RELATION_POLICY_CODE IS NULL) THEN 
         (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER NCM WHERE NCM.RELATION_POLICY_CODE= #{policy_code}
         	AND NCM.DOUBLE_MAINRISK_FLAG = '1'
         )
         ELSE
           CM.APPLY_CODE
       END 
       ) AS RELATIOIN_APPLY_CODE,
       (CASE WHEN ( CM.DOUBLE_MAINRISK_FLAG ='1' AND CM.RELATION_POLICY_CODE IS NULL AND NCM.RELATION_POLICY_CODE IS NULL) THEN 
         (SELECT POLICY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER NCM WHERE NCM.RELATION_POLICY_CODE= #{policy_code}
         	AND NCM.DOUBLE_MAINRISK_FLAG = '1'
         )
         ELSE
           CM.POLICY_CODE
       END 
       ) AS RELATIOIN_POLICY_CODE
         FROM DEV_PAS.T_CONTRACT_MASTER CM 
         LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON CM.POLICY_CODE = NCM.POLICY_CODE 
         WHERE 1 = 1  ]]>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
    </select>
    <!--根据受理号查询保单信息  -->
    <select id = "QRY_findCSAcceptCode" resultType = "java.util.Map" parameterType = "java.util.Map">
    	SELECT AC.ACCEPT_CODE, AC.ACCEPT_ID
			  FROM DEV_PAS.T_CS_ACCEPT_CHANGE AC
			 WHERE AC.ACCEPT_CODE = #{accept_code}
    </select>
    <!--根据赔案号查询理赔信息  -->
    <select id = "QRY_findClmCaseNo" resultType = "java.util.Map" parameterType = "java.util.Map">
    	 <![CDATA[
    	SELECT DISTINCT B.*
    FROM (SELECT DISTINCT (CC.CASE_ID),
                          CA.ACCIDENT_NO,
                          CC.ORGAN_CODE,
                          TO_CHAR(CC.END_CASE_TIME, 'yyyy-MM-dd') END_CASE_TIME,
                          CC.CASE_NO,
                          CC.CASE_STATUS,
                          (CASE (SELECT COUNT(1)
                               FROM DEV_CLM.T_CLAIM_UW UW
                              WHERE UW.CASE_NO = CC.CASE_NO)
                            WHEN 0 THEN
                             '否'
                            ELSE
                             '是'
                          END) UW_STATUS,
                          (CASE (SELECT COUNT(1)
                               FROM DEV_CLM.T_SURVEY_APPLY SA
                              WHERE SA.CASE_NO = CC.CASE_NO)
                            WHEN 0 THEN
                             '否'
                            ELSE
                             '是'
                          END) CHAKAN,
                          CA.INSURED_ID,
                          CM.CUSTOMER_NAME,
                          CM.CUSTOMER_CERTI_CODE,
                          CC.GREEN_FLAG,
                          CC.RPTR_TIME,
                          CC.REGISTE_TIME,
                          CC.ACTUAL_PAY,
                          to_char(NVL(NVL(NVL(NVL(NVL(NVL(NVL(END_CASE_TIME,
                                                      APPROVE_TIME),
                                                  AUDIT_TIME),
                                              REGISTE_CONF_TIME),
                                          SIGN_TIME),
                                      DOOR_SIGN_TIME),
                                  ACCEPT_TIME),
                              RPTR_TIME),'yyyy-MM-dd') AS STATUS_TIME,
                          CC.AUDIT_DECISION
            FROM DEV_CLM.T_CLAIM_CASE CC
            LEFT JOIN DEV_CLM.T_CLAIM_ACCIDENT CA
              ON CC.ACCIDENT_ID = CA.ACCIDENT_ID
            LEFT JOIN (SELECT CP.POLICY_CODE, CP.CASE_ID
                        FROM DEV_CLM.T_CLAIM_POLICY CP
                      UNION
                      SELECT CCM.POLICY_CODE, CCM.CASE_ID
                        FROM DEV_CLM.T_CONTRACT_MASTER CCM) BPP
              ON BPP.CASE_ID = CC.CASE_ID
            LEFT JOIN DEV_CLM.T_CLAIM_SUB_CASE TC
              ON CC.CASE_ID = TC.CASE_ID
            LEFT JOIN DEV_CLM.T_CUSTOMER CM
              ON CC.INSURED_ID = CM.CUSTOMER_ID
           WHERE 1 = 1
             AND CC.CASE_STATUS != '99'
             AND CC.Case_No = #{caseNo}          
             ) B
        ]]>  
    </select>
    
    <select id="queryInfoBySessionId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[SELECT UM.APPLY_CODE,UM.POLICY_CODE,UM.SESSIONID FROM DEV_UW.T_UW_MAGNUM_LOG UM WHERE 1 = 1  ]]>
        <if test=" sessionid != null and sessionid != ''  "><![CDATA[ AND UM.SESSIONID = #{sessionid} ]]></if>
    </select>
    <!-- 查询保单补发次数 -->
    <select id="findPolicyReissue" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT
                  COUNT(*) AS REISSUE
                FROM DEV_PAS.T_POLICY_REISSUE PR
                INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC ON PR.CHANGE_ID=AC.CHANGE_ID AND PR.ACCEPT_CODE=AC.ACCEPT_CODE
                WHERE AC.ACCEPT_STATUS='18' 
                 AND  PR.POLICY_ID= #{policy_id}
		]]>
	</select>
    <!-- 145832查询续期年金生存调查确认生存信息 -->
    <select id="getSurvInvCfmInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	select t.customer_name,t.customer_id,t.CUSTOMER_CERTI_CODE,t.SURVIVAL_CONFIRM_DATE,t.UPDATE_TIME,(select u.real_name||'（'|| u.user_name ||'）' from dev_pas.T_UDMP_USER u where u.user_id = t.update_by) confirm_user,t.CONFIRM_CHANNEL,c.CONFRIM_CHANNEL_NAME,t.PHOTO_CONPARE_ID
			  from dev_pas.T_CUS_SURVIVAL_CONFIRM t
			  join dev_pas.T_SURVIVAL_CONFRIM_CHANNEL c
			    on to_number(c.CONFRIM_CHANNEL_CODE) = t.CONFIRM_CHANNEL
			 where 1=1
			   and t.customer_id = #{customer_id}
			 order by t.UPDATE_TIME desc   ]]>
         
    </select>
    <!-- 145832查询生存调查验真信息 -->
    <select id="getIdentityCheckInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	select p.customer_name,p.customer_id_code,f.flag_desc check_type_name,p.name_check_result,p.id_check_result,m.check_date,p.judge_result,p.similarity*100 similarity
			  from dev_pas.T_PHOTO_COMPARE_CHECK p
			  join dev_pas.T_IDENTITY_CHECK_MAIN m
			    on m.identity_detail_id = p.photo_compare_id
			  join dev_pas.t_cs_identity_check_flag f
			    on f.flag_code = m.check_type
			 where p.photo_compare_id = #{photo_conpare_id} ]]>
         
    </select>
    
     <!-- 查询保单销售人员和保单服务人员 -->
    <select id="findPolicyAgent" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
          SELECT 
                  CA.AGENT_CODE,
                  A.AGENT_NAME,
                  A.AGENT_ORGAN_CODE,
                  1 AS IS_CURRENT_AGENT,
                  0 AS IS_NB_AGENT,
                  CA.CHANNEL_TYPE AS AGENT_CHANNEL, 
			      A.MANAGE_COM_OUTER,/*48816中介机构代码*/
			      /* NCM.OTHER_MANAGE_NAME,48816中介机构名称*/
			      MP.PROTOCOL_ID,/*48816协议号*/
                  DECODE(NCM.SUBMIT_CHANNEL,'5',CA.COOPERATION_CODE,TCM.OTHER_MANAGE_COM) OTHER_MANAGE_COM,--52031需求修改 中介机构代码
                  DECODE(NCM.SUBMIT_CHANNEL,'5',CA.COOPERATION_NAME,TCM.OTHER_MANAGE_NAME) OTHER_MANAGE_NAME--52031需求修改 中介机构名称
                FROM DEV_PAS.T_CONTRACT_AGENT CA
                INNER JOIN DEV_PAS.T_AGENT A
                ON CA.AGENT_CODE = A.AGENT_CODE
                LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON CA.POLICY_CODE = NCM.POLICY_CODE --52031需求增加
                LEFT JOIN DEV_NB.T_NCL_COM_MAPPING TCM ON  A.MANAGE_COM_OUTER=TCM.OTHER_MANAGE_COM --52031需求增加
                /*48816增加start*/
				LEFT JOIN DEV_NB.T_NCL_COM_MAPPING TCM ON  A.MANAGE_COM_OUTER=TCM.OTHER_MANAGE_COM
				LEFT JOIN DEV_NB.T_AGENT_MEDIATION_PROTOCOL MP ON TCM.NCL_MANAGE_COM=MP.MANAGE_COM
				AND MP.SIGN_DATE IS NOT NULL
				/*48816增加end*/
                WHERE 
                CA.POLICY_CODE = #{policy_code}  AND CA.IS_CURRENT_AGENT=1
          union
               SELECT 
                  CA.AGENT_CODE,
                  A.AGENT_NAME,
                  A.AGENT_ORGAN_CODE,
                  0 AS IS_CURRENT_AGENT,
                  1 AS IS_NB_AGENT,
                  CA.CHANNEL_TYPE AS AGENT_CHANNEL,
                  A.MANAGE_COM_OUTER,/* 48816中介机构代码*/
			      /*NCM.OTHER_MANAGE_NAME, 48816中介机构名称*/
			      MP.PROTOCOL_ID,/*48816协议号 */
                  DECODE(NCM.SUBMIT_CHANNEL,'5',CA.COOPERATION_CODE,TCM.OTHER_MANAGE_COM) OTHER_MANAGE_COM,--52031需求修改 中介机构代码
                  DECODE(NCM.SUBMIT_CHANNEL,'5',CA.COOPERATION_NAME,TCM.OTHER_MANAGE_NAME) OTHER_MANAGE_NAME--52031需求修改 中介机构名称
                FROM DEV_PAS.T_CONTRACT_AGENT CA
                INNER JOIN DEV_PAS.T_AGENT A
                ON CA.AGENT_CODE = A.AGENT_CODE
                LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON CA.POLICY_CODE = NCM.POLICY_CODE --52031需求增加
                LEFT JOIN DEV_NB.T_NCL_COM_MAPPING TCM ON  A.MANAGE_COM_OUTER=TCM.OTHER_MANAGE_COM --52031需求增加
               	/*48816增加start*/
				LEFT JOIN DEV_NB.T_NCL_COM_MAPPING TCM ON  A.MANAGE_COM_OUTER=TCM.OTHER_MANAGE_COM
				LEFT JOIN DEV_NB.T_AGENT_MEDIATION_PROTOCOL MP ON TCM.NCL_MANAGE_COM=MP.MANAGE_COM
				AND MP.SIGN_DATE IS NOT NULL
				/*48816增加end*/
                WHERE 
                CA.POLICY_CODE = #{policy_code}  AND CA.IS_NB_AGENT=1
        ]]>
    </select>
    
     <!--   查询服务人员变更轨迹 -->
     <select id="findServiceByPolicyCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT
          CA.POLICY_CODE,
          CA.AGENT_CHANNEL,/*展业类型*/
          CA.AGENT_CODE,/*代理人编码*/
          CA.AGENT_CODE_NEW,/*新代理人编码*/
          AG.AGENT_NAME,/*姓名*/
          AG.AGENT_GENDER,/*性别*/
          AG.SALES_ORGAN_CODE AGENT_ORGAN_CODE,/*代理人展业机构代码*/
		  (SELECT SLC.SALES_ORGAN_NAME FROM DEV_PAS.T_SALES_ORGAN SLC WHERE SLC.SALES_ORGAN_CODE = AG.SALES_ORGAN_CODE) AGENT_ORGAN_NAME,/*代理人展业机构名称*/
          AG.AGENT_MOBILE,/*手机*/
          AG.CERT_TYPE,/*证件类型*/
          AG.CERTI_CODE,/*证件号码*/
          CA.AGENT_START_DATE,/*服务起始日期*/
          CA.AGENT_END_DATE /*服务终止日期*/
        FROM (
             SELECT
             CA.POLICY_CODE,
             CA.AGENT_CODE,
             CA.AGENT_CHANNEL,
             CA.AGENT_CODE AS AGENT_CODE_NEW,
              CA_NEW.AGENT_START_DATE,
              CA_NEW.AGENT_END_DATE,
              CA_NEW.INSERT_TIME
              FROM
              ( SELECT
              CA.AGENT_CODE,/*代理人编码*/
              AG.AGENT_CHANNEL,
              CA.POLICY_CODE,
              (SELECT
              MIN(CA.LIST_ID) FROM DEV_PAS.T_contract_agent CAI
               WHERE CAI.INSERT_TIME>=CA.INSERT_TIME  AND CAI.POLICY_CODE=CA.POLICY_CODE
               ) AS LIST_ID,
               CA.INSERT_TIME
               FROM DEV_PAS.T_contract_agent CA
              INNER JOIN DEV_PAS.T_AGENT AG ON CA.AGENT_CODE=AG.AGENT_CODE
            WHERE CA.POLICY_CODE= #{policy_code}  
             ) CA 
             INNER JOIN DEV_PAS.T_contract_agent CA_NEW  
             ON CA.POLICY_CODE=CA_NEW.POLICY_CODE 
             AND CA_NEW.LIST_ID=CA.LIST_ID
        ) CA
        INNER JOIN DEV_PAS.T_AGENT AG 
        ON AG.AGENT_CODE=CA.AGENT_CODE_NEW 
        ORDER BY CA.AGENT_START_DATE 
        ]]>
    </select>
    <!-- 查询给付责任信息 -->
	<select id="queryPayPlanInfo" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[
		 	SELECT A.PLAN_ID,A.POLICY_ID,A.ITEM_ID,A.PAY_DUE_DATE,A.PAY_NUM,A.BEGIN_DATE,A.END_DATE,A.PAY_STATUS,A.PLAN_FREQ,A.TOTAL_AMOUNT,
		 	A.INSTALMENT_AMOUNT,A.MONEY_CODE,A.PAY_PERIOD,A.PAY_YEAR,A.END_PERIOD,A.END_YEAR,A.SURVIVAL_MODE,A.SURVIVAL_W_MODE,A.PAY_TYPE,
		 	A.INSTALMENT_PROPORTION,A.POLICY_CODE,A.BUSI_ITEM_ID,A.BUSI_PROD_CODE,A.PRODUCT_CODE,A.SURVIVAL_INVEST_FLAG,A.SURVIVAL_INVEST_RESULT,
		 	A.LIAB_ID,A.LIAB_NAME,A.PAY_PLAN_TYPE,A.GURNT_PAY_LIAB,A.GURNT_PAY_PERIOD,A.ONE_TIME_FLAG,A.BENE_AMOUNT,A.LIAB_CODE
			  FROM APP___PAS__DBUSER.T_PAY_PLAN A
			 WHERE 1 = 1 AND ROWNUM<=1
		 ]]>
		 <if test=" pay_due_date  != null and pay_due_date !='' "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
		 <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		 <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		 <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>		 
		 <if test=" pay_plan_type != null and pay_plan_type != ''  "><![CDATA[ AND A.PAY_PLAN_TYPE = #{pay_plan_type} ]]></if>
		 <if test=" gurnt_pay_liab  != null "><![CDATA[ AND (A.GURNT_PAY_LIAB = #{gurnt_pay_liab}  OR A.GUARANTEE_PERIOD_TYPE = 'A' OR  A.GUARANTEE_PERIOD_TYPE = 'Y' OR A.GUARANTEE_PERIOD_TYPE = 'W' )]]></if>
		 <if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
	</select>
	<!--#45810 新核心业务系统青岛上线优化需求——清单、查询、承保功能优化（1） -->
	 <select id="findPolicyQtStatus" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[  select qs.status_name,qtask.qa_type
          from dev_nb.t_nb_qt_task qtask, dev_nb.t_qt_status qs
         where qtask.qa_type in ('4', '5','6')
           and qs.status_code = qtask.qt_status
           and qtask.apply_code =  #{apply_code}
        ]]>
    </select>
      <!--#45810 新核心业务系统青岛上线优化需求——清单、查询、承保功能优化（1） end -->
    <!-- 查询关联附加险保单号  缺陷11115查询修改-->
    <select id="findPolicyAccessoryByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
    	SELECT listagg(CR.SUB_POLICY_CODE,',') WITHIN GROUP (ORDER BY  CR.MASTER_POLICY_CODE) AS SUB_POLICY_CODE /*关联附加险保单号*/ 
    			FROM DEV_PAS.T_CONTRACT_RELATION CR
		 WHERE CR.MASTER_POLICY_CODE = #{policy_code}
    </select>
    
    
     <select id="findSociSecubyCode" resultType="java.lang.Integer" parameterType="java.util.Map">
    	<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_NB_CONTRACT_MASTER CM,DEV_NB.T_NB_CONTRACT_MEDICAL CML
			       WHERE CM.APPLY_CODE=CML.APPLY_CODE  AND CML.MEDICAL_PAY_ORDER=2
			       AND CM.PROPOSAL_STATUS in ('10','14')
		]]> 
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CM.APPLY_CODE = #{apply_code} ]]></if>
    </select>
    
    <select id="findMedicalInsuranceInfoByCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[  SELECT TA.MEDICAL_PAY_ORDER AS MEDICAL_PAY_ORDER,
                          (SELECT TMT.PAY_ORDER_NAME FROM DEV_PAS.T_MEDICAL_PAY_ORDER_TYPE TMT WHERE TA.MEDICAL_PAY_ORDER = TMT.PAY_ORDER_CODE) AS MEDICAL_PAY_ORDER_NAME,
                          TA.MEDICAL_NO AS MEDICAL_NO,
                          TA.ACCOUNT_BANK AS ACCOUNT_BANK,
                          (SELECT TB.BANK_NAME FROM DEV_PAS.T_BANK TB WHERE TA.ACCOUNT_BANK = TB.BANK_CODE) AS ACCOUNT_BANK_NAME,
                          TA.ACCOUNT_NAME AS ACCOUNT_NAME,
                          TA.ACCOUNT AS ACCOUNT_ID,
                          TA.MEDICAL_PAY_ORDER_NEXT AS MEDICAL_PAY_ORDER_NEXT,
                          (SELECT TMT.PAY_ORDER_NAME FROM DEV_PAS.T_MEDICAL_PAY_ORDER_TYPE TMT WHERE TA.MEDICAL_PAY_ORDER_NEXT = TMT.PAY_ORDER_CODE) AS MEDICAL_PAY_ORDER_NAME_NEXT,
                          TA.MEDICAL_NO_NEXT AS MEDICAL_NO_NEXT,
                          TA.NEXT_ACCOUNT_BANK AS NEXT_ACCOUNT_BANK,
                          (SELECT TB.BANK_NAME FROM DEV_PAS.T_BANK TB WHERE TA.NEXT_ACCOUNT_BANK = TB.BANK_CODE ) AS NEXT_ACCOUNT_BANK_NAME,
                          TA.NEXT_ACCOUNT_NAME AS NEXT_ACCOUNT_NAME,
                          TA.NEXT_ACCOUNT AS NEXT_ACCOUNT_ID
                     FROM DEV_PAS.T_PAYER_ACCOUNT TA,DEV_PAS.T_CONTRACT_MASTER TM
                    WHERE TM.POLICY_ID = TA.POLICY_ID
                      AND TA.PAY_MODE = '18'
                      AND (TA.MEDICAL_PAY_ORDER = '1' 
                       OR  TA.MEDICAL_PAY_ORDER_NEXT = '1')
                      AND TM.ORGAN_CODE LIKE '8622%'
                      AND TM.POLICY_CODE =  #{policy_code}
        ]]>
    </select>
    <!-- 查询客户电话变更轨迹 总数-->
	<select id="find_customerPhoneChangePathTotal" resultType="java.lang.Integer"  parameterType="java.util.Map">
		<![CDATA[
		 	SELECT COUNT(0) FROM (SELECT
		                CCTCD.CUSTOMER_ID,
		                CCTCD.CUSTOMER_NAME,
		                CCTCD.TEL_BEFOR,
		                CCTCD.TEL_AFTER,
		                CCTCD.UPDATE_TIME,
		                CCTCD.IMG_PATH
		          FROM DEV_PAS.T_CS_CUST_TEL_CHG_DETAIL CCTCD
		         WHERE CCTCD.POLICY_CODE = #{policy_code})
		 ]]>
	</select>
    <!-- 查询客户电话变更轨迹 -->
	<select id="find_customerPhoneChangePath" resultType="java.util.Map"  parameterType="java.util.Map">
		<![CDATA[
		SELECT SSS.*
		  FROM (SELECT ROWNUM RN,
		               CCTCD.CUSTOMER_ID,/*客户号*/
		               CCTCD.CUSTOMER_NAME,/*导入模板录入的客户名称*/
		               CCTCD.TEL_BEFOR,/*变更前电话*/
		               CCTCD.TEL_AFTER,/*变更后电话*/
		               CCTCD.UPDATE_TIME,/*变更时间*/
		               CCTCD.IMG_PATH /*纸质影像资料存储路径*/
		          FROM DEV_PAS.T_CS_CUST_TEL_CHG_DETAIL CCTCD
		         WHERE CCTCD.POLICY_CODE = #{policy_code} 
		         ORDER BY CCTCD.UPDATE_TIME DESC) SSS
		 WHERE RN < #{LESS_NUM}
		   AND RN >= #{GREATER_NUM}
		 ]]>
	</select>
	 <!-- 投资组合账户-查询保单账户基本信息 -->
    <select id="qry_findPolicyAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
	    SELECT AP.POLICY_ID, /*保单ID*/
	       BP.BUSI_PROD_CODE, /*险种代码*/
	       P.PRODUCT_NAME_SYS AS BUSI_PROD_NAME, /*险种代码名称*/
	       p.product_abbr_name as busi_abbr_name,/*险种代码简称*/
	       TC.POLICY_CODE, /*保单号*/
	       min(AP.CREATE_DATE) CREATE_DATE, /*账户成立日期*/
	       SUM(AP.INTEREST_CAPITAL) AS ACCOUNT_VALUE /*账户价值*/
		  FROM DEV_PAS.T_CONTRACT_INVEST AP
		 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
		    ON BP.BUSI_ITEM_ID = AP.BUSI_ITEM_ID
		   AND BP.POLICY_ID = AP.POLICY_ID
		 INNER JOIN DEV_PAS.T_CONTRACT_MASTER TC
		    ON AP.POLICY_ID = TC.POLICY_ID
		   AND BP.POLICY_ID = TC.POLICY_ID
		 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT P
		    ON BP.BUSI_PROD_CODE = P.PRODUCT_CODE_SYS
		 WHERE 1 = 1
		   AND TC.POLICY_CODE = #{policy_code}/*保单号*/
		   AND BP.BUSI_PROD_CODE in ('********','********','********','********','********','00Z01000')
		 GROUP BY AP.POLICY_ID,
		          BP.BUSI_PROD_CODE,
		          P.PRODUCT_NAME_SYS,
		          TC.POLICY_CODE,
		          
		          p.product_abbr_name
    	]]>
    </select>
    
        
       <select id="qry_findIndividualPensionAccount" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[ 
		select 
		ai.CUSTOMER_NAME, /* 姓名*/
		(select t.type from dev_pas.T_CERTI_TYPE t where  ai.CUSTOMER_CERT_TYPE=t.code )CUSTOMER_CERT_TYPE,/* 证件类型*/
		ai.CUSTOMER_CERTI_CODE,/* 证件号码*/
		(ai.ACCOUNT_BANK||'-'||(select b.bank_name from dev_pas.t_bank b where ai.ACCOUNT_BANK=b.bank_code))as ACCOUNT_BANK,/* 开户银行*/
		ai.ACCOUNT_NAME,/*账户名称*/
		ai.ACCOUNT,/*账户号码*/
		ai.BANK_PHONE, /* 预留手机号*/
		ai.VALIDIY_END_DATE /*有效期至*/
		 from 
		dev_pas.t_special_account_relation ar
		left join
		dev_pas.t_special_account_info ai
		on
		ar.BANK_POLICY_ID=ai.LIST_ID
		where 
		ar.policy_code=#{policy_code}
	   ]]>
    </select>
    
    <!-- 查询投资组合账户基本信息 -->
    <select id="qry_findCombAccountInfo" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
	    SELECT AP.List_Id,
		       AP.POLICY_ID, /*保单ID*/
		       BP.BUSI_PROD_CODE, /*险种代码*/
		       FD.FUND_NAME, /*投资组合名称*/
		       p.product_abbr_name AS BUSI_PROD_NAME, /*险种代码名称*/
		       TC.POLICY_CODE, /*保单号*/
		       AP.CREATE_DATE, /*账户成立日期*/
		       AP.INTEREST_CAPITAL, /*账户价值*/
		       
		        (case when BP.BUSI_PROD_CODE = '********' THEN
                (SELECT DEAL_TIME
              FROM (SELECT FS.SETTLE_DATE AS DEAL_TIME, FS.INVEST_ID
                      FROM DEV_PAS.T_FUND_SETTLEMENT FS
                     INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                        ON FS.INVEST_ID = CI.LIST_ID
                     INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
                        ON CI.POLICY_ID = CM.POLICY_ID
                     WHERE CM.POLICY_CODE = #{policy_code}
                     AND FS.ACCOUNT_CODE = '996000'
                     ORDER BY FS.SETTLE_DATE DESC) B
             WHERE ROWNUM = 1)
                ELSE
                (SELECT DEAL_TIME
              FROM (SELECT FS.SETTLE_DATE AS DEAL_TIME, FS.INVEST_ID
                      FROM DEV_PAS.T_FUND_SETTLEMENT FS
                     INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                        ON FS.INVEST_ID = CI.LIST_ID
                     INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
                        ON CI.POLICY_ID = CM.POLICY_ID
                     WHERE CM.POLICY_CODE = #{policy_code}
                     ORDER BY FS.SETTLE_DATE DESC) B
             WHERE ROWNUM = 1) END
           )COUNT_DATE /*上一结算日期*/
           
		  FROM DEV_PAS.T_CONTRACT_INVEST AP
		 INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD BP
		    ON BP.BUSI_ITEM_ID = AP.BUSI_ITEM_ID
		   AND BP.POLICY_ID = AP.POLICY_ID
		 INNER JOIN DEV_PAS.T_CONTRACT_MASTER TC
		    ON AP.POLICY_ID = TC.POLICY_ID
		   AND BP.POLICY_ID = TC.POLICY_ID
		 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT P
		    ON BP.BUSI_PROD_CODE = P.PRODUCT_CODE_SYS
		 INNER JOIN DEV_PDS.T_FUND FD
		    ON AP.ACCOUNT_CODE = FD.FUND_CODE
		 WHERE 1 = 1
		   AND TC.POLICY_CODE = #{policy_code} /*保单号*/
		   AND BP.BUSI_PROD_CODE in ('********','********','********','********','********','00Z01000')
		ORDER BY BP.BUSI_PROD_CODE]]>
    </select>
    
    
         <!-- 查询保单信托 -->
    <select id="qry_findContractMasterTrustBusiFlag" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[
	    	select a.trust_busi_flag
             from DEV_PAS.T_CONTRACT_MASTER A where A.POLICY_CODE = #{policy_code}
         ]]>
    </select>
    
    
    <select id="findPolicyAccountTrailPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            SELECT A.*
		  FROM (SELECT ROWNUM RN,B.*
		  	FROM(]]>
  		<include refid="findPolicyAccountTrailInfo" />
  	 <![CDATA[)B WHERE  ROWNUM <=#{LESS_NUM}  ) A  WHERE  A.RN > #{GREATER_NUM} ]]> 
    </select>
    <select id="findPolicyAccountTrailTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT COUNT(1) FROM (]]>
        	<include refid="findPolicyAccountTrailInfo"></include>
        <![CDATA[)]]>
    </select>
    <sql id="findPolicyAccountTrailInfo">
    	<![CDATA[
    		SELECT TF.POLICY_ID,
		        (
		          case when tf.trans_code = '05' then 3
		             else  TF.TRANS_TYPE
		             end
		       )as TRANS_TYPE, /*交易类型*/
		       (case
		         when (tf.trans_code = '21' or tf.trans_code = '24')  and tf.fund_code in ('928000','928100','Z01000') then
		          '账户注销-稳健回报型投资组合'
		         when (tf.trans_code = '21' or tf.trans_code = '24')  and tf.fund_code in ('928001','928101','Z01001') then
		          '账户注销-积极进取型投资组合'
		         when tf.trans_code = '04' and tf.fund_code in ('928000','928100','Z01000') then
		          '保单管理费-稳健回报型投资组合'
		         when tf.trans_code = '04' and tf.fund_code in ('928001','928101','Z01001') then
		          '保单管理费-积极进取型投资组合'
		         when tf.trans_code = '42' and tf.fund_code in ('928000','928100','Z01000') then
		          '投资组合收益-稳健回报型投资组合'
		         when tf.trans_code = '42' and tf.fund_code in ('928001','928101','Z01001') then
		          '投资组合收益-积极进取型投资组合'
		         when tf.trans_code = '14' then
	              '投资组合转换-转入'
	             when tf.trans_code = '23' then
	              '投资组合转换-转出'
	             when tf.trans_code = '42' and tf.busi_prod_code = '********' then
              	 '结算利息-个人税收递延型养老年金保险A款（2018）产品账户'
              	 when tf.trans_code = '42' and tf.busi_prod_code = '********' then
              	 '结算利息-个人税收递延型养老年金保险B1款（2018）产品账户'
             	 when tf.trans_code = '42' and tf.busi_prod_code = '********' then
              	 '结算利息-个人税收递延型养老年金保险B2款（2018）产品账户'
             	 when tf.trans_code = '04' and tf.busi_prod_code = '********' then
              	 '保单管理费-个人税收递延型养老年金保险A款（2018）产品账户'
             	 when tf.trans_code = '04' and tf.busi_prod_code = '********' then
              	 '保单管理费-个人税收递延型养老年金保险B1款（2018）产品账户'
             	 when tf.trans_code = '04' and tf.busi_prod_code = '********' then
              	 '保单管理费-个人税收递延型养老年金保险B2款（2018）产品账户'
             	 when (tf.trans_code = '21' or tf.trans_code = '24')   and tf.busi_prod_code = '********' then
              	 '账户注销-个人税收递延型养老年金保险A款（2018）产品账户'
             	 when (tf.trans_code = '21' or tf.trans_code = '24')   and tf.busi_prod_code = '********' then
              	 '账户注销-个人税收递延型养老年金保险B1款（2018）产品账户'
            	  when (tf.trans_code = '21' or tf.trans_code = '24')   and tf.busi_prod_code = '********' then
              	 '账户注销-个人税收递延型养老年金保险B2款（2018）产品账户'
             	 when tf.trans_code = '14' and tf.busi_prod_code = '********' then
                 '转入-个人税收递延型养老年金保险A款（2018）产品账户'
             	 when tf.trans_code = '14' and tf.busi_prod_code = '********' then
                 '转入-个人税收递延型养老年金保险B1款（2018）产品账户'
             	 when tf.trans_code = '14' and tf.busi_prod_code = '********' then
                 '转入-个人税收递延型养老年金保险B2款（2018）产品账户'
             	 when tf.trans_code = '23' and tf.busi_prod_code = '********' then
                 '转出-个人税收递延型养老年金保险A款（2018）产品账户'
             	 when tf.trans_code = '23' and tf.busi_prod_code = '********' then
                 '转出-个人税收递延型养老年金保险B1款（2018）产品账户'
             	 when tf.trans_code = '23' and tf.busi_prod_code = '********' then
                 '转出-个人税收递延型养老年金保险B2款（2018）产品账户'
                 when tf.trans_code in ('05','35','46','48','50','56') and tf.busi_prod_code = '********' then
                 '初始扣费-个人税收递延型养老年金保险A款（2018）产品账户'
             	 when tf.trans_code in ('05','35','46','48','50','56') and tf.busi_prod_code = '********' then
                 '初始扣费-个人税收递延型养老年金保险B1款（2018）产品账户'
             	 when tf.trans_code in ('05','35','46','48','50','56') and tf.busi_prod_code = '********' then
                 '初始扣费-个人税收递延型养老年金保险B2款（2018）产品账户'
             	 when tf.trans_code = '52' and tf.busi_prod_code = '********' then 
             	 '期交保费-首期-个人税收递延型养老年金保险A款（2018）产品账户'
             	 when tf.trans_code = '52' and tf.busi_prod_code = '********' then 
             	 '期交保费-首期-个人税收递延型养老年金保险B1款（2018）产品账户'
             	 when tf.trans_code = '52' and tf.busi_prod_code = '********' then 
             	 '期交保费-首期-个人税收递延型养老年金保险B2款（2018）产品账户'
             	 when tf.trans_code = '12' and tf.busi_prod_code = '********'
             	 then '期交保费-续期-个人税收递延型养老年金保险A款（2018）产品账户'
             	 when tf.trans_code = '12' and tf.busi_prod_code = '********'
             	 then '期交保费-续期-个人税收递延型养老年金保险B1款（2018）产品账户'
             	 when tf.trans_code = '12' and tf.busi_prod_code = '********'
             	 then '期交保费-续期-个人税收递延型养老年金保险B2款（2018）产品账户'
		         when tf.trans_code in ('05','35','46','48','50','56') then '初始扣费'
		         when tf.trans_code = '52' and tf.busi_prod_code in ('00Z01000') then '一次交清保险费'
		         when tf.trans_code = '12' then '期交保费-续期'
		         when tf.trans_code = '52' then '期交保费-首期'
		         when tf.trans_code = '53' then '不定期交保费-首期'
		         when tf.trans_code = '51' then '期交保费-补交'
		         when tf.trans_code = '54' then '不定期交保费'
		         when tf.trans_code = '55' then '期交保费-补交'
		         when tf.trans_code = '42' then '投资组合收益'
		         when tf.trans_code = '58' then '追加保费'
		         else
		          (select T.DESCRIPTION
		             from DEV_PAS.T_TRANSACTION_CODE T
		            WHERE tf.TRANS_CODE = T.TRANS_CODE)
		       end) as trans_code,
		       TF.TRANS_AMOUNT, /*变更金额*/
		       TF.DEAL_TIME, /*交易日期*/
		       TF.BALANCE_AF TRANS_INTEREST  /*变更后账户价值*/
		  FROM DEV_PAS.T_FUND_GROUP_TRANS TF
		 WHERE TF.POLICY_ID =  #{policy_id}
		    	]]>
             <if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND TF.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
            <![CDATA[ 
		 ORDER BY  TF.DEAL_TIME DESC, TF.BUSI_PROD_CODE,TF.LIST_ID DESC
    	]]>
    </sql>
    <select id="findCombAccountTrailPage" resultType="java.util.Map"
        parameterType="java.util.Map">
     <![CDATA[
            SELECT H.*
		  FROM (SELECT ROWNUM RN,D.*
		  	FROM(]]>
  		<include refid="combAccountTrail" />
  	 <![CDATA[)D WHERE  ROWNUM <=#{LESS_NUM}  ) H  WHERE  H.RN > #{GREATER_NUM} ]]> 
    </select>
    <select id="findCombAccountTrailTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
    	 <![CDATA[
              SELECT COUNT(1) FROM (]]>
			<include refid="combAccountTrail" />
		  <![CDATA[)]]>
    </select>
    
    
    
        
    
    <sql id="trustCompanyWhereCondition">
		<if test=" manager_country != null and manager_country != ''  "><![CDATA[ AND A.MANAGER_COUNTRY = #{manager_country} ]]></if>
		<if test=" tax_code_start_date  != null  and  tax_code_start_date  != ''  "><![CDATA[ AND A.TAX_CODE_START_DATE = #{tax_code_start_date} ]]></if>
		<if test=" operator_certi_type != null and operator_certi_type != ''  "><![CDATA[ AND A.OPERATOR_CERTI_TYPE = #{operator_certi_type} ]]></if>
		<if test=" state != null and state != ''  "><![CDATA[ AND A.STATE = #{state} ]]></if>
		<if test=" company_en_name != null and company_en_name != ''  "><![CDATA[ AND A.COMPANY_EN_NAME = #{company_en_name} ]]></if>
		<if test=" operator_certi_start_date  != null  and  operator_certi_start_date  != ''  "><![CDATA[ AND A.OPERATOR_CERTI_START_DATE = #{operator_certi_start_date} ]]></if>
		<if test=" manager_certi_end_date  != null  and  manager_certi_end_date  != ''  "><![CDATA[ AND A.MANAGER_CERTI_END_DATE = #{manager_certi_end_date} ]]></if>
		<if test=" tax_code_end_date  != null  and  tax_code_end_date  != ''  "><![CDATA[ AND A.TAX_CODE_END_DATE = #{tax_code_end_date} ]]></if>
		<if test=" holding_person_name != null and holding_person_name != ''  "><![CDATA[ AND A.HOLDING_PERSON_NAME = #{holding_person_name} ]]></if>
		<if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
		<if test=" company_holder_certi_code != null and company_holder_certi_code != ''  "><![CDATA[ AND A.COMPANY_HOLDER_CERTI_CODE = #{company_holder_certi_code} ]]></if>
		<if test=" company_oran_code != null and company_oran_code != ''  "><![CDATA[ AND A.COMPANY_ORAN_CODE = #{company_oran_code} ]]></if>
		<if test=" operator_certi_code != null and operator_certi_code != ''  "><![CDATA[ AND A.OPERATOR_CERTI_CODE = #{operator_certi_code} ]]></if>
		<if test=" company_address != null and company_address != ''  "><![CDATA[ AND A.COMPANY_ADDRESS = #{company_address} ]]></if>
		<if test=" organ_code_end_date  != null  and  organ_code_end_date  != ''  "><![CDATA[ AND A.ORGAN_CODE_END_DATE = #{organ_code_end_date} ]]></if>
		<if test=" district != null and district != ''  "><![CDATA[ AND A.DISTRICT = #{district} ]]></if>
		<if test=" manager_name != null and manager_name != ''  "><![CDATA[ AND A.MANAGER_NAME = #{manager_name} ]]></if>
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
		<if test=" address != null and address != ''  "><![CDATA[ AND A.ADDRESS = #{address} ]]></if>
		<if test=" legal_person_certi_type != null and legal_person_certi_type != ''  "><![CDATA[ AND A.LEGAL_PERSON_CERTI_TYPE = #{legal_person_certi_type} ]]></if>
		<if test=" operator_name  != null  and  operator_name  != ''  "><![CDATA[ AND A.OPERATOR_NAME = #{operator_name} ]]></if>
		<if test=" licence_code_end_date  != null  and  licence_code_end_date  != ''  "><![CDATA[ AND A.LICENCE_CODE_END_DATE = #{licence_code_end_date} ]]></if>
		<if test=" business_cope != null and business_cope != ''  "><![CDATA[ AND A.BUSINESS_COPE = #{business_cope} ]]></if>
		<if test=" operator_certi_end_date  != null  and  operator_certi_end_date  != ''  "><![CDATA[ AND A.OPERATOR_CERTI_END_DATE = #{operator_certi_end_date} ]]></if>
		<if test=" organ_code_start_date  != null  and  organ_code_start_date  != ''  "><![CDATA[ AND A.ORGAN_CODE_START_DATE = #{organ_code_start_date} ]]></if>
		<if test=" legal_per_certi_start_date  != null  and  legal_per_certi_start_date  != ''  "><![CDATA[ AND A.LEGAL_PER_CERTI_START_DATE = #{legal_per_certi_start_date} ]]></if>
		<if test=" company_holder_country != null and company_holder_country != ''  "><![CDATA[ AND A.COMPANY_HOLDER_COUNTRY = #{company_holder_country} ]]></if>
		<if test=" legal_per_certi_end_date  != null  and  legal_per_certi_end_date  != ''  "><![CDATA[ AND A.LEGAL_PER_CERTI_END_DATE = #{legal_per_certi_end_date} ]]></if>
		<if test=" manager_certi_type != null and manager_certi_type != ''  "><![CDATA[ AND A.MANAGER_CERTI_TYPE = #{manager_certi_type} ]]></if>
		<if test=" manager_certi_code != null and manager_certi_code != ''  "><![CDATA[ AND A.MANAGER_CERTI_CODE = #{manager_certi_code} ]]></if>
		<if test=" company_holder_certi_type != null and company_holder_certi_type != ''  "><![CDATA[ AND A.COMPANY_HOLDER_CERTI_TYPE = #{company_holder_certi_type} ]]></if>
		<if test=" legal_person_certi_code != null and legal_person_certi_code != ''  "><![CDATA[ AND A.LEGAL_PERSON_CERTI_CODE = #{legal_person_certi_code} ]]></if>
		<if test=" holder_certi_start_date  != null  and  holder_certi_start_date  != ''  "><![CDATA[ AND A.HOLDER_CERTI_START_DATE = #{holder_certi_start_date} ]]></if>
		<if test=" post_code != null and post_code != ''  "><![CDATA[ AND A.POST_CODE = #{post_code} ]]></if>
		<if test=" manager_certi_start_date  != null  and  manager_certi_start_date  != ''  "><![CDATA[ AND A.MANAGER_CERTI_START_DATE = #{manager_certi_start_date} ]]></if>
		<if test=" tax_code != null and tax_code != ''  "><![CDATA[ AND A.TAX_CODE = #{tax_code} ]]></if>
		<if test=" holder_certi_end_date  != null  and  holder_certi_end_date  != ''  "><![CDATA[ AND A.HOLDER_CERTI_END_DATE = #{holder_certi_end_date} ]]></if>
		<if test=" licence_code_start_date  != null  and  licence_code_start_date  != ''  "><![CDATA[ AND A.LICENCE_CODE_START_DATE = #{licence_code_start_date} ]]></if>
		<if test=" busi_licence_code != null and busi_licence_code != ''  "><![CDATA[ AND A.BUSI_LICENCE_CODE = #{busi_licence_code} ]]></if>
		<if test=" city != null and city != ''  "><![CDATA[ AND A.CITY = #{city} ]]></if>
		<if test=" legal_person_name != null and legal_person_name != ''  "><![CDATA[ AND A.LEGAL_PERSON_NAME = #{legal_person_name} ]]></if>
		<if test=" legal_person_country != null and legal_person_country != ''  "><![CDATA[ AND A.LEGAL_PERSON_COUNTRY = #{legal_person_country} ]]></if>
		<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
    
    
    <!-- 查询信托全部信息 -->
       <select id="QRY_findCompanyDetailInfoByCompanyOragnCode" resultType="java.util.Map"
        parameterType="java.util.Map">
    	<![CDATA[ SELECT A.MANAGER_COUNTRY, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.STATE, A.COMPANY_EN_NAME, A.OPERATOR_CERTI_START_DATE, A.MANAGER_CERTI_END_DATE, 
			A.TAX_CODE_END_DATE, A.HOLDING_PERSON_NAME, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, A.COMPANY_ORAN_CODE, 
			A.OPERATOR_CERTI_CODE, A.COMPANY_ADDRESS, A.ORGAN_CODE_END_DATE, A.DISTRICT, A.MANAGER_NAME, A.COMPANY_ID, 
			A.ADDRESS, A.LEGAL_PERSON_CERTI_TYPE, A.OPERATOR_NAME, A.LICENCE_CODE_END_DATE, A.BUSINESS_COPE, A.OPERATOR_CERTI_END_DATE, 
			A.ORGAN_CODE_START_DATE, A.LEGAL_PER_CERTI_START_DATE, A.COMPANY_HOLDER_COUNTRY, A.LEGAL_PER_CERTI_END_DATE, A.MANAGER_CERTI_TYPE, A.MANAGER_CERTI_CODE, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.LEGAL_PERSON_CERTI_CODE, A.HOLDER_CERTI_START_DATE, A.POST_CODE, A.MANAGER_CERTI_START_DATE, A.TAX_CODE, A.HOLDER_CERTI_END_DATE, A.LICENCE_CODE_START_DATE, 
			A.BUSI_LICENCE_CODE, A.CITY, A.LEGAL_PERSON_NAME, A.LEGAL_PERSON_COUNTRY FROM dev_pas.T_TRUST_COMPANY A WHERE 1 = 1  ]]>
		<include refid="trustCompanyWhereCondition" />
    </select>
    
    
    
      <!-- 根据统一社会信用代码/组织机构代码查询信托公司全部信息-->
       <select id="QRY_findCompanyInfoByCompanyOragnCode" resultType="java.util.Map"
        parameterType="java.util.Map">
    	<![CDATA[ SELECT A.MANAGER_COUNTRY, A.TAX_CODE_START_DATE, A.OPERATOR_CERTI_TYPE, A.STATE, A.COMPANY_EN_NAME, A.OPERATOR_CERTI_START_DATE, A.MANAGER_CERTI_END_DATE, 
			A.TAX_CODE_END_DATE, A.HOLDING_PERSON_NAME, A.COMPANY_NAME, A.COMPANY_HOLDER_CERTI_CODE, A.COMPANY_ORAN_CODE, 
			A.OPERATOR_CERTI_CODE, A.COMPANY_ADDRESS, A.ORGAN_CODE_END_DATE, A.DISTRICT, A.MANAGER_NAME, A.COMPANY_ID, 
			A.ADDRESS, A.LEGAL_PERSON_CERTI_TYPE, A.OPERATOR_NAME, A.LICENCE_CODE_END_DATE, A.BUSINESS_COPE, A.OPERATOR_CERTI_END_DATE, 
			A.ORGAN_CODE_START_DATE, A.LEGAL_PER_CERTI_START_DATE, A.COMPANY_HOLDER_COUNTRY, A.LEGAL_PER_CERTI_END_DATE, A.MANAGER_CERTI_TYPE, A.MANAGER_CERTI_CODE, A.COMPANY_HOLDER_CERTI_TYPE, 
			A.LEGAL_PERSON_CERTI_CODE, A.HOLDER_CERTI_START_DATE, A.POST_CODE, A.MANAGER_CERTI_START_DATE, A.TAX_CODE, A.HOLDER_CERTI_END_DATE, A.LICENCE_CODE_START_DATE, 
			A.BUSI_LICENCE_CODE, A.CITY, A.LEGAL_PERSON_NAME, A.LEGAL_PERSON_COUNTRY FROM dev_pas.T_TRUST_COMPANY A WHERE 1 = 1  ]]>
		<include refid="trustCompanyWhereCondition" />
    </select>
    
    
    
    <sql id="trustCompanyBeneWhereCondition">
		<if test=" bene_certi_code != null and bene_certi_code != ''  "><![CDATA[ AND A.BENE_CERTI_CODE = #{bene_certi_code} ]]></if>
		<if test=" bene_certi_start_date  != null  and  bene_certi_start_date  != ''  "><![CDATA[ AND A.BENE_CERTI_START_DATE = #{bene_certi_start_date} ]]></if>
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
		<if test=" bene_name != null and bene_name != ''  "><![CDATA[ AND A.BENE_NAME = #{bene_name} ]]></if>
		<if test=" bene_certi_end_date  != null  and  bene_certi_end_date  != ''  "><![CDATA[ AND A.BENE_CERTI_END_DATE = #{bene_certi_end_date} ]]></if>
		<if test=" bene_certi_type != null and bene_certi_type != ''  "><![CDATA[ AND A.BENE_CERTI_TYPE = #{bene_certi_type} ]]></if>
		<if test=" bene_address != null and bene_address != ''  "><![CDATA[ AND A.BENE_ADDRESS = #{bene_address} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
    
    <!-- 查询所有受益人信息 -->
	<select id="findAllTrustCompanyBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_CERTI_CODE, A.BENE_CERTI_START_DATE, A.COMPANY_ID, A.BENE_NAME, A.BENE_CERTI_END_DATE, 
			A.BENE_CERTI_TYPE, A.BENE_ADDRESS, A.LIST_ID FROM DEV_PAS.T_TRUST_COMPANY_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="trustCompanyBeneWhereCondition" />
	</select>
    
    
    
    
    <sql id="combAccountTrail">
    <![CDATA[
    SELECT C.*
          FROM (SELECT 2 AS TRANS_TYPE,/*交易类型 1-出账 2-入账 码表t_invest_trans_type*/	
          			   '投资组合收益' AS TRANS_CODE, /*变更类型*/
                       B.SETTLE_DATE AS DEAL_TIME, /*变更日期*/
                       B.INTEREST AS TRANS_AMOUNT, /*变更金额*/
                       B.INTEREST_RATE * 100 AS TRANS_PROPORTION, /*结算利率*/
                       B.BALANCE AS TRANS_INTEREST, /*变更后账户价值*/
                       B.SETTLEMENT_ID AS TRANS_ID /*交易ID*/
                  FROM (SELECT CI.*, FS.*, ROWNUM AS RN
                          FROM DEV_PAS.T_FUND_SETTLEMENT FS
                         INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                            ON CI.LIST_ID = FS.INVEST_ID
                         WHERE 1 = 1
                           AND CI.LIST_ID = #{list_id} /*子账户ID*/
                        ) B
                UNION ALL
                SELECT B.TRANS_TYPE     AS TRANS_TYPE,/*交易类型*/
                	   ( case when  B.TRANS_CODE = '11' and B.fund_code in ('Z01000','Z01001') then '一次交清保险费'
                	   		  when  B.TRANS_CODE = '11' then '首期期交保费'
		                      when  B.TRANS_CODE = '47' then '首期不定期交保费'
		                      when  B.TRANS_CODE = '12' then '续期期交保费'
		                      when  B.TRANS_CODE = '51' then '补交保费'
		                      when  B.TRANS_CODE = '49' then '不定期交保费'
		                      when  B.TRANS_CODE = '42' then '投资组合收益'
		                      when  B.TRANS_CODE = '21' OR B.TRANS_CODE = '24'  then '账户注销'
		                      when  B.TRANS_CODE = '14' then '投资组合转换-转入' 
		                      when  B.TRANS_CODE = '23' then '投资组合转换-转出'
		                      when  B.TRANS_CODE = '13' then '追加保费'
		                 else
		                  (select T.DESCRIPTION
		                     from DEV_PAS.T_TRANSACTION_CODE T
		                    WHERE B.TRANS_CODE = T.TRANS_CODE)
		                 end     
		               ) 				AS trans_code, /*变更类型*/
                       B.DEAL_TIME      AS DEAL_TIME, /*变更日期*/
                       B.TRANS_AMOUNT   AS TRANS_AMOUNT, /*变更金额*/
                       B.TRANS_INTEREST AS TRANS_PROPORTION, /*结算利率*/
                       B.BALANCE        AS TRANS_INTEREST, /*变更后账户价值*/
                       B.TRANS_ID       AS TRANS_ID /*交易ID*/
                  FROM (SELECT CI.*,
                               TC.TRANS_CODE,
                               TC.DESCRIPTION,
                               FT.TRANS_TYPE,
                               FT.DEAL_TIME,
                               FT.TRANS_AMOUNT,
                               FT.FUND_CODE,
		 					   (CASE
		                         WHEN FT.TRANS_CODE = '42' THEN
		                          (SELECT A.ANNUAL_INTEREST_RATE
		                             FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
		                            WHERE A.INTEREST_RATE_TYPE = '1'
		                              AND A.INTEREST_START_DATE <= FT.DEAL_TIME
		                              AND A.FUND_CODE = FT.FUND_CODE
		                              AND A.interest_start_date =
		                                  (SELECT MAX(A.interest_start_date)
		                                     FROM DEV_PDS.T_UNIVERSAL_SETTLEMENT_RATE A
		                                    WHERE A.INTEREST_RATE_TYPE = '1'
		                                      AND A.INTEREST_START_DATE <= FT.DEAL_TIME
		                                      AND A.FUND_CODE = FT.FUND_CODE))
		                         ELSE
		                          NULL
		                       END) * 100  AS TRANS_INTEREST,
                               FT.TRANS_UNITS,
                               FT.TRANS_PRICE,
                               FT.BALANCE_UNITS_BF,
                               (CASE FT.TRANS_TYPE
                                 WHEN 0 THEN
                                  FT.BALANCE_UNITS_BF
                                 WHEN 1 THEN
                                  FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
                                 WHEN 2 THEN
                                  FT.BALANCE_UNITS_BF + FT.TRANS_AMOUNT
                                 WHEN 3 THEN
                                  FT.BALANCE_UNITS_BF - FT.TRANS_AMOUNT
                               END
                               
                               ) AS BALANCE,
                               ROWNUM AS RN,
                               FT.TRANS_ID AS TRANS_ID
                          FROM DEV_PAS.T_FUND_TRANS FT
                         INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
                            ON CI.LIST_ID = FT.LIST_ID
                         INNER JOIN DEV_PAS.T_TRANSACTION_CODE TC
                            ON FT.TRANS_CODE = TC.TRANS_CODE
                         WHERE  TC.TRANS_CODE NOT IN ('05','30','31','35','36','37')
                           AND CI.LIST_ID = #{list_id} /*子账户ID*/
                        ) B) C
         ORDER BY C.DEAL_TIME DESC, C.TRANS_ID DESC]]>
    </sql>

    <select id="findDocumentInfos" resultType = "java.util.Map" parameterType = "java.util.Map">
    	<![CDATA[SELECT B.RN,
		       B.TEMPLATE_CODE,
		       B.BUSS_SOURCE_CODE,
		       B.SEND_OBJ_ID,
		       B.CREATE_TIME,
		       B.CLOSE_TIME,
		       B.USER_NAME,
		       B.STATUS,
		       B.BUSS_CODE,
		       B.DOCUMENT_NO,
		       B.DOC_LIST_ID,
		       B.SEND_BY,
		       B.SOME_NAME
		  FROM (SELECT ROWNUM RN,
		               T.TEMPLATE_CODE,
		               (	SELECT A.UW_SOURCE_TYPE FROM DEV_UW.T_UW_MASTER A WHERE A.UW_ID = (
							SELECT UW_ID FROM DEV_UW.T_PENOTICE TP WHERE TP.DOCUMENT_NO = T.DOCUMENT_NO
							UNION
							SELECT UW_ID FROM DEV_UW.T_SURVIVAL_INVESTIGATION TS WHERE TS.DOCUMENT_NO = T.DOCUMENT_NO
							UNION
							SELECT UW_ID FROM DEV_UW.T_ASKFORINFO TA WHERE TA.DOCUMENT_NO = T.DOCUMENT_NO
							UNION
							SELECT UW_ID FROM DEV_UW.T_UW_NOTICE TW WHERE TW.DOCUMENT_NO = T.DOCUMENT_NO
							UNION
							SELECT UW_ID FROM DEV_UW.T_MEDICAL_DOCINFO TM WHERE TM.DOCUMENT_NO = T.DOCUMENT_NO)
						) AS BUSS_SOURCE_CODE,
		               T.SEND_OBJ_ID,
		               T.CREATE_TIME,
		               T.CLOSE_TIME,
		               (SELECT UU.USER_NAME FROM DEV_UW.T_UDMP_USER UU WHERE UU.USER_ID = T.SEND_BY) AS USER_NAME,
		               T.STATUS,
		               T.BUSS_CODE,
		               T.DOCUMENT_NO,
		       		   T.DOC_LIST_ID,
		       		   T.SEND_BY,
		       		   (SELECT C.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER C WHERE T.SEND_OBJ_ID = C.CUSTOMER_ID) AS SOME_NAME
		          FROM DEV_NB.T_DOCUMENT T
		         WHERE ROWNUM <= #{LESS_NUM}
		         
		           AND T.BUSS_CODE = #{buss_code}
		           AND T.BUSS_SOURCE_CODE = '002'
		         ORDER BY T.SEND_TIME) B
		 WHERE B.RN > #{GREATER_NUM}]]>
    </select>
    
    <select id="findBusiProdInfo" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[ SELECT T2.BUSI_PROD_CODE,T1.AMOUNT,T1.UNIT,T1.BENEFIT_LEVEL,
        T1.DECISION_CODE,/*新契约核保决定*/
        T1.POSTPONED_MONTHS,/*延期时长*/
        T7.PRODUCT_NAME_SYS,
        T4.LIABILITY_STATE,/*状态*/
        (SELECT B.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST B
         LEFT JOIN APP___UW__DBUSER.T_BENEFIT_INSURED C 
         ON B.LIST_ID = C.INSURED_ID WHERE C.BUSI_ITEM_ID = T2.busi_item_id
         AND B.UW_ID = C.UW_ID and rownum = 1 
         AND C.UW_ID = T2.UW_ID) customer_name,
        
        (SELECT T5.EM_VALUE FROM DEV_UW.T_UW_EXTRA_PREM T5
            WHERE T5.UW_PRD_ID = T1.UW_PRD_ID AND ROWNUM = 1) AS EM_VALUE,/*加费评点*/
		(SELECT T6.CONDITION_DESC FROM DEV_UW.T_UW_CONDITION T6
            WHERE T6.UW_PRD_ID = T1.UW_PRD_ID AND ROWNUM = 1) AS CONDITION_DESC/*特别约定*/
	FROM DEV_UW.T_UW_PRODUCT T1,DEV_UW.T_UW_BUSI_PROD T2,DEV_UW.T_UW_MASTER T3,DEV_NB.T_NB_CONTRACT_PRODUCT T4,DEV_PDS.T_BUSINESS_PRODUCT T7
    WHERE T1.UW_ID = T2.UW_ID  AND T1.UW_BUSI_ID = T2.UW_BUSI_ID AND T1.APPLY_CODE = T4.APPLY_CODE AND T7.PRODUCT_CODE_SYS = T2.BUSI_PROD_CODE
        AND T1.ITEM_ID = T4.ITEM_ID AND T1.UW_ID = T3.UW_ID AND T3.BIZ_CODE =  #{biz_code}   AND T3.UW_SOURCE_TYPE = '1'
        ORDER BY T2.ORDER_ID, T1.UW_BUSI_ID ]]>
    </select>
    <select id="findPasBusiProdInfo" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[ 
    		SELECT T2.BUSI_PROD_CODE,
       T1.AMOUNT,
       T1.UNIT,
       T1.BENEFIT_LEVEL,
       T1.DECISION_CODE, /*新契约核保决定*/
       T1.POSTPONED_MONTHS, /*延期时长*/
       T7.PRODUCT_NAME_SYS,
       T4.LIABILITY_STATE, /*状态*/
       (SELECT T5.EM_VALUE
          FROM DEV_UW.T_UW_EXTRA_PREM T5
         WHERE T5.UW_PRD_ID = T1.UW_PRD_ID
           AND T5.EXTRA_TYPE = '1'
           AND ROWNUM = 1) AS EM_VALUE, /*加费评点*/
       (SELECT T6.CONDITION_DESC
          FROM DEV_UW.T_UW_CONDITION T6
         WHERE T6.UW_PRD_ID = T1.UW_PRD_ID
           AND ROWNUM = 1) AS CONDITION_DESC, /*特别约定*/
           T2.ORDER_ID,
        (SELECT B.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST B
         LEFT JOIN APP___UW__DBUSER.T_BENEFIT_INSURED C 
         ON B.LIST_ID = C.INSURED_ID WHERE C.BUSI_ITEM_ID = T2.busi_item_id
         AND B.UW_ID = C.UW_ID and rownum = 1 
         AND C.UW_ID = T2.UW_ID) customer_name,
           T2.MASTER_BUSI_ITEM_ID
  FROM DEV_UW.T_UW_PRODUCT          T1,
       DEV_UW.T_UW_BUSI_PROD        T2,
       DEV_UW.T_UW_MASTER           T3,
       DEV_NB.T_NB_CONTRACT_PRODUCT T4,
       DEV_PDS.T_BUSINESS_PRODUCT    T7
 WHERE T1.UW_ID = T2.UW_ID
   AND T1.UW_BUSI_ID = T2.UW_BUSI_ID
   AND T1.APPLY_CODE = T4.APPLY_CODE
   AND T7.PRODUCT_CODE_SYS = T2.BUSI_PROD_CODE
   AND T1.ITEM_ID = T4.ITEM_ID
   AND T1.UW_ID = T3.UW_ID
   AND T4.LIABILITY_STATE NOT IN ('0', '1')
   AND T3.BIZ_CODE = #{biz_code}
   AND T3.UW_SOURCE_TYPE = '1'


UNION ALL

  SELECT T6.BUSI_PROD_CODE,
         T5.AMOUNT,
         T5.UNIT,
         T5.BENEFIT_LEVEL,
         T5.DECISION_CODE, /*新契约核保决定*/
         '', /*延期时长*/
         T7.PRODUCT_NAME_SYS,
         T6.LIABILITY_STATE, /*状态*/
         (SELECT T8.EM_VALUE
            FROM DEV_PAS.T_EXTRA_PREM T8
           WHERE T8.ITEM_ID = T5.ITEM_ID
             AND T8.EXTRA_TYPE = '1'
             AND ROWNUM = 1) AS EM_VALUE, /*加费评点*/
         (SELECT T9.CONDITION_DESC
            FROM DEV_PAS.T_POLICY_CONDITION T9
           WHERE T9.ITEM_ID = T5.ITEM_ID
             AND ROWNUM = 1) AS CONDITION_DESC, /*特别约定*/
          T6.ORDER_ID,
           (SELECT TC.CUSTOMER_NAME
                  FROM dev_pas.t_customer TC ,Dev_pas.T_INSURED_LIST TIL ,DEV_PAS.T_BENEFIT_INSURED TBI
                 WHERE TC.CUSTOMER_ID = TIL.Customer_Id 
                 AND TBI.BUSI_ITEM_ID = T6.BUSI_ITEM_ID 
                 AND TIL.LIST_ID = TBI.INSURED_ID
                 AND TIL.POLICY_CODE = T6.POLICY_CODE
                   AND ROWNUM = 1
                   ) customer_name,
          T6.MASTER_BUSI_ITEM_ID
          
         FROM DEV_PAS.T_CONTRACT_PRODUCT   T5,
         DEV_PAS.T_CONTRACT_BUSI_PROD T6,
         DEV_PDS.T_BUSINESS_PRODUCT    T7
   WHERE T5.POLICY_CODE = T6.POLICY_CODE
     AND T5.BUSI_ITEM_ID = T6.BUSI_ITEM_ID
     AND T6.BUSI_PRD_ID = T7.BUSINESS_PRD_ID
     AND T5.POLICY_CODE = #{policy_code}
ORDER BY  ORDER_ID, MASTER_BUSI_ITEM_ID
    	]]>
    </select>
        
    <select id="QRY_findamlFlag" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[ 
    	SELECT MAS.APPLY_DATE,
      		   A.CHARGE_PERIOD ,A.CHARGE_YEAR,A.PREM_FREQ,A.PRODUCT_CODE,A.STD_PREM_AF,
               C.COVER_PERIOD_TYPE,C.PRODUCT_CATEGORY,C.PRODUCT_CATEGORY2,
			   TC.CUSTOMER_BIRTHDAY
		FROM APP___NB__DBUSER.T_NB_CONTRACT_PRODUCT A /*保单责任层信息表*/
	    LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER MAS ON MAS.APPLY_CODE=A.APPLY_CODE
		LEFT JOIN APP___NB__DBUSER.T_NB_CONTRACT_BUSI_PROD B  ON A.APPLY_CODE = B.APPLY_CODE AND A.BUSI_ITEM_ID = B.BUSI_ITEM_ID /*投保单险种信息表*/
		LEFT JOIN APP___NB__DBUSER.T_BUSINESS_PRODUCT C ON B.PRODUCT_CODE = C.PRODUCT_CODE_SYS  /*业务产品*/
		LEFT JOIN DEV_NB.T_NB_INSURED_LIST NIL ON NIL.APPLY_CODE=MAS.APPLY_CODE /*被保人信息表*/
		LEFT JOIN  DEV_NB.T_CUSTOMER TC ON TC.CUSTOMER_ID=NIL.CUSTOMER_ID 
		WHERE MAS.APPLY_CODE=#{apply_code}
    	]]>
    </select>
    <!-- 投连账户轨迹查询个数操作 -->
    <select id="findUnitLinkedAccountPathTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
            <![CDATA[ 
            SELECT COUNT(*)
            FROM(
                     select A.TRANS_CODE, --变更类型
			         A.deal_time, -- 成交日期
			         CAST(ABS(A.trans_amount) as decimal(18,2)) as trans_amount ,-- 交易金额
               		 CAST(A.TRANS_INTEREST as  decimal(18,2)) as TRANS_INTEREST, --账户价值
			         ROUND(A.UNIT_NUMBE,6) as UNIT_NUMBER, -- 单位数
			         A.UNIT_PRICE -- 单位价格
			          from (
					       select (select t.description
		                    from dev_pas.t_transaction_code t
		                   where t.trans_code = ft.trans_code) as trans_code, --变更类型
		                 	ft.deal_time, -- 成交日期
		                	ft.trans_amount, -- 交易金额
			                 (
			                     case 
			                       when ft.TRANS_TYPE = '1' then ft.balance_units_bf - ft.trans_amount
			                       when ft.TRANS_TYPE = '2' then ft.balance_units_bf + ft.trans_amount
			                       when ft.TRANS_TYPE = '3' then ft.balance_units_bf - ft.trans_amount
			                     else
			                       ft.balance_units_bf
			                     end
			                 ) as TRANS_INTEREST, --账户价值
			                 (
	                          case 
	                            when ft.trans_price = '0' then  null
	                            else
	                             (
	                             case 
	                               when ft.TRANS_TYPE = '1' then (ft.balance_units_bf - ft.trans_amount)/ft.fund_sell_price
	                               when ft.TRANS_TYPE = '2' then (ft.balance_units_bf + ft.trans_amount)/ft.fund_sell_price
	                               when ft.TRANS_TYPE = '3' then round(ft.balance_units_bf / ft.fund_sell_price, 6) - ft.trans_units
	                             else
	                               ft.balance_units_bf
	                             end
	                         )
	                          end
	                       ) as  UNIT_NUMBE , -- 单位数
			                 ft.fund_sell_price as UNIT_PRICE -- 单位价格
		            		from dev_pas.T_FUND_TRANS ft
		            		 WHERE 1=1 
              ]]>
             <if test=" account_id  != null "><![CDATA[ AND  ft.list_id = #{account_id} ]]></if> 
            <![CDATA[ 
            UNION All
			             select '投连日计价' as TRANS_CODE, --变更类型
			                 ipd.price_date as deal_time, -- 成交日期
			                 (ipd.last_invest_unit - ipd.init_invest_unit) *
			                 ipd.bid_price as TRANS_AMOUNT, -- 交易金额
			                 ipd.curr_balance as TRANS_INTEREST, -- --账户价值
			                 ipd.last_invest_unit as UNIT_NUMBER, --单位数
			                 ipd.bid_price as UNIT_PRICE --单位价格
			            from dev_pas.T_INVEST_PRICE_DAY ipd
                        WHERE 1=1 
              ]]>
             <if test=" account_id  != null  "><![CDATA[ AND  ipd.invest_id = #{account_id} ]]></if>
             
             <![CDATA[ 
            	UNION All
				     SELECT '资产管理费' AS TRANS_CODE, --变更类型
				            B.PRICING_DATE AS DEAL_TIME, -- 成交日期
				            ROUND((A.ACCUM_UNITS * NVL(B.UNIT_ASSET_M_FEE, 0)), 2) AS TRANS_AMOUNT, -- 交易金额
				            ROUND((A.ACCUM_UNITS * NVL(B.CAL_OFF_PRICE, 0)), 2) AS TRANS_INTEREST, --账户价值
				            A.ACCUM_UNITS AS UNIT_NUMBER, -- 单位数
				            NVL(B.CAL_OFF_PRICE, 0) AS UNIT_PRICE -- 单位价格
				       FROM DEV_PAS.T_CONTRACT_INVEST A, DEV_PAS.T_INVEST_UNIT_PRICE B
				      WHERE 1 = 1
				        AND A.ACCOUNT_CODE = B.INVEST_ACCOUNT_CODE
				        AND A.ACCOUNT_CODE = '892001' 
				        AND B.PRICING_DATE >= A.CREATE_DATE
              ]]>
             <if test=" account_id  != null  "><![CDATA[ AND A.LIST_ID = #{account_id} ]]></if>
             
             <![CDATA[ ) A) C  WHERE 1=1 ]]>
    </select>
    <!-- 投连账户轨迹分页查询操作 -->
    <!-- 投连账户轨迹分页查询操作 -->
    <select id="findUnitLinkedAccountPath" resultType="java.util.Map"
        parameterType="java.util.Map">
            <![CDATA[ 
            SELECT F.* FROM(
            SELECT E.* FROM(
            SELECT D.*,ROWNUM as RN FROM(
            SELECT C.*
            FROM(
                     select A.TRANS_CODE, --变更类型
			         A.deal_time, -- 成交日期
			         CAST(ABS(A.trans_amount) as decimal(18,2)) as trans_amount ,-- 交易金额
               		 CAST(A.TRANS_INTEREST as  decimal(18,2)) as TRANS_INTEREST, --账户价值
			         ROUND(A.UNIT_NUMBE,6) as UNIT_NUMBER, -- 单位数
			         A.UNIT_PRICE -- 单位价格
			          from (
					       select (select t.description
		                    from dev_pas.t_transaction_code t
		                   where t.trans_code = ft.trans_code) as trans_code, --变更类型
		                 	ft.deal_time, -- 成交日期
		                	ft.trans_amount, -- 交易金额
			                 (
			                     case 
			                       when ft.TRANS_TYPE = '1' then ft.balance_units_bf - ft.trans_amount
			                       when ft.TRANS_TYPE = '2' then ft.balance_units_bf + ft.trans_amount
			                       when ft.TRANS_TYPE = '3' then ft.balance_units_bf - ft.trans_amount
			                     else
			                       ft.balance_units_bf
			                     end
			                 ) as TRANS_INTEREST, --账户价值
			                 (
	                          case 
	                            when ft.trans_price = '0' then  null
	                            else
	                             (
	                             case 
	                               when ft.TRANS_TYPE = '1' then (ft.balance_units_bf - ft.trans_amount)/ft.fund_sell_price
	                               when ft.TRANS_TYPE = '2' then (ft.balance_units_bf + ft.trans_amount)/ft.fund_sell_price
	                               when ft.TRANS_TYPE = '3' then round(ft.balance_units_bf / ft.fund_sell_price, 6) - ft.trans_units
	                             else
	                               ft.balance_units_bf
	                             end
	                         )
	                          end
	                       ) as  UNIT_NUMBE , -- 单位数
			                 ft.fund_sell_price as UNIT_PRICE -- 单位价格
		            		from dev_pas.T_FUND_TRANS ft
		            		 WHERE 1=1 
              ]]>
             <if test=" account_id  != null"><![CDATA[ AND  ft.list_id = #{account_id} ]]></if> 
             
            <![CDATA[ 
             UNION All
			              select '投连日计价' as TRANS_CODE, --变更类型
			                 ipd.price_date as deal_time, -- 成交日期
			                 (ipd.last_invest_unit - ipd.init_invest_unit) *
			                 ipd.bid_price as TRANS_AMOUNT, -- 交易金额
			                 ipd.curr_balance as TRANS_INTEREST, -- --账户价值
			                 ipd.last_invest_unit as UNIT_NUMBER, --单位数
			                 ipd.bid_price as UNIT_PRICE --单位价格
			            from dev_pas.T_INVEST_PRICE_DAY ipd
                        WHERE 1=1 
              ]]>
             <if test=" account_id  != null "><![CDATA[ AND  ipd.invest_id = #{account_id} ]]></if>
             
             <![CDATA[ 
            	UNION All
				     SELECT '资产管理费' AS TRANS_CODE, --变更类型
				            B.PRICING_DATE AS DEAL_TIME, -- 成交日期
				            ROUND((A.ACCUM_UNITS * NVL(B.UNIT_ASSET_M_FEE, 0)), 2) AS TRANS_AMOUNT, -- 交易金额
				            ROUND((A.ACCUM_UNITS * NVL(B.CAL_OFF_PRICE, 0)), 2) AS TRANS_INTEREST, --账户价值
				            A.ACCUM_UNITS AS UNIT_NUMBER, -- 单位数
				            NVL(B.CAL_OFF_PRICE, 0) AS UNIT_PRICE -- 单位价格
				       FROM DEV_PAS.T_CONTRACT_INVEST A, DEV_PAS.T_INVEST_UNIT_PRICE B
				      WHERE 1 = 1
				        AND A.ACCOUNT_CODE = B.INVEST_ACCOUNT_CODE
				        AND A.ACCOUNT_CODE = '892001'
				        AND B.PRICING_DATE >= A.CREATE_DATE 
              ]]>
             <if test=" account_id  != null  "><![CDATA[ AND A.LIST_ID = #{account_id} ]]></if>
             
             <![CDATA[ ) A) C ORDER BY C.DEAL_TIME DESC) D ) E WHERE 1=1  and RN <= #{LESS_NUM})F
            WHERE F.RN > #{GREATER_NUM} ]]>
    </select>
    
        <!-- 根据保单号查询第二投保人信息 -->
    <select id="QRY_findSecondPolicyHolderInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT A.CUSTOMER_NAME,
       A.CUSTOMER_GENDER,
       A.CUSTOMER_CERT_TYPE,
       A.CUSTOMER_CERTI_CODE,
       A.CUSTOMER_BIRTHDAY,
       B.RELATION_TO_INSURED_1 AS DESIGNATION,
       C.MOBILE_TEL,
       C.ADDRESS,
       C.STATE,
       C.CITY,
       C.DISTRICT,
       DS.NAME || DC.NAME || DD.NAME || C.ADDRESS AS ADDRESS_FULL
  FROM DEV_PAS.T_CUSTOMER A
 INNER JOIN DEV_PAS.T_SECOND_POLICY_HOLDER B
    ON A.CUSTOMER_ID = B.CUSTOMER_ID
 INNER JOIN DEV_PAS.T_ADDRESS C
    ON B.ADDRESS_ID = C.ADDRESS_ID
  LEFT JOIN DEV_PAS.T_DISTRICT DS
    ON DS.CODE = C.STATE
  LEFT JOIN DEV_PAS.T_DISTRICT DC
    ON DC.CODE = C.CITY
  LEFT JOIN DEV_PAS.T_DISTRICT DD
    ON DD.CODE = C.DISTRICT
 WHERE 1=1
        ]]>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND B.POLICY_CODE= #{policy_code} ]]></if>
    </select>
    
      
    <select id="findDocumentInfosPage" resultType = "java.lang.Integer" parameterType = "java.util.Map">
    	<![CDATA[ SELECT COUNT(1) FROM (SELECT T.TEMPLATE_CODE,
               (SELECT UM.UW_SOURCE_TYPE
                  FROM DEV_UW.T_UW_MASTER UM
                 WHERE UM.UW_ID = T.BUSS_ID
                   AND ROWNUM = 1) AS BUSS_SOURCE_CODE,
               T.SEND_OBJ_ID,
               T.CREATE_TIME,
               T.CLOSE_TIME,
               (SELECT UU.USER_NAME
                  FROM DEV_UW.T_UDMP_USER UU
                 WHERE UU.USER_ID = T.SEND_BY) AS USER_NAME,
               T.STATUS,
               T.BUSS_CODE,
               T.DOCUMENT_NO
          FROM DEV_NB.T_DOCUMENT T
         WHERE  T.BUSS_CODE = #{buss_code}
           AND T.BUSS_SOURCE_CODE = '002')]]>
    </select>
        
    <!-- 接入渠道回执签收日期查询接口查询 -->
	<select id="QRY_findCallPolicyInfoByAcknowledge" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[
			SELECT A.CHANNEL_TYPE,
					B.LIST_ID,
					B.APPLY_CODE,/*投保单号*/
			       	B.POLICY_CODE,/*保单号*/
			       	B.CALL_RESULT_DATE_SYS as call_result_save_date, /*回访时间*/
			       	B.CALL_RESULT_SYS, /*回访结果*/
			       	B.CALL_FINISH, /*是否完成回访*/
			       	AC.ACKNOWLEDGE_DATE, /*回执回销日期*/
			       	C.CUSTOMER_NAME as appnt_name, /*投保人姓名*/
			       	C.CUSTOMER_CERT_TYPE as appnt_cert_type, /*投保人证件类型*/
			       	C.CUSTOMER_CERTI_CODE as appnt_cert_code /*投保人证件号码*/
			  FROM (SELECT * FROM    (SELECT A.*, ROW_NUMBER() OVER(PARTITION BY APPLY_CODE ORDER BY A.LIST_ID ASC) AS ROW_FLG
			                       FROM DEV_NB.T_CONTRACT_CALL A WHERE  A.CALL_FINISH='1'  ) TPA 
			             WHERE  TPA.ROW_FLG=1  
		]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPA.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" qry_start_date != null and qry_start_date != '' "><![CDATA[ and trunc(tpa.call_result_date_sys) >=  TO_DATE(TO_CHAR(#{qry_start_date},'yyyy-MM-dd'), 'yyyy-MM-dd') ]]></if>
		<if test=" qry_end_date != null and qry_end_date != '' "><![CDATA[ and trunc(tpa.call_result_date_sys) <=  TO_DATE(TO_CHAR(#{qry_end_date},'yyyy-MM-dd'), 'yyyy-MM-dd') ]]></if>
		<![CDATA[
				) B
			 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER A
			    ON A.APPLY_CODE=B.APPLY_CODE        
			 INNER JOIN DEV_NB.T_NB_POLICY_HOLDER PH
			    ON PH.APPLY_CODE = B.APPLY_CODE
			 INNER JOIN DEV_NB.T_CUSTOMER C
			    ON C.CUSTOMER_ID = PH.CUSTOMER_ID
			 LEFT JOIN DEV_NB.T_NB_POLICY_ACKNOWLEDGEMENT AC
			    ON AC.POLICY_CODE = B.POLICY_CODE
			 LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD P 
          	 	ON  A.APPLY_CODE = P.APPLY_CODE
			 LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT P1 
           		ON P.PRODUCT_CODE = P1.PRODUCT_CODE_SYS
			 WHERE A.CHANNEL_TYPE='03'
			  AND  (A.DOUBLE_MAINRISK_FLAG IS NULL OR A.DOUBLE_MAINRISK_FLAG = 0 OR (A.DOUBLE_MAINRISK_FLAG = 1 AND P1.PRODUCT_CATEGORY1 <> '20003')) /*双主险剔除万能险*/
        ]]>
		<if test=" service_bank != null and service_bank != '' "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
	</select>
	
	<!-- 万能型保险风险告知问卷 -->
	<select id="QRY_queryRiskQuestionnaire" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT Q.APPLY_CODE,Q.PRODUCT_CODE_SYS,Q.CARD_CODE FROM DEV_NB.T_POLICY_RISK_QUESTIONNAIRE Q WHERE 1=1 ]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND Q.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!-- 万能型保险风险告知问卷问题及答案查询 -->
	<select id="QRY_queryAllRiskQuestionnaire" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT 
				DISTINCT PR.PRODUCT_CODE,PR.APPLY_CODE,EE.RISK_QUESTIONNAIRE_ID,
				PP.PRODUCT_NAME_STD AS PRODUCT_NAME,ER.RISK_QUESTION AS QUESTION_DESCRIBE,
				ER.SELECTED_ANSWER AS SELECT_ANSWER,PP.PRODUCT_CATEGORY1,ER.RISK_QUESTION_ID
			FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD PR
				LEFT JOIN DEV_NB.T_BUSINESS_PRODUCT PP ON PP.PRODUCT_CODE_SYS = PR.PRODUCT_CODE
				LEFT JOIN DEV_NB.T_POLICY_RISK_QUESTIONNAIRE EE ON EE.APPLY_CODE = PR.APPLY_CODE
				LEFT JOIN DEV_NB.T_RISK_QUESTIONNAIRE_ANSWER ER ON ER.RISK_QUESTIONNAIRE_ID =  EE.RISK_QUESTIONNAIRE_ID 
			WHERE 1=1]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND PR.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" card_code != null and card_code != ''  "><![CDATA[ AND EE.CARD_CODE = #{card_code} ]]></if>
		<if test=" proCode != null and proCode != ''">
		<![CDATA[ AND PR.PRODUCT_CODE IN ]]>
		<foreach collection="proCode" index="index" item="productCode" open="(" separator="," close=")">
		 #{productCode}</foreach>
		 </if>
		 <![CDATA[ ORDER BY PR.PRODUCT_CODE,ER.RISK_QUESTION_ID ]]>
	</select>
	
	<!-- 查询投保单客户信息 -->
	<select id="QRY_queryNbContractCustomerHis" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.LIST_ID,A.POLICY_ID,
			       PH.APPLY_CODE,
			       A.CUSTOMER_ID, /**客户编码*/       
			       A.CUSTOMER_NAME, /*客户姓名*/
			       A.CUSTOMER_BIRTHDAY, /*客户出生日期*/
			       A.CUSTOMER_GENDER, /*客户性别*/
			       (SELECT G.GENDER_DESC
			          FROM DEV_NB.T_GENDER G
			         WHERE G.GENDER_CODE = A.CUSTOMER_GENDER) AS CUSTOMER_GENDER_DESC, /*客户性别描述*/
			       (SELECT CT.TYPE
			          FROM DEV_NB.T_CERTI_TYPE CT
			         WHERE CT.CODE = A.CUSTOMER_CERT_TYPE) AS CUSTOMER_CERT_TYPE_DESC, /*客户证件类型描述*/
			       A.CUSTOMER_CERT_TYPE, /*客户证件类型代码*/
			       A.CUSTOMER_CERTI_CODE, /*客户证件号码*/
			       A.CUST_CERT_STAR_DATE, /*证件有效期-起*/
			       A.CUST_CERT_END_DATE, /*证件有效期-至*/
			       (SELECT CL.CUSTOMER_LEVEL_DESC
			          FROM DEV_NB.T_CUSTOMER_LEVEL CL
			         WHERE CL.CUSTOMER_LEVEL_CODE = C.CUSTOMER_LEVEL) AS CUSTOMER_LEVEL, /*客户等级描述*/
			       C.CUSTOMER_RISK_LEVEL, /*客户风险等级*/
			       (SELECT CO.COUNTRY_NAME
			          FROM DEV_NB.T_COUNTRY CO
			         WHERE CO.COUNTRY_CODE = B.COUNTRY_CODE) AS COUNTRY_CODE, /*国籍*/
			       (SELECT M.MARRIAGE
			          FROM DEV_NB.T_MARRIAGE M
			         WHERE M.MARRIAGE_CODE = A.MARRIAGE_STATUS) AS MARRIAGE_STATUS, /*婚姻状况*/
			       A.IS_PARENT, /*子女状况*/
			       PH.JOB_CODE, /*职业代码*/
			       PH.JOB_UNDERWRITE AS JOB_KIND, /*职业所属行业性质--职业类别*/
			       A.JOB_TITLE, /*职务*/
			       C.JOB_NATURE, /*行业*/
			       (SELECT CT.LICENSE_DESC
			          FROM DEV_NB.T_LICENSE_TYPE CT
			         WHERE CT.LICENSE_TYPE = C.DRIVER_LICENSE_TYPE) AS DRIVER_LICENSE_TYPE, /*驾照类型*/
			       B.COMPANY_NAME, /*工作单位*/
			       B.Fixed_Tel as OFFICE_TEL,/* 固定电话 */
			       B.MOBILE_TEL,
			       (SELECT DT.NAME FROM DEV_NB.T_DISTRICT DT WHERE DT.CODE = B.STATE) AS STATE, /*省/直辖市*/
			       (SELECT DT.NAME FROM DEV_NB.T_DISTRICT DT WHERE DT.CODE = B.CITY) AS CITY, /*市*/
			       (SELECT DT.NAME FROM DEV_NB.T_DISTRICT DT WHERE DT.CODE = B.DISTRICT) AS DISTRICT, /*区县*/
			       B.ADDRESS, /*地址*/
			       B.POST_CODE, /*邮编*/
			       B.EMAIL,
			       B.ADDRESS_TYPE,
			       A.SECOND_CERT_TYPE, /* 第二证件类型*/
			       A.SECOND_CERTI_CODE, /*第二证件号码*/  
			       PH.AGENT_RELATION,
			       PH.ANNUAL_INCOME_CEIL,
			       PH.SOCI_SECU,
			       PH.RESIDENT_TYPE,
			       PH.INCOME_SOURCE,
			       PH.APPLICANT_SPE_PEOPLE,
			       TI.RETIREMENT_AGE,/*退休年龄*/
	               TI.INSURED_TYPE,/*参保人身份类型*/
	               TI.BUS_SRE_DEPT_CODE,/*所在单位的统一社会信用代码*/
	               TI.TAX_DISTRICT,/*税收地点*/
	               TI.TAX_NUMBER, /*纳税人识别号*/
	               A.TAX_RESIDENT_TYPE /*税收居民身份*/
			  FROM DEV_NB.T_NB_POLICY_HOLDER PH
			 INNER JOIN DEV_NB.T_NB_CONTRACT_CUSTOMER_HIS A
			    ON A.APPLY_CODE = PH.APPLY_CODE
			 INNER JOIN DEV_NB.T_ADDRESS B
			    ON B.ADDRESS_ID = PH.ADDRESS_ID
			 INNER JOIN DEV_NB.T_CUSTOMER C
			    ON C.CUSTOMER_ID = PH.CUSTOMER_ID
			 LEFT JOIN DEV_NB.T_NB_CUSTOMER_TAX_INFO TI 
          		ON TI.APPLY_CODE=PH.APPLY_CODE   
			 WHERE PH.LIST_ID = A.ROLE_ID
			   AND A.ROLE_TYPE = '01'
			   AND PH.APPLY_CODE =  #{apply_code}
		]]>
	</select>
		
	<select id="QRY_findNotifyInfoByBusiProd" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT cp.product_id as product_id,
			   bp.PRODUCT_CATEGORY1 as product_category,
		       t.policy_code as policy_code,
		       bp.product_code_sys as product_code,
		       bp.product_name_sys as product_name
		FROM APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD t, 
			 APP___PAS__DBUSER.t_business_product bp,
       		 APP___PAS__DBUSER.T_CONTRACT_PRODUCT cp
		WHERE t.busi_prd_id = bp.business_prd_id
		  and cp.busi_item_id = t.busi_item_id
		  and t.policy_code = #{policy_code}
	</select>
	
	<select id="QRY_findNotifyInfoDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
		select t.doc_list_id,
       t.document_name,
       t.list_id,
       t.generate_time,
       t.document_print_type,
       t.document_print_type_name,
       t.document_print_status,
       t.document_print_status_name,
       t.insert_time
  from (SELECT doc.doc_list_id,
               doc.document_name,
               dp.list_id,
               doc.create_time as generate_time,
               dp.document_print_type,
               (select t.name
                  from APP___PAS__DBUSER.T_DOCUMENT_PRINT_TYPE t
                 WHERE t.code = dp.document_print_type) document_print_type_name,
               dp.document_print_status,
               (select t.name
                  from APP___PAS__DBUSER.T_DOCUMENT_PRINT_STATUS t
                 WHERE t.code = dp.document_print_status) document_print_status_name,
               dp.insert_time
          FROM APP___PAS__DBUSER.T_DOCUMENT_PRINT_LOG dp, APP___PAS__DBUSER.t_document doc
         WHERE dp.doc_list_id = doc.doc_list_id
           and doc.policy_code = #{policy_code}
           and  doc.TEMPLATE_CODE = #{document_code}
        union
        SELECT doc.doc_list_id,
               doc.document_name,
               null              as list_id,
               doc.create_time   as generate_time,
               null              as document_print_type,
               null              as document_print_type_name,
               null              as document_print_status,
               null              as document_print_status_name,
               null              as insert_time
          FROM APP___PAS__DBUSER.t_document doc
         WHERE doc.policy_code = #{policy_code}
         and  doc.TEMPLATE_CODE = #{document_code}
           and (doc.status = '1' or doc.status = '2')) t
 order by t.document_name, t.generate_time desc,t.insert_time asc
	</select>
	
	<select id="QRY_findNotifyInfoLog" resultType="java.util.Map"
		parameterType="java.util.Map">
		 SELECT 
			t.print_id,
	        t.list_id,
	        t.print_by,
	        t.insert_time as print_time,
	        t.print_reason
   		FROM 
   			APP___PAS__DBUSER.T_DOCUMENT_PRINT_DETAIL_LOG t
  		WHERE 
  			t.list_id = #{list_id}
  		order by t.insert_time asc
	</select>
	
	<select id="QRY_findDocument" resultType="java.util.Map"
		parameterType="java.util.Map">
		SELECT     
		   doc.document_name,
           doc.create_time as generate_time
     	FROM 
           APP___PAS__DBUSER.t_document doc 
        WHERE
        	   doc.policy_code = #{policy_code}
		  and  doc.TEMPLATE_CODE = #{document_code}
		  and  doc.status = #{document_print_status}
		order by doc.create_time
	</select>
	
	<select id="QRY_findPolicyStatusTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
         <![CDATA[
 			SELECT COUNT(1) FROM DEV_PAS.T_POLICY_STATUS_TRACE TST  WHERE TST.POLICY_CODE =#{policy_code,jdbcType=VARCHAR}
          ]]>
    </select>
    
    <select id="QRY_findPolicyStatus" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[ 
        SELECT A.RN,
       A.POLICY_CODE,
       A.BUSI_ITEM_ID,
       A.BUSI_PROD_CODE,
       A.BUSI_PROD_NAME,
       A.LIABILITY_STATE,
       A.LIABILITY_STATUS,
       A.LIABILITY_CAUSE,
       A.NOTES,
       A.LIABILITY_START_DATE,
       A.LIABILITY_END_DATE
  FROM (SELECT ROWNUM RN, B.*
          FROM (SELECT TST.POLICY_CODE,
                       TST.BUSI_ITEM_ID,
                       TST.BUSI_PROD_CODE,
                       (SELECT BP.PRODUCT_NAME_STD FROM APP___PAS__DBUSER.T_BUSINESS_PRODUCT BP WHERE TST.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS) BUSI_PROD_NAME,
                       (SELECT TLS.STATUS_NAME FROM DEV_PAS.T_LIABILITY_STATUS TLS WHERE TLS.STATUS_CODE = TST.LIABILITY_STATE) LIABILITY_STATE,
                       (SELECT TS.TYPE_DESC FROM DEV_PAS.T_STATE_TYPE TS WHERE TS.STATE_TYPE = TST.STATE_TYPE) LIABILITY_STATUS,
                       (SELECT TSC.CAUSE_DESC FROM DEV_PAS.T_STATE_CAUSE TSC WHERE TSC.STATE_CAUSE = TST.STATE_CAUSE) LIABILITY_CAUSE,
                       TST.STATE_REMARK AS NOTES,
                       TST.START_DATE AS LIABILITY_START_DATE,
                       TST.END_DATE AS LIABILITY_END_DATE
                  FROM DEV_PAS.T_POLICY_STATUS_TRACE TST
                 WHERE TST.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} 
                 ORDER BY TST.UPDATE_TIME DESC ) B
         WHERE 1 = 1
           AND ROWNUM <= #{LESS_NUM}) A
 WHERE A.RN > #{GREATER_NUM}
    	]]>
    </select>
    <!--根据客户5要素查询客户税收居民身份  -->
	<select id="qry_queryCustomerTaxType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TAX_RESIDENT_TYPE
				  FROM DEV_PAS.T_CUSTOMER_TAX A
				 WHERE A.CUSTOMER_TAX_NAME = #{customer_name}
				   AND A.CUSTOMER_TAX_BIRTHDAY = #{customer_birthday}
				   AND A.CUSTOMER_TAX_GENDER = #{customer_gender}
				   AND A.CUSTOMER_TAX_CERT_TYPE = #{customer_cert_type}
				   AND A.CUSTOMER_TAX_CERTI_CODE = #{customer_certi_code}
    	 ]]>
	</select>
	  <!--  根据客户编号查询保单信息SQL(个人客户信息) -->
    <sql id="SQL_findPolicyInfoByCustomerIdOne">
       <![CDATA[
        SELECT 
           (CASE WHEN ( MINFO.DOUBLE_MAINRISK_FLAG ='1' AND MINFO.RELATION_POLICY_CODE IS NULL AND NCM.RELATION_POLICY_CODE IS NULL) THEN 
             (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER TNCM WHERE TNCM.RELATION_POLICY_CODE= MINFO.POLICY_CODE
             	AND TNCM.DOUBLE_MAINRISK_FLAG = '1'
             )
                 ELSE
                   MINFO.APPLY_CODE1
               END 
               ) AS APPLY_CODE,
            MINFO.*,
            (SELECT MIN(EX.PAY_DUE_DATE) FROM DEV_PAS.T_CONTRACT_EXTEND EX 
                    WHERE MINFO.BUSI_ITEM_ID=EX.BUSI_ITEM_ID AND MINFO.POLICY_ID=EX.POLICY_ID               
            ) AS PAY_DUE_DATE,
            CASE
				 WHEN MINFO.RISK_SCORE_F >=
							(SELECT T.RISK_SCORE_MIN
								 FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
								WHERE 1 = 1
									AND T.IS_VALID = '1') AND
							MINFO.RISK_SCORE_F <=
							(SELECT TC.RISK_SCORE_MAX
								 FROM DEV_PAS.T_RISK_LEVEL_CONFIG TC
								WHERE 1 = 1
									AND TC.IS_VALID = '1') THEN
					CONCAT('低|', MINFO.RISK_SCORE_F)
				 WHEN MINFO.RISK_SCORE_F >=
							(SELECT T.RISK_SCORE_MIN_HIGH
								 FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
								WHERE 1 = 1
									AND T.IS_VALID = '1') THEN
					CONCAT('高|', MINFO.RISK_SCORE_F)
				 WHEN MINFO.RISK_SCORE_F >=
							(SELECT T.RISK_SCORE_MIN_SECONDARY
								 FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
								WHERE 1 = 1
									AND T.IS_VALID = '1') AND
							MINFO.RISK_SCORE_F <=
							(SELECT T.RISK_SCORE_MAX_SECONDARY
								 FROM DEV_PAS.T_RISK_LEVEL_CONFIG T
								WHERE 1 = 1
									AND T.IS_VALID = '1') THEN
					CONCAT('中|', MINFO.RISK_SCORE_F)
				 ELSE
					''
			 END RISK_SCORE,
			(SELECT TO_CHAR(WMSYS.WM_CONCAT(BP.PRODUCT_NAME_SYS)) 
			   FROM DEV_PDS.T_BUSINESS_PRODUCT BP,
			        DEV_PAS.T_CONTRACT_BUSI_PROD TCBP 
			  WHERE BP.BUSINESS_PRD_ID = TCBP.BUSI_PRD_ID 
			    AND TCBP.POLICY_ID = MINFO.POLICY_ID 
			    AND TCBP.MASTER_BUSI_ITEM_ID IS NULL) AS PRODUCT_NAME_SYS,
            (SELECT 
            (
            CASE WHEN COUNT(*)>0  THEN '投保人,' ELSE '' END
            ) FROM DEV_PAS.T_POLICY_HOLDER PH 
            WHERE PH.CUSTOMER_ID=#{customer_id} AND PH.POLICY_ID=MINFO.POLICY_ID) AS HOLDER,
            (SELECT CASE WHEN COUNT(*)>0  THEN '被保险人,' ELSE '' END FROM DEV_PAS.T_INSURED_LIST NIL 
            WHERE NIL.CUSTOMER_ID=#{customer_id} AND NIL.POLICY_ID=MINFO.POLICY_ID) AS INSURED,
            (SELECT CASE WHEN COUNT(*)>0  THEN '受益人' ELSE '' END FROM DEV_PAS.T_CONTRACT_BENE CB 
            WHERE CB.CUSTOMER_ID=#{customer_id} AND CB.POLICY_ID=MINFO.POLICY_ID) AS BENE,
            CA.AGENT_CODE
        FROM(
          SELECT
            AC.POLICY_ID,
            CM.DOUBLE_MAINRISK_FLAG,
            CM.APPLY_CODE AS APPLY_CODE1,
            CM.RELATION_POLICY_CODE,
            CM.POLICY_CODE,
            CM.POLICY_TYPE,
            CM.LIABILITY_STATE,
            CM.ORGAN_CODE ,
            CM.VALIDATE_DATE,
            CM.EXPIRY_DATE,
            CM.Subinput_Type,
            (SELECT 
            NBP.BUSI_ITEM_ID
             FROM
            DEV_PAS.T_CONTRACT_BUSI_PROD NBP
            WHERE NBP.POLICY_ID=CM.POLICY_ID AND NBP.MASTER_BUSI_ITEM_ID IS NULL AND ROWNUM=1) AS BUSI_ITEM_ID,
            (SELECT TO_CHAR(WMSYS.WM_CONCAT(DISTINCT CU.CUSTOMER_NAME)) FROM DEV_PAS.T_POLICY_HOLDER NPH 
            INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NPH.CUSTOMER_ID
            WHERE NPH.POLICY_ID=CM.POLICY_ID ) AS HOLDER_NAME,
            (SELECT 
            TO_CHAR(WMSYS.WM_CONCAT(DISTINCT CU.CUSTOMER_NAME))
            FROM 
            DEV_PAS.T_BENEFIT_INSURED NBI
            INNER JOIN DEV_PAS.T_INSURED_LIST NIL 
            ON NBI.INSURED_ID=NIL.LIST_ID
            INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NIL.CUSTOMER_ID
             WHERE NIL.POLICY_ID=CM.POLICY_ID
            ) AS INSURED_NAME,
            (SELECT 
            TO_CHAR(WMSYS.WM_CONCAT(DISTINCT CU.CUSTOMER_NAME))
            FROM 
            DEV_PAS.T_CONTRACT_BENE NCB
            INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NCB.CUSTOMER_ID
             WHERE NCB.POLICY_ID=CM.POLICY_ID 
            ) AS BENE_NAME,
            PA.PAY_NEXT,
            CM.SUBMIT_CHANNEL, --50694功能增加
            (SELECT MAX(CBP.RISK_SCORE) 
               FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP 
              WHERE CBP.POLICY_ID = CM.POLICY_ID 
                AND CBP.MASTER_BUSI_ITEM_ID IS NULL) AS RISK_SCORE_F
            FROM    
            (SELECT 
            NPH.APPLY_CODE,NPH.POLICY_ID
            FROM 
            DEV_PAS.T_POLICY_HOLDER NPH 
            WHERE 1=1
            AND NPH.CUSTOMER_ID=#{customer_id}
            UNION 
            SELECT 
            NIL.APPLY_CODE,NIL.POLICY_ID
            FROM 
            DEV_PAS.T_BENEFIT_INSURED NBI
            INNER JOIN DEV_PAS.T_INSURED_LIST NIL 
            ON NBI.INSURED_ID=NIL.LIST_ID 
            WHERE 1=1
            AND NIL.CUSTOMER_ID=#{customer_id}) AC 
            INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM ON 
            CM.POLICY_ID=AC.POLICY_ID
            LEFT JOIN DEV_PAS.T_PAYER_ACCOUNT PA ON 
            PA.POLICY_ID=CM.POLICY_ID 
            WHERE 1= 1 ]]>
            <if test="  insured_name != null and insured_name != ''  ">
            <![CDATA[ AND EXISTS (SELECT  'X' FROM DEV_PAS.T_BENEFIT_INSURED NBI
                                  INNER JOIN DEV_PAS.T_INSURED_LIST NIL 
                                   ON NBI.INSURED_ID=NIL.LIST_ID
                                  INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NIL.CUSTOMER_ID
                                  WHERE NIL.POLICY_ID=CM.POLICY_ID and CU.CUSTOMER_NAME = #{insured_name})
             ]]>
            </if>
            <if test="  holder_name != null and holder_name != ''  ">
            <![CDATA[ AND EXISTS (SELECT 'X' FROM DEV_PAS.T_POLICY_HOLDER NPH 
                                  INNER JOIN DEV_PAS.T_CUSTOMER CU ON CU.CUSTOMER_ID=NPH.CUSTOMER_ID
                                  WHERE NPH.POLICY_ID=CM.POLICY_ID and CU.CUSTOMER_NAME = #{holder_name})
            ]]>
            </if>
           <![CDATA[  ) MINFO     
        LEFT JOIN DEV_PAS.T_contract_agent CA ON MINFO.POLICY_ID=CA.POLICY_ID AND CA.IS_CURRENT_AGENT = '1'
        LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON MINFO.POLICY_CODE = NCM.POLICY_CODE
        WHERE 1=1
        ]]>
        <if test=" agent_code != null and agent_code != ''  "><![CDATA[AND CA.AGENT_CODE = #{agent_code}]]></if>
        <if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[AND MINFO.ORGAN_CODE = #{service_bank_branch}]]></if>
        <if test=" pay_next != null and pay_next != ''  "><![CDATA[AND MINFO.PAY_NEXT = #{pay_next}]]></if>
        <if test=" liability_state != null">
        <![CDATA[AND MINFO.LIABILITY_STATE = #{liability_state}]]>
        </if>
        <if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[AND EXISTS (SELECT 1 FROM DEV_PAS.T_CONTRACT_BUSI_PROD BP WHERE BP.POLICY_ID = MINFO.POLICY_ID AND BP.BUSI_PROD_CODE = #{busi_prod_code} AND BP.MASTER_BUSI_ITEM_ID IS NULL )]]></if>
        <if test=" validate_date != null and validate_date != ''  "><![CDATA[AND MINFO.VALIDATE_DATE =#{validate_date}]]></if>
    </sql>
	<!-- 根据客户编号查询保单信息的总数(个人客户信息) -->
	<select id="QRY_findPolicyInfoByCustomerIdOneTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(1) FROM 
        (
        ]]>
        <include refid="SQL_findPolicyInfoByCustomerIdOne" />
        <![CDATA[
         ) B
		]]>
	</select>
	 <!-- 根据客户编号查询保单相关信息(个人客户信息) -->
	<select id="QRY_findPolicyInfoByCustomerIdOne" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
        SELECT B.RN ,
          B.APPLY_CODE,
          B.POLICY_CODE,
          TRIM(',' from CONCAT(CONCAT(HOLDER,INSURED),BENE)) AS POLICY_ROLE,
          B.LIABILITY_STATE,
          B.PRODUCT_NAME_SYS,
          B.VALIDATE_DATE,
          B.EXPIRY_DATE,
          B.PAY_DUE_DATE,
          B.HOLDER_NAME,
          B.INSURED_NAME,
          B.BENE_NAME,
          B.Subinput_Type,
          B.ORGAN_CODE,
          B.AGENT_CODE,
          B.RISK_SCORE
        FROM
        (
            SELECT ROWNUM AS RN,C.* FROM (
            ]]>
            <include refid="SQL_findPolicyInfoByCustomerIdOne" />
        <![CDATA[
            AND (MINFO.liABILITY_STATE = 0 OR MINFO.liABILITY_STATE =1 OR MINFO.liABILITY_STATE = 3 OR MINFO.liABILITY_STATE = 4)
            ORDER BY MINFO.VALIDATE_DATE  ASC) C
        )B WHERE B.RN > #{GREATER_NUM} AND B.RN <= #{LESS_NUM}
        
        ]]>
	</select>
	
	<!-- 根据保单号查询收付费实收实付缴费退费信息信息  -->
	<select id="QRY_queryCapCashDetailByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT A.UNIT_NUMBER,
               A.BUSINESS_CODE,
		       A.FINISH_TIME,
		       A.BUSINESS_TYPE,
		       A.ARAP_FLAG,
		       A.PAID_COUNT,
		       ABS(A.FEE_AMOUNT) FEE_AMOUNT,
		       A.PAY_MODE,
		       A.ACTUAL_BANK_CODE,
		       A.ACTUAL_BANK_ACCOUNT,
		       A.BANK_USER_NAME,
		       A.DERIV_TYPE,
		       (CASE WHEN (A.DERIV_TYPE IN ('004') AND A.BUSINESS_TYPE IN ('2017', '2022', '2046', '2060')) 
		           THEN (SELECT TSA.HESITATE_FLAG FROM DEV_PAS.T_CS_ACCEPT_CHANGE TSA WHERE TSA.ACCEPT_CODE = A.BUSINESS_CODE)
		       END) HESITATE_FLAG
		  FROM (SELECT T.UNIT_NUMBER,
		               MAX(T.BUSINESS_CODE) BUSINESS_CODE,
		               MAX(T.FINISH_TIME) FINISH_TIME,
		               MAX(T.BUSINESS_TYPE) BUSINESS_TYPE,
		               MAX(T.ARAP_FLAG) ARAP_FLAG,
		               MAX(T.PAID_COUNT) PAID_COUNT,
		               SUM(CASE WHEN T.ARAP_FLAG = '1' THEN T.FEE_AMOUNT ELSE T.FEE_AMOUNT * -1 END) FEE_AMOUNT,
		               MAX(T.PAY_MODE) PAY_MODE,
		               MAX(T.ACTUAL_BANK_CODE) ACTUAL_BANK_CODE,
		               MAX(T.ACTUAL_BANK_ACCOUNT) ACTUAL_BANK_ACCOUNT,
		               MAX(T.BANK_USER_NAME) BANK_USER_NAME,
		               MAX(T.DERIV_TYPE) DERIV_TYPE
		          FROM DEV_CAP.V_CASH_DETAIL T
		         WHERE T.POLICY_CODE = #{POLICY_CODE}
		         GROUP BY T.UNIT_NUMBER) A
		]]>
	</select>
	  <select id="findAgentInfoFromInternet" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	SELECT CA.AGENT_CODE,
				       A.AGENT_NAME,
				       A.AGENT_ORGAN_CODE,
				       1 AS IS_CURRENT_AGENT,
				       0 AS IS_NB_AGENT,
				       CA.CHANNEL_TYPE AS AGENT_CHANNEL,
				       TAI.AGENT_COM,
				        CA.COOPERATION_CODE AS OTHER_MANAGE_COM,
				       CA.COOPERATION_NAME AS OTHER_MANAGE_NAME,
				       CA.COOPERATION_PROTOCOL_ID AS PROTOCOL_ID /*#159647协议号(互联网渠道)*/
				  FROM DEV_PAS.T_CONTRACT_AGENT CA
				 INNER JOIN DEV_PAS.T_AGENT A
				    ON CA.AGENT_CODE = A.AGENT_CODE
				  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER NCM
				    ON CA.POLICY_CODE = NCM.POLICY_CODE
				  LEFT JOIN DEV_PAS.T_AGENT_INTERMEDIARY TAI
				    ON A.AGENT_CODE = TAI.AGENT_CODE
				   AND TAI.STATE = 'Y'
				  LEFT JOIN DEV_PAS.T_AGENT_MEDIATION_AGR TAMA
				    ON TAI.AGENT_COM = TAMA.MEDIATION
				   AND TAMA.BRANCH_TYPE = '12'
				 WHERE CA.POLICY_CODE = #{policy_code}
				   AND CA.IS_CURRENT_AGENT = 1
				   AND EXISTS (SELECT 1 FROM DEV_PAS.T_CONTRACT_AGENT TCA WHERE TCA.POLICY_CODE = CA.POLICY_CODE AND TCA.IS_NB_AGENT = 1 AND TCA.CHANNEL_TYPE = '14')
				UNION
				SELECT CA.AGENT_CODE,
				       A.AGENT_NAME,
				       A.AGENT_ORGAN_CODE,
				       0 AS IS_CURRENT_AGENT,
				       1 AS IS_NB_AGENT,
				       CA.CHANNEL_TYPE AS AGENT_CHANNEL,
				       TAI.AGENT_COM,
				       CA.COOPERATION_CODE AS OTHER_MANAGE_COM,
				       CA.COOPERATION_NAME AS OTHER_MANAGE_NAME,
				       CA.COOPERATION_PROTOCOL_ID AS PROTOCOL_ID /*#159647协议号(互联网渠道)*/
				  FROM DEV_PAS.T_CONTRACT_AGENT CA
				 INNER JOIN DEV_PAS.T_AGENT A
				    ON CA.AGENT_CODE = A.AGENT_CODE
				  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER NCM
				    ON CA.POLICY_CODE = NCM.POLICY_CODE
				  LEFT JOIN DEV_PAS.T_AGENT_INTERMEDIARY TAI
				    ON A.AGENT_CODE = TAI.AGENT_CODE
				   AND TAI.STATE = 'Y'
				  LEFT JOIN DEV_PAS.T_AGENT_MEDIATION_AGR TAMA
				    ON TAI.AGENT_COM = TAMA.MEDIATION
				   AND TAMA.BRANCH_TYPE = '12'
				 WHERE CA.POLICY_CODE = #{policy_code}
				   AND CA.IS_NB_AGENT = 1
				   AND CA.CHANNEL_TYPE = '14'
		 ]]>
      </select>

	<!-- 根据保单号查询睡眠保单信息   -->
	<select id="QRY_queryPolicyMarkingInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.POLICY_CODE,
			       (CASE WHEN A.SLEEP_TYPE IS NULL THEN NULL ELSE A.SLEEP_TYPE || '.' || ST.TYPE_NAME END) AS SLEEP_TYPE,/* 睡眠保单类型 */
			       A.BUSI_PROD_CODE || BP.PRODUCT_ABBR_NAME AS BUSI_PROD_CODE,/* 险种 */
			       (CASE WHEN A.SLEEP_TYPE IS NULL THEN NULL ELSE A.DEAL_STATUS||'.'||MDS.STATUS_NAME||' '||TO_CHAR(A.DEAL_TIME, 'yyyy-MM-dd hh24:mi:ss') END) AS DEAL_STATUS, /* 睡眠保单处理状态 */
			       ENI.EFFECT_NOTICE_INFO_DESC AS EFFECT_NOTICE_INFO /* 睡眠保单有效通知情况 */
			  FROM DEV_PAS.T_POLICY_MARKING_INFO A
			  LEFT JOIN DEV_PAS.T_SLEEP_TYPE ST
			    ON ST.SLEEP_TYPE = A.SLEEP_TYPE
			  LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
			    ON BP.PRODUCT_CODE_SYS = A.BUSI_PROD_CODE
			  LEFT JOIN DEV_PAS.T_MARK_DEAL_STATUS MDS
			    ON MDS.DEAL_STATUS = A.DEAL_STATUS
			  LEFT JOIN DEV_PAS.T_EFFECT_NOTICE_INFO ENI
			    ON ENI.EFFECT_NOTICE_INFO = A.EFFECT_NOTICE_INFO
			 WHERE A.POLICY_CODE = #{policy_code}
			 ORDER BY A.POLICY_CODE, A.BUSI_PROD_CODE, A.DEAL_TIME
		]]>
	</select>
	 <select id="QRY_queryCooperationProtocolIdByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
      SELECT A.COOPERATION_PROTOCOL_ID,A.COOPERATION_CODE,A.COOPERATION_NAME  FROM DEV_NB.T_NB_CONTRACT_AGENT  A
       WHERE 1=1 AND A.APPLY_CODE  = #{apply_code} 
      <if test=" policy_code != null and policy_code != ''  "><![CDATA[AND A.POLICY_CODE=#{policy_code} ]]></if>
      </select>
	
	<!-- 根据投保单号查询最新一条官微银保通影像质检，质检不通过信息 -->
	<select id="QRY_queryNbQtTaskInfoByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
				SELECT * FROM (
					SELECT B.RESION_CODE FROM DEV_NB.T_NB_QT_TASK A 
					LEFT JOIN DEV_NB.T_NB_ESQUOALITYDECISON B ON A.TASK_ID = B.TASK_ID
					WHERE A.QA_TYPE='A'  AND A.QT_STATUS IN('4','9') AND B.ES_QT_RESULT = '0'
					AND A.APPLY_CODE = #{apply_code} ORDER BY B.ES_QUOALI_ID DESC) A WHERE ROWNUM=1
		]]>
	</select>
	
	
	
	<!--根据参数名称查询综合查询库参数配置信息  -->
	<select id="qry_queryParaDefInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEPT_ID, A.PARA_ID, A.MODULE_ID, A.SYSTEM_ID, A.SINGLE_PARA_VALUE, A.END_DATE, A.PARA_VALUE, 
			A.DEPT_RELA, A.PARA_NAME, A.PARA_DESC, A.SYSTEM_ADMIN, A.START_DATE, A.ORGAN_RELA, A.TIME_RELA, 
			A.SCOPE_CODE, A.PARA_VALUE_NAME, A.DATA_TYPE, A.ORGAN_ID FROM QUERY.T_UDMP_PARA_DEF A WHERE 1 = 1  ]]>
		<if test=" para_name != null and para_name != '' "><![CDATA[ AND A.PARA_NAME = #{para_name} ]]></if>
		<![CDATA[ ORDER BY A.PARA_ID ]]>
	</select>
	
	<!-- 投保单明细险种信息显示：是否录入了有关联关系的万能险查询 -->
	<select id="QRY_queryContractRelationByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MASTER_BUSI_PROD_CODE, A.MASTER_POLICY_CODE, A.SUB_BUSI_PROD_CODE, A.SUB_POLICY_CODE,
       		A.RELATION_TYPE, A.MASTER_BUSI_ITEM_ID
  			FROM APP___NB__DBUSER.T_NB_CONTRACT_RELATION A WHERE  A.RELATION_TYPE IN ('2', '3', '4')
  			AND A.SUB_BUSI_ITEM_ID =#{sub_busi_item_id} AND A.MASTER_POLICY_ID = #{master_policy_id} AND A.SUB_POLICY_ID = #{sub_policy_id}
			]]>
	</select>
	
	<!--根据保单号查询保单类型  -->
    <select id="QRY_findMediaTypeByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
        SELECT 2 MEDIA_TYPE
        FROM DEV_PAS.T_CS_ACCEPT_CHANGE A,
             DEV_PAS.T_CS_POLICY_CHANGE B,
             DEV_PAS.T_CONTRACT_MASTER  M
        WHERE 1 = 1
          AND B.ACCEPT_ID = A.ACCEPT_ID
          AND ((A.SERVICE_CODE IN ('PT', 'PU') AND B.REPRINT_FLAG = 1) OR A.SERVICE_CODE = 'RN')
          AND M.POLICY_CODE = B.POLICY_CODE
          AND A.ACCEPT_STATUS = '18'
          AND M.MEDIA_TYPE = 1
          AND M.POLICY_CODE = #{policy_code}
        ]]>
	</select>
    <!-- RM:145518 2023年新核心保全复核环节系统功能优化（1）保全 END -->
    <!-- 查询险种的被保人姓名 -->
	<select id="findBusiItemInsureds" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	select b.busi_item_id,
				       listagg(case
			                   when b.product_code in (select tbp.product_code_sys from dev_pas.t_business_product tbp where tbp.waiver_customer_role='01') then
			                    (Select cm.customer_name
			                       From dev_pas.t_Policy_Holder h, dev_pas.t_Customer cm
			                      where h.customer_id = cm.customer_id
			                        and h.policy_id = b.policy_id
			                        and rownum = 1)
			                   else
			                    c.customer_name
			                 end,
			                 '、') WITHIN GROUP(ORDER BY b.order_id) insured_names,
       				   listagg(c.customer_id, '_') WITHIN GROUP(ORDER BY b.order_id) insured_ids
				  from dev_pas.T_BENEFIT_INSURED b
				  join dev_pas.t_insured_list i
				    on i.policy_id = b.policy_id
				   and i.list_id = b.insured_id
				  join dev_pas.t_customer c
				    on c.customer_id = i.customer_id
				 where rownum < 1000
				   and b.policy_code = #{policy_code}
				 group by b.busi_item_id ]]> 
	</select>
    
</mapper>
