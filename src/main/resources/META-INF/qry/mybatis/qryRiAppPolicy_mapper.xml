<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UWRiAppPolicyDaoImpl">
<!--
	<sql id="riAppPolicyWhereCondition">
		<if test=" apply_ri_user  != null "><![CDATA[ AND A.APPLY_RI_USER = #{apply_ri_user} ]]></if>
		<if test=" apply_ri_time  != null  and  apply_ri_time  != ''  "><![CDATA[ AND A.APPLY_RI_TIME = #{apply_ri_time} ]]></if>
		<if test=" reply_advice_comment != null and reply_advice_comment != ''  "><![CDATA[ AND A.REPLY_ADVICE_COMMENT = #{reply_advice_comment} ]]></if>
		<if test=" is_reply  != null "><![CDATA[ AND A.IS_REPLY = #{is_reply} ]]></if>
		<if test=" reply_user  != null "><![CDATA[ AND A.REPLY_USER = #{reply_user} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" ri_app_comment != null and ri_app_comment != ''  "><![CDATA[ AND A.RI_APP_COMMENT = #{ri_app_comment} ]]></if>
		<if test=" reply_add_info_comment != null and reply_add_info_comment != ''  "><![CDATA[ AND A.REPLY_ADD_INFO_COMMENT = #{reply_add_info_comment} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" ri_id  != null "><![CDATA[ AND A.RI_ID = #{ri_id} ]]></if>
		<if test=" ri_num  != null "><![CDATA[ AND A.RI_NUM = #{ri_num} ]]></if>
		<if test=" reply_time  != null  and  reply_time  != ''  "><![CDATA[ AND A.REPLY_TIME = #{reply_time} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" reaction_time  != null  and  reaction_time  != ''  "><![CDATA[ AND A.REACTION_TIME = #{reaction_time} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryRiAppPolicyByRiIdCondition">
		<if test=" ri_id  != null "><![CDATA[ AND A.RI_ID = #{ri_id} ]]></if>
	</sql>	
	<sql id="queryRiAppPolicyByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="queryRiAppPolicyByUWIDCondition">
		<if test=" uw_id != null and uw_id != '' "><![CDATA[ AND A.uw_id = #{uw_id} ]]></if>
	</sql>	
<!-- 添加操作 -->
	<insert id="addRiAppPolicy"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO T_RI_APP_POLICY(
				APPLY_RI_USER, APPLY_RI_TIME, INSERT_TIME, REPLY_ADVICE_COMMENT, IS_REPLY, UPDATE_TIME, REPLY_USER, 
				APPLY_CODE, INSERT_TIMESTAMP, RI_APP_COMMENT, REPLY_ADD_INFO_COMMENT, POLICY_CODE, UPDATE_BY, RI_ID, 
				RI_NUM, UPDATE_TIMESTAMP, INSERT_BY, REPLY_TIME, POLICY_ID, REACTION_TIME ) 
			VALUES (
				#{apply_ri_user, jdbcType=NUMERIC}, #{apply_ri_time, jdbcType=DATE} , SYSDATE , #{reply_advice_comment, jdbcType=VARCHAR} , #{is_reply, jdbcType=NUMERIC} , SYSDATE , #{reply_user, jdbcType=NUMERIC} 
				, #{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{ri_app_comment, jdbcType=VARCHAR} , #{reply_add_info_comment, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{ri_id, jdbcType=NUMERIC} 
				, #{ri_num, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{reply_time, jdbcType=DATE} , #{policy_id, jdbcType=NUMERIC} , #{reaction_time, jdbcType=DATE} ) 
		 ]]>
	</insert>
	
	<insert id="adduwRiAppPolicyInfo"  useGeneratedKeys="true"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="ri_id"> SELECT S_T_RI_APP_POLICY.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO T_RI_APP_POLICY(
				RI_ID, UW_ID, APPLY_CODE, POLICY_ID, POLICY_CODE, RI_NUM, RI_APP_COMMENT, 
				REPLY_USER, REPLY_TIME, REPLY_ADVICE_COMMENT, REPLY_ADD_INFO_COMMENT, REACTION_TIME, IS_REPLY, 
				APPLY_RI_USER, APPLY_RI_TIME,INSERT_TIMESTAMP,INSERT_TIME,INSERT_BY,UPDATE_TIMESTAMP,UPDATE_TIME,UPDATE_BY) 
			VALUES (
				#{ri_id, jdbcType=NUMERIC}, #{uw_id, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{policy_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{ri_num, jdbcType=NUMERIC} , #{ri_app_comment, jdbcType=VARCHAR} 
				, #{reply_user, jdbcType=VARCHAR} , #{reply_time, jdbcType=DATE} , #{reply_advice_comment, jdbcType=VARCHAR} , #{reply_add_info_comment, jdbcType=VARCHAR} , #{reaction_time, jdbcType=DATE} , #{is_reply, jdbcType=NUMERIC} 
				, #{apply_ri_user, jdbcType=NUMERIC} , #{apply_ri_time, jdbcType=DATE},CURRENT_TIMESTAMP,SYSDATE,#{insert_by, jdbcType=NUMERIC},
				CURRENT_TIMESTAMP,SYSDATE,#{update_by, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteRiAppPolicy" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_RI_APP_POLICY WHERE RI_ID = #{ri_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateRiAppPolicy" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_RI_APP_POLICY ]]>
		<set>
		<trim suffixOverrides=",">
		    APPLY_RI_USER = #{apply_ri_user, jdbcType=NUMERIC} ,
		    APPLY_RI_TIME = #{apply_ri_time, jdbcType=DATE} ,
			REPLY_ADVICE_COMMENT = #{reply_advice_comment, jdbcType=VARCHAR} ,
		    IS_REPLY = #{is_reply, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    REPLY_USER = #{reply_user, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			RI_APP_COMMENT = #{ri_app_comment, jdbcType=VARCHAR} ,
			REPLY_ADD_INFO_COMMENT = #{reply_add_info_comment, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    RI_NUM = #{ri_num, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    REPLY_TIME = #{reply_time, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    REACTION_TIME = #{reaction_time, jdbcType=DATE} ,
		</trim>
		</set>
		<![CDATA[ WHERE RI_ID = #{ri_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findRiAppPolicyByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_RI_USER, A.APPLY_RI_TIME, A.REPLY_ADVICE_COMMENT, A.IS_REPLY, A.REPLY_USER, 
			A.APPLY_CODE, A.RI_APP_COMMENT, A.REPLY_ADD_INFO_COMMENT, A.POLICY_CODE, A.RI_ID, 
			A.RI_NUM, A.REPLY_TIME, A.POLICY_ID, A.REACTION_TIME,(select reply_advice from t_ri_app_product where ri_id = a.ri_id) reply_advice,
			(select reply_advice_amount from t_ri_app_product where ri_id = a.ri_id) reply_advice_amount,(select reply_advice_extra_point from t_ri_app_product where ri_id = a.ri_id) reply_advice_extra_point,
			(select busi_prod_code from t_ri_app_product where ri_id = a.ri_id) busi_prod_code
			 FROM T_RI_APP_POLICY A WHERE ri_num=(select max(ri_num) from t_ri_app_policy a where 1=1 ]]>
			 <include refid="queryRiAppPolicyByApplyCodeCondition" />
			 <include refid="queryRiAppPolicyByUWIDCondition" />	 
			 <![CDATA[ ) ]]>
		<include refid="queryRiAppPolicyByApplyCodeCondition" />
		<include refid="queryRiAppPolicyByUWIDCondition" />
		<![CDATA[ ORDER BY A.RI_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapRiAppPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_RI_USER, A.APPLY_RI_TIME, A.REPLY_ADVICE_COMMENT, A.IS_REPLY, A.REPLY_USER, 
			A.APPLY_CODE, A.RI_APP_COMMENT, A.REPLY_ADD_INFO_COMMENT, A.POLICY_CODE, A.RI_ID, 
			A.RI_NUM, A.REPLY_TIME, A.POLICY_ID, A.REACTION_TIME FROM T_RI_APP_POLICY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RI_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllRiAppPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_RI_USER, A.APPLY_RI_TIME, A.REPLY_ADVICE_COMMENT, A.IS_REPLY, A.REPLY_USER, 
			A.APPLY_CODE, A.RI_APP_COMMENT, A.REPLY_ADD_INFO_COMMENT, A.POLICY_CODE, A.RI_ID, 
			A.RI_NUM, A.REPLY_TIME, A.POLICY_ID, A.REACTION_TIME FROM T_RI_APP_POLICY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RI_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findRiAppPolicyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM dev_uw.T_RI_APP_POLICY A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="queryRiAppPolicyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.APPLY_RI_USER, B.APPLY_RI_TIME, B.REPLY_ADVICE_COMMENT, B.IS_REPLY, B.REPLY_USER, 
			B.APPLY_CODE, B.RI_APP_COMMENT, B.REPLY_ADD_INFO_COMMENT, B.POLICY_CODE, B.RI_ID, 
			B.RI_NUM, B.REPLY_TIME, B.POLICY_ID, B.REACTION_TIME FROM (
					SELECT ROWNUM RN, A.APPLY_RI_USER, A.APPLY_RI_TIME, A.REPLY_ADVICE_COMMENT, A.IS_REPLY, A.REPLY_USER, 
			A.APPLY_CODE, A.RI_APP_COMMENT, A.REPLY_ADD_INFO_COMMENT, A.POLICY_CODE, A.RI_ID, 
			A.RI_NUM, A.REPLY_TIME, A.POLICY_ID, A.REACTION_TIME FROM T_RI_APP_POLICY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RI_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!--Modify by xuhp 按指定条件查询再保保单表信息  2016年3月5日 -->
	<select id="findRiAppPolicyByAppointCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_RI_USER, A.APPLY_RI_TIME, A.REPLY_ADVICE_COMMENT, A.IS_REPLY, A.REPLY_USER, 
			A.APPLY_CODE, A.RI_APP_COMMENT, A.REPLY_ADD_INFO_COMMENT, A.POLICY_CODE, A.RI_ID, 
			A.RI_NUM, A.REPLY_TIME, A.POLICY_ID, A.REACTION_TIME,(select reply_advice from t_ri_app_product where ri_id = a.ri_id) reply_advice,uw_id FROM T_RI_APP_POLICY A WHERE 
			ri_num=(select max(ri_num) from t_ri_app_policy a  where 1=1 ]]>
			<include refid="queryRiAppPolicyByRiIdCondition" />	
			<include refid="queryRiAppPolicyByApplyCodeCondition" />
			<include refid="queryRiAppPolicyByUWIDCondition" />
		<![CDATA[ ) ]]>
		<include refid="queryRiAppPolicyByRiIdCondition" />	
		<include refid="queryRiAppPolicyByApplyCodeCondition" />
		<include refid="queryRiAppPolicyByUWIDCondition" />
		<![CDATA[ ORDER BY A.RI_ID ]]>
	</select>
	
	<!-- Modify by xuhp 按指定条件修改再保保单表数据 2016年3月9日 -->
	<update id="updateRiAppPolicyByAppointCondition" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_RI_APP_POLICY a ]]>
		<set>
		<trim suffixOverrides=",">
			<if test=" apply_ri_user  != null "><![CDATA[ apply_ri_user = #{apply_ri_user} , ]]></if>
			<if test=" apply_ri_time  != null "><![CDATA[ apply_ri_time = #{apply_ri_time} , ]]></if>
			<if test=" reply_advice_comment  != null "><![CDATA[ reply_advice_comment = #{reply_advice_comment}  ,]]></if>
			<if test=" is_reply  != null "><![CDATA[ is_reply = #{is_reply} , ]]></if>
			<if test=" reply_user  != null "><![CDATA[ reply_user = #{reply_user} , ]]></if>
			<if test=" ri_app_comment  != null "><![CDATA[ ri_app_comment = #{ri_app_comment}  ,]]></if>
			<if test=" reply_add_info_comment  != null "><![CDATA[ reply_add_info_comment = #{reply_add_info_comment} , ]]></if>
			<if test=" ri_num  != null "><![CDATA[ ri_num = #{ri_num} , ]]></if>
			<if test=" reply_time  != null "><![CDATA[ reply_time = #{reply_time} , ]]></if>
			<if test=" reaction_time  != null "><![CDATA[ reaction_time = #{reaction_time} , ]]></if>
			UPDATE_TIME = SYSDATE , 
			UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE 1=1 ]]>
		<include refid="queryRiAppPolicyByRiIdCondition" />	
		<include refid="queryRiAppPolicyByApplyCodeCondition" />
		<include refid="queryRiAppPolicyByUWIDCondition" />
	</update>
	
	<!--Modify by xuhp 再次呈报查询呈报保单信息  2016年4月8日 -->
	<select id="findRiAppPolicyByNextReportCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.ri_id,a.uw_id,a.apply_code,a.policy_id,a.policy_code,a.ri_num,a.ri_app_comment,a.apply_ri_user,a.apply_ri_time,A.IS_REPLY 
				from t_ri_app_policy a where 1=1 ]]> 
		<include refid="queryRiAppPolicyByRiIdCondition"/>	
		<include refid="queryRiAppPolicyByApplyCodeCondition"/>
		<include refid="queryRiAppPolicyByUWIDCondition"/>
		<![CDATA[and ri_num=(select max(ri_num) from t_ri_app_policy a where  1=1 ]]>
		<include refid="queryRiAppPolicyByRiIdCondition" />	
		<include refid="queryRiAppPolicyByApplyCodeCondition" />
		<include refid="queryRiAppPolicyByUWIDCondition" /> 
		<![CDATA[ ) ]]>
	</select>
	
	<!--by zhaoyoan_wb 通过投保单号查询  2016年4月21日 -->
	<select id="UW_findByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.*,(select b.user_name from DEV_PAS.T_UDMP_USER b where b.user_id = a.REPLY_USER) REPLY_USER_NAME,
                       (select b.user_name from DEV_PAS.T_UDMP_USER b where b.user_id = a.APPLY_RI_USER) APPLY_RI_USER_NAME 
			FROM DEV_UW.T_RI_APP_POLICY A 
			WHERE A.APPLY_CODE=#{apply_code} 
		]]>
	</select>
	
		<!--通过保单号查询 -->
	<select id="findByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT A.* 
			FROM DEV_UW.T_RI_APP_POLICY A 
			WHERE A.POLICY_CODE=#{apply_code} 
		]]>
	</select>
	
	
	<!--再保回复通过投保单号查询险种呈报信息-->
	<select id="findRiProductListByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT  A.*,
		      (select rrc.reply_code_desc from dev_uw.T_RI_REPLY_CODE rrc where rrc.reply_code=a.reply_advice ) reply_advice_name,
		      (select n.ri_app_reason_desc from dev_nb.t_ri_app_reason n where n.ri_app_reason=a.ri_app_reason) as app_reason,
		       b.ri_app_comment,
		      (SELECT PRODUCT_ID FROM DEV_UW.T_CONTRACT_PRODUCT 
		        WHERE PRODUCT_CODE=A.PRODUCT_CODE AND APPLY_CODE=A.APPLY_CODE AND ROWNUM=1) AS product_id,
		      (SELECT BUSI_PRD_ID FROM DEV_UW.T_CONTRACT_BUSI_PROD 
		        WHERE A.APPLY_CODE=APPLY_CODE AND A.BUSI_ITEM_ID=BUSI_ITEM_ID) AS busi_prd_id,
		      (SELECT PRODUCT_NAME_SYS FROM DEV_PAS.T_BUSINESS_PRODUCT WHERE BUSINESS_PRD_ID=
		        (SELECT BUSI_PRD_ID FROM DEV_UW.T_CONTRACT_BUSI_PROD 
		          WHERE A.APPLY_CODE=APPLY_CODE AND A.BUSI_ITEM_ID=BUSI_ITEM_ID)) AS product_name_sys
		      FROM DEV_UW.T_RI_APP_PRODUCT A ,dev_uw.t_ri_app_policy b 
		      WHERE a.ri_id=b.ri_id 
		      AND a.apply_code= #{apply_code}
		]]>
	</select>
	
			<!--再保回复通过保单号查询险种呈报信息-->
	<select id="findRiProductListByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			
			select a.* ,
              (select rrc.reply_code_desc from dev_uw.T_RI_REPLY_CODE rrc where rrc.reply_code= b.reply_advice) as reply_advice_name,
              (SELECT PRODUCT_ID FROM DEV_UW.T_CONTRACT_PRODUCT 
               WHERE PRODUCT_CODE=b.PRODUCT_CODE AND APPLY_CODE=b.APPLY_CODE AND ROWNUM=1) AS product_id,
              (SELECT BUSI_PRD_ID FROM DEV_UW.T_CONTRACT_BUSI_PROD 
              WHERE b.APPLY_CODE=APPLY_CODE AND b.BUSI_ITEM_ID=BUSI_ITEM_ID) AS busi_prd_id,
              (SELECT PRODUCT_NAME_SYS FROM DEV_PAS.T_BUSINESS_PRODUCT WHERE BUSINESS_PRD_ID=
               (SELECT BUSI_PRD_ID FROM DEV_UW.T_CONTRACT_BUSI_PROD 
                WHERE b.APPLY_CODE=APPLY_CODE AND b.BUSI_ITEM_ID=BUSI_ITEM_ID)) AS product_name_sys
             from 
              dev_uw.t_uw_master um , 
              dev_uw.t_ri_app_policy a,
              dev_uw.t_ri_app_product b         
             where  1=1
             and um.uw_id=a.uw_id
             and a.ri_id = b.ri_id             
             and um.biz_code = (
                 select csa.accept_code from dev_pas.t_cs_policy_change csp  ,dev_pas.t_cs_accept_change csa  
                 where   csp.change_id = csa.change_id
                 and csp.policy_code=#{apply_code} )
  	]]>
	</select>
</mapper>