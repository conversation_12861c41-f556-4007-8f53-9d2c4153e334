<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="policyAcknowledgement">

	<sql id="PA_policyAcknowledgementWhereCondition">
		<if test=" operator_id  != null "><![CDATA[ AND A.OPERATOR_ID = #{operator_id} ]]></if>
		<if test=" branch_receive_date  != null  and  branch_receive_date  != ''  "><![CDATA[ AND A.BRANCH_RECEIVE_DATE = #{branch_receive_date} ]]></if>
		<if test=" return_reason != null and return_reason != ''  "><![CDATA[ AND A.RETURN_REASON = #{return_reason} ]]></if>
		<if test=" acknowledge_date  != null  and  acknowledge_date  != ''  "><![CDATA[ AND A.ACKNOWLEDGE_DATE = #{acknowledge_date} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" dc_collect_date  != null  and  dc_collect_date  != ''  "><![CDATA[ AND A.DC_COLLECT_DATE = #{dc_collect_date} ]]></if>
		<if test=" dispatch_date  != null  and  dispatch_date  != ''  "><![CDATA[ AND A.DISPATCH_DATE = #{dispatch_date} ]]></if>
		<if test=" return_desc != null and return_desc != ''  "><![CDATA[ AND A.RETURN_DESC = #{return_desc} ]]></if>
		<if test=" reminder_date  != null  and  reminder_date  != ''  "><![CDATA[ AND A.REMINDER_DATE = #{reminder_date} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryPolicyAcknowledgementByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	


<!-- 按索引查询操作 -->	
	<select id="PA_findPolicyAcknowledgementByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyAcknowledgementByPolicyIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyAcknowledgement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyAcknowledgement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT A WHERE ROWNUM <=  1000  ]]>
		 <include refid="PA_policyAcknowledgementWhereCondition" /> 
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyAcknowledgementTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyAcknowledgementForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.OPERATOR_ID, B.BRANCH_RECEIVE_DATE, B.RETURN_REASON, B.ACKNOWLEDGE_DATE, B.MEDIA_TYPE, 
			B.DC_COLLECT_DATE, B.DISPATCH_DATE, B.RETURN_DESC, B.REMINDER_DATE, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询单条 -->
		<select id="PA_findPolicyAcknowledgement" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_ID, A.BRANCH_RECEIVE_DATE, A.RETURN_REASON, A.ACKNOWLEDGE_DATE, A.MEDIA_TYPE, 
			A.DC_COLLECT_DATE, A.DISPATCH_DATE, A.RETURN_DESC, A.REMINDER_DATE, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT A WHERE ROWNUM <=  1000  ]]>
<!-- 		<include refid="" /> -->
		<include refid="PA_queryPolicyAcknowledgementByPolicyIdCondition" />
	</select>
</mapper>
