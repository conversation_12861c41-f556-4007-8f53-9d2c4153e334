<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.AskforinfoDaoImpl">
<!--
	<sql id="UW_askforinfoWhereCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" askforinfo_id  != null "><![CDATA[ AND A.ASKFORINFO_ID = #{askforinfo_id} ]]></if>
		<if test=" role_type != null and role_type != ''  "><![CDATA[ AND A.ROLE_TYPE = #{role_type} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" GENDER_CODE != null and GENDER_CODE != ''  "><![CDATA[ AND A.GENDER_CODE = #{GENDER_CODE} ]]></if>
		<if test=" customer_birth  != null  and  customer_birth  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTH = #{customer_birth} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="UW_queryAskforinfoByAskforinfoIdCondition">
		<if test=" askforinfo_id  != null "><![CDATA[ AND A.ASKFORINFO_ID = #{askforinfo_id} ]]></if>
	</sql>	
	<sql id="UW_queryAskforinfoByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="UW_queryAskforinfoByDocListIdCondition">
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	</sql>	
	<sql id="UW_queryAskforinfoByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="UW_queryAskforinfoByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="UW_addAskforinfo"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="askforinfo_id">SELECT DEV_UW.S_T_ASKFORINFO.NEXTVAL FROM DUAL</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_ASKFORINFO                        (
				UW_ID, CUSTOMER_NAME, DOC_LIST_ID, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, ASKFORINFO_ID, 
				ROLE_TYPE, INSERT_TIMESTAMP, UPDATE_BY, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, GENDER_CODE, 
				CUSTOMER_BIRTH,apply_code,policy_code,STATUS, DOCUMENT_NO) 
			VALUES (
				#{uw_id, jdbcType=NUMERIC}, #{customer_name, jdbcType=VARCHAR} , #{doc_list_id, jdbcType=NUMERIC} , SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{askforinfo_id, jdbcType=NUMERIC} 
				, #{role_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{gender_code, jdbcType=NUMERIC} 
				, #{customer_birth, jdbcType=DATE},#{apply_code, jdbcType=VARCHAR},#{policy_code, jdbcType=VARCHAR},#{status,jdbcType=VARCHAR},#{document_no,jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="UW_deleteAskforinfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_ASKFORINFO                         WHERE ASKFORINFO_ID           = #{askforinfo_id          } ]]>
	</delete>

<!-- 修改操作 -->
	<update id="UW_updateAskforinfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_ASKFORINFO                         ]]>
		<set>
		<trim suffixOverrides=",">
		    UW_ID = #{uw_id, jdbcType=NUMERIC} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
		    DOC_LIST_ID = #{doc_list_id, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    ASKFORINFO_ID = #{askforinfo_id, jdbcType=NUMERIC} ,
			ROLE_TYPE = #{role_type, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			GENDER_CODE = #{gender_code, jdbcType=VARCHAR} ,
		    CUSTOMER_BIRTH = #{customer_birth, jdbcType=DATE} ,
		    DOCUMENT_NO = #{document_no, jdbcType=VARCHAR} ,
		    POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE ASKFORINFO_ID           = #{askforinfo_id          } ]]>
	</update>
	
<!-- 按索引查询操作 -->	
	<select id="UW_findAskforinfoByAskforinfoId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.ASKFORINFO_ID, 
			A.ROLE_TYPE, A.POLICY_ID, A.GENDER_CODE, A.DOCUMENT_NO, 
			A.CUSTOMER_BIRTH FROM DEV_UW.T_ASKFORINFO                         A WHERE 1 = 1  ]]>
		<include refid="UW_queryAskforinfoByAskforinfoIdCondition" />
		<![CDATA[ ORDER BY A.ASKFORINFO_ID           ]]>
	</select>
	
	<select id="UW_findAskforinfoByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.ASKFORINFO_ID, 
			A.ROLE_TYPE, A.POLICY_ID, A.GENDER_CODE, A.DOCUMENT_NO, 
			A.CUSTOMER_BIRTH FROM DEV_UW.T_ASKFORINFO                         A WHERE 1 = 1  ]]>
		<include refid="UW_queryAskforinfoByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.ASKFORINFO_ID           ]]>
	</select>
	
	<select id="UW_findAskforinfoByDocListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.ASKFORINFO_ID, 
			A.ROLE_TYPE, A.POLICY_ID, A.GENDER_CODE, A.APPLY_CODE,A.POLICY_CODE, A.DOCUMENT_NO, 
			A.CUSTOMER_BIRTH FROM DEV_UW.T_ASKFORINFO  A WHERE 1 = 1  ]]>
		<include refid="UW_queryAskforinfoByDocListIdCondition" />
		<if test="document_no  != null "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<![CDATA[ ORDER BY A.ASKFORINFO_ID           ]]>
	</select>
	
	<select id="UW_findResidentInfoByDocListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.RESIDENTINFO_ID, 
			A.CUSTOMER_ROLE_TYPE, A.POLICY_ID, A.CUSTOMER_GENDER, A.APPLY_CODE,A.POLICY_CODE, A.DOCUMENT_NO, 
			A.CUSTOMER_BIRTHDAY FROM DEV_UW.T_RESIDENTINFO  A WHERE 1 = 1  ]]>
		<include refid="UW_queryAskforinfoByDocListIdCondition" />
		<if test="document_no  != null "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<![CDATA[ ORDER BY A.RESIDENTINFO_ID           ]]>
	</select>
	
	
	<select id="UW_findAskforinfoByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.ASKFORINFO_ID, 
			A.ROLE_TYPE, A.POLICY_ID, A.GENDER_CODE, A.DOCUMENT_NO, 
			A.CUSTOMER_BIRTH FROM DEV_UW.T_ASKFORINFO                         A WHERE 1 = 1  ]]>
		<include refid="UW_queryAskforinfoByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.ASKFORINFO_ID           ]]>
	</select>
	
	<select id="UW_findAskforinfoByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.ASKFORINFO_ID, 
			A.ROLE_TYPE, A.POLICY_ID, A.GENDER_CODE, A.DOCUMENT_NO, 
			A.CUSTOMER_BIRTH FROM DEV_UW.T_ASKFORINFO                         A WHERE 1 = 1  ]]>
		<include refid="UW_queryAskforinfoByUwIdCondition" />
		<![CDATA[ ORDER BY A.ASKFORINFO_ID           ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="UW_findAllMapAskforinfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.ASKFORINFO_ID, 
			A.ROLE_TYPE, A.POLICY_ID, A.GENDER_CODE, A.DOCUMENT_NO, 
			A.CUSTOMER_BIRTH FROM DEV_UW.T_ASKFORINFO                         A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ASKFORINFO_ID           ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="UW_findAllAskforinfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.ASKFORINFO_ID, 
			A.ROLE_TYPE, A.POLICY_ID, A.GENDER_CODE, A.DOCUMENT_NO, 
			A.CUSTOMER_BIRTH FROM DEV_UW.T_ASKFORINFO                         A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ASKFORINFO_ID           ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="UW_findAskforinfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_ASKFORINFO                         A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="UW_queryAskforinfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UW_ID, B.CUSTOMER_NAME, B.DOC_LIST_ID, B.CUSTOMER_ID, B.ASKFORINFO_ID, 
			B.ROLE_TYPE, B.POLICY_ID, B.GENDER_CODE, 
			B.CUSTOMER_BIRTH FROM (
					SELECT ROWNUM RN, A.UW_ID, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.CUSTOMER_ID, A.ASKFORINFO_ID, 
			A.ROLE_TYPE, A.POLICY_ID, A.GENDER_CODE, 
			A.CUSTOMER_BIRTH FROM DEV_UW.T_ASKFORINFO                         A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ASKFORINFO_ID           ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<update id="updateDocumentStatusByDocListId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_ASKFORINFO       A                  ]]>
		<set>
		<trim suffixOverrides=",">
			STATUS = #{status, jdbcType=VARCHAR},
		</trim>
		</set>
		 	WHERE 1 = 1 
		 	<if test=" doc_list_id  != null "><![CDATA[AND DOC_LIST_ID = #{doc_list_id} ]]></if>
		 	<if test=" document_no  != null "><![CDATA[AND DOCUMENT_NO = #{document_no} ]]></if>
	</update>
	
	<update id="updateResidentDocumentStatusByDocListId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_RESIDENTINFO       A                  ]]>
		<set>
		<trim suffixOverrides=",">
			DOCUMENT_STATUS = #{status, jdbcType=VARCHAR},
		</trim>
		</set>
		 	WHERE 1 = 1 
		 	<if test=" doc_list_id  != null "><![CDATA[AND DOC_LIST_ID = #{doc_list_id} ]]></if>
		 	<if test=" document_no  != null "><![CDATA[AND DOCUMENT_NO = #{document_no} ]]></if>
	</update>
	
	<!-- 根据通知书号删除索要资料/问卷通知书 -->
	<delete id="UW_deleteAskforinfoByDocumentNO" parameterType="java.util.Map">
		<![CDATA[DELETE DEV_UW.T_ASKFORINFO WHERE DOC_LIST_ID = #{doc_list_id}]]>
	</delete>
	
	
	<!-- 根据通知书号删除纳税身份变动确认声明表通知书 -->
	<delete id="UW_deleteResidentInfoByDocumentNO" parameterType="java.util.Map">
		<![CDATA[DELETE DEV_UW.T_RESIDENTINFO WHERE DOC_LIST_ID = #{doc_list_id}]]>
	</delete>
	
	
	
	<update id="UW_updateAskforDocumentNO" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_ASKFORINFO ]]>
		<set>
		<trim suffixOverrides=",">
			DOCUMENT_NO = #{newDocument_no, jdbcType=VARCHAR},
		</trim>
		</set>
		 	<![CDATA[WHERE DOCUMENT_NO = #{document_no} AND APPLY_CODE = #{apply_code}]]>
	</update>
	
	<!-- 增加数据  国民纳税身份变动确认声明表  --><!-- ASKFORINFO_ID、 ROLE_TYPE、GENDER_CODE、CUSTOMER_BIRTH、STATUS-->
	<insert id="UW_addResidentforinfo"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="residentinfo_id">
			SELECT DEV_UW.S_T_ASKFORINFO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_RESIDENTINFO(
				RESIDENTINFO_ID,UW_ID, CUSTOMER_NAME, INSERT_TIME, CUSTOMER_ID, UPDATE_TIME, 
				INSERT_TIMESTAMP, UPDATE_BY, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, 
				DOCUMENT_NO,DOC_LIST_ID,APPLY_CODE,POLICY_CODE,CUSTOMER_GENDER,CUSTOMER_BIRTHDAY,DOCUMENT_STATUS,CUSTOMER_ROLE_TYPE) 
			VALUES (
				#{residentinfo_id, jdbcType=NUMERIC},#{uw_id, jdbcType=NUMERIC}, #{customer_name, jdbcType=VARCHAR} ,SYSDATE , #{customer_id, jdbcType=NUMERIC} , SYSDATE
				,CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC}
				,#{document_no,jdbcType=VARCHAR}, #{doc_list_id, jdbcType=NUMERIC},#{apply_code,jdbcType=VARCHAR}
				,#{policy_code,jdbcType=VARCHAR}, #{customer_gender, jdbcType=NUMERIC}, #{customer_birthday, jdbcType=TIMESTAMP}
				,#{document_status,jdbcType=VARCHAR}, #{customer_role_type, jdbcType=NUMERIC}
				) 
		 ]]>
	</insert>
	
	<!-- 上海医保卡产品核保通知书 --><!-- 查询是否已有通知书 -->
	<select id="UW_checkIsHasDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT UW_ID,DOCUMENT_NO,DOCUMENT_DESC  FROM DEV_UW.T_MEDICAL_DOCINFO
				WHERE 1=1 AND UW_ID=#{uw_id } AND DOCUMENT_NO=#{document_no } 
		 ]]>
	</select>
	
	<!-- 上海医保卡产品核保通知书 --><!-- 保存通知书 -->
	<insert id="UW_addHealthcareNoticeInfo" useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="medical_id">
			SELECT DEV_UW.S_MEDICAL_DOCINFO.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_MEDICAL_DOCINFO(
				MEDICAL_ID,UW_ID,DOC_LIST_ID,DOCUMENT_NO,DOCUMENT_DESC,APPLY_CODE,POLICY_ID,
				POLICY_CODE,ROLE_TYPE,CUSTOMER_NAME,CUSTOMER_ID,MOBILE,STATUS,INSERT_BY,
				UPDATE_BY,INSERT_TIME,UPDATE_TIME,INSERT_TIMESTAMP,UPDATE_TIMESTAMP) 
			VALUES (
				#{medical_id, jdbcType=NUMERIC},#{uw_id, jdbcType=NUMERIC},#{doc_list_id, jdbcType=NUMERIC},#{document_no, jdbcType=VARCHAR},
				#{document_desc, jdbcType=VARCHAR},#{apply_code, jdbcType=VARCHAR},#{policy_id, jdbcType=NUMERIC},
				#{policy_code, jdbcType=VARCHAR},#{role_type, jdbcType=NUMERIC},#{customer_name, jdbcType=VARCHAR},
				#{customer_id, jdbcType=NUMERIC},#{mobile, jdbcType=VARCHAR},#{status, jdbcType=VARCHAR},#{insert_by, jdbcType=NUMERIC},
				#{update_by, jdbcType=NUMERIC},SYSDATE,SYSDATE,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP )
		]]>
	</insert>
	
	<!-- 上海医保卡产品核保通知书 --><!-- 修改通知书 -->
	<update id="UW_updateHealthcareNoticeInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_MEDICAL_DOCINFO ]]>
		<set>
			DOCUMENT_DESC = #{document_desc, jdbcType=VARCHAR},
		</set>
		 	WHERE 1 = 1 
		 	<if test=" doc_list_id  != null "><![CDATA[AND DOC_LIST_ID = #{doc_list_id} ]]></if>
		 	<if test=" document_no  != null "><![CDATA[AND DOCUMENT_NO = #{document_no} ]]></if>
	</update>
	
	<!-- 上海医保卡产品核保通知书 --><!-- 修改通知书状态 -->
	<update id="UW_updateHealthcareNoticeInfoStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_MEDICAL_DOCINFO ]]>
		<set>
			STATUS = #{status, jdbcType=VARCHAR},
		</set>
		 	WHERE 1 = 1 
		 	<if test=" doc_list_id  != null "><![CDATA[AND DOC_LIST_ID = #{doc_list_id} ]]></if>
		 	<if test=" document_no  != null "><![CDATA[AND DOCUMENT_NO = #{document_no} ]]></if>
	</update>
	
	<!-- 上海医保卡产品核保通知书 --><!-- 查询通知书 -->
	<select id="UW_queryHealthcareInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT MEDICAL_ID,UW_ID,DOC_LIST_ID,DOCUMENT_NO,DOCUMENT_DESC,APPLY_CODE,POLICY_ID,
			POLICY_CODE,ROLE_TYPE,CUSTOMER_NAME,CUSTOMER_ID,MOBILE,STATUS FROM DEV_UW.T_MEDICAL_DOCINFO
			WHERE 1=1 AND DOC_LIST_ID = #{doc_list_id, jdbcType=NUMERIC} AND DOCUMENT_NO = #{document_no, jdbcType=VARCHAR}
		]]>
	</select>

	<!-- 查询处在待打印的通知书信息 -->
	<select id="UW_findDocumentCannelTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(*) FROM (]]>
		
		<if test=" template_code  != null and template_code != '' "><![CDATA[ 
			SELECT A.DOC_LIST_ID
	          FROM DEV_UW.T_DOCUMENT A, DEV_UW.T_NB_CONTRACT_MASTER B
	         WHERE A.BUSS_CODE = B.APPLY_CODE
	           AND A.SEND_BY = #{send_by}
	           AND A.BUSS_SOURCE_CODE = '002'
	           AND A.POLICY_CODE IS NULL
	           AND A.TEMPLATE_CODE IN (${template_code})
	           AND A.STATUS = '2'
	           AND B.PROPOSAL_STATUS = '22']]>
           	   <if test=" apply_code != null and apply_code != ''"><![CDATA[AND A.BUSS_CODE = #{apply_code} ]]></if>
         </if>
           
       <if test=" new_template_code  != null and new_template_code != '' ">
       		<if test=" template_code  != null and template_code != '' ">UNION</if>
       		
	        <![CDATA[ SELECT A.DOC_LIST_ID
	          FROM DEV_UW.T_DOCUMENT A, DEV_UW.T_NB_CONTRACT_MASTER B
	         WHERE A.BUSS_CODE = B.APPLY_CODE
	           AND A.BUSS_SOURCE_CODE = '002'
	           AND A.SEND_BY = #{send_by}
	           AND A.POLICY_CODE IS NULL
	           AND A.TEMPLATE_CODE = 'UN012'
	           AND A.STATUS = '5'
	           AND B.PROPOSAL_STATUS = '22']]>
	           <if test=" apply_code != null and apply_code != ''"><![CDATA[AND A.BUSS_CODE = #{apply_code} ]]></if>
         </if>
           ) D
	</select>
	
	<!-- 分页查询操作 -->
	<select id="UW_findDocumentCannelForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT ROWNUM AS RN, T.* FROM (SELECT *
                   FROM (]]>
              	<if test=" template_code  != null and template_code != '' "><![CDATA[ 
                   SELECT A.DOC_LIST_ID,
                                A.DOCUMENT_NO,
                                A.TEMPLATE_CODE,
                                A.BUSS_ID AS UW_ID,
                                A.BUSS_CODE AS APPLY_CODE,
                                A.SEND_TIME,
                                CASE
                                  WHEN A.TEMPLATE_CODE = 'UWS_00003' THEN
                                   '2'
                                  WHEN A.TEMPLATE_CODE = 'UWS_00004' THEN
                                   '1'
                                END TEMPLATE_S
                           FROM DEV_UW.T_DOCUMENT           A,
                                DEV_UW.T_NB_CONTRACT_MASTER B
                          WHERE A.BUSS_CODE = B.APPLY_CODE
                            AND A.SEND_BY = #{send_by}
				           	AND A.BUSS_SOURCE_CODE = '002'
				           	AND A.POLICY_CODE IS NULL
				           	AND A.TEMPLATE_CODE IN (${template_code})
				           	AND A.STATUS = '2'
				           	AND B.PROPOSAL_STATUS = '22']]>
			           	   	<if test=" apply_code != null and apply_code != ''"><![CDATA[AND A.BUSS_CODE = #{apply_code} ]]></if>
			     </if>
			     <if test=" new_template_code  != null and new_template_code != '' ">
                         <if test=" template_code  != null and template_code != '' ">UNION</if>
                         
                        <![CDATA[SELECT A.DOC_LIST_ID,
                                A.DOCUMENT_NO,
                                A.TEMPLATE_CODE,
                                A.BUSS_ID AS UW_ID,
                                A.BUSS_CODE AS APPLY_CODE,
                                A.SEND_TIME,
                                '3' AS TEMPLATE_S
                           FROM DEV_UW.T_DOCUMENT           A,
                                DEV_UW.T_NB_CONTRACT_MASTER B
                          WHERE A.BUSS_CODE = B.APPLY_CODE
                            AND A.BUSS_SOURCE_CODE = '002'
				           	AND A.SEND_BY = #{send_by}
				           	AND A.POLICY_CODE IS NULL
				           	AND A.TEMPLATE_CODE = 'UN012'
				           	AND A.STATUS = '5'
				           	AND B.PROPOSAL_STATUS = '22']]>
				           	<if test=" apply_code != null and apply_code != ''"><![CDATA[AND A.BUSS_CODE = #{apply_code} ]]></if>
			         </if>
                     <![CDATA[) D
                  ORDER BY D.APPLY_CODE, D.TEMPLATE_S) T WHERE ROWNUM <= #{LESS_NUM}) N WHERE N.RN > #{GREATER_NUM}
         ]]>
	</select>
	
	<!-- 查询通知书状态 -->
	<select id="UW_queryDocumentStatusForReply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT CASE WHEN T2.STATUS IS NOT NULL THEN T2.STATUS
		         WHEN T3.STATUS IS NOT NULL THEN T3.STATUS
		         WHEN T4.STATUS IS NOT NULL THEN T4.STATUS
		         WHEN T5.DOCUMENT_STATUS IS NOT NULL THEN T5.DOCUMENT_STATUS END STATUS
		  FROM DEV_UW.T_DOCUMENT T1 LEFT JOIN DEV_UW.T_ASKFORINFO T2 ON T1.DOC_LIST_ID = T2.DOC_LIST_ID
		  LEFT JOIN DEV_UW.T_PENOTICE T3 ON T1.DOC_LIST_ID = T3.DOC_LIST_ID
		  LEFT JOIN DEV_UW.T_SURVIVAL_INVESTIGATION T4 ON T1.DOC_LIST_ID = T4.DOC_LIST_ID
		  LEFT JOIN DEV_UW.T_RESIDENTINFO T5 ON T1.DOC_LIST_ID = T5.DOC_LIST_ID
		 	WHERE T1.BUSS_SOURCE_CODE = '002']]>
		   <![CDATA[AND T1.DOCUMENT_NO = #{document_no}]]>
	</select>
	
	<select id="QRY_findResidentinfoDetailById" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DETAIL_ID,
				       A.RESIDENTINFO_ID,
				       A.CUSTOMER_ROLE_TYPE,
				       A.RES_QUESTION_CODE,
				       A.RES_REASON,
				       A.CUSTOMER_ID,
				       (SELECT C.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER C WHERE C.CUSTOMER_ID = A.CUSTOMER_ID) AS CUSTOMER_NAME
				  FROM DEV_UW.T_RESIDENTINFO_DETAIL A WHERE 1 = 1  ]]>
				  AND A.RESIDENTINFO_ID = #{residentinfo_id}
		<![CDATA[ ORDER BY A.DETAIL_ID               ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="QRY_findAllUwDocumentVerify" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ROLE_TYPE, A.DOCUMENT_NO, A.UW_ID, A.LIST_ID, A.CUSTOMER_ID,
		 (SELECT C.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER C WHERE C.CUSTOMER_ID = A.CUSTOMER_ID) AS CUSTOMER_NAME
		 FROM DEV_UW.T_UW_DOCUMENT_VERIFY A WHERE 1 = 1  ]]>
			AND DOCUMENT_NO = #{document_no}
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
</mapper>
