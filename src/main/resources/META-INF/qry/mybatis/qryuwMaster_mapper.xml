<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwMasterDaoImpl">
<!--
	<sql id="uwMasterWhereCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" previous_uw_time  != null  and  previous_uw_time  != ''  "><![CDATA[ AND A.PREVIOUS_UW_TIME = #{previous_uw_time} ]]></if>
		<if test=" previous_uw_user  != null "><![CDATA[ AND A.PREVIOUS_UW_USER = #{previous_uw_user} ]]></if>
		<if test=" uw_submit_time  != null  and  uw_submit_time  != ''  "><![CDATA[ AND <PERSON>.UW_<PERSON>UBMIT_TIME = #{uw_submit_time} ]]></if>
		<if test=" uw_level_code != null and uw_level_code != ''  "><![CDATA[ AND A.UW_LEVEL_CODE = #{uw_level_code} ]]></if>
		<if test=" uw_status != null and uw_status != ''  "><![CDATA[ AND A.UW_STATUS = #{uw_status} ]]></if>
		<if test=" biz_date  != null  and  biz_date  != ''  "><![CDATA[ AND A.BIZ_DATE = #{biz_date} ]]></if>
		<if test=" uw_finish_time  != null  and  uw_finish_time  != ''  "><![CDATA[ AND A.UW_FINISH_TIME = #{uw_finish_time} ]]></if>
		<if test=" ri_tel_falg  != null "><![CDATA[ AND A.RI_TEL_FALG = #{ri_tel_falg} ]]></if>
		<if test=" uw_over_indi  != null "><![CDATA[ AND A.UW_OVER_INDI = #{uw_over_indi} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" uw_amend_indi  != null "><![CDATA[ AND A.UW_AMEND_INDI = #{uw_amend_indi} ]]></if>
		<if test=" apply_level_code != null and apply_level_code != ''  "><![CDATA[ AND A.APPLY_LEVEL_CODE = #{apply_level_code} ]]></if>
		<if test=" uw_cancel_cause != null and uw_cancel_cause != ''  "><![CDATA[ AND A.UW_CANCEL_CAUSE = #{uw_cancel_cause} ]]></if>
		<if test=" uw_decision  != null "><![CDATA[ AND A.UW_DECISION = #{uw_decision} ]]></if>
		<if test=" uw_esca_indi  != null "><![CDATA[ AND A.UW_ESCA_INDI = #{uw_esca_indi} ]]></if>
		<if test=" ri_falg  != null "><![CDATA[ AND A.RI_FALG = #{ri_falg} ]]></if>
		<if test=" uw_esca_user  != null "><![CDATA[ AND A.UW_ESCA_USER = #{uw_esca_user} ]]></if>
		<if test=" uw_review_indi  != null "><![CDATA[ AND A.UW_REVIEW_INDI = #{uw_review_indi} ]]></if>
		<if test=" biz_code != null and biz_code != ''  "><![CDATA[ AND A.BIZ_CODE = #{biz_code} ]]></if>
		<if test=" uw_source_type != null and uw_source_type != ''  "><![CDATA[ AND A.UW_SOURCE_TYPE = #{uw_source_type} ]]></if>
		<if test=" uw_user_id  != null "><![CDATA[ AND A.UW_USER_ID = #{uw_user_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryUwMasterByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addUwMaster"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO T_UW_MASTER(
				UW_ID, PREVIOUS_UW_TIME, PREVIOUS_UW_USER, UW_SUBMIT_TIME, UW_LEVEL_CODE, UW_STATUS, BIZ_DATE, 
				UW_FINISH_TIME, RI_TEL_FALG, INSERT_TIMESTAMP, UPDATE_BY, UW_OVER_INDI, SERVICE_CODE, UW_AMEND_INDI, 
				APPLY_LEVEL_CODE, UW_CANCEL_CAUSE, INSERT_TIME, UW_DECISION, UPDATE_TIME, UW_ESCA_INDI, RI_FALG, 
				UW_ESCA_USER, UW_REVIEW_INDI, BIZ_CODE, UW_SOURCE_TYPE, UPDATE_TIMESTAMP, UW_USER_ID, INSERT_BY ) 
			VALUES (
				#{uw_id, jdbcType=NUMERIC}, #{previous_uw_time, jdbcType=DATE} , #{previous_uw_user, jdbcType=NUMERIC} , SYSDATE , #{uw_level_code, jdbcType=VARCHAR} , #{uw_status, jdbcType=VARCHAR} , #{biz_date, jdbcType=DATE} 
				, #{uw_finish_time, jdbcType=DATE} , #{ri_tel_falg, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{uw_over_indi, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{uw_amend_indi, jdbcType=NUMERIC} 
				, #{apply_level_code, jdbcType=VARCHAR} , #{uw_cancel_cause, jdbcType=VARCHAR} , SYSDATE , #{uw_decision, jdbcType=NUMERIC} , SYSDATE , #{uw_esca_indi, jdbcType=NUMERIC} , #{ri_falg, jdbcType=NUMERIC} 
				, #{uw_esca_user, jdbcType=NUMERIC} , #{uw_review_indi, jdbcType=NUMERIC} , #{biz_code, jdbcType=VARCHAR} , #{uw_source_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{uw_user_id, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteUwMaster" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_UW_MASTER WHERE UW_ID = #{uw_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateUwMaster" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_UW_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
		    PREVIOUS_UW_TIME = #{previous_uw_time, jdbcType=DATE} ,
		    PREVIOUS_UW_USER = #{previous_uw_user, jdbcType=NUMERIC} ,
		    UW_SUBMIT_TIME = #{uw_submit_time, jdbcType=DATE} ,
			UW_LEVEL_CODE = #{uw_level_code, jdbcType=VARCHAR} ,
			UW_STATUS = #{uw_status, jdbcType=VARCHAR} ,
		    BIZ_DATE = #{biz_date, jdbcType=DATE} ,
		    UW_FINISH_TIME = #{uw_finish_time, jdbcType=DATE} ,
		    RI_TEL_FALG = #{ri_tel_falg, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UW_OVER_INDI = #{uw_over_indi, jdbcType=NUMERIC} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
		    UW_AMEND_INDI = #{uw_amend_indi, jdbcType=NUMERIC} ,
			APPLY_LEVEL_CODE = #{apply_level_code, jdbcType=VARCHAR} ,
			UW_CANCEL_CAUSE = #{uw_cancel_cause, jdbcType=VARCHAR} ,
		    UW_DECISION = #{uw_decision, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UW_ESCA_INDI = #{uw_esca_indi, jdbcType=NUMERIC} ,
		    RI_FALG = #{ri_falg, jdbcType=NUMERIC} ,
		    UW_ESCA_USER = #{uw_esca_user, jdbcType=NUMERIC} ,
		    UW_REVIEW_INDI = #{uw_review_indi, jdbcType=NUMERIC} ,
			BIZ_CODE = #{biz_code, jdbcType=VARCHAR} ,
			UW_SOURCE_TYPE = #{uw_source_type, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    UW_USER_ID = #{uw_user_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE UW_ID = #{uw_id} ]]>
	</update>

	<!-- 根据uwId修改t_uw_master修改操作 -->
	<update id="updateUwMasterByUwId" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_UW_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
		    UW_OVER_INDI = #{uw_over_indi, jdbcType=NUMERIC},
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE ,
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			uw_finish_time = SYSDATE ,
			<!-- <if test=" uw_finish_time  != null "> uw_finish_time = ${uw_finish_time} ,</if> -->
			<if test=" uw_decision  != null "> uw_decision = ${uw_decision} ,</if>
			<if test=" uw_status  != null ">uw_status = '${uw_status}' ,</if>
			<if test=" uw_user_id  != null "><![CDATA[ UW_USER_ID = #{uw_user_id} ]]></if>
		</trim>
		</set>
		<![CDATA[ WHERE UW_ID = #{uw_id}]]>
	</update>
	
	<!-- 根据uwId修改保全核保时的t_uw_master相关字段 -->
	<update id="updateUwMasterByCSUwId" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_UW_MASTER ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE ,
			UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			 <if test=" uw_user_id  != null "> uw_user_id = ${uw_user_id}  ,</if>
			<if test=" uw_over_indi  != null "> uw_over_indi = ${uw_over_indi},</if>
			<if test=" uw_finish_time  != null "> uw_finish_time = SYSDATE ,</if>
			<if test=" uw_esca_user  != null "> uw_esca_user = ${uw_esca_user},</if>
			<if test=" uw_esca_indi  != null ">UW_ESCA_INDI = #{uw_esca_indi},</if>
			<if test=" uw_decision  != null "> uw_decision = ${uw_decision} ,</if>
			<if test=" uw_status  != null "> uw_status = '${uw_status}' ,</if>  
			<if test=" uw_level_code != null and uw_level_code != ''  "><![CDATA[ UW_LEVEL_CODE = #{uw_level_code}, ]]></if>
			<if test=" previous_uw_user  != null "><![CDATA[PREVIOUS_UW_USER = #{previous_uw_user},PREVIOUS_UW_TIME = SYSDATE, ]]></if>
		</trim>
		</set>
		<![CDATA[ WHERE UW_ID = #{uw_id}]]>
	</update>
	
<!-- 按索引查询操作 -->	
	<select id="findUwMasterByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PREVIOUS_UW_TIME, A.PREVIOUS_UW_USER, A.UW_SUBMIT_TIME, A.UW_LEVEL_CODE, A.UW_STATUS, A.BIZ_DATE, 
			A.UW_FINISH_TIME, A.RI_TEL_FALG, A.UW_OVER_INDI, A.SERVICE_CODE, A.UW_AMEND_INDI, 
			A.APPLY_LEVEL_CODE, A.UW_CANCEL_CAUSE, A.UW_DECISION, A.UW_ESCA_INDI, A.RI_FALG, 
			A.UW_ESCA_USER, A.UW_REVIEW_INDI, A.BIZ_CODE, A.UW_SOURCE_TYPE, A.UW_USER_ID,(select user_name from t_udmp_user where user_id=A.UW_USER_ID) as user_name FROM T_UW_MASTER A WHERE 1 = 1  ]]>
		<include refid="queryUwMasterByUwIdCondition" />
		<if test=" biz_code != null and biz_code != ''  "><![CDATA[ AND A.BIZ_CODE = #{biz_code} ]]></if>
		<if test=" uw_source_type != null and uw_source_type != ''  "><![CDATA[ AND A.UW_SOURCE_TYPE = #{uw_source_type} ]]></if>
		<![CDATA[ ORDER BY A.UW_ID ]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="UW_findUwMasterByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PREVIOUS_UW_TIME, A.PREVIOUS_UW_USER, A.UW_SUBMIT_TIME, A.UW_LEVEL_CODE, A.UW_STATUS, A.BIZ_DATE, 
			A.UW_FINISH_TIME, A.RI_TEL_FALG, A.UW_OVER_INDI, A.SERVICE_CODE, A.UW_AMEND_INDI, 
			A.APPLY_LEVEL_CODE, A.UW_CANCEL_CAUSE, A.UW_DECISION, A.UW_ESCA_INDI, A.RI_FALG, 
			A.UW_ESCA_USER, A.UW_REVIEW_INDI, A.BIZ_CODE, A.UW_SOURCE_TYPE, A.UW_USER_ID FROM dev_uw.T_UW_MASTER A WHERE 1 = 1  ]]>
		<if test=" uw_id != null and uw_id != ''  "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<![CDATA[ ORDER BY A.UW_ID ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="findAllMapUwMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PREVIOUS_UW_TIME, A.PREVIOUS_UW_USER, A.UW_SUBMIT_TIME, A.UW_LEVEL_CODE, A.UW_STATUS, A.BIZ_DATE, 
			A.UW_FINISH_TIME, A.RI_TEL_FALG, A.UW_OVER_INDI, A.SERVICE_CODE, A.UW_AMEND_INDI, 
			A.APPLY_LEVEL_CODE, A.UW_CANCEL_CAUSE, A.UW_DECISION, A.UW_ESCA_INDI, A.RI_FALG, 
			A.UW_ESCA_USER, A.UW_REVIEW_INDI, A.BIZ_CODE, A.UW_SOURCE_TYPE, A.UW_USER_ID FROM T_UW_MASTER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllUwMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PREVIOUS_UW_TIME, A.PREVIOUS_UW_USER, A.UW_SUBMIT_TIME, A.UW_LEVEL_CODE, A.UW_STATUS, A.BIZ_DATE, 
			A.UW_FINISH_TIME, A.RI_TEL_FALG, A.UW_OVER_INDI, A.SERVICE_CODE, A.UW_AMEND_INDI, 
			A.APPLY_LEVEL_CODE, A.UW_CANCEL_CAUSE, A.UW_DECISION, A.UW_ESCA_INDI, A.RI_FALG, 
			A.UW_ESCA_USER, A.UW_REVIEW_INDI, A.BIZ_CODE, A.UW_SOURCE_TYPE, A.UW_USER_ID FROM T_UW_MASTER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findUwMasterTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM dev_uw.T_UW_MASTER A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" biz_code != null and biz_code != ''  "><![CDATA[ AND A.BIZ_CODE = #{biz_code} ]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="queryUwMasterForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.UW_ID, B.PREVIOUS_UW_TIME, B.PREVIOUS_UW_USER, B.UW_SUBMIT_TIME, B.UW_LEVEL_CODE, B.UW_STATUS, B.BIZ_DATE, 
			B.UW_FINISH_TIME, B.RI_TEL_FALG, B.UW_OVER_INDI, B.SERVICE_CODE, B.UW_AMEND_INDI, 
			B.APPLY_LEVEL_CODE, B.UW_CANCEL_CAUSE, B.UW_DECISION, B.UW_ESCA_INDI, B.RI_FALG, 
			B.UW_ESCA_USER, B.UW_REVIEW_INDI, B.BIZ_CODE, B.UW_SOURCE_TYPE, B.UW_USER_ID FROM (
					SELECT ROWNUM RN, A.UW_ID, A.PREVIOUS_UW_TIME, A.PREVIOUS_UW_USER, A.UW_SUBMIT_TIME, A.UW_LEVEL_CODE, A.UW_STATUS, A.BIZ_DATE, 
			A.UW_FINISH_TIME, A.RI_TEL_FALG, A.UW_OVER_INDI, A.SERVICE_CODE, A.UW_AMEND_INDI, 
			A.APPLY_LEVEL_CODE, A.UW_CANCEL_CAUSE, A.UW_DECISION, A.UW_ESCA_INDI, A.RI_FALG, 
			A.UW_ESCA_USER, A.UW_REVIEW_INDI, A.BIZ_CODE, A.UW_SOURCE_TYPE, A.UW_USER_ID FROM T_UW_MASTER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 根据受理号查询受理信息 -->
	<select id="findMasterByAccetpNo" resultType="java.util.Map" parameterType="java.util.Map">
	   select tm.uw_id, tcu.customer_name, pc.endorse_code,pc.policy_code,pc.apply_time,pc.validate_time,tm.UW_DECISION from dev_pas.t_cs_accept_change tc,dev_pas.t_cs_policy_change pc,dev_uw.t_uw_master tm,
       dev_pas.t_policy_holder tph,dev_pas.t_customer tcu
       where tc.accept_id = pc.accept_id
       and tm.biz_code = tc.accept_code
       and pc.policy_code = tph.policy_code
       and tph.customer_id = tcu.customer_id
       <if test="biz_code!=null and biz_code!=''">
          and tm.biz_code = #{biz_code}
       </if>
	
	</select>
	<!-- 根据受理号查询核保结论 -->
	<select id="findUwMasterPo" resultType="java.util.Map" parameterType="java.util.Map">
	      select ty.policy_decision from dev_pas.t_uw_master tm ,dev_uw.t_uw_policy ty
          where tm.uw_id = ty.uw_id
          and tm.biz_code = #{biz_code}
	
	</select>
	
	
</mapper>
