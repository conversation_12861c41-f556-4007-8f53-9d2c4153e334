<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QueryPayPlanInfoDaoImpl">
<select id="PA_findPayPlanInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT B.PLAN_ID, /*计划ID*/
       B.BUSI_ITEM_ID, /*险种ID*/
       B.BUSI_PROD_CODE, /*险种代码*/
       B.BUSI_PROD_NAME, /*险种名称*/
       B.PRODUCT_CODE, /*责任组代码*/
       B.PRODUCT_NAME, /*责任组名称*/
       B.LIAB_CODE, /*责任代码*/
       B.LIAB_NAME, /*责任名称*/
       B.BEGIN_DATE pay_begin_date, /*给付开始日期*/
       B.END_DATE pay_end_date, /*给付结束日期*/
       B.PAY_STATUS, /*给付状态代码*/
       B.PLAN_FREQ, /*给付频率代码*/
       B.SURVIVAL_INVEST_FLAG, /*生调标识*/
       B.TOTAL_AMOUNT, /*已给付总金额*/
       B.PAY_YEAR, /*已给付期数*/
       B.INSTALMENT_AMOUNT, /*下期给付金额*/
       (CASE
         WHEN B.BUSI_PROD_CODE in ('00928000', '00928100') THEN
          (select A.START_PAY_AGE
             from DEV_PAS.T_CONTRACT_PRODUCT A
            WHERE A.ITEM_ID = B.ITEM_ID)
         ELSE
          FLOOR(MONTHS_BETWEEN(B.BEGIN_DATE, B.CUSTOMER_BIRTHDAY) / 12)
       END) AS CUSTOMER_BIRTDAY, /*领取年龄*/
       B.RECEIVE_FORMALLY, /*领取形式代码*/   
       B.RECEIVE_FORMALLY_NAME,/*领取形式名称*/
       B.SURVIVAL_MODE, /*支取形式代码*/
       B.SURVIVAL_MODE_NAME, /*支取形式名称*/
       B.MANUAL_EXTRA_DATE, /*抽档日期*/     
       B.ISSUE_BANK_NAME, /*开户银行*/
       B.ACCO_NAME, /*户名*/
       B.BANK_ACCOUNT, /*银行账户*/
       B.BANK_CODE /*开户银行编码*/
  FROM (    
        SELECT  TPP.POLICY_CODE,
                TPP.PLAN_ID,
                TPP.ITEM_ID,
                TPP.BUSI_ITEM_ID,
                TPP.BUSI_PROD_CODE,
                (select tt.PRODUCT_NAME_SYS
                   from dev_pds.t_business_product tt
                   left join dev_pas.t_contract_busi_prod dd
                     on tt.business_prd_id = dd.busi_prd_id
                  where dd.busi_item_id = TPP.busi_item_id) as busi_prod_name,               
                TPP.PRODUCT_CODE,            
                (SELECT PL.PRODUCT_NAME
                   FROM DEV_PDS.T_PRODUCT_LIFE PL
                  WHERE PL.INTERNAL_ID = TPP.Product_Code) PRODUCT_NAME,               
                TPP.LIAB_CODE,                
                L.LIAB_NAME,                
                TPP.BEGIN_DATE,            
                TPP.END_DATE,                
                TPP.PAY_STATUS,              
                TPP.PLAN_FREQ,
                TPP.SURVIVAL_INVEST_FLAG, /*生调标识*/
                D.TOTAL_AMOUNT,
                D.PAY_YEAR,
                (CASE
                  WHEN TPP.PAY_STATUS = '2' then
                   TPP.INSTALMENT_AMOUNT
                  ELSE
                   NULL
                END) AS INSTALMENT_AMOUNT, /*下期给付金额*/
                TPP.SURVIVAL_MODE AS RECEIVE_FORMALLY, /*领取形式*/		
                (SELECT TSM.MODE_NAME FROM DEV_PAS.T_SURVIVAL_MODE TSM WHERE TSM.MODE_CODE = TPP.SURVIVAL_MODE) RECEIVE_FORMALLY_NAME, 						
				TPP.SURVIVAL_W_MODE SURVIVAL_MODE,
				(SELECT TSWM.MODE_NAME FROM DEV_PAS.T_SURVIVAL_W_MODE TSWM WHERE TSWM.MODE_CODE = TPP.SURVIVAL_W_MODE) SURVIVAL_MODE_NAME,
				TPP.MANUAL_EXTRA_DATE,
                NVL(TBA.ISSUE_BANK_NAME, B.BANK_NAME) AS ISSUE_BANK_NAME, /*开户银行*/
                TBA.ACCO_NAME, /*户名*/
                TBA.BANK_ACCOUNT, /*银行账户*/
                NVL(B.STD_BANK_CODE,B.BANK_CODE) AS BANK_CODE,/*开户银行编码*/           
                (SELECT A.CUSTOMER_BIRTHDAY
                   FROM DEV_PAS.T_CUSTOMER A /**客户表*/
                  WHERE A.CUSTOMER_ID = TPPP.CUSTOMER_ID) AS CUSTOMER_BIRTHDAY      
          FROM DEV_PAS.T_PAY_PLAN TPP
          LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCMP
            ON TPP.POLICY_ID = TCMP.POLICY_ID
          LEFT JOIN DEV_PAS.T_PAY_PLAN_PAYEE TPPP
            ON TPP.PLAN_ID = TPPP.PLAN_ID
          LEFT JOIN DEV_PAS.T_BANK_ACCOUNT TBA
            ON TPPP.PAYEE_ACCOUNT_ID = TBA.ACCOUNT_ID
          LEFT JOIN DEV_PAS.T_BANK B
            ON TBA.BANK_CODE = B.BANK_CODE
          LEFT JOIN DEV_PAS.T_SURVIVAL_MODE SM
            ON TPP.SURVIVAL_MODE = SM.MODE_CODE
          LEFT JOIN DEV_PAS.T_SURVIVAL_W_MODE SWM
            ON TPP.SURVIVAL_W_MODE = SWM.MODE_CODE
          LEFT JOIN DEV_PAS.T_LIABILITY L
            ON L.LIAB_ID = TPP.LIAB_ID
          LEFT JOIN (SELECT SUM(case
                                  when pd.survival_type in ('1', '3', '5') then
                                   PD.FEE_AMOUNT
                                  else
                                   pd.FEE_AMOUNT * -1
                                end) AS TOTAL_AMOUNT,
                            
                            COUNT(*) AS PAY_YEAR,
                            PD.PLAN_ID
                       FROM DEV_PAS.T_PAY_DUE PD
                      WHERE (PD.FEE_STATUS = '01' OR PD.FEE_STATUS = '16')
                        AND PD.POLICY_CODE =  #{policy_code}
                      GROUP BY PD.PLAN_ID) D
            ON D.PLAN_ID = TPP.PLAN_ID
         WHERE 1 = 1
           AND (TPP.PAY_PLAN_TYPE = '3' OR TPP.PAY_PLAN_TYPE = '4' or
               TPP.PAY_PLAN_TYPE = '10' OR TPP.PAY_PLAN_TYPE = '8')
          ]]>
	<if test=" policy_code  != null "><![CDATA[ AND TPP.POLICY_CODE = #{policy_code} ]]></if>	
	<if test=" busi_item_id  != null "><![CDATA[ AND TPP.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	<![CDATA[) B]]>
</select>

	<select id="PA_queryPolicyReleaseFlagPlan" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
		  SELECT A.SUB_POLICY_CODE,
	       A.SUB_BUSI_PROD_CODE,
	       B.LIABILITY_STATE,
	       TBP.PRODUCT_NAME_SYS,
	       A.RELATION_TYPE
	  FROM APP___PAS__DBUSER.T_CONTRACT_RELATION  A,
	       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B,
	       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
	 WHERE 1 = 1
	   AND A.SUB_BUSI_ITEM_ID = B.BUSI_ITEM_ID
	   AND B.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
	   AND A.RELATION_TYPE in ('2', '3', '4')
	   AND B.LIABILITY_STATE = '1'
	   AND A.MASTER_POLICY_CODE = #{policy_code}
	  UNION 
	   SELECT TCM1.POLICY_CODE SUB_POLICY_CODE,
	       TCBP.BUSI_PROD_CODE SUB_BUSI_PROD_CODE,
	       TCM1.LIABILITY_STATE,
	       TBP.PRODUCT_NAME_SYS,
	       CASE
	       		WHEN TCM.POLICY_RELATION_TYPE = '0' THEN '2'
	       		WHEN TCM.POLICY_RELATION_TYPE = '1' THEN '3'
	       		WHEN TCM.POLICY_RELATION_TYPE = '2' THEN '4'
	       		ELSE ''
	       END AS RELATION_TYPE
	  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
	       APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM1,
	       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
	       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
	 WHERE 1 = 1
	   AND TCM.RELATION_POLICY_CODE IS NOT NULL
	   AND TCM.POLICY_CODE = #{policy_code}
	   AND TCM.RELATION_POLICY_CODE = TCM1.POLICY_CODE
	   AND TCM1.POLICY_CODE = TCBP.POLICY_CODE
	   AND TCM1.LIABILITY_STATE = '1'
	   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
	   AND (TCM.POLICY_RELATION_TYPE IS NULL OR TCM.POLICY_RELATION_TYPE in ('0', '1', '2'))
	   AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
	     ]]>
	</select>

</mapper>