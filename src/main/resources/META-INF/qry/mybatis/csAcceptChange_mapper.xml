<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.object.dao.ICsAcceptChangeDao">

	<sql id="csAcceptChangeWhereCondition">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" cancel_time  != null  and  cancel_time  != ''  "><![CDATA[ AND A.CANCEL_TIME = #{cancel_time} ]]></if>
		<if test=" cancel_cause != null and cancel_cause != ''  "><![CDATA[ AND A.CANCEL_CAUSE = #{cancel_cause} ]]></if>
		<if test=" rollback_cause != null and rollback_cause != ''  "><![CDATA[ AND <PERSON><PERSON>ROLLBACK_CAUSE = #{rollback_cause} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" review_time  != null  and  review_time  != ''  "><![CDATA[ AND A.REVIEW_TIME = #{review_time} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" rollback_note != null and rollback_note != ''  "><![CDATA[ AND A.ROLLBACK_NOTE = #{rollback_note} ]]></if>
		<if test=" review_property != null and review_property != ''  "><![CDATA[ AND A.REVIEW_PROPERTY = #{review_property} ]]></if>
		<if test=" review_view != null and review_view != ''  "><![CDATA[ AND A.REVIEW_VIEW = #{review_view} ]]></if>
		<if test=" cancel_note != null and cancel_note != ''  "><![CDATA[ AND A.CANCEL_NOTE = #{cancel_note} ]]></if>		
		<if test=" urgent_detail != null and urgent_detail != ''  "><![CDATA[ AND A.URGENT_DETAIL = #{urgent_detail} ]]></if>
		<if test=" uncon_type != null and uncon_type != ''  "><![CDATA[ AND A.UNCON_TYPE = #{uncon_type} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" cs_accept_reason != null and cs_accept_reason != ''  "><![CDATA[ AND A.CS_ACCEPT_REASON = #{cs_accept_reason} ]]></if>
		<if test=" cancel_id  != null "><![CDATA[ AND A.CANCEL_ID = #{cancel_id} ]]></if>
		<if test=" rollback_app_id  != null "><![CDATA[ AND A.ROLLBACK_APP_ID = #{rollback_app_id} ]]></if>
		<if test=" uncon_cause != null and uncon_cause != ''  "><![CDATA[ AND A.UNCON_CAUSE = #{uncon_cause} ]]></if>
		<if test=" rollback_app_time  != null  and  rollback_app_time  != ''  "><![CDATA[ AND A.ROLLBACK_APP_TIME = #{rollback_app_time} ]]></if>
		<if test=" change_flag != null and change_flag != ''  "><![CDATA[ AND A.CHANGE_FLAG = #{change_flag} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" hesitate_flag  != null "><![CDATA[ AND A.HESITATE_FLAG = #{hesitate_flag} ]]></if>
		<if test=" rollback_review_view != null and rollback_review_view != ''  "><![CDATA[ AND A.ROLLBACK_REVIEW_VIEW = #{rollback_review_view} ]]></if>
		<if test=" insert_operator_id  != null "><![CDATA[ AND A.INSERT_OPERATOR_ID = #{insert_operator_id} ]]></if>
		<if test=" review_id  != null "><![CDATA[ AND A.REVIEW_ID = #{review_id} ]]></if>
		<if test=" validate_time  != null  and  validate_time  != ''  "><![CDATA[ AND A.VALIDATE_TIME = #{validate_time} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" pre_flag  != null "><![CDATA[ AND A.PRE_FLAG = #{pre_flag} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" conven_flag  != null "><![CDATA[ AND A.CONVEN_FLAG = #{conven_flag} ]]></if>
		<if test=" accept_code != null and accept_code != ''  "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>
		<if test=" accept_time  != null  and  accept_time  != ''  "><![CDATA[ AND A.ACCEPT_TIME = #{accept_time} ]]></if>
		<if test=" pre_validate_date  != null  and  pre_validate_date  != ''  "><![CDATA[ AND A.PRE_VALIDATE_DATE = #{pre_validate_date} ]]></if>
		<if test=" accept_status != null and accept_status != ''  "><![CDATA[ AND A.ACCEPT_STATUS = #{accept_status} ]]></if>
		<if test=" back_entyr_cause != null and back_entyr_cause != ''  "><![CDATA[ AND A.BACK_ENTYR_CAUSE = #{back_entyr_cause} ]]></if>
		<if test=" urgent_cause != null and urgent_cause != ''  "><![CDATA[ AND A.URGENT_CAUSE = #{urgent_cause} ]]></if>
		<if test=" delay_cause != null and delay_cause != ''  "><![CDATA[ AND A.DELAY_CAUSE = #{delay_cause} ]]></if>
		<if test=" urgent_flag  != null "><![CDATA[ AND A.URGENT_FLAG = #{urgent_flag} ]]></if>
		<if test=" rollback_review_result != null and rollback_review_result != ''  "><![CDATA[ AND A.ROLLBACK_REVIEW_RESULT = #{rollback_review_result} ]]></if>
		<if test=" review_accept_rate != null "><![CDATA[ AND A.REVIEW_ACCEPT_RATE = #{review_accept_rate} ]]></if>
		<if test=" review_input_rate  != null "><![CDATA[ AND A.REVIEW_INPUT_RATE = #{review_input_rate} ]]></if>
		<if test=" review_rate != null "><![CDATA[ AND A.REVIEW_RATE = #{review_rate} ]]></if>
		<if test=" bill_send_type != null "><![CDATA[ AND A.BILL_SEND_TYPE = #{bill_send_type} ]]></if>
	</sql>
	
	<sql id="findTotalWhereCondition">
	    <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE like #{organ_code} ]]></if>
		<if test=" review_result != null and review_result == '1'  ">
		<![CDATA[ 
		and a.accept_status in ('13','18') 		
		]]>
		</if>
		<if test=" review_result != null and review_result == '2'  ">
		<![CDATA[ 
		and a.accept_status in ('21') 
		]]>
		</if>
		<if test=" review_result != null and review_result == '3'  ">
		<![CDATA[ 
		and a.accept_status in ('14') 
		]]>
		</if>
	</sql>
<!-- 按索引生成的查询条件   -->	
	<sql id="queryCsAcceptChangeByAcceptIdCondition">
		<if test=" accept_id  != null and accept_id != '' "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
	    <if test=" change_id  != null and change_id !='' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
	</sql>	
	
<!-- 单条数据的查询条件 -->
	<sql id="queryCsAcceptChangeBySingle">
		<if test=" accept_code  != null "><![CDATA[ AND A.ACCEPT_CODE = #{accept_code} ]]></if>	
		<if test=" accept_id  != null and accept_id !='' "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>	
		<if test=" change_id  != null and change_id != '' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if	test=" service_code_list  != null and service_code_list != '' "><![CDATA[ AND A.SERVICE_CODE IN (${service_code_list}) ]]></if>
	</sql>		
	
    <sql id="queryCsAcceptChangeByChangeId">
		<if test=" change_id  != null and change_id != '' "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>		
	</sql>
	
	<!-- add by zkm -->
    <sql id="csEndorseManualCancelTrail">
		<if test="policy_code != null and policy_code != ''"><![CDATA[ and pc.policy_code = #{policy_code} ]]></if>
		<if test="apply_code != null and apply_code != ''"><![CDATA[ and a.apply_code = #{apply_code} ]]></if>
		<if test="accept_code != null and accept_code != ''"><![CDATA[ and ac.accept_code = #{accept_code} ]]></if>
		<if test="user_name != null and user_name != ''"><![CDATA[ and uu.user_name = #{user_name} ]]></if>
		<if test="certi_type != null and certi_type != ''"><![CDATA[ and uu.id_type = #{certi_type} ]]></if>
		<if test="certi_code != null and certi_code != ''"><![CDATA[ and uu.id_card = #{certi_code} ]]></if>
		<if test="insert_time != null and insert_time != ''"><![CDATA[and ac.cancel_time between to_date(#{insert_time} || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') and to_date(#{insert_time} || ' 23:59:59', 'yyyy-mm-dd hh24:mi:ss')]]></if>
	</sql>
	
	<!-- 添加操作 -->
	<insert id="addCsAcceptChange"  useGeneratedKeys="false"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="accept_id">
			SELECT DEV_PAS.S_CS_ACCEPT_CHANGE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO DEV_PAS.T_CS_ACCEPT_CHANGE(
				RELATED_ID, UNCON_CAUSE, CHANGE_FLAG, CANCEL_TIME, ACCEPT_TIME, HESITATE_FLAG, 
				FINISH_TIME, CANCEL_CAUSE, REVIEW_ID, INSERT_TIMESTAMP, ORGAN_CODE, REVIEW_TIME, VALIDATE_TIME, 
				UPDATE_BY, CHANGE_ID, PRE_FLAG, FEE_AMOUNT, SERVICE_CODE, ACCEPT_ID, 
				INSERT_TIME, REVIEW_PROPERTY, UPDATE_TIME, CONVEN_FLAG, CANCEL_NOTE, REVIEW_VIEW, ACCEPT_CODE, 
				REVIEW_RESULT, URGENT_DETAIL, PRE_VALIDATE_DATE, ORDER_ID, UNCON_TYPE, ACCEPT_STATUS, 
				CANCEL_ID, BACK_ENTYR_CAUSE, UPDATE_TIMESTAMP, URGENT_CAUSE, INSERT_BY, URGENT_FLAG, DELAY_CAUSE, 
				REVIEW_ACCEPT_RATE, REVIEW_INPUT_RATE, REVIEW_RATE,BILL_SEND_TYPE,BALANCE_FLAG,LIAB_VALIDATE_TIME) 
			VALUES (
				#{related_id, jdbcType=NUMERIC}, #{uncon_cause, jdbcType=VARCHAR} , ]]>				
				<if test=" change_flag != null and change_flag != ''  "><![CDATA[#{change_flag, jdbcType=VARCHAR} ,]]></if>
				<if test=" change_flag = null or change_flag = ''  "><![CDATA[ default,]]></if>
				<![CDATA[  #{cancel_time, jdbcType=DATE} , #{accept_time, jdbcType=DATE} , #{hesitate_flag, jdbcType=NUMERIC} 
				, #{finish_time, jdbcType=DATE} , #{cancel_cause, jdbcType=VARCHAR} , #{review_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{review_time, jdbcType=DATE} , #{validate_time, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC} , #{change_id, jdbcType=NUMERIC} , #{pre_flag, jdbcType=NUMERIC} , #{fee_amount, jdbcType=NUMERIC} , #{service_code, jdbcType=VARCHAR} , #{accept_id, jdbcType=NUMERIC} 
				, SYSDATE , #{review_property, jdbcType=VARCHAR} , SYSDATE , #{conven_flag, jdbcType=NUMERIC} , #{cancel_note, jdbcType=VARCHAR} , #{review_view, jdbcType=VARCHAR} , #{accept_code, jdbcType=VARCHAR} 
				, #{review_result, jdbcType=VARCHAR} , #{urgent_detail, jdbcType=VARCHAR} , #{pre_validate_date, jdbcType=DATE} , #{order_id, jdbcType=NUMERIC} , #{uncon_type, jdbcType=VARCHAR} , #{accept_status, jdbcType=VARCHAR} 
				, #{cancel_id, jdbcType=NUMERIC} , #{back_entyr_cause, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{urgent_cause, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{urgent_flag, jdbcType=NUMERIC} , #{delay_cause, jdbcType=VARCHAR} 
				, #{review_accept_rate, jdbcType=NUMERIC}, #{review_input_rate, jdbcType=NUMERIC}, #{review_rate, jdbcType=NUMERIC},#{bill_send_type, jdbcType=VARCHAR},#{balance_flag, jdbcType=NUMERIC}, #{liab_validate_time, jdbcType=DATE} ) 
		 ]]>
	</insert>

<!-- 修改操作 -->
	<update id="updateCsAcceptChange" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_PAS.T_CS_ACCEPT_CHANGE ]]>
		<set>
		<trim suffixOverrides=",">
		    RELATED_ID = #{related_id, jdbcType=NUMERIC} ,
		    CANCEL_TIME = #{cancel_time, jdbcType=DATE} ,
			CANCEL_CAUSE = #{cancel_cause, jdbcType=VARCHAR} ,
			ROLLBACK_CAUSE = #{rollback_cause, jdbcType=VARCHAR} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		   <if test="r_time != null">
			 REVIEW_TIME = to_date(#{r_time, jdbcType=VARCHAR},'YYYY-MM-DD HH24:MI:SS') ,
			</if>
			<if test="review_time == null">
			REVIEW_TIME = #{review_time,jdbcType=DATE},
			</if>
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHANGE_ID = #{change_id, jdbcType=NUMERIC} ,
			ROLLBACK_NOTE = #{rollback_note, jdbcType=VARCHAR} ,
			REVIEW_PROPERTY = #{review_property, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			REVIEW_VIEW = #{review_view, jdbcType=VARCHAR} ,
			CANCEL_NOTE = #{cancel_note, jdbcType=VARCHAR} ,
			REVIEW_RESULT = #{review_result, jdbcType=VARCHAR} ,
			URGENT_DETAIL = #{urgent_detail, jdbcType=VARCHAR} ,
			UNCON_TYPE = #{uncon_type, jdbcType=VARCHAR} ,
		    ORDER_ID = #{order_id, jdbcType=NUMERIC} ,
			CS_ACCEPT_REASON = #{cs_accept_reason, jdbcType=VARCHAR} ,
		    CANCEL_ID = #{cancel_id, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    ROLLBACK_APP_ID = #{rollback_app_id, jdbcType=NUMERIC} ,
			UNCON_CAUSE = #{uncon_cause, jdbcType=VARCHAR} ,
		    ROLLBACK_APP_TIME = #{rollback_app_time, jdbcType=DATE} ,
			CHANGE_FLAG = #{change_flag, jdbcType=VARCHAR} ,
		    FINISH_TIME = #{finish_time, jdbcType=DATE} ,
		    HESITATE_FLAG = #{hesitate_flag, jdbcType=NUMERIC} ,
			ROLLBACK_REVIEW_VIEW = #{rollback_review_view, jdbcType=VARCHAR} ,
		    INSERT_OPERATOR_ID = #{insert_operator_id, jdbcType=NUMERIC} ,
		    REVIEW_ID = #{review_id, jdbcType=NUMERIC} ,
		    VALIDATE_TIME = #{validate_time, jdbcType=DATE} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
		    PRE_FLAG = #{pre_flag, jdbcType=NUMERIC} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
		    CONVEN_FLAG = #{conven_flag, jdbcType=NUMERIC} ,
			ACCEPT_CODE = #{accept_code, jdbcType=VARCHAR} ,
		    ACCEPT_TIME = #{accept_time, jdbcType=DATE} ,
		    PRE_VALIDATE_DATE = #{pre_validate_date, jdbcType=DATE} ,
			ACCEPT_STATUS = #{accept_status, jdbcType=VARCHAR} ,
			BACK_ENTYR_CAUSE = #{back_entyr_cause, jdbcType=VARCHAR} ,
			URGENT_CAUSE = #{urgent_cause, jdbcType=VARCHAR} ,
			DELAY_CAUSE = #{delay_cause, jdbcType=VARCHAR} ,
		    URGENT_FLAG = #{urgent_flag, jdbcType=NUMERIC} ,
			ROLLBACK_REVIEW_RESULT = #{rollback_review_result, jdbcType=VARCHAR} ,
			REVIEW_ACCEPT_RATE = #{review_accept_rate, jdbcType=NUMERIC} ,
			REVIEW_INPUT_RATE = #{review_input_rate, jdbcType=NUMERIC} ,
			REVIEW_RATE = #{review_rate, jdbcType=NUMERIC} ,
			IS_SIGN_FLAG = #{is_sign_flag, jdbcType=NUMERIC} ,
			SIGN_NO_LIST = #{sign_no_list, jdbcType=VARCHAR} ,
			IS_SAVED = #{is_saved, jdbcType=NUMERIC} ,
			IS_ADD_DELAY = #{is_add_delay, jdbcType=NUMERIC},
			<if test="bill_send_type != null">
			BILL_SEND_TYPE = #{bill_send_type, jdbcType=VARCHAR},
			</if>
			<if test="liab_validate_time != null">
			LIAB_VALIDATE_TIME = #{liab_validate_time, jdbcType=DATE},
			</if>
		</trim>
		</set>
		<![CDATA[ WHERE ACCEPT_ID = #{accept_id} ]]>
	</update>
	
<!-- 按索引查询操作 -->	
	<select id="findCsAcceptChangeByAcceptId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,A.RELATED_ID,A.UNCON_CAUSE,A.CHANGE_FLAG,A.CANCEL_TIME,A.ACCEPT_TIME,
						A.HESITATE_FLAG,A.FINISH_TIME,A.CANCEL_CAUSE,A.REVIEW_ID,A.ORGAN_CODE,A.REVIEW_TIME,A.VALIDATE_TIME,A.CHANGE_ID,A.PRE_FLAG,A.FEE_AMOUNT,A.SERVICE_CODE,
						A.ACCEPT_ID,A.INSERT_OPERATOR_ID,A.REVIEW_PROPERTY,A.CONVEN_FLAG,A.CANCEL_NOTE,A.REVIEW_VIEW,A.ACCEPT_CODE,A.IS_SAVED,A.REVIEW_RESULT,A.URGENT_DETAIL,
						A.PRE_VALIDATE_DATE,A.ORDER_ID,A.UNCON_TYPE,A.ACCEPT_STATUS,A.CANCEL_ID,A.BACK_ENTYR_CAUSE,A.URGENT_CAUSE,A.URGENT_FLAG,A.DELAY_CAUSE,A.INSERT_BY,
						A.INSERT_TIME,A.REVIEW_ACCEPT_RATE,A.REVIEW_INPUT_RATE,A.REVIEW_RATE,A.IS_SIGN_FLAG,A.SIGN_NO_LIST,A.BILL_SEND_TYPE,B.APPLY_TIME,B.SERVICE_TYPE,
						B.APPLY_NAME,C.REISSUE_CAUSE,A.BALANCE_FLAG
						  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A
						 INNER JOIN DEV_PAS.T_CS_APPLICATION B
						    ON A.CHANGE_ID = B.CHANGE_ID
						  LEFT JOIN DEV_PAS.T_POLICY_REISSUE C
						    ON A.ACCEPT_CODE = C.ACCEPT_CODE
						 WHERE 1 = 1 
		]]>
		<include refid="queryCsAcceptChangeByAcceptIdCondition" />
		<![CDATA[ ORDER BY A.ACCEPT_ID ]]>
	</select>
	
<!-- 按map查询操作 -->
	<select id="findAllMapCsAcceptChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
		A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE,A.BILL_SEND_TYPE FROM DEV_PAS.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ACCEPT_ID ]]>
	</select>

<!-- 分页查询操作 -->
	<select id="queryCsAcceptChangeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RELATED_ID, B.UNCON_CAUSE, B.CHANGE_FLAG, B.CANCEL_TIME, B.ACCEPT_TIME, B.HESITATE_FLAG, 
			B.FINISH_TIME, B.CANCEL_CAUSE, B.REVIEW_ID, B.ORGAN_CODE, B.REVIEW_TIME, B.VALIDATE_TIME, 
			B.CHANGE_ID, B.PRE_FLAG, B.FEE_AMOUNT, B.SERVICE_CODE, B.ACCEPT_ID,  B.INSERT_OPERATOR_ID,
			B.REVIEW_PROPERTY, B.CONVEN_FLAG, B.CANCEL_NOTE, B.REVIEW_VIEW, B.ACCEPT_CODE, 
			B.REVIEW_RESULT, B.URGENT_DETAIL, B.PRE_VALIDATE_DATE, B.ORDER_ID, B.UNCON_TYPE, B.ACCEPT_STATUS , 
			B.CANCEL_ID, B.BACK_ENTYR_CAUSE, B.URGENT_CAUSE, B.URGENT_FLAG, B.DELAY_CAUSE,B.REVIEW_ACCEPT_RATE, B.REVIEW_INPUT_RATE, B.REVIEW_RATE FROM (
					SELECT ROWNUM RN, A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE FROM DEV_PAS.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ACCEPT_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findCsAcceptChangeByCustID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_ADD_DELAY,A.ROLLBACK_CAUSE,A.ROLLBACK_NOTE,A.ROLLBACK_APP_TIME,A.ROLLBACK_APP_ID,
						A.RELATED_ID, A.UNCON_CAUSE, A.CHANGE_FLAG, A.CANCEL_TIME, A.ACCEPT_TIME, A.HESITATE_FLAG, 
			A.FINISH_TIME, A.CANCEL_CAUSE, A.REVIEW_ID, A.ORGAN_CODE, A.REVIEW_TIME, A.VALIDATE_TIME, 
			A.CHANGE_ID, A.PRE_FLAG, A.FEE_AMOUNT, A.SERVICE_CODE, A.ACCEPT_ID,  A.INSERT_OPERATOR_ID,
			A.REVIEW_PROPERTY, A.CONVEN_FLAG, A.CANCEL_NOTE, A.REVIEW_VIEW, A.ACCEPT_CODE, 
			A.REVIEW_RESULT, A.URGENT_DETAIL, A.PRE_VALIDATE_DATE, A.ORDER_ID, A.UNCON_TYPE, A.ACCEPT_STATUS , 
			A.CANCEL_ID, A.BACK_ENTYR_CAUSE, A.URGENT_CAUSE, A.URGENT_FLAG, A.DELAY_CAUSE,A.REVIEW_ACCEPT_RATE, A.REVIEW_INPUT_RATE, A.REVIEW_RATE FROM DEV_PAS.T_CS_ACCEPT_CHANGE A WHERE ROWNUM <=  1000  ]]>
		<include refid="csAcceptChangeWhereCondition" />
		<![CDATA[ ORDER BY A.ACCEPT_ID ]]> 
	</select>
	
	<!-- 网站E化单证防伪验证码验真 -->
	<select id="judgeWebEDocumentPseudoVer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT  (CASE WHEN CPC.Endorse_Code is not null THEN 'CUS' WHEN CPC.POLICY_CODE is not null THEN 'PAS' ELSE '' END) FROM_MODLE,
          CPC.APPLY_TIME AS MAKE_DATE,CAC.SERVICE_CODE,'' CUSTOMER_NAME,CAC.ACCEPT_CODE,'' CLM_NO,NULL STAND_PAY,
          (SELECT CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER WHERE POLICY_ID=CPC.POLICY_ID) AS HOLDER_ID,
          (SELECT CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST WHERE LIST_ID=
              (SELECT INSURED_ID FROM DEV_PAS.T_BENEFIT_INSURED WHERE BUSI_ITEM_ID=CCBP.BUSI_ITEM_ID AND ORDER_ID=1)) AS INSU_ID,
          CPC.POLICY_CODE,CPC.ENDORSE_CODE,CPC.POLICY_ID,
          (SELECT SUM(INITIAL_DISCNT_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE POLICY_ID=CPC.POLICY_ID) AS PREM,
            EP.STATUS,'' as NOTICE_FLAG
          FROM DEV_PAS.T_ENDOR_PRINT EP
         INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
            ON EP.ACCEPT_CODE = CAC.Accept_Code
         INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC
            ON CAC.ACCEPT_ID = CPC.ACCEPT_ID
          LEFT JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL CCBP
            ON CPC.POLICY_CHG_ID = CCBP.POLICY_CHG_ID
           AND CCBP.MASTER_BUSI_ITEM_ID IS NULL
           AND CCBP.OLD_NEW = '0'
          inner join dev_pas.t_document doc
           on doc.policy_id = cpc.policy_id
           and doc.buss_code = cac.accept_code
           and ep.endorse_code = doc.document_no
		      WHERE (EP.VERIFY_CODE=#{cipher_text} OR EP.ANTI_FORGERY_CODE=#{cipher_text}) 
		    UNION ALL
		    SELECT 'CUS' FROM_MODLE, /*验真模块*/
			       CPC.APPLY_TIME AS MAKE_DATE, /*办理日期，保全取申请日期*/
			       CAC.SERVICE_CODE, /*保全项目*/
			       '' CUSTOMER_NAME, /*出险人姓名，保全为空*/
			       CAC.ACCEPT_CODE, /*保全受理号*/
			       '' CLM_NO, /*理赔赔号，查询保全项目为空*/
			       NULL STAND_PAY, /*理赔金额，保全为空*/
			       (SELECT CUSTOMER_ID
			          FROM DEV_PAS.T_POLICY_HOLDER
			         WHERE POLICY_ID = CPC.POLICY_ID) AS HOLDER_ID, /*投保人ID*/
			       (SELECT CUSTOMER_ID
			          FROM DEV_PAS.T_INSURED_LIST
			         WHERE LIST_ID = (SELECT INSURED_ID
			                            FROM DEV_PAS.T_BENEFIT_INSURED
			                           WHERE BUSI_ITEM_ID = CCBP.BUSI_ITEM_ID
			                             AND ORDER_ID = 1)) AS INSU_ID, /*被投保人ID*/
			       CPC.POLICY_CODE, /*保单号*/
			       CPC.ENDORSE_CODE, /*保全批单号*/
			       CPC.POLICY_ID, /*保单ID*/
			       (SELECT SUM(INITIAL_DISCNT_PREM_AF)
			          FROM DEV_PAS.T_CONTRACT_PRODUCT
			         WHERE POLICY_ID = CPC.POLICY_ID) AS PREM, /*首期保费*/
			       (SELECT (CASE
			                 WHEN PCD.INSERT_TIMESTAMP = MAX(T.INSERT_TIMESTAMP) THEN
			                  1
			                 ELSE
			                  0
			               END)
			          FROM DEV_PAS.T_PRINT_CLM_DOCUMENT T
			         WHERE T.DOC_LIST_ID = PCD.DOC_LIST_ID
			           AND ROWNUM = 1) STATUS, /*验真码状态*/
			       '2' AS NOTICE_FLAG /*个险补费通知书时，NOTICEFLAG的值置为2*/
			  FROM DEV_PAS.T_PRINT_CLM_DOCUMENT PCD
			 INNER JOIN DEV_PAS.V_DOCUMENT_ALL D
			    ON D.DOC_LIST_ID = PCD.DOC_LIST_ID
			 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
			    ON D.BUSS_CODE = CAC.ACCEPT_CODE
			 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC
			    ON CAC.ACCEPT_ID = CPC.ACCEPT_ID
			 INNER JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL CCBP
			    ON CCBP.CHANGE_ID = CPC.CHANGE_ID
			   AND CCBP.POLICY_CHG_ID = CPC.POLICY_CHG_ID
			 WHERE (PCD.ENCRYPT_AFTER_SECURITY = #{cipher_text} OR
			       PCD.ENCRYPT_AFTER_QR = #{cipher_text} /*防伪验证码*/
			       )
			   AND D.TEMPLATE_CODE = 'CUS_00006'
			   AND CCBP.MASTER_BUSI_ITEM_ID IS NULL
			   AND CCBP.OLD_NEW = '1'
			UNION ALL
			SELECT 'CLM' FROM_MODLE,CC.END_CASE_TIME AS MAKE_DATE,'' SERVICE_CODE,
			(SELECT CUSTOMER_NAME FROM DEV_CLM.T_CUSTOMER WHERE CUSTOMER_ID=CC.INSURED_ID) CUSTOMER_NAME,
			'' ACCEPT_CODE,CC.CASE_NO CLM_NO,
			(SELECT SUM(NVL(ACTUAL_PAY,0)) FROM DEV_CLM.T_CLAIM_CASE WHERE CASE_NO=CC.CASE_NO) STAND_PAY,
			(SELECT CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER WHERE POLICY_ID=CP.POLICY_ID) AS HOLDER_ID,
			(SELECT CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST WHERE LIST_ID=
			    (SELECT INSURED_ID FROM DEV_PAS.T_BENEFIT_INSURED WHERE BUSI_ITEM_ID=
			      (SELECT BUSI_ITEM_ID FROM DEV_PAS.T_CONTRACT_BUSI_PROD 
			        WHERE POLICY_ID=CP.POLICY_ID AND MASTER_BUSI_ITEM_ID IS NULL) AND ORDER_ID='1')) AS INSU_ID,
			CP.POLICY_CODE,'' ENDORSE_CODE,CP.POLICY_ID,
			(SELECT SUM(INITIAL_DISCNT_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE POLICY_ID=CP.POLICY_ID) AS PREM,
			  (SELECT 
		     (CASE WHEN PCD.INSERT_TIMESTAMP=MAX(T.INSERT_TIMESTAMP)  THEN 1 ELSE 0 END ) 
		  FROM DEV_CLM.T_PRINT_CLM_DOCUMENT T WHERE T.DOC_LIST_ID=PCD.DOC_LIST_ID 
		      ) STATUS,
		      '' AS NOTICE_FLAG
			FROM DEV_CLM.T_PRINT_CLM_DOCUMENT PCD,DEV_CLM.T_DOCUMENT D,DEV_CLM.T_CLAIM_CASE CC,DEV_CLM.T_CLAIM_POLICY CP
			WHERE (PCD.ENCRYPT_AFTER_SECURITY=#{cipher_text} OR PCD.ENCRYPT_AFTER_QR=#{cipher_text}) 
			AND D.DOC_LIST_ID=PCD.DOC_LIST_ID AND CC.CASE_NO=D.BUSS_CODE AND CC.CASE_ID=CP.CASE_ID
			AND NOT EXISTS(SELECT 1 FROM DEV_CLM.T_CLAIM_POLICY WHERE CASE_ID=CP.CASE_ID AND LIST_ID>CP.LIST_ID)
			UNION ALL
      SELECT 'CLM' FROM_MODLE,CC.END_CASE_TIME AS MAKE_DATE,'' SERVICE_CODE,
      (SELECT CUSTOMER_NAME FROM DEV_CLM.T_CUSTOMER WHERE CUSTOMER_ID=CC.INSURED_ID) CUSTOMER_NAME,
      '' ACCEPT_CODE,CC.CASE_NO CLM_NO,
      (SELECT SUM(NVL(ACTUAL_PAY,0)) FROM DEV_CLM.T_CLAIM_CASE WHERE CASE_NO=CC.CASE_NO) STAND_PAY,
      (SELECT CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER WHERE POLICY_ID=CP.POLICY_ID) AS HOLDER_ID,
      (SELECT CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST WHERE LIST_ID=
          (SELECT INSURED_ID FROM DEV_PAS.T_BENEFIT_INSURED WHERE BUSI_ITEM_ID=
            (SELECT max(BUSI_ITEM_ID) FROM DEV_PAS.T_CONTRACT_BUSI_PROD 
              WHERE POLICY_ID=CP.POLICY_ID AND MASTER_BUSI_ITEM_ID IS NULL) AND ORDER_ID='1')) AS INSU_ID,
      CP.POLICY_CODE,'' ENDORSE_CODE,CP.POLICY_ID,
      (SELECT SUM(INITIAL_DISCNT_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE POLICY_ID=CP.POLICY_ID) AS PREM,
        (SELECT 
     (CASE WHEN PCD.INSERT_TIMESTAMP=MAX(T.INSERT_TIMESTAMP)  THEN 1 ELSE 0 END ) 
  FROM DEV_CLM.T_PRINT_CLM_DOCUMENT T WHERE T.DOC_LIST_ID=PCD.DOC_LIST_ID 
      ) STATUS,
      '' AS NOTICE_FLAG
      FROM DEV_CLM.T_PRINT_CLM_DOCUMENT PCD,DEV_CLM.T_DOCUMENT D,DEV_CLM.T_CLAIM_CASE CC 
      left join DEV_CLM.T_CLAIM_POLICY CP on CC.CASE_ID=CP.CASE_ID
     WHERE (PCD.ENCRYPT_AFTER_SECURITY=#{cipher_text} OR PCD.ENCRYPT_AFTER_QR=#{cipher_text}) 
      AND D.DOC_LIST_ID=PCD.DOC_LIST_ID AND CC.CASE_NO=D.BUSS_CODE 
      AND NOT EXISTS(SELECT 1 FROM DEV_CLM.T_CLAIM_POLICY WHERE CASE_ID=CP.CASE_ID AND LIST_ID>CP.LIST_ID)
		]]>
		<if test=" css_flag  != null  and  css_flag  != ''  "><![CDATA[ 
			UNION ALL
			SELECT 'CSS' FROM_MODLE,PCD.INSERT_TIME AS MAKE_DATE,'' SERVICE_CODE,'' CUSTOMER_NAME,
			'' ACCEPT_CODE,'' CLM_NO,NULL STAND_PAY,NULL AS HOLDER_ID,NULL AS INSU_ID,
			'' POLICY_CODE,'' ENDORSE_CODE,NULL AS POLICY_ID,NULL AS PREM,
			(SELECT 
		     (CASE WHEN PCD.INSERT_TIMESTAMP=MAX(T.INSERT_TIMESTAMP)  THEN 1 ELSE 0 END ) 
			  FROM DEV_PAS.T_PRINT_CLM_DOCUMENT T WHERE T.DOC_LIST_ID=PCD.DOC_LIST_ID AND ROWNUM=1
			      ) STATUS,
	     	 '' AS NOTICE_FLAG
			FROM DEV_PAS.T_PRINT_CLM_DOCUMENT PCD
			LEFT JOIN DEV_PAS.V_DOCUMENT_ALL D ON PCD.DOC_LIST_ID = D.DOC_LIST_ID
			WHERE (PCD.ENCRYPT_AFTER_SECURITY=#{cipher_text} OR PCD.ENCRYPT_AFTER_QR=#{cipher_text})
			AND D.TEMPLATE_CODE='CSS_00001'
		]]></if>
		<![CDATA[ 
		UNION ALL
		SELECT 'CAP' FROM_MODLE,
		       COALESCE(CASE WHEN MAX(TCD.PAY_MODE) IN ('10', '22', '31', '11') THEN COALESCE(MAX(TBD.ARAP_DATE),MAX(TCD.ARAP_DATE))
		                     WHEN MAX(TCD.PAY_MODE) IN ('50') THEN MAX(TCD.ARAP_DATE)
		                     ELSE MAX(TCD.FINISH_TIME) END,
		                CASE WHEN MAX(TBD.BILL_MODE) IN ('10', '22', '31', '11') THEN MAX(TBD.ARAP_DATE)
		                   END) MAKE_DATE,
		       '' SERVICE_CODE,
		       (select MAX(T.HOLDER_NAME) from Dev_Cap.T_PREM_ARAP T where T.UNIT_NUMBER = TVP.UNIT_NUMBER GROUP BY T.UNIT_NUMBER) CUSTOMER_NAME,
		       '' ACCEPT_CODE,
		       '' CLM_NO,
		       NULL STAND_PAY,
		       NULL AS HOLDER_ID,
		       NULL AS INSU_ID,
		       (SELECT to_char(wm_concat(distinct T.POLICY_CODE)) FROM DEV_CAP.T_PREM_ARAP T where T.UNIT_NUMBER = TVP.UNIT_NUMBER
		           GROUP BY T.UNIT_NUMBER) POLICY_CODE,
		       '' ENDORSE_CODE,
		       NULL AS POLICY_ID,
		       NULL AS PREM,
		       MAX(TVP.PRINT_STATUS) STATUS,
		       MAX(TVP.Print_Type) AS NOTICE_FLAG
		  FROM DEV_CAP.T_VOUCHER_PRINT TVP
		  LEFT JOIN DEV_CAP.T_CASH_DETAIL TCD
		    ON TVP.UNIT_NUMBER = TCD.UNIT_NUMBER
		  LEFT JOIN DEV_CAP.T_BILL_DETAILS TBD
		    ON TVP.UNIT_NUMBER = TBD.UNIT_NUMBER
		 WHERE TVP.SECURITY_CODE_AFTER = #{cipher_text}
		 GROUP BY TVP.UNIT_NUMBER ]]>
	</select>
	
	
		<!-- 根据受理号查询保全信息 -->
	<select id="queryAcceptChangeByAcceptCode" resultType="java.util.Map" parameterType="java.util.Map">
	      select 
	      a.accept_id,a.change_id,tce.endorse_code,
	      tce.validate_time,
	      tce.organ_code,
	      tce.apply_time,
	      tce.policy_code,
	      tcu.customer_name,
	      a.accept_status,
          ts.status_desc
	      from 
	      dev_pas.t_cs_accept_change a,dev_pas.t_cs_policy_change tce,
	      dev_pas.t_policy_holder tph,
          dev_pas.t_customer tcu,
          dev_pas.t_accept_status ts
	      where  a.accept_id = tce.accept_id
	      and ts.accept_status = a.accept_status
	      and tph.policy_code = tce.policy_code
          and tcu.customer_id = tph.customer_id
	      <if test=" accept_code != null and accept_code != ''">
	      <![CDATA[ AND a.ACCEPT_CODE = #{accept_code} ]]>
	      </if>
	</select>
	
	
	
	<!-- 核心保单验真 -->
	<select id="judgeWebPolicyDocumentPseudoVer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT (CASE WHEN CPC.Endorse_Code is not null THEN 'CUS' WHEN CPC.POLICY_CODE  is not null  THEN 'PAS' ELSE '' END) FROM_MODLE,
			CPC.APPLY_TIME AS MAKE_DATE,CAC.SERVICE_CODE,'' CUSTOMER_NAME,CAC.ACCEPT_CODE,'' CLM_NO,NULL STAND_PAY,
			(SELECT CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER WHERE POLICY_ID=CPC.POLICY_ID) AS HOLDER_ID,
			(SELECT CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST WHERE LIST_ID=
			    (SELECT INSURED_ID FROM DEV_PAS.T_BENEFIT_INSURED WHERE BUSI_ITEM_ID=CCBP.BUSI_ITEM_ID AND ORDER_ID=1)) AS INSU_ID,
			CPC.POLICY_CODE,CPC.ENDORSE_CODE,CPC.POLICY_ID,
			(SELECT SUM(INITIAL_DISCNT_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE POLICY_ID=CPC.POLICY_ID) AS PREM,
			EP.STATUS,'' AS NOTICE_FLAG
			FROM DEV_PAS.T_ENDOR_PRINT EP
			INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC  ON  EP.ACCEPT_CODE=CAC.Accept_Code  
			INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC  ON  CAC.ACCEPT_ID=CPC.ACCEPT_ID 
			LEFT JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL CCBP  ON  CPC.POLICY_CHG_ID=CCBP.POLICY_CHG_ID AND CCBP.MASTER_BUSI_ITEM_ID IS NULL AND CCBP.OLD_NEW='0'
			WHERE (EP.VERIFY_CODE=#{cipher_text} OR EP.ANTI_FORGERY_CODE=#{cipher_text}) 
			UNION ALL
		    SELECT 'CUS' FROM_MODLE, /*验真模块*/
			       CPC.APPLY_TIME AS MAKE_DATE, /*办理日期，保全取申请日期*/
			       CAC.SERVICE_CODE, /*保全项目*/
			       '' CUSTOMER_NAME, /*出险人姓名，保全为空*/
			       CAC.ACCEPT_CODE, /*保全受理号*/
			       '' CLM_NO, /*理赔赔号，查询保全项目为空*/
			       NULL STAND_PAY, /*理赔金额，保全为空*/
			       (SELECT CUSTOMER_ID
			          FROM DEV_PAS.T_POLICY_HOLDER
			         WHERE POLICY_ID = CPC.POLICY_ID) AS HOLDER_ID, /*投保人ID*/
			       (SELECT CUSTOMER_ID
			          FROM DEV_PAS.T_INSURED_LIST
			         WHERE LIST_ID = (SELECT INSURED_ID
			                            FROM DEV_PAS.T_BENEFIT_INSURED
			                           WHERE BUSI_ITEM_ID = CCBP.BUSI_ITEM_ID
			                             AND ORDER_ID = 1)) AS INSU_ID, /*被投保人ID*/
			       CPC.POLICY_CODE, /*保单号*/
			       CPC.ENDORSE_CODE, /*保全批单号*/
			       CPC.POLICY_ID, /*保单ID*/
			       (SELECT SUM(INITIAL_DISCNT_PREM_AF)
			          FROM DEV_PAS.T_CONTRACT_PRODUCT
			         WHERE POLICY_ID = CPC.POLICY_ID) AS PREM, /*首期保费*/
			       (SELECT (CASE
			                 WHEN PCD.INSERT_TIMESTAMP = MAX(T.INSERT_TIMESTAMP) THEN
			                  1
			                 ELSE
			                  0
			               END)
			          FROM DEV_PAS.T_PRINT_CLM_DOCUMENT T
			         WHERE T.DOC_LIST_ID = PCD.DOC_LIST_ID
			           AND ROWNUM = 1) STATUS, /*验真码状态*/
			       '2' AS NOTICE_FLAG /*个险补费通知书时，NOTICEFLAG的值置为2*/
			  FROM DEV_PAS.T_PRINT_CLM_DOCUMENT PCD
			 INNER JOIN DEV_PAS.V_DOCUMENT_ALL D
			    ON D.DOC_LIST_ID = PCD.DOC_LIST_ID
			 INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE CAC
			    ON D.BUSS_CODE = CAC.ACCEPT_CODE
			 INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE CPC
			    ON CAC.ACCEPT_ID = CPC.ACCEPT_ID
			 INNER JOIN DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL CCBP
			    ON CCBP.CHANGE_ID = CPC.CHANGE_ID
			   AND CCBP.POLICY_CHG_ID = CPC.POLICY_CHG_ID
			 WHERE (PCD.ENCRYPT_AFTER_SECURITY = #{cipher_text} OR
			       PCD.ENCRYPT_AFTER_QR = #{cipher_text} /*防伪验证码*/
			       )
			   AND D.TEMPLATE_CODE = 'CUS_00006'
			   AND CCBP.MASTER_BUSI_ITEM_ID IS NULL
			   AND CCBP.OLD_NEW = '1'
			UNION ALL
			SELECT 'CLM' FROM_MODLE,CC.END_CASE_TIME AS MAKE_DATE,'' SERVICE_CODE,
			(SELECT CUSTOMER_NAME FROM DEV_CLM.T_CUSTOMER WHERE CUSTOMER_ID=CC.INSURED_ID) CUSTOMER_NAME,
			'' ACCEPT_CODE,CC.CASE_NO CLM_NO,
			(SELECT SUM(NVL(ACTUAL_PAY,0)) FROM DEV_CLM.T_CLAIM_CASE WHERE CASE_NO=CC.CASE_NO) STAND_PAY,
			(SELECT CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER WHERE POLICY_ID=CP.POLICY_ID) AS HOLDER_ID,
			(SELECT CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST WHERE LIST_ID=
			    (SELECT INSURED_ID FROM DEV_PAS.T_BENEFIT_INSURED WHERE BUSI_ITEM_ID=
			      (SELECT BUSI_ITEM_ID FROM DEV_PAS.T_CONTRACT_BUSI_PROD 
			        WHERE POLICY_ID=CP.POLICY_ID AND MASTER_BUSI_ITEM_ID IS NULL) AND ORDER_ID='1')) AS INSU_ID,
			CP.POLICY_CODE,'' ENDORSE_CODE,CP.POLICY_ID,
			(SELECT SUM(INITIAL_DISCNT_PREM_AF) FROM DEV_PAS.T_CONTRACT_PRODUCT WHERE POLICY_ID=CP.POLICY_ID) AS PREM,
			(SELECT 
		     (CASE WHEN PCD.INSERT_TIMESTAMP=MAX(T.INSERT_TIMESTAMP)  THEN 1 ELSE 0 END ) 
		  FROM DEV_CLM.T_PRINT_CLM_DOCUMENT T WHERE T.DOC_LIST_ID=PCD.DOC_LIST_ID 
		      ) STATUS,
		      '' AS NOTICE_FLAG
			FROM DEV_CLM.T_PRINT_CLM_DOCUMENT PCD,DEV_CLM.T_DOCUMENT D,DEV_CLM.T_CLAIM_CASE CC,DEV_CLM.T_CLAIM_POLICY CP
			WHERE (PCD.ENCRYPT_AFTER_SECURITY=#{cipher_text} OR PCD.ENCRYPT_AFTER_QR=#{cipher_text}) 
			AND D.DOC_LIST_ID=PCD.DOC_LIST_ID AND CC.CASE_NO=D.BUSS_CODE AND CC.CASE_ID=CP.CASE_ID
			AND NOT EXISTS(SELECT 1 FROM DEV_CLM.T_CLAIM_POLICY WHERE CASE_ID=CP.CASE_ID AND LIST_ID>CP.LIST_ID)
		]]>
		<if test=" css_flag  != null  and  css_flag  != ''  "><![CDATA[ 
			UNION ALL
			SELECT 'CSS' FROM_MODLE,PCD.INSERT_TIME AS MAKE_DATE,'' SERVICE_CODE,'' CUSTOMER_NAME,
			'' ACCEPT_CODE,'' CLM_NO,NULL STAND_PAY,NULL AS HOLDER_ID,NULL AS INSU_ID,
			'' POLICY_CODE,'' ENDORSE_CODE,NULL AS POLICY_ID,NULL AS PREM,NULL STATUS, '' AS NOTICE_FLAG
			FROM DEV_PAS.T_PRINT_CLM_DOCUMENT PCD
			LEFT JOIN DEV_PAS.V_DOCUMENT_ALL D ON PCD.DOC_LIST_ID = D.DOC_LIST_ID
			WHERE (PCD.ENCRYPT_AFTER_SECURITY=#{cipher_text} OR PCD.ENCRYPT_AFTER_QR=#{cipher_text})
			AND D.TEMPLATE_CODE='CSS_00001'
		]]></if>
	</select>
	
	
	<!-- 根据受理号查询受理信息 -->
	<select id="findAcceptChangeByCode" resultType="java.util.Map" parameterType="java.util.Map">
	   select te.accept_id from dev_pas.t_cs_accept_change te
	   where te.accept_code=#{accept_code}
	
	</select>
	
	
</mapper>