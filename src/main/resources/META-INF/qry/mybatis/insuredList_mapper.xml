<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="insuredList">
 
	<sql id="PA_insuredListWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" stand_life  != null "><![CDATA[ AND A.STAND_LIFE = #{stand_life} ]]></if>
		<if test=" smoking  != null "><![CDATA[ AND A.SMOKING = #{smoking} ]]></if>
		<if test=" relation_to_insured_1 != null and relation_to_insured_1 != ''  "><![CDATA[ AND A.RELATION_TO_INSURED_1 = #{relation_to_insured_1} ]]></if>
		<if test=" job_kind != null and job_kind != ''  "><![CDATA[ AND A.JOB_KIND = #{job_kind} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" relation_to_ph != null and relation_to_ph != ''  "><![CDATA[ AND A.RELATION_TO_PH = #{relation_to_ph} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" insured_age  != null "><![CDATA[ AND A.INSURED_AGE = #{insured_age} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" case_id  != null and case_id != '' "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryInsuredListByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryInsuredListByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryInsuredListByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	
	<sql id="PA_queryInsuredListByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<sql id="PA_findAllInsuredInfoByPolicyCode">
		<if test=" policy_code  != null "><![CDATA[ AND a.POLICY_CODE = trim(#{policy_code}) ]]></if>
	</sql>
	<sql id="QRY_queryInsuredListByPolicyCodeCondition">
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="QRY_queryInsuredListByApplyCodeCondition">
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>


	<sql id="NB_queryProposalListForUw">
		<if test="proposalList != null and proposalList.size() > 0">
			<if test=" proposal_new_status == 2 or proposal_new_status == 3">
				AND NCM.PROPOSAL_STATUS IN
				<foreach collection="proposalList" index="index" item="proposalNew" open="(" separator="," close=")">
					#{proposalNew }
				</foreach>
			</if>
			<if test=" proposal_new_status == 1">
				AND NCM.PROPOSAL_STATUS NOT IN
				<foreach collection="proposalList" index="index" item="proposalNew" open="(" separator="," close=")">
					#{proposalNew }
				</foreach>
			</if>
		</if>
	</sql>
	
	<sql id="NB_queryProposalListForPAS">
		<if test=" proposal_new_status == 1">
			AND CM.LIABILITY_STATE != 3
		</if>
		<if test=" proposal_new_status == 2">
			AND CM.LIABILITY_STATE = 3
		</if>
	</sql>

<!-- 按索引查询操作 -->	
	<select id="PA_findInsuredListByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.RELATION_TO_INSURED_1, A.JOB_KIND, A.CUSTOMER_ID, 
			A.CUSTOMER_HEIGHT, A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, 
			A.POLICY_CODE, A.INSURED_AGE, A.LIST_ID, A.POLICY_ID FROM T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_queryInsuredListByListIdCondition" />
	</select>
	
	<select id="PA_findInsuredListByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.RELATION_TO_INSURED_1, A.JOB_KIND, A.CUSTOMER_ID, 
			A.CUSTOMER_HEIGHT, A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, 
			A.POLICY_CODE, A.INSURED_AGE, A.LIST_ID, A.POLICY_ID FROM T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_queryInsuredListByPolicyIdCondition" />
	</select>
	
	<select id="PA_findInsuredListByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.RELATION_TO_INSURED_1, A.JOB_KIND, A.CUSTOMER_ID, 
			A.CUSTOMER_HEIGHT, A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, 
			A.POLICY_CODE, A.INSURED_AGE, A.LIST_ID, A.POLICY_ID FROM T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_queryInsuredListByAddressIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.RELATION_TO_INSURED_1, A.JOB_KIND, A.CUSTOMER_ID, 
			A.CUSTOMER_HEIGHT, A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, 
			A.POLICY_CODE, A.INSURED_AGE, A.LIST_ID, A.POLICY_ID FROM T_INSURED_LIST A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 A.JOB_KIND,-->
	<select id="PA_findAllInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.RELATION_TO_INSURED_1,  A.CUSTOMER_ID, 
			A.CUSTOMER_HEIGHT, A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, 
			A.POLICY_CODE, A.INSURED_AGE, A.LIST_ID, A.POLICY_ID, A.JOB_UNDERWRITE FROM DEV_PAS.T_INSURED_LIST A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_insuredListWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findInsuredListTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_insuredListWhereCondition" />
	</select>
<!-- 查询个数操作 -->
	<select id="findProductAmountByBusiItmeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(CP.AMOUNT) AMOUNT_SUM FROM DEV_PAS.T_CONTRACT_PRODUCT CP
                  WHERE CP.BUSI_ITEM_ID = #{busi_item_id} ]]>
	</select>
<!-- 查询个数操作 -->
	<select id="findApplyProductAmountByBusiItmeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SUM(CP.AMOUNT) AS AMOUNT_SUM FROM DEV_NB.T_NB_CONTRACT_PRODUCT CP WHERE CP.BUSI_ITEM_ID = #{busi_item_id} ]]>
	</select>		
<!-- 查询个数操作 -->
	<select id="queryDocumentByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT  D.DOCUMENT_NO,D.TEMPLATE_CODE  FROM DEV_PAS.V_DOCUMENT_ALL D WHERE 1=1
AND D.TEMPLATE_CODE NOT IN ('UWS_00003','UN012')
 AND D.BUSS_CODE = #{buss_code} ]]>
	</select>
<!-- 查询个数操作 -->
	<select id="findPolicyInfoByInsuredCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT B.*,
                       (CASE
                         WHEN B.SUB_POLICY_CODE IS NOT NULL THEN
                          ''
                         ELSE
                          B.BUSI_PROD_NAME_T
                       END) AS BUSI_PROD_NAME, /*主险名称*/
                       (CASE
                         WHEN B.SUB_POLICY_CODE IS NOT NULL THEN
                          null
                         ELSE
                          B.AMOUNT_T
                       END) AS AMOUNT, /*主险保额*/
                       (CASE
                         WHEN B.SUB_POLICY_CODE IS NOT NULL THEN
                          B.BUSI_PROD_NAME_T
                         ELSE
                          ''
                       END) AS SUB_BUSI_PRODUCT_NAME, /*附加险名称*/
                       (CASE
                         WHEN B.SUB_POLICY_CODE IS NOT NULL THEN
                          B.AMOUNT_T
                         ELSE
                          null
                       END) AS SUB_POLICY_AMOUNT, /*附加险保额*/
                       (CASE
				         WHEN ((B.AGE >= 18 AND BMI > 0) OR B.EXCEPTION_HEALTH_FLAG >0) THEN
				          1 + IS_EXCEPTION_NOTIFY_FLAG
				         ELSE
				          0 + IS_EXCEPTION_NOTIFY_FLAG
				       END) IS_EXCEPTION_NOTIFY_FLAG2
                  from (             ]]>
  <![CDATA[ select 
  POLICY_CODE,
  CUSTOMER_ID,
  VALIDATE_DATE,
  DECISION_CODE,
  POSTPONED,
  PRODUCT_CATEGORY,
  CHANNEL_TYPE,
  LIABILITY_STATE,
  BUSI_PROD_CODE,
  BUSI_PROD_NAME_T, /*险种名称*/
  BUSI_ITEM_ID,
  APPLY_CODE,
  SUBMIT_CHANNEL,
  CHARGE_YEAR, /*缴费年期*/
  COVERAGE_PERIOD, /*保障年期类型*/
  COVERAGE_YEAR, /*保障年期*/
  PREM_FREQ, /*缴费频率*/
  STD_PREM_AF, /*标准保费*/
  EXTRA_PREM_AF, /*加费*/
  TOTAL_PREM_AF, /*总保费，已缴保费的合计*/
  AMOUNT_T, /*客户投保金额（元）*/
  SUB_POLICY_CODE,
  IS_EXCEPTION_NOTIFY_FLAG,
  UW_ID,
  MULTI_MAINRISK_FLAG,
  PHONE_NUMBER,
 ALL_PRODUCT_NAME,
  ANNUAL_INCOME_CEIL, /*年收入*/
  ORGAN_CODE,
   NON_STANDARDS,
   NON_STANDARDSS,
   UW_DECISION_CODE,
   UW_06,
   EUM004_TOTAL,
   TOTAL_1,
   TOTAL_2,
   AGE,
   BMI,
   EXCEPTION_HEALTH_FLAG
   from (
  SELECT 
               row_number() over(partition by IL.POLICY_CODE order by TP.AMOUNT desc) rn ,
             IL.POLICY_CODE, IL.CUSTOMER_ID, CM.VALIDATE_DATE,
		    CM.DECISION_CODE,
(SELECT  C.POSTPONED_MONTHS
  FROM DEV_UW.T_UW_BUSI_PROD A,
          DEV_UW.T_UW_MASTER    B,
            DEV_UW.T_UW_PRODUCT   C
       WHERE A.UW_ID = B.UW_ID
     AND B.BIZ_CODE=CM.APPLY_CODE
     AND A.DECISION_CODE = '40'
     AND B.UW_SOURCE_TYPE = '1'
    AND A.UW_ID = C.UW_ID
  AND A.UW_BUSI_ID=C.UW_BUSI_ID
       AND C.ITEM_ID = TP.ITEM_ID
 AND ROWNUM=1)  AS  POSTPONED,
		BP.PRODUCT_CATEGORY,
  CM.CHANNEL_TYPE,CM.LIABILITY_STATE,CBP.BUSI_PROD_CODE,
  BP.PRODUCT_NAME_SYS  AS BUSI_PROD_NAME_T,/*险种名称*/
  CBP.BUSI_ITEM_ID,CM.APPLY_CODE,CM.SUBMIT_CHANNEL,
  TP.CHARGE_YEAR,/*缴费年期*/
  TP.COVERAGE_PERIOD, /*保障年期类型*/
  TP.COVERAGE_YEAR, /*保障年期*/
  TP.PREM_FREQ, /*缴费频率*/
  TP.STD_PREM_AF, /*标准保费*/
  TP.EXTRA_PREM_AF, /*加费*/
  TP.TOTAL_PREM_AF, /*总保费，已缴保费的合计*/
  TP.AMOUNT AS AMOUNT_T, /*客户投保金额（元）*/
  (SELECT SUB_POLICY_CODE FROM DEV_PAS.T_CONTRACT_RELATION CR WHERE CR.SUB_POLICY_CODE=IL.POLICY_CODE AND CR.RELATION_TYPE = '1') AS SUB_POLICY_CODE,/*附加险保单号*/
  
  (SELECT COUNT(0) FROM DEV_PAS.T_QUESTIONAIRE_CUSTOMER DC
                 INNER JOIN DEV_PAS.T_QUESTIONAIRE_INFO QI
                    ON DC.SURVEY_QUESTION_ID = QI.SURVEY_QUESTION_ID
                 WHERE 1 = 1
                   AND (DC.APPLY_CODE = IL.APPLY_CODE or DC.POLICY_CODE = IL.POLICY_CODE)
                   AND DC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
                   AND DC.CUSTOMER_ID = #{customer_id}
                   AND ((QI.SURVEY_VERSION = '19' AND
                                QI.SURVEY_CODE NOT IN
                                ('000',
                                   '001A',
                                   '001B',
                                   '002A',
                                   '012A',
                                   '014')) OR (QI.SURVEY_VERSION = '20' AND
                                QI.SURVEY_CODE NOT IN
                                ('000',
                                                 '001',
                                                 '002',
                                                 '003',
                                                 '013A',
                                                 '015',
                                                 '021',
                                                 '022',
                                                 '023',
                                                 '024',
                                                 '025')) OR
                                (QI.SURVEY_VERSION = '65' AND
                                QI.SURVEY_CODE NOT IN ('000', '015')) OR
                                (QI.SURVEY_VERSION = '25' AND
                                QI.SURVEY_CODE IN
                                ('004',
                                   '005',
                                   '006',
                                   '007',
                                   '008',
                                   '009',
                                   '010',
                                   '011',
                                   '012',
                                   '012B',
                                   '013',
                                   '015',
                                   '016',
                                   '017',
                                   '018',
                                   '024')) OR
                                (QI.SURVEY_VERSION = '52' AND
                                QI.SURVEY_CODE NOT IN ('000', '023')))
                   AND SUBSTR2(DC.SURVEY_MODULE_RESULT, 0, 1) = '是'
                )  AS IS_EXCEPTION_NOTIFY_FLAG,
    (SELECT  B.UW_ID FROM DEV_UW.T_UW_MASTER B 
                    WHERE B.BIZ_CODE = CM.APPLY_CODE) AS UW_ID,
	CM.MULTI_MAINRISK_FLAG,
	(SELECT COUNT(*) FROM DEV_UW.T_UW_PHONE_MASTER PM WHERE PM.IS_REPLY = '1' AND PM.APPLY_CODE = CM.APPLY_CODE) AS PHONE_NUMBER,
    (SELECT TO_CHAR(WM_CONCAT(PRODUCT_NAME_SYS)) FROM DEV_PDS.T_BUSINESS_PRODUCT PBP
		WHERE PBP.PRODUCT_CODE_SYS IN (SELECT PABP.BUSI_PROD_CODE
     		FROM DEV_PAS.T_CONTRACT_BUSI_PROD PABP WHERE PABP.POLICY_CODE = CM.POLICY_CODE ]]>
     		
     		<if test="query_busi_code==null or query_busi_code!=1">
     			AND PABP.MASTER_BUSI_ITEM_ID IS NULL
     		</if>
     		<if test="query_busi_code==1">
     			AND PABP.BUSI_PROD_CODE in (${busi_prod_code})
     		</if>
     		<![CDATA[ )) AS ALL_PRODUCT_NAME,
            
           	(CASE WHEN IL.ANNUAL_INCOME_CEIL IS NOT NULL THEN
	          TO_CHAR(IL.ANNUAL_INCOME_CEIL)
	         ELSE
	          (SELECT SURVEY_MODULE_RESULT
	             FROM DEV_PAS.T_QUESTIONAIRE_CUSTOMER TQC
	            WHERE CM.POLICY_CODE = TQC.POLICY_CODE
	              AND TQC.SURVEY_QUESTION_ID in (833, 564, 1093)
	              AND TQC.QUESTIONAIRE_OBJECT = 2)
	       		end) AS ANNUAL_INCOME_CEIL, /*年收入*/
               CM.ORGAN_CODE, (SELECT TUP.NON_STANDARDS FROM DEV_UW.T_UW_POLICY TUP WHERE 
                  	   TUP.APPLY_CODE = IL.APPLY_CODE AND TUP.UW_SOURCE_TYPE = '1' AND ROWNUM = 1) AS NON_STANDARDS,
                  	   
            (SELECT TIL.NON_STANDARDS
                             FROM   DEV_UW.T_INSURED_LIST TIL,DEV_UW.T_UW_POLICY TUP 
                            WHERE TUP.UW_SOURCE_TYPE = '1'
                              AND TIL.APPLY_CODE = CM.APPLY_CODE
                              and TUP.Apply_Code = CM.APPLY_CODE
                              AND TIL.UW_ID = TUP.UW_ID
                              AND TIL.CUSTOMER_ID = #{customer_id }) AS  NON_STANDARDSS,
                              
                              
                              
                  	   	 (CASE
                                 WHEN (SELECT COUNT(DISTINCT TIL.list_id)
                                         FROM APP___nb__DBUSER.t_nb_insured_list    TIL
                                        WHERE TIL.ORDER_ID = 1
                                          AND TIL.APPLY_CODE = CM.APPLY_CODE
                                          AND TIL.CUSTOMER_ID = #{customer_id }) = 1 THEN
                                  ''
                                 ELSE
                                  (SELECT TO_CHAR( COUNT(1))
                                     FROM APP___UW__DBUSER.t_uw_busi_prod    M,
                                          APP___UW__DBUSER.T_BENEFIT_INSURED A,
                                          APP___UW__DBUSER.T_INSURED_LIST    TIL
                                    WHERE A.ORDER_ID = 1
                                      AND TIL.LIST_ID = A.INSURED_ID
                                      AND A.UW_ID = M.UW_ID
                                      AND TIL.APPLY_CODE = CM.APPLY_CODE
                                      AND A.BUSI_ITEM_ID = M.BUSI_ITEM_ID
                                      and m.decision_code is not null
                                      and m.decision_code <> '10'
                                      AND TIL.CUSTOMER_ID = #{customer_id })
                               END  ) UW_DECISION_CODE,
                      (CASE
 						  WHEN ( SELECT COUNT(1) FROM DEV_UW.T_UW_DOCUMENT_VERIFY TDV, APP___NB__DBUSER.T_DOCUMENT TD
 						    WHERE TDV.DOCUMENT_NO = TD.DOCUMENT_NO
 						      AND TD.BUSS_CODE = CM.APPLY_CODE
 						      AND TDV.UW_ID = TD.BUSS_ID
 						      AND TD.BUSS_SOURCE_CODE = '002'
 						      AND TD.TEMPLATE_CODE = 'UWS_00006'
 						      AND TDV.CUSTOMER_ID = #{customer_id }   ) = 0 THEN
 						      ( SELECT COUNT(1) FROM APP___NB__DBUSER.T_DOCUMENT TD
 						        WHERE TD.BUSS_CODE = CM.APPLY_CODE
 						          AND TD.BUSS_SOURCE_CODE = '002'
 						          AND TD.TEMPLATE_CODE = 'UWS_00006' )
 						      ELSE 1 END ) UW_06,         
                  	  (CASE
                         WHEN (
                           SELECT TIS.DATA_SAVE_NO
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS
                        WHERE TIS.BUSS_CODE = CM.APPLY_CODE
                           AND TIS.BILLCARD_CODE = 'EUM004') IS NULL
                            THEN                 
                          (SELECT COUNT(TIS.BUSS_CODE)
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS
                         WHERE TIS.BUSS_CODE = CM.APPLY_CODE
                           AND TIS.BILLCARD_CODE = 'EUM004') 
                          ELSE
                            (SELECT COUNT(TIS.BUSS_CODE)
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS ,APP___NB__DBUSER.T_IMAGE_DATA_INFO TID ,APP___NB__DBUSER.T_CUSTOMER TCC
                         WHERE TIS.BUSS_CODE = CM.APPLY_CODE
                         AND TIS.DATA_SAVE_NO = TID.DATA_SAVE_NO
                         AND TCC.CUSTOMER_NAME = TID.CUSTOMER_NAME
                         AND TCC.CUSTOMER_BIRTHDAY = TID.CUSTOMER_BIRTHDAY
                         AND TCC.CUSTOMER_GENDER = TID.CUSTOMER_GENDER
                         AND TCC.CUSTOMER_CERTI_CODE = TID.CUSTOMER_CERTI_CODE
                         AND TCC.CUSTOMER_CERT_TYPE = TID.CUSTOMER_CERT_TYPE
                           AND TID.CATEGORY_TAB_CODE LIKE 'EUM004%'
                           AND TCC.CUSTOMER_ID = #{customer_id }
                           ) 
                       END) EUM004_TOTAL,           
             (SELECT COUNT(1)
             FROM DEV_UW.T_UW_TRACE T1, DEV_UW.T_UW_MASTER U
            WHERE U.BIZ_CODE = CM.APPLY_CODE AND T1.UW_ID = U.UW_ID
              AND T1.UW_EVENT_CODE = '13') AS TOTAL_1,
              (SELECT COUNT(1)
             FROM DEV_UW.T_UW_AUTO T2, DEV_UW.T_RULE_RESULT T3, DEV_UW.T_UW_MASTER U
            WHERE T2.AUTO_ID = T3.AUTO_ID AND T2.UW_ID = U.UW_ID
              AND U.BIZ_CODE = CM.APPLY_CODE) AS TOTAL_2,
            (SELECT FLOOR(MONTHS_BETWEEN(CM.APPLY_DATE, TC.CUSTOMER_BIRTHDAY) / 12) FROM DEV_PAS.T_CUSTOMER TC
                WHERE TC.CUSTOMER_ID = IL.CUSTOMER_ID) AS AGE,
            (SELECT SUM(CASE WHEN (CASE
                           WHEN TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                            0,
                                            INSTR(TC.SURVEY_MODULE_RESULT,
                                                  ',',
                                                  1) - 1)) = 0 THEN
                            22
                           ELSE
                            (CASE
                              WHEN INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) = 0 THEN
                               SUBSTR(TC.SURVEY_MODULE_RESULT,
                                      INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1)
                              ELSE
                               SUBSTR(TC.SURVEY_MODULE_RESULT,
                                      INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1,
                                      INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) - 1)
                            END) /
                            ((TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                          0,
                                          INSTR(TC.SURVEY_MODULE_RESULT,
                                                ',',
                                                1) - 1)) / 100) *
                            (TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                          0,
                                          INSTR(TC.SURVEY_MODULE_RESULT,
                                                ',',
                                                1) - 1)) / 100))
                         END) < 15
                    
                     THEN
                     1
                  
                    WHEN
                    
                     (CASE
                       WHEN TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                        0,
                                        INSTR(TC.SURVEY_MODULE_RESULT,
                                              ',',
                                              1) - 1)) = 0 THEN
                        22
                       ELSE
                        (CASE
                          WHEN INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) = 0 THEN
                           SUBSTR(TC.SURVEY_MODULE_RESULT,
                                  INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1)
                          ELSE
                           SUBSTR(TC.SURVEY_MODULE_RESULT,
                                  INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1,
                                  INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) - 1)
                        END) /
                        ((TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                      0,
                                      INSTR(TC.SURVEY_MODULE_RESULT,
                                            ',',
                                            1) - 1)) / 100) *
                        (TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                      0,
                                      INSTR(TC.SURVEY_MODULE_RESULT,
                                            ',',
                                            1) - 1)) / 100))
                     END) > 31
                    
                     THEN
                     1
                    ELSE
                     0
                  END)
         FROM DEV_PAS.T_QUESTIONAIRE_CUSTOMER TC,
              DEV_PAS.T_QUESTIONAIRE_INFO     I
        WHERE TC.SURVEY_QUESTION_ID = I.SURVEY_QUESTION_ID
          AND (I.SURVEY_VERSION IN
              ('65',
                '20',
                '19',
                '25',
                '52',
                '22',
                '32',
                '42') AND I.SURVEY_CODE = '000' OR
              I.SURVEY_VERSION = '102' AND
              I.SURVEY_CODE = '030')
          AND TC.APPLY_CODE = CM.APPLY_CODE
          AND TC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
          AND TC.CUSTOMER_ID = IL.CUSTOMER_ID
          AND TC.SURVEY_MODULE_RESULT IS NOT NULL) AS BMI,
          IL.EXCEPTION_HEALTH_FLAG
   FROM  ]]>
  <if test="query_busi_code==1 or query_busi_code==2">
      DEV_PAS.T_POLICY_HOLDER IL
  </if >
  <if test="query_busi_code==null or (query_busi_code!=1 and query_busi_code!=2)">
       DEV_PAS.T_INSURED_LIST IL
  </if>
 <![CDATA[ INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
    ON IL.POLICY_CODE = CM.POLICY_CODE]]>
    
    <include refid="NB_queryProposalListForPAS"/>
    
 <![CDATA[ INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
    ON CM.POLICY_CODE = CBP.POLICY_CODE]]>
    <if test="query_busi_code==null or query_busi_code!=1">
	AND CBP.MASTER_BUSI_ITEM_ID IS NULL
	AND (CBP.ORDER_ID IS NULL OR CBP.ORDER_ID = (
	SELECT MIN(LNTS.ORDER_ID) FROM DEV_PAS.T_CONTRACT_BUSI_PROD LNTS WHERE
	LNTS.POLICY_CODE = CBP.POLICY_CODE AND LNTS.MASTER_BUSI_ITEM_ID IS NULL
	))
    </if>
    <if test="query_busi_code==1">
	AND (CBP.ORDER_ID IS NULL OR CBP.ORDER_ID = (
	(SELECT MIN(LNTS.ORDER_ID) FROM DEV_PAS.T_CONTRACT_BUSI_PROD LNTS WHERE
	LNTS.POLICY_CODE = CBP.POLICY_CODE AND LNTS.BUSI_PROD_CODE in
	(${busi_prod_code})
	)
	))
	</if>
 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
 ON CBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT TP 
 ON IL.POLICY_CODE = TP.POLICY_CODE
 AND CBP.BUSI_ITEM_ID = TP.BUSI_ITEM_ID
 AND TP.PRODUCT_CODE NOT IN ('588001', '588002', '588003', '588004', '588005')
 AND substr(TP.PRODUCT_CODE,0,3)  IN ('385')    
 WHERE 1=1 AND IL.CUSTOMER_ID = #{customer_id} 
 <if test="query_busi_code==1">
      AND CBP.BUSI_PROD_CODE in (${busi_prod_code})
          <if test="policy_code !=null and policy_code != '' "><![CDATA[  AND  CM.POLICY_CODE = #{policy_code} ]]></if>                  
 </if>
<if test="query_busi_code==1">
	<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
	       AND EXISTS  (SELECT   1 FROM    
	        DEV_UW.T_UW_POLICY UP, 
	        DEV_UW.T_UW_MASTER M, 
	        DEV_UW.T_UW_BUSI_PROD UBP 
	           WHERE M.BIZ_CODE = CM.APPLY_CODE
	          AND M.UW_ID = UP.UW_ID 
	          AND UBP.UW_ID=UP.UW_ID
	           AND UBP.UW_POLICY_ID=UBP.UW_POLICY_ID
	       AND (UP.POLICY_DECISION is not null and UP.POLICY_DECISION !='10' Or UP.NON_STANDARDS = 1)
	      AND UBP.BUSI_PROD_CODE IN (${busi_prod_code})
	         union
			     SELECT  1 FROM DEV_UW.T_INSURED_LIST T,DEV_UW.T_UW_BUSI_PROD UBP, DEV_UW.T_UW_MASTER M,
     WHERE m.biz_code = CM.apply_code 
     AND T.UW_ID = M.UW_ID 
     AND T.UW_ID = UBP.UW_ID
     AND m.uw_source_type = '1'
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
			AND UBP.BUSI_PROD_CODE IN (${busi_prod_code} 
	        )
	</if>
	<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
	       AND NOT EXISTS  (SELECT   1  FROM  DEV_UW.T_UW_POLICY UP, DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_BUSI_PROD UBP 
	           WHERE M.BIZ_CODE = CM.APPLY_CODE
	          AND M.UW_ID = UP.UW_ID AND UBP.UW_ID=UP.UW_ID AND UBP.UW_POLICY_ID=UBP.UW_POLICY_ID
	     AND (UP.POLICY_DECISION is not null and  UP.POLICY_DECISION !='10' Or UP.NON_STANDARDS = 1)
	      AND UBP.BUSI_PROD_CODE IN (${busi_prod_code})
	       union
			     SELECT  1 FROM DEV_UW.T_INSURED_LIST T,DEV_UW.T_UW_BUSI_PROD UBP, DEV_UW.T_UW_MASTER M,
     WHERE m.biz_code = CM.apply_code 
     AND T.UW_ID = M.UW_ID 
     AND T.UW_ID = UBP.UW_ID
     AND m.uw_source_type = '1'
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
			AND UBP.BUSI_PROD_CODE IN (${busi_prod_code} 
	      
	      
	      )
	</if>
</if>

<if test="query_busi_code==null or query_busi_code!=1">
	<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
	  AND (
	  (SELECT COUNT(1) FROM DEV_UW.T_UW_POLICY UP 
	  WHERE UP.APPLY_CODE = CM.APPLY_CODE 
	  AND UP.UW_SOURCE_TYPE = '1' 
	  AND (UP.POLICY_DECISION IS NOT NULL AND UP.POLICY_DECISION != '10' OR UP.NON_STANDARDS = 1)  
	  ) 
	  + (
	  SELECT COUNT(1) FROM DEV_NB.T_DOCUMENT TD 
      WHERE CM.APPLY_CODE = TD.BUSS_CODE AND 
      TD.BUSS_SOURCE_CODE = '002' 
      AND TD.TEMPLATE_CODE = 'UWS_00006')
       + (SELECT COUNT(1)
                    FROM DEV_UW.T_UW_MASTER    A,
                         DEV_UW.T_INSURED_LIST B
                   WHERE A.UW_ID = B.UW_ID
                     AND B.APPLY_CODE = CM.APPLY_CODE
                     AND A.UW_SOURCE_TYPE = '1'
                     AND (B.NON_STANDARDS = '1' OR  B.NON_STANDARDS_VALUE = '1' OR B.STAND_LIFE = 2)
                     AND B.CUSTOMER_DECISION_FLAG = 1)
       +
       (SELECT COUNT(1)
                  FROM DEV_NB.T_NB_INSURED_LIST    T1,
                       DEV_NB.T_DOCUMENT           T2,
                       DEV_UW.T_UW_DOCUMENT_VERIFY T3
                 WHERE 1 = 1
                   AND T1.CUSTOMER_ID = T3.CUSTOMER_ID
                   AND T2.BUSS_CODE = T1.APPLY_CODE
                   AND T1.APPLY_CODE = CM.APPLY_CODE
                   AND T2.DOCUMENT_NO = T3.DOCUMENT_NO
                   AND T2.BUSS_ID = T3.UW_ID
                   AND T2.BUSS_SOURCE_CODE = '002'
                   AND T2.TEMPLATE_CODE = 'UWS_00006'
                    )
      ) > 0
	</if>
	<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
      AND EXISTS (
      SELECT   1  FROM  DEV_UW.T_UW_POLICY UP ,DEV_UW.T_INSURED_LIST T
          WHERE UP.APPLY_CODE = CM.APPLY_CODE
          AND UP.APPLY_CODE = T.APPLY_CODE
          AND UP.UW_SOURCE_TYPE = '1' 
          AND UP.POLICY_DECISION ='10' 
          AND (T.NON_STANDARDS IS NULL OR T.NON_STANDARDS != 1)
           AND (T.STAND_LIFE IS NULL OR T.STAND_LIFE !=2 )
          )
	</if>
</if>
 <![CDATA[
 ORDER BY VALIDATE_DATE DESC
  )WHERE rn=1
                  union all
                  ]]>
                  <![CDATA[
                  SELECT IL.POLICY_CODE, IL.CUSTOMER_ID, CM.VALIDATE_DATE,
		    CM.DECISION_CODE,
(SELECT  C.POSTPONED_MONTHS
  FROM DEV_UW.T_UW_BUSI_PROD A,
          DEV_UW.T_UW_MASTER    B,
            DEV_UW.T_UW_PRODUCT   C
       WHERE A.UW_ID = B.UW_ID
     AND B.BIZ_CODE=CM.APPLY_CODE
     AND A.DECISION_CODE = '40'
     AND B.UW_SOURCE_TYPE = '1'
    AND A.UW_ID = C.UW_ID
  AND A.UW_BUSI_ID=C.UW_BUSI_ID
       AND C.ITEM_ID = TP.ITEM_ID
 AND ROWNUM=1)  AS  POSTPONED,
		BP.PRODUCT_CATEGORY,
  CM.CHANNEL_TYPE,CM.LIABILITY_STATE,CBP.BUSI_PROD_CODE,
  BP.PRODUCT_NAME_SYS  AS BUSI_PROD_NAME_T,/*险种名称*/
  CBP.BUSI_ITEM_ID,CM.APPLY_CODE,CM.SUBMIT_CHANNEL,
  TP.CHARGE_YEAR,/*缴费年期*/
  TP.COVERAGE_PERIOD, /*保障年期类型*/
  TP.COVERAGE_YEAR, /*保障年期*/
  TP.PREM_FREQ, /*缴费频率*/
  TP.STD_PREM_AF, /*标准保费*/
  TP.EXTRA_PREM_AF, /*加费*/
  TP.TOTAL_PREM_AF, /*总保费，已缴保费的合计*/
  TP.AMOUNT AS AMOUNT_T, /*客户投保金额（元）*/
  (SELECT SUB_POLICY_CODE FROM DEV_PAS.T_CONTRACT_RELATION CR WHERE CR.SUB_POLICY_CODE=IL.POLICY_CODE AND CR.RELATION_TYPE = '1') AS SUB_POLICY_CODE,/*附加险保单号*/
  (SELECT COUNT(0) FROM DEV_PAS.T_QUESTIONAIRE_CUSTOMER DC
                 INNER JOIN DEV_PAS.T_QUESTIONAIRE_INFO QI
                    ON DC.SURVEY_QUESTION_ID = QI.SURVEY_QUESTION_ID
                 WHERE 1 = 1
                   AND (DC.APPLY_CODE = IL.APPLY_CODE or DC.POLICY_CODE = IL.POLICY_CODE)
                   AND DC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
                   AND DC.CUSTOMER_ID = #{customer_id}
                   AND ((QI.SURVEY_VERSION = '19' AND
                         QI.SURVEY_CODE NOT IN
                         ('000',
                            '001A',
                            '001B',
                            '002A',
                            '012A',
                            '014')) OR (QI.SURVEY_VERSION = '20' AND
                         QI.SURVEY_CODE NOT IN
                         ('000',
                                          '001',
                                          '002',
                                          '003',
                                          '013A',
                                          '015',
                                          '021',
                                          '022',
                                          '023',
                                          '024',
                                          '025')) OR
                         (QI.SURVEY_VERSION = '65' AND
                         QI.SURVEY_CODE NOT IN ('000', '015')) OR
                         (QI.SURVEY_VERSION = '25' AND
                         QI.SURVEY_CODE IN
                         ('004',
                            '005',
                            '006',
                            '007',
                            '008',
                            '009',
                            '010',
                            '011',
                            '012',
                            '012B',
                            '013',
                            '015',
                            '016',
                            '017',
                            '018',
                            '024')) OR
                         (QI.SURVEY_VERSION = '52' AND
                         QI.SURVEY_CODE NOT IN ('000', '023')))
                   AND SUBSTR2(DC.SURVEY_MODULE_RESULT, 0, 1) = '是'
                )  AS IS_EXCEPTION_NOTIFY_FLAG,
    (SELECT  B.UW_ID FROM DEV_UW.T_UW_MASTER B 
                    WHERE B.BIZ_CODE = CM.APPLY_CODE) AS UW_ID,
	CM.MULTI_MAINRISK_FLAG,
	(SELECT COUNT(*) FROM DEV_UW.T_UW_PHONE_MASTER PM WHERE PM.IS_REPLY = '1' AND PM.APPLY_CODE = CM.APPLY_CODE) AS PHONE_NUMBER,
    (SELECT TO_CHAR(WM_CONCAT(PRODUCT_NAME_SYS)) FROM DEV_PDS.T_BUSINESS_PRODUCT PBP
		WHERE PBP.PRODUCT_CODE_SYS IN (SELECT PABP.BUSI_PROD_CODE
     		FROM DEV_PAS.T_CONTRACT_BUSI_PROD PABP WHERE PABP.POLICY_CODE = CM.POLICY_CODE ]]>
     		
     		<if test="query_busi_code==null or query_busi_code!=1">
     			AND PABP.MASTER_BUSI_ITEM_ID IS NULL
     		</if>
     		<if test="query_busi_code==1">
     			AND PABP.BUSI_PROD_CODE in (${busi_prod_code})
     		</if>
     		<![CDATA[ )) AS ALL_PRODUCT_NAME,
            
           	(CASE WHEN IL.ANNUAL_INCOME_CEIL IS NOT NULL THEN
	          TO_CHAR(IL.ANNUAL_INCOME_CEIL)
	         ELSE
	          (SELECT SURVEY_MODULE_RESULT
	             FROM DEV_PAS.T_QUESTIONAIRE_CUSTOMER TQC
	            WHERE CM.POLICY_CODE = TQC.POLICY_CODE
	              AND TQC.SURVEY_QUESTION_ID in (833, 564, 1093)
	              AND TQC.QUESTIONAIRE_OBJECT = 2)
	       		end) AS ANNUAL_INCOME_CEIL, /*年收入*/
               CM.ORGAN_CODE, (SELECT TUP.NON_STANDARDS FROM DEV_UW.T_UW_POLICY TUP WHERE 
                  	   TUP.APPLY_CODE = IL.APPLY_CODE AND TUP.UW_SOURCE_TYPE = '1' AND ROWNUM = 1) AS NON_STANDARDS,
     		        (SELECT TIL.NON_STANDARDS
                             FROM   DEV_UW.T_INSURED_LIST TIL,DEV_UW.T_UW_POLICY TUP 
                            WHERE TUP.UW_SOURCE_TYPE = '1'
                              AND TIL.APPLY_CODE = CM.APPLY_CODE
                              and TUP.Apply_Code = CM.APPLY_CODE
                              AND TIL.UW_ID = TUP.UW_ID
                              AND TIL.CUSTOMER_ID = #{customer_id }) AS  NON_STANDARDSS,
                  	   	 (CASE
                                 WHEN (SELECT COUNT(DISTINCT TIL.list_id)
                                         FROM APP___nb__DBUSER.t_nb_insured_list    TIL
                                        WHERE TIL.ORDER_ID = 1
                                          AND TIL.APPLY_CODE = CM.APPLY_CODE
                                          AND TIL.CUSTOMER_ID = #{customer_id }) = 1 THEN
                                  ''
                                 ELSE
                                  (SELECT TO_CHAR( COUNT(1))
                                     FROM APP___UW__DBUSER.t_uw_busi_prod    M,
                                          APP___UW__DBUSER.T_BENEFIT_INSURED A,
                                          APP___UW__DBUSER.T_INSURED_LIST    TIL
                                    WHERE A.ORDER_ID = 1
                                      AND TIL.LIST_ID = A.INSURED_ID
                                      AND A.UW_ID = M.UW_ID
                                      AND TIL.APPLY_CODE = CM.APPLY_CODE
                                      AND A.BUSI_ITEM_ID = M.BUSI_ITEM_ID
                                      and m.decision_code is not null
                                      and m.decision_code <> '10'
                                      AND TIL.CUSTOMER_ID = #{customer_id })
                               END  ) UW_DECISION_CODE,
                  	   (CASE
 						  WHEN ( SELECT COUNT(1) FROM DEV_UW.T_UW_DOCUMENT_VERIFY TDV, APP___NB__DBUSER.T_DOCUMENT TD
 						    WHERE TDV.DOCUMENT_NO = TD.DOCUMENT_NO
 						      AND TD.BUSS_CODE = CM.APPLY_CODE
 						      AND TDV.UW_ID = TD.BUSS_ID
 						      AND TD.BUSS_SOURCE_CODE = '002'
 						      AND TD.TEMPLATE_CODE = 'UWS_00006'
 						      AND TDV.CUSTOMER_ID = #{customer_id }   ) = 0 THEN
 						      ( SELECT COUNT(1) FROM APP___NB__DBUSER.T_DOCUMENT TD
 						        WHERE TD.BUSS_CODE = CM.APPLY_CODE
 						          AND TD.BUSS_SOURCE_CODE = '002'
 						          AND TD.TEMPLATE_CODE = 'UWS_00006' )
 						      ELSE 1 END ) UW_06, 
                  	   (CASE
                         WHEN (
                           SELECT TIS.DATA_SAVE_NO
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS
                        WHERE TIS.BUSS_CODE = CM.APPLY_CODE
                           AND TIS.BILLCARD_CODE = 'EUM004') IS NULL
                            THEN                 
                          (SELECT COUNT(TIS.BUSS_CODE)
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS
                         WHERE TIS.BUSS_CODE = CM.APPLY_CODE
                           AND TIS.BILLCARD_CODE = 'EUM004') 
                          ELSE
                            (SELECT COUNT(TIS.BUSS_CODE)
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS ,APP___NB__DBUSER.T_IMAGE_DATA_INFO TID ,APP___NB__DBUSER.T_CUSTOMER TCC
                         WHERE TIS.BUSS_CODE = CM.APPLY_CODE
                         AND TIS.DATA_SAVE_NO = TID.DATA_SAVE_NO
                         AND TCC.CUSTOMER_NAME = TID.CUSTOMER_NAME
                         AND TCC.CUSTOMER_BIRTHDAY = TID.CUSTOMER_BIRTHDAY
                         AND TCC.CUSTOMER_GENDER = TID.CUSTOMER_GENDER
                         AND TCC.CUSTOMER_CERTI_CODE = TID.CUSTOMER_CERTI_CODE
                         AND TCC.CUSTOMER_CERT_TYPE = TID.CUSTOMER_CERT_TYPE
                           AND TID.CATEGORY_TAB_CODE LIKE 'EUM004%'
                           AND TCC.CUSTOMER_ID = #{customer_id }
                           ) 
                       END) EUM004_TOTAL,
        (SELECT COUNT(1)
         FROM DEV_UW.T_UW_TRACE T1, DEV_UW.T_UW_MASTER U
        WHERE U.BIZ_CODE = CM.APPLY_CODE AND T1.UW_ID = U.UW_ID
          AND T1.UW_EVENT_CODE = '13') AS TOTAL_1,
          (SELECT COUNT(1)
         FROM DEV_UW.T_UW_AUTO T2, DEV_UW.T_RULE_RESULT T3, DEV_UW.T_UW_MASTER U
        WHERE T2.AUTO_ID = T3.AUTO_ID AND T2.UW_ID = U.UW_ID
          AND U.BIZ_CODE = CM.APPLY_CODE) AS TOTAL_2,
          (SELECT FLOOR(MONTHS_BETWEEN(CM.APPLY_DATE, TC.CUSTOMER_BIRTHDAY) / 12) FROM DEV_PAS.T_CUSTOMER TC
                WHERE TC.CUSTOMER_ID = IL.CUSTOMER_ID) AS AGE,
          (SELECT SUM(CASE WHEN (CASE
                           WHEN TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                            0,
                                            INSTR(TC.SURVEY_MODULE_RESULT,
                                                  ',',
                                                  1) - 1)) = 0 THEN
                            22
                           ELSE
                            (CASE
                              WHEN INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) = 0 THEN
                               SUBSTR(TC.SURVEY_MODULE_RESULT,
                                      INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1)
                              ELSE
                               SUBSTR(TC.SURVEY_MODULE_RESULT,
                                      INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1,
                                      INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) - 1)
                            END) /
                            ((TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                          0,
                                          INSTR(TC.SURVEY_MODULE_RESULT,
                                                ',',
                                                1) - 1)) / 100) *
                            (TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                          0,
                                          INSTR(TC.SURVEY_MODULE_RESULT,
                                                ',',
                                                1) - 1)) / 100))
                         END) < 15
                    
                     THEN
                     1
                  
                    WHEN
                    
                     (CASE
                       WHEN TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                        0,
                                        INSTR(TC.SURVEY_MODULE_RESULT,
                                              ',',
                                              1) - 1)) = 0 THEN
                        22
                       ELSE
                        (CASE
                          WHEN INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) = 0 THEN
                           SUBSTR(TC.SURVEY_MODULE_RESULT,
                                  INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1)
                          ELSE
                           SUBSTR(TC.SURVEY_MODULE_RESULT,
                                  INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1,
                                  INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) - 1)
                        END) /
                        ((TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                      0,
                                      INSTR(TC.SURVEY_MODULE_RESULT,
                                            ',',
                                            1) - 1)) / 100) *
                        (TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
                                      0,
                                      INSTR(TC.SURVEY_MODULE_RESULT,
                                            ',',
                                            1) - 1)) / 100))
                     END) > 31
                    
                     THEN
                     1
                    ELSE
                     0
                  END)
         FROM DEV_PAS.T_QUESTIONAIRE_CUSTOMER TC,
              DEV_PAS.T_QUESTIONAIRE_INFO     I
        WHERE TC.SURVEY_QUESTION_ID = I.SURVEY_QUESTION_ID
          AND (I.SURVEY_VERSION IN
              ('65',
                '20',
                '19',
                '25',
                '52',
                '22',
                '32',
                '42') AND I.SURVEY_CODE = '000' OR
              I.SURVEY_VERSION = '102' AND
              I.SURVEY_CODE = '030')
          AND TC.APPLY_CODE = CM.APPLY_CODE
          AND TC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
          AND TC.CUSTOMER_ID = IL.CUSTOMER_ID
          AND TC.SURVEY_MODULE_RESULT IS NOT NULL) AS BMI,
           IL.EXCEPTION_HEALTH_FLAG
   FROM  ]]>
  <if test="query_busi_code==1 or query_busi_code==2">
      DEV_PAS.T_POLICY_HOLDER IL
  </if >
  <if test="query_busi_code==null or (query_busi_code!=1 and query_busi_code!=2)">
       DEV_PAS.T_INSURED_LIST IL
  </if>
 <![CDATA[ INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
    ON IL.POLICY_CODE = CM.POLICY_CODE]]>
    <include refid="NB_queryProposalListForPAS"/>
 <![CDATA[ INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
    ON CM.POLICY_CODE = CBP.POLICY_CODE]]>
    <if test="query_busi_code==null or query_busi_code!=1">
	AND CBP.MASTER_BUSI_ITEM_ID IS NULL
	AND (CBP.ORDER_ID IS NULL OR CBP.ORDER_ID = 
	(SELECT MIN(LNTS1.ORDER_ID)
                           FROM DEV_PAS.T_CONTRACT_BUSI_PROD LNTS1,
                                DEV_PAS.T_BENEFIT_INSURED    LNTS2
                          WHERE LNTS1.POLICY_ID = CBP.POLICY_ID
                            AND LNTS1.POLICY_ID = LNTS2.POLICY_ID
                            AND LNTS1.BUSI_ITEM_ID = LNTS2.BUSI_ITEM_ID
                            AND LNTS1.MASTER_BUSI_ITEM_ID IS NULL 
                            AND LNTS2.INSURED_ID = IL.LIST_ID)    
	)
    </if>
    <if test="query_busi_code==1">
	AND (CBP.ORDER_ID IS NULL OR CBP.ORDER_ID = (
	(SELECT MIN(LNTS.ORDER_ID) FROM DEV_PAS.T_CONTRACT_BUSI_PROD LNTS WHERE
	LNTS.POLICY_CODE = CBP.POLICY_CODE AND LNTS.BUSI_PROD_CODE in
	(${busi_prod_code})
	)
	))
	</if>
 INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
 ON CBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
 <if test="query_busi_code==null or (query_busi_code!=1 and query_busi_code!=2)">
 	INNER JOIN DEV_PAS.T_BENEFIT_INSURED  BI
    	ON BI.INSURED_ID = IL.LIST_ID AND IL.POLICY_ID = BI.POLICY_ID AND BI.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
 </if>
 INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT TP 
 ON IL.POLICY_CODE = TP.POLICY_CODE
 AND CBP.BUSI_ITEM_ID = TP.BUSI_ITEM_ID
 AND TP.PRODUCT_CODE NOT IN ('588001', '588002', '588003', '588004', '588005')
 AND substr(TP.PRODUCT_CODE,0,3)  NOT IN ('385') 
 WHERE 1=1 AND IL.CUSTOMER_ID = #{customer_id} 
 <if test="query_busi_code==1">
      AND CBP.BUSI_PROD_CODE in (${busi_prod_code})
          <if test="policy_code !=null and policy_code != '' "><![CDATA[  AND  CM.POLICY_CODE = #{policy_code} ]]></if>                  
 </if>
<if test="query_busi_code==1">
	<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
	       AND EXISTS  (
	       
	       SELECT   1 FROM     DEV_UW.T_UW_POLICY UP, DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_BUSI_PROD UBP 
	           WHERE M.BIZ_CODE = CM.APPLY_CODE
	          AND M.UW_ID = UP.UW_ID AND UBP.UW_ID=UP.UW_ID AND UBP.UW_POLICY_ID=UBP.UW_POLICY_ID
	      AND (UP.POLICY_DECISION is not null and UP.POLICY_DECISION !='10'  Or UP.NON_STANDARDS = 1)
	       AND UBP.BUSI_PROD_CODE IN (${busi_prod_code})
	                    UNION
     SELECT  1 FROM DEV_UW.T_INSURED_LIST T,DEV_UW.T_UW_BUSI_PROD UBP, DEV_UW.T_UW_MASTER M,
     WHERE m.biz_code = CM.apply_code 
     AND T.UW_ID = M.UW_ID 
     AND T.UW_ID = UBP.UW_ID
     AND m.uw_source_type = '1'
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
      
	        )
	</if>
	<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
	       AND NOT EXISTS  (SELECT   1  FROM  DEV_UW.T_UW_POLICY UP, DEV_UW.T_UW_MASTER M, DEV_UW.T_UW_BUSI_PROD UBP 
	           WHERE M.BIZ_CODE = CM.APPLY_CODE
	          AND M.UW_ID = UP.UW_ID AND UBP.UW_ID=UP.UW_ID AND UBP.UW_POLICY_ID=UBP.UW_POLICY_ID
	     AND (UP.policy_decision is not null and  UP.policy_decision !='10'  Or UP.NON_STANDARDS = 1)
	     AND UBP.BUSI_PROD_CODE IN (${busi_prod_code})
	                  UNION
     SELECT  1 FROM DEV_UW.T_INSURED_LIST T,DEV_UW.T_UW_BUSI_PROD UBP, DEV_UW.T_UW_MASTER M,
     WHERE m.biz_code = CM.apply_code 
     AND T.UW_ID = M.UW_ID 
     AND T.UW_ID = UBP.UW_ID
     AND m.uw_source_type = '1'
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
      
	     )
	</if>
</if>

<if test="query_busi_code==null or query_busi_code!=1">
	<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
	  AND (
	  (SELECT COUNT(1) FROM DEV_UW.T_UW_POLICY UP 
	  WHERE UP.APPLY_CODE = CM.APPLY_CODE 
	  AND UP.UW_SOURCE_TYPE = '1' 
	  AND (UP.POLICY_DECISION IS NOT NULL AND UP.POLICY_DECISION != '10' OR UP.NON_STANDARDS = 1)
	  ) 
	  + (SELECT COUNT(1) FROM DEV_NB.T_DOCUMENT TD 
      WHERE CM.APPLY_CODE = TD.BUSS_CODE 
      AND TD.BUSS_SOURCE_CODE = '002' 
      AND TD.TEMPLATE_CODE = 'UWS_00006')
       + (SELECT COUNT(1)
                    FROM DEV_UW.T_UW_MASTER    A,
                         DEV_UW.T_INSURED_LIST B
                   WHERE A.UW_ID = B.UW_ID
                     AND B.APPLY_CODE = CM.APPLY_CODE
                     AND A.UW_SOURCE_TYPE = '1'
                     AND (B.NON_STANDARDS = '1' OR  B.NON_STANDARDS_VALUE = '1' OR B.STAND_LIFE = 2)
                     AND B.CUSTOMER_DECISION_FLAG = 1)
       +
       (SELECT COUNT(1)
                  FROM DEV_NB.T_NB_INSURED_LIST    T1,
                       DEV_NB.T_DOCUMENT           T2,
                       DEV_UW.T_UW_DOCUMENT_VERIFY T3
                 WHERE 1 = 1
                   AND T1.CUSTOMER_ID = T3.CUSTOMER_ID
                   AND T2.BUSS_CODE = T1.APPLY_CODE
                   AND T1.APPLY_CODE = CM.APPLY_CODE
                   AND T2.DOCUMENT_NO = T3.DOCUMENT_NO
                   AND T2.BUSS_ID = T3.UW_ID
                   AND T2.BUSS_SOURCE_CODE = '002'
                   AND T2.TEMPLATE_CODE = 'UWS_00006'
                    )
      ) > 0
	</if>
	<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
      AND EXISTS (
      SELECT   1  FROM  DEV_UW.T_UW_POLICY UP,APP___UW__DBUSER.T_INSURED_LIST T
          WHERE UP.APPLY_CODE = CM.APPLY_CODE 
          AND UP.APPLY_CODE = T.APPLY_CODE
          AND UP.UW_SOURCE_TYPE = '1' 
          AND UP.POLICY_DECISION ='10' 
          AND (T.NON_STANDARDS IS NULL OR T.NON_STANDARDS != 1)
          AND (T.STAND_LIFE IS NULL OR T.STAND_LIFE !=2 )
          
          )
	</if>
</if>
 <![CDATA[
 )B WHERE 1=1 ]]>
 		<if test="query_busi_code==null or query_busi_code!=1">
         	AND (PRODUCT_CATEGORY = '10001' OR SUB_POLICY_CODE IS NOT NULL)
     	</if>
     	
     	<if test="appntoldimpartInfo != '' and appntoldimpartInfo == 0">
				<![CDATA[AND (B.IS_EXCEPTION_NOTIFY_FLAG + (CASE WHEN ((B.AGE >= 18 AND B.BMI > 0) OR B.EXCEPTION_HEALTH_FLAG >0) THEN
				1
				ELSE
				0
				END)) = 0]]>
	      </if>
	      <if test="appntoldimpartInfo != '' and appntoldimpartInfo == 1">
				<![CDATA[AND (B.IS_EXCEPTION_NOTIFY_FLAG + (CASE  WHEN ((B.AGE >= 18 AND B.BMI > 0) OR B.EXCEPTION_HEALTH_FLAG >0) THEN
				1
				ELSE
				0
				END)) > 0]]>
	      </if>
     	ORDER BY VALIDATE_DATE DESC
	</select>
	
	
	
	<select id="findApplyPolicyInfoByInsuredCustomerIdTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ 
    	SELECT COUNT(1)
	  	FROM
	  	(
	  	]]>
	  	<include refid="findApplyPolicyInfoByInsuredCustomerIdSql" />
	  	 <![CDATA[ 
	  	) D
	  	]]>
	</select>
	
    <sql id="findApplyPolicyInfoByInsuredCustomerIdSql">
    	 <![CDATA[ 
    	 SELECT B.*,
                       (CASE
                         WHEN B.SUB_APPLY_CODE IS NOT NULL THEN
                          ''
                         ELSE
                          B.MASTER_NAME_T
                       END) AS MASTER_NAME, /*主险名称*/
                       (CASE
                         WHEN B.SUB_APPLY_CODE IS NOT NULL THEN
                          null
                         ELSE
                          B.AMOUNT_T
                       END) AS AMOUNT, /*主险保额*/
                       (CASE
                         WHEN B.SUB_APPLY_CODE IS NOT NULL THEN
                          B.MASTER_NAME_T
                         ELSE
                          ''
                       END) AS SUB_BUSI_PRODUCT_NAME, /*附加险名称*/
                       (CASE
                         WHEN B.SUB_APPLY_CODE IS NOT NULL THEN
                          B.AMOUNT_T
                         ELSE
                          null
                       END) AS SUB_POLICY_AMOUNT, /*附加险保额*/
                       (CASE
                         WHEN ((B.AGE >= 18 AND (B.BMI < 15 OR B.BMI > 31)) OR B.EXCEPTION_HEALTH_FLAG >0) THEN
                          1 + IS_EXCEPTION_NOTIFY_FLAG
                         ELSE
                          0 + IS_EXCEPTION_NOTIFY_FLAG
                       END) IS_EXCEPTION_NOTIFY_FLAG2
                  from (   ]]>
                  <![CDATA[ select APPLY_CODE,
               CUSTOMER_ID,
               APPLY_DATE,
               DECISION_CODE,
               SUBMIT_CHANNEL,
               LIABILITY_STATE,
               PROPOSAL_STATUS, /**投保单状态 */
               BUSI_ITEM_ID,
               PROPOSAL_NAME,
               PRODUCT_CATEGORY,
               MASTER_NAME_T,
               CHARGE_YEAR,
               COVERAGE_PERIOD,
               COVERAGE_YEAR,
               PREM_FREQ,
               STD_PREM_AF,
               EXTRA_PREM_AF,
               TOTAL_PREM_AF,
               AMOUNT_T, /*客户投保金额（元）*/
               BENEFIT_LEVEL, /*投保档次*/
               UNIT, /*客户投保份数*/
               SUB_APPLY_CODE, /*附加险投保单号*/
               PHONE_NUMBER,
               IS_EXCEPTION_NOTIFY_FLAG,
               UW_ID,
               MULTI_MAINRISK_FLAG,
               ALL_PRODUCT_NAME,
               ANNUAL_INCOME_CEIL, /*年收入*/
               ORGAN_CODE,
               NON_STANDARDS,
               NON_STANDARDSS,
               UW_DECISION_CODE,
               UW_06,
               EUM004_TOTAL,
               NON_STANDARDS_VALUE,
               TOTAL_1,
               TOTAL_2,
               AGE, 
               BMI,
               EXCEPTION_HEALTH_FLAG, BOOKING_STATE
                  from (
                  SELECT
                         row_number() over(partition by NCP.APPLY_CODE order by NCP.AMOUNT desc) rn ,
                         NCP.APPLY_CODE,
                         NPH.CUSTOMER_ID,
                         NCM.APPLY_DATE,
                         NCM.DECISION_CODE,
                         NCM.SUBMIT_CHANNEL,
                         NCM.LIABILITY_STATE,
                         NCM.PROPOSAL_STATUS, /**投保单状态 */
                         NBP.BUSI_ITEM_ID,
                         C.CUSTOMER_NAME AS PROPOSAL_NAME,
                         BP.PRODUCT_CATEGORY,
                         BP.PRODUCT_NAME_SYS AS MASTER_NAME_T,
                         NCP.CHARGE_YEAR,
                         NCP.COVERAGE_PERIOD,
                         NCP.COVERAGE_YEAR,
                         NCP.PREM_FREQ,
                         NCP.STD_PREM_AF,
                         NCP.EXTRA_PREM_AF,
                         NCP.TOTAL_PREM_AF,
                         NCP.AMOUNT AS AMOUNT_T, /*客户投保金额（元）*/
                         NCP.BENEFIT_LEVEL, /*投保档次*/
                         NCP.UNIT, /*客户投保份数*/
                       (SELECT CR.SUB_APPLY_CODE
                              FROM DEV_NB.T_NB_CONTRACT_RELATION CR
                             WHERE CR.SUB_APPLY_CODE = NCM.APPLY_CODE AND CR.RELATION_TYPE = '1') AS SUB_APPLY_CODE, /*附加险投保单号*/
                       (SELECT COUNT(*) FROM DEV_UW.T_UW_PHONE_MASTER PM WHERE PM.IS_REPLY = '1' AND PM.APPLY_CODE = NCM.APPLY_CODE) AS PHONE_NUMBER,
                   (SELECT COUNT(0)
					  FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER DC
					 INNER JOIN DEV_NB.T_QUESTIONAIRE_INFO QI
					    ON DC.SURVEY_QUESTION_ID = QI.SURVEY_QUESTION_ID
					 WHERE 1 = 1
					   AND (DC.APPLY_CODE = NCM.APPLY_CODE or DC.POLICY_CODE = NCM.POLICY_CODE)					   
					   AND DC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
					   AND DC.CUSTOMER_ID = #{customer_id}
					   AND ((QI.SURVEY_VERSION = '19' AND
                                       QI.SURVEY_CODE NOT IN
                                       ('000',
                                          '001A',
                                          '001B',
                                          '002A',
                                          '012A',
                                          '014')) OR (QI.SURVEY_VERSION = '20' AND
                                       QI.SURVEY_CODE NOT IN
                                       ('000',
                                                        '001',
                                                        '002',
                                                        '003',
                                                        '013A',
                                                        '015',
                                                        '021',
                                                        '022',
                                                        '023',
                                                        '024',
                                                        '025')) OR
                                       (QI.SURVEY_VERSION = '65' AND
                                       QI.SURVEY_CODE NOT IN ('000', '015')) OR
                                       (QI.SURVEY_VERSION = '25' AND
                                       QI.SURVEY_CODE IN
                                       ('004',
                                          '005',
                                          '006',
                                          '007',
                                          '008',
                                          '009',
                                          '010',
                                          '011',
                                          '012',
                                          '012B',
                                          '013',
                                          '015',
                                          '016',
                                          '017',
                                          '018',
                                          '024')) OR
                                       (QI.SURVEY_VERSION = '52' AND
                                       QI.SURVEY_CODE NOT IN ('000', '023')))
					   AND SUBSTR2(DC.SURVEY_MODULE_RESULT, 0, 1) = '是') AS IS_EXCEPTION_NOTIFY_FLAG,
					   (SELECT  B.UW_ID FROM DEV_UW.T_UW_MASTER B 
                                WHERE B.BIZ_CODE = NCP.APPLY_CODE) AS UW_ID,
                        NCM.MULTI_MAINRISK_FLAG,
                       (SELECT TO_CHAR(WM_CONCAT(PRODUCT_NAME_SYS)) FROM DEV_PDS.T_BUSINESS_PRODUCT PBP, DEV_NB.T_NB_CONTRACT_BUSI_PROD PABP
                 			WHERE PBP.BUSINESS_PRD_ID = PABP.BUSI_PRD_ID AND PABP.APPLY_CODE = NCM.APPLY_CODE]]>
                  		<if test="query_busi_code==null or query_busi_code!=1">
							AND PABP.MASTER_BUSI_ITEM_ID IS NULL
                  		</if>
                  		<if test="query_busi_code==1">
                  			AND PABP.PRODUCT_CODE in  (${busi_prod_code})
                  		</if>
                  		<![CDATA[) AS ALL_PRODUCT_NAME,
                  		
                  		(SELECT CASE 
                  			WHEN NIL.ANNUAL_INCOME_CEIL IS NULL 
                  					THEN (SELECT SURVEY_MODULE_RESULT FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER TQC
		                         			WHERE NCM.APPLY_CODE = TQC.APPLY_CODE 
		                            		AND TQC.SURVEY_QUESTION_ID in(833,564,1093) AND TQC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
		                            		AND TQC.CUSTOMER_ID = #{customer_id} )
		                    ELSE TO_CHAR(NIL.ANNUAL_INCOME_CEIL)
                  			END AS ANNUAL_INCOME_CEIL
                  		FROM DEV_NB.T_NB_INSURED_LIST NIL WHERE NCM.APPLY_CODE = NIL.APPLY_CODE AND NIL.CUSTOMER_ID = #{customer_id}) AS ANNUAL_INCOME_CEIL,/*年收入*/
                  	   NCM.ORGAN_CODE, (SELECT TUP.NON_STANDARDS FROM DEV_UW.T_UW_POLICY TUP WHERE 
                  	   TUP.APPLY_CODE = NCM.APPLY_CODE AND TUP.UW_SOURCE_TYPE = '1' AND ROWNUM = 1) AS NON_STANDARDS,
              
              (SELECT TIL.NON_STANDARDS
                             FROM   DEV_UW.T_INSURED_LIST TIL,DEV_UW.T_UW_POLICY TUP 
                            WHERE TUP.UW_SOURCE_TYPE = '1'
                              AND TIL.APPLY_CODE = NCM.APPLY_CODE
                              and TUP.Apply_Code = NCM.APPLY_CODE
                              AND TIL.UW_ID = TUP.UW_ID
                              AND TIL.CUSTOMER_ID = #{customer_id }) AS  NON_STANDARDSS,
                  	   	 (CASE
                                 WHEN (SELECT COUNT(DISTINCT TIL.list_id)
                                         FROM APP___nb__DBUSER.t_nb_insured_list    TIL
                                        WHERE TIL.ORDER_ID = 1
                                          AND TIL.APPLY_CODE = NCM.APPLY_CODE
                                          AND TIL.CUSTOMER_ID = #{customer_id }) = 1 THEN
                                  ''
                                 ELSE
                                  (SELECT TO_CHAR( COUNT(1))
                                     FROM APP___UW__DBUSER.t_uw_busi_prod    M,
                                          APP___UW__DBUSER.T_BENEFIT_INSURED A,
                                          APP___UW__DBUSER.T_INSURED_LIST    TIL
                                    WHERE A.ORDER_ID = 1
                                      AND TIL.LIST_ID = A.INSURED_ID
                                      AND A.UW_ID = M.UW_ID
                                      AND TIL.APPLY_CODE = NCM.APPLY_CODE
                                      AND A.BUSI_ITEM_ID = M.BUSI_ITEM_ID
                                      and m.decision_code is not null
                                      and m.decision_code <> '10'
                                      AND TIL.CUSTOMER_ID = #{customer_id })
                               END  ) UW_DECISION_CODE,
                      (CASE
 						  WHEN ( SELECT COUNT(1) FROM DEV_UW.T_UW_DOCUMENT_VERIFY TDV, APP___NB__DBUSER.T_DOCUMENT TD
 						    WHERE TDV.DOCUMENT_NO = TD.DOCUMENT_NO
 						      AND TD.BUSS_CODE = NCM.APPLY_CODE
 						      AND TDV.UW_ID = TD.BUSS_ID
 						      AND TD.BUSS_SOURCE_CODE = '002'
 						      AND TD.TEMPLATE_CODE = 'UWS_00006'
 						      AND TDV.CUSTOMER_ID = #{customer_id }   ) = 0 THEN
 						      ( SELECT COUNT(1) FROM APP___NB__DBUSER.T_DOCUMENT TD
 						        WHERE TD.BUSS_CODE = NCM.APPLY_CODE
 						          AND TD.BUSS_SOURCE_CODE = '002'
 						          AND TD.TEMPLATE_CODE = 'UWS_00006' )
 						      ELSE 1 END ) UW_06,         
                  	  (CASE
                         WHEN (
                           SELECT TIS.DATA_SAVE_NO
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS
                        WHERE TIS.BUSS_CODE = NCM.APPLY_CODE
                           AND TIS.BILLCARD_CODE = 'EUM004') IS NULL
                            THEN                 
                          (SELECT COUNT(TIS.BUSS_CODE)
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS
                         WHERE TIS.BUSS_CODE = NCM.APPLY_CODE
                           AND TIS.BILLCARD_CODE = 'EUM004') 
                          ELSE
                            (SELECT COUNT(TIS.BUSS_CODE)
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS ,APP___NB__DBUSER.T_IMAGE_DATA_INFO TID ,APP___NB__DBUSER.T_CUSTOMER TCC
                         WHERE TIS.BUSS_CODE = NCM.APPLY_CODE
                         AND TIS.DATA_SAVE_NO = TID.DATA_SAVE_NO
                         AND TCC.CUSTOMER_NAME = TID.CUSTOMER_NAME
                         AND TCC.CUSTOMER_BIRTHDAY = TID.CUSTOMER_BIRTHDAY
                         AND TCC.CUSTOMER_GENDER = TID.CUSTOMER_GENDER
                         AND TCC.CUSTOMER_CERTI_CODE = TID.CUSTOMER_CERTI_CODE
                         AND TCC.CUSTOMER_CERT_TYPE = TID.CUSTOMER_CERT_TYPE
                           AND TID.CATEGORY_TAB_CODE LIKE 'EUM004%'
                           AND TCC.CUSTOMER_ID = #{customer_id }
                           ) 
                       END) EUM004_TOTAL,
                            (SELECT TO_CHAR(A.NON_STANDARDS_VALUE)  FROM DEV_UW.T_INSURED_LIST A,DEV_UW.T_UW_POLICY TUP
                                WHERE A.APPLY_CODE = NCM.APPLY_CODE  
                                AND A.APPLY_CODE = TUP.APPLY_CODE
                                AND TUP.UW_SOURCE_TYPE = '1'
                                AND ROWNUM = 1) NON_STANDARDS_VALUE,
                       (SELECT COUNT(1)
	                 FROM DEV_UW.T_UW_TRACE T1, DEV_UW.T_UW_MASTER U
	                WHERE U.BIZ_CODE = NCM.APPLY_CODE AND T1.UW_ID = U.UW_ID
	                  AND T1.UW_EVENT_CODE = '13') AS TOTAL_1,
	                  (SELECT COUNT(1)
	                 FROM DEV_UW.T_UW_AUTO T2, DEV_UW.T_RULE_RESULT T3, DEV_UW.T_UW_MASTER U
	                WHERE T2.AUTO_ID = T3.AUTO_ID AND T2.UW_ID = U.UW_ID
	                  AND U.BIZ_CODE = NCM.APPLY_CODE) AS TOTAL_2,
                  	(SELECT FLOOR(MONTHS_BETWEEN(NCM.APPLY_DATE, TC.CUSTOMER_BIRTHDAY) / 12)
                      FROM DEV_NB.T_CUSTOMER TC WHERE TC.CUSTOMER_ID = #{customer_id}) AS AGE,
                   	(SELECT CASE WHEN TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
				                          0,
				                          INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) - 1)) = 0 THEN
				          22
				         ELSE
				          (CASE
				            WHEN INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) = 0 THEN
				             SUBSTR(TC.SURVEY_MODULE_RESULT,
				                    INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1)
				            ELSE
				             SUBSTR(TC.SURVEY_MODULE_RESULT,
				                    INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1,
				                    INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) - 1)
				          END) /
				          (TRIM((SUBSTR(TC.SURVEY_MODULE_RESULT,
				                        0,
				                        INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) - 1)) / 100) *
				          (TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
				                        0,
				                        INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) - 1)) / 100))
				       END
                      FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER TC,
                           DEV_NB.T_QUESTIONAIRE_INFO     I
                     WHERE TC.SURVEY_QUESTION_ID = I.SURVEY_QUESTION_ID
                       AND (I.SURVEY_VERSION IN
                           ('65', '20', '19', '25', '52', '22', '32', '42') AND I.SURVEY_CODE = '000' 
                           OR I.SURVEY_VERSION = '102' AND I.SURVEY_CODE = '030')
                       AND TC.APPLY_CODE = NCM.APPLY_CODE
                       AND TC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
                       AND TC.CUSTOMER_ID = #{customer_id}
                       AND TC.SURVEY_MODULE_RESULT IS NOT NULL
                       AND ROWNUM = 1) AS BMI,
                       A.EXCEPTION_HEALTH_FLAG,
					   (SELECT NBI.BOOKING_STATE FROM DEV_NB.T_NB_ADVANCE_BOOKING_INFO NBI WHERE NBI.BOOKING_CODE = A.APPLY_CODE) AS BOOKING_STATE
		          FROM DEV_NB.T_NB_CONTRACT_MASTER NCM
		         INNER JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD NBP
		            ON NCM.APPLY_CODE = NBP.APPLY_CODE  
		            AND NCM.LIABILITY_STATE IN ('4', '0', '2', '3')]]>
           			<if test="query_busi_code==null or query_busi_code!=1">
	           			AND NBP.MASTER_BUSI_ITEM_ID IS NULL
	           			AND (NBP.ORDER_ID IS NULL OR NBP.ORDER_ID = '1')
           			</if>
		           <if test="query_busi_code==1">
		            AND  NBP.PRODUCT_CODE in  (${busi_prod_code})
		            AND (NBP.ORDER_ID IS NULL OR NBP.ORDER_ID = (
		            	(SELECT MIN(LNTS.ORDER_ID) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD LNTS WHERE 
    						LNTS.APPLY_CODE = NBP.APPLY_CODE AND LNTS.PRODUCT_CODE IN (${busi_prod_code})
    					)
		            ))
		           </if>
		             <include refid="NB_queryProposalListForUw"/>
		       <![CDATA[  INNER JOIN DEV_NB.T_NB_CONTRACT_PRODUCT NCP
		            ON NCP.BUSI_ITEM_ID = NBP.BUSI_ITEM_ID
		         INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		            ON NBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
		         INNER JOIN DEV_NB.T_NB_POLICY_HOLDER NPH
		            ON NCP.APPLY_CODE = NPH.APPLY_CODE]]>
		         <if test="query_busi_code == 1 or query_busi_code == 2">
		        	<if test="customer_id !=null and customer_id != '' "><![CDATA[ AND NPH.CUSTOMER_ID = #{customer_id} ]]></if>
		         </if>
		         <![CDATA[INNER JOIN DEV_NB.T_CUSTOMER C
		            ON NPH.CUSTOMER_ID = C.CUSTOMER_ID]]>
		            
		         <if test="query_busi_code != 1 and query_busi_code != 2">
			         <![CDATA[INNER JOIN DEV_NB.T_NB_INSURED_LIST A
	            		ON A.APPLY_CODE = NCM.APPLY_CODE]]>
	           		 <if test="customer_id !=null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
           		 </if>
           		 <if test="query_busi_code == 1 or query_busi_code == 2">
			         <![CDATA[INNER JOIN DEV_NB.T_NB_POLICY_HOLDER  A
	            		ON A.APPLY_CODE = NCM.APPLY_CODE]]>
	           		 <if test="customer_id !=null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
           		 </if>
           		
		         <![CDATA[WHERE  1=1]]>
		         <if test="query_busi_code==1">AND  BP.PRODUCT_CATEGORY = '10002' </if>
		<if test="query_busi_code==1">	 
		   <if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
			      <![CDATA[ and exists  (
			      select   1 from  DEV_UW.T_UW_POLICY UP, 
			      dev_uw.t_uw_master m,DEV_UW.T_UW_BUSI_PROD UBP 
			      where m.biz_code = NCM.apply_code
			      and m.uw_id = UP.uw_id 
			      AND UBP.UW_ID=UP.UW_ID
			      AND UBP.UW_POLICY_ID=UBP.UW_POLICY_ID
			      and (UP.policy_decision is not null and UP.policy_decision !='10' Or UP.NON_STANDARDS = 1）
			      AND UBP.BUSI_PROD_CODE IN (${busi_prod_code})
			     union
			     SELECT  1 FROM APP___UW__DBUSER.T_INSURED_LIST T,DEV_UW.T_UW_BUSI_PROD UBP, DEV_UW.T_UW_MASTER M,
     WHERE m.biz_code = NCM.apply_code 
     AND T.UW_ID = M.UW_ID 
     AND T.UW_ID = UBP.UW_ID
     AND m.uw_source_type = '1'
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
			AND UBP.BUSI_PROD_CODE IN (${busi_prod_code} 
			     
			        )]]>
			</if>
			 <if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
			      <![CDATA[ and not exists  (
			      select   1  from   DEV_UW.T_UW_POLICY UP, 
			      dev_uw.t_uw_master m, DEV_UW.T_UW_BUSI_PROD UBP 
			      where m.biz_code = NCM.apply_code 
			          and m.uw_id = UP.uw_id 
			          AND UBP.UW_ID=UP.UW_ID 
			          AND UBP.UW_POLICY_ID=UBP.UW_POLICY_ID
			          and (UP.policy_decision is not null  and  UP.policy_decision !='10' Or UP.NON_STANDARDS = 1)
			          AND UBP.BUSI_PROD_CODE IN (${busi_prod_code})
			          union
			     SELECT  1 FROM APP___UW__DBUSER.T_INSURED_LIST T,DEV_UW.T_UW_BUSI_PROD UBP, DEV_UW.T_UW_MASTER M,
     WHERE m.biz_code = NCM.apply_code 
     AND T.UW_ID = M.UW_ID 
     AND T.UW_ID = UBP.UW_ID
     AND m.uw_source_type = '1'
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
			AND UBP.BUSI_PROD_CODE IN (${busi_prod_code} 
			       )]]>
			</if>
		 </if>
		 <if test="query_busi_code==null or query_busi_code!=1">
		  <if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
	      		<![CDATA[
                         
					AND ((SELECT COUNT(1) FROM DEV_UW.T_UW_POLICY UP WHERE UP.APPLY_CODE = NCM.APPLY_CODE AND UP.UW_SOURCE_TYPE = '1'
						 AND (UP.POLICY_DECISION IS NOT NULL AND UP.POLICY_DECISION != '10' OR UP.NON_STANDARDS = 1)) 
						 + (SELECT COUNT(1) FROM DEV_NB.T_DOCUMENT TD 
				    WHERE NCM.APPLY_CODE = TD.BUSS_CODE AND TD.BUSS_SOURCE_CODE = '002' AND TD.TEMPLATE_CODE = 'UWS_00006')
				     + (SELECT COUNT(1)
                    FROM APP___UW__DBUSER.T_UW_MASTER    A,
                         APP___UW__DBUSER.T_INSURED_LIST B
                   WHERE A.UW_ID = B.UW_ID
                     AND B.APPLY_CODE = NCM.APPLY_CODE
                     AND A.UW_SOURCE_TYPE = '1'
                     AND (B.NON_STANDARDS = '1' OR  B.NON_STANDARDS_VALUE = '1' OR B.STAND_LIFE = 2)
                     AND B.CUSTOMER_DECISION_FLAG = 1)
       +
       (SELECT COUNT(1)
                  FROM DEV_NB.T_NB_INSURED_LIST    T1,
                       DEV_NB.T_DOCUMENT           T2,
                       DEV_UW.T_UW_DOCUMENT_VERIFY T3
                 WHERE 1 = 1
                   AND T1.CUSTOMER_ID = T3.CUSTOMER_ID
                   AND T2.BUSS_CODE = T1.APPLY_CODE
                   AND T1.APPLY_CODE = NCM.APPLY_CODE
                   AND T2.DOCUMENT_NO = T3.DOCUMENT_NO
                   AND T2.BUSS_ID = T3.UW_ID
                   AND T2.BUSS_SOURCE_CODE = '002'
                   AND T2.TEMPLATE_CODE = 'UWS_00006'
                    )
				    ) > 0]]>
                         
				</if>
			
				<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
			      <![CDATA[ 
		           	  	  AND(
			      (EXISTS 
			      (SELECT   1  FROM  DEV_UW.T_UW_POLICY UP ,DEV_UW.T_INSURED_LIST T
		           WHERE UP.APPLY_CODE = NCM.APPLY_CODE 
		            AND UP.APPLY_CODE = T.APPLY_CODE
		            AND UP.UW_SOURCE_TYPE = '1' 
		            AND ( UP.POLICY_DECISION ='10' or UP.POLICY_DECISION is NULL)
		           	AND (T.NON_STANDARDS IS NULL OR T.NON_STANDARDS != 1)
		           	AND (T.STAND_LIFE IS NULL OR T.STAND_LIFE != 2 )
		           	) )
		           	OR(
		           	(select count(1) from dev_uw.t_uw_policy up where UP.APPLY_CODE = NCM.APPLY_CODE
                                  AND UP.UW_SOURCE_TYPE = '1') = 0))
		           	
		           	]]>
				</if>
		 </if>
   		  <if test="query_busi_code==1">
	        <if test="apply_code !=null and apply_code != '' "><![CDATA[ AND NCM.apply_code = #{apply_code} ]]></if>                  
	      </if>
	     <![CDATA[ AND substr(NCP.PRODUCT_CODE,0,3)  IN ('385')
	               )WHERE rn=1 union all  ]]>
                  <![CDATA[ SELECT  NCP.APPLY_CODE,
		               NPH.CUSTOMER_ID,
		               NCM.APPLY_DATE,
		              NCM.DECISION_CODE,
		               NCM.SUBMIT_CHANNEL,
		               NCM.LIABILITY_STATE,
		               NCM.PROPOSAL_STATUS,/**投保单状态 */
		               NBP.BUSI_ITEM_ID,
		               C.CUSTOMER_NAME     AS PROPOSAL_NAME,
		               BP.PRODUCT_CATEGORY,
		               BP.PRODUCT_NAME_SYS AS MASTER_NAME_T,
		               NCP.CHARGE_YEAR,
		               NCP.COVERAGE_PERIOD,
		               NCP.COVERAGE_YEAR,
		               NCP.PREM_FREQ,
		               NCP.STD_PREM_AF,
		               NCP.EXTRA_PREM_AF,
		               NCP.TOTAL_PREM_AF,
		               NCP.AMOUNT AS AMOUNT_T, /*客户投保金额（元）*/
               		   NCP.BENEFIT_LEVEL,/*投保档次*/
                       NCP.UNIT,/*客户投保份数*/
                       (SELECT CR.SUB_APPLY_CODE
                              FROM DEV_NB.T_NB_CONTRACT_RELATION CR
                             WHERE CR.SUB_APPLY_CODE = NCM.APPLY_CODE AND CR.RELATION_TYPE = '1') AS SUB_APPLY_CODE, /*附加险投保单号*/
                       (SELECT COUNT(*) FROM DEV_UW.T_UW_PHONE_MASTER PM WHERE PM.IS_REPLY = '1' AND PM.APPLY_CODE = NCM.APPLY_CODE) AS PHONE_NUMBER,
                   (SELECT COUNT(0)
					  FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER DC
					 INNER JOIN DEV_NB.T_QUESTIONAIRE_INFO QI
					    ON DC.SURVEY_QUESTION_ID = QI.SURVEY_QUESTION_ID
					 WHERE 1 = 1
					   AND (DC.APPLY_CODE = NCM.APPLY_CODE or DC.POLICY_CODE = NCM.POLICY_CODE)
					   AND DC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
					   AND DC.CUSTOMER_ID = #{customer_id}
					   AND ((QI.SURVEY_VERSION = '19' AND
                                       QI.SURVEY_CODE NOT IN
                                       ('000',
                                          '001A',
                                          '001B',
                                          '002A',
                                          '012A',
                                          '014')) OR (QI.SURVEY_VERSION = '20' AND
                                       QI.SURVEY_CODE NOT IN
                                       ('000',
                                                        '001',
                                                        '002',
                                                        '003',
                                                        '013A',
                                                        '015',
                                                        '021',
                                                        '022',
                                                        '023',
                                                        '024',
                                                        '025')) OR
                                       (QI.SURVEY_VERSION = '65' AND
                                       QI.SURVEY_CODE NOT IN ('000', '015')) OR
                                       (QI.SURVEY_VERSION = '25' AND
                                       QI.SURVEY_CODE IN
                                       ('004',
                                          '005',
                                          '006',
                                          '007',
                                          '008',
                                          '009',
                                          '010',
                                          '011',
                                          '012',
                                          '012B',
                                          '013',
                                          '015',
                                          '016',
                                          '017',
                                          '018',
                                          '024')) OR
                                       (QI.SURVEY_VERSION = '52' AND
                                       QI.SURVEY_CODE NOT IN ('000', '023')))
					   AND SUBSTR2(DC.SURVEY_MODULE_RESULT, 0, 1) = '是') AS IS_EXCEPTION_NOTIFY_FLAG,
					   (SELECT  B.UW_ID FROM DEV_UW.T_UW_MASTER B 
                                WHERE B.BIZ_CODE = NCP.APPLY_CODE) AS UW_ID,
                        NCM.MULTI_MAINRISK_FLAG,
                       (SELECT TO_CHAR(WM_CONCAT(PRODUCT_NAME_SYS)) FROM DEV_PDS.T_BUSINESS_PRODUCT PBP, DEV_NB.T_NB_CONTRACT_BUSI_PROD PABP
                 			WHERE PBP.BUSINESS_PRD_ID = PABP.BUSI_PRD_ID AND PABP.APPLY_CODE = NCM.APPLY_CODE]]>
                  		<if test="query_busi_code==null or query_busi_code!=1">
							AND PABP.MASTER_BUSI_ITEM_ID IS NULL
                  		</if>
                  		<if test="query_busi_code==1">
                  			AND PABP.PRODUCT_CODE in  (${busi_prod_code})
                  		</if>
                  		<![CDATA[ ) AS ALL_PRODUCT_NAME,
                  		
                  		(SELECT CASE 
                  			WHEN NIL.ANNUAL_INCOME_CEIL IS NULL 
                  					THEN (SELECT SURVEY_MODULE_RESULT FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER TQC
		                         			WHERE NCM.APPLY_CODE = TQC.APPLY_CODE 
		                            		AND TQC.SURVEY_QUESTION_ID in(833,564,1093) AND TQC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
		                            		AND TQC.CUSTOMER_ID = #{customer_id})
		                    ELSE TO_CHAR(NIL.ANNUAL_INCOME_CEIL)
                  			END AS ANNUAL_INCOME_CEIL
                  		FROM DEV_NB.T_NB_INSURED_LIST NIL WHERE NCM.APPLY_CODE = NIL.APPLY_CODE  AND NIL.CUSTOMER_ID = #{customer_id}) AS ANNUAL_INCOME_CEIL,/*年收入*/
                  	   NCM.ORGAN_CODE, (SELECT TUP.NON_STANDARDS FROM DEV_UW.T_UW_POLICY TUP WHERE 
                  	   TUP.APPLY_CODE = NCM.APPLY_CODE AND TUP.UW_SOURCE_TYPE = '1' AND ROWNUM = 1) AS NON_STANDARDS,
                  	    (SELECT TIL.NON_STANDARDS
                             FROM   DEV_UW.T_INSURED_LIST TIL,DEV_UW.T_UW_POLICY TUP 
                            WHERE TUP.UW_SOURCE_TYPE = '1'
                              AND TIL.APPLY_CODE = NCM.APPLY_CODE
                              and TUP.Apply_Code = NCM.APPLY_CODE
                              AND TIL.UW_ID = TUP.UW_ID
                              AND TIL.CUSTOMER_ID = #{customer_id }) AS  NON_STANDARDSS,
                  	   	 (CASE
                                 WHEN (SELECT COUNT(DISTINCT TIL.list_id)
                                         FROM APP___nb__DBUSER.t_nb_insured_list    TIL
                                        WHERE TIL.ORDER_ID = 1
                                          AND TIL.APPLY_CODE = NCM.APPLY_CODE
                                          AND TIL.CUSTOMER_ID = #{customer_id }) = 1 THEN
                                  ''
                                 ELSE
                                  (SELECT TO_CHAR( COUNT(1))
                                     FROM APP___UW__DBUSER.t_uw_busi_prod    M,
                                          APP___UW__DBUSER.T_BENEFIT_INSURED A,
                                          APP___UW__DBUSER.T_INSURED_LIST    TIL
                                    WHERE A.ORDER_ID = 1
                                      AND TIL.LIST_ID = A.INSURED_ID
                                      AND A.UW_ID = M.UW_ID
                                      AND TIL.APPLY_CODE = NCM.APPLY_CODE
                                      AND A.BUSI_ITEM_ID = M.BUSI_ITEM_ID
                                      and m.decision_code is not null
                                      and m.decision_code <> '10'
                                      AND TIL.CUSTOMER_ID = #{customer_id })
                               END  ) UW_DECISION_CODE,
                  	   (CASE
 						  WHEN ( SELECT COUNT(1) FROM DEV_UW.T_UW_DOCUMENT_VERIFY TDV, APP___NB__DBUSER.T_DOCUMENT TD
 						    WHERE TDV.DOCUMENT_NO = TD.DOCUMENT_NO
 						      AND TD.BUSS_CODE = NCM.APPLY_CODE
 						      AND TDV.UW_ID = TD.BUSS_ID
 						      AND TD.BUSS_SOURCE_CODE = '002'
 						      AND TD.TEMPLATE_CODE = 'UWS_00006'
 						      AND TDV.CUSTOMER_ID = #{customer_id }   ) = 0 THEN
 						      ( SELECT COUNT(1) FROM APP___NB__DBUSER.T_DOCUMENT TD
 						        WHERE TD.BUSS_CODE = NCM.APPLY_CODE
 						          AND TD.BUSS_SOURCE_CODE = '002'
 						          AND TD.TEMPLATE_CODE = 'UWS_00006' )
 						      ELSE 1 END ) UW_06, 
                  	   (CASE
                         WHEN (
                           SELECT TIS.DATA_SAVE_NO
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS
                        WHERE TIS.BUSS_CODE = NCM.APPLY_CODE
                           AND TIS.BILLCARD_CODE = 'EUM004') IS NULL
                            THEN                 
                          (SELECT COUNT(TIS.BUSS_CODE)
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS
                         WHERE TIS.BUSS_CODE = NCM.APPLY_CODE
                           AND TIS.BILLCARD_CODE = 'EUM004') 
                          ELSE
                            (SELECT COUNT(TIS.BUSS_CODE)
                          FROM APP___NB__DBUSER.T_IMAGE_SCAN TIS ,APP___NB__DBUSER.T_IMAGE_DATA_INFO TID ,APP___NB__DBUSER.T_CUSTOMER TCC
                         WHERE TIS.BUSS_CODE = NCM.APPLY_CODE
                         AND TIS.DATA_SAVE_NO = TID.DATA_SAVE_NO
                         AND TCC.CUSTOMER_NAME = TID.CUSTOMER_NAME
                         AND TCC.CUSTOMER_BIRTHDAY = TID.CUSTOMER_BIRTHDAY
                         AND TCC.CUSTOMER_GENDER = TID.CUSTOMER_GENDER
                         AND TCC.CUSTOMER_CERTI_CODE = TID.CUSTOMER_CERTI_CODE
                         AND TCC.CUSTOMER_CERT_TYPE = TID.CUSTOMER_CERT_TYPE
                           AND TID.CATEGORY_TAB_CODE LIKE 'EUM004%'
                           AND TCC.CUSTOMER_ID = #{customer_id }
                           ) 
                       END) EUM004_TOTAL,  
                            (SELECT TO_CHAR(A.NON_STANDARDS_VALUE)  FROM DEV_UW.T_INSURED_LIST A,DEV_UW.T_UW_POLICY TUP
                                WHERE A.APPLY_CODE = NCM.APPLY_CODE  
                                AND A.APPLY_CODE = TUP.APPLY_CODE
                                AND TUP.UW_SOURCE_TYPE = '1'
                                AND ROWNUM = 1) NON_STANDARDS_VALUE, 
                      
                     (SELECT COUNT(1)
	                 FROM DEV_UW.T_UW_TRACE T1, DEV_UW.T_UW_MASTER U
	                WHERE U.BIZ_CODE = NCM.APPLY_CODE AND T1.UW_ID = U.UW_ID
	                  AND T1.UW_EVENT_CODE = '13') AS TOTAL_1,
	                  (SELECT COUNT(1)
	                 FROM DEV_UW.T_UW_AUTO T2, DEV_UW.T_RULE_RESULT T3, DEV_UW.T_UW_MASTER U
	                WHERE T2.AUTO_ID = T3.AUTO_ID AND T2.UW_ID = U.UW_ID
	                  AND U.BIZ_CODE = NCM.APPLY_CODE) AS TOTAL_2,
                  	(SELECT FLOOR(MONTHS_BETWEEN(NCM.APPLY_DATE, TC.CUSTOMER_BIRTHDAY) / 12)
                      FROM DEV_NB.T_CUSTOMER TC WHERE TC.CUSTOMER_ID = #{customer_id}) AS AGE,
                   	(SELECT CASE WHEN TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
					                          0,
					                          INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) - 1)) = 0 THEN
					          22
					         ELSE
					          (CASE
					            WHEN INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) = 0 THEN
					             SUBSTR(TC.SURVEY_MODULE_RESULT,
					                    INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1)
					            ELSE
					             SUBSTR(TC.SURVEY_MODULE_RESULT,
					                    INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) + 1,
					                    INSTR(TC.SURVEY_MODULE_RESULT, ',', 2) - 1)
					          END) /
					          (TRIM((SUBSTR(TC.SURVEY_MODULE_RESULT,
					                        0,
					                        INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) - 1)) / 100) *
					          (TRIM(SUBSTR(TC.SURVEY_MODULE_RESULT,
					                        0,
					                        INSTR(TC.SURVEY_MODULE_RESULT, ',', 1) - 1)) / 100))
					       END
                      FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER TC,
                           DEV_NB.T_QUESTIONAIRE_INFO     I
                     WHERE TC.SURVEY_QUESTION_ID = I.SURVEY_QUESTION_ID
                       AND (I.SURVEY_VERSION IN
                           ('65', '20', '19', '25', '52', '22', '32', '42') AND I.SURVEY_CODE = '000' 
                           OR I.SURVEY_VERSION = '102' AND I.SURVEY_CODE = '030')
                       AND TC.APPLY_CODE = NCM.APPLY_CODE
                       AND TC.QUESTIONAIRE_OBJECT = #{role_type,jdbcType=VARCHAR}
                       AND TC.CUSTOMER_ID = #{customer_id}
                       AND TC.SURVEY_MODULE_RESULT IS NOT NULL
                       AND ROWNUM = 1) AS BMI,
                       A.EXCEPTION_HEALTH_FLAG,
					   (SELECT NBI.BOOKING_STATE FROM DEV_NB.T_NB_ADVANCE_BOOKING_INFO NBI WHERE NBI.BOOKING_CODE = A.APPLY_CODE) AS BOOKING_STATE
		          FROM DEV_NB.T_NB_CONTRACT_MASTER NCM
		          ]]>
		         <if test="query_busi_code != 1 and query_busi_code != 2">
			         <![CDATA[INNER JOIN DEV_NB.T_NB_INSURED_LIST A
	            		ON A.APPLY_CODE = NCM.APPLY_CODE]]>
	           		 <if test="customer_id !=null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
           		 </if>  
		          <![CDATA[
		         INNER JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD NBP
		            ON NCM.APPLY_CODE = NBP.APPLY_CODE  
		            AND NCM.LIABILITY_STATE IN ('4', '0', '2', '3')]]>
           			<if test="query_busi_code != 1 and query_busi_code != 2">
	           			AND NBP.MASTER_BUSI_ITEM_ID IS NULL
	           			AND (NBP.ORDER_ID IS NULL OR NBP.ORDER_ID = (SELECT MIN(LNTS1.ORDER_ID)
                           FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD LNTS1,
                                DEV_NB.T_NB_BENEFIT_INSURED    LNTS2
                          WHERE LNTS1.POLICY_ID = NBP.POLICY_ID
                            AND LNTS1.POLICY_ID = LNTS2.POLICY_ID
                            AND LNTS1.BUSI_ITEM_ID = LNTS2.BUSI_ITEM_ID
                            AND LNTS1.MASTER_BUSI_ITEM_ID IS NULL 
                            AND LNTS2.INSURED_ID = A.LIST_ID
                         ))
           			</if>
           			<if test="query_busi_code==2">
	           			AND NBP.MASTER_BUSI_ITEM_ID IS NULL
	           			AND (NBP.ORDER_ID IS NULL OR NBP.ORDER_ID = '1')
           			</if>
		           <if test="query_busi_code==1">
		            AND  NBP.PRODUCT_CODE in  (${busi_prod_code})
		            AND (NBP.ORDER_ID IS NULL OR NBP.ORDER_ID = (
		            	(SELECT MIN(LNTS.ORDER_ID) FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD LNTS WHERE 
    						LNTS.APPLY_CODE = NBP.APPLY_CODE AND LNTS.PRODUCT_CODE IN (${busi_prod_code})
    					)
		            ))
		           </if>
		            <include refid="NB_queryProposalListForUw"/>
		       <![CDATA[  INNER JOIN DEV_NB.T_NB_CONTRACT_PRODUCT NCP
		            ON NCP.BUSI_ITEM_ID = NBP.BUSI_ITEM_ID
		         INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		            ON NBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
		         INNER JOIN DEV_NB.T_NB_POLICY_HOLDER NPH
		            ON NCP.APPLY_CODE = NPH.APPLY_CODE]]>
            	<if test="query_busi_code == 1 or query_busi_code == 2">
		        	<if test="customer_id !=null and customer_id != '' "><![CDATA[ AND NPH.CUSTOMER_ID = #{customer_id} ]]></if>
		         </if>
		         <![CDATA[INNER JOIN DEV_NB.T_CUSTOMER C
		            	ON NPH.CUSTOMER_ID = C.CUSTOMER_ID
		           ]]>
				 <if test="query_busi_code != 1 and query_busi_code != 2">
				  	INNER JOIN DEV_NB.T_NB_BENEFIT_INSURED BI
                    	ON BI.INSURED_ID = A.LIST_ID AND A.POLICY_ID = BI.POLICY_ID AND BI.BUSI_ITEM_ID = NBP.BUSI_ITEM_ID
				 </if>
            	 <if test="query_busi_code == 1 or query_busi_code == 2">
			         <![CDATA[INNER JOIN DEV_NB.T_NB_POLICY_HOLDER  A
	            		ON A.APPLY_CODE = NCM.APPLY_CODE]]>
	           		 <if test="customer_id !=null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
           		 </if>
           		 
            	
		         <![CDATA[ WHERE  1=1
		           AND NCP.PRODUCT_CODE NOT IN ('588001', '588002', '588003', '588004', '588005')
		           AND substr(NCP.PRODUCT_CODE,0,3) not IN ('385') ]]>
		           <if test="query_busi_code==1">AND  BP.PRODUCT_CATEGORY = '10002' </if>
		<if test="query_busi_code==1">	 
		   <if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
			      <![CDATA[ and exists  (select   1 from     DEV_UW.T_UW_POLICY UP, dev_uw.t_uw_master m, DEV_UW.T_UW_BUSI_PROD UBP 
			           where m.biz_code = NCM.apply_code
			          and m.uw_id = UP.uw_id AND UBP.UW_ID=UP.UW_ID AND UBP.UW_POLICY_ID=UBP.UW_POLICY_ID
			          and (UP.policy_decision is not null
			          and UP.policy_decision !='10'  Or UP.NON_STANDARDS = 1)
			          AND UBP.BUSI_PROD_CODE IN (${busi_prod_code})
			       	   UNION
     SELECT  1 FROM APP___UW__DBUSER.T_INSURED_LIST T,DEV_UW.T_UW_BUSI_PROD UBP, DEV_UW.T_UW_MASTER M,
     WHERE m.biz_code = NCM.apply_code 
     AND T.UW_ID = M.UW_ID 
     AND T.UW_ID = UBP.UW_ID
     AND m.uw_source_type = '1'
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
			AND UBP.BUSI_PROD_CODE IN (${busi_prod_code} 
			        )]]>
			</if>
			
			 <if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
			      <![CDATA[ and not exists  (select   1  from   DEV_UW.T_UW_POLICY UP, dev_uw.t_uw_master m, DEV_UW.T_UW_BUSI_PROD UBP 
			           where m.biz_code = NCM.apply_code 
			          and m.uw_id = UP.uw_id AND UBP.UW_ID=UP.UW_ID AND UBP.UW_POLICY_ID=UBP.UW_POLICY_ID
			          and (UP.policy_decision is not null
			          and  UP.policy_decision !='10'  Or UP.NON_STANDARDS = 1 )
			          AND UBP.BUSI_PROD_CODE IN (${busi_prod_code})
			             UNION
     SELECT  1 FROM APP___UW__DBUSER.T_INSURED_LIST T,DEV_UW.T_UW_BUSI_PROD UBP, DEV_UW.T_UW_MASTER M,
     WHERE m.biz_code = NCM.apply_code 
     AND T.UW_ID = M.UW_ID 
     AND T.UW_ID = UBP.UW_ID
     AND m.uw_source_type = '1'
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
			AND UBP.BUSI_PROD_CODE IN (${busi_prod_code} 
			       )]]>
			</if>
		 </if>
		 <if test="query_busi_code==null or query_busi_code!=1">
		  <if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==1">
	      		<![CDATA[
                         
					AND ((SELECT COUNT(1) FROM DEV_UW.T_UW_POLICY UP WHERE UP.APPLY_CODE = NCM.APPLY_CODE AND UP.UW_SOURCE_TYPE = '1'
						 AND (UP.POLICY_DECISION IS NOT NULL AND UP.POLICY_DECISION != '10' OR UP.NON_STANDARDS = 1)) 
						 + (SELECT COUNT(1) FROM DEV_NB.T_DOCUMENT TD 
				    WHERE NCM.APPLY_CODE = TD.BUSS_CODE AND TD.BUSS_SOURCE_CODE = '002' AND TD.TEMPLATE_CODE = 'UWS_00006')
				     + (SELECT COUNT(1)
                    FROM APP___UW__DBUSER.T_UW_MASTER    A,
                         APP___UW__DBUSER.T_INSURED_LIST B
                   WHERE A.UW_ID = B.UW_ID
                     AND B.APPLY_CODE = NCM.APPLY_CODE
                     AND A.UW_SOURCE_TYPE = '1'
                     AND (B.NON_STANDARDS = '1' OR  B.NON_STANDARDS_VALUE = '1' OR B.STAND_LIFE = 2)
                     AND B.CUSTOMER_DECISION_FLAG = 1)
       +
       (SELECT COUNT(1)
                  FROM DEV_NB.T_NB_INSURED_LIST    T1,
                       DEV_NB.T_DOCUMENT           T2,
                       DEV_UW.T_UW_DOCUMENT_VERIFY T3
                 WHERE 1 = 1
                   AND T1.CUSTOMER_ID = T3.CUSTOMER_ID
                   AND T2.BUSS_CODE = T1.APPLY_CODE
                   AND T1.APPLY_CODE = NCM.APPLY_CODE
                   AND T2.DOCUMENT_NO = T3.DOCUMENT_NO
                   AND T2.BUSS_ID = T3.UW_ID
                   AND T2.BUSS_SOURCE_CODE = '002'
                   AND T2.TEMPLATE_CODE = 'UWS_00006'
                    )
				    
				    ) > 0]]>
                         
				</if>
			
				<if test="isStandardRisk!=''  and isStandardRisk!=null   and isStandardRisk==0">
			      <![CDATA[ 
			        AND(
			      (EXISTS 
			      (SELECT   1  FROM  DEV_UW.T_UW_POLICY UP ,APP___UW__DBUSER.T_INSURED_LIST T
		           WHERE UP.APPLY_CODE = NCM.APPLY_CODE 
		           AND UP.UW_SOURCE_TYPE = '1'  
		           AND UP.APPLY_CODE = T.APPLY_CODE
		           AND ( UP.POLICY_DECISION ='10' or UP.POLICY_DECISION is NULL)
		           	AND (T.NON_STANDARDS IS NULL OR T.NON_STANDARDS != 1)
		           	AND (T.STAND_LIFE IS NULL OR T.STAND_LIFE !=2 )
		           	))OR((select count(1) from dev_uw.t_uw_policy up where UP.APPLY_CODE = NCM.APPLY_CODE
                                  AND UP.UW_SOURCE_TYPE = '1') = 0))
		           	]]>
				</if>
		 </if>
   		  <if test="query_busi_code==1">
	        <if test="apply_code !=null and apply_code != '' "><![CDATA[ AND NCM.apply_code = #{apply_code} ]]></if>                  
	      </if>
	      <![CDATA[)B WHERE 1=1 ]]>
		  <if test="query_busi_code==null or query_busi_code!=1">
		  	AND (PRODUCT_CATEGORY = '10001' OR SUB_APPLY_CODE IS NOT NULL)
		  </if>
		  
		   <if test="appntoldimpartInfo != '' and appntoldimpartInfo == 0">
				<![CDATA[AND (B.IS_EXCEPTION_NOTIFY_FLAG + (CASE WHEN ((B.AGE >= 18 AND (B.BMI < 15 OR B.BMI > 31)) OR B.EXCEPTION_HEALTH_FLAG >0) THEN
				1
				ELSE
				0
				END)) = 0]]>
	      </if>
	      <if test="appntoldimpartInfo != '' and appntoldimpartInfo == 1">
				<![CDATA[AND (B.IS_EXCEPTION_NOTIFY_FLAG + (CASE WHEN ((B.AGE >= 18 AND (B.BMI < 15 OR B.BMI > 31)) OR B.EXCEPTION_HEALTH_FLAG >0) THEN
				1
				ELSE
				0
				END)) > 0]]>
	      </if>
		  
         <![CDATA[ ORDER BY APPLY_DATE DESC  ]]>
    </sql>
    
    <select id="findApplyPolicyInfoByInsuredCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        select * from (
        select B.*，ROWNUM AS RN
          from (
         ]]>
         <include refid="findApplyPolicyInfoByInsuredCustomerIdSql" />
         <![CDATA[ ) B WHERE ROWNUM<=#{LESS_NUM}) C
            WHERE C.RN > #{GREATER_NUM}]]>
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryInsuredListForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.STAND_LIFE, B.SMOKING, B.RELATION_TO_INSURED_1, B.JOB_KIND, B.CUSTOMER_ID, 
			B.CUSTOMER_HEIGHT, B.JOB_CODE, B.RELATION_TO_PH, B.CUSTOMER_WEIGHT, B.APPLY_CODE, 
			B.POLICY_CODE, B.INSURED_AGE, B.LIST_ID, B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.RELATION_TO_INSURED_1, A.JOB_KIND, A.CUSTOMER_ID, 
			A.CUSTOMER_HEIGHT, A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, 
			A.POLICY_CODE, A.INSURED_AGE, A.LIST_ID, A.POLICY_ID FROM T_INSURED_LIST A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<select id="PA_findInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.RELATION_TO_INSURED_1, A.JOB_KIND, A.CUSTOMER_ID, 
			A.CUSTOMER_HEIGHT, A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, 
			A.POLICY_CODE, A.INSURED_AGE, A.LIST_ID, A.POLICY_ID FROM T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_queryInsuredListByCustomerIdCondition" />
		<include refid="PA_queryInsuredListByListIdCondition" />
	</select>
	
	<select id="PA_findInsuredListByIdAndPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.RELATION_TO_INSURED_1, A.JOB_KIND, A.CUSTOMER_ID, 
			A.CUSTOMER_HEIGHT, A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, 
			A.POLICY_CODE, A.INSURED_AGE, A.LIST_ID, A.POLICY_ID FROM T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_queryInsuredListByIdAndPolicy"/>
	</select>
	<sql id="PA_queryInsuredListByIdAndPolicy">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	
	<!-- R00101000014保单被保人信息查询 -->	
	<select id="PA_findAllInsuredInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select b.ORDER_ID,c.CUSTOMER_NAME,c.CUSTOMER_BIRTHDAY,c.CUSTOMER_GENDER,
					e.RELATION_NAME,f.GRADE_NAME,c.CUSTOMER_LEVEL,c.CUSTOMER_CERT_TYPE,g.TYPE,c.CUSTOMER_CERTI_CODE,
					c.UN_CUSTOMER_CODE,c.COUNTRY_CODE,h.COUNTRY_NAME,c.DRIVER_LICENSE_TYPE,i.LICENSE_DESC,c.JOB_CODE,
					j.CLASS_3,j.TITLE_DESC,c.MOBILE_TEL,c.OFFICE_TEL,c.FAX_TEL,c.HOUSE_TEL,d.ADDRESS,c.EMAIL,d.POST_CODE,
					c.COMPANY_NAME,c.MARRIAGE_STATUS,m.MARRIAGE,l.ORGAN_CODE,l.BRANCH_CODE,d.STATE,h.NAME as STATENAME,
					d.CITY,i.NAME as CITYNAME,d.DISTRICT,n.NAME as DISTRICTNAME,d.ADDRESS as STREET,c.JOB_TITLE,
					b.RELATION_TO_INSURED_1,e.RELATION_NAME
					from  T_INSURED_LIST a
					left outer join T_BENEFIT_INSURED b on a.LIST_ID = b.INSURED_ID
					left outer join T_CUSTOMER c on a.CUSTOMER_ID=c.CUSTOMER_ID
					left outer join T_ADDRESS d on c.CUSTOMER_ID=d.CUSTOMER_ID and a.ADDRESS_ID=d.ADDRESS_ID
					left outer join T_LA_PH_RELA e on a.RELATION_TO_PH=e.RELATION_CODE
					left outer join T_CUST_GRADE f on c.CUSTOMER_LEVEL=f.CUST_GRADE
					left outer join T_CERTI_TYPE g on c.CUSTOMER_CERT_TYPE=g.CODE
					left outer join T_COUNTRY h on c.COUNTRY_CODE=h.COUNTRY_CODE
					left outer join T_LICENSE_TYPE i on c.DRIVER_LICENSE_TYPE=i.LICENSE_TYPE
					left outer join T_JOB_CATEGORY j on c.JOB_CODE=j.JOB_CODE
					left outer join T_DISTRICT h on d.STATE=h.CODE
					left outer join T_DISTRICT i on d.CITY=i.CODE
					left outer join T_JOB_TITLE j on c.JOB_TITLE=j.TITLE_CODE
					left outer join T_LA_PH_RELA k on b.RELATION_TO_INSURED_1=k.RELATION_CODE
					left outer join T_CONTRACT_MASTER l on a.POLICY_ID=l.POLICY_ID
					left outer join T_MARRIAGE m on c.MARRIAGE_STATUS=m.MARRIAGE_CODE
					left outer join T_DISTRICT n on d.DISTRICT=n.CODE WHERE 1 = 1  ]]>
					<include refid="PA_findAllInsuredInfoByPolicyCode"/>
		<![CDATA[ ORDER BY a.LIST_ID,b.ORDER_ID ]]>
	</select>
	<select id="queryInsuredList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TIL.APPLY_CODE
			 FROM DEV_PAS.T_INSURED_LIST TIL WHERE TIL.CUSTOMER_ID=#{customer_id}  ]]>
	</select>
	<select id="queryApplyCodeIl" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TIL.APPLY_CODE
  			FROM DEV_PAS.T_INSURED_LIST TIL
 			WHERE TIL.CUSTOMER_ID = #{customer_id}  ]]>
	</select>
	<!-- t_insured_list表和t_customer表关联查询被保人信息 -->
	<select id="findAllInsuredListByUwId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT  A.ADDRESS_ID, A.UW_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, 
			A.INSURED_AGE, A.MOBILE_TEL, A.LIST_ID, A.EMAIL, A.POLICY_ID, A.STAND_LIFE, 
			 A.SMOKING, A.job_underwrite JOB_KIND, A.JOB_CODE, A.CUSTOMER_WEIGHT, 
			A.TELEPHONE, A.POLICY_CODE, A.UW_LIST_ID FROM dev_uw.T_INSURED_LIST A  WHERE 1 = 1  ]]>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	<!-- 查询责任组contractProductid -->
	<select id="findInsuredAndContractProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		select tcp.product_id from dev_pas.t_insured_list til,dev_pas.t_contract_product tcp
         where til.policy_code = tcp.policy_code
         and til.customer_id = #{customer_id}
	</select>
	
	<!-- 出险人保单查询 --><!-- 老核心查询保单层信息 -->
	<select id="QRY_queryCusContInfoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT T.* FROM (
			SELECT 'PAS' AS SOURCE,CM.POLICY_CODE AS contno,CM.ORGAN_CODE,CM.BRANCH_CODE,CM.AGENT_ORG_ID  AS signcom,
			CM.VALIDATE_DATE AS cvalidate,CM.LIABILITY_STATE,
			(SELECT MAX(PAY_DUE_DATE) FROM DEV_PAS.T_CONTRACT_EXTEND T WHERE T.POLICY_CODE=CM.POLICY_CODE ) AS paytodate
			FROM DEV_PAS.T_CONTRACT_MASTER CM 
			WHERE (EXISTS(SELECT 1 FROM DEV_PAS.T_INSURED_LIST WHERE CM.POLICY_ID=POLICY_ID AND CUSTOMER_ID=#{customer_id})
				OR EXISTS(SELECT 1 FROM DEV_PAS.T_POLICY_HOLDER WHERE CM.POLICY_ID=POLICY_ID AND CUSTOMER_ID=#{customer_id}))
			]]>
			<if test=" acc_date != null "><![CDATA[ AND CM.EXPIRY_DATE > #{acc_date} ]]></if>	
		<![CDATA[UNION
			SELECT 'NB' AS SOURCE,CM.APPLY_CODE AS contno,CM.ORGAN_CODE,CM.ORGAN_CODE,CM.AGENT_ORG_ID  AS signcom,
			CM.VALIDATE_DATE AS cvalidate,CM.LIABILITY_STATE,NULL AS paytodate
			FROM DEV_NB.T_NB_CONTRACT_MASTER CM 
			WHERE (EXISTS(SELECT 1 FROM DEV_NB.T_NB_INSURED_LIST WHERE CM.APPLY_CODE=POLICY_CODE AND CUSTOMER_ID=#{customer_id})
				OR EXISTS(SELECT 1 FROM DEV_NB.T_NB_POLICY_HOLDER WHERE CM.APPLY_CODE=POLICY_CODE AND CUSTOMER_ID=#{customer_id}))
		]]>
		<if test=" acc_date != null "><![CDATA[ AND CM.EXPIRY_DATE > #{acc_date} ]]></if>	
		<![CDATA[	
		) t
		]]>
		<if test=" strSQLa != null and strSQLa != '' ">
			${strSQLa}
		</if>
	</select>
	
	<!-- 通过policyCode 查询 -->
	<select id="QRY_findInsuredListByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 
			A.JOB_UNDERWRITE, A.LIST_ID, A.POLICY_ID FROM DEV_PAS.T_INSURED_LIST A WHERE 1 = 1 ]]>
		<include refid="QRY_queryInsuredListByPolicyCodeCondition"/>
	</select>
	
	<!-- 根据入参的投保人、被保人、证件号查询对应的保单号 -->
	<select id="QRY_INTEGRAL_findPolicyCodeByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
	    
	    <choose>
	    	<when test="customer_id !=null and customer_id!='' ">
	    		<![CDATA[ 
		    		select tph.policy_code from dev_pas.t_policy_holder tph
					where tph.customer_id = #{customer_id} union
					select til.policy_code from dev_pas.t_insured_list til
					where til.customer_id = #{customer_id}
				]]>
	    	</when>
	    	<otherwise>
	    		<![CDATA[ 
					select tph.policy_code as policy_code 
				  	from dev_pas.t_policy_holder tph, 
				    dev_pas.t_insured_list  til 
				 	where tph.policy_code = til.policy_code 
			    ]]>
				<if test="appCustomer_id != null and appCustomer_id !='' ">
		         	<![CDATA[ 
				   		and tph.customer_id = #{appCustomer_id}
		         	]]>
			    </if>
			    <if test="insCustomer_id !=null and insCustomer_id !='' ">
			        <![CDATA[ 
			        	and til.customer_id = #{insCustomer_id}
			         ]]>
			    </if>
			    <if test="fast_query !=null and fast_query!='' ">
			         <![CDATA[ 
			        	and (tph.customer_id = #{fast_query} or
				       til.customer_id = #{fast_query})
			        ]]>
			    </if>
			</otherwise>
	    </choose>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="QRY_INTEGRAL_findInsuredListByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM DEV_PAS.T_INSURED_LIST A WHERE 1 = 1   ]]>
		<include refid="PA_queryInsuredListByListIdCondition" />
		<include refid="PA_findAllInsuredInfoByPolicyCode" />
	</select>
	
	<!-- 查询保单下的第一被保险人 -->
	<select id="QRY_findAllFristInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select ti.ADDRESS_ID,
			       ti.STAND_LIFE,
			       ti.SMOKING,
			       ti.CUSTOMER_ID,
			       ti.CUSTOMER_HEIGHT,
			       ti.JOB_CODE,
			       ti.RELATION_TO_PH,
			       ti.CUSTOMER_WEIGHT,
			       ti.APPLY_CODE,
			       ti.INSURED_AGE,
			       ti.JOB_UNDERWRITE,
			       ti.LIST_ID,
			       tb.order_id,
			       tb.busi_item_id,
			       tb.policy_id,
			       tb.policy_code
			  from DEV_PAS.t_benefit_insured tb
			  left join DEV_PAS.t_insured_list ti
			    on tb.insured_id = ti.list_id
			 where ti.policy_id = #{policy_id}
			 and tb.order_id = #{order_id}]]>
	</select>
	
	<!-- 查询保单下的第一被保险人 -->
	<select id="PAandNB_findAllFristInsured" resultType="java.util.Map" parameterType="java.util.Map">
		<if test="  type != null and type == '1'.toString() ">
		<![CDATA[ select ti.ADDRESS_ID,
			       ti.STAND_LIFE,
			       ti.SMOKING,
			       ti.CUSTOMER_ID,
			       ti.CUSTOMER_HEIGHT,
			       ti.JOB_CODE,
			       ti.RELATION_TO_PH,
			       ti.CUSTOMER_WEIGHT,
			       ti.APPLY_CODE,
			       ti.INSURED_AGE,
			       ti.JOB_UNDERWRITE,
			       ti.LIST_ID,
			       tb.order_id,
			       tb.busi_item_id,
			       tb.policy_id,
			       tb.policy_code
			  from DEV_PAS.t_benefit_insured tb
			  left join DEV_PAS.t_insured_list ti
			    on tb.insured_id = ti.list_id
			 where ti.policy_id = #{policy_id}
			 and tb.order_id = #{order_id}]]>
			 
			 </if>
	
	<if test="  type != null and type == '0'.toString() ">
	SELECT TI.ADDRESS_ID,
             TI.SMOKING,
             TI.CUSTOMER_ID,
             TI.CUSTOMER_HEIGHT,
             TI.JOB_CODE,
		       TI.RELATION_TO_PH,
		       TI.CUSTOMER_WEIGHT,
		       TI.APPLY_CODE,
		       TI.JOB_UNDERWRITE,
		       TI.LIST_ID
			  FROM DEV_NB.T_NB_INSURED_LIST TI
			 where ti.policy_id = #{policy_id}
	</if>
	</select>
	
	<!-- 通过applyCode 查询 -->
	<select id="QRY_findInsuredListByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 
			A.JOB_UNDERWRITE, A.LIST_ID, A.POLICY_ID FROM DEV_NB.T_NB_INSURED_LIST A WHERE 1 = 1 ]]>
		<include refid="QRY_queryInsuredListByApplyCodeCondition"/>
	</select>
	
	<!-- 通过policyCode 查询 -->
	<select id="QRY_LpPrem_findInsuredListByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 
			A.JOB_UNDERWRITE, A.LIST_ID, A.POLICY_ID FROM DEV_PAS.T_INSURED_LIST A WHERE 1 = 1 ]]>
			 <if test=" apply_code != null and apply_code != '' ">
				AND A.APPLY_CODE = #{apply_code}
			</if>
			<if test=" policy_code != null and policy_code != '' ">
				AND A.POLICY_CODE = #{policy_code}
			</if>
		<![CDATA[
		UNION
		SELECT A.ADDRESS_ID, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 
			A.JOB_UNDERWRITE, A.LIST_ID, A.POLICY_ID FROM DEV_NB.T_NB_INSURED_LIST A WHERE 1 = 1 ]]>	
		 <if test=" apply_code != null and apply_code != '' ">
				AND A.APPLY_CODE = #{apply_code}
			</if>
			<if test=" policy_code != null and policy_code != '' ">
				AND A.POLICY_CODE = #{policy_code}
			</if>
	</select>
	<!-- 按索引查询操作    中保信保单编码路由快查  新核心保单是否存在   -->
      
    <select id="queryPolicySequenceNoExist" resultType="java.lang.Integer" parameterType="java.util.Map">
      <![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_MEDICAL CM WHERE CM.POLICY_SEQUENCE_NO = #{policy_code} ]]>
    </select>
	 <!-- 电话中心接口查询未承保信息-根据入参的投保人、被保人、证件号查询对应的保单号，投保单号 -->
	<select id="findApplyPolicyCodeByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">	     
	    <choose>
	    	<when test="customer_id !=null and customer_id!='' ">
	    		<![CDATA[ 
		    		select tph.policy_code,tph.apply_code from dev_nb.t_nb_policy_holder tph
					where tph.customer_id = #{customer_id} union
					select til.policy_code,til.apply_code from dev_nb.t_nb_insured_list til
					where til.customer_id = #{customer_id}
				]]>
	    	</when>
	    	<otherwise>
	    		<![CDATA[ 
					select tph.policy_code as policy_code,tph.apply_code as apply_code 
				  	from dev_nb.t_nb_policy_holder tph, 
				    dev_nb.t_nb_insured_list  til 
				 	where tph.apply_code = til.apply_code 
			    ]]>
				<if test="appCustomer_id != null and appCustomer_id !='' ">
		         	<![CDATA[ 
				   		and tph.customer_id = #{appCustomer_id}
		         	]]>
			    </if>
			    <if test="insCustomer_id !=null and insCustomer_id !='' ">
			        <![CDATA[ 
			        	and til.customer_id = #{insCustomer_id}
			         ]]>
			    </if>			    
			</otherwise>
	    </choose>
	</select>
	
	<select id="UW_queryUwDocumentByBussCode" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[SELECT count(*) FROM DEV_NB.T_DOCUMENT D WHERE 1 = 1 AND D.TEMPLATE_CODE = 'UWS_00006' 
				AND D.BUSS_SOURCE_CODE = '002' AND D.BUSS_CODE = #{buss_code} ]]>
	</select>
	
	<select id="findMaxAmountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT MAX(tcp.amount) maxAmount  FROM dev_pas.T_CONTRACT_PRODUCT tcp 
		     WHERE 1=1 ]]>
		 <if test="policyCodeList.size() > 0">
            <![CDATA[ AND tcp.POLICY_CODE IN ]]>
            <foreach item="item" index="id" collection="policyCodeList"
                open="(" separator="," close=")">
                #{item}
            </foreach>
            </if>
        <if test=" acc_date != null "><![CDATA[ AND tcp.EXPIRY_DATE > #{acc_date} ]]></if>
		
	</select>
	<!-- 根据保单号查询被保人表信息(#rm133925) -->
	<select id="qry_findInsuredListInterface" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.ANNUAL_INCOME_CEIL,A.INCOME_SOURCE,A.RESIDENT_TYPE,AGENT_RELATION,
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID,A.INS_SPE_PEOPLE, A.RURAL_POPULATION_FLAG ,A.DISABILITY_FLAG ,A.DISABILITY_NO,A.NEW_RESIDENT FROM APP___PAS__DBUSER.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_insuredListWhereCondition" />
	</select>
	<select id="qry_findInsuredListByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.ADDRESS_ID, A.STAND_LIFE, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 
			A.INSURED_AGE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.POLICY_ID FROM dev_pas.T_INSURED_LIST A WHERE 1 = 1  ]]>
		<include refid="PA_queryInsuredListByCustomerIdCondition" />
		<include refid="PA_queryInsuredListByListIdCondition" />
		<include refid="PA_insuredListWhereCondition" />
	</select>
	<select id="UW_findUwMasterByBizCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.UW_ID, A.BIZ_CODE, A.MEET_POV_STANDARD_FLAG FROM DEV_UW.T_UW_MASTER A WHERE 1 = 1  ]]>
		<if test=" uw_id != null and uw_id != ''  "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" biz_code != null and biz_code != ''  "><![CDATA[ AND A.BIZ_CODE = #{biz_code} ]]>
		<if test="uw_id == null or uw_id == ''"><![CDATA[AND A.UW_ID = 
				(select max(B.uw_id) from DEV_UW.T_UW_MASTER B where B.BIZ_CODE = #{biz_code} )]]></if>
		</if>
		<if test=" uw_status != null and uw_status != ''  "><![CDATA[ AND A.UW_STATUS = #{uw_status} ]]></if>
		<![CDATA[ ORDER BY A.UW_ID ]]>
	</select>
	
			
	<select id="findAllInsuredListBys" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SMOKING, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, 
			A.JOB_CODE, A.RELATION_TO_PH, A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, 
			A.JOB_UNDERWRITE, A.LIST_ID, A.POLICY_ID FROM DEV_PAS.T_INSURED_LIST A WHERE 1 = 1 ]]>
		<include refid="QRY_queryInsuredListByPolicyCodeCondition"/>
	</select>
	
	<!-- 通过赔案和保单获取被保险人  -->
	<select id="findCustomerNameByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.CUSTOMER_NAME
				FROM APP___CLM__DBUSER.T_INSURED_LIST A 
				INNER JOIN APP___CLM__DBUSER.T_CUSTOMER B
				 ON A.CUSTOMER_ID = B.CUSTOMER_ID 
				 AND A.CUR_FLAG = '1' 
				 WHERE ROWNUM <= 1000     
  		]]>
  		<include refid="PA_insuredListWhereCondition" />
	</select>
</mapper>
