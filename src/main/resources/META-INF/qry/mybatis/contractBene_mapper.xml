<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.impl.task.dao.impl.ContractBeneDaoImpl">

<sql id="PA_contractBeneWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" share_order  != null "><![CDATA[ AND A.SHARE_ORDER = #{share_order} ]]></if>
		<if test=" bene_type  != null "><![CDATA[ AND A.BENE_TYPE = #{bene_type} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" designation != null and designation != ''  "><![CDATA[ AND A.DESIGNATION = #{designation} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" legal_bene  != null "><![CDATA[ AND A.LEGAL_BENE = #{legal_bene} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" share_rate  != null "><![CDATA[ AND A.SHARE_RATE = #{share_rate} ]]></if>
		<if test=" company_id  != null "><![CDATA[ AND A.COMPANY_ID = #{company_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="PA_queryContractBeneByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractBeneByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractBeneByBeneTypeCondition">
		<if test=" bene_type  != null "><![CDATA[ AND A.BENE_TYPE = #{bene_type} ]]></if>
	</sql>	
	<sql id="PA_queryContractBeneByShareOrderCondition">
		<if test=" share_order  != null "><![CDATA[ AND A.SHARE_ORDER = #{share_order} ]]></if>
	</sql>	
	<sql id="PA_queryContractBeneByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	

<!-- 按索引查询操作 -->	
	<select id="PA_findContractBeneByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractBeneByListIdCondition" />
	</select>
	
	<select id="PA_findContractBeneByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractBeneByPolicyIdCondition" />
	</select>
	
	<select id="PA_findContractBeneByBeneType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractBeneByBeneTypeCondition" />
	</select>
	
	<select id="PA_findContractBeneByShareOrder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractBeneByShareOrderCondition" />
	</select>
	
	<select id="PA_findContractBeneByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractBeneByAddressIdCondition" />
	</select>
	
	<select id="PA_findContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="PA_contractBeneWhereCondition" />
	</select>
	
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM DEV_PAS.T_CONTRACT_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractBeneWhereCondition" />
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE, A.COMPANY_ID FROM DEV_PAS.T_CONTRACT_BENE A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_contractBeneWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractBeneTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_CONTRACT_BENE A WHERE 1 = 1  ]]>
		<include refid="PA_contractBeneWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractBeneForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.SHARE_ORDER, B.BENE_TYPE, B.PRODUCT_CODE, B.CUSTOMER_ID, 
			B.INSURED_ID, B.APPLY_CODE, B.POLICY_CODE, B.DESIGNATION, B.LIST_ID, 
			B.LEGAL_BENE, B.BUSI_ITEM_ID, B.POLICY_ID, B.SHARE_RATE FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.APPLY_CODE, A.POLICY_CODE, A.DESIGNATION, A.LIST_ID, 
			A.LEGAL_BENE, A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM T_CONTRACT_BENE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_contractBeneWhereCondition" />
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!--组合查询被保人及险种信息 -->
	<select id="PA_findContractBeneComps" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TC.CUSTOMER_NAME AS INSURED_NAME,TCBP.BUSI_PRD_ID,TCBP.BUSI_PROD_CODE,A.*
  			FROM DEV_PAS.T_CONTRACT_BENE A,DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,DEV_PAS.T_INSURED_LIST TIL,DEV_PAS.T_CUSTOMER  TC
 				WHERE A.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
   					AND TIL.LIST_ID = A.INSURED_ID
  					AND TIL.CUSTOMER_ID = TC.CUSTOMER_ID	]]>
		<include refid="PA_contractBeneWhereCondition" />
	</select>
	<!--再保险 受益人查询-->
	<select id="findNameInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
select distinct cb.product_code , bp.product_name_sys   ,p.prem ,p.amount from    DEV_PAS.T_contract_bene cb ,DEV_PAS.T_contract_master m, DEV_PAS.T_business_product bp,
 (select sum(t.total_prem_af) as prem , sum(t.amount) as amount from  ( select cp.* from DEV_PAS.T_contract_product cp where cp.policy_code = #{policy_code} ) t ,DEV_PAS.T_contract_bene cb where 
cb.policy_id = t.policy_id) p
where 
    cb.product_code = bp.product_code_sys  and cb.policy_id = m.policy_id and m.policy_code= #{policy_code}
	]]>
	</select>
    <!--根据投保单号查询受益人xinxi -->
	<select id="findNameInfoByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
select distinct cb.product_code , bp.product_name_sys   ,p.prem ,p.amount from    DEV_NB.T_NB_contract_bene cb,DEV_NB.T_NB_contract_master m,DEV_PDS.T_business_product bp,
 (select sum(t.total_prem_af) as prem , sum(t.amount) as amount from  ( select cp.* from DEV_NB.T_NB_contract_product cp where cp.apply_code = #{apply_code}) t ,DEV_NB.T_NB_contract_bene cb where 
cb.policy_id = t.policy_id) p
where cb.product_code = bp.product_code_sys  and cb.policy_id = m.policy_id and m.apply_code= #{apply_code}
	]]>
	</select>
	<select id="findBeiefitInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 select distinct  cb.bene_type ,cb.designation , cb.share_order ,cb.share_rate ,c.customer_gender ,
               c.customer_cert_type,c.customer_certi_code, c.customer_name ,c1.customer_id as insuredno , c1.customer_name as name
      from DEV_PAS.T_contract_bene cb , DEV_PAS.T_contract_master m,DEV_PAS.T_customer c ,DEV_PAS.T_insured_list i ,DEV_PAS.T_customer c1
  where cb.policy_id = m.policy_id and m.policy_code =#{policy_code} and c.customer_id = cb.customer_id and i.policy_code = #{policy_code} and i.customer_id = c1.customer_id
	]]>
	</select>
	<!--再保险 受益人查询-->
	<select id="findBeiefitInfoByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		 select distinct  cb.bene_type ,cb.designation , cb.share_order ,cb.share_rate ,c.customer_gender ,
               c.customer_cert_type,c.customer_certi_code, c.customer_name ,c1.customer_id as insuredno , c1.customer_name as name
      from DEV_PAS.T_contract_bene cb , DEV_PAS.T_contract_master m,DEV_PAS.T_customer c ,DEV_PAS.T_insured_list i ,DEV_PAS.T_customer c1
  where cb.policy_id = m.policy_id and m.apply_code =#{apply_code} and c.customer_id = cb.customer_id and i.apply_code = #{apply_code} and i.customer_id = c1.customer_id
	]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="findAllContractBene" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID, A.POLICY_ID, A.POLICY_CODE, A.APPLY_CODE, A.BUSI_ITEM_ID, A.PRODUCT_CODE, A.INSURED_ID, 
			A.BENE_TYPE, A.SHARE_ORDER, A.SHARE_RATE, A.DESIGNATION, A.ADDRESS_ID, A.LEGAL_BENE, A.CUSTOMER_ID, A.CUSTOMER_NAME, 
			A.CUSTOMER_BIRTHDAY, A.CUSTOMER_GENDER, A.CUSTOMER_CERT_TYPE, A.CUSTOMER_CERTI_CODE, A.OLD_BNF_NO, A.BENE_KIND, A.COMPANY_ID, 
			A.AGENT_RELATION FROM DEV_PAS.T_CONTRACT_BENE A WHERE ROWNUM <=  1000 ]]>
		<include refid="PA_contractBeneWhereCondition" />
	</select>
	<!-- 查询被保人下身故受益人，只查询主险的，并且包括任何状态的 -->
    <select id="findMainContractBeneInfos" resultType="java.util.Map" parameterType="java.util.Map">
          <![CDATA[  SELECT   TCU.CUSTOMER_CERTI_CODE,
			       TCU.CUSTOMER_GENDER,
			       TCU.CUSTOMER_BIRTHDAY,
			       TCU.CUST_CERT_STAR_DATE,
			       TCU.CUST_CERT_END_DATE,			      
			       TCU.CUSTOMER_CERT_TYPE,
			       TCU.JOB_CODE,
			       TCU.CUSTOMER_NAME,
			       TCU.CUSTOMER_ID,
			       TCU.OLD_CUSTOMER_ID,
			       TCU.COUNTRY_CODE,			       
			       TCB.SHARE_ORDER,
			       TCB.SHARE_RATE,
			       TCB.DESIGNATION,			       
			       TIL.RELATION_TO_PH,
			       TCU.MOBILE_TEL,
			       B.FIXED_TEL,
			       B.STATE,
			       B.CITY,
			       B.DISTRICT,
			       B.ADDRESS,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.STATE = O.CODE) AS STATENAME,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.CITY = O.CODE) AS CITYNAME,
			       (SELECT O.NAME
			          FROM APP___PAS__DBUSER.T_DISTRICT O
			         WHERE B.DISTRICT = O.CODE) AS DISTRICTNAME,
			       (SELECT O.COUNTRY_NAME
			          FROM APP___PAS__DBUSER.T_COUNTRY O
			         WHERE O.COUNTRY_CODE = TCU.COUNTRY_CODE) COUNTRY_NAME,
			       (SELECT O.TYPE
			          FROM APP___PAS__DBUSER.T_CERTI_TYPE O
			         WHERE O.CODE = TCU.CUSTOMER_CERTI_CODE) CERT_TYPE_NAME
			
			  FROM APP___PAS__DBUSER.T_INSURED_LIST TIL
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BENE TCB
				    ON TIL.LIST_ID = TCB.INSURED_ID
				 INNER JOIN APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP
				    ON TCB.POLICY_ID = TCBP.POLICY_ID
				   AND TCB.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
				 INNER JOIN APP___PAS__DBUSER.T_CUSTOMER TCU
				    ON TCB.CUSTOMER_ID = TCU.CUSTOMER_ID
				  LEFT JOIN APP___PAS__DBUSER.T_ADDRESS B
				    ON TCB.ADDRESS_ID = B.ADDRESS_ID
				 WHERE TCBP.MASTER_BUSI_ITEM_ID IS NULL
				   AND TCB.BENE_TYPE = '1' /*身故受益人*/
			   AND TIL.POLICY_CODE = #{policy_code}
			   ]]> 
    </select>
</mapper>
