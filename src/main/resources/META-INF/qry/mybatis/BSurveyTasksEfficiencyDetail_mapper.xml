<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="com.nci.tunan.qry.dao.IBSurveyTasksEfficiencyDetailDao">
	<sql id="queryBfSurveyTasksEfficiencyDetailForPage_info">
		<if test=" organCode != null and organCode != ''  "><![CDATA[ AND orgN.ORGAN_CODE  like #{organCode}||'%' ]]></if>
		<if test=" startTime != null and startTime != ''  "><![CDATA[ AND trunc(SurA.APPLY_DATE) >= #{startTime} ]]></if>
		<if test=" endTime != null and endTime != ''  "><![CDATA[  AND trunc(SurA.APPLY_DATE) <= #{endTime} ]]></if>
		<if test=" planName != null and planName != ''  "><![CDATA[   AND surBatch.Plan_Name like #{planName}||'%' ]]></if>
	</sql>


	<!-- 分页查询保后调查效能表 -->
	<select id="TasksEfficiencyDetailForPage"
		parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT C.* FROM ( SELECT B.*,ROWNUM RN FROM  (SELECT M.organCodeName,
        M.organCode as organCode,
       count(M.Apply_Id) as totalTask,
       SUM(M.totalSurveyPositive) as totalSurveyPositive,
       SUM(M.totaladdimpart) as totaladdimpart,
       SUM(M.totalRefualPolicy) as totalRefualPolicy,
       SUM(M.totalConditionPolicy) as totalConditionPolicy,
       SUM(M.totalAddPremPolicy) as totalAddPremPolicy,
       SUM(M.totalCancelCompany) as totalCancelCompany,
       SUM(M.totalSurrenderPolicy) as totalSurrenderPolicy,
       SUM(M.totalCancelCompanyAmount)as totalCancelCompanyAmount,
       SUM(M.totalSurrenderPolicyAmount) as totalSurrenderPolicyAmount,
       SUM(M.totalSplRiskAmount) as totalSplRiskAmount,
       SUM(M.TotalAddAmount) as TotalAddAmount,
       SUM(M.TotalReturnAmountOrCurrentPric) as TotalReturnAmountOrCurrentPric

  from (SELECT orgN.Organ_Name as organCodeName,
               ConMaster.Organ_Code as organCode,
               surA.Apply_Id, 
               (select COUNT(surCon.POSITIVE_FLAG)
                  from DEV_CLM.T_SURVEY_CONCLUSION surCon
                 where surCon.POSITIVE_FLAG = '1'
                   and surCon.Apply_Id = SurA.Apply_Id) as totalSurveyPositive, 
               COUNT(HiTask.Apply_Code) as totalAddImpart,
               (SELECT count(1) from dev_pas.t_uw_policy up
                   inner join dev_pas.T_UW_BUSI_PROD ubp
                      on up.policy_id=ubp.policy_id
                     and up.uw_id=ubp.uw_id
                   inner join dev_pas.t_uw_master um
                      on up.uw_id = um.uw_id
                   where um.biz_code = SurA.Cs_Accept_Code and up.policy_id=ConMaster.Policy_id and ubp.Decision_Code in (40, 50)) as totalRefualPolicy, 
               (SELECT count(uwProd.DECISION_CODE)
                  FROM DEV_CLM.T_UW_BUSI_PROD uwProd
                 WHERE uwProd.Policy_Code = sura.policy_code
                   and HiTask.Apply_Id = surA.Apply_Id
                   and uwProd.Decision_Code = 33) as totalConditionPolicy, 
               (SELECT count(uwProd.DECISION_CODE)
                  FROM DEV_CLM.T_UW_BUSI_PROD uwProd
                 WHERE uwProd.Policy_Code = sura.Policy_Code
                   and HiTask.Apply_Id = surA.Apply_Id
                   and uwProd.DECISION_CODE = 31) as totalAddPremPolicy, 
               (SELECT COUNT(PoChange.SERVICE_CODE)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange
                 WHERE PoChange.SERVICE_CODE = 'EA'
                   and HiTask.Apply_Id = surA.Apply_Id
                   and PoChange.Policy_Code = surA.Policy_Code) as totalCancelCompany, 
               (SELECT COUNT(PoChange.SERVICE_CODE)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange
                 WHERE PoChange.Service_Code = 'CT'
                   and HiTask.Apply_Id = surA.Apply_Id
                   and poChange.Policy_Code = SurA.Policy_Code) as totalSurrenderPolicy, 
               (SELECT COALESCE(SUM(ConProduct.AMOUNT), 0)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange,
                       DEV_CLM.T_CONTRACT_PRODUCT ConProduct
                 WHERE PoChange.SERVICE_CODE = 'EA'
                   and PoChange.Policy_Code = ConProduct.Policy_Code
                   and HiTask.Apply_Id = surA.Apply_Id
                   and ConProduct.Policy_Code = surA.Policy_Code) as totalCancelCompanyAmount, 
               (SELECT COALESCE(SUM(ConProduct.AMOUNT), 0)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange,
                       DEV_CLM.T_CONTRACT_PRODUCT ConProduct
                 WHERE PoChange.SERVICE_CODE = 'CT'
                   and HiTask.Apply_Id = surA.Apply_Id
                   and PoChange.Policy_Code = ConProduct.Policy_Code
                   and ConProduct.Policy_Code = surA.Policy_Code) as totalSurrenderPolicyAmount, 
               (SELECT COALESCE(SUM(ConProduct.AMOUNT), 0)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange,
                       DEV_CLM.T_CONTRACT_PRODUCT ConProduct,
                       DEV_CLM.T_UW_POLICY        uwPolicy
                 WHERE uwPolicy.Policy_Decision = '33'
                   and uwPolicy.Uw_Policy_Id = uwBuProd.Uw_Policy_Id
                   and HiTask.Apply_Id = surA.Apply_Id
                   and PoChange.Policy_Code = ConProduct.Policy_Code
                   and ConProduct.Policy_Code = surA.Policy_Code) as totalSplRiskAmount, 
               (SELECT COALESCE(SUM(a.extra_prem_af), 0) as 总加费
                  FROM dev_pas.t_cs_Contract_Product a
                 WHERE a.Policy_Id = ConMaster.Policy_Id
                   and uwBuProd.Decision_Code in ('31')
                   and a.old_new = '1') -
               (SELECT COALESCE(sum(a.extra_prem_af), 0) as 总加费
                  FROM dev_pas.t_cs_Contract_Product a
                 WHERE a.Policy_Id = ConMaster.Policy_Id
                   and uwBuProd.Decision_Code in ('31')
                   and a.old_new = '0') as TotalAddAmount, 
               (SELECT COALESCE(SUM(csArAp.FEE_AMOUNT), 0)
                  FROM DEV_PAS.T_CS_PREM_ARAP csArAp
                  left join APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC
                    ON (CAC.ACCEPT_CODE = csArAp.BUSINESS_CODE),
                 APP___PAS__DBUSER.T_CS_POLICY_CHANGE C,
                 APP___PAS__DBUSER.T_PAYER_ACCOUNT D
                 WHERE 1 = 1
                   AND C.Service_Code in ('CT', 'EA')
                   AND CAC.CHANGE_ID = C.CHANGE_ID
                   AND CAC.ACCEPT_ID = C.ACCEPT_ID
                   AND C.POLICY_CODE = SurA.POLICY_CODE) as TotalReturnAmountOrCurrentPric 
          FROM DEV_CLM.t_Survey_Apply SurA
          left join DEV_PAS.t_Contract_Master   ConMaster
          on ConMaster.Policy_Code = SurA.Policy_Code 
          left join DEV_CLM.T_UDMP_ORG          orgN 
          on ConMaster.Organ_Code = orgN.Organ_Code
          left join DEV_CLM.t_claim_survey_task surTask
          on surTask.Biz_No = SurA.Policy_Code
          left join DEV_CLM.T_CLAIM_SURVEY_BATCH surBatch
            on surBatch.Batch_Id = surTask.Batch_Id    
          left join DEV_CLM.T_CLAIM_HI_TASK HiTask
            on HiTask.Apply_Id = surA.Apply_Id
          left join DEV_CLM.T_UW_Master uwMaster
            on uwMaster.Biz_Code = HiTask.Apply_Code
          left join DEV_CLM.T_UW_BUSI_PROD uwBuProd
            on uwBuProd.Uw_Id = uwMaster.Uw_Id
         WHERE  surA.Biz_Type = '5'   ]]>
		<include
			refid="queryBfSurveyTasksEfficiencyDetailForPage_info" />
 <![CDATA[   group by SurA.Apply_Id,
                  ConMaster.Policy_Id,
                  HiTask.Apply_Id,
                  sura.policy_code,
                  ConMaster.Organ_Code,
                  orgN.Organ_Name,
                  sura.survey_code,
                  sura.apply_code,
                  uwBuProd.Uw_Policy_Id,
                  uwBuProd.Decision_Code,
                  SurA.Cs_Accept_Code) M group by M.organCodeName, M.organCode ]]>
             <![CDATA[)B WHERE ROWNUM <= #{LESS_NUM} )C WHERE C.RN > #{GREATER_NUM}]]>
	</select>
	<!-- 分页查询保后调查效能表 -->
	<select id="TasksEfficiencyDetailForTotal"
		parameterType="java.util.Map" resultType="java.lang.Integer">
			<![CDATA[SELECT Count(1) FROM  (SELECT B.*,ROWNUM RN FROM  (SELECT M.organCodeName,
        M.organCode as organCode,
       count(M.Apply_Id) as totalTask,
       SUM(M.totalSurveyPositive) as totalSurveyPositive,
       SUM(M.totaladdimpart) as totaladdimpart,
       SUM(M.totalRefualPolicy) as totalRefualPolicy,
       SUM(M.totalConditionPolicy) as totalConditionPolicy,
       SUM(M.totalAddPremPolicy) as totalAddPremPolicy,
       SUM(M.totalCancelCompany) as totalCancelCompany,
       SUM(M.totalSurrenderPolicy) as totalSurrenderPolicy,
       SUM(M.totalCancelCompanyAmount)as totalCancelCompanyAmount,
       SUM(M.totalSurrenderPolicyAmount) as totalSurrenderPolicyAmount,
       SUM(M.totalSplRiskAmount) as totalSplRiskAmount,
       SUM(M.TotalAddAmount) as TotalAddAmount,
       SUM(M.TotalReturnAmountOrCurrentPric) as TotalReturnAmountOrCurrentPric

  from (SELECT orgN.Organ_Name as organCodeName,
               ConMaster.Organ_Code as organCode,
               surA.Apply_Id,  
               (select COUNT(surCon.POSITIVE_FLAG)
                  from DEV_CLM.T_SURVEY_CONCLUSION surCon
                 where surCon.POSITIVE_FLAG = '1'
                   and surCon.Apply_Id = SurA.Apply_Id) as totalSurveyPositive, 
               COUNT(HiTask.Apply_Code) as totalAddImpart,
               (SELECT count(uwProd.DECISION_CODE)
                  FROM DEV_CLM.T_UW_BUSI_PROD uwProd
                 WHERE uwProd.Policy_Code = sura.policy_code
                   and HiTask.Apply_Id = surA.Apply_Id
                   and uwProd.Decision_Code in (40, 50)) as totalRefualPolicy, 
               (SELECT count(uwProd.DECISION_CODE)
                  FROM DEV_CLM.T_UW_BUSI_PROD uwProd
                 WHERE uwProd.Policy_Code = sura.policy_code
                   and HiTask.Apply_Id = surA.Apply_Id
                   and uwProd.Decision_Code = 33) as totalConditionPolicy, 
               (SELECT count(uwProd.DECISION_CODE)
                  FROM DEV_CLM.T_UW_BUSI_PROD uwProd
                 WHERE uwProd.Policy_Code = sura.Policy_Code
                   and HiTask.Apply_Id = surA.Apply_Id
                   and uwProd.DECISION_CODE = 31) as totalAddPremPolicy, 
               (SELECT COUNT(PoChange.SERVICE_CODE)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange
                 WHERE PoChange.SERVICE_CODE = 'EA'
                   and HiTask.Apply_Id = surA.Apply_Id
                   and PoChange.Policy_Code = surA.Policy_Code) as totalCancelCompany, 
               (SELECT COUNT(PoChange.SERVICE_CODE)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange
                 WHERE PoChange.Service_Code = 'CT'
                   and HiTask.Apply_Id = surA.Apply_Id
                   and poChange.Policy_Code = SurA.Policy_Code) as totalSurrenderPolicy, 
               (SELECT COALESCE(SUM(ConProduct.AMOUNT), 0)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange,
                       DEV_CLM.T_CONTRACT_PRODUCT ConProduct
                 WHERE PoChange.SERVICE_CODE = 'EA'
                   and PoChange.Policy_Code = ConProduct.Policy_Code
                   and HiTask.Apply_Id = surA.Apply_Id
                   and ConProduct.Policy_Code = surA.Policy_Code) as totalCancelCompanyAmount, 
               (SELECT COALESCE(SUM(ConProduct.AMOUNT), 0)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange,
                       DEV_CLM.T_CONTRACT_PRODUCT ConProduct
                 WHERE PoChange.SERVICE_CODE = 'CT'
                   and HiTask.Apply_Id = surA.Apply_Id
                   and PoChange.Policy_Code = ConProduct.Policy_Code
                   and ConProduct.Policy_Code = surA.Policy_Code) as totalSurrenderPolicyAmount, 
               (SELECT COALESCE(SUM(ConProduct.AMOUNT), 0)
                  FROM DEV_PAS.T_CS_POLICY_CHANGE PoChange,
                       DEV_CLM.T_CONTRACT_PRODUCT ConProduct,
                       DEV_CLM.T_UW_POLICY        uwPolicy
                 WHERE uwPolicy.Policy_Decision = '33'
                   and uwPolicy.Uw_Policy_Id = uwBuProd.Uw_Policy_Id
                   and HiTask.Apply_Id = surA.Apply_Id
                   and PoChange.Policy_Code = ConProduct.Policy_Code
                   and ConProduct.Policy_Code = surA.Policy_Code) as totalSplRiskAmount, 
               (SELECT COALESCE(SUM(a.extra_prem_af), 0) as 总加费
                  FROM dev_pas.t_cs_Contract_Product a
                 WHERE a.Policy_Id = ConMaster.Policy_Id
                   and uwBuProd.Decision_Code in ('31')
                   and a.old_new = '1') -
               (SELECT COALESCE(sum(a.extra_prem_af), 0) as 总加费
                  FROM dev_pas.t_cs_Contract_Product a
                 WHERE a.Policy_Id = ConMaster.Policy_Id
                   and uwBuProd.Decision_Code in ('31')
                   and a.old_new = '0') as TotalAddAmount, 
               (SELECT COALESCE(SUM(csArAp.FEE_AMOUNT), 0)
                  FROM DEV_PAS.T_CS_PREM_ARAP csArAp
                  left join APP___PAS__DBUSER.T_CS_ACCEPT_CHANGE CAC
                    ON (CAC.ACCEPT_CODE = csArAp.BUSINESS_CODE),
                 APP___PAS__DBUSER.T_CS_POLICY_CHANGE C,
                 APP___PAS__DBUSER.T_PAYER_ACCOUNT D
                 WHERE 1 = 1
                   AND C.Service_Code in ('CT', 'EA')
                   AND CAC.CHANGE_ID = C.CHANGE_ID
                   AND CAC.ACCEPT_ID = C.ACCEPT_ID
                   AND C.POLICY_CODE = SurA.POLICY_CODE) as TotalReturnAmountOrCurrentPric 
          FROM DEV_CLM.t_Survey_Apply SurA
          left join DEV_PAS.t_Contract_Master   ConMaster
          on ConMaster.Policy_Code = SurA.Policy_Code 
          left join DEV_CLM.T_UDMP_ORG          orgN 
          on ConMaster.Organ_Code = orgN.Organ_Code
          left join DEV_CLM.t_claim_survey_task surTask
          on surTask.Biz_No = SurA.Policy_Code
          left join DEV_CLM.T_CLAIM_SURVEY_BATCH surBatch
            on surBatch.Batch_Id = surTask.Batch_Id    
          left join DEV_CLM.T_CLAIM_HI_TASK HiTask
            on HiTask.Apply_Id = surA.Apply_Id
          left join DEV_CLM.T_UW_Master uwMaster
            on uwMaster.Biz_Code = HiTask.Apply_Code
          left join DEV_CLM.T_UW_BUSI_PROD uwBuProd
            on uwBuProd.Uw_Id = uwMaster.Uw_Id
         WHERE  surA.Biz_Type = '5'   ]]>
		<include
			refid="queryBfSurveyTasksEfficiencyDetailForPage_info" /> 
 <![CDATA[ group by SurA.Apply_Id,
                  ConMaster.Policy_Id,
                  HiTask.Apply_Id,
                  sura.policy_code,
                  ConMaster.Organ_Code,
                  orgN.Organ_Name,
                  sura.survey_code,
                  sura.apply_code,
                  uwBuProd.Uw_Policy_Id,
                  uwBuProd.Decision_Code,
                  SurA.Cs_Accept_Code) M group by M.organCodeName, M.organCode
]]> 
          <![CDATA[)B )C]]>
	</select>
</mapper>