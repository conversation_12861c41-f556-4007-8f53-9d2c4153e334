<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
 
<mapper namespace="contractMaster">

	<sql id="PA_contractMasterWhereCondition">
		<if test=" policy_pwd != null and policy_pwd != ''  "><![CDATA[ AND A.POLICY_PWD = #{policy_pwd} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_agent_name != null and sale_agent_name != ''  "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} ]]></if>
		<if test=" insured_family  != null "><![CDATA[ AND A.INSURED_FAMILY = #{insured_family} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" derivation != null and derivation != ''  "><![CDATA[ AND A.DERIVATION = #{derivation} ]]></if>
		<if test=" channel_org_code != null and channel_org_code != ''  "><![CDATA[ AND A.CHANNEL_ORG_CODE = #{channel_org_code} ]]></if>
		<if test=" basic_remark != null and basic_remark != ''  "><![CDATA[ AND A.BASIC_REMARK = #{basic_remark} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pwd_invalid_flag  != null "><![CDATA[ AND A.PWD_INVALID_FLAG = #{pwd_invalid_flag} ]]></if>
		<if test=" submit_channel  != null "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" sale_agent_code != null and sale_agent_code != ''  "><![CDATA[ AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENCY_CODE = #{agency_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" service_handler_code != null and service_handler_code != ''  "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" service_handler != null and service_handler != ''  "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if>
		<if test=" e_service_flag  != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" dc_indi  != null "><![CDATA[ AND A.DC_INDI = #{dc_indi} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" agent_org_id != null and agent_org_id != ''  "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if>
		<if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" lang_code != null and lang_code != ''  "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" input_type  != null  and  input_type  != ''  "><![CDATA[ AND A.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" subinput_type  != null  and  subinput_type  != ''  "><![CDATA[ AND A.SUBINPUT_TYPE = #{subinput_type} ]]></if>
		<if test=" sale_type  != null  and  sale_type  != ''  "><![CDATA[ AND A.SALE_TYPE = #{sale_type} ]]></if>
	</sql>


<!-- 按索引生成的查询条件  -->	
	<sql id="PA_queryContractMasterByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryContractMasterByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="PA_queryContractMasterByBranchCodeCondition">
		<if test=" branch_code != null and branch_code != '' "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
	</sql>	
	<sql id="PA_queryContractMasterByOrganCodeCondition">
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
	</sql>	
	<sql id="PA_queryContractMasterByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = trim(#{policy_code}) ]]></if>
	</sql>	


<!-- 按索引查询操作 -->	
	<select id="PA_findContractMasterByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, A.Lapse_Date,
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID, A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE FROM dev_pas.CONTRACT_MASTER A WHERE 1 = 1   ]]>
		<include refid="PA_queryContractMasterByPolicyIdCondition" />
	</select>
	
	<select id="PA_findContractMasterByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID , A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE FROM DEV_PAS.T_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterByApplyCodeCondition" />
	</select>
	
	<select id="PA_findContractMasterByBranchCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID , A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE FROM DEV_PAS.T_CONTRACT_MASTER A WHERE 1 = 1   ]]>
		<include refid="PA_queryContractMasterByBranchCodeCondition" />
	</select>
	
	<select id="PA_findContractMasterByOrganCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID , A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE FROM dev_pas.CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_queryContractMasterByOrganCodeCondition" />
	</select>
	
	<select id="PA_findContractMasterByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID, A.DERIVATION, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID , A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE FROM DEV_PAS.T_CONTRACT_MASTER A WHERE 1 = 1   ]]>
		<include refid="PA_queryContractMasterByPolicyCodeCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID , A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE FROM DEV_PAS.T_CONTRACT_MASTER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 查询所有操作 -->
	<select id="PA_findAllContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID, A.DERIVATION, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID , A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE FROM DEV_PAS.T_CONTRACT_MASTER A WHERE ROWNUM <=  1000   ]]>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findContractMasterTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_MASTER A WHERE 1 = 1  ]]>
		<include refid="PA_contractMasterWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryContractMasterForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.POLICY_PWD, B.MEDIA_TYPE, B.INTEREST_MODE, B.APPLY_CODE, B.ORGAN_CODE, B.CHANNEL_TYPE, B.SALE_AGENT_NAME, 
			B.INSURED_FAMILY,  B.POLICY_ID, B.DERIVATION, B.CHANNEL_ORG_CODE, 
			B.BASIC_REMARK, B.POLICY_TYPE, B.EXPIRY_DATE, B.PWD_INVALID_FLAG, B.SUBMIT_CHANNEL, B.LIABILITY_STATE, B.POLICY_CODE, 
			B.SALE_AGENT_CODE, B.BRANCH_CODE, B.VALIDATE_DATE, B.AGENCY_CODE, 
			B.MONEY_CODE, B.APL_PERMIT, B.SERVICE_HANDLER_CODE, B.APPLY_DATE, B.INITIAL_VALIDATE_DATE, B.SUBMISSION_DATE, 
			B.SERVICE_HANDLER, B.E_SERVICE_FLAG, B.SERVICE_BANK_BRANCH, B.DC_INDI, B.END_CAUSE, B.ISSUE_DATE, 
			B.LAPSE_CAUSE, B.DECISION_CODE, B.AGENT_ORG_ID, B.SERVICE_BANK, B.SUSPEND_DATE, B.SUSPEND_CAUSE, B.LANG_CODE, 
			B.FORMER_ID , B.INPUT_DATE, B.INPUT_TYPE, B.SUBINPUT_TYPE, B.SALE_TYPE FROM (
					SELECT ROWNUM RN, A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.CHANNEL_ORG_CODE, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.APL_PERMIT, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID, A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE  FROM DEV_PAS.T_CONTRACT_MASTER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="PA_contractMasterWhereCondition" /> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 查询单条 -->
	<select id="PA_findContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID, A.DERIVATION, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID , A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE,A.STATISTIC_CHANNEL, A.SALE_COM_CODE, A.RERINSTATE_DATE,A.LAPSE_DATE,A.INITIAL_PREM_DATE   FROM DEV_PAS.T_CONTRACT_MASTER A WHERE 1 = 1   ]]>
		<include refid="PA_contractMasterWhereCondition" />
	</select>
	
	<!-- 查询单条 -->
	<select id="findContractMasterPAandNB" resultType="java.util.Map" parameterType="java.util.Map">
		<if test=" type != null and type == '1'.toString() ">
		<![CDATA[SELECT A.POLICY_PWD, A.MEDIA_TYPE, A.INTEREST_MODE, A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID, A.DERIVATION, 
			A.BASIC_REMARK, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE, A.BRANCH_CODE, A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.DC_INDI, A.END_CAUSE, A.ISSUE_DATE, 
			A.LAPSE_CAUSE, A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, 
			A.FORMER_ID , A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE,A.STATISTIC_CHANNEL, A.SALE_COM_CODE, A.RERINSTATE_DATE,A.LAPSE_DATE,A.INITIAL_PREM_DATE,A.SPECIAL_ACCOUNT_FLAG,B.BUSI_ITEM_ID   FROM DEV_PAS.T_CONTRACT_MASTER A,DEV_PAS.T_CONTRACT_BUSI_PROD B WHERE A.POLICY_CODE= B.POLICY_CODE AND B.MASTER_BUSI_ITEM_ID IS NULL    ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		</if>
		
		<if test=" type != null and type == '0'.toString() ">
		<![CDATA[SELECT A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY,  A.POLICY_ID,A.POLICY_TYPE, A.EXPIRY_DATE,A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, 
			A.SALE_AGENT_CODE,A.VALIDATE_DATE, A.AGENCY_CODE, 
			A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE,A.SUBMISSION_DATE, 
			A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH,A.ISSUE_DATE, 
			 A.DECISION_CODE, A.AGENT_ORG_ID, A.SERVICE_BANK,A.LANG_CODE, 
			 A.INPUT_DATE, A.INPUT_TYPE, A.SUBINPUT_TYPE, A.SALE_TYPE, A.SALE_COM_CODE,A.INITIAL_PREM_DATE,B.BUSI_ITEM_ID  
			FROM DEV_NB.T_NB_CONTRACT_MASTER A,DEV_NB.T_NB_CONTRACT_BUSI_PROD B WHERE A.APPLY_CODE= B.APPLY_CODE AND B.MASTER_BUSI_ITEM_ID IS NULL AND  A.POLICY_CODE IS NULL]]>
		    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{policy_code} ]]></if>
		</if>
	</select>
	
	
	
	<select id="PA_findContractMasterComp" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.MONEY_CODE, A.APL_PERMIT, A.POLICY_PWD, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, A.MEDIA_TYPE, 
			A.APPLY_CODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, A.SUBMISSION_DATE, 
			A.INSURED_FAMILY, A.E_SERVICE_FLAG, A.SERVICE_HANDLER, A.POLICY_ID, A.SERVICE_BANK_BRANCH, A.DC_INDI, 
			A.DERIVATION, A.END_CAUSE, A.ISSUE_DATE, A.CHANNEL_ORG_CODE, A.LAPSE_CAUSE, 
			A.BASIC_REMARK, A.AGENT_ORG_ID, A.DECISION_CODE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.VALIDATE_DATE, 
			A.LIABILITY_STATE, A.SUBMIT_CHANNEL, A.POLICY_CODE, A.SERVICE_BANK, A.SALE_AGENT_CODE, A.BRANCH_CODE, 
			A.LANG_CODE, A.FORMER_ID, A.AGENCY_CODE FROM DEV_PAS.T_CONTRACT_MASTER A 
			           LEFT JOIN T_UDMP_ORG TCO1 ON A.ORGAN_CODE = TCO1.ORGAN_CODE
			           LEFT JOIN (SELECT TA2.ADDRESS CHANNEL_ORG_ADDRESS,TCO2.CHANNLE_CODE,TCO2.CHANNEL_NAME CHANNEL_ORG_NAME FROM DEV_PAS.T_CHANNEL_ORG TCO2,DEV_PAS.T_ADDRESS TA2 
			                   WHERE TCO2.ADDRESS_ID = TA2.ADDRESS_ID) C
			           ON A.CHANNEL_ORG_CODE = C.CHANNLE_CODE
			           LEFT JOIN (SELECT TA3.AGENT_CODE,TA3.AGENT_NAME SERVICE_AGENT_NAME,TCO3.CHANNEL_NAME SERVICE_ORGAN FROM DEV_PAS.T_AGENT TA3,DEV_PAS.T_CHANNEL_ORG TCO3
			                    WHERE TA3.SALES_ORGAN_CODE = TCO3.CHANNLE_CODE) D
			           ON A.AGENT_ORG_ID = D.AGENT_CODE
			           LEFT JOIN (SELECT TCA4.AGENT_CODE, TCO4.CHANNEL_NAME AGENT_ORGAN_NAME FROM DEV_PAS.T_contract_agent TCA4,DEV_PAS.T_CHANNEL_ORG TCO4
			                   WHERE TCA4.AGENT_ORGAN_CODE = TCO4.CHANNLE_CODE) E 
			           ON  A.SERVICE_HANDLER_CODE = E.AGENT_CODE
			      WHERE 1 = 1]]>
		<include refid="PA_queryContractMasterByPolicyIdCondition" />
		<include refid="PA_queryContractMasterByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID ]]>
	</select>
	
	<select id="findAgentCodeByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select SALE_AGENT_CODE 
			  FROM DEV_PAS.T_CONTRACT_MASTER a
			 where a.SALE_AGENT_CODE is not null
			   and exists
			 (select 'X' FROM DEV_PAS.T_agent where agent_code = a.SALE_AGENT_CODE)
			   and a.policy_code in
			       (select policy_code
			          FROM DEV_PAS.T_policy_holder
			         where customer_id = #{customer_Id}
			        union
			        select policy_code
			          FROM DEV_PAS.T_insured_list
			         where customer_id = #{customer_Id})
   ]]>
	</select>
	<!-- E事历-保单提醒功能查询 -->
	<select id="queryContractMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		select 
       a.policy_code,
        b.PAY_DUE_DATE,
       c.MATURITY_DATE,
       d.PAY_DUE_DATE as PAY_DUE_DATE1 
  FROM DEV_PAS.T_CONTRACT_MASTER    a,
       T_CONTRACT_EXTEND    b,
       T_CONTRACT_BUSI_PROD c,
       T_PAY_PLAN           d
 	where 
 		a.policy_id = d.policy_id and  a.policy_id = b.policy_id and a.policy_id = c.policy_id  and
 		a.policy_code = #{policy_code}
		]]>
		<if test=" start_date!= null and start_date!='' "><![CDATA[ b.pay_due_date >=#{start_date}]]></if>
		<if test= " end_date !=null and end_date !='' "><![CDATA[ and b.pay_due_date <= #{end_date}]]></if>
	</select>
	
	<select id="findCustomerPolicyByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select A.POLICY_CODE , A.ORGAN_CODE, A.VALIDATE_DATE, C.PRODUCT_NAME_SYS, D.STATUS_NAME
			  	 from 
			  	 	T_CONTRACT_MASTER A, T_CONTRACT_BUSI_PROD B, T_BUSINESS_PRODUCT C, T_LIABILITY_STATUS D
			 	 where 
			 	 	A.POLICY_CODE exists
					(select policy_code
				    	FROM DEV_PAS.T_policy_holder
				     	where customer_id = #{customer_Id}
			      	 union all
			         select policy_code
					 	FROM DEV_PAS.T_insured_list
					    where customer_id = #{customer_Id}
					)
					AND A.LIABILITY_STATE in ('1','2','3','4')
					AND B.POLICY_CODE = A.POLICY_CODE
					AND (B.MASTER_BUSI_ITEM_ID = B.BUSI_ITEM_ID or B.BUSI_ITEM_ID is null)
					AND C.BUSINESS_PRD_ID = B.BUSI_PRD_ID
					AND D.STATUS_CODE = A.LIABILITY_STATE
   ]]>
	</select>
	
	<select id="findFundTransHisByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select 
				    T.ContNo,
				    T.POLICY_ID AS PolicyId,
				    T.LIST_ID AS ListId,
				    T.CValiDate,
				    T.ContState,
				    T.UnitCount,
				    T.Money,
				    T.UnitPrice,
				    T.EdorAppDate,
				    G.PRODUCT_ABBR_NAME AS RiskShortName, 
				    F.Post_Code AS ZipCode,
				    F.address AS PostalAddress, 
				    E.Customer_Name AS AppntName,
				    E.customer_gender AS AppntSex,
            		H.ORGAN_ADDRESS
				FROM
				(select 
				    A.policy_code AS ContNo,
				    A.POLICY_ID,
				    C.LIST_ID, 
				    K.BUSI_PRD_ID,
				    A.VALIDATE_DATE AS CValiDate,
				    A.LIABILITY_STATE AS ContState,
				    C.TRANS_UNITS AS UnitCount,
				    C.TRANS_AMOUNT AS Money,
				    C.TRANS_PRICE AS UnitPrice,
            		A.ORGAN_CODE,
            		M.APPLY_TIME AS EdorAppDate
				from 
				    T_CONTRACT_MASTER A, T_CONTRACT_BUSI_PROD K
				LEFT JOIN T_FUND_TRANS C ON C.TRANS_CODE = '22' 
				                       AND C.POLICY_ID = K.POLICY_ID 
				                       AND C.DEAL_TIME BETWEEN date#{mStartDate} AND date#{mEndDate}
				LEFT JOIN T_FUND_TRANS_APPLY M ON M.APPLY_ID = C.APPLY_ID
				where 
				    	A.POLICY_CODE = #{contNo}
				    AND K.POLICY_CODE = A.POLICY_CODE) T,  
				    T_POLICY_HOLDER D, T_CUSTOMER E, T_ADDRESS F, T_BUSINESS_PRODUCT G, T_UDMP_ORG H
				where
				    	D.POLICY_CODE = T.ContNo
				    AND E.CUSTOMER_ID = D.CUSTOMER_ID 
				    AND F.ADDRESS_ID = D.ADDRESS_ID 
				    AND G.BUSINESS_PRD_ID = T.BUSI_PRD_ID
	                AND H.ORGAN_CODE = T.ORGAN_CODE
 		]]>
	</select>
	<!-- 体检资料查询 -->
	<select id="queryContract" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT TCM.POLICY_ID,
       				TCM.APPLY_CODE,
      				TCM.POLICY_CODE,
       				TCM.ORGAN_CODE,
       				TPH.CUSTOMER_ID,
       				TC.CUSTOMER_NAME,
       				TCA.AGENT_CODE,
       				TCA.AGENT_NAME 
  				FROM DEV_PAS.T_CONTRACT_MASTER TCM 
 				INNER JOIN DEV_PAS.T_POLICY_HOLDER TPH 
    				ON TCM.POLICY_ID = TPH.POLICY_ID 
 				INNER JOIN DEV_PAS.T_CUSTOMER TC 
    				ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID 
 				INNER JOIN DEV_PAS.T_contract_agent TCA
    				ON TPH.POLICY_ID = TCA.POLICY_ID 
 				WHERE TCM.APPLY_CODE = #{apply_code} 
		 ]]>
	</select>
	<select id="queryContractByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[	SELECT TCM.POLICY_ID,
		             TCM.APPLY_CODE,
		             TCM.POLICY_CODE,
		             TCM.ORGAN_CODE,
		             TPH.CUSTOMER_ID,
		             TC.CUSTOMER_NAME,
		             TCA.AGENT_CODE,
		             (select agent_name 
		             from DEV_PAS.T_AGENT 
		             where agent_code=tca.AGENT_CODE) as agent_name
		         FROM DEV_nb.T_nb_CONTRACT_MASTER TCM 
		       INNER JOIN DEV_nb.T_nb_POLICY_HOLDER TPH 
		           ON TCM.POLICY_ID = TPH.POLICY_ID 
		       INNER JOIN DEV_nb.T_CUSTOMER TC 
		           ON TPH.CUSTOMER_ID = TC.CUSTOMER_ID 
		       INNER JOIN DEV_nb.T_nb_CONTRACT_AGENT TCA
		           ON TPH.POLICY_ID = TCA.POLICY_ID 
		       WHERE TCM.APPLY_CODE = #{apply_code}]]>
	</select>
	<select id="queryContractByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CM.APPLY_CODE,
			CM.POLICY_CODE,
			CM.ORGAN_CODE,
			PH.CUSTOMER_ID,
			C.CUSTOMER_NAME,
			CA.AGENT_CODE, C.OLD_CUSTOMER_ID,
			(SELECT A.AGENT_NAME FROM DEV_PAS.T_AGENT A WHERE A.AGENT_CODE = CA.AGENT_CODE) AGENT_NAME
			FROM DEV_NB.T_NB_CONTRACT_MASTER CM
			LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PH
			ON CM.POLICY_ID = PH.POLICY_ID
			LEFT JOIN DEV_NB.T_CUSTOMER C
			ON PH.CUSTOMER_ID = C.CUSTOMER_ID
			LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
			ON CA.POLICY_ID = PH.POLICY_ID
			WHERE CM.POLICY_CODE = #{policy_code}
         ]]>
	</select>
	
	<select id="findAllQuestionaireCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_MODULE_RESULT, A.CUSTOMER_ID, A.QUESTIONAIRE_OBJECT, A.APPLY_CODE, A.SURVEY_QUESTION_ID, 
			A.CUSTOMER_SURVEY_ID, A.POLICY_CODE, A.POLICY_ID, 
			A.AGENT_CODE FROM DEV_PAS.V_QUESTIONAIRE_CUSTOMER_ALL A WHERE ROWNUM <=  1000  ]]>
		<include refid="questionaireCustomerWhereCondition" />
		<![CDATA[ ORDER BY A.CUSTOMER_SURVEY_ID ]]> 
	</select>
	<sql id="questionaireCustomerWhereCondition">
		<if test=" survey_module_result != null and survey_module_result != ''  "><![CDATA[ AND A.SURVEY_MODULE_RESULT = #{survey_module_result} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" questionaire_object != null and questionaire_object != ''  "><![CDATA[ AND A.QUESTIONAIRE_OBJECT = #{questionaire_object} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" survey_question_id  != null "><![CDATA[ AND A.SURVEY_QUESTION_ID = #{survey_question_id} ]]></if>
		<if test=" customer_survey_id  != null "><![CDATA[ AND A.CUSTOMER_SURVEY_ID = #{customer_survey_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>
	
	<!-- 保单查询接口-按投保单号或保单号查询 -->
	<select id="policyQueryInfoByAppOrPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT TCM.POLICY_CODE, /*保单号*/
       				 TO_CHAR(TIL1.BUSI_ITEM_ID) BUSI_ITEM_ID, /*险种ID*/
       				 TCM.POLICY_ID, /*保单ID*/
       				(SELECT COUNT(*) FROM DEV_NB.T_NB_QT_TASK T2
         				WHERE T2.APPLY_CODE = TCM.APPLY_CODE AND T2.QA_TYPE = '4' AND T2.QT_STATUS = '7') AS ZILIAO, /*投保资料是否质检通过*/
       				(SELECT COUNT(*) FROM DEV_NB.T_NB_QT_TASK T1
         				WHERE T1.APPLY_CODE = TCM.APPLY_CODE AND T1.QA_TYPE = '5' AND T1.QT_STATUS = '7') AS HUIZHI, /*回执质检是否通过*/
		       		TIL1.APPLY_CODE, /*投保单号*/
			      	TCM.POLICY_TYPE, /*保单类型*/
			      	TCM.SUBINPUT_TYPE, /*出单标识*/
			      	TIL1.PRD_PKG_CODE, /*产品组合名称*/
        			TO_CHAR(TPH1.CUSTOMER_CERT_TYPE) AS PH_CUSTOMER_CERT_TYPE, /*投保人证件类型*/
       				TPH1.CUSTOMER_CERTI_CODE AS APPNT_ID_NUMBER, /*投保人证件号码*/
       				TO_CHAR(TPH1.CUSTOMER_ID) AS PH_CUSTOMER_ID, /*投保人客户ID*/
       				TPH1.CUSTOMER_ID_CODE AS PH_CUSTOMER_ID_CODE, /*投保人身份证号码*/
        			TO_CHAR(TPH1.CUSTOMER_BIRTHDAY,'yyyy-MM-dd')  AS APPNT_BIRTHDAY, /*投保人出生日期*/
       				TO_CHAR(TPH1.CUSTOMER_GENDER) AS APPNT_SEX, /*投保人性别*/
       				TPH1.MOBILE_TEL AS APPNT_MOBILE, /*投保人手机号*/
       				TPH1.EMAIL,/*投保人邮箱*/
        			TO_CHAR(TIL1.RELATION_TO_PH) AS APPNT_AND_INSURED_RELATION, /*投保人与被保人关系*/
       				TPH1.CUSTOMER_NAME AS PH_CUSTOMER_NAME, /*投保人客户姓名*/
       				TPH1.ADDRESS,
       				TO_CHAR(TPH1.CUST_CERT_STAR_DATE,'yyyy-MM-dd') AS APPNT_CERT_STAR_DATE,/*投保人证件有效起期*/
           	    	TO_CHAR(TPH1.CUST_CERT_END_DATE,'yyyy-MM-dd') AS APPNT_CERT_END_DATE,/*投保人证件有效止期*/
           	    	TPH1.COUNTRY_CODE AS APPNT_COUNTRY,/*投保人国籍*/
       				(SELECT T.PRODUCT_NAME_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT T
         				WHERE T.PRODUCT_CODE_SYS = TIL1.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*主险名称*/
       				TIL1.BUSI_PROD_CODE AS PRODUCT_CODE_SYS, /*主险代码*/
       				TCM.APPLY_DATE, /*申请日期*/
       				TO_CHAR(TIL1.CUSTOMER_CERT_TYPE) AS INSURED_ID_TYPE, /*被保人证件类型*/
       				
       				/*需求 #122752-保单查询接口-按身份证号查询+按投保单号或保单号查询 变更----start */
       				TIL1.CUSTOMER_CERTI_CODE AS INSURED_ID_NUMBER, /*第一被保人证件号码*/
       				TO_CHAR(TIL1.CUSTOMER_ID) AS IN_CUSTOMER_ID, /*第一被保人客户ID*/
       				TIL1.CUSTOMER_ID_CODE AS IN_CUSTOMER_ID_CODE, /*第一被保人客户号码*/
       				TIL1.CUSTOMER_NAME AS IN_CUSTOMER_NAME, /*第一被保人客户姓名*/
      				TIL1.COUNTRY_CODE AS INSURED_COUNTRY,/*被保人国籍*/
      				
       				TO_CHAR(TIL2.OLD_CUSTOMER_ID) AS IN_CUSTOMER_ID_SEC, /*第二被保人客户号*/
       				TIL2.CUSTOMER_ID_CODE AS IN_CUSTOMER_ID_CODE_SEC, /*第二被保人身份证号码*/
       				TIL2.CUSTOMER_NAME AS IN_CUSTOMER_NAME_SEC, /*第二被保人客户姓名*/
       				TO_CHAR(TIL2.CUSTOMER_CERT_TYPE) AS INSURED_ID_TYPE_SEC, /*第二被保人证件类型*/ 
       				TIL2.CUSTOMER_CERTI_CODE AS INSURED_ID_NUMBER_SEC, /*第二被保人证件号码*/
       				TIL2.COUNTRY_CODE AS SECOND_INSURED_COUNTRY,/*第二被保人国籍*/
       				TO_CHAR(TIL2.RELATION_TO_PH) AS APPNT_AND_INSURED_RELATION_SEC, /*投保人与第二被保人关系*/
       				/*需求 #122752-保单查询接口-按身份证号查询+按投保单号或保单号查询 变更----end */
       				
       				(SELECT LIST_ID FROM DEV_NB.T_NB_QT_DEFECT T WHERE T.TASK_ID = 
       					(SELECT MAX(A.TASK_ID) FROM DEV_NB.T_NB_QT_TASK A
                             WHERE A.APPLY_CODE = TCM.APPLY_CODE AND ROWNUM = 1)) LIST_ID, /*质检缺失资料*/
       				TCM.MEDIA_TYPE, /*保单的发送形式*/
       				(SELECT Z.CONFIRM_WAY FROM DEV_NB.T_NB_CONTRACT_MASTER Z
         				WHERE Z.POLICY_CODE = TCM.POLICY_CODE AND ROWNUM = 1) AS CONFIRM_WAY, /*移动平台客户确认方式*/
       				TCM.SUBMIT_CHANNEL, /*递交渠道*/
       				(SELECT PROPOSAL_STATUS FROM DEV_NB.T_NB_CONTRACT_MASTER
         				WHERE APPLY_CODE = TCM.APPLY_CODE) PROPOSAL_STATUS, /*投保单状态*/
       				(CASE WHEN TCM.SUBMIT_CHANNEL = '3' THEN '1'
         				  WHEN TCM.SUBMIT_CHANNEL = '5' AND (TCM.SUBINPUT_TYPE = '18' OR TCM.SUBINPUT_TYPE = '19') THEN '0'
         				  ELSE '2' END) AS IS_BPO_FLAG, /*是否为BPO出单标识0-新时代出单；1-BPO出单；2-非BPO和新时代出单；*/
         				  
       				TCM.ISSUE_DATE AS issue_date, /*签单日期*/
       				TCM.SERVICE_BANK AS service_bank,/*银行*/
       				
       				(CASE WHEN TCM.POLICY_CODE <> TCM.RELATION_POLICY_CODE AND TCM.DOUBLE_MAINRISK_FLAG = '1' THEN
	          				(SELECT TO_CHAR(MIN(PRINT_TIME),'yyyy-MM-dd') FROM (SELECT DECODE(PPA.PRINT_TYPE, '1', PPA.BPO_PRINT_DATE, PPA.PRINT_TIME) AS PRINT_TIME,
	                          PPA.POLICY_CODE,
	                          PPA.PRINT_TYPE
	                     	FROM DEV_PAS.T_POLICY_PRINT PPA
	                   		UNION
	                   		SELECT DECODE(PPN.PRINT_TYPE, '1', PPN.BPO_PRINT_DATE, PPN.PRINT_TIME) AS PRINT_TIME,
	                          PPN.POLICY_CODE,
	                          PPN.PRINT_TYPE
	                     	FROM DEV_NB.T_POLICY_PRINT PPN) A WHERE POLICY_CODE = TCM.RELATION_POLICY_CODE
	              				AND PRINT_TIME IS NOT NULL
	              				AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1'))
         				ELSE
       						(SELECT TO_CHAR(MIN(PRINT_TIME),'yyyy-MM-dd')  FROM (SELECT DECODE(PPA.PRINT_TYPE, '1', PPA.BPO_PRINT_DATE, PPA.PRINT_TIME) AS PRINT_TIME,
                          		PPA.POLICY_CODE,
                          		PPA.PRINT_TYPE
                     		FROM DEV_PAS.T_POLICY_PRINT PPA
                   			UNION
                   			SELECT DECODE(PPN.PRINT_TYPE, '1', PPN.BPO_PRINT_DATE, PPN.PRINT_TIME) AS PRINT_TIME,
                          		PPN.POLICY_CODE,
                          		PPN.PRINT_TYPE
                   			  FROM DEV_NB.T_POLICY_PRINT PPN) A WHERE POLICY_CODE = TCM.POLICY_CODE
              				AND PRINT_TIME IS NOT NULL AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1')) END) AS print_date, /*保单打印日期*/
    			(SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = TCM.ORGAN_CODE) AS E_CHECKCOM_CODE, /*保单管理机构*/
       			(SELECT PA.ACCOUNT FROM DEV_PAS.T_PAYER_ACCOUNT PA WHERE PA.POLICY_ID = TCM.POLICY_ID) BANK_CARD_NUMBER, /*首期缴费银行账号*/
       			(SELECT PA.NEXT_ACCOUNT FROM DEV_PAS.T_PAYER_ACCOUNT PA WHERE PA.POLICY_ID = TCM.POLICY_ID) NEXTACCOUNT, /*续期缴费银行账号*/
       			(CASE WHEN TCM.SUBMIT_CHANNEL = '3' THEN '1'
         			  WHEN TCM.SUBMIT_CHANNEL = '5' AND (TCM.SUBINPUT_TYPE = '18' OR TCM.SUBINPUT_TYPE = '19') THEN '0'
         			  WHEN TCM.SUBMIT_CHANNEL = '1' THEN '2'
         			  WHEN TCM.SUBMIT_CHANNEL = '11' THEN '3'
         			  ELSE '' END) AS CONT_PLAT_FLAG, /*出单系统编码*/
         		(SELECT TO_CHAR(NTCM.DRQ_FLAG) FROM DEV_NB.T_NB_CONTRACT_MASTER NTCM WHERE NTCM.APPLY_CODE = TCM.APPLY_CODE) AS DRQ_FLAG, /*双录标识*/  
  				(CASE WHEN TCM.POLICY_REINSURE_FLAG = '1' THEN '1'
         			  WHEN TCM.POLICY_REINSURE_FLAG = '2' OR TCM.POLICY_REINSURE_FLAG = '3' THEN '2'
         			  ELSE ''
         		 END) AS POLICY_REINSURE_FLAG , /*重投或转投标识，01-已重投；02-已转保*/
         		 	(SELECT er.jointly_insured_type FROM DEV_NB.T_NB_CONTRACT_MASTER er
                 WHERE er.POLICY_CODE = TCM.POLICY_CODE AND ROWNUM = 1) AS  JOINTLY_INSURED_TYPE, 
         		(CASE WHEN TCM.FORMER_ID IS NOT NULL THEN (SELECT CA.SERVICE_TYPE FROM DEV_PAS.T_RENEW_CHANGE RC
                     INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC ON RC.CHANGE_ID=AC.CHANGE_ID
                     AND RC.ACCEPT_ID=AC.ACCEPT_ID
                     INNER JOIN DEV_PAS.T_CS_APPLICATION CA ON AC.CHANGE_ID=CA.CHANGE_ID
                     INNER JOIN  DEV_PDS.t_Business_Product BP ON RC.New_Busi_Prd_Id=BP.BUSINESS_PRD_ID
                     WHERE RC.POLICY_ID=TCM.FORMER_ID 
                     AND RC.VALID_STATUS = '1'  
                     AND AC.ACCEPT_STATUS = '18' AND  RC.RENEW_CHANGE_TYPE='3' AND AC.SERVICE_CODE='RR' AND BP.Product_Code_Sys='00846000')
            ELSE '' END) AS RR_INFO /*保全转保信息*/
  			FROM DEV_PAS.T_CONTRACT_MASTER TCM
		LEFT JOIN (SELECT *
               FROM (SELECT TCBP.PRD_PKG_CODE,
                            TCBP.BUSI_PROD_CODE,
                            TCBP.BUSI_ITEM_ID,
                            TCBP.MASTER_BUSI_ITEM_ID,
                            TIL.APPLY_CODE,
                            TIL.POLICY_CODE,
                            TIL.LIST_ID,
                            TIL.POLICY_ID,
                            TIL.RELATION_TO_PH,
                            CU.CUSTOMER_ID,
                            CU.CUSTOMER_NAME,
                            CU.CUSTOMER_BIRTHDAY,
                            CU.CUSTOMER_GENDER,
                            CU.CUSTOMER_CERT_TYPE,
                            CU.CUSTOMER_CERTI_CODE,
                            CU.CUSTOMER_ID_CODE,
                            CU.COUNTRY_CODE
                       FROM DEV_PAS.T_INSURED_LIST       TIL,
                            DEV_PAS.T_CUSTOMER           CU,
                            DEV_PAS.T_BENEFIT_INSURED    TBI,
                            DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
                      WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
                        AND TBI.INSURED_ID = TIL.LIST_ID
                        AND TBI.POLICY_ID = TIL.POLICY_ID
                        AND TBI.POLICY_ID = TCBP.POLICY_ID
                        AND TBI.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                        AND TCBP.MASTER_BUSI_ITEM_ID IS NULL  
                        AND TBI.ORDER_ID=1 ]]>
                        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TIL.POLICY_CODE = #{policy_code} ]]></if>
                        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TIL.APPLY_CODE = #{apply_code} ]]></if>
                        <![CDATA[ ORDER BY TBI.ORDER_ID) WHERE ROWNUM = 1) TIL1 ON TIL1.POLICY_ID = TCM.POLICY_ID
                        
		        /*需求 #122752-保单查询接口-按身份证号查询+按投保单号或保单号查询 变更----start */
				LEFT JOIN (SELECT *
		               FROM (SELECT TIL.APPLY_CODE,
                            TIL.POLICY_CODE,
                            TIL.LIST_ID,
                            TIL.POLICY_ID,
                            TIL.RELATION_TO_PH,
                            CU.OLD_CUSTOMER_ID,
                            CU.CUSTOMER_NAME,
                            CU.CUSTOMER_BIRTHDAY,
                            CU.CUSTOMER_GENDER,
                            CU.CUSTOMER_CERT_TYPE,
                            CU.CUSTOMER_CERTI_CODE,
                            CU.CUSTOMER_ID_CODE,
                            CU.COUNTRY_CODE
                        FROM DEV_PAS.T_INSURED_LIST       TIL,
                             DEV_PAS.T_CUSTOMER           CU,
                             DEV_PAS.T_BENEFIT_INSURED    TBI
                       WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
                         AND TBI.INSURED_ID = TIL.LIST_ID
                         AND TBI.POLICY_ID = TIL.POLICY_ID
                         AND TBI.ORDER_ID=2 ]]>
                        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TIL.POLICY_CODE = #{policy_code} ]]></if>
                        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TIL.APPLY_CODE = #{apply_code} ]]></if>
                        <![CDATA[ )WHERE ROWNUM = 1) TIL2 ON TIL2.POLICY_ID = TCM.POLICY_ID 
        		/*需求 #122752-保单查询接口-按身份证号查询+按投保单号或保单号查询 变更----end */
        
 		LEFT JOIN (SELECT TA.MOBILE_TEL,
                           TPH.ADDRESS_ID,
                           TPH.POLICY_CODE,
                           TPH.POLICY_ID,
                           CU.CUSTOMER_ID,
                           CU.CUSTOMER_NAME,
                           CU.CUSTOMER_BIRTHDAY,
                           CU.CUSTOMER_GENDER,
                           CU.CUSTOMER_CERT_TYPE,
                           CU.CUSTOMER_CERTI_CODE,
                           CU.CUSTOMER_ID_CODE,
                           TA.EMAIL,
                           DS.NAME || DC.NAME || DD.NAME || TA.ADDRESS AS ADDRESS,
                           CU.CUST_CERT_STAR_DATE,
      					   CU.CUST_CERT_END_DATE,
      					   CU.COUNTRY_CODE
                           FROM DEV_PAS.T_POLICY_HOLDER TPH
                           INNER JOIN DEV_PAS.T_CUSTOMER CU
                               ON TPH.CUSTOMER_ID = CU.CUSTOMER_ID
                           INNER JOIN DEV_PAS.T_ADDRESS TA
                                ON TPH.ADDRESS_ID = TA.ADDRESS_ID
                           AND TPH.CUSTOMER_ID = TA.CUSTOMER_ID
                            LEFT JOIN DEV_PAS.T_DISTRICT DS
                                ON DS.CODE = TA.STATE
                            LEFT JOIN DEV_PAS.T_DISTRICT DC
                                ON DC.CODE = TA.CITY
                            LEFT JOIN DEV_PAS.T_DISTRICT DD
                                 ON DD.CODE = TA.DISTRICT
                              WHERE 1 = 1 ]]>
		                <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPH.POLICY_CODE = #{policy_code} ]]></if>
		                <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TPH.APPLY_CODE = #{apply_code} ]]></if>
		                <![CDATA[) TPH1
		    ON TPH1.POLICY_ID = TIL1.POLICY_ID
		   AND TPH1.POLICY_ID = TCM.POLICY_ID
		 WHERE 1 = 1
		   AND TCM.ORGAN_CODE LIKE #{organ_code} || '%' ]]>
		    <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
   			<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TCM.APPLY_CODE = #{apply_code} ]]></if>
   			<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND EXISTS (SELECT 1
      			FROM DEV_PAS.T_contract_agent TCA WHERE TCA.POLICY_ID = TCM.POLICY_ID AND TCA.AGENT_CODE = #{agent_code}) ]]></if>
   </select>
	
	
   <!-- 保单查询接口-按投保单号或保单号查询 -->
	<select id="applyQueryInfoByAppOrPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT TCM.POLICY_CODE, /*保单号*/
       				 TO_CHAR(TIL1.BUSI_ITEM_ID) BUSI_ITEM_ID, /*险种ID*/
       				 TCM.POLICY_ID, /*保单ID*/
       			(SELECT COUNT(*) FROM DEV_NB.T_NB_QT_TASK T2
         			WHERE T2.APPLY_CODE = TCM.APPLY_CODE AND T2.QA_TYPE = '4' AND T2.QT_STATUS = '7') AS ZILIAO, /*投保资料是否质检通过*/
       			(SELECT COUNT(*) FROM DEV_NB.T_NB_QT_TASK T1 WHERE T1.APPLY_CODE = TCM.APPLY_CODE
           			AND T1.QA_TYPE = '5' AND T1.QT_STATUS = '7') AS HUIZHI, /*回执质检是否通过*/
       			TCM.APPLY_CODE, /*投保单号*/
       			TCM.SUBINPUT_TYPE, /*出单标识*/
       			TCM.CONFIRM_WAY, /*移动平台客户确认方式*/
       			TIL1.BUSI_PROD_CODE, /*险种代码*/
       			TO_CHAR(TCM.DRQ_FLAG) DRQ_FLAG, /*双录标识*/
       			TCM.POLICY_TYPE, /*保单类型*/
       			TO_CHAR(TPH1.CUSTOMER_CERT_TYPE) AS PH_CUSTOMER_CERT_TYPE, /*投保人证件类型*/
       			TPH1.CUSTOMER_CERTI_CODE AS APPNT_ID_NUMBER, /*投保人证件号码*/
       			TO_CHAR(TPH1.CUSTOMER_ID) AS PH_CUSTOMER_ID, /*投保人客户ID*/
       			TPH1.CUSTOMER_ID_CODE AS PH_CUSTOMER_ID_CODE, /*投保人身份证号码*/
       			TO_CHAR(TPH1.CUSTOMER_BIRTHDAY,'yyyy-MM-dd') AS APPNT_BIRTHDAY, /*投保人出生日期*/
       			TO_CHAR(TPH1.CUSTOMER_GENDER) AS APPNT_SEX, /*投保人性别*/
       			TPH1.MOBILE_TEL AS APPNT_MOBILE, /*投保人手机号*/
       			TPH1.EMAIL,/*投保人邮箱*/
       			TCM.jointly_insured_type AS JOINTLY_INSURED_TYPE, 
       			TO_CHAR(TIL1.RELATION_TO_PH) AS APPNT_AND_INSURED_RELATION, /*投保人与被保人关系*/
       			TPH1.CUSTOMER_NAME AS PH_CUSTOMER_NAME, /*投保人客户姓名*/
       			TPH1.ADDRESS,
       			TO_CHAR(TPH1.CUST_CERT_STAR_DATE,'yyyy-MM-dd') AS APPNT_CERT_STAR_DATE,/*投保人证件有效起期*/
           	    TO_CHAR(TPH1.CUST_CERT_END_DATE,'yyyy-MM-dd') AS APPNT_CERT_END_DATE,/*投保人证件有效止期*/
           	    TPH1.COUNTRY_CODE AS APPNT_COUNTRY,/*投保人国籍*/
       			(SELECT T.PRODUCT_NAME_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT T
         			WHERE T.PRODUCT_CODE_SYS = TIL1.BUSI_PROD_CODE) PRODUCT_NAME_SYS, /*主险名称*/
       			TIL1.BUSI_PROD_CODE AS PRODUCT_CODE_SYS, /*主险代码*/
       			TCM.APPLY_DATE, /*投保日期*/
        		TO_CHAR(TIL1.CUSTOMER_CERT_TYPE) AS INSURED_ID_TYPE, /*被保人证件类型*/
        		
       			/*需求 #122752-保单查询接口-按身份证号查询+按投保单号或保单号查询 变更----start */
				TIL1.CUSTOMER_CERTI_CODE AS INSURED_ID_NUMBER, /*第一被保人证件号码*/
				TO_CHAR(TIL1.CUSTOMER_ID) AS IN_CUSTOMER_ID, /*第一被保人客户ID*/
				TIL1.CUSTOMER_ID_CODE AS IN_CUSTOMER_ID_CODE, /*第一被保人客户号码*/
				TIL1.CUSTOMER_NAME AS IN_CUSTOMER_NAME, /*第一被保人客户姓名*/
				TIL1.COUNTRY_CODE AS INSURED_COUNTRY,/*被保人国籍*/
						       
				TO_CHAR(TIL2.OLD_CUSTOMER_ID) AS IN_CUSTOMER_ID_SEC, /*第二被保人客户号*/
				TIL2.CUSTOMER_ID_CODE AS IN_CUSTOMER_ID_CODE_SEC, /*第二被保人身份证号码*/
				TIL2.CUSTOMER_NAME AS IN_CUSTOMER_NAME_SEC, /*第二被保人客户姓名*/
				TO_CHAR(TIL2.CUSTOMER_CERT_TYPE) AS INSURED_ID_TYPE_SEC, /*第二被保人证件类型*/ 
				TIL2.CUSTOMER_CERTI_CODE AS INSURED_ID_NUMBER_SEC, /*第二被保人证件号码*/
				TIL2.COUNTRY_CODE AS SECOND_INSURED_COUNTRY,/*第二被保人国籍*/
				TO_CHAR(TIL2.RELATION_TO_PH) AS APPNT_AND_INSURED_RELATION_SEC, /*投保人与第二被保人关系*/
				/*需求 #122752-保单查询接口-按身份证号查询+按投保单号或保单号查询 变更----end */
				
       			(SELECT LIST_ID FROM DEV_NB.T_NB_QT_DEFECT T WHERE T.TASK_ID =
               		(SELECT MAX(A.TASK_ID) FROM DEV_NB.T_NB_QT_TASK A WHERE A.APPLY_CODE = TCM.APPLY_CODE)) LIST_ID, /*质检缺失资料*/
       			TCM.MEDIA_TYPE, /*保单的发送形式*/
       			TCM.SUBMIT_CHANNEL, /*递交渠道*/
       			(SELECT T.MEDIA_TYPE FROM DEV_PAS.T_CONTRACT_MASTER T WHERE T.POLICY_CODE = TCM.POLICY_CODE)MEDIA_TYPE1,
       			(SELECT T.SUBMIT_CHANNEL FROM DEV_PAS.T_CONTRACT_MASTER T WHERE T.POLICY_CODE = TCM.POLICY_CODE)SUBMIT_CHANNEL1,
       			TCM.PROPOSAL_STATUS, /*投保单状态*/
       			(CASE WHEN TCM.SUBMIT_CHANNEL = '3' THEN '1'
         			  WHEN TCM.SUBMIT_CHANNEL = '5' AND (TCM.SUBINPUT_TYPE = '18' OR TCM.SUBINPUT_TYPE = '19') THEN '0'
         			  ELSE '2' END) AS IS_BPO_FLAG, /*是否为BPO出单标识0-新时代出单；1-BPO出单；2-非BPO和新时代出单；*/
       			TCM.ISSUE_DATE as issue_date, /*签单日期*/
       			TCM.SERVICE_BANK AS service_bank,/*银行*/
       				
     			(CASE WHEN TCM.POLICY_CODE <> TCM.RELATION_POLICY_CODE AND TCM.DOUBLE_MAINRISK_FLAG = '1' THEN
         				(SELECT TO_CHAR(MIN(PRINT_TIME),'yyyy-MM-dd') FROM (SELECT DECODE(PPA.PRINT_TYPE, '1', PPA.BPO_PRINT_DATE, PPA.PRINT_TIME) AS PRINT_TIME,
                         PPA.POLICY_CODE,
                         PPA.PRINT_TYPE
                    	FROM DEV_PAS.T_POLICY_PRINT PPA
                  		UNION
                  		SELECT DECODE(PPN.PRINT_TYPE, '1', PPN.BPO_PRINT_DATE, PPN.PRINT_TIME) AS PRINT_TIME,
                         PPN.POLICY_CODE,
                         PPN.PRINT_TYPE
                    	FROM DEV_NB.T_POLICY_PRINT PPN) A WHERE POLICY_CODE = TCM.RELATION_POLICY_CODE
             				AND PRINT_TIME IS NOT NULL
             				AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1'))
       				ELSE
     						(SELECT TO_CHAR(MIN(PRINT_TIME),'yyyy-MM-dd')  FROM (SELECT DECODE(PPA.PRINT_TYPE, '1', PPA.BPO_PRINT_DATE, PPA.PRINT_TIME) AS PRINT_TIME,
                        		PPA.POLICY_CODE,
                        		PPA.PRINT_TYPE
                   		FROM DEV_PAS.T_POLICY_PRINT PPA
                 			UNION
                 			SELECT DECODE(PPN.PRINT_TYPE, '1', PPN.BPO_PRINT_DATE, PPN.PRINT_TIME) AS PRINT_TIME,
                        		PPN.POLICY_CODE,
                        		PPN.PRINT_TYPE
                 			  FROM DEV_NB.T_POLICY_PRINT PPN) A WHERE POLICY_CODE = TCM.POLICY_CODE
            				AND PRINT_TIME IS NOT NULL AND (PRINT_TYPE = '0' OR PRINT_TYPE = '1')) END) AS print_date, /*保单打印日期*/
       			(SELECT UO.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG UO WHERE UO.ORGAN_CODE = TCM.ORGAN_CODE) AS E_CHECKCOM_CODE, /*保单管理机构*/
       			(SELECT TPA.ACCOUNT FROM DEV_NB.T_NB_PAYER_ACCOUNT TPA WHERE TPA.POLICY_ID = TCM.POLICY_ID) BANK_CARD_NUMBER, /*首期交费银行账号*/
       			(SELECT TPA.NEXT_ACCOUNT FROM DEV_NB.T_NB_PAYER_ACCOUNT TPA WHERE TPA.POLICY_ID = TCM.POLICY_ID) NEXTACCOUNT, /*续期交费银行账号*/       			
       			(CASE WHEN TCM.SUBMIT_CHANNEL = '3' THEN '1'
         			  WHEN TCM.SUBMIT_CHANNEL = '5' AND (TCM.SUBINPUT_TYPE = '18' OR TCM.SUBINPUT_TYPE = '19') THEN '0'
         			  WHEN TCM.SUBMIT_CHANNEL = '1' THEN '2' 
         			  WHEN TCM.SUBMIT_CHANNEL = '11' THEN '3' ELSE '' END) AS CONT_PLAT_FLAG, /*出单系统编码*/
         		(CASE WHEN TCM.POLICY_REINSURE_FLAG = '1' THEN '1'
         			  WHEN TCM.POLICY_REINSURE_FLAG = '2' OR TCM.POLICY_REINSURE_FLAG = '3' THEN '2'
         			  ELSE ''
         		 END) AS POLICY_REINSURE_FLAG, /*重投或转投标识，01-已重投；02-已转保*/
         		(CASE WHEN TCM.OLD_POLICY_CODE IS NOT NULL THEN (SELECT CA.SERVICE_TYPE FROM DEV_PAS.T_RENEW_CHANGE RC
                     INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE AC ON RC.CHANGE_ID=AC.CHANGE_ID AND RC.ACCEPT_ID=AC.ACCEPT_ID
                     INNER JOIN DEV_PAS.T_CS_APPLICATION CA ON AC.CHANGE_ID=CA.CHANGE_ID
                     INNER JOIN  DEV_PDS.t_Business_Product BP ON RC.New_Busi_Prd_Id=BP.BUSINESS_PRD_ID
                     WHERE RC.POLICY_CODE=TCM.OLD_POLICY_CODE 
                     AND RC.VALID_STATUS = '1' 
                     AND AC.ACCEPT_STATUS = '18' AND  RC.RENEW_CHANGE_TYPE='3' AND AC.SERVICE_CODE='RR' AND BP.Product_Code_Sys='00846000')
            ELSE '' END) AS RR_INFO /*保全转保信息*/
  			FROM DEV_NB.T_NB_CONTRACT_MASTER TCM
 			LEFT JOIN (SELECT *
               FROM (SELECT TCBP.PRODUCT_CODE AS BUSI_PROD_CODE,
                            TCBP.BUSI_ITEM_ID,
                            TCBP.MASTER_BUSI_ITEM_ID,
                            TIL.APPLY_CODE,
                            TIL.POLICY_CODE,
                            TIL.LIST_ID,
                            TIL.POLICY_ID,
                            TIL.RELATION_TO_PH,
                            CU.CUSTOMER_ID,
                            CU.CUSTOMER_NAME,
                            CU.CUSTOMER_BIRTHDAY,
                            CU.CUSTOMER_GENDER,
                            CU.CUSTOMER_CERT_TYPE,
                            CU.CUSTOMER_CERTI_CODE,
                            CU.CUSTOMER_ID_CODE,
                            CU.COUNTRY_CODE
                       FROM DEV_NB.T_NB_INSURED_LIST       TIL,
                            DEV_NB.T_CUSTOMER              CU,
                            DEV_NB.T_NB_BENEFIT_INSURED    TBI,
                            DEV_NB.T_NB_CONTRACT_BUSI_PROD TCBP
                      WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
                        AND TBI.INSURED_ID = TIL.LIST_ID
                        AND TBI.POLICY_ID = TIL.POLICY_ID
                        AND TBI.POLICY_ID = TCBP.POLICY_ID
                        AND TBI.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                        AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
                        AND TIL.ORDER_ID=1 ]]>
                        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TIL.POLICY_CODE = #{policy_code} ]]></if>
                        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TIL.APPLY_CODE = #{apply_code} ]]></if>
                        <![CDATA[
                      ) WHERE ROWNUM = 1) TIL1 ON TIL1.POLICY_ID = TCM.POLICY_ID
                      
                       /*需求 #122752-保单查询接口-按身份证号查询+按投保单号或保单号查询 变更----start */
			LEFT JOIN (SELECT *
               FROM (SELECT TIL.APPLY_CODE,
                            TIL.POLICY_CODE,
                            TIL.LIST_ID,
                            TIL.POLICY_ID,
                            TIL.RELATION_TO_PH,
                            CU.OLD_CUSTOMER_ID,
                            CU.CUSTOMER_NAME,
                            CU.CUSTOMER_BIRTHDAY,
                            CU.CUSTOMER_GENDER,
                            CU.CUSTOMER_CERT_TYPE,
                            CU.CUSTOMER_CERTI_CODE,
                            CU.CUSTOMER_ID_CODE,
                            CU.COUNTRY_CODE
                       FROM DEV_NB.T_NB_INSURED_LIST       TIL,
                            DEV_NB.T_CUSTOMER              CU
                      WHERE TIL.CUSTOMER_ID = CU.CUSTOMER_ID
                        and TIL.ORDER_ID=2  ]]>
                        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TIL.POLICY_CODE = #{policy_code} ]]></if>
                        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TIL.APPLY_CODE = #{apply_code} ]]></if>
                        <![CDATA[
                       ) WHERE ROWNUM = 1) TIL2 ON TIL2.POLICY_ID = TCM.POLICY_ID                    
            /*需求 #122752-保单查询接口-按身份证号查询+按投保单号或保单号查询 变更----end */
            
			 			LEFT JOIN (SELECT TA.MOBILE_TEL,
			       TPH.ADDRESS_ID,
			       TPH.POLICY_CODE,
			       TPH.POLICY_ID,
			       CU.CUSTOMER_ID,
			       CU.CUSTOMER_NAME,
			       CU.CUSTOMER_BIRTHDAY,
			       CU.CUSTOMER_GENDER,
			       CU.CUSTOMER_CERT_TYPE,
			       CU.CUSTOMER_CERTI_CODE,
			       CU.CUSTOMER_ID_CODE,
			       TA.EMAIL,
			        DS.NAME || DC.NAME || DD.NAME || TA.ADDRESS AS ADDRESS,
			       CU.CUST_CERT_STAR_DATE,
       			   CU.CUST_CERT_END_DATE,
      			   CU.COUNTRY_CODE
			  FROM DEV_NB.T_NB_POLICY_HOLDER TPH
			 INNER JOIN DEV_NB.T_CUSTOMER CU
			    ON TPH.CUSTOMER_ID = CU.CUSTOMER_ID
			 INNER JOIN DEV_NB.T_ADDRESS TA
			    ON TPH.ADDRESS_ID = TA.ADDRESS_ID
			   AND TPH.CUSTOMER_ID = TA.CUSTOMER_ID
			  LEFT JOIN DEV_PAS.T_DISTRICT DS
			    ON DS.CODE = TA.STATE
			  LEFT JOIN DEV_PAS.T_DISTRICT DC
			    ON DC.CODE = TA.CITY
			  LEFT JOIN DEV_PAS.T_DISTRICT DD
			    ON DD.CODE = TA.DISTRICT
			      WHERE 1 = 1 ]]>
                <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TPH.POLICY_CODE = #{policy_code} ]]></if>
                <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TPH.APPLY_CODE = #{apply_code} ]]></if>
                <![CDATA[
               	) TPH1 ON TPH1.POLICY_ID = TIL1.POLICY_ID AND TPH1.POLICY_ID = TCM.POLICY_ID
 				WHERE 1=1
   			AND TCM.ORGAN_CODE like #{organ_code} || '%' ]]>
   		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND TCM.POLICY_CODE = #{policy_code} ]]></if>
   		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND TCM.APPLY_CODE = #{apply_code} ]]></if>
   		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND EXISTS (SELECT 1
      		FROM DEV_NB.T_NB_CONTRACT_AGENT TCA WHERE TCA.POLICY_ID = TCM.POLICY_ID AND TCA.AGENT_CODE = #{agent_code}) ]]></if>
   </select>
	
   <!-- 受益人信息查询 -->
   <select id="queryContractBeneInfo" resultType="java.util.Map" parameterType="java.util.Map">
        <if test=" flag1 != null and flag1 != ''  ">
        <![CDATA[ SELECT TC.CUSTOMER_NAME, TO_CHAR(TC.CUSTOMER_GENDER) AS BENE_SEX,
        			TO_CHAR(TC.CUSTOMER_BIRTHDAY,'yyyy-MM-dd') BENE_BIRTHDAY,
        		    TO_CHAR(TB.DESIGNATION) AS INSURED_AND_BENE_RELATION,
        			TO_CHAR(TB.BENE_TYPE) AS BENE_TYPE,
					TO_CHAR(TB.CUSTOMER_CERT_TYPE) AS BENE_ID_TYPE,
					TC.CUSTOMER_CERTI_CODE BENE_ID_NO,
					TC.COUNTRY_CODE AS BENE_COUNTRY /*受益人国籍*/
				  FROM DEV_PAS.T_CONTRACT_BENE TB,DEV_PAS.T_CUSTOMER TC WHERE TB.CUSTOMER_ID=TC.CUSTOMER_ID 
                  AND TB.BUSI_ITEM_ID=#{busi_item_id} AND TB.POLICY_ID=#{policy_id} ]]></if>
		<if test=" flag2 != null and flag2 != ''  ">
        <![CDATA[ SELECT TC.CUSTOMER_NAME, TO_CHAR(TC.CUSTOMER_GENDER) AS BENE_SEX,
        			TO_CHAR(TC.CUSTOMER_BIRTHDAY,'yyyy-MM-dd') BENE_BIRTHDAY,
        			TO_CHAR(TB.DESIGNATION) AS INSURED_AND_BENE_RELATION,
        			TO_CHAR(TB.BENE_TYPE) AS BENE_TYPE,
					TO_CHAR(TB.CUSTOMER_CERT_TYPE) AS BENE_ID_TYPE,
					TC.CUSTOMER_CERTI_CODE BENE_ID_NO,
					TC.COUNTRY_CODE AS BENE_COUNTRY /*受益人国籍*/
        		  FROM DEV_NB.T_NB_CONTRACT_BENE TB,DEV_NB.T_CUSTOMER TC WHERE TB.CUSTOMER_ID=TC.CUSTOMER_ID 
                  AND TB.BUSI_ITEM_ID=#{busi_item_id} AND TB.POLICY_ID=#{policy_id} ]]></if>
	</select>	
	
	<!-- 根据投被保人身份证号查询保单   -->
	<select id="queryPolicyByIdNO" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[  
            SELECT TPH.POLICY_CODE,TCM.APPLY_DATE
               FROM DEV_PAS.T_POLICY_HOLDER TPH, DEV_PAS.T_CONTRACT_MASTER TCM,DEV_PAS.T_contract_agent TCA
              WHERE TCM.POLICY_ID = TPH.POLICY_ID
                AND TCA.POLICY_ID=TCM.POLICY_ID
                AND TCM.ORGAN_CODE like #{organ_code}||'%']]>
        <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TCA.AGENT_CODE = #{agent_code} ]]></if>        
        <![CDATA[   AND TPH.CUSTOMER_ID IN
                    (SELECT T.CUSTOMER_ID
                       FROM DEV_PAS.T_CUSTOMER T
                      WHERE 1=1 ]]>
        <if test=" customer_id_code != null and customer_id_code != ''  ">
        <![CDATA[ AND  (T.CUSTOMER_ID_CODE = #{customer_id_code} OR T.CUSTOMER_CERTI_CODE = #{customer_id_code})]]></if>
        <if test=" appnt_name != null and appnt_name != ''  "><![CDATA[ AND T.CUSTOMER_NAME = #{appnt_name} ]]></if>
        <if test=" cert_type != null and cert_type != ''  "><![CDATA[ AND T.CUSTOMER_CERT_TYPE = #{cert_type}]]></if>          
        <![CDATA[ ) ]]>
        <![CDATA[
             UNION
             SELECT TIL.POLICY_CODE,TCM.APPLY_DATE
               FROM DEV_PAS.T_INSURED_LIST TIL, DEV_PAS.T_CONTRACT_MASTER TCM,DEV_PAS.T_contract_agent TCA
              WHERE TCM.POLICY_ID = TIL.POLICY_ID
                AND TCA.POLICY_ID=TCM.POLICY_ID
                AND TCM.ORGAN_CODE like #{organ_code}||'%']]>
         <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TCA.AGENT_CODE = #{agent_code} ]]></if> 
         <![CDATA[  AND TIL.CUSTOMER_ID IN
                    (SELECT T.CUSTOMER_ID
                       FROM DEV_PAS.T_CUSTOMER T
                      WHERE 1=1 ]]>
         <if test=" customer_id_code != null and customer_id_code != ''  ">
        <![CDATA[ AND  (T.CUSTOMER_ID_CODE = #{customer_id_code} OR T.CUSTOMER_CERTI_CODE = #{customer_id_code})]]></if>
        <if test=" appnt_name != null and appnt_name != ''  "><![CDATA[ AND T.CUSTOMER_NAME = #{appnt_name} ]]></if>
        <if test=" cert_type != null and cert_type != ''  "><![CDATA[ AND T.CUSTOMER_CERT_TYPE = #{cert_type}]]></if>
	    <![CDATA[ ) ]]>
	</select>
	
	<!-- 根据投被保人身份证号查询投保单 -->
	<select id="queryApplyByIdNO" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[  
            SELECT TCM.APPLY_CODE,TCM.APPLY_DATE
               FROM DEV_NB.T_NB_POLICY_HOLDER TPH, DEV_NB.T_NB_CONTRACT_MASTER TCM,DEV_NB.T_NB_CONTRACT_AGENT TCA
              WHERE TCM.POLICY_ID = TPH.POLICY_ID
                AND TCA.POLICY_ID=TCM.POLICY_ID
                AND TCM.ORGAN_CODE like #{organ_code}||'%']]>
        <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TCA.AGENT_CODE = #{agent_code} ]]></if>        
        <![CDATA[   AND TPH.CUSTOMER_ID IN
                    (SELECT T.CUSTOMER_ID
                       FROM DEV_NB.T_CUSTOMER T
                       WHERE 1=1 ]]>
        <if test=" customer_id_code != null and customer_id_code != ''  "><![CDATA[ AND  T.CUSTOMER_ID_CODE = #{customer_id_code} ]]></if>
        <if test=" appnt_name != null and appnt_name != ''  "><![CDATA[ AND T.CUSTOMER_NAME = #{appnt_name}]]></if>
        <if test=" cert_type != null and cert_type != ''  "><![CDATA[ AND T.CUSTOMER_CERT_TYPE = #{cert_type}]]></if>         <![CDATA[ ) ]]>
         <![CDATA[
             UNION
             SELECT TCM.APPLY_CODE,TCM.APPLY_DATE
               FROM DEV_NB.T_NB_INSURED_LIST TIL, DEV_NB.T_NB_CONTRACT_MASTER TCM,DEV_NB.T_NB_CONTRACT_AGENT TCA
              WHERE TCM.POLICY_ID = TIL.POLICY_ID
                AND TCA.POLICY_ID=TCM.POLICY_ID
                AND TCM.ORGAN_CODE like #{organ_code}||'%']]>
         <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND TCA.AGENT_CODE = #{agent_code} ]]></if> 
         <![CDATA[  AND TIL.CUSTOMER_ID IN
                    (SELECT T.CUSTOMER_ID
                       FROM DEV_NB.T_CUSTOMER T
                      WHERE 1=1 ]]>
        <if test=" customer_id_code != null and customer_id_code != ''  "><![CDATA[ AND T.CUSTOMER_ID_CODE = #{customer_id_code} ]]></if>
        <if test=" appnt_name != null and appnt_name != ''  "><![CDATA[ AND T.CUSTOMER_NAME = #{appnt_name}]]></if>
        <if test=" cert_type != null and cert_type != ''  "><![CDATA[ AND T.CUSTOMER_CERT_TYPE = #{cert_type}]]></if>
	    <![CDATA[ ) ]]>
	</select>
	
	<!-- 查询保单或契约险种信息 -->
	<select id="queryPasOrNbBuisItemInf" resultType="java.util.Map" parameterType="java.util.Map">
        <if test=" pas != null and pas != ''  "><![CDATA[
           SELECT T.BUSI_PROD_CODE FROM DEV_PAS.T_CONTRACT_BUSI_PROD T WHERE 
           T.MASTER_BUSI_ITEM_ID=#{busi_item_id} AND T.POLICY_CODE=#{policy_code}
        ]]></if> 
        <if test=" nb != null and nb != ''  "><![CDATA[ 
           SELECT T.PRODUCT_CODE AS BUSI_PROD_CODE FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD T WHERE 
           T.MASTER_BUSI_ITEM_ID=#{busi_item_id} AND T.APPLY_CODE=#{apply_code}
        ]]></if> 
	</select>
	
	<!-- 按投保单号投保人查询体检资料 -->
	<select id="queryPhysicalDataByconph" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
			SELECT
			I.BILLCARD_NO,
			I.BILLCARD_CODE,
			I.BILLCARD_ID,
			I.PAGES
			FROM DEV_NB.T_CUSTOMER C
			INNER JOIN DEV_NB.T_NB_POLICY_HOLDER PH
			ON C.CUSTOMER_ID = PH.CUSTOMER_ID
			INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER CM
			ON PH.POLICY_ID = CM.POLICY_ID
			INNER JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
			ON CM.POLICY_CODE = CA.POLICY_CODE
			RIGHT JOIN DEV_NB.T_DOCUMENT V
			ON V.POLICY_ID = CM.POLICY_ID
			INNER JOIN DEV_NB.T_IMAGE_SCAN I
			ON V.DOCUMENT_NO = I.BILLCARD_NO
			WHERE CM.APPLY_CODE = #{policy_code} AND C.CUSTOMER_ID = #{customer_id}
        ]]>
	</select>
	
	<!-- 按保单号投保人查询保单信息 -->
	<select id="queryContractMasterBycon" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
			SELECT
			C.CUSTOMER_ID c,
			C.CUSTOMER_NAME,
			CM.POLICY_CODE,
			PH.CUSTOMER_ID ph,
			(SELECT C.CUSTOMER_NAME cn FROM DEV_NB.T_CUSTOMER C WHERE C.CUSTOMER_ID = PH.CUSTOMER_ID) cn,
			CM.ORGAN_CODE,
			CA.AGENT_CODE,
			(SELECT A.AGENT_NAME FROM DEV_PAS.T_AGENT A WHERE A.AGENT_CODE = CA.AGENT_CODE) an,
			FROM DEV_NB.T_CUSTOMER C
			INNER JOIN DEV_NB.T_NB_POLICY_HOLDER PH
			ON C.CUSTOMER_ID = PH.CUSTOMER_ID
			INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER CM
			ON PH.POLICY_ID = CM.POLICY_ID
			INNER JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
			ON CM.POLICY_CODE = CA.POLICY_CODE
			RIGHT JOIN DEV_NB.T_DOCUMENT V
			ON V.POLICY_ID = CM.POLICY_ID
			INNER JOIN DEV_NB.T_IMAGE_SCAN I
			ON V.DOCUMENT_NO = I.BILLCARD_NO
			WHERE CM.POLICY_CODE = #{policy_code} AND C.CUSTOMER_ID = #{customer_id}
        ]]>
	</select>
	
	<!-- 按保单号被保人查询保单信息 -->
	<select id="queryContractMasterBycon2" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
			SELECT
			C.CUSTOMER_ID c,
			C.CUSTOMER_NAME,
			CM.POLICY_CODE,
			PH.CUSTOMER_ID ph,
			(SELECT C.CUSTOMER_NAME cn FROM DEV_NB.T_CUSTOMER C WHERE C.CUSTOMER_ID = PH.CUSTOMER_ID) cn,
			CM.ORGAN_CODE,
			CA.AGENT_CODE,
			(SELECT A.AGENT_NAME FROM DEV_PAS.T_AGENT A WHERE A.AGENT_CODE = CA.AGENT_CODE) an,
			FROM DEV_NB.T_CUSTOMER C
			INNER JOIN DEV_NB.T_NB_INSURED_LIST PH
			ON C.CUSTOMER_ID = PH.CUSTOMER_ID
			INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER CM
			ON PH.POLICY_ID = CM.POLICY_ID
			INNER JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
			ON CM.POLICY_CODE = CA.POLICY_CODE
			RIGHT JOIN DEV_NB.T_DOCUMENT V
			ON V.POLICY_ID = CM.POLICY_ID
			INNER JOIN DEV_NB.T_IMAGE_SCAN I
			ON V.DOCUMENT_NO = I.BILLCARD_NO
			WHERE CM.POLICY_CODE = #{policy_code} AND C.CUSTOMER_ID = #{customer_id}
        ]]>
	</select>
	
	
	<!-- 按客户号查询客户信息 -->
	<select id="findCustomerBycusid" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	SELECT C.CUSTOMER_ID,C.CUSTOMER_NAME FROM DEV_NB.T_CUSTOMER C WHERE C.CUSTOMER_ID = #{customer_id}
        ]]>
	</select>
	<!-- 按客户号查询投保人信息 -->
	<select id="findPolicyHolderBycusid" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	SELECT PH.APPLY_CODE FROM DEV_NB.T_NB_POLICY_HOLDER PH WHERE PH.CUSTOMER_ID = #{customer_id}
        ]]>
	</select>
	<!-- 按投保单号查询保单信息 -->
	<select id="findContractByappcode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	SELECT CM.APPLY_CODE,
			CM.POLICY_CODE,
			CM.ORGAN_CODE,
			PH.CUSTOMER_ID,
			C.CUSTOMER_NAME,
			CA.AGENT_CODE, C.OLD_CUSTOMER_ID,
			(SELECT A.AGENT_NAME FROM DEV_PAS.T_AGENT A WHERE A.AGENT_CODE = CA.AGENT_CODE) AGENT_NAME
			FROM DEV_NB.T_NB_CONTRACT_MASTER CM
			LEFT JOIN DEV_NB.T_NB_POLICY_HOLDER PH
			ON CM.POLICY_ID = PH.POLICY_ID
			LEFT JOIN DEV_NB.T_CUSTOMER C
			ON PH.CUSTOMER_ID = C.CUSTOMER_ID
			LEFT JOIN DEV_NB.T_NB_CONTRACT_AGENT CA
			ON CA.POLICY_ID = PH.POLICY_ID
			WHERE CM.APPLY_CODE = #{apply_code}
        ]]>
	</select>
	<!-- 按客户号查询被保人信息 -->
	<select id="findInsuredListBycusid" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	SELECT IL.APPLY_CODE FROM DEV_NB.T_NB_INSURED_LIST IL WHERE IL.CUSTOMER_ID = #{customer_id}
        ]]>
	</select>
	<!-- 按保单号查询影像信息 -->
	<select id="findPhysicalDataByconph" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	SELECT
			I.BILLCARD_NO,
			I.BILLCARD_CODE,
			I.BILLCARD_ID,
			I.PAGES
			FROM DEV_NB.T_NB_CONTRACT_MASTER CM
			RIGHT JOIN DEV_NB.T_DOCUMENT V
			ON V.POLICY_ID = CM.POLICY_ID
			RIGHT JOIN DEV_NB.T_IMAGE_SCAN I
			ON V.DOCUMENT_NO = I.BILLCARD_NO
			WHERE V.TEMPLATE_CODE = 'UWS_00003'
			AND CM.APPLY_CODE = #{apply_code,jdbcType=VARCHAR}
        ]]>
	</select>
	
		<!--电话中心保单信息查询 -->
	<select id="QRY_INTEGRAL_telephoneCenterQueryContractMasterInfo" resultType="java.util.Map" parameterType="java.util.Map" >
		 <![CDATA[  
				select TCM.Policy_Id as Policy_Id,
				       TCM.POLICY_CODE as Contno,
				       case
				         when (select pa.account_type
                         		from DEV_PAS.t_policy_account pa,dev_pas.t_policy_account_stream ast
                          	   where pa.account_id=ast.account_id
                          		 and pa.policy_id = TCM.POLICY_ID
                          		 and pa.account_type = '4'
                          		 and ast.regular_repay='0'
                          		 and rownum = '1') = 4 then
				          1
				         else
				          0
				       end as LoanStateCode,
				       TCM.ORGAN_CODE AS Managecom,
				       (SELECT t2.organ_name
				          FROM DEV_PAS.T_UDMP_ORG t2
				         where t2.organ_code = TCM.ORGAN_CODE) as ManagecomName,
				       (select t3.Group_Name
				          from DEV_PAS.T_CONTRACT_PRDGROUP_MAIN t3
				         where t3.apply_code = TCM.Apply_Code) as ContPlanName,
						(select t3.group_code
				          from DEV_PAS.T_CONTRACT_PRDGROUP_MAIN t3
				         where t3.apply_code = TCM.Apply_Code) as ContPlanCode,
				       TCM.LIABILITY_STATE as ContState,
				       TCM.EXPIRY_DATE as EndDate,
				       (select agent_code
                   from (select a.agent_code
                           from DEV_PAS.T_contract_agent a
                          where  a.policy_code = (select t1.policy_code
                          from DEV_PAS.T_CONTRACT_MASTER t1  where  
                t1.policy_code =#{policy_code}
 ) order by a.AGENT_START_DATE )where  rownum=1) as Sale_Agent_Code,
				       (select agent_code
                   from (select a.agent_code
                           from DEV_PAS.T_contract_agent a
                          where a.AGENT_END_DATE is null AND  a.policy_code = (select t1.policy_code
                          from DEV_PAS.T_CONTRACT_MASTER t1  where rownum=1  
          ]]>          
        <if test="policy_code != null and policy_code != ''">
				 <![CDATA[   AND t1.policy_code = #{policy_code} ]]>
		</if>
		<if test="apply_code != null and apply_code != ''">
				 <![CDATA[   AND t1.apply_code = #{apply_code} ]]>
		</if>
         <![CDATA[                    	
                         ) order by a.list_id desc)
                  where rownum = 1)agent_code
				  from DEV_PAS.T_CONTRACT_MASTER TCM
				 WHERE rownum <1000
		 ]]>
		 <if test="policy_code != null and policy_code != ''">
				 <![CDATA[   AND TCM.POLICY_CODE = #{policy_code} ]]>
		</if>
		
		 <if test="apply_code != null and apply_code != ''">
				 <![CDATA[   AND TCM.APPLY_CODE = #{apply_code} ]]>
		</if>
	</select>
	
	<!-- 按索引查询操作 -->
	<select id="QRY_INTEGRAL_findBySalesOrganCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			select d.state, d.parent_code, d.organ_level_code, 
			to_char(d.sales_organ_code) as sales_organ_code, d.admin_organ_code,
			(select c.sales_organ_name from dev_pas.t_sales_organ c
			where c.sales_organ_code = (select b.parent_code
			from dev_pas.t_sales_organ b where b.sales_organ_code =
			(select a.parent_code from dev_pas.t_sales_organ a
			where a.sales_organ_code = d.sales_organ_code)))
			||(select b.sales_organ_name from dev_pas.t_sales_organ b
			where b.sales_organ_code = (select a.parent_code
			from dev_pas.t_sales_organ a
			where a.sales_organ_code = d.sales_organ_code))
			||(select a.sales_organ_name from dev_pas.t_sales_organ a 
			where a.sales_organ_code = d.sales_organ_code) sales_organ_name
			from dev_pas.t_sales_organ d where 1=1
		]]>
		<if test=" sales_organ_code != null and sales_organ_code != '' ">
			<![CDATA[ and d.sales_organ_code = #{sales_organ_code} ]]>
		</if>
		<![CDATA[ order by d.sales_organ_code   ]]>
	</select>
	
	<select id="findPolicyCodeByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT POLICY_ID,INSURED_FAMILY,APPLY_CODE,DECISION_CODE,
			APPOINT_VALIDATE,APPLY_DATE,POLICY_TYPE,BRANCH_ORGAN_CODE,
			ORGAN_CODE,VALIDATE_DATE,EXPIRY_DATE,INITIAL_PREM_DATE,
			LIABILITY_STATE,MONEY_CODE,SUBMISSION_DATE,SUBMIT_CHANNEL,
			CHANNEL_TYPE,LANG_CODE,SALE_COM_CODE,SERVICE_BANK,SERVICE_BANK_BRANCH,
			SERVICE_HANDLER,SERVICE_HANDLER_CODE,SERVICE_HANDLER_NAME,
			BILL_CHECKED,SALE_AGENT_CODE,SALE_AGENT_NAME,WINNING_START_FLAG,HIGH_SA_INDI,
			BIRTHDAY_POL_INDI,RISK_INDI,AGENT_ORG_ID,DIALECT_INDI,MANUAL_UW_INDI,MEDIA_TYPE,
			E_SERVICE_FLAG,CHANNEL_ID,POLICY_CODE,OPERATOR_USER_CODE,PROPOSAL_STATUS,
			PA_USER_CODE,PA_COMPLETE_TIME,SCAN_USER_CODE,SCAN_COMPLETE_TIME,UW_USER_CODE,
			UW_COMPLETE_TIME,ISSUE_USER_CODE,ISSUE_DATE,OVERDUE_TIME,AGENCY_CODE,POLICY_PWD,
			APL_PERMIT,EVENT_CODE,INPUT_DATE,INPUT_TYPE,SUBINPUT_TYPE,SALE_TYPE,CALL_TIME_LIST,
			BANK_AGENCY_FLAG,TRANSACTION_NO,OLD_POLICY_CODE,CONFIRM_WAY,
			POLICY_RETURN_MODE,RELATION_POLICY_CODE,TRANSFER_FLAG,PRINT_SPECIAL_FLAG,
			INSERT_TIME,INSERT_TIMESTAMP,UPDATE_BY,UPDATE_TIME,UPDATE_TIMESTAMP,
			POLICY_PER_ARRT_ORGAN,EMERGENCY_CONTACTS_NAME,EMERGENCY_CONTACTS_MOBILE,IS_SELF_INSURED,
			EMER_CON_RELATION_TO_PH,POLICY_PER_ARRT_ORGAN_NAME,IDCARD_OCR_FLAG,BANKCARD_OCR_FLAG
			FROM DEV_NB.T_NB_CONTRACT_MASTER A WHERE A.APPLY_CODE = #{apply_code}
		]]>
	</select>
	
	<select id="findcspolicychangeaccepttime" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			select a.service_code,a.accept_time
			from dev_pas.t_cs_accept_change a, dev_pas.t_cs_policy_change b
			where a.accept_id = b.accept_id
			and a.service_code = b.service_code
			and a.accept_status = '18'
			and a.service_code = #{service_code}
			and b.policy_id = #{policy_id}
		]]>
	</select>
	<!--通过保单号查询保单下的附加险,并且这个附加险是849,状态是有效的 -->
	<select id="PA_findAllSubBusiProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[ 
		SELECT CBP.POLICY_CODE, CBP.BUSI_PROD_CODE
		      FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		     WHERE 1 = 1]]>
		  <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND CBP.POLICY_CODE = #{policy_code} ]]></if>
		   <![CDATA[ 
		    AND CBP.BUSI_PROD_CODE = '00849000'
	        AND CBP.MASTER_BUSI_ITEM_ID IS NOT NULL
	        AND CBP.LIABILITY_STATE = 1
		]]>
	</select>
	
	<!--通过保单号查询保单下的主险是否理赔终止 -->
	<select id="PA_findMasterProductByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[ 
		SELECT CBP.BUSI_ITEM_ID, /*险种id*/
	           CBP.BUSI_PROD_CODE, /*险种代码*/
	           CBP.MASTER_BUSI_ITEM_ID, /*主险id*/
	           CBP.LIABILITY_STATE, /*险种效力状态*/
	           CBP.END_CAUSE, /*险种终止原因*/
	           CBP.POLICY_CODE
	      FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
	     WHERE 1 = 1]]>
		  <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND CBP.POLICY_CODE = #{policy_code} ]]></if>
		   <![CDATA[ 
		    AND CBP.MASTER_BUSI_ITEM_ID IS NULL
       		AND CBP.END_CAUSE = '02'
	]]>
	</select>
	<!-- 工单INC-20210127-0056修改增加  -->
	<select id="LpPremItem_findPolicyCodeByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT POLICY_ID,POLICY_CODE,APPLY_CODE,APPLY_DATE
			FROM DEV_NB.T_NB_CONTRACT_MASTER A WHERE 1=1 
		]]>
		 <if test=" apply_code != null and apply_code != '' ">
			AND A.APPLY_CODE = #{apply_code}
		</if>
		<if test=" policy_code != null and policy_code != '' ">
			AND A.POLICY_CODE = #{policy_code}
		</if>
		<![CDATA[ 
		AND NOT EXISTS (
		SELECT POLICY_ID,POLICY_CODE,APPLY_CODE,APPLY_DATE
			FROM DEV_PAS.T_CONTRACT_MASTER T WHERE 1=1
		]]>
			<if test=" apply_code != null and apply_code != '' ">
			AND T.APPLY_CODE = #{apply_code}
		</if>
		<if test=" policy_code != null and policy_code != '' ">
			AND T.POLICY_CODE = #{policy_code}
		</if>	
		)
		
		<![CDATA[
			union
			SELECT POLICY_ID,POLICY_CODE,APPLY_CODE,APPLY_DATE
			FROM DEV_PAS.T_CONTRACT_MASTER A WHERE 1=1
			]]>
		 <if test=" apply_code != null and apply_code != '' ">
			AND A.APPLY_CODE = #{apply_code}
		</if>
		<if test=" policy_code != null and policy_code != '' ">
			AND A.POLICY_CODE = #{policy_code}
		</if>		
	</select>
	<!--通过保单号查询保单下的主险是否理赔终止 -->
	<select id="findRrInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[ 
		select tnp.print_type from 
		dev_pas.t_contract_master tcm,
         DEV_NB.t_policy_print tnp
		 where 
		 tcm.policy_code = tnp.policy_code
		 and tcm.policy_code = #{policy_code}
	]]>
	</select>
	<select id="qry_findPrintDetailInfo" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[ 
		SELECT 
       T.APPLY_CODE,
       T.POLICY_CODE
  FROM DEV_NB.T_POLICY_PRINT_DETAIL T
 WHERE T.POLICY_CODE = #{policy_code}
	]]>
	</select>
		<!--通过保单号和险种代码查询保单下的险种信息,并且这个险种代码是818 -->
	<select id="PA_findAllBusiProductByPolicyCodeAndBRC" resultType="java.util.Map" parameterType="java.util.Map" >
	<![CDATA[ 
		SELECT CBP.POLICY_CODE, CBP.BUSI_PROD_CODE,CBP.busi_item_id
		      FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		     WHERE 1 = 1
		     ]]>
		  <if test=" policy_code != null and policy_code != '' "><![CDATA[ AND CBP.POLICY_CODE = #{policy_code} ]]></if>
		   <![CDATA[ 
		    AND CBP.BUSI_PROD_CODE = '00818000'
		]]>
	</select>
	
		<!-- 查询理赔条数，理赔已结案，理赔金不为空为条件 -->
	<select id="PA_findCLMActualPayTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1)
				  FROM DEV_CLM.T_CLAIM_LIAB A, DEV_CLM.T_CLAIM_CASE B
				 WHERE A.CASE_ID = B.CASE_ID
				   AND B.CASE_STATUS = 80
				   AND A.LIAB_CONCLUSION != 5
				   AND A.ACTUAL_PAY !=0  ]]>
		<include refid="PA_contractMasterWhereCondition" />
	</select>
	
	<select id="NB_findCustomerByIdCode" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	SELECT C.CUSTOMER_ID, C.CUSTOMER_NAME, C.OLD_CUSTOMER_ID FROM DEV_PAS.T_CUSTOMER C WHERE rownum = 1
        ]]>
        
        <if test="customer_id_code != null and customer_id_code != ''">
		      <![CDATA[ AND C.CUSTOMER_ID_CODE = #{customer_id_code} ]]>
		</if>

		<if test="customer_certi_code != null and customer_certi_code != ''">
	      <![CDATA[ AND C.CUSTOMER_NAME = #{customer_name} AND C.CUSTOMER_BIRTHDAY = #{customer_birthday}
	        AND C.CUSTOMER_GENDER = #{customer_gender} AND C.CUSTOMER_CERT_TYPE = #{customer_cert_type} 
	        AND C.CUSTOMER_CERTI_CODE = #{customer_certi_code}]]>
		</if>
	</select>
	<!-- 共同参保人保单信息查询 -->
	<select id="qry_queryRelationPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[  
		      SELECT ncm.winning_start_flag,/* 是否参加预录入*/
		       njip.main_apply_code,/*主参保人投保单号*/
		       njip.Product_Code,/*共同参保险种代码 */
		       (SELECT a.customer_name FROM dev_nb.t_customer a where a.customer_id = MAIN_INSURED_ID) as insured_name,/* 主投保单共同参保险种被保人姓名 */
		       (SELECT a.customer_gender FROM dev_nb.t_customer a where a.customer_id = MAIN_INSURED_ID) as insured_gender,/* 主投保单共同参保险种被保人性别 */
		       njip.RELATION_TO_MAIN_INSURED,/* 与主投保单共同参保险种被保人关系 */
		       njip.CUSTOMER_NAME, /* 共同参保客户姓名 */
		       njip.CUSTOMER_BIRTHDAY, /* 共同参保客户出生日期 */
		       njip.customer_gender,/* 共同参保客户性别 */
		       njip.customer_cert_type,/* 共同参保客户证件类型 */
		       njip.customer_certi_code /* 共同参保客户证件号码 */
		  FROM dev_nb.T_NB_CONTRACT_MASTER ncm
		 inner join dev_nb.t_nb_policy_holder ph
		    on ph.apply_code = ncm.apply_code
		 inner join dev_nb.t_customer c
		    on c.customer_id = ph.customer_id
		 inner join dev_nb.T_NB_JOINTLY_INSURED_POLICY njip
		    on njip.main_apply_code = ncm.apply_code
		 where ncm.JOINTLY_INSURED_TYPE = '1'
		   and ncm.proposal_status IN ('05', '06', '07', '09','52','22','31','32','42','43','51')
		   and ncm.apply_date =  #{apply_date}
		   and c.customer_name =  #{appnt_name}
		   and c.customer_gender =  #{appnt_sex}
		   and c.customer_birthday =  #{appnt_birthday}
		   and c.customer_cert_type =  #{appnt_id_type}
		   and c.customer_certi_code =  #{appnt_id_no}
      ]]>
       <if test="reinsure_flag != null and reinsure_flag != ''">
		      <![CDATA[and ncm.policy_reinsure_flag =  #{reinsure_flag}]]>
		</if>
		<if test="reinsure_flag == null or reinsure_flag == ''">
			<![CDATA[and ncm.policy_reinsure_flag is null]]>
		</if>
	</select>
	
	<!-- 证件类型校验 -->
	<select id="QRY_findCertType" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        	SELECT T.CODE,T.TYPE FROM DEV_NB.T_CERTI_TYPE T WHERE 1 = 1
        ]]>
        <if test="code != null and code != ''">
		      <![CDATA[ AND T.CODE = #{code} ]]>
		</if>
	</select>
	<!-- 查询个险保单信息状态主表`-->
	<select id="PA_findContractMasterStatus" resultType="java.util.Map"
			parameterType="java.util.Map">
		 <![CDATA[
		SELECT A.POLICY_CODE, A.APPLY_CODE, A.LIABILITY_STATE
		FROM DEV_PAS.T_CONTRACT_MASTER A
		WHERE  A.APPLY_CODE = #{apply_code}
		]]>
	</select>
	<!-- 查询个险保单信息状态契约主表-->
	<select id="PA_findContractMasterStatusByNB" resultType="java.util.Map"
			parameterType="java.util.Map">
		 <![CDATA[
		SELECT A.POLICY_CODE, A.APPLY_CODE, A.LIABILITY_STATE
		FROM DEV_NB.T_NB_CONTRACT_MASTER A
		WHERE  A.APPLY_CODE = #{apply_code}
		]]>
	</select>
	<!-- 查询银代保单信息(保单详情查询接口)-->
	<select id="PA_findYinDaiContractMasterByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			SELECT 
			  TCM.ORGAN_CODE,TCM.POLICY_ID,TCM.POLICY_CODE,TCM.APPLY_CODE
            FROM 
              DEV_PAS.T_CONTRACT_MASTER TCM
            WHERE 1=1 
              AND TCM.SUBMIT_CHANNEL = '1'
              AND (TCM.END_CAUSE != '80' or  TCM.END_CAUSE is null)
              AND TCM.POLICY_CODE  = #{policy_code}  
		 ]]>
	</select>
	<!-- 根据保单号查询主险下的身故受益人信息 -->
	<select id="PA_queryYindaiBenefitContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		 <![CDATA[ 
			SELECT 
			  (SELECT COUNT(1)
               FROM DEV_PAS.T_CONTRACT_BENE TCB
               WHERE TCB.POLICY_CODE = TCM.POLICY_CODE
               AND TCB.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
               AND TCB.BENE_TYPE = 1) BENE_COUNT
            FROM 
               DEV_PAS.T_CONTRACT_MASTER TCM, DEV_PAS.T_CONTRACT_BUSI_PROD TCBP
            WHERE 1 = 1
               AND TCM.POLICY_CODE = TCBP.POLICY_CODE
               AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
               AND TCM.POLICY_CODE  = #{policy_code}  
		 ]]>
	</select>
</mapper>
