<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwPolicyDaoImpl">

	<sql id="uwPolicyWhereCondition">
		<if test=" apply_level_code != null and apply_level_code != ''  "><![CDATA[ AND A.APPLY_LEVEL_CODE = #{apply_level_code} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" uw_policy_id  != null "><![CDATA[ AND A.UW_POLICY_ID = #{uw_policy_id} ]]></if>
		<if test=" clm_type_list != null and clm_type_list != ''  "><![CDATA[ AND A.CLM_TYPE_LIST = #{clm_type_list} ]]></if>
		<if test=" uw_level_code != null and uw_level_code != ''  "><![CDATA[ AND A.UW_LEVEL_CODE = #{uw_level_code} ]]></if>
		<if test=" uw_status != null and uw_status != ''  "><![CDATA[ AND A.UW_STATUS = #{uw_status} ]]></if>
		<if test=" uw_finish_time  != null  and  uw_finish_time  != ''  "><![CDATA[ AND A.UW_FINISH_TIME = #{uw_finish_time} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" appoint_validate_indi  != null "><![CDATA[ AND A.APPOINT_VALIDATE_INDI = #{appoint_validate_indi} ]]></if>
		<if test=" appoint_validate  != null  and  appoint_validate  != ''  "><![CDATA[ AND A.APPOINT_VALIDATE = #{appoint_validate} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" ri_falg  != null "><![CDATA[ AND A.RI_FALG = #{ri_falg} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" uw_source_type != null and uw_source_type != ''  "><![CDATA[ AND A.UW_SOURCE_TYPE = #{uw_source_type} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" uw_user_id  != null "><![CDATA[ AND A.UW_USER_ID = #{uw_user_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" policy_decision  != null "><![CDATA[ AND A.POLICY_DECISION = #{policy_decision} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryUwPolicyByUwPolicyIdCondition">
		<if test=" uw_policy_id  != null "><![CDATA[ AND A.UW_POLICY_ID = #{uw_policy_id} ]]></if>
	</sql>	
  <!-- 保全核保做出核保决定 根据核保id查询该受理下的所有保单 2015年9月8日 start -->
  <sql id="queryUwPolicyByCSUWIDCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.uw_id = #{uw_id} ]]></if>
  </sql>
  
  <sql id="queryUwPolicyByCSSourceTypeCondition">
		<if test=" uw_source_type  != null "><![CDATA[ AND A.uw_source_type = #{uw_source_type} ]]></if>
  </sql>
  <!-- 保全核保做出核保决定 根据核保id查询该受理下的所有保单 2015年9月8日 end -->

<!-- 添加操作 -->
	<insert id="addUwPolicy" useGeneratedKeys="false" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="uw_policy_id"> SELECT  DEV_UW.S_UW_POLICY__UW_POLICY_ID.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO DEV_UW.T_UW_POLICY(
				UW_POLICY_ID,UW_ID,UW_LEVEL_CODE,
				UW_STATUS, UW_FINISH_TIME,APPLY_CODE, INSERT_TIMESTAMP, ORGAN_CODE, UPDATE_BY, 
				POLICY_CHG_ID, POLICY_ID, HOLDER_ID, 
				APPLY_LEVEL_CODE, INSERT_TIME, 
				CLM_TYPE_LIST, UPDATE_TIME, APPOINT_VALIDATE_INDI, APPOINT_VALIDATE, 
				RI_FALG, POLICY_CODE,  UW_SOURCE_TYPE, UPDATE_TIMESTAMP, INSERT_BY,uw_status_detail, ENDORSE_CODE ) 
			VALUES (
				#{uw_policy_id, jdbcType=NUMERIC},#{uw_id, jdbcType=NUMERIC},#{uw_level_code, jdbcType=VARCHAR}  
				, #{uw_status, jdbcType=VARCHAR} , #{uw_finish_time, jdbcType=DATE} ,#{apply_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				,#{policy_chg_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{holder_id, jdbcType=NUMERIC} 
				,#{apply_level_code, jdbcType=VARCHAR} , SYSDATE 
				, #{clm_type_list, jdbcType=VARCHAR} , SYSDATE ,  #{appoint_validate_indi, jdbcType=NUMERIC} , #{appoint_validate, jdbcType=DATE} 
				, #{ri_falg, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} ,  #{uw_source_type, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC}
				, #{uw_status_detail, jdbcType=VARCHAR}, #{endorse_code, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteUwPolicy" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_UW.T_UW_POLICY WHERE UW_POLICY_ID = #{uw_policy_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateUwPolicy" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_POLICY ]]>
		<set>
		<trim suffixOverrides=",">
			APPLY_LEVEL_CODE = #{apply_level_code, jdbcType=VARCHAR} ,
		    UW_ID = #{uw_id, jdbcType=NUMERIC} ,
			CLM_TYPE_LIST = #{clm_type_list, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			UW_LEVEL_CODE = #{uw_level_code, jdbcType=VARCHAR} ,
			UW_STATUS = #{uw_status, jdbcType=VARCHAR} ,
		    UW_FINISH_TIME = #{uw_finish_time, jdbcType=DATE} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    APPOINT_VALIDATE_INDI = #{appoint_validate_indi, jdbcType=NUMERIC} ,
		    APPOINT_VALIDATE = #{appoint_validate, jdbcType=DATE} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    RI_FALG = #{ri_falg, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UW_SOURCE_TYPE = #{uw_source_type, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_CHG_ID = #{policy_chg_id, jdbcType=NUMERIC} ,
		    UW_USER_ID = #{uw_user_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    HOLDER_ID = #{holder_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE UW_POLICY_ID = #{uw_policy_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findUwPolicyByUwPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_LEVEL_CODE, A.UW_ID, A.UW_POLICY_ID, A.CLM_TYPE_LIST, A.UW_LEVEL_CODE, M.UW_SUBMIT_TIME, 
			A.UW_STATUS, A.UW_FINISH_TIME, A.APPLY_CODE, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.ORGAN_CODE, 
			A.RI_FALG, A.POLICY_CODE, A.UW_SOURCE_TYPE, A.POLICY_CHG_ID,A.UW_STATUS_DETAIL,
			A.UW_USER_ID, A.POLICY_ID, (SELECT CUSTOMER_ID FROM DEV_UW.T_POLICY_HOLDER WHERE UW_ID= A.UW_ID AND ROWNUM =1) HOLDER_ID,
			A.POLICY_DECISION,M.SERVICE_CODE,M.BIZ_CODE FROM DEV_UW.T_UW_POLICY A ,DEV_UW.T_UW_MASTER M WHERE A.UW_ID = M.UW_ID  ]]>
		<include refid="queryUwPolicyByUwPolicyIdCondition" />
		<!-- 保全核保做出核保决定 根据核保id查询该受理下的所有保单 2015年9月8日 start -->
		<include refid="queryUwPolicyByCSUWIDCondition" />
		<include refid="queryUwPolicyByCSSourceTypeCondition" />
		<include refid="UW_queryUwPolicyByPolicyCodeCondition" />
		<!-- 保全核保做出核保决定 根据核保id查询该受理下的所有保单 2015年9月8日 end -->
		<![CDATA[ ORDER BY A.UW_POLICY_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapUwPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_LEVEL_CODE, A.UW_ID, A.UW_POLICY_ID, A.CLM_TYPE_LIST, A.UW_LEVEL_CODE, 
			A.UW_STATUS, A.UW_FINISH_TIME, A.APPLY_CODE, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.ORGAN_CODE, 
			A.RI_FALG, A.POLICY_CODE, A.UW_SOURCE_TYPE, A.POLICY_CHG_ID, 
			A.UW_USER_ID, A.POLICY_ID, A.HOLDER_ID FROM DEV_UW.T_UW_POLICY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_POLICY_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllUwPolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_LEVEL_CODE, A.UW_ID, A.UW_POLICY_ID, A.CLM_TYPE_LIST, A.UW_LEVEL_CODE, 
			A.UW_STATUS, A.UW_FINISH_TIME, A.APPLY_CODE, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.ORGAN_CODE, 
			A.RI_FALG, A.POLICY_CODE, A.UW_SOURCE_TYPE, A.POLICY_CHG_ID, 
			A.UW_USER_ID, A.POLICY_ID, A.HOLDER_ID FROM DEV_UW.T_UW_POLICY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_POLICY_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="UW_findUwPolicyTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_UW_POLICY A WHERE 1 = 1  ]]>
		<include refid="uwPolicyWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryUwPolicyForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.APPLY_LEVEL_CODE, B.UW_ID, B.UW_POLICY_ID, B.CLM_TYPE_LIST, B.UW_LEVEL_CODE, 
			B.UW_STATUS, B.UW_FINISH_TIME, B.APPLY_CODE, B.APPOINT_VALIDATE_INDI, B.APPOINT_VALIDATE, B.ORGAN_CODE, 
			B.RI_FALG, B.POLICY_CODE, B.UW_SOURCE_TYPE, B.POLICY_CHG_ID, 
			B.UW_USER_ID, B.POLICY_ID, B.HOLDER_ID FROM (
					SELECT ROWNUM RN, A.APPLY_LEVEL_CODE, A.UW_ID, A.UW_POLICY_ID, A.CLM_TYPE_LIST, A.UW_LEVEL_CODE, 
			A.UW_STATUS, A.UW_FINISH_TIME, A.APPLY_CODE, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.ORGAN_CODE, 
			A.RI_FALG, A.POLICY_CODE, A.UW_SOURCE_TYPE, A.POLICY_CHG_ID, 
			A.UW_USER_ID, A.POLICY_ID, A.HOLDER_ID FROM DEV_UW.T_UW_POLICY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.UW_POLICY_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!--拷贝一-->
	<select id="UW_findUwMiddleQtCriteriaResult" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.PREVIOUS_UW_TIME, A.PREVIOUS_UW_USER, A.UW_SUBMIT_TIME, A.UW_LEVEL_CODE, A.CASE_NO, 
			      A.UW_STATUS, A.UW_FINISH_TIME, A.RI_TEL_FALG, A.APPLY_CODE, A.ORGAN_CODE, 
			      A.UW_USER_CODE, A.APPLY_DECISION, A.CONCLUSION_CS, A.UW_OVER_INDI, A.POLICY_CHG_ID, A.POLICY_ID, A.HOLDER_ID, 
			      A.UW_AMEND_INDI, A.DOC_SEND_TIME, A.APPLY_LEVEL_CODE, A.DOC_SEND_INDI, A.UW_CANCEL_CAUSE, A.UW_DECISION, 
			      A.CLM_TYPE_LIST, A.UW_ESCA_INDI, A.ACCEPT_CODE, A.CASE_ID, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, 
			      A.RI_FALG, A.POLICY_CODE, A.UW_ESCA_USER, A.UW_REVIEW_INDI, A.UW_SOURCE_TYPE, 
			      A.CS_SERVICE 
		          FROM DEV_UW.T_UW_POLICY A
					   where not exists (select 1 from DEV_UW.T_UW_QT_TASK t2 where A.uw_id = t2.uw_id and A.POLICY_ID = t2.POLICY_ID)
					   	 ]]>
		<if test=" uw_time_end  != null  and  uw_time_end  != ''  "><![CDATA[ AND a.uw_finish_time < #{uw_time_end} ]]></if>
		<if test=" uw_time_start  != null  and  uw_time_start  != ''  "><![CDATA[ AND a.uw_finish_time > #{uw_time_start} ]]></if>
		<!-- and a.uw_finish_time > ${uw_time_start} and a.uw_finish_time < ${uw_time_end} -->
		<if test=" uw_source_type != null and uw_source_type != ''  "><![CDATA[ AND a.uw_source_type = #{uw_source_type} ]]></if>
		<if test=" uw_decision != null and uw_decision != ''  "><![CDATA[ AND a.uw_decision = #{uw_decision} ]]></if>
		<if test=" uw_user_id  != null "><![CDATA[ AND a.uw_user_code in #{uw_user_id} ]]></if>
		<if test=" uw_level != null and uw_level != ''  "><![CDATA[ AND a.uw_level_code = #{uw_level} ]]></if>
		<if test=" clm_type != null and clm_type != ''  "><![CDATA[ AND a.clm_type_list = #{clm_type} ]]></if>
		<if test=" cs_service != null and cs_service != ''  "><![CDATA[ AND a.cs_service = #{cs_service} ]]></if>
		<!-- <if test=" busi_prd_type != null and busi_prd_type != '' "><![CDATA[ 
			AND a.busi_prd_type = #{busi_prd_type} ]]></if> -->
		<if test=" notice_type != null and notice_type != ''  ">
						  <![CDATA[  
					     	and
						         (case
						           when exists (select 1 from DEV_UW.T_PENOTICE where uw_id = a.uw_id) then 1
						         end || case
						           when exists (select 1 from DEV_UW.T_ASKFORINFO where uw_id = a.uw_id) then
						            ',3'
						         end ) like '%${notice_type}%' ]]>
		</if>
	</select>
	
	<!--拷贝二-->
	<select id="UW_findUwPolicyForQtByUwId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT   A.UW_ID,
					       A.UW_USER_ID,
					       A.POLICY_CODE,
					       A.UW_SOURCE_TYPE,
					       A.POLICY_DECISION,
					       A.CS_SERVICE,
					       A.UW_LEVEL_CODE,
					       A.CLM_TYPE_LIST,
					       A.APPLY_CODE,
					       A.POLICY_ID,
					       A.POLICY_CODE,
					        case
					         when exists (select 1 from DEV_UW.T_PENOTICE where uw_id = A.uw_id) then
					          1
					       end || case
					         when exists (select 1 from DEV_UW.T_ASKFORINFO where uw_id = A.uw_id) then
					          ',3'
					       end as NOTICE_TYPE
					  FROM DEV_UW.T_UW_POLICY A WHERE 1=1   ]]>
		<include refid="UW_queryUwPolicyByUwIdCondition" />
		<![CDATA[ ORDER BY A.UW_ID            ]]>
	</select>
	<!-- 查询核保任务与保单组合信息 -->
	<select id="UW_findUwMasterPolicyForQtByUwId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT   A.UW_ID,
		                   B.SERVICE_CODE,
					       A.UW_USER_ID,
					       A.POLICY_CODE,
					       A.POLICY_DECISION,
					       A.UW_SOURCE_TYPE,
					       B.UW_DECISION,
					       A.UW_LEVEL_CODE,
					       A.CLM_TYPE_LIST,
					       A.APPLY_CODE,
					       A.POLICY_ID,
					       to_char(QT.QT_STATUS) QT_STATUS,
					        case
					         when exists (select 1 from DEV_UW.T_PENOTICE where uw_id = A.uw_id) then
					          1
					       end || case
					         when exists (select 1 from DEV_UW.T_ASKFORINFO where uw_id = A.uw_id) then
					          ',3'
					       end as NOTICE_TYPE
					  FROM DEV_UW.T_UW_MASTER B LEFT JOIN DEV_UW.T_UW_POLICY A ON B.UW_ID=A.UW_ID 
					  LEFT JOIN DEV_UW.T_UW_QT_TASK QT ON
					  B.UW_ID=QT.UW_ID and A.UW_SOURCE_TYPE=QT.UW_SOURCE_TYPE
					  WHERE 1=1   ]]>
		<include refid="UW_queryUwPolicyByUwIdCondition" />
		<![CDATA[ ORDER BY A.UW_ID            ]]>
	</select>
	
	<!--拷贝三-->
	<!-- 新契约投保规则查询保单生效日期 -->
	<select id="findAppointValidateByUwId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID,A.UW_LEVEL_CODE, A.UW_STATUS, A.UW_FINISH_TIME,A.APPLY_CODE, A.ORGAN_CODE, 
			A.UW_USER_ID,A.POLICY_CHG_ID, A.POLICY_ID, A.HOLDER_ID,A.APPLY_LEVEL_CODE,A.Policy_Decision, 
			A.CLM_TYPE_LIST,A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE,A.RI_FALG, A.POLICY_CODE,A.UW_SOURCE_TYPE 
			FROM DEV_UW.T_UW_POLICY A WHERE 1 = 1  AND A.UW_ID = ${uw_id} ]]>
			<include refid="UW_queryUwPolicyByPolicyCodeCondition" /> <!-- 保全核保下发核保结论 下发条件承保核保结论时需要支持契约和保全 2015年9月16日 -->
		<![CDATA[ ORDER BY A.UW_ID            ]]>
	</select>
	
	<!--拷贝四-->
	<!-- 更新核保决定 -->
	<update id="updateUwPolicyDecision" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_POLICY ]]>
		<set>
			<trim suffixOverrides=",">
				<if test=" uw_submit_time  != null "><![CDATA[  UW_SUBMIT_TIME = #{uw_submit_time, jdbcType=DATE} , ]]></if>
				<if test=" uw_status  != null "><![CDATA[  UW_STATUS = #{uw_status, jdbcType=VARCHAR} , ]]></if>
				<if test=" uw_status_detail != null and uw_status_detail != ''  "> uw_status_detail = #{uw_status_detail} , </if>
				<if test=" uw_finish_time  != null  and  uw_finish_time  != ''  "><![CDATA[ UW_FINISH_TIME = SYSDATE , ]]></if>
				UPDATE_BY = #{update_by, jdbcType=NUMERIC},
				UW_USER_ID = #{update_by, jdbcType=NUMERIC} ,
				UPDATE_TIME = SYSDATE ,
				<if test=" appoint_validate_indi  != null "><![CDATA[  APPOINT_VALIDATE_INDI = #{appoint_validate_indi, jdbcType=NUMERIC} ,]]></if>
				<if test=" appoint_validate  != null "><![CDATA[  APPOINT_VALIDATE = #{appoint_validate, jdbcType=DATE} , ]]></if>
				<if test=" policy_decision  != null "><![CDATA[  policy_decision = #{policy_decision, jdbcType=VARCHAR} , ]]></if>
				<!-- 	<if test=" two_way_input_flag  != null "><![CDATA[  two_way_input_flag = #{two_way_input_flag, jdbcType=VARCHAR} , ]]></if> -->
			</trim>
		</set>
		<![CDATA[ WHERE UW_ID                   = #{uw_id                  } ]]>
		<!-- add by xuhp 保全核保下发核保结论 根据险种层核保结论推算保单层核保结论时，需要支持契约和保全  2015年9月17日-->
		<include refid="UWQueryUwPolicyByCSPolicyCodeCondition"/>
	</update>
	
	<!--拷贝五-->
	<!-- 根据保单号查询保单的的代理人和代理人级别 -->
	<select id="findAgentNameByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.AGENT_CODE,
       (SELECT C.SALES_ORGAN_NAME
          FROM DEV_UW.T_SALES_ORGAN C
         WHERE C.SALES_ORGAN_CODE = A.SALES_ORGAN_CODE) AS SALES_ORGAN_CODE,
       A.AGENT_NAME AS AGENT_NAME,
     nvl((select b.agent_level_desc from DEV_UW.T_AGENT_LEVEL b where b.agent_level_code = a.agent_level),'') AS AGENT_NAMELEVEL
  FROM DEV_PAS.T_AGENT A
 WHERE A.AGENT_CODE =(SELECT AGENT_CODE
                         FROM DEV_UW.T_CONTRACT_AGENT
                        WHERE POLICY_ID = #{policy_id}
                          and uw_id = #{uw_id} and rownum = 1) ]]>
		<![CDATA[ ORDER BY A.AGENT_CODE ]]>
	</select>
	
	<!--拷贝六-->
	<!--按uwId和policyId查询add by zhaoxs -->
	<select id="UW_findUwPolicyByUwIdAndPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID,A.UW_STATUS,A.UW_FINISH_TIME, A.APPLY_CODE, 
	  	  A.ORGAN_CODE, A.POLICY_DECISION, A.POLICY_CHG_ID, A.POLICY_ID, 
          A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.POLICY_CODE,A.RI_FALG,  A.UW_SOURCE_TYPE
          FROM DEV_UW.T_UW_POLICY A WHERE 1 = 1  ]]>
		<include refid="UW_queryUwPolicyByUwIdCondition" />
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<![CDATA[ ORDER BY A.UW_ID            ]]>
	</select>
	
	<!--拷贝⑦-->
	<select id="UW_findUwPolicyByUwIdss" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_LEVEL_CODE, A.UW_ID, A.UW_POLICY_ID, A.CLM_TYPE_LIST, A.UW_LEVEL_CODE, 
			A.UW_STATUS, A.UW_FINISH_TIME, A.APPLY_CODE, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.ORGAN_CODE, 
			A.RI_FALG, A.POLICY_CODE, A.UW_SOURCE_TYPE, A.POLICY_CHG_ID, 
			A.UW_USER_ID, A.POLICY_ID, A.HOLDER_ID,(select uw_status_detail from DEV_UW.T_UW_MASTER where uw_id = A.UW_ID) uw_status_detail,
			(select to_char(count(uw_id)) from DEV_UW.T_ASKFORINFO  where uw_id = A.UW_ID) document_No FROM DEV_UW.T_UW_POLICY A WHERE 1=1   ]]>
		<!-- <include refid="UW_queryUwPolicyByUwIdCondition" /> -->
		<!-- <include refid="请添加查询条件" /> -->
		<if test="uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} AND ROWNUM=1 ]]></if>
		<![CDATA[ ORDER BY A.UW_POLICY_ID ASC ]]>
	</select>
	
		<select id="UW_findMaxPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT aa.policy_code,(select uw_status_detail from DEV_UW.T_UW_MASTER where uw_id = aa.UW_ID) uw_status_detail  FROM DEV_UW.T_UW_POLICY aa where 1=1 ]]>
		<!-- <include refid="UW_queryUwPolicyByUwIdCondition" /> -->
		<!-- <include refid="请添加查询条件" /> -->
		<if test="uw_id  != null "><![CDATA[ AND aa.UW_ID = #{uw_id} ]]></if>
<!-- 		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND aa.POLICY_CODE = #{policy_code} ]]></if> -->
		<if test="(uw_id  != null) and ( policy_code != null and policy_code != '' )"><![CDATA[ and aa.uw_level_code=
 (SELECT max(aa.uw_level_code) FROM DEV_UW.T_UW_POLICY aa where aa.uw_id=#{uw_id}) and rownum=1 ]]></if>
		<![CDATA[ ORDER BY aa.UW_POLICY_ID ASC ]]>
	</select>
	
	
	<!--拷贝八-->
	<update id="UW_updateUwPolicyStatusByUwId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_POLICY ]]>
		<set>
			<trim suffixOverrides=",">
				UW_USER_ID = #{uw_user_id, jdbcType=NUMERIC} ,
				UW_LEVEL_CODE = #{uw_level_code,jdbcType=VARCHAR}
			</trim>
		</set>
		<![CDATA[ WHERE UW_ID = #{uw_id}  AND UW_POLICY_ID = #{uw_policy_id} ]]>
	</update>
	
	<!--拷贝九-->
	<!-- 查询管理机构 -->
	<select id="UW_findUwPolicyOrganCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.ORGAN_CODE,A.APPLY_CODE,a.policy_id,A.policy_code  FROM  DEV_UW.T_UW_POLICY  A WHERE 1 = 1  AND ROWNUM = 1]]>
		<include refid="UW_queryUwPolicyByUwIdCondition" />
		<!-- <if test="uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if> -->
		<![CDATA[ ORDER BY A.UW_ID                   ]]>
	</select>
	
	<!--拷贝十-->
	<select id="UW_findUwPolicyByUnionCondition" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT 
           A.UW_LEVEL_CODE,C.UW_STATUS_DETAIL,
           C.UW_SUBMIT_TIME  AS UW_FINISH_TIME,
           A.APPLY_CODE, A.ORGAN_CODE, C.UW_USER_ID, 
           A.POLICY_CHG_ID, A.POLICY_ID,A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.POLICY_CODE, 
           A.RI_FALG,  A.UW_SOURCE_TYPE,B.AGENT_CODE,A.UW_ID,(SELECT AGENT_NAME FROM DEV_PAS.T_AGENT WHERE AGENT_CODE = B.AGENT_CODE) AGENT_NAME,
           DECODE((SELECT COUNT(*) FROM DEV_UW.T_PENOTICE D WHERE A.APPLY_CODE = D.APPLY_CODE),'0','0','1') AS  IS_PENOTICE,
           DECODE((SELECT COUNT(*) FROM  DEV_UW.T_SURVIVAL_INVESTIGATION C WHERE A.APPLY_CODE = C.APPLY_CODE),'0','0','1') AS IS_RREPORT
           FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_CONTRACT_AGENT B, DEV_UW.T_UW_MASTER C WHERE 1 = 1 AND A.UW_ID = B.UW_ID AND A.UW_ID=C.UW_ID
           AND C.UW_STATUS_DETAIL = '0110'  AND C.UW_SOURCE_TYPE = '1' 
       ]]>
		<include refid="UW_queryUwPolicyByApplyCodeCondition" />
		<if test=" uw_submit_time  != null "><![CDATA[ AND C.UW_SUBMIT_TIME like TO_DATE(#{uw_submit_time},'YYYY-MM-DD')]]></if>
		<if test=" organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" uw_level_code != null and uw_level_code != ''"><![CDATA[ AND A.UW_LEVEL_CODE = #{uw_level_code} ]]></if>
		<if test="agent_code != null and agent_code != ''"><![CDATA[AND B.AGENT_CODE = #{agent_code}]]></if>
		<if test="is_rreport != null and is_rreport == 0"><![CDATA[AND  A.APPLY_CODE NOT IN (SELECT A.APPLY_CODE FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_SURVIVAL_INVESTIGATION C WHERE A.APPLY_CODE = C.APPLY_CODE)]]></if>
		<if test="is_rreport != null and is_rreport == 1"><![CDATA[AND A.APPLY_CODE  IN (SELECT A.APPLY_CODE FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_SURVIVAL_INVESTIGATION C WHERE A.APPLY_CODE = C.APPLY_CODE)]]></if>
		<if test="is_penotice != null and is_penotice == 0"><![CDATA[AND A.APPLY_CODE NOT IN (SELECT A.APPLY_CODE FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_PENOTICE D WHERE A.APPLY_CODE = D.APPLY_CODE)]]></if>
		<if test="is_penotice != null and is_penotice == 1"><![CDATA[AND A.APPLY_CODE IN (SELECT A.APPLY_CODE FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_PENOTICE D WHERE A.APPLY_CODE = D.APPLY_CODE)]]></if>
		<![CDATA[ORDER BY A.UW_ID]]>
	</select>
	
	<!--拷贝十一-->
	<!--分页查询 工作量查询SQL关联t_uw_policy表和t_uw_workload表 add by guanshun -->
	<select id="UW_findUwPolicyAndUwWorkloadPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   
				SELECT A.POLICY_CODE,A.APPLY_CODE,A.ACCEPT_CODE,A.CASE_NO, 
				    A.OPERATE_DATE, A.OPERATE_ITEM_CODE,A.OPERATE_ITEM_DESC,
				    (SELECT B.TYPE_NAME FROM DEV_UW.T_UW_SOURCE_TYPE B  WHERE B.UW_SOURCE_TYPE = A.UW_SOURCE_TYPE) AS UW_SOURCE_TYPE,
				    (SELECT C.SERVICE_NAME FROM DEV_UW.T_SERVICE C  WHERE C.SERVICE_CODE = A.Service_Code) AS Service_Code,
				    (SELECT D.Status_Detail_Name FROM DEV_UW.T_UW_STATUS_DETAIL D  WHERE D.Status_Detail_Code = A.Uw_Status_Detail) AS UW_STATUS,
				    (SELECT E.DECISION_DESC FROM DEV_UW.T_POLICY_DECISION E  WHERE E.DECISION_CODE = A.POLICY_DECISION) AS POLICY_DECISION
    			FROM  DEV_UW.T_UW_WORKLOAD   A   WHERE  A.OPERATE_DATE BETWEEN  #{operateDateStart}  AND  #{operateDateEnd} ]]>
		<include refid="UW_queryUwPolicyByUwIdCondition" />
		<![CDATA[ ORDER BY A.OPERATE_DATE                 ]]>
	</select>
	
	<!--拷贝十二-->
	<!--分页查询 查询保全清单(保单号) -->
	<select id="finCSDetailedListVOs" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   
			SELECT 
				C.BIZ_CODE AS  ACCEPT_CODE,
			    (SELECT SERVICE_NAME FROM DEV_UW.T_SERVICE WHERE SERVICE_CODE=C.SERVICE_CODE  ) AS CS_SERVICE,
			    TO_CHAR(C.BIZ_DATE,'YYYY-MM-DD') AS BIZ_DATE,
			    TO_CHAR(C.UW_SUBMIT_TIME,'YYYY-MM-DD') AS UW_SUBMIT_TIME,
			    A.POLICY_CODE,
			    (SELECT REAL_NAME FROM DEV_PAS.T_UDMP_USER WHERE USER_ID=A.UW_USER_ID) AS REAL_NAME,
			    (SELECT USER_NAME FROM DEV_PAS.T_UDMP_USER WHERE USER_ID=A.UW_USER_ID) AS USER_NAME,
			    (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION  WHERE DECISION_CODE= A.POLICY_DECISION )  AS UW_DECISION,
			        TO_CHAR(A.UW_FINISH_TIME,'YYYY-MM-DD') UW_FINISH_TIME,
			    ( select DOC_LIST_ID from DEV_UW.T_PENOTICE where policy_code= a.policy_code and rownum = 2 order by insert_time )  AS DOCUMENT_NO1,
			    ( select DOC_LIST_ID from DEV_UW.T_PENOTICE where policy_code= a.policy_code and rownum = 2 order by insert_time ) AS DOCUMENT_NO2,
			     DECODE(（SELECT DISTINCT(POSITIVE_INDI) FROM  DEV_UW.T_PENOTICE WHERE  APPLY_CODE=A.APPLY_CODE AND  POSITIVE_INDI=1),'1','是','否'）AS  POSITIVE_INDI,
			    (SELECT RREPORT_CODE FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE  RREPORT_ID=(SELECT MIN(RREPORT_ID) FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE APPLY_CODE=A.APPLY_CODE) ) AS RREPORT_CODE1,
			    (SELECT RREPORT_CODE FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE  RREPORT_ID=(SELECT MIN(RREPORT_ID) FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE APPLY_CODE=A.APPLY_CODE
			    AND RREPORT_ID!=(SELECT MIN(RREPORT_ID) FROM DEV_UW.T_SURVIVAL_INVESTIGATION WHERE APPLY_CODE=A.APPLY_CODE))) AS RREPORT_CODE2,
			    (SELECT STATUS_NAME FROM DEV_UW.T_UW_STATUS WHERE UW_STATUS = A.UW_STATUS) AS UW_STATUS,
			    DECODE(A.UW_STATUS,'05','是','否') AS EXCEED,
			    (SELECT PRODUCT_NAME_STD FROM DEV_UW.T_BUSINESS_PRODUCT  WHERE product_code_sys=(SELECT BUSI_PROD_CODE FROM DEV_UW.T_CONTRACT_BUSI_PROD WHERE  POLICY_CODE=A.POLICY_CODE and uw_id=a.uw_id AND ROWNUM=1  ）) AS PRODUCT_NAME_SYS1,
			    (SELECT PRODUCT_NAME_STD FROM DEV_UW.T_BUSINESS_PRODUCT  WHERE product_code_sys=(SELECT BUSI_PROD_CODE FROM DEV_UW.T_CONTRACT_BUSI_PROD WHERE   POLICY_CODE=A.POLICY_CODE and uw_id=a.uw_id AND ROWNUM=2  ）) AS PRODUCT_NAME_SYS2,
			    (SELECT PRODUCT_NAME_STD FROM DEV_UW.T_BUSINESS_PRODUCT  WHERE product_code_sys=(SELECT BUSI_PROD_CODE FROM DEV_UW.T_CONTRACT_BUSI_PROD WHERE  POLICY_CODE=A.POLICY_CODE and uw_id=a.uw_id AND ROWNUM=3  ）) AS PRODUCT_NAME_SYS3,
			    (SELECT PRODUCT_NAME_STD FROM DEV_UW.T_BUSINESS_PRODUCT  WHERE product_code_sys=(SELECT BUSI_PROD_CODE FROM DEV_UW.T_CONTRACT_BUSI_PROD WHERE  POLICY_CODE=A.POLICY_CODE and uw_id=a.uw_id AND ROWNUM=4  ）) AS PRODUCT_NAME_SYS4
			  FROM DEV_UW.T_UW_POLICY A,DEV_UW.T_CONTRACT_MASTER B,DEV_UW.T_UW_MASTER C WHERE A.UW_ID=B.UW_ID AND A.UW_ID=C.UW_ID AND A.UW_SOURCE_TYPE=2 ]]>
		<if test=" cs_service != null and cs_service != ''"><![CDATA[ AND C.SERVICE_CODE IN #{cs_service} ]]></if>
		<if test=" user_name != null and user_name != ''"><![CDATA[   AND A.UW_USER_ID=(select USER_ID from DEV_PAS.T_UDMP_USER where REAL_NAME=#{user_name}) ]]></if>
		<if test=" uw_finish_time != null and uw_finish_time != ''"><![CDATA[ AND C.UW_FINISH_TIME LIKE TO_DATE(#{uw_finish_time},'YYYY-MM-DD') ]]></if>
		<if test=" uw_submit_time != null and uw_submit_time != ''"><![CDATA[ AND C.UW_SUBMIT_TIME LIKE TO_DATE(#{uw_submit_time},'YYYY-MM-DD') ]]></if>
		<if test=" uw_decision != null and uw_decision != ''"><![CDATA[ AND A.POLICY_DECISION= #{uw_decision} ]]></if>
  <![CDATA[ ORDER BY C.UW_SUBMIT_TIME,A.UW_FINISH_TIME,C.SERVICE_CODE  ASC]]>
	</select>
	
	<!--拷贝三-->
	<!--分页查询保全核保清单(责任组) -->
	<select id="finCSDetailedListTeamVOs" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   
			SELECT 
				E.BIZ_CODE AS ACCEPT_CODE,
			    (SELECT SERVICE_NAME FROM DEV_UW.T_SERVICE WHERE SERVICE_CODE=E.SERVICE_CODE  ) AS CS_SERVICE,
			    TO_CHAR(E.UW_SUBMIT_TIME,'YYYY-MM-DD') AS UW_SUBMIT_TIME,
			    A.POLICY_CODE,
			    (SELECT REAL_NAME FROM DEV_PAS.T_UDMP_USER WHERE USER_ID=A.UW_USER_ID) AS REAL_NAME,
			    (SELECT USER_NAME FROM DEV_PAS.T_UDMP_USER WHERE USER_ID=A.UW_USER_ID) AS USER_NAME,
			    (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION  WHERE DECISION_CODE= A.POLICY_DECISION )  AS UW_DECISION,
			    TO_CHAR(E.UW_FINISH_TIME,'YYYY-MM-DD HH24:MI:SS')  UW_FINISH_TIME,
			    (SELECT PRODUCT_NAME FROM DEV_UW.T_PRODUCT_LIFE WHERE PRODUCT_ID=C.ITEM_ID) AS  ITEM_ID,
			    (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION  WHERE DECISION_CODE=C.DECISION_CODE ) AS DECISION_CODE ,
			    (SELECT PRODUCT_NAME_SYS FROM DEV_UW.T_BUSINESS_PRODUCT  WHERE BUSINESS_PRD_ID=D.BUSI_ITEM_ID ) AS  BUSI_ITEM_ID ,
			    (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION  WHERE DECISION_CODE=D.DECISION_CODE ) AS  BUSI_DECISION_CODE ,
			    (SELECT E.EM_VALUE FROM  DEV_UW.T_EXTRA_PREM E WHERE E.UW_ID=A.UW_ID)  AS  EM_VALUE
			 FROM DEV_UW.T_UW_POLICY A,DEV_UW.T_CONTRACT_MASTER B,DEV_UW.T_CONTRACT_PRODUCT C ,DEV_UW.T_CONTRACT_BUSI_PROD D,DEV_UW.T_UW_MASTER E WHERE A.UW_ID=B.UW_ID AND B.UW_ID=C.UW_ID  AND C.UW_ID=D.UW_ID AND A.UW_ID=E.UW_ID AND A.UW_SOURCE_TYPE=2
		 ]]>
		<if test=" cs_service != null and cs_service != ''"><![CDATA[ AND E.SERVICE_CODE IN #{cs_service} ]]></if>
		<if test=" uw_finish_time != null and uw_finish_time != ''"><![CDATA[ AND E.UW_FINISH_TIME LIKE TO_DATE(#{uw_finish_time},'YYYY-MM-DD') ]]></if>
		<if test=" uw_submit_time != null and uw_submit_time != ''"><![CDATA[ AND E.UW_SUBMIT_TIME LIKE TO_DATE(#{uw_submit_time},'YYYY-MM-DD') ]]></if>
  <![CDATA[ORDER BY E.UW_SUBMIT_TIME,A.UW_FINISH_TIME,E.SERVICE_CODE  ASC]]>
	</select>
	
	<!--查询CS项目 -->
	<select id="findCSService" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		   SELECT A.SERVICE_CODE AS CS_SERVICE,A.SERVICE_NAME AS ACCEPT_CODE FROM DEV_UW.T_SERVICE A 
			 ]]>
 	 <![CDATA[ ORDER BY A.SERVICE_CODE]]>
	</select>
	
	<!--拷贝十四-->
	<!--分页查询 新契约清单(责任组) -->
	<select id="finDetailedListVOs" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[   
			SELECT 
			    (SELECT ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG_REL F WHERE ORGAN_CODE= A.ORGAN_CODE) AS ORGAN_CODE ,
			    (SELECT TYPE_NAME FROM DEV_UW.T_CHANNEL_TYPE WHERE INDIVIDUAL_GROUP= A.CHANNEL_TYPE) AS CHANNEL_TYPE,
			    A.APPLY_CODE,A.POLICY_CODE,TO_CHAR(A.SUBMISSION_DATE,'YYYY-MM-DD') SUBMISSION_DATE,
			    TO_CHAR(B.UW_FINISH_TIME,'YYYY-MM-DD HH24:MI:SS') UW_FINISH_TIME,
			    (SELECT REAL_NAME FROM DEV_PAS.T_UDMP_USER WHERE USER_ID=B.UW_USER_ID) AS REAL_NAME,
			    (SELECT USER_NAME FROM DEV_PAS.T_UDMP_USER WHERE USER_ID=B.UW_USER_ID) AS USER_NAME,
			    (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION  WHERE DECISION_CODE=B.POLICY_DECISION ) AS UW_DECISION ,
			    (SELECT PRODUCT_NAME FROM DEV_UW.T_PRODUCT_LIFE WHERE PRODUCT_ID=C.ITEM_ID) AS  ITEM_ID,
			    C.AMOUNT,
			    (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION  WHERE DECISION_CODE=C.DECISION_CODE ) AS DECISION_CODE ,
			    (SELECT PRODUCT_NAME_SYS FROM DEV_UW.T_BUSINESS_PRODUCT  WHERE BUSINESS_PRD_ID=D.BUSI_ITEM_ID ) AS  BUSI_ITEM_ID ,
			    (SELECT DECISION_DESC FROM DEV_UW.T_POLICY_DECISION  WHERE DECISION_CODE=D.DECISION_CODE ) AS  BUSI_DECISION_CODE ,
			    (SELECT E.EM_VALUE FROM  DEV_UW.T_EXTRA_PREM E WHERE E.UW_ID=A.UW_ID)  AS  EM_VALUE
			  FROM DEV_UW.T_CONTRACT_MASTER A ,DEV_UW.T_UW_POLICY B ,DEV_UW.T_CONTRACT_PRODUCT C,DEV_UW.T_CONTRACT_BUSI_PROD D  WHERE A.UW_ID=B.UW_ID AND B.UW_ID=C.UW_ID AND C.UW_ID=D.UW_ID AND B.UW_SOURCE_TYPE=1 ]]>
		<if test=" organ_code != null and organ_code != ''"><![CDATA[ AND A.ORGAN_CODE like '${organ_code}%' ]]></if>
		<if test=" channel_type != null and channel_type != ''"><![CDATA[ AND A.CHANNEL_TYPE = ${channel_type} ]]></if>
		<if test=" user_name != null and user_name != ''"><![CDATA[  AND B.UW_USER_ID=(select USER_ID from DEV_PAS.T_UDMP_USER where REAL_NAME=#{user_name}) ]]></if>
		<if test=" uw_finish_time != null and uw_finish_time != ''"><![CDATA[ AND TO_CHAR(B.UW_FINISH_TIME,'YYYY-MM-DD HH24:MI:SS') LIKE  '${uw_finish_time}%' ]]></if>
		<if test=" submission_date != null and submission_date != ''"><![CDATA[ AND A.SUBMISSION_DATE LIKE TO_DATE(#{submission_date},'YYYY-MM-DD') ]]></if>
		<if test=" uw_decision != null and uw_decision != ''"><![CDATA[ AND B.POLICY_DECISION= #{uw_decision} ]]></if>
 	 <![CDATA[ order by A.ORGAN_CODE,A.CHANNEL_TYPE,B.UW_FINISH_TIME  ASC]]>
	</select>
	
	<!--拷贝十五-->
	<!-- 查询工作量结果列表的记录数 add by guanshun -->
	<select id="UW_findUwPolicyAndUwWorkloadTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[   SELECT COUNT(1)
				    FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_UW_WORKLOAD B
				    WHERE A.UW_ID = B.UW_ID AND B.OPERATE_DATE BETWEEN  #{operateDateStart}  AND  #{operateDateEnd} ]]>
		<include refid="UW_queryUwPolicyByUwIdCondition" />

		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!--拷贝十六-->
	<!-- 按索引生成的查询条件 -->
	<sql id="UW_queryUwPolicyByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" apply_code  != null "><![CDATA[ AND A.apply_code = #{apply_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="UW_queryUwPolicyByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="UW_queryUwPolicyByCaseNoCondition">
		<if test=" case_no != null and case_no != '' "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
	</sql>
	<sql id="UW_queryUwPolicyByAcceptNoCondition">
		<if test=" accept_no != null and accept_no != '' "><![CDATA[ AND A.ACCEPT_NO = #{accept_no} ]]></if>
	</sql>
	<sql id="UW_queryUwPolicyByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="UW_queryUwPolicyByPolicyCode2Condition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_code = B.POLICY_code AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	<sql id="UW_queryUwPolicyByUwSubmitTimeCondition">
		<if test=" uw_submit_time  != null "><![CDATA[ AND A.UW_SUBMIT_TIME = #{uw_submit_time} ]]></if>
	</sql>
	
	<!-- add by xuhp 保全核保下发核保结论 根据险种层核保结论推算保单层核保结论时，需要支持契约和保全  2015年9月17日-->
	<sql id="UWQueryUwPolicyByCSPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND POLICY_CODE = #{policy_code} ]]></if>
	</sql>
	
	<sql id="UW_queryUwPolicyByDocumentCondition">
		<if test=" document_No != null and document_No != '' "><![CDATA[ and a.apply_code = (select apply_code from  DEV_UW.t_uw_notice  where document_no = #{document_No})]]></if>
	</sql>
	
	<!--拷贝十七-->
	<select id="UW_findUwPolicyOrganName" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT 
			A.ORGAN_NAME, A.ORGAN_CODE FROM  DEV_PAS.T_UDMP_ORG  A WHERE 1 = 1  ]]>
		AND A.ORGAN_CODE=#{organ_code}

	</select>
	
	<!--拷贝十八-->
	<!-- 按索引查询操作 -->
	<select id="findUwPolicyByUwId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.POLICY_ID,C.ORGAN_CODE,A.APPLY_LEVEL_CODE, A.UW_ID, A.UW_POLICY_ID, A.CLM_TYPE_LIST, A.UW_LEVEL_CODE, 
			A.UW_STATUS,(SELECT UW_FINISH_TIME FROM  DEV_UW.T_UW_MASTER   WHERE UW_ID =A.UW_ID)  UW_FINISH_TIME,        
      		 A.APPLY_CODE, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, 
			A.RI_FALG, A.POLICY_CODE, A.UW_SOURCE_TYPE, A.POLICY_CHG_ID, B.CUSTOMER_ID,
			A.UW_USER_ID, A.HOLDER_ID,A.POLICY_DECISION FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_POLICY_HOLDER B, DEV_UW.T_CONTRACT_MASTER C  
	  		WHERE 1 = 1 AND A.UW_ID = B.UW_ID AND A.UW_ID = C.UW_ID  and b.POLICY_ID = c.POLICY_ID  AND A.POLICY_ID = C.POLICY_ID ]]>
		<include refid="UW_queryUwPolicyByUwIdCondition" />
		<include refid="UW_queryUwPolicyByPolicyCode2Condition" />
		<include refid="UW_queryUwPolicyByDocumentCondition" />
		<![CDATA[ ORDER BY A.UW_ID ]]>
	</select>
	<!-- 查询所有操作yinzj 保单层核保结论只有四种：标准、次标体、拒保、延期 UW_REVIEW_INDI是核保复议的标识，暂停1为申请过复议 -->
 <select id="UW_findAllUwPolicy" parameterType="java.util.Map" resultType="java.util.Map"> 
	<![CDATA[ select e.RN,
       E.POLICY_ID,
       e.POLICY_CODE,
       e.APPLY_CODE,
       e.APPLY_DATE,
       e.UW_ID,
       e.uw_finish_time as UW_SUBMIT_TIME,
       e.ORGAN_CODE,
      (select organ_name from DEV_PAS.T_UDMP_ORG tuo where tuo.organ_code =  e.ORGAN_CODE) ORGAN_NAME,
       e.UW_STATUS,
       e.policy_DECISION,
       e.customer_name,
       e.UW_LEVEL_CODE,
       e.UW_USER_ID
       from (SELECT ROWNUM RN,
       A.POLICY_ID,
       A.POLICY_CODE,
       A.APPLY_CODE,
       C.APPLY_DATE,
       A.UW_ID,
       B.uw_finish_time,
       A.ORGAN_CODE,
       A.UW_STATUS,
       A.policy_DECISION,
       d.customer_name,
       A.UW_LEVEL_CODE,
       a.UW_USER_ID
  FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_UW_MASTER B, DEV_UW.T_CONTRACT_MASTER c, DEV_UW.T_POLICY_HOLDER d
 WHERE A.UW_ID = B.UW_ID
   and A.UW_ID = c.uw_id
   AND a.policy_DECISION != 10
   AND (B.UW_REVIEW_INDI != 1 or B.UW_REVIEW_INDI is null)
   and (B.UW_REVIEW_INDI != 2 or B.UW_REVIEW_INDI is null)
   AND b.uw_status_detail = '0110'
   and a.uw_source_type = 1
   and d.policy_id = a.policy_id
   and a.uw_id = d.uw_id
   and #{userOrganCode_code} in
       (SELECT B.ORGAN_CODE
          FROM DEV_PAS.T_UDMP_ORG_REL B
         START WITH B.ORGAN_CODE =a.organ_code
        CONNECT BY PRIOR B.UPORGAN_CODE = B.ORGAN_CODE)
    and ROWNUM<=#{LESS_NUM}]]>
	 <if test=" organ_code != null and organ_code != '' ">
	<![CDATA[ AND A.ORGAN_CODE like #{organ_code}||'%']]>
	</if> 
	<if test=" apply_code != null and apply_code != '' ">
	<![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]>
	</if> 
	<if test=" customer_id != null and customer_id != '' ">
	<![CDATA[ AND D.CUSTOMER_ID = #{customer_id} ]]>
	</if>
	<if test=" customer_name != null and customer_name != '' ">
	<![CDATA[ AND D.CUSTOMER_NAME = #{customer_name} ]]>
	</if>  
	<if test=" uw_id != null and uw_id != '' ">
	<![CDATA[ AND A.UW_ID = #{uw_id} ]]>
	</if>
	<![CDATA[ order by B.uw_finish_time desc )e where RN>#{GREATER_NUM} ]]>
<!-- <include refid="请添加查询条件" /> -->
 
<!-- <![CDATA[ ORDER BY A.UW_ID ]]> -->
 </select>
  <select id="UW_findAllUwPolicyCount" parameterType="java.lang.Integer" resultType="java.lang.Integer"> 
	<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_UW_POLICY A,DEV_UW.T_UW_MASTER B ,DEV_UW.T_CONTRACT_MASTER c ,DEV_UW.T_POLICY_HOLDER d WHERE A.UW_ID=B.UW_ID and A.UW_ID=c.uw_id AND a.policy_DECISION != 10 AND (B.UW_REVIEW_INDI !=1 or B.UW_REVIEW_INDI is null)
         AND b.uw_status_detail = '0110'  and a.uw_source_type=1 and d.policy_id = a.policy_id and a.uw_id = d.uw_id  and #{userOrganCode_code} in
       (SELECT B.ORGAN_CODE
          FROM DEV_PAS.T_UDMP_ORG_REL B
         START WITH B.ORGAN_CODE =a.organ_code
        CONNECT BY PRIOR B.UPORGAN_CODE = B.ORGAN_CODE)]]>
	 <if test=" organ_code != null and organ_code != '' ">
	<![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]>
	</if> 
	<if test=" apply_code != null and apply_code != '' ">
	<![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]>
	</if> 
	<if test=" customer_id != null and customer_id != '' ">
	<![CDATA[ AND D.CUSTOMER_ID = #{customer_id} ]]>
	</if> 
	<if test=" uw_id != null and uw_id != '' ">
	<![CDATA[ AND A.UW_ID = #{uw_id} ]]>
	</if>
<!-- <include refid="请添加查询条件" /> -->
 
<!-- <![CDATA[ ORDER BY A.UW_ID ]]> -->
 </select>
 
 <!-- 根据organ_code查询出当前机构的所有的上级机构(包含当前机构)yinzj_wb -->
 <select id="UW_findAllUpOrganCodeByOrganCode" parameterType="java.util.Map" resultType="java.util.Map"> 
	<![CDATA[ SELECT B.ORGAN_CODE,B.ORGAN_NAME FROM DEV_PAS.T_UDMP_ORG_REL B START WITH B.ORGAN_CODE = #{organ_code, jdbcType=NUMERIC} 
			  CONNECT BY PRIOR B.UPORGAN_CODE = B.ORGAN_CODE ]]>
	<!-- <include refid="请添加查询条件" /> -->
	<!-- <![CDATA[ ORDER BY A.UW_ID ]]> -->
 </select>
 
 
	<select id="findUwPolicyByCsUwId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.APPLY_LEVEL_CODE, A.UW_ID, A.UW_POLICY_ID, A.CLM_TYPE_LIST, A.UW_LEVEL_CODE, 
			A.UW_STATUS,(SELECT UW_FINISH_TIME FROM  DEV_UW.T_UW_MASTER   WHERE UW_ID =A.UW_ID)  UW_FINISH_TIME, A.APPLY_CODE, A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE, A.ORGAN_CODE, 
			A.RI_FALG, A.POLICY_CODE, A.UW_SOURCE_TYPE, A.POLICY_CHG_ID, B.CUSTOMER_ID,
			A.UW_USER_ID, A.POLICY_ID, A.HOLDER_ID,A.POLICY_DECISION FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_POLICY_HOLDER B, DEV_UW.T_CONTRACT_MASTER C  
	  		WHERE 1 = 1 AND A.UW_ID = B.UW_ID AND A.UW_ID = C.UW_ID  AND A.POLICY_CODE = C.POLICY_CODE AND A.POLICY_CODE = B.POLICY_CODE]]>
		<include refid="UW_queryUwPolicyByUwIdCondition" />
		<![CDATA[ ORDER BY A.UW_ID ]]>
	</select>
	
 <delete id="UW_deleteUwPolicy" parameterType="java.util.Map" > 
	<![CDATA[ DELETE FROM DEV_UW.T_UW_POLICY WHERE UW_ID = #{uw_id} ]]>
 </delete>
 <!-- 根据保单Id修改保单状态  add by guanshun -->
	<update id="UW_updateUwPolicyStatusByPolicyId" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_POLICY                         ]]>
		<set>
		<trim suffixOverrides=",">
		     
			UW_STATUS = #{uw_status, jdbcType=VARCHAR} ,
			 
		</trim>
		</set>
		<![CDATA[ WHERE POLICY_ID            = #{policy_id           } ]]>
	</update>
	<select id="UW_PolicysByUwIDSSS" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID,A.UW_LEVEL_CODE, A.UW_STATUS, A.UW_FINISH_TIME,A.APPLY_CODE, A.ORGAN_CODE, 
			A.UW_USER_ID,A.POLICY_CHG_ID, A.POLICY_ID, A.HOLDER_ID,A.APPLY_LEVEL_CODE,A.Policy_Decision, 
			A.CLM_TYPE_LIST,A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE,A.RI_FALG, A.POLICY_CODE,A.UW_SOURCE_TYPE 
			FROM DEV_UW.T_UW_POLICY A WHERE 1 = 1 ]]>
			<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = ${uw_id}  ]]></if>
		<![CDATA[ ORDER BY A.UW_ID            ]]>
	</select>
	
	
	<!-- R06601001889 -->
	<select id="UW_findUwPolicyPOForESB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.APPLY_CODE, 
			A.UPDATE_BY,
	        A.UPDATE_TIME,
	        A.POLICY_DECISION,
			(SELECT B.DECISION_DESC FROM DEV_UW.T_POLICY_DECISION B WHERE A.POLICY_DECISION = B.DECISION_CODE) AS DECISION_DESC 
			FROM DEV_UW.T_UW_POLICY A 
			WHERE 1 = 1  ]]> 
			<if test=" APPLY_CODE  != null "><![CDATA[ AND A.APPLY_CODE = #{APPLY_CODE}  ]]></if>
		<![CDATA[ ORDER BY A.APPLY_CODE]]>
	</select>
	<!-- 根据保单号查询核保历史轨迹信息 -->
	<select id="findUwPolicyPOForESB" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.APPLY_CODE, 
			A.UPDATE_BY,
	        A.UPDATE_TIME,
	        A.POLICY_DECISION,
			(SELECT B.DECISION_DESC FROM DEV_UW.T_POLICY_DECISION B WHERE A.POLICY_DECISION = B.DECISION_CODE) AS DECISION_DESC 
			FROM DEV_UW.T_UW_POLICY A 
			WHERE 1 = 1  ]]> 
			<if test=" APPLY_CODE  != null "><![CDATA[ AND A.POLICY_CODE = #{APPLY_CODE}  ]]></if>
		<![CDATA[ ORDER BY A.APPLY_CODE]]>
	</select>
	
	<select id="findUWPolicyListBySpecifiedConditions" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID,A.UW_LEVEL_CODE, A.UW_STATUS, A.UW_FINISH_TIME,A.APPLY_CODE, A.ORGAN_CODE, 
			A.UW_USER_ID,A.POLICY_CHG_ID, A.POLICY_ID, A.HOLDER_ID,A.APPLY_LEVEL_CODE,A.POLICY_DECISION, 
			A.CLM_TYPE_LIST,A.APPOINT_VALIDATE_INDI, A.APPOINT_VALIDATE,A.RI_FALG, A.POLICY_CODE,A.UW_SOURCE_TYPE 
			FROM DEV_UW.T_UW_POLICY A WHERE 1 = 1 ]]>
			<include refid="UW_queryUwPolicyByUwIdCondition" />
		<![CDATA[ ORDER BY A.UW_ID ]]>
	</select>
	
	<!-- R06601001897-合同核保轨迹查询   投保单号   Add by yangbo_wb  2016-07-28  -->
	<select id="queryContUwTraceList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.ENDORSE_CODE, A.UW_USER_ID, A.UW_FINISH_TIME, A.POLICY_DECISION, B.DECISION_DESC
 				 FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_POLICY_DECISION B
				 WHERE A.POLICY_DECISION = B.DECISION_CODE AND A.APPLY_CODE = #{apply_code} ]]>
				 
				 <if test="endorse_code!=null and endorse_code!=''">
				  AND A.ENDORSE_CODE = #{endorse_code}
				 </if>
				
		<![CDATA[ ORDER BY A.UW_USER_ID ]]>
	</select>
	
	<!-- 保单号  -->
	<select id="queryApplyCodeTraceList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.APPLY_CODE, A.ENDORSE_CODE, A.UW_USER_ID, A.UW_FINISH_TIME, A.POLICY_DECISION, B.DECISION_DESC
 				 FROM DEV_UW.T_UW_POLICY A, DEV_UW.T_POLICY_DECISION B
				 WHERE A.POLICY_DECISION = B.DECISION_CODE AND A.POLICY_CODE = #{policy_code,jdbcType=VARCHAR} ]]>
		         <if test="endorse_code!=null and endorse_code!=''">
		           AND A.ENDORSE_CODE = #{endorse_code} 
		         </if>
		
		<![CDATA[ ORDER BY A.UW_USER_ID ]]>
	</select>
	<!--查询核保决定 集成接口 P00001001335按投保单号或保单号查询 接口使用 --> 
	<select id="findUwPolicyDecodeByapplicyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	
	select t.policy_decision from dev_uw.t_uw_Policy t where t.apply_code =#{apply_code} 
	
	and t.uw_source_type = '1' and t.policy_decision = '50'

	]]>
	</select>	
		
	
	<!-- 查询核保决定 -->
	<select id="UW_findPolicyDecisionByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.MULTI_MAINRISK_FLAG, A.POLICY_DECISION FROM DEV_UW.T_UW_POLICY A
	    	WHERE 1 = 1 AND A.UW_ID = #{uw_id}]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</select>
			
	<select id="findPolicyDecisionByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT uo.policy_decision
				  FROM DEV_CLM.t_claim_case cc,
				       DEV_CLM.t_uw_master  um,
				       DEV_CLM.t_uw_policy  uo
				 WHERE cc.case_no = um.biz_code
				   and um.uw_id = uo.uw_id
				   and cc.case_id = #{case_id}
				   and uo.policy_code = #{policy_code}
				 order by um.insert_time desc	]]>
	</select>
	
	<select id="findPolicyDecisionByCaseId1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT uo.policy_decision
				  FROM DEV_CLM.t_claim_case cc,
				       DEV_CLM.T_CLAIM_CASE CC1,
				       DEV_CLM.t_uw_master  um,
				       DEV_CLM.t_uw_policy  uo
				 WHERE cc.case_no = CC1.RELATED_NO
				   AND CC1.CASE_NO=UM.BIZ_CODE
				   and um.uw_id = uo.uw_id
				   and cc.case_id = #{case_id}
				   and uo.policy_code = #{policy_code}
				   AND UM.INSERT_TIME < CC.END_CASE_TIME
				 order by um.insert_time desc ]]>
	</select>
</mapper>
