<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.PreAuditMasterDaoImpl">
<!--
	<sql id="NB_preAuditMasterWhereCondition">
		<if test=" audit_organ_code != null and audit_organ_code != ''  "><![CDATA[ AND A.AUDIT_ORGAN_CODE = #{audit_organ_code} ]]></if>
		<if test=" audit_by  != null "><![CDATA[ AND A.AUDIT_BY = #{audit_by} ]]></if>
		<if test=" busi_prod_pack_code != null and busi_prod_pack_code != ''  "><![CDATA[ AND A.BUSI_PROD_PACK_CODE = #{busi_prod_pack_code} ]]></if>
		<if test=" high_sa_indi  != null "><![CDATA[ AND A.HIGH_SA_INDI = #{high_sa_indi} ]]></if>
		<if test=" agent_sincerity_level  != null "><![CDATA[ AND A.AGENT_SINCERITY_LEVEL = #{agent_sincerity_level} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" insert_timestamp != null and insert_timestamp != ''  "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" custom_level != null and custom_level != ''  "><![CDATA[ AND A.CUSTOM_LEVEL = #{custom_level} ]]></if>
		<if test=" manual_uw_indi  != null "><![CDATA[ AND A.MANUAL_UW_INDI = #{manual_uw_indi} ]]></if>
		<if test=" birthday_indi  != null "><![CDATA[ AND A.BIRTHDAY_INDI = #{birthday_indi} ]]></if>
		<if test=" audit_id  != null "><![CDATA[ AND A.AUDIT_ID = #{audit_id} ]]></if>
		<if test=" manual_uw_tip != null and manual_uw_tip != ''  "><![CDATA[ AND A.MANUAL_UW_TIP = #{manual_uw_tip} ]]></if>
		<if test=" update_timestamp != null and update_timestamp != ''  "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" submit_date  != null  and  submit_date  != ''  "><![CDATA[ AND A.SUBMIT_DATE = #{submit_date} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="NB_queryByAuditIdCondition">
		<if test=" audit_id  != null "><![CDATA[ AND A.AUDIT_ID = #{audit_id} ]]></if>
	</sql>	
	<sql id="NB_queryByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	<sql id="NB_queryByApplyCodeAndAuditConclusionCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="NB_queryByConclCodeAndAuditConclusionCondition">
		<if test=" conclusion_code != null "><![CDATA[ AND A.CONCLUSION_CODE = #{conclusion_code} ]]></if>
	</sql>
	<sql id="NB_queryByApplyCodeAndDateCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" start_date != null and start_date != '' "><![CDATA[ AND A.SUBMIT_DATE>= to_date(#{start_date},'YYYY-MM-DD') ]]></if>
		<if test=" end_date != null and end_date != '' "><![CDATA[ AND A.SUBMIT_DATE<= to_date(#{end_date},'YYYY-MM-DD') ]]></if>
	</sql>	
	

<!-- 根据投保单号以及初审结论查询有多少条满足条件的记录 -->	
<sql id="NB_queryByPolicyIdAndAuditConclutionCondition">
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" conclusion_code  != null "><![CDATA[ AND A.CONCLUSION_CODE = #{conclusion_code} ]]></if>
	</sql>		


<!-- 添加操作 -->
	<insert id="NB_addPreAuditMaster"  useGeneratedKeys="false"  parameterType="java.util.Map">
	 <selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="audit_id"> SELECT DEV_NB.S_t_pre_audit_cause.NEXTVAL FROM DUAL </selectKey>
		<![CDATA[
			INSERT INTO    DEV_NB.T_PRE_AUDIT_MASTER  (
				CUSTOMER_LEVEL, AUDIT_ORGAN_CODE, EXHIBITION_VALID, AGENT_MOBILE,  APPLY_CODE, INSERT_TIMESTAMP, 
				CONCLUSION_CODE, MANUAL_UW_INDI,  UPDATE_BY, BIRTHDAY_INDI, AUDIT_ID, 
				SUBMIT_DATE, AGENT_VALID, INSERT_TIME, AUDIT_BY, UPDATE_TIME, HIGH_SA_INDI, 
				AGENT_SINCERITY_LEVEL, INPUT_DATE,   MANUAL_UW_TIP, UPDATE_TIMESTAMP, INSERT_BY, 
				AGENT_CODE, QUALIFICATION_VALID, WINNING_START_FLAG ) 
			VALUES (
				#{customer_level, jdbcType=VARCHAR}, #{audit_organ_code, jdbcType=VARCHAR} , #{exhibition_valid, jdbcType=NUMERIC} , #{agent_mobile, jdbcType=VARCHAR}  , #{apply_code, jdbcType=VARCHAR} , SYSDATE
				, #{conclusion_code, jdbcType=NUMERIC} , #{manual_uw_indi, jdbcType=NUMERIC} ,  #{update_by, jdbcType=NUMERIC} , #{birthday_indi, jdbcType=NUMERIC} , #{audit_id, jdbcType=NUMERIC} 
				, #{submit_date, jdbcType=DATE} , #{agent_valid, jdbcType=NUMERIC} , SYSDATE , #{audit_by, jdbcType=NUMERIC} , SYSDATE , #{high_sa_indi, jdbcType=NUMERIC} 
				, #{agent_sincerity_level, jdbcType=NUMERIC} , #{input_date, jdbcType=TIMESTAMP} ,  #{manual_uw_tip, jdbcType=VARCHAR} , SYSDATE, #{insert_by, jdbcType=NUMERIC} 
				, #{agent_code, jdbcType=VARCHAR} , #{qualification_valid, jdbcType=NUMERIC}, #{winning_start_flag, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="NB_deletePreAuditMaster" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_PRE_AUDIT_MASTER          WHERE AUDIT_ID           = #{audit_id          } ]]>
	</delete>
	<!-- 删除初审试算险种信息 -->
	<delete id="NB_deleteAuditProductByAuditId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM DEV_NB.T_pre_audit_product          WHERE AUDIT_ID           = #{audit_id } ]]>
	</delete>

<!-- 修改操作 -->
	<update id="NB_updatePreAuditMaster" parameterType="java.util.Map">
		<![CDATA[ UPDATE    DEV_NB.T_PRE_AUDIT_MASTER   ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_LEVEL = #{customer_level, jdbcType=VARCHAR} ,
			AUDIT_ORGAN_CODE = #{audit_organ_code, jdbcType=VARCHAR} ,
		    EXHIBITION_VALID = #{exhibition_valid, jdbcType=NUMERIC} ,
			AGENT_MOBILE = #{agent_mobile, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    CONCLUSION_CODE = #{conclusion_code, jdbcType=NUMERIC} ,
		    MANUAL_UW_INDI = #{manual_uw_indi, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    BIRTHDAY_INDI = #{birthday_indi, jdbcType=NUMERIC} ,
		    SUBMIT_DATE = #{submit_date, jdbcType=DATE} ,
		    AGENT_VALID = #{agent_valid, jdbcType=NUMERIC} ,
		    AUDIT_BY = #{audit_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    HIGH_SA_INDI = #{high_sa_indi, jdbcType=NUMERIC} ,
		    AGENT_SINCERITY_LEVEL = #{agent_sincerity_level, jdbcType=NUMERIC} ,
		    INPUT_DATE = #{input_date, jdbcType=TIMESTAMP} ,
			MANUAL_UW_TIP = #{manual_uw_tip, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = SYSDATE ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		    QUALIFICATION_VALID = #{qualification_valid, jdbcType=NUMERIC} ,
		    WINNING_START_FLAG = #{winning_start_flag, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE AUDIT_ID = #{audit_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="NB_findMasterByAuditId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_LEVEL, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
			A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIRTHDAY_INDI,  A.AUDIT_ID, 
			A.SUBMIT_DATE, A.AGENT_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE,  A.MANUAL_UW_TIP,
			A.AGENT_CODE, A.QUALIFICATION_VALID FROM    DEV_NB.T_PRE_AUDIT_MASTER   A WHERE 1 = 1  ]]>
		<include refid="NB_queryByAuditIdCondition" />
		<include refid="NB_queryByApplyCodeAndAuditConclusionCondition" />
		<![CDATA[ ORDER BY A.AUDIT_ID           ]]>
	</select>
	
	<select id="NB_findAuditByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.CUSTOMER_LEVEL, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
			A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIRTHDAY_INDI,  A.AUDIT_ID, 
			A.SUBMIT_DATE,A.AGENT_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE, A.MANUAL_UW_TIP, A.WINNING_START_FLAG,
			A.AGENT_CODE, A.QUALIFICATION_VALID FROM    DEV_NB.T_PRE_AUDIT_MASTER   A WHERE 1 = 1  ]]>
		<include refid="NB_queryByApplyCodeAndAuditConclusionCondition" />
		<include refid="NB_queryByConclCodeAndAuditConclusionCondition" /> 
		<![CDATA[ ORDER BY A.AUDIT_ID           ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="NB_findAllMapPreAuditMaster" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT A.CUSTOMER_LEVEL, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
			A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIRTHDAY_INDI, A.AUDIT_ID, 
			A.SUBMIT_DATE,A.AGENT_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE,  A.MANUAL_UW_TIP, 
			A.AGENT_CODE, A.QUALIFICATION_VALID FROM    DEV_NB.T_PRE_AUDIT_MASTER   A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.AUDIT_ID           ]]>
	</select>
	
	<!-- 按map查询操作  查询通知书内容信息-->
	<select id="NB_findAllMapPreAuditNOtice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select AUDIT_ID,apply_code,agent_code,AUDIT_BY from DEV_NB.T_PRE_AUDIT_MASTER]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY AUDIT_ID           ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="NB_findAllPreAuditMaster" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT A.CUSTOMER_LEVEL, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
			A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIRTHDAY_INDI,  A.AUDIT_ID, 
			A.SUBMIT_DATE, A.AGENT_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE,  A.MANUAL_UW_TIP, 
			A.AGENT_CODE, A.QUALIFICATION_VALID,B.AGENT_NAME FROM DEV_NB.T_PRE_AUDIT_MASTER A,DEV_PAS.T_AGENT B WHERE A.AGENT_CODE = B.AGENT_CODE AND ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="NB_queryByPolicyIdAndAuditConclutionCondition" />
		<![CDATA[ ORDER BY A.AUDIT_ID DESC]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="NB_findPreAuditMasterTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_PRE_AUDIT_MASTER          A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="NB_queryByPolicyIdAndAuditConclutionCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="NB_queryPreAuditMasterForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_LEVEL, B.AUDIT_ORGAN_CODE, B.EXHIBITION_VALID, B.AGENT_MOBILE,  B.APPLY_CODE, 
			B.CONCLUSION_CODE, B.MANUAL_UW_INDI,  B.BIRTHDAY_INDI,  B.AUDIT_ID, 
			B.SUBMIT_DATE,  B.AGENT_VALID, B.AUDIT_BY, B.HIGH_SA_INDI, 
			B.AGENT_SINCERITY_LEVEL, B.INPUT_DATE, B.MANUAL_UW_TIP, 
			B.AGENT_CODE, B.QUALIFICATION_VALID FROM (
					SELECT ROWNUM RN, A.CUSTOMER_LEVEL, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
			A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIRTHDAY_INDI,  A.AUDIT_ID, 
			A.SUBMIT_DATE,A.AGENT_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE,  A.MANUAL_UW_TIP,
			A.AGENT_CODE, A.QUALIFICATION_VALID FROM    DEV_NB.T_PRE_AUDIT_MASTER   A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.AUDIT_ID           ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 根据投保单号查询所有操作 -->
	<select id="NB_findAllPreAuditMasterByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT A.CUSTOMER_LEVEL, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
			A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIRTHDAY_INDI, A.AUDIT_ID, 
			A.SUBMIT_DATE, A.AGENT_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE, A.MANUAL_UW_TIP, A.WINNING_START_FLAG,
			A.AGENT_CODE, A.QUALIFICATION_VALID FROM    DEV_NB.T_PRE_AUDIT_MASTER   A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<include refid="NB_queryByAuditIdCondition" />
		<![CDATA[ ORDER BY A.Update_Time          ]]> 
	</select>
	<!-- 根据投保单号查询最新的一条数据 -->
	<sql id="NB_queryByApplyCode">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND b.apply_code = #{apply_code} ]]></if>
	</sql>
	<select id="NB_findMaxAuditByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONCLUSION_CODE, A.AUDIT_ORGAN_CODE, A.AUDIT_BY, A.HIGH_SA_INDI, 
            A.AGENT_SINCERITY_LEVEL, A.APPLY_CODE,  A.CUSTOMER_LEVEL, A.MANUAL_UW_INDI, 
            A.BIRTHDAY_INDI, A.AUDIT_ID, A.MANUAL_UW_TIP, A.AGENT_CODE,
            A.SUBMIT_DATE,A.WINNING_START_FLAG FROM DEV_NB.T_PRE_AUDIT_MASTER          A WHERE  1=1
            AND A.AUDIT_ID=(SELECT MAX(B.AUDIT_ID) FROM DEV_NB.T_PRE_AUDIT_MASTER B WHERE 1=1 ]]>
		<include refid="NB_queryByApplyCode" />
		<![CDATA[) ORDER BY A.AUDIT_ID]]>
	</select>
		<!-- 根据投保单号查询数据 -->
	<sql id="NB_findInfoByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.apply_code = #{apply_code} ]]></if>
	</sql>
	<select id="NB_findInfoByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.AUDIT_ORGAN_CODE, A.AUDIT_BY, A.HIGH_SA_INDI, A.CONCLUSION_CODE,
            A.AGENT_SINCERITY_LEVEL, A.APPLY_CODE,  A.CUSTOMER_LEVEL, A.MANUAL_UW_INDI, 
            A.BIRTHDAY_INDI, A.AUDIT_ID, A.MANUAL_UW_TIP, A.AGENT_CODE, A.WINNING_START_FLAG,
            A.SUBMIT_DATE from DEV_NB.T_PRE_AUDIT_MASTER  A WHERE  1=1 AND  A.CONCLUSION_CODE =1 ]]>
		<include refid="NB_findInfoByApplyCodeCondition" />
	</select>
	<!-- 根据投保单号更新数据 -->
	<update id="NB_updateInfoByApplyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE    DEV_NB.T_PRE_AUDIT_MASTER  T ]]>
		<set>
		<trim suffixOverrides=",">
		    T.winning_start_flag = 1 ,
		</trim>
		</set>
		<![CDATA[ WHERE T.APPLY_CODE = #{apply_code} ]]>
	</update>
	<!-- 根据投保单号更新初审主表中业务员数据 -->
	<update id="NB_updateInfoByApplyCodeAndAgentCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE    DEV_NB.T_PRE_AUDIT_MASTER  T ]]>
		<set>
		<trim suffixOverrides=",">
		    T.AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE T.APPLY_CODE = #{apply_code} AND T.CONCLUSION_CODE = '1' ]]>
	</update>

<!-- 分页查询操作 -->
	<select id="NB_findAllPreAuditMasterByApplyCodeAndDate" resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ 
					SELECT ROWNUM RN, A.CUSTOMER_LEVEL, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
			A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIRTHDAY_INDI, A.AUDIT_ID, 
			A.SUBMIT_DATE, A.AGENT_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE,  A.MANUAL_UW_TIP, 
			A.AGENT_CODE, A.QUALIFICATION_VALID FROM    DEV_NB.T_PRE_AUDIT_MASTER   A WHERE 1=1 AND A.CONCLUSION_CODE <> 2 ]]>
		<include refid="NB_queryByApplyCodeAndDateCondition" />
		<![CDATA[ ORDER BY A.AUDIT_ID           ]]> 
	</select>	
<!-- 按多条件查询个数操作 -->
	<select id="NB_findPreAuditMasterTotalByApplyCodeAndDate" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_NB.T_PRE_AUDIT_MASTER                           A WHERE 1 = 1  ]]>
		<include refid="NB_queryByApplyCodeAndDateCondition" />
	</select>	
	
	<!-- 根据投保单号查询初审主表对应的险种信息 -->
	<select id="NB_findAllPreAuditBusiProductByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT(BUSI_PRD_ID) ,AUDIT_ID  FROM DEV_NB.T_PRE_AUDIT_PRODUCT WHERE AUDIT_ID = ( 
				SELECT AUDIT_ID  FROM DEV_NB.T_PRE_AUDIT_MASTER WHERE APPLY_CODE = #{apply_code} AND INSERT_TIME = (SELECT max(INSERT_TIME) FROM DEV_NB.T_PRE_AUDIT_MASTER WHERE APPLY_CODE = #{apply_code})
				) ]]>
		<![CDATA[ ORDER BY AUDIT_ID]]>
	</select>
	<!-- 根据auditid和busiProdId查询初审主表对应的险种信息 -->
	<select id="NB_findAllPreAuditProductByAuditIdAndBusiProdId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_ID, A.PRODUCT_CODE, A.COVERAGE_PERIOD, A.BUSI_PRD_ID, A.CHARGE_YEAR, 
			A.AUDIT_ID, A.LIST_ID, A.UNIT, A.PAY_FREQ, A.AGE, A.COVERAGE_YEAR, A.PERIOD_PREM, 
			A.JOB_UNDERWRITE, A.PAY_YEAR, A.CHARGE_PERIOD, A.PAY_PERIOD, A.BENEFIT_LEVEL,A.PREM_FREQ,
			A.GENDER, A.AMOUNT FROM DEV_NB.T_PRE_AUDIT_PRODUCT  A WHERE A.AUDIT_ID = #{audit_id}]]>
			<if test=" busi_prd_id  != null "><![CDATA[ AND  A.BUSI_PRD_ID = #{busi_prd_id} ]]></if>
	</select>
	
	<select id="NB_findPreAuditMasterByApplyCodeAndConClu" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_LEVEL, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
			A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIRTHDAY_INDI,  A.AUDIT_ID, 
			A.SUBMIT_DATE, A.AGENT_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE, A.MANUAL_UW_TIP, 
			A.AGENT_CODE, A.QUALIFICATION_VALID FROM    DEV_NB.T_PRE_AUDIT_MASTER   A WHERE 1=1 ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[) ORDER BY A.AUDIT_ID]]>
	</select>	
	
	
	<!-- 根据投保单号查询 -->
	<select id="NB_findAuditAllByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.AUDIT_ID,B.AGENT_MOBILE,A.INPUT_DATE,B.AGENT_NAME,A.CONCLUSION_CODE, A.AUDIT_ORGAN_CODE, A.AUDIT_BY, A.HIGH_SA_INDI, 
			A.AGENT_SINCERITY_LEVEL, A.APPLY_CODE,  A.CUSTOMER_LEVEL, A.MANUAL_UW_INDI, 
			A.BIRTHDAY_INDI, A.MANUAL_UW_TIP, A.AGENT_CODE, A.WINNING_START_FLAG,
			A.SUBMIT_DATE,A.INSERT_TIME as auditDate FROM DEV_NB.T_PRE_AUDIT_MASTER   A  LEFT JOIN DEV_PAS.T_AGENT B ON A.AGENT_CODE=B.AGENT_CODE WHERE 1 = 1   ]]>
		<include refid="NB_queryByApplyCodeAndAuditConclusionCondition" />
		<include refid="NB_queryByConclCodeAndAuditConclusionCondition" />                                                                     
		
		<![CDATA[ ORDER BY A.AUDIT_ID           ]]>
	</select>
	<!-- 查询投保资料信息 -->
		<select id="NB_findInsureDocCfg" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.LIST_ID,A.DATA_NAME,A.COPIES_NUM, A.DATA_CATEGORY, A.IS_VALID FROM DEV_NB.T_INSURE_DOC_CFG   A ]]>
		<![CDATA[ ORDER BY A.LIST_ID           ]]>
	</select>

	<!-- 操作履历下初审基础信息查询 -->
	<select id="queryPreAuditMaster" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT m.APPLY_CODE,m.UPDATE_BY,m.UPDATE_TIME,m.CONCLUSION_CODE,
		m.AUDIT_ID FROM DEV_NB.T_PRE_AUDIT_MASTER m WHERE 1=1 AND
		m.APPLY_CODE=#{apply_code} ORDER BY m.APPLY_CODE]]>
	</select>

	<!-- 修改成出单前撤保状态  3 -->
	<update id="NB_updatePerAuditProposalStatus" parameterType="java.util.Map">
		<![CDATA[ UPDATE    DEV_NB.T_PRE_AUDIT_MASTER   ]]>
		<set>
		<trim suffixOverrides=",">
		    CONCLUSION_CODE = #{conclusion_code, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE ,
			UPDATE_TIMESTAMP = SYSDATE ,
		</trim>
		</set>
		<![CDATA[ WHERE APPLY_CODE = #{apply_code} ]]>
	</update>
	
	<!-- 根据投保单号查询 -->
	<select id="NB_findAgentMsgByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[select TT.AGENT_NAME, ff.apply_code 
                         from DEV_PAS.T_AGENT TT,(select AG.AGENT_CODE as agent_code,mm.apply_code 
                            from DEV_NB.T_NB_CONTRACT_AGENT AG, DEV_NB.T_NB_CONTRACT_MASTER mm 
                           WHERE AG.POLICY_ID = mm.POLICY_ID 
                             AND mm.APPLY_CODE = #{apply_code} ) ff where 1=1 and tt.agent_code = ff.agent_code]]>
	</select>
	<!-- 银保通数据更新为出单前撤保 -->
	<update id="NB_updateConclusioNByApplyCode" parameterType="java.util.Map">
		<![CDATA[ UPDATE    DEV_NB.T_PRE_AUDIT_MASTER  T ]]>
		<set>
		<trim suffixOverrides=",">
		    T.conclusion_code = 3 ,
		</trim>
		</set>
		<![CDATA[ WHERE T.APPLY_CODE = #{apply_code} ]]>
	</update>
	
</mapper>
