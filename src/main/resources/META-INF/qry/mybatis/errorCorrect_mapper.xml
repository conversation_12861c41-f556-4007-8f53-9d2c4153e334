<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.dao.impl.ErrorCorrectDaoImpl">
<!-- 按索引生成的查询条件 -->		
	<sql id="NB_queryByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>

	<!-- 根据保单号查询纠错主表的信息 -->
	<select id="NB_findErrorCorrectByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_LEVEL, A.ERROR_ITEM_DESC, A.CREATE_BY, A.AUDIT_FAIL_CAUSE, A.<PERSON>Y_CODE, A.ORGAN_CODE, 
			A.REPRINT_INDI, A.CHANNEL_TYPE, A.CORRECT_ID, A.AGENT_LEVEL, A.POLICY_RECYCLE_DATE, A.POLICY_ID, 
			A.APP_DATE, A.STATUS_CODE, A.AUDIT_CONCLUSION, A.REASON_DESC, A.UW_INDI, A.AUDIT_BY, 
			A.FEE_INDI, A.POLICY_CODE, A.AUDIT_DATE, A.VALI_DATE, A.APP_BY, A.ERROR_SOURCE, 
			A.CREATE_DATE FROM DEV_NB.T_ERROR_CORRECT A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.CORRECT_ID ]]>
	</select>
	
</mapper>
