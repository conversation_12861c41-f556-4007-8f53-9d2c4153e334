<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.clmConsultion">
	 <!--理赔综合查询-审核结论start -->
	  <select id="queryAuditConclusionInfo" resultType="java.util.Map"
	        parameterType="java.util.Map">
	         <![CDATA[ 
			              SELECT TC.ACCEPT_DECISION,
	                 TC.REPEAL_REASON,
	                 TC.REGISTER_ID,
	                 TC.REGISTE_TIME,
	                 TC.AUDIT_REMARK,
	                 TC.AUDIT_DECISION,
	                 TC.IS_COMMON,
	                 TC.AUDIT_REJECT_REASON,
	                 TC.OTHER_REASON,
	                 TC.AUDIT_TIME,
	                 T.REAL_NAME
	            FROM DEV_CLM.T_CLAIM_CASE TC 
	            LEFT JOIN DEV_PAS.T_UDMP_USER T ON  TC.AUDITOR_ID = T.USER_ID
	            WHERE 1=1
	    	  ]]>
	     <!-- 添加查询条件 -->
	       <if test=" caseID != null and caseID != ''  "><![CDATA[AND TC.CASE_ID=#{caseID} ]]></if>
	  </select>
	  <!--理赔综合查询-审核结论end -->
	  
	 <!--理赔综合查询-审批结论start -->
	<select id="queryApproveConclusionInfo" resultType="java.util.Map"
	        parameterType="java.util.Map">
	         <![CDATA[     	
				            SELECT TC.ACCEPT_DECISION,
				           TC.REPEAL_REASON,
				           TC.REGISTER_ID,
				           TC.REGISTE_TIME,
				           TC.APPROVE_REMARK,
				           TC.APPROVE_DECISION,
				           TC.APPROVE_REJECT_REASON,
				           T.REAL_NAME,
				           TC.APPROVE_TIME
				      FROM DEV_CLM.T_CLAIM_CASE TC
				      LEFT JOIN DEV_PAS.T_UDMP_USER T ON  TC.APPROVER_ID = T.USER_ID
				      WHERE 1=1 				    
	    	  ]]>
	    	   <!-- 添加查询条件 -->
	     <if test=" caseID != null and caseID != ''  "><![CDATA[AND TC.CASE_ID=#{caseID} ]]></if> 
	  </select> 
	  <!--理赔综合查询-审批结论end -->
</mapper>