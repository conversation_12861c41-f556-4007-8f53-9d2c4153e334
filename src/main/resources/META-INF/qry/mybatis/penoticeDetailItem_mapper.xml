<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwPenoticeDaoImpl">
	<!-- 体检信息明细表(体检项目) -->
	<!-- <sql id="UW_penoticeDetailItemWhereCondition"> <if test=" pe_hospital_code 
		!= null and pe_hospital_code != '' "><![CDATA[ AND A.PE_HOSPITAL_CODE = #{pe_hospital_code} 
		]]></if> <if test=" unit != null and unit != '' "><![CDATA[ AND A.UNIT = 
		#{unit} ]]></if> <if test=" penotice_id != null "><![CDATA[ AND A.PENOTICE_ID 
		= #{penotice_id} ]]></if> <if test=" peitem_code != null and peitem_code 
		!= '' "><![CDATA[ AND A.PEITEM_CODE = #{peitem_code} ]]></if> <if test=" 
		detail_id != null "><![CDATA[ AND A.DETAIL_ID = #{detail_id} ]]></if> <if 
		test=" max != null and max != '' "><![CDATA[ AND A.MAX = #{max} ]]></if> 
		<if test=" pe_result_value != null and pe_result_value != '' "><![CDATA[ 
		AND A.PE_RESULT_VALUE = #{pe_result_value} ]]></if> <if test=" pe_back_indi 
		!= null "><![CDATA[ AND A.PE_BACK_INDI = #{pe_back_indi} ]]></if> <if test=" 
		min != null and min != '' "><![CDATA[ AND A.MIN = #{min} ]]></if> <if test=" 
		peitem_detail_code != null and peitem_detail_code != '' "><![CDATA[ AND A.PEITEM_DETAIL_CODE 
		= #{peitem_detail_code} ]]></if> <if test=" list_id != null "><![CDATA[ AND 
		A.LIST_ID = #{list_id} ]]></if> <if test=" pe_msg != null and pe_msg != '' 
		"><![CDATA[ AND A.PE_MSG = #{pe_msg} ]]></if> </sql> -->

	<!-- 按索引生成的查询条件 -->
	<sql id="UW_queryPenoticeDetailItemByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>

	<!-- 按索引查询操作 -->
	<select id="UW_findPenoticeDetailItemByListId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PE_HOSPITAL_CODE, A.UNIT, A.PENOTICE_ID, A.PEITEM_CODE, A.DETAIL_ID, 
			A.MAX, A.PE_RESULT_VALUE, A.PE_BACK_INDI, A.MIN, A.PEITEM_DETAIL_CODE, 
			A.LIST_ID, A.PE_MSG FROM  DEV_UW.T_PENOTICE_DETAIL_ITEM                  A WHERE 1 = 1  ]]>
		<include refid="UW_queryPenoticeDetailItemByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID               ]]>
	</select>


	<!-- 按map查询操作 -->
	<select id="UW_findAllMapPenoticeDetailItem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PE_HOSPITAL_CODE, A.UNIT, A.PENOTICE_ID, A.PEITEM_CODE, A.DETAIL_ID, 
			A.MAX, A.PE_RESULT_VALUE, A.PE_BACK_INDI, A.MIN, A.PEITEM_DETAIL_CODE, 
			A.LIST_ID, A.PE_MSG FROM  DEV_UW.T_PENOTICE_DETAIL_ITEM                  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID               ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="UW_findAllPenoticeDetailItem" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PE_HOSPITAL_CODE, A.UNIT, A.PENOTICE_ID, A.PEITEM_CODE, A.DETAIL_ID, 
			A.MAX, A.PE_RESULT_VALUE, A.PE_BACK_INDI, A.MIN, A.PEITEM_DETAIL_CODE, 
			A.LIST_ID, A.PE_MSG FROM  DEV_UW.T_PENOTICE_DETAIL_ITEM                  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID               ]]>
	</select>

	<!-- 查询个数操作 -->
	<select id="UW_findPenoticeDetailItemTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM  DEV_UW.T_PENOTICE_DETAIL_ITEM                  A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 分页查询操作 -->
	<select id="UW_queryPenoticeDetailItemForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PE_HOSPITAL_CODE, B.UNIT, B.PENOTICE_ID, B.PEITEM_CODE, B.DETAIL_ID, 
			B.MAX, B.PE_RESULT_VALUE, B.PE_BACK_INDI, B.MIN, B.PEITEM_DETAIL_CODE, 
			B.LIST_ID, B.PE_MSG FROM (
					SELECT ROWNUM RN, A.PE_HOSPITAL_CODE, A.UNIT, A.PENOTICE_ID, A.PEITEM_CODE, A.DETAIL_ID, 
			A.MAX, A.PE_RESULT_VALUE, A.PE_BACK_INDI, A.MIN, A.PEITEM_DETAIL_CODE, 
			A.LIST_ID, A.PE_MSG FROM  DEV_UW.T_PENOTICE_DETAIL_ITEM                  A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID               ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

	<!-- 查询体检结果列表 add by youyuan -->
	<select id="UW_findPenoticeDetailItemsByPenoticeId" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT 
				A.UW_HOSPITAL_ID,A.UNIT,A.PENOTICE_ID,A.PEITEM_CODE,A.DETAIL_ID,A.MAX,A.PE_RESULT_VALUE,A.PE_BACK_INDI,
       			A.PEITEM_DETAIL_CODE,A.MIN,A.LIST_ID,A.PE_MSG, A.OTHER_PEITEM_NAME, A.OTHER_UNIT,
       			(SELECT PEITEM_NAME FROM DEV_UW.T_PHYSICAL_ITEM WHERE PEITEM_CODE=A.PEITEM_CODE)   AS PEITEM_NAME,
     			(SELECT PEITEM_DETAIL_NAME FROM  DEV_UW.T_PHYSICAL_ITEM_DETAIL   WHERE  PEITEM_DETAIL_CODE=A.PEITEM_DETAIL_CODE) AS PEITEM_DETAIL_NAME
 	 		FROM DEV_UW.T_PENOTICE_DETAIL_ITEM A   WHERE 1=1]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID               ]]>
	</select>
	<select id="UW_findPenoticeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PENOTICE_ID FROM DEV_UW.T_PENOTICE_DETAIL_ITEM A ]]>
	</select>
	<select id="UW_findSurvivalInvestigationDe" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT A.EXAMINE_CODE  FROM DEV_UW.T_SURVIVAL_INVESTIGATION_DETAI A WHERE 1=1
		 ]]>
		 <if test=" rreport_id  != null "><![CDATA[ AND A.RREPORT_ID = #{rreport_id} ]]></if>
	</select>
	
	<!--  add by zhouzx 2015-12-08   体检结果与异常体检结果  begin -->
	<select id="UW_findPhysicalResultByPenoticeIdOrPeMsg" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT 
				A.UW_HOSPITAL_ID,A.UNIT,A.PENOTICE_ID,A.PEITEM_CODE,A.DETAIL_ID,A.MAX,A.PE_RESULT_VALUE,A.PE_BACK_INDI,
       			A.PEITEM_DETAIL_CODE,A.MIN,A.LIST_ID,A.PE_MSG,
       			(SELECT PEITEM_NAME FROM DEV_UW.T_PHYSICAL_ITEM WHERE PEITEM_CODE=A.PEITEM_CODE)   AS PEITEM_NAME,
     			(SELECT PEITEM_DETAIL_NAME FROM  DEV_UW.T_PHYSICAL_ITEM_DETAIL   WHERE  PEITEM_DETAIL_CODE=A.PEITEM_DETAIL_CODE) AS PEITEM_DETAIL_NAME
 	 		FROM DEV_UW.T_PENOTICE_DETAIL_ITEM A   WHERE 1=1]]>
		<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
		<if test=" pe_msg  != null and pe_msg != '' "><![CDATA[ AND A.PE_MSG in (${pe_msg}) ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID               ]]>
	</select>
	<!--  add by zhouzx 2015-12-08   end -->
	
	<!-- 按PENOTICE_ID查询体检项目名称add by zhaoxs -->
	<select id="UW_findPeitemNameByPenoticeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT B.PEITEM_NAME, B.PEITEM_ID, A.DETAIL_ID, A.OTHER_CONTENT FROM DEV_UW.T_PENOTICE_DETAIL A 
			JOIN DEV_UW.T_PHYSICAL_ITEM B 
			ON A.PEITEM_CODE = B.PEITEM_CODE AND A.PENOTICE_ID = #{penotice_id} ORDER BY A.DETAIL_ID, B.PEITEM_ID ]]>
	</select>
	
	
	<select id="UW_findAllDetailedComment" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT A.PEITEM_NAME,
        A.PENOTICE_ID,
        A.LIST_ID,
        B.LIST_NUMBER,
        A.PEITEM_DETAIL_NAME,
        A.Ci as CODE_CI,
        A.LIFE AS CODE_LIFE,
        A.ADB AS CODE_ADB,
        A.CANCER AS CODE_CANCER,
        A.TPD AS CODE_TPD,
        A.MEDEX AS CODE_MEDEX,
        (SELECT C.NEW_CODE_NAME
           FROM dev_uw.T_CODE_MAPPER C
          WHERE C.OLD_CODE = A.CI
            AND C.CODETYPE = 'MAGNUM_CODE'
            AND C.FROM_MODLE = 'UW') AS CI,
        (SELECT C.NEW_CODE_NAME
           FROM dev_uw.T_CODE_MAPPER C
          WHERE C.OLD_CODE = A.LIFE
            AND C.CODETYPE = 'MAGNUM_CODE'
            AND C.FROM_MODLE = 'UW') AS LIFE,
        (SELECT C.NEW_CODE_NAME
           FROM dev_uw.T_CODE_MAPPER C
          WHERE C.OLD_CODE = A.TPD
            AND C.CODETYPE = 'MAGNUM_CODE'
            AND C.FROM_MODLE = 'UW') AS TPD,
        (SELECT C.NEW_CODE_NAME
           FROM dev_uw.T_CODE_MAPPER C
          WHERE C.OLD_CODE = A.MEDEX
            AND C.CODETYPE = 'MAGNUM_CODE'
            AND C.FROM_MODLE = 'UW') AS MEDEX,  
        (SELECT C.NEW_CODE_NAME
           FROM dev_uw.T_CODE_MAPPER C
          WHERE C.OLD_CODE = A.CANCER
            AND C.CODETYPE = 'MAGNUM_CODE'
            AND C.FROM_MODLE = 'UW') AS CANCER,
        (SELECT C.NEW_CODE_NAME
           FROM dev_uw.T_CODE_MAPPER C
          WHERE C.OLD_CODE = A.ADB
            AND C.CODETYPE = 'MAGNUM_CODE'
            AND C.FROM_MODLE = 'UW') AS ADB
   FROM APP___UW__DBUSER.T_UW_DETAILED_COMMENT A
   JOIN DEV_UW.T_PENOTICE_DETAILED B
     ON A.PEITEM_DETAIL_NAME = B.DETAILED_COMMENTS
    AND A.PEITEM_NAME = B.PENOTICE_ITEMS
    AND ROWNUM <=  1000 AND A.PENOTICE_ID = #{penotice_id}
			]]>
		<![CDATA[  ORDER BY B.LIST_NUMBER ]]> 
	</select>
	<!-- 查询所有操作 -->
	<select id="UW_findAllUwDetailedCommentPeitemName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[   SELECT A.PEITEM_NAME,
        A.PENOTICE_ID,
        A.LIST_ID,
        A.PEITEM_DETAIL_NAME,
        A.Ci ,
        A.LIFE ,
        A.ADB ,
        A.CANCER,
        A.TPD,
        A.MEDEX
   FROM APP___UW__DBUSER.T_UW_DETAILED_COMMENT A
   WHERE A.PEITEM_NAME = '参考评点'
   AND A.PENOTICE_ID = #{penotice_id}
			]]>
	</select>
	
	
	
</mapper>
