<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwQryDaoImpl"> 



	<select id="findReplyApplyDetialById" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
SELECT B.*,(CASE WHEN AGENT_NOTICE1>0 then 'Y' else 'N' end) AS AGENT_NOTICE FROM (
SELECT DISTINCT P.APPLY_CODE,
       CM.POLICY_ID,
       CM.ORGAN_CODE,
       CM.APPLY_DATE,
       CM.CHANNEL_TYPE,
       CM.SALE_AGENT_CODE,
       A.AGENT_NAME,
       A.AGENT_LEVEL,
       PC.CUSTOMER_NAME,
       (SELECT count(A.SURVEY_OBJECT) AS AGENT_NOTICE1 FROM DEV_UW.T_CUSTOMER_SURVEY_UW_COPY A WHERE UW_ID =P.UW_ID AND  SURVEY_OBJECT=3) AS AGENT_NOTICE1 
  FROM DEV_UW.T_UW_BUSI_PROD P
 LEFT JOIN DEV_UW.T_CONTRACT_MASTER CM
    ON CM.POLICY_ID = P.POLICY_ID
    AND P.UW_ID=CM.UW_ID
  LEFT JOIN (SELECT PH.POLICY_ID, PH.CUSTOMER_ID, C.CUSTOMER_NAME
               FROM DEV_NB.T_NB_POLICY_HOLDER PH
              INNER JOIN DEV_NB.T_CUSTOMER C
                 ON C.CUSTOMER_ID = PH.CUSTOMER_ID) PC
    ON PC.POLICY_ID = P.POLICY_ID
  LEFT JOIN DEV_PAS.T_AGENT A
    ON A.AGENT_CODE = CM.SALE_AGENT_CODE
   WHERE 1=1 
   
	]]>
	
	<if test=" uw_id != null and uw_id != ''  "><![CDATA[AND P.UW_ID = ${uw_id} ]]></if>
	<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[AND P.UW_BUSI_ID = ${busi_item_id} ]]></if>
	) B 
	</select>
	
	<select id="findReplyBusiInfoListById" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
	SELECT BP.BUSI_PROD_CODE,
	      CBP.BUSI_PRD_ID as PRODUCT_ID,
       BP.INSURED_1 as insured,
       CBP.VALIDATE_DATE,
       CBP.EXPIRY_DATE,
       CP.CHARGE_YEAR,
       (CASE WHEN AP.CUSTOMER_NAME IS NOT NULL  THEN AP.CUSTOMER_NAME
         ELSE C.CUSTOMER_NAME END ) AS CUSTOMER_NAME,
       AP.Initial_Type,
       AP.AMOUNT,
       AP.UNIT,
       CP.BENEFIT_LEVEL,
       CP.TOTAL_PREM_AF,
       AP.RI_APP_REASON,
       AP.EXTRA_POINT,
       AP.REPLY_ADVICE,
       AP.REPLY_ADVICE_AMOUNT,
       AP.REPLY_ADVICE_EXTRA_POINT
  FROM DEV_UW.T_UW_BUSI_PROD BP
 INNER JOIN DEV_UW.T_CONTRACT_BUSI_PROD CBP
    ON CBP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
 INNER JOIN DEV_UW.T_CONTRACT_PRODUCT CP
    ON CP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
 INNER JOIN DEV_UW.T_RI_APP_PRODUCT AP
    ON AP.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
 INNER JOIN APP___UW__DBUSER.T_RI_APP_POLICY RAP
    ON AP.RI_ID = RAP.RI_ID
   AND BP.UW_ID = RAP.UW_ID
 INNER JOIN DEV_PAS.T_CUSTOMER C
    ON AP.CUSTOMER_ID = C.CUSTOMER_ID
 WHERE RAP.UW_ID = ${uw_id}
   AND BP.UW_BUSI_ID = ${busi_item_id}
	]]>
	</select>
	
	<select id="findReplyInfoListById" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
SELECT BP.BUSI_PROD_CODE,
       CBP.BUSI_PRD_ID as PRODUCT_ID,
       AR.REPLY_ADVICE,
       AR.REPLY_ADVICE_EXTRA_POINT,
       AR.EXTRA_POINT,
       AP.REPLY_USER,
       AP.REPLY_TIME,
       AP.REACTION_TIME,
       AR.RI_ID
  FROM DEV_UW.T_UW_BUSI_PROD BP
 INNER JOIN DEV_UW.T_RI_APP_PRODUCT AR
    ON AR.BUSI_ITEM_ID = BP.BUSI_ITEM_ID
 INNER JOIN DEV_UW.T_CONTRACT_BUSI_PROD CBP
    ON BP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
 INNER JOIN DEV_UW.T_RI_APP_POLICY AP
    ON AP.RI_ID = AR.RI_ID
 WHERE BP.UW_ID = ${uw_id}
   AND BP.UW_BUSI_ID = ${busi_item_id}
	]]>
	</select>
	
	<!-- 按索引查询操作 -->	
	<select id="findRiapppolicyinfoByuwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT *
  FROM (SELECT A.REPLY_ADVICE_COMMENT,
               A.REPLY_ADD_INFO_COMMENT,
               A.INSERT_TIME,
               A.REPLY_TIME,
               A.REPLY_USER,
               A.APPLY_RI_TIME,
               A.RI_NUM
          FROM DEV_UW.T_RI_APP_POLICY A
         WHERE 1 = 1
		  ]]> 
 <if test="uw_id !=null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	 <![CDATA[
 		 ORDER BY A.RI_NUM DESC)
     WHERE ROWNUM = 1
 		 ]]>
	</select>
	<!-- 按索引查询操作 -->	
	<select id="findUwQtTaskByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.QT_TIME, A.CASE_NO, A.QT_TYPE, A.APPLY_CODE, A.QT_RESULT, 
			A.QT_COMMENT, A.QT_USER_CODE, A.CHANGE_ID, A.LIST_ID, A.POLICY_CHG_ID, A.POLICY_ID, 
			A.QT_USER_CODE_LIST, A.ACCEPT_CODE, A.CASE_ID, A.QTCRITERIA_ID, A.POLICY_CODE, 
			A.UW_SOURCE_TYPE, A.QT_STATUS FROM DEV_UW.T_UW_QT_TASK A WHERE 1 = 1  ]]>
		<include refid="queryUwQtTaskByListIdCondition" />
	</select>
	
	<sql id="queryUwQtTaskByListIdCondition">
        <![CDATA[ AND A.UW_ID = #{uw_id,jdbcType=NUMERIC} ]]>
    </sql>
    
    <select id="findAgentTraitListById" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[SELECT 
AQ.QUALITY_ID AS QUALITY_ID,
    AQ.Busi_Source AS BUSI_SOURCE_TYPE,
  AQ.BUSI_CODE AS BIZ_CODE,
       CM.SALE_AGENT_CODE,
         A.AGENT_NAME,
        A.AGENT_CODE,
       A.AGENT_GENDER,
       A.CERT_TYPE,
       A.BIRTHDAY,
       A.CERTI_CODE,
       AQ.QULITY_MARK AS QUALITY_MARK_LEVEL,
       (select 
to_char(wm_concat(B.CAUSE_DESC)) AS QUALITY_MARK_REASON
from dev_pas.t_quality_causes A
inner join 
dev_pas.T_QUALITY_CAUSE B ON A.CAUSE_CODE=B.CAUSE_CODE
WHERE A.Quality_Id=AQ.Quality_Id AND A.Quality_Id=AQ.Quality_Id) AS QUALITY_MARK_REASON
  FROM DEV_UW.T_UW_MASTER M
 INNER JOIN DEV_UW.T_CONTRACT_MASTER CM
    ON CM.UW_ID = M.UW_ID
 INNER JOIN DEV_PAS.T_QUALITY AQ
    ON AQ.Person_Code = CM.SALE_AGENT_CODE
 INNER JOIN DEV_PAS.T_AGENT A
    ON A.AGENT_CODE = AQ.PERSON_CODE
 WHERE AQ.QUALITY_TYPE=2   AND AQ.Valid=1 AND M.UW_ID = ${agent_name}
 ]]>
 <if test="quality_id !=null "><![CDATA[ AND AQ.QUALITY_ID = ${quality_id}]]></if>
    </select>
    
    <select id="findMarkReasonCode" resultType="java.util.Map" parameterType="java.util.Map">
	 <![CDATA[
      SELECT QC.CAUSE_CODE AS CODE,QC.CAUSE_DESC AS NAME FROM DEV_PAS.T_QUALITY_CAUSE QC WHERE QC.QUALITY_TYPE=2
	  ]]>   
    </select>
    
	
	<!-- 获取保单信息 -->
	<select id="uwPolicyMasterInfoQry" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
       SELECT 
        CM.APPLY_CODE,
        CM.POLICY_CODE,
        CY.UW_SOURCE_TYPE,
        TER.UW_USER_ID,/*核保人员，本次核保人员*/
        TER.UW_SUBMIT_TIME,
        TER.UW_FINISH_TIME,
        to_char(TER.PREVIOUS_UW_USER) as PREVIOUS_UW_USER,/*上次核保人员*/
        TER.UW_ESCA_INDI AS UW_EVENT_CODE,
        --(SELECT COUNT(1) FROM DEV_UW.T_UW_TRACE TUT WHERE TUT.UW_ID = CY.UW_ID AND TUT.UW_EVENT_CODE ='11') AS UW_EVENT_CODE ,
        TER.UW_OVER_INDI,
        CY.POLICY_DECISION,/* 保单层的核保结论*/
        TER.BIZ_CODE,/*46902*/
        S.SERVICE_CODE,/*46902*/
        S.SERVICE_NAME,/*46902*/
       (SELECT (CASE
                 WHEN COUNT(*) > 0 THEN
                  'Y'
                 WHEN COUNT(*) = 0 THEN
                  'N'
               END)
          FROM DEV_UW.T_CUSTOMER_SURVEY_UW_COPY UC
         WHERE UC.UW_ID = TER.UW_ID
           AND UC.APPLY_CODE = CY.APPLY_CODE
           AND UC.SURVEY_OBJECT = '3'
           AND ROWNUM=1) AS AGENT_INFORMATION,/*代理人告知*/
        (SELECT UC.AGENT_CODE
          FROM DEV_UW.T_CUSTOMER_SURVEY_UW_COPY UC
         WHERE UC.UW_ID = TER.UW_ID
           AND UC.APPLY_CODE = CY.APPLY_CODE
           AND UC.SURVEY_OBJECT = '3'
           AND ROWNUM=1) AS AGENT_CODE,/*代理人代码*/       
	       NVL(CY.INSURANCE_PLAN_MESSAGE,'') AS  INSURANCE_PLAN_MESSAGE/*承保方案*/   
   FROM DEV_UW.T_UW_MASTER TER 
   INNER JOIN DEV_UW.T_UW_POLICY CY
   INNER JOIN 
    (SELECT 
     (CASE WHEN ( M.DOUBLE_MAINRISK_FLAG ='1' AND M.RELATION_POLICY_CODE IS NULL AND NCM.RELATION_POLICY_CODE IS NULL) THEN 
         (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER NCM WHERE NCM.RELATION_POLICY_CODE= M.POLICY_CODE)
         ELSE
           M.APPLY_CODE
       END 
       ) AS APPLY_CODE,
    M.APPLY_CODE AS JOIN_APPY_CODE,
    M.POLICY_CODE FROM DEV_PAS.T_CONTRACT_MASTER M 
    LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON M.POLICY_CODE = NCM.POLICY_CODE
    WHERE M.APPLY_CODE=#{apply_code}
    UNION
    SELECT (CASE WHEN ( M.DOUBLE_MAINRISK_FLAG ='1' AND M.RELATION_POLICY_CODE IS NULL) THEN 
         (SELECT APPLY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER NCM WHERE NCM.RELATION_POLICY_CODE= M.POLICY_CODE)
         ELSE
           M.APPLY_CODE
       END 
       ) AS APPLY_CODE,
    M.APPLY_CODE AS JOIN_APPY_CODE,M.POLICY_CODE FROM DEV_NB.T_NB_CONTRACT_MASTER M WHERE M.APPLY_CODE=#{apply_code})  CM 
   ON CM.JOIN_APPY_CODE=CY.APPLY_CODE
  ON TER.UW_ID = CY.UW_ID
  LEFT JOIN DEV_PAS.T_SERVICE S ON S.SERVICE_CODE=TER.SERVICE_CODE
  where 1=1 
  ]]>

		<include refid="uwPolicyMasterInfoQryCondition" />
	</select>
    
    <sql id="uwPolicyMasterInfoQryCondition">
        <![CDATA[ AND TER.uw_id = #{uw_id,jdbcType=NUMERIC} ]]>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND CY.APPLY_CODE =  #{apply_code} ]]></if>
    </sql>
    

    <!-- 获取险种核保信息 险种1 -->
    <select id="insuraUwDetailQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
SELECT distinct(MA.UW_ID),
       (SELECT US.STATUS_NAME
          FROM DEV_UW.T_UW_STATUS US
         WHERE US.UW_STATUS = MA.UW_STATUS) UW_STATUS,
       CM.VALIDATE_DATE,
       (SELECT PD.DECISION_DESC
          FROM DEV_CLM.T_PRODUCT_DECISION PD
         WHERE PD.DECISION_CODE = MA.DECISION_CODE) DECISION_CODE,
        CASE WHEN MA.DECISION_CODE = '60'
                  THEN  
                    (SELECT RO.RENEW_OPTION_DESC
                   FROM DEV_UW.T_RENEW_OPTION RO
                  WHERE RO.RENEW_OPTION = '0')
                  ELSE 
                (SELECT RO.RENEW_OPTION_DESC
                   FROM DEV_UW.T_RENEW_OPTION RO
                  WHERE RO.RENEW_OPTION = TO_CHAR(MB.RENEW))
                  END RENEW,
       TER.UW_SUBMIT_TIME,
       MA.UW_BUSI_ID,
      (CASE WHEN  MA.BUSI_PROD_CODE IN ('00842000','00843000')
          THEN   (SELECT (SELECT SHJ.CATEGORY_NAME  FROM DEV_PAS.T_SH_JOB_CATEGORY SHJ
                         WHERE SHJ.CATEGORY_CODE = JCM.SH_JOB_CATEGORY)
                         FROM DEV_PAS.T_SH_JOB_MAPPING JCM
                         WHERE JCM.JOB_CODE = SST.JOB_CODE
                         AND JCM.RETURN_MARK = '1')
           ELSE   (SELECT TJC.JOB_CATEGORY_NAME
                   FROM DEV_PAS.T_JOB_CATEGORY TJC
                   WHERE TJC.JOB_CATEGORY_CODE = SST.JOB_CATEGORY) END
        ) JOB_CATEGORY,
       (CASE WHEN MA.BUSI_PROD_CODE IN ('00842000','00843000')
          THEN    (SELECT (SELECT JU.JOB_UW_LEVEL_NAME  FROM DEV_PAS.T_JOB_UNDERWRITE JU
                          WHERE JU.JOB_UW_LEVEL_CODE = JCM.SH_JOB_CATEGORY)
                          FROM DEV_PAS.T_SH_JOB_MAPPING JCM
                          WHERE JCM.JOB_CODE = SST.JOB_CODE
                          AND JCM.RETURN_MARK = '1')
           ELSE  (SELECT JU.JOB_UW_LEVEL_NAME
                  FROM DEV_PAS.T_JOB_UNDERWRITE JU
                  WHERE JU.JOB_UW_LEVEL_CODE = SST.JOB_UW_LEVEL) END
        ) JOB_UW_LEVEL,
       MA.POLICY_CODE,
       TER.UW_SOURCE_TYPE,
       MB.BUSI_ITEM_ID,
       MA.IS_DISPLAY
  FROM DEV_UW.T_UW_BUSI_PROD MA
 LEFT JOIN DEV_UW.T_CONTRACT_BUSI_PROD MB    ON
     MA.UW_ID=MB.UW_ID AND MA.APPLY_CODE=MB.Apply_Code  AND  MA.BUSI_ITEM_ID = MB.BUSI_ITEM_ID
 LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
    ON MA.APPLY_CODE = CM.APPLY_CODE
 LEFT JOIN DEV_UW.T_UW_MASTER TER
    ON TER.UW_ID = MA.UW_ID
 LEFT JOIN DEV_UW.T_UW_PRODUCT UCT
    ON MA.UW_BUSI_ID = UCT.UW_BUSI_ID
  LEFT JOIN (SELECT ST.UW_ID, JC.JOB_CATEGORY, JC.JOB_UW_LEVEL,BI.BUSI_ITEM_ID,JC.JOB_CODE
               FROM DEV_UW.T_INSURED_LIST ST,
                    DEV_PAS.T_CUSTOMER    MER,
                    DEV_PAS.T_JOB_CODE    JC,
                    DEV_UW.T_BENEFIT_INSURED  BI 
              WHERE MER.CUSTOMER_ID = ST.CUSTOMER_ID AND BI.INSURED_ID=ST.LIST_ID AND BI.UW_ID=ST.UW_ID AND BI.ORDER_ID=1
                AND JC.JOB_CODE = MER.JOB_CODE) SST
    ON SST.UW_ID = MA.UW_ID AND SST.BUSI_ITEM_ID=MB.BUSI_ITEM_ID
 WHERE 1 = 1
        ]]>
        <include refid="insuraUwDetailQryCondition" />
    </select>
    
    <sql id="insuraUwDetailQryCondition">
        <![CDATA[ AND MA.UW_ID = #{uw_id,jdbcType=NUMERIC} ]]>
        <![CDATA[ AND MA.UW_BUSI_ID = #{uw_busi_id,jdbcType=NUMERIC} ]]>
    </sql>
    <select id="insuraAutoUwInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        SELECT RULE_STATUS,NOTES,RULE_MAG
         FROM DEV_UW.T_UW_BUSI_PROD MA
         INNER JOIN DEV_UW.T_UW_AUTO UA  ON MA.UW_ID=UA.UW_ID AND MA.APPLY_CODE = UA.APPLY_CODE 
         INNER JOIN DEV_UW.T_RULE_RESULT RR ON RR.AUTO_ID=UA.AUTO_ID
        <include refid="insuraUwDetailQryCondition" />
    </select>
    <!-- 获取险种核保信息 加费信息 -->
    
     <select id="uwExtraPremsQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
SELECT (SELECT TPL.PRODUCT_NAME
          FROM DEV_PDS.T_PRODUCT_LIFE TPL
         WHERE TO_CHAR(TPL.PRODUCT_ID) = SS.PRODUCT_ID) PRODUCT_CODE,
       NB.VALIDATE_DATE,
       (SELECT DRO.REASON_OPTION_NAME
          FROM DEV_UW.T_UW_DECISION_REASON_OPTION DRO
         WHERE DRO.REASON_OPTION_CODE = JJ.REASON_OPTION) REASON_OPTION,
       (SELECT AA.ARITH_DESC
          FROM DEV_UW.T_ADD_ARITH AA
         WHERE AA.ADD_ARITH = SS.ADD_ARITH) ADD_ARITH,
       (SELECT ET.EXTRA_NAME
          FROM DEV_UW.T_EXTRA_TYPE ET
         WHERE ET.EXTRA_TYPE = SS.EXTRA_TYPE) EXTRA_TYPE,
       SS.EM_VALUE,
       SS.EXTRA_PERC,
       SS.EXTRA_PREM AS EXTRA_PARA,
       SS.APPLY_CODE,
       SS.START_DATE,
       SS.END_DATE,
       SS.EXTRA_PERIOD,
       SS.UW_BUSI_ID
  FROM (SELECT ZZ.POLICY_ID,
               ZZ.APPLY_CODE,
               NVL(
                (SELECT CC.PRODUCT_ID 
                 FROM DEV_UW.T_CONTRACT_PRODUCT CC 
                WHERE CC.UW_ID = #{uw_id} AND CC.ITEM_ID=Z.ITEM_ID),(SELECT PP.PRODUCT_ID
                 FROM DEV_PAS.T_CONTRACT_PRODUCT PP
                WHERE PP.ITEM_ID = Z.ITEM_ID)) PRODUCT_ID,
               Z.UW_PRD_ID,
               Z.EXTRA_TYPE,
               Z.ADD_ARITH,
               Z.EM_VALUE,
               Z.EXTRA_PERC,
               Z.EXTRA_PARA,
               Z.START_DATE,
               Z.END_DATE,
               Z.EXTRA_PERIOD,
               Z.UW_ID,
               Z.POLICY_CODE,
               Z.BUSI_ITEM_ID,
               Z.UW_BUSI_ID,
               Z.EXTRA_PREM
          FROM DEV_UW.T_UW_EXTRA_PREM Z, DEV_UW.T_UW_PRODUCT ZZ
         WHERE Z.UW_ID = ZZ.UW_ID
           AND Z.ITEM_ID = ZZ.ITEM_ID
              
           AND Z.BUSI_ITEM_ID = ZZ.BUSI_ITEM_ID) SS
     LEFT JOIN (select * from DEV_UW.T_UW_DECISION_REASON where REASON_TYPE = '2') JJ ON  SS.UW_PRD_ID = JJ.UW_PRD_ID
      INNER JOIN  DEV_NB.T_NB_CONTRACT_MASTER NB ON NB.APPLY_CODE = SS.APPLY_CODE
 WHERE 1=1
        ]]>
        <include refid="uwExtraPremsQryCondition" />
    </select>
    
    <sql id="uwExtraPremsQryCondition">
       <![CDATA[ and SS.uw_id = #{uw_id} 
       			AND SS.UW_BUSI_ID = #{uw_busi_id}
       ]]>
       <!--  <if test="ss.policy_code !=null "><![CDATA[ and ss.policy_code = #{policy_code} ]]></if>
        <if test="ss.busi_item_id !=null "><![CDATA[ and ss.busi_item_id = #{busi_item_id} ]]></if>--> 
    </sql>
    
    
     <!-- 获取险种核保信息 限额信息 -->
     <select id="uwLimitInfoQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
    SELECT (SELECT TPL.PRODUCT_NAME
          FROM DEV_PDS.T_PRODUCT_LIFE TPL
         WHERE TO_CHAR(TPL.PRODUCT_ID) = TTT.PRODUCT_ID) PRODUCT_CODE,
       NB.VALIDATE_DATE,
       CC.REASON_CONTENT,
       TTT.LIMIT_AMOUNT,
       TTT.UW_LIMIT_UNIT,
       TTT.COVERAGE_PERIOD,
       TTT.COVERAGE_YEAR,
       TTT.CHARGE_PERIOD,
       TTT.CHARGE_YEAR,
       TTT.STD_PREM
  FROM (SELECT 
               NVL(
                (SELECT CC.PRODUCT_ID 
                 FROM DEV_UW.T_CONTRACT_PRODUCT CC 
                WHERE CC.UW_ID = #{uw_id} AND CC.ITEM_ID=TT.ITEM_ID),(SELECT PP.PRODUCT_ID
                 FROM DEV_PAS.T_CONTRACT_PRODUCT PP
                WHERE PP.ITEM_ID = TT.ITEM_ID)) PRODUCT_ID,
  			   T.UW_BUSI_ID,
               TT.POLICY_ID,
               T.LIMIT_AMOUNT,
               T.UW_LIMIT_UNIT,
               T.COVERAGE_PERIOD,
               T.COVERAGE_YEAR,
               T.CHARGE_PERIOD,
               T.CHARGE_YEAR,
               T.STD_PREM,
               T.UW_PRD_ID,
               T.UW_ID,
               T.POLICY_CODE,
               T.BUSI_ITEM_ID,
               T.APPLY_CODE
          FROM DEV_UW.T_UW_LIMIT T, DEV_UW.T_UW_PRODUCT TT
         WHERE T.UW_ID = TT.UW_ID
           AND T.ITEM_ID = TT.ITEM_ID
           AND T.BUSI_ITEM_ID = TT.BUSI_ITEM_ID) TTT
 LEFT JOIN (select * from DEV_UW.T_UW_DECISION_REASON WHERE REASON_TYPE = '3') CC
    ON TTT.UW_PRD_ID = CC.UW_PRD_ID
 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER NB
    ON NB.APPLY_CODE = TTT.APPLY_CODE
 WHERE 1=1

        ]]>
        <include refid="uwLimitInfoQryCondition" />
    </select>
    
    <sql id="uwLimitInfoQryCondition">
        <![CDATA[ 
        and TTT.uw_id = #{uw_id} 
        AND TTT.UW_BUSI_ID = #{uw_busi_id}
        ]]>
        <!-- 
        <if test="ttt.policy_code !=null "><![CDATA[ and ttt.policy_code = #{policy_code} ]]></if>
        <if test="ttt.busi_item_id !=null "><![CDATA[ and ttt.busi_item_id = #{busi_item_id} ]]></if>
         -->
    </sql>
    
     <!-- 获取险种核保信息 特约信息 -->
    <select id="uwConditionQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
			  SELECT (SELECT TPL.PRODUCT_NAME
		          FROM DEV_PDS.T_PRODUCT_LIFE TPL
		         WHERE TO_CHAR(TPL.PRODUCT_ID) = NNN.PRODUCT_ID) PRODUCT_CODE,
		       NNN.CONDITION_DESC,
		       NB.VALIDATE_DATE,
		       NNN.UW_CONDITION_TYPE,
		       NNN.REASON_CONTENT,
		       CBP.BUSI_PROD_CODE, /**险种编码 */
		       TPL.PRODUCT_NAME, /**责任组名称 */
		       CM.POLICY_CODE,
		       bp.product_name_std,/**产品名 */
		       (CASE
		         WHEN CBP.MASTER_BUSI_ITEM_ID IS NOT NULL THEN
		          (SELECT BP.BUSI_PROD_CODE
		             FROM DEV_PAS.T_CONTRACT_BUSI_PROD BP
		            WHERE BP.BUSI_ITEM_ID = CBP.MASTER_BUSI_ITEM_ID)
		         WHEN CBP.MASTER_BUSI_ITEM_ID IS NULL THEN
		          ''
		       END) MASTER_PRODUCT_CODE, /**主险编码 */
		       (CASE
		         WHEN CBP.MASTER_BUSI_ITEM_ID IS NOT NULL THEN
		          (SELECT tbp.product_name_std
		             FROM DEV_PAS.T_CONTRACT_BUSI_PROD BP,
		                  DEV_PAS.T_BUSINESS_PRODUCT   TBP
		            WHERE BP.BUSI_PRD_ID = CBP.MASTER_BUSI_ITEM_ID
		              AND BP.BUSI_ITEM_ID = TBP.BUSINESS_PRD_ID)
		         WHEN CBP.MASTER_BUSI_ITEM_ID IS NULL THEN
		          ''
		       END) MASTER_PRODUCT_NAME, /**主险名称*/
		       (CASE
		         WHEN CM.FORMER_ID IS NOT NULL THEN
		          (SELECT C.POLICY_CODE
		             FROM DEV_PAS.T_CONTRACT_MASTER C
		            WHERE C.POLICY_ID = CM.FORMER_ID)
		         WHEN CM.FORMER_ID IS NULL THEN
		          ''
		       END) OLD_POLICY_CODE/**原保单号 */
		  FROM (SELECT NVL((SELECT CC.PRODUCT_ID
		                     FROM DEV_UW.T_CONTRACT_PRODUCT CC
		                    WHERE CC.UW_ID = N.UW_ID
		                      AND CC.ITEM_ID = NN.ITEM_ID AND CC.APPLY_CODE=N.APPLY_CODE),
		                   (SELECT PP.PRODUCT_ID
		                      FROM DEV_PAS.T_CONTRACT_PRODUCT PP
		                     WHERE PP.ITEM_ID = NN.ITEM_ID)) PRODUCT_ID,
		               NN.POLICY_ID,
		               N.CONDITION_DESC,
		               N.UW_CONDITION_TYPE,
		               TUDR.REASON_CONTENT,
		               N.UW_PRD_ID,
		               N.UW_ID,
		               N.POLICY_CODE,
		               N.APPLY_CODE,
		               N.BUSI_ITEM_ID,
		               N.UW_BUSI_ID
		          FROM DEV_UW.T_UW_CONDITION N
		         INNER JOIN DEV_UW.T_UW_PRODUCT NN
		            ON N.UW_ID = NN.UW_ID
		           AND N.ITEM_ID = NN.ITEM_ID
		           AND N.BUSI_ITEM_ID = NN.BUSI_ITEM_ID
		          LEFT JOIN (SELECT *
		                      FROM DEV_UW.T_UW_DECISION_REASON
		                     WHERE REASON_TYPE = '4') TUDR
		            ON N.UW_ID = TUDR.UW_ID
		            AND N.UW_BUSI_ID = TUDR.UW_BUSI_ID) NNN
		 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER NB
		    ON NB.APPLY_CODE = NNN.APPLY_CODE
		  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
		    ON CM.POLICY_CODE = NNN.POLICY_CODE
		  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
		    ON CBP.POLICY_CODE = CM.POLICY_CODE
		    AND CBP.BUSI_ITEM_ID = NNN.BUSI_ITEM_ID
		  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
		    ON CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
		  LEFT JOIN DEV_PDS.T_PRODUCT_LIFE TPL
		    ON CP.ITEM_ID = TPL.PRODUCT_ID
		  LEFT JOIN dev_pas.T_BUSINESS_PRODUCT bp
		    ON cbp.busi_prd_id = bp.business_prd_id
		 WHERE 1 = 1
        ]]>
        <include refid="uwConditionQryCondition" />
    </select>
    
    <sql id="uwConditionQryCondition">
        <![CDATA[ and NNN.uw_id = #{uw_id} ]]>
        <if test="uw_busi_id !=null and uw_busi_id!=''"><![CDATA[ and nnn.UW_BUSI_ID = #{uw_busi_id} ]]></if>
        <!-- 
        <if test="nnn.policy_code !=null "><![CDATA[ and nnn.policy_code = #{policy_code} ]]></if>
        
         -->
    </sql>
    <!--获取险种编码  -->
    <select id="busiProdCodeQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
			  SELECT UBP.BUSI_PROD_CODE, /*险种编码*/
			       TPL.PRODUCT_NAME, /*责任组名称*/
			       NB.VALIDATE_DATE, /*生效日期*/
			       TUDR.REASON_CONTENT, /*特约原因*/
			       BP.PRODUCT_NAME_STD, /*产品名*/
			       CM.POLICY_CODE,
			       (CASE
			         WHEN CBP.MASTER_BUSI_ITEM_ID IS NOT NULL THEN
			          (SELECT BP.BUSI_PROD_CODE
			             FROM DEV_PAS.T_CONTRACT_BUSI_PROD BP
			            WHERE BP.BUSI_ITEM_ID = CBP.MASTER_BUSI_ITEM_ID)
			         WHEN CBP.MASTER_BUSI_ITEM_ID IS NULL THEN
			          ''
			       END) MASTER_PRODUCT_CODE, /**主险编码 */
			       (CASE
			         WHEN CBP.MASTER_BUSI_ITEM_ID IS NOT NULL THEN
			          (SELECT TBP.PRODUCT_NAME_STD
			             FROM DEV_PAS.T_CONTRACT_BUSI_PROD BP,
			                  DEV_PAS.T_BUSINESS_PRODUCT   TBP
			            WHERE BP.BUSI_PRD_ID = CBP.MASTER_BUSI_ITEM_ID
			              AND BP.BUSI_ITEM_ID = TBP.BUSINESS_PRD_ID)
			         WHEN CBP.MASTER_BUSI_ITEM_ID IS NULL THEN
			          ''
			       END) MASTER_PRODUCT_NAME, /**主险名称*/
			       (CASE
			         WHEN CM.FORMER_ID IS NOT NULL THEN
			          (SELECT C.POLICY_CODE
			             FROM DEV_PAS.T_CONTRACT_MASTER C
			            WHERE C.POLICY_ID = CM.FORMER_ID)
			         WHEN CM.FORMER_ID IS NULL THEN
			          ''
			       END) OLD_POLICY_CODE /**原保单号 */
			  FROM DEV_UW.T_UW_BUSI_PROD UBP
			  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
			    ON CP.BUSI_ITEM_ID = UBP.BUSI_ITEM_ID
			  LEFT JOIN DEV_PDS.T_PRODUCT_LIFE TPL
			    ON CP.PRODUCT_ID = TPL.PRODUCT_ID
			  LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NB
			    ON UBP.APPLY_CODE = NB.APPLY_CODE
			  LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
			    ON CM.POLICY_CODE = UBP.POLICY_CODE
			  LEFT JOIN DEV_UW.T_UW_DECISION_REASON TUDR
			    ON TUDR.UW_BUSI_ID = UBP.UW_BUSI_ID
			   AND TUDR.REASON_TYPE = '4'
			  LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
			    ON UBP.POLICY_CODE = CBP.POLICY_CODE
			    AND cbp.busi_item_id = ubp.busi_item_id
			  LEFT JOIN DEV_PAS.T_BUSINESS_PRODUCT BP
			    ON CBP.BUSI_PRD_ID = BP.BUSINESS_PRD_ID
			 WHERE 1 = 1
        ]]>
        <![CDATA[ and UBP.uw_id = #{uw_id} ]]>
        <if test="uw_busi_id !=null and uw_busi_id!=''"><![CDATA[ and UBP.UW_BUSI_ID = #{uw_busi_id} ]]></if>
    </select>
    
      <!-- 获取险种核保信息 标准/拒保/延期信息-->
     <select id="uwProductQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[SELECT CASE
		         WHEN CTT1.PRODUCT_ID IS NOT NULL THEN
		          CTT1.PRODUCT_ID
		         ELSE
		          CTT2.PRODUCT_ID
		       END PRODUCT_ID,
		       NB.VALIDATE_DATE,
		       CASE
		         WHEN CTT1.EXPIRY_DATE IS NOT NULL THEN
		          CTT1.EXPIRY_DATE
		         ELSE
		          CTT2.EXPIRY_DATE
		       END EXPIRY_DATE,
		       CT.DECISION_CODE,
		       CT.UW_PRD_ID,
		       CT.UW_ID,
		       CT.POLICY_CODE,
		       CT.BUSI_ITEM_ID,
		       PP.REASON_OPTION,
		      
		       PP.REASON_CONTENT,
		       CT.POSTPONED_MONTHS
		  FROM DEV_UW.T_UW_PRODUCT CT
		  LEFT JOIN (SELECT BUSI_ITEM_ID,
		                    EXPIRY_DATE,
		                    PRODUCT_ID,
		                    ITEM_ID,
		                    POLICY_CODE,
		                    APPLY_CODE
		               FROM DEV_PAS.T_CONTRACT_PRODUCT) CTT1
		    ON CTT1.APPLY_CODE = CT.APPLY_CODE
		   AND CTT1.POLICY_CODE = CT.POLICY_CODE
		   AND CT.ITEM_ID = CTT1.ITEM_ID
		  LEFT JOIN (SELECT BUSI_ITEM_ID,
		                    EXPIRY_DATE,
		                    PRODUCT_ID,
		                    ITEM_ID,
		                    POLICY_CODE,
		                    APPLY_CODE,
		                    UW_ID
		               FROM DEV_UW.T_CONTRACT_PRODUCT) CTT2
		    ON CTT2.UW_ID = CT.UW_ID
		   AND CT.ITEM_ID = CTT2.ITEM_ID
		 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER NB
		    ON NB.APPLY_CODE = CT.APPLY_CODE
		 INNER JOIN (SELECT *
		               FROM DEV_UW.T_UW_DECISION_REASON
		              WHERE (REASON_TYPE = '5' OR REASON_TYPE = '6')) PP
		    ON PP.UW_PRD_ID = CT.UW_PRD_ID
		 WHERE 1 = 1
        ]]>
        <include refid="uwProductQryCondition" />
    </select>
    
    <sql id="uwProductQryCondition">
        <![CDATA[ 
	        AND CT.UW_ID = #{uw_id} 
	        AND CT.UW_BUSI_ID = #{uw_busi_id}
        ]]>
    </sql>
    
    
    <!-- 获取被保人信息 -->
    <select id="insuredInfoQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
SELECT DISTINCT C.CUSTOMER_NAME,
       C.CUSTOMER_ID,
       C.CUSTOMER_GENDER,
       C.CUSTOMER_CERT_TYPE,
       C.CUSTOMER_CERTI_CODE,
       (SELECT JC.JOB_NAME
          FROM DEV_PAS.T_JOB_CODE JC
         WHERE JC.JOB_CODE = IL.JOB_CODE) JOB_CODE,
         (CASE WHEN UBP.BUSI_PROD_CODE IN ('00557000','00558000','00842000','00843000') 
               		 THEN (SELECT JC.JOB_NAME FROM DEV_PAS.T_JOB_CODE JC WHERE JC.JOB_CODE = IL.JOB_CODE)
               		 ELSE '' END) SH_JOB_NAME,
       C.CUSTOMER_BIRTHDAY,
       UBP.DECISION_CODE,
       IL.SMOKING,
       (SELECT COUNT(8) FROM DEV_UW.T_PENOTICE P WHERE P.UW_ID = UBP.UW_ID AND P.CUSTOMER_ID = IL.Customer_Id ) AS PENOTICE,
       (CASE WHEN
                 pil.insured_age IS NOT NULL OR pil.insured_age !=''
                 THEN pil.insured_age
                   ELSE
                     TRUNC (MONTHS_BETWEEN(ncm.apply_date,c.customer_birthday)/12) 
                     END) AS insured_age,
       UM.UW_SOURCE_TYPE,
       (SELECT UIL.INSURED_BODY_OPTION
          FROM DEV_UW.T_INSURED_LIST UIL, DEV_UW.T_UW_MASTER UMI
         WHERE UIL.UW_ID = UMI.UW_ID
           AND UIL.APPLY_CODE = UP.APPLY_CODE
           AND UMI.BIZ_CODE = UP.APPLY_CODE
           AND UIL.CUSTOMER_ID = IL.CUSTOMER_ID) AS INSURED_BODY_OPTION,
       (SELECT UIL.INSURED_BODY_OPTION_ORIGINAL
          FROM DEV_UW.T_INSURED_LIST UIL, DEV_UW.T_UW_MASTER UMI
         WHERE UIL.UW_ID = UBP.UW_ID
           AND UIL.APPLY_CODE = UP.APPLY_CODE
           AND UMI.BIZ_CODE = UP.APPLY_CODE
           AND UIL.CUSTOMER_ID = IL.CUSTOMER_ID) AS INSURED_BODY_OPTION_ORIGINAL,
       IL.ORDER_ID, IL.INPUT_SEQUENCE
  FROM DEV_UW.T_UW_BUSI_PROD UBP
 INNER JOIN DEV_UW.T_UW_MASTER UM 
   ON UM.UW_ID=UBP.UW_ID
 INNER JOIN DEV_UW.T_UW_POLICY UP ON UM.UW_ID=UP.UW_ID
 INNER JOIN 
 (SELECT DISTINCT CUSTOMER_ID,SMOKING,APPLY_CODE,JOB_CODE,ORDER_ID,INPUT_SEQUENCE FROM (
 SELECT nil.CUSTOMER_ID,nil.SMOKING,nil.APPLY_CODE,NIL.JOB_CODE,NIL.ORDER_ID, NIL.INPUT_SEQUENCE
                       FROM dev_nb.t_nb_insured_list nil
                      WHERE nil.apply_code=#{apply_code}
                       AND NOT EXISTS(
                      SELECT IL.POLICY_CODE FROM DEV_PAS.T_INSURED_LIST IL WHERE IL.APPLY_CODE=#{apply_code}
                      )
 UNION 
SELECT IL.CUSTOMER_ID,
       IL.SMOKING,
       IL.APPLY_CODE,
       IL.JOB_CODE,
       CASE WHEN (SELECT COUNT(1)
                 FROM DEV_PAS.T_BENEFIT_INSURED TBI
                WHERE BI.POLICY_ID = BI.POLICY_ID
                  AND TBI.INSURED_ID =
                      BI.INSURED_ID
                  AND TBI.ORDER_ID = '2') > 0 THEN
          2
         ELSE
          BI.ORDER_ID
       END ORDER_ID,
       IL.INPUT_SEQUENCE
  FROM DEV_PAS.T_INSURED_LIST    IL,
       DEV_PAS.T_BENEFIT_INSURED BI
 WHERE IL.POLICY_CODE = BI.POLICY_CODE
   AND IL.LIST_ID = BI.INSURED_ID 
   AND  IL.APPLY_CODE=#{apply_code}) TEMP) IL/*迁移数据被保人抄单表中没有数据，需要和保单表中的数据关联*/
    ON UP.APPLY_CODE = IL.APPLY_CODE
 LEFT JOIN DEV_PAS.T_CUSTOMER C
   ON C.CUSTOMER_ID = IL.CUSTOMER_ID
 LEFT JOIN dev_nb.t_nb_contract_master ncm
       ON ncm.apply_code = up.apply_code
 LEFT JOIN DEV_PAS.T_INSURED_LIST pil
    ON pil.apply_code = up.apply_code
    AND PIL.CUSTOMER_ID = IL.CUSTOMER_ID
 WHERE 1 = 1 
        ]]>
        <include refid="insuredInfoQryCondition" />
        
   <![CDATA[  ORDER BY IL.INPUT_SEQUENCE, IL.ORDER_ID]]>
        
    </select>
    <!--获取被保人年龄  -->
	<select id="insuredAgeInfo" resultType="java.util.Map"
	        parameterType="java.util.Map">
	        <![CDATA[ 
			SELECT DISTINCT C.CUSTOMER_NAME,
			       C.CUSTOMER_ID,
			       C.CUSTOMER_GENDER,
			       C.CUSTOMER_CERT_TYPE,
			       C.CUSTOMER_CERTI_CODE,
			       (MONTHS_BETWEEN((SELECT SYSDATE FROM DUAL),
                                          C.CUSTOMER_BIRTHDAY)/12) AS INSURED_AGE
			  FROM DEV_PAS.T_CUSTOMER C
			 WHERE 1 = 1 
			 AND C.CUSTOMER_ID = #{customer_id}
	        ]]>
	    </select>
      <!-- 获取投保人信息 -->
    <select id="policyHoderInfoQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
 SELECT T.*,TRUNC (MONTHS_BETWEEN(NVL(APPLY_DATE,NB_APPLY_DATE),T.CUSTOMER_BIRTHDAY)/12) AS INSURED_AGE FROM(
        SELECT DISTINCT C.CUSTOMER_NAME,
               C.CUSTOMER_ID,
               C.CUSTOMER_GENDER,
               C.CUSTOMER_CERT_TYPE,
               C.CUSTOMER_CERTI_CODE,
               (SELECT JC.JOB_NAME
                  FROM DEV_PAS.T_JOB_CODE JC
                 WHERE JC.JOB_CODE = IL.JOB_CODE) JOB_CODE,
                 (CASE WHEN UBP.BUSI_PROD_CODE IN ('00557000','00558000','00842000','00843000') 
               		 THEN (SELECT JC.JOB_NAME FROM DEV_PAS.T_JOB_CODE JC WHERE JC.JOB_CODE = IL.JOB_CODE)
               		 ELSE '' END) SH_JOB_NAME, 
               C.CUSTOMER_BIRTHDAY,
               (SELECT APPLY_DATE FROM DEV_PAS.T_CONTRACT_MASTER CM WHERE CM.APPLY_CODE=#{apply_code}) AS APPLY_DATE,
               (SELECT APPLY_DATE FROM DEV_NB.T_NB_CONTRACT_MASTER CM WHERE CM.APPLY_CODE=#{apply_code}) AS NB_APPLY_DATE,
               UBP.DECISION_CODE,
               IL.SMOKING,
               (SELECT COUNT(8) FROM DEV_UW.T_PENOTICE P WHERE P.UW_ID = UBP.UW_ID) AS PENOTICE,
               UM.UW_SOURCE_TYPE,
                (SELECT UIL.INSURED_BODY_OPTION
                  FROM DEV_UW.T_INSURED_LIST UIL,
                       DEV_UW.T_UW_MASTER    UMI
                 WHERE UIL.UW_ID = UMI.UW_ID
                   AND UIL.APPLY_CODE = UP.APPLY_CODE
                   AND UMI.BIZ_CODE = UP.APPLY_CODE
                   AND UIL.CUSTOMER_ID = IL.CUSTOMER_ID
                   AND UIL.RELATION_TO_PH = '00') AS INSURED_BODY_OPTION,
               (SELECT UIL.INSURED_BODY_OPTION_ORIGINAL
                  FROM DEV_UW.T_INSURED_LIST UIL,
                       DEV_UW.T_UW_MASTER    UMI
                 WHERE UIL.UW_ID = UBP.UW_ID
                   AND UIL.APPLY_CODE = UP.APPLY_CODE
                   AND UMI.BIZ_CODE = UP.APPLY_CODE
                   AND UIL.CUSTOMER_ID = IL.CUSTOMER_ID
                   AND UIL.RELATION_TO_PH = '00') AS INSURED_BODY_OPTION_ORIGINAL,
              
               UBP.ORGAN_CODE
          FROM DEV_UW.T_UW_BUSI_PROD UBP
         INNER JOIN DEV_UW.T_UW_MASTER UM 
           ON UM.UW_ID=UBP.UW_ID
         INNER JOIN DEV_UW.T_UW_POLICY UP ON UM.UW_ID=UP.UW_ID
         INNER JOIN 
         (SELECT DISTINCT CUSTOMER_ID,SMOKING,APPLY_CODE,JOB_CODE FROM (
         SELECT NPH.CUSTOMER_ID,NPH.SMOKING,NPH.APPLY_CODE,NPH.JOB_CODE FROM 
          DEV_NB.T_NB_POLICY_HOLDER  NPH 
         WHERE APPLY_CODE=#{apply_code}
         AND  NOT EXISTS(
               SELECT PH.POLICY_CODE FROM DEV_PAS.T_POLICY_HOLDER PH WHERE APPLY_CODE=#{apply_code}
               )
            UNION 
         SELECT IL.CUSTOMER_ID,IL.SMOKING,IL.APPLY_CODE,IL.JOB_CODE FROM DEV_PAS.T_POLICY_HOLDER IL WHERE IL.APPLY_CODE=#{apply_code}) TEMP) IL/*迁移数据被保人抄单表中没有数据，需要和保单表中的数据关联*/
            ON UP.APPLY_CODE = IL.APPLY_CODE
         LEFT JOIN DEV_PAS.T_CUSTOMER C
           ON C.CUSTOMER_ID = IL.CUSTOMER_ID
         WHERE 1 = 1 
                ]]>
        <include refid="insuredInfoQryCondition" />
        <![CDATA[  )T]]>
    </select>
    <!--获取投保人年龄  -->
    <select id="policyHoderAgeInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
	        SELECT DISTINCT C.CUSTOMER_NAME,
			       C.CUSTOMER_ID,
			       C.CUSTOMER_GENDER,
			       C.CUSTOMER_CERT_TYPE,
			       C.CUSTOMER_CERTI_CODE,
			       (MONTHS_BETWEEN((SELECT SYSDATE FROM DUAL),
                                          C.CUSTOMER_BIRTHDAY)/12) AS INSURED_AGE
	          FROM  DEV_PAS.T_CUSTOMER C
	         WHERE 1 = 1 
	         AND C.CUSTOMER_ID = #{customer_id}
           ]]>
    </select>
    <sql id="insuredInfoQryCondition">
        <if test="uw_id != null and uw_id!='' ">
    		<![CDATA[ AND UBP.UW_ID =  #{uw_id,jdbcType=NUMERIC} ]]>
    	</if>
    	<if test="uw_busi_id !=null and uw_busi_id!=''">
    		<![CDATA[ AND UBP.uw_busi_id =  #{uw_busi_id} ]]>
    	</if>
    	<if test="uw_source_type!=null and uw_source_type!=''">
    		<![CDATA[ AND UM.UW_SOURCE_TYPE = #{uw_source_type } ]]>
    	</if>
        <if test=" apply_code != null and apply_code != ''  ">
        	<![CDATA[ AND UP.APPLY_CODE =  #{apply_code} ]]>
        </if>
    </sql>
    
    
    
    <!-- 获取复议信息 -->
    <select id="reconsiderInfoQry" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
      SELECT
 EW.UW_ID,
 EW.REVIEW_REASON,
 EW.ADVICE_RESULT,
 EW.REVIEW_APP_USER,
 
 EW.REVIEW_REPLY_USER,
 
 EW.REVIEW_APP_DATE,
 
 EW.PLUS_SIGN,
 
 EW.POLICY_CATE,
 
 EW.REVIEW_ADVICE,ew.plus_note 

  FROM DEV_UW.T_UW_POLICY_REVIEW EW 
  
  WHERE 1=1 
     
        ]]>
        <include refid="reconsiderInfoQryCondition" />
    </select>
    
    <sql id="reconsiderInfoQryCondition">
        <![CDATA[ and EW.uw_id = #{uw_id,jdbcType=NUMERIC} ]]>
    </sql>
    
      <!--获取风险保额信息 -->
      <select id="getRiskAmount3" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT * FROM (
   SELECT ra.customer_id,RA.APPLY_CODE,RA.BUSI_ITEM_ID,ART.RISK_DESC RISK_TYPE,RA.RISK_AMOUNT,RA.POLICY_CODE,RA.AMOUNT_STATUS,CP.LIABILITY_STATE 
FROM DEV_PAS.T_RISK_AMOUNT RA 
JOIN  DEV_PAS.T_CONTRACT_PRODUCT CP ON RA.APPLY_CODE = CP.APPLY_CODE AND RA.POLICY_CODE = CP.POLICY_CODE AND RA.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
JOIN  DEV_PAS.T_AGGREGATION_RISK_TYPE ART ON ART.RISK_TYPE = RA.RISK_TYPE )R
WHERE R.LIABILITY_STATE IS NULL OR R.LIABILITY_STATE ='0' OR  R.LIABILITY_STATE ='2' OR R.LIABILITY_STATE ='4' OR  R.LIABILITY_STATE ='9'  
        ]]>
      <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND R.APPLY_CODE =  #{apply_code} ]]></if>
      <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND R.CUSTOMER_ID =  #{customer_id} ]]></if>
    </select>
     <select id="getRiskAmount" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT * FROM (
   SELECT ra.customer_id,RA.APPLY_CODE,RA.BUSI_ITEM_ID,ART.RISK_DESC RISK_TYPE,RA.RISK_AMOUNT,RA.POLICY_CODE,RA.AMOUNT_STATUS,CP.LIABILITY_STATE 
FROM DEV_PAS.T_RISK_AMOUNT RA 
left JOIN  DEV_PAS.T_CONTRACT_PRODUCT CP ON RA.APPLY_CODE = CP.APPLY_CODE AND RA.POLICY_CODE = CP.POLICY_CODE AND RA.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
left JOIN  DEV_PAS.T_AGGREGATION_RISK_TYPE ART ON ART.RISK_TYPE = RA.RISK_TYPE )R
WHERE R.AMOUNT_STATUS ='1' 
        ]]>
        <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND R.APPLY_CODE =  #{apply_code} ]]></if>
        <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND R.CUSTOMER_ID =  #{customer_id} ]]></if>
    </select>
    <select id="getRiskAmount2" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT * FROM (
   SELECT ra.customer_id,RA.APPLY_CODE,RA.BUSI_ITEM_ID,ART.RISK_DESC RISK_TYPE,RA.RISK_AMOUNT,RA.POLICY_CODE,RA.AMOUNT_STATUS,CP.LIABILITY_STATE 
FROM DEV_PAS.T_RISK_AMOUNT RA 
left JOIN  DEV_PAS.T_CONTRACT_PRODUCT CP ON RA.APPLY_CODE = CP.APPLY_CODE AND RA.POLICY_CODE = CP.POLICY_CODE AND RA.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
left JOIN  DEV_PAS.T_AGGREGATION_RISK_TYPE ART ON ART.RISK_TYPE = RA.RISK_TYPE )R
WHERE R.AMOUNT_STATUS ='2' 
        ]]>
      <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND R.APPLY_CODE =  #{apply_code} ]]></if>
      <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND R.CUSTOMER_ID =  #{customer_id} ]]></if>
    </select>
    
   <!-- 获取风险保额概览信息 -->
    <select id="getRiskAmountSumary" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
         select risk_type,sum(TERMINATED_RISK) as TERMINATED_RISK,sum(ACTIVE_RISK) as ACTIVE_RISK,
         sum(MINORS_AMOUNT) as MINORS_AMOUNT,
         (SELECT 
         (
         CASE WHEN UM.UW_SOURCE_TYPE='1' THEN 
         (SELECT APPLY_DATE FROM DEV_NB.T_NB_CONTRACT_MASTER CM WHERE CM.APPLY_CODE=UM.BIZ_CODE AND ROWNUM=1)
         WHEN UM.UW_SOURCE_TYPE='2' THEN 
         (
         SELECT A.APPLY_TIME
                FROM DEV_PAS.T_CS_APPLICATION A
               INNER JOIN DEV_PAS.T_CS_ACCEPT_CHANGE B
                  ON A.CHANGE_ID = B.CHANGE_ID
          INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE C
                  ON B.ACCEPT_ID = C.ACCEPT_ID
                 AND A.CHANGE_ID = C.CHANGE_ID
          INNER JOIN DEV_PAS.T_CS_INSURED_LIST IL ON IL.CHANGE_ID=B.CHANGE_ID AND IL.OLD_NEW=1
          INNER JOIN DEV_UW.T_UW_MASTER UM ON UM.BIZ_CODE=B.ACCEPT_CODE
          AND IL.CUSTOMER_ID=#{customer_id}  AND UM.UW_ID=#{uw_id} AND ROWNUM=1
         )END ) AS APPLY_DATE
          FROM DEV_UW.T_UW_MASTER UM WHERE UM.UW_ID=#{uw_id}) AS APPLY_DATE
  from(
            SELECT RISK_TYPE, /** 风险类别*/
		       NVL(SUM(LAPSERISK), 0) AS TERMINATED_RISK, /*失效风险保额*/
		       NVL(SUM(ACTIVERISK), 0) AS ACTIVE_RISK, /*有效风险保额*/
		       (SELECT NVL(SUM(TR.RISK_AMOUNT), 0)
		          FROM DEV_PAS.T_RISK_AMOUNT TR
		          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TC
		            ON TR.ITEM_ID = TC.ITEM_ID
		         WHERE TR.CUSTOMER_ID = #{customer_id}
		           AND NOT (TR.AMOUNT_STATUS = 3 OR TC.LIABILITY_STATE = 3)
		           AND TR.RISK_TYPE = '14') AS MINORS_AMOUNT
		  FROM (SELECT RISK_TYPE,
		               PRODUCT_ID,
		               INTERNAL_CODE,
		               APPLY_DATE,
		               CASE
		                 WHEN AMOUNT_STATUS = 3 THEN
		                  SUM(RISK_AMOUNT)
		               END TERMINATEDRISK,
		               CASE
		                 WHEN AMOUNT_STATUS = 4 THEN
		                  SUM(RISK_AMOUNT)
		               END LAPSERISK,
		               CASE
		                 WHEN AMOUNT_STATUS = 1 THEN
		                  SUM(RISK_AMOUNT)
		               END ACTIVERISK
		          FROM (SELECT SUM(T.RISK_AMOUNT) RISK_AMOUNT,
		                       T.RISK_TYPE,
		                       T.AMOUNT_STATUS,
		                       T.PRODUCT_ID,
		                       T.APPLY_DATE,
		                       T.INTERNAL_CODE
		                  FROM (SELECT TC.PRODUCT_ID,
		                               TR.INTERNAL_CODE,
		                               TR.RISK_TYPE,
		                               TC.APPLY_DATE,
		                               SUM(TR.RISK_AMOUNT) AS RISK_AMOUNT,
		                               CASE
		                                 WHEN TR.AMOUNT_STATUS = 3 OR
		                                      TC.LIABILITY_STATE = 3 THEN
		                                  3
		                                 WHEN TC.LIABILITY_STATE = 4 THEN
		                                  4
		                                 ELSE
		                                  1
		                               END AS AMOUNT_STATUS
		                          FROM (SELECT * FROM 
                            DEV_PAS.T_RISK_AMOUNT TR
                            LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM ON TR.APPLY_CODE=CM.APPLY_CODE
                            LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON TR.APPLY_CODE=NCM.APPLY_CODE 
                            WHERE 
                            (NOT(TR.RISK_TYPE='16' OR TR.RISK_TYPE='17') AND (NOT(CM.LIABILITY_STATE=3 OR NCM.PROPOSAL_STATUS='08' OR NCM.PROPOSAL_STATUS='12' OR NCM.PROPOSAL_STATUS='28')
                            OR (CM.LIABILITY_STATE IS NULL AND NCM.PROPOSAL_STATUS IS NOT NULL)))  ]]>
                            
                        	<if test="query_busi_code==1">
                               and  (exists (
                                  select  1 from dev_nb.T_NB_CONTRACT_BUSI_PROD tbp  
                                where tbp.product_code in ('00425000','00952000')
                                and NCM.apply_code=tbp.apply_code
                              )  or exists (
                                  select  1 from dev_pas.T_CONTRACT_BUSI_PROD tbp  
                                where tbp.busi_prod_code in ('00425000','00952000')
                                and CM.apply_code=tbp.apply_code
                              ) )   
                            </if>
                                   <![CDATA[ 
                            ) TR
		                          LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TC
		                            ON TR.ITEM_ID = TC.ITEM_ID
		                         WHERE EXISTS
		                         (SELECT R.CUSTOMER_ID
		                                  FROM DEV_PAS.T_CUSTOMER R
		                                 WHERE R.CUSTOMER_ID = #{customer_id}
		                                   AND R.CUSTOMER_ID = TR.CUSTOMER_ID)
		                         GROUP BY RISK_TYPE,
		                                  TC.LIABILITY_STATE,
		                                  TR.AMOUNT_STATUS,
		                                  TC.PRODUCT_ID,
		                                  TR.INTERNAL_CODE,
		                                  TC.APPLY_DATE,
		                                  TR.RISK_TYPE) T
		                 GROUP BY T.RISK_TYPE,
		                          T.AMOUNT_STATUS,
		                          T.PRODUCT_ID,
		                          T.APPLY_DATE,
		                          T.INTERNAL_CODE)
		         GROUP BY RISK_TYPE,
		                  AMOUNT_STATUS,
		                  PRODUCT_ID,
		                  APPLY_DATE,
		                  INTERNAL_CODE) A
		 WHERE RISK_TYPE != '14'
		 GROUP BY RISK_TYPE
         union
     select risk_type,0 as terminated_risk,sum(risk_amount) as active_risk,sum(minors_amount) from (    
      select BP.PRODUCT_NAME_SYS AS BUSI_PROD_NAME,risk_type,busi_prod_code,risk_amount, minors_amount from (             
 select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
         cra.risk_type,
         cra.busi_prod_code,
         (select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) from  (select * from dev_pas.t_cs_risk_amount cra where not (cra.risk_type='16' or cra.risk_type='17')) cra ,
             dev_pas.t_cs_accept_change cac
       where cra.amount_status<>3 
         and cac.change_id = cra.change_id
         and cra.customer_id =  #{customer_id}
             and cac.accept_status in ('07', '08', '09', '10', '11', '13') and  cra.risk_type=14   ]]>
              <if test="query_busi_code==1">
          and exists (
                 select  1  from dev_pas.t_cs_policy_change c ,
               DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL tc where  
                cac.accept_id=c.accept_id
                and cac.CHANGE_ID=c.CHANGE_ID
                and  tc.change_id = c.change_id 
                and c.service_code = 'NS'
               and tc.old_new='1' and tc.operation_type='1' 
               and tc.busi_prod_code in ('00425000','00952000'))
      </if>
          
          
              <![CDATA[ 
          
          ) as  minors_amount
        from dev_pas.t_cs_accept_change cac,
              dev_pas.t_cs_risk_amount   cra
       where cra.amount_status<>3 
         and cra.customer_id =  #{customer_id} and cac.change_id=cra.change_id
                   and cac.accept_status in ('07', '08', '09', '10', '11', '13') and cra.risk_type!=14   ]]>
                  <if test="query_busi_code==1">
          and exists (
                 select  1  from dev_pas.t_cs_policy_change c ,
               DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL tc where  
                cac.accept_id=c.accept_id
                and cac.CHANGE_ID=c.CHANGE_ID
                and  tc.change_id = c.change_id 
                and c.service_code = 'NS'
               and tc.old_new='1' and tc.operation_type='1' 
               and tc.busi_prod_code in ('00425000','00952000'))
      </if>
          
          
            <![CDATA[ 
          group by cra.risk_type, cra.busi_prod_code,cra.policy_code) CS  INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP 
            ON BP.PRODUCT_CODE_SYS=CS.BUSI_PROD_CODE 
      ) b group by risk_type   )T group by risk_type   
      order by decode(RISK_TYPE,'2',1,'3',2,'1',3,'4',4,'5',5,'6',6,'7',7,'8',8,'9',9,'10',10,'11',11,'12',12,'13',13)
        ]]>
    </select>
    
    
 <select id="getQueryNotify" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[ 
            SELECT 
              DC.CUSTOMER_SURVEY_ID,
              QI.SURVEY_VERSION,
              QI.SURVEY_CODE,
              SV.SURVEY_VERSION_NAME,
              QI.QUESTION_CONTENT,
              DC.APPLY_CODE,
              CM.POLICY_CODE,
              (SELECT LS.STATUS_NAME FROM DEV_UW.T_LIABILITY_STATUS LS WHERE LS.STATUS_CODE = CM.LIABILITY_STATE)  LIABILITY_STATE,
              DC.SURVEY_MODULE_RESULT
            FROM 
            DEV_NB.T_QUESTIONAIRE_CUSTOMER DC
            INNER JOIN DEV_NB.T_QUESTIONAIRE_INFO QI ON DC.SURVEY_QUESTION_ID=QI.SURVEY_QUESTION_ID
            LEFT JOIN DEV_NB.T_QUESTIONAIRE_SURVEY_VERSION SV ON SV.SURVEY_VERSION_CODE=QI.SURVEY_VERSION
            LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM ON CM.APPLY_CODE=DC.APPLY_CODE
            WHERE 1=1
       ]]>
        <!-- 192831接口需求_官微医药无忧（A04）险种保单相关信息查询接口申请（新核心）-->
      <if test="policy_code !=null and policy_code != ''"><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
      <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND DC.APPLY_CODE =  #{apply_code} ]]></if>
      <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND DC.CUSTOMER_ID =  #{customer_id} ]]></if>
      <if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND DC.POLICY_ID =  #{policy_id} ]]></if>
      <!-- SURVEY_VERSION_TYPE 告知类型字段 1健康告知 2财务及其他告知 3代理人告知 -->
      <if test=" survey_version_type != null and survey_version_type != ''  "><![CDATA[ AND SV.SURVEY_VERSION_TYPE =  #{survey_version_type} ]]></if>
      <!-- QUESTIONAIRE_OBJECT 告知对象字段 1投保人 2被保人 3代理人 -->
      <if test=" questionaire_object != null and questionaire_object != ''  "><![CDATA[ AND DC.QUESTIONAIRE_OBJECT =  #{questionaire_object} ]]></if>
         <![CDATA[  order by qi.survey_version,qi.survey_code  asc ]]>
    </select>
    <!-- 41717 需求修改 -->
    <select id="getQueryNotifypa" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
		SELECT C.CUSTOMER_SURVEY_ID,
			       C.POLICY_ID,
			       C.POLICY_CODE,
			       UW.APPLY_CODE,
			       A.SURVEY_VERSION,
			       E.SURVEY_VERSION_NAME,
			       A.QUESTION_CONTENT,
			    /*   (CASE
			         WHEN A.SURVEY_QUESTION_ID = '528' AND C.SURVEY_MODULE_RESULT = '01' THEN
			          '城镇'
			         WHEN A.SURVEY_QUESTION_ID = '528' AND C.SURVEY_MODULE_RESULT = '02' THEN
			          '农村'
			         ELSE
			          C.SURVEY_MODULE_RESULT
			       END)*/ 
			       C.SURVEY_MODULE_RESULT,
			       F.QUESTIONAIRE_OBJECT_NAME,
			       UW.UW_SOURCE_TYPE,
			       UW.POLICY_DECISION AS POLICY_DECISION
			  FROM  DEV_PAS.T_CS_QUESTIONAIRE_CUSTOMER C
			  LEFT JOIN DEV_PAS.T_QUESTIONAIRE_INFO A
			    ON C.SURVEY_QUESTION_ID = A.SURVEY_QUESTION_ID
			  LEFT JOIN DEV_PAS.T_QUESTIONAIRE_OBJECT F
			    ON F.QUESTIONAIRE_OBJECT_CODE = C.QUESTIONAIRE_OBJECT
			  LEFT JOIN DEV_PAS.T_QUESTIONAIRE_SURVEY_VERSION E
			    ON E.SURVEY_VERSION_CODE = A.SURVEY_VERSION
			  LEFT JOIN DEV_UW.T_UW_POLICY UW ON UW.POLICY_ID = C.POLICY_ID 
			 WHERE  1=1
        ]]>
      <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND UW.APPLY_CODE =  #{apply_code} ]]></if>
      <if test=" uw_id != null and uw_id != ''  "><![CDATA[ AND UW.uw_id =  #{uw_id} ]]></if>
      <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND C.CUSTOMER_ID =  #{customer_id} ]]></if>
      <if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND C.POLICY_ID =  #{policy_id} ]]></if>
      <if test=" uw_source_type != null and uw_source_type != ''  "><![CDATA[ AND UW.UW_SOURCE_TYPE =  #{uw_source_type} ]]></if>
      <if test=" questionaire_object != null and questionaire_object != ''  "><![CDATA[ AND C.QUESTIONAIRE_OBJECT =  #{questionaire_object} ]]></if>
    </select>
    <!-- 按索引查询操作 -->	
	<select id="findCustomerIdBypolicycode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CODE,A.APPLY_CODE,A.CUSTOMER_ID FROM DEV_NB.T_NB_INSURED_LIST A WHERE  1=1   ]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE =  #{apply_code} ]]></if>
		 <if test="policy_code !=null and policy_code != ''"><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
<!-- 		 <if test="uw_id !=null and uw_id != ''"><![CDATA[ AND A.UW_ID = #{uw_id,jdbcType=NUMERIC}]]></if> -->
	</select>
	<!-- 查询所有操作 -->
	<select id="UW_findAllPenoticeDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PENOTICE_ID, A.PESET_CODE, (SELECT peitem_name FROM dev_uw.T_PHYSICAL_ITEM where peitem_code = A.PEITEM_CODE) peitem_name,A.PEITEM_CODE, A.IS_PESET_ITEM_INDI, 
			A.DETAIL_ID, A.OTHER_CONTENT FROM  dev_uw.T_PENOTICE_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
		<![CDATA[ ORDER BY A.DETAIL_ID               ]]> 
	</select>
	
	<select id="UW_findAskforinfoDetailByAskforinfoId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_CONTENT, A.QUESTION_TYPE, A.DETAIL_ID,  (SELECT C.CUSTOMER_NAME FROM DEV_PAS.T_CUSTOMER C WHERE C.CUSTOMER_ID = A.CUSTOMER_ID) AS CUSTOMER_NAME,
			A.ASKFORINFO_ID, A.ASK_QUESTION_CODE FROM dev_uw.T_ASKFORINFO_DETAIL  A   where 1=1  ]]>
			<if test=" askforinfo_id  != null "><![CDATA[ AND A.ASKFORINFO_ID = #{askforinfo_id} ]]></if>
		<![CDATA[ ORDER BY A.DETAIL_ID               ]]>
	</select>
    <!-- 风险保额明细  -->
    <select id="QRY_contractProductRiskAmount" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
       select  BUSI_PROD_NAME,RISK_TYPE,rank() over(partition by BUSI_PROD_CODE order by  decode(RISK_TYPE,'3',1,'2',2,'1',3,'4',4,'5',5,'6',6,'7',7,'8',8,'9',9,'10',10,'11',11,'12',12,'13',13)) px,
            BUSI_PROD_CODE,SUM(RISK_AMOUNT) AS RISK_AMOUNT,SUM(MINORS_AMOUNT) MINORS_AMOUNT from (
        SELECT 
            BP.PRODUCT_NAME_SYS AS BUSI_PROD_NAME,
            RISK_TYPE, 
            BUSI_PROD_CODE,
            (nvl(RA.LAPSERISK,0)+nvl(RA.ACTIVERISK,0)) AS RISK_AMOUNT,
            ( 
            SELECT SUM(TR.RISK_AMOUNT) 
            FROM dev_pas.T_RISK_AMOUNT TR
              WHERE TR.CUSTOMER_ID=#{customer_id} AND BUSI_PROD_CODE=RA.BUSI_PROD_CODE 
              AND (TR.AMOUNT_STATUS != 3) AND TR.RISK_TYPE='14'
              ) AS MINORS_AMOUNT
            FROM
            (
            SELECT RISK_TYPE,
                 BUSI_PROD_CODE,
                 CASE
                   WHEN AMOUNT_STATUS = 3 THEN
                    SUM(RISK_AMOUNT)
                 END TERMINATEDRISK,
                 CASE
                   WHEN AMOUNT_STATUS = 4 THEN
                    SUM(RISK_AMOUNT)
                 END LAPSERISK,
                 CASE
                   WHEN AMOUNT_STATUS = 1 THEN
                    SUM(RISK_AMOUNT)
                 END ACTIVERISK
            FROM (SELECT SUM(T.RISK_AMOUNT) RISK_AMOUNT,
                         T.RISK_TYPE,
                         T.AMOUNT_STATUS,
                         T.BUSI_PROD_CODE
                    FROM (SELECT TC.PRODUCT_ID,
                                 TR.BUSI_PROD_CODE,
                                 TR.RISK_TYPE,
                                 SUM(TR.RISK_AMOUNT) AS RISK_AMOUNT,
                                 CASE
                                   WHEN TR.AMOUNT_STATUS = 3 OR
                                        TC.LIABILITY_STATE = 3 THEN
                                    3
                                   WHEN TC.LIABILITY_STATE = 4 THEN
                                    4
                                   ELSE
                                    1
                                 END AS AMOUNT_STATUS
                            FROM (SELECT * FROM 
                            DEV_PAS.T_RISK_AMOUNT TR
                            LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM ON TR.APPLY_CODE=CM.APPLY_CODE  
                         LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER NCM ON TR.APPLY_CODE=NCM.APPLY_CODE 
                             WHERE NOT (TR.RISK_TYPE='16' OR TR.RISK_TYPE='17') AND  (NOT(CM.LIABILITY_STATE=3 OR NCM.PROPOSAL_STATUS='08' OR NCM.PROPOSAL_STATUS='12' OR NCM.PROPOSAL_STATUS='28')
                            OR (CM.LIABILITY_STATE IS NULL AND NCM.PROPOSAL_STATUS IS NOT NULL)) ]]>
                              <if test="query_busi_code==1">
                               and  (exists (
                                  select  1 from dev_nb.T_NB_CONTRACT_BUSI_PROD tbp  
                                where tbp.product_code in ('00425000','00952000')
                                and NCM.apply_code=tbp.apply_code
                              )  or exists (
                                  select  1 from dev_pas.T_CONTRACT_BUSI_PROD tbp  
                                where tbp.busi_prod_code in ('00425000','00952000')
                                and CM.apply_code=tbp.apply_code
                              ) )   
                               </if>
                            
                             <![CDATA[ 
                            ) TR
                            LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT TC
                              ON TR.ITEM_ID = TC.ITEM_ID
                           WHERE EXISTS
                           (SELECT R.CUSTOMER_ID
                                    FROM dev_pas.T_CUSTOMER R
                                   WHERE R.CUSTOMER_ID = #{customer_id} 
                                     AND R.CUSTOMER_ID = TR.CUSTOMER_ID)
                           GROUP BY RISK_TYPE,
                                    TC.LIABILITY_STATE,
                                    TR.AMOUNT_STATUS,
                                    TC.PRODUCT_ID,
                                    TR.BUSI_PROD_CODE,
                                    TR.RISK_TYPE
                                    ) T
                   GROUP BY T.RISK_TYPE,
                            T.AMOUNT_STATUS,
                            T.PRODUCT_ID,
                            T.BUSI_PROD_CODE
                            )
            GROUP BY RISK_TYPE, AMOUNT_STATUS, BUSI_PROD_CODE
            ) RA 
            INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP 
            ON BP.PRODUCT_CODE_SYS=RA.BUSI_PROD_CODE
            WHERE 1=1 AND RISK_TYPE!='14'
            union
    select BP.PRODUCT_NAME_SYS AS BUSI_PROD_NAME,risk_type,busi_prod_code,risk_amount, minors_amount from (             
 select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) as risk_amount,
         cra.risk_type,
         cra.busi_prod_code,
         (select sum((case cra.old_new
             when '1' then
              cra.risk_amount
             when '0' then
              -cra.risk_amount
           end)) from  (select * from dev_pas.t_cs_risk_amount cra where not (cra.risk_type='16' or cra.risk_type='17'))  cra ,
             dev_pas.t_cs_accept_change cac
       where cra.amount_status<>3 
       and cac.change_id = cra.change_id
         and cra.customer_id = #{customer_id} 
                   and cac.accept_status in ('07', '08', '09', '10', '11', '13') and  cra.risk_type=14 ]]>
                <if test="query_busi_code==1">
          and exists (
                 select  1  from dev_pas.t_cs_policy_change c ,
               DEV_PAS.V_CS_CONTRACT_BUSI_PROD_ALL tc where  
                cac.accept_id=c.accept_id
                and cac.CHANGE_ID=c.CHANGE_ID
                and  tc.change_id = c.change_id 
                and c.service_code = 'NS'
               and tc.old_new='1' and tc.operation_type='1' 
               and tc.busi_prod_code in ('00425000','00952000'))
      </if>
          
          
            <![CDATA[ 
          ) as  minors_amount
        from dev_pas.t_cs_accept_change cac,
             (select * from dev_pas.t_cs_risk_amount cra where not (cra.risk_type='16' or cra.risk_type='17')) cra
       where cra.amount_status<>3 
         and cra.customer_id = #{customer_id} and cac.change_id=cra.change_id
                   and cac.accept_status in ('07', '08', '09', '10', '11', '13') and cra.risk_type!=14  ]]>
               <if test="query_busi_code==1">
          and exists (
                 select  1  from APP___PAS__DBUSER.t_cs_policy_change c ,
               APP___PAS__DBUSER.V_CS_CONTRACT_BUSI_PROD_ALL tc where  
                cac.accept_id=c.accept_id
                and cac.CHANGE_ID=c.CHANGE_ID
                and  tc.change_id = c.change_id 
                and c.service_code = 'NS'
               and tc.old_new='1' and tc.operation_type='1' 
               and tc.busi_prod_code in ('00425000','00952000'))
      </if>
                <![CDATA[ 
          group by cra.risk_type, cra.busi_prod_code,cra.policy_code) CS  INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP 
            ON BP.PRODUCT_CODE_SYS=CS.BUSI_PROD_CODE ) T GROUP BY BUSI_PROD_NAME,RISK_TYPE,BUSI_PROD_CODE
         ]]>
    </select>
    <!-- 查询代理人告知信息 -->
    <select id="getQueryAgentNotifypa" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
			SELECT C.CUSTOMER_SURVEY_ID,
			       C.POLICY_ID,
			       C.POLICY_CODE,			      
			       A.SURVEY_VERSION,
			       A.SURVEY_CODE,
			       E.SURVEY_VERSION_NAME,
			       A.QUESTION_CONTENT,
			    /*   (CASE
			         WHEN A.SURVEY_QUESTION_ID = '528' AND C.SURVEY_MODULE_RESULT = '01' THEN
			          '城镇'
			         WHEN A.SURVEY_QUESTION_ID = '528' AND C.SURVEY_MODULE_RESULT = '02' THEN
			          '农村'
			         ELSE
			          C.SURVEY_MODULE_RESULT
			       END)*/ 
			       C.SURVEY_MODULE_RESULT,
			       B.QUESTION_ORDER,
			       F.QUESTIONAIRE_OBJECT_NAME			 
			  FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER C
			  LEFT JOIN DEV_PAS.V_QUESTIONAIRE_CUS_PARAM_ALL D
			    ON C.CUSTOMER_SURVEY_ID = D.CUSTOMER_SURVEY_ID
			  LEFT JOIN DEV_PAS.T_QUESTIONAIRE_INFO A
			    ON C.SURVEY_QUESTION_ID = A.SURVEY_QUESTION_ID
			  LEFT JOIN DEV_PAS.T_QUESTIONAIRE_PARAM B
			    ON B.SURVEY_ID = D.SURVEY_ID
			  LEFT JOIN DEV_PAS.T_QUESTIONAIRE_OBJECT F
			    ON F.QUESTIONAIRE_OBJECT_CODE = C.QUESTIONAIRE_OBJECT
			  LEFT JOIN DEV_PAS.T_QUESTIONAIRE_SURVEY_VERSION E
			    ON E.SURVEY_VERSION_CODE = A.SURVEY_VERSION			
			 WHERE  1=1
        ]]>
      <if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND C.APPLY_CODE =  #{apply_code} ]]></if>
      <if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND C.CUSTOMER_ID =  #{customer_id} ]]></if>
      <if test=" policy_id != null and policy_id != ''  "><![CDATA[ AND C.POLICY_ID =  #{policy_id} ]]></if>
      <if test=" questionaire_object != null and questionaire_object != ''  "><![CDATA[ AND C.QUESTIONAIRE_OBJECT =  #{questionaire_object} ]]></if>
      <if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND C.AGENT_CODE =  #{agent_code} ]]></if>
      <![CDATA[ORDER BY  A.SURVEY_VERSION,A.SURVEY_CODE ASC]]>
    </select>
    
  <select id="findHolderCustomerIdBypolicycode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.POLICY_CODE,A.APPLY_CODE,A.CUSTOMER_ID FROM DEV_NB.T_NB_POLICY_HOLDER A WHERE  1=1   ]]>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE =  #{apply_code} ]]></if>
		 <if test="policy_code !=null and policy_code != ''"><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
<!-- 		 <if test="uw_id !=null and uw_id != ''"><![CDATA[ AND A.UW_ID = #{uw_id,jdbcType=NUMERIC}]]></if> -->
	</select>
	<!-- 需求变更45225的功能修改  -->
	<!-- 需求变更52337的功能修改 -->
	<select id="findUwRiskManageAccidentRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT RM.PAGE_QUERY_CODE, /*平台查询码*/
	             RM.MULTI_COMPANY, /*多家公司承保提示*/
	             RM.MAJOR_DISEASE_PAYMENT, /*重疾理赔史*/
	             RM.DISABILITY, /*伤残理赔史*/
	             RM.DENSE_POLICY, /*密集投保*/
	             RM.ACCUMULATIVE_MONEY, /*累计保额提示*/
	             RM.DRIVE_ACC_ACCUMULATIVE_MONEY, /*自驾车意外险累计保额提示*/
	             RM.DRIVE_DUTYMULTI_COMPANY, /*自驾车责任多家投保家数*/
	             RM.SPEC_DISEASE_PAYMENT, /*特定疾病理赔史*/
	             RM.CUSTOMER_ID,
       			 CASE WHEN RM.CUSTOMER_ID IS NOT NULL THEN
	         	 (SELECT C.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST C WHERE C.UW_ID = B.UW_ID 
	          		 AND C.APPLY_CODE = B.BIZ_CODE AND RM.CUSTOMER_ID = C.CUSTOMER_ID)
	     		 ELSE
	       		   (SELECT C.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST C, APP___UW__DBUSER.T_BENEFIT_INSURED D
	           		 WHERE C.UW_ID = D.UW_ID AND D.INSURED_ID = C.LIST_ID AND C.POLICY_ID = D.POLICY_ID
	           		  AND D.ORDER_ID = 1 AND ROWNUM = 1) END CUSTOMER_NAME /*被保险人姓名*/
	        FROM DEV_UW.T_UW_RISK_MANAGE RM,
       			 APP___UW__DBUSER.T_UW_MASTER B
	       WHERE 1=1 AND RM.APPLY_CODE = B.BIZ_CODE AND RM.RISK_TYPE = 6 /*险种类型  6-意外险 参考码表 T_AGGREGATION_RISK_TYPE*/
		]]>
		<if test=" apply_code  != null and apply_code != ''"><![CDATA[ AND RM.APPLY_CODE = #{apply_code} ]]></if>	   
	</select>
	
	<select id="findUwRiskManageMajorDiseaseRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT RM.PAGE_QUERY_CODE, /*平台查询码*/
			       RM.ABNORMAL_CHECK, /*非正常核保结论*/
			       RM.ABNORMAL_PAYMENT, /*非正常理赔结论*/
			       RM.MAJOR_DISEASE_PAYMENT, /*重疾理赔史*/
			       RM.CHRONIC_DISEASE_PAYMENT, /*慢性病理赔史*/
			       RM.MAJOR_DISEASE_MONEY, /*重疾保额提示*/
			       RM.MULTI_COMPANY, /*多家公司承保提示*/
			       RM.DENSE_POLICY, /*密集投保*/
			       RM.SPEC_DISEASE_PAYMENT, /*特定疾病理赔史*/
			       RM.CUSTOMER_ID,
			       CASE WHEN RM.CUSTOMER_ID IS NOT NULL THEN
	         		(SELECT C.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST C WHERE C.UW_ID = B.UW_ID 
	          			AND C.APPLY_CODE = B.BIZ_CODE AND RM.CUSTOMER_ID = C.CUSTOMER_ID)
	     		   ELSE
	       		   (SELECT C.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST C, APP___UW__DBUSER.T_BENEFIT_INSURED D
	           		 WHERE C.UW_ID = D.UW_ID AND D.INSURED_ID = C.LIST_ID AND C.POLICY_ID = D.POLICY_ID
	           		  	AND D.ORDER_ID = 1 AND ROWNUM = 1) END CUSTOMER_NAME /*被保险人姓名*/
			  FROM DEV_UW.T_UW_RISK_MANAGE RM,
			  APP___UW__DBUSER.T_UW_MASTER B
			 WHERE 1=1 AND RM.APPLY_CODE = B.BIZ_CODE AND RM.RISK_TYPE = 3 /*险种类型  3-重疾险 参考码表 T_AGGREGATION_RISK_TYPE*/
		]]>
		<if test=" apply_code  != null and apply_code != ''"><![CDATA[ AND RM.APPLY_CODE = #{apply_code} ]]></if>
	</select>
		<!-- rm157590 调用意健险平台个人津贴核保风险提示服务  获取意健险平台个人津贴阳性-->
	<select id="findUwRiskManageAllowanceRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT RM.PAGE_QUERY_CODE, /*平台查询码*/
			       RM.ABNORMAL_CHECK, /*非正常核保结论*/
			       RM.ABNORMAL_PAYMENT, /*非正常理赔结论*/
			       RM.MAJOR_DISEASE_PAYMENT, /*重疾理赔史*/
			       RM.CHRONIC_DISEASE_PAYMENT, /*慢性病理赔史*/
			       RM.MAJOR_DISEASE_MONEY, /*重疾保额提示*/
			       RM.MULTI_COMPANY, /*多家公司承保提示*/
			       RM.DENSE_POLICY, /*密集投保*/
			       RM.SPEC_DISEASE_PAYMENT, /*特定疾病理赔史*/
			       RM.ACCUMULATIVE_DAY_MONEY,/*有效保单累计日额提示*/
                   RM.PAYMENT_COUNT_ONE_YEAR,/*1年内津贴赔付次数提示*/
                   RM.ACCUMU_PAY_MENT_DAY_COUNT,/*累计津贴险赔付天数提示*/
			       RM.CUSTOMER_ID,
			       CASE WHEN RM.CUSTOMER_ID IS NOT NULL THEN
	         		(SELECT C.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST C WHERE C.UW_ID = B.UW_ID 
	          			AND C.APPLY_CODE = B.BIZ_CODE AND RM.CUSTOMER_ID = C.CUSTOMER_ID)
	     		   ELSE
	       		   (SELECT C.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST C, APP___UW__DBUSER.T_BENEFIT_INSURED D
	           		 WHERE C.UW_ID = D.UW_ID AND D.INSURED_ID = C.LIST_ID AND C.POLICY_ID = D.POLICY_ID AND C.UW_ID = B.UW_ID
	           		  	AND D.ORDER_ID = 1 AND ROWNUM = 1) END CUSTOMER_NAME /*被保险人姓名*/
			  FROM DEV_UW.T_UW_RISK_MANAGE RM,
			  APP___UW__DBUSER.T_UW_MASTER B
			 WHERE 1=1 AND RM.APPLY_CODE = B.BIZ_CODE AND RM.RISK_TYPE = 963 /*险种类型  3-重疾险 参考码表 T_AGGREGATION_RISK_TYPE*/
		]]>
		<if test=" apply_code  != null and apply_code != ''"><![CDATA[ AND RM.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	
	<!-- 寿险 -->
	<select id="findUwRiskManageLifeRisk" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT RM.PAGE_QUERY_CODE, /*平台查询码*/
			       RM.ABNORMAL_CHECK, /*非正常核保结论*/
			       RM.ABNORMAL_PAYMENT, /*非正常理赔结论*/
			       RM.MAJOR_DISEASE_PAYMENT, /*重疾理赔史*/
			       RM.CHRONIC_DISEASE_PAYMENT, /*慢性病理赔史*/
			       RM.MAJOR_DISEASE_MONEY, /*重疾保额提示*/
			       RM.MULTI_COMPANY, /*多家公司承保提示*/
			       RM.DENSE_POLICY, /*密集投保*/
			       RM.SPEC_DISEASE_PAYMENT, /*特定疾病理赔史*/
			       RM.CUSTOMER_ID,
			       CASE WHEN RM.CUSTOMER_ID IS NOT NULL THEN
	         		(SELECT C.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST C WHERE C.UW_ID = B.UW_ID 
	          			AND C.APPLY_CODE = B.BIZ_CODE AND RM.CUSTOMER_ID = C.CUSTOMER_ID)
	     		   ELSE
	       		   (SELECT C.CUSTOMER_NAME FROM APP___UW__DBUSER.T_INSURED_LIST C, APP___UW__DBUSER.T_BENEFIT_INSURED D
	           		 WHERE C.UW_ID = D.UW_ID AND D.INSURED_ID = C.LIST_ID AND C.POLICY_ID = D.POLICY_ID
	           		  	AND D.ORDER_ID = 1 AND ROWNUM = 1) END CUSTOMER_NAME, /*被保险人姓名*/
	           	RM.ACCUMULATIVE_DAY_MONEY,
	           	RM.PAYMENT_COUNT_ONE_YEAR,
	           	RM.ACCUMU_PAY_MENT_DAY_COUNT,
	           	RM.SUM_INSURED,RM.SEARCH_COM_CNT,RM.DENSE_RGA,RM.MULTI_COMPANY_RGA,RM.ACC_ACCUMULATIVE_MONEY  	  	
			  FROM DEV_UW.T_UW_RISK_MANAGE RM,
			  APP___UW__DBUSER.T_UW_MASTER B
			 WHERE 1=1 AND RM.APPLY_CODE = B.BIZ_CODE AND RM.RISK_TYPE = 2 /*险种类型  2-寿险 参考码表 T_AGGREGATION_RISK_TYPE*/
		]]>
		<if test=" apply_code  != null and apply_code != ''"><![CDATA[ AND RM.APPLY_CODE = #{apply_code} ]]></if>
	</select>
	
	<select id="UW_queryBackDetailExcel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT AA.UW_ID,AA.POLICY_CODE,AA.BIZ_CODE,AA.OPERATER_ID,AA.USER_NAME AS OPERATER_NAME ,AA.OPERATER_TIME
				,(SELECT LISTAGG((SELECT 
         								CASE WHEN UWBD.REASON IS NULL THEN
                             				UWBC.CAUSE_DESC
                         				ELSE 
                         					UWBC.CAUSE_DESC||'-'||UWBD.REASON
                         				END 
                         			FROM DEV_UW.T_UW_BACK_CAUSE UWBC
                        			WHERE UWBC.CAUSE_CODE = UWBD.CASUE_CODE),
						',') WITHIN GROUP(ORDER BY UWBD.CASUE_CODE)
				FROM DEV_UW.T_UW_BACK_DETAIL UWBD WHERE UWBD.UW_ID = AA.UW_ID  ) AS REASON
			FROM (
				SELECT 	A.UW_ID, A.CASUE_CODE,A.REASON,A.OPERATER_ID,US.USER_NAME,A.OPERATER_TIME,
						B.BIZ_CODE,C.POLICY_CODE FROM DEV_UW.T_UW_BACK_DETAIL A,
						DEV_UW.T_UW_MASTER B ,
						DEV_UW.T_UW_POLICY C ,
						DEV_NB.T_UDMP_USER US
					WHERE A.UW_ID = B.UW_ID 
						AND B.UW_ID = C.UW_ID
						AND A.OPERATER_ID = US.USER_ID
						AND C.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
						AND TO_DATE(TO_CHAR(A.OPERATER_TIME,'yyyy/MM/dd'),'yyyy/MM/dd')  
							BETWEEN  TO_DATE(#{start_date},'yyyy/MM/dd') 
						AND TO_DATE(#{end_date},'yyyy/MM/dd') 
				)AA GROUP BY AA.UW_ID,AA.BIZ_CODE,AA.POLICY_CODE,AA.OPERATER_ID,AA.USER_NAME,AA.OPERATER_TIME
			ORDER BY AA.OPERATER_TIME,AA.POLICY_CODE
		]]>
	</select>
	<select id="UW_queryBackDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CC.RN,CC.UW_ID,CC.POLICY_CODE,CC.BIZ_CODE,CC.OPERATER_ID,CC.OPERATER_NAME,CC.OPERATER_TIME,CC.REASON FROM (
				SELECT ROWNUM RN,BB.UW_ID,BB.POLICY_CODE,BB.BIZ_CODE,BB.OPERATER_ID,BB.OPERATER_NAME,BB.OPERATER_TIME,BB.REASON FROM (
					SELECT AA.UW_ID,AA.POLICY_CODE,AA.BIZ_CODE,AA.OPERATER_ID,AA.USER_NAME AS OPERATER_NAME ,AA.OPERATER_TIME
						,(SELECT LISTAGG((SELECT 
		         								CASE WHEN UWBD.REASON IS NULL THEN
		                             				UWBC.CAUSE_DESC
		                         				ELSE 
		                         					UWBC.CAUSE_DESC||'-'||UWBD.REASON
		                         				END 
		                         			FROM DEV_UW.T_UW_BACK_CAUSE UWBC
		                        			WHERE UWBC.CAUSE_CODE = UWBD.CASUE_CODE),
								',') WITHIN GROUP(ORDER BY UWBD.CASUE_CODE)
						FROM DEV_UW.T_UW_BACK_DETAIL UWBD WHERE UWBD.UW_ID = AA.UW_ID  ) AS REASON
					FROM (
						SELECT 	A.UW_ID, A.CASUE_CODE,A.REASON,A.OPERATER_ID,US.USER_NAME,A.OPERATER_TIME,
								B.BIZ_CODE,C.POLICY_CODE FROM DEV_UW.T_UW_BACK_DETAIL A,
								DEV_UW.T_UW_MASTER B ,
								DEV_UW.T_UW_POLICY C ,
								DEV_NB.T_UDMP_USER US
							WHERE A.UW_ID = B.UW_ID 
								AND B.UW_ID = C.UW_ID
								AND A.OPERATER_ID = US.USER_ID
								AND C.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
								AND TO_DATE(TO_CHAR(A.OPERATER_TIME,'yyyy/MM/dd'),'yyyy/MM/dd')  
									BETWEEN  TO_DATE(#{start_date},'yyyy/MM/dd') 
								AND TO_DATE(#{end_date},'yyyy/MM/dd') 
						)AA GROUP BY AA.UW_ID,AA.BIZ_CODE,AA.POLICY_CODE,AA.OPERATER_ID,AA.USER_NAME,AA.OPERATER_TIME
					ORDER BY AA.OPERATER_TIME,AA.POLICY_CODE
			)BB)CC WHERE  CC.RN > #{GREATER_NUM} AND CC.RN<= #{LESS_NUM}
		]]>
	</select>
	<select id="UW_queryBackDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			SELECT COUNT(*) FROM (
			SELECT AA.UW_ID,AA.POLICY_CODE,AA.BIZ_CODE,AA.OPERATER_ID,AA.USER_NAME AS OPERATER_NAME ,AA.OPERATER_TIME FROM (
				SELECT 	A.UW_ID, A.CASUE_CODE,A.REASON,A.OPERATER_ID,US.USER_NAME,A.OPERATER_TIME,
						B.BIZ_CODE,C.POLICY_CODE FROM DEV_UW.T_UW_BACK_DETAIL A,
						DEV_UW.T_UW_MASTER B ,
						DEV_UW.T_UW_POLICY C ,
						DEV_NB.T_UDMP_USER US
				WHERE 	A.UW_ID = B.UW_ID 
						AND B.UW_ID = C.UW_ID
						AND A.OPERATER_ID = US.USER_ID
						AND C.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
						AND TO_DATE(TO_CHAR(A.OPERATER_TIME,'yyyy/MM/dd'),'yyyy/MM/dd')  
							BETWEEN  TO_DATE(#{start_date},'yyyy/MM/dd') 
						AND TO_DATE(#{end_date},'yyyy/MM/dd') 
				)AA GROUP BY AA.UW_ID,AA.BIZ_CODE,AA.POLICY_CODE,AA.OPERATER_ID,AA.USER_NAME,AA.OPERATER_TIME)
		]]>
	</select>
	
	<select id="UW_findPolicyDecisionByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT T.POLICY_DECISION FROM DEV_UW.T_UW_POLICY T WHERE T.APPLY_CODE = #{apply_code} AND T.UW_SOURCE_TYPE = '1'
		]]>
	</select>
	
	 <select id="uwMortalitys" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
 SELECT (SELECT TPL.PRODUCT_NAME
          FROM DEV_PDS.T_PRODUCT_LIFE TPL
         WHERE TPL.Internal_Id = SS.Product_Code) PRODUCT_CODE,
       NB.VALIDATE_DATE,
       SS.UW_BUSI_ID,
       SS.EM_VALUE
  FROM (SELECT ZZ.POLICY_ID,
               ZZ.APPLY_CODE,
               Z.UW_ID,
               Z.UW_BUSI_ID,
               Z.EM_VALUE,
               ZZ.Product_Code
          FROM DEV_UW.T_UW_EXTRA_PREM Z, DEV_UW.T_UW_PRODUCT ZZ
         WHERE Z.UW_ID = ZZ.UW_ID
           AND Z.Uw_Prd_Id = ZZ.Uw_Prd_Id
           AND Z.BUSI_ITEM_ID = ZZ.BUSI_ITEM_ID
           AND ZZ.DECISION_CODE = '80') SS
 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER NB
    ON NB.APPLY_CODE = SS.APPLY_CODE
 WHERE 1 = 1
        ]]>
        <include refid="uwExtraPremsQryCondition" />
    </select>
    
    <select id="UW_findInsuredAgeForUwNotify" resultType="java.util.Map" parameterType="java.util.Map">
    <choose>
    	<when test="uw_source_type != null and uw_source_type == 2">
    		<![CDATA[
    			SELECT FLOOR(MONTHS_BETWEEN(B.APPLY_TIME, E.CUSTOMER_BIRTHDAY) / 12) AS INSURED_AGE /*投、被保人年龄*/
				  FROM DEV_PAS.T_CS_ACCEPT_CHANGE A,
				       DEV_PAS.T_CS_APPLICATION   B,
				       DEV_UW.T_UW_MASTER         C, ]]>
				    <if test=" role_type != null and role_type == 2 "><![CDATA[ DEV_UW.T_INSURED_LIST  D, ]]></if>
			   		<if test=" role_type != null and role_type == 1 "><![CDATA[ DEV_UW.T_POLICY_HOLDER D, ]]></if>
				     <![CDATA[DEV_PAS.T_CUSTOMER  E
				 WHERE A.ACCEPT_CODE = C.BIZ_CODE
				   AND A.CHANGE_ID = B.CHANGE_ID
				   AND C.UW_ID = D.UW_ID
				   AND E.CUSTOMER_ID = D.CUSTOMER_ID
				   AND D.APPLY_CODE = #{apply_code}
				   AND D.CUSTOMER_ID = #{customer_id}
				   AND D.UW_ID = #{uw_id}
    		]]>
    	</when>
    	<otherwise>
    		<![CDATA[
			SELECT FLOOR(MONTHS_BETWEEN(A.APPLY_DATE, C.CUSTOMER_BIRTHDAY) / 12) AS INSURED_AGE /*投、被保人年龄*/
			    FROM DEV_NB.T_NB_CONTRACT_MASTER	 A,]]>
			   		<if test=" role_type != null and role_type == 2 "><![CDATA[ DEV_NB.T_NB_INSURED_LIST  B, ]]></if>
			   		<if test=" role_type != null and role_type == 1 "><![CDATA[ DEV_NB.T_NB_POLICY_HOLDER B, ]]></if>
			    	<![CDATA[DEV_PAS.T_CUSTOMER		C
			   WHERE ROWNUM = 1
			     AND A.APPLY_CODE = B.APPLY_CODE
			     AND B.CUSTOMER_ID = C.CUSTOMER_ID
			     AND B.CUSTOMER_ID = #{customer_id}
			     AND A.APPLY_CODE = #{apply_code}
			]]>
    	</otherwise>
    	</choose>
	</select>
    		<!-- 查询CUSTOMER_DECISION_FLAG标识 -->
	<select id="UW_queryCustomerDecisionFlag" resultType="java.util.Map" parameterType="java.util.Map">
			
			<![CDATA[  SELECT T.CUSTOMER_DECISION_FLAG ,
			(SELECT COUNT(*)
                    FROM APP___UW__DBUSER.T_UW_MASTER    A,
                         APP___UW__DBUSER.T_INSURED_LIST B
                   WHERE A.UW_ID = B.UW_ID
                     AND B.CUSTOMER_ID = #{customer_id}
                     AND B.APPLY_CODE = #{apply_code}
                     AND A.UW_SOURCE_TYPE = #{uw_source_type}
                     AND (B.NON_STANDARDS = 1 OR B.NON_STANDARDS_VALUE = 1 OR B.STAND_LIFE = 2 AND B.CUSTOMER_DECISION_FLAG = 1)
                     ) FLAG1
      FROM APP___UW__DBUSER.T_INSURED_LIST T ,APP___UW__DBUSER.T_UW_MASTER A 
      WHERE T.UW_ID = A.UW_ID
      AND A.UW_SOURCE_TYPE = #{uw_source_type} 
      AND T.APPLY_CODE = #{apply_code}
      AND T.CUSTOMER_ID = #{customer_id}
       ]]>
	</select>
				<select id="uw_findDocumentInsured" resultType="java.util.Map" parameterType="java.util.Map">
			
			<![CDATA[ SELECT TO_CHAR((SELECT COUNT(*)
                  FROM DEV_NB.T_NB_INSURED_LIST    T1,
                       DEV_NB.T_DOCUMENT           T2,
                       DEV_UW.T_UW_DOCUMENT_VERIFY T3
                 WHERE 1 = 1
                   AND T1.CUSTOMER_ID = T3.CUSTOMER_ID
                   AND T2.BUSS_CODE = T1.APPLY_CODE
                   AND T2.DOCUMENT_NO = T3.DOCUMENT_NO
                   AND T2.BUSS_ID = T3.UW_ID
                   AND T2.BUSS_SOURCE_CODE = '002'
                   AND T2.TEMPLATE_CODE = 'UWS_00006'
                   AND T1.APPLY_CODE = #{apply_code}
                   AND T3.CUSTOMER_ID =  #{customer_id })) UW_06
  FROM DUAL
]]>
	</select>
	
	
	
	<select id="UW_queryCustomerDecisionFlags" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  T.CUSTOMER_DECISION_FLAG ,(SELECT  COUNT(*)  FROM 
     APP___UW__DBUSER.T_INSURED_LIST T, APP___UW__DBUSER.T_UW_POLICY A ,APP___UW__DBUSER.T_UW_MASTER UM
     WHERE  T.APPLY_CODE = A.APPLY_CODE 
     AND T.POLICY_CODE = A.POLICY_CODE 
     AND T.UW_ID = UM.UW_ID
     AND A.UW_ID = UM.UW_ID
     AND A.IS_DISPLAY IS NULL
     AND A.UW_SOURCE_TYPE = '2'
     AND UM.BIZ_CODE = #{accept_code}
     AND (T.STAND_LIFE = 2 OR T.NON_STANDARDS_VALUE = '1' OR T.NON_STANDARDS = 1)
      AND T.CUSTOMER_DECISION_FLAG = 1
     AND T.CUSTOMER_ID = #{customer_id}
      AND ROWNUM = 1
     ) FLAG1
     FROM 
     APP___UW__DBUSER.T_INSURED_LIST T, APP___UW__DBUSER.T_UW_POLICY A ,APP___UW__DBUSER.T_UW_MASTER UM
     WHERE  T.APPLY_CODE = A.APPLY_CODE 
     AND T.POLICY_CODE = A.POLICY_CODE 
     AND T.UW_ID = UM.UW_ID
     AND A.UW_ID = UM.UW_ID
     AND A.IS_DISPLAY IS NULL
     AND A.UW_SOURCE_TYPE = '2'
     AND UM.BIZ_CODE = #{accept_code}
     AND T.CUSTOMER_ID = #{customer_id}
      AND ROWNUM = 1
     ]]>
	</select>
		
			<select id="uw_findDocumentInsureds" resultType="java.util.Map" parameterType="java.util.Map">
			
			<![CDATA[ 
     SELECT TO_CHAR((SELECT COUNT(*)
                  FROM DEV_UW.T_INSURED_LIST    T1,
                       DEV_NB.T_DOCUMENT           T2,
                       DEV_UW.T_UW_DOCUMENT_VERIFY T3,
                       APP___UW__DBUSER.T_UW_MASTER T4
                 WHERE 1 = 1
                   AND T1.CUSTOMER_ID = T3.CUSTOMER_ID
                   AND T2.BUSS_CODE = T1.APPLY_CODE
                   AND T2.DOCUMENT_NO = T3.DOCUMENT_NO
                   AND T2.BUSS_ID = T3.UW_ID
                   AND T1.UW_ID = T4.UW_ID
                   AND T3.UW_ID = T4.UW_ID
                   AND T4.BIZ_CODE = #{accept_code}
                   AND T2.BUSS_SOURCE_CODE = '002'
                   AND T2.TEMPLATE_CODE = 'UWS_00007'
                   AND T3.CUSTOMER_ID = #{customer_id})) UW_07
  FROM DUAL
]]>
	</select>
	
</mapper>

