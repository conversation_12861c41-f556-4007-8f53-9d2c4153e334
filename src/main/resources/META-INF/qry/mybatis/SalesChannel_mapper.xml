<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.ISalesChannelDao">

	<select id="findAllSalesChannel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SC.SALES_CHANNEL_CODE,SC.SALES_CHANNEL_NAME,SC.IS_VALID FROM  APP___UW__DBUSER.T_SALES_CHANNEL SC ORDER BY SC.SALES_CHANNEL_CODE]]>
	</select>

	<select id="findAllMapSalesChannel" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT SC.SALES_CHANNEL_CODE,SC.SALES_CHANNEL_NAME,SC.IS_VALID FROM  APP___UW__DBUSER.T_SALES_CHANNEL SC ORDER BY SC.SALES_CHANNEL_CODE]]>
	</select>
</mapper>