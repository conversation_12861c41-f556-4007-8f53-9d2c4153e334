<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="policyHolder">

	<sql id="PA_policyHolderWhereCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
		<if test=" job_kind != null and job_kind != ''  "><![CDATA[ AND A.JOB_KIND = #{job_kind} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
		<if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
		<if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	    
	<sql id="PA_queryPolicyHolderByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyHolderByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="PA_queryPolicyHolderByAddressIdCondition">
		<if test=" address_id  != null "><![CDATA[ AND A.ADDRESS_ID = #{address_id} ]]></if>
	</sql>	

<!-- 按索引查询操作 A.JOB_KIND,-->	
	<select id="PA_findPolicyHolderByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID,  A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM DEV_CLM.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyHolderByListIdCondition" />
	</select>
	
	<select id="PA_findPolicyHolderByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.JOB_KIND, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyHolderByPolicyIdCondition" />
	</select>
	
	<select id="PA_findPolicyHolderByAddressId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.JOB_KIND, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_queryPolicyHolderByAddressIdCondition" />
	</select>
	

<!-- 按map查询操作 -->
	<select id="PA_findAllMapPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.JOB_KIND, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM T_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

	<!-- 查询所有操作 -->
	<select id="PA_findAllPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID,  A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>
	
	<!-- 查询契约所有操作 -->
	<select id="NB_findAllPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID,  A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_POLICY_HOLDER A WHERE ROWNUM <=  1000 AND EXISTS (
      SELECT 1 FROM DEV_NB.T_NB_CONTRACT_MASTER B WHERE A.APPLY_CODE=B.APPLY_CODE AND B.POLICY_CODE IS NULL 
      ) ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>
	
	<!-- 查询所有操作 -->
	<select id="QRY_INTEGRAL_findAllPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID,  A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, a.job_underwrite, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_HOLDER A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>

<!-- 查询个数操作 -->
	<select id="PA_findPolicyHolderTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="PA_queryPolicyHolderForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ADDRESS_ID, B.JOB_KIND, B.CUSTOMER_ID, B.CUSTOMER_HEIGHT, B.JOB_CODE, 
			B.CUSTOMER_WEIGHT, B.APPLY_CODE, B.POLICY_CODE, B.LIST_ID, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.ADDRESS_ID, A.JOB_KIND, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM T_POLICY_HOLDER A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
<!-- 查询单条 A.JOB_KIND, -->
	<select id="PA_findPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.ADDRESS_ID,  A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>
	
	<!-- 查询单条 A.JOB_KIND, -->
	<select id="PAandNB_findPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
	    <if test="  type != null and type == '1'.toString()  ">	    
		<![CDATA[ SELECT DISTINCT A.ADDRESS_ID,  A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM DEV_PAS.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		</if>
	
		<if test="  type != null and type == '0'.toString()  ">		
		<![CDATA[ SELECT DISTINCT A.ADDRESS_ID,  A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.LIST_ID, 
			A.POLICY_ID FROM DEV_NB.T_NB_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{policy_code} ]]></if>			
		</if>		
	</select>

	<select id="findPolicyHolderByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A1.CUSTOMER_ID, A1.UN_CUSTOMER_CODE, 
      A1.OLD_CUSTOMER_ID, A1.MARRIAGE_DATE, A1.EDUCATION, 
      A1.CUSTOMER_NAME, A1.CUSTOMER_BIRTHDAY, A1.CUSTOMER_GENDER, 
      A1.CUSTOMER_HEIGHT, A1.CUSTOMER_WEIGHT, A1.CUSTOMER_CERT_TYPE, 
      A1.CUSTOMER_CERTI_CODE, A1.CUSTOMER_ID_CODE, 
      A1.CUST_CERT_STAR_DATE, A1.CUST_CERT_END_DATE, 
      A1.JOB_CODE, A1.JOB_NATURE, A1.JOB_KIND, A1.JOB_TITLE, 
      A1.MARRIAGE_STATUS, A1.IS_PARENT, A1.ANNUAL_INCOME, 
      A1.COUNTRY_CODE, A1.RELIGION_CODE, A1.NATION_CODE, 
      A1.DRIVER_LICENSE_TYPE, A1.COMPANY_NAME, A1.OFFEN_USE_TEL, 
      A1.HOUSE_TEL, A1.FAX_TEL, A1.OFFICE_TEL, A1.MOBILE_TEL, 
      A1.EMAIL, A1.QQ, A1.WECHAT_NO, A1.OTHER, A1.CUSTOMER_LEVEL, 
      A1.CUSTOMER_RISK_LEVEL, A1.CUSTOMER_VIP, A1.SMOKING_FLAG, 
      A1.DRUNK_FLAG, A1.BLACKLIST_FLAG, A1.HOUSEKEEPER_FLAG, 
      A1.SYN_MDM_FLAG, A1.LIVE_STATUS, A1.RETIRED_FLAG, A1.DEATH_DATE, 
      A1.HEALTH_STATUS, A1.REMARK, A1.CUST_PWD,  
      A1.GENDER_DESC, A1.TYPE_NAME, A1.MARRIAGE, A1.COUNTRY_NAME, A1.TYPE, A1.LICENSE_DESC, 
      A1.CATEGORY_JOB_DESC, A1.KIND_JOB_DESC, 
	  D.NEXT_ACCOUNT, D.NEXT_ACCOUNT_NAME, D.NEXT_ACCOUNT_BANK, D.NEXT_ACCOUNT_ID, D.PAY_NEXT, 
      N.NAME, P.ADDRESS, P.POST_CODE, P.STATE, P.CITY, P.DISTRICT,
      QSTATE.NAME AS STATENAME, QCITY.NAME AS CITYNAME, QDISTRICT.NAME AS DISTRICTNAME
      FROM
      ( SELECT B.POLICY_ID, B.ADDRESS_ID, A.CUSTOMER_ID, A.UN_CUSTOMER_CODE, 
      A.OLD_CUSTOMER_ID, A.MARRIAGE_DATE, A.EDUCATION, 
      A.CUSTOMER_NAME, A.CUSTOMER_BIRTHDAY, A.CUSTOMER_GENDER, 
      A.CUSTOMER_HEIGHT, A.CUSTOMER_WEIGHT, A.CUSTOMER_CERT_TYPE, 
      A.CUSTOMER_CERTI_CODE, A.CUSTOMER_ID_CODE, 
      A.CUST_CERT_STAR_DATE, A.CUST_CERT_END_DATE, 
      A.JOB_CODE, A.JOB_NATURE, A.JOB_KIND, A.JOB_TITLE, 
      A.MARRIAGE_STATUS, A.IS_PARENT, A.ANNUAL_INCOME, 
      A.COUNTRY_CODE, A.RELIGION_CODE, A.NATION_CODE, 
      A.DRIVER_LICENSE_TYPE, A.COMPANY_NAME, A.OFFEN_USE_TEL, 
      A.HOUSE_TEL, A.FAX_TEL, A.OFFICE_TEL, A.MOBILE_TEL, 
      A.EMAIL, A.QQ, A.WECHAT_NO, A.OTHER, A.CUSTOMER_LEVEL, 
      A.CUSTOMER_RISK_LEVEL, A.CUSTOMER_VIP, A.SMOKING_FLAG, 
      A.DRUNK_FLAG, A.BLACKLIST_FLAG, A.HOUSEKEEPER_FLAG, 
      A.SYN_MDM_FLAG, A.LIVE_STATUS, A.RETIRED_FLAG, A.DEATH_DATE, 
      A.HEALTH_STATUS, A.REMARK, A.CUST_PWD,  
      E.GENDER_DESC, F.TYPE_NAME, G.MARRIAGE, H.COUNTRY_NAME, J.TYPE, K.LICENSE_DESC, 
      L.JOB_DESC AS CATEGORY_JOB_DESC, M.JOB_DESC AS KIND_JOB_DESC
      FROM T_POLICY_HOLDER B, T_PAYER C, T_CUSTOMER A
      LEFT JOIN T_GENDER E ON E.GENDER_CODE = A.CUSTOMER_GENDER 
      LEFT JOIN T_YES_NO F ON F.YES_NO = A.CUSTOMER_VIP 
      LEFT JOIN T_MARRIAGE G ON G.MARRIAGE_CODE = A.MARRIAGE_STATUS 
      LEFT JOIN T_COUNTRY H ON H.COUNTRY_CODE = A.COUNTRY_CODE 
      LEFT JOIN T_CERTI_TYPE J ON J.CODE = A.CUSTOMER_CERT_TYPE 
      LEFT JOIN T_LICENSE_TYPE K ON K.LICENSE_TYPE = A.DRIVER_LICENSE_TYPE 
      LEFT JOIN T_JOB_CATEGORY L ON L.JOB_CODE = A.JOB_CODE 
      LEFT JOIN T_JOB_KIND M ON M.JOB_KIND = A.JOB_KIND
      WHERE 
      A.CUSTOMER_ID = B.CUSTOMER_ID 
      AND C.CUSTOMER_ID = B.CUSTOMER_ID 
      AND C.POLICY_CODE = B.POLICY_CODE 
      AND B.POLICY_CODE =  #{POLICY_CODE} ) A1
      LEFT JOIN T_PAYER_ACCOUNT D ON D.POLICY_ID = A1.POLICY_ID
      LEFT JOIN T_PAY_MODE N ON N.CODE = D.PAY_NEXT
      LEFT JOIN T_ADDRESS P ON P.ADDRESS_ID = A1.ADDRESS_ID     
      LEFT JOIN T_DISTRICT QSTATE ON QSTATE.CODE = P.STATE
      LEFT JOIN T_DISTRICT QCITY ON QCITY.CODE = P.CITY
      LEFT JOIN T_DISTRICT QDISTRICT ON QDISTRICT.CODE = P.DISTRICT ]]>
	</select>
	
	<!-- 查询单条 -->
	<select id="queryPolicyHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 
		TPH.APPLY_CODE 
		FROM DEV_PAS.T_POLICY_HOLDER TPH 
		WHERE TPH.CUSTOMER_ID=#{customer_id} ]]>
	</select>
	<!-- 查询投保单号 -->
	<select id="queryApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT CI.APPLY_CODE
  			FROM DEV_PAS.T_POLICY_HOLDER CI
 			WHERE CI.CUSTOMER_ID = #{customer_id} 
 		]]>
	</select>
	<!-- 查询投保人姓名 -->
	<select id="queryAppntname" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT TC.CUSTOMER_NAME,CI.CUSTOMER_ID
  			FROM DEV_PAS.T_POLICY_HOLDER CI 
  			LEFT JOIN DEV_PAS.T_CUSTOMER TC
  			ON CI.CUSTOMER_ID = TC.CUSTOMER_ID
 			WHERE 1=1
 		]]>
 		<if test="policy_code!=null and policy_code!=''">
 		    AND  CI.POLICY_CODE = #{policy_code} 
 		</if>
 		
	</select>
	<!-- 再保险 受益人查询 -->
	<select id="findCustomerInfoByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select c.customer_id , c.customer_name ,c.customer_gender ,c.customer_cert_type , c.customer_certi_code from DEV_PAS.T_customer c 
    where c.customer_id in( select ph.customer_id from DEV_PAS.T_policy_holder ph where ph.policy_code = #{policy_code})
     ]]>
	</select>
	<!-- 再保险 受益人根据apply_code查询 -->
	<select id="findCustomerInfoByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  select c.customer_id , c.customer_name ,c.customer_gender ,c.customer_cert_type , c.customer_certi_code from DEV_PAS.T_customer c 
    where c.customer_id in( select ph.customer_id from DEV_PAS.T_policy_holder ph where ph.apply_code = #{apply_code})
     ]]>
	</select>
	<!-- 客户保单信息查询接口 -->
	<select id="QRY_findPACustomerPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT DISTINCT TCM.APPLY_CODE,
                TCM.POLICY_ID,
                TCM.VALIDATE_DATE,
                TCM.CHANNEL_TYPE,
                A.MOBILE_TEL,
                A.CUSTOMER_NAME AS HOLDER_NAME,
                INSURED.CUSTOMER_NAME AS INSURED_NAME,
                INSURED.RELATION_TO_PH AS APPNTRELATIONTOINSURED,
                BP.PAY_DUE_DATE,
                BP.POLICY_PERIOD,
                BP.ISCRS,
                BP.BUSI_ITEM_ID,
                TPA.NEXT_ACCOUNT_BANK,
                TPA.NEXT_ACCOUNT,
                TPA.NEXT_ACCOUNT_NAME,
                TPA.PAY_NEXT,
                (SELECT DECODE(AA.LIABILITY_STATE, '1', '0;') || --有效    
                        DECODE(CsLock.SUB_ID, '068', '1;') || --保全挂起   
                        DECODE(AA.END_CAUSE, '03', '2;') || --退保终止
                        DECODE(AA.END_CAUSE, '02', '3;') || --理赔终止
                        DECODE(CLMLock.SUB_ID, '067', '4;') || --理赔挂起          
                        DECODE(CLMLock.LOCK_SERVICE_ID, '93', '5;') || --理赔未决
                        DECODE(AA.END_CAUSE, '08', '6;') || --自垫终止
                        DECODE(AA.LIABILITY_STATE, '4', '7;') || --失效状态
                        DECODE((SELECT COUNT(1)
                                 FROM DEV_CAP.V_PREM_ARAP G
                                WHERE G.ARAP_FLAG in ('1', '2')
                                  AND G.FEE_STATUS = '04'
                                  AND G.POLICY_CODE = AA.POLICY_CODE),
                               '0',
                               '',
                               '8;') || --处于银行划款状态
                        DECODE((select t.initial_validate_date
                                 from dev_pas.t_contract_master t
                                where t.policy_id = AA.policy_id
                                  and to_char(t.initial_validate_date,
                                              'yyyy-MM-dd') >
                                      to_char(sysdate, 'yyyy-MM-dd')),
                               '',
                               '',
                               '9;') || --承保未生效
                        DECODE(AA.END_CAUSE, '06', '10;') || --贷款终止                         
                        DECODE((SELECT ACKNOWLEDGE_DATE
                                 FROM DEV_PAS.T_POLICY_ACKNOWLEDGEMENT M
                                WHERE M.POLICY_ID = AA.POLICY_ID),
                               '',
                               '11;') --回执未签收状态
                        AS CONTSTATE
                   FROM DEV_PAS.T_CONTRACT_MASTER AA
                   LEFT JOIN (SELECT MM.Sub_Id, BB.POLICY_ID
                               FROm DEV_PAS.T_LOCK_POLICY BB
                              INNER JOIN DEV_PAS.T_LOCK_SERVICE_DEF MM
                                 ON BB.LOCK_SERVICE_ID = MM.LOCK_SERVICE_ID
                              WHERE MM.SUB_ID = '068' AND ROWNUM=1) CsLock
                     ON AA.POLICY_ID = CsLock.Policy_Id
                   LEFT JOIN (SELECT MM.SUB_ID,
                                    BB.POLICY_ID,
                                    BB.LOCK_SERVICE_ID
                               FROm DEV_PAS.T_LOCK_POLICY BB
                              INNER JOIN DEV_PAS.T_LOCK_SERVICE_DEF MM
                                 ON BB.LOCK_SERVICE_ID = MM.LOCK_SERVICE_ID
                              WHERE MM.SUB_ID = '067' AND ROWNUM = 1) ClmLock
                     ON AA.POLICY_ID = ClmLock.Policy_Id
                  WHERE AA.POLICY_CODE = tcm.policy_code) as contState,
                (CASE
                  WHEN TPA.NEXT_ACCOUNT_BANK IS NOT NULL THEN
                   (SELECT BK.BANK_NAME
                      FROM DEV_PAS.T_BANK BK
                     WHERE BK.BANK_CODE = TPA.NEXT_ACCOUNT_BANK)
                  ELSE
                   NULL
                END) AS BANK_NAME,
                DECODE((SELECT DISTINCT 1
                         FROM DEV_PAS.T_BUSINESS_PRODUCT   S,
                              DEV_PAS.T_CONTRACT_BUSI_PROD M
                        WHERE S.BUSINESS_PRD_ID = M.BUSI_PRD_ID
                          AND S.PRODUCT_CODE_STD IN ('994', '995', '996')
                          AND M.POLICY_CODE = TCM.POLICY_CODE),
                       '1',
                       'Y',
                       '0',
                       'N',
                       '',
                       'N') AS ISTAX,
                (DECODE((SELECT 1
                          FROM DEV_PAS.T_CUSTOMER      A,
                               DEV_PAS.T_CUSTOMER_TAX  B,
                               DEV_PAS.T_POLICY_HOLDER C
                         WHERE A.CUSTOMER_NAME = B.CUSTOMER_TAX_NAME
                           AND A.CUSTOMER_GENDER = B.CUSTOMER_TAX_GENDER
                           AND A.CUSTOMER_BIRTHDAY =
                               B.CUSTOMER_TAX_BIRTHDAY
                           AND A.CUSTOMER_CERT_TYPE =
                               B.CUSTOMER_TAX_CERT_TYPE
                           AND A.CUSTOMER_CERTI_CODE =
                               B.CUSTOMER_TAX_CERTI_CODE
                           AND C.CUSTOMER_ID = A.CUSTOMER_ID
                           AND C.POLICY_CODE = TCM.POLICY_CODE),
                        '1',
                        'Y',
                        '',
                        'N')) AS POLICYTYPE,
                (DECODE((SELECT DISTINCT 1
                          FROM DEV_PAS.T_CUSTOMER        A,
                               DEV_PAS.T_INSURED_LIST    B,
                               DEV_PAS.T_BENEFIT_INSURED C,
                               DEV_PAS.T_CUSTOMER_TAX    D
                         WHERE A.CUSTOMER_ID = B.CUSTOMER_ID
                           AND B.LIST_ID = C.INSURED_ID
                           AND A.CUSTOMER_NAME = D.CUSTOMER_TAX_NAME
                           AND A.CUSTOMER_GENDER = D.CUSTOMER_TAX_GENDER
                           AND A.CUSTOMER_BIRTHDAY =
                               D.CUSTOMER_TAX_BIRTHDAY
                           AND A.CUSTOMER_CERT_TYPE =
                               D.CUSTOMER_TAX_CERT_TYPE
                           AND A.CUSTOMER_CERTI_CODE =
                               D.CUSTOMER_TAX_CERTI_CODE
                           AND B.POLICY_CODE = TCM.POLICY_CODE
                           AND C.ORDER_ID = '1'),
                        '1',
                        'Y',
                        '',
                        'N')) AS INSUREDTYPE,
                		T_AGENT.AGENT_CODE,
                        T_AGENT.AGENT_NAME,
                        T_AGENT.AGENT_GENDER,
                        T_AGENT.AGENT_STATUS,
                        T_AGENT.AGENT_MOBILE,
                        T_AGENT.POLICY_CODE
		  FROM (SELECT AD.MOBILE_TEL, C.CUSTOMER_NAME, PH.POLICY_CODE, PH.CUSTOMER_ID
		          FROM DEV_PAS.T_POLICY_HOLDER PH
		         LEFT JOIN DEV_PAS.T_CUSTOMER C
		            ON PH.CUSTOMER_ID = C.CUSTOMER_ID  
		          LEFT JOIN DEV_PAS.T_ADDRESS AD 
		          ON PH.ADDRESS_ID = AD.ADDRESS_ID) A
		 INNER JOIN DEV_PAS.T_CONTRACT_MASTER TCM
		    ON TCM.POLICY_CODE = A.POLICY_CODE
	 INNER JOIN (SELECT CE.PAY_DUE_DATE,
	                    CE.POLICY_PERIOD,
	                    CBP.POLICY_ID,
	                    DECODE(TBP.TAX_REVENUE_FLAG, '1', 'Y', '', 'N') ISCRS,
	                    CBP.BUSI_ITEM_ID
	               FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
	              INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
	                 ON CBP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
	                AND CBP.MASTER_BUSI_ITEM_ID IS NULL
	              INNER JOIN DEV_PAS.T_CONTRACT_EXTEND CE
	                 ON CP.ITEM_ID = CE.ITEM_ID
	               LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT TBP
	                 ON CBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID) BP
	    ON TCM.POLICY_ID = BP.POLICY_ID
	  LEFT JOIN (SELECT C.CUSTOMER_NAME,
	                    IL.RELATION_TO_PH,
	                    IL.POLICY_ID,
	                    BI.BUSI_ITEM_ID
	               FROM DEV_PAS.T_BENEFIT_INSURED TB
	               LEFT JOIN DEV_PAS.T_INSURED_LIST IL
	                 ON TB.INSURED_ID = IL.LIST_ID
	                AND TB.ORDER_ID = '1'
	               LEFT JOIN DEV_PAS.T_CUSTOMER C
	                 ON IL.CUSTOMER_ID = C.CUSTOMER_ID
	               LEFT JOIN DEV_PAS.T_BENEFIT_INSURED BI
	                 ON IL.LIST_ID = BI.INSURED_ID) INSURED
		    ON TCM.POLICY_ID = BP.POLICY_ID
	   AND BP.BUSI_ITEM_ID = INSURED.BUSI_ITEM_ID
		 INNER JOIN DEV_PAS.T_PAYER_ACCOUNT TPA
		    ON TCM.POLICY_ID = TPA.POLICY_ID
		 INNER JOIN (SELECT TA.AGENT_CODE,
		                    TA.AGENT_NAME,
		                    TA.AGENT_GENDER,
		                    TA.AGENT_STATUS,
		                    TA.AGENT_MOBILE,
		                    TCA.POLICY_CODE
		               FROM DEV_PAS.T_contract_agent TCA
		              INNER JOIN DEV_PAS.T_AGENT TA
		                 ON TCA.AGENT_CODE = TA.AGENT_CODE
		                AND TCA.IS_CURRENT_AGENT = 1) T_AGENT
		    ON TCM.POLICY_CODE = T_AGENT.POLICY_CODE
		 WHERE A.CUSTOMER_ID = #{customer_id}

	</select>
	
	<!-- 客户保单信息查询接口-契约 -->
	<select id="QRY_findNBCustomerPolicyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT DISTINCT TCM.APPLY_CODE,
                TCM.POLICY_ID,
                TCM.VALIDATE_DATE,
                TCM.CHANNEL_TYPE,
                A.MOBILE_TEL,
                A.CUSTOMER_NAME AS HOLDER_NAME,
                INSURED.CUSTOMER_NAME AS INSURED_NAME,
                INSURED.RELATION_TO_PH AS APPNTRELATIONTOINSURED,
                NULL AS PAY_DUE_DATE,
                NULL AS POLICY_PERIOD,
                '' AS ISCRS,
                TCBP.BUSI_ITEM_ID,
                TPA.NEXT_ACCOUNT_BANK,
                TPA.NEXT_ACCOUNT,
                TPA.NEXT_ACCOUNT_NAME,
                TPA.PAY_NEXT,
                (CASE
                  WHEN TPA.NEXT_ACCOUNT_BANK IS NOT NULL THEN
                   (SELECT BK.BANK_NAME
                      FROM DEV_PAS.T_BANK BK
                     WHERE BK.BANK_CODE = TPA.NEXT_ACCOUNT_BANK)
                  ELSE
                   NULL
                END) AS BANK_NAME,
                'N' AS ISTAX,
                'N' AS POLICYTYPE,
                'N' AS INSUREDTYPE,
                T_AGENT.AGENT_CODE,
                T_AGENT.AGENT_NAME,
                T_AGENT.AGENT_GENDER,
                T_AGENT.AGENT_STATUS,
                T_AGENT.AGENT_MOBILE,
                T_AGENT.POLICY_CODE	
		  FROM (SELECT C.MOBILE_TEL,
		               C.CUSTOMER_NAME,
		               PH.POLICY_CODE,
		               PH.APPLY_CODE,
		               PH.CUSTOMER_ID
		          FROM DEV_NB.T_NB_POLICY_HOLDER PH
		         LEFT JOIN DEV_NB.T_CUSTOMER C
		            ON PH.CUSTOMER_ID = C.CUSTOMER_ID
		         ) A
		 INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER TCM
		    ON TCM.APPLY_CODE = A.APPLY_CODE AND TCM.POLICY_CODE IS NULL
		 LEFT JOIN DEV_NB.T_NB_CONTRACT_BUSI_PROD TCBP
			ON TCM.APPLY_CODE = TCBP.APPLY_CODE AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
		 LEFT JOIN (SELECT C.CUSTOMER_NAME,
                    	IL.RELATION_TO_PH,
                    	IL.POLICY_ID,
                    	BI.BUSI_ITEM_ID
		           FROM DEV_NB.T_NB_INSURED_LIST IL
		            LEFT JOIN DEV_NB.T_CUSTOMER C
		                ON IL.CUSTOMER_ID = C.CUSTOMER_ID
               		LEFT JOIN DEV_NB.T_NB_BENEFIT_INSURED BI
                 ON IL.LIST_ID = BI.INSURED_ID
                AND BI.ORDER_ID = '1') INSURED
		    ON TCM.POLICY_ID = INSURED.POLICY_ID
		 INNER JOIN DEV_NB.T_NB_PAYER_ACCOUNT TPA
		    ON TCM.POLICY_ID = TPA.POLICY_ID
		 INNER JOIN (SELECT TA.AGENT_CODE,
		                    TA.AGENT_NAME,
		                    TA.AGENT_GENDER,
		                    TA.AGENT_STATUS,
		                    TA.AGENT_MOBILE,
		                    TCA.POLICY_CODE,
		                    TCA.APPLY_CODE
		               FROM DEV_NB.T_NB_CONTRACT_AGENT TCA
		              INNER JOIN DEV_PAS.T_AGENT TA
		                 ON TCA.AGENT_CODE = TA.AGENT_CODE) T_AGENT
		    ON TCM.APPLY_CODE = T_AGENT.APPLY_CODE
		 WHERE A.CUSTOMER_ID = #{customer_id}
			
	</select>
	<select id="findPolicyHolderIsTrust" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
			select count(1)
			  from dev_pas.t_policy_holder a
			 inner join dev_pas.t_trust_company b
			    on a.customer_id = b.customer_id
			 where 1 = 1
		]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>
	<!-- 根据保单号查询投保人表信息(保单详情信息查询接口#rm133925) -->
	<select id="PA_findCustomerHolder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.APPLY_CODE, A.POLICY_CODE, A.JOB_UNDERWRITE, A.LIST_ID, A.SOCI_SECU, A.SMOKING,
			A.POLICY_ID FROM APP___PAS__DBUSER.T_POLICY_HOLDER A WHERE 1 = 1  ]]>
		<include refid="PA_policyHolderWhereCondition" />
	</select>
</mapper>
