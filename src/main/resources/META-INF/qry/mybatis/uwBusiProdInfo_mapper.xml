<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.impl.task.dao.impl.FindUwBusiProdInfoDaoImpl">
	<sql id="findUwBusiProdInfoSql">
        <![CDATA[
        	SELECT ROWNUM AS RN,A.* FROM (SELECT  UBP.UW_BUSI_ID,
                   UP.POLICY_CODE,
                   UP.POLICY_ID,
                   UP.APPLY_CODE,
                   UM.UW_ID,
                   UM.UW_SOURCE_TYPE,
                   UM.UW_USER_ID,
                   UM.UW_STATUS_DETAIL,
                   UBP.BUSI_PROD_CODE,
                   UBP.DECISION_CODE,
                   BP.PRODUCT_NAME_SYS,
                   CASE
                       WHEN UBP.BUSI_PROD_CODE IN
                            (SELECT BPR.PRODUCT_CODE_SYS 
	                                    FROM DEV_PDS.T_BUSINESS_PRODUCT BPR
                                       WHERE 1 = 1
                                        AND BPR.WAIVER_CUSTOMER_ROLE='01'
                                        AND BPR.WAIVER_FLAG='1') THEN
                        (SELECT PH.CUSTOMER_NAME
                           FROM DEV_UW.T_POLICY_HOLDER PH
                          WHERE PH.UW_ID = UM.UW_ID
                            AND PH.APPLY_CODE = UP.APPLY_CODE)
                       ELSE
                        (SELECT IL.CUSTOMER_NAME
                           FROM DEV_UW.T_INSURED_LIST    IL,
                                DEV_UW.T_BENEFIT_INSURED BI
                          WHERE IL.LIST_ID = BI.INSURED_ID
                            AND IL.UW_ID = BI.UW_ID
                            AND IL.POLICY_ID = BI.POLICY_ID
                            AND IL.UW_ID = UM.UW_ID
                            AND IL.APPLY_CODE = UP.APPLY_CODE
                            AND UBP.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
                            AND BI.ORDER_ID = '1'
                            AND ROWNUM = '1')
                     END CUSTOMER_NAME,
                   USD.STATUS_DETAIL_NAME AS UW_STATUS_NAME,
                   PD.DECISION_DESC       AS UW_DECISION,
                   UST.TYPE_NAME          AS UW_SOURCE_NAME,
                   max(UT.TRACE_ID) AS TRACE_ID,
                   '' AS SERVICE_CODE,
       			   '' AS SERVICE_NAME,
                   UM.UW_FINISH_TIME,
                   (SELECT TUP.NON_STANDARDS
			          FROM DEV_UW.T_UW_PRODUCT TUP
			         WHERE TUP.UW_BUSI_ID = UBP.UW_BUSI_ID
			           AND ROWNUM = 1) AS NON_STANDARDS,
			       '1'  AS SHOW_POLICY,
			       UBP.IS_DISPLAY
              FROM DEV_UW.T_UW_POLICY UP
             INNER JOIN DEV_UW.T_UW_MASTER UM
                ON UM.UW_ID = UP.UW_ID
             INNER JOIN DEV_UW.T_UW_BUSI_PROD UBP
                ON UP.UW_ID = UBP.UW_ID
                AND UP.APPLY_CODE = UBP.APPLY_CODE
             INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                ON UBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
             INNER JOIN DEV_UW.T_UW_STATUS_DETAIL USD
                ON USD.STATUS_DETAIL_CODE = UM.UW_STATUS_DETAIL
             INNER JOIN DEV_UW.T_UW_SOURCE_TYPE UST
                ON UST.UW_SOURCE_TYPE = UM.UW_SOURCE_TYPE
              LEFT JOIN DEV_UW.T_PRODUCT_DECISION PD
                ON PD.DECISION_CODE = UBP.DECISION_CODE
              LEFT JOIN  DEV_PAS.T_SERVICE S ON S.SERVICE_CODE=UM.SERVICE_CODE
              left join  (select * from DEV_UW.T_UW_TRACE UT where ut.UW_EVENT_CODE='13')   UT on ut.uw_id=um.uw_id
                     WHERE 1 = 1
               AND UM.UW_SOURCE_TYPE != '2'
               ]]>
         <if test=" apply_code  != null and apply_code != '' and apply_code != 'null'">
         <![CDATA[ AND UP.APPLY_CODE = #{apply_code} ]]>
         </if>
        <![CDATA[ 
           group by  UBP.UW_BUSI_ID,
               UP.POLICY_CODE,
               UP.POLICY_ID,
               UP.APPLY_CODE,
               UM.UW_ID,
               UM.UW_SOURCE_TYPE,
               UM.UW_USER_ID,
               UM.UW_STATUS_DETAIL,
               UBP.BUSI_PROD_CODE,
               UBP.DECISION_CODE,
               BP.PRODUCT_NAME_SYS,
               USD.STATUS_DETAIL_NAME,
               PD.DECISION_DESC,     
               UST.TYPE_NAME,
               UM.UW_FINISH_TIME,
               UBP.BUSI_ITEM_ID,
               UBP.IS_DISPLAY
         ]]>
         <![CDATA[ 
         	UNION ALL
         ]]>
          <![CDATA[
           select 
                t.UW_BUSI_ID,
                   t.POLICY_CODE,
                   t.POLICY_ID,
                   t.APPLY_CODE,
                   t.UW_ID,
                   t.UW_SOURCE_TYPE,
                   t.UW_USER_ID,
                   t.UW_STATUS_DETAIL,
                   t.BUSI_PROD_CODE,
                   t.DECISION_CODE,
                  (select PRODUCT_NAME_SYS from 
                    DEV_PDS.t_Business_Product bp where bp.product_code_sys=t.Busi_Prod_Code) AS PRODUCT_NAME_SYS,
                   t.CUSTOMER_NAME,
                   t.UW_STATUS_NAME,
                   t.UW_DECISION,
                   t.UW_SOURCE_NAME,
                   t.TRACE_ID,
                   t.SERVICE_CODE,
                   t.SERVICE_NAME,
                   t.UW_FINISH_TIME,
                   t.NON_STANDARDS,
                   (CASE
	                 WHEN T.SERVICE_CODE = 'NS' OR T.RULE_RUN_STATUS = 'V02' OR T.IS_DISPLAY = '1' THEN
	                  '1'
	                 ELSE
	                  '0'
	               END) AS SHOW_POLICY,
	               T.IS_DISPLAY
            from (
        	SELECT 
                   UBP.UW_BUSI_ID,
                   UP.POLICY_CODE,
                   UP.POLICY_ID,
                   UP.APPLY_CODE,
                   UM.UW_ID,
                   UM.UW_SOURCE_TYPE,
                   UM.UW_USER_ID,
                   UM.UW_STATUS_DETAIL,
                   UBP.BUSI_PROD_CODE,
                   UBP.DECISION_CODE,
                   CASE
                       WHEN UBP.BUSI_PROD_CODE IN
                            ( SELECT BP.PRODUCT_CODE_SYS
                                        FROM DEV_PDS.T_BUSINESS_PRODUCT BP
                                          WHERE 1 = 1
                                          AND BP.WAIVER_CUSTOMER_ROLE='01'
                                          AND BP.WAIVER_FLAG='1')
                          THEN
                        (SELECT PH.CUSTOMER_NAME
                           FROM DEV_UW.T_POLICY_HOLDER PH
                          WHERE PH.UW_ID = UM.UW_ID
                            AND PH.APPLY_CODE = UP.APPLY_CODE)
                       ELSE
                        (SELECT IL.CUSTOMER_NAME
                           FROM DEV_UW.T_INSURED_LIST    IL,
                                DEV_UW.T_BENEFIT_INSURED BI
                          WHERE IL.LIST_ID = BI.INSURED_ID
                            AND IL.UW_ID = BI.UW_ID
                            AND IL.POLICY_ID = BI.POLICY_ID
                            AND IL.UW_ID = UM.UW_ID
                            AND IL.APPLY_CODE = UP.APPLY_CODE
                            AND UBP.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
                            AND BI.ORDER_ID = '1'
                            AND ROWNUM = '1')
                      END CUSTOMER_NAME,
                   USD.STATUS_DETAIL_NAME AS UW_STATUS_NAME,
                   PD.DECISION_DESC       AS UW_DECISION,
                   UST.TYPE_NAME          AS UW_SOURCE_NAME,
                   max(UT.TRACE_ID) AS TRACE_ID,
                   S.SERVICE_CODE,
       			   S.SERVICE_NAME,
                   UM.UW_FINISH_TIME,
                   (SELECT TUP.NON_STANDARDS
			          FROM DEV_UW.T_UW_PRODUCT TUP
			         WHERE TUP.UW_BUSI_ID = UBP.UW_BUSI_ID
			           AND ROWNUM = 1) AS NON_STANDARDS,
			       UA.RULE_RUN_STATUS,
			       UBP.IS_DISPLAY
              FROM DEV_UW.T_UW_POLICY UP
             INNER JOIN DEV_UW.T_UW_MASTER UM
                ON UM.UW_ID = UP.UW_ID
             INNER JOIN DEV_UW.T_UW_BUSI_PROD UBP
                ON UP.UW_ID = UBP.UW_ID
                AND UP.APPLY_CODE = UBP.APPLY_CODE
             INNER JOIN DEV_UW.T_UW_STATUS_DETAIL USD
                ON USD.STATUS_DETAIL_CODE = UM.UW_STATUS_DETAIL
             INNER JOIN DEV_UW.T_UW_SOURCE_TYPE UST
                ON UST.UW_SOURCE_TYPE = UM.UW_SOURCE_TYPE
             INNER JOIN DEV_UW.T_UW_AUTO UA 
             	ON UM.UW_ID = UA.UW_ID AND UP.APPLY_CODE = UA.APPLY_CODE
              LEFT JOIN DEV_UW.T_PRODUCT_DECISION PD
                ON PD.DECISION_CODE = UBP.DECISION_CODE
              LEFT JOIN  DEV_PAS.T_SERVICE S ON S.SERVICE_CODE=UM.SERVICE_CODE
              left join  (select * from DEV_UW.T_UW_TRACE UT where ut.UW_EVENT_CODE='13')   UT on ut.uw_id=um.uw_id
                     WHERE 1 = 1
               AND UM.UW_SOURCE_TYPE = '2'
               ]]>
         <if test=" apply_code  != null and apply_code != '' and apply_code != 'null'">
         <![CDATA[ AND UP.APPLY_CODE = #{apply_code} ]]>
         </if>
        <![CDATA[ 
           group by  UBP.UW_BUSI_ID,
               UP.POLICY_CODE,
               UP.POLICY_ID,
               UP.APPLY_CODE,
               UM.UW_ID,
               UM.UW_SOURCE_TYPE,
               UM.UW_USER_ID,
               UM.UW_STATUS_DETAIL,
               UBP.BUSI_PROD_CODE,
               UBP.DECISION_CODE,
               USD.STATUS_DETAIL_NAME,
               PD.DECISION_DESC ,     
               UST.TYPE_NAME,
               S.SERVICE_CODE,
               S.SERVICE_NAME,
               UM.UW_FINISH_TIME,
               UA.RULE_RUN_STATUS,
               UBP.BUSI_ITEM_ID,
               UBP.IS_DISPLAY
               ) t ) A WHERE A.SHOW_POLICY = '1'
         ]]>
         
    </sql>
    <!-- 查询个数操作 -->
    <select id="findUwBusiProdInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[  
  		 SELECT COUNT(1)
		  FROM (
		 ]]>
		  <include refid="findUwBusiProdInfoSql" />
		 <![CDATA[  
		  ) C
  		]]>
    </select>

    <!-- 分页查询操作 -->
     <select id="findUwBusiProdInfoPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT  B.* FROM (
         SELECT
           C.*,
           CASE WHEN C.IS_DISPLAY = 0 THEN
                 ''
                ELSE
                 (SELECT UW_COMMENTS
                    FROM DEV_UW.T_UW_TRACE UT
                   WHERE UT.TRACE_ID = C.TRACE_ID)
              END UW_COMMENTS,
	         (SELECT LISTAGG(DR.REASON_CONTENT ,';') within group (order by dr.UW_BUSI_ID)
	                FROM dev_uw.t_uw_decision_reason DR 
	                LEFT JOIN DEV_UW.t_Uw_Product p
	                ON p.uw_prd_id = dr.uw_prd_id
	                WHERE p.UW_BUSI_ID = C.UW_BUSI_ID) AS REASON_CONTENT
           FROM (
            ]]>
          <include refid="findUwBusiProdInfoSql" />
         <![CDATA[ AND ROWNUM <= #{LESS_NUM} /* #104_31754 */
         		   ORDER BY UW_FINISH_TIME
              ) C WHERE  C.RN > #{GREATER_NUM}  ]]> 
        <![CDATA[ )  B ]]>
    </select>
</mapper>
