<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.*">
<!-- 查询 满意度清单信息个数操作 -->
    <!-- <select id="CSS_findScoreTaskTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         SELECT COUNT(1) FROM(
				SELECT TST.JOB_ID AS JOB_CODE
				  FROM DEV_CSS.T_CSS_SCORE_INFO   TS,
                 DEV_CSS.T_CSS_SUB_TASK_TRAIL_INFO  TST,
                 DEV_CSS.T_CSS_MAIN_TASK_TRAIL_INFO TSS
          		 WHERE TS.TASK_CODE = TST.TASK_CODE
                 AND TSS.TASK_CODE = TS.TASK_CODE 
        <include refid="请添加查询条件" />
	   <if test=" branch_code  != null and branch_code != '' "><![CDATA[ AND TS.BRANCH_CODE  LIKE CONCAT('%',CONCAT(#{branch_code},'%'))  ]]></if> 
        <if test="job_task_type != null and job_task_type != '' "><![CDATA[ AND TST.JOB_TASK_TYPE = #{job_task_type} ]]></if>
        <if test=" emp_code  != null and emp_code != '' "><![CDATA[ AND TS.INSERT_BY =#{emp_code} ]]></if>
        <if test=" customer_type  != null and customer_type != '' "><![CDATA[ AND TS.INSERT_TIME >= to_date(#{customer_type},'yyyy-MM-dd') ]]></if> 查询条件起始以及截止时间已客户类型，客户姓名字段传递 
         <if test=" customer_name  != null and customer_name != '' "><![CDATA[ AND TS.INSERT_TIME <= to_date(#{customer_name},'yyyy-MM-dd') ]]></if> 
         	<if test=" work_Progect != null and work_Progect != '' "><![CDATA[ AND TST.SERVICE_CODE =#{work_Progect} ]]></if>
         <if test=" content_Method  != null and content_Method != '' "><![CDATA[ AND TST.JOB_CHANNEL_TYPE =#{content_Method} ]]></if>
         <if test=" handIn_Method  != null and handIn_Method != '' "><![CDATA[ AND TST.APPLY_TYPE = #{handIn_Method} ]]></if> 
         <![CDATA[)AA  LEFT JOIN DEV_PAS.t_cs_application AC
            ON AA.JOB_CODE = AC.Apply_Code
          LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE TPC
            ON AC.CHANGE_ID = TPC.CHANGE_ID
          LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM
            ON TPC.POLICY_ID = TCM.POLICY_ID ]]>
    </select> -->
<!-- 满意度分页查询  -->
	 <select id="queryScoreTaskForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
         <![CDATA[
     	  SELECT  B.* FROM (
                SELECT ROWNUM RN, AA.*,(]]>
                <include refid="queryScoreTaskInfo"></include>
         <![CDATA[   
            ) B WHERE B.RN > #{GREATER_NUM} AND B.RN <= #{LESS_NUM} ]]>
	</select>	
	<sql id="queryScoreTaskInfo">
		<![CDATA[
		select SC.SALES_CHANNEL_NAME  from DEV_PAS.T_SALES_CHANNEL SC 
  			WHERE TCM.CHANNEL_TYPE=SC.SALES_CHANNEL_CODE)  CHANNEL_TYPE FROM( SELECT  
				(CASE
         WHEN LENGTH(TS.BRANCH_CODE) = 4 THEN
          (SELECT O.ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG O
            WHERE O.ORGAN_CODE = TS.BRANCH_CODE)
         WHEN LENGTH(TS.BRANCH_CODE) = 6 OR LENGTH(TS.BRANCH_CODE) = 8 THEN
          (SELECT O.ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG O
            WHERE O.ORGAN_CODE = SUBSTR(TS.BRANCH_CODE, 0, 4))
         ELSE
          ''
       END) TWOSS,
       (CASE LENGTH(TS.BRANCH_CODE)
         WHEN 6 THEN
          (SELECT O.ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG O
            WHERE O.ORGAN_CODE = TS.BRANCH_CODE)
         WHEN 8 THEN
          (SELECT O.ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG O
            WHERE O.ORGAN_CODE = SUBSTR(TS.BRANCH_CODE, 0, 6))
         ELSE
          ''
       END) THREESS,
       (CASE LENGTH(TS.BRANCH_CODE)
         WHEN 8 THEN
          (SELECT O.ORGAN_NAME
             FROM DEV_PAS.T_UDMP_ORG O
            WHERE O.ORGAN_CODE = TS.BRANCH_CODE)
         ELSE
          ''
       END) FOURSS,
       (CASE
         WHEN LENGTH(TS.BRANCH_CODE) = 4 THEN
          TS.BRANCH_CODE
         WHEN LENGTH(TS.BRANCH_CODE) = 6 OR LENGTH(TS.BRANCH_CODE) = 8 THEN
          (SELECT O.ORGAN_CODE
             FROM DEV_PAS.T_UDMP_ORG O
            WHERE O.ORGAN_CODE = SUBSTR(TS.BRANCH_CODE, 0, 4))
         ELSE
          ''
       END) TWOS,
       
       (CASE LENGTH(TS.BRANCH_CODE)
         WHEN 6 THEN
          TS.BRANCH_CODE
         WHEN 8 THEN
          (SELECT O.ORGAN_CODE
             FROM DEV_PAS.T_UDMP_ORG O
            WHERE O.ORGAN_CODE = SUBSTR(TS.BRANCH_CODE, 0, 6))
         ELSE
          ''
       END) THREES,
       
       (CASE LENGTH(TS.BRANCH_CODE)
         WHEN 8 THEN
          TS.BRANCH_CODE
         ELSE
          ''
       END) FOURS,
       TS.BRANCH_CODE,
       (SELECT JOB_TASK_NAME FROM DEV_CSS.T_JOB_TASK_TYPE WHERE JOB_TASK_TYPE=TST.JOB_TASK_TYPE) JOB_TASK_TYPE,
       	/* 缺陷修改缺陷6615   业务类型为“理赔”，显示理赔受理的赔案号02；业务类型为“保全”，显示保全受理的受理号01；
		       		业务类型为“咨询”，显示咨询相关的客户保单号，如为填写相关保单号，则显示为空05；
		       		业务类型为“投诉”，显示为空06*/
       (CASE TST.JOB_TASK_TYPE 
       		 WHEN '01' THEN
	              (SELECT LISTAGG(CAC.ACCEPT_CODE,'<br/>') WITHIN GROUP (ORDER BY  CAC.ACCEPT_ID) FROM DEV_PAS.T_CS_ACCEPT_CHANGE CAC 
	              			INNER JOIN DEV_PAS.T_CS_POLICY_CHANGE PC ON CAC.CHANGE_ID = PC.CHANGE_ID AND CAC.ACCEPT_ID = PC.ACCEPT_ID
	              			INNER JOIN DEV_PAS.T_CS_APPLICATION CA ON PC.CHANGE_ID = CA.CHANGE_ID
	              		WHERE CA.APPLY_CODE = TST.JOB_ID
	              	)
             WHEN '05' THEN
               (SELECT DISTINCT TCP.POLICY_CODE FROM DEV_CSS.T_CSS_CONSULT_POLICY_INFO TCP
                      WHERE TCP.CONSULT_ID = TST.JOB_ID)
             WHEN '06' THEN
       			''
             ELSE
             	TST.JOB_ID
        END) JOB_CODE, 
       (SELECT TD.CODE_NAME FROM DEV_CSS.T_DEGREE  TD WHERE TD.CODE_VALUE=TS.DEGREE) DEGREE,
       TS.INSERT_TIME,
       TS.INSERT_BY,
       TS.EMP_CODE || '-' ||
       (SELECT S.REAL_NAME FROM DEV_PAS.T_UDMP_USER S WHERE TS.EMP_CODE = S.USER_ID) EMP_CODE,
        (CASE WHEN TST.JOB_TASK_TYPE='01'   THEN 
          (SELECT TYPE_NAME FROM DEV_PAS.T_SERVICE_TYPE WHERE SERVICE_TYPE=TST.APPLY_TYPE)
           WHEN TST.JOB_TASK_TYPE='02'   THEN 
            (SELECT NAME FROM DEV_CLM.T_APPLY_TYPE WHERE CODE=TST.APPLY_TYPE)
           ELSE ''
            END) HANDIN_METHOD, /*#51782缺陷修改*/
	       TSS.TASK_TYPE,
	       (SELECT SERVICE_NAME FROM DEV_CSS.T_SERVICE WHERE SERVICE_CODE=TST.SERVICE_CODE)  WORK_PROGECT,
	        (select t.code_name  from DEV_CSS.T_JOB_CHANNEL_TYPE t where t.code_value =TST.JOB_CHANNEL_TYPE) CONTENT_METHOD,
	        ES.EVALUATE_SOURCE_NAME AS  EVALUATE_SOURCE     /*评价来源*/  
	  FROM DEV_CSS.T_CSS_MAIN_TASK_TRAIL_INFO TSS
	       LEFT JOIN DEV_CSS.T_CSS_SCORE_INFO TS ON TSS.TASK_CODE = TS.TASK_CODE
	       LEFT JOIN DEV_CSS.T_CSS_SUB_TASK_TRAIL_INFO TST ON TS.TASK_CODE = TST.TASK_CODE
	       LEFT JOIN DEV_CSS.T_EVALUATE_SOURCE ES ON　TS.EVALUATE_SOURCE=ES.EVALUATE_SOURCE
	 WHERE 1=1 
    ]]>
     	   <!-- 按照机构查询条件查询 -->
        <!-- <if test=" branch_code  != null and branch_code != '' "><![CDATA[ AND ts.branch_code = #{branch_code} ]]></if>
      <include refid="添加查询条件" />
        <if test=" panel_code  != null and panel_code != '' "><![CDATA[ AND ts.panel_code = #{panel_code} ]]></if>-->
     <if test=" branch_code  != null and branch_code != '' "><![CDATA[ AND TS.BRANCH_CODE  LIKE CONCAT('%',CONCAT(#{branch_code},'%'))  ]]></if> 
        <if test="job_task_type != null and job_task_type != '' "><![CDATA[ AND TST.JOB_TASK_TYPE = #{job_task_type} ]]></if>
        <if test=" emp_code  != null and emp_code != '' "><![CDATA[ AND TS.INSERT_BY =#{emp_code} ]]></if>
        <if test=" customer_type  != null and customer_type != '' "><![CDATA[ AND to_char(TS.INSERT_TIME,'yyyy-mm-dd') >= #{customer_type} ]]></if> <!--查询条件起始以及截止时间已客户类型，客户姓名字段传递  -->
         <if test=" customer_name  != null and customer_name != '' "><![CDATA[ AND to_char(TS.INSERT_TIME,'yyyy-mm-dd') <= #{customer_name} ]]></if> 
         	<if test=" work_Progect != null and work_Progect != '' "><![CDATA[ AND TST.SERVICE_CODE =#{work_Progect} ]]></if>
         <if test=" content_Method  != null and content_Method != '' "><![CDATA[ AND TST.JOB_CHANNEL_TYPE =#{content_Method} ]]></if>
         <if test=" handIn_Method  != null and handIn_Method != '' "><![CDATA[ AND TST.APPLY_TYPE = #{handIn_Method} ]]></if> 
        <![CDATA[ ORDER BY TWOS,THREES,FOURS,INSERT_TIME DESC
         ) AA   LEFT JOIN DEV_PAS.t_cs_application AC
            ON AA.JOB_CODE = AC.Apply_Code
          LEFT JOIN DEV_PAS.T_CS_POLICY_CHANGE TPC
            ON AC.CHANGE_ID = TPC.CHANGE_ID
          LEFT JOIN DEV_PAS.T_CONTRACT_MASTER TCM
            ON TPC.POLICY_ID = TCM.POLICY_ID ]]>
         <if test="job_task_type != null and job_task_type != '' and job_task_type == '01' ">
         	<![CDATA[ WHERE AA.JOB_CODE IS NOT NULL ]]>
         </if>
	</sql>
<!-- 满意度分页查询总条数  -->
	 <select id="CSS_findScoreTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
     	 <![CDATA[ SELECT COUNT(1) FROM (
                SELECT ROWNUM RN, AA.*,(]]>
                <include refid="queryScoreTaskInfo"></include>
         <![CDATA[     ) B ]]>
	</select>	
	
	   <select id="queryCodeVal" resultType="java.util.Map"
        parameterType="java.util.Map" statementType="STATEMENT">
        <![CDATA[ SELECT ${branch_code} code,${customer_name} val FROM ${task_code}]]>
        <if test=" customer_type  != null and customer_type != '' "><![CDATA[ where ${task_code}='${customer_type}']]></if> <!--用cust_type代替为码表的key 与查询的task_code（代替key）做比较-->
        <if test=" panel_code  != null and panel_code != '' "><![CDATA[ order {emp_code}]]></if> <!--用cust_code代替为排序字段  -->
    </select>
</mapper>