<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.IBusiItemDetailInfoDao">

	<!--根据id查契约查询险种详细信息  -->
	<select id="PA_findNBContractBusiProdById" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT DISTINCT CBP.BUSI_ITEM_ID,
			       CU.CUSTOMER_NAME,
			       (SELECT cu2.CUSTOMER_NAME
	                  FROM dev_nb.t_nb_insured_list til2
	                  left join DEV_NB.T_CUSTOMER cu2
	                    on til2.customer_id = cu2.CUSTOMER_ID
	                  where 1 = 1
	                    and til2.apply_code = #{apply_code}
	                    and order_id = '2') as CUSTOMER_NAME_SEC, /*第二被保人姓名 */
                   (SELECT relation_to_insured_1
	                   FROM dev_nb.t_nb_benefit_insured
	                  where apply_code = #{apply_code}
	                    and order_id = '2') as RELATION_IN_BETWEEN,/*第二被保险人与第一被保险人关系 */
			       CU.CUSTOMER_GENDER,
			       M.POLICY_REINSURE_FLAG,/*保单转保重投标识 */
			       CBP.RENEW, /*是否自动续保*/
			       BPT.PRODUCT_CODE_SYS AS PRODUCT_CODE, /*险种代码*/
			       CBP.WAIVED_BUSI_PRODS,/*约定豁免保费险种*/
			       PTL.PRODUCT_NAME /*责任组名称*/
			  FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD CBP
  			  INNER JOIN DEV_NB.T_NB_CONTRACT_MASTER M
   			  ON CBP.APPLY_CODE = M.APPLY_CODE
			  LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BPT
			    ON CBP.BUSI_PRD_ID = BPT.BUSINESS_PRD_ID
			  LEFT JOIN (SELECT * FROM DEV_NB.T_NB_BENEFIT_INSURED WHERE ORDER_ID = 1) BI
			    ON BI.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
			  LEFT JOIN DEV_NB.T_NB_INSURED_LIST IL
			    ON IL.LIST_ID = BI.INSURED_ID
			  LEFT JOIN 
			  DEV_NB.T_CUSTOMER CU
          		ON IL.CUSTOMER_ID = CU.CUSTOMER_ID   
			  LEFT JOIN DEV_PDS.T_PRODUCT_LIFE PTL
			    ON PTL.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
			 WHERE 1 = 1
			   AND ROWNUM = 1
	   ]]>
	   	<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND CBP.BUSI_ITEM_ID  = #{busi_item_id} ]]></if>
	</select>
	<!--根据险种编号查询险种详细信息下的配置列表字段  -->
	<select id="QRY_findPagecfUnitInfo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT U.UNIT_CODE,
			       CRA.BUSI_PRD_ID,
			       U.UNIT_NAME,
			       U.LABEL_NAME,
			       U.INPUT_TYPE,
			       U.VALUE_TYPE,
			       PRE.DEFAULT_VALUE_ORIGIN AS LIST_VALUE_ORIGIN,
			       U.LIST_VALUE,
			       U.BO_PARAMETER
			  FROM DEV_NB.T_PAGECFG_PRD_ELEMENT   PRE,
			       DEV_NB.T_PAGECFG_PRD_CATE_RELA CRA,
			       DEV_NB.T_PAGECFG_UNIT          U
			 WHERE CRA.RELATION_ID = PRE.RELATION_ID
			   AND U.UNIT_CODE = PRE.UNIT_CODE AND PRE.HIDDEN=0 
	   ]]>
	   	<if test=" busi_prd_id != null and busi_prd_id != ''  "><![CDATA[ AND CRA.BUSI_PRD_ID  = #{busi_prd_id} ]]></if>
	   	<if test=" product_id != null and product_id != ''  "><![CDATA[ AND CRA.PRODUCT_ID  = #{product_id} ]]></if>
	   <![CDATA[ORDER BY U.UNIT_CODE ]]>
	</select>
	<!--查询保单层险种责任组备用表信息  -->
	<select id="QRY_findContractProductOther" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT *
  				FROM DEV_NB.T_NB_CONTRACT_PRODUCT_OTHER CPO
			 WHERE 1=1
	   ]]>
	   	<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND CPO.BUSI_ITEM_ID  = #{busi_item_id} ]]></if>
	    	<if test=" item_id != null and item_id != ''  "><![CDATA[ AND CPO.ITEM_ID  = #{item_id} ]]></if>
	</select>
	<!--查询险种信息  -->
	<select id="QRY_findBusiProductByBusinessProductCodeSys" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 
                A.PRODUCT_NAME_SYS,
                A.PRODUCT_CODE_SYS,
                A.WAIVER_CUSTOMER_ROLE
           FROM APP___PDS__DBUSER.T_BUSINESS_PRODUCT A
           LEFT JOIN APP___PDS__DBUSER.T_PRODUCT_LIFE B
             ON A.BUSINESS_PRD_ID = B.BUSINESS_PRD_ID
          WHERE 1 = 1
            AND A.PRODUCT_CODE_SYS = '00' || B.INTERNAL_ID  ]]>
		<if test=" product_code != null and product_code != '' "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code} ]]></if> 
	</select>
	<!--查询保单层险种责任组信息  -->
	<select id="QRY_findContractProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT CP.AMOUNT, /*保额*/
				   CP.PRODUCT_CODE,/*险种编码*/
				       (CASE 
				          WHEN CP.CHARGE_PERIOD = '1' THEN
				            '一次交清'
				             WHEN CP.CHARGE_PERIOD = '2' THEN
				            CP.CHARGE_YEAR||'年'
				             WHEN CP.CHARGE_PERIOD = '3' THEN
				            '交至'||CP.CHARGE_YEAR||'岁'
				            WHEN CP.CHARGE_PERIOD = '4' THEN
				            '终身交费'
				            WHEN CP.CHARGE_PERIOD = '5' THEN
				            '不定期交'
				            WHEN CP.CHARGE_PERIOD = '6' THEN
				            CP.CHARGE_YEAR||'月'
				            WHEN CP.CHARGE_PERIOD = '7' THEN
				            CP.CHARGE_YEAR||'天'
				            WHEN CP.CHARGE_PERIOD = '8' THEN
				            TCP.CHARGE_DESC
				            WHEN CP.CHARGE_PERIOD = '9' THEN
			            	TCP.CHARGE_DESC
				         ELSE
				        ''
				       END) AS CHARGE_PERIOD,/*缴费期间*/
			       TCP.CHARGE_DESC             AS CHARGE_PERIOD, /*缴费年期类型*/
			       CP.CHARGE_YEAR, /*缴费年期*/
			       TCM.CHARGE_NAME             AS PREM_FREQ, /*当期缴费频率*/
			       CP.COVERAGE_PERIOD, /*保障年期类型*/
			       CP.COVERAGE_YEAR, /*保障年期*/
			       CP.PAY_PERIOD, /*领取年期类型*/
			       CP.PAY_YEAR, /*领取年期*/
			       TPT.PAY_NAME                AS PAY_FREQ, /*领取频率*/
			       CP.DECISION_CODE, /*核保决定*/
			       CP.UW_COMPLETE_DESC, /*核保结论描述*/
			       CP.STD_PREM_AF, /*标准保费*/
			       CP.INITIAL_DISCNT_PREM_AF, /*首期折扣后保费*/
			       CP.RENEWAL_DISCNTED_PREM_AF, /*续期折扣后保费*/
			       CP.EXTRA_PREM_AF, /*加费*/
			       CP.INITIAL_EXTRA_PREM_AF, /*首期折扣后加费*/
			       CP.RENEWAL_EXTRA_PREM_AF, /*续期折扣后加费*/
			       CP.TOTAL_PREM_AF, /*总保费*/
			       CP.IS_GIFT, /*赠险标记*/
			       CP.HEALTH_SERVICE_FLAG, /*健康服务标志*/
			       CP.INTEREST_MODE, /*利差领取方式*/
			       CP.PAIDUP_DATE, /*缴费终止日期*/
			       TCPT.ANNU_NAME              AS ANNU_PAY_TYPE, /*年金领取方式*/
			       CP.ADDITIONAL_PREM_AF, /*额外保费*/
			       CP.APPEND_PREM_AF, /*追加保费*/
			       CP.IS_WAIVED, /*是否是豁免险的豁免对象*/
			       CP.WAIVER_START, /*豁免开始日期*/
			       CP.WAIVER_END, /*豁免结束日期*/
			        CP.IRREGULAR_PREM, /*首次不定期交保险费*/
			        CP.START_PAY_AGE,/*养老年金开始领取年龄*/
      			   CP.START_PAY_DATE,/*养老年金开始领取日*/
      			   PO.FIELD8,/*保证领取期间类型*/
       			   PO.FIELD9,/*保证领取期间*/ 
       			   PO.MAX_BENEFIT_PERIOD,/*长期护理保险金最高给付期限*/
       			   CP.PAYOUT_RATE * 100 || '%' AS PAYOUT_RATE,/*赔付比例*/
			       CP.DEDUCTIBLE_FRANCHISE,/*免赔额*/ 
			       CP.BONUS_MODE_CODE,/*红利领取方式*/
			       (SELECT CR.ASSIGN_RATE * 100 || '%'
			          FROM DEV_NB.T_NB_CONTRACT_INVEST_RATE CR
			         WHERE CR.APPLY_CODE = CP.APPLY_CODE
			           AND CR.ITEM_ID = CP.ITEM_ID
			           AND CR.ACCOUNT_CODE in( '928000','928100','Z01000')
			           AND ROWNUM = 1) AS ASSIGN_RATE_ONE, /*稳健回报型投资组合分配比例*/
			       (SELECT CR.ASSIGN_RATE * 100 || '%'
			          FROM DEV_NB.T_NB_CONTRACT_INVEST_RATE CR
			         WHERE CR.APPLY_CODE = CP.APPLY_CODE
			           AND CR.ITEM_ID = CP.ITEM_ID
			           AND CR.ACCOUNT_CODE in( '928001','928101','Z01001')
			           AND ROWNUM = 1) AS ASSIGN_RATE_TWO /*积极进取型投资组合分配比例(%)*/
			  FROM DEV_NB.T_NB_CONTRACT_PRODUCT CP
			   LEFT JOIN DEV_NB.T_NB_CONTRACT_PRODUCT_OTHER PO
    			ON CP.BUSI_ITEM_ID = PO.BUSI_ITEM_ID
			  LEFT JOIN DEV_NB.T_CHARGE_PERIOD TCP
			    ON CP.CHARGE_PERIOD = TCP.CHARGE_PERIOD
			  LEFT JOIN DEV_NB.T_CHARGE_MODE TCM
			    ON CP.PREM_FREQ = TCM.CHARGE_TYPE
			  LEFT JOIN DEV_NB.T_COVERAGE_PERIOD TCPE
			    ON CP.COVERAGE_PERIOD = TCPE.COVERAGE_PERIOD
			  LEFT JOIN DEV_NB.T_PAY_TYPE TPT
			    ON CP.PAY_FREQ = TPT.PAY_TYPE
			  LEFT JOIN DEV_NB.T_ANNU_PAY_TYPE TCPT
			    ON CP.ANNU_PAY_TYPE = TCPT.ANNU_TYPE
			    WHERE 1=1
	   ]]>
	   	<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND CP.BUSI_ITEM_ID  = #{busi_item_id} ]]></if>
	   	<if test=" item_id != null and item_id != ''  "><![CDATA[ AND CP.ITEM_ID  = #{item_id} ]]></if>
	</select>
    <!-- 根据险种ID查询险种详细信息 -->
    <select id="PA_findContractBusiProdById" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
SELECT
BPT.PRODUCT_CATEGORY,
(CASE 
  WHEN  CBP.MASTER_BUSI_ITEM_ID IS NOT NULL        
    THEN (SELECT BP.BUSI_PRD_ID FROM DEV_PAS.T_CONTRACT_BUSI_PROD BP WHERE BP.BUSI_ITEM_ID = CBP.MASTER_BUSI_ITEM_ID)
      ELSE  NULL
     END) MASTER_BUSI_ITEM_ID,
CBP.BUSI_ITEM_ID,
CBP.LIABILITY_STATE,
CBP.APPLY_DATE,
CBP.VALIDATE_DATE,
CBP.EXPIRY_DATE,
CBP.PRD_PKG_CODE,
CBP.LAPSE_CAUSE,
CBP.END_CAUSE,
CBP.LAPSE_DATE,
CBP.RENEW_DECISION,
CBP.RENEW,
M.POLICY_REINSURE_FLAG,/*保单转保重投标识 */
CBP.BUSI_PROD_CODE,/*险种代码*/
BI.ORDER_ID,
CU.CUSTOMER_ID,
CU.CUSTOMER_NAME,
CU.CUSTOMER_GENDER
FROM DEV_PAS.T_CONTRACT_BUSI_PROD CBP
INNER JOIN DEV_PAS.T_CONTRACT_MASTER  M ON CBP.POLICY_CODE=M.POLICY_CODE
LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BPT
ON CBP.BUSI_PRD_ID = BPT.BUSINESS_PRD_ID
LEFT JOIN  
(SELECT * FROM DEV_PAS.T_BENEFIT_INSURED WHERE ORDER_ID=1)BI
ON BI.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
LEFT JOIN 
DEV_PAS.T_INSURED_LIST IL
ON IL.LIST_ID = BI.INSURED_ID 
LEFT JOIN 
DEV_PAS.T_CUSTOMER CU
ON IL.CUSTOMER_ID = CU.CUSTOMER_ID
WHERE 1=1 
   ]]>
   <if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND CBP.BUSI_ITEM_ID  = #{busi_item_id} ]]></if>
    </select>
    
<!--    查询第一、第二被保险人 -->
<select id="PA_findInsuredFirstSecond" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[
			SELECT BI.ORDER_ID,
			       IL.CUSTOMER_ID,
			       (CASE
			         WHEN BI.PRODUCT_CODE IN
			             (select tbp.product_code_sys from dev_pas.t_business_product tbp where tbp.waiver_customer_role='01') THEN
			          PC.CUSTOMER_NAME
			         ELSE
			          C.CUSTOMER_NAME
			       END) CUSTOMER_NAME,
			       (CASE
			         WHEN BI.PRODUCT_CODE IN
			              (select tbp.product_code_sys from dev_pas.t_business_product tbp where tbp.waiver_customer_role='01') THEN
			          PC.CUSTOMER_GENDER
			         ELSE
			          C.CUSTOMER_GENDER
			       END) CUSTOMER_GENDER
			  FROM DEV_PAS.T_BENEFIT_INSURED BI
			 INNER JOIN DEV_PAS.T_INSURED_LIST IL
			    ON IL.LIST_ID = BI.INSURED_ID
			 INNER JOIN DEV_PAS.T_CUSTOMER C
			    ON C.CUSTOMER_ID = IL.CUSTOMER_ID
			 INNER JOIN DEV_PAS.T_POLICY_HOLDER PH
			    ON BI.POLICY_ID = PH.POLICY_ID
			 INNER JOIN DEV_PAS.T_CUSTOMER PC
			    ON PH.CUSTOMER_ID = PC.CUSTOMER_ID
			 WHERE 1 = 1
 ]]>
 <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND BI.POLICY_ID = #{policy_code} ]]></if>
 <if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND BI.BUSI_ITEM_ID  = #{busi_item_id} ]]></if>
</select>
    
    <!-- 根据责任组ID查询责任组详细信息 -->
    <select id="PA_findContractProductById" resultType="java.util.Map"
        parameterType="java.util.Map">
	   <![CDATA[
	   SELECT T.* ,nvl2(BENEFIT_LEVEL1,'计划' || BENEFIT_LEVEL1,'') AS BENEFIT_LEVEL FROM (
	   		SELECT CBP.INITIAL_VALIDATE_DATE, /*续保保单的原始生效日期 */
	          CBP.RENEW, /*是否自动续保 */
	          CBP.Busi_Prod_Code,/*所属业务产品代码*/
	          CP.ITEM_ID, /*责任组ID*/
	          CP.PRODUCT_ID, /*精算产品ID*/
	          CP.BUSI_ITEM_ID, /*险种ID*/
	          CBP.BUSI_PRD_ID, /*产品ID*/
	          CP.APPLY_CODE, /*投保单号*/
	          CP.POLICY_CODE, /*保单号*/
	          CP.AMOUNT, /*保额*/
	          CP.UNIT, /*份数*/
	          CP.APPLY_DATE, /*投保日期*/
	          CP.VALIDATE_DATE, /*生效日期*/
	          CP.EXPIRY_DATE, /*终止日期*/
	          CP.PAIDUP_DATE, /*缴费终止日期*/
	          CP.LIABILITY_STATE, /*责任组状态*/
	          CP.END_CAUSE, /*责任组终止原因*/
	          CP.PREM_FREQ, /*交费方式*/
	          CP.CHARGE_YEAR, /*交费年期*/
	          CP.CHARGE_PERIOD, /*交费年期类型*/
	          CP.COVERAGE_YEAR, /*保障年期*/
	          CP.COVERAGE_PERIOD, /*保障年期类型*/
	          (CASE
	            WHEN CPO.FIELD1 = '1' THEN
	             '一'
	            WHEN CPO.FIELD1 = '2' THEN
	             '二'
	            WHEN CPO.FIELD1 = '3' THEN
	             '三'
	            WHEN CPO.FIELD1 = '4' THEN
	             '四'
	            WHEN CPO.FIELD1 = '5' THEN
	             '五'
	            WHEN CPO.FIELD1 = '1' THEN
	             '六'
	            ELSE
	             NULL
	          END) AS BENEFIT_LEVEL1, /*投保档次*/
	          CP.STD_PREM_AF, /*标准期交保费*/
	          CP.RENEWAL_DISCNTED_PREM_AF, /*折扣后保费*/
	          CP.INITIAL_DISCNT_PREM_AF + CP.EXTRA_PREM_AF INITIAL_DISCNT_PREM_AF, /*折扣保费*/
	          CP.BONUS_SA, /*累计红利保额*/
	          CP.LAPSE_CAUSE, /*失效原因*/
	          CP.WAIVER_START, /*豁免开始时间*/
	          CP.WAIVER_END, /*豁免结束时间*/
	          CP.APPEND_PREM_AF, /*期交保费加费总额*/
	          CP.PAYOUT_RATE, /*赔付比例*/
	          CP.DEDUCTIBLE_FRANCHISE, /*免赔额*/
	          ILB.INSURED_AGE /*投保年龄*/,
	          EP.EXTRA_PREM, /*加费金额*/
	          (SELECT EP.EXTRA_TYPE
	             FROM DEV_PAS.T_EXTRA_PREM EP
	            WHERE EP.APPLY_CODE = CP.APPLY_CODE
	              AND EP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID) EXTRA_TYPE,
	          (CASE
	            WHEN CBP.SUSPEND_CAUSE = '01' THEN
	             '1'
	            WHEN CBP.SUSPEND_CAUSE IS NULL OR
	                 CBP.SUSPEND_CAUSE = '' THEN
	             '0'
	          END) SUSPEND, /*是否预中止*/
	          (SELECT M.POLICY_CODE
                  FROM DEV_PAS.T_CONTRACT_MASTER M
                 WHERE M.POLICY_ID = CM.FORMER_ID) AS OLD_POLICY_CODE, /*续保前保单号*/
          M.POLICY_REINSURE_FLAG, /*保单续投转投标识*/
          NVL(CP.START_PAY_AGE,CP.PAY_YEAR) START_PAY_AGE, /*养老年间开始领取年龄*/
	      CP.START_PAY_DATE, /*养老年金开始领取日*/
	      PO.FIELD8,/*保证领取期间类型*/
	      PO.FIELD9,/*保证领取期间*/
	      TPT.PAY_NAME   AS PAY_FREQ, /*领取频率*/
	      (SELECT CR.ASSIGN_RATE * 100 || '%'
	         FROM DEV_PAS.T_CONTRACT_INVEST_RATE  CR
	        WHERE CR.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
	          AND CR.ITEM_ID = CP.ITEM_ID
	          AND CR.ACCOUNT_CODE in( '928000','928100','Z01000')
	          AND ROWNUM = 1) AS ASSIGN_RATE_ONE, /*稳健回报型投资组合分配比例*/
	      (SELECT CR.ASSIGN_RATE * 100 || '%'
	         FROM DEV_PAS.T_CONTRACT_INVEST_RATE  CR
	        WHERE CR.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
	          AND CR.ITEM_ID = CP.ITEM_ID
	          AND CR.ACCOUNT_CODE in( '928001','928101','Z01001')
	          AND ROWNUM = 1) AS ASSIGN_RATE_TWO, /*积极进取型投资组合分配比例(%)*/
	      CP.IRREGULAR_PREM  /*首次不定期交保险费*/
	     FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         INNER JOIN DEV_PAS.T_CONTRACT_MASTER M ON CP.POLICY_ID=M.POLICY_ID
	     LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT_OTHER CPO
	       ON CP.APPLY_CODE = CPO.APPLY_CODE
	      AND CP.BUSI_ITEM_ID = CPO.BUSI_ITEM_ID
	      AND CP.ITEM_ID=CPO.ITEM_ID
	     LEFT JOIN DEV_PAS.T_EXTRA_PREM EP
	       ON EP.APPLY_CODE = CP.APPLY_CODE
	      AND EP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
	      AND EP.ITEM_ID=CP.ITEM_ID
	    INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
	       ON CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
	    LEFT JOIN (SELECT IL.INSURED_AGE, BI.BUSI_ITEM_ID
	                 FROM DEV_PAS.T_INSURED_LIST IL
	                INNER JOIN DEV_PAS.T_BENEFIT_INSURED BI
	                   ON BI.INSURED_ID = IL.LIST_ID
	                WHERE BI.ORDER_ID = '1') ILB
	       ON CBP.BUSI_ITEM_ID = ILB.BUSI_ITEM_ID
	       LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
	           ON CP.POLICY_CODE = CM.POLICY_CODE
    LEFT JOIN DEV_PAS.T_PAY_TYPE            TPT  ON CP.PAY_FREQ = TPT.PAY_TYPE
    LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT_OTHER  PO ON CP.BUSI_ITEM_ID = PO.BUSI_ITEM_ID	           
	  WHERE 1=1
	        ]]>
	    <if test=" item_id != null and item_id != ''  "><![CDATA[ AND CP.ITEM_ID  = #{item_id} ]]></if>
	    ) T
</select>
<!-- 根据责任组ID查询责任组投资账户记录 -->
    <select id="QRY_findInvestAccountInfoById" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT 
  FF.FUND_NAME,--基金名称
  FF.FUND_CURRENCY,--基金币种
  CI.ACCUM_UNITS,--基金单位
  UP.PRICING_DATE,--价格生效日期
 (UP.OFF_PRICE/UP.BID_PRICE) AS PRICE,--基金价格
  CI.INTEREST_CAPITAL,---基金账户价值
  CIR.ASSIGN_RATE--分配比例
FROM 
  DEV_PAS.T_CONTRACT_INVEST CI
INNER JOIN
   DEV_PAS.T_INVEST_UNIT_PRICE UP
ON CI.ACCOUNT_CODE=UP.INVEST_ACCOUNT_CODE
INNER JOIN　 DEV_PAS.T_CONTRACT_INVEST_RATE CIR
ON UP.INVEST_ACCOUNT_CODE=CIR.ACCOUNT_CODE
INNER JOIN  DEV_PDS.T_FUND FF
ON CI.ACCOUNT_CODE=FF.FUND_CODE
WHERE 1=1
        ]]>
<if test=" item_id != null and item_id != ''  "><![CDATA[ AND CI.ITEM_ID  = #{item_id} ]]></if>
</select>
<!--根据责任组ID查询责任组投资账户记录总数  -->
<select id="findInvestAccountInfoByIdPageToatl" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[SELECT  COUNT(1) FROM
			(
			 SELECT 
			       FF.FUND_NAME, --基金名称
			       FF.FUND_CURRENCY, --基金币种
			       CI.ACCUM_UNITS, --基金单位
			       UP.PRICING_DATE, --价格生效日期
			       (UP.OFF_PRICE / UP.BID_PRICE) AS PRICE, --基金价格
			       CI.INTEREST_CAPITAL, ---基金账户价值
			       CIR.ASSIGN_RATE --分配比例
			  FROM DEV_PAS.T_CONTRACT_INVEST CI
			 INNER JOIN DEV_PAS.T_INVEST_UNIT_PRICE UP
			    ON CI.ACCOUNT_CODE = UP.INVEST_ACCOUNT_CODE 
			 INNER JOIN　 DEV_PAS.T_CONTRACT_INVEST_RATE CIR
			    ON CI.Account_Code = CIR.ACCOUNT_CODE AND cir.item_id =CI.Item_Id AND ci.busi_item_id=cir.busi_item_id
			 INNER JOIN DEV_PDS.T_FUND FF
			    ON CI.ACCOUNT_CODE = FF.FUND_CODE
			 WHERE 1=1
			 AND EXISTS (SELECT 1 FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
                          DEV_PDS.T_BUSINESS_PRODUCT TBP WHERE TCBP.BUSI_PRD_ID=TBP.BUSINESS_PRD_ID 
                          AND TBP.PRODUCT_CATEGORY1 = '20003')
                          ]]>
		<if test=" item_id != null and item_id != ''  "><![CDATA[ AND CI.ITEM_ID  = #{item_id} ]]></if>
		UNION
		
		SELECT FF.FUND_NAME, --基金名称
		       FF.FUND_CURRENCY, --基金币种
		       CI.ACCUM_UNITS, --基金单位
		       (CASE CI.INVEST_ACCOUNT_TYPE
		         WHEN 1 THEN
		          (SELECT PRICING_DATE
		             FROM (SELECT PRICING_DATE, ROWNUM AS RN
		                     FROM (SELECT TUP.PRICING_DATE, ROWNUM AS RN
		                             FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
		                            INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
		                               ON TUP.INVEST_ACCOUNT_CODE = CI.ACCOUNT_CODE
		                            WHERE CI.ITEM_ID = #{item_id}
		                            ORDER BY TUP.PRICING_DATE DESC) C) D
		            WHERE D.RN = 1)
		         WHEN 2 THEN
		          null
		       END) AS PRICING_DATE, --价格生效日期
		       (CASE CI.INVEST_ACCOUNT_TYPE
		         WHEN 1 THEN
		          (SELECT BID_PRICE
		             FROM (SELECT BID_PRICE, ROWNUM AS RN
		                     FROM (SELECT TUP.BID_PRICE, ROWNUM AS RN
		                             FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
		                            INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
		                               ON TUP.INVEST_ACCOUNT_CODE = CI.ACCOUNT_CODE
		                            WHERE CI.ITEM_ID = #{item_id}
		                            ORDER BY TUP.PRICING_DATE DESC) C) D
		            WHERE D.RN = 1)
		         WHEN 2 THEN
		          null
		       END) AS PRICE, --基金价格
		       (CASE CI.INVEST_ACCOUNT_TYPE
		         WHEN 1 THEN
		          (SELECT BID_PRICE
		             FROM (SELECT BID_PRICE, ROWNUM AS RN
		                     FROM (SELECT TUP.BID_PRICE, ROWNUM AS RN
		                             FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
		                            INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
		                               ON TUP.INVEST_ACCOUNT_CODE = CI.ACCOUNT_CODE
		                            WHERE CI.ITEM_ID = #{item_id}
		                            ORDER BY TUP.PRICING_DATE DESC) C) D
		            WHERE D.RN = 1)
		         WHEN 2 THEN
		          null
		       END) * CI.ACCUM_UNITS AS INTEREST_CAPITAL, ---基金账户价值
		       null AS ASSIGN_RATE --分配比例
		  FROM DEV_PAS.T_CONTRACT_INVEST CI
		 INNER JOIN DEV_PDS.T_FUND FF
		    ON CI.ACCOUNT_CODE = FF.FUND_CODE
		 WHERE 1 = 1
		   AND EXISTS (SELECT 1
		          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
		               DEV_PDS.T_BUSINESS_PRODUCT   TBP
		         WHERE TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		           AND TBP.PRODUCT_CATEGORY1 = '20004')
		<if test=" item_id != null and item_id != ''  "><![CDATA[ AND CI.ITEM_ID  = #{item_id} ]]></if>
        <![CDATA[ ) B]]>
	</select>
<!-- 根据责任组ID查询责任组投资账户记录  -->
<select id="findInvestAccountInfoByIdPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT c.* FROM (
		  SELECT ROWNUM AS rn,B.* FROM (SELECT 		
		  FF.FUND_NAME,--基金名称
		  FF.FUND_CURRENCY,--基金币种
		  CI.ACCUM_UNITS,--基金单位
		  UP.PRICING_DATE,--价格生效日期
		 (UP.OFF_PRICE/UP.BID_PRICE) AS PRICE,--基金价格
		  CI.INTEREST_CAPITAL,---基金账户价值
		  CIR.ASSIGN_RATE--分配比例
		FROM 
		  DEV_PAS.T_CONTRACT_INVEST CI
		INNER JOIN
		   DEV_PAS.T_INVEST_UNIT_PRICE UP
		ON CI.ACCOUNT_CODE=UP.INVEST_ACCOUNT_CODE
		INNER JOIN　 DEV_PAS.T_CONTRACT_INVEST_RATE CIR
		ON UP.INVEST_ACCOUNT_CODE=CIR.ACCOUNT_CODE AND CIR.ITEM_ID =CI.ITEM_ID AND CI.BUSI_ITEM_ID=CIR.BUSI_ITEM_ID
		INNER JOIN  DEV_PDS.T_FUND FF
		ON CI.ACCOUNT_CODE=FF.FUND_CODE
		WHERE 1=1]]>
		<if test=" item_id != null and item_id != ''  "><![CDATA[AND CI.ITEM_ID  = #{item_id} ]]></if>
		UNION		
		SELECT FF.FUND_NAME, --基金名称
		       FF.FUND_CURRENCY, --基金币种
		       CI.ACCUM_UNITS, --基金单位
		       (CASE CI.INVEST_ACCOUNT_TYPE
		         WHEN 1 THEN
		          (SELECT PRICING_DATE
		             FROM (SELECT PRICING_DATE, ROWNUM AS RN
		                     FROM (SELECT TUP.PRICING_DATE, ROWNUM AS RN
		                             FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
		                            INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
		                               ON TUP.INVEST_ACCOUNT_CODE = CI.ACCOUNT_CODE
		                            WHERE CI.ITEM_ID = #{item_id}
		                            ORDER BY TUP.PRICING_DATE DESC) C) D
		            WHERE D.RN = 1)
		         WHEN 2 THEN
		          null
		       END) AS PRICING_DATE, --价格生效日期
		       (CASE CI.INVEST_ACCOUNT_TYPE
		         WHEN 1 THEN
		          (SELECT BID_PRICE
		             FROM (SELECT BID_PRICE, ROWNUM AS RN
		                     FROM (SELECT TUP.BID_PRICE, ROWNUM AS RN
		                             FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
		                            INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
		                               ON TUP.INVEST_ACCOUNT_CODE = CI.ACCOUNT_CODE
		                            WHERE CI.ITEM_ID = #{item_id}
		                            ORDER BY TUP.PRICING_DATE DESC) C) D
		            WHERE D.RN = 1)
		         WHEN 2 THEN
		          null
		       END) AS PRICE, --基金价格
		       ROUND((CASE CI.INVEST_ACCOUNT_TYPE
		         WHEN 1 THEN
		          (SELECT BID_PRICE
		             FROM (SELECT BID_PRICE, ROWNUM AS RN
		                     FROM (SELECT TUP.BID_PRICE, ROWNUM AS RN
		                             FROM DEV_PAS.T_INVEST_UNIT_PRICE TUP
		                            INNER JOIN DEV_PAS.T_CONTRACT_INVEST CI
		                               ON TUP.INVEST_ACCOUNT_CODE = CI.ACCOUNT_CODE
		                            WHERE CI.ITEM_ID = #{item_id}
		                            ORDER BY TUP.PRICING_DATE DESC) C) D
		            WHERE D.RN = 1)
		         WHEN 2 THEN
		          null
		       END) * CI.ACCUM_UNITS,2) AS INTEREST_CAPITAL, ---基金账户价值
		       null AS ASSIGN_RATE --分配比例
		  FROM DEV_PAS.T_CONTRACT_INVEST CI
		 INNER JOIN DEV_PDS.T_FUND FF
		    ON CI.ACCOUNT_CODE = FF.FUND_CODE
		 WHERE 1 = 1
		   AND EXISTS (SELECT 1
		          FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
		               DEV_PDS.T_BUSINESS_PRODUCT   TBP
		         WHERE TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
		           AND TBP.PRODUCT_CATEGORY1 = '20004')
		<if test=" item_id != null and item_id != ''  "><![CDATA[ AND CI.ITEM_ID  = #{item_id} ]]></if>
        <![CDATA[
          ) B where 1=1 and ROWNUM <= #{LESS_NUM} ) C
        WHERE C.RN > #{GREATER_NUM} ]]>
	</select>

<!-- 根据责任组ID查询责任组待处理基金交易记录 -->
	<select id="QRY_findPendFundTransactionById" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT
  TA.ITEM_ID,
  TA.APPLY_ID,--申请编号
  (SELECT Z.DESCRIPTION 
    FROM DEV_PAS.T_TRANSACTION_CODE Z 
    WHERE Z.TRANS_CODE=TA.TRANS_CODE) TRANS_CODE,--申请类型
  TA.FUND_CODE,--基金代码
  FF.FUND_NAME,--基金名称
  FF.FUND_CURRENCY,--基金币种 
  TA.TARGET_CODE,--目标基金代码
  TA.APPLY_TIME,--交易申请时间
  TA.APPLY_AMOUNT,--待交易金额（保单币种）
 -- TA.APPLY_AMOUNT,--待交易金额（基金币种）
  TA.APPLY_UNITS --待交易单位数 
FROM 
  DEV_PAS.T_FUND_TRANS_APPLY TA
LEFT JOIN 
    DEV_PDS.T_FUND FF 
ON 
  TA.FUND_CODE=FF.FUND_CODE
WHERE 1=1 AND TA.FUND_PROCESS_STATUS='0'
		]]>
<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND TA.BUSI_ITEM_ID  = #{busi_item_id} ]]></if>
<![CDATA[ ORDER BY TA.APPLY_TIME DESC ]]>
</select>
<!-- 根据责任组ID查询责任组基金交易记录 -->
<!-- 保单组把表DEV_PAS.T_FUND_TRANS FT中的字段：价格生效日删除了，所以为空 -->
	<select id="QRY_findFundTransactionById" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
	SELECT * FROM (		
		SELECT 
    FT.ITEM_ID,
    FT.TRANS_ID,--交易流水号
    (SELECT Z.DESCRIPTION 
    FROM DEV_PAS.T_TRANSACTION_CODE Z 
    WHERE Z.TRANS_CODE=FT.TRANS_CODE) TRANS_CODE,--申请类型
    FT.FUND_CODE,--基金代码
    FF.FUND_NAME,--基金名称
    FF.FUND_CURRENCY,--基金币种
    FT.TRANS_AMOUNT,--交易额(基金币种) 
    FT.TRANS_UNITS,--交易单位数
    FT.TRANS_PRICE,--交易价格
    FT.DEAL_TIME,--交易确认日期
    (CASE   
       WHEN FT.TRANS_CODE = '13' OR FT.TRANS_CODE = '22'          
         THEN
        (SELECT TCAC.ACCEPT_TIME 
          FROM DEV_PAS.T_FUND_TRANS_APPLY TFTA,
          DEV_PAS.T_CS_ACCEPT_CHANGE TCAC 
          WHERE TFTA.ACCEPT_CODE=TCAC.ACCEPT_CODE 
          AND TFTA.APPLY_ID=FT.APPLY_ID
          AND ROWNUM=1)
       ELSE
        NULL
     END) AS APPLY_TIME,--生效日期
    FT.DEAL_TIME AS PRICING_EFFECTIVE_DATE--价格生效日
FROM 
    DEV_PAS.T_FUND_TRANS FT 
LEFT JOIN 
    DEV_PDS.T_FUND FF 
ON 
    FT.FUND_CODE=FF.FUND_CODE
WHERE 1=1
		]]>
<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND FT.BUSI_ITEM_ID  = #{busi_item_id} ]]></if>

<![CDATA[     
		UNION 
		 SELECT A.ITEM_ID,
		        NULL AS TRANS_ID, --交易流水号
		        '资产管理费' AS TRANS_CODE, --申请类型
		        A.ACCOUNT_CODE AS FUND_CODE, --基金代码
		        C.FUND_NAME, --基金名称
		        C.FUND_CURRENCY, --基金币种
		        ROUND((A.ACCUM_UNITS * NVL(B.UNIT_ASSET_M_FEE, 0)), 2) AS TRANS_AMOUNT, --交易额(基金币种) 
		        A.ACCUM_UNITS AS TRANS_UNITS, -- 单位数
		        NVL(B.UNIT_ASSET_M_FEE, 0) AS TRANS_PRICE, -- 交易价格
		        B.PRICING_DATE AS DEAL_TIME, -- 交易确认日期
		        B.PRICING_DATE AS APPLY_TIME, -- 交易确认日期
		        B.PRICING_DATE AS PRICING_EFFECTIVE_DATE -- 交易确认日期
		   FROM DEV_PAS.T_CONTRACT_INVEST   A,
		        DEV_PAS.T_INVEST_UNIT_PRICE B,
		        DEV_PDS.T_FUND              C
		  WHERE 1 = 1
		    AND A.ACCOUNT_CODE = B.INVEST_ACCOUNT_CODE
		    AND A.ACCOUNT_CODE = C.FUND_CODE
		    AND A.ACCOUNT_CODE = '892001' 
		    AND B.PRICING_DATE >= A.CREATE_DATE ]]>  
<if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[ AND A.BUSI_ITEM_ID  = #{busi_item_id} ]]></if>

<![CDATA[  ) AA ORDER BY AA.DEAL_TIME DESC ]]>
 
</select>
<!-- 根据责任组ID查询责任组资金分配 -->
    <select id="QRY_findCapitalDistributById" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT 
  CD.DISTRI_DATE,--操作日期
  CD.DISTRI_DATE,--生效日期
  CD.DISTRI_AMOUNT,--资金分配金额
  CD.DISTRI_TYPE --资金分配类型
FROM  
DEV_PAS.T_CAPITAL_DISTRIBUTE CD
WHERE 1=1
        ]]>
<if test=" item_id != null and item_id != ''  "><![CDATA[ AND CD.ITEM_ID  = #{item_id} ]]></if>
</select>
<!-- 根据责任组ID查询责任组投连保费流信息 -->
<!--    <select id="QRY_findInvestPremFlowById" resultType="java.util.Map" -->
<!--        parameterType="java.util.Map"> -->
<!--        <![CDATA[ -->
        
<!--        ]]> -->
<!-- <if test=" item_id != null and item_id != ''  "><![CDATA[ AND CI.ITEM_ID  = #{item_id} ]]></if> -->
<!-- </select> -->
<!-- 根据责任组ID查询责任组保险金额表 -->
	<select id="QRY_findInsuranceAmountById" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT MAX(CP.ITEM_ID) ITEM_ID, MAX(CP.AMOUNT) AMOUNT, MAX(CP.BONUS_SA) BONUS_SA, MAX(SC.START_DATE) START_DATE
  FROM DEV_PAS.T_CONTRACT_PRODUCT CP
 INNER JOIN DEV_PAS.T_SA_CHANGE SC
    ON CP.ITEM_ID = SC.ITEM_ID
 WHERE 1 = 1
 		]]> 
<if test=" item_id != null and item_id != ''  "><![CDATA[ AND CP.ITEM_ID  = #{item_id} ]]></if>
</select>
<!-- 根据责任组ID查询责任组累积生息账户 -->
    <select id="QRY_findAccrueInterestAccountById" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT 
       PRL.PRODUCT_NAME,
       PA.ITEM_ID,
       PA.CAPITAL_BALANCE,--开户金额
       PA.INTEREST_SUM,--累计利息
       PA.INTEREST_CAPITAL, --期末金额
       PTL.TRANS_AMOUNT,
        PTL.TRANS_TIME,
       PTL.INTEREST_RATE
  FROM DEV_PAS.T_POLICY_ACCOUNT PA
  INNER JOIN DEV_PAS.V_POL_ACC_TRANS_LIST_ALL PTL
  ON PA.ACCOUNT_ID=PTL.ACCOUNT_ID
    INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
  ON CP.ITEM_ID =PA.ITEM_ID
  INNER JOIN DEV_PDS.T_PRODUCT_LIFE PRL
  ON CP.PRODUCT_ID =PRL.PRODUCT_ID
 WHERE 1=1    AND PA.ACCOUNT_TYPE = '11'
 AND PTL.TRANS_CODE IN ('1', '2', '3', '4', '5')
    ]]> 
<if test=" item_id != null and item_id != ''  "><![CDATA[ AND PA.ITEM_ID  = #{item_id} ]]></if>
<![CDATA[ ORDER BY PTL.INSERT_TIME DESC]]> 
</select>

<!-- 根据保单号查询续保次数  -->
<select id="QRY_findPolicyCount" resultType="Integer"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT COUNT(*)
   FROM (SELECT *
           FROM DEV_PAS.T_CONTRACT_MASTER A
          START WITH A.POLICY_CODE = #{policy_code}
         CONNECT BY PRIOR A.FORMER_ID = A.POLICY_ID)
	]]> 
</select>

<!-- 根据责任组ID查询责任组投资策略 -->
<!--    <select id="QRY_findInvestAccountInfoById" resultType="java.util.Map" -->
<!--        parameterType="java.util.Map"> -->
<!--        <![CDATA[ -->
<!--        ]]> -->
<!-- <if test=" item_id != null and item_id != ''  "><![CDATA[ AND CI.ITEM_ID  = #{item_id} ]]></if> -->
<!-- </select> -->
<!-- 根据责任组ID查询责任组费用信息 -->
	<select id="QRY_findCostInfoById" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT PFC.ITEM_ID, PFC.CHARGE_CODE, LAST_DAY(ADD_MONTHS(PFC.LAST_CHARGE_DATE,1)) AS CHARGE_DUE_DATE,BPC.PAYMENT_FREQ
  FROM DEV_PAS.T_POLICY_FUND_CHARGE PFC
  INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
  ON CP.ITEM_ID = PFC.ITEM_ID AND PFC.CHARGE_CODE<>0
  INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
  ON CBP.BUSI_ITEM_ID = CP.BUSI_ITEM_ID
  INNER JOIN DEV_PDS.T_BUSINESS_PROD_CHARGE BPC
  ON BPC.BUSINESS_PRD_ID = CBP.BUSI_PRD_ID
 WHERE 1=1    
 AND (((CBP.BUSI_PROD_CODE = '********' OR CBP.BUSI_PROD_CODE = '********') AND BPC.PAYMENT_FREQ = '6')
 OR ((CBP.BUSI_PROD_CODE != '********' OR CBP.BUSI_PROD_CODE != '********') AND BPC.PAYMENT_FREQ IN (SELECT Z.FREQ_CODE FROM DEV_PDS.T_PAYMENT_FREQ Z)))
 AND PFC.CHARGE_CODE=BPC.CHARGE_TYPE
]]> 
<if test=" item_id != null and item_id != ''  "><![CDATA[ AND PFC.ITEM_ID  = #{item_id} ]]></if>
</select>
    <!-- 39107查询保障年期变更类型  -->
	<select id="findProductCoverPeriodOld" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT CP.COVERAGE_PERIOD, /*保障年期类新码表T_COVERAGE_PERIOD*/
		       CP.COVERAGE_YEAR, /*保障年期*/
		       CP.PREM_FREQ,/*交费方式码表T_CHARGE_MODE*/
		       (SELECT CM.CHARGE_NAME
		          FROM DEV_PAS.T_CHARGE_MODE CM
		         WHERE CM.CHARGE_TYPE = CP.PREM_FREQ) AS CHARGE_NAME, 
		       CP.CHARGE_YEAR, /*交费期间*/
		       CP.AMOUNT /*保额*/,
		       CP.INITIAL_DISCNT_PREM_AF, /*首期折扣后保费*/
       		   CP.STD_PREM_AF,/*标准保费*/
		       CP.OLD_NEW AS FLAG, /*变更标识*/
		       CP.ITEM_ID,
		       CP.CHANGE_ID, /*变更ID*/
		       CP.POLICY_CHG_ID
		  FROM DEV_PAS.T_CS_CONTRACT_PRODUCT CP
		 WHERE EXISTS (SELECT CPIN.*
		          FROM DEV_PAS.T_CS_CONTRACT_PRODUCT CPIN
		         WHERE CPIN.CHANGE_ID = CP.CHANGE_ID
		           AND CPIN.OLD_NEW = 1
		           AND CPIN.COVERAGE_PERIOD != CP.COVERAGE_PERIOD
		           AND CPIN.POLICY_CODE = CP.POLICY_CODE
		           AND CPIN.POLICY_CHG_ID = CP.POLICY_CHG_ID
		           AND CPIN.ITEM_ID = CP.ITEM_ID
		           AND CPIN.BUSI_ITEM_ID = CP.BUSI_ITEM_ID)
		   AND CP.OLD_NEW = 0
		  ]]>
		<if test=" item_id != null and item_id != ''  "><![CDATA[AND CP.ITEM_ID = #{item_id} ]]></if>
	</select>
	<!-- 39017 保障年期有不同时，再查询变更后的数据 -->
	<select id="findProductCoverPeriodNew" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT CP.COVERAGE_PERIOD, /*保障年期类新码表T_COVERAGE_PERIOD*/
		       CP.COVERAGE_YEAR, /*保障年期*/
		       CP.PREM_FREQ,/*交费方式码表T_CHARGE_MODE*/
		       (SELECT CM.CHARGE_NAME
		          FROM DEV_PAS.T_CHARGE_MODE CM
		         WHERE CM.CHARGE_TYPE = CP.PREM_FREQ) AS CHARGE_NAME, 
		       CP.CHARGE_YEAR, /*交费期间*/
		       CP.AMOUNT /*保额*/,
		       CP.INITIAL_DISCNT_PREM_AF, /*首期折扣后保费*/
       		   CP.STD_PREM_AF,/*标准保费*/
		       CP.OLD_NEW AS FLAG, /*变更标识*/
		       CP.ITEM_ID,
		       CP.CHANGE_ID, /*变更ID*/
		       CP.POLICY_CHG_ID
		  FROM DEV_PAS.T_CS_CONTRACT_PRODUCT CP
		 WHERE CP.OLD_NEW = 1
		]]>
		<if test=" item_id != null and item_id != ''  "><![CDATA[AND CP.ITEM_ID = #{item_id} ]]></if>
		<if test=" change_id != null and change_id != ''  "><![CDATA[AND CP.CHANGE_ID = #{change_id} ]]></if>
		<if test=" policy_chg_id != null and policy_chg_id != ''  "><![CDATA[AND CP.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
	</select>
</mapper>
