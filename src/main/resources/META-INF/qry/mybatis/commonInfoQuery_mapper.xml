<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimCasePO">

	<!-- 按索引生成的查询条件  -->
	<sql id="queryClaimCaseByCaseIdCondition">
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
	</sql>
	<sql id="claimMemoWhereCondition">
		<if test=" memo_type != null and memo_type != ''  "><![CDATA[ AND A.MEMO_TYPE = #{memo_type} ]]></if>
		<if test=" memo_id  != null "><![CDATA[ AND A.MEMO_ID = #{memo_id} ]]></if>
		<if test=" memo_content != null and memo_content != ''  "><![CDATA[ AND A.MEMO_CONTENT = #{memo_content} ]]></if>
		<if test=" memo_option != null and memo_option != ''  "><![CDATA[ AND A.MEMO_OPTION = #{memo_option} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</sql>
	<sql id="againInsurSuggesDetaiWhereCondition">
		<if test=" sugges_id  != null "><![CDATA[ AND A.SUGGES_ID = #{sugges_id} ]]></if>
		<if test=" again_standard_code != null and again_standard_code != ''  "><![CDATA[ AND A.AGAIN_STANDARD_CODE = #{again_standard_code} ]]></if>
		<if test=" suggestion_code != null and suggestion_code != ''  "><![CDATA[ AND A.SUGGESTION_CODE = #{suggestion_code} ]]></if>
		<if test=" supply_material != null and supply_material != ''  "><![CDATA[ AND A.SUPPLY_MATERIAL = #{supply_material} ]]></if>
		<if test=" reply_times  != null "><![CDATA[ AND A.REPLY_TIMES = #{reply_times} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" sugg_reply != null and sugg_reply != ''  "><![CDATA[ AND A.SUGG_REPLY = #{sugg_reply} ]]></if>
		<if test=" sugg_compen_money  != null "><![CDATA[ AND A.SUGG_COMPEN_MONEY = #{sugg_compen_money} ]]></if>
	</sql>
	<sql id="claimCaseWhereCondition">
		<if test=" trustee_certi_code != null and trustee_certi_code != ''  "><![CDATA[ AND A.TRUSTEE_CERTI_CODE = #{trustee_certi_code} ]]></if>
		<if test=" end_case_time  != null  and  end_case_time  != ''  "><![CDATA[ AND A.END_CASE_TIME = #{end_case_time} ]]></if>
		<if test=" actual_pay  != null "><![CDATA[ AND A.ACTUAL_PAY = #{actual_pay} ]]></if>
		<if test=" report_mode  != null "><![CDATA[ AND A.REPORT_MODE = #{report_mode} ]]></if>
		<if test=" approve_reject_reason != null and approve_reject_reason != ''  "><![CDATA[ AND A.APPROVE_REJECT_REASON = #{approve_reject_reason} ]]></if>
		<if test=" related_no != null and related_no != ''  "><![CDATA[ AND A.RELATED_NO = #{related_no} ]]></if>
		<if test=" rptr_relation != null and rptr_relation != ''  "><![CDATA[ AND A.RPTR_RELATION = #{rptr_relation} ]]></if>
		<if test=" over_comp_flag  != null "><![CDATA[ AND A.OVER_COMP_FLAG = #{over_comp_flag} ]]></if>
		<if test=" other_reason != null and other_reason != ''  "><![CDATA[ AND A.OTHER_REASON = #{other_reason} ]]></if>
		<if test=" repeal_reason  != null "><![CDATA[ AND A.REPEAL_REASON = #{repeal_reason} ]]></if>
		<if test=" is_common  != null "><![CDATA[ AND A.IS_COMMON = #{is_common} ]]></if>
		<if test=" cure_hospital != null and cure_hospital != ''  "><![CDATA[ AND A.CURE_HOSPITAL = #{cure_hospital} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" review_flag  != null "><![CDATA[ AND A.REVIEW_FLAG = #{review_flag} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" advance_ask_flag  != null "><![CDATA[ AND A.ADVANCE_ASK_FLAG = #{advance_ask_flag} ]]></if>
		<if test=" case_flag  != null "><![CDATA[ AND A.CASE_FLAG = #{case_flag} ]]></if>
		<if test=" green_flag  != null "><![CDATA[ AND A.GREEN_FLAG = #{green_flag} ]]></if>
		<if test=" audit_reject_reason != null and audit_reject_reason != ''  "><![CDATA[ AND A.AUDIT_REJECT_REASON = #{audit_reject_reason} ]]></if>
		<if test=" balance_pay  != null "><![CDATA[ AND A.BALANCE_PAY = #{balance_pay} ]]></if>
		<if test=" reject_reason != null and reject_reason != ''  "><![CDATA[ AND A.REJECT_REASON = #{reject_reason} ]]></if>
		<if test=" auditor_id  != null "><![CDATA[ AND A.AUDITOR_ID = #{auditor_id} ]]></if>
		<if test=" rptr_mp != null and rptr_mp != ''  "><![CDATA[ AND A.RPTR_MP = #{rptr_mp} ]]></if>
		<if test=" serious_disease != null and serious_disease != ''  "><![CDATA[ AND A.SERIOUS_DISEASE = #{serious_disease} ]]></if>
		<if test=" registe_time  != null  and  registe_time  != ''  "><![CDATA[ AND A.REGISTE_TIME = #{registe_time} ]]></if>
		<if test=" case_apply_type  != null "><![CDATA[ AND A.CASE_APPLY_TYPE = #{case_apply_type} ]]></if>
		<if test=" repeal_desc != null and repeal_desc != ''  "><![CDATA[ AND A.REPEAL_DESC = #{repeal_desc} ]]></if>
		<if test=" rptr_email != null and rptr_email != ''  "><![CDATA[ AND A.RPTR_EMAIL = #{rptr_email} ]]></if>
		<if test=" trustee_certi_type != null and trustee_certi_type != ''  "><![CDATA[ AND A.TRUSTEE_CERTI_TYPE = #{trustee_certi_type} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" accident_detail != null and accident_detail != ''  "><![CDATA[ AND A.ACCIDENT_DETAIL = #{accident_detail} ]]></if>
		<if test=" case_status != null and case_status != ''  "><![CDATA[ AND A.CASE_STATUS = #{case_status} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" rptr_time  != null  and  rptr_time  != ''  "><![CDATA[ AND A.RPTR_TIME = #{rptr_time} ]]></if>
		<if test=" audit_decision  != null "><![CDATA[ AND A.AUDIT_DECISION = #{audit_decision} ]]></if>
		<if test=" register_id  != null "><![CDATA[ AND A.REGISTER_ID = #{register_id} ]]></if>
		<if test=" claim_source  != null "><![CDATA[ AND A.CLAIM_SOURCE = #{claim_source} ]]></if>
		<if test=" trustee_type  != null "><![CDATA[ AND A.TRUSTEE_TYPE = #{trustee_type} ]]></if>
		<if test=" approver_id  != null "><![CDATA[ AND A.APPROVER_ID = #{approver_id} ]]></if>
		<if test=" accident_id  != null "><![CDATA[ AND A.ACCIDENT_ID = #{accident_id} ]]></if>
		<if test=" reject_pay  != null "><![CDATA[ AND A.REJECT_PAY = #{reject_pay} ]]></if>
		<if test=" sign_time  != null  and  sign_time  != ''  "><![CDATA[ AND A.SIGN_TIME = #{sign_time} ]]></if>
		<if test=" signer_id  != null "><![CDATA[ AND A.SIGNER_ID = #{signer_id} ]]></if>
		<if test=" advance_pay  != null "><![CDATA[ AND A.ADVANCE_PAY = #{advance_pay} ]]></if>
		<if test=" rptr_name != null and rptr_name != ''  "><![CDATA[ AND A.RPTR_NAME = #{rptr_name} ]]></if>
		<if test=" rptr_addr != null and rptr_addr != ''  "><![CDATA[ AND A.RPTR_ADDR = #{rptr_addr} ]]></if>
		<if test=" rptr_id  != null "><![CDATA[ AND A.RPTR_ID = #{rptr_id} ]]></if>
		<if test=" audit_permission_name != null and audit_permission_name != ''  "><![CDATA[ AND A.AUDIT_PERMISSION_NAME = #{audit_permission_name} ]]></if>
		<if test=" approve_remark != null and approve_remark != ''  "><![CDATA[ AND A.APPROVE_REMARK = #{approve_remark} ]]></if>
		<if test=" trustee_name != null and trustee_name != ''  "><![CDATA[ AND A.TRUSTEE_NAME = #{trustee_name} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
		<if test=" registe_conf_time  != null  and  registe_conf_time  != ''  "><![CDATA[ AND A.REGISTE_CONF_TIME = #{registe_conf_time} ]]></if>
		<if test=" trustee_tel != null and trustee_tel != ''  "><![CDATA[ AND A.TRUSTEE_TEL = #{trustee_tel} ]]></if>
		<if test=" rptr_zip  != null "><![CDATA[ AND A.RPTR_ZIP = #{rptr_zip} ]]></if>
		<if test=" is_bpo  != null "><![CDATA[ AND A.IS_BPO = #{is_bpo} ]]></if>
		<if test=" audit_time  != null  and  audit_time  != ''  "><![CDATA[ AND A.AUDIT_TIME = #{audit_time} ]]></if>
		<if test=" approve_time  != null  and  approve_time  != ''  "><![CDATA[ AND A.APPROVE_TIME = #{approve_time} ]]></if>
		<if test=" audit_remark != null and audit_remark != ''  "><![CDATA[ AND A.AUDIT_REMARK = #{audit_remark} ]]></if>
		<if
			test=" approve_permission_name != null and approve_permission_name != ''  "><![CDATA[ AND A.APPROVE_PERMISSION_NAME = #{approve_permission_name} ]]></if>
		<if test=" approve_decision  != null "><![CDATA[ AND A.APPROVE_DECISION = #{approve_decision} ]]></if>
		<if test=" trustee_code != null and trustee_code != ''  "><![CDATA[ AND A.TRUSTEE_CODE = #{trustee_code} ]]></if>
		<if test=" door_sign_time  != null  and  door_sign_time  != ''  "><![CDATA[ AND A.DOOR_SIGN_TIME = #{door_sign_time} ]]></if>
		<if test=" comfort_flag  != null "><![CDATA[ AND A.COMFORT_FLAG = #{comfort_flag} ]]></if>
		<if test=" accept_time  != null  and  accept_time  != ''  "><![CDATA[ AND A.ACCEPT_TIME = #{accept_time} ]]></if>
		<if test=" is_deduct_flag  != null "><![CDATA[ AND A.IS_DEDUCT_FLAG = #{is_deduct_flag} ]]></if>
		<if test=" med_dept != null and med_dept != ''  "><![CDATA[ AND A.MED_DEPT = #{med_dept} ]]></if>
		<if test=" doctor_name != null and doctor_name != ''  "><![CDATA[ AND A.DOCTOR_NAME = #{doctor_name} ]]></if>
		<if test=" trustee_mp != null and trustee_mp != ''  "><![CDATA[ AND A.TRUSTEE_MP = #{trustee_mp} ]]></if>
		<if test=" cure_status != null and cure_status != ''  "><![CDATA[ AND A.CURE_STATUS = #{cure_status} ]]></if>
		<if test=" accept_decision  != null "><![CDATA[ AND A.ACCEPT_DECISION = #{accept_decision} ]]></if>
		<if test=" acceptor_id  != null "><![CDATA[ AND A.ACCEPTOR_ID = #{acceptor_id} ]]></if>
		<if test=" case_sub_status != null and case_sub_status != ''  "><![CDATA[ AND A.CASE_SUB_STATUS = #{case_sub_status} ]]></if>
		<if test=" calc_pay  != null "><![CDATA[ AND A.CALC_PAY = #{calc_pay} ]]></if>
	</sql>
	<sql id="claimCaseWhereConditionForPage">
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no, jdbcType=VARCHAR} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ]]></if>
		<if test=" case_status != null and case_status != ''  "><![CDATA[ AND A.CASE_STATUS = #{case_status, jdbcType=VARCHAR} ]]></if>
	</sql>
	<sql id="claimPayWhereCondition">
		<if test=" prem_arap_id  != null "><![CDATA[ AND A.PREM_ARAP_ID = #{prem_arap_id} ]]></if>
		<if test=" adjust_busi_id  != null "><![CDATA[ AND A.ADJUST_BUSI_ID = #{adjust_busi_id} ]]></if>
		<if test=" payee_id  != null "><![CDATA[ AND A.PAYEE_ID = #{payee_id} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
		<if test=" pay_mole  != null "><![CDATA[ AND A.PAY_MOLE = #{pay_mole} ]]></if>
		<if test=" is_instalment  != null "><![CDATA[ AND A.IS_INSTALMENT = #{is_instalment} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" bene_id  != null "><![CDATA[ AND A.BENE_ID = #{bene_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" claim_pay_id  != null "><![CDATA[ AND A.CLAIM_PAY_ID = #{claim_pay_id} ]]></if>
		<if test=" bene_rate  != null "><![CDATA[ AND A.BENE_RATE = #{bene_rate} ]]></if>
		<if test=" arap_id  != null "><![CDATA[ AND A.ARAP_ID = #{arap_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" pay_deno  != null "><![CDATA[ AND A.PAY_DENO = #{pay_deno} ]]></if>
		<if test=" pay_amount  != null "><![CDATA[ AND A.PAY_AMOUNT = #{pay_amount} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="queryCustomerByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>	
	<!-- 按索引生成的查询条件 -->	
	<sql id="queryPremArapByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<!-- <select id="findClaimCaseByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[ SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
		A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
		A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
		A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
		A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
		A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
		A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
		A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
		A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME, 
		A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, 
		A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.TRUSTEE_MP, A.CURE_STATUS, A.ACCEPT_DECISION, 
		A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY,A.SURVEY_RESULT_INFO, A.IS_AUTO_HUNGUP, A.SPECIAL_REMARK_CODE, A.LOSS_REASON_CODE, A.LOSS_LEVEL_CODE   FROM DEV_CLM.T_CLAIM_CASE                       A WHERE 1 = 1  ]]>
		<include refid="queryClaimCaseByCaseIdCondition" />
	<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select> -->
	<select id="findClaimCaseByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID,A.ACCIDENT_ID,A.CASE_NO,A.INSURED_ID,A.RPTR_RELATION,A.RPTR_NAME,A.RPTR_MP,A.RPTR_ZIP,
             A.RPTR_ADDR,A.RPTR_EMAIL,A.REPORT_MODE,A.ORGAN_CODE,A.RPTR_TIME,A.CASE_APPLY_TYPE,A.APPLY_DATE,A.ACCEPTOR_ID,
             A.ACCEPT_TIME,A.TRUSTEE_TYPE,A.TRUSTEE_CODE,A.TRUSTEE_NAME,A.TRUSTEE_MP,A.TRUSTEE_TEL,A.TRUSTEE_CERTI_TYPE,
             A.TRUSTEE_CERTI_CODE,A.DOOR_SIGN_TIME,A.SIGN_TIME,A.RPTR_ID,A.SIGNER_ID,A.SERIOUS_DISEASE,ACCIDENT_DETAIL,
             A.CURE_HOSPITAL,A.CURE_STATUS,A.DOCTOR_NAME,A.MED_DEPT,A.CASE_SUB_STATUS,A.CASE_STATUS,A.GREEN_FLAG,A.IS_BPO,
             A.CASE_FLAG,A.REVIEW_FLAG,A.COMFORT_FLAG,A.ADVANCE_ASK_FLAG,A.ADVANCE_FLAG,A.IS_DEDUCT_FLAG,A.REPEAL_REASON,
             A.REPEAL_DESC,A.REGISTER_ID,A.REGISTE_TIME,A.REGISTE_CONF_TIME,A.ACCEPT_DECISION,A.REJECT_REASON,A.CALC_PAY,
             ADVANCE_PAY,A.BALANCE_PAY,A.ACTUAL_PAY,A.REJECT_PAY,A.AUDIT_TIME,A.AUDITOR_ID,A.AUDIT_DECISION,A.AUDIT_REMARK,
             A.AUDIT_REJECT_REASON,A.OTHER_REASON,A.APPROVER_ID,A.APPROVE_TIME,A.APPROVE_DECISION,A.APPROVE_REJECT_REASON,
             A.APPROVE_REMARK,A.END_CASE_TIME,A.OVER_COMP_FLAG,A.RELATED_NO,A.CLAIM_SOURCE,A.IS_COMMON,A.INSERT_BY,
             A.INSERT_TIME,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP,A.AUDIT_PERMISSION_NAME,
             A.APPROVE_PERMISSION_NAME,A.SURVEY_RESULT_INFO,A.CHANNEL_CODE,A.IS_AUTO_HUNGUP,A.SPECIAL_REMARK_CODE,
             A.LOSS_REASON_CODE,A.LOSS_LEVEL_CODE,A.COMFORT_STATUS,A.IS_MIGRATION,A.SEND_BENE_DOC_FLAG,A.SIGN_ORGAN,
             A.REGISTE_ORGAN,A.AUDIT_ORGAN,A.APPROVE_ORGAN,A.REMARK,A.EASY_AUDITOR_ID ,A.IS_OVER_COMP,A.OVER_REASON,A.OVER_DAYS,A.OVER_DUE_MONEY,
             A.SHARE_CONDITION_VALID,A.SHARE_CONDITION_DECISION FROM DEV_CLM.T_CLAIM_CASE A WHERE 1 = 1  ]]>
		<if test=" case_id != null and case_id != ''  "><![CDATA[ AND A.case_id = #{case_id} ]]></if>   
		<if test=" case_id == null or case_id == ''  ">
			<if test=" case_no == null or case_no == ''  ">
				<![CDATA[ AND A.case_id = '' ]]>
			</if>
		</if> 
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select>

	<!-- and by caoyy_wb 取缴费终止日期的最大值 -->
	<select id="findContractBusiProdMax" resultType="java.util.Map"
		parameterType="java.util.Map">
	<![CDATA[SELECT MAX(PAIDUP_DATE) PAIDUP_DATE  FROM DEV_CLM.T_CONTRACT_BUSI_PROD WHERE POLICY_CODE=#{policy_code}]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="findAllClaimMemo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.MEMO_TYPE, A.MEMO_ID, A.MEMO_CONTENT, 
			A.MEMO_OPTION, A.CASE_ID, A.INSERT_BY, A.UPDATE_TIME,A.OUTSOURCE_OPERATOR, A.IT_PROBLEM_NO , A.IT_PROBLEM_BACK_FLAG FROM DEV_CLM.T_CLAIM_MEMO A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimMemoWhereCondition" />
		<![CDATA[ ORDER BY A.MEMO_ID ]]>
	</select>

	<select id="findAllClaimCase" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.TRUSTEE_CERTI_CODE, A.END_CASE_TIME, A.ACTUAL_PAY, A.REPORT_MODE, A.APPROVE_REJECT_REASON, A.RELATED_NO, A.RPTR_RELATION, 
			A.OVER_COMP_FLAG, A.OTHER_REASON, A.REPEAL_REASON, A.IS_COMMON, A.CURE_HOSPITAL, A.CASE_NO, A.REVIEW_FLAG, 
			A.ORGAN_CODE, A.ADVANCE_ASK_FLAG, A.CASE_FLAG, A.GREEN_FLAG, A.AUDIT_REJECT_REASON, A.BALANCE_PAY, 
			A.REJECT_REASON, A.AUDITOR_ID, A.RPTR_MP, A.SERIOUS_DISEASE, A.REGISTE_TIME, A.CASE_APPLY_TYPE, 
			A.REPEAL_DESC, A.RPTR_EMAIL, A.TRUSTEE_CERTI_TYPE, A.INSURED_ID, A.ACCIDENT_DETAIL, A.CASE_STATUS, A.CASE_ID, 
			A.RPTR_TIME, A.AUDIT_DECISION, A.REGISTER_ID, A.CLAIM_SOURCE, A.TRUSTEE_TYPE, A.APPROVER_ID, 
			A.ACCIDENT_ID, A.REJECT_PAY, A.SIGN_TIME, A.SIGNER_ID, A.ADVANCE_PAY, A.RPTR_NAME, 
			A.RPTR_ADDR, A.RPTR_ID, A.AUDIT_PERMISSION_NAME, A.APPROVE_REMARK, A.TRUSTEE_NAME, A.APPLY_DATE, A.ADVANCE_FLAG, 
			A.REGISTE_CONF_TIME, A.TRUSTEE_TEL, A.RPTR_ZIP, A.IS_BPO, A.AUDIT_TIME, A.APPROVE_TIME, 
			A.AUDIT_REMARK, A.APPROVE_PERMISSION_NAME, A.APPROVE_DECISION, A.TRUSTEE_CODE, A.DOOR_SIGN_TIME, A.COMFORT_FLAG, 
			A.ACCEPT_TIME, A.IS_DEDUCT_FLAG, A.MED_DEPT, A.DOCTOR_NAME, A.TRUSTEE_MP, A.CURE_STATUS, A.ACCEPT_DECISION, 
			A.ACCEPTOR_ID, A.CASE_SUB_STATUS, A.CALC_PAY,A.IS_MIGRATION FROM DEV_CLM.T_CLAIM_CASE                       A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimCaseWhereCondition" />
		<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select>
	
	<select id="findAllAgainInsurSuggesDetai" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUGGES_ID,
				       A.STANDARD_CODE,
				       A.SUGGESTION_CODE,
				       A.SUPPLY_MATERIAL,
				       A.REPLY_TIME,
				       A.CASE_NO,
				       A.CASE_ID,
				       A.SUGG_REPLY,
				       A.SUGG_COMPEN_MONEY
				  FROM DEV_CLM.T_RI_SUGGEST_DETAIL A
				 WHERE ROWNUM <= 1000  ]]>
		 <include refid="againInsurSuggesDetaiWhereCondition" />
		<![CDATA[ ORDER BY A.SUGGES_ID ]]> 
	</select>
	
	<!-- add by xuyz_wb 分支流程 预付信息查询 -->
	<select id="findClaimLiabPayMsgByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT distinct A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID,  ( SELECT T.TXT_CONTENT FROM DEV_CLM.T_CLOB T WHERE T.CLOB_ID=A.CLOB_ID)CLM_REMARK, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.RESIST_FLAG, A.WAIVE_START, 
			A.ADJUST_PAY, A.LIAB_ID, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, 
			A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, A.WAIVE_END, A.CALC_PAY, 
			A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE,B.CASE_NO FROM DEV_CLM.T_CLAIM_LIAB A,DEV_CLM.T_CLAIM_CASE B 
			WHERE 1=1 AND A.CASE_ID = B.CASE_ID AND B.ADVANCE_FLAG='1' ]]>
		<if test="case_id !=null and case_id != ''"> 
	       	<![CDATA[ AND A.CASE_ID = #{case_id} ]]>
		</if>
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]>
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findAllClaimPay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.PAYEE_ID, A.REMARK, A.ADVANCE_FLAG, 
			A.PAY_MOLE, A.IS_INSTALMENT, A.CASE_ID, A.BUSI_PROD_CODE, A.BENE_ID, A.POLICY_CODE, 
			A.CLAIM_PAY_ID, A.BENE_RATE, A.BUSI_ITEM_ID, 
			A.PAY_DENO, A.PAY_AMOUNT, A.POLICY_ID FROM DEV_CLM.T_CLAIM_PAY A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimPayWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]> 
	</select>
	
	<select id="findPremArapByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ FROM DEV_CLM.T_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryPremArapByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<select id="selectBeneAndPayeeMsgs" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CASE_ID case_id, 
		                 A.POLICY_ID policy_id,
		                 A.BUSI_ITEM_ID busi_item_id, 
		                 A.PAY_AMOUNT pay_amount, 
		                 A.BENE_RATE bene_rate,
		                 B.BENE_NAME bene_name,
		                 B.BENE_RELATION bene_relation,
		                 B.BENE_BIRTH bene_birth,
		                 C.PAYEE_NAME payee_name,
		                 C.ACCOUNT_NO account_no,
		                 B.BENE_ID bene_id,
		                 D.name pay_mode
		                 FROM DEV_CLM.T_CLAIM_PAY  A 
		                 left join DEV_CLM.T_CLAIM_BENE B on A.BENE_ID=B.BENE_ID
		                 left join DEV_CLM.T_CLAIM_PAYEE C on A.PAYEE_ID=C.PAYEE_ID
		                 left join DEV_CLM.T_PAY_MODE D on C.PAY_MODE=D.CODE
		                 WHERE 1=1  AND A.ADVANCE_FLAG ='1'
        ]]>
		<if test="case_id !=null and case_id !=''"> 
        <![CDATA[    AND  a.case_id = #{case_id} ]]>
		</if>
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID desc]]>
	</select>
	
	<select id="findCustomerByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ,
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_CLM.T_CUSTOMER A WHERE 1 = 1  ]]>
		<include refid="queryCustomerByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.CUSTOMER_ID ]]>
	</select>
	
	<sql id="claimMidcTaskWhereCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" check_conclusion  != null "><![CDATA[ AND A.CHECK_CONCLUSION = #{check_conclusion} ]]></if>
		<if test=" check_date  != null  and  check_date  != ''  "><![CDATA[ AND A.CHECK_DATE = #{check_date} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" check_by  != null "><![CDATA[ AND A.CHECK_BY = #{check_by} ]]></if>
		<if test=" make_date  != null  and  make_date  != ''  "><![CDATA[ AND A.MAKE_DATE = #{make_date} ]]></if>
		<if test=" qc_status  != null "><![CDATA[ AND A.QC_STATUS = #{qc_status} ]]></if>
		<if test=" check_reason != null and check_reason != ''  "><![CDATA[ AND A.CHECK_REASON = #{check_reason} ]]></if>
	</sql>
	
	<select id="findAllClaimMidcTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.REMARK, A.CHECK_CONCLUSION, A.CHECK_DATE, A.TASK_ID, 
			A.CASE_NO, A.CASE_ID, A.ORGAN_CODE, A.CHECK_BY, A.MAKE_DATE, 
			A.QC_STATUS, A.CHECK_REASON FROM DEV_CLM.T_CLAIM_MIDC_TASK                       A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimMidcTaskWhereCondition" /> 
		<![CDATA[ ORDER BY A.TASK_ID ]]> 
	</select>
	
	<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimAccidentByAccidentIdCondition">
		<if test=" accident_id  != null "><![CDATA[ AND A.ACCIDENT_ID = #{accident_id} ]]></if>
	</sql>	
	<!-- 按索引查询操作 -->	
	<select id="findClaimAccidentByAccidentId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACC_REASON, A.ACC_PROVINCE, A.ACC_STREET, A.ACCIDENT_NO, A.INSURED_ID, 
			A.ACC_CITY, A.ACC_DISTREACT, A.ACC_DESC, A.ACC_DATE, 
			A.ACCIDENT_ID FROM DEV_CLM.T_CLAIM_ACCIDENT A WHERE 1 = 1  ]]>
		<include refid="queryClaimAccidentByAccidentIdCondition" />
		<![CDATA[ ORDER BY A.ACCIDENT_ID ]]>
	</select>
	<!-- zhangjy 查询理赔给付责任理算信息 -->
    <select id="findClaimLiaByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT * FROM DEV_CLM.T_CLAIM_LIAB A WHERE A.CASE_ID = #{case_id} ]]>
    </select>
	<sql id="queryCustomerWhere">
		<if test=" customer_name  != null "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
		<if test=" customer_cert_type  != null "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_certi_code  != null "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" customer_birthday  != null "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" un_customer_code  != null "><![CDATA[ AND A.UN_CUSTOMER_CODE = #{un_customer_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql> 
	<!-- 根据客户号查询客户信息  add by gaojh_wb -->
	<select id="findCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_ID,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_CLM.T_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
		<include refid="queryCustomerWhere" />
		<![CDATA[ ORDER BY A.CUSTOMER_ID ]]>
	</select>
	
	
<!-- 按索引生成的查询条件 -->	
	<sql id="claimQueryUserByUserIdCondition">
		<if test=" user_id  != null "><![CDATA[ AND A.USER_ID = #{user_id} ]]></if>
		<if test=" user_name != null and user_name != ''  "><![CDATA[ AND A.USER_NAME = #{user_name} ]]></if>
	</sql>
 <select id="claimFindUserByUserId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEPT_ID, A.UNIT_NAME, A.REAL_NAME, A.ID_TYPE, A.NEED_CHANGE_PASS, A.PHONE, A.ORGAN_CODE, 
			A.SHORT_NUM, A.ID_CARD, A.EMAIL, A.PASSWORD, A.ORGAN_ID, 
			A.USER_ID, A.DISABLE_DATE, A.USER_DISABLE, A.USER_TYPE, A.UNIQUEID, 
			A.PASSWORD_CHANGE, A.CLIENT_IP, A.LATEST_LOGIN_TIME, A.PARTY_ROLE, A.USER_NAME, A.INVALID_LOGIN, 
			A.CREATE_DATE, A.CHANGE_PWD_CAUSE, A.NAVIGATION_ID, A.UNIT_CODE FROM DEV_PAS.T_UDMP_USER A WHERE 1 = 1  ]]>
		<include refid="claimQueryUserByUserIdCondition" />
		<![CDATA[ ORDER BY A.USER_ID ]]>
	</select> 
	
	<!-- 反馈质检结果 查询审批案件权限 -->
	<select id="queryPermissionTypeList" parameterType="java.util.Map"
		resultType="java.util.Map">
    <![CDATA[ 
		  select A.PERMISSION_TYPE_ID,A.PERMISSION_TYPE_NAME
          from T_UDMP_PERMISSION_TYPE A
          where A.permission_type_id in
          (select PERMISSION_TYPE_ID 
             from T_UDMP_ROLE_PERMISSION B
            where B.role_id in (select role_id
                             from T_UDMP_GROUP_ROLE C
                            where C.ROLE_GROUP_ID in
                                  (select ROLE_GROUP_ID
                                     from DEV_CLM.T_UDMP_GROUP_USER D
                                    where 1=1
		]]>
		<if test="user_id !=null and user_id !=''"> 
	        	<![CDATA[  and D.user_id = #{user_id} ]]>
		</if>
		 <![CDATA[ ))) ]]>
	</select>
	
	<sql id="claimAfcTaskWhereCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" check_conclusion  != null "><![CDATA[ AND A.CHECK_CONCLUSION = #{check_conclusion} ]]></if>
		<if test=" check_date  != null  and  check_date  != ''  "><![CDATA[ AND A.CHECK_DATE = #{check_date} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" check_by  != null "><![CDATA[ AND A.CHECK_BY = #{check_by} ]]></if>
		<if test=" make_date  != null  and  make_date  != ''  "><![CDATA[ AND A.MAKE_DATE = #{make_date} ]]></if>
		<if test=" qc_status  != null "><![CDATA[ AND A.QC_STATUS = #{qc_status} ]]></if>
		<if test=" check_reason != null and check_reason != ''  "><![CDATA[ AND A.CHECK_REASON = #{check_reason} ]]></if>
	</sql>
	<!-- 查询所有操作 -->
	<select id="findAllClaimAfcTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.REMARK, A.CHECK_CONCLUSION, A.CHECK_DATE, A.TASK_ID, 
			A.CASE_NO, A.CASE_ID, A.ORGAN_CODE, A.CHECK_BY, A.MAKE_DATE, 
			A.QC_STATUS, A.CHECK_REASON FROM DEV_CLM.T_CLAIM_AFC_TASK A WHERE 1 = 1  ]]>
		 <include refid="claimAfcTaskWhereCondition" />
		<![CDATA[ ORDER BY A.TASK_ID ]]> 
	</select>
	
	<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimAfcPlanByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	
	<!-- 按索引查询操作 -->	
	<select id="findClaimAfcPlanByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALID_FLAG, A.PLAN_ID, A.REMARK, A.CUSTOMER_ID, A.PLAN_TYPE, 
			A.EXTRACT_LEVEL, A.PLAN_NAME, A.CERTI_CODE, A.MAKE_BY, A.VALID_DATE, 
			A.MAKE_DATE, A.CERTI_TYPE FROM DEV_CLM.T_CLAIM_AFC_PLAN A WHERE 1 = 1  ]]>
		<include refid="queryClaimAfcPlanByPlanIdCondition" />
		<![CDATA[ ORDER BY A.PLAN_ID ]]>
	</select>
	
	<!-- 按任务ID和质检要点ID查询 -->	
    <sql id="claimAfcDetailZYQCondition">
		<if test=" gist_id  != null "><![CDATA[ AND A.GIST_ID = #{gist_id} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
	</sql>
	<select id="findClaimAfcDetailByTGId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHECK_RESULT, A.REMARK, A.LIST_ID, A.GIST_ID, 
			A.TASK_ID FROM DEV_CLM.T_CLAIM_AFC_DETAIL A WHERE 1 = 1  ]]>
		<include refid="claimAfcDetailZYQCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
	<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimAfcGistByGistIdCondition">
		<if test=" gist_id  != null "><![CDATA[ AND A.GIST_ID = #{gist_id} ]]></if>
	</sql>	
	<!-- 按索引查询操作 -->	
	<select id="findClaimAfcGistByGistId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VALID_FLAG, A.GIST_DESC, A.GIST_ID, 
			A.ITEM_ID FROM DEV_CLM.T_CLAIM_AFC_GIST A WHERE 1 = 1  ]]>
		<include refid="queryClaimAfcGistByGistIdCondition" />
		<![CDATA[ ORDER BY A.GIST_ID ]]>
	</select>
	
	<!-- 根据质检计划ID查询去重的质检要点ID  add by zhaoyq_wb -->
    <sql id="findAllGistIdByPlanIdCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</sql>	
	<select id="findAllGistIdByPlanId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT(A.GIST_ID), A.PLAN_ID FROM DEV_CLM.T_CLAIM_AFC_PLAN_RELA A WHERE ROWNUM <=  1000  ]]>
		 <include refid="findAllGistIdByPlanIdCondition" />
		<![CDATA[ ORDER BY A.GIST_ID ]]> 
	</select>
	<!--根据caseId 查询审核结论 xuyz_wb add -->
	<select id="queryClaimAuditApproveByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM DEV_CLM.T_CLAIM_AUDIT_APPROVE A WHERE 1 = 1  ]]>
		<if test="case_id !=null and case_id !=''"> 
        <![CDATA[  AND A.case_id = #{case_id} ]]>
        </if>
        <if test="list_id !=null and list_id !=''"> 
        <![CDATA[  AND A.list_id = #{list_id} ]]>
        </if>
        <![CDATA[ ORDER BY A.INSERT_TIME desc ]]>
	</select>
	<select id="queryMedList" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[select distinct li.claim_liab_id,
                   li.policy_code,
                   li.busi_prod_code,
                   li.item_id,
                   li.liab_id,
                   li.liab_name,
                   bi.sum_amount,
                   bi.treat_start,
                   bi.treat_end,
                   li.ADVANCE_PAY,
                   li.ACTUAL_PAY,
                    ( SELECT T.TXT_CONTENT FROM DEV_CLM.T_CLOB T WHERE T.CLOB_ID=LI.CLOB_ID)CLM_REMARK,
                   li.liability_status liability_state
              from DEV_CLM.t_claim_liab li, DEV_CLM.t_claim_bill bi,DEV_CLM.t_contract_product pro
             where bi.case_id = li.case_id
               and pro.item_id = li.item_id
               and li.case_id = #{case_id}
               and li.claim_type = '08']]>
	</select>
	<sql id="claimLiabWhereCondition">
		<if test=" advance_pay  != null "><![CDATA[ AND A.ADVANCE_PAY = #{advance_pay} ]]></if>
		<if test=" claim_type != null and claim_type != ''  "><![CDATA[ AND A.CLAIM_TYPE = #{claim_type} ]]></if>
		<if test=" actual_pay  != null "><![CDATA[ AND A.ACTUAL_PAY = #{actual_pay} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" clm_remark != null and clm_remark != ''  "><![CDATA[ AND  ( SELECT T.TXT_CONTENT FROM DEV_CLM.T_CLOB T WHERE T.CLOB_ID=LI.CLOB_ID)= #{clm_remark} ]]></if>
		<if test=" claim_liab_id  != null "><![CDATA[ AND A.CLAIM_LIAB_ID = #{claim_liab_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" is_common  != null "><![CDATA[ AND A.IS_COMMON = #{is_common} ]]></if>
		<if test=" liab_start_date  != null  and  liab_start_date  != ''  "><![CDATA[ AND A.LIAB_START_DATE = #{liab_start_date} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" sub_case_id  != null "><![CDATA[ AND A.SUB_CASE_ID = #{sub_case_id} ]]></if>
		<if test=" amount_basic_pay  != null "><![CDATA[ AND A.AMOUNT_BASIC_PAY = #{amount_basic_pay} ]]></if>
		<if test=" waive_item  != null "><![CDATA[ AND A.WAIVE_ITEM = #{waive_item} ]]></if>
		<if test=" waive_reason != null and waive_reason != ''  "><![CDATA[ AND A.WAIVE_REASON = #{waive_reason} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" liab_adjust_reason != null and liab_adjust_reason != ''  "><![CDATA[ AND A.LIAB_ADJUST_REASON = #{liab_adjust_reason} ]]></if>
		<if test=" makeup_basic_pay  != null "><![CDATA[ AND A.MAKEUP_BASIC_PAY = #{makeup_basic_pay} ]]></if>
		<if test=" resist_flag  != null "><![CDATA[ AND A.RESIST_FLAG = #{resist_flag} ]]></if>
		<if test=" liability_status  != null "><![CDATA[ AND A.LIABILITY_STATUS = #{liability_status} ]]></if>
		<if test=" waive_start  != null  and  waive_start  != ''  "><![CDATA[ AND A.WAIVE_START = #{waive_start} ]]></if>
		<if test=" adjust_pay  != null "><![CDATA[ AND A.ADJUST_PAY = #{adjust_pay} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" bonus_basic_pay  != null "><![CDATA[ AND A.BONUS_BASIC_PAY = #{bonus_basic_pay} ]]></if>
		<if test=" liab_name != null and liab_name != ''  "><![CDATA[ AND A.LIAB_NAME = #{liab_name} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" adjust_remark != null and adjust_remark != ''  "><![CDATA[ AND A.ADJUST_REMARK = #{adjust_remark} ]]></if>
		<if test=" liab_conclusion  != null "><![CDATA[ AND A.LIAB_CONCLUSION = #{liab_conclusion} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" waive_amt  != null "><![CDATA[ AND A.WAIVE_AMT = #{waive_amt} ]]></if>
		<if test=" waive_end  != null  and  waive_end  != ''  "><![CDATA[ AND A.WAIVE_END = #{waive_end} ]]></if>
		<if test=" calc_pay  != null "><![CDATA[ AND A.CALC_PAY = #{calc_pay} ]]></if>
		<if test=" basic_pay  != null "><![CDATA[ AND A.BASIC_PAY = #{basic_pay} ]]></if>
		<if test=" liab_end_date  != null  and  liab_end_date  != ''  "><![CDATA[ AND A.LIAB_END_DATE = #{liab_end_date} ]]></if>
		<if test=" advance_date  != null  and  advance_date  != ''  "><![CDATA[ AND A.ADVANCE_DATE = #{advance_date} ]]></if>
		<if test=" clm_after_state  != null  and  clm_after_state  != ''  "><![CDATA[ AND A.CLM_AFTER_STATE = #{clm_after_state} ]]></if>
	</sql>
	 <select id="findClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID, ( SELECT T.TXT_CONTENT FROM DEV_CLM.T_CLOB T WHERE T.CLOB_ID=A.CLOB_ID)CLM_REMARK, A.CLAIM_LIAB_ID, A.ITEM_ID, 
			A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
			A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
			A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
			A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
			A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY FROM DEV_CLM.T_CLAIM_LIAB A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimLiabWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
	</select>
	<sql id="businessProductWhereCondition">
		<if test=" primary_sales_channel != null and primary_sales_channel != ''  "><![CDATA[ AND A.PRIMARY_SALES_CHANNEL = #{primary_sales_channel} ]]></if>
		<if test=" single_joint_life != null and single_joint_life != ''  "><![CDATA[ AND A.SINGLE_JOINT_LIFE = #{single_joint_life} ]]></if>
		<if test=" cover_period_type  != null "><![CDATA[ AND A.COVER_PERIOD_TYPE = #{cover_period_type} ]]></if>
		<if test=" product_category4 != null and product_category4 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY4 = #{product_category4} ]]></if>
		<if test=" business_prd_id  != null "><![CDATA[ AND A.BUSINESS_PRD_ID = #{business_prd_id} ]]></if>
		<if test=" product_category3 != null and product_category3 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY3 = #{product_category3} ]]></if>
		<if test=" product_code_original != null and product_code_original != ''  "><![CDATA[ AND A.PRODUCT_CODE_ORIGINAL = #{product_code_original} ]]></if>
		<if test=" product_category2 != null and product_category2 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY2 = #{product_category2} ]]></if>
		<if test=" product_category1 != null and product_category1 != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY1 = #{product_category1} ]]></if>
		<if test=" product_name_sys != null and product_name_sys != ''  "><![CDATA[ AND A.PRODUCT_NAME_SYS = #{product_name_sys} ]]></if>
		<if test=" product_abbr_name != null and product_abbr_name != ''  "><![CDATA[ AND A.PRODUCT_ABBR_NAME = #{product_abbr_name} ]]></if>
		<if test=" product_static_code != null and product_static_code != ''  "><![CDATA[ AND A.PRODUCT_STATIC_CODE = #{product_static_code} ]]></if>
		<if test=" insured_count_max  != null "><![CDATA[ AND A.INSURED_COUNT_MAX = #{insured_count_max} ]]></if>
		<if test=" insured_count_min  != null "><![CDATA[ AND A.INSURED_COUNT_MIN = #{insured_count_min} ]]></if>
		<if test=" product_desc != null and product_desc != ''  "><![CDATA[ AND A.PRODUCT_DESC = #{product_desc} ]]></if>
		<if test=" release_date  != null  and  release_date  != ''  "><![CDATA[ AND A.RELEASE_DATE = #{release_date} ]]></if>
		<if test=" premium_rate_layer != null and premium_rate_layer != ''  "><![CDATA[ AND A.PREMIUM_RATE_LAYER = #{premium_rate_layer} ]]></if>
		<if test=" premium_currency != null and premium_currency != ''  "><![CDATA[ AND A.PREMIUM_CURRENCY = #{premium_currency} ]]></if>
		<if test=" product_name_std != null and product_name_std != ''  "><![CDATA[ AND A.PRODUCT_NAME_STD = #{product_name_std} ]]></if>
		<if test=" product_code_std != null and product_code_std != ''  "><![CDATA[ AND A.PRODUCT_CODE_STD = #{product_code_std} ]]></if>
		<if test=" renew_option != null and renew_option != ''  "><![CDATA[ AND A.RENEW_OPTION = #{renew_option} ]]></if>
		<if test=" product_category != null and product_category != ''  "><![CDATA[ AND A.PRODUCT_CATEGORY = #{product_category} ]]></if>
		<if test=" schedule_rate  != null "><![CDATA[ AND A.SCHEDULE_RATE = #{schedule_rate} ]]></if>
		<if test=" product_code_sys != null and product_code_sys != ''  "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code_sys} ]]></if>
	</sql>
	
	<!-- 查询单条数据 -->
	<select id="findBusinessProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS FROM DEV_pds.T_BUSINESS_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="businessProductWhereCondition" />
		<![CDATA[ ORDER BY A.BUSINESS_PRD_ID ]]>
	</select>
	
	<!-- 查询单条数据 -->
	<select id="QRY_INTEGRAL_findBusinessProduct" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRIMARY_SALES_CHANNEL, A.SINGLE_JOINT_LIFE, A.COVER_PERIOD_TYPE, A.PRODUCT_CATEGORY4, A.BUSINESS_PRD_ID, A.PRODUCT_CATEGORY3, A.PRODUCT_CODE_ORIGINAL, 
			A.PRODUCT_CATEGORY2, A.PRODUCT_CATEGORY1, A.PRODUCT_NAME_SYS, A.PRODUCT_ABBR_NAME, A.PRODUCT_STATIC_CODE, A.INSURED_COUNT_MAX, A.INSURED_COUNT_MIN, 
			A.PRODUCT_DESC, A.RELEASE_DATE, A.PREMIUM_RATE_LAYER, A.PREMIUM_CURRENCY, A.PRODUCT_NAME_STD, A.PRODUCT_CODE_STD, A.RENEW_OPTION, 
			A.PRODUCT_CATEGORY, A.SCHEDULE_RATE, A.PRODUCT_CODE_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT A WHERE ROWNUM <=  1000  ]]>
		<include refid="businessProductWhereCondition" />
		<![CDATA[ ORDER BY A.BUSINESS_PRD_ID ]]>
	</select>
	
	<sql id="claimAdjustWhereCondition">
		<if test=" adjust_id  != null "><![CDATA[ AND A.ADJUST_ID = #{adjust_id} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
		<if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
		<if test=" adjust_type != null and adjust_type != ''  "><![CDATA[ AND A.ADJUST_TYPE = #{adjust_type} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<select id="findAllClaimAdjust" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADJUST_ID, A.PRODUCT_ID, A.LIAB_ID, A.ADVANCE_FLAG, A.ADJUST_TYPE, 
			A.ITEM_ID, A.INSURED_ID, A.CASE_ID, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.FEE_AMOUNT, A.BUSI_ITEM_ID, A.POLICY_ID FROM DEV_CLM.T_CLAIM_ADJUST A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimAdjustWhereCondition" />
		<![CDATA[ ORDER BY A.ADJUST_ID ]]> 
	</select>
	<sql id="claimBusiProdWhereCondition">
		<if test=" actual_pay  != null "><![CDATA[ AND A.ACTUAL_PAY = #{actual_pay} ]]></if>
		<if test=" claim_busi_prod_id  != null "><![CDATA[ AND A.CLAIM_BUSI_PROD_ID = #{claim_busi_prod_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" valid_date  != null  and  valid_date  != ''  "><![CDATA[ AND A.VALID_DATE = #{valid_date} ]]></if>
		<if test=" waive_reason != null and waive_reason != ''  "><![CDATA[ AND A.WAIVE_REASON = #{waive_reason} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" expire_date  != null  and  expire_date  != ''  "><![CDATA[ AND A.EXPIRE_DATE = #{expire_date} ]]></if>
		<if test=" waive_desc != null and waive_desc != ''  "><![CDATA[ AND A.WAIVE_DESC = #{waive_desc} ]]></if>
		<if test=" liability_status  != null "><![CDATA[ AND A.LIABILITY_STATUS = #{liability_status} ]]></if>
		<if test=" due_date  != null  and  due_date  != ''  "><![CDATA[ AND A.DUE_DATE = #{due_date} ]]></if>
		<if test=" waive_start  != null  and  waive_start  != ''  "><![CDATA[ AND A.WAIVE_START = #{waive_start} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" waive_amt  != null "><![CDATA[ AND A.WAIVE_AMT = #{waive_amt} ]]></if>
		<if test=" waive_end  != null  and  waive_end  != ''  "><![CDATA[ AND A.WAIVE_END = #{waive_end} ]]></if>
		<if test=" deal_conclusion  != null "><![CDATA[ AND A.DEAL_CONCLUSION = #{deal_conclusion} ]]></if>
		<if test=" calc_pay  != null "><![CDATA[ AND A.CALC_PAY = #{calc_pay} ]]></if>
		<if test=" reject_pay  != null "><![CDATA[ AND A.REJECT_PAY = #{reject_pay} ]]></if>
	</sql>
	<select id="findAllClaimBusiProd" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACTUAL_PAY, A.CLAIM_BUSI_PROD_ID, A.BUSI_PROD_CODE, A.IS_WAIVED, A.VALID_DATE, 
			A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.EXPIRE_DATE, A.LIABILITY_STATUS, A.DUE_DATE, A.WAIVE_START, 
			A.INSURED_ID, A.CASE_ID, A.POLICY_CODE, A.WAIVE_AMT, A.WAIVE_END, 
			A.DEAL_CONCLUSION, A.CALC_PAY, A.REJECT_PAY, A.WAIVE_DESC FROM DEV_CLM.T_CLAIM_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimBusiProdWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_BUSI_PROD_ID ]]> 
	</select>
	<sql id="contractProductWhereCondition">
		<if test=" is_pause  != null "><![CDATA[ AND A.IS_PAUSE = #{is_pause} ]]></if>
		<if test=" master_product_code != null and master_product_code != ''  "><![CDATA[ AND A.MASTER_PRODUCT_CODE = #{master_product_code} ]]></if>
		<if test=" prod_pkg_plan_code != null and prod_pkg_plan_code != ''  "><![CDATA[ AND A.PROD_PKG_PLAN_CODE = #{prod_pkg_plan_code} ]]></if>
		<if test=" append_prem_af  != null "><![CDATA[ AND A.APPEND_PREM_AF = #{append_prem_af} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" norenew_reason != null and norenew_reason != ''  "><![CDATA[ AND A.NORENEW_REASON = #{norenew_reason} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" extra_prem_af  != null "><![CDATA[ AND A.EXTRA_PREM_AF = #{extra_prem_af} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" cc_sa  != null "><![CDATA[ AND A.CC_SA = #{cc_sa} ]]></if>
		<if test=" initial_extra_prem_af  != null "><![CDATA[ AND A.INITIAL_EXTRA_PREM_AF = #{initial_extra_prem_af} ]]></if>
		<if test=" amount  != null "><![CDATA[ AND A.AMOUNT = #{amount} ]]></if>
		<if test=" master_item_id  != null "><![CDATA[ AND A.MASTER_ITEM_ID = #{master_item_id} ]]></if>
		<if test=" master_product_id  != null "><![CDATA[ AND A.MASTER_PRODUCT_ID = #{master_product_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" paidup_date  != null  and  paidup_date  != ''  "><![CDATA[ AND A.PAIDUP_DATE = #{paidup_date} ]]></if>
		<if test=" pay_year  != null "><![CDATA[ AND A.PAY_YEAR = #{pay_year} ]]></if>
		<if test=" pay_period != null and pay_period != ''  "><![CDATA[ AND A.PAY_PERIOD = #{pay_period} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" copy_date  != null  and  copy_date  != ''  "><![CDATA[ AND A.COPY_DATE = #{copy_date} ]]></if>
		<if test=" count_way != null and count_way != ''  "><![CDATA[ AND A.COUNT_WAY = #{count_way} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" is_gift  != null "><![CDATA[ AND A.IS_GIFT = #{is_gift} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" additional_prem_af  != null "><![CDATA[ AND A.ADDITIONAL_PREM_AF = #{additional_prem_af} ]]></if>
		<if test=" renew_decision  != null "><![CDATA[ AND A.RENEW_DECISION = #{renew_decision} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" bonus_mode_code  != null "><![CDATA[ AND A.BONUS_MODE_CODE = #{bonus_mode_code} ]]></if>
		<if test=" benefit_level != null and benefit_level != ''  "><![CDATA[ AND A.BENEFIT_LEVEL = #{benefit_level} ]]></if>
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
		<if test=" bonus_sa  != null "><![CDATA[ AND A.BONUS_SA = #{bonus_sa} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" coverage_period != null and coverage_period != ''  "><![CDATA[ AND A.COVERAGE_PERIOD = #{coverage_period} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" renewal_extra_prem_af  != null "><![CDATA[ AND A.RENEWAL_EXTRA_PREM_AF = #{renewal_extra_prem_af} ]]></if>
		<if test=" maturity_date  != null  and  maturity_date  != ''  "><![CDATA[ AND A.MATURITY_DATE = #{maturity_date} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" health_service_flag  != null "><![CDATA[ AND A.HEALTH_SERVICE_FLAG = #{health_service_flag} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" pay_freq != null and pay_freq != ''  "><![CDATA[ AND A.PAY_FREQ = #{pay_freq} ]]></if>
		<if test=" unit  != null "><![CDATA[ AND A.UNIT = #{unit} ]]></if>
		<if test=" coverage_year  != null "><![CDATA[ AND A.COVERAGE_YEAR = #{coverage_year} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" renewal_discnted_prem_af  != null "><![CDATA[ AND A.RENEWAL_DISCNTED_PREM_AF = #{renewal_discnted_prem_af} ]]></if>
		<if test=" total_prem_af  != null "><![CDATA[ AND A.TOTAL_PREM_AF = #{total_prem_af} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" pause_date  != null  and  pause_date  != ''  "><![CDATA[ AND A.PAUSE_DATE = #{pause_date} ]]></if>
		<if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
		<if test=" charge_period != null and charge_period != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
		<if test=" std_prem_af  != null "><![CDATA[ AND A.STD_PREM_AF = #{std_prem_af} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" initial_discnt_prem_af  != null "><![CDATA[ AND A.INITIAL_DISCNT_PREM_AF = #{initial_discnt_prem_af} ]]></if>
		<if test=" cur_flag  != null "><![CDATA[ AND A.CUR_FLAG = #{cur_flag} ]]></if>
		<if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
		<if test=" initial_amount  != null "><![CDATA[ AND A.INITIAL_AMOUNT = #{initial_amount} ]]></if>
		<if test=" waiver_start  != null  and  waiver_start  != ''  "><![CDATA[ AND A.WAIVER_START = #{waiver_start} ]]></if>
		<if test=" waiver_end  != null  and  waiver_end  != ''  "><![CDATA[ AND A.WAIVER_END = #{waiver_end} ]]></if>
	</sql>
	
	<select id="findAllContractProduct2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.IS_MASTER_ITEM, A.IS_PAUSE, A.PROD_PKG_PLAN_CODE, A.APPEND_PREM_AF, A.IS_WAIVED, A.INTEREST_MODE, A.APPLY_CODE, 
			A.NORENEW_REASON, A.ORGAN_CODE, A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.POLICY_ID, A.INITIAL_AMOUNT, 
			A.CC_SA, A.INITIAL_EXTRA_PREM_AF, A.PAYOUT_RATE, A.AMOUNT, A.PAIDUP_DATE,
			A.PAY_YEAR, A.EXPIRY_DATE, A.PAY_PERIOD, A.LIABILITY_STATE, A.POLICY_CODE, A.COUNT_WAY, 
			A.IS_GIFT, A.RERINSTATE_DATE, A.ADDITIONAL_PREM_AF, A.RENEW_DECISION, A.VALIDATE_DATE, 
			A.BENEFIT_LEVEL, A.PRODUCT_ID, A.BONUS_SA, A.PRODUCT_CODE, A.APPLY_DATE, A.INTEREST_FLAG, 
			A.COVERAGE_PERIOD, A.ITEM_ID, A.RENEWAL_EXTRA_PREM_AF, A.WAIVER_END, A.MATURITY_DATE, A.BUSI_ITEM_ID, 
			A.HEALTH_SERVICE_FLAG, A.LAPSE_DATE, A.PAY_FREQ, A.UNIT, A.COVERAGE_YEAR, A.END_CAUSE, 
			A.LAPSE_CAUSE, A.RENEWAL_DISCNTED_PREM_AF, A.TOTAL_PREM_AF, A.DECISION_CODE, A.PAUSE_DATE, A.STD_PREM_AF, A.CHARGE_PERIOD, 
			A.SUSPEND_DATE, A.SUSPEND_CAUSE, A.DEDUCTIBLE_FRANCHISE, A.WAIVER_START, A.PREM_FREQ, A.INITIAL_DISCNT_PREM_AF FROM dev_pas.T_CONTRACT_PRODUCT A WHERE ROWNUM <=  1000   ]]>
		<include refid="contractProductWhereCondition" />
	</select>
	<!-- add by zhaoyq 根据责任组ID查询既往赔付的金额 -->
	<select id="findSumAmountByProductID" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT nvl(sum(li.calc_pay),0) as amount FROM DEV_CLM.t_claim_liab li, DEV_CLM.t_claim_case ca where li.case_id = ca.case_id and ca.case_status='80' and li.product_id= #{product_id}]]>
	</select>
	<select id="queryNotMedList" parameterType="java.util.Map"
		resultType="java.util.Map"> 
   <![CDATA[select distinct li.claim_liab_id,
                   li.policy_code,
                   li.busi_prod_code,
                   li.item_id,
                   li.liab_id,
                   li.liab_name,
                   li.ADVANCE_PAY,
                   li.ACTUAL_PAY,
                   pro.amount,
                     ( SELECT T.TXT_CONTENT FROM DEV_CLM.T_CLOB T WHERE T.CLOB_ID=li.CLOB_ID)CLM_REMARK,
                   li.liability_status liability_state
              from DEV_CLM.t_claim_liab li, DEV_CLM.t_contract_product pro
             where pro.item_id = li.item_id
               and li.case_id = #{case_id}
               and li.claim_type != '08']]>
	</select>
	<sql id="claimQueryParaDefByParaNameCondition">
		<if test=" para_name != null and para_name != '' "><![CDATA[ AND A.PARA_NAME = #{para_name} ]]></if>
	</sql>	
	<select id="claimFindParaDefByParaName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEPT_ID, A.PARA_ID, A.MODULE_ID, A.SYSTEM_ID, A.SINGLE_PARA_VALUE, A.END_DATE, A.PARA_VALUE, 
			A.DEPT_RELA, A.PARA_NAME, A.PARA_DESC, A.SYSTEM_ADMIN, A.START_DATE, A.ORGAN_RELA, A.TIME_RELA, 
			A.SCOPE_CODE, A.PARA_VALUE_NAME, A.DATA_TYPE, A.ORGAN_ID FROM T_UDMP_PARA_DEF A WHERE 1 = 1  ]]>
		<include refid="claimQueryParaDefByParaNameCondition" />
		<![CDATA[ ORDER BY A.PARA_ID ]]>
	</select>
		

	<select id="findAllClaimAdjustBusi" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ASSIGN_FLAG, A.ADJUST_BUSI_ID, A.ADJ_AMOUNT, A.REMARKS, A.BUSI_ADJUST_REASON, A.ADVANCE_FLAG, 
			A.ADJUST_TYPE, A.INSURED_ID, A.CASE_ID, A.BUSI_PROD_CODE, A.POLICY_CODE, 
			A.FEE_AMOUNT, A.BUSI_ITEM_ID, A.PAY_AMOUNT, A.POLICY_ID FROM DEV_CLM.T_CLAIM_ADJUST_BUSI A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimAdjustBusiWhereCondition" />
		<![CDATA[ ORDER BY A.ADJUST_BUSI_ID ]]> 
	</select>
	<sql id="claimAdjustBusiWhereCondition">
		<if test=" assign_flag  != null "><![CDATA[ AND A.ASSIGN_FLAG = #{assign_flag} ]]></if>
		<if test=" adjust_busi_id  != null "><![CDATA[ AND A.ADJUST_BUSI_ID = #{adjust_busi_id} ]]></if>
		<if test=" adj_amount  != null "><![CDATA[ AND A.ADJ_AMOUNT = #{adj_amount} ]]></if>
		<if test=" remarks != null and remarks != ''  "><![CDATA[ AND A.REMARKS = #{remarks} ]]></if>
		<if test=" busi_adjust_reason  != null "><![CDATA[ AND A.BUSI_ADJUST_REASON = #{busi_adjust_reason} ]]></if>
		<if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
		<if test=" adjust_type != null and adjust_type != ''  "><![CDATA[ AND A.ADJUST_TYPE = #{adjust_type} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" pay_amount  != null "><![CDATA[ AND A.PAY_AMOUNT = #{pay_amount} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="contractMasterWhereCondition">
		<if test=" policy_pwd != null and policy_pwd != ''  "><![CDATA[ AND A.POLICY_PWD = #{policy_pwd} ]]></if>
		<if test=" media_type  != null "><![CDATA[ AND A.MEDIA_TYPE = #{media_type} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" interest_mode  != null "><![CDATA[ AND A.INTEREST_MODE = #{interest_mode} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" sale_agent_name != null and sale_agent_name != ''  "><![CDATA[ AND A.SALE_AGENT_NAME = #{sale_agent_name} ]]></if>
		<if test=" insured_family  != null "><![CDATA[ AND A.INSURED_FAMILY = #{insured_family} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" derivation != null and derivation != ''  "><![CDATA[ AND A.DERIVATION = #{derivation} ]]></if>
		<if test=" channel_org_code != null and channel_org_code != ''  "><![CDATA[ AND A.CHANNEL_ORG_CODE = #{channel_org_code} ]]></if>
		<if test=" subinput_type != null and subinput_type != ''  "><![CDATA[ AND A.SUBINPUT_TYPE = #{subinput_type} ]]></if>
		<if test=" basic_remark != null and basic_remark != ''  "><![CDATA[ AND A.BASIC_REMARK = #{basic_remark} ]]></if>
		<if test=" sale_com_code != null and sale_com_code != ''  "><![CDATA[ AND A.SALE_COM_CODE = #{sale_com_code} ]]></if>
		<if test=" input_date  != null  and  input_date  != ''  "><![CDATA[ AND A.INPUT_DATE = #{input_date} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" expiry_date  != null  and  expiry_date  != ''  "><![CDATA[ AND A.EXPIRY_DATE = #{expiry_date} ]]></if>
		<if test=" pwd_invalid_flag  != null "><![CDATA[ AND A.PWD_INVALID_FLAG = #{pwd_invalid_flag} ]]></if>
		<if test=" submit_channel  != null "><![CDATA[ AND A.SUBMIT_CHANNEL = #{submit_channel} ]]></if>
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" sale_agent_code != null and sale_agent_code != ''  "><![CDATA[ AND A.SALE_AGENT_CODE = #{sale_agent_code} ]]></if>
		<if test=" rerinstate_date  != null  and  rerinstate_date  != ''  "><![CDATA[ AND A.RERINSTATE_DATE = #{rerinstate_date} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" agency_code != null and agency_code != ''  "><![CDATA[ AND A.AGENCY_CODE = #{agency_code} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" apl_permit  != null "><![CDATA[ AND A.APL_PERMIT = #{apl_permit} ]]></if>
		<if test=" service_handler_code != null and service_handler_code != ''  "><![CDATA[ AND A.SERVICE_HANDLER_CODE = #{service_handler_code} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if
			test=" initial_validate_date  != null  and  initial_validate_date  != ''  "><![CDATA[ AND A.INITIAL_VALIDATE_DATE = #{initial_validate_date} ]]></if>
		<if test=" submission_date  != null  and  submission_date  != ''  "><![CDATA[ AND A.SUBMISSION_DATE = #{submission_date} ]]></if>
		<if test=" service_handler != null and service_handler != ''  "><![CDATA[ AND A.SERVICE_HANDLER = #{service_handler} ]]></if>
		<if test=" e_service_flag  != null "><![CDATA[ AND A.E_SERVICE_FLAG = #{e_service_flag} ]]></if>
		<if test=" service_bank_branch != null and service_bank_branch != ''  "><![CDATA[ AND A.SERVICE_BANK_BRANCH = #{service_bank_branch} ]]></if>
		<if test=" lapse_date  != null  and  lapse_date  != ''  "><![CDATA[ AND A.LAPSE_DATE = #{lapse_date} ]]></if>
		<if test=" dc_indi  != null "><![CDATA[ AND A.DC_INDI = #{dc_indi} ]]></if>
		<if test=" sale_type != null and sale_type != ''  "><![CDATA[ AND A.SALE_TYPE = #{sale_type} ]]></if>
		<if test=" input_type != null and input_type != ''  "><![CDATA[ AND A.INPUT_TYPE = #{input_type} ]]></if>
		<if test=" end_cause != null and end_cause != ''  "><![CDATA[ AND A.END_CAUSE = #{end_cause} ]]></if>
		<if test=" issue_date  != null  and  issue_date  != ''  "><![CDATA[ AND A.ISSUE_DATE = #{issue_date} ]]></if>
		<if test=" lapse_cause != null and lapse_cause != ''  "><![CDATA[ AND A.LAPSE_CAUSE = #{lapse_cause} ]]></if>
		<if test=" decision_code != null and decision_code != ''  "><![CDATA[ AND A.DECISION_CODE = #{decision_code} ]]></if>
		<if test=" agent_org_id != null and agent_org_id != ''  "><![CDATA[ AND A.AGENT_ORG_ID = #{agent_org_id} ]]></if>
		<if test=" service_bank != null and service_bank != ''  "><![CDATA[ AND A.SERVICE_BANK = #{service_bank} ]]></if>
		<if test=" suspend_date  != null  and  suspend_date  != ''  "><![CDATA[ AND A.SUSPEND_DATE = #{suspend_date} ]]></if>
		<if test=" initial_prem_date  != null  and  initial_prem_date  != ''  "><![CDATA[ AND A.INITIAL_PREM_DATE = #{initial_prem_date} ]]></if>
		<if test=" suspend_cause != null and suspend_cause != ''  "><![CDATA[ AND A.SUSPEND_CAUSE = #{suspend_cause} ]]></if>
		<if test=" lang_code != null and lang_code != ''  "><![CDATA[ AND A.LANG_CODE = #{lang_code} ]]></if>
		<if test=" former_id  != null "><![CDATA[ AND A.FORMER_ID = #{former_id} ]]></if>
		<if test=" statistic_channel  != null "><![CDATA[ AND A.STATISTIC_CHANNEL = #{statistic_channel} ]]></if>	
	</sql>
	
	<select id="CLM_findContractMaster" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT A.STATISTIC_CHANNEL,A.POLICY_PWD, A.MEDIA_TYPE, A.APPLY_CODE, A.INTEREST_MODE, A.ORGAN_CODE, A.CHANNEL_TYPE, A.SALE_AGENT_NAME, 
			A.INSURED_FAMILY, A.POLICY_ID, A.DERIVATION, A.SUBINPUT_TYPE, 
			A.BASIC_REMARK, A.SALE_COM_CODE, A.INPUT_DATE, A.POLICY_TYPE, A.EXPIRY_DATE, A.PWD_INVALID_FLAG, A.SUBMIT_CHANNEL, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, A.RERINSTATE_DATE, A.BRANCH_CODE, B.VALIDDATE_DATE, 
			A.AGENCY_CODE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, A.INITIAL_VALIDATE_DATE, 
			A.SUBMISSION_DATE, A.SERVICE_HANDLER, A.E_SERVICE_FLAG, A.SERVICE_BANK_BRANCH, A.LAPSE_DATE, A.DC_INDI, 
			A.SALE_TYPE, A.INPUT_TYPE, A.END_CAUSE, A.ISSUE_DATE, A.LAPSE_CAUSE, A.DECISION_CODE, 
			A.AGENT_ORG_ID, A.SERVICE_BANK, A.SUSPEND_DATE, A.INITIAL_PREM_DATE, A.SUSPEND_CAUSE, A.LANG_CODE, A.FORMER_ID  FROM DEV_pas.T_CONTRACT_MASTER  A  LEFT JOIN DEV_CLM.T_CONTRACT_MASTER B 
      ON A.POLICY_CODE=B.POLICY_CODE WHERE 1 = 1  ]]>
		<include refid="contractMasterWhereCondition" />
	</select>
	<!-- 查询所有操作 -->
	<select id="findAllAggregationRiskType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RISK_DESC, A.RISK_TYPE FROM DEV_PAS.T_AGGREGATION_RISK_TYPE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RISK_TYPE ]]> 
	</select>
	<sql id="findRiskAmountCondition">
		<if test=" liability_state  != null "><![CDATA[ AND A.LIABILITY_STATE = #{liability_state} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
		<if test=" risk_type != null and risk_type != ''  "><![CDATA[ AND A.RISK_TYPE = #{risk_type} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" amount_status  != null "><![CDATA[ AND A.AMOUNT_STATUS = #{amount_status} ]]></if>
		<if test=" internal_code != null and internal_code != ''  "><![CDATA[ AND A.INTERNAL_CODE = #{internal_code} ]]></if>
		<if test=" risk_amount  != null "><![CDATA[ AND A.RISK_AMOUNT = #{risk_amount} ]]></if>
		
		<if test=" liability_state_list  != null and liability_state_list.size()!=0">
		<![CDATA[ AND (A.liability_state in ]]>
		<foreach collection ="liability_state_list" item="list" index="index" open="(" close=")" separator=",">#{list}</foreach>
		<if test=" liability_is_null  != null "><![CDATA[ OR A.liability_state is null ]]></if>
		<![CDATA[ )]]>
		</if>
	</sql>
	<!-- 	条件累加风险保额 -->
	<select id="sumRiskAmountByAmountStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[      SELECT SUM(A.RISK_AMOUNT) as risk_amount FROM (
  		  select t1.*,
               t2.apply_code,
               t2.item_id,
               t2.liability_state
          from DEV_PAS.t_risk_amount t1
          left join DEV_PAS.t_contract_product t2
            on (t1.item_id = t2.item_id and t1.apply_code = t2.apply_code)
            )  A WHERE 1=1]]>
		<include refid="findRiskAmountCondition" />			    
	</select>
	<!-- 条件查询所有的风险类型 -->
	<select id="findAllRiskType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT DISTINCT A.risk_type FROM DEV_PAS.t_risk_amount A WHERE 1=1 ]]>
		<include refid="findRiskAmountCondition" />
	</select>
	<!-- 查询利率值 -->
	<select id="findBankInterestRate" resultType="java.util.Map"
		parameterType="java.util.Map">
			<![CDATA[ select b.rate from (select a.rate,row_number() over( order by a.effective_date desc) rownumber
	            from DEV_PDS.t_bank_interest_rate a where a.RATE_TYPE = #{rate_type} and #{effective_date, jdbcType=DATE} >= a.effective_date)  b where rownumber = 1 ]]>
	</select>
	
	<select id="queryClaimHisInfoTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
    <![CDATA[   select count(*)   
		       from DEV_CLM.t_claim_case c ,
		            DEV_CLM.t_contract_master    m,
		            DEV_PDS.t_business_product   p,
		            DEV_CLM.t_contract_busi_prod d,
		            DEV_CLM.t_claim_accident a
		      where c.insured_id = #{insured_id}
		        and c.case_id = m.case_id
		        and c.case_id = d.case_id
		        and c.accident_id = a.accident_id
		        and m.policy_code = d.policy_code
		        and m.cur_flag = d.cur_flag
		        and m.cur_flag='1' 
		        and p.business_prd_id = d.busi_prd_id]]>
		<if test=" case_no  != null  and  case_no  != ''  and  case_no  != 'null' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
	</select>
	
	<!-- gaojun_wb 通过出险人查询既往赔案信息   p.product_abbr_name, 保险名有人提bug删除  -->
	<select id="queryClaimHisInfo" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    	select B.* from(
           select A.*,rownum RN from(   
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.case_status,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		                (select tuu.real_name from  t_udmp_user  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		           (select tuu.real_name from  t_udmp_user  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            c.actual_pay,       
		            m.policy_code,       
		             m.policy_type,
		            a.acc_date     
		       from DEV_CLM.t_claim_case c ,
		            DEV_CLM.t_contract_master    m,
		            DEV_PDS.t_business_product   p,
		            DEV_CLM.t_contract_busi_prod d,
		            DEV_CLM.t_claim_accident a
		      where c.insured_id = #{insured_id}
		        and c.case_id = m.case_id
		        and c.case_id = d.case_id
		        and c.accident_id = a.accident_id
		        and m.policy_code = d.policy_code
		        and m.cur_flag = d.cur_flag
		        and m.cur_flag='1' 
		        and p.business_prd_id = d.busi_prd_id]]>
		<if test=" case_no  != null  and  case_no  != ''  and  case_no  != 'null' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<![CDATA[
		 group by    c.case_id,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,
                m.policy_code,
                c.approver_id,c.approver_id  ,c.auditor_id , c.actual_pay,      
		            a.acc_date  ,m.policy_type
		
		
		 ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM}]]>
	</select>
	
	<!-- add by zhangjy_wb start -->
	<select id="findClaimSubCaseByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM DEV_CLM.T_CLAIM_SUB_CASE A WHERE 1 = 1  AND A.CASE_ID = #{case_id}]]>
			<if test="claimType !=null and claimType != ''"> 
					 <![CDATA[  AND A.CLAIM_TYPE IN (${claimType}) ]]>
			</if>
			<![CDATA[ ORDER BY A.CLAIM_DATE ]]> 
	</select>
	<sql id="surveyApplyWhereCondition">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" survey_type  != null "><![CDATA[ AND A.SURVEY_TYPE = #{survey_type} ]]></if>
		<if test=" apply_section  != null "><![CDATA[ AND A.APPLY_SECTION = #{apply_section} ]]></if>
		<if test=" internal_resualt != null and internal_resualt != ''  "><![CDATA[ AND A.INTERNAL_RESUALT = #{internal_resualt} ]]></if>
		<if test=" survey_rule_id  != null "><![CDATA[ AND A.SURVEY_RULE_ID = #{survey_rule_id} ]]></if>
		<if test=" survey_doc_id  != null "><![CDATA[ AND A.SURVEY_DOC_ID = #{survey_doc_id} ]]></if>
		<if test=" survey_per  != null "><![CDATA[ AND A.SURVEY_PER = #{survey_per} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" survey_status  != null "><![CDATA[ AND A.SURVEY_STATUS = #{survey_status} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" survey_mode != null and survey_mode != ''  "><![CDATA[ AND A.SURVEY_MODE = #{survey_mode} ]]></if>
		<if test=" repeal_reason != null and repeal_reason != ''  "><![CDATA[ AND A.REPEAL_REASON = #{repeal_reason} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" survey_desc != null and survey_desc != ''  "><![CDATA[ AND A.SURVEY_DESC = #{survey_desc} ]]></if>
		<if test=" apply_org != null and apply_org != ''  "><![CDATA[ AND A.APPLY_ORG = #{apply_org} ]]></if>
		<if test=" apply_id  != null "><![CDATA[ AND A.APPLY_ID = #{apply_id} ]]></if>
		<if test=" biz_type  != null "><![CDATA[ AND A.BIZ_TYPE = #{biz_type} ]]></if>
		<if test=" survey_advice != null and survey_advice != ''  "><![CDATA[ AND A.SURVEY_ADVICE = #{survey_advice} ]]></if>
		<if test=" survey_reason != null and survey_reason != ''  "><![CDATA[ AND A.SURVEY_REASON = #{survey_reason} ]]></if>
		<if test=" cs_apply_code != null and cs_apply_code != ''  "><![CDATA[ AND A.CS_APPLY_CODE = #{cs_apply_code} ]]></if>
		<if test=" survey_org != null and survey_org != ''  "><![CDATA[ AND A.SURVEY_ORG = #{survey_org} ]]></if>
		<if test=" cs_background != null and cs_background != ''  "><![CDATA[ AND A.CS_BACKGROUND = #{cs_background} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id, jdbcType=NUMERIC} ]]></if>
		<if test=" cs_accept_code != null and cs_accept_code != ''  "><![CDATA[ AND A.CS_ACCEPT_CODE = #{cs_accept_code} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" survey_by_level  != null "><![CDATA[ AND A.SURVEY_BY_LEVEL = #{survey_by_level} ]]></if>
		<if test=" survey_code != null and survey_code != ''  "><![CDATA[ AND A.SURVEY_CODE = #{survey_code} ]]></if>
		<if test=" apply_per  != null "><![CDATA[ AND A.APPLY_PER = #{apply_per} ]]></if>
		<if test=" cs_item != null and cs_item != ''  "><![CDATA[ AND A.CS_ITEM = #{cs_item} ]]></if>
	</sql>
	<select id="claimFindAllSurveyApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM DEV_CLM.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<include refid="surveyApplyWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
	</select>
	<sql id="premArapWhereCondition">
		<if test=" task_mark  != null "><![CDATA[ AND A.TASK_MARK = #{task_mark} ]]></if>
		<if test=" policy_organ_code != null and policy_organ_code != ''  "><![CDATA[ AND A.POLICY_ORGAN_CODE = #{policy_organ_code} ]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" is_item_main  != null "><![CDATA[ AND A.IS_ITEM_MAIN = #{is_item_main} ]]></if>
		<if test=" bookkeeping_id  != null "><![CDATA[ AND A.BOOKKEEPING_ID = #{bookkeeping_id} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" payee_phone != null and payee_phone != ''  "><![CDATA[ AND A.PAYEE_PHONE = #{payee_phone} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" paid_count  != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
		<if test=" funds_rtn_code != null and funds_rtn_code != ''  "><![CDATA[ AND A.FUNDS_RTN_CODE = #{funds_rtn_code} ]]></if>
		<if test=" customer_account_flag  != null "><![CDATA[ AND A.CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} ]]></if>
		<if test=" fee_type != null and fee_type != ''  "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" seq_no  != null "><![CDATA[ AND A.SEQ_NO = #{seq_no} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" fee_status_date  != null  and  fee_status_date  != ''  "><![CDATA[ AND A.FEE_STATUS_DATE = #{fee_status_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" red_bookkeeping_time  != null  and  red_bookkeeping_time  != ''  "><![CDATA[ AND A.RED_BOOKKEEPING_TIME = #{red_bookkeeping_time} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" is_bank_text_date  != null  and  is_bank_text_date  != ''  "><![CDATA[ AND A.IS_BANK_TEXT_DATE = #{is_bank_text_date} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" is_risk_main  != null "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
		<if test=" is_bank_account  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT = #{is_bank_account} ]]></if>
		<if test=" frozen_status != null and frozen_status != ''  "><![CDATA[ AND A.FROZEN_STATUS = #{frozen_status} ]]></if>
		<if test=" posted != null and posted != ''  "><![CDATA[ AND A.POSTED = #{posted} ]]></if>
		<if test=" bookkeeping_flag  != null "><![CDATA[ AND A.BOOKKEEPING_FLAG = #{bookkeeping_flag} ]]></if>
		<if test=" group_id  != null "><![CDATA[ AND A.GROUP_ID = #{group_id} ]]></if>
		<if test=" red_bookkeeping_by  != null "><![CDATA[ AND A.RED_BOOKKEEPING_BY = #{red_bookkeeping_by} ]]></if>
		<if test=" frozen_status_date  != null  and  frozen_status_date  != ''  "><![CDATA[ AND A.FROZEN_STATUS_DATE = #{frozen_status_date} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
		<if test=" cus_acc_details_id  != null "><![CDATA[ AND A.CUS_ACC_DETAILS_ID = #{cus_acc_details_id} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" product_channel != null and product_channel != ''  "><![CDATA[ AND A.PRODUCT_CHANNEL = #{product_channel} ]]></if>
		<if test=" is_bank_account_date  != null  and  is_bank_account_date  != ''  "><![CDATA[ AND A.IS_BANK_ACCOUNT_DATE = #{is_bank_account_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" business_type != null and business_type != ''  "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" red_belnr != null and red_belnr != ''  "><![CDATA[ AND A.RED_BELNR = #{red_belnr} ]]></if>
		<if test=" bank_text_status != null and bank_text_status != ''  "><![CDATA[ AND A.BANK_TEXT_STATUS = #{bank_text_status} ]]></if>
		<if test=" group_code != null and group_code != ''  "><![CDATA[ AND A.GROUP_CODE = #{group_code} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" cus_acc_update_by  != null "><![CDATA[ AND A.CUS_ACC_UPDATE_BY = #{cus_acc_update_by} ]]></if>
		<if test=" is_bank_text_by  != null "><![CDATA[ AND A.IS_BANK_TEXT_BY = #{is_bank_text_by} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" is_bank_account_by  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT_BY = #{is_bank_account_by} ]]></if>
		<if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" red_bookkeeping_flag  != null "><![CDATA[ AND A.RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag} ]]></if>
		<if test=" cus_acc_fee_amount  != null "><![CDATA[ AND A.CUS_ACC_FEE_AMOUNT = #{cus_acc_fee_amount} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" is_bank_text  != null "><![CDATA[ AND A.IS_BANK_TEXT = #{is_bank_text} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" busi_apply_date  != null  and  busi_apply_date  != ''  "><![CDATA[ AND A.BUSI_APPLY_DATE = #{busi_apply_date} ]]></if>
		<if test=" group_name != null and group_name != ''  "><![CDATA[ AND A.GROUP_NAME = #{group_name} ]]></if>
		<if test=" belnr != null and belnr != ''  "><![CDATA[ AND A.BELNR = #{belnr} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" withdraw_type != null and withdraw_type != ''  "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
		<if test=" rollback_unit_number != null and rollback_unit_number != ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number} ]]></if>
		<if test=" fail_times  != null "><![CDATA[ AND A.FAIL_TIMES = #{fail_times} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" frozen_status_by  != null "><![CDATA[ AND A.FROZEN_STATUS_BY = #{frozen_status_by} ]]></if>
		<if test=" bank_user_name != null and bank_user_name != ''  "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" fee_status_by  != null "><![CDATA[ AND A.FEE_STATUS_BY = #{fee_status_by} ]]></if>
		<if test=" pay_end_date  != null  and  pay_end_date  != ''  "><![CDATA[ AND A.PAY_END_DATE = #{pay_end_date} ]]></if>
		<if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" red_bookkeeping_id  != null "><![CDATA[ AND A.RED_BOOKKEEPING_ID = #{red_bookkeeping_id} ]]></if>
		<if test=" bookkeeping_by  != null "><![CDATA[ AND A.BOOKKEEPING_BY = #{bookkeeping_by} ]]></if>
		<if test=" cus_acc_update_time  != null  and  cus_acc_update_time  != ''  "><![CDATA[ AND A.CUS_ACC_UPDATE_TIME = #{cus_acc_update_time} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" bookkeeping_time  != null  and  bookkeeping_time  != ''  "><![CDATA[ AND A.BOOKKEEPING_TIME = #{bookkeeping_time} ]]></if>
		<if test=" refeflag != null and refeflag != ''  "><![CDATA[ AND A.REFEFLAG = #{refeflag} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
	</sql>
	<select id="findAllPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ FROM DEV_CLM.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<include refid="premArapWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	<sql id="claimUwWhereCondition">
		<if test=" cancel_reason != null and cancel_reason != ''  "><![CDATA[ AND A.CANCEL_REASON = #{cancel_reason} ]]></if>
		<if test=" claim_uw_type  != null "><![CDATA[ AND A.CLAIM_UW_TYPE = #{claim_uw_type} ]]></if>
		<if test=" uw_cancel_flag  != null "><![CDATA[ AND A.UW_CANCEL_FLAG = #{uw_cancel_flag} ]]></if>
		<if test=" not_inform_situation != null and not_inform_situation != ''  "><![CDATA[ AND A.NOT_INFORM_SITUATION = #{not_inform_situation} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" claim_uw_reason != null and claim_uw_reason != ''  "><![CDATA[ AND A.CLAIM_UW_REASON = #{claim_uw_reason} ]]></if>
		<if test=" apply_by  != null "><![CDATA[ AND A.APPLY_BY = #{apply_by} ]]></if>
		<if test=" uw_status != null and uw_status != ''  "><![CDATA[ AND A.UW_STATUS = #{uw_status} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" repair_back_way != null and repair_back_way != ''  "><![CDATA[ AND A.REPAIR_BACK_WAY = #{repair_back_way} ]]></if>
		<if test=" open_account_bank != null and open_account_bank != ''  "><![CDATA[ AND A.OPEN_ACCOUNT_BANK = #{open_account_bank} ]]></if>
		<if test=" uw_conclusion  != null "><![CDATA[ AND A.UW_CONCLUSION = #{uw_conclusion} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" not_effect_reason != null and not_effect_reason != ''  "><![CDATA[ AND A.NOT_EFFECT_REASON = #{not_effect_reason} ]]></if>
		<if test=" clm_uw_id  != null "><![CDATA[ AND A.CLM_UW_ID = #{clm_uw_id} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" bank_no != null and bank_no != ''  "><![CDATA[ AND A.BANK_NO = #{bank_no} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" uw_policy_id != null and uw_policy_id != ''  "><![CDATA[ AND A.UW_POLICY_ID = #{uw_policy_id} ]]></if>
		<if test=" uw_times != null and uw_times != ''  "><![CDATA[ AND A.UW_TIMES = #{uw_times} ]]></if>
	</sql>
	<select id="findAllClaimUw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_POLICY_ID, A.UW_TIMES FROM DEV_CLM.T_CLAIM_UW A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.CLM_UW_ID ]]> 
	</select>
	<select id="findServiceEnv" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.SERVICE_TYPE,A.SERVICE_IP,A.SERVICE_PORT,A.SERVICE_CONTEXT,A.SYSTEM_ID 
		 FROM DEV_PAS.T_UDMP_SERVICE_ENV A WHERE ROWNUM <=  1000  AND A.SYSTEM_ID =  #{system_id} ]]>
	</select>

	<select id="CLM_queryClaimHisInfoTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
    <![CDATA[   
    	select count(*) from(
   		]]>
            <include refid="CLM_queryClaimHisInfoSQL" />   
      <![CDATA[ 
      ) B]]>
	</select>
	<sql id="CLM_queryClaimHisInfoSQL">
		<![CDATA[
				SELECT C.CASE_ID,
						C.CASE_NO,
						C.CASE_STATUS,
						C.REGISTE_CONF_TIME,
						C.ACCEPT_DECISION,
						C.AUDIT_TIME,
						C.AUDIT_DECISION,
						C.APPROVE_TIME,
						C.APPROVE_DECISION,
						C.AUDITOR_ID,
						C.APPROVER_ID,
						BP.PRODUCT_ABBR_NAME AS PRODUCT_ABBR_NAME,
						(SELECT TUU.REAL_NAME
						   FROM DEV_PAS.T_UDMP_USER TUU
						  WHERE TUU.USER_ID = C.AUDITOR_ID) AUDITOR_NAME,
						(SELECT TUU.REAL_NAME
						   FROM DEV_PAS.T_UDMP_USER TUU
						  WHERE TUU.USER_ID = C.APPROVER_ID) APPROVER_NAME,
						CL.ACTUAL_PAY,
						CM.POLICY_CODE,
						CM.POLICY_TYPE,
						A.ACC_DATE,
						PA.FEE_STATUS
					FROM DEV_CLM.T_CLAIM_CASE C
					INNER JOIN DEV_CLM.T_CLAIM_ACCIDENT A
				    	ON C.ACCIDENT_ID = A.ACCIDENT_ID
					LEFT JOIN dev_clm.T_CLAIM_POLICY CP
				    	ON C.CASE_ID = CP.CASE_ID
					LEFT join (SELECT CL.CASE_ID,
				                    CL.BUSI_PROD_CODE,
				                    CL.POLICY_CODE,
				                    SUM(CL.ACTUAL_PAY) AS ACTUAL_PAY
				               FROM DEV_CLM.T_CLAIM_LIAB CL
				              GROUP BY CL.CASE_ID, CL.BUSI_PROD_CODE, CL.POLICY_CODE) CL
				    ON CP.CASE_ID = cl.CASE_ID
					AND CP.POLICY_CODE = CL.POLICY_CODE
                 ]]>
               <if test="query_busi_code==1">
					AND CL.BUSI_PROD_CODE IN (
					 SELECT BP.PRODUCT_CODE_SYS FROM DEV_PDS.T_BUSINESS_PRODUCT BP
                       WHERE 1 = 1
                          AND BP.WAIVER_CUSTOMER_ROLE='01'
                          AND BP.WAIVER_FLAG='1'
					)
                  </if>
                  <![CDATA[
					LEFT JOIN DEV_PAS.T_CONTRACT_MASTER CM
						ON CP.POLICY_CODE = CM.POLICY_CODE
					LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
						ON BP.PRODUCT_CODE_SYS = cl.BUSI_PROD_CODE
					LEFT JOIN DEV_CLM.T_PREM_ARAP PA
						ON C.CASE_NO = PA.BUSINESS_CODE   
				 	WHERE 1 = 1
				 	AND C.CASE_STATUS != '99'
		      	]]>
		 <if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no = #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[and  c.auditor_id is not null and  c.approver_id is not null
      		         and  m.policy_code = #{policy_code}  ]]>
		</if>
		<![CDATA[
		  GROUP BY C.CASE_ID,
                C.CASE_NO,
                C.CASE_STATUS,
                C.REGISTE_CONF_TIME,
                C.ACCEPT_DECISION,
                C.AUDIT_TIME,
                C.AUDIT_DECISION,    
                C.APPROVE_TIME,     
                C.APPROVE_DECISION, 
                C.AUDITOR_ID,C.APPROVER_ID,
                CL.ACTUAL_PAY,
                CM.POLICY_CODE,
                CM.POLICY_TYPE,
                A.ACC_DATE,
                BP.PRODUCT_ABBR_NAME,
                PA.FEE_STATUS
		        ORDER BY A.ACC_DATE DESC
		  ]]>
	</sql>
	<select id="CLM_queryClaimHisInfo" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    	SELECT B.* FROM(
           SELECT A.*,ROWNUM RN FROM( 
        ]]>
         <include refid="CLM_queryClaimHisInfoSQL" />   
       <![CDATA[
		 ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM}]]>
	</select>
	<select id="CLM_findClaimSubCaseByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.ACC_REASON, A.CLAIM_DATE, A.SUB_CASE_ID, 
			A.CASE_ID FROM dev_clm.T_CLAIM_SUB_CASE A WHERE 1 = 1  AND A.CASE_ID = #{case_id}]]>
			<if test="claimType !=null and claimType != ''"> 
					 <![CDATA[  AND A.CLAIM_TYPE IN (${claimType}) ]]>
			</if>
			<![CDATA[ ORDER BY A.CLAIM_DATE ]]> 
	</select>
	<!-- 查询所有操作 -->
	<select id="CLM_claimFindAllSurveyApply" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RELATED_ID, A.SURVEY_TYPE, A.APPLY_SECTION, A.INTERNAL_RESUALT, A.SURVEY_RULE_ID, A.SURVEY_DOC_ID, A.SURVEY_PER, 
			A.REMARK, A.SURVEY_STATUS, A.APPLY_DATE, A.SURVEY_MODE, A.REPEAL_REASON, A.CASE_NO, A.APPLY_CODE, 
			A.SURVEY_DESC, A.APPLY_ORG, A.APPLY_ID, A.BIZ_TYPE, A.SURVEY_ADVICE, 
			A.SURVEY_REASON, A.CS_APPLY_CODE, A.SURVEY_ORG, A.CS_BACKGROUND, A.CASE_ID, 
			A.CS_ACCEPT_CODE, A.POLICY_CODE, A.SURVEY_BY_LEVEL, A.SURVEY_CODE, A.APPLY_PER, 
			A.CS_ITEM FROM DEV_CLM.T_SURVEY_APPLY A WHERE ROWNUM <=  1000  ]]>
		<include refid="surveyApplyWhereCondition" />
		<![CDATA[ ORDER BY A.APPLY_ID ]]> 
	</select>
	<!-- 查询所有操作 -->
	<select id="CLM_findAllClaimUw" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CANCEL_REASON, A.CLAIM_UW_TYPE, A.UW_CANCEL_FLAG, A.NOT_INFORM_SITUATION, A.REMARK, A.APPLY_DATE, A.CLAIM_UW_REASON, 
			A.APPLY_BY, A.UW_STATUS, A.CASE_NO, A.REPAIR_BACK_WAY, A.OPEN_ACCOUNT_BANK, 
			A.UW_CONCLUSION, A.POLICY_ID, A.NOT_EFFECT_REASON, A.CLM_UW_ID, A.CASE_ID, 
			A.BANK_NO, A.POLICY_CODE, A.UW_POLICY_ID, A.UW_TIMES FROM DEV_CLM.T_CLAIM_UW A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimUwWhereCondition" />
		<![CDATA[ ORDER BY A.CLM_UW_ID ]]> 
	</select>
	<select id="CLM_findAllPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE FROM DEV_CLM.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<include refid="premArapWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	<select id="queryClaimHisInfoTotalLog1" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
    <![CDATA[   select count(1) from(
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.case_status,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            (select tuu.real_name from  DEV_PAS.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  DEV_PAS.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            c.actual_pay,       
		            a.acc_date     
		       from dev_clm.T_CLAIM_CASE c left join  dev_clm.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            dev_clm.T_CLAIM_POLICY    m,
		            dev_clm.T_CLAIM_BUSI_PROD d
		      where c.case_id = m.case_id
		      	and c.case_id = d.case_id
		      	and c.case_status != 99
		        and m.policy_code = d.policy_code ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<!--既往赔案修改  -->
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no != #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[ AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM dev_clm.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>
		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code in (${policy_code_str}) ]]>
		</if>
		
		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND  d.busi_item_id in (${busi_item_id_str})]]>
		</if>  
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,c.approver_id,
                c.actual_pay,
                m.policy_code,
		        a.acc_date
      ) ]]>
	</select>
	
	<!--xinghj_wb 迁移数据查既往赔案信息分页     用于理赔页面查询既往赔案  -->
	<select id="queryClaimHisInfoLog1" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    	select B.* from(
           select A.*,rownum RN from(   
    		select distinct 
    				c.case_id,
		            c.case_no,
		            c.case_status,
		            c.registe_conf_time,
		            c.accept_decision,
		            c.audit_time,
		            c.audit_decision,    
		            c.approve_time,     
		            c.approve_decision, 
		            c.auditor_id,
		            c.approver_id,
		            (select tuu.real_name from  DEV_PAS.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
		            (select tuu.real_name from  DEV_PAS.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
		            c.actual_pay,       
		            a.acc_date     
		       from dev_clm.T_CLAIM_CASE c left join  dev_clm.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
		            dev_clm.T_CLAIM_POLICY    m,
		            dev_clm.T_CLAIM_BUSI_PROD d
		      where c.case_id = m.case_id
		      	and c.case_id = d.case_id
		      	and c.case_status != 99
		        and m.policy_code = d.policy_code  ]]>
		 <if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no != #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[  AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM dev_clm.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>
		
		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code in (${policy_code_str}) ]]>
		</if>
		
		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND  d.busi_item_id in (${busi_item_id_str})]]>
		</if>  
		<![CDATA[
		 group by c.case_id,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,c.approver_id,
                c.actual_pay,
		        a.acc_date
		        order by a.acc_date desc
		 ) A 
     ) B]]>
	</select>
	
	<!-- xinghj_wb 通过出险人查询既往赔案信息个数   用于理赔页面查询既往赔案 -->
	<select id="queryClaimHisInfoTotal1" parameterType="java.util.Map"
		resultType="java.lang.Integer"> 
    <![CDATA[   select count(1) from(
    		select distinct 
                    c.case_id,
                    c.case_no,
                    c.case_status,
                    c.registe_conf_time,
                    c.accept_decision,
                    c.audit_time,
                    c.audit_decision,    
                    c.approve_time,     
                    c.approve_decision, 
                    c.auditor_id,
                    c.approver_id,
                    (select tuu.real_name from  DEV_PAS.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
                    (select tuu.real_name from  DEV_PAS.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
                    c.actual_pay,       
                     m.policy_type,
                    a.acc_date     
               from dev_clm.T_CLAIM_CASE c left join  dev_clm.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
                    dev_clm.T_CONTRACT_MASTER    m,
                    dev_clm.T_CONTRACT_BUSI_PROD d
              where c.case_id = m.case_id
                and c.case_id = d.case_id
                and m.policy_code = d.policy_code
                and m.cur_flag = d.cur_flag
                and m.cur_flag='1' 
                and c.case_status!='99' ]]>
		<if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no != #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[ AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM dev_clm.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>
		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code in (${policy_code_str}) ]]>
		</if>
		
		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM dev_clm.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=${busi_item_id_str} )]]>
		</if>  
		<![CDATA[
		group by c.case_id,m.policy_code,a.accident_no,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,c.approver_id,
                c.actual_pay,
                m.policy_code,
                m.policy_type,
		        a.acc_date
      ) ]]>
	</select>
	
	<!-- xinghj_wb 通过出险人查询既往赔案信息   用于理赔页面查询既往赔案 -->
	<select id="queryClaimHisInfo1" parameterType="java.util.Map"
		resultType="java.util.Map"> 
    <![CDATA[
    	select B.* from(
           select A.*,rownum RN from(   
            select distinct 
                    c.case_id,
                    c.case_no,
                    c.case_status,
                    c.registe_conf_time,
                    c.accept_decision,
                    c.audit_time,
                    c.audit_decision,    
                    c.approve_time,     
                    c.approve_decision, 
                    c.auditor_id,
                    c.approver_id,
                    (select tuu.real_name from  DEV_PAS.T_UDMP_USER  tuu where tuu.user_id=c.auditor_id  )  auditor_name,        
                    (select tuu.real_name from  DEV_PAS.T_UDMP_USER  tuu where tuu.user_id=c.approver_id  ) approver_name,         
                    c.actual_pay,       
                     m.policy_type,
                    a.acc_date     
               from dev_clm.T_CLAIM_CASE c left join  dev_clm.T_CLAIM_ACCIDENT a on c.accident_id = a.accident_id,
                    dev_clm.T_CONTRACT_MASTER    m,
                    dev_clm.T_CONTRACT_BUSI_PROD d
              where c.case_id = m.case_id
                and c.case_id = d.case_id
                and m.policy_code = d.policy_code
                and m.cur_flag = d.cur_flag
                and m.cur_flag='1' 
                and c.case_status!='99'
		         ]]>
		 <if test=" insured_id  != null  and  insured_id  != '' ">
      		<![CDATA[ and c.insured_id = #{insured_id} ]]>
		</if>
		<if test=" case_no  != null  and  case_no  != '' ">
      		<![CDATA[and c.case_no != #{case_no}]]>
		</if>
		<if test=" case_status  != null  and  case_status  != ''   ">
      		<![CDATA[and c.case_status in (${case_status})]]>
		</if>
		<if test=" policy_code  != null and  policy_code  != '' ">
      		<![CDATA[  AND  M.POLICY_CODE = #{policy_code}  ]]>
		</if>
		<if test=" busi_item_id  != null and  busi_item_id  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM dev_clm.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=#{busi_item_id} ) ]]>
		</if>
		
		<if test=" policy_code_str  != null and  policy_code_str  != '' ">
      		<![CDATA[  AND m.policy_code in (${policy_code_str}) ]]>
		</if>
		
		<if test=" busi_item_id_str  != null and  busi_item_id_str  != '' ">
      		<![CDATA[  AND EXISTS (SELECT 1 FROM dev_clm.T_CLAIM_LIAB T WHERE  T.POLICY_CODE=M.POLICY_CODE AND T.BUSI_ITEM_ID=${busi_item_id_str} )]]>
		</if>  
		 
		<![CDATA[
		 group by c.case_id,m.policy_code,a.accident_no,
                c.case_no,
                c.case_status,
                c.registe_conf_time,
                c.accept_decision,
                c.audit_time,
                c.audit_decision,    
                c.approve_time,     
                c.approve_decision, 
                c.auditor_id,c.approver_id,
                c.actual_pay,
                m.policy_code,
		        a.acc_date,
                m.policy_type
		        order by a.acc_date desc
		 ) A where rownum <= #{LESS_NUM} 
     ) B where RN > #{GREATER_NUM} and rn<200]]>
	</select>
	<select id="queryClaimCase4Redoo" parameterType="java.util.Map"
		resultType="java.util.Map">
   	    select cc.case_no,
		       cc.case_id,
		       ( select c.customer_name from   
		        DEV_CLM.T_CUSTOMER 
		         c where c.customer_id= cc.insured_id )   customer_name,
		       sc.claim_type,
		       cc.approve_decision,
		       cc.actual_pay,
		       cc.end_case_time
		  from DEV_CLM.T_CLAIM_CASE     cc,
		       DEV_CLM.T_CLAIM_SUB_CASE sc 
			 where  cc.case_id = sc.case_id
			   and cc.case_status = 80 
			   and exists(
			        select 1 from   
			             DEV_CLM.T_CUSTOMER 
			               c where c.customer_id= cc.insured_id 
			              <if test="trustee_name !=null and trustee_name != ''"> 
			              <![CDATA[ and c.customer_name = #{trustee_name} ]]>
			              </if>
			              <if test="med_dept !=null and med_dept != ''"> 
			                       <![CDATA[ and c.customer_certi_code = #{med_dept} ]]>
			              </if>
			             <![CDATA[   and rownum<2 ]]>
            ) 
        
    <if test="case_no !=null and case_no != ''"> 
             <![CDATA[ and cc.case_no = #{case_no} ]]>
    </if>
   
    <if test="case_id!=null">
        <![CDATA[ and cc.case_id=#{case_id}]]>
    </if>
	</select>
		<select id="queryClaimVisitList" parameterType="java.util.Map"
		resultType="java.util.Map">
		SELECT *
  		FROM (SELECT a.organ_code,
       to_char(b.visit_date,'yyyy-MM-dd') visit_date,
       b.visite_by,
       b.is_succeed
          FROM DEV_CLM.T_CLAIM_CASE A,dev_clm.T_CLAIM_CARE_VISIT b
          
         WHERE 1 = 1 and a.case_id=b.case_id
           AND A.CASE_ID =#{case_no}
         ORDER BY A.CASE_ID)
		</select>
		
			<select id="queryPayInfoList" parameterType="java.util.Map"
		resultType="java.util.Map">
		      SELECT ROWNUM          RN,
                   B.ORGAN_CODE,
                   B.LIST_ID,
                   A.FEE_ID,
                   A.Bene_Id,
                   B.BUSINESS_CODE,
                   H.BENE_NAME,
                   B.FEE_AMOUNT,
                   E.BANK_RET_NAME,
                   A.CHANGE_BY,
                   A.AUDIT_BY,
                   B.DUE_TIME,
                   B.FINISH_TIME   CHECK_ENTER_TIME,
                   A.APPLY_ID,
                   C.LOG_ID
              FROM DEV_CLM.T_PREM_ARAP  B
                   left join DEV_CLM.T_CLAIM_PAY_CHANGE  A on A.FEE_ID = B.LIST_ID
                   left join DEV_CLM.T_BANK_RET_CONF   E  on  E.BANK_RET_CODE = B.FUNDS_RTN_CODE,
                   DEV_CLM.T_CLAIM_PAY_CHANGE_LOG C,
                   DEV_CLM.T_CLAIM_BENE           H,
                   DEV_CLM.T_CLAIM_PAY            P
             WHERE C.FEE_ID = A.FEE_ID
               AND B.LIST_ID = P.prem_arap_id
               AND P.BENE_ID = H.BENE_ID
               AND  B.BUSINESS_CODE=#{case_no}
		</select>
				<select id="queryPayApplyInfo" parameterType="java.util.Map"
		resultType="java.util.Map">
		SELECT 
  (SELECT NAME FROM DEV_CLM.T_PAY_CHANGE_DECI WHERE CODE= B.PAY_AUDIT_CON)PAY_AUDIT_CON,
(select REAL_NAME from DEV_PAS.T_UDMP_USER where USER_ID=B.AUDIT_BY)AUDIT_BY,
                             A.AF_ACCOUNT_NAME,
                             A.BF_ACCOUNT_NAME,
                             (SELECT BANK_NAME FROM DEV_PAS.T_BANK WHERE BANK_CODE=A.AF_BANK_CODE)AF_BANK_CODE,
                             A.BF_PAYEE_GENDER,
                             (SELECT BANK_NAME FROM DEV_PAS.T_BANK WHERE BANK_CODE=A.BF_BANK_CODE)BF_BANK_CODE,
                              (SELECT TYPE FROM DEV_CLM.T_CERTI_TYPE WHERE CODE=A.AF_PAYEE_CERTI_TYPE)AF_PAYEE_CERTI_TYPE,
                             (SELECT NAME FROM DEV_CLM.T_PAY_MODE WHERE CODE=A.AF_PAY_MODE) AF_PAY_MODE,
                             A.BF_PAYEE_CERTI_NO,
                             A.AF_PAYEE_CERTI_NO,
                             to_char(A.BF_PAYEE_CERTI_END,'yyyy-MM-dd')BF_PAYEE_CERTI_END,
                             (SELECT TYPE FROM DEV_CLM.T_CERTI_TYPE WHERE CODE=A.BF_PAYEE_CERTI_TYPE) BF_PAYEE_CERTI_TYPE,
                             (SELECT NAME FROM DEV_CLM.T_PAY_MODE WHERE CODE=A.BF_PAY_MODE)BF_PAY_MODE,
                             (SELECT COUNTRY_NAME FROM DEV_CLM.T_COUNTRY WHERE COUNTRY_CODE=A.AF_PAYEE_NATION)AF_PAYEE_NATION,
                             A.BF_ACCOUNT_NO,
                             A.APPLY_ID,
                              to_char(A.AF_PAYEE_BIRTH,'yyyy-MM-dd')AF_PAYEE_BIRTH,
                             to_char(A.AF_PAYEE_CERTI_END,'yyyy-MM-dd') AF_PAYEE_CERTI_END,
                              to_char(A.BF_PAYEE_BIRTH,'yyyy-MM-dd')BF_PAYEE_BIRTH,
                             A.AF_ACCOUNT_NO,
                              to_char(A.AF_PAYEE_CERTI_START,'yyyy-MM-dd')AF_PAYEE_CERTI_START,
	                        (SELECT RELATION_NAME FROM DEV_CLM.T_LA_PH_RELA WHERE RELATION_CODE=A.AF_PAYEE_RELATION)AF_PAYEE_RELATION,
	                       A.FEE_ID,
	                       A.CASE_ID,
	                       A.LOG_ID,
	                       A.BENE_ID,
	                       (SELECT RELATION_NAME FROM DEV_CLM.T_LA_PH_RELA WHERE RELATION_CODE=A.BF_PAYEE_RELATION)BF_PAYEE_RELATION,
	                         (SELECT COUNTRY_NAME FROM DEV_CLM.T_COUNTRY WHERE COUNTRY_CODE=A.BF_PAYEE_NATION)BF_PAYEE_NATION,
	                       A.AF_PAYEE_GENDER,
	                       to_char(A.BF_PAYEE_CERTI_START,'yyyy-MM-dd')BF_PAYEE_CERTI_START,
	                       A.BF_PAYEE_NAME,
	                       A.AF_PAYEE_NAME,
	                        (SELECT NAME FROM DEV_CLM.T_PAY_CHANGE_REASON WHERE CODE=trim(B.PAY_CHANGE_REASON)) PAY_CHANGE_REASON,
	                       B.CHANGE_DESC,
	                       B.CHANGE_BY,
	                        to_char(B.CHANGE_TIME,'yyyy-MM-dd') CHANGE_TIME,
	                        to_char(B.AUDIT_TIME,'yyyy-MM-dd') AUDIT_TIME,
                             B.AUDIT_DESC
		                  FROM DEV_CLM.T_CLAIM_PAY_CHANGE_LOG A,
		                       DEV_CLM.T_CLAIM_PAY_CHANGE     B
		                 WHERE 1 = 1 
		                 AND A.APPLY_ID = B.APPLY_ID 
		<if test=" case_id  != null and case_id!= ''"> AND A.APPLY_ID = #{case_id} </if>
		<if test="auditor_id != null and auditor_id != ''"> AND A.LOG_ID = #{auditor_id} </if>
		</select>
			<select id="findAllPolicyHolder_clm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.JOB_UNDERWRITE, A.CUSTOMER_HEIGHT, A.CUSTOMER_ID, A.JOB_CODE, 
			A.CUSTOMER_WEIGHT, A.CASE_ID, A.APPLY_CODE, A.LOG_ID, A.COPY_DATE, A.POLICY_CODE, 
			A.LIST_ID, A.POLICY_ID, A.CUR_FLAG FROM DEV_CLM.T_POLICY_HOLDER A WHERE 
			 A.CUSTOMER_ID = #{customer_id}  and
			 ROWNUM <=  1000  ]]>
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
	
	<select id="findAllInsuredList_clm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.RELATION_TO_PH, A.APPLY_CODE, A.INSURED_AGE, 
			A.LIST_ID, A.POLICY_ID, A.STAND_LIFE, A.SMOKING, A.SOCI_SECU,
			A.JOB_UNDERWRITE, A.JOB_CODE, A.CUSTOMER_WEIGHT, A.CASE_ID, A.LOG_ID, A.COPY_DATE, 
			A.POLICY_CODE, A.CUR_FLAG, A.SOCI_SECU FROM DEV_CLM.T_INSURED_LIST A WHERE  A.CUSTOMER_ID = #{customer_id} and ROWNUM <=  1000  ]]>
		<![CDATA[ ORDER BY A.LOG_ID ]]> 
	</select>
	
	<select id="findPagecfgPrdElementByProductCodeSys" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			select u.label_name, g.list_value
			from dev_pas.t_pagecfg_prd_element g,
			dev_pas.t_pagecfg_unit        u
			where g.unit_code = u.unit_code
			and g.relation_id in
			(select relation_id
			from dev_pas.t_pagecfg_prd_cate_rela c
			where c.busi_prd_id =
			(select business_prd_id
			from dev_pas.t_business_product a
			where a.product_code_sys = #{product_code_sys}))
		]]>
	</select>
	
	<select id="findAllPagecfgElementValue" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[
			select a.list_value_code, a.list_value_id, a.code2, a.display_order, 
			a.code from dev_pas.t_pagecfg_element_value a 
			where a.list_value_code = #{list_value_code} and rownum = 1
		]]>
	</select>
		 <!-- 查询扫描的信息 -->
	<select id="findAllImageScan_clm" resultType="java.util.Map" parameterType="java.util.Map">
			<if test="apply_code != null">
				<![CDATA[ SELECT DISTINCT (A.BILLCARD_NO)
						  FROM APP___NB__DBUSER.T_IMAGE_SCAN A
						 WHERE A.BUSS_CODE = #{apply_code}
						UNION
						SELECT DISTINCT (A.BILLCARD_NO)
						  FROM APP___NB__DBUSER.T_IMAGE_SCAN A
						 WHERE A.APPLY_CODE = #{apply_code}]]>
 			</if>
	</select>
	<select id="findAllImageScanByOld_clm" resultType="java.util.Map" parameterType="java.util.Map">
			<if test="apply_code != null">
			<![CDATA[ select  distinct (A.billcard_no)  from  APP___NB__DBUSER.t_image_scan A 
			 WHERE ROWNUM <=  100 ]]>
			 AND A.apply_code = #{apply_code}</if>
	</select>
	<!--  需求变更#43845，加入事后扫描的单证 -->
    <select id="findAllImageScanByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
            <if test="apply_code != null">
            <![CDATA[ select  distinct (A.billcard_no)  from  APP___NB__DBUSER.t_image_scan A 
             WHERE ROWNUM <=  100  ]]>
             AND A.buss_code = #{policy_code}</if>
    </select>
     <!--  //需求变更#43845，加入事后扫描的单证 -->
	<!-- add by zhaoxx_wb电子签名轨迹分页总数 -->
	<select id="findAllSignatureTraceTotal" parameterType="java.util.Map" resultType="java.lang.Integer"> 
    <![CDATA[SELECT COUNT(*) FROM DEV_CLM.T_CLAIM_SIGNATURE_TRACE A LEFT JOIN DEV_CLM.T_CLAIM_CASE B 
             ON A.CASE_NO = B.CASE_NO  WHERE   B.CASE_ID = #{case_id}]]>		
	</select>
	<!-- add by zhaoxx_wb电子签名轨迹分页 -->
	<select id="findAllSignatureTrace" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
	         SELECT A.TRACE_NUM, A.CASE_NO, 
             (SELECT C.USER_TYPE_NAME FROM DEV_CLM.T_SIGNATURE_USER_TYPE C WHERE C.USER_TYPE_CODE = A.SIGNATURE_USER_TYPE ) AS SIGNATURE_USER_TYPE_NAME, 
             A.USER_ID,A.SIGNATURE_IDENTIFICATION,A.SIGNATURE_TIME,B.CASE_ID 
             FROM DEV_CLM.T_CLAIM_SIGNATURE_TRACE A LEFT JOIN DEV_CLM.T_CLAIM_CASE B 
             ON A.CASE_NO = B.CASE_NO  
             WHERE B.CASE_ID = #{case_id}
             ORDER BY A.TRACE_NUM
	]]>
	</select>
	<!-- add by zhaoxx_wb电子签名查询数据  -->
	<select id="querySignatureLines" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[
	         SELECT A.TRACE_NUM, A.CASE_NO,A.USER_ID,A.SIGNATURE_IDENTIFICATION,A.SIGNATURE_TIME FROM DEV_CLM.T_CLAIM_SIGNATURE_TRACE A LEFT JOIN DEV_CLM.T_CLAIM_CASE B 
             ON A.CASE_NO = B.CASE_NO  WHERE   B.CASE_ID = #{case_id}
	]]>
	</select>	
	
	<!-- 按查询当前意健险有效设置信息 -->	
	<select id="findDataSwitchSettingsIsValid" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.LIST_ID, A.CASE_TYPE, A.IS_VALID, A.SUBMIT_DATE, 
		A.SUBMITOR_ID FROM DEV_CLM.T_DATA_SWITCH_SETTINGS A WHERE 1 = 1  ]]>
		<if test=" is_valid  != null "><![CDATA[ AND A.IS_VALID = #{is_valid} ]]></if>
		<![CDATA[ ORDER BY A.SUBMIT_DATE DESC ]]>
	</select>
	
	<!-- 查询意健险用户查看轨迹个数操作 -->
	<select id="findClaimViewRecordTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_CLM.T_CLAIM_VIEW_RECORD A WHERE 1 = 1  ]]>
		<if test=" record_id  != null "><![CDATA[ AND A.RECORD_ID = #{record_id} ]]></if>
		<if test=" view_count  != null "><![CDATA[ AND A.VIEW_COUNT = #{view_count} ]]></if>
		<if test=" opertor_id  != null "><![CDATA[ AND A.OPERTOR_ID = #{opertor_id} ]]></if>
		<if test=" count_record_type != null and count_record_type != ''  "><![CDATA[ AND A.COUNT_RECORD_TYPE = #{count_record_type} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</select>

<!-- 分页查询意健险用户查看轨迹操作 -->
	<select id="queryClaimViewRecordForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RECORD_ID, B.VIEW_COUNT, B.OPERTOR_ID, 
			B.COUNT_RECORD_TYPE, B.CASE_ID,B.UPDATE_TIME FROM (
					SELECT ROWNUM RN, A.RECORD_ID, A.VIEW_COUNT, A.OPERTOR_ID, 
			A.COUNT_RECORD_TYPE, A.CASE_ID,A.UPDATE_TIME FROM DEV_CLM.T_CLAIM_VIEW_RECORD A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<if test=" record_id  != null "><![CDATA[ AND A.RECORD_ID = #{record_id} ]]></if>
		<if test=" view_count  != null "><![CDATA[ AND A.VIEW_COUNT = #{view_count} ]]></if>
		<if test=" opertor_id  != null "><![CDATA[ AND A.OPERTOR_ID = #{opertor_id} ]]></if>
		<if test=" count_record_type != null and count_record_type != ''  "><![CDATA[ AND A.COUNT_RECORD_TYPE = #{count_record_type} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<![CDATA[ ORDER BY A.UPDATE_TIME DESC ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 查询意健险行业共享信息提示 -->
	<select id="findAllCiitcBackResult" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.HAVE_WEB_DATA, A.WEB_QUERY_CODE, A.SERIAL_NUMBER, A.RISK_RESULT, A.CASE_ID, 
			A.PRODUCT_TYPE, A.LIST_ID, A.DATA_EXPIRY_DATE, A.RISK_RESULT_DATA FROM DEV_CLM.T_CIITC_BACK_RESULT A WHERE ROWNUM <=  1000  ]]>
		<if test=" have_web_data  != null "><![CDATA[ AND A.HAVE_WEB_DATA = #{have_web_data} ]]></if>
		<if test=" web_query_code != null and web_query_code != ''  "><![CDATA[ AND A.WEB_QUERY_CODE = #{web_query_code} ]]></if>
		<if test=" serial_number != null and serial_number != ''  "><![CDATA[ AND A.SERIAL_NUMBER = #{serial_number} ]]></if>
		<if test=" risk_result != null and risk_result != ''  "><![CDATA[ AND A.RISK_RESULT = #{risk_result} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" product_type != null and product_type != ''  "><![CDATA[ AND A.PRODUCT_TYPE = #{product_type} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" data_expiry_date  != null  and  data_expiry_date  != ''  "><![CDATA[ AND A.DATA_EXPIRY_DATE = #{data_expiry_date} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	<!-- 查询意健险平台结果查询网址 -->
	<select id="ciitcFindParaDefByParaName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEPT_ID, A.PARA_ID, A.MODULE_ID, A.SYSTEM_ID, A.SINGLE_PARA_VALUE, A.END_DATE, A.PARA_VALUE, 
			A.DEPT_RELA, A.PARA_NAME, A.PARA_DESC, A.SYSTEM_ADMIN, A.START_DATE, A.ORGAN_RELA, A.TIME_RELA, 
			A.SCOPE_CODE, A.PARA_VALUE_NAME, A.DATA_TYPE, A.ORGAN_ID FROM DEV_CLM.T_UDMP_PARA_DEF A WHERE 1 = 1  ]]>
		<include refid="claimQueryParaDefByParaNameCondition" />
		<![CDATA[ ORDER BY A.PARA_ID ]]>
	</select>
	<select id="queryOverdueDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT A.CASE_NO,
                A.ACTUAL_PAY,
                COP.OVER_PAY_REASON,
                COP.OVER_PAY_DAYS,
                COP.OVER_PAY_MONEY,
                COP.OVER_PAY_FLAG,
                COP.OVER_PAY_PERSON,
                COP.OVER_PAY_DATE,
                A.APPROVE_TIME,
                C.PAY_MODE,
                (SELECT M.NAME
                   FROM APP___CLM__DBUSER.T_PAY_MODE M
                  WHERE M.CODE = C.PAY_MODE) PAY_TYPE,
                C.PAYEE_NAME,
                D.List_id AS ARAP_LIST_ID ,
                D.FINISH_TIME
	  FROM APP___CLM__DBUSER.T_CLAIM_CASE  A,
	       DEV_CLM.T_CLAIM_BUSI_PROD       B,
	       APP___CLM__DBUSER.T_CLAIM_PAYEE C,
	       APP___CLM__DBUSER.T_PREM_ARAP   D,
	       APP___CLM__DBUSER.T_CLAIM_OVERCOMP_PAY COP 
	 WHERE 1 = 1
	   AND A.CASE_ID = B.CASE_ID
	   AND B.CASE_ID = C.CASE_ID
	   AND A.CASE_NO=D.BUSINESS_CODE
	   AND C.PAYEE_NO = D.CUSTOMER_ID
	   AND B.BUSI_PROD_CODE=D.BUSI_PROD_CODE
	   AND B.POLICY_CODE=D.POLICY_CODE
	   AND D.FINISH_TIME IS NOT NULL
	   AND A.CASE_APPLY_TYPE = 1 
	   AND D.ARAP_FLAG = 2
	   AND D.FEE_TYPE != 'P005110000'
	   AND COP.OVER_PAY_FLAG = 1 
	   AND COP.ARAP_LIST_ID = D.LIST_ID
       AND COP.CASE_ID = #{case_id, jdbcType=VARCHAR} ]]>
	</select>
	
	<select id="findAllClaimEmrByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.EMR_ID,A.CASE_ID,A.SERIAL_NUM,A.MEDICAL_NUM
		,A.HOSPITAL_CODE,A.IN_HOSPITAL_NUM,A.CHEIF_COMPLAINT
		,A.HISTORY_PRESENT_ILLNESS,A.PAST_DISEASE_HISTORY,A.PERSONAL_HISTORY
		,A.OBSTETRICAL_HISTORY,A.MENSTRUATION_HISTORY,A.FAMILY_HISTORY
		,A.PHYSICAL_EXAMINATION,A.AUXILIARY_EXAMINATION,A.JUNIOR_COLLEGE
		,A.MEDICAL_ABSTRACT,A.DIAGNOSIS_TREATMENT,A.DISCHARGE_STATUS
		,A.DISCHARGE_ORDER,A.TREAT_MENTINFO,A.INSERT_BY,A.INSERT_TIME
		,A.INSERT_TIMESTAMP,A.UPDATE_BY,A.UPDATE_TIME,A.UPDATE_TIMESTAMP
		,A.MEDICAL_TYPE,A.PAST_HIST_DESC,A.INFECTION_ILLNESS,A.PAST_OPERATION
		,A.TRANSFUSION_ILLNESS,A.ALLERGIC_ILLNESS,A.VACCINE_ILLNESS,A.PLAN_OF_TRMT
		,A.CLINICAL_PATHWAYS,A.LEAVE_STATE,A.VITAL_SIGNS,A.REMARK,A.ADMISSION_DATE 
          FROM DEV_CLM.T_CLAIM_EMR A WHERE A.CASE_ID = #{case_id} ]]>
		<![CDATA[ ORDER BY A.CASE_ID ]]>
	</select>
	
	
	      <!-- 查询客户风险事件广播（理赔）信息-->
	<select id="queryIhiClmbroCustomer" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.CLMBRO_ID, A.SCENE_NAME, A.LABEL_NAME, A.DST_NAME, A.DST_CERT_TYPE, 
      A.DST_CERTI_CODE, A.APPKEY, A.POLICY_CODE,A.CUSTOMER_NAME,A.CUSTOMER_CERT_TYPE,
      A.CUSTOMER_CERTI_CODE,A.CUSTOMER_ID,A.INFO_SOURCE,A.RECEIVE_TIME
       FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER A 
       INNER JOIN (SELECT DST_NAME,DST_CERT_TYPE,DST_CERTI_CODE FROM DEV_CLM.T_IHI_CLMBRO_CUSTOMER 
       WHERE CUSTOMER_ID = #{customer_id} ) B
       ON B.DST_NAME=A.DST_NAME AND B.DST_CERT_TYPE=A.DST_CERT_TYPE AND B.DST_CERTI_CODE=A.DST_CERTI_CODE
       WHERE ROWNUM <=  1000
			]]>
		<![CDATA[ ORDER BY A.SCENE_NAME,LABEL_NAME ]]> 
	</select>
	
	<!-- hankai 查询理赔给付责任理算信息 -->
    <select id="findClaimLiabByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT * FROM DEV_CLM.T_CLAIM_LIAB A WHERE A.LIAB_CONCLUSION != '6' AND A.CASE_ID = #{case_id}  ]]>
    </select>
    	
	<select id="findYJXParaDefByParaName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEPT_ID, A.PARA_ID, A.MODULE_ID, A.SYSTEM_ID, A.SINGLE_PARA_VALUE, A.END_DATE, A.PARA_VALUE, 
			A.DEPT_RELA, A.PARA_NAME, A.PARA_DESC, A.SYSTEM_ADMIN, A.START_DATE, A.ORGAN_RELA, A.TIME_RELA, 
			A.SCOPE_CODE, A.PARA_VALUE_NAME, A.DATA_TYPE, A.ORGAN_ID FROM DEV_CLM.T_UDMP_PARA_DEF A WHERE 1 = 1 AND A.PARA_NAME = #{para_name} ]]>
		<![CDATA[ ORDER BY A.PARA_ID ]]>
	</select>
</mapper>
