<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwPenoticeDaoImpl">
<!--
	<sql id="penoticeWhereCondition">
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" quality_evaluation  != null "><![CDATA[ AND A.QUALITY_EVALUATION = #{quality_evaluation} ]]></if>
		<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
		<if test=" pe_reason != null and pe_reason != ''  "><![CDATA[ AND A.PE_REASON = #{pe_reason} ]]></if>
		<if test=" result_magnum_msg != null and result_magnum_msg != ''  "><![CDATA[ AND A.RESULT_MAGNUM_MSG = #{result_magnum_msg} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" result_age  != null "><![CDATA[ AND A.RESULT_AGE = #{result_age} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" role_type  != null "><![CDATA[ AND A.ROLE_TYPE = #{role_type} ]]></if>
		<if test=" result_cus_score != null and result_cus_score != ''  "><![CDATA[ AND A.RESULT_CUS_SCORE = #{result_cus_score} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" customer_birth  != null  and  customer_birth  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTH = #{customer_birth} ]]></if>
		<if test=" other_comments != null and other_comments != ''  "><![CDATA[ AND A.OTHER_COMMENTS = #{other_comments} ]]></if>
		<if test=" positive_indi  != null "><![CDATA[ AND A.POSITIVE_INDI = #{positive_indi} ]]></if>
		<if test=" result_pe_date  != null  and  result_pe_date  != ''  "><![CDATA[ AND A.RESULT_PE_DATE = #{result_pe_date} ]]></if>
		<if test=" uw_hospital_id  != null "><![CDATA[ AND A.UW_HOSPITAL_ID = #{uw_hospital_id} ]]></if>
		<if test=" result_tel != null and result_tel != ''  "><![CDATA[ AND A.RESULT_TEL = #{result_tel} ]]></if>
		<if test=" result_magnum_value != null and result_magnum_value != ''  "><![CDATA[ AND A.RESULT_MAGNUM_VALUE = #{result_magnum_value} ]]></if>
		<if test=" quality_comment != null and quality_comment != ''  "><![CDATA[ AND A.QUALITY_COMMENT = #{quality_comment} ]]></if>
		<if test=" result_comment != null and result_comment != ''  "><![CDATA[ AND A.RESULT_COMMENT = #{result_comment} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" gender_code  != null "><![CDATA[ AND A.GENDER_CODE = #{gender_code} ]]></if>
		<if test=" es_indi  != null "><![CDATA[ AND A.ES_INDI = #{es_indi} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryPenoticeByPenoticeIdCondition">
		<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
	</sql>
	<sql id="queryPenoticeByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>	
	            <sql id="queryPenoticeByDocListIdCondition">
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	</sql>	
					<sql id="UW_queryPenoticeByCustomerIdCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	<sql id="UW_queryPenoticeByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="UW_queryPenoticeByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>
	<sql id="UW_queryPenoticeByDocListIdCondition">
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	</sql>
	
		<sql id="UW_queryPenoticeByPolicyCodeAndApplyCodeCondition">
		<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>	
	
	<select id="UW_findPenoticeByCustomerId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DOC_LIST_ID, A.CUSTOMER_NAME, A.UW_ID, A.PE_REASON, A.PENOTICE_ID, A.RESULT_MAGNUM_MSG, A.CUSTOMER_ID, 
			A.RESULT_AGE, A.ROLE_TYPE, A.RESULT_PE_HOSPITAL, A.RESULT_CUS_SCORE, A.POLICY_ID, 
			A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.RESULT_TEL, 
			A.QUALITY_MARK, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.ES_INDI, 
			A.CUSTOMER_GENDER FROM T_PENOTICE                    A WHERE 1 = 1  ]]>
		<include refid="UW_queryPenoticeByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.PENOTICE_ID                ]]>
	</select>
	<!-- 根据penoticeID查询体检基本信息 -->
	<select id="UW_findExamineMessageByPenoticeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DOC_LIST_ID, A.CUSTOMER_NAME, A.UW_ID, A.PE_REASON, A.PENOTICE_ID, A.RESULT_MAGNUM_MSG, A.CUSTOMER_ID, 
			A.RESULT_AGE, A.ROLE_TYPE, A.UW_HOSPITAL_ID, A.RESULT_CUS_SCORE, A.POLICY_ID, 
			A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.RESULT_TEL, 
			A.QUALITY_EVALUATION, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.ES_INDI, 
			A.GENDER_CODE FROM  DEV_UW.T_PENOTICE   A  WHERE 1=1 ]]>
<!-- 			<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if> -->
			<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
			<if test=" penotice_id  != null "><![CDATA[ AND A.PENOTICE_ID = #{penotice_id} ]]></if>
		<![CDATA[ ORDER BY A.PENOTICE_ID               ]]> 
	</select>
 	<select id="findPenoticeByDocListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.APPLY_CODE,A.DOC_LIST_ID, A.CUSTOMER_NAME, A.UW_ID, A.PE_REASON, A.PENOTICE_ID, A.RESULT_MAGNUM_MSG, A.CUSTOMER_ID, 
      A.RESULT_AGE, A.ROLE_TYPE,  A.RESULT_CUS_SCORE, A.POLICY_ID,  A.QUALITY_EVALUATION, A.ES_INDI,  A.UW_HOSPITAL_ID,
      A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.RESULT_TEL, 
     A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT,A.GENDER_CODE,A.POLICY_CODE,a.is_change_flag,
      case when a.role_type = '1' then (select case when trim(job_code) is null then '' else  (select job_name from t_job_code where JOB_CODE = h.job_code)
          	  							end job_name from t_policy_holder h where customer_id=a.customer_id and uw_id = a.uw_id and rownum =1)
          when a.role_type = '2' then( select case when trim(job_code) is null then '' else (select job_name from t_job_code where JOB_CODE = s.job_code) end job_name
									   from T_INSURED_LIST s where customer_id=a.customer_id and uw_id = a.uw_id and rownum =1) else '' end job_name
	 FROM T_PENOTICE   A WHERE 1 = 1   
     ]]>
		<include refid="UW_queryPenoticeByDocListIdCondition" />
		<include refid="UW_queryPenoticeByUwIdCondition" />  <!-- Modify by xuhp 契约、保全提交核保结论时校验体检通知书是否被修改 2015年12月10日 -->
		<include refid="UW_queryPenoticeByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.PENOTICE_ID                ]]>
	</select>
	<select id="findPenoticeByCSDocListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  A.APPLY_CODE,A.DOC_LIST_ID, A.CUSTOMER_NAME, A.UW_ID, A.PE_REASON, A.PENOTICE_ID, A.RESULT_MAGNUM_MSG, A.CUSTOMER_ID, 
      A.RESULT_AGE, A.ROLE_TYPE,  A.RESULT_CUS_SCORE, A.POLICY_ID,  A.QUALITY_EVALUATION, A.ES_INDI,  A.UW_HOSPITAL_ID,
      A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.RESULT_TEL, 
     A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT,A.GENDER_CODE,A.POLICY_CODE,a.is_change_flag,
     case when a.role_type = '1' then (select case when trim(job_code) is null then '' else  (select job_name from t_job_code where JOB_CODE = h.job_code)
          	  							end job_name from t_policy_holder h where customer_id=a.customer_id and uw_id = a.uw_id and rownum =1)
          when a.role_type = '2' then( select case when trim(job_code) is null then '' else (select job_name from t_job_code where JOB_CODE = s.job_code) end job_name
									   from T_INSURED_LIST s where customer_id=a.customer_id and uw_id = a.uw_id and rownum =1) else '' end job_name FROM T_PENOTICE A where  1 = 1
     ]]>
		<include refid="UW_queryPenoticeByDocListIdCondition" />
		<include refid="UW_queryPenoticeByUwIdCondition" />
		<include refid="UW_queryPenoticeByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.PENOTICE_ID                ]]>
	</select>  
	<delete id="UW_deletePenoticeByDocListId" parameterType="java.util.Map">
		<![CDATA[DELETE FROM T_PENOTICE A WHERE A.DOC_LIST_ID = #{document_no}]]>
	</delete>
	
	<select id="UW_findPenoticeByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DOC_LIST_ID, A.CUSTOMER_NAME, A.UW_ID, A.PE_REASON, A.PENOTICE_ID, A.RESULT_MAGNUM_MSG, A.CUSTOMER_ID, 
			A.RESULT_AGE, A.ROLE_TYPE, A.RESULT_PE_HOSPITAL, A.RESULT_CUS_SCORE, A.POLICY_ID, 
			A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.RESULT_TEL, 
			A.QUALITY_MARK, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.ES_INDI, 
			A.CUSTOMER_GENDER FROM T_PENOTICE                    A WHERE 1 = 1  ]]>
		<include refid="UW_queryPenoticeByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.PENOTICE_ID                ]]>
	</select>
	
	<select id="UW_findPenoticeByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DOC_LIST_ID, A.CUSTOMER_NAME, A.UW_ID, A.PE_REASON, A.PENOTICE_ID, A.RESULT_MAGNUM_MSG, A.CUSTOMER_ID, 
			A.RESULT_AGE, A.ROLE_TYPE, A.RESULT_PE_HOSPITAL, A.RESULT_CUS_SCORE, A.POLICY_ID, 
			A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.RESULT_TEL, 
			A.QUALITY_MARK, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.ES_INDI, 
			A.CUSTOMER_GENDER FROM T_PENOTICE                    A WHERE 1 = 1  ]]>
		<include refid="UW_queryPenoticeByUwIdCondition" />
		<![CDATA[ ORDER BY A.PENOTICE_ID                ]]>
	</select>	
<!-- 添加操作 -->
	<insert id="addPenotice"  useGeneratedKeys="true"  parameterType="java.util.Map">
	<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="penotice_id">
			SELECT  S_T_PENOTICE.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO T_PENOTICE(
				CUSTOMER_NAME, DOC_LIST_ID, UW_ID, QUALITY_EVALUATION, PENOTICE_ID, PE_REASON, RESULT_MAGNUM_MSG, 
				CUSTOMER_ID, RESULT_AGE, APPLY_CODE, ROLE_TYPE, INSERT_TIMESTAMP, UPDATE_BY, RESULT_CUS_SCORE, 
				POLICY_ID, CUSTOMER_BIRTH, OTHER_COMMENTS, POSITIVE_INDI, RESULT_PE_DATE, UW_HOSPITAL_ID, INSERT_TIME, 
				RESULT_TEL, UPDATE_TIME, RESULT_MAGNUM_VALUE, QUALITY_COMMENT, RESULT_COMMENT, POLICY_CODE, GENDER_CODE, 
				ES_INDI, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				#{customer_name, jdbcType=VARCHAR}, #{doc_list_id, jdbcType=NUMERIC} , #{uw_id, jdbcType=NUMERIC} , #{quality_evaluation, jdbcType=NUMERIC} , #{penotice_id, jdbcType=NUMERIC} , #{pe_reason, jdbcType=VARCHAR} , #{result_magnum_msg, jdbcType=VARCHAR} 
				, #{customer_id, jdbcType=NUMERIC} , #{result_age, jdbcType=NUMERIC} , #{apply_code, jdbcType=VARCHAR} , #{role_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{result_cus_score, jdbcType=VARCHAR} 
				, #{policy_id, jdbcType=NUMERIC} , #{customer_birth, jdbcType=DATE} , #{other_comments, jdbcType=VARCHAR} , #{positive_indi, jdbcType=NUMERIC} , #{result_pe_date, jdbcType=DATE} , #{uw_hospital_id, jdbcType=NUMERIC} , SYSDATE 
				, #{result_tel, jdbcType=VARCHAR} , SYSDATE , #{result_magnum_value, jdbcType=VARCHAR} , #{quality_comment, jdbcType=VARCHAR} , #{result_comment, jdbcType=VARCHAR} , #{policy_code, jdbcType=VARCHAR} , #{gender_code, jdbcType=NUMERIC} 
				, #{es_indi, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePenotice" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_PENOTICE WHERE PENOTICE_ID = #{penotice_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updatePenotice" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_PENOTICE ]]>
		<set>
		<trim suffixOverrides=",">
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
			DOC_LIST_ID = #{doc_list_id, jdbcType=NUMERIC} ,
		    UW_ID = #{uw_id, jdbcType=NUMERIC} ,
		    QUALITY_EVALUATION = #{quality_evaluation, jdbcType=NUMERIC} ,
			PE_REASON = #{pe_reason, jdbcType=VARCHAR} ,
			RESULT_MAGNUM_MSG = #{result_magnum_msg, jdbcType=VARCHAR} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    RESULT_AGE = #{result_age, jdbcType=NUMERIC} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    ROLE_TYPE = #{role_type, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			RESULT_CUS_SCORE = #{result_cus_score, jdbcType=VARCHAR} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    CUSTOMER_BIRTH = #{customer_birth, jdbcType=DATE} ,
			OTHER_COMMENTS = #{other_comments, jdbcType=VARCHAR} ,
		    POSITIVE_INDI = #{positive_indi, jdbcType=NUMERIC} ,
		    RESULT_PE_DATE = #{result_pe_date, jdbcType=DATE} ,
		    UW_HOSPITAL_ID = #{uw_hospital_id, jdbcType=NUMERIC} ,
			RESULT_TEL = #{result_tel, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			RESULT_MAGNUM_VALUE = #{result_magnum_value, jdbcType=VARCHAR} ,
			QUALITY_COMMENT = #{quality_comment, jdbcType=VARCHAR} ,
			RESULT_COMMENT = #{result_comment, jdbcType=VARCHAR} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    GENDER_CODE = #{gender_code, jdbcType=NUMERIC} ,
		    ES_INDI = #{es_indi, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    IS_CHANGE_FLAG = #{is_change_flag, jdbcType=NUMERIC},
		</trim>
		</set>
		<![CDATA[ WHERE PENOTICE_ID = #{penotice_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPenoticeByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.QUALITY_EVALUATION, A.PENOTICE_ID, A.PE_REASON, A.RESULT_MAGNUM_MSG, 
			A.CUSTOMER_ID, A.RESULT_AGE, A.APPLY_CODE, A.ROLE_TYPE, A.RESULT_CUS_SCORE, 
			A.POLICY_ID, A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.UW_HOSPITAL_ID, 
			A.RESULT_TEL, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.POLICY_CODE, A.GENDER_CODE, 
			A.ES_INDI,A.IS_CHANGE_FLAG FROM T_PENOTICE A WHERE 1 = 1  ]]>
		<include refid="queryPenoticeByUwIdCondition" />
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<![CDATA[ ORDER BY A.PENOTICE_ID ]]>
	</select>
	<!-- 按索引查询操作 -->	
	<select id="findPenoticeByUwId2" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.QUALITY_EVALUATION, A.PENOTICE_ID, A.PE_REASON, A.RESULT_MAGNUM_MSG, 
			A.CUSTOMER_ID, A.RESULT_AGE, A.APPLY_CODE, A.ROLE_TYPE, A.RESULT_CUS_SCORE, 
			A.POLICY_ID, A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.UW_HOSPITAL_ID, 
			A.RESULT_TEL, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.POLICY_CODE, A.GENDER_CODE, 
			A.ES_INDI,a.is_change_flag,(select case when trim(l.job_code) is  null then ''
      else (select job_name from dev_uw.T_JOB_CODE where job_code = l.job_code ) end job_name 
      from dev_uw.T_INSURED_LIST l where l.uw_id =a.uw_id and l.apply_code = a.apply_code and l.customer_id = a.customer_id ) job_name FROM dev_uw.T_PENOTICE A WHERE 1 = 1  ]]>
		<include refid="queryPenoticeByUwIdCondition" />
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<![CDATA[ ORDER BY A.PENOTICE_ID ]]>
	</select>

<!-- 按map查询操作 -->
	<select id="findAllMapPenotice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.QUALITY_EVALUATION, A.PENOTICE_ID, A.PE_REASON, A.RESULT_MAGNUM_MSG, 
			A.CUSTOMER_ID, A.RESULT_AGE, A.APPLY_CODE, A.ROLE_TYPE, A.RESULT_CUS_SCORE, 
			A.POLICY_ID, A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.UW_HOSPITAL_ID, 
			A.RESULT_TEL, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.POLICY_CODE, A.GENDER_CODE, 
			A.ES_INDI FROM T_PENOTICE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.PENOTICE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPenotice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.QUALITY_EVALUATION, A.PENOTICE_ID, A.PE_REASON, A.RESULT_MAGNUM_MSG, 
			A.CUSTOMER_ID, A.RESULT_AGE, A.APPLY_CODE, A.ROLE_TYPE, A.RESULT_CUS_SCORE, 
			A.POLICY_ID, A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.UW_HOSPITAL_ID, 
			A.RESULT_TEL, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.POLICY_CODE, A.GENDER_CODE, 
			A.ES_INDI FROM T_PENOTICE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.PENOTICE_ID ]]> 
	</select>


<!-- 分页查询操作 -->
	<select id="UW_queryPenoticeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CUSTOMER_NAME, B.DOC_LIST_ID, B.UW_ID, B.QUALITY_EVALUATION, B.PENOTICE_ID, B.PE_REASON, B.RESULT_MAGNUM_MSG, 
			B.CUSTOMER_ID, B.RESULT_AGE, B.APPLY_CODE, B.ROLE_TYPE, B.RESULT_CUS_SCORE, 
			B.POLICY_ID, B.CUSTOMER_BIRTH, B.OTHER_COMMENTS, B.POSITIVE_INDI, B.RESULT_PE_DATE, B.UW_HOSPITAL_ID, 
			B.RESULT_TEL, B.RESULT_MAGNUM_VALUE, B.QUALITY_COMMENT, B.RESULT_COMMENT, B.POLICY_CODE, B.GENDER_CODE, 
			B.ES_INDI,b.INSERT_TIME,b.INSERT_BY,b.UPDATE_TIME ,b.UPDATE_BY ,b.IS_CHANGE_FLAG FROM (
					SELECT ROWNUM RN, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.QUALITY_EVALUATION, A.PENOTICE_ID, A.PE_REASON, A.RESULT_MAGNUM_MSG, 
			A.CUSTOMER_ID, A.RESULT_AGE, A.APPLY_CODE, A.ROLE_TYPE, A.RESULT_CUS_SCORE, 
			A.POLICY_ID, A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.UW_HOSPITAL_ID, 
			A.RESULT_TEL, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.POLICY_CODE, A.GENDER_CODE, 
			A.ES_INDI,a.INSERT_TIME,a.INSERT_BY,a.UPDATE_TIME ,a.UPDATE_BY ,a.IS_CHANGE_FLAG FROM T_PENOTICE A left join T_UW_POLICY c on a.APPLY_CODE =c.APPLY_CODE and a.uw_id=c.uw_id  WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ and  A.UW_ID not in (select z.uw_id from t_uw_policy z where z.uw_status ='11' and z.uw_id = a.uw_id) and a.result_pe_date is not null ]]>
		<if test=" doc_list_id  != null "><![CDATA[ AND 

A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	<if test=" customer_name  != null and customer_name  

			!='' "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
	<if test=" apply_code  != null and apply_code != 

	''"><![CDATA[ and exists (select 1 from t_contract_master where 

			apply_code =#{apply_code} and uw_id = a.uw_id ) and a.apply_code =#{apply_code} ]]></if>
	<if test=" penotice_id != null "><![CDATA[ and 

			a.penotice_id = #{penotice_id}]]></if>
		<![CDATA[ ORDER BY A.INSERT_TIME desc ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 体检结果查询,体检结果修改界面的查询  add by guanshun -->
	<select id="UW_findPenoticeByCondition" resultType="java.util.Map"

	parameterType="java.util.Map">
		<![CDATA[
		
		select a.DOC_LIST_ID ,a.CUSTOMER_NAME , 

a.RESULT_PE_DATE, a.INSERT_TIME , a.INSERT_BY ,
		 a.POSITIVE_INDI , a.PENOTICE_ID , a.UPDATE_TIME from T_PENOTICE a 

where 1=1 and not exists (select 1 from t_uw_policy b where 

b.uw_status = '04' and b.uw_id = a.uw_id) and a.result_pe_date is not null
		]]>
	<if test=" doc_list_id  != null "><![CDATA[ AND 

A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	<if test=" customer_name  != null and customer_name  

			!='' "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
	<if test=" apply_code  != null and apply_code != 

	''"><![CDATA[ and exists (select 1 from t_contract_master where 

			apply_code =#{apply_code} and uw_id = a.uw_id ) ]]></if>
	<if test=" penotice_id != null "><![CDATA[ and 

			a.penotice_id = #{penotice_id}]]></if>
			<![CDATA[ ORDER BY A.INSERT_TIME desc]]>
</select>
	<update id="updatePenoticePositiveIndi" 

		parameterType="java.util.Map">
		<![CDATA[ UPDATE T_PENOTICE ]]>
		<set>
		<trim suffixOverrides=",">
		    POSITIVE_INDI = #{positive_indi, jdbcType=NUMERIC} 
		</trim>
		</set>
		<![CDATA[ WHERE PENOTICE_ID = #{penotice_id} ]]>
	</update>
	<select id="findUwPolicyVOByUwId" resultType="java.util.Map" 

			parameterType="java.util.Map">
		<![CDATA[
			select  a.UW_STATUS from T_UW_POLICY a where 

			a.UW_ID = #{uw_id} and rownum=1
		]]>
	
	</select>
	<!-- 更新部分字段操作   add by youyuan-->
	<update id="UW_updatePenotice_part" parameterType="java.util.Map">
		<![CDATA[ UPDATE  T_PENOTICE                  ]]>
		<set>
		<trim suffixOverrides=",">
		   RESULT_TEL = #{result_tel, jdbcType=VARCHAR} ,
		   PENOTICE_ID = #{penotice_id, jdbcType=NUMERIC} ,
		   UW_HOSPITAL_ID = #{uw_hospital_id, jdbcType=NUMERIC} ,
		   RESULT_PE_DATE = #{result_pe_date, jdbcType=DATE} ,
		   RESULT_AGE = #{result_age, jdbcType=VARCHAR} ,
		   QUALITY_COMMENT = #{quality_comment, jdbcType=VARCHAR} ,
		   QUALITY_EVALUATION = #{quality_evaluation, jdbcType=NUMERIC} ,
		   POSITIVE_INDI = #{positive_indi, jdbcType=NUMERIC} ,
		   RESULT_CUS_SCORE = #{result_cus_score, jdbcType=VARCHAR} ,
		   RESULT_COMMENT = #{result_comment, jdbcType=VARCHAR} ,
		   UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		   UPDATE_TIME = SYSDATE , 
		   UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE PENOTICE_ID               = #{penotice_id              } ]]>
	</update>
	<select id="Uw_penoticeByDocumentNoS" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.QUALITY_EVALUATION, A.PENOTICE_ID, A.PE_REASON, A.RESULT_MAGNUM_MSG, 
			A.CUSTOMER_ID, A.RESULT_AGE, A.APPLY_CODE, A.ROLE_TYPE, A.RESULT_CUS_SCORE, 
			A.POLICY_ID, A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.UW_HOSPITAL_ID, 
			A.RESULT_TEL, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.POLICY_CODE, A.GENDER_CODE, 
			A.ES_INDI FROM DEV_UW.T_PENOTICE A WHERE ROWNUM <=  1000  ]]>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<![CDATA[ ORDER BY A.PENOTICE_ID ]]> 
	</select>
	<select id="Uw_PolicyByUwidAndPolicyCodess" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.UW_ID, A.DISCNTED_PREM_AF, A.AUTO_RENEWAL_INDI, A.UW_BUSI_ID, A.COVERAGE_PERIOD, A.ITEM_ID, 
			A.UW_PRD_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POSTPONED_MONTHS, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.PRE_DOC_UWDECISION, A.BUSI_ITEM_ID, A.POLICY_ID, A.UNIT, 
			A.COVERAGE_YEAR, A.UW_DECISION_STATUS, A.DELETE_INDI, A.QUALITY_INDI,A.DECISION_INDI, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.CHARGE_PERIOD, A.STD_PREM_AF, A.POLICY_CODE, 
			A.DISCNT_PREM_AF FROM T_UW_PRODUCT A WHERE 1 = 1  ]]>
			<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = ${uw_id}  ]]></if>
			<if test=" policy_code  != null "><![CDATA[ AND A.POLICY_CODE = ${policy_code}  ]]></if>
		<![CDATA[ ORDER BY A.UW_ID            ]]>
	</select>
	
	<select id="Uw_bussinessProductByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.MASTER_BUSI_ITEM_ID, A.UW_BUSI_ID, A.INSURED_1, A.INSURED_2, 
			A.UW_STATUS, A.DECISION_CODE, A.BUSI_PROD_CODE, A.APPLY_CODE, A.ORGAN_CODE, A.POLICY_CODE, 
			A.BUSI_ITEM_ID, A.POLICY_ID FROM dev_uw.T_UW_BUSI_PROD A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<if test=" busi_item_id != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<![CDATA[ ORDER BY A.UW_BUSI_ID ]]> 
	</select>
	
	<!-- 根据policycode和applycode 查询体检信息  -->
	<select id="UW_findPenoticeByPolicyCodeAndApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.QUALITY_EVALUATION, A.PENOTICE_ID, A.PE_REASON, A.RESULT_MAGNUM_MSG, 
			A.CUSTOMER_ID, A.RESULT_AGE, A.APPLY_CODE, A.ROLE_TYPE, A.RESULT_CUS_SCORE, 
			A.POLICY_ID, A.CUSTOMER_BIRTH, A.OTHER_COMMENTS, A.POSITIVE_INDI, A.RESULT_PE_DATE, A.UW_HOSPITAL_ID, 
			A.RESULT_TEL, A.RESULT_MAGNUM_VALUE, A.QUALITY_COMMENT, A.RESULT_COMMENT, A.POLICY_CODE, A.GENDER_CODE, 
			A.ES_INDI FROM T_PENOTICE    A WHERE 1 = 1  ]]>
		<include refid="UW_queryPenoticeByPolicyCodeAndApplyCodeCondition" /> 
		<![CDATA[ order by A.customer_id,A.RESULT_PE_DATE desc          ]]>
	</select>

</mapper>
