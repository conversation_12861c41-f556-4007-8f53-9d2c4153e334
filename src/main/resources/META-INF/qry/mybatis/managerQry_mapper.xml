<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.ManagerQryDaoImpl">
    <!-- 查询个数操作 -->
    <select id="managerQryTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[   
        
         SELECT
                COUNT(1)
            FROM
                DEV_PAS.T_CONTRACT_MASTER CM
            LEFT JOIN 
                  DEV_PAS.T_POLICY_HOLDER PH
            ON 
                  CM.POLICY_ID = PH.POLICY_ID
            LEFT JOIN
                  DEV_PAS.T_CUSTOMER C
            ON 
                  C.CUSTOMER_ID = PH.CUSTOMER_ID
            LEFT JOIN 
              (SELECT IL.*
                FROM DEV_PAS.T_INSURED_LIST IL
                INNER JOIN DEV_PAS.T_BENEFIT_INSURED BI
                ON IL.LIST_ID = BI.INSURED_ID
                WHERE  BI.ORDER_ID=0) IL
            ON 
                IL.POLICY_ID=CM.POLICY_ID AND C.CUSTOMER_ID=IL.CUSTOMER_ID
            WHERE 1 = 1
       ]]>
        <!-- <include refid="请添加查询条件" /> -->
         <if test=" APPLY_DATE  != null"><![CDATA[ AND CM.APPLY_DATE >= #{APPLY_DATE} ]]></if>
        <if test=" APPLY_DATE1  != null"><![CDATA[ AND CM.APPLY_DATE <= #{APPLY_DATE1} ]]></if>
        <if test=" ISSUE_DATE  != null"><![CDATA[ AND CM.ISSUE_DATE >= #{ISSUE_DATE} ]]></if>
        <if test=" ISSUE_DATE1  != null"><![CDATA[ AND CM.ISSUE_DATE <= #{ISSUE_DATE1} ]]></if>
    </select>

    <!-- 分页查询操作 -->
    <select id="managerQryPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT  B.* FROM (
            SELECT
            ROWNUM RN,
                CM.POLICY_CODE, 
                C.CUSTOMER_NAME AS HOLDER_CUSTOMER_NAME,
                (SELECT
                C.CUSTOMER_NAME
                FROM DEV_PAS.T_INSURED_LIST IL
                INNER JOIN DEV_PAS.T_BENEFIT_INSURED BI
                ON IL.LIST_ID = BI.INSURED_ID
                INNER JOIN  DEV_PAS.T_CUSTOMER C
                ON C.CUSTOMER_ID=IL.CUSTOMER_ID
                WHERE  BI.ORDER_ID=1 AND IL.POLICY_ID=CM.POLICY_ID AND ROWNUM=1)  AS INSURED_CUSTOMER_NAME,
                CM.APPLY_DATE, 
                CM.VALIDATE_DATE
            FROM
                DEV_PAS.T_CONTRACT_MASTER CM
            LEFT JOIN 
                  DEV_PAS.T_POLICY_HOLDER PH
            ON 
                  CM.POLICY_ID = PH.POLICY_ID
            LEFT JOIN
                  DEV_PAS.T_CUSTOMER C
            ON 
                  C.CUSTOMER_ID = PH.CUSTOMER_ID
            LEFT JOIN 
              (SELECT IL.*
                FROM DEV_PAS.T_INSURED_LIST IL
                INNER JOIN DEV_PAS.T_BENEFIT_INSURED BI
                ON IL.LIST_ID = BI.INSURED_ID
                WHERE  BI.ORDER_ID=0) IL
            ON 
                IL.POLICY_ID=CM.POLICY_ID AND C.CUSTOMER_ID=IL.CUSTOMER_ID
            WHERE 1 = 1
        ]]>
        <!-- <include refid="请添加查询条件" /> -->
        <if test=" APPLY_DATE  != null"><![CDATA[ AND CM.APPLY_DATE >= #{APPLY_DATE} ]]></if>
        <if test=" APPLY_DATE1  != null"><![CDATA[ AND CM.APPLY_DATE <= #{APPLY_DATE1} ]]></if>
        <if test=" ISSUE_DATE  != null"><![CDATA[ AND CM.ISSUE_DATE >= #{ISSUE_DATE} ]]></if>
        <if test=" ISSUE_DATE1  != null"><![CDATA[ AND CM.ISSUE_DATE <= #{ISSUE_DATE1} ]]></if>
        <![CDATA[ and ROWNUM <= #{LESS_NUM} ]]> 
        <![CDATA[ )  B
        WHERE B.RN > #{GREATER_NUM} ]]>
    </select>
    
     <!-- 查询要下载的数据 -->
    <select id="managerQryList" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
            SELECT
                CM.POLICY_CODE, 
                C.CUSTOMER_NAME AS HOLDER_CUSTOMER_NAME,
                (SELECT
                C.CUSTOMER_NAME
                FROM DEV_PAS.T_INSURED_LIST IL
                INNER JOIN DEV_PAS.T_BENEFIT_INSURED BI
                ON IL.LIST_ID = BI.INSURED_ID
                INNER JOIN  DEV_PAS.T_CUSTOMER C
                ON C.CUSTOMER_ID=IL.CUSTOMER_ID
                WHERE  BI.ORDER_ID=1 AND IL.POLICY_ID=CM.POLICY_ID AND ROWNUM=1)  AS INSURED_CUSTOMER_NAME,
                CM.APPLY_DATE, 
                CM.VALIDATE_DATE
            FROM
                DEV_PAS.T_CONTRACT_MASTER CM
            LEFT JOIN 
                  DEV_PAS.T_POLICY_HOLDER PH
            ON 
                  CM.POLICY_ID = PH.POLICY_ID
            LEFT JOIN
                  DEV_PAS.T_CUSTOMER C
            ON 
                  C.CUSTOMER_ID = PH.CUSTOMER_ID
            LEFT JOIN 
              (SELECT IL.*
                FROM DEV_PAS.T_INSURED_LIST IL
                INNER JOIN DEV_PAS.T_BENEFIT_INSURED BI
                ON IL.LIST_ID = BI.INSURED_ID
                WHERE  BI.ORDER_ID=0) IL
            ON 
                IL.POLICY_ID=CM.POLICY_ID AND C.CUSTOMER_ID=IL.CUSTOMER_ID
            WHERE 1 = 1
        ]]>
        <!-- <include refid="请添加查询条件" /> -->
        <if test=" APPLY_DATE  != null"><![CDATA[ AND CM.APPLY_DATE >= #{APPLY_DATE} ]]></if>
        <if test=" APPLY_DATE1  != null"><![CDATA[ AND CM.APPLY_DATE <= #{APPLY_DATE1} ]]></if>
        <if test=" ISSUE_DATE  != null"><![CDATA[ AND CM.ISSUE_DATE >= #{ISSUE_DATE} ]]></if>
        <if test=" ISSUE_DATE1  != null"><![CDATA[ AND CM.ISSUE_DATE <= #{ISSUE_DATE1} ]]></if>
      
       
    </select>
    
    
</mapper>
