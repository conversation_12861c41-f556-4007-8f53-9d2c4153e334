<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.nb.temp.mapper">

	<sql id="NB_nbJointlyInsuredPolicyWhereCondition">
		<if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" relation_to_main_insured != null and relation_to_main_insured != ''  "><![CDATA[ AND A.RELATION_TO_MAIN_INSURED = #{relation_to_main_insured} ]]></if>
		<if test=" main_insured_id  != null "><![CDATA[ AND A.MAIN_INSURED_ID = #{main_insured_id} ]]></if>
		<if test=" jointly_insured_list_id  != null "><![CDATA[ AND A.JOINTLY_INSURED_LIST_ID = #{jointly_insured_list_id} ]]></if>
		<if test=" main_apply_code != null and main_apply_code != ''  "><![CDATA[ AND A.MAIN_APPLY_CODE = #{main_apply_code} ]]></if>
		<if test=" product_code != null and product_code != ''  "><![CDATA[ AND A.PRODUCT_CODE = #{product_code} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
		<if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
	</sql>

	<!-- 根据applyCode查询对应的所有共同参保保单列表 -->
	<select id="NB_getAllJointlyInsuredPolicyByApplyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_CERT_TYPE,
				       A.CUSTOMER_NAME,
				       A.RELATION_TO_MAIN_INSURED,
				       A.MAIN_INSURED_ID,
				       A.JOINTLY_INSURED_LIST_ID,
				       A.MAIN_APPLY_CODE,
				       A.PRODUCT_CODE,
				       A.CUSTOMER_ID,
				       A.CUSTOMER_CERTI_CODE,
				       A.APPLY_CODE,
				       A.CUSTOMER_BIRTHDAY,
				       A.CUSTOMER_GENDER
				  FROM DEV_NB.T_NB_JOINTLY_INSURED_POLICY A
				 WHERE 1 = 1
				   AND A.MAIN_APPLY_CODE = #{apply_code}]]>
		<![CDATA[ ORDER BY A.JOINTLY_INSURED_LIST_ID ]]> 
	</select>
	
</mapper>
