<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UwNoticeDaoImpl">
<!--
	<sql id="uwNoticeWhereCondition">
		<if test=" reply_remark != null and reply_remark != ''  "><![CDATA[ AND A.REPLY_REMARK = #{reply_remark} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" uw_policy_id  != null "><![CDATA[ AND A.UW_POLICY_ID = #{uw_policy_id} ]]></if>
		<if test=" apply_date  != null  and  apply_date  != ''  "><![CDATA[ AND A.APPLY_DATE = #{apply_date} ]]></if>
		<if test=" reply_indi  != null "><![CDATA[ AND A.REPLY_INDI = #{reply_indi} ]]></if>
		<if test=" uw_submit_time  != null  and  uw_submit_time  != ''  "><![CDATA[ AND A.UW_SUBMIT_TIME = #{uw_submit_time} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" document_no != null and document_no != ''  "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<if test=" uw_user_code  != null "><![CDATA[ AND A.UW_USER_CODE = #{uw_user_code} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryUwNoticeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryUwNoticeByDocumentCondition">
		<if test=" document_no  != null "><![CDATA[ AND A.document_no = #{document_no} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.uw_id = #{uw_id} ]]></if>
		<if test=" apply_code  != null "><![CDATA[ AND A.apply_code  = #{apply_code } ]]></if>
		<if test=" reply_indi  != null "><![CDATA[ AND A.REPLY_INDI  = #{reply_indi } ]]></if>
		<if test=" uw_policy_id  != null "><![CDATA[ AND A.UW_POLICY_ID = #{uw_policy_id} ]]></if>
	</sql>
	
	 <!-- Modify by xuhp  添加操作条件 2015年11月7日  -->
    <sql id="queryUwNoticeByUwId">
		<if test=" uw_id  != null "><![CDATA[ AND A.uw_id = #{uw_id} ]]></if>
    </sql>
	

<!-- 按索引查询操作 -->	
	<select id="findUwNoticeByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REPLY_REMARK, A.UW_ID, A.UW_POLICY_ID, A.APPLY_DATE, A.REPLY_INDI, A.UW_SUBMIT_TIME, 
			A.APPLY_CODE, A.DOCUMENT_NO, A.UW_USER_CODE, A.LIST_ID FROM DEV_UW.T_UW_NOTICE A WHERE 1 = 1  ]]>
		<include refid="queryUwNoticeByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapUwNotice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REPLY_REMARK, A.UW_ID, A.UW_POLICY_ID, A.APPLY_DATE, A.REPLY_INDI, A.UW_SUBMIT_TIME, 
			A.APPLY_CODE, A.DOCUMENT_NO, A.UW_USER_CODE, A.LIST_ID FROM DEV_UW.T_UW_NOTICE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllUwNotice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REPLY_REMARK, A.UW_ID, A.UW_POLICY_ID, A.APPLY_DATE, A.REPLY_INDI, A.UW_SUBMIT_TIME, 
			A.APPLY_CODE, A.DOCUMENT_NO, A.UW_USER_CODE, A.LIST_ID,(select user_name from DEV_PAS.T_UDMP_USER where user_id =uw_user_code) user_name FROM DEV_UW.T_UW_NOTICE A WHERE ROWNUM <=  1000   
		]]>
		<include refid="queryUwNoticeByDocumentCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findUwNoticeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM DEV_UW.T_UW_NOTICE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="queryUwNoticeByDocumentCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryUwNoticeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.REPLY_REMARK, B.UW_ID, B.UW_POLICY_ID, B.APPLY_DATE, B.REPLY_INDI, B.UW_SUBMIT_TIME, 
			B.APPLY_CODE, B.DOCUMENT_NO, B.UW_USER_CODE, B.LIST_ID FROM (
					SELECT ROWNUM RN, A.REPLY_REMARK, A.UW_ID, A.UW_POLICY_ID, A.APPLY_DATE, A.REPLY_INDI, A.UW_SUBMIT_TIME, 
			A.APPLY_CODE, A.DOCUMENT_NO, A.UW_USER_CODE, A.LIST_ID FROM DEV_UW.T_UW_NOTICE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 根据通知书编号查询操作 -->
	<select id="findUwNotice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM (SELECT A.REPLY_REMARK, A.UW_ID, A.UW_POLICY_ID,  A.APPLY_DATE, A.REPLY_INDI,
               A.UW_SUBMIT_TIME, A.APPLY_CODE, A.DOCUMENT_NO, A.UW_USER_CODE, A.LIST_ID
          FROM DEV_UW.T_UW_NOTICE A WHERE 1 = 1]]>
           <include refid="queryUwNoticeByDocumentCondition" />
         <![CDATA[ ORDER BY A.INSERT_TIME DESC) WHERE ROWNUM = 1]]>
	</select>
	
	
	<!-- 根据通知书编号和投保单号查询操作 -->
	<select id="findUwNoticeByApplycode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select A.REPLY_REMARK, A.UW_ID, A.UW_POLICY_ID, A.APPLY_DATE, A.REPLY_INDI, A.UW_SUBMIT_TIME, 
			A.APPLY_CODE, A.DOCUMENT_NO, A.UW_USER_CODE, A.LIST_ID from DEV_UW.T_UW_NOTICE A where 1=1 ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="queryUwNoticeByDocumentCondition" />
		<![CDATA[ and rownum=1]]>
		<![CDATA[ ORDER BY A.INSERT_TIME desc ]]>
	</select>
	
	<!-- 修改核保决定通知书的状态 -->
	<update id="updateUwNoticeByDocumentNO" parameterType="java.util.Map">
		<![CDATA[ UPDATE DEV_UW.T_UW_NOTICE ]]>
		<set>
		<trim suffixOverrides=",">
		    <if test=" reply_indi  != null ">REPLY_INDI = #{reply_indi, jdbcType=NUMERIC} ,</if>
		    <if test=" status  != null ">STATUS = #{status, jdbcType=VARCHAR},</if>
		    <if test=" reply_remark != null and reply_remark != ''  ">REPLY_REMARK = #{reply_remark},</if>
		    <if test=" uw_submit_time  != null  and  uw_submit_time  != ''  ">UW_SUBMIT_TIME = #{uw_submit_time},</if>
		</trim>
		</set>
		<!-- <![CDATA[ WHERE DOCUMENT_NO = #{document_no} ]]> -->
		<![CDATA[ WHERE 1=1]]>
		<if test=" document_no  != null "><![CDATA[AND document_no = #{document_no} ]]></if>
		<if test=" uw_id  != null "><![CDATA[AND uw_id = #{uw_id} ]]></if>
	</update>
	
	<!--by zhaoyoan_wb 加费通知单号快速查询服务 -->
	<select id="queryIsExistAddPremDocNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			SELECT UN.LIST_ID FROM DEV_UW.T_UW_NOTICE UN 
			WHERE 1=1
			AND UN.DOCUMENT_NO = #{document_no} 
			AND EXISTS (SELECT 1 FROM DEV_UW.T_UW_EXTRA_PREM WHERE UW_ID = UN.UW_ID) 
		]]>
	</select>
</mapper>
