<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimCasePO">

	<!-- 网站在线自助报案客户校验接口  是否公司客户-->
	<select id="findIfCompanyCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		SELECT A.CUSTOMER_ID,
       A.UN_CUSTOMER_CODE,
       A.MARRIAGE_DATE,
       A.EDUCATION,
       A.CUSTOMER_NAME,
       A.CUSTOMER_BIRTHDAY,
       A.CUSTOMER_GENDER,
       A.CUSTOMER_HEIGHT,
       A.CUSTOMER_WEIGHT,
       A.CUSTOMER_CERT_TYPE,
       <PERSON><PERSON>C<PERSON>TOMER_CERTI_CODE,
       A<PERSON>CUSTOMER_ID_CODE,
       A<PERSON>CUST_CERT_STAR_DATE,
       A.CUST_CERT_END_DATE,
       A.JOB_CODE,
       A.JOB_NATURE,
       A.JOB_KIND,
       A.JOB_TITLE,
       A.MARRIAGE_STATUS,
       A.IS_PARENT,
       A.ANNUAL_INCOME,
       A.COUNTRY_CODE,
       A.RELIGION_CODE,
       A.NATION_CODE,
       A.DRIVER_LICENSE_TYPE,
       A.COMPANY_NAME,
       A.OFFEN_USE_TEL,
       A.HOUSE_TEL,
       A.FAX_TEL,
       A.OFFICE_TEL,
       A.MOBILE_TEL,
       A.EMAIL,
       A.QQ,
       A.WECHAT_NO,
       A.OTHER,
       A.CUSTOMER_LEVEL,
       A.CUSTOMER_VIP,
       A.SMOKING_FLAG,
       A.DRUNK_FLAG,
       A.BLACKLIST_FLAG,
       A.HOUSEKEEPER_FLAG,
       A.SYN_MDM_FLAG,
       A.LIVE_STATUS,
       A.RETIRED_FLAG,
       A.DEATH_DATE,
       A.HEALTH_STATUS,
       A.REMARK,
       A.CUST_PWD,
       A.INSERT_BY,
       A.INSERT_TIME,
       A.INSERT_TIMESTAMP,
       A.UPDATE_BY,
       A.UPDATE_TIME,
       A.UPDATE_TIMESTAMP,
       A.OLD_CUSTOMER_ID,
       A.CUSTOMER_RISK_LEVEL
      FROM DEV_PAS.T_CUSTOMER A
       WHERE 
		A.CUSTOMER_NAME=#{customer_name} AND 
		A.CUSTOMER_GENDER=#{customer_gender} AND 
		A.CUSTOMER_CERT_TYPE=#{customer_cert_type} AND 
		A.CUSTOMER_CERTI_CODE=#{customer_certi_code} 
		]]>
	</select>
	
	
		
	<!-- 赔案号快查服务 -->
	<select id="isExistsClainByCaseNo" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ SELECT count(1) FROM DEV_CLM.T_CLAIM_CASE A WHERE A.CASE_NO = #{case_no} ]]>
	</select>
	
	 <!-- 查询行业共享信息清单 -->
     <select id="queryTradeShareInfoList"  resultType="java.util.Map" parameterType="java.util.Map" >     
	        <![CDATA[ 
	        SELECT B.* FROM(
            SELECT C.*,ROWNUM RN
            FROM (
            
	        select distinct A.CASE_NO,
			( select i.CUSTOMER_NAME  from DEV_CLM.T_CUSTOMER i where i.CUSTOMER_ID=A.INSURED_ID) INSURED_NAME,
			(SELECT LISTAGG(CT.NAME,',') WITHIN GROUP(ORDER BY SUB.CASE_ID) FROM DEV_CLM.T_CLAIM_SUB_CASE SUB ,DEV_CLM.T_CLAIM_TYPE CT WHERE A.CASE_ID = SUB.CASE_ID AND SUB.CLAIM_TYPE = CT.CODE) CLAIM_TYPE,
			(select sum(SUM_AMOUNT) from  DEV_CLM.T_CLAIM_bill pp where pp.CASE_ID=A.CASE_ID) MEDICAL_TOTAL_MONY,
			C.PRODUCT_TYPE,
			LISTAGG( C.RISK_RESULT,',') WITHIN GROUP(ORDER BY C.RISK_RESULT) OVER (PARTITION BY C.CASE_ID,C.PRODUCT_TYPE) RISK_RESULT,
			(select Count(*)from DEV_CLM.T_SURVEY_APPLY u  where u.case_id=A.CASE_ID) surveyMark,
			(select MAX(h.POSITIVE_FLAG) from DEV_CLM.T_SURVEY_CONCLUSION h,DEV_CLM.T_SURVEY_APPLY k where h.apply_id=k.apply_id and k.CASE_ID=A.case_ID ) POSITIVE_FLAG,
			A.SHARE_CONDITION_VALID,A.APPROVE_TIME ,A.AUDIT_DECISION,A.ACTUAL_PAY,A.REJECT_PAY,
			(select m.REAL_NAME ii from DEV_PAS.T_UDMP_USER m where m.USER_ID=A.AUDITOR_ID) AUDITOR_NAME,
		      (select n.USER_NAME ll from DEV_PAS.T_UDMP_USER n where n.USER_ID=A.AUDITOR_ID) AUDITOR_CODE,
		      (select v.REAL_NAME mm from DEV_PAS.T_UDMP_USER v where v.USER_ID=A.APPROVER_ID) APPROVER_NAME,
		      (select x.USER_NAME oo from DEV_PAS.T_UDMP_USER x where x.USER_ID=A.APPROVER_ID) APPROVER_CODE,
		      A.AUDIT_REJECT_REASON,
		      (select l.ORGAN_NAME nn from DEV_CLM.t_udmp_org_rel l where l.ORGAN_CODE=A.ORGAN_CODE and l.ORGAN_GRADE='02')ORGAN_NAME1,
		      (select z.ORGAN_NAME yy from DEV_CLM.t_udmp_org_rel z where z.ORGAN_CODE=A.ORGAN_CODE and z.ORGAN_GRADE='03')ORGAN_NAME2,
		      (select q.ORGAN_NAME bb from DEV_CLM.t_udmp_org_rel q where q.ORGAN_CODE=A.ORGAN_CODE and q.ORGAN_GRADE='04')ORGAN_NAME3,A.RELATED_NO
			from DEV_CLM.T_CLAIM_CASE A ,
			DEV_CLM.T_CIITC_BACK_RESULT C
			where  C.CASE_ID=A.CASE_ID 
			 and A.CASE_STATUS='80'   
 ]]>
		  <if test=" product_Type != null and product_Type != 9  "><![CDATA[ AND C.PRODUCT_TYPE = #{product_Type} ]]></if>
		 <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.organ_code like '${organ_code}%' ]]></if>
          <if test="approve_time != null and  approve_time  != '' "> <![CDATA[ AND  to_date(to_char(A.APPROVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')>= #{approve_time} ]]> </if>
           <if test="end_case_time != null and  end_case_time  != '' "> <![CDATA[ AND  to_date(to_char(A.APPROVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')<= #{end_case_time} ]]> </if>
            <![CDATA[  )C WHERE
      
      ROWNUM <= #{LESS_NUM} 
 )B WHERE B.RN > #{GREATER_NUM}   ]]>
          </select>  
     
      <select id="queryTradeShareInfoListTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >     
	        <![CDATA[select Count(1) from 
	  (select distinct A.CASE_NO,
      ( select i.CUSTOMER_NAME  from DEV_CLM.T_CUSTOMER i where i.CUSTOMER_ID=A.INSURED_ID) INSURED_NAME,
          (select sum(SUM_AMOUNT) from  DEV_CLM.T_CLAIM_bill pp where pp.CASE_ID=A.CASE_ID) MEDICAL_TOTAL_MONY,
      C.PRODUCT_TYPE,
	  LISTAGG( C.RISK_RESULT,',') WITHIN GROUP(ORDER BY C.RISK_RESULT) OVER (PARTITION BY C.CASE_ID,C.PRODUCT_TYPE) RISK_RESULT,
      (select Count(*)from DEV_CLM.T_SURVEY_APPLY u  where u.case_id=A.CASE_ID) surveyMark,
      (select MAX(h.POSITIVE_FLAG) from DEV_CLM.T_SURVEY_CONCLUSION h,DEV_CLM.T_SURVEY_APPLY k where h.apply_id=k.apply_id and k.CASE_ID=A.case_ID ) POSITIVE_FLAG,
      A.SHARE_CONDITION_VALID,A.APPROVE_TIME ,A.AUDIT_DECISION,A.ACTUAL_PAY,A.REJECT_PAY,
      (select m.REAL_NAME from DEV_PAS.T_UDMP_USER m where m.USER_ID=A.AUDITOR_ID) AUDITOR_NAME,
      (select n.USER_NAME from DEV_PAS.T_UDMP_USER n where n.USER_ID=A.AUDITOR_ID) AUDITOR_CODE,
      (select v.REAL_NAME from DEV_PAS.T_UDMP_USER v where v.USER_ID=A.APPROVER_ID) APPROVER_NAME,
      (select x.USER_NAME from DEV_PAS.T_UDMP_USER x where x.USER_ID=A.APPROVER_ID) APPROVER_CODE,
      A.AUDIT_REJECT_REASON,
      (select l.ORGAN_NAME from DEV_CLM.t_udmp_org_rel l where l.ORGAN_CODE=A.ORGAN_CODE and l.ORGAN_GRADE='02')ORGAN_NAME1,
      (select z.ORGAN_NAME from DEV_CLM.t_udmp_org_rel z where z.ORGAN_CODE=A.ORGAN_CODE and z.ORGAN_GRADE='03')ORGAN_NAM2,
      (select q.ORGAN_NAME from DEV_CLM.t_udmp_org_rel q where q.ORGAN_CODE=A.ORGAN_CODE and q.ORGAN_GRADE='04')ORGAN_NAME3,A.RELATED_NO
	
		FROM DEV_CLM.T_CLAIM_CASE A ,
			DEV_CLM.T_CIITC_BACK_RESULT C
			WHERE  C.CASE_ID=A.CASE_ID 
			 AND A.CASE_STATUS='80'        
     ]]>
      <if test=" product_Type != null and product_Type != 9  "><![CDATA[ AND C.PRODUCT_TYPE = #{product_Type} ]]></if>
      <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.organ_code like '${organ_code}%' ]]></if>
      <if test="approve_time != null and  approve_time  != '' "> <![CDATA[ AND to_date(to_char(A.APPROVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')>= #{approve_time} ]]> </if>
      <if test="end_case_time != null and  end_case_time  != '' "> <![CDATA[ AND  to_date(to_char(A.APPROVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')<= #{end_case_time} ]]> </if>
            
            <![CDATA[  ) R ]]>
          </select> 
          
        <!-- 行业共享信息调用统计报表 -->        
      <select id="queryTradeShareInfoReport"  resultType="java.util.Map" parameterType="java.util.Map" >     
	        <![CDATA[ select B.product_type,
			count(B.CASE_ID)caseCount,
			(select Count(1) from DEV_CLM.T_CIITC_BACK_RESULT t,DEV_CLM.T_CLAIM_CASE g  where RISK_RESULT like '%Y%' and product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.APPROVE_TIME=A.APPROVE_TIME ) invokingCount,
			(select Count(1) from DEV_CLM.T_CIITC_BACK_RESULT t ,DEV_CLM.T_CLAIM_CASE g  where t.RISK_RESULT like '%Y%' and t.product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.ADVANCE_FLAG='0'  and g.APPROVE_TIME=A.APPROVE_TIME
			)positiveCount ,
			(select Count(1) from DEV_CLM.T_CIITC_BACK_RESULT t ,DEV_CLM.T_CLAIM_CASE g  where t.RISK_RESULT like '%Y%' and t.product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.ADVANCE_FLAG='0'  and g.APPROVE_TIME=A.APPROVE_TIME AND A.SHARE_CONDITION_VALID='1'
			)positiveEffectiveCount,
			(select Count(1) from DEV_CLM.T_CIITC_BACK_RESULT t ,DEV_CLM.T_CLAIM_CASE g  where  t.product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.APPROVER_ID!='950010953' and (g.CASE_FLAG!='' or g.CASE_FLAG!='4') and g.CASE_STATUS='80' and g.AUDIT_DECISION='3' and g.APPROVE_TIME=A.APPROVE_TIME
			)refuseCount,
			(select sum(REJECT_PAY) from DEV_CLM.T_CIITC_BACK_RESULT t ,DEV_CLM.T_CLAIM_CASE g  where  t.product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.APPROVER_ID!='950010953' and (g.CASE_FLAG!='' or g.CASE_FLAG!='4') and g.CASE_STATUS='80' and g.AUDIT_DECISION='3' and g.APPROVE_TIME=A.APPROVE_TIME
			)rejectPayCount,A.APPROVE_TIME
			from DEV_CLM.T_CLAIM_CASE A ,DEV_CLM.T_CIITC_BACK_RESULT B where A.CASE_ID=B.CASE_ID and A.CASE_STATUS='80'   ]]>
		  <if test=" product_Type != null and product_Type != 9  "><![CDATA[ AND B.product_Type = #{product_Type} ]]></if>
		 <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.organ_code like '${organ_code}%' ]]></if>
          <if test="approve_time != null and  approve_time  != '' "> <![CDATA[ AND  to_date(to_char(A.APPROVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')>= #{approve_time} ]]> </if>
          <if test="end_case_time != null and  end_case_time  != '' "> <![CDATA[ AND  to_date(to_char(A.APPROVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')<= #{end_case_time} ]]> </if> 
            <![CDATA[ group by B.product_type ,A.APPROVE_TIME,A.SHARE_CONDITION_VALID ]]>
          </select>       
         
         
         <select id="queryTradeShareInfoReportTotal"  resultType="java.lang.Integer" parameterType="java.util.Map" >     
	        <![CDATA[select count(1) from
			（select B.product_type,
			count(B.CASE_ID) caseCount,
			(select Count(1) from DEV_CLM.T_CIITC_BACK_RESULT t,DEV_CLM.T_CLAIM_CASE g  where RISK_RESULT like '%Y%' and product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.APPROVE_TIME=A.APPROVE_TIME ) invokingCount ,
			(select Count(1) from DEV_CLM.T_CIITC_BACK_RESULT t ,DEV_CLM.T_CLAIM_CASE g  where t.RISK_RESULT like '%Y%' and t.product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.ADVANCE_FLAG='0'  and g.APPROVE_TIME=A.APPROVE_TIME
			)positiveCount ,
			(select Count(1) from DEV_CLM.T_CIITC_BACK_RESULT t ,DEV_CLM.T_CLAIM_CASE g  where t.RISK_RESULT like '%Y%' and t.product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.ADVANCE_FLAG='0'  and g.APPROVE_TIME=A.APPROVE_TIME AND A.SHARE_CONDITION_VALID='1'
			)positiveEffectiveCount,
			(select Count(1) from DEV_CLM.T_CIITC_BACK_RESULT t ,DEV_CLM.T_CLAIM_CASE g  where  t.product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.APPROVER_ID!='950010953' and (g.CASE_FLAG!='' or g.CASE_FLAG!='4') and g.CASE_STATUS='80' and g.AUDIT_DECISION='3' and g.APPROVE_TIME=A.APPROVE_TIME
			)refuseCount,
			(select sum(REJECT_PAY) from DEV_CLM.T_CIITC_BACK_RESULT t ,DEV_CLM.T_CLAIM_CASE g  where  t.product_type=B.product_type and t.CASE_ID=g.CASE_ID and g.APPROVER_ID!='950010953' and (g.CASE_FLAG!='' or g.CASE_FLAG!='4') and g.CASE_STATUS='80' and g.AUDIT_DECISION='3' and g.APPROVE_TIME=A.APPROVE_TIME
			)rejectPayCount,A.APPROVE_TIME
			
			from DEV_CLM.T_CLAIM_CASE A ,DEV_CLM.T_CIITC_BACK_RESULT B where A.CASE_ID=B.CASE_ID and A.CASE_STATUS='80'   ]]>
      <if test=" product_Type != null and product_Type != 9  "><![CDATA[ AND B.product_Type = #{product_Type} ]]></if>
      <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.organ_code like '${organ_code}%' ]]></if>
      <if test="approve_time != null and  approve_time  != '' "> <![CDATA[ AND to_date(to_char(A.APPROVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')>= #{approve_time} ]]> </if>
      <if test="end_case_time != null and  end_case_time  != '' "> <![CDATA[ AND  to_date(to_char(A.APPROVE_TIME,'yyyy-MM-dd'),'yyyy-MM-dd')<= #{end_case_time} ]]> </if>     
            <![CDATA[ group by B.product_type ,A.APPROVE_TIME,A.SHARE_CONDITION_VALID  ）j ]]>
          </select> 
          
    <!--查询回退赔案的实际赔案号 -->
	<select id="queryRedoCaseNoByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT CC.CASE_NO,CC.CASE_ID,CC.CASE_STATUS FROM (
			SELECT A.RELATED_NO,A.* FROM APP___CLM__DBUSER.T_CLAIM_CASE A  
			START WITH A.CASE_NO =#{case_no}
			CONNECT BY PRIOR A.RELATED_NO =  A.CASE_NO ) CC ORDER BY CC.CASE_ID DESC
		  ]]>
	</select>
	<!-- 分页查询理赔直连案件清单 -->	
	<select  id="findClaimLiabCloseForPage" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[ 
	SELECT M.* FROM(
		select distinct 
		ROWNUM RN,
		a.case_no,
       a.ORGAN_CODE,
       d.Organ_Name,
       b.policy_code,
       b.BUSI_PROD_CODE,
       b.LIAB_NAME,
       (case
         when b.OPERATE_NOTE = '1' then
          '立案'
         when b.OPERATE_NOTE = '2' then
          '审核'
       end) OPERATE_NOTE,
       c.real_name,
       b.CLOSE_REASON,
       b.CLOSE_TIME
  from dev_clm.t_claim_case a, dev_clm.T_CLAIM_LIAB_CLOSE b,dev_pas.t_udmp_user c,
  DEV_CLM.t_udmp_org_rel d where b.OPERATE_BY = c.user_id and a.organ_code = d.organ_code
  and a.case_id =b.case_id
]]>
        <if test=" close_reason  != null and close_reason !='' "><![CDATA[ AND b.CLOSE_REASON = #{close_reason}]]></if>
        <if test=" start_time  != null and start_time !='' "><![CDATA[AND  trunc(a.END_CASE_TIME) >= #{start_time}]]></if>
       <if test=" end_time  != null and end_time !='' "><![CDATA[AND trunc(a.END_CASE_TIME) <= #{end_time} ]]></if>
	   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND a.ORGAN_CODE like '${organ_code}%' ]]></if>
	<![CDATA[ and ROWNUM <= #{LESS_NUM} ) M WHERE M.RN > #{GREATER_NUM}]]>
	</select>

<select id="findClaimLiabCloseTotals" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[select count(1)FROM(
		select distinct a.case_no,
       a.ORGAN_CODE,
       d.Organ_Name,
       b.policy_code,
       b.BUSI_PROD_CODE,
       b.LIAB_NAME,
       (case
         when b.OPERATE_NOTE = '1' then
          '立案'
         when b.OPERATE_NOTE = '2' then
          '审核'
       end) OPERATE_NOTE,
       c.real_name,
       b.CLOSE_REASON,
       b.CLOSE_TIME
  from dev_clm.t_claim_case a, dev_clm.T_CLAIM_LIAB_CLOSE b,dev_pas.t_udmp_user c,
  DEV_CLM.t_udmp_org_rel d where b.OPERATE_BY = c.user_id and a.organ_code = d.organ_code
  and a.case_id =b.case_id
]]>
        <if test=" close_reason  != null and close_reason !='' "><![CDATA[ AND b.CLOSE_REASON = #{close_reason}]]></if>
        <if test=" start_time  != null and start_time !='' "><![CDATA[AND  trunc(a.END_CASE_TIME) >= #{start_time}]]></if>
       <if test=" end_time  != null and end_time !='' "><![CDATA[AND trunc(a.END_CASE_TIME) <= #{end_time} ]]></if>
	   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND a.ORGAN_CODE like '${organ_code}%' ]]></if>
		       <![CDATA[ )]]>
	</select>
	
	
	<!-- 分页查询理赔直连案件清单 -->	
	<select  id="findClaimLiabCloseForPageExcel" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[ 
	SELECT M.* FROM(
		select distinct 
		ROWNUM RN,
		a.case_no,
       a.ORGAN_CODE,
       d.Organ_Name,
       b.policy_code,
       b.BUSI_PROD_CODE,
       b.LIAB_NAME,
       (case
         when b.OPERATE_NOTE = '1' then
          '立案'
         when b.OPERATE_NOTE = '2' then
          '审核'
       end) OPERATE_NOTE,
       c.real_name,
       b.CLOSE_REASON,
       b.CLOSE_TIME
  from dev_clm.t_claim_case a, dev_clm.T_CLAIM_LIAB_CLOSE b,dev_pas.t_udmp_user c,
  DEV_CLM.t_udmp_org_rel d where b.OPERATE_BY = c.user_id and a.organ_code = d.organ_code
  and a.case_id =b.case_id
]]>
        <if test=" close_reason  != null and close_reason !='' "><![CDATA[ AND b.CLOSE_REASON = #{close_reason}]]></if>
        <if test=" start_time  != null and start_time !='' "><![CDATA[AND  trunc(a.END_CASE_TIME) >= #{start_time}]]></if>
       <if test=" end_time  != null and end_time !='' "><![CDATA[AND trunc(a.END_CASE_TIME) <= #{end_time} ]]></if>
	   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND a.ORGAN_CODE like '${organ_code}%' ]]></if>
	<![CDATA[ and ROWNUM <= #{LESS_NUM} ) M WHERE M.RN > #{GREATER_NUM}]]>
	</select>
	
	<select id="findClaimLiabCloseTotalsExcel" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[select count(1)FROM(
		select distinct a.case_no,
       a.ORGAN_CODE,
       d.Organ_Name,
       b.policy_code,
       b.BUSI_PROD_CODE,
       b.LIAB_NAME,
       (case
         when b.OPERATE_NOTE = '1' then
          '立案'
         when b.OPERATE_NOTE = '2' then
          '审核'
       end) OPERATE_NOTE,
       c.real_name,
       b.CLOSE_REASON,
       b.CLOSE_TIME
  from dev_clm.t_claim_case a, dev_clm.T_CLAIM_LIAB_CLOSE b,dev_pas.t_udmp_user c,
  DEV_CLM.t_udmp_org_rel d where b.OPERATE_BY = c.user_id and a.organ_code = d.organ_code
  and a.case_id =b.case_id
]]>
        <if test=" close_reason  != null and close_reason !='' "><![CDATA[ AND b.CLOSE_REASON = #{close_reason}]]></if>
        <if test=" start_time  != null and start_time !='' "><![CDATA[AND  trunc(a.END_CASE_TIME) >= #{start_time}]]></if>
       <if test=" end_time  != null and end_time !='' "><![CDATA[AND trunc(a.END_CASE_TIME) <= #{end_time} ]]></if>
	   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND a.ORGAN_CODE like '${organ_code}%' ]]></if>
		       <![CDATA[ )]]>
	</select>
	
	<!--查询是否存在医疗费用明细 -->
	<select id="queryZLFYMXCount" parameterType="java.util.Map" resultType="java.lang.Integer">
    	<![CDATA[ 
			SELECT count(1)
			  FROM DEV_CLM.T_CLAIM_BILL_ITEM_DETAIL A, DEV_CLM.T_CLAIM_BILL TC
			 WHERE A.BILL_NO = TC.BILL_NO
			   AND A.CASE_ID = TC.CASE_ID
			   AND A.CASE_ID = #{caseNo}
		  ]]>
	</select>
		
	<select id="findCustomerNameByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
				SELECT B.CUSTOMER_NAME AS CUSTOMER_NAME
			  		FROM DEV_CLM.T_CLAIM_CASE A
			 		INNER JOIN DEV_CLM.T_CUSTOMER B
			    		ON A.INSURED_ID = B.CUSTOMER_ID
			 		WHERE A.CASE_ID = #{case_id}
		  ]]>
	</select>
	
	<!-- 反洗钱客户身份识别报表清单查询-->
	<select id="queryclaimAntiMoneyLaunderingListPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
		select D.* from (
		select C.*,rownum rn from (
			
		SELECT DISTINCT
       A.CASE_NO,
       A.ORGAN_CODE,
       C.POLICY_CODE,
       TO_CHAR((SELECT WM_CONCAT(DISTINCT T.NAME)
                 FROM DEV_CLM.T_CLAIM_LIAB LIAB, DEV_CLM.T_CLAIM_TYPE T
                WHERE LIAB.CLAIM_TYPE = T.CODE
                  AND LIAB.CASE_ID = A.CASE_ID
                  AND LIAB.POLICY_CODE = C.POLICY_CODE)) CLAIM_TYPE_STR,
       A.ACTUAL_PAY,
       G.PAY_AMOUNT,
       G.ADJUST_TYPE,
       SUBSTR(B.INSURED_NAME, 1, 1) || DECODE(LENGTH(B.INSURED_NAME),'2','****','****'||SUBSTR(B.INSURED_NAME,length(B.INSURED_NAME),1)) INSURED_NAME,
       DECODE(B.INSURED_SEX, '1', '男', '2', '女', '未知') INSURED_SEX_STR,
       (SELECT A.COUNTRY_NAME FROM  DEV_CLM.T_COUNTRY A WHERE A.COUNTRY_CODE = B.INSURED_NATION) INSURED_NATION_STR,
       (SELECT JOB.JOB_NAME
          FROM DEV_CLM.T_JOB_CODE JOB
         WHERE JOB.JOB_CODE = B.INSURED_JOB_CODE) INSURED_JOB_NAME,
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = B.INSURED_STATE) ||
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = B.INSURED_CITY
           AND DIS.NAME NOT IN ('市辖区', '县')) ||
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = B.INSURED_DISTRICT) ||  regexp_replace( B.INSURED_ADDRESS,'\d','****') INSURED_ADRESS_STR,
CASE
         WHEN SUBSTR(B.INSURED_PHONE, 1, 1) = '0' AND
              (SUBSTR(B.INSURED_PHONE, 2, 1) = '1' OR SUBSTR(B.INSURED_PHONE, 2, 1) = '2') 
              THEN
         	  SUBSTR(B.INSURED_PHONE, 1, 4) || '****'||SUBSTR(B.INSURED_PHONE,length(B.INSURED_PHONE)-1,2)
         WHEN SUBSTR(B.INSURED_PHONE, 1, 1) = '0' AND
              (SUBSTR(B.INSURED_PHONE, 2, 1) != '1' OR SUBSTR(B.INSURED_PHONE, 2, 1) != '2')
              THEN 
              SUBSTR(B.INSURED_PHONE, 1, 5) || '****'||SUBSTR(B.INSURED_PHONE,length(B.INSURED_PHONE)-1,2)
              
         WHEN LENGTH(B.INSURED_PHONE) = 7
              THEN 
               '****'||SUBSTR(B.INSURED_PHONE,length(B.INSURED_PHONE)-1,2)
         WHEN LENGTH(B.INSURED_PHONE) = 8
              THEN 
               '****'||SUBSTR(B.INSURED_PHONE,length(B.INSURED_PHONE)-2,3)
         WHEN LENGTH(B.INSURED_PHONE) = 11 AND
              SUBSTR(B.INSURED_PHONE, 1, 1) = '1'
              THEN 
              REPLACE(B.INSURED_PHONE, SUBSTR(B.INSURED_PHONE, 4, 4), '****')
         ELSE
          B.INSURED_PHONE
       END INSURED_PHONE_STR,       (SELECT CER.TYPE
          FROM DEV_CLM.T_CERTI_TYPE CER
         WHERE CER.CODE = B.INSURED_CERTI_TYPE) INSURED_CERTI_TYPE,
      CASE
          WHEN LENGTH(B.INSURED_CERTI_NO) = 18 AND
              (B.INSURED_CERTI_TYPE = '0' OR B.INSURED_CERTI_TYPE = '5') THEN
          REPLACE(B.INSURED_CERTI_NO,
                  SUBSTR(B.INSURED_CERTI_NO, 4, 12),
                  '************')
       WHEN B.INSURED_CERTI_TYPE = '2'
                     THEN SUBSTR(B.INSURED_CERTI_NO, 1, 4) || '****'
                  WHEN B.INSURED_CERTI_TYPE = '1'
                     THEN SUBSTR(B.INSURED_CERTI_NO, 1, 5) || '****'
         ELSE
          B.INSURED_CERTI_NO
       END INSURED_CERTI_NO,
       B.INSURED_CERTI_STAR_DATE,
       B.INSURED_CERTI_END_DATE,
       SUBSTR(D.BENE_NAME, 1, 1) || DECODE(LENGTH(D.BENE_NAME),'2','****','****'||SUBSTR(D.BENE_NAME,length(D.BENE_NAME),1))  BENE_NAME,
       DECODE(D.BENE_SEX, '1', '男', '2', '女', '未知') BENE_SEX_STR,
       (SELECT A.COUNTRY_NAME FROM  DEV_CLM.T_COUNTRY A WHERE A.COUNTRY_CODE = D.BENE_NATION) BENE_NATION_STR,
       (SELECT JOB.JOB_NAME
          FROM DEV_CLM.T_JOB_CODE JOB
         WHERE JOB.JOB_CODE = D.BENE_JOB_CODE) BENE_JOB_NAME,
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = D.BENE_PROVINCE) ||
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = D.BENE_CITY
           AND DIS.NAME NOT IN ('市辖区', '县')) ||
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = D.BENE_DISTRICT) || regexp_replace(D.BENE_ADDRESS,'\d','****')  BENE_ADRESS_STR,
CASE
         WHEN SUBSTR(D.BENE_PHONE, 1, 1) = '0' AND
              (SUBSTR(D.BENE_PHONE, 2, 1) = '1' OR SUBSTR(D.BENE_PHONE, 2, 1) = '2') 
              THEN
         	  SUBSTR(D.BENE_PHONE, 1, 4) || '****'||SUBSTR(D.BENE_PHONE,length(D.BENE_PHONE)-1,2)
         WHEN SUBSTR(D.BENE_PHONE, 1, 1) = '0' AND
              (SUBSTR(D.BENE_PHONE, 2, 1) != '1' OR SUBSTR(D.BENE_PHONE, 2, 1) != '2')
              THEN 
              SUBSTR(D.BENE_PHONE, 1, 5) || '****'||SUBSTR(D.BENE_PHONE,length(D.BENE_PHONE)-1,2)
              
         WHEN LENGTH(D.BENE_PHONE) = 7
              THEN 
               '****'||SUBSTR(D.BENE_PHONE,length(D.BENE_PHONE)-1,2)
         WHEN LENGTH(D.BENE_PHONE) = 8
              THEN 
               '****'||SUBSTR(D.BENE_PHONE,length(D.BENE_PHONE)-2,3)
         WHEN LENGTH(D.BENE_PHONE) = 11 AND
              SUBSTR(D.BENE_PHONE, 1, 1) = '1'
              THEN 
              REPLACE(D.BENE_PHONE, SUBSTR(D.BENE_PHONE, 4, 4), '****')
         ELSE
          D.BENE_PHONE
       END BENE_PHONE_STR,       (SELECT CER.TYPE
          FROM DEV_CLM.T_CERTI_TYPE CER
         WHERE CER.CODE = D.BENE_CERTI_TYPE) BENE_CERTI_TYPE,
       CASE
           WHEN LENGTH(D.BENE_CERTI_NO) = 18 AND
              (D.BENE_CERTI_TYPE = '0' OR D.BENE_CERTI_TYPE = '5') THEN
          REPLACE(D.BENE_CERTI_NO,
                  SUBSTR(D.BENE_CERTI_NO, 4, 12),
                  '************') WHEN D.BENE_CERTI_TYPE = '2'
                     THEN SUBSTR(D.BENE_CERTI_NO, 1, 4) || '****'
                  WHEN D.BENE_CERTI_TYPE = '1'
                     THEN SUBSTR(D.BENE_CERTI_NO, 1, 5) || '****'
         ELSE
         D.BENE_CERTI_NO
       END BENE_CERTI_NO,
       D.BENE_CERTI_START,
       D.BENE_CERTI_END,
       SUBSTR(E.PAYEE_NAME, 1, 1) || DECODE(LENGTH(E.PAYEE_NAME),'2','****','****'||SUBSTR(E.PAYEE_NAME,length(E.PAYEE_NAME),1))  PAYEE_NAME,
       DECODE(E.PAYEE_SEX, '1', '男', '2', '女', '未知') PAYEE_SEX_STR,
       (SELECT A.COUNTRY_NAME FROM  DEV_CLM.T_COUNTRY A WHERE A.COUNTRY_CODE = E.PAYEE_NATION) PAYEE_NATION_STR,
       (SELECT JOB.JOB_NAME
          FROM DEV_CLM.T_JOB_CODE JOB
         WHERE JOB.JOB_CODE = E.PAYEE_JOB_CODE) PAYEE_JOB_NAME,
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = E.PAYEE_STATE) ||
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = E.PAYEE_CITY
           AND DIS.NAME NOT IN ('市辖区', '县')) ||
       (SELECT DIS.NAME
          FROM DEV_CLM.T_DISTRICT DIS
         WHERE DIS.CODE = E.PAYEE_DISTRICT) ||  regexp_replace(E.PAYEE_ADDRESS,'\d','****')  PAYEE_ADRESS_STR,
CASE
         WHEN SUBSTR(E.PAYEE_PHONE, 1, 1) = '0' AND
              (SUBSTR(E.PAYEE_PHONE, 2, 1) = '1' OR SUBSTR(E.PAYEE_PHONE, 2, 1) = '2') 
              THEN
         	  SUBSTR(E.PAYEE_PHONE, 1, 4) || '****'||SUBSTR(E.PAYEE_PHONE,length(E.PAYEE_PHONE)-1,2)
         WHEN SUBSTR(E.PAYEE_PHONE, 1, 1) = '0' AND
              (SUBSTR(E.PAYEE_PHONE, 2, 1) != '1' OR SUBSTR(E.PAYEE_PHONE, 2, 1) != '2')
              THEN 
              SUBSTR(E.PAYEE_PHONE, 1, 5) || '****'||SUBSTR(E.PAYEE_PHONE,length(E.PAYEE_PHONE)-1,2)
              
         WHEN LENGTH(E.PAYEE_PHONE) = 7
              THEN 
               '****'||SUBSTR(E.PAYEE_PHONE,length(E.PAYEE_PHONE)-1,2)
         WHEN LENGTH(E.PAYEE_PHONE) = 8
              THEN 
               '****'||SUBSTR(E.PAYEE_PHONE,length(E.PAYEE_PHONE)-2,3)
         WHEN LENGTH(E.PAYEE_PHONE) = 11 AND
              SUBSTR(E.PAYEE_PHONE, 1, 1) = '1'
              THEN 
              REPLACE(E.PAYEE_PHONE, SUBSTR(E.PAYEE_PHONE, 4, 4), '****')
         ELSE
          E.PAYEE_PHONE
       END PAYEE_PHONE_STR,       (SELECT CER.TYPE
          FROM DEV_CLM.T_CERTI_TYPE CER
         WHERE CER.CODE = E.PAYEE_CERTI_TYPE) PAYEE_CERTI_TYPE,
       CASE
         WHEN LENGTH(E.PAYEE_CERTI_NO) = 18 AND
              (E.PAYEE_CERTI_TYPE = '0' OR E.PAYEE_CERTI_TYPE = '5') THEN
          REPLACE(E.PAYEE_CERTI_NO,
                  SUBSTR(E.PAYEE_CERTI_NO, 4, 12),
                  '************')
        WHEN E.PAYEE_CERTI_TYPE = '2'
                     THEN SUBSTR(E.PAYEE_CERTI_NO, 1, 4) || '****'
                  WHEN D.BENE_CERTI_TYPE = '1'
                     THEN SUBSTR(E.PAYEE_CERTI_NO, 1, 5) || '****'
         ELSE
          E.PAYEE_CERTI_NO
       END PAYEE_CERTI_NO,
       E.PAYEE_CERTI_START,
       E.PAYEE_CERTI_END,
       (SELECT LA.RELATION_NAME
          FROM DEV_CLM.T_LA_PH_RELA LA
         WHERE LA.RELATION_CODE = C.INSURED_HOLIDER_RE) INSURED_HOLIDER_STR,
       (SELECT LA.RELATION_NAME
          FROM DEV_CLM.T_LA_PH_RELA LA
         WHERE LA.RELATION_CODE = D.BENE_RELATION) BENE_INSURED_STR,
       (SELECT LA.RELATION_NAME
          FROM DEV_CLM.T_LA_PH_RELA LA
         WHERE LA.RELATION_CODE = G.BENE_HOLDER_RELATION) BENE_HOLDER_STR,
       (SELECT LA.RELATION_NAME
          FROM DEV_CLM.T_LA_PH_RELA LA
         WHERE LA.RELATION_CODE = G.PAYEE_RELATION) PAYEE_BENE_STR

  FROM DEV_CLM.T_CLAIM_CASE             A,
       DEV_CLM.T_CLAIM_INSURED          B,
       DEV_CLM.T_CLAIM_INSURED_RELATION C,
       DEV_CLM.T_CLAIM_BENE             D,
       DEV_CLM.T_CLAIM_PAYEE            E,
        (
 SELECT DISTINCT PD.DEDUCTION_ID,
                 (SELECT MAX(TCBP.OVER_COMP_PAY)
                    FROM DEV_CLM.T_CLAIM_BUSI_PROD TCBP
                   WHERE PD.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                     AND PD.CASE_ID = TCBP.CASE_ID) OVER_COMP_PAY,
                 PD.CASE_ID,
                 PD.POLICY_CODE,
                 PD.BUSI_PROD_CODE,
                 PD.BUSI_ITEM_ID,
                 PD.POLICY_ID,
                 PD.RESTORE_BALANCE AS PAY_AMOUNT,
                 '返还保费' AS ADJUST_TYPE,
                 '1' AS ADJUST_CODE,
                 CBP.ASSIGN_FLAG, /*返还保费不需要分配*/
                 (CASE WHEN NVL(PD.CASH_DIVIDEND, 0.0) = 0.0 THEN  '0' ELSE  '1' END) AS CASH_FLAG,
                 (CASE  WHEN NVL(PD.ACTUAL_PAY_BALANCE, 0.0) = 0.0 THEN '0' ELSE '1' END) AS ACTUAL_FLAG,
                 (CASE WHEN NVL(PD.RESTORE_BALANCE, 0.0) = 0.0 THEN  '0' ELSE '1' END) AS RESTORE_FLAG,
                 TCP.BENE_ID,
                 TCP.PAYEE_ID,
                 TCP.BENE_HOLDER_RELATION,
                 TCP.PAYEE_RELATION
   FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
  INNER JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CBP
     ON PD.BUSI_PROD_CODE = CBP.BUSI_PROD_CODE
    AND PD.POLICY_CODE = CBP.POLICY_CODE
    AND PD.CASE_ID = CBP.CASE_ID
  INNER JOIN (SELECT P.BENE_ID,
                     P.ADJUST_BUSI_ID,
                     CFM.ARAP_CODE,
                     (CASE
             WHEN P.ADJUST_BUSI_ID IS NULL THEN
                        P.PAY_AMOUNT
                       WHEN CFM.ARAP_CODE = 01 THEN
                        P.PAY_AMOUNT * -1
                       WHEN CFM.ARAP_CODE = 02 THEN
 P.PAY_AMOUNT
                     END) PAY_AMOUNT,
                     P.BUSI_ITEM_ID,
                     P.POLICY_ID,
                     P.PAYEE_ID,
    P.BUSI_PROD_CODE,
                     P.CASE_ID,
                     P.BENE_HOLDER_RELATION,
                     (CASE 
                       WHEN P.PAYEE_RELATION IS NULL THEN
                       PE.PAYEE_RELATION
                       ELSE P.PAYEE_RELATION
                       END) PAYEE_RELATION
                FROM DEV_CLM.T_CLAIM_PAY P
                INNER JOIN DEV_CLM.T_CLAIM_PAYEE PE
                      ON P.CASE_ID = PE.CASE_ID
                      AND P.PAYEE_ID = PE.PAYEE_ID
                
                LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CAB
                  ON CAB.ADJUST_BUSI_ID = P.ADJUST_BUSI_ID
                 AND CAB.CASE_ID = P.CASE_ID
                LEFT JOIN DEV_CLM.T_CLAIM_FEE_MAPPING CFM
                  ON CAB.ADJUST_TYPE = CFM.CLM_FEE_CODE
               WHERE P.ADVANCE_FLAG != 1
                 AND ((CAB.ADJUST_TYPE = '8' OR CAB.ADJUST_TYPE = '16' OR
                     CAB.ADJUST_TYPE = '17' OR CAB.ADJUST_TYPE = '18') AND
 P.ADJUST_BUSI_ID IS NOT NULL)) TCP
     ON TCP.CASE_ID = PD.CASE_ID
    AND TCP.POLICY_ID = PD.POLICY_ID
    AND TCP.BUSI_PROD_CODE = PD.BUSI_PROD_CODE
  WHERE PD.RESTORE_BALANCE != 0.0
    AND (CBP.ADJUST_TYPE = '8' OR CBP.ADJUST_TYPE = '16' OR
        CBP.ADJUST_TYPE = '17' OR CBP.ADJUST_TYPE = '18')
 UNION ALL
 SELECT DISTINCT PD.DEDUCTION_ID,
                 (SELECT MAX(TCBP.OVER_COMP_PAY)
                    FROM DEV_CLM.T_CLAIM_BUSI_PROD TCBP
                   WHERE PD.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
                     AND PD.CASE_ID = TCBP.CASE_ID) OVER_COMP_PAY,
                 PD.CASE_ID,
                 PD.POLICY_CODE,
                 PD.BUSI_PROD_CODE,
                 PD.BUSI_ITEM_ID,
                 PD.POLICY_ID,
                 PD.ACTUAL_PAY_BALANCE AS PAY_AMOUNT,
                 '理赔金' AS ADJUST_TYPE,
                 '2' AS ADJUST_CODE,
                 CBP.ASSIGN_FLAG,
                 (CASE WHEN NVL(PD.CASH_DIVIDEND, 0.0) = 0.0 THEN '0' ELSE '1' END) AS CASH_FLAG,
                 (CASE WHEN NVL(PD.ACTUAL_PAY_BALANCE, 0.0) = 0.0 THEN '0' ELSE '1' END) AS ACTUAL_FLAG,
                 (CASE WHEN NVL(PD.RESTORE_BALANCE, 0.0) = 0.0 THEN '0' ELSE '1' END) AS RESTORE_FLAG,
                 TCP.BENE_ID,
                 TCP.PAYEE_ID,
                 TCP.BENE_HOLDER_RELATION,
                 TCP.PAYEE_RELATION
   FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
  INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
     ON PD.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
  INNER JOIN DEV_CLM.T_CLAIM_BUSI_PROD CBP
     ON PD.BUSI_PROD_CODE = CBP.BUSI_PROD_CODE
    AND PD.POLICY_CODE = CBP.POLICY_CODE
    AND PD.CASE_ID = CBP.CASE_ID
  INNER JOIN (SELECT P.BENE_ID,
                     P.ADJUST_BUSI_ID,
                     CFM.ARAP_CODE,
                     (CASE
                       WHEN P.ADJUST_BUSI_ID IS NULL THEN
                        P.PAY_AMOUNT
                       WHEN CFM.ARAP_CODE = 01 THEN
                        P.PAY_AMOUNT * -1
                       WHEN CFM.ARAP_CODE = 02 THEN
                        P.PAY_AMOUNT
                     END) PAY_AMOUNT,
                     P.BUSI_ITEM_ID,
                     P.POLICY_ID,
                     P.PAYEE_ID,
                     P.BUSI_PROD_CODE,
                     P.CASE_ID,
                     P.BENE_HOLDER_RELATION,
                     (CASE 
                       WHEN P.PAYEE_RELATION IS NULL THEN
                       PE.PAYEE_RELATION
                       ELSE P.PAYEE_RELATION
                       END) PAYEE_RELATION
                FROM DEV_CLM.T_CLAIM_PAY P
                INNER JOIN DEV_CLM.T_CLAIM_PAYEE PE
                      ON P.CASE_ID = PE.CASE_ID
                      AND P.PAYEE_ID = PE.PAYEE_ID
               
                LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CAB
                  ON CAB.ADJUST_BUSI_ID = P.ADJUST_BUSI_ID
                 AND CAB.CASE_ID = P.CASE_ID
                LEFT JOIN DEV_CLM.T_CLAIM_FEE_MAPPING CFM
                  ON CAB.ADJUST_TYPE = CFM.CLM_FEE_CODE
               WHERE P.ADVANCE_FLAG != 1
                 AND ((P.ADJUST_BUSI_ID is null OR
                     (P.ADJUST_BUSI_ID is not null and
                     (CAB.ADJUST_TYPE != '20' and CAB.ADJUST_TYPE != '19' and
                     CAB.ADJUST_TYPE != '8' and CAB.ADJUST_TYPE != '16' and
                     CAB.ADJUST_TYPE != '17' and CAB.ADJUST_TYPE != '18'))))) TCP
     ON TCP.CASE_ID = PD.CASE_ID
    AND TCP.POLICY_ID = PD.POLICY_ID
    AND TCP.BUSI_PROD_CODE = PD.BUSI_PROD_CODE
  WHERE PD.ACTUAL_PAY_BALANCE != 0.0
  UNION ALL
   SELECT DISTINCT PD.DEDUCTION_ID,
                 (SELECT MAX(TCBP.OVER_COMP_PAY) FROM DEV_CLM.T_CLAIM_BUSI_PROD TCBP WHERE PD.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID AND PD.CASE_ID = TCBP.CASE_ID) OVER_COMP_PAY,
                 PD.CASE_ID,
                 PD.POLICY_CODE,
                 PD.BUSI_PROD_CODE,
                 PD.BUSI_ITEM_ID,
                 PD.POLICY_ID,
                 PD.CASH_DIVIDEND AS PAY_AMOUNT,
                 '现金红利' AS ADJUST_TYPE,
                 '3' AS ADJUST_CODE,
                 CBP.ASSIGN_FLAG, 
                 (case when nvl(PD.Cash_Dividend,0.0)=0.0 then '0' else '1' end)  AS CASH_FLAG,
                 (case when nvl(PD.Actual_Pay_Balance,0.0)=0.0 then '0' else '1' end) AS ACTUAL_FLAG,
                 (case when nvl(PD.Restore_Balance,0.0)=0.0 then '0' else '1' end) AS RESTORE_FLAG,
                 TCP.BENE_ID,
                 TCP.PAYEE_ID,
                 TCP.BENE_HOLDER_RELATION,
                 TCP.PAYEE_RELATION
   FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
  INNER JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CBP
     ON PD.BUSI_PROD_CODE = CBP.BUSI_PROD_CODE
    AND PD.POLICY_CODE = CBP.POLICY_CODE
    AND PD.CASE_ID = CBP.CASE_ID
  INNER JOIN (SELECT P.BENE_ID,
                     P.ADJUST_BUSI_ID,
                     CFM.ARAP_CODE,
                     (CASE
             WHEN P.ADJUST_BUSI_ID IS NULL THEN
                        P.PAY_AMOUNT
                       WHEN CFM.ARAP_CODE = 01 THEN
                        P.PAY_AMOUNT * -1
                       WHEN CFM.ARAP_CODE = 02 THEN
 P.PAY_AMOUNT
                     END) PAY_AMOUNT,
                     P.BUSI_ITEM_ID,
                     P.POLICY_ID,
                     P.PAYEE_ID,
    P.BUSI_PROD_CODE,
                     P.CASE_ID,
                     P.BENE_HOLDER_RELATION,
                (CASE 
                   WHEN P.PAYEE_RELATION IS NULL THEN
                   PE.PAYEE_RELATION
                   ELSE P.PAYEE_RELATION
                   END) PAYEE_RELATION
                FROM DEV_CLM.T_CLAIM_PAY P
                INNER JOIN DEV_CLM.T_CLAIM_PAYEE PE
                      ON P.CASE_ID = PE.CASE_ID
                      AND P.PAYEE_ID = PE.PAYEE_ID
                
                LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CAB
                  ON CAB.ADJUST_BUSI_ID = P.ADJUST_BUSI_ID
                 AND CAB.CASE_ID = P.CASE_ID
                LEFT JOIN DEV_CLM.T_CLAIM_FEE_MAPPING CFM
                  ON CAB.ADJUST_TYPE = CFM.CLM_FEE_CODE
               WHERE P.ADVANCE_FLAG != 1
                 AND ((CAB.ADJUST_TYPE = '19' OR CAB.ADJUST_TYPE = '20'))) TCP
     ON TCP.CASE_ID = PD.CASE_ID
    AND TCP.POLICY_ID = PD.POLICY_ID
    AND TCP.BUSI_PROD_CODE = PD.BUSI_PROD_CODE
  WHERE PD.CASH_DIVIDEND != 0.0
    AND (CBP.ADJUST_TYPE = '19' OR CBP.ADJUST_TYPE = '20'))            G
 WHERE A.CASE_ID = B.CASE_ID
   AND A.CASE_ID = C.CASE_ID
   AND A.CASE_ID = D.CASE_ID
   AND A.CASE_ID = E.CASE_ID
   AND A.CASE_ID = G.CASE_ID
   AND G.CASE_ID = C.CASE_ID
   AND G.POLICY_CODE = C.POLICY_CODE
   AND G.POLICY_ID = C.POLICY_ID
   AND G.PAYEE_ID = E.PAYEE_ID
   AND G.BENE_ID = D.BENE_ID
   AND A.CASE_STATUS!=99	]]>	
		
			<if test="organ_code != null and organ_code != ''">
		    	<![CDATA[ and A.ORGAN_CODE like  '${organ_code}%']]>
		    </if>
		    <if test="start_endCase_time != null and start_endCase_time != ''"><![CDATA[and A.APPROVE_TIME >= #{start_endCase_time}]]></if>
			<if test="end_end_case_time != null and end_end_case_time != ''"><![CDATA[and A.APPROVE_TIME <= #{end_end_case_time}]]></if>
		<![CDATA[
		  )C where rownum <= #{LESS_NUM})D where D.rn > #{GREATER_NUM}
		]]>
	</select>
	
	<select id="queryclaimAntiMoneyLaunderingListTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[
		select count(1)
		  from (SELECT DISTINCT
		       A.CASE_NO,
		       A.ORGAN_CODE,
		       C.POLICY_CODE,
		       TO_CHAR((SELECT WM_CONCAT(DISTINCT T.NAME)
		                 FROM DEV_CLM.T_CLAIM_LIAB LIAB, DEV_CLM.T_CLAIM_TYPE T
		                WHERE LIAB.CLAIM_TYPE = T.CODE
		                  AND LIAB.CASE_ID = A.CASE_ID
		                  AND LIAB.POLICY_CODE = C.POLICY_CODE)) CLAIM_TYPE_STR,
		       A.ACTUAL_PAY,
		       G.PAY_AMOUNT,
		       G.ADJUST_TYPE,
		       SUBSTR(B.INSURED_NAME, 1, 1) || DECODE(LENGTH(B.INSURED_NAME),'2','****','****'||SUBSTR(B.INSURED_NAME,length(B.INSURED_NAME),1)) INSURED_NAME,
		       DECODE(B.INSURED_SEX, '1', '男', '2', '女', '未知') INSURED_SEX_STR,
		       (SELECT A.COUNTRY_NAME FROM  DEV_CLM.T_COUNTRY A WHERE A.COUNTRY_CODE = B.INSURED_NATION) INSURED_NATION_STR,
		       (SELECT JOB.JOB_NAME
		          FROM DEV_CLM.T_JOB_CODE JOB
		         WHERE JOB.JOB_CODE = B.INSURED_JOB_CODE) INSURED_JOB_NAME,
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = B.INSURED_STATE) ||
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = B.INSURED_CITY
		           AND DIS.NAME NOT IN ('市辖区', '县')) ||
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = B.INSURED_DISTRICT) ||  regexp_replace( B.INSURED_ADDRESS,'\d','****') INSURED_ADRESS_STR,
		CASE
		         WHEN SUBSTR(B.INSURED_PHONE, 1, 1) = '0' AND
		              (SUBSTR(B.INSURED_PHONE, 2, 1) = '1' OR SUBSTR(B.INSURED_PHONE, 2, 1) = '2') 
		              THEN
		         	  SUBSTR(B.INSURED_PHONE, 1, 4) || '****'||SUBSTR(B.INSURED_PHONE,length(B.INSURED_PHONE)-1,2)
		         WHEN SUBSTR(B.INSURED_PHONE, 1, 1) = '0' AND
		              (SUBSTR(B.INSURED_PHONE, 2, 1) != '1' OR SUBSTR(B.INSURED_PHONE, 2, 1) != '2')
		              THEN 
		              SUBSTR(B.INSURED_PHONE, 1, 5) || '****'||SUBSTR(B.INSURED_PHONE,length(B.INSURED_PHONE)-1,2)
		              
		         WHEN LENGTH(B.INSURED_PHONE) = 7
		              THEN 
		               '****'||SUBSTR(B.INSURED_PHONE,length(B.INSURED_PHONE)-1,2)
		         WHEN LENGTH(B.INSURED_PHONE) = 8
		              THEN 
		               '****'||SUBSTR(B.INSURED_PHONE,length(B.INSURED_PHONE)-2,3)
		         WHEN LENGTH(B.INSURED_PHONE) = 11 AND
		              SUBSTR(B.INSURED_PHONE, 1, 1) = '1'
		              THEN 
		              REPLACE(B.INSURED_PHONE, SUBSTR(B.INSURED_PHONE, 4, 4), '****')
		         ELSE
		          B.INSURED_PHONE
		       END INSURED_PHONE_STR,       (SELECT CER.TYPE
		          FROM DEV_CLM.T_CERTI_TYPE CER
		         WHERE CER.CODE = B.INSURED_CERTI_TYPE) INSURED_CERTI_TYPE,
		      CASE
		          WHEN LENGTH(B.INSURED_CERTI_NO) = 18 AND
		              (B.INSURED_CERTI_TYPE = '0' OR B.INSURED_CERTI_TYPE = '5') THEN
		          REPLACE(B.INSURED_CERTI_NO,
		                  SUBSTR(B.INSURED_CERTI_NO, 4, 12),
		                  '************')
		       WHEN B.INSURED_CERTI_TYPE = '2'
		                     THEN SUBSTR(B.INSURED_CERTI_NO, 1, 4) || '****'
		                  WHEN B.INSURED_CERTI_TYPE = '1'
		                     THEN SUBSTR(B.INSURED_CERTI_NO, 1, 5) || '****'
		         ELSE
		          B.INSURED_CERTI_NO
		       END INSURED_CERTI_NO,
		       B.INSURED_CERTI_STAR_DATE,
		       B.INSURED_CERTI_END_DATE,
		       SUBSTR(D.BENE_NAME, 1, 1) || DECODE(LENGTH(D.BENE_NAME),'2','****','****'||SUBSTR(D.BENE_NAME,length(D.BENE_NAME),1))  BENE_NAME,
		       DECODE(D.BENE_SEX, '1', '男', '2', '女', '未知') BENE_SEX_STR,
		       (SELECT A.COUNTRY_NAME FROM  DEV_CLM.T_COUNTRY A WHERE A.COUNTRY_CODE = D.BENE_NATION) BENE_NATION_STR,
		       (SELECT JOB.JOB_NAME
		          FROM DEV_CLM.T_JOB_CODE JOB
		         WHERE JOB.JOB_CODE = D.BENE_JOB_CODE) BENE_JOB_NAME,
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = D.BENE_PROVINCE) ||
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = D.BENE_CITY
		           AND DIS.NAME NOT IN ('市辖区', '县')) ||
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = D.BENE_DISTRICT) || regexp_replace(D.BENE_ADDRESS,'\d','****')  BENE_ADRESS_STR,
		CASE
		         WHEN SUBSTR(D.BENE_PHONE, 1, 1) = '0' AND
		              (SUBSTR(D.BENE_PHONE, 2, 1) = '1' OR SUBSTR(D.BENE_PHONE, 2, 1) = '2') 
		              THEN
		         	  SUBSTR(D.BENE_PHONE, 1, 4) || '****'||SUBSTR(D.BENE_PHONE,length(D.BENE_PHONE)-1,2)
		         WHEN SUBSTR(D.BENE_PHONE, 1, 1) = '0' AND
		              (SUBSTR(D.BENE_PHONE, 2, 1) != '1' OR SUBSTR(D.BENE_PHONE, 2, 1) != '2')
		              THEN 
		              SUBSTR(D.BENE_PHONE, 1, 5) || '****'||SUBSTR(D.BENE_PHONE,length(D.BENE_PHONE)-1,2)
		              
		         WHEN LENGTH(D.BENE_PHONE) = 7
		              THEN 
		               '****'||SUBSTR(D.BENE_PHONE,length(D.BENE_PHONE)-1,2)
		         WHEN LENGTH(D.BENE_PHONE) = 8
		              THEN 
		               '****'||SUBSTR(D.BENE_PHONE,length(D.BENE_PHONE)-2,3)
		         WHEN LENGTH(D.BENE_PHONE) = 11 AND
		              SUBSTR(D.BENE_PHONE, 1, 1) = '1'
		              THEN 
		              REPLACE(D.BENE_PHONE, SUBSTR(D.BENE_PHONE, 4, 4), '****')
		         ELSE
		          D.BENE_PHONE
		       END BENE_PHONE_STR,       (SELECT CER.TYPE
		          FROM DEV_CLM.T_CERTI_TYPE CER
		         WHERE CER.CODE = D.BENE_CERTI_TYPE) BENE_CERTI_TYPE,
		       CASE
		           WHEN LENGTH(D.BENE_CERTI_NO) = 18 AND
		              (D.BENE_CERTI_TYPE = '0' OR D.BENE_CERTI_TYPE = '5') THEN
		          REPLACE(D.BENE_CERTI_NO,
		                  SUBSTR(D.BENE_CERTI_NO, 4, 12),
		                  '************') WHEN D.BENE_CERTI_TYPE = '2'
		                     THEN SUBSTR(D.BENE_CERTI_NO, 1, 4) || '****'
		                  WHEN D.BENE_CERTI_TYPE = '1'
		                     THEN SUBSTR(D.BENE_CERTI_NO, 1, 5) || '****'
		         ELSE
		         D.BENE_CERTI_NO
		       END BENE_CERTI_NO,
		       D.BENE_CERTI_START,
		       D.BENE_CERTI_END,
		       SUBSTR(E.PAYEE_NAME, 1, 1) || DECODE(LENGTH(E.PAYEE_NAME),'2','****','****'||SUBSTR(E.PAYEE_NAME,length(E.PAYEE_NAME),1))  PAYEE_NAME,
		       DECODE(E.PAYEE_SEX, '1', '男', '2', '女', '未知') PAYEE_SEX_STR,
		       (SELECT A.COUNTRY_NAME FROM  DEV_CLM.T_COUNTRY A WHERE A.COUNTRY_CODE = E.PAYEE_NATION) PAYEE_NATION_STR,
		       (SELECT JOB.JOB_NAME
		          FROM DEV_CLM.T_JOB_CODE JOB
		         WHERE JOB.JOB_CODE = E.PAYEE_JOB_CODE) PAYEE_JOB_NAME,
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = E.PAYEE_STATE) ||
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = E.PAYEE_CITY
		           AND DIS.NAME NOT IN ('市辖区', '县')) ||
		       (SELECT DIS.NAME
		          FROM DEV_CLM.T_DISTRICT DIS
		         WHERE DIS.CODE = E.PAYEE_DISTRICT) ||  regexp_replace(E.PAYEE_ADDRESS,'\d','****')  PAYEE_ADRESS_STR,
		CASE
		         WHEN SUBSTR(E.PAYEE_PHONE, 1, 1) = '0' AND
		              (SUBSTR(E.PAYEE_PHONE, 2, 1) = '1' OR SUBSTR(E.PAYEE_PHONE, 2, 1) = '2') 
		              THEN
		         	  SUBSTR(E.PAYEE_PHONE, 1, 4) || '****'||SUBSTR(E.PAYEE_PHONE,length(E.PAYEE_PHONE)-1,2)
		         WHEN SUBSTR(E.PAYEE_PHONE, 1, 1) = '0' AND
		              (SUBSTR(E.PAYEE_PHONE, 2, 1) != '1' OR SUBSTR(E.PAYEE_PHONE, 2, 1) != '2')
		              THEN 
		              SUBSTR(E.PAYEE_PHONE, 1, 5) || '****'||SUBSTR(E.PAYEE_PHONE,length(E.PAYEE_PHONE)-1,2)
		              
		         WHEN LENGTH(E.PAYEE_PHONE) = 7
		              THEN 
		               '****'||SUBSTR(E.PAYEE_PHONE,length(E.PAYEE_PHONE)-1,2)
		         WHEN LENGTH(E.PAYEE_PHONE) = 8
		              THEN 
		               '****'||SUBSTR(E.PAYEE_PHONE,length(E.PAYEE_PHONE)-2,3)
		         WHEN LENGTH(E.PAYEE_PHONE) = 11 AND
		              SUBSTR(E.PAYEE_PHONE, 1, 1) = '1'
		              THEN 
		              REPLACE(E.PAYEE_PHONE, SUBSTR(E.PAYEE_PHONE, 4, 4), '****')
		         ELSE
		          E.PAYEE_PHONE
		       END PAYEE_PHONE_STR,       (SELECT CER.TYPE
		          FROM DEV_CLM.T_CERTI_TYPE CER
		         WHERE CER.CODE = E.PAYEE_CERTI_TYPE) PAYEE_CERTI_TYPE,
		       CASE
		         WHEN LENGTH(E.PAYEE_CERTI_NO) = 18 AND
		              (E.PAYEE_CERTI_TYPE = '0' OR E.PAYEE_CERTI_TYPE = '5') THEN
		          REPLACE(E.PAYEE_CERTI_NO,
		                  SUBSTR(E.PAYEE_CERTI_NO, 4, 12),
		                  '************')
		        WHEN E.PAYEE_CERTI_TYPE = '2'
		                     THEN SUBSTR(E.PAYEE_CERTI_NO, 1, 4) || '****'
		                  WHEN D.BENE_CERTI_TYPE = '1'
		                     THEN SUBSTR(E.PAYEE_CERTI_NO, 1, 5) || '****'
		         ELSE
		          E.PAYEE_CERTI_NO
		       END PAYEE_CERTI_NO,
		       E.PAYEE_CERTI_START,
		       E.PAYEE_CERTI_END,
		       (SELECT LA.RELATION_NAME
		          FROM DEV_CLM.T_LA_PH_RELA LA
		         WHERE LA.RELATION_CODE = C.INSURED_HOLIDER_RE) INSURED_HOLIDER_STR,
		       (SELECT LA.RELATION_NAME
		          FROM DEV_CLM.T_LA_PH_RELA LA
		         WHERE LA.RELATION_CODE = D.BENE_RELATION) BENE_INSURED_STR,
		       (SELECT LA.RELATION_NAME
		          FROM DEV_CLM.T_LA_PH_RELA LA
		         WHERE LA.RELATION_CODE = G.BENE_HOLDER_RELATION) BENE_HOLDER_STR,
		       (SELECT LA.RELATION_NAME
		          FROM DEV_CLM.T_LA_PH_RELA LA
		         WHERE LA.RELATION_CODE = G.PAYEE_RELATION) PAYEE_BENE_STR
		
		  FROM DEV_CLM.T_CLAIM_CASE             A,
		       DEV_CLM.T_CLAIM_INSURED          B,
		       DEV_CLM.T_CLAIM_INSURED_RELATION C,
		       DEV_CLM.T_CLAIM_BENE             D,
		       DEV_CLM.T_CLAIM_PAYEE            E,
		        (
		 SELECT DISTINCT PD.DEDUCTION_ID,
		                 (SELECT MAX(TCBP.OVER_COMP_PAY)
		                    FROM DEV_CLM.T_CLAIM_BUSI_PROD TCBP
		                   WHERE PD.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		                     AND PD.CASE_ID = TCBP.CASE_ID) OVER_COMP_PAY,
		                 PD.CASE_ID,
		                 PD.POLICY_CODE,
		                 PD.BUSI_PROD_CODE,
		                 PD.BUSI_ITEM_ID,
		                 PD.POLICY_ID,
		                 PD.RESTORE_BALANCE AS PAY_AMOUNT,
		                 '返还保费' AS ADJUST_TYPE,
		                 '1' AS ADJUST_CODE,
		                 CBP.ASSIGN_FLAG, /*返还保费不需要分配*/
		                 (CASE WHEN NVL(PD.CASH_DIVIDEND, 0.0) = 0.0 THEN  '0' ELSE  '1' END) AS CASH_FLAG,
		                 (CASE  WHEN NVL(PD.ACTUAL_PAY_BALANCE, 0.0) = 0.0 THEN '0' ELSE '1' END) AS ACTUAL_FLAG,
		                 (CASE WHEN NVL(PD.RESTORE_BALANCE, 0.0) = 0.0 THEN  '0' ELSE '1' END) AS RESTORE_FLAG,
		                 TCP.BENE_ID,
		                 TCP.PAYEE_ID,
		                 TCP.BENE_HOLDER_RELATION,
		                 TCP.PAYEE_RELATION
		   FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
		  INNER JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CBP
		     ON PD.BUSI_PROD_CODE = CBP.BUSI_PROD_CODE
		    AND PD.POLICY_CODE = CBP.POLICY_CODE
		    AND PD.CASE_ID = CBP.CASE_ID
		  INNER JOIN (SELECT P.BENE_ID,
		                     P.ADJUST_BUSI_ID,
		                     CFM.ARAP_CODE,
		                     (CASE
		             WHEN P.ADJUST_BUSI_ID IS NULL THEN
		                        P.PAY_AMOUNT
		                       WHEN CFM.ARAP_CODE = 01 THEN
		                        P.PAY_AMOUNT * -1
		                       WHEN CFM.ARAP_CODE = 02 THEN
		 P.PAY_AMOUNT
		                     END) PAY_AMOUNT,
		                     P.BUSI_ITEM_ID,
		                     P.POLICY_ID,
		                     P.PAYEE_ID,
		    P.BUSI_PROD_CODE,
		                     P.CASE_ID,
		                     P.BENE_HOLDER_RELATION,
		                     (CASE 
		                       WHEN P.PAYEE_RELATION IS NULL THEN
		                       PE.PAYEE_RELATION
		                       ELSE P.PAYEE_RELATION
		                       END) PAYEE_RELATION
		                FROM DEV_CLM.T_CLAIM_PAY P
		                INNER JOIN DEV_CLM.T_CLAIM_PAYEE PE
		                      ON P.CASE_ID = PE.CASE_ID
		                      AND P.PAYEE_ID = PE.PAYEE_ID
		               
		                LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CAB
		                  ON CAB.ADJUST_BUSI_ID = P.ADJUST_BUSI_ID
		                 AND CAB.CASE_ID = P.CASE_ID
		                LEFT JOIN DEV_CLM.T_CLAIM_FEE_MAPPING CFM
		                  ON CAB.ADJUST_TYPE = CFM.CLM_FEE_CODE
		               WHERE P.ADVANCE_FLAG != 1
		                 AND ((CAB.ADJUST_TYPE = '8' OR CAB.ADJUST_TYPE = '16' OR
		                     CAB.ADJUST_TYPE = '17' OR CAB.ADJUST_TYPE = '18') AND
		 P.ADJUST_BUSI_ID IS NOT NULL)) TCP
		     ON TCP.CASE_ID = PD.CASE_ID
		    AND TCP.POLICY_ID = PD.POLICY_ID
		    AND TCP.BUSI_PROD_CODE = PD.BUSI_PROD_CODE
		  WHERE PD.RESTORE_BALANCE != 0.0
		    AND (CBP.ADJUST_TYPE = '8' OR CBP.ADJUST_TYPE = '16' OR
		        CBP.ADJUST_TYPE = '17' OR CBP.ADJUST_TYPE = '18')
		 UNION ALL
		 SELECT DISTINCT PD.DEDUCTION_ID,
		                 (SELECT MAX(TCBP.OVER_COMP_PAY)
		                    FROM DEV_CLM.T_CLAIM_BUSI_PROD TCBP
		                   WHERE PD.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID
		                     AND PD.CASE_ID = TCBP.CASE_ID) OVER_COMP_PAY,
		                 PD.CASE_ID,
		                 PD.POLICY_CODE,
		                 PD.BUSI_PROD_CODE,
		                 PD.BUSI_ITEM_ID,
		                 PD.POLICY_ID,
		                 PD.ACTUAL_PAY_BALANCE AS PAY_AMOUNT,
		                 '理赔金' AS ADJUST_TYPE,
		                 '2' AS ADJUST_CODE,
		                 CBP.ASSIGN_FLAG,
		                 (CASE WHEN NVL(PD.CASH_DIVIDEND, 0.0) = 0.0 THEN '0' ELSE '1' END) AS CASH_FLAG,
		                 (CASE WHEN NVL(PD.ACTUAL_PAY_BALANCE, 0.0) = 0.0 THEN '0' ELSE '1' END) AS ACTUAL_FLAG,
		                 (CASE WHEN NVL(PD.RESTORE_BALANCE, 0.0) = 0.0 THEN '0' ELSE '1' END) AS RESTORE_FLAG,
		                 TCP.BENE_ID,
		                 TCP.PAYEE_ID,
		                 TCP.BENE_HOLDER_RELATION,
		                 TCP.PAYEE_RELATION
		   FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
		  INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
		     ON PD.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
		  INNER JOIN DEV_CLM.T_CLAIM_BUSI_PROD CBP
		     ON PD.BUSI_PROD_CODE = CBP.BUSI_PROD_CODE
		    AND PD.POLICY_CODE = CBP.POLICY_CODE
		    AND PD.CASE_ID = CBP.CASE_ID
		  INNER JOIN (SELECT P.BENE_ID,
		                     P.ADJUST_BUSI_ID,
		                     CFM.ARAP_CODE,
		                     (CASE
		                       WHEN P.ADJUST_BUSI_ID IS NULL THEN
		                        P.PAY_AMOUNT
		                       WHEN CFM.ARAP_CODE = 01 THEN
		                        P.PAY_AMOUNT * -1
		                       WHEN CFM.ARAP_CODE = 02 THEN
		                        P.PAY_AMOUNT
		                     END) PAY_AMOUNT,
		                     P.BUSI_ITEM_ID,
		                     P.POLICY_ID,
		                     P.PAYEE_ID,
		                     P.BUSI_PROD_CODE,
		                     P.CASE_ID,
		                     P.BENE_HOLDER_RELATION,
		                     (CASE 
		                       WHEN P.PAYEE_RELATION IS NULL THEN
		                       PE.PAYEE_RELATION
		                       ELSE P.PAYEE_RELATION
		                       END) PAYEE_RELATION
		                FROM DEV_CLM.T_CLAIM_PAY P
		                INNER JOIN DEV_CLM.T_CLAIM_PAYEE PE
		                      ON P.CASE_ID = PE.CASE_ID
		                      AND P.PAYEE_ID = PE.PAYEE_ID
		              
		                LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CAB
		                  ON CAB.ADJUST_BUSI_ID = P.ADJUST_BUSI_ID
		                 AND CAB.CASE_ID = P.CASE_ID
		                LEFT JOIN DEV_CLM.T_CLAIM_FEE_MAPPING CFM
		                  ON CAB.ADJUST_TYPE = CFM.CLM_FEE_CODE
		               WHERE P.ADVANCE_FLAG != 1
		                 AND ((P.ADJUST_BUSI_ID is null OR
		                     (P.ADJUST_BUSI_ID is not null and
		                     (CAB.ADJUST_TYPE != '20' and CAB.ADJUST_TYPE != '19' and
		                     CAB.ADJUST_TYPE != '8' and CAB.ADJUST_TYPE != '16' and
		                     CAB.ADJUST_TYPE != '17' and CAB.ADJUST_TYPE != '18'))))) TCP
		     ON TCP.CASE_ID = PD.CASE_ID
		    AND TCP.POLICY_ID = PD.POLICY_ID
		    AND TCP.BUSI_PROD_CODE = PD.BUSI_PROD_CODE
		  WHERE PD.ACTUAL_PAY_BALANCE != 0.0
		  UNION ALL
		   SELECT DISTINCT PD.DEDUCTION_ID,
		                 (SELECT MAX(TCBP.OVER_COMP_PAY) FROM DEV_CLM.T_CLAIM_BUSI_PROD TCBP WHERE PD.BUSI_ITEM_ID = TCBP.BUSI_ITEM_ID AND PD.CASE_ID = TCBP.CASE_ID) OVER_COMP_PAY,
		                 PD.CASE_ID,
		                 PD.POLICY_CODE,
		                 PD.BUSI_PROD_CODE,
		                 PD.BUSI_ITEM_ID,
		                 PD.POLICY_ID,
		                 PD.CASH_DIVIDEND AS PAY_AMOUNT,
		                 '现金红利' AS ADJUST_TYPE,
		                 '3' AS ADJUST_CODE,
		                 CBP.ASSIGN_FLAG, 
		                 (case when nvl(PD.Cash_Dividend,0.0)=0.0 then '0' else '1' end)  AS CASH_FLAG,
		                 (case when nvl(PD.Actual_Pay_Balance,0.0)=0.0 then '0' else '1' end) AS ACTUAL_FLAG,
		                 (case when nvl(PD.Restore_Balance,0.0)=0.0 then '0' else '1' end) AS RESTORE_FLAG,
		                 TCP.BENE_ID,
		                 TCP.PAYEE_ID,
		                 TCP.BENE_HOLDER_RELATION,
		                 TCP.PAYEE_RELATION
		   FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
		  INNER JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CBP
		     ON PD.BUSI_PROD_CODE = CBP.BUSI_PROD_CODE
		    AND PD.POLICY_CODE = CBP.POLICY_CODE
		    AND PD.CASE_ID = CBP.CASE_ID
		  INNER JOIN (SELECT P.BENE_ID,
		                     P.ADJUST_BUSI_ID,
		                     CFM.ARAP_CODE,
		                     (CASE
		             WHEN P.ADJUST_BUSI_ID IS NULL THEN
		                        P.PAY_AMOUNT
		                       WHEN CFM.ARAP_CODE = 01 THEN
		                        P.PAY_AMOUNT * -1
		                       WHEN CFM.ARAP_CODE = 02 THEN
		 P.PAY_AMOUNT
		                     END) PAY_AMOUNT,
		                     P.BUSI_ITEM_ID,
		                     P.POLICY_ID,
		                     P.PAYEE_ID,
		    P.BUSI_PROD_CODE,
		                     P.CASE_ID,
		                     P.BENE_HOLDER_RELATION,
		                (CASE 
		                   WHEN P.PAYEE_RELATION IS NULL THEN
		                   PE.PAYEE_RELATION
		                   ELSE P.PAYEE_RELATION
		                   END) PAYEE_RELATION
		                FROM DEV_CLM.T_CLAIM_PAY P
		                INNER JOIN DEV_CLM.T_CLAIM_PAYEE PE
		                      ON P.CASE_ID = PE.CASE_ID
		                      AND P.PAYEE_ID = PE.PAYEE_ID
		                
		                LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CAB
		                  ON CAB.ADJUST_BUSI_ID = P.ADJUST_BUSI_ID
		                 AND CAB.CASE_ID = P.CASE_ID
		                LEFT JOIN DEV_CLM.T_CLAIM_FEE_MAPPING CFM
		                  ON CAB.ADJUST_TYPE = CFM.CLM_FEE_CODE
		               WHERE P.ADVANCE_FLAG != 1
		                 AND ((CAB.ADJUST_TYPE = '19' OR CAB.ADJUST_TYPE = '20'))) TCP
		     ON TCP.CASE_ID = PD.CASE_ID
		    AND TCP.POLICY_ID = PD.POLICY_ID
		    AND TCP.BUSI_PROD_CODE = PD.BUSI_PROD_CODE
		  WHERE PD.CASH_DIVIDEND != 0.0
		    AND (CBP.ADJUST_TYPE = '19' OR CBP.ADJUST_TYPE = '20'))            G
		 WHERE A.CASE_ID = B.CASE_ID
		   AND A.CASE_ID = C.CASE_ID
		   AND A.CASE_ID = D.CASE_ID
		   AND A.CASE_ID = E.CASE_ID
		   AND A.CASE_ID = G.CASE_ID
		   AND G.CASE_ID = C.CASE_ID
		   AND G.POLICY_CODE = C.POLICY_CODE
		   AND G.POLICY_ID = C.POLICY_ID
		   AND G.PAYEE_ID = E.PAYEE_ID
		   AND G.BENE_ID = D.BENE_ID
		   AND A.CASE_STATUS!=99]]>
		
			<if test="organ_code != null and organ_code != ''">
		    	<![CDATA[ and A.ORGAN_CODE like '${organ_code}%']]>
		    </if>
		    <if test="start_endCase_time != null and start_endCase_time != ''"><![CDATA[and A.APPROVE_TIME >= #{start_endCase_time}]]></if>
			<if test="end_end_case_time != null and end_end_case_time != ''"><![CDATA[and A.APPROVE_TIME <= #{end_end_case_time}]]></if>
		<![CDATA[
		  )
		]]>
	</select>
	
	<!-- 根据赔案号caseId去重查询保单数据 -->
	<select id="findInsurancePolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT distinct A.POLICY_CODE
			 FROM DEV_CLM.T_CLAIM_PAY A WHERE 1 = 1  ]]>
	<include refid="claimPayWhereCondition" />
	</select>
	
	<!-- 分页查询风险保单清单 -->
	<select id="isDiseasePolicyForPages" parameterType="java.util.Map"
		resultType="java.util.Map">
		<![CDATA[
		  SELECT C.* FROM ( SELECT ROWNUM RN,B.* FROM (
        ]]>
		<include refid="isDiseasePolicySql" />
		<![CDATA[
                )  B WHERE ROWNUM<=#{LESS_NUM}) C WHERE C.RN >#{GREATER_NUM}
         ]]>
	</select>

	<select id="isDiseasePolicyForPageTotals" parameterType="java.util.Map"
		resultType="java.lang.Integer">
		<![CDATA[ select count(1) from ( ]]>
		<include refid="isDiseasePolicySql" />
		<![CDATA[   )  B ]]>
	</select>

	<sql id="isDiseasePolicySql">
		<![CDATA[
		SELECT t.*
		from (SELECT substr(CM.ORGAN_CODE, 1, 4) Second_Organ_Code,
			       substr(CM.ORGAN_CODE, 1, 6) Third_Organ_Code,
			       CM.POLICY_CODE,
			       bp.product_name_sys as product_Name_Sys,
			       cbp.busi_prod_code,
			       cbp.busi_item_id,
			       (SELECT sum(cp.amount)
			          FROM dev_pas.t_contract_product cp
			         where cp.busi_item_id=cbp.busi_item_id) as amount,
			       '' as risk_level,
			       '' as risk_score,
			       (SELECT cu.customer_name
			          FROM dev_pas.t_policy_holder ph
			         inner join dev_pas.t_customer cu
			            on ph.customer_id = cu.customer_id
			         where ph.policy_id = cm.policy_id) as POLICY_HOLDER_NAME,
			       (SELECT LISTAGG(cu.customer_name, ', ') WITHIN GROUP(ORDER BY cu.customer_name)
			          FROM dev_pas.t_insured_list il
			         inner join dev_pas.t_customer cu
			            on il.customer_id = cu.customer_id
			         where il.policy_id = cm.policy_id) as CUSTOMER_NAME,
			       (SELECT LISTAGG(cu.customer_certi_code, ', ') WITHIN GROUP(ORDER BY cu.customer_name)
			          FROM dev_pas.t_insured_list il
			         inner join dev_pas.t_customer cu
			            on il.customer_id = cu.customer_id
			         where il.policy_id = cm.policy_id) as CUSTOMER_CERTI_CODE,
			       ca.agent_code,
			       ca.agent_name,
			       so1.sales_organ_code organ_code_group,
			       so2.sales_organ_code organ_code_stry,
			       so3.sales_organ_code organ_code_area,
			       so1.sales_organ_name organ_code_group_name,
			       so2.sales_organ_name organ_code_stry_name,
			       so3.sales_organ_name organ_code_area_name,
			       (SELECT LS.STATUS_NAME
			          FROM DEV_PAS.T_Liability_Status LS
			         WHERE LS.STATUS_CODE = CM.LIABILITY_STATE) as liability_state,
			       case
			         when exists (SELECT 1
			                 FROM dev_clm.t_contract_master cm1
			                inner join dev_clm.t_claim_case cc
			                   on cm1.case_id = cc.case_id
			                WHERE cc.case_status not in ('10', '20', '21')
			                  and cm1.policy_code = CM.POLICY_CODE) then
			          '是'
			         else
			          '否'
			       end is_case,
			       case
			         when exists (SELECT 1
			                 FROM DEV_CLM.T_SURVEY_APPLY SA
			                WHERE SA.BIZ_TYPE = '5'
			                  AND CM.POLICY_CODE = SA.POLICY_CODE) THEN
			          '是'
			         ELSE
			          '否'
			       END is_survey,
             	   ROW_NUMBER() over(PARTITION BY cm.policy_code  ORDER BY cbp.risk_score DESC)  as riskMax
			  FROM dev_PAS.t_contract_master cm
			  inner join dev_pas.t_contract_busi_prod cbp 
			    on cbp.policy_code=cm.policy_code
			   and cbp.master_busi_item_id is null
			  inner join dev_pds.t_business_product bp
			    on bp.product_code_sys=cbp.busi_prod_code
			  left join DEV_PAS.T_CONTRACT_AGENT CA
			    on CA.IS_NB_AGENT = '1'
			   AND CA.POLICY_ID = CM.POLICY_ID
			  left join dev_pas.t_agent ag
			    on ag.agent_code = ca.agent_code
			  left join dev_pas.t_sales_organ so1
			    on ag.group_code = so1.sales_organ_code
			  left join dev_pas.t_sales_organ so2
			    on so1.parent_code = so2.sales_organ_code
			  left join dev_pas.t_sales_organ so3
			    on so2.parent_code = so3.sales_organ_code
			 where 1 = 1 and bp.product_category3='40006' and bp.product_category4='50001']]>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND CM.ORGAN_CODE like '${organ_code}%'  ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND CM.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" (customer_certi_code != null and customer_certi_code != '')  or (customer_id != null and customer_id != '')">
			<![CDATA[
				AND EXISTS (SELECT 1
		          FROM dev_pas.t_insured_list il
		         inner join dev_pas.t_customer cu
		            on il.customer_id = cu.customer_id
		         where 1=1 ]]>
		         <if test=" customer_certi_code != null and customer_certi_code != ''  ">
		         	 <![CDATA[and cu.customer_certi_code = #{customer_certi_code}]]>
		         </if>
		         <if test=" customer_id != null and customer_id != ''  ">
		            <![CDATA[and cu.customer_id = #{customer_id}]]>
		         </if>
		          
		         <![CDATA[and il.policy_id = cm.policy_id) ]]>
		     
		</if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND ca.agent_code = #{agent_code} ]]></if>
		<if test=" startTime != null and startTime != '' "><![CDATA[ AND cm.VALIDATE_DATE >= #{startTime, jdbcType=DATE} ]]></if>
		<if test=" endTime != null  and endTime != '' "><![CDATA[ AND cm.VALIDATE_DATE <= #{endTime, jdbcType=DATE} ]]></if>
		<if test=" organ_code_group != null and organ_code_group != ''  "><![CDATA[ and so1.SALES_ORGAN_CODE = #{organ_code_group}]]></if>
		<if test=" organ_code_stry != null and organ_code_stry != ''  "><![CDATA[ and so2.SALES_ORGAN_CODE = #{organ_code_stry}]]></if>
		<if test=" organ_code_area != null and organ_code_area != ''  "><![CDATA[ and so3.SALES_ORGAN_CODE = #{organ_code_area}]]></if>
		<![CDATA[) t where riskMax='1']]>
	</sql>



	<!-- 分页查询保后调查任务清单 -->
	<select id="queryBfSurveyTasksDetailForPage" parameterType="java.util.Map"
		resultType="java.util.Map">
	<![CDATA[ 
	select A.ORGAN_CODE,A.POLICY_CODE,A.LIABILITY_STATE,to_char(A.VALIDDATE_DATE,'yyyy-mm-dd')
	,B.PRODUCT_DESC,C.AMOUNT,D.RISK_LEVEL_LIAB,D.RISK_FRACTION,E.AGENT_NAME,
      E.AGENT_CODE,F.IS_SURVEY,
      (select customer_name
      from dev_clm.T_CUSTOMER C,DEV_CLM.T_POLICY_HOLDER H
      where C.CUSTOMER_ID = H.CUSTOMER_ID
      and H.POLICY_CODE=A.POLICY_CODE) policyHolderName,
      (select CUSTOMER_NAME
      from dev_clm.T_CUSTOMER C,DEV_CLM.T_INSURED_LIST L
      where C.CUSTOMER_ID = L.CUSTOMER_ID
      and L.POLICY_CODE=A.POLICY_CODE ) customerName,
      (select CUSTOMER_CERTI_CODE
      from dev_clm.T_CUSTOMER C,DEV_CLM.T_INSURED_LIST L
      where C.CUSTOMER_ID = L.CUSTOMER_ID
      and L.POLICY_CODE=A.POLICY_CODE ) customerCertiCode,
      substr(I.ORGAN_CODE,1,4) SecondOrganCode,
      substr(I.ORGAN_CODE,1,6) ThirdOrganCode,
      K.ORGAN_CODE_AREA ORGAN_CODE_AREA, 
      K.ORGAN_CODE_STRY ORGAN_CODE_STRY, 
      K.ORGAN_CODE_GROUP ORGAN_CODE_GROUP, 
      (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_AREA) ORGAN_CODE_AREA_NAME,
      (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_STRY) ORGAN_CODE_STRY_NAME,
      (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_GROUP) ORGAN_CODE_GROUP_NAME,
      K.IS_CLAIM,
      M.SURVEY_ITEM,
      N.SURVEY_DESC
      from dev_clm.t_contract_master A,
      DEV_CLM.t_business_product B,
      DEV_CLM.t_contract_product C,
      DEV_CLM.T_CLAIM_RISK_LEVEL_LIAB D,
      DEV_CLM.T_CONTRACT_AGENT E,
      DEV_CLM.T_CLAIM_CASE F,
      DEV_CLM.T_CONTRACT_BUSI_PROD G,
      DEV_CLM.T_CUSTOMER H, 
      DEV_CLM.T_CLAIM_SURVEY_TASK  I,
      DEV_CLM.T_SURVEY_APPLY J,
      DEV_PAS.T_CLAIM_BF_SURVEY_PLAN K,
      DEV_PAS.T_CLAIM_SURVEY_BATCH L,
      DEV_CLM.T_SURVEY_ITEM M,
      DEV_CLM.T_SURVEY_APPLY N
      where A.POLICY_CODE=C.POLICY_CODE
      and A.POLICY_CODE=D.POLICY_CODE
      and A.POLICY_CODE=E.POLICY_CODE
      and A.CASE_ID=F.CASE_ID
      and A.POLICY_CODE=G.POLICY_CODE
      and G.BUSI_PROD_CODE=B.PRODUCT_CODE_SYS
      AND I.LIST_ID = J.SURVEY_RULE_ID
          AND J.CASE_ID = A.CASE_ID
          AND I.BATCH_ID = L.BATCH_ID
          AND L.PLAN_ID = K.PLAN_ID
          AND A.POLICY_CODE = N.POLICY_CODE
          AND N.APPLY_ID = M.APPLY_ID
          ]]>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" plan_name != null and plan_name != ''  "><![CDATA[ AND M.SURVEY_ITEM = #{plan_name} ]]></if>
		<if test=" startTime != null and startTime != ''  "><![CDATA[ AND A.VALIDDATE_DATE >= #{startTime} ]]></if>
		<if test=" endTime != null and endTime != ''  "><![CDATA[ AND A.VALIDDATE_DATE <= #{endTime} ]]></if>
			<![CDATA[   WHERE
      ROWNUM <= #{LESS_NUM}  WHERE B.RN > #{GREATER_NUM}   ]]>
	</select>

	<select id="queryBfSurveyTasksDetailForPageTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
		<![CDATA[SELECT COUNT(1) from(select A.ORGAN_CODE,A.POLICY_CODE,A.LIABILITY_STATE,to_char(A.VALIDDATE_DATE,'yyyy-mm-dd')
	,B.PRODUCT_DESC,C.AMOUNT,D.RISK_LEVEL_LIAB,D.RISK_FRACTION,E.AGENT_NAME,
      E.AGENT_CODE,F.IS_SURVEY,
      (select customer_name
      from dev_clm.T_CUSTOMER C,DEV_CLM.T_POLICY_HOLDER H
      where C.CUSTOMER_ID = H.CUSTOMER_ID
      and H.POLICY_CODE=A.POLICY_CODE) policyHolderName,
      (select CUSTOMER_NAME
      from dev_clm.T_CUSTOMER C,DEV_CLM.T_INSURED_LIST L
      where C.CUSTOMER_ID = L.CUSTOMER_ID
      and L.POLICY_CODE=A.POLICY_CODE ) customerName,
      (select CUSTOMER_CERTI_CODE
      from dev_clm.T_CUSTOMER C,DEV_CLM.T_INSURED_LIST L
      where C.CUSTOMER_ID = L.CUSTOMER_ID
      and L.POLICY_CODE=A.POLICY_CODE ) customerCertiCode,
      substr(I.ORGAN_CODE,1,4) SecondOrganCode,
      substr(I.ORGAN_CODE,1,6) ThirdOrganCode,
      K.ORGAN_CODE_AREA ORGAN_CODE_AREA, 
      K.ORGAN_CODE_STRY ORGAN_CODE_STRY, 
      K.ORGAN_CODE_GROUP ORGAN_CODE_GROUP, 
      (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_AREA) ORGAN_CODE_AREA_NAME,
      (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_STRY) ORGAN_CODE_STRY_NAME,
      (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_GROUP) ORGAN_CODE_GROUP_NAME,
      K.IS_CLAIM,
      M.SURVEY_ITEM,
      N.SURVEY_DESC
      from dev_clm.t_contract_master A,
      DEV_CLM.t_business_product B,
      DEV_CLM.t_contract_product C,
      DEV_CLM.T_CLAIM_RISK_LEVEL_LIAB D,
      DEV_CLM.T_CONTRACT_AGENT E,
      DEV_CLM.T_CLAIM_CASE F,
      DEV_CLM.T_CONTRACT_BUSI_PROD G,
      DEV_CLM.T_CUSTOMER H, 
      DEV_CLM.T_CLAIM_SURVEY_TASK  I,
      DEV_CLM.T_SURVEY_APPLY J,
      DEV_PAS.T_CLAIM_BF_SURVEY_PLAN K,
      DEV_PAS.T_CLAIM_SURVEY_BATCH L,
      DEV_CLM.T_SURVEY_ITEM M,
      DEV_CLM.T_SURVEY_APPLY N
      where A.POLICY_CODE=C.POLICY_CODE
      and A.POLICY_CODE=D.POLICY_CODE
      and A.POLICY_CODE=E.POLICY_CODE
      and A.CASE_ID=F.CASE_ID
      and A.POLICY_CODE=G.POLICY_CODE
      and G.BUSI_PROD_CODE=B.PRODUCT_CODE_SYS
      AND I.LIST_ID = J.SURVEY_RULE_ID
          AND J.CASE_ID = A.CASE_ID
          AND I.BATCH_ID = L.BATCH_ID
          AND L.PLAN_ID = K.PLAN_ID
          AND A.POLICY_CODE = N.POLICY_CODE
          AND N.APPLY_ID = M.APPLY_ID
          ]]>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" plan_name != null and plan_name != ''  "><![CDATA[ AND M.SURVEY_ITEM = #{plan_name} ]]></if>
		<if test=" startTime != null and startTime != ''  "><![CDATA[ AND A.VALIDDATE_DATE >= #{startTime} ]]></if>
		<if test=" endTime != null and endTime != ''  "><![CDATA[ AND A.VALIDDATE_DATE <= #{endTime} ]]></if>
			<![CDATA[  ) R ]]>
	</select>
	
	<!-- 分页查询理赔直连案件清单 -->	<!-- ''空串字段在service层单独处理 -->
	<select  id="findQueryBfSurveyTasksDetailForPage" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[ 
			SELECT TT.RN as ROWNUMER, TT.* FROM(
				SELECT ROWNUM RN,M.* FROM(SELECT SA.APPLY_ID,
					       CM.POLICY_CODE,
					       substr(CM.ORGAN_CODE, 1, 4) SecondOrganCode,
					       substr(CM.ORGAN_CODE, 1, 6) ThirdOrganCode,
					       SA.Apply_Per,
					       (SELECT cbp.busi_prod_code
									  FROM dev_pas.t_contract_busi_prod cbp
									 where cbp.busi_item_id =
									       (SELECT max(cbp1.busi_item_id)
									          FROM dev_pas.t_contract_busi_prod cbp1
									         where cbp1.master_busi_item_id is null
									           and cbp1.policy_code = cbp.policy_code)
									   and cbp.policy_id = cm.policy_id) as busi_prod_code,
					       (SELECT LISTAGG(cu.customer_name, ', ') WITHIN GROUP(ORDER BY cu.customer_name)
					          FROM dev_pas.t_insured_list il
					         inner join dev_pas.t_customer cu
					            on il.customer_id = cu.customer_id
					         where il.policy_id = cm.policy_id) as CUSTOMER_NAME,
					       (SELECT LISTAGG(cu.customer_certi_code, ', ') WITHIN GROUP(ORDER BY cu.customer_name)
					          FROM dev_pas.t_insured_list il
					         inner join dev_pas.t_customer cu
					            on il.customer_id = cu.customer_id
					         where il.policy_id = cm.policy_id) as CUSTOMER_CERTI_CODE,
					       (SELECT bp.product_name_sys
								  FROM dev_pas.t_contract_busi_prod cbp
								   inner join dev_pds.t_business_product bp
								   on cbp.busi_prod_code = bp.product_code_sys
								 where cbp.busi_item_id =
								       (SELECT max(cbp1.busi_item_id)
								          FROM dev_pas.t_contract_busi_prod cbp1
								         where cbp1.master_busi_item_id is null
								           and cbp1.policy_code = cbp.policy_code)
								   and cbp.policy_id = cm.policy_id) as product_name_sys,
					       to_char(cm.Initial_Validate_Date, 'YYYY-MM-DD') Initial_Validate_Date,
					       '' as RISK_LEVEL_LIAB,
					       '' as RISK_FRACTION,
					       (SELECT ca.agent_code
					          FROM dev_pas.t_contract_agent ca
					         where ca.is_nb_agent = '1'
					           and ca.policy_id = cm.policy_id) as agent_code,
					       (SELECT ca.agent_name
					          FROM dev_pas.t_contract_agent ca
					         where ca.is_nb_agent = '1'
					           and ca.policy_id = cm.policy_id) as agent_name,
					       (SELECT so3.sales_organ_code || '/' || so2.sales_organ_code || '/' ||
					               so1.sales_organ_code
					          FROM dev_pas.t_contract_agent ca
					         inner join dev_pas.t_agent ag
					            on ag.agent_code = ca.agent_code
					         inner join dev_pas.t_sales_organ so1
					            on ag.group_code = so1.sales_organ_code
					         inner join dev_pas.t_sales_organ so2
					            on so1.parent_code = so2.sales_organ_code
					         inner join dev_pas.t_sales_organ so3
					            on so2.parent_code = so3.sales_organ_code
					         where ca.is_nb_agent = '1'
					           and ca.policy_id = cm.policy_id) as organ_code_group,
					       (SELECT so3.sales_organ_name || '/' || so2.sales_organ_name || '/' ||
					               so1.sales_organ_name
					          FROM dev_pas.t_contract_agent ca
					         inner join dev_pas.t_agent ag
					            on ag.agent_code = ca.agent_code
					         inner join dev_pas.t_sales_organ so1
					            on ag.group_code = so1.sales_organ_code
					         inner join dev_pas.t_sales_organ so2
					            on so1.parent_code = so2.sales_organ_code
					         inner join dev_pas.t_sales_organ so3
					            on so2.parent_code = so3.sales_organ_code
					         where ca.is_nb_agent = '1'
					           and ca.policy_id = cm.policy_id) as organ_code_group_name,
					       (SELECT LISTAGG(sit.value, ', ') WITHIN GROUP(ORDER BY sit.value)
					          FROM dev_clm.t_survey_item suri
					         inner join DEV_CLM.T_SURVEY_ITEM_TYPE sit
					            on suri.survey_item = trim(sit.code)
					         where suri.SURVEY_ITEM IS NOT NULL and suri.apply_id = sa.apply_id) SURVEY_ITEM,
					       SA.SURVEY_DESC
					  FROM DEV_clm.T_SURVEY_APPLY SA
					 INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
					    ON SA.POLICY_CODE = CM.POLICY_CODE
					 INNER JOIN DEV_CLM.t_claim_survey_task CST
					    ON CST.LIST_ID = SA.SURVEY_RULE_ID
					 INNER JOIN DEV_CLM.T_CLAIM_SURVEY_BATCH CSB
					    ON CSB.BATCH_ID = CST.BATCH_ID
					  LEFT JOIN DEV_CLM.T_SURVEY_CONCLUSION SC
					    ON SC.APPLY_ID = SA.APPLY_ID
					 WHERE SA.BIZ_TYPE = '5'
		]]>
        <if test=" plan_name  != null and plan_name !='' "><![CDATA[ AND CSB.PLAN_NAME like '%'||${plan_name}||'%']]></if>
        <if test=" startTime  != null and startTime !='' "><![CDATA[AND SA.Apply_Date >= #{startTime}]]></if>
       <if test=" endTime  != null and endTime !='' "><![CDATA[AND SA.Apply_Date <= #{endTime} ]]></if>
	   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND CM.Organ_Code like ${organ_code}||'%' ]]></if>
	<![CDATA[ ) M where ROWNUM <= #{LESS_NUM} ) TT WHERE TT.RN > #{GREATER_NUM}]]>
	</select>
	
	<select id="findQueryBfSurveyTasksDetailForPageTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[SELECT COUNT(1) FROM DEV_clm.T_SURVEY_APPLY SA
					 INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
					    ON SA.POLICY_CODE = CM.POLICY_CODE
					 INNER JOIN DEV_CLM.t_claim_survey_task CST
					    ON CST.LIST_ID = SA.SURVEY_RULE_ID
					 INNER JOIN DEV_CLM.T_CLAIM_SURVEY_BATCH CSB
					    ON CSB.BATCH_ID = CST.BATCH_ID
					  LEFT JOIN DEV_CLM.T_SURVEY_CONCLUSION SC
					    ON SC.APPLY_ID = SA.APPLY_ID
					 WHERE SA.BIZ_TYPE = '5'
			]]>
        <if test=" plan_name  != null and plan_name !='' "><![CDATA[ AND CSB.PLAN_NAME like '%'||${plan_name}||'%']]></if>
        <if test=" startTime  != null and startTime !='' "><![CDATA[AND SA.Apply_Date >= #{startTime}]]></if>
        <if test=" endTime  != null and endTime !='' "><![CDATA[AND SA.Apply_Date <= #{endTime} ]]></if>
	    <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND CM.Organ_Code like ${organ_code}||'%' ]]></if>
	</select>

	<!-- 查询个数操作 -->
	<select id="findSalesOrganTotals" resultType="java.lang.Integer"
		parameterType="java.util.Map">
		<![CDATA[ select count(1)from (select ROWNUM RN, A.SALES_ORGAN_CODE as SALES_ORGAN_CODE_A,
       A.SALES_ORGAN_NAME as SALES_ORGAN_NAME_A,
       B.SALES_ORGAN_CODE as SALES_ORGAN_CODE_B,
       B.SALES_ORGAN_NAME as SALES_ORGAN_NAME_B,
       C.SALES_ORGAN_CODE as SALES_ORGAN_CODE_C,
       C.SALES_ORGAN_NAME AS SALES_ORGAN_NAME_C
  from APP___CLM__DBUSER.t_Sales_Organ A
right JOIN APP___CLM__DBUSER.t_Sales_Organ B
    ON A.PARENT_CODE=B.SALES_ORGAN_CODE
   AND B.ORGAN_LEVEL_CODE='2'
   AND B.STATE=0  
  AND A.STATE = 0 AND A.ORGAN_LEVEL_CODE=3
right JOIN APP___CLM__DBUSER.t_Sales_Organ C
    ON B.PARENT_CODE=C.SALES_ORGAN_CODE
   AND C.ORGAN_LEVEL_CODE='1'
   AND C.STATE=0 
 where 1=1]]>
		<if test=" sales_organ_namea != null and sales_organ_namea != ''"><![CDATA[ AND A.Sales_Organ_Name like '%${sales_organ_namea}%' ]]></if>
		<if test=" sales_organ_nameb != null and sales_organ_nameb != ''"><![CDATA[ AND B.Sales_Organ_Name like '%${sales_organ_nameb}%' ]]></if>
		<if test=" sales_organ_namec != null and sales_organ_namec != ''"><![CDATA[ AND C.Sales_Organ_Name like '%${sales_organ_namec}%' ]]></if>
 		<![CDATA[) B ]]></select>
	<!-- 分页查询操作 -->
	<select id="findSalesOrganForPage" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT B.RN AS rowNumber,B.SALES_ORGAN_CODE_A,
		B.SALES_ORGAN_NAME_A,
		B.SALES_ORGAN_CODE_B,
		B.SALES_ORGAN_NAME_B,
		B.SALES_ORGAN_CODE_C,
		B.SALES_ORGAN_NAME_C from( select ROWNUM RN, A.SALES_ORGAN_CODE as SALES_ORGAN_CODE_A,
       A.SALES_ORGAN_NAME as SALES_ORGAN_NAME_A,
       B.SALES_ORGAN_CODE as SALES_ORGAN_CODE_B,
       B.SALES_ORGAN_NAME as SALES_ORGAN_NAME_B,
       C.SALES_ORGAN_CODE as SALES_ORGAN_CODE_C,
       C.SALES_ORGAN_NAME AS SALES_ORGAN_NAME_C
  from APP___PAS__DBUSER.t_Sales_Organ A
right JOIN APP___PAS__DBUSER.t_Sales_Organ B
    ON A.PARENT_CODE=B.SALES_ORGAN_CODE
   AND B.ORGAN_LEVEL_CODE='2'
   AND B.STATE=0 
   AND A.STATE = 0 
   AND A.ORGAN_LEVEL_CODE=3 
right JOIN APP___PAS__DBUSER.t_Sales_Organ C
    ON B.PARENT_CODE=C.SALES_ORGAN_CODE
   AND C.ORGAN_LEVEL_CODE='1'
   AND C.STATE=0 
 where 1=1]]>
		<if test=" sales_organ_namea != null and sales_organ_namea != ''"><![CDATA[ AND A.Sales_Organ_Name like '%${sales_organ_namea}%' ]]></if>
		<if test=" sales_organ_nameb != null and sales_organ_nameb != ''"><![CDATA[ AND B.Sales_Organ_Name like '%${sales_organ_nameb}%' ]]></if>
		<if test=" sales_organ_namec != null and sales_organ_namec != ''"><![CDATA[ AND C.Sales_Organ_Name like '%${sales_organ_namec}%' ]]></if>
 		<![CDATA[AND ROWNUM <= #{LESS_NUM}) B WHERE B.RN > #{GREATER_NUM}]]>
	</select>
</mapper>
