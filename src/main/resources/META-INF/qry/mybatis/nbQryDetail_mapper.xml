<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.NbQryDaoImpl">

	<!-- 根据投保单号查询所有操作 -->
	<select id="NB_findAllPreAuditMasterByApplyCodedetail"
		resultType="java.util.Map" parameterType="java.util.Map">
			<![CDATA[ SELECT CL.CUSTOMER_LEVEL_DESC CUSTOMER_LEVEL_DESC, A.AUDIT_ORGAN_CODE, A.EXHIBITION_VALID, A.AGENT_MOBILE,  A.APPLY_CODE, 
      A.CONCLUSION_CODE, A.MANUAL_UW_INDI, A.BIR<PERSON>Y_INDI,AL.AGENT_LEVEL_DESC , A.AUDIT_ID, 
      <PERSON><PERSON>_DATE,  <PERSON><PERSON>EN<PERSON>_VALID, A.AUDIT_BY, A.HIGH_SA_INDI, 
      A.AGENT_SINCERITY_LEVEL, A.INPUT_DATE, A.MANUAL_UW_TIP, 
      A.AGENT_CODE, A.QUALIFICATION_VALID,T.AGENT_NAME FROM    DEV_NB.T_PRE_AUDIT_MASTER   A 
      LEFT JOIN DEV_NB.T_AGENT_LEVEL AL ON AL.AGENT_LEVEL_CODE = to_char(A.AGENT_LEVEL) 
      LEFT JOIN DEV_NB.T_CUSTOMER_LEVEL CL ON CL.CUSTOMER_LEVEL_CODE = A.CUSTOMER_LEVEL 
      LEFT JOIN DEV_PAS.T_AGENT T ON T.AGENT_CODE = A.AGENT_CODE
      WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.AUDIT_ID           ]]>
	</select>

	<sql id="NB_queryByApplyCodeCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>

	<!-- 根据auditId查询所有操作 -->
	<select id="NB_findAllPreAuditCauseByAuditId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CAUSE_CODE, A.COMMENTS, A.AUDIT_ID, A.LIST_ID,A.INSERT_TIME FROM DEV_NB.T_PRE_AUDIT_CAUSE A
		 WHERE ROWNUM <=  1000  AND A.AUDIT_ID = #{audit_id} ]]>
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>

	<!-- 查询所有操作 -->
	<select id="NB_findAllPreAuditCauseDesc" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CAUSE_CODE, A.CAUSE_DESC FROM dev_nb.T_PRE_AUDIT_CAUSE_DESC      A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<!-- <![CDATA[ ORDER BY A.CAUSE_CODE ]]> -->
	</select>

	<!-- 按索引查询操作 -->
	<select id="NB_findMasterByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, A.CHANNEL_ORG_CODE, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,A.APPOINT_VALIDATE FROM DEV_NB.T_NB_CONTRACT_MASTER        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_ID          ]]>
	</select>

	<!-- 按索引生成的查询条件 -->
	<sql id="NB_queryByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</sql>

	<select id="NB_findAllQuestionaireForObject" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_MODULE_RESULT, A.CUSTOMER_ID, A.QUESTIONAIRE_OBJECT, A.APPLY_CODE, A.SURVEY_QUESTION_ID, 
			A.CUSTOMER_SURVEY_ID, A.POLICY_CODE, A.POLICY_ID, C.SURVEY_VERSION_TYPE,
			A.AGENT_CODE, B.SURVEY_VERSION, B.SURVEY_CODE,B.QUESTION_CONTENT,B.SURVEY_PARAM_MODULE,B.REMARK FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER A ,DEV_NB.T_QUESTIONAIRE_INFO B ,DEV_NB.T_QUESTIONAIRE_SURVEY_VERSION C WHERE A.SURVEY_QUESTION_ID = B.SURVEY_QUESTION_ID AND B.SURVEY_VERSION = C.SURVEY_VERSION_CODE ]]>
		<include refid="NB_questionaireCustomerPolicyIDCondition" />
		<include refid="NB_questionaireCustomerApplyCodeCondition" />
		<include refid="NB_questionaireCustomerCustomerIDCondition" />
		<include refid="NB_questionaireCustomerAgentCodeCondition" />
		<include refid="NB_questionaireCustomerObjectCondition" />
		<include refid="NB_questionaireSurveyVersionTypeCondition" />
		<![CDATA[ ORDER BY A.CUSTOMER_SURVEY_ID ]]>
	</select>

	<select id="NB_findAllQuestionaireParamForObject" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_SURVEY_ID, A.SURVEY_PARAM, A.SURVEY_PARAMS_ID, A.SURVEY_ID,
			 B.SURVEY_QUESTION_ID, B.QUESTION_ORDER, B.SURVEY_NAME, B.SURVEY_INPUT_TYPE, B.SURVEY_INPUT_LIST_VALUE FROM DEV_NB.T_QUESTIONAIRE_CUSTOMER_PARAM A,DEV_NB.T_QUESTIONAIRE_PARAM B WHERE A.SURVEY_ID = B.SURVEY_ID  ]]>
		<include
			refid="NB_queryQuestionaireCustomerParamByCustomerSurveyIdCondition" />
		<![CDATA[ ORDER BY A.SURVEY_PARAMS_ID ]]>
	</select>

	<!-- 查询告知版别的码表列表 -->
	<select id="NB_findAllSurveyVersionCodeAndSurveyVersionName"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_VERSION_CODE as SURVEY_VERSION, A.SURVEY_VERSION_NAME,A.SURVEY_VERSION_TYPE,A.OLD_SYS_FLAG FROM DEV_NB.T_QUESTIONAIRE_SURVEY_VERSION A WHERE 
		ROWNUM <=  1000 AND A.OLD_SYS_FLAG=0 ]]>
		<include refid="NB_queryQuestionaireInfoBySurveyVersionCodeCondition" />
		<![CDATA[ ORDER BY SURVEY_VERSION ]]>
	</select>

	<select id="NB_findHolderByPolicyIdNew" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, A.APPLY_CODE,
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>

	<select id="NB_queryPayerByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, A.ACCOUNT, 
			A.PAY_NEXT, A.POLICY_CODE, A.PAY_MODE,  A.LIST_ID, A.ACCOUNT_ID, A.NEXT_ACCOUNT_ID, A.PAY_LOCATION, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT,APPLY_CODE FROM DEV_NB.T_NB_PAYER_ACCOUNT          A WHERE 1 = 1  ]]>
		<include refid="NB_queryPayerByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<!-- 查询所有省操作 -->
	<select id="NB_findAllProvince" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CODE, A.NAME FROM DEV_NB.T_DISTRICT A WHERE 1 = 1 AND A.CODE LIKE '__0000' ]]>
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	<!-- 查询所有操作 -->
	<select id="NB_findAllNbInsuredList" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID,A.JOB_CODE,A.JOB_UNDERWRITE FROM DEV_NB.T_NB_INSURED_LIST           A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.UPDATE_TIME desc            ]]>
	</select>
	<!-- 查询受益人列表通过保单Id linmy_mapper -->
	<select id="NB_findNewBeneByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.*, jc.job_name
  					from (SELECT BENE.BENE_ID,BENE.POLICY_ID,BENE.POLICY_CODE,BENE.APPLY_CODE,BENE.BUSI_ITEM_ID,BENE.PRODUCT_CODE,
               				BENE.INSURED_ID,BENE.BENE_TYPE,BENE.SHARE_ORDER,BENE.SHARE_RATE,BENE.DESIGNATION,BENE.ADDRESS_ID,
               				BENE.CUSTOMER_ID,BENE.INSERT_BY,BENE.INSERT_TIME,BENE.INSERT_TIMESTAMP,BENE.UPDATE_TIME,BENE.UPDATE_BY,
			                BENE.UPDATE_TIMESTAMP,BENE.CUSTOMER_NAME,BENE.CUSTOMER_BIRTHDAY,BENE.CUSTOMER_GENDER,BENE.CUSTOMER_CERT_TYPE,
			                BENE.CUSTOMER_CERTI_CODE,lins.customer_id as insured_customer_id,cu.customer_name as insured_customer_name,
               				pro.product_name_sys,cu.job_code
          				 from DEV_NB.T_NB_CONTRACT_BENE bene,
				              DEV_NB.T_nb_contract_busi_prod prd,
				              DEV_NB.T_nb_benefit_insured bins,
				              DEV_NB.T_nb_insured_list lins,
				              DEV_NB.T_BUSINESS_PRODUCT pro,
				              DEV_NB.T_customer cu
				         where prd.busi_item_id = bene.busi_item_id
				           and bins.busi_item_id = prd.busi_item_id
				           and lins.list_id = bins.insured_id
				           and bene.insured_id = lins.list_id
				           and pro.BUSINESS_PRD_ID = prd.busi_prd_id
				           and cu.customer_id = lins.customer_id
				           and bene.policy_id = #{policy_id}) a
				  left join DEV_NB.T_job_code jc on a.job_code = jc.job_code
				 ORDER BY a.bene_id desc]]>
	</select>

	<select id="NB_findMasterByApplyCodes" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[SELECT A.OPERATOR_USER_CODE,LTRIM(RTRIM(A.DECISION_CODE)) AS DECISION_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
      LTRIM(RTRIM(A.CHANNEL_TYPE)) AS CHANNEL_TYPE , A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
      A.POLICY_ID, A.SCAN_COMPLETE_TIME, 
      A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
      A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
      A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
      A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
      A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, A.DIALECT_INDI,
      A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE ,A.MEDIA_TYPE,A.APL_PERMIT,A.POLICY_PWD,A.AGENCY_CODE,B.SALES_CHANNEL_NAME AS TYPE_NAME,A.INPUT_DATE,A.CALL_TIME_LIST,A.APPOINT_VALIDATE,A.INSERT_BY from DEV_NB.T_NB_CONTRACT_MASTER  A ,DEV_NB.T_SALES_CHANNEL B WHERE 1 = 1 
      AND A.CHANNEL_TYPE=B.SALES_CHANNEL_CODE  ]]>
		<include refid="NB_queryByDeliverCondition" />
		<![CDATA[ ORDER BY A.APPLY_CODE          ]]>
	</select>

	<sql id="NB_queryByDeliverCondition">
		<if test=" apply_code != null and apply_code != '' "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" organ_code != null and organ_code != '' "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" channel_type != null and channel_type != '' "><![CDATA[ AND RTRIM(LTRIM(A.CHANNEL_TYPE)) = #{channel_type} ]]></if>
		<if test=" start_date != null and start_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME>= #{start_date} ]]></if>
		<if test=" end_date != null and end_date != '' "><![CDATA[ AND A.UW_COMPLETE_TIME<= #{end_date} ]]></if>
	</sql>
	<sql id="NB_queryPayerByPolicyIdCondition">
		<if test=" policy_id != null and policy_id != '' "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="NB_questionaireCustomerPolicyIDCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<sql id="NB_questionaireCustomerApplyCodeCondition">
		<if test=" apply_code  != null "><![CDATA[ AND A.APPlY_CODE = #{apply_code} ]]></if>
	</sql>
	<sql id="NB_questionaireCustomerCustomerIDCondition">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>
	<sql id="NB_questionaireCustomerAgentCodeCondition">
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>
	<sql id="NB_questionaireCustomerObjectCondition">
		<if test=" questionaire_object != null and questionaire_object != ''  "><![CDATA[ AND A.QUESTIONAIRE_OBJECT = #{questionaire_object} ]]></if>
	</sql>
	<sql id="NB_questionaireSurveyVersionTypeCondition">
		<if test=" survey_version_type != null and survey_version_type != ''  "><![CDATA[ AND C.SURVEY_VERSION_TYPE = #{survey_version_type} ]]></if>
	</sql>
	<sql id="NB_queryQuestionaireCustomerParamByCustomerSurveyIdCondition">
		<if test=" customer_survey_id  != null "><![CDATA[ AND A.CUSTOMER_SURVEY_ID = #{customer_survey_id} ]]></if>
	</sql>
	<!-- 慎用，这个是版别的码表的字段 -->
	<sql id="NB_queryQuestionaireInfoBySurveyVersionCodeCondition">
		<if test=" survey_version  != null "><![CDATA[ AND trim(A.SURVEY_VERSION_CODE) = trim(#{survey_version}) ]]></if>
		<if test=" survey_version_type  != null "><![CDATA[ AND trim(A.SURVEY_VERSION_TYPE) = trim(#{survey_version_type}) ]]></if>
	</sql>
	<select id="NB_findContractAgentByAgentPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_TYPE, A.POLICY_CODE, A.LIST_ID, 
			A.RELATION_TO_PH,A.POLICY_ID, A.AGENT_CODE FROM dev_nb.T_NB_CONTRACT_AGENT            A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE            ]]>
	</select>

	<select id="NB_findBusiProductByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.WAIVER, A.JOINT_LIFE_FLAG, A.MASTER_BUSI_ITEM_ID, A.BUSI_PRD_ID, A.PRODUCT_CODE, A.ISSUE_DATE,A.APL_PERMIT, 
			A.DECISION_CODE, A.HESITATION2ACC, A.RENEW, A.APPLY_CODE, A.EXPIRY_DATE, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.VALIDATE_DATE, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM dev_nb.T_NB_CONTRACT_BUSI_PROD     A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByApplyCodeCondition" />
		<![CDATA[ ORDER BY A.BUSI_ITEM_ID       ]]>
	</select>
	<!-- 根据busiItemId查询责任包含码表中的责任名称 zhuhaixin -->
	<select id="NB_findContractProductAndProductName" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL,A.PRODUCT_ID, A.PRODUCT_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, A.PAIDUP_DATE,
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, 
			A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, 
			A.PAY_YEAR, A.STD_PREM_AF, A.LIABILITY_STATE, A.CHARGE_PERIOD, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, 
			A.VALIDATE_DATE,A.PROD_PKG_PLAN_CODE,A.PREM_FREQ ,B.PRODUCT_NAME FROM dev_nb.T_NB_CONTRACT_PRODUCT       A ,dev_uw.T_PRODUCT_LIFE B WHERE A.PRODUCT_ID=B.PRODUCT_ID  ]]>
		<include refid="NB_queryByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>
	<sql id="NB_queryByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>

	<select id="NB_findMasterByServiceBankBranch" resultType="java.util.Map"
		parameterType="java.util.Map">
	    <![CDATA[select  A.OPERATOR_USER_CODE, A.CHANNEL_ID, A.PA_USER_CODE, A.PROPOSAL_STATUS, A.APPLY_CODE, A.ORGAN_CODE, 
			A.CHANNEL_TYPE, A.OVERDUE_TIME, A.SALE_AGENT_NAME, A.INSURED_FAMILY, A.SERVICE_HANDLER_NAME, 
			A.POLICY_ID, A.SCAN_COMPLETE_TIME, A.CHANNEL_ORG_CODE, 
			A.POLICY_TYPE, A.EXPIRY_DATE, A.SUBMIT_CHANNEL, A.LIABILITY_STATE, A.POLICY_CODE, A.SALE_AGENT_CODE, 
			A.VALIDATE_DATE, A.MONEY_CODE, A.SERVICE_HANDLER_CODE, A.APPLY_DATE, 
			A.BRANCH_ORGAN_CODE, A.MANUAL_UW_INDI, A.SUBMISSION_DATE, A.UW_USER_CODE, A.SERVICE_HANDLER, 
			A.E_SERVICE_FLAG, A.UW_COMPLETE_TIME, A.RISK_INDI, A.SERVICE_BANK_BRANCH, A.ISSUE_USER_CODE, 
			A.ISSUE_DATE, A.HIGH_SA_INDI, A.AGENT_ORG_ID, A.BILL_CHECKED, A.SERVICE_BANK, A.PA_COMPLETE_TIME, 
			A.BIRTHDAY_POL_INDI, A.LANG_CODE, A.SCAN_USER_CODE, A.DECISION_CODE,MEDIA_TYPE,APL_PERMIT,POLICY_PWD,AGENCY_CODE,A.APPOINT_VALIDATE
			from dev_nb.T_NB_CONTRACT_MASTER A where service_bank_branch=#{service_bank_branch} 
	    or apply_code =(select apply_code from dev_nb.T_NB_contract_agent where agent_code =#{service_bank_branch})
	    ]]>
         <![CDATA[ORDER BY N.POLICY_ID ) B WHERE B.RN > #{GREATER_NUM}]]>
	</select>
	<!-- 根据Account查询操作 -->
	<select id="NB_queryPayerByAccount" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, A.ACCOUNT, 
			A.PAY_NEXT, A.POLICY_CODE, A.PAY_MODE,  A.LIST_ID,  A.PAY_LOCATION, A.NEXT_ACCOUNT_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT,APPLY_CODE FROM dev_nb.T_NB_PAYER_ACCOUNT A      WHERE 1 = 1  ]]>
		<include refid="NB_queryPayerByAccountCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<sql id="NB_queryPayerByAccountCondition">
		<if test=" account != null "><![CDATA[ AND A.ACCOUNT = #{account} ]]></if>
	</sql>
	<select id="NB_findByPayerId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.PAYER_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.TELEPHONE, 
			A.POLICY_CODE, A.MOBILE_TEL, A.POLICY_ID, 
			A.SHARE_RATE,APPLY_CODE FROM dev_nb.T_NB_PAYER                  A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPayerIdCondition" />
		<![CDATA[ ORDER BY A.PAYER_ID           ]]>
	</select>
	<sql id="NB_queryByPayerIdCondition">
		<if test=" payer_id  != null "><![CDATA[ AND A.PAYER_ID = #{payer_id} ]]></if>
	</sql>
	<!-- 根据客户id查询 zhuhaixin -->
	<select id="NB_findNbPolicyHolderByCustomerId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM  dev_nb.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByCustomerIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<sql id="NB_queryByCustomerIdCondition">
		<if test=" customer_id != null and customer_id != '' "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>

	<select id="NB_findAllNbContractBeneByPolicyIdAndBusiItemId"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.BENE_ID, A.POLICY_CODE, A.DESIGNATION, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM dev_nb.T_NB_CONTRACT_BENE          A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByPolicyIdAndBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.BENE_ID            ]]>
	</select>
	<sql id="NB_queryByPolicyIdAndBusiItemIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>

	<select id="NB_findAllNbContractProductByPolicyIDAndBusiItemID"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENEFIT_LEVEL, A.APPLY_CODE,A.PRODUCT_ID, A.PRODUCT_CODE, A.ITEM_ID, A.COVERAGE_PERIOD, 
			A.CHARGE_YEAR, A.EXTRA_PREM_AF, A.INITIAL_DISCNT_PREM_AF,A.INITIAL_EXTRA_PREM_AF,A.RENEWAL_DISCNTED_PREM_AF,A.RENEWAL_EXTRA_PREM_AF,
			A.BUSI_ITEM_ID, A.HEALTH_SERVICE_FLAG, A.POLICY_ID, A.UNIT, A.COVERAGE_YEAR, 
			A.AMOUNT, A.TOTAL_PREM_AF, A.DECISION_CODE, A.EXPIRY_DATE, 
			A.PAY_YEAR, A.STD_PREM_AF, A.LIABILITY_STATE, A.CHARGE_PERIOD, A.PAY_PERIOD, A.COUNT_WAY, A.POLICY_CODE, 
			A.VALIDATE_DATE,A.PROD_PKG_PLAN_CODE,A.PREM_FREQ,A.PAIDUP_DATE,A.INTEREST_MODE,  A.DEDUCTIBLE_FRANCHISE,A.PAYOUT_RATE,A.ADDITIONAL_PREM_AF,A.APPEND_PREM_AF
			 FROM dev_nb.T_NB_CONTRACT_PRODUCT   A       WHERE 1 = 1      ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<include refid="NB_queryByProductIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID            ]]>
	</select>
	<sql id="NB_queryByProductIdCondition">
		<if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
	</sql>
	<!-- 查询所有操作 -->
	<select id="NB_findAllContractProductOther" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT  A.PRODUCT_ID, A.BUSI_PRD_ID, A.FIELD20, A.ITEM_ID,
			 A.FIELD19, A.APPLY_CODE, A.FIELD17, A.FIELD18, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.FIELD13, A.FIELD14, A.FIELD15, A.FIELD16, 
			A.FIELD10, A.FIELD11, A.FIELD12, A.FIELD7, A.FIELD6, 
			A.FIELD9, A.POLICY_CODE, A.FIELD8 FROM dev_nb.T_NB_CONTRACT_PRODUCT_OTHER       A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryContractProductOtherByPolicyIdCondition" />
		<include refid="NB_queryContractProductOtherByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_ID     ]]>
	</select>
	<!-- 按policyId生成的查询条件 -->
	<sql id="NB_queryContractProductOtherByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>
	<!-- 按BusiItemId生成的查询条件 -->
	<sql id="NB_queryContractProductOtherByBusiItemIdCondition">
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>
	<!-- 查询所有操作 -->
	<select id="NB_findAllNbContractInvestRate" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ACCOUNT_CODE, A.POLICY_CODE, 
			A.PREM_TYPE, A.LIST_ID, A.ASSIGN_RATE, A.LOW_PLY_YEAR, A.HIGH_PLY_YEAR, 
			A.POLICY_ID FROM dev_nb.T_NB_CONTRACT_INVEST_RATE   A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<sql id="NB_queryByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>
	<select id="NB_findAllNbExtraPremByPolicyIdAndItemId"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.UW_ID, A.END_DATE, A.PRODUCT_CODE, A.UW_BUSI_ID, A.ITEM_ID, A.UW_PRD_ID, A.EXTRA_DIV, 
			A.BUSI_PROD_CODE, A.EXTRA_PREM, A.APPLY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.EM_VALUE, A.POLICY_ID, A.EXTRA_PERIOD, A.EXTRA_TYPE, A.EXTRA_PARA, 
			A.INTO_EFFECT_TYPE, A.START_DATE, A.POLICY_CODE, A.EXTRA_PERC, A.ADD_ARITH FROM dev_nb.T_NB_EXTRA_PREM             A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<select id="NB_findNbPolicyConditionByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONDITION_DESC, A.PRODUCT_CODE, A.ITEM_ID, A.BUSI_PROD_CODE, A.APPLY_CODE, 
			A.INSURED_LIST_ID, A.POLICY_CODE, A.UW_CONDITION_TYPE, A.CONDITION_ID, 
			A.BUSI_ITEM_ID, A.INSURED_CUSTOMER_ID, A.POLICY_ID FROM dev_nb.T_NB_POLICY_CONDITION A WHERE ROWNUM <=  1000 ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByItemIdCondition" />
		<![CDATA[ ORDER BY A.CONDITION_ID       ]]>
	</select>
	<select id="NB_findAllHolderByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.JOB_CODE, 
			A.POLICY_CODE, A.LIST_ID, A.POLICY_ID,A.JOB_UNDERWRITE FROM dev_nb.T_NB_POLICY_HOLDER          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>

	<select id="NB_findAllNbPayerByPolicyId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.PAYER_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.TELEPHONE, 
			A.POLICY_CODE, A.MOBILE_TEL, A.POLICY_ID, 
			A.SHARE_RATE,APPLY_CODE FROM dev_nb.T_NB_PAYER                  A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.PAYER_ID           ]]>
	</select>
	<select id="NB_findByPolicyIdAndPayerId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CHEQUE_NO,A.ACCOUNT_NAME, A.ACCOUNT_BANK, A.PAYER_ID, A.NEXT_ACCOUNT_BANK, A.ACCOUNT, 
			A.PAY_NEXT, A.POLICY_CODE, A.PAY_MODE,  A.LIST_ID,  A.PAY_LOCATION, A.NEXT_ACCOUNT_ID, 
			A.NEXT_ACCOUNT_NAME, A.POLICY_ID, A.NEXT_ACCOUNT,APPLY_CODE FROM dev_nb.T_NB_PAYER_ACCOUNT          A WHERE 1 = 1  ]]>
		<include refid="NB_queryPayerByPolicyIdCondition" />
		<include refid="NB_queryByPayerIdCondition" />		
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<select id="NB_findBusiProductByApplyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.WAIVER, A.JOINT_LIFE_FLAG, A.MASTER_BUSI_ITEM_ID, A.BUSI_PRD_ID, A.PRODUCT_CODE, A.ISSUE_DATE,A.APL_PERMIT,
			A.DECISION_CODE, A.HESITATION2ACC, A.RENEW, A.APPLY_CODE, A.EXPIRY_DATE, 
			A.LIABILITY_STATE, A.POLICY_CODE, A.VALIDATE_DATE, A.BUSI_ITEM_ID,  B.PRODUCT_ABBR_NAME,
			A.POLICY_ID,  B.product_Category, A.INSERT_TIME, A.HESITATION_PERIOD_DAY, (case when a.master_busi_item_id is not null then a.master_busi_item_id else a.busi_item_id end ) as mbid FROM 
			dev_nb.T_NB_CONTRACT_BUSI_PROD A LEFT JOIN dev_nb.T_BUSINESS_PRODUCT B ON A.BUSI_PRD_ID=B.BUSINESS_PRD_ID
			 WHERE 1 = 1  ]]>
		<include refid="NB_queryByApplyCodeCondition" />
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByProductCategoryCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<include refid="NB_queryByMasterBusiItemId" />
		<![CDATA[ ORDER BY mbid, A.insert_time]]>
	</select>
	<sql id="NB_queryByProductCategoryCondition">
		<if test=" product_category  != null "><![CDATA[ AND B.PRODUCT_CATEGORY >= #{product_category} ]]></if>
	</sql>

	<sql id="NB_queryByMasterBusiItemId">
		<if test=" master_busi_item_id  != null "><![CDATA[ AND A.MASTER_BUSI_ITEM_ID = #{master_busi_item_id} ]]></if>
	</sql>
	<select id="NB_findByPolicyIdAndBusiItemId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, A.RELATION_TO_INSURED_1, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID FROM dev_nb.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<!-- 按索引查询操作 -->
	<select id="NB_findInsuredByListId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, 
			A.POLICY_CODE, A.LIST_ID,
			A.POLICY_ID, A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE , job_name FROM dev_nb.T_NB_INSURED_LIST  A left join dev_nb.T_job_code b on a.job_code = b.job_code  WHERE 1 = 1  ]]>
		<include refid="NB_queryByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<sql id="NB_queryByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>
	<select id="NB_findByPolicyIdAndInsuredId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.PRODUCT_CODE, A.JOB_UNDERWRITE, A.INSURED_ID, 
			A.ORDER_ID, A.POLICY_CODE, A.LIST_ID, A.BUSI_ITEM_ID, 
			A.POLICY_ID  FROM dev_nb.T_NB_BENEFIT_INSURED        A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByInsuredIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID            ]]>
	</select>
	<sql id="NB_queryByInsuredIdCondition">
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
	</sql>
	<select id="NB_findByPolicyCodeAndInsurant" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.ADDRESS_ID, A.SHARE_ORDER, A.BENE_TYPE, A.PRODUCT_CODE, A.CUSTOMER_ID, 
			A.INSURED_ID, A.BENE_ID, A.POLICY_CODE, A.DESIGNATION, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.SHARE_RATE FROM dev_nb.T_NB_CONTRACT_BENE          A WHERE 1 = 1  ]]>
		<include refid="NB_queryByPolicyIdCondition" />
		<include refid="NB_queryByInsuredIdCondition" />
		<include refid="NB_queryByBusiItemIdCondition" />
		<![CDATA[ ORDER BY A.BENE_ID            ]]>
	</select>
	<select id="QRY_findInsuredByCustomerIdToBusiProdTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		select count(*) from dev_nb.t_nb_contract_busi_prod d,
		(SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, A.LIST_ID,
      			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE 
      			from dev_nb.t_NB_INSURED_LIST A ,dev_nb.T_nb_contract_master B
      		   where A.Apply_Code = B.Apply_Code       
		      and B.Liability_State <>'1' ]]>
		<include refid="NB_queryByCustomerIdCondition" />  <![CDATA[) f
		      where d.policy_id = f.policy_id 
		      order by f.apply_code]]>
	</select>
	<select id="QRY_findInsuredByCustomerIdToBusiProdForPage"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT E.RN AS ROWNUMBER,E.APPLY_CODE,E.PRODUCT_CODE,E.BUSI_ITEM_ID 
		FROM 
		(
		SELECT ROWNUM RN,F.APPLY_CODE,D.PRODUCT_CODE,D.BUSI_ITEM_ID FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD D,
		(SELECT A.ADDRESS_ID, A.CUSTOMER_ID, A.RELATION_TO_PH, A.POLICY_CODE, A.LIST_ID,
      			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE 
      			FROM ]]>
      			<if test="query_busi_code==1">
      			     
      			</if>
      			<if test="query_busi_code==null or query_busi_code!=1">
      			    DEV_NB.T_NB_INSURED_LIST A 
      			</if>
      			<![CDATA[ 
      			,DEV_NB.T_NB_CONTRACT_MASTER B
      		   WHERE A.APPLY_CODE = B.APPLY_CODE       
		      AND B.LIABILITY_STATE in ('4','0','2','3') ]]>
		<include refid="NB_queryByCustomerIdCondition" />  <![CDATA[) F
		      WHERE D.POLICY_ID = F.POLICY_ID AND ROWNUM <= #{LESS_NUM}
		      ORDER BY F.APPLY_CODE
		  ) E WHERE E.RN > #{GREATER_NUM}  ]]>
	</select>

	<!-- 根据投保单号查询核保备注信息 -->
	<select id="NB_findDecisionReasonByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[
			SELECT 
			P.UW_FINISH_TIME,
			PD.DECISION_DESC AS UW_DECISION
			FROM DEV_UW.T_UW_MASTER  UM
			INNER JOIN DEV_UW.T_UW_DECISION_REASON UR ON UM.UW_ID=UR.UW_ID
			INNER JOIN DEV_UW.T_UW_POLICY P ON P.UW_ID = UR.UW_ID 
			INNER JOIN  DEV_UW.T_POLICY_DECISION PD ON PD.DECISION_CODE=P.POLICY_DECISION
			 WHERE 1=1
			AND  UM.BIZ_CODE=#{apply_code}
			AND UM.UW_SOURCE_TYPE=1 
		]]>
	</select>

	<!-- 根据投保单号查询质检备注信息 -->
	<select id="NB_findATRemarkByPolicyCode" resultType="java.util.Map"
		parameterType="java.util.Map">
	</select>
		<!-- 查询单条 -->
	<select id="NB_findSurveyTemplate" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFINED_BY, A.DEFINED_FOR_ORGAN, A.DEFINE_TIME, 
			A.SURVEY_TEMPLATE_ID, A.TEMPLATE_NAME, A.SURVEY_TEMPL_CODE, A.SURVEY_OBJECT FROM DEV_NB.T_SURVEY_TEMPLATE           A WHERE 1 = 1  ]]>
		<include refid="NB_surveyTemplateWhereCondition" />
		<![CDATA[ ORDER BY A.SURVEY_TEMPLATE_ID ]]>
	</select>
	<sql id="NB_surveyTemplateWhereCondition">
		<if test=" survey_type  != null "><![CDATA[ AND A.SURVEY_TYPE = #{survey_type} ]]></if>
		<if test=" defined_by  != null "><![CDATA[ AND A.DEFINED_BY = #{defined_by} ]]></if>
		<if test=" defined_for_organ != null and defined_for_organ != ''  "><![CDATA[ AND A.DEFINED_FOR_ORGAN = #{defined_for_organ} ]]></if>
		<if test=" define_time  != null  and  define_time  != ''  "><![CDATA[ AND A.DEFINE_TIME = #{define_time} ]]></if>
		<if test=" survey_template_id  != null "><![CDATA[ AND A.SURVEY_TEMPLATE_ID = #{survey_template_id} ]]></if>
		<if test=" template_name != null and template_name != ''  "><![CDATA[ AND A.TEMPLATE_NAME = #{template_name} ]]></if>
		<if test=" survey_templ_code != null and survey_templ_code != ''  "><![CDATA[ AND A.SURVEY_TEMPL_CODE = #{survey_templ_code} ]]></if>
		<if test=" update_timestamp != null and update_timestamp != ''  "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" survey_object  != null "><![CDATA[ AND A.SURVEY_OBJECT = #{survey_object} ]]></if>
	</sql>
	<!-- 查询所有操作 -->
	<select id="NB_findAllSurveyQuestion" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SURVEY_QUESTION, A.TOP_QUESTION_FLAG, A.QUESTION_ORDER, A.QUESTION_ID, A.QUESTION_CODE, 
			A.SURVEY_TEMPLATE_ID, A.QUESTION_TYPE, A.QUESTION_ANSWER_NUM FROM DEV_NB.T_SURVEY_QUESTION           A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_surveyQuestionWhereCondition" />
		<![CDATA[ ORDER BY A.QUESTION_ID        ]]> 
	</select>
	<sql id="NB_surveyQuestionWhereCondition">
		<if test=" survey_question != null and survey_question != ''  "><![CDATA[ AND A.SURVEY_QUESTION = #{survey_question} ]]></if>
		<if test=" top_question_flag  != null "><![CDATA[ AND A.TOP_QUESTION_FLAG = #{top_question_flag} ]]></if>
		<if test=" question_order  != null "><![CDATA[ AND A.QUESTION_ORDER = #{question_order} ]]></if>
		<if test=" question_id  != null "><![CDATA[ AND A.QUESTION_ID = #{question_id} ]]></if>
		<if test=" question_code != null and question_code != ''  "><![CDATA[ AND A.QUESTION_CODE = #{question_code} ]]></if>
		<if test=" insert_timestamp != null and insert_timestamp != ''  "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" survey_template_id  != null "><![CDATA[ AND A.SURVEY_TEMPLATE_ID = #{survey_template_id} ]]></if>
		<if test=" question_type  != null "><![CDATA[ AND A.QUESTION_TYPE = #{question_type} ]]></if>
		<if test=" question_answer_num  != null "><![CDATA[ AND A.QUESTION_ANSWER_NUM = #{question_answer_num} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
	</sql>
	<select id="NB_findAllSurveyQuestionOption" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SUB_QUESTION_FLAG, A.QUESTION_OPTION_CODE, A.QUESTION_OPTION_ID, 
			A.REF_QUESTION_ID, A.QUESTION_OPTION_DESC, A.QUESTION_ID FROM DEV_NB.T_SURVEY_QUESTION_OPTION    A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_surveyQuestionOptionWhereCondition" />
		<![CDATA[ ORDER BY A.QUESTION_OPTION_ID ]]> 
	</select>
	<sql id="NB_surveyQuestionOptionWhereCondition">
		<if test=" insert_timestamp  != null "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" sub_question_flag  != null "><![CDATA[ AND A.SUB_QUESTION_FLAG = #{sub_question_flag} ]]></if>
		<if test=" question_option_code != null and question_option_code != ''  "><![CDATA[ AND A.QUESTION_OPTION_CODE = #{question_option_code} ]]></if>
		<if test=" question_option_id  != null "><![CDATA[ AND A.QUESTION_OPTION_ID = #{question_option_id} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" ref_question_id  != null "><![CDATA[ AND A.REF_QUESTION_ID = #{ref_question_id} ]]></if>
		<if test=" question_option_desc != null and question_option_desc != ''  "><![CDATA[ AND A.QUESTION_OPTION_DESC = #{question_option_desc} ]]></if>
		<if test=" question_id  != null "><![CDATA[ AND A.QUESTION_ID = #{question_id} ]]></if>
	</sql>
	<!-- 查询所有操作 -->
	<select id="NB_findAllCustomerSurvey" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RESERVED_FIELD3, A.RESERVED_FIELD4, A.RESERVED_FIELD5, A.RESERVED_FIELD6, A.AUDIT_ORGAN_CODE, A.MAIN_BUSI_PROD_CODE, A.RESERVED_FIELD7, 
			A.RESERVED_FIELD8, A.RESERVED_FIELD9, A.CUSTOMER_ID, A.RESERVED_FIELD10, A.APPLY_CODE, 
			A.SURVEY_TEMPLATE_ID, A.MAIN_BUSI_PRD_PREM_YEARS, A.INSURANT_ORDER, A.PH_BIRTHDAY, A.PH_CERTI_CODE, A.POLICY_ID, A.RESERVED_FIELD2, 
			A.RESERVED_FIELD1, A.PH_CERT_TYPE, A.TOTAL_PREMIUM, A.MAIN_BUSI_PRD_COVERAGE, A.POLICY_CODE, 
			A.PAY_MODE, A.TOTAL_SA, A.SURVEY_OBJECT, A.SURVEY_ID,A.MAIN_BUSI_PROD_CODE FROM DEV_NB.T_CUSTOMER_SURVEY           A WHERE ROWNUM <=  1000  ]]>
		<include refid="NB_customerSurveyWhereCondition" />
		<![CDATA[ ORDER BY A.UPDATE_TIME          ]]> 
	</select>
	<sql id="NB_customerSurveyWhereCondition">
		<if test=" reserved_field3 != null and reserved_field3 != ''  "><![CDATA[ AND A.RESERVED_FIELD3 = #{reserved_field3} ]]></if>
		<if test=" reserved_field4 != null and reserved_field4 != ''  "><![CDATA[ AND A.RESERVED_FIELD4 = #{reserved_field4} ]]></if>
		<if test=" reserved_field5 != null and reserved_field5 != ''  "><![CDATA[ AND A.RESERVED_FIELD5 = #{reserved_field5} ]]></if>
		<if test=" reserved_field6 != null and reserved_field6 != ''  "><![CDATA[ AND A.RESERVED_FIELD6 = #{reserved_field6} ]]></if>
		<if test=" audit_organ_code != null and audit_organ_code != ''  "><![CDATA[ AND A.AUDIT_ORGAN_CODE = #{audit_organ_code} ]]></if>
		<if test=" MAIN_BUSI_PROD_CODE != null and MAIN_BUSI_PROD_CODE != ''  "><![CDATA[ AND A.MAIN_BUSI_PROD_CODE = #{MAIN_BUSI_PROD_CODE} ]]></if>
		<if test=" reserved_field7 != null and reserved_field7 != ''  "><![CDATA[ AND A.RESERVED_FIELD7 = #{reserved_field7} ]]></if>
		<if test=" reserved_field8 != null and reserved_field8 != ''  "><![CDATA[ AND A.RESERVED_FIELD8 = #{reserved_field8} ]]></if>
		<if test=" reserved_field9 != null and reserved_field9 != ''  "><![CDATA[ AND A.RESERVED_FIELD9 = #{reserved_field9} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" reserved_field10 != null and reserved_field10 != ''  "><![CDATA[ AND A.RESERVED_FIELD10 = #{reserved_field10} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" insert_timestamp != null and insert_timestamp != ''  "><![CDATA[ AND A.INSERT_TIMESTAMP = #{insert_timestamp} ]]></if>
		<if test=" survey_template_id  != null "><![CDATA[ AND A.SURVEY_TEMPLATE_ID = #{survey_template_id} ]]></if>
		<if test=" main_busi_prd_prem_years  != null "><![CDATA[ AND A.MAIN_BUSI_PRD_PREM_YEARS = #{main_busi_prd_prem_years} ]]></if>
		<if test=" insurant_order  != null "><![CDATA[ AND A.INSURANT_ORDER = #{insurant_order} ]]></if>
		<if test=" ph_birthday  != null  and  ph_birthday  != ''  "><![CDATA[ AND A.PH_BIRTHDAY = #{ph_birthday} ]]></if>
		<if test=" PH_CERTI_CODE != null and PH_CERTI_CODE != ''  "><![CDATA[ AND A.PH_CERTI_CODE = #{PH_CERTI_CODE} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" reserved_field2 != null and reserved_field2 != ''  "><![CDATA[ AND A.RESERVED_FIELD2 = #{reserved_field2} ]]></if>
		<if test=" reserved_field1 != null and reserved_field1 != ''  "><![CDATA[ AND A.RESERVED_FIELD1 = #{reserved_field1} ]]></if>
		<!-- <if test=" ph_cert_type != null and ph_cert_type != ''  "><![CDATA[ AND A.PH_CERT_TYPE = #{ph_cert_type} ]]></if> -->
		<if test=" total_premium  != null "><![CDATA[ AND A.TOTAL_PREMIUM = #{total_premium} ]]></if>
		<if test=" main_busi_prd_coverage  != null "><![CDATA[ AND A.MAIN_BUSI_PRD_COVERAGE = #{main_busi_prd_coverage} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" total_sa  != null "><![CDATA[ AND A.TOTAL_SA = #{total_sa} ]]></if>
		<if test=" update_timestamp  != null "><![CDATA[ AND A.UPDATE_TIMESTAMP = #{update_timestamp} ]]></if>
		<if test=" survey_object  != null "><![CDATA[ AND A.SURVEY_OBJECT = #{survey_object} ]]></if>
		<if test=" survey_id  != null "><![CDATA[ AND A.SURVEY_ID = #{survey_id} ]]></if>
	</sql>
	<!-- 查询所有操作 -->
	<select id="NB_findAllCustomerSurveyDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QUESTION_OPTION_CODE, A.DETAIL_ID, A.QUESTION_ANSWER_TXT4, A.QUESTION_ID, A.QUESTION_ANSWER_TXT3, 
			A.QUESTION_CODE, A.QUESTION_ANSWER_TXT2, A.QUESTION_ANSWER_TXT1, A.QUESTION_OPTION_ID, 
			A.SURVEY_ID FROM DEV_NB.T_CUSTOMER_SURVEY_DETAIL    A    LEFT JOIN DEV_NB.T_CUSTOMER_SURVEY B ON A.SURVEY_ID = B.SURVEY_ID  WHERE ROWNUM <=  1000]]>
		<include refid="NB_queryBySurveyDetailCondition" />
		<![CDATA[ ORDER BY A.DETAIL_ID          ]]> 
	</select>
	<!--关联的查询条件-->
	<sql id="NB_queryBySurveyDetailCondition">
		<if test=" apply_code  != null "><![CDATA[ AND B.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" survey_object  != null "><![CDATA[ AND B.SURVEY_OBJECT = #{survey_object} ]]></if>
		<if test=" survey_template_id  != null "><![CDATA[ AND B.SURVEY_TEMPLATE_ID = #{survey_template_id} ]]></if>
		<if test=" survey_id  != null "><![CDATA[ AND A.SURVEY_ID = #{survey_id} ]]></if>
	</sql>
	<select id="NB_findBusiProductByBusinessProductCodeSys" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSINESS_PRD_ID,A.PRODUCT_CATEGORY,A.PRODUCT_CATEGORY1, A.PRODUCT_CATEGORY2,A.PRODUCT_NAME_STD,A.PRODUCT_CODE_SYS, A.INSURED_COUNT_MIN, A.INSURED_COUNT_MAX, A.PRODUCT_ABBR_NAME,A.RENEW_OPTION,A.WAIVER_FLAG,A.COVER_PERIOD_TYPE,A.SINGLE_JOINT_LIFE FROM DEV_NB.T_BUSINESS_PRODUCT A WHERE 1 = 1 ]]>
		<include refid="NB_findBusinessProductByBusinessProductCodeSysCondition" /> 
	</select>
	<sql id="NB_findBusinessProductByBusinessProductCodeSysCondition">
		<if test=" product_code_sys != null and product_code_sys != '' "><![CDATA[ AND A.PRODUCT_CODE_SYS = #{product_code_sys} ]]></if>
	</sql>	
	
	<select id="NB_findHolderByCustomerIdToBusiProdForPage"
		resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
		SELECT E.RN AS ROWNUMBER,E.APPLY_CODE,E.PRODUCT_CODE,E.BUSI_ITEM_ID 
		FROM 
		(
		SELECT ROWNUM RN,F.APPLY_CODE,D.PRODUCT_CODE,D.BUSI_ITEM_ID FROM DEV_NB.T_NB_CONTRACT_BUSI_PROD D,
		(SELECT A.ADDRESS_ID, A.CUSTOMER_ID,'' as RELATION_TO_PH, A.POLICY_CODE, A.LIST_ID,
      			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE 
      			FROM 
      			  DEV_NB.t_nb_policy_holder A,DEV_NB.T_NB_CONTRACT_MASTER B
      		   WHERE A.APPLY_CODE = B.APPLY_CODE 
      		  AND B.LIABILITY_STATE in ('4','0','2','3') ]]>
		<include refid="NB_queryByCustomerIdCondition" />  <![CDATA[) F
		      WHERE D.POLICY_ID = F.POLICY_ID 
		       and D.PRODUCT_CODE  in ('00425000','00952000')
		      AND         
		       ROWNUM <= #{LESS_NUM}
		      ORDER BY F.APPLY_CODE
		  ) E WHERE E.RN > #{GREATER_NUM}  ]]>
	</select>
	
	<select id="NB_findHolderByCustomerIdToBusiProdTotal"
		resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[
		select count(*) from dev_nb.t_nb_contract_busi_prod d,
		(SELECT A.ADDRESS_ID, A.CUSTOMER_ID, '' as RELATION_TO_PH, A.POLICY_CODE, A.LIST_ID,
      			A.POLICY_ID,A.APPLY_CODE,A.JOB_CODE,A.JOB_UNDERWRITE 
      			from DEV_NB.t_nb_policy_holder A ,dev_nb.T_nb_contract_master B
      		   where A.Apply_Code = B.Apply_Code       
		      and B.Liability_State <>'1' ]]>
		<include refid="NB_queryByCustomerIdCondition" />  <![CDATA[) f
		      where d.policy_id = f.policy_id 
		       and d.PRODUCT_CODE  in ('00425000','00952000')
		      order by f.apply_code]]>
	</select>
	<!-- #50447 客户身份验真承保需求 投保单下验真客户查询 -->
    <select id="QRY_queryCustomerVerifyCustomers"
        resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
           SELECT C.CUSTOMER_ID,/*客户编号*/
                C.CUSTOMER_NAME, /*客户姓名*/
                '投保人' AS CUSTOMER_ROLE,/*客户角色*/
                C.CUSTOMER_GENDER,/*客户性别*/
                C.CUSTOMER_CERT_TYPE,/*客户证件类型*/
                C.CUSTOMER_CERTI_CODE /*客户证件号码*/
           FROM DEV_NB.T_NB_POLICY_HOLDER　 PH
          INNER JOIN DEV_NB.T_CUSTOMER C
             ON PH.CUSTOMER_ID = C.CUSTOMER_ID      
          WHERE 1 = 1
            AND PH.APPLY_CODE = #{apply_code}/*投保单号*/
         ]]>
         <if test="relation_to_ph != null and relation_to_ph != '' and relation_to_ph != '00'">
		     <![CDATA[
	         union
	         SELECT C.CUSTOMER_ID, /*客户编号*/
	                C.CUSTOMER_NAME, /*被保人姓名*/
	                '被保人' AS CUSTOMER_ROLE, /*客户角色*/
	                C.CUSTOMER_GENDER, /*客户性别*/
	                C.CUSTOMER_CERT_TYPE, /*客户证件类型*/
	                C.CUSTOMER_CERTI_CODE /*客户证件号码*/
	           FROM DEV_NB.T_NB_BENEFIT_INSURED BI
	           INNER JOIN DEV_NB.T_NB_INSURED_LIST IL 
	          ON BI.INSURED_ID=IL.LIST_ID
	         INNER JOIN DEV_NB.T_CUSTOMER C
	            ON IL.CUSTOMER_ID= C.CUSTOMER_ID
	          WHERE 1 = 1
	            AND BI.ORDER_ID <= 2
	            AND BI.APPLY_CODE = #{apply_code}/*投保单号*/
	         ]]>
         </if>
         <if test="relation_to_il != null and relation_to_il != '' and relation_to_il != '00'">
		     <![CDATA[
	         union
	         SELECT C.CUSTOMER_ID, /*客户编号*/
	                C.CUSTOMER_NAME, /*被保人姓名*/
	                '第二被保人' AS CUSTOMER_ROLE, /*客户角色*/
	                C.CUSTOMER_GENDER, /*客户性别*/
	                C.CUSTOMER_CERT_TYPE, /*客户证件类型*/
	                C.CUSTOMER_CERTI_CODE /*客户证件号码*/
	           FROM DEV_NB.T_NB_BENEFIT_INSURED BI
	          INNER JOIN DEV_NB.T_NB_INSURED_LIST IL 
	          ON BI.INSURED_ID=IL.LIST_ID
	          INNER JOIN DEV_NB.T_CUSTOMER C
	            ON IL.CUSTOMER_ID= C.CUSTOMER_ID
	          WHERE 1 = 1
	            AND BI.ORDER_ID = 2
	            AND BI.APPLY_CODE = #{apply_code}/*投保单号*/
	        ]]>
        </if>
    </select>
    <!-- #50447 客户身份验真承保需求 投保单下验真客户查询  END-->
    <!-- #50447 客户身份验真承保需求 客户验真历史查询  -->
    <sql id="QRY_queryCustomerVerifyHistorySQL">
    <![CDATA[
       SELECT 
		       A.CHECK_ID_CODE AS CERTI_CODE,--证件号码
		       A.CHECK_NAME,--姓名
		       NULL AS STATUS,--验真/维护结果  存在维护结果 验证结果一定为 不通过
		       '0' AS CERT_TYPE,--证件类型
		       NULL AS OPERATE_DATE,--维护时间
		       '' AS OPERATE_REASON,--维护原因
		       A.CHECK_DATE,--验真时间
		       (SELECT TC.SOURCE_NAME FROM DEV_NB.T_CHECK_SOURCE TC WHERE TC.SOURCE_CODE = A.CHECK_SOURCE) AS CHECK_RESULT_SOURCE,--验真来源
		       (SELECT CT.TYPE_NAME FROM DEV_NB.T_NB_CHECK_IDENTITY_TYPE CT WHERE A.CHECK_TYPE = CT.TYPE_CODE) AS CHECK_TYPE,--服务类型  
		       '' AS USER_NAME,--维护人员
		       A.IDENTITY_DETAIL_ID, /*验真明细ID */
		       ADD_MONTHS(A.CHECK_DATE,12*10) AS VALID_ENDDATE,--有效止期     
		       (SELECT UU.USER_NAME FROM T_UDMP_USER UU WHERE UU.USER_ID = A.CHECK_OPERATOR) AS CHECK_OPERATOR_NAME--操作人员
		  FROM DEV_PAS.T_IDENTITY_CHECK_MAIN A
		  LEFT JOIN DEV_PAS.T_UDMP_USER U
		       ON A.INSERT_BY = U.USER_ID
		 WHERE A.CHECK_TYPE = '2'
		   AND A.CHECK_NAME = #{check_name}/*身份识别姓名*/
		   AND A.CHECK_ID_CODE = #{certi_code}/*身份识别证件号码*/
       UNION ALL
		SELECT 
		       CR.CERTI_CODE,--证件号码
		       CR.CUSTOMER_NAME CHECK_NAME,--姓名
		       CR.STATUS,--验真/维护结果  存在维护结果 验证结果一定为 不通过
		       CR.CERT_TYPE,--证件类型
		       CR.OPERATE_DATE,--维护时间
		       CR.OPERATE_REASON,--维护原因
		       CR.CHECK_DATE,--验真时间
		       (SELECT TC.SOURCE_NAME FROM DEV_NB.T_CHECK_SOURCE TC WHERE TC.SOURCE_CODE = CR.CHECK_SOURCE) AS CHECK_RESULT_SOURCE,--验真来源      
		       (SELECT CT.TYPE_NAME FROM DEV_NB.T_NB_CHECK_IDENTITY_TYPE CT WHERE CR.IDENTITY_CHECK = CT.TYPE_CODE) AS CHECK_TYPE,--服务类型      
		       U.USER_NAME , --维护人员
		       null IDENTITY_DETAIL_ID,--验真明细id
		       ADD_MONTHS(CR.OPERATE_DATE,12*10) VALID_ENDDATE,--有效止期
		       U.USER_NAME AS CHECK_OPERATOR_NAME--操作人员
		 FROM DEV_NB.T_NB_CUSTOMER_CHECK_RESULT CR
		 INNER JOIN DEV_NB.T_NB_CONTRACT_CUST_VERIFY NCV
		    ON CR.CHECK_ID = NCV.CHECK_ID
		 LEFT JOIN DEV_PAS.T_UDMP_USER U
		    ON CR.USER_CODE = U.USER_ID
		 WHERE NCV.APPLY_CODE = #{apply_code}/*投保单号*/
		   AND CR.CUSTOMER_NAME = #{check_name}/*身份识别姓名*/
		   AND NVL(CR.CERTI_CODE, CR.CUSTOMER_ID_CODE) = #{certi_code}/*身份识别证件号码*/
		   AND (CR.IDENTITY_CHECK = '5' OR CR.IDENTITY_CHECK = '3')
	      ]]>
	      <if test=" cert_type != null and cert_type != '' ">
	      	<![CDATA[
		       AND NVL(CR.CERT_TYPE,'0')=#{cert_type}/*身份识别证件类型，传递不为空时增加此条件*/
	      	]]>
	      </if>
	 <![CDATA[ ORDER BY CHECK_DATE DESC]]>
     </sql>
     <select id="QRY_queryCustomerVerifyHistoryForPage"
        resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        SELECT * FROM (
        SELECT ROWNUM AS RN,
           CHECK_OPERATOR_NAME, /* 验真操作人*/
           CHECK_DATE, /* 验真时间*/
           CHECK_NAME, /*姓名*/
           CHECK_TYPE, /*验真类型*/
           CHECK_RESULT_SOURCE, /*验真来源*/
           STATUS,/*维护结果*/
           IDENTITY_DETAIL_ID, /*验真明细ID */
	       CERT_TYPE,/*证件类型*/
           CERTI_CODE,/*证件号码*/
           OPERATE_DATE,/*维护时间*/
           OPERATE_REASON,/*维护原因*/
           VALID_ENDDATE,/*有效止期：*/
           USER_NAME/*维护人员*/
      FROM ( ]]>
      <include refid="QRY_queryCustomerVerifyHistorySQL" /> 
       <![CDATA[
      ) B WHERE ROWNUM <= #{LESS_NUM}) B WHERE B.RN >#{GREATER_NUM}     
        ]]>
     </select>
      <select id="QRY_queryCustomerVerifyHistoryTotal"
        resultType="java.lang.Integer" parameterType="java.util.Map">
        <![CDATA[
        SELECT count(*) FROM (
         ]]>
         <include refid="QRY_queryCustomerVerifyHistorySQL" /> 
         <![CDATA[
        )
        ]]>
     </select>
    <!-- #50447 客户身份验真承保需求 客户验真历史查询  END-->
    <!-- #50447 客户身份验真承保需求 客户验真结果查询Start  -->
    <select id="QRY_queryCustomerVerifyResult"
        resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[
        SELECT T.*,to_number(#{logout_id}) AS LOGOUT_ID  FROM (
            SELECT '姓名' AS CHECK_ITEM_NAME,
                   PC.CUSTOMER_NAME AS CHECK_INPUT,
                   (CASE
                     WHEN (PC.LOGOUT_STATE = 1 OR PC.LOGOUT_STATE = 0) THEN
                      '一致'
                     ELSE
                      ''
                   END) AS CHECK_RESULT,
                   1 AS SORTORDER
              FROM DEV_PAS.T_LOGOUT_PERSONNEL_CHECK PC
             WHERE PC.LOGOUT_ID = #{logout_id}
            UNION
            SELECT '身份证号码' AS CHECK_ITEM_NAME,
                   PC.CUSTOMER_ID_CODE AS CHECK_INPUT,
                   (CASE
                     WHEN (PC.LOGOUT_STATE = 1 OR PC.LOGOUT_STATE = 0) THEN
                      '一致'
                     ELSE
                      ''
                   END) AS CHECK_RESULT,
                   2 AS SORTORDER
              FROM DEV_PAS.T_LOGOUT_PERSONNEL_CHECK PC
             WHERE PC.LOGOUT_ID = #{logout_id}
            UNION
            SELECT '人口状态' AS CHECK_ITEM_NAME,
                   '注销' AS CHECK_INPUT,
                   (CASE
                     WHEN (PC.LOGOUT_STATE = 1) THEN
                      '一致'
                     WHEN (PC.LOGOUT_STATE = 0) THEN
                      '不一致'
                     ELSE
                      ''
                   END) AS CHECK_RESULT,
                   3 AS SORTORDER
              FROM DEV_PAS.T_LOGOUT_PERSONNEL_CHECK PC
             WHERE PC.LOGOUT_ID = #{logout_id}
            UNION
            SELECT '原因标识' AS CHECK_ITEM_NAME,
                   LC.CAUSE_DESC AS CHECK_INPUT,
                   (CASE
                     WHEN (PC.LOGOUT_REASON IS NOT NULL AND PC.LOGOUT_REASON != '') THEN
                      '一致'
                     ELSE
                      '不一致'
                   END) AS CHECK_RESULT,
                   4 AS SORTORDER
              FROM DEV_PAS.T_LOGOUT_PERSONNEL_CHECK PC
              LEFT JOIN DEV_PAS.T_PERSON_LOGOUT_CAUSE LC
                ON PC.LOGOUT_CAUSE = LC.CAUSE_CODE
             WHERE PC.LOGOUT_ID = #{logout_id}
            ) T ORDER BY SORTORDER
          ]]>
     </select>
    <!-- #50447 客户身份验真承保需求 客户验真结果查询End  -->
    <!-- #50447 客户身份验真承保需求 客户验真契约维护结果查询Start  -->
    <sql id="QRY_queryContractCustVerifysSQL">
         <![CDATA[
       SELECT 
       ROWNUM AS RN,
      NCV.ORGAN_CODE,/*管理机构*/
       NCV.APPLY_CODE,/*投保单号*/
       CR.CUSTOMER_NAME,/*客户姓名*/
       NVL(CR.CERT_TYPE,0) AS CERT_TYPE,/*证件类型*/
       NVL(CR.CERTI_CODE,CR.CUSTOMER_ID_CODE) AS CERTI_CODE, /**证件号码*/
       CR.IDENTITY_CHECK,/*验真服务代码*/
       CT.TYPE_NAME,/*验真服务*/
       CR.CHECK_DATE,/*验真时间*/
       CR.STATUS,/*维护状态*/
       U.USER_NAME AS USER_CODE,/*维护操作员*/
       CR.OPERATE_DATE,/*维护时间*/
       CR.OPERATE_REASON/*修改原因*/
      FROM 
     DEV_NB.T_NB_CONTRACT_CUST_VERIFY NCV
     INNER JOIN DEV_NB.T_NB_CUSTOMER_CHECK_RESULT CR ON NCV.CHECK_ID=CR.CHECK_ID
     INNER JOIN DEV_NB.T_CHECK_IDENTITY_TYPE CT ON CR.IDENTITY_CHECK=CT.TYPE_CODE
     LEFT JOIN DEV_PAS.T_UDMP_USER U ON CR.USER_CODE=U.USER_ID
     WHERE
       NCV.APPLY_CODE=#{apply_code} /*投保单号*/
      AND CR.CUSTOMER_NAME=#{customer_name}/*客户姓名 */
      AND CR.CUSTOMER_NAME=#{customer_name}/*客户姓名 */
	   ]]>
	   <if test=" customer_id_code != null and customer_id_code != '' ">
	   		<![CDATA[AND (CR.CUSTOMER_ID_CODE = #{customer_id_code} OR CR.CERTI_CODE=#{customer_id_code})/*客户证件号码 */]]>
	   </if>
	   <if test=" cert_type != null and cert_type != '' ">
		   <![CDATA[ AND nvl(CR.CERT_TYPE,'0')=#{cert_type}/*证件类型*/ ]]> 
	   </if>
    </sql>
    <select id="QRY_queryContractCustVerifysForPage" resultType="java.util.Map" parameterType="java.util.Map">
     <![CDATA[
       SELECT * FROM (
       SELECt * FROM (
     ]]>  
     <include refid="QRY_queryContractCustVerifysSQL" /> 
      <![CDATA[
      ) B  WHERE B.RN <= #{LESS_NUM}
      ) B  WHERE B.RN > #{GREATER_NUM}  
     ]]>      
    </select>
    
   <select id="QRY_queryContractCustVerifysTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
    <![CDATA[
    SELECT count(*) FROM (
     ]]>
     <include refid="QRY_queryContractCustVerifysSQL" /> 
     <![CDATA[
    )
    ]]>
  </select>
    <!-- #50447 客户身份验真承保需求 客户验真契约维护结果查询END  -->
    <!-- #46080 查询投保单投保人、被保人是否存在身份验真和身份识别START -->
    <select id="find_isExistsVerifyHistory" resultType="java.lang.Integer" parameterType="java.util.Map">
    	<![CDATA[
			SELECT COUNT(*) as CNT
			  FROM DEV_PAS.T_IDENTITY_CHECK_MAIN A
			 INNER JOIN (SELECT C.CUSTOMER_ID, /*客户编号*/
			                    C.CUSTOMER_NAME, /*客户姓名*/
			                    '投保人' AS CUSTOMER_ROLE, /*客户角色*/
			                    C.CUSTOMER_GENDER, /*客户性别*/
			                    C.CUSTOMER_CERT_TYPE, /*客户证件类型*/
			                    C.CUSTOMER_CERTI_CODE /*客户证件号码*/
			               FROM DEV_NB.T_NB_POLICY_HOLDER　 PH
			              INNER JOIN DEV_NB.T_CUSTOMER C
			                 ON PH.CUSTOMER_ID = C.CUSTOMER_ID
			              WHERE 1 = 1
			                AND PH.APPLY_CODE = #{apply_code}/*投保单号*/
			             union
			             SELECT C.CUSTOMER_ID, /*客户编号*/
			                    C.CUSTOMER_NAME, /*被保人姓名*/
			                    '被保人' AS CUSTOMER_ROLE, /*客户角色*/
			                    C.CUSTOMER_GENDER, /*客户性别*/
			                    C.CUSTOMER_CERT_TYPE, /*客户证件类型*/
			                    C.CUSTOMER_CERTI_CODE /*客户证件号码*/
			               FROM DEV_NB.T_NB_BENEFIT_INSURED BI
			              INNER JOIN DEV_NB.T_NB_INSURED_LIST IL
			                 ON BI.INSURED_ID = IL.LIST_ID
			              INNER JOIN DEV_NB.T_CUSTOMER C
			                 ON IL.CUSTOMER_ID = C.CUSTOMER_ID
			              WHERE 1 = 1
			                AND BI.ORDER_ID < 2
			                AND BI.APPLY_CODE = #{apply_code}/*投保单号*/
			             union
			             SELECT C.CUSTOMER_ID, /*客户编号*/
			                    C.CUSTOMER_NAME, /*被保人姓名*/
			                    '第二被保人' AS CUSTOMER_ROLE, /*客户角色*/
			                    C.CUSTOMER_GENDER, /*客户性别*/
			                    C.CUSTOMER_CERT_TYPE, /*客户证件类型*/
			                    C.CUSTOMER_CERTI_CODE /*客户证件号码*/
			               FROM DEV_NB.T_NB_BENEFIT_INSURED BI
			              INNER JOIN DEV_NB.T_NB_INSURED_LIST IL
			                 ON BI.INSURED_ID = IL.LIST_ID
			              INNER JOIN DEV_NB.T_CUSTOMER C
			                 ON IL.CUSTOMER_ID = C.CUSTOMER_ID
			              WHERE 1 = 1
			                AND BI.ORDER_ID = 2
			                AND BI.APPLY_CODE = #{apply_code}/*投保单号*/
			             ) B ON A.CHECK_NAME=B.CUSTOMER_NAME AND A.CHECK_ID_CODE=B.CUSTOMER_CERTI_CODE 
			 WHERE 1 = 1
			   AND A.CHECK_TYPE = '2'
			   AND A.CHECK_SOURCE = '9'
			  
			]]>
    </select>
    <!-- #46080 查询投保单投保人、被保人是否存在身份验真和身份识别END -->
    <!-- #46080 查询投被保人,第一第二被保人关系 start -->
    <select id="QRY_queryCustomerRela" resultType="java.util.Map" parameterType="java.util.Map">
    	<![CDATA[
    		    SELECT C.CUSTOMER_ID, /*客户编号*/
		          C.CUSTOMER_NAME, /*被保人姓名*/
		          '被保人' AS CUSTOMER_ROLE, /*客户角色*/
		          IL.RELATION_TO_PH AS RELATION_TO_PH
		           FROM DEV_NB.T_NB_BENEFIT_INSURED BI
		           INNER JOIN DEV_NB.T_NB_INSURED_LIST IL 
		          ON BI.INSURED_ID=IL.LIST_ID
		         INNER JOIN DEV_NB.T_CUSTOMER C
		            ON IL.CUSTOMER_ID= C.CUSTOMER_ID
		          WHERE 1 = 1
		            AND BI.ORDER_ID < 2
		            AND BI.APPLY_CODE = #{apply_code}/*投保单号*/
		         union
		         SELECT C.CUSTOMER_ID, /*客户编号*/
		                C.CUSTOMER_NAME, /*被保人姓名*/
		                '第二被保人' AS CUSTOMER_ROLE, /*客户角色*/
		                BI.RELATION_TO_INSURED_1 AS RELATION_TO_PH
		           FROM DEV_NB.T_NB_BENEFIT_INSURED BI
		          INNER JOIN DEV_NB.T_NB_INSURED_LIST IL 
		          ON BI.INSURED_ID=IL.LIST_ID
		          INNER JOIN DEV_NB.T_CUSTOMER C
		            ON IL.CUSTOMER_ID= C.CUSTOMER_ID
		          WHERE 1 = 1
		            AND BI.ORDER_ID = 2
		            AND BI.APPLY_CODE = #{apply_code}
    	]]>
    </select>
    <!-- #46080 查询投被保人,第一第二被保人关系 end -->
</mapper>