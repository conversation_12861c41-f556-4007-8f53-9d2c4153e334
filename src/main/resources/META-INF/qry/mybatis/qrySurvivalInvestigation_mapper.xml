<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.UWSurvivalInvestigationDetaiDaoImpl">
<!--
	<sql id="survivalInvestigationWhereCondition">
		<if test=" examine_other != null and examine_other != ''  "><![CDATA[ AND A.EXAMINE_OTHER = #{examine_other} ]]></if>
		<if test=" positive_indi  != null "><![CDATA[ AND A.POSITIVE_INDI = #{positive_indi} ]]></if>
		<if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" INSERT_TIME  != null  and  INSERT_TIME  != ''  "><![CDATA[ AND A.INSERT_TIME = #{INSERT_TIME} ]]></if>
		<if test=" rreport_id  != null "><![CDATA[ AND A.RREPORT_ID = #{rreport_id} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" rreport_code != null and rreport_code != ''  "><![CDATA[ AND A.RREPORT_CODE = #{rreport_code} ]]></if>
		<if test=" role_type  != null "><![CDATA[ AND A.ROLE_TYPE = #{role_type} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" investigator_level  != null "><![CDATA[ AND A.INVESTIGATOR_LEVEL = #{investigator_level} ]]></if>
		<if test=" gender_code  != null "><![CDATA[ AND A.GENDER_CODE = #{gender_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" customer_birth  != null  and  customer_birth  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTH = #{customer_birth} ]]></if>
		<if test=" examine_reason != null and examine_reason != ''  "><![CDATA[ AND A.EXAMINE_REASON = #{examine_reason} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="querySurvivalInvestigationByRreportIdCondition">
		<if test=" rreport_id  != null "><![CDATA[ AND A.RREPORT_ID = #{rreport_id} ]]></if>
	</sql>	

<sql id="querySurvivalInvestigationByCusAndApplyCode">
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
	</sql>

	<sql id="querySurvivalInvestigationByUwIdCondition">
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
	</sql>	
<!-- 添加操作 -->
	<insert id="addSurvivalInvestigation"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="rreport_id">
			SELECT S_T_SURVIVAL_INVESTIGATION.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO T_SURVIVAL_INVESTIGATION(
				EXAMINE_OTHER, POSITIVE_INDI, CUSTOMER_NAME, DOC_LIST_ID, UW_ID, INSERT_TIME, RREPORT_ID, 
				CUSTOMER_ID, UPDATE_TIME, APPLY_CODE, RREPORT_CODE, ROLE_TYPE, INSERT_TIMESTAMP, POLICY_CODE, 
				UPDATE_BY, INVESTIGATOR_LEVEL, GENDER_CODE, UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID, CUSTOMER_BIRTH, 
				EXAMINE_REASON ) 
			VALUES (
				#{examine_other, jdbcType=VARCHAR}, #{positive_indi, jdbcType=NUMERIC} , #{customer_name, jdbcType=VARCHAR} , #{doc_list_id, jdbcType=NUMERIC} , #{uw_id, jdbcType=NUMERIC} , SYSDATE , #{rreport_id, jdbcType=NUMERIC} 
				, #{customer_id, jdbcType=NUMERIC} , SYSDATE , #{apply_code, jdbcType=VARCHAR} , #{rreport_code, jdbcType=VARCHAR} , #{role_type, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{policy_code, jdbcType=VARCHAR} 
				, #{update_by, jdbcType=NUMERIC} , #{investigator_level, jdbcType=NUMERIC} , #{gender_code, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{customer_birth, jdbcType=DATE} 
				, #{examine_reason, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteSurvivalInvestigation" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM T_SURVIVAL_INVESTIGATION WHERE RREPORT_ID = #{rreport_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateSurvivalInvestigation" parameterType="java.util.Map">
		<![CDATA[ UPDATE T_SURVIVAL_INVESTIGATION ]]>
		<set>
		<trim suffixOverrides=",">
			EXAMINE_OTHER = #{examine_other, jdbcType=VARCHAR} ,
		    POSITIVE_INDI = #{positive_indi, jdbcType=NUMERIC} ,
			CUSTOMER_NAME = #{customer_name, jdbcType=VARCHAR} ,
		    DOC_LIST_ID = #{doc_list_id, jdbcType=NUMERIC} ,
		    UW_ID = #{uw_id, jdbcType=NUMERIC} ,
		    INSERT_TIME =  SYSDATE,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
			RREPORT_CODE = #{rreport_code, jdbcType=VARCHAR} ,
		    ROLE_TYPE = #{role_type, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    INVESTIGATOR_LEVEL = #{investigator_level, jdbcType=NUMERIC} ,
		    GENDER_CODE = #{gender_code, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    CUSTOMER_BIRTH = #{customer_birth, jdbcType=DATE} ,
			EXAMINE_REASON = #{examine_reason, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE RREPORT_ID = #{rreport_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findSurvivalInvestigationByUwId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXAMINE_OTHER, A.POSITIVE_INDI, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.INSERT_TIME, A.RREPORT_ID, 
			A.CUSTOMER_ID, A.APPLY_CODE, A.RREPORT_CODE, A.ROLE_TYPE, A.POLICY_CODE, 
			A.INVESTIGATOR_LEVEL, A.GENDER_CODE, A.POLICY_ID, A.CUSTOMER_BIRTH, 
			A.EXAMINE_REASON FROM T_SURVIVAL_INVESTIGATION A WHERE 1 = 1  ]]>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<include refid="querySurvivalInvestigationByUwIdCondition" />
		<![CDATA[ ORDER BY A.RREPORT_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapSurvivalInvestigation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXAMINE_OTHER, A.POSITIVE_INDI, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.INSERT_TIME, A.RREPORT_ID, 
			A.CUSTOMER_ID, A.APPLY_CODE, A.RREPORT_CODE, A.ROLE_TYPE, A.POLICY_CODE, 
			A.INVESTIGATOR_LEVEL, A.GENDER_CODE, A.POLICY_ID, A.CUSTOMER_BIRTH, 
			A.EXAMINE_REASON FROM T_SURVIVAL_INVESTIGATION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RREPORT_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllSurvivalInvestigation" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.EXAMINE_OTHER, A.POSITIVE_INDI, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.INSERT_TIME, A.RREPORT_ID, 
			A.CUSTOMER_ID, A.APPLY_CODE, A.RREPORT_CODE, A.ROLE_TYPE, A.POLICY_CODE, 
			A.INVESTIGATOR_LEVEL, A.GENDER_CODE, A.POLICY_ID, A.CUSTOMER_BIRTH, 
			A.EXAMINE_REASON FROM T_SURVIVAL_INVESTIGATION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RREPORT_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findSurvivalInvestigationTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM dev_uw.T_SURVIVAL_INVESTIGATION A WHERE 1 = 1  ]]>
		<if test=" apply_code  != null "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
	</select>

<!-- 分页查询操作 -->
	<select id="querySurvivalInvestigationForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.EXAMINE_OTHER, B.POSITIVE_INDI, B.CUSTOMER_NAME, B.DOC_LIST_ID, B.UW_ID, B.INSERT_TIME, B.RREPORT_ID, 
			B.CUSTOMER_ID, B.APPLY_CODE, B.RREPORT_CODE, B.ROLE_TYPE, B.POLICY_CODE, 
			B.INVESTIGATOR_LEVEL, B.GENDER_CODE, B.POLICY_ID, B.CUSTOMER_BIRTH, 
			B.EXAMINE_REASON FROM (
					SELECT ROWNUM RN, A.EXAMINE_OTHER, A.POSITIVE_INDI, A.CUSTOMER_NAME, A.DOC_LIST_ID, A.UW_ID, A.INSERT_TIME, A.RREPORT_ID, 
			A.CUSTOMER_ID, A.APPLY_CODE, A.RREPORT_CODE, A.ROLE_TYPE, A.POLICY_CODE, 
			A.INVESTIGATOR_LEVEL, A.GENDER_CODE, A.POLICY_ID, A.CUSTOMER_BIRTH, 
			A.EXAMINE_REASON FROM T_SURVIVAL_INVESTIGATION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.RREPORT_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>

<!-- 根据通知书ID查询生调项目列表，联表查询操作 -->
	<select id="UW_findSurvivalName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.RREPORT_ID, C.EXAMINE_CODE, C.EXAMINE_NAME
				  FROM T_SURVIVAL_INVESTIGATION       A,
				       T_SURVIVAL_INVESTIGATION_DETAI B,
				       T_EXAMINE_CODE                 C
				 WHERE A.RREPORT_ID = B.RREPORT_ID
				   AND B.EXAMINE_CODE = C.EXAMINE_CODE
  				   AND A.DOC_LIST_ID = #{doc_list_id} ORDER BY A.RREPORT_ID, C.EXAMINE_CODE ]]>		 
	</select>
	
	
	
	
</mapper>
