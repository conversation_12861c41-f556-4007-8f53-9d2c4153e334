<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QueryPayPlanDetailInfoDaoImpl">
<select id="PA_findPayPlanDetailInfo" resultType="java.util.Map" parameterType="java.util.Map">
<![CDATA[SELECT DISTINCT C.POLICY_CODE, /*保单号*/
       C.PLAN_ID, /*计划ID*/
       C.BUSI_ITEM_ID, /*险种ID*/
       C.BUSI_PROD_CODE, /*险种代码*/
       C.BUSI_PROD_NAME, /*险种名称*/
       C.PRODUCT_CODE, /*责任组代码*/
       C.PRODUCT_NAME, /*责任组名称*/
       C.LIAB_CODE, /*责任代码*/
       C.LIAB_NAME, /*责任名称*/
       C.UNIT_NUMBER, /*应收应付号码*/
       C.DEDU_AMOUNT,/*贷款抵扣金额*/
       C.ACCEPT_CODE,/*贷款抵扣保全受理号*/
       C.REAL_PAY_DATE, /*应付日期*/
       C.SURVIVAL_MODE, /*领取形式代码*/
       C.SURVIVAL_W_MODE,/*支取形式代码*/
       C.FEE_AMOUNT, /*给付金额*/
       C.CONFIRM_DATE, /*业务核销日期*/
       C.FINISH_TIME, /*财务到帐日期*/
       C.PAYREFNO, /*实付号码*/
       C.PAY_DUE_DATE, /*本次领至日期*/
       C.SURVIVAL_INVEST_FLAG, /*生调标识*/
       C.SURVIVAL_MODE_NAME,/*领取形式名称*/ 
       C.SURVIVAL_W_MODE_NAME,/*支取形式名称*/
       C.ISSUE_BANK_NAME, /*实收实付开户银行*/
       C.ISSUE_BANK_CODE, /*实收实付开户银行编码*/
       C.ACCO_NAME, /*实收实付户名*/
       C.BANK_ACCOUNT, /*实收实付银行账户*/
       PLAN_BANK_CODE, /*生存给付计划收款人开户银行编码*/
       PLAN_BANK_NAME, /*生存给付计划收款人开户银行*/
       PLAN_ACCO_NAME, /*生存给付计划收款人户名*/
       PLAN_BANK_ACCOUNT, /*生存给付计划收款人银行账户*/
       IS_CM_AMOUNT /*是否为CM保全项产生的差额标识*/
  FROM (SELECT *
          FROM (SELECT DISTINCT PD.POLICY_CODE,
                                PD.PLAN_ID,
                                CBP.BUSI_ITEM_ID,
                                CBP.BUSI_PROD_CODE,
                                BP.PRODUCT_NAME_SYS BUSI_PROD_NAME,
                                CP.PRODUCT_CODE,
                                PRL.PRODUCT_NAME,
                                PP.LIAB_CODE,
                                PLA.LIAB_NAME,
                                PD.UNIT_NUMBER,
                                SDT.DEDU_AMOUNT,
               					SDT.ACCEPT_CODE,
                                PD.PAY_DUE_DATE AS REAL_PAY_DATE, /*应付日期*/
                                CONCAT(PP.SURVIVAL_MODE ,'') SURVIVAL_MODE,/*生存领取形式*/
                                CONCAT(PP.SURVIVAL_W_MODE , '') SURVIVAL_W_MODE,/*支取形式代码*/ 
                                (CASE
                                  WHEN PD.SURVIVAL_TYPE IN ('1', '3', '5') THEN
                                   PD.FEE_AMOUNT
                                  ELSE
                                   PD.FEE_AMOUNT * -1
                                END) AS FEE_AMOUNT, /*金额*/
                                (CASE
                                  WHEN PAR.Unit_Number is not null AND
                                       PAR.FEE_STATUS <> '01' AND
                                       PAR.FEE_STATUS <> '16' THEN
                                   PAR.FINISH_TIME
                                  ELSE
                                   PAR.STATISTICAL_DATE
                                END) AS CONFIRM_DATE, /*业务核销时间*/
                                PAR.FINISH_TIME, /*收付费到账时间*/
                                CD.PAYREFNO,
								PD.SURVIVAL_INVEST_FLAG, /*生调标识*/
                                NVL((SELECT MIN(T.PAY_DUE_DATE)
                                      FROM DEV_PAS.T_PAY_DUE T
                                     WHERE T.PLAN_ID = PD.PLAN_ID
                                       AND T.PAY_DUE_DATE > PD.PAY_DUE_DATE),
                                    PP.PAY_DUE_DATE) AS PAY_DUE_DATE, /*本次领至日期*/
                                (SELECT TSM.MODE_NAME FROM DEV_PAS.T_SURVIVAL_MODE TSM WHERE TSM.MODE_CODE = PP.SURVIVAL_MODE) SURVIVAL_MODE_NAME,  /*领取形式名称*/         
                                (SELECT TSWM.MODE_NAME FROM DEV_PAS.T_SURVIVAL_W_MODE TSWM WHERE TSWM.MODE_CODE = PP.SURVIVAL_W_MODE) SURVIVAL_W_MODE_NAME, /*支取形式名称*/
                                (SELECT TB.BANK_NAME FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = PAR.BANK_CODE) ISSUE_BANK_NAME, /*实收实付开户银行*/
                                (SELECT NVL(TB.STD_BANK_CODE,TB.BANK_CODE) FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = PAR.BANK_CODE) ISSUE_BANK_CODE, /*实收实付开户银行编码*/
                                PAR.BANK_USER_NAME ACCO_NAME, /*实收实付户名*/
                                PAR.BANK_ACCOUNT BANK_ACCOUNT, /*实收实付银行账户*/
                                (SELECT NVL(TB.STD_BANK_CODE,TB.BANK_CODE) FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = TBA.BANK_CODE) PLAN_BANK_CODE, /*生存给付计划收款人开户银行编码*/ 
                                (SELECT TB.BANK_NAME FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = TBA.BANK_CODE) PLAN_BANK_NAME, /*生存给付计划收款人开户银行*/
                                TBA.ACCO_NAME PLAN_ACCO_NAME, /*生存给付计划收款人户名*/ 
                                TBA.BANK_ACCOUNT PLAN_BANK_ACCOUNT, /*生存给付计划收款人银行账户*/
                                '' IS_CM_AMOUNT /*是否为CM保全项产生的差额标识*/     
                          FROM DEV_PAS.T_PAY_DUE PD
                          LEFT JOIN DEV_PAS.T_PAY_PLAN PP
                            ON pd.plan_id = pp.plan_id
                         INNER JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
                            ON CBP.BUSI_ITEM_ID = PD.BUSI_ITEM_ID
                         INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                            ON BP.PRODUCT_CODE_SYS = CBP.BUSI_PROD_CODE
                         INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
                            ON CP.ITEM_ID = PD.ITEM_ID
                         INNER JOIN DEV_PDS.T_PRODUCT_LIFE PRL
                            ON PRL.PRODUCT_ID = CP.PRODUCT_ID
                          LEFT JOIN DEV_PAS.T_LIABILITY PLA
                            ON PLA.LIAB_ID = PD.LIAB_ID
                          LEFT JOIN DEV_CAP.V_PREM_ARAP PAR
                            ON PAR.UNIT_NUMBER = PD.UNIT_NUMBER
                            AND PAR.BUSI_PROD_CODE = PD.BUSI_PROD_CODE
                          LEFT JOIN DEV_CAP.V_CASH_DETAIL CD
                            ON PAR.UNIT_NUMBER = CD.UNIT_NUMBER
                          LEFT JOIN DEV_PAS.T_PAY_PLAN_PAYEE TPPP
						    ON TPPP.PLAN_ID = PD.PLAN_ID
						   AND TPPP.ITEM_ID = PD.ITEM_ID
						   AND TPPP.POLICY_ID = PD.POLICY_ID
						   AND TPPP.BUSI_ITEM_ID = PD.BUSI_ITEM_ID
						  LEFT JOIN DEV_PAS.T_BANK_ACCOUNT TBA
						    ON TBA.ACCOUNT_ID = TPPP.PAYEE_ACCOUNT_ID
						  LEFT JOIN DEV_PAS.T_SURVIVAL_DEDUCTION_TASK SDT
        	    			ON SDT.PAY_ID = PD.PAY_ID
                			AND SDT.DEDU_RESULT = '1'
                         WHERE 1 = 1
                           AND (PAR.UNIT_NUMBER IS NOT NULL OR PD.FEE_STATUS = '00')
                           AND (PD.FEE_STATUS != '02')]]>
      <if test=" policy_code  != null   and  policy_code  != ''"><![CDATA[ AND PD.POLICY_CODE = #{policy_code} ]]></if>	                                                                                                                                     
      <if test=" busi_item_id  != null  and  busi_item_id  != '' "><![CDATA[ AND PD.BUSI_ITEM_ID = #{busi_item_id} ]]></if>	                                                                                                                                     
      <if test=" plan_id  != null and  plan_id  != '' "><![CDATA[ AND PD.PLAN_ID = #{plan_id} ]]></if>	  
      <if test=" start_date  != null and  start_date  != ''"><![CDATA[ AND PD.PAY_DUE_DATE >= #{start_date} ]]></if>	
      <if test=" end_date  != null and  end_date  != ''"><![CDATA[ AND PD.PAY_DUE_DATE <= #{end_date} ]]></if>	                                                                                                                                                   
          <![CDATA[  UNION               
                SELECT *
                  FROM (WITH TA AS (SELECT DISTINCT 
                                           B.POLICY_CODE,
                                           NEW_.PLAN_ID,
                                           NEW_.BUSI_ITEM_ID,
                                           NEW_.BUSI_PROD_CODE,
                                           (SELECT TT.PRODUCT_NAME_SYS
                                              FROM DEV_PDS.T_BUSINESS_PRODUCT TT
                                             WHERE TT.PRODUCT_CODE_SYS =
                                                   NEW_.BUSI_PROD_CODE) AS BUSI_PROD_NAME,
                                           NEW_.PRODUCT_CODE,
                                           (SELECT TP.PRODUCT_NAME
                                              FROM DEV_PDS.T_PRODUCT_LIFE TP
                                             WHERE TP.INTERNAL_ID =
                                                   NEW_.PRODUCT_CODE) AS PRODUCT_NAME,
                                           TPL.LIAB_CODE,
                                           (SELECT PLA.LIAB_NAME
                                              FROM DEV_PDS.T_LIABILITY PLA
                                             WHERE PLA.LIAB_ID = NEW_.LIAB_ID) AS LIAB_NAME,
                                           NEW_.PAY_DUE_DATE AS REAL_PAY_DATE,
                                           '1' SURVIVAL_MODE,/*生存领取形式*/
                                           (SELECT TSM.MODE_NAME FROM dev_pas.T_SURVIVAL_MODE TSM WHERE TSM.MODE_CODE = '1') SURVIVAL_MODE_NAME,
                                           NEW_.FEE_AMOUNT - OLD_.FEE_AMOUNT AS FEE_AMOUNT,
                                           NEW_.SURVIVAL_INVEST_FLAG,
                                           NVL((SELECT MIN(TCP.PAY_DUE_DATE)
                                                 FROM DEV_PAS.T_CS_PAY_DUE TCP
                                                WHERE B.POLICY_CHG_ID = TCP.POLICY_CHG_ID
                                                  AND B.POLICY_ID = TCP.POLICY_ID
                                                  AND TCP.PLAN_ID = NEW_.PLAN_ID
                                                  AND TCP.OLD_NEW = '1'
                                                  AND TCP.FEE_STATUS = '01'
                                                  AND TCP.PAY_DUE_DATE > NEW_.PAY_DUE_DATE),TPL.PAY_DUE_DATE) AS PAY_DUE_DATE,
                                             A.ACCEPT_CODE,
                                           (SELECT NVL(TB.STD_BANK_CODE, TB.BANK_CODE) FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = tba.BANK_CODE) PLAN_BANK_CODE,
									       (SELECT TB.BANK_NAME FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = tba.BANK_CODE) PLAN_BANK_NAME,
									       tba.ACCO_NAME PLAN_ACCO_NAME,
									       tba.BANK_ACCOUNT PLAN_BANK_ACCOUNT,
									       (SELECT 'X' FROM DEV_PAS.T_CS_PAY_PLAN PP WHERE PP.PLAN_ID = new_.PLAN_ID AND PP.PAY_PLAN_TYPE IN ('2','3','4','5','8','10','11') AND ROWNUM = 1) IS_CM_AMOUNT
                                              from dev_pas.t_cs_accept_change a INNER
                                              JOIN dev_pas.t_cs_policy_change b
                                                ON a.change_id = b.change_id
                                               and a.accept_id = b.accept_id
                                             inner join DEV_PAS.T_CS_PAY_DUE new_
                                                on b.policy_chg_id = new_.policy_chg_id
                                               and b.policy_id = new_.policy_id
                                               and new_.old_new = '1'
                                               and new_.fee_status = '01']]>
                                               
                    <if test=" plan_id  != null "><![CDATA[ AND NEW_.PLAN_ID  = #{plan_id} ]]></if>	  
                       
                                           <![CDATA[  inner join DEV_PAS.T_CS_PAY_DUE old_
                                                on b.policy_chg_id = old_.policy_chg_id
                                               and b.policy_id = old_.policy_id
                                               and old_.old_new = '0'
                                               and old_.fee_status = '01'
                                               and new_.pay_num = old_.pay_num 
                                               and new_.plan_id = old_.plan_id]]>
                    <if test=" plan_id  != null "><![CDATA[ AND OLD_.PLAN_ID  = #{plan_id} ]]></if>	     
                                           <![CDATA[   inner join DEV_PAS.t_Cs_Pay_Plan tpl
                                                on b.policy_chg_id = tpl.policy_chg_id
                                               and b.policy_id = tpl.policy_id
                                               and tpl.old_new = '1'
                                               and new_.plan_id = tpl.plan_id
                                              left join dev_pas.t_pay_plan_payee tppp
											    on tppp.plan_id = new_.plan_id
											   and tppp.item_id = new_.item_id
											   and tppp.policy_id = new_.policy_id
											   and tppp.busi_item_id = new_.busi_item_id
											  left join dev_pas.t_bank_account tba
											    on tba.account_id = tppp.payee_account_id
                                             where  a.SERVICE_CODE = 'CM'
                                               and a.accept_status = 18 ]]>
                    <if test=" policy_code  != null "><![CDATA[ AND B.POLICY_CODE = #{policy_code} ]]></if>	                                                                                                                                     
                                               
                                      <![CDATA[  ), 
                         tb as (select PAR.FINISH_TIME AS CONFIRM_DATE,
                                     PAR.FINISH_TIME,
                                     CD.PAYREFNO,
                                     PAR.UNIT_NUMBER,
                                     (CASE
                                       WHEN PAR.PAY_MODE = 10 THEN
                                        '1-自行领取'
                                       ELSE
                                        '2-银行转账'
                                     END) AS SURVIVAL_W_MODE,
                                     PAR.BUSINESS_CODE,
                                     (CASE
                                       WHEN PAR.PAY_MODE = 10 THEN
                                        '自行领取'
                                       ELSE
                                        '银行转账'
                                     END) AS SURVIVAL_W_MODE_NAME,
                                     PAR.BANK_CODE, 
                                     PAR.BANK_USER_NAME, 
                                     PAR.BANK_ACCOUNT
                                  from DEV_CAP.V_PREM_ARAP PAR
                                  LEFT JOIN DEV_CAP.V_CASH_DETAIL CD 
                                  ON PAR.UNIT_NUMBER = CD.UNIT_NUMBER 
                                   where PAR.DERIV_TYPE = '004' 
                                     AND PAR.SERVICE_CODE = 'CM'
                                     AND par.FEE_TYPE IN('P004390200', 'G004530400') 
                                     and par.fee_status in('01', '19', '16','00') ]]> 							
 <if test=" policy_code  != null "><![CDATA[ AND PAR.POLICY_CODE  = #{policy_code} ]]></if>            
                                <![CDATA[   group by par.finish_time,
                                            CD.PAYREFNO,
                                            PAR.UNIT_NUMBER,
                                            par.pay_mode,
                                            par.business_code,
                                            PAR.BANK_CODE, 
                                            PAR.BANK_USER_NAME, 
                                            PAR.BANK_ACCOUNT)
                                 select POLICY_CODE,
                                PLAN_ID,
                                BUSI_ITEM_ID,
                                BUSI_PROD_CODE,
                                BUSI_PROD_NAME,
                                PRODUCT_CODE,
                                PRODUCT_NAME,
                                LIAB_CODE,
                                LIAB_NAME,
                                UNIT_NUMBER,
                                null as dedu_amount,
        						null as dedu_accept_code,
                                REAL_PAY_DATE, /*应付日期*/
                                SURVIVAL_MODE, /*生存领取形式*/
                                SURVIVAL_W_MODE, /*生存领取形式*/
                                FEE_AMOUNT, /*金额*/
                                CONFIRM_DATE, /*业务核销时间*/
                                FINISH_TIME, /*收付费到账时间*/
                                PAYREFNO,
								SURVIVAL_INVEST_FLAG, /*生调标识*/ 
                                PAY_DUE_DATE, /*本次领至日期*/
                                SURVIVAL_MODE_NAME,/*领取形式名称*/ 
                                SURVIVAL_W_MODE_NAME,/*支取形式名称*/
                                (SELECT TB.BANK_NAME FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = BANK_CODE AND ROWNUM = 1) AS ISSUE_BANK_NAME,/*实收实付开户银行*/
                                (SELECT NVL(TB.STD_BANK_CODE,TB.BANK_CODE) FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = BANK_CODE AND ROWNUM = 1) AS ISSUE_BANK_CODE,/*实收实付开户银行编码*/
                                BANK_USER_NAME AS ACCO_NAME,/*实收实付户名*/
                                BANK_ACCOUNT,/*实收实付银行账户*/
                                PLAN_BANK_CODE, /*生存给付计划收款人开户银行编码*/
                                PLAN_BANK_NAME, /*生存给付计划收款人开户银行*/
                                PLAN_ACCO_NAME, /*生存给付计划收款人户名*/
                                PLAN_BANK_ACCOUNT, /*生存给付计划收款人银行账户*/
                                IS_CM_AMOUNT /*是否为CM保全项产生的差额标识*/
                                from    ta,tb where  ta.accept_code = tb.business_code)
											
                               union
                                 SELECT DISTINCT PD.POLICY_CODE,
												PD.PLAN_ID,
												PP.BUSI_ITEM_ID,
												PD.BUSI_PROD_CODE,
												BP.PRODUCT_NAME_SYS BUSI_PROD_NAME,
                                                CP.PRODUCT_CODE,
												PRL.PRODUCT_NAME,
												PP.LIAB_CODE,
												PLA.LIAB_NAME,
												PAR.UNIT_NUMBER,	
												SDT.DEDU_AMOUNT,
               									SDT.ACCEPT_CODE,																								 
												PD.PAY_DUE_DATE AS REAL_PAY_DATE, /*应付日期*/
                                                CONCAT(PP.SURVIVAL_MODE ,'') SURVIVAL_MODE,/*生存领取形式*/
                                                CONCAT(PP.SURVIVAL_W_MODE , '') SURVIVAL_W_MODE,/*支取形式代码*/
                                                (CASE
                                                    WHEN PD.SURVIVAL_TYPE IN ('1', '3', '5') THEN
                                                   PD.FEE_AMOUNT
                                                 ELSE
                                                   PD.FEE_AMOUNT * -1
                                                 END) AS FEE_AMOUNT, /*金额*/
												 PAR.STATISTICAL_DATE AS CONFIRM_DATE,
                                                 PAR.FINISH_TIME, 
												 CD.PAYREFNO,
                                                 PD.SURVIVAL_INVEST_FLAG,																										   
												 NVL((SELECT MIN(T.PAY_DUE_DATE)
                                                        FROM DEV_PAS.T_PAY_DUE T
                                                       WHERE T.PLAN_ID = PD.PLAN_ID
                                                         AND T.PAY_DUE_DATE > PD.PAY_DUE_DATE 
                                                         and t.fee_status = '01'),PP.PAY_DUE_DATE) AS PAY_DUE_DATE,
												 (SELECT TSM.MODE_NAME FROM DEV_PAS.T_SURVIVAL_MODE TSM WHERE TSM.MODE_CODE = PP.SURVIVAL_MODE) SURVIVAL_MODE_NAME,  /*领取形式名称*/         
												 (SELECT TSWM.MODE_NAME FROM DEV_PAS.T_SURVIVAL_W_MODE TSWM WHERE TSWM.MODE_CODE = PP.SURVIVAL_W_MODE) SURVIVAL_W_MODE_NAME, /*支取形式名称*/
												 (SELECT TB.BANK_NAME FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = PAR.BANK_CODE) ISSUE_BANK_NAME, /*实收实付开户银行*/
												 (SELECT NVL(TB.STD_BANK_CODE,TB.BANK_CODE) FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = PAR.BANK_CODE) ISSUE_BANK_CODE, /*实收实付开户银行编码*/
												 PAR.BANK_USER_NAME ACCO_NAME, /*实收实付户名*/
												 PAR.BANK_ACCOUNT BANK_ACCOUNT, /*实收实付银行账户*/
												 (SELECT NVL(TB.STD_BANK_CODE,TB.BANK_CODE) FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = TBA.BANK_CODE) PLAN_BANK_CODE, /*生存给付计划收款人开户银行编码*/ 
                                                 (SELECT TB.BANK_NAME FROM DEV_PAS.T_BANK TB WHERE TB.BANK_CODE = TBA.BANK_CODE) PLAN_BANK_NAME, /*生存给付计划收款人开户银行*/
				                                 TBA.ACCO_NAME PLAN_ACCO_NAME, /*生存给付计划收款人户名*/ 
				                                 TBA.BANK_ACCOUNT PLAN_BANK_ACCOUNT, /*生存给付计划收款人银行账户*/
				                                 (SELECT 'X' FROM DEV_PAS.T_PAY_PLAN PP WHERE PP.PLAN_ID = PD.PLAN_ID AND PP.PAY_PLAN_TYPE IN ('2','3','4','5','8','10','11')) IS_CM_AMOUNT /*是否为CM保全项产生的差额标识*/
                                   FROM DEV_PAS.T_PAY_DUE PD
                                   LEFT JOIN DEV_PAS.T_PAY_PLAN PP
                                     ON pd.plan_id = pp.plan_id
                                  INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
                                     ON BP.PRODUCT_CODE_SYS = pd.BUSI_PROD_CODE
                                  INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
                                     ON CP.ITEM_ID = PD.ITEM_ID
                                  INNER JOIN DEV_PDS.T_PRODUCT_LIFE PRL
                                     ON PRL.PRODUCT_ID = CP.PRODUCT_ID
                                   LEFT JOIN DEV_PAS.T_LIABILITY PLA
                                     ON PLA.LIAB_ID = PD.LIAB_ID
                                   LEFT JOIN DEV_CAP.V_PREM_ARAP PAR
                                     ON PAR.UNIT_NUMBER = PD.UNIT_NUMBER
                                   LEFT JOIN DEV_CAP.V_CASH_DETAIL CD
                                     ON PAR.UNIT_NUMBER = CD.UNIT_NUMBER
                                   LEFT JOIN DEV_PAS.T_PAY_PLAN_PAYEE TPPP
								     ON TPPP.PLAN_ID = PD.PLAN_ID
								    AND TPPP.ITEM_ID = PD.ITEM_ID
								    AND TPPP.POLICY_ID = PD.POLICY_ID
								    AND TPPP.BUSI_ITEM_ID = PD.BUSI_ITEM_ID
								   LEFT JOIN DEV_PAS.T_BANK_ACCOUNT TBA
								     ON TBA.ACCOUNT_ID = TPPP.PAYEE_ACCOUNT_ID
								   LEFT JOIN DEV_PAS.T_SURVIVAL_DEDUCTION_TASK SDT
					        	     ON SDT.PAY_ID = PD.PAY_ID
					                AND SDT.DEDU_RESULT = '1'
                                  WHERE pd.fee_status = '02' ]]>
                         <if test=" policy_code  != null   and  policy_code  != ''"><![CDATA[ AND PD.POLICY_CODE = #{policy_code} ]]></if>	                                                                                                                                     
      <if test=" busi_item_id  != null  and  busi_item_id  != '' "><![CDATA[ AND PD.BUSI_ITEM_ID = #{busi_item_id} ]]></if>	                                                                                                                                     
      <if test=" plan_id  != null and  plan_id  != '' "><![CDATA[ AND PD.PLAN_ID = #{plan_id} ]]></if>	  
      <if test=" start_date  != null and  start_date  != ''"><![CDATA[ AND PD.PAY_DUE_DATE >= #{start_date} ]]></if>	
      <if test=" end_date  != null and  end_date  != ''"><![CDATA[ AND PD.PAY_DUE_DATE <= #{end_date} ]]></if>	         
                                   <![CDATA[ and exists
                                  (select 1
                                           from dev_pas.t_cs_accept_change ta,
                                                dev_pas.t_cs_policy_change tb,
                                                dev_pas.t_cs_pay_due       tc
                                          where tb.accept_id = ta.accept_id
                                            and tb.policy_chg_id =
                                                tc.policy_chg_id
                                            and pd.plan_id = tc.plan_id
                                            and pd.pay_id = tc.pay_id
                                            and ta.service_code = 'CM'
                                            AND TA.ACCEPT_STATUS = '18'
                                            and tc.old_new = 0
                                            and tc.fee_status = '01')
                ) F
         ORDER BY REAL_PAY_DATE DESC) C
 WHERE C.FEE_AMOUNT != 0]]>
</select>

	<select id="PA_queryPolicyReleaseFlagDetail" resultType="java.util.Map" parameterType="java.util.Map">
	  <![CDATA[
		  SELECT A.SUB_POLICY_CODE,
	       A.SUB_BUSI_PROD_CODE,
	       B.LIABILITY_STATE,
	       TBP.PRODUCT_NAME_SYS,
	       A.RELATION_TYPE
	  FROM APP___PAS__DBUSER.T_CONTRACT_RELATION  A,
	       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD B,
	       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
	 WHERE 1 = 1
	   AND A.SUB_BUSI_ITEM_ID = B.BUSI_ITEM_ID
	   AND B.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
	   AND A.RELATION_TYPE in ('2', '3', '4')
	   AND B.LIABILITY_STATE = '1'
	   AND A.MASTER_POLICY_CODE = #{policy_code}
	  UNION 
	   SELECT TCM1.POLICY_CODE SUB_POLICY_CODE,
	       TCBP.BUSI_PROD_CODE SUB_BUSI_PROD_CODE,
	       TCM1.LIABILITY_STATE,
	       TBP.PRODUCT_NAME_SYS,
	       CASE
	       		WHEN TCM.POLICY_RELATION_TYPE = '0' THEN '2'
	       		WHEN TCM.POLICY_RELATION_TYPE = '1' THEN '3'
	       		WHEN TCM.POLICY_RELATION_TYPE = '2' THEN '4'
	       		ELSE ''
	       END AS RELATION_TYPE
	  FROM APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM,
	       APP___PAS__DBUSER.T_CONTRACT_MASTER    TCM1,
	       APP___PAS__DBUSER.T_CONTRACT_BUSI_PROD TCBP,
	       APP___PAS__DBUSER.T_BUSINESS_PRODUCT   TBP
	 WHERE 1 = 1
	   AND TCM.RELATION_POLICY_CODE IS NOT NULL
	   AND TCM.POLICY_CODE = #{policy_code}
	   AND TCM.RELATION_POLICY_CODE = TCM1.POLICY_CODE
	   AND TCM1.POLICY_CODE = TCBP.POLICY_CODE
	   AND TCM1.LIABILITY_STATE = '1'
	   AND TCBP.MASTER_BUSI_ITEM_ID IS NULL
	   AND (TCM.POLICY_RELATION_TYPE IS NULL OR TCM.POLICY_RELATION_TYPE in ('0', '1', '2'))
	   AND TCBP.BUSI_PRD_ID = TBP.BUSINESS_PRD_ID
	     ]]>
	</select>

</mapper>