<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.nci.tunan.qry.impl.nb.dao.IHealthInsuranceDao">
	
	<sql id="queryHealthInsuranceDetail_info">
		<![CDATA[	
		
			SELECT CC.ORGAN_CODE, CC.BUSINESS_CHANNEL, CC.SALES_DEPARTMENT, CC.INSURANCE_CODE, SUM(CC.PREMIUM) PREMIUM, 
				CC.SALESMAN_CODE, COUNT(CC.PREMIUM) COUNT_NUM, CC.PLATFORM_FLAG, CC.INTERMEDIARY_AGENT_CODE, 
				CC.INTERMEDIARY_AGENT_NAME, CC.BRANCH_CODE, CC.BRANCH_NAME, CC.PAYMENT_TYPE 
			FROM (SELECT M.ORGAN_CODE AS ORGAN_CODE,/*管理机构代码*/
				CASE WHEN M.CHANNEL_TYPE != '02' THEN '个人营销'
					WHEN M.CHANNEL_TYPE = '02' AND M.AGENCY_CODE IS NULL THEN '团险直销'
					WHEN M.CHANNEL_TYPE = '02' AND M.AGENCY_CODE IS NOT NULL THEN '团险中介' ELSE '未知' END AS BUSINESS_CHANNEL, /*业务渠道*/
				(SELECT MAX(UDOR.ORGAN_NAME) FROM DEV_NB.T_UDMP_ORG UDOR WHERE UDOR.ORGAN_CODE=M.ORGAN_CODE) AS SALES_DEPARTMENT,
				PROD.PRODUCT_CODE AS INSURANCE_CODE,
				PROD.TOTAL_PREM_AF AS PREMIUM,
				(SELECT MAX(CA.AGENT_CODE) FROM DEV_NB.T_NB_CONTRACT_AGENT CA WHERE CA.APPLY_CODE=M.APPLY_CODE AND CA.POLICY_CODE = M.POLICY_CODE ) AS SALESMAN_CODE,
				'上海APP' AS PLATFORM_FLAG,
				CASE WHEN M.AGENCY_CODE IS NULL THEN SUBSTR(C.AGENT_CODE,1,4) ELSE M.AGENCY_CODE END AS INTERMEDIARY_AGENT_CODE,
				CASE WHEN M.AGENCY_CODE IS NOT NULL THEN 
						(SELECT ACY.AGENCY_NAME FROM DEV_PAS.T_AGENCY ACY WHERE ACY.AGENCY_CODE = M.AGENCY_CODE)
					ELSE '' END AS INTERMEDIARY_AGENT_NAME,
				CASE WHEN M.AGENCY_CODE IS NOT NULL THEN 
						(SELECT TNCM.BRANCH_CODE FROM DEV_PAS.T_CONTRACT_MEDICAL TNCM WHERE TNCM.APPLY_CODE = M.APPLY_CODE)
					ELSE SUBSTR(C.AGENT_CODE,4) END AS BRANCH_CODE,
				CASE WHEN M.AGENCY_CODE IS NOT NULL THEN
						(SELECT TAB.BRANCH_NAME FROM DEV_PAS.T_AGENCY_BRANCH TAB, DEV_PAS.T_CONTRACT_MEDICAL TNCM WHERE TAB.BRANCH_CODE = TNCM.BRANCH_CODE
							AND TNCM.APPLY_CODE = M.APPLY_CODE)
					ELSE '' END AS BRANCH_NAME,
				CASE WHEN (SELECT MAX(P.PAY_MODE) FROM DEV_NB.T_PREM_ARAP P WHERE M.APPLY_CODE=P.APPLY_CODE AND M.POLICY_CODE = P.POLICY_CODE) = '18'
					THEN '医保扣款' ELSE '银行卡扣款' END AS PAYMENT_TYPE
			FROM DEV_PAS.T_CONTRACT_MASTER M
			INNER JOIN DEV_PAS.T_CONTRACT_MEDICAL NCM
				ON M.APPLY_CODE = NCM.APPLY_CODE
			LEFT JOIN DEV_NB.T_NB_CONTRACT_MASTER M1
				ON M.APPLY_CODE = M1.APPLY_CODE
			LEFT JOIN DEV_PAS.T_CONTRACT_BUSI_PROD CBP
				ON M.POLICY_ID = CBP.POLICY_ID
			LEFT JOIN DEV_CAP.T_PREM_ARAP A
				ON A.POLICY_CODE = M.POLICY_CODE
			LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT PROD
				ON M.POLICY_ID = PROD.POLICY_ID
			LEFT JOIN DEV_PAS.T_CONTRACT_AGENT C
				ON M.POLICY_CODE = C.POLICY_CODE
			WHERE 1=1
				AND M.LIABILITY_STATE = 1
				AND A.FEE_STATUS = '01'
				AND M.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')
				AND A.ARAP_FLAG = '1'
				AND ((NVL(CBP.RENEW_TIMES, 0) = 0 
		]]>
	</sql>
	<!-- 得到总数 -->
	<select id="nb_queryHealthInsuranceCount" resultType="Integer" parameterType="java.util.Map">
		<![CDATA[	select count(1) from (	]]>
		<include refid="queryHealthInsuranceDetail_info" />
		<include refid="queryHealthInsurance_info" />
		<![CDATA[	) CC GROUP BY CC.BRANCH_CODE,CC.SALESMAN_CODE,CC.INSURANCE_CODE,CC.PAYMENT_TYPE,
				CC.ORGAN_CODE,CC.BUSINESS_CHANNEL,CC.SALES_DEPARTMENT,CC.PLATFORM_FLAG,CC.INTERMEDIARY_AGENT_CODE,
			 	CC.INTERMEDIARY_AGENT_NAME,CC.BRANCH_NAME) ]]>
	</select>
	
	<!-- 得到分页结果集 -->
	<select id="nb_queryHealthInsurancePage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	
			SELECT T.RN,T.ORGAN_CODE,T.BUSINESS_CHANNEL,T.SALES_DEPARTMENT,T.INSURANCE_CODE,
				T.PREMIUM,T.SALESMAN_CODE,T.COUNT_NUM,T.PLATFORM_FLAG,T.INTERMEDIARY_AGENT_CODE,
				T.INTERMEDIARY_AGENT_NAME,T.BRANCH_CODE,T.BRANCH_NAME,T.PAYMENT_TYPE 
				FROM (
					SELECT ROWNUM RN,C.ORGAN_CODE,C.BUSINESS_CHANNEL,C.SALES_DEPARTMENT,C.INSURANCE_CODE,
					C.PREMIUM,C.SALESMAN_CODE,C.COUNT_NUM,C.PLATFORM_FLAG,C.INTERMEDIARY_AGENT_CODE,
					C.INTERMEDIARY_AGENT_NAME,C.BRANCH_CODE,C.BRANCH_NAME,C.PAYMENT_TYPE 
					FROM ( ]]>
		<include refid="queryHealthInsuranceDetail_info" />
		<include refid="queryHealthInsurance_info" />
		<![CDATA[	) CC GROUP BY CC.BRANCH_CODE,CC.SALESMAN_CODE,CC.INSURANCE_CODE,CC.PAYMENT_TYPE,
				CC.ORGAN_CODE,CC.BUSINESS_CHANNEL,CC.SALES_DEPARTMENT,CC.PLATFORM_FLAG,CC.INTERMEDIARY_AGENT_CODE,
			 	CC.INTERMEDIARY_AGENT_NAME,CC.BRANCH_NAME ) C ) T
				WHERE T.RN <= #{LESS_NUM} AND T.RN > #{GREATER_NUM} ]]>
	</select>
	
	<!-- 得到符合条件的所有的数据 -->
	<select id="nb_queryAllHealthInsuranceData" resultType="java.util.Map" parameterType="java.util.Map">
		<include refid="queryHealthInsuranceDetail_info" />
		<include refid="queryHealthInsurance_info" />
		<![CDATA[	) CC GROUP BY CC.BRANCH_CODE,CC.SALESMAN_CODE,CC.INSURANCE_CODE,CC.PAYMENT_TYPE,
				CC.ORGAN_CODE,CC.BUSINESS_CHANNEL,CC.SALES_DEPARTMENT,CC.PLATFORM_FLAG,CC.INTERMEDIARY_AGENT_CODE,
			 	CC.INTERMEDIARY_AGENT_NAME,CC.BRANCH_NAME ]]>
	</select>
	
	<!-- 查询条件sql语句 -->
	<sql id="queryHealthInsurance_info">
		<if test=" start_date  != null and start_date !='' "><!-- 统计起期 -->
			<![CDATA[  AND TRUNC(M.ISSUE_DATE) >= TO_DATE(#{start_date},'yyyy-mm-dd') ]]>
		</if>
		<if test=" end_date  != null and end_date !='' "><!-- 统计止期 -->
			<![CDATA[  AND TRUNC(M.ISSUE_DATE) <= TO_DATE(#{end_date},'yyyy-mm-dd') + 1 ]]>
		</if>
		<![CDATA[  ) OR (NVL(CBP.RENEW_TIMES, 0) <> 0 ]]>
		<if test=" start_date  != null and start_date !='' "><!-- 统计起期 -->
			<![CDATA[  AND TRUNC(CBP.VALIDATE_DATE) >= TO_DATE(#{start_date},'yyyy-mm-dd') ]]>
		</if>
		<if test=" end_date  != null and end_date !='' "><!-- 统计止期 -->
			<![CDATA[  AND TRUNC(CBP.VALIDATE_DATE) <= TO_DATE(#{end_date},'yyyy-mm-dd') + 1 ]]>
		</if>
		<![CDATA[) AND CBP.VALIDATE_DATE - A.VALIDATE_DATE = 0) ]]>
		<!--业务员渠道-->
		<if test=" business_channel != null and business_channel !='' and business_channel =='01'.toString() "><!-- 业务员渠道 :个人营销（营销、财富、保费等渠道业务员出单）-->
			<![CDATA[  AND M.CHANNEL_TYPE NOT IN ('02') ]]>
		</if>
		<if test=" business_channel != null and business_channel !='' and business_channel =='02'.toString() "><!-- 业务员渠道:团险直销（团体业务员出单）-->
			<![CDATA[  AND M.CHANNEL_TYPE IN ('02') AND M.AGENCY_CODE IS NULL ]]>
		</if>
		<if test=" business_channel != null and business_channel !='' and business_channel =='03'.toString() "><!-- 业务员渠道:团险中介（团体业务员绑定的中介机构出单）-->
			<![CDATA[  AND M.CHANNEL_TYPE IN ('02') AND M.AGENCY_CODE IS NOT NULL ]]>
		</if>
		<if test=" business_channel != null and business_channel !='' and business_channel =='04'.toString() "><!--页面多选个人营销+团险直销    业务员渠道:个人营销（营销、财富、保费等渠道业务员出单）和团险直销（团体业务员出单）-->
			<![CDATA[  AND M.AGENCY_CODE IS NULL ]]>
		</if>
		<if test=" business_channel != null and business_channel !='' and business_channel =='05'.toString() "><!--页面多选个人营销+团险中介    业务员渠道:个人营销（营销、财富、保费等渠道业务员出单）和团险中介（团体业务员绑定的中介机构出单）-->
			<![CDATA[  AND (M.CHANNEL_TYPE NOT IN ('02') OR (M.CHANNEL_TYPE IN ('02') AND M.AGENCY_CODE IS NOT NULL)) ]]>
		</if>
		<if test=" business_channel != null and business_channel !='' and business_channel =='06'.toString() "><!--页面多选团险直销+团险中介    业务员渠道:团险直销（团体业务员出单）和团险中介（团体业务员绑定的中介机构出单）-->
			<![CDATA[  AND M.CHANNEL_TYPE IN ('02') ]]>
		</if>
	</sql>
</mapper>