<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.cs.model.dao.impl.ICsPolicyChangeDao">

	<sql id="csPolicyChangeWhereCondition">
		<if test=" related_id  != null "><![CDATA[ AND A.RELATED_ID = #{related_id} ]]></if>
		<if test=" change_flag != null and change_flag != ''  "><![CDATA[ AND A.CHANGE_FLAG = #{change_flag} ]]></if>
		<if test=" apply_time  != null  and  apply_time  != ''  "><![CDATA[ AND A.APPLY_TIME = #{apply_time} ]]></if>
		<if test=" hesitate_flag  != null "><![CDATA[ AND A.HESITATE_FLAG = #{hesitate_flag} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" endorse_code != null and endorse_code != ''  "><![CDATA[ AND A.ENDORSE_CODE = #{endorse_code} ]]></if>
		<if test=" policy_lock_flag != null and policy_lock_flag != ''  "><![CDATA[ AND A.POLICY_LOCK_FLAG = #{policy_lock_flag} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" validate_time  != null  and  validate_time  != ''  "><![CDATA[ AND A.VALIDATE_TIME = #{validate_time} ]]></if>
		<if test=" change_id  != null "><![CDATA[ AND A.CHANGE_ID = #{change_id} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" print_num  != null "><![CDATA[ AND A.PRINT_NUM = #{print_num} ]]></if>
		<if test=" policy_copy_flag != null and policy_copy_flag != ''  "><![CDATA[ AND A.POLICY_COPY_FLAG = #{policy_copy_flag} ]]></if>
		<if test=" policy_chg_id  != null "><![CDATA[ AND A.POLICY_CHG_ID = #{policy_chg_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" accept_id  != null "><![CDATA[ AND A.ACCEPT_ID = #{accept_id} ]]></if>
		<if test=" notify_type != null and notify_type != ''  "><![CDATA[ AND A.NOTIFY_TYPE = #{notify_type} ]]></if>
		<if test=" print_status != null and print_status != ''  "><![CDATA[ AND A.PRINT_STATUS = #{print_status} ]]></if>
		<if test=" order_id  != null "><![CDATA[ AND A.ORDER_ID = #{order_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" print_type != null and print_type != ''  "><![CDATA[ AND A.PRINT_TYPE = #{print_type} ]]></if>
		<if test=" delay_cause != null and delay_cause != ''  "><![CDATA[ AND A.DELAY_CAUSE = #{delay_cause} ]]></if>
		<if test="change_ids  != null and change_ids.size()!=0">
			<![CDATA[ AND A.CHANGE_ID IN ]]>
			<foreach collection="change_ids" item="change_ids"
				index="index" open="(" close=")" separator=",">#{change_ids}</foreach></if>
		<if test="accept_ids  != null and accept_ids.size()!=0">
			<![CDATA[ AND A.ACCEPT_ID IN (]]>
			<foreach collection="accept_ids" item="accept_id_item"
				index="index" open="" close="" separator=",">#{accept_id_item}</foreach>
			<![CDATA[)]]>
		</if>
		<if test="policyCodes!= null and policyCodes.size()!=0">
			<![CDATA[ AND A.POLICY_CODE IN (]]>
			<foreach collection="policyCodes" item="policy_codes"
				index="index" open="" close="" separator=",">#{policy_codes}</foreach>
			<![CDATA[)]]>
		</if>
	</sql>

	<!--by zhaoyoan_wb 追加保费查询   -->
	<select id="queryAllAddPrem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select 'CS' source,a.policy_code,a.validate_time,a.service_code,
      		(select fee_amount from DEV_PAS.t_cs_accept_change b where b.accept_id=a.accept_id and b.ACCEPT_STATUS = '18') as fee_amount,
			'' pay_mode
			from dev_pas.t_cs_policy_change a,
                 dev_pas.V_CS_CONTRACT_BUSI_PROD_ALL CCBP,
	             DEV_PAS.T_BUSINESS_PRODUCT BP
            where a.service_code = 'AM'
              and a.VALIDATE_TIME is not null
              and CCBP.policy_chg_id = a.policy_chg_id
	          and BP.product_code_sys = CCBP.BUSI_PROD_CODE
	          AND BP.PRODUCT_CATEGORY1 IN ('20003','20004')
              and CCBP.old_new = '1'
			  and a.policy_code=#{policy_code,jdbcType=VARCHAR}
			  and CCBP.policy_code=#{policy_code,jdbcType=VARCHAR}
			  ]]>
	   <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND CCBP.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>    
		<![CDATA[
			union
			select 'NB' source,
			       c.policy_code,
			       c.insert_time validate_time,
			       'AM' service_code,
			       sum(c.fee_amount) fee_amount, 
			       c.pay_mode
			  from DEV_CAP.V_PREM_ARAP c,DEV_PAS.T_CONTRACT_BUSI_PROD  TCBP
			 where c.policy_code = #{policy_code,jdbcType=VARCHAR}
        		and c.VALIDATE_DATE is not null
			   and c.FEE_TYPE in ('G001070300', 'G001080300')]]>
			   	  <if test="main_pol_no != null and main_pol_no != '' "><![CDATA[AND TCBP.BUSI_ITEM_ID IN
               (SELECT P1.BUSI_ITEM_ID
                  FROM DEV_PAS.T_CONTRACT_BUSI_PROD P1,
                       (SELECT P.BUSI_ITEM_ID
                          FROM DEV_PAS.T_CONTRACT_BUSI_PROD P
                         WHERE P.BUSI_ITEM_ID = #{main_pol_no}
                            OR P.OLD_POL_NO = #{main_pol_no}) A
                 WHERE P1.BUSI_ITEM_ID = A.BUSI_ITEM_ID
                    OR P1.MASTER_BUSI_ITEM_ID = A.BUSI_ITEM_ID)]]></if>         
			 <![CDATA[group by c.policy_code, c.insert_time, c.pay_mode
   		]]>
	</select>
	
	<!-- 根据accetpid和changid查询信息 -->
	<select id="queryPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
	     select tcu.customer_name,pc.service_code, pc.endorse_code,pc.policy_code,pc.apply_time,pc.validate_time from dev_pas.t_cs_accept_change tc,dev_pas.t_cs_policy_change pc, 
         dev_pas.t_policy_holder tph,dev_pas.t_customer tcu
         where tc.accept_id = pc.accept_id
         and pc.policy_code = tph.policy_code
         and tph.customer_id = tcu.customer_id
         <if test="accept_code!=null and accept_code!=''">
             and tc.accept_code = #{accept_code}
         </if>
	</select>
	
	<!--by zhaoyoan_wb 追加保费查询 -->
	<select id="queryPayMode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[
			select distinct b.unit_number,b.pay_mode,b.validate_date 
			from DEV_CAP.V_PREM_ARAP b where b.business_code in 
			(select c.accept_code from dev_pas.t_cs_accept_change c where c.change_id in 
			(select a.change_id from dev_pas.t_cs_policy_change a 
			where a.service_code = 'AM' and 
			a.policy_code = #{policy_code,jdbcType=VARCHAR})
			) order by b.validate_date
   		]]>
	</select>
	
	<!-- 保全批单号快查服务集成接口 -->
		<select id="findAllCsPolicyChange" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		    SELECT 
		    A.RELATED_ID, A.CHANGE_FLAG, A.ENDORSE_CODE, 
		    A.PRINT_STATUS, A.PRINT_NUM ,A.APPLY_TIME, A.HESITATE_FLAG, 
		    A.FINISH_TIME, 
		    A.POLICY_LOCK_FLAG, A.ORGAN_CODE, A.VALIDATE_TIME,  
		    A.CHANGE_ID, A.FEE_AMOUNT, A.POLICY_COPY_FLAG, A.POLICY_CHG_ID, A.SERVICE_CODE, A.POLICY_ID, A.ACCEPT_ID, 
		    A.NOTIFY_TYPE, 
		    A.ORDER_ID, A.POLICY_CODE , A.PRINT_TYPE,
		    A.DELAY_CAUSE,A.REPRINT_FLAG, A.PRINT_MODE,
		    (select b.acknowledge_date from dev_pas.T_POLICY_ACKNOWLEDGEMENT b where b.policy_id=a.policy_id) acknowledge_date
		    FROM dev_pas.T_CS_POLICY_CHANGE A
		    WHERE ROWNUM <=  1000  
		]]>
		<include refid="csPolicyChangeWhereCondition" />
		<![CDATA[ ORDER BY A.POLICY_CODE ]]>
	</select>
	
	
</mapper>