<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.dao.impl.QryCommonQueryDaoImpl">





	<select id="findPremArapListInfo3" resultType="java.util.Map" parameterType="java.util.Map">
		select cash_detail_id, unit_number, policy_code, update_time, arap_date, cap_organ_code, business_code, business_type, pay_mode, busi_prod_code, busi_prod_name, fee_amount, 
		(CASE
                  WHEN t.arap_flag = 2 THEN
                   '付费'
                  WHEN t.arap_flag = 1 THEN
                   '收费'
                  else
                   ''
                end) arap_flag, fee_status, finish_time 
		from DEV_CAP.T_CASH_DETAIL t 
		where 1=1
		<if test=" UNIT_NUMBER != null and UNIT_NUMBER != ''  "> AND t.unit_number  = #{UNIT_NUMBER} </if>
		<if test=" POLICY_CODE != null and POLICY_CODE != ''  "> AND t.policy_code  = #{POLICY_CODE} </if>
		<if test=" BUSINESS_CODE != null and BUSINESS_CODE != ''  "> AND t.business_code  = #{BUSINESS_CODE} </if>
	</select>

	<select id="findPremArapListInfo1" resultType="java.util.Map" parameterType="java.util.Map">
		select cash_inter_trans_id cash_detail_id, unit_number, update_time, arap_date, cap_organ_code, business_code, business_type, pay_mode, busi_prod_code, busi_prod_name, fee_amount,
		(CASE
                  WHEN t.arap_flag = 2 THEN
                   '付费'
                  WHEN t.arap_flag = 1 THEN
                   '收费'
                  else
                   ''
                end) arap_flag, fee_status, finish_time 
		from DEV_CAP.T_CASH_INTER_TRANS t 
		where 1=1
		<if test=" UNIT_NUMBER != null and UNIT_NUMBER != ''  "> AND t.unit_number  = #{UNIT_NUMBER} </if>
		<if test=" POLICY_CODE != null and POLICY_CODE != ''  "> AND t.policy_code  = #{POLICY_CODE} </if>
		<if test=" BUSINESS_CODE != null and BUSINESS_CODE != ''  "> AND t.business_code  = #{BUSINESS_CODE} </if>
	</select>
	
	
	<select id="findPremArapListInfo2" resultType="java.util.Map" parameterType="java.util.Map">
		select max(update_time) update_time, max(arap_date) arap_date, max(cap_organ_code) cap_organ_code, max(business_code) business_code, max(business_type) business_type, max(pay_mode) pay_mode, max(busi_prod_code) busi_prod_code, max(busi_prod_name) busi_prod_name, 
		abs(SUM( CASE WHEN ARAP_FLAG ='1' THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END)) FEE_AMOUNT, 
		(CASE
                  WHEN SUM( CASE WHEN ARAP_FLAG ='1' THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END) &lt; 0 THEN
                   '付费'
                  WHEN SUM( CASE WHEN ARAP_FLAG ='1' THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END) > 0 THEN
                   '收费'
                  else
                   ''
                end) arap_flag, max(fee_status) fee_status, max(finish_time) finish_time,  
		max(bank_user_name) bank_user_name, max( payee_name) payee_name, max(actual_bank_code) bank_code, max(actual_bank_account) bank_account
		from DEV_CAP.T_CASH_INTER_TRANS t 
		where pay_mode='50'  
		<if test=" UNIT_NUMBER != null and UNIT_NUMBER != ''  "> AND t.unit_number  = #{UNIT_NUMBER} </if>
		<if test=" POLICY_CODE != null and POLICY_CODE != ''  "> AND t.policy_code  = #{POLICY_CODE} </if>
		<if test=" BUSINESS_CODE != null and BUSINESS_CODE != ''  "> AND t.business_code  = #{BUSINESS_CODE} </if>
		group by unit_number
	</select>
	
	
	<select id="findPremArapListInfo4" resultType="java.util.Map" parameterType="java.util.Map">
		select max(update_time) update_time, max(arap_date) arap_date, max(cap_organ_code) cap_organ_code, max(business_code) business_code, max(business_type) business_type, max(pay_mode) pay_mode, max(busi_prod_code) busi_prod_code, max(busi_prod_name) busi_prod_name, 
		abs(SUM( CASE WHEN ARAP_FLAG ='1' THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END)) FEE_AMOUNT, 
		(CASE
                  WHEN SUM( CASE WHEN ARAP_FLAG ='1' THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END) &lt; 0 THEN
                   '付费'
                  WHEN SUM( CASE WHEN ARAP_FLAG ='1' THEN FEE_AMOUNT ELSE  -1*FEE_AMOUNT END) > 0 THEN
                   '收费'
                  else
                   ''
                end) arap_flag, max(fee_status) fee_status, max(finish_time) finish_time,  
		max(bank_user_name) bank_user_name, max( payee_name) payee_name, max(actual_bank_code) bank_code, max(actual_bank_account) bank_account
		from DEV_CAP.V_CASH_DETAIL t where 1=1
		<if test=" UNIT_NUMBER != null and UNIT_NUMBER != ''  "> AND t.unit_number  = #{UNIT_NUMBER} </if>
		<if test=" POLICY_CODE != null and POLICY_CODE != ''  "> AND t.policy_code  = #{POLICY_CODE} </if>
		<if test=" BUSINESS_CODE != null and BUSINESS_CODE != ''  "> AND t.business_code  = #{BUSINESS_CODE} </if>
		group by unit_number
	</select>
	
  
    
    
</mapper>