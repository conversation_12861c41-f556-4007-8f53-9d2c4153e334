<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CLM_BfSurveyTasksDetailPO">
<!-- 查询理赔直连问题件选项 -->
	<select  id="findClaimDirectOptionList" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[ 
			SELECT  A.CODE,A.NAME AS PROBLEMOPTION FROM DEV_CLM.T_MEMO_ITEM A ]]>
			<if test=" directProblem  != null "><![CDATA[ WHERE A.CODE LIKE CONCAT(#{directProblem},'%') ]]></if>
	</select>
<!-- 分页查询理赔直连案件清单 -->	
	<select  id="queryBfSurveyTasksDetailForPage" parameterType="java.util.Map" resultType="java.util.Map">
	<![CDATA[ 
	SELECT M.* FROM(
         SELECT L.*,ROWNUM RN FROM(
		select
 C.POLICY_CODE POLIC_CODE,
 substr(B.ORGAN_CODE,1,4) SecondOrganCode,
 substr(B.ORGAN_CODE,1,6) ThirdOrganCode,
 B.CUSTOMER_ID CustomerId,
 (SELECT wm_concat(distinct customer.CUSTOMER_NAME) CUSTOMER_NAME FROM dev_clm.T_INSURED_LIST isnured left join dev_clm.T_CUSTOMER customer ON isnured.customer_id = customer.customer_id  WHERE isnured.policy_code= C.POLICY_CODE)  CUSTOMER_NAME,
 (SELECT wm_concat(distinct customer.CUSTOMER_CERTI_CODE) CUSTOMER_CERTI_CODE FROM dev_clm.T_INSURED_LIST isnured left join dev_clm.T_CUSTOMER customer ON isnured.customer_id = customer.customer_id  WHERE isnured.policy_code= C.POLICY_CODE) CUSTOMER_CERTI_CODE,
 (SELECT b.product_name_sys from dev_pas.T_CONTRACT_BUSI_PROD a left join dev_clm.T_Business_Product b on a.BUSI_PRD_ID = b.business_prd_id where a.POLICY_CODE = C.POLICY_CODE and rownum=1) PRODUCT_NAME_SYS,
 (select tcmaster.validate_date from dev_pas.T_Contract_Master tcmaster where tcmaster.POLICY_CODE = C.POLICY_CODE) VALIDATE_DATE,
 case G.RISK_LEVEL
      when 0 then '低风险'
      when 1 then '中风险'
      else '高风险' end RISK_LEVEL,
 (SELECT a.RISK_SCORE from dev_pas.T_CONTRACT_BUSI_PROD a where a.POLICY_CODE = C.POLICY_CODE and rownum=1) RISK_SCORE,
 (select cagent.AGENT_NAME from APP___PAS__DBUSER.T_CONTRACT_AGENT cagent where cagent.organ_code = G.AGENT_CODE) AGENT_NAME,
 G.AGENT_CODE AGENT_CODE, 
 G.ORGAN_CODE_AREA ORGAN_CODE_AREA, 
 G.ORGAN_CODE_STRY ORGAN_CODE_STRY, 
 G.ORGAN_CODE_GROUP ORGAN_CODE_GROUP, 
 (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = G.ORGAN_CODE_AREA) ORGAN_CODE_AREA_NAME,
 (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = G.ORGAN_CODE_STRY) ORGAN_CODE_STRY_NAME,
 (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = G.ORGAN_CODE_GROUP) ORGAN_CODE_GROUP_NAME,
(SELECT wm_concat(tsitem.survey_item) SURVEY_ITEM FROM dev_pas.T_SURVEY_APPLY tsapply left join dev_clm.T_SURVEY_ITEM tsitem ON tsapply.apply_id = tsitem.apply_id  WHERE tsapply.policy_code= C.POLICY_CODE) SURVEY_ITEM,
C.SURVEY_DESC SURVEY_DESC  
 from 
 APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A,
 APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK  B,
 APP___PAS__DBUSER.T_SURVEY_APPLY       C,
 APP___PAS__DBUSER.T_CONTRACT_MASTER    D,
 APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN G
 WHERE A.BATCH_ID = B.BATCH_ID
       AND B.LIST_ID = C.SURVEY_RULE_ID
       AND A.PLAN_ID = G.PLAN_ID
       AND C.POLICY_CODE = D.POLICY_CODE(+)]]>
        <if test=" plan_name  != null and plan_name !='' "><![CDATA[ AND A。PLAN_NAME like '%${PLAN_NAME}%']]></if>
       <if test=" startTime  != null and startTime !='' "><![CDATA[AND A.INSERT_TIME >= #{startTime}]]></if>
       <if test=" endTime  != null and endTime !='' "><![CDATA[AND A.INSERT_TIME <= #{endTime} ]]></if>
	   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND D.ORGAN_CODE like '${organ_code}%' ]]></if>
	<![CDATA[ ) l WHERE ROWNUM <= #{LESS_NUM}) M WHERE RN > #{GREATER_NUM}]]>
	</select>
	
	<select id="queryBfSurveyTasksDetailForPageTotal" parameterType="java.util.Map" resultType="java.lang.Integer">
		<![CDATA[SELECT COUNT(1) from(
		select
 C.POLICY_CODE POLIC_CODE,
 substr(B.ORGAN_CODE,1,4) SecondOrganCode,
 substr(B.ORGAN_CODE,1,6) ThirdOrganCode,
 B.CUSTOMER_ID CustomerId,
 (SELECT wm_concat(distinct customer.CUSTOMER_NAME) CUSTOMER_NAME FROM dev_clm.T_INSURED_LIST isnured left join dev_clm.T_CUSTOMER customer ON isnured.customer_id = customer.customer_id  WHERE isnured.policy_code= C.POLICY_CODE)  CUSTOMER_NAME,
 (SELECT wm_concat(distinct customer.CUSTOMER_CERTI_CODE) CUSTOMER_CERTI_CODE FROM dev_clm.T_INSURED_LIST isnured left join dev_clm.T_CUSTOMER customer ON isnured.customer_id = customer.customer_id  WHERE isnured.policy_code= C.POLICY_CODE) CUSTOMER_CERTI_CODE,
 (SELECT b.product_name_sys from dev_pas.T_CONTRACT_BUSI_PROD a left join dev_clm.T_Business_Product b on a.BUSI_PRD_ID = b.business_prd_id where a.POLICY_CODE = C.POLICY_CODE and rownum=1) PRODUCT_NAME_SYS,
 (select tcmaster.validate_date from dev_pas.T_Contract_Master tcmaster where tcmaster.POLICY_CODE = C.POLICY_CODE) VALIDATE_DATE,
 case G.RISK_LEVEL
      when 0 then '低风险'
      when 1 then '中风险'
      else '高风险' end RISK_LEVEL,
 (SELECT a.RISK_SCORE from dev_pas.T_CONTRACT_BUSI_PROD a where a.POLICY_CODE = C.POLICY_CODE and rownum=1) RISK_SCORE,
 (select cagent.AGENT_NAME from APP___PAS__DBUSER.T_CONTRACT_AGENT cagent where cagent.organ_code = G.AGENT_CODE) AGENT_NAME,
 G.AGENT_CODE AGENT_CODE, 
 G.ORGAN_CODE_AREA ORGAN_CODE_AREA, 
 G.ORGAN_CODE_STRY ORGAN_CODE_STRY, 
 G.ORGAN_CODE_GROUP ORGAN_CODE_GROUP, 
 (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = G.ORGAN_CODE_AREA) ORGAN_CODE_AREA_NAME,
 (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = G.ORGAN_CODE_STRY) ORGAN_CODE_STRY_NAME,
 (select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = G.ORGAN_CODE_GROUP) ORGAN_CODE_GROUP_NAME,
(SELECT wm_concat(tsitem.survey_item) SURVEY_ITEM FROM dev_pas.T_SURVEY_APPLY tsapply left join dev_clm.T_SURVEY_ITEM tsitem ON tsapply.apply_id = tsitem.apply_id  WHERE tsapply.policy_code= C.POLICY_CODE) SURVEY_ITEM,
C.SURVEY_DESC SURVEY_DESC  
 from 
 APP___PAS__DBUSER.T_CLAIM_SURVEY_BATCH A,
 APP___PAS__DBUSER.T_CLAIM_SURVEY_TASK  B,
 APP___PAS__DBUSER.T_SURVEY_APPLY       C,
 APP___PAS__DBUSER.T_CONTRACT_MASTER    D,
 APP___PAS__DBUSER.T_CLAIM_BF_SURVEY_PLAN G
 WHERE A.BATCH_ID = B.BATCH_ID
       AND B.LIST_ID = C.SURVEY_RULE_ID
       AND A.PLAN_ID = G.PLAN_ID
       AND C.POLICY_CODE = D.POLICY_CODE(+)]]>
       <if test=" plan_name  != null and plan_name !='' "><![CDATA[ AND A。PLAN_NAME like '%${PLAN_NAME}%']]></if>
       <if test=" startTime  != null and startTime !='' "><![CDATA[AND A.INSERT_TIME >= #{startTime}]]></if>
       <if test=" endTime  != null and endTime !='' "><![CDATA[AND A.INSERT_TIME <= #{endTime} ]]></if>
	   <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND D.ORGAN_CODE like '${organ_code}%' ]]></if>
		       <![CDATA[ )]]>
	</select>
	
	<!-- Execl分页查询理赔直连案件清单 -->
		<select id="queryBfSurveyTasksDetailForPageExcel" parameterType="java.util.Map" resultType="java.util.Map">
			<![CDATA[ 
	SELECT M.* FROM(
         SELECT L.*,ROWNUM RN FROM(
		SELECT X.CASE_NO ,
			   X.ORGAN_CODE,
			   X.ORGAN_NAME,
			   X.HOSPITAL_NAME,
			   Z.TREAT_TYPE,
			   Z.BILLCOUNT,
       	  	   X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       LISTAGG(AM.MEMO_OPTION,',')WITHIN GROUP(ORDER BY AM.MEMO_OPTION)MEMO_OPTION,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.FINISH_TIME FROM
		(SELECT DISTINCT A.CASE_NO,
				A.CASE_ID,A.ORGAN_CODE,B.ORGAN_NAME,C.HOSPITAL_NAME,D.DIRECT_APPLY_TIME,D.DIRECT_BACK_TIME,
		        (CASE WHEN D.CHECK_RESULT = 0 THEN '无效'
		              WHEN D.CHECK_RESULT = 1 THEN '有效' END)CHECK_RESULT,
		        (CASE WHEN D.DIRECT_APPLY_FLAG = 0 THEN '成功'
		              WHEN D.DIRECT_APPLY_FLAG = 1 THEN '失败' END)DIRECT_APPLY_FLAG,
		        D.DIRECT_FAIL_REASON,
		        (CASE WHEN G.DIRECT_CONN_BUS = 1 THEN '保医通'
                      WHEN G.DIRECT_CONN_BUS = 2 THEN '保信健康宝' END)DIRECT_CONN_BUS,
		        D.FINISH_TIME,
		        (SELECT UU.USER_NAME ||'-'|| UU.REAL_NAME FROM DEV_CLM.T_UDMP_USER UU WHERE UU.USER_ID = Y.OPERATOR_CODE)OPERATOR_CODE
		        FROM 
		         DEV_CLM.T_CLAIM_CASE A,
		         DEV_CLM.T_UDMP_ORG_REL B,
		         DEV_CLM.T_HOSPITAL C ,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_APPLY D,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_AUTH G ,
		         DEV_CLM.T_CLAIM_DIRECT_CHECK_CASE Y ]]>
		        <if test=" memo_type  != null and memo_type !='' "><![CDATA[ ,DEV_CLM.T_CLAIM_MEMO EM ]]></if>
		       
		         WHERE A.ORGAN_CODE = B.ORGAN_CODE
		               AND A.CURE_HOSPITAL = C.HOSPITAL_CODE
		        	   AND A.CASE_ID  = D.CASE_ID
		               AND G.CASE_ID = D.CASE_ID
		               AND Y.CASE_ID = D.CASE_ID
		          <if test=" memo_type  != null and memo_type !='' ">
		          		<![CDATA[ AND EM.CONN_APPLY_ID = D.LIST_ID AND EM.MEMO_TYPE = #{memo_type}]]></if>
		          <if test=" memo_option  != null and memo_option !='' ">
		          		<![CDATA[ AND EM.MEMO_OPTION = #{memo_option}]]></if>
		          <if test=" organ_code  != null and organ_code !='' ">
		          		<![CDATA[ AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		          <if test=" directConnBus  != null and directConnBus !='' ">
		          		<![CDATA[ AND G.DIRECT_CONN_BUS = #{directConnBus}]]></if>
		          <if test=" startTime  != null and startTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME) >=to_date(#{startTime},'yyyy-mm-dd')]]></if>
		          <if test=" endTime  != null and endTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME) <=to_date(#{endTime},'yyyy-mm-dd')]]></if>
	<![CDATA[    ORDER BY A.CASE_ID)X
		LEFT JOIN
		         (SELECT A.CASE_ID,
		         (CASE WHEN A.TREAT_TYPE = '0' THEN '门诊' 
		               WHEN A.TREAT_TYPE = '1' THEN '住院' END)TREAT_TYPE,
		         COUNT(BILL_NO)BILLCOUNT FROM DEV_CLM.T_CLAIM_BILL A GROUP BY A.TREAT_TYPE ,A.CASE_ID ORDER BY A.CASE_ID)Z
		ON X.CASE_ID = Z.CASE_ID      
		LEFT JOIN 
		          (SELECT DISTINCT ME.CASE_ID,MI.NAME AS MEMO_OPTION
		           FROM DEV_CLM.T_MEMO_ITEM MI ,DEV_CLM.T_CLAIM_MEMO ME
		           WHERE MI.CODE = MEMO_OPTION AND MI.CODE LIKE '12%'
		          )AM
		ON X.CASE_ID = AM.CASE_ID
		GROUP BY  X.CASE_NO ,X.ORGAN_CODE,X.ORGAN_NAME,X.HOSPITAL_NAME,Z.TREAT_TYPE,Z.BILLCOUNT,
		       X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.FINISH_TIME ]]>
	<![CDATA[ ) l WHERE ROWNUM <= #{LESS_NUM}) M WHERE RN > #{GREATER_NUM}]]>
	</select>
	<select id="queryClaimDirectCaseListForPageTotalExcel"  resultType="java.lang.Integer" parameterType="java.util.Map" >
				<![CDATA[SELECT COUNT(1) from(
					SELECT X.CASE_NO ,
			   X.ORGAN_CODE,
			   X.ORGAN_NAME,
			   X.HOSPITAL_NAME,
			   Z.TREAT_TYPE,
			   Z.BILLCOUNT,
       	  	   X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       LISTAGG(AM.MEMO_OPTION,',')WITHIN GROUP(ORDER BY AM.MEMO_OPTION)MEMO_OPTION,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.FINISH_TIME FROM
		(SELECT DISTINCT A.CASE_NO,
				A.CASE_ID,A.ORGAN_CODE,B.ORGAN_NAME,C.HOSPITAL_NAME,D.DIRECT_APPLY_TIME,D.DIRECT_BACK_TIME,
		        (CASE WHEN D.CHECK_RESULT = 0 THEN '无效'
		              WHEN D.CHECK_RESULT = 1 THEN '有效' END)CHECK_RESULT,
		        (CASE WHEN D.DIRECT_APPLY_FLAG = 0 THEN '成功'
		              WHEN D.DIRECT_APPLY_FLAG = 1 THEN '失败' END)DIRECT_APPLY_FLAG,
		        D.DIRECT_FAIL_REASON,
		        (CASE WHEN G.DIRECT_CONN_BUS = 1 THEN '保医通'
                	  WHEN G.DIRECT_CONN_BUS = 2 THEN '保信健康宝' END)DIRECT_CONN_BUS,
		        D.FINISH_TIME,
		        (SELECT UU.USER_NAME ||'-'|| UU.REAL_NAME FROM DEV_CLM.T_UDMP_USER UU WHERE UU.USER_ID = Y.OPERATOR_CODE)OPERATOR_CODE
		        FROM 
		         DEV_CLM.T_CLAIM_CASE A,
		         DEV_CLM.T_UDMP_ORG_REL B,
		         DEV_CLM.T_HOSPITAL C ,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_APPLY D,
		         DEV_CLM.T_CLAIM_DIRECT_CONN_AUTH G ,
		         DEV_CLM.T_CLAIM_DIRECT_CHECK_CASE Y ]]>
		       <if test=" memo_type  != null and memo_type !='' "><![CDATA[ ,DEV_CLM.T_CLAIM_MEMO EM ]]></if>
		       
		         WHERE A.ORGAN_CODE = B.ORGAN_CODE
		               AND A.CURE_HOSPITAL = C.HOSPITAL_CODE
		        	   AND A.CASE_ID  = D.CASE_ID
		               AND G.CASE_ID = D.CASE_ID
		               AND Y.CASE_ID = D.CASE_ID
		          <if test=" memo_type  != null and memo_type !='' ">
		          		<![CDATA[ AND EM.CONN_APPLY_ID = D.LIST_ID AND EM.MEMO_TYPE = #{memo_type}]]></if>
		          <if test=" memo_option  != null and memo_option !='' ">
		          		<![CDATA[ AND EM.MEMO_OPTION = #{memo_option}]]></if>
		          <if test=" organ_code  != null and organ_code !='' ">
		          		<![CDATA[ AND A.ORGAN_CODE LIKE CONCAT(#{organ_code},'%')]]></if>
		          <if test=" directConnBus  != null and directConnBus !='' ">
		          		<![CDATA[ AND G.DIRECT_CONN_BUS = #{directConnBus}]]></if>
		          <if test=" startTime  != null and startTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME)  >=to_date(#{startTime},'yyyy-mm-dd')]]></if>
		          <if test=" endTime  != null and endTime !='' ">
		          		<![CDATA[ AND TRUNC(D.DIRECT_APPLY_TIME)  <=to_date(#{endTime},'yyyy-mm-dd')]]></if>
	<![CDATA[    ORDER BY A.CASE_ID)X
		LEFT JOIN
		         (SELECT A.CASE_ID,
		         (CASE WHEN A.TREAT_TYPE = '0' THEN '门诊' 
		               WHEN A.TREAT_TYPE = '1' THEN '住院' END)TREAT_TYPE,
		         COUNT(BILL_NO)BILLCOUNT FROM DEV_CLM.T_CLAIM_BILL A GROUP BY A.TREAT_TYPE ,A.CASE_ID ORDER BY A.CASE_ID)Z
		ON X.CASE_ID = Z.CASE_ID      
		LEFT JOIN 
		          (SELECT DISTINCT ME.CASE_ID,MI.NAME AS MEMO_OPTION
		           FROM DEV_CLM.T_MEMO_ITEM MI ,DEV_CLM.T_CLAIM_MEMO ME
		           WHERE MI.CODE = MEMO_OPTION AND MI.CODE LIKE '12%'
		          )AM
		ON X.CASE_ID = AM.CASE_ID
		GROUP BY  X.CASE_NO ,X.ORGAN_CODE,X.ORGAN_NAME,X.HOSPITAL_NAME,Z.TREAT_TYPE,Z.BILLCOUNT,
		       X.DIRECT_APPLY_TIME,
		       X.DIRECT_BACK_TIME,
		       X.CHECK_RESULT,
		       X.DIRECT_APPLY_FLAG ,
		       X.DIRECT_FAIL_REASON,
		       X.DIRECT_CONN_BUS,
		       X.OPERATOR_CODE,
		       X.FINISH_TIME ]]>
		       <![CDATA[ )]]>
	</select>
	
	
	<!-- 分页查询风险保单清单 -->	
	<select  id="isDiseasePolicyForPage" parameterType="java.util.Map" resultType="java.util.Map">
			<![CDATA[select A.ORGAN_CODE,A.POLICY_CODE,A.LIABILITY_STATE,B.PRODUCT_DESC,C.AMOUNT,D.RISK_LEVEL_LIAB,D.RISK_FRACTION,E.AGENT_NAME,
			E.AGENT_CODE,F.IS_SURVEY,
			(select customer_name
			from dev_clm.T_CUSTOMER C,DEV_CLM.T_POLICY_HOLDER H
			where C.CUSTOMER_ID = H.CUSTOMER_ID
			and H.POLICY_CODE=A.POLICY_CODE) policyHolderName,
			(select CUSTOMER_NAME
			from dev_clm.T_CUSTOMER C,DEV_CLM.T_INSURED_LIST L
			where C.CUSTOMER_ID = L.CUSTOMER_ID
			and L.POLICY_CODE=A.POLICY_CODE ) customerName,
			(select CUSTOMER_CERTI_CODE
			from dev_clm.T_CUSTOMER C,DEV_CLM.T_INSURED_LIST L
			where C.CUSTOMER_ID = L.CUSTOMER_ID
			and L.POLICY_CODE=A.POLICY_CODE ) customerCertiCode,
			substr(I.ORGAN_CODE,1,4) SecondOrganCode,
 			substr(I.ORGAN_CODE,1,6) ThirdOrganCode,
 			K.ORGAN_CODE_AREA ORGAN_CODE_AREA, 
			K.ORGAN_CODE_STRY ORGAN_CODE_STRY, 
			K.ORGAN_CODE_GROUP ORGAN_CODE_GROUP, 
			(select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_AREA) ORGAN_CODE_AREA_NAME,
			(select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_STRY) ORGAN_CODE_STRY_NAME,
			(select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_GROUP) ORGAN_CODE_GROUP_NAME,
			K.IS_CLAIM
			from dev_clm.t_contract_master A,
			DEV_CLM.t_business_product B,
			DEV_CLM.t_contract_product C,
			DEV_CLM.T_CLAIM_RISK_LEVEL_LIAB D,
			DEV_CLM.T_CONTRACT_AGENT E,
			DEV_CLM.T_CLAIM_CASE F,
			DEV_CLM.T_CONTRACT_BUSI_PROD G,
			DEV_CLM.T_CUSTOMER H, 
			DEV_CLM.T_CLAIM_SURVEY_TASK  I,
 			DEV_CLM.T_SURVEY_APPLY J,
 			DEV_PAS.T_CLAIM_BF_SURVEY_PLAN K,
 			DEV_PAS.T_CLAIM_SURVEY_BATCH L
			where A.POLICY_CODE=C.POLICY_CODE
			and A.POLICY_CODE=D.POLICY_CODE
			and A.POLICY_CODE=E.POLICY_CODE
			and A.CASE_ID=F.CASE_ID
			and A.POLICY_CODE=G.POLICY_CODE
			and G.BUSI_PROD_CODE=B.PRODUCT_CODE_SYS
			AND I.LIST_ID = J.SURVEY_RULE_ID
       		AND J.CASE_ID = A.CASE_ID
       		AND I.BATCH_ID = L.BATCH_ID
       		AND L.PLAN_ID = K.PLAN_ID
			]]>
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
			<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND H.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
			<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND H.CUSTOMER_ID = #{customer_id} ]]></if>
			<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND E.AGENT_CODE = #{agent_code} ]]></if>
			<if test=" startTime != null and startTime != ''  "><![CDATA[ AND A.VALIDDATE_DATE >= #{startTime} ]]></if>
			<if test=" endTime != null and endTime != ''  "><![CDATA[ AND A.VALIDDATE_DATE <= #{endTime} ]]></if>
		</select>
		
		
		<select id="isDiseasePolicyForPageTotal" parameterType="java.util.Map" resultType="java.util.Map">
		<![CDATA[SELECT COUNT(1) from(select A.ORGAN_CODE,
			A.POLICY_CODE,
			A.LIABILITY_STATE,
			B.PRODUCT_DESC,
			C.AMOUNT,
			D.RISK_LEVEL_LIAB,
			D.RISK_FRACTION,
			E.AGENT_NAME,
			E.AGENT_CODE,F.IS_SURVEY,
			(select customer_name
			from dev_clm.T_CUSTOMER C,DEV_CLM.T_POLICY_HOLDER H
			where C.CUSTOMER_ID = H.CUSTOMER_ID
			and H.POLICY_CODE=A.POLICY_CODE) policyHolderName,
			(select CUSTOMER_NAME
			from dev_clm.T_CUSTOMER C,DEV_CLM.T_INSURED_LIST L
			where C.CUSTOMER_ID = L.CUSTOMER_ID
			and L.POLICY_CODE=A.POLICY_CODE ) customerName,
			(select CUSTOMER_CERTI_CODE
			from dev_clm.T_CUSTOMER C,DEV_CLM.T_INSURED_LIST L
			where C.CUSTOMER_ID = L.CUSTOMER_ID
			and L.POLICY_CODE=A.POLICY_CODE ) customerCertiCode,
			substr(I.ORGAN_CODE,1,4) SecondOrganCode,
 			substr(I.ORGAN_CODE,1,6) ThirdOrganCode,
 			K.ORGAN_CODE_AREA ORGAN_CODE_AREA, 
			K.ORGAN_CODE_STRY ORGAN_CODE_STRY, 
			K.ORGAN_CODE_GROUP ORGAN_CODE_GROUP, 
			(select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_AREA) ORGAN_CODE_AREA_NAME,
			(select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_STRY) ORGAN_CODE_STRY_NAME,
			(select a.SALES_ORGAN_NAME from dev_pas.T_SALES_ORGAN a where a.SALES_ORGAN_CODE = K.ORGAN_CODE_GROUP) ORGAN_CODE_GROUP_NAME,
			K.IS_CLAIM
			from dev_clm.t_contract_master A,
			DEV_CLM.t_business_product B,
			DEV_CLM.t_contract_product C,
			DEV_CLM.T_CLAIM_RISK_LEVEL_LIAB D,
			DEV_CLM.T_CONTRACT_AGENT E,
			DEV_CLM.T_CLAIM_CASE F,
			DEV_CLM.T_CONTRACT_BUSI_PROD G,
			DEV_CLM.T_CUSTOMER H, 
			DEV_CLM.T_CLAIM_SURVEY_TASK  I,
 			DEV_CLM.T_SURVEY_APPLY J,
 			DEV_PAS.T_CLAIM_BF_SURVEY_PLAN K,
 			DEV_PAS.T_CLAIM_SURVEY_BATCH L
			where A.POLICY_CODE=C.POLICY_CODE
			and A.POLICY_CODE=D.POLICY_CODE
			and A.POLICY_CODE=E.POLICY_CODE
			and A.CASE_ID=F.CASE_ID
			and A.POLICY_CODE=G.POLICY_CODE
			and G.BUSI_PROD_CODE=B.PRODUCT_CODE_SYS
			AND I.LIST_ID = J.SURVEY_RULE_ID
       		AND J.CASE_ID = A.CASE_ID
       		AND I.BATCH_ID = L.BATCH_ID
       		AND L.PLAN_ID = K.PLAN_ID)]]>
			<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
			<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
			<if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND H.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
			<if test=" customer_id != null and customer_id != ''  "><![CDATA[ AND H.CUSTOMER_ID = #{customer_id} ]]></if>
			<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND E.AGENT_CODE = #{agent_code} ]]></if>
			<if test=" startTime != null and startTime != ''  "><![CDATA[ AND A.VALIDDATE_DATE >= #{startTime} ]]></if>
			<if test=" endTime != null and endTime != ''  "><![CDATA[ AND A.VALIDDATE_DATE <= #{endTime} ]]></if>
	</select>
	
	
</mapper>