<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="customer">

    <sql id="PA_customerWhereCondition">
        <!-- <if test=" customer_name != null and customer_name != '' "><![CDATA[ 
            AND A.CUSTOMER_NAME like '%'||#{customer_name}||'%' ]]></if> -->
        <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
        <if test=" customer_level != null and customer_level != ''  "><![CDATA[ AND A.CUSTOMER_LEVEL = #{customer_level} ]]></if>
        <if test=" nation_code != null and nation_code != ''  "><![CDATA[ AND <PERSON>.NATION_CODE = #{nation_code} ]]></if>
        <if test=" death_date  != null  and  death_date  != ''  "><![CDATA[ AND A.DEATH_DATE = #{death_date} ]]></if>
        <if test=" job_nature != null and job_nature != ''  "><![CDATA[ AND A.JOB_NATURE = #{job_nature} ]]></if>
        <if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
        <if test=" cust_pwd != null and cust_pwd != ''  "><![CDATA[ AND A.CUST_PWD = #{cust_pwd} ]]></if>
        <if test=" wechat_no != null and wechat_no != ''  "><![CDATA[ AND A.WECHAT_NO = #{wechat_no} ]]></if>
        <if test=" offen_use_tel != null and offen_use_tel != ''  "><![CDATA[ AND A.OFFEN_USE_TEL = #{offen_use_tel} ]]></if>
        <if test=" un_customer_code != null and un_customer_code != ''  "><![CDATA[ AND A.UN_CUSTOMER_CODE = #{un_customer_code} ]]></if>
        <if test=" cust_cert_end_date  != null  and  cust_cert_end_date  != ''  "><![CDATA[ AND A.CUST_CERT_END_DATE = #{cust_cert_end_date} ]]></if>
        <if test=" qq != null and qq != ''  "><![CDATA[ AND A.QQ = #{qq} ]]></if>
        <if test=" OLD_CUSTOMER_ID != null and OLD_CUSTOMER_ID != ''  "><![CDATA[ AND A.OLD_CUSTOMER_ID = #{OLD_CUSTOMER_ID} ]]></if>
        <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
        <if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND to_char(A.CUSTOMER_BIRTHDAY,'yyyy-MM-dd') = to_char(#{customer_birthday},'yyyy-MM-dd') ]]></if>
        <if test=" is_parent  != null "><![CDATA[ AND A.IS_PARENT = #{is_parent} ]]></if>
        <if test=" country_code != null and country_code != ''  "><![CDATA[ AND A.COUNTRY_CODE = #{country_code} ]]></if>
        <if test=" fax_tel != null and fax_tel != ''  "><![CDATA[ AND A.FAX_TEL = #{fax_tel} ]]></if>
        <if test=" job_code != null and job_code != ''  "><![CDATA[ AND A.JOB_CODE = #{job_code} ]]></if>
        <if test=" office_tel != null and office_tel != ''  "><![CDATA[ AND A.OFFICE_TEL = #{office_tel} ]]></if>
        <if test=" smoking_flag  != null "><![CDATA[ AND A.SMOKING_FLAG = #{smoking_flag} ]]></if>
        <if test=" marriage_status != null and marriage_status != ''  "><![CDATA[ AND A.MARRIAGE_STATUS = #{marriage_status} ]]></if>
        <if test=" education != null and education != ''  "><![CDATA[ AND A.EDUCATION = #{education} ]]></if>
        <if test=" customer_id_code != null and customer_id_code != ''  "><![CDATA[ AND A.CUSTOMER_ID_CODE = #{customer_id_code} ]]></if>
        <if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
        <if test=" other != null and other != ''  "><![CDATA[ AND A.OTHER = #{other} ]]></if>
        <if test=" company_name != null and company_name != ''  "><![CDATA[ AND A.COMPANY_NAME = #{company_name} ]]></if>
        <if test=" job_title != null and job_title != ''  "><![CDATA[ AND A.JOB_TITLE = #{job_title} ]]></if>
        <if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
        <if test=" customer_height  != null "><![CDATA[ AND A.CUSTOMER_HEIGHT = #{customer_height} ]]></if>
        <if test=" health_status != null and health_status != ''  "><![CDATA[ AND A.HEALTH_STATUS = #{health_status} ]]></if>
        <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
        <if test=" retired_flag  != null "><![CDATA[ AND A.RETIRED_FLAG = #{retired_flag} ]]></if>
        <if test=" drunk_flag  != null "><![CDATA[ AND A.DRUNK_FLAG = #{drunk_flag} ]]></if>
        <if test=" marriage_date  != null  and  marriage_date  != ''  "><![CDATA[ AND A.MARRIAGE_DATE = #{marriage_date} ]]></if>
        <if test=" blacklist_flag  != null "><![CDATA[ AND A.BLACKLIST_FLAG = #{blacklist_flag} ]]></if>
        <if test=" driver_license_type != null and driver_license_type != ''  "><![CDATA[ AND A.DRIVER_LICENSE_TYPE = #{driver_license_type} ]]></if>
        <if test=" housekeeper_flag  != null "><![CDATA[ AND A.HOUSEKEEPER_FLAG = #{housekeeper_flag} ]]></if>
        <if test=" email != null and email != ''  "><![CDATA[ AND A.EMAIL = #{email} ]]></if>
        <if test=" annual_income  != null "><![CDATA[ AND A.ANNUAL_INCOME = #{annual_income} ]]></if>
        <if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND trim(A.CUSTOMER_CERT_TYPE) = #{customer_cert_type} ]]></if>
        <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
        <if test=" job_kind != null and job_kind != ''  "><![CDATA[ AND A.JOB_KIND = #{job_kind} ]]></if>
        <if test=" customer_weight  != null "><![CDATA[ AND A.CUSTOMER_WEIGHT = #{customer_weight} ]]></if>
        <if test=" religion_code  != null "><![CDATA[ AND A.RELIGION_CODE = #{religion_code} ]]></if>
        <if test=" cust_cert_star_date  != null  and  cust_cert_star_date  != ''  "><![CDATA[ AND A.CUST_CERT_STAR_DATE = #{cust_cert_star_date} ]]></if>
        <if test=" syn_mdm_flag  != null "><![CDATA[ AND A.SYN_MDM_FLAG = #{syn_mdm_flag} ]]></if>
        <if test=" customer_vip  != null "><![CDATA[ AND A.CUSTOMER_VIP = #{customer_vip} ]]></if>
        <if test=" live_status  != null "><![CDATA[ AND A.LIVE_STATUS = #{live_status} ]]></if>
    </sql>
    <sql id="PA_customerInjuredWhereCondition">
        <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
        <if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
        <if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
        <if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
        <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND (A.CUSTOMER_CERTI_CODE = #{customer_certi_code} OR A.SECOND_CERTI_CODE = #{customer_certi_code }) ]]></if>
        <if test=" customer_id != null and customer_id != ''  ">  
          <![CDATA[ AND    ]]>
           <if test=" old_customer_id != null and old_customer_id != ''  "> <![CDATA[ ( ]]></if>
           <![CDATA[  A.CUSTOMER_ID = #{customer_id}]]>
           <if test=" old_customer_id != null and old_customer_id != ''  "> <![CDATA[  OR  A.OLD_CUSTOMER_ID = #{old_customer_id} ]]> </if> 
           <if test=" old_customer_id != null and old_customer_id != ''  "> <![CDATA[ ) ]]></if>
         </if>
        <if test=" mobile_tel != null and mobile_tel != ''  ">
        <![CDATA[ AND  A.CUSTOMER_ID IN
	               (
	               SELECT DISTINCT PACB.customer_id FROM DEV_PAS.T_CONTRACT_BENE PACB INNER JOIN DEV_PAS.T_ADDRESS AD ON pacb.address_id = ad.address_id and ad.mobile_tel =#{mobile_tel} AND AD.ADDRESS_STATUS = 1
                     UNION
                  SELECT DISTINCT PAPH.customer_id FROM DEV_PAS.T_POLICY_HOLDER PAPH INNER JOIN DEV_PAS.T_ADDRESS AD ON paph.address_id = ad.address_id and ad.mobile_tel = #{mobile_tel} AND AD.ADDRESS_STATUS = 1
                     UNION
                  SELECT DISTINCT PAIL.customer_id FROM DEV_PAS.T_INSURED_LIST PAIL INNER JOIN DEV_PAS.T_ADDRESS AD ON PAIL.address_id = ad.address_id and ad.mobile_tel = #{mobile_tel} AND AD.ADDRESS_STATUS = 1
                     UNION
                 SELECT DISTINCT NBCB.Customer_Id FROM DEV_NB.T_NB_CONTRACT_BENE NBCB 
                 INNER JOIN (
                 	select * from DEV_PAS.T_ADDRESS where mobile_tel = #{mobile_tel}
                 ) NBAD 
                 ON nbcb.address_id= nbad.address_id 
                 LEFT JOIN dev_pas.t_contract_master cm on NBCB.apply_code=cm.apply_code 
                 where nbad.address_status = 1 and cm.policy_code is null   
                 UNION
                 SELECT DISTINCT NBPH.Customer_Id FROM DEV_NB.T_NB_POLICY_HOLDER NBPH 
                 INNER JOIN  (select * from DEV_PAS.T_ADDRESS where mobile_tel = #{mobile_tel}) NBAD 
                 ON NBPH.address_id= nbad.address_id
                  LEFT JOIN dev_pas.t_contract_master cm on NBPH.apply_code=cm.apply_code 
                  where  nbad.address_status = 1 and cm.policy_code is null   
                 UNION 
                 SELECT DISTINCT NBIL.Customer_Id from DEV_NB.T_NB_INSURED_LIST NBIL 
                 INNER JOIN (select * from DEV_PAS.T_ADDRESS where mobile_tel = #{mobile_tel}) NBAD 
                  ON NBIL.address_id= nbad.address_id 
                  LEFT JOIN dev_pas.t_contract_master cm on NBIL.apply_code=cm.apply_code 
                  where  nbad.address_status = 1 and cm.policy_code is null   )/*#104_15157将t_address修改为查询保单库表*/
				]]></if>
         <if test=" bank_account != null and bank_account != ''  "><![CDATA[
         AND A.CUSTOMER_ID in(SELECT B.CUSTOMER_ID FROM DEV_PAS.T_BANK_ACCOUNT B WHERE B.ACCOUNT_STATUS=1
         AND B.BANK_ACCOUNT = #{bank_account})
         ]]></if>
    </sql>
    <sql id="PA_customerInjuredWhereCondition1">
        <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND A.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
        <if test=" customer_birthday  != null  and  customer_birthday  != ''  "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
        <if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
        <if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
        <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND (A.CUSTOMER_CERTI_CODE = #{customer_certi_code} OR A.SECOND_CERTI_CODE = #{customer_certi_code }) ]]></if>
        <if test=" customer_id != null and customer_id != ''  ">  
          <![CDATA[ AND    ]]>
           <if test=" old_customer_id != null and old_customer_id != ''  "> <![CDATA[ ( ]]></if>
           <![CDATA[  A.CUSTOMER_ID = #{customer_id}]]>
           <if test=" old_customer_id != null and old_customer_id != ''  "> <![CDATA[  OR  A.OLD_CUSTOMER_ID = #{old_customer_id} ]]> </if> 
           <if test=" old_customer_id != null and old_customer_id != ''  "> <![CDATA[ ) ]]></if>
         </if>
        <if test=" mobile_tel != null and mobile_tel != ''  ">
        <![CDATA[ AND  A.CUSTOMER_ID IN
	               (
	               SELECT DISTINCT PACB.customer_id FROM DEV_PAS.T_CONTRACT_BENE PACB INNER JOIN DEV_PAS.T_ADDRESS AD ON pacb.address_id = ad.address_id and ad.mobile_tel =#{mobile_tel} AND AD.ADDRESS_STATUS = 1
                     UNION
                  SELECT DISTINCT PAPH.customer_id FROM DEV_PAS.T_POLICY_HOLDER PAPH INNER JOIN DEV_PAS.T_ADDRESS AD ON paph.address_id = ad.address_id and ad.mobile_tel = #{mobile_tel} AND AD.ADDRESS_STATUS = 1
                     UNION
                  SELECT DISTINCT PAIL.customer_id FROM DEV_PAS.T_INSURED_LIST PAIL INNER JOIN DEV_PAS.T_ADDRESS AD ON PAIL.address_id = ad.address_id and ad.mobile_tel = #{mobile_tel} AND AD.ADDRESS_STATUS = 1
                     UNION
                 SELECT DISTINCT NBCB.Customer_Id FROM DEV_NB.T_NB_CONTRACT_BENE NBCB 
                 INNER JOIN (
                 	select * from DEV_NB.T_ADDRESS where mobile_tel = #{mobile_tel}
                 ) NBAD 
                 ON nbcb.address_id= nbad.address_id 
                 LEFT JOIN DEV_NB.t_NB_contract_master cm on NBCB.apply_code=cm.apply_code 
                 where nbad.address_status = 1 and cm.policy_code is null   
                 UNION
                 SELECT DISTINCT NBPH.Customer_Id FROM DEV_NB.T_NB_POLICY_HOLDER NBPH 
                 INNER JOIN  (select * from DEV_NB.T_ADDRESS where mobile_tel = #{mobile_tel}) NBAD 
                 ON NBPH.address_id= nbad.address_id
                  LEFT JOIN DEV_NB.t_NB_contract_master cm on NBPH.apply_code=cm.apply_code 
                  where  nbad.address_status = 1 and cm.policy_code is null   
                 UNION 
                 SELECT DISTINCT NBIL.Customer_Id from DEV_NB.T_NB_INSURED_LIST NBIL 
                 INNER JOIN (select * from DEV_NB.T_ADDRESS where mobile_tel = #{mobile_tel}) NBAD 
                  ON NBIL.address_id= nbad.address_id 
                  LEFT JOIN DEV_NB.t_NB_contract_master cm on NBIL.apply_code=cm.apply_code 
                  where  nbad.address_status = 1 and cm.policy_code is null   )/*#104_15157将t_address修改为查询保单库表*/
				]]></if>
         <if test=" bank_account != null and bank_account != ''  "><![CDATA[
         AND A.CUSTOMER_ID in(SELECT B.CUSTOMER_ID FROM DEV_PAS.T_BANK_ACCOUNT B WHERE B.ACCOUNT_STATUS=1
         AND B.BANK_ACCOUNT = #{bank_account})
         ]]></if>
    </sql>
    <!-- 投保人 保单号查询条件 -->
    <sql id="PA_customerHolderCondition">
        <if test="policy_code != null and policy_code !=''"> <![CDATA[ AND TL.POLICY_CODE = #{policy_code}]]></if>
    </sql>
    <!-- 被保人 保单号查询条件 -->
    <sql id="PA_customerInsuredCondition">
        <if test="policy_code != null and policy_code !=''"> <![CDATA[ AND TP.POLICY_CODE = #{policy_code}]]></if>
    </sql>
    <!-- 客户的查询条件 -->
    <sql id="PA_customerBaseCodition">
        <if test=" customer_name != null and customer_name != ''  "><![CDATA[ AND T.CUSTOMER_NAME like '%${customer_name}%' ]]></if>
        <if test=" customer_gender  != null "><![CDATA[ AND T.CUSTOMER_GENDER = #{customer_gender} ]]></if>
        <if test=" customer_cert_type != null and customer_cert_type != ''  "><![CDATA[ AND T.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
        <if test=" customer_certi_code != null and customer_certi_code != ''  "><![CDATA[ AND T.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
        <if test=" customer_vip  != null and customer_vip != '' "><![CDATA[ AND T.CUSTOMER_VIP = #{customer_vip} ]]></if>
    </sql>



    <!-- 按索引生成的查询条件 -->
    <sql id="PA_queryCustomerByCustomerIdCodeCondition">
        <if test=" customer_id_code != null and customer_id_code != '' "><![CDATA[ AND A.CUSTOMER_ID_CODE = #{customer_id_code} ]]></if>
    </sql>
    <sql id="PA_queryCustomerByCustomerNameCondition">
        <if test=" customer_name != null and customer_name != '' "><![CDATA[ AND A.CUSTOMER_NAME = #{customer_name} ]]></if>
    </sql>
    <sql id="PA_queryCustomerByCustomerBirthdayCondition">
        <if test=" customer_birthday  != null "><![CDATA[ AND A.CUSTOMER_BIRTHDAY = #{customer_birthday} ]]></if>
    </sql>
    <sql id="PA_queryCustomerByCustomerGenderCondition">
        <if test=" customer_gender  != null "><![CDATA[ AND A.CUSTOMER_GENDER = #{customer_gender} ]]></if>
    </sql>
    <sql id="PA_queryCustomerByCustomerCertTypeCondition">
        <if test=" customer_cert_type != null and customer_cert_type != '' "><![CDATA[ AND A.CUSTOMER_CERT_TYPE = #{customer_cert_type} ]]></if>
    </sql>
    <sql id="PA_queryCustomerByCustomerCertiCodeCondition">
        <if test=" customer_certi_code != null and customer_certi_code != '' "><![CDATA[ AND A.CUSTOMER_CERTI_CODE = #{customer_certi_code} ]]></if>
    </sql>
    <sql id="PA_queryCustomerByCustomerIdCondition">
        <if test=" his_list_id!=null">
           AND A.list_id=#{his_list_id}
        </if>
        <if test="his_list_id==null">
           AND A.CUSTOMER_ID = #{customer_id}
        </if>
    </sql>

    <!-- 按索引查询操作 -->
    <select id="PA_findCustomerByCustomerIdCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.TAX_RESIDENT_TYPE,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
        <include refid="PA_queryCustomerByCustomerIdCodeCondition" />
    </select>

    <select id="PA_findCustomerByCustomerName" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
        <include refid="PA_queryCustomerByCustomerNameCondition" />
    </select>

    <select id="PA_findCustomerByCustomerBirthday" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
        <include refid="PA_queryCustomerByCustomerBirthdayCondition" />
    </select>

    <select id="PA_findCustomerByCustomerGender" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
        <include refid="PA_queryCustomerByCustomerGenderCondition" />
    </select>

    <select id="PA_findCustomerByCustomerCertType" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
        <include refid="PA_queryCustomerByCustomerCertTypeCondition" />
    </select>

    <select id="PA_findCustomerByCustomerCertiCode" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
        <include refid="PA_queryCustomerByCustomerCertiCodeCondition" />
    </select>

    <select id="QRY_INTEGRAL_findCustomerByCustomerId" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1 and  ROWNUM <=  1000]]>
        <include refid="PA_queryCustomerByCustomerIdCondition" />
    </select>

    <select id="PA_findCustomerByCustomerId" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1 and  ROWNUM <=  1000]]>
        <include refid="PA_queryCustomerByCustomerIdCondition" />
    </select>

    <!-- 按map查询操作 -->
    <select id="PA_findAllMapCustomer" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
        <!-- <include refid="请添加查询条件" /> -->
    </select>

    <!-- 查询所有操作 -->
    <select id="QRY_INTEGRAL_findAllCustomer" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
        <include refid="PA_customerWhereCondition" />
    </select>

    <!-- 查询个数操作 -->
    <select id="PA_findCustomerTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CUSTOMER A  LEFT JOIN DEV_PAS.T_CUSTOMER_TAX CT
                ON CT.CUSTOMER_TAX_NAME = A.CUSTOMER_NAME
               AND CT.CUSTOMER_TAX_GENDER = A.CUSTOMER_GENDER
               AND CT.CUSTOMER_TAX_BIRTHDAY = A.CUSTOMER_BIRTHDAY
               AND CT.CUSTOMER_TAX_CERT_TYPE = A.CUSTOMER_CERT_TYPE
               AND CT.CUSTOMER_TAX_CERTI_CODE = A.CUSTOMER_CERTI_CODE WHERE 1 = 1 ]]>
        <include refid="PA_customerInjuredWhereCondition1" />
    </select>

    <!-- 多表级联 分页查询个数操作 -->
    <select id="PA_findCustomerByConditionTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CUSTOMER T WHERE T.CUSTOMER_ID IN (SELECT TL.CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST TL WHERE 1=1 ]]>
        <include refid="PA_customerHolderCondition" />
         <![CDATA[
             UNION 
            SELECT TP.CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER TP WHERE 1=1
         ]]>
        <include refid="PA_customerInsuredCondition" />
         <![CDATA[
            )
         ]]>
        <include refid="PA_customerBaseCodition" />
    </select>

    <select id="PA_queryCustomerForAccountForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
    <![CDATA[ select B.RN AS rowNumber,
       b.CUSTOMER_ID,
       b.CUSTOMER_NAME,
       b.CUSTOMER_GENDER,
       b.CUSTOMER_BIRTHDAY,
       b.CUSTOMER_CERT_TYPE,
       b.CUSTOMER_CERTI_CODE
  from (SELECT ROWNUM RN,
               CU.CUSTOMER_ID,
               CU.CUSTOMER_NAME,
               CU.CUSTOMER_GENDER,
               CU.CUSTOMER_BIRTHDAY,
               CU.CUSTOMER_CERT_TYPE,
               CU.CUSTOMER_CERTI_CODE
          FROM dev_pas.T_CUSTOMER CU
         INNER JOIN dev_pas.T_BANK_ACCOUNT BA
            ON CU.CUSTOMER_ID = BA.CUSTOMER_ID
         WHERE ROWNUM <= #{LESS_NUM}
           AND BA.ACCOUNT_TYPE ='${account_type}'
           AND BA.BANK_ACCOUNT = '${account_id}') B
 WHERE B.RN > #{GREATER_NUM}]]>
    </select>

    <select id="PA_queryCustomerForAccountTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT count(*)
          FROM dev_pas.T_CUSTOMER CU
         INNER JOIN dev_pas.T_BANK_ACCOUNT BA
            ON CU.CUSTOMER_ID = BA.CUSTOMER_ID
         WHERE 1 = 1
           AND BA.ACCOUNT_TYPE ='${account_type}'
           AND BA.BANK_ACCOUNT = '${account_id}'
        ]]>
    </select>

    <!-- 分页查询操作 10980新增收入来源字段查询 客户层信息查询-->
    <select id="PA_queryCustomerForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
       <![CDATA[ SELECT B.RN AS rowNumber, B.INS_SPE_PEOPLE,B.RURAL_POPULATION_FLAG,B.DISABILITY_FLAG,B.DISABILITY_NO,B.CUSTOMER_NAME, B.CUSTOMER_RISK_LEVEL, B.INCOME_SOURCE, B.CUSTOMER_LEVEL, B.NATION_CODE, B.DEATH_DATE, B.JOB_NATURE, B.REMARK, B.CUST_PWD, 
            B.WECHAT_NO, B.OFFEN_USE_TEL, B.UN_CUSTOMER_CODE, B.CUST_CERT_END_DATE, B.QQ, B.OLD_CUSTOMER_ID, 
            B.MOBILE_TEL, B.CUSTOMER_BIRTHDAY, B.IS_PARENT,  (case when trim(B.COUNTRY_CODE)='H' then 'XXX'
             else B.COUNTRY_CODE
            end) AS COUNTRY_CODE, B.FAX_TEL, B.JOB_CODE, B.SH_JOB_CODE,
            B.OFFICE_TEL, B.SMOKING_FLAG, B.MARRIAGE_STATUS, B.EDUCATION, B.CUSTOMER_ID_CODE, 
            B.CUSTOMER_GENDER, B.OTHER, B.COMPANY_NAME, B.JOB_TITLE, B.CUSTOMER_ID, B.CUSTOMER_HEIGHT, B.HEALTH_STATUS, 
            B.CUSTOMER_CERTI_CODE, B.RETIRED_FLAG, B.DRUNK_FLAG, B.MARRIAGE_DATE, B.DRIVER_LICENSE_TYPE, 
            B.HOUSEKEEPER_FLAG, B.EMAIL, B.ANNUAL_INCOME, B.CUSTOMER_CERT_TYPE, B.HOUSE_TEL, B.JOB_KIND, 
            B.CUSTOMER_WEIGHT, B.RELIGION_CODE, B.CUST_CERT_STAR_DATE, B.SYN_MDM_FLAG, B.CUSTOMER_VIP, B.LIVE_STATUS,B.TAX_RESIDENT_TYPE,
            B.SECOND_CERT_TYPE,B.SECOND_CERTI_CODE,B.BLACKLIST_FLAG,B.JOB_UNDERWRITE,B.ANNUAL_INCOME_DESC,B.SOCI_SECU FROM (
                    SELECT ROWNUM RN, A.CUSTOMER_NAME, A.CUSTOMER_RISK_LEVEL, A.INCOME_SOURCE, A.INS_SPE_PEOPLE,A.RURAL_POPULATION_FLAG,A.DISABILITY_FLAG,A.DISABILITY_NO,
					A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE,
            (SELECT JCM.JOB_CODE || '-' || SHJC.JOB_NAME FROM DEV_PAS.T_SH_JOB_MAPPING JCM LEFT JOIN DEV_PAS.T_JOB_CODE SHJC
            ON  JCM.JOB_CODE = SHJC.JOB_CODE WHERE SHJC.JOB_CODE =A.JOB_CODE AND JCM.RETURN_MARK='1' ) SH_JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,
            A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE,NVL((SELECT blc.valid
										             FROM DEV_PAS.T_BLACKLIST_CUSTOMER blc
										            WHERE blc.CUSTOMER_ID = A.CUSTOMER_ID),
										           0) AS BLACKLIST_FLAG,/*黑名单客户标志*/
										           (CASE
										         WHEN trim(A.JOB_KIND) = '1' THEN
										          '一级'
										         WHEN trim(A.JOB_KIND) = '2' THEN
										          '二级'
										         WHEN trim(A.JOB_KIND) = '3' THEN
										          '三级'
										         WHEN trim(A.JOB_KIND) = '4' THEN
										          '四级'
										         WHEN trim(A.JOB_KIND) = '5' THEN
										          '五级'
										         WHEN trim(A.JOB_KIND) = '6' THEN
										          '六级'
										         WHEN trim(A.JOB_KIND) = 'z' THEN
										          '拒保职业'
										       END) AS JOB_UNDERWRITE,
                A.ANNUAL_INCOME AS ANNUAL_INCOME_DESC,
                (select  CT.TAX_RESIDENT_TYPE from 
                DEV_PAS.T_CUSTOMER_TAX CT
                where CT.CUSTOMER_TAX_NAME = A.CUSTOMER_NAME
               AND CT.CUSTOMER_TAX_GENDER = A.CUSTOMER_GENDER
               AND CT.CUSTOMER_TAX_BIRTHDAY = A.CUSTOMER_BIRTHDAY
               AND CT.CUSTOMER_TAX_CERT_TYPE = A.CUSTOMER_CERT_TYPE
               AND CT.CUSTOMER_TAX_CERTI_CODE = A.CUSTOMER_CERTI_CODE
               and ROWNUM=1)TAX_RESIDENT_TYPE,
			   A.SOCI_SECU
               FROM DEV_PAS.T_CUSTOMER A 
              WHERE ROWNUM <= #{LESS_NUM} ]]>
        <include refid="PA_customerInjuredWhereCondition1" />
        <![CDATA[ )  B
            WHERE B.RN > #{GREATER_NUM} ORDER BY  B.CUSTOMER_BIRTHDAY DESC]]>
    </select>
    <!-- 查询单条  10980新增收入来源字段查询 保单层客户信息查询-->
    <select id="PA_findCustomer" resultType="java.util.Map"
        parameterType="java.util.Map">  
        <![CDATA[ 
           SELECT A.CUSTOMER_NAME, A.INCOME_SOURCE, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,           
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, (case when trim(A.COUNTRY_CODE)='H' then 'XXX'
            else A.COUNTRY_CODE
            end) AS COUNTRY_CODE,  A.FAX_TEL,]]>
            <if test=" customer_role == 'policyHolder'">
				          	 CI.APPLICANT_SPE_PEOPLE,
				          	 NCT.BUS_SRE_DEPT_CODE,
						     NCT.INSURED_TYPE,
						     NCT.TAX_DISTRICT,
						     NCT.TAX_NUMBER,
						     NCT.RETIREMENT_AGE,
			</if>
			<if test=" customer_role == 'insured'">
							  CI.INS_SPE_PEOPLE,CI.RURAL_POPULATION_FLAG,CI.DISABILITY_FLAG,CI.DISABILITY_NO,
			</if>
			<if test=" (customer_role == null or customer_role == '')"> 
            	 		A.INS_SPE_PEOPLE,
            </if>
			
            <if test="(customer_role != null and customer_role != '') and (policy_code != null and policy_code != '') and  customer_role == 'benefit' "> 
            	<![CDATA[ (SELECT JCM.SH_JOB_CODE || ' - ' || TJC.JOB_NAME FROM DEV_PAS.T_SH_JOB_MAPPING JCM,DEV_PAS.T_JOB_CODE TJC
            		WHERE JCM.JOB_CODE = TJC.JOB_CODE AND TJC.JOB_CODE = A.JOB_CODE AND JCM.RETURN_MARK='1') SH_JOB_CODE, ]]>
            </if>
            <if test=" (customer_role != null and customer_role != '') and (policy_code != null and policy_code != '') and customer_role != 'benefit' ">
            	<![CDATA[ (SELECT JCM.SH_JOB_CODE || ' - ' || TJC.JOB_NAME FROM DEV_PAS.T_SH_JOB_MAPPING JCM,DEV_PAS.T_JOB_CODE TJC
            		WHERE JCM.JOB_CODE = TJC.JOB_CODE AND TJC.JOB_CODE = CI.JOB_CODE AND JCM.RETURN_MARK='1') SH_JOB_CODE, ]]>
            </if>
            <if test=" (customer_role == null or customer_role == '') or (policy_code == null or policy_code == '') "> 
            	<![CDATA[ (SELECT JCM.SH_JOB_CODE || ' - ' || TJC.JOB_NAME FROM DEV_PAS.T_SH_JOB_MAPPING JCM,DEV_PAS.T_JOB_CODE TJC
            		WHERE JCM.JOB_CODE = TJC.JOB_CODE AND TJC.JOB_CODE = A.JOB_CODE AND JCM.RETURN_MARK='1') SH_JOB_CODE, ]]>
            </if>
            <if test="(customer_role != null and customer_role != '') and (policy_code != null and policy_code != '') and  customer_role == 'benefit' "> 
            	<![CDATA[ A.SMOKING_FLAG, A.JOB_CODE, A.CUSTOMER_HEIGHT, A.CUSTOMER_WEIGHT, A.COMPANY_NAME,]]>
            </if>
            <if test=" (customer_role != null and customer_role != '') and (policy_code != null and policy_code != '') and customer_role != 'benefit' ">
            	<![CDATA[ 
            		CI.SMOKING AS SMOKING_FLAG, A.JOB_CODE AS JOB_CODE,
            	    CI.CUSTOMER_HEIGHT AS CUSTOMER_HEIGHT, CI.CUSTOMER_WEIGHT AS CUSTOMER_WEIGHT,
            	    A.COMPANY_NAME,
            	    CI.ANNUAL_INCOME_CEIL AS ANNUAL_INCOME_CEIL1,
					CI.INCOME_SOURCE AS INCOME_SOURCE1,
					CI.RESIDENT_TYPE AS RESIDENT_TYPE1,
					CI.SOCI_SECU AS SOCI_SECU,
            	]]>
            </if>
            <if test=" (customer_role == null or customer_role == '') or (policy_code == null or policy_code == '') "> 
            	<![CDATA[ 
            		A.SMOKING_FLAG, A.JOB_CODE, A.CUSTOMER_HEIGHT, A.CUSTOMER_WEIGHT, A.SMOKING_FLAG, A.JOB_CODE,
            		A.CUSTOMER_HEIGHT, A.CUSTOMER_WEIGHT, A.COMPANY_NAME,
            	]]>
            </if>
            <![CDATA[
            A.OFFICE_TEL, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.JOB_TITLE, A.CUSTOMER_ID, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, NVL((SELECT blc.valid 
               FROM DEV_PAS.T_BLACKLIST_CUSTOMER blc
               WHERE blc.CUSTOMER_ID = A.CUSTOMER_ID),0) AS BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, 
			
			A.ANNUAL_INCOME AS ANNUAL_INCOME_DESC,
			A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.CUSTOMER_RISK_LEVEL, CT.TAX_RESIDENT_TYPE,A.INCOME_SOURCE,
			(SELECT CB.CASE_ID FROM (SELECT C.CASE_ID,C.INSURED_ID FROM DEV_CLM.T_CLAIM_CASE C WHERE C.CASE_STATUS = '80' AND C.AUDIT_DECISION = '3' ORDER BY C.INSERT_TIME) CB WHERE CB.INSURED_ID=A.CUSTOMER_ID AND ROWNUM = 1) AS ALWAYS_CLAIMS,
 			(SELECT U.UW_POLICY_ID FROM DEV_UW.T_UW_POLICY U, DEV_UW.T_INSURED_LIST B WHERE U.UW_ID = B.UW_ID AND B.CUSTOMER_ID = A.CUSTOMER_ID AND U.POLICY_DECISION = '50' AND ROWNUM = 1) AS always_Drop,
      	  ]]>
          <if test=" (customer_role != null and customer_role != '') and (policy_code != null and policy_code != '') and customer_role != 'benefit' "> 
          <![CDATA[
          (CASE  WHEN  NVL(CI.JOB_UNDERWRITE,trim(A.JOB_KIND))='1' THEN 
              '一级'
              WHEN  NVL(CI.JOB_UNDERWRITE,trim(A.JOB_KIND)) ='2' THEN 
              '二级'
              WHEN  NVL(CI.JOB_UNDERWRITE,trim(A.JOB_KIND)) ='3' THEN 
              '三级'
              WHEN  NVL(CI.JOB_UNDERWRITE,trim(A.JOB_KIND)) ='4' THEN 
              '四级'
              WHEN  NVL(CI.JOB_UNDERWRITE,trim(A.JOB_KIND)) ='5' THEN 
              '五级'  
              WHEN  NVL(CI.JOB_UNDERWRITE,trim(A.JOB_KIND)) ='6' THEN 
              '六级'
              WHEN  NVL(CI.JOB_UNDERWRITE,trim(A.JOB_KIND)) ='z' THEN 
              '拒保职业'
            END)   AS JOB_UNDERWRITE, /*客户核保职业等级m*/
            ]]>
            </if>
            <if test=" (customer_role == null or customer_role == '') or (policy_code == null or policy_code == '') or customer_role == 'benefit' "> 
            <![CDATA[
            (CASE  WHEN  trim(A.JOB_KIND)='1' THEN 
              '一级'
              WHEN  trim(A.JOB_KIND)='2' THEN 
              '二级'
              WHEN  trim(A.JOB_KIND)='3' THEN 
              '三级'
              WHEN  trim(A.JOB_KIND)='4' THEN 
              '四级'
              WHEN  trim(A.JOB_KIND)='5' THEN 
              '五级'  
              WHEN  trim(A.JOB_KIND)='6' THEN 
              '六级'
              WHEN  trim(A.JOB_KIND)='z' THEN 
              '拒保职业'
            END)   AS JOB_UNDERWRITE, /*客户核保职业等级m*/
            ]]>
            </if>           
            A.SECOND_CERT_TYPE,/*第二证件的类型*/
			A.SECOND_CERTI_CODE/*第二证件的类型号码*/
			<if test=" (customer_role != null and customer_role != '') and (policy_code != null and policy_code != '') ">
			<![CDATA[
			,CI.AGENT_RELATION
			]]>
			</if>
			<if test=" (customer_role != null and customer_role != '') and (policy_code != null and policy_code != '') and customer_role != 'benefit' ">
			<![CDATA[
			,CI.ANNUAL_INCOME_CEIL,
      		CI.INCOME_SOURCE
			]]>
			</if>
			 FROM 
            <if test=" his_list_id!=null">
               DEV_PAS.T_CUSTOMER_B A 
            </if>
            <if test=" his_list_id==null">
               DEV_PAS.T_CUSTOMER A 
            </if>
             <![CDATA[ 
             LEFT JOIN DEV_PAS.T_CUSTOMER_TAX CT
			    ON CT.CUSTOMER_TAX_NAME = A.CUSTOMER_NAME
			    AND CT.CUSTOMER_TAX_GENDER = A.CUSTOMER_GENDER
			    AND CT.CUSTOMER_TAX_BIRTHDAY = A.CUSTOMER_BIRTHDAY
			    AND CT.CUSTOMER_TAX_CERT_TYPE = A.CUSTOMER_CERT_TYPE
			    AND CT.CUSTOMER_TAX_CERTI_CODE = A.CUSTOMER_CERTI_CODE
			    LEFT JOIN DEV_PAS.T_TAX_CUSTOMER_INFO NCT
  			 ON NCT.CUSTOMER_ID = A.CUSTOMER_ID 
			 ]]>
			 <if test=" (customer_role != null and customer_role != '') and (policy_code != null and policy_code != '') ">
			 <![CDATA[ 
			 LEFT JOIN 
			  (SELECT TA.COMPANY_NAME,T.POLICY_CODE,T.AGENT_RELATION,T.CUSTOMER_ID ]]>
			  		  <if test=" customer_role == 'policyHolder' ">
				  		  <![CDATA[, T.JOB_CODE,T.SMOKING,
				  		  T.CUSTOMER_WEIGHT,
				  		  T.CUSTOMER_HEIGHT,
				  		  T.JOB_UNDERWRITE,
				  		  T.ANNUAL_INCOME_CEIL,
						  T.INCOME_SOURCE,
						  T.RESIDENT_TYPE,
						  T.APPLICANT_SPE_PEOPLE,
						  T.SOCI_SECU ]]>
			          </if>
			          <if test=" customer_role == 'insured' ">
				  		  <![CDATA[, T.JOB_CODE,T.SMOKING,
				  		  T.CUSTOMER_WEIGHT,
				  		  T.CUSTOMER_HEIGHT,
				  		  T.JOB_UNDERWRITE,
				  		  T.ANNUAL_INCOME_CEIL,
						  T.INCOME_SOURCE,
						  T.RESIDENT_TYPE,
						  to_char(T.INS_SPE_PEOPLE)AS INS_SPE_PEOPLE,
						  T.RURAL_POPULATION_FLAG,
						  T.DISABILITY_FLAG,
						  T.DISABILITY_NO,

						  T.SOCI_SECU ]]>
			          </if>
			  	 <![CDATA[
			  	 FROM DEV_PAS.T_ADDRESS TA
			          INNER JOIN 
			          	]]>
			          	<if test=" customer_role == 'policyHolder' ">
				          	<![CDATA[ DEV_PAS.T_POLICY_HOLDER T ]]> 
			          	</if>
			          	<if test=" customer_role == 'insured' ">
				          	<![CDATA[ DEV_PAS.T_INSURED_LIST T ]]> 
			          	</if>
			          	<if test=" customer_role == 'benefit' ">
				          	<![CDATA[ DEV_PAS.T_CONTRACT_BENE T ]]> 
			          	</if>
			          <![CDATA[ ON T.ADDRESS_ID = TA.ADDRESS_ID
			          WHERE T.POLICY_CODE= #{policy_code} AND T.CUSTOMER_ID= #{customer_id} AND ROWNUM=1) CI
			  ON A.CUSTOMER_ID = CI.CUSTOMER_ID ]]>
			  </if>
			  <if test=" (customer_role != null and customer_role != '') and (apply_code != null and apply_code != '') ">
			 <![CDATA[ 
			 LEFT JOIN 
			  (SELECT TA.COMPANY_NAME,T.POLICY_CODE,T.AGENT_RELATION,T.CUSTOMER_ID ]]>
			  		  <if test=" customer_role == 'policyHolder' ">
				  		  <![CDATA[, T.JOB_CODE,T.SMOKING,
				  		  T.CUSTOMER_WEIGHT,
				  		  T.CUSTOMER_HEIGHT,
				  		  T.JOB_UNDERWRITE,
				  		  T.ANNUAL_INCOME_CEIL,
						  T.INCOME_SOURCE,
						  T.RESIDENT_TYPE,
						  T.APPLICANT_SPE_PEOPLE,
						  T.SOCI_SECU ]]>
			          </if>
			          <if test=" customer_role == 'insured' ">
				  		  <![CDATA[, T.JOB_CODE,T.SMOKING,
				  		  T.CUSTOMER_WEIGHT,
				  		  T.CUSTOMER_HEIGHT,
				  		  T.JOB_UNDERWRITE,
				  		  T.ANNUAL_INCOME_CEIL,
						  T.INCOME_SOURCE,
						  T.RESIDENT_TYPE,
						  T.INS_SPE_PEOPLE,
						  T.RURAL_POPULATION_FLAG,
						  T.DISABILITY_FLAG,
						  T.DISABILITY_NO,
						  T.SOCI_SECU]]>
			          </if>			          
			  	 <![CDATA[
			  	 FROM DEV_PAS.T_ADDRESS TA
			          INNER JOIN 
			          	]]>
			          	<if test=" customer_role == 'policyHolder' ">
				          	<![CDATA[ DEV_NB.T_NB_POLICY_HOLDER T ]]> 
			          	</if>
			          	<if test=" customer_role == 'insured' ">
				          	<![CDATA[ DEV_NB.T_NB_INSURED_LIST T ]]> 
			          	</if>
			          	<if test=" customer_role == 'benefit' ">
				          	<![CDATA[ DEV_NB.T_NB_CONTRACT_BENE T ]]> 
			          	</if>
			          <![CDATA[ ON T.ADDRESS_ID = TA.ADDRESS_ID
			          WHERE T.APPLY_CODE= #{apply_code} AND T.CUSTOMER_ID= #{customer_id} AND ROWNUM=1) CI
			  ON A.CUSTOMER_ID = CI.CUSTOMER_ID ]]>
			  </if>
            <![CDATA[ WHERE 1 = 1  ]]>
		<include refid="PA_queryCustomerByCustomerIdCondition" />
		<include refid="PA_queryCustomerByCustomerCertiCodeCondition" />
		<include refid="PA_customerWhereCondition" />
	</select>
    
    <!-- 查询保单和契约单条 -->
	<select id="PAandNB_findCustomer" resultType="java.util.Map"
		parameterType="java.util.Map">
		<if test="  type != null and type == '1'.toString() ">
		<![CDATA[ 
		   SELECT A.TAX_RESIDENT_TYPE,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID,
			(SELECT Z.MOBILE_TEL FROM DEV_PAS.T_ADDRESS Z WHERE Z.ADDRESS_ID=
			(SELECT Y.ADDRESS_ID FROM DEV_PAS.T_POLICY_HOLDER Y WHERE Y.POLICY_CODE=#{policy_code, jdbcType=VARCHAR})) AS MOBILE_TEL,
			A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, NVL((SELECT blc.valid 
               FROM DEV_PAS.T_BLACKLIST_CUSTOMER blc
               WHERE blc.CUSTOMER_ID = A.CUSTOMER_ID),0) AS BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, 			
			A.ANNUAL_INCOME AS ANNUAL_INCOME_DESC,
			A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.CUSTOMER_RISK_LEVEL,A.INCOME_SOURCE,
			(SELECT CB.CASE_ID FROM (SELECT C.CASE_ID,C.INSURED_ID FROM DEV_CLM.T_CLAIM_CASE C WHERE C.CASE_STATUS = '80' AND C.AUDIT_DECISION = '3' ORDER BY C.INSERT_TIME) CB WHERE CB.INSURED_ID=A.CUSTOMER_ID AND ROWNUM = 1) AS ALWAYS_CLAIMS,
 			(SELECT U.UW_POLICY_ID FROM DEV_UW.T_UW_POLICY U, DEV_UW.T_INSURED_LIST B WHERE U.UW_ID = B.UW_ID AND B.CUSTOMER_ID = A.CUSTOMER_ID AND U.POLICY_DECISION = '50' AND ROWNUM=1) AS always_Drop
			 FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
		     <include refid="PA_customerWhereCondition" />
			 </if>
			 
		    <if test="  type != null and type == '0'.toString() ">
		    <![CDATA[ 
		   SELECT A.TAX_RESIDENT_TYPE,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID,
			(SELECT Z.MOBILE_TEL FROM DEV_NB.T_ADDRESS Z WHERE Z.ADDRESS_ID=
			(SELECT Y.ADDRESS_ID FROM DEV_NB.T_NB_POLICY_HOLDER Y WHERE Y.APPLY_CODE=#{policy_code, jdbcType=VARCHAR})) AS MOBILE_TEL,
			A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE,
			A.HOUSEKEEPER_FLAG, A.EMAIL, 
			A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.CUSTOMER_RISK_LEVEL,A.INCOME_SOURCE
			 FROM DEV_NB.T_CUSTOMER A WHERE 1 = 1  ]]>
		     <include refid="PA_customerWhereCondition" />
			 </if>
	</select>
    
    <select id="PA_findCustomerByTelPast" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A
            WHERE A.HOUSE_TEL = #{house_tel} or A.MOBILE_TEL = #{mobile_tel} order by A.CUSTOMER_ID ASC ]]>

    </select>

    <select id="PA_findCustomerByTel" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A
            WHERE 1=1 ]]>
        <if test=" mobile_tel != null and mobile_tel != ''  "><![CDATA[ AND A.MOBILE_TEL = #{mobile_tel} ]]></if>
        <if test=" house_tel != null and house_tel != ''  "><![CDATA[ AND A.HOUSE_TEL = #{house_tel} ]]></if>
    </select>

    <select id="PA_findAllCustomerInjured" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
            A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_PAS.T_CUSTOMER A WHERE 1=1]]>
        <include refid="PA_customerInjuredWhereCondition" />
    </select>


    <!-- 分页查询 客户信息 -->
    <select id="PA_findCustomerListForPage" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
            SELECT   TAB.RN AS rowNumber,TAB.CUSTOMER_BIRTHDAY,TAB.CUSTOMER_ID,TAB.CUSTOMER_NAME,TAB.CUSTOMER_CERT_TYPE,
                     TAB.CUSTOMER_CERTI_CODE,TAB.CUSTOMER_GENDER,TAB.CUSTOMER_VIP
            FROM (SELECT  ROWNUM RN,T.CUSTOMER_BIRTHDAY,T.CUSTOMER_ID,T.CUSTOMER_NAME,T.CUSTOMER_CERT_TYPE,T.CUSTOMER_CERTI_CODE,
                  T.CUSTOMER_GENDER,  NVL(T.CUSTOMER_VIP,0) AS CUSTOMER_VIP FROM DEV_PAS.T_CUSTOMER T  WHERE T.CUSTOMER_ID IN
                 (SELECT TL.CUSTOMER_ID FROM DEV_PAS.T_INSURED_LIST TL WHERE 1=1 ]]>
        <include refid="PA_customerHolderCondition" />    
             <![CDATA[
                UNION 
                SELECT TP.CUSTOMER_ID FROM DEV_PAS.T_POLICY_HOLDER TP WHERE 1=1]]>
        <include refid="PA_customerInsuredCondition" />
             <![CDATA[
                  ) 
             ]]>
        <include refid="PA_customerBaseCodition" />
             <![CDATA[
                )  TAB
                WHERE TAB.RN > #{GREATER_NUM} order by  TAB.CUSTOMER_BIRTHDAY desc
             ]]>
    </select>
    <!-- 根据业务员编号和生日区间查询客户列表 -->
    <select id="PA_findAllCustomerByAgentCodeAndBirthDay"
        resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[   
        select d.policy_code,
               (select (select product_abbr_name
                          from DEV_PDS.T_business_product where product_code_sys = g.busi_prod_code )
                  from DEV_PAS.T_CONTRACT_BUSI_PROD g where g.policy_id = d.policy_id
                   and g.policy_code = d.policy_code and g.MASTER_BUSI_ITEM_ID is null) as riskname,
               c.customer_name, c.customer_birthday, c.mobile_tel,
               (select f.ADDRESS from DEV_PAS.T_ADDRESS f where f.address_id = d.address_id
                   and f.customer_id = d.customer_id) as address from DEV_PAS.T_customer c, T_INSURED_LIST d
         where c.customer_id in
               (select a.customer_id from DEV_PAS.T_INSURED_LIST a,
                   (select policy_code, policy_id from DEV_PAS.T_contract_agent where agent_code = #{agent_code}) b
                 where a.policy_id = b.policy_id and a.policy_code = b.policy_code)
           and c.customer_id = d.customer_id 
           and to_char(c.customer_birthday,'MMdd')<= #{birthDay_end} 
           and to_char(c.customer_birthday,'MMdd')>= #{birthDay_start}
        union
        select d.policy_code,
                (select (select product_abbr_name
                          from DEV_PDS.T_business_product where product_code_sys = g.busi_prod_code )
                  from DEV_PAS.T_CONTRACT_BUSI_PROD g where g.policy_id = d.policy_id
                   and g.policy_code = d.policy_code and g.MASTER_BUSI_ITEM_ID is null) as riskname,
               c.customer_name, c.customer_birthday, c.mobile_tel,
               (select f.ADDRESS from DEV_PAS.T_ADDRESS f where f.address_id = d.address_id
                   and f.customer_id = d.customer_id) as address from DEV_PAS.T_customer c, T_POLICY_HOLDER d
         where c.customer_id in
               (select a.customer_id from DEV_PAS.T_POLICY_HOLDER a,
                   (select policy_code, policy_id from DEV_PAS.T_contract_agent where agent_code = #{agent_code}) b
                 where a.policy_id = b.policy_id and a.policy_code = b.policy_code)
           and c.customer_id = d.customer_id 
           and to_char(c.customer_birthday,'MMdd')<= #{birthDay_end} 
           and to_char(c.customer_birthday,'MMdd')>= #{birthDay_start}
        ]]>
    </select>


    <!-- 查询被保人生存状态 -->
    <select id="queryLiveStatus" resultType="java.util.Map"
        parameterType="java.util.Map">
    <![CDATA[   
            SELECT C.LIVE_STATUS
              from DEV_PAS.T_BENEFIT_INSURED    A,
                   T_INSURED_LIST       B,
                   T_CUSTOMER           C
             WHERE A.INSURED_ID = B.LIST_ID
               AND B.CUSTOMER_ID = C.CUSTOMER_ID
               AND A.BUSI_ITEM_ID = #{busi_item_id}
        ]]>
    </select>
    <!-- 查询身份证号升位轨迹 -->
    <select id="PA_findCerticode" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
                SELECT A.CUSTOMER_CERTI_CODE,   /*客户证件号*/
                       A.CUSTOMER_NAME,          /*客户名称*/
                       A.CUSTOMER_ID,          /*客户记录编号*/
                       A.CUSTOMER_CERT_TYPE, /*客户证件类型*/
                       TL.POLICY_CODES,        /*保单号*/
                       TL.CERTI_CODE_BF,       /*自动升位前证件号码*/
                       TL.CERTI_CODE_AF,       /*自动升位后证件号码*/
                       TL.PROMOTE_DATE         /*自动升位日期*/
                    FROM DEV_PAS.T_CERTICODE_PROMOTE_LOG    TL
                    LEFT JOIN   DEV_PAS.T_CUSTOMER      A
                    ON A.CUSTOMER_ID = TL.CUSTOMER_ID
                    WHERE A.CUSTOMER_ID = #{customer_id}
    ]]>
    </select>

 	<!-- P00001002247接口根据证件号、投保人证件号、被保人证件号查询该客户名下的保单   -->
    <select id="QRY_INTEGRAL_findAllCustomerPolicyAndInsured2247" resultType="java.util.Map"
        parameterType="java.util.Map">
        
         <if test=" flag == 0 ">
	        <![CDATA[ 
	        
		        select ph.policy_code from dev_pas.t_policy_holder ph ,dev_pas.t_customer cus 
				where ph.customer_id = cus.customer_id and cus.customer_certi_code=#{idNo}
				union
				select ph.policy_code from dev_pas.t_policy_holder ph ,dev_pas.t_customer cus 
				where ph.customer_id = cus.customer_id and cus.customer_id_code=#{idNo}
	        ]]>
        </if>
        
        <if test=" flag == 1 ">
	        <![CDATA[ 
	        
				select il.policy_code from dev_pas.t_insured_list il ,dev_pas.t_customer cus 
				where il.customer_id = cus.customer_id and cus.customer_certi_code=#{idNo} 
				union
				select il.policy_code from dev_pas.t_insured_list il ,dev_pas.t_customer cus 
				where il.customer_id = cus.customer_id and cus.customer_id_code=#{idNo}
			
	        ]]>
        </if>
        
        <if test=" flag == 2 ">
	       <![CDATA[ 
		        select ph.policy_code from dev_pas.t_policy_holder ph ,dev_pas.t_customer cus 
				where ph.customer_id = cus.customer_id and cus.customer_certi_code=#{idNo}
				union 
				select il.policy_code from dev_pas.t_insured_list il ,dev_pas.t_customer cus 
				where il.customer_id = cus.customer_id and cus.customer_certi_code=#{idNo} 
				union
				select ph.policy_code from dev_pas.t_policy_holder ph ,dev_pas.t_customer cus 
				where ph.customer_id = cus.customer_id and cus.customer_id_code=#{idNo}
				union 
				select il.policy_code from dev_pas.t_insured_list il ,dev_pas.t_customer cus 
				where il.customer_id = cus.customer_id and cus.customer_id_code=#{idNo}
	       ]]>
       </if>
        
       
    </select>
	    
    <!-- 新老核心客户号转换 105_927-->
    <sql id="QRY_queryCustomerByCustomerIdOrOldCustomerIdCodeCondition">
        <if test=" customer_id != null"><![CDATA[ AND A.customer_id = #{customer_id} ]]></if>
        <if test=" old_customer_id != null and old_customer_id != '' "><![CDATA[ AND A.old_customer_id = #{old_customer_id} ]]></if>
    </sql>
    <!-- 105——927新老核心客户号转换 -->
    <select id="QRY_findCustomerByCustomerIdOrOldCustomerId" resultType="java.util.Map"
            parameterType="java.util.Map">
            <![CDATA[ SELECT A.CUSTOMER_RISK_LEVEL,A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
            A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
            A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
            A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
            A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
            A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, A.HOUSEKEEPER_FLAG, 
            A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, A.CUSTOMER_CERTI_CODE, 
            A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.RESIDENT_TYPE,
            A.TAX_RESIDENT_TYPE,A.NON_RESIDENT_FLAG,A.INCOME_SOURCE,A.ANNUAL_INCOME_CEIL,A.SOCI_SECU FROM DEV_PAS.T_CUSTOMER A WHERE 1 = 1  ]]>
        <include refid="QRY_queryCustomerByCustomerIdOrOldCustomerIdCodeCondition" />
        <![CDATA[ ORDER BY A.CUSTOMER_ID ]]>
         <!-- 105——927新老核心客户号转换 -->
    </select>
         <!-- 电话中心接口未承保保单根据证件号、投保人证件号、被保人证件号查询该客户名下的所有保单号和投保单号 -->
    <select id="findAllPolicyandApplycode" resultType="java.util.Map"  parameterType="java.util.Map">      
         <if test=" flag == 0 ">
	        <![CDATA[ 	        
		        select ph.policy_code,ph.apply_code from dev_nb.t_nb_policy_holder ph ,dev_nb.t_customer cus 
				where ph.customer_id = cus.customer_id and cus.customer_certi_code=#{idNo}
				union
				select ph.policy_code,ph.apply_code from dev_nb.t_nb_policy_holder ph ,dev_nb.t_customer cus 
				where ph.customer_id = cus.customer_id and cus.customer_id_code=#{idNo}
	        ]]>
        </if>       
        <if test=" flag == 1 ">
	        <![CDATA[ 
	        
				select il.policy_code,il.apply_code from dev_nb.t_nb_insured_list il ,dev_nb.t_customer cus 
				where il.customer_id = cus.customer_id and cus.customer_certi_code=#{idNo} 
				union
				select il.policy_code,il.apply_code from dev_nb.t_nb_insured_list il ,dev_nb.t_customer cus 
				where il.customer_id = cus.customer_id and cus.customer_id_code=#{idNo}
			
	        ]]>
        </if>
        
        <if test=" flag == 2 ">
	       <![CDATA[ 
		        select ph.policy_code,ph.apply_code from dev_nb.t_nb_policy_holder ph ,dev_nb.t_customer cus 
				where ph.customer_id = cus.customer_id and cus.customer_certi_code=#{idNo}
				union 
				select il.policy_code,il.apply_code from dev_nb.t_nb_insured_list il ,dev_nb.t_customer cus 
				where il.customer_id = cus.customer_id and cus.customer_certi_code=#{idNo} 
				union
				select ph.policy_code,ph.apply_code from dev_nb.t_nb_policy_holder ph ,dev_nb.t_customer cus 
				where ph.customer_id = cus.customer_id and cus.customer_id_code=#{idNo}
				union 
				select il.policy_code,il.apply_code from dev_nb.t_nb_insured_list il ,dev_nb.t_customer cus 
				where il.customer_id = cus.customer_id and cus.customer_id_code=#{idNo}
	       ]]>
       </if>    
        <if test=" flag == 3 ">
	        <![CDATA[ 	        
				select tph.policy_code,tph.apply_code from dev_nb.t_nb_policy_holder tph
				where tph.customer_id = #{customer_id} union
				select til.policy_code,til.apply_code from dev_nb.t_nb_insured_list til
				where til.customer_id = #{customer_id}
	        ]]>
        </if>   
    </select>
    <!-- 电话中心未承保保单查询所有契约客户操作 -->
	<select id="findAllCustomerbyNB" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND, 
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS FROM DEV_NB.T_CUSTOMER A WHERE ROWNUM <=  1000  ]]>
		<include refid="PA_customerWhereCondition" />
	</select>
    <!-- 通过customer_id查询客户名下是否有上海医保保单 -->
    <select id="PA_findIsHisHealthPolicy" resultType="java.lang.Integer" parameterType="java.util.Map">
    	<![CDATA[ SELECT COUNT(1) FROM DEV_PAS.T_CONTRACT_BUSI_PROD TCBP,
    				(SELECT NPH.APPLY_CODE, NPH.POLICY_ID FROM DEV_PAS.T_POLICY_HOLDER NPH
						WHERE NPH.CUSTOMER_ID=#{customer_id}
					UNION
					SELECT NIL.APPLY_CODE, NIL.POLICY_ID FROM DEV_PAS.T_BENEFIT_INSURED NBI,DEV_PAS.T_INSURED_LIST NIL
						WHERE NBI.INSURED_ID=NIL.LIST_ID AND NIL.CUSTOMER_ID=#{customer_id}) PI
					WHERE TCBP.APPLY_CODE=PI.APPLY_CODE AND TCBP.MASTER_BUSI_ITEM_ID IS NULL 
					AND TCBP.BUSI_PROD_CODE IN ('00557000','00558000','00842000','00843000')
    	 ]]>
    </select>
    
    <!-- 根据客户id查询客户信息(#rm133925) -->
    <select id="qry_findCustomerInterface" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT * FROM (SELECT A.RZ_LEVEL, A.CUSTOMER_NAME, A.CUSTOMER_LEVEL, A.NATION_CODE, A.DEATH_DATE, A.JOB_NATURE, A.REMARK, A.CUST_PWD,A.CUSTOMER_RISK_LEVEL, 
			A.WECHAT_NO, A.OFFEN_USE_TEL, A.UN_CUSTOMER_CODE, A.CUST_CERT_END_DATE, A.QQ, A.OLD_CUSTOMER_ID, 
			A.MOBILE_TEL, A.CUSTOMER_BIRTHDAY, A.IS_PARENT, A.COUNTRY_CODE, A.FAX_TEL, A.JOB_CODE, 
			A.OFFICE_TEL, A.SMOKING_FLAG, A.MARRIAGE_STATUS, A.EDUCATION, A.CUSTOMER_ID_CODE, 
			A.CUSTOMER_GENDER, A.OTHER, A.COMPANY_NAME, A.JOB_TITLE, A.CUSTOMER_ID, A.CUSTOMER_HEIGHT, A.HEALTH_STATUS, 
			A.CUSTOMER_CERTI_CODE, A.RETIRED_FLAG, A.DRUNK_FLAG, A.MARRIAGE_DATE, A.BLACKLIST_FLAG, A.DRIVER_LICENSE_TYPE, 
			A.HOUSEKEEPER_FLAG, A.EMAIL, A.ANNUAL_INCOME, A.CUSTOMER_CERT_TYPE, A.HOUSE_TEL, A.JOB_KIND,A.TAX_RESIDENT_TYPE,  
			A.CUSTOMER_WEIGHT, A.RELIGION_CODE, A.CUST_CERT_STAR_DATE, A.SYN_MDM_FLAG, A.CUSTOMER_VIP, A.LIVE_STATUS,A.COMM_METHOD,A.RESIDENT_TYPE,
			A.SECOND_CERT_TYPE,A.SECOND_CERTI_CODE,A.INS_SPE_PEOPLE,A.RURAL_POPULATION_FLAG,A.DISABILITY_FLAG,A.DISABILITY_NO,
			A.SOCI_SECU,A.IS_SUBSCRIPTION_EMAIL,A.NON_RESIDENT_FLAG,A.INCOME_SOURCE,A.ANNUAL_INCOME_CEIL,A.SOCIAL_IDENTITY FROM APP___PAS__DBUSER.T_CUSTOMER A WHERE 1 = 1 ]]>
		<include refid="PA_queryCustomerByCustomerCertiCodeCondition" />
		<include refid="PA_customerWhereCondition" />
		<![CDATA[ 
			ORDER BY A.INSERT_TIME DESC)
			WHERE rownum=1
		]]>
	</select>
	
	<select id="PA_findCustRightsQual" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	
		SELECT A.CUSTOMER_ID_CODE,
		       A.XHZ_LEVEL,
		       A.XHR_LEVEL,
		       A.XHA_LEVEL,
		       C.Customer_Id,
		       C.Customer_Name
		  FROM APP___PAS__DBUSER.T_CUSTOMER C
		  LEFT JOIN APP___PAS__DBUSER.T_CUSTOMER_RIGHTS_QUAL A
		    ON C.Customer_Certi_Code = A.Customer_Id_Code
		 WHERE 1 = 1
		   AND C.CUSTOMER_ID = #{customer_id}
		]]>
	</select>
</mapper>
