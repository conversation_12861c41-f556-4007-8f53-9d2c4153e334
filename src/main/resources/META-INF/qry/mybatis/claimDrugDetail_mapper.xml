<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimDrugDetailPO">

	<sql id="claimDrugDetailWhereCondition">
		<if test=" vendor_name != null and vendor_name != ''  "><![CDATA[ AND A.VENDOR_NAME = #{vendor_name} ]]></if>
		<if test=" chemical_name != null and chemical_name != ''  "><![CDATA[ AND A.CHEMICAL_NAME = #{chemical_name} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" drug_code != null and drug_code != ''  "><![CDATA[ AND A.DRUG_CODE = #{drug_code} ]]></if>
		<if test=" drug_type != null and drug_type != ''  "><![CDATA[ AND A.DRUG_TYPE = #{drug_type} ]]></if>
		<if test=" drug_name != null and drug_name != ''  "><![CDATA[ AND A.DRUG_NAME = #{drug_name} ]]></if>
	</sql>
<sql id="queryDrug">
		<if test=" drug_name != null and drug_name != ''  "><![CDATA[ AND A.DRUG_NAME like '%'||#{drug_name}||'%'  ]]></if>
		<if test=" chemical_name != null and chemical_name != ''  "><![CDATA[ AND A.CHEMICAL_NAME    like '%'||#{chemical_name}||'%'    ]]></if>
		<if test=" drug_type != null and drug_type != ''  "><![CDATA[ AND A.DRUG_TYPE = #{drug_type} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	

<!-- 添加操作 -->
	<insert id="addClaimDrugDetail"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_DRUG_DETAIL__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL(
				INSERT_TIMESTAMP, UPDATE_BY, VENDOR_NAME, CHEMICAL_NAME, INSERT_TIME, LIST_ID, UPDATE_TIMESTAMP, 
				UPDATE_TIME, DRUG_CODE, INSERT_BY, DRUG_TYPE, DRUG_NAME ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{vendor_name, jdbcType=VARCHAR} , #{chemical_name, jdbcType=VARCHAR} , SYSDATE , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, SYSDATE , #{drug_code, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{drug_type, jdbcType=VARCHAR} , #{drug_name, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimDrugDetail" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimDrugDetail" parameterType="java.util.Map">
		<![CDATA[ UPDATE  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			VENDOR_NAME = #{vendor_name, jdbcType=VARCHAR} ,
			CHEMICAL_NAME = #{chemical_name, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
			DRUG_CODE = #{drug_code, jdbcType=VARCHAR} ,
			DRUG_TYPE = #{drug_type, jdbcType=VARCHAR} ,
			DRUG_NAME = #{drug_name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimDrugDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VENDOR_NAME, A.CHEMICAL_NAME, A.LIST_ID, 
			A.DRUG_CODE, A.DRUG_TYPE, A.DRUG_NAME FROM  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimDrugDetail" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.VENDOR_NAME, A.CHEMICAL_NAME, A.LIST_ID, 
			A.DRUG_CODE, A.DRUG_TYPE, A.DRUG_NAME FROM  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimDrugDetailWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimDrugDetailTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL A WHERE 1 = 1  ]]>
		<include refid="queryDrug" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimDrugDetailForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.VENDOR_NAME, B.CHEMICAL_NAME, B.LIST_ID, 
			B.DRUG_CODE, B.DRUG_TYPE, B.DRUG_NAME FROM (
					SELECT ROWNUM RN, A.VENDOR_NAME, A.CHEMICAL_NAME, A.LIST_ID, 
			A.DRUG_CODE, A.DRUG_TYPE, A.DRUG_NAME FROM  APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="queryDrug" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
