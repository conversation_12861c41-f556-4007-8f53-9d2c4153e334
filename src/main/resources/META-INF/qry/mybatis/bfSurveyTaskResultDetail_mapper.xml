<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.qry.interfaces.model.po.ContractMasterPO">
	<!-- 分页查询保后调查结果清单 --><!-- ''空串字段在service层单独处理 -->
	<select id="queryBfSurveyTasksResultDetailForPage" parameterType="java.util.Map"
		resultType="java.util.Map">
	<![CDATA[ select Z.RN as ROWNUMER,
        Z.ORGAN_CODE,
       Z.second_organ_code,
       Z.third_organ_code,
       Z.polic_code,
       Z.status_name,
       z.product_code_sys,
       Z.product_name_sys,
       Z.amount,
       Z.risk_level,
       Z.risk_score,
       Z.policy_holder_name,
       Z.customer_name,
       Z.customer_certi_code,
       Z.agent_code,
       Z.agent_name,
       Z.organ_code_group,
       Z.organ_code_group_name,
       Z.survey_status,
       Z.positive_flag,
       Z.survey_conclusion,
       Z.accept_status,
       Z.decision_code,
       Z.service_code,
       Z.operation,
       Z.operation_status,
       Z.fee_amount,
       Z.total_additional_fee
  from (select ROWNUM RN,
               M.* from(SELECT CM.ORGAN_CODE,
                 substr(CM.ORGAN_CODE, 1, 4) as second_organ_code,
                 substr(CM.ORGAN_CODE, 1, 6) as third_organ_code,
                 CM.POLICY_CODE as polic_code,
                 (SELECT LS.STATUS_NAME
                    FROM DEV_PAS.T_Liability_Status LS
                   WHERE LS.STATUS_CODE = CM.LIABILITY_STATE) as status_name,
                 (SELECT cbp.busi_prod_code
						  FROM dev_pas.t_contract_busi_prod cbp
						 where cbp.busi_item_id =
						       (SELECT max(cbp1.busi_item_id)
						          FROM dev_pas.t_contract_busi_prod cbp1
						         where cbp1.master_busi_item_id is null
						           and cbp1.policy_code = cbp.policy_code)
						   and cbp.policy_id = cm.policy_id) as product_code_sys,
                 (SELECT bp.product_name_sys
						  FROM dev_pas.t_contract_busi_prod cbp
						   inner join dev_pds.t_business_product bp
						   on cbp.busi_prod_code = bp.product_code_sys
						 where cbp.busi_item_id =
						       (SELECT max(cbp1.busi_item_id)
						          FROM dev_pas.t_contract_busi_prod cbp1
						         where cbp1.master_busi_item_id is null
						           and cbp1.policy_code = cbp.policy_code)
						   and cbp.policy_id = cm.policy_id) as product_name_sys,
                 (SELECT sum(cp.amount)
						  FROM dev_pas.t_contract_busi_prod cbp
						   inner join dev_pas.t_contract_product cp
						   on cp.busi_item_id = cbp.busi_item_id
						 where cbp.busi_item_id =
						       (SELECT max(cbp1.busi_item_id)
						          FROM dev_pas.t_contract_busi_prod cbp1
						         where cbp1.master_busi_item_id is null
						           and cbp1.policy_code = cbp.policy_code)
						   and cbp.policy_id = cm.policy_id) as amount,
                 '' as risk_level,
                 '' as risk_score,
                 (SELECT cu.customer_name
                    FROM dev_pas.t_policy_holder ph
                   inner join dev_pas.t_customer cu
                      on ph.customer_id = cu.customer_id
                   where ph.policy_id = cm.policy_id) as POLICY_HOLDER_NAME,
                 (SELECT LISTAGG(cu.customer_name, ', ') WITHIN GROUP(ORDER BY cu.customer_name)
                    FROM dev_pas.t_insured_list il
                   inner join dev_pas.t_customer cu
                      on il.customer_id = cu.customer_id
                   where il.policy_id = cm.policy_id) as CUSTOMER_NAME,
                 (SELECT LISTAGG(cu.customer_certi_code, ', ') WITHIN GROUP(ORDER BY cu.customer_name)
                    FROM dev_pas.t_insured_list il
                   inner join dev_pas.t_customer cu
                      on il.customer_id = cu.customer_id
                   where il.policy_id = cm.policy_id) as CUSTOMER_CERTI_CODE,
                 (SELECT ca.agent_code
                    FROM dev_pas.t_contract_agent ca
                   where ca.is_nb_agent = '1'
                     and ca.policy_id = cm.policy_id) as agent_code,
                 (SELECT ca.agent_name
                    FROM dev_pas.t_contract_agent ca
                   where ca.is_nb_agent = '1'
                     and ca.policy_id = cm.policy_id) as agent_name,
                 (SELECT so3.sales_organ_code || '/' || so2.sales_organ_code || '/' ||
                         so1.sales_organ_code
                    FROM dev_pas.t_contract_agent ca
                   inner join dev_pas.t_agent ag
                      on ag.agent_code = ca.agent_code
                   inner join dev_pas.t_sales_organ so1
                      on ag.group_code = so1.sales_organ_code
                   inner join dev_pas.t_sales_organ so2
                      on so1.parent_code = so2.sales_organ_code
                   inner join dev_pas.t_sales_organ so3
                      on so2.parent_code = so3.sales_organ_code
                   where ca.is_nb_agent = '1'
                     and ca.policy_id = cm.policy_id) as organ_code_group,
                 (SELECT so3.sales_organ_name || '/' || so2.sales_organ_name || '/' ||
                         so1.sales_organ_name
                    FROM dev_pas.t_contract_agent ca
                   inner join dev_pas.t_agent ag
                      on ag.agent_code = ca.agent_code
                   inner join dev_pas.t_sales_organ so1
                      on ag.group_code = so1.sales_organ_code
                   inner join dev_pas.t_sales_organ so2
                      on so1.parent_code = so2.sales_organ_code
                   inner join dev_pas.t_sales_organ so3
                      on so2.parent_code = so3.sales_organ_code
                   where ca.is_nb_agent = '1'
                     and ca.policy_id = cm.policy_id) as organ_code_group_name,
                 (SELECT sst.name
                    FROM dev_clm.t_Survey_Status sst
                   where sst.code = sa.survey_status) survey_status,
                 (SELECT YN.TYPE_NAME FROM DEV_CLM.T_YES_NO YN WHERE YN.YES_NO=SC.POSITIVE_FLAG) as positive_flag,
                 SC.SURVEY_CONCLUSION as survey_conclusion,
                 (case
                   when exists (SELECT 1
                           FROM dev_clm.T_CLAIM_HI_TASK cht
                          where cht.apply_id = sa.apply_id) then
                    (SELECT ast.status_desc
							  FROM dev_clm.T_CLAIM_HI_TASK cht
							inner join dev_pas.t_cs_accept_change cac 
							     on cht.accept_code=cac.accept_code
							inner join dev_pas.t_accept_status ast
							     on ast.accept_status=cac.accept_status
                           where cht.list_id = (SELECT max(cht1.list_id)
                                                  FROM dev_clm.T_CLAIM_HI_TASK cht1
                                                 where cht1.apply_id = cht.apply_id)
                             and cht.apply_id=sa.apply_id)
                   else
                    '未发起'
                 end) as accept_status,
                 (SELECT (CASE
                           WHEN not exists (SELECT 1
                                   FROM DEV_PAS.T_UW_BUSI_PROD ubp
                                  where ubp.decision_code <> '10'
                                    and ubp.policy_id = up.policy_id
                                    and ubp.uw_id = up.uw_id) then
                            '标准承保'
                           when not exists (SELECT 1
                                   FROM DEV_PAS.T_UW_BUSI_PROD ubp
                                  where ubp.decision_code <> '50'
                                    and ubp.policy_id = up.policy_id
                                    and ubp.uw_id = up.uw_id) then
                            '拒保'
                           when not exists (SELECT 1
                                   FROM DEV_PAS.T_UW_BUSI_PROD ubp
                                  where ubp.decision_code is not null
                                    and ubp.policy_id = up.policy_id
                                    and ubp.uw_id = up.uw_id) then
                            ''
                           else
                            '非标准承保'
                         end)
                    from dev_pas.t_uw_policy up
                   inner join dev_pas.t_uw_master um
                      on up.uw_id = um.uw_id
                   where up.policy_id = cm.policy_id
                     and um.biz_code = sa.cs_accept_code) as decision_code,
                 (CASE
                   WHEN EXISTS (SELECT 1
                           from APP___PAS__DBUSER.t_cs_policy_change cpc
                          inner join dev_pas.t_cs_accept_change cac
                             on cac.accept_status = '18' 
                             and cac.service_code in ('CT', 'EA','XT')
                            and cac.accept_id = cpc.accept_id
                          where cpc.policy_id = CM.POLICY_ID) THEN
                    '是'
                   ELSE
                    '否'
                 END) AS operation,
                 (SELECT case
                           when CAC.service_code in ('CT','XT') THEN
                            '退保'
                           WHEN CAC.SERVICE_CODE = 'EA' THEN
                            '公司解约'
                           ELSE
                            ''
                         END
                    FROM APP___PAS__DBUSER.t_cs_policy_change cpc
                   inner join dev_pas.t_cs_accept_change cac
                      on cac.accept_status = '18' 
                     and cac.service_code in ('CT', 'EA','XT')
                     and cac.accept_id = cpc.accept_id
                   where cpc.policy_id = CM.POLICY_ID) AS service_code,
                 (SELECT (SELECT ASS.STATUS_DESC
                            FROM DEV_PAS.T_ACCEPT_STATUS ASS
                           WHERE ASS.ACCEPT_STATUS = cac.accept_status)
                    FROM APP___PAS__DBUSER.t_cs_policy_change cpc
                   inner join dev_pas.t_cs_accept_change cac
                      on cac.accept_status = '18' 
                     and cac.service_code in ('CT', 'EA','XT')
                     and cac.accept_id = cpc.accept_id
                   where cpc.policy_id = CM.POLICY_ID) AS operation_status,
                 (SELECT sum(nvl(cpa.fee_amount, 0))
                    from APP___PAS__DBUSER.t_cs_policy_change cpc
                   inner join dev_pas.t_cs_accept_change cac
                      on cac.accept_status = '18'
                     and cac.service_code in ('CT', 'EA','XT')
                     and cac.accept_id = cpc.accept_id
                   inner join dev_pas.t_cs_prem_arap cpa
                      on cpa.fee_type in ('P004300000','P004130000')
                     and cpa.business_code = cac.accept_code
                   where cpc.policy_code = cm.policy_code) fee_amount,
                 (SELECT sum(cep.EXTRA_PREM)
                    from dev_pas.t_cs_accept_change cac
                   inner join dev_pas.t_cs_policy_change cpc
                      on cac.accept_id = cpc.accept_id
                   inner join dev_pas.T_CS_EXTRA_PREM cep
                      on cep.policy_chg_id = cpc.policy_chg_id
                   where cac.accept_code = sa.cs_accept_code) total_additional_fee
            FROM DEV_clm.T_SURVEY_APPLY SA
           INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
              ON SA.POLICY_CODE = CM.POLICY_CODE
           INNER JOIN DEV_CLM.t_claim_survey_task CST
              ON CST.LIST_ID = SA.SURVEY_RULE_ID
           INNER JOIN DEV_CLM.T_CLAIM_SURVEY_BATCH CSB
              ON CSB.BATCH_ID = CST.BATCH_ID
            LEFT JOIN DEV_CLM.T_SURVEY_CONCLUSION SC
              ON SC.APPLY_ID = SA.APPLY_ID
           WHERE SA.BIZ_TYPE = '5'
	]]>
	<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND CM.ORGAN_CODE like ${organ_code}||'%' ]]></if>
	<if test=" plan_name != null and plan_name != ''  "><![CDATA[ AND CSB.PLAN_NAME like '%'||${plan_name}||'%' ]]></if>
	<if test=" positive_flag != null and positive_flag != ''  "><![CDATA[ AND SC.POSITIVE_FLAG = #{positive_flag} ]]></if>
	<if test=" startTime != null and startTime != ''  "><![CDATA[ AND SA.Apply_Date >= #{startTime} ]]></if>
	<if test=" endTime != null and endTime != ''  "><![CDATA[ AND SA.Apply_Date <= #{endTime} ]]></if>
	<if test=" survey_status != null and survey_status != ''  "><![CDATA[ AND SA.SURVEY_STATUS = #{survey_status} ]]></if>
	<![CDATA[ )M WHERE ROWNUM <= #{LESS_NUM}  ]]>
	<![CDATA[ ) Z WHERE Z.RN > #{GREATER_NUM} ]]>
    </select>
      
      
    <select id="queryBfSurveyTasksResultDetailForPageTotal" parameterType="java.util.Map"
		resultType="java.lang.Integer">
		<![CDATA[SELECT COUNT(1)  FROM DEV_clm.T_SURVEY_APPLY SA
						 INNER JOIN DEV_PAS.T_CONTRACT_MASTER CM
						    ON SA.POLICY_CODE = CM.POLICY_CODE
						 INNER JOIN DEV_CLM.t_claim_survey_task CST
						    ON CST.LIST_ID = SA.SURVEY_RULE_ID
						 INNER JOIN DEV_CLM.T_CLAIM_SURVEY_BATCH CSB
						    ON CSB.BATCH_ID = CST.BATCH_ID
						  LEFT JOIN DEV_CLM.T_SURVEY_CONCLUSION SC
						    ON SC.APPLY_ID = SA.APPLY_ID
						 WHERE SA.BIZ_TYPE = '5'
	  
		]]>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND CM.ORGAN_CODE like ${organ_code}||'%' ]]></if>
		<if test=" plan_name != null and plan_name != ''  "><![CDATA[ AND CSB.PLAN_NAME like '%'||${plan_name}||'%' ]]></if>
		<if test=" positive_flag != null and positive_flag != ''  "><![CDATA[ AND SC.POSITIVE_FLAG = #{positive_flag} ]]></if>
		<if test=" startTime != null and startTime != ''  "><![CDATA[ AND SA.Apply_Date >= #{startTime} ]]></if>
		<if test=" endTime != null and endTime != ''  "><![CDATA[ AND SA.Apply_Date <= #{endTime} ]]></if>
		<if test=" survey_status != null and survey_status != ''  "><![CDATA[ AND SA.SURVEY_STATUS = #{survey_status} ]]></if>
    </select>
    
    <select id="findBfSurveyTasksResultDetailfindAmount" parameterType="java.util.Map"
		resultType="java.util.Map">
	<![CDATA[ 
	select SUM(a.FEE_AMOUNT) fee_amount,sum(aa.extra_prem_af) amount1,sum(cc.extra_prem_af) amount2
                     FROM DEV_PAS.T_CS_CONTRACT_PRODUCT aa,
                          DEV_PAS.T_CS_POLICY_CHANGE    bb,
                          DEV_PAS.T_CS_CONTRACT_PRODUCT cc,
                          DEV_PAS.T_CS_PREM_ARAP a
                     left join DEV_PAS.T_CS_ACCEPT_CHANGE b
                       ON b.ACCEPT_CODE = a.BUSINESS_CODE 
                    WHERE aa.old_new = '1'
                       and a.POLICY_CODE = bb.POLICY_CODE
                       and aa.policy_chg_id = bb.policy_chg_id
                       and cc.policy_chg_id = bb.policy_chg_id
                       and cc.old_new = '0']]>
	<if test=" policy_code != null and policy_code != ''  "><![CDATA[ and a.POLICY_CODE  = #{policy_code} ]]></if>
    </select>
    
</mapper>