<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nci.tunan.qry.impl.task.dao.impl.ClmQueryDaoImpl"> 

    <sql id="claimTypeName">
        <![CDATA[ (SELECT TO_CHAR(WMSYS.WM_CONCAT(CT.NAME)) CLAIMTYPENAME
           FROM DEV_CLM.T_CLAIM_SUB_CASE SC 
        RIGHT JOIN DEV_CLM.T_CLAIM_TYPE CT
               ON CT.CODE=SC.CLAIM_TYPE  
               WHERE    SC.CASE_ID = CC.CASE_ID  )  CTN, ]]>
    </sql>  
    <sql id="claimQueryMainField">
               CA.ACCIDENT_NO,
                CC.ORGAN_CODE,
                TO_CHAR(CC.END_CASE_TIME, 'yyyy-MM-dd') END_CASE_TIME,/*结案时间*/
                CC.CASE_ID,
                CC.CASE_NO,
                CC.CASE_STATUS,
                CC.ACTUAL_PAY + NVL(CC.ADVANCE_PAY,0) AS ACTUAL_PAY,/*赔付金额=预付金额+实际给付金额*/
               /*需求变 #39862 关于理赔金实时支付功能的优化需求不发布先注释掉CASE WHEN CC.REALTIME_PAY IS NULL THEN 0 ELSE CC.REALTIME_PAY END AS REALTIME_PAY,*/
                CASE WHEN CC.REALTIME_PAY IS NULL THEN 0 ELSE CC.REALTIME_PAY END AS REALTIME_PAY,
             ( SELECT UW.UW_STATUS
                     FROM DEV_CLM.T_CLAIM_UW UW
                     WHERE UW.CASE_NO = CC.CASE_NO
                     AND UW.CLM_UW_ID=
                             (SELECT MAX(BW.CLM_UW_ID)
                                    FROM DEV_CLM.T_CLAIM_UW BW
                                   WHERE CC.CASE_NO = BW.CASE_NO
                                     ) 
              ) AS UW_STATUS_1,
                ( 
                SELECT SURVEY_STATUS FROM(
                SELECT (CASE SA.SURVEY_STATUS
                   WHEN 1 THEN
                   '已申请 '
                  WHEN 2 THEN
                     '已完成 '
                  WHEN 3 THEN
                     '已撤销'
                  WHEN 4 THEN
                     '进行中 '
                  WHEN 0 THEN
                     '进行中 '
                  ELSE
                   '-'
                END) AS SURVEY_STATUS
                 FROM DEV_CLM.T_SURVEY_APPLY SA
                 WHERE SA.CASE_NO = #{caseNo,jdbcType=VARCHAR} AND SA.BIZ_TYPE=1 ORDER BY SA.APPLY_ID DESC) WHERE  ROWNUM=1) AS CHAKAN1,
                (SELECT TO_CHAR(WMSYS.WM_CONCAT(CT.NAME))
                   FROM DEV_CLM.T_CLAIM_SUB_CASE SC
                  RIGHT JOIN DEV_CLM.T_CLAIM_TYPE CT
                     ON CT.CODE = SC.CLAIM_TYPE
                  WHERE SC.CASE_ID = CC.CASE_ID) CLAIMTYPE,
                CA.INSURED_ID,
                CM.CUSTOMER_NAME,
                CM.CUSTOMER_ID,
                CM.CUSTOMER_CERTI_CODE,
                CC.AUDIT_PERMISSION_NAME AUDIT_PERMISSION_NAME1,
                CC.APPROVE_PERMISSION_NAME APPROVE_PERMISSION_NAME1,
                CC.GREEN_FLAG,
                CC.RPTR_TIME,
                CC.REGISTE_TIME,
                (SELECT nvl(SUM(PA.FEE_AMOUNT)*-1,0) FROM DEV_CAP.V_PREM_ARAP PA WHERE PA.BUSINESS_CODE=CC.CASE_NO
                AND PA.ARAP_FLAG=1 AND PA.PAY_MODE!='99'  AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14') AS ACTUAL_PAY1,
                (SELECT nvl(SUM(PA.FEE_AMOUNT),0)  FROM DEV_CAP.V_PREM_ARAP  PA WHERE PA.BUSINESS_CODE=CC.CASE_NO
                AND PA.ARAP_FLAG=2 AND PA.PAY_MODE!='99' AND  PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14') AS ACTUAL_PAY2,
                (SELECT count(*) FROM DEV_CAP.V_PREM_ARAP  PA WHERE PA.BUSINESS_CODE=CC.CASE_NO  AND PA.ARAP_FLAG=2 AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14' AND PA.FEE_AMOUNT>0) AS PAY_COUNT,
                (SELECT COUNT(*) FROM  DEV_CAP.V_PREM_ARAP PA WHERE PA.BUSINESS_CODE=CC.CASE_NO AND PA.FEE_STATUS!='01' AND PA.FEE_STATUS!='16' AND PA.Fee_Status!='02'  AND PA.FEE_STATUS!='14' AND PA.FEE_AMOUNT>0) AS UN_PAY_COUNT ,
                CC.ACCEPT_TIME
    </sql>
    
    <!-- 理赔综合查询-理赔查询 -->
    <select id="queryClmInfo" resultType="java.util.Map"
        parameterType="java.util.Map"> 
        <![CDATA[ 
       SELECT C.*
  FROM (SELECT ROWNUM RN, 
          (  
          CASE
                 WHEN UW_STATUS_1 ='0' THEN
                  '待二核'
                 WHEN UW_STATUS_1 ='1' THEN
                  '二核完成'
                 WHEN UW_STATUS_1 ='2' THEN
                  '已撤销' 
                 WHEN UW_STATUS_1 ='3' THEN
                  '二核回退' 
                 WHEN UW_STATUS_1 ='4' THEN
                  '待二核加费'
                 ELSE
                  '-'
               END) AS UW_STATUS,
           (
          CASE WHEN CHAKAN1 IS NULL THEN '-'
          ELSE CHAKAN1
          END
          ) AS CHAKAN,
          (CASE
                 WHEN AUDIT_PERMISSION_NAME1 IS NULL THEN
                  '-'
                 ELSE
                  AUDIT_PERMISSION_NAME1
               END) AS AUDIT_PERMISSION_NAME,
               (CASE
                 WHEN APPROVE_PERMISSION_NAME1 IS NULL THEN
                  '-'
                 ELSE
                  APPROVE_PERMISSION_NAME1
               END) AS APPROVE_PERMISSION_NAME,
          B.*,
          (CASE WHEN B.PAY_COUNT<=0 THEN null WHEN B.UN_PAY_COUNT>0 AND B.PAY_COUNT>0 THEN '未支付' WHEN B.UN_PAY_COUNT<=0 AND B.PAY_COUNT>0 THEN '已支付'  END) AS FEE_STATUS
          FROM (
                SELECT 
        ]]>
        <include refid="claimQueryMainField" />
         <![CDATA[ 
                  FROM DEV_CLM.T_CLAIM_CASE CC
                  left join DEV_CLM.T_CLAIM_ACCIDENT CA
                    on CC.ACCIDENT_ID = CA.ACCIDENT_ID   
                  left join DEV_PAS.T_CUSTOMER CM
                    ON CC.INSURED_ID = CM.CUSTOMER_ID
                 WHERE 1 = 1 AND CC.CASE_STATUS != '99'
         ]]>
            <!-- <if test=" employeeNo != null and employeeNo != ''  "><![CDATA[AND CC.CASE_NO=  #{employeeNo} ]]></if> -->
       <if test=" caseNo != null and caseNo != ''  "><![CDATA[AND CC.CASE_NO=  #{caseNo} ]]></if>
         <if test=" policyNo != null and policyNo != ''  ">
                 AND Exists(
                 SELECT CP.CASE_ID FROM DEV_CLM.T_CLAIM_POLICY CP WHERE CP.POLICY_CODE=#{policyNo} AND　CP.CASE_ID=CC.CASE_ID 
                 UNION 
                 SELECT CM.CASE_ID FROM DEV_CLM.T_CONTRACT_MASTER CM WHERE CM.POLICY_CODE=#{policyNo}
              AND CM.CUR_FLAG=1 AND CM.CASE_ID=CC.CASE_ID
                 ) 
          </if>
        <if test=" accDate != null and accDate != ''  "><![CDATA[AND TO_CHAR(CA.acc_date,'yyyy-MM-dd')= #{accDate} ]]></if>
        <if test=" outDate != null and outDate != ''  "><![CDATA[AND TO_CHAR(CA.ACC_DATE,'yyyy-MM-dd') = #{outDate} ]]></if>
        <if test=" outer != null and outer != ''  "><![CDATA[AND  CM.CUSTOMER_NAME = #{outer} ]]></if>
        <if test=" outerCutNo != null and outerCutNo != ''  "><![CDATA[AND CC.INSURED_ID = #{outerCutNo} ]]></if>
        <if test=" cardId != null and cardId != ''  "> AND CM.CUSTOMER_CERTI_CODE= #{cardId} </if>
        <if test=" recordOrg != null and recordOrg != ''  "><![CDATA[AND CC.ORGAN_CODE = #{recordOrg} ]]></if>
        <if test=" recordBeginDate != null and recordBeginDate != ''  "><![CDATA[AND TO_CHAR(CC.REGISTE_TIME,'yyyy-MM-dd') >= #{recordBeginDate} ]]></if>
        <if test=" recordEndDate != null and recordEndDate != ''  "><![CDATA[AND TO_CHAR(CC.REGISTE_TIME,'yyyy-MM-dd') <= #{recordEndDate} ]]></if>
        <if test=" endCaseDate1 != null and endCaseDate1 != ''  "><![CDATA[AND TO_CHAR(CC.END_CASE_TIME,'yyyy-MM-dd') >= #{endCaseDate1} ]]></if>
        <if test=" endCaseDate2 != null and endCaseDate2 != ''  "><![CDATA[AND TO_CHAR(CC.END_CASE_TIME,'yyyy-MM-dd') <= #{endCaseDate2} ]]></if>
     <![CDATA[ ) B WHERE ROWNUM <= #{LESS_NUM}  ]]> 
        <![CDATA[ )  C
        WHERE C.RN > #{GREATER_NUM} ]]>
    </select>
    
    <!-- 理赔综合查询-理赔查询 count -->
    <select id="queryClmInfoTotal" resultType="java.lang.Integer"
        parameterType="java.util.Map">
         <![CDATA[ 
      SELECT  COUNT(CC.CASE_NO)
                  FROM DEV_CLM.T_CLAIM_CASE CC
                  left join DEV_CLM.T_CLAIM_ACCIDENT CA
                    on CC.ACCIDENT_ID = CA.ACCIDENT_ID
                  left join DEV_PAS.T_CUSTOMER CM
                    ON CC.INSURED_ID = CM.CUSTOMER_ID
                 WHERE 1 = 1 AND CC.CASE_STATUS != '99'
              ]]>
            <!-- <if test=" employeeNo != null and employeeNo != ''  "><![CDATA[AND CC.CASE_NO=  #{employeeNo} ]]></if> -->
        <if test=" caseNo != null and caseNo != ''  "><![CDATA[AND CC.CASE_NO=  #{caseNo} ]]></if>
        <if test=" policyNo != null and policyNo != ''  ">
                 AND Exists(
                 SELECT CP.CASE_ID FROM DEV_CLM.T_CLAIM_POLICY CP WHERE CP.POLICY_CODE=#{policyNo} AND　CP.CASE_ID=CC.CASE_ID 
                 UNION 
                 SELECT CM.CASE_ID FROM DEV_CLM.T_CONTRACT_MASTER CM WHERE CM.POLICY_CODE=#{policyNo}
              AND CM.CUR_FLAG=1 AND CM.CASE_ID=CC.CASE_ID
                 ) 
        </if>
        <if test=" accDate != null and accDate != ''  "><![CDATA[AND TO_CHAR(CA.acc_date,'yyyy-MM-dd')= #{accDate} ]]></if>
        <if test=" outDate != null and outDate != ''  "><![CDATA[AND TO_CHAR(CA.ACC_DATE,'yyyy-MM-dd') = #{outDate} ]]></if>
        <if test=" outer != null and outer != ''  "><![CDATA[AND  CM.CUSTOMER_NAME = #{outer} ]]></if>
        <if test=" outerCutNo != null and outerCutNo != ''  "><![CDATA[AND CA.INSURED_ID = #{outerCutNo} ]]></if>
        <if test=" cardId != null and cardId != ''  "> AND CM.CUSTOMER_CERTI_CODE= #{cardId} </if>
        <if test=" recordOrg != null and recordOrg != ''  "><![CDATA[AND CC.ORGAN_CODE = #{recordOrg} ]]></if>
        <if test=" recordBeginDate != null and recordBeginDate != ''  "><![CDATA[AND TO_CHAR(CC.REGISTE_TIME,'yyyy-MM-dd') >= #{recordBeginDate} ]]></if>
        <if test=" recordEndDate != null and recordEndDate != ''  "><![CDATA[AND TO_CHAR(CC.REGISTE_TIME,'yyyy-MM-dd') <= #{recordEndDate} ]]></if>
        <if test=" endCaseDate1 != null and endCaseDate1 != ''  "><![CDATA[AND TO_CHAR(CC.END_CASE_TIME,'yyyy-MM-dd') >= #{endCaseDate1} ]]></if>
        <if test=" endCaseDate2 != null and endCaseDate2 != ''  "><![CDATA[AND TO_CHAR(CC.END_CASE_TIME,'yyyy-MM-dd') <= #{endCaseDate2} ]]></if>
     </select>
    
    <!-- 案件信息-报案人信息 等 -->
    <select id="queryCaseInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT AA.CASE_NO,
               AA.RPTR_RELATION,
               AA.RPTR_NAME,
               AA.RPTR_MP,
               AA.RPTR_ZIP,
               AA.RPTR_ADDR,
               AA.RPTR_EMAIL,
               AA.REPORT_MODE,
               (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.ACCEPTOR_ID) ACCEPTOR_ID,
               AA.CASE_APPLY_TYPE,
               AA.RPTR_TIME,
               AA.INSERT_TIME,
               AA.RPTR_ID,
               AA.ORGAN_CODE,
               AA.SIGN_ORGAN,
               AA.ACCIDENT_ID,
               AA.TRUSTEE_TYPE,
               AA.TRUSTEE_CODE,
               AA.TRUSTEE_NAME,
               AA.TRUSTEE_MP,
               AA.TRUSTEE_TEL,
               AA.TRUSTEE_CERTI_TYPE,
               AA.TRUSTEE_CERTI_CODE,
               AA.DOOR_SIGN_TIME,
               TC.CUSTOMER_NAME,
               TC.CUSTOMER_CERTI_CODE,
               TC.CUSTOMER_ID,
               TC.CUSTOMER_ID_CODE,
               TC.CUSTOMER_GENDER,
               TC.CUSTOMER_BIRTHDAY,
               CA.INSURED_ID,
               CA.ACC_PROVINCE,
               CA.ACC_CITY,
               CA.ACC_DISTREACT,
               CA.ACC_STREET,
               CA.ACC_REASON,
               CA.OTHER_DIE_REASON,
               CA.ACC_DATE,
               AA.SERIOUS_DISEASE,
               (SELECT AC.DISEASE_TYPE FROM APP___CLM__DBUSER.T_LA_TYPE AC WHERE AC.CODE=AA.SERIOUS_DISEASE) DISEASE_TYPE,
               AA.ACCIDENT_DETAIL,
               AA.CURE_HOSPITAL,
               AA.CURE_STATUS,
               CA.ACC_DESC,
               AA.ACCEPT_DECISION,
               AA.CASE_FLAG,
               AA.GREEN_FLAG,
               AA.APPLY_DATE,
               AA.ADVANCE_FLAG,
               AA.ACCEPT_TIME,
       		   IL.SOCI_SECU,
       		   AA.IS_MIGRATION,
               AA.CHANNEL_CODE,
               (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.SIGNER_ID) signerCode,
               (SELECT UU.REAL_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.SIGNER_ID) signerName,
               (SELECT UU.USER_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.REGISTER_ID) registerCode,
               (SELECT UU.REAL_NAME FROM DEV_PAS.T_UDMP_USER UU WHERE UU.USER_ID = AA.REGISTER_ID) registerName,
               AA.DIAGNOSIS_TIME,
               AA.INSURANCE_PAID_FLAG,
               (SELECT B.REPORT_SOURCE_NAME
          FROM DEV_CLM.T_REPORT_SOURCE B
         WHERE B.REPORT_SOURCE_CODE = AA.REPORT_SOURCE) AS REPORT_SOURCE,
       (SELECT C.TYPE_NAME
          FROM DEV_CLM.T_YES_NO C
         WHERE C.YES_NO = AA.IS_CALLED_BACK) AS IS_CALLED_BACK 
          FROM DEV_CLM.T_CLAIM_CASE  AA 
         INNER JOIN DEV_CLM.T_CLAIM_ACCIDENT CA 
           ON  AA.ACCIDENT_ID=CA.ACCIDENT_ID 
           AND AA.CASE_ID = #{caseNo} 
          LEFT JOIN DEV_CLM.T_CUSTOMER TC 
            ON CA.INSURED_ID = TC.CUSTOMER_ID
          LEFT JOIN DEV_CLM.T_INSURED_LIST IL
		    ON IL.CASE_ID = AA.CASE_ID
		   AND IL.CUR_FLAG = 0
		   AND IL.CUSTOMER_ID = TC.CUSTOMER_ID
         ]]>
    </select>
    <!-- 案件信息-报案人信息 等 -->
    <select id="queryCaseInfoRptrTime" resultType="java.util.Map" parameterType="java.util.Map">
	    SELECT A.RELATED_NO,A.INSERT_TIME,A.FIRST_RPTR_TIME
	        FROM APP___CLM__DBUSER.T_CLAIM_CASE A
	       START WITH
	       A.CASE_ID = #{caseNo} 
	      CONNECT BY PRIOR A.RELATED_NO = A.CASE_NO
	       ORDER BY A.INSERT_TIME ASC
     </select>
    <!-- 案件信息-出险结果 -->
    <select id="queryCaseResult" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
	      SELECT 
	      AR.ACC_RESULT1,
	      AR.ACC_RESULT2,
	      AR.ACC_RESULT3,
	      (SELECT T1.NAME FROM DEV_CLM.T_ACCIDENT1 T1 WHERE T1.CODE=AR.ACC_RESULT1) R1,
	      (SELECT T1.NAME FROM DEV_CLM.T_ACCIDENT2 T1 WHERE T1.CODE=AR.ACC_RESULT2 AND T1.RELA_CODE=AR.ACC_RESULT1) R22,
          AR.ACC_RESULT3_NAME,
          UU.USER_NAME  UPDATE_NAME   
      FROM DEV_CLM.T_CLAIM_ACCIDENT_RESULT AR  
      LEFT JOIN DEV_PAS.T_UDMP_USER UU ON AR.UPDATE_BY=UU.USER_ID
         WHERE 1=1 
         AND AR.CASE_ID=#{caseNo}
         ORDER BY AR.LIST_ID
         ]]>
    </select>
    <!-- 案件信息-理赔类型等 -->
    <select id="caseType" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ 
              SELECT 
              SC.CLAIM_TYPE,
              TO_CHAR(SC.CLAIM_DATE,'yyyy-MM-dd') CLAIM_DATE,
              TO_CHAR(SC.CLAIM_DATE,'yyyy')-TO_CHAR(C.CUSTOMER_BIRTHDAY,'yyyy') AGE
              FROM DEV_CLM.T_CLAIM_SUB_CASE SC 
          LEFT JOIN DEV_CLM.T_CLAIM_CASE CC
                ON  SC.CASE_ID=CC.CASE_ID
          LEFT JOIN DEV_CLM.T_CUSTOMER C
                 ON CC.INSURED_ID=C.CUSTOMER_ID
          WHERE 1 = 1 
          AND SC.CASE_ID=#{caseNo}
         ]]>
    </select>
    <!-- 案件信息-申请人信息 -->
    <select id="queryAppList" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
            SELECT
            A.CLMT_NAME,
            A.CLMT_CERTI_NO,
            A.CLMT_MP,
            A.CLMT_PROVINCE,
            A.CLMT_CITY,
            A.CLMT_DISTREACT,
            A.CLMT_STREET
        FROM DEV_CLM.T_CLAIM_APPLICANT A 
        WHERE 1 = 1 
        AND A.CASE_ID=#{caseNo}
         ]]>
    </select>
    
    <!-- 案件信息-医生信息 -->
 <!--add by xuyz 通过 case_id 查询所有操作 -->
    <select id="queryClaimDoctor" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.DOCTOR_NAME, A.DOCTOR_ID, A.DIAG_TIME, 
                    A.CODE, A.CASE_ID,TUU.USER_NAME  UPDATE_NAME ,A.DIAGNOSIS_CODE,A.DIAGNOSIS_DESC,A.TCM_FLAG,A.MEDICAL_TYPE    
                FROM DEV_CLM.T_CLAIM_DOCTOR A , DEV_PAS.T_UDMP_USER tuu
      WHERE TUU.USER_ID = A.UPDATE_BY ]]>
        <if test=" caseNo  != null "><![CDATA[ AND A.CASE_ID = #{caseNo} ]]></if>
        <if test=" caseID  != null "><![CDATA[ AND A.CASE_ID = #{caseID} ]]></if>
        <![CDATA[ ORDER BY A.DOCTOR_ID ]]> 
    </select>

	<!-- 案件信息-鉴定信息 -->
	<select id="queryClaimIdentifyInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SOCIOL_CREDIT_CODE, A.ENTRUSTING_APPRAISAL_MATTER, A.LIENT, A.UNIT_CERTI_CODE, A.ACCREDITING_BODY_NAME, 
			A.CASE_ID, A.END_UPDATE_BY, A.ACCEPTANCE_DATE, A.APPRAISAL_REPORT_DATE, A.APPRAISAL_DATE, 
			A.LIST_ID, A.APPRAISER_NAME,TUU.USER_NAME as END_UPDATE_BY_NAME  FROM APP___CLM__DBUSER.T_CLAIM_IDENTIFY_INFO A , DEV_PAS.T_UDMP_USER tuu WHERE  TUU.USER_ID = A.END_UPDATE_BY ]]>
			<if test=" caseNo  != null "><![CDATA[ AND A.CASE_ID = #{caseNo} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
    <!-- 调查 协谈 二核 -->

    <!-- -调查 协谈 二核 查看调查 -->
    <select id="queryDCList" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
          SELECT SA.CASE_ID, 
                 SA.SURVEY_CODE,
                 SA.APPLY_SECTION,
                 (SELECT ST.NAME FROM DEV_CLM.T_SURVEY_TYPE ST WHERE ST.CODE=SA.SURVEY_TYPE) SURVEY_TYPE,
                 SA.SURVEY_REASON,
                 SA.SURVEY_ORG,
                 SA.SURVEY_STATUS,
                 SCC.POSITIVE_FLAG,
                 SCC.FINISH_DATE,
                 SA.BIZ_TYPE,
			     SA.APPLY_ID,
			     SA.SURVEY_DOC_ID,
			     SA.CASE_NO,
			     SCC.PRIORITY_CLAIM
            FROM DEV_CLM.T_SURVEY_APPLY SA
           LEFT JOIN DEV_CLM.T_SURVEY_CONCLUSION SCC
              ON SA.APPLY_ID = SCC.APPLY_ID
           WHERE 1=1 
             AND SA.CASE_ID =#{caseNo}
         ]]>
    </select>
    
    <!-- -调查 协谈 二核-查看不如实二核 -->
    <select id="queryNOList" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
         SELECT C.*,
       NVL(HOLDER_CUSTOMER_CLM, HOLDER_CUSTOMER_PAS) AS HOLDER_NAME,
       NVL(NEW_ORGAN_CODE, OLD_ORGAN_CODE) AS ORGAN_CODE
  	FROM (SELECT DISTINCT CU.CASE_ID,
                        CU.POLICY_CODE,
                        CU.CASE_NO,
                        (SELECT TCM.VALIDDATE_DATE 
                           FROM  DEV_CLM.T_CONTRACT_MASTER TCM
                           WHERE TCM.CASE_ID = TCC.CASE_ID 
                           AND TCM.CUR_FLAG = '1' 
                           AND TCM.POLICY_CODE = CU.POLICY_CODE) VALIDDATE_DATE,/*保单生效日期 */
                        (SELECT C.CUSTOMER_NAME
                           FROM DEV_CLM.T_POLICY_HOLDER PH
                           INNER JOIN DEV_PAS.T_CUSTOMER C
                           ON ph.customer_id = c.customer_id
                          WHERE PH.CASE_ID = CU.CASE_ID
                            AND PH.POLICY_CODE = CU.POLICY_CODE
                            AND PH.CUR_FLAG = 1
                            AND ROWNUM = 1) AS HOLDER_CUSTOMER_CLM, /*投保人姓名 */
                        (SELECT C.CUSTOMER_NAME
                           FROM DEV_PAS.T_POLICY_HOLDER PH
                           INNER JOIN DEV_PAS.T_CUSTOMER C
                           ON ph.customer_id = c.customer_id
                          WHERE PH.POLICY_CODE = CU.POLICY_CODE) AS HOLDER_CUSTOMER_PAS, /*投保人姓名 */
                        (SELECT listagg(C.CUSTOMER_NAME,',') WITHIN GROUP(ORDER BY c.customer_id)
                           FROM DEV_PAS.T_CUSTOMER C
                           WHERE TCC.INSURED_ID = c.customer_id) AS INSURED_NAME, /*被保人姓名 */
                        (SELECT PCM.ORGAN_CODE
                           FROM DEV_PAS.T_CONTRACT_MASTER PCM
                          WHERE PCM.POLICY_CODE = CU.POLICY_CODE) AS OLD_ORGAN_CODE,
                        (SELECT CCM.ORGAN_CODE
                           FROM DEV_CLM.T_CONTRACT_MASTER CCM
                          WHERE CCM.POLICY_CODE = CU.POLICY_CODE
                            AND CCM.CUR_FLAG = 1
                            AND CCM.CASE_ID = CU.CASE_ID) AS NEW_ORGAN_CODE,
                        (SELECT DISTINCT B.PRODUCT_NAME_SYS
                         FROM DEV_PAS.T_CONTRACT_BUSI_PROD A,
                              DEV_PAS.T_BUSINESS_PRODUCT B
	                       WHERE A.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
	                       AND A.BUSI_PROD_CODE = B.PRODUCT_CODE_SYS) AS PRODUCTABBR_NAME,  /*该被保险人的险种*/
	                    BI.BUSI_ITEM_ID,   
                        CU.CLAIM_UW_TYPE,
                        CU.UW_STATUS,
                        TCC.CASE_FLAG
          FROM DEV_CLM.T_CLAIM_UW CU
          LEFT JOIN DEV_CLM.T_UW_POLICY UP
            ON CU.UW_POLICY_ID = UP.UW_POLICY_ID
          LEFT JOIN DEV_CLM.T_CLAIM_CASE TCC
            ON TCC.CASE_ID = CU.CASE_ID
          LEFT JOIN DEV_CLM.T_INSURED_LIST IL
            ON IL.CASE_ID = TCC.CASE_ID
           AND TCC.INSURED_ID = IL.CUSTOMER_ID
          LEFT JOIN DEV_CLM.T_BENEFIT_INSURED BI
            ON BI.INSURED_ID = IL.LIST_ID
           AND BI.CUR_FLAG = IL.CUR_FLAG 
          WHERE IL.CUR_FLAG = '1'
           AND CU.POLICY_ID = BI.POLICY_ID
           AND CU.CASE_ID=#{caseNo}
           --AND CU.NOT_INFORM_SITUATION IS NOT NULL
           ) C
         ]]>
    </select>

	<select id="queryNOListts" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[
         SELECT C.*,
       NVL(HOLDER_CUSTOMER_CLM, HOLDER_CUSTOMER_PAS) AS HOLDER_NAME,
       NVL(INSURED_CUSTOMER_CLM, INSURED_CUSTOMER_PAS) AS INSURED_NAME,
       NVL(NEW_ORGAN_CODE, OLD_ORGAN_CODE) AS ORGAN_CODE
  FROM (SELECT DISTINCT CU.CASE_ID,
                        CU.POLICY_CODE,
                        CU.CASE_NO,
                        (SELECT TCM.VALIDDATE_DATE 
                           FROM  DEV_CLM.T_CONTRACT_MASTER TCM
                           WHERE TCM.CASE_ID = TCC.CASE_ID 
                           AND TCM.CUR_FLAG = '1' 
                           AND TCM.POLICY_CODE = CU.POLICY_CODE) VALIDDATE_DATE,/*保单生效日期 */
                        (SELECT C.CUSTOMER_NAME
                           FROM DEV_CLM.T_POLICY_HOLDER PH
                           INNER JOIN DEV_PAS.T_CUSTOMER C
                           ON ph.customer_id = c.customer_id
                          WHERE PH.CASE_ID = CU.CASE_ID
                            AND PH.POLICY_CODE = CU.POLICY_CODE
                            AND PH.CUR_FLAG = 1
                            AND ROWNUM = 1) AS HOLDER_CUSTOMER_CLM, /*投保人姓名 */
                        (SELECT listagg(C.CUSTOMER_NAME,',') WITHIN GROUP(ORDER BY c.customer_id)
                           FROM DEV_CLM.T_INSURED_LIST IL
                           INNER JOIN DEV_PAS.T_CUSTOMER C
                           ON il.customer_id = c.customer_id
                          WHERE IL.CASE_ID = CU.CASE_ID
                            AND IL.POLICY_CODE = CU.POLICY_CODE
                            AND IL.CUR_FLAG = 1) AS INSURED_CUSTOMER_CLM, /*被保人姓名 */
                        (SELECT C.CUSTOMER_NAME
                           FROM DEV_PAS.T_POLICY_HOLDER PH
                           INNER JOIN DEV_PAS.T_CUSTOMER C
                           ON ph.customer_id = c.customer_id
                          WHERE PH.POLICY_CODE = CU.POLICY_CODE) AS HOLDER_CUSTOMER_PAS, /*投保人姓名 */
                        (SELECT listagg(C.CUSTOMER_NAME,',') WITHIN GROUP(ORDER BY c.customer_id)
                           FROM DEV_PAS.T_INSURED_LIST IL
                           INNER JOIN DEV_PAS.T_CUSTOMER C
                           ON il.customer_id = c.customer_id
                          WHERE IL.POLICY_CODE = CU.POLICY_CODE) AS INSURED_CUSTOMER_PAS, /*被保人姓名 */
                        (SELECT PCM.ORGAN_CODE
                           FROM DEV_PAS.T_CONTRACT_MASTER PCM
                          WHERE PCM.POLICY_CODE = CU.POLICY_CODE) AS OLD_ORGAN_CODE,
                        (SELECT CCM.ORGAN_CODE
                           FROM DEV_CLM.T_CONTRACT_MASTER CCM
                          WHERE CCM.POLICY_CODE = CU.POLICY_CODE
                            AND CCM.CUR_FLAG = 1
                            AND CCM.CASE_ID = CU.CASE_ID) AS NEW_ORGAN_CODE,
                        CU.CLAIM_UW_TYPE,
                        CU.UW_STATUS,
                        TCC.CASE_FLAG
          FROM DEV_CLM.T_CLAIM_UW CU
          LEFT JOIN DEV_CLM.T_UW_POLICY UP
            ON CU.UW_POLICY_ID = UP.UW_POLICY_ID
          LEFT JOIN DEV_CLM.T_CLAIM_CASE TCC
            ON TCC.CASE_ID = CU.CASE_ID
         WHERE 1 = 1
           AND CU.CASE_ID=#{caseNo}
           --AND CU.NOT_INFORM_SITUATION IS NOT NULL
           ) C 
		 ]]>
	</select>
    <!-- -调查 协谈 二核 查看协谈 -->
    <select id="queryXTList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
          SELECT CT.CASE_ID,
          TC.CUSTOMER_NAME,
          (SELECT TC.CUSTOMER_NAME
             FROM DEV_PAS.T_CUSTOMER TC
            WHERE TC.CUSTOMER_ID = CT.TREATY_BY1) || ',' ||
          (SELECT TC.CUSTOMER_NAME
             FROM DEV_PAS.T_CUSTOMER TC
            WHERE TC.CUSTOMER_ID = CT.TREATY_BY2) BYS,
          
          CT.TREATY_STATUS,
          CT.TREATY_DESC,
          CT.TREATY_CONCLUSION,
          CT.ORGAN_CONCLUSION,
          CT.ORGAN_CODE,
          CT.Treaty_Id
     FROM DEV_CLM.T_CLAIM_TREATY_TALK CT
   
     LEFT JOIN DEV_PAS.T_CUSTOMER TC
       ON TC.CUSTOMER_ID IN (CT.TREATY_BY1, CT.TREATY_BY2)
                  WHERE CT.CASE_ID=#{caseNo}
                 ]]>
    </select>

    
    <!-- -调查 协谈 合议 -->
    <select id="queryHYList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                select D.DISCUSS_ID,
       D.CASE_ID,/*--赔案ID*/
       D.CASE_NO,/*--赔案号*/
       D.APPLY_TIME,/*--合议发起时间*/
       D.APPLY_BY,/*--合议发起人*/
       D.ASSIGN_CONCLUSION,/*--任务分配结论 */
       D.EXPECT_RETURN_TIME,/*--期望回复时间*/
       D.DISCUSS_CONTENT,/*--合议内容*/
       D.DISCUSS_MANAGE,/*--合议主管*/
       D.DISCUSS_CONCLUSION,/*--合议最终结论*/
       D.DISCUSS_DESC,/*--合议最终结论描述*/
       D.DISCUSS_STATUS,/*--合议状态*/
       D.DISCUSS_BACK_TIME,/*--合议最终结论回复时间*/
       (select count(*) from dev_clm.t_claim_Sub_Discuss SD  WHERE SD.Discuss_Id=D.Discuss_Id) AS Allot_Number,
       (select count(*) from dev_clm.t_claim_Sub_Discuss SD  WHERE SD.Discuss_Id=D.Discuss_Id AND  SD.SUB_DISCUSS_STATUS='1' ) AS Reply_Number
                FROM DEV_CLM.T_CLAIM_DISCUSS D 
                    WHERE D.CASE_ID=#{caseNo}
                 ]]>
    </select>


    <!-- 责任责任责任责任责任明细信息 -->
    <!-- 责任明细信息 门诊住院1 -->
    <select id="queryMZZYList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
          SELECT 
              CB.BILL_TYPE,
              CB.CASE_ID,
              CB.BILL_ID,
              CB.BILL_NO,
              CB.MEDICAL_DEPT_CODE,
	      CB.HOSPITAL_CODE,
              (SELECT H.HOSPITAL_NAME FROM DEV_CLM.T_HOSPITAL  H WHERE H.HOSPITAL_CODE= CB.HOSPITAL_CODE)HOSPITAL_NAME,
              DECODE(CB.TREAT_TYPE,'0','门诊','1','住院') TREAT_TYPE,
              TO_CHAR(CB.TREAT_START,'yyyy-MM-dd')TREAT_START,
              TO_CHAR(CB.TREAT_END,'yyyy-MM-dd')TREAT_END,
              CB.TREAT_END-CB.TREAT_START ZYTS,
              CB.TREAT_END-(SELECT CAA.ACC_DATE 
         FROM DEV_CLM.T_CLAIM_ACCIDENT CAA,
             DEV_CLM.T_CLAIM_CASE CC 
         WHERE CAA.ACCIDENT_ID=CC.ACCIDENT_ID 
            AND CC.CASE_ID=CB.CASE_ID) 
            SFTS,CB.TREAT_END-(SELECT CSC.CLAIM_DATE FROM (SELECT * FROM DEV_CLM.T_CLAIM_SUB_CASE  ORDER BY INSERT_TIME DESC) 
            CSC  WHERE CSC.CASE_ID=CB.CASE_ID  AND ROWNUM=1)
            CXTS,
            (SELECT NAME FROM DEV_CLM.T_ACCIDENT1 WHERE CODE=CB.ACC_DETAIL)ACC_DETAIL,
            CB.ICD_CODE,
            CB.SUM_AMOUNT,
            CB.DEDUCT_AMOUNT,
            (SELECT NVL(SUM(B.CALC_AMOUNT), '0') FROM APP___CLM__DBUSER.T_CLAIM_BILL_ITEM B
          		WHERE EXISTS (SELECT 1 FROM APP___CLM__DBUSER.T_CLAIM_LIAB_BILL_RELATION CLBR 
                           		WHERE CLBR.BILL_ITEM_ID = B.BILL_ITEM_ID
                                  AND CLBR.CASE_ID = B.CASE_ID
                             	  AND B.BILL_ID = CB.BILL_ID)
            	  AND B.CASE_ID = CB.CASE_ID) CALC_AMOUNT,
            CB.OTHER_FLAG,
            CB.OTHER_PAY,
			CB.THIRD_PAY,
			CB.MEDICAL_PAY,
			CB.APP_LIAB,
			CB.LIAB_TYPE,
			CB.IS_SPECIAL_INTERNATIONAL,
			CB.ELEC_BILL_CHECK,CB.NON_REAL_BILL,CB.VERIFY_RESULT,CB.VERIFY_RESULT_DESC,CB.ELEC_BILL_ID,CB.ELEC_BILL_NUM,CB.CHECK_CODE,CB.ELEC_BILL_AMOUNT,CB.ELEC_BILL_DATE,CB.ENTRY_AMOUNT
        FROM DEV_CLM.T_CLAIM_BILL CB,
            DEV_CLM.T_CLAIM_CASE CC 
        WHERE CC.CASE_ID=CB.CASE_ID 
            AND CB.CASE_ID=#{caseNo}
                 ]]>
    </select>

    <!-- 责任明细信息 费用明细2,T_CLAIM_BILL TC WHERE CB.BILL_ID=TC.BILL_ID -->
    <select id="queryFYMXList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
              SELECT CB.CASE_ID,
       CB.BILL_ID,
       CB.MED_FEE_ITEM,
       CB.FEE_AMOUNT,
       CB.DEDUCT_AMOUNT,
       CB.CALC_AMOUNT,
       CB.DEDUCT_REASON,
       CB.DEDUCT_REMARK
  FROM DEV_CLM.T_CLAIM_BILL_ITEM CB,DEV_CLM.T_CLAIM_BILL TC WHERE CB.BILL_ID=TC.BILL_ID
         AND  CB.BILL_ID = #{caseNo}
                 ]]>
    </select>
	<!-- 责任明细信息 费用明细2,T_CLAIM_BILL TC WHERE CB.BILL_ID=TC.BILL_ID -->
	<select id="queryZLFYMXList" resultType="java.util.Map"
		parameterType="java.util.Map">
              <![CDATA[ 
		      SELECT A.PRICE, A.MEDICAL_NUM, A.SELF_PAY_RATIO, A.BILL_NUM, A.TOTAL, A.CURRENT_PAGE, 
			A.MED_FEE_ITEM, A.RECIPE_SERIAL_NUM, A.LIST_ID, A.BILL_NO, A.QUANTITY, A.MED_FEE_NAME, 
			A.CHARGE_LEVEL, A.LIST_CAT, A.MED_LIMITED_PRICE, A.RECIPE_DATE, A.CASE_ID, 
			A.HOSPITAL_CHARGE_NAME, A.MONEY, A.HOSPITAL_CODE, A.HOSPITAL_CHARGE_CODE 
			,A.FEE_ITEM_SOURCE,A.ITEM_SPECIFICATION,A.DOSAGE_FORM
  FROM DEV_CLM.T_CLAIM_BILL_ITEM_DETAIL A,DEV_CLM.T_CLAIM_BILL TC WHERE A.BILL_NO=TC.BILL_NO
  		 AND A.CASE_ID = TC.CASE_ID
		 AND  A.CASE_ID = #{caseNo}
    			 ]]>
	</select>

    <!-- 责任明细信息 伤残录入信息3 -->
    <select id="querySCLRList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                  SELECT 
                      CI.DEFORMITY_TYPE,
                      CI.DEFORMITY_GRADE,
                      CI.INJURY_CODE1,
                      CI.INJURY_CODE2 INCODE,
                      CI.PART_DEGREE,
                      CI.PAY_RATE,
                      CI.ASSESS_ORGAN,
                      CI.ASSESS_DATE,
                      CI.REMARK 
                  FROM DEV_CLM.T_CLAIM_INJURY CI 
                  WHERE CI.CASE_ID=#{caseNo}
                 ]]>
    </select>
    <!-- 责任明细信息 特定手术/疾病/给付信息录入4 -->
    <select id="queryTDJFList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                 SELECT 
                     CSS.SURGERY_TYPE,
                     CSS.SURGERY_CODE,
                     CSS.SUM_AMOUNT,
                     CSS.MED_ORG_NAME,
                     CSS.DIAGNOSE_DATE 
                FROM DEV_CLM.T_CLAIM_SURGERY CSS 
                     WHERE CSS.CASE_ID=#{caseNo}
                 ]]>
    </select>
    <!-- 责任明细信息 特种费用录入信息5 -->
    <select id="queryTZFYList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                  SELECT
                  	   CM.SPECIAL_TYPE,
                       CM.SPECIAL_CODE,
                       CM.SUM_AMOUNT,
                       CM.SERVICE_ORG_NAME,
                       CM.FEE_START,
                       CM.FEE_END 
                  FROM DEV_CLM.T_CLAIM_SPECIAL CM 
                  WHERE CM.CASE_ID=#{caseNo}
                 ]]>
    </select>
    <!-- 责任明细信息 社保第三方给付信息录入6 1社会给付 2第三方给付 -->
    <select id="querySFJFList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
			        SELECT CB.LIST_ID,CB.OTHER_TYPE,
			       CB.OTH_FEE_ITEM,
			       CB.FEE_CODE,
			       CB.PAID_TYPE,
			       CB.DATA_ID,
			       CB.SUM_AMOUNT,
			       CB.MEDICAL_FUND_PAYMENT,
			       CB.SERVICE_ORG_NAME,
			       CB.SELF_PAY1,
			       CB.SELF_PAY2,
			       CB.OWN_EXPENSE,
			       CB.REMARK,
			       CB.PAID_AMOUNT,
			       CB.ACCOUNT_FUND_MONEY
			  FROM DEV_CLM.T_CLAIM_BILL_PAID CB
			 WHERE CB.CASE_ID = #{caseNo}
			   AND (CB.OTHER_TYPE = 1 OR CB.OTHER_TYPE = 2)]]>
	</select>
	<!-- 责任明细信息 -职业给付系数录入信息7 1职业给付系数 -->
	<select id="queryZYJFList" resultType="java.util.Map"
		parameterType="java.util.Map">
              <![CDATA[ 
                              SELECT
          CO.OCCUPATION_RATE,
          CO.OCCUPATION_REMARK,
                CO.JOB_CODE,
                CO.JOB_CATEGORY,
            (select b.claim_code||'-'||c.job_name
                  from APP___CLM__DBUSER.T_JOB_CODE c
                WHERE b.claim_code = c.job_code) as oldJobCode,
            (select e.job_category_name
                from APP___CLM__DBUSER.T_JOB_CODE c, DEV_CLM.t_Job_Category e
              WHERE b.claim_code = c.job_code
                and c.job_category = e.job_category_code) as oldJobCategoryName,
            (select c.job_uw_level
                from APP___CLM__DBUSER.T_JOB_CODE c
              WHERE c.job_code = b.claim_code) as oldJobUwLevel
      FROM DEV_CLM.T_CLAIM_OCCUPATION_RATE CO,
              APP___CLM__DBUSER.T_JOB_CODE            A 
              left join 
              APP___CLM__DBUSER.T_CLAIM_CHANGE_CONFIG b 
              on  a.job_code = b.change_code
        WHERE CO.CASE_ID = #{caseNo}
          and a.job_code=co.job_code
 ]]>
    </select>
    <!-- CO.OCCUPATION_CODE,CO.OCCUPATION_TYPE, -->
    <!-- 责任明细信息 一般失能信息录入CD.DISABILITY_TYPE=0是一般失能 页面区分 一次查询8 -->
    <!-- 重度失能 -->
    <select id="queryMUSList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
           
                    SELECT  CD.CASE_ID,
                       CD.DISABILITY_TYPE, 
                       TO_CHAR(CD.DISABILITY_GRADE) DISABILITY_GRADE,
                       CD.DISABILITY_DETAIL,
                       CD.PAY_RATE
                  FROM DEV_CLM.T_CLAIM_DISABILITY CD 
                  WHERE CD.CASE_ID =#{caseNo}
                   AND CD.DISABILITY_TYPE='1'
                   AND CD.DISABILITY_GRADE LIKE 'MUS%'
  ]]>   
    </select>
    <select id="queryALDList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
           
                    SELECT  CD.CASE_ID,
                       CD.DISABILITY_TYPE, 
                       TO_CHAR(CD.DISABILITY_GRADE) DISABILITY_GRADE,
                       CD.DISABILITY_DETAIL,
                       CD.PAY_RATE
                  FROM DEV_CLM.T_CLAIM_DISABILITY CD 
                  WHERE CD.CASE_ID =#{caseNo}
                   AND CD.DISABILITY_TYPE='1'
                   AND CD.DISABILITY_GRADE LIKE 'ADL%'
  ]]>   
    </select>
    <!--  一般失能信息录入 -->
     <select id="querySNList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
              SELECT CD.DISABILITY_TYPE,
                   CD.DISABILITY_GRADE,
                   CD.DISABILITY_DETAIL,
                   CD.PAY_RATE
              FROM DEV_CLM.T_CLAIM_DISABILITY CD
             WHERE CD.CASE_ID = #{caseNo}
               AND (CD.DISABILITY_TYPE = 0
                OR CD.DISABILITY_TYPE = 2)

  ]]>
    </select>

    <!-- 责任明细信息 年龄误告处理 9 -->
    <select id="queryNlwgInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                  SELECT 
                         CA.INSU_BIRTH,
                         CA.INSU_REAL_BIRTH
                  FROM DEV_CLM.T_CLAIM_ADJUST_AGE CA 
                  WHERE CA.CASE_ID=#{caseNo} ]]>
    </select>

    <!-- 豁免处理 -->
    <!--豁免处理 豁免列表 -->
    <select id="queryHuomList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
            SELECT CBP.CASE_ID,
        CBP.CLAIM_BUSI_PROD_ID,
        CBP.POLICY_CODE,
        CBP.BUSI_ITEM_ID,                                                                     
        CBP.BUSI_PROD_CODE,
        BP.PRODUCT_NAME_SYS,
        CBP.LIABILITY_STATUS,
        CBP.VALID_DATE,
        (SELECT  TO_CHAR(CP.PAIDUP_DATE,'yyyy-MM-dd')
          FROM ((SELECT *
                   FROM DEV_CLM.T_CONTRACT_PRODUCT CP
                  ORDER BY CP.BUSI_ITEM_ID, CP.PAIDUP_DATE DESC) CP)
         WHERE CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
           AND ROWNUM = 1) AS PAIDUP_DATE
        FROM 
        DEV_CLM.T_CLAIM_BUSI_PROD CBP
        LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
        ON 
          CBP.BUSI_PROD_CODE=BP.PRODUCT_CODE_SYS
        WHERE 1=1
          AND  CBP.CASE_ID=#{caseNo} ]]>
    </select>
    <!--豁免处理 豁免详细信息 -->
    <select id="queryHuomDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
       SELECT CBP.VALID_DATE,
       (SELECT max(CP.PAIDUP_DATE) PAIDUP_DATE
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         WHERE CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS PAIDUP_DATE,
       (SELECT sum(CP.STD_PREM_AF + CP.EXTRA_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         WHERE CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS STD_PREM_AF,
       (SELECT sum(CP.TOTAL_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         WHERE CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS TOTAL_PREM_AF,
       CCP.IS_WAIVED,
       CCP.WAIVE_START,
       CCP.WAIVE_END,
       CCP.WAIVE_REASON,
       0 AS WAIVE_COUNT,
       CCP.WAIVE_AMT,
       CBP.WAIVE_DESC,
       CP.PREM_FREQ,
       CCP.BUSI_ITEM_ID,
       CE. POLICY_PERIOD,
       CE.PAY_DUE_DATE,
       CP.Charge_Year
  FROM DEV_CLM.T_CLAIM_BUSI_PROD CBP
  INNER JOIN DEV_CLM.T_CLAIM_PRODUCT CCP
    ON CBP.BUSI_ITEM_ID=CCP.BUSI_ITEM_ID
   AND CBP.POLICY_CODE=CCP.POLICY_CODE
   AND CBP.CASE_ID=CCP.CASE_ID
  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
    ON CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
   and cbp.policy_code = cp.policy_code
  LEFT JOIN DEV_PAS.T_CONTRACT_EXTEND CE
    ON CE.Busi_Item_Id = CP.BUSI_ITEM_ID
   and cp.policy_code = CE.policy_code
  left join dev_clm.t_claim_sub_case sc
    on sc.case_id = cbp.case_id
 WHERE 1 = 1 AND CBP.CLAIM_BUSI_PROD_ID=#{busiprdid}]]>
    </select>
    
    <!--豁免处理 豁免详细信息 -->
    <select id="queryHuomDetailes" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
       SELECT CBP.VALID_DATE,
       (SELECT max(CP.PAIDUP_DATE) PAIDUP_DATE
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         WHERE CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS PAIDUP_DATE,
       (SELECT sum(CP.STD_PREM_AF + CP.EXTRA_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         WHERE CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS STD_PREM_AF,
       (SELECT sum(CP.TOTAL_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         WHERE CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID) AS TOTAL_PREM_AF,
       CCP.IS_WAIVED,
       CCP.WAIVE_START,
       CCP.WAIVE_END,
       CCP.WAIVE_REASON,
       0 AS WAIVE_COUNT,
       (SELECT SUM(CCP.WAIVE_AMT)
        FROM DEV_CLM.T_CLAIM_PRODUCT CCP
        WHERE CBP.POLICY_CODE=CCP.POLICY_CODE
        AND CBP.CASE_ID = CCP.CASE_ID) AS WAIVE_AMT,
       CBP.WAIVE_DESC,
       CP.PREM_FREQ,
       CCP.BUSI_ITEM_ID,
       CE. POLICY_PERIOD,
       CE.PAY_DUE_DATE,
       CP.Charge_Year
  FROM DEV_CLM.T_CLAIM_BUSI_PROD CBP
  INNER JOIN DEV_CLM.T_CLAIM_PRODUCT CCP
    ON CBP.BUSI_ITEM_ID=CCP.BUSI_ITEM_ID
   AND CBP.POLICY_CODE=CCP.POLICY_CODE
   AND CBP.CASE_ID=CCP.CASE_ID
   AND CCP.IS_WAIVED = '1'
  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
    ON CP.BUSI_ITEM_ID = CBP.BUSI_ITEM_ID
   and cbp.policy_code = cp.policy_code
  LEFT JOIN DEV_PAS.T_CONTRACT_EXTEND CE
    ON CE.Busi_Item_Id = CP.BUSI_ITEM_ID
   and cp.policy_code = CE.policy_code
  left join dev_clm.t_claim_sub_case sc
    on sc.case_id = cbp.case_id
 WHERE 1 = 1 AND CBP.POLICY_CODE=#{policy_code}
 	AND CBP.CASE_ID=#{case_id}
 	]]>
    </select>
    
    <!-- 理赔详细信息_合同处理 -->
    <select id="queryPaInfoListss" resultType="java.util.Map"
		parameterType="java.util.Map">
	   <![CDATA[ 
	   		SELECT  A.LIST_ID, A.CASE_ID, A.POLICY_ID, A.POLICY_CODE, A.CALC_PAY, A.ACTUAL_PAY, A.BASIC_PAY, A.ORGAN_CODE, A.LIABILITY_STATUS, A.EXPIRY_DATE, A.DEAL_CONCLUSION, A.INSERT_BY, A.INSERT_TIME, A.INSERT_TIMESTAMP, A.UPDATE_BY, A.UPDATE_TIME, A.UPDATE_TIMESTAMP,A.END_CAUSE,A.VALIDDATE_DATE,A.EXPIRY_WAY FROM APP___CLM__DBUSER.T_CLAIM_POLICY A WHERE 1 = 1 AND CASE_ID= #{caseNo}
	    ]]>
	</select>
    <!--理赔详细信息_合同处理 涉案保单信息 -->
    <select id="queryPaInfoListsa" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
             SELECT DISTINCT CP.POLICY_CODE,
       CP.LIABILITY_STATUS,
       CP.EXPIRY_DATE,
       CP.DEAL_CONCLUSION,
       UP.POLICY_DECISION,
       PD.DECISION_DESC as UW_CONCLUSION,
       TT.NAME,
       CU.CUSTOMER_NAME AS INSURED_NAME,
       CASE WHEN CP.EXPIRY_WAY=1 THEN '手动' WHEN CP.EXPIRY_WAY=0 THEN '自动' ELSE null end as EXPIRY_WAY_STR
  FROM DEV_CLM.T_CLAIM_POLICY CP
  LEFT JOIN DEV_CLM.T_UW_POLICY UP
    ON UP.POLICY_ID = CP.POLICY_ID
 LEFT JOIN DEV_CLM.T_UW_BUSI_PROD UBP
    ON UBP.POLICY_ID = UP.POLICY_ID
  LEFT JOIN DEV_CLM.T_BENEFIT_INSURED BI
    ON UBP.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
  LEFT JOIN DEV_CLM.T_INSURED_LIST IL
    ON BI.INSURED_ID = IL.LIST_ID
  LEFT JOIN DEV_CLM.T_CUSTOMER CU
    ON IL.CUSTOMER_ID = CU.CUSTOMER_ID 
 LEFT JOIN DEV_CLM.T_POLICY_DECISION PD
    ON PD.DECISION_CODE = UP.POLICY_DECISION
 LEFT JOIN DEV_CLM.T_TERMINATION TT
    ON TT.CODE = CP.DEAL_CONCLUSION
 WHERE CP.CASE_ID = #{caseNo} ORDER BY CP.POLICY_CODE ]]>
    </select>
    <!--理赔详细信息_合同处理 非涉案保单信息 -->
    <select id="queryPaInfoList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
             SELECT DISTINCT
          CM.POLICY_ID,
         CM.POLICY_CODE,
         CM.LIABILITY_STATE,
         CU.UW_CONCLUSION,
         CM.EXPIRY_DATE,
         NULL 
         FROM  DEV_CLM.T_CONTRACT_MASTER CM 
            LEFT JOIN  DEV_CLM.T_CLAIM_UW CU ON CM.CASE_ID=CU.CASE_ID AND CM.POLICY_ID=CU.POLICY_ID
            WHERE 1=1 AND CM.CUR_FLAG= '1' AND CM.CASE_ID=#{caseNo}  AND CM.POLICY_ID NOT IN (
       SELECT 
          DISTINCT
          CM.POLICY_ID
            FROM 
               DEV_CLM.T_CONTRACT_MASTER CM
            INNER JOIN
               DEV_CLM.T_CLAIM_POLICY CP ON CM.POLICY_ID=CP.POLICY_ID AND CP.CASE_ID= CM.CASE_ID
            LEFT JOIN DEV_CLM.T_TERMINATION T ON T.CODE = CP.DEAL_CONCLUSION 
            LEFT JOIN  DEV_CLM.T_CLAIM_UW CU ON CP.CASE_ID=CU.CASE_ID AND CP.POLICY_ID=CU.POLICY_ID
            WHERE 1=1 AND CM.CUR_FLAG= '1' AND CM.CASE_ID=#{caseNo} ) ORDER BY CM.POLICY_CODE]]>
    </select>
    <!--理赔详细信息_合同处理 涉案险种 -->
    <select id="queryPaSaxzList" resultType="java.util.Map"
        parameterType="java.util.Map">
                          <![CDATA[ 
               SELECT DISTINCT CBP.POLICY_CODE,
          CBP.BUSI_ITEM_ID,
          CBP.BUSI_PROD_CODE,
          BP.PRODUCT_NAME_SYS,
          CBP.LIABILITY_STATUS,
          CBP.EXPIRE_DATE,
          CBP.DEAL_CONCLUSION,
          BP.WAIVER_CUSTOMER_ROLE,
          PDD.NAME,
          CASE WHEN CBP.EXPIRY_WAY=1 THEN '手动' WHEN CBP.EXPIRY_WAY=0 THEN '自动' ELSE null end as EXPIRY_WAY_STR
     FROM DEV_CLM.T_CLAIM_BUSI_PROD CBP
    INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
       ON CBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
    LEFT JOIN DEV_CLM.T_PROD_DEAL_DECISION PDD
       ON PDD.CODE = CBP.DEAL_CONCLUSION
    INNER JOIN DEV_CLM.T_BENEFIT_INSURED BI
       ON CBP.CASE_ID = BI.CASE_ID
      AND CBP.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
    INNER JOIN DEV_CLM.T_INSURED_LIST IL
       ON BI.CASE_ID = IL.CASE_ID 
      AND BI.CUR_FLAG = IL.CUR_FLAG
      AND BI.INSURED_ID = IL.LIST_ID
    INNER JOIN DEV_CLM.T_CUSTOMER TC
       ON IL.CUSTOMER_ID = TC.CUSTOMER_ID      
    WHERE BI.CUR_FLAG = '1'
      AND CBP.CASE_ID = #{caseNo} AND CBP.POLICY_CODE= #{policyNo} AND TC.CUSTOMER_NAME IN ${insured_name} ORDER BY CBP.POLICY_CODE]]>
    </select>

    <!-- 合同处理结果 保单处理结果 -->
    <select id="queryPACLList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
         SELECT DISTINCT CP.POLICY_CODE,
			       CP.LIABILITY_STATUS,
			       CP.EXPIRY_DATE,
			       CP.DEAL_CONCLUSION,
			       T.NAME
			  FROM DEV_CLM.T_CLAIM_POLICY CP
			  LEFT JOIN dev_clm.T_TERMINATION T
			    ON T.CODE = CP.DEAL_CONCLUSION
			  INNER JOIN DEV_CLM.T_INSURED_LIST IL
			    ON CP.CASE_ID = IL.CASE_ID
			   AND CP.POLICY_CODE = IL.POLICY_CODE
			  INNER JOIN DEV_CLM.T_CUSTOMER TC
			    ON IL.CUSTOMER_ID = TC.CUSTOMER_ID
			 WHERE IL.CUR_FLAG = '1'
			   AND CP.CASE_ID = #{caseNo} AND CP.POLICY_CODE= #{policyNo} AND TC.CUSTOMER_NAME IN ${insured_name}
			 ORDER BY CP.POLICY_CODE	]]>
    </select>
    <!-- 合同处理结果 险种处理结果 -->
    <select id="queryXZJGList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                 SELECT DISTINCT CBP.POLICY_CODE,
				       CBP.BUSI_ITEM_ID,
				       CBP.BUSI_PROD_CODE,
				       CBP.LIABILITY_STATUS,
				       CBP.DEAL_CONCLUSION,
				       CBP.EXPIRE_DATE,
				       BP.WAIVER_CUSTOMER_ROLE,
				       pdd.name
				  FROM DEV_CLM.T_CLAIM_BUSI_PROD CBP
				  	INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
       			ON CBP.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
				  left join dev_clm.T_PROD_DEAL_DECISION pdd
				    on pdd.code = cbp.deal_conclusion
				 INNER JOIN DEV_CLM.T_BENEFIT_INSURED BI
				    ON CBP.CASE_ID = BI.CASE_ID
				   AND CBP.BUSI_ITEM_ID = BI.BUSI_ITEM_ID
				 INNER JOIN DEV_CLM.T_INSURED_LIST IL
				    ON BI.CASE_ID = IL.CASE_ID
				   AND BI.CUR_FLAG = IL.CUR_FLAG
				   AND BI.INSURED_ID = IL.LIST_ID
				 INNER JOIN DEV_CLM.T_CUSTOMER TC
				    ON IL.CUSTOMER_ID = TC.CUSTOMER_ID
				 WHERE BI.CUR_FLAG = '1'
        AND CBP.CASE_ID=#{caseNo} AND CBP.POLICY_CODE= #{policyNo} AND TC.CUSTOMER_NAME IN ${insured_name}
        ORDER BY CBP.POLICY_CODE]]>
    </select>
    <!-- 合同处理结果 合同结算结果 -->
    <select id="queryPaJsList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                 SELECT
              CAB.POLICY_CODE,
              CAB.BUSI_PROD_CODE, 
              CAB.ADJUST_TYPE,
              CAB.FEE_AMOUNT,
              CAB.PAY_AMOUNT,
              CAB.BUSI_ADJUST_REASON,
              CAB.REMARKS
            FROM 
            DEV_CLM.T_CLAIM_ADJUST_BUSI CAB
            WHERE CAB.CASE_ID=#{caseNo}  AND CAB.POLICY_ID NOT IN (SELECT DISTINCT
          CM.POLICY_ID
         FROM  DEV_CLM.T_CONTRACT_MASTER CM 
            LEFT JOIN  DEV_CLM.T_CLAIM_UW CU ON CM.CASE_ID=CU.CASE_ID AND CM.POLICY_ID=CU.POLICY_ID
            WHERE 1=1 AND CM.CUR_FLAG= '1' AND CM.CASE_ID=#{caseNo}  AND CM.POLICY_ID NOT IN (
       SELECT 
          DISTINCT
          CM.POLICY_ID
            FROM 
               DEV_CLM.T_CONTRACT_MASTER CM
            INNER JOIN
               DEV_CLM.T_CLAIM_POLICY CP ON CM.POLICY_ID=CP.POLICY_ID AND CP.CASE_ID= CM.CASE_ID
            LEFT JOIN DEV_CLM.T_TERMINATION T ON T.CODE = CP.DEAL_CONCLUSION 
            LEFT JOIN  DEV_CLM.T_CLAIM_UW CU ON CP.CASE_ID=CU.CASE_ID AND CP.POLICY_ID=CU.POLICY_ID
            WHERE 1=1 AND CM.CUR_FLAG= '1' AND CM.CASE_ID=#{caseNo} ))]]>
            <if test="policy_code!=null and policy_code!=''">
               <![CDATA[  AND CAB.POLICY_CODE=#{policy_code} ]]>
            </if>
            <if test="busiprodcode!=null and busiprodcode!=''">
                <![CDATA[ AND CAB.busi_prod_code=#{busiprodcode} ]]>
            </if>
    </select>

    <!-- 理赔支付计划 -->
    <!--理赔支付计划 列表 -->
    <select id="queryZFJHList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[  
               select * from (
                 SELECT distinct PD.DEDUCTION_ID, 
                 (SELECT  TCBP.OVER_COMP_PAY  FROM DEV_CLM.t_Claim_Busi_Prod TCBP 
        where PD.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID AND PD.BUSI_PROD_CODE=TCBP.BUSI_PROD_CODE AND PD.CASE_ID=TCBP.CASE_ID) OVER_COMP_PAY,
              PD.CASE_ID,
              PD.POLICY_CODE,
              PD.BUSI_PROD_CODE,
              PD.BUSI_ITEM_ID,
              BP.PRODUCT_NAME_SYS,
              PD.POLICY_ID,
              PD.RESTORE_BALANCE AS PAY_AMOUNT,
              '返还保费' AS ADJUST_TYPE,
              '1' AS ADJUST_CODE,
               CBP.ASSIGN_FLAG,/*返还保费不需要分配*/  
              (case when nvl(PD.Cash_Dividend,0.0)=0.0 then '0' else '1' end)  AS CASH_FLAG,
              (case when nvl(PD.Actual_Pay_Balance,0.0)=0.0 then '0' else '1' end) AS ACTUAL_FLAG,
              (case when nvl(PD.Restore_Balance,0.0)=0.0 then '0' else '1' end) AS RESTORE_FLAG
         FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
        INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
           ON PD.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
       INNER JOIN  DEV_CLM.T_CLAIM_ADJUST_BUSI CBP
         ON PD.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE and PD.Busi_Item_Id=CBP.busi_item_id AND PD.POLICY_CODE=CBP.POLICY_CODE
         AND PD.CASE_ID=CBP.CASE_ID
        AND CBP.Busi_Item_Id=PD.Busi_Item_Id
        WHERE PD.CASE_ID = #{caseNo}
          AND PD.RESTORE_BALANCE != 0.0
          AND (CBP.ADJUST_TYPE = '8' OR CBP.ADJUST_TYPE='16' OR CBP.ADJUST_TYPE='17' OR CBP.ADJUST_TYPE='18')
       UNION ALL
       SELECT distinct PD.DEDUCTION_ID, 
       (SELECT  TCBP.OVER_COMP_PAY  FROM DEV_CLM.t_Claim_Busi_Prod TCBP 
        where PD.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID AND PD.BUSI_PROD_CODE=TCBP.BUSI_PROD_CODE AND PD.CASE_ID=TCBP.CASE_ID) OVER_COMP_PAY,
              PD.CASE_ID,
              PD.POLICY_CODE,
              PD.BUSI_PROD_CODE,
              PD.BUSI_ITEM_ID,
              BP.PRODUCT_NAME_SYS,
              PD.POLICY_ID,
              PD.ACTUAL_PAY_BALANCE AS PAY_AMOUNT,
              '理赔金' AS ADJUST_TYPE,
              '2' AS ADJUST_CODE,
              CBP.ASSIGN_FLAG,
              (case when nvl(PD.Cash_Dividend,0.0)=0.0 then '0' else '1' end)  AS CASH_FLAG,
              (case when nvl(PD.Actual_Pay_Balance,0.0)=0.0 then '0' else '1' end) AS ACTUAL_FLAG,
              (case when nvl(PD.Restore_Balance,0.0)=0.0 then '0' else '1' end) AS RESTORE_FLAG
         FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
        INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
           ON PD.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
         INNER JOIN  DEV_CLM.T_CLAIM_BUSI_PROD CBP
         ON PD.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE and PD.Busi_Item_Id=CBP.busi_item_id AND PD.POLICY_CODE=CBP.POLICY_CODE
         AND PD.CASE_ID=CBP.CASE_ID  
        WHERE PD.CASE_ID =#{caseNo}
          AND PD.ACTUAL_PAY_BALANCE != 0.0
       UNION ALL
       SELECT distinct PD.DEDUCTION_ID, 
       (SELECT  TCBP.OVER_COMP_PAY  FROM DEV_CLM.t_Claim_Busi_Prod TCBP 
        where PD.BUSI_ITEM_ID=TCBP.BUSI_ITEM_ID AND PD.BUSI_PROD_CODE=TCBP.BUSI_PROD_CODE AND PD.CASE_ID=TCBP.CASE_ID) OVER_COMP_PAY,
              PD.CASE_ID,
              PD.POLICY_CODE,
              PD.BUSI_PROD_CODE,
              PD.BUSI_ITEM_ID,
              BP.PRODUCT_NAME_SYS,
              PD.POLICY_ID,
              PD.CASH_DIVIDEND AS PAY_AMOUNT,
              '现金红利' AS ADJUST_TYPE,
              '3' AS ADJUST_CODE,
              CBP.ASSIGN_FLAG,
              (case when nvl(PD.Cash_Dividend,0.0)=0.0 then '0' else '1' end)  AS CASH_FLAG,
              (case when nvl(PD.Actual_Pay_Balance,0.0)=0.0 then '0' else '1' end) AS ACTUAL_FLAG,
              (case when nvl(PD.Restore_Balance,0.0)=0.0 then '0' else '1' end) AS RESTORE_FLAG
         FROM DEV_CLM.T_CLAIM_PAY_DEDUCTION PD
        INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
           ON PD.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
        INNER JOIN  DEV_CLM.T_CLAIM_ADJUST_BUSI CBP
         ON PD.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE
         AND PD.CASE_ID=CBP.CASE_ID and PD.busi_prod_code=CBP.busi_prod_code and PD.Busi_Item_Id=CBP.busi_item_id AND PD.POLICY_CODE=CBP.POLICY_CODE
        WHERE PD.CASE_ID = #{caseNo}
          AND PD.CASH_DIVIDEND != 0.0
          AND (CBP.ADJUST_TYPE = '19' OR CBP.ADJUST_TYPE = '20')
          ) T ORDER BY POLICY_CODE,BUSI_PROD_CODE
             ]]>
    </select>
    <select  id="queryZFJHList_QIANYI" resultType="java.util.Map" parameterType="java.util.Map">
      <![CDATA[ 
        SELECT
           A.*,
          BP.PRODUCT_NAME_SYS,
          CBP.ASSIGN_FLAG,
          '1' AS ACTUAL_FLAG,
          '0' AS CASH_FLAG,
          '0' AS RESTORE_FLAG,
          '理赔金' AS ADJUST_TYPE
        FROM
        (
        SELECT CP.CASE_ID,CP.POLICY_CODE,CP.POLICY_ID,CP.BUSI_PROD_CODE,CP.BUSI_ITEM_ID,
        '0' AS ADJUST_CODE,/*迁移数据时 adjuxt_code赋值为0，获取收益人领款人信息时用来区分，金额是否需要判断费用类型*/
        SUM(CP.PAY_AMOUNT) AS PAY_AMOUNT
        FROM DEV_CLM.T_CLAIM_PAY CP LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI CBP ON CBP.ADJUST_BUSI_ID=CP.ADJUST_BUSI_ID
        AND CP.CASE_ID=CBP.CASE_ID
        WHERE 1=1
        AND CP.CASE_ID=#{caseNo} 
        AND CP.PAY_AMOUNT>0 
        GROUP BY  CP.CASE_ID, CP.POLICY_CODE,CP.POLICY_ID,CP.BUSI_PROD_CODE,CP.BUSI_ITEM_ID        
        ) A
         INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
           ON A.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
         INNER JOIN  DEV_CLM.T_CLAIM_BUSI_PROD CBP
         ON A.BUSI_PROD_CODE=CBP.BUSI_PROD_CODE AND A.POLICY_CODE=CBP.POLICY_CODE
         AND A.CASE_ID=CBP.CASE_ID  
         ORDER BY A.POLICY_CODE,A.BUSI_PROD_CODE
         ]]>        
    </select>
    <select id="queryZFJHSList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
             SELECT  case.case_id,case.case_no,adbus.adjust_busi_id,
                         adbus.policy_id,adbus.policy_code,
                         adbus.busi_item_id, adbus.busi_prod_code,prd.product_name_sys,
                         adbus.assign_flag,adbus.adjust_type,adbus.pay_amount ,'jiesuan' as type
                  FROM  DEV_CLM.t_claim_case case 
                  LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI adbus on adbus.case_id = case.case_id
                  LEFT JOIN DEV_PDS.t_business_product prd ON  prd.product_code_sys = adbus.busi_prod_code
                  WHERE case.case_id =#{caseNo} and adbus.pay_amount>0]]>
                  </select>
    
    <!--理赔支付计划 受益人领款人列表 -->
<!--    <select id="querySYLKList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
              SELECT
             CB.BENE_NAME,
              CB.BENE_ID,
              CB.CASE_ID,
            CP.PAYEE_ID,
             CPE.PAYEE_NAME,
             CP.PAY_AMOUNT,
             CP.BENE_RATE,
             CPE.PAY_MODE,
             CP.IS_INSTALMENT
            FROM  
            DEV_CLM.T_CLAIM_PAY CP
            INNER JOIN DEV_CLM.T_CLAIM_BENE CB
            ON CP.BENE_ID=CB.BENE_ID
            INNER JOIN DEV_CLM.T_CLAIM_PAYEE CPE 
            ON CP.PAYEE_ID=CPE.PAYEE_ID
            WHERE CP.CASE_ID=#{caseNo} ]]>
    </select> -->
    <!-- <select id="querySYLKList" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
              SELECT
             CB.BENE_NAME,
              CB.BENE_ID,
              CB.CASE_ID,
            CP.PAYEE_ID,
             CPE.PAYEE_NAME,
             CP.PAY_AMOUNT,
             CP.BENE_RATE,
             CPE.PAY_MODE,
             CP.IS_INSTALMENT
            FROM  
            DEV_CLM.T_CLAIM_PAY CP
            INNER JOIN DEV_CLM.T_CLAIM_BENE CB
            ON CP.BENE_ID=CB.BENE_ID
            INNER JOIN DEV_CLM.T_CLAIM_PAYEE CPE 
            ON CP.PAYEE_ID=CPE.PAYEE_ID
            WHERE CP.CASE_ID=#{caseNo} 
             AND CP.POLICY_CODE = #{policyNo} 
            AND CP.BUSI_PROD_CODE = #{busiprodcode} ]]>
    </select> -->
         <select id="querySYLKListlisuan" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
              SELECT 
             CB.BENE_NAME,
              CB.BENE_ID,
              CB.CASE_ID,
            CP.PAYEE_ID,
             CPE.PAYEE_NAME,
             CP.PAY_AMOUNT,
              CP.BENE_RATE,
             PM.NAME PAY_MODE,
             CP.IS_INSTALMENT,
             CP.ADJUST_BUSI_ID,
              CP.CLAIM_PAY_ID
            FROM  
            DEV_CLM.T_CLAIM_PAY CP
            INNER JOIN DEV_CLM.T_CLAIM_BENE CB
            ON CP.BENE_ID=CB.BENE_ID
            INNER JOIN DEV_CLM.T_CLAIM_PAYEE CPE 
            ON CP.PAYEE_ID=CPE.PAYEE_ID
             LEFT JOIN  DEV_PAS.T_PAY_MODE PM ON PM.CODE= CPE.PAY_MODE
            WHERE CP.CASE_ID=#{caseNo} AND CP.ADJUST_BUSI_ID is null
            ]]>
            <if test=" policyNo != null and policyNo != ''  "><![CDATA[AND CP.POLICY_CODE = #{policyNo} ]]></if>
            <if test=" busiprodcode != null and busiprodcode != ''  "><![CDATA[AND CP.BUSI_PROD_CODE = #{busiprodcode} ]]></if>
            
    </select> 
    <select id="querySYRListByPayId" resultType="java.util.Map"
        parameterType="java.util.Map">
        	<if test="adjust_code != null and adjust_code==0">
        		<![CDATA[
					SELECT distinct
					       t5.bene_name,
					       t2.bene_id,
					       t2.case_id,
					       t2.payee_id,
					       t3.payee_name,
					       sum(pay_amount) pay_amount,
					       t4.name pay_mode,
					       t2.pay_mole,
					       t2.bene_rate,
					       t2.pay_deno,
					       t2.is_instalment,
					       t2.policy_id,
					       t2.busi_item_id
					  FROM dev_clm.t_claim_pay t2
					 inner join dev_clm.t_claim_payee t3
					    on t2.payee_id = t3.payee_id
					 inner join dev_clm.t_claim_bene t5
					    on t2.bene_id=t5.bene_id
					 inner join DEV_PAS.T_PAY_MODE t4
					    ON t4.CODE = t3.PAY_MODE
					 where 1=1 
					   and t2.CASE_ID = #{caseNo}
					   AND t2.POLICY_ID = #{policy_id}
					   AND t2.BUSI_PROD_CODE = #{busiprodcode} 
					   group by t5.bene_name,
					       t2.bene_id,
					       t2.case_id,
					       t2.payee_id,
					       t3.payee_name,
					       t4.name,
					       t2.pay_mole,
					       t2.bene_rate,
					       t2.pay_deno,
					       t2.is_instalment,
					       t2.policy_id,
					       t2.busi_item_id
        		]]>
        	</if>
        	<if test="adjust_code != null  and adjust_code!=0">
              <![CDATA[ 
	              SELECT distinct
					       t5.bene_name,
					       t2.bene_id,
					       t1.case_id,
					       t2.payee_id,
					       t3.payee_name,]]>
					 <if test="adjust_code != null  and adjust_code==1">
					      <![CDATA[  round((t1.RESTORE_BALANCE*t2.pay_mole)/t2.pay_deno,2) pay_amount,]]>
					 </if>
					 <if test="adjust_code != null  and adjust_code==2">
					      <![CDATA[  round((t1.ACTUAL_PAY_BALANCE*t2.pay_mole)/t2.pay_deno,2) pay_amount,]]>
					 </if>
					 <if test="adjust_code != null  and adjust_code==3">
					      <![CDATA[  round((t1.CASH_DIVIDEND*t2.pay_mole)/t2.pay_deno,2) pay_amount,]]>
					 </if>
					       t4.name pay_mode,
					       t2.pay_mole,
					       t2.bene_rate,
					       t2.pay_deno,
					       t2.is_instalment,
					       t1.policy_id,
					       t1.busi_item_id
					  FROM dev_clm.t_claim_pay_deduction t1
					 inner join dev_clm.t_claim_pay t2
					    on t1.case_id = t2.case_id
					   and t1.busi_item_id = t2.busi_item_id
					 inner join dev_clm.t_claim_payee t3
					    on t2.payee_id = t3.payee_id
					 inner join dev_clm.t_claim_bene t5
					    on t2.bene_id=t5.bene_id
					 inner join DEV_PAS.T_PAY_MODE t4
					    ON t4.CODE = t3.PAY_MODE
					 left join DEV_CLM.T_CLAIM_ADJUST_BUSI T6
						ON T2.ADJUST_BUSI_ID=T6.ADJUST_BUSI_ID
					 where 1=1 
					<if test="adjust_code != null  and adjust_code==1">
					     <![CDATA[  
					   		and t2.adjust_busi_id is not null and (t6.ADJUST_TYPE ='8' OR t6.ADJUST_TYPE ='16'  OR t6.ADJUST_TYPE ='17'  OR t6.ADJUST_TYPE ='18')
					   	 ]]>
					</if>
					  <if test="adjust_code != null  and adjust_code==2">
					    <![CDATA[ and (t2.adjust_busi_id is null or t6.ADJUST_TYPE='1' or t6.ADJUST_TYPE='2' or t6.ADJUST_TYPE ='9' or t6.ADJUST_TYPE ='10' or t6.ADJUST_TYPE = '13' or t6.ADJUST_TYPE = '14' or t6.ADJUST_TYPE = '22' or t6.ADJUST_TYPE = '25' or t6.ADJUST_TYPE = '26')]]>
					</if>
					<if test="adjust_code != null  and adjust_code==3">
					    <![CDATA[  
							and t2.adjust_busi_id is not null and (t6.ADJUST_TYPE ='19' OR t6.ADJUST_TYPE ='20')
						]]>
					</if>
					   <![CDATA[  
					   		and t1.CASE_ID = #{caseNo}
							AND t1.POLICY_ID = #{policy_id}
							AND t1.BUSI_PROD_CODE = #{busiprodcode}
         				]]>
        </if>
    </select> 
    <!--理赔支付计划 受益人详细信息 -->
    <!-- <select id="querySYRInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                SELECT 
                CB.BENE_RELATION,
                CB.CASE_ID,
                CB.BENE_NAME,
                CB.BENE_SEX,
                CB.BENE_BIRTH,
                CB.BENE_CERTI_TYPE,
                CB.BENE_CERTI_NO,
                CB.BENE_NATION,
                TO_CHAR(CB.BENE_CERTI_START,'yyyy-MM-dd') BENE_CERTI_START,
                TO_CHAR(CB.BENE_CERTI_END,'yyyy-MM-dd') BENE_CERTI_END,
                CP.PAY_MOLE,
                CP.PAY_DENO,
                CP.PAY_AMOUNT
                FROM
                DEV_CLM.T_CLAIM_BENE CB,DEV_CLM.T_CLAIM_PAY CP
                WHERE 1=1 AND CB.CASE_ID=CP.CASE_ID AND CB.BENE_ID=CP.BENE_ID
                AND CB.CASE_ID=#{caseNo} ]]>
    </select> -->
    <select id="querySYRInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
                    SELECT (SELECT LP.RELATION_NAME
                      FROM DEV_CLM.T_LA_PH_RELA LP
                     WHERE LP.RELATION_CODE = CB.BENE_RELATION) BENE_RELATION,
                     (SELECT LP.RELATION_NAME
                      FROM DEV_CLM.T_LA_PH_RELA LP
                     WHERE LP.RELATION_CODE = CP.BENE_HOLDER_RELATION) BENE_HOLDER_RELATION,
                   CB.BENE_ID,
                   CB.CASE_ID,
                   CB.BENE_NAME,
                   CB.BENE_SEX,
                   (SELECT T.GENDER_DESC
                      FROM DEV_CLM.T_GENDER T
                     WHERE T.GENDER_CODE = CB.BENE_SEX) BENE_SEX_C,
                   TO_CHAR(CB.BENE_BIRTH, 'yyyy-MM-dd') BENE_BIRTH,
                   CB.BENE_CERTI_TYPE,
                   (SELECT C.TYPE
                      FROM DEV_CLM.T_CERTI_TYPE C
                     WHERE C.CODE = CB.BENE_CERTI_TYPE) BENE_CERTI_TYPE_C,
                   CB.BENE_CERTI_NO,
                   CB.BENE_NATION,
                   (SELECT CC.COUNTRY_NAME
                      FROM DEV_CLM.T_COUNTRY CC
                     WHERE CC.COUNTRY_CODE = CB.BENE_NATION) BENE_NATION_C,
                   TO_CHAR(CB.BENE_CERTI_START, 'yyyy-MM-dd') BENE_CERTI_START,
                   TO_CHAR(CB.BENE_CERTI_END, 'yyyy-MM-dd') BENE_CERTI_END,
                    (MONTHS_BETWEEN(CB.BENE_CERTI_END,CB.BENE_CERTI_START)/12) AS BENE_TIME,/*受益人有效期间*/
                   CP.PAY_MOLE,
                   (SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE = CB.BENE_PROVINCE) BENE_PROVINCE_C,
                   (SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE = CB.BENE_CITY) BENE_CITY_C,
                   (SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE = CB.BENE_DISTRICT) BENE_DISTRICT_C,
                   CB.BENE_ADDRESS,
                   (SELECT TD.JOB_NAME FROM DEV_CLM.T_JOB_CODE TD WHERE TD.JOB_CODE = CB.BENE_JOB_CODE) BENE_JOB_CODE_C,
                   CB.BENE_PHONE,
                   CB.LEGAL_PERSON_ID AS BENE_LEGAL_PERSON_ID,
                   (SELECT MM.NAME
                      FROM DEV_CLM.T_PAY_MODE MM
                     WHERE MM.CODE = CP.PAY_MOLE) PAY_MODE_C,
                   CP.PAY_DENO,
                   ]]>
                  <if test=" pay_amount != null">
                  <![CDATA[  #{pay_amount} AS PAY_AMOUNT  ]]>
                    </if>
                   <if test=" pay_amount == null">
                    <![CDATA[  '' AS PAY_AMOUNT  ]]>
                  </if>
                  <![CDATA[ 
                   FROM  DEV_CLM.T_CLAIM_BENE CB INNER JOIN 
                   (SELECT DISTINCT CP.CASE_ID,CP.BENE_ID,CP.PAYEE_ID,CP.PAY_DENO,CP.PAY_MOLE,CP.BENE_HOLDER_RELATION   FROM DEV_CLM.T_CLAIM_PAY CP WHERE 1 = 1  AND CP.CASE_ID =  #{case_id}  AND CP.PAYEE_ID=#{payeeid}
                   AND CP.POLICY_ID=#{policy_id} AND CP.BUSI_ITEM_ID=#{busi_item_id}) CP
                    ON  CB.BENE_ID = CP.BENE_ID AND CB.CASE_ID=CP.CASE_ID WHERE 1 = 1  AND CB.CASE_ID = #{case_id}
                ]]>
                <if test=" beneid != null and beneid != ''  "><![CDATA[AND CP.BENE_ID = #{beneid}]]></if>   
                <if test=" payeeid != null and payeeid != ''  "><![CDATA[AND CP.PAYEE_ID = #{payeeid} ]]></if>   
    </select>

    <!--理赔支付计划 领款人详细信息 -->
    <!-- <select id="queryLKRInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
              SELECT 
            CPE.PAYEE_RELATION,
            CPE.CASE_ID,
            CPE.PAYEE_NAME,
            CPE.PAYEE_SEX,
            CPE.PAYEE_BIRTH,
            CPE.PAYEE_CERTI_TYPE,
            CPE.PAYEE_CERTI_NO,
            CPE.PAYEE_NATION,
            TO_CHAR(CPE.PAYEE_CERTI_START,'yyyy-MM-dd') PAYEE_CERTI_START,
            TO_CHAR(CPE.PAYEE_CERTI_END,'yyyy-MM-dd') PAYEE_CERTI_END,
            CPE.PAY_MODE,
            CPE.ACCOUNT_NAME,
            CPE.ACCOUNT_NO,
            CPE.BANK_CODE
            FROM 
            DEV_CLM.T_CLAIM_PAYEE CPE
            WHERE 1=1
            AND CPE.CASE_ID=#{caseNo} ]]>
    </select> -->
        <select id="queryLKRInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
              SELECT A.PAYEE_RELATION,CASE WHEN A.PAYEE_RELATION_D IS NULL THEN A.PAYEE_RELATION_C ELSE A.PAYEE_RELATION_D END AS PAYEE_RELATION_C,
			A.CASE_ID,A.PAYEE_NAME,A.PAYEE_SEX,A.PAYEE_SEX_C,A.PAYEE_BIRTH,A.PAYEE_CERTI_TYPE,A.PAYEE_CERTI_TYPE_C,A.PAYEE_CERTI_NO,
            A.PAYEE_NATION,A.PAYEE_NATION_C,A.PAYEE_CERTI_START,A.PAYEE_CERTI_END,A.PAYEE_TIME,A.PAY_MODE,A.PAY_MODE_C,
            A.ACCOUNT_NAME,A.ACCOUNT_NO,A.BANK_CODE,A.BANK_NAME,A.PAYEE_ID,A.BENE_ID,A.PAYEE_STATE_C,A.PAYEE_CITY_C,A.PAYEE_DISTRICT_C,A.PAYEE_ADDRESS,A.PAYEE_JOB_CODE_C,A.PAYEE_PHONE
             ,A.PAYEE_LEGAL_PERSON_ID,A.CONTRARY_PAY_FLAG
             FROM
			(SELECT CPE.PAYEE_ID,CPY.BENE_ID,
			(case when CPY.PAYEE_RELATION is not null then CPY.PAYEE_RELATION else CPE.PAYEE_RELATION end)PAYEE_RELATION,
           
            (case when CPY.PAYEE_RELATION is not null then 
               (select r.relation_name from DEV_CLM.T_LA_PH_RELA r where r.relation_code=CPY.PAYEE_RELATION) 
            else 
              (select r.relation_name from DEV_CLM.T_LA_PH_RELA r where r.relation_code=CPE.PAYEE_RELATION)
            end) PAYEE_RELATION_C,
              (select r.relation_name from DEV_CLM.T_LA_PH_RELA r where r.relation_code=CPY.PAYEE_RELATION) PAYEE_RELATION_D,
            CPE.CASE_ID,
            CPE.PAYEE_NAME,
            CPE.PAYEE_SEX,
             (select t.gender_desc from DEV_CLM.T_GENDER t where t.gender_code=CPE.PAYEE_SEX) PAYEE_SEX_C,
            to_char(CPE.PAYEE_BIRTH,'yyyy-MM-dd') PAYEE_BIRTH,
            CPE.PAYEE_CERTI_TYPE,
            (select c.type from DEV_CLM.T_CERTI_TYPE c where c.code=CPE.PAYEE_CERTI_TYPE) PAYEE_CERTI_TYPE_C,
            CPE.PAYEE_CERTI_NO,
            CPE.PAYEE_NATION,
             (select cc.country_name from DEV_CLM.T_COUNTRY cc where cc.country_code=CPE.PAYEE_NATION)PAYEE_NATION_C,
            TO_CHAR(CPE.PAYEE_CERTI_START,'yyyy-MM-dd') PAYEE_CERTI_START,
            TO_CHAR(CPE.PAYEE_CERTI_END,'yyyy-MM-dd') PAYEE_CERTI_END,
            (MONTHS_BETWEEN(CPE.PAYEE_CERTI_END,CPE.PAYEE_CERTI_START)/12) AS PAYEE_TIME,
			(SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE = CPE.PAYEE_STATE) PAYEE_STATE_C,
            (SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE = CPE.PAYEE_CITY) PAYEE_CITY_C,
            (SELECT TD.NAME FROM DEV_PAS.T_DISTRICT TD WHERE TD.CODE = CPE.PAYEE_DISTRICT) PAYEE_DISTRICT_C,
            CPE.PAYEE_ADDRESS,
            (SELECT TD.JOB_NAME FROM DEV_CLM.T_JOB_CODE TD WHERE TD.JOB_CODE = CPE.PAYEE_JOB_CODE) PAYEE_JOB_CODE_C,
            CPE.PAYEE_PHONE,           
            CPE.PAY_MODE,
             (select mm.name from DEV_CLM.T_PAY_MODE mm where mm.code=CPE.PAY_MODE)PAY_MODE_C,
            CPE.ACCOUNT_NAME,
            CPE.ACCOUNT_NO,
           	CPE.BANK_CODE,B.BANK_NAME,CPY.CLAIM_PAY_ID,CPY.POLICY_ID,CPY.BUSI_ITEM_ID,
           	CPE.LEGAL_PERSON_ID AS PAYEE_LEGAL_PERSON_ID,
           	CPY.CONTRARY_PAY_FLAG
            FROM 
            DEV_CLM.T_CLAIM_PAYEE CPE, DEV_CLM.T_CLAIM_PAY CPY,APP___CLM__DBUSER.T_BANK B
            WHERE  CPE.PAYEE_ID=CPY.PAYEE_ID AND
                 CPE.BANK_CODE = B.BANK_CODE(+) 
            and CPY.CASE_ID = #{case_id}) A WHERE 1=1 
            ]]>
              <if test=" beneid != null and beneid != ''  "><![CDATA[AND A.BENE_ID = #{beneid}]]></if> 
            <if test=" payeeid != null and payeeid != ''  "><![CDATA[AND A.PAYEE_ID=#{payeeid} ]]></if> 
            <if test=" policy_id != null and policy_id != ''  "><![CDATA[AND A.POLICY_ID=#{policy_id} ]]></if>   
            <if test=" busi_item_id != null and busi_item_id != ''  "><![CDATA[AND A.BUSI_ITEM_ID=#{busi_item_id} ]]></if>  
            <if test=" claim_pay_id != null and claim_pay_id != ''  "><![CDATA[AND A.CLAIM_PAY_ID=#{claim_pay_id} ]]></if>   
        <!--    <if test=" adjustbusiid != null and adjustbusiid != ''  "><![CDATA[AND CPE.ADJUST_BUSI_ID=#{adjustbusiid} ]]></if>       -->
    </select>
    <!--理赔支付计划 投保人详细信息 -->
    <select id="queryTBRInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
               select TC.CUSTOMER_NAME, TC.CUSTOMER_CERT_TYPE, TC.CUSTOMER_CERTI_CODE,P.*,CA.PAY_MODE,CA.ACTUAL_BANK_ACCOUNT 
              from(  SELECT DISTINCT POLICY_CODE,CUSTOMER_ID FROM    DEV_CLM.T_POLICY_HOLDER PH    where PH.CASE_ID = #{caseNo})p
                         LEFT JOIN DEV_CLM.T_CUSTOMER TC
                          ON  P.CUSTOMER_ID = TC.CUSTOMER_ID 
                          left join dev_cap.v_cash_detail CA
                           ON P.POLICY_CODE=CA.BUSINESS_CODE 
             ]]>
    </select>
    <!--理赔支付计划 分期计划信息 -->
    <select id="queryFQInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
             SELECT CASE.CASE_ID,CASE.CASE_NO,PO.POLICY_ID,PO.POLICY_CODE,PROD.BUSI_ITEM_ID,PROD.BUSI_PROD_CODE, PRD.PRODUCT_NAME_SYS,CP.CLAIM_PAY_ID,
CL.PRODUCT_ID ,CL.LIAB_ID,ADBUS.ADJUST_BUSI_ID
              FROM  DEV_CLM.T_CLAIM_CASE CASE 
                          LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI ADBUS
               ON ADBUS.CASE_ID = CASE.CASE_ID
              LEFT JOIN DEV_CLM.T_CLAIM_POLICY PO ON PO.CASE_ID = CASE.CASE_ID
              LEFT JOIN DEV_CLM.T_CLAIM_BUSI_PROD PROD ON PROD.CASE_ID = CASE.CASE_ID AND PO.POLICY_ID = PROD.POLICY_ID
              LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT PRD ON  PRD.PRODUCT_CODE_SYS = PROD.BUSI_PROD_CODE
              LEFT JOIN DEV_CLM.T_CLAIM_PAY CP ON CP.CASE_ID = CASE.CASE_ID AND CP.POLICY_ID = PO.POLICY_ID 
              AND CP.BUSI_ITEM_ID = PROD.BUSI_ITEM_ID AND CP.BUSI_PROD_CODE=PROD.BUSI_PROD_CODE
              LEFT JOIN DEV_CLM.T_CLAIM_LIAB CL ON CL.CASE_ID = CASE.CASE_ID AND CL.POLICY_ID = PO.POLICY_ID
          WHERE  CASE.CASE_ID =#{caseID} AND PROD.ACTUAL_PAY >0  AND CL.IS_INSTALMENT ='1' and cp.is_instalment = '1'
UNION
SELECT  CASE.CASE_ID,CASE.CASE_NO, ADBUS.POLICY_ID,ADBUS.POLICY_CODE,ADBUS.BUSI_ITEM_ID,ADBUS.BUSI_PROD_CODE, PRD.PRODUCT_NAME_SYS,CP.CLAIM_PAY_ID,
CL.PRODUCT_ID ,CL.LIAB_ID,ADBUS.ADJUST_BUSI_ID
                FROM  DEV_CLM.T_CLAIM_CASE CASE 
          LEFT JOIN DEV_CLM.T_CLAIM_ADJUST_BUSI ADBUS ON ADBUS.CASE_ID = CASE.CASE_ID
          LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT PRD ON  PRD.PRODUCT_CODE_SYS = ADBUS.BUSI_PROD_CODE
           LEFT JOIN DEV_CLM.T_CLAIM_PAY CP ON CP.CASE_ID = CASE.CASE_ID AND CP.POLICY_ID = ADBUS.POLICY_ID 
           AND CP.BUSI_ITEM_ID = ADBUS.BUSI_ITEM_ID AND CP.BUSI_PROD_CODE = ADBUS.BUSI_PROD_CODE
           LEFT JOIN DEV_CLM.T_CLAIM_LIAB CL  ON CL.CASE_ID = CASE.CASE_ID AND CL.POLICY_ID = ADBUS.POLICY_ID
                  WHERE CASE.CASE_ID =#{caseID} AND ADBUS.PAY_AMOUNT >0 AND CL.IS_INSTALMENT ='1' and cp.is_instalment = '1'
             ]]>
    </select>
    <!-- <if test=" case_id  != null "><![CDATA[ AND CL.CASE_ID = #{case_id} ]]></if>
             <if test=" policy_id  != null "><![CDATA[ AND CL.POLICY_ID = #{policy_id} ]]></if> -->
    <!--理赔支付计划 分期计划信息 -->
    <select id="queryFQzfInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
            SELECT CI.PAY_DUE_DATE ,CI.COMMU_PAY,CI.PRINCIPAL,CI.INSTAL_TYPE,CI.INSTAL_STATUS,CI.SURVEY_FLAG,CI.SURVERY_PERIODS,
CI.SURVERY_FREQ_CODE,CI.PAY_TYPE  FROM DEV_CLM.T_CLAIM_INSTALMENT CI WHERE 1=1
             ]]>
             <if test=" claim_pay_id  != null "><![CDATA[ AND CI.CLAIM_PAY_ID = #{claim_pay_id} ]]></if>
             ORDER BY CI.INSTALMENT_ID 
    </select>
    <!-- 查询所有操作 -->
    <select id="findAllClaimLiab" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.ADVANCE_PAY, A.CLAIM_TYPE, A.ACTUAL_PAY, A.PRODUCT_ID,  ( SELECT T.TXT_CONTENT FROM DEV_CLM.T_CLOB T WHERE T.CLOB_ID=A.CLOB_ID)CLM_REMARK, A.CLAIM_LIAB_ID, A.ITEM_ID, 
            A.IS_COMMON, A.LIAB_START_DATE, A.BUSI_PROD_CODE, A.IS_WAIVED, A.SUB_CASE_ID, 
            A.AMOUNT_BASIC_PAY, A.WAIVE_ITEM, A.WAIVE_REASON, A.BUSI_ITEM_ID, A.POLICY_ID, A.LIAB_ADJUST_REASON, A.MAKEUP_BASIC_PAY, 
            A.RESIST_FLAG, A.LIABILITY_STATUS, A.WAIVE_START, A.ADJUST_PAY, A.LIAB_ID, 
            A.BONUS_BASIC_PAY, A.LIAB_NAME, A.CASE_ID, A.ADJUST_REMARK, A.LIAB_CONCLUSION, A.POLICY_CODE, A.WAIVE_AMT, 
            A.WAIVE_END, A.CALC_PAY, A.BASIC_PAY, A.LIAB_END_DATE, A.ADVANCE_DATE, A.CLM_AFTER_STATE, A.IS_SUBSIDY, A.IS_INSTALMENT,A.IS_MUST_SIGN FROM dev_clm.T_CLAIM_LIAB A WHERE ROWNUM <=  1000  ]]>
        <include refid="claimLiabWhereCondition" />
        <![CDATA[ ORDER BY A.CLAIM_LIAB_ID ]]> 
    </select>
    <sql id="claimLiabWhereCondition">
        <if test=" advance_pay  != null "><![CDATA[ AND A.ADVANCE_PAY = #{advance_pay} ]]></if>
        <if test=" claim_type != null and claim_type != ''  "><![CDATA[ AND A.CLAIM_TYPE = #{claim_type} ]]></if>
        <if test=" actual_pay  != null "><![CDATA[ AND A.ACTUAL_PAY = #{actual_pay} ]]></if>
        <if test=" product_id  != null "><![CDATA[ AND A.PRODUCT_ID = #{product_id} ]]></if>
        <if test=" clm_remark != null and clm_remark != ''  "><![CDATA[ AND  ( SELECT T.TXT_CONTENT FROM DEV_CLM.T_CLOB T WHERE T.CLOB_ID=LI.CLOB_ID)= #{clm_remark} ]]></if>
        <if test=" claim_liab_id  != null "><![CDATA[ AND A.CLAIM_LIAB_ID = #{claim_liab_id} ]]></if>
        <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
        <if test=" is_common  != null "><![CDATA[ AND A.IS_COMMON = #{is_common} ]]></if>
        <if test=" liab_start_date  != null  and  liab_start_date  != ''  "><![CDATA[ AND A.LIAB_START_DATE = #{liab_start_date} ]]></if>
        <if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
        <if test=" is_waived  != null "><![CDATA[ AND A.IS_WAIVED = #{is_waived} ]]></if>
        <if test=" sub_case_id  != null "><![CDATA[ AND A.SUB_CASE_ID = #{sub_case_id} ]]></if>
        <if test=" amount_basic_pay  != null "><![CDATA[ AND A.AMOUNT_BASIC_PAY = #{amount_basic_pay} ]]></if>
        <if test=" waive_item  != null "><![CDATA[ AND A.WAIVE_ITEM = #{waive_item} ]]></if>
        <if test=" waive_reason != null and waive_reason != ''  "><![CDATA[ AND A.WAIVE_REASON = #{waive_reason} ]]></if>
        <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
        <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
        <if test=" liab_adjust_reason != null and liab_adjust_reason != ''  "><![CDATA[ AND A.LIAB_ADJUST_REASON = #{liab_adjust_reason} ]]></if>
        <if test=" makeup_basic_pay  != null "><![CDATA[ AND A.MAKEUP_BASIC_PAY = #{makeup_basic_pay} ]]></if>
        <if test=" resist_flag  != null "><![CDATA[ AND A.RESIST_FLAG = #{resist_flag} ]]></if>
        <if test=" liability_status  != null "><![CDATA[ AND A.LIABILITY_STATUS = #{liability_status} ]]></if>
        <if test=" waive_start  != null  and  waive_start  != ''  "><![CDATA[ AND A.WAIVE_START = #{waive_start} ]]></if>
        <if test=" adjust_pay  != null "><![CDATA[ AND A.ADJUST_PAY = #{adjust_pay} ]]></if>
        <if test=" liab_id  != null "><![CDATA[ AND A.LIAB_ID = #{liab_id} ]]></if>
        <if test=" bonus_basic_pay  != null "><![CDATA[ AND A.BONUS_BASIC_PAY = #{bonus_basic_pay} ]]></if>
        <if test=" liab_name != null and liab_name != ''  "><![CDATA[ AND A.LIAB_NAME = #{liab_name} ]]></if>
        <if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
        <if test=" adjust_remark != null and adjust_remark != ''  "><![CDATA[ AND A.ADJUST_REMARK = #{adjust_remark} ]]></if>
        <if test=" liab_conclusion  != null "><![CDATA[ AND A.LIAB_CONCLUSION = #{liab_conclusion} ]]></if>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" waive_amt  != null "><![CDATA[ AND A.WAIVE_AMT = #{waive_amt} ]]></if>
        <if test=" waive_end  != null  and  waive_end  != ''  "><![CDATA[ AND A.WAIVE_END = #{waive_end} ]]></if>
        <if test=" calc_pay  != null "><![CDATA[ AND A.CALC_PAY = #{calc_pay} ]]></if>
        <if test=" basic_pay  != null "><![CDATA[ AND A.BASIC_PAY = #{basic_pay} ]]></if>
        <if test=" liab_end_date  != null  and  liab_end_date  != ''  "><![CDATA[ AND A.LIAB_END_DATE = #{liab_end_date} ]]></if>
        <if test=" advance_date  != null  and  advance_date  != ''  "><![CDATA[ AND A.ADVANCE_DATE = #{advance_date} ]]></if>
        <if test=" clm_after_state  != null  and  clm_after_state  != ''  "><![CDATA[ AND A.CLM_AFTER_STATE = #{clm_after_state} ]]></if>
        <if test=" is_instalment  != null"><![CDATA[ AND A.IS_INSTALMENT = #{is_instalment} ]]></if>
        <if test=" is_must_sign  != null"><![CDATA[ AND A.IS_MUST_SIGN = #{is_must_sign} ]]></if>
    </sql>
    <select id="findAllContractExtend2" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.POLICY_YEAR, A.EXTRACTION_DUE_DATE, A.ITEM_ID, A.BILLING_DATE, A.CASE_ID, 
            A.RENEW_DECISION_STATUS, A.LOG_ID, A.ORGAN_CODE, A.COPY_DATE, A.POLICY_CODE, 
            A.POLICY_PERIOD, A.LIST_ID, A.PAY_DUE_DATE, A.BUSI_ITEM_ID, A.POLICY_ID, 
            A.NEXT_PREM, A.PREM_STATUS, A.CUR_FLAG FROM dev_clm.T_CONTRACT_EXTEND A WHERE ROWNUM <=  1000  ]]>
        <include refid="contractExtendWhereCondition" /> 
        <![CDATA[ ORDER BY A.LOG_ID ]]> 
    </select>
    <sql id="contractExtendWhereCondition">
        <if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
        <if test=" extraction_due_date  != null  and  extraction_due_date  != ''  "><![CDATA[ AND A.EXTRACTION_DUE_DATE = #{extraction_due_date} ]]></if>
        <if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
        <if test=" billing_date  != null  and  billing_date  != ''  "><![CDATA[ AND A.BILLING_DATE = #{billing_date} ]]></if>
        <if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
        <if test=" renew_decision_status  != null "><![CDATA[ AND A.RENEW_DECISION_STATUS = #{renew_decision_status} ]]></if>
        <if test=" log_id  != null "><![CDATA[ AND A.LOG_ID = #{log_id} ]]></if>
        <if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
        <if test=" copy_date  != null  and  copy_date  != ''  "><![CDATA[ AND A.COPY_DATE = #{copy_date} ]]></if>
        <if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
        <if test=" policy_period  != null "><![CDATA[ AND A.POLICY_PERIOD = #{policy_period} ]]></if>
        <if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
        <if test=" pay_due_date  != null  and  pay_due_date  != ''  "><![CDATA[ AND A.PAY_DUE_DATE = #{pay_due_date} ]]></if>
        <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
        <if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
        <if test=" next_prem  != null "><![CDATA[ AND A.NEXT_PREM = #{next_prem} ]]></if>
        <if test=" prem_status != null "><![CDATA[ AND A.PREM_STATUS = #{prem_status} ]]></if>
        <if test=" cur_flag  != null "><![CDATA[ AND A.CUR_FLAG = #{cur_flag} ]]></if>
    </sql>
    <select id="queryCompWaiverManageList" parameterType="java.util.Map"
        resultType="java.util.Map">
        <![CDATA[ 
				

SELECT DISTINCT A.CASE_ID,
                                B.POLICY_ID,
                                B.POLICY_CODE,
                                C.BUSI_ITEM_ID,
                                C.BUSI_PRD_ID,
                                C.BUSI_PROD_CODE,
                                CBP.CLAIM_BUSI_PROD_ID,
                                C.LIABILITY_STATE,
                                C.VALIDATE_DATE,
                                D.PAIDUP_DATE,
                                D.ITEM_ID,
                                D.PREM_FREQ INITIAL_TYPE,
                                G.PRODUCT_CODE_SYS,
                                G.PRODUCT_NAME_SYS,
                                E.PAY_DUE_DATE,
                                E.POLICY_PERIOD,
                                (SELECT SUM(CP.STD_PREM_AF+CP.EXTRA_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         WHERE CP.BUSI_ITEM_ID = C.BUSI_ITEM_ID) AS NEXT_PREM,
               (SELECT SUM(CP.TOTAL_PREM_AF)
          FROM DEV_PAS.T_CONTRACT_PRODUCT CP
         WHERE CP.BUSI_ITEM_ID = C.BUSI_ITEM_ID) AS TOTAL_PREM_AF
                  FROM DEV_CLM.T_CLAIM_CASE        A
            INNER JOIN DEV_CLM.T_CLAIM_PRODUCT CPT
                    ON CPT.CASE_ID=A.CASE_ID
            INNER JOIN DEV_CLM.T_CLAIM_BUSI_PROD CBP
                    ON CPT.BUSI_ITEM_ID=CBP.BUSI_ITEM_ID
                   AND CBP.CASE_ID = A.CASE_ID 
            INNER JOIN DEV_PAS.T_CONTRACT_MASTER    B
                    ON CBP.POLICY_CODE=B.POLICY_CODE
            INNER JOIN  DEV_PAS.T_CONTRACT_BUSI_PROD C
                    ON CBP.BUSI_ITEM_ID=C.BUSI_ITEM_ID
            INNER JOIN DEV_PAS.T_CONTRACT_PRODUCT   D
                    ON CBP.BUSI_ITEM_ID=D.BUSI_ITEM_ID
            INNER JOIN DEV_PDS.T_BUSINESS_PRODUCT   G
                    ON C.BUSI_PROD_CODE =G.PRODUCT_CODE_SYS
            INNER JOIN DEV_PAS.T_CONTRACT_EXTEND E
                    ON   D.ITEM_ID=E.ITEM_ID
                 WHERE D.PREM_FREQ NOT IN (1) AND CPT.IS_WAIVED='1'
                 AND A.CASE_ID = #{caseNo} ]]>
    </select>
    <!--豁免处理 豁免详细信息 -->
    <select id="queryHuomDetail2" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[ 
              SELECT CSC.CLAIM_DATE,CE.PAY_DUE_DATE,CP.PREM_FREQ ,
CP.PAIDUP_DATE,CL.IS_WAIVED,CL.WAIVE_START,CL.WAIVE_END,CL.WAIVE_REASON,CL.WAIVE_AMT  
              FROM DEV_CLM.T_CLAIM_LIAB CL ,
              DEV_CLM.T_CLAIM_SUB_CASE CSC ,
              DEV_CLM.T_CONTRACT_EXTEND CE ,
              DEV_CLM.T_CONTRACT_PRODUCT CP
         WHERE 1=1 AND  CSC.SUB_CASE_ID = CL.SUB_CASE_ID AND CE.CASE_ID = CL.CASE_ID AND CE.POLICY_ID = CL.POLICY_ID 
         AND CE.BUSI_ITEM_ID = CL.BUSI_ITEM_ID AND CE.ITEM_ID = CL.ITEM_ID AND CE.CUR_FLAG =0 AND CP.CASE_ID = CL.CASE_ID 
         AND CP.POLICY_ID = CL.POLICY_ID AND CP.BUSI_ITEM_ID = CL.BUSI_ITEM_ID
         AND CP.ITEM_ID = CL.ITEM_ID AND CP.CUR_FLAG = 0  ]]>
          <if test="case_id !=null and case_no != ''"> <![CDATA[ and cl.case_id = #{case_id} ]]></if>
        <if test=" policy_id  != null and policy_id !=''"><![CDATA[ AND cl.POLICY_ID = #{policy_id} ]]></if>
        <if test=" busi_item_id  != null and busi_item_id !=''"><![CDATA[ AND cl.BUSI_ITEM_ID = #{busi_item_id} ]]></if>    
        <if test=" item_id  != null and item_id !=''"><![CDATA[ AND cl.ITEM_ID = #{item_id} ]]></if>    
        <if test=" liab_id  != null and liab_id !=''"><![CDATA[ AND cl.LIAB_ID = #{liab_id} ]]></if>    
    </select>
    <select id="queryCaseidByCaseNO" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[  SELECT A.CASE_ID FROM DEV_CLM.T_CLAIM_CASE  A WHERE 1=1  ]]>
              <if test="case_no !=null and case_no != ''"> 
					<![CDATA[ and a.case_no = #{case_no} ]]></if>
	</select>
	<select id="queryCaseidAndCaseFlagByCaseNO" resultType="java.util.Map"
	parameterType="java.util.Map">
             <![CDATA[  SELECT A.CASE_ID,A.CASE_FLAG FROM DEV_CLM.T_CLAIM_CASE  A WHERE 1=1 AND A.CASE_STATUS>32 ]]>
             <if test="case_no !=null and case_no != ''"> 
				<![CDATA[ and a.case_no = #{case_no} ]]></if>
			 <if test="case_id !=null and case_id != ''"> 
			<![CDATA[ and a.case_id = #{case_id} ]]></if>
	</select>
	<!--其他核保决定明细  -->
	<select id="findOtherDecitionDetail" resultType="java.util.Map"
		parameterType="java.util.Map">
              <![CDATA[  SELECT UP.DECISION_CODE, /*核保决定编码*/
						       PD.DECISION_DESC AS DECISION_NAME, /*核保决定*/
						       UP.QUALITY_INDI, /*是否优质体*/
						       UP.POSTPONED_MONTHS, /*延期月数*/
						       UDR.REASON_CONTENT /*原因*/
						  FROM DEV_CLM.T_UW_PRODUCT UP
						  LEFT JOIN DEV_CLM.T_UW_DECISION_REASON UDR
						    ON UDR.UW_PRD_ID = UP.UW_PRD_ID ]]>
						  <if test="reason_type !=null and reason_type != ''">
						  	<![CDATA[ AND UDR.REASON_TYPE = #{reason_type} ]]>
						  </if>
						  <![CDATA[ LEFT JOIN DEV_PAS.T_PRODUCT_DECISION PD
						    ON UP.DECISION_CODE = PD.DECISION_CODE
						 WHERE 1 = 1 ]]>
              <if test="uw_prd_id !=null and uw_prd_id != ''"> 
					<![CDATA[ AND UP.UW_PRD_ID = #{uw_prd_id} ]]></if>
	</select>
    <select id="findAllClaimAnnuity" resultType="java.util.Map"
        parameterType="java.util.Map">
              <![CDATA[  SELECT  TO_CHAR(TRUNC(MONTHS_BETWEEN(
　　TO_DATE(TO_CHAR(CSC.CLAIM_DATE, 'yyyy-MM-dd'),'yyyy-MM-dd'),
　　TO_DATE(TO_CHAR(CB.BENE_BIRTH, 'yyyy-MM-dd'),'yyyy-MM-dd')
　　) / 12)) AS BENE_AGE,CSC.CLAIM_DATE,CB.BENE_BIRTH,CA.ANNUITY_PAY,CL.ACTUAL_PAY AS TOTAL_AMOUNT
FROM DEV_CLM.T_CLAIM_LIAB CL 
INNER JOIN DEV_CLM.T_CLAIM_SUB_CASE CSC ON CL.SUB_CASE_ID=CSC.SUB_CASE_ID
INNER JOIN DEV_CLM.T_CLAIM_ANNUITY CA ON CA.CLAIM_LIAB_ID=CL.CLAIM_LIAB_ID
LEFT JOIN DEV_CLM.T_CLAIM_PAY CP ON CP.CLAIM_PAY_ID=CA.CLAIM_PAY_ID
LEFT JOIN DEV_CLM.T_CLAIM_BENE CB ON CB.CASE_ID=CL.CASE_ID AND CB.BENE_ID=CP.BENE_ID
WHERE  1=1  ]]>
<if test="case_id !=null and case_id != ''"><![CDATA[ and cl.case_id = #{case_id} ]]></if>
<if test="bene_id !=null and bene_id != ''"><![CDATA[ and cb.bene_id = #{bene_id} ]]></if>
    </select>
    
    <select id="findAllClaimDiscussByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT U.USER_NAME,
       U.REAL_NAME,
       A.DISCUSS_BACK_TIME,
       A.EXPECT_RETURN_TIME,
       A.DISCUSS_CONCLUSION,
       A.DISCUSS_STATUS,
       A.APPLY_TIME,
       A.DISCUSS_ID,
       A.CASE_NO,
       A.APPLY_BY,
       A.CASE_ID,
       A.ASSIGN_CONCLUSION,
       A.DISCUSS_CONTENT,
       A.DISCUSS_MANAGE,
       A.DISCUSS_DESC
  FROM DEV_CLM.T_CLAIM_DISCUSS A
 left JOIN DEV_PAS.T_UDMP_USER U
    ON U.USER_ID = A.DISCUSS_MANAGE WHERE ROWNUM <=  1000  AND A.DISCUSS_STATUS != 2]]>
        <if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
        <if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
        <![CDATA[ ORDER BY A.DISCUSS_ID ]]> 
    </select>
    <!-- 查询合议子表数据 -->
    <select id="findAllClaimSubDiscuss" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[  SELECT U.USER_NAME,
        U.REAL_NAME,
        A.SUB_DISCUSS_STATUS,
        A.SUB_DISCUSS_BY,
        A.SUB_DISCUSS_CONCLUSION,
        A.DISCUSS_ID,
        A.SUB_DISCUSS_DATE,
        A.CASE_NO,
        A.SUB_DISCUSS_ID,
        A.SUB_DISCUSS_REPLY
   FROM DEV_CLM.T_CLAIM_SUB_DISCUSS A
  left JOIN DEV_PAS.T_UDMP_USER U
     ON U.USER_ID = A.Sub_Discuss_By WHERE ROWNUM <=  1000  ]]>
        <if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
        <if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
        <if test=" discuss_id != null and discuss_id != ''  "><![CDATA[ AND A.DISCUSS_ID = #{discuss_id} ]]></if>
        <![CDATA[ ORDER BY A.SUB_DISCUSS_ID ]]> 
    </select>
    <!-- ===================================责任明细=================================== -->
    <!-- 1 -->
    <select id="findAllClaimBillItemByConditions" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.BILL_ITEM_ID, A.DEDUCT_REASON, A.CALC_AMOUNT, A.CASE_ID, A.DEDUCT_AMOUNT, 
            A.MED_FEE_ITEM, A.DEDUCT_REMARK, A.FEE_AMOUNT, A.BILL_ID,A.OPERATION_CODE,A.PAY_AMOUNT,A.EXPENSE_AMOUNT,A.OTHER_AMOUNT,A.DRUG_DETAIL_ID,A.COST_CONTROL_AMOUNT,A.COST_CONTROL_REMARK,A.IS_ACCEPTABLE FROM DEV_CLM.T_CLAIM_BILL_ITEM A WHERE 1 = 1  ]]>
        <if test=" bill_id  != null "><![CDATA[ AND A.BILL_ID = #{bill_id} ]]></if>
        <if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
        <![CDATA[ ORDER BY A.BILL_ITEM_ID ]]>
    </select>
    <!-- 2 -->
    <select id="findClaimBillByConditions" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ SELECT A.BILL_TYPE, A.TREAT_START, A.CALC_AMOUNT, A.SUM_AMOUNT, A.TREAT_END, 
            A.CASE_ID, A.DEDUCT_AMOUNT,A.OTHER_FLAG, A.OTHER_PAY, A.ACC_DETAIL, A.ICD_CODE, 
            A.BILL_ID, A.BILL_NO, A.TREAT_TYPE,A.OUTPATIENT_TREAT_TYPE, A.HOSPITAL_CODE, (SELECT B.HOSPITAL_NAME FROM DEV_CLM.T_HOSPITAL B WHERE B.HOSPITAL_CODE=A.HOSPITAL_CODE) hospital_name,
       (SELECT C.LEVEL_DESC FROM DEV_CLM.T_CLM_HOSPITAL_LEVEL C WHERE C.LEVEL_CODE IN (SELECT B.HOSPITAL_LEVEL FROM DEV_CLM.T_HOSPITAL B WHERE B.HOSPITAL_CODE=A.HOSPITAL_CODE)) hospital_level,decode(a.treat_type,'0', (select sum(case when aa.treat_type=0  then 1 end ) from  DEV_CLM.t_claim_bill aa where  aa.case_id=a.case_id and (aa.medical_dept_code=a.medical_dept_code or aa.medical_dept_code is null) and  aa.hospital_code=a.hospital_code ))  outpatient_total,a.medical_dept_code,A.SOCI_MEDICAL_TREATMENT,A.ADMINSSION_ID,A.PRE_MIMING_INSPECT_TIME,A.CAR_CELL_TRANS_TIME,A.Third_Pay,A.Medical_Pay,A.App_Liab,A.Liab_Type ,A.IS_SPECIAL_INTERNATIONAL,A.ELEC_BILL_CHECK,A.NON_REAL_BILL,A.VERIFY_RESULT,A.VERIFY_RESULT_DESC,A.ELEC_BILL_ID,A.ELEC_BILL_NUM,A.CHECK_CODE,A.ELEC_BILL_AMOUNT,A.ELEC_BILL_DATE,A.ENTRY_AMOUNT  FROM DEV_CLM.T_CLAIM_BILL A WHERE ROWNUM <=  1000  ]]>
        <if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
        <if test=" bill_id  != null "><![CDATA[ AND A.BILL_ID = #{bill_id} ]]></if>
        <if test=" bill_no  != null "><![CDATA[ AND A.bill_no = #{bill_no} ]]></if>
        <if test=" bill_type  != null "><![CDATA[ AND A.BILL_TYPE = #{bill_type} ]]></if>
        <![CDATA[ ORDER BY A.BILL_ID ]]>
    </select>
    <!-- 3 -->
    <select id="findAllClaimOperation" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ SELECT A.OPERATION_LEVEL, A.REMARK, A.OPERATION_ID, A.PAYMENT_RATE, A.CASE_ID, 
            A.OPERATION_ITEM_CODE,B.OPERATION_ITEM_NAME OPERATION_ITEM_NAME, A.DEPARTMENT_CODE,B.DEPARTMENT_NAME DEPARTMENT_NAME, A.OPERATION_DATE,
            A.HOSPITAL_CODE,C.HOSPITAL_NAME HOSPITAL_NAME FROM DEV_CLM.T_CLAIM_OPERATION A,DEV_CLM.T_OPERATION_LEVEL B,DEV_CLM.T_HOSPITAL C WHERE ROWNUM <=  1000
      AND A.DEPARTMENT_CODE = B.DEPARTMENT_CODE
      AND A.OPERATION_ITEM_CODE = B.OPERATION_ITEM_CODE
      AND A.HOSPITAL_CODE = C.HOSPITAL_CODE]]>
		<include refid="claimOperationWhereCondition" />
		<![CDATA[ ORDER BY A.OPERATION_ID ]]> 
	</select>
	
	<sql id="claimOperationWhereCondition">
		<if test=" operationLevel  != null "><![CDATA[ AND A.OPEATION_LEVEL = #{operationLevel} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" operation_id  != null "><![CDATA[ AND A.OPERATION_ID = #{operation_id} ]]></if>
		<if test=" payment_rate  != null "><![CDATA[ AND A.PAYMENT_RATE = #{payment_rate} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" operation_item_code != null and operation_item_code != ''  "><![CDATA[ AND A.OPERATION_ITEM_CODE = #{operation_item_code} ]]></if>
		<if test=" department_code != null and department_code != ''  "><![CDATA[ AND A.DEPARTMENT_CODE = #{department_code} ]]></if>
		<if test=" operation_date  != null  and  operation_date  != ''  "><![CDATA[ AND A.OPERATION_DATE = #{operation_date} ]]></if>
		<if test=" hospital_code != null and hospital_code != ''  "><![CDATA[ AND A.HOSPITAL_CODE = #{hospital_code} ]]></if>
	</sql>
	<!-- 4 -->
	<select id="findAllClaimSpecialByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.SERVICE_ORG_NAME, A.SUM_AMOUNT, A.CASE_ID, A.SPECIAL_CODE, A.SPECIAL_TYPE, 
			A.SPECIAL_ID, A.FEE_START, A.FEE_END FROM DEV_CLM.T_CLAIM_SPECIAL A WHERE ROWNUM <=  1000  ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<![CDATA[ ORDER BY A.SPECIAL_ID ]]> 
	</select>
	<!-- 5 -->
	<select id="findAllClaimBillPaidByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_CODE, A.PAID_TYPE, A.REMARK, A.OTHER_TYPE, A.SERVICE_ORG_NAME, 
			A.SUM_AMOUNT, A.OWN_EXPENSE, A.CASE_ID, A.OTH_FEE_ITEM, A.SELF_PAY1, 
			A.SELF_PAY2, A.PAID_AMOUNT, A.DATA_ID, A.LIST_ID, A.MEDICAL_FUND_PAYMENT,A.DEPART_AMOUNT, A.FUND_AMOUNT, A.LARGE_HELP_AMOUNT, A.LARGE_HELP_YEAR_AMOUNT, A.FUND_YEAR_AMOUNT
			,A.DIFFERENT_AREA_FLAG ,A.TC_FEE_TOTAL,A.YBJJ_FEE,A.ZGDE_FEE,A.JMDB_FEE
			,A.GWY_FEE ,A.YLJZ_FEE,A.YFBZ_FEE,A.CZDD_FEE,A.QTJJ_FEE,A.DBZ_FLAG
			,A.HOSPITAL_FEE,A.DIAGNOSIS_NUM  FROM DEV_CLM.T_CLAIM_BILL_PAID A WHERE ROWNUM <=  1000  ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.list_id = #{list_id} ]]></if>
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	<!-- 6 -->
	<select id="findAllClaimSurgeryByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MED_ORG_NAME, A.SUM_AMOUNT, A.CASE_ID, A.SURGERY_ID,   A.JOINT_CONTENT_CODE ,  A.JOINT_PAY_RATE ,  
			A.SURGERY_TYPE, A.SURGERY_CODE, A.DIAGNOSE_DATE FROM DEV_CLM.T_CLAIM_SURGERY A WHERE 1 = 1 AND A.CASE_ID = #{case_id} ]]>
		<if test=" surgery_id  != null "><![CDATA[ AND A.surgery_id = #{surgery_id} ]]></if>
		<![CDATA[ ORDER BY A.SURGERY_ID ]]>
	</select>
	<!-- 7 -->
	<select id="findAllClaimInjuryByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ASSESS_ORGAN, A.REMARK, A.DEFORMITY_GRADE, A.PART_DEGREE, A.CASE_ID, 
			A.DEFORMITY_TYPE, A.ASSESS_DATE, A.INJURY_CODE1, A.INJURY_CODE2, 
			A.INJURY_ID, A.PAY_RATE FROM DEV_CLM.T_CLAIM_INJURY A WHERE 1 = 1  ]]>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" injury_id  != null "><![CDATA[ AND A.injury_id = #{injury_id} ]]></if>
		<![CDATA[ ORDER BY A.INJURY_ID ]]>
	</select>
	<!-- ===================================责任明细 END=================================== -->
    <!-- ===================================责任明细 END=================================== -->
    <!--=============================================================================================================  -->
     <!--#34682 查看二核信息  -->
	<select id="queryUWDecision" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
         SELECT  C.*,
	       NVL(HOLDER_CUSTOMER_CLM, HOLDER_CUSTOMER_PAS) AS HOLDER_NAME
	  FROM (SELECT A.CANCEL_REASON,
	               A.CLAIM_UW_TYPE,
	               A.UW_CANCEL_FLAG,
	               A.NOT_INFORM_SITUATION,
	               A.REMARK,
	               A.APPLY_DATE, /**提起核保日期 */
	               A.CLAIM_UW_REASON,
	               A.APPLY_BY,
	               A.UW_STATUS,/**二核状态 */
	               A.CASE_NO,
	               A.REPAIR_BACK_WAY,
	               A.OPEN_ACCOUNT_BANK,
	               A.UW_CONCLUSION,
	               A.POLICY_ID,
	               A.NOT_EFFECT_REASON,
	               A.CASE_ID,
	               A.BANK_NO,
	               A.POLICY_CODE, /**保单号 */
	               (SELECT C.CUSTOMER_NAME
	                  FROM DEV_CLM.T_POLICY_HOLDER PH
	                 INNER JOIN DEV_PAS.T_CUSTOMER C
	                    ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	                 WHERE PH.CASE_ID = A.CASE_ID
	                   AND PH.POLICY_CODE = A.POLICY_CODE
	                   AND PH.CUR_FLAG = 1
	                   AND ROWNUM = 1) AS HOLDER_CUSTOMER_CLM, /*投保人姓名 */
	               (SELECT C.CUSTOMER_NAME
	                  FROM DEV_PAS.T_POLICY_HOLDER PH
	                 INNER JOIN DEV_PAS.T_CUSTOMER C
	                    ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	                 WHERE PH.POLICY_CODE = A.POLICY_CODE) AS HOLDER_CUSTOMER_PAS, /*投保人姓名 */
	               A.INSERT_TIME,
	               A.CLM_UW_ID,
	               P.UW_POLICY_ID,
	               P.uw_user_id, /**核保用户ID */
	               UU.user_name, /**核保用户*/
	               (select count(*) from DEV_CLM.T_UW_BUSI_PROD B where B.UW_POLICY_ID = a.UW_POLICY_ID and B.decision_code != '10') is_Standard,
	               P.UW_FINISH_TIME, /**核保完成时间 */
	               P.UW_ID,
	               P.UW_STATUS AS puw_status /**核保状态 */
	          FROM DEV_CLM.T_CLAIM_UW A
	          LEFT JOIN DEV_CLM.T_UW_POLICY P
	            ON A.UW_POLICY_ID = P.UW_POLICY_ID
	          LEFT JOIN DEV_PAS.T_UDMP_USER UU
	            ON UU.USER_ID = P.UW_USER_ID
	          WHERE A.UW_STATUS != '0'
	          AND A.UW_POLICY_ID IS NOT NULL
              AND A.POLICY_ID = P.POLICY_ID
	          ]]>
	           <if test=" caseNo  != null and caseNo !=''"><![CDATA[  and A.CASE_ID = #{caseNo} ]]></if>
	           <if test=" uw_Batch_No  != null and uw_Batch_No !=''"><![CDATA[  and A.UW_BATCH_NO = #{uw_Batch_No} ]]></if>
	        <![CDATA[
	           ) C 
		 ]]>
	</select>
	<!-- 通过赔案查询二核批次信息 -->
    <select id="queryUWBatch" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[
         SELECT distinct  
                   A.CASE_ID, 
                   A.UW_BATCH_NO,
                   (SELECT COUNT(*) 
                    FROM APP___CLM__DBUSER.T_CLAIM_UW UU 
                    INNER JOIN APP___CLM__DBUSER.T_UW_BUSI_PROD TT ON UU.UW_POLICY_ID = TT.UW_POLICY_ID
                    WHERE TT.decision_code != '10' AND UU.UW_BATCH_NO = A.UW_BATCH_NO AND UU.CASE_ID = A.CASE_ID
                       ) is_Standard,
                   (CASE WHEN (SELECT COUNT(*) FROM APP___CLM__DBUSER.T_CLAIM_UW BB WHERE
                               BB.UW_BATCH_NO = A.UW_BATCH_NO AND BB.CASE_ID = A.CASE_ID AND (BB.UW_STATUS = '1' OR BB.UW_STATUS = '4')) > 0 
                         THEN '1'
                         ELSE A.UW_STATUS END ) UW_STATUS,    
                   A.APPLY_DATE, /**提起核保日期 */
                  (CASE
                   WHEN A.UW_status ='1' or A.UW_status ='4' THEN
                  (select distinct P.UW_FINISH_TIME FROM APP___CLM__DBUSER.T_UW_POLICY P
                   where A.UW_POLICY_ID = P.UW_POLICY_ID)
                   ELSE NULL
               END) AS UW_FINISH_TIME, /**核保完成时间 */
                   A.INSERT_TIME
              FROM APP___CLM__DBUSER.T_CLAIM_UW A
              where A.CASE_ID = #{caseNo}
              order by A.Apply_Date desc, A.INSERT_TIME DESC ]]>
  	</select>
  	
  	<select id="queryClaimUwCancelByCaseId" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[
         SELECT  C.*,
	       NVL(HOLDER_CUSTOMER_CLM, HOLDER_CUSTOMER_PAS) AS HOLDER_NAME
	  FROM (SELECT A.CANCEL_REASON,
	               A.CLAIM_UW_TYPE,
	               A.UW_CANCEL_FLAG,
	               A.NOT_INFORM_SITUATION,
	               A.REMARK,
	               A.APPLY_DATE, /**提起核保日期 */
	               A.CLAIM_UW_REASON,
	               A.APPLY_BY,
	               A.UW_STATUS,/**二核状态 */
	               A.CASE_NO,
	               A.REPAIR_BACK_WAY,
	               A.OPEN_ACCOUNT_BANK,
	               A.UW_CONCLUSION,
	               A.POLICY_ID,
	               A.NOT_EFFECT_REASON,
	               A.CASE_ID,
	               A.BANK_NO,
	               A.POLICY_CODE, /**保单号 */
	               (SELECT C.CUSTOMER_NAME
	                  FROM DEV_CLM.T_POLICY_HOLDER PH
	                 INNER JOIN DEV_PAS.T_CUSTOMER C
	                    ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	                 WHERE PH.CASE_ID = A.CASE_ID
	                   AND PH.POLICY_CODE = A.POLICY_CODE
	                   AND PH.CUR_FLAG = 1
	                   AND ROWNUM = 1) AS HOLDER_CUSTOMER_CLM, /*投保人姓名 */
	               (SELECT C.CUSTOMER_NAME
	                  FROM DEV_PAS.T_POLICY_HOLDER PH
	                 INNER JOIN DEV_PAS.T_CUSTOMER C
	                    ON PH.CUSTOMER_ID = C.CUSTOMER_ID
	                 WHERE PH.POLICY_CODE = A.POLICY_CODE) AS HOLDER_CUSTOMER_PAS, /*投保人姓名 */
	               A.INSERT_TIME,
	               (select count(*) from DEV_CLM.T_UW_BUSI_PROD B where B.UW_POLICY_ID = a.UW_POLICY_ID and B.decision_code != '10') is_Standard,
	               A.CLM_UW_ID
	          FROM DEV_CLM.T_CLAIM_UW A
	          WHERE A.UW_STATUS = '2'
	          ]]>
		<if test=" caseNo  != null and caseNo !=''"><![CDATA[  and A.CASE_ID = #{caseNo} ]]></if>
		<if test=" uw_Batch_No  != null and uw_Batch_No !=''"><![CDATA[  and A.UW_BATCH_NO = #{uw_Batch_No} ]]></if>
	        <![CDATA[
	           ) C 
		 ]]>
	</select>
	<!--查询险种清单明细  -->
	<select id="queryUWProductDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
         SELECT PRO.BUSI_PROD_CODE, /**险种*/
	       (SELECT BP.PRODUCT_NAME_SYS
	          FROM DEV_PDS. T_BUSINESS_PRODUCT BP
	         WHERE BP.PRODUCT_CODE_SYS = PRO.BUSI_PROD_CODE) AS BUSI_PROD_NAME, /**险种名称 */
	       PRO.POLICY_ID,
	       PRO.PRODUCT_CODE, /**责任组*/
	       (SELECT RL.PRODUCT_NAME
	          FROM DEV_PDS.T_PRODUCT_LIFE RL, DEV_PAS.T_CONTRACT_PRODUCT CP
	         WHERE CP.PRODUCT_ID = RL.PRODUCT_ID
	           AND CP.PRODUCT_CODE = PRO.PRODUCT_CODE
	           AND cp.policy_id = busi.policy_id
	           AND CP.Busi_Item_Id = busi.busi_item_id) AS PRODUCT_NAME, /**责任组名称 */
	       PRO.COVERAGE_PERIOD, /**保障年期类型 */
	       PRO.COVERAGE_YEAR, /**保障年期 */
	       PRO.AMOUNT, /**保额 */
	       PRO.UNIT, /**份数 */
	       PRO.BENEFIT_LEVEL, /**档次 */
	       PRO.TOTAL_PREM_AF,
	       PRO.STD_PREM_AF, /**期缴保险费 */
	       PRO.DECISION_CODE, /**理赔核保决定 */
	       PRO.EXTRA_PREM_AF,
	       PRO.UW_ID,
	       PRO.UW_PRD_ID,
	       PRO.ITEM_ID,
	       EP.EM_VALUE, /**加费评点 */
	       PRO.PAID_REFUND_FEE /**加费金额 */
	  FROM DEV_CLM.T_UW_BUSI_PROD BUSI
	  LEFT JOIN DEV_CLM.T_UW_PRODUCT PRO
	    ON BUSI.POLICY_ID = PRO.POLICY_ID
	   AND BUSI.UW_BUSI_ID = PRO.UW_BUSI_ID
	  LEFT JOIN DEV_CLM.T_UW_EXTRA_PREM EP
	    ON EP.POLICY_ID = BUSI.POLICY_ID
	   AND EP.UW_ID = PRO.UW_ID
	   AND EP.BUSI_ITEM_ID = BUSI.BUSI_ITEM_ID
	   AND EP.BUSI_PROD_CODE = BUSI.BUSI_PROD_CODE
	 WHERE 1 = 1]]>
	   <if test=" policy_id  != null and policy_id !=''"><![CDATA[ AND BUSI.POLICY_ID = #{policy_id} ]]></if>
	   <if test=" uw_policy_id  != null and uw_policy_id !=''"><![CDATA[  AND BUSI.UW_POLICY_ID = #{uw_policy_id} ]]></if>
	</select>
	<!--未告知情况,核保结论生效,核保结论撤销  -->
	<select id="queryOtherInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
         SELECT A.NOT_INFORM_SITUATION, /**未告知信息 */
		       A.REMARK, /**备注信息 */
		       A.UW_CONCLUSION, /**二核处理结论 */
		       A.REPAIR_BACK_WAY, /**补/退费方式 */
		       A.OPEN_ACCOUNT_BANK, /**开户银行 */
		       A.BANK_NO, /**银行账号 */
		       A.NOT_EFFECT_REASON, /**不生效原因 */
		       A.UW_CANCEL_FLAG, /**二核撤销标识 */
		       A.CANCEL_REASON, /**撤销原因 */
		       A.CLAIM_UW_TYPE/**二核类型 */
		  FROM DEV_CLM.T_CLAIM_UW A
		 WHERE 1=1]]>
		 <if test=" clm_uw_id  != null and clm_uw_id !=''"><![CDATA[AND A.CLM_UW_ID = #{clm_uw_id} ]]></if>
           
	</select>
    <!--  #38942需求变更 OCR识别 -->
     <select id="queryOcrBeneInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
           SELECT A.MOBILE_BENE_NATION,
               TO_CHAR(A.MOBILE_BENE_CERTI_END, 'yyyy-mm-dd') AS MOBILE_BENE_CERTI_END,
               A.MOBILE_BENE_ID,
               A.MOBILE_CASE_ID,
               A.MOBILE_BENE_NO,
               A.MOBILE_BENE_BIRTH,
               A.MOBILE_BENE_RELATION,
               A.MOBILE_BENE_CERTI_NO,
               A.MOBILE_BENE_NAME,
               TO_CHAR(A.MOBILE_BENE_CERTI_START, 'yyyy-mm-dd') AS MOBILE_BENE_CERTI_START,
               (MONTHS_BETWEEN(A.MOBILE_BENE_CERTI_END, A.MOBILE_BENE_CERTI_START) / 12) AS BENE_TIME, /*受益人有效期间*/
               A.MOBILE_BENE_SEX,
               A.MOBILE_BENE_CERTI_TYPE
          FROM DEV_CLM.T_CLAIM_BENE_MOBILE A
         WHERE A.MOBILE_CASE_ID = #{caseNo} 
        ]]>
    </select>
    
    <select id="queryOcrPayeeInfo" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[ 
        SELECT A.MOBILE_PAYEE_ID,
               A.MOBILE_PAYEE_NO,
               A.MOBILE_PAYEE_RELATION,
               A.MOBILE_CASE_ID,
               A.MOBILE_PAYEE_SEX,
               A.MOBILE_PAYEE_NATION,
               A.MOBILE_PAYEE_CERTI_NO,
               A.MOBILE_ACCOUNT_NAME,
               A.MOBILE_PAY_MODE,
               A.MOBILE_PAYEE_CERTI_TYPE,
               TO_CHAR(A.MOBILE_PAYEE_CERTI_END, 'yyyy-mm-dd') AS MOBILE_PAYEE_CERTI_END,
               TO_CHAR(A.MOBILE_PAYEE_CERTI_START, 'yyyy-mm-dd') AS MOBILE_PAYEE_CERTI_START,
               (MONTHS_BETWEEN(A.MOBILE_PAYEE_CERTI_END, A.MOBILE_PAYEE_CERTI_START) / 12) AS PAYEE_TIME, /*受益人有效期间*/
               A.MOBILE_ACCOUNT_NO,
               A.MOBILE_PAYEE_NAME,
               A.MOBILE_PAYEE_BIRTH,
               A.MOBILE_BANK_CODE
          FROM DEV_CLM.T_CLAIM_PAYEE_MOBILE A
         WHERE A.MOBILE_CASE_ID = #{caseNo}
        ]]>
    </select>
     <!-- #38942需求变更 OCR识别 -->
    <!--理赔二核加费明细  -->
	<select id="queryUWExtraPremDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
	         SELECT EP.EXTRA_TYPE, /*加费类型*/
	         		EP.ADD_ARITH,/*加费计算方式*/
			       EP.INTO_EFFECT_TYPE, /*生效类别*/
			       EP.EXTRA_PERIOD, /*加费期数*/
			       EP.START_DATE, /*开始日期*/
			       EP.END_DATE, /*终止日期*/
			       EP.EXTRA_PERC, /*加费比例*/
			       EP.EXTRA_PARA, /*加费金额*/
			       EP.EM_VALUE, /*EM值*/
			       EP.EXTRA_PREM, /*额外保险费*/
			       UDR.REASON_CONTENT /*原因*/
			  FROM DEV_CLM.T_UW_EXTRA_PREM EP
			  LEFT JOIN DEV_CLM.T_UW_DECISION_REASON UDR
			    ON UDR.EXTRA_PREM_ID = EP.LIST_ID
			    AND UDR.REASON_TYPE = #{reason_type}
			 WHERE 1 = 1
	         ]]>
	   <if test=" uw_prd_id  != null and uw_prd_id !=''"><![CDATA[  AND EP.UW_PRD_ID = #{uw_prd_id} ]]></if>
	</select>
	<!--理赔二核特别约定  -->
	<select id="queryUWConditionDetail" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
	         SELECT BP.PRODUCT_NAME_SYS, /*产品名称*/
			       PRL.PRODUCT_NAME, /*责任组名称*/
			       UC.CONDITION_DESC, /*描述*/
			       UC.BUSI_PROD_CODE, /*险种编码*/
			       C.CUSTOMER_NAME, /*被保人姓名*/
			       DR.REASON_CONTENT /*原因*/
			  FROM DEV_CLM.T_UW_CONDITION UC
			  LEFT JOIN DEV_PDS.T_BUSINESS_PRODUCT BP
			    ON UC.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
			  LEFT JOIN DEV_PAS.T_CONTRACT_PRODUCT CP
			    ON CP.ITEM_ID = UC.ITEM_ID
			  LEFT JOIN DEV_PAS.T_PRODUCT_LIFE PRL
			    ON PRL.PRODUCT_ID = CP.PRODUCT_ID
			  LEFT JOIN DEV_PAS.T_CUSTOMER C
			    ON C.CUSTOMER_ID = UC.INSURED_CUSTOMER_ID
			  LEFT JOIN DEV_CLM.T_UW_DECISION_REASON DR
			    ON DR.CONDITION_LIST_ID = UC.LIST_ID
			    AND DR.REASON_TYPE = #{reason_type}
			 WHERE 1 = 1
	         ]]>
	   <if test="uw_prd_id != null and uw_prd_id!=''"><![CDATA[AND UC.UW_PRD_ID = #{uw_prd_id} ]]></if>
	</select>
	
	<!-- 审核-调查/协谈/二核/合议-处理查看二核-查询“待二核”的二核信息 -->
	<select id="queryClaimUwByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.CANCEL_REASON,
			       A.CLAIM_UW_TYPE,
			       A.UW_CANCEL_FLAG,
			       A.NOT_INFORM_SITUATION,
			       A.REMARK,
			       A.APPLY_DATE,
			       A.CLAIM_UW_REASON,
			       A.APPLY_BY,
			       A.UW_STATUS,
			       A.CASE_NO,
			       A.REPAIR_BACK_WAY,
			       A.OPEN_ACCOUNT_BANK,
			       A.UW_CONCLUSION,
			       A.POLICY_ID,
			       A.NOT_EFFECT_REASON,
			       A.CLM_UW_ID,
			       A.CASE_ID,
			       A.BANK_NO,
			       A.POLICY_CODE,
			       (SELECT C.CUSTOMER_NAME
			          FROM DEV_CLM.T_POLICY_HOLDER PH
			         INNER JOIN DEV_PAS.T_CUSTOMER C
			            ON PH.CUSTOMER_ID = C.CUSTOMER_ID
			         WHERE PH.CASE_ID = A.CASE_ID
			           AND PH.POLICY_CODE = A.POLICY_CODE
			           AND PH.CUR_FLAG = 1
			           AND ROWNUM = 1) AS HOLDER_NAME, /*投保人姓名 */
			       	A.UNIT_NUMBER,
			       	A.UW_CONCLUSION_TIME,
			       	(select count(*) from DEV_CLM.T_UW_BUSI_PROD B where B.UW_POLICY_ID = a.UW_POLICY_ID and B.decision_code != '10') is_Standard,
			       	A.UW_NUCLEUS_TIME
			       	FROM dev_clm.t_claim_uw a
			   where  A.UW_STATUS = '0'
			     AND A.UW_POLICY_ID IS NULL
			     AND A.CASE_ID = #{caseNo}
			     and A.UW_BATCH_NO = #{uw_Batch_No}
			    AND NOT EXISTS (SELECT 1
			           FROM dev_clm.t_claim_uw 
			          WHERE A.POLICY_CODE = POLICY_CODE
			            AND A.CLM_UW_ID < CLM_UW_ID)]]> 
	</select>
	<!-- 审核-调查/协谈/二核/合议-处理查看二核-查询“待二核”的二核信息的核保状态核保人 -->
	<select id="queryClaimUwByCaseIdStatus" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ select user_name,uw_user_id,puw_status from (
             SELECT UU.user_name,
                    tum.uw_user_id,
		                tum.uw_status AS puw_status
               FROM dev_uw.t_uw_master tum
               LEFT JOIN DEV_PAS.T_UDMP_USER UU
                 ON UU.USER_ID = tum.UW_USER_ID
                 WHERE TUM.BIZ_CODE = #{caseNo}
                 ORDER BY uw_id desc) where rownum = 1]]> 
	</select>
	<select id="queryUwIdByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[ SELECT um.UW_ID,um.BIZ_CODE FROM deV_CLM.T_UW_MASTER um ,
			       deV_CLM.T_claim_uw uu   WHERE 
			       uu.CASE_NO=um.BIZ_CODE AND uu.UW_STATUS='3' 
			       AND um.UW_STATUS='02' AND 
			       uu.case_id=#{caseNo}]]>
	</select>
	
	<select id="queryClaimBillEntryByMainListId" resultType="java.util.Map" parameterType="java.util.Map">
        <!--获取电票入账明细-->	
				<![CDATA[ SELECT 
				    A.ENTRY_DATE,
				    A.ENTRY_AMOUNT,
				    A.LIST_ID,
				    A.MAIN_LIST_ID,
				    B.ELEC_BILL_NUM
				FROM 
				    DEV_CLM.T_CLAIM_BILL_ENTRY A
				INNER JOIN 
				    DEV_CLM.T_CLAIM_BILL B ON A.MAIN_LIST_ID = B.BILL_ID
				WHERE 
				    B.CASE_ID = #{case_id} ]]>
		<![CDATA[ ORDER BY A.ENTRY_DATE DESC ]]>
	</select>		
		
	<!-- 理赔综合查询-理赔查询 -->
	<select id="queryClmInffo" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
       SELECT C.*
  FROM (SELECT ROWNUM RN, 
          (  
          CASE
                 WHEN UW_STATUS_1 ='0' THEN
                  '待二核'
                 WHEN UW_STATUS_1 ='1' THEN
                  '二核完成'
                 WHEN UW_STATUS_1 ='2' THEN
                  '已撤销' 
                 WHEN UW_STATUS_1 ='3' THEN
                  '二核回退' 
                 WHEN UW_STATUS_1 ='4' THEN
                  '待二核加费' 
                 ELSE
                  '-'
               END) AS UW_STATUS,
           (
          CASE WHEN CHAKAN1 IS NULL THEN '-'
          ELSE CHAKAN1
          END
          ) AS CHAKAN,
          (CASE
                 WHEN AUDIT_PERMISSION_NAME1 IS NULL THEN
                  '-'
                 ELSE
                  AUDIT_PERMISSION_NAME1
               END) AS AUDIT_PERMISSION_NAME,
                (CASE
                 WHEN APPROVE_PERMISSION_NAME1 IS NULL THEN
                  '-'
                 ELSE
                  APPROVE_PERMISSION_NAME1
               END) AS APPROVE_PERMISSION_NAME,
          B.*,
          (CASE WHEN B.PAY_COUNT<=0 THEN null WHEN B.UN_PAY_COUNT>0 AND B.PAY_COUNT>0 THEN '未支付' WHEN B.UN_PAY_COUNT<=0 AND B.PAY_COUNT>0 THEN '已支付'  END) AS FEE_STATUS
          FROM (
                SELECT DISTINCT 
        ]]>
		<include refid="claimQueryMainField" />
         <![CDATA[ 
                  FROM DEV_CLM.T_CLAIM_CASE CC
                  left join DEV_CLM.T_CLAIM_ACCIDENT CA
                    on CC.ACCIDENT_ID = CA.ACCIDENT_ID                
                  left join DEV_CLM.T_INSURED_LIST CT
                    ON CC.CASE_ID = CC.CASE_ID
                   AND CC.INSURED_ID = CT.CUSTOMER_ID
                   AND CT.CUR_FLAG = '1'
                  left join DEV_CLM.T_CUSTOMER CM
                    ON CT.CUSTOMER_ID = CM.CUSTOMER_ID
                 WHERE 1 = 1 AND CC.CASE_STATUS != '99'
		 ]]>
		<if test=" caseNo != null and caseNo != ''  "><![CDATA[AND CC.CASE_NO=  #{caseNo} ]]></if>
		<if test=" policyNo != null and policyNo != ''  ">
			AND Exists(
			SELECT CP.CASE_ID FROM DEV_CLM.T_CLAIM_POLICY CP WHERE
			CP.POLICY_CODE=#{policyNo} AND CP.CASE_ID=CC.CASE_ID
			UNION
			SELECT CM.CASE_ID FROM DEV_CLM.T_CONTRACT_MASTER CM WHERE
			CM.POLICY_CODE=#{policyNo}
			AND CM.CUR_FLAG=1 AND CM.CASE_ID=CC.CASE_ID
			)
		</if>
		<if test=" accDate != null and accDate != ''  "><![CDATA[AND TO_CHAR(CA.acc_date,'yyyy-MM-dd')= #{accDate} ]]></if>
		<if test=" outDate != null and outDate != ''  "><![CDATA[AND TO_CHAR(CA.ACC_DATE,'yyyy-MM-dd') = #{outDate} ]]></if>
		<if test=" outer != null and outer != ''  "><![CDATA[AND  CM.CUSTOMER_NAME = #{outer} ]]></if>
		<if test=" outerCutNo != null and outerCutNo != ''  "><![CDATA[AND CC.INSURED_ID = #{outerCutNo} ]]></if>
		<if test=" cardId != null and cardId != ''  "> AND CM.CUSTOMER_CERTI_CODE= #{cardId} </if>
		<if test=" recordOrg != null and recordOrg != ''  "><![CDATA[AND CC.ORGAN_CODE = #{recordOrg} ]]></if>
		<if test=" recordBeginDate != null and recordBeginDate != ''  "><![CDATA[AND TO_CHAR(CC.REGISTE_TIME,'yyyy-MM-dd') >= #{recordBeginDate} ]]></if>
		<if test=" recordEndDate != null and recordEndDate != ''  "><![CDATA[AND TO_CHAR(CC.REGISTE_TIME,'yyyy-MM-dd') <= #{recordEndDate} ]]></if>
		<if test=" endCaseDate1 != null and endCaseDate1 != ''  "><![CDATA[AND TO_CHAR(CC.END_CASE_TIME,'yyyy-MM-dd') >= #{endCaseDate1} ]]></if>
		<if test=" endCaseDate2 != null and endCaseDate2 != ''  "><![CDATA[AND TO_CHAR(CC.END_CASE_TIME,'yyyy-MM-dd') <= #{endCaseDate2} ]]></if>
	 <![CDATA[ ) B WHERE ROWNUM <= #{LESS_NUM}  ]]> 
        <![CDATA[ )  C
        WHERE C.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 理赔综合查询理赔查询 "!"中回退信息查询 -->
	<select id="findBackCaseByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
         <![CDATA[  select a.case_id, a.case_no, a.related_no, b.accident_no
					  from Dev_clm.T_CLAIM_CASE a
					  left join Dev_clm.T_CLAIM_ACCIDENT b
					    on a.accident_id = b.accident_id
					 where a.case_no in (select t.related_no from Dev_clm.T_CLAIM_CASE t where 1 = 1  ]]>
		     <if test="case_id !=null and case_id != ''"><![CDATA[ and t.case_id = #{case_id} ]]></if>
		<![CDATA[  ) ]]>
	</select>
	
	<select id="queryClmInffoTotal" resultType="java.lang.Integer"
		parameterType="java.util.Map">
        <![CDATA[ 
       SELECT COUNT(1)
  FROM (SELECT ROWNUM RN, 
          (  
          CASE
                 WHEN UW_STATUS_1 ='0' THEN
                  '待二核'
                 WHEN UW_STATUS_1 ='1' THEN
                  '二核完成'
                 WHEN UW_STATUS_1 ='2' THEN
                  '已撤销' 
                 WHEN UW_STATUS_1 ='3' THEN
                  '二核回退' 
                 WHEN UW_STATUS_1 ='4' THEN
                  '待二核加费' 
                 ELSE
                  '-'
               END) AS UW_STATUS,
           (
          CASE WHEN CHAKAN1 IS NULL THEN '-'
          ELSE CHAKAN1
          END
          ) AS CHAKAN,
          (CASE
                 WHEN AUDIT_PERMISSION_NAME1 IS NULL THEN
                  '-'
                 ELSE
                  AUDIT_PERMISSION_NAME1
               END) AS AUDIT_PERMISSION_NAME,
                (CASE
                 WHEN APPROVE_PERMISSION_NAME1 IS NULL THEN
                  '-'
                 ELSE
                  APPROVE_PERMISSION_NAME1
               END) AS APPROVE_PERMISSION_NAME,
          B.*,
          (CASE WHEN B.PAY_COUNT<=0 THEN null WHEN B.UN_PAY_COUNT>0 AND B.PAY_COUNT>0 THEN '未支付' WHEN B.UN_PAY_COUNT<=0 AND B.PAY_COUNT>0 THEN '已支付'  END) AS FEE_STATUS
          FROM (
                SELECT 
        ]]>
		<include refid="claimQueryMainField" />
         <![CDATA[ 
                  FROM DEV_CLM.T_CLAIM_CASE CC
                  left join DEV_CLM.T_CLAIM_ACCIDENT CA
                    on CC.ACCIDENT_ID = CA.ACCIDENT_ID                
                  left join DEV_CLM.T_INSURED_LIST CT
                    ON CC.CASE_ID = CC.CASE_ID
                   AND CC.INSURED_ID = CT.CUSTOMER_ID
                   AND CT.CUR_FLAG = '1'
                  left join DEV_CLM.T_CUSTOMER CM
                    ON CT.CUSTOMER_ID = CM.CUSTOMER_ID
                 WHERE 1 = 1 AND CC.CASE_STATUS != '99'
		 ]]>
		<if test=" caseNo != null and caseNo != ''  "><![CDATA[AND CC.CASE_NO=  #{caseNo} ]]></if>
		<if test=" policyNo != null and policyNo != ''  ">
			AND Exists(
			SELECT CP.CASE_ID FROM DEV_CLM.T_CLAIM_POLICY CP WHERE
			CP.POLICY_CODE=#{policyNo} AND CP.CASE_ID=CC.CASE_ID
			UNION
			SELECT CM.CASE_ID FROM DEV_CLM.T_CONTRACT_MASTER CM WHERE
			CM.POLICY_CODE=#{policyNo}
			AND CM.CUR_FLAG=1 AND CM.CASE_ID=CC.CASE_ID
			)
		</if>
		<if test=" accDate != null and accDate != ''  "><![CDATA[AND TO_CHAR(CA.acc_date,'yyyy-MM-dd')= #{accDate} ]]></if>
		<if test=" outDate != null and outDate != ''  "><![CDATA[AND TO_CHAR(CA.ACC_DATE,'yyyy-MM-dd') = #{outDate} ]]></if>
		<if test=" outer != null and outer != ''  "><![CDATA[AND  CM.CUSTOMER_NAME = #{outer} ]]></if>
		<if test=" outerCutNo != null and outerCutNo != ''  "><![CDATA[AND CC.INSURED_ID = #{outerCutNo} ]]></if>
		<if test=" cardId != null and cardId != ''  "> AND CM.CUSTOMER_CERTI_CODE= #{cardId} </if>
		<if test=" recordOrg != null and recordOrg != ''  "><![CDATA[AND CC.ORGAN_CODE = #{recordOrg} ]]></if>
		<if test=" recordBeginDate != null and recordBeginDate != ''  "><![CDATA[AND TO_CHAR(CC.REGISTE_TIME,'yyyy-MM-dd') >= #{recordBeginDate} ]]></if>
		<if test=" recordEndDate != null and recordEndDate != ''  "><![CDATA[AND TO_CHAR(CC.REGISTE_TIME,'yyyy-MM-dd') <= #{recordEndDate} ]]></if>
		<if test=" endCaseDate1 != null and endCaseDate1 != ''  "><![CDATA[AND TO_CHAR(CC.END_CASE_TIME,'yyyy-MM-dd') >= #{endCaseDate1} ]]></if>
		<if test=" endCaseDate2 != null and endCaseDate2 != ''  "><![CDATA[AND TO_CHAR(CC.END_CASE_TIME,'yyyy-MM-dd') <= #{endCaseDate2} ]]></if>
	 <![CDATA[ ) B WHERE ROWNUM <= #{LESS_NUM}  ]]> 
        <![CDATA[ )  C
        WHERE C.RN > #{GREATER_NUM} ]]>
	</select>
</mapper>
