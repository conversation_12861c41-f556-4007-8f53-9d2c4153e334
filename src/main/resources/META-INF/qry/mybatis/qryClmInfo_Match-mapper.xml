<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper 
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.clmMatch">
	<!-- 理算结果 -赔案计算信息 start -->
	<select id="matchPolicy" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
       		SELECT  CC.ADVANCE_PAY,CC.CALC_PAY,CC.BALANCE_PAY,
       				CC.ACTUAL_PAY,CC.REJECT_PAY 
			  FROM  DEV_CLM.T_CLAIM_CASE CC 
			WHERE  1=1 
		 ]]>
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND CC.CASE_ID = #{caseID} ]]></if>
	</select>
	<!-- 理算结果 -赔案计算信息 end -->

	<!-- 理算结果 -保单计算信息 start -->
	<select id="matchBusiProd" resultType="java.util.Map"
		parameterType="java.util.Map">
        <![CDATA[ 
              select distinct bp.policy_code,
               sc.claim_type,
               bp.valid_date VALIDATE_DATE,
               bp.expire_date, 
               (select T_CONTRACT_EXTEND.Pay_Due_Date from    DEV_CLM.T_CONTRACT_EXTEND  where  policy_id=bp.policy_id
               and cur_flag='0'
                and rownum=1) due_date,
               bp.busi_prod_code,
               p.product_name_std,
               sum(cl.actual_pay) actual_pay,
               bp.busi_item_id,
               sum(cl.calc_pay) calc_pay,
               '',
               ''
             from DEV_CLM.T_CLAIM_SUB_CASE sc, DEV_CLM.T_CLAIM_BUSI_PROD bp, DEV_PDS.T_BUSINESS_PRODUCT p, DEV_CLM.T_CLAIM_LIAB cl
            where sc.case_id = bp.case_id
           and cl.busi_item_id = bp.busi_item_id
           and sc.sub_case_id = cl.sub_case_id
           and bp.busi_prod_code = p.product_code_sys
           and sc.case_id = #{caseID}
           GROUP BY  bp.policy_code,
           bp.policy_id,
               sc.claim_type,
               bp.valid_date,
               bp.expire_date,
               bp.busi_prod_code,
               p.product_name_std, 
               bp.busi_item_id
		 ]]>
	</select>
	<!-- 理算结果 -保单计算信息 end -->

 <!-- 理赔类型计算信息 -->
    <!-- 理赔类型为08医疗时，才有账单金额，为医疗类型时，核赔赔付金额需要计算：
    等 （帐单金额-社保给付-第三方给付）>没有津贴的实际支付金额时，核赔赔付金额为没有津贴的实际支付金额+支付的津贴,否则为（帐单金额-社保给付-第三方给付）+支付的津贴。
     -->
    <select id="queryClaimLiabForMatchResultByType" resultType="java.util.Map"
        parameterType="java.util.Map">
        <![CDATA[
        SELECT C.*,
         (
         CASE WHEN CLAIM_TYPE='08' AND (BILL_AMOUNT-PAID_AMOUNT-SUM_AMOUNT)>NOJINACTUAL_PAY THEN NOJINACTUAL_PAY+JINACTUAL_PAY
         WHEN CLAIM_TYPE='08' AND (BILL_AMOUNT-PAID_AMOUNT-SUM_AMOUNT)<=NOJINACTUAL_PAY THEN (BILL_AMOUNT-PAID_AMOUNT-SUM_AMOUNT)+JINACTUAL_PAY
          WHEN CLAIM_TYPE！='08' THEN CALC_PAY
          END
         ) AS ACTUAL_PAY_NEW
         FROM 
         (
         SELECT B.*,  
         NVL((SELECT SUM(A.ACTUAL_PAY) FROM dev_clm.T_CLAIM_LIAB A WHERE A.CASE_ID = B.CASE_ID AND A.IS_SUBSIDY = 1),0) JINACTUAL_PAY,
        NVL((SELECT SUM(A.ACTUAL_PAY) FROM dev_clm.T_CLAIM_LIAB A WHERE A.CASE_ID =  B.CASE_ID AND A.IS_SUBSIDY != 1),0) NOJINACTUAL_PAY,
        NVL((SELECT SUM(D.PAID_AMOUNT) FROM dev_clm.T_CLAIM_BILL_PAID D WHERE D.CASE_ID =  B.CASE_ID  AND D.OTHER_TYPE = '1'),0) PAID_AMOUNT,
        NVL((SELECT SUM(D.PAID_AMOUNT) FROM dev_clm.T_CLAIM_BILL_PAID D WHERE D.CASE_ID =  B.CASE_ID AND D.OTHER_TYPE = '2'),0) SUM_AMOUNT,
        (case B.CLAIM_TYPE when '08' then (select SUM(SUM_AMOUNT)from dev_clm.T_CLAIM_BILL CB WHERE CB.CASE_ID=B.CASE_ID) else null end) AS BILL_AMOUNT
         FROM (
         SELECT A.CASE_ID,A.CLAIM_TYPE, 
                   SUM(A.CALC_PAY) CALC_PAY,
                   SUM(A.ACTUAL_PAY) ACTUAL_PAY ,
                   SUM(A.BASIC_PAY) BASIC_PAY      
          FROM dev_clm.T_CLAIM_LIAB A, dev_clm.T_CLAIM_SUB_CASE B
          WHERE 1 = 1
            AND A.SUB_CASE_ID = B.SUB_CASE_ID 
             AND A.CLAIM_TYPE = B.CLAIM_TYPE
            AND A.CASE_ID=#{caseID}
            GROUP BY A.CASE_ID,A.CLAIM_TYPE
          ) B
          ) C  
        ]]>
    </select>
	<!-- 理算结果 -保项赔付结论 start -->
	<select id="matchLib" resultType="java.util.Map" parameterType="java.util.Map">
        <![CDATA[ 
       		 SELECT CL.POLICY_CODE,CL.ITEM_ID,(SELECT T.LIAB_NAME FROM DEV_CLM.T_LIABILITY T WHERE T.LIAB_ID=CL.LIAB_ID) LIAB_NAME,
       		 CL.LIAB_ID,CL.LIAB_START_DATE,CL.LIAB_END_DATE,'保额' BAOE,
		       		 '年度红利' NDHL,'终了红利' ZLHL,CL.CALC_PAY,CL.ADVANCE_PAY ,
					 CL.LIAB_CONCLUSION,CL.IS_COMMON,CL.RESIST_FLAG,CL.LIAB_ADJUST_REASON,
					 CL.ADJUST_REMARK 
			 FROM  DEV_CLM.T_CLAIM_LIAB CL 
			 WHERE  1=1
		 ]]>
		<if test=" caseID != null and caseID != ''  "><![CDATA[AND CL.CASE_ID = #{caseID} ]]></if>
	</select>
	<!-- 理算结果 -保项赔付结论 end -->

</mapper>
