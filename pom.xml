<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
     <version>521.0.1</version>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>qry-impl</artifactId>
	<packaging>jar</packaging>
	<groupId>com.nci.core.qry</groupId>
	<parent>
		<groupId>com.nci.udmp</groupId>
		<artifactId>udmp-parent</artifactId>
		<version>0.5.5.3</version>
	</parent>
	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>qry-interface</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.3</version>
		</dependency>	
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<!--删除无用的jar包文件  -->
		<!--<dependency>
			<groupId>com.nci.core.prd</groupId>
			<artifactId>Prd-Import-Service-Stub</artifactId>
			<version>2.0.1.0</version>
		</dependency>-->	
		<!--核包冲突的文件  -->
		<!-- <dependency>
			<groupId>com.nci.core.uw</groupId>
			<artifactId>UW-Import-Service-Stub</artifactId>
			<version>0.0.4</version>
		</dependency>-->	
		<dependency>
			<groupId>com.nci.tunan.prd</groupId>
			<artifactId>prd-impl</artifactId>
			<version>521.0.1</version>
		</dependency>
		<dependency>
			<groupId>ecss.easyclient</groupId>
			<artifactId>ecss-easyclient</artifactId>
			<version>0.8.8</version>
		</dependency>
		<dependency>
			<groupId>com.nci.tunan.cap</groupId>
			<artifactId>cap-interface</artifactId>
			<version>521.0.1</version>
		</dependency>
	</dependencies>
</project>
